#ifndef SOFTWARE_SRC_APP_SLAVEOPER_H_
#define SOFTWARE_SRC_APP_SLAVEOPER_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "common.h"
#include "battery.h"

BOOLEAN SmartLi_IsPowerDown_Slave(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattIn);
void MultBattMixPowerOnAndOffVolt_Slave(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo, FLOAT *pfPowerDownVol, FLOAT *pfPowerOffVol);
void BMSChgButBDUDischg_Slave(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo);




void CtrlChargeRotate_Slave(T_RotateInfoStruct *ptRotate);
void DealSlaveRotateTimeout_Slave(T_BattDealInfoStruct *ptBattDeal);
void DealChargeRotate_Slave(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo);
void SmartLi_CalcDischargeOutputVolt_Slave(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo);
void SmartLi_CalcSysMaxChargeCurr_Slave(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo);
void startDischg_Slave(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo);
void SetChgPara_Slave(T_BattDealInfoStruct *ptBattDeal, FLOAT fChargeFullVol);
void GetPowerdownVolt_Slave(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);


BOOLEAN SlaveOperUnitest(BOOLEAN bFlag);
#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // SOFTWARE_SRC_APP_SAMPLE_H_;
