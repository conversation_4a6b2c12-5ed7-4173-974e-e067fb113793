#ifndef SEARCH_HISDATA_H_
#define SEARCH_HISDATA_H_

#include "hisdata.h"
#include "common.h"

#define TIME_VALID 0               // 时间有效
#define TIME_INVALID 1             // 时间无效
#define FIND_TIME_PRECISE 0        // 查找到具体时间
#define FIND_TIME_FUZZY 1          // 查找到时间范围
#define FIND_TIME_INVALID 2        // 查找到时间无效
#define USE_HIGH_BOUNDARY 1        // 无法完全匹配时间时用上边界
#define USE_LOW_BOUNDARY 0         // 无法完全匹配时间时用下边界

typedef UINT32 numTime; // 将时间中的 "年月日" 或 "时分秒" 表示成一个数值

typedef enum
{
    TIME_LESS_THAN_TARGET = 0, // 待比较时间 < 目标时间
    TIME_EQUAL_TARGET,         // 待比较时间 = 目标时间
    TIME_GREATER_THAN_TARGET,  // 待比较时间 > 目标时间
} TimeCompareResult;

void ExtractTimeSlot(T_TimeStruct *ptStartTime, T_TimeStruct *ptEndTime, BYTE *pBuff);
TimeCompareResult CompareTime(T_TimeStruct tTime, T_TimeStruct tTargetTime);
WORD GetHisSaveNum(BYTE ucHisRecordType, T_TimeStruct *ptStartTime, T_TimeStruct *ptEndTime);
BOOLEAN IfTimeAllZero(T_TimeStruct tStartTime, T_TimeStruct tEndTime);
void TmTime2TimeStruct(T_TimeStruct *ptBlockTime, struct tm tTime);
BYTE FindTimeInBlock(T_TimeStruct *atBlock, BYTE ucBlockNum, T_TimeStruct tInputTime, BYTE *IndexZone, WORD *pwIndex);
void ExtractTimeStructFromBuff(T_TimeStruct *ptBlockTime, BYTE *pBuff);

#endif
