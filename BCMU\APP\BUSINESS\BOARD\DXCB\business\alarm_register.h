#ifndef ALARM_REGISTER_H_  
#define ALARM_REGISTER_H_  
#ifdef __cplusplus
extern "C" {
#endif
#include "app_config.h"

#define SWITCH_DISSCONN_STATUS    1
#define SWITCH_CONN_STATUS        0

#define TEMP_MIN    -30.0
#define TEMP_MAX    120.0

#define MAX_CHANL_NUM  16


void register_dxcb_alarm();
char dxcb_in_overheat_low_status_judge(int alm_id);
char conden_press_sensor_fault_judge(int alm_id);
char inhale_temp_sensor_fault_judge(int alm_id);
char eva_press_low_judge(int alm_id);
char eva_press_sensor_fault_judge(int alm_id);
void register_dxcb_alarm();
char judge_vol_switch_alm(int alm_id);
char judge_fc_error_alm(int alm_id);
char conden_press_high_alm_judge(int alm_id);
char vfd_curr_high_alm_judge(int alm_id);
char exhaust_temp_sensor_fault_judge(int alm_id);
char exhaust_overheat_low_alm_judge(int alm_id);
char exhaust_temp_high_alm_judge(int alm_id);
char freq_high_temp_judge(int alm_id);

#ifdef __cplusplus
}  
#endif

#endif