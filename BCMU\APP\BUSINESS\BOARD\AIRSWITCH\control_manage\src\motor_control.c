﻿#include "motor_control.h"
#include <rtthread.h>
#include <rtdevice.h>
#include <drv_motor_ctrl.h>
#include "data_type.h"
#include "pin_define.h"

/**
 * 相关控制位置说明：
 * A：合闸到位位置
   B：自由位置
   C：合闸状态信号为低电平；分闸状态时信号为高电平
   D：未授权位置
*/

Static rt_device_t s_dev_motor = RT_NULL;
Static motor_state_e s_motor_state = MOTOR_STOPPED;
Static motor_para_t s_para = {
    .speed = 100,
    .act_interval = 1,
    .timeout_count = 200,
    .wait = 100,
    .unauthorize_act_time = 200,
};

Static char is_leaved_position(rt_base_t pin, int time_out);
Static char is_moved_to_position(rt_base_t pin, int time_out);
Static char is_moved_or_leaved_position(rt_base_t pin, int time_out, char is_to_pos);
Static char is_pos_detected(rt_base_t pin);
Static char motor_ctrl(int speed, motor_direction_e direction);

// Static char motor_stop(void);
Static char rotate_reverse(void);
Static char rotate_foward(void);
Static char motor_brake(void);

Static char motor_D_to_B(void);
Static char motor_B_to_D(void);
Static char motor_A_to_B(void);
Static char motor_B_to_A(void);
Static char opened_state_B_to_D(void);

char init_motor_control(void)
{
    s_dev_motor = rt_device_find("MOTOR_CTRL");
    if (s_dev_motor == RT_NULL)
    {
        return FAILURE;
    }

    return SUCCESSFUL;
}

Static char motor_ctrl(int speed, motor_direction_e direction)
{
    RETURN_VAL_IF_FAIL(s_dev_motor != NULL, FAILURE);
    struct motor_config config = {speed, direction, PWM_FREQUENCY};
    rt_device_control(s_dev_motor, RT_NULL, &config);
    return SUCCESSFUL;
}

Static char rotate_foward(void)
{
    char ret = motor_ctrl(s_para.speed, MOVING_FORWARD);
    s_motor_state = MOTOR_RUNNING;
    return ret;
}

Static char rotate_reverse(void)
{
    char ret = motor_ctrl(s_para.speed, MOVING_REVERSE);
    s_motor_state = MOTOR_RUNNING;
    return ret;
}

Static char motor_brake(void)
{
    char ret = motor_ctrl(s_para.speed, MOVING_BRAKE);
    s_motor_state = MOTOR_RUNNING;
    return ret;
}

/*Static char motor_stop(void)
{
    char ret = motor_ctrl(0, MOVING_FORWARD);
    s_motor_state = MOTOR_STOPPED;
    return ret;
}*/

Static char is_pos_detected(rt_base_t pin)
{
    int state = 0;
    state = rt_pin_read(pin);

    if (state == PIN_LOW)
    {
        return TRUE;
    }
    return FALSE;
}

/**
 * @brief 判断是否在指定时间内移动到或离开指定位置
 * @param[in] pin 检测端口
 * @param[in] time_out 超时时间
 * @param[in] is_to_pos 是否向位置移动（TRUE:向位置移动, FALSE:离开位置）
*/
Static char is_moved_or_leaved_position(rt_base_t pin, int time_out, char is_to_pos)
{
    char pos_detected = FALSE;
    int i = 0;

    for (i = 0; i <= time_out; i++)
    {
        pos_detected = is_pos_detected(pin);
        if (pos_detected == is_to_pos)
        {
            motor_brake();
            return TRUE;
        }
        rt_thread_mdelay(s_para.act_interval);
    }
    motor_brake();

    return FALSE;
}

Static char is_moved_to_position(rt_base_t pin, int time_out)
{
    return is_moved_or_leaved_position(pin, time_out, TRUE);
}

Static char is_leaved_position(rt_base_t pin, int time_out)
{
    return is_moved_or_leaved_position(pin, time_out, FALSE);
}

/**
 * @brief 自由状态转到B位置
*/
char motor_act_power_on(void)
{
    char is_at_B = 0;
    char pos_B_detected = is_pos_detected(PIN_MOTOR_VSB);
    char pos_C_detected = is_pos_detected(PIN_MOTOR_VSC);

    // 已在B点，B点低电平
    RETURN_VAL_IF_FAIL(pos_B_detected == FALSE, SUCCESSFUL);

    // C点低电平/合闸状态
    if (pos_C_detected == TRUE)
    {
        is_at_B = motor_D_to_B();
        if (is_at_B == FAILURE)
        {
            rt_thread_mdelay(100); 
            motor_A_to_B();
        }
        return SUCCESSFUL;
    }
    else
    {
        opened_state_B_to_D();
        return SUCCESSFUL;
    }

    return FAILURE;
}

/**
 * @brief  初始化时为分闸状态则先到D：B->D
*/
Static char opened_state_B_to_D(void)
{
    char is_at_B = 0;
    
    s_para.speed = MOTOR_NORMAL_SPEED;
    s_para.timeout_count = MOTOR_DETECT_TIMEOUT;
    rotate_foward();
    is_at_B = is_moved_to_position(PIN_MOTOR_VSB, s_para.timeout_count);
    if (is_at_B == FALSE)
    {
        rt_thread_mdelay(100);
        motor_D_to_B();
    }
    
    return SUCCESSFUL;
}

/**
 * @brief 执行合闸动作
*/
char motor_act_close(void)
{
    char is_at_B = 0;
    char pos_C_detected = is_pos_detected(PIN_MOTOR_VSC);

    // 已在合闸到位C位置
    if (pos_C_detected == TRUE)
    {
        return SUCCESSFUL;
    }

    // B->A 执行合闸
    motor_B_to_A();
    rt_thread_mdelay(100);

    // A->B 合闸后回B点
    is_at_B = motor_A_to_B();
    RETURN_VAL_IF_FAIL(is_at_B == FAILURE, SUCCESSFUL); // B点电平为低

    return FAILURE;
}

/**
 * @brief  合闸动作：B->A
*/
Static char motor_B_to_A(void)
{
    char is_at_A = 0;

    s_para.speed = MOTOR_NORMAL_SPEED;
    s_para.timeout_count = MOTOR_DETECT_TIMEOUT;
    rotate_reverse();
    is_at_A = is_moved_to_position(PIN_MOTOR_VSA, s_para.timeout_count);
    RETURN_VAL_IF_FAIL(is_at_A == FALSE, SUCCESSFUL);
    
    return FAILURE;
}

/**
 * @brief  初始化为合闸状态下D->B没找到B时，反向找B、合闸后回B点：A->B
*/
Static char motor_A_to_B(void)
{
    char is_at_B = 0;
    s_para.speed = MOTOR_NORMAL_SPEED;
    s_para.timeout_count = MOTOR_DETECT_TIMEOUT;
    rotate_foward();
    is_at_B = is_moved_to_position(PIN_MOTOR_VSB, s_para.timeout_count);
    RETURN_VAL_IF_FAIL(is_at_B == FALSE, SUCCESSFUL);

    return FAILURE;
}

/**
 * @brief 执行分闸动作
*/
char motor_act_open(void)
{
    char is_at_B = 0;
    char pos_C_detected = is_pos_detected(PIN_MOTOR_VSC);

    // 已处于分闸位置
    RETURN_VAL_IF_FAIL(pos_C_detected == TRUE, SUCCESSFUL);

    // B->D 执行分闸
    motor_B_to_D();
    rt_thread_mdelay(100);

    // D->B 分闸后回B点
    is_at_B = motor_D_to_B();
    RETURN_VAL_IF_FAIL(is_at_B == FAILURE, SUCCESSFUL);

    return FAILURE;
}

/**
 * @brief  下发未授权指令、以及分闸动作：B->D
*/
Static char motor_B_to_D(void)
{
    char is_at_C = 0;

    s_para.speed = MOTOR_NORMAL_SPEED;
    s_para.timeout_count = MOTOR_DETECT_TIMEOUT;
    rotate_foward();
    rt_thread_mdelay(200); //解决分闸状态下，执行未授权电机不动作
    is_at_C = is_leaved_position(PIN_MOTOR_VSC, s_para.timeout_count);
    RETURN_VAL_IF_FAIL(is_at_C == FALSE, SUCCESSFUL);

    return FAILURE;
}

/**
 * @brief  分闸后恢复动作、下发授权指令、或者初始化时合闸状态不在B：D->B
*/
Static char motor_D_to_B(void)
{
    char is_at_B = 0;
    s_para.speed = MOTOR_NORMAL_SPEED;
    s_para.timeout_count = MOTOR_DETECT_TIMEOUT;
    rotate_reverse();
    is_at_B = is_moved_to_position(PIN_MOTOR_VSB, s_para.timeout_count);
    RETURN_VAL_IF_FAIL(is_at_B == FALSE, SUCCESSFUL);

    return FAILURE;
}

/**
 * @brief 未授权执行分闸
 * @note  外部判断是否已授权
*/
char unauthorize_act_open(void)
{  
    return motor_B_to_D();
}

/**
 * @brief  授权执行复位到B位置
 * @note  外部判断从未授权切换到授权状态时调用执行
*/
char authorize_act_reset(void)
{
    char is_at_B;
    char pos_B_detected = is_pos_detected(PIN_MOTOR_VSB);
    char pos_A_detected = is_pos_detected(PIN_MOTOR_VSA);

    // 已在B位置（正常情况，未授权应在D）
    RETURN_VAL_IF_FAIL(pos_B_detected == FALSE, SUCCESSFUL);

    // 检测到A，说明B微动开关有问题，导致转动到最右侧检测到A，阻止动作保护电机
    RETURN_VAL_IF_FAIL(pos_A_detected == FALSE, FAILURE);

    is_at_B = motor_D_to_B();
    RETURN_VAL_IF_FAIL(is_at_B == FAILURE, SUCCESSFUL);

    return FAILURE;
}
