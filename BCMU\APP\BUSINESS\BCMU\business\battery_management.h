/*******************************************************************************
  * @file        battery_management.h
  * @version     v1.0.0
  * @copyright   COPYRIGHT &copy; 2023 CSG
  * <AUTHOR>
  * @date        2023-4-27
  * @brief
  * @attention
  * Modification History
  * DATE         DESCRIPTION
  * ------------------------
  * - 2023-4-27  dongmengling Created
*******************************************************************************/


#ifndef BATTERY_MANAGEMENT_H_  
#define BATTERY_MANAGEMENT_H_  
#ifdef __cplusplus
extern "C" {
#endif

#include "battery.h"

#define CHARGE_TIME_FULL_MIN     600
#define CURR_MIN_DET             2       ///检查误差 2A
#define AFFECT_COEF              (0.98f)
#define SOC_CAL_DISCHG_CURR      2       //SOC校正放电电流2A
#define CAP_CHANGE_SAVE_COEF     (0.02f)       //存储eefrom容量变化百分比

typedef struct {
    float batt_vol;
    unsigned char  soc;
}SOC_cal_info_attr_t;


// 定义电池管理状态枚举类型
typedef enum {
    RUNNING,                             ///< 正常运行
    DISCHARGING,                         ///< 放电状态，输出断路器已经闭合
    CHARGING,                            ///< 充电状态，输出断路器已经闭合
    STANDBY_BREAKER_CLOSED,              ///< 待机状态，输出断路器已经闭合
    STANDBY_BREAKER_CAN_NOT_CLOSE,       ///< 待机状态，输出断路器无法闭合
    STANDBY_BREAKER_CAN_MANUAL_CLOSE,    ///< 待机状态，输出断路器处于可手动闭合
    SHUTDOWN                             ///< 关机状态
} bcmu_battery_status_t;


void* init_batt_manage(void * param);
void batt_manage_main(void *param);
void cell_ocv_unitest(void);
void save_batt_info(unsigned char mod_no);
batt_result_t* get_batt_result_info(unsigned char mod_no);

int get_batt_charge_minute(unsigned char cluster_index) ;

#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // BATTERY_MANAGEMENT_H_  ;