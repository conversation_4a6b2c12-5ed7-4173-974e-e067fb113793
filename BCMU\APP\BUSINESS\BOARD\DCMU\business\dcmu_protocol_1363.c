/*
 * @file    : dcmu_protocol_modbus.c
 * @brief   : DCMU南向modbus协议实现
 * @details :
 * <AUTHOR> penglei
 * @Date    : 2024-07-18
 * @LastEditTime: 2024-07-18
 * @version : V0.0.1
 */

#include <string.h>
#include <board.h>
#include "sps.h"
#include "sample.h"
#include "dev_dcmu.h"
#include "para_id_in.h"
#include "para_manage.h"
#include "protocol_layer.h"
#include "realdata_id_in.h"
#include "system_manage.h"
#include "alarm_manage.h"
#include "realdata_save.h"
#include "pdt_version.h"
#include "log_mgr_api.h"
#include "io_control.h"
#include "alarm_register.h"
#include "alarm_mgr_api.h"
#include "utils_time.h"
#include "his_record.h"
#include "const_define_in.h"
#include "dcmu_protocol_1363.h"
#include "gui_data_interface.h"
#include "utils_data_transmission.h"
#include "utils_rtthread_security_func.h"
#include "dev_dcmu.h"

static dcmu_para_value  para_value={0};
Static unsigned char s_bufflen = 0;
Static unsigned short s_start_index = 0;
Static unsigned char s_command_types = 0x00;
Static unsigned char s_command_group = 0x00;

Static char dcmu_checkMenuKey(unsigned char* buff,unsigned char len);
Static int dcmu_pack_para_comm(void* dev_inst, void *cmd_buf);
Static int dcmu_pack_env_para_comm(void* dev_inst, void *cmd_buf);
Static int dcmu_parse_para_comm(void* dev_inst, void *cmd_buf);
Static int dcmu_pack_time(void* dev_inst, void* cmd_buff);
Static int dcmu_parse_time(void* dev_inst, void* cmd_buff);
Static int dcmu_parse_remote_control(void* dev_inst, void *cmd_buf);
Static int dcmu_pack_dc_alm_data(void* dev_inst, void* cmd_buff);
Static int dcmu_pack_env_alm_data(void* dev_inst, void* cmd_buff);
Static int dcmu_pack_analog(void* dev_inst, void* cmd_buff);
Static int dcmu_pack_sw_status(void* dev_inst, void* cmd_buff);
Static int dcmu_pack_his_alarm(void* dev_inst, void* cmd_buff);
Static int dcmu_parse_his_alarm(void* dev_inst, void* cmd_buff);
Static int His_Alm_To_Buff(unsigned short index, cmd_buf_t *cmd_buf);
Static int dcmu_pack_env_analog(void* dev_inst, void* cmd_buff);
Static int dcmu_pack_factory_info_custom(void* dev_inst, void* cmd_buff);
Static int dcmu_pack_boot_ver(void* dev_inst, void* cmd_buff);
Static int oper_after_save_custormpara(signed char opr_rst, unsigned char cmd_type);

// 辅助函数声明
Static unsigned char handle_alarm_status(int alm_id, unsigned char value_if_exist, unsigned char value_if_not_exist);
Static unsigned char judge_voltage_alm_status(int alm_id_low, int alm_id_high, int alm_id_too_low, int alm_id_too_high ,char sig_id);
Static unsigned char judge_alarm_status(int alm_id, char sig_id);
Static unsigned char judge_battery_temperature_sensor_alarm_status(char sig_id);
Static unsigned char judge_env_temperature_sensor_alarm_status(int alm_code_low, int alm_code_high, int alm_code_invalid);
Static signed char manual_control_out_relay(unsigned char relay_index, unsigned char control_status);


static s1363_cmd_head_t cmd_req[] RAM_SECTION = 
{
    {VER_22 , CID1_DC , CMD_GET_INRELAY_INFO},                  //0
    {VER_22 , CID1_DC , CMD_SET_INRELAY_INFO},                  //1
    {VER_22 , CID1_DC , CMD_REMOTE_CONTROL},                    //2
    {VER_22 , CID1_DC , CMD_GET_SYSPARA},                       //3
    {VER_22 , CID1_DC , CMD_SET_SYSPARA},                       //4
    {VER_22 , CID1_DC , CMD_GET_ALMRELAY},                      //5
    {VER_22 , CID1_DC , CMD_SET_ALMRELAY},                      //6
    {VER_22 , CID1_DC , CMD_GET_ALMLEVEL},                      //7
    {VER_22 , CID1_DC , CMD_SET_ALMLEVEL},                      //8
    {VER_22 , CID1_DC , CMD_GET_ALARM},                         //9
    {VER_22 , CID1_DC , CMD_GET_CUSTOMPARA},                    //10
    {VER_22 , CID1_DC , CMD_SET_CUSTOMPARA},                    //11
    {VER_22 , CID1_1363_ENV , CMD_GET_ALARM},                   //12
    {VER_22 , CID1_DC , CMD_GET_TIME},                          //13
    {VER_22 , CID1_DC , CMD_SET_TIME},                          //14
    {VER_22 , CID1_DC , CMD_GET_ANALOG},                        //15
    {VER_22 , CID1_DC , CMD_GET_SW_STATUS},                     //16
    {VER_22 , CID1_DC , CMD_GET_HIS_ALARM},                     //17
    {VER_22 , CID1_1363_ENV , CMD_GET_ANALOG},                  //18
    {VER_22 , CID1_DC , CMD_GET_FACTORY_INFO_CUSTOM},           //19
    {VER_22 , CID1_1363_ENV , CMD_GET_SYSPARA},                 //20
    {VER_22 , CID1_1363_ENV , CMD_SET_SYSPARA},                 //21
    {VER_22 , CID1_1363_ENV , CMD_GET_ALMLEVEL},                //22
    {VER_22 , CID1_1363_ENV , CMD_SET_ALMLEVEL},                //23
    {VER_22 , CID1_1363_ENV , CMD_GET_ALMRELAY},                //24
    {VER_22 , CID1_1363_ENV , CMD_SET_ALMRELAY},                //25
    {VER_22 , CID1_DC , CMD_GET_BOOT_VERSION},                  //26
};

static s1363_cmd_head_t cmd_ack[] RAM_SECTION = 
{
    {VER_22 , CID1_DC , CMD_GET_INRELAY_INFO},                  //0
    {VER_22 , CID1_DC , CMD_SET_INRELAY_INFO},                  //1
    {VER_22 , CID1_DC , CMD_REMOTE_CONTROL},                    //2
    {VER_22 , CID1_DC , CMD_GET_SYSPARA},                       //3
    {VER_22 , CID1_DC , CMD_SET_SYSPARA},                       //4
    {VER_22 , CID1_DC , CMD_GET_ALMRELAY},                      //5
    {VER_22 , CID1_DC , CMD_SET_ALMRELAY},                      //6
    {VER_22 , CID1_DC , CMD_GET_ALMLEVEL},                      //7
    {VER_22 , CID1_DC , CMD_SET_ALMLEVEL},                      //8
    {VER_22 , CID1_DC , CMD_GET_ALARM},                         //9
    {VER_22 , CID1_DC , CMD_GET_CUSTOMPARA},                    //10
    {VER_22 , CID1_DC , CMD_SET_CUSTOMPARA},                    //11
    {VER_22 , CID1_1363_ENV , CMD_GET_ALARM},                   //12
    {VER_22 , CID1_DC , CMD_GET_TIME},                          //13
    {VER_22 , CID1_DC , CMD_SET_TIME},                          //14
    {VER_22 , CID1_DC , CMD_GET_ANALOG},                        //15
    {VER_22 , CID1_DC , CMD_GET_SW_STATUS},                     //16
    {VER_22 , CID1_DC , CMD_GET_HIS_ALARM},                     //17
    {VER_22 , CID1_1363_ENV , CMD_GET_ANALOG},                  //18
    {VER_22 , CID1_DC , CMD_GET_FACTORY_INFO_CUSTOM},           //19
    {VER_22 , CID1_1363_ENV , CMD_GET_SYSPARA},                 //20
    {VER_22 , CID1_1363_ENV , CMD_SET_SYSPARA},                 //21
    {VER_22 , CID1_1363_ENV , CMD_GET_ALMLEVEL},                //22
    {VER_22 , CID1_1363_ENV , CMD_SET_ALMLEVEL},                //23
    {VER_22 , CID1_1363_ENV , CMD_GET_ALMRELAY},                //24
    {VER_22 , CID1_1363_ENV , CMD_SET_ALMRELAY},                //25
    {VER_22 , CID1_DC , CMD_GET_BOOT_VERSION},                  //26
};

static dcmu_para_info s_inrelay_info[] = {
    {0x80, DCMU_PARA_ID_INRELAYTTL_OFFSET, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x81, DCMU_PARA_ID_INRELAYNAME_OFFSET, type_string, LEN_RELAYNAME,DOP_0},
    {0x82, DCMU_PARA_ID_INRELAYTTL_OFFSET+1, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x83, DCMU_PARA_ID_INRELAYNAME_OFFSET+1, type_string, LEN_RELAYNAME,DOP_0},
    {0x84, DCMU_PARA_ID_INRELAYTTL_OFFSET+2,type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x85, DCMU_PARA_ID_INRELAYNAME_OFFSET+2, type_string, LEN_RELAYNAME,DOP_0},
    {0x86, DCMU_PARA_ID_INRELAYTTL_OFFSET+3, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x87, DCMU_PARA_ID_INRELAYNAME_OFFSET+3, type_string, LEN_RELAYNAME,DOP_0},
};

static dcmu_para_info s_syspara_info[] = {
    {0x80, DCMU_PARA_ID_DC_VOLTAGE_HIGH_THRESHOLD_OFFSET, type_short, ARRAY_SIZE_1, DOP_1},
    {0x81, DCMU_PARA_ID_DC_VOLTAGE_LOW_THRESHOLD_OFFSET, type_short, ARRAY_SIZE_1, DOP_1},
    {0xA0, DCMU_PARA_ID_BATTERY_VOLTAGE_LOW_THRESHOLD_OFFSET, type_short, ARRAY_SIZE_1, DOP_1},
    {0xA1, DCMU_PARA_ID_BATTERY_VOLTAGE_TOO_LOW_THRESHOLD_OFFSET, type_short, ARRAY_SIZE_1, DOP_1},
    {0xA2, DCMU_PARA_ID_BATTERY_CURRENT_THRESHOLD_OFFSET, type_unsigned_char, ARRAY_SIZE_1, DOP_2},
    {0xA3, DCMU_PARA_ID_BATTERY_TEMPERATURE_HIGH_THRESHOLD_OFFSET, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0xA4, DCMU_PARA_ID_BATTERY_TEMPERATURE_LOW_THRESHOLD_OFFSET, type_char, ARRAY_SIZE_1, DOP_0},
    {0xA5, DCMU_PARA_ID_BATTERY_LOOP_BROKEN_THRESHOLD_OFFSET, type_short, ARRAY_SIZE_1, DOP_1},
    {0xA6, DCMU_PARA_ID_BATTERY_DISCHARGE_THRESHOLD_OFFSET, type_short, ARRAY_SIZE_1, DOP_1},
    {0xA7, DCMU_PARA_ID_BATTERY_SHUNT_OFFSET, type_short, ARRAY_SIZE_1, DOP_0},
    {0xA8, DCMU_PARA_ID_BATTERY_SHUNT_OFFSET + 1, type_short, ARRAY_SIZE_1, DOP_0},
    {0xA9, DCMU_PARA_ID_BATTERY_SHUNT_OFFSET + 2, type_short, ARRAY_SIZE_1, DOP_0},
    {0xAA, DCMU_PARA_ID_BATTERY_SHUNT_OFFSET + 3, type_short, ARRAY_SIZE_1, DOP_0},
    {0xAB, DCMU_PARA_ID_LOAD_SHUNT_OFFSET, type_short, ARRAY_SIZE_1, DOP_0},
    {0xAC, DCMU_PARA_ID_LOAD_SHUNT_OFFSET + 1, type_short, ARRAY_SIZE_1, DOP_0},
    {0xAD, DCMU_PARA_ID_LOAD_SHUNT_OFFSET + 2, type_short, ARRAY_SIZE_1, DOP_0},
    {0xAE, DCMU_PARA_ID_LOAD_SHUNT_OFFSET + 3, type_short, ARRAY_SIZE_1, DOP_0},
    {0xAF, DCMU_PARA_ID_LOAD_SHUNT_OFFSET + 4, type_short, ARRAY_SIZE_1, DOP_0},
    {0xB0, DCMU_PARA_ID_LOAD_SHUNT_OFFSET + 5, type_short, ARRAY_SIZE_1, DOP_0},
    {0xB1, DCMU_PARA_ID_LOAD_SHUNT_OFFSET + 6, type_short, ARRAY_SIZE_1, DOP_0},
    {0xB2, DCMU_PARA_ID_LOAD_SHUNT_OFFSET + 7, type_short, ARRAY_SIZE_1, DOP_0},
    {0xB3, DCMU_PARA_ID_LOAD_SHUNT_OFFSET + 8, type_short, ARRAY_SIZE_1, DOP_0},
    {0xB4, DCMU_PARA_ID_LOAD_SHUNT_OFFSET + 9, type_short, ARRAY_SIZE_1, DOP_0},
    {0xB5, DCMU_PARA_ID_LOAD_SHUNT_OFFSET + 10, type_short, ARRAY_SIZE_1, DOP_0},
    {0xB6, DCMU_PARA_ID_LOAD_SHUNT_OFFSET + 11, type_short, ARRAY_SIZE_1, DOP_0},
    {0xB7, DCMU_PARA_ID_LOAD_SHUNT_OFFSET + 12, type_short, ARRAY_SIZE_1, DOP_0},
    {0xB8, DCMU_PARA_ID_LOAD_SHUNT_OFFSET + 13, type_short, ARRAY_SIZE_1, DOP_0},
    {0xB9, DCMU_PARA_ID_LOAD_SHUNT_OFFSET + 14, type_short, ARRAY_SIZE_1, DOP_0},
    {0xBA, DCMU_PARA_ID_LOAD_SHUNT_OFFSET + 15, type_short, ARRAY_SIZE_1, DOP_0},
    {0xBB, DCMU_PARA_ID_LOAD_SHUNT_OFFSET + 16, type_short, ARRAY_SIZE_1, DOP_0},
    {0xBC, DCMU_PARA_ID_LOAD_SHUNT_OFFSET + 17, type_short, ARRAY_SIZE_1, DOP_0},
    {0xBD, DCMU_PARA_ID_LOAD_SHUNT_OFFSET + 18, type_short, ARRAY_SIZE_1, DOP_0},
    {0xBE, DCMU_PARA_ID_LOAD_SHUNT_OFFSET + 19, type_short, ARRAY_SIZE_1, DOP_0},
    {0xBF, DCMU_PARA_ID_LOAD_SHUNT_OFFSET + 20, type_short, ARRAY_SIZE_1, DOP_0},
    {0xC0, DCMU_PARA_ID_LOAD_SHUNT_OFFSET + 21, type_short, ARRAY_SIZE_1, DOP_0},
    {0xC1, DCMU_PARA_ID_LOAD_SHUNT_OFFSET + 22, type_short, ARRAY_SIZE_1, DOP_0},
    {0xC2, DCMU_PARA_ID_LOAD_SHUNT_OFFSET + 23, type_short, ARRAY_SIZE_1, DOP_0},
    {0xC3, DCMU_PARA_ID_BATTERY_TEMPERATURE_TOO_HIGH_THRESHOLD_OFFSET, type_short, ARRAY_SIZE_1, DOP_0},
    {0xC4, DCMU_PARA_ID_DC_VOLTAGE_TOO_LOW_THRESHOLD_OFFSET, type_short, ARRAY_SIZE_1, DOP_1},
    {0xC5, DCMU_PARA_ID_DC_VOLTAGE_TOO_HIGH_THRESHOLD_OFFSET, type_short, ARRAY_SIZE_1, DOP_1}
};

static dcmu_para_info s_env_para_info[] = {
    {0x80, DCMU_PARA_ID_ENV_TEMPERATURE_SENSOR_UPPER_LIMIT_OFFSET, type_short,ARRAY_SIZE_1, DOP_0},
    {0x81, DCMU_PARA_ID_ENV_TEMPERATURE_SENSOR_LOWER_LIMIT_OFFSET, type_short,ARRAY_SIZE_1, DOP_0},
    {0x82, DCMU_PARA_ID_ENV_HUMIDITY_SENSOR_UPPER_LIMIT_OFFSET, type_short,ARRAY_SIZE_1, DOP_0},
    {0x83, DCMU_PARA_ID_ENV_HUMIDITY_SENSOR_LOWER_LIMIT_OFFSET, type_short,ARRAY_SIZE_1, DOP_0},
    {0x84, DCMU_PARA_ID_ENV_TEMP_ZERO_POINT_OFFSET, type_short,ARRAY_SIZE_1, DOP_0},
    {0x85, DCMU_PARA_ID_ENV_HUMIDITY_ZERO_POINT_OFFSET, type_short,ARRAY_SIZE_1, DOP_0},
};

static dcmu_para_info s_env_almlevel_info[] = {
    {0x80, DCMU_ALM_ID_ENV_TEMP_HIGH_ALARM_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x81, DCMU_ALM_ID_ENV_TEMP_LOW_ALARM_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x82, DCMU_ALM_ID_ENV_TEMP_SENSOR_INVALID_ALARM_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x83, DCMU_ALM_ID_ENV_HUMIDITY_HIGH_ALARM_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x84, DCMU_ALM_ID_ENV_HUMIDITY_LOW_ALARM_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x85, DCMU_ALM_ID_ENV_HUMIDITY_SENSOR_INVALID_ALARM_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x86, DCMU_ALM_ID_FUMES_SENSOR_ALARM_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x87, DCMU_ALM_ID_FLOOD_SENSOR_ALARM_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x88, DCMU_ALM_ID_DOORMAT_SENSOR_ALARM_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
};

static dcmu_para_info s_env_almrelay_info[] = {
    {0x80, DCMU_ALM_ID_ENV_TEMP_HIGH_ALARM_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x81, DCMU_ALM_ID_ENV_TEMP_LOW_ALARM_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x82, DCMU_ALM_ID_ENV_TEMP_SENSOR_INVALID_ALARM_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x83, DCMU_ALM_ID_ENV_HUMIDITY_HIGH_ALARM_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x84, DCMU_ALM_ID_ENV_HUMIDITY_LOW_ALARM_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x85, DCMU_ALM_ID_ENV_HUMIDITY_SENSOR_INVALID_ALARM_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x86, DCMU_ALM_ID_FUMES_SENSOR_ALARM_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x87, DCMU_ALM_ID_FLOOD_SENSOR_ALARM_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x88, DCMU_ALM_ID_DOORMAT_SENSOR_ALARM_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
};

static dcmu_para_info s_almrelay_info[] = {
    {0x80, DCMU_ALM_ID_DC_VOLT_HIGH_RELAY, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x81, DCMU_ALM_ID_DC_VOLT_LOW_RELAY, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x82, DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN_RELAY, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x83, DCMU_ALM_ID_TOTAL_ALARM_RELAY, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x84, DCMU_ALM_ID_DC_LIGHTNING_PROTECTOR_DAMAGED_RELAY, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x85, DCMU_ALM_ID_BATTERY_VOLTAGE_LOW_RELAY, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x86, DCMU_ALM_ID_BATTERY_VOLTAGE_TOO_LOW_RELAY, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x87, DCMU_ALM_ID_ABNORMAL_BATTERY_CURRENT_RELAY, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x88, DCMU_ALM_ID_BATTERY_TEMPERATURE_HIGH_RELAY, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x89, DCMU_ALM_ID_BATTERY_TEMPERATURE_LOW_RELAY, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x8A, DCMU_ALM_ID_BATTERY_LOOP_BROKEN_RELAY, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x8B, DCMU_ALM_ID_BATTERY_DISCHARGE_RELAY, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x8C, DCMU_ALM_ID_INVALID_BATTERY_TEMPERATURE_RELAY, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x8D, DCMU_ALM_ID_BATTERY_TEMPERATURE_TOO_HIGH_RELAY, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x8E, DCMU_ALM_ID_DC_VOLTAGE_TOO_LOW_RELAY, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x8F, DCMU_ALM_ID_DC_VOLTAGE_TOO_HIGH_RELAY, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x90, DCMU_ALM_ID_INPUT_RELAY1_ALARM_RELAY, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x91, DCMU_ALM_ID_INPUT_RELAY2_ALARM_RELAY, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x92, DCMU_ALM_ID_INPUT_RELAY3_ALARM_RELAY, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x93, DCMU_ALM_ID_INPUT_RELAY4_ALARM_RELAY, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
};

static dcmu_para_info s_almlevel_info[] = {
    {0x80, DCMU_ALM_ID_DC_VOLT_HIGH_LEVEL, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x81, DCMU_ALM_ID_DC_VOLT_LOW_LEVEL, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x82, DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN_LEVEL, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x83, DCMU_ALM_ID_TOTAL_ALARM_LEVEL, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x84, DCMU_ALM_ID_DC_LIGHTNING_PROTECTOR_DAMAGED_LEVEL, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x85, DCMU_ALM_ID_BATTERY_VOLTAGE_LOW_LEVEL, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x86, DCMU_ALM_ID_BATTERY_VOLTAGE_TOO_LOW_LEVEL, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x87, DCMU_ALM_ID_ABNORMAL_BATTERY_CURRENT_LEVEL, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x88, DCMU_ALM_ID_BATTERY_TEMPERATURE_HIGH_LEVEL, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x89, DCMU_ALM_ID_BATTERY_TEMPERATURE_LOW_LEVEL, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x8A, DCMU_ALM_ID_BATTERY_LOOP_BROKEN_LEVEL, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x8B, DCMU_ALM_ID_BATTERY_DISCHARGE_LEVEL, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x8C, DCMU_ALM_ID_INVALID_BATTERY_TEMPERATURE_LEVEL, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x8D, DCMU_ALM_ID_BATTERY_TEMPERATURE_TOO_HIGH_LEVEL, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x8E, DCMU_ALM_ID_DC_VOLTAGE_TOO_LOW_LEVEL, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x8F, DCMU_ALM_ID_DC_VOLTAGE_TOO_HIGH_LEVEL, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x90, DCMU_ALM_ID_INPUT_RELAY1_ALARM_LEVEL, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x91, DCMU_ALM_ID_INPUT_RELAY2_ALARM_LEVEL, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x92, DCMU_ALM_ID_INPUT_RELAY3_ALARM_LEVEL, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x93, DCMU_ALM_ID_INPUT_RELAY4_ALARM_LEVEL, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
};

static dcmu_para_info s_custompara_info[] = {
    // 电池组配置
    {0x01, DCMU_PARA_ID_BATTERY_CONFIG_OFFSET + 0, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x02, DCMU_PARA_ID_BATTERY_CONFIG_OFFSET + 1, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x03, DCMU_PARA_ID_BATTERY_CONFIG_OFFSET + 2, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x04, DCMU_PARA_ID_BATTERY_CONFIG_OFFSET + 3, type_unsigned_char, ARRAY_SIZE_1, DOP_0},

    // 负载配置
    {0x05, DCMU_PARA_ID_LOAD_CONFIG_OFFSET + 0, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x06, DCMU_PARA_ID_LOAD_CONFIG_OFFSET + 1, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x07, DCMU_PARA_ID_LOAD_CONFIG_OFFSET + 2, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x08, DCMU_PARA_ID_LOAD_CONFIG_OFFSET + 3, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x09, DCMU_PARA_ID_LOAD_CONFIG_OFFSET + 4, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x0A, DCMU_PARA_ID_LOAD_CONFIG_OFFSET + 5, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x0B, DCMU_PARA_ID_LOAD_CONFIG_OFFSET + 6, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x0C, DCMU_PARA_ID_LOAD_CONFIG_OFFSET + 7, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x0D, DCMU_PARA_ID_LOAD_CONFIG_OFFSET + 8, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x0E, DCMU_PARA_ID_LOAD_CONFIG_OFFSET + 9, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x0F, DCMU_PARA_ID_LOAD_CONFIG_OFFSET + 10, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x10, DCMU_PARA_ID_LOAD_CONFIG_OFFSET + 11, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x11, DCMU_PARA_ID_LOAD_CONFIG_OFFSET + 12, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x12, DCMU_PARA_ID_LOAD_CONFIG_OFFSET + 13, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x13, DCMU_PARA_ID_LOAD_CONFIG_OFFSET + 14, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x14, DCMU_PARA_ID_LOAD_CONFIG_OFFSET + 15, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x15, DCMU_PARA_ID_LOAD_CONFIG_OFFSET + 16, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x16, DCMU_PARA_ID_LOAD_CONFIG_OFFSET + 17, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x17, DCMU_PARA_ID_LOAD_CONFIG_OFFSET + 18, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x18, DCMU_PARA_ID_LOAD_CONFIG_OFFSET + 19, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x19, DCMU_PARA_ID_LOAD_CONFIG_OFFSET + 20, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x1A, DCMU_PARA_ID_LOAD_CONFIG_OFFSET + 21, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x1B, DCMU_PARA_ID_LOAD_CONFIG_OFFSET + 22, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x1C, DCMU_PARA_ID_LOAD_CONFIG_OFFSET + 23, type_unsigned_char, ARRAY_SIZE_1, DOP_0},

    // 负载回路配置
    {0x1D, DCMU_PARA_ID_LOAD_LOOP_CONFIG_OFFSET, type_unsigned_char, ARRAY_SIZE_1, DOP_0},

    // 直流电压零点
    {0x1E, DCMU_PARA_ID_DC_VOLT_ZERO_OFFSET, type_short, ARRAY_SIZE_1, DOP_1},

    // 负载电流零点
    {0x1F, DCMU_PARA_ID_LOAD_CURRENT_ZERO_OFFSET + 0, type_short, ARRAY_SIZE_1, DOP_1},
    {0x20, DCMU_PARA_ID_LOAD_CURRENT_ZERO_OFFSET + 1, type_short, ARRAY_SIZE_1, DOP_1},
    {0x21, DCMU_PARA_ID_LOAD_CURRENT_ZERO_OFFSET + 2, type_short, ARRAY_SIZE_1, DOP_1},
    {0x22, DCMU_PARA_ID_LOAD_CURRENT_ZERO_OFFSET + 3, type_short, ARRAY_SIZE_1, DOP_1},
    {0x23, DCMU_PARA_ID_LOAD_CURRENT_ZERO_OFFSET + 4, type_short, ARRAY_SIZE_1, DOP_1},
    {0x24, DCMU_PARA_ID_LOAD_CURRENT_ZERO_OFFSET + 5, type_short, ARRAY_SIZE_1, DOP_1},
    {0x25, DCMU_PARA_ID_LOAD_CURRENT_ZERO_OFFSET + 6, type_short, ARRAY_SIZE_1, DOP_1},
    {0x26, DCMU_PARA_ID_LOAD_CURRENT_ZERO_OFFSET + 7, type_short, ARRAY_SIZE_1, DOP_1},
    {0x27, DCMU_PARA_ID_LOAD_CURRENT_ZERO_OFFSET + 8, type_short, ARRAY_SIZE_1, DOP_1},
    {0x28, DCMU_PARA_ID_LOAD_CURRENT_ZERO_OFFSET + 9, type_short, ARRAY_SIZE_1, DOP_1},
    {0x29, DCMU_PARA_ID_LOAD_CURRENT_ZERO_OFFSET + 10, type_short, ARRAY_SIZE_1, DOP_1},
    {0x2A, DCMU_PARA_ID_LOAD_CURRENT_ZERO_OFFSET + 11, type_short, ARRAY_SIZE_1, DOP_1},
    {0x2B, DCMU_PARA_ID_LOAD_CURRENT_ZERO_OFFSET + 12, type_short, ARRAY_SIZE_1, DOP_1},
    {0x2C, DCMU_PARA_ID_LOAD_CURRENT_ZERO_OFFSET + 13, type_short, ARRAY_SIZE_1, DOP_1},
    {0x2D, DCMU_PARA_ID_LOAD_CURRENT_ZERO_OFFSET + 14, type_short, ARRAY_SIZE_1, DOP_1},
    {0x2E, DCMU_PARA_ID_LOAD_CURRENT_ZERO_OFFSET + 15, type_short, ARRAY_SIZE_1, DOP_1},
    {0x2F, DCMU_PARA_ID_LOAD_CURRENT_ZERO_OFFSET + 16, type_short, ARRAY_SIZE_1, DOP_1},
    {0x30, DCMU_PARA_ID_LOAD_CURRENT_ZERO_OFFSET + 17, type_short, ARRAY_SIZE_1, DOP_1},
    {0x31, DCMU_PARA_ID_LOAD_CURRENT_ZERO_OFFSET + 18, type_short, ARRAY_SIZE_1, DOP_1},
    {0x32, DCMU_PARA_ID_LOAD_CURRENT_ZERO_OFFSET + 19, type_short, ARRAY_SIZE_1, DOP_1},
    {0x33, DCMU_PARA_ID_LOAD_CURRENT_ZERO_OFFSET + 20, type_short, ARRAY_SIZE_1, DOP_1},
    {0x34, DCMU_PARA_ID_LOAD_CURRENT_ZERO_OFFSET + 21, type_short, ARRAY_SIZE_1, DOP_1},
    {0x35, DCMU_PARA_ID_LOAD_CURRENT_ZERO_OFFSET + 22, type_short, ARRAY_SIZE_1, DOP_1},
    {0x36, DCMU_PARA_ID_LOAD_CURRENT_ZERO_OFFSET + 23, type_short, ARRAY_SIZE_1, DOP_1},

    // 负载电流斜率
    {0x37, DCMU_PARA_ID_LOAD_CURRENT_SLOPE_OFFSET + 0, type_short, ARRAY_SIZE_1, DOP_2},
    {0x38, DCMU_PARA_ID_LOAD_CURRENT_SLOPE_OFFSET + 1, type_short, ARRAY_SIZE_1, DOP_2},
    {0x39, DCMU_PARA_ID_LOAD_CURRENT_SLOPE_OFFSET + 2, type_short, ARRAY_SIZE_1, DOP_2},
    {0x3A, DCMU_PARA_ID_LOAD_CURRENT_SLOPE_OFFSET + 3, type_short, ARRAY_SIZE_1, DOP_2},
    {0x3B, DCMU_PARA_ID_LOAD_CURRENT_SLOPE_OFFSET + 4, type_short, ARRAY_SIZE_1, DOP_2},
    {0x3C, DCMU_PARA_ID_LOAD_CURRENT_SLOPE_OFFSET + 5, type_short, ARRAY_SIZE_1, DOP_2},
    {0x3D, DCMU_PARA_ID_LOAD_CURRENT_SLOPE_OFFSET + 6, type_short, ARRAY_SIZE_1, DOP_2},
    {0x3E, DCMU_PARA_ID_LOAD_CURRENT_SLOPE_OFFSET + 7, type_short, ARRAY_SIZE_1, DOP_2},
    {0x3F, DCMU_PARA_ID_LOAD_CURRENT_SLOPE_OFFSET + 8, type_short, ARRAY_SIZE_1, DOP_2},
    {0x40, DCMU_PARA_ID_LOAD_CURRENT_SLOPE_OFFSET + 9, type_short, ARRAY_SIZE_1, DOP_2},
    {0x41, DCMU_PARA_ID_LOAD_CURRENT_SLOPE_OFFSET + 10, type_short, ARRAY_SIZE_1, DOP_2},
    {0x42, DCMU_PARA_ID_LOAD_CURRENT_SLOPE_OFFSET + 11, type_short, ARRAY_SIZE_1, DOP_2},
    {0x43, DCMU_PARA_ID_LOAD_CURRENT_SLOPE_OFFSET + 12, type_short, ARRAY_SIZE_1, DOP_2},
    {0x44, DCMU_PARA_ID_LOAD_CURRENT_SLOPE_OFFSET + 13, type_short, ARRAY_SIZE_1, DOP_2},
    {0x45, DCMU_PARA_ID_LOAD_CURRENT_SLOPE_OFFSET + 14, type_short, ARRAY_SIZE_1, DOP_2},
    {0x46, DCMU_PARA_ID_LOAD_CURRENT_SLOPE_OFFSET + 15, type_short, ARRAY_SIZE_1, DOP_2},
    {0x47, DCMU_PARA_ID_LOAD_CURRENT_SLOPE_OFFSET + 16, type_short, ARRAY_SIZE_1, DOP_2},
    {0x48, DCMU_PARA_ID_LOAD_CURRENT_SLOPE_OFFSET + 17, type_short, ARRAY_SIZE_1, DOP_2},
    {0x49, DCMU_PARA_ID_LOAD_CURRENT_SLOPE_OFFSET + 18, type_short, ARRAY_SIZE_1, DOP_2},
    {0x4A, DCMU_PARA_ID_LOAD_CURRENT_SLOPE_OFFSET + 19, type_short, ARRAY_SIZE_1, DOP_2},
    {0x4B, DCMU_PARA_ID_LOAD_CURRENT_SLOPE_OFFSET + 20, type_short, ARRAY_SIZE_1, DOP_2},
    {0x4C, DCMU_PARA_ID_LOAD_CURRENT_SLOPE_OFFSET + 21, type_short, ARRAY_SIZE_1, DOP_2},
    {0x4D, DCMU_PARA_ID_LOAD_CURRENT_SLOPE_OFFSET + 22, type_short, ARRAY_SIZE_1, DOP_2},
    {0x4E, DCMU_PARA_ID_LOAD_CURRENT_SLOPE_OFFSET + 23, type_short, ARRAY_SIZE_1, DOP_2},

    // 电池电压零点
    {0x4F, DCMU_PARA_ID_BATTERY_VOLT_ZERO_OFFSET + 0, type_short, ARRAY_SIZE_1, DOP_1},
    {0x50, DCMU_PARA_ID_BATTERY_VOLT_ZERO_OFFSET + 1, type_short, ARRAY_SIZE_1, DOP_1},
    {0x51, DCMU_PARA_ID_BATTERY_VOLT_ZERO_OFFSET + 2, type_short, ARRAY_SIZE_1, DOP_1},
    {0x52, DCMU_PARA_ID_BATTERY_VOLT_ZERO_OFFSET + 3, type_short, ARRAY_SIZE_1, DOP_1},

    // 电池电流零点
    {0x53, DCMU_PARA_ID_BATTERY_CURRENT_ZERO_OFFSET + 0, type_short, ARRAY_SIZE_1, DOP_1}, 
    {0x54, DCMU_PARA_ID_BATTERY_CURRENT_ZERO_OFFSET + 1, type_short, ARRAY_SIZE_1, DOP_1}, 
    {0x55, DCMU_PARA_ID_BATTERY_CURRENT_ZERO_OFFSET + 2, type_short, ARRAY_SIZE_1, DOP_1}, 
    {0x56, DCMU_PARA_ID_BATTERY_CURRENT_ZERO_OFFSET + 3, type_short, ARRAY_SIZE_1, DOP_1},

    // 电池电流斜率
    {0x57, DCMU_PARA_ID_BATTERY_CURRENT_SLOPE_OFFSET + 0, type_short, ARRAY_SIZE_1, DOP_2},
    {0x58, DCMU_PARA_ID_BATTERY_CURRENT_SLOPE_OFFSET + 1, type_short, ARRAY_SIZE_1, DOP_2},
    {0x59, DCMU_PARA_ID_BATTERY_CURRENT_SLOPE_OFFSET + 2, type_short, ARRAY_SIZE_1, DOP_2},
    {0x5A, DCMU_PARA_ID_BATTERY_CURRENT_SLOPE_OFFSET + 3, type_short, ARRAY_SIZE_1, DOP_2},

    // 电池温度零点
    {0x5B, DCMU_PARA_ID_BATTERY_TEMP_ZERO_OFFSET + 0, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x5C, DCMU_PARA_ID_BATTERY_TEMP_ZERO_OFFSET + 1, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x5D, DCMU_PARA_ID_BATTERY_TEMP_ZERO_OFFSET + 2, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x5E, DCMU_PARA_ID_BATTERY_TEMP_ZERO_OFFSET + 3, type_unsigned_char, ARRAY_SIZE_1, DOP_0},

    // 蜂鸣器开关
    {0x5F, DCMU_PARA_ID_BUZZER_SWITCH_OFFSET, type_unsigned_char, ARRAY_SIZE_1, DOP_0},

    // 主动告警功能
    {0x60, DCMU_PARA_ID_ALARM_ENABLE_OFFSET, type_unsigned_char, ARRAY_SIZE_1, DOP_0},

    // 告警BP号码
    {0x61, DCMU_PARA_ID_ALARM_BP_OFFSET, type_string, ARRAY_SIZE_20, DOP_0},

    // 告警电话号码
    {0x62, DCMU_PARA_ID_ALARM_PHONE_OFFSET, type_string, ARRAY_SIZE_20, DOP_0},

    // 历史数据保存时间间隔
    {0x63, DCMU_PARA_ID_HISTORY_SAVE_INTERVAL_OFFSET, type_unsigned_char, ARRAY_SIZE_1, DOP_0},

    // 串口波特率设置
    {0x64, DCMU_PARA_ID_UART_BAUDRATE_OFFSET + 1, type_unsigned_char, ARRAY_SIZE_1, DOP_0},

    // 本机地址
    {0x65, DCMU_PARA_ID_DEVICE_ADDR_OFFSET, type_short, ARRAY_SIZE_1, DOP_0},

    // 主动上报间隔时间
    {0x66, DCMU_PARA_ID_REPORT_INTERVAL_OFFSET, type_short, ARRAY_SIZE_1, DOP_0},

    // 语言设置
    {0x67, DCMU_PARA_ID_LANGUAGE_SET_OFFSET, type_unsigned_char, ARRAY_SIZE_1, DOP_0},

    // 菜单口令设置
    {0x68, DCMU_PARA_ID_MENU_PASERWORD_OFFSET, type_string, ARRAY_SIZE_4, DOP_0},

    // 使用场景
    {0x69, DCMU_PARA_ID_USAGE_SCENARIO_OFFSET, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
};

static cmd_t no_poll_cmd_tab[] = {
    {DCMU_GET_INRELAY_INFO, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),   NULL, NULL, dcmu_pack_para_comm,NULL,},
    {DCMU_SET_INRELAY_INFO, CMD_PASSIVE, &cmd_req[1], &cmd_ack[1], sizeof(s1363_cmd_head_t),   NULL, NULL, NULL, dcmu_parse_para_comm, },
    {DCMU_REMOTE_CONTROL,   CMD_PASSIVE, &cmd_req[2], &cmd_ack[2], sizeof(s1363_cmd_head_t),   NULL, NULL, NULL, dcmu_parse_remote_control, },
    {DCMU_GET_SYSPARA, CMD_PASSIVE, &cmd_req[3], &cmd_ack[3], sizeof(s1363_cmd_head_t),   NULL, NULL, dcmu_pack_para_comm,NULL,},
    {DCMU_SET_SYSPARA, CMD_PASSIVE, &cmd_req[4], &cmd_ack[4], sizeof(s1363_cmd_head_t),   NULL, NULL, NULL, dcmu_parse_para_comm, },
    {DCMU_GET_ALMRELAY, CMD_PASSIVE, &cmd_req[5], &cmd_ack[5], sizeof(s1363_cmd_head_t),   NULL, NULL, dcmu_pack_para_comm,NULL,},
    {DCMU_SET_ALMRELAY, CMD_PASSIVE, &cmd_req[6], &cmd_ack[6], sizeof(s1363_cmd_head_t),   NULL, NULL, NULL, dcmu_parse_para_comm, },
    {DCMU_GET_ALMLEVEL, CMD_PASSIVE, &cmd_req[7], &cmd_ack[7], sizeof(s1363_cmd_head_t),   NULL, NULL, dcmu_pack_para_comm,NULL,},
    {DCMU_SET_ALMLEVEL, CMD_PASSIVE, &cmd_req[8], &cmd_ack[8], sizeof(s1363_cmd_head_t),   NULL, NULL, NULL, dcmu_parse_para_comm, },
    {DCMU_DC_ALARM_DATA, CMD_PASSIVE, &cmd_req[9], &cmd_ack[9], sizeof(s1363_cmd_head_t),   NULL, NULL, dcmu_pack_dc_alm_data,NULL,},
    {DCMU_GET_CUSTOMPARA, CMD_PASSIVE, &cmd_req[10], &cmd_ack[10], sizeof(s1363_cmd_head_t),   NULL, NULL, dcmu_pack_para_comm,NULL,},
    {DCMU_SET_CUSTOMPARA, CMD_PASSIVE, &cmd_req[11], &cmd_ack[11], sizeof(s1363_cmd_head_t),   NULL, NULL, NULL, dcmu_parse_para_comm, },
    {DCMU_ENV_ALARM_DATA, CMD_PASSIVE, &cmd_req[12], &cmd_ack[12], sizeof(s1363_cmd_head_t),   NULL, NULL, dcmu_pack_env_alm_data, NULL, },
    {DCMU_GET_TIME, CMD_PASSIVE, &cmd_req[13], &cmd_ack[13], sizeof(s1363_cmd_head_t),   NULL, NULL, dcmu_pack_time, NULL, },
    {DCMU_SET_TIME, CMD_PASSIVE, &cmd_req[14], &cmd_ack[14], sizeof(s1363_cmd_head_t),   NULL, NULL, NULL, dcmu_parse_time, },
    {DCMU_GET_ANALOG, CMD_PASSIVE, &cmd_req[15], &cmd_ack[15], sizeof(s1363_cmd_head_t),   NULL, NULL, dcmu_pack_analog, NULL, },
    {DCMU_GET_SW_STATUS, CMD_PASSIVE, &cmd_req[16], &cmd_ack[16], sizeof(s1363_cmd_head_t),   NULL, NULL, dcmu_pack_sw_status, NULL, },
    {DCMU_GET_HIS_ALARM, CMD_PASSIVE, &cmd_req[17], &cmd_ack[17], sizeof(s1363_cmd_head_t),   NULL, NULL, dcmu_pack_his_alarm, dcmu_parse_his_alarm, },
    {DCMU_GET_ENV_ANALOG, CMD_PASSIVE, &cmd_req[18], &cmd_ack[18], sizeof(s1363_cmd_head_t),   NULL, NULL, dcmu_pack_env_analog, NULL, },
    {DCMU_GET_FACTORY_INFO_CUSTOM, CMD_PASSIVE, &cmd_req[19], &cmd_ack[19], sizeof(s1363_cmd_head_t),   NULL, NULL, dcmu_pack_factory_info_custom, NULL, },
    {DCMU_GET_ENV_PARA, CMD_PASSIVE, &cmd_req[20], &cmd_ack[20], sizeof(s1363_cmd_head_t),   NULL, NULL, dcmu_pack_env_para_comm,NULL,},
    {DCMU_SET_ENV_PARA, CMD_PASSIVE, &cmd_req[21], &cmd_ack[21], sizeof(s1363_cmd_head_t),    NULL, NULL, NULL, dcmu_parse_para_comm,},
    {DCMU_GET_ENV_ALMLEVEL, CMD_PASSIVE, &cmd_req[22], &cmd_ack[22], sizeof(s1363_cmd_head_t),   NULL, NULL, dcmu_pack_env_para_comm,NULL,},
    {DCMU_SET_ENV_ALMLEVEL, CMD_PASSIVE, &cmd_req[23], &cmd_ack[23], sizeof(s1363_cmd_head_t),    NULL, NULL, NULL, dcmu_parse_para_comm,},
    {DCMU_GET_ENV_ALMRELAY, CMD_PASSIVE, &cmd_req[24], &cmd_ack[24], sizeof(s1363_cmd_head_t),   NULL, NULL, dcmu_pack_env_para_comm,NULL,},
    {DCMU_SET_ENV_ALMRELAY, CMD_PASSIVE, &cmd_req[25], &cmd_ack[25], sizeof(s1363_cmd_head_t),    NULL, NULL, NULL, dcmu_parse_para_comm, },
    {DCMU_GET_BOOT_VERSION, CMD_PASSIVE, &cmd_req[26], &cmd_ack[26], sizeof(s1363_cmd_head_t),   NULL, NULL, dcmu_pack_boot_ver, NULL, },
    {0},
};

Static dev_type_t dev_dcmu_1363 = {
  DEV_NORTH_1363, 1, PROTOCOL_YD_1363, LINK_DCMU, R_BUFF_LEN, S_BUFF_LEN, 0, &no_poll_cmd_tab[0],
};

dev_type_t* init_dev_dcmu_1363(void)
{
    return &dev_dcmu_1363;
}

Static char dcmu_pack_para_handle(dcmu_para_info para_info,unsigned char* buff)
{
    str64 str = {0};
    float f_data = 0.0f;
    unsigned char uc_data; 
    char c_data; 
    rt_memset_s(&para_value, sizeof(dcmu_para_value), 0, sizeof(dcmu_para_value));

    unsigned char save_type = GET_SYS_PARA_DATA_TYPE(para_info.sid);

    // 根据存储类型转换并组包
    switch (save_type) {
        case TYPE_INT8U:
                get_one_para(para_info.sid, &uc_data);
                para_value.t_data = (short)uc_data;
            break;
        case TYPE_INT8S:
                get_one_para(para_info.sid, &c_data);
                para_value.t_data = (short)c_data;
            break;
        case TYPE_INT16U:
                get_one_para(para_info.sid, &para_value.t_data);
            break;
        case TYPE_FLOAT:
                get_one_para(para_info.sid, &f_data);
                para_value.t_data = f_data * pow(10,para_info.precision);
            break;
        case TYPE_STRING:
                get_one_para(para_info.sid, str);
            break;
        default:
            break;
    }

    switch (para_info.data_type) 
    {
        case type_unsigned_char:
            *buff = (unsigned char)para_value.uc_data;
            return sizeof(unsigned char);
        case type_char:
            *buff = para_value.c_data;
            return sizeof( char);
        case type_short:
            put_int16_to_buff(buff, para_value.t_data);
            return sizeof(short);
        case type_string:
            rt_memset_s(buff,para_info.array_len,0x20,para_info.array_len);
            rt_memcpy_s(buff, para_info.array_len,str, rt_strnlen_s(str,para_info.array_len));
            return para_info.array_len;
        default:
            break;
    }

    return 0;
}

Static char dcmu_parse_para_handle(dcmu_para_info para_info,unsigned char* buff)
{
    signed char rst = -1;
    str64 str = {0};
    float f_data = 0.0f;
    unsigned char save_type = 0;
    rt_memset_s(&para_value, sizeof(dcmu_para_value), 0, sizeof(dcmu_para_value));

    switch (para_info.data_type) 
    {
        case type_unsigned_char:
            para_value.uc_data = buff[0];
            f_data = para_value.uc_data;
            break;
        case type_char:
            para_value.c_data = buff[0];
            f_data = para_value.c_data;
            break;
        case type_short:
            para_value.t_data = get_int16_data(&buff[0]);
            f_data = para_value.t_data;
            break;
        case type_string:
            if(s_bufflen > para_info.array_len)
            {
                return FAILURE;
            }
            rt_memset_s(str,sizeof(str64),0,sizeof(str64));
            rt_memcpy_s(str, s_bufflen, &buff[0], s_bufflen);
            break;
        default:
            break;
    }

    // 根据存储类型转换并存储
    save_type = GET_SYS_PARA_DATA_TYPE(para_info.sid);
    
    switch (save_type) {
        case TYPE_INT8U:
                rst = set_one_para(para_info.sid, &para_value.uc_data, TRUE, TRUE);
            break;
        case TYPE_INT8S:
                rst = set_one_para(para_info.sid, &para_value.c_data, TRUE, TRUE);
            break;
        case TYPE_INT16U:
                rst = set_one_para(para_info.sid, &para_value.t_data, TRUE, TRUE);
            break;
        case TYPE_FLOAT:
                f_data = f_data / pow(10,para_info.precision);
                rst = set_one_para(para_info.sid, &f_data, TRUE, TRUE);
            break;
        case TYPE_STRING:
                rst = set_one_para(para_info.sid, str, TRUE, TRUE);
            break;
        default:
                rst = FAILURE;
            break;
    }

    return rst;
}


Static int oper_after_save_custormpara(signed char opr_rst, unsigned char cmd_type) {
    if (opr_rst < 0) {
        return FAILURE;
    }
    switch (cmd_type) {
        case 0x64:    // 串口波特率设置
            dcmu_update_baudrate();
            break;
        case 0x65:    // 本机地址
            dcmu_update_host_address();
            break;
        default:
            return FAILURE;
    }
    return SUCCESSFUL;
}

static int dcmu_LookupTable_func(dcmu_para_info *para,unsigned char* buff,unsigned char len,unsigned char parse_type)
{
    signed char rst = -1;
    unsigned char command_type = 0;

    command_type = buff[0];

    for (size_t i = 0; i < len; i++) {
        if (para[i].command_type == command_type) {
            if(PARSE_PARA == parse_type)
            {
                rst = dcmu_parse_para_handle(para[i],&buff[1]);
            }
            else
            {
                rst = set_alm_para(para[i].sid,&buff[1],TRUE);
            }
            oper_after_save_custormpara(rst, command_type);
            return rst;
        }
    }
    return FAILURE;
}

Static int dcmu_parse_para_comm(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff;
    unsigned char cmt_id = 0;
    unsigned char len = 0;
    signed char rst = 0;
    buff = cmd_buf_temp->buf;
    s_bufflen = cmd_buf_temp->data_len - 1;

    cmt_id = cmd_buf_temp->cmd->cmd_id;

    switch (cmt_id)
    {
        case DCMU_SET_INRELAY_INFO:
                len = ARR_SIZE(s_inrelay_info);
                rst = dcmu_LookupTable_func(s_inrelay_info,buff,len,PARSE_PARA);
            break;
        case DCMU_SET_SYSPARA:
                len = ARR_SIZE(s_syspara_info);
                rst = dcmu_LookupTable_func(s_syspara_info,buff,len,PARSE_PARA);
            break;
        case DCMU_SET_ALMRELAY:
                len = ARR_SIZE(s_almrelay_info);
                rst = dcmu_LookupTable_func(s_almrelay_info,buff,len,PARSE_ALM);
            break;
        case DCMU_SET_ALMLEVEL:
                len = ARR_SIZE(s_almlevel_info);
                rst = dcmu_LookupTable_func(s_almlevel_info,buff,len,PARSE_ALM);
            break;
        case DCMU_SET_CUSTOMPARA:
                if(dcmu_checkMenuKey(buff,s_bufflen) != 0)
                {
                    rst = FAILURE;
                }
                else
                {
                    len = ARR_SIZE(s_custompara_info);
                    rst = dcmu_LookupTable_func(s_custompara_info,buff,len,PARSE_PARA);
                }
            break;
        case DCMU_SET_ENV_PARA:
                len = ARR_SIZE(s_env_para_info);
                if(buff[0] != s_command_group)
                {
                    rst = FAILURE;
                }
                else
                {
                    rst = dcmu_LookupTable_func(s_env_para_info,buff+1,len,PARSE_PARA);
                }            
            break;
        case DCMU_SET_ENV_ALMLEVEL:
                len = ARR_SIZE(s_env_almlevel_info);
                rst = dcmu_LookupTable_func(s_env_almlevel_info,buff,len,PARSE_ALM);
            break;
        case DCMU_SET_ENV_ALMRELAY:
                len = ARR_SIZE(s_env_almrelay_info);
                rst = dcmu_LookupTable_func(s_env_almrelay_info,buff,len,PARSE_ALM);
            break;
        default:
                rst = FAILURE;
            break;
    }

    if(rst < 0)
    {
        cmd_buf_temp->rtn = RTN_WRONG_COMMAND;
    }
    else
    {
        update_para();
    }
    
    return SUCCESSFUL;
}

Static int dcmu_pack_para_comm(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff;
    unsigned char uc_data = 0;
    unsigned char cmt_id = 0;
    unsigned char offset = 0;

    cmt_id = cmd_buf_temp->cmd->cmd_id;
    buff = cmd_buf_temp->buf;

    switch (cmt_id)
    {
        case DCMU_GET_INRELAY_INFO:
            {
                buff[offset++] = RELAY_NUM;                            /* 干接点数量 */

                for (int i = 0; i < ARR_SIZE(s_inrelay_info); i++) {
                    offset += dcmu_pack_para_handle(s_inrelay_info[i], &buff[offset]);
                }
            }
            break;
        case DCMU_GET_SYSPARA:
            {
                // 处理前两个参数
                for (int i = 0; i < 2; i++) {
                    offset += dcmu_pack_para_handle(s_syspara_info[i], &buff[offset]);
                }

                // 设置自定义参数数量
                buff[offset++] = ARR_SIZE(s_syspara_info) - 2;

                // 处理剩余的自定义参数
                for (int i = 2; i < ARR_SIZE(s_syspara_info); i++) {
                    offset += dcmu_pack_para_handle(s_syspara_info[i], &buff[offset]);
                }
            }
            break;
        case DCMU_GET_ALMRELAY:
            {
                buff[offset++] = ARR_SIZE(s_almrelay_info);       //告警干接点数量

                for (size_t i = 0; i < ARR_SIZE(s_almrelay_info); i++) {
                    get_alm_para(s_almrelay_info[i].sid,&uc_data);
                    buff[offset++]  = uc_data;
                }
            }
            break;
        case DCMU_GET_ALMLEVEL:
            {
                buff[offset++] = ARR_SIZE(s_almlevel_info);       //告警级别数量

                for (size_t i = 0; i < ARR_SIZE(s_almlevel_info); i++) {
                    get_alm_para(s_almlevel_info[i].sid,&uc_data);
                    buff[offset++]  = uc_data;
                }
            }
            break;
        case DCMU_GET_CUSTOMPARA:
            {
                for (int i = 0; i < ARR_SIZE(s_custompara_info); i++) {
                    offset += dcmu_pack_para_handle(s_custompara_info[i], &buff[offset]);
                }
            }
            break;       
        default:
            break;
    }

     /* LENID */
    cmd_buf_temp->data_len = offset;
    
    return SUCCESSFUL;
}

Static int dcmu_pack_env_para_comm(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff;
    unsigned char uc_data = 0;
    unsigned char cmt_id = 0;
    unsigned char offset = 0;

    cmt_id = cmd_buf_temp->cmd->cmd_id;
    buff = cmd_buf_temp->buf;

    switch (cmt_id)
    {
        case DCMU_GET_ENV_PARA:
            {
                buff[offset++] = 0x01;                            /* 温度传感器数量 */
                // 温度两个参数
                for (int i = 0; i < 2; i++) {
                    offset += dcmu_pack_para_handle(s_env_para_info[i], &buff[offset]);
                }

                buff[offset++] = 0x01;                            /* 湿度传感器数量 */
                // 湿度两个参数
                for (int i = 2; i < 4; i++) {
                    offset += dcmu_pack_para_handle(s_env_para_info[i], &buff[offset]);
                }

                buff[offset++] = 0x02;                            /* 自定义参数数量 */
                // 自定义两个参数
                for (int i = 4; i < 6; i++) {
                    offset += dcmu_pack_para_handle(s_env_para_info[i], &buff[offset]);
                }
            }
            break;
        case DCMU_GET_ENV_ALMLEVEL:
            {
                buff[offset++] = 11;       //环境告警级别数量

                for (size_t i = 0; i < ARR_SIZE(s_env_almlevel_info); i++) {
                    get_alm_para(s_env_almlevel_info[i].sid,&uc_data);
                    buff[offset++]  = uc_data;
                }
            }
            break;
        case DCMU_GET_ENV_ALMRELAY:
            {
                buff[offset++] = 11;       //环境告警干接点数量

                for (size_t i = 0; i < ARR_SIZE(s_env_almrelay_info); i++) {
                    get_alm_para(s_env_almrelay_info[i].sid,&uc_data);
                    buff[offset++]  = uc_data;
                }
            }
            break;
        default:
            break;
    }

     /* LENID */
    cmd_buf_temp->data_len = offset;
    
    return SUCCESSFUL;
}

Static char dcmu_checkMenuKey(unsigned char* buff,unsigned char len)
{
    unsigned char command_type = 0;

    command_type = buff[0];

    if (command_type == 0x68) //0x68是菜单口令的command_type
    {
        for(int i = 1;i <= MENUKEY_LEN;i++)
        {
            if(!((buff[i]>= '0') && (buff[i] <= '9')))
            {
                return FAILURE;
            }
        }
    }
    return SUCCESSFUL;
}

Static signed char dcmu_ctrl_reset(void)
{
    static rt_timer_t dcmu_reset = NULL;

    if (dcmu_reset == NULL)
    {
        dcmu_reset = rt_timer_create("dcmu_reset", rt_hw_cpu_reset, NULL, 1000, RT_TIMER_FLAG_ONE_SHOT);
    }

    if (dcmu_reset != NULL)
    {
        rt_timer_stop(dcmu_reset);
        rt_timer_start(dcmu_reset);
    }
    else
    {
        return FAILURE;
    }

    set_software_reset_reason(RESET_REASON_REMOTE_CTRL);
    return SUCCESSFUL;
}

Static int dcmu_parse_remote_control(void* dev_inst, void *cmd_buf)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    unsigned char* buff;
    unsigned char command_type = 0;
    unsigned char relay_index = 0;
    signed char rst = 0;
    buff = cmd_buf_temp->buf;
    unsigned char manual_control_mode = 0;
    T_CtrlOutStruct tCtrl = {0,};

    /* COMMAND_TYPE */
    command_type = buff[0];
    switch(command_type) {
        case 0x10:          // 系统复位
            rst = dcmu_ctrl_reset();
            break;

        case 0x11:          // 恢复默认运行参数
            rst = restore_para_to_fact(EXCLUDE_TYPE_RUNTIME);
            update_para();
            pub_hisaction_save_msg(ID1_DCMU_CTRL, ID2_CTL_LDRUNPARA, 0xFF, NULL);
            break;

        case 0x12:          // 恢复默认配置参数
            rst = restore_para_to_fact(EXCLUDE_TYPE_CONFIG);
            update_para();
            dcmu_update_baudrate();
            dcmu_update_host_address();
            pub_hisaction_save_msg(ID1_DCMU_CTRL, ID2_CTL_LDCFGPARA, 0xFF, NULL);
            break;

        case 0x13 ... 0x1A:          // 输出干接点1、2、3、4、5、6、7、8恢复
            relay_index = command_type - 0x13;
            rst = manual_control_out_relay(relay_index, CTRL_OFF);
            pub_hisaction_save_msg(ID1_DCMU_CTRL, ID2_CTL_RLYRESET, relay_index, NULL);
            break;

        case 0x1B ... 0x22:          // 输出干接点1、2、3、4、5、6、7、8动作
            relay_index = command_type - 0x1B;
            rst = manual_control_out_relay(relay_index, CTRL_ON);
            pub_hisaction_save_msg(ID1_DCMU_CTRL, ID2_CTL_RLYACT, relay_index, NULL);
            break;

        case 0x23:          // 输出干节点手动控制
            clear_ctrl_out();
            manual_control_mode = 1;
            set_one_data(DCMU_DATA_ID_OUTRELAY_MANUAL_CONTROL_MODE, &manual_control_mode);
            pub_hisaction_save_msg(ID1_DCMU_CTRL, ID2_CTL_RLYFORCECTL, 0, NULL);
            break;

        case 0x24:          // 输出干节点非手动控制
            clear_ctrl_out();
            pub_hisaction_save_msg(ID1_DCMU_CTRL, ID2_CTL_RLYNOTFORCECTL, 0, NULL);
            break;

        case 0x25 ... 0x27:          // 删除操作记录、历史告警、历史数据、极值记录
            rst = delete_record_data_new(AUTH_CODE_HIS_ACTION + command_type - 0x25, RECORD_TYPE_HIS_ACTION + command_type - 0x25);
            pub_hisaction_save_msg(ID1_DCMU_CTRL, ID2_CTL_DELACTIONREC + command_type - 0x25, 0xFF, NULL);
            break;

        case 0x28:
            delete_extreme_data();
            pub_hisaction_save_msg(ID1_DCMU_CTRL, ID2_CTL_DELPEAKREC, 0xFF, NULL);
            break;

        case 0x29:          // 删除电量数据（包括总电量和负载电量）
            rst = clear_power_data();
            pub_hisaction_save_msg(ID1_DCMU_CTRL, ID2_CTL_DELPOWERDATA, 0xFF, NULL);
            break;

        case 0x2A:          // 删除所有记录数据
            // 删除操作记录
            rst += delete_record_data_new(AUTH_CODE_HIS_ACTION, RECORD_TYPE_HIS_ACTION);
            // 删除历史告警
            rst += delete_record_data_new(AUTH_CODE_HIS_ALARM, RECORD_TYPE_HIS_ALARM);
            // 删除历史数据
            rst += delete_record_data_new(AUTH_CODE_HIS_DATA, RECORD_TYPE_HIS_DATA);
            // 删除极值记录
            delete_extreme_data();
            // 删除电量数据
            rst += clear_power_data();
            pub_hisaction_save_msg(ID1_DCMU_CTRL, ID2_CTL_DELALLREC, 0xFF, NULL);
            break;

        default:
            cmd_buf_temp->rtn = RTN_WRONG_COMMAND;
            break;
    }

    if(rst != SUCCESSFUL){
        cmd_buf_temp->rtn = RTN_CTRL_FAIL;
    }

    return SUCCESSFUL;
}

Static signed char manual_control_out_relay(unsigned char relay_index, unsigned char control_status)
{
    unsigned char manual_control_mode = 0;
    T_CtrlOutStruct tCtrl = {0,};

    get_one_data(DCMU_DATA_ID_OUTRELAY_MANUAL_CONTROL_MODE, &manual_control_mode);
    if(manual_control_mode == FALSE)   // 如果不在手动控制模式，则控制不会生效
    {
        return FAILURE;
    }
    GetCtrlOut( &tCtrl );
    tCtrl.wRelayOut |= 0x0001 << relay_index;
    tCtrl.aucRelay[relay_index] = control_status;
    SetCtrlOut( &tCtrl );
    return SUCCESSFUL;
}

Static int dcmu_pack_dc_alm_data(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff = cmd_buf_temp->buf;
    unsigned char offset = 0;
    int alm_id = 0;

    /* DATA_FLAG */
    unsigned char data_flag;
    get_one_data(DCMU_DATA_ID_DATA_FLAG, &data_flag);
    buff[offset++] = data_flag & 0xFE;
    
    /* WARN_STATE */
    buff[offset++] = 0x01;                    //直流屏数量为1

    buff[offset++] = judge_voltage_alm_status(DCMU_ALM_ID_DC_VOLT_LOW, DCMU_ALM_ID_DC_VOLT_HIGH, DCMU_ALM_ID_DC_VOLTAGE_TOO_LOW, DCMU_ALM_ID_DC_VOLTAGE_TOO_HIGH, 0); // 直流电压状态

    buff[offset++] = BATTERY_FUSE_NUM;    // 电池熔丝数量
    for (char i = 0; i < BATTERY_FUSE_NUM; i++)
    {
        alm_id = GET_ALM_ID(DCMU_ALM_ID_BATTERY_LOOP_BROKEN, i + 1, 1);  //电池回路断告警
        buff[offset++] = handle_alarm_status(alm_id, 0x03, 0x00);
    }
    
    buff[offset++] = LOAD_FUSE_SWITCH_NUM;        // 监测负载分录熔丝开关数量
    for (char i = 0; i < LOAD_FUSE_SWITCH_NUM; i++)
    {
         alm_id = GET_ALM_ID(DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN, i + 1, 1);  //直流负载熔丝告警
        buff[offset++] = handle_alarm_status(alm_id, 0x03, 0x00);
    }

    alm_id = GET_ALM_ID(DCMU_ALM_ID_INSULATION_ALARM, 1, 1);  // 绝缘告警
    buff[offset++] = handle_alarm_status(alm_id, 0x03, 0x00);

    buff[offset++] = 22;        // 自定义告警数量为22

    alm_id = GET_ALM_ID(DCMU_ALM_ID_TOTAL_ALARM,1,1);//总告警
    buff[offset++] = handle_alarm_status(alm_id, 0xF0, 0x00);

    alm_id = GET_ALM_ID(DCMU_ALM_ID_DC_LIGHTNING_PROTECTOR_DAMAGED,1,1);//直流防雷器告警
    buff[offset++] = handle_alarm_status(alm_id, 0xE1, 0x00);

    for (char i = 0; i < BATTERY_FUSE_NUM; i++)
    {
        buff[offset++] = judge_voltage_alm_status(DCMU_ALM_ID_BATTERY_VOLTAGE_LOW, 0, DCMU_ALM_ID_BATTERY_VOLTAGE_TOO_LOW, 0, i); // 直流电压状态
    }

    for (char i = 0; i < BATTERY_FUSE_NUM; i++)
    {
        alm_id = GET_ALM_ID(DCMU_ALM_ID_ABNORMAL_BATTERY_CURRENT, i + 1, 1);  //电池电流异常
        buff[offset++] = handle_alarm_status(alm_id, 0xE2, 0x00);
    }

    for (char i = 0; i < BATTERY_FUSE_NUM; i++)
    {
        buff[offset++] = judge_battery_temperature_sensor_alarm_status(i);
    }

    for (char i = 0; i < BATTERY_FUSE_NUM; i++)
    {
        alm_id = GET_ALM_ID(DCMU_ALM_ID_BATTERY_DISCHARGE, i + 1, 1);  //电池放电告警
        buff[offset++] = handle_alarm_status(alm_id, 0xE2, 0x00);
    }

    alm_id = GET_ALM_ID(DCMU_ALM_ID_INPUT_RELAY1_ALARM, 1, 1);  //输入干接点1告警
    buff[offset++] = handle_alarm_status(alm_id, 0xE2, 0x00);
    alm_id = GET_ALM_ID(DCMU_ALM_ID_INPUT_RELAY2_ALARM, 1, 1);  //输入干接点2告警
    buff[offset++] = handle_alarm_status(alm_id, 0xE2, 0x00);
    alm_id = GET_ALM_ID(DCMU_ALM_ID_INPUT_RELAY3_ALARM, 1, 1);  //输入干接点3告警
    buff[offset++] = handle_alarm_status(alm_id, 0xE2, 0x00);
    alm_id = GET_ALM_ID(DCMU_ALM_ID_INPUT_RELAY4_ALARM, 1, 1);  //输入干接点4告警
    buff[offset++] = handle_alarm_status(alm_id, 0xE2, 0x00);   

    /* LENID */
    cmd_buf_temp->data_len = offset;
    
    return SUCCESSFUL;
}

Static int dcmu_pack_time(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    time_base_t tm  = {0};
    unsigned char* buff;
    unsigned char offset = 0;
    buff = cmd_buf_temp->buf;

    time_t tTime = time(RT_NULL);
    time_t_to_timestruct(tTime,&tm);

    put_uint16_to_buff(buff, tm.year);
    offset += 2;
    buff[offset++] = tm.month;
    buff[offset++] = tm.day;
    buff[offset++] = tm.hour;
    buff[offset++] = tm.minute;
    buff[offset++] = tm.second;

    /* LENID */
    cmd_buf_temp->data_len = offset;

    return SUCCESSFUL;
}

Static int dcmu_parse_time(void* dev_inst, void* cmd_buff)
{
    time_base_t last_tm = {0},tm = {0};
    int offset = 0;
    char info[20] = {0};

    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    time_t tTime = time(RT_NULL);
    time_t_to_timestruct(tTime,&last_tm);

    unsigned char *data_buff = ((cmd_buf_t *)cmd_buff)->buf;

    tm.year = get_int16_data(&data_buff[offset]);
    offset += 2;
    tm.month = data_buff[offset++];
    tm.day = data_buff[offset++];
    tm.hour = data_buff[offset++];
    tm.minute = data_buff[offset++];
    tm.second = data_buff[offset++];

    if( check_time_range(tm) == FAILURE )
    {
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA ;
        return SUCCESSFUL;
    }

    /* 设置系统时间 */
    if (set_system_time(&tm) != SUCCESSFUL) {
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA;
        return SUCCESSFUL;
    }

    rt_snprintf(info, sizeof(info),
                "%d-%02d-%02d %02d:%02d:%02d", 
               last_tm.year, last_tm.month, last_tm.day, last_tm.hour, last_tm.minute, last_tm.second);
    pub_hisaction_save_msg(ID1_DCMU_SETSTATUS, ID2_PR_TIME, 0, info);

    return SUCCESSFUL;
}

// 辅助函数实现
Static unsigned char handle_alarm_status(int alm_id, unsigned char value_if_exist, unsigned char value_if_not_exist) {
    return (get_realtime_alarm_value(alm_id) == ALARM_EXSIT) ? value_if_exist : value_if_not_exist;
}


Static unsigned char judge_voltage_alm_status(int alm_id_low, int alm_id_high, int alm_id_too_low, int alm_id_too_high ,char sig_id)
{
    unsigned char alarm_status_high, alarm_status_low, alarm_status_too_high, alarm_status_too_low;
    
    alarm_status_low = judge_alarm_status(alm_id_low, sig_id);
    alarm_status_high = judge_alarm_status(alm_id_high, sig_id);
    alarm_status_too_low = judge_alarm_status(alm_id_too_low, sig_id);
    alarm_status_too_high = judge_alarm_status(alm_id_too_high, sig_id);

    if(alarm_status_low == ALARM_EXSIT)
    {
        return 0x01;
    }else if(alarm_status_high == ALARM_EXSIT)
    {
        return 0x02;
    }else if(alarm_status_too_low == ALARM_EXSIT)
    {
        return 0xE4;
    }else if(alarm_status_too_high == ALARM_EXSIT)
    {
        return 0xE5;
    }else
    {
        return 0x00;
    }
}

Static unsigned char judge_alarm_status(int alm_id, char sig_id)
{
    if(alm_id == 0)
    {
        return 0;
    }

    int alm_id_real;
    unsigned char alarm_status = 0;
    alm_id_real = GET_ALM_ID(alm_id, sig_id + 1, 1);
    alarm_status = get_realtime_alarm_value(alm_id_real);
    return alarm_status;
}

Static unsigned char judge_battery_temperature_sensor_alarm_status(char sig_id)
{
    unsigned char ret = 0;
    char alarm_status = 0;
    int alm_id = 0;

    alm_id = GET_ALM_ID(DCMU_ALM_ID_BATTERY_TEMPERATURE_HIGH, sig_id + 1,1);
    alarm_status = get_realtime_alarm_value(alm_id);
    if(TRUE == alarm_status){
        ret = 2;    // 电池温度高告警
    }

    alm_id = GET_ALM_ID(DCMU_ALM_ID_BATTERY_TEMPERATURE_TOO_HIGH, sig_id + 1,1);
    alarm_status = get_realtime_alarm_value(alm_id);
    if(TRUE == alarm_status){
        ret = 0xE5;    //电池温度过高告警
    }

    alm_id = GET_ALM_ID(DCMU_ALM_ID_BATTERY_TEMPERATURE_LOW, sig_id + 1,1);
    alarm_status = get_realtime_alarm_value(alm_id);
    if(TRUE == alarm_status){
        ret = 1;    // 电池温度低告警
    }

    alm_id = GET_ALM_ID(DCMU_ALM_ID_INVALID_BATTERY_TEMPERATURE, sig_id + 1,1);
    alarm_status = get_realtime_alarm_value(alm_id);
    if(TRUE == alarm_status){
        ret = 0xE3;    // 电池温度无效告警
    }

    return ret;
}

Static int dcmu_pack_env_alm_data(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    int alm_id = 0;
    unsigned char* buff;
    unsigned char offset = 0,data_flag;
    buff = cmd_buf_temp->buf;

    /* DATA_FLAG */
    get_one_data(DCMU_DATA_ID_DATA_FLAG,&data_flag);
    buff[offset++] = data_flag & 0xFE;
    
    /* WARN_STATE */
    buff[offset++] = 0x01;                    //温度传感器数量为1

    buff[offset++] = judge_env_temperature_sensor_alarm_status(DCMU_ALM_ID_ENV_TEMP_LOW_ALARM, DCMU_ALM_ID_ENV_TEMP_HIGH_ALARM, DCMU_ALM_ID_ENV_TEMP_SENSOR_INVALID_ALARM); // 温度传感器告警

    buff[offset++] = 0x01;                    //湿度传感器数量为1

    buff[offset++] = judge_env_temperature_sensor_alarm_status(DCMU_ALM_ID_ENV_HUMIDITY_LOW_ALARM, DCMU_ALM_ID_ENV_HUMIDITY_HIGH_ALARM, DCMU_ALM_ID_ENV_HUMIDITY_SENSOR_INVALID_ALARM); // 湿度传感器告警
    
    buff[offset++] = 0x01;                    //烟雾传感器数量为1
    alm_id = GET_ALM_ID(DCMU_ALM_ID_FUMES_SENSOR_ALARM,1,1);//烟雾传感器告警
    buff[offset++] = (get_realtime_alarm_value(alm_id) == ALARM_EXSIT) ? 0x04 : 0x00;

    buff[offset++] = 0x01;                    //水浸传感器数量为1
    alm_id = GET_ALM_ID(DCMU_ALM_ID_FLOOD_SENSOR_ALARM,1,1);//水浸传感器告警
    buff[offset++] = (get_realtime_alarm_value(alm_id) == ALARM_EXSIT) ? 0x04 : 0x00;

    buff[offset++] = 0x01;                    //门磁传感器数量为1
    alm_id = GET_ALM_ID(DCMU_ALM_ID_DOORMAT_SENSOR_ALARM,1,1);//门磁传感器告警
    buff[offset++] = (get_realtime_alarm_value(alm_id) == ALARM_EXSIT) ? 0x04 : 0x00;

    buff[offset++] = 0x01;                    //空调防盗传感器数量为1
    buff[offset++] = 0x00;                    //空调防盗传感器告警(未使用)

    buff[offset++] = 0x01;                    //电池防盗传感器数量为1
    buff[offset++] = 0x00;                    //电池防盗传感器告警(未使用)

    buff[offset++] = 0x01;                    //接地排防盗传感器数量为1
    buff[offset++] = 0x00;                    //接地排防盗传感器告警(未使用)

    buff[offset++] = 0x01;                    //告警器防盗传感器数量为1
    buff[offset++] = 0x00;                    //告警器防盗传感器告警(未使用)

    buff[offset++] = 0x00;                    //用户自定义告警数量为0

    /* LENID */
    cmd_buf_temp->data_len = offset;
    
    return SUCCESSFUL;
}

Static unsigned char judge_env_temperature_sensor_alarm_status(int alm_code_low, int alm_code_high, int alm_code_invalid)
{
    unsigned char ret = 0;
    char alarm_status = 0;
    int alm_id = 0;

    alm_id = GET_ALM_ID(alm_code_low, 1, 1);
    alarm_status = get_realtime_alarm_value(alm_id);
    if(TRUE == alarm_status){
        ret = 1;    // 电池温度低告警
    }

    alm_id = GET_ALM_ID(alm_code_high, 1, 1);
    alarm_status = get_realtime_alarm_value(alm_id);
    if(TRUE == alarm_status){
        ret = 2;    // 电池温度高告警
    }
    alm_id = GET_ALM_ID(alm_code_invalid, 1, 1);
    alarm_status = get_realtime_alarm_value(alm_id);
    if(TRUE == alarm_status){
        ret = 4;    // 电池温度无效告警
    }

    return ret;
}

Static int dcmu_pack_analog(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    unsigned char* buff;
    unsigned int offset = 0;
    float f_data = 0.0f;
    unsigned int u_data = 0;
    int i = 0;

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    RETURN_VAL_IF_FAIL(cmd_buf_temp->buf != NULL, FAILURE);
    buff = cmd_buf_temp->buf;
    
    get_one_data(DCMU_DATA_ID_DATA_FLAG, &buff[offset++]);
    
    buff[offset++] = 0x01;                               // 直流屏数量

    get_one_data(DCMU_DATA_ID_DC_OUTPUT_VOLTAGE, &f_data); // 直流输出电压
    put_int16_to_buff(&buff[offset], round(f_data*pow(10,1)));
    offset += 2;

    get_one_data(DCMU_DATA_ID_TOTAL_LOAD_CURRENT, &f_data); // 负载总电流
    put_int16_to_buff(&buff[offset], round(f_data*pow(10,1)));
    offset += 2;

    buff[offset++] = 0x04;            // 监测电池电流数量 M=4
    for (i = 0; i < 4; i++)
    {
        get_one_data(DCMU_DATA_ID_BATTERY_CURRENT + i, &f_data); // 电池电流
        put_int16_to_buff(&buff[offset], round(f_data*pow(10,1)));
        offset += 2; 
    }

    buff[offset++] = LOAD_NUM;      // 直流分路数量 N=24
    for (i = 0; i < LOAD_NUM; i++)
    {
        get_one_data(DCMU_DATA_ID_LOAD_CURRENT + i, &f_data); // 分路(负载)电流
        put_int16_to_buff(&buff[offset], round(f_data * pow(10,1)));
        offset += 2;
    }

    buff[offset++] = 0x24;           // 用户自定义遥测数量36

    buff[offset++] = 0x04;           // 监测电池分路电压数量 M＝4
    for (i = 0; i < 4; i++)
    {
        get_one_data(DCMU_DATA_ID_BATTERY_VOLTAGE + i, &f_data); // 电池电压
        put_int16_to_buff(&buff[offset], round(f_data * pow(10,1)));
        offset += 2; 
    }

    buff[offset++] = 0x04;           // 监测电池分路温度数量M＝4
    for (i = 0; i < 4; i++)
    {
        get_one_data(DCMU_DATA_ID_BATTERY_TEMPERATURE + i, &f_data); // 电池温度
        put_int16_to_buff(&buff[offset], round(f_data));
        offset += 2; 
    }

    get_one_data(DCMU_DATA_ID_TOTAL_ELECTRIC_QUANTITY, &f_data); // 总电量
    put_int32_to_buff(&buff[offset], round(f_data * pow(10,2)));
    offset += 4;

    buff[offset++] = LOAD_NUM;      // 直流分路电量数量 N=24
    for (i = 0; i < LOAD_NUM; i++)
    {
        get_one_data(DCMU_DATA_ID_LOAD_BATTERY + i, &f_data); // 分路(负载)电量
        put_int32_to_buff(&buff[offset], round(f_data * pow(10,2)));
        offset += 4;
    }

    /* LENID */
    cmd_buf_temp->data_len = offset;
    
    return SUCCESSFUL;
}

Static int dcmu_pack_sw_status(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    unsigned char* buff;
    unsigned int offset = 0;
    unsigned char uc_data = 0;
    int i = 0;

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    RETURN_VAL_IF_FAIL(cmd_buf_temp->buf != NULL, FAILURE);
    buff = cmd_buf_temp->buf;

     /* DATA_FLAG */
    get_one_data(DCMU_DATA_ID_DATA_FLAG, &buff[offset++]);

    /* DATAF */
    buff[offset++] = 0x01;            // 直流屏数量 m=1

    buff[offset++] = RELAY_NUM;       // 输入干接点数量 M=4

    for (i = 0; i < RELAY_NUM; i++)
    {
        get_one_data(DCMU_DATA_ID_INPUT_RELAY + i, &uc_data); // 输入干接点
        buff[offset++] = uc_data;
    }

    /* LENID */
    cmd_buf_temp->data_len = offset;
    
    return SUCCESSFUL;
}

Static int His_Alm_To_Buff(unsigned short index, cmd_buf_t *cmd_buf)
{
    RETURN_VAL_IF_FAIL(cmd_buf != NULL && cmd_buf->buf != NULL, FAILURE);

    unsigned char* buff = cmd_buf->buf;
    unsigned int offset = 3; //0:data_type,1:直流屏数量,2:历史告警条数
    unsigned short total_alarms = 0;
    alarm_map_t *alarm_map;
    his_alarm_info_t tHisAlm = {0};
    unsigned short alm_code = 0;
    time_base_t start_time = {0};
    time_base_t end_time = {0};
    int ret = 0;
    unsigned char uc_ID1 = 0;
    unsigned char uc_ID2 = 0;
    unsigned char uc_index = 0;

    pub_get_saved_record_num_msg(1, &total_alarms);

    if (index >= total_alarms)
    {
        s_start_index = 0;
        cmd_buf->rtn = RTN_NODATA;
        cmd_buf->data_len = 0;
        return FALSE;
    }

    // 读取一条历史告警
    if (pub_hisrecord_read_msg(1, 1, index, (unsigned char *)&tHisAlm) != 0)
    {
        cmd_buf->rtn = RTN_NODATA;
        cmd_buf->data_len = 0;
        return FALSE;
    }

    // 解析告警码
    alm_code = tHisAlm.alarm_id;
    time_t_to_timestruct(tHisAlm.start_time, &start_time);
    time_t_to_timestruct(tHisAlm.end_time, &end_time);
    ret = get_alarm_sn(alm_code, &alarm_map);
    // 检查get_alarm_sn是否成功
    if (ret == -1)
    {
        cmd_buf->rtn = RTN_NODATA;
        cmd_buf->data_len = 0;
        return FALSE;
    }
    uc_ID1 = alarm_map->id1;
    uc_ID2 = alarm_map->id2;
    uc_index = tHisAlm.index;
    if (alm_code >= DCMU_ALM_ID_INPUT_RELAY1_ALARM && alm_code <= DCMU_ALM_ID_INPUT_RELAY4_ALARM)
    {
        uc_index = (alm_code - DCMU_ALM_ID_INPUT_RELAY1_ALARM) / 0x100 + 1;
    }
    
    // 打包到buf
    buff[offset++] = uc_ID1;      // 告警ID2
    buff[offset++] = uc_ID2;      // 告警ID2
    buff[offset++] = (uc_index > 0) ? (uc_index - 1) : uc_index; // 告警Index
    put_time_to_buff(&buff[offset], start_time);  // 告警产生时间
    offset += 7;
    put_time_to_buff(&buff[offset], end_time);    // 告警结束时间
    offset += 7;

    // 判断是否为最后一条
    if (index == total_alarms - 1)
    {
        buff[0] = GROUP_TYPE_SEND_LAST;
    }
    else
    {
        buff[0] = GROUP_TYPE_SEND_NORMAL;
    }

    cmd_buf->data_len = offset;
    cmd_buf->rtn = 0;
    return TRUE; // 成功读取一条
}

Static int dcmu_parse_his_alarm(void* dev_inst, void *cmd_buf)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf != NULL, FAILURE);
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    RETURN_VAL_IF_FAIL(cmd_buf_temp->buf != NULL, FAILURE);

    unsigned char* buff = cmd_buf_temp->buf;
    s_command_types = (cmd_buf_temp->data_len == 1) ? buff[0] : WRONG_DATA_LENGTH;
    return SUCCESSFUL;
}

Static int dcmu_pack_his_alarm(void* dev_inst, void *cmd_buf)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf != NULL, FAILURE);

    int ret = 0;
    unsigned char offset = 1; //0表示data_type，在最后赋值
    unsigned short total_alarms = 0;

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    RETURN_VAL_IF_FAIL(cmd_buf_temp->buf != NULL, FAILURE);
    unsigned char* buff = cmd_buf_temp->buf;
    
    pub_get_saved_record_num_msg(1, &total_alarms);

    switch (s_command_types)
    {
        case GROUP_TYPE_RCV_START:
            s_start_index = 0;    
            break;
        case GROUP_TYPE_RCV_NEXT:
            s_start_index += 1;   
            break;
        case GROUP_TYPE_RCV_ERROR:   
            break;
        case GROUP_TYPE_RCV_END:
            cmd_buf_temp->data_len = offset;
            return SUCCESSFUL;
        default:
            cmd_buf_temp->rtn = RTN_INVLDATA;
            cmd_buf_temp->data_len = offset;
            return SUCCESSFUL;
    }
    
    buff[offset++] = 1;       // 直流屏数量
    buff[offset++] = ( (total_alarms - s_start_index - 1) >= 10) ? 10 : (total_alarms - s_start_index - 1); //历史告警条数

    // 读取一条历史告警并打包
    ret = His_Alm_To_Buff(s_start_index, cmd_buf_temp);
    return SUCCESSFUL;
}

Static int dcmu_pack_env_analog(void *dev_inst, void *cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff;
    unsigned char offset = 0;
    float f_data = 0.0f;
    char s8_data = 0;

    buff = cmd_buf_temp->buf;

    /* DATA_FLAG */
    get_one_data(DCMU_DATA_ID_DATA_FLAG, &buff[offset++]);

    /* DATAF */
    buff[offset++] = 0x01;            // 温度传感器数量m=1
    get_one_data(DCMU_DATA_ID_ENV_TEMP, &s8_data);  // 环境温度
    put_int16_to_buff(&buff[offset], s8_data);
    offset += 2;

    buff[offset++] = 0x01;            // 湿度传感器数量n=1
    get_one_data(DCMU_DATA_ID_ENV_HUMIDITY, &s8_data);  // 环境湿度
    put_int16_to_buff(&buff[offset], s8_data);
    offset += 2;

    buff[offset++] = 0x01;            // 自定义遥测数量为1

    get_one_data(DCMU_DATA_ID_BOARD_RESERVED_TEMPERATURE, &f_data);  // 环境温度
    put_int16_to_buff(&buff[offset], FLOAT_TO_SHORT(f_data));
    offset += 2;

    /* LENID */
    cmd_buf_temp->data_len = offset;

    return SUCCESSFUL;
}

Static int dcmu_pack_factory_info_custom(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff;
    unsigned char offset = 0;
    unsigned char sm_name[SM_NAME_LEN];
    unsigned char sm_soft_ver[SM_SOFT_VER_LEN];
    unsigned char factory_name[SM_FACTORY_NAME_LEN];
    unsigned char release_date[SM_SOFT_RELEASE_DATE_LEN];
    unsigned char hardware_version[SM_HARDWARE_VERSION_LEN] = {0};
    unsigned char serial_number[SM_SERIAL_NUMBER_LEN] = {0};

    buff = cmd_buf_temp->buf;

    /* 获取SM名称 */
    rt_memset_s(sm_name, SM_NAME_LEN, 0x20, SM_NAME_LEN);
    rt_memcpy_s(sm_name, SM_NAME_LEN, SOFTWARE_NAME, rt_strnlen_s(SOFTWARE_NAME, SM_NAME_LEN));

    /* 获取SM软件版本 */
    rt_memset_s(sm_soft_ver, SM_SOFT_VER_LEN, 0x20, SM_SOFT_VER_LEN);
    rt_memcpy_s(sm_soft_ver, SM_SOFT_VER_LEN, SOFTWARE_VER, rt_strnlen_s(SOFTWARE_VER, SM_SOFT_VER_LEN));

    /* 获取厂家名称 */
    rt_memset_s(factory_name, SM_FACTORY_NAME_LEN, 0x20, SM_FACTORY_NAME_LEN);
    rt_memcpy_s(factory_name, SM_FACTORY_NAME_LEN, CORPERATION_NAME, rt_strnlen_s(CORPERATION_NAME, SM_FACTORY_NAME_LEN));

    /* 获取软件发布日期 */
    release_date[0] = (unsigned char)(SOFTWARE_DATE_YEAR >> 8);   // 年份高字节
    release_date[1] = (unsigned char)(SOFTWARE_DATE_YEAR & 0xFF); // 年份低字节
    release_date[2] = SOFTWARE_DATE_MONTH;                        // 月份
    release_date[3] = SOFTWARE_DATE_DAY;                          // 日期

    get_one_para(DCMU_PARA_ID_HARDWARE_VERSION_OFFSET, hardware_version);
    get_one_para(DCMU_PARA_ID_SERIAL_NUMBER_OFFSET, serial_number);


    rt_memcpy_s(&buff[offset], SM_NAME_LEN, sm_name, SM_NAME_LEN);
    offset += SM_NAME_LEN;

    rt_memcpy_s(&buff[offset], SM_SOFT_VER_LEN, sm_soft_ver, SM_SOFT_VER_LEN);
    offset += SM_SOFT_VER_LEN;

    rt_memcpy_s(&buff[offset], SM_FACTORY_NAME_LEN, factory_name, SM_FACTORY_NAME_LEN);
    offset += SM_FACTORY_NAME_LEN;

    rt_memcpy_s(&buff[offset], SM_SOFT_RELEASE_DATE_LEN, release_date, SM_SOFT_RELEASE_DATE_LEN);
    offset += SM_SOFT_RELEASE_DATE_LEN;

    rt_memcpy_s(&buff[offset], SM_HARDWARE_VERSION_LEN-1, hardware_version, SM_HARDWARE_VERSION_LEN-1);
    offset = offset + SM_HARDWARE_VERSION_LEN-1;

    rt_memcpy_s(&buff[offset], SM_SERIAL_NUMBER_LEN-1, serial_number, SM_SERIAL_NUMBER_LEN-1);
    offset = offset + SM_SERIAL_NUMBER_LEN-1;

    cmd_buf_temp->data_len = offset;

    return SUCCESSFUL;
}

Static int dcmu_pack_boot_ver(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff;
    unsigned char offset = 0;
    buff = cmd_buf_temp->buf;
    boot_info_t boot_info = {0};
    rt_memset_s(&boot_info, sizeof(boot_info_t), 0x20, sizeof(boot_info_t));

    get_one_data(DCMU_DATA_ID_BOOT_VER, &boot_info.ver);  // boot版本号
    rt_memcpy_s(&buff[offset], BOOT_VER_LEN, boot_info.ver, BOOT_VER_LEN);
    offset += 16;
    get_one_data(DCMU_DATA_ID_BOOT_VER_DATE, &boot_info.date);  // boot版本日期
    rt_memcpy_s(&buff[offset], BOOT_VER_LEN, boot_info.date, BOOT_VER_LEN);
    offset += 16;

    /* LENID */
    cmd_buf_temp->data_len = offset;

    return SUCCESSFUL;
}