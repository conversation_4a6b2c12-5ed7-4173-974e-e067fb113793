#include "common.h"
#include "battery.h"
#include "CommCan.h"
#include "MultBattData.h"
#include "MasterOper.h"
#include "SlaveOper.h"
#include "usart.h"
#include "utils_rtthread_security_func.h"

Static T_SlavesRealData s_tSlavesRealData[SLAVE_NUM];
Static T_RotateInfoStruct s_tRotate;
Static BOOLEAN s_bHeaterOpen = FALSE;

void (*CtrlChargeRotateOper)(T_RotateInfoStruct *ptRotate);
void (*DealSlaveRotateTimeoutOper)(T_BattDealInfoStruct *ptBattDeal);
void (*DealChargeRotateOper)(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo);

void RotateMaster(void)
{
    CtrlChargeRotateOper = CtrlChargeRotate_Master;
    DealSlaveRotateTimeoutOper = DealSlaveRotateTimeout_Master;
    DealChargeRotateOper = DealChargeRotate_Master;
}

void RotateSlave(void)
{
    CtrlChargeRotateOper = CtrlChargeRotate_Slave;
    DealSlaveRotateTimeoutOper = DealSlaveRotateTimeout_Slave;
    DealChargeRotateOper = DealChargeRotate_Slave;
}

/*--------------------------for charge rotate----------------------------------*/
void InitRotate(BOOLEAN bStartCharge)
{
    rt_memset_s(&s_tRotate, sizeof(s_tRotate), 0, sizeof(s_tRotate));
    if (bStartCharge)
    {
        s_tRotate.bStartChargeRotate = True;
        rt_memset_s(s_tRotate.abCtrlSlaveRotateChargeClose, sizeof(s_tRotate.abCtrlSlaveRotateChargeClose), 1, sizeof(s_tRotate.abCtrlSlaveRotateChargeClose));
    }
    else
    {
        rt_memset_s(s_tRotate.aucCtrlSlaveCurrLimit, sizeof(s_tRotate.aucCtrlSlaveCurrLimit), SMART_LI_ROTATE_CURR_MAX, sizeof(s_tRotate.aucCtrlSlaveCurrLimit));
    }
}

/*
1. 先找到BMS中轮换电流最小的
2. 在最小轮换电流的BMS中，找到SOC最小的
*/
static void SearchNextBattIncreaseCurr(BYTE* pucIndex)
{
    BYTE i=0;
    WORD wHighSOC = 100*Pow10(2);
    T_SlavesRealData *p = NULL;

    if ( NULL == pucIndex)
    {
        return;
    }
    getSlavesRealData(&s_tSlavesRealData[0]);
    s_tRotate.ucMinRotateCurr = SMART_LI_ROTATE_CURR_MAX;

    ////search for the minimum rotate current limit.
    for(i = 0; i < SLAVE_NUM; i++)
    {
        p = &s_tSlavesRealData[i];
        if (p->bExist 
            && !s_tRotate.abCtrlSlaveRotateChargeClose[i]
            && s_tRotate.aucCtrlSlaveCurrLimit[i] < s_tRotate.ucMinRotateCurr)
        {
            s_tRotate.ucMinRotateCurr = s_tRotate.aucCtrlSlaveCurrLimit[i];
        }
    }

    ////search for the minimum soc bms within the minimum rotate current limit.
    for(i = 0; i < SLAVE_NUM; i++)
    {
        p = &s_tSlavesRealData[i];
        if (p->bExist 
            && !s_tRotate.abCtrlSlaveRotateChargeClose[i]
            && s_tRotate.aucCtrlSlaveCurrLimit[i] == s_tRotate.ucMinRotateCurr
            && p->wBatHighSOC < wHighSOC)
        {
            wHighSOC = p->wBatHighSOC;
            *pucIndex = i;
        }
    }

    return;
}

void CheckRotateCurrent(void)
{
    #ifdef UNITEST
    LoadTestRotateInfo(&s_tRotate);
    #endif
    BYTE i = 0, ucMinIndex = SLAVE_NUM+1;    
    T_SlavesRealData *p = NULL;

    getSlavesRealData(&s_tSlavesRealData[0]);
    for(i = 0; i < SLAVE_NUM; i++)
    {
        p = &s_tSlavesRealData[i];
        if (!p->bExist )
        {
            continue;
        }

        if (s_tRotate.abCtrlSlaveRotateChargeClose[i])
        {
            s_tRotate.ucRotateCurrLimitCounter = 0;
            return;
        }
        
        if(!p->bUpgrade
            && !p->bLoopOff
            && !p->bStatFault
            && !p->bChgPrt 
            && p->bChargeStat
            )      
        {
            if(!p->bChgCurrSta)
            {
                s_tRotate.ucRotateCurrLimitCounter = 0;
                return;
            }
        }
    }

    TimerPlus(s_tRotate.ucRotateCurrLimitCounter, ROTATE_CURR_LIMIT_COUNTER_MAX);

    if(!TimeOut(s_tRotate.ucRotateCurrLimitCounter, ROTATE_CURR_LIMIT_COUNTER_MAX))
    {
        return;
    }

    SearchNextBattIncreaseCurr(&ucMinIndex);

    if(ucMinIndex < SLAVE_NUM)
    {
        s_tRotate.aucCtrlSlaveCurrLimit[ucMinIndex] += ROTATE_STEP_CURRENT;
        s_tRotate.aucCtrlSlaveCurrLimit[ucMinIndex] = MIN(s_tRotate.aucCtrlSlaveCurrLimit[ucMinIndex], SMART_LI_ROTATE_CURR_MAX);
        s_tRotate.ucRotateCurrLimitCounter = 0;
    } 
}

static void GetRotateSOCAndIndex(BOOLEAN bMax, BOOLEAN bClosed, WORD *pwSoc, BYTE* pucIndex)
{
    BYTE i=0;
    WORD wSOC = 100 * Pow10(2);
    T_SlavesRealData *p = NULL;

    if (NULL == pwSoc || NULL == pucIndex)
    {
        return;
    }

    getSlavesRealData(&s_tSlavesRealData[0]);
    if(bMax)
    {
        wSOC = 0;
    }
    
    for(i = 0; i < SLAVE_NUM; i++)
    {
        p = &s_tSlavesRealData[i];
        if (p->bExist)
        {
            if ((bClosed && !s_tRotate.abCtrlSlaveRotateChargeClose[i])||(!bClosed && s_tRotate.abCtrlSlaveRotateChargeClose[i]))
            {
                continue;
            }
                
            if (!bMax && p->wBatHighSOC < wSOC)
            {
                wSOC = p->wBatHighSOC;
                *pucIndex = i;
            }
            else if (bMax  && p->wBatHighSOC > wSOC)
            {
                wSOC = p->wBatHighSOC;
                *pucIndex = i;
            }
        }        
    }

    *pwSoc = wSOC;
}

static BOOLEAN FaultCounter(void)
{
    BYTE i=0;
    T_SlavesRealData *p = NULL;

    getSlavesRealData(&s_tSlavesRealData[0]);
    for(i = 0; i < SLAVE_NUM; i++)
    {
        p = &s_tSlavesRealData[i];
        if (!p->bExist)
        {
            s_tRotate.aucFaultCounter[i] = 0;
            continue;
        }
    
         ////回路断电池要有充电机会
        if(p->bLoopOff || p->bStatFault)
        {
            TimerPlus(s_tRotate.aucFaultCounter[i], ROTATE_OPEN_FAULT_COUNTER_MAX);
        }
        else
        {
            if (TimeOut(s_tRotate.aucFaultCounter[i], ROTATE_OPEN_FAULT_COUNTER_MAX))
            {////如果异常电池恢复了正常，减小轮换限流点
                s_tRotate.aucCtrlSlaveCurrLimit[i] = ROTATE_STEP_CURRENT;
            }
            s_tRotate.aucFaultCounter[i] = 0;
        }

        if (TimeOut(s_tRotate.aucFaultCounter[i], ROTATE_OPEN_FAULT_COUNTER_MAX))
        {
            s_tRotate.abCtrlSlaveRotateChargeClose[i] = False;
            s_tRotate.aucCtrlSlaveCurrLimit[i] = SMART_LI_ROTATE_CURR_MAX;
        }     
    }

    return TRUE;
}

void CheckRotateOpen(void)
{
    BYTE i=0, ucMinIndex = SLAVE_NUM+1;
    WORD wMinSoc = 0;
    BYTE abInitCloseStat[SLAVE_NUM] = {1};
    T_SlavesRealData *p = NULL;

    FaultCounter();

    rt_memset_s(abInitCloseStat, sizeof(abInitCloseStat), 1, sizeof(abInitCloseStat));
    
    getSlavesRealData(&s_tSlavesRealData[0]);
    if(rt_memcmp(abInitCloseStat, s_tRotate.abCtrlSlaveRotateChargeClose, sizeof(abInitCloseStat)))
    {
        s_bHeaterOpen = FALSE;
        for(i = 0; i < SLAVE_NUM; i++)
        {
            p = &s_tSlavesRealData[i];
            if(getMaxBusCurrInAll() > MIN_CURR_DET_BDCU
            && p->bExist && !p->bUpgrade
            && p->bChargeStat 
            && !p->bStatFault 
            && !s_tRotate.abCtrlSlaveRotateChargeClose[i] 
            && !p->bLoopOff 
            && (!p->bChgPrt || p->bRotateChgHeat)
            && !TimeOut(s_tRotate.aucFaultCounter[i], ROTATE_OPEN_FAULT_COUNTER_MAX)
            && !p->bChgCurrSta)
            {
                if(p->bRotateChgHeat && p->fBusCur > MIN_CURR_DET_BDCU && p->fBatCur < MIN_CURR_DET_BDCU)
                {
                    s_bHeaterOpen = TRUE;
                }
                s_tRotate.ucRotateOpenCounter = 0;
                return;
            }
        }

        TimerPlus(s_tRotate.ucRotateOpenCounter, ROTATE_OPEN_COUNTER_MAX);

        if(TimeOut(s_tRotate.ucRotateOpenCounter, ROTATE_OPEN_COUNTER_MAX))
        {
            GetRotateSOCAndIndex(False, True, &wMinSoc, &ucMinIndex);
        }
    }
    else
    {////first time, open bms directly.
        GetRotateSOCAndIndex(False, True, &wMinSoc, &ucMinIndex);
    }    

    if(ucMinIndex < SLAVE_NUM)
    {
        s_tRotate.abCtrlSlaveRotateChargeClose[ucMinIndex] = False;
        s_tRotate.aucCtrlSlaveCurrLimit[ucMinIndex] = ROTATE_STEP_CURRENT;
        s_tRotate.ucRotateOpenCounter = 0;
#ifdef ROTATE_DEBUG        
        s_tRotateDebug.ucMinSocAddr = Sn2Addr(ucMinIndex);
        s_tRotateDebug.wMinSOC = wMinSoc;
#endif
    }

    return;
}

void CheckRotateClose(void)
{    
    WORD wMaxSocInOpen = 0, wMinSocInClose = 100;
     
    s_tRotate.ucMaxSocSnInOpen = SLAVE_NUM+1;
    s_tRotate.ucMinSocSnInClose = SLAVE_NUM+1;

    getSlavesRealData(&s_tSlavesRealData[0]);

    if (0 == s_tRotate.ucRotateOpenCounter && s_bHeaterOpen)
    {
        TimerPlus(s_tRotate.wRotateHeaterCounter, ROTATE_HEATER_OPEN_COUNTER_MAX);

        if(!TimeOut(s_tRotate.wRotateHeaterCounter, ROTATE_HEATER_OPEN_COUNTER_MAX))
        {
            return;
        }
    }
    else
    {
        s_tRotate.wRotateHeaterCounter = 0;
    }

    if (0 == s_tRotate.ucRotateOpenCounter)
    {
        TimerPlus(s_tRotate.wRotateCloseCounter, ROTATE_CLOSE_COUNTER_MAX);
    }
    else
    {
        s_tRotate.wRotateCloseCounter = 0;
    }

    ////无法连续开BMS的情况下，持续80s开始计算是否能关BMS
    if(!TimeOut(s_tRotate.wRotateCloseCounter, ROTATE_CLOSE_COUNTER_MAX))
    {
        return;
    }

    /*--------search the maxinum soc in the open bms-----------*/
    GetRotateSOCAndIndex(True, False, &wMaxSocInOpen, &s_tRotate.ucMaxSocSnInOpen);

#ifdef ROTATE_DEBUG
    s_tRotateDebug.ucMaxSocAddr = s_tRotate.ucMaxSocSnInOpen;
//    s_tRotateDebug.wMaxSOC = wMaxSocInOpen;
#endif

    /*--------search the mininum soc in the closed bms-----------*/
    GetRotateSOCAndIndex(False, True, &wMinSocInClose, &s_tRotate.ucMinSocSnInClose);    

    if( s_tRotate.ucMinSocSnInClose >= SLAVE_NUM || s_tRotate.ucMaxSocSnInOpen >= SLAVE_NUM)
    {
        return;
    }

    if( !s_tSlavesRealData[s_tRotate.ucMinSocSnInClose].bExist 
        || !s_tSlavesRealData[s_tRotate.ucMaxSocSnInOpen].bExist)
    {
        return;
    }

    if (s_tSlavesRealData[s_tRotate.ucMaxSocSnInOpen].wBatHighSOC >
        (s_tSlavesRealData[s_tRotate.ucMinSocSnInClose].wBatHighSOC + 700))
    {
        s_tRotate.abCtrlSlaveRotateChargeClose[s_tRotate.ucMaxSocSnInOpen] = True;
        s_tRotate.aucCtrlSlaveCurrLimit[s_tRotate.ucMaxSocSnInOpen] = 0;        
    }

    return;
}

void CtrlChargeRotate(void)
{
    if(CtrlChargeRotateOper != NULL)
    {
        CtrlChargeRotateOper(&s_tRotate);
    }
    return;
}

/*
如果从机超过30分钟轮换禁止且没有收到主机的轮换控制命令，则自行打开
*/
void DealSlaveRotateTimeout(T_BattDealInfoStruct *ptBattDeal)
{
    if(DealSlaveRotateTimeoutOper != NULL)
    {
        DealSlaveRotateTimeoutOper(ptBattDeal);
    }
    return;
}

void DealChargeRotate(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo)
{
    if(DealChargeRotateOper != NULL)
    {
        DealChargeRotateOper(ptBattDeal,ptBattInfo);
    }
    return;
}

static void ExcuteRotateCtrl(BYTE sn, BOOLEAN bChargeDisable, BYTE ucCurr)
{
    if(sn >= SLAVE_NUM)
    {
        return;
    }

    if (Sn2Addr(sn) == GetBMSAddr())
    {
        SetBattModeDisable(bChargeDisable);
        SetRotateMaxCurr(ucCurr);
    }
    else
    {
        SetRotateCtrlSlave(ucCurr, bChargeDisable, Sn2Addr(sn));
    }

    return;
}

void CtrlChargeRotate_Master(T_RotateInfoStruct *ptRotate)
{
    BYTE i=0;
    T_SlavesRealData *p = NULL;

    getSlavesRealData(&s_tSlavesRealData[0]);
    for (i=0; i<SLAVE_NUM; i++)
    {
        p = &s_tSlavesRealData[i];
        if (!p->bExist)
        {
            continue;
        }
       
        if( p->bRotateDisable != ptRotate->abCtrlSlaveRotateChargeClose[i]
            || p->ucRotateLimitCurr != ptRotate->aucCtrlSlaveCurrLimit[i]
            )
                
        {
            ExcuteRotateCtrl(i, ptRotate->abCtrlSlaveRotateChargeClose[i], ptRotate->aucCtrlSlaveCurrLimit[i]);
        }

    }
    return;
}

void DealSlaveRotateTimeout_Master(T_BattDealInfoStruct *ptBattDeal)
{
    return;
}

void DealChargeRotate_Master(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo)
{
    if (GetQtptestFlag())
    {
        InitRotate(False);
        return;
    }

    ////受控模式不需要轮换，CSU针对光伏，都是非受控模式
    if (ptBattInfo->tPara.ucRunMode == RUN_MODE_CONTROLLED && !JudgeCommDisconnect())
    {
        InitRotate(False);
        return;
    }

    if(BATT_MODE_STANDBY != ptBattDeal->ucChargeMode
       && BATT_MODE_CHARGE != ptBattDeal->ucChargeMode)
    {
        InitRotate(False);
        return;
    }

    if(ptBattInfo->tPara.bChargeRotate)
    {
        if (!s_tRotate.bStartChargeRotate)
        {
            InitRotate(True);
        }
        CheckRotateClose();
        CheckRotateOpen();
        CheckRotateCurrent();
    }
    else
    {
        InitRotate(False);
    }  
    return;
}

void CheckRemoteSuplyOutput(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo)
{
    if (ptBattInfo->tPara.ucUsageScen == DISCHG_MODE_CONSTANT_VOLTAGE)
    {

        if (((!GetMasterDischgProtect() && ptBattInfo->tData.wBattSOC < ptBattInfo->tPara.bRemoSupplyDischgSwitchSOC)
            || (GetMasterDischgProtect() && (ptBattInfo->tData.wSlaveAvgSOC < ptBattInfo->tPara.bRemoSupplyDischgSwitchSOC)))
            && ptBattDeal->ucChargeMode == BATT_MODE_DISCHARGE)
        {
            // ptBattDeal->fDischargeHopeVol = ptBattDeal->fBatteryAverageVol;
            if (ptBattInfo->tPara.bRemoSupplyDischgSwitchSOC == ptBattInfo->tPara.bRemoSupplyDischgSwitchSOC2)
            {
                if(TimeOut(ptBattDeal->wDischargeEndTimer,THREE_MINUTES_SECOND))
                {
                    ptBattDeal->fDischargeHopeVol = ptBattInfo->tPara.fDischargeEndVolt2;
                }
                else
                {
                    ptBattDeal->fDischargeHopeVol = ptBattInfo->tPara.fDischargeEndVolt1;
                    TimerPlus(ptBattDeal->wDischargeEndTimer,THREE_MINUTES_SECOND);
                }
            }
            else
            {
                if ((!GetMasterDischgProtect() && ptBattInfo->tData.wBattSOC < ptBattInfo->tPara.bRemoSupplyDischgSwitchSOC2)
                    || (GetMasterDischgProtect() && (ptBattInfo->tData.wSlaveAvgSOC < ptBattInfo->tPara.bRemoSupplyDischgSwitchSOC2)))
                {
                    ptBattDeal->fDischargeHopeVol = ptBattInfo->tPara.fDischargeEndVolt2;
                }
                else
                {
                    ptBattDeal->fDischargeHopeVol = ptBattInfo->tPara.fDischargeEndVolt1;
                }
            }
        }
        else
        {
            ptBattDeal->wDischargeEndTimer = 0;
            ptBattDeal->fDischargeHopeVol = MAX(ptBattInfo->tPara.fRemoteSuplyVolt, OUT_VOLT_MIN);
        }
    }

    if(ptBattDeal->bFmDischg)
    {
        ptBattDeal->fDischargeHopeVol = ptBattDeal->fFmDischgVolt;
    }
    else if(ptBattDeal->bPeakDischg)
    {
        ptBattDeal->fDischargeHopeVol = ptBattDeal->fPeakDischgVolt;
    }

    return;
}

//检查是否进入远供末期
void CheckIfEnterRemoteEnd(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo)
{
    if (ptBattInfo->tPara.ucUsageScen == DISCHG_MODE_CONSTANT_VOLTAGE)
    {
        if (ptBattInfo->tData.wBattSOC < ptBattInfo->tPara.bRemoSupplyDischgSwitchSOC)
        {
            ptBattDeal->fDischargeHopeVol = ptBattDeal->fBatteryAverageVol;
            ptBattDeal->fDischargeSetVol = ptBattDeal->fDischargeHopeVol;
        }
    }
    return;
}

void CtrlChargeRotate_Slave(T_RotateInfoStruct *ptRotate)
{
    return;
}

void DealSlaveRotateTimeout_Slave(T_BattDealInfoStruct *ptBattDeal)
{
    if (ptBattDeal->bBattRotateDisable && BATT_MODE_CHARGE == ptBattDeal->ucChargeMode)
    {
        TimerPlus(ptBattDeal->ucSlaveDisableCounter, SLAVE_DISABLE_ROTATE_TIME_MAX);
    }
    
    if (TimeOut(ptBattDeal->ucSlaveDisableCounter, SLAVE_DISABLE_ROTATE_TIME_MAX))
    {
        SetRotate(SMART_LI_ROTATE_CURR_MAX, False);
    }
    return;
}

void DealChargeRotate_Slave(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo)
{
    return;
}

void setRotateDebugInfo(T_RotateDebugStruct *ptRotateDebug, T_BattDealInfoStruct *ptBattDeal)
{
    BYTE i;
    T_SlavesRealData *p = NULL;

    getSlavesRealData(&s_tSlavesRealData[0]);
    ptRotateDebug->ucInitStat = s_tRotate.ucRotateOpenCounter;
    ptRotateDebug->wMaxSOC = s_tRotate.wRotateCloseCounter|(s_tRotate.ucRotateCurrLimitCounter<<8);
    for (i=0; i<SLAVE_NUM; i++)
    {
        p = &s_tSlavesRealData[i];
        if (s_tRotate.abCtrlSlaveRotateChargeClose[i])
        {
            ptRotateDebug->ulDisableFlag |= _BIT(i);
        }
        else
        {
            ptRotateDebug->ulDisableFlag &= ~(_BIT(i));
        }

        if (p->bRotateDisable)
        {
            ptRotateDebug->ulDisableStat |= _BIT(i);
        }
        else
        {
            ptRotateDebug->ulDisableStat &= ~(_BIT(i));
        }

        if (p->bChgCurrSta)
        {
            ptRotateDebug->ulCurrStat |= _BIT(i);
        }
        else
        {
            ptRotateDebug->ulCurrStat &= ~(_BIT(i));
        }

        if (p->bChgPrt)
        {
            ptRotateDebug->ulCurrPrt |= _BIT(i);
        }
        else
        {
            ptRotateDebug->ulCurrPrt &= ~(_BIT(i));
        }

        ptRotateDebug->aucSlaveRotateCurr[i] = p->ucRotateLimitCurr;
        ptRotateDebug->aucCtrlRotateLimitCurr[i] = s_tRotate.aucCtrlSlaveCurrLimit[i];

    }
}

BOOLEAN ChargeRotateUnitest(BOOLEAN bFlag)
{

    if(bFlag == 1)
    {
        return True;
    }
    else
    {
        return False;
    }
}

void parseRotateInfo(T_RotateInfoStruct *ptRotate)
{
    rt_memcpy(ptRotate, &s_tRotate, sizeof(s_tRotate));
    return;
}


