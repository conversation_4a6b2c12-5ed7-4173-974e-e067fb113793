#include "utils_heart_beat.h"
#include "thread_id.h"
#include "interface.h"
#include "io_ctrl_api.h"
#include "msg_id.h"
#include "server_id.h"
#include "button.h"
#include "led_R552.h"
#include "utils_thread.h"

static unsigned short s_wCnt = 0;
static unsigned char s_ucSWCnt = 0;        // 休眠按键按下次数
static unsigned short s_wSWInterval = 0;
static unsigned char s_ucFireControlStatus = 0;
static unsigned char s_ucButtonMode = BUZZOFF;
static unsigned char s_ucButtonStatus = 0;
static unsigned char s_ucCurrBuzzMode;
static unsigned short s_wShowSpecialCnt = 0;
static _rt_server_t s_curr_server = NULL; // 用于获取当前服务
static T_ShowConfig tShowSpecial = {0};
static CTRL_State BuzzSta[] = {CTRL_OFF, CTRL_BUZZ_MODE1, CTRL_BUZZ_MODE2};


static void process_FC_msg(const rt_msg_t pMsg)
{
    // 检查输入参数的有效性
    if (pMsg == NULL || pMsg->data == NULL)
    {
        return;
    }

    // 获取火控消息指针
    relay_msg *pFCMsg = pMsg->data;

    // 检查引脚号是否匹配
    if (pFCMsg->pin_no != FC_PIN)
    {
        return;
    }

    // 更新火控状态
    s_ucFireControlStatus = pFCMsg->active;
}



static void onBuzz(unsigned char ucBuzzMode)
{
    // 仅在新的模式与当前模式不同的情况下进行更新
    if (ucBuzzMode != s_ucCurrBuzzMode)
    {
        s_ucCurrBuzzMode = ucBuzzMode;
        BSP_BUZZ_CTRL(BuzzSta[ucBuzzMode]);
    }
}



static void ctrlBuzz(void)
{
    unsigned char ucBuzzMode = GetBuzzMode();

    // 如果模式发生变化，直接调用 onBuzz 并返回
    if (ucBuzzMode != s_ucCurrBuzzMode)
    {
        onBuzz(ucBuzzMode);
        return;
    }

    // 仅在 BUZZMODE1 模式下进行计数控制
    if (ucBuzzMode == BUZZMODE1)
    {
        // 使用更简洁的条件判断
        if (s_wCnt % 10 == 0)
        {
            BSP_BUZZ_CTRL(BuzzSta[BUZZMODE1]);
        }
        else if (s_wCnt % 5 == 0)
        {
            BSP_BUZZ_CTRL(BuzzSta[BUZZOFF]);
        }
    }
}



static void SwitchButtonMode(void)
{
    switch (s_ucButtonMode)
    {
        case BUT_NORMAL:
            if (!GetDefenceStatus())
            {
                s_ucButtonMode = BUT_SLEEP;
            }
            break;

        case BUT_SLEEP:
            s_ucButtonMode = BUT_SHUTDOWN;  //TODO：保存操作记录、关机
            break;

        default:
            // 处理未知状态或保持当前状态
            break;
    }
}



static void ShowDefenceStatus(void)
{
    const unsigned char ucLedMode = GetDefenceStatus() ? LEDON : LEDQUICKSH;
    s_wShowSpecialCnt = (ucLedMode == LEDON) ? 100 : 200;

    for (unsigned char i = 0; i < LED_NUM; ++i)
    {
        tShowSpecial.aucLedMode[i] = ucLedMode;
    }

    SetShowSpecial(&tShowSpecial);
}



static void process_key_msg(const rt_msg_t pMsg)
{
    // 提前返回，减少嵌套层级
    if (pMsg == NULL || pMsg->data == NULL) {
        return;
    }

    relay_msg *pButtonMsg = pMsg->data;

    if (pButtonMsg->pin_no != SW_DET_Pin) {
        return;
    }

    unsigned char test_flag = GetApptestFlag() || GetQtptestFlag();

    if (pButtonMsg->active) {  // 按键按下
        if (s_wSWInterval > 0) {
            s_wSWInterval += s_wCnt;
        }
        s_ucButtonStatus = 1;
        if (test_flag) {
            onBuzz(BUZZMODE1);
        }
    } else {  // 按键恢复
        s_ucButtonStatus = 0;
        s_ucSWCnt++;
        s_wSWInterval += s_wCnt;
        if (test_flag) {
            onBuzz(BUZZOFF);
        } else {
            if (s_wCnt >= 30) {
                SwitchButtonMode();
            }

            if (s_wCnt > 15 || s_wSWInterval > 50) {  // 连续按键，单次不超过1.5秒，持续不超过5秒
                s_wSWInterval = 0;
                s_ucSWCnt = 0;
            } else if (s_ucSWCnt == 3 && s_ucButtonMode == BUT_NORMAL) {
                s_wSWInterval = 0;
                s_ucSWCnt = 0;
                ShowDefenceStatus();
            }
        }
    }
    s_wCnt = 0;
}


static int process_recv_msg(_rt_server_t server)
{
    _rt_msg_t curr_msg;
    rt_mutex_take(&server->mutex, RT_WAITING_FOREVER);

    while (server->msg_count > 0)
    {
        curr_msg = server->msg_node;
        if (curr_msg == NULL)
        {
            rt_mutex_release(&server->mutex);
            return -1;
        }

        switch (curr_msg->msg.msg_id)
        {
            case INPUT_BUTTON_MSG:
                process_key_msg(&curr_msg->msg);
                break;
            case INPUT_RELAY_MSG:
                process_FC_msg(&curr_msg->msg);
                break;
            default:
                // 可选：处理未知消息ID的情况
                break;
        }

        // 更新链表和计数器
        server->msg_node = curr_msg->next;
        server->msg_count--;
        softbus_free(curr_msg);
    }

    rt_mutex_release(&server->mutex);
    return 0;
}


static void processSustain(void)
{
    if (GetApptestFlag())
    {
        if ( s_wCnt == 100 )   //10s复位
        {
//            ResetMCU(NO_RESET_APPTEST);
        }
    }
    else
    {
        if ( s_wCnt == 30 ) 
        {
            onBuzz(BUZZMODE1);
            s_wShowSpecialCnt = 10;
        }
    }

    return;
}


static void InitButton(void)
{
    GPIO_Init();
    // 初始化按钮信息
    io_item_info_t buttonInfo = {
        .eIoType = INPUT_KEY,
        .initialVal = PIN_LOW,
        .pin_no = SW_DET_Pin,
        .msg_id = INPUT_BUTTON_MSG
    };

    // 初始化火控继电器信息
    io_item_info_t fireControlInfo = {
        .eIoType = INPUT_RELAY,
        .initialVal = PIN_LOW,
        .pin_no = FC_PIN,
        .msg_id = INPUT_RELAY_MSG
    };

    // 注册按钮信息并订阅其消息
    register_io_item_info(&buttonInfo);
    rt_server_subscribe(BUTTON_SERVER_ID, INPUT_BUTTON_MSG, RT_NULL);

    // 注册火控继电器信息并订阅其消息
    register_io_item_info(&fireControlInfo);
    rt_server_subscribe(BUTTON_SERVER_ID, INPUT_RELAY_MSG, RT_NULL);

    // 初始化LED输出
    InitLedOut();
}


void ButtonMgr(void* parameter)
{
    // 初始化按钮和线程心跳
    InitButton();
    pre_thread_beat_f(THREAD_BUTTON);
    s_curr_server = _curr_server_get();

    // 主循环
    while (loopCnt((int*)parameter)) 
    {
        // 维持线程心跳
        thread_beat_go_on(THREAD_BUTTON);

        // 处理接收到的消息
        process_recv_msg(s_curr_server);

        // 更新等待计数器，每600次循环（1分钟）重置一次
        if (s_wCnt < 600) {
            ++s_wCnt;
        }

        // 处理持续按下的情况
        if (s_ucButtonStatus) {
            processSustain();
        }

        // 处理特殊显示计数器
        if (s_wShowSpecialCnt > 0) {
            --s_wShowSpecialCnt;
            if (s_wShowSpecialCnt == 0) {
                SetShowSpecial(RT_NULL);  // 指示灯、蜂鸣器恢复
            }
        } 
        else if (!GetApptestFlag() && !GetQtptestFlag()) {
            ctrlBuzz();  // 控制蜂鸣器
        }

        // 延时100毫秒
        rt_thread_delay(100);
    }
}

unsigned char GetBtnMode(void)
{
    return s_ucButtonMode;
}

unsigned char GetSWSleepBtnCount(void)
{
    return s_ucSWCnt;
}

void ClearBtnCount(void)
{
    s_ucSWCnt  = 0;
}

//消防检测暂时放在这里，将来再移出
unsigned char GetFireControlStatus(void)
{
    return s_ucFireControlStatus;
}
