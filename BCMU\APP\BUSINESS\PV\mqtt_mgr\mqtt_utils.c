#include <sys/time.h>
#include "dev_dc_ac_parallel_modbus.h"
#include "mqtt_utils.h"
#include "mqtt_tab_in.h"
#include "realdata_save.h"
#include "para_manage.h"
#include "cmd.h"
#include "msg.h"
#include "msg_id.h"
#include "softbus.h"
#include "backup_manage.h"
#include "alarm_manage.h"
#include "system_manage.h"
#include "his_record.h"
#include "reset.h"
#include "sps.h"
#include "partition_def.h"
#include "utils_rtthread_security_func.h"
#include "para_common.h"
#include "para_common.h"
#include "north_data_utils.h"
#include "dev_dc_ac_1363.h"

static char g_uuid[UUID_LEN] = {0};
static char g_site_id[MAX_CLIENT][SITE_ID_LEN] = {0};
static ms_ver_t g_soft_ver[PV_NUM] = {0};
static unsigned short g_slave_msg_cnt = 0;
static mqtt_client_t* g_zte_client = NULL;
static mqtt_client_t* g_oth_client = NULL;

#define MQTT_REALDATA_NUM   sizeof(mqtt_realdata_tab) / sizeof(mqtt_data_t)
#define MQTT_ALARM_NUM      sizeof(mqtt_alarm_tab) / sizeof(mqtt_alarm_t)
#define MQTT_PARA_NUM       sizeof(mqtt_para_tab) / sizeof(mqtt_data_t)
#define MQTT_CONTROL_NUM    sizeof(mqtt_control_tab) / sizeof(mqtt_data_t)

static int control_iv_scan(int is_mst, int addr, int val);
static int power_on_off(int is_mst, int addr, int val);

static int get_unsigned_data_default(int is_mst, char addr, int sid, int cmd, cJSON* val);
static int get_signed_data_default(int is_mst, char addr, int sid, int cmd, cJSON* val);
static int get_data_float(int is_mst, char addr, int sid, int cmd, cJSON* val);
static int get_data_string(int is_mst, char addr, int sid, int cmd, cJSON* val);
static int get_data_date(int is_mst, char addr, int sid, int cmd, cJSON* val);
static int get_data_time(int is_mst, char addr, int sid, int cmd, cJSON* val);

static int set_data_default(int is_mst, int addr, int sid, int type, cJSON* val);
static int set_data_float(int is_mst, int addr, int sid, int type, cJSON* val);
static int set_data_string(int is_mst, int addr, int sid, int type, cJSON* val);
static int set_data_date(int is_mst, int addr, int sid, int type, cJSON* val);
static int set_data_time(int is_mst, int addr, int sid, int type, cJSON* val);

static control_sp_t mqtt_control_sp[] =
{
    {DAC_CTRL_ID_PV_CTRL_ORDER,      control_iv_scan},
    {DAC_CTRL_ID_CTRL_POWER_STATUS,  power_on_off},
    {DAC_CTRL_ID_SYSTEM_RESTART,     mqtt_restart_system},
    {DAC_CTRL_ID_GET_AFCI_FILE,      mqtt_get_afci_file},
    {DAC_CTRL_ID_CLEAN_HIS_DATA,     mqtt_clean_his_data},
    {DAC_CTRL_ID_CLEAN_ALARM,        mqtt_clean_alarm},
    {DAC_CTRL_ID_CLEAN_HIS_EVENT,    mqtt_clean_his_event},
    {DAC_CTRL_ID_DELETE_HIS_ENERGY,  mqtt_clean_his_energy},
    {DAC_CTRL_ID_CLEAN_IV_DATA,      mqtt_clean_iv_data},
    {DAC_CTRL_ID_FAULT_RECORD_DATA,  mqtt_clean_fault_record},
    {DAC_CTRL_ID_DUBUG_CONTROL_COMMAND,  trig_fault_record},
};
#define MQTT_CONTROL_SP_NUM     sizeof(mqtt_control_sp) / sizeof(control_sp_t)

static data_tab_t s_mqtt_data_process[] =
{
    {TYPE_INT8U,  get_unsigned_data_default, set_data_default},
    {TYPE_INT8S,  get_signed_data_default,   set_data_default},
    {TYPE_INT16U, get_unsigned_data_default, set_data_default},
    {TYPE_INT16S, get_signed_data_default,   set_data_default},
    {TYPE_INT32U, get_unsigned_data_default, set_data_default},
    {TYPE_INT32S, get_signed_data_default,   set_data_default},
    {TYPE_FLOAT,  get_data_float,   set_data_float},
    {TYPE_STRING, get_data_string,  set_data_string},
    {TYPE_DATE_T, get_data_date,    set_data_date},
    {TYPE_TIME_T, get_data_time,    set_data_time},
};
#define MQTT_DATA_PROCESS_NUM     sizeof(s_mqtt_data_process) / sizeof(data_tab_t)

static alarm_mqtt_t s_alarm_list[MAX_ALARM_COUNT] RAM_SECTION = {0};

int get_one_comm(int is_mst, char addr, int sid, int cmd, void* data)
{
    int err = FAILURE;
    if (cmd == CMD_PARA)
    {
        if (is_mst)
        {
            err = get_one_para(sid, data);
        }
        else
        {
            // 从机获取参数从实时数据中获取
            err = get_one_data(sid, data);
        }
    }
    else
    {
        if (is_mst)
        {
            err = get_one_data(sid, data);
        }
        else
        {
            err = get_one_slave_data(addr, sid, data);
        }
        
    }
    return err;
}

static int set_one_comm(int is_mst, int addr, int sid, void* data)
{
    char send_data[5] = {0};
    if (is_mst)
    {
        return set_one_para(sid, data, TRUE, TRUE);
    }

    // 发送信息设置从机参数数据
    send_data[0] = addr;
    rt_memcpy(&send_data[1], &sid, sizeof(sid));
    set_one_data(sid, data);
    g_slave_msg_cnt++;
    send_msg_to_thread(SET_SLAVE_ONE_PARA, MOD_INVERTER, send_data, sizeof(send_data));
    return SUCCESSFUL;
}

static int get_signed_data_default(int is_mst, char addr, int sid, int cmd, cJSON* val)
{
    int data = 0;
    int err = get_one_comm(is_mst, addr, sid, cmd, &data);

    cJSON_AddNumberToObject(val, "value", data);
    return err;
}

static int get_unsigned_data_default(int is_mst, char addr, int sid, int cmd, cJSON* val)
{
    unsigned int data = 0;
    int err = get_one_comm(is_mst, addr, sid, cmd, &data);

    cJSON_AddNumberToObject(val, "value", data);
    return err;
}

static int get_data_float(int is_mst, char addr, int sid, int cmd, cJSON* val)
{
    float data = 0;
    int err = get_one_comm(is_mst, addr, sid, cmd, &data);

    cJSON_AddNumberToObject(val, "value", data);
    return err;
}

static int get_data_string(int is_mst, char addr, int sid, int cmd, cJSON* val)
{
    str32 data = {0};
    int err = get_one_comm(is_mst, addr, sid, cmd, data);

    cJSON_AddStringToObject(val, "value", data);
    return err;
}

static int get_data_date(int is_mst, char addr, int sid, int cmd, cJSON* val)
{
    date_base_t data = {0};
    str16 str_data = {0};
    int err = get_one_comm(is_mst, addr, sid, cmd, &data);

    rt_snprintf(str_data, STR_LEN_16, "%d-%d-%d", data.year, data.month, data.day);
    cJSON_AddStringToObject(val, "value", str_data);
    return err;
}

static int get_data_time(int is_mst, char addr, int sid, int cmd, cJSON* val)
{
    time_base_t data = {0};
    str32 str_data = {0};
    int err = get_one_comm(is_mst, addr, sid, cmd, &data);

    rt_snprintf(str_data, STR_LEN_32, "%d-%d-%d %d:%d:%d",
                data.year, data.month, data.day, data.hour, data.minute, data.second);
    cJSON_AddStringToObject(val, "value", str_data);
    return err;
}

static int set_data_default(int is_mst, int addr, int sid, int type, cJSON* val)
{
    int val_i = 0;
    if (!cJSON_IsNumber(val))
    {
        return -MQTT_FORMAT_ERR;
    }
    val_i = cJSON_GetNumberValue(val);
    return set_one_comm(is_mst, addr, sid, &val_i);
}

static int set_data_float(int is_mst, int addr, int sid, int type, cJSON* val)
{
    float val_f = 0.0;

    if (!cJSON_IsNumber(val))
    {
        return -MQTT_FORMAT_ERR;
    }
    val_f = cJSON_GetNumberValue(val);
    return set_one_comm(is_mst, addr, sid, &val_f);
}

static int set_data_string(int is_mst, int addr, int sid, int type, cJSON* val)
{
    if (!cJSON_IsString(val))
    {
        return -MQTT_FORMAT_ERR;
    }
    return set_one_comm(is_mst, addr, sid, cJSON_GetStringValue(val));
}

static int set_data_date(int is_mst, int addr, int sid, int type, cJSON* val)
{
    date_base_t data = {0};

    if (!cJSON_IsString(val))
    {
        return -MQTT_FORMAT_ERR;
    }
    rt_sscanf_s(cJSON_GetStringValue(val), "%hu-%hhu-%hhu", &data.year, &data.month, &data.day);
    return set_one_comm(is_mst, addr, sid, &data);
}

static int set_data_time(int is_mst, int addr, int sid, int type, cJSON* val)
{
    time_base_t data = {0};

    if (!cJSON_IsString(val))
    {
        return -MQTT_FORMAT_ERR;
    }
    rt_sscanf_s(cJSON_GetStringValue(val), "%hu-%hhu-%hhu %hhu:%hhu:%hhu",
            &data.year, &data.month, &data.day, &data.hour, &data.minute, &data.second);
    return set_one_comm(is_mst, addr, sid, &data);
}

int  deal_other_pv_control_cmd(int is_mst, int addr, int val)
{
    unsigned char ctrl_cmd_id[] = {CMD_INVERTER_PARALLEL_MODBUS_CTRL_START_CHECK_ISOLATE, 
                                   CMD_INVERTER_PARALLEL_MODBUS_CTRL_START_CHECK_AFCI};
    if (val != CTRL_START_CHECK_ISOLATE && val != CTRL_START_CHECK_AFCI)
    {
        return MQTT_SCOPE_ERR;
    }
    if(is_mst)
    {
        set_one_data(DAC_CTRL_ID_PV_CTRL_ORDER, &val);
        send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, DC_AC_IV_CTRL_ORDER);
        save_real_ctrl_cmd_event(DAC_CTRL_ID_PV_CTRL_ORDER, CTRL_ID_TYPE, NULL);
    }
    else
    {
        char data[2] = {addr, ctrl_cmd_id[val - 3]};
        g_slave_msg_cnt++;
        send_msg_to_thread(MASTER_CTRL_SLAVE, MOD_INVERTER, data, sizeof(data));
    }
    return MQTT_SUCCESS;
}

static int control_iv_scan(int is_mst, int addr, int val)
{
    unsigned char iv_complete_status = FALSE;
    if (val != CTRL_START_IV && val != CTRL_CLOSE_IV)
    {
        return deal_other_pv_control_cmd(is_mst, addr,val);
    }
    if (is_mst)
    {
        set_one_data(DAC_CTRL_ID_PV_CTRL_ORDER, &val);
        send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, DC_AC_IV_CTRL_ORDER);
        save_real_ctrl_cmd_event(DAC_CTRL_ID_PV_CTRL_ORDER, CTRL_ID_TYPE, NULL);//保存操作记录
        if (val == CTRL_START_IV)
        {
            send_msg_to_thread(STOP_IV_SCAN_MSG, MOD_SYS_MANAGE, NULL, 0);
            storage_unlink(IV_DATA_FILE);
        }
        else
        {
            // 停止扫描后将iv扫描完成状态设置为已完成
            iv_complete_status = TRUE;
            send_msg_to_thread(STOP_IV_SCAN_MSG, MOD_SYS_MANAGE, NULL, 0);
        }
        set_one_data(DAC_DATA_ID_IV_COMPLETE_STATUS, &iv_complete_status);
        send_msg_to_thread(MQTT_REAL_UP_MSG, MOD_MQTT, NULL, 0);
    }
    else
    {
        char data[2] = {0};
        data[0] = addr;
        data[1] = CMD_INVERTER_PARALLEL_MODBUS_CTRL_IV_SCAN_START + val - 1;
        g_slave_msg_cnt++;
        send_msg_to_thread(MASTER_CTRL_SLAVE, MOD_INVERTER, data, sizeof(data));
    }

    return MQTT_SUCCESS;
}


int mqtt_restart_system(int is_mst, int addr, int val)
{
    if (val != CTRL_RESTART_SYSTEM)
    {
        return MQTT_SCOPE_ERR;
    }

    if(is_mst)
    {
        set_one_data(DAC_CTRL_ID_SYSTEM_RESTART, &val);
        if(get_restart_south_rtn() != SUCCESSFUL)
        {
            return MQTT_REQ_TIMEOUT_ERR;
        }
    }
    else
    {
        char data[2] = {addr, CMD_INVERTER_PARALLEL_MODBUS_CTRL_RESTART_SYSTEM};
        g_slave_msg_cnt++;
        send_msg_to_thread(MASTER_CTRL_SLAVE, MOD_INVERTER, data, sizeof(data));
    }

    return MQTT_SUCCESS;
}



int mqtt_get_afci_file(int is_mst, int addr, int val)
{
    // 检查输入值是否为预期的控制命令
    if (val != CTRL_GET_AFCI_FILE)
    {
        return MQTT_REQ_TIMEOUT_ERR;
    }

    // 发送消息给南向设备以获取AFCI日志文件
    unsigned short ctrl_status = CMD_GET_AFCI_LOG;
    set_one_data(DAC_CTRL_ID_PV_CTRL_ORDER, &ctrl_status);
    send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, DC_AC_IV_CTRL_ORDER);

    // 获取AFCI文件句柄，如果失败则返回错误码
    if (get_afci_file_handle() != SUCCESSFUL)
    {
        return MQTT_REQ_TIMEOUT_ERR;
    }

    // 如果所有步骤都成功，则返回成功码
    return MQTT_SUCCESS;
}



int set_power_off_on_para(int val)
{
    power_off_reason_t* power_off_reason = NULL;
    power_off_reason = get_power_off_reason();
    LOG_E("%s | %d power_on_off: %d emergy: %d  time:%d  normal:%d\n",__FUNCTION__ , __LINE__, power_on_off, power_off_reason->emergy_off, power_off_reason->time_off, power_off_reason->normal_off);
    if(val == 1)
    {
        // 开机处理，如果是非正常关机，则开机失败
        if((power_off_reason->emergy_off == TRUE) || (power_off_reason->time_off == TRUE))
        {   
            return FAILURE;
        }
        power_off_reason->normal_off = FALSE;
    }
    else
    {
        power_off_reason->normal_off = TRUE;
    }

    return SUCCESSFUL;
}


static int power_on_off(int is_mst, int addr, int val)
{
    int status = CTRL_POWER_ON;
    if (val == CTRL_POWER_ON_VAL)
    {
        status = CTRL_POWER_ON;
    }
    else if (val == CTRL_POWER_OFF_VAL)
    {
        status = CTRL_POWER_OFF;
    }
    else
    {
        return MQTT_SCOPE_ERR;
    }

    if (is_mst)
    {   
        if(set_power_off_on_para(val) != SUCCESSFUL)
        {
            return MQTT_SCOPE_ERR;
        }

        set_one_para(DAC_PARA_ID_POWER_ON_OFF_OFFSET, &status, TRUE, TRUE);
        send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, DC_AC_SET_REMOTE_CTRL_PARA_DATA);
    }
    else
    {
        set_one_comm(is_mst, addr, DAC_PO_PARA_ID_POWER_ON_OFF,&status);
    }
    return MQTT_SUCCESS;
}

static mqtt_err_e mqtt_is_data_valid(int is_mst, int* sid, mqtt_data_t* data_tab, int data_num)
{
    int idx = 0;
    if (sid == NULL)
    {
        return MQTT_SID_ERR;
    }

    for (idx = 0; idx < data_num; idx++)
    {
        if ((GET_DATA_ID_NO_NUM(*sid) == GET_DATA_ID_NO_NUM(data_tab[idx].sid)) &&
            (GET_DATA_NUM(*sid) <= data_tab[idx].num))
        {
            if (is_mst)
            {
                *sid = data_tab[idx].sid_offset + GET_DATA_NUM(*sid) - 1;
            }
            else    // 如果并机ID为0怎么办
            {
                *sid = data_tab[idx].sid_po + GET_DATA_NUM(*sid) - 1;
            }
            return MQTT_SUCCESS;
        }
    }

    return MQTT_SID_ERR;
}

static int mqtt_cmd_get_data(int is_mst, char addr, int sid, int type, int cmd, cJSON* val)
{
    int err = FAILURE;
    int idx = 0;

    for (idx = 0; idx < MQTT_DATA_PROCESS_NUM; idx++)
    {
        if (type == s_mqtt_data_process[idx].type)
        {
            err = s_mqtt_data_process[idx].get_data(is_mst, addr, sid, cmd, val);
            break;
        }
    }

    if (err != FAILURE)
    {
        return MQTT_SUCCESS;
    }

    return MQTT_SID_ERR;
}

static int mqtt_cmd_set_para(int is_mst, int addr, int sid, int type, cJSON* val)
{
    int err = FAILURE;
    int idx = 0;

    for (idx = 0; idx < MQTT_DATA_PROCESS_NUM; idx++)
    {
        if (type == s_mqtt_data_process[idx].type)
        {
            err = s_mqtt_data_process[idx].set_data(is_mst, addr, sid, type, val);
            break;
        }
    }

    // 备份功率调节相关参数
    mqtt_backup_power_ctrl_para(sid);

    if (err == PARA_ERR_DATA_OVER_SCOPE || err == PARA_ERR_CONSTRAINT)
    {
        return MQTT_SCOPE_ERR;
    }
    else if (err == PARA_ERR_STRING)
    {
        return MQTT_FORMAT_ERR;
    }
    else if (err > 0)
    {
        return MQTT_SUCCESS;
    }

    return MQTT_SID_ERR;
}

static int mqtt_cmd_control(int is_mst, int addr, int sid, int type, cJSON* in_data)
{
    for (int i = 0; i < MQTT_CONTROL_SP_NUM; i++)
    {
        if (sid == mqtt_control_sp[i].sid)
        {
            return mqtt_control_sp[i].sp_func(is_mst, addr, cJSON_GetNumberValue(in_data));
        }
    }

    return MQTT_SID_ERR;
}

cJSON* mqtt_parse_msg(const char* in, char* uuid)
{
    cJSON* ret_json = NULL;
    cJSON* msg_id = NULL;
    cJSON* time = NULL;
    if (in == NULL || uuid == NULL)
    {
        return NULL;
    }
    ret_json = cJSON_Parse(in);
    if (ret_json == NULL)
    {
        return NULL;
    }
    msg_id = cJSON_GetObjectItem(ret_json, "msgId");
    if (msg_id == NULL || msg_id->valuestring == NULL || rt_strnlen(msg_id->valuestring, UUID_LEN) != UUID_LEN - 1)
    {
        cJSON_Delete(ret_json);
        return NULL;
    }
    time = cJSON_GetObjectItem(ret_json, "time");
    if (time == NULL)
    {
        cJSON_Delete(ret_json);
        return NULL;
    }
    rt_memcpy(uuid, msg_id->valuestring, UUID_LEN);
    return ret_json;
}

static int parse_out_sid(int is_mst, int* sid, cmd_id_e* cmd, int* type)
{
    int data_num = 0;
    mqtt_data_t* data_tab = NULL;

    *cmd = GET_CMD(*sid);
    *type = GET_TYPE(*sid);
    if (*type >= TYPE_MAX)
    {
        return MQTT_SID_ERR;
    }

    if (*cmd == CMD_PARA)
    {
        data_num = MQTT_PARA_NUM;
        data_tab = mqtt_para_tab;
    }
    else if (*cmd == CMD_REALDATA)
    {
        data_num = MQTT_REALDATA_NUM;
        data_tab = mqtt_realdata_tab;
    }
    else if (*cmd == CMD_CONTROL)
    {
        is_mst = TRUE;
        data_num = MQTT_CONTROL_NUM;
        data_tab = mqtt_control_tab;
    }
    else
    {
        return MQTT_CMD_ERR;
    }

    return mqtt_is_data_valid(is_mst, sid, data_tab, data_num);
}

uint64_t mqtt_get_timestamp(void)
{
    struct timeval tv = {0};

    gettimeofday(&tv, NULL);
    return (uint64_t)tv.tv_sec * 1000 + (uint64_t)tv.tv_usec / 1000;
}

mqtt_err_e mqtt_set_data(int is_mst, int addr, int sid, cJSON* val)
{
    int type = TYPE_MAX;
    cmd_id_e cmd = CMD_MAX;
    mqtt_err_e err = parse_out_sid(is_mst, &sid, &cmd, &type);
    RETURN_VAL_IF_FAIL(err == MQTT_SUCCESS, err);

    switch (cmd)
    {
        case CMD_PARA:
            err = mqtt_cmd_set_para(is_mst, addr, sid, type, val);
            break;
        case CMD_CONTROL:
            err = mqtt_cmd_control(is_mst, addr, sid, type, val);
            break;
        default:
            err = MQTT_CMD_ERR;
            break;
    }
    return err;
}

mqtt_err_e mqtt_get_data(int is_mst, char addr, int sid, cJSON* val)
{
    int type = TYPE_MAX;
    cmd_id_e cmd = CMD_MAX;
    mqtt_err_e err = parse_out_sid(is_mst, &sid, &cmd, &type);
    RETURN_VAL_IF_FAIL(err == MQTT_SUCCESS, err);

    switch (cmd)
    {
        case CMD_PARA:
        case CMD_REALDATA:
            err = mqtt_cmd_get_data(is_mst, addr, sid, type, cmd, val);
            break;
        default:
            err = MQTT_CMD_ERR;
            break;
    }
    return err;
}

unsigned char get_mode_addr(unsigned char* addr)
{
    unsigned char mode = SLAVE;
    if (addr == NULL)
    {
        return SLAVE;
    }
    get_one_data(DAC_DATA_ID_MASTER_SLAVE_STATUS, &mode);
    get_one_para(DAC_COMMON_ID_RS485_COMMU_ADDR_OFFSET, addr);
    return mode;
}

void set_uuid(const char* uuid)
{
    if (uuid == NULL)
    {
        return;
    }
    rt_memcpy(g_uuid, uuid, UUID_LEN);
}

void get_uuid(char* uuid)
{
    if (uuid == NULL)
    {
        return;
    }
    rt_memcpy_s(uuid, UUID_LEN, g_uuid, UUID_LEN);
}

void set_site_id(int which, const char* id)
{
    if ((id == NULL) || (which >= MAX_CLIENT))
    {
        return;
    }
    rt_memcpy_s(g_site_id[which], SITE_ID_LEN, id, SITE_ID_LEN);
}

void get_site_id(int which, char* id)
{
    if ((id == NULL) || (which >= MAX_CLIENT))
    {
        return;
    }
    rt_memcpy_s(id, SITE_ID_LEN, g_site_id[which], SITE_ID_LEN);
}


unsigned short get_slave_msg_cnt(void)
{
    return g_slave_msg_cnt;
}

void set_slave_msg_cnt(unsigned short cnt)
{
    g_slave_msg_cnt = cnt;
}

void set_mqtt_client(int which, mqtt_client_t* client)
{
    if (which == ZTE_CLIENT)
    {
        g_zte_client = client;
    }
    else
    {
        g_oth_client = client;
    }
}

mqtt_client_t* get_mqtt_client(int which)
{
    if (which == ZTE_CLIENT)
    {
        return g_zte_client;
    }
    else if (which == OTH_CLIENT)
    {
        return g_oth_client;
    }
    return NULL;
}

int set_master_soft_ver()
{
    int master_addr = 0;
    get_one_para(DAC_COMMON_ID_RS485_COMMU_ADDR_OFFSET, &master_addr);
    get_one_data(DAC_DATA_ID_MONITOR_SOFTWARE_VER, g_soft_ver[master_addr - 1].mnte_ver);
    get_one_data(DAC_DATA_ID_MASTER_CTRL_SOFTWARE_VER, g_soft_ver[master_addr - 1].mstc_ver);
    get_one_data(DAC_DATA_ID_AUXI_CTRL_SOFTWARE_VER, g_soft_ver[master_addr - 1].auxc_ver);
    get_one_data(DAC_DATA_ID_CPLD_SOFTWARE_VER, g_soft_ver[master_addr - 1].cpld_ver);
    return SUCCESSFUL;

}

int set_soft_ver(int addr)
{
    get_one_data(DAC_PO_DATA_ID_MONITOR_SOFTWARE_VER, g_soft_ver[addr - 1].mnte_ver);
    get_one_data(DAC_PO_DATA_ID_MASTER_CTRL_SOFTWARE_VER, g_soft_ver[addr - 1].mstc_ver);
    get_one_data(DAC_PO_DATA_ID_AUXI_CTRL_SOFTWARE_VER, g_soft_ver[addr - 1].auxc_ver);
    get_one_data(DAC_PO_DATA_ID_CPLD_SOFTWARE_VER, g_soft_ver[addr - 1].cpld_ver);
    return SUCCESSFUL;
}

ms_ver_t* get_soft_ver(int addr)
{
    return &g_soft_ver[addr - 1];
}

int is_client_conn_normal(int which)
{
    mqtt_client_t* client = get_mqtt_client(which);
    if (client != NULL)
    {
        return (client->mqtt_client_state == CLIENT_STATE_CONNECTED);
    }
    else
    {
        return FALSE;
    }
}

int which_client(mqtt_client_t* client)
{
    for (int idx = 0; idx < MAX_CLIENT; idx++)
    {
        if (client == get_mqtt_client(idx))
        {
            return idx;
        }
    }
    return MAX_CLIENT;
}

mqtt_err_e parse_msg_sn(const char* topic, int* sn_idx)
{
    int idx = 0;
    char data[MQTT_TOPIC_LEN_MAX] = {0};
    char* token = NULL;
    char* topic_sn = NULL;
    char* ptr = NULL;
    char device_id[SN_LEN] = {};
    RETURN_VAL_IF_FAIL(topic != NULL, MQTT_NULL_ERR);

    token = rt_strstr(topic, "inv/");
    RETURN_VAL_IF_FAIL(token != NULL, MQTT_FORMAT_ERR);
    rt_strncpy_s(data, MQTT_TOPIC_LEN_MAX, token, rt_strnlen_s(token, MQTT_TOPIC_LEN_MAX - 1));
    token = rt_strtok_s(data, MQTT_TOPIC_LEN_MAX, "/", &ptr);
    RETURN_VAL_IF_FAIL(token != NULL, MQTT_FORMAT_ERR);
    topic_sn = rt_strtok_s(NULL, 0, "/", &ptr);
    RETURN_VAL_IF_FAIL(topic_sn != NULL, MQTT_FORMAT_ERR);

    for (idx = 0; idx < PV_NUM; idx++)
    {
        get_device_id(idx, device_id);
        if (rt_strncmp(device_id, topic_sn, SN_LEN) == 0)
        {
            if (sn_idx != NULL)
            {
                *sn_idx = idx;
            }
            return MQTT_SUCCESS;
        }
    }

    return MQTT_FORMAT_ERR;
}

int send_mqtt_data(mqtt_client_t* client, const char* topic_name, cJSON* json_data)
{
    static char s_json_print[STR_LEN_2048] = {0};      // 使用固定内存大小用于解析json数据
    mqtt_message_t msg = {0};
    int loop = 0;
    rt_memset(s_json_print, 0, STR_LEN_2048);

    if (client == NULL || client->mqtt_client_state != CLIENT_STATE_CONNECTED)
    {
        return FAILURE;
    }

    if (cJSON_PrintPreallocated(json_data, s_json_print, STR_LEN_2048, RT_FALSE) == RT_FALSE)
    {
        LOG_E("topic:%s send mqtt data error!\n", topic_name);
        return FAILURE;
    }
    rt_kprintf("topic_name:%s\n", topic_name);
    for(loop = 0; loop < STR_LEN_2048; loop ++)
    {
        rt_kprintf("%c", s_json_print[loop]);
    }
    rt_kprintf("\n");
    msg.qos = QOS0;
    msg.payload = (void*)s_json_print;
    if (mqtt_publish(client, topic_name, &msg) == MQTT_SUCCESS_ERROR)
    {
        net_connect_mode_handel(NET_MODE_MQTT, DAC_DATA_ID_NET_CONNECT_MODE);
    }
    return SUCCESSFUL;
}

int alarm_is_mqtt_alarm(int alarm_id)
{
    for (int idx = 0; idx < MQTT_ALARM_NUM; idx++)
    {
        if ((ALM_ID_GET_ALM_CODE(alarm_id) + 1) == (mqtt_alarm_tab[idx].sid & 0xFFFF))
        {
            return TRUE;
        }
    }

    return FALSE;
}

int search_alarm_for_po(int po_alarm_id)
{
    for (int idx = 0; idx < MQTT_ALARM_NUM; idx++)
    {
        if (po_alarm_id >= mqtt_alarm_tab[idx].sid_po &&
            po_alarm_id < mqtt_alarm_tab[idx].sid_po + mqtt_alarm_tab[idx].num)
        {
            return mqtt_alarm_tab[idx].sid + po_alarm_id - mqtt_alarm_tab[idx].sid_po;
        }
    }

    return 0;
}



int mqtt_map_dev_status(int sid, cJSON* item)
{
    if (sid != DAC_DATA_ID_DEV_STATUS && sid != DAC_PO_DATA_ID_DEV_STATUS)
    {
        return SUCCESSFUL;
    }

    cJSON* val = cJSON_GetObjectItem(item, "value");
    if (!cJSON_IsNumber(val))
    {
        return FAILURE;
    }

    int status = cJSON_GetNumberValue(val);
    if ((status & 0xFF00) == 0x0300)
    {
        cJSON_SetNumberValue(val, DEV_OFF);
    }
    else if ((status & 0xFF00) == 0x0000 || (status & 0xFF00) == 0xA000)
    {
        cJSON_SetNumberValue(val, DEV_STANDBY);
    }
    else
    {
        cJSON_SetNumberValue(val, DEV_ON);
    }

    return SUCCESSFUL;
}


char is_immediate_submit_sid(unsigned int sid)
{
    unsigned int immediate_submit_sid[] = {DAC_PO_DATA_ID_POWER_ON_OFF_STA, DAC_PO_DATA_ID_ACTIVE_POWER_CONTROL_MODE_CURR, DAC_PO_DATA_ID_ACTIVE_POWER_DERATING_SETTING_CURR,
    DAC_PO_DATA_ID_ACTIVE_POWER_DERATING_PERCENT_CURR, DAC_PO_DATA_ID_REACTIVE_POWER_COMPEN_MODE_CURR, DAC_PO_DATA_ID_REACTIVE_POWER_COMPEN_SETTING_FACTOR_CURR,
    DAC_PO_DATA_ID_REACTIVE_POWER_COMPEN_SETTING_CURR, DAC_PO_DATA_ID_CSC_VERSION_CURR, DAC_PO_DATA_ID_PSC_VERSION_CURR,
    DAC_PO_DATA_ID_ALARM_TOTAL_SIGNAL, DAC_PO_DATA_ID_ACCI_TOTAL_SIGNAL, DAC_PO_DATA_ID_REACTIVE_POWER_VALUE_CURR, DAC_PO_DATA_ID_REACTIVE_POWER_PCT_CURR};
    int loop = 0;
    for(loop = 0; loop < sizeof(immediate_submit_sid) / sizeof(unsigned int); loop ++)
    {
        if(sid == immediate_submit_sid[loop])
        {
            return TRUE;
        }
    }
    return FALSE;
}

char is_immediate_submit_master_sid(unsigned int sid)
{
    unsigned int immediate_submit_sid[] = {DAC_DATA_ID_POWER_ON_OFF_STA, DAC_DATA_ID_ACTIVE_POWER_DERATING_SETTING_CURR, DAC_DATA_ID_ACTIVE_POWER_DERATING_PERCENT_CURR,
    DAC_DATA_ID_REACTIVE_POWER_COMPEN_SETTING_FACTOR_CURR, DAC_DATA_ID_REACTIVE_POWER_COMPEN_SETTING_CURR, DAC_DATA_ID_ACTIVE_POWER_CONTROL_MODE_CURR, DAC_DATA_ID_REACTIVE_POWER_COMPEN_MODE_CURR,
    DAC_DATA_ID_CSC_VERSION_CURR, DAC_DATA_ID_PSC_VERSION_CURR, DAC_DATA_ID_ALARM_TOTAL_SIGNAL, DAC_DATA_ID_ACCI_TOTAL_SIGNAL, DAC_DATA_ID_REACTIVE_POWER_VALUE_CURR, DAC_DATA_ID_REACTIVE_POWER_PCT_CURR};
    int loop = 0;
    for(loop = 0; loop < sizeof(immediate_submit_sid) / sizeof(unsigned int); loop ++)
    {
        if(sid == immediate_submit_sid[loop])
        {
            return TRUE;
        }
    }
    return FALSE;
}

int mqtt_get_master_slave_data(int is_mst, char is_immediate_submit, cJSON* item, char addr, int sid_index, int id_loop)
{
    unsigned int sid = 0;
    int rc = MQTT_SID_ERR;
    if (is_mst)
    {
        // 1）立刻上送 (仅限特殊SID)    2）定时上送(全量SID)
        if((is_immediate_submit && is_immediate_submit_master_sid(mqtt_realdata_tab[sid_index].sid_offset)) ||
            !is_immediate_submit)
        {
            sid = mqtt_realdata_tab[sid_index].sid_offset + id_loop;
            rc = mqtt_cmd_get_data(TRUE, addr, sid, GET_REAL_TYPE(sid), CMD_REALDATA, item);
        }
    }
    else
    {
        // 1）立刻上送 (仅限特殊SID)    2）定时上送(全量SID)
        if((is_immediate_submit && is_immediate_submit_sid(mqtt_realdata_tab[sid_index].sid_po)) || 
        (!is_immediate_submit && mqtt_realdata_tab[sid_index].sid_po != 0))
        {
            sid = mqtt_realdata_tab[sid_index].sid_po + id_loop;
            rc = mqtt_cmd_get_data(FALSE, addr, sid, GET_REAL_TYPE(sid), CMD_REALDATA, item);
        }
    }
    mqtt_map_dev_status(sid, item);
    return rc;
}

cJSON* pack_property(int is_mst, char addr, char is_immediate_submit)
{
    static int sid_index = 0;
    int id_loop = 0;
    int rc = 0;
    unsigned char pack_data_num = 0;
    cJSON* arr = cJSON_CreateArray();
    if (arr == NULL)
    {
        sid_index = 0;
        LOG_E("pack_property cJSON_CreateArray failed");
        return NULL;
    }

    // 遍历实时数据
    for (; sid_index < MQTT_REALDATA_NUM; sid_index++)
    {
        for (id_loop = 0; id_loop < mqtt_realdata_tab[sid_index].num; id_loop++)
        {
            cJSON* item = cJSON_CreateObject();
            if (item == NULL)
            {
                LOG_E("pack_property cJSON_CreateObject failed");
                continue;
            }
            if (cJSON_AddNumberToObject(item, "id", mqtt_realdata_tab[sid_index].sid + id_loop) == NULL)
            {
                cJSON_Delete(item);
                LOG_E("pack_property cJSON_AddNumberToObject failed");
                continue;
            }
            rc = mqtt_get_master_slave_data(is_mst, is_immediate_submit, item, addr, sid_index, id_loop);
            if (rc != MQTT_SUCCESS)
            {
                cJSON_Delete(item);
                continue;
            }
            cJSON_AddItemToArray(arr, item);

            pack_data_num++;
            if (pack_data_num > SEND_REAL_DATA_NUM_ONCE)
            {
                sid_index++;
                return arr;
            }
        }
    }

    if (pack_data_num > 0)
    {
        return arr;
    }

    sid_index = 0;
    cJSON_Delete(arr);
    return NULL;
}

int is_slave_data_invalid(int msg)
{
    if (msg == GET_SLAVE_PARA)
    {
        short val = 0;
        get_one_data(DAC_PO_PARA_ID_REACTIVE_POWER_REGULATIONG_TIME, &val);
        return (val == BAD_DATA);
    }
    return TRUE;
}

static void set_slave_data_invalid(int msg)
{
    if (msg == GET_SLAVE_PARA)
    {
        short val = BAD_DATA;
        set_one_data(DAC_PO_PARA_ID_REACTIVE_POWER_REGULATIONG_TIME, &val);
    }
}

// 发送消息给北向，获取对应从机信息
void send_get_msg_to_slave(char addr, int msg)
{
    set_slave_data_invalid(msg);
    send_msg_to_thread(msg, MOD_INVERTER, &addr, sizeof(addr));
}

int handle_inform_4g_reset_device()
{
    mqtt_client_t* client_zte = get_mqtt_client(ZTE_CLIENT);
    mqtt_client_t* client_oth = get_mqtt_client(OTH_CLIENT);


    if(CLIENT_STATE_CONNECTED == client_zte->mqtt_client_state || CLIENT_STATE_CONNECTED == client_oth->mqtt_client_state)
    {
        return TRUE;
    }
    rt_kprintf("mqtt send reset 4G dev msg\n");
    send_msg_to_thread(MQTT_INFORM_4G_RESET, MOD_NET_4G, NULL, 0);
    return FALSE;
}

int judge_iv_file_exit()
{
    unsigned char iv_complete_status = 0;
    get_one_data(DAC_DATA_ID_IV_COMPLETE_STATUS, &iv_complete_status);
    if(iv_complete_status == 0)
    {
        return FALSE;
    }
    return TRUE;
}

int check_control_id(int sid, int id)
{
    for (int idx = 0; idx < MQTT_PARA_NUM; idx++)
    {
        if ((GET_DATA_ID_NO_NUM(sid) == GET_DATA_ID_NO_NUM(mqtt_para_tab[idx].sid)) &&
            (GET_DATA_NUM(sid) <= mqtt_para_tab[idx].num))
        {
            return ((mqtt_para_tab[idx].sid_po + GET_DATA_NUM(sid) - 1) == id);
        }
    }

    for (int idx = 0; idx < MQTT_CONTROL_NUM; idx++)
    {
        if (sid == mqtt_control_tab[idx].sid)
        {
            if (mqtt_control_tab[idx].sid_offset == DAC_CTRL_ID_CTRL_POWER_STATUS)
            {
                if (id == CMD_INVERTER_PARALLEL_MODBUS_CTRL_POWER_ON || id == CMD_INVERTER_PARALLEL_MODBUS_CTRL_POWER_OFF)
                {
                    return TRUE;
                }
                break;
            }
            else if (mqtt_control_tab[idx].sid_offset == DAC_CTRL_ID_PV_CTRL_ORDER)
            {
                if (id == CMD_INVERTER_PARALLEL_MODBUS_CTRL_IV_SCAN_STOP || id == CMD_INVERTER_PARALLEL_MODBUS_CTRL_IV_SCAN_START)
                {
                    return TRUE;
                }
                break;
            }
        }
    }

    return FALSE;
}

void mqtt_backup_power_ctrl_para(int sid)
{
    short data_s = 0;
    float data_f = 0.0;
    int power_ctrl_ids[] = {DAC_PARA_ID_ACTIVE_POWER_CONTROL_MODE_OFFSET,
                                DAC_PARA_ID_REACTIVE_POWER_COMPEN_MODE_OFFSET,
                                DAC_PARA_ID_GRID_ACTIVE_POWER_CHANGE_OFFSET,
                                DAC_PARA_ID_ACTIVE_POWER_DERATING_SETTING_OFFSET,
                                DAC_PARA_ID_ACTIVE_POWER_DERATING_PERCENT_OFFSET,
                                DAC_PARA_ID_GRID_REACTIVE_POWER_CHANGE_OFFSET,
                                DAC_PARA_ID_REACTIVE_POWER_COMPEN_SETTING_FACTOR_OFFSET,
                                DAC_PARA_ID_REACTIVE_POWER_COMPEN_SETTING_OFFSET,
                                DAC_PARA_ID_REACTIVE_POWER_REGULATIONG_TIME_OFFSET,
                                DAC_PARA_ID_REACTIVE_POWER_VALUE_OFFSET,
                                DAC_PARA_ID_REACTIVE_POWER_PCT_OFFSET};

    int len_para = sizeof(power_ctrl_ids)/sizeof(power_ctrl_ids[0]);

    for(int i = 0; i < len_para; i++)
    {
        if(sid == power_ctrl_ids[i])
        {
            if(i < 2)
            {
                get_one_para(sid, &data_s);
                set_one_para(sid+1, &data_s, TRUE, FALSE);
            }
            else
            {
                get_one_para(sid, &data_f);
                set_one_para(sid+1, &data_f, TRUE, FALSE);
            }
        }
    }
}


int collect_para_sids(sid_list_info_t* sid_list_info, int sid, int is_mst)
{
    int type = TYPE_MAX;
    cmd_id_e cmd = CMD_MAX;
    mqtt_err_e err = parse_out_sid(is_mst, &sid, &cmd, &type);
    RETURN_VAL_IF_FAIL(err == MQTT_SUCCESS, err);

    if(CMD_PARA == cmd)
    {
        sid_list_info->sid_list[sid_list_info->sid_num] = sid;
        sid_list_info->sid_num++;
    }
    return SUCCESSFUL;

}


int save_alarm_when_disconn(alarm_mqtt_t* alarm_mqtt)
{
    RETURN_VAL_IF_FAIL(alarm_mqtt->alm_id != 0, FAILURE);
    static int list_head = 0;
    static int list_tail = 0;

    // 存入新的alarm_mqtt元素
    char found = FALSE;
    for (int i = 0; i < MAX_ALARM_COUNT; i++) {
        if(s_alarm_list[i].alm_id == 0) break;
        if (s_alarm_list[i].alm_id == alarm_mqtt->alm_id){
            found = TRUE;
            if (s_alarm_list[i].time_stamp < alarm_mqtt->time_stamp) {
                // 如果新元素的时间戳更大，则替换旧元素
                rt_memcpy_s(&s_alarm_list[i], sizeof(alarm_mqtt_t), alarm_mqtt, sizeof(alarm_mqtt_t));
            }
            break;
        }
    }

    if (!found) {
        // 如果没有找到相同ID的元素，则将新元素添加到列表中
        rt_memcpy_s(&s_alarm_list[list_tail], sizeof(alarm_mqtt_t), alarm_mqtt, sizeof(alarm_mqtt_t));
        list_tail = (list_tail + 1) % MAX_ALARM_COUNT;
        
        // 如果列表已满，删除最早存入的元素
        if (list_tail == list_head) {
            list_head = (list_head + 1) % MAX_ALARM_COUNT;
        }
    }

    return SUCCESSFUL;
}


int get_latest_ala_list(void)
{
    int alarm_count = 0;
    for(int i = 0; i < MAX_ALARM_COUNT; i++)
    {
        if(s_alarm_list[i].alm_id == 0)
        {
            break;
        }
        else
        {
            alarm_count++;
        }
    }
    return alarm_count;
}

void clean_s_alarm_list()
{
    rt_memset_s(s_alarm_list, sizeof(alarm_mqtt_t) * MAX_ALARM_COUNT, 0, sizeof(alarm_mqtt_t) * MAX_ALARM_COUNT);
}

cJSON* pack_alarm_property(void)
{
    static int alarm_index = 0;
    int mqtt_alarm_id = 0;
    unsigned char pack_alarm_num = 0;
    int alarm_count = 0;
    alarm_count = get_latest_ala_list();
    RETURN_VAL_IF_FAIL(alarm_count != 0, NULL);
    cJSON* alarm_arr = cJSON_CreateArray();
    if(alarm_arr == NULL)
    {
        alarm_index = 0;
        LOG_E("pack_alarm_property cJSON_CreateArray failed!");
        return NULL;
    }

    for(; alarm_index < alarm_count; alarm_index++)
    {
        mqtt_alarm_id = (ALM_ID_GET_ALM_CODE(s_alarm_list[alarm_index].alm_id) + ALM_ID_GET_DEV(s_alarm_list[alarm_index].alm_id)) | ALARM_ID_MQTT_BASE;
        cJSON* item = cJSON_CreateObject();
        if(item == NULL)
        {
            LOG_E("pack_alarm_property | createObject failed | alram_id: %x", mqtt_alarm_id);
            continue;
        }
        cJSON_AddNumberToObject(item, "id", mqtt_alarm_id);
        cJSON_AddNumberToObject(item, "value", s_alarm_list[alarm_index].value);
        cJSON_AddNumberToObject(item, "time", (uint64_t)(s_alarm_list[alarm_index].time_stamp) * 1000);   // 将秒转换成毫秒
        cJSON_AddItemToArray(alarm_arr, item);

        pack_alarm_num++;
        if(pack_alarm_num > ALARM_ACCU_MAX)
        {
            alarm_index++;
            return alarm_arr;
        }
    }

    if(pack_alarm_num > 0)
    {
        return alarm_arr;
    }

    alarm_index = 0;
    cJSON_Delete(alarm_arr);
    return NULL;

}


int mqtt_clean_his_data(int is_mst, int addr, int val)
{
    send_clean_his_data_msg();
    return MQTT_SUCCESS;
}

int mqtt_clean_alarm(int is_mst, int addr, int val)
{
    send_clean_alarm_msg();
    return MQTT_SUCCESS;
}

int mqtt_clean_his_event(int is_mst, int addr, int val)
{
    send_clean_his_event_msg();
    return MQTT_SUCCESS;
}

int mqtt_clean_his_energy(int is_mst, int addr, int val)
{
    send_delete_his_energy_msg();
    return MQTT_SUCCESS;
}

int mqtt_clean_iv_data(int is_mst, int addr, int val)
{
    send_clean_iv_data_msg();
    return MQTT_SUCCESS;
}

int mqtt_clean_fault_record(int is_mst, int addr, int val)
{
    send_clean_fault_record_msg();
    return MQTT_SUCCESS;
}

int trig_fault_record(int is_mst, int addr, int val)
{   
    short ctrl_val = 0x0001;
    set_one_data(DAC_CTRL_ID_DUBUG_CONTROL_COMMAND, &ctrl_val);
    send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, DC_AC_DEBUG_CTRL_ORDER);
    return MQTT_SUCCESS;
}

