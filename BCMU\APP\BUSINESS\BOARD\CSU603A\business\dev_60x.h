/**
 * @file     device_type.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2017-04-19
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */

#ifndef _DEVICE_604A_H
#define _DEVICE_604A_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "device_type.h"

/*设备类型定义*/
#define DEV_CSU                 1
#define DEV_603A_MAIN          	2
#define DEV_603A_UIB01          3

#pragma pack(1)

typedef struct {
	unsigned char barcode[32];
	unsigned int csu_fault_state;
	unsigned int csu_fault_index;
}csu_604_para_t;

#pragma pack()

dev_type_t* init_dev_603a_main(void);
dev_type_t* init_dev_603a_uib01(void);


#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _DEVICE_604A_H
