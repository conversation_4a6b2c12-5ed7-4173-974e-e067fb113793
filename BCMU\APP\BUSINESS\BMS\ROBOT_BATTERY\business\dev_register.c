#include "dev_can_comm.h"


static cmd_t no_poll_cmd_tab[CMD_TAB_NUM] = {0};
static dev_type_t s_dev_robot_bat_can = {
    DEV_ROBOT_BAT, 1, PROTOCOL_REMOTE_COMM, LINK_ROBOT_BAT_CAN, R_BUFF_LEN_1024, S_BUFF_LEN_1024, 0, no_poll_cmd_tab, NULL
};

dev_type_t* init_dev_robot_bat_can(void) {
    register_charger_tab();
    register_csu_tab();
    register_main_ctrl_tab();
    return &s_dev_robot_bat_can;
}

int register_cmd_tab(cmd_t* cmd_tab, int cmd_num)
{
    if (cmd_num <= 0 || cmd_tab == NULL) {
        return FAILURE;
    }

    int exist_cmd_num = 0;
    for (; exist_cmd_num < CMD_TAB_NUM; exist_cmd_num++)
    {
        if (no_poll_cmd_tab[exist_cmd_num].cmd_id == 0)
        {
            break;
        }
    }

    if ((exist_cmd_num + cmd_num) > CMD_TAB_NUM)
    {
        return FAILURE;
    }

    rt_memcpy_s(&no_poll_cmd_tab[exist_cmd_num], 
                (CMD_TAB_NUM - exist_cmd_num) * sizeof(cmd_t), 
                cmd_tab, 
                cmd_num * sizeof(cmd_t));

    return SUCCESSFUL;
}

