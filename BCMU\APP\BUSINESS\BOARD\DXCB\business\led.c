#include <rtthread.h>
#include <rtdevice.h>
#include <board.h>
#include "led.h"
#include "utils_thread.h"
#include "msg_id.h"
#include "msg.h"
#include "data_type.h"
#include "softbus.h"
#include "utils_server.h"
#include "signal_led.h"
#include "pin_define.h"
#include "io_ctrl_api.h"



int send_ctrl_led_msg(unsigned short lightoff_time, unsigned short lighton_time, unsigned char pin_no)
{
    led_ctrl_msg ctrl_msg = {0};
    ctrl_msg.lightoff_time = lightoff_time;
    ctrl_msg.lighton_time = lighton_time;
    ctrl_msg.pin_no = pin_no;
    pub_msg_to_thread(LED_CTRL_MSG, &ctrl_msg, sizeof(led_ctrl_msg));
    return SUCCESSFUL;
}

int send_led_ctrl_msg(unsigned char status)
{
    if(status == LED_NORMAL)
    {
        send_ctrl_led_msg(50,50,PIN_LED_CONTROL);
    }
    else if(status == LED_DOWNLOD)
    {
        send_ctrl_led_msg(16,16,PIN_LED_CONTROL);
    }
    else if(status == LED_ALWAYS_OFF)
    {
        send_ctrl_led_msg(50,0,PIN_LED_CONTROL);
    }
    else if(status == LED_ALWAYS_ON)
    {
        send_ctrl_led_msg(0,50,PIN_LED_CONTROL);
    }

    return SUCCESSFUL;
}




