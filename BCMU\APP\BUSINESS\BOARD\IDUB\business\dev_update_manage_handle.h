#ifndef _DEV_UPDATE_MANAGE_HANDLE_H
#define _DEV_UPDATE_MANAGE_HANDLE_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "sps.h"

#define FLAG_NO_UPDATE                          0
#define FLAG_APP_UPDATE_APP                     1
#define FLAG_BOOT_UPDATE_APP                    2

#define UPDATE_FILE_NAME        "IDUB_APP.bin"
#define UPDATE_FILE_NAME_LEN    40
#define UPDATE_FILE_TIME_LEN    20

#define UPDATE_TRIG_COUNT       3

#define APP_DOWNLOAD_SIZE       1572864

typedef struct{
    int trig_success;       ///<  触发成功
    int trig_times;         ///<  触发次数
}trig_ctr_inf_t;
#pragma pack(1)
typedef struct
{
    unsigned short per_frame_len;                       // 单帧长度
    unsigned short total_frames;                        // 帧数量
    unsigned int   total_leng;                          // 文件长度
    unsigned char  file_name[UPDATE_FILE_NAME_LEN];     // 文件名
    unsigned char  file_time[UPDATE_FILE_TIME_LEN];     // 文件传输时间
    unsigned short file_crc;                            // 文件总校验
    unsigned short resv;                                // 备用
}bottom_update_info_t;


typedef struct
{
    bottom_update_info_t file_info;
    unsigned short  cur_frame_no;
    unsigned int    data_offset;     ///<  文件存储偏移
    unsigned char   sys_run_flag;
    unsigned char   backup_flag;
    unsigned char   update_flag;
    unsigned char   count;           //程序卡boot计数
    unsigned short crc;
}bottom_update_manage_t;
#pragma pack()
int bottom_update_init(void);
void time_out_ctrl_update();
int parse_update_trig(void* dev_inst, void *cmd_buf);
int pack_update_trig(void* dev_inst, void *cmd_buf);
int parse_update_data(void* dev_inst, void *cmd_buf);
int pack_update_data(void* dev_inst, void *cmd_buf);
int parse_first_frm(cmd_buf_t* tran_cmd_buf);
int parse_data_frm(void* dev_inst, cmd_buf_t* cmd_buf, unsigned short frm_no);
int file_change_check(bottom_update_info_t *pold_file, bottom_update_info_t *pnew_file);
int write_update_info(bottom_update_manage_t* update_manage_info);
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _DEV_UPDATE_MANAGE_HANDLE_H