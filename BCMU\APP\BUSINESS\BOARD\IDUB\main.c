#include <stdio.h>
#include <rtthread.h>
#include "north_main.h"
#include "sample.h"
#include "utils_server.h"
#include "server_id.h"
#include "storage.h"
#include "partition_table.h"
#include "protocol_modbus_comm.h"
#include "dev_north_idub_modbus.h"
#include "watchdog.h"
#include "dev_north_update_tab.h"
#include "dev_update_manage_handle.h"
#include "utils_data_transmission.h"
#include "realdata_save.h"
#include "realdata_id_in.h"
#include "dev_north_idub_apptest.h"
#include "protocol_1363_comm.h"
#include "software_version.h"
#include "utils_math.h"
#include "unified_id_interface.h"
#include "ee_public_info.h"
#include "utils_time.h"

static char s_north_thread_stack[4096];
static char s_sample_thread_stack[4096];
static rt_device_t wdg_dev;

static server_info_t g_server_group[] = {
    /*   服务ID              服务名字            栈大小                             栈的起始地址                    优先级*/
    {{{NORTH_SERVER_ID,      "north",        sizeof(s_north_thread_stack),    s_north_thread_stack,       SERVER_PRIO_LOW}}, init_north,      north_comm_th},
    {{{SAMPLE_SERVER_ID,     "sample",       sizeof(s_sample_thread_stack),   s_sample_thread_stack,      SERVER_PRIO_LOW}}, sample_init_sys,  sample_main},
};

static protocol_process_t protocol_process_tab[] = {
    {PROTOCOL_YD_1363, s1363_pack, s1363_parse},
    {PROTOCOL_MODBUS_RTU, modbus_pack, modbus_parse},
    {PROTOCOL_BOTTOM_COMM, bottom_pack, bottom_parse},
};

static dev_init_t dev_init_tab[] = {
    {DEV_NORTH_IDUB_APPTEST, init_idub_dev_apptest, NULL},
    {DEV_NORTH_IDUB, init_dev_north_idub, NULL},
    {DEV_NORTH_IDUB_UPDATE, init_idub_dev_north_update, NULL},

};

static link_type_t link_type_tab[] = {
     {LINK_COM, s485_dev_init, com_dev_read, com_dev_write, com_dev_set},
};

static link_inst_t link_inst_tab[] = {
    {LINK_NORTH_IDUB,   LINK_COM, "usart1"},     //串口模式
};

int sys_run_result()
{
    bottom_update_manage_t update_info = {0};
    if(handle_storage(read_opr, ONCHIP_UPDATE_INFO, (unsigned char*)&update_info, sizeof(bottom_update_manage_t), 0) != SUCCESSFUL)
    {
        return FAILURE;
    }
    
    update_info.count = 0;
    update_info.sys_run_flag = TRUE;
    write_update_info(&update_info);
    
    return  SUCCESSFUL;
}


static void feed_dog(void)
{
    rt_device_control(wdg_dev, RT_DEVICE_CTRL_WDT_KEEPALIVE, NULL);
    return;
}

int init_watchdog(void)
{
    wdg_dev = rt_device_find("wdt");
    if (RT_NULL == wdg_dev)
    {
        return -RT_ERROR;
    }
    if (RT_EOK != rt_device_control(wdg_dev, RT_DEVICE_CTRL_WDT_START, RT_NULL))
    {
        return -RT_ERROR;
    }
    rt_thread_idle_sethook(feed_dog);

    return RT_EOK;
}


void update_idub_fact_info(void)
{
    unsigned char sn[SN_LEN] = {0};
    unsigned char hardware_version[HARDWARE_VER_LEN] = {0};
    unsigned char board_manu_date[BOARD_MANU_DATE_LEN] = {0};
    date_base_t tm = {0};
    set_one_data(IDUB_DATA_ID_SOFTWARE_NAME, "IDUB");
    set_one_data(IDUB_DATA_ID_SOFTWARE_VERSION, IDUB_COMPILE_VERSION);
    set_one_data(IDUB_DATA_ID_SOFTWARE_DATE, IDUB_COMPILE_VERSION_DATE);
    handle_storage(read_opr, EE_PUBLIC_INFO, sn, SN_LEN, SN_OFFSET);
    set_one_data(IDUB_DATA_ID_SERIAL_NUMBER, sn);
    handle_storage(read_opr, EE_PUBLIC_INFO, hardware_version, HARDWARE_VER_LEN, HARDWARE_VERSION_OFFSET);
    set_one_data(IDUB_DATA_ID_HARDWARE_VERSION, hardware_version);
    handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char *)&tm, sizeof(date_base_t), MANU_DATE_OFFSET);
    date_to_string((char *)board_manu_date, (date_base_t *)&tm);
    set_one_data(IDUB_DATA_ID_BOARD_MANU_DATE, board_manu_date);
    return;
}


// 定义接口表
static const data_access_interface_t id_base_interface = {
    .get_data = unified_get_data,
    .set_data = unified_set_data,    
    .linear_search = linear_search_id_index
};

int main(void)
{
    init_watchdog();
    init_crc();
    if (SUCCESSFUL != storage_init(&part_tab[0], sizeof(part_tab)/sizeof(part_info_t))) {
        return FAILURE;
    }
    register_link_inst_tab_info(link_inst_tab, sizeof(link_inst_tab)/sizeof(link_inst_tab[0]));
    register_link_type_info(link_type_tab, sizeof(link_type_tab) / sizeof(link_type_t));
    // 注册 协议所使用的函数接口
    register_protocol_process_info(protocol_process_tab, sizeof(protocol_process_tab) / sizeof(protocol_process_t));
    register_data_access_interface(&id_base_interface);
    // 注册协议
    init_dev_tab(dev_init_tab, sizeof(dev_init_tab)/sizeof(dev_init_tab[0]));
    
    init_real_data_memory();
    update_idub_fact_info();
    
    create_server(&g_server_group[0], sizeof(g_server_group)/sizeof(g_server_group[0]));
    sys_run_result();
    return 0;
}








