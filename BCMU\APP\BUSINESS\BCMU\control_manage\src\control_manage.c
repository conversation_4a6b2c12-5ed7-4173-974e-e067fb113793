/*
 * @file    : control_manage.c
 * @brief   : 控制管理
 * @details : 
 * <AUTHOR> 邹绍云10326737
 * @Date    : 2023-02-08
 * @LastEditTime: 2023-02-08
 * @version : V0.0.1
 * @para    : Copyright (c) 
 *            ZTE Corporation
 * @par     : History
 *     version: author, date, descn
 */
#include <string.h>
#include <rtthread.h>
#include <stdlib.h>
#include "utils_thread.h"
#include "control_manage.h"
#include "msg.h"
#include "cmd.h"
#include "device_type.h"
#include "sps.h"
#include "msg_id.h"
#include "addr_distribution.h"
// #define CONTROL_MANAGE_DEBUG 

static rt_uint32_t last_time = 0;
static module_msg_t msg_rcv = {.dest = MOD_CONTROL};

/* DO控制处理 */
void ctrl_msg_process(void);

static void process_data_change(const rt_msg_t pMsg) {

}

static msg_map control_manage_msg_map[] =
{
    {DATA_CHANGE_MSG_ID,   process_data_change,  },      
};

/*thread_info_t thread_ctrl_manage_group[] = {
    {"control_manage", ctrl_manage_thread_init, ctrl_manage_thread, RT_NULL, 256, THREAD_PRIO_HIG, THREAD_TICK},
};*/

/* 北向初始化，起线程 */
void* init_ctrl_manage(void * param)
{
    server_info_t *server_info = (server_info_t *)param;
    RETURN_VAL_IF_FAIL(server_info != NULL, NULL);
    server_info->server.server.map_size = sizeof(control_manage_msg_map) / sizeof(msg_map);
    register_server_msg_map(control_manage_msg_map, server_info);

    init_addr_distr();
    ctrl_manage_init(NULL);
    return NULL;
}

int ctrl_manage_init(void *param) {
    if (init_msg_queue(MOD_CONTROL) == NULL) {
        return FAILURE;
    }

    fire_protect_ctrl_init();
    return SUCCESSFUL;
}

/* BSMU收发线程 */
void ctrl_manage_main(void *param) {
	#ifdef CONTROL_MANAGE_DEBUG
	rt_kprintf("###### ctrl_manage_main start ###### \n");
	#endif
    rt_int32_t time_interval = 10; // unit:ms 时间需要后续调试后确定
    while (is_running(TRUE)) {
        //调用站址分配流程
        addr_distribution(NULL);
        // 收消息模块返回的消息,并发送结果
        ctrl_msg_process();
        //exe_heartbeat(last_time, MOD_CONTROL);
		#ifdef CONTROL_MANAGE_DEBUG
		rt_kprintf("###### ctrl_manage_main thread runing ###### \n");
		#endif
        rt_thread_mdelay(time_interval);
    }
}

void ctrl_msg_process(void) {
    
    if (SUCCESSFUL != recv_msg(&msg_rcv))
        return;
    
    switch (msg_rcv.msg_id) {
        case NOTIFY_ADDR_DISTRIBUTION_MSG:
            set_wait_start_flag(&msg_rcv);
            break;
    
        case RELAY_DO_CTR_MSG:
            do_ctrl_process((do_msg_t*)msg_rcv.data);
            break;
            
        case EPO_ALARM_MSG:
            epo_ctrl_process();
            break;
        
        case ALM_LED_CTRL_MSG:
            // alarm_led_ctrl(msg_rcv.data);
            break;

        case SOC_SOH_SET_MSG:
            soc_soh_set_proc(msg_rcv.data);
            break;
        
        default:
            break;    
    }
    /*if (msg_rcv.data != NULL) {
        free(msg_rcv.data);
        msg_rcv.data = NULL;
    }*/
}

void soc_soh_set_proc(void* data) {
    if (data == NULL)
        return;
    para_msg_t *para_msg = (para_msg_t *)data;
    set_bmu_soc_data(para_msg->bmu_para);
    get_bmu_data(&(para_msg->dev_inst->dev_data), 0);
    send_dest_cmd(para_msg->dev_inst, BMU_SET_SOC_SOH_DATA);
}

