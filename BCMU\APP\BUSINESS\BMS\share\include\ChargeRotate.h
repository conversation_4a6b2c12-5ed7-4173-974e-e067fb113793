#ifndef SOFTWARE_SRC_APP_CHARGEROTATE_H_
#define SOFTWARE_SRC_APP_CHARGEROTATE_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "common.h"
#include "battery.h"

void RotateMaster(void);
void RotateSlave(void);
void InitRotate(BOOLEAN bStartCharge);
void CheckRemoteSuplyOutput(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo);
void CheckRotateCurrent(void);
void CheckRotateOpen(void);
void CheckRotateClose(void);
void CheckRotateCurrent(void);
void DealChargeRotate(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo);
void DealSlaveRotateTimeout(T_BattDealInfoStruct *ptBattDeal);
void CtrlChargeRotate(void);
void CheckIfEnterRemoteEnd(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo); //是否进入远供末期

void setRotateDebugInfo(T_RotateDebugStruct *ptRotateDebug, T_BattDealInfoStruct *ptBattDeal);
void parseRotateInfo(T_RotateInfoStruct *ptRotate);
void LoadTestRotateInfo(T_RotateInfoStruct *ptRotate);

BOOLEAN ChargeRotateUnitest(BOOLEAN bFlag);
#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // SOFTWARE_SRC_APP_SAMPLE_H_;
