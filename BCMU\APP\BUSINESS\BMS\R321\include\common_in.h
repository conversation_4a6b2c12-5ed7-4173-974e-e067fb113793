#ifndef SOFTWARE_SRC_APP_COMMON_IN_H_
#define SOFTWARE_SRC_APP_COMMON_IN_H_
#include "common.h"

#ifdef __cplusplus
extern "C" {
#endif

/// 参数ID宏定义
#define PARA_ID_CHG_CURR_HIGH_ALM  0 // 充电过流告警阈值
#define PARA_ID_CHG_CURR_HIGH_PRT  1 // 充电过流保护阈值
#define PARA_ID_DISCHG_CURR_HIGH_ALM  2 // 放电过流告警阈值
#define PARA_ID_DISCHG_CURR_HIGH_PRT  3 // 放电过流保护阈值
#define PARA_ID_BATT_OVER_VOLT_ALM  4 // 电池组过压告警阈值,  使用单体值表示，其真实值应乘以电芯节数
#define PARA_ID_BATT_OVER_VOLT_PRT  5 // 电池组过压保护阈值,  使用单体值表示，其真实值应乘以电芯节数
#define PARA_ID_BOARD_TEMP_HIGH_PRT  6 // 单板过温保护阈值
#define PARA_ID_BOARD_TEMP_HIGH_ALM  7 // 单板过温告警阈值
#define PARA_ID_ENV_TEMP_HIGH_ALM  8 // 环境温度高告警阈值
#define PARA_ID_ENV_TEMP_LOW_ALM  9 // 环境温度低告警阈值
#define PARA_ID_BATT_UNDER_VOLT_ALM  10 // 电池组欠压告警阈值,  使用单体值表示，其真实值应乘以电芯节数
#define PARA_ID_BATT_UNDER_VOLT_PRT  11 // 电池组欠压保护阈值,  使用单体值表示，其真实值应乘以电芯节数
#define PARA_ID_CELL_OVER_VOLT_ALM  12 // 单体过压告警阈值
#define PARA_ID_CELL_OVER_VOLT_PRT  13 // 单体过压保护阈值
#define PARA_ID_CELL_UNDER_VOLT_ALM  14 // 单体欠压告警阈值
#define PARA_ID_CELL_UNDER_VOLT_PRT  15 // 单体欠压保护阈值
#define PARA_ID_CHG_TEMP_HIGH_ALM  16 // 充电高温告警阈值
#define PARA_ID_CHG_TEMP_HIGH_PRT  17 // 充电高温保护阈值
#define PARA_ID_DISCHG_TEMP_HIGH_ALM  18 // 放电高温告警阈值
#define PARA_ID_DISCHG_TEMP_HIGH_PRT  19 // 放电高温保护阈值
#define PARA_ID_CHG_TEMP_LOW_ALM  20 // 充电低温告警阈值
#define PARA_ID_CHG_TEMP_LOW_PRT  21 // 充电低温保护阈值
#define PARA_ID_DISCHG_TEMP_LOW_ALM  22 // 放电低温告警阈值
#define PARA_ID_DISCHG_TEMP_LOW_PRT  23 // 放电低温保护阈值
#define PARA_ID_CELL_POOR_CONSIS_ALM  24 // 单体一致性差告警阈值
#define PARA_ID_CELL_POOR_CONSIS_PRT  25 // 单体一致性差保护阈值
#define PARA_ID_BATT_SOC_LOW_ALM  26 // 电池SOC低告警阈值
#define PARA_ID_BATT_SOC_LOW_PRT  27 // 电池SOC低保护阈值
#define PARA_ID_BATT_SOH_ALM  28 // 电池SOH低告警阈值
#define PARA_ID_BATT_SOH_PRT  29 // 电池SOH低保护阈值
#define PARA_ID_CELL_DAMAGE_PRT  30 // 单体损坏保护阈值
#define PARA_ID_BATT_CHG_FULL_AVER_VOLT  31 // 电池充满电压,  使用单体值表示，其真实值应乘以电芯节数
#define PARA_ID_BATT_CHG_FULL_AVER_CURR  32 // 电池充满电流
#define PARA_ID_CHG_MAX_DURA  33 // 充电最长时间
#define PARA_ID_CHG_END_DURA  34 // 充电末期维持时间
#define PARA_ID_BATT_SUPPL_VOLT  35 // 电池补充电电压,  使用单体值表示，其真实值应乘以电芯节数
#define PARA_ID_BATT_SUPPL_PERIOD  36 // 电池补充电周期
#define PARA_ID_CELL_EQU_VOLT_DIFF  37 // 单体均衡启动压差阈值
#define PARA_ID_CHRG_MAX_CURR  38 // 充电最大电流
#define PARA_ID_SLEEP_EN  39 // 软关机使能
#define PARA_ID_HIS_DATA_INTER  40 // 历史数据保存间隔
#define PARA_ID_POWER_OFF_VOLT  41 // 系统停电电压阈值
#define PARA_ID_POWER_ON_VOLT  42 // 系统来电电压阈值
#define PARA_ID_BUZZER_ENABLE  43 // 蜂鸣器使能
#define PARA_ID_BATT_CAP  44 // 电池容量
#define PARA_ID_COMMON_LI_CHG_LIMIT_CURR  45 // 常规锂电充电限电流
#define PARA_ID_SOFT_ANTI_THEFT_DELAY  46 // 软件防盗延时
#define PARA_ID_BACKSTAGE_IP_ADDR  47 // 后台ip地址
#define PARA_ID_BACKSTAGE_PORT  48 // 后台端口
#define PARA_ID_GPRS_USER_NAME  49 // GPRS用户名
#define PARA_ID_GPRS_PAZWERD  50 // GPRS PW
#define PARA_ID_GPRS_APN  51 // GPRS APN
#define PARA_ID_ALM_PHONE_NUM_1  52 // 告警手机号码-1#
#define PARA_ID_ALM_PHONE_NUM_2  53 // 告警手机号码-2#
#define PARA_ID_ALM_PHONE_NUM_3  54 // 告警手机号码-3#
#define PARA_ID_ALM_PHONE_NUM_4  55 // 告警手机号码-4#
#define PARA_ID_ALM_PHONE_NUM_5  56 // 告警手机号码-5#
#define PARA_ID_SMS_CENTER_NUM  57 // 短信中心号码
#define PARA_ID_DEVICE_NAME  58 // 设备名称
#define PARA_ID_SWITCH_SOC  59 // 放电末期切换SOC1,  值随放电模式联动，恒压放电模式为0%，混用为10%
#define PARA_ID_RUN_MODE  60 // 运行模式
#define PARA_ID_USAGE_SCENARIO  61 // 放电模式
#define PARA_ID_CYCLE_MODE  62 // 循环模式
#define PARA_ID_LEAD_ACID_BATT_SWITCH_VOLT  63 // 铅酸切换电压
#define PARA_ID_REMOTE_SUPPLY_OUT_VOLT  64 // 恒压放电输出电压
#define PARA_ID_LI_ACID_DISCHG_RATE  65 // 锂电铅酸放电比率
#define PARA_ID_DOC_PER_DISCHG  66 // 单次放电DOD
#define PARA_ID_LEAD_ACID_BATT_DURA_DISCHG  67 // 铅酸单次放电时间
#define PARA_ID_BOOST_CHARGE  68 // 升压充电
#define PARA_ID_DISCHRG_MAX_CURR  69 // 放电最大电流
#define PARA_ID_POWER_DOWN_VOLT  70 // 掉电电压阈值
#define PARA_ID_OUTPUT_OFFSET_VOLT  71 // 输出电压偏差阈值
#define PARA_ID_CELL_UVP_DELAY  72 // 单体动态欠压保护
#define PARA_ID_RELAY  73 // 设置告警属性,  告警干接点
#define PARA_ID_ALARM_CLASS  74 // 告警级别,  告警级别
#define PARA_ID_ENABLE_DATE  75 // 启用日期
#define PARA_ID_MAJOR_BATT  76 // 主用电池
#define PARA_ID_VIBRATION_ALARM_ENABLE  77 // 振动告警使能
#define PARA_ID_GYROSCOPE_SENSITIVITY  78 // 陀螺仪灵敏度
#define PARA_ID_HEARTBEAT_CYCLE  79 // 心跳周期
#define PARA_ID_UVP_TEMP_COMPENSATION_EN  80 // 整组欠压保护温度补偿
#define PARA_ID_BMS_SYSTEM_NAME  81 // BMS系统名称
#define PARA_ID_RELAY_DEFAULT_STATUS  82 // 干接点默认状态
#define PARA_ID_DISCHARGE_MODE  83 // 放电方式
#define PARA_ID_SLEEP_INDICATOR  84 // 休眠指示灯
#define PARA_ID_THROUGH_ENABLE  85 // 放电直通使能
#define PARA_ID_SAVE_ENERGY_FUNCTION  86 // 节能功能
#define PARA_ID_ENV_TEMP_HIGH_PRT  87 // 环境温度高保护阈值
#define PARA_ID_ENV_TEMP_LOW_PRT  88 // 环境温度低保护阈值
#define PARA_ID_BATT_OVER_VOLT_ALM_RECO  89 // 电池组过压告警恢复阈值,  使用单体值表示，其真实值应乘以电芯节数
#define PARA_ID_BATT_OVER_VOLT_PRT_RECO  90 // 电池组过压保护恢复阈值,  参数实际未使用，范围不具有参考性。使用单体值表示，其真实值应乘以电芯节数
#define PARA_ID_CELL_OVER_VOLT_ALM_RECO  91 // 单体过压告警恢复阈值
#define PARA_ID_CELL_OVER_VOLT_PRT_RECO  92 // 单体过压保护恢复阈值
#define PARA_ID_CELL_UNDER_VOLT_ALM_RECO  93 // 单体欠压告警恢复阈值
#define PARA_ID_CELL_UNDER_VOLT_PRT_RECO  94 // 单体欠压保护恢复阈值
#define PARA_ID_CHG_TEMP_HIGH_ALM_RECO  95 // 充电高温告警恢复阈值
#define PARA_ID_CHG_TEMP_HIGH_PRT_RECO  96 // 充电高温保护恢复阈值
#define PARA_ID_DISCHG_TEMP_HIGH_ALM_RECO  97 // 放电高温告警恢复阈值
#define PARA_ID_DISCHG_TEMP_HIGH_PRT_RECO  98 // 放电高温保护恢复阈值
#define PARA_ID_CHG_TEMP_LOW_ALM_RECO  99 // 充电低温告警恢复阈值
#define PARA_ID_CHG_TEMP_LOW_PRT_RECO  100 // 充电低温保护恢复阈值
#define PARA_ID_DISCHG_TEMP_LOW_ALM_RECO  101 // 放电低温告警恢复阈值
#define PARA_ID_DISCHG_TEMP_LOW_PRT_RECO  102 // 放电低温保护恢复阈值
#define PARA_ID_ENV_TEMP_HIGH_ALM_RECO  103 // 环境温度高告警恢复阈值
#define PARA_ID_ENV_TEMP_HIGH_PRT_RECO  104 // 环境温度高保护恢复阈值
#define PARA_ID_ENV_TEMP_LOW_ALM_RECO  105 // 环境温度低告警恢复阈值
#define PARA_ID_ENV_TEMP_LOW_PRT_RECO  106 // 环境温度低保护恢复阈值
#define PARA_ID_BOARD_TEMP_HIGH_ALM_RECO  107 // 单板过温告警恢复阈值
#define PARA_ID_BOARD_TEMP_HIGH_PRT_RECO  108 // 单板过温保护恢复阈值
#define PARA_ID_REMOTE_SUPPLY_END_SWITCH_MODE  109 // 恒压末期放电切换方式
#define PARA_ID_REMOTE_SUPPLY_END_SWITCH_SOC  110 // 恒压末期放电切换SOC,  与放电末期切换SOC1统一，值随放电模式联动
#define PARA_ID_REMOTE_SUPPLY_END_SWITCH_VOLT  111 // 恒压末期放电切换电压
#define PARA_ID_GSP_ANTI_THEFT_DISTANCE  112 // GPS防盗距离
#define PARA_ID_GYROSCOPE_ANTI_THEFT_MODE  113 // 陀螺仪防盗方式
#define PARA_ID_BATT_UNLOCK_MODE  114 // 电池解锁方式
#define PARA_ID_CHARGE_ROTATE_ENABLE  115 // 充电轮换使能
#define PARA_ID_HIS_DATA_TYPE  116 // 历史数据类型
#define PARA_ID_CELL_CHG_CUTOFF_VOL  117 // 单体充电截止电压
#define PARA_ID_SHUTDOWN_TIME  118 // 静置关机时间
#define PARA_ID_BUSVOLT_CTIVE_EN  119 // 电压激活使能
#define PARA_ID_MODBUS_ADDR  120 // Modbus基地址
#define PARA_ID_HEATING_PAD_ENABLE  121 // 加热垫使能
#define PARA_ID_SNMP_TRAP_IP  122 // SNMP告警ip地址
#define PARA_ID_SNMP_READ_COMM  123 // SNMP可读共同体
#define PARA_ID_SNMP_SET_COMM  124 // SNMP设置共同体
#define PARA_ID_SNMP_LEVEL  125 // SNMP V3用户等级
#define PARA_ID_SNMP_NAME  126 // SNMP V3用户名
#define PARA_ID_SNMP_AUTH_PASS  127 // SNMP V3鉴别PW
#define PARA_ID_SNMP_PRIV_PASS  128 // SNMP V3加密PW
#define PARA_ID_SNMP_TRAP_PORT  129 // SNMP Trap端口号
#define PARA_ID_SNMP_AGENT_PORT  130 // SNMP Agent端口号
#define PARA_ID_LOCAL_IP  131 // 本机ip地址
#define PARA_ID_MASK  132 // 子网掩码
#define PARA_ID_GATEWAY  133 // 网关
#define PARA_ID_LOCAL_IP_GET  134 // 本机ip获取方式
#define PARA_ID_RETMOTE_UPDATE_IP_ADDR  135 // 远程升级后台ip地址
#define PARA_ID_REMOTE_UPDATE_PORT  136 // 远程升级端口
#define PARA_ID_CHG_HEATER_STARTUP_TEMP  137 // 充电加热膜启动温度
#define PARA_ID_CHG_HEATER_SHUTDOWN_TEMP  138 // 充电加热膜关闭温度
#define PARA_ID_HEATER_TEMP_HIGH_THRE  139 // 加热膜过温阈值
#define PARA_ID_HEATER_TEMP_HIGH_RELEASE  140 // 加热膜过温解除
#define PARA_ID_CURR_BALANCE_AMPLITUDE  141 // 均流SOC补偿幅值
#define PARA_ID_CURR_BALANCE_SLOPE  142 // 均流SOC补偿斜率
#define PARA_ID_CURR_BALANCE_METHOD  143 // 均流方式
#define PARA_ID_DISCHARGE_REMO_END_OUTPUT_VOLT_FIRST  144 // 末期放电电压1
#define PARA_ID_DISCHARGE_REMO_END_OUTPUT_VOLT_SECOND  145 // 末期放电电压2
#define PARA_ID_CELL_SUPPL_VOLT  146 // 单体补充电电压
#define PARA_ID_POWER_ON_DETERMINE_TIME  147 // 来电判断时间
#define PARA_ID_CHARGE_MAP_ENABLE  148 // 充电MAP使能
#define PARA_ID_SAG_EQUAL_CURR_EN  149 // 下垂均流使能
#define PARA_ID_SNMP_VERSION  150 // SNMP版本
#define PARA_ID_SNMP_AUTHENTICATION_ALGO  151 // SNMP V3认证算法
#define PARA_ID_SNMP_PRIVACY_ALGO  152 // SNMP V3加密算法
#define PARA_ID_PREFAULT_RECORDING_TIME_INTERVAL  153 // 故障前录波时间间隔
#define PARA_ID_POSTFAULT_RECORDING_TIME_INTERVAL  154 // 故障后录波时间间隔
#define PARA_ID_PREFAULT_RECORDING_NUMBER  155 // 故障前录波条数
#define PARA_ID_POSTFAULT_RECORDING_NUMBER  156 // 故障后录波条数
#define PARA_ID_MEASUREMENT_POINTS_NUMBER  157 // 测点数量X
#define PARA_ID_VARIABLE_MEASUREMENT_POINT_ID  158 // 可变测点ID,  备注：业务未使用参数用红色字体标出，蓝色字体为同步R311参数暂时未使用参数，黄色字体表示相较于FB100B3新增约束关系
#define PARA_ID_BATT_ADDRESS_MODE  159 // 电池地址获取方式
#define PARA_ID_BATT_SWITCH_ADDRESS  160 // 电池切换地址
#define PARA_ID_DCR_FAULT_ALM_THRE  161 // 直流内阻异常告警阈值
#define PARA_ID_DCR_FAULT_PRT_THRE  162 // 直流内阻异常保护阈值
#define PARA_ID_CELL_TEMP_RISE_ABNORMAL  163 // 单体温升速率异常阈值
#define PARA_ID_SELFDISCHG_ACR  164 // 自放电容量比率
#define PARA_ID_CAP_DCDR  165 // 容量衰减一致性差告警比率阈值
#define PARA_ID_MAX_CHARGE_POWER  166 // 最大充电功率
#define PARA_ID_MAX_DISCHARGE_POWER  167 // 最大放电功率
#define PARA_ID_LOCATE_MODE  168 // 定位方式
#define PARA_ID_CAN2_ADDRESS_MODE  169 // 柜间地址获取方式
#define PARA_ID_CAN2_SWITCH_ADDRESS  170 // 柜间切换地址
#define PARA_ID_BATT_FAULT_TEMP_HIGH_PRT  171 // 电池异常温度高保护阈值
#define PARA_ID_RELAY_SYNCHRONIZE  172 // 并机干接点同步使能
#define PARA_ID_SITE_ANTITHEFT_DELAY_TIME  173 // 站点防盗延时时间
#define PARA_ID_NTP_IP  174 // NTP服务器IP
#define PARA_ID_TIME_ZONE  175 // 时区
#define PARA_ID_AUTO_TIMEING_ENABLE  176 // 网络自动对时使能
#define PARA_ID_NET_ANTITHEFT_DELAY_TIME  177 // 网管防盗延时时间
#define PARA_ID_CRITICAL_ALARM_INDICATOR  178 // 严重告警指示灯
#define PARA_ID_SECONDARY_ALARM_INDICATOR  179 // 次要告警指示灯
#define PARA_ID_FIRE_RELAY_CONTROL_ENABLE  180 // 消防干接点控制使能
#define PARA_ID_CHARGE_DISCHARGE_MACHINE_TEST_MODEE  181 // 充放电机模式
#define PARA_ID_DEYE_FULL_VOLT  182 // 德业逆变器充满电压
#define PARA_ID_DEYE_UNDER_VOLT  183 // 德业逆变器放电下限电压
#define PARA_ID_CELL_TYPE  184 // 电芯类型
#define PARA_ID_SWITCH_SOC2  185 // 放电末期切换SOC2,  值随放电模式联动，恒压放电模式为0%，混用为10%
#define PARA_ID_DISCHG_HEATER_STARTUP_TEMP  186 // 放电加热膜启动温度
#define PARA_ID_DISCHG_HEATER_SHUTDOWN_TEMP  187 // 放电加热膜关闭温度
#define PARA_ID_AUTO_LOCAL_REFRESH_PERIOD  188 // 自动位置刷新周期
#define PARA_ID_GYRO_ANTI_THEFT_TIME  189 // 陀螺仪防盗延时
#define PARA_ID_NTC_INVALID_SHIELD_NUM  190 // NTC无效屏蔽路数
#define PARA_ID_SELFADAPTION_INIT_VOLT  191 // 自适应模式初始电压
#define PARA_ID_WATER_INGRNESS_ENABLE  192 // 防水检测使能

/// 参数结构体
typedef	struct
{
	FLOAT  fChgCurrHighAlmThre; // 充电过流告警阈值(C3)
	FLOAT  fChgCurrHighPrtThre; // 充电过流保护阈值(C3)
	FLOAT  fDischgCurrHighAlmThre; // 放电过流告警阈值(C3)
	FLOAT  fDischgCurrHighPrtThre; // 放电过流保护阈值(C3)
	FLOAT  fBattOverVoltAlmThre; // 电池组过压告警阈值(V),  使用单体值表示，其真实值应乘以电芯节数
	FLOAT  fBattOverVoltPrtThre; // 电池组过压保护阈值(V),  使用单体值表示，其真实值应乘以电芯节数
	FLOAT  fBoardTempHighPrtThre; // 单板过温保护阈值(℃)
	FLOAT  fBoardTempHighcThre; // 单板过温告警阈值(℃)
	FLOAT  fEnvTempHighAlmThre; // 环境温度高告警阈值(℃)
	FLOAT  fEnvTempLowAlmThre; // 环境温度低告警阈值(℃)
	FLOAT  fBattUnderVoltAlmThre; // 电池组欠压告警阈值(V),  使用单体值表示，其真实值应乘以电芯节数
	FLOAT  fBattUnderVoltPrtThre; // 电池组欠压保护阈值(V),  使用单体值表示，其真实值应乘以电芯节数
	FLOAT  fCellOverVoltAlmThre; // 单体过压告警阈值(V)
	FLOAT  fCellOverVoltPrtThre; // 单体过压保护阈值(V)
	FLOAT  fCellUnderVoltAlmThre; // 单体欠压告警阈值(V)
	FLOAT  fCellUnderVoltPrtThre; // 单体欠压保护阈值(V)
	FLOAT  fChgTempHighAlmThre; // 充电高温告警阈值(℃)
	FLOAT  fChgTempHighPrtThre; // 充电高温保护阈值(℃)
	FLOAT  fDischgTempHighAlmThre; // 放电高温告警阈值(℃)
	FLOAT  fDischgTempHighPrtThre; // 放电高温保护阈值(℃)
	FLOAT  fChgTempLowAlmThre; // 充电低温告警阈值(℃)
	FLOAT  fChgTempLowPrtThre; // 充电低温保护阈值(℃)
	FLOAT  fDischgTempLowAlmThre; // 放电低温告警阈值(℃)
	FLOAT  fDischgTempLowPrtThre; // 放电低温保护阈值(℃)
	FLOAT  fCellPoorConsisAlmThre; // 单体一致性差告警阈值(V)
	FLOAT  fCellPoorConsisPrtThre; // 单体一致性差保护阈值(V)
	WORD  wBattSOCLowAlmThre; // 电池SOC低告警阈值(%)
	WORD  wBattSOCLowPrtThre; // 电池SOC低保护阈值(%)
	WORD  wBattSOHAlmThre; // 电池SOH低告警阈值(%)
	WORD  wBattSOHPrtThre; // 电池SOH低保护阈值(%)
	FLOAT  fCellDamagePrtThre; // 单体损坏保护阈值(V)
	FLOAT  fBattChgFullAverVoltThre; // 电池充满电压(V),  使用单体值表示，其真实值应乘以电芯节数
	FLOAT  fBattChgFullAverCurrThre; // 电池充满电流(C3)
	WORD  wChgMaxDura; // 充电最长时间(min)
	WORD  wChgEndDura; // 充电末期维持时间(min)
	FLOAT  fBattSupplVolt; // 电池补充电电压(V),  使用单体值表示，其真实值应乘以电芯节数
	WORD  wBattSupplPeriod; // 电池补充电周期(day)
	FLOAT  fCellEquVoltDiffThre; // 单体均衡启动压差阈值(V)
	FLOAT  fChargeMaxCurr; // 充电最大电流(C3)
	BOOLEAN  bSleepEn; // 软关机使能
	WORD  wHisDataInter; // 历史数据保存间隔(Min)
	FLOAT  fPowerOffVoltThre; // 系统停电电压阈值(V)
	FLOAT  fPowerOnVoltThre; // 系统来电电压阈值(V)
	BOOLEAN  bBuzzerEnable; // 蜂鸣器使能
	WORD  wBatteryCap; // 电池容量(AH)
	FLOAT  fCommonLiLimitCurr; // 常规锂电充电限电流(A)
	WORD  wSoftAntiTheftDelay; // 软件防盗延时(Min)
	BYTE  acBackstageIpAddr[LEN_TYPE_STRING_20]; // 后台ip地址
	WORD  wBackstagePort; // 后台端口
	BYTE  acGPGSUserName[LEN_TYPE_STRING_20]; // GPRS用户名
	BYTE  acGPGSPazwerd[LEN_TYPE_STRING_10]; // GPRS PW
	BYTE  acGPGSAPN[LEN_TYPE_STRING_20]; // GPRS APN
	BYTE  acAlmPhoneNum_1[LEN_TYPE_STRING_20]; // 告警手机号码-1#
	BYTE  acAlmPhoneNum_2[LEN_TYPE_STRING_20]; // 告警手机号码-2#
	BYTE  acAlmPhoneNum_3[LEN_TYPE_STRING_20]; // 告警手机号码-3#
	BYTE  acAlmPhoneNum_4[LEN_TYPE_STRING_20]; // 告警手机号码-4#
	BYTE  acAlmPhoneNum_5[LEN_TYPE_STRING_20]; // 告警手机号码-5#
	BYTE  acSMSCenterNum[LEN_TYPE_STRING_20]; // 短信中心号码
	BYTE  acDeviceName[LEN_TYPE_STRING_32]; // 设备名称
	WORD  wDischgSwitchSOC; // 放电末期切换SOC1,  值随放电模式联动，恒压放电模式为0%，混用为10%
	BYTE  ucRunMode; // 运行模式
	BYTE  ucUsageScen; // 放电模式
	BYTE  ucCycleMode; // 循环模式
	FLOAT  fLABattSwitchVolt; // 铅酸切换电压(V)
	FLOAT  fRemoteSupplyOutVolt; // 恒压放电输出电压(V)
	BYTE  ucLiAcidDischargeRate; // 锂电铅酸放电比率(:1)
	BYTE  ucDODPerDischarge; // 单次放电DOD(%)
	WORD  wLABattDurationPerDischarge; // 铅酸单次放电时间(Min)
	BOOLEAN  bBoostCharge; // 升压充电
	FLOAT  fDischgMaxCurr; // 放电最大电流(C3)
	FLOAT  fPowerDownVoltThre; // 掉电电压阈值(V)
	FLOAT  fOutputOffsetVoltThre; // 输出电压偏差阈值(V)
	BYTE  ucCellUVPDelay; // 单体动态欠压保护
	BYTE  aucRelayBit[ALARM_CLASS]; // 设置告警属性,  告警干接点
	BYTE  aucAlarmLevel[ALARM_CLASS]; // 告警级别,  告警级别
	T_DateStruct  tEnableTime; // 启用日期
	BOOLEAN  bMajorBatt; // 主用电池
	BYTE  bVibrationAlarmEn; // 振动告警使能
	BYTE  bGyroscopeSensitivity; // 陀螺仪灵敏度
	WORD  wHeartbeatCycle; // 心跳周期(Min)
	BYTE  bUVPTempCompensationEn; // 整组欠压保护温度补偿
	BYTE  acBMSSysName[LEN_TYPE_STRING_20]; // BMS系统名称
	BYTE  ucRelayDefaultStatus; // 干接点默认状态
	BYTE  ucDischargeMode; // 放电方式
	BYTE  ucSleepIndicator; // 休眠指示灯
	BOOLEAN  bThroughDischgEnable; // 放电直通使能
	BOOLEAN  bSaveEnergy; // 节能功能
	FLOAT  fEnvTempHighPrtThre; // 环境温度高保护阈值(℃)
	FLOAT  fEnvTempLowPrtThre; // 环境温度低保护阈值(℃)
	FLOAT  fBattOverVoltAlmRecoThre; // 电池组过压告警恢复阈值(V),  使用单体值表示，其真实值应乘以电芯节数
	FLOAT  fBattOverVoltPrtRecoThre; // 电池组过压保护恢复阈值(V),  参数实际未使用，范围不具有参考性。使用单体值表示，其真实值应乘以电芯节数
	FLOAT  fCellOverVoltAlmRecoThre; // 单体过压告警恢复阈值(V)
	FLOAT  fCellOverVoltPrtRecoThre; // 单体过压保护恢复阈值(V)
	FLOAT  fCellUnderVoltAlmRecoThre; // 单体欠压告警恢复阈值(V)
	FLOAT  fCellUnderVoltPrtRecoThre; // 单体欠压保护恢复阈值(V)
	FLOAT  fChgTempHighAlmRecoThre; // 充电高温告警恢复阈值(℃)
	FLOAT  fChgTempHighPrtRecoThre; // 充电高温保护恢复阈值(℃)
	FLOAT  fDischgTempHighAlmRecoThre; // 放电高温告警恢复阈值(℃)
	FLOAT  fDischgTempHighPrtRecoThre; // 放电高温保护恢复阈值(℃)
	FLOAT  fChgTempLowAlmRecoThre; // 充电低温告警恢复阈值(℃)
	FLOAT  fChgTempLowPrtRecoThre; // 充电低温保护恢复阈值(℃)
	FLOAT  fDischgTempLowAlmRecoThre; // 放电低温告警恢复阈值(℃)
	FLOAT  fDischgTempLowPrtRecoThre; // 放电低温保护恢复阈值(℃)
	FLOAT  fEnvTempHighAlmRecoThre; // 环境温度高告警恢复阈值(℃)
	FLOAT  fEnvTempHighPrtRecoThre; // 环境温度高保护恢复阈值(℃)
	FLOAT  fEnvTempLowAlmRecoThre; // 环境温度低告警恢复阈值(℃)
	FLOAT  fEnvTempLowPrtRecoThre; // 环境温度低保护恢复阈值(℃)
	FLOAT  fBoardTempHighAlmRecoThre; // 单板过温告警恢复阈值(℃)
	FLOAT  fBoardTempHighPrtRecoThre; // 单板过温保护恢复阈值(℃)
	BYTE  bRemoteSupplyEndDischgSwitchMode; // 恒压末期放电切换方式
	BYTE  bRemoteSupplyEndDischgSwitchSOC; // 恒压末期放电切换SOC,  与放电末期切换SOC1统一，值随放电模式联动
	FLOAT  fRemoteSupplyEndDischgSwitchVolt; // 恒压末期放电切换电压(V)
	WORD  wGPSAntiTheftDistance; // GPS防盗距离(m)
	BYTE  ucGyroAntiTheftMode; // 陀螺仪防盗方式
	BYTE  ucBattUnlockMode; // 电池解锁方式
	BOOLEAN  bChargeRotate; // 充电轮换使能
	BYTE  ucHisDataType; // 历史数据类型
	FLOAT  fCellChargeFullVolt; // 单体充电截止电压(V)
	WORD  wBattShutdownTime; // 静置关机时间(min)
	BOOLEAN  bBusVoltActiveEn; // 电压激活使能
	BYTE  ucModbusAddr; // Modbus基地址
	BOOLEAN  bHeatingPadEnable; // 加热垫使能
	BYTE  acSNMPTrapIP[LEN_TYPE_STRING_16]; // SNMP告警ip地址
	BYTE  acSNMPReadCommunity[LEN_TYPE_STRING_16]; // SNMP可读共同体
	BYTE  acSNMPSetCommunity[LEN_TYPE_STRING_16]; // SNMP设置共同体
	BYTE  ucSNMPV3UserLevel; // SNMP V3用户等级
	BYTE  acSNMPV3UserName[LEN_TYPE_STRING_16]; // SNMP V3用户名
	BYTE  acSNMPV3AuthPass[LEN_TYPE_STRING_16]; // SNMP V3鉴别PW
	BYTE  acSNMPV3PrivPass[LEN_TYPE_STRING_16]; // SNMP V3加密PW
	WORD  wSNMPTrapPort; // SNMP Trap端口号
	WORD  wSNMPAgentPort; // SNMP Agent端口号
	BYTE  acLocalIPAddr[LEN_TYPE_STRING_16]; // 本机ip地址
	BYTE  acMask[LEN_TYPE_STRING_16]; // 子网掩码
	BYTE  acGateway[LEN_TYPE_STRING_16]; // 网关
	BYTE  ucLocalIPGetMode; // 本机ip获取方式
	BYTE  acRemoteUpdateIpAddr[LEN_TYPE_STRING_16]; // 远程升级后台ip地址
	WORD  wRemoteUpdatePort; // 远程升级端口
	FLOAT  fChgHeaterStartupTemp; // 充电加热膜启动温度(℃)
	FLOAT  fChgHeaterShutdownTemp; // 充电加热膜关闭温度(℃)
	FLOAT  fHeaterTempHighThre; // 加热膜过温阈值(℃)
	FLOAT  fHeaterTempHighRel; // 加热膜过温解除(℃)
	BYTE  ucCurrBalanceAmplitude; // 均流SOC补偿幅值
	BYTE  ucCurrBalanceSlope; // 均流SOC补偿斜率
	BYTE  ucCurrBalanceMethod; // 均流方式
	FLOAT  fDischargeEndVolt1; // 末期放电电压1(V)
	FLOAT  fDischargeEndVolt2; // 末期放电电压2(V)
	FLOAT  fCellSupplVolt; // 单体补充电电压(V)
	WORD  wPowerOnDetermineTime; // 来电判断时间(s)
	BOOLEAN  bChargeMapEnable; // 充电MAP使能
	BOOLEAN  bSagEqualCurr; // 下垂均流使能
	BYTE  ucSNMPVersion; // SNMP版本
	BYTE  ucSNMPAuthenticationAlgo; // SNMP V3认证算法
	BYTE  ucSNMPPrivacyAlgo; // SNMP V3加密算法
	WORD  wPreRecordInterval; // 故障前录波时间间隔(us)
	WORD  wPostRecordInterval; // 故障后录波时间间隔(us)
	BYTE  ucPreRecordNum; // 故障前录波条数
	BYTE  ucPostRecordNum; // 故障后录波条数
	BYTE  ucMeasurePointsNum; // 测点数量X
	BYTE  ucMeasurePointsID[NUM_OF_MEASUREPOINTS]; // 可变测点ID,  备注：业务未使用参数用红色字体标出，蓝色字体为同步R311参数暂时未使用参数，黄色字体表示相较于FB100B3新增约束关系
	BYTE  ucBattAddressMode; // 电池地址获取方式
	BYTE  ucBattSwitchAddr; // 电池切换地址
	BYTE  ucDcrFaultAlmThre; // 直流内阻异常告警阈值
	BYTE  ucDcrFaultPrtThre; // 直流内阻异常保护阈值
	FLOAT  fCellTempRiseAbnormalThre; // 单体温升速率异常阈值(℃)
	FLOAT  fSelfDischgACR; // 自放电容量比率(%)
	FLOAT  fCapDCPRFaultAlmThre; // 容量衰减一致性差告警比率阈值(%)
	WORD  wChargeMaxPower; // 最大充电功率
	WORD  wDischargeMaxPower; // 最大放电功率
	BYTE  ucLocateMode; // 定位方式
	BYTE  ucCAN2AddressMode; // 柜间地址获取方式
	BYTE  ucCAN2SwitchAddr; // 柜间切换地址
	FLOAT  fBattFaultTempHighPrtThre; // 电池异常温度高保护阈值(℃)
	BOOLEAN  bRelaySyncEn; // 并机干接点同步使能
	WORD  wSiteAntiTheftDelayTime; // 站点防盗延时时间(min)
	BYTE  acNtpIPAddr[LEN_TYPE_STRING_16]; // NTP服务器IP
	BYTE  ucTimeZone; // 时区
	BOOLEAN  bAutoTimingEn; // 网络自动对时使能
	WORD  wNetAntiTheftDelayTime; // 网管防盗延时时间(min)
	BYTE  ucCriticalAlarmlight; // 严重告警指示灯
	BYTE  ucSecondaryAlarmlight; // 次要告警指示灯
	BYTE  ucFireRelayControlEnable; // 消防干接点控制使能
	BYTE  ucChargeMachineTestMode; // 充放电机模式
	FLOAT  fDeyeFullVolt; // 德业逆变器充满电压(V)
	FLOAT  fDeyeUnderVolt; // 德业逆变器放电下限电压(V)
	BYTE  ucCellType; // 电芯类型
	WORD  wDischgSwitchSOC2; // 放电末期切换SOC2,  值随放电模式联动，恒压放电模式为0%，混用为10%
	FLOAT  fDischgHeaterStartupTemp; // 放电加热膜启动温度(℃)
	FLOAT  fDischgHeaterShutdownTemp; // 放电加热膜关闭温度(℃)
	BYTE  ucAutoLocationRefreshPeriod; // 自动位置刷新周期(s)
	BYTE  ucGyroAntiTheftTime; // 陀螺仪防盗延时(min)
	BYTE  ucNTCInvalidShieldNum; // NTC无效屏蔽路数
	FLOAT  fSelfAdaptionInitVolt; // 自适应模式初始电压(V)
	BYTE  ucWaterIngrnessEn; // 防水检测使能
} T_SysPara;

#ifdef __cplusplus
} /* end of the extern "C" block */
#endif

#endif

