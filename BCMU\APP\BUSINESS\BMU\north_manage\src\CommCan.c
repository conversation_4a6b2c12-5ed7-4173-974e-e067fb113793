/**************************************************************************
* Copyright (C) 2010-2011, ZTE Corporation.
* 版权信息：（C）2013，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：ZXDUx8 CTL板软件
* 文件名称：commcan.c
* 文件说明：CAN通讯模块
* 作    者  ：lc
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/
#pragma pack(1)
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <rtthread.h>
#include "sps.h"
#include "msg.h"
#include "msg_id.h"
#include "pin_define.h"
#include "his_data.h"
#include "CommCan.h"
#include "utils_data_transmission.h"
#include "version.h"
#include "para_manage.h"
#include "cell_balance.h"
#include "time_sync.h"
#include "dev_bcmu.h"
#include "utils_string.h"
#include "flash.h"
#include "realdata_id_in.h"
#include "para_id_in.h"
#include "concurrent_upgrade.h"
#include "protocol_bottom_comm.h"
#include "realdata_save.h"
#include "utils_server.h"
#include "utils_time.h"
#include "drv_mt9805.h"
#include "sample_new.h"

#define INIT_HOST_ADDR    -1
#define INIT_ASSIGN_TYPE  0
#define INVALID_ADDR 0XFF
#define PIN_LOW 0x00
#define time_wait_tick  10
#define TIME_SYNC_MAX_NUM  20
#define BMU_NORTH_TIME_DELAY  1   // unit ms
#define BMU_TIME_SYNC_PERIOD  1000  // unit ms
#define BMU_RESYNC_INTERVAL   (6 * 3600 * 1000)  // unit s
#define MAX_GET_ONE_DATA_LEN   7   ///< max is sizeof(time_base_t), for critical kw ABV.GENERAL
#define ODD_BIT_BALANCE  1 // 奇数电芯均衡
#define EVEN_BIT_BALANCE 0 // 偶数电芯均衡

Static struct mt9805_data s_cell_balance_mt9805[SAMPLE_CHIP_NUM] = {0}; // 用于控制mt9805芯片的电芯均衡
Static char s_odd_even_trans = ODD_BIT_BALANCE; // 用于控制电芯均衡奇偶切换，0：偶数电芯均衡；1：奇数电芯均衡

static msg_map north_msg_map[] =
{
     {0,NULL},
};

typedef struct {
    unsigned char fire_alarm_protect_flag;
} emergency_stop_flag_t;

Static struct rt_can_filter_item can_filter_item[CAN1_HEAD_FILTER_NUM] = {
    RT_CAN_FILTER_ITEM_INIT(0x10000000, 1, 0, 1, 0x10000000), /* std,match ID:0x100~0x1ff，hdr 为 - 1，设置默认过滤表 */
    RT_CAN_FILTER_ITEM_INIT(0x10000000, 1, 0, 1, 0x10000000),
};

Static void bmu_process_switch(void* dev_instance, bmu_north_mgr_t* north_mgr);
Static void bmu_qtp_get_process_switch(void* dev_instance, bmu_north_mgr_t* north_mgr);
Static void bmu_qtp_set_process_switch(void* dev_instance, bmu_north_mgr_t* north_mgr);
static bmu_north_mgr_t* init_thread_data(thread_info_t* north_thread_info, link_inst_t* link_inst, unsigned char mod_id);
Static void process_data_sample(void* dev_instance, cmd_buf_t* cmd_buff);
Static int broad_addr_competition(void* dev_instance, cmd_buf_t* cmd_buff);
Static int broad_opt_addr_competition(void* dev_instance, cmd_buf_t* cmd_buff);
Static int addr_competition_prepare(void* dev_instance, cmd_buf_t* cmd_buff);
Static int addr_allocation(void* dev_instance, cmd_buf_t* cmd_buff);
Static int init_address(void);
Static int reset_can_filter(link_inst_t* link_inst);

Static short bmu_ctrl_reset(void* dev_instance, cmd_buf_t* cmd_buff);
Static short control_cell_balance(void* dev_instance, cmd_buf_t* cmd_buff);
Static short control_opt_power_status(void* dev_instance, cmd_buf_t* cmd_buff);
Static short cell_balance_map(unsigned short *input, unsigned short *output);
Static short cell_balance_odd_even_trans(unsigned short *chip_balance_control);
Static unsigned short reverse_bits_short(unsigned short input_value);
Static rt_err_t cell_balance_excute(unsigned short *chip_balance_control);
Static short set_cell_balance_status(unsigned short *cell_balance_status);

Static int deal_qtp_trigger_end(void* dev_inst, void* cmd_buf);
Static unsigned char is_para_need_save(cmd_buf_t* cmd_buff);

static unsigned char localhost_addr = 0;//INVALID_ADDR;
static unsigned char s_qtp_status = 0;
Static dev_inst_t *dev_BCMU = NULL;

static unsigned char time_sync_state = FALSE;
static time_sync_para_t time_sync_para = {
    BMU_TIME_SYNC_PERIOD,
    BMU_NORTH_TIME_DELAY,
    TIME_SYNC_MAX_NUM,
    BMU_TIME_SYNC,
    BMU_RESYNC_INTERVAL,
    {MOD_BMU_NORTH, MOD_ALARM_MANAGE, DEVICE_SAMPLE_MSG, NULL},
    NULL,
    &time_sync_state
};

static cmd_handle_register_t s_cmd_handle[] = {
    {DEV_BCMU,  BMU_QTP_TRIGGER,     CMD_TYPE_NO_POLL, NULL, deal_qtp_trigger_end},
};

unsigned char read_localhost_addr(void){
    return localhost_addr;
}

dev_inst_t* read_dev_inst(void) {
    return dev_BCMU;
}

Static void bmu_process_switch(void* dev_instance, bmu_north_mgr_t* north_mgr) {
    switch(north_mgr->cmd_buff->cmd->cmd_id){
        case BMU_ADDR_ALLOCATION_PREPARE:
            addr_competition_prepare(dev_instance, north_mgr->cmd_buff);
            break;
        case BMU_ADDR_ALLOCATION:
            addr_allocation(dev_instance, north_mgr->cmd_buff);
            break;
        case BMU_GET_ANA_DATA:
            process_data_sample(dev_instance, north_mgr->cmd_buff);
            break;
        case BMU_GET_SWITCH_DATA:
            process_data_sample(dev_instance, north_mgr->cmd_buff);
            break;
        case BMU_GET_ALARM_DATA:
            process_data_sample(dev_instance, north_mgr->cmd_buff);
            break;
        case BMU_GET_MANUFACTOR_DATA:
        case BMU_SYNC_SOH_DATA:
            cmd_send(dev_instance, north_mgr->cmd_buff);
            break;
        case BMU_CTRL_CELL_BALANCE:
            control_cell_balance(dev_instance, north_mgr->cmd_buff);
            break;
        case BMU_CTRL_RESET:
            bmu_ctrl_reset(dev_instance, north_mgr->cmd_buff);
            break;
        // BCMU下发的广播序列号命令，无需回包。
        case BMU_CONTROL_OPT_SHUTDOWN:
            control_opt_power_status(dev_instance, north_mgr->cmd_buff);
            break;
        case BMU_SYNC_SERIAL_NUM:
            break;
        default :
            bmu_qtp_get_process_switch(dev_instance, north_mgr);
            break;
    }
    return;
};

Static void bmu_qtp_get_process_switch(void* dev_instance, bmu_north_mgr_t* north_mgr) {

    switch(north_mgr->cmd_buff->cmd->cmd_id){
        case BMU_QTP_TRIGGER:
            if (s_qtp_status < TRIGGER_TIMES)
            {
                s_qtp_status++;
            }
            cmd_send(dev_instance, north_mgr->cmd_buff);
            break;
        case BMU_QTP_EXIT:
            s_qtp_status = 0;
            cmd_send(dev_instance, north_mgr->cmd_buff);
            break;
        case BMU_QTP_GET_ANA_DATA:
        case BMU_QTP_GET_SWITCH_DATA:
        case BMU_QTP_GET_ALARM_DATA:
        case BMU_QTP_GET_SOFTWARE_DATA:
        case BMU_QTP_GET_MANUFACTOR_DATA:
            if(s_qtp_status != TRIGGER_TIMES)
            {
                break;
            }
            cmd_send(dev_instance, north_mgr->cmd_buff);
            break;
        default :
            bmu_qtp_set_process_switch(dev_instance, north_mgr);
            break;
    }
    return;
};

Static void bmu_qtp_set_process_switch(void* dev_instance, bmu_north_mgr_t* north_mgr) {

    switch(north_mgr->cmd_buff->cmd->cmd_id){
        case BMU_QTP_SET_DEVICE_SN:
        case BMU_QTP_SET_CELL_MANU_FACTRUER:
        case BMU_QTP_SET_BATT_DATE_TIME:
        case BMU_QTP_SET_CELL_CYCLE_TIMES:
        case BMU_QTP_SET_PACK_BAR_CODE:
        case BMU_QTP_SET_SYS_NAME:
        case BMU_QTP_SET_HARDWARE_VERSION:
            if(s_qtp_status != TRIGGER_TIMES)
            {
                break;
            }
            cmd_send(dev_instance, north_mgr->cmd_buff);
            break;
        default :
            cmd_send(dev_instance, north_mgr->cmd_buff);
            break;
    }
    return;
};

static thread_info_t thread_north_group[] = {
    {"bmu_north", NULL, north_thread, RT_NULL, 1024, THREAD_PRIO_LOW, THREAD_TICK},
};

int register_proto_cmd_tab()
{
    concur_update_init();//并发升级初始化_协议命令表的注册
    return SUCCESSFUL;
}


/* 北向初始化，起线程 */
void* init_can_comm(void *param) {
    bmu_north_mgr_t* north_mgr = NULL;
    server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(north_msg_map) / sizeof(msg_map);
    register_server_msg_map(north_msg_map, server_info);
    dev_BCMU = init_dev_inst(DEV_BCMU);
    RETURN_VAL_IF_FAIL(dev_BCMU != NULL, NULL);
    time_sync_para.dev_inst = dev_BCMU;
    init_address();
    register_proto_cmd_tab();//注册协议命令表

    cell_balance_process_start();
    north_mgr = init_thread_data(&thread_north_group[0], dev_BCMU->dev_type->link_inst, MOD_BMU_NORTH);
    if(NULL == north_mgr){
        return NULL;
    }
    for(int i = 0; i < sizeof(s_cmd_handle)/sizeof(s_cmd_handle[0]); i++) {
        register_cmd_handle(&s_cmd_handle[i]);
    }
    return north_mgr;
}

static bmu_north_mgr_t* init_thread_data(thread_info_t* north_thread_info, link_inst_t* link_inst, unsigned char mod_id) {
    bmu_north_mgr_t* north_mgr = NULL;

    north_mgr = malloc(sizeof(bmu_north_mgr_t));
    if (north_mgr == NULL)
        return NULL;

    rt_memset_s(north_mgr, sizeof(bmu_north_mgr_t), 0x00, sizeof(bmu_north_mgr_t));
    north_mgr->cmd_buff = malloc(sizeof(cmd_buf_t));

    if (north_mgr->cmd_buff == NULL) {
        free(north_mgr);
        return NULL;
    }

    rt_memset_s(north_mgr->cmd_buff, sizeof(cmd_buf_t), 0x00, sizeof(cmd_buf_t));

    north_mgr->north_mq = init_msg_queue(mod_id);
    if (north_mgr->north_mq == NULL) {
        free(north_mgr->cmd_buff);
        free(north_mgr);
        return NULL;
    }

    north_mgr->link_inst = link_inst;
    north_thread_info->thread_data = north_mgr;
    return north_mgr;
}

Static unsigned char is_para_need_save(cmd_buf_t* cmd_buff)
{
    static const int table[] = {
        BMU_QTP_SET_SYS_NAME,
        BMU_QTP_SET_HARDWARE_VERSION,
        BMU_QTP_SET_PACK_BAR_CODE,
        BMU_QTP_SET_CELL_MANU_FACTRUER,
        BMU_QTP_SET_DEVICE_SN,
        BMU_QTP_SET_BATT_DATE_TIME,
        BMU_QTP_SET_CELL_CYCLE_TIMES,
        BMU_QTP_SET_CELL_SN,
        BMU_SYNC_SERIAL_NUM,
        BMU_SYNC_SOH_DATA,
    };

    for (int i = 0; i < sizeof(table) / sizeof(table[0]); ++i)
    {
        if (cmd_buff->cmd->cmd_id == table[i])
            return 1;
    }
    return 0;
}

void north_thread(void *param) {
    module_msg_t msg;
    bmu_north_mgr_t* north_mgr = (bmu_north_mgr_t*)param;
    unsigned short chip_balance_control[SAMPLE_CHIP_NUM] = {0};
    unsigned short comm_failed_count = 0; // 通信断延时，用于控制电芯均衡关闭

    while (is_running(TRUE)) {
        if(RT_EOK == rt_sem_take(&(north_mgr->link_inst->rx_sem), time_wait_tick)) {
            if (SUCCESSFUL == cmd_recv(dev_BCMU, north_mgr->cmd_buff)) {
                comm_failed_count = 0;
                bmu_process_switch(dev_BCMU, north_mgr);
                if(1 == is_para_need_save(north_mgr->cmd_buff))
                {
                    save_numeric_para();
                    save_string_para();
                }
            }
            else {
                comm_failed_count++; // 收包解析不成功也认为是通信断
            }
        }
        else {
            comm_failed_count++; // 没有收包则说明通信断通信断
        }

        if(comm_failed_count > 500) { // 如果与北向BCMU通信断时间超过5s，则控制所有电芯停止均衡
            comm_failed_count = 0;
            cell_balance_excute(chip_balance_control);
        }
        check_restart_delay(); // 检查并发升级重启延迟
        rt_thread_mdelay(BMU_NORTH_TIME_DELAY);
    }
}

Static void process_data_sample(void* dev_instance, cmd_buf_t* cmd_buff) {
    RETURN_IF_FAIL(dev_instance != NULL && cmd_buff != NULL);
    cmd_send((dev_inst_t*)dev_instance, cmd_buff);
    return ;
}

Static int reset_can_filter(link_inst_t* link_inst)
{
    RETURN_VAL_IF_FAIL(link_inst != NULL, FAILURE);

    link_can_filter can_filter_table;
    can_header_t filter_head[CAN1_HEAD_FILTER_NUM];

    rt_memset_s(filter_head, sizeof(filter_head), 0, sizeof(filter_head));

    filter_head[0].d_addr = 0; // 广播
    filter_head[1].d_addr = read_localhost_addr(); // 本机地址
    for (unsigned char i = 0; i < CAN1_HEAD_FILTER_NUM; i++)
    {
        filter_head[i].d_dev = BOTTOM_BMU_TYPE;
        can_filter_item[i].id = filter_head[i].d_addr + (filter_head[i].d_dev << 7);
        can_filter_item[i].mask = 0x3FFF; // ID掩码：0 表示对应的位不关心，1 表示对应的位必须匹配
    }

    can_filter_table.can_filter_num = sizeof(can_filter_item)/sizeof(can_filter_item[0]);
    can_filter_table.can_filter_items = can_filter_item;

    can_dev_set(link_inst, &can_filter_table);

    return SUCCESSFUL;
}

Static int init_address(void)
{
    unsigned char address = 0;
    rt_pin_mode(DI_BMU_PIN, PIN_MODE_INPUT);   // DI输入
    rt_pin_mode(DO_BMU_PIN, PIN_MODE_OUTPUT);           // DO输出
    rt_pin_mode(DO_BMU_PIN_OPT_POWER, PIN_MODE_OUTPUT);           // 优化器电源DO输出

    rt_pin_write(DO_BMU_PIN, PIN_HIGH);                // DO输出高电平
    rt_pin_write(DO_BMU_PIN_OPT_POWER, PIN_HIGH);      // 优化器电源DO输出高电平

    get_one_para(BMU_PARA_ID_HOST_ADDR_OFFSET, &address);
    set_addr(address);

    return SUCCESSFUL;
}

// 地址设置
int set_addr(unsigned char addr)
{
    if(addr<MIN_ADDR || addr>MAX_ADDR)
    {
        return FAILURE;
    }
    localhost_addr = addr;
    set_host_addr(addr);
    set_one_data(BMU_DATA_ID_ASSIGN_HOST_ADDR, &localhost_addr);
    set_one_data(BMU_DATA_ID_OPT_ADDRESS, &localhost_addr);
    set_one_para(BMU_PARA_ID_HOST_ADDR_OFFSET, &localhost_addr, TRUE, FALSE);
    reset_can_filter(dev_BCMU->dev_type->link_inst);
    return SUCCESSFUL;
}

// 广播地址分配 BMU->BMU
Static int broad_addr_competition(void* dev_instance, cmd_buf_t* cmd_buff)
{
    dev_inst_t* dev_inst = (dev_inst_t*)dev_instance;
    dev_inst->dev_type->dev_code = BOTTOM_BMU_TYPE;  // 更改目的设备类型
    cmd_buff->cmd = &(dev_BCMU->dev_type->no_poll_tab[7]);
    cmd_send(dev_inst, cmd_buff);
    dev_inst->dev_type->dev_code = BOTTOM_BCMU_TYPE; // 还原目的设备类型
    return SUCCESSFUL;
}

// 广播地址分配 BMU->优化器
Static int broad_opt_addr_competition(void* dev_instance, cmd_buf_t* cmd_buff)
{
    dev_inst_t* dev_inst = (dev_inst_t*)dev_instance;
    dev_inst->dev_type->dev_code = BOTTOM_OPT_TYPE;  // 更改目的设备类型
    cmd_buff->cmd = &(dev_BCMU->dev_type->no_poll_tab[33]);
    cmd_send(dev_inst, cmd_buff);
    dev_inst->dev_type->dev_code = BOTTOM_BCMU_TYPE; // 还原目的设备类型
    return SUCCESSFUL;
}

// 地址竞争准备 BCMU->BMU
Static int addr_competition_prepare(void* dev_instance, cmd_buf_t* cmd_buff)
{
    unsigned char allocation_addr=0; // 分配地址
    rt_pin_write(DO_BMU_PIN, PIN_HIGH);  // 设置DO_BMU_PIN为高电平
    rt_thread_mdelay(500);
    if(PIN_HIGH == rt_pin_read(DI_BMU_PIN))
    {
        return FAILURE;
    }
    allocation_addr = 1;
    set_addr(allocation_addr);
    rt_thread_mdelay(500); //设置DO之前延时，避免DO状态变化，导致其他BMU误判
    rt_pin_write(DO_BMU_PIN, PIN_LOW);
    broad_opt_addr_competition(dev_instance, cmd_buff);
    broad_addr_competition(dev_instance, cmd_buff);

    return SUCCESSFUL;
}

// 地址分配
Static int addr_allocation(void* dev_instance, cmd_buf_t* cmd_buff)
{
    unsigned char allocation_addr=0; // 分配地址
    if(PIN_HIGH == rt_pin_read(DI_BMU_PIN))
    {
        return FAILURE;
    }
    if(PIN_LOW == rt_pin_read(DO_BMU_PIN))
    {
        return FAILURE;
    }
    get_one_data(BMU_DATA_ID_ASSIGN_HOST_ADDR, &allocation_addr);
    allocation_addr += 1;
    set_addr(allocation_addr);
    rt_thread_mdelay(100); //设置DO之前延时，避免DO状态变化，导致其他BMU误判
    rt_pin_write(DO_BMU_PIN, PIN_LOW);
    broad_opt_addr_competition(dev_instance, cmd_buff);
    broad_addr_competition(dev_instance, cmd_buff);
    return SUCCESSFUL;
}

/**
 * @brief BMU电芯均衡控制
 * @param[in] dev_instance 连接的设备实例
 * @param[in] cmd_buff     命令数据buff
 * @retval    SUCCESSFUL 成功，FAILURE 失败
*/
Static short control_cell_balance(void* dev_instance, cmd_buf_t* cmd_buff)
{
    dev_inst_t* dev_inst = (dev_inst_t*)dev_instance;
    unsigned short balance_ctrl_state[9] = {0};
    unsigned short chip_balance_control[SAMPLE_CHIP_NUM] = {0};
    rt_err_t excute_ret = -RT_ERROR;

    if (dev_inst == NULL || cmd_buff == NULL || cmd_buff->buf == NULL)
        return FAILURE;

    for(char i = 0; i < 9; i++) {
        balance_ctrl_state[i] = cmd_buff->buf[i + 3];
    }

    cell_balance_map(balance_ctrl_state, chip_balance_control);
    cell_balance_odd_even_trans(chip_balance_control);
    excute_ret = cell_balance_excute(chip_balance_control);

    if(excute_ret != RT_EOK)
    {
        cmd_buff->rtn = BOTTOM_RTN_CMD_ERROR;
        cmd_send((dev_inst_t*)dev_instance, cmd_buff);
        return FAILURE;
    }

    cmd_buff->rtn = BOTTOM_RTN_APP_CORRECT;
    cmd_send((dev_inst_t*)dev_instance, cmd_buff);

    return SUCCESSFUL;
}

Static short control_opt_power_status(void* dev_instance, cmd_buf_t* cmd_buff)
{
    dev_inst_t* dev_inst = (dev_inst_t*)dev_instance;
    unsigned char control_status = 0;
    if (dev_inst == NULL || cmd_buff == NULL || cmd_buff->buf == NULL)
        return FAILURE;

    control_status = cmd_buff->buf[12];

    if(control_status == 1)
    {
        rt_pin_write(DO_BMU_PIN_OPT_POWER, PIN_LOW);    //控制DO为低电平，断开优化器电源
        set_one_data(BMU_DATA_ID_OPT_POWER_STA, &control_status);
        cmd_buff->rtn = BOTTOM_RTN_APP_CORRECT;
    }
    else if(control_status == 0)
    {
        rt_pin_write(DO_BMU_PIN_OPT_POWER, PIN_HIGH);    //控制DO为高电平，闭合优化器电源
        set_one_data(BMU_DATA_ID_OPT_POWER_STA, &control_status);
        cmd_buff->rtn = BOTTOM_RTN_APP_CORRECT;
    }
    else
    {
        cmd_buff->rtn = BOTTOM_RTN_DATA_ERROR;
    }
    cmd_send((dev_inst_t*)dev_instance, cmd_buff);

    return SUCCESSFUL;
}

// 将BCMU电芯均衡控制命令的数据段进行映射，分成5个芯片对应的电芯均衡控制位
Static short cell_balance_map(unsigned short *input, unsigned short *output)
{
    output[0] = (input[0] << 5) | ((input[1] >> 3) & 0x001F);
    output[1] = ((input[1] & 0x0007) << 10) | (input[2] << 2) | ((input[3] >> 6) & 0x0003);
    output[2] = ((input[3] & 0x003F) << 7) | ((input[4] >> 1) & 0x007F);
    output[3] = ((input[4] & 0x0001) << 12) | (input[5] << 4) | ((input[6] >> 4) & 0x000F);
    output[4] = ((input[6] & 0x000F) << 9) | (input[7] << 1) | ((input[8] >> 7) & 0x0001);

    return SUCCESSFUL;
}


// 控制电芯均衡奇偶交替进行
Static short cell_balance_odd_even_trans(unsigned short *chip_balance_control)
{
    if (s_odd_even_trans == ODD_BIT_BALANCE)
    {
        chip_balance_control[0] = (chip_balance_control[0] & 0x1555);
        chip_balance_control[1] = (chip_balance_control[1] & 0x0AAA);
        chip_balance_control[2] = (chip_balance_control[2] & 0x1555);
        chip_balance_control[3] = (chip_balance_control[3] & 0x0AAA);
        chip_balance_control[4] = (chip_balance_control[4] & 0x1555);
    }
    else if (s_odd_even_trans == EVEN_BIT_BALANCE)
    {
        chip_balance_control[0] = (chip_balance_control[0] & 0x0AAA);
        chip_balance_control[1] = (chip_balance_control[1] & 0x1555);
        chip_balance_control[2] = (chip_balance_control[2] & 0x0AAA);
        chip_balance_control[3] = (chip_balance_control[3] & 0x1555);
        chip_balance_control[4] = (chip_balance_control[4] & 0x0AAA);
    }
    return SUCCESSFUL;
}


// 将unsigned short类型的输入反序后输出
Static unsigned short reverse_bits_short(unsigned short input_value) {
    unsigned short reverse_result = 0;
    for (int i = 0; i < 16; i++) {
        if (input_value & (1 << i)) {
            reverse_result |= (1 << (15 - i));
        }
    }
    return reverse_result;
}

// 调用BSP接口，控制电芯均衡
Static rt_err_t cell_balance_excute(unsigned short *chip_balance_control) {
    rt_err_t ret = RT_EOK;
    rt_device_t dev = rt_device_find("dev_mt9805");

    for (char i = 0; i < ARRAY_SIZE(s_cell_balance_mt9805); i++) {
        s_cell_balance_mt9805[i].chip_num = i;
        s_cell_balance_mt9805[i].balance_ctrl = (unsigned int)((reverse_bits_short(chip_balance_control[i]) >> 3) & 0x1FFF);
    }

    ret = rt_device_control(dev, BALANCE_CONTROL, s_cell_balance_mt9805);
    if(ret == RT_EOK)
    {
        s_odd_even_trans ^= 0x01; // 如果均衡执行成功，则改变下次均衡的奇偶路
        set_cell_balance_status(chip_balance_control); // 均衡执行成功后，更新实时数据开关量中的电芯均衡状态
    }
    return ret;
}


// 均衡执行成功后，设置实时数据中的电芯均衡状态
Static short set_cell_balance_status(unsigned short *cell_balance_status)
{
    unsigned char bit_status = 0;
    unsigned char i = 0, j = 0;

    for (i = 0; i < SAMPLE_CHIP_NUM; i++) // 5个采样芯片
    {
        for (j = 0; j < CHIP_CELL_NUM; j++) // 每个芯片对应13个电芯
        {
            bit_status = (cell_balance_status[i] >> (12 - j)) & 0x1;
            set_one_data(BMU_DATA_ID_CELL_BLC_STA + i * CHIP_CELL_NUM + j, &bit_status); // 依次设置65个电芯的均衡状态
        }
    }
    return SUCCESSFUL;
}


Static int deal_qtp_trigger_end(void* dev_inst, void* cmd_buf)
{
    RETURN_VAL_IF_FAIL(cmd_buf != NULL, FAILURE);

    cmd_buf_t* s_cmd_buf = (cmd_buf_t*)cmd_buf;
    s_cmd_buf->buf[0] = 0;

    if (s_qtp_status == TRIGGER_TIMES)
    {
        s_cmd_buf->buf[0] = 1;
    }

    s_cmd_buf->data_len = 1;
    s_cmd_buf->rtn = BOTTOM_RTN_APP_CORRECT;

    return SUCCESSFUL;
}

/**
 * 控制命令复位
 */
Static short bmu_ctrl_reset(void* dev_instance, cmd_buf_t* cmd_buff)
{
    static rt_timer_t bmu_reset = NULL;

    if (bmu_reset == NULL)
    {
        bmu_reset = rt_timer_create("bmu_reset", NVIC_SystemReset, NULL, 1000, RT_TIMER_FLAG_ONE_SHOT);
    }

    if (bmu_reset != NULL)
    {
        rt_timer_stop(bmu_reset);
        rt_timer_start(bmu_reset);
    }

    cmd_buff->rtn = BOTTOM_RTN_APP_CORRECT;
    cmd_send((dev_inst_t*)dev_instance, cmd_buff);
    return SUCCESSFUL;
}
