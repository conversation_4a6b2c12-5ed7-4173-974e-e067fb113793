#ifndef SOFTWARE_SRC_APP_ALARM_IN_H_
#define SOFTWARE_SRC_APP_ALARM_IN_H_
#include "common.h"

#ifdef __cplusplus
extern "C" {
#endif

/// 告警SN宏定义
#define ALM_SN_COCP 0 // 充电过流保护
#define ALM_SN_DOCP 1 // 放电过流保护
#define ALM_SN_BATT_OVP 2 // 电池组过压保护
#define ALM_SN_BOARD_OTP 3 // 单板过温保护
#define ALM_SN_COCA 4 // 充电过流告警
#define ALM_SN_DOCA 5 // 放电过流告警
#define ALM_SN_BATT_OVA 6 // 电池组过压告警
#define ALM_SN_ENV_TEMP_HIGH_ALM 7 // 环境温度高告警
#define ALM_SN_ENV_TEMP_LOW_ALM 8 // 环境温度低告警
#define ALM_SN_BATT_UVA 9 // 电池组欠压告警
#define ALM_SN_BATT_UVP 10 // 电池组欠压保护
#define ALM_SN_CELL_OVA 11 // 单体过压告警
#define ALM_SN_CELL_OVP 12 // 单体过压保护
#define ALM_SN_CELL_UVA 13 // 单体欠压告警
#define ALM_SN_CELL_UVP 14 // 单体欠压保护
#define ALM_SN_PACK_CHG_TEMP_HIGH_ALM 15 // 单体充电高温告警
#define ALM_SN_PACK_CHG_TEMP_HIGH_PRT 16 // 单体充电高温保护
#define ALM_SN_PACK_DISCHG_TEMP_HIGH_ALM 17 // 单体放电高温告警
#define ALM_SN_PACK_DISCHG_TEMP_HIGH_PRT 18 // 单体放电高温保护
#define ALM_SN_PACK_CHG_TEMP_LOW_ALM 19 // 单体充电低温告警
#define ALM_SN_PACK_CHG_TEMP_LOW_PRT 20 // 单体充电低温保护
#define ALM_SN_PACK_DISCHG_TEMP_LOW_ALM 21 // 单体放电低温告警
#define ALM_SN_PACK_DISCHG_TEMP_LOW_PRT 22 // 单体放电低温保护
#define ALM_SN_CELL_POOR_CONSIS_ALM 23 // 单体一致性差告警
#define ALM_SN_CELL_POOR_CONSIS_PRT 24 // 单体一致性差保护
#define ALM_SN_BATT_SOC_LOW_ALM 25 // 电池SOC低告警
#define ALM_SN_BATT_SOC_LOW_PRT 26 // 电池SOC低保护
#define ALM_SN_BATT_SOH_ALM 27 // 电池SOH低告警
#define ALM_SN_BATT_SOH_PRT 28 // 电池SOH低保护
#define ALM_SN_CELL_DAMAGE_PRT 29 // 单体损坏保护
#define ALM_SN_BATT_LOSE_ALM 30 // 电池丢失告警
#define ALM_SN_BDU_EEPROM_ALM 31 // BDU EEPROM故障
#define ALM_SN_CELL_VOLT_SAMPLE_FAULT 32 // 单体电压采样异常
#define ALM_SN_CHG_LOOP_INVALID 33 // 充电回路失效
#define ALM_SN_DISCHG_LOOP_INVALID 34 // 放电回路失效
#define ALM_SN_CURR_LIM_LOOP_INVALID 35 // 限流回路失效
#define ALM_SN_BATT_SHORTCUT 36 // 电池短路
#define ALM_SN_BATT_REVERSE 37 // 电池反接
#define ALM_SN_CELL_TEMP_INVALID 38 // 单体温度无效
#define ALM_SN_INSIDE_TEMP_HIGH_PRT 39 // 机内过温保护
#define ALM_SN_BDU_BATT_VOLT_LOW_PRT 40 // BDU电池欠压保护
#define ALM_SN_CELL_TEMP_ABNORMAL 41 // 单体温度异常
#define ALM_SN_ADDRESS_CLASH 42 // 地址冲突
#define ALM_SN_SHAKE_ALARM 43 // 振动告警
#define ALM_SN_BDU_BUS_VOLT_LOW_PRT 44 // BDU母排欠压保护
#define ALM_SN_BDU_BUS_VOLT_HIGH_PRT 45 // BDU母排过压保护
#define ALM_SN_BDU_COMM_FAIL 46 // BDU通信断
#define ALM_SN_BATT_VOLT_SAMPLE_ALM 47 // 电压采样故障
#define ALM_SN_LOOP_FAULT 48 // 回路异常
#define ALM_SN_BDU_BATT_CHG_VOLT_LOW_PRT 49 // BDU电池充电欠压保护
#define ALM_SN_BDU_BATT_LOCK_ALM 50 // BDU电池闭锁告警
#define ALM_SN_ENV_TEMP_HIGH_PRT 51 // 环境温度高保护
#define ALM_SN_ENV_TEMP_LOW_PRT 52 // 环境温度低保护
#define ALM_SN_BOARD_OTA 53 // 单板过温告警
#define ALM_SN_EQUALIZATION_CIRCUIT_FAULT_ALM 54 // 均衡电路故障告警
#define ALM_SN_UCBALANCERESISTEMPHIGHPRT 55 // 均衡电阻温度高保护
#define ALM_SN_HEATER_FILM_FAILURE 56 // 加热膜失效
#define ALM_SN_BDU_CONNECTOR_TEMP_HIGH_PRT 57 // 连接器温度高保护
#define ALM_SN_MAIN_RELAY_FAILURE 58 // 主继电器失效
#define ALM_SN_DCDC_FAULT 59 // DCDC故障
#define ALM_SN_SAMPLE_FAULT 60 // 采集异常
#define ALM_SN_AUXILIARY_SOURCE_FAILURE 61 // 辅助源故障
#define ALM_SN_CELL_DYNAMIC_UVP 62 // 单体动态欠压保护
#define ALM_SN_FIRE_CONTROL_ALM 63 // 消防告警
#define ALM_SN_ACTIVATE_PORT_CURR_ERR_PRT 64 // 激活回路电流异常保护
#define ALM_SN_ABNORMAL_CEL_TEMP_RISE 65 // 单体温升速率异常
#define ALM_SN_DCR_FAULT_ALM 66 // 直流内阻异常告警
#define ALM_SN_DCR_FAULT_PRT 67 // 直流内阻异常保护
#define ALM_SN_ABNORMAL_SELF_DISCHARGE 68 // 自放电异常
#define ALM_SN_CAPACITY_DECAY_CONSISTENCY_POOR_ALARM 69 // 容量衰减一致性差告警
#define ALM_SN_BATT_FAULT_TEMP_HIGH_ALM 70 // 电池异常高温保护告警
#define ALM_SN_FIRE_CONTROL_FAULT_ALM 71 // 消防故障告警
#define ALM_SN_ACTIVATE_PORT_REVERSE_ALM 72 // 激活口反接告警
#define ALM_SN_SITE_ANTITHEFT_ALM 73 // 站点防盗告警
#define ALM_SN_GPS_FAULT_ALARM 74 // GPS故障告警
#define ALM_SN_GYROSCOPE_FAULT_ALARM 75 // 陀螺仪故障告警
#define ALM_SN_NET_ANTITHEFT_ALM 76 // 网管防盗告警
#define ALM_SN_BATT_CELL_DAMAGE_PRT 77 // 电池单体损坏保护
#define ALM_SN_BATT_CELL_TEMP_SENSOR_INVALID_ALM 78 // 电池单体温度无效
#define ALM_SN_WATER_INGRNESS_FAULT_ALM 79 // 防水告警

/// 实时告警结构体
typedef struct
{
	BYTE	ucChgCurrHighPrt;                                  //  ID=1                 //  充电过流保护
	BYTE	ucDischgCurrHighPrt;                               //  ID=2                 //  放电过流保护
	BYTE	ucBattOverVoltPrt;                                 //  ID=3                 //  电池组过压保护
	BYTE	ucBoardTempHighPrt;                                //  ID=4                 //  单板过温保护
	BYTE	ucChgCurrHighAlm;                                  //  ID=5                 //  充电过流告警
	BYTE	ucDischgCurrHighAlm;                               //  ID=6                 //  放电过流告警
	BYTE	ucBattOverVoltAlm;                                 //  ID=7                 //  电池组过压告警
	BYTE	ucEnvTempHighAlm;                                  //  ID=8                 //  环境温度高告警
	BYTE	ucEnvTempLowAlm;                                   //  ID=9                 //  环境温度低告警
	BYTE	ucBattUnderVoltAlm;                                //  ID=10                //  电池组欠压告警
	BYTE	ucBattUnderVoltPrt;                                //  ID=11                //  电池组欠压保护
	BYTE	aucCellOverVoltAlm[CELL_VOL_NUM_MAX];              //  ID=12-27             //  单体过压告警
	BYTE	aucCellOverVoltPrt[CELL_VOL_NUM_MAX];              //  ID=28-43             //  单体过压保护
	BYTE	aucCellUnderVoltAlm[CELL_VOL_NUM_MAX];             //  ID=44-59             //  单体欠压告警
	BYTE	aucCellUnderVoltPrt[CELL_VOL_NUM_MAX];             //  ID=60-75             //  单体欠压保护
	BYTE	aucChgTempHighAlm[CELL_TEMP_NUM_MAX];              //  ID=76-91             //  单体充电高温告警
	BYTE	aucChgTempHighPrt[CELL_TEMP_NUM_MAX];              //  ID=92-107            //  单体充电高温保护
	BYTE	aucDischgTempHighAlm[CELL_TEMP_NUM_MAX];           //  ID=108-123           //  单体放电高温告警
	BYTE	aucDischgTempHighPrt[CELL_TEMP_NUM_MAX];           //  ID=124-139           //  单体放电高温保护
	BYTE	aucChgTempLowAlm[CELL_TEMP_NUM_MAX];               //  ID=140-155           //  单体充电低温告警
	BYTE	aucChgTempLowPrt[CELL_TEMP_NUM_MAX];               //  ID=156-171           //  单体充电低温保护
	BYTE	aucDischgTempLowAlm[CELL_TEMP_NUM_MAX];            //  ID=172-187           //  单体放电低温告警
	BYTE	aucDischgTempLowPrt[CELL_TEMP_NUM_MAX];            //  ID=188-203           //  单体放电低温保护
	BYTE	ucCellPoorConsisAlm;                               //  ID=204               //  单体一致性差告警
	BYTE	ucCellPoorConsisPrt;                               //  ID=205               //  单体一致性差保护
	BYTE	ucBattSOCLowAlm;                                   //  ID=206               //  电池SOC低告警
	BYTE	ucBattSOCLowPrt;                                   //  ID=207               //  电池SOC低保护
	BYTE	ucBattSOHAlm;                                      //  ID=208               //  电池SOH低告警
	BYTE	ucBattSOHPrt;                                      //  ID=209               //  电池SOH低保护
	BYTE	aucCellDamagePrt[CELL_VOL_NUM_MAX];                //  ID=210-225           //  单体损坏保护
	BYTE	ucBattLoseAlm;                                     //  ID=226               //  电池丢失告警
	BYTE	ucBduEepromAlm;                                    //  ID=227               //  BDU EEPROM故障
	BYTE	ucCellVoltSampleFault;                             //  ID=228               //  单体电压采样异常
	BYTE	ucChgLoopInvalid;                                  //  ID=229               //  充电回路失效
	BYTE	ucDischgLoopInvalid;                               //  ID=230               //  放电回路失效
	BYTE	ucCurrLimLoopInvalid;                              //  ID=231               //  限流回路失效
	BYTE	ucBattShortCut;                                    //  ID=232               //  电池短路
	BYTE	ucBattReverse;                                     //  ID=233               //  电池反接
	BYTE	ucCellTempSensorInvalidAlm[CELL_TEMP_NUM_MAX];     //  ID=234-249           //  单体温度无效
	BYTE	ucInsideTempHighPrt;                               //  ID=250               //  机内过温保护
	BYTE	ucBDUBattVoltLowPrt;                               //  ID=251               //  BDU电池欠压保护
	BYTE	ucCellTempAbnormal;                                //  ID=252               //  单体温度异常
	BYTE	ucAddressClash;                                    //  ID=253               //  地址冲突
	BYTE	ucShakeAlarm;                                      //  ID=254               //  振动告警
	BYTE	ucBDUBusVoltLowPrt;                                //  ID=255               //  BDU母排欠压保护
	BYTE	ucBDUBusVoltHighPrt;                               //  ID=256               //  BDU母排过压保护
	BYTE	ucBDUCommFail;                                     //  ID=257               //  BDU通信断
	BYTE	ucBattVoltSampleAlm;                               //  ID=258               //  电压采样故障
	BYTE	ucLoopFault;                                       //  ID=259               //  回路异常
	BYTE	ucBDUBattChgVoltLowPrt;                            //  ID=260               //  BDU电池充电欠压保护
	BYTE	ucBDUBattLockAlm;                                  //  ID=261               //  BDU电池闭锁告警
	BYTE	ucEnvTempHighPrt;                                  //  ID=262               //  环境温度高保护
	BYTE	ucEnvTempLowPrt;                                   //  ID=263               //  环境温度低保护
	BYTE	ucBoardTempHighAlm;                                //  ID=264               //  单板过温告警
	BYTE	ucEqualCircuitFaultAlm;                            //  ID=265               //  均衡电路故障告警
	BYTE	ucBalanceResisTempHighPrt;                         //  ID=266               //  均衡电阻温度高保护
	BYTE	ucHeaterFilmFailure;                               //  ID=267               //  加热膜失效
	BYTE	ucBDUConnTempHighPrt;                              //  ID=268               //  连接器温度高保护
	BYTE	ucMainRelayFail;                                   //  ID=269               //  主继电器失效
	BYTE	ucDCDCErr;                                         //  ID=270               //  DCDC故障
	BYTE	ucSampleErr;                                       //  ID=271               //  采集异常
	BYTE	ucAuxiSourceErr;                                   //  ID=272               //  辅助源故障
	BYTE	aucCellDynamicUnderVoltPrt[CELL_VOL_NUM_MAX];      //  ID=273-288           //  单体动态欠压保护
	BYTE	ucFireControlAlm;                                  //  ID=289               //  消防告警
	BYTE	ucActivePortCurrError;                             //  ID=290               //  激活回路电流异常保护
	BYTE	ucCellTempRiseAbnormal;                            //  ID=291               //  单体温升速率异常
	BYTE	ucDcrFaultAlm;                                     //  ID=292               //  直流内阻异常告警
	BYTE	ucDcrFaultPrt;                                     //  ID=293               //  直流内阻异常保护
	BYTE	ucSelfDischFualt;                                  //  ID=294               //  自放电异常
	BYTE	ucCapDCPRFaultAlm;                                 //  ID=295               //  容量衰减一致性差告警
	BYTE	ucBattFaultTempHighAlm;                            //  ID=296               //  电池异常高温保护告警
	BYTE	ucFireControlFaultAlm;                             //  ID=297               //  消防故障告警
	BYTE	ucActivatePortReverseAlm;                          //  ID=298               //  激活口反接告警
	BYTE	ucSiteAntitheftAlm;                                //  ID=299               //  站点防盗告警
	BYTE	ucGpsFaultAlm;                                     //  ID=300               //  GPS故障告警
	BYTE	ucGyrFaultAlm;                                     //  ID=301               //  陀螺仪故障告警
	BYTE	ucNetAntitheftAlm;                                 //  ID=302               //  网管防盗告警
	BYTE	ucBattCellDamagePrt;                               //  ID=303               //  电池单体损坏保护
	BYTE	ucBattCellTempInvalidAlm;                          //  ID=304               //  电池单体温度无效
	BYTE	ucWaterIngrAlm;                                    //  ID=305               //  防水告警
} T_BCMAlarmStruct;


#ifdef __cplusplus
} /* end of the extern "C" block */
#endif

#endif

