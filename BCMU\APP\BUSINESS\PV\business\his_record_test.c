#include "his_data.h"
#include "type_define_in.h"
#include "alarm_id_in.h"
#include "utils_data_type_conversion.h"
#include "utils_data_transmission.h"
#include "stdlib.h"
#include "his_record_test.h"
#include "realdata_id_in.h"
#include "hisdata_in.h"
#include "para_id_in.h"
#include "energy_accu.h"
#include "partition_def.h"
#include "storage.h"
#include "sys/stat.h"
#include "alarm_manage.h"
#include "utils_rtthread_security_func.h"

// #define rt_snprintf snprintf
// #define rt_kprintf printf

static his_record_t* g_his_record = NULL;
static unsigned char g_hisdata_buff[HISDATA_LEN] = {0};
static reg_record_test_t_func_t s_reg_func = {};
int save_his_alarm_for_test(unsigned short save_num)
{
    int i = 0;
    his_alm_t his_alm_data;
    unsigned short his_id_index = 0;
    unsigned short his_alm_id[] =
    {
        DAC_ALARM_ID_LOW_TEMP_INSIDS_MACHINE,
        DAC_ALARM_ID_STR_LOSS,
        DAC_ALARM_ID_ABNORMAL_OPER_BUILD_IN_PID,
        DAC_ALARM_ID_BUS_VOLT_IMBALANCE,
        DAC_ALARM_ID_NEG_BUS_OVER_VOLT,
        DAC_ALARM_ID_POS_BUS_OVER_VOLT,
        DAC_ALARM_ID_BUS_OVER_VOLT,
        DAC_ALARM_ID_AFCI_SELF_CHK_FAIL,
        DAC_ALARM_ID_DC_ARC_DAULT,
    };
    unsigned int his_alm_id_array_len = sizeof(his_alm_id)/sizeof(his_alm_id[0]);

    rt_kprintf("save_his_alarm_for_test start %d save_num: %d\n",rt_tick_get_millisecond(), save_num);
    time_t cur_time = time(NULL);
    for(i = 0; i < save_num ; i++)
    {
        rt_thread_mdelay(10);
        his_id_index = rt_rand_s() % his_alm_id_array_len;
        his_alm_data.alm_code =ALM_ID_GET_ALM_CODE(his_alm_id[his_id_index]) + ALM_ID_GET_DEV(his_alm_id[his_id_index]);  
        time_t_to_timestruct(cur_time + i * 10, &his_alm_data.start_time);
        time_t_to_timestruct(cur_time + (i + 1) * 10, &his_alm_data.end_time);
        save_his_alarm(&his_alm_data);
    }
    rt_kprintf("save_his_alarm_for_test end %d save_num: %d\n",rt_tick_get_millisecond(), save_num);
    return SUCCESSFUL;
}

void register_test_his_record_tab(his_record_t* his_record, int (*func)(unsigned char*)) 
{
    g_his_record = his_record;
    s_reg_func.save_sample_data_func = func;
    return;
}

short save_test_his_data(void* his_data, time_base_t* time) {
    unsigned short record_size = 0;
    short ret;
    int time_head_len = sizeof(time_base_t);
    unsigned short crc = 0;
    
    RETURN_VAL_IF_FAIL(his_data != NULL, FAILURE);
    record_size = g_his_record[HIS_DATA_REC_INDEX].record_size;
    g_his_record[HIS_DATA_REC_INDEX].data = (unsigned char*)rt_malloc(record_size);
    RETURN_VAL_IF_FAIL(g_his_record[HIS_DATA_REC_INDEX].data != NULL, FAILURE);

    put_time_to_buff(g_his_record[HIS_DATA_REC_INDEX].data,*time);

    g_his_record[HIS_DATA_REC_INDEX].data[time_head_len] = get_his_data_number();
    rt_memcpy_s(&g_his_record[HIS_DATA_REC_INDEX].data[time_head_len + 1], record_size, his_data, HISDATA_LEN);

    crc = crc_cal(g_his_record[HIS_DATA_REC_INDEX].data, record_size - 2);
    rt_memcpy_s(&g_his_record[HIS_DATA_REC_INDEX].data[record_size-2], record_size, &crc, 2);

    ret = save_one_record(&g_his_record[HIS_DATA_REC_INDEX]);
    rt_free(g_his_record[HIS_DATA_REC_INDEX].data);
    return ret; 
}

int gen_test_sample_data(unsigned short save_num)
{   
    int i = 0;
    time_t time_stamp = 0;
    time_base_t time = {0};
    
    get_time(&time);
    time_stamp = timestruct_to_time_t(&time);

    rt_kprintf("gen_test_sample_data start %d save_num: %d\n",rt_tick_get_millisecond(), save_num);
    for(i = 0; i < save_num; i++)
    {   
        rt_thread_mdelay(10);
        time_stamp += TIME_GAP;
        rt_memset_s(g_hisdata_buff, HISDATA_LEN, 0, HISDATA_LEN);
        time_t_to_timestruct(time_stamp, &time);
        s_reg_func.save_sample_data_func(g_hisdata_buff);
        if(SUCCESSFUL != save_test_his_data(g_hisdata_buff, &time)){
            rt_kprintf("gen_test_sample_data error %d\n",rt_tick_get_millisecond());
            return FAILURE;
        }
    }
    rt_kprintf("gen_test_sample_data end %d\n",rt_tick_get_millisecond());
    return SUCCESSFUL;
}


int gen_test_event_record(unsigned short save_num)
{
    unsigned short i = 0;
    unsigned char idx = 0;
    event_record_t data = {0};
    unsigned short para_ids[] = 
    {
        DAC_PARA_ID_UPPER_LIMIT_GRID_RECONN_VOLT,
        DAC_PARA_ID_LOWER_LIMIT_GRID_CONN_START_VOLT,
        DAC_PARA_ID_VOLT_PERCENT,
        DAC_PARA_ID_ACTIVE_POWER_REFERENCE,
        DAC_PARA_ID_COMMU_BROKEN_CHAIN_DETECT_TIME,
        DAC_PARA_ID_ACTIVE_POWER_DERATING_PERCENT,
        DAC_PARA_ID_QU_SCHEDU_TRIGG_POWER_PERCENT,
        DAC_PARA_ID_REACTIVE_POWER_REGULAR_CYCLE,
        DAC_PARA_ID_UNDER_VOLT_PROT_POINT_GRID,
        DAC_PARA_ID_AUXI_CONTROL_INVERTER_VOLT_CORRECT_RATIO,
    };
    unsigned char para_ids_num = sizeof(para_ids) / sizeof(unsigned short);

    rt_kprintf("gen_test_event_record start %d save_num: %d\n",rt_tick_get_millisecond(), save_num);
    for (i = 0; i < save_num; i++)
    {   
        rt_thread_mdelay(10);
        idx = rt_rand_s() % para_ids_num;
        data.type = 0x02;   //参数设置
        data.event_id = para_ids[idx];
        rt_snprintf(data.info, MAX_EVENT_INFO_LEN, "%s%d", "TestCmd,No=", i);
        save_event_record(&data);
    }
    rt_kprintf("gen_test_event_record end %d save_num: %d\n",rt_tick_get_millisecond(), save_num);
    return SUCCESSFUL;
}

int save_one_data(char* name,energy_record_t* data)
{
    part_data_t part_data = {0};
    struct stat buf={0};
    part_data.buff = (unsigned char* )data;
    part_data.len = sizeof(energy_record_t);
    int ret = storage_stat( name , &buf);
    if( ret == -1 )
    {
        return FAILURE;
    } 
    part_data.offset = buf.st_size;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), name);
    if(SUCCESSFUL != storage_process(&part_data, write_opr))
    {
        return FAILURE;
    }

    return SUCCESSFUL;
}


void save_one_energy_record(int type ,energy_record_t* data )
{
    if(NULL == data)
    {
        return;
    }
    switch(type)
    {
        case HIS_YEAR:
            save_one_data(YEAR_FILE,data);
            break;

        case HIS_MONTH:
            save_one_data(MONTH_FILE,data);
            break;

        case HIS_DAY:
            save_one_data(DAY_FILE,data);
            break;

        case HIS_HOUR:
            save_one_data(HOUR_FILE,data);
            break;

        default:
            break;
    }
   // rt_kprintf("%d-%d-%d-%d engery:%f\n" , data->save_time.year , data->save_time.month , data->save_time.day , data->save_time.hour , data->energy);
    return;
}

void save_exclude_lastyear_all_data()
{
    energy_record_t record={0};
    time_base_t save_time = {2000,1,0,0,0,0};

    //前24年数据自己制造,该结构体放了6年月的电量样本数据
    static float energ_12month[6][12] = 
    {
        {10,20,30,40,50,60,70,80,90,100,110,120},
        {30,40,50,60,90,100,110,120,130,140,150,160},
        {20,20,21,22,19,18,17,20,21,22,20,20},
        {30,20,10,9,8,10,20,30,31,40,45,46},
        {20,22,30,40,50,60,80,60,50,40,30,20},
        {30,30,30,30,32,32,31,31,31,29,29,28}
    };
    for(int i = 0; i < 24 ; i++)
    {
        float sum = 0.0;
        save_time.year = 2000 + i;
        for(int j = 0;j<12 ;j++)
        {
            
            save_time.month = 1 + j;
            int tmp =i%6;
            rt_memcpy_s(&record.save_time, sizeof(time_base_t), &save_time, sizeof(time_base_t));
            record.energy = energ_12month[tmp][j];
            record.crc = crc_cal((unsigned char*)&record,sizeof(energy_record_t)-2);
            sum += energ_12month[tmp][j];
            //存月数据
            save_one_energy_record( HIS_MONTH , &record );
        }
        //存年数据
        save_time.month = 0;
        rt_memcpy_s(&record.save_time, sizeof(time_base_t), &save_time, sizeof(time_base_t));
        // put_time_to_buff((unsigned char* )&(record.save_time),save_time);
        record.energy = sum;
        record.crc = crc_cal((unsigned char*)&record,sizeof(energy_record_t)-2);
        save_one_energy_record( HIS_YEAR , &record );
    }
}

//
float save_last_year_day_hour_data()
{
    energy_record_t record={0};
    float hour_sum = 0.0;
    time_base_t save_time = {2024,12,1,0,0,0};
    //首先记录30*24小时数据
    static float all_hours[24]={0.2,0.3,0.4,0.5,0.5,0.6,0.4,0.3,0.2,0.1,0.5,0.5,0.8,0.9,1.0,0.8,0.8,0.7,0.5,0.6,0.3,0.3,0.1,0.1};
    for(int i = 0;i < 30;i++)
    {        
        hour_sum = 0.0;
        save_time.day = 1 + i;
        for(int j = 0 ; j < 24 ; j++)
        {
            save_time.hour = j;
            hour_sum +=all_hours[j];
            rt_memcpy_s(&record.save_time, sizeof(time_base_t), &save_time ,sizeof(time_base_t));
            record.energy = all_hours[j];
            record.crc = crc_cal((unsigned char*)&record,sizeof(energy_record_t)-2);
            //存时数据
            save_one_energy_record( HIS_HOUR , &record );
        }
        save_time.hour = 0;
        rt_memcpy_s(&record.save_time, sizeof(time_base_t), &save_time ,sizeof(time_base_t));
        // put_time_to_buff((unsigned char* )&(record.save_time) ,save_time);
        record.energy = hour_sum;
        record.crc = crc_cal((unsigned char*)&record,sizeof(energy_record_t)-2);
        //存日数据
        save_one_energy_record( HIS_DAY , &record );
    }
    //存最后一条月的数据
    save_time.month = 12;
    save_time.day = 0;
    rt_memcpy_s(&record.save_time, sizeof(time_base_t), &save_time ,sizeof(time_base_t));
    // put_time_to_buff((unsigned char* )&(record.save_time),save_time);
    record.energy = hour_sum * 30;
    record.crc = crc_cal((unsigned char*)&record,sizeof(energy_record_t)-2);
    save_one_energy_record( HIS_MONTH , &record );
    return record.energy;
}


////最近一年的12个月的日发电量数据，其中最后一个月和小时，天电量挂钩 分开写
void save_last_year_month_day_data(float last_month)
{
    energy_record_t record={0};
    float day_sum = 0;
    time_base_t save_time = {2024,1,1,0,0,0};
    float day[30] = {1,2,3,4,1,1,1,2,2,2,3,3,3,3,2,2,2,1,1,1,2,2,3,1,1,2,3,4,1,2};
    for(int i = 0; i < 11;i++)
    {
        day_sum = 0;
        save_time.month = 1 + i;
        for(int j = 0;j < 30 ; j++)
        {
            save_time.day = 1 + j;
            rt_memcpy_s(&record.save_time, sizeof(time_base_t), &save_time ,sizeof(time_base_t));
            record.energy = day[j];
            day_sum += day[j]; 
            record.crc = crc_cal((unsigned char*)&record,sizeof(energy_record_t)-2);
            //存日数据
            save_one_energy_record( HIS_DAY , &record );
        }
        save_time.day = 0;
        rt_memcpy_s(&record.save_time, sizeof(time_base_t), &save_time ,sizeof(time_base_t));
        // put_time_to_buff((unsigned char* )&(record.save_time) , save_time);
        record.energy = day_sum;
        record.crc = crc_cal((unsigned char*)&record,sizeof(energy_record_t)-2);
        //存月的数据
        save_one_energy_record( HIS_MONTH , &record );
    }
    //存最后一条年的数据
    save_time.month = 0;
    save_time.hour = 0;
    rt_memcpy_s(&record.save_time, sizeof(time_base_t), &save_time ,sizeof(time_base_t));
    // put_time_to_buff((unsigned char* )&(record.save_time),save_time);
    record.energy = day_sum * 11 + last_month;
    record.crc = crc_cal((unsigned char*)&record,sizeof(energy_record_t)-2);
    save_one_energy_record( HIS_YEAR , &record );
}

void gen_energy_record_test_data()
{   
    storage_unlink(YEAR_FILE);
    storage_unlink(MONTH_FILE);
    storage_unlink(DAY_FILE);
    storage_unlink(HOUR_FILE);
    storage_create(YEAR_FILE);
    storage_create(MONTH_FILE);
    storage_create(DAY_FILE);
    storage_create(HOUR_FILE);
    save_exclude_lastyear_all_data();
    float last_month = save_last_year_day_hour_data();
    save_last_year_month_day_data(last_month);
}
