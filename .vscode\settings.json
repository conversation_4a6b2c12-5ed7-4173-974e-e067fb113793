{
    // "cmake.sourceDirectory": "${workspaceFolder}/BCMU/IDE/AIRSWITCH/GD32C1XX",
    // "cmake.sourceDirectory": "${workspaceFolder}/BCMU/IDE/ZXESM_BMU/GD32F403",
    // "cmake.sourceDirectory": "${workspaceFolder}/BCMU/IDE/ZXESM_R321/GD32F470ZG",
    // "cmake.sourceDirectory": "${workspaceFolder}/BCMU/IDE/ZXESM_D121/GD32F470ZG",
    // "cmake.sourceDirectory": "${workspaceFolder}/BCMU/IDE/SEUB/GD32F470ZG",
    // "cmake.sourceDirectory": "${workspaceFolder}/BCMU/IDE/ACMU/GD32F470ZG",
    "cmake.sourceDirectory": "${workspaceFolder}/BCMU/IDE/DCMU/GD32F470ZG",
}

{
    // 减少文件监视数量
    "files.watcherExclude": {
        "**/.git/objects/**": true,
        "**/.git/subtree-cache/**": true,
        "**/node_modules/**": true
    },
    
    // 减少搜索文件数量
    "search.exclude": {
        "**/node_modules": true,
        "**/bower_components": true,
        "**/*.code-search": true
    },

    // 关闭自动更新
    "update.mode": "manual"
}