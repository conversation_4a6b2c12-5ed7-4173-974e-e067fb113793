#ifndef SOFTWARE_SRC_APP_ALARM_IN_H_
#define SOFTWARE_SRC_APP_ALARM_IN_H_
#include "common.h"

#ifdef __cplusplus
extern "C" {
#endif

/// 告警SN宏定义
#define ALM_SN_BATT_UVA 0 // 电池组欠压告警
#define ALM_SN_BATT_OVA 1 // 电池组过压告警
#define ALM_SN_BATT_UVP 2 // 电池组欠压保护
#define ALM_SN_BATT_OVP 3 // 电池组过压保护
#define ALM_SN_COCA 4 // 充电过流告警
#define ALM_SN_COCP 5 // 充电过流保护
#define ALM_SN_DOCA 6 // 放电过流告警
#define ALM_SN_DOCP 7 // 放电过流保护
#define ALM_SN_BATT_SHORTCUT 8 // 电池短路
#define ALM_SN_BATT_REVERSE 9 // 电池反接
#define ALM_SN_DISCHG_LOOP_INVALID 10 // 放电回路失效
#define ALM_SN_CHG_LOOP_INVALID 11 // 充电回路失效
#define ALM_SN_CURR_LIM_LOOP_INVALID 12 // 限流回路失效
#define ALM_SN_BATT_SOC_LOW_ALM 13 // 电池SOC低告警
#define ALM_SN_BATT_SOC_LOW_PRT 14 // 电池SOC低保护
#define ALM_SN_BATT_SOH_ALM 15 // 电池SOH低告警
#define ALM_SN_BATT_SOH_PRT 16 // 电池SOH低保护
#define ALM_SN_BOARD_OTA 17 // 单板过温告警
#define ALM_SN_BOARD_OTP 18 // 单板过温保护
#define ALM_SN_CELL_UVA 19 // 单体欠压告警
#define ALM_SN_CELL_OVA 20 // 单体过压告警
#define ALM_SN_CELL_UVP 21 // 单体欠压保护
#define ALM_SN_CELL_OVP 22 // 单体过压保护
#define ALM_SN_CELL_POOR_CONSIS_ALM 23 // 单体一致性差告警
#define ALM_SN_CELL_POOR_CONSIS_PRT 24 // 单体一致性差保护
#define ALM_SN_CELL_VOLT_SAMPLE_FAULT 25 // 单体电压采样异常
#define ALM_SN_CELL_DAMAGE_PRT 26 // 单体损坏保护
#define ALM_SN_PACK_CHG_TEMP_HIGH_ALM 27 // 单体充电高温告警
#define ALM_SN_PACK_CHG_TEMP_LOW_ALM 28 // 单体充电低温告警
#define ALM_SN_PACK_CHG_TEMP_HIGH_PRT 29 // 单体充电高温保护
#define ALM_SN_PACK_CHG_TEMP_LOW_PRT 30 // 单体充电低温保护
#define ALM_SN_PACK_DISCHG_TEMP_HIGH_ALM 31 // 单体放电高温告警
#define ALM_SN_PACK_DISCHG_TEMP_LOW_ALM 32 // 单体放电低温告警
#define ALM_SN_PACK_DISCHG_TEMP_HIGH_PRT 33 // 单体放电高温保护
#define ALM_SN_PACK_DISCHG_TEMP_LOW_PRT 34 // 单体放电低温保护
#define ALM_SN_CELL_TEMP_SENSOR_INVALID_ALM 35 // 单体温度传感器无效告警
#define ALM_SN_ENV_TEMP_HIGH_ALM 36 // 环境温度高告警
#define ALM_SN_ENV_TEMP_LOW_ALM 37 // 环境温度低告警
#define ALM_SN_ENV_TEMP_HIGH_PRT 38 // 环境温度高保护
#define ALM_SN_ENV_TEMP_LOW_PRT 39 // 环境温度低保护
#define ALM_SN_ENV_TEMP_SENSOR_INVALID_ALM 40 // 环境温度传感器无效告警
#define ALM_SN_BDU_BATT_VOLT_LOW_PRT 41 // BDU电池欠压保护
#define ALM_SN_BDU_EEPROM_ALM 42 // BDU EEPROM故障
#define ALM_SN_BDU_BUS_VOLT_LOW_PRT 43 // BDU母排欠压保护
#define ALM_SN_BDU_BUS_VOLT_HIGH_PRT 44 // BDU母排过压保护
#define ALM_SN_BDU_COMM_FAIL 45 // BDU通信断
#define ALM_SN_BDU_BATT_CHG_VOLT_LOW_PRT 46 // BDU电池充电欠压保护
#define ALM_SN_BDU_BATT_LOCK_ALM 47 // BDU电池闭锁告警
#define ALM_SN_EQUALCIRCUITFAULTALM 48 // 均衡电路故障告警
#define ALM_SN_BATT_LOSE_ALM 49 // 电池丢失告警
#define ALM_SN_INSIDE_TEMP_HIGH_PRT 50 // 机内过温保护
#define ALM_SN_LOOP_FAULT 51 // 回路异常
#define ALM_SN_POWER_OFF_TIME_PRT_ALM 52 // 停电时间保护告警
#define ALM_SN_HEATER_FILM_FAILURE 53 // 加热膜失效
#define ALM_SN_FUSE_ERROR 54 // 熔丝损坏
#define ALM_SN_BDU_CONNECTOR_TEMP_HIGH_PRT 55 // 连接器温度高保护
#define ALM_SN_MAIN_RELAY_FAILURE 56 // 主继电器失效
#define ALM_SN_DCDC_FAULT 57 // DCDC故障
#define ALM_SN_SAMPLE_FAULT 58 // 采集异常
#define ALM_SN_AUXILIARY_SOURCE_FAILURE 59 // 辅助源故障
#define ALM_SN_CELL_TEMP_ABNORMAL 60 // 单体温度异常
#define ALM_SN_ADDRESS_CLASH 61 // 地址冲突
#define ALM_SN_CELL_DYNAMIC_UVP 62 // 单体动态欠压保护
#define ALM_SN_HEAT_CONNECTOR_TEMP_HIGH_PRT 63 // 加热膜连接器高温
#define ALM_SN_ABNORMAL_CEL_TEMP_RISE 64 // 单体温升速率异常
#define ALM_SN_DCR_FAULT_ALM 65 // 直流内阻异常告警
#define ALM_SN_DCR_FAULT_PRT 66 // 直流内阻异常保护
#define ALM_SN_ABNORMAL_SELF_DISCHARGE 67 // 自放电异常
#define ALM_SN_POWER_COMM_FAIL 68 // 电源通信断
#define ALM_SN_CAPACITY_DECAY_CONSISTENCY_POOR_ALARM 69 // 容量衰减一致性差告警
#define ALM_SN_BATT_FAULT_TEMP_HIGH_ALM 70 // 电池异常高温保护告警

/// 实时告警结构体
typedef struct
{
	BYTE	ucBattUnderVoltAlm; //  电池组欠压告警
	BYTE	ucBattOverVoltAlm; //  电池组过压告警
	BYTE	ucBattUnderVoltPrt; //  电池组欠压保护
	BYTE	ucBattOverVoltPrt; //  电池组过压保护
	BYTE	ucChgCurrHighAlm; //  充电过流告警
	BYTE	ucChgCurrHighPrt; //  充电过流保护
	BYTE	ucDischgCurrHighAlm; //  放电过流告警
	BYTE	ucDischgCurrHighPrt; //  放电过流保护
	BYTE	ucBattShortCut; //  电池短路
	BYTE	ucBattReverse; //  电池反接
	BYTE	ucDischgLoopInvalid; //  放电回路失效
	BYTE	ucChgLoopInvalid; //  充电回路失效
	BYTE	ucCurrLimLoopInvalid; //  限流回路失效
	BYTE	ucBattSOCLowAlm; //  电池SOC低告警
	BYTE	ucBattSOCLowPrt; //  电池SOC低保护
	BYTE	ucBattSOHAlm; //  电池SOH低告警
	BYTE	ucBattSOHPrt; //  电池SOH低保护
	BYTE	ucBoardTempHighAlm; //  单板过温告警
	BYTE	ucBoardTempHighPrt; //  单板过温保护
	BYTE	aucCellUnderVoltAlm[CELL_VOL_NUM_MAX]; //  单体欠压告警
	BYTE	aucCellOverVoltAlm[CELL_VOL_NUM_MAX]; //  单体过压告警
	BYTE	aucCellUnderVoltPrt[CELL_VOL_NUM_MAX]; //  单体欠压保护
	BYTE	aucCellOverVoltPrt[CELL_VOL_NUM_MAX]; //  单体过压保护
	BYTE	ucCellPoorConsisAlm; //  单体一致性差告警
	BYTE	ucCellPoorConsisPrt; //  单体一致性差保护
	BYTE	ucCellVoltSampleFault; //  单体电压采样异常
	BYTE	aucCellDamagePrt[CELL_VOL_NUM_MAX]; //  单体损坏保护
	BYTE	aucChgTempHighAlm[CELL_TEMP_NUM_MAX]; //  单体充电高温告警
	BYTE	aucChgTempLowAlm[CELL_TEMP_NUM_MAX]; //  单体充电低温告警
	BYTE	aucChgTempHighPrt[CELL_TEMP_NUM_MAX]; //  单体充电高温保护
	BYTE	aucChgTempLowPrt[CELL_TEMP_NUM_MAX]; //  单体充电低温保护
	BYTE	aucDischgTempHighAlm[CELL_TEMP_NUM_MAX]; //  单体放电高温告警
	BYTE	aucDischgTempLowAlm[CELL_TEMP_NUM_MAX]; //  单体放电低温告警
	BYTE	aucDischgTempHighPrt[CELL_TEMP_NUM_MAX]; //  单体放电高温保护
	BYTE	aucDischgTempLowPrt[CELL_TEMP_NUM_MAX]; //  单体放电低温保护
	BYTE	ucCellTempSensorInvalidAlm[CELL_TEMP_NUM_MAX]; //  单体温度传感器无效告警
	BYTE	ucEnvTempHighAlm; //  环境温度高告警
	BYTE	ucEnvTempLowAlm; //  环境温度低告警
	BYTE	ucEnvTempHighPrt; //  环境温度高保护
	BYTE	ucEnvTempLowPrt; //  环境温度低保护
	BYTE	ucEnvTempSensorInvalidAlm; //  环境温度传感器无效告警
	BYTE	ucBDUBattVoltLowPrt; //  BDU电池欠压保护
	BYTE	ucBduEepromAlm; //  BDU EEPROM故障
	BYTE	ucBDUBusVoltLowPrt; //  BDU母排欠压保护
	BYTE	ucBDUBusVoltHighPrt; //  BDU母排过压保护
	BYTE	ucBDUCommFail; //  BDU通信断
	BYTE	ucBDUBattChgVoltLowPrt; //  BDU电池充电欠压保护
	BYTE	ucBDUBattLockAlm; //  BDU电池闭锁告警
	BYTE	ucEqualCircuitFaultAlm; //  均衡电路故障告警
	BYTE	ucBattLoseAlm; //  电池丢失告警
	BYTE	ucInsideTempHighPrt; //  机内过温保护
	BYTE	ucLoopFault; //  回路异常
	BYTE	ucPowerOffTimePrtAlm; //  停电时间保护告警
	BYTE	ucHeaterFilmFailure; //  加热膜失效
	BYTE	ucFuseError; //  熔丝损坏
	BYTE	ucBDUConnTempHighPrt; //  连接器温度高保护
	BYTE	ucMainRelayFail; //  主继电器失效
	BYTE	ucDCDCErr; //  DCDC故障
	BYTE	ucSampleErr; //  采集异常
	BYTE	ucAuxiSourceErr; //  辅助源故障
	BYTE	ucCellTempAbnormal; //  单体温度异常
	BYTE	ucAddressClash; //  地址冲突
	BYTE	aucCellDynamicUnderVoltPrt[CELL_VOL_NUM_MAX]; //  单体动态欠压保护
	BYTE	ucHeatConnTempHighPrt; //  加热膜连接器高温
	BYTE	ucCellTempRiseAbnormal; //  单体温升速率异常
	BYTE	ucDcrFaultAlm; //  直流内阻异常告警
	BYTE	ucDcrFaultPrt; //  直流内阻异常保护
	BYTE	ucSelfDischFualt; //  自放电异常
	BYTE	ucPowerCommFail; //  电源通信断
	BYTE	ucCapDCPRFaultAlm; //  容量衰减一致性差告警
	BYTE	ucBattFaultTempHighAlm; //  电池异常高温保护告警
} T_BCMAlarmStruct;


#ifdef __cplusplus
} /* end of the extern "C" block */
#endif

#endif

