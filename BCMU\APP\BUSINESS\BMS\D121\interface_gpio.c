#include "interface.h"

/**************************************************************************
* Copyright (C) 2010-2011, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：BMS板软件
* 文件名称：alarm.c
* 文件说明：按键Button驱动
* 作    者  ：王梓藤
* 版本信息：V1.0
* 设计日期：2022-10-21
* 修改记录：
* 日    期      版  本      修改人      修改摘要
* 其他说明：
***************************************************************************/
void GPIO_Init(void)
{
    rt_pin_mode(LED_RUN_PIN, PIN_MODE_OUTPUT);
    rt_pin_mode(LED_ALM_PIN, PIN_MODE_OUTPUT);
    rt_pin_mode(LED_SOC1_PIN, PIN_MODE_OUTPUT);
    rt_pin_mode(LED_SOC2_PIN, PIN_MODE_OUTPUT);
    rt_pin_mode(LED_SOC3_PIN, PIN_MODE_OUTPUT);
    rt_pin_mode(LED_SOC4_PIN, PIN_MODE_OUTPUT);
    rt_pin_mode(RELAY1_PIN, PIN_MODE_OUTPUT);
    rt_pin_mode(RELAY2_PIN, PIN_MODE_OUTPUT);
    rt_pin_mode(SW_B1_Pin, PIN_MODE_OUTPUT);
    rt_pin_mode(WARM_CTL_Pin, PIN_MODE_OUTPUT);
    rt_pin_mode(SW_DET_Pin, PIN_MODE_INPUT);
    rt_pin_mode(S_POWER_Pin, PIN_MODE_INPUT);
    rt_pin_mode(THEFT_DET_Pin, PIN_MODE_INPUT);
    rt_pin_mode(BALANCE_FAULT_Pin, PIN_MODE_INPUT);
    rt_pin_write(SW_B1_Pin, 1);
    
    rt_pin_mode(SWITCH_4G, PIN_MODE_OUTPUT);
    rt_pin_mode(SWITCH_WIFI, PIN_MODE_OUTPUT);

    return;
}

void PWM_Config(void)
{
    return;
}


void BSP_BUZZ_CTRL(CTRL_State state)
{
    return;
}