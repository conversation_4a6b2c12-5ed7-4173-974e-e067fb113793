#include <rtthread.h>
#include <rtdef.h>
#include <rtdevice.h>
#include <math.h>
#include <string.h>
#include <stdlib.h>
#include "gd32f4xx.h"
#include "flash.h"
#include "drv_spi_flash.h"
#include "spi_flash.h"
#include "sfud.h"
#include "sfud_def.h"
#include "utils_data_transmission.h"
#include "fal.h"
#include "cmd.h"
#include "update_manage.h"
#include "sps.h"
#include "protocol_layer.h"


#define UPDATE_FILE_NAME     "bccu_app.bin"

#ifdef UNITEST
unsigned char dest_dev;
#else
extern unsigned char dest_dev;
#endif

Static trig_ctr_inf_t s_trig_ctr_inf;
static rt_mutex_t s_ptMutexFile;

Static int get_app_download_rtn(void *cmd_buf);
Static int bccu_update_trigger(void);
Static int parse_update_trig_old(void* dev_inst, void *cmd_buf);
Static int pack_update_trig_old(void* dev_inst, void *cmd_buf);

static cmd_handle_register_t update_cmd_handle[] = {
    {DEV_BCCU_NORTH_USART, CMD_UPDATE_TRIG_OLD, CMD_TYPE_NO_POLL, parse_update_trig_old, pack_update_trig_old},
};

void update_manage_init(void)
{
    short i = 0;

    for (i = 0; i < sizeof(update_cmd_handle) / sizeof(update_cmd_handle[0]); i++)
    {
        register_cmd_handle(&update_cmd_handle[i]);
    }

    s_ptMutexFile = rt_mutex_create("file_lock", RT_IPC_FLAG_PRIO);
}

Static int get_app_download_rtn(void *cmd_buf)
{
    bottom_comm_cmd_head_t *proto_head = NULL;
    cmd_buf_t *trig_cmd_buf = (cmd_buf_t *)cmd_buf;
    unsigned char dev_type = 0;

    // 入参校验
    if (trig_cmd_buf == NULL || trig_cmd_buf->buf == NULL)
    {
        return FAILURE;
    }

    proto_head = (bottom_comm_cmd_head_t *)trig_cmd_buf->cmd->req_head;
    dev_type = dest_dev;
    char *p_file_name = s_trig_ctr_inf.file_name;
    unsigned char *p = trig_cmd_buf->buf;

    if (proto_head->append != 0xAA55)
    {
        return DownloadFrameErr;
    }

    if (trig_cmd_buf->data_len == UPDATE_FILE_NAME_LEN)
    {
        switch (dev_type)
        {
        case BOTTOM_BCCU_TYPE:
            rt_snprintf_s(p_file_name, UPDATE_FILE_NAME_LEN, UPDATE_FILE_NAME);
            if (rt_strncmp((char *)p, UPDATE_FILE_NAME, UPDATE_FILE_NAME_LEN) == 0)
            {
                return BOTTOM_RTN_APP_CORRECT;
            }
            break;
        }
    }
    return DownFileNameErr;
}

Static int parse_update_trig_old(void *dev_inst, void *cmd_buf)
{

    s_trig_ctr_inf.rtn = get_app_download_rtn(cmd_buf);

    if (s_trig_ctr_inf.rtn == BOTTOM_RTN_APP_CORRECT)
    {
        bccu_update_trigger();
    }

    return SUCCESSFUL;
}

Static int pack_update_trig_old(void *dev_inst, void *cmd_buf)
{
    cmd_buf_t *trig_cmd_buf = (cmd_buf_t *)cmd_buf;
    bottom_comm_cmd_head_t *proto_head = NULL;

    // 入参校验
    if (trig_cmd_buf == NULL || trig_cmd_buf->buf == NULL)
    {
        return FAILURE;
    }

    proto_head = (bottom_comm_cmd_head_t *)trig_cmd_buf->cmd->ack_head;
    proto_head->append = 0x55AA;

    if (s_trig_ctr_inf.rtn == DownloadFrameErr)
    {
        // 附加码错误
        trig_cmd_buf->data_len = 0;
        trig_cmd_buf->rtn = BOTTOM_RTN_CMD_ERROR;
        return SUCCESSFUL;
    }
    else
    {
        // 文件名错误或正确，回复正确文件名
        trig_cmd_buf->data_len = UPDATE_FILE_NAME_LEN;
        rt_memset_s(trig_cmd_buf->buf, trig_cmd_buf->data_len, 0x00, trig_cmd_buf->data_len);
        rt_memcpy_s(trig_cmd_buf->buf, sizeof(UPDATE_FILE_NAME) - 1, UPDATE_FILE_NAME, sizeof(UPDATE_FILE_NAME) - 1);
    }

    return SUCCESSFUL;
}

int8_t BeginDownload(uint8_t ucMode)
{
    uint8_t aucBuff[32] = {
        0,
    };
    uint32_t boudMode = BAUD_RATE_9600;
    T_FileManageStruct tFileManage;
    rt_spi_flash_device_t dev_w25q;
    uint8_t *pFlag = (uint8_t *)FLAG_PROGRAM;
    const struct fal_flash_dev *flash_dev = NULL;
    flash_dev = fal_flash_device_find("gd32_onchip");
    if (flash_dev == RT_NULL)
    {
        return FAILURE;
    }
    dev_w25q = (rt_spi_flash_device_t)rt_device_find("GD25Q128");

    if (RT_NULL == dev_w25q)
    {
        return FAILURE;
    }
    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);

    sfud_read((sfud_flash *)dev_w25q->user_data, UPDATEINFO_STATRT, sizeof(T_FileManageStruct), (uint8_t *)&tFileManage);

    if (ucMode == FLAG_BACKUP)
    {
        if (*pFlag == 0xFF) // 烧写后未备份
        {
            tFileManage.tFileAttr.wTotalFrameNum = MAX_PACKET_NUM; //(count-0x8020000)/0x100+1;
            flash_dev->ops.write((FLAG_PROGRAM - GD32F4_FLASH_BASE), aucBuff, 32);
        }
        else if (tFileManage.ucFlag != FLAG_APP)
        {
            rt_mutex_release(s_ptMutexFile);
            return SUCCESSFUL;
        }
    }

    rt_memcpy_s(tFileManage.tFileAttr.acFileName, FILE_NAME_LEN, UPDATE_FILE_NAME, UPDATE_FILE_NAME_LEN);
    tFileManage.ucUpdateAddr = get_host_addr();
    tFileManage.ucFlag = ucMode;
    tFileManage.wBaudRate = boudMode;
    tFileManage.wCrc = crc_cal((uint8_t *)&tFileManage, offsetof(T_FileManageStruct, wCrc));
    tFileManage.wCrc = (tFileManage.wCrc % 256) * 256 + tFileManage.wCrc / 256;
    sfud_erase_write((sfud_flash *)dev_w25q->user_data, 0, sizeof(T_FileManageStruct), (uint8_t *)&tFileManage);
    rt_mutex_release(s_ptMutexFile);

    // rt_thread_mdelay(100);
    NVIC_SystemReset();

    return SUCCESSFUL;
}

Static int bccu_update_trigger(void)
{
    s_trig_ctr_inf.trig_repeat++;

    if (s_trig_ctr_inf.trig_repeat >= TRIGGER_COUNTER)
    {
        if (s_trig_ctr_inf.trig_repeat == TRIGGER_COUNTER)
        {
            s_trig_ctr_inf.rtn = TriggerEnd;
        }
        else
        {
            s_trig_ctr_inf.rtn = JumpBootFail;
        }
    }

    return SUCCESSFUL;
}

uint8_t get_update_trigger(void)
{
    return s_trig_ctr_inf.trig_repeat;
}

uint8_t check_update_timeout(void)
{
    if (s_trig_ctr_inf.trig_repeat == 0)
    {
        s_trig_ctr_inf.trig_timeout = 0;
        return SUCCESSFUL;
    }

    if (++s_trig_ctr_inf.trig_timeout > 150)
    {
        s_trig_ctr_inf.trig_repeat = 0;
        s_trig_ctr_inf.trig_timeout = 0;
    }

    return SUCCESSFUL;
}
