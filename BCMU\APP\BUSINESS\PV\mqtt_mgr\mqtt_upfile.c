#include "mqtt_upfile.h"
#include "mqtt_utils.h"

#include "storage.h"
#include "webclient.h"
#include "utils_flag.h"
#include "msg_id.h"

static file_name_map_t g_file_name_map[] =
{
    {HIS_EVENT_FILE_TYPE, {HIS_EVENT_FILE_0, HIS_EVENT_FILE_1, HIS_EVENT_FILE_2, HIS_EVENT_FILE_3, HIS_EVENT_FILE_4, HIS_EVENT_FILE_5, HIS_EVENT_FILE_6}, 7},
    {HIS_DATA_FILE_TYPE, {HISDATA_CONFIG_BIN, HIS_DATA_FILE_0, HIS_DATA_FILE_1, HIS_DATA_FILE_2, HIS_DATA_FILE_3, HIS_DATA_FILE_4, HIS_DATA_FILE_5, HIS_DATA_FILE_6}, 8},
    {IV_DATA_TYPE, {IV_DATA_FILE}, 1},
    {RECORD_FAULT_FILE_TYPE, {RECORD_FAULT_FILE_0, RECORD_FAULT_FILE_1, RECORD_FAULT_FILE_2, RECORD_FAULT_FILE_3, RECORD_FAULT_FILE_4, RECORD_FAULT_FILE_5, RECORD_FAULT_FILE_6, RECORD_FAULT_FILE_7, RECORD_FAULT_FILE_8, RECORD_FAULT_FILE_9}, 10},
    {HIS_ALARM_FILE_TYPE, {HIS_ALARM_FILE_0, HIS_ALARM_FILE_1, HIS_ALARM_FILE_2, HIS_ALARM_FILE_3, HIS_ALARM_FILE_4, HIS_ALARM_FILE_5, HIS_ALARM_FILE_6}, 7},
    {POWER_FILE_TYPE, {YEAR_FILE, MONTH_FILE, DAY_FILE, HOUR_FILE}, 4},
    {ALM_PARA_FILE_TYPE, {ALMPARA_PART}, 1},
    {NUM_PARA_FILE_TYPE, {NUMPARA_PART}, 1},
    {STR_PARA_FILE_TYPE, {STRPARA_PART}, 1} ,
    {LOG_FILE_TYPE, {LOG_FILE1_PART, LOG_FILE2_PART}, 2},
    {AFCI_FILE_TYPE, {AFCI_FILE_PART}, 1}
};
#define FILE_MAP_NUM    sizeof(g_file_name_map) / sizeof(file_name_map_t)

int is_file_exit(file_name_map_t* file_name_map , const char* file_name[])
{
    struct stat stat_file = {0};
    int file_nums = 0 ;

    for(int file_loop = 0 ; file_loop < file_name_map->num ; file_loop++ )
    {
        if ( storage_stat(file_name_map->file_name[file_loop] , &stat_file) != MQTT_SUCCESS )
        {
            continue;
        }
        file_name[file_nums] = file_name_map->file_name[file_loop];
        file_nums++;
    }
    return file_nums;
}

int pack_up_file(int which_tls, char* url , int file_type)
{
    int ret = -MQTT_NO_FILE_ERR;
    const char* tmp[16] ={0};
    int file_nums = 0;
    if (url == NULL)
    {
        return -MQTT_NULL_ERR;
    }

    //判断iv扫描文件是否可获取
    if (file_type == IV_DATA_TYPE)
    {
        if (judge_iv_file_exit() == FALSE)
            return -MQTT_NO_FILE_ERR;
    }

    for (int i = 0 ; i < FILE_MAP_NUM; i++)
    {
        if (file_type == g_file_name_map[i].file_type)
        {
            file_nums = is_file_exit(&g_file_name_map[i] , tmp);
            //校验文件是否存在
            if( file_nums <= 0)
                return -MQTT_NO_FILE_ERR;
            ret = webclient_post_file(which_tls, url , tmp , file_nums, "");
        }
    }
    // 文件上送完成后，清除AFCI文件状态
    if(file_type == AFCI_FILE_TYPE && ret >= 0)
    {
        send_msg_to_thread(AFCI_FILE_EXPORT_MSG, MOD_SYS_MANAGE, NULL, 0);
    }
    return ret;
}

int handle_up_file(void* client, message_data_t* msg)
{
    char pub_topic[MQTT_TOPIC_LEN_MAX] = {0};
    char site_id[SITE_ID_LEN] = {0};
    char device_id[STR_LEN_16] = {0};
    char uuid[UUID_LEN] = {0};
    cJSON* recv_json = NULL;
    cJSON* send_json = NULL;
    cJSON* fileId = NULL;
    cJSON* file_path = NULL;
    char* url = NULL;
    int ret = 1;
    int file_type = 0;
    int cur_dev = 0;
    int which = 0;
    
    if (client == NULL || msg == NULL || msg->message == NULL)
    {
        return MQTT_NULL_ERR;
    }
    
    rt_kprintf("%s\n",msg->message->payload);
    ret = parse_msg_sn(msg->topic_name, &cur_dev);
    if (ret != MQTT_SUCCESS)
    {
        return MQTT_SCOPE_ERR;
    }
    
    which = which_client(client);
    get_site_id(which, site_id);
    get_device_id(cur_dev, device_id);
    rt_snprintf(pub_topic, MQTT_TOPIC_LEN_MAX,
                    "/zte/up%s/inv/%s/upFile", site_id, device_id);

    recv_json = mqtt_parse_msg(msg->message->payload, uuid);
    if (recv_json == NULL)
    {
        return MQTT_NULL_ERR;
    }
    
    fileId = cJSON_GetObjectItem(recv_json, "fileId");
    file_type = cJSON_GetNumberValue(fileId);
    file_path = cJSON_GetObjectItem(recv_json, "url");
    url = cJSON_GetStringValue(file_path);

    int which_tls = which_client(client);
    mqtt_set_keep_alive_interval((mqtt_client_t*)client, UP_FILE_KEEP_ALIVE_INTERVAL);
    set_stop_his_data_flag(TRUE);
    ret = pack_up_file(which_tls, url, file_type) ;
    set_stop_his_data_flag(FALSE);
    cJSON_Delete(recv_json);
    send_json = cJSON_CreateObject();
    if (send_json == NULL)
    {   
        mqtt_set_keep_alive_interval((mqtt_client_t*)client, MQTT_KEEP_ALIVE_INTERVAL);
        return MQTT_NULL_ERR;
    }
    cJSON_AddStringToObject(send_json, "msgId", uuid);
    cJSON_AddNumberToObject(send_json, "time", mqtt_get_timestamp());
    cJSON_AddNumberToObject(send_json, "fileId", file_type);
    cJSON_AddNumberToObject(send_json, "success", ret < 0 ? 0 :1);
    if (ret < 0)
    {
        cJSON_AddNumberToObject(send_json, "errCode", ret);
    }

    send_mqtt_data(client, pub_topic, send_json);
    cJSON_Delete(send_json);
    mqtt_set_keep_alive_interval((mqtt_client_t*)client, MQTT_KEEP_ALIVE_INTERVAL);
    
    return MQTT_SUCCESS;
}