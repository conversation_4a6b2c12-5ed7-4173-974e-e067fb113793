#ifndef _DEV_CSU_COMM_H
#define _DEV_CSU_COMM_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include <string.h>
#include "device_type.h"
#include "sps.h"
#include "protocol_layer.h"
#include "realdata_id_in.h"
#include "utils_data_transmission.h"
#include "utils_rtthread_security_func.h"
#include "utils_data_type_conversion.h"

/*-----设备类型-----*/
#define  DEV_ROBOT_BAT      1
#define  DEV_ROBOT_BAT_CAN  2


/* 协议功能码 */
/* 充电器 */
#define CMD_HAND_SHAKE             0x80      // 握手命令
#define CMD_HAND_SHAKE_RESULT      0x81      // 握手结果
#define CMD_PARA_CONF              0x82      // 参数配置
#define CMD_PARA_CONF_RESULT       0x83      // 参数配置结果
#define CMD_GET_CHARGE_STATUS      0x84      // 获取充电状态
#define CMD_CHARGE_END             0x85      // 充电终止
#define CMD_CHARGE_STAT            0x86      // 充电统计
/* 上位机 */
#define CMD_GET_ANA_DATA           0x60      // 获取模拟量
#define CMD_GET_STATUS             0x61      // 获取状态量
#define CMD_GET_ALARM              0x62      // 获取告警量
#define CMD_SET_PARA               0x63      // 设置参数量
#define CMD_GET_PARA               0x64      // 获取参数量
#define CMD_CTRL_ORDER             0x65      // 控制命令
#define CMD_GET_FACTORY            0x66      // 获取厂家信息
#define CMD_GET_HIST_DATA          0x67      // 获取历史数据
#define CMD_GET_HIS_ALM            0x68      // 获取历史告警
#define CMD_GET_OPE_REC            0x69      // 获取操作记录
#define CMD_GET_CELL_STA_REC       0x6A      // 获取电芯统计记录
#define CMD_GET_CHG_REC            0x6B      // 获取充电记录
/* 主控 */
#define CMD_GET_BATT_INFO          0x70      // 获取电池信息



/* 远端通信协议命令ID:唯一标示一个命令 */
#define HAND_SHAKE            1        ///<  握手命令
#define HAND_SHAKE_RESULT     2        ///<  握手结果
#define PARA_CONF             3        ///<  参数配置
#define PARA_CONF_RESULT      4        ///<  参数配置结果
#define GET_CHARGE_STATUS     5        ///<  获取充电状态
#define CHARGE_END            6        ///<  充电终止
#define CHARGE_STAT           7        ///<  充电统计
#define GET_FACTORY_INFO      8
#define CTRL_ORDER            9
#define GET_STATUS            10
#define GET_REAL_DATA         11
#define GET_ALARM             12
#define GET_PARA              13
#define SET_PARA              14
#define GET_HIS_DATA          15      // 获取历史数据
#define GET_HIS_ALM           16      // 获取历史告警
#define GET_OPE_REC           17      // 获取操作记录
#define GET_CELL_STA_REC      18      // 获取电芯统计记录
#define GET_CHG_REC           19      // 获取充电记录
#define GET_BATT_INFO         20      // 获取电池信息

#define HAND_SHAKE_LEN        18  ///<  握手字节长度
#define HAND_SHAKE_ACK_LEN    23  ///<  握手应答字节长度
#define HAND_SHAKE_RESULT_LEN 1   ///<  握手结果字节长度
#define PARA_CONF_LEN         8   ///<  参数配置字节长度
#define PARA_CONF_ACK_LEN     16  ///<  参数配置应答字节长度
#define PARA_CONF_RESULT_LEN  1   ///<  参数配置结果字节长度
#define CHARGE_STATUS_LEN     9   ///<  充电状态字节长度
#define CHARGE_STATUS_ACK_LEN 29  ///<  充电状态应答字节长度
#define CHARGE_END_LEN        3   ///<  充电终止字节长度
#define CHARGE_END_ACK_LEN    3   ///<  充电终止应答字节长度
#define CHARGE_STAT_LEN       8   ///<  充电统计字节长度
#define CHARGE_STAT_ACK_LEN   10  ///<  充电统计应答字节长度
#define BATT_INFO_LEN         3   ///<  电池信息字节长度
#define BATT_INFO_ACK_LEN     14  ///<  电池信息应答字节长度
#define PARA_LEN              214 ///<  参数长度
#define CHG_REC_LEN           18  ///<  充电记录长度
#define CELL_TEMP_RANGE_NUM   7   ///<  温度范围数量
#define SOC_RANGE_NUM         5   ///<  SOC范围数量
#define HIS_DATA_LEN          38  ///<  历史数据长度
#define CELL_STA_REC_LEN      (CELL_TEMP_RANGE_NUM * SOC_RANGE_NUM * 4 + 24) ///<  电芯统计记录长度


#define CMD_TAB_NUM           30  // tab表可以存放29个命令
#define CELL_NUM              8   // 单体数量
#define FACTORY_CMD_LEN       172
#define STATUS_CMD_LEN        2
#define REAL_DATA_CMD_LEN     (53 + 2*CELL_NUM + 2*CELL_NUM)
#define REC_TIME_LEN          15  // 历史数据记录时间长度14+段字节数1

#define GET_RECORD_TIME_SLOT  0x00   // 获取时间段的历史记录
#define GET_RECORD_CORRECT    0x01   // 收到历史记录正确，上送下一条历史记录
#define GET_RECORD_WRONG      0x02   // 收到历史记录错误，重发上一条历史记录
#define GET_RECORD_ALL        0x03   // 接收完所有的历史记录
#define GET_RECORD_NUM        0x04   // 获取特定时间段历史记录条数

#define TIME_NO_MORE_THAN_TARGET 0 // 待比较时间 <= 目标时间
#define TIME_MORE_THAN_TARGET    1 // 待比较时间 >  目标时间

#define HIS_DATA_REC          0 // 历史数据
#define HIS_ALM_REC           1 // 历史告警
#define HIS_OPE_REC           2 // 历史操作记录

#define FRAME_TYPE_NORMAL     0 // 正常发送一条电池记录
#define FRAME_TYPE_LAST       1 // 发送最后一条电池记录



typedef struct {
    unsigned char  cmd_type;
    unsigned char  data_type;
    unsigned char  precision;
    unsigned short para_id;
} para_cmd_t;



typedef struct {
    unsigned char  rec_type;
    unsigned char  cmd_type;
    void (*pack_his_rec) (unsigned char* buf, unsigned int* data_len);
} his_rec_t;


dev_type_t* init_dev_robot_bat_can(void);
int register_cmd_tab(cmd_t* cmd_tab, int cmd_num);
int register_csu_tab();
int register_main_ctrl_tab();
int register_charger_tab();
int parse_control_cmd(void* dev_inst, void* cmd_buff);
int pack_alarm_cmd(void* dev_inst, void* cmd_buff);
int parse_para_cmd(void* dev_inst, void* cmd_buff);
int parse_his_rec(void* dev_inst, void* cmd_buff);
int pack_his_data(void* dev_inst, void* cmd_buff);
int pack_his_alm(void* dev_inst, void* cmd_buff);
int pack_his_ope(void* dev_inst, void* cmd_buff);
int pack_cell_sta_rec(void* dev_inst, void* cmd_buff);
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif 
