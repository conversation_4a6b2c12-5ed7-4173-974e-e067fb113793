/**************************************************************************
* Copyright (C) 2010-2011, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：ZXDU18 CTL板软件
* 文件名称：para.h
* 文件说明：参数模块头文件
* 作    者：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要
* 其他说明：
***************************************************************************/
#ifndef SOFTWARE_SRC_APP_PARA_H_
#define SOFTWARE_SRC_APP_PARA_H_
#include "common.h"
#include "battery.h"
#ifdef __cplusplus
extern "C" {
#endif

typedef enum
{
    PARA_TYPE_FLOAT = 0,
    PARA_TYPE_WORD,
    PARA_TYPE_BOOLEAN,
    PARA_TYPE_BYTE,
	PARA_TYPE_T_DateStruct,
    PARA_TYPE_ULONG,
} ParaType;

typedef enum
{
    CHANGE_BY_YD1363=0,
    CHANGE_BY_MDBUS,
    CHANGE_BY_CAN,
    CHANGE_BY_INIT,
    CHANGE_BY_LOAD,
    CHANGE_BY_SWITCH,
    CHANGE_BY_GPRS,
    CHANGE_BY_APPTEST,
    CHANGE_BY_SNMP,
    CHANGE_BY_QTPTEST,
    CHANGE_BY_LOCATE_INIT,
    CHANGE_BY_AUTO,
    CHANGE_BY_UNKOWN,
}T_ChangeByEnum;

// #define PARA_TYPE_FLOAT 0
#define BDCU_DISCHARGE_OVER_CURR ((FLOAT)120.0f)

#define ONE_WEEK_DAYS                   7
#define ONE_DAY_SECONDS                 (24*60*60)
#define ONE_WEEK_SECONDS                (ONE_WEEK_DAYS*ONE_DAY_SECONDS)

#define PARA_SAVE_SEGMENT_NUM          2

#define MAX_STRING_PARA_SAVE_LEN       40

#define MAX_NUMERICAL_PARA_SAVE_NUM_1  160
#define MAX_STRING_PARA_SAVE_NUM_1     30
#define MAX_ALARM_PARA_SAVE_LEN_1      116

#define MAX_NUMERICAL_PARA_SAVE_NUM_2  40
#define MAX_STRING_PARA_SAVE_NUM_2     10
#define MAX_ALARM_PARA_SAVE_LEN_2      14

#define MAX_NUMERICAL_PARA_SAVE_NUM     (MAX_NUMERICAL_PARA_SAVE_NUM_1 + MAX_NUMERICAL_PARA_SAVE_NUM_2)
#define MAX_STRING_PARA_SAVE_NUM        (MAX_STRING_PARA_SAVE_NUM_1 + MAX_STRING_PARA_SAVE_NUM_2)
#define MAX_ALARM_PARA_SAVE_LEN         (MAX_ALARM_PARA_SAVE_LEN_1 + MAX_ALARM_PARA_SAVE_LEN_2)

#define PARA_SECTION_ALM_RELAY 0x04
#define PARA_SECTION_ALM_LEVEL 0x05
#define PARA_ALM_START_INDEX 0x80

#define AUTO_UNLOCK 0 //通讯自动解锁
#define MANUAL_UNLOCK 1 //人工解锁


//保存在EEPROM中的本地参数
typedef struct  
{ 
    BYTE   ucLanguage; 
    BYTE    uc1104ProtocolType;       //yyh增加1104协议类型
    BYTE    ucRS232Bps2;            // 串口2波特率  ***
}T_LocalPara;

typedef struct  
{
  T_LocalPara   LocalPara;
  BYTE          crc_h;      //CRC校验高字节 
  BYTE          crc_l;      //CRC校验低字节
}T_LocalParaType;

typedef struct  
{
    BOOLEAN     bValid;                 //当前存储值是否有效
    FLOAT       afBattCap[BATT_NUM];    //存储电池容量
    FLOAT       fBattTotalCap;  //存储电池容量
    BYTE        crc_h;                  //CRC校验高字节
    BYTE        crc_l;                  //CRC校验低字节
}T_SaveCapStruct;

//以上为存储在EEPROM中的参数，分20个区循环存储
//以下为标识存储位置的指针结构在RAM中维护
typedef struct  
{
    BYTE        ucTimePoint;    //存储时间指针
    BYTE        ucCapPoint;     //存储电池容量指针
}T_SavePointStruct;

//数值型参数保存的数据
//数值型参数字节长度固定，数量可能增减
typedef struct
{
	WORD		wID;					//参数唯一ID
	BYTE		ucAccuracy;				//参数精度（先保存，但是精度上的变化较复杂，暂不作处理）
	BYTE		ucLen;					//参数字节长度
	BYTE		aucVal[4];				//参数值（最大长度为4）
}T_NumericalParaSaveItem;

//长字符型参数保存的数据（除去告警干接点和告警级别）
//长字符型参数字节长度可能增减，数量也可能增减
typedef struct
{
	WORD		wID;					                    //参数唯一ID
	BYTE		ucLen;										//参数字节长度
	BYTE		aucVal[MAX_STRING_PARA_SAVE_LEN];			//参数值
}T_StringParaSaveItem;

//告警参数保存的数据（告警干接点和告警级别）
//告警参数字节长度较长，单独拿出来，其数量不变，但是字节长度可能增减
#pragma pack(push, 1)
typedef struct
{
    WORD wID;
    BYTE ucLen;
} T_AlarmParaSaveItemHead;
#pragma pack(pop)

typedef struct
{
	WORD		wID;
	BYTE		ucLen;
	BYTE		aucVal[MAX_ALARM_PARA_SAVE_LEN_1];
} T_AlarmParaSaveItem1;

typedef struct
{
	WORD		wID;
	BYTE		ucLen;
	BYTE		aucVal[MAX_ALARM_PARA_SAVE_LEN_2];
} T_AlarmParaSaveItem2;

//参数保存内容
typedef struct
{
    WORD						wNumericalParaNum;											//实际存入的数值型参数个数
	WORD						wStringParaNum;												//实际存入的长字符型参数个数
	WORD						wAlarmParaNum;
	T_NumericalParaSaveItem		atNumericalParaList[MAX_NUMERICAL_PARA_SAVE_NUM_1];
	T_StringParaSaveItem		atStringParaList[MAX_STRING_PARA_SAVE_NUM_1];
	T_AlarmParaSaveItem1		atAlarmParaList[2];
    WORD        				wCrc;
} T_SaveParaSegment1;

typedef struct
{
	WORD						wNumericalParaNum;											//实际存入的数值型参数个数
	WORD						wStringParaNum;												//实际存入的长字符型参数个数
	WORD						wAlarmParaNum;
	T_NumericalParaSaveItem		atNumericalParaList[MAX_NUMERICAL_PARA_SAVE_NUM_2];
	T_StringParaSaveItem		atStringParaList[MAX_STRING_PARA_SAVE_NUM_2];
	T_AlarmParaSaveItem2		atAlarmParaList[2];
    WORD        				wCrc;
} T_SaveParaSegment2;

#pragma pack(push, 1)
typedef struct
{
    T_SaveParaSegment1 segment1;
    T_SaveParaSegment2 segment2;
} T_SavePara;
#pragma pack(pop)

typedef struct ParaSerializationContext
{
    WORD ParaID;
    WORD ParaSN;
    WORD ParaOffset;
    WORD NumericalParaNum;
    WORD StringParaNum;
    WORD AlarmParaNum;
    BYTE ParaLen;
    BYTE ParaAccuracy;
} T_ParaSerializationContext;

typedef struct ParaSegmentInfo
{
    void *itemStartAddr;
    WORD *savedNum;
    WORD offset;
    WORD capacity;
} T_ParaSegmentInfo;

typedef struct AlarmParaSegmentInfo
{
    T_ParaSegmentInfo segment[PARA_SAVE_SEGMENT_NUM];
} T_AlarmParaSegmentInfo;

typedef struct
{
    BOOLEAN bValid;             ////有效
    BYTE    ucPos;                //补偿位置
    FLOAT   fCompValue;             ////补偿值
//
}T_CellCompValAndPosStruct;

typedef uint16_t ParaId;

typedef struct ParaProperty
{
    ParaType elementType;
    size_t elementNum;
    size_t elementSize;
    size_t offset;
} ParaProperty;

/*********************  函数原型定义  **********************/
void GetHardwarePara(T_HardwareParaStruct *ptHardwarePara);
void GetParaMax(BYTE ID_PARA, FLOAT *fRet);
void GetParaMin(BYTE ID_PARA, FLOAT *fRet);
void GetSysPara(T_SysPara *ptSysPara);
void GetSysDefaultPara(T_SysPara *ptSysPara);
BOOLEAN ChkRamPara(void);
BOOLEAN InitSysPara(void);
BOOLEAN TrySetSysPara(T_SysPara *ptSysPara, BOOLEAN bChk, T_ChangeByEnum ChangeBy);
BOOLEAN ExecuteSaveSysPara(T_SysPara *ptSysPara, BOOLEAN bChk, T_ChangeByEnum ChangeBy);
BOOLEAN SetSysPara(T_SysPara *ptSysPara, BOOLEAN bChk, T_ChangeByEnum ChangeBy);
void LoadPara(void);
void WriteBMSDefaultPara(T_SysPara *ptSysPara);
void GetBmsSysName(CHAR *pacSysName, BYTE NameSize);
void SaveHisOpID(U_Int *uData, rt_uint16_t ID);
void InitAesKey(void);
void JudgeBuzzEnable( void );
BOOLEAN ChkSysPara(T_SysPara *ptSysPara);
T_SysPara *GetSysParaPointer(void);
#ifdef UNITEST
Static BOOLEAN  ChkSysPara( T_SysPara  *tSysPara );
#endif

int GetParaById(ParaId id, void *dest, size_t len, size_t (*converter)(ParaType type, void *value));
int SetParaById(ParaId id, const void *src, size_t len, size_t (*converter)(ParaType type, void *value));
int GetMultiParaBySection(uint8_t section, void *dest, size_t len, size_t (*converter)(ParaType type, void *value));
BOOLEAN GetParaPropertyById(ParaId id, ParaProperty *property);
uint8_t GetParaIndexById(ParaId id);
uint8_t GetParaSectionById(ParaId id);
ParaId MakeParaId(uint8_t section, uint8_t index);
ssize_t FindParaById(ParaId id);
size_t EndianConvert(ParaType type, void *value);
BOOLEAN IsValidParaType(int type);
BOOLEAN SetParaSaveFlag(BOOLEAN bParaSaveFlag);
BOOLEAN GetParaSaveFlag(void);
BOOLEAN SaveTempSysPara(T_SysPara *ptTempSyspara);
BOOLEAN GetTempSysPara(T_SysPara *ptTempSyspara);
BOOLEAN CheckBattEnableDate(T_BattInfo *pBattIn);

#ifdef DEVICE_USING_R321
int InitDefaultPara(void);
#endif

#ifdef GPS_BEIDOU_SET
BYTE getDefLocateMode(void);
#endif
#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // SOFTWARE_SRC_APP_PARA_H_;
