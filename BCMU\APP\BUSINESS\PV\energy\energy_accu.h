/**
 * @file     sample_handle.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2023-06-06
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */

 
#ifndef _ENERGY_ACCU_H
#define _ENERGY_ACCU_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include <stdio.h>
#include <rtthread.h>
#include <rtdevice.h>
#include <string.h>
#include "storage.h"
#include "device_type.h"
#include "dev_dc_ac.h"


#define ACCUM_ENERGY_PERIOD    1000  // 1s
#define MINUTE_10              (10 * 60)   // 10分钟
#define HOUR                   (60 * 60)   // 一小时

#define ENERGY_DATA_VALID      0x55   // 表示数据有效

#define PRECISION      100 // 数据存储的精度

#define CURR_START_ADDR       0
#define CURR_BAK_START_ADDR   sizeof(energy_info_cal_t)

#define SAVE_TURN     14   // 空间轮转写


#define HIS_YEAR        0 // 年历史数据
#define HIS_MONTH       1 // 月数据
#define HIS_DAY         2 // 日数据
#define HIS_HOUR        3 // 小时数据
#define HIS_ENERGY      4 // 历史数据
#define HIS_ENERGY_TYPE_NUM   4

///  新结构
#define RCD_NUM        20    // 一次读取多少记录

#define TIME_UP         1    // 时间上升
#define TIME_SAME       0    // 时间相同
#define TIME_DOWN       -1   // 时间下降


#define HOUR_SAME       0    // 小时相同
#define DAY_SAME        1    // 日相同
#define MONTH_SAME      2    // 月相同
#define YEAR_SAME       3    // 年相同
#define YEAR_DIFF       4    // 年不同

// 模拟eeprom
#define ENERGY_START_ADDR 0x00
// #define ENERGY_PART   "energy"    // 电量分区

#define CO2_FACTOR     0.997      // CO2和电量转换

// 时发电量，保存30天的
#pragma pack(1)
typedef struct
{   

    double total_accum_energy;             // 累计发电量   // 产线清零
    double curr_year_energy;               // 当年发电量
    double curr_month_energy;              // 当月发电量
    double curr_day_energy;                // 当日发电量
    double curr_hour_energy;                // 小时发电量
    time_base_t curr_time;                 // 最后一个数据的日期
    unsigned short valid;                  // 表示这个数据有效
    unsigned short crc;
}energy_info_cal_t;

typedef struct
{
    char* file_name;
    char* file_name_tmp;
    int record_size;
    int (*is_time_same)(time_base_t* start, time_base_t* end);
    int time_cmp_status;

}his_energy_record_t;

/**
 * 历史数据存储结构体
 */
typedef struct
{
    time_base_t save_time;  //保存时间
    float energy;
    unsigned short crc;
}energy_record_t;
#pragma pack()


#define HOUR_RECORD_MAX      (30*24)   // 30天    实际文件保存 2* HOUR_RECORD_MAX
#define HOUR_RECORD_SIZE     (HOUR_RECORD_MAX * sizeof(energy_record_t))

#define DAY_RECORD_MAX      (12*30)   // 12月    实际文件保存 2* DAY_RECORD_MAX
#define DAY_RECORD_SIZE     (DAY_RECORD_MAX * sizeof(energy_record_t))

#define MONTH_RECORD_MAX      (25*12)   // 25年    实际文件保存 2* MONTH_RECORD_MAX
#define MONTH_RECORD_SIZE     (MONTH_RECORD_MAX * sizeof(energy_record_t))

#define YEAR_RECORD_MAX      (25)   // 25年    实际文件保存 2* YEAR_RECORD_MAX
#define YEAR_RECORD_SIZE     (YEAR_RECORD_MAX * sizeof(energy_record_t))

void energy_accum_init(void);
char* get_eeprom_dev();
void accum_energy_period(dev_inst_t* dev_dc_ac);
energy_info_cal_t* get_his_energy(void);
void save_record_restart(void);
int clean_his_energy();
void data_init(time_base_t* curr_time);
void his_energy_move_zero(energy_info_cal_t* his_energy);
void write_his_energy(part_data_t* part_data, energy_info_cal_t* his_energy);
float get_energy_by_cal(int type, time_base_t* save_time);
int read_last_record(int type, energy_record_t* one_record);
int save_time_same(int type, energy_record_t* one_record);
int save_time_increase(int type, energy_record_t* one_record);
int record_max_check(int type);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _SAMPLE_H