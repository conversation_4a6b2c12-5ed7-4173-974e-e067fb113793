/**************************************************************************
* 文件名称：protocol_v25.c
* 文件说明：固网协议模块文件（协议组包与协议判断等过程隔离）
***************************************************************************/
#include "common.h"
#include "protocol.h"
#include "rtthread.h"
#include "hisdata.h"
#include "fileSys.h"
#include "sample.h"
#include "para.h"
#include "flash.h"
#include "CommCan.h"
#include "commBdu.h"
#include "wireless.h"
#include "realAlarm.h"
#include "sys/time.h"
#include "apptest.h"
#include "protocol_v25.h"
#include "utils_rtthread_security_func.h"

static T_SysPara s_tSysPara;

// CID2 function
static void GetAnalog_V25(BYTE ucPort);                 // 获取模拟量量化后的数据（定点数）
static void GetAlarm_V25(BYTE ucPort);                  // 获取告警量
static void GetSystemPara_V25(BYTE ucPort);             // 获取系统参数（定点数）
static void SetSystemPara_V25(BYTE ucPort);             // 设定系统参数（定点数）
static void SendProtocolVer_V25( BYTE ucPort );         // 获取通信协议版本号(不支持)
static void SendFactoryInfo_V25( BYTE ucPort );         // 获取设备厂家信息
static void GetSpecialPara_V25(BYTE ucPort);            // 获取电池特定参数
static void SetSpecialPara_V25(BYTE ucPort);            // 设置电池特定参数
static void GetPackNum_V25( BYTE ucPort );              // 获取PACK数量(回复RTN = 04H)
static void SetBaudRate_V25( BYTE ucPort );             // 设定通信速率(不支持，不回复)
static void ChargeCurrentCtrl_V25(BYTE ucPort);         // 充电电流控制命令（不支持）
Static void RemoteCtrl_V25(BYTE ucPort);                // 遥控（仅支持防盗功能）
static void GetDigitalNew_V25(BYTE ucPort);             // 获取电池状态量（扩展命令）
static void GetAnalogNew_V25( BYTE ucPort );            // 获取电池模拟量（扩展命令）
static void GetAlarmNew_V25(BYTE ucPort);               // 获取电池告警量（扩展命令）
static BOOLEAN IsCellFault(BYTE arr[], BYTE length);
static BOOLEAN IsCellConsisAlarm(BYTE index);
static void MemsetBuffWithoutTerm(void *des, void *src, BYTE srcLen, BYTE defaultLen, BYTE defalutValue);
static BOOLEAN SetSysPara_V25_8X(void);
static BOOLEAN SetSysPara_V25_CX(void);
static BOOLEAN SetSysPara_V25_DX(void);
static BOOLEAN SetSysPara_V25_XX(void);
Static BOOLEAN SetSysPara_V25_AntiTheft(void);
Static BYTE ManualCtrlEnterDefenceStatus_V25(void);
Static BOOLEAN ManualAntitheftUnlock_V25(BYTE bType);

const T_CmdFuncStruct CID2AllHardLinkTable[] =
{
    {GET_ANALOG_V25,            GetAnalog_V25,          2},     // 0
    {GET_ALARM_V25,             GetAlarm_V25,           2},     // 1
    {GET_PARA_V25,              GetSystemPara_V25,      2},     // 2
    {SET_PARA_V25,              SetSystemPara_V25,      8},     // 3
    {GET_PROTOCOL_VER_V25,      SendProtocolVer_V25,    0},     // 4
    {GET_FACTORY_INFO_V25,      SendFactoryInfo_V25,    0},     // 5
    {GET_SPECIAL_PARA_V25,      GetSpecialPara_V25,     2},     // 6
    {SET_SPECIAL_PARA_V25,      SetSpecialPara_V25,     8},     // 7
    {GET_PACK_PARA_V25,         GetPackNum_V25,         0},     // 8
    {SET_BAUD_RATE_V25,         SetBaudRate_V25,        2},     // 9
    {CHARGE_CURRENT_CTRL_V25,   ChargeCurrentCtrl_V25,  2},     // 10
    {REMOTE_CTRL_V25,           RemoteCtrl_V25,         2},     // 11
    {GET_DIGITAL_NEW_V25,       GetDigitalNew_V25,      2},     // 12
    {GET_ANALOG_NEW_V25,        GetAnalogNew_V25,       2},     // 13
    {GET_ALARM_NEW_V25,         GetAlarmNew_V25,        2},     // 14

    {0x00, 0x0000, 0x00},
};

static const T_CmdFuncStruct* GetCmdFnucStruct_v20(BYTE segment)
{
    return &CID2AllHardLinkTable[segment];
}

/***************************************************************************
 * @brief    获取模拟量量化后的数据（定点数
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
static void GetAnalog_V25(BYTE ucPort)
{
    BYTE *p = NULL;
    BYTE ucCommandGroup = 0;
    T_BCMDataStruct tBCMAnaData = {0};
    BYTE i = 0;
    T_HardwareParaStruct tHWPara;

    rt_memset(&tHWPara, 0, sizeof(T_HardwareParaStruct));
    readBmsHWPara(&tHWPara);
    p = s_tProtocol.aucSendBuf;
    rt_memset_s(&tBCMAnaData, sizeof(tBCMAnaData), 0, sizeof(tBCMAnaData));
    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));
    rt_memset_s(&s_tSysPara, sizeof(T_SysPara), 0, sizeof(T_SysPara));
    GetRealData(&tBCMAnaData);
    GetSysPara(&s_tSysPara);

    if (tHWPara.ucCellVoltNum < 1 || tHWPara.ucCellVoltNum > CELL_VOL_NUM_MAX)
    {
        return;
    }

    *p++ = GetDataFlag(BCM_ALARM, ucPort);

    ucCommandGroup = s_tProtocol.aucRecBuf[7];
    *p++ = ucCommandGroup; // Pack数量

    *p++ = tHWPara.ucCellVoltNum; //电池节数(单体电池数量)

    for (i = 0; i < tHWPara.ucCellVoltNum; i++)
    {
        *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.afCellVolt[i] * 1000); //单体电压
        p += 2;
    }

    *p++ = CELL_TEMP_NUM; //电芯温度数量

    for (i = 0; i < CELL_TEMP_NUM; i++)
    {
        *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.afCellTemp[i] * 10 + 2731); //电芯温度数据
        p += 2;
    }

    *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.fBusCurr * 100); //电池电流，单位为10mA，以母排电流上送
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.fExterVolt * 1000); //Pack总电压，单位为mV，以母排电压上送
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.wRemainCap * 100); //电池剩余容量，单位为10mAH
    p += 2;

    *p++ = 0x06; //用户自定义数量

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.wBatteryCap * 100); //电池总容量
    p += 2;

    *(SHORT *)p = Host2Modbus((SHORT *)&tBCMAnaData.wBattCycleTimes); //电池循环次数
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.fEnvTemp * 10 + 2731); //环境温度
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.fBoardTemp * 10 + 2731); //PCB板温度
    p += 2;

    *p++ = 0;
    *p++ = 0x0A; //特殊单位标识数(2字节，传输10)

    *p++ = 0;
    *p++ = GetDefenceStatus(); //防盗布防状态

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    return;
}

/***************************************************************************
 * @brief    获取告警量
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
static void GetAlarm_V25(BYTE ucPort)
{
    BYTE *p = NULL;
    BYTE i = 0;
    BYTE ucCommandGroup = 0;
    T_BCMAlarmStruct tBCMAlm;
    T_BCMDataStruct tBCMData;
    T_HardwareParaStruct tHWPara;

    rt_memset(&tBCMData, 0, sizeof(T_BCMDataStruct));
    rt_memset(&tBCMAlm, 0, sizeof(T_BCMAlarmStruct));
    rt_memset(&tHWPara, 0, sizeof(T_HardwareParaStruct));

    GetRealData(&tBCMData);
    GetSysPara(&s_tSysPara);
    GetRealAlarm(BCM_ALARM, (BYTE *)&tBCMAlm);
    readBmsHWPara(&tHWPara);

    if (tHWPara.ucCellVoltNum < 1 || tHWPara.ucCellVoltNum > CELL_VOL_NUM_MAX)
    {
        return;
    }

    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    *p++ = GetDataFlag(BCM_ALARM, ucPort);

    ucCommandGroup = s_tProtocol.aucRecBuf[7];
    *p++ = ucCommandGroup; // Pack数量

    *p++ = tHWPara.ucCellVoltNum;             // 单体电压数量
    for (i = 0; i < tHWPara.ucCellVoltNum; i++) // 单体电压告警
    {
        *p++ = tBCMAlm.aucCellUnderVoltAlm[i] + tBCMAlm.aucCellOverVoltAlm[i] * 2;
    }
    for (i = 0; i < tHWPara.ucCellVoltNum; i++) // 单体电压保护
    {
        *p++ = tBCMAlm.aucCellUnderVoltPrt[i] + tBCMAlm.aucCellOverVoltPrt[i] * 2;
    }
    for (i = 0; i < tHWPara.ucCellVoltNum; i++) // 单体一致性差保护(分单体判断，仅用于固网协议)
    {
        *p++ = IsCellConsisAlarm(i);
    }
    *p++ = tBCMAlm.ucBattUnderVoltAlm + tBCMAlm.ucBattOverVoltAlm * 2; // 电池组电压告警
    *p++ = tBCMAlm.ucBattUnderVoltPrt + tBCMAlm.ucBattOverVoltPrt * 2; // 电池组电压保护
    *p++ = CELL_TEMP_NUM;                                              // 单体温度数量
    for (i = 0; i < CELL_TEMP_NUM; i++)                                // 单体充电温度告警
    {
        *p++ = tBCMAlm.aucChgTempLowAlm[i] + tBCMAlm.aucChgTempHighAlm[i] * 2;
    }
    for (i = 0; i < CELL_TEMP_NUM; i++) // 单体充电温度保护
    {
        *p++ = tBCMAlm.aucChgTempLowPrt[i] + tBCMAlm.aucChgTempHighPrt[i] * 2;
    }
    for (i = 0; i < CELL_TEMP_NUM; i++) // 单体放电温度告警
    {
        *p++ = tBCMAlm.aucDischgTempLowAlm[i] + tBCMAlm.aucDischgTempHighAlm[i] * 2;
    }
    for (i = 0; i < CELL_TEMP_NUM; i++) // 单体放电温度保护
    {
        *p++ = tBCMAlm.aucDischgTempLowPrt[i] + tBCMAlm.aucDischgTempHighPrt[i] * 2;
    }
    for (i = 0; i < CELL_TEMP_NUM; i++) // 单体温度传感器无效告警
    {
        *p++ = tBCMAlm.ucCellTempSensorInvalidAlm[i];
    }
    *p++ = tBCMAlm.ucEnvTempHighAlm;          // 环境温度高告警
    *p++ = tBCMAlm.ucEnvTempLowAlm;           // 环境温度低告警
    *p++ = tBCMAlm.ucEnvTempSensorInvalidAlm; // 环境温度传感器无效告警
    *p++ = tBCMAlm.ucChgCurrHighAlm;          // 电池组充电过流告警
    *p++ = tBCMAlm.ucChgCurrHighPrt;          // 电池组充电过流保护
    *p++ = tBCMAlm.ucDischgCurrHighAlm;       // 电池组放电过流告警
    *p++ = tBCMAlm.ucDischgCurrHighPrt;       // 电池组放电过流保护
    *p++ = tBCMAlm.ucBattShortCut;            // 电池组短路保护
    *p++ = tBCMAlm.ucDischgLoopInvalid;       // 放电回路开关失效告警
    *p++ = tBCMAlm.ucChgLoopInvalid;          // 充电回路开关失效告警

    //状态指示1
    SetDigitalAlarmBit(0, p, (IsCellFault(tBCMAlm.aucCellOverVoltPrt, CELL_VOL_NUM) || FAULT == tBCMAlm.ucBattOverVoltPrt)); //单体过压保护或总体过压保护
    SetDigitalAlarmBit(1, p, IsCellFault(tBCMAlm.aucCellUnderVoltPrt, CELL_VOL_NUM));                                    //单体欠压保护
    SetDigitalAlarmBit(2, p, FAULT == tBCMAlm.ucChgCurrHighPrt);                                                         //充电过流保护
    SetDigitalAlarmBit(3, p, FAULT == tBCMAlm.ucBattLoseAlm);                                                             //电池丢失告警
    SetDigitalAlarmBit(4, p, FAULT == tBCMAlm.ucDischgCurrHighPrt);
    SetDigitalAlarmBit(5, p, (IsCellFault(tBCMAlm.aucDischgTempHighPrt, CELL_TEMP_NUM) || IsCellFault(tBCMAlm.aucDischgTempLowPrt, CELL_TEMP_NUM))); //放电高温保护或放电低温保护
    SetDigitalAlarmBit(6, p, (IsCellFault(tBCMAlm.aucChgTempHighPrt, CELL_TEMP_NUM) || IsCellFault(tBCMAlm.aucChgTempLowPrt, CELL_TEMP_NUM)));      //充电高温保护或充电低温保护
    SetDigitalAlarmBit(7, p, FAULT == tBCMAlm.ucBattUnderVoltPrt);                                                                                   //总电压欠压保护
    p += 1;

    //状态指示2
    SetDigitalAlarmBit(0, p, False); //preFET指示，直接传输0
    SetDigitalAlarmBit(1, p, False); //CFET指示，直接传输0
    SetDigitalAlarmBit(2, p, False); //DFET指示，直接传输0
    SetDigitalAlarmBit(3, p, False); //使用Pack供电指示,直接传输0
    p += 1;

    //状态指示3，待确认数据正确性
    *p = (((BYTE)((s_tSysPara.bBuzzerEnable > 0) ? 1 : 0)) |               // Bit0:蜂鸣器声指示
          ((BYTE)(tBCMData.bBattFull << 3)) |                              // Bit3:电池充满电指示
          ((BYTE)(0 << 5)) |                                               // Bit5:加热膜启动指示，直接传输0
          ((BYTE)(((!tBCMData.ucBattDischEn) & 0x01) << 6)) |              // Bit6:有效放电电流指示
          ((BYTE)(((!tBCMData.ucBattChargeEn) & 0x01) << 7))) & 0xff;      // Bit7:有效充电电流指示
    p += 1;

    //状态指示4
    *p = ((BYTE)((tBCMAlm.ucCellVoltSampleFault > 0) ? 0xFF : 0)); //单体故障指示，当有电芯采样异常时全部故障，否则全部正常
    p += 1;

    //状态指示5     预留字节
    *p = 0;
    p += 1;

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    return;
}

/***************************************************************************
 * @brief    获取系统参数（定点数）
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
static void GetSystemPara_V25(BYTE ucPort)
{
    BYTE *p = NULL;
    BYTE ucCommandGroup = 0;

    GetSysPara(&s_tSysPara);

    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    *p++ = GetDataFlag(BCM_ALARM, ucPort);

    ucCommandGroup = s_tProtocol.aucRecBuf[7];
    if (ucCommandGroup == 0xFF)
    {
        *p++ = BATT_NUM_BCM; // 铁锂电池组数M，实际未使用FF
    }

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fCellOverVoltAlmThre * 100); // 单体过压告警阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fCellUnderVoltAlmThre * 100); // 单体欠压告警阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fCellOverVoltPrtThre * 100); // 单体过压保护阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fCellUnderVoltPrtThre * 100); // 单体欠压保护阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fBattOverVoltAlmThre * 100); // 电池组过压告警阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fBattUnderVoltAlmThre * 100); // 电池组欠压告警阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fBattOverVoltPrtThre * 100); // 电池组过压保护阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fBattUnderVoltPrtThre * 100); // 电池组欠压保护阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fChgTempHighAlmThre * 100); // 充电温度高告警阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fChgTempLowAlmThre * 100); // 充电温度低告警阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fChgTempHighPrtThre * 100); // 充电温度高保护阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fChgTempLowPrtThre * 100); // 充电温度低保护阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fDischgTempHighAlmThre * 100); // 放电温度高告警阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fDischgTempLowAlmThre * 100); // 放电温度低告警阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fDischgTempHighPrtThre * 100); // 放电温度高保护阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fDischgTempLowPrtThre * 100); // 放电温度低保护阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fChgCurrHighAlmThre * s_tSysPara.wBatteryCap * 2); // 充电过流告警阈值，单位为A
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fChgCurrHighPrtThre * s_tSysPara.wBatteryCap * 2); // 充电过流保护阈值
    p += 2;
    *(SHORT *)p = 0; //放电过流保护阈值读取默认为0，不可设
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fDischgCurrHighAlmThre * s_tSysPara.wBatteryCap * 2); // 放电过流告警阈值
    p += 2;
    *p++ = 15; // 用户自定义参数数量
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fBattUnderVoltPrtRecoThre * 100);       //电池组放电欠压保护恢复阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fCellOverVoltPrtRecoThre * 100); // 单体过压保护恢复阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fCellUnderVoltPrtRecoThre * 100); // 单体欠压保护恢复阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fChgTempHighPrtRecoThre * 100); // 充电过温保护恢复阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fDischgTempHighPrtRecoThre * 100); // 放电过温保护恢复阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fChgTempLowPrtRecoThre * 100); // 充电低温保护恢复阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fDischgTempLowPrtRecoThre * 100); // 放电低温保护恢复阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fCellPoorConsisAlmThre * 100); // 单体落后告警电压差阈值，单体一致性差
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fEnvTempHighAlmThre * 100); // 环境温度高告警阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fEnvTempLowAlmThre * 100); // 环境温度低告警阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fBattOverVoltPrtRecoThre * 100);       //电池组充电过压保护恢复阈值
    p += 2;
    *p++ = 0;
    *p++ = s_tSysPara.ucBattUnlockMode; //电池解锁方式
    *(SHORT *)p = Host2Modbus((SHORT *)&s_tSysPara.wSoftAntiTheftDelay); //防盗延时时间
    p += 2;
    //陀螺仪防盗方式、陀螺仪倾角未使用，默认上送0
    *p++ = 0;
    *p++ = 0; //陀螺仪防盗方式
    *p++ = 0;
    *p++ = 0; //陀螺仪倾角(°)

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    return;
}

static BOOLEAN SetSysPara_V25_8X(void)
{
    FLOAT fPara = 0.0;
    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[8];
    fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9)));
    switch (s_tProtocol.ucCommandType)
    {
        case 0x80:
            s_tSysPara.fCellOverVoltAlmThre = fPara / 100.0; // 单体过压告警阈值
            break;
        case 0x81:
            s_tSysPara.fCellUnderVoltAlmThre = fPara / 100.0; // 单体欠压告警阈值
            break;
        case 0x82:
            s_tSysPara.fCellOverVoltPrtThre = fPara / 100.0; // 单体过压保护阈值
            break;
        case 0x83:
            s_tSysPara.fCellUnderVoltPrtThre = fPara / 100.0; // 单体欠压保护阈值
            break;
        case 0x84:
            s_tSysPara.fBattOverVoltAlmThre = fPara / 100.0; // 电池组过压告警阈值
            break;
        case 0x85:
            s_tSysPara.fBattUnderVoltAlmThre = fPara / 100.0; // 电池组欠压告警阈值
            break;
        case 0x86:
            s_tSysPara.fBattOverVoltPrtThre = fPara / 100.0; // 电池组过压保护阈值
            break;
        default:
            return FALSE;
    }
    return TRUE;
}

static BOOLEAN SetSysPara_V25_CX(void)
{
    FLOAT fPara = 0.0;
    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[8];
    fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9)));
    switch (s_tProtocol.ucCommandType)
    {
        case 0xC4:
            s_tSysPara.fBattUnderVoltPrtThre = fPara / 100.0; // 电池组欠压保护阈值
            break;
        case 0xC5:
            s_tSysPara.fChgTempHighAlmThre = fPara / 100.0; // 充电温度高告警阈值
            break;
        case 0xC6:
            s_tSysPara.fChgTempLowAlmThre = fPara / 100.0; // 充电温度低告警阈值
            break;
        case 0xC7:
            s_tSysPara.fChgTempHighPrtThre = fPara / 100.0; // 充电温度高保护阈值
            break;
        case 0xC8:
            s_tSysPara.fChgTempLowPrtThre = fPara / 100.0; // 充电温度低保护阈值
            break;
        case 0xC9:
            s_tSysPara.fDischgTempHighAlmThre = fPara / 100.0; // 放电温度高告警阈值
            break;
        case 0xCA:
            s_tSysPara.fDischgTempLowAlmThre = fPara / 100.0; // 放电温度低告警阈值
            break;
        case 0xCB:
            s_tSysPara.fDischgTempHighPrtThre = fPara / 100.0; // 放电温度高保护阈值
            break;
        case 0xCC:
            s_tSysPara.fDischgTempLowPrtThre = fPara / 100.0; // 放电温度低保护阈值
            break;
        case 0xCD:
            s_tSysPara.fChgCurrHighAlmThre = fPara / (s_tSysPara.wBatteryCap * 2); // 充电过流告警阈值
            break;
        case 0xCE:
            s_tSysPara.fChgCurrHighPrtThre = fPara / (s_tSysPara.wBatteryCap * 2); // 充电过流保护阈值
            break;
        // case 0xCF:
        //     s_tSysPara.fDischgCurrHighPrtThre = fPara / (s_tSysPara.wBatteryCap * 2); // 放电过流保护阈值
        //     break;
        default:
            return FALSE;
    }
    return TRUE;
}

static BOOLEAN SetSysPara_V25_DX(void)
{
    FLOAT fPara = 0.0;

    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[8];
    fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9)));
    switch (s_tProtocol.ucCommandType)
    {
        case 0xD0:
            s_tSysPara.fDischgCurrHighAlmThre = fPara / (s_tSysPara.wBatteryCap * 2); // 放电过流告警阈值
            break;
        case 0xD1:
            s_tSysPara.fBattUnderVoltPrtRecoThre = fPara / 100.0; // 电池组放电欠压保护恢复阈值
            break;
        case 0xD2:
            s_tSysPara.fCellOverVoltPrtRecoThre = fPara / 100.0; // 单体过压保护恢复阈值
            break;
        case 0xD3:
            s_tSysPara.fCellUnderVoltPrtRecoThre = fPara / 100.0; // 单体欠压保护恢复阈值
            break;
        case 0xD4:
            s_tSysPara.fChgTempHighPrtRecoThre = fPara / 100.0; // 充电过温保护恢复阈值
            break;
        case 0xD5:
            s_tSysPara.fDischgTempHighPrtRecoThre = fPara / 100.0; // 放电过温保护恢复阈值
            break;
        case 0xD6:
            s_tSysPara.fChgTempLowPrtRecoThre = fPara / 100.0; // 充电低温保护恢复阈值
            break;
        case 0xD7:
            s_tSysPara.fDischgTempLowPrtRecoThre = fPara / 100.0; // 放电低温保护恢复阈值
            break;
        case 0xD8:
            s_tSysPara.fCellPoorConsisAlmThre = fPara / 100.0; // 单体落后告警电压差阈值，单体一致性差
            break;
        case 0xD9:
            s_tSysPara.fEnvTempHighAlmThre = fPara / 100.0; // 环境温度高告警阈值
            break;
        case 0xDA:
            s_tSysPara.fEnvTempLowAlmThre = fPara / 100.0; // 环境温度低告警阈值
            break;
        case 0xDB:
            s_tSysPara.fBattOverVoltPrtRecoThre = fPara / 100.0; // 电池组充电过压保护恢复阈值
            break;
        default:
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
            return FALSE;
    }
    return TRUE;
}

Static BOOLEAN SetSysPara_V25_AntiTheft(void)
{
    FLOAT fPara = 0.0;
    T_BCMAlarmStruct tBCMAlm;

    GetRealAlarm(BCM_ALARM, (BYTE *)&tBCMAlm);
    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[8];
    fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9)));
    switch (s_tProtocol.ucCommandType)
    {
        case 0xDC:
            if (tBCMAlm.ucBattLoseAlm && s_tSysPara.ucBattUnlockMode) //模式为人工解锁，且有电池丢失告警时，不切换解锁方式
            {
                s_tProtocol.ucRTN = RTN_INVALID_DATA;
                return FALSE;
            }
            s_tSysPara.ucBattUnlockMode = fPara; //电池解锁方式
            break;
        case 0xDD:
            s_tSysPara.wSoftAntiTheftDelay = fPara; //软件防盗延时(Min)
            break;
        case 0xDE:
            s_tProtocol.ucRTN = RTN_INVALID_DATA; //陀螺仪防盗方式，不支持设置
            break;
        case 0xDF:
            s_tProtocol.ucRTN = RTN_INVALID_DATA; //陀螺仪倾角(°)，不支持设置
            break;
        default:
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
            return FALSE;
    }
    return TRUE;
}

/// @brief 按commandType设置系统参数
/// @param void
/// @return
static BOOLEAN SetSysPara_V25_XX(void)
{
    if (SetSysPara_V25_8X())
    {
        return TRUE;
    }
    if (SetSysPara_V25_CX())
    {
        return TRUE;
    }
    if (SetSysPara_V25_DX())
    {
        return TRUE;
    }
    if (SetSysPara_V25_AntiTheft())
    {
        return TRUE;
    }
    return FALSE;
}
/***************************************************************************
 * @brief    设定系统参数（定点数）
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
static void SetSystemPara_V25(BYTE ucPort)
{
    GetSysPara(&s_tSysPara);

    if (!SetSysPara_V25_XX())
    {
        return;
    }

    if (False == ChkSysPara(&s_tSysPara))
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
    }
    else
    {
        SaveTempSysPara(&s_tSysPara);
        SetParaSaveFlag(TRUE);
    }

    return;
}

/***************************************************************************
 * @brief    获取通信协议版本号(不支持)
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
static void SendProtocolVer_V25(BYTE ucPort)
{
    s_tProtocol.ucRTN = NO_RETURN;
    return;
}

/***************************************************************************
 * @brief    获取设备厂家信息
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
static void SendFactoryInfo_V25(BYTE ucPort)
{
    WORD wSendLen = 64; //(10+2+20)*2 = 32*2=64
    CHAR ucCorpName[12] = "ZXESM D121";
    BYTE *p = NULL;
    T_BmsInfoStruct tBmsInfo = {0};

    readBMSInfofact(&tBmsInfo);

    p = s_tProtocol.aucSendBuf;

    MemsetBuffWithoutTerm(p, ucCorpName, (BYTE)rt_strnlen_s(ucCorpName, 10), 10, 0x20); //BMS 生产厂商
    p += 10;

    *p++ = 0;                                                  //BMS通信软件版本号
    *p++ = 0x10;
    MemsetBuff(p, tBmsInfo.acBattCorpName, (BYTE)rt_strnlen_s(tBmsInfo.acBattCorpName, 20), 20, 0x20); //电池生产厂商信息
    p += 20;

    s_tProtocol.wSendLenid = wSendLen; //为解决KW问题

    return;
}

/***************************************************************************
 * @brief    获取电池特定参数
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
static void GetSpecialPara_V25(BYTE ucPort)
{
    BYTE *p = NULL;
    BYTE ucCommandGroup = 0;

    GetSysPara(&s_tSysPara);

    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

    *p++ = GetDataFlag(BCM_ALARM, ucPort);

    ucCommandGroup = s_tProtocol.aucRecBuf[7];
    if (ucCommandGroup == 0xFF)
    {
        *p++ = BATT_NUM_BCM; // 铁锂电池组数M，实际未使用FF
    }

    *(SHORT *)p = Host2Modbus((SHORT *)&s_tSysPara.wBatteryCap); // 电池组标称容量，禁止设置
    p += 2;
    *p++ = 1;                            // 短路保护使能，默认上送允许，禁止设置
    *p++ = 1;                            // 电池组充电过压保护使能，默认上送允许，禁止设置
    *p++ = 1;                            // 电池组放电欠压保护使能，默认上送允许，禁止设置
    *p++ = 1;                            // 单体充电过压保护使能，默认上送允许，禁止设置
    *p++ = 1;                            // 单体放电欠压保护使能，默认上送允许，禁止设置
    *p++ = 1;                            // 充电过温保护使能，默认上送允许，禁止设置
    *p++ = 1;                            // 放电过温保护使能，默认上送允许，禁止设置
    *p++ = 1;                            // 充电低温保护使能，默认上送允许，禁止设置
    *p++ = 1;                            // 放电低温保护使能，默认上送允许，禁止设置
    *p++ = s_tSysPara.bBuzzerEnable;     // 蜂鸣器使能
    *p++ = s_tSysPara.bHeatingPadEnable; // 加热垫使能，默认禁止

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    return;
}

/***************************************************************************
 * @brief    设置电池特定参数
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
static void SetSpecialPara_V25(BYTE ucPort)
{
    BYTE i = 0;
    BYTE ucPara = 0;
    const BYTE aucNonSettableType[] = {0x80, 0x88, 0x89, 0x8A, 0x8B, 0x8C, 0x8D, 0x8E, 0x8F, 0x90, 0x00, 0x00};

    GetSysPara(&s_tSysPara);

    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[8];
    ucPara = s_tProtocol.aucRecBuf[9];

    // 部分参数不支持设置
    while (aucNonSettableType[i] != 0x00)
    {
        if (s_tProtocol.ucCommandType == aucNonSettableType[i])
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
            return;
        }
        i++;
    }

    switch (s_tProtocol.ucCommandType)
    {
        case 0x91:
            s_tSysPara.bBuzzerEnable = ucPara; // 蜂鸣器使能
            break;
        case 0x92:
            s_tSysPara.bHeatingPadEnable = ucPara; // 加热垫使能
            break;
        default:
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
            return;
    }

    if (False == ChkSysPara(&s_tSysPara))
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
    }
    else
    {
        SaveTempSysPara(&s_tSysPara);
        SetParaSaveFlag(TRUE);
    }

    return;
}

/***************************************************************************
 * @brief    获取PACK数量(回复RTN = 04H)
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
static void GetPackNum_V25(BYTE ucPort)
{
    s_tProtocol.ucRTN = RTN_WRONG_CID2;
    return;
}

/***************************************************************************
 * @brief    设定通信速率(不支持，不回复)
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
static void SetBaudRate_V25(BYTE ucPort)
{
    s_tProtocol.ucRTN = NO_RETURN;
    return;
}

/***************************************************************************
 * @brief    充电电流控制命令（不支持,不回复）
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
static void ChargeCurrentCtrl_V25(BYTE ucPort)
{
    s_tProtocol.ucRTN = NO_RETURN;
    return;
}

/***************************************************************************
 * @brief    人工布防
 **************************************************************************/
Static BYTE ManualCtrlEnterDefenceStatus_V25(void)
{
    //布防条件
    int value = IsDefenseCondition();
    switch (value)
    {
        case RTN_FAIL_ALREADY_DEFENCE:
            s_tProtocol.ucRTN = RTN_FAIL_ALREADY_DEFENCE;
            break;
        case RTN_FAIL_DEFENCE_PARA:
            s_tProtocol.ucRTN = RTN_FAIL_DEFENCE_PARA;
            break;
        case RTN_FAIL_DEFENCE_LEVEL:
            s_tProtocol.ucRTN = RTN_FAIL_DEFENCE_LEVEL;
            break;
        case RTN_FAIL_DEFENCE_LEVEL_PARA:
            s_tProtocol.ucRTN = RTN_FAIL_DEFENCE_LEVEL_PARA;
            break;
        case RTN_FAIL_COMMAND:
            s_tProtocol.ucRTN = RTN_FAIL_COMMAND;
            break;
        case RTN_SATISFY_DEFENCE:
            // 布防成功并保存布防状态
            SaveAction(GetActionId(CONTOL_MANUAL_ENTER_DEFENCE), "FN_EnterDefence");
            WriteDefenceStatus(True);
            s_tProtocol.ucRTN = RTN_SUCCESS_DEFENCE;
            break;
        default:
            return FAILURE;
    }
    return SUCCESSFUL;
}

/***************************************************************************
 * @brief    人工防盗解锁和撤防
 **************************************************************************/
Static BOOLEAN ManualAntitheftUnlock_V25(BYTE bType)
{
    switch (bType)
    {
        case DEVICE_UNLOCK:
            rt_thread_delay(80);
            if (SUCCESSFUL == DealDeviceUnlock(DEVICE_UNLOCK_MANUAL))
            {
                SaveAction(GetActionId(CONTOL_NETWORK_UNLOCK_ANTITHEFT), "FN Unlock");
            }
            break;
        case DEVICE_CANCEL_DEFENCE:
            if (True == GetDefenceStatus())
            {
                WriteDefenceStatus(False);
                SaveAction(GetActionId(CONTOL_NETWORK_CANCEL_DEVICE_DEFENCE), "FN Cancle Defence");
            }
            else
            {
                // 非布防状态执行撤防操作，回包延时80ms；
                rt_thread_delay(80);
            }
            break;
        default:
            break;
    }
    return True;
}

/***************************************************************************
 * @brief    遥控（仅支持防盗功能）
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
Static void RemoteCtrl_V25(BYTE ucPort)
{
    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];
    switch (s_tProtocol.ucCommandType)
    {
        case MANUAL_DEFENCE_V25: //人工布防
            ManualCtrlEnterDefenceStatus_V25();
            // 布防失败或者已经处于布防状态时无需执行写flash操作，延时80ms后回包（固网协议回包延时需求）；
            if (s_tProtocol.ucRTN != RTN_SUCCESS_DEFENCE)
            {
                rt_thread_delay(80);
            }
            break;
        case MANUAL_WITHDRAW_DEFENCE_V25: //人工撤防
            ManualAntitheftUnlock_V25(DEVICE_CANCEL_DEFENCE);
            break;
        case MANUAL_UNLOCK_V25: //人工解锁
            ManualAntitheftUnlock_V25(DEVICE_UNLOCK);
            break;
        default:
            s_tProtocol.ucRTN = NO_RETURN;
            break;
    }
    return;
}

/****************************************************************************
* 函数名称：DealCommand_v25()
* 调    用：DealCommand()
* 被调用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：根据CID1和CID2，调用相应的命令处理程序
* 作    者  ：hanhui
* 版本信息：V1.0
* 设计日期：2021-10-22
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
void DealCommand_v25(BYTE ucPort)
{
    WORD i = 0;
    const T_CmdFuncStruct *CID2AllFuncTable_v25 = GetCmdFnucStruct_v20(CID2ALL);

    if (ucPort >= SCI_PORT_NUM)
    {
        return;
    }
    for (i = 0; i < HIGH_SEND_LEN; i++) /* 清空响应缓冲区 */
    {
        s_tProtocol.aucSendBuf[i] = 0;
    }
    /* 将响应包的LENID预置为0 */
    s_tProtocol.wSendLenid = 0;

    i = 0;
    while (CID2AllFuncTable_v25 != NULL && CID2AllFuncTable_v25[i].ucCmdCode)
    {
        if (CID2AllFuncTable_v25[i].ucCmdCode == s_tProtocol.ucCID2)
        {
            (*CID2AllFuncTable_v25[i].func)(ucPort);
            DealDeviceUnlock(AUTO_UNLOCK);
            return;
        }
        i++;
    }

    return;
}

static BOOLEAN IsCellFault(BYTE arr[], BYTE length)
{
    BYTE i;
    for (i = 0; i < length; ++i)
    {
        if (FAULT == arr[i])
        {
            return True;
        }
    }
    return False;
}

static BOOLEAN IsCellConsisAlarm(BYTE index)
{
    T_HardwareParaStruct tHWPara;
    BYTE i;
    T_BCMDataStruct tBcmData;
    FLOAT fCellVolMax = 0.0, fCellVolMin = 0.0, fCellConsisValue = 0.0;

    rt_memset(&tBcmData, 0, sizeof(T_BCMDataStruct));
    rt_memset(&tHWPara, 0, sizeof(T_HardwareParaStruct));
    GetRealData(&tBcmData);
    readBmsHWPara(&tHWPara);

    fCellVolMax = tBcmData.afCellVolt[0];
    fCellVolMin = tBcmData.afCellVolt[0];

    if (tHWPara.ucCellVoltNum < 1 || tHWPara.ucCellVoltNum > CELL_VOL_NUM_MAX)
    {
        return False;
    }

    for (i = 0; i < tHWPara.ucCellVoltNum; i++)
    {
        if (fCellVolMax < tBcmData.afCellVolt[i])
        {
            fCellVolMax = tBcmData.afCellVolt[i];
        }
        if (fCellVolMin > tBcmData.afCellVolt[i])
        {
            fCellVolMin = tBcmData.afCellVolt[i];
        }
    }
    fCellConsisValue = max((tBcmData.afCellVolt[index] - fCellVolMin), (fCellVolMax - tBcmData.afCellVolt[index]));
    if (fCellConsisValue >= s_tSysPara.fCellPoorConsisPrtThre)
    {
        return True;
    }
    else
    {
        return False;
    }
}

/***************************************************************************
 * @brief    从src中拷贝srcLen个字符到dst中，剩下的值用defaultValue填充，末尾不需要终止符，仅为该协议设备厂商信息编写，由于字符串长度不够所致。
 * @param    {void} *des 目标位置
 * @param    {void} *src 源位置
 * @param    {BYTE} srcLen 从src中拷贝srcLen个数据
 * @param    {BYTE} defaultLen 默认长度，拷贝defaultLen个数据到des中
 * @param    {BYTE} defalutValue 默认填充数据
 **************************************************************************/
static void MemsetBuffWithoutTerm(void *des, void *src, BYTE srcLen, BYTE defaultLen, BYTE defalutValue)
{
    BYTE buff[40];
    BYTE *pucDest = (BYTE *)des;
    BYTE *pucSrc = (BYTE *)src;

    if (defaultLen > sizeof(buff) || srcLen > defaultLen) // 异常返回
    {
        return;
    }
    rt_memset(buff, defalutValue, defaultLen);
    rt_memcpy(buff, pucSrc, srcLen);
    rt_memcpy(pucDest, buff, defaultLen);
}

static void GetDigitalNew_V25(BYTE ucPort)
{
    BYTE *p = NULL;
    BYTE ucCommandGroup = 0;
    T_BCMDataStruct tBCMAnaData = {0};
    T_HardwareParaStruct tHWPara = {0};

    p = s_tProtocol.aucSendBuf;
    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));

    readBmsHWPara(&tHWPara);
    GetRealData(&tBCMAnaData);

    *p++ = GetDataFlag(BCM_ALARM, ucPort);

    ucCommandGroup = s_tProtocol.aucRecBuf[7];
    *p++ = ucCommandGroup; // Pack数量

    *p++ = tHWPara.ucCellVoltNum; //电池节数(单体电池数量)

    *p++ = tBCMAnaData.ucBattPackSta; // 电池管理模式（00H:充电；01H:放电；02H:在线非浮充；03H:离线）

    *p++ = 0x00; // 用户自定义字节数（可扩展）

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    return;
}

static void GetAnalogNew_V25(BYTE ucPort)
{
    BYTE *p = NULL;
    BYTE ucCommandGroup = 0;
    T_BCMDataStruct tBCMAnaData = {0};
    T_HardwareParaStruct tHWPara = {0};

    p = s_tProtocol.aucSendBuf;
    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));

    readBmsHWPara(&tHWPara);
    GetRealData(&tBCMAnaData);

    *p++ = GetDataFlag(BCM_ALARM, ucPort);

    ucCommandGroup = s_tProtocol.aucRecBuf[7];
    *p++ = ucCommandGroup; // Pack数量

    *p++ = tHWPara.ucCellVoltNum; //电池节数(单体电池数量)

    *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.wBattSOH); // 电池组最大可用容量SOH（%）
    p += 2;

    *p++ = 0x00; // 用户自定义字节数（可扩展）

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    return;
}

static void GetAlarmNew_V25(BYTE ucPort)
{
    BYTE *p = NULL;
    BYTE ucCommandGroup = 0;
    T_BCMAlarmStruct tBCMAlm = {0};
    T_BCMDataStruct tBCMAnaData = {0};
    T_HardwareParaStruct tHWPara = {0};

    p = s_tProtocol.aucSendBuf;
    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));

    readBmsHWPara(&tHWPara);
    GetRealData(&tBCMAnaData);
    GetRealAlarm(BCM_ALARM, (BYTE *)&tBCMAlm);

    *p++ = GetDataFlag(BCM_ALARM, ucPort);

    ucCommandGroup = s_tProtocol.aucRecBuf[7];
    *p++ = ucCommandGroup; // Pack数量

    *p++ = tHWPara.ucCellVoltNum; //电池节数(单体电池数量)

    *p++ = tBCMAlm.ucBattSOCLowAlm; // 电池SOC低告警

    *p++ = tBCMAlm.ucBattSOCLowPrt; // 电池SOC低保护

    *p++ = tBCMAlm.ucBattSOHAlm; // 电池SOH低告警

    *p++ = tBCMAlm.ucBattSOHPrt; // 电池SOH低保护

    *p++ = (tBCMAnaData.ucBattPackSta == 0x01) ? 0x01 : 0x00; // 电池放电告警（如果电池处于放电状态则为1，其他状态为0）

    *p++ = tBCMAlm.ucBDUBusVoltLowPrt; // BDU母排欠压保护

    *p++ = 0x00; // 用户自定义字节数（可扩展）

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    return;
}