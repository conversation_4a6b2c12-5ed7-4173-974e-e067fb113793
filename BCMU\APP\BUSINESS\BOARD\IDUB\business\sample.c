#include <rtthread.h>
#include <rtdevice.h>
#include <board.h>
#include <drv_common.h>
#include <math.h>
#include <string.h>
#include "sample.h"
#include "data_type.h"
#include "utils_thread.h"
#include "adc_ctrl.h"
// #include "hal_adc.h"
// #include "pin_define.h" //补充hal相关引脚定义，用于采样，do，片选
#include "utils_server.h"
#include "pin_ctrl.h"
#include "utils_rtthread_security_func.h"
#include "realdata_save.h"
#include "realdata_id_in.h"
#include "parse_layer.h"
#include "utils_data_transmission.h"

/***********************  变量定义  ************************/
static unsigned int s_adc_data[SAMPLE_CHANNL_END][ADCCONVERTEDVALUES_SAMPLING_NUM]; // 补充ADC采样路数
unsigned short g_flag = 0;
unsigned int g_reg = 0;


static ADC_CHANNEL_INFO_STRUCT s_sample_info[SAMPLE_CHANNL_END] =
{
    {1,  IO1,  0, 8,  "adc0", IDUB_DATA_ID_BRANCH_CURRENT},
    {9,  IO9,  0, 9,  "adc0", IDUB_DATA_ID_BRANCH_CURRENT + 8},
    {17, IO17, 0, 8,  "adc2", IDUB_DATA_ID_BRANCH_CURRENT + 16},
    {25, IO25, 0, 9,  "adc2", IDUB_DATA_ID_BRANCH_CURRENT + 24},
    {32, IO32, 0, 14, "adc2", IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 1},
    {40, IO40, 0, 0,  "adc0", IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 9},
    {48, IO48, 0, 3,  "adc0", IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 17},
    {56, IO56, 0, 10, "adc0", IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 25},
    {2,  IO2,  1, 8,  "adc0", IDUB_DATA_ID_BRANCH_CURRENT + 1},
    {10, IO10, 1, 9,  "adc0", IDUB_DATA_ID_BRANCH_CURRENT + 9},
    {18, IO18, 1, 8,  "adc2", IDUB_DATA_ID_BRANCH_CURRENT + 17},
    {26, IO26, 1, 9,  "adc2", IDUB_DATA_ID_BRANCH_CURRENT + 25},
    {33, IO33, 1, 14, "adc2", IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 2},
    {41, IO41, 1, 0,  "adc0", IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 10},
    {49, IO49, 1, 3,  "adc0", IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 18},
    {57, IO57, 1, 10, "adc0", IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 26},
    {3,  IO3,  2, 8,  "adc0", IDUB_DATA_ID_BRANCH_CURRENT + 2},
    {11, IO11, 2, 9,  "adc0", IDUB_DATA_ID_BRANCH_CURRENT + 10},
    {19, IO19, 2, 8,  "adc2", IDUB_DATA_ID_BRANCH_CURRENT + 18},
    {27, IO27, 2, 9,  "adc2", IDUB_DATA_ID_BRANCH_CURRENT + 26},
    {34, IO34, 2, 14, "adc2", IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 3},
    {42, IO42, 2, 0,  "adc0", IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 11},
    {50, IO50, 2, 3,  "adc0", IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 19},
    {58, IO58, 2, 10, "adc0", IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 27},
    {4,  IO4,  3, 8,  "adc0", IDUB_DATA_ID_BRANCH_CURRENT + 3},
    {12, IO12, 3, 9,  "adc0", IDUB_DATA_ID_BRANCH_CURRENT + 11},
    {20, IO20, 3, 8,  "adc2", IDUB_DATA_ID_BRANCH_CURRENT + 19},
    {28, IO28, 3, 9,  "adc2", IDUB_DATA_ID_BRANCH_CURRENT + 27},
    {35, IO35, 3, 14, "adc2", IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 4},
    {43, IO43, 3, 0,  "adc0", IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 12},
    {51, IO51, 3, 3,  "adc0", IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 20},
    {59, IO59, 3, 10, "adc0", IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 28},
    {5,  IO5,  4, 8,  "adc0", IDUB_DATA_ID_BRANCH_CURRENT + 4},
    {13, IO13, 4, 9,  "adc0", IDUB_DATA_ID_BRANCH_CURRENT + 12},
    {21, IO21, 4, 8,  "adc2", IDUB_DATA_ID_BRANCH_CURRENT + 20},
    {29, IO29, 4, 9,  "adc2", IDUB_DATA_ID_BRANCH_CURRENT + 28},
    {36, IO36, 4, 14, "adc2", IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 5},
    {44, IO44, 4, 0,  "adc0", IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 13},
    {52, IO52, 4, 3,  "adc0", IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 21},
    {60, IO60, 4, 10, "adc0", IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 29},
    {6,  IO6,  5, 8,  "adc0", IDUB_DATA_ID_BRANCH_CURRENT + 5},
    {14, IO14, 5, 9,  "adc0", IDUB_DATA_ID_BRANCH_CURRENT + 13},
    {22, IO22, 5, 8,  "adc2", IDUB_DATA_ID_BRANCH_CURRENT + 21},
    {30, IO30, 5, 9,  "adc2", IDUB_DATA_ID_BRANCH_CURRENT + 29},
    {37, IO37, 5, 14, "adc2", IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 6},
    {45, IO45, 5, 0,  "adc0", IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 14},
    {53, IO53, 5, 3,  "adc0", IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 22},
    {61, IO61, 5, 10, "adc0", IDUB_DATA_ID_RSV_BRANCH_CURRENT},
    {7,  IO7,  6, 8,  "adc0", IDUB_DATA_ID_BRANCH_CURRENT + 6},
    {15, IO15, 6, 9,  "adc0", IDUB_DATA_ID_BRANCH_CURRENT + 14},
    {23, IO23, 6, 8,  "adc2", IDUB_DATA_ID_BRANCH_CURRENT + 22},
    {31, IO31, 6, 9,  "adc2", IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT},
    {38, IO38, 6, 14, "adc2", IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 7},
    {46, IO46, 6, 0,  "adc0", IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 15},
    {54, IO54, 6, 3,  "adc0", IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 23},
    {62, IO62, 6, 10, "adc0", IDUB_DATA_ID_RSV_BRANCH_CURRENT + 1},
    {8,  IO8,  7, 8,  "adc0", IDUB_DATA_ID_BRANCH_CURRENT + 7},
    {16, IO16, 7, 9,  "adc0", IDUB_DATA_ID_BRANCH_CURRENT + 15},
    {24, IO24, 7, 8,  "adc2", IDUB_DATA_ID_BRANCH_CURRENT + 23},
    {39, IO39, 7, 14, "adc2", IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 8},
    {47, IO47, 7, 0,  "adc0", IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 16},
    {55, IO55, 7, 3,  "adc0", IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 24},
};


static msg_map sample_msg_map[] =
    {
        {0, NULL}, // 临时添加解决编译问题
};

void *sample_init_sys(void *param)
{
    // rt_kprintf("sample_init_sys\n");
    server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(sample_msg_map) / sizeof(msg_map);
    register_server_msg_map(sample_msg_map, server_info);
    hal_board_gpio_init(); // 初始化IO模式
    // humi_sensor_init();
    return NULL;
}

void sample_main(void *parameter)
{
    while (is_running(TRUE))
    {
        sample();
        rt_thread_mdelay(400);
    }
}

/* 采样数据 */
void sample(void)
{
    analog_sample();
}


void analog_sample(void)
{
    // rt_kprintf("adc Ai sample start\n");
    unsigned int i, j;
    unsigned int analog_data = 0;
    float actaul_analog_data = 0.0, tmp_val = 0.0;
    for (i = 0; i < SAMPLE_CHANNL_END; i++)
    {
        for (j = 0; j < ADCCONVERTEDVALUES_SAMPLING_NUM; j++)
        {
            s_adc_data[i][j] = adc_get_data_by_channel(i);
        }
        analog_data = MedianFilter(s_adc_data[i]);
        tmp_val = (float)analog_data * REFER_VOLTAGE / 4095;
        actaul_analog_data = (tmp_val - 1.25) * 4;
        set_one_data(s_sample_info[i].data_sid, &actaul_analog_data);
    }
    return;
}


// 功能引脚初始化
void hal_board_gpio_init(void)
{
    rt_pin_mode(PIN_A0_S1, PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_A1_S1, PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_A2_S1, PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_ADDR0_S1, PIN_MODE_INPUT);
    rt_pin_mode(PIN_ADDR1_S1, PIN_MODE_INPUT);
    rt_pin_mode(PIN_LED_CONTROL, PIN_MODE_OUTPUT);
}

// 采样通道选择
void set_selection_sample_gpio(ADC_SAMPLE_CHANNEL channel)
{
    if ((s_sample_info[channel].select_status & 0x01) == 1)
    {
        rt_pin_write(PIN_A0_S1, 1);
    }
    else
    {
        rt_pin_write(PIN_A0_S1, 0);
    }
    if ((s_sample_info[channel].select_status >> 1 & 0x01) == 1)
    {
        rt_pin_write(PIN_A1_S1, 1);
    }
    else
    {
        rt_pin_write(PIN_A1_S1, 0);
    }
    if ((s_sample_info[channel].select_status >> 2 & 0x01) == 1)
    {
        rt_pin_write(PIN_A2_S1, 1);
    }
    else
    {
        rt_pin_write(PIN_A2_S1, 0);
    }
    return;
}

// 从通道获取AI数据
int adc_get_data_by_channel(ADC_SAMPLE_CHANNEL channel)
{
    rt_uint32_t value;
    rt_adc_device_t adc_dev;
    unsigned short flag = 0;

    adc_dev = (rt_adc_device_t)rt_device_find(s_sample_info[channel].adc_device);
    if (adc_dev == RT_NULL)
    {
        rt_kprintf("adc sample run failed! can't find %s device!\n", s_sample_info[channel].adc_device);
        return RT_ERROR;
    }
    set_selection_sample_gpio(channel);
    rt_thread_mdelay(20);
    rt_adc_enable(adc_dev, s_sample_info[channel].adc_channel);
    value = rt_adc_read(adc_dev, s_sample_info[channel].adc_channel);
    get_one_data(IDUB_DATA_ID_MOCK_SWITCH, &flag);
    if(flag == TRUE)
    {
        get_one_data(IDUB_DATA_ID_MOCK_DATA, &value);
    }
    rt_adc_disable(adc_dev, s_sample_info[channel].adc_channel);
    return value;
}

unsigned int MedianFilter(unsigned int data[ADCCONVERTEDVALUES_SAMPLING_NUM])
{
    unsigned int temp, ave;
    for (int i = 0; i < ADCCONVERTEDVALUES_SAMPLING_NUM - 1; i++)
    {
        for (int j = i + 1; j < ADCCONVERTEDVALUES_SAMPLING_NUM; j++)
        {
            if (data[j] < data[i])
            {
                temp = data[i];
                data[i] = data[j];
                data[j] = temp;
            }
        }
    }
    if (ADCCONVERTEDVALUES_SAMPLING_NUM < 4)
    {
        ave = data[ADCCONVERTEDVALUES_SAMPLING_NUM / 2];
    }
    else
    {
        ave = (data[ADCCONVERTEDVALUES_SAMPLING_NUM / 2 - 2] + data[ADCCONVERTEDVALUES_SAMPLING_NUM / 2 - 1] + data[ADCCONVERTEDVALUES_SAMPLING_NUM / 2] + data[ADCCONVERTEDVALUES_SAMPLING_NUM / 2 + 1]) / 4;
    }
    return ave;
}