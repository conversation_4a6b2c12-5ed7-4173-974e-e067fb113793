#include <stddef.h>
#include "alarm_in.h"
#include "battery.h"
#include "prtclDL.h"
#include "realAlarm.h"
#include "DataLoad_product.h"
#include "sample.h"

/***************************************************************************
 * @brief    是否禁止放电
 **************************************************************************/
Bool IsDischargeForbidden(T_BCMAlarmStruct const *ptBcmAlarm, T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn)
{
    if((ptBcmAlarm == NULL) || (pHardwarePara==NULL) || (pBattIn == NULL))
    {
        return FALSE;
    }
    WORD wAlmIndex = -1;
    T_AlarmConfigStruct atAlarmConfig[] = {
        {offsetof(T_BCMAlarmStruct, ucDischgCurrHighPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucBoardTempHighPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucBattUnderVoltPrt), 1},
        {offsetof(T_BCMAlarmStruct, aucCellUnderVoltPrt[0]), pHardwarePara->ucCellVoltNum},
        {offsetof(T_BCMAlarmStruct, aucCellDynamicUnderVoltPrt[0]), pHardwarePara->ucCellVoltNum},
        {offsetof(T_BCMAlarmStruct, aucDischgTempHighPrt[0]), pHardwarePara->ucCellTempNum},
        {offsetof(T_BCMAlarmStruct, aucDischgTempLowPrt[0]), pHardwarePara->ucCellTempNum},
        {offsetof(T_BCMAlarmStruct, ucBattSOCLowPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucBattSOHPrt), 1},
        {offsetof(T_BCMAlarmStruct, aucCellDamagePrt[0]), pHardwarePara->ucCellVoltNum},
        {offsetof(T_BCMAlarmStruct, ucBattShortCut), 1},
        {offsetof(T_BCMAlarmStruct, ucBattReverse), 1},
        {offsetof(T_BCMAlarmStruct, ucBduEepromAlm), 1},
        {offsetof(T_BCMAlarmStruct, ucChgLoopInvalid), 1},
        {offsetof(T_BCMAlarmStruct, ucDischgLoopInvalid), 1},
        {offsetof(T_BCMAlarmStruct, ucCurrLimLoopInvalid), 1},
        {offsetof(T_BCMAlarmStruct, ucBDUBusVoltHighPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucInsideTempHighPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucBDUBattVoltLowPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucBDUBusVoltLowPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucBattLoseAlm), 1},
        {offsetof(T_BCMAlarmStruct, ucCellVoltSampleFault), 1},
        {offsetof(T_BCMAlarmStruct, ucCellPoorConsisPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucCellTempSensorInvalidAlm), pHardwarePara->ucCellTempNum},
        {offsetof(T_BCMAlarmStruct, ucEnvTempHighPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucEnvTempLowPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucBDUCommFail), 1},
        {offsetof(T_BCMAlarmStruct, ucBDUBattLockAlm), 1},
        {offsetof(T_BCMAlarmStruct, ucEqualCircuitFaultAlm), 1},
        {offsetof(T_BCMAlarmStruct, ucBDUConnTempHighPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucHeaterFilmFailure), 1},
        {offsetof(T_BCMAlarmStruct, ucCellTempRiseAbnormal),1}, //单体温升速率异常告警
        {offsetof(T_BCMAlarmStruct, ucDcrFaultPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucBattFaultTempHighAlm),1}, //电池异常高温保护告警
    };

    /* 升级过程禁止放电 */
    if (IsUpdate())
    {
        return TRUE;
    }

    if (IsExistForbiddenAlarm(&atAlarmConfig[0], ptBcmAlarm, sizeof(atAlarmConfig) / sizeof(atAlarmConfig[0]), &wAlmIndex))
    {
        return TRUE;
    }

    if (IsExistForbiddenDischargeTemperature(pHardwarePara, pBattIn))
    {
        return TRUE;
    }

    return FALSE;
}

/***************************************************************************
 * @brief    是否存在激活口需要的欠压保护
 **************************************************************************/
BOOLEAN IsActivatePortUVP(T_BCMAlarmStruct const *ptBcmAlarm, T_HardwareParaStruct *pHardwarePara)
{
    return FALSE;
}

/***************************************************************************
 * @brief    载入产品特性电池数据
 **************************************************************************/
void LoadProductCharacteristicData(T_BattInfo *pBattIn, T_SysPara *pSysPara)
{
    return;
}

/***************************************************************************
 * @brief    qtp模式下对部分电池数据覆写(注意位置)
 **************************************************************************/
void QtpBattDataOverWrite(T_BattInfo *pBattIn, T_BCMDataStruct *pBcmData, T_SysPara *pSysPara)
{
    if((pBattIn == NULL) || (pBcmData==NULL) || (pSysPara == NULL))
    {
        return;
    }

    if (GetQtptestFlag()) // 在外协模式下,采用纯锂电场景
    {
        pBattIn->tPara.fChargeMaxCurr = pSysPara->fChgCurrHighAlmThre - 0.05;
        pBattIn->tPara.ucUsageScen = DISCHG_MODE_BATT_CHARACTERISTIC;
        if (pBcmData->ucBattPackSta == BATT_MODE_DISCHARGE)
        {
            pBattIn->tPara.ucDischargeMode = BATT_DISCHARGE_16FOLLOW; // D121纯锂模式下 充电模式只有16串一种方式
            pBattIn->tPara.bThroughDischgEnable = False;
        }
    }
    return;
}

/***************************************************************************
 * @brief    载入电池系统参数
 **************************************************************************/
void LoadBattSyspara(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal, T_SysPara *pSyspara, T_BCMDataStruct *pBcmData)
{
    if((pBattIn == NULL) || (pBattDeal==NULL) || (pSyspara == NULL) || (pBcmData == NULL))
    {
        return;
    }


    pBattIn->tPara.fChargeFullVol = pSyspara->fBattChgFullAverVoltThre / g_ProConfig.fVoltTrasRate;
    pBattIn->tPara.fChagEndCurCoeff = pSyspara->fBattChgFullAverCurrThre;
    pBattIn->tPara.wChagMaxMinute = pSyspara->wChgMaxDura;
    pBattIn->tPara.wChagEndHoldMinute = pSyspara->wChgEndDura;
    pBattIn->tPara.fBattRefreshVoltThreshold = pSyspara->fBattSupplVolt / g_ProConfig.fVoltTrasRate;

    pBattIn->tPara.wRechargeSOC = pSyspara->wRechargeSOC;
    pBattIn->tPara.fCellSupplVolt = pSyspara->fCellSupplVolt;
    pBattIn->tPara.ucDischargeMode = BATT_DISCHARGE_16FOLLOW; // D121纯锂模式下 充电模式只有16串一种方式
    pBattIn->tPara.bThroughDischgEnable = True;    /// 放电直通使能默认为允许
    pBattIn->tPara.fEqulizationStartThreshold = pSyspara->fCellEquVoltDiffThre;
    pBattIn->tPara.fChargeMaxCurr = pSyspara->fChargeMaxCurr;
    pBattIn->tPara.fCellChargeFullVolt = pSyspara->fCellChargeFullVolt;
    pBattIn->tPara.wSwitchSOC = pSyspara->wDischgSwitchSOC;
    pBattIn->tPara.wSwitchSOC2 = pSyspara->wDischgSwitchSOC2;
    pBattIn->tPara.bRemoSupplyDischgSwitchSOC = pBattIn->tPara.wSwitchSOC;
    pBattIn->tPara.bRemoSupplyDischgSwitchSOC2 = pBattIn->tPara.wSwitchSOC2;
    pBattIn->tPara.ucDODOfDischarge = pSyspara->ucDODPerDischarge;
    pBattIn->tPara.fRemoteSuplyVolt = pSyspara->fRemoteSupplyOutVolt;
    pBattIn->tPara.fChgHighTempPrt = pSyspara->fChgTempHighPrtThre;
    pBattIn->tPara.fChgLowTempPrt = pSyspara->fChgTempLowPrtThre;
    pBattIn->tPara.fDischgHighTempPrt = pSyspara->fDischgTempHighPrtThre;
    pBattIn->tPara.fDischgLowTempPrt = pSyspara->fDischgTempLowPrtThre;
    pBattIn->tPara.bChargeRotate = pSyspara->bChargeRotate;
    pBattIn->tPara.fDischargeEndVolt1 = pSyspara->fDischargeEndVolt1;
    pBattIn->tPara.fDischargeEndVolt2 = pSyspara->fDischargeEndVolt2;
    pBattIn->tPara.fSelfDischgACR= pSyspara->fSelfDischgACR/100;//自放电容量比率
    pBattIn->tPara.fCapDCDR= pSyspara->fCapDCPRFaultAlmThre/100;//容量衰减一致性差告警比率阈值
    pBattIn->tPara.ucChargeMap = pSyspara->bChargeMapEnable;//充电MAP使能
    return;
}