#include <rtthread.h>
#include <rtdevice.h>
#include <math.h>
#include <stdint.h>
#include <string.h>
#include <stdbool.h>
#include "sample.h"
#include "msg.h"
#include "utils_server.h"
#include "utils_thread.h"
#include "utils_math.h"
#include "alarm_mgr_api.h"
#include "realdata_id_in.h"
#include "alarm_id_in.h"
#include "para_id_in.h"
#include "realdata_save.h"
#include "para_manage.h"
#include "his_record.h"
#include "dev_acmu.h"
#include "data_type.h"
#include "drv_timer_adc.h"
#include "rt_drv_pwm.h"
#include "drv_common.h"
#include "pin_define.h"
#include "acmu_watchdog.h"
#include "Menu.h"
#include "const_define_in.h"
#include "utils_heart_beat.h"

Static rt_device_t s_adc_dev = NULL;
Static struct rt_device_pwm *s_pwm_dev = NULL;
static struct rt_semaphore sync_adc_rx_record_sem;
static struct rt_semaphore sync_adc_rx_sample_sem;
Static his_extreme_data_t s_extreme_data = {0};
Static data_base_address_t *s_data_base_addr = NULL;
Static sample_mode_t s_confirmed_sample_mode = UNCONFIRMED_MODE;

#define REFERENCE_VOL_ADC 3.0f
#define VOL_PHASE_NUM 3
#define CURR_PHASE_NUM 3
#define CURRENT_CHANNEL_TRANS   9   // 变送器方案电流通道
#define VOLTAGE_CHANNEL_TRANS   11  // 变送器方案电压通道
#define PHASE_TO_LINE_COEFF 1.732f  // 相电压转线电压系数

const uint16_t total_cycles = 200;                // 总采集次数
const uint16_t channels_per_cycle = 15;           // 每次采集的通道数
Static PhaseType s_current_phase = PHASE_A;

// 互斥量定义
static struct rt_mutex extreme_data_mutex;

Static unsigned char update_alarm_status(void);
Static unsigned char update_ac_power_off_status(void);
Static unsigned char update_phase_vol_balace_status(void);
Static short save_extreme_data(void);
Static int adc_device_find(void);
Static int pwm_device_find(void);
Static int get_acmu_env_temp(void);
Static int get_acmu_env_hum(void);
Static int get_vol_curr_transmit(void);
Static int get_vol_curr_sensor(void);
Static void phase_pin_init(void);
Static void phase_switch(void);
Static int di_read_all_states();
Static float temp_valid(float temp);

static HumidityData env_humiditytab[] = {
    {10, 9410}, {20, 9256}, {30, 9102},
    {40, 8948}, {50, 8795}, {60, 8641},
    {70, 8487}, {80, 8333}, {90, 8179},
    {95, 8102},
};

Static float s_vol_rms_sensor[VOL_PHASE_NUM];
Static float s_curr_rms_sensor[CURR_PHASE_NUM];
Static ThreePhaseValues s_three_phase_value;

static msg_map sample_msg_map[] =
{
    {0,NULL},//临时添加解决编译问题
};

Static rt_err_t sync_adc_rx_indicate(rt_device_t dev, rt_size_t size)
{
    rt_sem_release(&sync_adc_rx_record_sem);
    rt_sem_release(&sync_adc_rx_sample_sem);
    return RT_EOK;
}

static void di_gpio_init() {
    rt_pin_mode(MUX_A0, PIN_MODE_OUTPUT);  // MUX通道选择Bit0
    rt_pin_mode(MUX_A1, PIN_MODE_OUTPUT);  // MUX通道选择Bit1
    rt_pin_mode(MUX_A2, PIN_MODE_OUTPUT);  // MUX通道选择Bit2

    rt_pin_mode(DI_CS1, PIN_MODE_INPUT);  // 芯片1片选
    rt_pin_mode(DI_CS2, PIN_MODE_INPUT);  // 芯片2片选
    rt_pin_mode(DI_CS3, PIN_MODE_INPUT);  // 芯片3片选
}

void* sample_init(void *param)
{
    server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(sample_msg_map) / sizeof(msg_map);
    register_server_msg_map(sample_msg_map, server_info);
    rt_sem_init(&sync_adc_rx_record_sem, "recorder", 0, RT_IPC_FLAG_FIFO);
    rt_sem_init(&sync_adc_rx_sample_sem, "sample", 0, RT_IPC_FLAG_FIFO);
    phase_pin_init();
    di_gpio_init();
    adc_device_find();
    rt_device_set_rx_indicate(s_adc_dev, sync_adc_rx_indicate);
    pwm_device_find();
    return RT_NULL;
}

void sample_main(void* parameter)
{
    rt_tick_t tk_cnt = 0, tk_recorder = 0, tk_sample = 0;
    init_extreme_data();
    thread_monitor_register("sample");
    while (is_running(TRUE))
    {
        thread_monitor_update_heartbeat();
        tk_cnt++;
        if (tk_cnt - tk_recorder == RECORDER_THREAD_DELAY_TIME)
        {
            if(rt_sem_take(&sync_adc_rx_record_sem, 20) == RT_EOK)
            {
                get_vol_curr_sensor();
            }
            tk_recorder = tk_cnt;
        }
        if (tk_cnt - tk_sample == SAMPLE_THREAD_DELAY_TIME)
        {
            if(rt_sem_take(&sync_adc_rx_sample_sem, 200) == RT_EOK)
            {
                get_acmu_env_temp();
                get_vol_curr_transmit();
                rt_sem_clear_value(&sync_adc_rx_sample_sem);
            }
            get_acmu_env_hum();
            update_alarm_status();
            save_extreme_data();
            di_read_all_states();
            phase_switch();
            tk_sample = tk_cnt;
        }
        rt_thread_mdelay(1);
    }
}

Static int adc_device_find(void)
{
    s_adc_dev = rt_device_find(ADC_DEV_NAME);
    if(s_adc_dev == NULL)
    {
        return FAILURE;
    }

    rt_device_open(s_adc_dev,  RT_DEVICE_FLAG_INT_RX);

    if(s_adc_dev->user_data == NULL)
    {
        return FAILURE;
    }

    s_data_base_addr = s_adc_dev->user_data;
    return SUCCESSFUL;

}

Static int pwm_device_find(void)
{
    s_pwm_dev = (struct rt_device_pwm *)rt_device_find(PWM_DEV_NAME);
    if(s_pwm_dev == NULL)
    {
        return FAILURE;
    }
    rt_device_open((rt_device_t)s_pwm_dev,  RT_DEVICE_OFLAG_RDONLY);
   
    return SUCCESSFUL;

}

Static float temp_valid(float temp)
{
    float result = temp;
    if(temp > 110.1f)
    {
        result = 110.1;
    }
    else if (temp < -50.1f)
    {
        result = -50.1;
    }
    return result;
}

Static int get_acmu_env_temp(void)
{
    int count = 0;
    float temp = 0;
    char env_temp_zero = 0;
    float avg_volt = 0;
    uint16_t *adc_volt = NULL;
    char buff[MAX_LETTERS];

    rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));

    if(s_data_base_addr == NULL || s_data_base_addr->adc_data_addr == NULL)
    {
        return FAILURE;
    }

    adc_volt = s_data_base_addr->adc_data_addr;

    for (int i = 1; i <= ADC_SAMPLE_COUNT; i++) {
        if(i % 15 == 0)
        {
            avg_volt += ((adc_volt[i - 1] / 4095.0f) * 3.0f); //转换为adc电压
            count++;
        }
    }

    if(count > 0)
    {
        avg_volt = avg_volt / count;
        temp = -1481.96 + sqrt((2.1962 * 3.88 + (1.8639 - avg_volt)) / 3.88) * 1000;    //转换为温度
        get_one_para(ACMU_PARA_ID_ENV_TEMP_ZERO_POINT_OFFSET, &env_temp_zero);
        temp = temp_valid(temp);
        temp = temp + env_temp_zero;
        set_one_data(ACMU_DATA_ID_ENV_TEMP, &temp);
    }

    rt_snprintf_s(buff, sizeof(buff), "ET:%4.3f %4.1f", avg_volt, temp);
    SetTraceStr(14, buff);

    return SUCCESSFUL;
}

Static int get_acmu_env_hum(void)
{
    static int count = 0;
    static int frequency_buffer[10] = {0};
    int frequency = 0, avg_frequency = 0;
    float humidity = 0, humidityDiff = 0, frequencyDiff = 0, ratio = 0;
    char humidity_zero = 0;
    struct rt_pwm_configuration pwm_config = {0};
    char buff[MAX_LETTERS];

    rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));

    if(s_pwm_dev == RT_NULL)
    {
        return FAILURE;
    }

    pwm_config.channel = 1;
    rt_device_control((rt_device_t)s_pwm_dev, PWM_CMD_GET, &pwm_config);
    frequency = pwm_config.pulse;

    if (count < 10)
    {
        frequency_buffer[count++] = frequency;
    }
    else
    {
        for (int i = 0; i < 9; i++)
        {
            frequency_buffer[i] = frequency_buffer[i + 1];
        }
        frequency_buffer[9] = frequency;
    }
    
    for (int i = 0; i < count; i++)
    {
        avg_frequency += frequency_buffer[i];
    }

    frequency = avg_frequency / count;
    for (int i = 0; i < (ARR_SIZE(env_humiditytab) -1); i++) {
        if (frequency <= env_humiditytab[i].frequency && frequency >= env_humiditytab[i + 1].frequency) {
            // 使用线性插值计算精准湿度
            humidityDiff = env_humiditytab[i + 1].humidity - env_humiditytab[i].humidity;
            frequencyDiff = env_humiditytab[i + 1].frequency - env_humiditytab[i].frequency;
            ratio = (float)(frequency - env_humiditytab[i].frequency) / frequencyDiff;
            humidity = env_humiditytab[i].humidity + ratio * humidityDiff;
            break;
        }
    }
    get_one_para(ACMU_PARA_ID_ENV_HUMIDITY_ZERO_POINT_OFFSET, &humidity_zero);
    humidity = humidity + humidity_zero;
    set_one_data(ACMU_DATA_ID_ENV_HUMIDITY, &humidity);
    rt_snprintf_s(buff, sizeof(buff), "EH:%u %4.3f %4.1f", avg_frequency, humidity);
    SetTraceStr(15, buff);

    return SUCCESSFUL;
}

Static unsigned char update_alarm_status(void)
{
    update_ac_power_off_status();
    update_phase_vol_balace_status();
    return SUCCESSFUL;
}

Static unsigned char update_ac_power_off_status(void)
{
    int alm_id = GET_ALM_ID(ACMU_ALM_ID_AC_POWER_OFF, 1, 1);
    unsigned char ac_power_off_alarm = get_realtime_alarm_value(alm_id);
    float phase_voltages[3] = {0};
    unsigned char ac_power_off_status = 0;

    // 获取所有相电压
    get_one_data(ACMU_DATA_ID_PHASE_VOLT, &phase_voltages[0]);
    get_one_data(ACMU_DATA_ID_PHASE_VOLT + 1, &phase_voltages[1]);
    get_one_data(ACMU_DATA_ID_PHASE_VOLT + 2, &phase_voltages[2]);

    // 检查相电压是否都小于20v(产生告警)
    if (ac_power_off_alarm == ALARM_NOT_EXSIT &&
        phase_voltages[0] < 20.0f && phase_voltages[1] < 20.0f && phase_voltages[2] < 20.0f)
    {
        ac_power_off_status = 1;
        set_one_data(ACMU_DATA_ID_AC_POWER_OFF_STATUS, &ac_power_off_status);
    }
    // 检查相电压是否都大于等于30v(恢复告警)
    else if ((ac_power_off_alarm == ALARM_EXSIT) &&
             (phase_voltages[0] >= 30.0f || phase_voltages[1] >= 30.0f || phase_voltages[2] >= 30.0f))
    {
        ac_power_off_status = 0;
        set_one_data(ACMU_DATA_ID_AC_POWER_OFF_STATUS, &ac_power_off_status);
    }

    return SUCCESSFUL;
}

Static unsigned char update_phase_vol_balace_status(void) {
    int alm_id = GET_ALM_ID(ACMU_ALM_ID_PHASE_VOLT_UNBALANCE, 1, 1);
    unsigned char phase_vol_balance_alarm = get_true_alarm_value(alm_id);
    float phase_voltages[3] = {0};
    unsigned char phase_vol_balance_status = 0;
    int vol_range = 0;
    float max = 0, min = 0;
    float alarm_tres = 0;

    get_one_data(ACMU_DATA_ID_PHASE_VOLT, &phase_voltages[0]);
    get_one_data(ACMU_DATA_ID_PHASE_VOLT + 1, &phase_voltages[1]);
    get_one_data(ACMU_DATA_ID_PHASE_VOLT + 2, &phase_voltages[2]);
    get_one_para(ACMU_PARA_ID_PHASE_VOLTAGE_UNBALANCE_OFFSET, &alarm_tres);

    // 获取所有相电压差值
    max = fmax(phase_voltages[0], fmax(phase_voltages[1], phase_voltages[2]));
    min = fmin(phase_voltages[0], fmin(phase_voltages[1], phase_voltages[2]));
    vol_range = max - min;

    // 判断差值是否大于55
    if (phase_vol_balance_alarm == ALARM_NOT_EXSIT && vol_range > alarm_tres) {
        phase_vol_balance_status = 1;
        set_one_data(ACMU_DATA_ID_PHASE_VOLTAGE_BALANCE_STATE, &phase_vol_balance_status);
    } else if (phase_vol_balance_alarm == ALARM_EXSIT && vol_range <= (alarm_tres - 5)) {
        phase_vol_balance_status = 0;
        set_one_data(ACMU_DATA_ID_PHASE_VOLTAGE_BALANCE_STATE, &phase_vol_balance_status);
    }

    return SUCCESSFUL;
}

/**
 * @brief   重置极值数据为默认值
 * @param   time_now 当前时间戳
 * @return  void
 */
static void reset_extreme_data_to_default(time_t time_now)
{
    // 交流电压最大值
    s_extreme_data.phase_volt_max = 0;
    s_extreme_data.a_phase_volt_max[0] = 0;
    s_extreme_data.a_phase_volt_max[1] = 0;
    s_extreme_data.a_phase_volt_max[2] = 0;

    // 交流电压最小值
    s_extreme_data.phase_volt_min = 400;
    s_extreme_data.a_phase_volt_min[0] = 400;
    s_extreme_data.a_phase_volt_min[1] = 400;
    s_extreme_data.a_phase_volt_min[2] = 400;

    // 环境温度最大值
    s_extreme_data.env_temp_max = -50;

    // 环境温度最小值
    s_extreme_data.env_temp_min = 100;

    // 时间戳统一设置
    s_extreme_data.phase_volt_max_time = time_now;
    s_extreme_data.phase_volt_min_time = time_now;
    s_extreme_data.env_temp_max_time   = time_now;
    s_extreme_data.env_temp_min_time   = time_now;
}

/**
 * @description: 初始化极值记录
 */
int init_extreme_data(void)
{     
    time_t save_time; 
    int ret = SUCCESSFUL;

    // 初始化互斥量（只执行一次）
    rt_mutex_init(&extreme_data_mutex, "extreme_mtx", RT_IPC_FLAG_PRIO);

    ret = pub_hisrecord_read_msg(RECORD_TYPE_EXTREME_DATA, 1, 0, &s_extreme_data);
    if(ret == FAILURE)
    {
        time(&save_time);
        reset_extreme_data_to_default(save_time);
        return FAILURE;
    }
    return SUCCESSFUL;
}

/**
 * @description: 删除极值记录
 */
int delete_extreme_data(void)
{     
    time_t save_time; 

    rt_mutex_take(&extreme_data_mutex, RT_WAITING_FOREVER);
    
    time(&save_time);
    reset_extreme_data_to_default(save_time);

    pub_extremedata_save_msg(&s_extreme_data);

    rt_mutex_release(&extreme_data_mutex);

    return SUCCESSFUL;
}

/**
 * @description: 保存极值记录
 * @param {void} 无输入参数
 * @return {short}  成功 SUCCESSFUL
 */
Static short save_extreme_data(void)
{
    unsigned char no;
    unsigned char extreme_flag = FALSE;
    time_t save_time;
    float phase_volt[AC_PHASE_NUM] = {0.0};
    float f_env_temp = 0.0;
    char env_temp;
    static unsigned char last_volt_max_300_flag[AC_PHASE_NUM] = {FALSE, FALSE, FALSE};

    time(&save_time);

    for (no = 0; no < AC_PHASE_NUM; no++) {
        get_one_data(ACMU_DATA_ID_PHASE_VOLT + no, &phase_volt[no]);
    }

    get_one_data(ACMU_DATA_ID_ENV_TEMP, &f_env_temp);
    env_temp = (char)round(f_env_temp);

    rt_mutex_take(&extreme_data_mutex, RT_WAITING_FOREVER);

    /* Started by AICoder, pid:kc946ucfc6l9a28142e90ae7c0272e2106d60f12 */
    // 保存交流电压最大值、最小值
    for (no = 0; no < AC_PHASE_NUM; no++) {
        if (((short)round(phase_volt[no]) > s_extreme_data.phase_volt_max) || 
            (if_float_equal(phase_volt[no], 300) && !last_volt_max_300_flag[no])) {
            // 更新最大值并记录状态
            s_extreme_data.phase_volt_max = (short)round(phase_volt[no]);
            s_extreme_data.a_phase_volt_max[0] = (short)round(phase_volt[0]);
            s_extreme_data.a_phase_volt_max[1] = (short)round(phase_volt[1]);
            s_extreme_data.a_phase_volt_max[2] = (short)round(phase_volt[2]);
            s_extreme_data.phase_volt_max_time = save_time;
            extreme_flag = TRUE;
            
            // 更新状态：仅当本次由300触发时设置为TRUE
            last_volt_max_300_flag[no] = (if_float_equal(phase_volt[no], 300)) ? TRUE : FALSE;
        }
        
        // 最小值逻辑保持不变
        if (((short)round(phase_volt[no]) < s_extreme_data.phase_volt_min) && (phase_volt[no] > 20)) {
            s_extreme_data.phase_volt_min = (short)round(phase_volt[no]);
            s_extreme_data.a_phase_volt_min[0] = (short)round(phase_volt[0]);
            s_extreme_data.a_phase_volt_min[1] = (short)round(phase_volt[1]);
            s_extreme_data.a_phase_volt_min[2] = (short)round(phase_volt[2]);
            s_extreme_data.phase_volt_min_time = save_time;
            extreme_flag = TRUE;
        }
    }
    /* Ended by AICoder, pid:kc946ucfc6l9a28142e90ae7c0272e2106d60f12 */

    // 环境温度最大值
    if (env_temp > s_extreme_data.env_temp_max) {
        s_extreme_data.env_temp_max = env_temp;
        s_extreme_data.env_temp_max_time = save_time;
        extreme_flag = TRUE;
    }

    // 环境温度最小值
    if (env_temp < s_extreme_data.env_temp_min) {
        s_extreme_data.env_temp_min = env_temp;
        s_extreme_data.env_temp_min_time = save_time;
        extreme_flag = TRUE;
    }

    if (extreme_flag == TRUE) {
        pub_extremedata_save_msg(&s_extreme_data);
    }

    rt_mutex_release(&extreme_data_mutex);

    return SUCCESSFUL;
}


/**
 * @brief 将ADC采样值转换为实际电压值
 * @param adc_value ADC采样值(0~4095)
 * @return 实际电压值
 */
float convert_to_actual_voltage(uint16_t adc_value) {
    // 先转换为基准电压对应的电压值X（3V基准）
    float x = (float)adc_value * REFERENCE_VOL_ADC / 4095.0f;
    // 应用电压转换公式
    return 431.620536565039f * x - 647.430804847559f;
}

/**
 * @brief 将ADC采样值转换为实际电流值
 * @param adc_value ADC采样值(0~4095)
 * @param sensor_type 电流传感器类型
 * @return 实际电流值
 */
float convert_to_actual_current(uint16_t adc_value, uint16_t sensor_type) {
    // 先转换为基准电压对应的电压值X（3V基准）
    float x = (float)adc_value * REFERENCE_VOL_ADC / 4095.0f;
    return (x - 1.5f) * 0.7716049382716049f * (float)sensor_type;
}

/**
 * @brief 传感器方案计算三相电压有效值（通道2、5、8）
 * @param data_base_addr 数据基地址结构体指针
 * @param voltage_rms 输出：3路电压有效值数组（长度为3）
 */
void calculate_voltage_rms(data_base_address_t* data_base_addr, float* voltage_rms) {
    const uint8_t voltage_channels[] = {2, 5, 8}; // 电压通道号
    for (int i = 0; i < 3; i++) {
        uint8_t ch = voltage_channels[i];
        double sum_sq = 0.0;
        for (int cycle = 0; cycle < total_cycles; cycle++) {
            int index = cycle * channels_per_cycle + ch;
            uint16_t adc_sample = data_base_addr->adc_data_addr[index];
            float actual_voltage = convert_to_actual_voltage(adc_sample);
            // rt_kprintf("%.3f\t", actual_voltage);
            sum_sq += (double)actual_voltage * actual_voltage;
        }
        voltage_rms[i] = (float)sqrt(sum_sq / total_cycles);
        // rt_kprintf("传感器方案第%d相电压有效值为%f\n", i, voltage_rms[i]);
    }
}

/**
 * @brief 传感器方案计算3路电流有效值（通道0、3、6）
 * @param data_base_addr 数据基地址结构体指针
 * @param sensor_type 电流传感器类型
 * @param current_rms 输出：3路电流有效值数组（长度为3）
 */
void calculate_current_rms(data_base_address_t* data_base_addr,
                          float* current_rms) {
    const uint8_t current_channels[] = {0, 3, 6}; // 电流通道号
    for (int i = 0; i < 3; i++) {
        uint8_t ch = current_channels[i];
        double sum_sq = 0.0;
        uint16_t mutual_type = 600;
        get_one_para(ACMU_PARA_ID_AC_MUTUAL_INDUCTANCE_TYPE_OFFSET + i, &mutual_type);
        for (int cycle = 0; cycle < total_cycles; cycle++) {
            int index = cycle * channels_per_cycle + ch;
            uint16_t adc_sample = data_base_addr->adc_data_addr[index];
            float actual_current = convert_to_actual_current(adc_sample, mutual_type);
            // rt_kprintf("%.3f\t", actual_current);
            sum_sq += (double)actual_current * actual_current;
        }
        current_rms[i] = (float)sqrt(sum_sq / total_cycles);
        // rt_kprintf("传感器方案第%d相电流有效值为%f\n", i, current_rms[i]);
    }
}

/**
 * @brief 初始化PB1、PB4为输出引脚（片选控制）
 */
Static void phase_pin_init(void) {
    // 配置PB1为输出
    rt_pin_mode(GET_PIN(B, 1), PIN_MODE_OUTPUT);
    // 配置PB4为输出
    rt_pin_mode(GET_PIN(B, 4), PIN_MODE_OUTPUT);
    // 初始化为A相（00）
    rt_pin_write(GET_PIN(B, 1), PIN_LOW);
    rt_pin_write(GET_PIN(B, 4), PIN_LOW);
}

/**
 * @brief 自动循环切换A、B、C三相
 */
Static void phase_switch(void) {
    // 计算下一相（循环逻辑：A→B→C→A）
    PhaseType next_phase;
    switch (s_current_phase) {
        case PHASE_A:
            next_phase = PHASE_B;
            break;
        case PHASE_B:
            next_phase = PHASE_C;
            break;
        case PHASE_C:
            next_phase = PHASE_A;
            break;
        default:
            // 异常情况默认切换到A相
            next_phase = PHASE_A;
            break;
    }
    
    // 切换到下一相的硬件逻辑（复用原有引脚操作）
    switch (next_phase) {
        case PHASE_A:
            rt_pin_write(GET_PIN(B, 1), PIN_LOW);
            rt_pin_write(GET_PIN(B, 4), PIN_LOW);
            break;
        case PHASE_B:
            rt_pin_write(GET_PIN(B, 1), PIN_HIGH);
            rt_pin_write(GET_PIN(B, 4), PIN_LOW);
            break;
        case PHASE_C:
            rt_pin_write(GET_PIN(B, 1), PIN_LOW);
            rt_pin_write(GET_PIN(B, 4), PIN_HIGH);
            break;
    }
    
    // 更新当前相位为已切换的下一相
    s_current_phase = next_phase;
    
    rt_thread_mdelay(1); // 延时1ms确保切换稳定
}


/**
 * @brief ADC值转换为实际电压（通道11）
 * @param adc_value ADC采样值（0~4095）
 * @return 实际电压值（Y=100X，X=ADC*3/4095）
 */
Static float adc_to_voltage(uint16_t adc_value) {
    float x = (float)adc_value * REFERENCE_VOL_ADC / 4095.0f;
    return 100.0f * x;
}

/**
 * @brief ADC值转换为实际电流（通道9）
 * @param adc_value ADC采样值（0~4095）
 * @return 实际电流值（Y=5X/3，X=ADC*3/4095）
 */
Static float adc_to_current(uint16_t adc_value, rt_uint16_t mutual_type) {
    float x = (float)adc_value * REFERENCE_VOL_ADC / 4095.0f;
    return (float)mutual_type * x / 3.0f;
}

/**
 * @brief 处理200个数据，计算当前相的有效值（取平均）
 */
void phase_data_process(data_base_address_t* data_base_addr, PhaseType current_phase, ThreePhaseValues* result) {
    float current_sum = 0.0f;
    float voltage_sum = 0.0f;
    rt_uint16_t mutual_type = 600;

    get_one_para(ACMU_PARA_ID_AC_MUTUAL_INDUCTANCE_TYPE_OFFSET + (rt_uint8_t)current_phase, &mutual_type);
    // 累加200个数据的转换结果
    for (uint16_t i = 0; i < total_cycles; i++) {
        // 计算当前数据点的通道索引
        uint32_t current_idx = i * channels_per_cycle + CURRENT_CHANNEL_TRANS;
        uint32_t voltage_idx = i * channels_per_cycle + VOLTAGE_CHANNEL_TRANS;

        // 转换并累加
        current_sum += adc_to_current(data_base_addr->adc_data_addr[current_idx], mutual_type);
        voltage_sum += adc_to_voltage(data_base_addr->adc_data_addr[voltage_idx]);
    }

    float avg_current = current_sum / total_cycles;
    float avg_voltage = voltage_sum / total_cycles;
    // rt_kprintf("变送器方案第%d相电压有效值为%f, 电流有效值为%f\n", current_phase, avg_voltage, avg_current);
    switch (current_phase) {
        case PHASE_A:
            result->a_phase.current = avg_current;
            result->a_phase.voltage = avg_voltage;
            break;
        case PHASE_B:
            result->b_phase.current = avg_current;
            result->b_phase.voltage = avg_voltage;
            break;
        case PHASE_C:
            result->c_phase.current = avg_current;
            result->c_phase.voltage = avg_voltage;
            break;
    }
}

// 检查电压值是否在[90.0f, 300.0f]范围内
Static bool is_voltage_in_range(float voltage)
{
    return (voltage - 90.0f) > -EPSILON && (300.0f - voltage) > -EPSILON;
}

// 处理单相电压数据
Static int process_phase_voltage(int phase_idx, float input_vol)
{
    char zero_vol = 0;
    float slope_vol = 0.0f;
    char buff[MAX_LETTERS];
    float phase_act_power = 0.0;
    acem_analog_data_t acem_show_data;

    rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));
    rt_memset_s(&acem_show_data, sizeof(acem_show_data), 0, sizeof(acem_show_data));
    get_acem_show_data(&acem_show_data);

    // 获取当前相的偏移参数
    get_one_para(ACMU_PARA_ID_AC_PHASE_VOLT_ZERO_OFFSET + phase_idx, &zero_vol);
    get_one_para(ACMU_PARA_ID_AC_PHASE_VOLT_SLOPE_OFFSET + phase_idx, &slope_vol);

    // 计算并设置数据
    float phase_vol = input_vol * slope_vol + zero_vol;
    phase_vol = phase_vol > 300.0f ? 300.0f : phase_vol;
    phase_vol = phase_vol < 20.0f ? 0.0f : phase_vol;
    
    float line_vol = PHASE_TO_LINE_COEFF * phase_vol;
    set_one_data(ACMU_DATA_ID_PHASE_VOLT + phase_idx, &phase_vol);
    set_one_data(ACMU_DATA_ID_LINE_VOLT + phase_idx, &line_vol);

    rt_snprintf_s(buff, sizeof(buff), "U%d:%5.3f %5.1f", phase_idx+1, input_vol, phase_vol);
    SetTraceStr(8 + phase_idx, buff); 
    rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));
    rt_snprintf_s(buff, sizeof(buff), "AP%d:%4.3f", phase_idx+1, acem_show_data.phase_act_power[phase_idx]);
    SetTraceStr(16 + phase_idx, buff);
    return SUCCESSFUL;
}

// 处理单相电流数据
Static int process_phase_current(int phase_idx, float input_curr)
{
    unsigned char status = 0;
    char zero_curr = 0;
    float slope_curr = 0.0f;
    char buff[MAX_LETTERS];

    rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));

    // 获取当前相的偏移参数
    get_one_para(ACMU_PARA_ID_AC_PHASE_CURR_ZERO_OFFSET + phase_idx, &zero_curr);
    get_one_para(ACMU_PARA_ID_AC_PHASE_CURR_SLOPE_OFFSET + phase_idx, &slope_curr);

    // 计算并设置数据
    float phase_curr = input_curr * slope_curr + zero_curr;
    phase_curr = phase_curr > 630.0f ? 630.0f : phase_curr;

    get_one_para(ACMU_PARA_ID_SYS_AC_MUTUAL_INDUCTANCE_CONFIGURATION_OFFSET + phase_idx, &status);
    phase_curr = (status == 0 ? 0 : phase_curr);
    rt_snprintf_s(buff, sizeof(buff), "I%d:%5.3f %5.1f", phase_idx + 1, input_curr, phase_curr);
    SetTraceStr(11 + phase_idx, buff);

    set_one_data(ACMU_DATA_ID_PHASE_CURR + phase_idx, &phase_curr);

    return SUCCESSFUL;
}

// 处理传感器方式的数据
Static int handle_sensor_mode_data(void)
{
    calculate_voltage_rms(s_data_base_addr, &s_vol_rms_sensor[0]);
    calculate_current_rms(s_data_base_addr, &s_curr_rms_sensor[0]);
    // 处理传感器方式的电压数据
    for(int i = 0; i < VOL_PHASE_NUM; i++)
    {
        process_phase_voltage(i, s_vol_rms_sensor[i]);
    }

    // 处理传感器方式的电流数据
    for(int i = 0; i < CURR_PHASE_NUM; i++)
    {
        process_phase_current(i, s_curr_rms_sensor[i]);
    }
    return SUCCESSFUL;
}

// 处理变送器方式的数据
Static int handle_transmitter_mode_data(void)
{
    phase_data_process(s_data_base_addr, s_current_phase, &s_three_phase_value);
    // 处理变送器方式的电压数据
    process_phase_voltage(0, s_three_phase_value.a_phase.voltage);
    process_phase_voltage(1, s_three_phase_value.b_phase.voltage);
    process_phase_voltage(2, s_three_phase_value.c_phase.voltage);

    // 处理变送器方式的电流数据
    process_phase_current(0, s_three_phase_value.a_phase.current);
    process_phase_current(1, s_three_phase_value.b_phase.current);
    process_phase_current(2, s_three_phase_value.c_phase.current);
    return SUCCESSFUL;
}

// 检查并处理传感器模式
Static bool check_sensor_mode(void)
{
    calculate_voltage_rms(s_data_base_addr, &s_vol_rms_sensor[0]);
    calculate_current_rms(s_data_base_addr, &s_curr_rms_sensor[0]);

    bool has_valid = is_voltage_in_range(s_vol_rms_sensor[0]) ||
                    is_voltage_in_range(s_vol_rms_sensor[1]) ||
                    is_voltage_in_range(s_vol_rms_sensor[2]);

    return has_valid;
}

// 检查并处理变送器模式
Static bool check_transmitter_mode(void)
{
    phase_data_process(s_data_base_addr, s_current_phase, &s_three_phase_value);
    bool has_valid = is_voltage_in_range(s_three_phase_value.a_phase.voltage) ||
                    is_voltage_in_range(s_three_phase_value.b_phase.voltage) ||
                    is_voltage_in_range(s_three_phase_value.c_phase.voltage);

    return has_valid;
}

// 变送器方式电压电流采样主函数
Static int get_vol_curr_transmit(void)
{
    // 检查数据地址有效性
    if (!s_data_base_addr || !(s_data_base_addr->adc_data_addr))
    {
        return FAILURE;
    }

    // 已确认模式：直接使用该模式处理数据
    if (s_confirmed_sample_mode != UNCONFIRMED_MODE)
    {
        if (s_confirmed_sample_mode == TRANSMIT_SAMPLE_MODE)
        {
            handle_transmitter_mode_data();
        }
        return SUCCESSFUL;
    }

    bool current_mode_valid = check_transmitter_mode();
    if (current_mode_valid)
    {
        s_confirmed_sample_mode = TRANSMIT_SAMPLE_MODE;
    }

    return SUCCESSFUL;
}

// 传感器方式电压电流采样主函数
Static int get_vol_curr_sensor(void)
{
    // 检查数据地址有效性
    if (!s_data_base_addr || !(s_data_base_addr->adc_data_addr))
    {
        return FAILURE;
    }

    // 已确认模式：直接使用该模式处理数据
    if (s_confirmed_sample_mode != UNCONFIRMED_MODE)
    {
        if (s_confirmed_sample_mode == SENSOR_SAMPLE_MODE)
        {
            handle_sensor_mode_data();
        }
        return SUCCESSFUL;
    }

    // 未确认模式：优先检查传感器模式，无效则检查变送器模式
    bool current_mode_valid = check_sensor_mode();
    if (current_mode_valid)
    {
        s_confirmed_sample_mode = SENSOR_SAMPLE_MODE;
    }

    return SUCCESSFUL;
}

/**
 * @brief 设置多路复用器通道选择
 * @param channel 通道号（0-7）
 */
void set_mux_channel(uint8_t channel) {
    rt_pin_write(MUX_A0, (channel & 0x01) ? PIN_HIGH : PIN_LOW); // 控制Bit0
    rt_pin_write(MUX_A1, ((channel >> 1) & 0x01) ? PIN_HIGH : PIN_LOW); // 控制Bit1
    rt_pin_write(MUX_A2, ((channel >> 2) & 0x01) ? PIN_HIGH : PIN_LOW); // 控制Bit2
}

/**
 * @brief 读取指定芯片和通道的状态
 * @param chip 芯片编号（1-3）
 * @param channel 通道编号（0-7）
 * @return 状态值，0xFF表示错误
 */
rt_uint8_t read_state(uint8_t chip, uint8_t channel) {
    /* 1. 配置多路复用器通道 */
    set_mux_channel(channel);

    /* 2. 延时确保信号稳定 */
    rt_thread_mdelay(1);

    /* 3. 根据芯片编号读取对应引脚状态 */
    rt_uint8_t state;
    switch (chip) {
        case 1: state = rt_pin_read(DI_CS1); break;
        case 2: state = rt_pin_read(DI_CS2); break;
        case 3: state = rt_pin_read(DI_CS3); break;
        default: return 0xFF;  // 错误值
    }

    return state;
}

/**
 * @brief 读取所有DI状态并存储到ACMU
 */
int di_read_all_states() {
    rt_uint8_t states[3][8]; // 三维数组存储3个芯片×8个通道的状态
    rt_uint8_t temp = 0;

    /* 遍历所有芯片和通道 */
    for (rt_uint8_t chip = 1; chip <= 3; chip++) {
        for (rt_uint8_t ch = 0; ch < 8; ch++) {
            states[chip-1][ch] = read_state(chip, ch);
        }
    }

    /* 设置各传感器数据到ACMU */
    set_one_data(ACMU_DATA_ID_FLOOD_SWITCH_STATUS,&states[0][0]);
    temp = !states[0][1];
    set_one_data(ACMU_DATA_ID_FUMES_SWITCH_STATUS, &temp);
    // 芯片1的NO2为空
    set_one_data(ACMU_DATA_ID_AC_INPUT_SWITCH,&states[0][3]);

    /* 处理芯片1剩余通道 */
    for(int i = 4; i < 8; i++) {
        set_one_data(ACMU_DATA_ID_AC_OUT_SWITCH + (i - 4), &states[0][i]);
    }

    /* 处理芯片2全部通道 */
    for(int i = 0; i < 8; i++) {
        set_one_data(ACMU_DATA_ID_AC_OUT_SWITCH + (i + 4), &states[1][i]);
    }

    /* 处理芯片3前4个通道 */
    for(int i = 0; i < 4; i++) {
        set_one_data(ACMU_DATA_ID_INPUT_RELAY + i, &states[2][i]);
    }

    /* 处理芯片3特殊通道 */
    set_one_data(ACMU_DATA_ID_LIGHT_PRO_CIRCUIT_C,  &states[2][4]);
    set_one_data(ACMU_DATA_ID_DOORMAT_SWITCH_STATUS, &states[2][5]);
    // 芯片3的NO6，NO7为空

    return 0;
}

// static int s_sample_test_flag = FALSE;
// 
// void sample_main(void* parameter)
// {
//     rt_tick_t tk_cnt = 0, tk_recorder = 0, tk_sample = 0;
//     init_extreme_data();
//     thread_monitor_register("sample");
//     while (is_running(TRUE))
//     {
//         thread_monitor_update_heartbeat();
//         tk_cnt++;
//         if (tk_cnt - tk_recorder == RECORDER_THREAD_DELAY_TIME)
//         {
//             if(rt_sem_take(&sync_adc_rx_record_sem, 20) == RT_EOK)
//             {
//                 if(s_sample_test_flag == FALSE)
//                 {
//                     get_vol_curr_sensor();
//                 }                
//             }
//             tk_recorder = tk_cnt;
//         }
//         if (tk_cnt - tk_sample == SAMPLE_THREAD_DELAY_TIME)
//         {
//             if(rt_sem_take(&sync_adc_rx_sample_sem, 200) == RT_EOK)
//             {
//                 if(s_sample_test_flag == FALSE)
//                 {
//                     get_acmu_env_temp();
//                     get_vol_curr_transmit();
//                 }
//                 rt_sem_clear_value(&sync_adc_rx_sample_sem);
//             }
//             if(s_sample_test_flag == FALSE)
//             {
//                 get_acmu_env_hum();
//             }           
//             update_alarm_status();
//             save_extreme_data();
//             di_read_all_states();
//             phase_switch();
//             tk_sample = tk_cnt;
//         }
//         rt_thread_mdelay(1);
//     }
// }
// 
// int set_sample_data(int argc, char *argv[])
// {
//     int ret=1;
//     float f_data;
//     int index;

//     if (strcmp(argv[1], "open") == 0)
//     {
//         //停止实际采用，启动模拟采样
//         s_sample_test_flag = TRUE;
//         rt_kprintf("set_sample_data open success\n");
//         ret = 0;
//     }
//     if (strcmp(argv[1], "close") == 0)
//     {
//         //启动实际采用，关闭模拟采样
//         s_sample_test_flag = FALSE;
//         rt_kprintf("set_sample_data close success\n");
//         ret = 0;
//     }

//     else if(strcmp(argv[1], "line_volt") == 0 && argc >2 && s_sample_test_flag == TRUE)
//     {
//         f_data  = atof(argv[2]);
//         index = atoi(argv[3]);
//         if(index <= 2)
//         {
//             ret = set_one_data(ACMU_DATA_ID_LINE_VOLT + index, &f_data);
//         }
//     }
//     else if(strcmp(argv[1], "phase_curr") == 0 && argc >2 && s_sample_test_flag == TRUE)
//     {
//         f_data  = atof(argv[2]);
//         index = atoi(argv[3]);
//         if(index <= 2)
//         {
//             ret = set_one_data(ACMU_DATA_ID_PHASE_CURR + index, &f_data);
//         }
//     }
//     else if(strcmp(argv[1], "phase_volt") == 0 && argc >2 && s_sample_test_flag == TRUE)
//     {
//         f_data  = atof(argv[2]);
//         index = atoi(argv[3]);
//         if(index <= 2)
//         {
//             ret = set_one_data(ACMU_DATA_ID_PHASE_VOLT + index, &f_data);
//         }
//     }
//     else if(strcmp(argv[1], "env_temp") == 0 && s_sample_test_flag == TRUE)
//     {
//         f_data  = atof(argv[2]);
//         ret = set_one_data(ACMU_DATA_ID_ENV_TEMP, &f_data);
//     }
//     else if(strcmp(argv[1], "env_humid") == 0 && s_sample_test_flag == TRUE)
//     {
//         f_data  = atof(argv[2]);
//         ret = set_one_data(ACMU_DATA_ID_ENV_HUMIDITY, &f_data);
//     }

//     get_one_data(ACMU_DATA_ID_LINE_VOLT, &f_data);
//     rt_kprintf("line_volt:%f\n", f_data);
//     get_one_data(ACMU_DATA_ID_LINE_VOLT+1, &f_data);
//     rt_kprintf("line_volt:%f\n", f_data);
//     get_one_data(ACMU_DATA_ID_LINE_VOLT+2, &f_data);
//     rt_kprintf("line_volt:%f\n", f_data);

//     get_one_data(ACMU_DATA_ID_PHASE_CURR, &f_data);
//     rt_kprintf("phase_curr:%f\n", f_data);
//     get_one_data(ACMU_DATA_ID_PHASE_CURR+1, &f_data);
//     rt_kprintf("phase_curr:%f\n", f_data);
//     get_one_data(ACMU_DATA_ID_PHASE_CURR+2, &f_data);
//     rt_kprintf("phase_curr:%f\n", f_data);
    
//     get_one_data(ACMU_DATA_ID_PHASE_VOLT, &f_data);
//     rt_kprintf("phase_volt:%f\n", f_data);
//     get_one_data(ACMU_DATA_ID_PHASE_VOLT+1, &f_data);
//     rt_kprintf("phase_volt:%f\n", f_data);
//     get_one_data(ACMU_DATA_ID_PHASE_VOLT+2, &f_data);
//     rt_kprintf("phase_volt:%f\n", f_data);

//     get_one_data(ACMU_DATA_ID_ENV_TEMP, &f_data);
//     rt_kprintf("env_temp:%f\n", f_data);

//     get_one_data(ACMU_DATA_ID_ENV_HUMIDITY, &f_data);
//     rt_kprintf("env_humid:%f\n", f_data);

//     if(ret)
//     {
//         rt_kprintf("set_sample_data input error\n");
//     }
//     return 0;
// }

// MSH_CMD_EXPORT(set_sample_data, set sample);//注册打桩测试函数