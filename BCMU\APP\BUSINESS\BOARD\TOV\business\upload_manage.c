#include <string.h>
#include <rtthread.h>
#include <stdlib.h>
#include "upload_manage.h"
#include "msg.h"
#include "device_type.h"
#include "sps.h"
#include "protocol_layer.h"
#include "msg_id.h"
#include "softbus.h"
#include "dev_csu_comm.h"
#include "utils_rtthread_security_func.h"
#include "utils_data_transmission.h"
#include "his_data.h"
#include "partition_def.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "his_record.h"
#include "log_mgr_api.h"

Static int parse_upload_file_list(void* dev_inst, void *cmd_buf);
Static int parse_upload_file_data(void* dev_inst, void *cmd_buf);
Static int parse_filedata_first_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no);
Static int pack_upload_file_list(void* dev_inst, void *cmd_buf);
Static int pack_filelist_first_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no);
Static int pack_filelist_other_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no);
Static int pack_upload_file_data(void* dev_inst, void *cmd_buf);
Static int pack_filedata_first_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no);
Static int pack_filedata_next_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no);
Static int parse_update_trig(void* dev_inst, void *cmd_buf);
Static int pack_update_trig(void* dev_inst, void *cmd_buf);
Static void upload_trigTimer_timeout();
Static void upload_trig_end();

static cmd_handle_register_t s_upload_cmd_handle[] = {
    {DEV_CSU_CAN, UPLOAD_FILE_TRIG,               CMD_TYPE_NO_POLL, parse_update_trig, pack_update_trig},
    {DEV_CSU_CAN, UPLOAD_FILE_LIST,               CMD_TYPE_NO_POLL, parse_upload_file_list, pack_upload_file_list},
    {DEV_CSU_CAN, UPLOAD_FILE_DATA,               CMD_TYPE_NO_POLL, parse_upload_file_data, pack_upload_file_data},
};

T_FileUploadStruct s_upload_trsdata_inf = {0};
Static download_trig_ctr_inf_t s_upload_trig_inf = {0};
rt_timer_t         upload_trigTimer = NULL;

/* 解析上传文件列表帧 首发帧和其他帧DATA均为空 */
int parse_upload_file_list(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = NULL;
    T_FileUploadStruct* trsdata_ctr_inf = NULL;
    bottom_comm_cmd_head_t* proto_head = NULL;
    trsdata_ctr_inf = &s_upload_trsdata_inf;

    //入参校验
    if ( cmd_buf == NULL || ((cmd_buf_t*)cmd_buf)->buf == NULL)
    {
        return FAILURE;
    }

    tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    //判断协议层解析数据是否正确
    if (tran_cmd_buf->rtn != DOWNLOAD_FRAME_CORRECT)
    {
        return FAILURE;
    }
    proto_head = (bottom_comm_cmd_head_t*)tran_cmd_buf->cmd->req_head;
    trsdata_ctr_inf->cur_frame_no = proto_head->append;
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;

    return SUCCESSFUL;
}

/* 打包上传文件列表帧 打包首发帧和其他帧 */
int pack_upload_file_list(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    unsigned short frm_no = 0;
    bottom_comm_cmd_head_t* req_head = NULL;
    bottom_comm_cmd_head_t* ack_head = NULL;

    // 入参校验
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL)
    {
        return FAILURE;
    }

    //判断协议层解析数据是否正确
    if (tran_cmd_buf->rtn != DOWNLOAD_FRAME_CORRECT)
    {
        return FAILURE;
    }
    
    req_head = (bottom_comm_cmd_head_t*)tran_cmd_buf->cmd->req_head;
    ack_head = (bottom_comm_cmd_head_t*)tran_cmd_buf->cmd->ack_head;
    frm_no = req_head->append;
    ack_head->append = frm_no;
        
    if (frm_no == 0)
    {
        upload_trig_end();
        pack_filelist_first_frame(tran_cmd_buf, frm_no);
    }
    else
    {
        pack_filelist_other_frame(tran_cmd_buf, frm_no);
    }
    
    if (s_upload_trsdata_inf.tFileList.wListTotalFrame ==  req_head->append &&
        s_upload_trsdata_inf.tFileList.wListTotalFrame != 0)
    {
        rt_memset_s(&s_upload_trsdata_inf, sizeof(T_FileUploadStruct), 0, sizeof(T_FileUploadStruct));
    }

    return SUCCESSFUL;
}

/* 打包上传文件列表首发帧 */
Static int pack_filelist_first_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no)
{
    int i = 0;
    int offset = 0;
    int totalByte = 0;

    //获取文件列表
    get_record_file_list_new((char**)s_upload_trsdata_inf.tFileList.pucListName, (char*)s_upload_trsdata_inf.tFileList.aucListNameLen, 
                    &s_upload_trsdata_inf.tFileList.ucListTotalNum);

    //计算总帧数
    for (i=0; i<s_upload_trsdata_inf.tFileList.ucListTotalNum; i++)
    {
        totalByte  += rt_strnlen_s(s_upload_trsdata_inf.tFileList.pucListName[i], s_upload_trsdata_inf.tFileList.aucListNameLen[i]);
    }
    s_upload_trsdata_inf.tFileList.wListTotalFrame = (totalByte + UPLOAD_DATA_FRAME_LEN - 1) / UPLOAD_DATA_FRAME_LEN;

    s_upload_trsdata_inf.tFileList.wListTotalFrame++;

    put_int16_to_buff(&tran_cmd_buf->buf[offset], s_upload_trsdata_inf.tFileList.wListTotalFrame);

    offset += 2;
    tran_cmd_buf->data_len = offset;
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;

    return SUCCESSFUL;
}

/* 打包上传文件列表其他帧 */
Static int pack_filelist_other_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no)
{
    int i = 0;
    int offset = 0;

    // 有几个文件
    tran_cmd_buf->buf[offset] = s_upload_trsdata_inf.tFileList.ucListTotalNum;
    offset += 1;

    //获取文件列表，每个文件名占有40字节
    for (i=0; i<s_upload_trsdata_inf.tFileList.ucListTotalNum; i++)
    {
        rt_memset_s(&tran_cmd_buf->buf[offset], UPLOAD_FILE_NAME_MAX_LEN, 0x00, UPLOAD_FILE_NAME_MAX_LEN);
        rt_memcpy_s(&tran_cmd_buf->buf[offset], s_upload_trsdata_inf.tFileList.aucListNameLen[i], 
            s_upload_trsdata_inf.tFileList.pucListName[i], s_upload_trsdata_inf.tFileList.aucListNameLen[i]);
        offset += UPLOAD_FILE_NAME_MAX_LEN;
    }
    
    tran_cmd_buf->data_len = offset;
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;

    return SUCCESSFUL;
}

/* 解析上传文件数据帧 解析首发帧，其他帧DATA为空 */
Static int parse_upload_file_data(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    T_FileUploadStruct* trsdata_ctr_inf = NULL;
    bottom_comm_cmd_head_t* proto_head = NULL;
    trsdata_ctr_inf = &s_upload_trsdata_inf;
    unsigned char ctrl_sample_switch[2] = {0};
    // 入参校验
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL)
    {
        trsdata_ctr_inf->rtn = DOWNLOAD_FRAME_ERR;
        return FAILURE;
    }

    //判断协议层解析数据是否正确
    if (tran_cmd_buf->rtn != DOWNLOAD_FRAME_CORRECT)
    {
        trsdata_ctr_inf->rtn = DOWNLOAD_FRAME_ERR;
        return FAILURE;
    }
    proto_head = (bottom_comm_cmd_head_t*)tran_cmd_buf->cmd->req_head;
    trsdata_ctr_inf->cur_frame_no = proto_head->append;
    trsdata_ctr_inf->rtn = DOWNLOAD_FRAME_CORRECT;
    rt_timer_start(upload_trigTimer);
    if (trsdata_ctr_inf->cur_frame_no == 0)
    {
        s_upload_trig_inf.trig_times = 0;
        unsigned char upfile_sta = 1;
        set_one_data(TOV_DATA_ID_RECORD_UPFILE_STATUS, &upfile_sta);
        parse_filedata_first_frame(tran_cmd_buf, trsdata_ctr_inf->cur_frame_no);
        // 关闭采样
        ctrl_sample_switch[0] = FALSE;  
        ctrl_sample_switch[1] = FALSE;  
        pub_msg_to_thread(CTRL_SAMPLE_SWITCH, &ctrl_sample_switch, sizeof(ctrl_sample_switch));
    }

    return SUCCESSFUL;
}

/* 解析上传文件数据首发帧 */
Static int parse_filedata_first_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no)
{
    s_upload_trsdata_inf.tFileName.wReqFileNameLen = UPLOAD_FILE_NAME_MAX_LEN; //请求的文件名长度
    rt_memset_s( s_upload_trsdata_inf.tFileName.ucReqFileName , UPLOAD_FILE_NAME_MAX_LEN , 0 , UPLOAD_FILE_NAME_MAX_LEN );
    rt_memcpy_s(s_upload_trsdata_inf.tFileName.ucReqFileName, sizeof(s_upload_trsdata_inf.tFileName.ucReqFileName), 
        &tran_cmd_buf->buf[0], s_upload_trsdata_inf.tFileName.wReqFileNameLen); //请求的文件名

    return SUCCESSFUL;
}

/* 打包上传文件数据帧 打包首发帧和其他帧 */
Static int pack_upload_file_data(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    unsigned short frm_no = 0;
    bottom_comm_cmd_head_t* proto_head = NULL;  
    bottom_comm_cmd_head_t* ack_head = NULL;

    // 入参校验
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL)
    {
        return FAILURE;
    }

    //判断协议层解析数据是否正确
    if (tran_cmd_buf->rtn != DOWNLOAD_FRAME_CORRECT)
    {
        return FAILURE;
    }

    proto_head = (bottom_comm_cmd_head_t*)tran_cmd_buf->cmd->req_head;
    ack_head = (bottom_comm_cmd_head_t*)tran_cmd_buf->cmd->ack_head;
    frm_no = proto_head->append;
    ack_head->append = frm_no;
    
    if (frm_no == 0)
    {
        pack_filedata_first_frame(tran_cmd_buf, frm_no);
    }
    else
    {
        pack_filedata_next_frame(tran_cmd_buf, frm_no);
    } 
    return SUCCESSFUL;
}

/* 打包上传文件数据首发帧 */
Static int pack_filedata_first_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no)
{
    int offset = 0;
    time_t pt_time;
    static unsigned int crc_ctx = 0 ^ 0xFFFFFFFF; // 校验和

    if (s_upload_trsdata_inf.rtn == DOWNLOAD_FRAME_ERR)
    {
        tran_cmd_buf->rtn = DOWNLOAD_FRAME_ERR;
        return FAILURE;
    }

    // 获取文件总长度，his_data.c提交后放开
    s_upload_trsdata_inf.uFileTotalLen = get_record_file_len_new(s_upload_trsdata_inf.tFileName.ucReqFileName);

    // 上传帧字节数
    put_int16_to_buff(&(tran_cmd_buf->buf[offset]), UPLOAD_DATA_FRAME_LEN);
    offset += 2;

    //总帧数
    s_upload_trsdata_inf.wFileTotalFrame = 1 + (s_upload_trsdata_inf.uFileTotalLen + UPLOAD_DATA_FRAME_LEN - 1) 
                                                / UPLOAD_DATA_FRAME_LEN; //向上取整
    put_int16_to_buff(&(tran_cmd_buf->buf[offset]), s_upload_trsdata_inf.wFileTotalFrame);
    offset += 2;

    //上传文件总大小
    put_int32_to_buff(&(tran_cmd_buf->buf[offset]), s_upload_trsdata_inf.uFileTotalLen);
    offset += 4;

    //上传文件名
    rt_memcpy_s(&tran_cmd_buf->buf[offset], s_upload_trsdata_inf.tFileName.wReqFileNameLen, 
        s_upload_trsdata_inf.tFileName.ucReqFileName, s_upload_trsdata_inf.tFileName.wReqFileNameLen);
    offset += s_upload_trsdata_inf.tFileName.wReqFileNameLen;

    //上传文件时间
    pt_time = get_timestamp_s();
    rt_memset_s(&s_upload_trsdata_inf.ucFileTime[0], UPLOAD_FILE_TIME_LEN, 0, UPLOAD_FILE_TIME_LEN);
    put_time_t_to_buff(s_upload_trsdata_inf.ucFileTime, pt_time);
    rt_memcpy_s(&tran_cmd_buf->buf[offset], sizeof(s_upload_trsdata_inf.ucFileTime), s_upload_trsdata_inf.ucFileTime, sizeof(s_upload_trsdata_inf.ucFileTime));
    offset += UPLOAD_FILE_TIME_LEN;

    //文件校验码
    crc32_calc(&crc_ctx, tran_cmd_buf->buf, offset);
    s_upload_trsdata_inf.uCrc = crc_ctx;
    put_int16_to_buff(&(tran_cmd_buf->buf[offset]), s_upload_trsdata_inf.uCrc);
    offset += 4;

    tran_cmd_buf->data_len = offset;
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;

    return SUCCESSFUL;
}

/* 打包上传文件数据其他帧 */
Static int pack_filedata_next_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no)
{
    int offset = 0;
    int actual_len = 0;
    int tmp = 0;
    unsigned char ctrl_sample_switch[2] = {0};
    if (s_upload_trsdata_inf.rtn == DOWNLOAD_FRAME_ERR)
    {
        tran_cmd_buf->rtn = DOWNLOAD_FRAME_ERR;
        return FAILURE;
    }

    //当请求的数据帧号为最后一帧，打开采样控制开关
    if(s_upload_trsdata_inf.wFileTotalFrame - 1 == frm_no)
    {
        unsigned char upfile_sta = 0;
        set_one_data(TOV_DATA_ID_RECORD_UPFILE_STATUS, &upfile_sta);
        rt_timer_stop(upload_trigTimer);
        ctrl_sample_switch[0] = TRUE;     // 开启采样
        if(rt_memcmp((char*)(s_upload_trsdata_inf.tFileName.ucReqFileName), RECORD_FAULT_DATA, sizeof(RECORD_FAULT_DATA)) == 0)
        {
            rt_kprintf("upload record data success\n");
            ctrl_sample_switch[1] = TRUE;     // 上传成功
            // unsigned char auth_code = 0x10;
            // delete_record_data_new(auth_code, RECORD_DATA_ACC_INDEX);//删除文件
        }
        else
        {
            rt_kprintf("upload extreme data success\n");
            ctrl_sample_switch[1] = FALSE;    // 维持原样
        }
        pub_msg_to_thread(CTRL_SAMPLE_SWITCH, &ctrl_sample_switch, sizeof(ctrl_sample_switch));
    }
    
    //求当前文件读取偏移
    offset = (frm_no - 1) * UPLOAD_DATA_FRAME_LEN;
    rt_memset_s(tran_cmd_buf->buf, S_BUFF_LEN_2048, 0, S_BUFF_LEN_2048);
    tmp = s_upload_trsdata_inf.uFileTotalLen - offset ;
    actual_len =( tmp >= UPLOAD_DATA_FRAME_LEN ) ? UPLOAD_DATA_FRAME_LEN : tmp ;
    //获取文件数据，his_data.c提交后放开
    actual_len = get_frame_from_record_file_new(s_upload_trsdata_inf.tFileName.ucReqFileName, offset, actual_len, tran_cmd_buf->buf);

    tran_cmd_buf->data_len = actual_len;
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;
    return SUCCESSFUL;
}

Static int parse_update_trig(void* dev_inst, void *cmd_buf) {
    unsigned char upfile_sta = 0;
    get_one_data(TOV_DATA_ID_RECORD_UPFILE_STATUS, &upfile_sta);
    if(upfile_sta == TRUE)
    {
        rt_kprintf("record file is uploading\n");
        return NO_NEED_REPONSE;
    }
    //开启触发定时器
    rt_timer_start(upload_trigTimer);
    s_upload_trig_inf.trig_success = FALSE;
    s_upload_trig_inf.trig_times++;
    //触发成功
    if(s_upload_trig_inf.trig_times >= DOWNLOAD_TRIG_TIMES){
        s_upload_trig_inf.trig_success = TRUE;
    }
    return SUCCESSFUL;
}

Static int pack_update_trig(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    // 入参校验
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL) {
        return FAILURE;
    }

    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;
    return SUCCESSFUL;
}

void upload_manage_init(void) {

    rt_uint32_t trig_tick_num   = TRIG_TIMEOUT * 1000;      //30秒
    short i = 0;

    for(i = 0; i < sizeof(s_upload_cmd_handle)/sizeof(s_upload_cmd_handle[0]); i++) 
    {
        register_cmd_handle(&s_upload_cmd_handle[i]);
    }

    upload_trigTimer = rt_timer_create("upload_trigTimer", upload_trigTimer_timeout, RT_NULL, trig_tick_num, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
    if(upload_trigTimer == NULL)
    {
        return;
    }
    return;
}


Static void upload_trigTimer_timeout()
{
    unsigned char ctrl_sample_switch[2] = {0};
    unsigned char upfile_sta = 0;
    set_one_data(TOV_DATA_ID_RECORD_UPFILE_STATUS, &upfile_sta);
    s_upload_trig_inf.trig_times = 0;
    rt_timer_stop(upload_trigTimer);
    // 维持原样
    ctrl_sample_switch[0] = TRUE;   
    ctrl_sample_switch[1] = FALSE; 
    pub_msg_to_thread(CTRL_SAMPLE_SWITCH, &ctrl_sample_switch, sizeof(ctrl_sample_switch));
}

Static void upload_trig_end()
{
    s_upload_trig_inf.trig_times = 0;
    rt_timer_stop(upload_trigTimer);
}