#ifndef _CONCUR_MASTER_DOWNLOAD_H
#define _CONCUR_MASTER_DOWNLOAD_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "device_type.h"

#define DOWNLOAD_CONCURRENT_DATA_TRIG            1       ///<  并发升级下，主机发送触发
#define DOWNLOAD_CONCURRENT_DATA                 2       ///<  并发升级下，主机发送数据包
#define DOWNLOAD_CONCURRENT_ACK_FRAME            3       ///<  并发升级下，主机发送确认帧
#define DOWNLOAD_CONCURRENT_UPDATE_FRAME         4       ///<  并发升级下，主机发送更新帧

dev_type_t* init_master_dev_inverter();

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _CONCUR_MASTER_DOWNLOAD_H

