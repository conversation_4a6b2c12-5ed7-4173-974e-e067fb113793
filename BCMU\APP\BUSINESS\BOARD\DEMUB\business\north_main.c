#include <string.h>
#include <stdlib.h>
#include <rtthread.h>
#include "utils_thread.h"
#include "device_type.h"
#include "msg.h"
#include "cmd.h"
#include "north_main.h"
#include "data_type.h"
#include "sps.h"
#include "utils_server.h"
#include "utils_string.h"
#include "utils_rtthread_security_func.h"
#include "protocol_modbus_comm.h"
#include "dev_north_demub_modbus.h"
#include "dev_update_manage_handle.h"
#include "dev_north_demub_apptest.h"
#include "realdata_save.h"
#include "realdata_id_in.h"
#include "ee_public_info.h"
#include "storage.h"
#include "bspconfig.h"
#include "utils_flag.h"

static unsigned char s_dev_type[MAX_DEV_INST_NUM] = {
    DEV_NORTH_DEMUB_APPTEST,
    DEV_NORTH_DEMUB_UPDATE,
    DEV_NORTH_DEMUB,  // modbus协议需放在最后
};

static dev_inst_t* north_dev_demub;
static dev_inst_t* s_north_dev_inst[MAX_DEV_INST_NUM] = {NULL};
static unsigned char s_backup_buff[MAX_NORTH_COMM_BUFF_LEN] = {0};
static unsigned int s_backup_len = 0;

north_mgr_t * init_thread_data(link_inst_t* link_inst, unsigned char mod_id) {
    north_mgr_t* north_mgr = NULL;

    north_mgr = rt_malloc(sizeof(north_mgr_t));
    if (north_mgr == NULL)
        return NULL;
    
    north_mgr->cmd_buff = rt_malloc(sizeof(cmd_buf_t));
    if (north_mgr->cmd_buff == NULL) 
    {
        rt_free(north_mgr);
        return NULL;
    }

    north_mgr->link_inst = link_inst;
    return north_mgr;
}

static msg_map s_north_msg_map[] =
{
         {0,NULL},//临时添加解决编译问题
};


int update_dev_addr()
{
    unsigned short data = 0;
    handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char*)&data, sizeof(data), DEVICE_ADDR_OFFSET);
    if(data == 0xFFFF)
    {
        data = DEMUB_DEFAULT_DEV_ADDR;
    }
    set_one_data(DEMUB_DATA_ID_DEVICE_ADDR, &data);
    return SUCCESSFUL;
}


/* 北向初始化*/
void*  init_north(void * param) {
    RETURN_VAL_IF_FAIL(param != NULL, NULL);
    server_info_t *server_info = (server_info_t *)param;
    north_mgr_t* north_mgr = NULL;
    unsigned char loop = 0;
    unsigned char src_addr = 1;

    update_dev_addr();
    for(loop = 0; loop < MAX_DEV_INST_NUM; loop ++)
    {
        north_dev_demub = init_dev_inst(s_dev_type[loop]);
        RETURN_VAL_IF_FAIL(north_dev_demub != NULL, NULL);
        s_north_dev_inst[loop] = north_dev_demub;
    }

    set_host_addr(src_addr);
    bottom_update_init();
    server_info->server.server.map_size = sizeof(s_north_msg_map) / sizeof(msg_map);
    register_server_msg_map(s_north_msg_map, server_info);
    apptest_register_cmd_table();
    north_mgr = init_thread_data(north_dev_demub->dev_type->link_inst, MOD_NORTH_DEMUB_COMM);
    return north_mgr;
}


void handle_received_data(north_mgr_t* north_mgr) {
    dev_inst_t north_dev_inst_tmp = {0};
    unsigned short dev_addr = 1;
    for(int loop = 0; loop < MODBUS_PROTO_INDEX ; loop ++)
    {
        
        if(get_apptest_flag() == TRUE && loop == 1)
        {
            continue;
        }
        
        rt_memcpy_s(&north_dev_inst_tmp, sizeof(dev_inst_t), s_north_dev_inst[loop],sizeof(dev_inst_t));
        rt_memcpy_s(north_mgr->cmd_buff->buf, MAX_NORTH_COMM_BUFF_LEN, s_backup_buff, s_backup_len);
        north_mgr->cmd_buff->data_len = s_backup_len;
        get_one_data(DEMUB_DATA_ID_DEVICE_ADDR, &dev_addr);
        set_host_addr(dev_addr);
        if(SUCCESSFUL == protocol_parse_recv(&north_dev_inst_tmp, north_mgr->cmd_buff))
        {
            cmd_send(&north_dev_inst_tmp, north_mgr->cmd_buff);
            rt_memset_s(s_backup_buff, MAX_NORTH_COMM_BUFF_LEN, 0, MAX_NORTH_COMM_BUFF_LEN);
            s_backup_len = 0;
            rt_sem_clear_value(&(north_mgr->link_inst->rx_sem));
            break;
        }
    }
}



void handle_modbus_data(north_mgr_t* north_mgr)
{
    rt_memcpy_s(north_mgr->cmd_buff->buf, MAX_NORTH_COMM_BUFF_LEN, s_backup_buff, s_backup_len);
    north_mgr->cmd_buff->data_len = s_backup_len;
    north_mgr->cmd_buff->cmd = NULL;
    unsigned short src_addr = 1;
    get_one_data(DEMUB_DATA_ID_DEVICE_ADDR, (unsigned char*)&src_addr);
    s_north_dev_inst[MODBUS_PROTO_INDEX]->dev_addr = src_addr;
    if (SUCCESSFUL == protocol_parse_recv(s_north_dev_inst[MODBUS_PROTO_INDEX], north_mgr->cmd_buff)) {
        cmd_send(s_north_dev_inst[MODBUS_PROTO_INDEX], north_mgr->cmd_buff);
    }
    rt_memset_s(s_backup_buff, MAX_NORTH_COMM_BUFF_LEN, 0, MAX_NORTH_COMM_BUFF_LEN);
    s_backup_len = 0;
}


/*收发线程 */
void north_comm_th(void *param) {
    RETURN_IF_FAIL(param != NULL);
    north_mgr_t* north_mgr = (north_mgr_t*)param;
    unsigned char time_delay = 20;

    while (is_running(TRUE)) {
        if (RT_EOK == rt_sem_take(&(north_mgr->link_inst->rx_sem), THREAD_TICK)) {
            if (FAILURE == linker_recv(s_north_dev_inst[0], north_mgr->cmd_buff)) {
                continue;
            }
            s_backup_len = north_mgr->cmd_buff->data_len;
            rt_memcpy_s(s_backup_buff, MAX_NORTH_COMM_BUFF_LEN, north_mgr->cmd_buff->buf, s_backup_len);
            handle_received_data(north_mgr);
        }

        
        else
        {
            if (s_backup_len > 0 && get_apptest_flag() != TRUE) {
                handle_modbus_data(north_mgr);
            }
            s_north_dev_inst[0]->dev_type->link_inst->r_cache.index = 0;
            rt_memset_s(s_backup_buff, MAX_NORTH_COMM_BUFF_LEN, 0, MAX_NORTH_COMM_BUFF_LEN);
            s_backup_len = 0;
        }
        

        rt_thread_mdelay(time_delay);
    }

}


