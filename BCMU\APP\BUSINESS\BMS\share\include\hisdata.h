/**************************************************************************
* Copyright (C) 2010-2011, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要
* 其他说明：
***************************************************************************/

/***********************  常量定义  ************************/
#ifndef SOFTWARE_SRC_APP_HISDATA_H_
#define SOFTWARE_SRC_APP_HISDATA_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "common.h"
#include "commBdu.h"
#include "sample.h"
#include <rtdevice.h>
#include "protocol.h"

#define HISACT_MIN_NUM          500
#define HISALARM_MIN_NUM        10000
#define HISDATA_MIN_NUM         10000
#define DCRDATA_MIN_NUM         1000
#define HISFILE_NUM             4           // 历史记录文件数量
#define HISDATA_VER             3           // 历史数据版本
#define HISEXTREME_VER          1           // 极值记录版本
#define HISTORICAL_ACTION       0           // 历史操作
#define HISTORICAL_ALARM        1           // 历史告警
#define HISTORICAL_DATA         2           // 历史数据
#define DCR_RECORD              3           // 直流内阻记录
#define HISTORICAL_TOWERDATA    4           // 铁塔历史数据(待统一)

#define HISDATA_RESERVED_SIZE   8           /**< 历史数据预留字节数 */

#define BDU_RECORD_MIN_NUM      100         // 录波记录存储条数
#define BDU_RECORD_SIZE         (499 + 2)   // 单条录波记录所占字节数（sizeof(序列化T_BduRecordData) + sizeof(wCRC)）

/* 历史互备份数据索引：0历史数据、1历史告警、2操作记录 */
#define HISDATA_BACKUP_INDEX       0
#define HISALARM_BACKUP_INDEX      1
#define HISACT_BACKUP_INDEX        2
#define BDURECORD_BACKUP_INDEX     3
#define EXTREME_BACKUP_INDEX       4
#define ANALYSE_BACKUP_INDEX       5
#define BACKUP_FILE_NAME_MAX_LEN   40       //互备份文件名最大长度

/* 备份管理文件 */
#define HISDATA_BACKUP_MANAGE_FILE "/BACKUP/HisDataBackupMag"
#define HISALARM_BACKUP_MANAGE_FILE "/BACKUP/HisAlarmBackupMag"
#define HISACT_BACKUP_MANAGE_FILE "/BACKUP/HisActBackupMag"
#define BDURECORD_BACKUP_MANAGE_FILE "/BACKUP/BduRecBackupMag"
#define EXTREME_BACKUP_MANAGE_FILE "/BACKUP/ExtremeBackupMag"
#define ANALYSE_BACKUP_MANAGE_FILE "/BACKUP/AnalyseBackupMag"
#define FILELIST                   "/BACKUP/filelist"

/* 本地文件 */
#define HISDATA_LOCAL_FILE      "hisdata"
#define HISALARM_LOCAL_FILE     "hisalarm"
#define HISACT_LOCAL_FILE       "hisaction"
#define BDURECORD_LOCAL_FILE    "BduRecordData"
#define ANALYSE_LOCAL_FILE      "AnalyseInfo"
#define HISEXTREME_LOCAL_FILE   "hisextreme"

enum    //历史数据状态位
{
    CELL_EQU_STA_BIT = 0,   //电芯均衡启动状态(锂)  1bit
    CHG_PROT_STA_BIT,       //电池充电保护(锂)  1bit
    DISCH_PROT_STA_BIT,     //电池放电保护(锂)  1bit
    //BATTPACK_STA_BIT,       //电池组状态(锂)    1bit 电池组状态有多种状态
    //CHG_TEMPL_PROT_BIT,     //电池组充电低温保护(锂)    1bit
    BATT_VOLH_PROT_BIT,     //电池组过压保护(锂)    1bit
    //CHG_TEMPH_PROT_BIT,     //电池组充电过温保护(锂)    1bit
    //DISCH_TEMPL_PRPT_BIT,   //电池组放电低温保护(锂)    1bit
    BATT_VOLL_PROT_BIT,     //电池组欠压保护(锂)    1bit
    //DISCH_TEMPH_PRPT_BIT,   //电池组放电过温保护(锂)    1bit
    CHG_CURH_PROT_BIT,      //电池组充电过流保护(锂)    1bit
    DISCH_CURH_PROT_BIT,    //电池组放电过流保护(锂)    1bit
    LIMIT_STA_BIT,      //限流状态  1bit
    BDU_SLEEP_STA_BIT,   //bdu休眠状态  1bit
    BMS_CHGDISCH_STA_BIT,//bms充放电状态 3bit(0:常规充电；1：常规放电；2：升压充电；3：升压放电；4：降压充电；5：降压放电；6：充放电停止)
};

#ifdef DEVICE_USING_D121
/***************************************************************************
 * @brief   历史操作记录结构体
 **************************************************************************/
typedef struct
{
    time_t tTime;
    WORD ucActId;
    char aucMsg[20];
    rt_uint16_t wCrc;
} T_ActionRecord;
#else
/***************************************************************************
 * @brief   历史操作记录结构体
 **************************************************************************/
typedef struct
{
    time_t tTime;
    BYTE ucActId;
    char aucMsg[20];
    rt_uint16_t wCrc;
} T_ActionRecord;
#endif

/***************************************************************************
 * @brief   历史数据结构体(D121)
 **************************************************************************/
#if (HISDATA_VER == 2)
typedef struct
{
    time_t tStartTime;                          //时间
    rt_uint8_t ucSysMode;                       //系统模式
    rt_uint8_t ucCellEvent;                     //电芯状态事件
    rt_uint8_t ucCellVolEvent;                  //单体电压事件
    rt_uint16_t wTempEvent;                     //温度事件
    rt_uint8_t ucCurrentEvent;                  //电流事件
    rt_uint8_t ucCapacityEvent;                 //容量事件
    rt_uint32_t uiFaultStatus;                  //故障状态
    rt_int16_t wBattCurr;                       //充放电流
    rt_uint16_t wBattVolt;                      //电池电压
    rt_uint16_t wBattSOC;                       //剩余容量(电池SOC)
    rt_uint8_t ucCellTempNum;                   //电芯温度数量
    rt_int16_t awCellTemp[CELL_TEMP_NUM_MAX];   //电芯温度
    rt_int16_t sEnvTemp;                        //环境温度
    rt_int16_t sPowerTemp;                      //功率温度
    rt_uint8_t ucCellVoltNum;                   //电芯电压数量
    rt_int16_t awCellVolt[CELL_VOL_NUM_MAX];    //电芯电压
    rt_uint16_t wBusVolt;                       //母排电压
    rt_uint16_t wBusCurr;                       //母排电流
    rt_uint16_t wBattCycleTimes;                //电池循环次数
    rt_uint16_t wBattBalanceStatus;             //16节单体均衡状态
    rt_uint8_t ucBattStatus;                    //电池状态
    rt_uint16_t wPowerOnVoltThre;               //来电电压阈值
    rt_uint16_t wPowerDownVoltThre;             //停电电压阈值
    rt_int16_t sConnTemp;                       //连接器温度
    rt_uint16_t wDischgSetVol;                  //放电设定电压   新增
    rt_uint16_t wSetChgCurr;                    //充电限流千分比 新增  
    rt_uint16_t wCellVoltMax;                   //单节电压最大值
    rt_uint8_t ucCellVoltMaxNo;
    rt_uint16_t wCellVoltMin;                   //单节电压最小值
    rt_uint8_t ucCellVoltMinNo;
    rt_int16_t wCellTempMax;                    //单体最高温度
    rt_uint8_t ucCellTempMaxNo;
    rt_int16_t wCellTempMin;                    //单体最低温度
    rt_uint8_t ucCellTempMinNo;
    rt_uint16_t wBattSOH;                       //电池SOH
    rt_uint16_t wStatus;                        //状态量
    rt_uint8_t  reserve1;                       //保留位置1
    rt_uint16_t reserve2;                       //保留位置2
    rt_uint16_t reserve3;                       //保留位置3
    rt_uint16_t wCheckSum;
} T_HisDataStruct;
#elif (HISDATA_VER == 3)

#pragma pack(push, 1)
typedef struct
{
    time_t tStartTime;                          // 时间
    uint8_t ucSysMode;                          // 系统模式
    uint8_t ucCellEvent;                        // 电芯状态事件
    uint8_t ucCellVolEvent;                     // 单体电压事件
    uint16_t wTempEvent;                        // 温度事件
    uint8_t ucCurrentEvent;                     // 电流事件
    uint8_t ucCapacityEvent;                    // 容量事件
    uint32_t uiFaultStatus;                     // 故障状态
    int16_t wBattCurr;                          // 充放电流
    uint16_t wBattVolt;                         // 电池电压
    uint16_t wBattSOC;                          // 剩余容量(电池SOC)
    uint8_t ucCellTempNum;                      // 电芯温度数量
    int16_t awCellTemp[CELL_TEMP_NUM_MAX];      // 电芯温度
    int16_t sEnvTemp;                           // 环境温度
    int16_t sPowerTemp;                         // 功率温度
    uint8_t ucCellVoltNum;                      // 电芯电压数量
    int16_t awCellVolt[CELL_VOL_NUM_MAX];       // 电芯电压
    uint16_t wBusVolt;                          // 母排电压
    uint16_t wBusCurr;                          // 母排电流
    uint16_t wBattCycleTimes;                   // 电池循环次数
    uint16_t wBattBalanceStatus;                // 16节单体均衡状态
    uint8_t ucBattStatus;                       // 电池状态
    uint16_t wPowerOnVoltThre;                  // 来电电压阈值
    uint16_t wPowerDownVoltThre;                // 停电电压阈值
    int16_t sConnTemp;                          // 连接器温度
    uint16_t wDischgSetVol;                     // 放电设定电压   新增
    uint16_t wSetChgCurr;                       // 充电限流千分比 新增  
    uint16_t wCellVoltMax;                      // 单节电压最大值
    uint8_t ucCellVoltMaxNo;
    uint16_t wCellVoltMin;                      // 单节电压最小值
    uint8_t ucCellVoltMinNo;
    int16_t wCellTempMax;                       // 单体最高温度
    uint8_t ucCellTempMaxNo;
    int16_t wCellTempMin;                       // 单体最低温度
    uint8_t ucCellTempMinNo;
    uint16_t wBattSOH;                          // 电池SOH
    uint16_t wStatus;                           // 状态量
    uint32_t ulTotalChgQuantity;                /**< 累计充电电量（单位：kWh，精度：2） */
    uint32_t ulTotalDischgQuantity;             /**< 累计放电电量（单位：kWh，精度：2） */
    uint8_t ucReservedSize;                     /**< 预留字节数 */
    uint8_t ucReserved[HISDATA_RESERVED_SIZE];  /**< 预留 */
    uint16_t wCheckSum;
} T_HisDataStruct;
#pragma pack(pop)

#endif /* HISDATA_VER == 3 */

/***************************************************************************
 * @brief   直流内阻数据结构体
 **************************************************************************/
typedef struct
{
    time_t tStartTime;                       //记录保存时间
    rt_uint8_t ucCellNum;                    //单体数量
    rt_uint16_t wCellDcr[CELL_VOL_NUM];      //单体DCR
    rt_uint16_t wCheckSum;
} T_DcrRecordStruct;

/***************************************************************************
 * @brief   互备份历史文件结构体
 *  **************************************************************************/
typedef struct
{
    rt_uint8_t ucFileName[40];
    rt_uint16_t wRecordSize;
    rt_uint16_t wSavedNum;                 //保存记录条数
    time_t tLastTime;
}T_HisBackStruct;

/***************************************************************************
 * @brief   互备份历史数据管理文件结构体，记录每台并机电池的历史数据文件名列表、
            对应每个文件的条目数量及每个文件的最后一条记录的保存时间
 **************************************************************************/
typedef struct
{
    rt_uint8_t ucFileName[40];
    rt_uint16_t wSavedNum;
    time_t tLastTime;
}T_HisBackMagStruct;

/***************************************************************************
 * @brief    铁塔历史数据结构体(待统一)
 **************************************************************************/
typedef struct
{
    time_t tStartTime;
    BYTE ucMode;                           //系统模式
    BYTE ucCellEvent;                      //电芯状态事件
    BYTE ucCellVolEvent;                   //单节电压事件
    WORD wTempEvent;                       //温度事件
    BYTE ucCurrentEvent;                   //电流事件
    BYTE ucCapacityEvent;                  //容量事件
    SHORT iBattCurr;                       //电池电流
    WORD wBattVolt;                        //电池电压
    WORD wBattSOC;                         //电池SOC
    WORD awCellTemp[CELL_TEMP_NUM_MAX];    //单体温度
    WORD wEnvTemp;                         //环境温度
    WORD wBoardTemp;                       //功率温度
    WORD awCellVolt[CELL_VOL_NUM_MAX];     //单节电压
    WORD wCheckSum;
}T_HisDataTowerStruct;

/***************************************************************************
 * @brief   历史告警结构体
 **************************************************************************/
typedef struct
{
    rt_uint16_t wAlarmID; // 告警ID号
    time_t tStartTime;    // 告警开始时间
    time_t tEndTime;      // 告警结束时间
} T_HisAlarmSaveStruct;

void initHisData(void);
void SaveResetToHisOper(void);
void processHisData(void *parameter);
void MoveProtoHisDataPoint(rt_uint8_t ucIncNum, record_north_protocol_e wNorthProtocolIndex);
BOOLEAN MoveProtoDcrRecordPoint(rt_uint8_t ucIncNum, record_north_protocol_e wNorthProtocolIndex);
rt_uint16_t GetProtoHisDataNum(record_north_protocol_e wNorthProtocolIndex);
WORD ReadMoreHisData(rt_uint16_t wNum , rt_uint8_t *ptHisData, record_north_protocol_e wNorthProtocolIndex);
void ReadMoreHisData_new(rt_uint16_t wNum , rt_uint8_t *ptHisData, record_north_protocol_e wNorthProtocolIndex);
BOOLEAN ReadMoreDcrRecord_new(rt_uint16_t wNum , rt_uint8_t *ptDcrRecord, record_north_protocol_e wNorthProtocolIndex);
BOOLEAN ReadHisData(T_HisDataStruct *tHisData, record_north_protocol_e wNorthProtocolIndex);
rt_uint16_t GetHisDataTotNum(void);
rt_uint16_t GetDcrRecordTotNum(void);
void DelAllHisData(void);
BOOLEAN DelAllDcrRecord(void);
void SaveHisData(void);
void SaveTempHisData(void);
void SaveAction(WORD wActId, char *str);
WORD GetActionId(rt_uint16_t wActionId);
rt_uint16_t GetCtrlActionId(BYTE ucCtrlCode);
void DelAllHisAction(void);
void ReadMoreHisOperation(rt_uint16_t wNum, rt_uint8_t *tHisOperation, record_north_protocol_e wNorthProtocolIndex);
rt_uint16_t GetProtoHisOperNum(record_north_protocol_e wNorthProtocolIndex);
rt_uint16_t GetProtoHisOperPoint(record_north_protocol_e wNorthProtocolIndex);
void MoveProtoHisOperPoint(rt_uint8_t ucIncNum, record_north_protocol_e wNorthProtocolIndex);

// 按键操作记录接口
void SaveKeyShutDownAction(void);
void SaveKeySleepAction(void);
void SaveKeyUpgradeAction(void);
void SaveKeyDefQueryAction(void);
void SaveKeyUpgradeTimeoutQuitAction(void);
BOOLEAN SaveKeyUpgradeMannualQuitAction(void);

// 历史告警接口
void SaveHisAlarm(T_HisAlarmSaveStruct *ptDisHisAlarm);
rt_uint8_t ReadMoreHisAlarm(rt_uint16_t wNum, rt_uint8_t *tHisAlarm, record_north_protocol_e wNorthProtocolIndex);
void DeleteHisAlarm(void);
rt_uint16_t GetProtoHisAlarmNum(record_north_protocol_e wNorthProtocolIndex);
void MoveProtoHisAlarmPoint(rt_uint8_t ucIncNum, record_north_protocol_e wNorthProtocolIndex);
rt_uint16_t GetProtoHisAlarmPoint(record_north_protocol_e wNorthProtocolIndex);
void ResetProtoHisAlarmPoint(record_north_protocol_e wNorthProtocolIndex);
void ResetProtoHisDataPoint(record_north_protocol_e wNorthProtocolIndex);
void ResetBmsHisOperPoint(record_north_protocol_e wNorthProtocolIndex);
void SaveHisPoint(void);
rt_uint16_t GetProtoHisDataPoint(record_north_protocol_e wNorthProtocolIndex);
void SetProtoHisDataPoint(WORD wHisDataReadPoint, record_north_protocol_e wNorthProtocolIndex);
rt_uint16_t GetProtoDcrRecordPoint(record_north_protocol_e wNorthProtocolIndex);
BOOLEAN SetProtoDcrRecordPoint(WORD wDcrRecordReadPoint, record_north_protocol_e wNorthProtocolIndex);
rt_uint16_t GetHisAlarmTotNum(void);
void SetProtoHisAlarmPoint(WORD wHisAlarmReadPoint, record_north_protocol_e wNorthProtocolIndex);
rt_uint16_t GetHisOperationTotNum(void);
void SetProtoHisActionPoint(WORD wHisActionReadPoint, record_north_protocol_e wNorthProtocolIndex);

// 获取特定时间历史记录相关
void DivideRecordToBlock(T_TimeStruct *ptBlockArray, BYTE *pucBlockIndex, BYTE ucHisRecordType);
SHORT GetHisDataPosition(BYTE *IndexZone, BYTE ucBlockNum, T_TimeStruct tInputTime, BOOLEAN bTrue, BYTE ucHisRecordType);
BYTE GetStartTimePose(T_TimeStruct *atBlock, BYTE ucBlockNum, T_TimeStruct tInputTime, WORD *pwIndex, BYTE ucHisRecordType);
BYTE GetEndTimePose(T_TimeStruct *atBlock, BYTE ucBlockNum, T_TimeStruct tInputTime, WORD *pwIndex, BYTE ucHisRecordType);
BOOLEAN WriteHisBackupData(BYTE *pfilename, BYTE* pstr, WORD wLen);
BOOLEAN WriteHisBackupAlarm(BYTE *pfilename, BYTE* pstr, WORD wLen);
BOOLEAN WriteHisBackupAct(BYTE *pfilename, BYTE* pstr, WORD wLen);
BOOLEAN WriteBduRecordBackup(BYTE *pfilename, BYTE* pstr, WORD wLen);
BOOLEAN WriteAnalyseBackup(BYTE *pfilename, BYTE* pstr, WORD wLen);
BOOLEAN WriteExtremeBackup(BYTE *pfilename, BYTE* pstr, WORD wLen, BYTE ucPackNum);
BOOLEAN AnalyseBackupProcess(BYTE ucNum, BYTE *pfilename, BYTE* pstr, WORD wLen);
BOOLEAN ExtremeBackupProcess(BYTE ucNum, BYTE *pfilename, BYTE* pstr, WORD wLen, BYTE ucPackNum);
void initHisBackupMagFile(void);
BOOLEAN HisBackupDataProcess(BYTE ucNum, BYTE *pfilename, BYTE* pstr, WORD wLen);
BOOLEAN HisBackupAlarmProcess(BYTE ucNum, BYTE *pfilename, BYTE* pstr, WORD wLen);
BOOLEAN HisBackupActProcess(BYTE ucNum, BYTE *pfilename, BYTE* pstr, WORD wLen);
BOOLEAN BduRecBackupProcess(BYTE ucNum, BYTE *pfilename, BYTE* pstr, WORD wLen);
void GetFileListInfo();
void GetFileListInfoSpec(void);
void GetBackupFileList(rt_uint8_t ucNum, T_HisBackMagStruct *ptMagFile);
void GetLocalFileList(rt_uint8_t *pucName);
void CheckAlarmSaved(T_HisAlarmSaveStruct *ptDisHisAlarm);
void WriteShortCutAlarmCheckFile(BOOLEAN bExist);
// 获取历史极值相关
void initExtremeData(void);
BOOLEAN ReadHisExtremeData(T_HisExtremeData *ptHisExtremeData);
void UpdateDataExtreme(void);
void DelAllHisExtreme(void);
char is_short_cut_alarm(T_HisAlarmSaveStruct *ptDisHisAlarm);
BOOLEAN SaveDcrRecord(T_DcrRecordStruct *ptDcrRecord);

SHORT* CopyBCMAlarmStructOld(void);

/* 录波存储接口 */
int StoreBduRecord(void *ptData, int dataLen);

int GetMcuResetReason(void);
BOOLEAN SaveMcuResetReason(int id);

#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // SOFTWARE_SRC_APP_HISDATA_H_
