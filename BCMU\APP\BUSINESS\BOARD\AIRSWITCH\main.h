#ifndef _60X_MAIN_H
#define _60X_MAIN_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

#include "utils_server.h"
#include "server_id.h"

#define SAMPLE_THREAD_STACK_SIZE    1024
#define NORTH_THREAD_STACK_SIZE     1536
#define LED_THREAD_STACK_SIZE       512
#define ALARM_THREAD_STACK_SIZE     1024
#define CONTROL_THREAD_STACK_SIZE   1024

typedef enum {
    RESET_REASON_NONE         = 0x00,     // 无复位标志
    RESET_REASON_POWER_ON     = (1 << 0), // 电源复位
    RESET_REASON_EXTERNAL_PIN = (1 << 1), // 外部引脚复位
    RESET_REASON_SOFTWARE_1   = (1 << 2), // 软件复位
    RESET_REASON_FWDGT        = (1 << 3), // 窗口看门狗复位
    RESET_REASON_WWDGT        = (1 << 4), // 独立看门狗复位
    RESET_REASON_BROWN_OUT    = (1 << 5)  // 欠压复位
} reset_reason_t;

int main(void);
reset_reason_t get_reset_reason(void);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _BHVCDB_MAIN_H


