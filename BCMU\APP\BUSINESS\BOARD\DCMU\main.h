#ifndef _ACMU_MAIN_H
#define _ACMU_MAIN_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

#include "utils_server.h"
#include "server_id.h"

#define THREAD_PRIORITY 15
#define KEY_SCAN_THREAD_PRIORITY 13

#define THREAD_STACK_SIZE_COMM          2048
#define THREAD_STACK_SIZE_SAMPLE        3072
#define SYSTEM_THREAD_STACK_SIZE        4096
#define THREAD_STACK_SIZE_IO_CONTROL    2048
#define THREAD_STACK_SIZE_LOG           4096
#define THREAD_STACK_SIZE_KEY_PROCESS   4096

int main(void);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _ACMU_MAIN_H