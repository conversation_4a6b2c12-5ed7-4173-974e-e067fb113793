#include <stdlib.h>
#include "data_type.h"
#include "self_check.h"
#include "msg.h"
#include "msg_id.h"
#include "utils_server.h"
#include "utils_thread.h"
#include "his_record.h"


static rt_uint32_t last_time = 0;
Static module_msg_t msg_rcv = {.dest = MOD_SYS_MANAGE};

static void process_recv_msg(void);

void process_data_change(const rt_msg_t pMsg){

}

static msg_map system_manage_msg_map[] =
{
{DATA_CHANGE_MSG_ID,   process_data_change,},
};

void *init_sys_manage(void * param)
{
    server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(system_manage_msg_map) / sizeof(msg_map);
    register_server_msg_map(system_manage_msg_map, server_info);
    RETURN_VAL_IF_FAIL(init_msg_queue(MOD_SYS_MANAGE) != NULL, NULL);
    south_self_check();
    return NULL;
}


void system_manage_main(void *param) {
    while (is_running(TRUE)) {
        process_recv_msg();
        //exe_heartbeat(last_time, MOD_SYS_MANAGE);
        rt_thread_delay(1000);
    }
}

static void process_recv_msg(void) {
    
    if (SUCCESSFUL != recv_msg(&msg_rcv))
        return;
    
    switch (msg_rcv.msg_id) {
            case ALARM_CHANGE_MSG:
                alm_change_trigger_save_his_data(&msg_rcv);
                break;
            case TOPO_CALC_FINISH_MSG:
                south_self_check();
                break;
            case ALM_MANAGE_SAVE_HIS_ALM:
                change_alm_and_save(msg_rcv.data);
                break;
            default:
                break;
    }  
    /*double free 治理
    if (msg_rcv.data != NULL) {
        free(msg_rcv.data);
        msg_rcv.data = NULL;
    }
    */
}