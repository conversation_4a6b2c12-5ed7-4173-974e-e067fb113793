#ifndef _SEUB_MAIN_NORTH_COMM_H_
#define _SEUB_MAIN_NORTH_COMM_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include "cmd.h"
#include "linklayer.h"
#include "protocol_layer.h"
#include "pb_encode.h"
#include "pb.h"

typedef struct
{
    void *data;
    cmd_buf_t *cmd_buff;
    link_inst_t *link_inst;
    rt_mq_t north_mq;
} north_mgr_t;

void *init_north(void *param);
void north_comm_th(void *param);
north_mgr_t *init_thread_data(link_inst_t *link_inst, unsigned char mod_id);
unsigned char set_apptest_usart_test_flag(unsigned char flag_set);

#ifdef __cplusplus
}
#endif //< __cplusplus

#endif //< _SEUB_MAIN_NORTH_COMM_H_
