#ifndef _MQTTT_UPFILE_H_
#define _MQTTT_UPFILE_H_

#ifdef __cplusplus
extern "C" {
#endif    //  __cplusplus

#include "mqttclient.h"
#include "partition_def.h"

#define UP_FILE_KEEP_ALIVE_INTERVAL (15*60)    // mqtt文件上传的时候修改保活时间为15min

typedef struct
{
    unsigned char file_type;
    char*         file_name[15];
    unsigned char num;
}file_name_map_t;

typedef enum
{
    HIS_EVENT_FILE_TYPE = 20,
    HIS_DATA_FILE_TYPE,
    IV_DATA_TYPE,
    RECORD_FAULT_FILE_TYPE,
    HIS_ALARM_FILE_TYPE,
    POWER_FILE_TYPE,
    ALM_PARA_FILE_TYPE,
    NUM_PARA_FILE_TYPE,
    STR_PARA_FILE_TYPE,
    LOG_FILE_TYPE,
    AFCI_FILE_TYPE,
} up_file_type_e;

int pack_up_file(int which_tls, char* url , int file_type);
int handle_up_file(void* client, message_data_t* msg);
int is_file_exit(file_name_map_t* file_name_map , const char* file_name[]);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif // _MQTTT_UPFILE_H_
