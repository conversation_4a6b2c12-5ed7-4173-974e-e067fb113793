/**************************************************************************
* Copyright (C) 2010-2011, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：ZXDU18 CTL板软件
* 文件名称：Candrive.h
* 文件说明：CAN通讯底层驱动模块
* 作    者  ：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/
#ifndef __CAN_H 
#define __CAN_H

#ifdef __cplusplus
extern "C" {
#endif

#include "common.h"

#define MAX_PORTS   2       /* Number of CAN port on the chip */        

/* Acceptance filter mode in AFMR register */
#define ACCF_OFF                0x01
#define ACCF_BYPASS             0x02
#define ACCF_ON                 0x00
#define ACCF_FULLCAN            0x04

/* This number applies to all FULLCAN IDs, explicit STD IDs, group STD IDs, 
explicit EXT IDs, and group EXT IDs. */ 
#define ACCF_IDEN_NUM           4

/* Identifiers for FULLCAN, EXP STD, GRP STD, EXP EXT, GRP EXT */
#define FULLCAN_ID              0x100
#define EXP_STD_ID              0x100
#define GRP_STD_ID              0x200
#define EXP_EXT_ID              0x100000
#define GRP_EXT_ID              0x200000

// 定义CAN发送标识
#define     CAN_SENDFLAG_START      0   // 启动发送
#define     CAN_SENDFLAG_CONTINUE   1   // 继续发送，用于发送中断处理

/* Buffer size required for ring buffer of receive and send */  
#define CANDRIVE_BUF_LEN        150          // 发送接收缓冲区大小
#define CANDRIVE_TRANS_BUF_LEN  13          // 需与SJA_DATA_BUF相等
#define CYCLE_COUNTER_MAX       200         // 防止循环无法跳出用

#define CAN_FILTER_REG_NUM      6
#define CAN_FRAME_MAX_DATA_LEN  8
#define CAN_FLITER_NUM          6


/********************  全局变量  **********************/
extern BOOLEAN  g_bCANCommFlag;    // CAN通讯标志:=True 正常通讯；=False  通讯异常，不能发送数据


/********************  数据结构  **********************/
typedef struct
{
    UINT32  Frame;  // Bits 16..19: DLC - Data Length Counter
                    // Bit 30: Set if this is a RTR message
                    // Bit 31: Set if this is a 29-bit ID message
    UINT32  MsgID;  // CAN Message ID (11-bit or 29-bit)
    UINT32  DataA;  // CAN Message Data Bytes 0-3
    UINT32  DataB;  // CAN Message Data Bytes 4-7
}T_Can_Frame_Raw;

typedef struct
{  
    BYTE    ucCan_tx_buf[CANDRIVE_TRANS_BUF_LEN];   /* temp buffer for can bus transfer. */  
    BYTE    ucCan_rx_buf[CANDRIVE_TRANS_BUF_LEN];   /* temp buffer for can bus transfer. */  
    
    T_Can_Frame_Raw tRxb[CANDRIVE_BUF_LEN]; /* ring buffer for receive. */  
    T_Can_Frame_Raw tTxb[CANDRIVE_BUF_LEN]; /* ring buffer for send. */  
    
    BYTE    ucTxbin;           /* pos of in for ring buffer of send. */  
    BYTE    ucTxbout;          /* pos of out for ring buffer of send. */  
    BYTE    ucRxbin;           /* pos of in for ring buffer of receive. */  
    BYTE    ucRxbout;          /* pos of out for ring buffer of receive. */  
    BOOLEAN ucSndNotOver;
    //FLOAT fBit_rate;        /* save bit rate of current set. */  
    //WORD  wCount;           /* count of the device opened. */  
      WORD   slRecTimerSn;   // 接收超时计数器
      WORD   slSendTimerSn;
}T_CanDriveStruct;

extern T_CanDriveStruct  s_tCanDrive;
/*********************  函数原型定义  **********************/
BOOLEAN CanDrive_Write(BYTE *pIn, SHORT len, BYTE *tHeader);
WORD CanDrive_Read(BYTE *buf);
void CAN_IRQHandler(void);
void InitCan(void);
void CanDriveCounterAdd(void);  // yl
BOOLEAN CanDrive_Write_test(BYTE *pIn, SHORT len, BYTE ucType);
void CanDrive_SetSendOver(void);
void CanDrive_SetCommFlag(BOOLEAN flag);

#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  //  __CAN_H;

