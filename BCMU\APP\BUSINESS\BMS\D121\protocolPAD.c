/**************************************************************************
* 文件名称：protocolPAD.c
* 文件说明：PAD北向协议模块文件（协议组包与协议判断等过程隔离）
***************************************************************************/

#include "common.h"
#include "protocol.h"
#include "protocolPAD.h"
#include <rtthread.h>
#include "hisdata.h"
#include "fileSys.h"
#include "sample.h"
#include "para.h"
#include "flash.h"
#include "CommCan.h"
#include "commBdu.h"
#include "HeaterControl.h"
#include "led.h"
#include "SearchHisdata.h"
#include "usart.h"
#include "pdt_version.h"
#include "version_flag.h"
#include "utils_rtthread_security_func.h"
#include "BalanceTimeStatistics.h"

// 内部宏
#define MAX_RECEIVE_FAILED_COUNTER 5       // 获取M4内存地址时counter
#define MAX_BISDIFF_UPGRADE_FLAG_LEN 40    ///< 差分升级标记最大长度

// static var
static T_SysPara s_tSysPara;
static T_BCMAlarmStruct s_tBCMAlm;
static BYTE s_ucBlockNum = 0;         // 记录实际分成了多少块
static T_TimeStruct s_atBlockTime[13]; // 分块数组
static rt_timer_t s_waitPADLedQuickNormalTimer = RT_NULL;
Static rt_timer_t s_ResetTimer = RT_NULL;
static T_HisExtremeData s_tHisExtremeRecordData;  //极值记录
// CID2 function
static void GetAnalog_PAD(BYTE ucPort);                 // 获取模拟量量化后的数据（定点数）
static void GetAlarm_PAD(BYTE ucPort);                  // 获取告警量
static void GetFactoryInfo_PAD(BYTE ucPort);            // 获取设备厂家信息
static void GetPara_PAD(BYTE ucPort);                   // 获取电池参数（定点数）
static void SetPara_PAD(BYTE ucPort);                   // 设定电池参数（定点数）
static void GetSpecialPara_PAD(BYTE ucPort);            // 获取电池特定参数
static void SetSpecialPara_PAD(BYTE ucPort);            // 设置电池特定参数
static void RemoteCtrl_PAD(BYTE ucPort);                // 遥控
static void GetParaNew_PAD(BYTE ucPort);                // 获取中移新增参数
static void SetParaNew_PAD(BYTE ucPort);                // 设置中移新增参数
static void GetHisData_PAD(BYTE ucPort);                // 获取历史数据
static void GetHisAlarm_PAD(BYTE ucPort);               // 获取历史告警
static void GetHisAction_PAD(BYTE ucPort);              // 获取历史操作
static void GetNewAnalog_PAD(BYTE ucPort);              // 获取模拟量量化后的数据（定点数）(D121新增)
static void GetSpeFacInfo_PAD(BYTE ucPort);             // 获取特定设备厂家信息(D121新增)
static void SetSpeFacInfo_PAD(BYTE ucPort);             // 设置特定设备厂家信息(D121新增)
static void GetBattNum_PAD(BYTE ucPort);                // 获取电池数量(D121新增)
static void GetAlarmNew_PAD(BYTE ucPort);               // 获取告警量(D121新增)
static void GetNewPara_PAD(BYTE ucPort);                // 获取系统参数（D121新增）
static void SetNewPara_PAD(BYTE ucPort);                // 设置系统参数（D121新增）
static void RemoteCtrlNew_PAD(BYTE ucPort);             // 遥控(D121新增)
static void GetAlarmLevel_PAD(BYTE ucPort);             // 获取告警级别(D121新增)
static void SetAlarmLevel_PAD(BYTE ucPort);             // 设置告警级别(D121新增)
static void GetAlarmRLY_PAD(BYTE ucPort);               // 获取告警干接点(D121新增)
static void SetAlarmRLY_PAD(BYTE ucPort);               // 设置告警干接点(D121新增)
static void RecSysTime_PAD(BYTE ucPort);                // 设置时间
static void	RecSwapBaudrate_PAD(BYTE ucPort);           // 波特率切换(D121新增)
static void GetBsdiffUpgradeVer_PAD(BYTE ucPort);       // 获取差分升级版本标志
static void GetDcrInfo_PAD(BYTE ucPort);                // 获取直流内阻信息
static void GetDcrRecord_PAD(BYTE ucPort);              // 获取直流内阻记录
static void SendBalanceCapacityRecord(BYTE ucPort);     // 获取均衡容量记录
static void GetBmsStatisticsNew(BYTE ucPort);           // 获取电芯统计记录
static void GetRealBalanceCapacity_PAD(BYTE ucPort);    // 获取均衡容量信息
Static void GetCustomerName(BYTE ucPort);               // 获取客户名称(D121新增)
Static void GetFactoryInfoEx(BYTE ucPort);              // 获取厂家扩展信息
static void GetBmsExtremeRecordValue(BYTE ucPort);                              //获得极值记录
static BYTE FillBattExtremeRecordValue(BYTE* p);                                //获得电池极值记录
static BYTE FillCellVoltMaxExtremeRecordValue(BYTE* p, BYTE ucCellVoltNum);     //填充单体电压最大值极值记录
static BYTE FillCellVoltMinExtremeRecordValue(BYTE* p, BYTE ucCellVoltNum);     //填充单体电压最小值极值记录
static BYTE FillCellTempMaxMinExtremeRecordValue(BYTE* p, BYTE ucCellTempNum);  //填充单体温度最大值最小值极值记录
static void GetElaborateMemoryManagement(BYTE ucPort); //获取内存精细化管理信息

// other function
static void ReadBootVerDate(BYTE *p);
static BOOLEAN CheckHisCommandType(void);
static void ReAddressCompetition_PAD();
static void Unblocking_PAD();
Static BOOLEAN ManualAntitheftUnlock(BYTE bType);
Static BYTE  ManualCtrlEnterDefenceStatus_PAD(void);
#ifdef SOFTBANK_VERSION_BUILD
static void GetRealAlarm_PAD(BYTE ucPort);
static BOOLEAN GetSoftBankAlarmRly(T_BCMAlarmStruct const *ptBcmAlarm, T_SoftBankAlarmStruct *ptSoftBankAlarm);
static BOOLEAN CalAlarmRlyState(T_SoftBankAlarmConfigStruct *ptSoftBankAlarmConfig, T_BCMAlarmStruct const *ptBcmAlarm);
#endif
static BOOLEAN GetAlarmByte1(T_BCMAlarmStruct const *ptBcmAlarm,  BYTE *pucSendChar);
static BOOLEAN GetAlarmByte2(T_BCMAlarmStruct const *ptBcmAlarm,  BYTE *pucSendChar);
static BOOLEAN GetBalanceNum_PAD (WORD wAlarmNum,BYTE ucPort); //解决圈复杂度

// CID2 Function Table
const T_CmdFuncStruct s_atCID2AllPADTable[] = {
    {GET_ANALOG_PAD, GetAnalog_PAD, 0},                             // 获取模拟量量化后的数据（定点数）
    {GET_ALARM_PAD, GetAlarm_PAD, 0},                               // 获取告警量
    {GET_FACTORY_INFO_PAD, GetFactoryInfo_PAD, 0},                  // 获取设备厂家信息
    {GET_PARA_PAD, GetPara_PAD, 0},                                 // 获取系统参数（定点数）
    {SET_PARA_PAD, SetPara_PAD, 6},                                 // 设定系统参数（定点数）
    {GET_SPECIAL_PARA_PAD, GetSpecialPara_PAD, 0},                  // 获取电池特定参数
    {SET_SPECIAL_PARA_PAD, SetSpecialPara_PAD, 4},                  // 设置电池特定参数
    {REMOTE_CONTROL_PAD, RemoteCtrl_PAD, 2},                        // 遥控
    {GET_BATT_NUM_INFO_PAD, GetBattNum_PAD, 0},                     // 获取电池数量
    {GET_CMCC_PARA_PAD, GetParaNew_PAD, 0},                         // 获取中移新增参数
    {SET_CMCC_PARA_PAD, SetParaNew_PAD, 8},                         // 设定中移新增参数
    {GET_HIS_DATA_PAD, GetHisData_PAD, HIS_RECORD_LEN_TYPE_ID},     // 获取历史数据
    {GET_HIS_ALARM_PAD, GetHisAlarm_PAD, HIS_RECORD_LEN_TYPE_ID},   // 获取历史告警
    {GET_HIS_ACTION_PAD, GetHisAction_PAD, HIS_RECORD_LEN_TYPE_ID}, // 获取历史操作
    {GET_NEW_ANALOG_PAD, GetNewAnalog_PAD, 0},                      // 获取模拟量量化后的数据（定点数）(新增D121)
    {GET_SPE_FACT_PAD, GetSpeFacInfo_PAD, 0},                       // 获取特定设备厂家信息(D121新增)
    {SET_SPE_FACT_PAD, SetSpeFacInfo_PAD, 2},                       // 设置特定设备厂家信息(D121新增)
    {GET_NEW_ALARM_PAD, GetAlarmNew_PAD, 0},                        // 获取告警量(D121新增)
    {GET_NEW_PARA_PAD, GetNewPara_PAD, 0},                          // 获取系统参数（D121新增）
    {SET_NEW_PARA_PAD, SetNewPara_PAD, 4},                          // 设置系统参数（D121新增）
    {REMOTE_CONTROL_NEW_PAD, RemoteCtrlNew_PAD, 2},                 // 遥控(D121新增)
    {GET_ALARM_LEVEL_PAD, GetAlarmLevel_PAD, 0},                    // 获取告警级别(D121新增)
    {SET_ALARM_LEVEL_PAD, SetAlarmLevel_PAD, 4},                    // 设置告警级别(D121新增)
    {GET_ALARM_RLY_PAD, GetAlarmRLY_PAD, 0},                        // 获取告警干接点(D121新增)
    {SET_ALARM_RLY_PAD, SetAlarmRLY_PAD, 4},                        // 设置告警干接点(D121新增)
    {GET_TIME_PAD, SendSysTime, 0},                                 // 获取时间(D121新增)
    {SET_TIME_PAD, RecSysTime_PAD, 14},                             // 设置时间(D121新增)
    {REMOTE_SWAP_BAUD_PAD, RecSwapBaudrate_PAD, 6},                 // 波特率切换
    {GET_BSDIFF_UPGRADE_VER_PAD, GetBsdiffUpgradeVer_PAD, 0},       // 获取差分升级版本标志
    {GET_DCR_INFO_PAD, GetDcrInfo_PAD, 0},                          // 获取直流内阻信息
    {GET_DCR_RECORD_PAD, GetDcrRecord_PAD, HIS_RECORD_LEN_TYPE_ID}, // 获取直流内阻记录
    {GET_BMS_BALANCE_CAPACITY,SendBalanceCapacityRecord,HIS_RECORD_LEN_TYPE_ID}, //获取均衡容量记录
    {GET_BMS_STATISTICS_NEW, GetBmsStatisticsNew, 0},               // 获取电芯统计信息
    {GET_BALANCE_CAPACITY,GetRealBalanceCapacity_PAD,0},            // 获取均衡容量信息
    {GET_CUSTOMER_NAME_PAD, GetCustomerName, 0},                    // 获取客户名称
#ifdef SOFTBANK_VERSION_BUILD
    {GET_REAL_ALARM_PAD, GetRealAlarm_PAD, 0},                      // 获取真实告警量(D121 新增)-软银版本支持
#endif
    {GET_BMS_EXTREME_RECORD, GetBmsExtremeRecordValue, 4},          // 获取极值记录
    {GET_FACTORY_INFO_EXTEND, GetFactoryInfoEx, 0},                 // 获取厂家扩展信息
    {GET_ELABORATE_MEMORY_MANAGEMENT, GetElaborateMemoryManagement, 0}, //获取内存精细化管理信息
    {0x00, 0x0000, 0x00},
};

/***************************************************************************
 * @brief    获取模拟量量化后的数据（定点数）
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
static void GetAnalog_PAD(BYTE ucPort)
{
    BYTE *p = NULL;
    T_BCMDataStruct tBCMAnaData;

    rt_memset(&tBCMAnaData, 0, sizeof(T_BCMDataStruct));
    GetRealData(&tBCMAnaData);

    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    *p++ = GetDataFlag(BCM_ALARM, ucPort);

    *p++ = 1; // 铁锂电池组数M

    *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.fCellVoltMax * 100); // 电芯最大电压(V)
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.fCellVoltMin * 100); // 电芯最小电压(V)
    p += 2;

    *(SHORT *)p = BYTEHost2Modbus(&tBCMAnaData.ucCellMaxVoltChan); // 电芯最大电压通道
    p += 2;

    *(SHORT *)p = BYTEHost2Modbus(&tBCMAnaData.ucCellMinVoltChan); // 电芯最小电压通道
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.fCellTempMax); // 电芯最高温度( ℃ )
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.fCellTempMin); // 电芯最低温度( ℃ )
    p += 2;

    *(SHORT *)p = BYTEHost2Modbus(&tBCMAnaData.ucCellMaxTempChan); // 电芯最高温度通道
    p += 2;

    *(SHORT *)p = BYTEHost2Modbus(&tBCMAnaData.ucCellMinTempChan); // 电芯最低温度通道
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.fBusCurr * 100); // 电池组电流( A )：以母排电流上送
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.fExterVolt * 100); // 电池组电压( V ): 以母排电压上送
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.fBoardTemp); // 单板温度( ℃)
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.fEnvTemp); // 环境温度 ( ℃ )
    p += 2;

    *(SHORT *)p = Host2Modbus((SHORT *)&tBCMAnaData.wBattSOC); // 电池组剩余容量(SOC)( % )
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.wBattSOH * 1); // 电池组最大可用容量SOH（%）
    p += 2;

    *(SHORT *)p = Host2Modbus((SHORT *)&tBCMAnaData.wBattCycleTimes); // 循环次数
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.fExterVolt * 100); // 输入电压（V）
    p += 2;

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    return;
}

/***************************************************************************
 * @brief    获取模拟量量化后的数据（定点数）(D121新增)
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
static void GetNewAnalog_PAD(BYTE ucPort)
{
    BYTE *p = NULL;
    BYTE i = 0;
    BYTE ucCellVoltNum = 0;
    BYTE ucCellTempNum = 0;
    T_BCMDataStruct tBCMAnaData;
    T_BattResult tBattout;
    T_HardwareParaStruct tHWPara;

    rt_memset(&tBCMAnaData, 0, sizeof(T_BCMDataStruct));
    rt_memset(&tBattout, 0, sizeof(T_BattResult));

    GetRealData(&tBCMAnaData);
    GetBattResult(&tBattout);
    readBmsHWPara(&tHWPara);
    ucCellVoltNum = tHWPara.ucCellVoltNum < CELL_VOL_NUM_MAX ? tHWPara.ucCellVoltNum : CELL_VOL_NUM_MAX;
    ucCellTempNum = tHWPara.ucCellTempNum < CELL_TEMP_NUM_MAX ? tHWPara.ucCellTempNum : CELL_TEMP_NUM_MAX;

    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    *p++ = GetDataFlag(BCM_ALARM, ucPort);

    *p++ = 1;     // 铁锂电池组数M

    *p++ = ucCellVoltNum; // 单体电压数量
    for (i = 0; i < ucCellVoltNum ; i++)
    {
        *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.afCellVolt[i] * 1000); // 单体电压
        p += 2;
    }
    *p++ = ucCellTempNum; // 单体温度数量
    for (i = 0; i < ucCellTempNum; i++)
    {
        *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.afCellTemp[i] * 10);   // 单体温度
        p += 2;
    }
    *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.fBattChgReqVolt * 100);     // 电池充电请求电压
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.fBattChgReqCurr * 100);     // 电池充电请求电流
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.fBusChgReqCurr * 100);     // Bus侧充电请求电流
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.fBattCurr * 100);            // PACK电流
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(tBattout.fSetChgVolt * 100);            // 当前设定充电电压
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(tBattout.fSetDischVolt * 100);          // 当前设定放电电压
    p += 2;
    *(SHORT *)p = Host2Modbus((SHORT *)&tBattout.wSetChgCurr);                // 当前设定电池侧充电限电流比例
    p += 2;
    *(SHORT *)p = Host2Modbus((SHORT *)&tBattout.wSetBusChgCurr);             // 当前设定Bus侧充电限电流比例
    p += 2;
    *(SHORT *)p = Host2Modbus((SHORT *)&tBattout.wSetDischCurr);              // 当前设定放电限电流比例
    p += 2;
    *(INT32 *)p = FloatChangeToInt32Modbus(tBattout.fTotalDischargeQuanty * 100); // 累计放电电量
    p += 4;
    *(INT32 *)p = FloatChangeToInt32Modbus(tBattout.fTotalDischargeCap * 100);    //累计放电容量
    p += 4;
    *(SHORT *)p = Host2Modbus((SHORT *)&tBattout.wChargeLeftMinutes);         // 充电剩余时间
    p += 2;
    *(SHORT *)p = Host2Modbus((SHORT *)&tBattout.wDisChargeLeftMinutes);      // 放电剩余时间
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.fDischObjVolt * 100);       // 当前放电目标电压
    p += 2;
    *(SHORT *)p = Host2Modbus((SHORT *)&s_tSysPara.wBatteryCap);              // 电池额定容量
    p += 2;
    *p++ = s_tSysPara.ucUsageScen;            //使用场景
    *p++ = tBCMAnaData.ucLimit;               //限流状态
    *p++ = tBattout.ucInputBreak;             //充电输入断
    *(SHORT *)p = Host2Modbus((SHORT *)&tBattout.wCellBalVoltBits);              // 电芯均衡状态
    p += 2;
    *p++ = tBCMAnaData.ucBduSleep;                 //BDU休眠状态
    *p++ = tBCMAnaData.bUpgradeEnable;             //升级使能状态
    *p++ = tBCMAnaData.ucAlarmStatus;              //告警状态
    *p++ = tBCMAnaData.ucBattChargeEn;             // 充电禁止状态
    *p++ = tBCMAnaData.ucBattDischEn;              // 放电禁止状态
    *p++ = tBCMAnaData.ucChgProtSta;               // BDU充电保护告警状态
    *p++ = tBCMAnaData.ucDischgProtSta;            // BDU放电保护告警状态
    *p++ = tBCMAnaData.ucBattPackSta;              // 电池管理模式
    *p++ = tBCMAnaData.ucBduStatus;                // BMS充放电状态
    *p++ = (tBattout.ucMasterStatus + 1) % 2;      // 主从机状态
    *p++ = tBCMAnaData.aucOutputRly[0];            // 输出干接点状态
    *p++ = tBCMAnaData.bHeatpadEnable;             // 加热垫开关状态
    *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.fConnTemp * 100);    // 连接器温度
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(tBattout.fPowerdownVolt*100);    //系统掉电电压阈值
    p += 2;
    *p++ = (GetBduDiagStatus()>>24)&0xFF;     //故障诊断码
    *p++ = (GetBduDiagStatus()>>16)&0xFF;
    *p++ = (GetBduDiagStatus()>>8)&0xFF;
    *p++ = (GetBduDiagStatus())&0xFF;
    *p++ = tBCMAnaData.ucExternalPowerStatus; // 外部有电状态
    *p++ = tBCMAnaData.ucTransChagEn; //转充电允许
    *p++ = tBCMAnaData.bContactorStatus; // MOS关闭状态
    *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.fHeatConnTempMax*100);    //加热膜连接器温度（最高点温度）
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.fBattVolt*100);    //PACK电压
    p += 2;
    *p++ = 2;                                 //用户自定义字节
    *p++ = GetDefenceStatus();  //设备防盗布防状态
    //*p++ = tBCMAnaData.bContatctorStatus; //接触器状态
    *p++ = (rt_uint8_t)tBCMAnaData.wBattLeftEnergy; //剩余能效循环
    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    return;
}

/***************************************************************************
 * @brief    获取直流内阻信息
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
static void GetDcrInfo_PAD(BYTE ucPort)
{
    BYTE *p = NULL;
    BYTE i = 0;
    SHORT sTemp;
    T_BattResult tBattout;
	T_HardwareParaStruct tHWPara;
    BYTE ucCellVoltNum = 0;  
    rt_memset(&tBattout, 0, sizeof(T_BattResult));
    GetBattResult(&tBattout);
    readBmsHWPara(&tHWPara);
    ucCellVoltNum = tHWPara.ucCellVoltNum;

    if(tHWPara.ucCellVoltNum > CELL_VOL_NUM)
    {
        return;
    }

    p  = s_tProtocol.aucSendBuf;
    rt_memset_s(s_tProtocol.aucSendBuf,sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));
    /* DATA_FLAG */
    *p++ = GetDataFlag( BCM_ALARM, ucPort );
    *p++ = 1;//铁锂电池组数M
    *p++ = ucCellVoltNum;//单体数量
    for (i = 0; i < ucCellVoltNum; i++)
    {
        sTemp  = FloatChangeToModbus(tBattout.fCellDcr[i]*100);    //单体DCR
        rt_memcpy_s(p,sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
        p += 2;
    }
    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2); 
    return;
}

/***************************************************************************
 * @brief    获取告警量
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
static void GetAlarm_PAD(BYTE ucPort)
{
    BYTE *p = NULL;
    BYTE j = 0;
    BYTE Alarm = 0;
    T_BCMAlarmStruct tBCMAlm;
    T_SoftBankAlarmStruct tSoftBankAlarm;

    rt_memset(&tBCMAlm, 0, sizeof(T_BCMAlarmStruct));
    rt_memset(&tSoftBankAlarm, 0, sizeof(T_SoftBankAlarmStruct));
    GetRealAlarm(BCM_ALARM, (BYTE *)&tBCMAlm);

#ifdef SOFTBANK_VERSION_BUILD
    GetSoftBankAlarmRly(&tBCMAlm, &tSoftBankAlarm);
#endif

    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    *p++ = GetDataFlag(BCM_ALARM, ucPort);

    *p++ = 1; // 铁锂电池组数M

    *p |= (tBCMAlm.ucBattOverVoltPrt << (0)); // 电池组过压保护

    *p |= (tBCMAlm.ucBattUnderVoltAlm << (1)); // 电池组欠压告警

    *p |= (tBCMAlm.ucBattUnderVoltPrt << (2)); // 电池组欠压保护

    *p |= (0 << (3)); // BMS关闭(预留，暂时不用做)

    *p |= (0 << (4)); // 功率线断开(没有实际使用到)

    *p |= (tBCMAlm.ucBattSOCLowAlm << (5)); // SOC低告警

    *p |= (tBCMAlm.ucBattSOCLowPrt << (6)); // SOC低保护

    *p |= (tBCMAlm.ucBattOverVoltAlm << (7)); // 电池组过压告警

    p++;

    GetAlarmByte1(&tBCMAlm, p);
    p++;

    *p |= (tBCMAlm.ucChgCurrHighAlm << (0)); // 充电过流告警

    *p |= (tBCMAlm.ucChgCurrHighPrt << (1)); // 充电过流保护

    *p |= (tBCMAlm.ucDischgCurrHighAlm << (2)); // 放电过流告警

    *p |= (tBCMAlm.ucDischgCurrHighPrt << (3)); // 放电过流保护

    *p |= (tBCMAlm.ucBattShortCut << (4)); // 短路保护

    *p |= (tBCMAlm.ucCellPoorConsisAlm << (5)); // 电芯一致性差告警

    p++;

    GetAlarmByte2(&tBCMAlm, p);
    p++;

    *p |= (0 << (0)); // 加热(TODO，需增加状态量)

    *p |= (0 << (1)); // 风扇(预留)

    *p |= (tBCMAlm.ucBoardTempHighAlm << (2)); // 单板温度高告警

    *p |= (tBCMAlm.ucBoardTempHighPrt << (3)); // 单板温度高保护

    *p |= ((tBCMAlm.ucEnvTempHighAlm | tSoftBankAlarm.ucEnvTempHighAlm) << (4)); // 环境温度高告警

    *p |= (tBCMAlm.ucEnvTempLowAlm << (5)); // 环境温度低告警

    *p |= ((tBCMAlm.ucHeaterFilmFailure | tSoftBankAlarm.ucHeaterFilmFailure) << (6)); // 加热膜失效,软银版本：加热膜连接器高温保护会映射到该告警

    *p |= (tBCMAlm.ucBDUConnTempHighPrt << (7)); // 连接器温度高保护

    p++;

    *p |= 0 << (0); // 电芯损坏告警(会议决策不做)

    Alarm = 0;
    for (j = 0; j < sizeof(tBCMAlm.aucCellDamagePrt) / sizeof(tBCMAlm.aucCellDamagePrt[0]); j++)
    {
        Alarm |= tBCMAlm.aucCellDamagePrt[j];
    }
    *p |= (Alarm | tSoftBankAlarm.ucCellDamagePrt) << (1); // 电芯损坏保护,软银版本：单体一致性差保护、直流内阻异常保护、单体温度异常、单体温升速率异常、自放电异常会映射到该告警；

    Alarm = 0;
    for (j = 0; j < sizeof(tBCMAlm.ucCellTempSensorInvalidAlm) / sizeof(tBCMAlm.ucCellTempSensorInvalidAlm[0]); j++)
    {
        Alarm |= tBCMAlm.ucCellTempSensorInvalidAlm[j];
    }
    Alarm |= tBCMAlm.ucEnvTempSensorInvalidAlm;
    //软银版本：单体一致性差保护、直流内阻异常保护、单体温度异常、单体温升速率异常、自放电异常会映射到该告警
    *p |= (Alarm | tSoftBankAlarm.ucCellTempSensorInvalidAlm) << (2); // 温度失效告警 单体温度传感器无效与环境温度传感器无效

    *p |= (tBCMAlm.ucCellVoltSampleFault << (3)); // 电压失效告警

    //软银版本：BDU EEPROM故障、BDU电池闭锁、BDU通信断、均衡电路故障、主继电器失效、DCDC故障、采集异常、辅助源故障会映射到该告警；
    *p |= ((tBCMAlm.ucChgLoopInvalid | tBCMAlm.ucCurrLimLoopInvalid | tSoftBankAlarm.ucChgLimitLoopInvalid) << (4)); // 充电/限流回路坏

    *p |= (tBCMAlm.ucDischgLoopInvalid << (5)); // 放电回路坏

    *p |= (tBCMAlm.ucBattSOHPrt << (6)); // SOH异常

    //软银版本：电池反接、回路异常会映射到该告警；
    *p |= ((tBCMAlm.ucFuseError | tSoftBankAlarm.ucFuseError) << (7)); // 熔丝损坏

    p++;

    
    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    return;
}

static BOOLEAN GetAlarmByte1(T_BCMAlarmStruct const *ptBcmAlarm,  BYTE *pucSendChar)
{
    BYTE Alarm = 0;
    BYTE j = 0;
    T_BattResult tBattout;

    if(ptBcmAlarm == NULL || pucSendChar == NULL)
    {
        return False;
    }

    rt_memset(&tBattout, 0, sizeof(T_BattResult));
    GetBattResult(&tBattout);

    for (j = 0; j < sizeof(ptBcmAlarm->aucCellUnderVoltAlm) / sizeof(ptBcmAlarm->aucCellUnderVoltAlm[0]); j++)
    {
        Alarm |= ptBcmAlarm->aucCellUnderVoltAlm[j]; // 单体欠压告警
    }
    *pucSendChar |= Alarm << (0);

    Alarm = 0;
    for (j = 0; j < sizeof(ptBcmAlarm->aucCellUnderVoltPrt) / sizeof(ptBcmAlarm->aucCellUnderVoltPrt[0]); j++)
    {
        Alarm |= ptBcmAlarm->aucCellUnderVoltPrt[j]; // 单体欠压保护
        if(Alarm)
        {
             *pucSendChar &= ~(1 << 0);
             break;
        }
    }
    *pucSendChar |= Alarm << (1);

    Alarm = 0;
    for (j = 0; j < sizeof(ptBcmAlarm->aucCellOverVoltPrt) / sizeof(ptBcmAlarm->aucCellOverVoltPrt[0]); j++)
    {
        Alarm |= ptBcmAlarm->aucCellOverVoltPrt[j]; // 单体过压保护
    }
    *pucSendChar |= Alarm << (2);

    *pucSendChar |= ((tBattout.ucBatStatus == BATT_MODE_STANDBY) ? 1 : 0) << (3); // 满充截止(电池正常充满没有产生保护、告警，0：正常，1：截止)

    Alarm = 0;
    if(*pucSendChar & 0x04)
    {
        *pucSendChar |= 0 << (4);
    }
    else
    {
        for (j = 0; j < sizeof(ptBcmAlarm->aucCellOverVoltAlm) / sizeof(ptBcmAlarm->aucCellOverVoltAlm[0]); j++)
        {
            Alarm |= ptBcmAlarm->aucCellOverVoltAlm[j]; // 单体过压告警
        }
        *pucSendChar |= Alarm << (4);
    }

    return True;
}

static BOOLEAN GetAlarmByte2(T_BCMAlarmStruct const *ptBcmAlarm,  BYTE *pucSendChar)
{
    BYTE Alarm = 0;
    BYTE j = 0;

    if(ptBcmAlarm == NULL || pucSendChar == NULL)
    {
        return False;
    }

    for (j = 0; j < sizeof(ptBcmAlarm->aucChgTempLowAlm) / sizeof(ptBcmAlarm->aucChgTempLowAlm[0]); j++)
    {
        Alarm |= ptBcmAlarm->aucChgTempLowAlm[j]; // 充电低温告警
    }
    *pucSendChar |= Alarm << (0);

    Alarm = 0;
    for (j = 0; j < sizeof(ptBcmAlarm->aucChgTempLowPrt) / sizeof(ptBcmAlarm->aucChgTempLowPrt[0]); j++)
    {
        Alarm |= ptBcmAlarm->aucChgTempLowPrt[j]; // 充电低温保护
        if(Alarm)
        {
             *pucSendChar &= ~(1 << 0);
             break;
        }
    }
    *pucSendChar |= Alarm << (1);

    Alarm = 0;
    for (j = 0; j < sizeof(ptBcmAlarm->aucChgTempHighAlm) / sizeof(ptBcmAlarm->aucChgTempHighAlm[0]); j++)
    {
        Alarm |= ptBcmAlarm->aucChgTempHighAlm[j]; // 充电高温告警
    }
    *pucSendChar |= Alarm << (2);

    Alarm = 0;
    for (j = 0; j < sizeof(ptBcmAlarm->aucChgTempHighPrt) / sizeof(ptBcmAlarm->aucChgTempHighPrt[0]); j++)
    {
        Alarm |= ptBcmAlarm->aucChgTempHighPrt[j]; // 充电高温保护
        if(Alarm)
        {
             *pucSendChar &= ~(1 << 2);
             break;
        }
    }
    *pucSendChar |= Alarm << (3);

    Alarm = 0;
    for (j = 0; j < sizeof(ptBcmAlarm->aucDischgTempLowAlm) / sizeof(ptBcmAlarm->aucDischgTempLowAlm[0]); j++)
    {
        Alarm |= ptBcmAlarm->aucDischgTempLowAlm[j]; // 放电低温告警
    }
    *pucSendChar |= Alarm << (4);

    Alarm = 0;
    for (j = 0; j < sizeof(ptBcmAlarm->aucDischgTempLowPrt) / sizeof(ptBcmAlarm->aucDischgTempLowPrt[0]); j++)
    {
        Alarm |= ptBcmAlarm->aucDischgTempLowPrt[j]; // 放电低温保护
        if(Alarm)
        {
             *pucSendChar &= ~(1 << 4);
             break;
        }
    }
    *pucSendChar |= Alarm << (5);

    Alarm = 0;
    for (j = 0; j < sizeof(ptBcmAlarm->aucDischgTempHighAlm) / sizeof(ptBcmAlarm->aucDischgTempHighAlm[0]); j++)
    {
        Alarm |= ptBcmAlarm->aucDischgTempHighAlm[j]; // 放电高温告警
    }
    *pucSendChar |= Alarm << (6);

    Alarm = 0;
    for (j = 0; j < sizeof(ptBcmAlarm->aucDischgTempHighPrt) / sizeof(ptBcmAlarm->aucDischgTempHighPrt[0]); j++)
    {
        Alarm |= ptBcmAlarm->aucDischgTempHighPrt[j]; // 放电高温保护
        if(Alarm)
        {
             *pucSendChar &= ~(1<<6);
             break;
        }
    }
    *pucSendChar |= Alarm << (7);

    return True;
}

/***************************************************************************
 * @brief    获取告警量(D121新增)
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
static void GetAlarmNew_PAD(BYTE ucPort)
{
    BYTE *p = NULL;
    BYTE j = 0;
    BYTE Alarm = 0;
    T_BCMAlarmStruct tBCMAlm;
    T_BattResult tBattout;

    rt_memset(&tBattout, 0, sizeof(T_BattResult));
    rt_memset(&tBCMAlm, 0, sizeof(T_BCMAlarmStruct));
    GetRealAlarm(BCM_ALARM, (BYTE *)&tBCMAlm);
    GetBattResult(&tBattout);

    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    *p++ = GetDataFlag(BCM_ALARM, ucPort);

    *p++ = 1; // 铁锂电池组数M

    *p |= (tBCMAlm.ucBduEepromAlm << (0)); // BDU EEPROM故障

    *p |= (tBCMAlm.ucBattReverse << (1)); // 电池反接

    *p |= (tBCMAlm.ucBDUBattVoltLowPrt << (2)); // BDU电池欠压保护

    *p |= (tBCMAlm.ucBDUBusVoltLowPrt << (3)); // BDU母排欠压保护

    *p |= (tBCMAlm.ucBDUBusVoltHighPrt << (4)); // BDU母排过压保护

    *p |= (tBCMAlm.ucBDUBattChgVoltLowPrt << (5)); // BDU电池充电欠压保护

    *p |= (tBCMAlm.ucBDUBattLockAlm << (6)); // BDU 电池闭锁

    *p |= (tBCMAlm.ucBDUCommFail << (7)); // BDU通讯断

    p++;

    *p |= (tBCMAlm.ucCellPoorConsisPrt << (0)); // 单体一致性差保护

    *p |= (tBCMAlm.ucBattSOHAlm << (1)); // SOH低告警

    *p |= (tBCMAlm.ucCellTempAbnormal << (2)); // 单体温度异常

    *p |= (tBCMAlm.ucHeatConnTempHighPrt << (3)); // 加热膜连接器高温保护

    *p |= (tBCMAlm.ucLoopFault << (4)); // 回路异常

    *p |= (tBCMAlm.ucEnvTempHighPrt << (5)); // 环境温度高保护

    *p |= (tBCMAlm.ucEnvTempLowPrt << (6)); // 环境温度低保护

    *p |= (tBCMAlm.ucEqualCircuitFaultAlm << (7)); // 均衡电路故障告警

    p++;

    *p |= (tBCMAlm.ucPowerOffTimePrtAlm << (0)); // 停电时间告警

    *p |= (tBCMAlm.ucAddressClash << (1)); // 地址冲突

    *p |= (tBCMAlm.ucBattLoseAlm << (2)); // 电池丢失告警

    *p |= (tBCMAlm.ucInsideTempHighPrt << (3)); // 机内过温保护

    *p |= (tBCMAlm.ucMainRelayFail << (4));   // 主继电器失效

    *p |= (tBCMAlm.ucDCDCErr << (5));         // DCDC故障

    *p |= (tBCMAlm.ucSampleErr << (6));       // 采样异常

    *p |= (tBCMAlm.ucAuxiSourceErr << (7));       // 辅助源故障
    p++;


	Alarm = 0;
    for (j = 0; j < sizeof(tBCMAlm.aucCellDynamicUnderVoltPrt) / sizeof(tBCMAlm.aucCellDynamicUnderVoltPrt[0]); j++)
    {
        Alarm |= tBCMAlm.aucCellDynamicUnderVoltPrt[j]; // 单体动态欠压保护
    }
    *p |= Alarm << (0);
    *p |= tBCMAlm.ucCellTempRiseAbnormal << 1; //单体温升速率异常 
    *p |= tBCMAlm.ucDcrFaultAlm << 2; // 直流内阻异常告警
    *p |= tBCMAlm.ucDcrFaultPrt << 3; // 直流内阻异常保护
    *p |= tBCMAlm.ucSelfDischFualt << 4;//自放电异常告警
    *p |= tBCMAlm.ucPowerCommFail << 5;//电源通信断告警
    *p |= tBCMAlm.ucCapDCPRFaultAlm << 6;//电芯容量一致性差异常告警
    *p |= tBCMAlm.ucBattFaultTempHighAlm << 7;//电池异常高温保护告警
    p++;
    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    return;
}

/***************************************************************************
 * @brief    获取告警级别(D121新增)
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
static void GetAlarmLevel_PAD(BYTE ucPort)
{
    BYTE *p = NULL;
    BYTE i = 0;

    rt_memset(&s_tSysPara, 0, sizeof(T_SysPara));
    GetSysPara(&s_tSysPara);

    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    *p++ = GetDataFlag(BCM_ALARM, ucPort);

    *p++ = 1; // 铁锂电池组数M

    for (i = 0; i < ALARM_CLASS; i++)
    {
        *p++ = s_tSysPara.aucAlarmLevel[i];
    }

    *p++ = 0;      //自定义字节数
    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    return;
}

/***************************************************************************
 * @brief    设定告警级别(D121新增)
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
static void SetAlarmLevel_PAD(BYTE ucPort)
{
    BYTE ucPara;
    BYTE ucIndex = 0;
    rt_memset(&s_tBCMAlm, 0, sizeof(T_BCMAlarmStruct));
    GetSysPara( &s_tSysPara );
    GetRealAlarm(BCM_ALARM, (BYTE *)&s_tBCMAlm);

    s_tProtocol.ucCommandType   = s_tProtocol.aucRecBuf[7];

    ucPara = s_tProtocol.aucRecBuf[8];

    if (s_tProtocol.ucCommandType >= 0x80
        &&s_tProtocol.ucCommandType <= 0xC6)//电池异常高温保护告警
    {
        //已经产生电池丢失告警时禁止屏蔽电池丢失告警
        if((s_tProtocol.ucCommandType == 0xB1) && (s_tBCMAlm.ucBattLoseAlm == FAULT) && (ucPara == IGNORE))
        {
            s_tProtocol.ucRTN  = RTN_INVALID_DATA;
            return;
        }
        ucIndex = s_tProtocol.ucCommandType - 0x80;
        s_tSysPara.aucAlarmLevel[ucIndex] = ucPara;
    }
    else
    {
        s_tProtocol.ucRTN  = RTN_WRONG_COMMAND;
        return;
    }
    if ( !(SetSysPara( &s_tSysPara, True, CHANGE_BY_YD1363 )) )
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
    }

    return;
}

/***************************************************************************
 * @brief    获取告警干接点(D121新增)
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
static void GetAlarmRLY_PAD(BYTE ucPort)
{
    BYTE *p = NULL;
    BYTE i = 0;

    rt_memset(&s_tSysPara, 0, sizeof(T_SysPara));
    GetSysPara(&s_tSysPara);

    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    *p++ = GetDataFlag(BCM_ALARM, ucPort);

    *p++ = 1; // 铁锂电池组数M

    for (i = 0; i < ALARM_CLASS; i++)
    {
        *p++ = s_tSysPara.aucRelayBit[i];
    }

    *p++ = 0;      //自定义字节数
    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    return;
}

/***************************************************************************
 * @brief    设定告警干接点(D121新增)
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
static void SetAlarmRLY_PAD(BYTE ucPort)
{
    BYTE ucPara;
    BYTE ucIndex = 0;

    GetSysPara( &s_tSysPara );

    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];

    ucPara = s_tProtocol.aucRecBuf[8];

    if (s_tProtocol.ucCommandType >= 0x80
        &&s_tProtocol.ucCommandType <= 0xC6 && s_tProtocol.ucCommandType != 0xC4)//电池异常高温保护告警，目前电源通讯断不要求设置告警干接点
    {
        ucIndex = s_tProtocol.ucCommandType -0x80;
        s_tSysPara.aucRelayBit[ucIndex] = ucPara;
    }
    else
    {
        s_tProtocol.ucRTN  = RTN_WRONG_COMMAND;
        return;
    }
    if ( !(SetSysPara( &s_tSysPara, True, CHANGE_BY_YD1363 )) )
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
    }

    return;
}

/***************************************************************************
 * @brief    获取系统参数（定点数）
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
static void GetPara_PAD(BYTE ucPort)
{
    BYTE *p = NULL;

    GetSysPara(&s_tSysPara);

    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fCellOverVoltPrtThre * 100); // 单体过压保护阈值
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fCellOverVoltPrtRecoThre * 100); // 单体过压保护恢复阈值
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fCellUnderVoltPrtThre * 100); // 单体欠压保护阈值
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fCellUnderVoltPrtRecoThre * 100); // 单体欠压保护恢复阈值
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fBattOverVoltPrtThre * 100); // 电池组过压保护阈值
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fBattOverVoltPrtRecoThre * 100); // 电池组过压保护恢复阈值
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fBattUnderVoltPrtThre * 100); // 电池组欠压保护阈值
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fBattUnderVoltPrtRecoThre * 100); // 电池组欠压保护恢复阈值
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fChgTempHighPrtThre); // 充电温度高保护阈值
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fChgTempHighPrtRecoThre); // 充电温度高保护解除阈值
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fChgTempLowPrtThre); // 充电温度低保护阈值
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fChgTempLowPrtRecoThre); // 充电温度低保护解除阈值
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fDischgTempHighPrtThre); // 放电温度高保护阈值
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fDischgTempHighPrtRecoThre); // 放电温度高保护解除阈值
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fDischgTempLowPrtThre); // 放电温度低保护阈值
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fDischgTempLowPrtRecoThre); // 放电温度低保护解除阈值
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fBoardTempHighPrtThre); // 单板过温保护阈值
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fBoardTempHighPrtRecoThre); // 单板过温保护恢复阈值
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fChgHeaterStartupTemp); // 充电加热膜启动温度
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fChgHeaterShutdownTemp); // 充电加热膜关闭温度
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fHeaterTempHighThre); // 加热膜过温阈值
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fHeaterTempHighRel); // 加热膜过温解除
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fBattChgFullAverVoltThre * 100); // 充满电条件截止电压
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fBattChgFullAverCurrThre * s_tSysPara.wBatteryCap * 100); // 充满电条件截止电流
    p += 2;

    *(SHORT *)p = Host2Modbus((SHORT *)&s_tSysPara.wRechargeSOC); // 补充电SOC(%)
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fBattSupplVolt * 100); // 补充电电压(V)
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fCellPoorConsisPrtThre * 100); // 电芯一致性差阈值
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fCellPoorConsisPrtRecoThre * 100); // 电芯一致性差解除
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.wHisDataInter * 60); // 数据保存间隔
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.wBattSOHPrtThre); // SOH保护值
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fCellUnderVoltAlmThre * 100); // 单体欠压告警阈值
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fCellUnderVoltAlmRecoThre * 100); // 单体欠压告警解除阈值
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fBattUnderVoltAlmThre * 100); // 电池组欠压告警阈值
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fBattUnderVoltAlmRecoThre * 100); // 电池组欠压告警解除
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fDischgCurrHighAlmThre * s_tSysPara.wBatteryCap * 100); // 放电过流告警阈值
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fDischgCurrHighAlmRecThre * s_tSysPara.wBatteryCap * 100); // 放电过流告警解除
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fDischgTempLowAlmThre); // 放电温度低告警阈值
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fDischgTempLowAlmRecoThre); // 放电温度低告警解除
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fDischgTempHighAlmThre); // 放电温度高告警阈值
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fDischgTempHighAlmRecoThre); // 放电温度高告警解除
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fEnvTempHighAlmThre); // 环境温度高告警阈值
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fEnvTempHighAlmRecoThre); // 环境温度高告警解除
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fEnvTempLowAlmThre); // 环境温度低告警阈值
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fEnvTempLowAlmRecoThre); // 环境温度低告警解除
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fChargeMaxCurr * s_tSysPara.wBatteryCap * 100); // 电池充电限流值
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fCellSupplVolt * 100); // 单体补充电电压(V)
    p += 2;

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    return;
}

static BYTE SetPara_PAD1(FLOAT fPara) {
    switch (s_tProtocol.ucCommandType){
        case 0x80:
            s_tSysPara.fCellOverVoltPrtThre = fPara / 100.0; // 单体过压告警阈值
            break;
        case 0x81:
            s_tSysPara.fCellOverVoltPrtRecoThre = fPara / 100.0; // 单体过压保护恢复阈值
            break;
        case 0x82:
            s_tSysPara.fCellUnderVoltPrtThre = fPara / 100.0; // 单体欠压保护阈值
            break;
        case 0x83:
            s_tSysPara.fCellUnderVoltPrtRecoThre = fPara / 100.0; // 单体欠压保护恢复阈值
            break;
        case 0x84:
            s_tSysPara.fBattOverVoltPrtThre = fPara / 100.0; //  电池组过压保护阈值
            break;
        case 0x85:
            s_tSysPara.fBattOverVoltPrtRecoThre = fPara / 100.0; // 电池组过压保护恢复阈值
            break;
        case 0x86:
            s_tSysPara.fBattUnderVoltPrtThre = fPara / 100.0; // 电池组欠压保护阈值
            break;
        case 0x87:
            s_tSysPara.fBattUnderVoltPrtRecoThre = fPara / 100.0; // 电池组欠压保护恢复阈值
            break;
        case 0x88:
            s_tSysPara.fChgTempHighPrtThre = fPara; // 充电温度高保护阈值
            break;
        case 0x89:
            s_tSysPara.fChgTempHighPrtRecoThre = fPara; // 充电温度高保护解除阈值
            break;
        case 0x8A:
            s_tSysPara.fChgTempLowPrtThre = fPara; // 充电低温保护阈值(℃)
            break;
        case 0x8B:
            s_tSysPara.fChgTempLowPrtRecoThre = fPara; // 充电温度低保护解除阈值
            break;
        case 0x8C:
            s_tSysPara.fDischgTempHighPrtThre = fPara; // 放电高温保护阈值(℃)
            break;
        case 0x8D:
            s_tSysPara.fDischgTempHighPrtRecoThre = fPara; // 放电温度高保护解除阈值
            break;
        case 0x8E:
            s_tSysPara.fDischgTempLowPrtThre = fPara; // 放电温度低保护阈值
            break;
        case 0x8F:
            s_tSysPara.fDischgTempLowPrtRecoThre = fPara; // 放电温度低保护解除阈值
            break;
        default:
        return False;
    }
    return True;
}

static BYTE SetPara_PAD2(FLOAT fPara) {
    switch (s_tProtocol.ucCommandType){
        case 0x90:
            s_tSysPara.fBoardTempHighPrtThre = fPara; // 单板温度高保护阈值
            break;
        case 0x91:
            s_tSysPara.fBoardTempHighPrtRecoThre = fPara; // 单板温度高保护解除阈值
            break;
        case 0x92:
            s_tSysPara.fChgHeaterStartupTemp = fPara; // 充电加热膜启动温度
            break;
        case 0x93:
            s_tSysPara.fChgHeaterShutdownTemp = fPara; // 充电加热膜关闭温度
            break;
        case 0x94:
            s_tSysPara.fHeaterTempHighThre = fPara; // 加热膜过温阈值
            break;
        case 0x95:
            s_tSysPara.fHeaterTempHighRel = fPara; // 加热膜过温解除
            break;
        case 0x96:
            s_tSysPara.fBattChgFullAverVoltThre = fPara / 100.0; // 充满电条件截止电压
            break;
        case 0x97:
            s_tSysPara.fBattChgFullAverCurrThre = fPara / s_tSysPara.wBatteryCap / 100.0; // 充满电条件截止电流
            break;
        case 0x98:
            s_tSysPara.wRechargeSOC = (WORD)fPara; // 补充电SOC(%)
            break;
        case 0x99:
            s_tSysPara.fBattSupplVolt = fPara / 100.0; // 补充电电压(V)
            break;
        case 0x9A:
            s_tSysPara.fCellPoorConsisPrtThre = fPara / 100.0; // 电芯一致性差阈值
            break;
        case 0x9B:
            s_tSysPara.fCellPoorConsisPrtRecoThre = fPara / 100.0; // 电芯一致性差解除
            break;
        case 0x9C:
            s_tSysPara.wHisDataInter = (fPara + 59) / 60; // 数据保存间隔(向上取整)
            break;
        case 0x9D:
            s_tSysPara.wBattSOHPrtThre = fPara; // SOH保护值
            break;
        case 0x9E:
            s_tSysPara.fCellUnderVoltAlmThre = fPara / 100.0; // 单体欠压告警阈值
            break;
        case 0x9F:
            s_tSysPara.fCellUnderVoltAlmRecoThre = fPara / 100.0; // 单体欠压告警解除阈值
            break;
        default:
        return False;
    }
    return True;
}

static BYTE SetPara_PAD3(FLOAT fPara) {
    switch (s_tProtocol.ucCommandType){
        case 0xA0:
            s_tSysPara.fBattUnderVoltAlmThre = fPara / 100.0; // 电池组欠压告警阈值
            break;
        case 0xA1:
            s_tSysPara.fBattUnderVoltAlmRecoThre = fPara / 100.0; // 电池组欠压告警解除
            break;
        case 0xA2:
            s_tSysPara.fDischgCurrHighAlmThre = fPara / 100.0 / s_tSysPara.wBatteryCap; // 放电过流告警阈值
            break;
        case 0xA3:
            s_tSysPara.fDischgCurrHighAlmRecThre = fPara / 100.0 / s_tSysPara.wBatteryCap; // 放电过流告警解除
            break;
        case 0xA4:
            s_tSysPara.fDischgTempLowAlmThre = fPara; // 放电温度低告警阈值
            break;
        case 0xA5:
            s_tSysPara.fDischgTempLowAlmRecoThre = fPara; //  放电温度低告警解除
            break;
        case 0xA6:
            s_tSysPara.fDischgTempHighAlmThre = fPara; // 放电温度高告警阈值
            break;
        case 0xA7:
            s_tSysPara.fDischgTempHighAlmRecoThre = fPara; // 放电温度高告警解除
            break;
        case 0xA8:
            s_tSysPara.fEnvTempHighAlmThre = fPara; // 环境温度高告警阈值
            break;
        case 0xA9:
            s_tSysPara.fEnvTempHighAlmRecoThre = fPara; // 环境温度高告警解除
            break;
        case 0xAA:
            s_tSysPara.fEnvTempLowAlmThre = fPara; //  环境温度低告警阈值
            break;
        case 0xAB:
            s_tSysPara.fEnvTempLowAlmRecoThre = fPara; //  环境温度低告警解除
            break;
        case 0xAC:
            s_tSysPara.fChargeMaxCurr = fPara / 100.0 / s_tSysPara.wBatteryCap; // 设置电池充电限流值
            break;
        case 0xAD:
            s_tSysPara.fCellSupplVolt = fPara / 100.0; // 设置单体补充电电压
            break;
        default:
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
            return False;
    }
    return True;
}

/***************************************************************************
 * @brief    设定系统参数（定点数）
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
static void SetPara_PAD(BYTE ucPort)
{
    FLOAT fPara = 0.0;
    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];

    GetSysPara(&s_tSysPara);

    // command type范围检查
    if ((s_tProtocol.ucCommandType >= 0x80 && s_tProtocol.ucCommandType <= 0xAD))
    {
        // 此命令中的DATAI字节长度一致，LENID在进入函数前统一检查
    }
    else
    {
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
        return;
    }

    fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8)));

    if (SetPara_PAD1(fPara) == False) {
        if (SetPara_PAD2(fPara) == False) {
            if(SetPara_PAD3(fPara) == False) {
                return;
            }
        }
    }

    if (False == SetSysPara(&s_tSysPara, True, CHANGE_BY_YD1363))
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
    }

    return;
}

/***************************************************************************
 * @brief    获取系统参数（定点数）(D121新增)
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/

static void GetNewPara_PAD(BYTE ucPort)
{
    BYTE *p = NULL, i = 0;

    GetSysPara(&s_tSysPara);

    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

    *p++ = s_tSysPara.ucCurrBalanceAmplitude; // 均流SOC补偿幅值
    *p++ = s_tSysPara.ucCurrBalanceSlope; // 均流SOC补偿斜率
    *p++ = s_tSysPara.bCurrBalanceSOCEn; // 均流SOC补偿使能

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fCellOverVoltAlmThre * 100);     // 单体过压告警阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fCellOverVoltAlmRecoThre * 100); // 单体过压告警恢复阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fBoardTempHighcThre);       // 单板过温告警阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fBoardTempHighAlmRecoThre); // 单体过温告警恢复阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fChgCurrHighAlmThre * s_tSysPara.wBatteryCap * 100);      // 充电过流告警阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fChgCurrHighPrtThre * s_tSysPara.wBatteryCap * 100);      // 充电过流保护阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fCellPoorConsisAlmThre * 100);              // 电芯一致性差告警阈值
    p += 2;
    *(SHORT *)p  = Host2Modbus((SHORT*)&s_tSysPara.wBattSOHAlmThre);               // SOH告警阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fCellDamagePrtThre * 100);        // 单体损坏保护阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fCellEquVoltDiffThre * 100);     // 单体均衡启动压差
    p += 2;
    *(SHORT *)p = Host2Modbus((SHORT*)&s_tSysPara.wChgMaxDura);              // 充电最长时间
    p += 2;
    *(SHORT *)p = Host2Modbus((SHORT*)&s_tSysPara.wChgEndDura);              // 充电末期维持时间
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fCellChargeFullVolt*100);   // 单板充电截至电压
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fDischgMaxCurr * s_tSysPara.wBatteryCap * 100);        // 放电最大电流
    p += 2;
    *p++ = s_tSysPara.ucCellUVPDelay;                                        // 单体动态欠压保护
    *p++ = s_tSysPara.bUVPTempCompensationEn;                                // 整组欠压保护温度补偿
    *p++ = s_tSysPara.ucUsageScen;                                           // 使用场景
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fRemoteSupplyOutVolt * 100);// 远供输出电压
    p += 2;
    *(SHORT *)p = Host2Modbus((SHORT*)&s_tSysPara.wDischgSwitchSOC);         // 放电切换SOC
    p += 2;
    *p++ = s_tSysPara.ucDODPerDischarge;                                     // 单次放电DOD
    MemsetBuff(p, s_tSysPara.acBMSSysName, (BYTE)rt_strnlen_s((CHAR*)s_tSysPara.acBMSSysName, 20), (BYTE)20, 0x20);   //BMS系统名称
    p += 20;
    *p++ = s_tSysPara.ucGyroAngle;                                           // 陀螺仪倾角
    *(SHORT *)p = Host2Modbus((SHORT*)&s_tSysPara.wHisDataInter);            // 历史记录保存间隔时间
    p += 2;
    *(SHORT *)p = Host2Modbus((SHORT*)&s_tSysPara.wSoftAntiTheftDelay);      // 软件防盗延时
    p += 2;
    rt_memcpy(p, (BYTE*)s_tSysPara.acDeviceName, 32);                        // 设备名称
    p += 32;
    PutInt16ToBuff(p, (SHORT)s_tSysPara.tEnableTime.wYear);                  // 启用日期
    p += 2;
    *p++ = s_tSysPara.tEnableTime.ucMonth;
    *p++ = s_tSysPara.tEnableTime.ucDay;
    *p++ = s_tSysPara.ucGyroAntiTheftMode;                                   // 陀螺仪防盗方式
    *p++ = s_tSysPara.ucBattUnlockMode;                                      // 电池解锁方式
    *p++ = s_tSysPara.ucSleepIndicator;                                      // 休眠指示灯
    *p++ = s_tSysPara.ucBattAddressMode;      // 电池地址获取方式
    *p++ = s_tSysPara.ucBattSwitchAddr;       // 电池切换地址
    *p++ = s_tSysPara.ucRelayDefaultStatus; // 干接点默认状态
    *p++ = s_tSysPara.bChargeRotate;     // 充电轮换使能
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fDischargeEndVolt1 * 100); // 末期放电电压1(V)
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fDischargeEndVolt2 * 100); // 末期放电电压2(V)
    p += 2;
    *p++ = s_tSysPara.ucCellTempPrtShield;                                  // 单体温度保护屏蔽
    *p++ = s_tSysPara.ucChargeMachineTestMode;                              // 充放电机测试模式
    *(SHORT *)p = Host2Modbus((SHORT*)&s_tSysPara.wPreRecordInterval);      //故障前录波时间间隔（us）
    p += 2;
    *(SHORT *)p = Host2Modbus((SHORT*)&s_tSysPara.wPostRecordInterval);     //故障后录波时间间隔（us）
    p += 2;
    *p++ = s_tSysPara.ucPreRecordNum;//故障前录波条数
    *p++ = s_tSysPara.ucPostRecordNum;//故障后录波条数
    *p++ = s_tSysPara.ucMeasurePointsNum;//测点数量X
    for(i = 0; i < s_tSysPara.ucMeasurePointsNum; i++)
    {
        *p++ = s_tSysPara.ucMeasurePointsID[i];//可变测点ID
    }
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fDischgCurrHighPrtThre * s_tSysPara.wBatteryCap * 100);   // 放电过流保护阈值
    p += 2;

    *p++ = 19;   // 用户自定义字节数
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fCellTempRiseAbnormalThre * 10);   // 单体温升速率异常阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fSelfDischgACR * 10);   // 自放电容量比率
    p += 2;
    *p++ = s_tSysPara.ucDcrFaultAlmThre;// 直流内阻异常告警阈值
    *p++ = s_tSysPara.ucDcrFaultPrtThre;// 直流内阻异常保护阈值
    *p++ = s_tSysPara.fSelfDevelopProtocolOffsetAddr;// 自研协议偏移地址
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fCapDCPRFaultAlmThre * 10); // 容量衰减一致性差告警比率阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fBattFaultTempHighPrtThre); // 电池异常温度高保护阈值(℃)
    p += 2;
    *p++ = s_tSysPara.bChargeMapEnable;// 充电MAP使能
    *p++ = s_tSysPara.ucCriticalAlarmlight;
    *p++ = s_tSysPara.ucSecondaryAlarmlight;
    *p++ = s_tSysPara.ucCellType;// 电芯类型
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fDischgHeaterStartupTemp); // 放电加热膜启动温度(℃)
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fDischgHeaterShutdownTemp); // 放电加热膜关闭温度(℃)
    p += 2;
    *p++ = s_tSysPara.ucNTCInvalidShieldNum; // NTC无效屏蔽路数
    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    return;
}


static BOOLEAN SetNewPara_Area1(BYTE ucCommandType)
{
    FLOAT fPara = 0.0;
    WORD wTemp = 0;
    BOOLEAN bSetState = TRUE;

    switch (ucCommandType)
    {
    case 0x80:
        s_tSysPara.ucCurrBalanceAmplitude = s_tProtocol.aucRecBuf[8]; // 均流SOC补偿幅值
        break;
    case 0x81:
        s_tSysPara.ucCurrBalanceSlope = s_tProtocol.aucRecBuf[8]; // 均流SOC补偿斜率
        break;
    case 0x82:
        s_tSysPara.bCurrBalanceSOCEn = s_tProtocol.aucRecBuf[8]; // 均流SOC补偿使能
        break;
    case 0x83:
        fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8)));
        s_tSysPara.fCellOverVoltAlmThre = fPara / 100.0;                 // 单体过压告警阈值
        break;
    case 0x84:
        fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8)));
        s_tSysPara.fCellOverVoltAlmRecoThre = fPara / 100.0;             // 单体过压告警恢复阈值
        break;
    case 0x85:
        fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8)));
        s_tSysPara.fBoardTempHighcThre = fPara;                  // 单板过温告警阈值
        break;
    case 0x86:
        fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8)));
        s_tSysPara.fBoardTempHighAlmRecoThre = fPara;             // 单板过温告警恢复阈值
        break;
    case 0x87:
        fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8)));
        s_tSysPara.fDischgCurrHighPrtThre = fPara / s_tSysPara.wBatteryCap / 100.0;      // 放电过流保护阈值
        break;
    case 0x88:
        fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8)));
        s_tSysPara.fChgCurrHighAlmThre = fPara / s_tSysPara.wBatteryCap / 100.0;      // 充电过流告警阈值
        break;
    case 0x89:
        fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8)));
        s_tSysPara.fChgCurrHighPrtThre = fPara / s_tSysPara.wBatteryCap / 100.0;      //  充电过流保护阈值
        break;
    case 0x8A:
        fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8)));
        s_tSysPara.fCellPoorConsisAlmThre = fPara / 100.0;    // 单体一致性差告警阈值
        break;
    case 0x8B:
        wTemp = Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8));
        s_tSysPara.wBattSOHAlmThre = wTemp;                  // SOH告警阈值
        break;
    case 0x8C:
        fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8)));
        s_tSysPara.fCellDamagePrtThre = fPara / 100.0;       // 单体损坏保护阈值
        break;
    default:
       bSetState = FALSE;
    break;
    }
    return bSetState;
}

static BOOLEAN SetNewPara_Area2(BYTE ucCommandType)
{
    FLOAT fPara = 0.0;
    WORD wTemp = 0;
    BOOLEAN bSetState = TRUE;

    switch (ucCommandType)
    {
    case 0x8D:
        fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8)));
        s_tSysPara.fCellEquVoltDiffThre = fPara / 100.0;     // 单体均衡启动压差
        break;
    case 0x8E:
        wTemp = Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8));
        s_tSysPara.wChgMaxDura = wTemp;                     // 充电最长时间
        break;
    case 0x8F:
        wTemp = Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8));
        s_tSysPara.wChgEndDura = wTemp;                     // 充电末期维持时间
        break;
    case 0x91:
        fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8)));
        s_tSysPara.fCellChargeFullVolt = fPara / 100.0;     // 单板充电截至电压
        break;
    case 0x92:
        fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8)));
        s_tSysPara.fDischgMaxCurr = fPara / s_tSysPara.wBatteryCap / 100.0;       // 放电最大电流
        break;
    case 0x93:
        s_tSysPara.ucCellUVPDelay = s_tProtocol.aucRecBuf[8];           // 单体动态欠压保护
        break;
    case 0x94:
        s_tSysPara.bUVPTempCompensationEn = s_tProtocol.aucRecBuf[8];   // 整组欠压保护温度补偿
        break;
    case 0x95:
        s_tSysPara.ucUsageScen = s_tProtocol.aucRecBuf[8];              // 使用场景
        break;
    case 0x96:
        fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8)));
        s_tSysPara.fRemoteSupplyOutVolt = fPara /100.0;                 // 远供输出电压
        break;
    case 0x97:
        wTemp = Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8));
        s_tSysPara.wDischgSwitchSOC = wTemp;                            // 放电切换SOC
        break;
    case 0x98:
        s_tSysPara.ucDODPerDischarge = s_tProtocol.aucRecBuf[8];        // 单次放电DOD
        break;
    case 0x99:
        // rt_memcpy((BYTE *)&(s_tSysPara.acBMSSysName[0]), (BYTE *)(&s_tProtocol.aucRecBuf[8]), sizeof(s_tSysPara.acBMSSysName) - 1); //BMS系统名称（PAD协议不支持设置）
        bSetState = FALSE;
        break;
    case 0x9A:
        s_tSysPara.ucGyroAngle = s_tProtocol.aucRecBuf[8];              // 陀螺仪倾角
        break;
    case 0x9B:
        wTemp = Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8));
        s_tSysPara.wHisDataInter = wTemp;                               // 历史记录保存间隔时间
        break;
    default:
       bSetState = FALSE;
    break;
    }
    return bSetState;
}

static BOOLEAN SetNewPara_Area3(BYTE ucCommandType)
{
    FLOAT fPara = 0.0;
    WORD wTemp = 0;
    BOOLEAN bSetState = TRUE;
    T_BCMAlarmStruct    tBCMAlm;

    rt_memset(&tBCMAlm, 0, sizeof(T_BCMAlarmStruct)); 
    GetRealAlarm(BCM_ALARM, (BYTE *)&tBCMAlm);

    switch (ucCommandType)
    {
     case 0x9C:
        wTemp = Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8));
        s_tSysPara.wSoftAntiTheftDelay = wTemp;                         // 软件防盗延时
        break;
    case 0x9D:
        rt_memcpy((BYTE *)&(s_tSysPara.acDeviceName[0]), (BYTE *)(&s_tProtocol.aucRecBuf[8]), sizeof(s_tSysPara.acDeviceName) - 1); //设备名称
        break;
    case 0x9E:
        wTemp = Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf + 8));    //启用时间
        s_tSysPara.tEnableTime.wYear = (WORD)wTemp;
        s_tSysPara.tEnableTime.ucMonth = s_tProtocol.aucRecBuf[10];
        s_tSysPara.tEnableTime.ucDay = s_tProtocol.aucRecBuf[11];
        break;
    case 0x9F:
        s_tSysPara.ucGyroAntiTheftMode = s_tProtocol.aucRecBuf[8];   // 陀螺仪防盗方式
        break;
    case 0xA0:
        if(tBCMAlm.ucBattLoseAlm && s_tSysPara.ucBattUnlockMode)//模式为人工解锁，且有电池丢失告警时，切换解锁方式返回错误
        {
            s_tProtocol.ucRTN   = RTN_INVALID_DATA; 
            return True;
        }
        s_tSysPara.ucBattUnlockMode = s_tProtocol.aucRecBuf[8];      // 电池解锁方式
        break;
    case 0xA1:
        s_tSysPara.ucSleepIndicator = s_tProtocol.aucRecBuf[8];      // 休眠指示灯
        break;
    case 0xA2: // 电池地址获取方式
        s_tSysPara.ucBattAddressMode = s_tProtocol.aucRecBuf[8];
        break;
    case 0xA3: // 电池切换地址参数
        s_tSysPara.ucBattSwitchAddr = s_tProtocol.aucRecBuf[8];
        break;
    case 0xA4:
        s_tSysPara.ucRelayDefaultStatus = s_tProtocol.aucRecBuf[8]; // 干接点默认状态
        break;
    case 0xA5:
        s_tSysPara.bChargeRotate = s_tProtocol.aucRecBuf[8]; // 充电轮换使能
        break;
    case 0xA6:
        fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8)));
        s_tSysPara.fDischargeEndVolt1 = fPara /100.0;   //末期放电电压1
        break;
    case 0xA7:
        fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8)));
        s_tSysPara.fDischargeEndVolt2 = fPara /100.0;   //末期放电电压2
        break;
    default:
       bSetState = FALSE;
    break;
    }
    return bSetState;
}

static BOOLEAN SetNewPara_Area4(BYTE ucCommandType)
{
    BOOLEAN bSetState = TRUE;
    BYTE i = 0;
    FLOAT fPara = 0.0;
    switch (ucCommandType)
    {
    case 0xA8:
        s_tSysPara.ucCellTempPrtShield = s_tProtocol.aucRecBuf[8];   //单体温度保护屏蔽（没有实际意义）
        break;
    case 0xA9:
        if (s_tProtocol.aucRecBuf[8] == 0 || s_tProtocol.aucRecBuf[8] == 1)
            {s_tSysPara.ucChargeMachineTestMode = s_tProtocol.aucRecBuf[8];   // 充放电机测试模式
            BduCtrl(SCI_CTRL_CHG_MANCHIN_TEST, s_tSysPara.ucChargeMachineTestMode);}
        break;
    case 0xAA:
        s_tSysPara.wPreRecordInterval = Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8)); // 故障前录波时间间隔(us)
        break;
    case 0xAB:
        s_tSysPara.wPostRecordInterval = Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8)); // 故障后录波时间间隔(us)
        break;
    case 0xAC:
        s_tSysPara.ucPreRecordNum = s_tProtocol.aucRecBuf[8];  // 故障前录波条数
        break;
    case 0xAD:
        s_tSysPara.ucPostRecordNum = s_tProtocol.aucRecBuf[8];  // 故障后录波条数
        break;
    case 0xAE:
        s_tSysPara.ucMeasurePointsNum = s_tProtocol.aucRecBuf[8];  // 测点数量X
        break;
    case 0xAF:
        if ((s_tProtocol.wRecLenid / 2 - 1) != s_tSysPara.ucMeasurePointsNum)
        {
            bSetState = FALSE;
            return bSetState;
        }
        else
        {
            rt_memset(s_tSysPara.ucMeasurePointsID, 0x00, NUM_OF_MEASUREPOINTS);
            for(i = 0; i < s_tSysPara.ucMeasurePointsNum; i++)
            {
                s_tSysPara.ucMeasurePointsID[i] = s_tProtocol.aucRecBuf[8 + i];  // 可变测点ID
            }
        }
        break;
    case 0xB0:
        fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8)));
        s_tSysPara.fCellTempRiseAbnormalThre = fPara / 10.0;  // 单体温升速率异常阈值
        break;
    case 0xB1:
        fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8)));
        s_tSysPara.fSelfDischgACR = fPara / 10.0;  // 自放电容量比率
        break;
    default:
       bSetState = FALSE;
    break;
    }
    return bSetState;
}

static BOOLEAN SetNewPara_Area5(BYTE ucCommandType)
{
    BOOLEAN bSetState = TRUE;
    switch (ucCommandType)
    {
        case 0xB2:
            s_tSysPara.ucDcrFaultAlmThre = s_tProtocol.aucRecBuf[8]; // 直流内阻异常告警阈值
            break;
        case 0xB3:
            s_tSysPara.ucDcrFaultPrtThre = s_tProtocol.aucRecBuf[8]; // 直流内阻异常告警阈值
            break;
        case 0xB4:
            s_tSysPara.fSelfDevelopProtocolOffsetAddr = s_tProtocol.aucRecBuf[8]; // 自研协议偏移地址
            break;
        case 0xB5:
            s_tSysPara.fCapDCPRFaultAlmThre = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8))) / 10.0;  // 容量衰减一致性阈值
            break;
        case 0xB6:
            s_tSysPara.fBattFaultTempHighPrtThre = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8)));  // 电池异常温度高保护阈值(℃)
            break;
        case 0xB7:
            s_tSysPara.bChargeMapEnable = s_tProtocol.aucRecBuf[8]; // 充电MAP使能
            break;
        case 0xB8:      // 严重告警闪烁方式,0：灭，1：常亮，2：慢闪，3：快闪
            s_tSysPara.ucCriticalAlarmlight = s_tProtocol.aucRecBuf[8];
            break;
        case 0xB9:      // 次级告警闪烁方式,0：灭，1：常亮，2：慢闪，3：快闪
            s_tSysPara.ucSecondaryAlarmlight = s_tProtocol.aucRecBuf[8];
            break;
        case 0xBA:
            s_tSysPara.ucCellType = s_tProtocol.aucRecBuf[8]; // 电芯类型
            break;
        case 0xBB:
            s_tSysPara.fDischgHeaterStartupTemp = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8))); // 放电加热膜启动温度(℃)
            break;
        case 0xBC:
            s_tSysPara.fDischgHeaterShutdownTemp = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8))); // 放电加热膜关闭温度(℃)
            break;
        case 0xBD:
            s_tSysPara.ucNTCInvalidShieldNum = s_tProtocol.aucRecBuf[8]; // NTC无效屏蔽路数
            break;
        default:
            bSetState = FALSE;
            break;
    }
    return bSetState;
}

/***************************************************************************
 * @brief    设定系统参数（定点数）D121新增
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
static void SetNewPara_PAD(BYTE ucPort)
{
    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];

    GetSysPara(&s_tSysPara);

    // command type范围检查
    if ((s_tProtocol.ucCommandType >= 0x80 && s_tProtocol.ucCommandType <= 0xBD)) //NTC无效屏蔽路数
    {
        // 此命令中的DATAI字节长度一致，LENID在进入函数前统一检查
    }
    else
    {
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
        return;
    }

    if (TRUE != SetNewPara_Area1(s_tProtocol.ucCommandType))
    {
        if (TRUE != SetNewPara_Area2(s_tProtocol.ucCommandType))
        {
            if (TRUE != SetNewPara_Area3(s_tProtocol.ucCommandType))
            {
                if(TRUE != SetNewPara_Area4(s_tProtocol.ucCommandType))
                {
                    if(TRUE != SetNewPara_Area5(s_tProtocol.ucCommandType))
                    {
                        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
                        return;
                    }
                }
            }
        }
    }

    if (s_tProtocol.ucRTN == RTN_INVALID_DATA) {
        return;
    }
    
    if (False == SetSysPara(&s_tSysPara, True, CHANGE_BY_YD1363))
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
    }
    if (s_tProtocol.ucCommandType == 0x95 && s_tProtocol.ucRTN != RTN_INVALID_DATA)
    {
        SynTimeAndMode(); // 主机修改时间或应用场景同步从机
    }

    return;
}

/***************************************************************************
 * @brief    获取设备厂家信息
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
static void GetFactoryInfo_PAD(BYTE ucPort)
{
    BYTE *p = NULL;
    T_BmsPACKFactoryStruct tBmsPACKFactory;
    T_BmsInfoStruct tBmsInfo;

    rt_memset(&tBmsPACKFactory, 0, sizeof(T_BmsPACKFactoryStruct));
    readBmsPackFacInfo(&tBmsPACKFactory);

    rt_memset_s(&tBmsInfo, sizeof(T_BmsInfoStruct), 0x00, sizeof(T_BmsInfoStruct));
    readBMSInfofact(&tBmsInfo);

    GetSysPara(&s_tSysPara);

    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

    MemsetBuff(p, (BYTE *)(&s_tSysPara.acBMSSysName), (BYTE)rt_strnlen_s((char *)s_tSysPara.acBMSSysName, 20), 20, 0x20); // 电池名称  BMS系统名称
    p += 20;

    *(BYTE *)p = MAJOR_VER;
    p += 1;
    *(SHORT *)p = MINOR_VER;
    p += 1;

    MemsetBuff(p, tBmsInfo.acBattCorpName, (BYTE)rt_strnlen_s(tBmsInfo.acBattCorpName, 20), 20, 0x20); // 电池厂家名称
    p += 20;

    MemsetBuff(p, &tBmsPACKFactory.acDeviceSn[0], (BYTE)rt_strnlen_s((char *)&tBmsPACKFactory.acDeviceSn[0], 15), 15, 0x20); // 整机序列号
    p += 15;

    *p++= 0;
    *p++= 0;
    *p++= 0;
    *p++= 0;
    *p++= 0;

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    return;
}

/***************************************************************************
 * @brief    获取电池特定参数
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
static void GetSpecialPara_PAD(BYTE ucPort)
{
    BYTE *p = NULL;

    GetSysPara(&s_tSysPara);

    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

    *(SHORT *)p = Host2Modbus((SHORT *)&s_tSysPara.wBatteryCap); // 电池组标称容量，禁止设置
    p += 2;
    *p++ = s_tSysPara.aucAlarmLevel[8] > 0 ? 1 : 0; // 短路保护使能，默认上送允许，禁止设置
    *p++ = s_tSysPara.aucAlarmLevel[3] > 0 ? 1 : 0; // 电池组充电过压保护使能，默认上送允许，禁止设置
    *p++ = s_tSysPara.aucAlarmLevel[2] > 0 ? 1 : 0; // 电池组放电欠压保护使能，默认上送允许，禁止设置
    *p++ = s_tSysPara.aucAlarmLevel[22] > 0 ? 1 : 0; // 单体充电过压保护使能，默认上送允许，禁止设置
    *p++ = s_tSysPara.aucAlarmLevel[21] > 0 ? 1 : 0; // 单体放电欠压保护使能，默认上送允许，禁止设置
    *p++ = s_tSysPara.aucAlarmLevel[29] > 0 ? 1 : 0; // 充电过温保护使能，默认上送允许，禁止设置
    *p++ = s_tSysPara.aucAlarmLevel[33] > 0 ? 1 : 0; // 放电过温保护使能，默认上送允许，禁止设置
    *p++ = s_tSysPara.aucAlarmLevel[30] > 0 ? 1 : 0; // 充电低温保护使能，默认上送允许，禁止设置
    *p++ = s_tSysPara.aucAlarmLevel[34] > 0 ? 1 : 0; // 放电低温保护使能，默认上送允许，禁止设置
    *p++ = s_tSysPara.bBuzzerEnable;                 // 蜂鸣器使能
    *p++ = s_tSysPara.bHeatingPadEnable;             // 加热垫使能，默认禁止
    *p++ = s_tSysPara.aucAlarmLevel[5] > 0 ? 1 : 0;  // 充电过流保护使能，默认上送允许，禁止设置
    *p++ = s_tSysPara.aucAlarmLevel[7] > 0 ? 1 : 0;  // 放电过流保护使能，默认上送允许，禁止设置
    *p++ = s_tSysPara.bIntCharge;                    // 智能间歇充电使能
    *p++ = s_tSysPara.aucAlarmLevel[16] > 0 ? 1 : 0; // SOH异常告警使能
    *p++ = s_tSysPara.bChgCurrLiEn;                  // 电池组充电限流使能
    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    return;
}

/***************************************************************************
 * @brief    设置电池特定参数
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
static void SetSpecialPara_PAD(BYTE ucPort)
{
    BYTE ucPara = 0;

    GetSysPara(&s_tSysPara);
    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];
    ucPara = s_tProtocol.aucRecBuf[8];
    if ((s_tProtocol.wRecLenid == 6 && s_tProtocol.ucCommandType == 0xC0) ||
        (s_tProtocol.wRecLenid == 4 && s_tProtocol.ucCommandType != 0xC0))
    {
    }
    else
    {
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
        return;
    }
    switch (s_tProtocol.ucCommandType)
    {
    case 0xC0: // 按照额定容量上报(仅支持查询，不支持设置)
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
        return;
        break;
    case 0xC1: // 短路保护使能
        s_tSysPara.aucAlarmLevel[8] = ucPara;
        break;
    case 0xC2: // 电池组充电过压保护使能
        s_tSysPara.aucAlarmLevel[3] = ucPara;
        break;
    case 0xC3: // 电池组放电欠压保护使能
        s_tSysPara.aucAlarmLevel[2] = ucPara;
        break;
    case 0xC4: // 单体充电过压保护使能
        s_tSysPara.aucAlarmLevel[22] = ucPara;
        break;
    case 0xC5: // 单体放电欠压保护使能
        s_tSysPara.aucAlarmLevel[21] = ucPara;
        break;
    case 0xC6: // 充电过温保护使能
        s_tSysPara.aucAlarmLevel[29] = ucPara;
        break;
    case 0xC7: // 放电过温保护使能
        s_tSysPara.aucAlarmLevel[33] = ucPara;
        break;
    case 0xC8: // 充电低温保护使能
        s_tSysPara.aucAlarmLevel[30] = ucPara;
        break;
    case 0xC9: // 放电低温保护使能
        s_tSysPara.aucAlarmLevel[34] = ucPara;
        break;
    case 0xCA: // 蜂鸣器使能
        s_tSysPara.bBuzzerEnable = ucPara;
        break;
    case 0xCB: // 加热垫使能
        s_tSysPara.bHeatingPadEnable = ucPara;
        break;
    case 0xCC: // 充电过流保护使能
        s_tSysPara.aucAlarmLevel[5] = ucPara;
        break;
    case 0xCD: // 放电过流保护使能
        s_tSysPara.aucAlarmLevel[7] = ucPara;
        break;
    case 0xCE: // 智能间歇充电使能
        s_tSysPara.bIntCharge = ucPara;
        break;
    case 0xCF: // SOH异常告警使能
        s_tSysPara.aucAlarmLevel[16] = ucPara;
        break;
    case 0xD0: // 电池组充电限流使能
        s_tSysPara.bChgCurrLiEn = ucPara;
        break;
    default:
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
        return;
    }

    if (False == SetSysPara(&s_tSysPara, True, CHANGE_BY_YD1363))
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
    }

    return;
}

/***************************************************************************
 * @brief    获取系统参数（F231）
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
static void GetParaNew_PAD(BYTE ucPort)
{
    BYTE *p = NULL;

    GetSysPara(&s_tSysPara);

    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fBattOverVoltAlmThre * 100); // 电池组过压告警阈值(V)
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fBattOverVoltAlmRecoThre * 100); // 电池组过压告警解除
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fChgTempHighAlmThre); // 充电高温告警阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fChgTempHighAlmRecoThre); // 充电高温告警解除
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fChgTempLowAlmThre); // 充电低温告警阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fChgTempLowAlmRecoThre); // 充电低温告警解除
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fEnvTempHighPrtThre); // 环境温度高保护阈值（℃）
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fEnvTempHighPrtRecoThre); // 环境温度高保护解除（℃）
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fEnvTempLowPrtThre); // 环境温度低保护阈值(V)
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fEnvTempLowPrtRecoThre); // 环境温度低保护解除
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.wBattSOCLowAlmThre); // SOC低告警阈值
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.wBattSOCLowAlmRecoThre); // SOC低告警解除
    p += 2;
    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.wBattSOCLowPrtThre); // SOC低保护阈值
    p += 2;
    *(ULONG *)p = (ULONG)Int32ValuetoModbus((INT32)s_tSysPara.ulPoweroffTimePrtThre); // 停电时间保护阈值
    p += 4;
    *p++ = s_tSysPara.aucAlarmLevel[38] > 0 ? 1 : 0; // 环境温度高保护使能
    *p++ = s_tSysPara.aucAlarmLevel[39] > 0 ? 1 : 0; // 环境温度低保护使能
    *p++ = s_tSysPara.aucAlarmLevel[14] > 0 ? 1 : 0; // SOC低保护使能
    *p++ = s_tSysPara.aucAlarmLevel[52] > 0 ? 1 : 0; // 停电时间保护使能

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    return;
}

/***************************************************************************
 * @brief    设定中移新增参数（F231）
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
static void SetParaNew_PAD(BYTE ucPort)
{
    FLOAT fPara = 0.0;
    U_16Int tData;
    // const BYTE aucJudgeType[] = {0x6A, 0x6B, 0x6C, 0x6E, 0x6F, 0x70, 0x71};
    U_32Int uTemp;
    GetSysPara(&s_tSysPara);
    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];
    tData.ucByte[0] = s_tProtocol.aucRecBuf[9];
    tData.ucByte[1] = s_tProtocol.aucRecBuf[8];
		if(s_tProtocol.ucCommandType <= 0x6C)
		{
			fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8)));
		}
		else if (s_tProtocol.ucCommandType == 0x6D)
		{
			//fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8)));
            uTemp.ucData[0] = s_tProtocol.aucRecBuf[11];
            uTemp.ucData[1] = s_tProtocol.aucRecBuf[10];
            uTemp.ucData[2] = s_tProtocol.aucRecBuf[9];
            uTemp.ucData[3] = s_tProtocol.aucRecBuf[8];
		}
		else
		{
			//fPara = WordHost2Modbus((WORD *)(s_tProtocol.aucRecBuf + 8));
		}
    

    // command type检查
    //if (s_tProtocol.ucCommandType >= 0x80 && s_tProtocol.ucCommandType <= 0x98)
    //{
        // 此命令中的DATAI字节长度不一致，单独检查
    //   if (((s_tProtocol.ucCommandType <= 0x87 || s_tProtocol.ucCommandType == 0x91) && (s_tProtocol.wRecLenid / 2 - 2) != 2) ||
    //        ((s_tProtocol.ucCommandType == 0x88 || s_tProtocol.ucCommandType == 0x90) && (s_tProtocol.wRecLenid / 2 - 2) != 1) ||
    //        ((s_tProtocol.ucCommandType >= 0x89 && s_tProtocol.ucCommandType <= 0x8E) && (s_tProtocol.wRecLenid / 2 - 2) != 20) ||
    //        ((s_tProtocol.ucCommandType == 0x8F || s_tProtocol.ucCommandType == 0x93) && (s_tProtocol.wRecLenid / 2 - 2) != 32) ||
    //        ((s_tProtocol.ucCommandType == 0x92 || s_tProtocol.ucCommandType >= 0x94) && (s_tProtocol.wRecLenid / 2 - 2) != 1))
    //    {
    //        s_tProtocol.ucRTN = RTN_INVALID_DATA;
    //        return;
    //    }
    //}
    //else
    //{
    //    s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
    //    return;
    //}

    // 数据域检查：WORD型 BYTE型 BOOLEAN型参数不能为负
    //if (tData.sData < 0)
    //{
    //    while (aucJudgeType[i] != 0x00)
    //    {
    //        if (aucJudgeType[i] == s_tProtocol.ucCommandType)
    //        {
    //            s_tProtocol.ucRTN = RTN_INVALID_DATA;
    //            return;
    //        }
    //        i++;
    //    }
    //}

    switch (s_tProtocol.ucCommandType)
    {
    case 0x60: // 电池组过压告警阈值
        s_tSysPara.fBattOverVoltAlmThre  = fPara / 100;
        break;
    case 0x61: //  电池组过压告警解除
        s_tSysPara.fBattOverVoltAlmRecoThre = fPara / 100;
        break;
    case 0x62: // 充电高温告警阈值
        s_tSysPara.fChgTempHighAlmThre = fPara;
        break;
    case 0x63: // 充电高温告警解除
        s_tSysPara.fChgTempHighAlmRecoThre = fPara;
        break;
    case 0x64: // 充电低温告警阈值
        s_tSysPara.fChgTempLowAlmThre = fPara;
        break;
    case 0x65: // 充电低温告警解除
        s_tSysPara.fChgTempLowAlmRecoThre = fPara;
        break;
    case 0x66: // 环境温度高保护阈值
        s_tSysPara.fEnvTempHighPrtThre = fPara;
        break;
    case 0x67: // 环境温度高保护解除
        s_tSysPara.fEnvTempHighPrtRecoThre = fPara;
        break;
    case 0x68: // 环境温度低保护阈值
        s_tSysPara.fEnvTempLowPrtThre = fPara;
        break;
    case 0x69: // 环境温度低保护解除
        s_tSysPara.fEnvTempLowPrtRecoThre = fPara;
        break;
    case 0x6A: // SOC低告警阈值
        s_tSysPara.wBattSOCLowAlmThre = fPara;
        break;
    case 0x6B: // SOC低告警解除
        s_tSysPara.wBattSOCLowAlmRecoThre = fPara;
        break;
    case 0x6C: // SOC低保护阈值
        s_tSysPara.wBattSOCLowPrtThre = fPara;
        break;
    case 0x6D: // 停电时间保护阈值
        s_tSysPara.ulPoweroffTimePrtThre = uTemp.LData;
        break;
    case 0x6E: // 环境温度高保护使能
        s_tSysPara.aucAlarmLevel[38] = tData.ucByte[1];
        break;
    case 0x6F: // 环境温度低保护使能
        s_tSysPara.aucAlarmLevel[39] = tData.ucByte[1];
        break;
    case 0x70: // SOC低保护使能
        s_tSysPara.aucAlarmLevel[14] = tData.ucByte[1];
        break;
    case 0x71: // 停电时间保护使能
        s_tSysPara.aucAlarmLevel[52] = tData.ucByte[1];
        break;
    default:
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
        return;
    }

    if (False == SetSysPara(&s_tSysPara, True, CHANGE_BY_YD1363))
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
    }

    return;
}

/***************************************************************************
 * @brief    遥控
 **************************************************************************/
static void RemoteCtrl_PAD(BYTE ucPort)
{
    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];

    switch (s_tProtocol.ucCommandType)
    {
    case BATT_IN_PLACE_FLAG: // 电池在位指示
        if (s_tProtocol.ucAddr == 0x00) // 不支持广播
            break;
        SetBattIndicationStatus(True);
        if (RT_NULL == s_waitPADLedQuickNormalTimer)
        {
            s_waitPADLedQuickNormalTimer = rt_timer_create("PADLedQuickNormal", (void *)SetBattIndicationStatus, False, 15000, RT_TIMER_FLAG_ONE_SHOT);
        }

        if(s_waitPADLedQuickNormalTimer != RT_NULL)
        {
            rt_timer_start(s_waitPADLedQuickNormalTimer);
        }
        SaveAction(GetActionId(CONTOL_BATT_INDICATION),"PAD FindAddrbyLED");
        break;
    case REMOTE_CHARGE_ON: // 遥控充电回路闭合
        SaveAction(GetActionId(CONTOL_CHAG_ON),"PAD CHGON");
        break;
    case REMOTE_CHARGE_OFF: // 遥控充电回路断开
        SaveAction(GetActionId(CONTOL_CHAG_OFF),"PAD CHGOFF");
        break;
    case REMOTE_DISCHARGE_ON: // 遥控放电回路闭合
        SaveAction(GetActionId(CONTOL_DSCHAG_ON),"PAD DISCHGON");
        break;
    case REMOTE_DISCHARGE_OFF: // 遥控放电回路断开
        SaveAction(GetActionId(CONTOL_DSCHAG_OFF),"PAD DISCHGOFF");
        break;
    default:
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
        break;
    }

    if ((s_tProtocol.ucAddr == 0x00) || (s_tProtocol.ucAddr == 0x40))
    {
        s_tProtocol.ucRTN = NO_RETURN;
    }
    return;
}

/***************************************************************************
 * @brief    遥控重启BMS定时器
 **************************************************************************/
Static void BMSReset(void *para)
{
    rt_kprintf("system soft ReseType:%d\n",NO_RESET_REMOTE_CTRL);
    system_reset();
}

/***************************************************************************
 * @brief    遥控重启BMS
 **************************************************************************/
Static void RemoteCtrlReset(void)
{
    U_Int ucActId;

#if !defined(KW_CHECK)
    if (RT_NULL == s_ResetTimer)
    {
        s_ResetTimer = rt_timer_create("ResetMCU", BMSReset, RT_NULL, 2 * ONE_SEC, RT_TIMER_FLAG_ONE_SHOT | RT_TIMER_FLAG_SOFT_TIMER);
    }

    if (s_ResetTimer != NULL)
    {
        rt_timer_start(s_ResetTimer);
    }
    else
    {
        s_tProtocol.ucRTN = RTN_FAIL_COMMAND;
        return;
    }
#endif
    SaveHisOpID(&ucActId, CONTOL_RST_BMS);
    SaveAction(ucActId.iData,"PAD Remote Reset");

    saveResetData(RESET_REASON, NO_RESET_REMOTE_CTRL);
    SaveBattInfo();
    SaveHisPoint();
    return;
}

/***************************************************************************
 * @brief    人工防盗解锁和撤防
 **************************************************************************/
Static BOOLEAN ManualAntitheftUnlock(BYTE bType)
{
    T_BmsPACKFactoryStruct tBmsPackFacInfo;
    T_BmsCustomerNameStruct tBmsCustomerName;
    T_UnlockRecordStruct t_UnlockRecord;
    CHAR acLocalSeralNumber[33] = {0};
    WORD wCRCHighLow=0;
    BYTE bCRCHigh = 0;
    BYTE bCRCLow = 0;
    CHAR buff[21]={0};

    rt_memset_s(&tBmsPackFacInfo, sizeof(T_BmsPACKFactoryStruct), 0, sizeof(T_BmsPACKFactoryStruct));
    rt_memset_s(&tBmsCustomerName, sizeof(T_BmsCustomerNameStruct), 0, sizeof(T_BmsCustomerNameStruct));
    rt_memset_s(&t_UnlockRecord, sizeof(T_UnlockRecordStruct), 0, sizeof(T_UnlockRecordStruct));

    readBmsPackFacInfo(&tBmsPackFacInfo);
    readBmsCustomerName(&tBmsCustomerName);
    rt_snprintf_s(&acLocalSeralNumber[0], sizeof(acLocalSeralNumber),"%s%s",&tBmsPackFacInfo.acBmsFacSn[0],&tBmsCustomerName.acBmsCustomerName[0]);
    //将客户名称后的一个字节置为结束符
    rt_memset_s(acLocalSeralNumber + rt_strnlen_s(&tBmsPackFacInfo.acBmsFacSn[0], sizeof(tBmsPackFacInfo.acBmsFacSn)) + 17, 1, 0, 1);
    wCRCHighLow = UnlockCodeCreate(acLocalSeralNumber,rt_strnlen_s(acLocalSeralNumber, 33));
    bCRCHigh = ((0xff00 & wCRCHighLow)>>8);
    bCRCLow = ((0x00ff & wCRCHighLow));
    if(s_tProtocol.aucRecBuf[8]  != bCRCHigh || s_tProtocol.aucRecBuf[9] != bCRCLow)
    {
        s_tProtocol.ucRTN   = RTN_WRONG_COMMAND;
        return False;
    }
    if(GPIO_OFF == GetPMOSStatus())
    {
        s_tProtocol.ucRTN   = RTN_FAIL_COMMAND;
        return False;
    }
    rt_memcpy_s(t_UnlockRecord.ucUnlockAccount, sizeof(t_UnlockRecord.ucUnlockAccount), (BYTE *)(&s_tProtocol.aucRecBuf[12]), sizeof(t_UnlockRecord.ucUnlockAccount));
    rt_snprintf_s(buff, sizeof(buff),"%s", t_UnlockRecord.ucUnlockAccount);
    t_UnlockRecord.ucUnlockType = s_tProtocol.aucRecBuf[10];
    switch (bType) {
        case DEVICE_UNLOCK:
            DealDeviceUnlock( DEVICE_UNLOCK_MANUAL);
            if(t_UnlockRecord.ucUnlockType == 0)
            {
                SaveAction(GetActionId(CONTOL_UPPER_COMPUTER_UNLOCK_ID),buff);
            }
            else
            {
                SaveAction(GetActionId(CONTOL_NETWORK_UNLOCK_ANTITHEFT),buff);
            }
            break;
        case DEVICE_CANCEL_DEFENCE:
            if(GetDefenceStatus() == True) {
                WriteDefenceStatus(False);
            }
            if(t_UnlockRecord.ucUnlockType == 0)
            {
                SaveAction(GetActionId(CONTOL_UPPER_CANCEL_DEVICE_DEFENCE),buff);
            }
            else
            {
                SaveAction(GetActionId(CONTOL_NETWORK_CANCEL_DEVICE_DEFENCE),buff);
            }
            break;
        default:
            break;
    }
    return True;
}


static BOOLEAN CheckDataLength(T_BMSProLengthTable* pProLengthTable, WORD wSize) {
    WORD wIndex = 0;

    RETURN_VAL_IF_FAIL(pProLengthTable != NULL, False);

    for (wIndex = 0; wIndex < wSize; wIndex++) {
        if(s_tProtocol.ucCommandType == pProLengthTable[wIndex].ucCommandType) {
            if (s_tProtocol.wRecLenid == pProLengthTable[wIndex].wDataLength) {
                return True;
            } else {
                return False;
            }
        }
    }
    return False;
}

static BOOLEAN DealSwitchBatteryAddress(void)
{
    if((s_tSysPara.ucBattAddressMode == MANUAL_MODE)&&(s_tProtocol.ucAddr == 0))
    {
        SetBattAddr(s_tSysPara.ucBattSwitchAddr);
        SaveAction(GetActionId(CONTOL_SWITCH_ADDR),"PADSwitch Addr");
        return True;
    }
    else
    {
        s_tProtocol.ucRTN = RTN_FAIL_COMMAND;
        return False;
    }
}


/***************************************************************************
 * @brief    遥控(D121新增)
 **************************************************************************/
static void RemoteCtrlNew_PAD(BYTE ucPort)
{
    T_BMSProLengthTable tProLengthTable[11] = {{0x20, 2},{0x21, 2},{0x22, 2},{0x23, 2},{0x24, 50},{0x25, 2},{0x26, 2},{0x27, 2},{0x28, 2},{0x2A, 50},{0x2B, 2}};
    
    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];

    if (!CheckDataLength(&tProLengthTable[0], sizeof(tProLengthTable)/sizeof(T_BMSProLengthTable))) {
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
        return;
    }

    GetSysPara(&s_tSysPara);
    switch (s_tProtocol.ucCommandType)
    {
        case 0x20: // BMS复位，2S
            RemoteCtrlReset();
            break;
        case 0x21: // 恢复出厂参数
            LoadPara();
            SaveAction(GetActionId(CONTOL_RST_PARA),"PADDefault Para");
            break;
        case 0x22: // 重新地址竞争
            ReAddressCompetition_PAD();
            break;
        case 0x23: // 解除闭锁
            Unblocking_PAD();
            break;
        case SET_BATT_MANUAL_UNLOCK: // 人工解锁防盗
            ManualAntitheftUnlock(DEVICE_UNLOCK);
            break;
        case 0x25: // 地址切换，TODO
            DealSwitchBatteryAddress();
            break;
        case 0x26: // 启动加热
            SetHeaterTestCtrl(HEATER_TEST_ON);
            break;
        case 0x27: // 停止加热
            SetHeaterTestCtrl(HEATER_TEST_OFF);
            break;
        case 0x28: // 清除直流内阻异常保护
            ClearDcrFaultPrt();
            break;
        case MANUAL_CANCEL_DEVICE_DEFENCE: // 人工撤防
            ManualAntitheftUnlock(DEVICE_CANCEL_DEFENCE);
            break;
        case 0x2B: // 人工布防 
            ManualCtrlEnterDefenceStatus_PAD();
            break;
        default:
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
            break;
    }

    if ((s_tProtocol.ucAddr == 0x00) || (s_tProtocol.ucAddr == 0x40))
    {
        s_tProtocol.ucRTN = NO_RETURN;
    }
    return;
}

/***************************************************************************
 * @brief    重新地址竞争（降低圈复杂度）
 **************************************************************************/
static void ReAddressCompetition_PAD()
{
    U_Int ucActId;
    if(s_tSysPara.ucBattAddressMode == AUTO_MODE)
        {
            restartAddrCompete();
            SaveHisOpID(&ucActId, CONTOL_ADDR_COMPETE);
            SaveAction(ucActId.iData, "PAD RstAddress");
        }
        else
        {
            s_tProtocol.ucRTN = RTN_FAIL_COMMAND;
            return;
        }
    return;
}
/***************************************************************************
 * @brief    解除闭锁（降低圈复杂度）
 **************************************************************************/
static void Unblocking_PAD()
{
    s_tProtocol.ucRTN = RTN_CORRECT;
            BduCtrl(SCI_CTRL_BDULOCK, 0);
            SaveAction(GetActionId(CONTOL_RELEASE_LOCK),"PADRemote Unlock");
    return;
}

/***************************************************************************
 * @brief    读boot日期
 * @param    {BYTE} *p
 * @return   {*}
 **************************************************************************/
static void ReadBootVerDate(BYTE *p)
{
    rt_memcpy(p, (BYTE *)BOOT_VER_START, 10);
    return;
}

static void GetSpeFacInfo_PAD(BYTE ucPort) {
    BYTE  *p = NULL;
    BYTE  ucLength = 0;
    T_BmsPACKFactoryStruct tBmsPACKFactory;
    T_BCMDataStruct tBCMAnaData;
    T_DCFactory tDcFactory;
    T_BmsPACKManufactStruct tPackInfo = {0};
    CHAR acPackAndCell[20] = {0};
    SHORT sSymbolPos = -1; // 电芯厂家与PACK厂家中符号'-'所在位置

    rt_memset(&tBCMAnaData, 0, sizeof(tBCMAnaData));
    rt_memset(&tBmsPACKFactory, 0, sizeof(T_BmsPACKFactoryStruct));
    readPackManufact(&tPackInfo);
    readBmsPackFacInfo(&tBmsPACKFactory);
    GetRealData(&tBCMAnaData);
    GetSysPara(&s_tSysPara);

    sSymbolPos = FindSympolPosition(tPackInfo.acCellManufactruer, sizeof(tPackInfo.acCellManufactruer));

    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    p = s_tProtocol.aucSendBuf;
    MemsetBuff(p, tBmsPACKFactory.acDeviceSn, (BYTE)rt_strnlen_s(tBmsPACKFactory.acDeviceSn, 15), 15, 0x20); // 整机序列号
    p += 15;

    *p++ = (BYTE)(tBCMAnaData.wBattSOH&0xff);                 // 电池SOH

    *(SHORT *)p = Host2Modbus((SHORT *)&s_tSysPara.wBatteryCap); // 电池容量
    p += 2;

    rt_memcpy_s((BYTE *)&(acPackAndCell[0]), sizeof(tPackInfo.acPackInfo), (BYTE *)(&tPackInfo.acPackInfo[0]), sizeof(tPackInfo.acPackInfo));   //PACK厂家信息
    rt_memcpy_s((BYTE *)&(acPackAndCell[sSymbolPos + 2]), 1, (BYTE *)(&tPackInfo.acCellManufactruer[sSymbolPos + 1]), 1);  //电芯厂家信息
    rt_memcpy_s((BYTE *)&(p[0]), sizeof(acPackAndCell), (BYTE *)(&acPackAndCell[0]), sizeof(acPackAndCell));
    p += 20;

    *(SHORT *)p = Host2Modbus((SHORT *)&tPackInfo.tBattDate.wYear);       // 电芯出厂日期_年
    p += 2;

    *p = tPackInfo.tBattDate.ucMonth;                                 // 电芯出厂日期_月
    p += 1;

    *p = tPackInfo.tBattDate.ucDay;                                   // 电芯出厂日期_日
    p += 1;

    *(SHORT *)p = Host2Modbus((SHORT *)&tBCMAnaData.wBattCycleTimes);       // 电池循环次数
    p += 2;

#ifdef SOFTBANK_VERSION_BUILD
    MemsetBuff(p, BMS_VER_SOFTBANK, (BYTE)rt_strnlen_s(BMS_VER_SOFTBANK, 20), 20, 0x20);         // BMS软件版本
#else
    MemsetBuff(p, BMS_VER, (BYTE)rt_strnlen_s(BMS_VER, 20), 20, 0x20);         // BMS软件版本
#endif
    p += 20;

    MemsetBuff(p, g_ProConfig.chBMSType, (BYTE)rt_strnlen_s(g_ProConfig.chBMSType, 20), 20, 0x20); // BMS型号
    p += 20;

    MemsetBuff(p, tBmsPACKFactory.acBmsFacSn, 15, 15, 0x20);   //BMS序列号
    p += 15;

    PutInt16ToBuff(p, SOFTWARE_RELEASE_YEAR);     //BMS发布日期
    p += 2;
    *p++ = SOFTWARE_RELEASE_MONTH;
    *p++ = SOFTWARE_RELEASE_DATE;

    ReadBootVerDate(p); // BOOT日期
    p += 10;

    GetBduFact(&tDcFactory);
    MemsetBuff(p, tDcFactory.acSysName, 30, 30, 0x20);                     //BDU名称
    p += 30;

    PutInt16ToBuff(p, ((WORD)tDcFactory.acVerDate[0] << 8) + ((WORD)tDcFactory.acVerDate[1]));       //BDU版本日期
    p +=2;
    *p++ = tDcFactory.acVerDate[2];
    *p++ = tDcFactory.acVerDate[3];

    MemsetBuff(p, tDcFactory.acPlatformVer, 8, 8, 0x20);    //数控平台版本
    p += 8;

    MemsetBuff(p, tDcFactory.acSoftVer, 6, 6, 0x20);        //BDU软件版本
    p += 6;

    MemsetBuff(p, tDcFactory.acAsset, 30, 30, 0x20);        //BDU资产信息
    p += 30;

    PutInt32ToBuff(p, tDcFactory.ulSN);      //BDU出厂序列号
    p += 4;

    *p++ = tDcFactory.bHeatFilm;            //是否支持加热

    *p++ = 38;                                //自定义字节数

    ucLength = PutTimeStruct2Buff(p, tBmsPACKFactory.tFactoryTime);         // 出厂时间
    p += ucLength;

    ucLength = PutTimeStruct2Buff(p, tBmsPACKFactory.tFirstBootTime);       // 第一次开机时间
    p += ucLength;

    MemsetBuff(p, tBmsPACKFactory.acPackBarCode[0], 20, 20, 0x00);        //pack bar code1
    p += 20;

    *p++ = CELL_VOL_NUM_MAX / 2;  // D121电芯串数

    *p++ = MAX_CHARGE_CURRNET; //最大充电电流(25A)

    *p++ = MAX_DISCHARGE_CURRNET; //最大放电电流(50A)

    *p++ = tDcFactory.bParalleUpdate;    //BDU是否支持并发升级

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
}

static void SetSpeFacInfo_PAD(BYTE ucPort) {
    SHORT wTemp = 0;
    U_16Int tData;
    T_BmsPACKFactoryStruct tBmsPACKFactory;
    T_BmsPACKManufactStruct tPackInfo = {0};
    BYTE ucLenBuf[] = {15, 1, 2, 20, 4, 2, 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 20};
    SHORT sSymbolPos = -1; // 电芯厂家与PACK厂家中符号'-'所在位置

    rt_memset((BYTE *)&tBmsPACKFactory, 0x00, sizeof(tBmsPACKFactory));
    GetSysPara(&s_tSysPara);
    readBmsPackFacInfo(&tBmsPACKFactory);
    readPackManufact(&tPackInfo);

    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];
    // command type检查
    if (s_tProtocol.ucCommandType < 0x80 + sizeof(ucLenBuf) && s_tProtocol.ucCommandType >= 0x80)
    {
        // 此命令中的DATAI字节长度不一致，单独检查
        if (ucLenBuf[s_tProtocol.ucCommandType - 0x80] != (s_tProtocol.wRecLenid / 2 - 1))
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
            return;
        }
    }
    else
    {
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
        return;
    }
    switch (s_tProtocol.ucCommandType)
    {
        case 0x80: // 整机序列号
            rt_memset(tBmsPACKFactory.acDeviceSn, 0, sizeof(tBmsPACKFactory.acDeviceSn));
			//if(False == CheckSn(&s_tProtocol.aucRecBuf[8], sizeof(tBmsPACKFactory.acDeviceSn)))
			//{
			//	s_tProtocol.ucRTN  = RTN_INVALID_DATA;
			//	return;
			//}
            rt_memcpy((BYTE *)&(tBmsPACKFactory.acDeviceSn[0]), (BYTE *)(&s_tProtocol.aucRecBuf[8]), sizeof(tBmsPACKFactory.acDeviceSn) - 1);
            break;
        case 0x81: //电池SOH
            if ((SetBattSOH(s_tProtocol.aucRecBuf[8])) == False)
            {
                s_tProtocol.ucRTN = RTN_INVALID_DATA;
            }
            break;
        case 0x82: // 电池容量
            s_tSysPara.wBatteryCap = (WORD)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8)));
            if (False == SetSysPara(&s_tSysPara, True, CHANGE_BY_YD1363))
            {
                s_tProtocol.ucRTN = RTN_INVALID_DATA;
            }
            break;
        case 0x83: // PACK和电芯厂家
            if (False == CheckPackAndCellFormat((CHAR *)&s_tProtocol.aucRecBuf[8], 20))
            {
                s_tProtocol.ucRTN = RTN_INVALID_DATA;
                return;
            }
            sSymbolPos = FindSympolPosition((char*)&s_tProtocol.aucRecBuf[8], LEN_TYPE_STRING_20);
            rt_memcpy((BYTE *)&(tPackInfo.acPackInfo[0]), (BYTE *)(&s_tProtocol.aucRecBuf[8]), sizeof(tPackInfo.acPackInfo) - 1);
            rt_memcpy((BYTE *)&(tPackInfo.acPackInfo[sSymbolPos + 1]), (BYTE *)(&s_tProtocol.aucRecBuf[8 + sSymbolPos + 1]), 1); //// PACK厂家首字母
            rt_memset((BYTE *)&(tPackInfo.acPackInfo[sSymbolPos + 2]), 0, 1);

            rt_memcpy((BYTE *)&(tPackInfo.acCellManufactruer[0]), (BYTE *)(&s_tProtocol.aucRecBuf[8]), sizeof(tPackInfo.acCellManufactruer) - 1);
            rt_memcpy((BYTE *)&(tPackInfo.acCellManufactruer[sSymbolPos + 1]), (BYTE *)(&s_tProtocol.aucRecBuf[8 + sSymbolPos + 2]), 1); //电芯厂家首字母
            rt_memset((BYTE *)&(tPackInfo.acCellManufactruer[sSymbolPos + 2]), 0, 1);
            break;
        case 0x84: // 电芯出厂日期
            wTemp = Host2Modbus((SHORT *)&s_tProtocol.aucRecBuf[8]);
            tPackInfo.tBattDate.wYear = (WORD)wTemp;
            tPackInfo.tBattDate.ucMonth = s_tProtocol.aucRecBuf[10];
            tPackInfo.tBattDate.ucDay = s_tProtocol.aucRecBuf[11];
            if (!CheckDateValid(&tPackInfo.tBattDate))
            {
                s_tProtocol.ucRTN = RTN_INVALID_DATA;
                return;
            }
            break;
        case 0x85: // 电池循环次数
            tData.ucByte[0] = s_tProtocol.aucRecBuf[9];
            tData.ucByte[1] = s_tProtocol.aucRecBuf[8];
            if (tData.sData < 0)
            {
                s_tProtocol.ucRTN = RTN_INVALID_DATA;
                return;
            }
            wTemp = Host2Modbus((SHORT *)&s_tProtocol.aucRecBuf[8]);
            if (True != SetBattCycleTimes(wTemp))
            {
                s_tProtocol.ucRTN = RTN_INVALID_DATA;
            }
            break;
        case 0x88: // BMS序列号
            rt_memset(tBmsPACKFactory.acBmsFacSn, 0, sizeof(tBmsPACKFactory.acBmsFacSn));
            rt_memcpy((BYTE *)&(tBmsPACKFactory.acBmsFacSn[0]), (BYTE *)(&s_tProtocol.aucRecBuf[8]), sizeof(tBmsPACKFactory.acBmsFacSn) - 1);
            break;
        case 0x91: // pack bar code1
            MemsetBuff(tBmsPACKFactory.acPackBarCode[0], (BYTE *)(&s_tProtocol.aucRecBuf[8]), 20, 20, 0x00);
            if(False == CheckDigit((BYTE*)(tBmsPACKFactory.acPackBarCode[0]), 20)){
                s_tProtocol.ucRTN = RTN_INVALID_DATA;
                return;
            }
            break;
        default:
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
            return;
    }

    tBmsPACKFactory.wCRC = CRC_Cal((BYTE *)&tBmsPACKFactory, (sizeof(tBmsPACKFactory) - 2));
    writeBmsPackFacInfo(&tBmsPACKFactory);
    tPackInfo.wCRC = CRC_Cal((BYTE *)&tPackInfo, (sizeof(tPackInfo) - 2));
    writePackManufact(&tPackInfo);

    return;
}

static void GetBattNum_PAD(BYTE ucPort)
{
    BYTE *p = NULL;

    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

    if (IsMaster() == True)
    {
        *p++ = GetBattNum();
    }
    else
    {
        *p++ = 1;
    }
    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    return;
}

/***************************************************************************
 * @brief    检查历史记录命令传入的command type
 * @return   True-无问题 False-有问题
 **************************************************************************/
static BOOLEAN CheckHisCommandType(void)
{
    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[COMMAND_TYPE];

    if (s_tProtocol.ucCommandType == GET_RECORD_TIME_SLOT || s_tProtocol.ucCommandType == GET_RECORD_CORRECT ||
        s_tProtocol.ucCommandType == GET_RECORD_WRONG || s_tProtocol.ucCommandType == GET_RECORD_COMPLETE ||
        s_tProtocol.ucCommandType == GET_RECORD_NUM)
    {
        return True;
    }

    s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
    return False;
}

/***************************************************************************
 * @brief    获取历史数据
 **************************************************************************/
static void GetHisData_PAD(BYTE ucPort)
{
    BYTE *p = NULL;
    WORD wHisNum = 0;
    BYTE ucCommandID = 0;
    T_HisDataStruct tHisData;
    static WORD wStartIndex = 0, wEndIndex = 0;
    static BYTE s_ucCommandID = 0;
    BYTE ucStartTimeRtn = 0, ucEndTimeRtn = 0;
    T_TimeStruct tStartTime, tEndTime;
    static BYTE s_ucGetNum = 0;
    static WORD s_wLastTop = 0xFFFF;
    WORD wHisDataLen = 0;

    /* 检查command type */
    if (False == CheckHisCommandType())
    {
        return;
    }

    ucCommandID = s_tProtocol.aucRecBuf[COMMAND_ID]; // ucCommandType为0x00H~0x03H时，存在此字段
    rt_memset(&tHisData, 0, sizeof(T_HisDataStruct));
    rt_memset(&tStartTime, 0, sizeof(T_TimeStruct));
    rt_memset(&tEndTime, 0, sizeof(T_TimeStruct));
    GetSysPara(&s_tSysPara);

    /* 获取特定时间段内的历史数据条数 */
    if (s_tProtocol.ucCommandType == GET_RECORD_NUM)
    {
        ExtractTimeSlot(&tStartTime, &tEndTime, &s_tProtocol.aucRecBuf[8]);

        if (CompareTime(tStartTime, tEndTime) == TIME_GREATER_THAN_TARGET)
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
            return;
        }

        wHisNum = GetHisSaveNum(HISTORICAL_DATA, &tStartTime, &tEndTime);

        p = s_tProtocol.aucSendBuf;
        rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

        /* 历史数据条数4字节 */
        *p++ = 0;
        *p++ = 0;
        *p++ = (wHisNum >> 8) & 0XFF;
        *p++ = wHisNum & 0XFF;
        s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
        return;
    }

    /* 获取特定时间段内的历史数据，根据起始与终止时间，得到对应的readpoint */
    if(s_tProtocol.ucCommandType == GET_RECORD_TIME_SLOT)
    {
        if(s_tProtocol.wRecLenid != HIS_RECORD_LEN_TYPE_ID_TIME)
        {
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
            return;
        }

        ExtractTimeStructFromBuff(&tStartTime, &s_tProtocol.aucRecBuf[9]);
        ExtractTimeStructFromBuff(&tEndTime, &s_tProtocol.aucRecBuf[16]);

        if (IfTimeAllZero(tStartTime, tEndTime) == True)
        {
            wStartIndex = 0;
            wEndIndex = GetHisDataTotNum() - 1;
        }
        else
        {
            DivideRecordToBlock(s_atBlockTime, &s_ucBlockNum, HISTORICAL_DATA);
            if (s_ucBlockNum == 0)
            {
                s_tProtocol.ucRTN = RTN_NO_DATA;
                return;
            }

            /* 获得起始时间与终止时间对应的readpoint */
            ucStartTimeRtn = GetStartTimePose(s_atBlockTime, s_ucBlockNum, tStartTime, &wStartIndex, HISTORICAL_DATA);
            ucEndTimeRtn = GetEndTimePose(s_atBlockTime, s_ucBlockNum, tEndTime, &wEndIndex, HISTORICAL_DATA);
            if ((ucStartTimeRtn == TIME_INVALID) || (ucEndTimeRtn == TIME_INVALID))
            {
                s_tProtocol.ucRTN = RTN_NO_DATA;
                return;
            }
        }

        SetProtoHisDataPoint(wStartIndex, NORTH_PROTOCOL_PAD);
    }

    /*p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    if (s_tProtocol.ucCommandType == GET_RECORD_CORRECT)
    {
        MoveProtoHisDataPoint();
    }*/

    wHisNum = wEndIndex - GetProtoHisDataPoint(NORTH_PROTOCOL_PAD) + 1;

    if (wHisNum == 0)
    {
        s_tProtocol.ucRTN = RTN_NO_DATA;
        return;
    }
    
    /* 发送第一条历史数据时不会进入以下判断，第二次发送开始进入 */
    if (s_tProtocol.ucCommandType == GET_RECORD_CORRECT || (s_tProtocol.ucCommandType == GET_RECORD_WRONG && ((s_ucCommandID + 1) == ucCommandID)) )//解决切换波特率重发问题
    {
        if (GetProtoHisDataPoint(NORTH_PROTOCOL_PAD) == s_wLastTop)
        {
            MoveProtoHisDataPoint(s_ucGetNum, NORTH_PROTOCOL_PAD); // 移动历史告警数据的readpoint
        }
        wHisNum = wEndIndex - GetProtoHisDataPoint(NORTH_PROTOCOL_PAD) + 1;
        if (wHisNum == 0)
        {
            s_tProtocol.ucRTN = RTN_NO_DATA;
            return;
        }
    }
    s_ucCommandID = ucCommandID;
    s_wLastTop = GetProtoHisDataPoint(NORTH_PROTOCOL_PAD);
    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));


    *p++ = wHisNum > 2 ? 0x00 : 0x01;
    s_ucGetNum = wHisNum > 2? 2 : wHisNum; // 实际待读取的条数
    *p++ = s_ucGetNum; // 历史数据条数（历史数据固定为每次发两条）
   
    /* 以下部分内容见PAD协议的xlsx表格定义 */
    wHisDataLen = ReadMoreHisData(s_ucGetNum, p, NORTH_PROTOCOL_PAD);
    p += wHisDataLen; // 移动指针
    *p++ = ucCommandID; // ucCommandType为0x00H~0x03H时，存在此字段
    /* LENID */
    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);

    return;
}

/***************************************************************************
 * @brief    是否为无效时间
 **************************************************************************/
static BOOLEAN isInValidTime(BYTE ucStartTimeRtn, BYTE ucEndTimeRtn)
{
    if((ucStartTimeRtn == TIME_INVALID) || (ucEndTimeRtn == TIME_INVALID))
    {
        return True;
    }
    return False;
}

/***************************************************************************
 * @brief    读取并打包回包数据
 **************************************************************************/
static void ReadAndPackDcrRecord(BYTE *aucSendBuf, WORD wHisNum, BYTE *ucGetNum, BYTE ucCommandId)
{
    *aucSendBuf++ = wHisNum > 2 ? 0x00 : 0x01;
    *ucGetNum = wHisNum > 2? 2 : wHisNum; // 实际待读取的条数
    *aucSendBuf++ = *ucGetNum; // 直流内阻记录条数（直流内阻记录固定为每次发两条）
   
    /* 以下部分内容见PAD协议的xlsx表格定义 */
    ReadMoreDcrRecord_new(*ucGetNum, aucSendBuf, NORTH_PROTOCOL_PAD);
    aucSendBuf += *ucGetNum * DCR_RECORD_LEN; 
    *aucSendBuf++ = ucCommandId; // ucCommandType为0x00H~0x03H时，存在此字段
    /* LENID */
    s_tProtocol.wSendLenid = GetLength((aucSendBuf - s_tProtocol.aucSendBuf) * 2);
}

/***************************************************************************
 * @brief    获取直流内阻记录
 **************************************************************************/
static void GetDcrRecord_PAD(BYTE ucPort)
{
    BYTE *p = NULL;
    WORD wHisNum = 0;
    BYTE ucCommandID = 0;
    T_DcrRecordStruct tDcrRecord;
    static WORD wStartIndex = 0, wEndIndex = 0;
    static BYTE s_ucCommandID = 0;
    BYTE ucStartTimeRtn = 0, ucEndTimeRtn = 0;
    T_TimeStruct tStartTime, tEndTime;
    static BYTE s_ucGetNum = 0;
    static WORD s_wLastTop = 0xFFFF;

    /* 检查command type */
    if (False == CheckHisCommandType())
    {
        return;
    }

    ucCommandID = s_tProtocol.aucRecBuf[COMMAND_ID]; // ucCommandType为0x00H~0x03H时，存在此字段
    rt_memset(&tDcrRecord, 0, sizeof(T_DcrRecordStruct));
    rt_memset(&tStartTime, 0, sizeof(T_TimeStruct));
    rt_memset(&tEndTime, 0, sizeof(T_TimeStruct));
    GetSysPara(&s_tSysPara);

    /* 获取特定时间段内的直流内阻记录条数 */
    if (s_tProtocol.ucCommandType == GET_RECORD_NUM)
    {
        ExtractTimeSlot(&tStartTime, &tEndTime, &s_tProtocol.aucRecBuf[8]);

        if (CompareTime(tStartTime, tEndTime) == TIME_GREATER_THAN_TARGET)
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
            return;
        }

        wHisNum = GetHisSaveNum(DCR_RECORD, &tStartTime, &tEndTime);

        p = s_tProtocol.aucSendBuf;
        rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

        /* 直流内阻记录条数4字节 */
        *p++ = 0;
        *p++ = 0;
        *p++ = (wHisNum >> 8) & 0XFF;
        *p++ = wHisNum & 0XFF;
        s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
        return;
    }

    /* 获取特定时间段内的直流内阻记录，根据起始与终止时间，得到对应的readpoint */
    if(s_tProtocol.ucCommandType == GET_RECORD_TIME_SLOT)
    {
        if(s_tProtocol.wRecLenid != HIS_RECORD_LEN_TYPE_ID_TIME)
        {
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
            return;
        }

        ExtractTimeStructFromBuff(&tStartTime, &s_tProtocol.aucRecBuf[9]);
        ExtractTimeStructFromBuff(&tEndTime, &s_tProtocol.aucRecBuf[16]);

        if (IfTimeAllZero(tStartTime, tEndTime) == True)
        {
            wStartIndex = 0;
            wEndIndex = GetDcrRecordTotNum() - 1;
        }
        else
        {
            DivideRecordToBlock(s_atBlockTime, &s_ucBlockNum, DCR_RECORD);
            if (s_ucBlockNum == 0)
            {
                s_tProtocol.ucRTN = RTN_NO_DATA;
                return;
            }

            /* 获得起始时间与终止时间对应的readpoint */
            ucStartTimeRtn = GetStartTimePose(s_atBlockTime, s_ucBlockNum, tStartTime, &wStartIndex, DCR_RECORD);
            ucEndTimeRtn = GetEndTimePose(s_atBlockTime, s_ucBlockNum, tEndTime, &wEndIndex, DCR_RECORD);
            if (isInValidTime(ucStartTimeRtn, ucEndTimeRtn))
            {
                s_tProtocol.ucRTN = RTN_NO_DATA;
                return;
            }
        }

        SetProtoDcrRecordPoint(wStartIndex, NORTH_PROTOCOL_PAD);
    }

    /*p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    if (s_tProtocol.ucCommandType == GET_RECORD_CORRECT)
    {
        MoveProtoHisDataPoint();
    }*/

    wHisNum = wEndIndex - GetProtoDcrRecordPoint(NORTH_PROTOCOL_PAD) + 1;

    if (wHisNum == 0)
    {
        s_tProtocol.ucRTN = RTN_NO_DATA;
        return;
    }
    
    /* 发送第一条直流内阻记录时不会进入以下判断，第二次发送开始进入 */
    if (s_tProtocol.ucCommandType == GET_RECORD_CORRECT || (s_tProtocol.ucCommandType == GET_RECORD_WRONG && ((s_ucCommandID + 1) == ucCommandID)) )//解决切换波特率重发问题
    {
        if (GetProtoDcrRecordPoint(NORTH_PROTOCOL_PAD) == s_wLastTop)
        {
            MoveProtoDcrRecordPoint(s_ucGetNum, NORTH_PROTOCOL_PAD); // 移动直流内阻记录的readpoint
        }
        wHisNum = wEndIndex - GetProtoDcrRecordPoint(NORTH_PROTOCOL_PAD) + 1;
        if (wHisNum == 0)
        {
            s_tProtocol.ucRTN = RTN_NO_DATA;
            return;
        }
    }
    s_ucCommandID = ucCommandID;
    s_wLastTop = GetProtoDcrRecordPoint(NORTH_PROTOCOL_PAD);
    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

    ReadAndPackDcrRecord(p, wHisNum, &s_ucGetNum, ucCommandID);

    return;
}

/***************************************************************************
 * @brief    获取历史告警
 **************************************************************************/
static void GetHisAlarm_PAD(BYTE ucPort)
{
    BYTE *p;
    WORD wAlarmNum;
    BYTE ucCommandID = 0;
    static BYTE s_ucGetNum = 0;
    static WORD s_wLastTop = 0xFFFF;
    static WORD wStartIndex = 0, wEndIndex = 0;
    static BYTE s_ucCommandID = 0;
    BYTE ucStartTimeRtn = 0, ucEndTimeRtn = 0;
    T_TimeStruct tStartTime, tEndTime;

    /* 检查command type */
    if (False == CheckHisCommandType())
    {
        return;
    }

    ucCommandID = s_tProtocol.aucRecBuf[COMMAND_ID];

    /* 获取特定时间段内的历史告警条数 */
    if(s_tProtocol.ucCommandType == GET_RECORD_NUM)
    {
        ExtractTimeSlot(&tStartTime, &tEndTime, &s_tProtocol.aucRecBuf[8]);

        if (CompareTime(tStartTime, tEndTime) == TIME_GREATER_THAN_TARGET)
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
            return;
        }

        wAlarmNum = GetHisSaveNum(HISTORICAL_ALARM, &tStartTime, &tEndTime);

        p = s_tProtocol.aucSendBuf;
        rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

        /* 历史告警条数4字节 */
        *p++ = 0;
        *p++ = 0;
        *p++ = (wAlarmNum >> 8) & 0XFF;
        *p++ = wAlarmNum & 0XFF;
        s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
        return;
    }

    /* 获取特定时间段内的历史告警，根据起始与终止时间，得到最终的readpoint */
    if(s_tProtocol.ucCommandType == GET_RECORD_TIME_SLOT)
    {
        if(s_tProtocol.wRecLenid != HIS_RECORD_LEN_TYPE_ID_TIME)
        {
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
            return;
        }

        ExtractTimeStructFromBuff(&tStartTime, &s_tProtocol.aucRecBuf[9]);
        ExtractTimeStructFromBuff(&tEndTime, &s_tProtocol.aucRecBuf[16]);

        if (IfTimeAllZero(tStartTime, tEndTime) == True)
        {
            wStartIndex = 0;
            wEndIndex = GetHisAlarmTotNum() - 1;
        }
        else
        {
            DivideRecordToBlock(s_atBlockTime, &s_ucBlockNum, HISTORICAL_ALARM);
            if (s_ucBlockNum == 0)
            {
                s_tProtocol.ucRTN = RTN_NO_DATA;
                return;
            }

            /* 获得起始时间与终止时间对应的readpoint */
            ucStartTimeRtn = GetStartTimePose(s_atBlockTime, s_ucBlockNum, tStartTime, &wStartIndex, HISTORICAL_ALARM);
            ucEndTimeRtn = GetEndTimePose(s_atBlockTime, s_ucBlockNum, tEndTime, &wEndIndex, HISTORICAL_ALARM);
            if ((ucStartTimeRtn == TIME_INVALID) || (ucEndTimeRtn == TIME_INVALID))
            {
                s_tProtocol.ucRTN = RTN_NO_DATA;
                return;
            }
        }

        SetProtoHisAlarmPoint(wStartIndex, NORTH_PROTOCOL_PAD);
    }

    wAlarmNum = wEndIndex - GetProtoHisAlarmPoint(NORTH_PROTOCOL_PAD) + 1;

    if (wAlarmNum == 0)
    {
        ClearDataFlag(BCM_ALARM, 0xFE, ucPort);
        s_tProtocol.ucRTN = RTN_NO_DATA;
        return;
    }

    /* 发送第一条历史告警时不会进入以下判断，第二次发送开始进入 */
    if (s_tProtocol.ucCommandType == GET_RECORD_CORRECT || (s_tProtocol.ucCommandType == GET_RECORD_WRONG && ((s_ucCommandID + 1) == ucCommandID)) )
    {
        if (GetProtoHisAlarmPoint(NORTH_PROTOCOL_PAD) == s_wLastTop)
        {
            MoveProtoHisAlarmPoint(s_ucGetNum, NORTH_PROTOCOL_PAD); // 移动历史告警数据的readpoint
        }
        wAlarmNum = wEndIndex - GetProtoHisAlarmPoint(NORTH_PROTOCOL_PAD) + 1;
        if (wAlarmNum == 0)
        {
            ClearDataFlag(BCM_ALARM, 0xFE, ucPort);
            s_tProtocol.ucRTN = RTN_NO_DATA;
            return;
        }
    }
    s_ucCommandID = ucCommandID;
    s_wLastTop = GetProtoHisAlarmPoint(NORTH_PROTOCOL_PAD);
    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

    /* DATA TYPE判断，判断是否为最后一次发送 */
    *p++ = wAlarmNum > 10 ? 0x00 : 0x01;
    s_ucGetNum = wAlarmNum > 10 ? 10 : wAlarmNum; // 实际待读取的条数
    *p++ = s_ucGetNum; 
    ReadMoreHisAlarm(s_ucGetNum, p, NORTH_PROTOCOL_PAD);
    p += 16 * s_ucGetNum; // 移动指针
    *p++ = ucCommandID;

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    return;
}

/***************************************************************************
 * @brief    获取历史操作
 **************************************************************************/
static void GetHisAction_PAD(BYTE ucPort)
{
    BYTE *p = NULL;
    WORD wActionNum;
    static BYTE s_ucGetNum = 0;
    static WORD s_wLastTop = 0xFFFF;
    static WORD wStartIndex = 0, wEndIndex = 0;
    static BYTE s_ucCommandID = 0;
    BYTE ucStartTimeRtn = 0, ucEndTimeRtn = 0;
    T_TimeStruct tStartTime, tEndTime;
    BYTE ucCommandID = 0;

    if (False == CheckHisCommandType())
    {
        return;
    }

    ucCommandID = s_tProtocol.aucRecBuf[COMMAND_ID];

    /* 获取特定时间段内的历史操作记录条数 */
    if (s_tProtocol.ucCommandType == GET_RECORD_NUM)
    {
        ExtractTimeSlot(&tStartTime, &tEndTime, &s_tProtocol.aucRecBuf[8]);

        if (CompareTime(tStartTime, tEndTime) == TIME_GREATER_THAN_TARGET)
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
            return;
        }

        wActionNum = GetHisSaveNum(HISTORICAL_ACTION, &tStartTime, &tEndTime);
        p = s_tProtocol.aucSendBuf;
        rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

        /* 历史操作记录条数4字节 */
        *p++ = 0;
        *p++ = 0;
        *p++ = (wActionNum >> 8) & 0XFF;
        *p++ = wActionNum & 0XFF;
        s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
        return;
    }

    /* 获取特定时间段内的历史操作，根据起始与终止时间，得到最终的readpoint */
    if (s_tProtocol.ucCommandType == GET_RECORD_TIME_SLOT)
    {
        if (s_tProtocol.wRecLenid != HIS_RECORD_LEN_TYPE_ID_TIME)
        {
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
            return;
        }

        ExtractTimeStructFromBuff(&tStartTime, &s_tProtocol.aucRecBuf[9]);
        ExtractTimeStructFromBuff(&tEndTime, &s_tProtocol.aucRecBuf[16]);

        if (IfTimeAllZero(tStartTime, tEndTime) == True)
        {
            wStartIndex = 0;
            wEndIndex = GetHisOperationTotNum() - 1;
        }
        else
        {
            DivideRecordToBlock(s_atBlockTime, &s_ucBlockNum, HISTORICAL_ACTION);
            if (s_ucBlockNum == 0)
            {
                s_tProtocol.ucRTN = RTN_NO_DATA;
                return;
            }
            ucStartTimeRtn = GetStartTimePose(s_atBlockTime, s_ucBlockNum, tStartTime, &wStartIndex, HISTORICAL_ACTION);
            ucEndTimeRtn = GetEndTimePose(s_atBlockTime, s_ucBlockNum, tEndTime, &wEndIndex, HISTORICAL_ACTION);
            if ((ucStartTimeRtn == TIME_INVALID) || (ucEndTimeRtn == TIME_INVALID))
            {
                s_tProtocol.ucRTN = RTN_NO_DATA;
                return;
            }
        }

        SetProtoHisActionPoint(wStartIndex, NORTH_PROTOCOL_PAD);
    }

    wActionNum = wEndIndex - GetProtoHisOperPoint(NORTH_PROTOCOL_PAD) + 1;

    if (wActionNum == 0)
    {
        s_tProtocol.ucRTN = RTN_NO_DATA;
        return;
    }

    if (s_tProtocol.ucCommandType == GET_RECORD_CORRECT || (s_tProtocol.ucCommandType == GET_RECORD_WRONG && ((s_ucCommandID + 1) == ucCommandID)) )
    {
        if (GetProtoHisOperPoint(NORTH_PROTOCOL_PAD) == s_wLastTop)
        {
            MoveProtoHisOperPoint(s_ucGetNum, NORTH_PROTOCOL_PAD);
        }
        wActionNum = wEndIndex - GetProtoHisOperPoint(NORTH_PROTOCOL_PAD) + 1;
        if (wActionNum == 0)
        {
            s_tProtocol.ucRTN = RTN_NO_DATA;
            return;
        }
    }
    s_ucCommandID = ucCommandID;
    s_wLastTop = GetProtoHisOperPoint(NORTH_PROTOCOL_PAD);
    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

    /* DATA TYPE判断 */
    *p++ = wActionNum > 5 ? 0x00 : 0x01;
    s_ucGetNum = wActionNum > 5 ? 5 : wActionNum; // 实际待读取的条数
    *p++ = s_ucGetNum;
    ReadMoreHisOperation(s_ucGetNum, p, NORTH_PROTOCOL_PAD);
    p += 30 * s_ucGetNum;
    *p++ = ucCommandID;

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    return;
}

static void RecSysTime_PAD(BYTE ucPort) {
    RecSysTime(ucPort);
}


/***************************************************************************
 * @brief   波特率切换
 **************************************************************************/
static void	RecSwapBaudrate_PAD(BYTE ucPort)
{
  	BYTE *p = NULL;
    CHAR buff[21] = {0};
    WORD wTemp=0;
    UINT32 boudMode = 0;
    BYTE handShakeCount = 0;

    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];
    
    s_tProtocol.ucRTN = RTN_CORRECT;
    switch (s_tProtocol.ucCommandType)
    {
        case SET_HIGH_BAUDRATE:
            //handshake 3 times
            wTemp = (WORD)(Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+8)));
            if(wTemp != 96 && wTemp != 1152 && wTemp != 576 && wTemp != 384 && wTemp != 192){
                s_tProtocol.ucRTN	=  RTN_WRONG_COMMAND;
                setHandShakeCounter(0);
                break;
            }
            handShakeCounterIncrement();
            handShakeCount = getHandShakeCounter();
            if(handShakeCount >= 3){
                rt_snprintf_s(buff, sizeof(buff), "%d to %d", (UINT32)getBaudMode(), (UINT32)wTemp * 100);
                SaveAction(GetActionId(CONTOL_BAUDRATE_SWITCH), buff);
                setBaudMode((UINT32)wTemp * 100);
            }
						boudMode = getBaudMode();
            wTemp = boudMode/100;

            p = s_tProtocol.aucSendBuf;
            rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));
            PutInt16ToBuff(p, wTemp);
            p += 2;
            s_tProtocol.wSendLenid = GetLength((p-s_tProtocol.aucSendBuf) * 2);
            break;
        default:
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
    }

    if((s_tProtocol.ucAddr==0x00) || (s_tProtocol.ucAddr==0x40))
    {
        s_tProtocol.ucRTN	=  NO_RETURN;
        //return;
    }

    return;
}

/***************************************************************************
 * @brief    获取差分升级版本标志
 * @param    {BYTE} ucPort
 * @return   {*}
 * @note     差分标记是由型号_版本号_版本制作时间组成的字符串
 *           差分标记示例："D121_V1.00.00.00_2023-08-24"
 **************************************************************************/
static void GetBsdiffUpgradeVer_PAD(BYTE ucPort)
{
    char upgrade_flag[] = BMS_VER_FLAG;
    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0x00, sizeof(s_tProtocol.aucSendBuf));
    rt_snprintf_s((char *)s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), "%s", upgrade_flag);
    s_tProtocol.wSendLenid = GetLength(MAX_BISDIFF_UPGRADE_FLAG_LEN * 2);
}

BYTE GetChargeTestMode(void)
{
    GetSysPara(&s_tSysPara);
    return s_tSysPara.ucChargeMachineTestMode;
}

static void GetBmsStatisticsNew(BYTE ucPort)
{
    int i, j;
    BYTE *p = NULL;
    SHORT sTemp;
    T_AnalyseInfoStruct tAnalyseInfo;

    rt_memset(&tAnalyseInfo, 0, offsetof(T_AnalyseInfoStruct,wCheckSum)+2);
    GetAnalyticalAlm( &tAnalyseInfo );

    p   = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    *p++ = GetDataFlag( BCM_ALARM, ucPort );
    *p++ = BATT_NUM_BCM;//铁锂电池组数M

    for (i=0; i<CELL_TEMP_RANGE_NUM; i++)
    {
        for (j=0; j<SOC_RANGE_NUM; j++)
        {
            PutInt32ToBuff(p, tAnalyseInfo.aulBattCellTempandSOCRangeTimer[i][j]);
            p += 4;
        }
    }

    PutInt32ToBuff(p, tAnalyseInfo.ulBattUVPTimer);
    p += 4;
    PutInt32ToBuff(p, tAnalyseInfo.ulBattOVPTimer);
    p += 4;
    PutInt32ToBuff(p, tAnalyseInfo.ulBattOCPTimer);
    p += 4;
    PutInt32ToBuff(p, tAnalyseInfo.ulBattUVPShutDownTimer);
    p += 4;

    sTemp = Host2Modbus((SHORT *)&tAnalyseInfo.wBattUVPCounter);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp = Host2Modbus((SHORT *)&tAnalyseInfo.wBattOVPCounter);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp = Host2Modbus((SHORT *)&tAnalyseInfo.wBattOCPCounter);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp = Host2Modbus((SHORT *)&tAnalyseInfo.wBattUVPShutDownCounter);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;

    /* LENID */
    s_tProtocol.wSendLenid  = GetLength((p-s_tProtocol.aucSendBuf)*2);

    return;
}

/***************************************************************************
 * @brief   新增遥控指令(人工布防)
 **************************************************************************/
Static BYTE  ManualCtrlEnterDefenceStatus_PAD(void)
{
    //布防条件
    int value = IsDefenseCondition();
    switch(value)
    {
        case RTN_FAIL_ALREADY_DEFENCE:
            s_tProtocol.ucRTN = RTN_FAIL_ALREADY_DEFENCE;
            break;
        case RTN_FAIL_DEFENCE_PARA:
            s_tProtocol.ucRTN = RTN_FAIL_DEFENCE_PARA;
            break;
        case RTN_FAIL_DEFENCE_LEVEL:
            s_tProtocol.ucRTN = RTN_FAIL_DEFENCE_LEVEL;
            break;
        case RTN_FAIL_DEFENCE_LEVEL_PARA:
            s_tProtocol.ucRTN = RTN_FAIL_DEFENCE_LEVEL_PARA;
            break;
        case RTN_FAIL_COMMAND:
            s_tProtocol.ucRTN  = RTN_FAIL_COMMAND;
            break;
        case RTN_SATISFY_DEFENCE:
            // 布防成功并保存布防状态
            SaveAction(GetActionId(CONTOL_MANUAL_ENTER_DEFENCE), "PADEnterDefence");
            WriteDefenceStatus(True);
            s_tProtocol.ucRTN = RTN_SUCCESS_DEFENCE;
            break;
        default:
            return  FAILURE;
    }
    return  SUCCESSFUL;
}
/***************************************************************************
 * @brief    获取客户名称
 * @param    {BYTE} ucPort
 * @return   无
 * @note     
 **************************************************************************/
Static void GetCustomerName(BYTE ucPort)
{
    BYTE *p = NULL;
    T_BmsCustomerNameStruct tBmsCustomerName;

    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }

    rt_memset_s(&tBmsCustomerName, sizeof(T_BmsCustomerNameStruct), 0, sizeof(T_BmsCustomerNameStruct));

    p  = s_tProtocol.aucSendBuf;
    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));

    readBmsCustomerName(&tBmsCustomerName);

    MemsetBuff(p, &tBmsCustomerName.acBmsCustomerName[0], 17, 17, 0x20);//客户名称
    //MemsetBuff将最后一个字节赋值为0，最后一个字节保持原先的不变
    rt_memcpy_s(p + 16, 1, &tBmsCustomerName.acBmsCustomerName[0] + 16, 1);

    p += 17;

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf)*2);
}

//解决圈复杂度
static BOOLEAN GetBalanceNum_PAD (WORD wAlarmNum,BYTE ucPort)
{
	if (wAlarmNum == 0)
    {
        ClearDataFlag(BCM_ALARM, 0xFE, ucPort);
        s_tProtocol.ucRTN = RTN_NO_DATA;
        return True;
    }

     if(s_tProtocol.ucCommandType == GET_RECORD_CORRECT || s_tProtocol.ucCommandType == GET_RECORD_WRONG || s_tProtocol.ucCommandType == GET_RECORD_COMPLETE) {
        if (s_tProtocol.wRecLenid != HIS_RECORD_LEN_TYPE_ID)
        {
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
            return True;
        }
    }

    return False;
}

/***************************************************************************
 * @brief   获取均衡容量历史记录
 **************************************************************************/
static void SendBalanceCapacityRecord(BYTE ucPort)
{
    BYTE *p;
    WORD alarmNum;
    static BYTE s_ucGetNum = 0;
    static BYTE s_ucCommandID = 0;
    static WORD s_wLastTop = 0xFFFF;
    static WORD s_wStartIndex = 0 , s_wEndIndex = 0;
    BYTE ucCommandID = 0;

    if ( !CheckHisCommandType() )
    {
        return;
    }

    /* 获取特定时间段内的历史数据条数 */
    if(s_tProtocol.ucCommandType == GET_RECORD_NUM)
    {

        alarmNum = GetBalanceTimeRecordNum();

        p = s_tProtocol.aucSendBuf;
        rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

        /* 历史数据条数4字节 */
        *p++ = 0;
        *p++ = 0;
        *p++ = (alarmNum >> 8) & 0XFF;
        *p++ = alarmNum & 0XFF;
        s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
        return;
    }

    ucCommandID = s_tProtocol.aucRecBuf[COMMAND_ID];
     /* 获取特定时间段内的历史数据，根据起始与终止时间，得到最终的readpoint */
    if(s_tProtocol.ucCommandType == GET_RECORD_TIME_SLOT)
    {
        s_wStartIndex = 0;
        s_wEndIndex = GetBalanceTimeRecordNum() - 1;
        SetBalTimeHisDataPoint(s_wStartIndex);
    }

    alarmNum = s_wEndIndex - GetProtoBalanceCapacityPoint(NORTH_PROTOCOL_PAD) + 1;

    // if (alarmNum == 0)
    // {
    //     ClearDataFlag(BCM_ALARM, 0xFE, ucPort);
    //     s_tProtocol.ucRTN = RTN_NO_DATA;
    //     return;
    // }

    // if(s_tProtocol.ucCommandType == GET_RECORD_CORRECT || s_tProtocol.ucCommandType == GET_RECORD_WRONG || s_tProtocol.ucCommandType == GET_RECORD_COMPLETE) 
    // {
    //     if (s_tProtocol.wRecLenid != HIS_RECORD_LEN_TYPE_ID)
    //     {
    //         s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
    //         return;
    //     }
    // }
    if(GetBalanceNum_PAD(alarmNum,ucPort) == True)
    {
        return;
    }

     /* 发送第一条历史数据时不会进入以下判断，第二次发送开始进入 */
    if (s_tProtocol.ucCommandType == GET_RECORD_CORRECT  || (s_tProtocol.ucCommandType == GET_RECORD_WRONG && ((s_ucCommandID + 1) == ucCommandID)) )
    {
        if (GetProtoBalanceCapacityPoint(NORTH_PROTOCOL_PAD) == s_wLastTop)
        {
            MoveBalTimeHisDataPoint(s_ucGetNum); // 移动历史数据的readpoint
        }
    }

    s_ucCommandID = ucCommandID;
    s_wLastTop = GetProtoBalanceCapacityPoint(NORTH_PROTOCOL_PAD);
    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

    /* DATA TYPE判断，判断是否为最后一次发送 */
    *p++ = alarmNum > 1 ? 0x00 : 0x01;

    s_ucGetNum = (alarmNum > 1 ? 1 : alarmNum) & 0x00FF; // 实际待读取的条数
    *p++ = s_ucGetNum;
    ReadBalanceCapacityRecordData(s_ucGetNum, p,NORTH_PROTOCOL_PAD);
    p += 68 * s_ucGetNum; // 移动指针
    *p++ = ucCommandID;
    
    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    return ;

}

static void GetRealBalanceCapacity_PAD(BYTE ucPort)
{
    BYTE *p = NULL;
    BYTE i = 0;
    SHORT sTemp;
	T_HardwareParaStruct tHWPara;
    BYTE ucCellVoltNum = 0; 
    T_HisCellBalCAPData tHisCellBalCAPData;
    T_HisCellBalTimeDataStruct tHisCellBalTimeData;

    readBmsHWPara(&tHWPara);
    ucCellVoltNum = tHWPara.ucCellVoltNum;

    if(tHWPara.ucCellVoltNum > CELL_VOL_NUM)
    {
        return;
    }

    p  = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    /* DATA_FLAG */
    *p++ = GetDataFlag( BCM_ALARM, ucPort );
    *p++ = 1;//铁锂电池组数M

    GetCellBalTimeData(&tHisCellBalTimeData);
   
    if(GetRealBalCAP(&tHisCellBalCAPData,&tHisCellBalTimeData,&tHWPara,&sTemp))
    {
        // sTemp =  Host2Modbus(&sTemp);
        

        *(SHORT *)p = Host2Modbus(&sTemp);       //时间统计，以24小时为周期
        p += 2;

        *p++ = ucCellVoltNum;

        for(i = 0;i < ucCellVoltNum ; i++)
        {
            *(SHORT *)p = FloatChangeToModbus(tHisCellBalCAPData.fBalCAP[i]*100); // SOC低保护阈值
            p += 2;
        }
    }
    else
    {
        s_tProtocol.ucRTN = RTN_NO_DATA;
        return;
    }

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2); 
    return;
}

#ifdef SOFTBANK_VERSION_BUILD
/***************************************************************************
 * @brief   获取真实告警量(软银版本)-软银版本支持
 **************************************************************************/
static void GetRealAlarm_PAD(BYTE ucPort)
{
    BYTE *p = NULL;
    BYTE j = 0;
    BYTE Alarm = 0;
    T_BCMAlarmStruct tBCMAlm;

    rt_memset(&tBCMAlm, 0, sizeof(T_BCMAlarmStruct));
    GetRealAlarm(BCM_ALARM, (BYTE *)&tBCMAlm);

    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    *p++ = GetDataFlag(BCM_ALARM, ucPort);

    *p++ = 1; // 铁锂电池组数M

    *p |= (tBCMAlm.ucBattOverVoltPrt << (0)); // 电池组过压保护

    *p |= (tBCMAlm.ucBattUnderVoltAlm << (1)); // 电池组欠压告警

    *p |= (tBCMAlm.ucBattUnderVoltPrt << (2)); // 电池组欠压保护

    *p |= (0 << (3)); // BMS关闭(预留，暂时不用做)

    *p |= (0 << (4)); // 功率线断开(没有实际使用到)

    *p |= (tBCMAlm.ucBattSOCLowAlm << (5)); // SOC低告警

    *p |= (tBCMAlm.ucBattSOCLowPrt << (6)); // SOC低保护

    *p |= (tBCMAlm.ucBattOverVoltAlm << (7)); // 电池组过压告警

    p++;

    GetAlarmByte1(&tBCMAlm, p);
    p++;

    *p |= (tBCMAlm.ucChgCurrHighAlm << (0)); // 充电过流告警

    *p |= (tBCMAlm.ucChgCurrHighPrt << (1)); // 充电过流保护

    *p |= (tBCMAlm.ucDischgCurrHighAlm << (2)); // 放电过流告警

    *p |= (tBCMAlm.ucDischgCurrHighPrt << (3)); // 放电过流保护

    *p |= (tBCMAlm.ucBattShortCut << (4)); // 短路保护

    *p |= (tBCMAlm.ucCellPoorConsisAlm << (5)); // 电芯一致性差告警

    p++;

    GetAlarmByte2(&tBCMAlm, p);
    p++;

    *p |= (0 << (0)); // 加热(TODO，需增加状态量)

    *p |= (0 << (1)); // 风扇(预留)

    *p |= (tBCMAlm.ucBoardTempHighAlm << (2)); // 单板温度高告警

    *p |= (tBCMAlm.ucBoardTempHighPrt << (3)); // 单板温度高保护

    *p |= (tBCMAlm.ucEnvTempHighAlm << (4)); // 环境温度高告警

    *p |= (tBCMAlm.ucEnvTempLowAlm << (5)); // 环境温度低告警

    *p |= (tBCMAlm.ucHeaterFilmFailure << (6)); // 加热膜失效

    *p |= (tBCMAlm.ucBDUConnTempHighPrt << (7)); // 连接器温度高保护

    p++;

    *p |= 0 << (0); // 电芯损坏告警(会议决策不做)

    Alarm = 0;
    for (j = 0; j < sizeof(tBCMAlm.aucCellDamagePrt) / sizeof(tBCMAlm.aucCellDamagePrt[0]); j++)
    {
        Alarm |= tBCMAlm.aucCellDamagePrt[j];
    }
    *p |= Alarm << (1); // 电芯损坏保护

    Alarm = 0;
    for (j = 0; j < sizeof(tBCMAlm.ucCellTempSensorInvalidAlm) / sizeof(tBCMAlm.ucCellTempSensorInvalidAlm[0]); j++)
    {
        Alarm |= tBCMAlm.ucCellTempSensorInvalidAlm[j];
    }
    Alarm |= tBCMAlm.ucEnvTempSensorInvalidAlm;
    *p |= Alarm << (2); // 温度失效告警 单体温度传感器无效与环境温度传感器无效

    *p |= (tBCMAlm.ucCellVoltSampleFault << (3)); // 电压失效告警

    *p |= ((tBCMAlm.ucChgLoopInvalid | tBCMAlm.ucCurrLimLoopInvalid) << (4)); // 充电/限流回路坏

    *p |= (tBCMAlm.ucDischgLoopInvalid << (5)); // 放电回路坏

    *p |= (tBCMAlm.ucBattSOHPrt << (6)); // SOH异常

    *p |= (tBCMAlm.ucFuseError << (7)); // 熔丝损坏

    p++;

    
    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    return;
}

static BOOLEAN GetSoftBankAlarmRly(T_BCMAlarmStruct const *ptBcmAlarm, T_SoftBankAlarmStruct *ptSoftBankAlarm)
{
    BYTE i= 0;
    if(ptBcmAlarm == NULL || ptSoftBankAlarm == NULL)
    {
        return False;
    }

    T_SoftBankAlarmConfigStruct atSoftBankAlarmConfig[] = {
        {offsetof(T_BCMAlarmStruct, ucHeatConnTempHighPrt), &(ptSoftBankAlarm->ucHeaterFilmFailure)},

        {offsetof(T_BCMAlarmStruct, ucCellPoorConsisPrt), &(ptSoftBankAlarm->ucCellDamagePrt)},
        {offsetof(T_BCMAlarmStruct, ucDcrFaultPrt), &(ptSoftBankAlarm->ucCellDamagePrt)},
        //{offsetof(T_BCMAlarmStruct, ucCellTempAbnormal), &(ptSoftBankAlarm->ucCellDamagePrt)},
        {offsetof(T_BCMAlarmStruct, ucCellTempRiseAbnormal), &(ptSoftBankAlarm->ucCellDamagePrt)},
        //{offsetof(T_BCMAlarmStruct, ucSelfDischFualt), &(ptSoftBankAlarm->ucCellDamagePrt)},

        {offsetof(T_BCMAlarmStruct, ucBDUConnTempHighPrt), &(ptSoftBankAlarm->ucCellTempSensorInvalidAlm)},

        {offsetof(T_BCMAlarmStruct, ucBduEepromAlm), &(ptSoftBankAlarm->ucChgLimitLoopInvalid)},
        {offsetof(T_BCMAlarmStruct, ucBDUBattLockAlm), &(ptSoftBankAlarm->ucChgLimitLoopInvalid)},
        {offsetof(T_BCMAlarmStruct, ucBDUCommFail), &(ptSoftBankAlarm->ucChgLimitLoopInvalid)},
        {offsetof(T_BCMAlarmStruct, ucEqualCircuitFaultAlm), &(ptSoftBankAlarm->ucChgLimitLoopInvalid)},
        {offsetof(T_BCMAlarmStruct, ucMainRelayFail), &(ptSoftBankAlarm->ucChgLimitLoopInvalid)},
        {offsetof(T_BCMAlarmStruct, ucDCDCErr), &(ptSoftBankAlarm->ucChgLimitLoopInvalid)},
        {offsetof(T_BCMAlarmStruct, ucSampleErr), &(ptSoftBankAlarm->ucChgLimitLoopInvalid)},
        {offsetof(T_BCMAlarmStruct, ucAuxiSourceErr), &(ptSoftBankAlarm->ucChgLimitLoopInvalid)},

        {offsetof(T_BCMAlarmStruct, ucBattReverse), &(ptSoftBankAlarm->ucFuseError)},
        {offsetof(T_BCMAlarmStruct, ucLoopFault), &(ptSoftBankAlarm->ucFuseError)},

        {offsetof(T_BCMAlarmStruct, ucInsideTempHighPrt), &(ptSoftBankAlarm->ucEnvTempHighAlm)},

        
    };

    for(i = 0; i < sizeof(atSoftBankAlarmConfig)/sizeof(atSoftBankAlarmConfig[0]); i++)
    {
        CalAlarmRlyState(&atSoftBankAlarmConfig[i], ptBcmAlarm);
    }

    return True;
}

static BOOLEAN CalAlarmRlyState(T_SoftBankAlarmConfigStruct *ptSoftBankAlarmConfig, T_BCMAlarmStruct const *ptBcmAlarm)
{
    BYTE *pAlarm = NULL;

    if(ptSoftBankAlarmConfig == NULL || ptBcmAlarm ==NULL)
    {
        return False;
    }

    pAlarm = (BYTE *)ptBcmAlarm + ptSoftBankAlarmConfig->offset;

    if(*pAlarm != (BYTE)NORMAL)
    {
        *(ptSoftBankAlarmConfig->pucAlarmValue) = *pAlarm;
    }

    return True;
}

#endif


/**************************************************************************
 * @brief   获取极值记录
 **************************************************************************/
static void GetBmsExtremeRecordValue(BYTE ucPort)
{
    //WORD wSendLen = 856;//(1+1+2*9+1+2*15*2+1+2*4*2+1)*2 = 99*2 = 198+658
    BYTE *p = NULL;
    BYTE ucTemp = 0;    //指针偏移
    T_HardwareParaStruct tHWPara = {0};
    BYTE ucCellVoltNum = 0;
    BYTE ucCellTempNum = 0;
    readBmsHWPara(&tHWPara);
    ReadHisExtremeData(&s_tHisExtremeRecordData);
    ucCellVoltNum = tHWPara.ucCellVoltNum<CELL_VOL_NUM_MAX?tHWPara.ucCellVoltNum:CELL_VOL_NUM_MAX;
    ucCellTempNum = tHWPara.ucCellTempNum<CELL_TEMP_NUM_MAX?tHWPara.ucCellTempNum:CELL_TEMP_NUM_MAX;

    p  = s_tProtocol.aucSendBuf;
    rt_memset_s(s_tProtocol.aucSendBuf,sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));
    /* COMMAND_TYPE */
    s_tProtocol.ucCommandType   = s_tProtocol.aucRecBuf[7];
    /* DATA_TYPE */
    if((0x80==s_tProtocol.ucCommandType) || (0x81==s_tProtocol.ucCommandType) ||(0x82==s_tProtocol.ucCommandType))
    {
        *p++ = 0x00;
    }
    else if(0x83==s_tProtocol.ucCommandType)
    {
        *p++ = 0x01;
    }
    else
    {
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
        return;
    }
  
    *p++ = BATT_NUM_BCM;//铁锂电池组数M

    switch (s_tProtocol.ucCommandType)
    {
        case 0x80:
            ucTemp = FillBattExtremeRecordValue(p);    // 填充电池极值记录
            p += ucTemp;
            break;
        case 0x81:
            ucTemp = FillCellVoltMaxExtremeRecordValue(p, ucCellVoltNum);     //填充单体电压最大值极值记录
            p += ucTemp;
            break;
        case 0x82:
            ucTemp = FillCellVoltMinExtremeRecordValue(p, ucCellVoltNum);     //填充单体电压最小值极值记录
            p += ucTemp;
            break;
        case 0x83:
            ucTemp = FillCellTempMaxMinExtremeRecordValue(p, ucCellTempNum);  //填充单体温度最大值最小值极值记录
            p += ucTemp;
            break;
    }

    /* LENID */
    s_tProtocol.wSendLenid = (WORD) ((p-s_tProtocol.aucSendBuf)*2);
    //s_tProtocol.wSendLenid = wSendLen;
    return;
}

static BYTE FillBattExtremeRecordValue(BYTE* p)
{
    SHORT sTemp;
    struct tm tTime;
    BYTE *pTemp = p; //保存指针起始地址
    BYTE ucTemp = 0; //指针偏移

    //电池电压最大值
    sTemp = FloatChangeToModbus(s_tHisExtremeRecordData.fBattVoltMax*100);
    rt_memcpy_s(p,sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    //电池电压最大值记录时间
    localtime_r(&s_tHisExtremeRecordData.BattVoltMaxTime, &tTime);
    ucTemp = PutTime2Buff(p, tTime);
    p += ucTemp;

    //电池电压最小值
    sTemp = FloatChangeToModbus(s_tHisExtremeRecordData.fBattVoltMin*100);
    rt_memcpy_s(p,sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    //电池电压最小值记录时间
    localtime_r(&s_tHisExtremeRecordData.BattVoltMinTime, &tTime);
    ucTemp = PutTime2Buff(p, tTime);
    p += ucTemp;

    //外部电压最大值
    sTemp = FloatChangeToModbus(s_tHisExtremeRecordData.fExterVoltMax*100);
    rt_memcpy_s(p,sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    //外部电压最大值记录时间
    localtime_r(&s_tHisExtremeRecordData.ExterVoltMaxTime, &tTime);
    ucTemp = PutTime2Buff(p, tTime);
    p += ucTemp;

    //环境温度最大值
    sTemp = FloatChangeToModbus(s_tHisExtremeRecordData.fEnvTempMax*10);
    rt_memcpy_s(p,sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    //环境温度最大值记录时间
    localtime_r(&s_tHisExtremeRecordData.EnvTempMaxTime, &tTime);
    ucTemp = PutTime2Buff(p, tTime);
    p += ucTemp;

    //环境温度最小值
    sTemp = FloatChangeToModbus(s_tHisExtremeRecordData.fEnvTempMin*10);
    rt_memcpy_s(p,sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    //环境温度最小值记录时间
    localtime_r(&s_tHisExtremeRecordData.EnvTempMinTime, &tTime);
    ucTemp = PutTime2Buff(p, tTime);
    p += ucTemp;

    //BUS充电电流最大值
    sTemp = FloatChangeToModbus(s_tHisExtremeRecordData.fChargeBusCurrMax*100);
    rt_memcpy_s(p,sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    //BUS充电电流最大值记录时间
    localtime_r(&s_tHisExtremeRecordData.ChargeBusCurrMaxTime, &tTime);
    ucTemp = PutTime2Buff(p, tTime);
    p += ucTemp;

    //BUS放电电流最大值
    sTemp = FloatChangeToModbus(s_tHisExtremeRecordData.fDischargeBusCurrMax*100);
    rt_memcpy_s(p,sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    //BUS放电电流最大值记录时间
    localtime_r(&s_tHisExtremeRecordData.DischargeBusCurrMaxTime, &tTime);
    ucTemp = PutTime2Buff(p, tTime);
    p += ucTemp;

    //电池充电电流最大值
    sTemp = FloatChangeToModbus(s_tHisExtremeRecordData.fChargeBattCurrMax*100);
    rt_memcpy_s(p,sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    //电池充电电流最大值记录时间
    localtime_r(&s_tHisExtremeRecordData.ChargeBattCurrMaxTime, &tTime);
    ucTemp = PutTime2Buff(p, tTime);
    p += ucTemp;

    //电池放电电流最大值
    sTemp = FloatChangeToModbus(s_tHisExtremeRecordData.fDischargeBattCurrMax*100);
    rt_memcpy_s(p,sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    //电池放电电流最大值记录时间
    localtime_r(&s_tHisExtremeRecordData.DischargeBattCurrMaxTime, &tTime);
    ucTemp = PutTime2Buff(p, tTime);
    p += ucTemp;

    return p-pTemp;
}

static BYTE FillCellVoltMaxExtremeRecordValue(BYTE* p, BYTE ucCellVoltNum)
{
    SHORT sTemp;
    struct tm tTime;
    BYTE i = 0;
    BYTE ucTemp = 0;    //指针偏移
    BYTE * pTemp = p;   //指针起始地址

    //单体电压数量
    *p++ = ucCellVoltNum;

    //单体电压最大值
    for(i=0; i<ucCellVoltNum; i++)
    {
        sTemp  = FloatChangeToModbus(s_tHisExtremeRecordData.fCellVoltMax[i]*100);
        rt_memcpy_s(p,sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
        p += 2;
    }

    //单体电压最大值记录时间
    for(i=0; i<ucCellVoltNum; i++)
    {
        localtime_r(&s_tHisExtremeRecordData.CellVoltMaxTime[i], &tTime);
        ucTemp = PutTime2Buff(p, tTime);
        p += ucTemp;
    }

    return p-pTemp;
}

static BYTE FillCellVoltMinExtremeRecordValue(BYTE* p, BYTE ucCellVoltNum)
{
    SHORT sTemp;
    struct tm tTime;
    BYTE i = 0;
    BYTE ucTemp = 0;    //指针偏移
    BYTE * pTemp = p;   //指针起始地址

    //单体电压数量
    *p++ = ucCellVoltNum;

    //单体电压最小值
    for(i=0; i<ucCellVoltNum; i++)
    {
        sTemp  = FloatChangeToModbus(s_tHisExtremeRecordData.fCellVoltMin[i]*100);
        rt_memcpy_s(p,sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
        p += 2;
    }

    //单体电压最小值记录时间
    for(i=0; i<ucCellVoltNum; i++)
    {
        localtime_r(&s_tHisExtremeRecordData.CellVoltMinTime[i], &tTime);
        ucTemp = PutTime2Buff(p, tTime);
        p += ucTemp;
    }

    return p-pTemp;
}

static BYTE FillCellTempMaxMinExtremeRecordValue(BYTE* p, BYTE ucCellTempNum)
{
    SHORT sTemp;
    struct tm tTime;
    BYTE i = 0;
    BYTE ucTemp = 0;    //指针偏移
    BYTE * pTemp = p;   //指针起始地址

    //单体温度数量
    *p++ = ucCellTempNum;

    //单体温度最大值
    for (i = 0; i<ucCellTempNum; i++)
    {
        sTemp  = FloatChangeToModbus(s_tHisExtremeRecordData.fCellTempMax[i]*10);
        rt_memcpy_s(p,sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
        p += 2;
    }

    //单体温度最大值记录时间
    for (i = 0; i<ucCellTempNum; i++)
    {
        localtime_r(&s_tHisExtremeRecordData.CellTempMaxTime[i], &tTime);
        ucTemp = PutTime2Buff(p, tTime);
        p += ucTemp;
    }

    //单体温度最小值
    for (i = 0; i<ucCellTempNum; i++)
    {
        sTemp  = FloatChangeToModbus(s_tHisExtremeRecordData.fCellTempMin[i]*10);
        rt_memcpy_s(p,sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
        p += 2;
    }

    //单体温度最小值记录时间
    for (i = 0; i<ucCellTempNum; i++)
    {
        localtime_r(&s_tHisExtremeRecordData.CellTempMinTime[i], &tTime);
        ucTemp = PutTime2Buff(p, tTime);
        p += ucTemp;
    }

    //自定义字节
    *p++ = 0;

    return p-pTemp;
}


Static void GetFactoryInfoEx(BYTE ucPort)
{
    BYTE *p = NULL;
    T_BmsInfoStruct tBmsInfo;
    rt_memset_s(&tBmsInfo, sizeof(T_BmsInfoStruct), 0x00, sizeof(T_BmsInfoStruct));

    readBMSInfofact(&tBmsInfo);
    p = s_tProtocol.aucSendBuf;
    rt_memset_s(p, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));

    MemsetBuff(p, tBmsInfo.acHardwareInfo, (BYTE)rt_strnlen_s(tBmsInfo.acHardwareInfo, LENGTH_HARDWARE_INFORMATION), LENGTH_HARDWARE_INFORMATION, 0x20); // BMS硬件版本信息

    p += 20;

    *p = 0; // 自定义字节数
    p++;

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    return;
}



static void GetElaborateMemoryManagement(BYTE ucPort)
{
    BYTE *p = NULL;
    rt_int32_t sWriteFsCnt = getWriteFsCnt();

    p = s_tProtocol.aucSendBuf;
    rt_memset_s(p, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));

    PutInt32ToBuff(p, sWriteFsCnt);  // flash擦写次数统计
    p += 4;

    *p = 0; // 自定义字节数
    p ++;

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
}

