/**************************************************************************
* Copyright (C) 2010-2020, ZTE Corporation.
* 版权信息：（C）2011-2020，中兴通讯股份有限公司动力开发部版权所有
* 系统名称：ZXDUPA-PMSA V2.1平台软件
* 文件名称：fm.c
* 文件说明：调频充放电模块源文件
* 作    者：黄雪科
* 版本信息：V1.0
* 设计日期：2024-4-26
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/
#include "common.h"
#include "fm.h"
#include "utils_rtthread_security_func.h"


/***********************  变量定义  ************************/
Static T_FmDistributestruct s_tFmDistributeData; //调频数据
static T_FmSubmissionstruct s_tFmRealData;
static BYTE s_ucFmTimerCounter = 0;  // 调频时间计数器，当进入调频后，如果15分钟未收到调频指令，则退出调频模式


/*****************  静态函数原型定义  **********************/

//调频初始化

VOID InitFrequencyModulation(void)
{
    rt_memset_s(&s_tFmDistributeData, sizeof(s_tFmDistributeData), 0, sizeof(s_tFmDistributeData));
    rt_memset_s(&s_tFmRealData, sizeof(s_tFmRealData), 0, sizeof(s_tFmRealData));
}


//获取充放电禁止状态

BYTE MakeChgAndDischgStatus(BYTE ChgStatus, BYTE DischgStatus)
{
    return (ChgStatus << 1) | DischgStatus;
}


//获取当前的调频模式：备电、调频、错峰

BYTE MakeFMCurrentMode(BYTE ucFmModue, BYTE ucPeakShiftMode)
{
    if(ucFmModue == True)
    {
        return FM_MODE;
    }
    
    if(ucPeakShiftMode == True)
    {
        return PEAK_SHIFT_MODE;
    }

    return BACKUP_POWER;
}


//计算单体电池最大可充电功率

FLOAT GetSingleBatteryChargePower(FLOAT fChgCurrent, FLOAT fChgVoltage)
{
    return (fChgCurrent * fChgVoltage) / 1000.0; //电池最大可充电功率单位为kw，故除以1000
}


//设置调频数据

VOID SetFmDistributeData(T_FmDistributestruct *pFmDistributeData)
{
    rt_memcpy_s((BYTE*)&s_tFmDistributeData, sizeof(T_FmDistributestruct),(BYTE*)pFmDistributeData, sizeof(T_FmDistributestruct));
}


//获取调频数据

VOID GetFmDistributeData(T_FmDistributestruct *pFmDistributeData)
{
    rt_memcpy_s((BYTE*)pFmDistributeData, sizeof(T_FmDistributestruct),(BYTE*)&s_tFmDistributeData, sizeof(T_FmDistributestruct));
}


//获取调频实时数据

VOID GetFmRealData(T_FmSubmissionstruct *pFmRealData)
{
     rt_memcpy_s((BYTE*)pFmRealData, sizeof(T_FmSubmissionstruct),(BYTE*)&s_tFmRealData, sizeof(T_FmSubmissionstruct));
}


//设置调频实时数据

VOID SetFmRealData(T_FmSubmissionstruct *pFmRealData)
{
    rt_memcpy_s((BYTE*)&s_tFmRealData,sizeof(T_FmSubmissionstruct) ,(BYTE*)pFmRealData ,sizeof(T_FmSubmissionstruct));
}


//获取调频模式

BOOLEAN IsFmMode(void)
{
    return s_tFmDistributeData.ucFmStatus;
}


//设置调频模式

BOOLEAN SetFmMode(BOOLEAN FmMode)
{
    s_tFmDistributeData.ucFmStatus = FmMode;
    return True;
}


//设置调频功率

BOOLEAN SetFmPower(FLOAT fPower)
{
    s_tFmDistributeData.fFmPower = fPower;
    return True;
}


//获取退出调频时间计数

BYTE GetFmTimeCounter(void)
{
    return s_ucFmTimerCounter;
}


//设置退出调频时间计数

BOOLEAN SetFmTimeCounter(void)
{
    TimerPlus(s_ucFmTimerCounter, EXIT_FM_TIME);
    return True;
}


//清除退出调频时间计数

BOOLEAN ClearFmTimeCounter(void)
{
    s_ucFmTimerCounter = 0;
    return True;
}

