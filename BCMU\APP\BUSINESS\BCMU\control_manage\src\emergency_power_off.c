/*
 * @file    : control_manage.c
 * @brief   : 控制管理
 * @details : 
 * <AUTHOR> 邹绍云10326737
 * @Date    : 2023-02-08
 * @LastEditTime: 2023-02-08
 * @version : V0.0.1
 * @para    : Copyright (c) 
 *            ZTE Corporation
 * @par     : History
 *     version: author, date, descn
 */
#include "msg.h"
#include "emergency_power_off.h"
#include "do_ctrl.h"
#include "pin_define.h"
#include "cmd.h"
#include "msg_id.h"
#include "device_num.h"

/**
 * @brief 紧急关机控制
 * @param[in] msg      收到的消息
 * @retval    SUCCESSFUL 成功， FAILURE 失败
 * @note
 */
int epo_ctrl_process(void) {
    module_msg_t epo_msg = {0};
    unsigned char msg_data[2] = {0};
    unsigned char i = 0;
    

    epo_msg.dest = MOD_DC_DC;
    epo_msg.src = MOD_CONTROL;
    epo_msg.msg_id = EXE_DEST_CMD_MSG;
    epo_msg.data = &msg_data;
    msg_data[0] = DC_DC_CTRL_EPO;
    for (i = 0; i < BATT_CLUSTER_NUM; i++) {
        msg_data[1] = i + 1;
        send_msg(&epo_msg);
    }

    set_dest_do(CTL1_PIN, DO_ACTIVE);
    set_dest_do(CTL2_PIN, DO_ACTIVE);
    return SUCCESSFUL;
}

/**
 * @brief 关闭继电器
 * @param[in] breaker_no 第几个断路器下的
 * @retval    SUCCESSFUL 成功， FAILURE 失败
 * @note
 */
int relay_ctrl_process(unsigned char breaker_no) {
    module_msg_t msg = {0};
    unsigned char msg_data[2] = {0};

    RETURN_VAL_IF_FAIL(breaker_no > 0 && breaker_no <= 2, FAILURE);

    msg.dest = MOD_DC_DC;
    msg.src = MOD_CONTROL;
    msg.msg_id = EXE_DEST_CMD_MSG;
    msg.data = &msg_data;
    msg_data[0] = DC_DC_CTRL_EPO;
    msg_data[1] = (breaker_no - 1) * 2 + 1;
    send_msg(&msg);
    msg_data[1] = (breaker_no - 1) * 2 + 2;
    send_msg(&msg);

    if(1 == breaker_no)
    {
        set_dest_do(CTL1_PIN, DO_ACTIVE);
    }
    else
    {
        set_dest_do(CTL2_PIN, DO_ACTIVE);
    }

    return SUCCESSFUL;
}
