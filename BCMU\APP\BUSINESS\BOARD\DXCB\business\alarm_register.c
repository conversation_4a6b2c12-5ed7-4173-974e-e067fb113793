#include "alarm_register.h"
#include "alarm_manage.h"
#include "alarm_id_in.h"
#include "realdata_id_in.h"
#include "alarm_config_in.h"
#include "realdata_save.h"
#include "para_id_in.h"
#include "para_manage.h"
#include "south_dev_common.h"
#include "sample.h"
#include "para_id_in.h"
#include "utils_math.h"
#include <math.h>


static float temp_tab[] = 
{
    883.2036, 830.0151, 780.3755, 734.0260, 690.7282, 650.2625, 612.4261, 577.0324, 543.9088, 512.8958,
    483.8463, 456.6244, 431.1039, 407.1685, 384.7103, 363.6294, 343.8333, 325.2361, 307.7581, 291.3257,
    275.8701, 261.3276, 247.6390, 234.7494, 222.6075, 211.1656, 200.3796, 190.2080, 180.6125, 171.5571,
    163.0086, 154.9356, 147.3091, 140.1019, 133.2887, 126.8458, 120.7510, 114.9837, 109.5245, 104.3552,
    99.4589,  94.8197,  90.4228,  86.2542,  82.3008,  78.5505,  74.9916,  71.6135,  68.4060,  65.3595,
    62.4653,  59.7148,  57.1002,  54.6141,  52.2496,  50.0000,  47.8592,  45.8214,  43.8811,  42.0331,
    40.2726,  38.5950,  36.9961,  35.4716,  34.0179,  32.6312,  31.3081,  30.0455,  28.8402,  27.6894,
    26.5903,  25.5404,  24.5372,  23.5785,  22.6620,  21.7857,  20.9476,  20.1459,  19.3788,  18.6448,
    17.9421,  17.2693,  16.6251,  16.0080,  15.4167,  14.8502,  14.3072,  13.7867,  13.2876,  12.8090,
    12.3498,  11.9093,  11.4866,  11.0809,  10.6914,  10.3174,  9.9583,   9.6133,   9.2819,   8.9634,
    8.6573,   8.3631,   8.0802,   7.8082,   7.5466,   7.2950,   7.0528,   6.8198,   6.5956,   6.3797,
    6.1719,   5.9717,   5.7790,   5.5933,   5.4144,   5.2420,   5.0759,   4.9158,   4.7614,   4.6125,
    4.4689,   4.3305,   4.1969,   4.0680,   3.9436,   3.8236,   3.7078,   3.5959,   3.4879,   3.3836,
    3.2828,   3.1855,   3.0915,   3.0007,   2.9129,   2.8280,   2.7460,   2.6666,   2.5900,   2.5158,
    2.4440,   2.3746,   2.3075,   2.2425,   2.1797,   2.1188,   2.0599,   2.0029,   1.9477,   1.8942,
    1.8424,
};
#define TEMP_NUM (sizeof(temp_tab) / sizeof(float))



static alarm_manage_info_t dxcb_manage = {
    ALARM_ID_OFFSET_MAX - 1,        // 告警码的个数
    ana_alm_config_tab,             // 模拟量告警表
    dig_alm_config_tab,             // 状态量告警表
    self_alm_config_tab,            // 自定义告警表
    alm_shielded,                    // 屏蔽关系表
    NULL,        // 以告警码清除相关告警列表
};

void register_dxcb_alarm()
{
    register_alarm_manage_info(&dxcb_manage);
}

float conden_press_cal(float sample_value)
{
    float factor = 0.0f;
    unsigned char sensor_mode = 0;
    get_one_para(DXCB_PARA_ID_HIGH_PRESSURE_SENSOR_MODE_OFFSET, &sensor_mode);
    factor = sensor_mode == 0 ? 22.5 : 23.0;
    return factor / 20.0 * (1301.0 / 301.0 * sample_value - 0.5) * 10.0;
}

float eva_press_cal(float sample_value)
{
    return 1.0 / 2.0 * (1301.0 / 301.0 * sample_value - 0.5) * 10.0;
}

// 吸气过热度低告警
char dxcb_in_overheat_low_status_judge(int alm_id)
{
    int dev_sn = 0;
    unsigned short in_gas_temp_ch = 0;
    unsigned short eva_press_ch = 0;
    float in_gas_temp = 0.0;
    float ntc_r = 0.0;
    float sample_value = 0.0;
    unsigned char stop_status = 0;
    float in_gas_temp_low_thresh = 0.0;
    float eva_press = 0.0;
    float eva_temp = 0.0;
    float in_overheat = 0.0;
    unsigned short run_speed = 0;

    dev_sn = ALM_ID_GET_DEV(alm_id);

    get_one_para(DXCB_PARA_ID_INHALE_TEMP_SAMPLE_CHANNEL_OFFSET + dev_sn - 1, &in_gas_temp_ch);
    if(in_gas_temp_ch == 0)
    {
        return FALSE;
    }

    // 获取吸气温度
    get_one_data(DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + in_gas_temp_ch - 1, &sample_value);
    RETURN_VAL_IF_FAIL(!FLOAT_EQ(sample_value, 0), FALSE);
    ntc_r = (90.0 / sample_value - 31.0);
    in_gas_temp = find_temperature(ntc_r, TEMP_NUM, TEMP_MIN, TEMP_MAX, temp_tab);
    get_one_para(DXCB_PARA_ID_INHALE_OVERHEAT_LOW_ALARM_THRED_OFFSET, &in_gas_temp_low_thresh);

    // 获取蒸发温度
    get_one_para(DXCB_PARA_ID_EVAPORATION_PRESSURE_SAMPLE_CHANNEL_OFFSET + dev_sn - 1, &eva_press_ch);
    if(eva_press_ch == 0)
    {
        return FALSE;
    }

    get_one_data(DXCB_DATA_ID_SAMPLE_AI_SIGNAL + eva_press_ch - 1, &sample_value);
    eva_press = eva_press_cal(sample_value);  //单位是bar

    eva_temp =-0.000000103574*pow(eva_press, 6)+0.0000161145*pow(eva_press, 5)-0.001014935*pow(eva_press, 4)+0.033637436*pow(eva_press,3)-0.656664463*pow(eva_press,2)+9.407599652*eva_press-43.00095055;
    in_overheat = in_gas_temp - eva_temp;

    get_one_data(DXCB_DATA_ID_VFD_COMPRESSOR_RUN_STATUS + dev_sn - 1, &stop_status);
    if(stop_status == 0 && in_overheat >= 0)
    {
        return FALSE;
    }

    get_one_data(DXCB_DATA_ID_COMPRESSOR_RUN_SPEED + dev_sn - 1, &run_speed);
    if(in_overheat < in_gas_temp_low_thresh && run_speed >= 30)
    {
        return TRUE;
    }

    // 返回当前告警值
    return get_realtime_alarm_value(alm_id);
}



// 吸气温度传感器故障
char inhale_temp_sensor_fault_judge(int alm_id)
{   
    int dev_sn = 0;
    unsigned short in_gas_temp_ch = 0;
    float sample_value = 0.0;
    float in_gas_temp = 0.0;
    float ntc_r = 0.0;
    float thresh_high = 0.0, thresh_low = 0.0;

    dev_sn = ALM_ID_GET_DEV(alm_id);
    get_one_para(DXCB_PARA_ID_INHALE_TEMP_SAMPLE_CHANNEL_OFFSET + dev_sn - 1, &in_gas_temp_ch);
    if(in_gas_temp_ch == 0)
    {
        return FALSE;
    }

    // 获取吸气温度
    get_one_data(DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + in_gas_temp_ch - 1, &sample_value);
    RETURN_VAL_IF_FAIL(!FLOAT_EQ(sample_value, 0), FALSE);
    ntc_r = (90.0 / sample_value - 31.0);
    in_gas_temp = find_temperature(ntc_r, TEMP_NUM, TEMP_MIN, TEMP_MAX, temp_tab);
    get_one_para(DXCB_PARA_ID_INHALE_TEMP_SENSOR_FAULT_ALARM_HIGH_THRED_OFFSET, &thresh_high);
    get_one_para(DXCB_PARA_ID_INHALE_TEMP_SENSOR_FAULT_ALARM_LOW_THRED_OFFSET, &thresh_low);

    if((in_gas_temp > thresh_high) || (in_gas_temp < thresh_low))
    {
        return TRUE;
    }
    return FALSE;
}

// 蒸发压力传感器故障
char eva_press_sensor_fault_judge(int alm_id)
{
    int dev_sn = 0;
    unsigned short eva_press_ch = 0;
    float sample_value = 0.0;
    float eva_press = 0.0;
    float thresh_high = 0.0, thresh_low = 0.0;

    dev_sn = ALM_ID_GET_DEV(alm_id);
    get_one_para(DXCB_PARA_ID_EVAPORATION_PRESSURE_SAMPLE_CHANNEL_OFFSET + dev_sn - 1, &eva_press_ch);
    if(eva_press_ch == 0)
    {
        return FALSE;
    }

    get_one_data(DXCB_DATA_ID_SAMPLE_AI_SIGNAL + eva_press_ch - 1, &sample_value);
    eva_press = eva_press_cal(sample_value);
    get_one_para(DXCB_PARA_ID_EVA_PRESS_SENSOR_FAULT_ALARM_HIGH_THRED_OFFSET, &thresh_high);
    get_one_para(DXCB_PARA_ID_EVA_PRESS_SENSOR_FAULT_ALARM_LOW_THRED_OFFSET, &thresh_low);
    if((eva_press > thresh_high) || (eva_press < thresh_low))
    {
        return TRUE;
    }
    return FALSE;
}

// 蒸发压力过低
char eva_press_low_judge(int alm_id)
{
    int dev_sn = 0;
    unsigned short eva_press_ch = 0;
    float sample_value = 0.0;
    float eva_press = 0.0;
    float thresh = 0.0, thresh_recover = 0.0;
    unsigned char alm = FALSE;

    dev_sn = ALM_ID_GET_DEV(alm_id);
    RETURN_VAL_IF_FAIL(dev_sn >= 1 && dev_sn <= MAX_VFD_NUM, FALSE);
    get_one_para(DXCB_PARA_ID_EVAPORATION_PRESSURE_SAMPLE_CHANNEL_OFFSET + dev_sn - 1, &eva_press_ch);
    if(eva_press_ch == 0)
    {
        return FALSE;
    }

    get_one_data(DXCB_DATA_ID_SAMPLE_AI_SIGNAL + eva_press_ch - 1, &sample_value);
    eva_press = eva_press_cal(sample_value);

    get_one_para(DXCB_PARA_ID_EVAPORAT_PRESSURE_LOW_ALARM_THRED_OFFSET, &thresh);
    get_one_para(DXCB_PARA_ID_EVAPORAT_PRESSURE_LOW_RECOVER_THRED_OFFSET, &thresh_recover);

    alm = get_realtime_alarm_value(alm_id);

    if(eva_press <= thresh)
    {
        alm = TRUE;
    } else if(eva_press > thresh_recover) {
        alm = FALSE;
    }

    return alm;
}

// 冷凝压力传感器故障告警
char conden_press_sensor_fault_judge(int alm_id)
{
    unsigned char dev_addr = 0;
    unsigned short vfd_num = 0, chnl = 0;
    float high_thre = 0.0, low_thre = 0.0, sample_val = 0.0;

    dev_addr = ALM_ID_GET_DEV(alm_id);
    get_one_para(DXCB_PARA_ID_COMPRESSORS_NUM_OFFSET, &vfd_num);
    RETURN_VAL_IF_FAIL(dev_addr <= vfd_num, FALSE);

    get_one_para(DXCB_PARA_ID_CONDEN_PRESS_SENSOR_FAULT_ALARM_HIGH_THRED_OFFSET, &high_thre);
    get_one_para(DXCB_PARA_ID_CONDEN_PRESS_SENSOR_FAULT_ALARM_LOW_THRED_OFFSET, &low_thre);
    RETURN_VAL_IF_FAIL(high_thre > low_thre, FALSE);
    get_one_para(DXCB_PARA_ID_CONDEN_PRESSURE_SAMPLE_CHANNEL_OFFSET + dev_addr - 1, &chnl);
    RETURN_VAL_IF_FAIL(1 <= chnl && chnl <= MAX_CHANL_NUM, FALSE);
    get_one_data(DXCB_DATA_ID_SAMPLE_AI_SIGNAL + chnl -1, &sample_val);
    sample_val = conden_press_cal(sample_val);

    return (sample_val > high_thre || sample_val < low_thre) ? TRUE : FALSE;
}

//高低压开关告警
char judge_vol_switch_alm(int alm_id)
{
    unsigned char dev_addr = ALM_ID_GET_DEV(alm_id);

    unsigned short compressor_num = 0;
    unsigned short sample_channle = 0;
    unsigned char switch_value = 0;

    get_one_para(DXCB_PARA_ID_COMPRESSORS_NUM_OFFSET, &compressor_num);
    if(dev_addr > compressor_num)
    {
        return FALSE;
    }
    if(ALM_ID_GET_ALM_CODE(alm_id) == DCXB_ALARM_ID_HIGH_VOL_SWITCH_DISCONNECT)
    {
        get_one_para(DXCB_PARA_ID_HIGH_TENSION_SWITCH_SAMPLE_CHANNEL_OFFSET + (dev_addr - 1), &sample_channle);
    }
    else
    {
        get_one_para(DXCB_PARA_ID_LOW_TENSION_SWITCH_SAMPLE_CHANNEL_OFFSET + (dev_addr - 1), &sample_channle);
    }

    if(sample_channle < DI1_IN_1 || sample_channle > DI_END)
    {
        return FALSE;
    }
    
    get_one_data(DXCB_DATA_ID_INPUT_DRY_POINT + (sample_channle - 1), &switch_value);
    return switch_value == SWITCH_DISSCONN_STATUS;

}

//变频器错误码
char judge_fc_error_alm(int alm_id)
{
    unsigned char dev_addr = ALM_ID_GET_DEV(alm_id);
    unsigned short compressor_num = 0;
    unsigned short fc_error_code = 0;
    unsigned char offset = 0;

    get_one_para(DXCB_PARA_ID_COMPRESSORS_NUM_OFFSET, &compressor_num);
    if(dev_addr > compressor_num)
    {
        return FALSE;
    }

    for(int i = 0; i < MAX_VFD_NUM; i++)
    {
        offset = (dev_addr - 1) * MAX_VFD_NUM + i;
        get_one_data(DXCB_DATA_ID_FREQUENCY_CONVERTER_ERR_CODE + offset, &fc_error_code);
        if(fc_error_code != 0)
        {
            return TRUE;
        }
    }
    return FALSE;
}

// 排气温度计算
int ex_gas_temp_cal()
{
    unsigned short ex_gas_temp_ch = 0;
    float sample_value = 0.0;
    float ntc_r = 0.0;
    float ret_value = 0.0;
    int i = 0;
    for(i = 0; i < MAX_VFD_NUM; i++)
    {
        get_one_para(DXCB_PARA_ID_EXHAUST_TEMP_SAMPLE_CHANNEL_OFFSET + i, &ex_gas_temp_ch);

        if(ex_gas_temp_ch == 0)
        {
            continue;
        }

        get_one_data(DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + ex_gas_temp_ch - 1, &sample_value);
        CONTINUE_IF_FAIL(!FLOAT_EQ(sample_value, 0));
        ntc_r = (90.0 / sample_value - 31.0);

        ret_value = find_temperature(ntc_r, TEMP_NUM, TEMP_MIN, TEMP_MAX, temp_tab);
        set_one_data(DXCB_DATA_ID_EX_GAS_TEMP + i, &ret_value);
    }
    return SUCCESSFUL;
}

// 冷凝压力高告警
char conden_press_high_alm_judge(int alm_id)
{
    unsigned short vfd_num = 0, chnl = 0;
    unsigned char dev_addr = 0, alm = FALSE;
    float alm_thre = 0.0, rec_thre = 0.0, sample_val = 0.0;

    dev_addr = ALM_ID_GET_DEV(alm_id);
    get_one_para(DXCB_PARA_ID_COMPRESSORS_NUM_OFFSET, &vfd_num);
    RETURN_VAL_IF_FAIL(dev_addr <= vfd_num, FALSE);

    get_one_para(DXCB_PARA_ID_CONDEN_PRESSURE_HIGH_ALARM_THRED_OFFSET, &alm_thre);
    get_one_para(DXCB_PARA_ID_CONDEN_PRESSURE_HIGH_RECOVER_THRED_OFFSET, &rec_thre);
    RETURN_VAL_IF_FAIL(alm_thre > rec_thre, FALSE);
    get_one_para(DXCB_PARA_ID_CONDEN_PRESSURE_SAMPLE_CHANNEL_OFFSET + dev_addr - 1, &chnl);
    RETURN_VAL_IF_FAIL(1 <= chnl && chnl <= MAX_CHANL_NUM, FALSE);
    get_one_data(DXCB_DATA_ID_SAMPLE_AI_SIGNAL + chnl -1, &sample_val);
    sample_val = conden_press_cal(sample_val);

    alm = get_realtime_alarm_value(alm_id);

    if(sample_val >= alm_thre)
    {
        alm = TRUE;
    } else if(sample_val < rec_thre) {
        alm = FALSE;
    }

    return alm;
}

// 变频器电流高告警
char vfd_curr_high_alm_judge(int alm_id)
{
    unsigned char dev_addr = 0, alm = FALSE;
    float alm_thre = 0.0, rec_thre = 0.0, curr = 0.0;
    unsigned short vfd_num = 0;

    dev_addr = ALM_ID_GET_DEV(alm_id);
    get_one_para(DXCB_PARA_ID_COMPRESSORS_NUM_OFFSET, &vfd_num);
    RETURN_VAL_IF_FAIL(dev_addr <= vfd_num, FALSE);

    get_one_para(DXCB_PARA_ID_FREQ_CURR_HIGH_ALARM_THRED_OFFSET, &alm_thre);
    get_one_para(DXCB_PARA_ID_FREQ_CURR_HIGH_RECOVER_THRED_OFFSET, &rec_thre);
    RETURN_VAL_IF_FAIL(alm_thre > rec_thre, FALSE);
    get_one_data(DXCB_DATA_ID_FREQUENCY_CONVERTER_BOARD_CURR + dev_addr -1, &curr);
    alm = get_realtime_alarm_value(alm_id);

    if(curr >= alm_thre)
    {
        alm = TRUE;
    } else if(curr < rec_thre) {
        alm = FALSE;
    }
    return alm;
}

// 排气温度传感器故障告警
char exhaust_temp_sensor_fault_judge(int alm_id)
{
    unsigned char dev_addr = 0;
    unsigned short vfd_num = 0, chnl = 0;
    float high_thre = 0.0, low_thre = 0.0, sample_val = 0.0;

    dev_addr = ALM_ID_GET_DEV(alm_id);
    get_one_para(DXCB_PARA_ID_COMPRESSORS_NUM_OFFSET, &vfd_num);
    RETURN_VAL_IF_FAIL(dev_addr <= vfd_num, FALSE);

    get_one_para(DXCB_PARA_ID_EXHAUST_TEMP_SENSOR_FAULT_ALARM_HIGH_THRED_OFFSET, &high_thre);
    get_one_para(DXCB_PARA_ID_EXHAUST_TEMP_SENSOR_FAULT_ALARM_LOW_THRED_OFFSET, &low_thre);
    RETURN_VAL_IF_FAIL(high_thre > low_thre, FALSE);
    get_one_para(DXCB_PARA_ID_EXHAUST_TEMP_SAMPLE_CHANNEL_OFFSET + dev_addr - 1, &chnl);
    RETURN_VAL_IF_FAIL(1 <= chnl && chnl <= MAX_CHANL_NUM, FALSE);
    get_one_data(DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + chnl -1, &sample_val);
    RETURN_VAL_IF_FAIL(!FLOAT_EQ(sample_val, 0), FALSE);
    sample_val = (90.0 / sample_val - 31.0);
    sample_val = find_temperature(sample_val, TEMP_NUM, TEMP_MIN, TEMP_MAX, temp_tab);

    return (sample_val > high_thre || sample_val < low_thre) ? TRUE : FALSE;
}

// 排气过热度低告警
char exhaust_overheat_low_alm_judge(int alm_id)
{
    unsigned char dev_addr = 0, compress_status = 0;
    unsigned short vfd_num = 0, chnl = 0;
    float alm_thre = 0.0, exhuast_temp = 0.0, conden_temp = 0.0, exhuast_overheat;

    dev_addr = ALM_ID_GET_DEV(alm_id);
    get_one_para(DXCB_PARA_ID_COMPRESSORS_NUM_OFFSET, &vfd_num);
    RETURN_VAL_IF_FAIL(dev_addr <= vfd_num, FALSE);

    get_one_data(DXCB_DATA_ID_VFD_COMPRESSOR_RUN_STATUS + dev_addr - 1, &compress_status);
    RETURN_VAL_IF_FAIL(compress_status != 0, FALSE);  // 压缩机停机告警恢复

    // 排气温度
    get_one_para(DXCB_PARA_ID_EXHAUST_TEMP_SAMPLE_CHANNEL_OFFSET + dev_addr - 1, &chnl);
    RETURN_VAL_IF_FAIL(1 <= chnl && chnl <= MAX_CHANL_NUM, FALSE);
    get_one_data(DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + chnl -1, &exhuast_temp);
    RETURN_VAL_IF_FAIL(!FLOAT_EQ(exhuast_temp, 0), FALSE);
    exhuast_temp = (90.0 / exhuast_temp - 31.0);
    exhuast_temp = find_temperature(exhuast_temp, TEMP_NUM, TEMP_MIN, TEMP_MAX, temp_tab);
    // 冷凝温度
    get_one_para(DXCB_PARA_ID_CONDEN_PRESSURE_SAMPLE_CHANNEL_OFFSET + dev_addr - 1, &chnl);
    RETURN_VAL_IF_FAIL(1 <= chnl && chnl <= MAX_CHANL_NUM, FALSE);
    get_one_data(DXCB_DATA_ID_SAMPLE_AI_SIGNAL + chnl -1, &conden_temp);
    conden_temp = conden_press_cal(conden_temp);
    conden_temp = -0.000000103574*pow(conden_temp, 6) + 0.0000161145*pow(conden_temp, 5) - 0.001014935*pow(conden_temp, 4)
                  + 0.033637436*pow(conden_temp,3) - 0.656664463*pow(conden_temp,2) + 9.407599652*conden_temp - 43.00095055;
    // 排气过热度 = 排气温度-冷凝温度
    exhuast_overheat = exhuast_temp - conden_temp;

    get_one_para(DXCB_PARA_ID_EXHAUST_OVERHEAT_LOW_ALARM_THRED_OFFSET, &alm_thre);
    RETURN_VAL_IF_FAIL(exhuast_overheat > alm_thre, TRUE);

    return get_realtime_alarm_value(alm_id);
}

//排气温度高告警
char exhaust_temp_high_alm_judge(int alm_id)
{
    float ex_gas_temp = 0.0, ex_temp_thresh = 0.0, ex_temp_recover = 0.0;
    unsigned char dev_addr =  ALM_ID_GET_DEV(alm_id);

    get_one_data(DXCB_DATA_ID_EX_GAS_TEMP + dev_addr -1, &ex_gas_temp);
    get_one_para(DXCB_PARA_ID_EXHAUST_TEMP_HIGH_ALARM_THRED_OFFSET, &ex_temp_thresh);
    get_one_para(DXCB_PARA_ID_EXHAUST_TEMP_HIGH_RECOVER_THRED_OFFSET, &ex_temp_recover);

    if(ex_gas_temp >= ex_temp_thresh)
    {
        return TRUE;
    }

    if(ex_gas_temp < ex_temp_recover)
    {
        return FALSE;
    }

    // 两个阈值中间返回现在的告警状态
    return get_realtime_alarm_value(alm_id);
}

//变频器温度高告警
char freq_high_temp_judge(int alm_id)
{
    float vfd_temp = 0.0, vfd_thresh = 0.0, vfd_recover = 0.0;
    unsigned char dev_addr =  ALM_ID_GET_DEV(alm_id);
    unsigned short vfd_num = 0;

    get_one_para(DXCB_PARA_ID_COMPRESSORS_NUM_OFFSET, &vfd_num);
    RETURN_VAL_IF_FAIL(dev_addr <= vfd_num, FALSE);

    get_one_data(DXCB_DATA_ID_FREQUENCY_CONVERTER_BOARD_TEMPERATURE + dev_addr -1, &vfd_temp);
    get_one_para(DXCB_PARA_ID_FREQ_TEMP_HIGH_ALARM_THRED_OFFSET, &vfd_thresh);
    get_one_para(DXCB_PARA_ID_FREQ_TEMP_HIGH_RECOVER_THRED_OFFSET, &vfd_recover);

    if(vfd_temp >= vfd_thresh)
    {
        return TRUE;
    }

    if(vfd_temp < vfd_recover)
    {
        return FALSE;
    }

    // 两个阈值中间返回现在的告警状态
    return get_realtime_alarm_value(alm_id);
}

