/**************************************************************************
* Copyright (C) 2001, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：ZXDU18 CTL板软件
* 文件名称：led.c
* 文件说明：指示灯模块
* 作    者：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要
* 其他说明：
***************************************************************************/

#ifndef LED_H_
#define LED_H_

#ifdef __cplusplus
extern "C" {
#endif

#define LED_NUM		6
enum
{
	BMS_SHUTDOWN = 0,		//关机
	BMS_SLEEP ,   	        //休眠
    BMS_STANDBY,            //待机
	BMS_CHARGE,             //充电
	BMS_DISCH,              //放电
	BMS_ADDR,				//显示地址
}; 

enum
{
	LEDOFF = 0,     //灭
	LEDON,          //亮
	LEDSLOWSH,      //慢闪
	LEDSLOWSH2,     //慢闪2
	LEDQUICKSH,     //快闪
	LEDBYSOC,
	LEDBYALM,
	LEDBYADDR
};

enum
{
    BUZZOFF = 0,
    BUZZMODE1,
    BUZZMODE2,
    BUZZBYALM
};

typedef	struct
{
	unsigned char ucShowType;
	unsigned char aucLedMode[LED_NUM];
    unsigned char ucBuzzMode;
}T_ShowConfig;

void InitLedOut( void );
void SetLedByAddr(void);
void SetLedShow(unsigned char ucMode);
void SetShowSpecial(T_ShowConfig *pSpecialMode);
unsigned char GetBuzzMode(void);

/***缺少的接口函数，暂时放在这里*****/
unsigned char GetFireControlStatus(void);
unsigned char GetDefenceStatus(void);
unsigned char GetDefenceStatus(void);
unsigned char GetApptestFlag(void);
unsigned char GetQtptestFlag(void);
unsigned char JudgeAlarmType(void);
/***************/

#ifdef __cplusplus
}
#endif

#endif /* LED_H_ */
