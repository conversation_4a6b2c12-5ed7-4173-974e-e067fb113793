
#ifndef _BCMU_CELL_BALANCE_H
#define _BCMU_CELL_BALANCE_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

/*  Include   */
#include <stdio.h>
#include <rtthread.h>
#include <rtdevice.h>
#include <string.h>
#include "addr_distribution.h"
#include "data_type.h"
#include "device_num.h"

typedef struct
{
    float cell_balance_difference_threshold;
    float cell_balance_open_min_threshold;
    unsigned char cluster_timer_count[BATT_CLUSTER_NUM]; 
    cluster_topology_t cluster_topo;
} cell_balance_data_t;

void set_cell_balance_difference_threshold(float cell_balance_difference_threshold);
void set_cell_balance_open_min_threshold(float cell_balance_open_min_threshold);
void cell_balance_process_start();
void cell_balance_process_stop();
void all_cell_balance_close();
void cluster_cell_balance_close(char cluster_no);
char bmu_cell_balance_send(char bmu_ID, int cell_status);
#ifdef __cplusplus
}
#endif  // __cplusplus

#endif  // _BCMU_CELL_BALANCE_H