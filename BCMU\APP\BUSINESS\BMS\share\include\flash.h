#ifndef  FLASH_H
#define  FLASH_H

#include "board.h"
#include <rtthread.h>
#include <rtdevice.h>
#include "stdint.h"
#define GD32F4_FLASH_BASE              (0x08000000UL)
#define GD32F4_FLASH_SIZE              (0x00100000UL)
#define GD32F4_FLASH_END_ADDR          (0x08100000UL)

/* Base address of the Flash sectors Bank 1 */
#define ADDR_FLASH_SECTOR_0_BANK1      ((uint32_t)0x08000000) /* Base @ of Sector 0, 16 Kbytes */
#define ADDR_FLASH_SECTOR_1_BANK1      ((uint32_t)0x08004000) /* Base @ of Sector 1, 16 Kbytes */
#define ADDR_FLASH_SECTOR_2_BANK1      ((uint32_t)0x08008000) /* Base @ of Sector 2, 16 Kbytes */
#define ADDR_FLASH_SECTOR_3_BANK1      ((uint32_t)0x0800C000) /* Base @ of Sector 3, 16 Kbytes */
#define ADDR_FLASH_SECTOR_4_BANK1      ((uint32_t)0x08010000) /* Base @ of Sector 4, 64 Kbytes */
#define ADDR_FLASH_SECTOR_5_BANK1      ((uint32_t)0x08020000) /* Base @ of Sector 5, 128 Kbytes */
#define ADDR_FLASH_SECTOR_6_BANK1      ((uint32_t)0x08040000) /* Base @ of Sector 6, 128 Kbytes */
#define ADDR_FLASH_SECTOR_7_BANK1      ((uint32_t)0x08060000) /* Base @ of Sector 7, 128 Kbytes */
#define ADDR_FLASH_SECTOR_8_BANK1      ((uint32_t)0x08080000) /* Base @ of Sector 8, 128 Kbytes */
#define ADDR_FLASH_SECTOR_9_BANK1      ((uint32_t)0x080A0000) /* Base @ of Sector 9, 128 Kbytes */
#define ADDR_FLASH_SECTOR_10_BANK1     ((uint32_t)0x080C0000) /* Base @ of Sector 10, 128 Kbytes */
#define ADDR_FLASH_SECTOR_11_BANK1     ((uint32_t)0x080E0000) /* Base @ of Sector 11, 128 Kbytes */

#define FLASH_SECTOR_0             0U       /*!< Sector Number 0   */
#define FLASH_SECTOR_1             1U       /*!< Sector Number 1   */
#define FLASH_SECTOR_2             2U       /*!< Sector Number 2   */
#define FLASH_SECTOR_3             3U       /*!< Sector Number 3   */
#define FLASH_SECTOR_4             4U       /*!< Sector Number 4   */
#define FLASH_SECTOR_5             5U       /*!< Sector Number 5   */
#define FLASH_SECTOR_6             6U       /*!< Sector Number 6   */
#define FLASH_SECTOR_7             7U       /*!< Sector Number 7   */
#define FLASH_SECTOR_8             8U       /*!< Sector Number 8   */
#define FLASH_SECTOR_9             9U       /*!< Sector Number 9   */
#define FLASH_SECTOR_10            10U      /*!< Sector Number 10  */
#define FLASH_SECTOR_11            11U      /*!< Sector Number 11  */

#ifdef UNITEST
#define BOOT_VER_START        "2022-07-12"
#else
#define BOOT_VER_START                0x08007000//BOOT版本信息
#endif
#define FLAG_PROGRAM                  0x08007200 //升级标记
#define USER_FLASH_END_ADDRESS        0x080FFFFF
#define APPLICATION_ADDRESS           0x08008000
#define BACKUP_FLASH_START            0x00001000//程序备份放在NORFLASH
#define UPDATEINFO_STATRT             0x00000000
#define NORFLASH_APP_START            0x00101000 //norflash的起始地址处

#define USER_FLASH_SIZE   (USER_FLASH_END_ADDRESS - APPLICATION_ADDRESS + 1)
#define MSC_MAX_PACKET  256
#define MAX_PACKET_NUM  2944//升级包最大帧数
#define MAX_UPDATE_NORFLASH_NUM	 184 //可供升级使用的norflash扇区的最大数量

#define	FILE_NAME_LEN	40
#define	FILE_TIME_LEN	20
#define	FLAG_IAP		0x55	//待升级
#define	FLAG_CAN_IAP	0x56	//待升级CAN
#define FLAG_NOR_IAP    0x57    //待从NorFlash升级
#define FLAG_MASTER_CAN_IAP 0x58 //待升级CAN，且为主机升级从机
#define FLAG_NOR_TOOL_IAP 0x59  //远程升级工具做客户端升级
#define	FLAG_APP		0x66	//0xAA 已烧写	//升级结束
#define FLAG_NOR_APP    0x67    //主机升级结束
#define	FLAG_BACKUP  	0x88	//运行正常待备份
#define FLAG_NOR_BACKUP 0x89    //网口升级待备份 
#define	FLAG_OK			0x99	//已备份
#define FLAG_NOR_OK     0x9A    //主机完成备份，从机开始升级的标志

/* Error code */
enum 
{
	FLASHIF_OK = 0,
	FLASHIF_ERASEKO,
	FLASHIF_WRITINGCTRL_ERROR,
	FLASHIF_WRITING_ERROR
};
  
enum{
	FLASHIF_PROTECTION_NONE         = 0,
	FLASHIF_PROTECTION_PCROPENABLED = 0x1,
	FLASHIF_PROTECTION_WRPENABLED   = 0x2,
	FLASHIF_PROTECTION_RDPENABLED   = 0x4,
};

typedef struct
{
	uint16_t	wDataLenPerFrame;
	uint16_t	wTotalFrameNum;
	uint32_t	ulTotalFileLength;
	char	    acFileName[FILE_NAME_LEN];
	uint8_t	    acFileTime[FILE_TIME_LEN];
	uint16_t	wFileCheck;
	uint16_t	wResv;
}T_FileAttrStruct;

typedef struct
{
	T_FileAttrStruct tFileAttr;
	uint8_t  ucFlag;
	uint8_t	 ucUpdateAddr;
	uint16_t wCounter;
	uint32_t wBaudRate;
	uint16_t wBackupFrameNum;
	uint16_t wCrc;
}T_FileManageStruct;

void      FLASH_If_Init(void);
rt_err_t  FLASH_If_Erase(uint32_t offset, uint32_t size);
rt_err_t  FLASH_If_Write(uint32_t offset, const uint8_t *buf, uint32_t size);
rt_err_t  FLASH_If_Read(uint32_t offset, uint8_t *buf, uint32_t size);


#endif /* FLASH_H */
