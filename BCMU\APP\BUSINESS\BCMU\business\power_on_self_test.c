#include "power_on_self_test.h"


short power_on_check(int* power_on_check_item,unsigned short len_power_on_check_item)
{
    // 检测每一项是否符合要求
    for(short i = 0; i<len_power_on_check_item;i++)
    {   
        // printf("i = %d \n id = %d \n value = %d \n get_value = %d \n",i,power_on_check_item[i].check_ID,power_on_check_item[i].value,get_realtime_alarm_value(power_on_check_item[i].check_ID));
        //RETURN_VAL_IF_FAIL(power_on_check_item[i].value != get_realtime_alarm_value(power_on_check_item[i].check_ID),FAILURE);
        RETURN_VAL_IF_FAIL(TRUE != get_realtime_alarm_value(power_on_check_item[i]),FAILURE);
    } 
    return SUCCESSFUL;
}


