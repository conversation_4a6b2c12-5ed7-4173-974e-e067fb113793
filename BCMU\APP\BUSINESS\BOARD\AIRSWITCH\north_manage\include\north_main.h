/**
 * @brief 604a main板北向通信头文件
 */

#ifndef _AIRSWITCH_MAIN_NORTH_COMM_H_
#define _AIRSWITCH_MAIN_NORTH_COMM_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "cmd.h"
#include "linklayer.h"
#include "protocol_layer.h"
#include "dev_airswitch.h"

#define  TABLE1_PROCESS_OFFSET 0
#define  TABLE2_PROCESS_OFFSET (sizeof(Airswitch_para1_t))
#define  TIMEOUT_COMMFAIL_MAXTIME  300000  //5分钟
#define  LINK_DEBUG  0    // 用于建链测试验证  1：打开  0：关闭
#define  OC_THRESHOLD_MIN 2.0f    //过流告警阈值最小值

#define P2P_TIMER_NAME                "P2Pstatus"
#define P2P_TIMEOUT   (20*1000)   ///<  定时器时间，20S
#define TRIGGER_TIMES 3
#define APPTEST_TRIGGER_TO_EXIT_FLAG  (-1)  //apptest从触发到退出状态时，重置apptest_trigger_repeat为-1，解决退出apptest时无响应，但商用模式不需要响应apptest问题
#define APPTEST_TRIGGER_SUCCESS  1
typedef struct {
    void*        data;
    cmd_buf_t*   cmd_buff;
    link_inst_t* link_inst;
    rt_mq_t      north_mq;
}north_mgr_t;


#pragma pack(4)
typedef struct {
    char serial_number_flag;           // 空开序列号标志位
    char manufacturing_month;          // 空开出厂月份
    char manufacturing_day;            // 空开出厂日
    char breaker_specification;        // 空开规格
    char eeprom_power_loss_test[8];   // 空开EEPROM掉电保存测试
    char board_barcode[BOARD_SN_LEN];                // 单板条码
    char hardware_version[HW_VER_LEN];             // 空开硬件版本
    char breaker_system_name[32];          // 空开系统名称
    short manufacturing_day_sequence;   // 空开出厂日序号
    short manufacturing_year;           // 空开出厂年份
    float current_calibration_slope;    // 空开电流校准斜率
    float current_calibration_zero;     // 空开电流校准零点
    float bus_voltage_calibration_slope;// 空开母排电压校准斜率
    float bus_voltage_calibration_zero; // 空开母排电压校准零点
    float terminal_voltage_calibration_slope; // 空开接线端电压校准斜率
    float terminal_voltage_calibration_zero;  // 空开接线端电压校准零点
} production_info_t;
#pragma pack()


void*  init_north(void * param);
void north_comm_th(void *param);
char init_link_tab(void);
char sync_product_info_to_data_id(production_info_t* pdt_info);
char init_device_para(void);

#ifdef __cplusplus
}
#endif      //< __cplusplus

#endif      //< _603A_MAIN_NORTH_COMM_H_
