#include "data_process.h"
#include "realdata_id_in.h"
#include "para_id_in.h"
#include "para_manage.h"
#include "realdata_save.h"
#include "south_dev_common.h"
#include "msg_id.h"
#include "sps.h"
#include "para_id_in.h"
#include "para_manage.h"
#include "dev_north_dxcb_modbus.h"
#include "utils_math.h"


int deal_invalid_fan_data(unsigned short fan_no, fan_type_e fan_type)
{
    unsigned short commu_sta = 0;
    unsigned short err_code = 0;
    unsigned short invalid_int_data = 0xFFFF;
    float invalid_float_data = 6553.5;
    int fan_float_data[][2] = {
        // 内风机
        {DXCB_DATA_ID_INTERNAL_FAN_POWER, DXCB_DATA_ID_INTERNAL_FAN_CURR},
        // 外风机
        {DXCB_DATA_ID_EXTERNAL_FAN_POWER, DXCB_DATA_ID_EXTERNAL_FAN_CURR}};
    int fan_int_data[][2] = {
        // 内风机
        {DXCB_DATA_ID_INTERNAL_FAN_SPEED_PERCENT, DXCB_DATA_ID_INTERNAL_FAN_SPEED},
        // 外风机
        {DXCB_DATA_ID_EXTERNAL_FAN_SPEED_PERCENT, DXCB_DATA_ID_EXTERNAL_FAN_SPEED}};
    int fan_err_codes[] = {DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE};

    unsigned char offset = fan_type * SIG_FAN_MAX_NUM + fan_no;
    set_one_data(DCXB_TRACE_ID_FAN_COMM_FAIL_ALARM + offset, &commu_sta);

    for (int i = 0; i < sizeof(fan_float_data[fan_type]) / sizeof(int); i++)
    {
        set_one_data(fan_float_data[fan_type][i] + fan_no, &invalid_float_data);
    }

    for (int i = 0; i < sizeof(fan_int_data[fan_type]) / sizeof(int); i++)
    {
        set_one_data(fan_int_data[fan_type][i] + fan_no, &invalid_int_data);
    }

    for (int i = 0; i < ERR_CODE_NUM; i++)
    {
        set_one_data(fan_err_codes[fan_type] + (fan_no * ERR_CODE_NUM) + i, &err_code);
    }

    return SUCCESSFUL;
}



int deal_invalid_vfd_data(unsigned short vfd_no)
{
    int vfd_float_sids[] = {
        DXCB_DATA_ID_COMPRESSOR_RUN_FREQUENCY,
        DXCB_DATA_ID_COMPRESSOR_OUTPUT_FREQUENCY,
        DXCB_DATA_ID_FREQUENCY_CONVERTER_BOARD_CURR,
        DXCB_DATA_ID_FREQUENCY_CONVERTER_BOARD_TEMPERATURE};
    int vfd_int_data[] = {DXCB_DATA_ID_COMPRESSOR_RUN_SPEED};
    unsigned short invalid_int_data = 0xFFFF;
    float invalid_float_data = 6553.5;
    unsigned short err_code = 0;
    unsigned short commu_sta = 0;
    set_one_data(DCXB_TRACE_ID_VFD_COMM_FAIL_ALARM + vfd_no, &commu_sta);
    for(int i = 0; i < sizeof(vfd_float_sids) / sizeof(int); i++)
    {
        set_one_data(vfd_float_sids[i] + vfd_no, &invalid_float_data);
    }
    for(int i = 0; i < sizeof(vfd_int_data) / sizeof(int); i++)
    {
        set_one_data(vfd_int_data[i] + vfd_no, &invalid_int_data);
    }

    for (int i = 0; i < ERR_CODE_NUM; i++)
    {
        // 变频器故障码处理
        set_one_data(DXCB_DATA_ID_FREQUENCY_CONVERTER_ERR_CODE + (vfd_no * ERR_CODE_NUM) + i, &err_code);
    }

    return SUCCESSFUL;
}



int deal_invalid_south_data(unsigned short inter_fan_num, unsigned short out_fan_num, unsigned short compressor_num)
{
    // 处理风机数据
    for (int i = 0; i < SIG_FAN_MAX_NUM; i++)
    {
        if (i >= inter_fan_num)
        {
            deal_invalid_fan_data(i, INTER_FAN_TYPE);
        }
        if (i >= out_fan_num)
        {
            deal_invalid_fan_data(i, OUTER_FAN_TYPE);
        }
    }

    // 处理压缩机、变频器数据
    for (int i = 0; i < MAX_VFD_NUM; i++)
    {
        if (i >= compressor_num)
        {
            deal_invalid_vfd_data(i);
        }
    }
    return SUCCESSFUL;
}

/* Started by AICoder, pid:z5bcf19f1eq434c14fec0b30f098e14872e259a0 */
int trans_south_to_north(south_north_data_t* data_map, unsigned short vfd_no)
{
    unsigned short uint_data = 0;
    short int_data = 0;
    float float_data = 0.0;
    switch (data_map->south_data_type)
    {
        case UINT_TYPE:
            get_one_data(data_map->south_sid + vfd_no, &uint_data);
            break;
        case INT_TYPE:
            get_one_data(data_map->south_sid + vfd_no, &int_data);
            break;
        case FLOAT_TYPE:
            get_one_data(data_map->south_sid + vfd_no, &float_data);
            break;
        default:
            // 未知数据类型
            return FAILURE;
    }
    switch (data_map->north_data_type)
    {
        case UINT_TYPE:
            // 若源是浮点型，这里做类型转换（根据实际需求处理）
            uint_data = data_map->south_data_type == FLOAT_TYPE ? (unsigned short)float_data : uint_data;
            set_one_data(data_map->north_sid + vfd_no, &uint_data);
            break;
        case INT_TYPE:
            int_data = data_map->south_data_type == FLOAT_TYPE ? (short)float_data : int_data;
            set_one_data(data_map->north_sid + vfd_no, &int_data);
            break;
        case FLOAT_TYPE:
            // 若源是整型，这里做类型转换
            float_data =(data_map->south_data_type == UINT_TYPE) ? (float)uint_data : (data_map->south_data_type == INT_TYPE) ? (float)int_data : float_data;
            set_one_data(data_map->north_sid + vfd_no, &float_data);
            break;
        default:
            // 未知数据类型
            return FAILURE;
    }
    return SUCCESSFUL;
}


int deal_vfd_fault_bit_code(int north_sid, int* bit_map, int vfd_no)
{
    unsigned short fault_code = 0;
    unsigned short bit = 0;
    for(int i = 0; i < 16; i++)
    {
        if(bit_map[i] != 0xFFFFFFFF)
        {
            get_one_data(bit_map[i] + vfd_no, &bit);
        }
        else
        {
            bit = 0;
        }
        fault_code |= (bit << i);
    }
    set_one_data(north_sid, &fault_code);
    return SUCCESSFUL;
}


int deal_vfd_ams_data(unsigned short compressor_num)
{
    south_north_data_t data_map[] = {
        {DXCB_DATA_ID_FREQUENCY_CONVERTER_BOARD_CURR,        FLOAT_TYPE, DXCB_DATA_ID_VFD_COMPRESSOR_PHASE_CURR,   FLOAT_TYPE},    // 变频板电流
        {DXCB_DATA_ID_FREQUENCY_CONVERTER_BOARD_TEMPERATURE, FLOAT_TYPE, DXCB_DATA_ID_VFD_POWER_MODULE_TEMP,       INT_TYPE},     // 变频板温度
    };
    int fault_code1[] = {
                        DCXB_TRACE_ID_COMPRESSOR_OVER_CURR,
                        DCXB_TRACE_ID_AC_INPUT_OVER_CURRENT,
                        DCXB_TRACE_ID_BUS_OVERVOLTAGE_FAULT,
                        DCXB_TRACE_ID_BUS_UNDERVOLTAGE_FAULT,
                        DCXB_TRACE_ID_VFD_INPUT_OVER_VOL,
                        DCXB_TRACE_ID_VFD_INPUT_UNDER_VOL,
                        DCXB_TRACE_ID_VFD_COMPRESSOR_CONFIG_ERROR,
                        DCXB_TRACE_ID_VFD_COMPONENT_OR_DRIVE_EEPROM_INVAILD,
                        0xFFFFFFFF,
                        0xFFFFFFFF,
                        0xFFFFFFFF,
                        DCXB_TRACE_ID_VFD_POWER_MODULE_HIGH_TEMP_CURR_LIMIT_STATUS,
                        DCXB_TRACE_ID_VFD_PFC_IGBT_HIGH_TEMP,
                        DCXB_TRACE_ID_VFD_ROTOR_OUT_OF_STEP,
                        0xFFFFFFFF,
                        0xFFFFFFFF};
    int fault_code2[] = {
                        DCXB_TRACE_ID_VFD_DC_UNDER_VOL,
                        DCXB_TRACE_ID_VFD_COMPRESSOR_OVER_CURR_MEDIAN,
                        DCXB_TRACE_ID_VFD_COMPRESSOR_PHASE_CURR_LIMIT_TIMEOUT,
                        DCXB_TRACE_ID_VFD_POWR_MODULE_HIGH_TEMP_FRE_LIMIT_TIMEOUT,
                        DCXB_TRACE_ID_VFD_AC_INPUT_CURR_FRE_LIMIT_TIMEOUT,
                        DCXB_TRACE_ID_VFD_POWR_MODULE_LOW_TEMP_OR_SENSOR_INVALID,
                        DCXB_TRACE_ID_VFD_PFC_IGBT_LOW_TEMP,
                        DCXB_TRACE_ID_VFD_MODBUS_COMM_FAIL,
                        0xFFFFFFFF,
                        0xFFFFFFFF,
                        0xFFFFFFFF,
                        DCXB_TRACE_ID_VFD_POWER_MODULE_TEMP_TOO_HIGH,
                        DCXB_TRACE_ID_VFD_PFC_IGBT_TEMP_TOO_HIGH,
                        DCXB_TRACE_ID_VFD_COMM_LOSS_BETWEEN_DSP_AND_PFC,
                        DCXB_TRACE_ID_VFD_DSP_COMM_LOSS,
                        DCXB_TRACE_ID_VFD_FAILURE_LOCK};
    unsigned short uint_data = 0;
    float float_data = 0.0;
    
    for(int vfd_no = 0; vfd_no < compressor_num; vfd_no++)
    {
        for (int i = 0; i < sizeof(data_map) / sizeof(south_north_data_t); i++)
        {
            trans_south_to_north(&data_map[i], vfd_no);
        }
        // 压缩机输出频率
        get_one_data(DXCB_DATA_ID_COMPRESSOR_OUT_FREQ_VALUE + vfd_no, &float_data);
        set_one_data(DXCB_DATA_ID_COMPRESSOR_OUTPUT_FREQUENCY + vfd_no, &float_data);
        // 压缩机运行频率
        get_one_data(DXCB_DATA_ID_VFD_FEEDBACK_SPEED + vfd_no, &uint_data);
        float_data = uint_data / 120.0;
        set_one_data(DXCB_DATA_ID_COMPRESSOR_RUN_FREQUENCY + vfd_no, &float_data);
        // 压缩机运行转速
        uint_data = uint_data / 2;
        set_one_data(DXCB_DATA_ID_COMPRESSOR_RUN_SPEED + vfd_no, &uint_data);

        // 故障码处理
        deal_vfd_fault_bit_code(DXCB_DATA_ID_FREQUENCY_CONVERTER_ERR_CODE + (vfd_no * MAX_VFD_NUM), fault_code1, vfd_no);
        deal_vfd_fault_bit_code(DXCB_DATA_ID_FREQUENCY_CONVERTER_ERR_CODE + (vfd_no * MAX_VFD_NUM) + 1, fault_code2, vfd_no);
    }
    return SUCCESSFUL;
}



int deal_vfd_dfs_data(unsigned short compressor_num)
{
    south_north_data_t data_map[] = {
        {DXCB_DATA_ID_FREQUENCY_CONVERTER_BOARD_CURR,        FLOAT_TYPE, DXCB_DATA_ID_VFD_COMPRESSOR_PHASE_CURR,   FLOAT_TYPE},    // 变频板电流
        {DXCB_DATA_ID_FREQUENCY_CONVERTER_BOARD_TEMPERATURE, FLOAT_TYPE, DXCB_DATA_ID_VFD_POWER_MODULE_TEMP,       INT_TYPE},     // 变频板温度
    };
    unsigned short uint_data = 0;
    float float_data = 0.0;
    
    for(int vfd_no = 0; vfd_no < compressor_num; vfd_no++)
    {
        for (int i = 0; i < sizeof(data_map) / sizeof(south_north_data_t); i++)
        {
            trans_south_to_north(&data_map[i], vfd_no);
        }
        // 压缩机输出频率
        get_one_data(DXCB_DATA_ID_COMPRESSOR_OUT_FREQ_VALUE + vfd_no, &float_data);
        set_one_data(DXCB_DATA_ID_COMPRESSOR_OUTPUT_FREQUENCY + vfd_no, &float_data);
        // 压缩机运行频率
        get_one_data(DXCB_DATA_ID_VFD_FEEDBACK_FREQUECY + vfd_no, &float_data);
        float_data = float_data / 3.0;
        set_one_data(DXCB_DATA_ID_COMPRESSOR_RUN_FREQUENCY +  vfd_no, &float_data);
        // 压缩机错误码
        get_one_data(DXCB_DATA_ID_VFD_ALARM_CODE + vfd_no, &uint_data);
        set_one_data(DXCB_DATA_ID_FREQUENCY_CONVERTER_ERR_CODE + (vfd_no * MAX_VFD_NUM), &uint_data);
        // 压缩机警告码
        get_one_data(DXCB_DATA_ID_VFD_WARNING_CODE + vfd_no, &uint_data);
        set_one_data(DXCB_DATA_ID_FREQUENCY_CONVERTER_ERR_CODE + (vfd_no * MAX_VFD_NUM) + 1, &uint_data);
    }
    return SUCCESSFUL;
}

int deal_vfd_hc_data(unsigned short compressor_num)
{
    float float_data = 0.0;
    unsigned short uint_data = 0;
    for(int vfd_no = 0; vfd_no < compressor_num; vfd_no++)
    {
        get_one_data(DXCB_DATA_ID_COMPRESSOR_OUT_FREQ_VALUE + vfd_no, &float_data);
        set_one_data(DXCB_DATA_ID_COMPRESSOR_OUTPUT_FREQUENCY + vfd_no, &float_data);
        get_one_data(DXCB_DATA_ID_COMPRESSOR_RUN_SPEED + vfd_no, &uint_data);
        float_data = uint_data / 60;
        set_one_data(DXCB_DATA_ID_COMPRESSOR_RUN_FREQUENCY + vfd_no, &float_data);
    }
    return SUCCESSFUL;
}

int deal_vfd_data(unsigned short compressor_num, unsigned short compressor_brand)
{
    switch (compressor_brand)
    {
        case VFD_AMS_BRAND:
            deal_vfd_ams_data(compressor_num);
            break;
        case VFD_DFS_BRAND:
            deal_vfd_dfs_data(compressor_num);
            break;
        case VFD_HC_BRAND:
            deal_vfd_hc_data(compressor_num);
            break;
        default:
            return FAILURE;
    }
    return SUCCESSFUL;
}



int deal_fault_bit_code(int north_sid, code_tab_t* bit_map, int fan_no, unsigned short id_offset)
{
    unsigned short fault_code = 0;
    unsigned short bit = 0;
    for(int i = 0; i < 16; i++)
    {
        if(bit_map[i].sid != 0xFFFFFFFF)
        {
            get_one_data(bit_map[i].sid + bit_map[i].dev_offset*(fan_no + id_offset), &bit);
        }
        else
        {
            bit = 0;
        }
        fault_code |= (bit << i);
    }
    set_one_data(north_sid, &fault_code);
    return SUCCESSFUL;
}

int avc_code(int code_id, unsigned short id_offset, int i)
{
    code_tab_t code_tab[] = 
    {
        {DXCB_DATA_ID_OC_ALARM_FAULT_CODE, 8},
        {DXCB_DATA_ID_OC_ALARM_FAULT_CODE+1, 8},
        {DXCB_DATA_ID_OC_ALARM_FAULT_CODE+2, 8},
        {DXCB_DATA_ID_OC_ALARM_FAULT_CODE+3, 8},
        {DXCB_DATA_ID_OC_ALARM_FAULT_CODE+4, 8},
        {DXCB_DATA_ID_OC_ALARM_FAULT_CODE+5, 8},
        {DXCB_DATA_ID_OC_ALARM_FAULT_CODE+6, 8},
        {DXCB_DATA_ID_OC_ALARM_FAULT_CODE+7, 8},

        {DXCB_DATA_ID_OC_ALARM_OVER_LIMIT, 1},
        {DXCB_DATA_ID_OC_ALARM_DIRECTION, 1},
        {0xFFFFFFFF, 1},
        {DXCB_DATA_ID_OC_ALARM_LIFECYCLE, 1},
        {DXCB_DATA_ID_OC_ALARM_VIBRATION, 1},
        {DXCB_DATA_ID_OC_ALARM_APP_SYS, 1},
        {DXCB_DATA_ID_OC_ALARM_MOTOR_SYS, 1},
        {0xFFFFFFFF, 1},
    };

    deal_fault_bit_code(code_id + 4*i, code_tab, i, id_offset);

    return SUCCESSFUL;
}

int slb_code(int code_id, unsigned short id_offset, int i)
{
    code_tab_t code_tab_1[] = 
    {
        {DXCB_DATA_ID_STOP_STATUS, 1},
        {DXCB_DATA_ID_TEMP_MANAGE, 1},
        {DXCB_DATA_ID_IGBT_COOL_PHASE_STATUS, 1},
        {DXCB_DATA_ID_IN_SYS_ERROR, 1},
        {DXCB_DATA_ID_DIR_RATATION_ERROR, 1},
        {DXCB_DATA_ID_BYPASS_TEMP_MANAGE, 1},
        {DXCB_DATA_ID_FIELD_WEAK, 1},
        {DXCB_DATA_ID_DC_CURR_LIMIT, 1},

        {DXCB_DATA_ID_D1_STATUS, 1},
        {DXCB_DATA_ID_E1_STATUS, 1},
        {DXCB_DATA_ID_K1_STATUS, 1},
        {DXCB_DATA_ID_DC_LINK_OVER, 1},
        {DXCB_DATA_ID_IGBT_TEMP_ALARM, 1},
        {DXCB_DATA_ID_IN_TEMP_ALARM, 1},
        {DXCB_DATA_ID_DIR_CHANGE_ACTIVE, 1},
        {DXCB_DATA_ID_FAN_BAD, 1},
    };

    code_tab_t code_tab_2[] = 
    {
        {DXCB_DATA_ID_OC_ALARM_FAULT_CODE, 8},
        {DXCB_DATA_ID_OC_ALARM_FAULT_CODE+1, 8},
        {DXCB_DATA_ID_OC_ALARM_FAULT_CODE+2, 8},
        {DXCB_DATA_ID_OC_ALARM_FAULT_CODE+3, 8},
        {DXCB_DATA_ID_OC_ALARM_FAULT_CODE+4, 8},
        {DXCB_DATA_ID_OC_ALARM_FAULT_CODE+5, 8},
        {DXCB_DATA_ID_OC_ALARM_FAULT_CODE+6, 8},
        {DXCB_DATA_ID_OC_ALARM_FAULT_CODE+7, 8},

        {DXCB_DATA_ID_OC_ALARM_OVER_LIMIT, 1},
        {DXCB_DATA_ID_OC_ALARM_DIRECTION, 1},
        {0xFFFFFFFF, 1},
        {DXCB_DATA_ID_OC_ALARM_LIFECYCLE, 1},
        {DXCB_DATA_ID_OC_ALARM_VIBRATION, 1},
        {DXCB_DATA_ID_OC_ALARM_APP_SYS, 1},
        {DXCB_DATA_ID_OC_ALARM_MOTOR_SYS, 1},
        {0xFFFFFFFF, 1},
    };

    code_tab_t code_tab_3[] = 
    {
        {DXCB_DATA_ID_IGBT_ERROR, 1},
        {DXCB_DATA_ID_EARTH_ERROR, 1},
        {DXCB_DATA_ID_DC_VOL_HIGH},
        {DXCB_DATA_ID_DC_VOL_LOW, 1},
        {DXCB_DATA_ID_INPUT_VOL_HIGH, 1},
        {DXCB_DATA_ID_INPUT_VOL_LOW, 1},
        {DXCB_DATA_ID_MAIS_FAIL, 1},
        {0xFFFFFFFF, 1},

        {DXCB_DATA_ID_POSITION_ROTOR, 1},
        {DXCB_DATA_ID_MOTOR_BLOCK, 1},
        {DXCB_DATA_ID_PEAK_CURR, 1},
        {0xFFFFFFFF, 1},
        {DXCB_DATA_ID_SAFE_SHUTDOWN, 1},
        {DXCB_DATA_ID_TEMP_ERROR, 1},
        {DXCB_DATA_ID_MOTOR_START, 1},
        {DXCB_DATA_ID_COM_ERROR, 1},
    };

    code_tab_t code_tab_4[] = 
    {
        {DXCB_DATA_ID_IDENTIFIER_ERROR_REASON, 8},
        {DXCB_DATA_ID_IDENTIFIER_ERROR_REASON+1, 8},
        {DXCB_DATA_ID_IDENTIFIER_ERROR_REASON+2, 8},
        {DXCB_DATA_ID_IDENTIFIER_ERROR_REASON+3, 8},
        {DXCB_DATA_ID_IDENTIFIER_ERROR_REASON+4, 8},
        {DXCB_DATA_ID_IDENTIFIER_ERROR_REASON+5, 8},
        {DXCB_DATA_ID_IDENTIFIER_ERROR_REASON+6, 8},
        {DXCB_DATA_ID_IDENTIFIER_ERROR_REASON+7, 8},

        {DXCB_DATA_ID_LIMIT, 1},
        {DXCB_DATA_ID_ROTAT_DIR, 1},
        {0xFFFFFFFF, 1},
        {DXCB_DATA_ID_LIFETIME_ERROR, 1},
        {DXCB_DATA_ID_VIBRATION_ERROR, 1},
        {DXCB_DATA_ID_APP_SYS_ERROR, 1},
        {DXCB_DATA_ID_MOTOR_CTRL_SYS_ERROR, 1},
        {0xFFFFFFFF, 1},
    };

    deal_fault_bit_code(code_id + 4*i, code_tab_1, i, id_offset);
    deal_fault_bit_code(code_id + 1 + 4*i, code_tab_2, i, id_offset);
    deal_fault_bit_code(code_id + 2 + 4*i, code_tab_3, i, id_offset);
    deal_fault_bit_code(code_id + 3 + 4*i, code_tab_4, i, id_offset);
    return SUCCESSFUL;
}



/**
 * @brief 处理单个风机的数据同步（内风机或外风机）
 * @param fan_count 风机数量
 * @param fan_brand 风机品牌
 * @param rated_speed 额定转速
 * @param id_offset 数据ID偏移量（内风机为0，外风机为EXTERNAL_FAN_ID_OFFSET）
 * @param speed_percent_id 转速百分比数据ID基址
 * @param speed_id 转速数据ID基址
 * @param power_id 功率数据ID基址
 * @param curr_id 电流数据ID基址
 */
static void process_fan_data(unsigned short fan_count, 
                           unsigned char fan_brand,
                           unsigned short rated_speed,
                           unsigned short id_offset,
                           unsigned int speed_percent_id,
                           unsigned int speed_id,
                           unsigned int power_id,
                           unsigned int curr_id,
                           unsigned int code_id)
{
    float current = 0.0f;
    for (unsigned short i = 0; i < fan_count; i++)
    {
        unsigned short actual_speed = 0;
        unsigned short speed_pct = 0;
        
        // 处理转速和转速百分比
        if (FAN_SLB_BRAND == fan_brand)
        {
            get_one_data(DXCB_DATA_ID_ACTUAL_SPEED + id_offset + i, &actual_speed);
            // 计算百分比时使用浮点数避免精度丢失，再转换为整数
            if(rated_speed != 0)
            {
                speed_pct = (unsigned short)((actual_speed * 100.0f) / rated_speed);;
            }
            set_one_data(speed_percent_id + i, &speed_pct);
            set_one_data(speed_id + i, &actual_speed);
        }
        else
        {
            get_one_data(DXCB_DATA_ID_FAN_SPEED_PERCENT + id_offset + i, &speed_pct);
            if(FAN_EBM_BRAND == fan_brand || FAN_FSD_BRAND == fan_brand)
            {
                speed_pct = speed_pct / 64000.0 * 100;
            }
            else if(FAN_AVC_BRAND == fan_brand)
            {
                speed_pct = speed_pct / 10.0;
            }
            
            set_one_data(speed_percent_id + i, &speed_pct);
            // 计算实际转速，使用浮点数确保精度
            actual_speed = (unsigned short)((speed_pct * rated_speed) / 100.0);
            set_one_data(speed_id + i, &actual_speed);
        }

        // 处理功率数据
        unsigned short actual_power_short = 0;
        float actual_power_float = 0.0f;
        get_one_data(DXCB_DATA_ID_POWER + id_offset + i, &actual_power_short);
        actual_power_float = (float)actual_power_short;
        set_one_data(power_id + i, &actual_power_float);

        // 处理电流数据（仅AVC和SLB品牌）
        if (FAN_AVC_BRAND == fan_brand || FAN_SLB_BRAND == fan_brand)
        {
            get_one_data(DXCB_DATA_ID_MOTOR_CURR + id_offset + i, &current);
            set_one_data(curr_id + i, &current);
        }
        else
        {
            current = 0.0f;
            set_one_data(curr_id + i, &current);
        }

        // 错误码
        if(FAN_EBM_BRAND == fan_brand || FAN_FSD_BRAND == fan_brand)
        {
            unsigned short code = 0;
            get_one_data(DXCB_DATA_ID_TOTAL_ALARM + id_offset + i, &code);
            set_one_data(code_id + i*4, &code);
        }
        else if(FAN_AVC_BRAND == fan_brand)
        {
            avc_code(code_id, id_offset, i);
        }
        else
        {
            slb_code(code_id, id_offset, i);
        }
    }
}

/**
 * @brief 同步内外风机数据（转速、功率、电流等）
 */
int fan_sync_data(unsigned short internal_fan_count, unsigned short external_fan_count)
{
    // 内风机参数
    unsigned char internal_fan_brand = 0;
    unsigned short internal_rated_speed = 0;
    
    // 外风机参数
    unsigned char external_fan_brand = 0;
    unsigned short external_rated_speed = 0;

    // 获取内风机配置参数
    get_one_para(DXCB_PARA_ID_FAN_BRAND_OFFSET, &internal_fan_brand);
    get_one_para(DXCB_PARA_ID_INTERNAL_FAN_MAX_SPEED_OFFSET, &internal_rated_speed);

    // 获取外风机配置参数
    get_one_para(DXCB_PARA_ID_FAN_BRAND_OFFSET + 1, &external_fan_brand);
    get_one_para(DXCB_PARA_ID_EXTERNAL_FAN_MAX_SPEED_OFFSET, &external_rated_speed);

    // 处理内风机数据
    process_fan_data(internal_fan_count,
                    internal_fan_brand,
                    internal_rated_speed,
                    0,  // 内风机ID无偏移
                    DXCB_DATA_ID_INTERNAL_FAN_SPEED_PERCENT,
                    DXCB_DATA_ID_INTERNAL_FAN_SPEED,
                    DXCB_DATA_ID_INTERNAL_FAN_POWER,
                    DXCB_DATA_ID_INTERNAL_FAN_CURR,
                    DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE);

    // 处理外风机数据
    process_fan_data(external_fan_count,
                    external_fan_brand,
                    external_rated_speed,
                    SIG_FAN_MAX_NUM,  // 外风机ID偏移
                    DXCB_DATA_ID_EXTERNAL_FAN_SPEED_PERCENT,
                    DXCB_DATA_ID_EXTERNAL_FAN_SPEED,
                    DXCB_DATA_ID_EXTERNAL_FAN_POWER,
                    DXCB_DATA_ID_EXTERNAL_FAN_CURR,
                    DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE);
    
    return SUCCESSFUL;
}

/* Started by AICoder, pid:2fee9t7764if09614c46086200067f1238d33eb8 */
int init_usart_commu_para()
{
    usart_para_t usart_para[] = {
        {DXCB_PARA_ID_BAUD_RATE_OFFSET, DXCB_PARA_ID_PARITY_BIT_OFFSET, NORTH_EDU_UART_NAME},
        {DXCB_PARA_ID_FAN_BAUD_RATE_OFFSET, DXCB_PARA_ID_FAN_PARITY_BIT_OFFSET, SOUTH_FAN_UART_NAME},
        {DXCB_PARA_ID_VFD_BAUD_RATE_OFFSET, DXCB_PARA_ID_VFD_PARITY_BIT_OFFSET, SOUTH_VFD_UART_NAME},
    };
    for(int i = 0; i < sizeof(usart_para)/sizeof(usart_para_t); i++)
    {
        update_south_commu_para(usart_para[i].usart_name, usart_para[i].baud_rate_sid, usart_para[i].parity_bit_sid);
    }
    return SUCCESSFUL;
}
/* Ended by AICoder, pid:2fee9t7764if09614c46086200067f1238d33eb8 */