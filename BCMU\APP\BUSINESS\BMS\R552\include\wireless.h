/**************************************************************************
* Copyright (C) 2010-2011, ZTE Corporation.
* 版权信息：（C）2011-2012，中兴通讯股份有限公司动力开发部版权所有
* 系统名称：ZXDUPA-PMSA V2.1平台软件
* 文件名称：wireless.h
* 文件说明：GPRS通讯模块头文件
* 作    者：王威
* 版本信息：V2.1
* 设计日期：2011-04-09
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/
#ifndef _GPRS_H
#define _GPRS_H

#ifdef __cplusplus
extern "C" {
#endif

#define PI              3.14159265358979f
#define EARTH_RADIUS    6371.004    //地球半径km

typedef struct
{
    int            iGpsLocated;       // 是否定位上
    unsigned short wYear;             // 年
    unsigned char  ucMonth;           // 月
    unsigned char  ucDay;             // 日 
    unsigned char  ucHour;            // 时
    unsigned char  ucMinute;          // 分
    unsigned char  ucSecond;          // 秒

    unsigned char  ucLongDirect;      // 经度方向
    float          fLongtitude;       // 经度
    unsigned char  ucLatiDirect;      // 纬度方向
    float          fLatitude;         // 纬度
    float          fSpeed;            // 速度
    float          fAzimuth;          // 方位角
    unsigned char  ucStarNum;         // 卫星数量
}T_GpsData;

int register_4g_func(void);
#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // _GPRS_H

