/**
 * @file     device_type.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2017-04-19
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */

#ifndef _ACMU_PROTOCOL_1104_H
#define _ACMU_PROTOCOL_1104_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "device_type.h"

/*1104 protocol的命令唯一标识定义 */
#define ACMU_GET_SYS_TIME                   1   ///<  获取系统时间
#define ACMU_SET_SYS_TIME                   2   ///<  设置系统时间
#define ACMU_GET_COMMPROTOCOL_VER           3   ///<  获取通信协议版本号
#define ACMU_GET_DEVICE_ADDR                4   ///<  获取设备地址
#define ACMU_GET_AC_ANALOG_FLOAT            5   ///<  获取交流设备模拟量（浮点）
#define ACMU_GET_AC_SWITCH                  6   ///<  获取交流设备开关量
#define ACMU_GET_ACEMFACTORY_INFO           7   ///<  获取交流电表厂家信息
#define ACMU_GET_ENV_ANALOG_FLOAT           8   ///<  获取环境设备模拟量（浮点）
#define ACMU_GET_ENV_SWITCH                 9   ///<  获取环境设备开关量
#define ACMU_GET_ENV_ALM_DATA               10  ///<  获取环境设备告警数据
#define ACMU_GET_ENV_PARA                   11  ///<  获取环境设备参数
#define ACMU_SET_ENV_PARA                   12  ///<  设置环境设备参数
#define ACMU_GET_AC_PARA                    13  ///<  获取交流单元参数量
#define ACMU_SET_AC_PARA                    14  ///<  设置交流单元参数量
#define ACMU_GET_AC_ALM_DATA                15  ///<  获取交流单元告警数据

/* cid2 功能码定义 */
#define ACMU_CMD_GET_SYS_TIME                    0x4D    ///<  获取系统时间
#define ACMU_CMD_SET_SYS_TIME                    0x4E    ///<  设置系统时间
#define ACMU_CMD_GET_COMMPROTOCOL_VER            0x4F    ///<  获取通信协议版本号
#define ACMU_CMD_GET_DEVICE_ADDR                 0x50    ///<  获取设备地址
#define ACMU_CMD_GET_MONITORFACTORY_INFO         0x51    ///<  获取监控设备厂家信息
#define ACMU_CMD_GET_ANALOG_FLOAT                0x41    ///<  获取模拟量（浮点）
#define ACMU_CMD_GET_SWITCH                      0x43    ///<  获取开关量
#define ACMU_CMD_GET_ACEMFACTORY_INFO            0xE1    ///<  获取交流电表厂家信息
#define ACMU_CMD_GET_ALM                         0x44    ///<  获取告警
#define ACMU_CMD_GET_PARA                        0x46    ///<  获取参数
#define ACMU_CMD_SET_PARA                        0x48    ///<  设置参数

#define ENV_FAULT                           0x04  /* 环境数字量告警 */

dev_type_t* init_dev_acmu_1104(void);
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _ACMU_PROTOCOL_1104_H

