#ifndef _BMS_1363_H_
#define _BMS_1363_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "cmd.h"
#include "device_type.h"


// #define NORTH_CAN1_DELAY    10  // unit:ms
// #define CAN1_R_BUFF_LEN               64     ///<  接收缓冲区长度
// #define CAN1_S_BUFF_LEN               64     ///<  发送缓冲区长度

/*1363的命令唯一标识定义 */
#define GET_ANA_INT_DATA        1           //<获取模拟量量化后数据（定点数）
#define GET_DIG_DATA            2           //<获取状态量
#define GET_ALM_DATA            3           //<获取告警量
#define REC_CONTROL             4           //<遥控命令
#define GET_SYSPARA_DATA        5           //<获取系统参数
#define SET_SYSPARA_DATA        6           //<设定系统参数
#define GET_HIS_DATA            7           //获取系统历史数据
#define GET_HIS_ALM_DATA        8           //<获取历史告警
#define GET_TIME_DATA           9           //<获取监测模块时间
#define SET_TIME_DATA           10           //<设置监测模块时间
#define GET_FACT_DATA           11           //<获取设备（监测模块）厂家信息
#define GET_SPE_PARA            12           //<获取特定参数
#define SET_SPE_PARA            13           //<设置特定参数
#define GET_ALM_LEV             14           //<获取告警级别
#define SET_ALM_LEV             15           //<设置告警级别
#define GET_HIS_OPE_DATA        16           //<获取历史操作记录
#define GET_EXTREME_REC         17           //<获取极值记录
#define GET_CELL_STA_REC        18           //<获取电芯统计记录
#define SET_SPE_DATA            19           //<设置特殊数据


/*1363的cid2 功能码定义 */
#define CMD_GET_ANA_INT_DATA        0x42           //<获取模拟量量化后数据（定点数）
#define CMD_GET_DIG_DATA            0x43           //<获取状态量
#define CMD_GET_ALM_DATA            0x44           //<获取告警量
#define CMD_CONTROL                 0x45           //<遥控命令
#define CMD_GET_SYSPARA_DATA        0x47           //<获取系统参数
#define CMD_SET_SYSPARA_DATA        0x49           //<设定系统参数
#define CMD_GET_HIS_DATA            0x4B           //获取系统历史数据
#define CMD_GET_HIS_ALM_DATA        0x4C           //<获取历史告警
#define CMD_GET_TIME_DATA           0x4D           //<获取监测模块时间
#define CMD_SET_TIME_DATA           0x4E           //<设置监测模块时间
#define CMD_GET_FACT_DATA           0x51           //<获取设备（监测模块）厂家信息
#define CMD_GET_SPE_PARA            0x80           //<获取特定参数
#define CMD_SET_SPE_PARA            0x81           //<设置特定参数
#define CMD_GET_ALM_LEV             0x82           //<获取告警级别
#define CMD_SET_ALM_LEV             0x83           //<设置告警级别
#define CMD_GET_HIS_OPE_DATA        0x84           //<获取历史操作记录
#define CMD_GET_EXTREME_REC         0x85           //<获取极值记录
#define CMD_GET_CELL_STA_REC        0x86           //<获取电芯统计记录
#define CMD_SET_SPE_DATA            0x87           //<设置特殊数据


/*cmd_type定义*/
#define RES_DEFAULT_PARA_CMD                 0x11    //恢复缺省参数
#define START_ADDR_COMP_CMD                  0x12    //启动地址竞争
#define REL_LOCK_CMD                         0x13    //解除闭锁
#define UNLOCK_LOSS_ALM_CMD                  0x14    //解锁丢失告警
#define ENTER_DEFENCE_CMD                    0x15    //布防
#define EXIT_DEFENCE_CMD                     0x16    //撤防
#define START_HEATER_CMD                     0x17    //启动加热器加热
#define STOP_HEATER_CMD                      0x18    //停止加热器加热

#define ANA_DATA_FLAG_DEF_VALUE  0       ///模拟量数据flag默认值

typedef struct {
    unsigned char  cmd_type;
    unsigned int   ctrl_id;
    unsigned short ctrl_status;
    unsigned char  ctrl_cmd_id;
    unsigned char  is_south_ctrl_cmd;
    char  info[20];
    void (*func)();
}ctrl_cmd_1363_t;


dev_type_t* init_dev_bms_can(void);
void bms_register_cmd_table();


#ifdef __cplusplus
}
#endif      //< __cplusplus

#endif      //< _COMM_CAN_H_
