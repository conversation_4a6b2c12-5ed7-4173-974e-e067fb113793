#include <string.h>
#include <stdlib.h>
#include <rtthread.h>
#include "protocol_bottom_comm.h"
#include "utils_thread.h"
#include "device_type.h"
#include "msg.h"
#include "cmd.h"
#include "north_main.h"
#include "data_type.h"
#include "sps.h"
#include "UIB.pb.h"
#include "pb_decode.h"
#include "pin_define.h"
#include "sample.h"
#include "pin_ctrl.h"
#include "pwm_ctrl.h"
#include "utils_server.h"
#include "storage.h"
#include "dev_60x.h"
#include "utils_string.h"

Static int link_cmd_pack(void* dev_inst, void *cmd_buf);
Static int get_ai_pack(void* dev_inst, void *cmd_buf);
Static int get_di_pack(void* dev_inst, void *cmd_buf);
Static int get_barcode_pack(void* dev_inst, void *cmd_buf);
Static int do_ctrl_parse(void* dev_inst, void *cmd_buf);
Static int set_barcode_parse(void* dev_inst, void *cmd_buf);
Static int set_fault_do_parse(void* dev_inst, void *cmd_buf);
Static int set_fan_parse(void* dev_inst, void *cmd_buf);
Static int app_pb_data_decode(cmd_buf_t* cmd_buf, const pb_field_t fields[], void *dest_struct);
Static int app_pb_data_encode(cmd_buf_t* cmd_buf, const pb_field_t fields[], void *dest_struct);
Static void init_csu_para(void);
Static void communication_timeout_func();
Static int judge_para_invalid(unsigned char* temp_pointer, unsigned char size_t);

#define time_wait_tick  10
#define commun_timeout_cnt  1200
Static csu_604_para_t csu_604a_para = {0};
static dev_inst_t* dev_603a;
Static unsigned int reset_counter = 0;

static dev_init_t dev_init_tab[] = {
	#ifdef USING_DEVICE_60XA_MAIN
    {DEV_CSU, init_dev_603a_main, NULL, NULL},
    #endif

	#ifdef USING_DEVICE_60XA_UIB01
    {DEV_CSU, init_dev_603a_uib01, NULL, NULL},
    #endif
};

#ifdef USING_DEVICE_60XA_UIB01
static facinfo_response factory_info = {
"GD_UIB", "V3.00.00.00", {2024, 9, 3},
};

static cmd_handle_register_t s_cmd_handle[] = {
    {DEV_CSU,  UIB_LINK,          CMD_TYPE_NO_POLL,    NULL, link_cmd_pack},
    {DEV_CSU,  UIB_GET_AI,        CMD_TYPE_NO_POLL,    NULL, get_ai_pack},
    {DEV_CSU,  UIB_GET_DI,        CMD_TYPE_NO_POLL,    NULL, get_di_pack},
    {DEV_CSU,  UIB_GET_BARCODE,   CMD_TYPE_NO_POLL,    NULL, get_barcode_pack},
    {DEV_CSU,  UIB_DO_CTRL,       CMD_TYPE_NO_POLL,    do_ctrl_parse, NULL},
    {DEV_CSU,  UIB_SET_BARCODE,   CMD_TYPE_NO_POLL,    set_barcode_parse, NULL},
    {DEV_CSU,  UIB_SET_CSU_FAULT_DO, CMD_TYPE_NO_POLL, set_fault_do_parse, NULL},
    {DEV_CSU,  UIB_SET_FAN,       CMD_TYPE_NO_POLL,    set_fan_parse, NULL},
};
#else
static facinfo_response factory_info = {
"GD_IDDB", "V3.00.00.00", {2024, 9, 3},
};

static cmd_handle_register_t s_cmd_handle[] = {
    {DEV_CSU,  UIB_LINK,          CMD_TYPE_NO_POLL,    NULL, link_cmd_pack},
    {DEV_CSU,  UIB_GET_AI,        CMD_TYPE_NO_POLL,    NULL, get_ai_pack},
    {DEV_CSU,  UIB_GET_DI,        CMD_TYPE_NO_POLL,    NULL, get_di_pack},
    {DEV_CSU,  UIB_GET_BARCODE,   CMD_TYPE_NO_POLL,    NULL, get_barcode_pack},
    {DEV_CSU,  UIB_DO_CTRL,       CMD_TYPE_NO_POLL,    do_ctrl_parse, NULL},
    {DEV_CSU,  UIB_SET_BARCODE,   CMD_TYPE_NO_POLL,    set_barcode_parse, NULL},
    {DEV_CSU,  UIB_SET_CSU_FAULT_DO, CMD_TYPE_NO_POLL, set_fault_do_parse, NULL},
    // // {dev_603a_MAIN,  UIB_SET_FAN,       CMD_TYPE_NO_POLL,    set_fan_parse, NULL}, // IDDB中无PWM控制输出需求
};
#endif

static rt_base_t do_relay[] = {
    #ifdef USING_DEVICE_60XA_UIB01
        PIN_UIB01_RLY1_OUT,
        PIN_UIB01_RLY2_OUT,
        PIN_UIB01_RLY3_OUT,
        PIN_UIB01_RLY4_OUT,
        PIN_UIB01_RLY5_OUT,
        PIN_UIB01_RLY6_OUT,
        PIN_UIB01_RLY7_OUT,
        PIN_UIB01_RLY8_OUT,
    #else
        CSU_FAULT_DO_PIN,
    #endif
};

Static void communication_timeout_func()
{
    unsigned int do_index_t = csu_604a_para.csu_fault_index;
    if (do_index_t > sizeof(do_relay)/sizeof(rt_base_t) || do_index_t == 0)
        return ;
    rt_base_t do_pin_t = do_relay[do_index_t-1];
    rt_pin_mode(do_pin_t, PIN_MODE_OUTPUT);
    rt_pin_write(do_pin_t, !(csu_604a_para.csu_fault_state));
    return ;
}


Static north_mgr_t * init_thread_data(link_inst_t* link_inst, unsigned char mod_id) {
    north_mgr_t* north_mgr = NULL;

    north_mgr = malloc(sizeof(north_mgr_t));
    if (north_mgr == NULL)
        return NULL;
    
    north_mgr->cmd_buff = malloc(sizeof(cmd_buf_t));
    if (north_mgr->cmd_buff == NULL) {
        goto NORTH_MGR_FREE;
    }
    
    north_mgr->north_mq = init_msg_queue(mod_id);
    if (north_mgr->north_mq == NULL) {
        goto CMD_BUFF_FREE;
    }

    north_mgr->link_inst = link_inst;
    return north_mgr;
 
CMD_BUFF_FREE:
    free(north_mgr->cmd_buff);
     
NORTH_MGR_FREE:
    free(north_mgr);
    return NULL;
}

Static msg_map north_msg_map[] =
{
         {0,NULL},//临时添加解决编译问题
};


/* 设备初始化*/
char init_dev_init_tab(void)
{
    char ret = 0;
    ret = init_dev_tab(dev_init_tab, sizeof(dev_init_tab)/sizeof(dev_init_tab[0]));
    return ret;
}



/* 北向初始化*/
void*  init_north(void * param) {
    server_info_t *server_info = (server_info_t *)param;
    north_mgr_t* north_mgr = NULL;
    unsigned char i, src_addr_t;
    src_addr_t = get_src_addr(LOCAL_DEV_TYPE);
    dev_603a = init_dev_inst(DEV_CSU);
    if(dev_603a == NULL)
    {
        return NULL;
    }

    set_host_addr(src_addr_t);
    register_server_msg_map(north_msg_map, server_info);
    north_mgr = init_thread_data(dev_603a->dev_type->link_inst, MOD_60X_MAIN_COMM);
    for(i = 0; i < sizeof(s_cmd_handle)/sizeof(s_cmd_handle[0]); i++) {
        register_cmd_handle(&s_cmd_handle[i]);
    }
    init_csu_para();
    return north_mgr;
}

/* 604A_MAIN收发线程 */
void north_comm_th(void *param) {
    unsigned short i = 0;
    north_mgr_t* north_mgr = (north_mgr_t*)param;
    module_msg_t msg;
    unsigned char time_delay = 50;
    int data_len;
    cmd_buf_t* cmd_buff;
    int result = FAILURE;

    rt_device_t g_serial;
    rt_uint8_t byte;

    while (is_running(TRUE)) {
        rt_memset_s(&msg, sizeof(module_msg_t),0x00, sizeof(module_msg_t));
        reset_counter++;
        if (reset_counter > commun_timeout_cnt) {  // total_time = commun_timeout_cnt * time_delay
            communication_timeout_func();
            reset_counter = 0;
        }
        
        if(RT_EOK == rt_sem_take(&(north_mgr->link_inst->rx_sem), THREAD_TICK)){
            if (SUCCESSFUL == cmd_recv(dev_603a, north_mgr->cmd_buff)) {    
                reset_counter = 0;
                cmd_send(dev_603a, north_mgr->cmd_buff);     
            }
            continue;
        }
        rt_thread_mdelay(time_delay);
    }

}


Static int link_cmd_pack(void* dev_inst, void *cmd_buf) {
    facinfo_response msg;
    pb_ostream_t stream;
    cmd_buf_t* s_cmd_buf = (cmd_buf_t*)cmd_buf;
    dev_inst_t* device = (dev_inst_t*)dev_inst;

    rt_memset_s(&msg, sizeof(msg), 0, sizeof(msg));
    rt_memcpy_s(&msg, sizeof(facinfo_response), &factory_info, sizeof(facinfo_response));
    stream = pb_ostream_from_buffer(s_cmd_buf->buf, device->dev_type->send_buff_len);
    if (pb_encode(&stream, facinfo_response_fields, &msg)) {
         s_cmd_buf->data_len = (unsigned int)stream.bytes_written;
    }
   return SUCCESSFUL;
}

Static int get_ai_pack(void* dev_inst, void *cmd_buf) {
    realdata_AI_response msg;
    pb_ostream_t stream;
    cmd_buf_t* s_cmd_buf = (cmd_buf_t*)cmd_buf;

    rt_memset_s(&msg, sizeof(msg), 0, sizeof(msg));
    msg.AI_count = get_analog_data((int *)msg.AI, (unsigned short)sizeof(msg.AI));
    app_pb_data_encode(s_cmd_buf, realdata_AI_response_fields, &msg);

    return SUCCESSFUL;
}

Static int get_di_pack(void* dev_inst, void *cmd_buf) {
    realdata_DI_response msg;
    pb_ostream_t stream;
    cmd_buf_t* s_cmd_buf = (cmd_buf_t*)cmd_buf;

    rt_memset_s(&msg, sizeof(msg), 0, sizeof(msg));
    msg.DI_count = get_digital_data((int *)msg.DI, (unsigned short)sizeof(msg.DI));
    app_pb_data_encode(s_cmd_buf, realdata_DI_response_fields, &msg);

    return SUCCESSFUL;
}

Static int get_barcode_pack(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* s_cmd_buf = (cmd_buf_t*)cmd_buf;
    s_cmd_buf->data_len = sizeof(csu_604a_para.barcode);
    memcpy_s(s_cmd_buf->buf, sizeof(csu_604a_para.barcode), &csu_604a_para.barcode[0], sizeof(csu_604a_para.barcode));
    return SUCCESSFUL;
}

Static int do_ctrl_parse(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* fault_do = (cmd_buf_t*)cmd_buf;
    setDO_request msg;
    unsigned char i = 0;
    unsigned char set_do_num = 0;
  
    if (app_pb_data_decode((cmd_buf_t*)cmd_buf, setDO_request_fields, &msg) != SUCCESSFUL) {
        fault_do->rtn = BOTTOM_RTN_DATA_ERROR;
        return SUCCESSFUL;
    }
   
    set_do_num = msg.DO_count < sizeof(do_relay) ? msg.DO_count : sizeof(do_relay);
    for (i = 0; i < set_do_num; i++) {
        set_pin(do_relay[i], msg.DO[i]);
    }
    return SUCCESSFUL;
}

Static int set_barcode_parse(void* dev_inst, void *cmd_buf) {
    part_data_t data;
    unsigned int init_offset = 0;

    cmd_buf_t* barcode = (cmd_buf_t*)cmd_buf;
    if(0 == strcmp((char *)csu_604a_para.barcode, barcode->buf))
        return SUCCESSFUL;
    rt_snprintf(&csu_604a_para.barcode[0], barcode->data_len+1, "%s", barcode->buf);
    memcpy_s(&data.name[0], sizeof(data.name), SYSPARA_PART, sizeof(SYSPARA_PART));
    data.buff = (unsigned char*)&csu_604a_para;
    data.offset = init_offset;
    data.len = sizeof(csu_604_para_t);

    if(SUCCESSFUL != storage_process(&data, erase_write_opr)){
        barcode->rtn = BOTTOM_RTN_DATA_ERROR;
    }
    return SUCCESSFUL;
}

Static int set_fault_do_parse(void* dev_inst, void *cmd_buf) {
    part_data_t data;
    unsigned int init_offset = 0;
    cmd_buf_t* fault_do = (cmd_buf_t*)cmd_buf;
    unsigned char DO_msg[2] = {0};
    unsigned char csu_fault_relate_DO_index;
    unsigned char csu_fault_relate_DO_state;
    rt_memcpy_s(DO_msg, 2, fault_do->buf, 2);
    if (DO_msg[0] == 0) {
        fault_do->rtn = BOTTOM_RTN_DATA_ERROR;
        return SUCCESSFUL;
    }

//    if (app_pb_data_decode((cmd_buf_t*)cmd_buf, setDO_request_fields, &msg) != SUCCESSFUL) {
//        fault_do->rtn = BOTTOM_RTN_DATA_ERROR;
//        return SUCCESSFUL;
//    }
    
    /* 1、硬件默认状态表示的为CSU异常时关联的DO状态；
          比如：硬件默认状态为断开，则当CSU异常时，UIB关联DO为断开状态；
       2、如果CSU关联了CSU异常故障告警，则web上配置的DO默认状态必须为硬件默认状态取反；
          比如：CSU异常故障告警关联了DO1,此时UIB板的硬件默认状态为断开，则web上必须要配置DO1的默认状态为闭合；
       3、CSU异常故障告警关联DO的情景下，CSU下发给UIB/SIU板的1表示翻转动作；
          比如：UIB板的硬件默认状态为断开，则1表示翻转（与默认状态相反），0表示不翻转（与默认状态相同），
            CSU下发的1存储到flash中，在CSU异常情况时，1状态取反为0并设置下去，表示不翻转（即 异常对应不翻转）； */
    csu_fault_relate_DO_index = DO_msg[0];
    csu_fault_relate_DO_state = DO_msg[1];

    memcpy_s(&data.name[0], sizeof(data.name), SYSPARA_PART, sizeof(SYSPARA_PART));
    data.offset = init_offset;
    data.len = sizeof(csu_604_para_t);
    if(csu_fault_relate_DO_state != csu_604a_para.csu_fault_state ||
    csu_fault_relate_DO_index != csu_604a_para.csu_fault_index){
        csu_604a_para.csu_fault_state = csu_fault_relate_DO_state;
        csu_604a_para.csu_fault_index = csu_fault_relate_DO_index;
        data.buff = (unsigned char*)&csu_604a_para;
        if(SUCCESSFUL != storage_process(&data, erase_write_opr)){
            fault_do->rtn = BOTTOM_RTN_DATA_ERROR;
        }
    }
    return SUCCESSFUL;
}

Static int set_fan_parse(void* dev_inst, void *cmd_buf) {
    unsigned int fan_para[2] = {0, 2000};  // 第一个表示占空比，第二个表示频率（默认2000）
    cmd_buf_t* set_fan = (cmd_buf_t*)cmd_buf;
    
    if (set_fan->data_len < sizeof(int) || set_fan->data_len > sizeof(fan_para) || set_fan->buf[0] > 100) {
        set_fan->rtn = BOTTOM_RTN_DATA_ERROR;
        return SUCCESSFUL;
    }

    rt_memcpy_s(&fan_para, set_fan->data_len, &set_fan->buf[0], set_fan->data_len);
    pwm_ctrl(fan_para[0], fan_para[1]);

    return SUCCESSFUL;
}


Static int app_pb_data_decode(cmd_buf_t* cmd_buf, const pb_field_t fields[], void *dest_struct) {
    pb_istream_t stream;
    
    stream = pb_istream_from_buffer(cmd_buf->buf, cmd_buf->buflen);
    if (pb_decode(&stream, fields, dest_struct)) {
        return SUCCESSFUL;
    }
    return FAILURE;
}

Static int app_pb_data_encode(cmd_buf_t* cmd_buf, const pb_field_t fields[], void *dest_struct) {
    pb_ostream_t stream;
    
    stream = pb_ostream_from_buffer(cmd_buf->buf, cmd_buf->buflen);
    if (pb_encode(&stream, fields, dest_struct)) {
         cmd_buf->data_len = (unsigned int)stream.bytes_written;
    }
    return SUCCESSFUL;
}

Static void init_csu_para(void){
    part_data_t data;
    unsigned int init_offset = 0;
    memcpy_s(&data.name[0], sizeof(data.name), SYSPARA_PART, sizeof(SYSPARA_PART));
    data.offset = init_offset;
    data.len = sizeof(csu_604_para_t);
    data.buff = (unsigned char *)&csu_604a_para;
    if (SUCCESSFUL != storage_process(&data, read_opr) || judge_para_invalid(&csu_604a_para.barcode[0],sizeof(csu_604a_para.barcode)))
        rt_memset_s(&csu_604a_para, sizeof(csu_604a_para), 0, sizeof(csu_604a_para));
}

Static int judge_para_invalid(unsigned char* temp_pointer, unsigned char size_t) {
    int i;
    for (i = 0; i < size_t; i++) {
        if ((temp_pointer[i] > 0 && temp_pointer[i] < 48) || (temp_pointer[i] > 57 && temp_pointer[i] < 65) || temp_pointer[i] > 70) {
            return TRUE;
        }
    }
    return FALSE;
}