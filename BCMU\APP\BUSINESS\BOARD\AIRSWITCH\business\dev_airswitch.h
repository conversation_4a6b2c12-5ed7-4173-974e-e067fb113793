/**
 * @file     device_type.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2017-04-19
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */

#ifndef _DEVICE_AIRSWITCH_H
#define _DEVICE_AIRSWITCH_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "device_type.h"

/*-----设备类型-----*/
#define  DEV_CSU       1
#define  DEV_AIRSWITCH 2

//数据长度
#define SYNCFLAG_LEN   3
#define ANA_DATA_LEN   22
#define DIG_DATA_LEN   1
#define ALM_DATA_LEN   2
#define PARA1_DATA_LEN   16
#define PARA2_DATA_LEN   8
#define FAC_DATA_LEN    66
#define HW_VER_LEN      15
#define BOARD_SN_LEN    12

//附加码
#define CMD_APPEND_TABLE_1            			0x0010 ///<  参数表1
#define CMD_APPEND_TABLE_2            			0x0011 ///<  参数表2
#define SWITCH_POWEROFF							0X041D //空开下电
#define SWITCH_POWERON							0X0421 //空开上电
#define DELETE_TOTAL_SWITCHPOWER				0X0463 //删除空开电量
#define SWENTER_PEERTOPEER						0X0464 //进入空开一键功能
#define SWQUIT_PEERTOPEER						0X0465 //退出空开一键功能
#define SWITCH_RESET							0X0490 //空开复位
#define CMD_CLEAR_ALL_ALARM                     0x0491 //清除所有告警

#define CMD_TRIGGER_APPTEST                     0x0010 // apptest触发
#define CMD_EXIT_APPTEST                        0x0011 // apptest退出

//命令码：0x51
#define CMD_APPTEST_REALDATA                    0x0010 //空开APPTEST获取模拟量数据
#define CMD_APPTEST_DIGITAL_DATA                0x0011 //空开APPTEST获取数字量数据
#define CMD_APPTEST_ADDRESS                     0x0012 //空开APPTEST查询槽位地址
#define CMD_APPTEST_LED_STATUS                  0x0013 //空开APPTEST查询LED颜色
#define CMD_APPTEST_SNDATA                      0x0014 //空开APPTEST查询整机条码(空开序列号)
#define CMD_APPTEST_SOFTVERSION                 0x0015 //空开APPTEST获取软件版本
#define CMD_APPTEST_ADJUST_PARA_GET             0x0016 //空开APPTEST获取零点校正参数
#define CMD_APPTEST_GET_E2_WR_TEST_RESULT       0x0017 //空开APPTEST获取E2读写测试结果
#define CMD_APPTEST_GET_E2_POWERDOWN_SAVE       0x0018 //空开APPTEST获取E2掉电保存参数
#define CMD_APPTEST_SN_BOARD                    0x0019 //空开APPTEST查询单板条码

//命令码：0x52
#define CMD_APPTEST_SET_SN_DATA                 0x0010 //空开APPTEST设置整机条码
#define CMD_APPTEST_SET_MOTOR_DUTY_CYCLE        0x0011 //空开APPTEST设置占空比
#define CMD_APPTEST_ADJUST_PARA_SET_K           0x0013 //空开APPTEST设置零点校正参数K
#define CMD_APPTEST_SET_SYS_NAME                0x0014 //空开APPTEST设置系统名称
#define CMD_APPTEST_SET_HARDWARE_VER            0x0015 //空开APPTEST设置硬件版本
#define CMD_APPTEST_SET_E2_POWERDOWN_SAVE       0x0016 //空开APPTEST设置E2掉电保存参数
#define CMD_APPTEST_SET_SN_BOARD                0x0017 //空开APPTEST设置单板条码
#define CMD_APPTEST_ADJUST_PARA_SET_B           0x0018 //空开APPTEST设置零点校正参数B

//命令码：0x53
#define CMD_APPTEST_CTRL_RESET                  0x0010 //空开APPTEST控制单板复位    
#define CMD_APPTEST_CTRL_SET_LED_STATUS         0x0011 //空开APPTEST设置指示灯状态
#define CMD_APPTEST_CLEAR_FAULT_OPENING_TIMES   0x0012 //空开APPTEST清空故障分闸次数
#define CMD_APPTEST_RESTORE_DEF_PARA            0x0013 //空开APPTEST恢复默认参数

//命令码：0x54
#define CMD_GET_DEBUG_INFO                      0x0010 //获取调试信息
#define CMD_ADJUST_MOTOR_SLIGNTLY               0x0011 //微调电机

typedef enum {
    SW_TYPE_VOID = 0,
    SW_TYPE_BOOLEAN = 1,
    SW_TYPE_CHAR = 2,
    SW_TYPE_INT8U = 3,
    SW_TYPE_INT8S = 4,
    SW_TYPE_INT16U = 5,
    SW_TYPE_INT16S = 6,
    SW_TYPE_INT32U = 7,
    SW_TYPE_INT32S = 8,
    SW_TYPE_FP32 = 9,
    SW_TYPE_INT64S = 10,
    SW_TYPE_TIMESTRUCT = 11,
    SW_TYPE_DATESTRUCT = 12,
    SW_TYPE_BIT = 13,
}airswitch_data_type_e;

#pragma pack(1)

typedef struct {
	unsigned char Syncflag_LEN;			//段1（同步标志）字节长度
	unsigned char LINK_Master_Address;	//建链主机地址
	unsigned short CRC_Code;			//批量公共参数段1的CRC码
}Airswitch_syncflag_t;

typedef struct {
	unsigned char  Ana_Data_LEN;						//段2（模拟量）字节长度	
	unsigned short Switch_Output_Voltage; 				//空开输出电压
	unsigned short Switch_Current;  					//空开电流
	unsigned short Switch_Power;						//空开功率
	unsigned int Switch_Input_Power_Energy;  			//空开输入电量
	unsigned int Switch_Output_Power_Energy; 			//空开输出电量
	unsigned short Switch_Contact_Temperature;			//空开触点温度
	unsigned short Switch_Internal_Temperature;			//空开内部温度
	unsigned short Fault_Opening_Times;					//故障分闸次数
	unsigned char SW_Group_Internal_Address;			//空开组内地址
	unsigned char SW_Group_Address;						//空开组地址
}Airswitch_ana_data_t;

typedef struct {
	unsigned char Dig_Data_LEN;						    //段3（状态量）字节长度	
	unsigned char Switch_Loop_Status:1;					//空开回路状态
	unsigned char Switch_DownLoad_Status:1;				//空开下电状态
	unsigned char Authorized_status:1;					//授权状态
	unsigned char Switch_Power_Status:1;					//空开有电状态
	unsigned char Switch_P2P_Status:1;					//空开一键功能状态
	unsigned char DigReserve:3;							//预留
}Airswitch_dig_data_t;

typedef struct {
	unsigned char Alm_Data_LEN;							//段4（告警量）字节长度		
	unsigned char Switch_Over_Current_Alarm:1;			//空开过流告警
	unsigned char Switch_Open:1;							//空开断开
	unsigned char Switch_Contact_Over_Temperature:1;		//空开触点过温
	unsigned char Switch_Internal_Over_Temperature:1;		//空开内部过温
	unsigned char Switch_Low_Voltage_Download:1;			//空开低压下电
	unsigned char Switch_Download:1;						//空开下电
	unsigned char Switch_Close_Failure:1;					//空开合闸失败
	unsigned char Switch_Opening_Failure:1;				//空开分闸失败
	unsigned char Switch_Connected_Reversely:1;			//空开接反
	unsigned char Switch_Controller_Failure:1;			//空开控制器故障
	unsigned char Switch_Over_Current_Protect:1;			//空开过流保护告警
	unsigned char Switch_Over_Temperature_Protect:1;		//空开过温保护告警
	unsigned char Switch_Overload_Trip:1;					//空开过载跳闸
	unsigned char AlmReserve:3;								//预留
}Airswitch_alm_data_t;

typedef struct {
	unsigned char  Para1_Data_LEN;							//段1（私有参数1）字节长度	
	unsigned char  Switch_Download_Enabled;					//空开下电使能
	unsigned short Switch_Download_Voltage_Threshold;		//空开下电电压阈值
	unsigned short Switch_Upload_Voltage_Threshold;			//空开上电电压阈值
	unsigned char  Switch_Over_Current_Protect_Enabled;		//空开过流保护使能
	unsigned short Switch_Over_Current_Alarm_Threshold;		//空开过流告警阈值
	unsigned char  Switch_Upload_Authorization;				//空开上电授权
	unsigned short Switch_Over_Current_Protect_Delay;		//空开过流保护延时
	unsigned char  Switch_Over_Current_Resume_Times;		//空开过流保护自动恢复次数
	unsigned char  Switch_Over_Current_Resume_Space;		//空开过流保护自动恢复间隔
	unsigned short Switch_Over_Current_Protect_Threshold;	//空开过流保护阈值
	unsigned char  Switch_Disconnect_judgment_mode;			//空开断开判断方式
}Airswitch_para1_t;

typedef struct {
	unsigned char  Para2_Data_LEN;							//段1（私有参数2）字节长度		
	unsigned int   OutPut_Electricity_Benchmark_Parameters;	//空开输出电量基准参数
	unsigned int   Input_Electricity_Benchmark_Parameters;	//空开输入电量基准参数
}Airswitch_para2_t;

typedef struct {
	unsigned char  Fac_Data_Len;							//段1（版本信息）字节长度	
	unsigned char  Switch_SystemName[32];					//空开系统名称
	unsigned char  Switch_SoftwareVersion[20];				//空开软件版本
	date_base_t	   Switch_Software_ReleaseDate;				//空开软件发布日期			
	unsigned char  Switch_SN[8];							//空开序列号			
	unsigned short Switch_Rated_Current;					//空开额定电流
}Airswitch_fac_data_t;

//获取实时数据汇总
typedef struct {
	Airswitch_syncflag_t Airswitch_syncflag;
	Airswitch_ana_data_t Airswitch_ana_data;
	Airswitch_dig_data_t Airswitch_dig_data;
	Airswitch_alm_data_t Airswitch_alm_data;
}Airswitch_real_data_t; 

#pragma pack()

dev_type_t* init_dev_airswitch(void);
cmd_parse_info_id_verison_t* deal_apptest_set_cmd(unsigned char data_struct_interact);


#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _DEVICE_604A_H

