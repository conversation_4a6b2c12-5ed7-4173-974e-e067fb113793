#include <stdio.h>
#include <string.h>
#include "cmd.h"
#include "sps.h"
#include "protocol_layer.h"
#include "dev_airswitch.h"
#include "realdata_id_in.h"
#include "para_id_in.h"


/* 设备缓冲区长度 */
#define R_BUFF_LEN                    512       ///<  接收缓冲区长度
#define S_BUFF_LEN                    512      ///<  发送缓冲区长度

/* 命令请求头 */
static bottom_comm_cmd_head_t cmd_req[] = {
    // 通信命令
    {BOTTOM_PROTO_TYPE_COMM, CMD_LINK,           CMD_APPEND_NONE,    BOTTOM_RTN_APP_ACK},               // 0
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA,  CMD_APPEND_NONE,    BOTTOM_RTN_APP_ACK},               // 1
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_BATCH_PARA, CMD_APPEND_TABLE_1, BOTTOM_RTN_APP_ACK},               // 2
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_BATCH_PARA, CMD_APPEND_TABLE_1, BOTTOM_RTN_APP_ACK},               // 3
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_BATCH_PARA, CMD_APPEND_TABLE_2, BOTTOM_RTN_APP_ACK},               // 4
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_BATCH_PARA, CMD_APPEND_TABLE_2, BOTTOM_RTN_APP_ACK},               // 5
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL,       SWITCH_POWEROFF,    BOTTOM_RTN_APP_ACK},               // 6
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL,       SWITCH_POWERON,    BOTTOM_RTN_APP_ACK},                // 7
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL,       DELETE_TOTAL_SWITCHPOWER,    BOTTOM_RTN_APP_ACK},      // 8
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL,       SWENTER_PEERTOPEER,    BOTTOM_RTN_APP_ACK},            // 9
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL,       SWQUIT_PEERTOPEER,    BOTTOM_RTN_APP_ACK},             // 10
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL,       SWITCH_RESET,    BOTTOM_RTN_APP_ACK},                  // 11
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_DATA_OLD, CMD_APPEND_NONE, BOTTOM_RTN_APP_ACK},                 // 12
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_TRIG_OLD, CMD_APPEND_UPDATE_REQ, BOTTOM_RTN_APP_ACK},           // 13
    {BOTTOM_PROTO_TYPE_COMM, CMD_TRIGGER_EXIT_APPTEST, CMD_TRIGGER_APPTEST, BOTTOM_RTN_APP_ACK},        // 14
    {BOTTOM_PROTO_TYPE_COMM, CMD_TRIGGER_EXIT_APPTEST, CMD_EXIT_APPTEST, BOTTOM_RTN_APP_ACK},           // 15
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_INFO_APPTEST, CMD_APPTEST_REALDATA, BOTTOM_RTN_APP_ACK},           // 16
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_INFO_APPTEST, CMD_APPTEST_DIGITAL_DATA, BOTTOM_RTN_APP_ACK},       // 17
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_INFO_APPTEST, CMD_APPTEST_ADDRESS, BOTTOM_RTN_APP_ACK},            // 18
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_INFO_APPTEST, CMD_APPTEST_LED_STATUS, BOTTOM_RTN_APP_ACK},         // 19
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_INFO_APPTEST, CMD_APPTEST_SNDATA, BOTTOM_RTN_APP_ACK},             // 20
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_INFO_APPTEST, CMD_APPTEST_SN_BOARD, BOTTOM_RTN_APP_ACK},           // 21
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_INFO_APPTEST, CMD_APPTEST_SOFTVERSION, BOTTOM_RTN_APP_ACK},        // 22
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_INFO_APPTEST, CMD_APPTEST_ADJUST_PARA_GET, BOTTOM_RTN_APP_ACK},    // 23
    {BOTTOM_PROTO_TYPE_COMM, CMD_CTRL_APPTEST, CMD_APPTEST_CTRL_RESET, BOTTOM_RTN_APP_ACK},             // 24
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_INFO_APPTEST, CMD_APPTEST_SET_SN_DATA, BOTTOM_RTN_APP_ACK},        // 25
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_INFO_APPTEST, CMD_APPTEST_SET_SN_BOARD, BOTTOM_RTN_APP_ACK},           // 26
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_INFO_APPTEST, CMD_APPTEST_SET_MOTOR_DUTY_CYCLE, BOTTOM_RTN_APP_ACK},   // 27
    {BOTTOM_PROTO_TYPE_COMM, CMD_CTRL_APPTEST, CMD_APPTEST_CTRL_SET_LED_STATUS, BOTTOM_RTN_APP_ACK},        // 28
    {BOTTOM_PROTO_TYPE_COMM, CMD_CTRL_APPTEST, CMD_APPTEST_CLEAR_FAULT_OPENING_TIMES, BOTTOM_RTN_APP_ACK},  // 29
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_INFO_APPTEST,  CMD_APPTEST_ADJUST_PARA_SET_K,    BOTTOM_RTN_APP_ACK},  // 30
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_INFO_APPTEST, CMD_APPTEST_SET_SYS_NAME, BOTTOM_RTN_APP_ACK},           // 31
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_INFO_APPTEST, CMD_APPTEST_SET_HARDWARE_VER, BOTTOM_RTN_APP_ACK},       // 32
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_INFO_APPTEST, CMD_APPTEST_GET_E2_WR_TEST_RESULT, BOTTOM_RTN_APP_ACK},  // 33
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_INFO_APPTEST, CMD_APPTEST_SET_E2_POWERDOWN_SAVE, BOTTOM_RTN_APP_ACK},  // 34
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_INFO_APPTEST, CMD_APPTEST_GET_E2_POWERDOWN_SAVE, BOTTOM_RTN_APP_ACK},  // 35
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA_2,  CMD_APPEND_NONE,    BOTTOM_RTN_APP_ACK},                 // 36
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_HARDWARE_VER, CMD_APPEND_NONE, BOTTOM_RTN_APP_ACK},                    // 37
    {BOTTOM_PROTO_TYPE_COMM, CMD_SOFTWARE_DEBUG, CMD_GET_DEBUG_INFO, BOTTOM_RTN_APP_ACK},                   // 38
    {BOTTOM_PROTO_TYPE_COMM, CMD_SOFTWARE_DEBUG, CMD_ADJUST_MOTOR_SLIGNTLY, BOTTOM_RTN_APP_ACK},            // 39
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_BOARD_SN, CMD_APPEND_NONE, BOTTOM_RTN_APP_ACK},                        // 40
    {BOTTOM_PROTO_TYPE_COMM, CMD_CTRL_APPTEST, CMD_APPTEST_RESTORE_DEF_PARA, BOTTOM_RTN_APP_ACK},           // 41
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_INFO_APPTEST,  CMD_APPTEST_ADJUST_PARA_SET_B,    BOTTOM_RTN_APP_ACK},  // 42
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL,       CMD_CLEAR_ALL_ALARM, BOTTOM_RTN_APP_ACK},                  // 43
};

/* 命令应答头 */
static bottom_comm_cmd_head_t cmd_ack[] = {
    // 通信命令
    {BOTTOM_PROTO_TYPE_COMM, CMD_LINK,           CMD_APPEND_NONE,    BOTTOM_RTN_APP_CORRECT},           // 0
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA,  CMD_APPEND_NONE,    BOTTOM_RTN_APP_CORRECT},           // 1
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_BATCH_PARA, CMD_APPEND_TABLE_1, BOTTOM_RTN_APP_CORRECT},           // 2
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_BATCH_PARA, CMD_APPEND_TABLE_1, BOTTOM_RTN_APP_CORRECT},           // 3
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_BATCH_PARA, CMD_APPEND_TABLE_2, BOTTOM_RTN_APP_CORRECT},           // 4
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_BATCH_PARA, CMD_APPEND_TABLE_2, BOTTOM_RTN_APP_CORRECT},           // 5
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL,       SWITCH_POWEROFF,    BOTTOM_RTN_APP_CORRECT},           // 6
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL,       SWITCH_POWERON,    BOTTOM_RTN_APP_CORRECT},            // 7
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL,       DELETE_TOTAL_SWITCHPOWER,    BOTTOM_RTN_APP_CORRECT},  // 8
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL,       SWENTER_PEERTOPEER,    BOTTOM_RTN_APP_CORRECT},        // 9
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL,       SWQUIT_PEERTOPEER,    BOTTOM_RTN_APP_CORRECT},         // 10
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL,       SWITCH_RESET,    BOTTOM_RTN_APP_CORRECT},              // 11
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_DATA_OLD, CMD_APPEND_NONE, BOTTOM_RTN_APP_CORRECT},             // 12
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_TRIG_OLD, CMD_APPEND_UPDATE_ACK, BOTTOM_RTN_APP_CORRECT},       // 13
    {BOTTOM_PROTO_TYPE_COMM, CMD_TRIGGER_EXIT_APPTEST, CMD_TRIGGER_APPTEST, BOTTOM_RTN_APP_CORRECT},    // 14
    {BOTTOM_PROTO_TYPE_COMM, CMD_TRIGGER_EXIT_APPTEST, CMD_EXIT_APPTEST, BOTTOM_RTN_APP_CORRECT},       // 15
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_INFO_APPTEST, CMD_APPTEST_REALDATA, BOTTOM_RTN_APP_CORRECT},       // 16
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_INFO_APPTEST, CMD_APPTEST_DIGITAL_DATA, BOTTOM_RTN_APP_CORRECT},   // 17
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_INFO_APPTEST, CMD_APPTEST_ADDRESS, BOTTOM_RTN_APP_CORRECT},        // 18
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_INFO_APPTEST, CMD_APPTEST_LED_STATUS, BOTTOM_RTN_APP_CORRECT},     // 19
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_INFO_APPTEST, CMD_APPTEST_SNDATA, BOTTOM_RTN_APP_CORRECT},         // 20
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_INFO_APPTEST, CMD_APPTEST_SN_BOARD, BOTTOM_RTN_APP_CORRECT},       // 21
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_INFO_APPTEST, CMD_APPTEST_SOFTVERSION, BOTTOM_RTN_APP_CORRECT},    // 22
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_INFO_APPTEST, CMD_APPTEST_ADJUST_PARA_GET, BOTTOM_RTN_APP_CORRECT},        // 23
    {BOTTOM_PROTO_TYPE_COMM, CMD_CTRL_APPTEST,  CMD_APPTEST_CTRL_RESET,    BOTTOM_RTN_APP_CORRECT},             // 24
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_INFO_APPTEST, CMD_APPTEST_SET_SN_DATA, BOTTOM_RTN_APP_CORRECT},            // 25
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_INFO_APPTEST, CMD_APPTEST_SET_SN_BOARD, BOTTOM_RTN_APP_CORRECT},           // 26
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_INFO_APPTEST, CMD_APPTEST_SET_MOTOR_DUTY_CYCLE, BOTTOM_RTN_APP_CORRECT},   // 27
    {BOTTOM_PROTO_TYPE_COMM, CMD_CTRL_APPTEST, CMD_APPTEST_CTRL_SET_LED_STATUS, BOTTOM_RTN_APP_CORRECT},        // 28
    {BOTTOM_PROTO_TYPE_COMM, CMD_CTRL_APPTEST, CMD_APPTEST_CLEAR_FAULT_OPENING_TIMES, BOTTOM_RTN_APP_CORRECT},  // 29
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_INFO_APPTEST, CMD_APPTEST_ADJUST_PARA_SET_K,    BOTTOM_RTN_APP_CORRECT},   // 30
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_INFO_APPTEST, CMD_APPTEST_SET_SYS_NAME, BOTTOM_RTN_APP_CORRECT},           // 31
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_INFO_APPTEST, CMD_APPTEST_SET_HARDWARE_VER, BOTTOM_RTN_APP_CORRECT},       // 32
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_INFO_APPTEST, CMD_APPTEST_GET_E2_WR_TEST_RESULT, BOTTOM_RTN_APP_CORRECT},  // 33
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_INFO_APPTEST, CMD_APPTEST_SET_E2_POWERDOWN_SAVE, BOTTOM_RTN_APP_CORRECT},  // 34
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_INFO_APPTEST, CMD_APPTEST_GET_E2_POWERDOWN_SAVE, BOTTOM_RTN_APP_CORRECT},  // 35
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA_2,  CMD_APPEND_NONE,    BOTTOM_RTN_APP_CORRECT},                 // 36
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_HARDWARE_VER, CMD_APPEND_NONE, BOTTOM_RTN_APP_CORRECT},                    // 37
    {BOTTOM_PROTO_TYPE_COMM, CMD_SOFTWARE_DEBUG, CMD_GET_DEBUG_INFO, BOTTOM_RTN_APP_CORRECT},                   // 38
    {BOTTOM_PROTO_TYPE_COMM, CMD_SOFTWARE_DEBUG, CMD_ADJUST_MOTOR_SLIGNTLY, BOTTOM_RTN_APP_CORRECT},            // 39
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_BOARD_SN, CMD_APPEND_NONE, BOTTOM_RTN_APP_CORRECT},                        // 40
    {BOTTOM_PROTO_TYPE_COMM, CMD_CTRL_APPTEST, CMD_APPTEST_RESTORE_DEF_PARA, BOTTOM_RTN_APP_CORRECT},           // 41
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_INFO_APPTEST, CMD_APPTEST_ADJUST_PARA_SET_B,    BOTTOM_RTN_APP_CORRECT},   // 42
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL,       CMD_CLEAR_ALL_ALARM, BOTTOM_RTN_APP_CORRECT},                  // 43
};

/* 通信协议命令表 */
static cmd_t no_poll_cmd_tab[] = {
    {AIRSWITCH_LINK,           CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(bottom_comm_cmd_head_t),   NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_GET_REALDATA,   CMD_PASSIVE, &cmd_req[1], &cmd_ack[1], sizeof(bottom_comm_cmd_head_t),   NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_GET_TABLE_1,    CMD_PASSIVE, &cmd_req[2], &cmd_ack[2], sizeof(bottom_comm_cmd_head_t),   NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_SET_TABLE_1,    CMD_PASSIVE, &cmd_req[3], &cmd_ack[3], sizeof(bottom_comm_cmd_head_t),   NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_GET_TABLE_2,    CMD_PASSIVE, &cmd_req[4], &cmd_ack[4], sizeof(bottom_comm_cmd_head_t),   NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_SET_TABLE_2,    CMD_PASSIVE, &cmd_req[5], &cmd_ack[5], sizeof(bottom_comm_cmd_head_t),   NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_CTRL_CMD1,      CMD_PASSIVE, &cmd_req[6], &cmd_ack[6], sizeof(bottom_comm_cmd_head_t),   NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_CTRL_CMD2,      CMD_PASSIVE, &cmd_req[7], &cmd_ack[7], sizeof(bottom_comm_cmd_head_t),   NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_CTRL_CMD3,      CMD_PASSIVE, &cmd_req[8], &cmd_ack[8], sizeof(bottom_comm_cmd_head_t),   NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_CTRL_CMD4,      CMD_PASSIVE, &cmd_req[9], &cmd_ack[9], sizeof(bottom_comm_cmd_head_t),   NULL, NULL, NULL, NULL, NULL, NULL },
    {AIRSWITCH_CTRL_CMD5,      CMD_PASSIVE, &cmd_req[10], &cmd_ack[10], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, NULL },
    {AIRSWITCH_CTRL_CMD6,      CMD_PASSIVE, &cmd_req[11], &cmd_ack[11], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_UPDATE_DATA,  CMD_BROADCAST|CMD_PASSIVE,  &cmd_req[12], &cmd_ack[12], sizeof(bottom_comm_cmd_head_t), },
    {AIRSWITCH_UPDATE_TRIG,  CMD_PASSIVE,  &cmd_req[13], &cmd_ack[13], sizeof(bottom_comm_cmd_head_t), },
    {AIRSWITCH_TRIGGER_APPTEST,   CMD_PASSIVE, &cmd_req[14], &cmd_ack[14], sizeof(bottom_comm_cmd_head_t),   NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_EXIT_APPTEST,   CMD_PASSIVE, &cmd_req[15], &cmd_ack[15], sizeof(bottom_comm_cmd_head_t),   NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_GET_REALDATA_APPTEST,   CMD_PASSIVE, &cmd_req[16], &cmd_ack[16], sizeof(bottom_comm_cmd_head_t),   NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_GET_DIGITAL_DATA_APPTEST,   CMD_PASSIVE, &cmd_req[17], &cmd_ack[17], sizeof(bottom_comm_cmd_head_t),   NULL, NULL, NULL, NULL, NULL, NULL},  //更新获取数字量结构体
    {AIRSWITCH_GET_ADDRESS_APPTEST,   CMD_PASSIVE, &cmd_req[18], &cmd_ack[18], sizeof(bottom_comm_cmd_head_t),   NULL, NULL, NULL, NULL, NULL, NULL},   //更新获取槽位地址结构体
    {AIRSWITCH_GET_LED_STATUS_APPTEST,   CMD_PASSIVE, &cmd_req[19], &cmd_ack[19], sizeof(bottom_comm_cmd_head_t),   NULL, NULL, NULL, NULL, NULL, NULL},    //更新获取LED颜色结构体
    {AIRSWITCH_GET_SNDATA_APPTEST,   CMD_PASSIVE, &cmd_req[20], &cmd_ack[20], sizeof(bottom_comm_cmd_head_t),   NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_GET_BOARD_SN_APPTEST,   CMD_PASSIVE, &cmd_req[21], &cmd_ack[21], sizeof(bottom_comm_cmd_head_t),   NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_GET_SOFTVERSION_APPTEST,   CMD_PASSIVE, &cmd_req[22], &cmd_ack[22], sizeof(bottom_comm_cmd_head_t),   NULL, NULL, NULL, NULL, NULL, NULL}, 
    {AIRSWITCH_GET_ADJUST_PARA_APPTEST,   CMD_PASSIVE, &cmd_req[23], &cmd_ack[23], sizeof(bottom_comm_cmd_head_t),   NULL, NULL, NULL, NULL, NULL, NULL},   //更新获取零点校正参数结构体
    {AIRSWITCH_CTRL_RESET_APPTEST,   CMD_PASSIVE, &cmd_req[24], &cmd_ack[24], sizeof(bottom_comm_cmd_head_t),   NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_SET_SNDATA_APPTEST,    CMD_PASSIVE, &cmd_req[25], &cmd_ack[25], sizeof(bottom_comm_cmd_head_t),   NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_SET_BOARD_SN_APPTEST,    CMD_PASSIVE, &cmd_req[26], &cmd_ack[26], sizeof(bottom_comm_cmd_head_t),   NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_SET_MOTOR_DUTY_CYCLE_APPTEST,    CMD_PASSIVE, &cmd_req[27], &cmd_ack[27], sizeof(bottom_comm_cmd_head_t),   NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_CTRL_SET_LED_STATUS_APPTEST,    CMD_PASSIVE, &cmd_req[28], &cmd_ack[28], sizeof(bottom_comm_cmd_head_t),   NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_CTRL_CLEAR_FAULT_OPENING_TIMES_APPTEST,    CMD_PASSIVE, &cmd_req[29], &cmd_ack[29], sizeof(bottom_comm_cmd_head_t),   NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_ADJUST_PARA_SET_APPTEST_K,      CMD_PASSIVE, &cmd_req[30], &cmd_ack[30], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_SET_SYS_NAME_APPTEST, CMD_PASSIVE, &cmd_req[31], &cmd_ack[31], sizeof(bottom_comm_cmd_head_t),   NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_SET_HARDWARE_VER_APPTEST,      CMD_PASSIVE, &cmd_req[32], &cmd_ack[32], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_GET_E2_WR_TEST_RESULT_APPTEST,   CMD_PASSIVE, &cmd_req[33], &cmd_ack[33], sizeof(bottom_comm_cmd_head_t),   NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_SET_E2_POWERDOWN_SAVE_APPTEST,   CMD_PASSIVE, &cmd_req[34], &cmd_ack[34], sizeof(bottom_comm_cmd_head_t),   NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_GET_E2_POWERDOWN_SAVE_APPTEST,   CMD_PASSIVE, &cmd_req[35], &cmd_ack[35], sizeof(bottom_comm_cmd_head_t),   NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_GET_REALDATA_2,   CMD_PASSIVE, &cmd_req[36], &cmd_ack[36], sizeof(bottom_comm_cmd_head_t),   NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_GET_HARDWARE_VER,      CMD_PASSIVE, &cmd_req[37], &cmd_ack[37], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_GET_DEBUG_INFO,      CMD_PASSIVE, &cmd_req[38], &cmd_ack[38], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_ADJUST_MOTOR_SLIGNTLY_DEBUG,      CMD_PASSIVE, &cmd_req[39], &cmd_ack[39], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_GET_BOARD_SN,      CMD_PASSIVE, &cmd_req[40], &cmd_ack[40], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_RESTORE_DEF_PARA_APPTEST,    CMD_PASSIVE, &cmd_req[41], &cmd_ack[41], sizeof(bottom_comm_cmd_head_t),   NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_ADJUST_PARA_SET_APPTEST_B,      CMD_PASSIVE, &cmd_req[42], &cmd_ack[42], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, NULL},
    {AIRSWITCH_CTRL_CMD7,      CMD_PASSIVE, &cmd_req[43], &cmd_ack[43], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, NULL},
    {0},
};

static dev_type_t dev_airswitch = {
    DEV_CSU, 1, PROTOCOL_BOTTOM_COMM, LINK_AIRSWITCH, R_BUFF_LEN, S_BUFF_LEN, BOTTOM_CSU_TYPE, &no_poll_cmd_tab[0],
};

dev_type_t* init_dev_airswitch(void){
    return &dev_airswitch;
}
