#ifndef _DEV_NORTH_SEUB_MODBUS_H
#define _DEV_NORTH_SEUB_MODBUS_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "device_type.h"
#include "cmd.h"
#include "linklayer.h"
#include "protocol_layer.h"
#include "dev_dc_ac_modbus.h"
#include "ee_public_info.h"

#define NORTH_PRECISION_ZERO    0
#define NORTH_PRECISION_ONE     1
#define NORTH_PRECISION_TWO     2
#define NORTH_PRECISION_THREE   3

#define SERVERS_WIRED_PORT        502

#define R_DATA_LEN_NO_USE    0
#define S_DATA_LEN_NO_USE    0

typedef struct{
    unsigned short      register_addr;      // 寄存器地址
    data_type_e         type;               // modbus传输数据类型
    unsigned char       data_len;           // modbus上送数据长度
    sid_type_e          sid_type;           // sid的类型
    void *pData;
} modbus_seub_addr_map_data_t;

#define IP_ADDRESS_DEFAULT "***********"
#define NETMASK_DEFAULT "*************"
#define GATEWAY_DEFAULT "***********"
#define IP_ADDRESS_REG_ADDR  221
#define NETMASK_REG_ADDR     229
#define GATEWAY_REG_ADDR     237


#define SEUB_SOFTWARE_NAME_LEN_MODBUS    10   // 软件名称长度
#define SEUB_SOFTWARE_VER_LEN_MODBUS     16   // 软件版本长度
#define SEUB_SOFTWARE_DATE_LEN_MODBUS    16   // 软件发布日期长度
#define SEUB_SERIAL_NUMBER_LEN_MODBUS    20   // 序列号长度
#define SEUB_SYSTEM_NAME_LEN_MODBUS      20   // 系统名称长度
#define SEUB_HARDWARE_VERSION_LEN_MODBUS 20   // 硬件版本信息长度
#define SEUB_FACTORY_INFO_EEPROM_LEN_MODBUS (SEUB_SERIAL_NUMBER_LEN_MODBUS + \
                                             SEUB_SYSTEM_NAME_LEN_MODBUS + \
                                             SEUB_HARDWARE_VERSION_LEN_MODBUS) // 存储在eeprom上的厂家信息长度

typedef struct
{
    char acSwName[SEUB_SOFTWARE_NAME_LEN_MODBUS];      // 软件名称
    char acSwVer[SEUB_SOFTWARE_VER_LEN_MODBUS];        // 软件版本
    char acSwDate[SEUB_SOFTWARE_DATE_LEN_MODBUS];      // 软件发布日期
    char acSN[SEUB_SERIAL_NUMBER_LEN_MODBUS];          // 序列号
    char acSysName[SEUB_SYSTEM_NAME_LEN_MODBUS];       // 系统名称
    char acHwVer[SEUB_HARDWARE_VERSION_LEN_MODBUS];    // 硬件版本信息
} T_SEUBFactInfoStruct;


typedef struct{
    unsigned short usAI1;
    unsigned short usAI2;
    unsigned short usAI3;
    unsigned short usAI4;
    unsigned short usAI5;
    unsigned short usAI6;
    unsigned short usAI7;
    unsigned short usAI8;
    unsigned short usAI9;
    unsigned short usAI10;
    unsigned short usAI11;
    unsigned short usAI12;
    unsigned short usAI13;
    unsigned short usAI14;
    unsigned short usAI15;
    unsigned short usAI16;
    unsigned short usAI17;
    unsigned short usAI18;
    unsigned short usAI19;
    unsigned short usAI20;
    unsigned short usAI21;
    unsigned short usAI22;
    unsigned short usAI23;
    unsigned short usAI24;
    unsigned short usAI25;
    unsigned short usAI26;
    unsigned short usAI27;
    unsigned short usAI28;
    unsigned short usAI29;
    unsigned short usAI30;
    unsigned short usAI31;
    unsigned short usAI32;
    unsigned short usDI1_12;

    unsigned short usDO1;
    unsigned short usDO2;
    unsigned short usDO3;
    unsigned short usDO4;
    unsigned short usDO5;
    unsigned short usDO6;
    unsigned short usDO7;
    unsigned short usDO8;
    unsigned short usAO1;
    unsigned short usAO2;

    T_SEUBFactInfoStruct tFacInfo;
    network_info_t  tNetwork;

} T_SheetSEUBDataStruct;


dev_type_t* init_dev_north_seub(void);
dev_type_t *init_dev_modbus_tcp_seub(void);

#ifdef __cplusplus
}
#endif  // __cplusplus

#endif  // _DEV_NORTH_SEUB_MODBUS_H