
#include <stdio.h>
#include <stdint.h>
#include <sys/time.h>
#include <netdev_ipaddr.h>
#include <netdev.h>
#include "net_connect_mode.h"
#include "realdata_id_in.h"
#include "realdata_save.h"

static rt_timer_t s_net_status_timer = NULL;

void net_connect_mode_handel(unsigned char conn_protocol, unsigned int net_conn_mode_sid)
{
    unsigned char net_conn_mode_old = 0;
    unsigned char net_conn_mode = 0;

    get_one_data(net_conn_mode_sid, &net_conn_mode_old);
    if(net_conn_mode_old == NET_MODE_NO_CONN)
    {
        rt_timer_start(s_net_status_timer);
    }

    net_conn_mode = judge_net_connect_mode(conn_protocol);
    if(net_conn_mode >= net_conn_mode_old)
    {
        set_one_data(net_conn_mode_sid, &net_conn_mode);
        reset_net_conn_status_timer();
    }
}

unsigned char judge_net_connect_mode(unsigned char conn_protocol)
{
    unsigned char net_conn_mode = 0;
    struct netdev* net_dev_4g = RT_NULL;
    struct netdev* net_dev_wifi = RT_NULL;

    net_dev_4g = netdev_get_by_name(DEV_NAME_4G);
    net_dev_wifi = netdev_get_by_name(DEV_NAME_WIFI);

    if(net_dev_wifi != RT_NULL &&
            conn_protocol == NET_MODE_MQTT)
    {
        net_conn_mode = NET_MODE_WIFI;  //wifi通讯棒组网方式待测试
    }
    else if(net_dev_4g != RT_NULL &&
            conn_protocol == NET_MODE_MQTT)
    {
        net_conn_mode = NET_MODE_4G;
    }
    else if(conn_protocol == NET_MODE_MODBUS)
    {
        net_conn_mode = NET_MODE_RS485;
    }

    return net_conn_mode;
}

void reset_net_conn_status_timer(void)
{
    unsigned long reset_time = NET_CONN_WAITE;
    rt_timer_stop(s_net_status_timer);
    rt_timer_control(s_net_status_timer , RT_TIMER_CTRL_SET_TIME , &reset_time);
    rt_timer_start(s_net_status_timer);
}

int create_net_conn_status_timer(void)
{
    char* timer_name = "net_conn_status_timer";
    s_net_status_timer = rt_timer_create(timer_name, net_connect_timeout, NULL, NET_CONN_WAITE, RT_TIMER_FLAG_ONE_SHOT | RT_TIMER_FLAG_SOFT_TIMER);
    if (s_net_status_timer == NULL)
    {
        return FAILURE;
    }
    return SUCCESSFUL;
}

void net_connect_timeout(void *parameter)
{
    unsigned char net_conn_mode = NET_MODE_NO_CONN;
    set_one_data(DAC_DATA_ID_NET_CONNECT_MODE, &net_conn_mode);
}

