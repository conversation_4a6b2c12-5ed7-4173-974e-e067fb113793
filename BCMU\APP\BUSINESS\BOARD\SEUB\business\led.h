#ifndef _DXCB_LED_H
#define _DXCB_LED_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

/* 引脚定义 */
#define  PIN_LED_CONTROL  GET_PIN(F, 15)

/* 控制状态 */
#define LED_OFF_MODE      0       ///< 常灭
#define LED_ON_MODE       1       ///< 常亮
#define LED_BLINK_MODE   2       ///< 1s间断闪
#define LED_BLINK_MODE_QUICK   3      ///< 升级1/6亮，1/6灭间断闪

/* LED灯 */
typedef enum {
    LED_RUN_STATE = 0,  ///< 主板运行状态指示灯
}led_type_e;

typedef struct {
    unsigned char led_no;         ///< LED
    unsigned char ctrl_state;     ///< 控制状态
}led_ctrl_msg_t;

void* init_led_blink(void *param);
void led_thread_entry(void* parameter);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _DXCB_LED_H