#include <rtthread.h>
#include <rtdevice.h>
#include <board.h>
#include <drv_common.h>
#include <math.h>
#include <string.h>
#include "sample.h"
#include "data_type.h"
#include "utils_thread.h"
#include "adc_ctrl.h"
#include "utils_server.h"
#include "pin_ctrl.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "protocol_modbus_comm.h"
#include "gd32f4xx_adc.h"

/***********************  变量定义  ************************/
static unsigned int   s_adc_data[32][ADCCONVERTEDVALUES_SAMPLING_NUM]; //补充ADC采样路数

/*********************  静态函数原型定义  **********************/
static void sample(void);
static void analog_sample(void);
static void digital_sample(void);
static unsigned int MedianFilter(unsigned int data[ADCCONVERTEDVALUES_SAMPLING_NUM], int size);
static void hal_board_gpio_init(void);
static int adc_get_data_by_channel(ADC_SAMPLE_CHANNEL channel);
static int di_get_data_by_channel(eDI_SAMPLE channel);
static void set_selection_sample_gpio(ADC_SAMPLE_CHANNEL channel);

static DI_CHANNEL_INFO_STRUCT s_di_sample_info[DI_END] =
{
     {DI1_IN_0, 0, PIN_DI1_IN},
     {DI1_IN_1, 1, PIN_DI1_IN},
     {DI1_IN_2, 2, PIN_DI1_IN},
     {DI1_IN_3, 3, PIN_DI1_IN},
     {DI1_IN_4, 4, PIN_DI1_IN},
     {DI1_IN_5, 5, PIN_DI1_IN},
     {DI1_IN_6, 6, PIN_DI1_IN},
     {DI1_IN_7, 7, PIN_DI1_IN},
     {DI2_IN_0, 0, PIN_DI2_IN},
     {DI2_IN_1, 1, PIN_DI2_IN},
     {DI2_IN_2, 2, PIN_DI2_IN},
     {DI2_IN_3, 3, PIN_DI2_IN},
     {DI2_IN_4, 4, PIN_DI2_IN},
     {DI2_IN_5, 5, PIN_DI2_IN},
     {DI2_IN_6, 6, PIN_DI2_IN},
     {DI2_IN_7, 7, PIN_DI2_IN},
     {DI_17,    0, PIN_DI_17},
};

static ADC_CHANNEL_INFO_STRUCT s_sample_info[AI_END] =
{
    {AI_0_0, 0, ADC_CHANNEL_8, DXCB_DATA_ID_SAMPLE_AI_SIGNAL},
    {AI_0_1, 1, ADC_CHANNEL_8, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 1},
    {AI_0_2, 2, ADC_CHANNEL_8, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 2},
    {AI_0_3, 3, ADC_CHANNEL_8, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 3},
    {AI_0_4, 4, ADC_CHANNEL_8, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 4},
    {AI_0_5, 5, ADC_CHANNEL_8, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 5},
    {AI_0_6, 6, ADC_CHANNEL_8, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 6},
    {AI_0_7, 7, ADC_CHANNEL_8, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 7},
    {AI_1_0, 0, ADC_CHANNEL_9, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 8},
    {AI_1_1, 1, ADC_CHANNEL_9, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 9},
    {AI_1_2, 2, ADC_CHANNEL_9, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 10},
    {AI_1_3, 3, ADC_CHANNEL_9, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 11},
    {AI_1_4, 4, ADC_CHANNEL_9, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 12},
    {AI_1_5, 5, ADC_CHANNEL_9, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 13},
    {AI_1_6, 6, ADC_CHANNEL_9, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 14},
    {AI_1_7, 7, ADC_CHANNEL_9, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 15},
    {AI_2_0, 0, ADC_CHANNEL_3, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL},
    {AI_2_1, 8, ADC_CHANNEL_3, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 1},
    {AI_2_2, 16, ADC_CHANNEL_3, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 2},
    {AI_2_3, 24, ADC_CHANNEL_3, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 3},
    {AI_2_4, 32, ADC_CHANNEL_3, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 4},
    {AI_2_5, 40, ADC_CHANNEL_3, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 5},
    {AI_2_6, 48, ADC_CHANNEL_3, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 6},
    {AI_2_7, 56, ADC_CHANNEL_3, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 7},
    {AI_3_0, 0, ADC_CHANNEL_0,  DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 8},
    {AI_3_1, 8, ADC_CHANNEL_0,  DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 9},
    {AI_3_2, 16, ADC_CHANNEL_0, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 10},
    {AI_3_3, 24, ADC_CHANNEL_0, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 11},
    {AI_3_4, 32, ADC_CHANNEL_0, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 12},
    {AI_3_5, 40, ADC_CHANNEL_0, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 13},
    {AI_3_6, 48, ADC_CHANNEL_0, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 14},
    {AI_3_7, 56, ADC_CHANNEL_0, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 15},
};


static msg_map sample_msg_map[] =
{
     {0,NULL},//临时添加解决编译问题
};

void* sample_init_sys(void *param)
{
    server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(sample_msg_map) / sizeof(msg_map);
    register_server_msg_map(sample_msg_map, server_info);
    hal_board_gpio_init();//初始化IO模式
    return NULL;
}

void sample_main(void* parameter)
{
    while (is_running(TRUE))
    {
        sample();
        rt_thread_mdelay(400);
    }

}

/* 采样数据 */
static void sample(void)
{
    digital_sample();
    analog_sample();
}

static void digital_sample(void)
{
    unsigned int i;
    unsigned char di_value = 0;
    for(i = 0; i < DI_END; i++) //17路DI
    {
        di_value = di_get_data_by_channel(i);
        CONTINUE_IF_FAIL(get_pile_flag() == FALSE);
        set_one_data(DXCB_DATA_ID_INPUT_DRY_POINT + i, &di_value);
    }
    return;
}

static void analog_sample(void) {
    unsigned int i,j;
    unsigned int analog_data = 0;
    float actual_analog_data = 0.0;

    for(i = 0; i < AI_END; i++) 
    { //16路AI + 16路NTC
        for(j = 0; j < ADCCONVERTEDVALUES_SAMPLING_NUM; j++)
        {
            s_adc_data[i][j] = adc_get_data_by_channel(i);
        }
        analog_data = MedianFilter(s_adc_data[i], ADCCONVERTEDVALUES_SAMPLING_NUM);
        actual_analog_data = analog_data * REFER_VOLTAGE / 4096.0 ;
        CONTINUE_IF_FAIL(get_pile_flag() == FALSE);
        set_one_data(s_sample_info[i].sid, &actual_analog_data);
    }
    return;
}

//功能引脚初始化
static void hal_board_gpio_init(void)
{
    rt_pin_mode(PIN_A0_S1, PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_A1_S1, PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_A2_S1, PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_A3_S1, PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_A4_S1, PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_A5_S1, PIN_MODE_OUTPUT);

    rt_pin_mode(PIN_DI1_IN, PIN_MODE_INPUT);
    rt_pin_mode(PIN_DI2_IN, PIN_MODE_INPUT);
    rt_pin_mode(PIN_DI_17, PIN_MODE_INPUT);
}

// 采样通道选择
static void set_selection_sample_gpio(ADC_SAMPLE_CHANNEL channel)
{
    if((s_sample_info[channel].select_status & 0x01) == 1)
        rt_pin_write(PIN_A0_S1, 1);
    else
        rt_pin_write(PIN_A0_S1, 0);

    if((s_sample_info[channel].select_status>>1 & 0x01) == 1)
        rt_pin_write(PIN_A1_S1, 1);
    else
        rt_pin_write(PIN_A1_S1, 0);

    if((s_sample_info[channel].select_status>>2 & 0x01) == 1)
        rt_pin_write(PIN_A2_S1, 1);
    else
        rt_pin_write(PIN_A2_S1, 0);

    if((s_sample_info[channel].select_status>>3 & 0x01) == 1)
        rt_pin_write(PIN_A3_S1, 1);
    else
        rt_pin_write(PIN_A3_S1, 0);

    if((s_sample_info[channel].select_status>>4 & 0x01) == 1)
        rt_pin_write(PIN_A4_S1, 1);
    else
        rt_pin_write(PIN_A4_S1, 0);

    if((s_sample_info[channel].select_status>>5 & 0x01) == 1)
        rt_pin_write(PIN_A5_S1, 1);
    else
        rt_pin_write(PIN_A5_S1, 0);
     return ;
}
//从通道获取AI数据
static int adc_get_data_by_channel(ADC_SAMPLE_CHANNEL channel)
{
    unsigned int value;
    rt_adc_device_t adc_dev;

    adc_dev = (rt_adc_device_t)rt_device_find(ADC_DEV_NAME);
    if (adc_dev == RT_NULL)
    {
        rt_kprintf("adc sample run failed! can't find %s device!\n", ADC_DEV_NAME);
        return RT_ERROR;
    }
    set_selection_sample_gpio(channel);
    rt_thread_mdelay(20);
    rt_adc_enable(adc_dev, s_sample_info[channel].adc_channel);
    value = rt_adc_read(adc_dev, s_sample_info[channel].adc_channel);
    rt_adc_disable(adc_dev, s_sample_info[channel].adc_channel);
    return value;
}
//从通道获取DI数据
static int di_get_data_by_channel(eDI_SAMPLE channel)
{
    unsigned int value;

    set_selection_sample_gpio(channel);
    rt_thread_mdelay(20);

    value = rt_pin_read(s_di_sample_info[channel].di_pin);

    return value;
}

static unsigned int MedianFilter(unsigned int data[ADCCONVERTEDVALUES_SAMPLING_NUM],int size)
{
    unsigned int max,min,sum;
    if(size>2)
    {
        max = data[0];
        min = max;
        sum = 0;
        for(int i=0;i<size;i++)
        {
            sum += data[i];
            if(data[i]>max)
            {
                max = data[i];
            }

            if(data[i]<min)
            {
                min = data[i];
            }
        }

        sum = sum-max-min;
        return sum/(size-2);
    }

    return 0;
}