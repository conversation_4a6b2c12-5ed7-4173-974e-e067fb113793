/**
 * @file     dcmu_protocol_north.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2025-05-12
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */

#ifndef _DCMU_PROTOCOL_NORTH_H
#define _DCMU_PROTOCOL_NORTH_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "device_type.h"

// 厂家信息长度宏定义
#define LEN_SMNAME      30
#define LEN_RESERVED    13

/*north protocol的命令唯一标识定义 */
#define DCMU_NORTH_GET_DATA                 1           //<呼叫命令
#define DCMU_NORTH_GET_COMM_PARA            2           //<获取直流公共参数
#define DCMU_NORTH_SET_COMM_PARA            3           //<设置直流公共参数
#define DCMU_NORTH_GET_EXT_PARA             4           //<获取直流扩展公共参数
#define DCMU_NORTH_SET_EXT_PARA             5           //<设置直流扩展公共参数
#define DCMU_NORTH_GET_ALARM                6           //<查询实时告警
#define DCMU_NORTH_CTRL_CMD                 7           //<遥控命令
#define DCMU_NORTH_GET_HIS_ALM              8           //<查询历史告警
#define DCMU_NORTH_GET_FAC                  9           //<查询厂家信息
#define DCMU_NORTH_SET_TIME                 10          //<校准时间
#define DCMU_NORTH_SET_SPEC_PARA            11          //<设置特定参数

// 功能码定义
#define	ORDER_NULL				0x00	
#define	ORDER_CTRL				0x06	// 遥控命令
#define	ORDER_GET				0x20	// 呼叫命令
#define	ORDER_GETREALALM		0x21	// 查询实时告警
#define	ORDER_GETHISALM		    0x22	// 查询历史告警
#define	ORDER_GETFACTINFO		0x23	// 查询厂家信息
#define	ORDER_GETPARA			0x24	// 查询公共参数
#define	ORDER_SETTIME			0x25	// 校准时间
#define	ORDER_SETPARA			0x26	// 设置公共参数
#define	ORDER_SETSPEPARA		0x27	// 设置特定参数
#define	ORDER_GET_DC_ALARM_PARA	 0x28	// 查询扩展公共参数
#define	ORDER_SET_DC_ALARM_PARA	 0x29	// 设置扩展公共参数


#define FLAG1_ALMCH		        0x01	// 实时告警变化标志
#define FLAG1_PARACH		    0x02	// 可设参数变化标志
#define FLAG1_TIMECH		    0x08	// 时间重置标志
#define FLAG1_F				    0x40	// 报文组结束标志F
#define FLAG1_E			        0x80	// 报文结束标志E
#define FLAG2_HISALMOV	        0x01	// 历史告警溢出标志
#define FLAG2_RST			    0x40	// 复位标志
#define FLAG2_HISALM		    0x80	// 有未读取历史告警标志

#define RTNRS485_OK	0x00	// 正确
#define RTNRS485_INVLORDER	0x01	// 不合法功能代码
#define RTNRS485_INVLDATAADD	0x02	// 不合法数据地址
#define RTNRS485_INVLDATA	0x03	// 不合法数据
#define RTNRS485_NODATA		0x04	// 无数据

#define GROUP_TYPE_RCV_START    0x00     //接收第一包
#define GROUP_TYPE_RCV_NEXT     0x01     //接收下一包
#define GROUP_TYPE_RCV_ERROR    0x02     //接收错误，重发
#define GROUP_TYPE_RCV_END      0x03     //接收结束

#define GROUP_TYPE_SEND_START    0x00     //发送第一包
#define GROUP_TYPE_SEND_NEXT     0x01     //发送下一包

#define GROUP_TYPE_SEND_NORMAL    0x00     //正常发送
#define GROUP_TYPE_SEND_LAST      0x01     //最后一包
#define GROUP_TYPE_SEND_END       0x02     //发送结束

#define RS485_DATALEN_MAX             255
#define DCMU_REAL_ALARM_DATALEN_MAX     (RS485_DATALEN_MAX - 15)
#define DCMU_HIS_ALARM_PACK_MAX         (RS485_DATALEN_MAX - 22)	// 一条历史告警的长度是18,预留4个字节,所以要减去22

#define ID1_ALM      0x32

#define DATA_INFO_OFFSET 3
#define COMM_PARA_INFO_SETTING_NUM 12
#define EXTEND_COMM_PARA_INFO_SETTING_NUM 3

dev_type_t* init_dev_dcmu_north(void);
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _DCMU_PROTOCOL_NORTH_H

