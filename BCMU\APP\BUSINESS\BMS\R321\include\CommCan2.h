#ifndef SOFTWARE_SRC_APP_COMMCAN2_H_
#define SOFTWARE_SRC_APP_COMMCAN2_H_

#include <rtdevice.h>
#include "hisdata.h"

#ifdef __cplusplus
extern "C" {
#endif

/****************定义协议宏*******************************/
#define RTN_CORRECT_CAN2         0x00       // 接收数据正确
#define RTN_WRONG_COMMAND_CAN2   0x01       // 命令格式错
#define RTN_INVALID_DATA_CAN2    0x02       // 无效数据

/*********************  函数原型定义  **********************/
#define CAN2_DEV_NAME       "can1"
#define CAN2_ADDR_START      0x01
#define CAN2_SLAVE_NUM       20
#define MACHINE_NUM_PER_CABINET          10
#define TIME_CAN2NORTH_MAX   6000  // CAN2北向60S无通信判为通信断

void InitCan2(void);
void Process_CAN2_Comm(void* parameter);
void CheckIsCan2InterconnectTestFrm(T_Can_Frame * ptCan_Frame);
void SendCan2InterconnectTestFrm(void);
void GetSmrRealDataCan2(BYTE ucDevType);
UINT32 GetCan2InterconnetTestCounter(void);
void ClearCan2InterconnetTestCounter(void);
BOOLEAN getCAN2SlaveData(T_Can2Data * ptCAN2SlavesData);
BYTE GetBMSCan2Addr(void);
BYTE GetCAN2Addr(void);
BYTE GetCan2ScAdr();
BOOLEAN IsMasterCan2(void);
void SetBMSCan2Addr(BYTE addr);
INT32S Can2RecCon(void);
BOOLEAN GetCan2ConnectedFlag(void);
void SetCan2ConnectedFlag(BOOLEAN flag);
BOOLEAN DealCan2Connected(int Cnt);
BOOLEAN cabinetMasterIdentifying();
void ChangeIntoCan2Frm(BYTE *dataComm, WORD wInLen, BYTE ucInScrDev, BYTE ucInSrcAdr);
BYTE GetCan2ScAdr();
BOOLEAN SendAlarmToSCUByCan2(T_Can_Frame const* ptFrame, BOOLEAN ucAlarmOccurFlag);
BOOLEAN DirectSendAlarmToSCUByCan2(BYTE usAlarmId, BOOLEAN ucAlarmOccurFlag);
BOOLEAN restartCan2AddrCompete(BYTE ucAddrMode);
BOOLEAN GetCan2Flag();
BOOLEAN JudgeCan2CommDisconnect(void);

#ifdef UNITEST
BOOLEAN masterSlaveComm_CAN2(void);
void SendCAN2CompeteFrm(void);
void sendCan2Frm(BYTE ucDstAdr, BYTE ucDstDev, BYTE* pucBuff);
BOOLEAN SynCAN2Para(void);
BOOLEAN SendCAN2EqualizeCurrFrm(void);
#endif

#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // SOFTWARE_SRC_APP_COMMCAN_H_;
