
#ifndef ISM330DHCX__H
#define ISM330DHCX__H

#ifdef __cplusplus
  extern "C" {
#endif

#define ACC_ANGLEALM_THRE 30 // 陀螺仪告警阈值
#define ACC_SAMPLE_LENGTH 10 // 倾角采样个数
#define ACC_ALMARM_COUNT 5   // 判断异常次数阈值

typedef struct
{
  BOOLEAN ucSensitivity;                         // 传感器采样使能     0：禁止; 1:低灵敏度;2:高灵敏度
  BYTE ucIndex;
  BOOLEAN ucSampleResult[ACC_SAMPLE_LENGTH + 1]; // 传感器采样结果 0：倾角正常              1：倾角异常
  BOOLEAN ucAngleAlm;                            // 倾角异常告警 0：正常 1：告警
  FLOAT fCurrentAngle;                           // 当前倾角
}T_AccSampleStruct;

typedef struct{
    float   fXAcc_base;
    float   fYAcc_base;
    float   fZAcc_base;
    WORD    wCrc;
}ism330dhcx_acc_base;

BOOLEAN checkLean(BYTE bGyroscopeSensitivity);
BOOLEAN SaveGyroInfo(void);
void Proc_G_Sensor(void* parameter);
rt_err_t CheckIsm330Connect(void);
BOOLEAN GetGyrCommFailStatus(void);
BOOLEAN InitGyroDev(void);

#ifdef __cplusplus
}
#endif

#endif /* ISM330DHCX_H */
