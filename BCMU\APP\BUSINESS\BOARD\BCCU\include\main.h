#ifndef _BCCU_MAIN_H
#define _BCCU_MAIN_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

#include "utils_server.h"
#include "server_id.h"

#define LED_THREAD_STACK_SIZE       512
#define SAMPLE_THREAD_STACK_SIZE    1024
#define THREAD_STACK_SIZE_CAN1      1024
#define THREAD_STACK_SIZE_CAN3      4096
#define THREAD_STACK_SIZE_USART     1024
#define ALARM_MANAGE_THREAD_STACK_SIZE     1024

int main(void);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _BCCU_MAIN_H

