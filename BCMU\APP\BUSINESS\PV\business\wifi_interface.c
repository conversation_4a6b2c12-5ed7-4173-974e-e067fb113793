#include "para_id_in.h"
#include "realdata_id_in.h"
#include "para_manage.h"
#include "realdata_save.h"
#include "wifi_interface.h"

unsigned short is_wifi_should_recover()
{
    unsigned short dev_status = 0;
    static char on_off_cnt = 0;
    static char off_on_cnt = 0;
    char check_times = 0;
    get_one_data(DAC_DATA_ID_DEV_STATUS, &dev_status);        // 状态量 1
    if(dev_status == SHUTDOWN_DC_SWITCH_OFF)   // 直流开关断
    {
        on_off_cnt ++;
        if(off_on_cnt > 0)
        {
            check_times = JUDGE_WIFI_RECOVER_TIMES;
        }
    }
    else
    {
        if(on_off_cnt > 0)
        {
            off_on_cnt ++;
        }
    }
    if(on_off_cnt * WIFI_THREAD_RUN_INTERVAL + off_on_cnt * WIFI_THREAD_RUN_INTERVAL < JUDGE_WIFI_RECOVER_INTERVAL && check_times == JUDGE_WIFI_RECOVER_TIMES)
    {
        check_times = 0;
        on_off_cnt = 0;
        off_on_cnt = 0;
        return TRUE;

    }
    if(on_off_cnt * WIFI_THREAD_RUN_INTERVAL + off_on_cnt * WIFI_THREAD_RUN_INTERVAL >= JUDGE_WIFI_RECOVER_INTERVAL)
    {
        on_off_cnt = 0;
        off_on_cnt = 0;
    }
    return FALSE;
}

void register_wifi_data()
{
    wifi_data_t input_wifi_data = {DAC_COMMON_ID_WIFI_NAME_OFFSET, DAC_COMMON_ID_WIFI_PASSWORD_OFFSET, DAC_PARA_ID_WIFI_WORK_MODE_OFFSET};
    init_wifi_data(input_wifi_data);
}