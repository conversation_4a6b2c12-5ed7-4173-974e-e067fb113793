#include <rtthread.h>
#include "utils_thread.h"
#include "device_type.h"
#include "cmd.h"
#include "sps.h"
#include "msg.h"
#include "msg_id.h"
#include "utils_time.h"
#include "utils_data_transmission.h"
#include "day_power.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "partition_def.h"
#include "app_config.h"
#include "utils_rtthread_security_func.h"

static time_base_t g_last_time = { 0 };
static power_record_t g_power_record = { 0 };
static dev_inst_t* g_dev_dc_ac = NULL;
static rt_timer_t g_day_power_timer = NULL;

static unsigned int get_act_power(void);
static void init_act_power(void);
static int is_save_time(time_base_t* time_base);
static int is_need_save(time_base_t* curr_time, time_base_t* last_time);


int init_day_power(void)
{
    init_act_power();
    char* timer_name = "day_power";
    g_day_power_timer = rt_timer_create(timer_name, send_day_power_msg_to_sample, NULL, DAY_POWER_PERIOD, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
    if (g_day_power_timer == NULL)
    {   
        LOG_E("%s:%d| create timer fail", __FUNCTION__ , __LINE__);
        return FAILURE;
    }
    rt_timer_start(g_day_power_timer);
    return SUCCESSFUL;
}

//发送消息给设备模块
void send_day_power_msg_to_sample(void *parameter)
{
    rt_msg_t msg = NULL;
    msg = rt_msg_create(DAY_POWER_MSG, NULL, 0);
    RETURN_IF_FAIL(msg != NULL);
    rt_msg_send_event(msg);
}


static void save_power_record(time_base_t* curr_time, unsigned int curr_power)
{   
    int minute_index = curr_time->minute / SAVE_MINUTE;
    g_power_record.power[curr_time->hour][minute_index] = curr_power;
    rt_memcpy(&g_power_record.save_time, curr_time, sizeof(time_base_t));
    return;
}


static void init_act_power(void)
{
    time_base_t curr_time = { 0 };
    time_base_t save_time = { 0 };
    get_time(&curr_time);

    part_data_t part_data = { 0 };
    part_data.buff = (unsigned char*)&g_power_record;
    part_data.len = sizeof(g_power_record);
    part_data.offset = 0;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), "%s", ACT_POWER_FILE);
    if (SUCCESSFUL != storage_process(&part_data, read_opr))
    {
        LOG_E("%s:%d| read act fail", __FUNCTION__ , __LINE__);
        return;
    }

    // 获取当前功率
    unsigned int curr_power = get_act_power();
    if ((crc_cal((unsigned char*)&g_power_record, sizeof(power_record_t) - 2) == g_power_record.crc) 
        && (g_power_record.valid == ENERGY_DATA_VALID))
    {
        // 之前存过数据
        rt_memcpy(&save_time, &g_power_record.save_time, sizeof(time_base_t));
        day_power_time_check(&curr_time, &save_time, curr_power);
    }
    else
    {   
        // 没有存过数据
        rt_memset(&g_power_record, 0x00, sizeof(g_power_record));

        // 新的时间是保存时间点，进行保存
        if (is_save_time(&curr_time) == TRUE)
        {
            save_power_record(&curr_time, curr_power);
            save_act_power();
        }
    }

    rt_memcpy(&g_last_time, &curr_time, sizeof(time_base_t));
    return;

}

int is_same_day(time_base_t* curr_time, time_base_t* last_time)
{
    if ((curr_time->year == last_time->year) && (curr_time->month == last_time->month)
        && (curr_time->day == last_time->day))
    {
        return TRUE;
    }

    return FALSE;
}


static unsigned int get_act_power(void)
{
    float act_power = 0.0;

    g_dev_dc_ac = init_dev_inst(DEV_DC_AC);
    RETURN_VAL_IF_FAIL(g_dev_dc_ac != NULL, 0);
    if (g_dev_dc_ac[0].state == STAT_COMM_FAIL)
    {
        return 0;
    }
    else
    {
        get_one_data(DAC_DATA_ID_ACTIVE_POWER, (void*)&act_power);
        return (unsigned int)(act_power * ACT_POWER_PRECISION);
    }
}

void save_act_power(void)
{   
    g_power_record.valid = ENERGY_DATA_VALID;
    g_power_record.crc = crc_cal((unsigned char*)&g_power_record, sizeof(power_record_t) - 2);

    part_data_t part_data = { 0 };
    part_data.buff = (unsigned char*)&g_power_record;
    part_data.len = sizeof(g_power_record);
    part_data.offset = 0;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), "%s", ACT_POWER_FILE);
    if (SUCCESSFUL != storage_process(&part_data, write_opr))
    {   
        LOG_E("%s:%d| write act fail", __FUNCTION__ , __LINE__);
        return;
    }
    return;
}


int day_power_time_check(time_base_t* curr_time, time_base_t* last_time, unsigned int curr_power)
{
    // 检查是否同一天
    if (is_same_day(curr_time, last_time) == FALSE)
    {
        rt_memset(&g_power_record, 0x00, sizeof(g_power_record));
        // 新的时间是保存时间点，进行保存
        if (is_save_time(curr_time) == TRUE)
        {
            save_power_record(curr_time, curr_power);
            save_act_power();
            return TRUE;
        }
        return FALSE;
    }

    // 是同一天
    if (is_need_save(curr_time, last_time) == TRUE)
    {
        save_power_record(curr_time, curr_power);
        save_act_power();
        return TRUE;
    }

    return FALSE;

}
void day_power_handle(void)
{
    time_base_t curr_time = { 0 };
    get_time(&curr_time);

    unsigned int curr_power = get_act_power();
    day_power_time_check(&curr_time, &g_last_time, curr_power);
    rt_memcpy(&g_last_time, &curr_time, sizeof(time_base_t));
}


static int is_save_time(time_base_t* time_base)
{
    if(time_base->minute % SAVE_MINUTE == 0)
    {
        return TRUE;
    }

    return FALSE;
}


// 前提条件，是同一天
static int is_need_save(time_base_t* curr_time, time_base_t* last_time)
{
    // 上次时间不是保存时间，这次时间是保存时间
    if ((is_save_time(curr_time) == TRUE) && (is_save_time(last_time) == FALSE))
    {
        return TRUE;
    }

    return FALSE;
}