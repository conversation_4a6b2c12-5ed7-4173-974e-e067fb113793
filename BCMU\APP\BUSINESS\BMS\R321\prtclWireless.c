/**************************************************************************
* Copyright (C) 2001, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：ZXDU18 CTL板软件
* 文件名称：prtclWireless.c
* 文件说明：无线协议模块
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/
#include "common.h"
#include "realAlarm.h"
#include "battery.h"
#include "rtthread.h"
#include "sample.h"
#include "para.h"
#include "comm.h"
#include "prtclWireless.h"
#include "fileSys.h"
#include "CommCan.h"
#include "protocol.h"
#include "stdio.h"
#include "utils_rtthread_security_func.h"
#include "wireless.h"
#include "sys/socket.h"

/***********************  变量定义  ************************/
static T_SysPara   s_tSysPara;
Static T_PrtclWirelessStruct s_tPrtclWireless;    /* 通信协议层 */
Static T_GprsLinkStruct s_tGprsProtocol;
static T_GprsBuff s_tGprsBuf;
Static BYTE  s_aucGprsRecBuf[LEN_COMM_REC_BUF];
Static BOOLEAN s_b4GNorthCommDisconnect = True;     // 4G通信断状态
Static BOOLEAN ucCloseSocketFlag = False;
Static T_4GTrafficTotail s_t4GTrafficTotail;
Static UINT32 s_uiUp4GTrafficBk = 0;
Static UINT32 s_uiDown4GTrafficBk = 0;
Static WORD s_wSave4GTrafficTimeCnt = 0;

T_GprsCacheStruct g_tGprsCache;         //无线通讯协议缓存数据

static int isocket;
/*********************  静态函数原型定义  **********************/
Static BYTE GetDataRTN(void);
static void DealDataCmd(void);
static void PackSendData(void);

// 通用命令
static void SendData(BYTE);
static void SendAlarm(BYTE);
static void SendPara(BYTE);
static void RecPara(BYTE);
static void	RecRemoteCtrl(BYTE);
Static void RecAutoUpData(BYTE);

static INT32S  INT32SToHost(INT32S Temp);

Static void ChangeIntoGprsFrm(void);
static void GetBMSDeviceSnID(void);
Static BOOLEAN Set4GCommDisconnect(BOOLEAN flag);
/********************************命令码处理START**************************************/
const T_CmdFuncStruct s_atCmdTable[] =
{
    {GET_REAL_DATA, SendData, 0},
    {GET_REAL_ALM, SendAlarm, 0},
    {GET_PARA, SendPara, 0},
    {SET_PARA, RecPara, 0},
    {SET_CTRL, RecRemoteCtrl, 2},
    {AUTO_UP_ACK, RecAutoUpData, 2},

    {0x00, 0x0000, 0x00},
};

BOOLEAN Judge4GCommDisconnect(void)
{
    return s_b4GNorthCommDisconnect;
}

Static BOOLEAN Set4GCommDisconnect(BOOLEAN flag)
{
    s_b4GNorthCommDisconnect = flag;
    return s_b4GNorthCommDisconnect;
}

Static void ChangeIntoGprsFrm(void)
{
    WORD wHeadLen = 0, wFrmLen = 0;
    BYTE *p;
    INT32 iLen;

    p = g_tGprsCache.aucSendBuff; //发送缓冲区缓存
    rt_memset(g_tGprsCache.aucSendBuff, 0 ,sizeof(g_tGprsCache.aucSendBuff));

    GetBMSDeviceSnID();
    /** HeadLen = HeadByte + VerByte + FrmByte + IDLenByte + NeIdByte + DataLenByte **/
    wHeadLen = 2+1+1+1+s_tGprsProtocol.ucIDLen+2;     // 协议帧包头长度

    *p++ = wHeadLen / 256;
    *p++ = wHeadLen % 256;       	/* HeaderLen*/
    *p++ = GPRS_PRO_VER;         	/* Ver */
    *p++ = FRM_TYPE_SEND;			/* FrameType */
    *p++ = s_tGprsProtocol.ucIDLen;			/* IDLen */

    /* NeID */
    rt_memcpy(p, s_tGprsProtocol.aucNeIDBuff, s_tGprsProtocol.ucIDLen);
    p += s_tGprsProtocol.ucIDLen;

    /* DataLen */
    *p++ = s_tGprsProtocol.wSndDataCacheLen / 256;
    *p++ = s_tGprsProtocol.wSndDataCacheLen % 256;

    /* DATA_INFO */
    rt_memcpy(p, s_tGprsProtocol.aucSndDataBuffCache, s_tGprsProtocol.wSndDataCacheLen);
    p += s_tGprsProtocol.wSndDataCacheLen;

    wFrmLen = wHeadLen+s_tGprsProtocol.wSndDataCacheLen;

    g_tGprsCache.ucSendOriginLen = wFrmLen;

    iLen = send(isocket, g_tGprsCache.aucSendBuff, wFrmLen, 0);

    if(iLen > 0)
    {
        s_t4GTrafficTotail.s_tUp4GTraffic.wByte += (iLen + 40);    //40为TCP/IP头字节数
        if(s_t4GTrafficTotail.s_tUp4GTraffic.wByte >= 1024)
        {
            s_t4GTrafficTotail.s_tUp4GTraffic.uiByteTotail++;
            s_t4GTrafficTotail.s_tUp4GTraffic.wByte -= 1024;

            if((s_t4GTrafficTotail.s_tUp4GTraffic.uiByteTotail - s_uiUp4GTrafficBk) > 200 && (s_wSave4GTrafficTimeCnt > 3600))
            {
                write4GTraffic(&s_t4GTrafficTotail);
                s_uiDown4GTrafficBk = s_t4GTrafficTotail.s_tDown4GTraffic.uiByteTotail;
                s_uiUp4GTrafficBk   = s_t4GTrafficTotail.s_tUp4GTraffic.uiByteTotail;
                s_wSave4GTrafficTimeCnt = 0;
            }
        }
    }

    return;
}

static void GetBMSDeviceSnID(void)
{
    T_BmsPACKFactoryStruct  tBmsPACKFactory;

    rt_memset_s(&tBmsPACKFactory, sizeof(T_BmsPACKFactoryStruct), 0, sizeof(T_BmsPACKFactoryStruct));
    rt_memset_s(s_tGprsProtocol.aucNeIDBuff, sizeof(s_tGprsProtocol.aucNeIDBuff), 0, sizeof(s_tGprsProtocol.aucNeIDBuff));

    readBmsPackFacInfo(&tBmsPACKFactory);

    s_tGprsProtocol.ucIDLen = rt_strnlen_s(tBmsPACKFactory.acBmsFacSn, sizeof(tBmsPACKFactory.acBmsFacSn))+1;
    rt_memcpy_s(s_tGprsProtocol.aucNeIDBuff, sizeof(tBmsPACKFactory.acBmsFacSn), (BYTE*)tBmsPACKFactory.acBmsFacSn, sizeof(tBmsPACKFactory.acBmsFacSn));
}

BYTE GetDataFromGprsCache(void)
{
    BYTE i = 0;
    WORD wRevDataLen = 0;

    rt_memset((BYTE *)&s_tGprsProtocol, 0, sizeof(s_tGprsProtocol));

    /* Parse Recv Data */
    s_tGprsProtocol.wHeaderLen 	= g_tGprsCache.aucRecBuf[0]*256 + g_tGprsCache.aucRecBuf[1];
    s_tGprsProtocol.ucVer 		= g_tGprsCache.aucRecBuf[2];
    s_tGprsProtocol.ucFrameType = g_tGprsCache.aucRecBuf[3];
    s_tGprsProtocol.ucIDLen	= g_tGprsCache.aucRecBuf[4];

    rt_memcpy(s_tGprsProtocol.aucNeIDBuff, &g_tGprsCache.aucRecBuf[5], s_tGprsProtocol.ucIDLen);

    wRevDataLen = g_tGprsCache.aucRecBuf[s_tGprsProtocol.wHeaderLen-2]*256 + g_tGprsCache.aucRecBuf[s_tGprsProtocol.wHeaderLen-1];
    s_tGprsProtocol.wRecDataCacheLen = wRevDataLen;

    /**** Copy 1363 DATA to Cache ****/
    for(i = 0; i < wRevDataLen; i++)
    {
        s_tGprsProtocol.aucRecDataBuffCache[i] = g_tGprsCache.aucRecBuf[s_tGprsProtocol.wHeaderLen+i];
    }

    if(s_tGprsProtocol.ucVer != GPRS_PRO_VER)
    {
        return	RTN_PARSE_WRONG_VER;	/**版本错误**/
    }

    if(s_tGprsProtocol.ucFrameType != FRM_TYPE_RECV)
    {
        return RTN_PARSE_WRONG_FRMTYPE;	/**帧类型错误**/
    }

    if(s_tGprsProtocol.ucIDLen <= 1)
    {
        return RTN_PARSE_WRONG_IDLEN;	/**ID长度错误**/
    }

    if(s_tGprsProtocol.wHeaderLen != (WORD)(s_tGprsProtocol.ucIDLen+7))
    {
        return RTN_PARSE_WRONG_HEADLEN;	/**帧头长度错误**/
    }

    if(s_tGprsProtocol.wRecDataCacheLen == 0)
    {
        return RTN_PARSE_HEART_CORRECT; /**心跳帧**/
    }
    return RTN_PARSE_CORRECT;
}

/****************************************************************************
* 函数名称：DealWirelessData()
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：处理接收到的数据包
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
void DealWirelessData(void)
{  
    if(GetDataFromGprsCache() != RTN_PARSE_CORRECT)
    {
        return;
    }
    s_tPrtclWireless.wOriginLength = s_tGprsProtocol.wRecDataCacheLen;
    ConvertAscii2Hex(s_tGprsProtocol.aucRecDataBuffCache, s_tPrtclWireless.aucRecBuf, s_tGprsProtocol.wRecDataCacheLen);

    /* 根据接收到的数据包判断响应数据包的RTN值 */
    s_tPrtclWireless.ucRTN   = GetDataRTN( );
    
    /* 若RTN为正确，组织要发送数据 */
    if ( RTN_CORRECT == s_tPrtclWireless.ucRTN )
    {
        DealDataCmd();
    }
    /* 若数据有误，响应包的INFO部分为空 */
    else
    {
        s_tPrtclWireless.wSendLenid  = 0;
    }
    
    /* 如果需要返回数据包 */
    if ( s_tPrtclWireless.ucRTN != NO_RETURN && s_tPrtclWireless.ucAddr != BRODCAST_ADR )
    {
        PackSendData();
    }
    
    return;
}

/****************************************************************************
* 函数名称：GetDataRTN()
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：判断响应数据包的RTN值
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
Static BYTE GetDataRTN( void )
{
    WORD    wChkSum;
    WORD    i, j;
    BYTE    ucLChkSum;

    s_tPrtclWireless.ucLCHKSUM   = (s_tPrtclWireless.aucRecBuf[5]>>4)&0x0F;
    s_tPrtclWireless.aucRecBuf[5]    = s_tPrtclWireless.aucRecBuf[5]&0x0F;
    s_tPrtclWireless.wRecLenid       = GetInt16Data( &s_tPrtclWireless.aucRecBuf[5] );

    /* 累加和判断 */
    wChkSum = GetInt16Data( &s_tPrtclWireless.aucRecBuf[7+s_tPrtclWireless.wRecLenid/2] );
    
    /* CHKSUM的计算是除SOI、EOI和CHKSUM外，其它字符按ASCII码值累加求和，所得结果模65536余数取反加1 */
    j   = 2*6+1+s_tPrtclWireless.wRecLenid;
    for ( i = 1; i < j; i++ )
    {
        wChkSum  = wChkSum + s_tGprsProtocol.aucRecDataBuffCache[i];//需要使用原始ASCII数据
    }

    /* 校验和出错，返回RTN_WRONG_CHKSUM */
    if ( 0 != wChkSum )
    {
        return RTN_WRONG_CHKSUM;
    }

    /* 获取数据 */  
    s_tPrtclWireless.ucVer   = s_tPrtclWireless.aucRecBuf[1];
    s_tPrtclWireless.ucAddr  = s_tPrtclWireless.aucRecBuf[2];
    s_tPrtclWireless.ucCID1  = s_tPrtclWireless.aucRecBuf[3];
    s_tPrtclWireless.ucCID2  = s_tPrtclWireless.aucRecBuf[4]; 

    if (s_tPrtclWireless.ucCID1 != CID1_BMS)
    { // 判断CID1是否正确
        return RTN_WRONG_CID1;      /* CID1出错 */
    }

    if(s_tPrtclWireless.ucCID2 == AUTO_UP_ACK)
    {
        return NO_RETURN;
    }

    /* 长度校验和判断，若错误返回长度校验错 */
    ucLChkSum = (s_tPrtclWireless.aucRecBuf[5]&0x0F)+(s_tPrtclWireless.aucRecBuf[6]&0x0F)+((s_tPrtclWireless.aucRecBuf[6]&0xF0)>>4);
    if ( (s_tPrtclWireless.wOriginLength != (WORD)(s_tPrtclWireless.wRecLenid+18))
        || (((BYTE)(16-ucLChkSum)&0x0F) != s_tPrtclWireless.ucLCHKSUM) )
    {
        return RTN_WRONG_LCHKSUM;   /* 长度校验错 */
    }

    /*  检查命令格式是否错，有返回RTN_WRONG_COMMAND，否则返回RTN_CORRECT */
    return  RTN_CORRECT;
}

/****************************************************************************
* 函数名称：DealDataCmd()
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：根据CID1和CID2，调用相应的命令处理程序
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
static void DealDataCmd(void )
{
    WORD i = 0;
    
    for ( i = 0; i < HIGH_SEND_LEN; i++ )    /* 清空响应缓冲区 */
    {
        s_tPrtclWireless.aucSendBuf[i] = 0;
    }
    
    /* 将响应包的LENID预置为0 */   
    s_tPrtclWireless.wSendLenid  = 0;
    i = 0;
    while(s_atCmdTable[i].ucCmdCode != 0x00)
    {
        if (s_atCmdTable[i].ucCmdCode == s_tPrtclWireless.ucCID2 )
        {   
            if (s_atCmdTable[i].ucDataLen != s_tPrtclWireless.wRecLenid )
            {
                if(s_tPrtclWireless.ucCID2 != SET_PARA)
                {
                    s_tPrtclWireless.ucRTN = RTN_WRONG_COMMAND;
                    return;
                }
            }
            (*s_atCmdTable[i].func)(0);
            return;
        }
        i++;
    }

    s_tPrtclWireless.ucRTN   = RTN_WRONG_CID2;
    return;
}

/****************************************************************************
* 函数名称：PackSendDataa()
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：将协议缓冲区的数据打包后放入底层发送缓冲区
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
static void PackSendData( void )
{
    CHAR    cCh1;
    WORD    i;
    WORD    wSum;
    BYTE    *p;
    BYTE    aucTmp[HIGH_SEND_LEN];
    
    p = s_tGprsProtocol.aucSndDataBuffCache;
    rt_memset(s_tGprsProtocol.aucSndDataBuffCache, 0, sizeof(s_tGprsProtocol.aucSndDataBuffCache));
    
    p[0]        = SOI;                  
    p++;
    
    aucTmp[0]   = s_tPrtclWireless.ucVer;   
    aucTmp[1]   = s_tPrtclWireless.ucAddr;    
    aucTmp[2]   = s_tPrtclWireless.ucCID1;      
    aucTmp[3]   = s_tPrtclWireless.ucRTN;        
    
    /* LENGTH=LCHKSUM+LENID */
    *(WORD*)&aucTmp[4]  = s_tPrtclWireless.wSendLenid;
    ExchangeIntHighLowByte( &aucTmp[4] );
    cCh1        = aucTmp[5]&0x0F;
    cCh1        += (aucTmp[5]>>4);
    cCh1        += (aucTmp[4]&0x0F);
    cCh1        &= 0x0F;
    cCh1        = 16-cCh1;
    aucTmp[4]   += cCh1*16;
    
    /* INFO */
    rt_memcpy( &aucTmp[6], s_tPrtclWireless.aucSendBuf, s_tPrtclWireless.wSendLenid/2 );

    /* 16进制转ASCII码 */
    for ( i = 0; i < 6+s_tPrtclWireless.wSendLenid/2; i++ )
    {
        *p++ = HexToASCII( aucTmp[i] >> 4 );
        *p++ = HexToASCII( aucTmp[i] & 0x0F );
    }

    /* CHKSUM */
    wSum    = 0;
    for ( i = 1; i <= s_tPrtclWireless.wSendLenid + 12; i++ )
    {
        wSum += s_tGprsProtocol.aucSndDataBuffCache[i];
    }
    wSum    = (~wSum)+1;
    for ( i = 0; i < 4; i++ )
    {
        *p++    = HexToASCII( (wSum >> (12-i*4)) & 0x0F );
    }
    
    *p    = EOI;      /* EOI */
    
    /* 置底层发送缓冲区头、尾指针 */
    s_tGprsProtocol.wSndDataCacheLen = 18 + s_tPrtclWireless.wSendLenid;//1+8*2+1+Lenid
    ChangeIntoGprsFrm();
    return;
}

/****************************************************************************
* 函数名称：InitPrtclWirelessData()
* 调    用：
* 被 调 用：在InitVariable()中调用
* 输入参数：无
* 返 回 值：无
* 功能描述：初始化通讯协议数据
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
void InitPrtclWirelessData(void)
{
    rt_memset((BYTE *)&s_tGprsBuf,     0, sizeof(s_tGprsBuf));
    rt_memset((BYTE *)&s_tPrtclWireless, 0, sizeof(s_tPrtclWireless));
    rt_memset((BYTE *)&s_tGprsProtocol, 0, sizeof(s_tGprsProtocol));

    read4GTraffic(&s_t4GTrafficTotail);
    s_uiUp4GTrafficBk = s_t4GTrafficTotail.s_tUp4GTraffic.uiByteTotail;
    s_uiDown4GTrafficBk = s_t4GTrafficTotail.s_tDown4GTraffic.uiByteTotail;
    return;
}

static void SendData(BYTE ucPort)
{
    BYTE ucNum = 0;//用户自定义数量
    WORD wSendLen = (24 + ucNum*2 )*2;
    BYTE *p = NULL;
    T_GpsData tGps = {0};
    T_GPSPositionStruct tGPSPos = {0};
    T_GPSDirectionStruct tGpsDirect = {0};
    WORD wAzimuth = 0;

    if(getCurrentPlace(&tGps) == FALSE)
    {
        GetGpsInfo(&tGPSPos, &tGpsDirect);
        tGps.fLatitude = tGPSPos.fLatitude;
        tGps.fLongtitude = tGPSPos.fLongtitude;
        tGps.ucLatiDirect = tGpsDirect.ucLatiDirect;
        tGps.ucLongDirect = tGpsDirect.ucLongDirect;
    }

    p = s_tPrtclWireless.aucSendBuf;
    rt_memset(s_tPrtclWireless.aucSendBuf, 0, sizeof(s_tPrtclWireless.aucSendBuf));

    *(WORD*)p  = WordHost2Modbus((WORD*)&tGps.wYear);
    p += 2;
    *p++  = tGps.ucMonth;
    *p++  = tGps.ucDay;
    *p++  = tGps.ucHour;
    *p++  = tGps.ucMinute;
    *p++  = tGps.ucSecond;

    *p++ = tGps.ucLatiDirect;
    *(INT32S*)p = INT32SToHost(tGps.fLatitude * 100000);
    p += 4;

    *p++ = tGps.ucLongDirect;
    *(INT32S*)p = INT32SToHost(tGps.fLongtitude * 100000);
    p += 4;

    *(INT32S*)p = INT32SToHost(tGps.fSpeed * 100);
    p += 4;
    wAzimuth = tGps.fAzimuth * 100;
    *(WORD*)p = WordHost2Modbus((WORD*)&wAzimuth);
    p += 2;

    *p++ = ucNum;//用户自定义数量
    p = p + ucNum*2;
    *p = 0; //to solve KW4 problems
    s_tPrtclWireless.wSendLenid = wSendLen;
    return;
}

static INT32S  INT32SToHost(INT32S Temp)
{
    return ((Temp & 0xFF) << 24) | ((Temp & 0xFF00)<< 8)
                          | ((Temp & 0xFF0000) >> 8) | ((Temp & 0xFF000000) >> 24);
}

static void SendAlarm(BYTE ucPort)
{
    BYTE ucNum = 0;//用户自定义数量
    WORD wSendLen = (3 + ucNum)*2;
    BYTE *p = NULL;
    T_BCMAlarmStruct    tBCMAlm;
    rt_memset(&tBCMAlm, 0, sizeof(T_BCMAlarmStruct)); 
    GetRealAlarm(BCM_ALARM, (BYTE *)&tBCMAlm);
    
    p = s_tPrtclWireless.aucSendBuf;
    rt_memset(s_tPrtclWireless.aucSendBuf, 0, sizeof(s_tPrtclWireless.aucSendBuf));

    *p++ = tBCMAlm.ucShakeAlarm;        //振动告警
    *p++ = tBCMAlm.ucBattLoseAlm;       //电池锁死告警

    *p++ = ucNum;//用户自定义数量
    p = p + ucNum;

    *p = 0; //to solve KW4 problems

    s_tPrtclWireless.wSendLenid  = wSendLen;
    return;
}

static void SendPara(BYTE ucPort)
{
    BYTE ucNum = 0;//用户自定义数量
    WORD wSendLen = (125 + ucNum*2)*2;
    BYTE *p = NULL;

    rt_memset(&s_tSysPara, 0, sizeof(T_SysPara));    
    GetSysPara(&s_tSysPara);
    
    p = s_tPrtclWireless.aucSendBuf;
    rt_memset(s_tPrtclWireless.aucSendBuf, 0, sizeof(s_tPrtclWireless.aucSendBuf));

    *p++  = s_tSysPara.bGyroscopeSensitivity;         // 陀螺仪灵敏度
    *p++  = s_tSysPara.bVibrationAlarmEn;            //振动使能
    *(WORD*)p  = WordHost2Modbus((WORD*)&s_tSysPara.wSoftAntiTheftDelay); //软件防盗延时
    p += 2;
    MemsetBuff(p, s_tSysPara.acAlmPhoneNum_1, 20, 20, 0x00);  //手机号1
    p += 20;
    MemsetBuff(p, s_tSysPara.acAlmPhoneNum_2, 20, 20, 0x00);  //手机号2
    p += 20;
    MemsetBuff(p, s_tSysPara.acAlmPhoneNum_3, 20, 20, 0x00);  //手机号3
    p += 20;
    MemsetBuff(p, s_tSysPara.acAlmPhoneNum_4, 20, 20, 0x00);  //手机号4
    p += 20;
    MemsetBuff(p, s_tSysPara.acAlmPhoneNum_5, 20, 20, 0x00);  //手机号5
    p += 20;
    MemsetBuff(p, s_tSysPara.acDeviceName, 20, 20, 0x00);  //站点名称
    p += 20;

    *p++ = ucNum;//用户自定义数量
    p = p + ucNum * 2;

    *p = 0; //to solve KW4 problems
    s_tPrtclWireless.wSendLenid  = wSendLen;
    return;
}

static void RecPara(BYTE ucPort)
{
    BYTE ucPara;
    WORD wPara;
    GetSysPara( &s_tSysPara );

    s_tPrtclWireless.ucCommandType = s_tPrtclWireless.aucRecBuf[7];

    switch (s_tPrtclWireless.ucCommandType)
    {
        case 0x80: //陀螺仪灵敏度
            ucPara = s_tPrtclWireless.aucRecBuf[8];
            s_tSysPara.bGyroscopeSensitivity = ucPara;
            break;
        case 0x81: //振动使能
            ucPara = s_tPrtclWireless.aucRecBuf[8];
            s_tSysPara.bVibrationAlarmEn = ucPara;
            break;
        case 0x82:
            wPara = WordHost2Modbus((WORD*)(s_tPrtclWireless.aucRecBuf+8));
            s_tSysPara.wSoftAntiTheftDelay = wPara;
            break;
        case 0x83:
            ParaStrCpy(s_tSysPara.acAlmPhoneNum_1, (s_tPrtclWireless.aucRecBuf+8), sizeof(s_tSysPara.acAlmPhoneNum_1)); 
            break;
        case 0x84:
            ParaStrCpy(s_tSysPara.acAlmPhoneNum_2, (s_tPrtclWireless.aucRecBuf+8), sizeof(s_tSysPara.acAlmPhoneNum_2)); 
            break;
        case 0x85:
            ParaStrCpy(s_tSysPara.acAlmPhoneNum_3, (s_tPrtclWireless.aucRecBuf+8), sizeof(s_tSysPara.acAlmPhoneNum_3));             
            break;
        case 0x86:
               ParaStrCpy(s_tSysPara.acAlmPhoneNum_4, (s_tPrtclWireless.aucRecBuf+8), sizeof(s_tSysPara.acAlmPhoneNum_4)); 
            break;
        case 0x87:
            ParaStrCpy(s_tSysPara.acAlmPhoneNum_5, (s_tPrtclWireless.aucRecBuf+8), sizeof(s_tSysPara.acAlmPhoneNum_5)); 
            break;
        case 0x88://站点名称
            ParaStrCpy(s_tSysPara.acDeviceName, (s_tPrtclWireless.aucRecBuf+8), 20);
            break;

        default:
            s_tPrtclWireless.ucRTN  = RTN_WRONG_COMMAND;    
            return;
    }
    
    if ( False == SetSysPara( &s_tSysPara, True, CHANGE_BY_GPRS ) )
    {
        s_tPrtclWireless.ucRTN   = RTN_INVALID_DATA; 
    }

    return;
}

static void	RecRemoteCtrl( BYTE ucPort )
{
    s_tPrtclWireless.ucCommandType = s_tPrtclWireless.aucRecBuf[7];
    s_tPrtclWireless.ucRTN = RTN_CORRECT;

    switch (s_tPrtclWireless.ucCommandType)
    {
        case CLEAR_BATT_LOCK:
            GprsClearBattTheftAlm();
            break;
        case SET_BATT_LOCK:
            GprsSetBattTheftAlm();
            break;
        default:
            s_tPrtclWireless.ucRTN = RTN_WRONG_COMMAND;
            break;
    }

    return;
}

/****************************************************************************
* 函数名称：AutoSendAlarmData()
* 调    用：
* 被 调 用：在AutoSendAlarm()中调用
* 输入参数：无
* 返 回 值：无
* 功能描述：发送主动告警包
* 修改记录：
* 日    期		版	本		修改人		修改摘要      
***************************************************************************/
void  AutoUpGpsData(void)
{
    BYTE ucNum = 0;//用户自定义数量
    BYTE *p = NULL;
    T_GpsData tGps = {0};
    WORD wAzimuth = 0;
    T_BCMAlarmStruct    tBCMAlm = {0};
    T_GPSPositionStruct tGPSPos = {0};
    T_GPSDirectionStruct tGpsDirect = {0};

    GetRealAlarm(BCM_ALARM, (BYTE *)&tBCMAlm);

    if(getCurrentPlace(&tGps) == FALSE)
    {
        GetGpsInfo(&tGPSPos, &tGpsDirect);
        tGps.fLatitude = tGPSPos.fLatitude;
        tGps.fLongtitude = tGPSPos.fLongtitude;
        tGps.ucLatiDirect = tGpsDirect.ucLatiDirect;
        tGps.ucLongDirect = tGpsDirect.ucLongDirect;
    }

    s_tPrtclWireless.ucCID1	= CID1_BMS;
    s_tPrtclWireless.ucRTN	= AUTO_UP_DATA;
    s_tPrtclWireless.ucVer = WIRELESS_PRTCL_VER;
    s_tPrtclWireless.ucAddr = GetBMSAddr();

    p = s_tPrtclWireless.aucSendBuf;
    rt_memset(s_tPrtclWireless.aucSendBuf, 0, sizeof(s_tPrtclWireless.aucSendBuf));

    *(WORD*)p  = WordHost2Modbus((WORD*)&tGps.wYear);
    p += 2;
    *p++  = tGps.ucMonth;
    *p++  = tGps.ucDay;
    *p++  = tGps.ucHour;
    *p++  = tGps.ucMinute;
    *p++  = tGps.ucSecond;

    *p++ = tBCMAlm.ucShakeAlarm;        //振动告警
    *p++ = tBCMAlm.ucBattLoseAlm;       //电池锁死告警

    *p++ = tGps.ucLatiDirect;
    *(INT32S*)p = INT32SToHost(tGps.fLatitude * 100000);
    p += 4;

    *p++ = tGps.ucLongDirect;
    *(INT32S*)p = INT32SToHost(tGps.fLongtitude * 100000);
    p += 4;

    *(INT32S*)p = INT32SToHost(tGps.fSpeed * 100);
    p += 4;
    wAzimuth = tGps.fAzimuth * 100;
    *(WORD*)p = WordHost2Modbus((WORD*)&wAzimuth);
    p += 2;

    *p++ = ucNum;//用户自定义数量
    p += ucNum*2;

    /* LENID */
    s_tPrtclWireless.wSendLenid  = (WORD) ((p-s_tPrtclWireless.aucSendBuf)*2);
    
    PackSendData();
    
    return;
}


Static void RecAutoUpData(BYTE ucPort)
{
    s_tPrtclWireless.ucCommandType = s_tPrtclWireless.aucRecBuf[7];
    s_tPrtclWireless.ucRTN = NO_RETURN;   //收到主动告警确认包不用回复
    if(s_tPrtclWireless.ucCommandType == AUTO_UPDATA_COMMAND_TYPE)
    {
        //RecGprsUpData();
    }
    return;
}

void SendGprsHeart(void)
{
    rt_memset(s_tGprsProtocol.aucSndDataBuffCache, 0, sizeof(s_tGprsProtocol.aucSndDataBuffCache));
    s_tGprsProtocol.wSndDataCacheLen = 0;
    ChangeIntoGprsFrm();
    return;
}

Static BOOLEAN GprsParseData(void)
{
    s_tGprsBuf.wHeaderLen = s_aucGprsRecBuf[0]*256 + s_aucGprsRecBuf[1];//HEAD_LEN
    if(s_tGprsBuf.wHeaderLen > LEN_COMM_REC_BUF || s_tGprsBuf.wHeaderLen < 5)
    {
        return False;
    }

    s_tGprsBuf.ucVer = s_aucGprsRecBuf[2];//VER
    if(s_tGprsBuf.ucVer != GPRS_PRO_VER)
    {
        return False;
    }
    s_tGprsBuf.ucFrameType = s_aucGprsRecBuf[3];//FrmType
    if(s_tGprsBuf.ucFrameType != 0x01 && s_tGprsBuf.ucFrameType != 0x02)
    {
        return False;
    }

    //DATA_LEN
    s_tGprsBuf.wDataLen = s_aucGprsRecBuf[s_tGprsBuf.wHeaderLen-1] + s_aucGprsRecBuf[s_tGprsBuf.wHeaderLen-2]*256;
    s_tGprsBuf.wRcvLen = s_tGprsBuf.wDataLen + s_tGprsBuf.wHeaderLen;
    if(s_tGprsBuf.wRcvLen > LEN_COMM_REC_BUF)
    {
        return False;
    }
    else if(s_tGprsBuf.wDataLen == 0)//此时表示收到心跳回复
    {
        return True;
    }
    if(s_aucGprsRecBuf[s_tGprsBuf.wRcvLen-1] == 0x0D)
    {
        rt_memcpy_s(g_tGprsCache.aucRecBuf, s_tGprsBuf.wRcvLen, s_aucGprsRecBuf, s_tGprsBuf.wRcvLen);
    }
    if(g_tGprsCache.aucRecBuf[s_tGprsBuf.wRcvLen - 1] == 0x00)
    {
        return False;
    }

    g_tGprsCache.wRecHeadLen = s_tGprsBuf.wHeaderLen;
    g_tGprsCache.wRecDataLen = s_tGprsBuf.wDataLen;

    DealWirelessData();
    return True;
}

Static BOOLEAN buildLink(void)
{
    INT32 iRet;
    GetSysPara(&s_tSysPara);
    struct sockaddr_in server_addr;

    if (0 == inet_aton((char *)s_tSysPara.acBackstageIpAddr, &server_addr.sin_addr))//将argv[1]中的字符串转换到地址中去
    {
        rt_kprintf("invaliod server_ip!\n");
        return FALSE;
    }

    server_addr.sin_family = AF_AT;
    server_addr.sin_port = htons(s_tSysPara.wBackstagePort);
    rt_memset_s(server_addr.sin_zero, sizeof(server_addr.sin_zero), 0, sizeof(server_addr.sin_zero));

    isocket = socket(AF_AT, SOCK_STREAM,0);

    if(isocket < 0)
    {
        return FALSE;
    }

    iRet = connect(isocket, (struct sockaddr *)&server_addr, sizeof(struct sockaddr));

    if(iRet < 0)
    {
        return FALSE;
    }

    return TRUE;
}

Static void reConnect(void)
{
    closesocket(isocket);
    rt_thread_delay(2000);
    buildLink();
}

Static BOOLEAN WirelessFaultFunc(void)
{
    if (getWirelessFaultInitFlag() == True)
    {
        if(ucCloseSocketFlag == False)
        {
            closesocket(isocket);
            ucCloseSocketFlag = True;
        }
        return True;
    }

    if(ucCloseSocketFlag == True)
    {
        buildLink();
        ucCloseSocketFlag = False;
    }

    return False;
}

void wireless_Task(void* pDev)
{
    rt_int32_t iRcvLen;
    rt_uint32_t ulCnt = 0, ulAutoCnt = 0;
    char aucTmp[LEN_COMM_REC_BUF];

    InitPrtclWirelessData();
    buildLink();

#ifndef UNITEST
    while(1)
#endif
    {
        rt_thread_delay(1000);
        rt_memset_s(aucTmp, sizeof(aucTmp), 0x00, sizeof(aucTmp));

        s_wSave4GTrafficTimeCnt++;

        if(WirelessFaultFunc() == True)
        {
        #ifndef UNITEST
            continue;
        #endif
        }

        iRcvLen = recv(isocket, aucTmp, sizeof(aucTmp), MSG_DONTWAIT);
        if (iRcvLen > 0)
        {
            s_t4GTrafficTotail.s_tDown4GTraffic.wByte += (iRcvLen + 40);       //40为TCP/IP头字节数
            if(s_t4GTrafficTotail.s_tDown4GTraffic.wByte >= 1024)
            {
                s_t4GTrafficTotail.s_tDown4GTraffic.uiByteTotail++;
                s_t4GTrafficTotail.s_tDown4GTraffic.wByte -= 1024;

                if((s_t4GTrafficTotail.s_tDown4GTraffic.uiByteTotail - s_uiDown4GTrafficBk) > 200 && (s_wSave4GTrafficTimeCnt > 3600))
                {
                    write4GTraffic(&s_t4GTrafficTotail);
                    s_uiDown4GTrafficBk = s_t4GTrafficTotail.s_tDown4GTraffic.uiByteTotail;
                    s_uiUp4GTrafficBk   = s_t4GTrafficTotail.s_tUp4GTraffic.uiByteTotail;
                    s_wSave4GTrafficTimeCnt = 0;
                }
            }

            rt_memcpy_s(s_aucGprsRecBuf, LEN_COMM_REC_BUF, aucTmp, LEN_COMM_REC_BUF);
            if (True == GprsParseData())
            {
                ulCnt = 1;
                Set4GCommDisconnect(False); // 清除4G通信断标志
            }
        #ifndef UNITEST
            continue;
        #endif
        }

		//一旦产生电池丢失告警则主动上送数据，周期可通过协议进行设置，默认30s。
        if(++ulAutoCnt  >= s_tSysPara.ucAutoLocationRefreshPeriod)
        {
            if(GetGprsAutoUpStatus() == True)
            {
				AutoUpGpsData();
            }
            ulAutoCnt = 0;
        }

        if(s_tSysPara.wHeartbeatCycle == 0)
        {
            s_tSysPara.wHeartbeatCycle = 1;
        }

        if (ulCnt % (s_tSysPara.wHeartbeatCycle*60) == 0)
        {
            SendGprsHeart();
        }
        if (ulCnt++ > (s_tSysPara.wHeartbeatCycle*180))
        {
            ulCnt = 0;
            reConnect();        //超时重连
            Set4GCommDisconnect(True); // 超时4G通信断标志置位
        }
    }
}

BOOLEAN Get4GTraffic(T_4GTrafficTotail *pDest)
{
    if (NULL == pDest)
    {
        return FALSE;
    }

    rt_memcpy_s(pDest,  sizeof(T_4GTrafficTotail), (BYTE *)&s_t4GTrafficTotail, sizeof(T_4GTrafficTotail));
    return TRUE;
}

BOOLEAN Clear4GTraffic(void)
{
    rt_memset_s(&s_t4GTrafficTotail, sizeof(T_4GTrafficTotail), 0x00, sizeof(T_4GTrafficTotail));
    write4GTraffic(&s_t4GTrafficTotail);
    return TRUE;
}

BOOLEAN Save4GTrafficTotail(void)
{
    write4GTraffic(&s_t4GTrafficTotail);
    return TRUE;
}