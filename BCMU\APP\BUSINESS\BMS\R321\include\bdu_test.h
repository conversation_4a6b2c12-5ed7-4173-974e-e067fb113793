#ifndef SOFTWARE_SRC_APP_BDUTEST_H_ 
#define SOFTWARE_SRC_APP_BDUTEST_H_ 

#ifdef __cplusplus
extern "C" {
#endif

#define RECORD_TOTAL_NUM         10
#define RECORD_POINT_MAX         23

#define SCI_REAL_SEG_LEN_ANA     20
#define SCI_REAL_SEG_LEN_STS     8
#define SCI_REAL_SEG_LEN_ALM     4
#define SCI_REAL_SEG_LEN_CRC     4

#define SCI_TEST_SEG_LEN_ANA     28
#define SCI_TEST_SEG_LEN_STS     1
#define SCI_TEST_SEG_LEN_CRC     2

#define SCI_BDUADJ_MAX_LEN       30   // 定义最大的BDU校正通道数

#define SCI_DATA_LEN_PARA    18

typedef struct
{
    int16_t     sBusPinTestVol;        // 母排Pin端电压(按功率意见更改为有符号)
    uint16_t    wHeaterTestVol;        // 加热膜电压检测电压
    uint16_t    wMidMOSTestVol1;	   // MOS管中点电压检测电压1
    uint16_t    wMidMOSTestVol2;	   // MOS管中点电压检测电压2
    uint16_t    wMidMOSTestVol3;	   // MOS管中点电压检测电压3
    uint16_t    wMidMOSTestVol4;	   // MOS管中点电压检测电压4
    int16_t     sActCircTestVol;	   // 激活电路电压检测电压(按功率意见更改为有符号)
    uint16_t    wBusRelayTestVol;	   // 母排继电器前电压检测电压
    int16_t     sBoardTemp1;           // 单板温度
    int16_t     sBoardTemp2;           // 单板温度
    int16_t     sBoardTemp3;           // 单板温度
    int16_t     sBoardTemp4;           // 单板温度
    int16_t     sConnTemp1;            // 连接器温度
    int16_t     sConnTemp2;            // 连接器温度

    uint16_t    bBusRelayTestSts : 5;  // 母排继电器驱动检测状态
    uint16_t    bWavePrtTestSts  : 1;  // 逐波保护电压测试状态
    uint16_t    bRsvd            : 2;  // 保留位

    uint16_t    wTestParaCrc;          // 测试参数校验值
}T_DCTestData;


typedef struct
{
    uint16_t awBduAdjValue[SCI_BDUADJ_MAX_LEN];     // 透传数据
    uint16_t wBduAdjNum;            // 透传的通道个数,1个通道对应2个字节
}T_DCCaliGet;

//查询/设置模块参数：
typedef struct
{
    uint16_t wBatOverVolVal;         // 电池组过压保护阈值，精度2
    uint16_t wDropVol;               // 母排掉电阈值，精度2
    uint16_t wChgCurVal;             // 当前BAT充电限电流，千分比
    uint16_t wDisChgCurVal;          // 当前BUS放电限电流，千分比
    uint16_t wChgVolVal;             // 当前充电电压，精度2
    uint16_t wDischgVolVal;          // 当前放电电压，精度2
    uint16_t wChgBusCurVal;          // 当前BUS充电限电流，千分比
    uint16_t wMaxChgPowerVal;        // 最大充电功率
    uint16_t wMaxDisChgPowerVal;     // 最大放电功率
    uint16_t wCrc;                   // 校验值
}T_DCPara;

#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif
#endif  //SOFTWARE_SRC_APP_BDUTEST_H_;
