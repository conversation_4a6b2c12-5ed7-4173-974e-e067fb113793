#include "airswitch_alarm.h"
#include <rtthread.h>
#include <math.h>
#include <time.h>
#include <string.h>
#include <stdlib.h>
#include "utils_heart_beat.h"
#include "msg.h"
#include "pin.h"
#include "data_type.h"
#include "utils_thread.h"
#include "utils_list.h"
#include "utils_thread.h"
#include "msg_id.h"
#include "realdata_save.h"
#include "softbus.h"
#include "thread_id.h"
#include "eeprom_storage.h"
#include "pin_define.h"
#include "utils_rtthread_security_func.h"
#include "realdata_id_in.h"
#include "para_id_in.h"
#include "sample.h"
#include "dev_airswitch.h"
#include "device_common.h"

// 定义静态全局变量
static airswitch_alarm_t alarm_appear_counter = {0};      // 告警产生延时计数
static airswitch_alarm_t alarm_disappear_counter = {0};   // 告警消失延时计数
Static unsigned char s_loop_off_small_current_cnt = 0;    // 回路断开还有小电流的持续时间
Static unsigned char s_controller_fault_cause_by_small_curr = CONTROLLER_FAULT_NORMAL; // 空开控制器故障告警原因


/* 静态函数声明 */
static unsigned char authorization_judge_alarm(void);
Static unsigned char judge_airswitch_alarm(void);
static unsigned char judge_airswitch_analog_alarm(void);
static unsigned char judge_airswitch_digital_alarm(void);
static unsigned char judge_airswitch_custom_alarm(void);
Static unsigned char judge_airswitch_over_current_alarm(void);
Static unsigned char judge_airswitch_contact_over_temperature_alarm(void);
Static unsigned char judge_airswitch_internal_over_temperature_alarm(void);
Static unsigned char judge_over_current_protect_alarm(void);
Static unsigned char judge_overload_protect_alarm(void);
Static unsigned char judge_airswitch_over_temperature_alarm(void);
Static unsigned char judge_over_voltage_protect_alarm(void);
static unsigned char judge_analog_alarm_appear_disappear(alarm_judge_t* alarm_judge);
static unsigned char judge_alarm_delay(unsigned char status, alarm_judge_t* alarm_judge);
Static unsigned char judge_airswitch_disconnection_alarm(void);
Static unsigned char judge_airswitch_controller_fault_alarm(void);
Static unsigned char judge_airswitch_connected_reversely_alarm(void);
Static unsigned char judge_airswitch_power_down_alarm(void);
Static unsigned char judge_low_volt_down_power_alarm(void);
Static unsigned char judge_brake_ctrl_alarm(void);
Static unsigned char if_exist_protect_alarm(void);
Static unsigned char controller_fault_appear_judge(unsigned char loop_status, unsigned char control_failed_count, float current);
Static unsigned char controller_fault_disappear_judge(unsigned char loop_status, float current);
Static unsigned char sync_alm_status(void);
Static int eeprom_save_alarm(void);
Static char read_controller_fault_reason(void);

/* 告警管理接收消息 */
static msg_map s_alm_msg_map[] = {
    {PARA_CHANGE_MSG_ID, NULL},
    {CLEAN_REAL_ALARM_MSG, NULL},
};

/* 告警判断 */
Static unsigned char judge_airswitch_alarm(void)
{
    unsigned char authorization_status = 0;
    get_one_para(SWITCH_PARA_ID_POWER_ON_AUTHORIZATION_OFFSET, &authorization_status);

    if(authorization_status == AUTHORIZATION_STATUS)
    {
        // 授权相关告警判断
        authorization_judge_alarm();
        judge_airswitch_analog_alarm();
        judge_airswitch_digital_alarm();
    }

    judge_airswitch_custom_alarm();

    return TRUE;
}

Static unsigned char sync_alm_status(void)
{
    unsigned char alm_status_val = 0;
    unsigned short i = 0;
    unsigned char status = FALSE;
    unsigned short protect_alarm_id[3] = {SWITCH_DATA_ID_DOWN_POWER_ALARM,SWITCH_DATA_ID_DISCONNECTION_ALARM,
                                                            SWITCH_DATA_ID_CLOSE_FAILURE_ALARM};
    unsigned short protect_status_id[3] ={SWITCH_DATA_ID_POWER_DOWN_ALARM_STATUS,SWITCH_DATA_ID_DISCONNECT_STATUS,
                                                            SWITCH_DATA_ID_DOWNLOAD_STATUS};
    for (i = 0; i < 3; i++)
    {
        get_one_data(protect_alarm_id[i], &alm_status_val);
        if(alm_status_val == TRUE)
        {
            set_one_data(protect_status_id[i], &alm_status_val);
        }
    }

    if(PIN_LOW == rt_pin_read(PIN_MOTOR_VSC))
    {
        set_one_data(SWITCH_DATA_ID_POWER_DOWN_ALARM_STATUS, &status);
        set_one_para(SWITCH_PARA_ID_LOW_VOLTAGE_POWER_DOWN_STATUS_OFFSET, &status,TRUE,FALSE);
    }
    return SUCCESSFUL;
}

/* 上电读取保存的告警状态 */
char init_read_eeprom_alarm(void)
{
    int ret = 0;
    unsigned char save_alarm_buf[SAVE_ALARM_INDEX_MAX] = {0};
    
    ret = eeprom_storage_read(&save_alarm_buf[0], sizeof(save_alarm_buf),PART_ALARM, TRUE);
    
    if(sizeof(save_alarm_buf) != ret)
    {
        return FALSE;
    }

    set_one_data(SWITCH_DATA_ID_DISCONNECTION_ALARM, &save_alarm_buf[DISCONNECTION_ALARM]);
    set_one_data(SWITCH_DATA_ID_DOWN_POWER_ALARM, &save_alarm_buf[DOWN_POWER_ALARM]);
    set_one_data(SWITCH_DATA_ID_CLOSE_FAILURE_ALARM, &save_alarm_buf[CLOSE_FAILURE_ALARM]);
    set_one_data(SWITCH_DATA_ID_OPENING_FAILURE_ALARM, &save_alarm_buf[OPENING_FAILURE_ALARM]);
    set_one_data(SWITCH_DATA_ID_CONTROLLER_FAILURE_ALARM, &save_alarm_buf[CONTROLLER_FAILURE_ALARM]);
    
    sync_alm_status();//同步告警状态

    return TRUE;
}

/* 掉电保存的告警状态 */
Static int eeprom_save_alarm(void)
{
    int ret = 0;
    unsigned char save_real_alarm_buf[SAVE_ALARM_INDEX_MAX] = {0}; // 保存的实时告警状态
    static unsigned char save_before_alarm_buf[SAVE_ALARM_INDEX_MAX] = {1,1,1,1,1}; // 保存的历史告警状态
    
    get_one_data(SWITCH_DATA_ID_DISCONNECTION_ALARM, &save_real_alarm_buf[DISCONNECTION_ALARM]);
    get_one_data(SWITCH_DATA_ID_DOWN_POWER_ALARM, &save_real_alarm_buf[DOWN_POWER_ALARM]);
    get_one_data(SWITCH_DATA_ID_CLOSE_FAILURE_ALARM, &save_real_alarm_buf[CLOSE_FAILURE_ALARM]);
    get_one_data(SWITCH_DATA_ID_OPENING_FAILURE_ALARM, &save_real_alarm_buf[OPENING_FAILURE_ALARM]);
    get_one_data(SWITCH_DATA_ID_CONTROLLER_FAILURE_ALARM, &save_real_alarm_buf[CONTROLLER_FAILURE_ALARM]);

    if(0 != rt_memcmp(save_real_alarm_buf,save_before_alarm_buf,sizeof(save_before_alarm_buf)))
    {
        ret = eeprom_storage_write(&save_real_alarm_buf[0], sizeof(save_real_alarm_buf),PART_ALARM, TRUE);
    }

    rt_memcpy_s(save_before_alarm_buf,sizeof(save_before_alarm_buf),save_real_alarm_buf,sizeof(save_real_alarm_buf));

    return ret;
}

char restore_default_alarm_params(void)
{
    int ret = 0;
    unsigned char status = ALARM_NOT_EXIST;
    unsigned char default_alarm_buf[SAVE_ALARM_INDEX_MAX] = {0};

    unauthorization_clear_alarm();
    // 分闸失败告警和控制器故障告警单独清除
    clear_controller_fault_alarm();
    set_one_data(SWITCH_DATA_ID_OPENING_FAILURE_ALARM, &status);

    ret = eeprom_storage_write(&default_alarm_buf[0], sizeof(default_alarm_buf), PART_ALARM, TRUE);
    return ret;
}

/* 模拟量告警判断 */
static unsigned char judge_airswitch_analog_alarm(void)
{
    judge_airswitch_over_current_alarm();
    judge_airswitch_contact_over_temperature_alarm();
    judge_airswitch_internal_over_temperature_alarm();
    if (FRAME_CURRENT_63A != judge_frame_current()) //63A壳架硬件不支持反接
    {
        judge_airswitch_connected_reversely_alarm();
    }

    return TRUE;
}

/* 授权状态才进行相应告警判断 */
static unsigned char authorization_judge_alarm(void)
{
    judge_over_current_protect_alarm();
    judge_over_voltage_protect_alarm();
    judge_overload_protect_alarm();
    judge_airswitch_over_temperature_alarm();   //空开过温保护告警
    judge_airswitch_power_down_alarm();
    judge_low_volt_down_power_alarm();      //空开低压下电告警
    judge_airswitch_disconnection_alarm();  // 空开断开告警

    return TRUE;
}

/* 数字量告警判断 */
static unsigned char judge_airswitch_digital_alarm(void)
{

    return TRUE;
}

/* 自定义告警判断 */
static unsigned char judge_airswitch_custom_alarm(void)
{
    judge_brake_ctrl_alarm();      //空开合闸分闸失败告警

    judge_airswitch_controller_fault_alarm();   //空开控制器故障告警
    return TRUE;
}

//空开过温保护告警
Static unsigned char judge_airswitch_over_temperature_alarm(void)
{
    alarm_judge_t over_temperature_protect={0};
    unsigned char loop_status;
    unsigned char airswtich_over_temperature_protect_status = 0;

    get_one_para(SWITCH_PARA_ID_OVER_TEMPERATURE_PROTECT_STATUS_OFFSET, &airswtich_over_temperature_protect_status);
    get_one_data(SWITCH_DATA_ID_LOOP_STATUS, &loop_status);

    // 获取实时告警
    get_one_data(SWITCH_DATA_ID_OVER_TEMPERATURE_PROTECT_ALARM, &over_temperature_protect.real_alarm_status);

    over_temperature_protect.status = airswtich_over_temperature_protect_status;

    if(over_temperature_protect.real_alarm_status != over_temperature_protect.status)
    {
        if(over_temperature_protect.real_alarm_status == ALARM_NOT_EXIST && loop_status == LOOP_OPEN)
        {
            over_temperature_protect.real_alarm_status = over_temperature_protect.status;
            set_one_data(SWITCH_DATA_ID_OVER_TEMPERATURE_PROTECT_ALARM, &over_temperature_protect.real_alarm_status);
        }
        else if(over_temperature_protect.real_alarm_status == ALARM_EXIST && loop_status == LOOP_CLOSE)
        {
            over_temperature_protect.real_alarm_status = over_temperature_protect.status;
            set_one_data(SWITCH_DATA_ID_OVER_TEMPERATURE_PROTECT_ALARM, &over_temperature_protect.real_alarm_status);
        }
    }

    return TRUE;
}

//空开过压保护告警
Static unsigned char judge_over_voltage_protect_alarm(void)
{
    alarm_judge_t over_voltage_protect={0};
    unsigned char loop_status;
    unsigned char airswtich_over_voltage_protect_status = 0;

    //获取保护状态
    get_one_para(SWITCH_PARA_ID_OVER_VOLTAGE_PROTECT_STATUS_OFFSET, &airswtich_over_voltage_protect_status);
    // 获取实时数据
    get_one_data(SWITCH_DATA_ID_LOOP_STATUS, &loop_status);

    // 获取实时告警
    get_one_data(SWITCH_DATA_ID_OVER_VOLTAGE_PROTECT_ALARM, &over_voltage_protect.real_alarm_status);

    over_voltage_protect.status = airswtich_over_voltage_protect_status;

    if(over_voltage_protect.real_alarm_status != over_voltage_protect.status)
    {
        if(over_voltage_protect.real_alarm_status == ALARM_NOT_EXIST && loop_status == LOOP_OPEN)
        {
            over_voltage_protect.real_alarm_status = over_voltage_protect.status;   //告警产生
            set_one_data(SWITCH_DATA_ID_OVER_VOLTAGE_PROTECT_ALARM, &over_voltage_protect.real_alarm_status);
        }
        else if(over_voltage_protect.real_alarm_status == ALARM_EXIST && loop_status == LOOP_CLOSE)
        {
            over_voltage_protect.real_alarm_status = over_voltage_protect.status;   //告警恢复
            set_one_data(SWITCH_DATA_ID_OVER_VOLTAGE_PROTECT_ALARM, &over_voltage_protect.real_alarm_status);
        }
    }

    return TRUE;
}

/* 空开过流告警 */
Static unsigned char judge_airswitch_over_current_alarm(void)
{
    alarm_judge_t over_current_alarm={0};
    unsigned char status;
    float value = 0,threshold = 0;

    // 获取实时数据
    get_one_data(SWITCH_DATA_ID_CURRENT, &value);
    // 获取告警参数
    get_one_para(SWITCH_PARA_ID_OVER_CURRENT_ALARM_THRESHOLD_OFFSET, &threshold);
    // 获取实时告警
    get_one_data(SWITCH_DATA_ID_OVER_CURRENT_ALARM, &over_current_alarm.real_alarm_status);

    over_current_alarm.value = fabsf(value);
    over_current_alarm.threshold = fabsf(threshold);
    over_current_alarm.value_add = OVER_CURRENT_ALARM_ADD * over_current_alarm.threshold;   // 回差
    over_current_alarm.appear_counter_threshold = 1*ONE_SECOND_TICK;       // 产生延时1S
    over_current_alarm.disappear_counter_threshold = 5*ONE_SECOND_TICK;    // 消失延时5S
    over_current_alarm.appear_counter = &alarm_appear_counter.airswitch_over_current_alarm;       // 产生计数
    over_current_alarm.disappear_counter = &alarm_disappear_counter.airswitch_over_current_alarm;  // 消失计数

    // 判断实时告警
    status = judge_analog_alarm_appear_disappear(&over_current_alarm);
    // 延时判断后的告警状态
    status = judge_alarm_delay(status, &over_current_alarm);

    if(status != over_current_alarm.real_alarm_status)
    {
        over_current_alarm.real_alarm_status = status;
        set_one_data(SWITCH_DATA_ID_OVER_CURRENT_ALARM, &over_current_alarm.real_alarm_status);
    }

    return status;
}

/* 空开触点过温告警 */
Static unsigned char judge_airswitch_contact_over_temperature_alarm(void)
{
    alarm_judge_t contact_over_temperature_alarm={0};
    unsigned char status;

    // 获取实时数据
    get_one_data(SWITCH_DATA_ID_CONTACT_TEMPERATURE, &contact_over_temperature_alarm.value);
    // 获取告警参数
    get_one_para(SWITCH_PARA_ID_CONTACT_OVER_TEMPERATURE_ALARM_THRESHOLD_OFFSET, &contact_over_temperature_alarm.threshold);
    // 获取实时告警
    get_one_data(SWITCH_DATA_ID_CONTACT_OVER_TEMPERATURE_ALARM, &contact_over_temperature_alarm.real_alarm_status);

    contact_over_temperature_alarm.value_add = CONTACT_OVER_TEMPERATURE_ADD;   // 回差
    contact_over_temperature_alarm.appear_counter_threshold = 10*ONE_SECOND_TICK;       // 产生延时10S
    contact_over_temperature_alarm.disappear_counter_threshold = 5*ONE_SECOND_TICK;    // 消失延时5S
    contact_over_temperature_alarm.appear_counter = &alarm_appear_counter.airswitch_contact_over_temperature_alarm;        // 产生计数
    contact_over_temperature_alarm.disappear_counter = &alarm_disappear_counter.airswitch_contact_over_temperature_alarm;  // 消失计数

    // 判断实时告警
    status = judge_analog_alarm_appear_disappear(&contact_over_temperature_alarm);
    // 延时判断过的告警状态
    status = judge_alarm_delay(status, &contact_over_temperature_alarm);

    if(status != contact_over_temperature_alarm.real_alarm_status)
    {
        contact_over_temperature_alarm.real_alarm_status = status;
        set_one_data(SWITCH_DATA_ID_CONTACT_OVER_TEMPERATURE_ALARM, &contact_over_temperature_alarm.real_alarm_status);
    }
    return status;
}


/* 空开内部过温告警 */
Static unsigned char judge_airswitch_internal_over_temperature_alarm(void)
{
    alarm_judge_t internal_over_temperature_alarm={0};
    unsigned char status;

    // 获取实时数据
    get_one_data(SWITCH_DATA_ID_INTERNAL_TEMPERATURE, &internal_over_temperature_alarm.value);
    // 获取告警参数
    get_one_para(SWITCH_PARA_ID_INTERNAL_OVER_TEMPERATURE_ALARM_THRESHOLD_OFFSET, &internal_over_temperature_alarm.threshold);
    // 获取实时告警
    get_one_data(SWITCH_DATA_ID_INTERNAL_OVER_TEMPERATURE_ALARM, &internal_over_temperature_alarm.real_alarm_status);

    internal_over_temperature_alarm.value_add = INTERNAL_OVER_TEMPERATURE_ADD;   // 回差
    internal_over_temperature_alarm.appear_counter_threshold = 10*ONE_SECOND_TICK;       // 产生延时10S
    internal_over_temperature_alarm.disappear_counter_threshold = 5*ONE_SECOND_TICK;    // 消失延时5S
    internal_over_temperature_alarm.appear_counter = &alarm_appear_counter.airswitch_internal_over_temperature_alarm;        // 产生计数
    internal_over_temperature_alarm.disappear_counter = &alarm_disappear_counter.airswitch_internal_over_temperature_alarm;  // 消失计数

    // 判断实时告警
    status = judge_analog_alarm_appear_disappear(&internal_over_temperature_alarm);
    // 延时判断过的告警状态
    status = judge_alarm_delay(status, &internal_over_temperature_alarm);

    if(status != internal_over_temperature_alarm.real_alarm_status)
    {
        internal_over_temperature_alarm.real_alarm_status = status;
        set_one_data(SWITCH_DATA_ID_INTERNAL_OVER_TEMPERATURE_ALARM, &internal_over_temperature_alarm.real_alarm_status);
    }
    return status;
}


/* 空开接反告警 */
Static unsigned char judge_airswitch_connected_reversely_alarm(void)
{
    static unsigned char status_delay = 0;
    unsigned char reversely_status,alm_status;

    // 获取实时数据
    get_one_data(SWITCH_DATA_ID_SWITCH_POWER_REVERSE_STATUS, &reversely_status);  //获取反接状态
    // 获取实时告警
    get_one_data(SWITCH_DATA_ID_CONNECTED_REVERSELY_ALARM, &alm_status);  //获取反接告警

   if(alm_status == ALARM_NOT_EXIST)
    {
        // 告警不存在，判断告警产生
        if(reversely_status == TRUE)
        {   
            if(status_delay++ >= ONE_SECOND_TICK)//告警延时
            {
                set_one_data(SWITCH_DATA_ID_CONNECTED_REVERSELY_ALARM,&reversely_status);//更新告警TRUE
                status_delay = 0;
            }
            
        }
        else
        {
            status_delay = 0;
        }
    }
    else
    {
        //告警存在，判断告警消失
        if(reversely_status == FALSE)
        {
            set_one_data(SWITCH_DATA_ID_CONNECTED_REVERSELY_ALARM,&reversely_status);//更新告警FALSE
        }
    }

    return TRUE;
}

/* 空开下电告警 */
Static unsigned char judge_airswitch_power_down_alarm(void)
{
    alarm_judge_t power_down_alarm={0};

    // 获取实时告警
    get_one_data(SWITCH_DATA_ID_DOWN_POWER_ALARM, &power_down_alarm.real_alarm_status);
    // 获取实时告警状态
    get_one_data(SWITCH_DATA_ID_POWER_DOWN_ALARM_STATUS, &power_down_alarm.status);


    if(power_down_alarm.status != power_down_alarm.real_alarm_status)
    {
        power_down_alarm.real_alarm_status = power_down_alarm.status;
        set_one_data(SWITCH_DATA_ID_DOWN_POWER_ALARM, &power_down_alarm.real_alarm_status);
    }
    return TRUE;
}

/* 空开过流保护告警判断 */
Static unsigned char judge_over_current_protect_alarm(void)
{
    unsigned char protect_status,loop_status,alm_status; 
    // 获取实时数据
    get_one_data(SWITCH_DATA_ID_LOOP_STATUS,&loop_status);//获取回路状态
    get_one_data(SWITCH_DATA_ID_OVER_CURRENT_PROTECT_ALARM,&alm_status);//获取告警
    //获取空开过流保护状态
    get_one_para(SWITCH_PARA_ID_OVER_CURRENT_PROTECT_STATUS_OFFSET, &protect_status); 

    if(alm_status == ALARM_NOT_EXIST)
    {
        // 告警不存在，判断告警产生
        if((protect_status == 1) && (loop_status == 0))
        {
            set_one_data(SWITCH_DATA_ID_OVER_CURRENT_PROTECT_ALARM,&protect_status);//更新告警TRUE
        }
    }
    else
    {
        //告警存在，判断告警消失
        if((protect_status == 0) && (loop_status == 1))
        {
            set_one_data(SWITCH_DATA_ID_OVER_CURRENT_PROTECT_ALARM,&protect_status);//更新告警FALSE
        }
    }
    return TRUE;
}

/**
 * @brief 空开低压下电告警
 * @param[in]
 * @retval
 * @note
 */
Static unsigned char judge_low_volt_down_power_alarm(void)
{
    alarm_judge_t over_current_alarm={0};
    unsigned char loop_status = 0;
    unsigned char download_status = AIRSWITCH_POWER_DOWN;
    unsigned char power_down_status = 0;
    
    get_one_para(SWITCH_PARA_ID_LOW_VOLTAGE_POWER_DOWN_STATUS_OFFSET, &power_down_status); // 获取低压下电状态
    // 获取实时告警
    get_one_data(SWITCH_DATA_ID_LOW_VOLTAGE_DOWN_POWER_ALARM, &over_current_alarm.real_alarm_status);
    // 获取实时数据
    get_one_data(SWITCH_DATA_ID_LOOP_STATUS,&loop_status);

    if(loop_status == LOOP_OPEN && power_down_status)
    {
        over_current_alarm.real_alarm_status = ALARM_EXIST;
        set_one_data(SWITCH_DATA_ID_LOW_VOLTAGE_DOWN_POWER_ALARM, &over_current_alarm.real_alarm_status);
        set_one_data(SWITCH_DATA_ID_DOWNLOAD_STATUS,&download_status);
        set_one_para(SWITCH_PARA_ID_POWER_ON_POWER_DOWN_COMMAND_OFFSET, &download_status, TRUE, FALSE);
    }
    else if(loop_status == LOOP_CLOSE)
    {
        over_current_alarm.real_alarm_status = ALARM_NOT_EXIST;
        set_one_data(SWITCH_DATA_ID_LOW_VOLTAGE_DOWN_POWER_ALARM, &over_current_alarm.real_alarm_status);
    }
    return TRUE;
}

/**
 * @brief 空开合闸分闸失败告警
 * @param[in]
 * @retval
 * @note
 */
Static unsigned char judge_brake_ctrl_alarm(void)
{
    unsigned char loop_status = 0;
    unsigned char opening_failure = 0;
    unsigned char close_failure = 0;
    unsigned char real_alarm_status = ALARM_NOT_EXIST;
    
    // 获取实时告警
    get_one_data(SWITCH_DATA_ID_OPENING_FAILURE_ALARM, &opening_failure);
    get_one_data(SWITCH_DATA_ID_CLOSE_FAILURE_ALARM, &close_failure);
    // 获取实时数据
    get_one_data(SWITCH_DATA_ID_LOOP_STATUS,&loop_status);

    if(close_failure == ALARM_EXIST && loop_status == LOOP_CLOSE)
    {
        set_one_data(SWITCH_DATA_ID_CLOSE_FAILURE_ALARM, &real_alarm_status);
    }
    else if(opening_failure == ALARM_EXIST && loop_status == LOOP_OPEN)
    {
        set_one_data(SWITCH_DATA_ID_OPENING_FAILURE_ALARM, &real_alarm_status);
    }
    return TRUE;
}

/* 空开断开告警 */
Static unsigned char judge_airswitch_disconnection_alarm(void)
{
    static unsigned char airswitch_disconnect_real_alarm_status = 0;
    unsigned char disconnect_alarm_status = 0;

    if(if_exist_protect_alarm())
    {
        return FALSE;
    }

    // 获取实时数据
    get_one_data(SWITCH_DATA_ID_DISCONNECT_STATUS,&disconnect_alarm_status);
    // 获取实时告警
    get_one_data(SWITCH_DATA_ID_DISCONNECTION_ALARM, &airswitch_disconnect_real_alarm_status);
    if(airswitch_disconnect_real_alarm_status == ALARM_NOT_EXIST)   //告警产生判断
    {
        if(disconnect_alarm_status == FAULT)
        {
            set_one_data(SWITCH_DATA_ID_DISCONNECTION_ALARM, &disconnect_alarm_status);
        }
    }
    else    // 告警恢复判断
    {
        if(disconnect_alarm_status == NORMAL)
        {
            set_one_data(SWITCH_DATA_ID_DISCONNECTION_ALARM, &disconnect_alarm_status);
        }
    }

    return airswitch_disconnect_real_alarm_status;
}

/// @brief 控制器故障告警判断
/// @param void
/// @return
Static unsigned char judge_airswitch_controller_fault_alarm(void)
{
    unsigned char airswitch_controller_fault_alarm_status = ALARM_NOT_EXIST;
    unsigned char control_failed_count = 0;
    unsigned char current_loop_status = LOOP_OPEN;
    float current = 0.0;

    get_one_data(SWITCH_DATA_ID_LOOP_STATUS, &current_loop_status);
    get_one_data(SWITCH_DATA_ID_CURRENT, &current);
    get_one_data(SWITCH_DATA_ID_CONTROLLER_FAILURE_ALARM, &airswitch_controller_fault_alarm_status);

    // 告警产生判断
    if (airswitch_controller_fault_alarm_status == ALARM_NOT_EXIST)
    {
        // 获取3+1控制失败次数
        get_one_data(SWITCH_DATA_ID_ACTION_COUNT, &control_failed_count);
        airswitch_controller_fault_alarm_status = controller_fault_appear_judge(current_loop_status, control_failed_count, current);
        if (airswitch_controller_fault_alarm_status == ALARM_EXIST)
        {
            set_one_data(SWITCH_DATA_ID_CONTROLLER_FAILURE_ALARM, &airswitch_controller_fault_alarm_status);
        }
    }
    // 告警恢复判断
    else
    {
        // 由于分闸失败或者合闸失败引起的控制器故障告警，其恢复直接在分闸失败告警和合闸失败告警的恢复逻辑中处理。
        // 以下告警恢复逻辑针对空开断3秒小电流引起的控制器故障告警
        airswitch_controller_fault_alarm_status = controller_fault_disappear_judge(current_loop_status, current);
        if(airswitch_controller_fault_alarm_status == ALARM_NOT_EXIST)
        {
            clear_controller_fault_alarm();
        }
    }
    return airswitch_controller_fault_alarm_status;
}

/// @brief 控制器故障告警产生判断
/// @param loop_status 回路状态
/// @param control_failed_count 3+1动作失败次数
/// @param current 电流
/// @return 控制器故障告警
Static unsigned char controller_fault_appear_judge(unsigned char loop_status, unsigned char control_failed_count, float current)
{
    unsigned char airswitch_controller_fault_alarm_status = ALARM_NOT_EXIST;
    // 控制失败次数达到3次，则将空开控制器故障告警置位；
    if (AIRSWITCH_MAX_CONTROL_COUNT == control_failed_count)
    {
        airswitch_controller_fault_alarm_status = ALARM_EXIST;
        // 一旦airswitch_controller_fault_alarm_status置位，中间计数s_loop_off_small_current_cnt就得清零，避免下次判断异常。
        s_loop_off_small_current_cnt = 0;
        if (LOOP_OPEN == loop_status)
        {
            s_controller_fault_cause_by_small_curr = CONTROLLER_FAULT_CLOSE_FAULT;
            set_one_para(SWITCH_PARA_ID_CONTROLLER_FAULT_REASON_OFFSET, &s_controller_fault_cause_by_small_curr, TRUE, FALSE);
        }
        else
        {
            s_controller_fault_cause_by_small_curr = CONTROLLER_FAULT_OPEN_FAULT;
            set_one_para(SWITCH_PARA_ID_CONTROLLER_FAULT_REASON_OFFSET, &s_controller_fault_cause_by_small_curr, TRUE, FALSE);
        }
        return airswitch_controller_fault_alarm_status;
    }

    // 如果空开处于断开状态，并且连续3秒内检测到电流绝对值大于1.2A则将空开控制器故障告警置位
    if ((LOOP_OPEN == loop_status) && (fabs(current) > 1.2))
    {
        s_loop_off_small_current_cnt += 1;
        if (s_loop_off_small_current_cnt * ALM_TICK_DELAY > 3000)
        {
            s_controller_fault_cause_by_small_curr = CONTROLLER_FAULT_SMALL_CURR;
            s_loop_off_small_current_cnt = 0;
            airswitch_controller_fault_alarm_status = ALARM_EXIST;
            // 将小电流引起的空开控制器故障告警进行保存
            set_one_para(SWITCH_PARA_ID_CONTROLLER_FAULT_REASON_OFFSET, &s_controller_fault_cause_by_small_curr, TRUE, FALSE);
        }
    }
    else
    {
        s_loop_off_small_current_cnt = 0;
    }

    return airswitch_controller_fault_alarm_status;
}

/// @brief 控制器故障告警恢复判断
/// @param loop_status 回路状态
/// @param current 电流
/// @return 控制器故障告警
Static unsigned char controller_fault_disappear_judge(unsigned char loop_status, float current)
{
    unsigned char airswitch_controller_fault_alarm_status = ALARM_EXIST;
    // 如果是空开断开后连续3秒小电流判断出的控制器故障告警，那么电流绝对值小于等于1.2A持续3秒，则告警消失。
    // 处于合闸状态的情况下也能恢复对应告警。
    if (s_controller_fault_cause_by_small_curr == CONTROLLER_FAULT_SMALL_CURR)
    {
        if (fabs(current) <= 1.2)
        {
            s_loop_off_small_current_cnt += 1;
            if (s_loop_off_small_current_cnt * ALM_TICK_DELAY > 3000)
            {
                s_loop_off_small_current_cnt = 0;
                airswitch_controller_fault_alarm_status = ALARM_NOT_EXIST;
            }
        }
        if (LOOP_CLOSE == loop_status)
        {
            airswitch_controller_fault_alarm_status = ALARM_NOT_EXIST;
        }
    }
    else if(s_controller_fault_cause_by_small_curr == CONTROLLER_FAULT_CLOSE_FAULT)
    {
        if (LOOP_CLOSE == loop_status)
        {
            airswitch_controller_fault_alarm_status = ALARM_NOT_EXIST;
        }
    }
    else if(s_controller_fault_cause_by_small_curr == CONTROLLER_FAULT_OPEN_FAULT)
    {
        if (LOOP_OPEN == loop_status)
        {
            airswitch_controller_fault_alarm_status = ALARM_NOT_EXIST;
        }
    }
    return airswitch_controller_fault_alarm_status;
}

/* 空开过载跳闸保护告警判断 */
Static unsigned char judge_overload_protect_alarm(void)
{
    unsigned char protect_status, loop_status, alm_status;
    // 获取空开过载跳闸保护状态
    get_one_para(SWITCH_PARA_ID_OVERLOAD_TRIP_STATUS_OFFSET, &protect_status);

    get_one_data(SWITCH_DATA_ID_LOOP_STATUS, &loop_status);
    // 获取空开过载跳闸保护告警
    get_one_data(SWITCH_DATA_ID_OVERLOAD_TRIP_ALARM, &alm_status);

    if (alm_status == ALARM_NOT_EXIST)
    {
        // 过载保护状态异常并且处于分闸状态时，判断告警产生；
        if ((protect_status == FAULT) && (loop_status == LOOP_OPEN))
        {
            alm_status = ALARM_EXIST;
            set_one_data(SWITCH_DATA_ID_OVERLOAD_TRIP_ALARM, &alm_status);
        }
    }
    else
    {
        // 过载保护告警产生后如果发现已经合闸，那么告警恢复正常；
        if (loop_status == LOOP_CLOSE)
        {
            alm_status = ALARM_NOT_EXIST;
            set_one_data(SWITCH_DATA_ID_OVERLOAD_TRIP_ALARM, &alm_status);
        }
    }
    return TRUE;
}

Static unsigned char if_exist_protect_alarm(void)
{
    unsigned char airswitch_over_temperature_status = 0;
    unsigned char airswitch_over_current_status = 0;
    unsigned char airswitch_overload_trip_status = 0;
    unsigned char airswitch_low_voltage_power_down_status = 0;
    unsigned char airswitch_down_power_status = 0;
    unsigned char airswitch_disconnect_real_alarm_status = 0;

    // 获取实时数据
    get_one_data(SWITCH_DATA_ID_OVER_TEMPERATURE_PROTECT_ALARM, &airswitch_over_temperature_status);
    get_one_data(SWITCH_DATA_ID_OVER_CURRENT_PROTECT_ALARM, &airswitch_over_current_status);
    get_one_data(SWITCH_DATA_ID_OVERLOAD_TRIP_ALARM, &airswitch_overload_trip_status);
    get_one_data(SWITCH_DATA_ID_LOW_VOLTAGE_DOWN_POWER_ALARM, &airswitch_low_voltage_power_down_status);
    get_one_data(SWITCH_DATA_ID_DOWN_POWER_ALARM, &airswitch_down_power_status);

    if(airswitch_over_temperature_status  || airswitch_over_current_status 
        || airswitch_overload_trip_status || airswitch_low_voltage_power_down_status || airswitch_down_power_status
    )
    {
        set_one_data(SWITCH_DATA_ID_DISCONNECTION_ALARM, &airswitch_disconnect_real_alarm_status);
        return TRUE;
    }
    
    return FALSE;
}

/* 判断告警状态是产生还是消失 */
static unsigned char judge_analog_alarm_appear_disappear(alarm_judge_t* alarm_judge)
{
    char alarm_status = alarm_judge->real_alarm_status;
    if(alarm_judge->real_alarm_status == ALARM_NOT_EXIST)
    {
        // 告警不存在，判断告警产生
        if(alarm_judge->value_add < 0)
        {
            if(alarm_judge->value > alarm_judge->threshold)
            {
                alarm_status = ALARM_EXIST;
            }
        }
        else
        {
            if(alarm_judge->value < alarm_judge->threshold+0.00001f)    // 0.00001f是为了解决浮点数比较的问题
            {
                alarm_status = ALARM_EXIST;
            }
        }
    }
    else
    {
        // 告警存在，判断告警消失
        if(alarm_judge->value_add < 0)
        {
            if(alarm_judge->value < alarm_judge->threshold+alarm_judge->value_add)
            {
                alarm_status = ALARM_NOT_EXIST;
            }
        }
        else
        {
            if(alarm_judge->value > alarm_judge->threshold+alarm_judge->value_add-0.00001f) // 0.00001f是为了解决浮点数比较的问题
            {
                alarm_status = ALARM_NOT_EXIST;
            }
        }
    }
    return alarm_status;
}

/* 告警产生与消失延时判断 */
static unsigned char judge_alarm_delay(unsigned char status, alarm_judge_t* alarm_judge)
{
    unsigned char alarm_status = alarm_judge->real_alarm_status;
    if(alarm_judge->real_alarm_status == ALARM_NOT_EXIST)
    {
        // 产生延时判断
        if(status == ALARM_EXIST)
        {
            (*(alarm_judge->appear_counter))++;
            if(*(alarm_judge->appear_counter) >= alarm_judge->appear_counter_threshold)
            {
                alarm_status = status;
                *(alarm_judge->appear_counter) = 0;
            }
        }
        else
        {
            *(alarm_judge->appear_counter) = 0;
        }
    }
    else
    {
        // 消失延时判断
        if(status == ALARM_NOT_EXIST)
        {
            (*(alarm_judge->disappear_counter))++;
            if(*(alarm_judge->disappear_counter) >= alarm_judge->disappear_counter_threshold)
            {
                alarm_status = status;
                *(alarm_judge->disappear_counter) = 0;
            }
        }
        else
        {
            *(alarm_judge->disappear_counter) = 0;
        }
    }
    return alarm_status;
}

/// @brief 清除控制器故障告警
/// @param void
/// @return true
unsigned char clear_controller_fault_alarm(void)
{
    unsigned char alarm_status = ALARM_NOT_EXIST;
    unsigned char control_fail_count = 0;
    s_controller_fault_cause_by_small_curr = CONTROLLER_FAULT_NORMAL;
    set_one_para(SWITCH_PARA_ID_CONTROLLER_FAULT_REASON_OFFSET, &s_controller_fault_cause_by_small_curr, TRUE, FALSE);
    s_loop_off_small_current_cnt = 0;
    set_one_data(SWITCH_DATA_ID_ACTION_COUNT, &control_fail_count);
    set_one_data(SWITCH_DATA_ID_CONTROLLER_FAILURE_ALARM, &alarm_status);
    eeprom_save_alarm();
    return TRUE;
}

/**
 * @brief 告警管理初始化接口
 * @param[in]
 * @retval
 * @note 读取告警参数，初始化告警信息
 */
void* init_alarm_manage(void * param) {
    server_info_t *server_info = (server_info_t *)param;
    RETURN_VAL_IF_FAIL(server_info != NULL, NULL);
    server_info->server.server.map_size = sizeof(s_alm_msg_map) / sizeof(msg_map);
    register_server_msg_map(s_alm_msg_map, server_info);
    read_controller_fault_reason();
    return NULL;
}

/**
 * @brief 系统重启之后读取一次控制器故障告警原因
 * @param void
 * @retval
 * @note 如果是小电流引发的控制器故障告警，系统重启后需要获取该原因用于告警恢复。
 */
Static char read_controller_fault_reason(void)
{
    get_one_para(SWITCH_PARA_ID_CONTROLLER_FAULT_REASON_OFFSET, &s_controller_fault_cause_by_small_curr);
    return TRUE;
}

/**
 * @brief 告警管理周期执行主函数
 * @param[in]
 * @retval
 * @note
 */
void alarm_main(void * param) {
    while (is_running(TRUE)){
        judge_airswitch_alarm();
        eeprom_save_alarm();

        rt_thread_mdelay(ALM_TICK_DELAY);
    }
}
