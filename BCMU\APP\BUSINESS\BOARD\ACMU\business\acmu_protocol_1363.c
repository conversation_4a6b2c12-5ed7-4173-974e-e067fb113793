/*
 * @file    : acmu_protocol_modbus.c
 * @brief   : ACMU南向modbus协议实现
 * @details :
 * <AUTHOR> penglei
 * @Date    : 2024-07-18
 * @LastEditTime: 2025-09-17 16:58:35
 * @version : V0.0.1  
 */
#include <string.h>
#include <board.h>
#include "sps.h"
#include "dfs_fs.h"
#include "dev_acmu.h"
#include "his_data.h"
#include "MIBTable.h"
#include "his_record.h"
#include "realdata_id_in.h"
#include "utils_time.h"
#include "alarm_manage.h"
#include "para_id_in.h"
#include "para_manage.h"
#include "realdata_save.h"
#include "protocol_layer.h"
#include "const_define_in.h"
#include "acmu_protocol_1363.h"
#include "utils_data_transmission.h"
#include "utils_rtthread_security_func.h"
#include "pdt_version.h"
#include "alarm_register.h"
#include "alarm_mgr_api.h"
#include "log_mgr_api.h"
#include "io_control.h"
#include "sample.h"
#include "gui_data_interface.h"
#include "system_manage.h"
#include "dev_acmu.h"

Static acmu_para_value  para_value = {0};
Static unsigned char s_bufflen = 0;
Static unsigned char s_command_group = 0x00;
Static unsigned char s_command_type = 0x00;
static unsigned short s_start_index = 0;

Static char acmu_checkMenuKey(unsigned char* buff, unsigned char len);
Static char acmu_sync_north_addr(unsigned char* buff, char ret);
Static int acmu_pack_para_comm(void* dev_inst, void *cmd_buf);
Static int acmu_parse_para_comm(void* dev_inst, void *cmd_buf);
Static int acmu_pack_factory_info_custom(void* dev_inst, void* cmd_buff);
Static int acmu_pack_env_analog_info(void* dev_inst, void* cmd_buff);
Static int acmu_parse_remote_control(void* dev_inst, void *cmd_buf);
Static int acmu_pack_ac_analog_info(void* dev_inst, void *cmd_buf);
Static int acmu_pack_ac_sw_input_status(void* dev_inst, void *cmd_buf);
Static int check_ac_command_group(void* dev_inst, void *cmd_buf);
Static int acmu_pack_time(void* dev_inst, void* cmd_buff);
Static int acmu_parse_time(void* dev_inst, void* cmd_buff);
Static int acmu_pack_ac_alarm_info(void* dev_inst, void* cmd_buff);
Static int acmu_pack_env_alarm_info(void* dev_inst, void* cmd_buff);
Static int parse_his_alarm(void* dev_inst, void *cmd_buf);
Static int pack_his_alarm(void* dev_inst, void *cmd_buf);
Static signed char manual_control_out_relay(unsigned char relay_index, unsigned char control_status);
Static int get_env_para(void* dev_inst, void* cmd_buff);
Static int acmu_pack_boot_ver(void* dev_inst, void* cmd_buff);

static s1363_cmd_head_t cmd_req[] RAM_SECTION =
{
    {VER_22 , CID1_AC , CMD_GET_INRELAY_INFO},                  //0
    {VER_22 , CID1_AC , CMD_SET_INRELAY_INFO},                  //1
    {VER_22 , CID1_AC , CMD_GET_SYS_TIME},                      //2
    {VER_22 , CID1_AC , CMD_SET_SYS_TIME},                      //3
    {VER_22 , CID1_AC , CMD_REMOTE_CONTROL},                    //4
    {VER_22 , CID1_AC , CMD_GET_AC_ANALOG_INFO},                //5
    {VER_22 , CID1_AC , CMD_GET_AC_SW_INPUT_STATUS},            //6
    {VER_22 , CID1_AC , CMD_GET_ALMRELAY},                      //7
    {VER_22 , CID1_AC , CMD_SET_ALMRELAY},                      //8
    {VER_22 , CID1_AC , CMD_GET_ALMLEVEL},                      //9
    {VER_22 , CID1_AC , CMD_SET_ALMLEVEL},                      //10
    {VER_22 , CID1_AC , CMD_GET_FACTORY_INFO_CUSTOM},           //11
    {VER_22 , CID1_AC , CMD_GET_AC_ALARM_INFO},                 //12
    {VER_22 , CID1_AC , CMD_GET_SYSPARA},                       //13
    {VER_22 , CID1_AC , CMD_SET_SYSPARA},                       //14
    {VER_22 , CID1_AC , CMD_GET_CUSTOMPARA},                    //15
    {VER_22 , CID1_AC , CMD_SET_CUSTOMPARA},                    //16
    {VER_22 , CID1_1363_ENV , CMD_GET_ENV_ANALOG_INFO},         //17
    {VER_22 , CID1_1363_ENV , CMD_GET_ENV_ALARM_INFO},          //18
    {VER_22 , CID1_AC , CMD_GET_HIS_ALARM},                     //19
    {VER_22 , CID1_1363_ENV , CMD_GET_SYSPARA},                 //20
    {VER_22 , CID1_1363_ENV , CMD_SET_SYSPARA},                 //21
    {VER_22 , CID1_1363_ENV , CMD_GET_ALMLEVEL},                //22
    {VER_22 , CID1_1363_ENV , CMD_SET_ALMLEVEL},                //23
    {VER_22 , CID1_1363_ENV , CMD_GET_ALMRELAY},                //24
    {VER_22 , CID1_1363_ENV , CMD_SET_ALMRELAY},                //25
    {VER_22 , CID1_AC , CMD_GET_BOOT_VERSION},                  //26

};

static s1363_cmd_head_t cmd_ack[] RAM_SECTION = 
{
    {VER_22 , CID1_AC , CMD_GET_INRELAY_INFO},                  //0
    {VER_22 , CID1_AC , CMD_SET_INRELAY_INFO},                  //1
    {VER_22 , CID1_AC , CMD_GET_SYS_TIME},                      //2
    {VER_22 , CID1_AC , CMD_SET_SYS_TIME},                      //3
    {VER_22 , CID1_AC , CMD_REMOTE_CONTROL},                    //4
    {VER_22 , CID1_AC , CMD_GET_AC_ANALOG_INFO},                //5
    {VER_22 , CID1_AC , CMD_GET_AC_SW_INPUT_STATUS},            //6
    {VER_22 , CID1_AC , CMD_GET_ALMRELAY},                      //7
    {VER_22 , CID1_AC , CMD_SET_ALMRELAY},                      //8
    {VER_22 , CID1_AC , CMD_GET_ALMLEVEL},                      //9
    {VER_22 , CID1_AC , CMD_SET_ALMLEVEL},                      //10
    {VER_22 , CID1_AC , CMD_GET_FACTORY_INFO_CUSTOM},           //11
    {VER_22 , CID1_AC , CMD_GET_AC_ALARM_INFO},                 //12
    {VER_22 , CID1_AC , CMD_GET_SYSPARA},                       //13
    {VER_22 , CID1_AC , CMD_SET_SYSPARA},                       //14
    {VER_22 , CID1_AC , CMD_GET_CUSTOMPARA},                    //15
    {VER_22 , CID1_AC , CMD_SET_CUSTOMPARA},                    //16
    {VER_22 , CID1_1363_ENV , CMD_GET_ENV_ANALOG_INFO},         //17
    {VER_22 , CID1_1363_ENV , CMD_GET_ENV_ALARM_INFO},          //18
    {VER_22 , CID1_AC , CMD_GET_HIS_ALARM},                     //19
    {VER_22 , CID1_1363_ENV , CMD_GET_SYSPARA},                 //20
    {VER_22 , CID1_1363_ENV , CMD_SET_SYSPARA},                 //21
    {VER_22 , CID1_1363_ENV , CMD_GET_ALMLEVEL},                //22
    {VER_22 , CID1_1363_ENV , CMD_SET_ALMLEVEL},                //23
    {VER_22 , CID1_1363_ENV , CMD_GET_ALMRELAY},                //24
    {VER_22 , CID1_1363_ENV , CMD_SET_ALMRELAY},                //25
    {VER_22 , CID1_AC , CMD_GET_BOOT_VERSION},                  //26

};

static acmu_para_info s_inrelay_info[] = {
    {0x80, ACMU_PARA_ID_INRELAYTTL_OFFSET, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x81, ACMU_PARA_ID_INRELAYNAME_OFFSET, type_string, LEN_RELAYNAME,DOP_0},
    {0x82, ACMU_PARA_ID_INRELAYTTL_OFFSET+1, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x83, ACMU_PARA_ID_INRELAYNAME_OFFSET+1, type_string, LEN_RELAYNAME,DOP_0},
    {0x84, ACMU_PARA_ID_INRELAYTTL_OFFSET+2,type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x85, ACMU_PARA_ID_INRELAYNAME_OFFSET+2, type_string, LEN_RELAYNAME,DOP_0},
    {0x86, ACMU_PARA_ID_INRELAYTTL_OFFSET+3, type_unsigned_char,ARRAY_SIZE_1, DOP_0},
    {0x87, ACMU_PARA_ID_INRELAYNAME_OFFSET+3, type_string, LEN_RELAYNAME,DOP_0},
};

static acmu_para_info s_almlevel_info[] = {
    {0x80, ACMU_ALM_ID_AC_LINE_VOLT_HIGH_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x81, ACMU_ALM_ID_AC_LINE_VOLT_LOW_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x82, ACMU_ALM_ID_AC_PHASE_LOST_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x83, ACMU_ALM_ID_SPD_C_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x84, ACMU_ALM_ID_AC_POWER_OFF_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x85, ACMU_ALM_ID_PHASE_VOLT_HIGH_ALARM_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x86, ACMU_ALM_ID_PHASE_VOLT_LOW_ALARM_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x87, ACMU_ALM_ID_TOTAL_ALARM_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x88, ACMU_ALM_ID_PHASE_VOLT_UNBALANCE_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x89, ACMU_ALM_ID_AC_OUTPUT_SWITCH_DISCONNECTION_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x8A, ACMU_ALM_ID_PHASE_CURR_HIGH_ALARM_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x8B, ACMU_ALM_ID_AC_METER_DISCONNECTED_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x8C, ACMU_ALM_ID_INPUT_RELAY1_ALARM_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x8D, ACMU_ALM_ID_INPUT_RELAY2_ALARM_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x8E, ACMU_ALM_ID_INPUT_RELAY3_ALARM_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x8F, ACMU_ALM_ID_INPUT_RELAY4_ALARM_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
};

static acmu_para_info s_almrelay_info[] = {
    {0x80, ACMU_ALM_ID_AC_LINE_VOLT_HIGH_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x81, ACMU_ALM_ID_AC_LINE_VOLT_LOW_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x82, ACMU_ALM_ID_AC_PHASE_LOST_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x83, ACMU_ALM_ID_SPD_C_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x84, ACMU_ALM_ID_AC_POWER_OFF_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x85, ACMU_ALM_ID_PHASE_VOLT_HIGH_ALARM_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x86, ACMU_ALM_ID_PHASE_VOLT_LOW_ALARM_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x87, ACMU_ALM_ID_TOTAL_ALARM_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x88, ACMU_ALM_ID_PHASE_VOLT_UNBALANCE_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x89, ACMU_ALM_ID_AC_OUTPUT_SWITCH_DISCONNECTION_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x8A, ACMU_ALM_ID_PHASE_CURR_HIGH_ALARM_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x8B, ACMU_ALM_ID_AC_METER_DISCONNECTED_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x8C, ACMU_ALM_ID_INPUT_RELAY1_ALARM_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x8D, ACMU_ALM_ID_INPUT_RELAY2_ALARM_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x8E, ACMU_ALM_ID_INPUT_RELAY3_ALARM_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x8F, ACMU_ALM_ID_INPUT_RELAY4_ALARM_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x90, ACMU_ALM_ID_OIL_ENGINE_START_UP_ALARM_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
};

static acmu_para_info s_syspara_info[] = {
    {0x80, ACMU_PARA_ID_AC_PHASE_VOLTAGE_MAX_OFFSET, type_short, ARRAY_SIZE_1, DOP_0},
    {0x81, ACMU_PARA_ID_AC_PHASE_VOLTAGE_MIN_OFFSET, type_short, ARRAY_SIZE_1, DOP_0},
    {0x82, ACMU_PARA_ID_AC_OUT_CURRENT_MAX_OFFSET, type_short, ARRAY_SIZE_1, DOP_0},
    {0x83, ACMU_PARA_ID_AC_FREQUENCY_MAX_OFFSET, type_short, ARRAY_SIZE_1, DOP_0},
    {0x84, ACMU_PARA_ID_AC_FREQUENCY_MIN_OFFSET, type_short, ARRAY_SIZE_1, DOP_0},
    {0xE0, ACMU_PARA_ID_PHASE_VOLTAGE_UNBALANCE_OFFSET, type_short, ARRAY_SIZE_1, DOP_0},
    {0xE1, ACMU_PARA_ID_AC_MUTUAL_INDUCTANCE_TYPE_OFFSET, type_short, ARRAY_SIZE_1, DOP_0},
    {0xE2, ACMU_PARA_ID_AC_MUTUAL_INDUCTANCE_TYPE_OFFSET + 1, type_short, ARRAY_SIZE_1, DOP_0},
    {0xE3, ACMU_PARA_ID_AC_MUTUAL_INDUCTANCE_TYPE_OFFSET + 2, type_short, ARRAY_SIZE_1, DOP_0},
    {0xE4, ACMU_PARA_ID_AC_METER_CONFIGURATION_OFFSET, type_short, ARRAY_SIZE_1, DOP_0},
    {0xE5, ACMU_PARA_ID_SYS_AC_OUT_SWITCH_CONFIGURATION_OFFSET, type_short, ARRAY_SIZE_1, DOP_0},
    {0xE6, ACMU_PARA_ID_SYS_AC_OUT_SWITCH_CONFIGURATION_OFFSET + 1, type_short, ARRAY_SIZE_1, DOP_0},
    {0xE7, ACMU_PARA_ID_SYS_AC_OUT_SWITCH_CONFIGURATION_OFFSET + 2, type_short, ARRAY_SIZE_1, DOP_0},
    {0xE8, ACMU_PARA_ID_SYS_AC_OUT_SWITCH_CONFIGURATION_OFFSET + 3, type_short, ARRAY_SIZE_1, DOP_0},
    {0xE9, ACMU_PARA_ID_SYS_AC_OUT_SWITCH_CONFIGURATION_OFFSET + 4, type_short, ARRAY_SIZE_1, DOP_0},
    {0xEA, ACMU_PARA_ID_SYS_AC_OUT_SWITCH_CONFIGURATION_OFFSET + 5, type_short, ARRAY_SIZE_1, DOP_0},
    {0xEB, ACMU_PARA_ID_SYS_AC_OUT_SWITCH_CONFIGURATION_OFFSET + 6, type_short, ARRAY_SIZE_1, DOP_0},
    {0xEC, ACMU_PARA_ID_SYS_AC_OUT_SWITCH_CONFIGURATION_OFFSET + 7, type_short, ARRAY_SIZE_1, DOP_0},
    {0xED, ACMU_PARA_ID_SYS_AC_OUT_SWITCH_CONFIGURATION_OFFSET + 8, type_short, ARRAY_SIZE_1, DOP_0},
    {0xEE, ACMU_PARA_ID_SYS_AC_OUT_SWITCH_CONFIGURATION_OFFSET + 9, type_short, ARRAY_SIZE_1, DOP_0},
    {0xEF, ACMU_PARA_ID_SYS_AC_OUT_SWITCH_CONFIGURATION_OFFSET + 10, type_short, ARRAY_SIZE_1, DOP_0},
    {0xF0, ACMU_PARA_ID_SYS_AC_OUT_SWITCH_CONFIGURATION_OFFSET + 11, type_short, ARRAY_SIZE_1, DOP_0},
    {0xF1, ACMU_PARA_ID_AC_PHASE_VOLT_ZERO_OFFSET, type_short, ARRAY_SIZE_1, DOP_0},
    {0xF2, ACMU_PARA_ID_AC_PHASE_VOLT_ZERO_OFFSET + 1, type_short, ARRAY_SIZE_1, DOP_0},
    {0xF3, ACMU_PARA_ID_AC_PHASE_VOLT_ZERO_OFFSET + 2, type_short, ARRAY_SIZE_1, DOP_0},
    {0xF4, ACMU_PARA_ID_AC_PHASE_VOLT_SLOPE_OFFSET, type_short, ARRAY_SIZE_1, DOP_2},
    {0xF5, ACMU_PARA_ID_AC_PHASE_VOLT_SLOPE_OFFSET + 1, type_short, ARRAY_SIZE_1, DOP_2},
    {0xF6, ACMU_PARA_ID_AC_PHASE_VOLT_SLOPE_OFFSET + 2, type_short, ARRAY_SIZE_1, DOP_2},
    {0xF7, ACMU_PARA_ID_AC_PHASE_CURR_ZERO_OFFSET, type_short, ARRAY_SIZE_1, DOP_0},
    {0xF8, ACMU_PARA_ID_AC_PHASE_CURR_ZERO_OFFSET + 1, type_short, ARRAY_SIZE_1, DOP_0},
    {0xF9, ACMU_PARA_ID_AC_PHASE_CURR_ZERO_OFFSET + 2, type_short, ARRAY_SIZE_1, DOP_0},
    {0xFA, ACMU_PARA_ID_AC_PHASE_CURR_SLOPE_OFFSET, type_short, ARRAY_SIZE_1, DOP_2},
    {0xFB, ACMU_PARA_ID_AC_PHASE_CURR_SLOPE_OFFSET + 1, type_short, ARRAY_SIZE_1, DOP_2},
    {0xFC, ACMU_PARA_ID_AC_PHASE_CURR_SLOPE_OFFSET + 2, type_short, ARRAY_SIZE_1, DOP_2},
    {0xFD, ACMU_PARA_ID_AC_LINE_VOLTAGE_MAX_OFFSET, type_short, ARRAY_SIZE_1, DOP_0},
    {0xFE, ACMU_PARA_ID_AC_LINE_VOLTAGE_MIN_OFFSET, type_short, ARRAY_SIZE_1, DOP_0},
};

static acmu_para_info s_custompara_info[] = {
    {0x80, ACMU_PARA_ID_SYS_BUZZER_SWITCH_OFFSET, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x81, ACMU_PARA_ID_HISDATA_SAVE_INTERVAL_OFFSET, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x82, ACMU_PARA_ID_SYS_SERIAL_PORT_BAUD_RATE_SETTING_OFFSET + 1, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x83, ACMU_PARA_ID_SYS_LOCAL_MACHINE_ADDRESS_OFFSET, type_short, ARRAY_SIZE_1, DOP_0},
    {0x84, ACMU_PARA_ID_SYS_LANGUAGE_SETTING_OFFSET, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x85, ACMU_PARA_ID_SYS_MENU_PASERWORD_SETTING_OFFSET, type_string, ARRAY_SIZE_4, DOP_0},
    {0x86, ACMU_PARA_ID_USAGE_SCENARIO_OFFSET, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x87, ACMU_PARA_ID_SYS_AC_MUTUAL_INDUCTANCE_CONFIGURATION_OFFSET, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x88, ACMU_PARA_ID_SYS_AC_MUTUAL_INDUCTANCE_CONFIGURATION_OFFSET + 1, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x89, ACMU_PARA_ID_SYS_AC_MUTUAL_INDUCTANCE_CONFIGURATION_OFFSET + 2, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x8A, ACMU_PARA_ID_AC_OUTPUT_MUTUAL_INDUCTANCE_TYPE_OFFSET, type_short, ARRAY_SIZE_1, DOP_0},
    {0x8B, ACMU_PARA_ID_AC_UNDERVOLTAGE_TRIG_THRESHOLD_OFFSET, type_unsigned_short, ARRAY_SIZE_1, DOP_1},
    {0x8C, ACMU_PARA_ID_AC_UNDERVOLTAGE_DEVIATION_THRESHOLD_OFFSET, type_unsigned_short, ARRAY_SIZE_1, DOP_1},
};

static acmu_para_info s_envpara_info[] = {
    {0x80, ACMU_PARA_ID_TEMPERATURE_SENSOR_UPPER_OFFSET, type_short, ARRAY_SIZE_1, DOP_0},
    {0x81, ACMU_PARA_ID_TEMPERATURE_SENSOR_LOWER_OFFSET, type_short, ARRAY_SIZE_1, DOP_0},
    {0x82, ACMU_PARA_ID_HUMIDITY_SENSOR_UPPER_OFFSET, type_short, ARRAY_SIZE_1, DOP_0},
    {0x83, ACMU_PARA_ID_HUMIDITY_SENSOR_LOWER_OFFSET, type_short, ARRAY_SIZE_1, DOP_0},
    {0x84, ACMU_PARA_ID_ENV_TEMP_ZERO_POINT_OFFSET, type_short, ARRAY_SIZE_1, DOP_0},
    {0x85, ACMU_PARA_ID_ENV_HUMIDITY_ZERO_POINT_OFFSET, type_short, ARRAY_SIZE_1, DOP_0},
};

static acmu_para_info s_env_almlevel_info[] = {
    {0x80, ACMU_ALM_ID_ENV_TEMP_HIGH_ALARM_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x81, ACMU_ALM_ID_ENV_TEMP_LOW_ALARM_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x82, ACMU_ALM_ID_ENV_TEMP_SENSOR_INVALID_ALARM_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x83, ACMU_ALM_ID_ENV_HUMIDITY_HIGH_ALARM_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x84, ACMU_ALM_ID_ENV_HUMIDITY_LOW_ALARM_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x85, ACMU_ALM_ID_ENV_HUMIDITY_SENSOR_INVALID_ALARM_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x86, ACMU_ALM_ID_FUMES_SENSOR_ALARM_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x87, ACMU_ALM_ID_FLOOD_SENSOR_ALARM_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x88, ACMU_ALM_ID_DOORMAT_SENSOR_ALARM_LEVEL, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
};

static acmu_para_info s_env_almrelay_info[] = {
    {0x80, ACMU_ALM_ID_ENV_TEMP_HIGH_ALARM_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x81, ACMU_ALM_ID_ENV_TEMP_LOW_ALARM_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x82, ACMU_ALM_ID_ENV_TEMP_SENSOR_INVALID_ALARM_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x83, ACMU_ALM_ID_ENV_HUMIDITY_HIGH_ALARM_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x84, ACMU_ALM_ID_ENV_HUMIDITY_LOW_ALARM_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x85, ACMU_ALM_ID_ENV_HUMIDITY_SENSOR_INVALID_ALARM_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x86, ACMU_ALM_ID_FUMES_SENSOR_ALARM_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x87, ACMU_ALM_ID_FLOOD_SENSOR_ALARM_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
    {0x88, ACMU_ALM_ID_DOORMAT_SENSOR_ALARM_RELAY, type_unsigned_char, ARRAY_SIZE_1, DOP_0},
};

static cmd_t no_poll_cmd_tab[] = {
    {ACMU_GET_INRELAY_INFO, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),   NULL, NULL, acmu_pack_para_comm,NULL,},
    {ACMU_SET_INRELAY_INFO, CMD_PASSIVE, &cmd_req[1], &cmd_ack[1], sizeof(s1363_cmd_head_t),   NULL, NULL, NULL, acmu_parse_para_comm, },
    {ACMU_GET_SYS_TIME_1363, CMD_PASSIVE, &cmd_req[2], &cmd_ack[2], sizeof(s1363_cmd_head_t),   NULL, NULL, acmu_pack_time, NULL,},
    {ACMU_SET_SYS_TIME_1363, CMD_PASSIVE, &cmd_req[3], &cmd_ack[3], sizeof(s1363_cmd_head_t),   NULL, NULL,  NULL,acmu_parse_time, },
    {ACMU_REMOTE_CONTROL,   CMD_PASSIVE, &cmd_req[4], &cmd_ack[4], sizeof(s1363_cmd_head_t),   NULL, NULL, NULL, acmu_parse_remote_control, },
    {ACMU_GET_AC_ANALOG_INFO, CMD_PASSIVE, &cmd_req[5], &cmd_ack[5], sizeof(s1363_cmd_head_t),   NULL, NULL, acmu_pack_ac_analog_info, check_ac_command_group},
    {ACMU_GET_AC_SW_INPUT_STATUS, CMD_PASSIVE, &cmd_req[6], &cmd_ack[6], sizeof(s1363_cmd_head_t),   NULL, NULL, acmu_pack_ac_sw_input_status, check_ac_command_group},
    {ACMU_GET_ALMRELAY, CMD_PASSIVE, &cmd_req[7], &cmd_ack[7], sizeof(s1363_cmd_head_t),   NULL, NULL, acmu_pack_para_comm,NULL,},
    {ACMU_SET_ALMRELAY, CMD_PASSIVE, &cmd_req[8], &cmd_ack[8], sizeof(s1363_cmd_head_t),   NULL, NULL, NULL, acmu_parse_para_comm, },
    {ACMU_GET_ALMLEVEL, CMD_PASSIVE, &cmd_req[9], &cmd_ack[9], sizeof(s1363_cmd_head_t),   NULL, NULL, acmu_pack_para_comm,NULL,},
    {ACMU_SET_ALMLEVEL, CMD_PASSIVE, &cmd_req[10], &cmd_ack[10], sizeof(s1363_cmd_head_t),   NULL, NULL, NULL, acmu_parse_para_comm, }, 
    {ACMU_GET_FACTORY_INFO_CUSTOM, CMD_PASSIVE, &cmd_req[11], &cmd_ack[11], sizeof(s1363_cmd_head_t),   NULL, NULL, acmu_pack_factory_info_custom,NULL,},
    {ACMU_GET_AC_ALARM_INFO, CMD_PASSIVE, &cmd_req[12], &cmd_ack[12], sizeof(s1363_cmd_head_t),   NULL, NULL, acmu_pack_ac_alarm_info,NULL,},
    {ACMU_GET_SYSPARA, CMD_PASSIVE, &cmd_req[13], &cmd_ack[13], sizeof(s1363_cmd_head_t),   NULL, NULL, acmu_pack_para_comm,NULL,},
    {ACMU_SET_SYSPARA, CMD_PASSIVE, &cmd_req[14], &cmd_ack[14], sizeof(s1363_cmd_head_t),   NULL, NULL, NULL, acmu_parse_para_comm, },
    {ACMU_GET_CUSTOMPARA, CMD_PASSIVE, &cmd_req[15], &cmd_ack[15], sizeof(s1363_cmd_head_t),   NULL, NULL, acmu_pack_para_comm,NULL,},
    {ACMU_SET_CUSTOMPARA, CMD_PASSIVE, &cmd_req[16], &cmd_ack[16], sizeof(s1363_cmd_head_t),   NULL, NULL, NULL, acmu_parse_para_comm, },     
    {ACMU_GET_ENV_ANALOG_INFO, CMD_PASSIVE, &cmd_req[17], &cmd_ack[17], sizeof(s1363_cmd_head_t),   NULL, NULL, acmu_pack_env_analog_info,NULL,},
    {ACMU_GET_ENV_ALARM_INFO, CMD_PASSIVE, &cmd_req[18], &cmd_ack[18], sizeof(s1363_cmd_head_t),   NULL, NULL, acmu_pack_env_alarm_info,NULL,},
    {ACMU_GET_HIS_ALARM, CMD_PASSIVE, &cmd_req[19], &cmd_ack[19], sizeof(s1363_cmd_head_t),   NULL, NULL, pack_his_alarm, parse_his_alarm,},
    {ACMU_GET_ENVPARA, CMD_PASSIVE, &cmd_req[20], &cmd_ack[20], sizeof(s1363_cmd_head_t),   NULL, NULL, get_env_para,NULL,},
    {ACMU_SET_ENVPARA, CMD_PASSIVE, &cmd_req[21], &cmd_ack[21], sizeof(s1363_cmd_head_t),   NULL, NULL, NULL, acmu_parse_para_comm, },
    {ACMU_GET_ENVALMLEVEL, CMD_PASSIVE, &cmd_req[22], &cmd_ack[22], sizeof(s1363_cmd_head_t),   NULL, NULL, get_env_para,NULL,},
    {ACMU_SET_ENVALMLEVEL, CMD_PASSIVE, &cmd_req[23], &cmd_ack[23], sizeof(s1363_cmd_head_t),   NULL, NULL, NULL, acmu_parse_para_comm, }, 
    {ACMU_GET_ENVALMRELAY, CMD_PASSIVE, &cmd_req[24], &cmd_ack[24], sizeof(s1363_cmd_head_t),   NULL, NULL, get_env_para,NULL,},
    {ACMU_SET_ENVALMRELAY, CMD_PASSIVE, &cmd_req[25], &cmd_ack[25], sizeof(s1363_cmd_head_t),   NULL, NULL, NULL, acmu_parse_para_comm, },
    {ACMU_GET_BOOT_VERSION, CMD_PASSIVE, &cmd_req[26], &cmd_ack[26], sizeof(s1363_cmd_head_t),   NULL, NULL, acmu_pack_boot_ver, NULL, },
    {0},
};

Static dev_type_t dev_acmu_1363 = {
  DEV_NORTH_1363, 1, PROTOCOL_YD_1363, LINK_ACMU, R_BUFF_LEN, S_BUFF_LEN, 0, &no_poll_cmd_tab[0],
};

dev_type_t* init_dev_acmu_1363(void)
{
    return &dev_acmu_1363;
}

Static char acmu_pack_para_handle(acmu_para_info para_info,unsigned char* buff)
{
    str64 str = {0};
    unsigned char uc_data; 
    char c_data; 
    float f_data = 0.0f;
    rt_memset_s(&para_value, sizeof(acmu_para_value), 0, sizeof(acmu_para_value));

    unsigned char save_type = GET_SYS_PARA_DATA_TYPE(para_info.sid);

    // 根据存储类型转换并组包
    switch (save_type) {
        case TYPE_INT8U:
                get_one_para(para_info.sid, &uc_data);
                para_value.t_data = (short)uc_data;
            break;
        case TYPE_INT8S:
                get_one_para(para_info.sid, &c_data);
                para_value.t_data = (short)c_data;
            break;
        case TYPE_INT16U:
                get_one_para(para_info.sid, &para_value.t_data);
            break;
        case TYPE_FLOAT:
                get_one_para(para_info.sid, &f_data);
                if(para_info.data_type == type_short)
                    para_value.t_data = (short)roundf(f_data * pow(10,para_info.precision));
                else
                    para_value.t_data = (unsigned short)roundf(f_data * pow(10,para_info.precision));
            break;
        case TYPE_STRING:
                get_one_para(para_info.sid, str);
            break;
        default:
            break;
    }

    switch (para_info.data_type) 
    {
        case type_unsigned_char:
            *buff = (unsigned char)para_value.uc_data;
            return sizeof(char);
        case type_short:
            put_int16_to_buff(buff, para_value.t_data);
            return sizeof(short);
        case type_unsigned_short:
            put_uint16_to_buff(buff, para_value.t_data);
            return sizeof(unsigned short);
        case type_string:
            rt_memset_s(buff,para_info.array_len,0x20,para_info.array_len);
            rt_memcpy_s(buff, para_info.array_len,str, rt_strnlen_s(str,para_info.array_len));
            return para_info.array_len;
        default:
            break;
    }

    return 0;
}

Static char acmu_parse_para_handle(acmu_para_info para_info,unsigned char* buff)
{
    signed char rst = 0;
    str64 str = {0};
    float f_data = 0.0f;
    unsigned char save_type = 0;
   rt_memset_s(&para_value, sizeof(acmu_para_value), 0, sizeof(acmu_para_value));

    switch (para_info.data_type) 
    {
        case type_unsigned_char:
            para_value.uc_data = buff[0];
            break;
        case type_short:
            para_value.t_data = get_int16_data(&buff[0]);
            break;
        case type_string:
            if(s_bufflen > para_info.array_len)
            {
                return FAILURE;
            }
            rt_memset_s(str,sizeof(str64),0,sizeof(str64));
            rt_memcpy_s(str, s_bufflen, &buff[0], s_bufflen);
            break;
        default:
            break;
    }

    // 根据存储类型转换并存储
    save_type = GET_SYS_PARA_DATA_TYPE(para_info.sid);
    
    switch (save_type) {
        case TYPE_INT8U:
                rst = set_one_para(para_info.sid, &para_value.uc_data, TRUE, TRUE);
            break;
        case TYPE_INT8S:
                rst = set_one_para(para_info.sid, &para_value.c_data, TRUE, TRUE);
            break;
        case TYPE_INT16U:
                rst = set_one_para(para_info.sid, &para_value.t_data, TRUE, TRUE);
            break;
        case TYPE_FLOAT:
                f_data = (float)para_value.t_data / pow(10,para_info.precision);
                rst = set_one_para(para_info.sid, &f_data, TRUE, TRUE);
            break;
        case TYPE_STRING:
                rst = set_one_para(para_info.sid, str, TRUE, TRUE);
            break;
        default:
                rst = FAILURE;
            break;
    }

    return rst;
}

static int acmu_LookupTable_func(acmu_para_info *para,unsigned char* buff,unsigned char len,unsigned char parse_type)
{
    signed char rst = 0;
    unsigned char command_type = 0;

    command_type = buff[0];

    for (size_t i = 0; i < len; i++) {
        if (para[i].command_type == command_type) {
            if(PARSE_PARA == parse_type)
            {
                rst = acmu_parse_para_handle(para[i],&buff[1]);
            }
            else
            {
                rst = set_alm_para(para[i].sid,&buff[1],TRUE);
            }
            return rst;
        }
    }
    return FAILURE;
}

Static int acmu_parse_para_comm(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff;
    unsigned char cmt_id = 0;
    unsigned char len = 0;
    signed char rst = 0;
    buff = cmd_buf_temp->buf;
    s_bufflen = cmd_buf_temp->data_len - 1;

    cmt_id = cmd_buf_temp->cmd->cmd_id;

    switch (cmt_id)
    {
        case ACMU_SET_INRELAY_INFO:
                len = ARR_SIZE(s_inrelay_info);
                rst = acmu_LookupTable_func(s_inrelay_info,buff,len,PARSE_PARA);
            break;
        case ACMU_SET_SYSPARA:
                len = ARR_SIZE(s_syspara_info);
                rst = acmu_LookupTable_func(s_syspara_info,buff,len,PARSE_PARA);
            break;
        case ACMU_SET_ALMRELAY:
                len = ARR_SIZE(s_almrelay_info);
                rst = acmu_LookupTable_func(s_almrelay_info,buff,len,PARSE_ALM);
            break;
        case ACMU_SET_ALMLEVEL:
                len = ARR_SIZE(s_almlevel_info);
                rst = acmu_LookupTable_func(s_almlevel_info,buff,len,PARSE_ALM);
            break;
        case ACMU_SET_CUSTOMPARA:
                if(acmu_checkMenuKey(buff,s_bufflen) != 0)
                {
                    rst = FAILURE;
                }
                else
                {
                    len = ARR_SIZE(s_custompara_info);
                    rst = acmu_LookupTable_func(s_custompara_info,buff,len,PARSE_PARA);
                    acmu_sync_north_addr(buff,rst);
                }
            break;
        case ACMU_SET_ENVPARA:
                 len = ARR_SIZE(s_envpara_info);
                if(buff[0] != s_command_group)
                {
                    rst = FAILURE;
                }
                else
                {
                    rst = acmu_LookupTable_func(s_envpara_info,buff+1,len,PARSE_PARA);
                }
            break;
        case ACMU_SET_ENVALMLEVEL:
                len = ARR_SIZE(s_env_almlevel_info);
                rst = acmu_LookupTable_func(s_env_almlevel_info,buff,len,PARSE_ALM);
            break;
        case ACMU_SET_ENVALMRELAY:
                len = ARR_SIZE(s_env_almrelay_info);
                rst = acmu_LookupTable_func(s_env_almrelay_info,buff,len,PARSE_ALM);
            break;
        default:
                rst = FAILURE;
            break;
    }

    if(rst < 0)
    {
        cmd_buf_temp->rtn = RTN_WRONG_COMMAND;
    }
    else
    {
        update_para();
    }

    return SUCCESSFUL;
}

Static char acmu_sync_north_addr(unsigned char* buff,char ret)
{
    unsigned char command_type = 0;

    command_type = buff[0];

    if (ret < 0) {
        return FAILURE;
    }

    switch (command_type) {
        case 0x83:  // 设置地址
            acmu_update_host_address();
            break;
        case 0x82:  // 设置波特率
            acmu_update_baudrate(); 
            break;
        default:
            return FAILURE;
    }

    return SUCCESSFUL;
}

Static char acmu_checkMenuKey(unsigned char* buff,unsigned char len)
{
    unsigned char command_type = 0;

    command_type = buff[0];

    if (command_type == 0x85) //0x85是菜单口令的command_type
    {
        for(int i = 1;i <= MENUKEY_LEN;i++)
        {
            if(!((buff[i]>= '0') && (buff[i] <= '9')))
            {
                return FAILURE;
            }
        }
    }
    return SUCCESSFUL;
}

Static int acmu_pack_para_comm(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff;
    unsigned char uc_data = 0;
    unsigned char cmt_id = 0;
    unsigned char offset = 0;

    cmt_id = cmd_buf_temp->cmd->cmd_id;
    buff = cmd_buf_temp->buf;

    switch (cmt_id)
    {
        case ACMU_GET_INRELAY_INFO:
            {
                buff[offset++] = RELAY_NUM;                            /* 干接点数量 */

                for (int i = 0; i < ARR_SIZE(s_inrelay_info); i++) {
                    offset += acmu_pack_para_handle(s_inrelay_info[i], &buff[offset]);
                }
            }
            break;
        case ACMU_GET_SYSPARA:
            {
                // 处理前五个参数
                for (int i = 0; i < 5; i++) {
                    offset += acmu_pack_para_handle(s_syspara_info[i], &buff[offset]);
                }

                // 设置自定义参数数量
                buff[offset++] = ARR_SIZE(s_syspara_info) - 5;

                // 处理剩余的自定义参数
                for (int i = 5; i < ARR_SIZE(s_syspara_info); i++) {
                    offset += acmu_pack_para_handle(s_syspara_info[i], &buff[offset]);
                }
            }
            break;
        case ACMU_GET_ALMRELAY:
            {
                buff[offset++] = ARR_SIZE(s_almrelay_info);       //告警干接点数量

                for (size_t i = 0; i < ARR_SIZE(s_almrelay_info); i++) {
                    get_alm_para(s_almrelay_info[i].sid,&uc_data);
                    buff[offset++]  = uc_data;
                }
            }
            break;
        case ACMU_GET_ALMLEVEL:
            {
                buff[offset++] = ARR_SIZE(s_almlevel_info);       //告警级别数量

                for (size_t i = 0; i < ARR_SIZE(s_almlevel_info); i++) {
                    get_alm_para(s_almlevel_info[i].sid,&uc_data);
                    buff[offset++]  = uc_data;
                }
            }
            break;
        case ACMU_GET_CUSTOMPARA:
            {
                for (int i = 0; i < ARR_SIZE(s_custompara_info); i++) {
                    offset += acmu_pack_para_handle(s_custompara_info[i], &buff[offset]);
                }
            }
            break;
        default:
            break;
    }

     /* LENID */
    cmd_buf_temp->data_len = offset;
    
    return SUCCESSFUL;
}

Static int get_env_para(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff;
    unsigned char uc_data = 0;
    unsigned char cmd_id = 0;
    unsigned char offset = 0;

    cmd_id = cmd_buf_temp->cmd->cmd_id;
    buff = cmd_buf_temp->buf;

    switch (cmd_id)
    {
        case ACMU_GET_ENVPARA:
            {
                buff[offset++] = 1; //温度传感器数量
                offset += acmu_pack_para_handle(s_envpara_info[0], &buff[offset]);  // 温度传感器上限
                offset += acmu_pack_para_handle(s_envpara_info[1], &buff[offset]);  // 温度传感器下限

                buff[offset++] = 1; //湿度传感器数量
                offset += acmu_pack_para_handle(s_envpara_info[2], &buff[offset]);  // 湿度传感器上限
                offset += acmu_pack_para_handle(s_envpara_info[3], &buff[offset]);  // 湿度传感器下限

                buff[offset++] = 2; //用户自定义参数数量
                offset += acmu_pack_para_handle(s_envpara_info[4], &buff[offset]);  // 环境温度零点
                offset += acmu_pack_para_handle(s_envpara_info[5], &buff[offset]);  // 环境湿度零点
            }
            break;
        case ACMU_GET_ENVALMLEVEL:
            {
                buff[offset++] = ARR_SIZE(s_env_almlevel_info);       //环境告警数量

                for (size_t i = 0; i < ARR_SIZE(s_env_almlevel_info); i++) {
                    get_alm_para(s_env_almlevel_info[i].sid,&uc_data);
                    buff[offset++]  = uc_data;
                }
            }
            break;
        case ACMU_GET_ENVALMRELAY:
            {
                buff[offset++] = ARR_SIZE(s_env_almrelay_info);       //环境告警数量

                for (size_t i = 0; i < ARR_SIZE(s_env_almrelay_info); i++) {
                    get_alm_para(s_env_almrelay_info[i].sid,&uc_data);
                    buff[offset++]  = uc_data;
                }
            }
            break;
        default:
            break;
    }

     /* LENID */
    cmd_buf_temp->data_len = offset;

    return SUCCESSFUL;
}

Static int acmu_pack_factory_info_custom(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff;
    unsigned char offset = 0;
    unsigned char sm_name[SM_NAME_LEN];
    unsigned char sm_soft_ver[SM_SOFT_VER_LEN];
    unsigned char factory_name[SM_FACTORY_NAME_LEN];
    unsigned char release_date[SM_SOFT_RELEASE_DATE_LEN];
    unsigned char hardware_version[SM_HARDWARE_VERSION_LEN] = {0};
    unsigned char serial_number[SM_SERIAL_NUMBER_LEN] = {0};

    buff = cmd_buf_temp->buf;

    /* 获取SM名称 */
    rt_memset_s(sm_name, SM_NAME_LEN, 0x20, SM_NAME_LEN);
    rt_memcpy_s(sm_name, SM_NAME_LEN, SOFTWARE_NAME, rt_strnlen_s(SOFTWARE_NAME, SM_NAME_LEN));

    /* 获取SM软件版本 */
    rt_memset_s(sm_soft_ver, SM_SOFT_VER_LEN, 0x20, SM_SOFT_VER_LEN);
    rt_memcpy_s(sm_soft_ver, SM_SOFT_VER_LEN, SOFTWARE_VER, rt_strnlen_s(SOFTWARE_VER, SM_SOFT_VER_LEN));

    /* 获取厂家名称 */
    rt_memset_s(factory_name, SM_FACTORY_NAME_LEN, 0x20, SM_FACTORY_NAME_LEN);
    rt_memcpy_s(factory_name, SM_FACTORY_NAME_LEN, CORPERATION_NAME, rt_strnlen_s(CORPERATION_NAME, SM_FACTORY_NAME_LEN));

    /* 获取软件发布日期 */
    release_date[0] = (unsigned char)(SOFTWARE_DATE_YEAR >> 8);   // 年份高字节
    release_date[1] = (unsigned char)(SOFTWARE_DATE_YEAR & 0xFF); // 年份低字节
    release_date[2] = SOFTWARE_DATE_MONTH;                        // 月份
    release_date[3] = SOFTWARE_DATE_DAY;                          // 日期

    get_one_para(ACMU_PARA_ID_HARDWARE_VERSION_OFFSET, hardware_version);
    get_one_para(ACMU_PARA_ID_SERIAL_NUMBER_OFFSET, serial_number);

    rt_memcpy_s(&buff[offset], SM_NAME_LEN, sm_name, SM_NAME_LEN);
    offset += SM_NAME_LEN;

    rt_memcpy_s(&buff[offset], SM_SOFT_VER_LEN, sm_soft_ver, SM_SOFT_VER_LEN);
    offset += SM_SOFT_VER_LEN;

    rt_memcpy_s(&buff[offset], SM_FACTORY_NAME_LEN, factory_name, SM_FACTORY_NAME_LEN);
    offset += SM_FACTORY_NAME_LEN;

    rt_memcpy_s(&buff[offset], SM_SOFT_RELEASE_DATE_LEN, release_date, SM_SOFT_RELEASE_DATE_LEN);
    offset += SM_SOFT_RELEASE_DATE_LEN;

    rt_memcpy_s(&buff[offset], SM_HARDWARE_VERSION_LEN-1, hardware_version, SM_HARDWARE_VERSION_LEN-1);
    offset = offset + SM_HARDWARE_VERSION_LEN-1;

    rt_memcpy_s(&buff[offset], SM_SERIAL_NUMBER_LEN-1, serial_number, SM_SERIAL_NUMBER_LEN-1);
    offset = offset + SM_SERIAL_NUMBER_LEN-1;

    cmd_buf_temp->data_len = offset;

    return SUCCESSFUL;
}

Static int acmu_pack_env_analog_info(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff;
    unsigned char offset = 0;
    float f_data = 0.0f;

    buff = cmd_buf_temp->buf;

    /* DATA_FLAG */
    get_one_data(ACMU_DATA_ID_DATA_FLAG, &buff[offset++]);

    /* DATAF */
    buff[offset++] = 0x01;            // 温度传感器数量m=1
    get_one_data(ACMU_DATA_ID_ENV_TEMP, &f_data);  // 环境温度
    put_int16_to_buff(&buff[offset], FLOAT_TO_SHORT(f_data));
    offset += 2;

    buff[offset++] = 0x01;            // 湿度传感器数量n=1
    get_one_data(ACMU_DATA_ID_ENV_HUMIDITY, &f_data);  // 环境湿度
    put_int16_to_buff(&buff[offset], FLOAT_TO_SHORT(f_data));
    offset += 2;

    buff[offset++] = 0x00;            // 自定义遥测数量为0

    /* LENID */
    cmd_buf_temp->data_len = offset;

    return SUCCESSFUL;
}

Static signed char acmu_ctrl_reset(void)
{
    static rt_timer_t acmu_reset = NULL;

    if (acmu_reset == NULL)
    {
        acmu_reset = rt_timer_create("acmu_reset", rt_hw_cpu_reset, NULL, 1000, RT_TIMER_FLAG_ONE_SHOT);
    }

    if (acmu_reset != NULL)
    {
        rt_timer_stop(acmu_reset);
        rt_timer_start(acmu_reset);
    }
    else
    {
        return FAILURE;
    }

    set_software_reset_reason(RESET_REASON_REMOTE_CTRL);
    return SUCCESSFUL;
}

Static int acmu_parse_remote_control(void* dev_inst, void *cmd_buf)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    unsigned char* buff;
    unsigned char command_type = 0;
    unsigned char relay_index = 0;
    signed char rst = 0;
    buff = cmd_buf_temp->buf;
    unsigned char manual_control_mode = 0;
    T_CtrlOutStruct tCtrl;

    rt_memset_s(&tCtrl, sizeof(T_CtrlOutStruct), 0, sizeof(T_CtrlOutStruct));

    /* 预留COMMAND_Group,COMMAND_TYPE为buff[1] */
    command_type = buff[1];
    switch(command_type) {
        case 0x10:          // 系统复位
            rst = acmu_ctrl_reset();
            break;

        case 0x11:          // 恢复默认运行参数
            rst = restore_para_to_fact(EXCLUDE_TYPE_RUNTIME);
            update_para();
            pub_hisaction_save_msg(ID1_ACMU_CTRL, ID2_CTL_LDRUNPARA, 0, NULL);
            break;

        case 0x12:          // 恢复默认配置参数
            rst = restore_para_to_fact(EXCLUDE_TYPE_CONFIG);
            update_para();
            pub_hisaction_save_msg(ID1_ACMU_CTRL, ID2_CTL_LDCFGPARA, 0, NULL);

            acmu_update_baudrate();
            acmu_update_host_address();
            break;

        case 0x13 ... 0x1A:          // 输出干接点1、2、3、4、5、6、7、8恢复
            relay_index = command_type - 0x13;
            rst = manual_control_out_relay(relay_index, CTRL_OFF);
            break;

        case 0x1B ... 0x22:          // 输出干接点1、2、3、4、5、6、7、8动作
            relay_index = command_type - 0x1B;
            rst = manual_control_out_relay(relay_index, CTRL_ON);
            break;

        case 0x23:          // 输出干节点手动控制
            clear_ctrl_out();
            manual_control_mode = 1;
            set_one_data(ACMU_DATA_ID_OUTRELAY_MANUAL_CONTROL_MODE, &manual_control_mode);
            break;

        case 0x24:          // 输出干节点非手动控制
            clear_ctrl_out();
            break;

        case 0x25 ... 0x27:          // 删除操作记录、历史告警、历史数据、极值记录
            rst = delete_record_data_new(AUTH_CODE_HIS_ACTION + command_type - 0x25, RECORD_TYPE_HIS_ACTION + command_type - 0x25);
            pub_hisaction_save_msg(ID1_ACMU_CTRL, ID2_CTL_DELACTIONREC + command_type - 0x25, 0, NULL);
            break;

        case 0x28:
            delete_extreme_data();
            pub_hisaction_save_msg(ID1_ACMU_CTRL, ID2_CTL_DELPEAKREC, 0, NULL);
            break;

        case 0x29:          // 删除所有记录数据
            // 删除操作记录
            rst += delete_record_data_new(AUTH_CODE_HIS_ACTION, RECORD_TYPE_HIS_ACTION);
            // 删除历史告警
            rst += delete_record_data_new(AUTH_CODE_HIS_ALARM, RECORD_TYPE_HIS_ALARM);
            // 删除历史数据
            rst += delete_record_data_new(AUTH_CODE_HIS_DATA, RECORD_TYPE_HIS_DATA);
            // 删除极值记录
            delete_extreme_data();
            pub_hisaction_save_msg(ID1_ACMU_CTRL, ID2_CTL_DELALLREC, 0, NULL);
            break;

        default:
            cmd_buf_temp->rtn = RTN_WRONG_COMMAND;
            break;
    }

    if(rst != SUCCESSFUL){
        cmd_buf_temp->rtn = RTN_CTRL_FAIL;
    }

    return SUCCESSFUL;
}

Static signed char manual_control_out_relay(unsigned char relay_index, unsigned char control_status)
{
    unsigned char manual_control_mode = 0;
    T_CtrlOutStruct tCtrl = {0,};

    get_one_data(ACMU_DATA_ID_OUTRELAY_MANUAL_CONTROL_MODE, &manual_control_mode);
    if(manual_control_mode == FALSE)   // 如果不在手动控制模式，则控制不会生效
    {
        return FAILURE;
    }
    GetCtrlOut( &tCtrl );
    tCtrl.wRelayOut |= 0x0001 << relay_index;
    tCtrl.aucRelay[relay_index] = control_status;
    SetCtrlOut( &tCtrl );
    return SUCCESSFUL;
}

Static int acmu_pack_ac_analog_info(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    unsigned char* buff;
    unsigned int offset = 0;
    unsigned short us_data = 0;
    float f_data = 0;
    int i = 0;
    acem_analog_data_t acem_show_data;

    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf_temp != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(cmd_buf_temp->buf != NULL, FAILURE);

    buff = cmd_buf_temp->buf;

    rt_memset_s(&acem_show_data, sizeof(acem_show_data), 0, sizeof(acem_show_data));
    get_acem_show_data(&acem_show_data);
    get_one_data(ACMU_DATA_ID_DATA_FLAG, &buff[offset++]);

    if (0xFF == s_command_group)    // 多个交流屏时，交流屏数量置为1
    {
        buff[offset++] = 0x01;
    }

    buff[offset] = 0x01;            // 本屏交流配电数量
    offset += 1;

    for (i = 0; i < AC_PHASE_NUM; i++)
    {
        get_one_data(ACMU_DATA_ID_PHASE_VOLT + i, &f_data);              // 输入相电压
        put_int16_to_buff(&buff[offset], round(f_data * pow(10,1)));
        offset += 2;
    }

    rt_memset_s(&buff[offset], 2, 0x20, 2);
    offset += 2;                     // 交流频率,用20H填充

    buff[offset] = 24;               // 自定义遥测数量为24
    offset += 1;

    for (i = 0; i < AC_PHASE_NUM; i++)
    {
        get_one_data(ACMU_DATA_ID_LINE_VOLT + i, &f_data);
        put_int16_to_buff(&buff[offset], round(f_data * pow(10,1)));         // 输入线电压
        offset += 2;
    }

    for (i = 0; i < AC_PHASE_NUM; i++)
    {
        put_int16_to_buff(&buff[offset], round(acem_show_data.phase_volt[i] * pow(10,1)));         // 交流表相电压
        offset += 2;
    }

    for (i = 0; i < AC_PHASE_NUM; i++)
    {
        put_int16_to_buff(&buff[offset], round(acem_show_data.phase_curr[i] * pow(10,1)));         // 交流表相电流
        offset += 2;
    }

    for (i = 0; i < AC_PHASE_NUM; i++)
    {
        put_int16_to_buff(&buff[offset], round(acem_show_data.power_factor[i] * pow(10,2)));         // 交流电表功率因数
        offset += 2;
    }

    for (i = 0; i < AC_PHASE_NUM; i++)
    {
        put_int16_to_buff(&buff[offset], round(acem_show_data.phase_act_power[i] * pow(10,1)));         // 交流电表相有功功率
        offset += 2;
    }

    put_int32_to_buff(&buff[offset], round(acem_show_data.total_energy));                             // 交流电表电能
    offset += 4;

    for (i = 0; i < AC_PHASE_NUM; i++)
    {
        get_one_data(ACMU_DATA_ID_PHASE_CURR + i, &f_data);
        put_int16_to_buff(&buff[offset], round(f_data * pow(10,1)));        // 相电流
        offset += 2;
    }
       
    put_int16_to_buff(&buff[offset], round(acem_show_data.total_act_power * pow(10,1))); // 交流主输出有功功率
    offset += 2;
            
    put_int16_to_buff(&buff[offset], round(acem_show_data.total_react_power * pow(10,1))); // 总无功功率
    offset += 2;
      
    put_int16_to_buff(&buff[offset], round(acem_show_data.total_apparent_power * pow(10,1)));  // 总视在功率
    offset += 2;

    put_int16_to_buff(&buff[offset], round(acem_show_data.total_power_factor * pow(10,2)));   // 总功率因数
    offset += 2;

    put_int16_to_buff(&buff[offset], acem_show_data.frequency);   // 交流电表频率
    offset += 2;

    /* LENID */
    cmd_buf_temp->data_len = offset;
    return SUCCESSFUL;
}

Static int acmu_pack_ac_alarm_info(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff;
    unsigned char offset = 0;
    unsigned char data_flag;
    int alm_id = 0;

    buff = cmd_buf_temp->buf;

    /* DATA_FLAG */
    get_one_data(ACMU_DATA_ID_DATA_FLAG,&data_flag);
    buff[offset++] = data_flag & 0xEF;

    buff[offset++] = 0x01;      // 交流配电屏数量
    buff[offset++] = 0x01;      // 本屏交流配电数量

    judge_phase_volt_alarm(buff, offset);       // 输入相电压
    offset += 3;

    buff[offset++] = 0x00;       // 交流频率（未监测）
    buff[offset++] = 0x00;          // 监测熔丝数量为0 

     
    alm_id = GET_ALM_ID(ACMU_ALM_ID_SPD_C, 1, 1);
    buff[offset++] = handle_alarm_status(alm_id, 0xE1, 0x00);   // 交流防雷器异常

    buff[offset++] = 24; // 用户自定义告警数量

    alm_id = GET_ALM_ID(ACMU_ALM_ID_TOTAL_ALARM, 1, 1);
    buff[offset++] = handle_alarm_status(alm_id, 0xF0, 0x00);   // 交流总告警

    alm_id = GET_ALM_ID(ACMU_ALM_ID_AC_POWER_OFF, 1, 1);
    buff[offset++] = handle_alarm_status(alm_id, 0xE0, 0x00);   // 交流停电

    alm_id = GET_ALM_ID(ACMU_ALM_ID_PHASE_VOLT_UNBALANCE, 1, 1);
    buff[offset++] = handle_alarm_status(alm_id, 0xE2, 0x00);   // 交流相电压不平衡

    for (char i = 0; i < 12; i++)
    {
        alm_id = GET_ALM_ID(ACMU_ALM_ID_AC_OUTPUT_SWITCH_DISCONNECTION, i + 1, 1);
        buff[offset++] = handle_alarm_status(alm_id, 0x05, 0x00);    //交流输出空开断
    }

    for (char i = 0; i < 3; i++)
    {
        alm_id = GET_ALM_ID(ACMU_ALM_ID_PHASE_CURR_HIGH_ALARM, i + 1, 1);
        buff[offset++] = handle_alarm_status(alm_id, 0x02, 0x00);    //交流相电流高
    }

    alm_id = GET_ALM_ID(ACMU_ALM_ID_AC_METER_DISCONNECTED, 1, 1);
    buff[offset++] = handle_alarm_status(alm_id, 0xE3, 0x00);   // 交流电表通讯断

    alm_id = GET_ALM_ID(ACMU_ALM_ID_OIL_ENGINE_START_UP_ALARM, 1, 1);
    buff[offset++] = handle_alarm_status(alm_id, 0xE4, 0x00);   // 油机启动、

    
    alm_id = GET_ALM_ID(ACMU_ALM_ID_INPUT_RELAY1_ALARM, 1, 1);
    buff[offset++] = handle_alarm_status(alm_id, 0xE5, 0x00);    //输入干接点告警
    alm_id = GET_ALM_ID(ACMU_ALM_ID_INPUT_RELAY2_ALARM, 1, 1);
    buff[offset++] = handle_alarm_status(alm_id, 0xE5, 0x00);    //输入干接点告警
    alm_id = GET_ALM_ID(ACMU_ALM_ID_INPUT_RELAY3_ALARM, 1, 1);
    buff[offset++] = handle_alarm_status(alm_id, 0xE5, 0x00);    //输入干接点告警
    alm_id = GET_ALM_ID(ACMU_ALM_ID_INPUT_RELAY4_ALARM, 1, 1);
    buff[offset++] = handle_alarm_status(alm_id, 0xE5, 0x00);    //输入干接点告警

    for (char i = 0; i < 3; i++)
    {
        alm_id = GET_ALM_ID(ACMU_ALM_ID_OUTPUT_CURRENT, i + 1, 1);
        buff[offset++] = handle_alarm_status(alm_id, 0x02, 0x00);    //输出电流
    }

    ((cmd_buf_t*)cmd_buff)->data_len=offset;

    return SUCCESSFUL;
}

Static int acmu_pack_env_alarm_info(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff;
    unsigned char offset = 0;
    unsigned char data_flag;
    int alm_id = 0;

    buff = cmd_buf_temp->buf;

    /* DATA_FLAG */
    get_one_data(ACMU_DATA_ID_DATA_FLAG,&data_flag);
    buff[offset++] = data_flag & 0xEF;

    buff[offset++] = 0x01;      // 温度传感器数量
    buff[offset++] = temperature_sensor_alarm_judge();      // 温度传感器告警

    buff[offset++] = 0x01;      // 湿度传感器数量
    buff[offset++] = humidity_sensor_alarm_judge();     // 湿度传感器告警

    buff[offset++] = 0x01;      // 烟雾传感器数量
    alm_id = GET_ALM_ID(ACMU_ALM_ID_FUMES_SENSOR_ALARM, 1, 1);
    buff[offset++] = handle_alarm_status(alm_id, 0x04, 0x00);   // 烟雾传感器告警

    buff[offset++] = 0x01;      // 水浸传感器数量
    alm_id = GET_ALM_ID(ACMU_ALM_ID_FLOOD_SENSOR_ALARM, 1, 1);
    buff[offset++] = handle_alarm_status(alm_id, 0x04, 0x00);   // 水浸传感器告警

    buff[offset++] = 0x01;      // 门磁传感器数量
    alm_id = GET_ALM_ID(ACMU_ALM_ID_DOORMAT_SENSOR_ALARM, 1, 1);
    buff[offset++] = handle_alarm_status(alm_id, 0x04, 0x00);   // 门磁传感器告警

    buff[offset++] = 0x01;      // 空调防盗传感器数量
    buff[offset++] = 0x00;      // 空调防盗传感器告警

    buff[offset++] = 0x01;      // 电池防盗传感器数量
    buff[offset++] = 0x00;      // 电池防盗传感器告警

    buff[offset++] = 0x01;      // 接地排防盗传感器数量
    buff[offset++] = 0x00;      // 接地排防盗传感器告警

    buff[offset++] = 0x01;      // 告警器防盗传感器数量
    buff[offset++] = 0x00;      // 告警器防盗传感器告警

    buff[offset++] = 0x00;      // 用户自定义告警数量

    ((cmd_buf_t*)cmd_buff)->data_len=offset;

    return SUCCESSFUL;
}



Static int acmu_pack_time(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    time_base_t  pt_time={0};
    int offset=0;
    get_time(&pt_time);

    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;

    put_int16_to_buff(&data_buff[offset],pt_time.year);
    offset+=2;

    data_buff[offset++] = pt_time.month;
    data_buff[offset++] = pt_time.day;
    data_buff[offset++] = pt_time.hour;
    data_buff[offset++] = pt_time.minute;
    data_buff[offset++] = pt_time.second;

    ((cmd_buf_t*)cmd_buff)->data_len=offset;

    return SUCCESSFUL;
}

Static int acmu_parse_time(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    int offset = 0;
    time_base_t tm = {0};
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;

    tm.year = get_int16_data(&data_buff[offset]);
    offset+=2;
    tm.month  = data_buff[offset++];
    tm.day = data_buff[offset++];
    tm.hour = data_buff[offset++];
    tm.minute  = data_buff[offset++];
    tm.second  = data_buff[offset++];
    if( check_time_range(tm) == FAILURE )
    {
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA ;
        return SUCCESSFUL;
    }
    
    /* 设置系统时间 */
    if (set_system_time(&tm) != SUCCESSFUL) {
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA;
        return SUCCESSFUL;
    }
    pub_hisaction_save_msg(ID1_ACMU_SETSTATUS, ID2_PR_TIME, 0, NULL);
    
    return SUCCESSFUL;
}

Static int acmu_pack_ac_sw_input_status(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    unsigned char* buff;
    unsigned int offset = 0;
    unsigned char uc_data = 0;
    int i = 0;

    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf_temp != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(cmd_buf_temp->buf != NULL, FAILURE);

    buff = cmd_buf_temp->buf;
    
    get_one_data(ACMU_DATA_ID_DATA_FLAG, &buff[offset++]);
    
    if (0xFF == s_command_group)    // 多个交流屏时，交流屏数量置为1
    {
        buff[offset++] = 0x01;
    }
    
    buff[offset++]    = 0x00;                            // 监测的开关数量
    buff[offset++]    = 28  ;                            // 自定义开关数量28

    get_one_data(ACMU_DATA_ID_POWER_SOURCE, &uc_data);
    buff[offset++]    = uc_data;                         // 系统供电 0市电1；1市电2；2电池
    
    get_one_data(ACMU_DATA_ID_AC_INPUT_SWITCH, &uc_data);
    buff[offset++]    = uc_data;                         // 交流输入空开状态 

    for ( i = 0; i < RELAY_NUM; i++ )
    {
        get_one_data(ACMU_DATA_ID_INPUT_RELAY + i, &uc_data);
        buff[offset++]    = uc_data;                    // ACMU输入干结点
    }

    for ( i = 0; i < SWITCH_NUM; i++ )
    {
        get_one_data(ACMU_DATA_ID_AC_OUT_SWITCH + i, &uc_data);
        buff[offset++]    = uc_data;                    // 交流输出空开
    }

    get_one_data(ACMU_DATA_ID_EMERGENCY_LAMP_STATE, &uc_data);
    buff[offset++]    = uc_data;                        // 应急照明灯状态 

    get_one_data(ACMU_DATA_ID_LIGHT_PRO_CIRCUIT_C, &uc_data);
    buff[offset++]    = uc_data;                        // C级防雷回路

    for ( i = 0; i < 8; i++ )
    {
        get_one_data(ACMU_DATA_ID_OUTPUT_RELAY + i, &uc_data);
        buff[offset++]    = uc_data;                    // 输出干接点控制状态
    }

    /* LENID */
    cmd_buf_temp->data_len = offset;
    return SUCCESSFUL;
}

Static int check_ac_command_group(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    unsigned char* buff;
    
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf_temp != NULL, FAILURE);

    buff = cmd_buf_temp->buf;

    RETURN_VAL_IF_FAIL(buff != NULL, FAILURE);

    if(cmd_buf_temp->data_len > 0)
    {
        s_command_group = buff[0];
    }
    else
    {
        s_command_group = WRONG_DATA_LENGTH;
    }

    return SUCCESSFUL;
}


Static int parse_his_alarm(void* dev_inst, void *cmd_buf) {
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf != NULL, FAILURE);
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    RETURN_VAL_IF_FAIL(cmd_buf_temp->buf != NULL, FAILURE);

    unsigned char* buff = cmd_buf_temp->buf;
    s_command_type = (cmd_buf_temp->data_len > 1) ? buff[1] : WRONG_DATA_LENGTH;
    s_command_group = buff[0];
    return SUCCESSFUL;
}



Static int His_Alm_To_Buff(unsigned short index, cmd_buf_t *cmd_buf) {
    if (cmd_buf == NULL || cmd_buf->buf == NULL) {
        return FAILURE;
    }

    unsigned char* buff = cmd_buf->buf;
    unsigned int offset = 2;
    unsigned short total_alarms = 0;
    alarm_map_t* alarm_map = NULL;
    pub_get_saved_record_num_msg(1, &total_alarms);
    if (0xFF == s_command_group)    // 多个直流屏时
    {
        offset++;       // 直流屏数量
    }
    // 判断越界
    if (index >= total_alarms) {
        s_start_index = 0;
        cmd_buf->rtn = RTN_NODATA;
        cmd_buf->data_len = 0;
        return FALSE;
    }

    // 读取一条历史告警
    his_alarm_info_t tHisAlm = {0};
    if (pub_hisrecord_read_msg(1, 1, index, (unsigned char *)&tHisAlm) != 0) {
        cmd_buf->rtn = RTN_NODATA;
        cmd_buf->data_len = 0;
        return FALSE;
    }

    // 解析告警码
    unsigned short alm_code = tHisAlm.alarm_id;
    if(0x7FFFFFFF <= tHisAlm.start_time || 0x7FFFFFFF <= tHisAlm.end_time)      //时间戳校验
    {
        cmd_buf->rtn = RTN_NODATA;
        cmd_buf->data_len = 0;
        return FALSE;
    }
    time_base_t start_time = {0};
    time_base_t end_time = {0};
    time_t_to_timestruct(tHisAlm.start_time, &start_time);
    time_t_to_timestruct(tHisAlm.end_time, &end_time);
    int ret = get_alarm_sn(alm_code, &alarm_map);
    // 检查get_alarm_sn是否成功
    if (ret == -1) {
        cmd_buf->rtn = RTN_NODATA;
        cmd_buf->data_len = 0;
        return FALSE;
    }
    unsigned char uc_ID2 = alarm_map->id2;
    unsigned char uc_index = tHisAlm.index;

    // 打包到buf
    buff[offset++] = uc_ID2;      // 告警ID2
    buff[offset++] = (uc_index > 0) ? (uc_index - 1) : uc_index; // 告警Index
    put_time_to_buff(&buff[offset], start_time);  // 告警产生时间
    offset += 7;
    put_time_to_buff(&buff[offset], end_time);    // 告警结束时间
    offset += 7;

    // 判断是否为最后一条
    if (index == total_alarms - 1) {
        buff[0] = GROUP_TYPE_SEND_LAST;
    } else {
        buff[0] = 0;
    }

    cmd_buf->data_len = offset;
    cmd_buf->rtn = 0;
    return TRUE; // 成功读取一条
}



Static int pack_his_alarm(void* dev_inst, void *cmd_buf) {
    if (dev_inst == NULL || cmd_buf == NULL) {
        return FAILURE;
    }

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    if (cmd_buf_temp->buf == NULL) {
        return FAILURE;
    }

    unsigned char* buff = cmd_buf_temp->buf;
    unsigned char offset = 0;
    unsigned short total_alarms = 0;
    pub_get_saved_record_num_msg(1, &total_alarms);

    // 处理命令类型
    switch (s_command_type) {
        case GROUP_TYPE_RCV_START:
            s_start_index = 0;
            break;
        case GROUP_TYPE_RCV_NEXT:
            s_start_index += 1;
            break;
        case GROUP_TYPE_RCV_ERROR:
            break;
        case GROUP_TYPE_RCV_END:
            cmd_buf_temp->data_len = offset;
            return SUCCESSFUL;
        default:
            cmd_buf_temp->rtn = RTN_INVLDATA;
            cmd_buf_temp->data_len = offset;
            return SUCCESSFUL;
    }
    offset++; // 返回是否为最后一条占位
    // 协议头部填充
    if (0xFF == s_command_group)    // 多个交流屏时，交流屏数量置为1
    {
        buff[offset++] = 1;       // 直流屏数量
    }
    buff[offset++] = ( (total_alarms - s_start_index - 1) >= 10) ? 10 : (total_alarms - s_start_index - 1); //历史告警条数

    // 读取一条历史告警并打包
    int ret = His_Alm_To_Buff(s_start_index, cmd_buf_temp);
    return ret;
}

Static int acmu_pack_boot_ver(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff;
    unsigned char offset = 0;
    buff = cmd_buf_temp->buf;
    boot_info_t boot_info = {0};
    rt_memset_s(&boot_info, sizeof(boot_info_t), 0x20, sizeof(boot_info_t));

    get_one_data(ACMU_DATA_ID_BOOT_VER, &boot_info.ver);  // boot版本号
    rt_memcpy_s(&buff[offset], BOOT_VER_LEN, boot_info.ver, BOOT_VER_LEN);
    offset += 16;
    get_one_data(ACMU_DATA_ID_BOOT_VER_DATE, &boot_info.date);  // boot版本日期
    rt_memcpy_s(&buff[offset], BOOT_VER_LEN, boot_info.date, BOOT_VER_LEN);
    offset += 16;

    /* LENID */
    cmd_buf_temp->data_len = offset;

    return SUCCESSFUL;
}
