#ifndef BMS_SYSMONITOR_H
#define BMS_SYSMONITOR_H

#ifdef ENABLE_SYS_MONITOR

#include "common.h"
#include "drv_utils.h"

#include <rtthread.h>

#include <stdint.h>

#define FILENAME_SYS_MONITOR_LOG "/sysmonitor"

#define SYS_MONITOR_COLLECT_CYCLE 10000000

#define SYS_MONITOR_LOG_VER ((uint16_t)1)

#define SYS_MONITOR_MEMHEAP_NUM_MAX 1

#define SYS_MONITOR_THREAD_NAME_MAX (RT_NAME_MAX)
#define SYS_MONITOR_THREAD_NUM_MAX 31

#ifdef __cplusplus
extern "C" {
#endif

typedef struct sys_monitor_heap_info
{
    uint32_t current_available_size;
    uint32_t min_available_size;
} sys_monitor_heap_info_t;

typedef struct sys_monitor_thread_info_item
{
    char name[SYS_MONITOR_THREAD_NAME_MAX];
    uint8_t current_stack_usage;
    uint8_t max_stack_usage;
} sys_monitor_thread_info_item_t;

typedef struct sys_monitor_thread_info
{
    uint8_t num;
    sys_monitor_thread_info_item_t info[SYS_MONITOR_THREAD_NUM_MAX];
} sys_monitor_thread_info_t;

typedef struct thread_info_log
{
    char name[SYS_MONITOR_THREAD_NAME_MAX];
    uint8_t max_stack_usage;
} thread_info_log_t;

typedef struct sys_monitor_log
{
    uint16_t crc16;
    uint16_t ver;
    uint32_t min_heap_remained_size;
    uint8_t max_cpu_usage;
    uint8_t thread_num;
    thread_info_log_t thread_log[SYS_MONITOR_THREAD_NUM_MAX];
} sys_monitor_log_t;

void init_sys_monitor(void);
const sys_monitor_thread_info_t *get_thread_info(void);
const sys_monitor_heap_info_t *get_heap_info(void);
const sys_monitor_log_t *get_sys_monitor_log(void);
const BYTE get_cpu_usage(void);
BOOLEAN read_sys_monitor_log(sys_monitor_log_t *log);
BOOLEAN save_sys_monitor_log(void);
void delete_sys_monitor_log(void);

void app_idle_execute(void);

#ifdef __cplusplus
}
#endif

#endif /* ENABLE_SYS_MONITOR */

#endif /* BMS_SYSMONITOR_H */
