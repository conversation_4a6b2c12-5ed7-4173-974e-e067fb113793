﻿#include <stdio.h>
#include <rtthread.h>
#include <rtdbg.h>
#include <board.h>
#include "main.h"
#include "softbus.h"
#include "dev_dcmu.h"
#include "north_main.h"
#include "server_id.h"
#include "utils_server.h"
#include "realdata_save.h"
#include "alarm_manage.h"
#include "alarm_register.h"
#include "utils_heart_beat.h"
#include "partition_table.h"
#include "gd32f4xx_gpio.h"
#include "dcmu_protocol_north.h"
#include "dcmu_protocol_1104.h"
#include "dcmu_protocol_1363.h"
#include "dcmu_protocol_apptest.h"
#include "protocol_north_comm.h"
#include "sample.h"
#include "system_manage.h"
#include "update_and_upload_manage.h"
#include "utils_rtthread_security_func.h"
#include "gui_data_interface.h"
#include "io_control.h"
#include "log_mgr_api.h"
#include "remote_download_update_cmd.h"
#include "protocol_remote_download.h"
#include "protocol_bottom_comm.h"
#include "remote_download_update_handle.h"
#include "MIBTable.h"
#include "his_record.h"
#include "reset.h"
#include "Menu.h"
#include "wtd_ctrl.h"

static char s_alarm_thread_stack[THREAD_STACK_SIZE_COMM] RAM_SECTION_BSS;
static char s_usart1_thread_stack[THREAD_STACK_SIZE_COMM] RAM_SECTION_BSS;
static char s_usart2_thread_stack[THREAD_STACK_SIZE_COMM] RAM_SECTION_BSS;
static char s_can0_thread_stack[THREAD_STACK_SIZE_COMM] RAM_SECTION_BSS;
static char s_sample_thread_stack[THREAD_STACK_SIZE_SAMPLE] RAM_SECTION_BSS;
static char s_io_control_thread_stack[THREAD_STACK_SIZE_IO_CONTROL] RAM_SECTION_BSS;
static char s_thread_stack_log[THREAD_STACK_SIZE_LOG] RAM_SECTION_BSS;
static char s_system_thread_stack[SYSTEM_THREAD_STACK_SIZE] RAM_SECTION_BSS;
static char s_key_process_thread_stack[THREAD_STACK_SIZE_KEY_PROCESS] RAM_SECTION_BSS;

rt_uint8_t mempool_32[BLOCK_COUNT_32 * (BLOCK_SIZE_32+4)] RT_SECTION(".bss");
rt_uint8_t mempool_64[BLOCK_COUNT_64 * (BLOCK_SIZE_64+4)] RT_SECTION(".bss");
rt_uint8_t mempool_128[BLOCK_COUNT_128 * (BLOCK_SIZE_128+4)] RT_SECTION(".bss");
rt_uint8_t mempool_512[BLOCK_COUNT_512 * (BLOCK_SIZE_512+4)] RT_SECTION(".bss");

softbus_mempool_all_t softbus_mempool_info;
download_file_name_info_t download_file_name[] = {{0, "CCLIB.BIN", ZK_DOWNLOAD},
                                                  {0, "DCMU_APP.bin", APP_DOWNLOAD}};
update_info_save_t update_info_save = {UPDATE_INFO, 0, thread_monitor_update_heartbeat};
bottom_transfer_file_name_info_t s_bottom_transfer_file_name[] = {
    {0, "CCLIB.BIN", ZK_DOWNLOAD, ZK_DOWNLOAD_AREA_SIZE},
};
bottom_update_info_save_t s_bottom_update_info_save = {UPDATE_INFO, 0, thread_monitor_update_heartbeat};

static server_info_t g_server_group[] = {
    {{{THREAD_LOG ,       "log_manage",     sizeof(s_thread_stack_log),      s_thread_stack_log,      THREAD_PRIORITY  }}, init_log_mgr,  log_mgr_thread_entry},
    {{{ALARM_MANAGE_SERVER_ID,       "alarm_manage",         sizeof(s_alarm_thread_stack),     s_alarm_thread_stack,      THREAD_PRIORITY  }}, init_alarm_manage, alarm_main},
    {{{COMM_USART1_SERVER_ID ,    "comm_usart1",        sizeof(s_usart1_thread_stack),      s_usart1_thread_stack,      THREAD_PRIORITY  }}, init_usart1_comm, usart1_comm_thread},
    {{{COMM_USART2_SERVER_ID,      "comm_usart2",       sizeof(s_usart2_thread_stack),     s_usart2_thread_stack,      THREAD_PRIORITY  }}, init_usart2_comm, usart2_comm_thread},
    {{{COMM_CAN0_SERVER_ID,      "comm_can0",       sizeof(s_can0_thread_stack),     s_can0_thread_stack,      THREAD_PRIORITY  }}, init_can0_comm, can0_comm_thread},
    {{{SAMPLE_SERVER_ID,   "sample",  sizeof(s_sample_thread_stack),  s_sample_thread_stack,  THREAD_PRIORITY}}, sample_init, sample_main},
    {{{IO_CONTROL_SERVER_ID,   "io_control",  sizeof(s_io_control_thread_stack),  s_io_control_thread_stack,  KEY_SCAN_THREAD_PRIORITY}}, init_io_control, io_control_main},
    {{{KEY_PROCESS_SERVER_ID,   "key_process",  sizeof(s_key_process_thread_stack),  s_key_process_thread_stack,  THREAD_PRIORITY}}, init_key_process, key_process_main},
    {{{SYSTEM_MANAGE_SERVER_ID,       "system_manage",         sizeof(s_system_thread_stack),     s_system_thread_stack,      THREAD_PRIORITY  }}, init_sys_manage, system_manage_main},
};

static dev_init_t dev_init_tab[] = {
    {DEV_CSU, init_dev_dcmu_north, NULL},
    {DEV_NORTH_1104, init_dev_dcmu_1104, NULL},
    {DEV_NORTH_1363, init_dev_dcmu_1363, NULL},
    {DEV_REMOTE_DOWNLOAD, init_remote_download_dev_type, NULL},
    {DEV_BOTTOM_COMM_UPDATE_UPLOAD, init_dcmu_bottom_comm, NULL},
    {DEV_NORTH_DCMU_APPTEST, init_dev_dcmu_apptest, NULL},
    {DEV_NORTH_DCMU_CAN0, init_dcmu_bottom_comm_can0, NULL}
};

static protocol_process_t protocol_process_tab[] = {
    {PROTOCOL_YD_1363, s1363_pack, s1363_parse},
    {PROTOCOL_NORTH_COMM, snorth_pack, snorth_parse},
    {PROTOCOL_DOWNLOAD, download_pack, download_parse},
    {PROTOCOL_BOTTOM_COMM, bottom_pack, bottom_parse},
};

static link_type_t link_type_tab[] = {
    {LINK_COM, s485_dev_init, com_dev_read, com_dev_write, com_dev_set},
    {LINK_CAN, can_dev_init,  can_dev_read, can_dev_write, can_dev_set},
};

static link_inst_t link_inst_tab[] = {
    {LINK_DCMU_NORTH, LINK_COM, "usart1"},
    {LINK_DCMU,       LINK_COM, "usart2"},
    {LINK_DCMU_CAN,   LINK_CAN, "can0", CAN_FRAME_DATA_LEN_8},
};

/* 设备初始化*/
static char init_dcmu_link_tab(void)
{
    char ret = SUCCESSFUL;
    // 注册链路类型信息
    register_link_type_info(link_type_tab, sizeof(link_type_tab) / sizeof(link_type_t));
    // 注册链路实例表信息
    register_link_inst_tab_info(link_inst_tab, sizeof(link_inst_tab)/sizeof(link_inst_tab[0]));
    // 注册协议处理信息
    register_protocol_process_info(protocol_process_tab, sizeof(protocol_process_tab) / sizeof(protocol_process_t));
    // 初始化设备表
    ret = init_dev_tab(dev_init_tab, sizeof(dev_init_tab)/sizeof(dev_init_tab[0]));
    return ret;
}

static void init_softbus_config(void)
{
    softbus_mempool_info.mempool_32.mempool_addr = &mempool_32[0];
    softbus_mempool_info.mempool_32.mempool_size = sizeof(mempool_32);
    softbus_mempool_info.mempool_32.block_size = BLOCK_SIZE_32;
    softbus_mempool_info.mempool_64.mempool_addr = &mempool_64[0];
    softbus_mempool_info.mempool_64.mempool_size = sizeof(mempool_64);
    softbus_mempool_info.mempool_64.block_size = BLOCK_SIZE_64;
    softbus_mempool_info.mempool_128.mempool_addr = &mempool_128[0];
    softbus_mempool_info.mempool_128.mempool_size = sizeof(mempool_128);
    softbus_mempool_info.mempool_128.block_size = BLOCK_SIZE_128;
    softbus_mempool_info.mempool_512.mempool_addr = &mempool_512[0];
    softbus_mempool_info.mempool_512.mempool_size = sizeof(mempool_512);
    softbus_mempool_info.mempool_512.block_size = BLOCK_SIZE_512;
    return ;
}

int change_update_flag(unsigned char ucMode)
{
    update_file_manage_t ptFileManage;
    rt_memset_s(&ptFileManage, sizeof(update_file_manage_t), 0xFF, sizeof(update_file_manage_t));
    read_download_tmpInfo(&ptFileManage);
    ptFileManage.ucFlag = ucMode;
    ptFileManage.crc = crc_cal((unsigned char *)&ptFileManage, offsetof(update_file_manage_t, crc));
    write_download_tmpInfo(&ptFileManage);
    return SUCCESSFUL;

}

static void save_debug_info(void)
{
    char buff[33] = {0};
    unsigned char reset_source = 0;
    time_base_t tTime;

    reset_source = save_reset_reason();
    rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));
    rt_snprintf_s(buff, sizeof(buff), "RstSource=%02x", reset_source);
    SetTraceStr(2,buff);
    rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));
    rt_memset_s(&tTime, sizeof(tTime), 0, sizeof(tTime));
    get_time(&tTime);
    rt_snprintf_s(buff, sizeof(buff), "Rst%04d%02d%02d %02d:%02d:%02d", tTime.year, tTime.month, tTime.day, tTime.hour, tTime.minute, tTime.second);
    SetTraceStr(0, buff);

    return;
}

int main(void)
{


    init_crc();
    init_flash_page_size(FLASH_PAGE_SIZE);
    init_real_data_memory();
    register_product_para();
    register_dcmu_alarm();
    if (SUCCESSFUL != storage_init(&part_tab[0], sizeof(part_tab)/sizeof(part_info_t))) {
        return FAILURE;
    }
    // 确保文件系统初始化完成
    if (initFileSys() != SUCCESSFUL) {
        rt_kprintf("File system init failed!\n");
        return RT_ERROR;
    }
    if(init_dir() != SUCCESSFUL)
    {
        rt_kprintf("init_dir failed!\n");
        return RT_ERROR;
    }
    init_his_record();
    init_softbus_config();
    softbus_init(&softbus_mempool_info);
    init_para_manage();
    init_dcmu_link_tab();
    update_download_init();
    init_mib_table_para_from_dic();

    dcmu_update_host_address();
    set_base_addr(DCMU_BASE_ADDR);

    // 然后再创建需要写文件的服务
    create_server(&g_server_group[0], sizeof(g_server_group)/sizeof(g_server_group[0]));
    register_download_file_name(download_file_name, sizeof(download_file_name) / sizeof(download_file_name_info_t), &update_info_save);
    bottom_register_download_file_name(s_bottom_transfer_file_name, sizeof(s_bottom_transfer_file_name) / sizeof(bottom_transfer_file_name_info_t), &s_bottom_update_info_save);
    save_debug_info();
    change_update_flag(FLAG_OK);

    return RT_EOK;
}
