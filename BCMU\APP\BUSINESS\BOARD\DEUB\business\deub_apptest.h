#ifndef _DEUB_APPTEST_H
#define _DEUB_APPTEST_H

#ifdef __cplusplus
extern "C"
{
#endif /*__cplusplus */

#include "device_type.h"

#define DEUB_SOFTWARE_NAME        "DEUB"

// CID2

#define CID2_TRIG_APPTEST               0x00   // 触发apptest命令
#define CID2_EXIT_APPTEST               0x01   // 退出apptest
#define CID2_GET_FACT_INFO              0x11   // 获取厂家信息
#define CID2_CHIP_TEST_INFO             0x12   // 获取芯片测试信息
#define CID2_SET_HARDWARE_VER           0x13   // 设置硬件版本号
#define CID2_GET_HARDWARE_VER           0x14   // 获取硬件版本号
#define CID2_SET_BOARD_MANU_DATE        0x15   // 设置生产日期
#define CID2_GET_BOARD_MANU_DATE        0x16   // 设置生产日期
#define CID2_GET_DI_INFO                0x17   // 获取DI信息
#define CID2_GET_AI                     0x18   // 获取ai信号
#define CID2_GET_INSULATION_RESISTANCE  0x19   // 获取绝缘阻抗
#define CID2_GET_PROTO_VER              0x4F   // 获取协议版本号
#define CID2_GET_DEVICE_ADDR            0x50   // 获取单板地址
#define CID2_SET_CALIBRATE_PARA         0x20   // 设置校准系数
#define CID2_GET_CALIBRATE_PARA         0x21   // 获取校准系数
#define CID2_SET_SN                     0x22   // 设置序列号
#define CID2_GET_SN                     0x23   // 获取序列号


#define DEUB_APPTEST_TRIG             1   // 触发apptest命令
#define DEUB_SET_HARDWARE_VER         2   // 设置硬件版本号
#define DEUB_GET_HARDWARE_VER         3   // 获取硬件版本号
#define DEUB_GET_FACT_INFO            4   // 获取厂家信息
#define DEUB_GET_AI                   5   // 获取ai信号
#define DEUB_GET_RESISTANCE           6   // 获取阻抗
#define DEUB_GET_DI_INFO              7   // 获取DI信息
#define DEUB_APPTEST_EXIT             8   // 退出apptest
#define DEUB_GET_PROTO_VER            9   // 获取协议版本号
#define DEUB_CHIP_TEST_INFO           10  // 获取芯片测试信息
#define DEUB_SET_BOARD_MANU_DATE      11  // 设置单板生产日期
#define DEUB_GET_BOARD_MANU_DATE      12  // 获取单板生产日期
#define DEUB_GET_COMM_ADDR            13  // 获取通信地址信息
#define DEUB_SET_CALIBRATE_PARA       14  // 设置校准系数
#define DEUB_GET_CALIBRATE_PARA       15  // 获取校准系数
#define DEUB_SET_SN                   16  // 设置序列号
#define DEUB_GET_SN                   17  // 获取序列号



#define SOFTWARE_NAME_LEN              32   // 软件名称长度
#define SOFTWARE_VER_LEN               32   // 软件版本号长度
#define SOFTWARE_DATE_LEN              10    // 软件版本日期长度
#define SOFTWARE_INFO_LEN              (SOFTWARE_NAME_LEN + SOFTWARE_VER_LEN + SOFTWARE_DATE_LEN) // 软件信息长度

dev_type_t* init_deub_dev_apptest(void);
int apptest_get_ai_pack(void* dev_inst, void* cmd_buff);
#ifdef __cplusplus
}
#endif //  __cplusplus

#endif //
