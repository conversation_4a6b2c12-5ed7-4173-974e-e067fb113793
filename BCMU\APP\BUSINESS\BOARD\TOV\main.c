#include <stdio.h>
#include <rtthread.h>
#include <rtdevice.h>
#include <fal.h>
#include <dfs_fs.h>
#include "data_type.h"
#include "utils_data_transmission.h"
#include "utils_server.h"
#include "server_id.h"
#include "syswatch.h"
#include "utils_thread.h"
#include "main.h"
#include "utils_heart_beat.h"
#include "utils_flag.h"
#include "app_config.h"
#include "sample.h"
#include "north_main.h"
#include "his_record.h"
#include "partition_table.h"
#include "storage.h"
#include "dev_csu_comm.h"
#include "north_main_can.h"
#include "log_mgr_api.h"
#include "tov_dev_update_manage_handle.h"
#include "para_manage.h"
#include "pin_define.h"
#include "io_ctrl_api.h"
#include "msg_id.h"
#include "4g_mgr.h"
#include "tov_4g_interface.h"
#include "mqtt_main.h"
#include "syswatch.h"
#include "unified_id_interface.h"
#include "realdata_save.h"


static char s_sample_thread_stack[SAMPLE_THREAD_STACK_SIZE];
static char s_north_thread_stack[NORTH_THREAD_STACK_SIZE] RAM_SECTION_BSS;
static char s_north_can_thread_stack[NORTH_THREAD_STACK_SIZE] RAM_SECTION_BSS;
static char s_log_mgr_thread_stack[LOG_MGR_THREAD_STACK_SIZE];
static char s_net_4g_thread_stack[NET_4G_THREAD_STACK_SIZE] RAM_SECTION_BSS;
static char s_mqtt_thread_stack[MQTT_THREAD_STACK_SIZE] RAM_SECTION_BSS;


static rt_device_t wdg_dev;
static io_item_info_t run_led_info = {OUTPUT_LED, 1, LED_RUN_PIN, LED_CTRL_MSG};
static io_item_info_t fault_led_info = {OUTPUT_LED, 0, LED_FAULT_PIN, LED_CTRL_MSG};


rt_uint8_t mempool_32[BLOCK_COUNT_32 * BLOCK_SIZE_32] RAM_SECTION_BSS;
rt_uint8_t mempool_64[BLOCK_COUNT_64 * BLOCK_SIZE_64] RAM_SECTION_BSS;
rt_uint8_t mempool_512[BLOCK_COUNT_512 * BLOCK_SIZE_512] RAM_SECTION_BSS;

softbus_mempool_all_t softbus_mempool_info;


static protocol_process_t protocol_process_tab[] = {
    {PROTOCOL_BOTTOM_COMM, bottom_pack, bottom_parse},
};

// 添加线程需要在thread_id中添加对应的线程号
static server_info_t g_server_group[] = {
    /*   服务ID                  服务名字            栈大小                             栈的起始地址        优先级*/
    
    {{{TOV_NORTH_SERVER_ID,       "north_485",    sizeof(s_north_thread_stack),     s_north_thread_stack,      13}}, init_north,      north_main},
    {{{TOV_NORTH_CAN_SERVER_ID,   "north_can",    sizeof(s_north_can_thread_stack), s_north_can_thread_stack,  13}}, init_north_can,  north_main_can},
    {{{TOV_LOG_MGR_SERVER_ID,     "LOG_MGR",      sizeof(s_log_mgr_thread_stack),   s_log_mgr_thread_stack,    13}}, init_log_mgr,    log_mgr_thread_entry},
    {{{TOV_4G_SERVER_ID,          "4G_MGR",       sizeof(s_net_4g_thread_stack),    s_net_4g_thread_stack,     13}}, net_4g_init,     net_4g_thread_entry},
    {{{TOV_MQTT_ID,               "mqtt",         sizeof(s_mqtt_thread_stack),      s_mqtt_thread_stack,       13}}, init_mqtt,     mqtt_thread_entry},
    {{{TOV_SAMPLE_SERVER_ID,      "sample",       sizeof(s_sample_thread_stack),    s_sample_thread_stack,     4}}, init_sample,     sample_main},
};

static link_type_t link_type_tab[] = {
    {LINK_COM, s485_dev_init, com_dev_read, com_dev_write, com_dev_set},
    {LINK_CAN, can_dev_init, can_dev_read, can_dev_write, can_dev_set},
};


static dev_init_t dev_init_tab[] = {
    {DEV_TOV, init_dev_tov_com, NULL},
    {DEV_CSU_CAN, init_dev_csu_can, NULL},
};

server_info_t* get_server_group(int *num)
{
    *num = sizeof(g_server_group)/sizeof(g_server_group[0]);
    return &g_server_group[0];
}

static link_inst_t link_inst_tab[] = {
    {LINK_CSU_CAN,      LINK_CAN, "can1",   CAN_FRAME_DATA_LEN_8},
    {LINK_TOV_COM,      LINK_COM, "uart2"},
};
int init_file_sys(void)
{
    if(SUCCESSFUL != init_file_sys_mutex())
    {
        return RT_ERROR;
    }

    return RT_EOK;
}
INIT_ENV_EXPORT(init_file_sys);

static void init_softbus_config(void)
{
    softbus_mempool_info.mempool_32.mempool_addr = &mempool_32[0];
    softbus_mempool_info.mempool_32.mempool_size = sizeof(mempool_32);
    softbus_mempool_info.mempool_32.block_size = BLOCK_SIZE_32;

    softbus_mempool_info.mempool_64.mempool_addr = &mempool_64[0];
    softbus_mempool_info.mempool_64.mempool_size = sizeof(mempool_64);
    softbus_mempool_info.mempool_64.block_size = BLOCK_SIZE_64;

    softbus_mempool_info.mempool_512.mempool_addr = &mempool_512[0];
    softbus_mempool_info.mempool_512.mempool_size = sizeof(mempool_512);
    softbus_mempool_info.mempool_512.block_size = BLOCK_SIZE_512;
    return ;
}

static const data_access_interface_t id_base_interface = {
    .get_data = unified_get_data,
    .set_data = unified_set_data,    
    .linear_search = linear_search_id_index
};

int init_main(void)
{
    //初始化
    init_softbus_config();
    softbus_init(&softbus_mempool_info);
    init_crc();
    if (SUCCESSFUL != storage_init(&part_tab[0], sizeof(part_tab)/sizeof(part_info_t))) 
    {
        return FAILURE;
    }
    init_his_record();
    register_product_para();
   
    if(init_para_manage() != SUCCESSFUL)
    {
        rt_kprintf("init_para_manage failure\n");
        return FAILURE;
    }
    
    return SUCCESSFUL; 
}
static void feed_dog(void)
{
    rt_device_control(wdg_dev, RT_DEVICE_CTRL_WDT_KEEPALIVE, NULL);
    return ;
}

static int init_watchdog(void)
{
    wdg_dev = rt_device_find("wdt");
    if (RT_NULL == wdg_dev)
    {
        return -RT_ERROR;
    }
    if (RT_EOK != rt_device_control(wdg_dev, RT_DEVICE_CTRL_WDT_START, RT_NULL))
    {
        return -RT_ERROR;
    }
    rt_thread_idle_sethook(feed_dog);

    return RT_EOK;
}

int sys_run_result()
{
    bottom_update_manage_t update_info = {0};
    if(handle_storage(read_opr, ONCHIP_UPDATE_INFO, (unsigned char*)&update_info, sizeof(bottom_update_manage_t), 0) != SUCCESSFUL)
    {
        return FAILURE;
    }
    update_info.count = 0;
    update_info.sys_run_flag = TRUE;
    write_update_info(&update_info);
    return  SUCCESSFUL;
}

int main(void)
{
    server_info_t *g_server_group = NULL;
    int server_num = 0;

    /*1、初始化*/
    init_watchdog();
    init_flash_page_size(FLASH_PAGE_SIZE);
    init_main();
    //初始化实时数据内存区
    init_real_data_memory();
    register_link_type_info(link_type_tab, sizeof(link_type_tab) / sizeof(link_type_t));
    register_link_inst_tab_info(link_inst_tab, sizeof(link_inst_tab)/sizeof(link_inst_tab[0]));
    // 注册 协议所使用的函数接口
    register_protocol_process_info(protocol_process_tab, sizeof(protocol_process_tab) / sizeof(protocol_process_t));
    register_data_access_interface(&id_base_interface);
    // 注册协议
    init_dev_tab(dev_init_tab, sizeof(dev_init_tab)/sizeof(dev_init_tab[0]));
    set_host_addr(1);
    // 注册4G相关接口
    register_4g_func();
    /*2、创建服务*/
    register_io_item_info(&run_led_info);
    register_io_item_info(&fault_led_info);
    g_server_group = get_server_group(&server_num);
    create_server(g_server_group, server_num);
    sys_run_result();//后面需要引入守护线程
    syswatch_init();
    return RT_EOK;
}
