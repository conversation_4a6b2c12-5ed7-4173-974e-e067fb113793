/*
 * @file    :
 * @brief   :
 * @details :
 * <AUTHOR> raojing
 * @Date    : 2023-3-27
 * @LastEditTime: 2023-3-27
 * @version : V0.0.1
 * @para    : Copyright (c)
 *            ZTE Corporation
 * @par     : History
 *     version: author, date, descn
 */
/*
 * Copyright (c) 2006-2018, RT-Thread Development Team
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Change Logs:
 * Date           Author       Notes
 * 2018-11-7      SummerGift   first version
 */

#ifndef A604_ADC_H_
#define A604_ADC_H_

#ifdef __cplusplus
extern "C" {
#endif

#ifdef USING_DEVICE_60XA_UIB01
typedef enum {
	 UIB01_TB_IN_3 = 0,
	 UIB01_TB_IN_4,
	 SMOKR_IN,
	 PIN_DOOR_IN,
	 PIN_WATER_IN,
	 UIB01_HUM_IN,   // 湿度并不是由adc采样得到，放在这里只是便于处理
	 UIB01_RLY_IN1,
	 UIB01_RLY_IN2,
	 UIB01_RLY_IN3,
	 UIB01_RLY_IN4,
	 __2V5REF,
     SAMPLE_CHANNL_END,
}ADC_SAMPLE_CHANNEL;
#else
typedef enum 
{
	 FUSE_AD_01 = 0,
	 FUSE_AD_02,
	 FUSE_AD_03,
	 FUSE_AD_04,
	 FUSE_AD_05,
	 FUSE_AD_06,
	 FUSE_AD_07,
	 FUSE_AD_08,
	 FUSE_AD_09,
 	 FUSE_AD_10,
 	 FUSE_AD_11,
	 FUSE_AD_12,
	 AIR_AD_01,
	 DCFL_AD_01,
	 CFL_AD_01,
	 TBIN_AD_01,
	 TBIN_AD_02,
	 I_AD_01,
	 I_AD_02,
	 I_AD_03,
	 I_AD_04,
	 I_AD_05,
	 I_AD_06,
	 I_AD_07,
	 I_AD_08,

	 VB_AD_01,
	 VB_AD_02,
	 VB_AD_03,
	 VB_AD_04,

	 VB_AD_05,
	 VB_AD_06,
	 VL_AD,

	 AIN_AD_01,
	 AIN_AD_02,
	 AIN_AD_03,
	 AIN_AD_04,
	 VBM_AD_01,
	 VBM_AD_02,
	 VBM_AD_03,
	 VBM_AD_04,
	 _2VREF,

     SAMPLE_CHANNL_END,
}ADC_SAMPLE_CHANNEL;
#endif

typedef struct 
{
	 ADC_SAMPLE_CHANNEL    sample_channel;
	 int                   select_status;
	 int                   adc_channel;
}ADC_CHANNEL_INFO_STRUCT;

int adc_get_data_by_channel(ADC_SAMPLE_CHANNEL channel);
int get_board_addr(void);
void hal_board_gpio_init(void);

#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // A604_ADC_H_;
