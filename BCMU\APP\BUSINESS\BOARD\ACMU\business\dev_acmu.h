#ifndef _DEVICE_ACMU_H
#define _DEVICE_ACMU_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "utils_math.h"

/*-----设备类型-----*/
#define DEV_CSU        1
#define DEV_ACMU       2
#define DEV_NORTH_1104     3
#define DEV_NORTH_1363     4
#define DEV_SOUTH     5
#define DEV_REMOTE_DOWNLOAD     6
#define DEV_BOTTOM_COMM_UPDATE_UPLOAD  7
#define DEV_NORTH_ACMU_APPTEST  8
#define DEV_NORTH_ACMU_CAN1  9

#define FLAG1_ALMCH		        0x01	// 实时告警变化标志
#define FLAG1_PARACH		    0x02	// 可设参数变化标志
#define FLAG1_F				    0x40	// 报文组结束标志F
#define FLAG1_E			        0x80	// 报文结束标志E
#define FLAG2_HISALMOV	        0x01	// 历史告警溢出标志
#define FLAG2_RST			    0x40	// 复位标志
#define FLAG2_HISALM		    0x80	// 有未读取历史告警标志

#define DATAFLAG1		        0x01
#define DATAFLAG2		        0x02

#define ACMU_BASE_ADDR 0x50

#ifdef UNITEST
static int mock_addr = 0XFF;
#define BOOT_DATA_ADDRESS                 (&mock_addr)
#else
#define BOOT_DATA_ADDRESS       0x08007800
#endif

#define TIMEOUT_COMMFAIL_MAXTIME    1000

#define MAJOR_VER  1
#define MINOR_VER  0

#define AC_PHASE_NUM   3
#define SWITCH_NUM     12
#define RELAY_NUM      4
#define LEN_RELAYNAME  13

#define PREC_AC_VOLT                        1       // 交流电压精度
#define POW_PREC_AC_VOLT                    pow_10(PREC_AC_VOLT) // 交流电压精度取幂
#define PREC_AC_CURR                        1       // 交流电流精度
#define POW_PREC_AC_CURR                    pow_10(PREC_AC_CURR) // 交流电流精度取幂
#define PREC_ACEM_POWER_FACT                2       // 交流电表功率因子精度
#define POW_PREC_ACEM_POWER_FACT            pow_10(PREC_ACEM_POWER_FACT) // 交流电表功率因子精度取幂
#define PREC_ACEM_POWER                     1       // 交流电表功率精度
#define POW_PREC_ACEM_POWER                 pow_10(PREC_ACEM_POWER) // 交流电表功率精度取幂
#define PREC_ACEM_TOTAL_ENERGY              1       // 交流电表电能精度
#define POW_PREC_ACEM_TOTAL_ENERGY          pow_10(PREC_ACEM_TOTAL_ENERGY) // 交流电表电能精度取幂

/* 设备缓冲区长度 */
#define R_BUFF_LEN                    2048       ///<  接收缓冲区长度
#define S_BUFF_LEN                    2048       ///<  发送缓冲区长度

#define VER_20                0x20                    ///<  1104协议2.0版本号
#define VER_22                0x22                    ///<  1363协议2.2版本号

#define UNKNOW_MODE     0
#define OLD_MODE        1
#define NEW_MODE        2

#define ALM_NORMAL  0
#define LOW         1
#define HIGH        2
#define LACK_PHASE  3

#define OLD_PARA_LEN  17

//告警级别定义
#define ALARMCLASS_MASK        0        // 屏蔽
#define ALARMCLASS_CRITICAL    1        // 紧急
#define ALARMCLASS_MAJOR       2        // 重要
#define ALARMCLASS_MINOR       3        // 一般
#define ALARMCLASS_WARNING     4        // 轻微

enum
{
    ACEM_NORMAL,
    ACEM_COMM_FAIL,
    ACEM_NO_CONFIG,
    ACEM_COMM_TRANS,
};

typedef enum
{
    PROTOCOL_NORTH_INDEX = 0,
    PROTOCOL_1104_INDEX,
    PROTOCOL_1363_INDEX,
    PROTOCOL_DOWNLOAD_INDEX,
    PROTOCOL_BOTTOM_INDEX,
    PROTOCOL_INDEX_MAX,
} dev_protocol_index_e;

typedef enum {
    COMM_BAUD_RATE_1200 = 0,
    COMM_BAUD_RATE_2400,
    COMM_BAUD_RATE_4800,
    COMM_BAUD_RATE_9600,
    COMM_BAUD_RATE_19200,
    COMM_BAUD_RATE_38400,
    COMM_BAUD_RATE_NUM
}acmu_comm_baudrate_type_e;

typedef struct
{
    float phase_volt[3];        // 交流相电压
    int line_curr[3];           // 交流线电流
    int line_volt[3];           // 交流线电压
    float phase_curr[3];        // 交流相电流
    float total_act_power;      // 总有功功率
    float phase_act_power[3];   // 三相有功功率
    float total_react_power;    // 总无功功率
    float total_apparent_power; // 总视在功率
    float total_power_factor;   // 总功率因数
    int frequency;              // 交流频率
    float total_energy;         // 总电能
    float power_factor[3];      // 交流相功率因数
} acem_analog_data_t;

typedef struct
{
    float phase_voltage_unbalance_offset;                  ///< 相电压不平衡阀值
    float ac_phase_voltage_max_offset;                     ///< 交流输入线/相电压上限
    float ac_phase_voltage_min_offset;                     ///< 交流输入线/相电压下限
    float ac_current_max_offset;                           ///< 交流输入电流上限
    unsigned char power_off_alarm_level;                   ///< 交流停电告警属性设置
    unsigned char phase_lost_alarm_level;                  ///< 交流缺相告警属性设置
    unsigned char phase_volt_unbalance_alarm_level;        ///< 交流相电压不平衡告警属性设置
    unsigned char volt_high_alarm_level;                   ///< 交流相电压高告警属性设置
    unsigned char volt_low_alarm_level;                    ///< 交流相电压低告警属性设置
    unsigned char curr_hign_alarm_level;                   ///< 交流相电流高告警属性设置
    unsigned char spd_c_alarm_level;                       ///< C级防雷器损坏告警属性设置
    unsigned char output_switch_disconnection_alarm_level; ///< 交流输出空开断告警属性设置
    unsigned char obligate_para_s;                         ///< 预留公共参数字节数
    float ac_line_voltage_max;                             ///< 交流线电压高阈值
    float ac_line_voltage_min;                             ///< 交流线电压低阈值
    unsigned char ac_line_voltage_max_alarm_level;         ///< 交流线电压高告警属性设置
    unsigned char ac_line_voltage_min_alarm_level;         ///< 交流线电压低告警属性设置
} acmu_para_data_t;

typedef struct boot_info
{
    char ver[16];
    char date[16];
} boot_info_t;

unsigned char is_show_acem_fact(void);
int get_acem_state(void);
unsigned char get_acem_cfg(void);
int updata_acem_fact_info(void);
int get_acem_show_data(acem_analog_data_t* acemShowData);
unsigned char get_acem_comm_fail(void);
unsigned char set_acem_timeout_flag(unsigned char acem_comm);
unsigned char get_acem_timeout_flag(void);
int acmu_update_baudrate(void);
int acmu_update_host_address(void);
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _DEVICE_ACMU_H