/**************************************************************************
* Copyright (C) 2010-2011, ZTE Corporation.
* 版权信息：（C）2011-2012，中兴通讯股份有限公司动力开发部版权所有
* 系统名称：ZXDUPA-PMSA V2.1平台软件
* 文件名称：wireless.h
* 文件说明：GPRS通讯模块头文件
* 作    者：王威
* 版本信息：V2.1
* 设计日期：2011-04-09
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/
#ifndef _GPRS_H
#define _GPRS_H

#ifdef __cplusplus
extern "C" {
#endif

#include "comm.h"

#define PI              3.14159265358979f
#define EARTH_RADIUS    6371.004    //地球半径km

#define  MAX_TX_HZ           70
#define  MAX_SMS_SEND        30

#define  SMS_SEND_RETRY         2

#define STR_IMEI_NOT_FOUND     "IMEI NOT FOUND"
#define STR_IMSI_NOT_FOUND     "IMSI NOT FOUND"

#define  IMEI_LEN        15
#define  IMSI_LEN        20

#define  TRY_MAX         20

#define  CSQ_SIGNAL_FAULT  (99)
#define  CSQ_SIGNAL_FAULT_RESET_COUNTER_MAX  (300)
#define  CSQ_GPS_FAULT_COUNTER_MAX (10)

typedef enum{
    GPS_MODE = 1,       //GPS定位方式
    BEIDOU_MODE,        //北斗定位方式
    MIX_MODE,           //混合定位方式
    MAX_ID,             //无效方式
}LocateMode;


typedef enum {
    GPS_ONLY = 1,           //只支持GPS定位
    BEIDOU_ONLY,            //只支持北斗定位
    GPS_AND_BEIDOU,         //支持北斗和GPS
    MAX_Mode                //无效方式
} ModuleSupportLocateMode;   //模块支持的定位方式


enum ModelStage
{
    GPS_STAGE = 0,
    SMS_STAGE,
};

typedef enum ModelStage T_ModelStage;
/*********************  数据结构定义  **********************/

typedef struct
{
	T_ModelStage ucStage;
    BOOLEAN bGetPlace;
}T_Mc20Manage;

typedef struct
{
    LONG slSendLen;    //短信长度
    char auSmsData[MAX_TX_HZ * 2];   //发送缓冲区
}T_SendBuff;

typedef struct
{
    LONG slSmsSendTop;    //发送短信队列头
    LONG slSmsSendTail;    //发送短信队列尾
    T_SendBuff atSmsData[MAX_SMS_SEND];   //发送短信缓冲队列
}T_SmsSendBuff;

typedef struct
{
	WORD  wHeaderLen;                 //协议帧包头长度(DATA之前的长度)
	BYTE  ucVer;                      //协议版本号
	BYTE  ucFrameType;                //协议帧类型
	BYTE  ucIDLen;                    //设备唯一标识码长度
	BYTE  aucNeIDBuff[100];           //设备唯一标识码
	WORD  wDataLen;                   //数据域长度

    WORD wSendLen;
    WORD wRcvLen;                            //接收数据原始长度
    BYTE aucSendData[LEN_COMM_SEND_BUF];
    BYTE aucRcvData[LEN_COMM_REC_BUF];       //接收原始数据
}T_GprsBuff;

#pragma pack(push, 1)
typedef struct
{
	BOOLEAN bGpsLocated; //是否定位上
	BYTE  ucLongDirect; //经度方向
    FLOAT fLongtitude;  //经度
    BYTE  ucLatiDirect; //纬度方向
    FLOAT fLatitude;    //纬度

    WORD    wYear;                  /* 年 */
    BYTE    ucMonth;                /* 月 */
    BYTE    ucDay;                  /* 日 */
    BYTE    ucHour;                 /* 时 */
    BYTE    ucMinute;               /* 分 */
    BYTE    ucSecond;               /* 秒 */

    FLOAT fSpeed;       //速度
    FLOAT fAzimuth;     //方位角
    BYTE  ucStarNum;    //卫星数量
}T_GpsData;
#pragma pack(pop)

typedef struct
{
	BYTE	ucMC20Stage; //当前状态
	BOOLEAN	bGprsHeartFlag;
	BOOLEAN	bGpsStartFlag;
	BOOLEAN bApptestGprsStat;
	T_GpsData tGpsData;
	BOOLEAN bSIMCardOk;
	BYTE ucSingnalQuality;
}T_GpsDebugData;

typedef struct 
{
    FLOAT fLatitude;      //纬度
    FLOAT fLongtitude;    //经度
	WORD  wCRC;
}T_GPSPositionStruct;

typedef struct
{
    BYTE ucLatiDirect;      // 纬度方向
    BYTE ucLongDirect;      // 经度方向
    WORD wCRC;
}T_GPSDirectionStruct;

typedef struct 
{
    BYTE ucMC20Stage; //当前状态
    BOOLEAN bSIMCardOk;
    BYTE ucSignalQuality;
    BYTE ucStarNum;
    INT8S ucSNR;      //信噪比   
}T_Mg21Status;

typedef struct
{
    BOOLEAN bIMEIGet;
    char acIMEI[20];
}T_IMEI;

typedef struct
{
    BOOLEAN bIMSIGet;
    char acIMSI[20];
}T_IMSI;

typedef struct
{
    WORD wSignalFaultCnt;
    BOOLEAN bFaultInitFlag;
} T_WirelessCheck;

typedef struct 
{
    BYTE ucGetConnectStatusCnt;
    BYTE ucGetGpsStatusCnt;
    BYTE ucGetSignalPowerCnt;
    BYTE ucGetDeviceClassCnt;
    BOOLEAN bFaultGpsStatusFlag;
}T_GPSFaultStatus;
/********************* 函数原型定义 ************************/
BOOLEAN SendSms( char *pBuf );
void mg21_CommTask(void* parameter);
BOOLEAN getCurrentPlace(T_GpsData* ptGps);
FLOAT calcDistance(T_GpsData* ptGps1, T_GpsData* ptGps2);
void checkGps(void);
void TestSms(char *pcPhoneNum);
BOOLEAN ApptestGetGprsState(void);
void RecGprsUpData(void);
BOOLEAN GetMG21State(void);
void getMg21Status(T_Mg21Status *ptStatus);
BOOLEAN ParseIMEI(void);
void GetIMEI(T_IMEI *ptIMEI);
void GetIMSI(T_IMSI *ptIMSI);
void GPSSendSMS();
BYTE GetLocateModeType(void);

BOOLEAN SetLocateMode(BYTE ucLocateMode, BOOLEAN bIsInit);

BOOLEAN GetFlagRun(void);
BOOLEAN getWirelessFaultInitFlag(void);

BOOLEAN getPlaceForApptest(T_GpsData* ptGps);

BOOLEAN GetGpsModuleStatus(void);
BOOLEAN GetGpsInfo(T_GPSPositionStruct *ptGPSPos, T_GPSDirectionStruct *ptGpsDirec);
INT8S GetGpsSnr(void);
#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // _GPRS_H

