
#include "alarm_register.h"
#include "alarm_mgr_api.h"
#include "alarm_id_in.h"
#include "para_id_in.h"
#include "realdata_id_in.h"
#include "alarm_config_in.h"

// 告警清除代码表
static unsigned short alarm_clean_by_code_tab[] = {
    0xffff // 结束标志
};

// BCMU告警管理信息结构体
static alarm_manage_info_t bccu_manage = {
    ALARM_ID_OFFSET_MAX - 1, // 告警ID偏移量最大值减1
    ana_alm_config_tab,      // 模拟告警配置表
    dig_alm_config_tab,      // 数字告警配置表
    self_alm_config_tab,     // 自检告警配置表
    alm_shielded,            // 告警屏蔽状态
    alarm_clean_by_code_tab  // 告警清除代码表
};

// 注册BCM告警管理信息
void register_bccu_alarm(void) {
    register_alarm_manage_info(&bccu_manage);
}

