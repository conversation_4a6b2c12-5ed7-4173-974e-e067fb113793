/**
 * @brief ACMU板北向通信头文件
 */

#ifndef _ACMU_NORTH_H_
#define _ACMU_NORTH_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "linklayer.h"
#include "protocol_layer.h"

typedef struct {
    void*        data;
    cmd_buf_t*   cmd_buff;
    link_inst_t* link_inst;
    rt_mq_t      north_mq;
}protocol_north_mgr_t;

void* init_protocol_north_comm(void * param);
void protocol_north_comm_th(void *param);

#ifdef __cplusplus
}
#endif      //< __cplusplus

#endif      //< _ACMU_NORTH_H_
