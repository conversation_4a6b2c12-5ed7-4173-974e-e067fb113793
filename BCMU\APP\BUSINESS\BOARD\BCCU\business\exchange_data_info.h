﻿#ifndef _EXCHANGE_DATA_INFO_H_
#define _EXCHANGE_DATA_INFO_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "cmd.h"
#include "data_type.h"
#include "device_type.h"

#define FUNC_INDEX_IN_FRAME   0
#define TIME_INDEX_IN_FRAME   1
#define ID_INDEX_IN_FRAME     5
#define DATA_LEN_OF_FRAME     7
#define DIDO_COMFIRM_ADDR_IN_FRAME   1

#define FUNC_HEARTBEAT                   0x06    ///< 心跳命令
#define FUNC_CONTROLLER_STATE            0x07    ///< 主动上送柜级控制器状态
#define FUNC_CONTROLLER_ANALOG           0x08    ///< 主动上送柜级控制器模拟量
#define FUNC_CONTROLLER_ALARM_HAPPEN     0x09    ///< 主动上送柜级控制器告警产生
#define FUNC_CONTROLLER_ALARM_RECOVER    0x0A    ///< 主动上送柜级控制器告警消失
#define FUNC_BATTERY_STATE               0x0B    ///< 主动上送柜内电池状态
#define FUNC_BATTERY_ANALOG              0x0C    ///< 主动上送柜内电池模拟量
#define FUNC_BATTERY_ALARM_HAPPEN        0x0D    ///< 主动上送柜内电池告警产生
#define FUNC_BATTERY_ALARM_RECOVER       0x0E    ///< 主动上送柜内电池告警消失
#define FUNC_CONTROLLER_CONTROL          0x0F    ///< 柜级控制器遥控命令
#define FUNC_BATTERY_CONTROL             0x10    ///< 柜内电池遥控命令
#define FUNC_BCCU_DIDO_COMPETE           0x11    ///< 柜间地址竞争命令

/********* 柜级控制器遥控命令 ***********/
#define CMD_CONTACTOR_DISCONNECTION       0X01         //接触器断开
#define CMD_CONTACTOR_CONNECTION          0X02         //接触器连接
#define CMD_CTRL_ADDRESS_COMPETE          0x03         //控制地址竞争


/********* 柜内电池遥控命令 ***********/
#define CMD_BATTERY_UNLOCK        0X01          //电池解锁
#define CMD_BATTERY_LOCK          0X02          //电池闭锁


#define RTN_NORMAL            0x00    ///< 正常
#define RTN_FRM_FORMAT_ERR    0x01    ///< 帧格式错误
#define RTN_INVALID_DATA      0x02    ///< 无效数据

typedef enum
{
    FIRE_LEVEL_1_INDEX = 0,              ///< 消防一级告警
    FIRE_LEVEL_2_INDEX,                  ///< 消防二级告警
    FIRE_LEVEL_3_INDEX,                  ///< 消防三级告警
    ENV_TEMP_SENSOR_INVALID_INDEX,       ///< 环境温度传感器失效告警
    FUN_ABNORMAL_INDEX,                  ///< 散热风扇异常告警
    HUMIDITY_SENSOR_INVALID_INDEX,       ///< 湿度检测失效告警
    COMPOSITE_SENSOR_ABNORMAL_INDEX,     ///< 复合传感器异常告警
    DOOR_SWITCH_INDEX,                   ///< 门磁告警
    BREAKER_FAULT_INDEX,                 ///< 断路器故障
    CONTACTOR_FAULT_INDEX,               ///< 接触器故障
    CONTROLLER_ALARM_MAX
}controller_alarm_index_e;

typedef enum
{
    CELL_DAMAGE_INDEX = 0,                      ///< 单体损坏告警
    DC_RESISTANCE_ABNORMAL_INDEX,               ///< 电芯直流内阻异常告警
    CELL_TEMP_ABNORMAL_INDEX,                   ///< 单体温度异常告警
    CELL_TEMP_RISE_RATE_ABNORMAL_INDEX,         ///< 单体温升速率异常告警
    SELF_DISCHARGE_ABNORMAL_INDEX,              ///< 自放电异常告警
    CAP_ATTENUATION_CONSISTENCY_ALARM_INDEX,    ///< 容量衰减一致性告警
    BATTERY_ALARM_MAX
}battery_alarm_index_e;

typedef struct
{
    unsigned char alm_val;
    unsigned int time;           ///< 告警产生时间或恢复时间
}alarm_data_t;

typedef struct {
    unsigned char func_code;
    int (*recv_handle)(unsigned char* data, unsigned int data_len, char para);
    unsigned char para;
}exchange_data_handle_t;

int init_exchange_data_info(void);
int set_controller_alarm_data(int index, alarm_data_t data);
alarm_data_t get_controller_alarm_data(int index);
int set_battery_alarm_data(int index, alarm_data_t data);
alarm_data_t get_battery_alarm_data(int index);
int deal_receviced_can_short_frame(dev_inst_t* dev_inst, exchange_data_handle_t *frm_handle, unsigned int handle_len);
int pack_battery_alarm_can_frm(alarm_data_t alarm_data, unsigned short id, unsigned char* data, unsigned char data_len);
int pack_controller_alarm_can_frm(alarm_data_t alarm_data, unsigned short id, unsigned char* data, unsigned char data_len);
int can_linklayer_send(unsigned char addr,unsigned char* data, unsigned int data_len);
int can3_linklayer_send(unsigned char dev_code, unsigned char* data, unsigned int data_len);
dev_inst_t* init_can_common(void);
dev_inst_t* init_can3_common(void);

#ifdef __cplusplus
}
#endif      //< __cplusplus

#endif      //< _EXCHANGE_DATA_INFO_H_
