#include <rtthread.h>
#include <rtdef.h>
#include <rtdevice.h>
#include <math.h>
#include <string.h>
#include <stdlib.h>
#include "comm_usart.h"
#include "msg.h"
#include "cmd.h"
#include "utils_server.h"
#include "utils_thread.h"
#include "utils_math.h"
#include "protocol_layer.h"
#include "sps.h"
#include "update_manage.h"

/* 设备缓冲区长度 */
#define R_COM_BUFF_LEN                    400     ///<  接收缓冲区长度（串口链路）
#define S_COM_BUFF_LEN                    512     ///<  发送缓冲区长度（串口链路）

static dev_inst_t* dev_usart = NULL;

/* 命令请求头 */
static bottom_comm_cmd_head_t cmd_req[] = {
    // 通信命令
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_TRIG_OLD, CMD_APPEND_UPDATE_REQ, BOTTOM_RTN_APP_ACK},           // 0
};

/* 命令应答头 */
static bottom_comm_cmd_head_t cmd_ack[] = {
    // 通信命令
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_TRIG_OLD, CMD_APPEND_UPDATE_ACK, BOTTOM_RTN_APP_CORRECT},       // 0
};

/* 通信协议命令表 */
static cmd_t no_poll_cmd_tab[] = {
    {BCCU_UPDATE_TRIG,  CMD_PASSIVE,  &cmd_req[0], &cmd_ack[0], sizeof(bottom_comm_cmd_head_t), },
    {0},
};

static dev_type_t dev_bccu_usart = {
    DEV_BCCU_NORTH_USART, 1, PROTOCOL_BOTTOM_COMM, LINK_BCCU_SCU_USART, R_COM_BUFF_LEN, S_COM_BUFF_LEN, 0, no_poll_cmd_tab, NULL
};

Static msg_map sample_msg_map[] =
{
     {0,NULL},
};

static dev_type_t* intit_bccu_usart(void) {
    return &dev_bccu_usart;
}

static dev_init_t dev_init_tab[] = {
    {DEV_BCCU_NORTH_USART, intit_bccu_usart, NULL, NULL},
};

Static north_mgr_t* init_thread_data(link_inst_t* link_inst, unsigned char mod_id) {
    north_mgr_t* north_mgr = NULL;

    north_mgr = malloc(sizeof(north_mgr_t));
    if (north_mgr == NULL)
        return NULL;

    north_mgr->cmd_buff = malloc(sizeof(cmd_buf_t));
    if (north_mgr->cmd_buff == NULL) {
        goto NORTH_MGR_FREE;
    }

    north_mgr->cmd_buff->addr_dev_data = 0x01;
    north_mgr->link_inst = link_inst;
    return north_mgr;

NORTH_MGR_FREE:
    free(north_mgr);
    return NULL;
}

void* usart_comm_init(void *param)
{
    server_info_t *server_info = (server_info_t *)param;
    north_mgr_t* north_mgr = NULL;
    init_dev_tab(dev_init_tab, sizeof(dev_init_tab)/sizeof(dev_init_tab[0]));
    dev_usart = init_dev_inst(DEV_BCCU_NORTH_USART);
    if (dev_usart == NULL)
    {
        return NULL;
    }
    server_info->server.server.map_size = sizeof(sample_msg_map) / sizeof(msg_map);
    register_server_msg_map(sample_msg_map, server_info);
    north_mgr = init_thread_data(dev_usart->dev_type->link_inst, MOD_BCCU_USART);

    update_manage_init();

    return north_mgr;
}

void process_usart_comm(void* param)
{
    north_mgr_t* north_mgr = (north_mgr_t*)param;
    while (is_running(TRUE))
    {
        if(RT_EOK == rt_sem_take(&(north_mgr->link_inst->rx_sem), SAMPLE_USART_DELAY)) {
            // 接收命令数据
            if (SUCCESSFUL == cmd_recv(dev_usart, north_mgr->cmd_buff)) {
                cmd_send(dev_usart, north_mgr->cmd_buff);
                if (get_update_trigger() >= TRIGGER_COUNTER)
                {
                    BeginDownload(FLAG_IAP);
                }
            }
            continue;
        }

        check_update_timeout();
    }
}
