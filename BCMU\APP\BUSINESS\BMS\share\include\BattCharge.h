#ifndef BATTCHARGE_H_
#define BATTCHARGE_H_
#include "battery.h"

FLOAT GetMixOutputVolt(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
void BattCharge(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
void ClearRunModeControledFlag(void);
BOOLEAN BattSleepCharge(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
#ifdef UNITEST
void ParseBusVolResult(FLOAT *pafBusVol, BYTE ucBusVolIndex);
void GetBusVolResult(FLOAT *pafBusVol, BYTE ucBusVolIndex);
FLOAT UpdateMixVolt(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
#endif

#endif
