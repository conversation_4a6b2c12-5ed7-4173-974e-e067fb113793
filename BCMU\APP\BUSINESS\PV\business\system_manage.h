#ifndef _SYSTEM_MANAGE_H
#define _SYSTEM_MANAGE_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

#include "his_record.h"

#define FLASH_FAIL_MAX_NUMS 100 //flash操作最大错误次数

typedef struct {
    rt_uint32_t msg_id;
    void (*handle)(_rt_msg_t curr_msg);
} pv_msg_handle_process_t;

typedef struct boot_info
{
    char ver[16];
    char data[12];
    char is_first_start;
} boot_info_t;

void *init_sys_manage(void * param);
void system_manage_main(void *param);
void init_soft_info();
void system_reset(unsigned char reason);
void sys_run_result();
int register_common_fun(void);
void handle_format_file_sys(_rt_msg_t curr_msg);
void handle_save_his_data_msg(_rt_msg_t curr_msg);
void handle_rs485_stat(_rt_msg_t curr_msg);
void handle_chk_all_alm_chg_msg(_rt_msg_t curr_msg);
void handle_save_his_alm_msg(_rt_msg_t curr_msg);
void send_his_data_save_msg();
void run_time_timer_timeout();
void alarm_his_data_timer_timeout(void *parameter);
void init_hisdata_timer();
void init_run_time_timer();
void init_alarm_his_data_process_timer(void);
void check_save_hisdata_time();
void check_master_alarm();
int set_heart_beat_time_by_num(unsigned short num);
void handle_enter_apptest(_rt_msg_t curr_msg);
void handle_start_delay_update_msg(_rt_msg_t curr_msg);
void handle_grid_code_check_msg(_rt_msg_t curr_msg);
void handle_para_file_export(_rt_msg_t curr_msg);
void handle_delay_update(_rt_msg_t curr_msg);
void handle_del_his_data_msg(_rt_msg_t curr_msg);
void handle_save_fac_data_msg(_rt_msg_t curr_msg);
void handle_clean_his_alm_msg(_rt_msg_t curr_msg);
void handle_operate_record_test_msg(_rt_msg_t curr_msg);
void handle_his_alm_test_msg(_rt_msg_t curr_msg);
void handle_save_iv_data_msg(_rt_msg_t curr_msg);
void handle_energy_data_test_msg(_rt_msg_t curr_msg);
void handle_check_dc_ac_para_msg(_rt_msg_t curr_msg);
void handle_autho_time_check_msg(_rt_msg_t curr_msg);
void handle_save_record_fault_msg(_rt_msg_t curr_msg);
void handle_crt_one_change_msg(_rt_msg_t curr_msg);
void handle_south_upload_msg(_rt_msg_t curr_msg);
void handle_clean_afci_msg(_rt_msg_t curr_msg);
void handle_afci_file_export(_rt_msg_t curr_msg);
void handle_clean_his_data(_rt_msg_t curr_msg);
void handle_clean_his_event(_rt_msg_t curr_msg);
void handle_clean_iv_data(_rt_msg_t curr_msg);
void handle_clean_fault_record(_rt_msg_t curr_msg);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _SYSTEM_MANAGE_H