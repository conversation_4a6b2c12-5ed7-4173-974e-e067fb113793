/* Started by AICoder, pid:799fetdc89f4e5b147020b3b81c0ee2591f93206 */
#ifndef _DCMU_MIBTABLE_H_
#define _DCMU_MIBTABLE_H_
#ifdef __cplusplus
extern "C" {
#endif

#define INPUT_RELAY_INDEX_ALM_MAP 25
#define LEN_RELAYNAME       13
#define LEN_PASER           4  // 菜单口令长度

// MIB库节点结构体定义
/************************************************************
** 结构名: MIBTable_ParaNode
** 描  述: 参数属性数据结构定义(表征一个参数量的属性)
** 作  者:
** 日  期: 2025-06-28
** 版  本:
**************************************************************/
typedef struct {
    unsigned short wParaOffsetId;    // 参数偏移ID，用于set_one_para
    unsigned char ucID1;       // ID1
    unsigned char ucID2;       // ID2
    unsigned char ucIDNumber;  // 用于标志ID1和ID2相同的数量(如有三组电池时，电池容量ID就有3个)(当ID2=0x00时表示ID2数量)
    unsigned char ucDataType;  // 参量数据类型
    unsigned char ucDataLen;   // 参量字节长度
    unsigned char ucParaType;  // 参数类型
    unsigned short wParaId;    // 参数ID，不带offset，set_one_para保存事件记录的参数ID
    char scStep;               // 参量步长值
    char *ptrUnit;             // 参数单位
    char scPrecision;          // 参量数据精度，小数点后几位数,如1表示小数点后1位。
    int iDefaultVal;           // 参量缺省值
    int iMinVal;               // 参量最小值
    int iMaxVal;               // 参量最大值
    unsigned char bSCAccess;   // 用于标志此参数后台能否访问,TRUE:后台可以访问,FALSE:后台不能访问
} MIBTable_ParaNode;

/************************************************************
** 结构名: MIB_AnalogDataNode
** 描  述: 模拟量属性数据结构定义(表征一个模拟量的属性)
** 作  者: 潘奇银
** 日  期: 2008-01-11
** 版  本: V5.0
**************************************************************/
typedef struct {
    unsigned char ucID1;       // ID1
    unsigned char ucID2;       // ID2
    unsigned char ucIDNumber;  // ID1和ID2相同的数量(例如负载有24路，那么这个数就是24)
    unsigned char ucDataType;  // 参量数据类型
    unsigned char ucDataLen;   // 参量字节长度
    unsigned char scPrecision; // 参量数据精度，小数点后几位数,如1表示小数点后1位。
} MIB_AnalogDataNode;

/************************************************************
** 结构名: MIB_DigitalDataNode
** 描  述: 数字量属性数据结构定义(表征一个数字量的属性)
** 作  者: 潘奇银
** 日  期: 2008-01-11
** 版  本: V5.0
**************************************************************/
typedef struct {
    unsigned char ucID1;       // ID1
    unsigned char ucID2;       // ID2
    unsigned char ucIDNumber;  // ID1和ID2相同的数量(例如有24路负载，该数字就是24),另当ID2=0x00时，表示ID2总数
} MIB_DigitalDataNode;

/************************************************************
** 结构名: MIB_CtrlDataNode
** 描  述: 控制量属性数据结构定义(表征一个数字量的属性)
** 作  者: 刘东波
** 日  期: 2008-10-11
** 版  本: V5.0
**************************************************************/
typedef struct {
    unsigned char ucID1;       // ID1
    unsigned char ucID2;       // ID2
    unsigned char ucIDNumber;  // ID1和ID2相同的数量(例如有24路负载，该数字就是24),另当ID2=0x00时，表示ID2总数
} MIB_CtrlDataNode;

/************************************************************
** 结构名: MIB_AlarmDataNode
** 描  述: 告警量属性数据结构定义(表征一个告警量的属性)
** 作  者: 潘奇银
** 日  期: 2008-01-11
** 版  本: V5.0
**************************************************************/
typedef struct{
    unsigned short alarm_id;     
    unsigned char id1;
    unsigned char id2;
    unsigned char number;
}alarm_map_t;

typedef struct{
	unsigned char	ucID1;			// ID1
	unsigned char	ucID2;			// ID2
	unsigned char	ucIDNumber;	// ID1和ID2相同的数量(如有三个整流器时，整流器在位信息ID就有3个),另当ID2=0x00时，表示ID2总数
}MIB_AlarmDataNode;

/************************************************************
** 结构名: T_HisOperIDStruct
** 描  述: 历史操作记录ID数据结构
** 作  者: 潘奇银
** 日  期: 2008-01-15
** 版  本: V5.0
**************************************************************/
typedef struct {
    unsigned char ucID1;       // ID1
    unsigned char ucID2;       // ID2
    unsigned char ucIDNumber;  // ID1和ID2相同的数量(如有三个整流器时，整流器在位信息ID就有3个),另当ID2=0x00时，表示ID2总数
} MIB_HisOperIDNode;

extern MIBTable_ParaNode Para_MIBTable[];
// extern const MIB_AnalogDataNode AnalogData_MIBTable[];
// extern const MIB_DigitalDataNode StatusData_MIBTable[];
// extern const MIB_AlarmDataNode Alarm_MIBTable[];
// extern const MIB_HisOperIDNode HisOper_MIBTable[];
// extern const MIB_CtrlDataNode CtrlData_MIBTable[];
// extern const MIB_HisOperIDNode CTRL_MIBTable[];
// extern const MIB_AlarmDataNode * GetAlarmNode( INT8U ucAlarmSn );

const MIBTable_ParaNode *GetParaNode(unsigned char ucID1, unsigned char ucID2);
const MIBTable_ParaNode* GetInrelayAlmParaNode(unsigned char ucID1, unsigned char ucID2, unsigned char idx);
const MIBTable_ParaNode *GetParaNodeByParaId(unsigned short wParaOffsetId);
const MIBTable_ParaNode *GetParaNodeByType(unsigned short wParaId, unsigned char ucType);
unsigned short GetParaOffset(unsigned char ucID1, unsigned char ucID2, unsigned char ucIDIndex);
unsigned short GetInRelayAlmParaOffset(unsigned char ucID1, unsigned char ucID2, unsigned char ucIDIndex, unsigned char ucinrelayIdx);
int get_exclude_para_ids_by_type(unsigned char para_type, unsigned short* out_id_list, int max_num);
unsigned short GetParaZkSn(unsigned char ucID1, unsigned char ucID2);
unsigned char GetAlmClassZk(unsigned char ucID1, unsigned char ucID2, unsigned char ucType);
unsigned char init_mib_table_para_from_dic(void);
int get_alarm_sn(unsigned short alarm_id, alarm_map_t** out_map);
const MIB_AlarmDataNode * GetAlarmNode(unsigned char ucAlarmSn);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _DCMU_MIBTABLE_H_
/* Ended by AICoder, pid:799fetdc89f4e5b147020b3b81c0ee2591f93206 */