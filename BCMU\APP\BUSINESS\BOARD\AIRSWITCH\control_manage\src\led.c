#include "led.h"
#include "msg.h"
// #include <board.h>
#include <stdlib.h>
#include <string.h>
#include "msg_id.h"
#include <rtthread.h>
#include <rtdevice.h>
#include "data_type.h"
#include "pin_define.h"
#include "signal_led.h"
#include "utils_thread.h"
#include "utils_rtthread_security_func.h"
#include "realdata_id_in.h"
#include "para_id_in.h"
#include "softbus.h"
#include "flash.h"
#include "utils_server.h"
#include "device_common.h"
#include "realdata_save.h"
#include "para_manage.h"

Static T_FileManageStruct   s_tFileManage;

/* 定义信号灯对象句柄 */
Static led_t* s_green = NULL;          // 绿灯
Static led_t* s_red = NULL;            // 红灯
Static led_t* s_blue = NULL;           // 蓝灯

/* 设置信号灯一个周期内的闪烁模式 
 * 格式为 “亮、灭、亮、灭、亮、灭 …………”
 */
static char* led_on_mode = "1000,0,";       ///< 常亮
static char* led_off_mode = "0,1000,";      ///< 常灭
static char* led_blink1Hz_mode = "500,500,";  ///< 1Hz闪烁
static char* led_blink4Hz_mode = "125,125,";  ///< 4Hz闪烁
static char led_map[6] = {7, 1, 3, 5, 6, 0};  ///< 按红绿蓝顺序，低电平有效，7对应111，全灭

static msg_map s_led_msg_map[] = {
    {0, NULL},
};

Static airswitch_led_Onekey_status_t airswitch_led_Onekey_status_table[] = {
    {ONEKEY_MODE, LED_UNAUTHORIZED, LED_OPENING, YELLOW, FAST_4HZ_FLASH},
    {ONEKEY_MODE, LED_UNAUTHORIZED, LED_CLOSING, YELLOW, FAST_4HZ_FLASH},
    {ONEKEY_MODE, LED_AUTHORIZED, LED_OPENING, GREEN, FAST_4HZ_FLASH},
    {ONEKEY_MODE, LED_AUTHORIZED, LED_CLOSING, RED, FAST_4HZ_FLASH},
};

Static airswitch_led_status_t airswitch_led_status_table[] = {
    {NORMAL_MODE, LED_NORMAL, LED_UNAUTHORIZED, LED_OPENING, YELLOW, CONSTANTLY_ON},
    {NORMAL_MODE, LED_NORMAL, LED_UNAUTHORIZED, LED_CLOSING, YELLOW, CONSTANTLY_ON},
    {NORMAL_MODE, LED_NORMAL, LED_AUTHORIZED, LED_CLOSING, RED, CONSTANTLY_ON},
    {NORMAL_MODE, LED_NORMAL, LED_AUTHORIZED, LED_OPENING, GREEN, CONSTANTLY_ON},
    {NORMAL_MODE, LED_FAULT, LED_UNAUTHORIZED, LED_OPENING, YELLOW, SLOW_1HZ_FLASH},
    {NORMAL_MODE, LED_FAULT, LED_UNAUTHORIZED, LED_CLOSING, YELLOW, SLOW_1HZ_FLASH},
    {NORMAL_MODE, LED_FAULT, LED_AUTHORIZED, LED_CLOSING, RED, SLOW_1HZ_FLASH},
    {NORMAL_MODE, LED_FAULT, LED_AUTHORIZED, LED_OPENING, GREEN, SLOW_1HZ_FLASH},
};

Static airswitch_led_special_status_t airswitch_led_special_status_table[] = {
    {BUSINT_MODE, BLUE, SLOW_1HZ_FLASH},
};

Static led_t* get_led_handle(airswitch_light_e light);
Static char* get_blink_mode(airswitch_blinking_e blinking);
int set_light_and_blinking_special(airswitch_special_e special);
int set_light_and_blinking_Onekey(airswitch_Empower_e empower, airswitch_Brake_e brake);
int set_light_and_blinking_normal(airswitch_Alarm_e alarm, airswitch_Empower_e empower, airswitch_Brake_e brake);
Static int led_ctrl(void);
Static int judge_led_status(airswitch_led_status_t* data);



/* 定义开灯函数 */
Static void led_green_switch_on(void *param) {
    rt_pin_write(PIN_LED_GREEN_CONTROL, PIN_LOW);
}

Static void led_blue_switch_on(void *param) {
    rt_pin_write(PIN_LED_BLUE_CONTROL, PIN_LOW);
}

Static void led_red_switch_on(void *param) {
    rt_pin_write(PIN_LED_RED_CONTROL, PIN_LOW);
}

/* 定义关灯函数 */
Static void led_green_switch_off(void *param) {
    rt_pin_write(PIN_LED_GREEN_CONTROL, PIN_HIGH);
}

Static void led_blue_switch_off(void *param) {
    rt_pin_write(PIN_LED_BLUE_CONTROL, PIN_HIGH);
}

Static void led_red_switch_off(void *param) {
    rt_pin_write(PIN_LED_RED_CONTROL, PIN_HIGH);
}

/* 初始化闪烁led */

void* init_led_blink(void* param) {
    server_info_t *server_info = NULL;
    
    rt_pin_mode(PIN_LED_GREEN_CONTROL, PIN_MODE_OUTPUT);
    s_green = led_create(led_green_switch_on, led_green_switch_off, NULL);
    RETURN_VAL_IF_FAIL(s_green != NULL, NULL);
    led_set_mode(s_green, LOOP_PERMANENT, led_on_mode);
    led_start(s_green);

    rt_pin_mode(PIN_LED_BLUE_CONTROL, PIN_MODE_OUTPUT);
    s_blue = led_create(led_blue_switch_on, led_blue_switch_off, NULL);
    RETURN_VAL_IF_FAIL(s_blue != NULL, NULL);
    led_set_mode(s_blue, LOOP_PERMANENT, led_on_mode);
    led_start(s_blue);

    rt_pin_mode(PIN_LED_RED_CONTROL, PIN_MODE_OUTPUT);
    s_red = led_create(led_red_switch_on, led_red_switch_off, NULL);
    RETURN_VAL_IF_FAIL(s_red != NULL, NULL);
    led_set_mode(s_red, LOOP_PERMANENT, led_on_mode);
    led_start(s_red);

    fmc_read_data(UPDATEINFO_STATRT, (uint8_t *)&s_tFileManage,sizeof(T_FileManageStruct));

    server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(s_led_msg_map) / sizeof(msg_map);
    register_server_msg_map(s_led_msg_map, server_info);
    return NULL;
}

static short process_ctrl_led(airswitch_light_e light,airswitch_blinking_e blinking)
{
    led_t* led_handle = NULL;
    char* blink_mode = NULL;
    blink_mode = get_blink_mode(blinking);

    if(light == WHITE)      //实际并无白灯，需同时开启绿灯红灯和蓝灯来实现
    {
        led_set_mode(s_green, LOOP_PERMANENT, blink_mode);
        led_start(s_green);
        led_set_mode(s_red, LOOP_PERMANENT, blink_mode);
        led_start(s_red);
        led_set_mode(s_blue, LOOP_PERMANENT, blink_mode);
        led_start(s_blue);
    }
    else if(light == YELLOW)      //实际并无黄灯，需同时开启绿灯红灯来实现
    {
        led_set_mode(s_green, LOOP_PERMANENT, blink_mode);
        led_start(s_green);
        led_set_mode(s_red, LOOP_PERMANENT, blink_mode);
        led_start(s_red);
    }
    else
    {
        
        led_handle = get_led_handle(light);

        led_set_mode(led_handle, LOOP_PERMANENT, blink_mode);
        led_start(led_handle);
    }

     return SUCCESSFUL;
}

/*
 *@brief 根据brake、empower获取对应的light、blinking和time
 *@param[in] empower 授权状态
 *@param[in] brake 电闸状态
 *@retval SUCCESSFUL 获取成功
 *@retval FAILURE 获取失败
 */
int set_light_and_blinking_Onekey(airswitch_Empower_e empower, airswitch_Brake_e brake) {
    airswitch_light_e light = NULL_LED;
    airswitch_blinking_e blinking = CONSTANTLY_ON;

    for (int i = 0; i < sizeof(airswitch_led_Onekey_status_table) / sizeof(airswitch_led_Onekey_status_t); i++) {
        if (airswitch_led_Onekey_status_table[i].empower == empower && airswitch_led_Onekey_status_table[i].brake == brake) {
            light = airswitch_led_Onekey_status_table[i].light;
            blinking = airswitch_led_Onekey_status_table[i].blinking;

           process_ctrl_led(light,blinking);
        }
    }
    return SUCCESSFUL;
}

/*
 *@brief 根据special获取对应的light、blinking和time
 *@param[in] special 功能状态
 *@retval SUCCESSFUL 获取成功
 *@retval FAILURE 获取失败
 */
int set_light_and_blinking_special(airswitch_special_e special) {
    airswitch_light_e light = NULL_LED;
    airswitch_blinking_e blinking = CONSTANTLY_ON;

    for (int i = 0; i < sizeof(airswitch_led_special_status_table) / sizeof(airswitch_led_special_status_t); i++) {
        if (airswitch_led_special_status_table[i].special == special) {
            light = airswitch_led_special_status_table[i].light;
            blinking = airswitch_led_special_status_table[i].blinking;

            process_ctrl_led(light,blinking);
        }
    }
    return SUCCESSFUL;
}

/*
 *@brief 根据alarm、empower和brake获取对应的light、blinking和time
 *@param[in] alarm 告警状态
 *@param[in] empower 授权状态
 *@param[in] brake 电闸状态
 *@retval SUCCESSFUL 获取成功
 *@retval FAILURE 获取失败
 */
int set_light_and_blinking_normal(airswitch_Alarm_e alarm, airswitch_Empower_e empower, airswitch_Brake_e brake) {
    airswitch_light_e light = NULL_LED;
    airswitch_blinking_e blinking = CONSTANTLY_ON;

    for (int i = 0; i < sizeof(airswitch_led_status_table) / sizeof(airswitch_led_status_t); i++) {
        if (airswitch_led_status_table[i].alarm == alarm && airswitch_led_status_table[i].empower == empower && airswitch_led_status_table[i].brake == brake) {
            light = airswitch_led_status_table[i].light;
            blinking = airswitch_led_status_table[i].blinking;
            
            process_ctrl_led(light,blinking);
        }
    }
    return SUCCESSFUL;
}

Static led_t* get_led_handle(airswitch_light_e light) {
    switch (light) {
        case GREEN:
            return s_green;
            break;
        case BLUE:
            return s_blue;
            break;
        case RED:
            return s_red;
            break;
        default:
            break;
    }
    return NULL;
}

Static char* get_blink_mode(airswitch_blinking_e blinking) {
    switch (blinking) {
        case CONSTANTLY_ON:
            return led_on_mode;
            break;
        case FAST_4HZ_FLASH:
            return led_blink4Hz_mode;
            break;
        case SLOW_1HZ_FLASH:
            return led_blink1Hz_mode;
            break;
        default:
            break;
    }
    return NULL;
}

Static int judge_led_status(airswitch_led_status_t* data) {
    unsigned char CommFailstatus = 0;
    unsigned char p2p_status = 0,empower_statu = 0,loop_statu = 0,alm_status = 0;
    get_one_data(SWITCH_DATA_ID_COMMUNICATION_FAIL_STATUS,&CommFailstatus);
    get_one_data(SWITCH_DATA_ID_P2P_STATUS,&p2p_status);
    get_one_data(SWITCH_DATA_ID_LOOP_STATUS,&loop_statu);
    get_one_para(SWITCH_PARA_ID_POWER_ON_AUTHORIZATION_OFFSET,&empower_statu);

    for (unsigned int i = SWITCH_DATA_ID_OVER_CURRENT_ALARM; i <= SWITCH_DATA_ID_OVERLOAD_TRIP_ALARM; i++)
    {
        get_one_data(i, &alm_status);

        if (TRUE == alm_status)
        {
            break;
        }
    }

    if(p2p_status == P2P_STATUS_ON)
    {
        data->special = ONEKEY_MODE;
    }
    else if(CommFailstatus == 1)
    {
        data->special = BUSINT_MODE;
    }
    else{
        data->special = NORMAL_MODE;
    }
    data->empower = empower_statu;
    data->brake = loop_statu;
    data->alarm = alm_status;
    
    return TRUE;
}

/* led指示灯控制 */
Static int led_ctrl(void) {
    airswitch_led_status_t data = {0};
    static char old_data[T_LED_STATUS_LEN] = {-1};
    judge_led_status(&data);
    if(0 == rt_memcmp(old_data ,&data ,T_LED_STATUS_LEN))
    {
        return TRUE;
    }
    rt_memcpy_s(old_data,T_LED_STATUS_LEN,&data,T_LED_STATUS_LEN);

    led_set_mode(s_green, LOOP_PERMANENT, led_off_mode);
    led_set_mode(s_blue, LOOP_PERMANENT, led_off_mode);
    led_set_mode(s_red, LOOP_PERMANENT, led_off_mode);
   
    if(s_tFileManage.ucFlag != FLAG_OK)
    {
        return process_ctrl_led(WHITE,CONSTANTLY_ON);
    }
    else if(data.special == ONEKEY_MODE)
    {
        return set_light_and_blinking_Onekey(data.empower,data.brake);
    }
    else if(data.special == NORMAL_MODE)
    {
        return set_light_and_blinking_normal(data.alarm,data.empower,data.brake);
    }
    else if(data.special <= OTAUPGRADE_MODE)
    {
        return set_light_and_blinking_special(data.special);
    }
    return FAILURE;
}

// 0-灭；1-黄；2-红；3-绿；4-蓝；5-白
char set_led_color(unsigned char led_status)
{
    char value = 0;
    if(led_status>WHITE || led_status<NULL_LED)
        return FAILURE;
    value = led_map[led_status];
    rt_pin_write(PIN_LED_RED_CONTROL, (value>>2)&0x01);
    rt_pin_write(PIN_LED_GREEN_CONTROL, (value>>1)&0x01);
    rt_pin_write(PIN_LED_BLUE_CONTROL, value&0x01);
    set_one_data(SWITCH_DATA_ID_LED_STATUS, &led_status);

    return SUCCESSFUL;
}

void led_thread_entry(void* parameter) {
    unsigned char apptest_flag = 0;

    while (is_running(TRUE))
    {
        rt_thread_mdelay(LED_TICK_TIME);

        get_one_data(SWITCH_DATA_ID_APPTEST_FLAG, &apptest_flag);

        if(1 == apptest_flag)
        {
            continue;
        }
        

        led_ctrl();
        led_ticks();
       
    }
}
