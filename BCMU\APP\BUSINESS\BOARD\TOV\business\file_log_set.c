#include <stdint.h>
#include <backend/ulog_be.h>
#include "file_log_set.h"
#include "common.h"
#include <backend/file_log_refactor.h>
#include "file_log_set.h"


#ifdef ULOG_BACKEND_USING_FILE

/*
* 后端注册表
*/
#define MOTION_TAG  "MOVE"
#define ERROR_TAG  3
#define LOG_PATH "/" //设置保存路径
#define FILE_SIZE (1024 * 512)   //设置单个文件大小
#define BUFF_SIZE 4096           //设备缓存区大小
#define FILE_MAX_NUM 2


static struct ulog_backend sys_log_backend;
static struct ulog_file_be sys_log_file;

struct _log_file log_output_file_table[SAVE_FILE_CLASS_NUM] =  // 对声明的extern变量的赋初值
{
    {"sys"      ,&sys_log_backend,&sys_log_file,LOG_PATH,FILE_MAX_NUM,FILE_SIZE,BUFF_SIZE,True,sys_log_file_backend_filter},
};


//自定义sys规则的过滤函数
rt_bool_t sys_log_file_backend_filter(struct ulog_backend *backend, rt_uint32_t level, const char *tag, rt_bool_t is_raw, 
                                    const char *log, rt_size_t len)
{
    // 只输出级别为错误的
    if (level == ERROR_TAG)
    {
        return RT_TRUE;
    }
    else
    {
        return RT_FALSE;
    }    
}

unsigned int get_write_log_cnt()
{
    return sys_log_file.log_num;
}
#endif

