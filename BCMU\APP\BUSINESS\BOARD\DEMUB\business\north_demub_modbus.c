/*
 * @file    : north_dxcb_modbus.c
 * @brief   : DXCB北向modbus协议实现
 * @details :
 * <AUTHOR> 邵敏10132013
 * @Date    : 2023-08-30
 * @LastEditTime: 2023-08-30
 * @version : V0.0.1
 * @para    : Copyright (c)
 *            ZTE Corporation
 * @par     : History
 *
 *     version: author, date, descn
 */

#include "dev_north_demub_modbus.h"
#include "protocol_layer.h"
#include <string.h>
#include "sample.h"
#include "utils_data_transmission.h"
#include "protocol_north_modbus.h"
#include "utils_rtthread_security_func.h"
#include "realdata_save.h"
#include "realdata_id_in.h"
#include "para_id_in.h"
#include "msg_id.h"
#include "storage.h"
#include "ee_public_info.h"
#include "bspconfig.h"
#include "para_manage.h"

/* 设备缓冲区长度 */
#define R_NORTH_MODBUS_BUFF_LEN 1024 ///<  接收缓冲区长度
#define S_NORTH_MODBUS_BUFF_LEN 1024 ///<  发送缓冲区长度

#define DEV_CODE_NO_USE 1

unsigned short g_reg_addr = 0;
unsigned short g_reg_nums = 0;
unsigned char g_func_code = 0;
signed short g_set_val = 0;


#define MODBUS_DATA_MAP_LEN sizeof(g_modbus_data_map) / sizeof(modbus_addr_map_data_t)
#define PARSE_FUN_MAP_LEN   sizeof(parse_fun_map)/sizeof(parse_fun_map_t)



pack_fun_map_t pack_fun_map[] = {
    {TYPE_FLOAT,  TYPE_INT32U, floattoint32u },
    {TYPE_FLOAT,  TYPE_INT32S, floattoint32s},
    {TYPE_FLOAT,  TYPE_INT16U, floattoint16u},
    {TYPE_FLOAT,  TYPE_INT16S, floattoint16s},
    {TYPE_INT16U, TYPE_INT16U, int16utoint16u},
    {TYPE_INT16S, TYPE_INT16S, int16stoint16s},
    {TYPE_INT32U, TYPE_INT32U, int32utoint32u}, 
    {TYPE_STRING, TYPE_STRING, stringtostring},
    {TYPE_DATE_T, TYPE_INT16U, datetoint16u},
    {TYPE_INT32S, TYPE_INT32S, int32stoint32s},
    {TYPE_MAX,    TYPE_MAX,    NULL},
};




parse_fun_map_t parse_fun_map[] = {
    {TYPE_INT16U, TYPE_FLOAT,  parse_int16utofloat},
    {TYPE_INT16S, TYPE_FLOAT,  parse_int16stofloat },
    {TYPE_INT32U, TYPE_FLOAT,  parse_int32utofloat },
    {TYPE_INT32S, TYPE_FLOAT,  parse_int32stofloat},
    {TYPE_INT16U, TYPE_INT16U, parse_int16utoint16u},
    {TYPE_INT32U, TYPE_INT32U, parse_int32utoint32u},
    {TYPE_STRING, TYPE_STRING, parse_stringtostring },
    {TYPE_INT32S, TYPE_INT32S, parse_int32stoint32s},
    {TYPE_MAX,    TYPE_MAX,    NULL},
};


/* 命令请求头 */
static modbusrtu_cmd_head_t cmd_req[] = {
    {GET_ANA_DATA},
    {GET_PARA_DATA},
    {SET_SINGLE_PARA_DATA},
    {SET_PARA_DATA},
};

/* 命令应答头 */
static modbusrtu_cmd_head_t cmd_ack[] = {
    {GET_ANA_DATA},
    {GET_PARA_DATA},
    {SET_SINGLE_PARA_DATA},
    {SET_PARA_DATA},
};

/* 命令总表,必须以空字符串""结束 */
static cmd_t no_poll_cmd_tab[] = {
    {GET_ANA_DATA, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, new_pack_ana_para_data, parse_start_addr_and_reg_nums},
    {GET_PARA_DATA, CMD_PASSIVE, &cmd_req[1], &cmd_ack[1], sizeof(modbusrtu_cmd_head_t), NULL, NULL, new_pack_ana_para_data, parse_start_addr_and_reg_nums},
    {SET_SINGLE_PARA_DATA, CMD_PASSIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_set_para_data, new_parse_para_data_from_buff},//后续处理
    {SET_PARA_DATA, CMD_PASSIVE, &cmd_req[3], &cmd_ack[3], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_set_para_data, new_parse_para_data_from_buff},
    {0},
};


static dev_type_t dev_val_north_demub = {
    DEV_NORTH_DEMUB,
    1,
    PROTOCOL_MODBUS_RTU,
    LINK_NORTH_DEMUB,
    R_NORTH_MODBUS_BUFF_LEN,
    S_NORTH_MODBUS_BUFF_LEN,
    DEV_CODE_NO_USE,
    no_poll_cmd_tab,
    NULL,
    0
};


modbus_addr_map_data_t g_modbus_data_map[] = {
    {1,     0, TYPE_INT16U,    TYPE_INT16U, 0, 2,   TYPE_ANA,   DEMUB_DATA_ID_PARA_1_CRC},                                   //参数组1CRC
    {2,     0, TYPE_INT16U,    TYPE_INT16U, 0, 2,   TYPE_ANA,   DEMUB_DATA_ID_PARA_2_CRC},                                   //参数组2CRC
    {3,     0, TYPE_INT32U,    TYPE_INT32U, 0, 4,   TYPE_ANA,   DEMUB_DATA_ID_DI_24_STATUS},                                 //DI1-DI24
    {5,     0, TYPE_FLOAT,     TYPE_INT16S, 1, 2,   TYPE_ANA,   DEMUB_DATA_ID_FIRST_INPUT_PHASE_VOL},                        //输入A电压_U1
    {6,     0, TYPE_FLOAT,     TYPE_INT16S, 1, 2,   TYPE_ANA,   DEMUB_DATA_ID_FIRST_INPUT_PHASE_VOL + 1},                    //输入A电压_U2
    {7,     0, TYPE_FLOAT,     TYPE_INT16S, 1, 2,   TYPE_ANA,   DEMUB_DATA_ID_FIRST_INPUT_PHASE_VOL + 2},                    //输入A电压_U3
    {8,     0, TYPE_FLOAT,     TYPE_INT32U, 2, 4,   TYPE_ANA,   DEMUB_DATA_ID_FIRST_INPUT_PHASE_CUR},                        //输入A电流_I1
    {10,    0, TYPE_FLOAT,     TYPE_INT32U, 2, 4,   TYPE_ANA,   DEMUB_DATA_ID_FIRST_INPUT_PHASE_CUR + 1},                    //输入A电流_I2
    {12,    0, TYPE_FLOAT,     TYPE_INT32U, 2, 4,   TYPE_ANA,   DEMUB_DATA_ID_FIRST_INPUT_PHASE_CUR + 2},                    //输入A电流_I3
    {14,    0, TYPE_FLOAT,     TYPE_INT16S, 1, 2,   TYPE_ANA,   DEMUB_DATA_ID_FIRST_INPUT_LINE_VOL},                         //输入A线电压_U1
    {15,    0, TYPE_FLOAT,     TYPE_INT16S, 1, 2,   TYPE_ANA,   DEMUB_DATA_ID_FIRST_INPUT_LINE_VOL + 1},                     //输入A线电压_U2
    {16,    0, TYPE_FLOAT,     TYPE_INT16S, 1, 2,   TYPE_ANA,   DEMUB_DATA_ID_FIRST_INPUT_LINE_VOL + 2},                     //输入A线电压_U3
    {17,    0, TYPE_INT16U,    TYPE_INT16U, 0, 2,   TYPE_ANA,   DEMUB_DATA_ID_FIRST_INPUT_PHASE_VOL_ANGLE_DIFF},             //输入A的L1相电压角度差
    {18,    0, TYPE_INT16U,    TYPE_INT16U, 0, 2,   TYPE_ANA,   DEMUB_DATA_ID_FIRST_INPUT_PHASE_VOL_ANGLE_DIFF + 1},         //输入A的L2相电压角度差
    {19,    0, TYPE_INT16U,    TYPE_INT16U, 0, 2,   TYPE_ANA,   DEMUB_DATA_ID_FIRST_INPUT_PHASE_VOL_ANGLE_DIFF + 2},         //输入A的L3相电压角度差
    {20,    0, TYPE_INT16U,    TYPE_INT16U, 0, 2,   TYPE_ANA,   DEMUB_DATA_ID_FIRST_INPUT_PHASE_CUR_ANGLE_DIFF},             //输入A的L1相电流角度差
    {21,    0, TYPE_INT16U,    TYPE_INT16U, 0, 2,   TYPE_ANA,   DEMUB_DATA_ID_FIRST_INPUT_PHASE_CUR_ANGLE_DIFF + 1},         //输入A的L2相电流角度差
    {22,    0, TYPE_INT16U,    TYPE_INT16U, 0, 2,   TYPE_ANA,   DEMUB_DATA_ID_FIRST_INPUT_PHASE_CUR_ANGLE_DIFF + 2},         //输入A的L3相电流角度差
    {23,    0, TYPE_INT16U,    TYPE_INT16U, 0, 2,   TYPE_ANA,   DEMUB_DATA_ID_FIRST_INPUT_PHASE_VOL_CUR_ANGLE_DIFF},         //输入A的L1同相电压电流角度差
    {24,    0, TYPE_INT16U,    TYPE_INT16U, 0, 2,   TYPE_ANA,   DEMUB_DATA_ID_FIRST_INPUT_PHASE_VOL_CUR_ANGLE_DIFF + 1},     //输入A的L2同相电压电流角度差
    {25,    0, TYPE_INT16U,    TYPE_INT16U, 0, 2,   TYPE_ANA,   DEMUB_DATA_ID_FIRST_INPUT_PHASE_VOL_CUR_ANGLE_DIFF + 2},     //输入A的L3同相电压电流角度差
    {26,    0, TYPE_FLOAT,     TYPE_INT16U, 1, 2,   TYPE_ANA,   DEMUB_DATA_ID_FIRST_AC_INPUT_FREQ},                          //输入A频率 _F
    {27,    0, TYPE_FLOAT,     TYPE_INT16U, 1, 2,   TYPE_ANA,   DEMUB_DATA_ID_FIRST_ZERO_GROUND_VOL},                        //输入A零地电压
    {28,    0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_FIRST_AC_PHASE_VOL_IMBALANCE},                 //交流A三相电压不平衡度
    {29,    0, TYPE_FLOAT,     TYPE_INT16U, 1, 2,   TYPE_ANA,   DEMUB_DATA_ID_SECOND_INPUT_PHASE_VOL},                       //输入B电压_U1
    {30,    0, TYPE_FLOAT,     TYPE_INT16U, 1, 2,   TYPE_ANA,   DEMUB_DATA_ID_SECOND_INPUT_PHASE_VOL + 1},                   //输入B电压_U2
    {31,    0, TYPE_FLOAT,     TYPE_INT16U, 1, 2,   TYPE_ANA,   DEMUB_DATA_ID_SECOND_INPUT_PHASE_VOL + 2},                   //输入B电压_U3
    {32,    0, TYPE_FLOAT,     TYPE_INT32U, 2, 4,   TYPE_ANA,   DEMUB_DATA_ID_SECOND_INPUT_PHASE_CUR},                       //输入B电流_I1
    {34,    0, TYPE_FLOAT,     TYPE_INT32U, 2, 4,   TYPE_ANA,   DEMUB_DATA_ID_SECOND_INPUT_PHASE_CUR + 1},                   //输入B电流_I2
    {36,    0, TYPE_FLOAT,     TYPE_INT32U, 2, 4,   TYPE_ANA,   DEMUB_DATA_ID_SECOND_INPUT_PHASE_CUR + 2},                   //输入B电流_I3
    {38,    0, TYPE_FLOAT,     TYPE_INT16S, 1, 2,   TYPE_ANA,   DEMUB_DATA_ID_SECOND_INPUT_LINE_VOL},                        //输入B线电压_U1
    {39,    0, TYPE_FLOAT,     TYPE_INT16S, 1, 2,   TYPE_ANA,   DEMUB_DATA_ID_SECOND_INPUT_LINE_VOL + 1},                    //输入B线电压_U2
    {40,    0, TYPE_FLOAT,     TYPE_INT16S, 1, 2,   TYPE_ANA,   DEMUB_DATA_ID_SECOND_INPUT_LINE_VOL + 2},                    //输入B线电压_U3
    {41,    0, TYPE_INT16U,    TYPE_INT16U, 0, 2,   TYPE_ANA,   DEMUB_DATA_ID_SECOND_INPUT_PHASE_VOL_ANGLE_DIFF},            //输入B的L1相电压角度差
    {42,    0, TYPE_INT16U,    TYPE_INT16U, 0, 2,   TYPE_ANA,   DEMUB_DATA_ID_SECOND_INPUT_PHASE_VOL_ANGLE_DIFF + 1},        //输入B的L2相电压角度差
    {43,    0, TYPE_INT16U,    TYPE_INT16U, 0, 2,   TYPE_ANA,   DEMUB_DATA_ID_SECOND_INPUT_PHASE_VOL_ANGLE_DIFF + 2},        //输入B的L3相电压角度差
    {44,    0, TYPE_INT16U,    TYPE_INT16U, 0, 2,   TYPE_ANA,   DEMUB_DATA_ID_SECOND_INPUT_PHASE_CUR_ANGLE_DIFF},            //输入B的L1相电流角度差
    {45,    0, TYPE_INT16U,    TYPE_INT16U, 0, 2,   TYPE_ANA,   DEMUB_DATA_ID_SECOND_INPUT_PHASE_CUR_ANGLE_DIFF + 1},        //输入B的L2相电流角度差
    {46,    0, TYPE_INT16U,    TYPE_INT16U, 0, 2,   TYPE_ANA,   DEMUB_DATA_ID_SECOND_INPUT_PHASE_CUR_ANGLE_DIFF + 2},        //输入B的L3相电流角度差
    {47,    0, TYPE_INT16U,    TYPE_INT16U, 0, 2,   TYPE_ANA,   DEMUB_DATA_ID_SECOND_INPUT_PHASE_VOL_CUR_ANGLE_DIFF},        //输入B的L1同相电压电流角度差
    {48,    0, TYPE_INT16U,    TYPE_INT16U, 0, 2,   TYPE_ANA,   DEMUB_DATA_ID_SECOND_INPUT_PHASE_VOL_CUR_ANGLE_DIFF + 1},    //输入B的L2同相电压电流角度差
    {49,    0, TYPE_INT16U,    TYPE_INT16U, 0, 2,   TYPE_ANA,   DEMUB_DATA_ID_SECOND_INPUT_PHASE_VOL_CUR_ANGLE_DIFF + 2},    //输入B的L3同相电压电流角度差
    {50,    0, TYPE_FLOAT,     TYPE_INT16U, 1, 2,   TYPE_ANA,   DEMUB_DATA_ID_SECOND_AC_INPUT_FREQ},                         //输入B频率 _F
    {51,    0, TYPE_FLOAT,     TYPE_INT16U, 1, 2,   TYPE_ANA,   DEMUB_DATA_ID_SECOND_ZERO_GROUND_VOL},                       //输入B零地电压
    {52,    0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_SECOND_AC_PHASE_VOL_IMBALANCE},                //交流B三相电压不平衡度
    {53,    0, TYPE_FLOAT,     TYPE_INT32U, 3, 4,   TYPE_ANA,   DEMUB_DATA_ID_ZERO_CUR},                                     //零序电流
    {55,    0, TYPE_FLOAT,     TYPE_INT16U, 1, 2,   TYPE_ANA,   DEMUB_DATA_ID_CUR_IMBALANCE},                                //电流不平衡度
    {56,    0, TYPE_FLOAT,     TYPE_INT32S, 2, 4,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_ACTIVE_POWER},                           //输入_L1相有功功率_P1
    {58,    0, TYPE_FLOAT,     TYPE_INT32S, 2, 4,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_ACTIVE_POWER + 1},                       //输入_L2相有功功率_P2
    {60,    0, TYPE_FLOAT,     TYPE_INT32S, 2, 4,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_ACTIVE_POWER + 2},                       //输入_L3相有功功率_P3
    {62,    0, TYPE_FLOAT,     TYPE_INT32S, 2, 4,   TYPE_ANA,   DEMUB_DATA_ID_SYSTEM_ACTIVE_POWER},                          //输入总有功功率_P
    {64,    0, TYPE_FLOAT,     TYPE_INT32S, 2, 4,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_REACTIVE_POWER},                         //L1相无功功率Q1
    {66,    0, TYPE_FLOAT,     TYPE_INT32S, 2, 4,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_REACTIVE_POWER + 1},                     //L2相无功功率Q2
    {68,    0, TYPE_FLOAT,     TYPE_INT32S, 2, 4,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_REACTIVE_POWER + 2},                     //L3相无功功率Q3
    {70,    0, TYPE_FLOAT,     TYPE_INT32S, 2, 4,   TYPE_ANA,   DEMUB_DATA_ID_SYSTEM_REACTIVE_POWER},                        //系统无功功率
    {72,    0, TYPE_FLOAT,     TYPE_INT16S, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_APPARENT_POWER},                         //L1相视在功率S1
    {73,    0, TYPE_FLOAT,     TYPE_INT16S, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_APPARENT_POWER + 1},                     //L2相视在功率S2
    {74,    0, TYPE_FLOAT,     TYPE_INT16S, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_APPARENT_POWER + 2},                     //L3相视在功率S3
    {75,    0, TYPE_FLOAT,     TYPE_INT16S, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_SYSTEM_APPARENT_POWER},                        //系统视在功率
    {76,    0, TYPE_FLOAT,     TYPE_INT16S, 3, 2,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_POWER_FACTOR},                           //输入_L1相功率因数_PF1
    {77,    0, TYPE_FLOAT,     TYPE_INT16S, 3, 2,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_POWER_FACTOR + 1},                       //输入_L2相功率因数_PF2
    {78,    0, TYPE_FLOAT,     TYPE_INT16S, 3, 2,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_POWER_FACTOR + 2},                       //输入_L3相功率因数_PF3
    {79,    0, TYPE_FLOAT,     TYPE_INT16S, 3, 2,   TYPE_ANA,   DEMUB_DATA_ID_SYSTEM_POWER_FACTOR},                           //输入总功率因数_PF
    {80,    0, TYPE_FLOAT,    TYPE_INT32S, 2, 4,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_ACTIVE_ENERGY},                          //输入_L1相有功电能 $电能数据以整数上送,上位机进行折算,除以精度$
    {82,    0, TYPE_FLOAT,    TYPE_INT32S, 2, 4,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_ACTIVE_ENERGY + 1},                      //输入_L2相有功电能
    {84,    0, TYPE_FLOAT,    TYPE_INT32S, 2, 4,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_ACTIVE_ENERGY + 2},                      //输入_L3相有功电能
    {86,    0, TYPE_FLOAT,    TYPE_INT32S, 2, 4,   TYPE_ANA,   DEMUB_DATA_ID_TOTAL_ACTIVE_ENERGY},                          //输入总有功电能
    {88,    0, TYPE_FLOAT,    TYPE_INT32S, 1, 4,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_POSITIVE_REACTIVE_ENERGY},               //输入_L1相正向无功电能
    {90,    0, TYPE_FLOAT,    TYPE_INT32S, 1, 4,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_POSITIVE_REACTIVE_ENERGY + 1},           //输入_L2相正向无功电能
    {92,    0, TYPE_FLOAT,    TYPE_INT32S, 1, 4,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_POSITIVE_REACTIVE_ENERGY + 2},           //输入_L3相正向无功电能
    {94,    0, TYPE_FLOAT,    TYPE_INT32S, 1, 4,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_TOATL_POSITIVE_REACTIVE_ENERGY},         //输入总正向无功电能
    {96,    0, TYPE_FLOAT,    TYPE_INT32S, 1, 4,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_NEGTIVE_REACTIVE_ENERGY},                //L1相负向无功电能
    {98,    0, TYPE_FLOAT,    TYPE_INT32S, 1, 4,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_NEGTIVE_REACTIVE_ENERGY + 1},            //L2相负向无功电能
    {100,   0, TYPE_FLOAT,    TYPE_INT32S, 1, 4,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_NEGTIVE_REACTIVE_ENERGY + 2},            //L3相负向无功电能
    {102,   0, TYPE_FLOAT,    TYPE_INT32S, 1, 4,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_TOATL_NEGTIVE_REACTIVE_ENERGY},          //系统负向无功电能
    {104,   0, TYPE_FLOAT,    TYPE_INT32S, 1, 4,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_TOTAL_REACTIVE_ENERGY},                  //输入_L1相总无功电能
    {106,   0, TYPE_FLOAT,    TYPE_INT32S, 1, 4,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_TOTAL_REACTIVE_ENERGY + 1},              //输入_L2相总无功电能
    {108,   0, TYPE_FLOAT,    TYPE_INT32S, 1, 4,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_TOTAL_REACTIVE_ENERGY + 2},              //输入_L3相总无功电能
    {110,   0, TYPE_FLOAT,    TYPE_INT32S, 1, 4,   TYPE_ANA,   DEMUB_DATA_ID_TOTAL_REACTIVE_ENERGY},                        //输入总无功电能
    {112,   0, TYPE_FLOAT,     TYPE_INT32U, 1, 4,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_NEED_CUR},                               //L1相电流需量
    {114,   0, TYPE_FLOAT,     TYPE_INT32U, 1, 4,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_NEED_CUR + 1},                           //L2相电流需量
    {116,   0, TYPE_FLOAT,     TYPE_INT32U, 1, 4,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_NEED_CUR + 2},                           //L3相电流需量
    {118,   0, TYPE_FLOAT,     TYPE_INT32U, 1, 4,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_MAX_NEED_CUR},                           //L1相电流最大需量
    {120,   0, TYPE_FLOAT,     TYPE_INT32U, 1, 4,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_MAX_NEED_CUR + 1},                       //L2相电流最大需量
    {122,   0, TYPE_FLOAT,     TYPE_INT32U, 1, 4,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_MAX_NEED_CUR + 2},                       //L3相电流最大需量
    {124,   0, TYPE_FLOAT,     TYPE_INT32S, 2, 4,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_NEED_ACTIVE_ENERGY},                     //L1相有功功率需量
    {126,   0, TYPE_FLOAT,     TYPE_INT32S, 2, 4,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_NEED_ACTIVE_ENERGY + 1},                 //L2相有功功率需量
    {128,   0, TYPE_FLOAT,     TYPE_INT32S, 2, 4,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_NEED_ACTIVE_ENERGY + 2},                 //L3相有功功率需量
    {130,   0, TYPE_FLOAT,     TYPE_INT32S, 2, 4,   TYPE_ANA,   DEMUB_DATA_ID_SYSTEM_NEED_ACTIVE_ENERGY},                    //系统有功功率需量
    {132,   0, TYPE_FLOAT,     TYPE_INT32S, 2, 4,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_MAX_NEED_ACTIVE_ENERGY},                 //L1相有功功率最大需量
    {134,   0, TYPE_FLOAT,     TYPE_INT32S, 2, 4,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_MAX_NEED_ACTIVE_ENERGY + 1},             //L2相有功功率最大需量
    {136,   0, TYPE_FLOAT,     TYPE_INT32S, 2, 4,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_MAX_NEED_ACTIVE_ENERGY + 2},             //L3相有功功率最大需量
    {138,   0, TYPE_FLOAT,     TYPE_INT32S, 2, 4,   TYPE_ANA,   DEMUB_DATA_ID_SYSTEM_MAX_NEED_ACTIVE_ENERGY},                //系统有功功率最大需量

    {200,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_CUR_HARMONIC_DISTORSTION_RATE},          //输入_I1电流总谐波畸变率THD_I1
    {201,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_CUR_HARMONIC_DISTORSTION_RATE + 1},      //输入_I2电流总谐波畸变率THD_I2
    {202,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_CUR_HARMONIC_DISTORSTION_RATE + 2},      //输入_I3电流总谐波畸变率THD_I3
    {203,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_CUR_AVE_HARMONIC_DISTORSTION_RATE},      //输入相电流平均总谐波畸变率THD_I
    {204,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_VOL_HARMONIC_DISTORSTION_RATE},          //V1总谐波畸变率 THD_V1
    {205,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_VOL_HARMONIC_DISTORSTION_RATE + 1},      //V2总谐波畸变率 THD_V2
    {206,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_VOL_HARMONIC_DISTORSTION_RATE + 2},      //V3总谐波畸变率 THD_V3
    {207,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_PHASE_VOL_AVE_HARMONIC_DISTORSTION_RATE},      //相电压平均总谐波畸变率 THD_V

    {208,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_VOL_HARMONIC_RATIO},                   //V1 2~31次谐波含有率
    {209,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_VOL_HARMONIC_RATIO + 1},
    {210,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_VOL_HARMONIC_RATIO + 2},
    {211,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_VOL_HARMONIC_RATIO + 3},
    {212,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_VOL_HARMONIC_RATIO + 4},
    {213,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_VOL_HARMONIC_RATIO + 5},
    {214,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_VOL_HARMONIC_RATIO + 6},
    {215,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_VOL_HARMONIC_RATIO + 7},
    {216,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_VOL_HARMONIC_RATIO + 8},
    {217,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_VOL_HARMONIC_RATIO + 9},
    {218,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_VOL_HARMONIC_RATIO + 10},
    {219,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_VOL_HARMONIC_RATIO + 11},
    {220,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_VOL_HARMONIC_RATIO + 12},
    {221,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_VOL_HARMONIC_RATIO + 13},
    {222,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_VOL_HARMONIC_RATIO + 14},
    {223,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_VOL_HARMONIC_RATIO + 15},
    {224,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_VOL_HARMONIC_RATIO + 16},
    {225,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_VOL_HARMONIC_RATIO + 17},
    {226,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_VOL_HARMONIC_RATIO + 18},
    {227,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_VOL_HARMONIC_RATIO + 19},
    {228,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_VOL_HARMONIC_RATIO + 20},
    {229,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_VOL_HARMONIC_RATIO + 21},
    {230,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_VOL_HARMONIC_RATIO + 22},
    {231,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_VOL_HARMONIC_RATIO + 23},
    {232,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_VOL_HARMONIC_RATIO + 24},
    {233,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_VOL_HARMONIC_RATIO + 25},
    {234,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_VOL_HARMONIC_RATIO + 26},
    {235,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_VOL_HARMONIC_RATIO + 27},
    {236,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_VOL_HARMONIC_RATIO + 28},
    {237,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_VOL_HARMONIC_RATIO + 29},
  
    {238,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_VOL_HARMONIC_RATIO},                  //I1 2~31次谐波含有率
    {239,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_VOL_HARMONIC_RATIO + 1},
    {240,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_VOL_HARMONIC_RATIO + 2},
    {241,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_VOL_HARMONIC_RATIO + 3},
    {242,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_VOL_HARMONIC_RATIO + 4},
    {243,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_VOL_HARMONIC_RATIO + 5},
    {244,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_VOL_HARMONIC_RATIO + 6},
    {245,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_VOL_HARMONIC_RATIO + 7},
    {246,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_VOL_HARMONIC_RATIO + 8},
    {247,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_VOL_HARMONIC_RATIO + 9},
    {248,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_VOL_HARMONIC_RATIO + 10},
    {249,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_VOL_HARMONIC_RATIO + 11},
    {250,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_VOL_HARMONIC_RATIO + 12},
    {251,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_VOL_HARMONIC_RATIO + 13},
    {252,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_VOL_HARMONIC_RATIO + 14},
    {253,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_VOL_HARMONIC_RATIO + 15},
    {254,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_VOL_HARMONIC_RATIO + 16},
    {255,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_VOL_HARMONIC_RATIO + 17},
    {256,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_VOL_HARMONIC_RATIO + 18},
    {257,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_VOL_HARMONIC_RATIO + 19},
    {258,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_VOL_HARMONIC_RATIO + 20},
    {259,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_VOL_HARMONIC_RATIO + 21},
    {260,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_VOL_HARMONIC_RATIO + 22},
    {261,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_VOL_HARMONIC_RATIO + 23},
    {262,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_VOL_HARMONIC_RATIO + 24},
    {263,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_VOL_HARMONIC_RATIO + 25},
    {264,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_VOL_HARMONIC_RATIO + 26},
    {265,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_VOL_HARMONIC_RATIO + 27},
    {266,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_VOL_HARMONIC_RATIO + 28},
    {267,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_VOL_HARMONIC_RATIO + 29},
  
    {268,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_VOL_HARMONIC_RATIO},                  //V2 2~31次谐波含有率
    {269,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_VOL_HARMONIC_RATIO + 1},
    {270,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_VOL_HARMONIC_RATIO + 2},
    {271,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_VOL_HARMONIC_RATIO + 3},
    {272,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_VOL_HARMONIC_RATIO + 4},
    {273,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_VOL_HARMONIC_RATIO + 5},
    {274,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_VOL_HARMONIC_RATIO + 6},
    {275,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_VOL_HARMONIC_RATIO + 7},
    {276,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_VOL_HARMONIC_RATIO + 8},
    {277,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_VOL_HARMONIC_RATIO + 9},
    {278,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_VOL_HARMONIC_RATIO + 10},
    {279,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_VOL_HARMONIC_RATIO + 11},
    {280,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_VOL_HARMONIC_RATIO + 12},
    {281,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_VOL_HARMONIC_RATIO + 13},
    {282,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_VOL_HARMONIC_RATIO + 14},
    {283,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_VOL_HARMONIC_RATIO + 15},
    {284,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_VOL_HARMONIC_RATIO + 16},
    {285,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_VOL_HARMONIC_RATIO + 17},
    {286,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_VOL_HARMONIC_RATIO + 18},
    {287,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_VOL_HARMONIC_RATIO + 19},
    {288,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_VOL_HARMONIC_RATIO + 20},
    {289,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_VOL_HARMONIC_RATIO + 21},
    {290,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_VOL_HARMONIC_RATIO + 22},
    {291,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_VOL_HARMONIC_RATIO + 23},
    {292,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_VOL_HARMONIC_RATIO + 24},
    {293,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_VOL_HARMONIC_RATIO + 25},
    {294,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_VOL_HARMONIC_RATIO + 26},
    {295,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_VOL_HARMONIC_RATIO + 27},
    {296,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_VOL_HARMONIC_RATIO + 28},
    {297,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_VOL_HARMONIC_RATIO + 29},
  
    {298,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_CUR_HARMONIC_RATIO},                  //I2 2~31次谐波含有率
    {299,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_CUR_HARMONIC_RATIO + 1},
    {300,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_CUR_HARMONIC_RATIO + 2},
    {301,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_CUR_HARMONIC_RATIO + 3},
    {302,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_CUR_HARMONIC_RATIO + 4},
    {303,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_CUR_HARMONIC_RATIO + 5},
    {304,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_CUR_HARMONIC_RATIO + 6},
    {305,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_CUR_HARMONIC_RATIO + 7},
    {306,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_CUR_HARMONIC_RATIO + 8},
    {307,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_CUR_HARMONIC_RATIO + 9},
    {308,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_CUR_HARMONIC_RATIO + 10},
    {309,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_CUR_HARMONIC_RATIO + 11},
    {310,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_CUR_HARMONIC_RATIO + 12},
    {311,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_CUR_HARMONIC_RATIO + 13},
    {312,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_CUR_HARMONIC_RATIO + 14},
    {313,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_CUR_HARMONIC_RATIO + 15},
    {314,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_CUR_HARMONIC_RATIO + 16},
    {315,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_CUR_HARMONIC_RATIO + 17},
    {316,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_CUR_HARMONIC_RATIO + 18},
    {317,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_CUR_HARMONIC_RATIO + 19},
    {318,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_CUR_HARMONIC_RATIO + 20},
    {319,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_CUR_HARMONIC_RATIO + 21},
    {320,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_CUR_HARMONIC_RATIO + 22},
    {321,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_CUR_HARMONIC_RATIO + 23},
    {322,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_CUR_HARMONIC_RATIO + 24},
    {323,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_CUR_HARMONIC_RATIO + 25},
    {324,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_CUR_HARMONIC_RATIO + 26},
    {325,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_CUR_HARMONIC_RATIO + 27},
    {326,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_CUR_HARMONIC_RATIO + 28},
    {327,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_A_PHASE_CUR_HARMONIC_RATIO + 29},
  
    {328,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_CUR_HARMONIC_RATIO},                  //V3 2~31次谐波含有率
    {329,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_CUR_HARMONIC_RATIO + 1},
    {330,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_CUR_HARMONIC_RATIO + 2},
    {331,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_CUR_HARMONIC_RATIO + 3},
    {332,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_CUR_HARMONIC_RATIO + 4},
    {333,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_CUR_HARMONIC_RATIO + 5},
    {334,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_CUR_HARMONIC_RATIO + 6},
    {335,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_CUR_HARMONIC_RATIO + 7},
    {336,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_CUR_HARMONIC_RATIO + 8},
    {337,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_CUR_HARMONIC_RATIO + 9},
    {338,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_CUR_HARMONIC_RATIO + 10},
    {339,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_CUR_HARMONIC_RATIO + 11},
    {340,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_CUR_HARMONIC_RATIO + 12},
    {341,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_CUR_HARMONIC_RATIO + 13},
    {342,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_CUR_HARMONIC_RATIO + 14},
    {343,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_CUR_HARMONIC_RATIO + 15},
    {344,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_CUR_HARMONIC_RATIO + 16},
    {345,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_CUR_HARMONIC_RATIO + 17},
    {346,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_CUR_HARMONIC_RATIO + 18},
    {347,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_CUR_HARMONIC_RATIO + 19},
    {348,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_CUR_HARMONIC_RATIO + 20},
    {349,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_CUR_HARMONIC_RATIO + 21},
    {350,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_CUR_HARMONIC_RATIO + 22},
    {351,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_CUR_HARMONIC_RATIO + 23},
    {352,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_CUR_HARMONIC_RATIO + 24},
    {353,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_CUR_HARMONIC_RATIO + 25},
    {354,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_CUR_HARMONIC_RATIO + 26},
    {355,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_CUR_HARMONIC_RATIO + 27},
    {356,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_CUR_HARMONIC_RATIO + 28},
    {357,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_B_PHASE_CUR_HARMONIC_RATIO + 29},
  
    {358,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_CUR_HARMONIC_RATIO},                  //I3 2~31次谐波含有率
    {359,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_CUR_HARMONIC_RATIO + 1},
    {360,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_CUR_HARMONIC_RATIO + 2},
    {361,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_CUR_HARMONIC_RATIO + 3},
    {362,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_CUR_HARMONIC_RATIO + 4},
    {363,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_CUR_HARMONIC_RATIO + 5},
    {364,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_CUR_HARMONIC_RATIO + 6},
    {365,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_CUR_HARMONIC_RATIO + 7},
    {366,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_CUR_HARMONIC_RATIO + 8},
    {367,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_CUR_HARMONIC_RATIO + 9},
    {368,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_CUR_HARMONIC_RATIO + 10},
    {369,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_CUR_HARMONIC_RATIO + 11},
    {370,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_CUR_HARMONIC_RATIO + 12},
    {371,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_CUR_HARMONIC_RATIO + 13},
    {372,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_CUR_HARMONIC_RATIO + 14},
    {373,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_CUR_HARMONIC_RATIO + 15},
    {374,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_CUR_HARMONIC_RATIO + 16},
    {375,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_CUR_HARMONIC_RATIO + 17},
    {376,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_CUR_HARMONIC_RATIO + 18},
    {377,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_CUR_HARMONIC_RATIO + 19},
    {378,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_CUR_HARMONIC_RATIO + 20},
    {379,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_CUR_HARMONIC_RATIO + 21},
    {380,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_CUR_HARMONIC_RATIO + 22},
    {381,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_CUR_HARMONIC_RATIO + 23},
    {382,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_CUR_HARMONIC_RATIO + 24},
    {383,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_CUR_HARMONIC_RATIO + 25},
    {384,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_CUR_HARMONIC_RATIO + 26},
    {385,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_CUR_HARMONIC_RATIO + 27},
    {386,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_CUR_HARMONIC_RATIO + 28},
    {387,   0, TYPE_FLOAT,     TYPE_INT16U, 2, 2,   TYPE_ANA,   DEMUB_DATA_ID_C_PHASE_CUR_HARMONIC_RATIO + 29},

    // 打桩参数部分
    {2000,  0, TYPE_INT32U,    TYPE_INT32U, 0, 4,   TYPE_ANA,   DEMUB_DATA_ID_MOCK_PHASE_VOL},                       // A相电压有效值(打桩)
    {2002,  0, TYPE_INT32U,    TYPE_INT32U, 0, 4,   TYPE_ANA,   DEMUB_DATA_ID_MOCK_PHASE_VOL + 1},                   // B相电压有效值(打桩)
    {2004,  0, TYPE_INT32U,    TYPE_INT32U, 0, 4,   TYPE_ANA,   DEMUB_DATA_ID_MOCK_PHASE_VOL + 2},                   // C相电压有效值(打桩)
    {2006,  0, TYPE_INT32U,    TYPE_INT32U, 0, 4,   TYPE_ANA,   DEMUB_DATA_ID_MOCK_PHASE_CUR},                       // A相电流有效值(打桩)
    {2008,  0, TYPE_INT32U,    TYPE_INT32U, 0, 4,   TYPE_ANA,   DEMUB_DATA_ID_MOCK_PHASE_CUR + 1},                   // B相电流有效值(打桩)
    {2010,  0, TYPE_INT32U,    TYPE_INT32U, 0, 4,   TYPE_ANA,   DEMUB_DATA_ID_MOCK_PHASE_CUR + 2},                   // C相电流有效值(打桩)
    {2012,  0, TYPE_INT32U,    TYPE_INT32U, 0, 4,   TYPE_ANA,   DEMUB_DATA_ID_MOCK_ZERO_GROUND},                     // 零地电压(打桩)
    {2014,  0, TYPE_INT32U,    TYPE_INT32U, 0, 4,   TYPE_ANA,   DEMUB_DATA_ID_MOCK_FREQUENCY},                       // 频率(打桩)
    {2016,  0, TYPE_INT32U,    TYPE_INT32U, 0, 4,   TYPE_ANA,   DEMUB_DATA_ID_MOCK_PHASE_VOL_ANGLE},                 // A相电压夹角(打桩)
    {2018,  0, TYPE_INT32U,    TYPE_INT32U, 0, 4,   TYPE_ANA,   DEMUB_DATA_ID_MOCK_PHASE_VOL_ANGLE + 1},             // B相电压夹角(打桩)
    {2020,  0, TYPE_INT32U,    TYPE_INT32U, 0, 4,   TYPE_ANA,   DEMUB_DATA_ID_MOCK_PHASE_VOL_ANGLE + 2},             // C相电压夹角(打桩)
    {2022,  0, TYPE_INT32U,    TYPE_INT32U, 0, 4,   TYPE_ANA,   DEMUB_DATA_ID_MOCK_PHASE_VOL_CUR_ANGLE},             // A相电压与电流夹角(打桩)
    {2024,  0, TYPE_INT32U,    TYPE_INT32U, 0, 4,   TYPE_ANA,   DEMUB_DATA_ID_MOCK_PHASE_VOL_CUR_ANGLE + 1},         // B相电压与电流夹角(打桩)
    {2026,  0, TYPE_INT32U,    TYPE_INT32U, 0, 4,   TYPE_ANA,   DEMUB_DATA_ID_MOCK_PHASE_VOL_CUR_ANGLE + 2},         // C相电压与电流夹角(打桩)
    {2028,  0, TYPE_INT32U,    TYPE_INT32U, 0, 4,   TYPE_ANA,   DEMUB_DATA_ID_MOCK_PHASE_POWER_FACTOR},              // A相功率因数(打桩)
    {2030,  0, TYPE_INT32U,    TYPE_INT32U, 0, 4,   TYPE_ANA,   DEMUB_DATA_ID_MOCK_PHASE_POWER_FACTOR + 1},          // B相功率因数(打桩)
    {2032,  0, TYPE_INT32U,    TYPE_INT32U, 0, 4,   TYPE_ANA,   DEMUB_DATA_ID_MOCK_PHASE_POWER_FACTOR + 2},          // C相功率因数(打桩)
    {2034,  0, TYPE_INT32U,    TYPE_INT32U, 0, 4,   TYPE_ANA,   DEMUB_DATA_ID_MOCK_TOTAL_POWER_FACTOR},              // 合功率因数(打桩)
    {2036,  0, TYPE_INT32U,    TYPE_INT32U, 0, 4,   TYPE_ANA,   DEMUB_DATA_ID_MOCK_POWER_DIRECTOR},                  // 功率方向(打桩)
    {2038,  0, TYPE_INT16U,    TYPE_INT16U, 0, 2,   TYPE_ANA,   DEMUB_DATA_ID_MOCK_SWITCH},                          // 参数打桩开关(打桩)

    
    {5632,  0, TYPE_STRING,    TYPE_STRING, 0, 20,  TYPE_ANA,   DEMUB_DATA_ID_SOFTWARE_NAME},        // 软件名称
    {5642,  0, TYPE_STRING,    TYPE_STRING, 0, 20,  TYPE_ANA,   DEMUB_DATA_ID_SOFTWARE_VERSION},     // 软件版本
    {5652,  0, TYPE_STRING,    TYPE_STRING, 0, 20,  TYPE_ANA,   DEMUB_DATA_ID_SOFTWARE_DATE},        // 软件日期
    {5662,  0, TYPE_STRING,    TYPE_STRING, 0, 20,  TYPE_ANA,   DEMUB_DATA_ID_SERIAL_NUMBER},        // 序列号
    {5672,  0, TYPE_STRING,    TYPE_STRING, 0, 16,  TYPE_ANA,   DEMUB_DATA_ID_HARDWARE_VERSION},     // 硬件版本号
    {5682,  0, TYPE_STRING,    TYPE_STRING, 0, 20,  TYPE_ANA,   DEMUB_DATA_ID_BOARD_MANU_DATE },     // 单板生产日期
    

    {40970, 0, TYPE_INT16U,    TYPE_INT16U, 0, 2,   TYPE_ANA,   DEMUB_DATA_ID_AC_CUR_IN_TRANS_RATIO},        // 交流输入电流互感器变比
    {40971, 0, TYPE_INT16U,    TYPE_INT16U, 3, 2,   TYPE_ANA,   DEMUB_DATA_ID_AC_MTS_STATUS_DETECT},         // 交流MTS状态检测
    {40972, 0, TYPE_INT16U,    TYPE_INT16U, 0, 2,   TYPE_ANA,   DEMUB_DATA_ID_AC_INPUT_MCCB_CONFIG},         // 交流输入塑壳断路器配置
    {40973, 0, TYPE_FLOAT,     TYPE_INT16S, 1, 2,   TYPE_ANA,   DEMUB_DATA_ID_INPUT_A_CURR_ZERO_POINT},             // 输入A电流零点1
    {40974, 0, TYPE_FLOAT,     TYPE_INT16S, 1, 2,   TYPE_ANA,   DEMUB_DATA_ID_INPUT_A_CURR_ZERO_POINT + 1},         // 输入A电流零点2
    {40975, 0, TYPE_FLOAT,     TYPE_INT16S, 1, 2,   TYPE_ANA,   DEMUB_DATA_ID_INPUT_A_CURR_ZERO_POINT + 2},         // 输入A电流零点3
    {40976, 0, TYPE_FLOAT,     TYPE_INT16S, 3, 2,   TYPE_ANA,   DEMUB_DATA_ID_INPUT_A_CURR_SLOP},                   // 输入A电流斜率1
    {40977, 0, TYPE_FLOAT,     TYPE_INT16S, 3, 2,   TYPE_ANA,   DEMUB_DATA_ID_INPUT_A_CURR_SLOP + 1},               // 输入A电流斜率2
    {40978, 0, TYPE_FLOAT,     TYPE_INT16S, 3, 2,   TYPE_ANA,   DEMUB_DATA_ID_INPUT_A_CURR_SLOP + 2},               // 输入A电流斜率3
    {40979, 0, TYPE_FLOAT,     TYPE_INT16S, 1, 2,   TYPE_ANA,   DEMUB_DATA_ID_INPUT_A_VOL_ZERO_POINT},              // 输入A电压零点1
    {40980, 0, TYPE_FLOAT,     TYPE_INT16S, 1, 2,   TYPE_ANA,   DEMUB_DATA_ID_INPUT_A_VOL_ZERO_POINT + 1},          // 输入A电压零点2
    {40981, 0, TYPE_FLOAT,     TYPE_INT16S, 1, 2,   TYPE_ANA,   DEMUB_DATA_ID_INPUT_A_VOL_ZERO_POINT + 2},          // 输入A电压零点3
    {40982, 0, TYPE_FLOAT,     TYPE_INT16S, 3, 2,   TYPE_ANA,   DEMUB_DATA_ID_INPUT_A_VOL_SLOP},                    // 输入A电压斜率1
    {40983, 0, TYPE_FLOAT,     TYPE_INT16S, 3, 2,   TYPE_ANA,   DEMUB_DATA_ID_INPUT_A_VOL_SLOP + 1},                // 输入A电压斜率2
    {40984, 0, TYPE_FLOAT,     TYPE_INT16S, 3, 2,   TYPE_ANA,   DEMUB_DATA_ID_INPUT_A_VOL_SLOP + 2},                // 输入A电压斜率3
    {40985, 0, TYPE_FLOAT,     TYPE_INT16S, 1, 2,   TYPE_ANA,   DEMUB_DATA_ID_INPUT_B_CURR_ZERO_POINT},             // 输入B电流零点1
    {40986, 0, TYPE_FLOAT,     TYPE_INT16S, 1, 2,   TYPE_ANA,   DEMUB_DATA_ID_INPUT_B_CURR_ZERO_POINT + 1},         // 输入B电流零点2
    {40987, 0, TYPE_FLOAT,     TYPE_INT16S, 1, 2,   TYPE_ANA,   DEMUB_DATA_ID_INPUT_B_CURR_ZERO_POINT + 2},         // 输入B电流零点3
    {40988, 0, TYPE_FLOAT,     TYPE_INT16S, 3, 2,   TYPE_ANA,   DEMUB_DATA_ID_INPUT_B_CURR_SLOP},                   // 输入B电流斜率1
    {40989, 0, TYPE_FLOAT,     TYPE_INT16S, 3, 2,   TYPE_ANA,   DEMUB_DATA_ID_INPUT_B_CURR_SLOP + 1},               // 输入B电流斜率2
    {40990, 0, TYPE_FLOAT,     TYPE_INT16S, 3, 2,   TYPE_ANA,   DEMUB_DATA_ID_INPUT_B_CURR_SLOP + 2},               // 输入B电流斜率3
    {40991, 0, TYPE_FLOAT,     TYPE_INT16S, 1, 2,   TYPE_ANA,   DEMUB_DATA_ID_INPUT_B_VOL_ZERO_POINT},              // 输入B电压零点1
    {40992, 0, TYPE_FLOAT,     TYPE_INT16S, 1, 2,   TYPE_ANA,   DEMUB_DATA_ID_INPUT_B_VOL_ZERO_POINT + 1},          // 输入B电压零点2
    {40993, 0, TYPE_FLOAT,     TYPE_INT16S, 1, 2,   TYPE_ANA,   DEMUB_DATA_ID_INPUT_B_VOL_ZERO_POINT + 2},          // 输入B电压零点3
    {40994, 0, TYPE_FLOAT,     TYPE_INT16S, 3, 2,   TYPE_ANA,   DEMUB_DATA_ID_INPUT_B_VOL_SLOP},                    // 输入B电压斜率1
    {40995, 0, TYPE_FLOAT,     TYPE_INT16S, 3, 2,   TYPE_ANA,   DEMUB_DATA_ID_INPUT_B_VOL_SLOP + 1},                // 输入B电压斜率2
    {40996, 0, TYPE_FLOAT,     TYPE_INT16S, 3, 2,   TYPE_ANA,   DEMUB_DATA_ID_INPUT_B_VOL_SLOP + 2},                // 输入B电压斜率3
    {40997, 0, TYPE_INT16U,    TYPE_INT16U, 0, 2,   TYPE_ANA,   DEMUB_DATA_ID_DEVICE_ADDR},                         // 设备地址

    {45060, 0, TYPE_INT16U,    TYPE_INT16U, 0, 2,   TYPE_ANA,   DEMUB_DATA_ID_CLEAN_ELECTRIC_ENERGY},               // 电能清零

    {45400, 0, TYPE_INT16U,    TYPE_INT16U, 0, 2,   TYPE_ANA,   DEMUB_DATA_ID_CTRL_OUTPUT_RLY},                      // 控制输出干接点1
    {45401, 0, TYPE_INT16U,    TYPE_INT16U, 0, 2,   TYPE_ANA,   DEMUB_DATA_ID_CTRL_OUTPUT_RLY + 1},                  // 控制输出干接点2

};



dev_type_t *init_dev_north_demub(void)
{
    return &dev_val_north_demub;
}


void check_ana_reserve_flag(int index, void* data, modbus_addr_map_data_t* data_map)
{
    if (data_map[index].reserve_flag != 1)
    {
        get_data_by_id_type(index, data, data_map);
        return;
    }
    return;
}



/*******************************打包函数定义*****************/
int floattoint32u(int* index , unsigned char* data_buff , int* reg_nums , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map  )
{
    float data = 0.0 ;  
    int tmp = 0;
    check_ana_reserve_flag( *index ,&data,data_map);    
    tmp =round(data * percision );
    put_uint32_to_buff(&data_buff[total_len], (unsigned int)(int)(tmp));
    (*index)++;
    (*reg_nums) -= 2;
    return DATA_LEN_4;
}
int floattoint32s(int* index , unsigned char* data_buff, int* reg_nums , int data_len ,  int percision, int total_len, modbus_addr_map_data_t* data_map  )
{
    float data = 0.0 ;  
    int tmp = 0;
    check_ana_reserve_flag( *index ,&data, data_map);    
    tmp =round(data * percision );
    put_int32_to_buff(&data_buff[total_len], (signed int)(tmp));
    (*index)++;
    (*reg_nums) -= 2;
    return DATA_LEN_4;
}



int floattoint16u(int* index , unsigned char* data_buff , int* reg_nums , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map )
{
    float data = 0.0 ;  
    int tmp = 0;
    check_ana_reserve_flag( *index ,&data, data_map);    
    tmp =round(data * percision );
    put_uint16_to_buff(&data_buff[total_len], (unsigned short)(short)(tmp));
    (*index)++;
    (*reg_nums) -= 1;
    return DATA_LEN_2;
}
int floattoint16s(int* index , unsigned char* data_buff , int* reg_nums , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map )
{
    float data = 0.0 ;  
    int tmp = 0;
    check_ana_reserve_flag( *index ,&data, data_map);    
    tmp =round(data * percision );
    put_int16_to_buff(&data_buff[total_len], (signed short)(tmp));
    (*index)++;
    (*reg_nums) -= 1;
    return DATA_LEN_2;
}
int int16utoint16u(int* index , unsigned char* data_buff , int* reg_nums , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map )
{
    unsigned short data = 0;
    check_ana_reserve_flag(*index ,&data, data_map);  
    put_uint16_to_buff(&data_buff[total_len], data * percision);
    (*index)++;
    (*reg_nums) -= 1;
    return DATA_LEN_2;
}



int int32stoint32s(int* index , unsigned char* data_buff , int* reg_nums, int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map )
{
    int data = 0;
    check_ana_reserve_flag( *index ,&data, data_map);  

    put_int32_to_buff(&data_buff[total_len], data * percision );
    (*index)++;
    (*reg_nums) -= 2;
    return DATA_LEN_4;
}



int int16stoint16s(int* index , unsigned char* data_buff , int* reg_nums , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map )
{
    signed short data = 0;
    check_ana_reserve_flag( *index ,&data, data_map);  
    put_int16_to_buff(&data_buff[total_len], data * percision);

    (*index)++;
    (*reg_nums) -= 1;
    return DATA_LEN_2;
}

int int32utoint32u(int* index , unsigned char* data_buff , int* reg_nums, int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map )
{
    unsigned int data = 0;
    check_ana_reserve_flag( *index ,&data, data_map);  

    put_uint32_to_buff(&data_buff[total_len], data * percision );
    (*index)++;
    (*reg_nums) -= 2;
    return DATA_LEN_4;
}

int stringtostring(int* index , unsigned char* data_buff , int* reg_nums , int data_len,  int percision  , int total_len, modbus_addr_map_data_t* data_map )
{
    unsigned char data[64]={0};
    check_ana_reserve_flag( *index ,&data, data_map);  
    rt_memcpy_s(&data_buff[total_len], data_len, data, data_len);
    (*reg_nums) -= data_len / 2;
    (*index)++;
    return data_len;
}

int datetoint16u(int* index, unsigned char* data_buff, int* reg_nums, int data_len, int percision, int total_len, modbus_addr_map_data_t* data_map)
{
    date_base_t data = {0};
    get_one_data(data_map[*index].data_addr, &data);
    put_uint16_to_buff(&data_buff[total_len], data.year);
    put_uint16_to_buff(&data_buff[total_len+2], data.month);
    put_uint16_to_buff(&data_buff[total_len+4], data.day);
    (*index) += 3;
    (*reg_nums) -= 3;
    return DATA_LEN_6;
}


/*********************************解包函数定义***********************************************/

int parse_int32stoint32s(int* index, unsigned char* data_buff, int* data_valude_index, int percision, int* reg_nums, modbus_addr_map_data_t* data_map)
{
    int data = 0;
    int result = 0;
    data = get_long_data(&data_buff[*data_valude_index]);
    result = (int)(data / percision);
    (*reg_nums) -= 2;
    (*data_valude_index) += 4;
    return set_data_by_id_type(*index, &result, data_map);
}



int parse_int16utofloat(int* index, unsigned char* data_buff, int* data_valude_index, int percision, int* reg_nums, modbus_addr_map_data_t* data_map)
{
    unsigned short data = 0;
    float result = 0.0; 
    data = get_uint16_data(&data_buff[*data_valude_index]);
    result = (float)data / percision;
    (*reg_nums) -= 1;
    (*data_valude_index) += 2;
    return set_data_by_id_type(*index, &result, data_map);
}

int parse_int16stofloat(int* index, unsigned char* data_buff, int* data_valude_index, int percision, int* reg_nums, modbus_addr_map_data_t* data_map)
{
    signed short data = 0;
    float result = 0.0;
    data = get_int16_data(&data_buff[*data_valude_index]);
    result = (float)data / percision;
    (*reg_nums) -= 1;
    (*data_valude_index) += 2;
    return set_data_by_id_type(*index, &result, data_map);
}

int parse_int32utofloat(int* index, unsigned char* data_buff, int* data_valude_index, int percision, int* reg_nums, modbus_addr_map_data_t* data_map)
{
    unsigned int data = 0;
    float result = 0.0;
    data = get_ulong_data(&data_buff[*data_valude_index]);
    result = (float)data / percision;
    (*reg_nums) -= 2;
    (*data_valude_index) += 4;
    return set_data_by_id_type(*index, &result, data_map);
}

int parse_int32stofloat(int* index, unsigned char* data_buff, int* data_valude_index, int percision, int* reg_nums, modbus_addr_map_data_t* data_map)
{
    signed int data = 0;
    float result = 0.0;
    data = get_ulong_data(&data_buff[*data_valude_index]); // 假设 get_int32_data 是正确的函数
    result = (float)data / percision;
    (*reg_nums) -= 2;
    (*data_valude_index) += 4;
    return set_data_by_id_type(*index, &result, data_map);
}

int parse_int16utoint16u(int* index, unsigned char* data_buff, int* data_valude_index, int percision, int* reg_nums, modbus_addr_map_data_t* data_map)
{
    unsigned short data = 0;
    unsigned short result = 0;
    data = get_uint16_data(&data_buff[*data_valude_index]);
    result = (unsigned short)(data / percision);
    (*reg_nums) -= 1;
    (*data_valude_index) += 2;
    return set_data_by_id_type(*index, &result, data_map);
}

int parse_int32utoint32u(int* index, unsigned char* data_buff, int* data_valude_index, int percision, int* reg_nums, modbus_addr_map_data_t* data_map)
{
    unsigned int data = 0;
    unsigned int result = 0;
    data = get_ulong_data(&data_buff[*data_valude_index]);
    result = (unsigned int)(data / percision);
    (*reg_nums) -= 2;
    (*data_valude_index) += 4;
    return set_data_by_id_type(*index, &result, data_map);
}

int parse_stringtostring(int* index, unsigned char* data_buff, int* data_valude_index, int percision, int* reg_nums, modbus_addr_map_data_t* data_map)
{
    int ret = SUCCESSFUL;
    unsigned char* data = (unsigned char*)rt_malloc(data_map[*index].data_len);
    if (data == NULL)
    {
        return FAILURE;
    }
    rt_memcpy_s(data, data_map[*index].data_len, &data_buff[*data_valude_index], data_map[*index].data_len);
    (*reg_nums) -= data_map[*index].data_len / 2;
    (*data_valude_index) += data_map[*index].data_len;
    ret = set_data_by_id_type(*index, data, data_map);
    rt_free(data);
    return ret;  
}

char set_data_by_id_type(int index, void* data, modbus_addr_map_data_t* data_map)
{
    unsigned char id_type = data_map[index].sid_type;

    if (id_type == TYPE_PARA)
    {
        if (set_one_para(data_map[index].data_addr, data, FALSE, TRUE) < 0)
        {
            return FAILURE;
        }
    }
    else if (id_type == TYPE_ALAM_PARA)
    {
        if (set_alm_para(data_map[index].data_addr, data, TRUE) == FAILURE)
        {
            return FAILURE;
        }
    }
    else if (id_type == TYPE_ANA)
    {
        if (set_one_data(data_map[index].data_addr, data) == FAILURE)
        {
            return FAILURE;
        }
    }
    return SUCCESSFUL;
}



void get_data_by_id_type(int index , void* data, modbus_addr_map_data_t* data_map)
{   
    unsigned char id_type = data_map[index].sid_type;

    if(id_type == TYPE_PARA )
    {
        get_one_para( data_map[index].data_addr , data);
    }
    else if(id_type == TYPE_ANA )
    {
        get_one_data( data_map[index].data_addr , data);
    }
    else if(id_type == TYPE_ALAM_PARA)
    {
        get_alm_para( data_map[index].data_addr , data);
    }
}


//总入口函数
//打包入口函数

int new_pack_data_to_buff(void* cmd_buff , int* index, int offset_value, modbus_addr_map_data_t* data_map)
{
    int type = 0;
    int old_type = 0;
    int percision = 0;
    int data_len = 0;
    int total_len = 0;
    int reg_nums = g_reg_nums;

    //+1应为第一个字节要放总长度（尽管可以直接寄存器数目计算，但是为了验证代码准性，累加长度的）
    unsigned char *data_buff = (((cmd_buf_t *)cmd_buff)->buf) + offset_value; // 准备往data_buff中存放数据

    while (reg_nums > 0 )
    {
        // is_contain_time(*index , 0, data_map);
        type = data_map[*index].type;
        old_type = data_map[*index].old_type;
        percision = pow(10, data_map[*index].precision);
        data_len = data_map[*index].data_len;

        for( int i = 0 ;i < sizeof(pack_fun_map)/sizeof(pack_fun_map_t); i++)
        {
            if (pack_fun_map[i].src_type == TYPE_MAX)
            {
                LOG_E("pack | i:%d, not this data type!\n", i);
                ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_VALUE;
                return FAILURE;
            }
            if(pack_fun_map[i].src_type == old_type && pack_fun_map[i].dst_type == type)
            {
                total_len += pack_fun_map[i].pack_fun(index , data_buff , &reg_nums , data_len , percision , total_len,data_map);
                break;
            }
        }
    }
    return total_len;
}




int new_pack_ana_para_data(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    int reg_addr = g_reg_addr;
    /*先找到基地址的下标索引*/
    int index = find_register_start_addr_index(reg_addr);
    if (index == NO_MATCH_REGISTER_ADDR)
    {
        ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_ADDR;
        return SUCCESSFUL;
    }
    int data_len = new_pack_data_to_buff( cmd_buff , &index, 1,g_modbus_data_map);

    ((cmd_buf_t *)cmd_buff)->buf[0] = data_len;
    ((cmd_buf_t *)cmd_buff)->data_len = data_len + 1;

    return SUCCESSFUL;
}

int parse_start_addr_and_reg_nums(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    unsigned char *data_buff = ((cmd_buf_t *)cmd_buff)->buf;

    g_reg_addr = get_int16_data(&data_buff[0]); // 获取数据域的寄存器首地址 2字节
    g_reg_nums = get_int16_data(&data_buff[2]); // 获取数据域的寄存器个数 2字节
    return SUCCESSFUL;
}



int find_register_start_addr_index(int reg_addr)
{
    int ret = NO_MATCH_REGISTER_ADDR;
    int star_index = 0;
    int mid_index = 0;
    int end_index = sizeof(g_modbus_data_map) / sizeof(modbus_addr_map_data_t) - 1;

    while (star_index <= end_index)
    {
        mid_index = (star_index + end_index) / 2;
        if (g_modbus_data_map[mid_index].register_addr == reg_addr)
        {
            return mid_index; // 直接返回索引
        }
        if (g_modbus_data_map[mid_index].register_addr < reg_addr)
        {
            star_index = mid_index + 1;
        }
        else
        {
            end_index = mid_index - 1;
        }
    }

    return ret;
}



short deal_special_data(int reg_addr, unsigned char *data_buff)
{
    int ret = 0;
    unsigned short data = get_uint16_data(data_buff);
    unsigned short origin_addr = 0;

    if(reg_addr == 40997)
    {

        // 设备地址
        ret = handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char*)&origin_addr, sizeof(origin_addr), DEVICE_ADDR_OFFSET);
        RETURN_VAL_IF_FAIL(ret == SUCCESSFUL, ret);
        if(origin_addr == data)
        {
            ret = SUCCESSFUL;
            return ret;
        }

        ret = handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&data, sizeof(data), DEVICE_ADDR_OFFSET);
    }
    return ret;
}



int new_parse_para_data_from_buff(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    unsigned char *data_buff = ((cmd_buf_t *)cmd_buff)->buf;

    g_reg_addr = get_int16_data(&data_buff[0]); // 获取数据域的寄存器首地址 2字节
    g_reg_nums = get_int16_data(&data_buff[2]); // 获取数据域的寄存器个数 2字节

    int data_valude_indx = 5;
    int reg_addr = g_reg_addr;
    int reg_nums = g_reg_nums;
    int type = 0;
    int old_type = 0;
    int percision = 0;
    int set_index = 0;
    char set_val = 0;
    /*先找到基地址的下标索引*/
    int index = find_register_start_addr_index(reg_addr);
    if (index == NO_MATCH_REGISTER_ADDR)
    {
        ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_ADDR;
        return SUCCESSFUL;
    }

    while ( (reg_nums > 0 ) && ( index < MODBUS_DATA_MAP_LEN) )
    {
        unsigned char flag_result = g_modbus_data_map[index].reserve_flag;
        if(flag_result == 1)
        {
            index ++ ;
            reg_nums--;
            data_valude_indx += 2;
            continue;
        }
        type = g_modbus_data_map[index].type;
        old_type = g_modbus_data_map[index].old_type;
        percision = pow(10, g_modbus_data_map[index].precision);
        for( int i = 0 ;i < PARSE_FUN_MAP_LEN; i++)
        {
            if (parse_fun_map[i].src_type == TYPE_MAX)
            {
                LOG_E("parse | not this data type!\n");
                ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_VALUE;
                return SUCCESSFUL;
            }
            if(parse_fun_map[i].src_type == type && parse_fun_map[i].dst_type == old_type)
            {
                
                if(deal_special_data(g_modbus_data_map[index].register_addr, (unsigned char*)&data_buff[data_valude_indx]) != SUCCESSFUL)
                {
                    LOG_E("deal_special_data error!");
                    ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_ADDR;
                    return SUCCESSFUL;
                }
                
                if(parse_fun_map[i].parse_func(&index , data_buff , &data_valude_indx , percision , &reg_nums, g_modbus_data_map) != SUCCESSFUL)
                {
                    ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_VALUE;
                    return SUCCESSFUL;
                }
                break;
            }
        }
        
        
        // 干接点做特殊处理
        if((45400 == g_modbus_data_map[index].register_addr) || (45401 == g_modbus_data_map[index].register_addr))
        {
            set_index = g_modbus_data_map[index].register_addr - 45400;
            get_one_data(DEMUB_DATA_ID_CTRL_OUTPUT_RLY + set_index, &set_val);
            setDoValue(set_index, set_val);
        }
        

        // 电量清零
        if(45060 == g_modbus_data_map[index].register_addr)
        {
            pub_msg_to_thread(CLEAN_HIS_ENERGY_MSG, NULL, 0);
        }
        
        index ++;
    }
    pub_msg_to_thread(NORTH_SET_PARA_MSG, NULL, 0);
    return SUCCESSFUL;
}



int pack_set_para_data(void* dev_inst, void* cmd_buff)
{
    int offset = 0;
    // 正确响应，数据域：回复寄存器地址+寄存器个数
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    unsigned char *data_buff = ((cmd_buf_t *)cmd_buff)->buf; // 准备往data_buff中存放数据

    put_uint16_to_buff(&data_buff[offset], g_reg_addr);
    offset += 2;

    put_uint16_to_buff(&data_buff[offset], g_reg_nums);
    offset += 2;

    ((cmd_buf_t *)cmd_buff)->data_len = offset;
    return SUCCESSFUL;
}

