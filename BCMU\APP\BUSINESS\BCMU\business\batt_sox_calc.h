/*******************************************************************************
  * @file        batt_sox_calc.h
  * @version     v1.0.0
  * @copyright   COPYRIGHT &copy; 2023 CSG
  * <AUTHOR>
  * @date        2023-4-25
  * @brief
  * @attention
  * Modification History
  * DATE         DESCRIPTION
  * ------------------------
  * - 2023-4-25  dongmengling Created
*******************************************************************************/

 
#ifndef _BATT_SOX_CALC_H
#define _BATT_SOX_CALC_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "battery.h"



//#define DISCHARGE_TIME_DIFF_SEC  5       ///放电秒计时两次时间差
//#define CHARGE_TIME_DIFF_SEC     60      ///充电秒计时两次时间差


void init_sox(batt_save_t* batt_save_data, batt_deal_info_t* batt_deal);
float ocv_to_soc(float ocv);
float calc_soc_from_cap(batt_deal_info_t* batt_deal);
void calc_batt_SOH(batt_deal_info_t* batt_deal);


#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _BATT_SOX_CALC_H