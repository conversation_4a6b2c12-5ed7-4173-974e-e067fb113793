#include <rtthread.h>
#include <rtdevice.h>
#include <board.h>
#include <drv_common.h>
#include <math.h>
#include <string.h>
#include "south_manage.h"
#include "sps.h"
#include "softbus.h"
#include "utils_server.h"
#include "south_dev_common.h"
#include "msg_id.h"
#include "para_id_in.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "para_manage.h"
#include "south_dev_modbus.h"

typedef int (*update_vfd_or_fan)(dev_inst_t* dev_inst, char num, char is_inter_fan);

static dxcb_south_msg_handle_process_t s_fan_msg_handle[] = {
    {NORTH_CTRL_SOUTH_FAN_MSG,          handle_exe_dest_cmd_msg},
};      

static msg_map fan_msg_map[] =
{
    {NORTH_CTRL_SOUTH_FAN_MSG,      msg_handle_nothing},
};

static dxcb_south_msg_handle_process_t s_vfd_msg_handle[] = {
    {NORTH_CTRL_SOUTH_VFD_MSG,          handle_exe_dest_cmd_msg},
};      

static msg_map vfd_msg_map[] =
{
    {NORTH_CTRL_SOUTH_VFD_MSG,      msg_handle_nothing},
};

static dev_comm_t* south_comm_thread_init(unsigned char dev_type_id);
static dev_comm_t* south_comm_thread_init(unsigned char dev_type_id) 
{
    dev_comm_t* dev_comm = NULL;
    dev_comm = init_dev_comm_info(dev_type_id);
    if (dev_comm == NULL)
        return NULL;

    return dev_comm;
}


int update_addr_by_brand(dev_comm_t* dev_comm, char brand_type)
{
    unsigned short inter_fan_num = 0;
    unsigned short exter_fan_num = 0;
    unsigned short vfd_num = 0;
    if(brand_type == SOUTH_FAN_TYPE)
    {
        get_one_para(DXCB_PARA_ID_INTERNAL_FAN_NUM_OFFSET, &inter_fan_num);
        get_one_para(DXCB_PARA_ID_EXTERNAL_FAN_NUM_OFFSET, &exter_fan_num);
        if(inter_fan_num + exter_fan_num > MAX_FAN_NUM)
        {
            LOG_E("fan num:%d is over max fan num\n", inter_fan_num + exter_fan_num);
            return FAILURE;
        }
        dev_comm->dev_mgr->dev_num = inter_fan_num + exter_fan_num;
        
        for(int loop = 0; loop < inter_fan_num; loop ++)
        {
            dev_comm->dev_inst[loop].dev_addr = loop + 1;
        }
        for(int loop = 0; loop < exter_fan_num; loop ++)
        {
            dev_comm->dev_inst[loop + inter_fan_num].dev_addr = loop + 9;
        }
    }
    else
    {
        get_one_para(DXCB_PARA_ID_COMPRESSORS_NUM_OFFSET, &vfd_num);
        if(vfd_num > MAX_VFD_NUM)
        {
            LOG_E("vfd num:%d is over max vfd num\n", vfd_num);
            return FAILURE;
        }
        dev_comm->dev_mgr->dev_num = get_pile_flag() == FALSE? vfd_num:vfd_num+1;
        for(int loop = 0; loop < vfd_num; loop ++)
        {
            dev_comm->dev_inst[loop].dev_addr = loop + 1;
        }
        if(get_pile_flag() == TRUE) { dev_comm->dev_inst[vfd_num].dev_addr = 5; }
    }
    return SUCCESSFUL;
}


void* init_south_comm(void * param)
{   
    server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(fan_msg_map) / sizeof(msg_map);
    register_server_msg_map(fan_msg_map, server_info);
    dev_comm_t* dev_comm = south_comm_thread_init(DEV_SOUTH_MODBUS);
    RETURN_VAL_IF_FAIL((dev_comm != NULL), NULL);

    start_dev_poll(dev_comm, NULL);
    rt_kprintf("FAN_HREAD|dev_comm:%p\n", dev_comm);
    return dev_comm;
}

void south_comm_thread(void *param) {
    dev_comm_t* dev_comm = (dev_comm_t*)param;
    link_inst_t* link_inst = dev_comm->dev_inst[0].dev_type->link_inst;
    init_modbusrtn_var();
    while (is_running(TRUE)) {
        // 先发送数据
        judge_fan_comm_fail_alarm(dev_comm);
        update_addr_by_brand(dev_comm, SOUTH_FAN_TYPE);
        update_protocol_by_brand(dev_comm, SOUTH_FAN_TYPE);
        if(dev_comm->dev_mgr->cmd_sta == CMD_FINISH){
            south_process_recv_msg(dev_comm);
            dev_comm->cmd_buf->rtn = 0;
            exe_next_cmd_id_version(dev_comm);
            rt_thread_mdelay(50);
        }
        // 接收命令应答数据
        if(rt_sem_take(&(link_inst->rx_sem), 10) == RT_EOK) {
            update_dev_mgr_info_id_version(dev_comm);
        }
    }
}


void south_process_recv_msg(dev_comm_t* dev_comm) 
{
    int i = 0;
    _rt_server_t south_server = _curr_server_get();
    if ((south_server == NULL) || (rt_sem_take(&south_server->msg_sem, 10) != RT_EOK) || (south_server->msg_node == RT_NULL))
    {
       return;
    }
    dxcb_south_msg_handle_process_t* south_msg_handle = NULL;
    char msg_num = 0;
    if(dev_comm->dev_inst->dev_type->id == DEV_SOUTH_MODBUS)
    {
        south_msg_handle = s_fan_msg_handle;
        msg_num = sizeof(s_fan_msg_handle) / sizeof(s_fan_msg_handle[0]);
    }
    else{
        south_msg_handle = s_vfd_msg_handle;
        msg_num = sizeof(s_vfd_msg_handle) / sizeof(s_vfd_msg_handle[0]);
    }
    _rt_msg_t curr_msg = south_server->msg_node;
    for (i = 0; i < msg_num; i++) {
        if (south_msg_handle[i].msg_id == curr_msg->msg.msg_id) {
            south_msg_handle[i].handle(dev_comm, curr_msg);
            break;
        }
    }

    rt_mutex_take(&south_server->mutex, RT_WAITING_FOREVER);
    south_server->msg_node = curr_msg->next;
    south_server->msg_count--;
    rt_mutex_release(&south_server->mutex);
    softbus_free(curr_msg);
    return;
}


void handle_exe_dest_cmd_msg(dev_comm_t* dev_comm, _rt_msg_t curr_msg)
{
    process_cmd_exe_msg(curr_msg->msg.data, dev_comm);
    return;
}


void* init_south2_comm(void * param)
{   
    server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(vfd_msg_map) / sizeof(msg_map);
    register_server_msg_map(vfd_msg_map, server_info);
    dev_comm_t* dev_comm = south_comm_thread_init(DEV_SOUTH2_MODBUS);
    RETURN_VAL_IF_FAIL((dev_comm != NULL), NULL);
    start_dev_poll(dev_comm, NULL);
    rt_kprintf("VFD_HREAD|dev_comm:%p\n", dev_comm);
    return dev_comm;
}

void south2_comm_thread(void *param) {
    dev_comm_t* dev_comm = (dev_comm_t*)param;
    link_inst_t* link_inst = dev_comm->dev_inst[0].dev_type->link_inst;
    // g_south_server = _curr_server_get();
    init_modbusrtn_var();
    while (is_running(TRUE)) {
        // 先发送数据
        judge_vfd_comm_fail_alarm(dev_comm);
        update_addr_by_brand(dev_comm, SOUTH_VFD_TYPE);
        update_protocol_by_brand(dev_comm, SOUTH_VFD_TYPE);
        if(dev_comm->dev_mgr->cmd_sta == CMD_FINISH){
            south_process_recv_msg(dev_comm);
            dev_comm->cmd_buf->rtn = 0;
            exe_next_cmd_id_version(dev_comm);
            rt_thread_mdelay(50);
        }
        // 接收命令应答数据
        if(rt_sem_take(&(link_inst->rx_sem), 10) == RT_EOK) {
            update_dev_mgr_info_id_version(dev_comm);
        }
    }
}



void judge_fan_comm_fail_alarm(dev_comm_t* dev_comm)
{
    int i = 0;
    unsigned short inter_fan_num = 0, exter_fan_num = 0;
    unsigned char alarm_state = 0, offset = 0;
    dev_inst_t* dev_inst = dev_comm->dev_inst;
    get_one_para(DXCB_PARA_ID_INTERNAL_FAN_NUM_OFFSET, &inter_fan_num);
    get_one_para(DXCB_PARA_ID_EXTERNAL_FAN_NUM_OFFSET, &exter_fan_num);

    // 内风机：1~8，外风机：9~16
    for(i = 0; i < inter_fan_num + exter_fan_num; i++)
    {
        if(dev_inst[i].state == STAT_NORMAL) {
            alarm_state = FALSE;
        } else {
            alarm_state = TRUE;
        }

        offset = i < inter_fan_num ? i : i - inter_fan_num + SIG_FAN_MAX_NUM;
        set_one_data(DCXB_TRACE_ID_FAN_COMM_FAIL_ALARM + offset, &alarm_state);
    }
}

void judge_vfd_comm_fail_alarm(dev_comm_t* dev_comm)
{
    int i = 0;
    unsigned short vfd_num = 0;
    unsigned char alarm_state = 0;
    dev_inst_t* dev_inst = dev_comm->dev_inst;
    get_one_para(DXCB_PARA_ID_COMPRESSORS_NUM_OFFSET, &vfd_num);
    for(i = 0; i < vfd_num; i++)
    {
        if(dev_inst[i].state == STAT_NORMAL) {
            alarm_state = FALSE;
        } else {
            alarm_state = TRUE;
        }

        set_one_data(DCXB_TRACE_ID_VFD_COMM_FAIL_ALARM + i, &alarm_state);
    }
}




void handle_vfd_or_fan(dev_inst_t* dev_inst, int num, int dev_type, int brand, char is_inter_fan) {
    // 变频器品牌处理函数映射表
    static const update_vfd_or_fan vfd_brand_handlers[] = {update_vfd_ams_dev_type, update_vfd_dfs_dev_type, update_vfd_hc_dev_type};
    static const update_vfd_or_fan fan_brand_handlers[] = {update_fan_slb_dev_type, update_fan_ebm_dev_type, update_fan_fsd_dev_type, update_fan_avc_dev_type};

    if(dev_type == SOUTH_FAN_TYPE)
    {
        if(num > MAX_FAN_NUM) return;
        fan_brand_handlers[brand](dev_inst, num, is_inter_fan);
    }
    else
    {
        if(num > MAX_VFD_NUM) return;
        vfd_brand_handlers[brand](dev_inst, num, FALSE);
    }
}

void update_south_mock(dev_inst_t* dev_inst, char* is_need_update)
{
    unsigned char mock_sta = get_pile_flag();
    static unsigned char s_last_mock_sta = FALSE;
    if(s_last_mock_sta != mock_sta)
    {
        if(mock_sta == TRUE)
        {
            update_south_mock_dev_type(dev_inst);
        }
        *is_need_update = TRUE;
        s_last_mock_sta = mock_sta;
    }
    return;
}

int update_protocol_by_brand(dev_comm_t* dev_comm, char dev_type) {
    unsigned char inter_fan_brand = 0, exter_fan_brand = 0;
    unsigned short inter_fan_num = 0, exter_fan_num = 0;
    unsigned char vfd_brand = 0;
    unsigned short vfd_num = 0;

    static unsigned char s_last_inter_fan_brand = 0, s_last_exter_fan_brand = 0;
    static unsigned short s_last_inter_fan_num = 0, s_last_exter_fan_num = 0;
    static unsigned char s_last_vfd_brand = 0;
    static unsigned short s_last_vfd_num = 0;

    char is_need_update = FALSE;

    if (!dev_comm || !dev_comm->dev_mgr || !dev_comm->dev_mgr->comm_dev) {
        return FAILURE;
    }
    dev_inst_t* dev_inst = dev_comm->dev_inst;
    if (dev_type == SOUTH_FAN_TYPE) {
        // 内风机配置
        get_one_para(DXCB_PARA_ID_FAN_BRAND_OFFSET, &inter_fan_brand);
        get_one_para(DXCB_PARA_ID_INTERNAL_FAN_NUM_OFFSET, &inter_fan_num);
        if(inter_fan_brand != s_last_inter_fan_brand || s_last_inter_fan_num != inter_fan_num)
        {
            for(int loop = 0; loop < inter_fan_num; loop ++)
            {
                handle_vfd_or_fan(dev_inst + loop, inter_fan_num, SOUTH_FAN_TYPE, inter_fan_brand, TRUE); 

            }
            s_last_inter_fan_brand = inter_fan_brand;
            s_last_inter_fan_num = inter_fan_num;
            is_need_update = TRUE;
        }
        // 外风机配置
        get_one_para(DXCB_PARA_ID_FAN_BRAND_OFFSET + 1, &exter_fan_brand);
        get_one_para(DXCB_PARA_ID_EXTERNAL_FAN_NUM_OFFSET, &exter_fan_num);
        
        if(exter_fan_brand != s_last_exter_fan_brand || exter_fan_num != s_last_exter_fan_num)
        {
            for(int loop = 0; loop < exter_fan_num; loop ++)
            {
                handle_vfd_or_fan(dev_inst + inter_fan_num + loop, exter_fan_num, SOUTH_FAN_TYPE, exter_fan_brand, FALSE); 
            }
            s_last_exter_fan_num = exter_fan_num;
            s_last_exter_fan_brand = exter_fan_brand;
            is_need_update = TRUE;
        }
    } else {
        get_one_para(DXCB_PARA_ID_FREQUENCY_CONVERTER_BRAND_OFFSET, &vfd_brand);
        get_one_para(DXCB_PARA_ID_COMPRESSORS_NUM_OFFSET, &vfd_num);
        if(s_last_vfd_brand != vfd_brand || s_last_vfd_num != vfd_num)
        {
            for(int loop = 0; loop < vfd_num; loop ++)
            {
                handle_vfd_or_fan(dev_inst + loop, vfd_num, SOUTH_VFD_TYPE, vfd_brand, FALSE);
            }
            s_last_vfd_brand = vfd_brand;
            s_last_vfd_num = vfd_num;
            is_need_update = TRUE;
        }
        update_south_mock(dev_inst + vfd_num, &is_need_update);
    }
    if(is_need_update)
    {
        rt_kprintf("update_protocol_by_brand|config_update\n");
        start_dev_poll(dev_comm, NULL);
    }
    return SUCCESSFUL;
}


