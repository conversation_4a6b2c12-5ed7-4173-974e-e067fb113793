/**************************************************************************
* 文件名称：SearchHisdata.c
* 文件说明：获取特定时间段历史记录的搜索算法
***************************************************************************/
#include "SearchHisdata.h"
#include "hisdata.h"

static void GetNumTimeFromBlock(T_TimeStruct tBlockTime, numTime *pTime1, numTime *pTime2);

/***************************************************************************
 * @brief    提取时间段
 * @param    {T_TimeStruct} *ptStartTime---起始时间
 * @param    {T_TimeStruct} *ptEndTime---终止时间
 * @param    {BYTE} *pBuff---接收缓冲区
 **************************************************************************/
void ExtractTimeSlot(T_TimeStruct *ptStartTime, T_TimeStruct *ptEndTime, BYTE *pBuff)
{
    ExtractTimeStructFromBuff(ptStartTime, pBuff);
    ExtractTimeStructFromBuff(ptEndTime, pBuff + 7);

    return;
}

/***************************************************************************
 * @brief    从struct tm time中提取blocktime
 **************************************************************************/
void TmTime2TimeStruct(T_TimeStruct *ptBlockTime, struct tm tTime)
{
    ptBlockTime->wYear = tTime.tm_year + 1900;
    ptBlockTime->ucMonth = tTime.tm_mon + 1;
    ptBlockTime->ucDay = tTime.tm_mday;
    ptBlockTime->ucHour = tTime.tm_hour;
    ptBlockTime->ucMinute = tTime.tm_min;
    ptBlockTime->ucSecond = tTime.tm_sec;
    return;
}

/***************************************************************************
 * @brief    从databuff中提取blocktime
 **************************************************************************/
void ExtractTimeStructFromBuff(T_TimeStruct *ptBlockTime, BYTE *pBuff)
{
    ptBlockTime->wYear = (WORD)GetInt16Data(pBuff);
    ptBlockTime->ucMonth = pBuff[2];
    ptBlockTime->ucDay = pBuff[3];
    ptBlockTime->ucHour = pBuff[4];
    ptBlockTime->ucMinute = pBuff[5];
    ptBlockTime->ucSecond = pBuff[6];
    return;
}

/***************************************************************************
 * @brief    将T_TimeStruct时间转为数值型的时间
 * @param    {T_TimeStruct} tBlockTime---待转换时间
 * @return   数值型时间
 **************************************************************************/
static void GetNumTimeFromBlock(T_TimeStruct tBlockTime, numTime *pTime1, numTime *pTime2)
{
    *pTime1 = tBlockTime.ucDay + tBlockTime.ucMonth * 100 + tBlockTime.wYear * 10000;
    *pTime2 = tBlockTime.ucSecond + tBlockTime.ucMinute * 100 + tBlockTime.ucHour * 10000;
    return;
}

/***************************************************************************
 * @brief    比较两时间的先后
 * @param    {T_TimeStruct} tTime---待比较时间
 * @param    {T_TimeStruct} tTargetTime---目标时间
 * @return   待比较时间 < 目标时间:TIME_LESS_THAN_TARGET
 *           待比较时间 = 目标时间:TIME_EQUAL_TARGET
 *           待比较时间 > 目标时间:TIME_GREATER_THAN_TARGET
 **************************************************************************/
TimeCompareResult CompareTime(T_TimeStruct tTime, T_TimeStruct tTargetTime)
{
    numTime uTime1, uTime2 = 0;
    numTime uTargetTime1, uTargetTime2 = 0;

    GetNumTimeFromBlock(tTime, &uTime1, &uTime2);
    GetNumTimeFromBlock(tTargetTime, &uTargetTime1, &uTargetTime2);

    if (uTime1 > uTargetTime1)
    {
        return TIME_GREATER_THAN_TARGET;
    }
    else if (uTime1 < uTargetTime1)
    {
        return TIME_LESS_THAN_TARGET;
    }
    else
    {
        if (uTime2 > uTargetTime2)
        {
            return TIME_GREATER_THAN_TARGET;
        }
        else if (uTime2 < uTargetTime2)
        {
            return TIME_LESS_THAN_TARGET;
        }
        else
        {
            return TIME_EQUAL_TARGET;
        }
    }
}

/***************************************************************************
 * @brief    获取特定时间段历史记录条数
 * @param    {BYTE} ucHisRecordType---历史记录类型
 * @param    {T_TimeStruct} *ptStartTime---起始时间
 * @param    {T_TimeStruct} *ptEndTime---终止时间
 * @return   历史记录条数
 **************************************************************************/
WORD GetHisSaveNum(BYTE ucHisRecordType, T_TimeStruct *ptStartTime, T_TimeStruct *ptEndTime)
{
    T_TimeStruct atBlockTime[13] = {0, };         // 分块数组
    BYTE ucBlockNum = 0;                 // 记录实际分成了多少块
    WORD wStartIndex = 0, wEndIndex = 0; // 历史记录序号
    BYTE ucStartTimeRtn = 0, ucEndTimeRtn = 0;
    T_TimeStruct tStartTime, tEndTime;

    rt_memcpy(&tStartTime, ptStartTime, sizeof(T_TimeStruct));
    rt_memcpy(&tEndTime, ptEndTime, sizeof(T_TimeStruct));

    /* 如果起始、终止时间段均为0，则返回全部条数 */
    if (IfTimeAllZero(tStartTime, tEndTime))
    {
        wStartIndex = 0;
        if (ucHisRecordType >= HISFILE_NUM)
        {
            return 0;
        }
        switch (ucHisRecordType)
        {
        case HISTORICAL_ACTION:
            wEndIndex = GetHisOperationTotNum() - 1;
            break;
        case HISTORICAL_ALARM:
            wEndIndex = GetHisAlarmTotNum() - 1;
            break;
        case HISTORICAL_DATA:
            wEndIndex = GetHisDataTotNum() - 1;
            break;
        case DCR_RECORD:
            wEndIndex = GetDcrRecordTotNum() - 1;
        default:
            break;
        }
    }
    else
    {
        DivideRecordToBlock(atBlockTime, &ucBlockNum, ucHisRecordType);
        if (ucBlockNum == 0) // 无分块，则表示没有历史记录
        {
            return 0;
        }
        if (ucBlockNum == 1) // 只有一个分块，则表示只有一条历史记录
        {
            return 1;
        }
        ucStartTimeRtn = GetStartTimePose(atBlockTime, ucBlockNum, tStartTime, &wStartIndex, ucHisRecordType);
        ucEndTimeRtn = GetEndTimePose(atBlockTime, ucBlockNum, tEndTime, &wEndIndex, ucHisRecordType);
        if ((ucStartTimeRtn == TIME_INVALID) || (ucEndTimeRtn == TIME_INVALID))
        {
            return 0;
        }
    }

    return (wEndIndex - wStartIndex + 1);
}

/***************************************************************************
 * @brief    判断起始时间、终止时间是否全为0
 * @param    {T_TimeStruct} tStartTime---起始时间
 * @param    {T_TimeStruct} tEndTime---终止时间
 * @return   True---是 False---否
 **************************************************************************/
BOOLEAN IfTimeAllZero(T_TimeStruct tStartTime, T_TimeStruct tEndTime)
{
    T_TimeStruct tTimeZero;

    rt_memset(&tTimeZero, 0, sizeof(T_TimeStruct));

    if (CompareTime(tStartTime, tTimeZero) == TIME_EQUAL_TARGET &&
        CompareTime(tEndTime, tTimeZero) == TIME_EQUAL_TARGET)
    {
        return True;
    }

    return False;
}

/***************************************************************************
 * @brief    定位待查找时间所在的分块区间，若精准查到则传递记录序号，若模糊查到则传递区间范围
 * @param    {T_TimeStruct} *atBlock---分块数组
 * @param    {BYTE} ucBlockNum---时间分段数
 * @param    {T_TimeStruct} tInputTime---待查找的时间
 * @param    {BYTE} *IndexZone---模糊查找时的最终定位时间范围
 * @param    {WORD} *pwIndex---历史记录序号
 * @return   查找到具体时间:FIND_TIME_PRECISE-0
             查找到时间范围:FIND_TIME_FUZZY-1
             查找到时间无效:FIND_TIME_INVALID-2
 **************************************************************************/
BYTE FindTimeInBlock(T_TimeStruct *atBlock, BYTE ucBlockNum, T_TimeStruct tInputTime, BYTE *IndexZone, WORD *pwIndex)
{
    BYTE ucBlockIndexMin = 0;
    BYTE ucBlockIndexMax = ucBlockNum - 1;
    BYTE ucBlockIndexTmp = 0;

    while (ucBlockIndexMin < ucBlockIndexMax)
    {
        if (ucBlockIndexMax - ucBlockIndexMin == 1)
        {
            if (CompareTime(atBlock[ucBlockIndexMin], tInputTime) == TIME_EQUAL_TARGET)
            {
                *pwIndex = ucBlockIndexMin * 1000;
                return FIND_TIME_PRECISE;
            }
            else if (CompareTime(atBlock[ucBlockIndexMax], tInputTime) == TIME_EQUAL_TARGET)
            {
                *pwIndex = (ucBlockIndexMax == ucBlockNum - 1) ? (GetHisDataTotNum() - 1) : ucBlockIndexMax * 1000;
                return FIND_TIME_PRECISE;
            }
            else
            {
                IndexZone[0] = ucBlockIndexMin;
                IndexZone[1] = ucBlockIndexMax;
                return FIND_TIME_FUZZY;
            }
        }

        ucBlockIndexTmp = (ucBlockIndexMin + ucBlockIndexMax) / 2;
        if (CompareTime(atBlock[ucBlockIndexTmp], tInputTime) == TIME_EQUAL_TARGET)
        {
            *pwIndex = ucBlockIndexTmp * 1000;
            return FIND_TIME_PRECISE;
        }
        else if (CompareTime(atBlock[ucBlockIndexTmp], tInputTime) == TIME_GREATER_THAN_TARGET)
        {
            ucBlockIndexMax = ucBlockIndexTmp;
        }
        else
        {
            ucBlockIndexMin = ucBlockIndexTmp;
        }
    }

    return FIND_TIME_INVALID;
}


