#include "MdbsRtu.h"
#include "SNMPData_in.h"
#include "sample.h"
#include "realAlarm.h"
#include "rtthread.h"

#include <stddef.h>
#include "lwip_prvmib_in.h"

#define TOTAL_BATT_NUM 32
#define STRCMP(str1, str2) (strcmp(str1, str2) == 0 ? 0 : 1)
#define TYPE_NAME_TO_STRING(type_name) #type_name

#define X_SNMP_SYNC_ITEM(_1, _2, _3, data_type, data_num, precision, sync_flag, struct_type, struct_member, _10) X_SYNC_ITEM(data_type, data_num, precision, sync_flag, struct_type, struct_member)

const T_SnmpRealDataStruct s_atSnmpRealData[] = {
#define X_SYNC_ITEM(data_type, data_num, precision, sync_flag, struct_type, struct_member) {offsetof(struct_type, struct_member), data_type, precision},
    X_SNMP_SYNC_LIST_REALDATA
#undef X_SYNC_ITEM
};

const T_SnmpRealAlarmStruct s_atSnmpRealAlarm[] = {
#define X_SYNC_ITEM(data_type, data_num, precision, sync_flag, struct_type, struct_member) {offsetof(struct_type, struct_member), data_num},
    X_SNMP_SYNC_LIST_ALARM
#undef X_SYNC_ITEM
};

const T_SnmpSyncParaStruct s_atSnmpSyncPara[] = {
#define X_SYNC_ITEM(data_type, data_num, precision, sync_flag, struct_type, struct_member) {offsetof(struct_type, struct_member), data_type, precision, STRCMP(TYPE_NAME_TO_STRING(struct_type), "T_SysPara")},
    X_SNMP_SYNC_LIST_PARA
#undef X_SYNC_ITEM
};

const T_SnmpSyncParaStrStruct s_atSnmpSyncParaStr[] = {
#define X_SYNC_ITEM(data_type, data_num, precision, sync_flag, struct_type, struct_member) {offsetof(struct_type, struct_member), data_type, data_num, sync_flag, STRCMP(TYPE_NAME_TO_STRING(struct_type), "T_SysPara")},
    X_SNMP_SYNC_LIST_OTHERINFO
#undef X_SYNC_ITEM
};

const T_SnmpFactInfoStruct s_atSnmpFactInfo[] = {
#define X_SYNC_ITEM(data_type, data_num, precision, sync_flag, struct_type, struct_member) {offsetof(struct_type, struct_member), data_num},
    X_SNMP_SYNC_LIST_FACTINFO
#undef X_SYNC_ITEM
};

/* NOTE: 必须放在最后，以避免宏重定义 */
const T_SnmpCtrlCmd s_atSnmpCtrlCmd[] = {
#undef X_SNMP_SYNC_ITEM
#define X_SNMP_SYNC_ITEM(id, asn1_type, access, data_type, data_num, precision, sync_flag, undefined_1, func, comment) {id, func}, 
    X_SNMP_SYNC_LIST_CTRL_SCALAR
    X_SNMP_SYNC_LIST_CTRL_TABLE
};

const T_SnmpRealDataStruct  *GetSnmpRealDataPoint(void)
{
    return s_atSnmpRealData;
}

const T_SnmpRealAlarmStruct  *GetSnmpRealAlarmPoint(void)
{
    return s_atSnmpRealAlarm;
}

const T_SnmpSyncParaStruct  *GetSnmpParaPoint(void)
{
    return s_atSnmpSyncPara;
}

const T_SnmpSyncParaStrStruct  *GetSnmpParaStrPoint(void)
{
    return s_atSnmpSyncParaStr;
}

const T_SnmpFactInfoStruct  *GetSnmpFactInfoPoint(void)
{
    return s_atSnmpFactInfo;
}

const T_SnmpCtrlCmd  *GetSnmpCtrlCmdPoint(void)
{
    return s_atSnmpCtrlCmd;
}