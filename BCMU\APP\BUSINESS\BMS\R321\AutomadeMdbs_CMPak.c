
/**************************************************************************
* 版权信息：（C）2011-2018，中兴通讯股份有限公司动力开发部版权所有
* 系统名称：MODBUS RTU协议代码
* 文件名称：AutoMdbsCode.c
* 文件说明：自动生成带modbus寄存器值及相关属性的数组
* 作    者：fsg
* 版本信息：V1.0
* 设计日期：2018-8-20
* 修改记录：
* 日    期      版  本      修改人      修改摘要
*
* 其他说明：
***************************************************************************/
#include "stddef.h"
#include "common.h"
#include "hisdata.h"
#include "sample.h"
#include "realAlarm.h"
#include "MdbsRtu.h"

/****************************变量声明********************************/
#ifdef PAKISTAN_CMPAK_PROTOCOL
const T_OneDataAttrStruct s_atAscDataAttr[] = {
    {0x03,0,0,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,fBusbarVolt),1,DATA_TYPE_INT16U,2,2,NULL,},
    {0x03,0,1,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,fBattVolt),1,DATA_TYPE_INT16U,2,2,NULL,},
    {0x03,0,2,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,fBattCurr),1,DATA_TYPE_INT16S,2,2,NULL,},
    {0x03,0,3,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,wBattSOC),1,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,4,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,wBattSOH),1,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,5,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,fMaxCellTemp),1,DATA_TYPE_INT16S,0,2,NULL,},
    {0x03,0,6,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,fMinCellTemp),1,DATA_TYPE_INT16S,0,2,NULL,},
    {0x03,0,7,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awReserved2[0]),7,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,8,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awReserved2[1]),7,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,9,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awReserved2[2]),7,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,10,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awReserved2[3]),7,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,11,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awReserved2[4]),7,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,12,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awReserved2[5]),7,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,13,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awReserved2[6]),7,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,14,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,wModuleMode),1,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,15,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awReserved3[0]),3,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,16,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awReserved3[1]),3,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,17,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awReserved3[2]),3,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,18,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellTemp[0]),16,DATA_TYPE_INT16S,0,2,NULL,},
    {0x03,0,19,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellTemp[1]),16,DATA_TYPE_INT16S,0,2,NULL,},
    {0x03,0,20,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellTemp[2]),16,DATA_TYPE_INT16S,0,2,NULL,},
    {0x03,0,21,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellTemp[3]),16,DATA_TYPE_INT16S,0,2,NULL,},
    {0x03,0,22,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellTemp[4]),16,DATA_TYPE_INT16S,0,2,NULL,},
    {0x03,0,23,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellTemp[5]),16,DATA_TYPE_INT16S,0,2,NULL,},
    {0x03,0,24,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellTemp[6]),16,DATA_TYPE_INT16S,0,2,NULL,},
    {0x03,0,25,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellTemp[7]),16,DATA_TYPE_INT16S,0,2,NULL,},
    {0x03,0,26,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellTemp[8]),16,DATA_TYPE_INT16S,0,2,NULL,},
    {0x03,0,27,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellTemp[9]),16,DATA_TYPE_INT16S,0,2,NULL,},
    {0x03,0,28,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellTemp[10]),16,DATA_TYPE_INT16S,0,2,NULL,},
    {0x03,0,29,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellTemp[11]),16,DATA_TYPE_INT16S,0,2,NULL,},
    {0x03,0,30,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellTemp[12]),16,DATA_TYPE_INT16S,0,2,NULL,},
    {0x03,0,31,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellTemp[13]),16,DATA_TYPE_INT16S,0,2,NULL,},
    {0x03,0,32,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellTemp[14]),16,DATA_TYPE_INT16S,0,2,NULL,},
    {0x03,0,33,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellTemp[15]),16,DATA_TYPE_INT16S,0,2,NULL,},
    {0x03,0,34,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellVolt[0]),16,DATA_TYPE_INT16U,3,2,NULL,},
    {0x03,0,35,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellVolt[1]),16,DATA_TYPE_INT16U,3,2,NULL,},
    {0x03,0,36,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellVolt[2]),16,DATA_TYPE_INT16U,3,2,NULL,},
    {0x03,0,37,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellVolt[3]),16,DATA_TYPE_INT16U,3,2,NULL,},
    {0x03,0,38,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellVolt[4]),16,DATA_TYPE_INT16U,3,2,NULL,},
    {0x03,0,39,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellVolt[5]),16,DATA_TYPE_INT16U,3,2,NULL,},
    {0x03,0,40,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellVolt[6]),16,DATA_TYPE_INT16U,3,2,NULL,},
    {0x03,0,41,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellVolt[7]),16,DATA_TYPE_INT16U,3,2,NULL,},
    {0x03,0,42,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellVolt[8]),16,DATA_TYPE_INT16U,3,2,NULL,},
    {0x03,0,43,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellVolt[9]),16,DATA_TYPE_INT16U,3,2,NULL,},
    {0x03,0,44,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellVolt[10]),16,DATA_TYPE_INT16U,3,2,NULL,},
    {0x03,0,45,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellVolt[11]),16,DATA_TYPE_INT16U,3,2,NULL,},
    {0x03,0,46,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellVolt[12]),16,DATA_TYPE_INT16U,3,2,NULL,},
    {0x03,0,47,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellVolt[13]),16,DATA_TYPE_INT16U,3,2,NULL,},
    {0x03,0,48,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellVolt[14]),16,DATA_TYPE_INT16U,3,2,NULL,},
    {0x03,0,49,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellVolt[15]),16,DATA_TYPE_INT16U,3,2,NULL,},
    {0x03,0,50,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awReserved4[0]),16,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,51,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awReserved4[1]),16,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,52,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awReserved4[2]),16,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,53,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awReserved4[3]),16,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,54,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awReserved4[4]),16,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,55,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awReserved4[5]),16,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,56,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awReserved4[6]),16,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,57,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awReserved4[7]),16,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,58,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awReserved4[8]),16,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,59,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awReserved4[9]),16,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,60,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awReserved4[10]),16,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,61,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awReserved4[11]),16,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,62,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awReserved4[12]),16,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,63,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awReserved4[13]),16,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,64,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awReserved4[14]),16,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,65,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awReserved4[15]),16,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,66,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,wDischgTimesHigh),1,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,67,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,wDischgTimesLow),1,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,68,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,wTotalDischgCapHigh),1,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,69,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,wTotalDischgCapLow),1,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,70,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,wCriticalAlarmStatus1),1,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,71,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,wCriticalAlarmStatus2),1,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,72,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,wMajorAlarmStatus),1,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,73,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,wMinorAlarmStatus),1,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,74,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,wModuleAlarmStatus),1,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,257,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,wVersion),1,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,258,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,wHardwareVer),1,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,259,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,wBootLoaderVer),1,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,260,DATA_TYPE_INT8U,1,BCM_PART_DATA,offsetof(T_CmpakStruct,ucDeviceType),1,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,261,DATA_TYPE_INT8U,1,BCM_PART_DATA,offsetof(T_CmpakStruct,ucManufature),1,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,262,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,wSubSoftwareID),1,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,263,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,wBatteryCap),1,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,271,DATA_TYPE_INT8U,1,BCM_PART_DATA,offsetof(T_CmpakStruct,ucCellNum),1,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,516,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,wOutputPower),1,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,517,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,fExtBusbarVolt),1,DATA_TYPE_INT16U,2,2,NULL,},
    {0x03,0,521,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,wAccuBattRunTimeHigh),1,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,522,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,wAccuBattRunTimeLow),1,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,768,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellTempAdd[0]),8,DATA_TYPE_INT16S,0,2,NULL,},
    {0x03,0,769,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellTempAdd[1]),8,DATA_TYPE_INT16S,0,2,NULL,},
    {0x03,0,770,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellTempAdd[2]),8,DATA_TYPE_INT16S,0,2,NULL,},
    {0x03,0,771,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellTempAdd[3]),8,DATA_TYPE_INT16S,0,2,NULL,},
    {0x03,0,772,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellTempAdd[4]),8,DATA_TYPE_INT16S,0,2,NULL,},
    {0x03,0,773,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellTempAdd[5]),8,DATA_TYPE_INT16S,0,2,NULL,},
    {0x03,0,774,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellTempAdd[6]),8,DATA_TYPE_INT16S,0,2,NULL,},
    {0x03,0,775,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellTempAdd[7]),8,DATA_TYPE_INT16S,0,2,NULL,},
    {0x03,0,784,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellVoltAdd[0]),8,DATA_TYPE_INT16U,3,2,NULL,},
    {0x03,0,785,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellVoltAdd[1]),8,DATA_TYPE_INT16U,3,2,NULL,},
    {0x03,0,786,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellVoltAdd[2]),8,DATA_TYPE_INT16U,3,2,NULL,},
    {0x03,0,787,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellVoltAdd[3]),8,DATA_TYPE_INT16U,3,2,NULL,},
    {0x03,0,788,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellVoltAdd[4]),8,DATA_TYPE_INT16U,3,2,NULL,},
    {0x03,0,789,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellVoltAdd[5]),8,DATA_TYPE_INT16U,3,2,NULL,},
    {0x03,0,790,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellVoltAdd[6]),8,DATA_TYPE_INT16U,3,2,NULL,},
    {0x03,0,791,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_CmpakStruct,afCellVoltAdd[7]),8,DATA_TYPE_INT16U,3,2,NULL,},
    {0x03,0,800,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,wCriticalAlarmStatus3),1,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,818,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awRepBattModel[0]),12,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,819,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awRepBattModel[1]),12,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,820,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awRepBattModel[2]),12,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,821,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awRepBattModel[3]),12,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,822,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awRepBattModel[4]),12,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,823,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awRepBattModel[5]),12,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,824,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awRepBattModel[6]),12,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,825,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awRepBattModel[7]),12,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,826,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awRepBattModel[8]),12,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,827,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awRepBattModel[9]),12,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,828,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awRepBattModel[10]),12,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,829,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,awRepBattModel[11]),12,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,834,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,wDischgWHHigh),1,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,835,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,wDischgWHLow),1,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0x10,4096,DATA_TYPE_INT16U,2,CMPAK_TIME_PART,offsetof(T_TimeStruct,wYear),1,DATA_TYPE_INT16U,0,2,DealTimeSet,},
    {0x03,0x10,4097,DATA_TYPE_INT8U,1,CMPAK_TIME_PART,offsetof(T_TimeStruct,ucMonth),1,DATA_TYPE_INT16U,0,2,DealTimeSet,},
    {0x03,0x10,4098,DATA_TYPE_INT8U,1,CMPAK_TIME_PART,offsetof(T_TimeStruct,ucDay),1,DATA_TYPE_INT16U,0,2,DealTimeSet,},
    {0x03,0x10,4099,DATA_TYPE_INT8U,1,CMPAK_TIME_PART,offsetof(T_TimeStruct,ucHour),1,DATA_TYPE_INT16U,0,2,DealTimeSet,},
    {0x03,0x10,4100,DATA_TYPE_INT8U,1,CMPAK_TIME_PART,offsetof(T_TimeStruct,ucMinute),1,DATA_TYPE_INT16U,0,2,DealTimeSet,},
    {0x03,0x10,4101,DATA_TYPE_INT8U,1,CMPAK_TIME_PART,offsetof(T_TimeStruct,ucSecond),1,DATA_TYPE_INT16U,0,2,DealTimeSet,},
    {0x03,0,32107,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,wBattCycTimeHigh),1,DATA_TYPE_INT16U,0,2,NULL,},
    {0x03,0,32108,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakStruct,wBattCycTimeLow),1,DATA_TYPE_INT16U,0,2,NULL,},
};
/********************************************************************/

/***************************************************************************
* 函数名称：GetDataAttrPoint()
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：
* 作    者：fsg
* 设计日期：2010-01-24
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
const T_OneDataAttrStruct  *GetDataAttrPoint(void)
{
    /*if(NULL == s_atAscDataAttr)
    {
        return NULL;
    }*/
    return s_atAscDataAttr;
}

/***************************************************************************
* 函数名称：GetDataAttrNum()
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：
* 作    者：fsg
* 设计日期：2010-01-24
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
INT32  GetDataAttrNum(void)
{
    /*if(NULL == s_atAscDataAttr)
    {
        return APP_FAILURE;
    }*/
    return sizeof(s_atAscDataAttr) / sizeof(s_atAscDataAttr[0]);
}
#endif

