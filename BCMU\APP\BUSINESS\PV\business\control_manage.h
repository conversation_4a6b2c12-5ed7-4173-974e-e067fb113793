/**
 * @file     control_manage.h
 * @brief    控制管理
 * @details  This is the detail description.
 * <AUTHOR> 
 * @date     2023-01-29
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation 
 * @par History:
 *   version: author, date, descn
 */ 
#ifndef _CONTROL_MANAGE_H
#define _CONTROL_MANAGE_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus
#include "do_ctrl.h"
#include "device_type.h"
#include "utils_server.h"

#define OUT_RELAY_NO   1   // 第一个是输出干接点
#define SEC_COUNT      50  // 在ctrl模块中计数几次是1s

void* init_ctrl_manage(void * param);
int ctrl_manage_init(void *param);
void ctrl_manage_main(void *param);
int in_relay_deal(void);
int alarm_trig_relay(do_msg_t* do_msg);
void ctrl_msg_process(void);
int set_out_relay_status(do_msg_t* do_msg);
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _CONTROL_MANAGE_H