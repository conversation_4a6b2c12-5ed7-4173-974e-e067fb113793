/*******************************************************************************
  * @file        update_manage.c
  * @version     v1.0.0
  * @copyright   COPYRIGHT &copy; 2023 CSG
  * <AUTHOR>
  * @date        2023-4-6
  * @brief
  * @attention
  * Modification History
  * DATE         DESCRIPTION
  * ------------------------
  * - 2023-4-6  dongmengling Created
*******************************************************************************/
#include <string.h>
#include <rtthread.h>
#include <rtdevice.h>
#include <stdlib.h>
#include <stddef.h>
#include "update_manage.h"
#include "device_type.h"
#include "sps.h"
#include "cmd.h"
#include "sample.h"
#include "protocol_layer.h"
#include "utils_data_transmission.h"
#include "utils_rtthread_security_func.h"
#include "flash.h"
#include "gd32c10x_fmc.h"
#include "realdata_id_in.h"
#include "device_common.h"
#include "eeprom_storage.h"
#include "dev_airswitch.h"
#include "realdata_save.h"
#include "para_id_in.h"
#include "para_manage.h"

Static int parse_update_trig_old(void* dev_inst, void *cmd_buf);
Static int pack_update_trig_old(void* dev_inst, void *cmd_buf);
Static int get_app_download_rtn(void *cmd_buf);
Static int save_upgrade_file_info( short update_mode);

Static T_FileManageStruct  ptFileManage;

// extern int board_addr;
#ifdef UNITEST
unsigned char dest_dev;
#else
extern unsigned char dest_dev;
#endif

static char s_ucSysName[FILE_NAME_LEN] = {0};
Static trig_ctr_inf_t s_trig_ctr_inf;

Static cmd_handle_register_t update_cmd_handle[] = {
    {DEV_CSU, AIRSWITCH_UPDATE_TRIG, CMD_TYPE_NO_POLL, parse_update_trig_old, pack_update_trig_old},
};

Static int get_app_download_rtn(void *cmd_buf) {
    bottom_comm_cmd_head_t* proto_head = NULL;
    cmd_buf_t* trig_cmd_buf = (cmd_buf_t*)cmd_buf;
    unsigned char dev_type = 0;

    // 入参校验
    if ( trig_cmd_buf == NULL || trig_cmd_buf->buf == NULL) {
        return FAILURE;
    }

    get_one_data(SWITCH_DATA_ID_SYSTEM_NAME, &s_ucSysName);
    rt_strncat_s(s_ucSysName, 
        sizeof(s_ucSysName), 
        "_sw.bin", 
        sizeof(s_ucSysName) - rt_strnlen_s(s_ucSysName, sizeof(s_ucSysName)) - 1);
    proto_head = (bottom_comm_cmd_head_t*)trig_cmd_buf->cmd->req_head;
    dev_type = dest_dev;
    char *p_file_name = s_trig_ctr_inf.file_name;
    unsigned char *p = trig_cmd_buf->buf;

    if (proto_head->append != 0xAA55)
    {
        return DownloadFrameErr;
    }

    if (trig_cmd_buf->data_len == UPDATE_FILE_NAME_LEN)
    {
        switch(dev_type) {
            case BOTTOM_AIRSWITCH_TYPE:
                rt_snprintf_s(p_file_name, UPDATE_FILE_NAME_LEN, "%s", s_ucSysName);
                if (rt_strncmp((char*)p, s_ucSysName, UPDATE_FILE_NAME_LEN) == 0) {
                    rt_memcpy_s(ptFileManage.tFileAttr.acFileName, sizeof(ptFileManage.tFileAttr.acFileName),(char *)s_ucSysName, UPDATE_FILE_NAME_LEN);
                    return BOTTOM_RTN_APP_CORRECT;
                }
                break;
        }
    }
    return DownFileNameErr;
}


Static int parse_update_trig_old(void* dev_inst, void *cmd_buf) {

    s_trig_ctr_inf.rtn = get_app_download_rtn(cmd_buf);
    
    return SUCCESSFUL;
}

Static int pack_update_trig_old(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* trig_cmd_buf = (cmd_buf_t*)cmd_buf;
    
    // 入参校验
    if ( trig_cmd_buf == NULL || trig_cmd_buf->buf == NULL) {
        return FAILURE;
    }

    if(s_trig_ctr_inf.rtn == DownloadFrameErr) {
        //附加码错误
        trig_cmd_buf->data_len = 0;
        trig_cmd_buf->rtn = BOTTOM_RTN_CMD_ERROR;
        return SUCCESSFUL;
    }
    else if(s_trig_ctr_inf.rtn == DownFileNameErr) {
        trig_cmd_buf->data_len = UPDATE_FILE_NAME_LEN;
        rt_memset_s(trig_cmd_buf->buf, trig_cmd_buf->data_len,0x00, trig_cmd_buf->data_len);
        rt_memcpy_s(trig_cmd_buf->buf, UPDATE_FILE_NAME_LEN,s_ucSysName, UPDATE_FILE_NAME_LEN);

        return SUCCESSFUL;
    }
    save_power_energy();
    fmc_erase_pages(UPDATEINFO_STATRT);
    begin_download(FLAG_IAP);

    return SUCCESSFUL;
}


void update_manage_init(void) {
    short i = 0;
    for(i = 0; i < sizeof(update_cmd_handle)/sizeof(update_cmd_handle[0]); i++) {
        register_cmd_handle(&update_cmd_handle[i]);
    }
    fmc_read_data(UPDATEINFO_STATRT,(uint8_t*)&ptFileManage, sizeof(T_FileManageStruct));
}

int begin_download( short update_mode )
{
    if (ptFileManage.ucFlag == FLAG_IAP)   //正在进行在线升级
    {
        return TRUE;
    }

    if (save_upgrade_file_info(update_mode))
    {
        return TRUE;
    }

    NVIC_SystemReset();
    return FALSE;
}

Static int  save_upgrade_file_info( short update_mode)
{
    uint8_t *pFlag = (uint8_t*)FLAG_PROGRAM;
    uint8_t aucBuffUpdateFlag[32] ={0,};
    
    if (update_mode == FLAG_BACKUP )
    {
        if (*pFlag==0xFF)   //烧写后未备份
        {
            ptFileManage.tFileAttr.wTotalFrameNum = MAX_PACKET_NUM;
            fmc_program(FLAG_PROGRAM - GD32C103_FLASH_BASE, (unsigned char *)aucBuffUpdateFlag, 32);          
        }
        else if(ptFileManage.ucFlag != FLAG_APP)
        {
            return TRUE;
        }
    }
    ptFileManage.ucUpdateAddr = get_host_addr();
    ptFileManage.ucFlag = update_mode;
    ptFileManage.wCrc = crc_cal((unsigned char *)&ptFileManage, offsetof(T_FileManageStruct,wCrc));
    fmc_erase_pages(UPDATEINFO_STATRT);
    fmc_program(UPDATEINFO_STATRT, (unsigned char *)&ptFileManage, sizeof(T_FileManageStruct));

    return FALSE;
}

