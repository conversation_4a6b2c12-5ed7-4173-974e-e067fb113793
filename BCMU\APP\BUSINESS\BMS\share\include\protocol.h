/**************************************************************************
 * Copyright (C) 2001, ZTE Corporation.
 * 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
 * 系统名称：ZXDU18 CTL板软件
 * 文件名称：protocol.h
 * 文件说明：协议模块头文件
 * 作    者：王威
 * 版本信息：V1.0
 * 设计日期：2010-06-13
 * 修改记录：
 * 日    期      版  本      修改人      修改摘要
 * 其他说明：
 ***************************************************************************/
#ifndef SOFTWARE_SRC_APP_PROTOCOL_H_
#define SOFTWARE_SRC_APP_PROTOCOL_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include "commBdu.h"

/***********************  常量定义  ************************/
/*****************   后台通讯 包头、包尾   *****************/
#define SOI 0x7E
#define EOI 0x0D

#define YD1363_INFO_LEN_MAX  2047
#define YD1363_FRAME_LEN_MIN 18

/***************网口通信状态（单播/广播)****************************/ 
#define NET_COMM_BROADCAST     0x00
#define NET_COMM_SINGLE        0x01

/************************  返回码   ************************/
#define RTN_CORRECT 0x00       // 接收数据正确
#define RTN_WRONG_VER 0x01     // 版本号错
#define RTN_WRONG_CHKSUM 0x02  // 和校验错
#define RTN_WRONG_LCHKSUM 0x03 // 长度校验错
#define RTN_WRONG_CID2 0x04    // CID2错
#define RTN_WRONG_COMMAND 0x05 // 命令格式错
#define RTN_INVALID_DATA 0x06  // 无效数据
#define RTN_NO_DATA 0x07       // 前台无相应数据
#define RTN_FAIL_COMMAND 0xE2  // 命令执行失败
#define RTN_WRONG_CID1 0xE1    // CID1错
#define NO_RETURN 0xFF         // 不用返回响应包

/************************   CID1码   ***********************/
#define CID1_BMS 0x4A    // 铁锂电池检测装置
#define CID1_COMMON 0x40 // 公共
#define CID1_DC 0x42     // 直流配电
#define CID1_1104 0x46   // 1104协议
#define CID1_PAD 0x41   // PAD协议
#define CID1_TOWER (TOWER_CID1)   // TOWER协议
#define CID1_DEYE 0x46   // 德业协议

#define P2P_TRIGGER 0xFF //点对点触发命令

#define HIGH_SEND_LEN 512 // 协议层发送缓冲区长度
#define HIGH_REC_LEN 256  // 协议层接收缓冲区长度

#define PROTOCOL_VER_22 0x22 // 通讯协议版本号
#define PROTOCOL_VER_20 0x20 // 通讯协议版本号
#define PROTOCOL_VER_21 0x21 // 通讯协议版本号
#define PROTOCOL_VER_25 0x25 // 固网通讯协议版本号
#define PROTOCOL_VER_DEYE 0x20  // 德业通讯协议版本号

#define BRODCAST_ADR 0

#define SIGAL_FAT_RECORD_LENGTH 9
#define TRIGGER_COUNTER 3

#define CID2ALL 0
#define CID2DC 1
#define CID2BMS 1
#define CID2PAD 0
#define CID2Tower 2
#define CID2DEYE  3

#define FLOAT_INVALID 0x00 // 用于工装协议中实时数据显示无效值
#define BATT_NUM_BCM 1
#define CRC_CCITT16 0x1021  // CRC_CCITT16

////////////////  文件上传协议开始  ////////////////
/* 上传文件宏定义 */
#define UPLOAD_DATA_FRAME_LEN                    256    //串口256，网口1024
#define UPLOAD_FILE_NAME_MAX_LEN                 40     //文件名最大长度
#define UPLOAD_FILE_NUM                          20     //文件列表数
#define UPLOAD_FILE_TIME_LEN                     20     //文件上传时间长度，协议定的20

/* 触发帧 */
#define REMOTE_COMM_TRG_COUNTER        3       //  触发帧次数
#define REMOTE_COMM_TRG_FRAME_TYPE     0x4141  //  触发帧标识
#define REMOTE_COMM_TRG_CHIP_TYPE      0x3232  //  触发帧芯片 ID 标识>
#define REMOTE_COMM_TRG_VERS_F         0x3237  //  F版本
#define REMOTE_COMM_TRG_ADDR_INDEX     1       //  触发帧 - 帧地址偏移 >
#define REMOTE_COMM_TRG_RESERVED_INDEX 3       //  触发帧 - 帧预留偏移 >
#define REMOTE_COMM_TRG_FRAME_INDEX    5       //  触发帧 - 帧标识偏移 >
#define REMOTE_COMM_TRG_CHIP_INDEX     7       //  触发帧 - 芯片 ID 偏移 >
#define REMOTE_COMM_TRG_CODE_INDEX     9       //  触发帧 - 内容体偏移 >
#define REMOTE_COMM_TRG_CODE_LEN       14      //  触发帧 - 内容体长度 >
#define CMD_APPEND_TRG                 0xAA55  //  触发帧附加码
#define CMD_APPEND_TRG_ACK             0x55AA  //  触发帧应答附加码

/* 协议转换规则码 */
#define REMOTE_COMM_STANDARD_SOI       0x7E    //  包头
#define REMOTE_COMM_STANDARD_EOI       0x0D    //  包尾
#define REMOTE_COMM_FILE_DOWNLOAD      0x41    //  文件下载
#define REMOTE_COMM_FILE_UPLOAD        0x42    //  文件上传

#define REMOTE_COMM_SHIFT_FLG          0x7D    //  转换序列
#define REMOTE_COMM_CONVERT_INFO1      0x10    //  转换序列
#define REMOTE_COMM_CONVERT_INFO2      0x12    //  转换序列
#define REMOTE_COMM_CONVERT_INFO3      0x7F    //  转换序列

/* 数据组包偏移 */
#define REMOTE_COMM_HEADER_ADDR          0
#define REMOTE_COMM_FLAG_ADDR            1
#define REMOTE_COMM_ADDR_ADDR            2
#define REMOTE_COMM_REVSERD_ADDR         4
#define REMOTE_COMM_DATALEN_ADDR         6
#define REMOTE_COMM_FN1_ADDR             8
#define REMOTE_COMM_FN2_ADDR             10
#define REMOTE_COMM_DEVICE_ADDR          12
#define REMOTE_COMM_CID_ADDR             13
#define REMOTE_COMM_RTN_ADDR             14
#define REMOTE_COMM_DATA_ADDR            15

/* CID */
#define HISTORY_GET_FILELIST       0x18    //获取互备份文件列表
#define HISTORY_GET_DATA           0x19    //获取互备份文件数据

#define REMOTE_COMM_DATA_LEN       2       //获取文件列表首发帧LEN的值固定为2
#define REMOTE_COMM_BUFF_LEN       512     //文件上传数据缓存，串口512，网口1024

/***************************************************************************
 * @brief   协议触发帧
 **************************************************************************/
#ifndef UNITEST
#pragma pack(1)
#endif
typedef struct {
    unsigned char head;
    unsigned short addr;
    unsigned short reserved;
    unsigned short frame_type;                          ///<  帧格式
    unsigned short chip_type;                           ///<  芯片类型
    unsigned char  trig_code[REMOTE_COMM_TRG_CODE_LEN];  ///<  触发帧数据域
    unsigned char endCode;
} T_RemoteTrigStruct;
#ifndef UNITEST
#pragma pack()
#endif

/***************************************************************************
 * @brief   文件上传结构体
 **************************************************************************/
typedef struct  
{
    BYTE        ucHeader;                        // 帧头 0x7e
    BYTE        ucFlag;                          // 标志 0x42文件接收
    WORD        wAddr;                           // 地址
    WORD        wReserved;                        // 预留
    WORD        wDataLen;                        // DATA段数据长度
    WORD        wFN1;                            // 对于文件的接收方，表示已经成功收到的帧号
    WORD        wFN2;                            // 对于文件的发送方，表示发送帧号。对于文件的接收方，表示请求帧号
    BYTE        ucDevice;
    BYTE        ucCID;
    BYTE        ucRtnCode;                        // 返回码 0－正确 1－校验错误请求重发 2－命令执行失败 3 - 未触发
    BYTE        aucData[UPLOAD_DATA_FRAME_LEN];   // 数据收发缓冲区
    WORD        wCRC;                             // 校验码 从SOH后面第一个字节开始，到DATA段
    BYTE        ucEndCode;                        // 帧尾 0x0d
}T_RemoteTransStruct;

/* 上传的文件信息 */
typedef struct
{
    //请求的文件信息
    unsigned short wReqFileNameLen;
    unsigned char ucReqFileName[UPLOAD_FILE_NAME_MAX_LEN];
    //上传文件列表信息
    unsigned short wListTotalFrame;  //列表总帧数
    unsigned char ucListTotalNum;    //列表总个数
    unsigned char aucListNameLen[UPLOAD_FILE_NAME_MAX_LEN];    //列表中的文件名长度
    unsigned char *pucListName[UPLOAD_FILE_NUM];               //列表中的文件名
    //上传文件信息
    unsigned short cur_frame_no;
    unsigned short wFileTotalFrame;
    unsigned long int uFileTotalLen;
    unsigned char ucFileTime[UPLOAD_FILE_TIME_LEN];
    unsigned int uCrc; 
}T_FileUploadStruct;
/////////////////  文件上传协议结束  ////////////////

// 协议层数据结构定义
typedef struct
{
    /***********   接收数据   ************/
    BYTE aucRecBuf[HIGH_REC_LEN]; //接收到的16进制数据帧
    WORD wOriginLength;           //接收数据包的原长度
    BYTE ucVer;                   //接收数据包的版本号
    BYTE ucAddr;
    BYTE ucAddrBak; //将收到的地址备份
    BYTE ucCID1;
    BYTE ucCID2;         //接收数据包的设备地址、CID1和CID2
    BYTE ucLCHKSUM;      //接收数据包的长度校验和
    WORD wRecLenid;      //接收数据包的LENID
    BYTE ucCommandGroup; //接收数据包的COMMAND_GROUP
    BYTE ucCommandType;  //接收数据包的COMMAND_TYPE

    /***********   发送数据   ************/
    BYTE ucRTN;                     //响应数据包的返回码
    WORD wSendLenid;                //响应数据包的LENID
    BYTE aucSendBuf[HIGH_SEND_LEN]; //响应数据包发送前的16进制数据帧
    BYTE aucInvalidFlag[HIGH_SEND_LEN];          //无效标志
} T_ProtocolStruct;

// 协议层数据结构定义
typedef struct
{
    BYTE ucCmdCode;
    void (*func)(BYTE);
    WORD ucDataLen;
} T_CmdFuncStruct;

typedef struct
{
    BYTE ucCharge;
    BYTE ucDisCharge;
    BYTE ucBuzzer;
    BYTE ucLEDStatus;
    BYTE ucBanlanceLow;
    BYTE ucBanlanceHigh;
} T_BMSControlStruct;

typedef struct
{
    BYTE ucCommandType;
    WORD wDataLength;
} T_BMSProLengthTable;

typedef struct Yd1363FrameHead
{
    BYTE ucVer;
    BYTE ucAddr;
    BYTE ucCid1;
    union {
        BYTE ucCid2;
        BYTE ucRtn;
    };
} T_Yd1363FrameHead;

/*********************  变量的声明  **********************/
extern BYTE g_ucTriggerRepeat;
extern WORD g_wTriggerTimeOut;
extern T_ProtocolStruct s_tProtocol;

/*********************  函数原型定义  **********************/
void DealTriggerTimeOut(void);
void Deal1363CommData(T_CommStruct *ptComm);
void InitProtocolData(void);
WORD Get1363DataFromCache(BYTE const *pucSrc, BYTE *pucBuff, SHORT sLen);
void InitApptestData(void);
void InitQtptestData(void);
void SendSysTime(BYTE ucPort);
void RecSysTime(BYTE ucPort);
void SendProtocolVer(BYTE ucPort);
Bool CheckSn(BYTE *p, BYTE ucSnLength);
void DeleteTrigTimer(void);
BYTE GetQtpTrigger(void);
BYTE GetQtpTestTriggerRepeat(void);
int DataPackShift(char *sendBuf, unsigned int uSendBuflen, char *packBuf);
int DataParseShift(char *pucRecvBuf, unsigned int uRcevLen, char *pucParseBuf);
short get_int16_data(const unsigned char *p);
BOOLEAN GetRemoteCommFlag(void);
void SetRemoteCommFlag(BOOLEAN bflag);
void GetFileList(char *pucSendBuf, WORD *pwSendBufLen, T_RemoteTransStruct *ptTransFile);
void GetFileData(char *pucSendBuf, WORD *pwSendBufLen, T_RemoteTransStruct *ptTransFile);
INT16S EncodeToYd1363Frame(void *dest, WORD destlen, T_Yd1363FrameHead tFrameHead, const void* info, WORD infolen);
BOOLEAN DealFaultDiagnAlarm(BYTE *p, T_DCRealData *ptDcRealData);
BYTE GetBMSDLAddr(void);
BYTE GetCanBMSDLAddr(void);
void GetSysTimeQTP(time_t t);


BOOLEAN GetSoftwareTagInfo(BYTE *resultArr, BYTE size);

#ifdef __cplusplus
} /* end of the 'extern "C"' block */
#endif

#endif // SOFTWARE_SRC_APP_PROTOCOL_H_;
