#ifndef PARTITION_TABLE_H_  
#define PARTITION_TABLE_H_    
#ifdef __cplusplus
extern "C" {
#endif

#include "storage.h"


part_info_t part_tab[] = {
    {APP_RUN_PART,      storage_flash,    UNUSE},
    {DOWNLOAD_PART,     storage_flash,    UNUSE},
    {BACK_PART,         storage_flash,    UNUSE},
    {EEP_DATA_PART,     storage_eeprom,   UNUSE},
};


#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // PARTITION_TABLE_H_    ;