#ifndef _CO_AIRSWITCH_ALARM_MANAGE_H
#define _CO_AIRSWITCH_ALARM_MANAGE_H

#ifdef __cplusplus
extern "C" {
#endif    //  __cplusplus

#include "data_type.h"
#include "device_type.h"
#include "para_manage.h"
#include "utils_server.h"
#include "time.h"

#define    ALARM_EXIST     1
#define    ALARM_NOT_EXIST 0

#define    NORMAL 0
#define    FAULT 1

#define    ONE_SECOND_TICK 2  // 1S 对应的执行次数

#define    CONTACT_OVER_TEMPERATURE_ADD    -5  // 空开触点过温告警的回差
#define    INTERNAL_OVER_TEMPERATURE_ADD   -5  // 空开内部过温告警的回差
#define    OVER_CURRENT_ALARM_ADD       -0.05f // 空开过流告警的回差
#define    CONNECTED_REVERSELY_ALARM_ADD   20  // 空开接反告警的回差

#define    ALM_TICK_DELAY                   500    //告警模块主线程延时
#define    ALM_MIN_TICK                     (60000/ALM_TICK_DELAY)   //一分钟

#define    CONTROLLER_FAULT_NORMAL      0
#define    CONTROLLER_FAULT_SMALL_CURR  1
#define    CONTROLLER_FAULT_CLOSE_FAULT 2
#define    CONTROLLER_FAULT_OPEN_FAULT  3

typedef struct
{
    unsigned char real_alarm_status;     // 告警当前状态
    unsigned char *appear_counter;       // 产生判断次数
    unsigned char *disappear_counter;    // 消失判断次数
    unsigned char appear_counter_threshold;     // 产生判断次数阈值
    unsigned char disappear_counter_threshold;  // 消失判断次数阈值

    //模拟量部分
    float value_add;       //回差
    float threshold;       //阈值
    float value;           //传入的模拟量值

    //数字量部分
    unsigned char status;     //数字量状态

}alarm_judge_t;


// 掉电保存告警枚举
typedef enum
{
    DISCONNECTION_ALARM = 0,           //  空开断开告警
    DOWN_POWER_ALARM,                  //  空开下电告警
    CLOSE_FAILURE_ALARM,               //  空开合闸失败告警
    OPENING_FAILURE_ALARM,             //  空开分闸失败告警
    CONTROLLER_FAILURE_ALARM,          //  空开控制器故障告警
    SAVE_ALARM_INDEX_MAX,
}save_alarm_e;


// 实时告警延时计数结构体
typedef struct
{
	unsigned char	airswitch_over_current_alarm;                //  空开过流告警
    unsigned char	airswitch_disconnection_alarm;               //  空开断开告警
    unsigned char	airswitch_contact_over_temperature_alarm;    //  空开触点过温告警
    unsigned char	airswitch_internal_over_temperature_alarm;   //  空开内部过温告警
    unsigned char	airswitch_low_voltage_down_power_alarm;      //  空开低压下电告警
    unsigned char	airswitch_down_power_alarm;                  //  空开下电告警
    unsigned char	airswitch_close_failure_alarm;               //  空开合闸失败告警
    unsigned char	airswitch_opening_failure_alarm;             //  空开分闸失败告警
    unsigned char	airswitch_connected_reversely_alarm;         //  空开接反告警
    unsigned char	airswitch_controller_failure_alarm;          //  空开控制器故障告警
    unsigned char	airswitch_over_current_protect_alarm;        //  空开过流保护告警
    unsigned char	airswitch_over_temperature_protect_alarm;    //  空开过温保护告警
    unsigned char	airswitch_overload_trip_alarm;               //  空开跳闸告警

}airswitch_alarm_t;



void* init_alarm_manage(void * param);
void alarm_main(void * param);
char init_read_eeprom_alarm(void);
char restore_default_alarm_params(void);
unsigned char clear_controller_fault_alarm(void);
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _CO_ALARM_MANAGE_H
