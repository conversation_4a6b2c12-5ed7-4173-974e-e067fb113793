#ifdef E<PERSON>BLE_SYS_MONITOR

#include "SysMonitor.h"
#include "cpuusage.h"
#include "common.h"
#include "fileSys.h"
#include "drv_utils.h"
#include "utils_rtthread_security_func.h"

static sys_monitor_heap_info_t s_heap_info = {0, };
static sys_monitor_thread_info_t s_thread_info = {0, };
static sys_monitor_log_t s_sys_monitor_log = {0, };
static BYTE s_uccpu_usage = 0;
static struct rt_thread s_thread_tcb = {0, };

void init_sys_monitor(void)
{
    CLEAR_STRUCT(s_heap_info);
    CLEAR_STRUCT(s_thread_info);
    CLEAR_STRUCT(s_sys_monitor_log);

    s_sys_monitor_log.min_heap_remained_size = UINT32_MAX;
}


const sys_monitor_thread_info_t *get_thread_info(void)
{
    return &s_thread_info;
}

const sys_monitor_heap_info_t *get_heap_info(void)
{
    return &s_heap_info;
}

const sys_monitor_log_t *get_sys_monitor_log(void)
{
    return &s_sys_monitor_log;
}


const BYTE get_cpu_usage(void)
{
    return s_uccpu_usage;
}

static int get_thread_stack_usage(rt_thread_t thread)
{
    RETURN_VAL_IF_FAIL((thread->type & ~RT_Object_Class_Static) == RT_Object_Class_Thread, -1);

#if defined(ARCH_CPU_STACK_GROWS_UPWARD)
    return ((rt_ubase_t)thread->sp - (rt_ubase_t)thread->stack_addr) * 100 / thread->stack_size;
#else
    return (thread->stack_size + ((rt_ubase_t)thread->stack_addr - (rt_ubase_t)thread->sp)) * 100 / thread->stack_size;
#endif
}

static int get_thread_max_stack_usage(rt_thread_t thread)
{
    RETURN_VAL_IF_FAIL((thread->type & ~RT_Object_Class_Static) == RT_Object_Class_Thread, -1);

    rt_uint8_t *ptr = (rt_uint8_t *)thread->stack_addr;
    while (*ptr == '#')
    {
        ++ptr;
    }
    return (thread->stack_size - ((rt_ubase_t)ptr - (rt_ubase_t)thread->stack_addr)) * 100 / thread->stack_size;
}



static void collect_heap_info(void)
{
    struct rt_list_node *node;
    struct rt_object_information *info = rt_object_get_information(RT_Object_Class_MemHeap);
    struct rt_memheap *mh;
    rt_base_t level;
    size_t index = 0;

    RETURN_IF_FAIL(info != RT_NULL);

    CLEAR_STRUCT(s_heap_info);

    rt_list_for_each(node, &(info->object_list))
    {
        if (index >= SYS_MONITOR_MEMHEAP_NUM_MAX) { break; }

        struct rt_object *obj = rt_list_entry(node, struct rt_object, list);

        level = rt_hw_interrupt_disable();
        if ((obj->type & ~RT_Object_Class_Static) != RT_Object_Class_MemHeap)
        {
            rt_hw_interrupt_enable(level);
            continue;
        }

        mh = (struct rt_memheap *)obj;
        s_heap_info.current_available_size += mh->available_size;
        s_heap_info.min_available_size += (mh->pool_size - mh->max_used_size);
        rt_hw_interrupt_enable(level);

        ++index;
    }
}

static void collect_thread_info(sys_monitor_thread_info_t *thread_info)
{
    struct rt_list_node *node;
    struct rt_object_information *info = rt_object_get_information(RT_Object_Class_Thread);
    struct rt_thread *thread;
    rt_base_t level;
    size_t index = 0;

    RETURN_IF_FAIL(info != RT_NULL);

    rt_list_for_each(node, &(info->object_list))
    {
        if (index >= SYS_MONITOR_THREAD_NUM_MAX) { break; }

        thread = rt_list_entry(node, struct rt_thread, list);

        level = rt_hw_interrupt_disable();
        s_thread_tcb = *thread;
        rt_hw_interrupt_enable(level);

        int stack_usage = get_thread_stack_usage(&s_thread_tcb);
        int max_stack_usage = get_thread_max_stack_usage(&s_thread_tcb);
        if (stack_usage < 0 || max_stack_usage < 0) { continue; }

        rt_memcpy_s(thread_info->info[index].name, sizeof(thread_info->info[0].name), s_thread_tcb.name, sizeof(s_thread_tcb.name));
        thread_info->info[index].current_stack_usage = stack_usage;
        thread_info->info[index].max_stack_usage = max_stack_usage;

        ++index;
    }
    thread_info->num = index;
}



static BOOLEAN refresh_heap_info_log(void)
{
    BOOLEAN log_changed_flag = False;

    if (s_sys_monitor_log.min_heap_remained_size > s_heap_info.min_available_size)
    {
        s_sys_monitor_log.min_heap_remained_size = s_heap_info.min_available_size;
        log_changed_flag = True;
    }

    return log_changed_flag;
}

static BOOLEAN refresh_thread_info_log(void)
{
    const sys_monitor_thread_info_t *thread_info = get_thread_info();
    const size_t log_thread_num = thread_info->num;
    BOOLEAN log_changed_flag = False;

    for (size_t i = 0; i < log_thread_num; ++i)
    {
        if (s_sys_monitor_log.thread_log[i].max_stack_usage < thread_info->info[i].max_stack_usage)
        {
            s_sys_monitor_log.thread_log[i].max_stack_usage = thread_info->info[i].max_stack_usage;
            log_changed_flag = True;
        }
    }
    s_sys_monitor_log.thread_num = log_thread_num;

    return log_changed_flag;
}



static BOOLEAN refresh_cpu_usage_info_log()
{
    BOOLEAN log_changed_flag = False;
    s_uccpu_usage = FLOAT_TO_BYTE(cpu_usage_get());

    if(s_sys_monitor_log.max_cpu_usage < s_uccpu_usage)
    {
        s_sys_monitor_log.max_cpu_usage = s_uccpu_usage;
        log_changed_flag = True;
    }

    return log_changed_flag;
}


BOOLEAN read_sys_monitor_log(sys_monitor_log_t *log)
{
    size_t sz = readFile(FILENAME_SYS_MONITOR_LOG, (BYTE *)log, sizeof(*log));
    RETURN_VAL_IF_FAIL(sz == sizeof(*log), False);
    RETURN_VAL_IF_FAIL(log->crc16 == CRC_Cal((BYTE *)&log->ver, sizeof(*log) - offsetof(sys_monitor_log_t, ver)), False);
    RETURN_VAL_IF_FAIL(log->ver == SYS_MONITOR_LOG_VER, False);

    return True;
}

static BOOLEAN write_sys_monitor_log(sys_monitor_log_t *log)
{
    log->ver = SYS_MONITOR_LOG_VER;
    log->crc16 = CRC_Cal((BYTE *)&log->ver, sizeof(*log) - offsetof(sys_monitor_log_t, ver));
    return writeFile(FILENAME_SYS_MONITOR_LOG, (BYTE *)log, sizeof(*log)) == sizeof(*log);
}

BOOLEAN save_sys_monitor_log(void)
{
    BOOLEAN is_thread_info_log_changed = False;
    BOOLEAN is_heap_info_log_changed = False;
    BOOLEAN is_cpu_max_usage_info_changed = False;

    is_thread_info_log_changed = refresh_thread_info_log();
    is_heap_info_log_changed = refresh_heap_info_log();
    is_cpu_max_usage_info_changed = refresh_cpu_usage_info_log();

    if (is_thread_info_log_changed || is_heap_info_log_changed || is_cpu_max_usage_info_changed)
    {
        return write_sys_monitor_log(&s_sys_monitor_log);
    }

    return False;
}


void delete_sys_monitor_log(void)
{
    // Delete the system monitor log file
    deleteFile(FILENAME_SYS_MONITOR_LOG);

    // Clear the system monitor log structure
    CLEAR_STRUCT(s_sys_monitor_log);

    // Set the minimum heap remained size to its maximum value
    s_sys_monitor_log.min_heap_remained_size = UINT32_MAX;
}


void app_idle_execute(void)
{
    static uint32_t i = 0;

    if (i < SYS_MONITOR_COLLECT_CYCLE - 1)
    {
        ++i;
    }
    else
    {
        collect_thread_info(&s_thread_info);
        collect_heap_info();
        i = 0;
    }
}

#ifdef RT_USING_HOOK
int app_idle_hook(void)
{
    return rt_thread_idle_sethook(app_idle_execute);
}
INIT_APP_EXPORT(app_idle_hook);
#endif /* RT_USING_HOOK */

#endif /* ENABLE_SYS_MONITOR */
