/**************************************************************************
* Copyright (C) 2010-2011, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：ZXDU18 CTL板软件
* 文件名称：const_define.h
* 文件说明：常用常量定义
* 作    者：龙明星
* 版本信息：V1.0
* 设计日期：2023-08-05
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/
#ifndef SOFTWARE_SRC_APP_CONST_DEFINE_H_  
#define SOFTWARE_SRC_APP_CONST_DEFINE_H_  

#ifdef __cplusplus
extern "C" {
#endif

#define CONTOL_RST_SYS                    77//(PARA_ID_TIME+1)  //77系统重启ACTION_ROOT_ID
#define CONTOL_RST_PARA                   78//(PARA_ID_TIME+2)  //78恢复默认参数ACTION_LOAD_DEFAULT_PARA_ID
#define CONTOL_ADDR_COMPETE               79//(PARA_ID_TIME+3)  //79重新竞争地址ACTION_RESET_ADD_COMPETE_ID
#define CONTOL_SET_TIME                   80//(PARA_ID_TIME+4)  //80设置系统时间ACTION_SET_SYSTIME_ID
#define CONTOL_ADDR_CHANGE                81//(PARA_ID_TIME+5)  //81地址改变ACTION_ADDR_CHANGE_ID
#define CONTOL_BUTTON_SLEEP               82//(PARA_ID_TIME+6)  //82按键休眠ACTION_KEY_SLEEP_ID
#define CONTOL_RELEASE_LOCK               83//(PARA_ID_TIME+7)  //83解除闭锁ACTION_RELEASE_LOCK_ID
#define ACTION_DISCONNECT_PMOS            84//(PARA_ID_TIME+8)  //84PMOS断开ACTION_DISCONNECT_PMOS
#define ACTION_HOLE_BUTTON_SHUT_DOWN_ID   85//(PARA_ID_TIME+9)  //85小孔关机ACTION_HOLE_BUTTON_SHUT_DOWN_ID
#define CONTOL_UNLOCK_ANTITHEFT           86//(PARA_ID_TIME+10) //86人工防盗解锁ACTION_SET_BATT_MANUAL_UNLOCK_ID
#define CONTOL_MASTER_SLAVE_CHANGE        87//(PARA_ID_TIME+11) //87主从机模式切换ACTION_SET_BATT_MASTER_SLAVE_ID
#define CONTOL_BOOT_UPDATE                88//(PARA_ID_TIME+12) //88boot升级ACTION_BOOT_UPDATE_ID
#define CONTOL_CLEAR_CELL_DAMAGE          89//(PARA_ID_TIME+13) //89清除单体损坏ACTION_CLEAR_CELL_DAMAGE_PRT_ID
#define CONTOL_BATTERY_STATE              90//(PARA_ID_TIME+14) //90铁塔协议遥控命令ACTION_BATTCTRLSTAT_ID
#define CONTOL_BAUDRATE_SWITCH            91//(PARA_ID_TIME+15) //91波特率切换ACTION_BAUDRATE_SWITCH_ID
#define CONTOL_SHUTDOWN                   92//(PARA_ID_TIME+16) //92开始关机ACTION_START_SHUTDOWN
#define CONTOL_UPPER_COMPUTER_UNLOCK_ID   93//(PARA_ID_TIME+17) //93上位机防盗解锁ACTION_UPPER_COMPUTER_UNLOCK_ID
#define CONTOL_NETWORK_MANAGE_UNLOCK_ID   94//(PARA_ID_TIME+18) //94网管防盗解锁ACTION_NETWORK_MANAGE_UNLOCK_ID
#define CONTOL_ENTER_QUIET_SLEEP          95//(PARA_ID_TIME+19) //95进入静置休眠状态ACTION_ENTER_QUIET_SLEEP
#define CONTOL_QUIT_QUIET_SLEEP           96//(PARA_ID_TIME+20) //96退出静置休眠状态ACTION_QUIT_QUIET_SLEEP
#define CONTOL_SET_LOCK                   97//(PARA_ID_TIME+21) //97控制闭锁ACTION_SET_LOCK_ID
#define CONTOL_CONTACTOR_BREAK            98//电压二级保护接触器断开
#define CONTOL_CONTACTOR_CLOSE            99//电压二级保护接触器闭合
#define CONTOL_TWO_CLASS_LOCK             100//电压二级保护闭锁控制
#define CONTOL_ENTER_APPTEST              101//进入apptest
#define CONTOL_QUIT_APPTEST               102//退出apptest
#define CONTOL_ENTER_QTP                  103//进入qtp
#define CONTOL_QUIT_QTP                   104//退出qtp
#define CONTOL_LOW_POWER_CONSUMPTION_SLEEP  105//低功耗休眠
#define CONTOL_SAMPLE_IC_RESTART          106//采样IC重启
#define CONTOL_EMC_TEST                   107//EMC测试
#define CONTOL_MANUAL_ENTER_DEFENCE       108//人工进布防
#define CONTOL_BATT_INDICATION            109//电池指示
#define CONTOL_SWITCH_ADDR                110//地址切换
#define CONTOL_KEY_UPGRADE                111//按键进入升级
#define CONTOL_KEY_DEFQUERY               112//按键布防查询
#define CONTOL_KEY_UPGRADE_TIMEOUT_TO_NORMAL 113//按键升级超时回退到正常模式
#define CONTOL_START_HEATING               114//启动加热
#define CONTOL_STOP_HEATING                115//停止加热
#define CONTOL_KEY_UPGRADE_MANNUAL_TO_NORMAL             116//按键退出升级模式
#define CONTOL_UPPER_CANCEL_DEVICE_DEFENCE 117//上位机人工撤防
#define CONTOL_DEFENCE_STATUS_CHANGE       118//布防状态改变
#define CONTOL_CAN1_FIRE_LOCK              119//收到CAN1火灾告警导致闭锁改变
#define CONTOL_CAN2_ADDR_CHANGE            120//柜间地址改变
#define CONTOL_SET_CAN2_ADDR               121//设置CAN2柜间地址
#define CONTOL_SET_CAN2_ADDR_MODE          122//设置CAN2柜间地址获取方式
#define CONTOL_CAN2_ADDR_COMPLETE          123//控制柜间地址竞争
#define CONTOL_CLEAR_DCR_PRT               124 //清除直流内阻保护
#define CONTOL_NETWORK_CANCEL_DEVICE_DEFENCE  125 //网管人工撤防
#define CONTOL_SLAVE_UPDATE_SUCCESSFUL     126//升级从机升级成功
#define CONTOL_CLEAR_4G_TRAFFIC            127//清除流量记录

#define CTRL_SITE_ANTI_THEFT_ON      242   //  站点防盗布防
#define CTRL_SITE_ANTI_THEFT_OFF     243   //  站点防盗撤防

#define CTRL_NET_ANTI_THEFT          245   //  网管防盗相关控制
//以下十个对应错峰相关操作记录
#define PARA_PEAK_SHIFT_TEMPLATE     246   //  错峰日模板
#define PARA_PEAK_SHIFT_ENABLE       247   //  错峰使能
#define PARA_PEAK_SHIFT_DOD          248   //  错峰放电深度
#define PARA_ELEC_PRICE_PATTERN      249   //  错峰模式
#define PARA_DAY_PATTER_NUM          250   //  日模式对应日模板序号
#define PARA_WEEK_PATTER_NUM         251   //  周模式对应日模板序号
#define PARA_MONTH_PATTER_NUM        252   //  月模式对应日模板序号
#define PARA_HOLIDAY_PATTER_NUM      253   //  节假日模式对应日模板序号
#define PARA_HOLIDAY_PATTER_DATE     254   //  节假日模式对应日期
#define PARA_POWER_OFF_PEAK_EN       255   //  停电错峰使能

#define SMART_LI_RATE_CURRENT_CHG    (150.0f)       //电池侧最大充电电流
#define SMART_LI_RATE_CURRENT_CHG_BUS    (100.0f)   //Bus侧最大电流
#define SMART_LI_RATE_CURRENT_DISCHG (100.0f)
#define SMART_LI_RATE_TIME_CHG_SLOW (5)
#define SMART_LI_RATE_CURRENT_CHG_SLOW (5)
#define MIN_CURR_DET_BDCU (1.0) //电流检测误差修改为1A
#define DCR_TEST_MIN_CURRENT 8.0f   //DCR测试最小电池电流
#define SMART_LI_SELF_DISCHARGE_CURRENT (0.02f)
#define SELF_DISCHARGE_RATE_35C (0.745)
/*******************   人工布防命令返回码   *****************/
#define RTN_SATISFY_DEFENCE              0    // 满足布防条件
#define RTN_SUCCESS_DEFENCE              0x80 // 成功进入布防
#define RTN_FAIL_DEFENCE_WIRE            0x81 // 布防失败，防盗线未接好
#define RTN_FAIL_DEFENCE_LEVEL           0x82 // 布防失败，告警级别未设置好
#define RTN_FAIL_DEFENCE_WIRE_LEVEL_PARA 0x83 // 布防失败，防盗线未接好，告警级别未设置好，软件防盗延时和陀螺仪灵敏度都为0
#define RTN_FAIL_DEFENCE_PARA            0x84 // 布防失败，软件防盗延时和陀螺仪灵敏度都为0
#define RTN_FAIL_DEFENCE_APPTEST         0x85 // 布防失败，当前为apptest模式
#define RTN_FAIL_DEFENCE_QTP             0x86 // 布防失败，当前为qtp模式

#define CELL_TEMP_NUM       4      //单体温度数量
#define ALARM_CLASS         80     //告警数量
#define NUM_OF_MEASUREPOINTS         23    // 可变监测点数量


#define SMART_LI_MAX_DISCHARGE_CURRENT (105.0f)
#define BATT_SOC_MAX (1.0f)
#define DISCHARGE_CURRENT_COEFFICIENT  (1.05)

#define BDCU_CHARGE_RATE_MAX ((FLOAT)100.0f)
#define BDCU_CHARGE_RATE_MIN ((FLOAT)2.0f)
#define BDCU_DISCHARGE_RATE_MAX ((FLOAT)105.0f)
#define BDCU_DISCHARGE_RATE_MIN ((FLOAT)2.0f)
#define SHORT_CUT_ALARM_SAVE_ID        (232)
#define BASE_ADDR 1
#define CYCLE_TIMES_MAX (3500) // 电芯最大循环次数

#define CHARGE_OVER_CURR 	  10500		// 初始化充电过流保护阈值，精度2
#define DISCHARGE_OVER_CURR   10000  	// 初始化放电过流保护阈值，精度2;
#define BATTERY_OVER_VOLT	  2922		// 初始化电池组过压保护阈值，精度2
#define BUS_DROP_VOLT	      4500		// 初始化母排跌落电压阈值，精度2
#define CHARGE_LIMIT_CURR	  1000		// 初始化设定充电限电流，千分比	
#define DISCHARGE_LIMIT_CURR  1280		// 初始化设定放电限电流，千分比	
#define CHARGE_VOLT 		  5250		// 初始化设定充电电压，精度2
#define DISCHARGE_VOLT        4800      // 初始化设定放电电压，精度2
#define CURR_SHARE			  0			// 初始化均流电流，精度2
#define CHARGE_BUS_VOLT		  3500		// 初始化BUS充电最低电压阀值，精度2
#define MAX_CHARGE_POWER      5500		// 初始化最大充电功率，精度0
#define MAX_DISCHARGE_POWER   5500      // 初始化最大放电功率，精度0

#define BATT_LOSE_ALM_INDEX   30        //电池丢失告警对应告警序号
#define DCR_FAULT_ALM_INDEX   66        //直流内阻异常告警对应告警序号
#define DCR_FAULT_PRT_INDEX   67        //直流内阻异常保护对应告警序号

#define TOWER_CID1 0X4A   // R321 TOWER协议 CID1

#define LED_NUM               6   // R321 指示灯总数(LED_RUN, LED_ALARM, LED_CAP1, LED_CAP2, LED_CAP3,LED_CAP4)

#define BACKUP_DEV_TYPE                2   // 互备份设备类型
#define BACKUP_HISACT_LEN              37  // 互备份操作记录自定义长度
#define BACKUP_DEVICE_MAX_NUM          32  // 互备份最大设备数量
#define BACKUP_HISDATA_MAX_NUM        360  // 互备份历史数据最大条数
#define BACKUP_HISALARM_MAX_NUM       360  // 互备份历史告警最大条数
#define BACKUP_HISACT_MAX_NUM         360  // 互备份历史操作记录最大条数
#define BACKUP_RECORD_MAX_NUM         100  // 互备份录波数据最大条数
#define BACKUP_TIME_VERIFY_INDEX      2    // 互备份记录保存时时间校验偏移

typedef enum {
    NORTH_PROTOCOL_1363 = 0,
    NORTH_PROTOCOL_TOWER,
    NORTH_PROTOCOL_NUM_MAX,
}record_north_protocol_e;

#define MAIN_NORTH_PROTOCOL NORTH_PROTOCOL_1363 //此版本主要使用的北向协议

#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // SOFTWARE_SRC_APP_CONST_DEFINE_H_  ;
