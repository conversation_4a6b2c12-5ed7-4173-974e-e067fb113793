/**
 * @file     device_type.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2017-04-19
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */

#ifndef _DEVICE_BMU_H
#define _DEVICE_BMU_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "device_type.h"
#include "device_num.h"


#define BMU_SOFT_VER_LEN        20      ///< BMU 软件版本数组长度
#define BMU_ASSET_MANAGE_LEN    30      ///< BMU 资产管理数组长度
#define BMU_SYS_NAME_LEN        32
#define BMU_SOFT_VER            32
#define BMU_CELL_FAC_NAME       32
#define BMU_CELL_VER            32
#define BMU_CELL_SERIES_NUM_LEN 32      ///< BMU电芯出厂序列号数组长度
#define BMU_PACK_BAR_CODE_LEN   20      ///< pack bar code数组长度


#pragma pack(1)

typedef struct {
    unsigned short batt_soc;
    unsigned short batt_soh;
    unsigned int cell_balance_control;
    time_base_t parse_system_time;
    unsigned char bmu_serial_number[15];
}BMU_para_data_t;
#pragma pack()




#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _DEVICE_BMU_H
