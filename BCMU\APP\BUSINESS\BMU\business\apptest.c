#include <stdbool.h>
#include <stdint.h>
#include <string.h>
#include <math.h>
#include "sample_new.h"
#include <board.h>
#include <rtthread.h>
#include <rtdevice.h>
#include "realdata_save.h"
#include "realdata_id_in.h"
#include "para_id_in.h"
#include "para_manage.h"
#include "pin.h"
#include "data_type.h"
#include "utils_server.h"
#include "pin_define.h"
#include "realdata_id_in.h"
#include "apptest.h"


int get_address_input_pin_status(void)
{
    return PIN_HIGH == rt_pin_read(DI_BMU_PIN);
}

int set_address_output_pin_status(int status)
{
    if(status == TRUE)
    {
        rt_pin_write(DO_BMU_PIN, PIN_HIGH);
    }
    else
    {
        rt_pin_write(DO_BMU_PIN, PIN_LOW);
    }

    return SUCCESSFUL;
}

int get_bmu_analog_status(bmu_apptest_analog_data* data)
{
    if(data == NULL)
    {
        return FAILURE;
    }
    float temp = 0.0f;
    float voltage = 0.0f;
    for (int i = 0; i < CELL_TEMP_SAMPLE_NUM; i++)
    {
        get_one_data(BMU_DATA_ID_CELL_TEMP + i, &temp);
        data->bmu_cell_temperature[i] = temp;
    }
    
    for (int i = 0; i < CELL_TEMP_NUM; i++)
    {
        get_one_data(BMU_DATA_ID_CELL_VOLT + i, &voltage);
        data->bmu_cell_voltage[i] = voltage;
    }

    for (int i = 0; i < SAMPLE_CHIP_NUM; i++)
    {
        get_one_data(BMU_DATA_ID_EQUA_RESISTOR_TEMP + i, &temp);
        get_one_data(BMU_DATA_ID_CELL_MOD_VOL1 + i, &voltage);
        data->bmu_equal_resistance_temp[i] = temp;
        data->bmu_batt_mod_voltage[i] = voltage;
    }

    get_one_data(BMU_DATA_ID_BATT_TEMP, &temp);
    get_one_data(BMU_DATA_ID_BATT_MOD_VOLT, &voltage);
    data->bmu_board_temp = temp;
    data->bmu_batt_voltage = voltage;

    get_one_data(BMU_DATA_ID_BATT_POS_TEMP, &temp);
    data->bmu_batt_pos_temp = temp;
    get_one_data(BMU_DATA_ID_BATT_NEG_TEMP, &temp);
    data->bmu_batt_neg_temp = temp;
    
    return SUCCESSFUL;
}

int get_bmu_digital_status(bmu_apptest_digital_data* data)
{
    if(data == NULL)
    {
        return FAILURE;
    }
    unsigned char bmu_fire_status;
    unsigned char bmu_msd_status;
    get_one_data(BMU_DATA_ID_FIRE_TRIG_STA, &bmu_fire_status);
    get_one_data(BMU_DATA_ID_MSD_CONNECTION, &bmu_msd_status);
    data->bmu_fire_status = bmu_fire_status;
    data->bmu_msd_status = bmu_msd_status;
    return SUCCESSFUL;
}

int get_bmu_alarm_status(bmu_apptest_alarm_data* data)
{
    if(data == NULL)
    {
        return FAILURE;
    }
    unsigned char bmu_temp_det_status;
    unsigned char bmu_sample_chip_status;
    for (int i = 0; i < SAMPLE_CHIP_NUM; i++)
    {
        get_one_data(BMU_DATA_ID_SAM_CHIP_FAULT_STA + i, &bmu_sample_chip_status);
        data->bmu_samp_chip_fault_status[i] = bmu_sample_chip_status;
    }

    for (int i = 0; i < CELL_TEMP_SAMPLE_NUM; i++) 
    {
        get_one_data(BMU_DATA_ID_TEMP_DET_FAULT_STA + i, &bmu_temp_det_status);
        data->bmu_cell_temp_det_fault_status[i] = bmu_temp_det_status;
    }
    return SUCCESSFUL;
}
int get_bmu_factory_info(bmu_apptest_factory_data* data)
{
    if(data == NULL)
    {
        return FAILURE;
    }

    get_one_para(BMU_PARA_ID_BMU_SN_OFFSET, &data->bmu_factory_sn[0]);
    get_one_data(BMU_DATA_ID_SOFT_VER, &data->bmu_soft_ver[0]);
    get_one_data(BMU_DATA_ID_SOFT_REL_DATE, &data->bmu_soft_release_date);
    get_one_para(BMU_PARA_ID_HARDWARE_VERSION_OFFSET, &data->bmu_hardware_ver[0]);
    
    return SUCCESSFUL;
}