#include "common.h"
#include "algorithmAES.h"
#include "sample.h"
#include "realAlarm.h"
#include "battery.h"
#include "para.h"
#include "comm.h"
#include "protocol.h"
#include "stdio.h"
#include "commBdu.h"
#include "apptest.h"
#include "apptest_product.h"
#include "CommCan.h"
#include "wireless.h"
#include "led.h"
#include "fileSys.h"
#include "ism330dhcx.h"
#include "CommCan2.h"
#include "utils_rtthread_security_func.h"

/* CAN对接测试状态 */
#define CAN_INTERCONNECT_STATE_ABNORMAL 0             ///< CAN1、CAN2发送接收都异常
#define CAN_INTERCONNECT_STATE_NORMAL   1             ///< CAN1、CAN2发送接收都正常
#define CAN_INTERCONNECT_STATE_CAN1_TO_CAN2_FAIL 2    ///< CAN1 TO CAN2 异常
#define CAN_INTERCONNECT_STATE_CAN2_TO_CAN1_FAIL 3    ///< CAN2 TO CAN1 异常

static T_SysPara   s_tSysPara;
static struct rt_device *s_BattChip_device;

/*********************  静态函数原型定义  **********************/
static void DealDcCommand( BYTE   ucPort );
static void DealCommonCommand( BYTE ucPort );

//APPTEST命令
static void ChipcheckRtcandFlash(BYTE ucPort);
Static void GetDcAngData(BYTE ucPort);
static void GetDcStatus(BYTE ucPort);
static void SetBMSAdjustData( BYTE ucPort );
Static void GetBmsPara(BYTE ucPort);
static void SetBmsPara(BYTE ucPort);
static void GetBmsParaNew(BYTE ucPort);
static void SetBmsParaNew(BYTE ucPort);
Static void GetAlarmLevel(BYTE ucPort);
Static void SetAlarmLevel(BYTE ucPort);
Static void GetAlarmRelay(BYTE ucPort);
Static void SetAlarmRelay(BYTE ucPort);
static void GetSpecificPara(BYTE ucPort);
static void SetSpecificPara(BYTE ucPort);
static const T_CmdFuncStruct* GetCmdFnucStruct(BYTE segment);
static void CheckSms(BYTE ucPort) UNUSED;  // TODO: 仅R321使用，应抽离成接口后去除UNUSED宏和static属性
static void GetGPRSStatus(BYTE ucPort) UNUSED;  // TODO: 仅R321使用，应抽离成接口后去除UNUSED宏和static属性
// static void SetBMSAdjustDataNew(BYTE ucPort);
static void GetBMSAdjustData(BYTE ucPort);
static void GetIMEICode(BYTE ucPort) UNUSED;  // TODO: 仅R321使用，应抽离成接口后去除UNUSED宏和static属性
static void RecCan2BusCtrl(BYTE ucPort) UNUSED;  // TODO: 仅R321使用，应抽离成接口后去除UNUSED宏和static属性
static void SendCan2Report(BYTE ucPort) UNUSED;  // TODO: 仅R321使用，应抽离成接口后去除UNUSED宏和static属性
static void GetBduApptestData(BYTE ucPort);
static void SetBMSChgVolt(BYTE ucPort);
static void RecCanInterconnectCtrl(BYTE ucPort);
static void SendCanInterconnectReport(BYTE ucPort);
static void BDUCtrl(BYTE ucPort);
static void SetCustomerName(BYTE ucPort);
static void GetCustomerName(BYTE ucPort);
Static T_SysPara* GetSysParaPtr(void);
static void RecvSysParaById(BYTE ucPort);
static void SendSysParaById(BYTE ucPort);
static void Clear4GTrafficApptest(BYTE ucPort);
/********************************命令码处理START**************************************/ 
//CID1处理
const T_CmdFuncStruct s_atCID1AllAppTestFuncTable[] =
{
    {CID1_COMMON, DealCommonCommand, 0},  // 公共部分CID1 = 0X40
    {CID1_DC, DealDcCommand, 0},          // 直流部分CID1 = 0X42
    //end flag
    {0x00, 0x0000, 0x00},
};

    //APPTEST COMMON_PART
const T_CmdFuncStruct   s_atCID2AllAppTestTable[] = 
{
    // 公共测试命令
    {GET_TIME_AT, SendSysTime, 0},
    {SET_TIME_AT, RecSysTime, 14},
    {GET_FACTORY_INFO_AT, GetFactoryInfo, 0},
    {SET_FIRST_BOOT_TIME, SetFirstBootTime, 14},
    {GET_FIRST_BOOT_TIME, GetFirstBootTime, 0},
    {SET_FAC_TIME, SetFacTime, 14},
    {GET_FAC_TIME, GetFacTime, 0},
    //中试增加的命令
    {START_CAN_BUS_TEST_AT, RecCanBusCtrl, 0},
    {GET_CAN_BUS_RESULT_AT, SendCanReport, 0},
    {SET_CSU_ID_AT, SetCsuId, 30},
    {GET_MAC_AT,GetHWMacAddr,0},
    {SET_MAC_AT,SetHWMacAddr,12},
    {START_NET_TEST_AT,SetNetTest,2},

    //new add
    {GET_PROTOCOL_VER_AT, SendProtocolVer, 0},

    {CHIP_TEST_AT, ChipcheckRtcandFlash, 0},
    {SET_FAT_ID_AT, SetFactoryAcceptanceTest, 18},
    {GET_FAT_ID_AT, GetFactoryAcceptanceTest, 0},
    {SET_QUIT_TEST_AT, SetQuitAPPTest, 0},
    {GET_CSU_ID_AT, GetCsuId, 0},
    {START_BMS_CTRL_AT, SendBMSCtrl, 12},
    {GET_BMS_PACK_INFO_AT, GetBMSPACKFactoryInfo, 0},
    {SET_BMS_PACK_INFO_AT, SetBMSPACKFactoryInfo, 160},
    {SET_BMS_BATT_CAP_AT, SetBMSBattCap, 8},

    {SET_APPTEST_RESET_AT, RecResetCmd, 0},
    {BMS_ADJUST_DATA_AT, SetBMSAdjustData, 6},
    {SET_DEL_HISDATA_AT, SetDelHisData, 6},
    {GET_BMS_ID_AT, GetBmsId, 0},
    {SET_BMS_ID_AT, SetBmsId, 30},
    {GET_BDU_FACT_AT, GetBDUFact, 0},
    {COMM_LI_EFFICIENCY_TEST_AT, SetEfficiencyTest, 2},
    {GET_CELL_FACT_INFO_AT, SendCellFactInfo, 0},
    {SET_CELL_FACT_INFO_AT, RecCellFactInfo, 0},
    {GET_BMS_FACT_AT,GetBmsFactInfo, 0},
    {SET_BMS_FACT_AT, SetBmsFactInfo, 42},
    {SET_BMS_FACT_AT_NEW,SetBmsFactInfoNew,66},
    {GET_BMS_FACT_AT_NEW,GetBmsFactInfoNew,0},

    {BMS_SEND_SMS, CheckSms, 32},
    {GET_GPRS_STATUS, GetGPRSStatus, 0},
    {GET_GPS_LOCATED, GetGpsStatus, 0},
    {GET_IMEI_CODE, GetIMEICode, 0},
    {START_CAN2_TEST, RecCan2BusCtrl, 0},
    {GET_CAN2_TEST, SendCan2Report, 0},

    {GET_BTN_COUNT, GetBtnCount, 0},
    {SET_BMS_SHUTDOWN, SetBMSShutdown, 0},
    //{BMS_ADJUST_DATA_NEW,SetBMSAdjustDataNew, 10},
    {REFRESH_DEFAULT_PARA, RefreshDefaultPara, 0},
    {SET_BDU_OPENCYCLE, SetOpenCycleCtrl, 2},
    {GET_BDU_ID_AT, GetBduIdNo, 0},
    {SET_BDU_ID_AT, SetBduIdNo, 8},
    {GET_BDPB_ID, GetBDPBSn, 0},
    {SET_BDPB_ID, SetBDPBSn, 30},
    {GET_MAIN_ID, GetMainSn, 0},
    {SET_MAIN_ID, SetMainSn, 30},

    {GET_BMS_BATT_CAP,GetBMSBattCap,0},
    {SET_RELEASE_LOCK, SetBMSReleaseLock,0},

    {GET_HARDWARE_PARA, GetBMSHardwarePara,0},
    {SET_HARDWARE_PARA, SetBMSHardwarePara,4},

    {SET_BDU_HEAT, SetHeatCtrl, 2},
    {GET_BDU_FACT_HEAT, GetBDUFactHeat, 0},

    // {START_BALA_CIRC_CHECK, StartBalanceCircCheck, 0},
    // {GET_BALA_CIRC_FAULT, GetBalanceCircFault, 0},

    {START_CAN_INTERCONNECT_TEST, RecCanInterconnectCtrl, 0},
    {GET_CAN_INTERCONNECT_TEST, SendCanInterconnectReport, 0},
    {BDU_CTRL, BDUCtrl, 4},

    {SET_CUSTOMER_NAME_AT, SetCustomerName, 34},
    {GET_CUSTOMER_NAME_AT, GetCustomerName, 0},

    {GET_HARDWARE_INFORMATION_EX, GetHardwareInfo, 0},
    {SET_HARDWARE_INFORMATION_EX, SetHardwareInfo, 40},
    {GET_4G_MODE_REAL_DATA,Get4GModeRealData,0},
    //end flag
    {0x00, 0x0000, 0x00},
};

//APPTEST DC_PART
const T_CmdFuncStruct   s_atCID2BMSDCTestTable[] =
{
    // 直流配电部分测试命令
    {GET_ANALOG_INT_AT, GetDcAngData, 0},      //直流部分定点数据
    {GET_SWITCH_AT, GetDcStatus, 0},           //直流部分开关状态
    {REMOTE_CTRL_AT, SetDcControl, 16},        //输出干接点控制
    {GET_THEFT_TEST_AT, GetTheftTest, 0},      //获取防盗端口状态
    {GET_PARA_INT_AT, GetBmsPara, 0},          //获取bms参数
    {SET_PARA_INT_AT, SetBmsPara, 6},          //设置bms参数
    //{GET_SLEEP_TEST_AT, GetSleepStatus, 0},      //休眠功能测试
    {GET_ALARM_LEVEL, GetAlarmLevel, 0},
    {SET_ALARM_LEVEL, SetAlarmLevel, 6},
    {GET_ALARM_RELAY_APPTEST, GetAlarmRelay, 0},
    {SET_ALARM_RELAY_APPTEST, SetAlarmRelay, 6},
    {GET_PARA_NEW_APPTEST, GetBmsParaNew, 0},
    {SET_PARA_NEW_APPTEST, SetBmsParaNew, 6},
    {GET_BDU_ADJ_PARA, GetBMSAdjustData,2},
    {GET_SPECIFIC_PARA, GetSpecificPara, 0},
    {SET_SPECIFIC_PARA, SetSpecificPara, 6},
    {GET_BMS_CHARGE_VOLT, GetBMSChgVolt, 0},
    {SET_BMS_CHARGE_VOLT, SetBMSChgVolt, 6},
    {SET_BDU_TEST_PARA, SetBduAppTestPara, 0},
    {GET_BDU_TEST_DATA, GetBduApptestData, 0},
    {GET_BAT_PARA_BY_ID, SendSysParaById, 4},
    {SET_BAT_PARA_BY_ID, RecvSysParaById, 0},
    {SET_BAT_BMS_INFO, SetBmsInfo, 0},
    {GET_BAT_BMS_INFO, GetBmsInfo, 0},
    {CLEAR_4G_TRAFFIC_APPTEST, Clear4GTrafficApptest, 0},

    // end flag
    {0x00, 0x0000, 0x00},
};

const T_CmdFuncStruct* s_auAppTestProtocl[] =
{s_atCID2AllAppTestTable, s_atCID2BMSDCTestTable};

/********************************命令码处理END**************************************/   
/****************************************************************************
* 函数名称：
* 输入参数： 
* 返 回 值： 
* 功能描述：如果版本号为偶数，版本号加1，否则不变
* 作    者  ：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
yang_an,整理协议修改，20110511
***************************************************************************/
static const T_CmdFuncStruct* GetCmdFnucStruct(BYTE segment)
{ 
    if (segment < sizeof(s_auAppTestProtocl)/sizeof(s_auAppTestProtocl[0]))
    {
        return s_auAppTestProtocl[segment];
    }
    return NULL;
}

/****************************************************************************
* 函数名称：DealApptestCommand()
* 调    用：无
* 被 调 用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：根据CID1和CID2，调用相应的命令处理程序
* 作    者  ：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
void DealApptestCommand(T_CommStruct *ptComm)
{
    WORD i = 0;

    for ( i = 0; i < HIGH_SEND_LEN; i++ )    /* 清空响应缓冲区 */
    {
        s_tProtocol.aucSendBuf[i] = 0;
    }
    /* 将响应包的LENID预置为0 */
    s_tProtocol.wSendLenid  = 0;
    if ( (GET_PROTOCOL_VER_AT == s_tProtocol.ucCID2) && (CID1_COMMON == s_tProtocol.ucCID1) )
    {
        return;
    }

//CID1处理
    i = 0;
    while(s_atCID1AllAppTestFuncTable[i].ucCmdCode)
    {
        if ( s_atCID1AllAppTestFuncTable[i].ucCmdCode == s_tProtocol.ucCID1 )
        {
            (*s_atCID1AllAppTestFuncTable[i].func)( ptComm->ucPortType );
            return;
        }
        i++;
    }

    // CID1错，返回RTN_WRONG_CID1
    s_tProtocol.ucRTN   = RTN_WRONG_CID1;

    return;
}

/****************************************************************************
* 函数名称：DealDcCommand()
* 调    用：无
* 被 调 用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：根据CID1和CID2，调用相应的命令处理程序
* 作    者  ：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
static void DealDcCommand( BYTE ucPort )
{
    WORD i = 0;
    const T_CmdFuncStruct *s_atCID2BMSDCTestTable = GetCmdFnucStruct(CID2DC);

    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }
    while (s_atCID2BMSDCTestTable != NULL && s_atCID2BMSDCTestTable[i].ucCmdCode)
    {
        if ( s_atCID2BMSDCTestTable[i].ucCmdCode == s_tProtocol.ucCID2 )
        {
            if (s_atCID2BMSDCTestTable[i].ucDataLen != s_tProtocol.wRecLenid)
            {
                if((s_tProtocol.ucCID2 == SET_PARA_INT_AT)
                    || (s_tProtocol.ucCID2 == SET_PARA_NEW_APPTEST)
                    || (s_tProtocol.ucCID2 == SET_SPECIFIC_PARA)//DC段设置参数
                    || (s_tProtocol.ucCID2 == SET_BDU_TEST_PARA)
                    || (s_tProtocol.ucCID2 == SET_BAT_PARA_BY_ID)
                    || (s_tProtocol.ucCID2 == SET_BAT_BMS_INFO))
                {

                }
                else
                {
                    s_tProtocol.ucRTN   = RTN_WRONG_COMMAND;
                    return;
                }
            }
            (*s_atCID2BMSDCTestTable[i].func)(ucPort);
            return;
        }
        i++;
    }
    s_tProtocol.ucRTN   = RTN_WRONG_CID2;       /* CID2无效 */
}

static void DealCommonCommand( BYTE ucPort )
{
    WORD i = 0;
    const T_CmdFuncStruct *s_atCID2CommonTestTable = GetCmdFnucStruct(CID2ALL);

    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }

    i = 0;
    while (s_atCID2CommonTestTable != NULL && s_atCID2CommonTestTable[i].ucCmdCode)
    {
        if ( s_atCID2CommonTestTable[i].ucCmdCode == s_tProtocol.ucCID2 )
        {
            if (s_atCID2CommonTestTable[i].ucDataLen != s_tProtocol.wRecLenid )
            {
                if ((s_tProtocol.ucCID2 == SET_CELL_FACT_INFO_AT))
                { 

                }
                else
                {
                    s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
                    return;
                }
            }
            (*s_atCID2CommonTestTable[i].func)(ucPort);
            return;
        }
        i++;
    }

    s_tProtocol.ucRTN   = RTN_WRONG_CID2;       /* CID2无效 */
}


/****************************************************************************
* 函数名称：ChipcheckRtcandFlash(BYTE ucPort)
* 调    用：无
* 被 调 用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：获取芯片测试信息
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
static void ChipcheckRtcandFlash(BYTE ucPort)
{
    BYTE *p = s_tProtocol.aucSendBuf;
    BYTE ucEEPROMFlag = 1;
    WORD wNorFlashFlag = 0XA55A;
    CHAR chipNameMt[6] = {'M','T','9','8','1','8'};
    CHAR chipNameBq[7] = {'B','Q','7','6','9','5','2'};

    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }

    *p++ = 5;                   //the number of  chips
    *p++ = 1;                   //rtc
    writeNorFlashTestPara(&wNorFlashFlag);
    wNorFlashFlag = 0;
    readNorFlashTestPara(&wNorFlashFlag);
#ifndef KW_CHECK
    if( 0XA55A != wNorFlashFlag )
    {
        *p++ = 0x00;            //norflash 异常
    }
    else
    {
        *p++ = 0x01;            //norflash 正常
    }
#endif
    *p++ = ucEEPROMFlag;          //EEPROM

    *p++ = GetMG21State();

    s_BattChip_device = rt_device_find("mt9818");
    if(RT_NULL == s_BattChip_device)
    {
        s_BattChip_device = rt_device_find("bq76952");
        if(s_BattChip_device != RT_NULL)
        {
            MemsetBuff(p, chipNameBq, 7, 16, 0x00);
        }
    }
    else
    {
        MemsetBuff(p, chipNameMt, 6, 16, 0x00);
    }

    p += 16;

    s_tProtocol.wSendLenid  = (WORD) ((p-s_tProtocol.aucSendBuf)*2);
    return;
}

/****************************************************************************
* 函数名称：RecCan2BusCtrl(BYTE ucPort)
* 调    用：无
* 被 调 用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：启动CAN2总线测试，判断CAN2是否初始化
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
static void RecCan2BusCtrl(BYTE ucPort)
{
    BYTE *p  = s_tProtocol.aucSendBuf;
    WORD wSendLen = 2;
    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }
       InitCanProto();
    GetSmrRealDataCan2(CAN2_DEV);
    *p++ = 0x01;
     *p = 0;
    s_tProtocol.wSendLenid = wSendLen;
    return;
}

/**
 * @brief 接收CAN对接测试命令
 * @param[in] ucPort 串口号
 * @note 启动CAN1和CAN2对接测试
*/
static void RecCanInterconnectCtrl(BYTE ucPort)
{
    BYTE *p = s_tProtocol.aucSendBuf;
    WORD wSendLen = 2;
    if (ucPort >= SCI_PORT_NUM)
    {
        return;
    }

    SendCan1InterconnectTestFrm();
    SendCan2InterconnectTestFrm();

    *p++ = 0x01;
    *p = 0;
    s_tProtocol.wSendLenid = wSendLen;
}

/**
 * @brief  获取本机CAN对接测试结果
 * @retval CAN_INTERCONNECT_STATE_ABNORMAL           CAN1、CAN2发送接收都异常
 *         CAN_INTERCONNECT_STATE_NORMAL             CAN1、CAN2发送接收都正常
 *         CAN_INTERCONNECT_STATE_CAN1_TO_CAN2_FAIL  CAN1 TO CAN2 异常
 *         CAN_INTERCONNECT_STATE_CAN2_TO_CAN1_FAIL  CAN2 TO CAN1 异常
 */
static BYTE CanInterconnectTestState(void)
{
    BYTE retVal = CAN_INTERCONNECT_STATE_ABNORMAL;
    UINT32 i_can_1_InterconnetTestCounter = GetCan1InterconnetTestCounter();  // CAN1对接测试成功计数
    UINT32 i_can_2_InterconnetTestCounter = GetCan2InterconnetTestCounter();  // CAN2对接测试成功计数
    if(i_can_1_InterconnetTestCounter != 0 && i_can_2_InterconnetTestCounter != 0)
    {
        retVal = CAN_INTERCONNECT_STATE_NORMAL;
    }
    else if (0 == i_can_1_InterconnetTestCounter && i_can_2_InterconnetTestCounter != 0)
    {
        retVal = CAN_INTERCONNECT_STATE_CAN1_TO_CAN2_FAIL;
    }
    else if (i_can_1_InterconnetTestCounter != 0 && 0 == i_can_2_InterconnetTestCounter)
    {
        retVal = CAN_INTERCONNECT_STATE_CAN2_TO_CAN1_FAIL;
    }
    ClearCan1InterconnetTestCounter();
    ClearCan2InterconnetTestCounter();
    return retVal;
}

static void SendCanInterconnectReport(BYTE ucPort) {
    WORD wSendLen = 2;
    BYTE *p  = s_tProtocol.aucSendBuf;
    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }

    *p++ = CanInterconnectTestState();
    *p = 0;
    s_tProtocol.wSendLenid = wSendLen;
}

/****************************************************************************
* 函数名称：SendCan2Report(BYTE ucPort)
* 调    用：无
* 被 调 用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：获取CAN2总线测试信息
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
static void SendCan2Report(BYTE ucPort)
{
    WORD wSendLen = 2;
    BYTE *p  = s_tProtocol.aucSendBuf;
    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }
    *p++  = Can2RecCon();
    s_tProtocol.wSendLenid = wSendLen;
    *p = 0;
    return;
}

/****************************************************************************
* 函数名称：GetDcAngData(BYTE ucPort)
* 调    用：无
* 被 调 用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：获取直流模拟量定点数据
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
Static void GetDcAngData(BYTE ucPort)
{
    BYTE *p = NULL;
    BYTE i = 0;
    BYTE wCellVoltNum = 0;
    BYTE wCellTempNum = 0;
    T_BCMDataStruct    tBCMAnaData;
    T_DCPara tOutPara;
    
    T_DCRealData tDcRealData;
    T_HardwareParaStruct tHWPara;

    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }
    rt_memset(&tBCMAnaData, 0, sizeof(T_BCMDataStruct));
    rt_memset(&tOutPara, 0, sizeof(T_DCPara));
    rt_memset(&tDcRealData, 0, sizeof(T_DCRealData));
    rt_memset(&tHWPara, 0, sizeof(T_HardwareParaStruct));

    GetRealData( &tBCMAnaData );
    GetBduPara(&tOutPara);
    GetBduReal(&tDcRealData);
    readBmsHWPara(&tHWPara);

    if (tHWPara.ucCellVoltNum <= CELL_VOL_NUM_MAX) {
        wCellVoltNum = tHWPara.ucCellVoltNum;
    }

    if (tHWPara.ucCellTempNum <= CELL_TEMP_NUM_MAX) {
        wCellTempNum = tHWPara.ucCellTempNum;
    }
    
    p  = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0x00, sizeof(s_tProtocol.aucSendBuf));

    /*DATA_FLAG*/
    *p++ = GetDataFlag( BCM_ALARM, ucPort );                    //DATA_FLAG

    /*DATA_DATAI*/
    rt_memset(p, FLOAT_INVALID, 4);                 //直流输出电压
    p += 4;
    rt_memset(p, FLOAT_INVALID, 4);                 //负载总电流
    p += 4;
    rt_memset(p, FLOAT_INVALID, 4);                 //电池电流1
    p += 4;
    rt_memset(p, FLOAT_INVALID, 4);                 //电池电流2
    p += 4;
    rt_memset(p, FLOAT_INVALID, 4);                 //电池电流3
    p += 4;
    rt_memset(p, FLOAT_INVALID, 4);                 //电池电流4
    p += 4;

    rt_memset(p, FLOAT_INVALID, 4);                 //用户自定义电池电压1
    p += 4;
    rt_memset(p, FLOAT_INVALID, 4);                 //用户自定义电池电压2
    p += 4;
    rt_memset(p, FLOAT_INVALID, 4);                 //用户自定义电池电压3
    p += 4;
    rt_memset(p, FLOAT_INVALID, 4);                 //用户自定义电池电压4
    p += 4;
    rt_memset(p, FLOAT_INVALID, 4);                 //用户自定义电池温度1
    p += 4;
    rt_memset(p, FLOAT_INVALID, 4);                 //用户自定义电池温度2
    p += 4;
    rt_memset(p, FLOAT_INVALID, 4);                 //用户自定义电池温度3
    p += 4;
    rt_memset(p, FLOAT_INVALID, 4);                 //用户自定义电池温度4
    p += 4;

    rt_memset(p, FLOAT_INVALID, 4);                 //用户自定义电池中点电压1
    p += 4;
    rt_memset(p, FLOAT_INVALID, 4);                 //用户自定义电池中点电压2
    p += 4;
    rt_memset(p, FLOAT_INVALID, 4);                 //用户自定义电池中点电压3
    p += 4;
    rt_memset(p, FLOAT_INVALID, 4);                 //用户自定义电池中点电压4
    p += 4;
    /*********************    以上为通用命令非BMS专用，因此赋值无效                    *************************/

    /*************BMS_RealData***************************/
//	*(FLOAT*)p = tBCMAnaData.fBattVolt;                //电池电压
    *(FLOAT*)p = tDcRealData.tDCAnalag.wBatVol / 100.0f;
    p += 4;

//	*(FLOAT*)p = tBCMAnaData.fExterVolt;               //外部电压
    *(FLOAT*)p = tDcRealData.tDCAnalag.wBusVol / 100.0f;
    p += 4;

//	*(FLOAT*)p = tBCMAnaData.fEnvTemp;                  //环境温度
    *(FLOAT*)p = tDcRealData.tDCAnalag.sEnvTemp / 10.0f;
    p += 4;

//	*(FLOAT*)p = tBCMAnaData.fBattCurr;                 //电池电流
    *(FLOAT*)p = tDcRealData.tDCAnalag.sBatCur / 100.0f;
    p += 4;

    *p++ = wCellVoltNum;                                //检测的单体电芯数量M

    for(i = 0; i < wCellVoltNum && i < sizeof(tBCMAnaData.afCellVolt)/sizeof(FLOAT); i++)
    {
        *(FLOAT*)p = tBCMAnaData.afCellVolt[i];        //单体电压
        p += 4;
    }

    *p++ = wCellTempNum;                              //检测的电芯温度数量N

    for(i = 0; i < wCellTempNum && i < sizeof(tBCMAnaData.afCellTemp)/sizeof(FLOAT); i++)
    {
        *(FLOAT*)p = tBCMAnaData.afCellTemp[i];        //单体温度
        p += 4;
    }

//	*(FLOAT*)p = tBCMAnaData.fBusCurr;                 //BUS电流
    *(FLOAT*)p = tDcRealData.tDCAnalag.sBusCur / 100.0f;
    p += 4;

    //增加 1当前设定充电电压 2当前设定放电电压 3当前设定充电限电流 0730
    *(FLOAT*)p=tOutPara.wChgVolVal/100.0f;
    
    p += 4;

    *(FLOAT*)p=tOutPara.wDischgVolVal/100.0f;
    p += 4;

    *(FLOAT*)p=(FLOAT)(tOutPara.wChgBusCurVal);    //千分比
    p += 4;

    *(FLOAT*)p = tBCMAnaData.fBoardTemp;            //单板温度
    p += 4;

    *(FLOAT*)p = GetFireControlAdcVol();            //消防采样电压
    p += 4;
    
    /* LENID */
    s_tProtocol.wSendLenid  = (WORD) ((p-s_tProtocol.aucSendBuf)*2);

    return;
}

/****************************************************************************
* 函数名称：GetDcAngData(BYTE ucPort)
* 调    用：无
* 被 调 用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：获取直流开关状态
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
static void GetDcStatus(BYTE ucPort)
{

    BYTE *p = NULL;
    BYTE i = 0;
    WORD wLedTemp = 0x00;
    BYTE wFault = 1;
    BYTE ucChgLimit = 0,ucDischgLimit = 0;
    BYTE wCellTempNum = 0;

    T_BCMDataStruct    tBCMAnaData;
    T_BCMAlarmStruct    tBCMAlm;
    T_CtrlOutStruct tCtrlOut;
    T_DCRealData tDcRealData;
    T_HardwareParaStruct tHWPara;

    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }

    rt_memset(&tBCMAnaData, 0, sizeof(T_BCMDataStruct));
    rt_memset(&tDcRealData, 0, sizeof(T_DCRealData));
    rt_memset(&tHWPara, 0, sizeof(T_HardwareParaStruct));

    GetRealData( &tBCMAnaData );
    GetBduReal(&tDcRealData);
    rt_memset(&tBCMAlm, 0, sizeof(T_BCMAlarmStruct));

    GetBcmRealAlarm(&tBCMAlm);
    readBmsHWPara(&tHWPara);
    wCellTempNum = tHWPara.ucCellTempNum;

    p  = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

    /*DATA_FLAG*/
    *p++ = GetDataFlag( BCM_ALARM, ucPort );                   //DATA_FLAG

    *p++ = 0x00;                    //输入干接点1
    *p++ = 0x00;                    //输入干接点2
    *p++ = 0x00;                    //输入干接点3
    *p++ = 0x00;                    //输入干接点4

    *p++ = 0x00;                    //直流负载回路状态1
    *p++ = 0x00;                    //直流负载回路状态2
    *p++ = 0x00;                    //直流负载回路状态3
    *p++ = 0x00;                    //直流负载回路状态4
    *p++ = 0x00;                    //直流负载回路状态5
    *p++ = 0x00;                    //直流负载回路状态6
    *p++ = 0x00;                    //直流负载回路状态7
    *p++ = 0x00;                    //直流负载回路状态8
    *p++ = 0x00;                    //直流负载回路状态9
    *p++ = 0x00;                    //直流负载回路状态10
    *p++ = 0x00;                    //直流防雷
    *p++ = 0x00;                    //烟雾
    *p++ = 0x00;                    //门磁
    *p++ = 0x00;                    //水淹
    *p++ = 0x00;                    //门禁
    /***********    以上是通用命令，非BMS专用，因此数据赋值为无效值         *************/

    //BMS专用
    *p++ = tDcRealData.tDCStatus.bChgPrt;                       //充电保护状态
    *p++ = tDcRealData.tDCStatus.bDischgPrt;                    //放电保护状态
    if (tBCMAnaData.ucLimit)
    {
        if ( (BDU_STATUS_COMMON_CHARGE == tBCMAnaData.ucBduStatus) || \
             (BDU_STATUS_BOOST_CHARGE == tBCMAnaData.ucBduStatus)  || \
             (BDU_STATUS_BUCK_CHARGE == tBCMAnaData.ucBduStatus) )
        {
            ucChgLimit = wFault;                      //充电限流状态获取:常规充电/升压充电/降压充电
        }
        else if ( (BDU_STATUS_COMMON_DISCHARGE == tBCMAnaData.ucBduStatus) || \
                  (BDU_STATUS_BOOST_DISCHARGE == tBCMAnaData.ucBduStatus)  || \
                  (BDU_STATUS_BUCK_DISCHARGE == tBCMAnaData.ucBduStatus) )
        {
            ucDischgLimit = wFault;                   //放电限流状态获取：常规放电/升压放电/降压放电
        }
    }   

    *p++ = ucChgLimit;                                //充电限流状态
    *p++ = ucDischgLimit;                             //放电限流状态
    *p++ = tBCMAnaData.ucCellEquSta;                  //电芯均衡状态

    GetCtrlOut(&tCtrlOut);

    *p++ = tCtrlOut.bBuzz;                               //蜂鸣器状态

    for(i = 0; i < 6; i++)
    {
        if(tCtrlOut.bLed[i] != 0)
        {
            wLedTemp |= BIT(i);
        }
    }
    *p++ = wLedTemp;                                       //LED灯状态

    *p++ = 1;                                              //下载拨码开关状态
    *p++ = GetTotalAddr();                                   //地址拨码开关状态
    *p++ = tCtrlOut.wRelayOut % 2;            //tBCMAnaData.aucOutputRly[0];   //输出干接点1
    *p++ = (tCtrlOut.wRelayOut / 2) % 2;       //tBCMAnaData.aucOutputRly[1];   //输出干接点2

    /*****************以下根据总表协议修改新增部分告警 add by xzx *****************/
    *p++ = tBCMAlm.ucBoardTempHighPrt;                   //单板过温保护
    *p++ = tBCMAlm.ucChgLoopInvalid;                     //充电回路失效
    *p++ = tBCMAlm.ucDischgLoopInvalid;                  //放电回路失效
    *p++ = tBCMAlm.ucCurrLimLoopInvalid;                 //限流回路失效
    *p++ = tBCMAlm.ucInsideTempHighPrt;                   //机内过温保护

    *p++ = tBCMAlm.ucBDUBattVoltLowPrt;                  //BDU电池欠压保护
    *p++ = tBCMAlm.ucBDUBusVoltLowPrt;                   //BDU母排欠压保护
    *p++ = tBCMAlm.ucBDUBusVoltHighPrt;                  //BDU母排过压保护
    *p++ = tBCMAlm.ucBduEepromAlm;                       //BDU EEPROM故障

    *p++ = tBCMAlm.ucShakeAlarm;                         //振动告警

    *p++ = tBCMAlm.ucCellVoltSampleFault;                //单体电压采样异常
    *p++ = tBCMAlm.ucBDUCommFail;                        //BDU通讯断
    *p++ = 0;                   //单体温度异常

    *p++ = wCellTempNum;                              //检测的电芯温度数量N

    for(i = 0; i < wCellTempNum; i++)
    {
        *p++ = tBCMAlm.ucCellTempSensorInvalidAlm[i];            //单体温度无效
    }

    //*p++ = tBCMAnaData.ucBduStatus;

    //增加 1充电输入断 2充电使能 3放电使能  4 BMS充放电状态 5反接 6短路 7 16位电芯均衡状态 8电池组过压保护 9充电过流保护 10放电过流保护 11陀螺仪状态
    *p++ = tBCMAnaData.ucInputBreak;
    *p++ = tBCMAnaData.ucBattChargeEn;
    *p++ = tBCMAnaData.ucBattDischEn;   
    *p++ = tBCMAnaData.ucBduStatus;
    *p++ = tBCMAlm.ucBattReverse;
    *p++ = tBCMAlm.ucBattShortCut;
    *(WORD*)p = GetBalaneceCode();   //16位电芯均衡状态，实际均衡控制状态
    p += 2;
    *p++ = tBCMAlm.ucBattOverVoltPrt;       
    *p++ = tBCMAlm.ucChgCurrHighPrt;
    *p++ = tBCMAlm.ucDischgCurrHighPrt;
#ifndef KW_CHECK
    if(True == CheckIsm330Connect())
    {
        *p++ = 0;   //陀螺仪通信正常
    }
    else
#endif
    {
        *p++ = 1;   //陀螺仪通信异常
    }
    *p++ = 0;    //均衡电路故障检测状态(删除该参数，默认上送0)
    *p++ = tDcRealData.tDCStatus.bHeaterStatus;		//BDU加热状态
    *p++ = tDcRealData.tDCAlarm.bHeaterErr;			//加热器故障
    *p++ = tDcRealData.tDCAlarm.bWavePrt;			//逐波保护
    *p++ = tDcRealData.tDCAlarm.bMainRelayFail;		//主继电器失效
    *p++ = tDcRealData.tDCAlarm.bDCDCErr;			//DCDC故障
    *p++ = tDcRealData.tDCAlarm.bSampleErr;			//采样异常
    *p++ = tDcRealData.tDCAlarm.bAuxiSourceErr;		//辅助源故障

    DealFaultDiagnAlarm(p, &tDcRealData);
    p += 4;

    *p++ = GetFireControlStatus();	//消防信号状态
    *p++ = tDcRealData.tDCAlarm.bActivatePortCurrError;	    //激活回路电流异常保护
    *p++ = tDcRealData.tDCStatus.bContactorBatt;            //电池接触器状态
    *p++ = tBCMAlm.ucSelfDischFualt; //自放电异常
    *p++ = GetFireControlFaultStatus();	//消防故障状态
    /* LENID */	
    s_tProtocol.wSendLenid  = (WORD) ((p-s_tProtocol.aucSendBuf)*2);

    return;
}

static void SetBMSAdjustData( BYTE ucPort )
{
    WORD wTemp;

    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }
    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];

    if( s_tProtocol.ucCommandType >= 0x80 ) // 上限不做拦截，由BDU自己检测
    {
        wTemp = (WORD)(Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+8)));
        BduAdj(s_tProtocol.ucCommandType, wTemp);
    }
    else
    {
         s_tProtocol.ucRTN  = RTN_WRONG_COMMAND;    // 命令格式错
         return;
    }
    return;
}


Static void GetBmsPara(BYTE ucPort)
{
    BYTE *p = NULL;
    SHORT sTemp = 0;
    T_DCPara tDCPara;

    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }

    rt_memset(&s_tSysPara, 0, sizeof(T_SysPara));    
    GetSysPara(&s_tSysPara);
    GetBduPara(&tDCPara);
    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    *p++ = GetDataFlag( BCM_ALARM, ucPort );
    sTemp = s_tSysPara.ucUsageScen;	// 使用场景
    *(SHORT*)p  = Host2Modbus((SHORT*)&sTemp);
    p += 2;

    *p++ = s_tSysPara.bUVPTempCompensationEn;
    *p++ = (BOOLEAN)s_tSysPara.ucCellUVPDelay;

    //新增充电最大电流和放电电压 1103 mwl
	*(FLOAT*)p = ((FLOAT)tDCPara.wChgBusCurVal) * g_ProConfig.fRateCurrChgBus / BDU_LIMIT_MAX;
    p += 4;
    *(FLOAT*)p = ((FLOAT)tDCPara.wDischgVolVal) / 100;
    p += 4;

    //新增放电低温告警阈值和放电低温保护阈值 1207 mwl
    *(FLOAT*)p = s_tSysPara.fDischgTempLowAlmThre;
    p += 4;
    *(FLOAT*)p = s_tSysPara.fDischgTempLowPrtThre;
    p += 4;

    *(FLOAT*)p = s_tSysPara.fChargeMaxCurr; // 充电最大电流 s_tSysPara.fChargeMaxCurr
    p += 4;
      *p++ = s_tSysPara.ucRelayDefaultStatus; // 干接点默认状态

    *p++ = BATT_DISCHARGE_FOLLOW;      // 放电方式 R321删除该参数，只支持16串跟随方式放电


    *p++ = s_tSysPara.ucSleepIndicator;     // 休眠指示灯
    *(FLOAT*)p = s_tSysPara.fDischgMaxCurr;		// 放电最大电流 s_tSysPara.fDischgMaxCurr
    p += 4;
    sTemp = s_tSysPara.bBuzzerEnable;
    *(SHORT*)p  = Host2Modbus((SHORT*)&sTemp);
    p += 2;
    *p++ = s_tSysPara.bHeatingPadEnable; // 加热垫使能，默认禁止

    sTemp = (WORD)(s_tSysPara.fSelfDischgACR * 10);	// 自放电异常
    *(SHORT*)p  = Host2Modbus((SHORT*)&sTemp);
    p += 2;

    sTemp = (WORD)(s_tSysPara.fCellTempRiseAbnormalThre * 10);	// 单体温升速率异常
    *(SHORT*)p  = Host2Modbus((SHORT*)&sTemp);
    p += 2;

    *p++ = s_tSysPara.ucDcrFaultAlmThre;     // 直流内阻异常告警阈值
    *p++ = s_tSysPara.ucDcrFaultPrtThre;     // 直流内阻异常保护阈值

     /* LENID */
    s_tProtocol.wSendLenid  = (WORD) ((p-s_tProtocol.aucSendBuf)*2);
    return; 
}

BOOLEAN RangeValidationFloat(FLOAT fData, FLOAT fMinValue, FLOAT fMaxValue) {
    if(fData < fMinValue || fData > fMaxValue)
    {
        return FALSE;
    }
    return TRUE;
}


Static BOOLEAN SetBmsParaByCmdType1(void) {
    FLOAT fPara;
    U_32FLOAT tTemp;
    U_16Int tData;
    T_DCPara tDCPara;

    rt_memset(&tDCPara,0x00,sizeof(T_DCPara));
    tTemp.ucData[0] = s_tProtocol.aucRecBuf[8];
    tTemp.ucData[1] = s_tProtocol.aucRecBuf[9];
    tTemp.ucData[2] = s_tProtocol.aucRecBuf[10];
    tTemp.ucData[3] = s_tProtocol.aucRecBuf[11];

    switch(s_tProtocol.ucCommandType)
    {
        case 0x80:
            tData.ucByte[0] = s_tProtocol.aucRecBuf[9];
            tData.ucByte[1] = s_tProtocol.aucRecBuf[8];
            if(tData.sData < 0)
            {
                s_tProtocol.ucRTN  = RTN_INVALID_DATA;
                return TRUE;
            }
            fPara = (FLOAT)(Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+8)));
            s_tSysPara.ucUsageScen = (BYTE)fPara;
            break;
        case 0x81:
            s_tSysPara.bUVPTempCompensationEn = s_tProtocol.aucRecBuf[8];
            break;
        case 0x82:
            s_tSysPara.ucCellUVPDelay = s_tProtocol.aucRecBuf[8];
            break;
        case 0x83:                                                   //设置充电电流 1103 mwl 
            if(!RangeValidationFloat(tTemp.fData, 3.0f, 100.0f)) {//设置充电电流 1103 mwl 
                s_tProtocol.ucRTN   = RTN_INVALID_DATA;
                return TRUE;
            } 
            GetBduPara(&tDCPara);

            tDCPara.wChgCurVal = (WORD)(g_ProConfig.fRateCurrChg / g_ProConfig.fRateCurrChg * BDU_LIMIT_MAX);//采用千分比类型
            tDCPara.wChgBusCurVal = (WORD)(tTemp.fData / g_ProConfig.fRateCurrChgBus * BDU_LIMIT_MAX);//采用千分比类型

            SetBduPara(&tDCPara);
            BduCtrl(SCI_CTRL_CHG_STG, DISABLE);
            BduCtrl(SCI_CTRL_CHG2CHG, ENABLE);
            BduCtrl(SCI_CTRL_CHG_PRT, DISABLE);
//			GetBduIOPara(&tBduIOPara);
//			tBduIOPara.wChgMaxCurr = tTemp.fData * 10;               //千分比类型
//			setBduIoPara(&tBduIOPara);
//			SetBduCtrl(BDU_CHG_START);
            return TRUE;
        case 0x84:                                                  // 对设置的电压数值不做处理，仅允许放电回路闭合
            if (!RangeValidationFloat(tTemp.fData, 42.0f, 58.0f))
            {
                s_tProtocol.ucRTN = RTN_INVALID_DATA;
                return TRUE;
            }
            GetBduPara(&tDCPara);
            tDCPara.wDischgVolVal = (WORD)(tTemp.fData * 100);
            SetBduPara(&tDCPara);
            BduCtrl(SCI_CTRL_DISCHG_STG, DISABLE);
            BduCtrl(SCI_CTRL_CHG2CHG, DISABLE);
            BduCtrl(SCI_CTRL_DISCHG_PRT, DISABLE);
            return TRUE;
        case 0x85:
            s_tSysPara.fDischgTempLowAlmThre = tTemp.fData; 
            break;
        case 0x86:
            s_tSysPara.fDischgTempLowPrtThre = tTemp.fData;  
            break;
        case 0x87:
            s_tSysPara.fChargeMaxCurr = tTemp.fData;
            break;
        case 0x88:
            s_tSysPara.ucRelayDefaultStatus = s_tProtocol.aucRecBuf[8];
            break;
        // case 0x89:  	
        //	R321删除放电方式参数
        case 0x8A:
            s_tSysPara.ucSleepIndicator = s_tProtocol.aucRecBuf[8];
            break;
        case 0x8B:
            s_tSysPara.fDischgMaxCurr = tTemp.fData;
            break;
        default:
            return FALSE;
    }
    return TRUE;
}

static BOOLEAN SetBmsParaByCmdType2(void) {
    FLOAT fPara;
    U_16Int tData;
    switch(s_tProtocol.ucCommandType)
    {
        case 0x8C:
            tData.ucByte[0] = s_tProtocol.aucRecBuf[9];
            tData.ucByte[1] = s_tProtocol.aucRecBuf[8];
            if(tData.sData < 0)
            {
                s_tProtocol.ucRTN  = RTN_INVALID_DATA;
                return TRUE;
            }
            fPara = (FLOAT)(Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+8)));
            s_tSysPara.bBuzzerEnable = (BYTE)fPara;
            break;
        case 0x8D:
            s_tSysPara.bHeatingPadEnable = s_tProtocol.aucRecBuf[8];; // 加热垫使能
            break;
        case 0x8E:
            fPara = (FLOAT)(Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+8)));
            s_tSysPara.fSelfDischgACR = fPara / 10.0;
            break;
        case 0x8F:
            fPara = (FLOAT)(Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+8)));
            s_tSysPara.fCellTempRiseAbnormalThre = fPara / 10.0;
            break;
        case 0x90:
            s_tSysPara.ucDcrFaultAlmThre = s_tProtocol.aucRecBuf[8];
            break;
        case 0x91:
            s_tSysPara.ucDcrFaultPrtThre = s_tProtocol.aucRecBuf[8];
            break;
        default:
            s_tProtocol.ucRTN  = RTN_WRONG_COMMAND;    /* 命令格式错 */
            return FALSE;
    }
    return TRUE;
}

static BOOLEAN SetBmsParaAll(void)
{
    if(SetBmsParaByCmdType1())
    {
        return TRUE;
    }
    else if(SetBmsParaByCmdType2())
    {
        return TRUE;
    }
    else
    {
        return FALSE;
    }
}

static void SetBmsPara(BYTE ucPort)
{
    BYTE aucDataLen[18] = {2,1,1,4,4,4,4,4,1,1,1,4,2,1,2,2,1,1};

//	T_BduIOParaStruct tBduIOPara;
//	memset(&tBduIOPara,0x00,sizeof(T_BduIOParaStruct));
    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }

    GetSysPara( &s_tSysPara );

    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];
    
    if(s_tProtocol.ucCommandType < 0x80 + sizeof(aucDataLen) && s_tProtocol.ucCommandType >= 0x80)
    {
        if(aucDataLen[s_tProtocol.ucCommandType - 0x80] != (s_tProtocol.wRecLenid / 2 - 1))
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
            return;
        }
    }
    else
    {
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
        return;
    }

    SetBmsParaAll();
    
    if (s_tProtocol.ucRTN == RTN_INVALID_DATA || s_tProtocol.ucRTN == RTN_WRONG_COMMAND) {
        return;
    }
    
    if ( False == SetSysPara( &s_tSysPara, True, CHANGE_BY_APPTEST ) )
    {
        s_tProtocol.ucRTN   = RTN_INVALID_DATA; 
    }

    //设置保存参数缺省值
    GetSysPara( &s_tSysPara );
    WriteBMSDefaultPara( &s_tSysPara );

    return;
}

Static void GetAlarmLevel(BYTE ucPort)
{
    BYTE *p = NULL;
    if (ucPort >= SCI_PORT_NUM)
    {
        return;
    }

    rt_memset(&s_tSysPara, 0, sizeof(T_SysPara));
    GetSysPara(&s_tSysPara);

    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    *p++ = GetDataFlag(BCM_ALARM, ucPort);
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[0];//充电过流保护
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[1];//放电过流保护
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[2];//电池组过压保护
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[3];//单板过温保护
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[4];//充电过流告警
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[5];//放电过流告警
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[6];//电池组过压告警
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[7];//环境温度高告警
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[8];//环境温度低告警
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[9];//电池组欠压告警
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[10];//电池组欠压保护
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[11];//单体过压告警
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[12];//单体过压保护
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[13];//单体欠压告警
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[14];//单体欠压保护
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[15];//充电高温告警
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[16];//充电高温保护
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[17];//放电高温告警
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[18];//放电高温保护
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[19];//充电低温告警
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[20];//充电低温保护
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[21];//放电低温告警
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[22];//放电低温保护
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[23];//单体一致性差告警
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[24];//单体一致性差保护
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[25];//电池SOC低告警
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[26];//电池SOC低保护
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[27];//电池SOH告警
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[28];//电池SOH保护
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[29];//单体损坏保护
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[30];//电池丢失告警
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[31];//BDU EEPROM故障
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[32];//单体电压采样异常
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[33];//充电回路失效
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[34];//放电回路失效
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[35];//限流回路失效
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[36];//电池短路
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[37];//电池反接
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[38];//单体温度无效
    *p++ = 0;
    *p++ = 0; // 机内过温保护已删除    
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[40];//BDU电池欠压保护
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[41];//单体温度异常
    *p++ = 0;
    *p++ = 0; // 地址冲突已删除
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[43];//43-振动告警---不存在该告警
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[44];//BDU母排欠压保护
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[45];//BDU母排过压保护
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[46];//BDU通讯断
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[47];//电压采样故障
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[48];// 回路异常       
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[49];//BDU电池充电欠压保护
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[63];//消防告警
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[64];//激活回路电流异常保护
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[65];//单体温升速率异常
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[66];//直流内阻异常告警
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[67];//直流内阻异常保护
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[68];//自放电异常
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[71];//消防故障告警
    *p++ = 0;
    *p++ = s_tSysPara.aucAlarmLevel[72];//激活口反接告警

    s_tProtocol.wSendLenid = (WORD)((p - s_tProtocol.aucSendBuf) * 2);
    return;
}

static BOOLEAN SetBmsNewParaByCmdType1(void)
{
    U_Int tData;
    U_32FLOAT tTemp;

    tTemp.ucData[0] = s_tProtocol.aucRecBuf[8];
    tTemp.ucData[1] = s_tProtocol.aucRecBuf[9];
    tTemp.ucData[2] = s_tProtocol.aucRecBuf[10];
    tTemp.ucData[3] = s_tProtocol.aucRecBuf[11];

    tData.ucByte[0] = s_tProtocol.aucRecBuf[9];
    tData.ucByte[1] = s_tProtocol.aucRecBuf[8];

    switch(s_tProtocol.ucCommandType)
    {
        case 0x80:
            s_tSysPara.bThroughDischgEnable = s_tProtocol.aucRecBuf[8];
            break;
        case 0x81:
            s_tSysPara.wBattSOCLowAlmThre = tData.iData;
            break;
        case 0x82:
            s_tSysPara.wBattSOCLowPrtThre = tData.iData;
            break;
        case 0x83:
            s_tSysPara.wBattSOHAlmThre = tData.iData;
            break;
        case 0x84:
            s_tSysPara.wBattSOHPrtThre = tData.iData;
            break;
        case 0x85:
            s_tSysPara.fBattUnderVoltAlmThre = tTemp.fData;
            break;
        case 0x86:
            s_tSysPara.fBattUnderVoltPrtThre = tTemp.fData;
            break;
        case 0x87:
            s_tSysPara.fRemoteSupplyOutVolt = tTemp.fData;
            break;
        case 0x88:
            s_tSysPara.fPowerDownVoltThre = tTemp.fData;
            break;
        case 0x8A:
            s_tSysPara.wDischgSwitchSOC = tData.iData;
            break;
        case 0x8B:
            s_tSysPara.wPowerOnDetermineTime = tData.iData;
            break;
        default:
            return FALSE;
    }
    return TRUE;
}

static BOOLEAN SetBmsNewParaByCmdType2(void)
{
    FLOAT fPara = 0.0;
    switch(s_tProtocol.ucCommandType)
    {
        case 0x8C:
            fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8)));
            s_tSysPara.fChgHeaterStartupTemp = fPara; //充电加热膜启动温度
            break;
        case 0x8D:
            fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8)));
            s_tSysPara.fChgHeaterShutdownTemp = fPara; //充电加热膜关闭温度
            break;
        case 0x8E:
            s_tSysPara.ucLocateMode = s_tProtocol.aucRecBuf[8];
            break;
        case 0x8F:
            s_tSysPara.bRelaySyncEn = s_tProtocol.aucRecBuf[8];
            break;
        default:
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
            return FALSE;
    }
    return TRUE;
}

static BOOLEAN SetBmsParaNewAll(void)
{
    if(SetBmsNewParaByCmdType1())
    {
        return TRUE;
    }
    else if(SetBmsNewParaByCmdType2())
    {
        return TRUE;
    }
    else
    {
        return FALSE;
    }
}

static void SetBmsParaNew(BYTE ucPort)
{
    BYTE aucDataLen[] = {1,2,2,2,2,4,4,4,4,1,2,2,2,2,1,1,1};
    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }
    GetSysPara( &s_tSysPara );

    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];
    if(s_tProtocol.ucCommandType < 0x80 + sizeof(aucDataLen) && s_tProtocol.ucCommandType >= 0x80)
    {
        if(aucDataLen[s_tProtocol.ucCommandType - 0x80] != (s_tProtocol.wRecLenid/2 - 1))
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
            return;
        }
    }
    else
    {
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
        return;
    }

    SetBmsParaNewAll();

    if ( !(SetSysPara( &s_tSysPara, True, CHANGE_BY_APPTEST )) )
    {
        s_tProtocol.ucRTN   = RTN_INVALID_DATA; 
    }

    //设置保存参数缺省值
    GetSysPara( &s_tSysPara );
    WriteBMSDefaultPara( &s_tSysPara );

    return;
}

static BOOLEAN  SetSpecificPara_Area1(BYTE ucCommandType, U_Int tData ,U_32FLOAT tTemp)
{
    switch(ucCommandType)
    {
        case 0x80:
            s_tSysPara.ucCellUVPDelay = s_tProtocol.aucRecBuf[8];
            break;
//		case 0x81:
//			s_tSysPara.wDischgSwitchSOC = tData.iData;
//			break;
        case 0x82:
            s_tSysPara.wGPSAntiTheftDistance = tData.iData;
            break;
        case 0x83:
            s_tSysPara.aucAlarmLevel[50] = s_tProtocol.aucRecBuf[9];
            break;
        case 0x84:
            s_tSysPara.aucAlarmLevel[51] = s_tProtocol.aucRecBuf[9];
            break;
        case 0x85:
            s_tSysPara.aucAlarmLevel[52] = s_tProtocol.aucRecBuf[9];
            break;
        case 0x86:
            s_tSysPara.aucAlarmLevel[53] = s_tProtocol.aucRecBuf[9];
            break;
        case 0x87:
            s_tSysPara.aucRelayBit[50] = s_tProtocol.aucRecBuf[9];
            break;
        case 0x88:
            s_tSysPara.aucRelayBit[51] = s_tProtocol.aucRecBuf[9];
            break;
        case 0x89:
            s_tSysPara.aucRelayBit[52] = s_tProtocol.aucRecBuf[9];
            break;
        case 0x8A:
            s_tSysPara.aucRelayBit[53] = s_tProtocol.aucRecBuf[9];
            break;
        default:
            return FALSE;
    }
    return TRUE;
}

static BOOLEAN  SetSpecificPara_Area2(BYTE ucCommandType, U_Int tData ,U_32FLOAT tTemp)
{
    switch(ucCommandType)
    {
        case 0x8B: ///单体欠压告警阈值
            s_tSysPara.fCellUnderVoltAlmThre = tData.iData / 100.0f;
            break;
        case 0x8C: ///单体欠压保护阈值
            s_tSysPara.fCellUnderVoltPrtThre = tData.iData / 100.0f;
            break;
        case 0x8D:
            s_tSysPara.bChargeRotate = s_tProtocol.aucRecBuf[8];
            break;
        case 0x8E: /// 软件防盗延时
            s_tSysPara.wSoftAntiTheftDelay = tData.iData;
            break;
        case 0x8F:
            s_tSysPara.fCellOverVoltPrtThre = tTemp.fData;
            break;
        case 0x90:
            s_tSysPara.fCellChargeFullVolt = tTemp.fData;
            break;
        case 0x91:
            s_tSysPara.ucHisDataType = s_tProtocol.aucRecBuf[8];
            break;
        case 0x92:
            s_tSysPara.wBattShutdownTime = tData.iData;
            break;
        case 0x93:
            s_tSysPara.bBusVoltActiveEn = s_tProtocol.aucRecBuf[8];
            break;
        case 0x94:
            if(GetTotalAddr() + s_tProtocol.aucRecBuf[8] > 255)
            {//modbus通信地址不可大于255
                s_tProtocol.ucRTN   = RTN_INVALID_DATA;
                return TRUE;
            }
            s_tSysPara.ucModbusAddr = s_tProtocol.aucRecBuf[8];
            break;
        case 0x95:
            s_tSysPara.bSagEqualCurr = s_tProtocol.aucRecBuf[8];
            break;
        case 0x96:
            s_tSysPara.bGyroscopeSensitivity = s_tProtocol.aucRecBuf[8];
            break;
        default:
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
            return FALSE;
    }
    return TRUE;
}

static void SetSpecificPara(BYTE ucPort)
{
    U_Int tData;
    U_32FLOAT tTemp;
    BYTE aucDataLen[23] = {1,2,2,2,2,2,2,2,2,2,2,2,2,1,2,4,4,1,2,1,1,1,1};
    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }
    GetSysPara( &s_tSysPara );

    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];
    tTemp.ucData[0] = s_tProtocol.aucRecBuf[8];
    tTemp.ucData[1] = s_tProtocol.aucRecBuf[9];
    tTemp.ucData[2] = s_tProtocol.aucRecBuf[10];
    tTemp.ucData[3] = s_tProtocol.aucRecBuf[11];
    if(s_tProtocol.ucCommandType < 0x80 + sizeof(aucDataLen) && s_tProtocol.ucCommandType >= 0x80)
    {
        if(aucDataLen[s_tProtocol.ucCommandType - 0x80] != s_tProtocol.wRecLenid / 2 - 1)
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
            return;
        }
    // 屏蔽放电切换SOC的设置
        if(s_tProtocol.ucCommandType == 0X81) // 无效参数，禁止设置
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
            return;
        }
    }
    else
    {
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
        return;
    }
    
    tData.ucByte[0] = s_tProtocol.aucRecBuf[9];
    tData.ucByte[1] = s_tProtocol.aucRecBuf[8];

    if(TRUE != SetSpecificPara_Area1(s_tProtocol.ucCommandType, tData, tTemp))
    {
        SetSpecificPara_Area2(s_tProtocol.ucCommandType, tData, tTemp);  
    }

    if (s_tProtocol.ucRTN == RTN_WRONG_COMMAND || s_tProtocol.ucRTN == RTN_INVALID_DATA) {
        return;
    }

    if ( !(SetSysPara( &s_tSysPara, True, CHANGE_BY_APPTEST )) )
    {
        s_tProtocol.ucRTN   = RTN_INVALID_DATA; 
    }

    //设置保存参数缺省值
    GetSysPara( &s_tSysPara );
    WriteBMSDefaultPara( &s_tSysPara );

    return;
}

static BYTE SetAlarmLevel_CmdType8x(BYTE ucPara) {
    switch (s_tProtocol.ucCommandType){
        case 0x80:
            s_tSysPara.aucAlarmLevel[0] = ucPara;//充电过流保护:1
            break;
        case 0x81:
            s_tSysPara.aucAlarmLevel[1] = ucPara;//放电过流保护:2
            break;
        case 0x82:
            s_tSysPara.aucAlarmLevel[2] = ucPara;//电池组过压保护:3
            break;
        case 0x83:
            s_tSysPara.aucAlarmLevel[3] = ucPara;//单板过温保护:4
            break;
        case 0x84:
            s_tSysPara.aucAlarmLevel[4] = ucPara;//充电过流告警:5
            break;
        case 0x85:
            s_tSysPara.aucAlarmLevel[5] = ucPara;//放电过流告警:6
            break;
        case 0x86:
            s_tSysPara.aucAlarmLevel[6] = ucPara;//电池组过压告警:7
            break;
        case 0x89:
            s_tSysPara.aucAlarmLevel[9] = ucPara;//电池组欠压告警:10
            break;
        case 0x8A:
            s_tSysPara.aucAlarmLevel[10] = ucPara;//电池组欠压保护:11
            break;
        case 0x8B:
            s_tSysPara.aucAlarmLevel[11] = ucPara;//单体过压告警:12
            break;
        case 0x8C:
            s_tSysPara.aucAlarmLevel[12] = ucPara;//单体过压保护:13
            break;
        case 0x8D:
            s_tSysPara.aucAlarmLevel[13] = ucPara;//单体欠压告警:14
            break;
        case 0x8E:
            s_tSysPara.aucAlarmLevel[14] = ucPara;//单体欠压保护:15
            break;
        case 0x8F:
            s_tSysPara.aucAlarmLevel[15] = ucPara;//充电高温告警:16
            break;
        default:
            return False;
    }
    return True;
}

static BYTE SetAlarmLevel_CmdType9x(BYTE ucPara) {
    switch (s_tProtocol.ucCommandType){
        case 0x90:
            s_tSysPara.aucAlarmLevel[16] = ucPara;//充电高温保护:17
            break;
        case 0x91:
            s_tSysPara.aucAlarmLevel[17] = ucPara;//放电高温告警:18
            break;
        case 0x92:
            s_tSysPara.aucAlarmLevel[18] = ucPara;//放电高温保护:19
            break;
        case 0x93:
            s_tSysPara.aucAlarmLevel[19] = ucPara;//充电低温告警:20
            break;
        case 0x94:
            s_tSysPara.aucAlarmLevel[20] = ucPara;//充电低温保护:21
            break;
        case 0x95:
            s_tSysPara.aucAlarmLevel[21] = ucPara;//放电低温告警:22
            break;
        case 0x96:
            s_tSysPara.aucAlarmLevel[22] = ucPara;//放电低温保护:23
            break;
        case 0x97:
            s_tSysPara.aucAlarmLevel[23] = ucPara;//单体一致性差告警:24
            break;
        case 0x98:
            s_tSysPara.aucAlarmLevel[24] = ucPara;//单体一致性差保护:25
            break;
        case 0x99:
            s_tSysPara.aucAlarmLevel[25] = ucPara;//电池SOC低告警:26
            break;
        case 0x9A:
            s_tSysPara.aucAlarmLevel[26] = ucPara;//电池SOC低保护:27
            break;
        case 0x9B:
            s_tSysPara.aucAlarmLevel[27] = ucPara;//电池SOH告警:28
            break;
        case 0x9C:
            s_tSysPara.aucAlarmLevel[28] = ucPara;//电池SOH保护:29
            break;
        case 0x9D:
            s_tSysPara.aucAlarmLevel[29] = ucPara;//单体损坏保护:30
            break;
        case 0x9E:
            s_tSysPara.aucAlarmLevel[30] = ucPara;//电池丢失告警:31
            break;
        case 0x9F:
            s_tSysPara.aucAlarmLevel[31] = ucPara;//BDU EEPROM故障:32
            break;
        default:
            return False;
    }
    return True;
}

static BYTE SetAlarmLevel_CmdTypeAx(BYTE ucPara) {
    switch (s_tProtocol.ucCommandType){
        case 0xA0:
            s_tSysPara.aucAlarmLevel[32] = ucPara;//单体电压采样异常:33
            break;
        case 0xA1:
            s_tSysPara.aucAlarmLevel[33] = ucPara;//充电回路失效:34
            break;
        case 0xA2:
            s_tSysPara.aucAlarmLevel[34] = ucPara;//放电回路失效:35
            break;
        case 0xA3:
            s_tSysPara.aucAlarmLevel[35] = ucPara;//限流回路失效:36
            break;
        case 0xA4:
            s_tSysPara.aucAlarmLevel[36] = ucPara;//电池短路:37
            break;
        case 0xA5:
            s_tSysPara.aucAlarmLevel[37] = ucPara;//电池反接:38
            break;
        case 0xA6:
            s_tSysPara.aucAlarmLevel[38] = ucPara;//单体温度无效:39
            break;
        // case 0xA7:
        //     // 机内过温保护已删除
        //     break;
        case 0xA8:
            s_tSysPara.aucAlarmLevel[40] = ucPara;//BDU电池欠压保护:41
            break;
        case 0xA9:
            s_tSysPara.aucAlarmLevel[41] = ucPara;//单体温度异常:42
            break;
        // case 0xAA:
        //     // 地址冲突已删除
        //     break;
        case 0xAB:
            s_tSysPara.aucAlarmLevel[43] = ucPara;//振动告警-参数列表无
            break;
        case 0xAC:
            s_tSysPara.aucAlarmLevel[44] = ucPara;//BDU母排欠压保护:45
            break;
        case 0xAD:
            s_tSysPara.aucAlarmLevel[45] = ucPara;//BDU母排过压保护:46
            break;
        case 0xAE:
            s_tSysPara.aucAlarmLevel[46] = ucPara;//BDU通讯断:47
            break;
        case 0xAF:
            s_tSysPara.aucAlarmLevel[47] = ucPara;//电压采样故障-参数列表无
            break;
        default:
            return False;
    }
    return True;
}

Static BYTE SetAlarmLevel_CmdTypeBx(BYTE ucPara) {
    switch (s_tProtocol.ucCommandType){
        case 0xB0:
             s_tSysPara.aucAlarmLevel[48] = ucPara; //回路异常
             break;
        case 0xB1:
            s_tSysPara.aucAlarmLevel[49] = ucPara;//BDU电池充电欠压保护
            break;
        case 0xB2:
            s_tSysPara.aucAlarmLevel[63] = ucPara;//消防告警
            break;
        case 0xB3:
            s_tSysPara.aucAlarmLevel[64] = ucPara;//激活回路电流异常保护
            break;
        case 0xB4:
            s_tSysPara.aucAlarmLevel[65] = ucPara;//单体温升速率异常
            break;
        case 0xB5:
            s_tSysPara.aucAlarmLevel[66] = ucPara;//直流内阻异常告警
            break;
        case 0xB6:
            s_tSysPara.aucAlarmLevel[67] = ucPara;//直流内阻异常保护
            break;
        case 0xB7:
            s_tSysPara.aucAlarmLevel[68] = ucPara;//自放电异常
            break;
        case 0xB8:
            s_tSysPara.aucAlarmLevel[71] = ucPara;//消防故障告警
            break;
        case 0xB9:
            s_tSysPara.aucAlarmLevel[72] = ucPara;//激活口反接告警
            break;
        default:
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;  /* 命令格式错 */
            return False;
    }
    return True;
}

static void SetAlarmLevelAll(BYTE ucPara) {
    if (SetAlarmLevel_CmdType8x(ucPara)) {
        return;
    }
    if (SetAlarmLevel_CmdType9x(ucPara)) {
        return;
    }
    if (SetAlarmLevel_CmdTypeAx(ucPara)) {
        return;
    }
    if (SetAlarmLevel_CmdTypeBx(ucPara)) {
        return;
    }
    return;
}

Static void SetAlarmLevel(BYTE ucPort)
{  
    BYTE ucPara;

    GetSysPara( &s_tSysPara );

    s_tProtocol.ucCommandType	= s_tProtocol.aucRecBuf[7];
    ucPara = s_tProtocol.aucRecBuf[9]; 	 
    //屏蔽环境温度高和环境温度低告警级别的设置						mwl 
    if(s_tProtocol.ucCommandType == 0x87 || s_tProtocol.ucCommandType == 0x88)
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;  
        return;
    }

    SetAlarmLevelAll(ucPara);

    if (s_tProtocol.ucRTN == RTN_INVALID_DATA || s_tProtocol.ucRTN == RTN_WRONG_COMMAND) {
        return;
    }

    if ( False == SetSysPara( &s_tSysPara, True, CHANGE_BY_APPTEST ) )
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;	
    }

    //设置保存参数缺省值
    GetSysPara( &s_tSysPara );
    WriteBMSDefaultPara( &s_tSysPara );

    return;
}

static void SetBMSChgVolt(BYTE ucPort)
{
    U_Int tData;
    T_DCPara tDCPara;
    BYTE ucDataLen = 2;
    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }

    rt_memset(&tDCPara, 0, sizeof(T_DCPara));
    GetBduPara(&tDCPara);

    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];

    tData.ucByte[0] = s_tProtocol.aucRecBuf[9];
    tData.ucByte[1] = s_tProtocol.aucRecBuf[8];

    if (s_tProtocol.ucCommandType == 0x80)
    {
        if(ucDataLen != s_tProtocol.wRecLenid / 2 - 1)
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
            return;
        }
        if(tData.iData / 100.0f < 37.0f || tData.iData / 100.0f > 58.0f)
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
            return;
        }

        tDCPara.wChgVolVal = tData.iData;
    }
    else
    {
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND; /*wrong command format*/
        return;
    }
    SetBduPara(&tDCPara);
    return;
}

Static void GetAlarmRelay(BYTE ucPort)
{
    BYTE *p = NULL;

    rt_memset(&s_tSysPara, 0, sizeof(T_SysPara));
    GetSysPara(&s_tSysPara);

    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    *p++ = GetDataFlag(BCM_ALARM, ucPort);

    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[0];//1-充电过流保护:1
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[1];//2-放电过流保护:2
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[2];//3-电池组过压保护:3
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[3];//4-单板过温保护:4
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[4];//5-充电过流告警:5
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[5];//6-放电过流告警:6
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[6];//7-电池组过压告警:7
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[7];//8-环境温度高告警:8
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[8];//9-环境温度低告警:9
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[9];//10-电池组欠压告警:10
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[10];//11-电池组欠压保护:11
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[11];//12-单体过压告警:12
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[12];//13-单体过压保护:13
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[13];//14-单体欠压告警:14
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[14];//15-单体欠压保护:15
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[15];//16-充电高温告警:16
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[16];//17-充电高温保护:17
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[17];//18-放电高温告警:18
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[18];//19-放电高温保护:19
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[19];//20-充电低温告警:20
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[20];//21-充电低温保护:21
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[21];//22-放电低温告警:22
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[22];//23-放电低温保护:23
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[23];//24-单体一致性差告警:24
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[24];//25-单体一致性差保护:25
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[25];//26-电池SOC低告警:26
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[26];//27-电池SOC低保护:27
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[27];//28-电池SOH告警:28
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[28];//29-电池SOH保护:29
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[29];//30-单体损坏保护:30
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[30];//31-电池丢失告警:31
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[31];//32-BDU EEPROM故障:32
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[32];//33-单体电压采样异常:33
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[33];//34-充电回路失效:34
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[34];//35-放电回路失效:35
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[35];//36-限流回路失效:36
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[36];//37-电池短路:37
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[37];//38-电池反接:38
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[38];//39-单体温度无效:39
    *p++ = 0;
    *p++ = 0; // 机内过温保护已删除
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[40];//41-BDU电池欠压保护:41
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[41];//单体温度异常
    *p++ = 0;
    *p++ = 0; // 地址冲突已删除
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[43];//44-振动告警-参数表中无该字段
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[44];//45-BDU母排欠压保护:45
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[45];//46-BDU母排过压保护:46
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[46];//47-BDU通讯断:47
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[47];//48-电压采样故障-参数表中无该字段
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[48]; // 回路异常
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[49];//50-BDU电池充电欠压保护:50
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[63];//消防告警:63
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[64];//激活回路电流异常保护:64
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[65];//单体温升速率异常:65
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[66];//直流内阻异常告警
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[67];//直流内阻异常保护
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[68];//自放电异常
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[71];//消防故障告警
    *p++ = 0;
    *p++ = s_tSysPara.aucRelayBit[72];//激活口反接告警 

    s_tProtocol.wSendLenid = (WORD)((p - s_tProtocol.aucSendBuf) * 2);
    return;
}

static BYTE SetAlarmRelay_CmdType8x(BYTE ucPara) {
    switch (s_tProtocol.ucCommandType){
        case 0x80:
            s_tSysPara.aucRelayBit[0] = ucPara;//充电过流保护
            break;
        case 0x81:
            s_tSysPara.aucRelayBit[1] = ucPara;//放电过流保护
            break;
        case 0x82:
            s_tSysPara.aucRelayBit[2] = ucPara;//电池组过压保护
            break;
        case 0x83:
            s_tSysPara.aucRelayBit[3] = ucPara;//单板过温保护
            break;
        case 0x84:
            s_tSysPara.aucRelayBit[4] = ucPara;//充电过流告警
            break;
        case 0x85:
            s_tSysPara.aucRelayBit[5] = ucPara;//放电过流告警
            break;
        case 0x86:
            s_tSysPara.aucRelayBit[6] = ucPara;//电池组过压告警
            break;
        case 0x89:
            s_tSysPara.aucRelayBit[9] = ucPara;//电池组欠压告警
            break;
        case 0x8A:
            s_tSysPara.aucRelayBit[10] = ucPara;//电池组欠压保护
            break;
        case 0x8B:
            s_tSysPara.aucRelayBit[11] = ucPara;//单体过压告警
            break;
        case 0x8C:
            s_tSysPara.aucRelayBit[12] = ucPara;//单体过压保护
            break;
        case 0x8D:
            s_tSysPara.aucRelayBit[13] = ucPara;//单体欠压告警
            break;
        case 0x8E:
            s_tSysPara.aucRelayBit[14] = ucPara;//单体欠压保护
            break;
        case 0x8F:
            s_tSysPara.aucRelayBit[15] = ucPara;//充电高温告警
            break;
        default:
            return False;
    }
    return True;
}

static BYTE SetAlarmRelay_CmdType9x(BYTE ucPara) {
    switch (s_tProtocol.ucCommandType){
        case 0x90:
            s_tSysPara.aucRelayBit[16] = ucPara;//充电高温保护
            break;
        case 0x91:
            s_tSysPara.aucRelayBit[17] = ucPara;//放电高温告警
            break;
        case 0x92:
            s_tSysPara.aucRelayBit[18] = ucPara;//放电高温保护
            break;
        case 0x93:
            s_tSysPara.aucRelayBit[19] = ucPara;//充电低温告警
            break;
        case 0x94:
            s_tSysPara.aucRelayBit[20] = ucPara;//充电低温保护
            break;
        case 0x95:
            s_tSysPara.aucRelayBit[21] = ucPara;//放电低温告警
            break;
        case 0x96:
            s_tSysPara.aucRelayBit[22] = ucPara;//放电低温保护
            break;
        case 0x97:
            s_tSysPara.aucRelayBit[23] = ucPara;//单体一致性差告警
            break;
        case 0x98:
            s_tSysPara.aucRelayBit[24] = ucPara;//单体一致性差保护
            break;
        case 0x99:
            s_tSysPara.aucRelayBit[25] = ucPara;//电池SOC低告警
            break;
        case 0x9A:
            s_tSysPara.aucRelayBit[26] = ucPara;//电池SOC低保护
            break;
        case 0x9B:
            s_tSysPara.aucRelayBit[27] = ucPara;//电池SOH告警
            break;
        case 0x9C:
            s_tSysPara.aucRelayBit[28] = ucPara;//电池SOH保护
            break;
        case 0x9D:
            s_tSysPara.aucRelayBit[29] = ucPara;//单体损坏保护
            break;
        case 0x9E:
            s_tSysPara.aucRelayBit[30] = ucPara;//电池丢失告警
            break;
        case 0x9F:
            s_tSysPara.aucRelayBit[31] = ucPara;//BDU EEPROM故障
            break;
        default:
        return False;
    }
    return True;
}

static BYTE SetAlarmRelay_CmdTypeAx(BYTE ucPara) {
    switch (s_tProtocol.ucCommandType){
        case 0xA0:
            s_tSysPara.aucRelayBit[32] = ucPara;//单体电压采样异常
            break;
        case 0xA1:
            s_tSysPara.aucRelayBit[33] = ucPara;//充电回路失效
            break;
        case 0xA2:
            s_tSysPara.aucRelayBit[34] = ucPara;//放电回路失效
            break;
        case 0xA3:
            s_tSysPara.aucRelayBit[35] = ucPara;//限流回路失效
            break;
        case 0xA4:
            s_tSysPara.aucRelayBit[36] = ucPara;//电池短路
            break;
        case 0xA5:
            s_tSysPara.aucRelayBit[37] = ucPara;//电池反接
            break;
        case 0xA6:
            s_tSysPara.aucRelayBit[38] = ucPara;//单体温度无效
            break;
        // case 0xA7:
        //     // 机内过温保护已删除
        //     break;
        case 0xA8:
            s_tSysPara.aucRelayBit[40] = ucPara;//BDU电池欠压保护
            break;
        case 0xA9:
            s_tSysPara.aucRelayBit[41] = ucPara;//单体温度异常
            break;
        // case 0xAA:
        //     // 地址冲突已删除
        //     break;
        case 0xAB:
            s_tSysPara.aucRelayBit[43] = ucPara;//振动告警:参数表无，应该写为0或新增参数
            break;
        case 0xAC:
            s_tSysPara.aucRelayBit[44] = ucPara;//BDU母排欠压保护
            break;
        case 0xAD:
            s_tSysPara.aucRelayBit[45] = ucPara;//BDU母排过压保护
            break;
        case 0xAE:
            s_tSysPara.aucRelayBit[46] = ucPara;//BDU通讯断
            break;
        case 0xAF:
            s_tSysPara.aucRelayBit[47] = ucPara;//电压采样故障:参数表无，应该写为0或新增参数
            break;
        default:
            return False;
    }
    return True;
}

static BYTE SetAlarmRelay_CmdTypeBx(BYTE ucPara) {
    switch (s_tProtocol.ucCommandType){
        case 0xB0:
         s_tSysPara.aucRelayBit[48] = ucPara;//回路异常
            break;
        case 0xB1:
            s_tSysPara.aucRelayBit[49] = ucPara;//BDU电池充电欠压保护
            break;
        case 0xB2:
            s_tSysPara.aucRelayBit[63] = ucPara;//消防告警
            break;
        case 0xB3:
            s_tSysPara.aucRelayBit[64] = ucPara;//激活回路电流异常保护
            break;
        case 0xB4:
            s_tSysPara.aucRelayBit[65] = ucPara;//单体温升速率异常
            break;
        case 0xB5:
            s_tSysPara.aucRelayBit[66] = ucPara;//直流内阻异常告警
            break;
        case 0xB6:
            s_tSysPara.aucRelayBit[67] = ucPara;//直流内阻异常保护
            break;
        case 0xB7:
            s_tSysPara.aucRelayBit[68] = ucPara;//自放电异常
            break;
        case 0xB8:
            s_tSysPara.aucRelayBit[71] = ucPara;//消防故障告警
            break;
        case 0xB9:
            s_tSysPara.aucRelayBit[72] = ucPara;//激活口反接告警
            break;
        default:
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;  /* 命令格式错 */
            return False;
    }
    return True;
}

static void SetAlarmRelayAll(BYTE ucPara) {
    if (SetAlarmRelay_CmdType8x(ucPara)) {
        return;
    }
    if (SetAlarmRelay_CmdType9x(ucPara)) {
        return;
    }
    if (SetAlarmRelay_CmdTypeAx(ucPara)) {
        return;
    }
    if (SetAlarmRelay_CmdTypeBx(ucPara)) {
        return;
    }
    return;
}

Static void SetAlarmRelay(BYTE ucPort)
{

    BYTE ucPara;
    GetSysPara( &s_tSysPara );
    s_tProtocol.ucCommandType	= s_tProtocol.aucRecBuf[7];
    ucPara = s_tProtocol.aucRecBuf[9]; 

    SetAlarmRelayAll(ucPara);

    if (s_tProtocol.ucRTN == RTN_INVALID_DATA || s_tProtocol.ucRTN == RTN_WRONG_COMMAND) {
        return;
    }

    if ( False == SetSysPara( &s_tSysPara, True, CHANGE_BY_APPTEST ) )
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
    }

    //设置保存参数缺省值
    GetSysPara( &s_tSysPara );
    WriteBMSDefaultPara( &s_tSysPara );

    return;
}


static void CheckSms(BYTE ucPort)
{
    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }
    TestSms((CHAR*)&s_tProtocol.aucRecBuf[7]); //to realize iterate2
    return;
}


static void GetGPRSStatus(BYTE ucPort)
{
    BYTE  *p = NULL;
    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    p  = s_tProtocol.aucSendBuf;
    *p++ = ApptestGetGprsState(); //to realize iterate2
    s_tProtocol.wSendLenid = 2;
    *p = 0;  //解决KW4级问题添加 对业务无影响 1106
    return;
}

// static void SetBMSAdjustDataNew(BYTE ucPort)
// {
//     WORD wTemp;
//     U_32FLOAT tTemp;

//     if ( ucPort >= SCI_PORT_NUM )
//     {
//         return;
//     }
//     s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];

//     if(s_tProtocol.ucCommandType >= 0x80)
//     {
//         tTemp.ucData[0] = s_tProtocol.aucRecBuf[8];
//         tTemp.ucData[1] = s_tProtocol.aucRecBuf[9];
//         tTemp.ucData[2] = s_tProtocol.aucRecBuf[10];
//         tTemp.ucData[3] = s_tProtocol.aucRecBuf[11];
//         wTemp = (WORD)tTemp.fData;
//         BduAdj(s_tProtocol.ucCommandType, wTemp);
//     }
//     else
//     {
//          s_tProtocol.ucRTN  = RTN_WRONG_COMMAND;    // 命令格式错
//          return;
//     }
//     return;
// }


BOOLEAN GetGpsStatus(BYTE ucPort)
{
    BYTE  *p = NULL;
    T_GpsData tGpsData = {0}; // 初始化tGpsData

    if ( ucPort >= SCI_PORT_NUM )
    {
        return False;
    }
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    p  = s_tProtocol.aucSendBuf;
    *p++ = getPlaceForApptest(&tGpsData); //to realize iterate2
    *p = 0; //to solve KW4 problems
    s_tProtocol.wSendLenid = 2;

    return True;
}


static void GetBmsParaNew(BYTE ucPort)
{
    BYTE *p = NULL;
    WORD wSendLen = 0;
    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }
    rt_memset(&s_tSysPara, 0, sizeof(T_SysPara));    
    GetSysPara(&s_tSysPara);
    p = s_tProtocol.aucSendBuf;
    *p++ = GetDataFlag( BCM_ALARM, ucPort );
    wSendLen++;
    *p++ = s_tSysPara.bThroughDischgEnable;
    wSendLen++;
    *(SHORT*)p  = Host2Modbus((SHORT*)&s_tSysPara.wBattSOCLowAlmThre);
    p += 2;
    wSendLen += 2;
    *(SHORT*)p  = Host2Modbus((SHORT*)&s_tSysPara.wBattSOCLowPrtThre);
    p += 2;
    wSendLen += 2;
    *(SHORT*)p  = Host2Modbus((SHORT*)&s_tSysPara.wBattSOHAlmThre);
    p += 2;
    wSendLen += 2;
    *(SHORT*)p  = Host2Modbus((SHORT*)&s_tSysPara.wBattSOHPrtThre);
    p += 2;
    wSendLen += 2;
    *(FLOAT*)p = s_tSysPara.fBattUnderVoltAlmThre;
    p += 4;
    wSendLen += 4;
    *(FLOAT*)p = s_tSysPara.fBattUnderVoltPrtThre;
    p += 4;
    wSendLen += 4;
    *(FLOAT*)p = s_tSysPara.fRemoteSupplyOutVolt;
    p += 4;
    wSendLen += 4;
    *(FLOAT*)p = s_tSysPara.fPowerDownVoltThre;
    wSendLen += 4;
    p += 4;
    *p++ = 0;  // 删除节能功能
    wSendLen++;
    *(SHORT*)p  = Host2Modbus((SHORT*)&s_tSysPara.wDischgSwitchSOC);
    p += 2;
    wSendLen += 2;
    *(SHORT*)p  = Host2Modbus((SHORT*)&s_tSysPara.wPowerOnDetermineTime);//来电判断时间
    p += 2;
    wSendLen += 2;
    *(SHORT*)p  = FloatChangeToModbus(s_tSysPara.fChgHeaterStartupTemp);//充电加热膜启动温度
    p += 2;
    wSendLen += 2;
    *(SHORT*)p  = FloatChangeToModbus(s_tSysPara.fChgHeaterShutdownTemp);//充电加热膜关闭温度
    p += 2;
    wSendLen += 2;
    *p++ = s_tSysPara.ucLocateMode;
    wSendLen++;
    *p++ = s_tSysPara.bRelaySyncEn;
    wSendLen++;
    *p++ = 0;      //删除节能功能
    wSendLen++;
    s_tProtocol.wSendLenid = wSendLen*2;
    return;
}

static void GetSpecificPara(BYTE ucPort)
{
    BYTE *p = NULL;
    WORD wSendLen = 0;
    SHORT slTemp = 0;
    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }
    rt_memset(&s_tSysPara, 0, sizeof(T_SysPara));    
    GetSysPara(&s_tSysPara);
    p = s_tProtocol.aucSendBuf;
    *p++ = GetDataFlag( BCM_ALARM, ucPort );
    wSendLen++;
    *p++ = s_tSysPara.ucCellUVPDelay;
    wSendLen++;
    // *(SHORT*)p = Host2Modbus((SHORT*)&s_tSysPara.wDischgSwitchSOC);
    *(SHORT*)p = 0;
    p += 2;
    wSendLen += 2;

    *(SHORT*)p = Host2Modbus((SHORT*)&s_tSysPara.wGPSAntiTheftDistance);

    p += 2;
    wSendLen += 2;

    *p++ = 0; //高字节填充0
    *p++ = s_tSysPara.aucAlarmLevel[50];//BDU电池闭锁告警:51
    wSendLen += 2;

    *p++ = 0; //高字节填充0
    *p++ = s_tSysPara.aucAlarmLevel[51];//环境温度高保护:52
    wSendLen += 2;

    *p++ = 0; //高字节填充0
    *p++ = s_tSysPara.aucAlarmLevel[52];//环境温度低保护:53
    wSendLen += 2;

    *p++ = 0; //高字节填充0
    *p++ = s_tSysPara.aucAlarmLevel[53];//单板过温告警:54
    wSendLen += 2;

    *p++ = 0; //高字节填充0
    *p++ = s_tSysPara.aucRelayBit[50];//BDU电池闭锁告警（告警干接点）:51
    wSendLen += 2;

    *p++ = 0; //高字节填充0
    *p++ = s_tSysPara.aucRelayBit[51];//环境温度高保护（告警干接点）:52
    wSendLen += 2;

    *p++ = 0; //高字节填充0
    *p++ = s_tSysPara.aucRelayBit[52];//环境温度低保护（告警干接点）:53
    wSendLen += 2;

    *p++ = 0; //高字节填充0
    *p++ = s_tSysPara.aucRelayBit[53];//单板过温告警（告警干接点）:54
    wSendLen += 2;

    slTemp = s_tSysPara.fCellUnderVoltAlmThre * 100;
    *(SHORT*)p = Host2Modbus((SHORT*)&slTemp);
    p += 2;
    wSendLen += 2;

    slTemp = s_tSysPara.fCellUnderVoltPrtThre * 100;
    *(SHORT*)p = Host2Modbus((SHORT*)&slTemp);
    p += 2;
    wSendLen += 2;

    *p++ = s_tSysPara.bChargeRotate;
    wSendLen++;

    slTemp  = Host2Modbus((SHORT*)&s_tSysPara.wSoftAntiTheftDelay);
    rt_memcpy(p, &slTemp, sizeof(slTemp));
    p += 2;
    wSendLen += 2;

    rt_memcpy(p, (BYTE*)&s_tSysPara.fCellOverVoltPrtThre, sizeof(s_tSysPara.fCellOverVoltPrtThre));
    p += 4;
    wSendLen += 4;

    rt_memcpy(p, (BYTE*)&s_tSysPara.fCellChargeFullVolt, sizeof(s_tSysPara.fCellChargeFullVolt));
    p += 4;
    wSendLen += 4;

    *p++ = s_tSysPara.ucHisDataType;
    wSendLen++;

    slTemp  = Host2Modbus((SHORT*)&s_tSysPara.wBattShutdownTime);  ///静置关机时间
    rt_memcpy(p, &slTemp, sizeof(slTemp));
    p += 2;
    wSendLen += 2;

    *p++ = s_tSysPara.bBusVoltActiveEn;  //电压激活使能
    wSendLen++;

    *p++ = s_tSysPara.ucModbusAddr; // Modbus基地址
    wSendLen++;

    *p++ = s_tSysPara.bSagEqualCurr;
    wSendLen++;

    *p++ = s_tSysPara.bGyroscopeSensitivity;
    wSendLen++;

    p += 18;  // 预留字节
    wSendLen += 18;

    s_tProtocol.wSendLenid = wSendLen*2;
    return;
}


static void GetBMSAdjustData(BYTE ucPort)
{
    T_DCCaliGet t_AdjPara;
    T_DCRealData tDcRealData;
    T_DCTestData tDCTestData;
    BYTE *p = NULL;
    WORD wSendLen = 0;
    BYTE i = 0;

    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }

    rt_memset(&tDcRealData, 0, sizeof(T_DCRealData));
    rt_memset(&t_AdjPara, 0, sizeof(T_DCCaliGet));
    rt_memset(&tDCTestData, 0, sizeof(T_DCTestData));

    GetBduReal(&tDcRealData);
    GetBduAdj(&t_AdjPara);
    GetBduTestData(&tDCTestData);

    p = s_tProtocol.aucSendBuf;
    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];
    switch(s_tProtocol.ucCommandType)
    {
        case 0X80: // 访问校正值
            if(t_AdjPara.wBduAdjNum >= SCI_BDUADJ_MAX_LEN)
            {
                return;
            }
            *p++ = t_AdjPara.wBduAdjNum;
            wSendLen += 1;
            for(i = 0; i < t_AdjPara.wBduAdjNum; i++)
            {
                *(SHORT*)p = Host2Modbus((SHORT*)&t_AdjPara.awBduAdjValue[i]);
                p+=2;
                wSendLen += 2;
            }
            break;
        case 0X81: // 访问模拟量
            *p++ = 5;
            *(SHORT*)p = Host2Modbus((SHORT*)&tDcRealData.tDCAnalag.sBatCur);
            p += 2;
            *(SHORT*)p = Host2Modbus((SHORT*)&tDcRealData.tDCAnalag.wBatVol);
            p += 2;
            *(SHORT*)p = Host2Modbus((SHORT*)&tDcRealData.tDCAnalag.sBusCur);
            p += 2;
            *(SHORT*)p = Host2Modbus((SHORT*)&tDcRealData.tDCAnalag.wBusVol);
            p += 2;
            *(SHORT*)p = Host2Modbus((SHORT*)&tDCTestData.sBusPinTestVol);
            p += 2;
            wSendLen += 11;
            break;
        default:break;
    }

    s_tProtocol.wSendLenid = wSendLen*2;
}


static void GetIMEICode(BYTE ucPort)
{
    if (ucPort >= SCI_PORT_NUM)
    {
        return;
    }

    T_IMEI tIMEI;
    rt_memset_s(&tIMEI, sizeof(T_IMEI), 0, sizeof(T_IMEI));
    GetIMEI(&tIMEI);

    BYTE *p = NULL;
    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));
    p = s_tProtocol.aucSendBuf;
    MemsetBuff(p, tIMEI.acIMEI, rt_strnlen_s(tIMEI.acIMEI, 20), 20, 0x20);
    p += 20;

    s_tProtocol.wSendLenid = (WORD)((p - s_tProtocol.aucSendBuf) * 2);

    return;
}

static void GetBduApptestData(BYTE ucPort)
{
    BYTE  *p = NULL;
    T_DCTestData tDCTestData;
    T_DCTestPara tDCTestPara;
    T_DCRealData tDcRealData;

    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }
    rt_memset(&tDCTestData, 0, sizeof(T_DCTestData));
    rt_memset(&tDCTestPara, 0, sizeof(T_DCTestPara));
    rt_memset(&tDcRealData, 0, sizeof(T_DCRealData));
    GetBduTestData(&tDCTestData);
    GetBduTestPara(&tDCTestPara);
    GetBduReal(&tDcRealData);
    
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    p  = s_tProtocol.aucSendBuf;

    /*DATA_FLAG*/
    *p++ = GetDataFlag( BCM_ALARM, ucPort );               //DATA_FLAG

    *(FLOAT*)p = tDcRealData.tDCAnalag.wBusVol / 100.0f;   // Bus电压
    p += 4;
    *(FLOAT*)p = tDcRealData.tDCAnalag.wBatVol / 100.0f;   // 电池电压
    p += 4;
    *(FLOAT*)p = tDcRealData.tDCAnalag.sBusCur / 100.0f;   // Bus电流
    p += 4;
    *(FLOAT*)p = tDcRealData.tDCAnalag.sBatCur / 100.0f;   // 电池电流
    p += 4;	
    
    *(FLOAT*)p = tDCTestData.sBusPinTestVol / 100.0f;	   // 母排Pin端电压
    p += 4;
    *(FLOAT*)p = tDCTestData.wHeaterTestVol / 100.0f;      // 加热膜电压检测电压
    p += 4;
    *(FLOAT*)p = tDCTestData.wMidMOSTestVol1 / 100.0f;	   // MOS管中点电压检测电压1
    p += 4;
    *(FLOAT*)p = tDCTestData.wMidMOSTestVol2 / 100.0f;	   // MOS管中点电压检测电压2
    p += 4;
    *(FLOAT*)p = tDCTestData.wMidMOSTestVol3 / 100.0f;	   // MOS管中点电压检测电压3
    p += 4;
    *(FLOAT*)p = tDCTestData.wMidMOSTestVol4 / 100.0f;	   // MOS管中点电压检测电压4
    p += 4;
    *(FLOAT*)p = tDCTestData.sActCircTestVol / 100.0f;	   // 激活电路电压检测电压
    p += 4;
    *(FLOAT*)p = tDCTestData.wBusRelayTestVol / 100.0f;	   // 母排继电器前电压检测电压
    p += 4;
    *(FLOAT*)p = tDCTestData.sBoardTemp1 / 10.0f;	   // 单板温度1
    p += 4;
    *(FLOAT*)p = tDCTestData.sBoardTemp2 / 10.0f;	   // 单板温度2
    p += 4;
    *(FLOAT*)p = tDCTestData.sBoardTemp3 / 10.0f;	   // 单板温度3
    p += 4;
    *(FLOAT*)p = tDCTestData.sBoardTemp4 / 10.0f;	   // 单板温度4
    p += 4;
    *(FLOAT*)p = tDCTestData.sConnTemp1 / 10.0f;	   // 连接器温度1
    p += 4;
    *(FLOAT*)p = tDCTestData.sConnTemp2 / 10.0f;	   // 连接器温度2
    p += 4;

    *(SHORT*)p  = Host2Modbus((SHORT*)&tDCTestPara.sWavePointChg);       // 充电逐波点
    p += 2;
    *(SHORT*)p  = Host2Modbus((SHORT*)&tDCTestPara.sWavePointDischg);    // 放电逐波点
    p += 2;

    /* LENID */	
    s_tProtocol.wSendLenid  = (WORD) ((p-s_tProtocol.aucSendBuf)*2);
    return;

}

static void BDUCtrl(BYTE ucPort)
{
    BYTE ucCtlData = 0;
    T_DCPara tDCPara;

    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }

    rt_memset(&tDCPara,0x00,sizeof(T_DCPara));
    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];
    ucCtlData = s_tProtocol.aucRecBuf[8];

    switch(s_tProtocol.ucCommandType)
    {
        case 0x80: // 充电直通
            if(True == ucCtlData) // 进入充电直通，限流千分比设置为1000
            {
                GetBduPara(&tDCPara);
                tDCPara.wChgCurVal = CHARGE_LIMIT_CURR;//采用千分比类型
                tDCPara.wChgBusCurVal = CHARGE_LIMIT_CURR;//采用千分比类型
                SetBduPara(&tDCPara);
                BduCtrl(SCI_CTRL_CHG_STG,ENABLE);   // 充电直通允许
                BduCtrl(SCI_CTRL_CHG2CHG, ENABLE);  // 转充电使能允许
                BduCtrl(SCI_CTRL_CHG_PRT, DISABLE); // 充电保护禁止
            }
            else if(False == ucCtlData) // 退出充电直通
            {
                BduCtrl(SCI_CTRL_CHG_STG, DISABLE);    // 充电直通禁止
                BduCtrl(SCI_CTRL_DISCHG_STG, DISABLE); // 放电直通禁止
                BduCtrl(SCI_CTRL_CHG_PRT, ENABLE);     // 充电保护允许
                BduCtrl(SCI_CTRL_DISCHG_PRT, ENABLE);  // 放电保护允许
            }
            else
            {
                s_tProtocol.ucRTN = RTN_WRONG_COMMAND; // 命令格式错
                return;
            }
            break;
        case 0x81: // 放电直通
            if(True == ucCtlData) // 进入放电直通
            {
                BduCtrl(SCI_CTRL_DISCHG_STG,ENABLE);   // 放电直通允许
                BduCtrl(SCI_CTRL_DISCHG_PRT, DISABLE); // 放电保护禁止
            }
            else if(False == ucCtlData) // 退出放电直通
            {
                BduCtrl(SCI_CTRL_CHG_STG, DISABLE);    // 充电直通禁止
                BduCtrl(SCI_CTRL_DISCHG_STG, DISABLE); // 放电直通禁止
                BduCtrl(SCI_CTRL_CHG_PRT, ENABLE);     // 充电保护允许
                BduCtrl(SCI_CTRL_DISCHG_PRT, ENABLE);  // 放电保护允许
            }
            else
            {
                s_tProtocol.ucRTN = RTN_WRONG_COMMAND; // 命令格式错
                return;
            }
            break;
        default:
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND; // 命令格式错
            break;
    }

    return;
}

static void GetCustomerName(BYTE ucPort)
{
    BYTE *p = NULL;
    T_BmsCustomerNameStruct tBmsCustomerName;

    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }

    rt_memset_s(&tBmsCustomerName, sizeof(T_BmsCustomerNameStruct), 0, sizeof(T_BmsCustomerNameStruct));

    p  = s_tProtocol.aucSendBuf;
    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));

    readBmsCustomerName(&tBmsCustomerName);

    MemsetBuff(p, &tBmsCustomerName.acBmsCustomerName[0], 17, 17, 0x20);//客户名称
    //MemsetBuff将最后一个字节赋值为0，最后一个字节保持原先的不变
    rt_memcpy_s(p + 16, 1, &tBmsCustomerName.acBmsCustomerName[0] + 16, 1);

    p += 17;

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf)*2);
}


static void SetCustomerName(BYTE ucPort)
{
    T_BmsCustomerNameStruct  tBmsCustomerName;

    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }

    rt_memset_s((BYTE*)&tBmsCustomerName, sizeof(T_BmsCustomerNameStruct), 0x00, sizeof(T_BmsCustomerNameStruct));

    readBmsCustomerName(&tBmsCustomerName);

    rt_memcpy_s((BYTE *)&(tBmsCustomerName.acBmsCustomerName[0]), sizeof(tBmsCustomerName.acBmsCustomerName), (BYTE *)(&s_tProtocol.aucRecBuf[7]), sizeof(tBmsCustomerName.acBmsCustomerName));
    if(!CheckCharRange((BYTE *)&tBmsCustomerName.acBmsCustomerName[0], sizeof(tBmsCustomerName.acBmsCustomerName)))
    {
        s_tProtocol.ucRTN  = RTN_INVALID_DATA;
        return;
    }

    writeBmsCustomerName(&tBmsCustomerName);

    return;
}

static void RecvSysParaById(BYTE ucPort)
{
    WORD wParaId = MakeParaId(s_tProtocol.aucRecBuf[7], s_tProtocol.aucRecBuf[8]);
    WORD wRecvParaLen = s_tProtocol.wRecLenid / 2 - 2;
    BYTE *p = &s_tProtocol.aucRecBuf[9];

    int sz = SetParaById(wParaId, p, wRecvParaLen, EndianConvert);
    if (sz < 0)
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
        return;
    }
    GetSysPara(&s_tSysPara);
    WriteBMSDefaultPara(&s_tSysPara);
    return;
}

static void SendSysParaById(BYTE ucPort)
{
    BYTE *p = s_tProtocol.aucSendBuf;
    WORD wParaId = 0;
    int sz = -1;
    rt_memset_s(p, sizeof(s_tProtocol.aucSendBuf), 0x00, sizeof(s_tProtocol.aucSendBuf));

    if (s_tProtocol.aucRecBuf[8] != 0xFF)
    {
        wParaId = MakeParaId(s_tProtocol.aucRecBuf[7], s_tProtocol.aucRecBuf[8]);
        sz = GetParaById(wParaId, p, sizeof(s_tProtocol.aucSendBuf), EndianConvert);
    }
    else
    {
        sz = GetMultiParaBySection(s_tProtocol.aucRecBuf[7], p, sizeof(s_tProtocol.aucSendBuf), EndianConvert);
    }

    if (sz < 0)
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
        return;
    }
    p += sz;

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
}

static void Clear4GTrafficApptest(BYTE ucPort)
{
    Clear4GTraffic();
}

/* 以下为UT辅助函数，业务代码不可使用 */
#ifdef UNITEST
T_SysPara* GetSysParaPtr(void)
{
    return &s_tSysPara;
}
#endif
