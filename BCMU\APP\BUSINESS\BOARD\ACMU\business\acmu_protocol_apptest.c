#include <string.h>
#include <stdlib.h>
#include <rtthread.h>
#include "sps.h"
#include "dev_acmu.h"
#include "sample.h"
#include "storage.h"
#include "MIBTable.h"
#include "utils_time.h"
#include "pdt_version.h"
#include "pdt_version.h"
#include "his_record.h"
#include "const_define_in.h"
#include "alarm_manage.h"
#include "realdata_id_in.h"
#include "partition_def.h"
#include "para_id_in.h"
#include "para_manage.h"
#include "realdata_save.h"
#include "log_mgr_api.h"
#include "protocol_layer.h"
#include "protocol_1363_comm.h"
#include "acmu_protocol_apptest.h"
#include "utils_data_transmission.h"
#include "utils_rtthread_security_func.h"
#include "utils_data_type_conversion.h"
#include "utils_flag.h"
#include "io_control.h"
#include "acmu_watchdog.h"
#include "can.h"

Static char s_usart_can_test_result = TEST_FAULT; //串口回环测试结果
Static char s_usart_can_test_frame[] = {0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88}; // 串口回环测试的收发帧
Static unsigned char s_apptest_trig_count = 0;

Static int apptest_trig_pack(void* dev_inst, void* cmd_buff);
Static int apptest_exit_parse(void* dev_inst, void* cmd_buff);
Static int apptest_comm_parse(void* dev_inst, void* cmd_buff);
Static int usart_rev_handle(rt_device_t dev);
Static int can_rev_handle(rt_device_t dev);
Static int acmu_usart_loopback_test_parse(void* dev_inst, void* cmd_buff);
Static int acmu_can_loopback_test_parse(void* dev_inst, void* cmd_buff);
Static int acmu_usart_can_loopback_test_pack(void* dev_inst, void* cmd_buff);
Static int acmu_parse_set_do_state(void* dev_inst, void* cmd_buff);
Static int acmu_pack_get_di_state(void* dev_inst, void* cmd_buff);
Static int acmu_pack_get_ai_state(void* dev_inst, void* cmd_buff);
Static int acmu_pack_hardware_ver(void* dev_inst, void* cmd_buff);
Static int acmu_pack_serial_number(void* dev_inst, void* cmd_buff);
Static int acmu_pack_manufacture_date(void* dev_inst, void* cmd_buff);
Static int acmu_parse_serial_number(void* dev_inst, void* cmd_buff);
Static int acmu_parse_manufacture_date(void* dev_inst, void* cmd_buff);
Static int acmu_parse_hardware_ver(void* dev_inst, void* cmd_buff);
Static int acmu_pack_chip_status(void* dev_inst, void* cmd_buff);
Static int acmu_pack_softinfo(void* dev_inst, void* cmd_buff);
Static int acmu_reset_config_para(void* dev_inst, void* cmd_buff);
Static int acmu_delete_hisrecord(void* dev_inst, void* cmd_buff);
Static int acmu_pack_time(void* dev_inst, void* cmd_buff);
Static int acmu_parse_time(void* dev_inst, void* cmd_buff);
Static int acmu_parse_set_led_state(void* dev_inst, void* cmd_buff);
Static int acmu_parse_set_buzz_state(void* dev_inst, void* cmd_buff);
Static int acmu_parse_set_start_button_test(void* dev_inst, void* cmd_buff);
Static int acmu_pack_get_button_test_state(void* dev_inst, void* cmd_buff);
Static int acmu_pack_zero_slope(void* dev_inst, void* cmd_buff);
Static int acmu_parse_zero_slope(void* dev_inst, void* cmd_buff);

Static s1363_cmd_head_t cmd_req[] = {
    {VER_21, CMD_NONE, CMD_NONE},                               // 0
    {VER_21, CMD_NONE, CMD_APPTEST_ENTER},                      // 1 进入apptest
    {VER_21, CID1_COMMON, CMD_APPTEST_EXIT},                    // 2  退出apptest
    {VER_21, CID1_COMMON, CMD_GET_COMMPROTOCOL_VER},            // 3 获取通信协议版本号
    {VER_21, CID1_COMMON, CMD_GET_MANUFACTURE_DATE},            // 4 获取单板生产日期
    {VER_21, CID1_COMMON, CMD_SET_MANUFACTURE_DATE},            // 5 设置单板生产日期
    {VER_21, CID1_COMMON, CMD_GET_HARDWARE_VER},                // 6 获取硬件版本号
    {VER_21, CID1_COMMON, CMD_SET_HARDWARE_VER},                // 7 设置硬件版本号
    {VER_21, CID1_COMMON, CMD_GET_SERIAL_NUMBER_APPTEST},       // 8 获取序列号
    {VER_21, CID1_COMMON, CMD_SET_SERIAL_NUMBER_APPTEST},       // 9 设置序列号
    {VER_21, CID1_COMMON, CMD_SET_DO_STATE},                    // 10设置输出干接点状态命令
    {VER_21, CID1_COMMON, CMD_GET_DI_STATE},                    // 11 获取输入干接点状态命令
    {VER_21, CID1_COMMON, CMD_GET_AI_DATA},                     // 12 获取模拟量
    {VER_21, CID1_COMMON, CMD_SET_LED_STATE},                   // 13 控制液晶面板灯
    {VER_21, CID1_COMMON, CMD_SET_BUZZ_STATE},                  // 14 控制蜂鸣器
    {VER_21, CID1_COMMON, CMD_USART_LOOPBACK_TEST},             // 15 串口回环测试
    {VER_21, CID1_COMMON, CMD_CAN_LOOPBACK_TEST},               // 16 CAN回环测试
    {VER_21, CID1_COMMON, CMD_SET_START_BUTTON_TEST},           // 17 启动按键测试
    {VER_21, CID1_COMMON, CMD_GET_BUTTON_TEST_STATE},           // 18 获取按键测试结果
    {VER_21, CID1_COMMON, CMD_GET_TIME},                        // 19 获取时间
    {VER_21, CID1_COMMON, CMD_SET_TIME},                        // 20 设置时间
    {VER_21, CID1_COMMON, CMD_GET_ZERO_SLOPE},                  // 21 获取零点斜率
    {VER_21, CID1_COMMON, CMD_SET_ZERO_SLOPE},                  // 22 设置零点斜率
    {VER_21, CID1_COMMON, CMD_GET_CHIP_STATUS},                 // 23 获取芯片测试信息
    {VER_21, CID1_COMMON, CMD_GET_SOFTINFO},                    // 24 获取软件信息命令
    {VER_21, CID1_COMMON, CMD_RESET_CONFIG_PARA},               // 25 恢复配置参数
    {VER_21, CID1_COMMON, CMD_DELETE_HISRECORD},                // 26 删除历史记录 
};

/* 命令应答头 */
Static s1363_cmd_head_t cmd_ack[] = {
    {VER_21, CMD_NONE, CMD_NONE},                               // 0
    {VER_21, CMD_NONE, CMD_APPTEST_ENTER},                      // 1 进入apptest
    {VER_21, CID1_COMMON, CMD_APPTEST_EXIT},                    // 2 退出apptest
    {VER_21, CID1_COMMON, CMD_GET_COMMPROTOCOL_VER},            // 3 获取通信协议版本号
    {VER_21, CID1_COMMON, CMD_GET_MANUFACTURE_DATE},            // 4 获取单板生产日期
    {VER_21, CID1_COMMON, CMD_SET_MANUFACTURE_DATE},            // 5 设置单板生产日期
    {VER_21, CID1_COMMON, CMD_GET_HARDWARE_VER},                // 6 获取硬件版本号
    {VER_21, CID1_COMMON, CMD_SET_HARDWARE_VER},                // 7 设置硬件版本号
    {VER_21, CID1_COMMON, CMD_GET_SERIAL_NUMBER_APPTEST},       // 8 获取序列号
    {VER_21, CID1_COMMON, CMD_SET_SERIAL_NUMBER_APPTEST},       // 9 设置序列号
    {VER_21, CID1_COMMON, CMD_SET_DO_STATE},                    // 10 设置输出干接点状态命令
    {VER_21, CID1_COMMON, CMD_GET_DI_STATE},                    // 11 获取输入干接点状态命令
    {VER_21, CID1_COMMON, CMD_GET_AI_DATA},                     // 12 获取模拟量
    {VER_21, CID1_COMMON, CMD_SET_LED_STATE},                   // 13 控制液晶面板灯
    {VER_21, CID1_COMMON, CMD_SET_BUZZ_STATE},                  // 14 控制蜂鸣器
    {VER_21, CID1_COMMON, CMD_USART_LOOPBACK_TEST},             // 15 串口回环测试
    {VER_21, CID1_COMMON, CMD_CAN_LOOPBACK_TEST},               // 16 CAN回环测试
    {VER_21, CID1_COMMON, CMD_SET_START_BUTTON_TEST},           // 17 启动按键测试
    {VER_21, CID1_COMMON, CMD_GET_BUTTON_TEST_STATE},           // 18 获取按键测试结果
    {VER_21, CID1_COMMON, CMD_GET_TIME},                        // 19 获取时间
    {VER_21, CID1_COMMON, CMD_SET_TIME},                        // 20 设置时间
    {VER_21, CID1_COMMON, CMD_GET_ZERO_SLOPE},                  // 21 获取零点斜率
    {VER_21, CID1_COMMON, CMD_SET_ZERO_SLOPE},                  // 22 设置零点斜率
    {VER_21, CID1_COMMON, CMD_GET_CHIP_STATUS},                 // 23 获取芯片测试信息
    {VER_21, CID1_COMMON, CMD_GET_SOFTINFO},                    // 24 获取软件信息命令
    {VER_21, CID1_COMMON, CMD_RESET_CONFIG_PARA},               // 25 恢复配置参数
    {VER_21, CID1_COMMON, CMD_DELETE_HISRECORD},                // 26 删除历史记录 
};

static acmu_zero_slope_info s_zero_slope_info[] = {
    {0x0100, ACMU_PARA_ID_AC_PHASE_VOLT_ZERO_OFFSET},
    {0x0101, ACMU_PARA_ID_AC_PHASE_VOLT_ZERO_OFFSET + 1},
    {0x0102, ACMU_PARA_ID_AC_PHASE_VOLT_ZERO_OFFSET + 2},
    {0x0200, ACMU_PARA_ID_AC_PHASE_VOLT_SLOPE_OFFSET},
    {0x0201, ACMU_PARA_ID_AC_PHASE_VOLT_SLOPE_OFFSET + 1},
    {0x0202, ACMU_PARA_ID_AC_PHASE_VOLT_SLOPE_OFFSET + 2},
    {0x0300, ACMU_PARA_ID_AC_PHASE_CURR_ZERO_OFFSET},
    {0x0301, ACMU_PARA_ID_AC_PHASE_CURR_ZERO_OFFSET + 1},
    {0x0302, ACMU_PARA_ID_AC_PHASE_CURR_ZERO_OFFSET + 2},
    {0x0400, ACMU_PARA_ID_AC_PHASE_CURR_SLOPE_OFFSET},
    {0x0401, ACMU_PARA_ID_AC_PHASE_CURR_SLOPE_OFFSET + 1},
    {0x0402, ACMU_PARA_ID_AC_PHASE_CURR_SLOPE_OFFSET + 2},
    {0x0500, ACMU_PARA_ID_ENV_TEMP_ZERO_POINT_OFFSET},
    {0x0600, ACMU_PARA_ID_ENV_HUMIDITY_ZERO_POINT_OFFSET},
};

/* 命令总表,必须以空字符串""结束 */
Static cmd_t no_poll_cmd_tab[] = {
    {ACMU_APPTEST_ENTER_APPTEST, CMD_PASSIVE, &cmd_req[1], &cmd_ack[1], sizeof(s1363_cmd_head_t), NULL, NULL, apptest_trig_pack, SELF_PARSE},    // 进入apptest
    {ACMU_APPTEST_EXIT_APPTEST, CMD_PASSIVE, &cmd_req[2], &cmd_ack[2], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, apptest_exit_parse},    // 退出apptest
    {ACMU_APPTEST_GET_COMMPROTOCOL_VER, CMD_PASSIVE, &cmd_req[3], &cmd_ack[3], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, apptest_comm_parse},    // 获取通信协议版本号
    {ACMU_APPTEST_GET_MANUFACTURE_DATE, CMD_PASSIVE, &cmd_req[4], &cmd_ack[4], sizeof(s1363_cmd_head_t), NULL, NULL, acmu_pack_manufacture_date, apptest_comm_parse},    // 获取单板生产日期
    {ACMU_APPTEST_SET_MANUFACTURE_DATE, CMD_PASSIVE, &cmd_req[5], &cmd_ack[5], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, acmu_parse_manufacture_date},    // 设置单板生产日期
    {ACMU_APPTEST_GET_HARDWARE_VER, CMD_PASSIVE, &cmd_req[6], &cmd_ack[6], sizeof(s1363_cmd_head_t), NULL, NULL, acmu_pack_hardware_ver, apptest_comm_parse},    // 获取硬件版本号
    {ACMU_APPTEST_SET_HARDWARE_VER, CMD_PASSIVE, &cmd_req[7], &cmd_ack[7], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, acmu_parse_hardware_ver},    // 设置硬件版本号
    {ACMU_APPTEST_GET_SERIAL_NUMBER, CMD_PASSIVE, &cmd_req[8], &cmd_ack[8], sizeof(s1363_cmd_head_t), NULL, NULL, acmu_pack_serial_number, apptest_comm_parse},    // 获取序列号
    {ACMU_APPTEST_SET_SERIAL_NUMBER, CMD_PASSIVE, &cmd_req[9], &cmd_ack[9], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, acmu_parse_serial_number},    // 设置序列号
    {ACMU_APPTEST_SET_DO_STATE, CMD_PASSIVE, &cmd_req[10], &cmd_ack[10], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, acmu_parse_set_do_state},     // 设置输出干接点状态命令
    {ACMU_APPTEST_GET_DI_STATE, CMD_PASSIVE, &cmd_req[11], &cmd_ack[11], sizeof(s1363_cmd_head_t), NULL, NULL, acmu_pack_get_di_state, apptest_comm_parse},    // 获取输入干接点状态命令
    {ACMU_APPTEST_GET_AI_DATA, CMD_PASSIVE, &cmd_req[12],  &cmd_ack[12], sizeof(s1363_cmd_head_t), NULL, NULL, acmu_pack_get_ai_state, apptest_comm_parse},    // 获取模拟量
    {ACMU_APPTEST_SET_LED_STATUS, CMD_PASSIVE, &cmd_req[13],  &cmd_ack[13], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, acmu_parse_set_led_state},    // 控制液晶面板灯
    {ACMU_APPTEST_SET_BUZZ_STATUS, CMD_PASSIVE, &cmd_req[14],  &cmd_ack[14], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, acmu_parse_set_buzz_state},    // 控制蜂鸣器
    {ACMU_APPTEST_USART_LOOPBACK_TEST, CMD_PASSIVE, &cmd_req[15], &cmd_ack[15], sizeof(s1363_cmd_head_t), NULL, NULL, acmu_usart_can_loopback_test_pack, acmu_usart_loopback_test_parse},    // 串口自回环测试
    {ACMU_APPTEST_CAN_LOOPBACK_TEST, CMD_PASSIVE, &cmd_req[16], &cmd_ack[16], sizeof(s1363_cmd_head_t), NULL, NULL, acmu_usart_can_loopback_test_pack, acmu_can_loopback_test_parse},    // can口自回环测试
    {ACMU_APPTEST_SET_BUZZ_STATUS, CMD_PASSIVE, &cmd_req[17],  &cmd_ack[17], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, acmu_parse_set_start_button_test},    // 启动按键测试
    {ACMU_APPTEST_GET_SERIAL_NUMBER, CMD_PASSIVE, &cmd_req[18], &cmd_ack[18], sizeof(s1363_cmd_head_t), NULL, NULL, acmu_pack_get_button_test_state, apptest_comm_parse},    // 获取按键测试结果
    {ACMU_APPTEST_GET_TIME, CMD_PASSIVE, &cmd_req[19], &cmd_ack[19], sizeof(s1363_cmd_head_t), NULL, NULL, acmu_pack_time, apptest_comm_parse},    // 获取时间
    {ACMU_APPTEST_SET_TIME, CMD_PASSIVE, &cmd_req[20], &cmd_ack[20], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, acmu_parse_time},    // 设置时间
    {ACMU_APPTEST_GET_ZERO_SLOPE, CMD_PASSIVE, &cmd_req[21], &cmd_ack[21], sizeof(s1363_cmd_head_t), NULL, NULL, acmu_pack_zero_slope, apptest_comm_parse},    // 获取零点、斜率
    {ACMU_APPTEST_SET_ZERO_SLOPE, CMD_PASSIVE, &cmd_req[22], &cmd_ack[22], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, acmu_parse_zero_slope},    // 设置零点、斜率
    {ACMU_APPTEST_GET_CHIP_STATUS, CMD_PASSIVE, &cmd_req[23], &cmd_ack[23], sizeof(s1363_cmd_head_t), NULL, NULL, acmu_pack_chip_status,apptest_comm_parse},
    {ACMU_APPTEST_GET_SOFTINFO, CMD_PASSIVE, &cmd_req[24], &cmd_ack[24], sizeof(s1363_cmd_head_t), NULL, NULL, acmu_pack_softinfo, apptest_comm_parse},
    {ACMU_APPTEST_RESET_CONFIG_PARA, CMD_PASSIVE, &cmd_req[25], &cmd_ack[25], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, acmu_reset_config_para},
    {ACMU_APPTEST_DELETE_HISRECORD, CMD_PASSIVE, &cmd_req[26], &cmd_ack[26], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, acmu_delete_hisrecord},
    {0},
};

Static dev_type_t s_acmu_apptest_dev =
{
    DEV_NORTH_ACMU_APPTEST, 1, PROTOCOL_YD_1363, LINK_ACMU_NORTH, R_BUFF_LEN, S_BUFF_LEN, 0, no_poll_cmd_tab, NULL
};

dev_type_t* init_dev_acmu_apptest(void)
{
    return &s_acmu_apptest_dev;
}

Static int apptest_comm_parse(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(get_apptest_flag() == TRUE, NO_NEED_REPONSE);
    return SUCCESSFUL;
}

Static int apptest_trig_pack(void* dev_inst, void* cmd_buff) 
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    int offset = 0;
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;
    char str[MAX_EVENT_INFO_LEN] = {};

    s_apptest_trig_count++;
    
    if(s_apptest_trig_count < 3)
    {
        data_buff[offset] = 0x00;
    }
    else
    {
        if (get_apptest_flag() == FALSE) {
            rt_memset_s(str, MAX_EVENT_INFO_LEN, 0, MAX_EVENT_INFO_LEN);
            rt_snprintf_s(str, MAX_EVENT_INFO_LEN, "enter apptest");
            pub_hisaction_save_msg(ID1_ACMU_CTRL, ID2_CTL_ENTERAPPTEST, 0, str);
        }
        data_buff[offset] = 0x01;
        set_apptest_flag(TRUE);
    }
    offset++;
    ((cmd_buf_t*)cmd_buff)->data_len=offset;
    return SUCCESSFUL;
}

Static int apptest_exit_parse(void* dev_inst, void* cmd_buff)
{
    char str[MAX_EVENT_INFO_LEN] = {};

    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    RETURN_VAL_IF_FAIL(get_apptest_flag() == TRUE, NO_NEED_REPONSE);

    rt_memset_s(str, MAX_EVENT_INFO_LEN, 0, MAX_EVENT_INFO_LEN);
    rt_snprintf_s(str, MAX_EVENT_INFO_LEN, "quit apptest");

    set_apptest_flag(FALSE);
    s_apptest_trig_count = 0;
    pub_hisaction_save_msg(ID1_ACMU_CTRL, ID2_CTL_QUITAPPTEST, 0, str);
    return SUCCESSFUL;
}

Static int acmu_parse_set_do_state(void* dev_inst, void* cmd_buff) {
    unsigned char do_type = 0;
    unsigned char do_state = 0;
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff = NULL;

    if (get_apptest_flag() == FALSE) {
        return NO_NEED_REPONSE;
    }

    if (dev_inst == NULL || cmd_buff == NULL ||
        cmd_buf_temp->buf == NULL ||
        cmd_buf_temp->data_len < 2) {
        return FAILURE;
    }

    buff = cmd_buf_temp->buf;
    do_type = buff[0];
    do_state = buff[1];

    if (do_type <= DO_TYPE_RLY8) {
        control_out_relay(do_type, do_state);
    } else if(do_type == DO_TYPE_CTRL_ALM_LED) {
        // 0:常亮LEDON，1:常灭LEDOFF,2:闪烁LEDSLOWSH(500ms)
        led_set_status(RACK_ALARM_LED, do_state);
    } else if (do_type == DO_TYPE_STOP_FEED_DOG) {
        // 0:停止喂狗，1:开启喂狗
        set_watchdog_monitor_feed(do_state);
    } else {
         cmd_buf_temp->data_len = 0;
        return FAILURE;
    }
    cmd_buf_temp->data_len = 0;
    return SUCCESSFUL;
}

Static int acmu_pack_get_di_state(void* dev_inst, void* cmd_buff) {
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    int i = 0;
    int j = 0;
    unsigned char* buff = NULL;
    unsigned int offset = 0;
    unsigned char c_value = 0;

    if (dev_inst == NULL || cmd_buff == NULL ||
        cmd_buf_temp->buf == NULL) {
        return FAILURE;
    }

    buff = cmd_buf_temp->buf;
    // 烟雾，门磁，防雷，交流输入，水淹
    get_one_data(ACMU_DATA_ID_FUMES_SWITCH_STATUS, &c_value);
    buff[offset++] = c_value;
    get_one_data(ACMU_DATA_ID_DOORMAT_SWITCH_STATUS, &c_value);
    buff[offset++] = c_value;
    get_one_data(ACMU_DATA_ID_LIGHT_PRO_CIRCUIT_C, &c_value);
    buff[offset++] = c_value;
    get_one_data(ACMU_DATA_ID_AC_INPUT_SWITCH, &c_value);
    buff[offset++] = c_value;
    get_one_data(ACMU_DATA_ID_FLOOD_SWITCH_STATUS, &c_value);
    buff[offset++] = c_value;

    // 12路交流输出空开状态
    for (i = 0; i < 12; i++) {
        get_one_data(ACMU_DATA_ID_AC_OUT_SWITCH + i, &c_value);
        buff[offset++] = c_value;
    }

    // 4路输入干接点状态
    for (j = 0; j < 4; j++) {
        get_one_data(ACMU_DATA_ID_INPUT_RELAY + j, &c_value);
        buff[offset++] = c_value;
    }
    cmd_buf_temp->data_len = offset;

    return SUCCESSFUL;
}

Static int acmu_pack_get_ai_state(void* dev_inst, void* cmd_buff) {
    int i = 0, j = 0, k = 0, m = 0;
    int offset = 0;
    float f_data = 0.0f;
    unsigned char* buff = NULL;
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    acem_analog_data_t acem_show_data;

    if (dev_inst == NULL || cmd_buff == NULL ||
        cmd_buf_temp->buf == NULL) {
        return FAILURE;
    }

    rt_memset_s(&acem_show_data, sizeof(acem_show_data), 0, sizeof(acem_show_data));
    get_acem_show_data(&acem_show_data);

     buff = cmd_buf_temp->buf;
    // 温度
    get_one_data(ACMU_DATA_ID_ENV_TEMP, &f_data);
    put_int16_to_buff(&buff[offset], round(f_data * pow(10,1)));
    offset += 2;

    // 环境湿度
    get_one_data(ACMU_DATA_ID_ENV_HUMIDITY, &f_data);
    put_int16_to_buff(&buff[offset], round(f_data * pow(10,1)));
    offset += 2;

    // 3路交流电表相电压
    for (i = 0; i < 3; i++)
    {
        put_int16_to_buff(&buff[offset], round(acem_show_data.phase_volt[i] * pow(10,1)));
        offset += 2;
    }

    // 3路交流电表相电流
    for (j = 0; j < 3; j++)
    {
        put_int16_to_buff(&buff[offset], round(acem_show_data.phase_curr[j] * pow(10,1)));
        offset += 2;
    }

    // 3路相电压
    for (k = 0; k < 3; k++)
    {
        get_one_data(ACMU_DATA_ID_PHASE_VOLT + k, &f_data);
        put_int16_to_buff(&buff[offset], round(f_data * pow(10,1)));
        offset += 2;
    }

    // C\B\A相电流（N-PE电流删除）
    for (m = 0; m < 3; m++)
    {
        get_one_data(ACMU_DATA_ID_PHASE_CURR + 2 - m, &f_data);
        put_int16_to_buff(&buff[offset], round(f_data * pow(10,1)));
        offset += 2;
    }

    cmd_buf_temp->data_len = offset;
    return SUCCESSFUL;
}

Static int acmu_pack_time(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    time_base_t  pt_time={0};
    int offset=0;
    get_time(&pt_time);

    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;

    put_int16_to_buff(&data_buff[offset],pt_time.year);
    offset+=2;

    data_buff[offset++] = pt_time.month;
    data_buff[offset++] = pt_time.day;
    data_buff[offset++] = pt_time.hour;
    data_buff[offset++] = pt_time.minute;
    data_buff[offset++] = pt_time.second;

    ((cmd_buf_t*)cmd_buff)->data_len=offset;

    return SUCCESSFUL;
}

Static int acmu_parse_time(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    int offset = 0;
    time_base_t tm = {0};
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;

    tm.year = get_int16_data(&data_buff[offset]);
    offset+=2;
    tm.month  = data_buff[offset++];
    tm.day = data_buff[offset++];
    tm.hour = data_buff[offset++];
    tm.minute  = data_buff[offset++];
    tm.second  = data_buff[offset++];
    if( check_time_range(tm) == FAILURE )
    {
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA ;
        return SUCCESSFUL;
    }
    
    /* 设置系统时间 */
    if (set_system_time(&tm) != SUCCESSFUL) {
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA;
        return SUCCESSFUL;
    }
    
    return SUCCESSFUL;
}

Static int acmu_pack_hardware_ver(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff;
    unsigned char offset = 0;
    buff = cmd_buf_temp->buf;
    unsigned char hardware_version[SM_HARDWARE_VERSION_LEN_APPTEST] = {0};
    RETURN_VAL_IF_FAIL(get_apptest_flag() == TRUE, NO_NEED_REPONSE);

    get_one_para(ACMU_PARA_ID_HARDWARE_VERSION_OFFSET, hardware_version);  // 硬件版本号
    rt_memcpy_s(&buff[offset], SM_HARDWARE_VERSION_LEN_APPTEST, hardware_version, SM_HARDWARE_VERSION_LEN_APPTEST);
    offset += SM_HARDWARE_VERSION_LEN_APPTEST;

    /* LENID */
    cmd_buf_temp->data_len = offset;

    return SUCCESSFUL;
}

Static int acmu_pack_serial_number(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff;
    unsigned char offset = 0;
    buff = cmd_buf_temp->buf;
    unsigned char serial_number[SM_SERIAL_NUMBER_LEN_APPTEST] = {0};
    RETURN_VAL_IF_FAIL(get_apptest_flag() == TRUE, NO_NEED_REPONSE);

    get_one_para(ACMU_PARA_ID_SERIAL_NUMBER_OFFSET, serial_number);  // 序列号
    rt_memcpy_s(&buff[offset], SM_SERIAL_NUMBER_LEN_APPTEST, serial_number, SM_SERIAL_NUMBER_LEN_APPTEST);
    offset += SM_SERIAL_NUMBER_LEN_APPTEST;

    /* LENID */
    cmd_buf_temp->data_len = offset;

    return SUCCESSFUL;
}

Static int acmu_pack_manufacture_date(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff;
    unsigned char offset = 0;
    buff = cmd_buf_temp->buf;
    unsigned char manufacture_date[SM_MANUFACTURE_DATE_LEN_APPTEST] = {0};
    RETURN_VAL_IF_FAIL(get_apptest_flag() == TRUE, NO_NEED_REPONSE);

    get_one_para(ACMU_PARA_ID_BOARD_MANUFACTURE_DATE_OFFSET, manufacture_date);  // 单板生产日期
    rt_memcpy_s(&buff[offset], SM_MANUFACTURE_DATE_LEN_APPTEST, manufacture_date, SM_MANUFACTURE_DATE_LEN_APPTEST);
    offset += SM_MANUFACTURE_DATE_LEN_APPTEST;

    /* LENID */
    cmd_buf_temp->data_len = offset;

    return SUCCESSFUL;
}

Static int acmu_parse_serial_number(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff;
    unsigned char offset = 0;
    buff = cmd_buf_temp->buf;
    unsigned char serial_number[SM_SERIAL_NUMBER_LEN_APPTEST] = {0};
    RETURN_VAL_IF_FAIL(get_apptest_flag() == TRUE, NO_NEED_REPONSE);

    rt_memcpy_s(serial_number, SM_SERIAL_NUMBER_LEN_APPTEST, &buff[offset], SM_SERIAL_NUMBER_LEN_APPTEST);
    set_one_para(ACMU_PARA_ID_SERIAL_NUMBER_OFFSET, serial_number, TRUE, FALSE);  // 序列号
    /* LENID */
    cmd_buf_temp->data_len = offset;

    return SUCCESSFUL;
}

Static int acmu_parse_manufacture_date(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff;
    unsigned char offset = 0;
    buff = cmd_buf_temp->buf;
    RETURN_VAL_IF_FAIL(get_apptest_flag() == TRUE, NO_NEED_REPONSE);

    set_one_para(ACMU_PARA_ID_BOARD_MANUFACTURE_DATE_OFFSET, &buff[offset], TRUE, FALSE);  // 单板生产日期
    /* LENID */
    cmd_buf_temp->data_len = offset;

    return SUCCESSFUL;
}

Static int acmu_parse_hardware_ver(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff;
    unsigned char offset = 0;
    buff = cmd_buf_temp->buf;
    unsigned char hardware_version[SM_HARDWARE_VERSION_LEN_APPTEST] = {0};
    RETURN_VAL_IF_FAIL(get_apptest_flag() == TRUE, NO_NEED_REPONSE);

    rt_memcpy_s(hardware_version, SM_HARDWARE_VERSION_LEN_APPTEST, &buff[offset], SM_HARDWARE_VERSION_LEN_APPTEST);
    set_one_para(ACMU_PARA_ID_HARDWARE_VERSION_OFFSET, hardware_version, TRUE, FALSE);  // 硬件版本号
    /* LENID */
    cmd_buf_temp->data_len = offset;

    return SUCCESSFUL;
}

/******************************************************
 * 协议类型：apptest工装协议

 * 协议名称：获取芯片测试信息命令信息
*******************************************************/
//1、检测RTC芯片
Static rt_err_t Check_Ic_RTC(void)
{
    time_t testTime = 0;
    return read_time_from_pfc8563(&testTime);
}

//2、检测norFLASH
Static rt_err_t Check_Ic_NORFlash(void)
{
    part_data_t part_data = {0};

    unsigned char apptest_flash_test_wdata[] = {"flash test ok"};
    unsigned char apptest_flash_test_rdata[20] = {0,};

    part_data.buff = (unsigned char *)&apptest_flash_test_wdata;
    part_data.len = sizeof(apptest_flash_test_wdata);
    part_data.offset = 0;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), APPTEST_FILE);

    if(SUCCESSFUL != storage_process(&part_data,erase_write_opr))
    {
        storage_unlink(APPTEST_FILE_NAME);
        return -RT_ERROR;
    }
    
    part_data.buff = (unsigned char *)&apptest_flash_test_rdata;
    if(SUCCESSFUL != storage_process(&part_data,read_opr))
    {
        storage_unlink(APPTEST_FILE_NAME);
        return -RT_ERROR;
    }
    if(rt_memcmp(apptest_flash_test_rdata, apptest_flash_test_wdata, sizeof(apptest_flash_test_wdata)) != 0)
    {
        storage_unlink(APPTEST_FILE_NAME);
        return -RT_ERROR;
    }
    storage_unlink(APPTEST_FILE_NAME);
    return RT_EOK;
}
//3、检测EEPROM
Static rt_err_t Check_Ic_EEPROM( void)
{
    part_data_t part_data = {0};

    unsigned char apptest_eeprom_test_wdata[] = {"eeprom test ok"};
    unsigned char apptest_eeprom_test_rdata[20] = {0,};

    part_data.buff = (unsigned char *)&apptest_eeprom_test_wdata;
    part_data.len = sizeof(apptest_eeprom_test_wdata);
    part_data.offset = 0;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), EEPROMTEST);

    if(SUCCESSFUL != storage_process(&part_data,erase_write_opr))
    {
        storage_process(&part_data,erase_opr);
        return -RT_ERROR;
    }
    
    part_data.buff = (unsigned char *)&apptest_eeprom_test_rdata;
    if(SUCCESSFUL != storage_process(&part_data,read_opr))
    {
        storage_process(&part_data,erase_opr);
        return -RT_ERROR;
    }
    if(rt_memcmp(apptest_eeprom_test_rdata, apptest_eeprom_test_wdata, sizeof(apptest_eeprom_test_wdata)) != 0)
    {
        storage_process(&part_data,erase_opr);
        return -RT_ERROR;
    }
    storage_process(&part_data,erase_opr);
    return RT_EOK;
}

Static int acmu_pack_chip_status(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff;
    unsigned char offset = 0;
    buff = cmd_buf_temp->buf;

    buff[offset++] = 4;
    buff[offset++] = (Check_Ic_RTC() == RT_EOK) ? TEST_NORMAL : TEST_FAULT;
    buff[offset++] = (Check_Ic_NORFlash() == RT_EOK) ? TEST_NORMAL : TEST_FAULT;
    buff[offset++] = (Check_Ic_EEPROM() == RT_EOK) ? TEST_NORMAL : TEST_FAULT;
    buff[offset++] = TEST_NORMAL;//SDRAM
    cmd_buf_temp->data_len = offset;

    return SUCCESSFUL;
}

Static int acmu_pack_softinfo(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff;
    unsigned char offset = 0;
    unsigned char soft_name[SOFTNAME_LEN_APPTEST] = {0};
    unsigned char soft_ver[SOFTVER_LEN_APPTEST] = {0};
    buff = cmd_buf_temp->buf;

    rt_memcpy_s(soft_name, SOFTNAME_LEN_APPTEST, SOFTWARE_NAME, sizeof(SOFTWARE_NAME));
    rt_memcpy_s(soft_ver, SOFTVER_LEN_APPTEST, SOFTWARE_VER, sizeof(SOFTWARE_VER));

    rt_memcpy_s(&buff[offset], SOFTNAME_LEN_APPTEST, soft_name, SOFTNAME_LEN_APPTEST);
    offset += SOFTNAME_LEN_APPTEST;
    rt_memcpy_s(&buff[offset], SOFTNAME_LEN_APPTEST, soft_ver, SOFTNAME_LEN_APPTEST);
    offset += SOFTVER_LEN_APPTEST;
    buff[offset++] = (unsigned char)(SOFTWARE_DATE_YEAR >> 8);   // 年份高字节
    buff[offset++] = (unsigned char)(SOFTWARE_DATE_YEAR & 0xFF); // 年份低字节
    buff[offset++] = SOFTWARE_DATE_MONTH;                        // 月份
    buff[offset++] = SOFTWARE_DATE_DAY;                          // 日期

    cmd_buf_temp->data_len = offset;

    return SUCCESSFUL;
}

Static int acmu_reset_config_para(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(get_apptest_flag() == TRUE, NO_NEED_REPONSE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;

    restore_para_to_fact(EXCLUDE_TYPE_CONFIG);
    update_para();
    acmu_update_baudrate();
    acmu_update_host_address();

    cmd_buf_temp->data_len = 0;
    return SUCCESSFUL;
}

Static int acmu_delete_hisrecord(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(get_apptest_flag() == TRUE, NO_NEED_REPONSE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff;
    unsigned char offset = 0;
    buff = cmd_buf_temp->buf;
    
    if(buff[offset++] == 0)
    {
        delete_record_data_new(AUTH_CODE_HIS_ALARM , RECORD_TYPE_HIS_ALARM);
    }
    if(buff[offset++] == 0)
    {
        delete_record_data_new(AUTH_CODE_HIS_ACTION , RECORD_TYPE_HIS_ACTION);
    }
    if(buff[offset++] == 0)
    {
        delete_record_data_new(AUTH_CODE_HIS_DATA , AUTH_CODE_HIS_DATA);
    }
    if(buff[offset++] == 0)
    {
        delete_extreme_data();
    }

    cmd_buf_temp->data_len = 0;
    return SUCCESSFUL;
}

Static int acmu_parse_set_led_state(void* dev_inst, void* cmd_buff) {
    unsigned char command_type = 0;
    unsigned char led_state = 0;
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff = NULL;

    if (get_apptest_flag() == FALSE) {
        return NO_NEED_REPONSE;
    }

    if (dev_inst == NULL || cmd_buff == NULL ||
        cmd_buf_temp->buf == NULL ||
        cmd_buf_temp->data_len < 2) {
        return FAILURE;
    }

    buff = cmd_buf_temp->buf;
    command_type = buff[0];
    led_state = buff[1];

    if(command_type >= COMMAND_TYPE_RUN_LED && command_type <= COMMAND_TYPE_COMM_LED && led_state <= LEDOFF)
    {
        led_set_status((command_type - 0x80), led_state);
    }
    else
    {
        cmd_buf_temp->data_len = 0;
        return FAILURE;
    }

    cmd_buf_temp->data_len = 0;
    return SUCCESSFUL;
}

Static int acmu_parse_set_buzz_state(void* dev_inst, void* cmd_buff) {
    unsigned char buzz_state = 0;
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff = NULL;

    if (get_apptest_flag() == FALSE) {
        return NO_NEED_REPONSE;
    }

    if (dev_inst == NULL || cmd_buff == NULL ||
        cmd_buf_temp->buf == NULL ||
        cmd_buf_temp->data_len < 1) {
        return FAILURE;
    }

    buff = cmd_buf_temp->buf;
    buzz_state = buff[0];

    beep_control(buzz_state);

    cmd_buf_temp->data_len = 0;
    return SUCCESSFUL;
}

Static int acmu_usart_can_loopback_test_pack(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    int offset = 0;
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;
    data_buff[offset++] = s_usart_can_test_result;
    ((cmd_buf_t*)cmd_buff)->data_len = offset;
    s_usart_can_test_result = TEST_FAULT;

    return SUCCESSFUL;
}

Static int acmu_usart_loopback_test_parse(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(get_apptest_flag() == TRUE, NO_NEED_REPONSE);
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    rt_device_t test_dev = rt_device_find("usart1");
    if (!test_dev)
    {
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA;
        return FAILURE;
    }

    // 标记测试开始
    set_apptest_usart_test_flag(TRUE);
    rt_thread_mdelay(100);

    // 发送10字节
    rt_device_write(test_dev, 0, s_usart_can_test_frame, sizeof(s_usart_can_test_frame) / sizeof(s_usart_can_test_frame[0]));

    // 接收10字节
    s_usart_can_test_result = usart_rev_handle(test_dev);

    // 测试结束
    set_apptest_usart_test_flag(FALSE);

    return SUCCESSFUL;
}

Static int acmu_can_loopback_test_parse(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(get_apptest_flag() == TRUE, NO_NEED_REPONSE);
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    rt_device_t can_dev;
    struct rt_can_msg tx_msg = {0};
    rt_err_t res;
    struct rt_can_status status = {0};

    //获取 can0 设备
    can_dev = rt_device_find("can1");
    if (can_dev == RT_NULL)
    {
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA;
        return FAILURE;
    }

    // 标记测试开始
    set_apptest_can_test_flag(TRUE);
    rt_thread_mdelay(100);

    //构造发送消息
    tx_msg.id  = 0x123;                    // 标准帧 ID
    tx_msg.ide = RT_CAN_STDID;             // 标准 ID
    tx_msg.rtr = RT_CAN_DTR;               // 数据帧
    tx_msg.len = 8;                        // 8 字节数据
    for (int i = 0; i < 8; i++)
    {
        tx_msg.data[i] = s_usart_can_test_frame[i];
    }

    //发送
    res = rt_device_write(can_dev, 0, &tx_msg, sizeof(tx_msg));

    // 接收
    s_usart_can_test_result = can_rev_handle(can_dev);

    // 测试结束
    set_apptest_can_test_flag(FALSE);

    return SUCCESSFUL;
}

// 串口数据接收处理函数
Static int usart_rev_handle(rt_device_t dev)
{
    unsigned char temp_buff[8];
    int recv_len = 0;
    const rt_tick_t timeout = rt_tick_get() + rt_tick_from_millisecond(100);

    while (recv_len < 8 && rt_tick_get() < timeout)
    {
        int len = rt_device_read(dev, 0, &temp_buff[recv_len], 8 - recv_len);
        if (len > 0)
        {
            recv_len += len;
        }
        else
        {
            rt_thread_mdelay(2);
        }
    }

    if (recv_len == 8 && rt_memcmp(temp_buff, s_usart_can_test_frame, 8) == 0)
    {
        return TEST_NORMAL;
    }

    return TEST_FAULT;
}

// CAN 数据接收处理函数
Static int can_rev_handle(rt_device_t dev)
{
    struct rt_can_msg rx_msg;
    const rt_tick_t timeout = rt_tick_get() + rt_tick_from_millisecond(100);

    while (rt_tick_get() < timeout)
    {
        if (rt_device_read(dev, 0, &rx_msg, sizeof(rx_msg)) == sizeof(rx_msg) &&
            rt_memcmp(rx_msg.data, s_usart_can_test_frame, 8) == 0)
        {
            return TEST_NORMAL;
        }
        rt_thread_mdelay(2);
    }

    return TEST_FAULT;
}

Static int acmu_parse_set_start_button_test(void* dev_inst, void* cmd_buff) {
    unsigned char buzz_state = 0;
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff = NULL;

    if (get_apptest_flag() == FALSE) {
        return NO_NEED_REPONSE;
    }

    if (dev_inst == NULL || cmd_buff == NULL) {
        return FAILURE;
    }

    clear_button_press_count();

    cmd_buf_temp->data_len = 0;
    return SUCCESSFUL;
}

Static int acmu_pack_get_button_test_state(void* dev_inst, void* cmd_buff) {
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff = NULL;
    unsigned int offset = 0;

    if (dev_inst == NULL || cmd_buff == NULL ||
        cmd_buf_temp->buf == NULL) {
        return FAILURE;
    }

    buff = cmd_buf_temp->buf;
    buff[offset++] = get_button_press_test_status(esc_button_index);    //esc按键测试结果
    buff[offset++] = get_button_press_test_status(up_button_index);     //up按键测试结果
    buff[offset++] = get_button_press_test_status(down_button_index);   //down按键测试结果
    buff[offset++] = get_button_press_test_status(enter_button_index);  //enter按键测试结果
    buff[offset++] = get_button_press_test_status(left_button_index);   //left按键测试结果
    buff[offset++] = get_button_press_test_status(right_button_index);  //right按键测试结果
    cmd_buf_temp->data_len = offset;

    return SUCCESSFUL;
}

Static int acmu_pack_zero_slope(void* dev_inst, void* cmd_buff) {
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff = NULL;
    unsigned char offset = 0;
    int i = 0;
    char c_data = 0;
    float f_data = 0;

    if (get_apptest_flag() == FALSE) {
        return NO_NEED_REPONSE;
    }

    if (dev_inst == NULL || cmd_buff == NULL) {
        return FAILURE;
    }
    buff = cmd_buf_temp->buf;
    for(i = 0; i < sizeof(s_zero_slope_info)/sizeof(s_zero_slope_info[0]); i++) {
        if(TYPE_INT8S == GET_SYS_PARA_DATA_TYPE(s_zero_slope_info[i].sid))
        {
            get_one_para(s_zero_slope_info[i].sid, &c_data);
            buff[offset] = c_data;
            offset += 1;
        }
        else
        {
            get_one_para(s_zero_slope_info[i].sid, &f_data);
            put_float_to_buff(&buff[offset], f_data);
            offset += 4;
        }
    }

    /* LENID */
    cmd_buf_temp->data_len = offset;
    return SUCCESSFUL;
}

Static int acmu_parse_zero_slope(void* dev_inst, void* cmd_buff) {
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff = NULL;
    unsigned short command_info = 0;
    int i = 0;
    float data = 0;

    if (get_apptest_flag() == FALSE) {
        return NO_NEED_REPONSE;
    }

    if (dev_inst == NULL || cmd_buff == NULL) {
        return FAILURE;
    }
    buff = cmd_buf_temp->buf;
    command_info = ((uint16_t)buff[0] << 8) | buff[1];
    for(i = 0; i < sizeof(s_zero_slope_info)/sizeof(s_zero_slope_info[0]); i++) {
        if (s_zero_slope_info[i].command_type == command_info)
        {
            if(TYPE_INT8S == GET_SYS_PARA_DATA_TYPE(s_zero_slope_info[i].sid))
            {
                set_one_para(s_zero_slope_info[i].sid, &buff[2], TRUE, TRUE);
            }
            else
            {
                data = get_float_data(&buff[2]);
                set_one_para(s_zero_slope_info[i].sid, &data, TRUE, TRUE);
            }
        }
    }

    /* LENID */
    cmd_buf_temp->data_len = 0;
    return SUCCESSFUL;
}
