



#ifndef _BMU_CELL_BALANCE_H
#define _BMU_CELL_BALANCE_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

/*  Include   */
#include "data_type.h"
#include <stdio.h>
#include <rtthread.h>
#include <rtdevice.h>
#include <string.h>

typedef struct
{
    unsigned char ucBalanceReg;                //单节电压检测数量
    unsigned char ucVolReg;
    unsigned char bOddBit[2];
    unsigned char ucCellVoltNum[2];
    unsigned char passive_flag;            //当设置为1 TURE，被动均衡, false 均衡 
}cell_balance_data_t;


void cell_balance_process_start(void);
void cell_balance_control(unsigned int cell_balance_control_status);
void cell_balance_timer_start();
void cell_balance_timer_stop();
void start_balance_fault_detected();
unsigned char get_balance_passive_flag(void);
unsigned char get_equal_circuit_fault(void);
unsigned char* get_equal_circuit_open_fault(void);

#ifdef __cplusplus
}
#endif  // __cplusplus

#endif  // _BMU_CELL_BALANCE_H