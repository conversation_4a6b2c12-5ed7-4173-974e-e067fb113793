/**
 * @brief 604a main板北向通信头文件
 */

#ifndef _604A_MAIN_NORTH_COMM_H_
#define _604A_MAIN_NORTH_COMM_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "cmd.h"
#include "linklayer.h"
#include "protocol_layer.h"

#define MAX_NORTH_COMM_BUFF_LEN  1024

typedef struct {
    void*        data;
    cmd_buf_t*   cmd_buff;
    link_inst_t* link_inst;
}north_mgr_t;

typedef struct boot_info
{
    unsigned char ver[32];
    unsigned char date[12];
} boot_info_t;

void*  init_north(void * param);
void north_comm_th(void *param);
int judge_north_comm_status(rt_tick_t comm_fail_time);
void handle_modbus_data(north_mgr_t* north_mgr, rt_tick_t* comm_fail_time);
void handle_received_data(north_mgr_t* north_mgr, rt_tick_t* comm_fail_time);
#ifdef __cplusplus
}
#endif      //< __cplusplus

#endif      //< _604A_MAIN_NORTH_COMM_H_
