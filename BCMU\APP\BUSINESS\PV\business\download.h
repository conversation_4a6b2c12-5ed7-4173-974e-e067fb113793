/**
 * @file     download.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR> @date     
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */

#ifndef _DOWNLOAD_H
#define _DOWNLOAD_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "device_type.h"

/* 远程下载协议命令ID:唯一标示一个命令 */
#define DOWNLOAD_DATA_TRIG                       1        ///<  触发
#define DOWNLOAD_DATA_DOWNLOAD                   2        ///<  下载
#define DOWNLOAD_DATA_DOWNLOAD_EXTEND            3        ///<  下载扩展
#define DOWNLOAD_VERSION_CMP                     4        ///<  版本比较
#define DOWNLOAD_STOP_UPDATE                     5        ///<  停止升级
#define DOWNLOAD_GET_FLAG                        6        ///<  获取前台唯一标识 
#define DOWNLOAD_GET_UPDATE_PROG                 7        ///<  获取升级进度
#define DOWNLOAD_GET_FRAME_MAX_LEN               8        ///<  获取单帧最大长度
#define DOWNLOAD_SET_FRAME_MAX_LEN               9        ///<  设置单帧最大长度
#define UPLOAD_FILE_LIST                         10       ///<  上传文件列表 
#define UPLOAD_FILE_DATA                         11       ///<  上传文件数据
#define DOWNLOAD_EXTERN_TRIG                     12       ///<  扩展触发帧
#define DOWNLOAD_CONCUR_DATA                     13       ///<  并发升级数据帧
#define DOWNLOAD_CONCUR_CONFIRM_DATA             14       ///<  并发升级数据确认帧
#define DOWNLOAD_CONCUR_CONFIRM_UPDATE           15       ///<  并发升级,升级确认帧
#define DOWNLOAD_MASTER_DATA_TRIG                16       ///<  主机触发
#define DOWNLOAD_MASTER_DATA_DOWNLOAD            17       ///<  主机发送，解析数据 
#define DOWNLOAD_MASTER_DATA_TRIG_RSP            18       ///<  主机触发响应
#define DOWNLOAD_SLAVE_DATA_DOWNLOAD             19       ///<  从机解析，发送数据


dev_type_t* init_dev_inverter(void);
dev_type_t* init_dev_download_IP(void);
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _DOWNLOAD_H

