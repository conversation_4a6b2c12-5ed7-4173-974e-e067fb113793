/*
 * @file    :
 * @brief   :
 * @details :
 * <AUTHOR> raojing
 * @Date    : 2023-3-27
 * @LastEditTime: 2023-3-27
 * @version : V0.0.1
 * @para    : Copyright (c)
 *            ZTE Corporation
 * @par     : History
 *     version: author, date, descn
 */
/*
 * Copyright (c) 2006-2018, RT-Thread Development Team
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Change Logs:
 * Date           Author       Notes
 * 2018-11-7      SummerGift   first version
 */

#ifndef _ADC_H_
#define _ADC_H_

#ifdef __cplusplus
extern "C" {
#endif
#ifdef USING_DEVICE_BSC_UIB03
typedef enum 
{
	VCC2V5_T,
	UIB03_TB_IN_1,
	UIB03_TB_IN_2,
	UIB03_RLY_IN3,
	PIN_DOOR_IN,
	PIN_WATER_IN,
	UIB03_HUM_IN,
	UIB03_FAN_IN1,
	UIB03_FAN_IN2,
	UIB03_PWM_OUT,
	UIB03_RLY_IN1,
	UIB03_RLY_IN2,
	SAMPLE_CHANNL_END,
}ADC_SAMPLE_CHANNEL;
#else
typedef enum 
{
	 IL1 = 0,
	 IL2,
	 IL3,
	 IL4,
	 IL5,
	 IL6,
	 IL7,
	 IL8,
	 IL9,
 	 IL10,
	 F_01,
	 F_02,
	 F_03,
	 F_04,
	 F_05,
	 F_06,
	 F_07,
	 F_08,
	 TB_IN1,
	 TB_IN2,
	 F_09,
	 F_10,
	 JK01,
	 JK02,
	 JK03,
	 JK04,
	 JK05,
	 JK06,
	 JK07,
	 JK08,
	 JK09,
	 JK10,
     SAMPLE_CHANNL_END,
}ADC_SAMPLE_CHANNEL;
#endif
typedef struct 
{
	 ADC_SAMPLE_CHANNEL    sample_channel;
	 int                   select_status;
	 int                   adc_channel;
}ADC_CHANNEL_INFO_STRUCT;

int adc_get_data_by_channel(ADC_SAMPLE_CHANNEL channel);
int get_board_addr(void);
int hal_board_gpio_init(void);

#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // _ADC_H_;
