#ifndef _SAMPLE_NEW_H
#define _SAMPLE_NEW_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

#include <rtthread.h>
#include <stdint.h>
#include <adc.h>

#define BMU_VER            "V1.00.00.00"
#define AFE_CHIP_TYPE       "MT9805"
#define SOFTWARE_RELEASE_YEAR (2025)
#define SOFTWARE_RELEASE_MONTH (8)
#define SOFTWARE_RELEASE_DATE (25)


#define ADC_NUM 77  // 一共77个温度
#define CELL_TEMP_SAMPLE_NUM 35
#define SAMPLE_TEMP_TATAL_NUM 43    // 电芯温度35个，单板温度1个，端子温度两个，均衡电阻温度5个
#define CELL_TEMP_NUM 65
#define BATT_POS_TEMP_INDEX 35
#define BATT_NEG_TEMP_INDEX 36
#define BATT_BORAD_TEMP_INDEX 37
#define BALANCE_RESISTANCE_TEMP_INDEX 38
#define ADC_DEV_NAME0 "adc0"

#define SAMPLE_CHIP_NUM 5   //采样芯片数量
#define CHIP_CELL_NUM   13  //每个采样芯片电压有效数量

#define REFER_VOLTAGE 3.0f          /* VOLTAGE is 3.0V */
#define CONVERT_BITS 4096           /* 12bit */
#define INVALID_VOLTAGE 0.0f
#define INVALID_SAMPLE_DATA 0Xffff  //异常电压采样数据
#define INVALID_SAMPLE_TEMPERATURE -75.0f
#define TEN_SECOND_COUNT 100    //采样现场10s计数
#define EPSILON 0.0001  // 浮点数比较的容差值

#define INVALID_SAMPLE_TEMPERATURE_MAX 150.0f
#define INVALID_SAMPLE_TEMPERATURE_MIN -40.0f

#ifndef UNITEST
// 片选引脚定义
#define DO_1_A0 GET_PIN(D, 13)
#define DO_1_A1 GET_PIN(D, 14)
#define DO_1_A2 GET_PIN(D, 15)
#define FIRE_BMU_PIN_1                 GET_PIN(E, 10)  // 消防干接点
#define FIRE_BMU_PIN_2                 GET_PIN(E, 11)  // 消防干接点
#define MSD_BMU_PIN                    GET_PIN(E, 12)  // MSD通断状态
#else
#define DO_1_A0 1
#define DO_1_A1 2
#define DO_1_A2 3
#define FIRE_BMU_PIN_1 4
#define FIRE_BMU_PIN_2 6
#define MSD_BMU_PIN  5
#endif

#define CHANNEL_SAMPLE_NUM  8   //通过片选信号单设备采样数量

// 采样通道引脚定义
#define SAMPLE_CHANNEL_0 10
#define SAMPLE_CHANNEL_1 11
#define SAMPLE_CHANNEL_2 12
#define SAMPLE_CHANNEL_3 13
#define SAMPLE_CHANNEL_4 0
#define SAMPLE_CHANNEL_5 1
#define SAMPLE_CHANNEL_6 14
#define SAMPLE_CHANNEL_7 15
#define SAMPLE_CHANNEL_8 8
#define SAMPLE_CHANNEL_9 9

#define Normal 0    //正常
#define Fault  1    //异常

// 定义采样状态枚举
typedef enum {
    SAMPLE_VALID = 0,
    SAMPLE_INVALID
} SampleStatus;

// 电压采样数据结构
typedef struct {
    float voltage[65];
    SampleStatus status[5];
} VoltageSampleData;

typedef struct {
    float buffer[65][10];
    int index;
} VoltageSampleBuffer;

// 温度采样数据结构
typedef struct {
    float temperature[SAMPLE_TEMP_TATAL_NUM];
    SampleStatus status[SAMPLE_TEMP_TATAL_NUM];
} TemperatureSampleData;

// 电压采样缓冲区结构
typedef struct {
    float buffer[SAMPLE_TEMP_TATAL_NUM][10]; // 存储最近10次的电压采样数据
    int index; // 当前缓冲区索引
} TemperatureSampleBuffer;

// MT9805芯片采样数据结构
typedef struct {
    uint8_t chipNum;
    uint16_t volData[18]; // 只有前13个数据有效
    uint16_t balTemp;
    uint8_t chipStatus;
    uint8_t balStatus;
} MT9805Data;

int initialize_temperature_buffer(TemperatureSampleBuffer* buffer);
int init_bmu_soft_version(void);
void average_filter_temperature(TemperatureSampleBuffer* buffer, TemperatureSampleData* TemperatureData);
float interpolate_temperature(int resistance);
int initialize_chip(void);
int read_chip_sample_data(VoltageSampleData* outVoltageData);
void set_sample_data(TemperatureSampleData* temperature_data, VoltageSampleData* voltage_data);
int get_temperature_sample_values(void);
int check_temperature_sample_status(TemperatureSampleData* temperatureData);
void *init_sampling(void *param);
void sample_main_new(void * param);
int set_fire_data(void);
int set_msd_data(void);


#ifdef __cplusplus
}
#endif  // __cplusplus

#endif  // _SAMPLE_NEW_H
