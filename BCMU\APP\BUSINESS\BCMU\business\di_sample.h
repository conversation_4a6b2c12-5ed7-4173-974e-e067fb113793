/**
 * @file     device_type.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2023-01-04
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */

 
#ifndef _DI_SAMPLE_H
#define _DI_SAMPLE_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "data_type.h"



#pragma pack(1)

/* 输入干接点状态结构体 */
typedef struct {
    unsigned char di_pin_no;   ///<  DI干接点引脚号，使用宏传入
    unsigned char state;       ///<   干接点状态， 1 高，0 低
} di_data_t;


/* 输入干接点管理结构体 */
typedef struct {
    unsigned char di_pin_no;                       ///<  DI干接点引脚号，使用宏传入
    void        (*di_irq_callback)(void* arg);     ///<  DI中断回调接口
    unsigned int  irq_mode;                        ///<  DI中断模式
} di_manage_t;

#pragma pack()


void di_sample_init(di_manage_t* di_manage, unsigned char di_num);
int get_di_state(di_data_t* di_data);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _DI_SAMPLE_H
