#include "common.h"
#include "battery.h"
#include "CommCan.h"
#include "MultBattData.h"
#include "ChargeRotate.h"
#include "BattDataLoad.h"
#include "usart.h"

#ifdef USE_CAN2
#include "CommCan2.h"
#endif
//#define CHARGE_VOL_DIFF (0.45f)   //功率侧要求外部电压和掉电电压阈值压差达到0.45才会有充电电流

Static WORD get_poweron_check_times(T_BattInfo *ptBattIn);
#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
static BOOLEAN JudgeB3MixPowerOn(T_BattInfo *ptBattIn, T_BattDealInfoStruct *ptBattDeal);
Static INT32 JudgeCAN2Powerdown(BOOLEAN* pbStatus, T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattIn);
#endif

static BOOLEAN IsChgInputBreak(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattIn)
{
    if (BATT_MODE_DISCHARGE == ptBattDeal->ucChargeMode || IsTransPeriod())
    {
        return False;
    }

    if (ptBattIn->tData.bSlaveChargeBreak)
    {
        return True;
    }

    return False;
}

// static BOOLEAN GetMixPowerStatusFromVolt(BOOLEAN bStartFlag, T_BattDealInfoStruct *ptBattDeal,T_BattInfo *ptBattIn)
// {
//     static FLOAT s_fBusVolMin = 0;
//     static FLOAT s_fBusVolMax = 0;
//     static WORD  s_wBusIsPowerOnCounter = 0;
//     static WORD  s_wBusVoltSampleCounter = 0;
//     if (!bStartFlag)
//     {
//         s_wBusIsPowerOnCounter = 0;
//         s_wBusVoltSampleCounter = 0;
//         ptBattDeal->wNeedChgTimer = 0;
//         ptBattDeal->bNeedDeclineDischgVolt = FALSE;
//         return FALSE;
//     }

//     if ((fabs(ptBattIn->tData.fMaxCurrInAll) < MIN_CURR_DET_BDCU) && (ptBattIn->tData.fSysVol > MIX_CHG_VOL_MIN))//放电电流小于2A并且母排电压大于52V
//     {
//         s_wBusIsPowerOnCounter += 1;
//     }
//     else
//     {
//         s_wBusIsPowerOnCounter = 0;
//         s_wBusVoltSampleCounter = 0;
//         if (ptBattDeal->bNeedDeclineDischgVolt)
//         {
//             ptBattDeal->wNeedChgTimer = 0;
//             ptBattDeal->bDirectStartChgJudge = FALSE;
//             ptBattDeal->bNeedDeclineDischgVolt = FALSE;
//         }
//         return FALSE;
//     }

//     if (s_wBusIsPowerOnCounter == THREE_MINUTES_SECOND)//持续3分钟，设置放电电压下调标志，同时准备计算母排电压波动
//     {
//         s_fBusVolMin = ptBattIn->tData.fBusVol;
//         s_fBusVolMax = s_fBusVolMin;
//         ptBattDeal->bNeedDeclineDischgVolt = TRUE;
//     }
//     else if (s_wBusIsPowerOnCounter > THREE_MINUTES_SECOND)//持续时间超过3分钟
//     {
//         if (s_wBusIsPowerOnCounter%BUS_VOLT_SAMPLE_INTERVAL == 0)//间隔10秒更新一次母排最大最小电压
//         {
//             s_wBusVoltSampleCounter += 1;
//             s_fBusVolMin = MIN(s_fBusVolMin, ptBattIn->tData.fBusVol);
//             s_fBusVolMax = MAX(s_fBusVolMax, ptBattIn->tData.fBusVol);
//             if (s_wBusVoltSampleCounter < BUS_VOLT_SAMPLE_NUM)
//             {
//                 if ((s_fBusVolMax - s_fBusVolMin) > ((FLOAT)DISCHG_VOLT_DECLINE_THRESHOLD/2))//母排电压波动阈值取放电电压下降幅度的一半
//                 {
//                     s_wBusIsPowerOnCounter = 0;
//                     s_wBusVoltSampleCounter = 0;
//                     ptBattDeal->wNeedChgTimer = 0;
//                     ptBattDeal->bDirectStartChgJudge = FALSE;
//                     ptBattDeal->bNeedDeclineDischgVolt = FALSE;
//                 }
//             }
//             else
//             {
//                 s_wBusIsPowerOnCounter = 0;
//                 s_wBusVoltSampleCounter = 0;
//                 ptBattDeal->wNeedChgTimer = 0;
//                 ptBattDeal->bDirectStartChgJudge = FALSE;
//                 ptBattDeal->bNeedDeclineDischgVolt = FALSE;
//                 if ((s_fBusVolMax - s_fBusVolMin) <= ((FLOAT)DISCHG_VOLT_DECLINE_THRESHOLD/2))//母排电压波动阈值取放电电压下降幅度的一半
//                 {
//                     ptBattIn->tPara.fSysPowerOnThreshold  = 52.0f;
//                     ptBattIn->tPara.fSysPowerOffThreshold = 48.0f;
//                     //ptBattIn->tPara.fPowerDownVolt        = 51.0f;
//                     ptBattDeal->fPowerDownVolt            = 51.0f;
//                     return TRUE;
//                 }
//             }
//         }
//     }
//     return FALSE;
// }

/***************************************************************************
 * @brief   判断电池特性模式下是否满足来电 
 * @return   
 **************************************************************************/
static BOOLEAN CheckCharacteristicPowerOn(T_BattInfo *ptBattIn, T_BattDealInfoStruct *ptBattDeal, WORD wCheckCounter)
{
#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
    if (True == IfConditionOkNTimes(isExternalPowerOn() && JudgeB3MixPowerOn(ptBattIn, ptBattDeal), &(ptBattIn->tData.wJudgePowerOnCounter), wCheckCounter))
#else
    if (True == IfConditionOkNTimes(isExternalPowerOn(), &(ptBattIn->tData.wJudgePowerOnCounter), wCheckCounter))
#endif
    {
        return True;
    }
    return False;
}

/***************************************************************************
 * @brief   判断恒压模式下是否满足来电 
 * @return   
 **************************************************************************/
static BOOLEAN CheckConstVoltPowerOn(T_BattInfo *ptBattIn, T_BattDealInfoStruct *ptBattDeal, WORD wCheckCounter)
{
#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
    if (True == IfConditionOkNTimes(isExternalPowerOn()&&(ptBattIn->tData.fBusVol > ptBattIn->tPara.fRemoteSuplyVolt+0.6)&&JudgeB3MixPowerOn(ptBattIn, ptBattDeal), &(ptBattIn->tData.wJudgePowerOnCounter), wCheckCounter))
#else
    if (True == IfConditionOkNTimes(isExternalPowerOn()&&(ptBattIn->tData.fBusVol > ptBattIn->tPara.fRemoteSuplyVolt+0.6), &(ptBattIn->tData.wJudgePowerOnCounter), wCheckCounter))
#endif
    {
        return True;
    }
    return False;
}

/***************************************************************************
 * @brief   判断自适应模式下是否满足来电 
 * @return   
 **************************************************************************/
static BOOLEAN CheckSelfAdaptionPowerOn(T_BattInfo *ptBattIn, T_BattDealInfoStruct *ptBattDeal, WORD wCheckCounter)
{
#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
    if (True == IfConditionOkNTimes((ptBattIn->tData.fSysVol > ptBattIn->tPara.fSysPowerOnThreshold) && isExternalPowerOn() && JudgeB3MixPowerOn(ptBattIn, ptBattDeal), &(ptBattIn->tData.wJudgePowerOnCounter), wCheckCounter))
#else
    if (True == IfConditionOkNTimes((ptBattIn->tData.fSysVol > ptBattIn->tPara.fSysPowerOnThreshold) && isExternalPowerOn(), &(ptBattIn->tData.wJudgePowerOnCounter), wCheckCounter))
#endif
    {
        return True;
    }
    return False;
}

static INT32 GetPowerStatusFromVolt(BOOLEAN* pbStatus, T_BattDealInfoStruct *ptBattDeal,T_BattInfo *ptBattIn)
{
    WORD wCheckCounter = get_poweron_check_times(ptBattIn);
    
    if (DISCHG_MODE_BATT_CHARACTERISTIC == ptBattIn->tPara.ucUsageScen)
    {
        if(CheckCharacteristicPowerOn(ptBattIn, ptBattDeal, wCheckCounter))
        {
            *pbStatus = False;
            return SUCCESSFUL;
        }
    }
    else if (DISCHG_MODE_CONSTANT_VOLTAGE == ptBattIn->tPara.ucUsageScen)
    {
        if(CheckConstVoltPowerOn(ptBattIn, ptBattDeal, wCheckCounter))
        {
            *pbStatus = False;
            return SUCCESSFUL;
        }
    }
    else if (DISCHG_MODE_SELF_ADAPTION == ptBattIn->tPara.ucUsageScen || DISCHG_MODE_SOLAR == ptBattIn->tPara.ucUsageScen)
    {
        if(CheckSelfAdaptionPowerOn(ptBattIn, ptBattDeal, wCheckCounter))
        {
            *pbStatus = False;
            return SUCCESSFUL;
        }

        /*if (BATT_MODE_DISCHARGE == ptBattDeal->ucChargeMode)
        {
            if (ptBattDeal->bDirectStartChgJudge || (ptBattDeal->wNeedChgTimer >= START_CHG_JUDGE_INTERVAL))
            {
                if (GetMixPowerStatusFromVolt(TRUE, ptBattDeal, ptBattIn) == TRUE)
                {
                    *pbStatus = False;
                    ptBattDeal->bNeedLimitCurrCharge = TRUE;
                    return SUCCESSFUL;
                }
            }
            if (ptBattDeal->wNeedChgTimer >= START_CHG_JUDGE_INTERVAL)
            {
                ptBattDeal->bDirectStartChgJudge = FALSE;
            }
        }
        else
        {
            GetMixPowerStatusFromVolt(FALSE, ptBattDeal, ptBattIn);
        }*/
    }

    return FAILURE;
}

BOOLEAN SmartLi_IsPowerDown_Master(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattIn)
{
    BOOLEAN bPowerOff = False;
    T_ProtocolSetInfoStruct tSetVal;

    //下点继电器断开的 或者 开始激活的过程中 或者 激活回路电流异常后，不能转充电，不判断充放电状态，保持放电状态
    if( ptBattDeal->bActivateStart == TRUE || ptBattDeal->bActivatePortCurrError == TRUE)
    {
        ptBattIn->tData.wJudgePowerOnCounter = 0;       //转充电计数清0
        return TRUE;
    }

    SetExternalPowerOnFlag(IfExistExternalPowerOn());

    //交流输入断表示停电
    if (IsChgInputBreak(ptBattDeal,ptBattIn))
    {
        ptBattIn->tData.wJudgePowerOnCounter = 0;
        return True;
    }
#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
    if((INT32)SUCCESSFUL == JudgeCAN2Powerdown(&bPowerOff, ptBattDeal, ptBattIn))
    {
        return bPowerOff;
    }
#endif
    if (FAILURE != GetValueFromProtocol(INFO_TYPE_STATUS,&tSetVal))
    {
        return tSetVal.sValue == POWER_STATUS_OFF;
    }

    if ((INT32)SUCCESSFUL == GetPowerStatusFromVolt(&bPowerOff, ptBattDeal, ptBattIn))
    {
#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
        if(IsMasterCan2())
        {
            if(!ptBattIn->tData.bCAN2DischExist)
            {
                return bPowerOff;
            }
        }
        else
        {
            return bPowerOff;
        }
#else
        return bPowerOff;
#endif
    }    

    return ptBattDeal->bPowerOff;
}

/***************************************************************************
 * @brief   簇间并机时来电停电判断增加电流条件
 **************************************************************************/
#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
Static INT32 JudgeCAN2Powerdown(BOOLEAN* pbStatus, T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattIn)
{
    WORD wPowerOnCount = get_poweron_check_times(ptBattIn);

    if(IsMasterCan2())
    {
        if(ptBattIn->tData.bCAN2DischExist)
        {
            ptBattIn->tData.wJudgePowerOnCounter = 0;
            *pbStatus = True;
            return SUCCESSFUL;
        }
    }
    else
    {
        if(ptBattDeal->tEqualObjData.bValid)
        {
            if(ptBattDeal->tEqualObjData.ucObjSta)
            {
                ptBattIn->tData.wJudgePowerOnCounter = 0;
                *pbStatus = True;
                return SUCCESSFUL;
            }
            else if(isExternalPowerOn() || (0 == GetBattExistNum()))
            {
                TimerPlus(ptBattDeal->ucCAN2SlavePowerOnCount, wPowerOnCount);
                if(TimeOut(ptBattDeal->ucCAN2SlavePowerOnCount, wPowerOnCount))
                {
                    *pbStatus = False;
                    return SUCCESSFUL;
                }
            }
        }
    }
    return FAILURE;
}
#endif

void MultBattMixPowerOnAndOffVolt_Master(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo, FLOAT *pfPowerDownVol, FLOAT *pfPowerOffVol)
{
    if(ptBattDeal->wBusVoltNum > 0 && (DISCHG_MODE_SELF_ADAPTION == ptBattInfo->tPara.ucUsageScen || DISCHG_MODE_SOLAR == ptBattInfo->tPara.ucUsageScen))
    {
        ptBattDeal->fSlavePowerDownCompVolt = 0.00f;
        ptBattDeal->bChargeEqualCurrValid = False;
    }
}

void BMSChgButBDUDischg_Master(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo)
{
    /*
    受控模式，如果bdu自动切放电，可能是系统真的掉电但是母排还没判断出来,不能连续发启动充电命令.
    */
    if (ptBattInfo->tData.bRunModeControled )
    {
        TimerPlus(ptBattDeal->ucControlledDelayCnt, (BYTE)ONE_MINUTE_PER_SECONDS);
        if (ptBattDeal->ucControlledDelayCnt >= (BYTE)ONE_MINUTE_PER_SECONDS)
        {
            ptBattDeal->ucBduCtrlMode           = BDU_CHG_START;
            ptBattDeal->ucControlledDelayCnt = 0;
        }
    }
    else if (!IsTransPeriod())
    {
        ptBattDeal->ucBduCtrlMode           = BDU_CHG_START;
    }
    if(ptBattDeal->bCellUnderVolt)
    {
        ptBattDeal->ucBduCtrlMode = BDU_CHG_START;            
    }
}

//掉电电压的计算
void GetPowerdownVolt_Master(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    #ifdef SYNC_POWER_DOWN_VOLTAGE
    if (pBattIn->tPara.ucRunMode == RUN_MODE_CONTROLLED && !JudgeCommDisconnect())
    {
//        pBattDeal->fPowerDownVolt = 48.0f - CHARGE_VOL_DIFF;   //48V以上有充电电流
        return;
    }
    else if(pBattIn->tPara.ucUsageScen == DISCHG_MODE_CONSTANT_VOLTAGE)
    {
        pBattDeal->fPowerDownVolt = pBattDeal->fDischargeHopeVol;
        SetPowerdownVoltSlave(pBattDeal->fPowerDownVolt*100.0);
    }
    else if(pBattIn->tPara.ucUsageScen == DISCHG_MODE_BATT_CHARACTERISTIC)
    {
        pBattDeal->fPowerDownVolt = pBattIn->tData.fBatVol * g_ProConfig.fVoltTrasRate - 0.5;
    }
    else if(pBattIn->tPara.ucUsageScen == DISCHG_MODE_SELF_ADAPTION || pBattIn->tPara.ucUsageScen == DISCHG_MODE_SOLAR)
    {
        SetPowerdownVoltSlave(pBattDeal->fPowerDownVolt*100.0);
    }

    if (pBattDeal->bActivatePowerOffFlag == TRUE)
    {
        pBattDeal->fPowerDownVolt = 48;
    }
    #else
    if (pBattIn->tPara.ucRunMode == RUN_MODE_CONTROLLED && !JudgeCommDisconnect())
    {
//        pBattDeal->fPowerDownVolt = 48.0f - CHARGE_VOL_DIFF;  //48V以上有充电电流
        return;
    }
    else if(pBattIn->tPara.ucUsageScen == DISCHG_MODE_CONSTANT_VOLTAGE)
    {
        pBattDeal->fPowerDownVolt = pBattDeal->fDischargeHopeVol;
    }
    else if(pBattIn->tPara.ucUsageScen == DISCHG_MODE_BATT_CHARACTERISTIC)
    {
        pBattDeal->fPowerDownVolt = pBattDeal->fDischargeHopeVol;
    }

    SetPowerdownVoltSlave(pBattDeal->fPowerDownVolt*100.0);
    #endif
}
#ifdef DEVICE_USING_R321
void SmartLi_CalcDischargeOutputVolt_Master(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo)
{
    if(ptBattInfo->tData.bRunModeControled && ptBattDeal->tOutputVoltByCtrl.bValid)
    {
        ptBattDeal->fDischargeHopeVol       = GetValidData(ptBattDeal->tOutputVoltByCtrl.sValue/100.0, OUT_VOLT_MAX, OUT_VOLT_MIN);
        /*
        if comm fail,will detect system voltage and adjust output volt. 
        Added by fengfj, 2020-05-09 16:02:41
        */
        ptBattDeal->ucDischgDelayCounter    = 0;
        ptBattDeal->bChargeCurrEnough       = False;
    }
    else if(!ptBattInfo->tData.bRunModeControled && ptBattDeal->tOutputVoltNonCtrl.bValid)
    {
        ptBattDeal->fDischargeHopeVol       = GetValidData(ptBattDeal->tOutputVoltNonCtrl.sValue/100.0, OUT_VOLT_MAX, OUT_VOLT_MIN);
        ptBattDeal->ucDischgDelayCounter    = 0;
        ptBattDeal->bChargeCurrEnough       = False;
    }
    else
    {
//        s_tBattDeal.fDischargeHopeVol   = MAX(s_tBattDeal.fSysVoltOnPowerOff+s_tBattIn.tPara.fOutVoltOffset, OUT_VOLT_MIN);
        
        if (ptBattDeal->bPowerOff || ptBattDeal->bPeakDischg || ptBattDeal->bFmDischg)
        {
            CheckDishargeModeAndSwitch();
            CheckRemoteSuplyOutput(ptBattDeal, ptBattInfo);
        }
    }
    ptBattDeal->fDischargeSetVol = ptBattDeal->fDischargeHopeVol;

    if(ptBattInfo->tData.bB3BattExist)
    {
        CalBoostVolB3();
    }
    else
    {
        CalBoostVol();
    }
    ptBattDeal->fDischgEqualCurr = ptBattDeal->fDischgEqualCurr*0.5 + fabs(ptBattInfo->tData.fBusCurr)*0.5;

    LoopOffVoltAdjust(ptBattDeal, ptBattInfo);             //充电不起作用
    return;
}
#else
//主机下发从机放电电压
static VOID MasterSetDischargeVoltToSlave(T_BattDealInfoStruct *ptBattDeal)
{
    if(ptBattDeal->ucChargeMode == BATT_MODE_CHARGE || ptBattDeal->ucChargeMode == BATT_MODE_STANDBY)
    {
        setVol( BATT_MODE_CHARGE, (SHORT)(ptBattDeal->fDischargeSetVol*100.0));
    }
    else
    {
        setVol( BATT_MODE_DISCHARGE, (SHORT)(ptBattDeal->fDischargeSetVol*100.0));
    }
    return;
}

void SmartLi_CalcDischargeOutputVolt_Master(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo)
{
    static FLOAT s_fBatVoltSum = 0;
	static BYTE s_fBatVoltCounter = 0;
	static BYTE s_bBatteryModeBak;
	static FLOAT s_fBatAvgVolt = 0;
	FLOAT fOutputADischargeVolt = ptBattDeal->fDischargeHopeVol;

	if(ptBattInfo->tPara.ucUsageScen == DISCHG_MODE_BATT_CHARACTERISTIC)
    {
         if(s_bBatteryModeBak == ptBattDeal->ucChargeMode)
	     {
            if(!TimeOut(s_fBatVoltCounter,10))
            {
                s_fBatVoltCounter++;
                s_fBatVoltSum += ptBattInfo->tData.fBatVol; 
                if(s_fBatVoltCounter == 10)
                {
                    s_fBatAvgVolt = s_fBatVoltSum/s_fBatVoltCounter;
                    fOutputADischargeVolt = 2*s_fBatAvgVolt;
                    s_fBatVoltCounter = 0;
                    s_fBatVoltSum = 0;
                }	
            }
	     }
         else
         {
            s_bBatteryModeBak = ptBattDeal->ucChargeMode;
            s_fBatVoltCounter = 0;
            s_fBatVoltSum = 0;
            s_fBatVoltCounter++;
            s_fBatVoltSum += ptBattInfo->tData.fBatVol; 
         }

        ptBattDeal->fDischargeHopeVol = fOutputADischargeVolt;
        ptBattDeal->fDischargeSetVol = ptBattDeal->fDischargeHopeVol;

        MasterSetDischargeVoltToSlave(ptBattDeal);
    }
    else if(ptBattInfo->tPara.ucUsageScen == DISCHG_MODE_CONSTANT_VOLTAGE)
    {
         //CheckIfEnterRemoteEnd(ptBattDeal, ptBattInfo);
         CheckRemoteSuplyOutput(ptBattDeal, ptBattInfo);
         ptBattDeal->fDischargeSetVol = ptBattDeal->fDischargeHopeVol;
         MasterSetDischargeVoltToSlave(ptBattDeal);
    }

    LoopOffVoltAdjust(ptBattDeal, ptBattInfo);             //充电不起作用
    return;
}
#endif

static void DealSourceInsufficient(BOOLEAN bModeChangeFlag, T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo)
{
    if(bModeChangeFlag)
    {
        if(!TimeOut(ptBattDeal->wSourceInsufficientCount, TEN_MINUTES_PER_SECONDS))
        { // 记录当前切换时差
            ptBattDeal->wChargeModeChangeTimeDiff = ptBattDeal->wSourceInsufficientCount;
        }
        else
        {
            ptBattDeal->wChargeModeChangeTimeDiff = 0;
        }

        ptBattDeal->wSourceInsufficientCount = 0;
    }
    else
    {
        if(TimeOut(ptBattDeal->wSourceInsufficientCount, TEN_MINUTES_PER_SECONDS))
        { // 源足计时重置
            ptBattDeal->bSourceInsufficientTimerStart = FALSE;
            ptBattDeal->wChargeModeChangeTimeDiff = 0;
            ptBattDeal->ucChargeModeChangeCount = 0;
            ptBattDeal->wSourceInsufficientCount = 0;
        }
        else
        {
            if((ptBattDeal->wSourceInsufficientCount + ptBattDeal->wChargeModeChangeTimeDiff) >= TEN_MINUTES_PER_SECONDS
                && ptBattDeal->ucChargeModeChangeCount > 0)
            { // 窗口超时，只保留最近一次充放电转换计数
                ptBattDeal->ucChargeModeChangeCount = 1;
            }
        }
    }

    return;
}

void SmartLi_CalcSysMaxChargeCurr_Master(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo)
{
    if (ptBattInfo->tData.bRunModeControled)
    {
       ptBattDeal->fSysChgMaxCurr = (FLOAT)g_ProConfig.fRateCurrChgBus;
       InitSetCurrAdjust();
    }
    else if (False == ptBattInfo->tPara.bChargeRotate)
    {
        if(ptBattDeal->ucChargeCounter < (BYTE)CHARGE_SOURCE_DELAY || GetQtptestFlag() || ptBattDeal->bBattChargeFull)
        {
            ptBattDeal->fSysChgMaxCurr = (FLOAT)g_ProConfig.fRateCurrChgBus;
            InitSetCurrAdjust();
        }
        else if(ptBattDeal->bChargeModeChange)
        {
            ptBattDeal->bSourceInsufficientTimerStart = TRUE;
            TimerPlus(ptBattDeal->ucChargeModeChangeCount, SOURCE_INSUFFICIENT_COUNT_MIN);   // 只计算最近3次切换计数

            if(ptBattDeal->wChargeModeChangeTimeDiff + ptBattDeal->wSourceInsufficientCount < TEN_MINUTES_PER_SECONDS 
                && ptBattDeal->ucChargeModeChangeCount >= SOURCE_INSUFFICIENT_COUNT_MIN)
            { // 10min滑动窗口
                ptBattDeal->fSysChgMaxCurr /= 2.0f;
            }

            DealSourceInsufficient(TRUE, ptBattDeal, ptBattInfo);

        }
        else
        {
            DealSourceInsufficient(FALSE, ptBattDeal, ptBattInfo);
        }
    }
    else
    {
        ptBattDeal->fSysChgMaxCurr = (FLOAT)g_ProConfig.fRateCurrChgBus;
    }
    ptBattDeal->fSysChgMaxCurr = GetValidData(ptBattDeal->fSysChgMaxCurr, g_ProConfig.fRateCurrChgBus,  SMART_LI_CURRENT_MIN);
    ptBattDeal->fChargeBusSetCurr = GetValidData(ptBattDeal->fChargeBusCurrHope, ptBattDeal->fSysChgMaxCurr, SMART_LI_CURRENT_MIN);
    ptBattDeal->fChargeBusSetCurr = GetValidData(ptBattDeal->fChargeBusSetCurr, (FLOAT)ptBattDeal->ucRotateMaxCurr, SMART_LI_CURRENT_MIN);
    return;
}

void startDischg_Master(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo)
{
    ptBattDeal->fDischargeHopeVol = MAX(ptBattDeal->fDischargeHopeVol, OUT_VOLT_MIN);
    if ( ptBattDeal->fDischargeHopeVol > (ptBattInfo->tData.fSysVol + ptBattInfo->tPara.fOutVoltOffset)
        && ptBattInfo->tData.fSysVol > SMART_LI_OUTPUT_VOL_MIN )
    {
        ptBattDeal->fDischargeHopeVol = MAX(ptBattInfo->tData.fSysVol + ptBattInfo->tPara.fOutVoltOffset, OUT_VOLT_MIN);
    }

#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
    if(ptBattInfo->tData.bB3BattExist)
    {
        CalBoostVolB3();
    }
    else
    {
        CalBoostVol();
    }
#else
    CalBoostVol();
#endif
}

void SetChgPara_Master(T_BattDealInfoStruct *ptBattDeal, FLOAT fChargeFullVol)
{
    FLOAT fChgVol;

    fChgVol = getChgVolt(fChargeFullVol);

    ptBattDeal->fChargeSetVol       = fChgVol;
    SetChgHopeVol( fChgVol );
    setBMSMode( BATT_MODE_CHARGE, Float2Short(fChgVol*100), 0);
    
    return;
}

Static WORD get_poweron_check_times(T_BattInfo *ptBattIn) {
    if (GetQtptestFlag())
        return (WORD) SMARTLI_SYSPOWER_COUNTER;
    
    #ifdef POWER_ON_COUNTER_ENABLE
    return ptBattIn->tPara.wPowerOnCheckTime;
    #else
    JudgeChargeTestmode();
    if (GetChargeTestMode())
        {return 0;}
    else
    {
        return (WORD) SMARTLI_SYSPOWER_COUNTER;  
    }
    #endif
}

/***************************************************************************
 * @brief  当存在和B3电池混用时，增加判断来电的条件(B3电池没有外部有电状态)
 * @return   
 **************************************************************************/
#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
static BOOLEAN JudgeB3MixPowerOn(T_BattInfo *ptBattIn, T_BattDealInfoStruct *ptBattDeal)
{
    if(!ptBattIn->tData.bB3BattExist)
    {
        return True;
    }

    if(ptBattIn->tPara.ucUsageScen == DISCHG_MODE_SELF_ADAPTION || ptBattIn->tPara.ucUsageScen == DISCHG_MODE_SOLAR)
    {
        if(ptBattIn->tData.fSysVol >= ptBattIn->tPara.fSysPowerOnThreshold)
        {
            return True;
        }
    }
    else
    {
        if(ptBattIn->tData.fSysVol >= ptBattDeal->fDischargeHopeVol + 1.0f)
        {
            return True;
        }
    }
    return False;

}
#endif

