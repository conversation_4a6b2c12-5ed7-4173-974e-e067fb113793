#ifndef _SYSTEM_MANAGE_H
#define _SYSTEM_MANAGE_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

#include "data_type.h"
#include "his_record.h"

#define SYSTEM_MANAGE_THREAD_DELAY_TIME 200  // 单位毫秒

void *init_sys_manage(void * param);
void system_manage_main(void *param);
void handle_acmu_alm_msg(_rt_msg_t curr_msg);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _SYSTEM_MANAGE_H