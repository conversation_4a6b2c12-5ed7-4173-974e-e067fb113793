
#ifndef _ACMU_UPDATE_MANAGE_H
#define _ACMU_UPDATE_MANAGE_H

#ifdef __cplusplus
extern "C" {
#endif

#include "device_type.h"

#define UPDATE_FIRST_FRAME_LEN 72
#define ACMU_UPDATE_FILE_NAME      "ACMU_APP.bin"
#define ACMU_ZK_UPDATE_FILE_NAME  "CCLIB.BIN"
#define FLAG_APP_UPDATE_ZK     1
#define FLAG_APP_UPDATE_APP    2
#define BAUD_RATE_9600           9600

//下载部分的宏定义
#define DownloadFrameErr         0x80
#define DownFileNameErr          0x86

#define TRIGGER_COUNTER        3
#define INTERRUPT_DATA_LEN     1
#define INTERRUPT_NOT_DISCARD  0
#define INTERRUPT_DISCARD      1

#define UPDATE_FILE_TIME_LEN   20
#define UPDATE_FILE_NAME_LEN   40
#define ZK_UPDATE_FILE_NAME_LEN    40
#define DOWNLOAD_UPDATE_FIRST_FRAME_LEN         72
#define UPDATE_FILE_EXTEND_NAME_LEN             256

#define DOWNLOAD_FIRST_FRAME_ERR                 2
#define DOWNLOAD_FRAME_ERR                       1
#define DOWNLOAD_FRAME_CORRECT                   0

#define BOTTOM_UPDATE_FILE_TRIG              1
#define BOTTOM_UPLOAD_FILE_TRIG              2         ///<  上传触发
#define BOTTOM_UPLOAD_FILE_LIST              3         ///<  上传文件列表
#define BOTTOM_UPLOAD_FILE_DATA              4         ///<  上传文件数据
#define BOTTOM_ZK_UPDATE_TRIG                5         ///<  字库升级触发帧
#define BOTTOM_UPDATE_DATA_FRM               6         ///<  升级数据帧

//升级或备份文件记录数据
#define    FILE_NAME_LEN    40
#define    FILE_TIME_LEN    20
#define    FLAG_IAP         0x55    //待升级
#define    FLAG_CAN_IAP     0x56    //待升级CAN
#define    FLAG_APP         0x66    //0xAA 已烧写    //升级结束
#define    FLAG_BACKUP      0x88    //运行正常待备份
#define    FLAG_OK          0x99    //已备份

#define BOTTOM_UPLOAD_DATA_FRAME_LEN             256
#define UPLOAD_FILE_NAME_MAX_LEN                 40       //文件名最大长度
#define UPLOAD_FILE_NUM                          20       //文件列表数
#define UPLOAD_FILE_TIME_LEN                     20       //上传文件时间长度，协议定的20

#define DOWNLOAD_FRAME_ERR                       1
#define DOWNLOAD_FRAME_CORRECT                   0

#define UPLOADING  1
#define NOTUPLOAD  0

#define DOWNLOAD_TRIG_TIMES                      3
#define TRIG_TIMEOUT                             30

#define BOTTOM_MAX_DOWNLOAD_FILE_NUM             5      // 底层通信协议支持的最大下载文件个数
#define BOTTOM_SAVE_UPDATE_DATA_INDEX            10

/**
 * 升级触发交互控制信息
 */
typedef struct {
    int       rtn;                                 ///<  返回码 00 正确  
    char                file_name[UPDATE_FILE_NAME_LEN];        ///<  对端下发的升级文件名称
} trig_ctr_inf_t;

typedef enum
{
    BOTTOM_NO_UPDATEING  = 0,
    BOTTOM_UPDATE_SUCCESS,
    BOTTOM_UPDATE_FAILURE,
    BOTTOM_UPDATEING,
    BOTTOM_DIFF_UPDATE_FAILURE,
    BOTTOM_TRANSFER_SUCCESS,
    BOTTOM_TRANSFER_FAILURE,
    BOTTOM_UPDATE_END,
}e_bottom_update_status;

typedef enum
{
    BOTTOM_MONITOR_FILE_TYPE = 0,
    BOTTOM_POWER_FILE_TYPE = 1,
    BOTTOM_DOWNLOAD_FILE_TYPE = 2,
}e_bottom_download_file_type;

typedef enum
{
    BOTTOM_EVENT_BEGIN_UPDATE = 0,
    BOTTOM_EVENT_UPDATE_SUCCESS,
    BOTTOM_EVENT_UPDATE_FAILURE,
    BOTTOM_EVENT_UPDATE_FINISH,
}e_bottom_update_result_save_event;

typedef struct 
{
    char  file_type;            // 0:监控升级文件，1:南向升级文件，2:仅下载文件
    char* file_name;            // 文件名
    char* store_name;           // 下载文件存储的名字：文件系统，flash, eeprom ，RAM
    unsigned int store_size;    // 下载文件存储的大小
}bottom_transfer_file_name_info_t;

typedef struct 
{
    char* update_info_part_name;    // 升级文件存储区
    unsigned int offset;    // 升级文件读/写偏移地址    
    void (*feed_dog)(void);  ///< 喂狗函数，无则置NULL
}bottom_update_info_save_t;

typedef struct
{
    char cur_file_type;             // 0:监控升级文件，1:南向升级文件，2:仅下载文件
    char* cur_file_name;            // 正在传输的文件名
    char* cur_store_file_name;      // 传输文件内容的分区名称
    char* cur_store_info_name;      // 传输文件内容的信息的分区名称，如：update_info
    unsigned int  cur_store_info_offset;           // 传输文件内容的信息的分区地址偏移
    void (*feed_dog)(void);
}bottom_cur_download_file_info_t;

#pragma pack(1)
typedef struct{
    unsigned char trig_success;       ///<  触发成功
    unsigned char trig_times;         ///<  触发次数
}bottom_download_trig_ctr_inf_t;
typedef struct
{
    unsigned short wListTotalFrame;  //列表总帧数
    unsigned char ucListTotalNum;    //列表总个数
    unsigned char aucListNameLen[UPLOAD_FILE_NAME_MAX_LEN];    //列表中的文件名长度
    char *pucListName[UPLOAD_FILE_NUM];               //列表中的文件名
}T_BottomFileListStruct;
typedef struct
{
    unsigned short wReqFileNameLen;
    char ucReqFileName[UPLOAD_FILE_NAME_MAX_LEN];
}T_BottomFileNameStruct;

typedef struct
{
    T_BottomFileNameStruct tFileName;
    T_BottomFileListStruct tFileList;
    unsigned short cur_frame_no;
    unsigned short wFileTotalFrame;
    unsigned int uFileTotalLen;
    unsigned char  rtn;
    unsigned char ucFileTime[UPLOAD_FILE_TIME_LEN];
    unsigned int uCrc; 
}T_BottomFileUploadStruct;

typedef struct
{
    unsigned short per_frame_len;                       // 单帧长度
    char   file_time[UPDATE_FILE_TIME_LEN];             ///< 下载时间
    char   file_name[UPDATE_FILE_NAME_LEN];           ///< 文件名称
    unsigned char   param_type;                                  ///< 参数类型
    unsigned int    total_leng;                                 ///< 文件总长度
    unsigned short  total_frames;                                ///< 总帧数量int
    unsigned int    filecrc;                                    ///< 文件CRC
    unsigned short  resv;
}bottom_update_file_attr_t;

typedef struct
{
    bottom_update_file_attr_t     file_info;
    unsigned char   sys_run_flag;
    unsigned char   backup_flag;
    unsigned char   update_flag;
    unsigned char   count;                                        ///控制boot侧升级逻辑的count
    unsigned char   rtn;  
    unsigned char   update_status;   
    unsigned int    data_offset;                                 ///<  文件存储偏移
    unsigned int    backup_frame_leng;                                
    unsigned short  cur_frame_no;
    unsigned short  restore_frame_num;
    //适配旧升级信息结构体添加的成员变量
    unsigned short  wBackupFrameNum;
    unsigned char   ucUpdateAddr;
    unsigned char   ucFlag;
    unsigned short  crc;
}bottom_update_file_manage_t;
#pragma pack()

void upload_manage_init(void);
int BeginDownload(unsigned char ucMode);
dev_type_t* init_acmu_bottom_comm(void);
dev_type_t* init_acmu_bottom_comm_can1(void);
int bottom_register_download_file_name(bottom_transfer_file_name_info_t* download_file_name, int download_file_num, bottom_update_info_save_t* update_info_save);
#ifdef __cplusplus
}
#endif

#endif  // _ACMU_UPDATE_MANAGE_H

