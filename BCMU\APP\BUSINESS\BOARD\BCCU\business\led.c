#include <rtthread.h>
#include <rtdevice.h>
#include <rtdef.h>
#include <math.h>
#include <string.h>
#include "utils_server.h"
#include "utils_thread.h"
#include "led.h"
#include "data_type.h"

static msg_map sample_msg_map[] =
{
     {0,NULL},
};

/* 定义信号灯对象句柄 */
Static led_t handle_led[CAN3_LED_TYPE] = {NULL,};

// 信号灯一个周期内的闪烁模式
Static led_mode_t led_blink_mode_table[] = {
    {CONSTANTLY_ON, "1000,0,"},
    {CONSTANTLY_OFF, "0,1000,"},
    {PERIOD_BLINK, "1000,500,"},
    {DISCONTINUOUS_BLINK, "5000,1000,"},
    {FAST_BLINK, "200,100,"}
};

Static led_ctrl_t led_crtl_table[] = {
    {RUN_LED_TYPE, LED_RUN_PIN, {CONSTANTLY_ON, "1000,0,"}, &handle_led[RUN_LED_TYPE]},
    {ALM_LED_TYPE, LED_ALM_PIN, {CONSTANTLY_ON, "1000,0,"}, &handle_led[ALM_LED_TYPE]},
    {CAN1_LED_TYPE, LED_CAN1_PIN, {CONSTANTLY_ON, "1000,0,"}, &handle_led[CAN1_LED_TYPE]},
    {CAN3_LED_TYPE, LED_CAN3_PIN, {CONSTANTLY_ON, "1000,0,"}, &handle_led[CAN3_LED_TYPE]},
};

Static void led_ctrl_on(void *param)
{
    rt_base_t led_pin = *(rt_base_t *)param;
    rt_pin_write(led_pin, PIN_LOW);
}

Static void led_ctrl_off(void *param)
{
    rt_base_t led_pin = *(rt_base_t *)param;
    rt_pin_write(led_pin, PIN_HIGH);
}


void* led_init_sys(void *param)
{
    unsigned char led_type_index = 0;
    for (led_type_index = 0; led_type_index <= CAN3_LED_TYPE; led_type_index++)
    {
        rt_pin_mode(led_crtl_table[led_type_index].led_pin, PIN_MODE_OUTPUT);
        led_crtl_table[led_type_index].led_handle = led_create(led_ctrl_on, led_ctrl_off, &(led_crtl_table[led_type_index].led_pin));
        RETURN_VAL_IF_FAIL(led_crtl_table[led_type_index].led_handle != NULL, NULL);
        led_set_mode(led_crtl_table[led_type_index].led_handle, LOOP_PERMANENT, led_crtl_table[led_type_index].led_mode.led_blink_state);
        led_start(led_crtl_table[led_type_index].led_handle);
    }

    server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(sample_msg_map) / sizeof(msg_map);
    register_server_msg_map(sample_msg_map, server_info);

    return NULL;
}

Static char led_status_switch(led_ctrl_t *led_ctrl, led_blink_stat_e switch_led_state)
{
    if (led_ctrl->led_mode.led_blink_state_index != switch_led_state)
    {
        led_ctrl->led_mode.led_blink_state_index = switch_led_state;
        led_ctrl->led_mode.led_blink_state = led_blink_mode_table[switch_led_state].led_blink_state;
        led_set_mode(led_ctrl->led_handle, LOOP_PERMANENT, led_ctrl->led_mode.led_blink_state);
        led_start(led_ctrl->led_handle);
    }

    return SUCCESSFUL;
}


void led_ctrl(void* parameter)
{
    while (is_running(TRUE))
    {
        rt_thread_delay(LED_THREAD_DELAY_TIME);

        led_status_switch(&led_crtl_table[RUN_LED_TYPE], DISCONTINUOUS_BLINK);
        led_status_switch(&led_crtl_table[ALM_LED_TYPE], FAST_BLINK);
        led_status_switch(&led_crtl_table[CAN1_LED_TYPE], CONSTANTLY_OFF);
        led_status_switch(&led_crtl_table[CAN3_LED_TYPE], PERIOD_BLINK);

        led_ticks();
    }
}