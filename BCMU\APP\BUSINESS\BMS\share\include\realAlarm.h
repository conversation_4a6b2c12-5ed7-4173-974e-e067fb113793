/**************************************************************************
* Copyright (C) 2001, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：ZXDU18 CTL板软件
* 文件名称：alarm.h
* 文件说明：告警模块头文件
* 作    者  ：   王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要
* 其他说明：
***************************************************************************/
#ifndef SOFTWARE_SRC_APP_REALALARM_H_
#define SOFTWARE_SRC_APP_REALALARM_H_
#include "alarm_in.h"
#include "battery.h"
#include "common.h"

#ifdef __cplusplus
extern "C" {
#endif

/***********************  常量定义  ************************/
#define ALARM_DELAY_COUNTER             30           // 开机启动，按照实时告警判断的延时
#define ALARM_COUNTER                   (7*3)        // 21   告警判断次数
#define CELL_VOL_LOW_PROTECT_COUNTER    255
#define COMMFAIL_COUNTER                10           // 通讯断判断次数
#define ALM_TASK_PERIOD                 360
#define ONE_MINUTE_COUNTER              (60000/ALM_TASK_PERIOD)
#define OVERCURR_RERTY_DELAY            (ONE_MINUTE_COUNTER - ALARM_COUNTER) //150// 充放电过流保护重试延时
#define SHORT_RERTY_DELAY               10           // 短路保护重试延时  功率要50S判断，监控对应判断时间缩短  应用D21 R321是否应用待确认
#define CELLLOWPRT_DELAY                1700         // 单体欠压保护延时1700*0.36s
#define CLEARSHAKEALM_DELAY             30           // 清除震动告警延时
#define ONE_MINUTE_CNT_ALARM_THREAD     167          // 告警线程执行一分钟的计数

//告警级别定义，yang_an,整理协议修改
#define EMERGENCY           1               //严重
#define Minor               2               //次要
#define IGNORE              0               //忽略

//告警量取值含义
//#define     NORMAL          0               /* 正常 */  // TODO: 与enum RT_CAN_STATUS_MODE冲突
#define     FAULT           1                 /* 异常 */
#define     COMMON_ALARM_NUM 1
#define     BCM_ALARM_NUM  (sizeof(T_BCMAlarmStruct))
#define     ALARM_NUM       BCM_ALARM_NUM
#define     RTN_ALM_STATUE  2

//#define HISALM_256_RECNUM  9
#define HISALM_NUM 16  ///16*9 = 144
#define REALALM_MAX_NUM 100
#define SHORT_CUT_ALM_COUNTER (21)
#define BATT_VOLT_SAMPLE_DEFAULT_DTECT     (0.3)
#define CELL_GROUP_NOT_FOUND 0
#define CELL_GROUP_VOLT_NUM_MAX 1
#define CELL_GROUP_TEMP_NUM_MAX 2
#define ALARM_FIVE_MIN (833)   // 0.36 * 833 = 299.88
#define BDUCOMMFAIL_ALM_COUNTER (42)

enum
{
    CURR_HIGH_BDPB = 1,
    CURR_HIGH_BDCU,
    CURR_HIGH_FAULT,
};

enum
{
    SHIELD_LVL = 0, // 屏蔽
    SERIOUS_LVL,    // 严重
    MINOR_LVL,      // 次要
};

enum
{
    DCR_NORMAL = 0, // 正常
    DCR_PROTECT,    // 保护
};

enum
{
    DEFENCE_CLEAR_BY_ALARMLVL = 0, // 修改告警等级清除布防
    DEFENCE_CLEAR_BY_SYSPARA,      // 修改防盗参数清除布防
    DEFENCE_CLEAR_BY_ENTERAPPTEST, // 进入APPTEST清除布防
    DEFENCE_CLEAR_BY_APPTEST,      // APPTEST清除布防
    DEFENCE_CLEAR_BY_EXITAPPTEST,  // 退出APPTEST清除布防
    DEFENCE_CLEAR_BY_ENTERQTP,     // 进入QTP清除布防
    DEFENCE_CLEAR_BY_QTP,          // QTP清除布防
    DEFENCE_CLEAR_BY_EXITQTP,      // 退出QTP清除布防
};

typedef struct
{
    FLOAT   fCurrMinDet;////电流检测误差，ODCU和BDCU不同
    FLOAT   fBattCurr;
    FLOAT   fCurrCoef;
    FLOAT   afCellTemp[CELL_TEMP_NUM_MAX];
    FLOAT   afCellVolt[CELL_VOL_NUM_MAX];
    BOOLEAN bBuzze;
    BOOLEAN bLoopOff;
    WORD    wCurrLimitInvalidCnt;
    WORD    wDischgCurrHighPrtCnt;
    WORD    wChgCurrHighPrtCnt;
    WORD    wBattShortCutCnt;
    WORD    wBduBusVoltLowPrtCnt;

    WORD    wDischgCurrHighPrtCntMax;
    WORD    wBattShortCutCntMax;
    WORD    wBduBusVoltLowPrtCntMax;
    WORD    wCurrLimitInvalidCntMax;
    WORD    wChgCurrHighPrtCntMax;
    BYTE	ucBDUBattLockAlm; //  BDU电池闭锁告警
    BYTE    ucBDUConnTempHighPrt;  //   连接器温度高保护
    BYTE	ucHeaterFilmFailure; //  加热膜失效
    BYTE    ucBalanceResisTempHighPrt;  // 均衡电阻温度高保护
    FLOAT   fBattVolt;
    FLOAT   fCellVoltMin;
    FLOAT   fCellVoltMax;
    BOOLEAN bDcrFaultAlm;
    BOOLEAN bDcrFaultPrt;
}T_AlmDataStruct;

typedef struct
{
    BOOLEAN bBuzzEnable;
}T_AlmParaStruct;

/************************************************************
** 结构名: T_AlmInfoStruct
** 描  述: 告警信息结构体作为告警判断的输入
** 作  者: 王威
** 日  期: 2011-05-13
** 版  本: V2.1
** 修改记录         
** 日  期       版  本      修改人      修改摘要
** 
**************************************************************/
typedef struct T_AlmInfoTmp
{
    BYTE *pucRealAlarm;          //告警当前状态
    BYTE *pucCounter;    //
    BYTE *pucCounterThreshold;    //判断次数
    BYTE  ucAlarmType;      //告警值类型
  
    //模拟量部分
    FLOAT fValueAdd;      //回差
    FLOAT fThread;         //阈值
    FLOAT fValue;          //传入的模拟量值
    INT32S (*pExceptionHandling)(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus);     //特殊处理函数
    
    //数字量部分
    BYTE  ucState;            //数字量状态
    BYTE *pucStateAlarm;   //告警状态
    BYTE  ucReserve;        //保持其他必要信息
    __packed WORD* pwRetryCounter;            ///计数数据指针
    __packed WORD* pwRetryCounterMax;    ////计数最大值
}T_AlmInfoStruct;

/************************************************************
** 结构名: T_DisRealAlarmStruct
** 描  述: 实时告警显示数据结构
** 作  者: 严宏明
** 日  期: 2004-08-07
** 版  本: V4.0 
** 修改记录         
** 日  期       版  本      修改人      修改摘要
** 
**************************************************************/
typedef struct
{
    BYTE     ucState;        //告警值
    time_t   tTime;          //告警产生时间
    BYTE  ucInnerFlag;      //yl内部告警标志0: 表示外部告警  1: 表示内部告警
}T_DisRealAlarmStruct;

typedef struct
{
     WORD        wAlarmID;      //告警ID号
     BYTE        ucInnerFlag;
     time_t   tTime;          //告警产生时间
}T_RealAlarmStruct; // 用户保存

typedef struct
{
    T_RealAlarmStruct atRealAlm[REALALM_MAX_NUM];
    BYTE ucRealAlmLen;
    WORD wCrc;
}T_RealAlm256SaveStruct;

typedef struct
{
	WORD        wDefenceStatus;   //布防状态 0:未布防 1:已布防
    WORD           wCheckSum;  
}T_DefenceInfoStruct;

typedef struct
{
    BOOLEAN bIfShortCutExist;
    WORD wCrc;
} T_AlarmCheckStruct;

typedef struct
{
    BOOLEAN bSiteAntiTheftStat;                       // 站点防盗布防状态，0为未布防，1为已布防
    BYTE aucSiteAntiTheftKey[SITE_ANTITHEFT_KEY_LEN]; // 加密SN(32字节)
    WORD wSiteAntiTheftDelay;                         // 站点防盗延时
    WORD wCrc;                                        // 存储计算CRC
} T_SiteAntiTheftStruct;

typedef struct
{
    BOOLEAN bNetAntiTheftStat;     // 网管防盗布防状态，0为未布防，1为已布防
    BYTE aucNetAntiTheftKey[NETANTITHEFT_KEY_LEN];   // 网管防盗电子钥匙
    BYTE aucNetAntiTheftSN[NETANTITHEFT_SN_LEN];   // 网管防盗SN信息
    WORD wCrc;                     // 存储计算CRC
} T_NetAntiTheftStruct;

typedef struct
{
    BOOLEAN bFilled;
    BOOLEAN bReceived;
    CHAR acBmsSn[6];
} T_BindingInfoStruct;

typedef struct
{
    T_BindingInfoStruct atBindingInfo[SLAVE_NUM]; // 绑定防盗时的并机信息
    BOOLEAN bSaveInfoFinish; // 保存绑定防盗并机信息状态，0为未完成保存，1为已完成保存
    WORD wCrc;               // 存储计算CRC
} T_SaveBindingInfoStruct;

/*********************  函数原型定义  **********************/
void JudgeAlarm( void* parameter );
WORD CalRealAlarmTotal( BYTE ucAlarmType, BYTE *pucOutCommonAlm);
BYTE GetDataFlag( BYTE ucDataFlagID, BYTE ucCommPort );
void SetDataFlag( BYTE ucDataFlagID, BYTE ucValue );
void ClearDataFlag( BYTE ucDataFlagID, BYTE ucValue, BYTE ucCommPort );
void GetRealAlarm( BYTE ucUnitID, BYTE *pDest );
void GetBcmRealAlarm( T_BCMAlarmStruct *pDest );
void SetCtrlOutRelay( void );
void TransBcmAlarm( T_BCMAlarmStruct* tBcmAlarm );
BYTE JudgeAlarmType( T_BattResult const* pBattOut );
BOOLEAN IfSysInvalid(void);
void setShakeAlm(BOOLEAN bSet);
void ClearBattTheftAlm(void);
BOOLEAN SetGprsAutoUpStatus(BOOLEAN bStatus);
BOOLEAN GetGprsAutoUpStatus(void);
void GprsSetBattTheftAlm(void);
void GprsClearBattTheftAlm(void);
void readBmsRealAlarm(T_RealAlm256SaveStruct* ptRealAlmSave);
void SaveRealAlarm(T_RealAlm256SaveStruct* ptRealAlmSave);
void writePackManufact(T_BmsPACKManufactStruct* ptBmsPACKManufact);
void ClearCellDamagePrt(void);
void CheckCellDamegeFlag(void);
WORD GetDefenceStatus(void);
SHORT DealDeviceUnlock(BYTE ucMode);
BOOLEAN ClearDcrFaultPrt(void);
BOOLEAN ClearDcrFaultAlm(void);
BOOLEAN JudgeWireAntiTheft(void);
WORD ClearBattTheftAlmCnt(void);
BOOLEAN IsBattLock(void);
BOOLEAN GetExistAlarmFlag(VOID);
FLOAT GetBattVoltThresh(void);
BOOLEAN GetICRestartFlag(void);
void SetICRestartFlag(BOOLEAN Flag);
BOOLEAN WriteDefenceStatus(BOOLEAN bDefenceStatus);
WORD IsDefenseCondition();
BOOLEAN IsExistBalanceResisTempHighPrt(void);
BOOLEAN SetBDUConnTempHighRecoverFlag(BOOLEAN bConnTempHighRecoverFlag);
BOOLEAN GetBDUConnTempHighRecoverFlag(VOID);
BOOLEAN GetSiteAntiTheftStatus(void);
BOOLEAN DealSiteAntiTheftStart(BYTE *pucSiteAntiTheftKey);
BOOLEAN DealSiteAntiTheftHeartbeat(BYTE *pucSiteAntiTheftKey);
BOOLEAN DealSiteAntiTheftEnd(BYTE *pucSiteAntiTheftKey);
BOOLEAN DealSiteAntiTheftChangeKey(BYTE *pucSiteAntiTheftKey, BYTE *pucSiteAntiTheftKeyNew);
BOOLEAN ClearDefenceStatus(BYTE ucFlag);
BOOLEAN IsExistLostAlarm(T_BCMAlarmStruct *ptBcmAlarm);
BOOLEAN GetNetAntiTheftStatus(void);
BYTE    DealNetAntiTheftStart(BYTE *pucNetAntiTheftKey);
BYTE    DealNetAntiTheftHeartbeat(BYTE *pucNetAntiTheftKey, BYTE *pucNetAntiTheftSN);
BYTE    DealNetAntiTheftEnd(BYTE *pucNetAntiTheftKey);
rt_int32_t JudgeNetAntiTheftKey(BYTE *pucNetAntiTheftKey);
BYTE WriteNetAntiTheftInfo(BOOLEAN stat, BYTE *pucNetAntiTheftKey);
size_t WriteNetAntiTheftSN(BYTE *pucNetAntiTheftSN);
#ifdef POWER_COMM_FAIL_ALARM
BOOLEAN ResetPowerCommFalg(BOOLEAN bFlag);
BOOLEAN SetPowerCommShieldFlag(BOOLEAN bFlag);
#endif
#ifdef PAKISTAN_CMPAK_PROTOCOL
BOOLEAN GetOverLoadLockStatus(void);
#endif

void ClearCANCommLostCnt(void) UNUSED;

#ifdef UNITEST
    void alarmToClass(BYTE *pucClass);
void fillAlarmShield( void );
#endif

#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // SOFTWARE_SRC_APP_REALALARM_H_;
