#ifndef DCMU_ALARM_REGISTER_H_  
#define DCMU_ALARM_REGISTER_H_  
#ifdef __cplusplus
extern "C" {
#endif

#define FULL_SCALE_CURRENT 2000
#define ALARM_NOT_EXSIT 0
#define ALARM_EXSIT     1

#define TEMPERATURE_MAX     100
#define TEMPERATURE_MIN     -40

#define HUMIDITY_MAX     95
#define HUMIDITY_MIN     10

#define ALARM_HIGH_LIMIT    0   //高限判断模式
#define ALARM_LOW_LIMIT     1   //低限判断模式

#define INPUT_RELAY_NUM 4

short register_dcmu_alarm(void);
char abnormal_battery_current_judge(int alm_id);
char battery_group_config_judge(int alm_id);
char battery_loop_broken_judge(int alm_id);
char battery_discharge_judge(int alm_id);
char abnormal_load_config_judge(int alm_id);
char invalid_battery_temperature_judge(int alm_id);
char env_humidity_invalid_judge(int alm_id);
char env_temp_invalid_judge(int alm_id);
char temperature_sensor_alarm_judge(void);
char humidity_sensor_alarm_judge(void);
char dcmu_input_relay_status_judge(int alm_id);
char battery_voltage_low_judge(int alm_id);
char battery_voltage_too_low_judge(int alm_id);
char battery_temp_high_judge(int alm_id);
char battery_temp_low_judge(int alm_id);
char battery_temp_too_high_judge(int alm_id);
char total_alarm_judge(int alm_id);
char env_humidity_high_alarm_judge(int alm_id);
char env_humidity_low_alarm_judge(int alm_id);
char env_temp_alarm_judge(int alm_id);
char sensor_alarm_judge(int alm_id);
char check_alarm(float current_value, float threshold, 
                      float hysteresis, char mode, 
                      char current_state);

#ifdef __cplusplus
}  
#endif

#endif
