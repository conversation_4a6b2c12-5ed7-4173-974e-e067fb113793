#ifndef _DEV_NORTH_DEUB_MODBUS_H
#define _DEV_NORTH_DEUB_MODBUS_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "device_type.h"
#include "cmd.h"
#include "linklayer.h"
#include "protocol_layer.h"
#include "protocol_north_modbus.h"

#define R_DATA_LEN_NO_USE    0
#define S_DATA_LEN_NO_USE    0

#define NO_MATCH_REGISTER_ADDR -1
#define DATA_LEN_2          2//数据长度为2
#define DATA_LEN_4          4 //数据长度为4
#define DATA_LEN_6          6 //数据长度为6

typedef enum {
    TYPE_ALAM = 0,
    TYPE_PARA,
    TYPE_ANA,
    TYPE_ALAM_PARA
}sid_type_e;


typedef struct {
    unsigned short      register_addr;                          // 寄存器地址
    unsigned char       reserve_flag;                           // 该寄存器值是否保留，1 是保留
    char                old_type;                               // 原始数据类型
    char                type;                                   // Modbus 传输数据类型
    unsigned char       precision;                              // Modbus 精度
    unsigned char       data_len;                               // Modbus 上送数据长度
    sid_type_e          sid_type;                               // SID 的类型
    unsigned int        data_addr;                              // 数据取值地址
} modbus_addr_map_data_t;

typedef struct {
    char src_type;
    char dst_type;
    int (*pack_fun)(int* index, unsigned char* cmd_buff, int* reg_num, int data_len, int percision, int total_len, modbus_addr_map_data_t* data_map);
} pack_fun_map_t;

typedef struct {
    char src_type;
    char dst_type;
    int (*parse_func)(int* index, unsigned char* cmd_buff, int* data_value_index, int percision, int* reg_nums, modbus_addr_map_data_t* data_map);
} parse_fun_map_t;


dev_type_t* init_dev_north_deub(void);

/**************************************打包函数*****************/
int floattoint32u(int* index , unsigned char* data_buff , int* reg_num , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map);
int floattoint32s(int* index , unsigned char* data_buff , int* reg_num , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map);
int floattoint16u(int* index , unsigned char* data_buff , int* reg_num , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map);
int floattoint16s(int* index , unsigned char* data_buff , int* reg_num , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map);
int int16utoint16u(int* index , unsigned char* data_buff, int* reg_num , int data_len , int percision , int total_len, modbus_addr_map_data_t* data_map);
int int16stoint16s(int* index , unsigned char* data_buff, int* reg_num , int data_len , int percision , int total_len, modbus_addr_map_data_t* data_map);
int int32utoint32u(int* index , unsigned char* data_buff, int* reg_num , int data_len , int percision , int total_len, modbus_addr_map_data_t* data_map);
int int32stoint32s(int* index , unsigned char* data_buff , int* reg_nums, int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map );
int stringtostring(int* index , unsigned char* data_buff, int* reg_num , int data_len , int percision , int total_len, modbus_addr_map_data_t* data_map);
int datetoint16u(int* index , unsigned char* data_buff, int* reg_num , int data_len , int percision , int total_len, modbus_addr_map_data_t* data_map);
int chartobit(int* index , unsigned char* data_buff , int* reg_num , int data_len , int percision , int total_len, modbus_addr_map_data_t* data_map);

/*******************************解包函数*****************/
int parse_int16utofloat( int* index , unsigned char* data_buff , int* data_valude_index ,int percision , int* reg_nums, modbus_addr_map_data_t* data_map);
int parse_int16stofloat( int* index , unsigned char* data_buff , int* data_valude_index ,int percision , int* reg_nums, modbus_addr_map_data_t* data_map );
int parse_int32utofloat( int* index , unsigned char* data_buff , int* data_valude_index , int percision, int* reg_nums, modbus_addr_map_data_t* data_map);
int parse_int32stofloat( int* index , unsigned char* data_buff , int* data_valude_index ,int percision, int* reg_nums, modbus_addr_map_data_t* data_map);
int parse_int16utoint16u( int* index , unsigned char* data_buff , int* data_valude_index ,int percision , int* reg_nums, modbus_addr_map_data_t* data_map);
int parse_int32utoint32u( int* index , unsigned char* data_buff , int* data_valude_index,int percision, int* reg_nums, modbus_addr_map_data_t* data_map);
int parse_stringtostring( int* index , unsigned char* data_buff , int* data_valude_index ,int percision, int* reg_nums, modbus_addr_map_data_t* data_map);

int new_pack_data_to_buff(void* cmd_buff , int* index, int offset_value, modbus_addr_map_data_t* data_map);
int new_pack_ana_para_data(void* dev_inst, void* cmd_buff);
int new_parse_para_data_from_buff(void* dev_inst, void* cmd_buff);
int pack_set_para_data(void* dev_inst, void* cmd_buff);
int parse_start_addr_and_reg_nums(void* dev_inst, void* cmd_buf);
void check_ana_reserve_flag(int index , void* data,modbus_addr_map_data_t* data_map);
void get_data_by_id_type(int index , void* data, modbus_addr_map_data_t* data_map);
char set_data_by_id_type(int index , void* data, modbus_addr_map_data_t* data_map);
int find_register_start_addr_index(int reg_addr);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  // 
