#ifndef _APP_UPLOAD_MANAGE_H
#define _APP_UPLOAD_MANAGE_H

#ifdef __cplusplus
extern "C" {
#endif

#include "storage.h"
#include "device_type.h"
#include "net.h"
#include "utils_uuid.h"

#define UPLOAD_DATA_FRAME_LEN                    1024
#define UPLOAD_FILE_NAME_MAX_LEN                 40       //文件名最大长度
#define UPLOAD_FILE_NUM                          20        //文件列表数
#define UPLOAD_FILE_TIME_LEN                     20       //上传文件时间长度，协议定的20

#define DOWNLOAD_FRAME_ERR                       1
#define DOWNLOAD_FRAME_CORRECT                   0

#define DOWNLOAD_TRIG_TIMES                      3
#define TRIG_TIMEOUT                             30
#pragma pack(1)
typedef struct{
    unsigned char trig_success;       ///<  触发成功
    unsigned char trig_times;         ///<  触发次数
}download_trig_ctr_inf_t;
typedef struct
{
    unsigned short wListTotalFrame;  //列表总帧数
    unsigned char ucListTotalNum;    //列表总个数
    unsigned char aucListNameLen[UPLOAD_FILE_NAME_MAX_LEN];    //列表中的文件名长度
    char *pucListName[UPLOAD_FILE_NUM];               //列表中的文件名
}T_FileListStruct;
typedef struct
{
    unsigned short wReqFileNameLen;
    char ucReqFileName[UPLOAD_FILE_NAME_MAX_LEN];
}T_FileNameStruct;

typedef struct
{
    T_FileNameStruct tFileName;
    T_FileListStruct tFileList;
    unsigned short cur_frame_no;
    unsigned short wFileTotalFrame;
    unsigned int uFileTotalLen;
    unsigned char  rtn;
    unsigned char ucFileTime[UPLOAD_FILE_TIME_LEN];
    unsigned int uCrc; 
}T_FileUploadStruct;
#pragma pack()
void upload_manage_init(void);
#ifdef __cplusplus
}
#endif

#endif 
