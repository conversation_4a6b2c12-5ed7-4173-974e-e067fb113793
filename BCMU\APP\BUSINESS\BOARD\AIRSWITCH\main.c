#include <stdio.h>
#include <rtthread.h>
#include "utils_thread.h"
#include "msg.h"
#include "utils_data_transmission.h"
#include "utils_time.h"
#include "north_main.h"
#include "sample.h"
#include "led.h"
#include "utils_server.h"
#include "server_id.h"
#include "storage.h"
#include "partition_table.h"
#include "update_manage.h"
#include "main.h"
#include "realdata_save.h"
#include "airswitch_alarm.h"
#include "control_manage.h"
#include "wearLeve_eeprom.h"
#include "wtd_ctrl.h"
#include "eeprom_storage.h"

static char s_north_thread_stack[NORTH_THREAD_STACK_SIZE] RAM_SECTION_BSS;
static char s_signal_led_thread_stack[LED_THREAD_STACK_SIZE] RAM_SECTION_BSS;
static char s_sample_thread_stack[SAMPLE_THREAD_STACK_SIZE] RAM_SECTION_BSS;
static char s_alarm_thread_stack[ALARM_THREAD_STACK_SIZE] RAM_SECTION_BSS;
static char s_ctrl_manage_thread_stack[CONTROL_THREAD_STACK_SIZE] RAM_SECTION_BSS;

static PartitionInfo_TypeDef PartitionInfoTable[] =
{
    {"elecpara", 64, 7*64}, // "elecpara"分成7个64小区域磨损
    {"alarm", 64, 7*64}, // "alarm"分成7个64小区域磨损
    {"strpara", 128, 7*128}, // "strpara"分成7个128小区域磨损
};

reset_reason_t get_reset_reason(void)
{
    uint32_t reset_flags = RCU_RSTSCK;
    reset_reason_t reason = RESET_REASON_NONE;

    /* 解析复位标志,最终按复位原因优先级返回 */
    if (reset_flags & RCU_RSTSCK_PORRSTF)
    {
        reason = RESET_REASON_POWER_ON;
    }

    if (reset_flags & RCU_RSTSCK_SWRSTF)
    {
        reason = RESET_REASON_SOFTWARE;
    }

    if (reset_flags & RCU_RSTSCK_FWDGTRSTF)
    {
        reason = RESET_REASON_FWDGT;
    }

    /* 清除所有复位标志（写1清除） */
    RCU_RSTSCK |= (RCU_RSTSCK_PORRSTF | RCU_RSTSCK_EPRSTF | 
                   RCU_RSTSCK_SWRSTF | RCU_RSTSCK_FWDGTRSTF | 
                   RCU_RSTSCK_WWDGTRSTF | RCU_RSTSCK_LPRSTF);

    return reason;
}

static server_info_t g_server_group[] = {
    {{{SAMPLE_SERVER_ID,   "sample",  sizeof(s_sample_thread_stack),  s_sample_thread_stack,  SERVER_PRIO_HIG}}, sample_init_sys, sample_main},
    {{{NORTH_SERVER_ID,   "north",  sizeof(s_north_thread_stack),  s_north_thread_stack,  SERVER_PRIO_LOW}}, init_north, north_comm_th},
    {{{ALARM_MANAGE_SERVER_ID,   "alarm",  sizeof(s_alarm_thread_stack),  s_alarm_thread_stack,  SERVER_PRIO_MID}}, init_alarm_manage, alarm_main},
    {{{CONTROL_MANAGE_ID, "control_manage",  sizeof(s_ctrl_manage_thread_stack),  s_ctrl_manage_thread_stack, SERVER_PRIO_MID}}, init_control_manage, control_manage_main},
    {{{LED_SERVER_ID,   "signal_led",  sizeof(s_signal_led_thread_stack),  s_signal_led_thread_stack,  SERVER_PRIO_LOW}}, init_led_blink, led_thread_entry},
};

int main(void){
    watchdog_init();
    init_flash_page_size(FLASH_PAGE_SIZE);
    init_crc();
    init_real_data_memory();
    register_product_para();
    if (SUCCESSFUL != storage_init(&part_tab[0], sizeof(part_tab)/sizeof(part_info_t))) {
        return FAILURE;
    }
    if(RT_EOK != WearLeverEeprom_init(PartitionInfoTable, sizeof(PartitionInfoTable)/sizeof(PartitionInfo_TypeDef))){
        return FAILURE;
    }

    init_para_manage();
    init_read_eeprom_alarm();
    init_link_tab();
    hal_board_gpio_init();
    update_manage_init();
    init_device_para();
    create_server(&g_server_group[0], sizeof(g_server_group)/sizeof(g_server_group[0]));
    rt_thread_mdelay(3000);
    begin_download(FLAG_BACKUP);
    return SUCCESSFUL;
}
