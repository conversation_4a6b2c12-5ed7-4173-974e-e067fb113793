#ifndef BATTDISCHARGE_PRODUCT_H_
#define BATTDISCHARGE_PRODUCT_H_

#include "battery.h"

#define VOLT_REC_TIMES 10

void JudgeDisChargeThrough(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
void UpdateMixPowerOnVolWhenDischg(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
void ClearVoltRecord(void);

#ifdef INTELLIGENT_PEAK_SHIFTING
BOOLEAN JudgePeakShiftDischarge(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal, T_PeakShiftPara *pPeakShift);
BOOLEAN JudgeFmDischgSwit(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal); //调频放电控制
BOOLEAN PeakShiftSlaveDisableDischg(T_BattInfo *ptBattIn,T_BattDealInfoStruct *pBattDeal,T_PeakShiftPara *ptPeakShift);
#endif

#endif