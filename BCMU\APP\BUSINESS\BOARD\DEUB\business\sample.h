/**************************************************************************
* Copyright (C) 2001, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：E260 DXCB板软件
* 文件名称：sample.h
* 文件说明：数据采集模块头文件
* 作    者：hlb
* 版本信息：V1.0
* 设计日期：2024-01-17
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/
#ifndef DEUB_MAIN_SAMPLE_H_
#define DEUB_MAIN_SAMPLE_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "pin_define.h"
// #include <rtdevice.h>
// #include <spi.h>
// #include <math.h>

/***********************  常量定义  ************************/
//#define ADCCONVERTEDVALUES_CHANNEL_NUM   9   //ADC采样通道数
#define ADCCONVERTEDVALUES_SAMPLING_NUM  16   //ADC采样次数
//#define ADCCONVERTEDVALUES_ROAD_NUM      8  //单路通道采样路数
#define AC_VOLTAGE_CHANNEL_NUM   3            //AC采样通道数
#define    AC_SAMPLE_TIMES   128                 //AC采样次数
#define mVOLT_PER_VOLT    1000

#define DIGITAL_COUNT        3
#define ANALOG_COUNT         10
#define ADC_DEV_NAME        "adc0"
#define REFER_VOLTAGE        3        /* VOLTAGE  is 3V */
#define ISO_SWITCH_NUM       3

//交流频率计算
#define SAMPLE_DEFAULT_FREQ_50    50.0
#define SAMPLE_DEFAULT_FREQ_60    60.0
#define SAMPLE_Min_FREQ           30.0
#define VALID_FREQ_MIN            35.0
#define VALID_FREQ_MAX            80.0
#define STABLE_THR_STABLE_FREQ    0.3
#define FREQ_CALC_UNSTABLE_TIMES  3
#define FREQ_CALC_NUM             16
#define MIN_VALID_AC_VOLT         150
#define MAX_VALID_AC_VOLT         300

//DI采样
#define DI_SAMPLE_STABLE_TIMES    3
#define AC_DI_CONVERT_MAX_TIMES   3
#define AC_DI_CONVERT_MIN_TIMES   1

//定时器
#define HWTIMER_DEV_NAME     "timer5"


// 阻抗计算
#define ISO_DIFF_VOL    3.0     // 电压变化小于3V才计算阻抗
#define ISO_RES_MAX     1000

#define UNBALANCE_WAIT_TIMES   0  // 大概是3s

// 电池电流校准
#define BATT_CUR_FULL_SCALE      ((2.76-0.24)/2)   //满量程ADC采样电压值
#define ZERO_DRIFT_THRESHOLD     fabs((BATT_CUR_FULL_SCALE / 50.4 * mVOLT_PER_VOLT) * 100)


/*********************  数据结构定义  **********************/

typedef enum 
{
    AD0 = 0,
    AD1,
    AD2,
    V_ISO,
    V_OUT,
    I_BAT1,
    I_BAT2,
    I_BAT3,
    I_BAT4,
    AI_END,
}ADC_SAMPLE_CHANNEL;

typedef enum{
    DI1 = 0,
    DI2,
    DI3,
    DI_END,
} eDI_SAMPLE;

enum{
    B_FREQ_CALC_NORMAL = 0,
    B_FREQ_CANNOT_CALC,
    B_FREQ_CALC_ABNORMAL,
};

enum{
    b_FREQ_SM_STATUS_CALC = 0,
    b_FREQ_SM_STATUS_STABLE,
};

enum{
    DATA_PRECISION_ZERO = 0,
    DATA_PRECISION_ONE,
    DATA_PRECISION_TWO,
    DATA_PRECISION_THREE,
};

enum
{
    UNBALANCE_NONE = 0,                  // 初始化
    UNBALANCE_D1CLOSE_D2OPEN_D3CLOSE,
    UNBALANCE_D1CLOSE_D2CLOSE_D3OPEN,
    UNBALANCE_D1CLOSE_D2CLOSE_D3CLOSE,
    UNBALANCE_ISNCAL,
};

enum
{
    BALANCE_NONE = 0,                  // 初始化
    BALANCE_GET_VOL,
    BALANCE_ISNCAL,
};


enum
{
    ISO_TYPE_BALANCE = 0,    // 平衡桥
    ISO_TYPE_UNBALANCE,      // 非平衡桥
};

typedef struct 
{
    ADC_SAMPLE_CHANNEL    sample_channel;
    int                   select_status;
    int                   adc_channel;
    int                   sid;
}ADC_CHANNEL_INFO_STRUCT;

typedef struct 
{
    eDI_SAMPLE    sample_channel;
    int           select_status;
    int           di_pin;
}DI_CHANNEL_INFO_STRUCT;

typedef struct
{
    rt_uint32_t    ucCurrTimes;                        // 采样次数
    rt_uint32_t    ucChannelID;                        // 采样通道
    rt_int32_t    ucVoltageData[AC_VOLTAGE_CHANNEL_NUM][AC_SAMPLE_TIMES];
    rt_uint8_t  ucDIData[DI_END][AC_SAMPLE_TIMES];
    float AcVoltageValue[AC_VOLTAGE_CHANNEL_NUM];
}T_AcSampleStruct;

typedef struct
{
    int iso_switch;
    float s_fV_step1_neg;
    float s_fV_step1_pos;
    float s_fV_step2_neg;
    float s_fV_step2_pos;
    float s_fV_step3_neg;
    float s_fV_step3_pos;
}unbalance_info_t;

typedef struct
{
    int iso_switch;
    float s_fV_step1_neg;
    float s_fV_step1_pos;
}balance_info_t;

/*********************  函数原型定义  **********************/
void *sample_init_sys(void * param);
void sample_main(void * parameter);
int set_iso_switch_status(unsigned int index, unsigned int status);
unsigned int get_iso_switch_status(unsigned int index);
int judge_iso_vol_stable(float f1, float f2);
int cal_iso_value(balance_info_t* balance_info, unbalance_info_t* unbalance_info);
int set_iso_stable_vol();

#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // E260_DXCB_MAIN_SAMPLE_H_;
