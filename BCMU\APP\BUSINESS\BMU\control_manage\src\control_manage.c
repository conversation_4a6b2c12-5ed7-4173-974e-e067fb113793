/*******************************************************************************
  * @file        bmu_control_manage.c
  * @version     v1.0.0
  * @copyright   COPYRIGHT &copy; 2023 CSG
  * <AUTHOR>
  * @date        2023-4-11
  * @brief
  * @attention
  * Modification History
  * DATE         DESCRIPTION
  * ------------------------
  * - 2023-4-11  dongmengling Created
*******************************************************************************/

#include <string.h>
#include <rtthread.h>
#include <rtdevice.h>
#include <stdlib.h>
#include "utils_thread.h"
#include "control_manage.h"
#include "msg.h"
#include "msg_id.h"
#include "data_type.h"
#include "do_ctrl.h"
#include "device_type.h"

#define CTRL_INTERVAL_NORMAL  10    // unit:ms
#define CTRL_MSG_SEM_TIMEOUT  10    // unit OS tick

static _rt_server_t s_curr_server = NULL;
static short process_recv_msg(void);
static short handle_balance_fault_detect_msg(_rt_msg_t curr_msg);

static msg_map s_ctrl_msg_map[] = {
    {BALANCE_FAULT_DETECTED_MSG, msg_handle_nothing},
};
static control_msg_handle_process_t s_ctrl_msg_handle[] = {
    {BALANCE_FAULT_DETECTED_MSG, handle_balance_fault_detect_msg},
};

void* init_ctrl_manage(void* param) {
    server_info_t *server_info = (server_info_t *)param;
    RETURN_VAL_IF_FAIL(server_info != NULL, NULL);
    server_info->server.server.map_size = sizeof(s_ctrl_msg_map) / sizeof(msg_map);
    register_server_msg_map(s_ctrl_msg_map, server_info);
    return server_info;
}

void ctrl_manage_main(void *param) {
    rt_int32_t time_interval = 10; // unit:ms
    s_curr_server = _curr_server_get();

    while (is_running(TRUE)) {
        process_recv_msg();
        rt_thread_mdelay(time_interval);
    }
}

/* 消息接收处理 */
static short process_recv_msg(void) {
    unsigned short i = 0;
    _rt_msg_t curr_msg = NULL;

    // 消息接收
    if ((NULL == s_curr_server) || (NULL == s_curr_server->msg_node) || (rt_sem_take(&s_curr_server->msg_sem, CTRL_MSG_SEM_TIMEOUT) != RT_EOK)) {
        rt_thread_mdelay(CTRL_INTERVAL_NORMAL);
        return FAILURE;
    }

    // 消息处理
    curr_msg = s_curr_server->msg_node;
    for (i = 0; i < sizeof(s_ctrl_msg_handle) / sizeof(s_ctrl_msg_handle[0]); i++) {
        if (s_ctrl_msg_handle[i].msg_id == curr_msg->msg.msg_id) {
            s_ctrl_msg_handle[i].handle(curr_msg);
        }
    }

    // 服务消息节点处理
    rt_mutex_take(&s_curr_server->mutex, RT_WAITING_FOREVER);
    s_curr_server->msg_node = curr_msg->next;
    s_curr_server->msg_count--;
    rt_mutex_release(&s_curr_server->mutex);

    RT_KERNEL_FREE(curr_msg);
    return SUCCESSFUL;
}

/* 启动均衡回路故障检测 */
static short handle_balance_fault_detect_msg(_rt_msg_t curr_msg) {
    start_balance_fault_detected();
    return SUCCESSFUL;
}
