#include "time_sync.h"
#include "rtthread.h"
#include "time.h"
static long long last_sync_time = 0;
char need_resync = FALSE;
static void resync_timeout(void* parameter);
rt_timer_t resync_timer;

/**
 * @brief 主动时间同步
 * @param[in] set_state   从采样设置告警状态函数
 * @param[in] get_state   从采样获取告警状态函数
 * @param[in] para        同步参数
 * @retval
 * @note 启动或定时时间到BMU从BCMU获取同步时间
         或 BCMU从BCMU获取同步时间
 */
void positive_time_sync(set_t_sync_alm_f set_state, get_t_sync_alm_f get_state, time_sync_para_t* para) {
    static unsigned int sync_num = 0;
    static unsigned int count = 0;
    static char started = FALSE;
    char alm_value = FALSE;
    RETURN_IF_FAIL(set_state != NULL && get_state != NULL && para != NULL);

    if (FALSE == started) {
        resync_timer = rt_timer_create("time_resyc", resync_timeout, NULL, para->resync_interval, RT_TIMER_FLAG_PERIODIC);
        rt_timer_start(resync_timer);
        started = TRUE;
    }

    if (TRUE == need_resync) {
        *(para->sync_state) = FALSE;
        set_state(FALSE);
        sync_num = 0;
        count = 0;
        need_resync = FALSE;
    }

    alm_value = get_state();
    if (FALSE == *(para->sync_state)) {
        if (0 != count % (para->check_period / para->north_delay)) {
            count++;
            return;
        }

        if (TRUE == alm_value) {
            return;
        }

        count++;

        if (sync_num >= para->max_sync_num) {
            sync_num = 0;
            count = 0;
            set_state(TRUE);
            send_msg(&para->alm_msg);
            return;
        }
        // send_dest_cmd(para->dev_inst, para->sync_cmd_id);  // iar test error
        sync_num++;
    } else {
        if (FALSE == alm_value) {
            return;
        }
        sync_num = 0;
        count = 0;
        set_state(FALSE);
        send_msg(&para->alm_msg);
    }
}

/* 定周期时间同步 定时器超时函数 */
static void resync_timeout(void* parameter) {
    last_sync_time = time(NULL);
    need_resync = TRUE;
}
