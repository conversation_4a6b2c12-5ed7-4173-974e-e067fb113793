#ifndef _4G_INTERFACE_H
#define _4G_INTERFACE_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

#include "4g_mgr.h"
#include "4g_data_utils.h"

#define SLAVE_STATUS    0
#define MASTER_STATUS   1

#define MAX_IP_ERR_COUNT      1

#define NET_4G_INTERVAL             ((30000)/NET_4G_THREAD_RUN_INTERVAL)
#define GET_4G_INFO_INIT_TIME       ((15000)/NET_4G_THREAD_RUN_INTERVAL)
#define MAX_SIM_NO_COUNT            2

short is_site_id_effective();
short judge_simcard_status(rt_device_t dev, unsigned short curr_sim_status);
int judge_4G_ip(rt_device_t dev);
int get_4g_info(short isClear);
int check_4g_is_online(rt_device_t dev);
int is_4g_info_effective();
short set_master_slave_status(unsigned char flag);
short product_special_handle();
short model_4g_offline_handle(rt_device_t dev);
short check_sim_status_handle(rt_device_t dev);
int register_4g_func();

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  