#include <rtthread.h>
#include "flash.h"

void fmc_erase_pages(uint32_t offset)
{
	uint32_t w_addr = offset + GD32C103_FLASH_BASE;
	fmc_unlock();
	fmc_flag_clear(FMC_FLAG_END|FMC_FLAG_WPERR|FMC_FLAG_PGERR);

	fmc_page_erase(w_addr);
	fmc_flag_clear(FMC_FLAG_END | FMC_FLAG_WPERR | FMC_FLAG_PGERR);

	fmc_lock();
}

fmc_state_enum fmc_program(uint32_t offset, const uint8_t *buf, size_t size)
{
	fmc_state_enum ret = FMC_READY;
    fmc_data_t fmc_data = {0};

	uint32_t w_addr = offset + GD32C103_FLASH_BASE;
	fmc_unlock();
	for(uint32_t i=0; i<size; )
	{
		fmc_data.bf.data1 = buf[i++];
		fmc_data.bf.data2 = buf[i++];
		fmc_data.bf.data3 = buf[i++];
		fmc_data.bf.data4 = buf[i++];
		ret = fmc_word_program(w_addr,fmc_data.data);
		if(ret != FMC_READY){
			fmc_lock();
			return ret;
		}
		w_addr += 4;
		fmc_flag_clear(FMC_FLAG_END | FMC_FLAG_WPERR | FMC_FLAG_PGERR);
	}
	fmc_lock();
	return FMC_READY;
#if 0
	size_t i = 0,j = 0;
	size_t size_temp= 0;
	uint32_t addr = GD32C103_FLASH_BASE + offset;
	uint32_t data_temp = 0;

    fmc_state_enum ret;

	if(size%4)
	{
		size_temp = (size/4)*4+4;
	}
	else
	{
		size_temp = size;
	}

	fmc_unlock();

	fmc_flag_clear(FMC_FLAG_PGAERR | FMC_FLAG_PGERR | FMC_FLAG_WPERR | FMC_FLAG_END);

	for (i = 0; i < size_temp; i++)
	{
		if(i < size)
		{
			data_temp += buf[i] << (j*8);
		}
		else
		{
			data_temp += 0xFF <<(j*8);
		}
		j++;

		if(j < 4)
		{
			continue;
		}

		ret=fmc_word_program(addr,data_temp);

		if (ret == FMC_READY)
		{
		  if(*(rt_uint32_t*)addr!=(rt_uint32_t)data_temp)
			{
				break;
			}
		}
		else
		{
			break;
		}

		j = 0;
		data_temp	= 0;
		addr += 4;
	}
	fmc_lock();

	return ret;
#endif
}

void fmc_read_data(uint32_t offset, uint8_t *p,uint32_t num)
{
	uint32_t r_addr = 0;
	uint32_t index = 0;
	r_addr = offset + GD32C103_FLASH_BASE;
	fmc_data_t fmc_datas = {0};
	for(uint32_t i=0;i<num;)
	{
		fmc_datas.data = *(uint32_t *)(r_addr+i);
		i += 4;
		p[index++] = fmc_datas.bf.data1;
		p[index++] = fmc_datas.bf.data2;
		p[index++] = fmc_datas.bf.data3;
		p[index++] = fmc_datas.bf.data4;
	}
}
