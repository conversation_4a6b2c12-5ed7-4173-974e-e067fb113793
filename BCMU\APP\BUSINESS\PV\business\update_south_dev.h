
#ifndef _SOUTH_UPDATE_MANAGE_H
#define _SOUTH_UPDATE_MANAGE_H

#ifdef __cplusplus
extern "C" {
#endif

#include "cmd.h"
#include "device_type.h"
#include "msg.h"
#include "update_manage.h"
#include "backup_manage.h"
#include "north_update_utils.h"

#pragma pack(4)

#define TRIGGER_SUCCESS_TIMES       3
#define TRIGGER_FAILURE_TIMES       20

#define TRANS_FAILURE_TIMES         3
#define INTERRUPT_FAILURE_TIMES     3

//#define UPDATE_SUCCESS          0
#define TRANS_TRIGGER_FAIL      1
#define TRANS_DATA_FAIL         2
#define UPDATE_TRIGGER_FAIL     3
#define UPDATE_INTERRUPT_FAIL   4
#define UPDATE_TIMEOT           5

typedef enum {
    trans_trigger,
    trans_data,
    update_trigger,
    trans_interrupt,
    update_exit,
} update_state_e;

typedef struct {
    unsigned char dev_id;
    module_id_e mod_id;
    file_attr_t file_manage;
    unsigned char file_info[512];
    unsigned int file_addr;
    unsigned char is_first_data;
    unsigned char is_broadcast;
    unsigned char dev_num;
    unsigned char next_addr;
    update_state_e* cur_sta;
    unsigned short* suc_counts;
    unsigned short* fai_counts;
    unsigned short* fail_ret;
    unsigned short* his_fai_counts;
    unsigned short send_frm_no;     
} update_manage_t;

typedef struct {
    update_state_e update_sta;
    update_state_e (*state_judge)(update_manage_t* info, cmd_buf_t* cmd_buf);
    void (*state_pack_act)(update_manage_t* info, cmd_buf_t* cmd_buf);
    void (*state_parse_act)(update_manage_t* info, cmd_buf_t* cmd_buf);
} update_sta_map_t;


#pragma pack()

short start_south_update(file_attr_t* file_info, unsigned char dev_id, unsigned char dev_addr, unsigned char dev_num);
void init_south_update(void);
update_state_e get_update_stat(unsigned char dev_id, unsigned short dev_addr) ;
void finish_south_dev_update();
void south_dev_update_end();
void init_south_update(void);
update_manage_t* get_update_info(unsigned char dev_id);
int update_cmd_pack(void* dev_inst, void *cmd_buf);
int update_cmd_parse(void* dev_inst, void *cmd_buf);
void trans_data_pack(update_manage_t* info, cmd_buf_t* cmd_buf);
void trans_data_parse(update_manage_t* info, cmd_buf_t* cmd_buf);
unsigned short get_download_file_data_addr(file_attr_t* file_info, unsigned short frm_no, unsigned int* data_addr);
update_state_e trans_trigger_judge(update_manage_t* info, cmd_buf_t* cmd_buf);
update_state_e trans_data_judge(update_manage_t* info, cmd_buf_t* cmd_buf);
update_state_e update_trigger_judge(update_manage_t* info, cmd_buf_t* cmd_buf);
void clear_cmd_count(update_manage_t* info,  cmd_buf_t* cmd_buf);
update_state_e trans_interrupt_judge(update_manage_t* info, cmd_buf_t* cmd_buf);
void register_addr_di_func(void* arg);
void update_next_addr(update_manage_t* info, int cur_addr, update_state_e last_sta);


#ifdef __cplusplus
}
#endif

#endif  // _SOUTH_UPDATE_MANAGE_H

