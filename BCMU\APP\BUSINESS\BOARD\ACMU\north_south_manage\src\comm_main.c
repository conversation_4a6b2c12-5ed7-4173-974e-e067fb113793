
#include "north_south_main.h"

int init_isNorthOrSouth(server_info_t *server_group,int server_num)
{
    for ( int index= 0; index < server_num; index++){
        if (server_group[index].server.server.server_id == NORTH_SOUTH_SERVER_ID){
            if(get_acem_cfg())
            {
                server_group[index].server_init = init_south_comm;
                server_group[index].server_entry = south_comm_thread;
            }
            else
            {
                server_group[index].server_init = init_north_comm;
                server_group[index].server_entry = north_comm_thread;
            }
            break;
        }
    }
    return 0;
}