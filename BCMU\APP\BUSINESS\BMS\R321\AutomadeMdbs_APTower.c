/**************************************************************************
* 版权信息：（C）2011-2018，中兴通讯股份有限公司动力开发部版权所有
* 系统名称：MODBUS RTU协议代码
* 文件名称：AutoMdbsCode.c
* 文件说明：自动生成带modbus寄存器值及相关属性的数组
* 作    者：fsg
* 版本信息：V1.0
* 设计日期：2018-8-20
* 修改记录：
* 日    期      版  本      修改人      修改摘要
*
* 其他说明：
***************************************************************************/
#include "stddef.h"
#include "common.h"
#include "hisdata.h"
#include "sample.h"
#include "realAlarm.h"
#include "MdbsRtu.h"
#include "AutomadeMdbs.h"
#include "MdbsRtuAPT.h"

/****************************变量声明********************************/
const T_OneDataAttrStruct s_atAscDataAttr[] = {
	{0x03,0,200,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wParametersChanged_b),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,201,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wNoOfBatteryModules_b),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,202,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wNoOfBatteryStrings_b),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,203,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wBatteryStringCapacity_b),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,204,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wProdDateYear_b),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,205,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wProdDateMonth_b),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,206,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wProdDateDay_b),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,207,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acModelDescription_b[0]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,208,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acModelDescription_b[1]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,209,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acModelDescription_b[2]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,210,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acModelDescription_b[3]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,211,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acModelDescription_b[4]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,212,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acModelDescription_b[5]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,213,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acModelDescription_b[6]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,214,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acModelDescription_b[7]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,215,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wBatteryType_b),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,216,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acSerialNumber_b[0]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,217,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acSerialNumber_b[1]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,218,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acSerialNumber_b[2]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,219,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acSerialNumber_b[3]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,220,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acSerialNumber_b[4]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,221,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acSerialNumber_b[5]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,222,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acSerialNumber_b[6]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,223,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acSerialNumber_b[7]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,224,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acPartNumber_b[0]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,225,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acPartNumber_b[1]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,226,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acPartNumber_b[2]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,227,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acPartNumber_b[3]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,228,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acPartNumber_b[4]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,229,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acPartNumber_b[5]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,230,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acPartNumber_b[6]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,231,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acPartNumber_b[7]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,232,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acSoftwareVersion_b[0]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,233,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acSoftwareVersion_b[1]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,234,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acSoftwareVersion_b[2]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,235,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acSoftwareVersion_b[3]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,236,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acHardwareVersion_b[0]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,237,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acHardwareVersion_b[1]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,238,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acHardwareVersion_b[2]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,239,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acHardwareVersion_b[3]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,240,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wTempCompEnable_b),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,241,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wFloatChargeRefVoltage_b),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,242,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wNomFloatCharegeRefTemp_b),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,243,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wTempCompSlope_b),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,244,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wMaxChargeVoltage_b),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,245,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wMinChargeVoltage_b),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,246,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wBatteryCurrentLimitEnable_b),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,247,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wMaxBatteryChargeCurr_b),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,248,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wMaxBatteryDisChargeCurr_b),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,249,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wBoostChargeRefVoltage_b),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,250,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wEqualizeChargeRefVoltage_b),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,251,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wMaxBoostChargeTime_b),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,252,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wMaxEqualizeChargeTime_b),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,253,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wBoostInterval_b),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,254,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wEqualizeInterval_b),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,255,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wBoostTriggerVoltage_b),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,256,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wLowVoltBattDisconnect_b),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,257,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wLowVoltBatteryReconnect_b),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,258,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wHighTempBattDisconnect_b),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,259,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wHighTempBattDisconnectHyst_b),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,260,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wBatteryRate_b),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,261,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acManufacturerName_b[0]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,262,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acManufacturerName_b[1]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,263,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acManufacturerName_b[2]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,264,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acManufacturerName_b[3]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,265,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acManufacturerName_b[4]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,266,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acManufacturerName_b[5]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,267,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acManufacturerName_b[6]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,268,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acManufacturerName_b[7]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,269,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acName_b[0]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,270,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acName_b[1]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,271,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acName_b[2]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,272,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acName_b[3]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,273,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acName_b[4]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,274,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acName_b[5]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,275,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acName_b[6]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,276,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acName_b[7]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,277,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acProtocolDescription_b[0]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,278,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acProtocolDescription_b[1]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,279,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acProtocolDescription_b[2]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,280,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acProtocolDescription_b[3]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,281,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acProtocolDescription_b[4]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,282,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acProtocolDescription_b[5]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,283,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acProtocolDescription_b[6]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,284,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acProtocolDescription_b[7]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4000,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wExterVolt),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4001,DATA_TYPE_FP32,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,fBattCurr),1,DATA_TYPE_INT16S,0,2,NULL,},
	{0x03,0,4002,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wModuleTemperature),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4003,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wBattSOH),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4004,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wBattSOC),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4005,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wModuleImpedance),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4006,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wTimeLeft),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4007,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wAccumulatedCharged),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4008,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wAccumulatedDischarged),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4009,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wBattdischargeCycleTimes),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4010,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wBattchargeCycleTimes),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4011,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wModuleAlarms),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4012,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wModuleWarnings),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4013,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wModuleStatus),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4014,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wBattVolt),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4015,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wModbusVer),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4016,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[0]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4017,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[1]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4018,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[2]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4019,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[3]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4020,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[4]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4021,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[5]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4022,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[6]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4023,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[7]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4024,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[8]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4025,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[9]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4026,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[10]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4027,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[11]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4028,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[12]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4029,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[13]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4030,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[14]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4031,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[15]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4032,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[16]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4033,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[17]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4034,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[18]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4035,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[19]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4036,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[20]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4037,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[21]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4038,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[22]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4039,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[23]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4040,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[24]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4041,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[25]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4042,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[26]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4043,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[27]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4044,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[28]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4045,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[29]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4046,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[30]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4047,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[31]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4048,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[32]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4049,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[33]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4050,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[34]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4051,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[35]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4052,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[36]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4053,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[37]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4054,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[38]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4055,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[39]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4056,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[40]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4057,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[41]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4058,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[42]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4059,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[43]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4060,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[44]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4061,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[45]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4062,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[46]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4063,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[47]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4064,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[48]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4065,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[49]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4066,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[50]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4067,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[51]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4068,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[52]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4069,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[53]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4070,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[54]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4071,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[55]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4072,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[56]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4073,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[57]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4074,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[58]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4075,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[59]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4076,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[60]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4077,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[61]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4078,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[62]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4079,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[63]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4080,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[64]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4081,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[65]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4082,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[66]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4083,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[67]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4084,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[68]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4085,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[69]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4086,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[70]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4087,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[71]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4088,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[72]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4089,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[73]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4090,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[74]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4091,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[75]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4092,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[76]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4093,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[77]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4094,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[78]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4095,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[79]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4096,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[80]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4097,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[81]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4098,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[82]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4099,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[83]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4100,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[84]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4101,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[85]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4102,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[86]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4103,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[87]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4104,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[88]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4105,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[89]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4106,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[90]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4107,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[91]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4108,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[92]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4109,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[93]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4110,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[94]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4111,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[95]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4112,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[96]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4113,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[97]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4114,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[98]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4115,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[99]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4116,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[100]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4117,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[101]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4118,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[102]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4119,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[103]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4120,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[104]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4121,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[105]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4122,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[106]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4123,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[107]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4124,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[108]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4125,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[109]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4126,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[110]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4127,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[111]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4128,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[112]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4129,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[113]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4130,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[114]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4131,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[115]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4132,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[116]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4133,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[117]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4134,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[118]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4135,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[119]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4136,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[120]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4137,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[121]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4138,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[122]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4139,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[123]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4140,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[124]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4141,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[125]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4142,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[126]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4143,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[127]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4144,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[128]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4145,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[129]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4146,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[130]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4147,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[131]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4148,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[132]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4149,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[133]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4150,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[134]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4151,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[135]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4152,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[136]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4153,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[137]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4154,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[138]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4155,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[139]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4156,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[140]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4157,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[141]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4158,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[142]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4159,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[143]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4160,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[144]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4161,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[145]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4162,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[146]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4163,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[147]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4164,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[148]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4165,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[149]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4166,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[150]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4167,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[151]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4168,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[152]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4169,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[153]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4170,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[154]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4171,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[155]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4172,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[156]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4173,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[157]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4174,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[158]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4175,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[159]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4176,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[160]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4177,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[161]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4178,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[162]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4179,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[163]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4180,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[164]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4181,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[165]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4182,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[166]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4183,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[167]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4184,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[168]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4185,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[169]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4186,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[170]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4187,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[171]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4188,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[172]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4189,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[173]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4190,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[174]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4191,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[175]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4192,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[176]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4193,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[177]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4194,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[178]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4195,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[179]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4196,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[180]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4197,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[181]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4198,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[182]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4199,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparesamll[183]),184,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4200,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wParametersChanged),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4201,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wBatteryCap),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4202,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wMaxBattChargeCurr),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4203,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wMaxBattDischargeCurr),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4204,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wBatteryProdDateYear),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4205,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wBatteryProdDateMonth),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4206,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wBatteryProdDateDay),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4207,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBatteryModelDescription[0]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4208,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBatteryModelDescription[1]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4209,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBatteryModelDescription[2]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4210,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBatteryModelDescription[3]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4211,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBatteryModelDescription[4]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4212,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBatteryModelDescription[5]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4213,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBatteryModelDescription[6]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4214,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBatteryModelDescription[7]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4215,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wBatteryType),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4216,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBatterySerialNumber[0]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4217,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBatterySerialNumber[1]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4218,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBatterySerialNumber[2]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4219,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBatterySerialNumber[3]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4220,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBatterySerialNumber[4]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4221,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBatterySerialNumber[5]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4222,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBatterySerialNumber[6]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4223,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBatterySerialNumber[7]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4224,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBatteryPartNumber[0]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4225,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBatteryPartNumber[1]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4226,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBatteryPartNumber[2]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4227,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBatteryPartNumber[3]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4228,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBatteryPartNumber[4]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4229,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBatteryPartNumber[5]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4230,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBatteryPartNumber[6]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4231,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBatteryPartNumber[7]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4232,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBatterySoftwareVersion[0]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4233,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBatterySoftwareVersion[1]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4234,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBatterySoftwareVersion[2]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4235,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBatterySoftwareVersion[3]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4236,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBatteryHardwareVersion[0]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4237,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBatteryHardwareVersion[1]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4238,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBatteryHardwareVersion[2]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4239,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBatteryHardwareVersion[3]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4240,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wBatteryRate),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4241,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wNoOfCells),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4242,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,wCellSize),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4243,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acManufacturerName[0]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4244,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acManufacturerName[1]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4245,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acManufacturerName[2]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4246,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acManufacturerName[3]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4247,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acManufacturerName[4]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4248,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acManufacturerName[5]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4249,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acManufacturerName[6]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4250,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acManufacturerName[7]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4251,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acName[0]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4252,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acName[1]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4253,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acName[2]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4254,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acName[3]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4255,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acName[4]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4256,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acName[5]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4257,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acName[6]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4258,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acName[7]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4259,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBMSHardwareVersion[0]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4260,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBMSHardwareVersion[1]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4261,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBMSHardwareVersion[2]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4262,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acBMSHardwareVersion[3]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4263,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acProtocolDescription[0]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4264,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acProtocolDescription[1]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4265,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acProtocolDescription[2]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4266,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acProtocolDescription[3]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4267,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acProtocolDescription[4]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4268,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acProtocolDescription[5]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4269,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acProtocolDescription[6]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4270,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,acProtocolDescription[7]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4271,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[0]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4272,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[1]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4273,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[2]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4274,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[3]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4275,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[4]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4276,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[5]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4277,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[6]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4278,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[7]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4279,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[8]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4280,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[9]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4281,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[10]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4282,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[11]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4283,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[12]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4284,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[13]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4285,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[14]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4286,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[15]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4287,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[16]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4288,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[17]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4289,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[18]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4290,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[19]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4291,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[20]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4292,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[21]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4293,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[22]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4294,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[23]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4295,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[24]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4296,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[25]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4297,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[26]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4298,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[27]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4299,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[28]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4300,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[29]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4301,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[30]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4302,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[31]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4303,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[32]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4304,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[33]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4305,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[34]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4306,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[35]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4307,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[36]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4308,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[37]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4309,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[38]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4310,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[39]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4311,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[40]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4312,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[41]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4313,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[42]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4314,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[43]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4315,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[44]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4316,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[45]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4317,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[46]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4318,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[47]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4319,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[48]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4320,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[49]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4321,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[50]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4322,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[51]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4323,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[52]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4324,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[53]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4325,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[54]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4326,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[55]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4327,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[56]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4328,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[57]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4329,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[58]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4330,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[59]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4331,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[60]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4332,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[61]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4333,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[62]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4334,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[63]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4335,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[64]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4336,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[65]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4337,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[66]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4338,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[67]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4339,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[68]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4340,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[69]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4341,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[70]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4342,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[71]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4343,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[72]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4344,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[73]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4345,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[74]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4346,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[75]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4347,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[76]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4348,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[77]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4349,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[78]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4350,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[79]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4351,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[80]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4352,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[81]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4353,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[82]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4354,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[83]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4355,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[84]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4356,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[85]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4357,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[86]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4358,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[87]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4359,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[88]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4360,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[89]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4361,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[90]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4362,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[91]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4363,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[92]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4364,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[93]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4365,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[94]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4366,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[95]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4367,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[96]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4368,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[97]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4369,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[98]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4370,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[99]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4371,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[100]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4372,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[101]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4373,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[102]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4374,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[103]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4375,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[104]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4376,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[105]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4377,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[106]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4378,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[107]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4379,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[108]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4380,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[109]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4381,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[110]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4382,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[111]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4383,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[112]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4384,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[113]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4385,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[114]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4386,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[115]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4387,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[116]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4388,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[117]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4389,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[118]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4390,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[119]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4391,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[120]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4392,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[121]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4393,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[122]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4394,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[123]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4395,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[124]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4396,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[125]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4397,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[126]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4398,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[127]),129,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0,4399,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_CmpakSMUStruct,aBattSparelarge[128]),129,DATA_TYPE_INT16U,0,2,NULL,},
};

/********************************************************************/

/***************************************************************************
* 函数名称：GetDataAttrPoint()
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：
* 作    者：fsg
* 设计日期：2010-01-24
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
const T_OneDataAttrStruct  *GetDataAttrPoint(void)
{
	/*if(NULL == s_atAscDataAttr)
	{
		return NULL;
	}*/
	return s_atAscDataAttr;
}

/***************************************************************************
* 函数名称：GetDataAttrNum()
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：
* 作    者：fsg
* 设计日期：2010-01-24
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
INT32  GetDataAttrNum(void)
{
	/*if(NULL == s_atAscDataAttr)
	{
		return APP_FAILURE;
	}*/
	return sizeof(s_atAscDataAttr) / sizeof(s_atAscDataAttr[0]);
}
