#include <rtthread.h>
#include <rtdevice.h>
#include <math.h>
#include <string.h>
#include "sample.h"
#include "control_manage.h"
#include "msg.h"
#include "msg_id.h"
#include "utils_math.h"
#include "pin.h"
#include "realdata_save.h"
#include "realdata_id_in.h"
#include "dev_airswitch.h"
#include "pin_define.h"
#include "utils_server.h"
#include "utils_thread.h"
#include "utils_rtthread_security_func.h"
#include "device_common.h"
#include "para_id_in.h"
#include "para_manage.h"
#include <drv_motor_ctrl.h>
#include "motor_control.h"

#define     FAULT           1                 /* 异常 */

/***********************  全局变量定义  ************************/
Static msg_map s_control_msg_map[] =
{
    {0,NULL},
};
Static rt_device_t dev_motor = RT_NULL;
Static struct motor_config s_CurrentMotorConfig={0,1,1000};
Static struct motor_config s_SetMotorConfig={0,1,1000};
Static unsigned char ctrl_status = 0;
Static unsigned char power_on_power_down_command = 0;

Static char airswitch_control_by_state(airswitch_control_status_e control_status);
Static char update_power_down_status(void);

/**
 * @brief 智能空开合闸控制
 * 
 * @param type
 * @return SUCCESSFUL 成功 FAILURE 失败
 * @note 
 */
char ctrl_closing(void)
{
    char ret = 0;
    // 电机控制合闸
    ret = motor_act_close();
    return ret;
}

/**
 * @brief 智能空开分闸控制
 * 
 * @param type
 * @return SUCCESSFUL 成功 FAILURE 失败
 * @note 
 */
char ctrl_opening(void)
{
    char ret = 0;

    // 电机控制分闸
    if (ctrl_status == AIRSWITCH_COMMAND_STATUS_UNAUTHORIZE)
    {
        ret = unauthorize_act_open();
    }
    else
    {
        ret = motor_act_open();
    }
    return ret;
}

/**
 * @brief 智能空开回路判断
 * 
 * @param type
 * @return int 判断结果
 * @note 回路判断函数，判断回路状态，并修改空开下电状态
 */
unsigned char get_loop_status(void)
{
    unsigned char close_status = 0;
    get_one_data(SWITCH_DATA_ID_LOOP_STATUS,&close_status);
    return close_status;
}

/**
 * @brief 智能空开控制
 * 
 * @param type 参数类型
 * @return BOOLEAN 判断结果
 * @note 3+1控制函数
 */
char airswitch_brake_ctrl(int brake)
{
    unsigned char brake_Failure = FAULT;
    unsigned char download_status = AIRSWITCH_POWER_ON;
    unsigned char cnt = 0;
    unsigned char power_on_down_command = 1;

    for(cnt = 0; cnt < 4; cnt++)
    {
        if(cnt == 3){
            rt_thread_mdelay(5000);
        }
        if(brake == LOOP_OPEN)
        {
            ctrl_opening();
        }
        else{
            ctrl_closing();
        }
        rt_thread_mdelay(100);
        if(brake == get_loop_status())
        {
            if(LOOP_CLOSE == brake)
            {
                set_one_data(SWITCH_DATA_ID_DOWNLOAD_STATUS,&download_status);
                set_one_para(SWITCH_PARA_ID_POWER_ON_POWER_DOWN_COMMAND_OFFSET, &download_status, TRUE, FALSE);
            }
            return SUCCESSFUL;
        }
    }
    if(LOOP_CLOSE == brake)
    {
        set_one_data(SWITCH_DATA_ID_CLOSE_FAILURE_ALARM,&brake_Failure);
    }
    else if(LOOP_OPEN == brake)
    {
        power_on_down_command = AIRSWITCH_POWER_ON;
        set_one_para(SWITCH_PARA_ID_POWER_ON_POWER_DOWN_COMMAND_OFFSET, &power_on_down_command, TRUE, FALSE);
        set_one_data(SWITCH_DATA_ID_DOWNLOAD_STATUS,&download_status);
        set_one_data(SWITCH_DATA_ID_OPENING_FAILURE_ALARM,&brake_Failure);
    }
    return FAILURE;
}

Static char airswitch_control_by_state(airswitch_control_status_e control_status)
{
    char control_result = FAILURE;

    if (AIRSWITCH_CONTROL_STATUS_CLOSE == control_status||AIRSWITCH_COMMAND_STATUS_CLOSE == control_status)
    {
        control_result = airswitch_brake_ctrl(LOOP_CLOSE);  // 空开合闸控制
    }
    else if((AIRSWITCH_CONTROL_STATUS_OPEN == control_status) ||
            (AIRSWITCH_COMMAND_STATUS_OPEN == control_status) ||
            (AIRSWITCH_COMMAND_STATUS_UNAUTHORIZE == control_status))
    {
        control_result = airswitch_brake_ctrl(LOOP_OPEN);  // 空开分闸控制
    }

    return control_result;
}

/* 下电状态更新 命令上下电成功时调用 */
Static char update_power_down_status(void)
{
    unsigned char power_down_status = 0;
    unsigned char loop_status = 0;

    get_one_data(SWITCH_DATA_ID_LOOP_STATUS, &loop_status);

    if(loop_status == LOOP_OPEN && power_on_power_down_command == AIRSWITCH_POWER_DOWN)
    {
        power_down_status = AIRSWITCH_POWER_DOWN;
        set_one_data(SWITCH_DATA_ID_DOWNLOAD_STATUS, &power_down_status);
        set_one_para(SWITCH_PARA_ID_POWER_ON_POWER_DOWN_COMMAND_OFFSET, &power_down_status, TRUE, FALSE);
        set_one_data(SWITCH_DATA_ID_DOWN_POWER_ALARM, &power_down_status);
    }
    else if(loop_status == LOOP_CLOSE && power_on_power_down_command == AIRSWITCH_POWER_ON)
    {
        power_down_status = AIRSWITCH_POWER_ON;
        set_one_data(SWITCH_DATA_ID_DOWNLOAD_STATUS, &power_down_status);
        set_one_para(SWITCH_PARA_ID_POWER_ON_POWER_DOWN_COMMAND_OFFSET, &power_down_status, TRUE, FALSE);
        set_one_data(SWITCH_DATA_ID_DOWN_POWER_ALARM, &power_down_status);
    }

    return 0;
}

Static char deal_protect_control(void)
{
    // 空开分、合闸控制始终实行3*(3+1)控制
    // 单次(3+1)控制的结果
    char control_result = FAILURE;

    static unsigned char s_control_fail_count = 0; // 上下电命令产生的分合闸失败的次数
    unsigned char controller_fault_alarm = 0;
    
    static rt_tick_t s_control_fail_tick = 0; // 上下电命令产生分合闸失败计数
    static rt_tick_t s_control_fail_tick_init = 0;

    get_one_data(SWITCH_DATA_ID_CONTROL_STATUS, &ctrl_status);
    get_one_para(SWITCH_PARA_ID_POWER_ON_POWER_DOWN_COMMAND_OFFSET, &power_on_power_down_command);

    // 不需要执行分合闸操作直接返回
    if(ctrl_status == AIRSWITCH_CONTROL_STATUS_NO_ACT)
    {
        return FAILURE;
    }

    // 如果是未授权转授权，执行回自由位置
    if (ctrl_status == AIRSWITCH_COMMAND_STATUS_AUTHORIZE)
    {
        control_result = authorize_act_reset();
        ctrl_status = AIRSWITCH_CONTROL_STATUS_NO_ACT;
        set_one_data(SWITCH_DATA_ID_CONTROL_STATUS, &ctrl_status);
        return control_result;
    }

    // 如果已经产生控制器故障告警，那么不允许执行上下电操作。
    get_one_data(SWITCH_DATA_ID_CONTROLLER_FAILURE_ALARM, &controller_fault_alarm);
    if(controller_fault_alarm == FAULT)
    {
        ctrl_status = AIRSWITCH_CONTROL_STATUS_NO_ACT;
        set_one_data(SWITCH_DATA_ID_CONTROL_STATUS, &ctrl_status);
        return FAILURE;
    }

    save_power_energy();    // 保存电量信息到eeprom中

    control_result = airswitch_control_by_state(ctrl_status);
    if(control_result == FAILURE)
    {
        s_control_fail_count++;

        s_control_fail_tick = rt_tick_get_millisecond();
        if (fabs(s_control_fail_tick - s_control_fail_tick_init) > CONTROLLER_FAIL_TICK || 3 < s_control_fail_count)
        {
            s_control_fail_count = 1;  //两次失败操作间隔5min以上，从1开始重新计数
            s_control_fail_tick_init = s_control_fail_tick;
        }
        set_one_data(SWITCH_DATA_ID_ACTION_COUNT, &s_control_fail_count);
        
        ctrl_status = AIRSWITCH_CONTROL_STATUS_NO_ACT;
        set_one_data(SWITCH_DATA_ID_CONTROL_STATUS, &ctrl_status);
        return FAILURE;
    }
    else
    {
        s_control_fail_count = 0;
        set_one_data(SWITCH_DATA_ID_ACTION_COUNT, &s_control_fail_count);
        if(ctrl_status == AIRSWITCH_COMMAND_STATUS_CLOSE||ctrl_status == AIRSWITCH_COMMAND_STATUS_OPEN)
        {
            // 上电下电命令执行成功，更新下电状态
            update_power_down_status();
        }
        // 空开控制成功之后将控制状态置为为不动作，等待下一次控制。
        ctrl_status = AIRSWITCH_CONTROL_STATUS_NO_ACT;
        set_one_data(SWITCH_DATA_ID_CONTROL_STATUS, &ctrl_status);
        return SUCCESSFUL;
    }

    return FAILURE;
}

void* init_control_manage(void* param)
{
    server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(s_control_msg_map) / sizeof(msg_map);
    register_server_msg_map(s_control_msg_map, server_info);

    init_motor_control();

    return param;
}

void control_manage_main(void* parameter)
{
    unsigned char authorize_status = 0;
    unsigned char apptest_flag = 0;
    get_one_para(SWITCH_PARA_ID_POWER_ON_AUTHORIZATION_OFFSET, &authorize_status);
    get_one_data(SWITCH_DATA_ID_APPTEST_FLAG, &apptest_flag);

    //  上电电机回初始位置
    if ((TRUE == authorize_status) &&
        (FALSE == apptest_flag))
    {
        motor_act_power_on();
    }

    while (is_running(TRUE))
    {
        rt_thread_mdelay(50);
        get_one_data(SWITCH_DATA_ID_APPTEST_FLAG, &apptest_flag);
        if (TRUE == apptest_flag)
        {
            continue;
        }
        deal_protect_control();
    }
}

/**
 * 设置电机的速度、方向
 * speed : 取值0-100,表示速度 
 * direction:0-反向，1-正向
 */
char airswitchMotorCtrl(int speed, int direction)
{
    rt_err_t ret = RT_ERROR;

    // 对设置的速度、方向、频率做校验
    if(speed < 0 || speed > 100)
    {
        return FAILURE;
    }

    if(direction!=0 && direction!=1)
    {
        return FAILURE;
    }

    dev_motor = rt_device_find("MOTOR_CTRL");
    if (dev_motor == RT_NULL)
    {
        return FAILURE;
    }

    s_SetMotorConfig.speed = speed;
    s_SetMotorConfig.direction = direction;
    s_SetMotorConfig.freq = 1000;//BSP说此项数据暂不需要设置，配置为固定值

    // 调用BSP给的设置接口
    ret = rt_device_control(dev_motor, RT_NULL, &s_SetMotorConfig);
    if (ret != RT_EOK)
    {
        return FAILURE;
    }

    // 更新当前的设置值
    rt_memcpy_s(&s_CurrentMotorConfig, sizeof(struct motor_config), &s_SetMotorConfig, sizeof(struct motor_config));

    return SUCCESSFUL;
}

/**
 * 软件调试：微调电机位置
 * speed : 取值0-100,表示速度 
 * direction:0-反向，1-正向
 */
int motor_ctrl_debug(int speed, int direction)
{
    // 对设置的速度、方向、频率做校验
    if(speed < 0 || speed > 100)
    {
        return FAILURE;
    }

    if(direction!=0 && direction!=1)
    {
        return FAILURE;
    }

    dev_motor = rt_device_find("MOTOR_CTRL");
    if (dev_motor == RT_NULL)
    {
        return FAILURE;
    }

    struct motor_config config = {speed, direction, PWM_FREQUENCY};
    if (direction == 0)
    {
        rt_device_control(dev_motor, RT_NULL, &config);
        rt_thread_mdelay(20);
        config.speed = 0;
        rt_device_control(dev_motor, RT_NULL, &config);
    }

    if (direction == 1)
    {
        rt_device_control(dev_motor, RT_NULL, &config);
        rt_thread_mdelay(20);
        config.speed = 0;
        rt_device_control(dev_motor, RT_NULL, &config);
    }

    return SUCCESSFUL;
}
