/*
 * @Author: dml00265523
 * @Date: 2025-06-13 10:35:36
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-09-23 09:13:15
 * @Description: 
 * @FilePath: \BCMU\APP\BUSINESS\BOARD\ACMU\business\gui_data_interface.c
 */
#include "data_type.h"
#include "realdata_id_in.h"
#include "para_id_in.h"
#include "realdata_save.h"
#include "para_manage.h"
#include "gui_data_interface.h"
#include "utils_rtthread_security_func.h"
#include "alarm_manage.h"
#include "alarm_id_in.h"
#include "his_record.h"
#include "const_define_in.h"
#include "utils_data_type_conversion.h"
#include "his_record.h"
#include "MIBTable.h"
#include "io_control.h"
#include "do_ctrl.h"
#include "dev_acmu.h"

static T_AnalogDataStruct    s_tAnalogData = {0};        // 实时模拟量数据
static T_DigitalDataStruct    s_tDigitalData = {0};        // 实时状态量数据
static T_AlarmStruct     s_tAlarmNoMsk = {0};            // 屏蔽关系前的告警值
Static T_SysPara  s_tSysPara = {0};                // 系统参数
Static T_SysPara  s_tGUIPara = {0};                // 用户界面设置参数

static rt_mutex_t key_mutex = RT_NULL;  // 互斥锁
Static unsigned char    s_aucKeyBuf[KEY_BUFF_LEN];    //键盘缓冲区 
Static unsigned char    s_ucKeyHead    = 0;            //键盘缓冲区的头指针 
Static unsigned char    s_ucKeyTail    = 0;            //键盘缓冲区的尾指针 

Static int get_para(unsigned short para_id_offset_base, unsigned char index, void* pSysPara);
Static char GetCommonPara(void);
Static char GetAcPara(void);
Static char GetEnvPara(void);
Static char GetAlarmRelayPara(void);
Static char GetAlarmGradePara(void);
Static char GetCommonPara2(void);


/* 初始化互斥锁 */
void key_buffer_init(void)
{
    key_mutex = rt_mutex_create("key_mtx", RT_IPC_FLAG_PRIO);
    return;
}

/****************************************************************************
* 函数名称:    AddKey
* 调    用:    无
* 被 调 用: 外部主函数调用
* 输入参数: ucKeyID -- 写入的键值
* 返 回 值: 无
* 功能描述: 键盘队列的写入
* 作    者: 
* 设计日期: 
* 修改记录: 
* 日    期        版    本        修改人        修改摘要      
***************************************************************************/
void AddKey( unsigned char ucKeyID )
{
    rt_mutex_take(key_mutex, RT_WAITING_FOREVER);
    s_aucKeyBuf[s_ucKeyTail]    = ucKeyID;
    
    s_ucKeyTail++;
    s_ucKeyTail    %= KEY_BUFF_LEN;
    
    if( s_ucKeyHead == s_ucKeyTail )
    {
        s_ucKeyHead++;
        s_ucKeyHead    %= KEY_BUFF_LEN;
    }
    rt_mutex_release(key_mutex);  // 释放互斥锁
    // SendMessage( MSG_KEY_PRESSED );
    // SetAddkeyTimer(50);//同步按键响应慢

    return;
}

/****************************************************************************
* 函数名称:    DelKey
* 调    用:    无
* 被 调 用: 外部主函数调用
* 输入参数: 无
* 返 回 值: 无
* 功能描述: 读出键盘值
* 作    者: 
* 设计日期: 
* 修改记录: 
* 日    期        版    本        修改人        修改摘要      
***************************************************************************/
unsigned char DelKey( void )
{
    unsigned char    ucRet;
    rt_mutex_take(key_mutex, RT_WAITING_FOREVER);
    if( s_ucKeyHead==s_ucKeyTail )
    {
        rt_mutex_release(key_mutex);
        return KEY_NOTHING;
    }
    
    ucRet    = s_aucKeyBuf[s_ucKeyHead];
    
    s_ucKeyHead++;
    s_ucKeyHead    %=KEY_BUFF_LEN;
    rt_mutex_release(key_mutex);
    return ucRet;
}

/**
 * @description: 
 * @param {char *} pDest
 * @return {*}
 */
int get_acem_fact_info(char * pDest) {
    T_ACEMFactStruct acem_fact;
    
    RETURN_VAL_IF_FAIL(pDest != NULL, FAILURE);

    rt_memset_s(&acem_fact, sizeof(T_ACEMFactStruct), 0x0, sizeof(T_ACEMFactStruct));
    get_one_data(ACMU_DATA_ID_ACEM_VERSION_IN_GUI, acem_fact.aucACEMSysVersion);

    rt_memcpy_s(pDest, sizeof(T_ACEMFactStruct), &acem_fact, sizeof(T_ACEMFactStruct));

    return SUCCESSFUL;
}




static int GetAcAlarmNoMskData(void)
{
    int i = 0;
    char true_alarm_value = FALSE;
    for(i = 0; i < 12; i++) {
        true_alarm_value = get_true_alarm_value(GET_ALM_ID(ACMU_ALM_ID_AC_OUTPUT_SWITCH_DISCONNECTION , 1+i , 1 ));
        s_tAlarmNoMsk.tACAlarm.aucOutSwitch[i] = true_alarm_value;   // 交流输出空开告警
    }

    for(i = 0; i < 3; i++) {
        true_alarm_value = get_true_alarm_value(GET_ALM_ID(ACMU_ALM_ID_AC_PHASE_LOST , i+1 , 1 ));
        s_tAlarmNoMsk.tACAlarm.aucPhaseLost[i] = true_alarm_value;
        true_alarm_value = get_true_alarm_value(GET_ALM_ID(ACMU_ALM_ID_PHASE_VOLT_HIGH_ALARM , i+1 , 1 ));
        s_tAlarmNoMsk.tACAlarm.aucVoltHigh[i] = true_alarm_value;
        true_alarm_value = get_true_alarm_value(GET_ALM_ID(ACMU_ALM_ID_PHASE_VOLT_LOW_ALARM , i+1 , 1 ));
        s_tAlarmNoMsk.tACAlarm.aucVoltLow[i] = true_alarm_value;
        true_alarm_value = get_true_alarm_value(GET_ALM_ID(ACMU_ALM_ID_PHASE_VOLT_HIGH_ALARM , i+1 , 1 ));
        s_tAlarmNoMsk.tACAlarm.aucCurr[i] = true_alarm_value;
        true_alarm_value = get_true_alarm_value(GET_ALM_ID(ACMU_ALM_ID_AC_LINE_VOLT_HIGH , i+1 , 1 ));
        s_tAlarmNoMsk.tACAlarm.aucPhasePhaseVoltHigh[i] = true_alarm_value;
        true_alarm_value = get_true_alarm_value(GET_ALM_ID(ACMU_ALM_ID_AC_LINE_VOLT_LOW , i+1 , 1 ));
        s_tAlarmNoMsk.tACAlarm.aucPhasePhaseVoltLow[i] = true_alarm_value;
    }
    true_alarm_value = get_true_alarm_value(GET_ALM_ID(ACMU_ALM_ID_AC_POWER_OFF , 1 , 1 ));
    s_tAlarmNoMsk.tACAlarm.ucPowerOff = true_alarm_value;
    true_alarm_value = get_true_alarm_value(GET_ALM_ID(ACMU_ALM_ID_PHASE_VOLT_UNBALANCE , 1 , 1 ));
    s_tAlarmNoMsk.tACAlarm.ucPhUnbalance = true_alarm_value;
    true_alarm_value = get_true_alarm_value(GET_ALM_ID(ACMU_ALM_ID_SPD_C , 1 , 1 ));
    s_tAlarmNoMsk.tACAlarm.ucSPD_C = true_alarm_value;
    true_alarm_value = get_true_alarm_value(GET_ALM_ID(ACMU_ALM_ID_AC_METER_DISCONNECTED , 1 , 1 ));
    s_tAlarmNoMsk.tACAlarm.ucACEMCommFail = true_alarm_value;
    return SUCCESSFUL;
}

static int GetEnvAlarmNoMskData(void)
{
    char true_alarm_value = FALSE;
    true_alarm_value = get_true_alarm_value(GET_ALM_ID(ACMU_ALM_ID_ENV_TEMP_HIGH_ALARM , 1 , 1 ));
    s_tAlarmNoMsk.tENVAlarm.ucTempHigh = true_alarm_value;
    true_alarm_value = get_true_alarm_value(GET_ALM_ID(ACMU_ALM_ID_ENV_TEMP_LOW_ALARM , 1 , 1 ));     
    s_tAlarmNoMsk.tENVAlarm.ucTempLow = true_alarm_value; 
    true_alarm_value = get_true_alarm_value(GET_ALM_ID(ACMU_ALM_ID_ENV_HUMIDITY_HIGH_ALARM , 1 , 1 ));
    s_tAlarmNoMsk.tENVAlarm.ucHumHigh = true_alarm_value; 
    true_alarm_value = get_true_alarm_value(GET_ALM_ID(ACMU_ALM_ID_ENV_HUMIDITY_LOW_ALARM , 1 , 1 ));
    s_tAlarmNoMsk.tENVAlarm.ucHumLow = true_alarm_value; 
    true_alarm_value = get_true_alarm_value(GET_ALM_ID(ACMU_ALM_ID_FUMES_SENSOR_ALARM , 1 , 1 ));
    s_tAlarmNoMsk.tENVAlarm.ucSmog = true_alarm_value; 
    true_alarm_value = get_true_alarm_value(GET_ALM_ID(ACMU_ALM_ID_FLOOD_SENSOR_ALARM , 1 , 1 ));
    s_tAlarmNoMsk.tENVAlarm.ucFlood = true_alarm_value;     
    true_alarm_value = get_true_alarm_value(GET_ALM_ID(ACMU_ALM_ID_DOORMAT_SENSOR_ALARM , 1 , 1 ));
    s_tAlarmNoMsk.tENVAlarm.ucDoorMag = true_alarm_value; 
    true_alarm_value = get_true_alarm_value(GET_ALM_ID(ACMU_ALM_ID_ENV_TEMP_SENSOR_INVALID_ALARM , 1 , 1 ));
    s_tAlarmNoMsk.tENVAlarm.ucETSensor = true_alarm_value; 
    true_alarm_value = get_true_alarm_value(GET_ALM_ID(ACMU_ALM_ID_ENV_HUMIDITY_SENSOR_INVALID_ALARM , 1 , 1 ));
    s_tAlarmNoMsk.tENVAlarm.ucEHSensor = true_alarm_value; 

    return SUCCESSFUL;
}

static int GetAlarmNoMskData(void)
{
    char true_alarm_value = FALSE;
    GetAcAlarmNoMskData();
    GetEnvAlarmNoMskData(); 
    true_alarm_value = get_true_alarm_value(GET_ALM_ID(ACMU_ALM_ID_TOTAL_ALARM , 1 , 1 ));
    s_tAlarmNoMsk.ucCommon = true_alarm_value;
    true_alarm_value = get_true_alarm_value(GET_ALM_ID(ACMU_ALM_ID_OIL_ENGINE_START_UP_ALARM , 1 , 1 ));
    s_tAlarmNoMsk.ucGenStart = true_alarm_value;
    true_alarm_value = get_true_alarm_value(GET_ALM_ID(ACMU_ALM_ID_INPUT_RELAY1_ALARM , 1 , 1 ));
    s_tAlarmNoMsk.aucInRelay[0] = true_alarm_value;
    true_alarm_value = get_true_alarm_value(GET_ALM_ID(ACMU_ALM_ID_INPUT_RELAY2_ALARM , 1 , 1 ));
    s_tAlarmNoMsk.aucInRelay[1] = true_alarm_value;
    true_alarm_value = get_true_alarm_value(GET_ALM_ID(ACMU_ALM_ID_INPUT_RELAY3_ALARM , 1 , 1 ));
    s_tAlarmNoMsk.aucInRelay[2] = true_alarm_value;
    true_alarm_value = get_true_alarm_value(GET_ALM_ID(ACMU_ALM_ID_INPUT_RELAY4_ALARM , 1 , 1 ));
    s_tAlarmNoMsk.aucInRelay[3] = true_alarm_value;
    return SUCCESSFUL;
}

/**
 * @description: 获取没有经过屏蔽关系的告警
 * @param {unsigned char} ucPart 交流/整流/直流等ID号
 * @param {char} *pDest 目标地址
 * @return {*}
 */
void GetAlarmNoMsk( unsigned char ucPart, char *pDest )
{
    char * ptSource;
    unsigned char    ucSize;

    RETURN_IF_FAIL(pDest != NULL);
    
    switch ( ucPart )
    {
        case AC_PART:
            GetAcAlarmNoMskData();
            ptSource = ( char* )&s_tAlarmNoMsk.tACAlarm;
            ucSize = sizeof( T_ACAlarmStruct );
            break;
        case ENV_PART:
            GetEnvAlarmNoMskData();
            ptSource = ( char* )&s_tAlarmNoMsk.tENVAlarm;
            ucSize = sizeof( T_ENVAlarmStruct );
            break;
        case ALL_PART:
            GetAlarmNoMskData();
            ptSource = ( char* )&s_tAlarmNoMsk;
            ucSize = sizeof( T_AlarmStruct );
            break;
        default:
            ptSource = ( char* )&s_tAlarmNoMsk;
            ucSize = 0;
            break;
    }
    
    rt_memcpy_s( pDest, ucSize, ptSource, ucSize );
    return;
}


static int GetAcDigitalData(void)
{
    int i = 0;
    unsigned char uc_data = 0;
    for(i = 0; i < 12; i++) {
        get_one_data(ACMU_DATA_ID_AC_OUT_SWITCH + i, &uc_data);
        s_tDigitalData.tACData.aucACOutSwitch[i] = uc_data;   // 交流输出空开：0 闭合；1 断开
    }

    for(i = 0; i < 4; i++) {
        get_one_data(ACMU_DATA_ID_INPUT_RELAY + i, &uc_data);
        s_tDigitalData.tACData.aucInputRelay[i] = uc_data;   // 输入继电器  0闭合；1断开 
    }

    get_one_data(ACMU_DATA_ID_POWER_SOURCE, &uc_data);
    s_tDigitalData.tACData.ucPowerSource = uc_data;   // 交流供电方式：0市电1；1市电2；2电池
    get_one_data(ACMU_DATA_ID_INPUT_RELAY, &uc_data);
    s_tDigitalData.tACData.ucACMainSwitch = uc_data;   // 交流输入空开：0市电1；1市电2
    get_one_data(ACMU_DATA_ID_INPUT_RELAY, &uc_data);
    s_tDigitalData.tACData.ucLamp = uc_data;   // 应急照明灯状态:0灭；1亮
    get_one_data(ACMU_DATA_ID_INPUT_RELAY, &uc_data);
    s_tDigitalData.tACData.ucACSPD_C = uc_data;   // C级防雷回路：0正常；1故障
    get_one_data(ACMU_DATA_ID_INPUT_RELAY, &uc_data);
    s_tDigitalData.tACData.ucACEMState = uc_data;   // 交流电表在位：0不在位，1在位

    return SUCCESSFUL;
}

static int GetEnvDigitalData(void)
{
    unsigned char uc_data = 0;
    get_one_data(ACMU_DATA_ID_POWER_SOURCE, &uc_data);
    s_tDigitalData.tENVData.ucSmog = uc_data;  
    get_one_data(ACMU_DATA_ID_POWER_SOURCE, &uc_data);   
    s_tDigitalData.tENVData.ucFlood = uc_data;  
    get_one_data(ACMU_DATA_ID_POWER_SOURCE, &uc_data);
    s_tDigitalData.tENVData.ucDoorMag = uc_data;
    get_one_data(ACMU_DATA_ID_POWER_SOURCE, &uc_data);
    s_tDigitalData.tENVData.ucDoorInfrared = uc_data; 
    get_one_data(ACMU_DATA_ID_POWER_SOURCE, &uc_data); 
    s_tDigitalData.tENVData.ucGlass = uc_data;

    return SUCCESSFUL;
}

/**
 * @description: 获取实时数字量数据
 * @param {unsigned char} ucPart 数据段
 * @param {char *} pDest 目标结构体变量首地址
 * @return {*}
 */
void GetDigitalData(unsigned char ucPart, char *pDest)
{
    char* ptSource;
    unsigned char ucSize;

    RETURN_IF_FAIL(pDest != NULL);

    switch (ucPart)
    {
        case AC_PART:
            GetAcDigitalData();
            ptSource = (char*)&s_tDigitalData.tACData;
            ucSize = sizeof(T_ACDigitalDataStruct);
            break;
        case ENV_PART:
            GetEnvDigitalData();
            ptSource = (char*)&s_tDigitalData.tENVData;
            ucSize = sizeof(T_ENVDigitalDataStruct);
            break;
        case ALL_PART:
            GetAcDigitalData();
            GetEnvDigitalData();
            ptSource = (char*)&s_tDigitalData;
            ucSize = sizeof(T_DigitalDataStruct);
            break;
        default:
            ptSource = (char*)&s_tDigitalData;
            ucSize = 0;
            break;
    }

    rt_memcpy_s(pDest, ucSize, ptSource, ucSize);

    return;
}

static int GetAcAnalogData(void)
{
    int i = 0;
    float f_data = 0.0f;
    for (i = 0; i < 3; i++)
    {
        get_one_data(ACMU_DATA_ID_PHASE_VOLT + i, &f_data);
        s_tAnalogData.tAcAnalogData.aiPhaseVolt[i] = f_data;      // 交流相电压
        get_one_data(ACMU_DATA_ID_PHASE_CURR + i, &f_data);
        s_tAnalogData.tAcAnalogData.aiIin[i] = f_data;            // 交流线电流
        get_one_data(ACMU_DATA_ID_LINE_VOLT + i, &f_data);
        s_tAnalogData.tAcAnalogData.aiPhasePhaseVolt[i] = f_data; // 交流线电压
        get_one_data(ACMU_DATA_ID_PHASE_CURR + i, &f_data);
        s_tAnalogData.tAcAnalogData.aslAcPhaseCur[i] = f_data;    // 交流相电流
    }

    return SUCCESSFUL;
}

static int GetEnvAnalogData(void)
{
    float f_data = 0;
    get_one_data(ACMU_DATA_ID_ENV_TEMP, &f_data);
    s_tAnalogData.tEnvAnalogData.scTemp = (char)round(f_data);     // 温度
    get_one_data(ACMU_DATA_ID_ENV_HUMIDITY, &f_data);
    s_tAnalogData.tEnvAnalogData.scHum = (char)round(f_data);      // 湿度

    return SUCCESSFUL;
}

/*
 * @description: 获取实时模拟量数据
 * @param {unsigned char} ucPart 数据段
 * @param {char *} pDest 目标结构体变量首地址
 * @return {*}
 */
void GetAnalogData( unsigned char ucPart, char * pDest )
{
    char * ptSource;
    unsigned char    ucSize;
    
    RETURN_IF_FAIL(pDest != NULL);
    
    switch ( ucPart )
    {
        case AC_PART:
            GetAcAnalogData();
            ptSource = ( char* )&s_tAnalogData.tAcAnalogData;
            ucSize = sizeof( T_AcAnalogDataStruct );
            break;
        case ENV_PART:
            GetEnvAnalogData();
            ptSource = ( char* )&s_tAnalogData.tEnvAnalogData;
            ucSize = sizeof( T_EnvAnalogDataStruct );
            break;
        case ALL_PART:
            GetAcAnalogData();
            GetEnvAnalogData();
            ptSource = ( char* )&s_tAnalogData;
            ucSize = sizeof( T_AnalogDataStruct );
            break;
        default:
            ptSource = ( char* )&s_tAnalogData;
            ucSize = 0;
            break;
    }
    rt_memcpy_s( pDest, ucSize, ptSource, ucSize );
    return;
}

/**
 * @description: 获取通用参数
 * @return {int} 成功返回SUCCESSFUL
 */
Static char GetCommonPara(void)
{
    int i = 0;

    get_one_para(ACMU_PARA_ID_SYS_BUZZER_SWITCH_OFFSET, &s_tSysPara.tCommonPara.ucBeepOn);
    get_one_para(ACMU_PARA_ID_HISDATA_SAVE_INTERVAL_OFFSET, &s_tSysPara.tCommonPara.ucHisDataInterval);
    get_one_para(ACMU_PARA_ID_SYS_LOCAL_MACHINE_ADDRESS_OFFSET, &s_tSysPara.tCommonPara.awDeviceAddr);

    for (i = 0; i < COMM_PORT_NUM; i++)
    {
        get_one_para(ACMU_PARA_ID_SYS_SERIAL_PORT_BAUD_RATE_SETTING_OFFSET + i, &s_tSysPara.tCommonPara.aucCOMRate[i]);
    }
    
    get_one_para(ACMU_PARA_ID_SYS_LANGUAGE_SETTING_OFFSET, &s_tSysPara.tCommonPara.ucLanguage);
    get_one_para(ACMU_PARA_ID_SYS_MENU_PASERWORD_SETTING_OFFSET, &s_tSysPara.tCommonPara.aucPaserword[0]);

    return SUCCESSFUL;
}

Static char GetCommonPara2(void)
{
    get_one_para(ACMU_PARA_ID_USAGE_SCENARIO_OFFSET, &s_tSysPara.tCommonPara2.usageScenario);
    get_one_para(ACMU_PARA_ID_AC_OUTPUT_MUTUAL_INDUCTANCE_TYPE_OFFSET, &s_tSysPara.tCommonPara2.awOutCurrTransType);
    return SUCCESSFUL;
}

/**
 * @description: 获取交流参数
 * @return {int} 成功返回SUCCESSFUL
 */
Static char GetAcPara(void)
{
    int i = 0;

    get_para(ACMU_PARA_ID_AC_PHASE_VOLTAGE_MAX_OFFSET, 0, &s_tSysPara.tAcPara.sAcVoltMax);
    get_para(ACMU_PARA_ID_AC_PHASE_VOLTAGE_MIN_OFFSET, 0, &s_tSysPara.tAcPara.sAcVoltMin);
    get_para(ACMU_PARA_ID_PHASE_VOLTAGE_UNBALANCE_OFFSET, 0, &s_tSysPara.tAcPara.sAcVolUnbalance);
    get_para(ACMU_PARA_ID_AC_IN_CURRENT_MAX_OFFSET, 0, &s_tSysPara.tAcPara.sAcCurrMax);
    get_para(ACMU_PARA_ID_AC_LINE_VOLTAGE_MAX_OFFSET, 0, &s_tSysPara.tAcPara.sAcVoltPPMax);
    get_para(ACMU_PARA_ID_AC_LINE_VOLTAGE_MIN_OFFSET, 0, &s_tSysPara.tAcPara.sAcVoltPPMin);

    for (i = 0; i < AC_PHASE_NUM; i++)
    {
        get_one_para(ACMU_PARA_ID_SYS_AC_MUTUAL_INDUCTANCE_CONFIGURATION_OFFSET + i, &s_tSysPara.tAcPara.ucCurrCfg[i]);
        get_one_para(ACMU_PARA_ID_AC_MUTUAL_INDUCTANCE_TYPE_OFFSET + i, &s_tSysPara.tAcPara.awCurrTransType[i]);
    }

    for (i = 0; i < SWITCH_NUM; i++)
    {
        get_one_para(ACMU_PARA_ID_SYS_AC_OUT_SWITCH_CONFIGURATION_OFFSET + i, &s_tSysPara.tAcPara.ucSwitchCfg[i]);
    }

    get_one_para(ACMU_PARA_ID_AC_METER_CONFIGURATION_OFFSET, &s_tSysPara.tAcPara.ucAcemCfg);

    return SUCCESSFUL;
}

Static char GetAdjustPara(void)
{
    int i = 0;
    for(i = 0; i < 3; i++) {
        get_one_para(ACMU_PARA_ID_AC_PHASE_VOLT_ZERO_OFFSET + i, &s_tSysPara.tAdjustPara.VoltageZeroPoint[i]);
        get_para(ACMU_PARA_ID_AC_PHASE_VOLT_SLOPE_OFFSET, i, &s_tSysPara.tAdjustPara.VoltageSlope[i]);
        get_one_para(ACMU_PARA_ID_AC_PHASE_CURR_ZERO_OFFSET + i, &s_tSysPara.tAdjustPara.CurrentZeroPoint[i]);
        get_para(ACMU_PARA_ID_AC_PHASE_CURR_SLOPE_OFFSET, i, &s_tSysPara.tAdjustPara.CurrentSlope[i]);
    }

    get_one_para(ACMU_PARA_ID_ENV_TEMP_ZERO_POINT_OFFSET, &s_tSysPara.tAdjustPara.EnvTempZeroPoint);
    get_one_para(ACMU_PARA_ID_ENV_HUMIDITY_ZERO_POINT_OFFSET, &s_tSysPara.tAdjustPara.EnvHumZeroPoint);
    
    return SUCCESSFUL;
}

/**
 * @description: 获取环境参数
 * @return {int} 成功返回SUCCESSFUL
 */
Static char GetEnvPara(void)
{
    get_para(ACMU_PARA_ID_TEMPERATURE_SENSOR_UPPER_OFFSET, 0, &s_tSysPara.tEnvPara.scEnvTempMax);
    get_para(ACMU_PARA_ID_TEMPERATURE_SENSOR_LOWER_OFFSET, 0, &s_tSysPara.tEnvPara.scEnvTempMin);
    get_para(ACMU_PARA_ID_HUMIDITY_SENSOR_UPPER_OFFSET, 0, &s_tSysPara.tEnvPara.scEnvHumMax);
    get_para(ACMU_PARA_ID_HUMIDITY_SENSOR_LOWER_OFFSET, 0, &s_tSysPara.tEnvPara.scEnvHumMin);
    return SUCCESSFUL;
}

/**
 * @description: 获取告警干节点参数
 * @return {int} 成功返回SUCCESSFUL
 */
Static char GetAlarmRelayPara(void)
{
    for(int i = 0; i < RELAY_NUM; i++) {
        get_one_para(ACMU_PARA_ID_INRELAYTTL_OFFSET + i, &s_tSysPara.tAlarmRelayPara.aucRlyAlarmTTL[i]);
        get_one_para(ACMU_PARA_ID_INRELAYNAME_OFFSET + i, s_tSysPara.tAlarmRelayPara.acRlyName[i]);
        get_alm_para(ACMU_ALM_ID_INPUT_RELAY1_ALARM_LEVEL + i, &s_tSysPara.tAlarmRelayPara.aucRlyAlarmGrade[i]);
    }


    return SUCCESSFUL;
}

/**
 * @description: 获取告警级别参数
 * @return {int} 成功返回SUCCESSFUL
 */

Static char GetAlarmGradePara(void)
{
    // 定义告警级别参数ID数组，按照指定顺序
    const int alarmGradeParaIds[] = {
        ACMU_ALM_ID_TOTAL_ALARM_LEVEL,          //总告警
        ACMU_ALM_ID_AC_POWER_OFF_LEVEL,         // 交流停电
        ACMU_ALM_ID_AC_PHASE_LOST_LEVEL,        // 交流缺相
        ACMU_ALM_ID_PHASE_VOLT_UNBALANCE_LEVEL, // 相电压不平衡
        ACMU_ALM_ID_PHASE_VOLT_HIGH_ALARM_LEVEL,// 相电压高
        ACMU_ALM_ID_PHASE_VOLT_LOW_ALARM_LEVEL, // 相电压低
        ACMU_ALM_ID_PHASE_CURR_HIGH_ALARM_LEVEL,         // 相电流高
        ACMU_ALM_ID_SPD_C_LEVEL,                // C级防雷器异常
        ACMU_ALM_ID_AC_OUTPUT_SWITCH_DISCONNECTION_LEVEL, // 输出空开断
        ACMU_ALM_ID_AC_LINE_VOLT_HIGH_LEVEL,    // 线电压高
        ACMU_ALM_ID_AC_LINE_VOLT_LOW_LEVEL,     // 线电压低
        ACMU_ALM_ID_AC_METER_DISCONNECTED_LEVEL,// 交流电表断
        ACMU_ALM_ID_ENV_TEMP_HIGH_ALARM_LEVEL,    // 环境温度高
        ACMU_ALM_ID_ENV_TEMP_LOW_ALARM_LEVEL,    // 环境温度低
        ACMU_ALM_ID_ENV_HUMIDITY_HIGH_ALARM_LEVEL,    // 环境湿度高告警
        ACMU_ALM_ID_ENV_HUMIDITY_LOW_ALARM_LEVEL,    // 环境湿度低告警
        ACMU_ALM_ID_FUMES_SENSOR_ALARM_LEVEL,    // 烟雾告警
        ACMU_ALM_ID_FLOOD_SENSOR_ALARM_LEVEL,    // 水淹告警
        ACMU_ALM_ID_DOORMAT_SENSOR_ALARM_LEVEL,    // 门磁告警
        ACMU_ALM_ID_ENV_TEMP_SENSOR_INVALID_ALARM_LEVEL,    // 环境温度失效
        ACMU_ALM_ID_ENV_HUMIDITY_SENSOR_INVALID_ALARM_LEVEL,// 环境湿度失效
        ACMU_ALM_ID_OIL_ENGINE_START_UP_ALARM_LEVEL,        // 油机启动告警
        ACMU_ALM_ID_INPUT_RELAY1_ALARM_LEVEL,
        ACMU_ALM_ID_INPUT_RELAY2_ALARM_LEVEL,
        ACMU_ALM_ID_INPUT_RELAY3_ALARM_LEVEL,
        ACMU_ALM_ID_INPUT_RELAY4_ALARM_LEVEL,
    };

    // 定义告警干接点参数ID数组，按照指定顺序
    const int alarmRelayParaIds[] = {
        ACMU_ALM_ID_TOTAL_ALARM_RELAY,          //总告警
        ACMU_ALM_ID_AC_POWER_OFF_RELAY,         // 交流停电
        ACMU_ALM_ID_AC_PHASE_LOST_RELAY,        // 交流缺相
        ACMU_ALM_ID_PHASE_VOLT_UNBALANCE_RELAY, // 相电压不平衡
        ACMU_ALM_ID_PHASE_VOLT_HIGH_ALARM_RELAY,// 相电压高
        ACMU_ALM_ID_PHASE_VOLT_LOW_ALARM_RELAY, // 相电压低
        ACMU_ALM_ID_PHASE_CURR_HIGH_ALARM_RELAY,         // 相电流高
        ACMU_ALM_ID_SPD_C_RELAY,                // C级防雷器异常
        ACMU_ALM_ID_AC_OUTPUT_SWITCH_DISCONNECTION_RELAY, // 输出空开断
        ACMU_ALM_ID_AC_LINE_VOLT_HIGH_RELAY,    // 线电压高
        ACMU_ALM_ID_AC_LINE_VOLT_LOW_RELAY,     // 线电压低
        ACMU_ALM_ID_AC_METER_DISCONNECTED_RELAY,// 交流电表断
        ACMU_ALM_ID_ENV_TEMP_HIGH_ALARM_RELAY,    // 环境温度高
        ACMU_ALM_ID_ENV_TEMP_LOW_ALARM_RELAY,    // 环境温度低
        ACMU_ALM_ID_ENV_HUMIDITY_HIGH_ALARM_RELAY,    // 环境湿度高告警
        ACMU_ALM_ID_ENV_HUMIDITY_LOW_ALARM_RELAY,    // 环境湿度低告警
        ACMU_ALM_ID_FUMES_SENSOR_ALARM_RELAY,    // 烟雾告警
        ACMU_ALM_ID_FLOOD_SENSOR_ALARM_RELAY,    // 水淹告警
        ACMU_ALM_ID_DOORMAT_SENSOR_ALARM_RELAY,    // 门磁告警
        ACMU_ALM_ID_ENV_TEMP_SENSOR_INVALID_ALARM_RELAY,    // 环境温度失效
        ACMU_ALM_ID_ENV_HUMIDITY_SENSOR_INVALID_ALARM_RELAY,// 环境湿度失效
        ACMU_ALM_ID_OIL_ENGINE_START_UP_ALARM_RELAY,        // 油机启动告警
        ACMU_ALM_ID_INPUT_RELAY1_ALARM_RELAY,
        ACMU_ALM_ID_INPUT_RELAY2_ALARM_RELAY,
        ACMU_ALM_ID_INPUT_RELAY3_ALARM_RELAY,
        ACMU_ALM_ID_INPUT_RELAY4_ALARM_RELAY,
    };

    // 获取告警级别参数,存到系统参数中
    for (int i = 0; i < sizeof(alarmGradeParaIds) / sizeof(alarmGradeParaIds[0]); i++) 
    {
        get_alm_para(alarmGradeParaIds[i], &s_tSysPara.tAlarmGradePara.aucAlmGrade[i]);
        get_alm_para(alarmRelayParaIds[i], &s_tSysPara.tAlarmGradePara.aucAlmOutRly[i]);
    }

    return SUCCESSFUL;
}



/**
 * @description: 获取系统参数
 * @param {unsigned char} ucParaType 参数类型
 * @param {char *} pDest 目标结构体变量首地址
 * @return {*}
 */
unsigned char GetSysPara( unsigned char ucParaType, char * pDest )
{
    char * ptSource;
    unsigned char ucSize;
    T_SysPara* pDestSysPara = (T_SysPara* )pDest;

    RETURN_VAL_IF_FAIL(pDest != NULL, FAILURE);

    switch ( ucParaType )
    {
        case PARATYPE_SYSTEM:
            GetCommonPara();
            ptSource = ( char* )&s_tSysPara.tCommonPara;
            ucSize = sizeof( T_CommonParaStruct );
            rt_memcpy_s((char*)&pDestSysPara->tCommonPara, ucSize, ptSource, ucSize );
            GetCommonPara2();
            ptSource = ( char* )&s_tSysPara.tCommonPara2;
            ucSize = sizeof( T_CommonParaStruct2 );
            rt_memcpy_s((char*)&pDestSysPara->tCommonPara2, ucSize, ptSource, ucSize );
            // 交流参数获取，解决交流电表配置参数重启后显示为无
            GetAcPara();
            ptSource = ( char* )&s_tSysPara.tAcPara;
            ucSize = sizeof( T_AcParaStruct );
            rt_memcpy_s((char*)&pDestSysPara->tAcPara, ucSize, ptSource, ucSize );
            return SUCCESSFUL;
        case PARATYPE_AC:
            GetAcPara();
            ptSource = ( char* )&s_tSysPara.tAcPara;
            ucSize = sizeof( T_AcParaStruct );
            rt_memcpy_s( (char*)&pDestSysPara->tAcPara, ucSize, ptSource, ucSize );
            break;
        case PARATYPE_ADJUST:
            GetAdjustPara();
            ptSource = ( char* )&s_tSysPara.tAdjustPara;
            ucSize = sizeof( T_AdjustParaStruct );
            rt_memcpy_s( (char*)&pDestSysPara->tAdjustPara, ucSize, ptSource, ucSize );
            break;
        case PARATYPE_ENV:
            GetEnvPara();
            ptSource = ( char* )&s_tSysPara.tEnvPara;
            ucSize = sizeof( T_EnvParaStruct );
            rt_memcpy_s( (char*)&pDestSysPara->tEnvPara, ucSize, ptSource, ucSize );
            break;
        case PARATYPE_ALARM_RELAY:
            GetAlarmRelayPara();
            ptSource = ( char* )&s_tSysPara.tAlarmRelayPara;
            ucSize = sizeof( T_AlarmRelayParaStruct );
            rt_memcpy_s( (char*)&pDestSysPara->tAlarmRelayPara, ucSize, ptSource, ucSize );
            break;
        case PARATYPE_ALARM_GRADE:
            GetAlarmGradePara();
            ptSource = ( char* )&s_tSysPara.tAlarmGradePara;
            ucSize = sizeof( T_AlarmGradeParaStruct );
            rt_memcpy_s( (char*)&pDestSysPara->tAlarmGradePara, ucSize, ptSource, ucSize );
            break;
        case PARATYPE_ALL:
            GetCommonPara();
            GetCommonPara2();
            GetAcPara();
            GetAdjustPara();
            GetEnvPara();
            GetAlarmRelayPara();
            GetAlarmGradePara();
            ptSource = ( char* )&s_tSysPara;
            ucSize = sizeof( T_SysPara );
            rt_memcpy_s( pDest, ucSize, ptSource, ucSize );
            break;
        default:
            break;
    }
    return SUCCESSFUL;
}


char GetPara(T_SysPara *ptPara, MIBTable_ParaInstance *ptInstance) {
    if (ptPara == NULL || ptInstance == NULL) {
        return FAILURE;
    }

    char adjust_para_data = 0;
    get_one_para(ptInstance->tParaNode.wParaOffsetId, &adjust_para_data);

    return adjust_para_data;
}

int GetPeakData(char* pDest) {
    T_PeakDataStruct tPeakData;
    his_extreme_data_t extreme_data;
    int ret = SUCCESSFUL;

    RETURN_VAL_IF_FAIL(pDest != NULL, FAILURE);

    // 初始化结构体
    rt_memcpy_s( &tPeakData, sizeof(T_PeakDataStruct), 0, sizeof(T_PeakDataStruct) );
    rt_memcpy_s( &extreme_data, sizeof(his_extreme_data_t), 0, sizeof(his_extreme_data_t) );

    // 读取历史极值数据
    ret = pub_hisrecord_read_msg(RECORD_TYPE_EXTREME_DATA, 1, 0, &extreme_data);

    RETURN_VAL_IF_FAIL(ret == SUCCESSFUL, FAILURE);
       
    // 填充交流电压最大值
    tPeakData.tMaxAcVolt.iVolt = extreme_data.phase_volt_max;
    for (int i = 0; i < AC_PHASE_NUM; i++) {
        tPeakData.tMaxAcVolt.aiVolt[i] = extreme_data.a_phase_volt_max[i];
    }

    time_t_to_timestruct(extreme_data.phase_volt_max_time, &tPeakData.tMaxAcVolt.tTime);

    // 填充交流电压最小值
    tPeakData.tMinAcVolt.iVolt = extreme_data.phase_volt_min;
    for (int i = 0; i <AC_PHASE_NUM; i++) {
        tPeakData.tMinAcVolt.aiVolt[i] = extreme_data.a_phase_volt_min[i];
    }
    time_t_to_timestruct(extreme_data.phase_volt_min_time, &tPeakData.tMinAcVolt.tTime);

    // 填充环境温度最大值
    tPeakData.tMaxEnvTemp.scEnvTemp = extreme_data.env_temp_max;
    time_t_to_timestruct(extreme_data.env_temp_max_time, &tPeakData.tMaxEnvTemp.tTime);

    // 填充环境温度最小值
    tPeakData.tMinEnvTemp.scEnvTemp = extreme_data.env_temp_min;
    time_t_to_timestruct(extreme_data.env_temp_min_time, &tPeakData.tMinEnvTemp.tTime);

    // 复制到目标缓冲区
    rt_memcpy_s(pDest, sizeof(T_PeakDataStruct), &tPeakData, sizeof(T_PeakDataStruct));
    return SUCCESSFUL;
}


/**
 * @description: 获取历史事件记录数量
 * @return {unsigned short} 保存数目
 */
unsigned short GetHisEventNum( void ){
    unsigned short saved_num = 0;

    pub_get_saved_record_num_msg(RECORD_TYPE_HIS_ACTION, &saved_num);
    return saved_num;
}

/**
 * @description: 获取历史告警数量
 * @return {unsigned short} 保存数目
 */
unsigned short GetHisAlmNum( void ){
    unsigned short saved_num = 0;

    pub_get_saved_record_num_msg(RECORD_TYPE_HIS_ALARM, &saved_num);
    return saved_num;
}

/**
 * @description: 
 * @param {unsigned short} index
 * @param {T_EventRecord*} dest
 * @return {*}
 */
short GetDisHisEvent(unsigned short index, T_EventRecord* dest)
{
    his_action_record_info his_action_record;
    T_EventRecord his_event;
    int ret = SUCCESSFUL;

    RETURN_VAL_IF_FAIL(dest != NULL, FAILURE);

    rt_memset_s(&his_action_record, sizeof(his_action_record_info), 0, sizeof(his_action_record_info));
    rt_memset_s(&his_event, sizeof(T_EventRecord), 0, sizeof(T_EventRecord));
    
    RETURN_VAL_IF_FAIL(index < MAX_HIS_ACTION_NUM, FAILURE);

    ret = pub_hisrecord_read_msg(RECORD_TYPE_HIS_ACTION, 1, index, &his_action_record);

    RETURN_VAL_IF_FAIL(ret == SUCCESSFUL, FAILURE);

    his_event.ucID1 = his_action_record.id1;
    his_event.ucID2 = his_action_record.id2;
    his_event.ucIndex = his_action_record.index;
    time_t_to_timestruct(his_action_record.save_time, &his_event.tTime);
    rt_memcpy_s(his_event.aucMsg, sizeof(his_event.aucMsg), his_action_record.msg, sizeof(his_action_record.msg));

    rt_memcpy_s(dest, sizeof(T_EventRecord), &his_event, sizeof(his_event));

    return SUCCESSFUL;
}

/**
 * @description: 
 * @param {unsigned short} index 告警偏移
 * @param {T_HisAlarmStruct*} dest 出参
 * @param {MIB_AlarmDataNode*} alarm_node 出参
 * @return {short} 成功SUCCESSFUL；失败FAILURE
 */
short GetDisHisAlarmAndNode( unsigned short index, T_HisAlarmStruct* dest, MIB_AlarmDataNode* alarm_node)
{
    his_alarm_info_t his_alarm_record;
    T_HisAlarmStruct his_alarm;
    MIB_AlarmDataNode  alarm_data_node;
    int ret = SUCCESSFUL;
    alarm_map_t* alarm_map = NULL;

    RETURN_VAL_IF_FAIL(dest != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(alarm_node != NULL, FAILURE);

    rt_memset_s(&his_alarm_record, sizeof(his_alarm_record), 0, sizeof(his_alarm_record));
    rt_memset_s(&his_alarm, sizeof(T_HisAlarmStruct), 0, sizeof(T_HisAlarmStruct));
    rt_memset_s(&alarm_data_node, sizeof(alarm_data_node), 0, sizeof(alarm_data_node));
    
    RETURN_VAL_IF_FAIL(index < MAX_HIS_ALARM_NUM, FAILURE);

    ret = pub_hisrecord_read_msg(RECORD_TYPE_HIS_ALARM, 1, index, &his_alarm_record);

    RETURN_VAL_IF_FAIL(ret == SUCCESSFUL, FAILURE);
    
    ret = get_alarm_sn(his_alarm_record.alarm_id, &alarm_map);

    RETURN_VAL_IF_FAIL(ret != FAILURE, FAILURE);
    
    his_alarm.ucAlarmSn = ret;
    his_alarm.ucIDIndex = his_alarm_record.index;
    his_alarm.ucAlarmState = his_alarm_record.alarm_state;
    time_t_to_timestruct(his_alarm_record.start_time, &his_alarm.tStartTime);
    time_t_to_timestruct(his_alarm_record.end_time, &his_alarm.tEndTime);

    rt_memcpy_s(dest, sizeof(T_HisAlarmStruct), &his_alarm, sizeof(his_alarm));

    alarm_data_node.ucID1 = alarm_map->id1;
    alarm_data_node.ucID2 = alarm_map->id2;
    alarm_data_node.ucIDNumber = alarm_map->number;

    rt_memcpy_s(alarm_node, sizeof(MIB_AlarmDataNode), &alarm_data_node, sizeof(alarm_data_node));
    return SUCCESSFUL;
}


unsigned char CalRealAlarmTotal(void)
{
    return (unsigned char)get_realtime_alarm_count();
}



T_RealAlarmStruct * GetDisRealAlarm(unsigned char ucItem)
{
    static T_RealAlarmStruct real_alarm_data; // 使用静态变量
    alarm_map_t* alarm_map_info = NULL;
    real_alarm_info_t real_alarm;

    if (get_nth_realtime_alarm_info(&real_alarm, ucItem) == FAILURE) {
        return NULL;
    }

    real_alarm_data.ucAlarmSn = get_alarm_sn(ALM_ID_GET_ALM_CODE(real_alarm.alm_id), &alarm_map_info);
    real_alarm_data.ucIDIndex = ALM_ID_GET_DEV(real_alarm.alm_id);
    real_alarm_data.ucAlarmState = real_alarm.alm_level;
    time_t_to_timestruct(real_alarm.start_time, &real_alarm_data.tStartTime);

    if (real_alarm_data.ucAlarmState == ALARM_LEVEL_NONE) {
        return NULL;
    }

    return &real_alarm_data;
}


unsigned char get_precision(unsigned char raw_type, unsigned short para_id_offset_base) {
    numeric_para_attr_t* para_attr = NULL;
    unsigned short para_offset = GET_SYS_PARA_OFFSET(para_id_offset_base);
    unsigned char precision = 0;
    switch (raw_type) {
        case TYPE_FLOAT:
            // 获取参数属性表
            RETURN_VAL_IF_FAIL(para_offset <= SYS_NUMERIC_PARA_MAX_OFFSET, 0);
            para_attr = get_numeric_para_attr_tab();
            RETURN_VAL_IF_FAIL(para_attr != NULL, FAILURE);
            precision = para_attr[para_offset].precision;
            break;
        default:
            break;
    }
    return precision;
}


Static int get_para(unsigned short para_id_offset_base, unsigned char index, void* pSysPara) {
    float temp = 0;
    u_value raw_val = {0};
    MIBTable_ParaNode* ptParaNode = NULL;
    unsigned short para_id_offset = para_id_offset_base + index;
    unsigned char ucRawType = GET_SYS_PARA_DATA_TYPE(para_id_offset);
    unsigned char precision = 0;

    // 参数有效性检查
    RETURN_VAL_IF_FAIL(pSysPara != NULL, FAILURE);

    precision = get_precision(ucRawType, para_id_offset_base);

    // 获取参数节点
    ptParaNode = GetParaNodeByParaId(para_id_offset_base);
    RETURN_VAL_IF_FAIL(ptParaNode != NULL, FAILURE);

    switch (ucRawType) {
        case TYPE_STRING:
        case TYPE_INT16S:
        case TYPE_INT16U:
        case TYPE_INT8S:
        case TYPE_INT8U:
            get_one_para(para_id_offset, pSysPara);
            return SUCCESSFUL;
        case TYPE_FLOAT:
            get_one_para(para_id_offset, &raw_val.f_val);
            temp = raw_val.f_val * pow(10, precision);
            break;
        default:
            return FAILURE;
    }

    switch (ptParaNode->ucDataType) {
        case TYPE_INT8U:
            *(unsigned char*)pSysPara = (unsigned char)temp;
            break;
        case TYPE_INT8S:
            *(signed char*)pSysPara = (signed char)temp;
            break;
        case TYPE_INT16S:
            *(signed short*)pSysPara = (signed short)temp;
            break;
        case TYPE_INT16U:
            *(unsigned short*)pSysPara = (unsigned short)temp;
            break;
        default:
            return FAILURE;
    }

    return SUCCESSFUL;
}

void    GetCtrlOut( T_CtrlOutStruct * tCtrlOut )
{
    unsigned char i = 0;
    unsigned char status = 0;
    for(i = 0; i < OUT_RELAY_MAX_NUM; i ++)
    {
        get_one_data(ACMU_DATA_ID_OUTRELAY_MANUAL_CONTROL_STATUS + i, &status);
        if(status)
        {
            tCtrlOut->wRelayOut |= 0x0001 << i;
        }
        tCtrlOut->aucRelay[i] = status;
    }
    return;    
}

void    SetCtrlOut( T_CtrlOutStruct * tCtrlOut )
{
    unsigned char i = 0;
    for(i = 0; i < OUT_RELAY_MAX_NUM; i ++)
    {
        if(tCtrlOut->aucRelay[i] == CTRL_ON)
        {
            control_out_relay(i, DO_ACTIVE);
        }
        else if(tCtrlOut->aucRelay[i] == CTRL_OFF)
        {
            control_out_relay(i, DO_NOT_ACTIVE);
        }
        set_one_data(ACMU_DATA_ID_OUTRELAY_MANUAL_CONTROL_STATUS + i, &tCtrlOut->aucRelay[i]);
    }
    return;
}

unsigned char clear_ctrl_out(void)
{
    unsigned char i = 0;
    unsigned char manual_control_status = CTRL_NULL;
    unsigned char manual_control_mode = 0;
    for(i = 0; i < OUT_RELAY_MAX_NUM; i ++)
    {
        control_out_relay(i, DO_NOT_ACTIVE);
        set_one_data(ACMU_DATA_ID_OUTRELAY_MANUAL_CONTROL_STATUS + i, &manual_control_status);
    }
    set_one_data(ACMU_DATA_ID_OUTRELAY_MANUAL_CONTROL_MODE, &manual_control_mode);
    return SUCCESSFUL;     
}
