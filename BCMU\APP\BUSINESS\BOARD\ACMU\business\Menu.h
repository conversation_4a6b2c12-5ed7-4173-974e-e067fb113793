/**************************************************************************
* 版权信息：（C）2008-2009，中兴通讯股份有限公司动力开发部版权所有
* 系统名称：ZXDU88 S402 ACMU CORE板软件
* 文件名称：menu.h
* 文件说明：菜单模块头文件
* 作    者：潘奇银
* 版本信息：V1.0
* 设计日期：2008-09-12
* 修改记录：
* 日    期		版	本		修改人		修改摘要      
* 其他说明：
***************************************************************************/

#ifndef _MENU_H
#define _MENU_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "temp.h"

/***********************  常量定义  ************************/

#define     MAX_HELP_MSG    21
#define     MAX_ADJPARA_SET    8
#define     MAX_TRACE_PAGE  3
#define	LEN_EDITCHAR	68
#define EDITDIR_INC          0
#define EDITDIR_DEC         1

#define WND_MAX         (WND_CTRL_RELAY + 1)
#define KEY_PAINT           0x80

#define CURRCTRL			0
#define CURRPAGE			1

#define WND_TYPE_DIALOG     0
#define WND_TYPE_PAGE       1
#define WND_TYPE_EDIT_LINE  2
#define WND_TYPE_EDIT_INT   3
#define WND_TYPE_EDIT_FLOAT 4
#define WND_TYPE_OTHERS     5

#define MSG_SRC_KEY         0
#define MSG_SRC_CHILD       1
#define MSG_SRC_PARENT      2
#define PAGE_ITEMS	3

#define SCENE_SUPPORTING     0     /// 配套场景
#define SCENE_INDEPENDENT    1     /// 独立场景

// 该序号为alarm_map中输入干接点索引
#define INPUT_RELAY1_INDEX 22

enum
{
	NO_CONFIG,
	MAINOUTPUT_CONFIG,
	MAININTPUT_CONFIG
};

/*#define STR_DOWNLOAD                    0
#define STR_WND_MAIN                    2
#define STR_WND_DATA                    10
#define STR_WND_TIME                    18
#define STR_WND_VER                     23
#define STR_WND_SWITCH                  28
#define STR_WND_CONSOLE                 36
#define STR_WND_PARA                    53
#define STR_WND_HISTORY                 117
#define STR_WND_LIST                    125
#define STR_WND_SUPER                   145
#define STR_WND_PEAK                    150
#define	STR_CLASS_ALARM                 158
#define STR_WND_REC_ACT                 185
#define STR_WND_HELP                    196
*/
//������
/*#define STR_DOWNLOAD                    0
#define STR_WND_MAIN                    2
#define STR_WND_DATA                    10
#define STR_WND_TIME                    18
#define STR_WND_VER                     23
#define STR_WND_SWITCH                  28
#define STR_WND_CONSOLE                 36
#define STR_WND_PARA                    53
#define STR_WND_HISTORY                 117
#define STR_WND_LIST                    125
#define STR_WND_SUPER                   145
#define STR_WND_PEAK                    150
#define	STR_CLASS_ALARM                 158
#define STR_WND_REC_ACT                 185
#define STR_WND_HELP                    196*/

#define STR_DOWNLOAD                    0
#define STR_WND_MAIN                    2
#define STR_WND_DATA                    10
#define STR_WND_TIME                    28
#define STR_WND_VER                     33
#define STR_WND_SWITCH                  38
#define STR_WND_CONSOLE                 46
#define STR_WND_PARA                    63
#define STR_WND_HISTORY                 128
#define STR_WND_LIST                    136
#define STR_WND_SUPER                   158
#define STR_WND_PEAK                    163
#define	STR_CLASS_ALARM                 171
#define STR_WND_REC_ACT                 199
#define STR_WND_HELP                    220


#define ALIGN_CUSTOM           0
#define ALIGN_MIIDLE             1

#define LIST_MAX             6

#define PAGES_PER_SMR		4	// 每个整流器的实时数据页数
/*********************  数据结构定义  **********************/
typedef	struct WndStruct 
{
	unsigned char 		ucID;			// 本窗口ID
	unsigned char 		ucIDParent;		// 父窗口ID
	unsigned char 		ucIDChild;		// 子窗口ID
	unsigned char	    bActive;		// 是否激活窗口
	unsigned char	    bFocus;		    // 是否焦点窗口
	unsigned char 		ucType;		    // 窗口类型(属性页、对话框、编辑控件、列表控件等)
	unsigned char 		ucStyle;		// 窗口风格(正常、反显、闪烁、禁用等)
	unsigned char 		ucMsgChild;	    // 子窗口回传的消息，即反射消息
	unsigned char 		ucMsgParent;	// 父窗口传递的消息
	unsigned char 		ucMsgKey;		// 键盘消息
	unsigned char		ucKeyAccel;		// 加速键定义
	unsigned char     	bInit;          // 是否初始化
	unsigned char     	bPartial;       // 是否部分窗口

	unsigned int 		wTotalPage;	// 显示页总数
	unsigned int 		wCurPage;		// 当前显示页
	
	
	unsigned char 		ucTotalCtrl;	// 对话框控件总数
	unsigned char 		ucCurCtrl;		// 对话框当前焦点控件

	void 		(*p)(void);			// 执行程序
	
}T_Wnd;

//控件类型定义
typedef struct 
{
    unsigned char    ucStyle;
    char   pszText[40];
}T_Label;

//控件类型定义
typedef struct 
{
    char acText1[40];
    char acText2[40];
}T_AlertText;

typedef struct 
{
	char *  pucParaPtr;
	unsigned char    ucIndex;
	char    acListStr[LIST_MAX][20];    
}T_List;

typedef struct 
{
	unsigned char    ucRange;
	unsigned char    x;
	unsigned char    y;
	char    acEditStr[20];            

	unsigned char	ucAttrib;
	int   iMax;
	int   iMin;
    short iData;
	char  scDelt;
	char  scPrecision;
	char  *pcParaPtr;
    short *piParaPtr;
	char    acParaStr[20];
}T_Edit;    

/*********************  函数原型定义  **********************/
unsigned char   	GetFocusWndID( void );
void	MainMenu( void );
void    RealDataDlg( void );
void    VersionDlg( void );
void	RealAlarmDlg( void );
void 	TraceDlg( void );
void    ParseDlg( void );
void    ParaMenu( void );
void    AdjustParaMenu( void );
void    SysParaMenu( void );
void	ControlMenu( void );
void    ListBoxCtrl( void );
void    EditLineCtrl( void );
void    QueryDlg( void );
void	EditIntCtrl( void );
void    TimeEditMenu( void );
void    AlmAttributeMenu( void );
void    CtrlRelayDlg( void );
void    MessageBox( void );
void   ConfirmDlg( void );
void   AlertDlg( void );
void    StrEditMenu( void );
void    ConsoleMenu( void );
void    DeleteDlg( void );
void    RestoreParaDlg( void );
//void    DownloadDlg( void );
void    OutSwitchEditMenu( void );
void    SaveScreenDlg( void );
void    HelpDlg( void );
void    SwitchMenu( void );
void    HistoryMenu( void );
void    HisAlarmDlg( void );
void    HisEventDlg( void );
void    HisPeakDlg( void );
void	ProcessKey( void );
T_Wnd  	* GetWnd( unsigned char uWndId );
void	SetFocusWnd(unsigned char uWndId);
void   	PostParentItemMsg( unsigned char ucIDItem );
int GetEditLineText(char * buff, unsigned int buf_size);
void    DisplayWelcome( void );
void    DeleteHisEvent( void );
unsigned char SetTraceStr(unsigned char ucId, char * pcStr );
unsigned char GetHisEvent( unsigned char ucPort, char *pDest ,unsigned char * bLastRecord);
void	AdjHisEventTail( unsigned char ucPort, unsigned char bReSend );
void    InRelayEditMenu( void );
void	CheckHisEvent( void );
unsigned char SaveCurrentPara( void );
unsigned char get_scene_from_para(void);
rt_int16_t GetParaMax(MIBTable_ParaInstance* ptrParaInstance);
rt_int16_t GetParaMin(MIBTable_ParaInstance* ptrParaInstance);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _MENU_H

