#ifndef _APP_UPDATE_SOLAR_H
#define _APP_UPDATE_SOLAR_H

#ifdef __cplusplus
extern "C" {
#endif

#include "storage.h"
#include "device_type.h"
#include "net.h"
#include "utils_uuid.h"

#define DOWNLOAD_UPDATE_FIRST_FRAME_LEN         70
#define DOWNLOAD_UPDATE_FIRST_EXTEND_FRAME_LEN  290

#define UPDATE_FILE_EXTEND_NAME_LEN             256
#define UPDATE_FILE_NAME_LEN                    40
#define UPDATE_FILE_TIME_LEN                    20

#define VER_FILE_NAME_LEN                       20
#define VER_FILE_LEN                            20

#define DOWNLOAD_FIRST_FRAME_ERR                 2
#define DOWNLOAD_FRAME_ERR                       1
#define DOWNLOAD_FRAME_CORRECT                   0

#define SINGLE_FRAME_MAX_LENGTH_MAX              1024
#define SAVE_UPDATE_DATA_INDEX                   10
#define DOWNLOAD_TRIG_TIMES                      3
#define READ_BYTES                               256
#define MAC_DATA_LEN                             20
#define TRIG_TIMEOUT                             30
#define MSC_MAX_PACKET                           1024
#define UPLOAD_DATA_FRAME_LEN                    1024
#define UPLOAD_FILE_NAME_MAX_LEN                 40       //文件名最大长度
#define UPLOAD_FILE_NUM                          20        //文件列表数
#define UPLOAD_FILE_TIME_LEN                     20       //上传文件时间长度，协议定的20

#define  FLAG_NO_UPDATE                         0
#define  FLAG_APP_UPDATE_APP                    1

#define MAX_INFO_LEN    20

#define MQTT_SERVER_NUM   2      // mqtt服务端数量
#define CERT_FILE_NUM     3      // 加密证书文件数量               
typedef enum
{
    DEVICE_NO_UPDATE = 0,
    DEVICE_UPDATING,
}pv_update_status_t;

typedef enum
{
    MAIN_CTRL_TYPE=0,
    AUXI_CTRL_TYPE,
    CPLD_CTRL_TYPE,
    INVALID_DEV_TYPE,
}south_dev_type;

typedef enum
{
    PARA_EXPORT=0,
    PARA_IMPORT,
}para_file_operation;

typedef enum
{
    UPDATE_NO_START=0,
    MONITER_UPDATE_SUCCESS=1,
    MONITER_UPDATE_FAILED=2,
    TRIG_FAILED=3,
    NORTH_TRANS_FAILED=4,
    NORTH_TRANS_SUCCESS=5,
    SOUTH_TRANS_FAILED=6,
    SOUTH_TRANS_SUCCESS=7,
    SOUTH_UPDATE_FAILED=8,
    SOUTH_UPDATE_SUCCESS=9,
    CERT_UPDATE_SUCCESS=10,
    WIFI_UPDATE_SUCCESS=11,
    PARA_UPDATE_SUCCESS=12,
    PARA_UPDATE_FAILED=13,
}update_sta_code;


typedef enum{
    OTHER_NO_UPDATE=0,
    CERT_UPDATE,
    WIFI_UPDATE,
    PARA_UPDATE,
}OtherUpdateType;


#pragma pack(1)
// 升级状态消息实体
typedef struct {
    unsigned int  data_s;
    short         south_dev;
    char          info[MAX_INFO_LEN];
}update_sta_msg_t;
#pragma pack()

typedef enum
{
    NO_UPDATEING  = 0,
    UPDATE_SUCCESS,
    UPDATE_FAILURE,
    UPDATEING,
    DIFF_UPDATE_FAILURE,
    TRANSFER_SUCCESS,
    TRANSFER_FAILURE,
    UPDATE_END,
}updata_status;

typedef enum {
    UPDATE_SOUTH_REMOTE = 0, // 远程升级功率
    UPDATE_PV_REMOTE,        // 远程升级监控
    UPDATE_MQTT,             // mqtt升级
    UPDATE_MODE_MAX,
}update_mode_t;

typedef struct
{
    rt_bool_t bSysRunOk;    // 系统运行正常
    rt_bool_t bBackUpOk;    // 备份完成
    rt_bool_t bUpdateReq;   // 是否升级 - 给 boot 使用
}UpdateFlag_t;

typedef struct{
    int trig_success;       ///<  触发成功
    int trig_times;         ///<  触发次数
    int slave_trig_success; ///<  从机触发成功
    int slave_trig_times;   ///<  从机触发次数
}download_trig_ctr_inf_t;

typedef struct{
    int trig_success;       ///<  触发成功
    int trig_times;         ///<  触发次数
}upload_trig_ctr_inf_t;

typedef struct{
    char file_name[64];         ///<  文件名称
    char dev_type[32];          ///<  设备类型
    int  file_size;             ///<  文件大小
    unsigned int check_sum;           ///<  校验和
}file_head_t;

#pragma pack(1)
typedef struct
{
    char   file_time[UPDATE_FILE_TIME_LEN];             ///< 下载时间
    char   file_name[UPDATE_FILE_EXTEND_NAME_LEN];      ///< 文件名称
    unsigned char   param_type;                                  ///< 参数类型
    unsigned int    total_leng;                                 ///< 文件总长度
    unsigned short  total_frames;                                ///< 总帧数量int
    unsigned int    filecrc;                                    ///< 文件CRC
    unsigned short  resv;
}update_file_attr_t;

typedef struct
{
    update_file_attr_t     file_info;
    unsigned char   sys_run_flag;
    unsigned char   backup_flag;
    unsigned char   update_flag;
    unsigned char   count;                                        ///控制boot侧升级逻辑的count
    unsigned char   rtn;  
    unsigned char   update_status;   
    unsigned int    data_offset;                                 ///<  文件存储偏移
    unsigned int    backup_frame_leng;                                
    unsigned short  cur_frame_no;
    unsigned short  restore_frame_num;
    unsigned short  crc;
}update_file_manage_t;
#pragma pack()

/* 上传的列表信息 */
typedef struct
{
    unsigned short wListTotalFrame;  //列表总帧数
    unsigned char ucListTotalNum;    //列表总个数
    unsigned char aucListNameLen[UPLOAD_FILE_NAME_MAX_LEN];    //列表中的文件名长度
    char *pucListName[UPLOAD_FILE_NUM];               //列表中的文件名
}T_FileListStruct;

/* 上位机请求的文件信息 */
typedef struct
{
    unsigned short wReqFileNameLen;
    char ucReqFileName[UPLOAD_FILE_NAME_MAX_LEN];
}T_FileNameStruct;

/* 上传的文件信息 */
typedef struct
{
    T_FileNameStruct tFileName;
    T_FileListStruct tFileList;
    unsigned short cur_frame_no;
    unsigned short wFileTotalFrame;
    unsigned int uFileTotalLen;
    unsigned char  rtn;
    unsigned char ucFileTime[UPLOAD_FILE_TIME_LEN];
    unsigned int uCrc; 
}T_FileUploadStruct;

void  update_download_init(void);
void write_download_tmpInfo(update_file_manage_t* tFileManage);
void read_download_tmpInfo(update_file_manage_t* tFileManage);
void erase_download_tmpInfo();
int pack_filedata_other_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no);
const char (*get_south_update_file_names(int *length))[UPDATE_FILE_EXTEND_NAME_LEN];
void is_need_restart();
void trigTimer_timeout();
void time_out_ctrl_update();
void trig_end();
int south_update_dev_cmp(const char* file_name);
int judge_cert_trans_complete(update_file_manage_t* file_manage);
int judge_one_cert_trans_complete(update_file_manage_t* file_manage,int server_index);
void update_trans_failed();
void upload_file_end();
void send_update_status(const char* info, int data_s);
int is_trig_success();
int is_first_frame_deal(unsigned short frm_no, cmd_buf_t* tran_cmd_buf);
int is_expect_date_frame_no(unsigned short frm_no, update_file_manage_t* trsdata_ctr_inf);
int erase_flash_in_first_frame(update_file_manage_t* trsdata_ctr_inf, part_data_t part_data);
void deal_last_frame(unsigned short frm_no, update_file_manage_t* trsdata_ctr_inf, cmd_buf_t *cmd_buf, void* dev_inst);
int is_para_file_deal();
int judge_update_by_file_name(const char (*file_names)[UPDATE_FILE_EXTEND_NAME_LEN], int file_name_num, const char *file_name);
int south_update_deal(update_file_manage_t* trsdata_ctr_inf);
int deal_concur_update(void* file_info, void* cmd_buf, void* dev_inst);
int save_data_to_diff_part(update_file_attr_t* file_info);
int self_update_deal(update_file_manage_t* trsdata_ctr_inf, cmd_buf_t *cmd_buf, void* dev_inst);
#ifdef __cplusplus
}
#endif

#endif  // __APP_UPDATE_SOLAR_H
