#ifndef _TOV_HIS_RECORD_H
#define _TOV_HIS_RECORD_H

#ifdef __cplusplus
extern "C" {
#endif

#include "msg.h"

#define MAX_POINT_NUM_PER_CHANNEL              3000
#define MAX_POINT_NUM_PER_CHANNEL_BUFF_SIZE    (MAX_POINT_NUM_PER_CHANNEL * 2)
#define SIGLE_RECORD_DATA_MAX_LEN    (35 * 1024)
//故障录波
#define MAX_RECORD_FAULT_NUM           320
#define MIN_RECORD_FAULT_NUM           240
#define MAX_RECORD_FAULT_NUM_PER_FILE  80
#define RECORD_FAULT_FILE_NUM          ((MAX_RECORD_FAULT_NUM + MAX_RECORD_FAULT_NUM_PER_FILE - 1) / MAX_RECORD_FAULT_NUM_PER_FILE)

#define MAX_RECORD_NAME_LEN           32

#define MAX_AC_SAMPLE_NUM 8
#define MAX_DC_SAMPLE_NUM 2

#define EXTREME_DATA_LEN           240
#define SIGLE_EXTREME_DATA_LEN     24
#define EXTREME_SAMPLE_ID_INDEX    0
#define EXTREME_MAX_TIME_INDEX     2
#define EXTREME_MAX_INDEX          11
#define EXTREME_MIN_TIME_INDEX     13
#define EXTREME_MIN_INDEX          22
#define FALUT_MAX_VALUE    4096

#define AC_TOTAL_TIME     20
#define DC_TOTAL_TIME     5

#define INVALID_POINT     0
#define DC_VOL_POINT      9
#define DC_CURR_POINT     10




#define POWER_CAUSE_AC_T0 15
#define POWER_CAUSE_AC_T1 5
#define POWER_CAUSE_DC_T0 1
#define POWER_CAUSE_DC_T1 4


#pragma pack(push, 1)
// 定义触发原因枚举类型，使用明确的命名和注释以提高可读性

typedef enum {
    AC_A_HIGH_VOL_CAUSE = 0xF1,      ///< AC A相过压
    AC_A_LOW_VOL_CAUSE,              ///< AC A相欠压
    AC_B_HIGH_VOL_CAUSE,             ///< AC B相过压
    AC_B_LOW_VOL_CAUSE,              ///< AC B相欠压
    AC_C_HIGH_VOL_CAUSE,             ///< AC C相过压
    AC_C_LOW_VOL_CAUSE,              ///< AC C相欠压
    AC_N_HIGH_VOL_CAUSE,             ///< AC 中性线过压
    AC_A_HIGH_CUR_CAUSE,             ///< AC A相过流
    AC_B_HIGH_CUR_CAUSE,             ///< AC B相过流
    AC_C_HIGH_CUR_CAUSE,             ///< AC C相过流
    AC_N_HIGH_CUR_CAUSE,             ///< AC 中性线过流
    DC_HIGH_VOL_CAUSE,               ///< DC 过压
    DC_LOW_VOL_CAUSE,                ///< DC 欠压
    DC_HIGH_CUR_CAUSE,               ///< DC 过流
    POWER_DOWN_CAUSE,           ///< 电源关闭异常
    MAX_TRIG_CAUSE              ///< 最大触发原因
} trig_cause_e;



typedef enum {
    AC_A_VOL_ID = 0x101,     ///< AC A相电压
    AC_B_VOL_ID,             ///< AC B相电压
    AC_C_VOL_ID,             ///< AC C相电压
    AC_N_VOL_ID,             ///< AC 中性线电压
    AC_A_CUR_ID,             ///< AC A相电流
    AC_B_CUR_ID,             ///< AC B相电流
    AC_C_CUR_ID,             ///< AC C相电流
    AC_N_CUR_ID,             ///< AC 中性线电流
    DC_VOL_ID,               ///< DC 电压
    DC_CUR_ID,               ///< DC 电流
} data_id_e;




typedef enum
{
    RECORD_DATA_INDEX = 0,
    EXTREME_DATA_INDEX,
    SYS_LOG_INDEX,
    SYS_LOG1_INDEX,
    MAX_RECORD_FILE_NUM
} his_record_data_type_e;

typedef struct {
    unsigned short  data_id;
    unsigned short* sample_data;
    unsigned int    data_len;
}sample_data_info_t;

typedef struct {
    ms_time_base_t      trig_time;    // 触发时间
    unsigned short      trig_cause;      // 触发原因
    unsigned int        trig_index;      // 触发序号
    sample_data_info_t  ac_sample_data[MAX_AC_SAMPLE_NUM];   // 后续可考虑设计为链表？
    sample_data_info_t  dc_sample_data[MAX_DC_SAMPLE_NUM];
}record_data_sample_info_t;

typedef struct {
    unsigned char  data[SIGLE_RECORD_DATA_MAX_LEN];
    int len;
}record_data_info_t;
#pragma pack(pop)

int init_his_record(void);
unsigned int save_before_and_after_data(float before_record_time, float after_record_time, unsigned int trig_index, unsigned char* sample_data, unsigned char* buff, unsigned int* offset);
unsigned int save_after_data(unsigned int trig_index, unsigned char* sample_data, unsigned char* buff, unsigned int* offset);
int save_record_data(record_data_sample_info_t* record_data_sample_info, unsigned char save_num, trig_cause_e trig_cause);
int register_record_data_info(record_data_sample_info_t* info);
int pack_record_data(record_data_sample_info_t* record_data_sample_info, unsigned char* record_data, trig_cause_e trig_cause);
int send_ctrl_led_msg(unsigned short lightoff_time, unsigned short lighton_time, unsigned char pin_no);
int save_sample_extreme_value(unsigned short* sample_data, unsigned int sample_len, unsigned short sample_id, ms_time_base_t* time, unsigned char sample_point);
unsigned int pack_ac_points(record_data_sample_info_t* record_data_sample_info, unsigned char* record_data, trig_cause_e trig_cause, unsigned int offset, unsigned char* point_num);
unsigned int pack_dc_points(record_data_sample_info_t* record_data_sample_info, unsigned char* record_data, trig_cause_e trig_cause, unsigned int offset, unsigned char* point_num);
int save_record_data_to_file(unsigned char* save_num);
int clear_memory_exterme_data();
int init_extreme_data();
#ifdef __cplusplus
}
#endif

#endif  // _TOV_HIS_RECORD_H
