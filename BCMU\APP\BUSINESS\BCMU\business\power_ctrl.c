#include <rtthread.h>
#include "data_type.h"
#include "mqtt_utils.h"
#include "utils_heart_beat.h"
#include "thread_id.h"
#include "para_manage.h"
#include "power_ctrl.h"
#include "para_id_in.h"
#include "cmd.h"
#include "msg_id.h"
#include "sps.h"
#include "mqtt_error.h"
#include "power_ctrl_utils.h"

int mqtt_disconn_ctrl()
{
    static char s_disconn_first_flag = TRUE;
    static unsigned int s_disconn_start_time = 0;
    static char conn_change_flag = TRUE;
    static char enable_change_flag = TRUE;
    static char is_mqtt_conned = FALSE;

    char mqtt_conn_sta = FALSE;
    unsigned short mqtt_disconn_time = 0;
    unsigned short mqtt_disconn_enable = DISCONN_CTRL_ENABLE;
    unsigned short mqtt_disconn_time_set = 0;

    mqtt_conn_sta = is_client_conn_normal(ZTE_CLIENT);
    is_mqtt_conned |= mqtt_conn_sta;
    if(mqtt_conn_sta != TRUE && is_mqtt_conned == TRUE)
    {
        conn_change_flag = TRUE;
        if(s_disconn_first_flag == TRUE)
        {
            s_disconn_start_time = rt_tick_get_millisecond();
            s_disconn_first_flag = FALSE;
        }
        mqtt_disconn_time = (rt_tick_get_millisecond() - s_disconn_start_time)/1000;
        get_one_para(DAC_PARA_ID_ENABLE_COMMU_CHAIN_FAIL_PROT_OFFSET, &mqtt_disconn_enable);
        get_one_para(DAC_PARA_ID_COMMU_BROKEN_CHAIN_DETECT_TIME_OFFSET, &mqtt_disconn_time_set);
        if(mqtt_disconn_enable == DISCONN_CTRL_ENABLE && mqtt_disconn_time >= mqtt_disconn_time_set)
        {
            enable_change_flag = TRUE;
            // 设置通讯断有功功率参数
            set_act_power_adj();
            // 设置通讯断无功功率参数
            set_react_power_adj();
        }
        else if(enable_change_flag != FALSE)
        {
            enable_change_flag = FALSE;
            res_ori_power_ctrl_data();
        }
        return TRUE;
    }
    else if(conn_change_flag != FALSE)
    {
        conn_change_flag = FALSE;
        s_disconn_first_flag = TRUE;
        res_ori_power_ctrl_data();
        return FALSE;
    }
    return FALSE;
}

void power_ctrl_thread_entry(void *parameter)
{
    PRINT_MSG_AND_RETURN_IF_FAIL(parameter != NULL);
    pre_thread_beat_f(THREAD_POWER_CTRL);

    while (is_running(TRUE))
    {
        thread_beat_go_on(THREAD_POWER_CTRL);
        mqtt_disconn_ctrl();
        is_power_ctrl_para_chg();
        rt_thread_mdelay(1000);
    }
}


void* power_ctrl_init(void* param)
{
    return param;
}