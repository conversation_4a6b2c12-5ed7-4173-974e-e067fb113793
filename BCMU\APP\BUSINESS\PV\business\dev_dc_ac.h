/**
 * @file     dev_dc_ac.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2017-04-19
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */


#ifndef _DEVICE_DC_AC_H
#define _DEVICE_DC_AC_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "device_type.h"

/* 设备缓冲区长度 */
#define R_DC_AC_BUFF_LEN                    1024       ///<  接收缓冲区长度
#define S_DC_AC_BUFF_LEN                    1024       ///<  发送缓冲区长度

// 逆变读取结构  块ID 起始地址 参数个数
#define PARA_START_ADDR              0x01

// 查询实时数据ID
#define MPPT_ANA_ID                  0x01      ///<  MPPTx模拟信息查询
#define SERIES_ANA_ID                0x10      ///<  组串模拟信息查询
#define OUTPUT_ID                    0x11      ///<  输出信息查询
#define INVERTER_ID                  0x12      ///<  逆变器信息查询
#define AUX_POWER_ID                 0x13      ///<  辅助电源模拟信息查询
#define AUX_ANA_ID                   0x14      ///<  辅控模拟信息查询
#define DEBUG_MODE_ANA_ID            0x15      ///<  调测模式模拟信息查询
#define DIG_ID                       0x20      ///<  状态信息查询
#define ALARM_ID                     0x30      ///<  告警信息查询
#define DUBUG_INFO_ID                0x16      ///<  调测用状态信息查询
// 查询实时数据参数长度
#define MPPT_PARA_LEN                0x05      ///<  MPPTx模拟信息查询
#define SERIES_PARA_LEN              0x10      ///<  组串模拟信息查询
#define OUTPUT_PARA_LEN              0x1F      ///<  输出信息查询
#define INVERTER_PARA_LEN            0x1D      ///<  逆变器信息查询
#define AUX_POWER_PARA_LEN           0x12      ///<  辅助电源模拟信息查询
#define DEBUG_MODE_ANA_LEN           0x10      ///<  调测模式模拟信息查询
#define AUX_ANA_LEN                  0x1C      ///<  辅控模拟信息查询
#define DIG_PARA_LEN                 0x05      ///<  状态信息查询
#define ALARM_PARA_LEN               0x0F      ///<  告警信息查询
#define DUBUG_INFO_LEN               0x01      ///<  调测用状态信息查询


// 版本查询命令ID
#define VER_ID                       0x01      ///<  版本查询命令
// 版本查询命令参数长度
#define VER_PARA_LEN                 0x2A      ///<  版本查询命令

// 升级状态主动上送ID
#define UPDATE_STATUS_ID             0x01      ///<  升级状态主动上送
// 升级状态主动上送参数长度
#define UPDATE_STATUS_LEN            0x03      ///<  升级状态主动上送

// 辅控升级帧数
#define AUXI_CTRL_FRAME_ID             0x02      ///<  辅控升级帧数
#define AUXI_CTRL_FRAME_LEN            0x01      ///<  辅控升级帧数

// 参数读写数据ID
#define REMOTE_CONTROL_SWITCH_ID                                              0x01           ///<  遥控开关机         
#define POWER_GRID_PARA_ID                                                    0x02           ///<  电网参数
#define CHARACTERISTIC_PARA_ID                                                0x03           ///<  特性参数
#define MPPT_PARA_ID                                                          0x04           ///<  MPPT参数设置
#define LVRT_PARA_ID                                                          0x05           ///<  LVRT参数设置
#define LVRT_CURVE_PARA_ID                                                    0x06           ///<  LVRT特征曲线
#define HVRT_PARA_ID                                                          0x07           ///<  HVRT参数设置
#define PID_PARA_ID                                                           0x08           ///<  PID参数设置
#define AFCI_PARA_ID                                                          0x09           ///<  AFCI参数
#define POWER_REGULATION_PARA_ID                                              0x0A           ///<  功率调节参数
#define COMM_FAIL_SAFE_PARA_ID                                                0x0B           ///<  通信断链失效保护参数
#define ACTIVE_POWER_REGULATION_PARA_ID                                       0x0C           ///<  有功功率调节参数
#define REACTIVE_POWER_REGULATION_PARA_ID                                     0x0D           ///<  无功功率调节
#define P_PN_CURVE_PARA_ID                                                    0x0E           ///<  cosφ-P/Pn特性曲线
#define Q_U_CURVE_PARA_ID                                                     0x0F           ///<  Q-U特性曲线设置
#define PF_U_CURVE_PARA_ID                                                    0x10           ///<  PF-U特性曲线设置
#define PPC_ACTIVE_POWER_PARA_ID                                              0x11           ///<  并网点控制-有功功率
#define PPC_REACTIVE_POWER_PARA_ID                                            0x12           ///<  并网点控制-无功功率
#define PPC_FEED_OVER_LIMIT_PARA_ID                                           0x13           ///<  并网点控制-馈电越限保护关机
#define PROTECTION_PARA_ID                                                    0x14           ///<  保护参数
#define PROTECTIN_LEVEL1_PARA_ID                                              0x15           ///<  一级保护参数
#define PROTECTIN_LEVEL2_PARA_ID                                              0x16           ///<  二级保护参数
#define PROTECTIN_LEVEL3_PARA_ID                                              0x17           ///<  三级保护参数
#define PROTECTIN_LEVEL4_PARA_ID                                              0x18           ///<  四级保护参数
#define PROTECTIN_LEVEL5_PARA_ID                                              0x19           ///<  五级保护参数
#define PROTECTIN_LEVEL6_PARA_ID                                              0x1A           ///<  六级保护参数
#define OTHER_PARA_ID                                                         0x1B           ///<  其他参数
#define FAULT_RECORD_1_ID                                                     0x60           ///<  故障录播1
#define FAULT_RECORD_2_ID                                                     0x61           ///<  故障录播2
#define HVRT_CURVE_PARA_ID                                                    0x1c           ///<  HVRT特征曲线
#define STRING_IN_DETECTION                                                   0X1D           ///<  组串接入检测
#define OVER_UNDER_FREQ_DERATING                                              0x20           ///<  过欠频降额参数
#define DRIVE_TEST_PARA_ID                                                    0x80           ///<  驱动测试相关参数
#define ADC_SAMPLE_TEST_PARA_ID                                               0x81           ///<  ADC采样测试相关参数
#define RD_TEST_RELATED_ID                                                    0x82           ///<  研发测试相关参数
#define PRODUCTION_TEST_PARA_ID                                               0x83           ///<  生产测试相关参数



// 参数读写数据参数长度
#define REMOTE_CONTROL_SWITCH_PARA_LEN                                        0x07           ///<  遥控开关机
#define POWER_GRID_PARA_LEN                                                   0x0F           ///<  电网参数
#define CHARACTERISTIC_PARA_LEN                                               0x06           ///<  特性参数
#define MPPT_PARA_PARA_LEN                                                    0x02           ///<  MPPT参数设置
#define LVRT_PARA_LEN                                                         0x08           ///<  LVRT参数设置
#define LVRT_CURVE_PARA_LEN                                                   0x15           ///<  LVRT特征曲线
#define HVRT_PARA_LEN                                                         0x04           ///<  HVRT参数设置
#define PID_PARA_LEN                                                          0x04           ///<  PID参数设置
#define AFCI_PARA_LEN                                                         0x03           ///<  AFCI参数
#define POWER_REGULATION_PARA_LEN                                             0x11           ///<  功率调节参数
#define COMM_FAIL_SAFE_PARA_LEN                                               0x08           ///<  通信断链失效保护参数
#define ACTIVE_POWER_REGULATION_PARA_LEN                                      0x06           ///<  有功功率调节参数
#define REACTIVE_POWER_REGULATION_PARA_LEN                                    0x09           ///<  无功功率调节
#define P_PN_CURVE_PARA_LEN                                                   0x17           ///<  cosφ-P/Pn特性曲线
#define Q_U_CURVE_PARA_LEN                                                    0x19           ///<  Q-U特性曲线设置
#define PF_U_CURVE_PARA_LEN                                                   0x16           ///<  PF-U特性曲线设置
#define PPC_ACTIVE_POWER_PARA_LEN                                             0x09           ///<  并网点控制-有功功率
#define PPC_REACTIVE_POWER_PARA_LEN                                           0x04           ///<  并网点控制-无功功率
#define PPC_FEED_OVER_LIMIT_PARA_LEN                                          0x04           ///<  并网点控制-馈电越限保护关机
#define PROTECTION_PARA_LEN                                                   0x02           ///<  保护参数
#define PROTECTIN_LEVEL_PARA_LEN                                              0x0C           ///<  等级保护参数
#define OTHER_PARA_LEN                                                        0x01           ///<  其他参数
#define HVRT_CURVE_PARA_LEN                                                   0x15           ///<  HVRT特征曲线
#define STRING_IN_DETECTION_LEN                                               0x0B           ///<  组串接入检测
#define OVER_UNDER_FREQ_DERATING_LEN                                          0x15           ///<  过欠频降额参数
#define DRIVE_TEST_PARA_LEN                                                   0x06           ///<  驱动测试相关参数
#define ADC_SAMPLE_TEST_PARA_LEN                                              0x03           ///<  ADC采样测试相关参数
#define RD_TEST_RELATED_LEN                                                   0x03           ///<  研发测试相关参数
#define PRODUCTION_TEST_PARA_LEN                                              0x02           ///<  生产测试相关参数



// 校正参数读写数据ID 
#define OUT_VOLT_ADJ_ID                                                       0x50           ///<  输出电压比例校正
#define BUS_VOLT_ADJ_ID                                                       0x51           ///<  BUS电压比例校正 
#define MPPT_ADJ_ID                                                           0x52           ///<  MPPT比例校正 
#define AUX_ADJ_ID                                                            0x56           ///<  辅控比例校正 
#define LEAK_CURR_ADJ_ID                                                      0x57           ///<  漏电流比例校正
#define DC_COMPONENT_ADJ_ID                                                   0x58           ///<  直流分量比例校正

// 校正参数读写数据参数长度 
#define OUT_VOLT_ADJ_LEN                                                      0x12           ///<  输出电压比例校正
#define BUS_VOLT_ADJ_LEN                                                      0x04           ///<  BUS电压比例校正 
#define MPPT_ADJ_LEN                                                          0x08           ///<  MPPT比例校正 
#define AUX_ADJ_LEN                                                           0x20           ///<  辅控比例校正 
#define LEAK_CURR_ADJ_LEN                                                     0x02           ///<  漏电流比例校正 
#define DC_COMPONENT_ADJ_LEN                                                  0x06           ///<  直流分量比例校正


// 生产相关参数读写数据ID
#define PPRO_ID                                                               0x90           ///<  生产信息写入
#define ASSET_ID                                                              0x91           ///<  资产信息写入
#define PRODUCT_INFO_ID                                                       0x92           ///<  产品信息
#define POWER_HARDWARE_VERSION_ID                                             0x93           ///<  功率硬件版本

// 逆变器控制命令
#define IV_CTRL_ORDER                                                         0x02
#define IV_CTRL_LEN                                                           0x01

//生产控制命令
#define PRODUCT_CTRL_ORDER                                                    0x01
#define PRODUCT_CTRL_LEN                                                      0x07

// 逆变器告警清除命令
#define PV_ALARM_CLEAR_CTRL_ORDER                                             0x03
#define PV_ALARM_CLEAR_CTRL_LEN                                               0x01

// 工作模式控制命令
#define WORK_MODE_CTRL_ORDER                                             0x10
#define WORK_MODE_CTRL_LEN                                               0x01

// 研发调试控制命令
#define DEBUG_CTRL_ORDER                                             0x20
#define DEBUG_CTRL_LEN                                               0x01

// 生产相关参数读写数据长度
#define PPRO_LEN                                                              0x08           ///<  生产信息写入
#define ASSET_LEN                                                             0x15           ///<  资产信息写入
#define PRODUCT_INFO_LEN                                                      0x0F           ///<  产品信息
// #define IV_DATA_LEN                                                           0x80           ///<  获取iv数据长度
#define HARDWARE_VERSION_LEN                                                  0x0F           ///<  硬件版本

#define DC_AC_MPPT_NUM                    4              /// MPPT的数量
#define DC_AC_DC_ARC_NUM                  4              /// 直流电弧检测校正比例数量
#define DC_AC_SOFT_VER_LEN                12             /// 软件版本
#define DC_AC_MACH_SERIES_LEN             12             /// 整机条码长度
#define DC_AC_PRO_MODEL_LEN               30             /// 产品型号
#define AUXILIARY_POWER_NUM               10             ///辅助电源数量
#define DC_AC_PRO_MODEL_LEN_2             32             /// 产品型号2
#define DC_AC_HARDWARE_VERSION_LEN        30             /// 硬件版本

#define MAX_ID_INDEX                                   16 // 命令ID最大维度

typedef struct{
    cmd_parse_info_id_verison_t* para_cmd_pack_info;
    int info_num;
}para_cmd_pack_info_tab;



dev_type_t* init_dev_dc_ac(void);
para_cmd_pack_info_tab* get_para_cmd_pack_info_tabs(void);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _DEVICE_DC_DC_H
