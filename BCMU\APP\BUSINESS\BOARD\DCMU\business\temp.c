/**************************************************************************
 * 版权信息：（C）2008-2009，深圳市中兴通讯股份有限公司电源开发部版权所有
 * 系统名称：ZXDU88 S402 DCMU CORE板软件
 * 文件名称：temp.c
 * 文件说明：临时文件
 * 作    者：王刚
 * 版本信息：V1.0
 * 设计日期：2025-07-10
 * 修改记录：
 * 日    期      版  本      修改人      修改摘要
 * 其他说明：
 ***************************************************************************/
#include <sys/time.h>
#include <time.h>
#include "temp.h"
#include "his_record.h"
#include "gui_data_interface.h"

rt_uint8_t ucDeviceType = ZXDU88_S402;
static rt_mutex_t key_mutex = RT_NULL;       // 互斥锁
static rt_uint8_t s_aucKeyBuf[KEY_BUFF_LEN]; // 键盘缓冲区
static rt_uint8_t s_ucKeyHead = 0;           // 键盘缓冲区的头指针
static rt_uint8_t s_ucKeyTail = 0;           // 键盘缓冲区的尾指针

static T_AnalogDataStruct s_tAnalogData; // 实时模拟量数据
T_AnalogDataStruct g_tAnalogAdjData;     // 实时模拟量临时数据
rt_uint8_t s_ucAutoSendTimes = 0;
rt_uint8_t s_ucAutoSendStep = 0;
rt_uint16_t s_wAutoSendWaitTmr = 0;
T_SYS_DEBUG g_tSysDebug;
T_COMM_DEBUG g_tCommDebug;
rt_uint8_t g_abNeedSendAlarm[COMM_PORT_NUM] = {TRUE, TRUE};       // 重要告警变化，需要主动告警标志，在告警处理时产生，在告警上报后消失 by wu 2009.10.29
rt_uint8_t g_abAutoSendAlarmFlag[COMM_PORT_NUM] = {FALSE, FALSE}; // 1104处于主动告警流程标志 更改为数组形式 by wu 2010.3.11
T_CommStruct g_atComm[COMM_PORT_NUM];                             /* 通讯底层 */
T_EEpromISPSruct tPrevISP;
rt_uint8_t g_ucDownloadMode = MODE_APP;
rt_uint8_t g_bHisAlarmChk = FALSE; // 表示历史告警队列检查是否完毕
rt_uint16_t g_wMaxHisEventLen;     // 可保存事件记录条数
rt_uint16_t g_wMaxHisAlmLen;       // 可保存历史告警条数
T_FileManageStruct s_tFileManage = {0};
rt_uint16_t s_wCmdNum[9] = {0, 0, 0, 0, 0, 0, 0, 0, 0};

// 操作记录ID MIB表
// const MIB_HisOperIDNode CTRL_MIBTable[] =
//     {
//         {ID1_DCMU_SETSTATUS, ID2_PR_TIME, 1},           // 系统时间设置
//         {ID1_DCMU_CTRL, ID2_CTL_RLYRESET, INRELAY_NUM}, // 输出干节点恢复
//         {ID1_DCMU_CTRL, ID2_CTL_RLYACT, INRELAY_NUM},   // 输出干节点动作
//         {ID1_DCMU_CTRL, ID2_CTL_LDRUNPARA, 1},          // 恢复默认运行参数
//         {ID1_DCMU_CTRL, ID2_CTL_LDCFGPARA, 1},          // 恢复默认配置参数
//         {ID1_DCMU_CTRL, ID2_CTL_DOWMPROG, 1},           // 下载程序
//         {ID1_DCMU_CTRL, ID2_CTL_DOWNZK, 1},             // 下载字库
//         {ID1_DCMU_CTRL, ID2_CTL_RESET, 1},              // 系统复位
//         {0xFF, 0xFF, 0},                                // IDNumber为0表明数组结束
// };

// const MIB_AnalogDataNode AnalogData_MIBTable[] =
//     {

//         // 注意:实时模拟量数据结构要以这个表格的顺序来定义
//         // DCMU模拟量
//         //{ID1_DCMU_ANALOG, ID2_GENERAL, 6, TYPE_INT16S, 5, 1 },				// 所有DCMU模拟量ID2总数为6
//         {ID1_DCMU_ANALOG, ID2_AN_LOADVOLT, 1, TYPE_INT16S, 2, 1},        // 负载电压
//         {ID1_DCMU_ANALOG, ID2_AN_LOADSUMCURR, 1, TYPE_INT16S, 2, 1},     // 负载总电流
//         {ID1_DCMU_ANALOG, ID2_AN_LOADCURR, LOAD_NUM, TYPE_INT16S, 2, 1}, // 分路负载电流
//         {ID1_DCMU_ANALOG, ID2_AN_BATTVOLT, BATT_NUM, TYPE_INT16S, 2, 1}, // 电池电压
//         {ID1_DCMU_ANALOG, ID2_AN_BATTCURR, BATT_NUM, TYPE_INT16S, 2, 1}, // 电池电流
//         {ID1_DCMU_ANALOG, ID2_AN_BATTTEMP, BATT_NUM, TYPE_INT8S, 1, 0},  // 电池温度
//         {ID1_DCMU_ANALOG, ID2_AN_TOTALPOWER, 1, TYPE_FP32, 4, 2},        // 总电量//tcf
//         {ID1_DCMU_ANALOG, ID2_AN_LOADPOWER, LOAD_NUM, TYPE_FP32, 4, 2},  // 负载电量//tcf

//         // ENV模拟量
//         //{ID1_ENV_ANALOG, ID2_GENERAL, 2, TYPE_INT8S, 2, 0 },				// 所有ENV模拟量ID2总数为2
//         {ID1_ENV_ANALOG, ID2_AN_ENVTEMP, 1, TYPE_INT8S, 1, 0}, // 环境温度
//         {ID1_ENV_ANALOG, ID2_AN_ENVHUM, 1, TYPE_INT8S, 1, 0},  // 环境湿度

//         {0xFF, 0xFF, 0, TYPE_INT8U, 1, 0}, // IDNumber为0表明数组结束
// };

// T_FactoryInfo g_tFactInfo =
//     {
//         {"ZTE Corporation"},  // 厂家名称
//         {"ZXDU88 S402 DCMU"}, // SM名称
//         {1, 23, 00, 01},      // SM软件版本V1.10(中文)
//         2022,
//         05,
//         11,                                                          // SM软件发布日期:2016,08, 11,
//         {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 预留字节
//          0x00, 0x00, 0x00}};

const rt_uint16_t cg_awCRC_Table[256] = {
    0x0000, 0x1021, 0x2042, 0x3063, 0x4084, 0x50a5, 0x60c6, 0x70e7,
    0x8108, 0x9129, 0xa14a, 0xb16b, 0xc18c, 0xd1ad, 0xe1ce, 0xf1ef,
    0x1231, 0x0210, 0x3273, 0x2252, 0x52b5, 0x4294, 0x72f7, 0x62d6,
    0x9339, 0x8318, 0xb37b, 0xa35a, 0xd3bd, 0xc39c, 0xf3ff, 0xe3de,
    0x2462, 0x3443, 0x0420, 0x1401, 0x64e6, 0x74c7, 0x44a4, 0x5485,
    0xa56a, 0xb54b, 0x8528, 0x9509, 0xe5ee, 0xf5cf, 0xc5ac, 0xd58d,
    0x3653, 0x2672, 0x1611, 0x0630, 0x76d7, 0x66f6, 0x5695, 0x46b4,
    0xb75b, 0xa77a, 0x9719, 0x8738, 0xf7df, 0xe7fe, 0xd79d, 0xc7bc,
    0x48c4, 0x58e5, 0x6886, 0x78a7, 0x0840, 0x1861, 0x2802, 0x3823,
    0xc9cc, 0xd9ed, 0xe98e, 0xf9af, 0x8948, 0x9969, 0xa90a, 0xb92b,
    0x5af5, 0x4ad4, 0x7ab7, 0x6a96, 0x1a71, 0x0a50, 0x3a33, 0x2a12,
    0xdbfd, 0xcbdc, 0xfbbf, 0xeb9e, 0x9b79, 0x8b58, 0xbb3b, 0xab1a,
    0x6ca6, 0x7c87, 0x4ce4, 0x5cc5, 0x2c22, 0x3c03, 0x0c60, 0x1c41,
    0xedae, 0xfd8f, 0xcdec, 0xddcd, 0xad2a, 0xbd0b, 0x8d68, 0x9d49,
    0x7e97, 0x6eb6, 0x5ed5, 0x4ef4, 0x3e13, 0x2e32, 0x1e51, 0x0e70,
    0xff9f, 0xefbe, 0xdfdd, 0xcffc, 0xbf1b, 0xaf3a, 0x9f59, 0x8f78,
    0x9188, 0x81a9, 0xb1ca, 0xa1eb, 0xd10c, 0xc12d, 0xf14e, 0xe16f,
    0x1080, 0x00a1, 0x30c2, 0x20e3, 0x5004, 0x4025, 0x7046, 0x6067,
    0x83b9, 0x9398, 0xa3fb, 0xb3da, 0xc33d, 0xd31c, 0xe37f, 0xf35e,
    0x02b1, 0x1290, 0x22f3, 0x32d2, 0x4235, 0x5214, 0x6277, 0x7256,
    0xb5ea, 0xa5cb, 0x95a8, 0x8589, 0xf56e, 0xe54f, 0xd52c, 0xc50d,
    0x34e2, 0x24c3, 0x14a0, 0x0481, 0x7466, 0x6447, 0x5424, 0x4405,
    0xa7db, 0xb7fa, 0x8799, 0x97b8, 0xe75f, 0xf77e, 0xc71d, 0xd73c,
    0x26d3, 0x36f2, 0x0691, 0x16b0, 0x6657, 0x7676, 0x4615, 0x5634,
    0xd94c, 0xc96d, 0xf90e, 0xe92f, 0x99c8, 0x89e9, 0xb98a, 0xa9ab,
    0x5844, 0x4865, 0x7806, 0x6827, 0x18c0, 0x08e1, 0x3882, 0x28a3,
    0xcb7d, 0xdb5c, 0xeb3f, 0xfb1e, 0x8bf9, 0x9bd8, 0xabbb, 0xbb9a,
    0x4a75, 0x5a54, 0x6a37, 0x7a16, 0x0af1, 0x1ad0, 0x2ab3, 0x3a92,
    0xfd2e, 0xed0f, 0xdd6c, 0xcd4d, 0xbdaa, 0xad8b, 0x9de8, 0x8dc9,
    0x7c26, 0x6c07, 0x5c64, 0x4c45, 0x3ca2, 0x2c83, 0x1ce0, 0x0cc1,
    0xef1f, 0xff3e, 0xcf5d, 0xdf7c, 0xaf9b, 0xbfba, 0x8fd9, 0x9ff8,
    0x6e17, 0x7e36, 0x4e55, 0x5e74, 0x2e93, 0x3eb2, 0x0ed1, 0x1ef0};

/* 初始化互斥锁 */
// void key_buffer_init(void)
// {
//     key_mutex = rt_mutex_create("key_mtx", RT_IPC_FLAG_PRIO);
//     return;
// }

void InitLcd(void)
{
    return;
}

/****************************************************************************
 * 函数名称:	AddKey
 * 调    用:	无
 * 被 调 用: 外部主函数调用
 * 输入参数: ucKeyID -- 写入的键值
 * 返 回 值: 无
 * 功能描述: 键盘队列的写入
 * 作    者:
 * 设计日期:
 * 修改记录:
 * 日    期		版	本		修改人		修改摘要
 ***************************************************************************/
// void AddKey(unsigned char ucKeyID)
// {
//     rt_mutex_take(key_mutex, RT_WAITING_FOREVER);
//     s_aucKeyBuf[s_ucKeyTail] = ucKeyID;

//     s_ucKeyTail++;
//     s_ucKeyTail %= KEY_BUFF_LEN;

//     if (s_ucKeyHead == s_ucKeyTail)
//     {
//         s_ucKeyHead++;
//         s_ucKeyHead %= KEY_BUFF_LEN;
//     }
//     rt_mutex_release(key_mutex); // 释放互斥锁
//     // SendMessage( MSG_KEY_PRESSED );
//     // SetAddkeyTimer(50);//同步按键响应慢

//     return;
// }

/****************************************************************************
 * 函数名称:	DelKey
 * 调    用:	无
 * 被 调 用: 外部主函数调用
 * 输入参数: 无
 * 返 回 值: 无
 * 功能描述: 读出键盘值
 * 作    者:
 * 设计日期:
 * 修改记录:
 * 日    期		版	本		修改人		修改摘要
 ***************************************************************************/
// rt_uint8_t DelKey(void)
// {
//     rt_uint8_t ucRet;
//     rt_mutex_take(key_mutex, RT_WAITING_FOREVER);
//     if (s_ucKeyHead == s_ucKeyTail)
//     {
//         rt_mutex_release(key_mutex);
//         return KEY_NOTHING;
//     }

//     ucRet = s_aucKeyBuf[s_ucKeyHead];

//     s_ucKeyHead++;
//     s_ucKeyHead %= KEY_BUFF_LEN;
//     rt_mutex_release(key_mutex);
//     return ucRet;
// }

/****************************************************************
//	函数名  	：GetSysParaPtr
//	入口参数	：无
//	出口参数	：参数结构体指针
//	功能	  	：读取参数指针
//  调用关系	：
//  全局变量	：
*****************************************************************/
// T_SysPara_old *GetSysParaPtr(void)
// {
//     return &s_tSysPara;
// }

/*************************************************************************
函数名	：	Delayms
功能	：	延时?ms,
输入	：	无
输出	：	无
*************************************************************************/
void Delayms(unsigned int wMillisecond)
{
    return;
}

void FeedWatchDog(void)
{
    return;
}
/*****************************************************************************
//	函数名  	：CRC_Cal(char *p,rt_uint16_t wCount)
//	入口参数	：p：数据指针   wCount：数据长度
//	出口参数	：CRC结果
//	功能	  	：查表计算CRC
*****************************************************************************/
rt_uint16_t CRC_Cal(char *p, rt_uint16_t wCount)
{
    rt_uint16_t i, crc;
    rt_uint8_t ucTmp;

    crc = 0;
    for (i = 0; i < wCount; i++)
    {
        ucTmp = (rt_uint8_t)((crc >> 8) ^ p[i]);
        crc = (crc << 8) ^ cg_awCRC_Table[ucTmp];
    }
    return (crc);
}

/*************************************************************************
*函数名  :  WriteNVRAM
*调  用  :	无
*被调用  :
*输入参数:	wAddress--偏移量
            ucData--数据
*返回值	 :	TRUE--成功
            FALSE--失败
*功能描述:	写入NVRAM某地址数据
*作  者  :
*设计日期:
*修改记录:
* 日    期		版	本		修改人		修改摘要
*************************************************************************/
unsigned char WriteNVRAM(unsigned long ulAddress, rt_uint8_t ucData)
{
    return 0;
}

/****************************************************************************
 * 函数名称：MemCopyFromNVRam()
 * 调    用：
 * 被 调 用：无
 * 输入参数：p--待写入的地址; wOffset--偏移量; wLen--待读出的长度
 * 返 回 值：无
 * 功能描述：从NVRAM中拷贝数据至RAM
 * 作    者：严宏明
 * 设计日期：2004-06-07
 * 修改记录：
 * 日    期		版	本		修改人		修改摘要
 ***************************************************************************/
void MemCopyFromNVRAM(unsigned char *p, unsigned long ulOffset, rt_uint8_t ucLen)
{
    return;
}

/*************************************************************************
函数名	:	IfLeapYear
功能	:	判断是否是闰年
输入	:	year--年
输出	:	TRUE--为闰年 FALSE--非闰年
*************************************************************************/
rt_uint8_t IfLeapYear(rt_uint16_t year)
{
    return ((year % 4 == 0) && (year % 100 != 0) || (year % 400 == 0));
}

/*************************************************************************
函数名	：	CheckTimeValid
功能	：	检查设置时间的有效性
输入	： 	无
输出	：	TRUE-有效，FALSE-无效
*************************************************************************/
rt_uint8_t CheckTimeValid(T_TimeStruct *ptTime)
{
    static rt_uint8_t MonthsLeap[12] = {31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    static rt_uint8_t MonthsNoLeap[12] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};

    if ((ptTime->wYear > 2099) || (ptTime->wYear < 1970))
    {
        return FALSE;
    }
    if ((ptTime->ucMonth < 1) || (ptTime->ucMonth > 12))
    {
        return FALSE;
    }
    if ((ptTime->ucHour > 23) || (ptTime->ucMinute > 59) || (ptTime->ucSecond > 59))
    {
        return FALSE;
    }

    if (IfLeapYear(ptTime->wYear))
    {
        if ((ptTime->ucDay > MonthsLeap[ptTime->ucMonth - 1]) ||
            (ptTime->ucDay < 1))
        {
            return FALSE;
        }
    }
    else
    {
        if ((ptTime->ucDay > MonthsNoLeap[ptTime->ucMonth - 1]) ||
            (ptTime->ucDay < 1))
        {
            return FALSE;
        }
    }

    return TRUE;
}

/****************************************************************************
 * 函数名称：CheckHisAlarm()
 * 调    用：无
 * 被 调 用：
 * 输入参数：无
 * 返 回 值：无
 * 功能描述：获取历史告警指针
 * 作    者：潘奇银
 * 设计日期：2008-12-22
 * 日    期		版	本		修改人		修改摘要
 ***************************************************************************/
void GetAlmBuff(T_BuffIndex *tBuffIndex)
{
    return;
}

rt_uint8_t GetHisDataTop(void)
{
    return 0;
}

rt_uint8_t GetHisDataSendTail(void)
{
    return 0;
}
rt_uint8_t GetHisDataAckTail(void)
{
    return 0;
}

/****************************************************************************
 * 函数名称：CalRealAlarmTotal()
 * 调    用：无
 * 被 调 用：无
 * 输入参数：无
 * 返 回 值：实时告警总条数
 * 功能描述：计算实时告警总条数
 * 作    者：潘奇银
 * 设计日期：2008-02-27
 * 修改记录：
 * 日    期		版	本		修改人		修改摘要
 ***************************************************************************/
// rt_uint8_t CalRealAlarmTotal(void)
// {
//     return 0;
// }

/*****************************************************************************
//	函数名  	：GetTime
//	入口参数	：指向获取时间的指针
//	出口参数	：无
//	功能	  	：获取时间
//  调用关系	：
//  全局变量	：
*****************************************************************************/
void GetTime(T_TimeStruct *ptTime)
{
    time_t t = time(RT_NULL);
    struct tm t_time;

    if (ptTime == NULL)
    {
        return;
    }

    memset(&t_time, 0x00, sizeof(t_time));
    localtime_r(&t, &t_time); // IAR 报错，未定义
    ptTime->wYear = t_time.tm_year + 1900;
    ptTime->ucMonth = t_time.tm_mon + 1;
    ptTime->ucDay = t_time.tm_mday;
    ptTime->ucHour = t_time.tm_hour;
    ptTime->ucMinute = t_time.tm_min;
    ptTime->ucSecond = t_time.tm_sec;
    return;
}

/*****************************************************************************
//	函数名  	：SetTime
//	入口参数	：需要设置的时间
//	出口参数	：无
//	功能	  	：设置时间
//  调用关系	：
//  全局变量	：
*****************************************************************************/
void SetTime(T_TimeStruct *ptTime)
{
    return;
}

/****************************************************************************
 * 函数名称：GetDisHisAlm()
 * 调    用：无
 * 被 调 用：
 * 输入参数：无
 * 返 回 值：无
 * 功能描述：从NVRAM中读取历史告警,用于界面显示
 * 作    者：
 * 设计日期：
 * 日    期		版	本		修改人		修改摘要
 ***************************************************************************/
rt_uint8_t GetDisHisAlm(rt_uint16_t wNum, T_HisAlarmStruct *tHisAlarm)
{
    return 0;
}

/***************************************************************************
 * 函数名称：DealRS485Comm
 * 调    用：
 * 被 调 用：
 * 输入参数：ucWhichFlag--DATAFLAG编号,ucVal--设置值
 * 返 回 值：
 * 功能描述：设置RS485通讯协议的DATAFLAG
 * 作    者：潘奇银
 * 设计日期：2008-09-08
 * 修改记录：
 * 日    期		版	本		修改人		修改摘要
 ***************************************************************************/
void SetRS485DataFlag(rt_uint8_t ucWhichFlag, rt_uint8_t ucVal)
{
    return;
}

/****************************************************************************
* 函数名称：GetPara()
* 调    用：无
* 被 调 用：
* 输入参数：ptPara -- 参数区,ucParaID  -- 参数在参数结构体T_SysPara中的序号,
            ucIndex--参数索引(如电池1、2、3等)
* 返 回 值：参数值
* 功能描述：获取参数
* 作    者：潘奇银
* 设计日期：2008-02-01
* 修改记录：
* 日    期		版	本		修改人		修改摘要
***************************************************************************/
// rt_int16_t GetPara(T_SysPara_old *ptPara, rt_uint8_t ucID1, rt_uint8_t ucID2, rt_uint8_t ucIndex)
// {
//     return 0;
// }


/****************************************************************************
 * 函数名称：LoadDefaultPara()
 * 调    用：无
 * 被 调 用：
 * 输入参数：ptPara -- 参数保存区
 * 返 回 值：无
 * 功能描述：载入参数缺省值
 * 作    者：刘东波
 * 设计日期：2008-10-01
 * 修改记录：
 * 日    期		版	本		修改人		修改摘要
 ***************************************************************************/
void LoadDefaultPara(rt_uint8_t ucParaType)
{
    return;
}

/*****************************************************************************
//	函数名  	：WrClk
//	入口参数	：tTime -- 写入的时间
//	出口参数	：无
//	功能	  	：写入时钟
//  调用关系	：
//  全局变量	：
*****************************************************************************/
void WrClk(T_TimeStruct tTime)
{
    return;
}