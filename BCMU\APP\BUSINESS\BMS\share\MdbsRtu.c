/**************************************************************************
* Copyright (C) 2001, ZTE Corporation.
* 版权信息：（C）2009，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：BMS
* 文件名称：MdbsRtu.c
* 文件说明：
* 作    者：
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要
* 其他说明：
***************************************************************************/

#include "rtdebug.h"
#include "rtthread.h"
#include "fileSys.h"
#include <stdio.h>
#include "common.h"
#include "sample.h"
#include "hisdata.h"
#include "para.h"
#include "comm.h"
#include "realAlarm.h"
#include "commBdu.h"
#include "CommCan.h"
#include "MdbsRtu.h"
#include "AutomadeMdbs.h"
#include "protocol.h"
#include "battery.h"
#include "flash.h"
#include "sys/time.h"
#include <rtc_PFC8563.h>
#include "apptest.h"
#include "pdt_version.h"
#include "CommCan.h"
#include "led.h"
#include "utils_rtthread_security_func.h"

#ifdef ZXESM_R321_APTOWER_VERSION
#include "MdbsRtuAPT.h"
#endif
/****************************  函数声明********************************/
rt_err_t PFC8563set_date(Datebuff *Rcvdate);  //TODO: 待BSP添加对应声明后删除

static INT32 DealCommFail(void);
static BOOLEAN JudgeCommFail(void);
static INT32 UpdateCommFailAlm(void);
Static INT32 FindCntSn(INT32 slInAddrSn);
static INT32 InitCommFailJudge(void);
static INT32 ConvertRecPhycomm2Link(BYTE *pInBuff, WORD wInLen, Enum_NorthType ucInNorthType);
static INT32 CheckRecLink(void);
static INT32 ConvertRecLink2App(void);
#ifdef PAKISTAN_CMPAK_PROTOCOL
Static INT32 CheckRecApp(void);
#else
static INT32 CheckRecApp(void);
#endif
static INT32 DealMdbsRtuCommand(T_MdbsRtuApp *ptInData);
static INT32 ConvertSendApp2Link(void);
static INT32 ConvertSendLink2Phycomm(T_CommStruct* ptComm);
//static INT32 SendReadDig(T_MdbsRtuApp *ptInApp);
static INT32 SendReadDigIn(T_MdbsRtuApp *ptInApp);
static INT32 SendReadAna(T_MdbsRtuApp *ptInApp);
static INT32 SendReadAnaIn(T_MdbsRtuApp *ptInApp);
static INT32 WriteDigSingle(T_MdbsRtuApp *ptInApp);
static INT32 WriteAnaSingle(T_MdbsRtuApp *ptInApp);
static INT32 WriteAnaMul(T_MdbsRtuApp *ptInApp);
//static INT32 WriteDigMul(T_MdbsRtuApp *ptInApp);
static INT32 FillInByteToBitBuff(BYTE *pucOutData, INT32 slInNum, BYTE *pucInData);
static INT32 PacketOrParseBcmDataPartAna(const T_OneDataAttrStruct *ptInDataAttr,  E_DATA_DIRECTION eInDir, BYTE *pucOutData, BYTE *pucInRsvData);
//static INT32 PacketOrParseBcmAlmPartAna(const T_OneDataAttrStruct *ptInDataAttr,  E_DATA_DIRECTION eInDir, BYTE *pucOutData, BYTE *pucInRsvData);
static INT32 PacketOrParseBduFacPartAna(const T_OneDataAttrStruct *ptInDataAttr,  E_DATA_DIRECTION eInDir, BYTE *pucOutData, BYTE *pucInRsvData);
static INT32 PacketOrParseBcmFacPartAna(const T_OneDataAttrStruct *ptInDataAttr,  E_DATA_DIRECTION eInDir, BYTE *pucOutData, BYTE *pucInRsvData);
static INT32 PacketOrParseSysParaPartAna(const T_OneDataAttrStruct *ptInDataAttr,  E_DATA_DIRECTION eInDir, BYTE *pucOutData, BYTE *pucInRsvData);
static INT32 PacketOrParseSpecDataPartAna(const T_OneDataAttrStruct *ptInDataAttr,  E_DATA_DIRECTION eInDir, BYTE *pucOutData, BYTE *pucInRsvData);
Static INT32 PacketOrParseBmsPackPartAna(const T_OneDataAttrStruct *ptInDataAttr,  E_DATA_DIRECTION eInDir, BYTE *pucOutData, BYTE *pucInRsvData);
Static INT32 PacketOrParsePackManuPartAna(const T_OneDataAttrStruct *ptInDataAttr,  E_DATA_DIRECTION eInDir, BYTE *pucOutData, BYTE *pucInRsvData);
static INT32 PacketOrParseAna(const T_OneDataAttrStruct *ptInDataAttr,  E_DATA_DIRECTION eInDir, BYTE *pucOutData, BYTE *pucInRsvData);
static INT32 CoBitByteWordToByteBuff(const T_OneDataAttrStruct *ptInDataAttr, BYTE *pucOutData);
static INT32 SetSndAnswer(Enum_MdbsRTN ucInAnswer);
static INT32 SetErrorCode(BYTE ucInErr);
static BOOLEAN IsAddrLegal(void);
static INT32 ConvertCorrSendApp2Link(BYTE *pucOutData);
static INT32 ConvertIncorrSendApp2Link(BYTE *pucOutData);
static INT32 BOOLEANToINT16(const T_OneDataAttrStruct *ptInDataAttr, void *pInData, void *pOutByteBuff);
static INT32 CHARToINT16(const T_OneDataAttrStruct *ptInDataAttr, void *pInData, void *pOutByteBuff);
static INT32 INT8UToINT16(const T_OneDataAttrStruct *ptInDataAttr, void *pInData, void *pOutByteBuff);
//static INT32 INT8SToINT16(const T_OneDataAttrStruct *ptInDataAttr, void *pInData, void *pOutByteBuff);
static INT32 INT16UToINT16(const T_OneDataAttrStruct *ptInDataAttr, void *pInData, void *pOutByteBuff);
//static INT32 INT16SToINT16(const T_OneDataAttrStruct *ptInDataAttr, void *pInData, void *pOutByteBuff);
static INT32 FP32ToINT16(const T_OneDataAttrStruct *ptInDataAttr, void *pInData, void *pOutByteBuff);
static INT32 TIMEToINT16(const T_OneDataAttrStruct *ptInDataAttr, void *pInData, void *pOutByteBuff);
static INT32 DATEToINT16(const T_OneDataAttrStruct *ptInDataAttr, void *pInData, void *pOutByteBuff);
static INT32 INT16ToBOOLEAN(const T_OneDataAttrStruct *ptInDataAttr, void *pInData, void *pOutByteBuff);
static INT32 INT16ToCHAR(const T_OneDataAttrStruct *ptInDataAttr, void *pInData, void *pOutByteBuff);
static INT32 INT16ToINT8U(const T_OneDataAttrStruct *ptInDataAttr, void *pInData, void *pOutByteBuff);
//static INT32 INT16ToINT8S(const T_OneDataAttrStruct *ptInDataAttr, void *pInData, void *pOutByteBuff);
static INT32 INT16ToINT16U(const T_OneDataAttrStruct *ptInDataAttr, void *pInData, void *pOutByteBuff);
//static INT32 INT16ToINT16S(const T_OneDataAttrStruct *ptInDataAttr, void *pInData, void *pOutByteBuff);
static INT32 INT16ToFP32(const T_OneDataAttrStruct *ptInDataAttr, void *pInData, void *pOutByteBuff);
static INT32 INT16ToTIME(const T_OneDataAttrStruct *ptInDataAttr, void *pInData, void *pOutByteBuff);
static INT32 INT16ToDATE(const T_OneDataAttrStruct *ptInDataAttr, void *pInData, void *pOutByteBuff);
static INT32 INT16ToCTRLDATA(const T_OneDataAttrStruct *ptInDataAttr, void *pInData, void *pOutByteBuff);
static INT32 BinarySearch(SHORT sInStart, SHORT sInEnd, WORD wInTargetAddr, const T_OneDataAttrStruct *ptInDataAttr);
static INT32 FindStartAddr(WORD wInTargetAddr, const T_OneDataAttrStruct *ptInDataAttr);
static BYTE *SelWritePart(BYTE *pucInSysPara, BYTE *pucInBmsPack, Enum_MdbsDataType ucInDataPart, UINT32 *ulInPartFlag);
static INT32 ExcWritePart(BYTE *pucInSysPara, BYTE *pucInBmsPack, UINT32 ulInPartFlag);
Static BOOLEAN JudgeIsAntithftCtrl(WORD wRegAddr);
Static BOOLEAN JudgeWriteRecLen(T_MdbsRtuApp *ptInApp);
#ifdef ZXESM_R321_APTOWER_VERSION
Static INT32 SendReadAnaAPT(T_MdbsRtuApp *ptInApp);
Static BOOLEAN SetBit(WORD *pData, BYTE pos, BYTE flag);
Static void GettCmpakDatavalue(void);
static void GetCmpakDataVlueRemain(void);
static INT32 ProcessLowAddr(T_MdbsRtuApp *ptInApp, BYTE *p, BYTE *pucByteCnt, WORD *wCnt);
static INT32 ProcessMidAddr(T_MdbsRtuApp *ptInApp, BYTE *p, BYTE *pucByteCnt, INT32 slFindStartAddrSn, WORD *wCnt);
static INT32 ProcessSpareAddr(T_MdbsRtuApp *ptInApp, BYTE *p, BYTE *pucByteCnt, INT32 slFindStartAddrSn, WORD *wCnt);
Static INT32 DealMdbsRtuCommandAPT(T_MdbsRtuApp *ptInData);
static BYTE JudgeErrorCode(T_MdbsRtuApp *ptApp);
#endif


#ifdef PAKISTAN_CMPAK_PROTOCOL
Static INT32 PacketOrParseCmpakTimeAna(const T_OneDataAttrStruct *ptInDataAttr,  E_DATA_DIRECTION eInDir, BYTE *pucOutData, BYTE *pucInRsvData);
Static BOOLEAN InitCmpakPara(T_CmpakStruct *ptCmpakPara);
Static BOOLEAN SetCmpakRegDefault(T_CmpakStruct *ptCmpakPara ,BYTE DataType, WORD RegContent);
Static BOOLEAN SetCmpakBit(WORD* p, BYTE BitAddr, BYTE Value);
Static BOOLEAN JudgeCmpakBattStatus(T_CmpakStruct *ptCmpakPara, T_BCMDataStruct *ptBcmData);
Static BOOLEAN ParseCmpakCellTemp(T_CmpakStruct *ptCmpakPara, T_BCMDataStruct *ptBcmData);
Static BOOLEAN ParseCmpakAlarmData(T_CmpakStruct *ptCmpakPara, T_BCMAlarmStruct *ptBCMAlarm, BYTE alarmtype);
Static BOOLEAN SetBmsName(T_CmpakStruct *ptCmpakPara);
static BOOLEAN HandleCriticalAlarm1(T_CmpakStruct *ptCmpakPara, T_BCMAlarmStruct *ptBCMAlarm, BOOLEAN status);
static BOOLEAN HandleCriticalAlarm2(T_CmpakStruct *ptCmpakPara, T_BCMAlarmStruct *ptBCMAlarm);
static BOOLEAN HandleMajorAlarm(T_CmpakStruct *ptCmpakPara, T_BCMAlarmStruct *ptBCMAlarm);
static BOOLEAN HandleMinorAlarm(T_CmpakStruct *ptCmpakPara, T_BCMAlarmStruct *ptBCMAlarm);
static BOOLEAN HandleModuleAlarm(T_CmpakStruct *ptCmpakPara, T_BCMAlarmStruct *ptBCMAlarm);
#endif


/****************************  变量声明********************************/
Static T_SpecialDataStruct s_tSpecialData;
Static T_MdbsRtuManageStruct s_tMdbsRtuManage;
static T_MdbsRtuLinkStruct s_tMdbsRtuLink;
Static T_MdbsRtuApp s_tMdbsRtuApp;
Static T_SysPara  s_tSysPara;
static T_SysPara  s_tSysParaTmp;
static UINT32 s_ulWritePartFlag = WRITE_DEFAULT;
static T_BmsPACKFactoryStruct s_tBmsPackFacInfo ={0,};
static BYTE s_aucBitTemp[MODBUSRTU_TEMP_LEN];
Static rt_timer_t s_waitModbusLedQuickNormalTimer = RT_NULL;
#ifdef ZXESM_R321_APTOWER_VERSION
Static T_CmpakSMUStruct s_tCmpakData;
#endif
const T_MdbsRtuFnucStruct s_atMdbsRtuSendFuncTable[] =
{
    #ifdef ZXESM_R321_APTOWER_VERSION
    {MODBUS_READ_ANA, SendReadAnaAPT, ANSWER_YES},
    #else
    //{MODBUS_READ_DIG, SendReadDig, ANSWER_YES},
    {MODBUS_READ_DIG_IN, SendReadDigIn, ANSWER_YES},
    {MODBUS_READ_ANA, SendReadAna, ANSWER_YES},
    {MODBUS_READ_ANA_IN, SendReadAnaIn, ANSWER_YES},
    {MODBUS_WRITE_DIG_SINGLE, WriteDigSingle, ANSWER_YES},
    {MODBUS_WRITE_ANA_SINGLE, WriteAnaSingle, ANSWER_YES},
    //{MODBUS_WRITE_DIG_MUL, WriteDigMul, ANSWER_YES},
    {MODBUS_WRITE_ANA_MUL, WriteAnaMul, ANSWER_YES},
    #endif
    {0x00, NULL, ANSWER_NO},
};
#define MDBSRTU_FUNC_NUM (sizeof(s_atMdbsRtuSendFuncTable) / sizeof(T_MdbsRtuFnucStruct))

//DATA_TYPE_VOID,	DATA_TYPE_BOOLEAN, DATA_TYPE_CHAR, DATA_TYPE_INT8U, DATA_TYPE_INT8S, DATA_TYPE_INT16U, DATA_TYPE_INT16S, 
//DATA_TYPE_INT32U, DATA_TYPE_INT32S, DATA_TYPE_FP32, DATA_TYPE_INT64S, DATA_TYPE_T_TIMESTRUCT, DATA_TYPE_T_DATESTRUCT, 
//DATA_TYPE_T_PROCTIMESTRUCT, DATA_TYPE_BIT, DATA_TYPE_CTRL_DATA,
const T_DataCovFuncStruct s_atDataCovFunc[MAX_COV_TYPE][MAX_DATA_TYPES] = 
{
    {{NULL}, {BOOLEANToINT16}, {CHARToINT16}, {INT8UToINT16}, {NULL}, {INT16UToINT16}, {NULL}, {NULL}, {NULL}, {FP32ToINT16}, {NULL}, {TIMEToINT16}, {DATEToINT16}, {NULL}, {NULL}, {NULL},}, 
    {{NULL}, {INT16ToBOOLEAN}, {INT16ToCHAR}, {INT16ToINT8U}, {NULL}, {INT16ToINT16U}, {NULL}, {NULL}, {NULL}, {INT16ToFP32}, {NULL}, {INT16ToTIME}, {INT16ToDATE}, {NULL}, {NULL}, {INT16ToCTRLDATA},},
};


#if defined(PAKISTAN_CMPAK_PROTOCOL)
const T_PacketOrParseFuncStruct s_atPacketOrParseFunc[] = 
{	
    {NULL}, {PacketOrParseBcmDataPartAna}, {NULL}, {NULL},
    {NULL}, {PacketOrParseSysParaPartAna}, {NULL}, {PacketOrParseBduFacPartAna},
    {PacketOrParseBcmFacPartAna}, {PacketOrParseSpecDataPartAna}, {PacketOrParseBmsPackPartAna},
    {PacketOrParsePackManuPartAna}, {PacketOrParseCmpakTimeAna},
};
#else
const T_PacketOrParseFuncStruct s_atPacketOrParseFunc[] = 
{	
    {NULL}, {PacketOrParseBcmDataPartAna}, {NULL}, {NULL},
    {NULL}, {PacketOrParseSysParaPartAna}, {NULL}, {PacketOrParseBduFacPartAna},
    {PacketOrParseBcmFacPartAna}, {PacketOrParseSpecDataPartAna}, {PacketOrParseBmsPackPartAna},
    {PacketOrParsePackManuPartAna},
};
#endif


static BYTE GetLowFromInt16(BYTE* pbuff)
{
    SHORT sTemp = GetInt16Data(pbuff);

    return (BYTE)(sTemp&0xff);
}

/****************************************************************************
* 函数名称：MdbsRtuCommMain
* 调    用：无
* 被 调 用：
* 输入参数：ptComm
* 返 回 值：无
* 功能描述：底层通信接收函数
* 作    者：刘斌
* 设计日期：2014-8-6
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
void MdbsRtuCommMain(T_CommStruct* ptComm)
{
    RT_ASSERT(ptComm != RT_NULL);
/*  
    中断接收超时判断，如果数据没有收讫,
    1200bps时，发送一个字节需要8.33ms,字节间超过300ms，需要重新搜索报头 ,是否应该关闭中断？
*/

    if (PROTOCOL_MODBUS != ptComm->ucPrtclType)
    {
        return;
    }
    if (!ptComm->bRecOk && ptComm->bRecReady 
        /* && (g_atComm[ucPort].ucRecTime > 3) */ )
    {
        Interface_ClearCommRecBuff(ptComm);
        // g_atComm[ucPort].ucRecTime  = 0;
    }
    //当数据发完后再处理接收到的数据
    if ( ( (0 == ptComm->wSendLength) 
        /* && (0 == g_atComm[ucPort].wSendIndex) */ )
        || ptComm->bSendOk )
    {
        /*处理接收到的数据*/
        if (ptComm->bRecOk)
        {
            s_tMdbsRtuManage.slPacketUseable = DealMdbsRtuCommData(ptComm->aucRecBuf, ptComm->wRecLength, NORTH_RS485_MODBUS, ptComm);
            /* 清空底层接收缓冲区 */
            Interface_ClearCommRecBuff(ptComm);
            rt_memset(&ptComm->aucRecBuf, 0, sizeof(ptComm->aucRecBuf));
            // NorthClearCommFail();
        }   
    }
    DealCommFail();

    return;
}

/*************************************************************************
*函数名  :  Interface_ClearCommRecBuff
*输入参数:  ptComm
*返回值  :  无
*功能描述:  清空485/232数据接收缓冲区
*************************************************************************/
void Interface_ClearCommRecBuff(T_CommStruct *ptComm)
{
    RT_ASSERT(ptComm != RT_NULL);

    if (SCI_PORT_NUM > ptComm->ucPortType)
    {
        ptComm->wRecIndex  = 0;  //清空底层接收缓冲区 
        ptComm->wRecLength = 0;  //清空底层接收缓冲区 
        ptComm->bRecReady  = False;
        ptComm->bRecOk     = False;
    }

    return;
}

/****************************************************************
函数名：DealCommFail
入口参数：
出口参数：
功能：
****************************************************************/
static INT32 DealCommFail(void)
{
    if (COMM_FAIL == JudgeCommFail())
    {
        if (s_tMdbsRtuManage.wCommFailCnt < COMM_FAIL_COUNT)
        {
            s_tMdbsRtuManage.wCommFailCnt++;
        }
    }
    else
    {
        s_tMdbsRtuManage.wCommFailCnt = 0;
        UpdateCommFailAlm();
    }

    s_tMdbsRtuManage.ucCommFail = (s_tMdbsRtuManage.wCommFailCnt >= COMM_FAIL_COUNT) ? 
                                  COMM_FAIL_STATE : COMM_NORMAL_STATE;

    if (COMM_FAIL_STATE == s_tMdbsRtuManage.ucCommFail)
    {
        SetErrorCode(ERRORCODE_FAIL_ASSOCIATED_DEV);
        UpdateCommFailAlm();
        InitMdbsRtu();
    }
    InitCommFailJudge();
    return APP_SUCCESS;
}

/****************************************************************
函数名：InitMdbsRtu
入口参数：
出口参数：
功能：初始化变量
****************************************************************/
void InitMdbsRtu(void)
{
    rt_memset(&s_tMdbsRtuManage, 0, sizeof(s_tMdbsRtuManage));
    rt_memset(&s_tMdbsRtuLink, 0, sizeof(s_tMdbsRtuLink));
    rt_memset(&s_tMdbsRtuApp, 0, sizeof(s_tMdbsRtuApp));

    s_tMdbsRtuManage.ucPort = COMM_RS485;
    s_tMdbsRtuManage.ptDataAttr = GetDataAttrPoint();
    s_tMdbsRtuManage.slDataAttrNum = GetDataAttrNum();
    s_tMdbsRtuManage.slTotalCnt = FindCntSn(s_tMdbsRtuManage.slDataAttrNum);
    InitCommFailJudge();

    return;    
}

/****************************************************************
函数名：FindCntSn
入口参数：slInAddrSn
出口参数：
功能：
****************************************************************/
Static INT32 FindCntSn(INT32 slInAddrSn)
{
    INT32 slTotalCnt = 0, i = 0;
    if (0 > slInAddrSn)
    {
        return APP_FAILURE;
    }
    else if (0 == slInAddrSn)
    {
        return 0;
    }
    else
    {
        for (i = 0; i < slInAddrSn; i++)
        {
            slTotalCnt += s_tMdbsRtuManage.ptDataAttr[i].ucNewDataLen;
        }
        slTotalCnt = slTotalCnt / 2;
    }

    return slTotalCnt;
}

/****************************************************************
函数名：InitCommFailJudge
入口参数：
出口参数：
功能：
****************************************************************/
static INT32 InitCommFailJudge(void)
{
    s_tMdbsRtuManage.slPacketUseable = FAILURE;
    s_tMdbsRtuManage.ucAnswerRtn = APP_RTN_INCORRECT;

    return APP_SUCCESS;
}

/****************************************************************
函数名：JudgeCommFail
入口参数：
出口参数：
功能：
****************************************************************/
static BOOLEAN JudgeCommFail(void)
{
    if (FAILURE == s_tMdbsRtuManage.slPacketUseable)
    {
        return COMM_FAIL;
    }
    if (APP_RTN_INCORRECT == s_tMdbsRtuManage.ucAnswerRtn)
    {
        return COMM_FAIL;
    }

    return COMM_NORMAL;
}

/****************************************************************
函数名：UpdateCommFailAlm
入口参数：
出口参数：
功能：
****************************************************************/
static INT32 UpdateCommFailAlm(void)
{
    return APP_SUCCESS;
}

// 获取出厂信息接口

Static BOOLEAN GetBduFactory(T_BduFactoryInfo *pFact)
{
    T_DCFactory tDcFactory;
    T_SoftwareCustomizedInfo tSoftwareCustomizedInfo = {0, };

    if (NULL == pFact)
    {
        return False;
    }

    GetBduFact(&tDcFactory);
    readSoftwareCustomizedInfo(&tSoftwareCustomizedInfo);

    if(CheckArrAllZero(tSoftwareCustomizedInfo.CustomizedBduRealseDate, sizeof(tSoftwareCustomizedInfo.CustomizedBduRealseDate)))
    {
        pFact->tSoftDate.wYear = (WORD)GetInt16Data(&tDcFactory.acVerDate[0]);//BDU真实软件版本日期
        pFact->tSoftDate.ucMonth = tDcFactory.acVerDate[2];
        pFact->tSoftDate.ucDay = tDcFactory.acVerDate[3];
    }
    else
    {
        pFact->tSoftDate.wYear = (WORD)GetInt16Data(&tSoftwareCustomizedInfo.CustomizedBduRealseDate[0]);//BDU客户个性化版本日期
        pFact->tSoftDate.ucMonth = tSoftwareCustomizedInfo.CustomizedBduRealseDate[2];
        pFact->tSoftDate.ucDay  = tSoftwareCustomizedInfo.CustomizedBduRealseDate[3];
    }

    rt_memcpy(&pFact->acName[0], &tDcFactory.acSysName[0], 30);
    rt_memcpy(&pFact->acDigControlVer[0], &tDcFactory.acPlatformVer[0], 8);

    if(CheckArrAllZero(tSoftwareCustomizedInfo.CustomizedBduVersion, sizeof(tSoftwareCustomizedInfo.CustomizedBduVersion)))
    {
        rt_memcpy_s(&pFact->acSoftVer[0], sizeof(pFact->acSoftVer), &tDcFactory.acSoftVer[0], 6);//BDU真实软件版本
    }
    else
    {
        rt_memcpy_s(&pFact->acSoftVer[0], sizeof(pFact->acSoftVer), &tSoftwareCustomizedInfo.CustomizedBduVersion[0], 6);//BDU客户个性化软件版本
    }

    rt_memcpy(&pFact->acBduAssetMagInfo[0], &tDcFactory.acAsset[0], 30);
    pFact->ulBduFacSn = tDcFactory.ulSN;

#ifdef GET_BDU_PARALL_UPDATE_FLAG
    pFact->bParallUpdateFlag = tDcFactory.bParalleUpdate;
#endif
    
    return True;
}


/****************************************************************************
* 函数名称：DealMdbsRtuCommData()
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：处理接收到的数据包
* 作    者  ：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/

INT32 DealMdbsRtuCommData(BYTE* pInBuff, WORD wInLen, BYTE ucInNorthType, T_CommStruct* ptComm)
{
    RT_ASSERT(ptComm != RT_NULL);

    INT32 slRet = APP_FAILURE;
    if (FAILURE == ConvertRecPhycomm2Link(pInBuff, wInLen, (Enum_NorthType)ucInNorthType))
    {
        return FAILURE;
    }
    if (FAILURE == CheckRecLink())
    {
        return FAILURE;
    }
    ConvertRecLink2App();
    slRet = CheckRecApp();
    if (FAILURE == slRet)
    {
        return FAILURE;
    }
    else if (ILL_DATA_ADDR_FAILURE == slRet)
    {
        SetErrorCode(ERRORCODE_ILL_DATA_ADDR);
    }
#if defined(PAKISTAN_CMPAK_PROTOCOL)
    else if(ILL_FUNC_FAILURE == slRet)
    {
        SetErrorCode(ERRORCODE_ILL_FUNC);
    }
    else if(ILL_DATA_VAL_FAILURE == slRet)
    {
        SetErrorCode(ERRORCODE_ILL_DATA_VAL);  
    }
#endif
    else
    {
        #ifndef ZXESM_R321_APTOWER_VERSION
        DealMdbsRtuCommand(&s_tMdbsRtuApp);
        #else
        DealMdbsRtuCommandAPT(&s_tMdbsRtuApp);
        #endif
        //通讯自动解锁
        DealDeviceUnlock(DEVICE_UNLOCK_COMMUN);
    }
    if (SUCCESSFUL == ConvertSendApp2Link())
    {
        ConvertSendLink2Phycomm(ptComm);
        slRet = SUCCESSFUL;
    }
    else
    {
        slRet = FAILURE;
    }

    if(WRITE_DEFAULT != s_ulWritePartFlag)
    {
        slRet = ExcWritePart((BYTE *)&s_tSysPara, (BYTE *)&s_tBmsPackFacInfo, s_ulWritePartFlag);
    }
    s_ulWritePartFlag = WRITE_DEFAULT;

    return slRet;
}


/****************************************************************************
* 函数名称：CheckRecLink()
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：检查接收到的链路层数据包
* 作    者  ：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
static INT32 CheckRecLink(void)
{
    // 确保最小长度,否则函数GetCRC16的第二个参数为负时,系统会死机
    if (MIN_MDBSREC_BYTE_CNT > s_tMdbsRtuLink.wRecLength) 
    {
        return FAILURE;
    }

    return SUCCESSFUL;
}

/****************************************************************
函数名：ConvertMdbsRtuPhycomm2Link
入口参数：
出口参数：
功能：将物理层数据包转换成链路层数据包
****************************************************************/
static INT32 ConvertRecPhycomm2Link(BYTE* pInBuff, WORD wInLen, Enum_NorthType ucInNorthType)
{
    // 接口定义的都在底层通讯线程内调用
    // 暂时不加锁，如果promain在别的线程内调用则需加锁
    if (NULL == pInBuff || wInLen > sizeof(s_tMdbsRtuLink.aucRecBuff))
    {
        return FAILURE;     // 超过大小新包不接收
    }

    rt_memcpy(s_tMdbsRtuLink.aucRecBuff, pInBuff, wInLen);
    s_tMdbsRtuLink.wRecLength = wInLen;
    s_tMdbsRtuManage.ucNorthType = ucInNorthType;
    
    return SUCCESSFUL;
}

/****************************************************************************
* 函数名称：ConvertRecLink2App()
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：将链路层数据包转换成应用层数据包
* 作    者  ：fsg
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
static INT32 ConvertRecLink2App(void)
{
    rt_memcpy(s_tMdbsRtuApp.aucRecBuf, &s_tMdbsRtuLink.aucRecBuff, s_tMdbsRtuLink.wRecLength);
    s_tMdbsRtuApp.ucFunc = s_tMdbsRtuLink.aucRecBuff[MODBUSRTU_REC_FUNC_OFFSET];
    //来自can数据帧的压力，需要特别处理长度
    if(MODBUS_WRITE_DIG_MUL == s_tMdbsRtuApp.ucFunc || MODBUS_WRITE_ANA_MUL == s_tMdbsRtuApp.ucFunc)
    {
        s_tMdbsRtuApp.wRecLen = MIN_MDBSREC_BYTE_CNT + s_tMdbsRtuLink.aucRecBuff[MODBUSRTU_REC_BYTECNT_OFFSET] + 1;
    }
    else if(MODBUS_WRITE_DIG_SINGLE == s_tMdbsRtuApp.ucFunc)
    {
        s_tMdbsRtuApp.wRecLen = s_tMdbsRtuLink.wRecLength;
    }
    else
    {
        s_tMdbsRtuApp.wRecLen = MIN_MDBSREC_BYTE_CNT;
    }
    s_tMdbsRtuApp.ucDevType = s_tMdbsRtuLink.aucRecBuff[MODBUSRTU_REC_DEVTYPE_OFFSET];
    s_tMdbsRtuApp.wRegAddr = (WORD)GetInt16Data(&s_tMdbsRtuLink.aucRecBuff[MODBUSRTU_REC_REGADDR_OFFSET]);
    if (MODBUS_WRITE_ANA_SINGLE == s_tMdbsRtuApp.ucFunc)
    {
        s_tMdbsRtuApp.wRegCnt = DEFAULT_WRITE_ANA_SINGLE_CNT;
    }
    else if (MODBUS_WRITE_DIG_SINGLE == s_tMdbsRtuApp.ucFunc)
    {
        s_tMdbsRtuApp.wRegCnt = DEFAULT_WRITE_DIG_SINGLE_CNT;
    }
    else
    {
        s_tMdbsRtuApp.wRegCnt  = (WORD)GetInt16Data(&s_tMdbsRtuLink.aucRecBuff[MODBUSRTU_REC_REGCNT_OFFSET]);
    }
    s_tMdbsRtuApp.wCRC = (s_tMdbsRtuLink.aucRecBuff[s_tMdbsRtuApp.wRecLen - 1] << 8) + s_tMdbsRtuLink.aucRecBuff[s_tMdbsRtuApp.wRecLen - 2];
    
    return SUCCESSFUL;
}

/****************************************************************************
* 函数名称：CheckRecApp()
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：检查应用层数据包
* 作    者  ：fsg
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/

#if defined(PAKISTAN_CMPAK_PROTOCOL)
Static INT32 CheckRecApp(void)
{
    GetSysPara(&s_tSysPara);
#ifndef ZXESM_R321_APTOWER_VERSION
    WORD wCRC = 0;
    BYTE ucBmsAddr = GetBMSAddr() + s_tSysPara.ucModbusAddr;
    if (s_tMdbsRtuApp.wRecLen > 2)
    {
        wCRC = GetCRC16(s_tMdbsRtuApp.aucRecBuf, s_tMdbsRtuApp.wRecLen - 2);
    }
#endif

    if(((s_tMdbsRtuApp.ucDevType != ucBmsAddr) && (s_tMdbsRtuApp.ucDevType != getGrpAddr())) || (s_tMdbsRtuApp.ucDevType > 247) || (s_tMdbsRtuApp.ucDevType < 224) || (s_tMdbsRtuApp.ucDevType > 231)) // CMPAK协议地址约束
    {
        return FAILURE;
    }

    if((0x03 != s_tMdbsRtuApp.ucFunc) && (0x06 != s_tMdbsRtuApp.ucFunc) && (0x10 != s_tMdbsRtuApp.ucFunc)) // CMPAK协议命令码约束
    {
        return ILL_FUNC_FAILURE;
    }
    
    if (MAX_MDBSREC_BYTE_CNT < s_tMdbsRtuApp.wRecLen)
    {
        return FAILURE;
    }

    if (False == IsAddrLegal())
    {
        return ILL_DATA_ADDR_FAILURE;
    }

    if (wCRC != s_tMdbsRtuApp.wCRC)
    {
        return ILL_DATA_VAL_FAILURE;
    }
  
    return SUCCESSFUL;
}
#else
static INT32 CheckRecApp(void)
{
#ifndef ZXESM_R321_APTOWER_VERSION
    WORD wCRC = 0;

    if (s_tMdbsRtuApp.wRecLen > 2)
    {
        wCRC = GetCRC16(s_tMdbsRtuApp.aucRecBuf, s_tMdbsRtuApp.wRecLen - 2);
    }
    if (wCRC != s_tMdbsRtuApp.wCRC)
    {
        return FAILURE;
    }
#endif

    GetSysPara(&s_tSysPara);
   
    #ifdef MODBUS_BASE_ADDR_ENABLE
    if((s_tMdbsRtuApp.ucDevType != GetTotalAddr() + s_tSysPara.ucModbusAddr) && (s_tMdbsRtuApp.ucDevType != getGrpAddr()))
    #else
    if((s_tMdbsRtuApp.ucDevType != GetBMSAddr()) && (s_tMdbsRtuApp.ucDevType != getGrpAddr()))
    #endif
    {
        return FAILURE;
    }
    /*if(s_tMdbsRtuApp.ucDevType != s_tBoardInfo.ucDevType + s_tMdbsRtuManage.ucBoardAddr)
    {
        return FAILURE;
    }*/    
    if (MAX_MDBSREC_BYTE_CNT < s_tMdbsRtuApp.wRecLen)
    {
        return FAILURE;
    }
    if (False == IsAddrLegal())
    {
        return ILL_DATA_ADDR_FAILURE;
    }

    return SUCCESSFUL;
}
#endif


/****************************************************************************
* 函数名称：DealMdbsRtuCommand()
* 调    用：无
* 被 调 用：
* 输入参数：ptInData-应用层数据包
* 返 回 值：
* 功能描述：处理命令
* 作    者  ：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
static INT32 DealMdbsRtuCommand(T_MdbsRtuApp *ptInData)
{
	Enum_MdbsRTN ucRTN = APP_RTN_CORRECT;
    WORD i = 0;
    T_MdbsRtuApp *ptApp = ptInData;
    INT32 slFrameLen = APP_FAILURE; //转化出来的数据域长度

    if (NULL == ptInData || NULL == s_tMdbsRtuManage.ptDataAttr || 1 > s_tMdbsRtuManage.slDataAttrNum)
    {
        return APP_FAILURE;
    }

    while (i < sizeof(s_atMdbsRtuSendFuncTable) / sizeof(s_atMdbsRtuSendFuncTable[0])
           && s_atMdbsRtuSendFuncTable[i].func)
    {
        if (s_atMdbsRtuSendFuncTable[i].ucFunc== ptApp->ucFunc)
        {
            slFrameLen = (*s_atMdbsRtuSendFuncTable[i].func)(ptInData);
            if ((ANSWER_NO == s_atMdbsRtuSendFuncTable[i].EAnswer) 
                || (ptInData->ucDevType == 0))
            {
                SetSndAnswer(APP_RTN_NO_ANSWER);
                return FAILURE;
            }
            break;
        }
        i++;
    }

    ucRTN = slFrameLen > APP_FAILURE ? APP_RTN_CORRECT : APP_RTN_INCORRECT;
    SetSndAnswer(ucRTN);
    if (ucRTN == APP_RTN_INCORRECT)
    {
        if (MDBSRTU_FUNC_NUM - 1 > i)
        {
            SetErrorCode(ERRORCODE_ILL_DATA_VAL);
        }
        else
        {
            SetErrorCode(ERRORCODE_ILL_FUNC);
        }
    }
    s_tMdbsRtuApp.ucNeedRespond = ANSWER_YES;
    //数据长度和帧尾
    s_tMdbsRtuApp.wSendLen = slFrameLen < APP_SUCCESS ? 0 : slFrameLen;

    //将协议包转换成待发送的底层数据
    return  SUCCESSFUL;
}

/***************************************************************************
* 函数名称：CovertSendApp2Link()
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：将应用层数据包转换成链路层数据包
* 作    者：fsg
* 设计日期：2010-01-24
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
static INT32 ConvertSendApp2Link(void)
{
    BYTE* p = s_tMdbsRtuLink.aucSendBuff;
    WORD wCRC = 0;
    INT32 slRet = APP_FAILURE;
    WORD wLength = 0;

    rt_memset(s_tMdbsRtuLink.aucSendBuff, 0, sizeof(s_tMdbsRtuLink.aucSendBuff));
    *p++ = s_tMdbsRtuApp.ucDevType;
    wLength++;
    switch (s_tMdbsRtuManage.ucAnswerRtn)
    {
    case APP_RTN_CORRECT:
        slRet = ConvertCorrSendApp2Link(p);
        if (APP_FAILURE == slRet)
        {
            return FAILURE;
        }
        break;
    case APP_RTN_INCORRECT:
        slRet = ConvertIncorrSendApp2Link(p);
        if (APP_FAILURE == slRet)
        {
            return FAILURE;
        }
        break;
    default:
        return FAILURE;
    }
    p += slRet;
    wLength += slRet;
    // GetCRC
    wCRC = GetCRC16(s_tMdbsRtuLink.aucSendBuff, wLength);
    *p++ = (BYTE)wCRC; //先低字节,后高字节
    *p++ = (BYTE)(wCRC >> 8);
    wLength += 2;
    s_tMdbsRtuLink.wSendLength = wLength;

    return SUCCESSFUL;
}

/***************************************************************************
* 函数名称：CovertSendLink2Phycomm()
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：将链路层数据包转换成物理层数据包
* 作    者：fsg
* 设计日期：2010-01-24
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
static INT32 ConvertSendLink2Phycomm(T_CommStruct* ptComm)
{
    RT_ASSERT(ptComm != RT_NULL);

    if ((s_tMdbsRtuLink.wSendLength <= LEN_COMM_SEND_BUF) && (s_tMdbsRtuLink.wSendLength <= sizeof(s_tMdbsRtuLink.aucSendBuff)))
    {
        rt_memset(ptComm->aucSendBuf, 0x0, LEN_COMM_SEND_BUF);  
        rt_memcpy(ptComm->aucSendBuf, s_tMdbsRtuLink.aucSendBuff, s_tMdbsRtuLink.wSendLength);
        ptComm->wSendLength = s_tMdbsRtuLink.wSendLength;
        if(NORTH_RS485_MODBUS == s_tMdbsRtuManage.ucNorthType)
        {    // 写串口
            ptComm->bSendOk = False;
            sendCommData(ptComm);
        }
        return SUCCESSFUL;
    }

    return FAILURE;
}

/****************************************************************
函数名：FillInByteToBitBuff
入口参数：
出口参数：
功能：
****************************************************************/
static INT32 FillInByteToBitBuff(BYTE* pucOutData, INT32 slInNum, BYTE* pucInData)
{
    BYTE ucTemp = 0, *pTempByte = pucInData, *p = pucOutData, *pucOutDataStart = pucOutData;
    WORD i = 0;

    if (NULL == pucOutData || NULL == pucInData || 1 > slInNum)
    {
        return APP_FAILURE;
    }

    for (i = 0; i < slInNum; i++)
    {
        if (i > 0 && i % BIT_NUM_IN_ONE_BYTE == 0)
        {
            *p = ucTemp;
            ucTemp = 0;
            p++;
        }
        if (*pTempByte)
        {
            ucTemp += (1 << (i % BIT_NUM_IN_ONE_BYTE));
        }
        pTempByte++;        
    }
    *p++ = ucTemp;

    return p - pucOutDataStart;
}

/****************************************************************
函数名：CoBitByteWordToByteBuff
入口参数：
出口参数：
功能：
****************************************************************/
static INT32 CoBitByteWordToByteBuff(const T_OneDataAttrStruct* ptInDataAttr, BYTE* pucOutData)
{
    BYTE *pTemp = NULL, k = 0, *p = NULL, *pucAlarm = NULL;//ucShiftBitNum = 0, 
    //WORD wTemp = 0;
    INT32 slNum = APP_SUCCESS;

    T_BCMAlarmStruct tBCMAlarm = {0,};
    if (NULL == pucOutData || NULL == ptInDataAttr)
    {
        return APP_FAILURE;
    }
    p = pucOutData;
    rt_memset((BYTE*)&tBCMAlarm,0,sizeof(T_BCMAlarmStruct));
    GetRealAlarm(BCM_ALARM, (BYTE*)&tBCMAlarm);
    pucAlarm = (BYTE*)&tBCMAlarm;
    pTemp = (BYTE*)(pucAlarm + ptInDataAttr->wOldDataOffset);
    switch (ptInDataAttr->ucOldDataType)
    {
    case DATA_TYPE_BIT:
        k =  ptInDataAttr->ucOldDataLen;
        while (k > 0)
        {
            k--;
            p[slNum] = (*pTemp >> k) & 0x01;
            slNum++;
        }
        break;
    case DATA_TYPE_INT8U:
        for (k = 0; k < BIT_NUM_EIGHT; k++)
        {
            p[slNum] = (*pTemp >> k) & 0x01;
            slNum++;
        }
        break;
    /*case DATA_TYPE_INT16U:
        wTemp = *(WORD*)pTemp;
        for (k = 0; k < BIT_NUM_SIXTEEN; k++)
        {
            ucShiftBitNum = (k < BIT_NUM_EIGHT) ? (k + BIT_NUM_EIGHT) : (k - BIT_NUM_EIGHT);
            p[slNum] = (wTemp >> ucShiftBitNum) & 0x01;
            slNum++;
        }
        break;*/
    default:
        return APP_FAILURE;
    }

    return slNum;
}

/****************************************************************
函数名：PacketOrParseBcmDataPartAna
入口参数：
出口参数：
功能：降低圈复杂度
****************************************************************/

static INT32 PacketOrParseBcmDataPartAna(const T_OneDataAttrStruct* ptInDataAttr,  E_DATA_DIRECTION eInDir, BYTE* pucOutData, BYTE* pucInRsvData)
{
    BYTE* pMemberAddr = NULL;
#ifndef ZXESM_R321_APTOWER_VERSION
    T_BCMDataStruct tBCMData;
    rt_memset(&tBCMData, 0x00, sizeof(T_BCMDataStruct));  
#endif
#if defined(PAKISTAN_CMPAK_PROTOCOL)
    T_CmpakStruct tCmpakData;
    rt_memset(&tCmpakData, 0x00, sizeof(T_CmpakStruct));
#endif
    
    if (NULL == pucOutData || NULL == ptInDataAttr)
    {
        return APP_FAILURE;
    }
#if defined(PAKISTAN_CMPAK_PROTOCOL)
    if ((BCM_PART_DATA != ptInDataAttr->ucOldDataPart && CMPAK_TIME_PART != ptInDataAttr->ucOldDataPart)|| MAX_DATA_TYPES <= ptInDataAttr->ucOldDataType || NULL == s_atDataCovFunc[eInDir][ptInDataAttr->ucOldDataType].Func)
    {
        return APP_FAILURE;
    }

    if(BCM_PART_DATA == ptInDataAttr->ucOldDataPart)
    {
        InitCmpakPara(&tCmpakData);
        pMemberAddr = (BYTE *)&tCmpakData;
        return (*s_atDataCovFunc[eInDir][ptInDataAttr->ucOldDataType].Func)(ptInDataAttr, (void *)(pMemberAddr + ptInDataAttr->wOldDataOffset), pucOutData);
    }
    else
    {
        GetTime(&s_tSpecialData.tTime);
        pMemberAddr = (BYTE *)&s_tSpecialData.tTime;
        return (*s_atDataCovFunc[eInDir][ptInDataAttr->ucOldDataType].Func)(ptInDataAttr, (void *)(pMemberAddr + ptInDataAttr->wOldDataOffset), pucOutData);
    }
#else
    if (BCM_PART_DATA != ptInDataAttr->ucOldDataPart || MAX_DATA_TYPES <= ptInDataAttr->ucOldDataType || NULL == s_atDataCovFunc[eInDir][ptInDataAttr->ucOldDataType].Func)
    {
        return APP_FAILURE;
    }
    #ifndef ZXESM_R321_APTOWER_VERSION
    GetRealData(&tBCMData);
    pMemberAddr = (BYTE *)&tBCMData;
    #else
    pMemberAddr = (BYTE *)&s_tCmpakData;
    #endif

    return (*s_atDataCovFunc[eInDir][ptInDataAttr->ucOldDataType].Func)(ptInDataAttr, (void *)(pMemberAddr + ptInDataAttr->wOldDataOffset), pucOutData);
#endif
}


/****************************************************************
函数名：PacketOrParseBduFacPartAna
入口参数：
出口参数：
功能：降低圈复杂度
****************************************************************/
static INT32 PacketOrParseBduFacPartAna(const T_OneDataAttrStruct* ptInDataAttr,  E_DATA_DIRECTION eInDir, BYTE* pucOutData, BYTE* pucInRsvData)
{
    BYTE* pMemberAddr = NULL;
    T_BduFactoryInfo tBduFactory;
    
    if (NULL == pucOutData || NULL == ptInDataAttr)
    {
        return APP_FAILURE;
    }
    if (BDU_FAC_PART != ptInDataAttr->ucOldDataPart || MAX_DATA_TYPES <= ptInDataAttr->ucOldDataType || NULL == s_atDataCovFunc[eInDir][ptInDataAttr->ucOldDataType].Func)
    {
        return APP_FAILURE;
    }

    rt_memset_s((BYTE*)&tBduFactory, sizeof(T_BduFactoryInfo), 0x00, sizeof(T_BduFactoryInfo));

    GetBduFactory(&tBduFactory);
		
    pMemberAddr = (BYTE*)&tBduFactory;
    
    return (*s_atDataCovFunc[eInDir][ptInDataAttr->ucOldDataType].Func)(ptInDataAttr, (void*)(pMemberAddr + ptInDataAttr->wOldDataOffset), pucOutData);
}

#ifdef MODBUS_BASE_ADDR_ENABLE

Static void GetBcmFacInfoC2(T_BCMFactoryC2Struct* tBmsFacInfoC2) {
    T_BmsPACKFactoryStruct  tBmsPACKFactory;
    T_BmsInfoStruct tBmsInfo;
    BYTE softwareTag[CUSTOMIZED_INFO_LEN] = {0,};
    T_SoftwareCustomizedInfo tSoftwareCustomizedInfo = {0, };
    rt_memset_s(&tBmsInfo, sizeof(T_BmsInfoStruct), 0x00, sizeof(T_BmsInfoStruct));
    rt_memset_s((BYTE *)&tBmsPACKFactory, sizeof(tBmsPACKFactory), 0x00, sizeof(tBmsPACKFactory));

    readBMSInfofact(&tBmsInfo);
    readSoftwareCustomizedInfo(&tSoftwareCustomizedInfo);
    GetSoftwareTagInfo(softwareTag, CUSTOMIZED_INFO_LEN); //计算软件标识码
    rt_strncpy_s(tBmsFacInfoC2->acBmsTypeName,sizeof(tBmsFacInfoC2->acBmsTypeName), g_ProConfig.chBMSType, sizeof(tBmsFacInfoC2->acBmsTypeName)-1);//电池型号10字节
    rt_strncpy_s(tBmsFacInfoC2->acCorpName,sizeof(tBmsFacInfoC2->acCorpName), tBmsInfo.acBattCorpName, sizeof(tBmsFacInfoC2->acCorpName)-1); //制造厂家
    readBmsPackFacInfo(&tBmsPACKFactory);
    rt_memcpy((BYTE *)&(tBmsFacInfoC2->acProductNoFront[0]), (BYTE *)&(tBmsPACKFactory.acDeviceSn[0]), sizeof(tBmsFacInfoC2->acProductNoFront)); //序列号
    rt_memcpy((BYTE *)&(tBmsFacInfoC2->acProductNoBack[0]), (BYTE *)&(tBmsPACKFactory.acDeviceSn[8]), sizeof(tBmsPACKFactory.acDeviceSn) - 8);  

    if(CheckArrAllZero(tSoftwareCustomizedInfo.CustomizedBmsVersion, sizeof(tSoftwareCustomizedInfo.CustomizedBmsVersion)))
    {
        rt_strncpy_s(tBmsFacInfoC2->acVersion, sizeof(tBmsFacInfoC2->acVersion), BMS_VER, 19);
    }
    else
    {
        rt_strncpy_s(tBmsFacInfoC2->acVersion, sizeof(tBmsFacInfoC2->acVersion), (char *)tSoftwareCustomizedInfo.CustomizedBmsVersion, 19);
    }

    rt_memcpy((BYTE *)&(tBmsFacInfoC2->acVer[0]), tBmsFacInfoC2->acVersion, sizeof(tBmsFacInfoC2->acVer));//软件版本2字节

    if(CheckArrAllZero(tSoftwareCustomizedInfo.CustomizedBmsReleaseDate, sizeof(tSoftwareCustomizedInfo.CustomizedBmsReleaseDate)))
    {
        tBmsFacInfoC2->tSoftDate.wYear = SOFTWARE_RELEASE_YEAR;//BMS客户真实版本日期
        tBmsFacInfoC2->tSoftDate.ucMonth = SOFTWARE_RELEASE_MONTH;
        tBmsFacInfoC2->tSoftDate.ucDay = SOFTWARE_RELEASE_DATE;
    }
    else
    {
        tBmsFacInfoC2->tSoftDate.wYear = (WORD)GetInt16Data(&tSoftwareCustomizedInfo.CustomizedBmsReleaseDate[0]);//BMS客户个性化版本日期
        tBmsFacInfoC2->tSoftDate.ucMonth = tSoftwareCustomizedInfo.CustomizedBmsReleaseDate[2];
        tBmsFacInfoC2->tSoftDate.ucDay  = tSoftwareCustomizedInfo.CustomizedBmsReleaseDate[3];
    }

    rt_memcpy(tBmsFacInfoC2->acBootDate, (BYTE *)BOOT_VER_START, 10);
    rt_strncpy_s(tBmsFacInfoC2->acCorpNameNew, sizeof(tBmsFacInfoC2->acCorpNameNew),tBmsInfo.acBattCorpName, 19);
    rt_strncpy_s(tBmsFacInfoC2->acBmsTypeNameNew, sizeof(tBmsFacInfoC2->acBmsTypeNameNew), g_ProConfig.chBMSType, 19);
    rt_strncpy_s(tBmsFacInfoC2->acHardwareVersion, sizeof(tBmsFacInfoC2->acHardwareVersion),BMS_HARDWARE_VERSION, 19);
    GetBmsSysName(tBmsFacInfoC2->acSysNameNew, sizeof(tBmsFacInfoC2->acSysNameNew));
#ifdef GET_NUMOFCELL_AND_TYPEOFBMS_ENABLED
    tBmsFacInfoC2->ucNumofCell = CELL_VOL_NUM;
    tBmsFacInfoC2->ucTypeofBms = SMART_LI;
#endif    
    rt_memcpy_s(tBmsFacInfoC2->acSoftwareTag, sizeof(tBmsFacInfoC2->acSoftwareTag), softwareTag, sizeof(softwareTag));
    rt_memcpy_s(tBmsFacInfoC2->acHardwareInfo, sizeof(tBmsFacInfoC2->acHardwareInfo), (char *)&tBmsInfo.acHardwareInfo[0], sizeof(tBmsInfo.acHardwareInfo));
    return;
}

#endif /* MODBUS_BASE_ADDR_ENABLE */


Static void GetBcmFacInfo(T_BCMFactoryStruct* tBmsFacInfo)
{
    T_BmsInfoStruct tBmsInfo;
    T_SoftwareCustomizedInfo tSoftwareCustomizedInfo = {0, };
    BYTE softwareTag[CUSTOMIZED_INFO_LEN] = {0,};
    rt_memset_s(&tBmsInfo, sizeof(T_BmsInfoStruct), 0x00, sizeof(T_BmsInfoStruct));

    readBMSInfofact(&tBmsInfo);
    GetBmsSysName(tBmsFacInfo->acSysName, sizeof(tBmsFacInfo->acSysName));
    readSoftwareCustomizedInfo(&tSoftwareCustomizedInfo);
    GetSoftwareTagInfo(softwareTag, CUSTOMIZED_INFO_LEN); //计算软件标识码

#ifdef SOFTBANK_VERSION_BUILD
    rt_strncpy_s(tBmsFacInfo->acVersion, sizeof(tBmsFacInfo->acVersion), BMS_VER_SOFTBANK, 19);
#else
    if(CheckArrAllZero(tSoftwareCustomizedInfo.CustomizedBmsVersion, sizeof(tSoftwareCustomizedInfo.CustomizedBmsVersion)))
    {
        rt_strncpy_s(tBmsFacInfo->acVersion, sizeof(tBmsFacInfo->acVersion), BMS_VER, 19);
    }
    else
    {
        rt_strncpy_s(tBmsFacInfo->acVersion, sizeof(tBmsFacInfo->acVersion), (char *)tSoftwareCustomizedInfo.CustomizedBmsVersion, 19);
    }
#endif

    rt_strncpy_s(tBmsFacInfo->acCorpName, sizeof(tBmsFacInfo->acCorpName), tBmsInfo.acBattCorpName, 19);

    if(CheckArrAllZero(tSoftwareCustomizedInfo.CustomizedBmsReleaseDate, sizeof(tSoftwareCustomizedInfo.CustomizedBmsReleaseDate)))
    {
        tBmsFacInfo->tSoftDate.wYear = SOFTWARE_RELEASE_YEAR; // BMS客户真实版本日期
        tBmsFacInfo->tSoftDate.ucMonth = SOFTWARE_RELEASE_MONTH;
        tBmsFacInfo->tSoftDate.ucDay = SOFTWARE_RELEASE_DATE;
    }
    else
    {
        tBmsFacInfo->tSoftDate.wYear = (WORD)GetInt16Data(&tSoftwareCustomizedInfo.CustomizedBmsReleaseDate[0]); // BMS客户个性化版本日期
        tBmsFacInfo->tSoftDate.ucMonth = tSoftwareCustomizedInfo.CustomizedBmsReleaseDate[2];
        tBmsFacInfo->tSoftDate.ucDay = tSoftwareCustomizedInfo.CustomizedBmsReleaseDate[3];
    }

    rt_strncpy_s(tBmsFacInfo->acBmsTypeName, sizeof(tBmsFacInfo->acBmsTypeName), g_ProConfig.chBMSType, 19);
    rt_memcpy(tBmsFacInfo->acBootDate, (BYTE *)BOOT_VER_START, 10);
    rt_strncpy_s(tBmsFacInfo->acCorpNameNew, sizeof(tBmsFacInfo->acCorpNameNew), tBmsInfo.acBattCorpName, 19);
    rt_strncpy_s(tBmsFacInfo->acBmsTypeNameNew, sizeof(tBmsFacInfo->acBmsTypeNameNew), g_ProConfig.chBMSType, 19);
    rt_strncpy_s(tBmsFacInfo->acHardwareVersion, sizeof(tBmsFacInfo->acHardwareVersion), BMS_HARDWARE_VERSION, 19);
    GetBmsSysName(tBmsFacInfo->acSysNameNew, sizeof(tBmsFacInfo->acSysNameNew));

#ifdef GET_NUMOFCELL_AND_TYPEOFBMS_ENABLED
    tBmsFacInfo->ucNumofCell = CELL_VOL_NUM;
    tBmsFacInfo->ucTypeofBms = SMART_LI;
#endif

    rt_memcpy_s(tBmsFacInfo->acSoftwareTag, sizeof(tBmsFacInfo->acSoftwareTag), (char *)softwareTag, sizeof(softwareTag));
    rt_memcpy_s(tBmsFacInfo->acHardwareInfo, sizeof(tBmsFacInfo->acHardwareInfo), (char *)&tBmsInfo.acHardwareInfo[0], sizeof(tBmsInfo.acHardwareInfo));
    return;
}


/****************************************************************
函数名：PacketOrParseBcmFacPartAna
入口参数：
出口参数：
功能：降低圈复杂度
****************************************************************/
static INT32 PacketOrParseBcmFacPartAna(const T_OneDataAttrStruct* ptInDataAttr,  E_DATA_DIRECTION eInDir, BYTE* pucOutData, BYTE* pucInRsvData)
{
    BYTE* pMemberAddr = NULL;
    T_BCMFactoryStruct tBmsFacInfo = {{'\0',},};
#ifdef MODBUS_BASE_ADDR_ENABLE
    T_BCMFactoryC2Struct tBmsFacInfoC2 = {{'\0',},};
#endif

    if (NULL == pucOutData || NULL == ptInDataAttr)
    {
        return APP_FAILURE;
    }
    if (BCM_FAC_PART != ptInDataAttr->ucOldDataPart || MAX_DATA_TYPES <= ptInDataAttr->ucOldDataType || NULL == s_atDataCovFunc[eInDir][ptInDataAttr->ucOldDataType].Func)
    {
        return APP_FAILURE;
    }
#ifdef MODBUS_BASE_ADDR_ENABLE
    if(s_tSysPara.ucModbusAddr == 0) {
        GetBcmFacInfo(&tBmsFacInfo);
        pMemberAddr = (BYTE *)&tBmsFacInfo;
    } else {       
        GetBcmFacInfoC2(&tBmsFacInfoC2);
        pMemberAddr = (BYTE *)&tBmsFacInfoC2;
    }
#else
    GetBcmFacInfo(&tBmsFacInfo);
    pMemberAddr = (BYTE *)&tBmsFacInfo;
#endif
    
    return (*s_atDataCovFunc[eInDir][ptInDataAttr->ucOldDataType].Func)(ptInDataAttr, (void *)(pMemberAddr + ptInDataAttr->wOldDataOffset), pucOutData);
}

/****************************************************************
函数名：PacketOrParseBduFacPartAna
入口参数：
出口参数：
功能：降低圈复杂度
****************************************************************/
static INT32 PacketOrParseSysParaPartAna(const T_OneDataAttrStruct* ptInDataAttr,  E_DATA_DIRECTION eInDir, BYTE* pucOutData, BYTE* pucInRsvData)
{
    BYTE* pMemberAddr = NULL;
    
    if (NULL == pucOutData || NULL == ptInDataAttr|| (SYS_PARA == ptInDataAttr->ucOldDataPart && NULL == pucInRsvData))
    {
        return APP_FAILURE;
    }
    if (SYS_PARA != ptInDataAttr->ucOldDataPart || MAX_DATA_TYPES <= ptInDataAttr->ucOldDataType|| NULL == s_atDataCovFunc[eInDir][ptInDataAttr->ucOldDataType].Func)
    {
        return APP_FAILURE;
    }
    
    pMemberAddr = pucInRsvData;

    return (*s_atDataCovFunc[eInDir][ptInDataAttr->ucOldDataType].Func)(ptInDataAttr, (void*)(pMemberAddr + ptInDataAttr->wOldDataOffset), pucOutData);
}

/****************************************************************
函数名：PacketOrParseSpecDataPartAna
入口参数：
出口参数：
功能：降低圈复杂度
****************************************************************/
static INT32 PacketOrParseSpecDataPartAna(const T_OneDataAttrStruct* ptInDataAttr,  E_DATA_DIRECTION eInDir, BYTE* pucOutData, BYTE* pucInRsvData)
{
    INT32 slRet = APP_FAILURE;
    BYTE* pMemberAddr = NULL;
    T_ProtocolSetInfoStruct tProData;
    struct tm tTime;
    time_t t;

    if (NULL == ptInDataAttr)
    {
        return APP_FAILURE;
    }
    if (SPECIAL_DATA_PART != ptInDataAttr->ucOldDataPart || MAX_DATA_TYPES <= ptInDataAttr->ucOldDataType || NULL == s_atDataCovFunc[eInDir][ptInDataAttr->ucOldDataType].Func)
    {
        return APP_FAILURE;
    }

    t = time(RT_NULL);
    localtime_r(&t, &tTime);
    s_tSpecialData.tTime.wYear    = tTime.tm_year + 1900;
    s_tSpecialData.tTime.ucMonth  = tTime.tm_mon + 1;
    s_tSpecialData.tTime.ucDay    = tTime.tm_mday;
    s_tSpecialData.tTime.ucHour   = tTime.tm_hour;
    s_tSpecialData.tTime.ucMinute = tTime.tm_min;
    s_tSpecialData.tTime.ucSecond = tTime.tm_sec;

    GetValueFromProtocol(INFO_TYPE_OUT_VOLT, &tProData);
    s_tSpecialData.fOutputVolt = tProData.sValue / 100.0;
    GetValueFromProtocol(INFO_TYPE_CHG_COEF, &tProData);
    s_tSpecialData.fChgCurrCoeff = tProData.sValue / 100.0;
    GetValueFromProtocol(INFO_TYPE_DISCH_VOLT_FREE, &tProData);
    s_tSpecialData.fOutputVoltFree = tProData.sValue / 100.0;
    GetValueFromProtocol(INFO_TYPE_CHG_COEF_FREE, &tProData);
    s_tSpecialData.fChgCurrCoeffFree = tProData.sValue / 1000.0;

    pMemberAddr = (BYTE*)&s_tSpecialData;

    if (JudgeIsAntithftCtrl(ptInDataAttr->wRegAddr) && ptInDataAttr->wRegAddr != 2175)
    {
        rt_memcpy_s(pMemberAddr + ptInDataAttr->wOldDataOffset, NETANTITHEFT_KEY_LEN, pucOutData + 2, NETANTITHEFT_KEY_LEN);
    }
    else if (ptInDataAttr->wRegAddr == 2175)
    {
        rt_memcpy_s(pMemberAddr + ptInDataAttr->wOldDataOffset, NETANTITHEFT_KEY_LEN + NETANTITHEFT_SN_LEN, pucOutData + 2, NETANTITHEFT_KEY_LEN + NETANTITHEFT_SN_LEN);
    }
    else if (ptInDataAttr->wRegAddr == 2177 || ptInDataAttr->wRegAddr == 2178)
    {
        rt_memcpy_s(pMemberAddr + ptInDataAttr->wOldDataOffset, NETANTITHEFT_KEY_LEN * 2, pucOutData + 2, NETANTITHEFT_KEY_LEN * 2);
    }
    slRet = (*s_atDataCovFunc[eInDir][ptInDataAttr->ucOldDataType].Func)(ptInDataAttr, (void*)(pMemberAddr + ptInDataAttr->wOldDataOffset), pucOutData);
    if (APP_FAILURE != slRet && BYTEBUFF_TO_DATA == eInDir)
    {
        if (NULL != ptInDataAttr->pDealFunc)
        {
            slRet = ptInDataAttr->pDealFunc(ptInDataAttr);
        }
    }
    return slRet;
}

Static BOOLEAN JudgeIsAntithftCtrl(WORD wRegAddr)
{
    if ((wRegAddr >= 2174 && wRegAddr <= 2176) || (wRegAddr >= 2164 && wRegAddr <= 2166))
    {
        return True;
    }
    return False;
}

/****************************************************************
函数名：PacketOrParseBmsPackPartAna
入口参数：
出口参数：
功能：降低圈复杂度
****************************************************************/
Static INT32 PacketOrParseBmsPackPartAna(const T_OneDataAttrStruct* ptInDataAttr,  E_DATA_DIRECTION eInDir, BYTE* pucOutData, BYTE* pucInRsvData)
{
    if (NULL == pucOutData || NULL == ptInDataAttr || NULL == pucInRsvData)
    {
        return APP_FAILURE;
    }
    if (BMS_PACK_FAC_PART != ptInDataAttr->ucOldDataPart || MAX_DATA_TYPES <= ptInDataAttr->ucOldDataType || NULL == s_atDataCovFunc[eInDir][ptInDataAttr->ucOldDataType].Func)
    {
        return APP_FAILURE;
    }

    return (*s_atDataCovFunc[eInDir][ptInDataAttr->ucOldDataType].Func)(ptInDataAttr, (void *)(pucInRsvData + ptInDataAttr->wOldDataOffset), pucOutData);
}
/****************************************************************
函数名：PacketOrParsePackManuPartAna
入口参数：
出口参数：
功能：降低圈复杂度
****************************************************************/
Static INT32 PacketOrParsePackManuPartAna(const T_OneDataAttrStruct* ptInDataAttr,  E_DATA_DIRECTION eInDir, BYTE* pucOutData, BYTE* pucInRsvData)
{
	BYTE* pMemberAddr = NULL;
	T_BmsPACKManufactStruct tBmsPACKManufact;
	rt_memset(&tBmsPACKManufact, 0, sizeof(T_BmsPACKManufactStruct));

    if (NULL == pucOutData || NULL == ptInDataAttr)
    {
        return APP_FAILURE;
    }
    if (PACK_MANUFACT_PART != ptInDataAttr->ucOldDataPart || MAX_DATA_TYPES <= ptInDataAttr->ucOldDataType || NULL == s_atDataCovFunc[eInDir][ptInDataAttr->ucOldDataType].Func)
    {
        return APP_FAILURE;
    }
	readPackManufact(&tBmsPACKManufact);
    pMemberAddr = (BYTE*)&tBmsPACKManufact;
    return (*s_atDataCovFunc[eInDir][ptInDataAttr->ucOldDataType].Func)(ptInDataAttr, (void*)(pMemberAddr + ptInDataAttr->wOldDataOffset), pucOutData);
}

/****************************************************************
函数名：PacketOrParseAna
入口参数：
出口参数：
功能：
****************************************************************/
static INT32 PacketOrParseAna(const T_OneDataAttrStruct* ptInDataAttr, E_DATA_DIRECTION eInDir, BYTE* pucOutData, BYTE* pucInRsvData)
{
    if (NULL == pucOutData || NULL == ptInDataAttr)
    {
        return APP_FAILURE;
    }
    
    if (MAX_PART_NUM <= ptInDataAttr->ucOldDataPart || NULL == s_atPacketOrParseFunc[ptInDataAttr->ucOldDataPart].Func)
    {
        return APP_FAILURE;
    }
    
    return (*s_atPacketOrParseFunc[ptInDataAttr->ucOldDataPart].Func)(ptInDataAttr, eInDir, pucOutData, pucInRsvData);
}

/****************************************************************
函数名：BOOLEANToINT16
入口参数：
出口参数：
功能：
****************************************************************/
static INT32 BOOLEANToINT16(const T_OneDataAttrStruct* ptInDataAttr, void* pInData, void* pOutByteBuff)
{
    SHORT sTemp = 0;
    BYTE* pucBuff = (BYTE*)pOutByteBuff;
    
    if (NULL == pInData || NULL == pOutByteBuff)
    {
        return APP_FAILURE;
    }
    sTemp = (SHORT)(*(BYTE*)(pInData));
    PutInt16ToBuff(pucBuff, sTemp);
    return APP_SUCCESS;		
}

/****************************************************************
函数名：CHARToINT16
入口参数：
出口参数：
功能：
****************************************************************/
static INT32 CHARToINT16(const T_OneDataAttrStruct* ptInDataAttr, void* pInData, void* pOutByteBuff)
{
    SHORT sTemp = 0;
    if (NULL == pInData || NULL == pOutByteBuff)
    {
        return APP_FAILURE;
    }
    sTemp = (SHORT)(*(CHAR*)pInData);
    PutInt16ToBuff((BYTE*)pOutByteBuff, sTemp);
    return APP_SUCCESS;
}

/****************************************************************
函数名：INT8UToINT16
入口参数：
出口参数：
功能：
****************************************************************/
static INT32 INT8UToINT16(const T_OneDataAttrStruct* ptInDataAttr, void* pInData, void* pOutByteBuff)
{
    BYTE ucTemp = 0;
    SHORT sTemp = 0;
    if (NULL == pInData || NULL == pOutByteBuff || NULL == ptInDataAttr)
    {
        return APP_FAILURE;
    }
    ucTemp = *(BYTE*)(pInData);
    sTemp = (SHORT)(ucTemp * Pow10(ptInDataAttr->ucNewPrecision));
    PutInt16ToBuff((BYTE*)pOutByteBuff, sTemp);
    return APP_SUCCESS;
}

/****************************************************************
函数名：INT16UToINT16
入口参数：
出口参数：
功能：
****************************************************************/
static INT32 INT16UToINT16(const T_OneDataAttrStruct* ptInDataAttr, void* pInData, void* pOutByteBuff)
{
    WORD wTemp = 0;
    if (NULL == pInData || NULL == pOutByteBuff || NULL == ptInDataAttr)
    {
        return APP_FAILURE;
    }
    wTemp = *(WORD*)(pInData);
    wTemp = wTemp * Pow10(ptInDataAttr->ucNewPrecision);
    PutInt16ToBuff((BYTE*)pOutByteBuff, (SHORT)wTemp);
    return APP_SUCCESS;
}

/****************************************************************
函数名：FP32ToINT16
入口参数：
出口参数：
功能：
****************************************************************/

static INT32 FP32ToINT16(const T_OneDataAttrStruct* ptInDataAttr, void* pInData, void* pOutByteBuff)
{
    FLOAT fTemp = 0.0;
    SHORT sTemp = 0;
    SHORT wTemp = 0;
    if (NULL == pInData || NULL == pOutByteBuff || NULL == ptInDataAttr)
    {
        return APP_FAILURE;
    }
    fTemp = *(FLOAT*)(pInData);
#if defined(PAKISTAN_CMPAK_PROTOCOL)
    if(DATA_TYPE_INT16U == ptInDataAttr->ucNewDataType)
    {
        if(fabs(fTemp - 0xFFFF) > 0.0001)
        {
            wTemp = FLOAT_TO_WORD(fTemp * Pow10(ptInDataAttr->ucNewPrecision));
        }
        else
        {
            wTemp = fTemp;
        }
        PutInt16ToBuff((BYTE *)pOutByteBuff, (SHORT)wTemp);
    }
    else
    {
        if(fabs(fTemp - 0x8000) > 0.0001)
        {
            sTemp = FLOAT_TO_SHORT(fTemp * Pow10(ptInDataAttr->ucNewPrecision));
        }
        else
        {
            sTemp = fTemp;
        }
        PutInt16ToBuff((BYTE *)pOutByteBuff, sTemp);
    }
#else
    if (DATA_TYPE_INT16U == ptInDataAttr->ucNewDataType)
    {
        wTemp = FLOAT_TO_WORD(fTemp * Pow10(ptInDataAttr->ucNewPrecision));
        PutInt16ToBuff((BYTE*)pOutByteBuff, (SHORT)wTemp);
    }
    else
    {
        sTemp = FLOAT_TO_SHORT(fTemp * Pow10(ptInDataAttr->ucNewPrecision));
        PutInt16ToBuff((BYTE*)pOutByteBuff, sTemp);
    }
#endif
    return APP_SUCCESS;
}


/****************************************************************
函数名：TIMEToINT16
入口参数：
出口参数：
功能：
****************************************************************/
static INT32 TIMEToINT16(const T_OneDataAttrStruct* ptInDataAttr, void* pInData, void* pOutByteBuff)
{
    T_TimeStruct* ptTime = NULL;
    BYTE* pucBuff = NULL;
    if (NULL == pInData || NULL == pOutByteBuff || NULL == ptInDataAttr)
    {
        return APP_FAILURE;
    }
    ptTime = (T_TimeStruct*)pInData;
    pucBuff = (BYTE*)pOutByteBuff;
    PutInt16ToBuff(pucBuff, (SHORT)ptTime->wYear);
    pucBuff += 2;
    PutInt16ToBuff(pucBuff, (SHORT)ptTime->ucMonth);
    pucBuff += 2;
    PutInt16ToBuff(pucBuff, (SHORT)ptTime->ucDay);
    pucBuff += 2;
    PutInt16ToBuff(pucBuff, (SHORT)ptTime->ucHour);
    pucBuff += 2;
    PutInt16ToBuff(pucBuff, (SHORT)ptTime->ucMinute);
    pucBuff += 2;
    PutInt16ToBuff(pucBuff, (SHORT)ptTime->ucSecond);
    
    return APP_SUCCESS;
}

/****************************************************************
函数名：TIMEToINT16
入口参数：
出口参数：
功能：
****************************************************************/
static INT32 DATEToINT16(const T_OneDataAttrStruct* ptInDataAttr, void* pInData, void* pOutByteBuff)
{
    T_DateStruct* ptDate = NULL;
    BYTE* pucBuff = NULL;
    if (NULL == pInData || NULL == pOutByteBuff || NULL == ptInDataAttr)
    {
        return APP_FAILURE;
    }
    ptDate = (T_DateStruct*)pInData;
    pucBuff = (BYTE*)pOutByteBuff;
    PutInt16ToBuff(pucBuff, (SHORT)ptDate->wYear);
    pucBuff += 2;
    PutInt16ToBuff(pucBuff, (SHORT)ptDate->ucMonth);
    pucBuff += 2;
    PutInt16ToBuff(pucBuff, (SHORT)ptDate->ucDay);
    
    return APP_SUCCESS;
}

/****************************************************************
函数名：INT16ToBOOLEAN
入口参数：
出口参数：
功能：
****************************************************************/
static INT32 INT16ToBOOLEAN(const T_OneDataAttrStruct* ptInDataAttr, void* pInData, void* pOutByteBuff)
{
    WORD wTemp = 0;
    SHORT sTemp = 0;
    if (NULL == pInData || NULL == pOutByteBuff || NULL == ptInDataAttr)
    {
        return APP_FAILURE;
    }
    if (DATA_TYPE_INT16U == ptInDataAttr->ucNewDataType)
    {
        wTemp = (WORD)GetInt16Data((BYTE*)pOutByteBuff);
        wTemp = wTemp / Pow10(ptInDataAttr->ucNewPrecision);
        *(BOOLEAN*)pInData = (BOOLEAN)wTemp;
    }
    else
    {
        sTemp = (SHORT)GetInt16Data((BYTE*)pOutByteBuff);
        sTemp = sTemp / Pow10(ptInDataAttr->ucNewPrecision);
        *(BOOLEAN*)pInData = (BOOLEAN)sTemp;
    }
    return APP_SUCCESS;				
}

/****************************************************************
函数名：INT16ToCHAR
入口参数：
出口参数：
功能：
****************************************************************/
static INT32 INT16ToCHAR(const T_OneDataAttrStruct* ptInDataAttr, void* pInData, void* pOutByteBuff)
{
    SHORT sTemp = 0;
    if(NULL == pInData || NULL == pOutByteBuff || NULL == ptInDataAttr)
    {
        return APP_FAILURE;
    }
    sTemp = GetInt16Data((BYTE*)pOutByteBuff);
    *(CHAR*)pInData = (CHAR)sTemp;
    return APP_SUCCESS;			
}

/****************************************************************
函数名：INT16ToINT8U
入口参数：
出口参数：
功能：
****************************************************************/
static INT32 INT16ToINT8U(const T_OneDataAttrStruct* ptInDataAttr, void* pInData, void* pOutByteBuff)
{
    WORD wTemp = 0;
    SHORT sTemp = 0;
    if (NULL == pInData || NULL == pOutByteBuff || NULL == ptInDataAttr)
    {
        return APP_FAILURE;
    }
    if (DATA_TYPE_INT16U == ptInDataAttr->ucNewDataType)
    {
        wTemp = (WORD)GetInt16Data((BYTE*)pOutByteBuff);
        wTemp = wTemp / Pow10(ptInDataAttr->ucNewPrecision);
        *(BYTE*)pInData = (BYTE)wTemp;
    }
    else
    {
        sTemp = GetInt16Data((BYTE*)pOutByteBuff);
        sTemp = sTemp / Pow10(ptInDataAttr->ucNewPrecision);
        *(BYTE*)pInData = (BYTE)sTemp;
    }
    return APP_SUCCESS;	
}

/****************************************************************
函数名：INT16ToINT16U
入口参数：
出口参数：
功能：
****************************************************************/
static INT32 INT16ToINT16U(const T_OneDataAttrStruct *ptInDataAttr, void *pInData, void *pOutByteBuff){
    WORD wTemp = 0;
    SHORT sTemp = 0;
    if (NULL == pInData || NULL == pOutByteBuff || NULL == ptInDataAttr)
    {
        return APP_FAILURE;
    }
    if (DATA_TYPE_INT16U == ptInDataAttr->ucNewDataType)
    {
        wTemp = (WORD)GetInt16Data((BYTE *)pOutByteBuff);
        wTemp = wTemp / Pow10(ptInDataAttr->ucNewPrecision);
        *(WORD *)pInData = wTemp;
    }
    else
    {
        sTemp = GetInt16Data((BYTE *)pOutByteBuff);
        sTemp = sTemp / Pow10(ptInDataAttr->ucNewPrecision);
        *(WORD *)pInData = (WORD)sTemp;
    }
    return APP_SUCCESS;			
}

/****************************************************************
函数名：INT16ToFP32
入口参数：
出口参数：
功能：
****************************************************************/
static INT32 INT16ToFP32(const T_OneDataAttrStruct *ptInDataAttr, void *pInData, void *pOutByteBuff){
    WORD wTemp = 0;
    SHORT sTemp = 0;
    FLOAT fTemp = 0.0;
    if(NULL == pInData || NULL == pOutByteBuff || NULL == ptInDataAttr)
    {
        return APP_FAILURE;
    }
    if(DATA_TYPE_INT16U == ptInDataAttr->ucNewDataType)
    {
        wTemp = (WORD)GetInt16Data((BYTE *)pOutByteBuff);
        fTemp = (FLOAT)wTemp / Pow10(ptInDataAttr->ucNewPrecision);
    }
    else
    {
        sTemp = GetInt16Data((BYTE *)pOutByteBuff);
        fTemp = (FLOAT)sTemp / Pow10(ptInDataAttr->ucNewPrecision);
    }
    *(FLOAT *)pInData = fTemp;

    return APP_SUCCESS;		
}

/****************************************************************
函数名：INT16ToTIME
入口参数：
出口参数：
功能：
****************************************************************/
static INT32 INT16ToTIME(const T_OneDataAttrStruct *ptInDataAttr, void *pInData, void *pOutByteBuff){
//	WORD wTemp = 0;
    T_TimeStruct *ptTime = NULL;
    BYTE *pucBuff = NULL;
    if(NULL == pInData || NULL == pOutByteBuff || NULL == ptInDataAttr)
    {
        return APP_FAILURE;
    }
    ptTime = (T_TimeStruct *)pInData;
    pucBuff = (BYTE *)pOutByteBuff;
    
    ptTime->wYear = (WORD)GetInt16Data(pucBuff);
    pucBuff += 2;
    ptTime->ucMonth = GetLowFromInt16(pucBuff);
    pucBuff += 2;
//	wTemp = (WORD)GetInt16Data(pucBuff);
    ptTime->ucDay = GetLowFromInt16(pucBuff);
    pucBuff += 2;
//	wTemp = (WORD)GetInt16Data(pucBuff);
    ptTime->ucHour = GetLowFromInt16(pucBuff);
    pucBuff += 2;
//	wTemp = (WORD)GetInt16Data(pucBuff);
    ptTime->ucMinute = GetLowFromInt16(pucBuff);
    pucBuff += 2;
//	wTemp = (WORD)GetInt16Data(pucBuff);
    ptTime->ucSecond = GetLowFromInt16(pucBuff);

    return APP_SUCCESS;		
}

/****************************************************************
函数名：INT16ToTIME
入口参数：
出口参数：
功能：
****************************************************************/
static INT32 INT16ToDATE(const T_OneDataAttrStruct *ptInDataAttr, void *pInData, void *pOutByteBuff){
    WORD wTemp = 0;
    T_DateStruct *ptDate = NULL;
    BYTE *pucBuff = NULL;
    if(NULL == pInData || NULL == pOutByteBuff || NULL == ptInDataAttr)
    {
        return APP_FAILURE;
    }
    ptDate = (T_DateStruct *)pInData;
    pucBuff = (BYTE *)pOutByteBuff;
    
    ptDate->wYear = (WORD)GetInt16Data(pucBuff);
    pucBuff += 2;
    wTemp = (WORD)GetInt16Data(pucBuff);
    ptDate->ucMonth = (BYTE)(wTemp&0xff);
    pucBuff += 2;
    wTemp = (WORD)GetInt16Data(pucBuff);
    ptDate->ucDay = (BYTE)(wTemp&0xff);

    if (CheckDateValid(ptDate) == False)
    {
        return APP_FAILURE;
    }

    return APP_SUCCESS;		
}

/****************************************************************
函数名：INT16ToCTRLDATA
入口参数：
出口参数：
功能：
****************************************************************/
static INT32 INT16ToCTRLDATA(const T_OneDataAttrStruct *ptInDataAttr, void *pInData, void *pOutByteBuff){
    WORD wTemp = 0;
    if(NULL == pInData || NULL == pOutByteBuff || NULL == ptInDataAttr)
    {
        return APP_FAILURE;
    }
    PutInt16ToBuff((BYTE *)&wTemp, (*(SHORT *)pOutByteBuff));
    if (DATA_COIL_ON != wTemp || NULL == ptInDataAttr->pDealFunc)
    {
        return APP_FAILURE; 
    }
    
    return APP_SUCCESS;	
}

/***************************************************************************
* 函数名称：BinarySearch()
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：从小到大的有序数列，采用二分法
* 作    者：fsg
* 设计日期：2010-01-24
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
static INT32 BinarySearch(SHORT sInStart, SHORT sInEnd, WORD wInTargetAddr, const T_OneDataAttrStruct *ptInDataAttr)
{
    SHORT sMiddle = 0, sStart = sInStart, sEnd = sInEnd;
    INT32 slRet = APP_FAILURE;
    if(NULL == ptInDataAttr)
    {
        return APP_FAILURE;
    }
    while (sStart <= sEnd && 0 <= sStart)
    {
        sMiddle = (sStart + sEnd) / 2;
        if (wInTargetAddr == ptInDataAttr[sMiddle].wRegAddr)
        {
            slRet = (INT32)sMiddle;
            break;
        }
        sStart = (wInTargetAddr > ptInDataAttr[sMiddle].wRegAddr) ? (sMiddle + 1) : sStart;
        sEnd = (wInTargetAddr > ptInDataAttr[sMiddle].wRegAddr) ? sEnd : (sMiddle - 1);
    }

    return slRet;
}

/***************************************************************************
* 函数名称：FindStartAddr()
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：找到起始地址数据 (从小到大的有序数列，采用二分法)
* 作    者：fsg
* 设计日期：2010-01-24
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
static INT32 FindStartAddr(WORD wInTargetAddr, const T_OneDataAttrStruct *ptInDataAttr)
{
    INT32 slRet = APP_FAILURE;
    slRet = BinarySearch(0, s_tMdbsRtuManage.slDataAttrNum - 1, wInTargetAddr, ptInDataAttr);
    return slRet;
}
/***************************************************************************
* 函数名称：SendReadDigIn()
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：发送数字量数据(只读)
* 作    者：fsg
* 设计日期：2010-01-24
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
static INT32 SendReadDigIn(T_MdbsRtuApp *ptInApp)
{
    BYTE *p = ptInApp->aucSendBuf, *pucByteCnt = ptInApp->aucSendBuf,  *pucBuff = s_aucBitTemp;
    WORD i = 0;
    INT32 slFindStartAddrSn = APP_FAILURE, slByteNum = APP_FAILURE;
    if (NULL == ptInApp)
    {
        return APP_FAILURE;
    }

    p++;//字节长度
    slFindStartAddrSn = FindStartAddr(ptInApp->wRegAddr, s_tMdbsRtuManage.ptDataAttr);
    if (APP_FAILURE < slFindStartAddrSn && FindCntSn(slFindStartAddrSn) + ptInApp->wRegCnt <= s_tMdbsRtuManage.slTotalCnt)
    {
        for (i = 0; i < ptInApp->wRegCnt; i++)
        {
            slByteNum = CoBitByteWordToByteBuff(&s_tMdbsRtuManage.ptDataAttr[slFindStartAddrSn + i], pucBuff);
            if (slByteNum < APP_SUCCESS)
            {
                return APP_FAILURE;
            }
            pucBuff += slByteNum;
        }
    }
    slByteNum = APP_FAILURE;
    slByteNum = FillInByteToBitBuff(p, (INT32)(pucBuff - s_aucBitTemp), s_aucBitTemp);
    if (slByteNum < APP_SUCCESS)
    {
        return APP_FAILURE;
    }
    p += slByteNum;
    *pucByteCnt = p - pucByteCnt - 1;
    
    return (INT32)(p - pucByteCnt);
}

static INT32 checkSn(INT32 slSn, WORD wCnt)
{
    if (s_tMdbsRtuManage.slTotalCnt < FindCntSn(slSn) + wCnt)
    {
        return APP_FAILURE;
    }
    return slSn;
}

/***************************************************************************
* 函数名称：SendReadAna()
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：发送模拟量数据(可读写)
* 作    者：fsg
* 设计日期：2010-01-24
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
static INT32 SendReadAna(T_MdbsRtuApp *ptInApp)
{
    BYTE *p = ptInApp->aucSendBuf, *pucByteCnt = ptInApp->aucSendBuf, ucOneDataLen = 0;
    WORD wCnt=0;
    WORD wIndex = 0;
    INT32 slRet = APP_FAILURE, slFindStartAddrSn, slSn;

	T_BmsPACKManufactStruct	tPackInfo;
	rt_memset((BYTE*)&tPackInfo, 0x00, sizeof(T_BmsPACKManufactStruct));

    if (NULL == ptInApp)
    {
        return APP_FAILURE;
    }

    GetSysPara(&s_tSysPara);
	readPackManufact(&tPackInfo);
    readBmsPackFacInfo(&s_tBmsPackFacInfo);

    p++;//字节长度
    slFindStartAddrSn = FindStartAddr(ptInApp->wRegAddr, s_tMdbsRtuManage.ptDataAttr);
    slSn = checkSn(slFindStartAddrSn, ptInApp->wRegCnt);
    if (APP_FAILURE < slSn)
    {
        while (wCnt < ptInApp->wRegCnt)
        {
            if (BMS_PACK_FAC_PART == s_tMdbsRtuManage.ptDataAttr[slSn + wIndex].ucOldDataPart)
            {
                slRet = PacketOrParseAna(&s_tMdbsRtuManage.ptDataAttr[slSn + wIndex], DATA_TO_BYTEBUFF, p, (BYTE *)&s_tBmsPackFacInfo);
            }
            else if(PACK_MANUFACT_PART == s_tMdbsRtuManage.ptDataAttr[slSn + wIndex].ucOldDataPart)
            {
            	slRet = PacketOrParseAna(&s_tMdbsRtuManage.ptDataAttr[slSn + wIndex], DATA_TO_BYTEBUFF, p, (BYTE *)&tPackInfo);
            }
            else if (SYS_PARA == s_tMdbsRtuManage.ptDataAttr[slSn + wIndex].ucOldDataPart)
            {
                slRet = PacketOrParseAna(&s_tMdbsRtuManage.ptDataAttr[slSn + wIndex], DATA_TO_BYTEBUFF, p, (BYTE *)&s_tSysPara);
            }
            else if (SPECIAL_DATA_PART == s_tMdbsRtuManage.ptDataAttr[slSn + wIndex].ucOldDataPart)
            {
                slRet = PacketOrParseAna(&s_tMdbsRtuManage.ptDataAttr[slSn + wIndex], DATA_TO_BYTEBUFF, p, (BYTE *)&s_tSpecialData);
            }
            if (APP_FAILURE == slRet)
            {
                return APP_FAILURE;
            }
            ucOneDataLen = s_tMdbsRtuManage.ptDataAttr[slSn + wIndex].ucNewDataLen;
            p += ucOneDataLen;
            wCnt += (ucOneDataLen / 2);
            wIndex++;
        }
    }
    *pucByteCnt = p - pucByteCnt - 1;
    return (INT32)(p - ptInApp->aucSendBuf);
}

/***************************************************************************
* 函数名称：SendReadAnaIn()
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：发送模拟量数据(只读)
* 作    者：fsg
* 设计日期：2010-01-24
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
static INT32 SendReadAnaIn(T_MdbsRtuApp *ptInApp)
{
    BYTE *p = ptInApp->aucSendBuf, *pucByteCnt = ptInApp->aucSendBuf, ucOneDataLen = 0;
    WORD wCnt = 0;
    WORD wIndex = 0;
    INT32 slFindStartAddrSn = APP_FAILURE;
    
    if (NULL == ptInApp)
    {
        return APP_FAILURE;
    }

    p++;//字节长度
    slFindStartAddrSn = FindStartAddr(ptInApp->wRegAddr, s_tMdbsRtuManage.ptDataAttr);
    if (APP_FAILURE < slFindStartAddrSn && FindCntSn(slFindStartAddrSn) + ptInApp->wRegCnt <= s_tMdbsRtuManage.slTotalCnt)
    {
        while (wCnt < ptInApp->wRegCnt)
        {
            if (APP_FAILURE == PacketOrParseAna(&s_tMdbsRtuManage.ptDataAttr[slFindStartAddrSn + wIndex], DATA_TO_BYTEBUFF, p, NULL))
            {
                return APP_FAILURE;
            }
            ucOneDataLen=s_tMdbsRtuManage.ptDataAttr[slFindStartAddrSn + wIndex].ucNewDataLen;
            p += ucOneDataLen;
            wCnt += (ucOneDataLen / 2);
            wIndex++;
        }
    }
    *pucByteCnt = p - pucByteCnt - 1;
    return (INT32)(p - ptInApp->aucSendBuf);
}
/***************************************************************************
* 函数名称：WriteDigSingle()
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：写状态量数据(单个)
Request:    ADDR    FUNC    H.O.DATACOIL    L.O.DATACOIL    DATA#ON/GPIO_OFF DATA  ERROR CHECK FIELD
Response:   ADDR    FUNC    H.O.DATACOIL    L.O.DATACOIL    DATA#ON/GPIO_OFF DATA  ERROR CHECK FIELD
* 作    者：fsg
* 设计日期：2010-01-24
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
static INT32 WriteDigSingle(T_MdbsRtuApp *ptInApp)
{
    BYTE *p = &ptInApp->aucRecBuf[MODBUSRTU_REC_WRITE_SINGLE_OFFSET], *pucBuff = ptInApp->aucSendBuf;
    INT32 slRet = APP_FAILURE, slFindStartAddrSn = APP_FAILURE;
    
    if (NULL == ptInApp)
    {
        return APP_FAILURE;
    }
    else if(JudgeWriteRecLen(ptInApp))
    {
        return APP_FAILURE;
    }
    slFindStartAddrSn = FindStartAddr(ptInApp->wRegAddr, s_tMdbsRtuManage.ptDataAttr);
    if (APP_FAILURE < slFindStartAddrSn && FindCntSn(slFindStartAddrSn) + 1 <= s_tMdbsRtuManage.slTotalCnt)
    {
        slRet = PacketOrParseAna(&s_tMdbsRtuManage.ptDataAttr[slFindStartAddrSn], BYTEBUFF_TO_DATA, p, NULL);
    }
    if (APP_FAILURE < slRet)
    {
        rt_memcpy((void *)pucBuff, (void *)&ptInApp->aucRecBuf[MODBUSRTU_REC_REGADDR_OFFSET], 4);
        pucBuff += 4;
        slRet = pucBuff - ptInApp->aucSendBuf;
        return slRet;
    }
    
    return APP_FAILURE;
}

Static BOOLEAN JudgeWriteRecLen(T_MdbsRtuApp *ptInApp)
{
    if (ptInApp->wRecLen == DEFAULT_MDBSWRITE_SGL_BYTE_CNT) {
        return (ptInApp->wRegAddr == 2177 || ptInApp->wRegAddr == 2178 || JudgeIsAntithftCtrl(ptInApp->wRegAddr)) ? FAILURE : SUCCESSFUL;
    }

    if (ptInApp->wRecLen == MDBSWRITE_SGL_NET_ANTITHEFT_BYTE_CNT && (JudgeIsAntithftCtrl(ptInApp->wRegAddr) && ptInApp->wRegAddr != 2175)) {
        return SUCCESSFUL;
    }

    if (ptInApp->wRecLen == MDBSWRITE_HEART_NET_ANTITHEFT_BYTE_CNT && ptInApp->wRegAddr == 2175) {
        return SUCCESSFUL;
    }

    if (ptInApp->wRegAddr == 2177 && ptInApp->wRecLen == MDBSWRITE_CHANGE_NET_ANTITHEFT_BYTE_CNT) {
        return SUCCESSFUL;
    }

    if (ptInApp->wRegAddr == 2178 && ptInApp->wRecLen == MDBSWRITE_CHANGE_NET_ANTITHEFT_BYTE_CNT) {
        return SUCCESSFUL;
    }

    return FAILURE;
}

/***************************************************************************
* 函数名称：WriteAnaSingle()
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：写模拟量数据(只读)
Request:    ADDR    FUNC    H.O.DATAREG    L.O.DATAREG  H.O.DATA    L.O.DATA    ERROR CHECK FIELD
Response:   ADDR    FUNC    H.O.DATAREG    L.O.DATAREG  H.O.DATA    L.O.DATA    ERROR CHECK FIELD
* 作    者：fsg
* 设计日期：2010-01-24
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
static INT32 WriteAnaSingle(T_MdbsRtuApp *ptInApp)
{
    BYTE *p = &ptInApp->aucRecBuf[MODBUSRTU_REC_WRITE_SINGLE_OFFSET]; 
    BYTE  *pucBuff = ptInApp->aucSendBuf, *pucSel = NULL;
    INT32 slRet = APP_FAILURE, slFindStartAddrSn = APP_FAILURE, slSn;
    
    if (NULL == ptInApp || DEFAULT_MDBSWRITE_SGL_BYTE_CNT != ptInApp->wRecLen)
    {
        return APP_FAILURE;
    }
    GetSysPara(&s_tSysPara);

    readBmsPackFacInfo(&s_tBmsPackFacInfo);
    slFindStartAddrSn = FindStartAddr(ptInApp->wRegAddr, s_tMdbsRtuManage.ptDataAttr);
    slSn = checkSn(slFindStartAddrSn, 1);
    if (APP_FAILURE < slSn)
    {
        pucSel = SelWritePart((BYTE *)&s_tSysPara, (BYTE *)&s_tBmsPackFacInfo, s_tMdbsRtuManage.ptDataAttr[slSn].ucOldDataPart, &s_ulWritePartFlag);
        slRet = PacketOrParseAna(&s_tMdbsRtuManage.ptDataAttr[slSn], BYTEBUFF_TO_DATA, p, pucSel);
    }
 
    if (APP_FAILURE == slRet || ChkSysPara( &s_tSysPara ) == False)
    {
        s_ulWritePartFlag = WRITE_DEFAULT;
        return APP_FAILURE;
    }
    rt_memcpy(pucBuff, &ptInApp->aucRecBuf[MODBUSRTU_REC_REGADDR_OFFSET], DEFAULT_SND_ANA_SINGLE_LEN);
    pucBuff += DEFAULT_SND_ANA_SINGLE_LEN;
    return (INT32)(pucBuff - ptInApp->aucSendBuf);
}

/***************************************************************************
* 函数名称：WriteAnaMul()
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：写模拟量数据(多个)
Request:    ADDR    FUNC    H.O.ADDR    L.O.ADDR    QUANTITY    BYTE CNT    H.O.DATA    L.O.DATA    etc.    ERROR CHECK FIELD
Response:   ADDR    FUNC    H.O.ADDR    L.O.ADDR    QUANTITY    ERROR CHECK FIELD
* 作    者：fsg
* 设计日期：2010-01-24
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
static INT32 WriteAnaMul(T_MdbsRtuApp *ptInApp)
{
    BYTE *p = &ptInApp->aucRecBuf[MODBUSRTU_REC_BYTECNT_OFFSET], 
         *pucRecByteCnt = &ptInApp->aucRecBuf[MODBUSRTU_REC_BYTECNT_OFFSET]; 
    BYTE  *pucBuff = ptInApp->aucSendBuf, *pucSel = NULL, ucOneDataLen = 0;
    WORD wCnt = 0;
    WORD wIndex = 0;
    INT32 slRes = APP_SUCCESS;
    INT32 slFindStartAddrSn = APP_FAILURE;

    if (NULL == ptInApp || MIN_MDBSWRITE_MUL_BYTE_CNT > ptInApp->wRecLen)
    {
        return APP_FAILURE;
    }
    GetSysPara(&s_tSysPara);
    GetSysPara(&s_tSysParaTmp);
    readBmsPackFacInfo(&s_tBmsPackFacInfo);
    p++;//字节长度
    slFindStartAddrSn = FindStartAddr(ptInApp->wRegAddr, s_tMdbsRtuManage.ptDataAttr);
    if(s_tMdbsRtuManage.ptDataAttr[slFindStartAddrSn].ucWrFunc != ptInApp->ucFunc)
    {
        return APP_FAILURE;
    }
    if (APP_FAILURE < slFindStartAddrSn && FindCntSn(slFindStartAddrSn) + ptInApp->wRegCnt <= s_tMdbsRtuManage.slTotalCnt)
    {
        while (wCnt < ptInApp->wRegCnt)
        {
            pucSel = SelWritePart((BYTE *)&s_tSysPara, (BYTE *)&s_tBmsPackFacInfo, s_tMdbsRtuManage.ptDataAttr[slFindStartAddrSn + wIndex].ucOldDataPart, &s_ulWritePartFlag);
            if (APP_FAILURE == PacketOrParseAna(&s_tMdbsRtuManage.ptDataAttr[slFindStartAddrSn + wIndex], BYTEBUFF_TO_DATA, p, pucSel))
            {
                return APP_FAILURE;
            }
            if(ptInApp->wRegAddr != 0x04A5) // ip
            {
                if(ChkSysPara( &s_tSysPara ) == True)
                {
                    rt_memcpy(&s_tSysParaTmp, &s_tSysPara, sizeof(T_SysPara));
                }
                else{
                    rt_memcpy(&s_tSysPara, &s_tSysParaTmp, sizeof(T_SysPara));
                    slRes = APP_FAILURE;
                }
            }
            ucOneDataLen=s_tMdbsRtuManage.ptDataAttr[slFindStartAddrSn + wIndex].ucNewDataLen;
            p += ucOneDataLen;
            wCnt += (ucOneDataLen / 2);
            wIndex++;
        }
    }
    if (*pucRecByteCnt == (p - pucRecByteCnt - 1))
    {
        if(slRes == APP_FAILURE)
        {
            return APP_FAILURE;
        }
        PutInt16ToBuff(pucBuff, (SHORT)(ptInApp->wRegAddr));
        pucBuff += 2;
        PutInt16ToBuff(pucBuff, (SHORT)(ptInApp->wRegCnt));
        pucBuff += 2;
        return (INT32)(pucBuff - ptInApp->aucSendBuf);
    }
    return APP_FAILURE;
}

/****************************************************************
函数名：SetSndAnswer
入口参数：
出口参数：
功能：设置响应值
****************************************************************/
static INT32 SetSndAnswer(Enum_MdbsRTN ucInAnswer)
{
    if (APP_RTN_NO_ANSWER == s_tMdbsRtuManage.ucAnswerRtn)
    {
        return APP_FAILURE;
    }

    s_tMdbsRtuManage.ucAnswerRtn = ucInAnswer;
    
    return APP_SUCCESS;
}

/****************************************************************
函数名：SetErrorCode
入口参数：
出口参数：
功能：设置错误码
****************************************************************/
static INT32 SetErrorCode(BYTE ucInErr)
{
    s_tMdbsRtuApp.ucErrCode = ucInErr;
    SetSndAnswer(APP_RTN_INCORRECT);
    
    return APP_SUCCESS;
}

/****************************************************************
函数名：SetErrorCode
入口参数：
出口参数：
功能：设置错误码
****************************************************************/

static BOOLEAN IsAddrLegal(void)
{
    INT32 slFindStartAddrSn = APP_FAILURE;

    if (NULL == s_tMdbsRtuManage.ptDataAttr || 1 > s_tMdbsRtuManage.slDataAttrNum)
    {
        return False;
    }
    #ifndef ZXESM_R321_APTOWER_VERSION
    slFindStartAddrSn = FindStartAddr(s_tMdbsRtuApp.wRegAddr, s_tMdbsRtuManage.ptDataAttr);
#if defined(PAKISTAN_CMPAK_PROTOCOL)
    if(APP_FAILURE < slFindStartAddrSn && FindCntSn(slFindStartAddrSn) + s_tMdbsRtuApp.wRegCnt <= s_tMdbsRtuManage.slTotalCnt && (s_tMdbsRtuApp.wRegCnt <= 32)) 
#else
    if (APP_FAILURE < slFindStartAddrSn && FindCntSn(slFindStartAddrSn) + s_tMdbsRtuApp.wRegCnt <= s_tMdbsRtuManage.slTotalCnt) 
#endif
    {
        return True;
    }
    #else
    if (((s_tMdbsRtuApp.wRegAddr >= LOCATION_AC_PROTOCOLDESCRIPTION_B_END + 1) && (s_tMdbsRtuApp.wRegAddr <= LOCATION_WEXTER_VOLT - 1)) || (s_tMdbsRtuApp.wRegAddr < LOCATION_WPARAMETERS_CHANGED_B))
    {
        return True;
    }
    else
    {    
        slFindStartAddrSn = FindStartAddr(s_tMdbsRtuApp.wRegAddr, s_tMdbsRtuManage.ptDataAttr);
        if (APP_FAILURE < slFindStartAddrSn && FindCntSn(slFindStartAddrSn) + s_tMdbsRtuApp.wRegCnt <= s_tMdbsRtuManage.slTotalCnt) 
        {
            return True;
        }
    }
    #endif

    return False;
}


/****************************************************************
函数名：ConvertCorrSendApp2Link
入口参数：
出口参数：
功能：当发送的数据包无错误的情况时，将合适数据送入链路缓存区
****************************************************************/
static INT32 ConvertCorrSendApp2Link(BYTE *pucOutData)
{
    BYTE *p = pucOutData;
    WORD i = 0;
    
    if (NULL == pucOutData)
    {
        return APP_FAILURE;
    }

    *p++ = s_tMdbsRtuApp.ucFunc;
    for (i = 0; i < s_tMdbsRtuApp.wSendLen; i++)
    {
        *p++ = s_tMdbsRtuApp.aucSendBuf[i];
    }
    
    return (INT32)(p - pucOutData);
}

/****************************************************************
函数名：ConvertCorrSendApp2Link
入口参数：
ADDR    FUNC    ERROR_CODE   ERROR_CHECK_FELD
To indicate that the response is a notification of an error, the high-order bit of
the function code is set to one.
出口参数：
功能：当发送的数据包有错误的情况时，将合适数据送入链路缓存区
****************************************************************/
static INT32 ConvertIncorrSendApp2Link(BYTE *pucOutData)
{
    BYTE *p = pucOutData;
    
    if (NULL == pucOutData)
    {
        return APP_FAILURE;
    }

    *p++ = s_tMdbsRtuApp.ucFunc | MODBUS_ERRORCODE_MASK;
    *p++ = s_tMdbsRtuApp.ucErrCode;
    
    return (INT32)(p - pucOutData);
}

/****************************************************************
函数名：SelWritePart
入口参数：
出口参数：
功能：选择数据源，在系统参数和bms包装信息里面选取
****************************************************************/
static BYTE *SelWritePart(BYTE *pucInSysPara, BYTE *pucInBmsPack, Enum_MdbsDataType ucInDataPart, UINT32 *ulInPartFlag)
{
    if (NULL == pucInSysPara || NULL == pucInBmsPack)
    {
        return NULL;
    }
    if (SYS_PARA == ucInDataPart)
    {
        (*ulInPartFlag) |= WRITE_SYSPARA_PART;
        return pucInSysPara;
    }
    else if (BMS_PACK_FAC_PART == ucInDataPart)
    {
        (*ulInPartFlag) |= WRITE_BMSPACK_PART;
        return pucInBmsPack;
    }
    
    return NULL;
}

/****************************************************************
函数名：ExcWritePart
入口参数：
出口参数：
功能：根据数据源标志，写系统参数或者bms包装信息
****************************************************************/
static INT32 ExcWritePart(BYTE *pucInSysPara, BYTE *pucInBmsPack, UINT32 ulInPartFlag)
{
    INT32 slRet = APP_SUCCESS;
    T_BmsPACKFactoryStruct tBmsPack;
    T_BmsPACKManufactStruct tPackInfo;
    rt_memset((BYTE *)&tPackInfo, 0x00, sizeof(T_BmsPACKManufactStruct));

    if (NULL == pucInSysPara || NULL == pucInBmsPack || (GPIO_OFF == GetPMOSStatus()))
    {
        return APP_FAILURE;
    }
    if (ulInPartFlag & WRITE_SYSPARA_PART)
    {
        slRet =(True == SetSysPara((T_SysPara *)pucInSysPara, True, CHANGE_BY_MDBUS) ? APP_SUCCESS : APP_FAILURE);
    }
    if (ulInPartFlag & WRITE_BMSPACK_PART)
    {
        rt_memcpy((BYTE*)&tBmsPack, pucInBmsPack, (sizeof(T_BmsPACKFactoryStruct) - 2));
        tBmsPack.wCRC = CRC_Cal((BYTE*)&tBmsPack, (sizeof(T_BmsPACKFactoryStruct) - 2));

        writeBmsPackFacInfo(&tBmsPack);
    }

    GetSysPara(&s_tSysPara);
    //190128先读取再设置
    readPackManufact(&tPackInfo);
    //修改启用日期参数后同步到电芯厂家信息中
    if (rt_memcmp(&s_tSysPara.tEnableTime, &tPackInfo.tActiveDate, sizeof(T_DateStruct)) != 0)
    {
        rt_memcpy(&tPackInfo.tActiveDate, &s_tSysPara.tEnableTime, sizeof(T_DateStruct));
        tPackInfo.wCRC = CRC_Cal((BYTE *)&tPackInfo, (sizeof(tPackInfo) - 2));
        writePackManufact(&tPackInfo);
    }
    
    return slRet;
}

/****************************************************************
函数名：DealReset
入口参数：
出口参数：
功能：BMS复位(分开写，降低圈复杂度)
****************************************************************/
INT32 DealReset(const T_OneDataAttrStruct *ptInDataAttr)
{
    ResetMCU(NO_RESET_REMOTE_CTRL);

    return APP_SUCCESS;
}

/****************************************************************
函数名：DealStartChg
入口参数：
出口参数：
功能：请求启动充电(分开写，降低圈复杂度)
****************************************************************/
INT32 DealStartChg(const T_OneDataAttrStruct *ptInDataAttr)
{
#ifdef RUN_MODE_CONTROLLED_ENABLED
    if (RUN_MODE_CONTROLLED != s_tSysPara.ucRunMode)
    {
        return APP_FAILURE;
    }
    SetValueFromProtocol(NPROTOCOL_TYPE_RS485_MODBUS, INFO_TYPE_STATUS, POWER_STATUS_ON);
#endif
    return APP_SUCCESS;
}

INT32 DealSendPowerOn(const T_OneDataAttrStruct *ptInDataAttr)
{
#ifdef RUN_MODE_CONTROLLED_ENABLED
    if (RUN_MODE_FREE != s_tSysPara.ucRunMode || getChargeBreakStatus())
    {
        return APP_FAILURE;
    }
    SetValueFromProtocol(NPROTOCOL_TYPE_RS485_MODBUS, INFO_TYPE_STATUS, POWER_STATUS_ON);
#endif
    return APP_SUCCESS;
}

/****************************************************************
函数名：DealStartDischg
入口参数：
出口参数：
功能：请求启动放电(分开写，降低圈复杂度)
****************************************************************/
INT32S DealStartDischg(const T_OneDataAttrStruct *ptInDataAttr)
{
#ifdef RUN_MODE_CONTROLLED_ENABLED
    if (RUN_MODE_CONTROLLED != s_tSysPara.ucRunMode)
    {
        return APP_FAILURE;
    }
    SetValueFromProtocol(NPROTOCOL_TYPE_RS485_MODBUS, INFO_TYPE_STATUS, POWER_STATUS_OFF);
#endif
    return APP_SUCCESS;
}

INT32 DealSendPowerOff(const T_OneDataAttrStruct *ptInDataAttr)
{
#ifdef RUN_MODE_CONTROLLED_ENABLED
    if (RUN_MODE_FREE != s_tSysPara.ucRunMode)
    {
        return APP_FAILURE;
    }
    SetValueFromProtocol(NPROTOCOL_TYPE_RS485_MODBUS, INFO_TYPE_STATUS, POWER_STATUS_OFF);
#endif
    return APP_SUCCESS;
}
/****************************************************************
函数名：DealOutVolSet
入口参数：
出口参数：
功能：控制放电输出电压(分开写，降低圈复杂度)
****************************************************************/
INT32S DealOutVolSet(const T_OneDataAttrStruct *ptInDataAttr)
{
#ifdef RUN_MODE_CONTROLLED_ENABLED
    if (RUN_MODE_CONTROLLED != s_tSysPara.ucRunMode)
    {
        return APP_FAILURE;
    }
    return SetValueFromProtocol(NPROTOCOL_TYPE_RS485_MODBUS, INFO_TYPE_OUT_VOLT,
        FLOAT_TO_SHORT(s_tSpecialData.fOutputVolt * 100.0)) == SUCCESSFUL ? APP_SUCCESS : APP_FAILURE;
#else
    return APP_SUCCESS;
#endif
}

/****************************************************************
函数名：DealChargeCurr
入口参数：
出口参数：
功能：控制充电电流系数(分开写，降低圈复杂度)
****************************************************************/
INT32S DealChargeCurr(const T_OneDataAttrStruct *ptInDataAttr)
{
#ifdef RUN_MODE_CONTROLLED_ENABLED
    if (RUN_MODE_CONTROLLED != s_tSysPara.ucRunMode)
    {
        return APP_FAILURE;
    }
    return SetValueFromProtocol(NPROTOCOL_TYPE_RS485_MODBUS, INFO_TYPE_CHG_COEF,
        FLOAT_TO_SHORT(s_tSpecialData.fChgCurrCoeff * 100.0)) == SUCCESSFUL ? APP_SUCCESS : APP_FAILURE;
#else
    return APP_SUCCESS;
#endif
}

#ifdef RUN_MODE_CONTROLLED_ENABLED
/****************************************************************
函数名：DealOutVolSetFree
入口参数：
出口参数：
功能：非受控模式控制放电输出电压(分开写，降低圈复杂度)
****************************************************************/
INT32S DealOutVolSetFree(const T_OneDataAttrStruct *ptInDataAttr)
{
    if (RUN_MODE_FREE != s_tSysPara.ucRunMode)
    {
        return APP_FAILURE;
    }
    return SetValueFromProtocol(NPROTOCOL_TYPE_RS485_MODBUS, INFO_TYPE_DISCH_VOLT_FREE,
        FLOAT_TO_SHORT(s_tSpecialData.fOutputVoltFree * 100.0)) == SUCCESSFUL ? APP_SUCCESS : APP_FAILURE;
}

/****************************************************************
函数名：DealChargeCurrFree
入口参数：
出口参数：
功能：非受控模式控制充电电流系数(分开写，降低圈复杂度)
****************************************************************/
INT32S DealChargeCurrFree(const T_OneDataAttrStruct *ptInDataAttr)
{
    if (RUN_MODE_FREE != s_tSysPara.ucRunMode)
    {
        return APP_FAILURE;
    }
    return SetValueFromProtocol(NPROTOCOL_TYPE_RS485_MODBUS, INFO_TYPE_CHG_COEF_FREE,
        FLOAT_TO_SHORT(s_tSpecialData.fChgCurrCoeffFree * 1000.0)) == SUCCESSFUL ? APP_SUCCESS : APP_FAILURE;
}
#endif

/****************************************************************
函数名：DealDefPara
入口参数：
出口参数：
功能：恢复缺省参数(分开写，降低圈复杂度)
****************************************************************/
INT32 DealDefPara(const T_OneDataAttrStruct *ptInDataAttr)
{
    LoadPara();
    SaveAction(GetActionId(CONTOL_RST_PARA), "MdbsDefault Para");
    return APP_SUCCESS;
}

/****************************************************************
函数名：DealTimeSet
入口参数：
出口参数：
功能：设置时间(分开写，降低圈复杂度)
****************************************************************/
INT32S DealTimeSet(const T_OneDataAttrStruct *ptInDataAttr)
{
    static Datebuff tTime;
    BYTE buff[21] = {0};
    struct tm tTimeBefore;
    time_t t = time(RT_NULL);

    if (!CheckTimeValid((T_TimeStruct *) &s_tSpecialData.tTime))
    {
        return APP_FAILURE;
    }

    rt_memset_s(&tTimeBefore, sizeof(tTimeBefore), 0x00, sizeof(tTimeBefore));

    localtime_r(&t, &tTimeBefore);

    tTime.PFC8563_Date.year    = s_tSpecialData.tTime.wYear - 1900;
    tTime.PFC8563_Date.month   = s_tSpecialData.tTime.ucMonth ;
    tTime.PFC8563_Date.date    = s_tSpecialData.tTime.ucDay;
    tTime.PFC8563_Date.hour    = s_tSpecialData.tTime.ucHour;
    tTime.PFC8563_Date.min     = s_tSpecialData.tTime.ucMinute;
    tTime.PFC8563_Date.second  = s_tSpecialData.tTime.ucSecond;

    if (PFC8563set_date(&tTime) == RT_EOK) {
        rt_snprintf_s((char *)buff, sizeof(buff), "%d-%02d-%02d %02d:%02d:%02d",
                 (WORD)(tTimeBefore.tm_year + 1900),
                 tTimeBefore.tm_mon +1,
                 tTimeBefore.tm_mday,
                 tTimeBefore.tm_hour,
                 tTimeBefore.tm_min,
                 tTimeBefore.tm_sec);
        SaveAction(GetActionId(CONTOL_SET_TIME), (char *)buff);
        return APP_SUCCESS;
    }
    else
    {
        return APP_FAILURE;
    }
}
/****************************************************************
DealAddrCompete
入口参数：
出口参数：
功能：地址竞争(分开写，降低圈复杂度)
****************************************************************/
INT32 DealAddrCompete(const T_OneDataAttrStruct *ptInDataAttr)
{

    if(s_tSysPara.ucBattAddressMode == AUTO_MODE)
    {
        restartAddrCompete();
        SaveAction(GetActionId(CONTOL_ADDR_COMPETE), "MdbsCompete Addr");
        return APP_SUCCESS;
    }
    return APP_FAILURE;

}

/****************************************************************
DealAddrSwitch
入口参数：
出口参数：
功能：地址切换（广播）
****************************************************************/
INT32 DealAddrSwitchBroadcast(const T_OneDataAttrStruct *ptInDataAttr)
{
    GetSysPara(&s_tSysPara);
    if((s_tSysPara.ucBattAddressMode == MANUAL_MODE)&&(s_tMdbsRtuApp.ucDevType == 0x00))
    {
        SetBattAddr(s_tSysPara.ucBattSwitchAddr);
        SaveAction(GetActionId(CONTOL_SWITCH_ADDR), "MdbsSwitch Addr");
        return APP_SUCCESS;
    }
    return APP_FAILURE;
}

INT32S DealCSUSendCurr(const T_OneDataAttrStruct *ptInDataAttr)
{
#ifdef RUN_MODE_CONTROLLED_ENABLED
    INT32S slReturn=APP_SUCCESS;
    INT32S slRes = 0;
    T_BCMDataStruct tBCMData;
    rt_memset_s(&tBCMData, sizeof(T_BCMDataStruct), 0, sizeof(T_BCMDataStruct));
    GetRealData(&tBCMData);
    GetSysPara(&s_tSysPara);

    if (RUN_MODE_FREE != s_tSysPara.ucRunMode)
    {
        return APP_FAILURE;
    }
    if(s_tSpecialData.fCsuSendCurr > 300.0f || s_tSpecialData.fCsuSendCurr < -300.0f)
    {
        return APP_FAILURE;
    }

    slRes = SetValueFromProtocol(NPROTOCOL_TYPE_RS485_MODBUS, INFO_TYPE_SET_MIX_BATT_CURR, FLOAT_TO_SHORT(s_tSpecialData.fCsuSendCurr*100.0f));

    switch(slRes)
    {
        case 0:
            slReturn=APP_SUCCESS;
            break;
        default:
            slReturn=APP_FAILURE;
            break;
    }
    return slReturn;
#else
    return APP_SUCCESS;
#endif
}

INT32S DealMaxSOC(const T_OneDataAttrStruct *ptInDataAttr)
{
#ifdef RUN_MODE_CONTROLLED_ENABLED
    if(!IsMaster())
    {
        return APP_FAILURE;
    }
    
    if(s_tSpecialData.wMaxSOC > 10000)
    {
        return APP_FAILURE;
    }

    SetValueFromProtocol(NPROTOCOL_TYPE_RS485_MODBUS, INFO_TYPE_SET_MAX_SOC, s_tSpecialData.wMaxSOC);
#endif
    return APP_SUCCESS;
}

INT32S DealRelaseLock(const T_OneDataAttrStruct *ptInDataAttr)
{
#ifdef RUN_MODE_CONTROLLED_ENABLED
    BduCtrl(SCI_CTRL_BDULOCK, 0);
    SaveAction(GetActionId(CONTOL_RELEASE_LOCK), "MdbsRemote Unlock");
#endif
    return APP_SUCCESS;
}


INT32S DealManualUnlock(const T_OneDataAttrStruct *ptInDataAttr)
{
    CHAR acBMS_SN[16];
    GetBmsSn((CHAR *)acBMS_SN);
    acBMS_SN[15] = '\0';
    if(s_tSpecialData.wManualUnlock != UnlockCodeCreate(acBMS_SN, rt_strnlen_s(acBMS_SN, 16)))
    {
        return APP_FAILURE;
    }
    if (GPIO_OFF == GetPMOSStatus())
    {
        return APP_FAILURE;
    }

    DealDeviceUnlock(DEVICE_UNLOCK_MANUAL);
    //SaveAction(GetActionId(CONTOL_UNLOCK_ANTITHEFT), "Mdbs UnAnti-Theft");
    return APP_SUCCESS; 
}

/****************************************************************
DealBatteryIndication
入口参数：
出口参数：
功能：查找电池
****************************************************************/
INT32S DealBatteryIndication(const T_OneDataAttrStruct *ptInDataAttr) 
{  
    RETURN_VAL_IF_FAIL(s_tMdbsRtuApp.ucDevType != 0x00, APP_FAILURE);// 不支持广播
    SetBattIndicationStatus(True);
    
    if (s_waitModbusLedQuickNormalTimer == RT_NULL) {
        s_waitModbusLedQuickNormalTimer = rt_timer_create("ModbusLedQuickNormal", CloseBattIndication, False, 15000, RT_TIMER_FLAG_ONE_SHOT);
    }
    
    if(s_waitModbusLedQuickNormalTimer != RT_NULL)
    {
        rt_timer_start(s_waitModbusLedQuickNormalTimer);
    }

    SaveAction(GetActionId(CONTOL_BATT_INDICATION),"MdbsFind Address");

    return APP_SUCCESS;
}

/* 清除直流内阻异常保护 */
INT32S DealClearDcrFaultPrt(const T_OneDataAttrStruct *ptInDataAttr)
{
    ClearDcrFaultPrt();
    return APP_SUCCESS;
}

#ifdef SITE_ANTI_THEFT_ENABLED

/***************************************************************************
 * @brief    Mdbs-站点防盗开启
 **************************************************************************/
INT32S DealStartBinding(const T_OneDataAttrStruct *ptInDataAttr)
{
    if (GetSiteAntiTheftStatus() == (BOOLEAN)SiteAntiTheftOn)
    {
        return APP_FAILURE;
    }

    DealSiteAntiTheftStart(s_tSpecialData.aucStartBinding);
    if (GetSiteAntiTheftStatus() == (BOOLEAN)SiteAntiTheftOn)
    {
        SaveAction(GetActionId(CTRL_SITE_ANTI_THEFT_ON), "Site Alm On");
        return APP_SUCCESS;
    }
    else
    {
        return APP_FAILURE;
    }
}

/***************************************************************************
 * @brief    Mdbs-站点防盗心跳
 **************************************************************************/
INT32S DealHeartbeatBinding(const T_OneDataAttrStruct *ptInDataAttr)
{
    if (GetSiteAntiTheftStatus() == (BOOLEAN)SiteAntiTheftOff)
    {
        return APP_FAILURE;
    }

    if (DealSiteAntiTheftHeartbeat(s_tSpecialData.aucHeartbeatBinding) == True)
    {
        return APP_SUCCESS;
    }
    else
    {
        return APP_FAILURE;
    }
}

/***************************************************************************
 * @brief    Mdbs-站点防盗关闭
 **************************************************************************/
INT32S DealCloseBinding(const T_OneDataAttrStruct *ptInDataAttr)
{
    if (GetSiteAntiTheftStatus() == (BOOLEAN)SiteAntiTheftOff)
    {
        return APP_SUCCESS;
    }

    DealSiteAntiTheftEnd(s_tSpecialData.aucCloseBinding);
    if (GetSiteAntiTheftStatus() == (BOOLEAN)SiteAntiTheftOff)
    {
        SaveAction(GetActionId(CTRL_SITE_ANTI_THEFT_OFF), "Site Alm Off");
        return APP_SUCCESS;
    }
    else
    {
        return APP_FAILURE;
    }
}


/***************************************************************************
 * @brief    Mdbs-站点防盗更换电子钥匙
 **************************************************************************/
INT32S DealChangeSiteAntitheftKey(const T_OneDataAttrStruct *ptInDataAttr)
{
    BYTE *pOldKey = &s_tSpecialData.aucChangeSiteAntitheftKey[0];
    BYTE *pNewKey = &s_tSpecialData.aucChangeSiteAntitheftKey[SITE_ANTITHEFT_KEY_LEN];
    if (GetSiteAntiTheftStatus() == (BOOLEAN)SiteAntiTheftOff)
    {
        return APP_FAILURE;
    }

    return (DealSiteAntiTheftChangeKey(pOldKey, pNewKey) == True) ? APP_SUCCESS : APP_FAILURE;
}

#endif

#ifdef NET_ANTI_THEFT_ENABLED
/***************************************************************************
 * @brief    Mdbs-网管防盗开启
 **************************************************************************/
INT32S DealNetStartBinding(const T_OneDataAttrStruct *ptInDataAttr)
{
    if (GetNetAntiTheftStatus() == NETANTITHEFT_ON)
    {
        return APP_FAILURE;
    }
    if (DealNetAntiTheftStart(s_tSpecialData.aucStartBindingKey) == SUCCESSFUL)
    {
        SaveAction(GetActionId(CTRL_NET_ANTI_THEFT), "Mdbs NetDefence On");
        return APP_SUCCESS;
    }
    else
    {
        return APP_FAILURE;
    }
}

/***************************************************************************
 * @brief    Mdbs-网管防盗心跳
 **************************************************************************/
INT32S DealNetbeatBinding(const T_OneDataAttrStruct *ptInDataAttr)
{
    BYTE ucResult = FAILURE;
    BYTE aucNetAntiTheftKey[NETANTITHEFT_KEY_LEN] = {0,};
    BYTE aucNetAntiTheftSN[NETANTITHEFT_SN_LEN] = {0,};
    rt_memcpy_s(aucNetAntiTheftKey, sizeof(aucNetAntiTheftKey), s_tSpecialData.aucHeartbeatBindingKey, NETANTITHEFT_KEY_LEN);
    rt_memcpy_s(aucNetAntiTheftSN, sizeof(aucNetAntiTheftSN), &s_tSpecialData.aucHeartbeatBindingKey[NETANTITHEFT_KEY_LEN], NETANTITHEFT_SN_LEN);
    if (GetNetAntiTheftStatus() == NETANTITHEFT_OFF)
    {
        return APP_FAILURE;
    }
    ucResult = DealNetAntiTheftHeartbeat(aucNetAntiTheftKey, aucNetAntiTheftSN);

    if (ucResult == SUCCESSFUL || ucResult == NET_RESPONSE_FAILURE_BY_KEY_SNCORRECT)
    {
        return APP_SUCCESS;
    }
    else
    {
        return APP_FAILURE;
    }
}

/***************************************************************************
 * @brief    Mdbs-网管防盗关闭
 **************************************************************************/
INT32S DealNetCloseBinding(const T_OneDataAttrStruct *ptInDataAttr)
{
    if (GetNetAntiTheftStatus() == NETANTITHEFT_OFF)
    {
        return APP_SUCCESS;
    }

    if (DealNetAntiTheftEnd(s_tSpecialData.aucCloseBindingKey) == SUCCESSFUL)
    {
        SaveAction(GetActionId(CTRL_NET_ANTI_THEFT), "Mdbs NetDefenceOff");
        return APP_SUCCESS;
    }
    else
    {
        return APP_FAILURE;
    }
}

/***************************************************************************
 * @brief    Mdbs-网管更换电子钥匙
 **************************************************************************/
INT32S DealNetChangeBinding(const T_OneDataAttrStruct *ptInDataAttr)
{
    if (GetNetAntiTheftStatus() == NETANTITHEFT_OFF)
    {
        return APP_FAILURE;
    }

    if (JudgeNetAntiTheftKey(s_tSpecialData.aucChangeBindingKey) != 0)
    {
        return APP_FAILURE;
    }

    if(WriteNetAntiTheftInfo(NETANTITHEFT_ON, &s_tSpecialData.aucChangeBindingKey[NETANTITHEFT_KEY_LEN]))
    {
        return APP_FAILURE;
    }
    else
    {
        SaveAction(GetActionId(CTRL_NET_ANTI_THEFT), "MdbsChangeNetDeKey");
        return APP_SUCCESS;
    }
}
#endif

/* 清除上行下流流量统计 */
INT32S DealClear4GTraffic(const T_OneDataAttrStruct *ptInDataAttr)
{
    Clear4GTraffic();
    return APP_SUCCESS;
}

#ifdef ZXESM_R321_APTOWER_VERSION
/****************************************************************************
* 函数名称：DealMdbsRtuCommandAPT()
* 调    用：无
* 被 调 用：
* 输入参数：ptInData-应用层数据包
* 返 回 值：
* 功能描述：处理命令
* 作    者  ：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
Static INT32 DealMdbsRtuCommandAPT(T_MdbsRtuApp *ptInData)
{
    Enum_MdbsRTN ucRTN = APP_RTN_CORRECT;
    T_MdbsRtuApp *ptApp = ptInData;
    INT32 slFrameLen = APP_FAILURE; //转化出来的数据域长度
    BYTE ucErrorCode = 0;

    if (NULL == ptInData || NULL == s_tMdbsRtuManage.ptDataAttr || 1 > s_tMdbsRtuManage.slDataAttrNum || (s_tMdbsRtuApp.ucDevType - s_tSysPara.ucModbusAddr == 0) || (s_tMdbsRtuApp.ucDevType - s_tSysPara.ucModbusAddr > MAX_ADDRESS))
    {
        return APP_FAILURE;
    }
    ucErrorCode = JudgeErrorCode(ptApp);
    if (ucErrorCode)
    {
        SetErrorCode(ucErrorCode);
    }
    else
    {
        slFrameLen = (*s_atMdbsRtuSendFuncTable[LOCATION_SEND_READ_ANA_IN].func)(ptInData);
        if ((ANSWER_NO == s_atMdbsRtuSendFuncTable[LOCATION_SEND_READ_ANA_IN].EAnswer)
            ||(ptInData->ucDevType == 0))
        {
            SetSndAnswer(APP_RTN_NO_ANSWER);
            return FAILURE;
        }

		ucRTN = slFrameLen > APP_FAILURE ? APP_RTN_CORRECT : APP_RTN_INCORRECT;
		SetSndAnswer(ucRTN);
		if (ucRTN == APP_RTN_INCORRECT)
		{
			SetErrorCode(ERRORCODE_ILL_DATA_VAL);
		}
	}

    s_tMdbsRtuApp.ucNeedRespond = ANSWER_YES;
    //数据长度和帧尾
    s_tMdbsRtuApp.wSendLen = slFrameLen < APP_SUCCESS ? 0 : slFrameLen;

    //将协议包转换成待发送的底层数据
    return  SUCCESSFUL;
}

static BYTE JudgeErrorCode(T_MdbsRtuApp *ptApp)
{
    WORD wCRC = 0;
    if (s_tMdbsRtuApp.wRecLen > 2)
    {
        wCRC = GetCRC16(s_tMdbsRtuApp.aucRecBuf, s_tMdbsRtuApp.wRecLen- 2);
    }

    if (ptApp->ucFunc != MODBUS_READ_ANA)
    {
        return ERRORCODE_ILL_FUNC;
    }
    else if(wCRC != s_tMdbsRtuApp.wCRC)
    {
        return ERRORCODE_FAIL_ASSOCIATED_DEV;
    }
    else if(ptApp->wRegCnt > 0X3D)
    {
        return ERRORCODE_ILL_DATA_VAL;
    }
    else
    {
        return ERRORCODE_NULL;
    }
}

/***************************************************************************
* 函数名称：SendReadAnaIn()
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：发送模拟量数据(只读)
* 作    者：fsg
* 设计日期：2010-01-24
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
Static INT32 SendReadAnaAPT(T_MdbsRtuApp *ptInApp)
{
    BYTE *p = ptInApp->aucSendBuf, *pucByteCnt = ptInApp->aucSendBuf;
    WORD wCnt = 0;
    INT32 slFindStartAddrSn = APP_FAILURE;

    p++;//字节长度
    GettCmpakDatavalue();
    if (ptInApp->wRegAddr < LOCATION_WPARAMETERS_CHANGED_B)
    {
        return ProcessLowAddr(ptInApp, p, pucByteCnt, &wCnt);
    }
    if (((ptInApp->wRegAddr > LOCATION_WPARAMETERS_CHANGED_B - 1) && (ptInApp->wRegAddr < LOCATION_AC_PROTOCOLDESCRIPTION_B_END +1)) || (ptInApp->wRegAddr > LOCATION_WEXTER_VOLT - 1))
    {
        slFindStartAddrSn = FindStartAddr(ptInApp->wRegAddr, s_tMdbsRtuManage.ptDataAttr);    

        if(APP_FAILURE < slFindStartAddrSn && FindCntSn(slFindStartAddrSn) + ptInApp->wRegCnt <= s_tMdbsRtuManage.slTotalCnt)
        {
            return ProcessMidAddr(ptInApp, p, pucByteCnt, slFindStartAddrSn, &wCnt);
        }
    }
    else
    {
        return ProcessSpareAddr(ptInApp, p, pucByteCnt, slFindStartAddrSn, &wCnt);
    }
}

static INT32 ProcessMidAddr(T_MdbsRtuApp *ptInApp, BYTE *p, BYTE *pucByteCnt, INT32 slFindStartAddrSn, WORD *wCnt)
{
    BYTE ucOneDataLen = 0;
    WORD wIndex = 0;
    while(*wCnt < ptInApp->wRegCnt)
    {
        if ((ptInApp->wRegAddr + wIndex >= LOCATION_AC_PROTOCOLDESCRIPTION_B_END + 1) && (ptInApp->wRegAddr + wIndex <= LOCATION_WEXTER_VOLT - 1))
        {
            PutInt16ToBuff((BYTE *)p, (SHORT)SPARE_ADDRESS_VALUE);
            p += 2;
            *wCnt += 1;
            wIndex++;
        }
        else if(APP_FAILURE == PacketOrParseAna(&s_tMdbsRtuManage.ptDataAttr[slFindStartAddrSn + wIndex], DATA_TO_BYTEBUFF, p, NULL))
        {
            return APP_FAILURE;
        }
        else
        {
            ucOneDataLen=s_tMdbsRtuManage.ptDataAttr[slFindStartAddrSn + wIndex].ucNewDataLen;
            p += ucOneDataLen;
            *wCnt += (ucOneDataLen / 2);
            wIndex++; 
        }
    }
    *pucByteCnt = p - pucByteCnt - 1;
    return (INT32)(p - ptInApp->aucSendBuf);
}

static INT32 ProcessSpareAddr(T_MdbsRtuApp *ptInApp, BYTE *p, BYTE *pucByteCnt, INT32 slFindStartAddrSn, WORD *wCnt)
{
    BYTE ucOneDataLen = 0;
    WORD wIndex = 0;
    while(*wCnt < ptInApp->wRegCnt)
    {
        if (ptInApp->wRegAddr + wIndex < LOCATION_WEXTER_VOLT)
        {
            PutInt16ToBuff((BYTE *)p, (SHORT) SPARE_ADDRESS_VALUE);
            p += 2;
            *wCnt += 1;
            wIndex++;
        }
        else
        {
            if(APP_FAILURE == PacketOrParseAna(&s_tMdbsRtuManage.ptDataAttr[0X00000054 + wIndex], DATA_TO_BYTEBUFF, p, NULL))
            {
                return APP_FAILURE;
            }
            ucOneDataLen = s_tMdbsRtuManage.ptDataAttr[slFindStartAddrSn + wIndex].ucNewDataLen;
            p += ucOneDataLen;
            *wCnt += (ucOneDataLen / 2);
            wIndex++; 
        }
    }
    *pucByteCnt = p - pucByteCnt - 1;
    return (INT32)(p - ptInApp->aucSendBuf);
}

static INT32 ProcessLowAddr(T_MdbsRtuApp *ptInApp, BYTE *p, BYTE *pucByteCnt, WORD *wCnt)
{
    BYTE ucOneDataLen = 0;
    WORD wIndex = 0;
    INT32 slFindStartAddrSn = APP_FAILURE;
    if (ptInApp->wRegAddr < LOCATION_WPARAMETERS_CHANGED_B)
    {
        while(*wCnt < ptInApp->wRegCnt)
        {
            if(ptInApp->wRegAddr + wIndex < LOCATION_WPARAMETERS_CHANGED_B)
            {
                slFindStartAddrSn = FindStartAddr(ptInApp->wRegAddr + LOCATION_WEXTER_VOLT, s_tMdbsRtuManage.ptDataAttr);
            }
            else
            {
                slFindStartAddrSn = FindStartAddr(ptInApp->wRegAddr, s_tMdbsRtuManage.ptDataAttr);
            }
            if (APP_FAILURE == PacketOrParseAna(&s_tMdbsRtuManage.ptDataAttr[slFindStartAddrSn + wIndex], DATA_TO_BYTEBUFF, p, NULL))
            {
                return APP_FAILURE;
            }
            ucOneDataLen = s_tMdbsRtuManage.ptDataAttr[slFindStartAddrSn + wIndex].ucNewDataLen;
            p += ucOneDataLen;
            *wCnt += (ucOneDataLen / 2);
            wIndex++; 
        }
    }
    *pucByteCnt = p - pucByteCnt - 1;
    return (INT32)(p - ptInApp->aucSendBuf);
}

Static BOOLEAN SetBit(WORD *pData, BYTE pos, BYTE flag)
{
    if(pData == NULL || pos > 15)
    {
        return FAILURE;
    }
    if (flag == 1)
    {
        *(pData) |= (1 << (pos));
    }
    else if (flag==0)
    {
        *(pData) &=~(1 << (pos));
    }
    return SUCCESSFUL;
}

Static void GettCmpakDatavalue()
{
    int i = 0, j = 0, Flag = 0;
    T_BmsPACKManufactStruct tPackInfo;
    T_BCMDataStruct tBCMData;
    T_BCMAlarmStruct tBCMAlarm;
    static T_BattResult s_tBattres;

    rt_memset_s((BYTE*)&tBCMAlarm, sizeof(T_BCMAlarmStruct), 0x00, sizeof(T_BCMAlarmStruct));
    rt_memset_s(&tBCMData, sizeof(T_BCMDataStruct), 0x00, sizeof(T_BCMDataStruct));
    rt_memset_s((BYTE *)&tPackInfo, sizeof(T_BmsPACKManufactStruct), 0x00, sizeof(T_BmsPACKManufactStruct));
    rt_memset_s((BYTE *)&s_tBmsPackFacInfo, sizeof(T_BmsPACKFactoryStruct), 0x00, sizeof(T_BmsPACKFactoryStruct));
    rt_memset_s((BYTE *)&s_tBattres, sizeof(T_BattResult), 0x00, sizeof(T_BattResult));
    GetBattResult(&s_tBattres);
    GetRealAlarm(BCM_ALARM, (BYTE *)&tBCMAlarm);
    GetRealData(&tBCMData);
    GetSysPara(&s_tSysPara);
    readPackManufact(&tPackInfo);
	readBmsPackFacInfo(&s_tBmsPackFacInfo);
    s_tCmpakData.wParametersChanged_b = SPARE_ADDRESS_VALUE;
    s_tCmpakData.wNoOfBatteryModules_b = 1;
    s_tCmpakData.wNoOfBatteryStrings_b = 1;
    s_tCmpakData.wBatteryStringCapacity_b = s_tSysPara.wBatteryCap;
    s_tCmpakData.wProdDateYear_b = tPackInfo.tBattDate.wYear;
    s_tCmpakData.wProdDateMonth_b = (WORD)tPackInfo.tBattDate.ucMonth;
    s_tCmpakData.wProdDateDay_b = (WORD)tPackInfo.tBattDate.ucDay;
    s_tCmpakData.wBatteryType_b = 0;
    s_tCmpakData.wTempCompEnable_b = s_tSysPara.bUVPTempCompensationEn;
    s_tCmpakData.wFloatChargeRefVoltage_b = SPARE_ADDRESS_VALUE;
    s_tCmpakData.wNomFloatCharegeRefTemp_b = SPARE_ADDRESS_VALUE;
    s_tCmpakData.wTempCompSlope_b = SPARE_ADDRESS_VALUE;
    s_tCmpakData.wMaxChargeVoltage_b = SPARE_ADDRESS_VALUE;
    s_tCmpakData.wMinChargeVoltage_b = SPARE_ADDRESS_VALUE;
    s_tCmpakData.wBatteryCurrentLimitEnable_b = tBCMData.ucBattChargeEn | tBCMData.ucBattDischEn;
    s_tCmpakData.wMaxBatteryChargeCurr_b = s_tSysPara.fChargeMaxCurr * 10 * s_tSysPara.wBatteryCap;
    s_tCmpakData.wMaxBatteryDisChargeCurr_b = s_tSysPara.fDischgMaxCurr * 10 * s_tSysPara.wBatteryCap;
    s_tCmpakData.wBoostChargeRefVoltage_b = SPARE_ADDRESS_VALUE;
    s_tCmpakData.wEqualizeChargeRefVoltage_b = SPARE_ADDRESS_VALUE;
    s_tCmpakData.wBatteryRate_b = BATTERY_RATE;
    s_tCmpakData.wExterVolt = tBCMData.fExterVolt * 100;
    s_tCmpakData.fBattCurr = tBCMData.fBusCurr * 10;
    s_tCmpakData.wModuleTemperature = tBCMData.fCellTempMax;
    s_tCmpakData.wModuleTemperature *= 10;
    s_tCmpakData.wBattSOH = tBCMData.wBattSOH;
    s_tCmpakData.wBattSOC = (tBCMData.wBattSOC * s_tSysPara.wBatteryCap) / 100;
    s_tCmpakData.wModuleImpedance = MODULE_IMPEDANCE; 
    s_tCmpakData.wTimeLeft = tBCMData.wDisChargeLeftMinutes;
    s_tCmpakData.wAccumulatedDischarged = s_tBattres.fTotalDischargeCap / 100;
    s_tCmpakData.wBatteryCap = s_tSysPara.wBatteryCap;
    s_tCmpakData.wAccumulatedCharged = s_tCmpakData.wAccumulatedDischarged;
    s_tCmpakData.wBattdischargeCycleTimes = tBCMData.wBattCycleTimes;
    s_tCmpakData.wBattchargeCycleTimes = tBCMData.wBattCycleTimes;

    j = 0;
    for (i = 0; i < sizeof(s_tBmsPackFacInfo.acDeviceSn); i++)
    {	
        if(i % 2 == 0)
        {
            s_tCmpakData.acBatteryPartNumber[j] |= ((s_tBmsPackFacInfo.acDeviceSn[i] & 0x00FF) << 8);
            s_tCmpakData.acPartNumber_b[j] = s_tCmpakData.acBatteryPartNumber[j];
        }
        else
        {
            s_tCmpakData.acBatteryPartNumber[j] |= (s_tBmsPackFacInfo.acDeviceSn[i] & 0x00FF);
            s_tCmpakData.acPartNumber_b[j] = s_tCmpakData.acBatteryPartNumber[j];
            j++;
        }
    } 
    SetBit(&s_tCmpakData.wModuleWarnings, 0, tBCMAlarm.ucChgCurrHighAlm);

    if (tBCMData.ucBattPackSta == BATT_MODE_OFFLINE || tBCMData.ucBattPackSta == BATT_MODE_CONSRV || tBCMData.ucBattPackSta == BATT_MODE_INVALID )
    {
        SetBit(&s_tCmpakData.wModuleStatus, 1, 1);
    }
    else
    {
        SetBit(&s_tCmpakData.wModuleStatus, 0, 1);
        SetBit(&s_tCmpakData.wModuleStatus, 2, tBCMData.ucBattChargeEn | tBCMData.ucBattDischEn);
        Flag = (tBCMData.ucBattChargeEn | tBCMData.ucBattDischEn) ? 0 : 1;
        SetBit(&s_tCmpakData.wModuleStatus, 3, Flag);
        Flag = tBCMData.ucBattPackSta == BATT_MODE_CHARGE ? 1 : 0;
        SetBit(&s_tCmpakData.wModuleStatus, 4, Flag);
        Flag = tBCMData.ucBattPackSta == BATT_MODE_STANDBY ? 1 : 0;
        SetBit(&s_tCmpakData.wModuleStatus, 5, Flag);
        Flag = tBCMData.ucBattPackSta == BATT_MODE_DISCHARGE ? 1 : 0;
        SetBit(&s_tCmpakData.wModuleStatus, 6, Flag);
        SetBit(&s_tCmpakData.wModuleStatus, 10, s_tBattres.ucBattIdleStatus);
        Flag = tBCMData.ucBattPackSta == BATT_MODE_STANDBY ? 1 : 0;
        SetBit(&s_tCmpakData.wModuleStatus, 11, Flag);
    }
    GetCmpakDataVlueRemain();
}

static void GetCmpakDataVlueRemain()
{
    int i = 0, j = 0, Flag = 0;
    T_BmsPACKManufactStruct tPackInfo;
    T_BCMDataStruct tBCMData;
    T_BCMAlarmStruct tBCMAlarm;

    rt_memset_s((BYTE*)&tBCMAlarm, sizeof(T_BCMAlarmStruct), 0x00, sizeof(T_BCMAlarmStruct));
    rt_memset_s(&tBCMData, sizeof(T_BCMDataStruct), 0x00, sizeof(T_BCMDataStruct));
    rt_memset_s((BYTE *)&tPackInfo, sizeof(T_BmsPACKManufactStruct), 0x00, sizeof(T_BmsPACKManufactStruct));
    rt_memset_s((BYTE *)&s_tBmsPackFacInfo, sizeof(T_BmsPACKFactoryStruct), 0x00, sizeof(T_BmsPACKFactoryStruct));

    
    GetRealData(&tBCMData);
    readPackManufact(&tPackInfo);
	readBmsPackFacInfo(&s_tBmsPackFacInfo);
    GetRealAlarm(BCM_ALARM, (BYTE *)&tBCMAlarm);
    for(i = 0; i < NUM_SPARE_REGISTER_1; i++)
    {
        s_tCmpakData.aBattSparesamll[i] = SPARE_ADDRESS_VALUE;
    }
    for(i = 0; i < NUM_SPARE_REGISTER_2; i++)
    {
        s_tCmpakData.aBattSparelarge[i] = SPARE_ADDRESS_VALUE;
    }
    if (tBCMAlarm.ucEnvTempHighAlm | tBCMAlarm.ucEnvTempLowAlm | tBCMAlarm.ucBattOverVoltAlm | tBCMAlarm.ucBattUnderVoltAlm)
    {
        Flag = 1;
    }
    SetBit(&s_tCmpakData.wModuleAlarms, 0, Flag);
    SetBit(&s_tCmpakData.wModuleAlarms, 1, tBCMAlarm.ucCellVoltSampleFault);
    j = 0;
    for (i = 0; i < CELL_VOL_NUM; i++)
    {
        j += tBCMAlarm.aucCellOverVoltPrt[i];
    }
    Flag = j > 0 ? 1 : 0; 
    SetBit(&s_tCmpakData.wModuleAlarms, 2, (tBCMAlarm.ucBattOverVoltPrt | Flag));
    j = 0;
    for (i = 0; i < CELL_VOL_NUM; i++)
    {
        j += tBCMAlarm.aucCellOverVoltAlm[i];
    }
    Flag = j > 0 ? 1 : 0; 
    SetBit(&s_tCmpakData.wModuleAlarms, 3, Flag);
    SetBit(&s_tCmpakData.wModuleAlarms, 4, tBCMAlarm.ucChgCurrHighPrt);
    SetBit(&s_tCmpakData.wModuleAlarms, 5, tBCMAlarm.ucDischgCurrHighPrt);
    SetBit(&s_tCmpakData.wModuleAlarms, 6, tBCMAlarm.ucBoardTempHighPrt);
    j = 0;
    for (i = 0; i < CELL_TEMP_NUM; i++)
    {
        j += tBCMAlarm.aucChgTempLowPrt[i];
        j += tBCMAlarm.aucDischgTempLowPrt[i];
    }
    Flag = j > 0 ? 1 : 0; 
    SetBit(&s_tCmpakData.wModuleAlarms, 7, Flag);
    SetBit(&s_tCmpakData.wModuleAlarms, 8, tBCMAlarm.ucBattShortCut);
    SetBit(&s_tCmpakData.wModuleAlarms, 9, tBCMAlarm.ucBattReverse);
    SetBit(&s_tCmpakData.wModuleAlarms, 10, tBCMAlarm.ucChgLoopInvalid | tBCMAlarm.ucDischgLoopInvalid | tBCMAlarm.ucCurrLimLoopInvalid);
    s_tCmpakData.wBattVolt = tBCMData.fBattVolt * 100;
    s_tCmpakData.wModbusVer = MODBUS_DOC_VERSION;
    s_tCmpakData.wParametersChanged = SPARE_ADDRESS_VALUE; 
    s_tCmpakData.wMaxBattChargeCurr = s_tSysPara.fChargeMaxCurr * 10 * s_tSysPara.wBatteryCap;
    s_tCmpakData.wMaxBattDischargeCurr = s_tSysPara.fDischgMaxCurr * 10 * s_tSysPara.wBatteryCap;
    s_tCmpakData.wBatteryProdDateYear = tPackInfo.tBattDate.wYear;
    s_tCmpakData.wBatteryProdDateMonth = (WORD)tPackInfo.tBattDate.ucMonth;
    s_tCmpakData.wBatteryProdDateDay = (WORD)tPackInfo.tBattDate.ucDay;
    //ZXESM R321
    s_tCmpakData.acBatteryModelDescription[0] = 0X5A58;
    s_tCmpakData.acBatteryModelDescription[1] = 0X4553;
    s_tCmpakData.acBatteryModelDescription[2] = 0X4D20;
    s_tCmpakData.acBatteryModelDescription[3] = 0X5233;
    s_tCmpakData.acBatteryModelDescription[4] = 0X3231;
    s_tCmpakData.acBatteryModelDescription[5] = 0;
    s_tCmpakData.acBatteryModelDescription[6] = 0;
    s_tCmpakData.acBatteryModelDescription[7] = 0;
    s_tCmpakData.acModelDescription_b[0] = s_tCmpakData.acBatteryModelDescription[0];
    s_tCmpakData.acModelDescription_b[1] = s_tCmpakData.acBatteryModelDescription[1];
    s_tCmpakData.acModelDescription_b[2] = s_tCmpakData.acBatteryModelDescription[2];
    s_tCmpakData.acModelDescription_b[3] = s_tCmpakData.acBatteryModelDescription[3];
    s_tCmpakData.acModelDescription_b[4] = s_tCmpakData.acBatteryModelDescription[4];
    s_tCmpakData.acModelDescription_b[5] = s_tCmpakData.acBatteryModelDescription[5];
    s_tCmpakData.acModelDescription_b[6] = s_tCmpakData.acBatteryModelDescription[6];
    s_tCmpakData.acModelDescription_b[7] = s_tCmpakData.acBatteryModelDescription[7];
    s_tCmpakData.wBatteryType = 0;
    j = 0;
    for (i = 0; i < sizeof(s_tBmsPackFacInfo.acBmsFacSn); i++)
    {	
        if(i % 2 == 0)
        {
            s_tCmpakData.acBatterySerialNumber[j] |= ((s_tBmsPackFacInfo.acBmsFacSn[i] & 0x00FF) << 8);
        }
        else
        {
            s_tCmpakData.acBatterySerialNumber[j] |= (s_tBmsPackFacInfo.acBmsFacSn[i] & 0x00FF);
            j++;
        }
    }
    s_tCmpakData.acSerialNumber_b[0] = s_tCmpakData.acBatterySerialNumber[0];
    s_tCmpakData.acSerialNumber_b[1] = s_tCmpakData.acBatterySerialNumber[1];
    s_tCmpakData.acSerialNumber_b[2] = s_tCmpakData.acBatterySerialNumber[2];
    s_tCmpakData.acSerialNumber_b[3] = s_tCmpakData.acBatterySerialNumber[3];
    s_tCmpakData.acSerialNumber_b[4] = s_tCmpakData.acBatterySerialNumber[4];
    s_tCmpakData.acSerialNumber_b[5] = s_tCmpakData.acBatterySerialNumber[5];
    s_tCmpakData.acSerialNumber_b[6] = s_tCmpakData.acBatterySerialNumber[6];
    s_tCmpakData.acSerialNumber_b[7] = s_tCmpakData.acBatterySerialNumber[7];
    // 1.3.4
    s_tCmpakData.acBatterySoftwareVersion[0] = 0X312E;
    s_tCmpakData.acBatterySoftwareVersion[1] = 0X332E;
    s_tCmpakData.acBatterySoftwareVersion[2] = 0X3400;
    s_tCmpakData.acBatterySoftwareVersion[3] = 0;
    s_tCmpakData.acSoftwareVersion_b[0] =  s_tCmpakData.acBatterySoftwareVersion[0];
    s_tCmpakData.acSoftwareVersion_b[1] =  s_tCmpakData.acBatterySoftwareVersion[1];
    s_tCmpakData.acSoftwareVersion_b[2] =  s_tCmpakData.acBatterySoftwareVersion[2];
    s_tCmpakData.acSoftwareVersion_b[3] =  s_tCmpakData.acBatterySoftwareVersion[3];
    //此处R321应该上送SmartLi
    s_tCmpakData.acBatteryHardwareVersion[0] = 0X536D;
    s_tCmpakData.acBatteryHardwareVersion[1] = 0X6172;
    s_tCmpakData.acBatteryHardwareVersion[2] = 0X744C;
    s_tCmpakData.acBatteryHardwareVersion[3] = 0X6900;
    s_tCmpakData.acHardwareVersion_b[0] = s_tCmpakData.acBatteryHardwareVersion[0];
    s_tCmpakData.acHardwareVersion_b[1] = s_tCmpakData.acBatteryHardwareVersion[1];
    s_tCmpakData.acHardwareVersion_b[2] = s_tCmpakData.acBatteryHardwareVersion[2];
    s_tCmpakData.acHardwareVersion_b[3] = s_tCmpakData.acBatteryHardwareVersion[3];
    s_tCmpakData.wBatteryRate = BATTERY_RATE;  
    s_tCmpakData.wNoOfCells = CELL_VOL_NUM; 
    s_tCmpakData.wCellSize = CELL_SIZE; 
    // ZTE Corporation
    s_tCmpakData.acManufacturerName[0] = 0X5A54;
    s_tCmpakData.acManufacturerName[1] = 0X4520;
    s_tCmpakData.acManufacturerName[2] = 0X436F;
    s_tCmpakData.acManufacturerName[3] = 0X7270;
    s_tCmpakData.acManufacturerName[4] = 0X6F72;
    s_tCmpakData.acManufacturerName[5] = 0X6174;
    s_tCmpakData.acManufacturerName[6] = 0X696F;
    s_tCmpakData.acManufacturerName[7] = 0X6E00;
    s_tCmpakData.acManufacturerName_b[0] = s_tCmpakData.acManufacturerName[0];
    s_tCmpakData.acManufacturerName_b[1] = s_tCmpakData.acManufacturerName[1];
    s_tCmpakData.acManufacturerName_b[2] = s_tCmpakData.acManufacturerName[2];
    s_tCmpakData.acManufacturerName_b[3] = s_tCmpakData.acManufacturerName[3];
    s_tCmpakData.acManufacturerName_b[4] = s_tCmpakData.acManufacturerName[4];
    s_tCmpakData.acManufacturerName_b[5] = s_tCmpakData.acManufacturerName[5];
    s_tCmpakData.acManufacturerName_b[6] = s_tCmpakData.acManufacturerName[6];
    s_tCmpakData.acManufacturerName_b[7] = s_tCmpakData.acManufacturerName[7];
    // ZTE Corporation
    s_tCmpakData.acName[0] = 0X5A54;
    s_tCmpakData.acName[1] = 0X4520;
    s_tCmpakData.acName[2] = 0X436F;
    s_tCmpakData.acName[3] = 0X7270;
    s_tCmpakData.acName[4] = 0X6F72;
    s_tCmpakData.acName[5] = 0X6174;
    s_tCmpakData.acName[6] = 0X696F;
    s_tCmpakData.acName[7] = 0X6E00;
    s_tCmpakData.acName_b[0] = s_tCmpakData.acName[0];
    s_tCmpakData.acName_b[1] = s_tCmpakData.acName[1];
    s_tCmpakData.acName_b[2] = s_tCmpakData.acName[2];
    s_tCmpakData.acName_b[3] = s_tCmpakData.acName[3];
    s_tCmpakData.acName_b[4] = s_tCmpakData.acName[4];
    s_tCmpakData.acName_b[5] = s_tCmpakData.acName[5];
    s_tCmpakData.acName_b[6] = s_tCmpakData.acName[6];
    s_tCmpakData.acName_b[7] = s_tCmpakData.acName[7];
    //此处R321应该上送SmartLi
    s_tCmpakData.acBatteryHardwareVersion[0] = 0X536D;
    s_tCmpakData.acBatteryHardwareVersion[1] = 0X6172;
    s_tCmpakData.acBatteryHardwareVersion[2] = 0X744C;
    s_tCmpakData.acBatteryHardwareVersion[3] = 0X6900; 
    // ModBus V1.30
    s_tCmpakData.acProtocolDescription[0] = 0X4D6F;
    s_tCmpakData.acProtocolDescription[1] = 0X6442;
    s_tCmpakData.acProtocolDescription[2] = 0X7573;
    s_tCmpakData.acProtocolDescription[3] = 0X2056;
    s_tCmpakData.acProtocolDescription[4] = 0X312E;
    s_tCmpakData.acProtocolDescription[5] = 0X3330;
    s_tCmpakData.acProtocolDescription[6] = 0;
    s_tCmpakData.acProtocolDescription[7] = 0;
    s_tCmpakData.acProtocolDescription_b[0] = s_tCmpakData.acProtocolDescription[0];
    s_tCmpakData.acProtocolDescription_b[1] = s_tCmpakData.acProtocolDescription[1];
    s_tCmpakData.acProtocolDescription_b[2] = s_tCmpakData.acProtocolDescription[2];
    s_tCmpakData.acProtocolDescription_b[3] = s_tCmpakData.acProtocolDescription[3];
    s_tCmpakData.acProtocolDescription_b[4] = s_tCmpakData.acProtocolDescription[4];
    s_tCmpakData.acProtocolDescription_b[5] = s_tCmpakData.acProtocolDescription[5];
    s_tCmpakData.acProtocolDescription_b[6] = s_tCmpakData.acProtocolDescription[6];
    s_tCmpakData.acProtocolDescription_b[7] = s_tCmpakData.acProtocolDescription[7];
}
#endif

#ifdef PAKISTAN_CMPAK_PROTOCOL
// 初始化cmpak协议结构体参数

Static BOOLEAN InitCmpakPara(T_CmpakStruct *ptCmpakPara)
{
    if(NULL == ptCmpakPara){return FAILURE;}
    T_BCMAlarmStruct tBCMAlarm;
    T_BCMDataStruct tBCMData;

    rt_memset_s(&tBCMAlarm, sizeof(T_BCMAlarmStruct), 0x00, sizeof(T_BCMAlarmStruct));
    rt_memset_s(&tBCMData, sizeof(T_BCMDataStruct), 0x00, sizeof(T_BCMDataStruct));

    GetRealAlarm(BCM_ALARM, (BYTE *)&tBCMAlarm);
    GetRealData(&tBCMData);
    GetSysPara(&s_tSysPara);

    ptCmpakPara->fBusbarVolt  = tBCMData.fExterVolt;
    ptCmpakPara->fBattVolt    = tBCMData.fBattVolt;
    ptCmpakPara->fBattCurr    = tBCMData.fBattCurr;
    ptCmpakPara->wBattSOC     = tBCMData.wBattSOC;
    ptCmpakPara->wBattSOH     = tBCMData.wBattSOH;
    ptCmpakPara->fMaxCellTemp = tBCMData.fCellTempMax;
    ptCmpakPara->fMinCellTemp = tBCMData.fCellTempMin;
    ptCmpakPara->wDischgTimesHigh = tBCMData.wDischgTimesHigh;
    ptCmpakPara->wDischgTimesLow  = tBCMData.wDischgTimesLow;
    ptCmpakPara->wTotalDischgCapHigh = tBCMData.wTotalDischgCapHigh;
    ptCmpakPara->wTotalDischgCapLow  = tBCMData.wTotalDischgCapLow;
    ptCmpakPara->wVersion            = BATTERY_SOFTWARE_VERSION_CMPAK;
    ptCmpakPara->wHardwareVer        = BATTERY_HARDWARE_VERSION_CMPAK;
    ptCmpakPara->wBootLoaderVer      = BATTERY_BOOT_VERSION_CMPAK;
    ptCmpakPara->ucDeviceType        = BATTERY_DEVICE_TYPE_CMPAK;
    ptCmpakPara->ucManufature        = BATTERY_MANUFATURE;
    ptCmpakPara->wSubSoftwareID      = BATTERY_SUB_SOFTWARE_ID;
    ptCmpakPara->wBatteryCap         = s_tSysPara.wBatteryCap;
    ptCmpakPara->ucCellNum           = CELL_VOL_NUM;
    ptCmpakPara->wOutputPower        = (WORD)(tBCMData.fBattCurr > 0? 0:(-1.0 * tBCMData.fBattVolt * tBCMData.fBattCurr));
    ptCmpakPara->fExtBusbarVolt      = tBCMData.fExterVolt; 
    ptCmpakPara->wAccuBattRunTimeHigh = tBCMData.wAccuBattRunTimeHigh;
    ptCmpakPara->wAccuBattRunTimeLow = tBCMData.wAccuBattRunTimeLow;
    ptCmpakPara->wDischgWHHigh    = tBCMData.wTotalDischgQHigh;
    ptCmpakPara->wDischgWHLow     = tBCMData.wTotalDischgQLow;
    ptCmpakPara->wBattCycTimeHigh = (WORD)(tBCMData.wBattCycleTimes / 65536);
    ptCmpakPara->wBattCycTimeLow  = (WORD)(tBCMData.wBattCycleTimes % 65536);

    // 单体电压上送
    for(BYTE i = 0; i < CELL_VOL_NUM; i++)
    {
        ptCmpakPara->afCellVolt[i] = tBCMData.afCellVolt[i];
    }
    ptCmpakPara->afCellVolt[CELL_VOL_NUM] = 0;

    SetCmpakRegDefault(ptCmpakPara, RESERVE_REG_2, REG_DEFAULT_VALUE_1);
    SetCmpakRegDefault(ptCmpakPara, RESERVE_REG_3, REG_DEFAULT_VALUE_1);
    SetCmpakRegDefault(ptCmpakPara, RESERVE_REG_4, REG_DEFAULT_VALUE_1);
    SetCmpakRegDefault(ptCmpakPara, CELL_TEMP_EXTRAL, REG_DEFAULT_VALUE_2);
    SetCmpakRegDefault(ptCmpakPara, CELL_VOLT_EXTRAL, REG_DEFAULT_VALUE_1);
    SetBmsName(ptCmpakPara);
    JudgeCmpakBattStatus(ptCmpakPara, &tBCMData);
    ParseCmpakCellTemp(ptCmpakPara, &tBCMData);
    ParseCmpakAlarmData(ptCmpakPara, &tBCMAlarm, CRITICAL_BATTERY_ALARM_1);
    ParseCmpakAlarmData(ptCmpakPara, &tBCMAlarm, CRITICAL_BATTERY_ALARM_2);
    ParseCmpakAlarmData(ptCmpakPara, &tBCMAlarm, CRITICAL_BATTERY_ALARM_3);
    ParseCmpakAlarmData(ptCmpakPara, &tBCMAlarm, MAJOR_BATTERY_ALARM);
    ParseCmpakAlarmData(ptCmpakPara, &tBCMAlarm, MINOR_BATTERY_ALARM);
    ParseCmpakAlarmData(ptCmpakPara, &tBCMAlarm, MODULE_BATTERY_ALARM);

    return SUCCESSFUL;
}

// 设置保留寄存器默认状态

Static BOOLEAN SetCmpakRegDefault(T_CmpakStruct *ptCmpakPara , BYTE DataType, WORD RegContent)
{
    if(NULL == ptCmpakPara)
    {
        return FAILURE;
    }

    switch (DataType)
    {
        case RESERVE_REG_2:
            for(BYTE i = 0; i < REGADDR_RESERVED_2; i++)
            {
                ptCmpakPara->awReserved2[i] = RegContent;
            }
            break;
        case RESERVE_REG_3:
            for(BYTE i = 0; i < REGADDR_RESERVED_3; i++)
            {
                ptCmpakPara->awReserved3[i] = RegContent;
            }
            break;
        case RESERVE_REG_4:
            for(BYTE i = 0; i < REGADDR_RESERVED_4; i++)
            {
                ptCmpakPara->awReserved4[i] = RegContent;
            }
            break;
        case CELL_TEMP_EXTRAL:
            for(BYTE i = 0; i < EXTRAL_CELL_TEMP; i++)
            {
                ptCmpakPara->afCellTempAdd[i] = RegContent;
            }
            break;
        case CELL_VOLT_EXTRAL:
            for(BYTE i = 0; i < EXTRAL_CELL_VOLT; i++)
            {
                ptCmpakPara->afCellVoltAdd[i] = RegContent;
            }
            break;
        default:
            break;
    }

    return SUCCESSFUL;
}

// 设置寄存器位状态

Static BOOLEAN SetCmpakBit(WORD* p, BYTE BitAddr, BYTE Value)
{
    if(NULL == p)
    {
        return FAILURE;
    }

    if (Value == 1)
    {
        *p |= (1 << (BitAddr));
    }
    else if (Value == 0)
    {
        *p &= ~(1 << (BitAddr));
    }

    return SUCCESSFUL;
}

// 判断巴基斯坦cmpak电池状态

Static BOOLEAN JudgeCmpakBattStatus(T_CmpakStruct *ptCmpakPara, T_BCMDataStruct *ptBcmData)
{
    if(NULL == ptCmpakPara || NULL == ptBcmData)
    {
        return FAILURE;
    }

    BYTE ucBattStatus = ptBcmData->ucBattPackSta;
    switch (ucBattStatus)
    {
        case BATT_MODE_CHARGE:
            ptCmpakPara->wModuleMode = CHARGE_BATTERY_STATUS_CMPAK; 
            break;
        case BATT_MODE_DISCHARGE:
            ptCmpakPara->wModuleMode = DISCHARGE_BATTERY_STATUS_CMPAK; 
            break;
        case BATT_MODE_OFFLINE:
            ptCmpakPara->wModuleMode = OFFLINE_BATTERY_STATUS_CMPAK;  
            break;
        case BATT_MODE_STANDBY:
            ptCmpakPara->wModuleMode = STANDBY_BATTERY_STATUS_CMPAK; 
            break;
        case BATT_MODE_CONSRV:
            ptCmpakPara->wModuleMode = CONSRV_BATTERY_STATUS_CMPAK;   
            break;
        case BATT_MODE_INVALID:
            ptCmpakPara->wModuleMode = INVALID_BATTERY_STATUS_CMPAK; 
            break;
        default:
            break;
    }
    return SUCCESSFUL;
}

// 解析cmpak电芯温度

Static BOOLEAN ParseCmpakCellTemp(T_CmpakStruct *ptCmpakPara, T_BCMDataStruct *ptBcmData)
{
    if(NULL == ptCmpakPara || NULL == ptBcmData)
    {
        return FAILURE;
    }

    FLOAT fCmpakTemp_1 = (ptBcmData->fCellTempMax - ptBcmData->fCellTempMin) / 2 + ptBcmData->fCellTempMin;
    FLOAT fCmpakTemp_2 = (ptBcmData->fCellTempMax - ptBcmData->fCellTempMin) / 3 + ptBcmData->fCellTempMin;

    for(BYTE i = 0; i < CELL_VOL_NUM; i++)
    {
        if(i < 4 )
        {
            ptCmpakPara->afCellTemp[i] = ptBcmData->afCellTemp[i];
        }
        else if((i == 5) || (i == 7) || (i == 8) || (i == 10) || (i == 12) || (i == 13))
        {
            ptCmpakPara->afCellTemp[i] = fCmpakTemp_1;
        }
        else if((i == 6) || (i == 9) || (i == 14))
        {
            ptCmpakPara->afCellTemp[i] = fCmpakTemp_2;
        }
        else
        {
            ptCmpakPara->afCellTemp[i] = ptBcmData->fCellTempMin;
        }
    }
    ptCmpakPara->afCellTemp[CELL_VOL_NUM] = 0;

    return SUCCESSFUL;
}

// 告警解析

Static BOOLEAN ParseCmpakAlarmData(T_CmpakStruct *ptCmpakPara, T_BCMAlarmStruct *ptBCMAlarm, BYTE alarmtype)
{
    if (NULL == ptCmpakPara || NULL == ptBCMAlarm)
    {
        return FAILURE;
    }

    BYTE ucAlarmType = alarmtype;
    BOOLEAN bOverLoadLock = GetOverLoadLockStatus();

    switch (ucAlarmType)
    {
    case CRITICAL_BATTERY_ALARM_1:
        HandleCriticalAlarm1(ptCmpakPara, ptBCMAlarm, bOverLoadLock);
        break;
    case CRITICAL_BATTERY_ALARM_2:
        HandleCriticalAlarm2(ptCmpakPara, ptBCMAlarm);
        break;
    case CRITICAL_BATTERY_ALARM_3:
        ptCmpakPara->wCriticalAlarmStatus3 = 0;
        break;
    case MAJOR_BATTERY_ALARM:
        HandleMajorAlarm(ptCmpakPara, ptBCMAlarm);
        break;
    case MINOR_BATTERY_ALARM:
        HandleMinorAlarm(ptCmpakPara, ptBCMAlarm);
        break;
    case MODULE_BATTERY_ALARM:
        HandleModuleAlarm(ptCmpakPara, ptBCMAlarm);
        break;
    default:
        break;
    }

    return SUCCESSFUL;
}

static BOOLEAN HandleCriticalAlarm1(T_CmpakStruct *ptCmpakPara, T_BCMAlarmStruct *ptBCMAlarm, BOOLEAN status)
{
    SetCmpakBit(&ptCmpakPara->wCriticalAlarmStatus1, 0, ptBCMAlarm->ucHeaterFilmFailure);
    SetCmpakBit(&ptCmpakPara->wCriticalAlarmStatus1, 1, ptBCMAlarm->ucBattVoltSampleAlm);
    for (BYTE i = 0; i < CELL_TEMP_NUM; i++)
    {
        if (ptBCMAlarm->ucCellTempSensorInvalidAlm[i])
        {
            SetCmpakBit(&ptCmpakPara->wCriticalAlarmStatus1, 2, True);
        }
    }
    SetCmpakBit(&ptCmpakPara->wCriticalAlarmStatus1, 3, ptBCMAlarm->ucChgLoopInvalid);
    SetCmpakBit(&ptCmpakPara->wCriticalAlarmStatus1, 4, False);
    SetCmpakBit(&ptCmpakPara->wCriticalAlarmStatus1, 5, status);
    SetCmpakBit(&ptCmpakPara->wCriticalAlarmStatus1, 6, ptBCMAlarm->ucDischgLoopInvalid);
    return True;
}

static BOOLEAN HandleCriticalAlarm2(T_CmpakStruct *ptCmpakPara, T_BCMAlarmStruct *ptBCMAlarm)
{
    for (BYTE i = 0; i < 16; i++)
    {
        SetCmpakBit(&ptCmpakPara->wCriticalAlarmStatus2, i, ptBCMAlarm->aucCellDamagePrt[i]);
    }
    return True;
}

static BOOLEAN HandleMajorAlarm(T_CmpakStruct *ptCmpakPara, T_BCMAlarmStruct *ptBCMAlarm)
{
    for (BYTE i = 0; i < CELL_TEMP_NUM; i++)
    {
        if (ptBCMAlarm->aucChgTempLowPrt[i]) SetCmpakBit(&ptCmpakPara->wMajorAlarmStatus, 0, True);
        if (ptBCMAlarm->aucChgTempHighPrt[i]) SetCmpakBit(&ptCmpakPara->wMajorAlarmStatus, 1, True);
        if (ptBCMAlarm->aucDischgTempLowPrt[i]) SetCmpakBit(&ptCmpakPara->wMajorAlarmStatus, 2, True);
        if (ptBCMAlarm->aucDischgTempHighPrt[i]) SetCmpakBit(&ptCmpakPara->wMajorAlarmStatus, 3, True);
    }
    SetCmpakBit(&ptCmpakPara->wMajorAlarmStatus, 4, ptBCMAlarm->ucBattOverVoltPrt);
    SetCmpakBit(&ptCmpakPara->wMajorAlarmStatus, 6, ptBCMAlarm->ucBattUnderVoltPrt);
    for (BYTE i = 0; i < CELL_VOL_NUM_MAX; i++)
    {
        if (ptBCMAlarm->aucCellOverVoltPrt[i]) SetCmpakBit(&ptCmpakPara->wMajorAlarmStatus, 5, True);
        if (ptBCMAlarm->aucCellUnderVoltPrt[i]) SetCmpakBit(&ptCmpakPara->wMajorAlarmStatus, 7, True);
    }
    SetCmpakBit(&ptCmpakPara->wMajorAlarmStatus, 8, ptBCMAlarm->ucBDUBusVoltHighPrt);
    SetCmpakBit(&ptCmpakPara->wMajorAlarmStatus, 9, ptBCMAlarm->ucBoardTempHighPrt);
    SetCmpakBit(&ptCmpakPara->wMajorAlarmStatus, 10, ptBCMAlarm->ucBattShortCut);
    SetCmpakBit(&ptCmpakPara->wMajorAlarmStatus, 11, ptBCMAlarm->ucBattSOCLowPrt);
    SetCmpakBit(&ptCmpakPara->wMajorAlarmStatus, 12, ptBCMAlarm->ucBattReverse);
    SetCmpakBit(&ptCmpakPara->wMajorAlarmStatus, 13, ptBCMAlarm->ucBDUBattLockAlm);
    SetCmpakBit(&ptCmpakPara->wMajorAlarmStatus, 14, ptBCMAlarm->ucBDUBusVoltLowPrt);

    return True;
}

static BOOLEAN HandleMinorAlarm(T_CmpakStruct *ptCmpakPara, T_BCMAlarmStruct *ptBCMAlarm)
{
    for (BYTE i = 0; i < CELL_TEMP_NUM; i++)
    {
        if (ptBCMAlarm->aucChgTempLowAlm[i]) SetCmpakBit(&ptCmpakPara->wMinorAlarmStatus, 0, True);
        if (ptBCMAlarm->aucChgTempHighAlm[i]) SetCmpakBit(&ptCmpakPara->wMinorAlarmStatus, 1, True);
        if (ptBCMAlarm->aucDischgTempLowAlm[i]) SetCmpakBit(&ptCmpakPara->wMinorAlarmStatus, 2, True);
        if (ptBCMAlarm->aucDischgTempHighAlm[i]) SetCmpakBit(&ptCmpakPara->wMinorAlarmStatus, 3, True);
    }
    SetCmpakBit(&ptCmpakPara->wMinorAlarmStatus, 4, ptBCMAlarm->ucBattOverVoltAlm);
    SetCmpakBit(&ptCmpakPara->wMinorAlarmStatus, 6, ptBCMAlarm->ucBattUnderVoltAlm);
    for (BYTE i = 0; i < CELL_VOL_NUM_MAX; i++)
    {
        if (ptBCMAlarm->aucCellOverVoltAlm[i]) SetCmpakBit(&ptCmpakPara->wMinorAlarmStatus, 5, True);
        if (ptBCMAlarm->aucCellUnderVoltAlm[i]) SetCmpakBit(&ptCmpakPara->wMinorAlarmStatus, 7, True);
    }
    SetCmpakBit(&ptCmpakPara->wMinorAlarmStatus, 8, False);
    SetCmpakBit(&ptCmpakPara->wMinorAlarmStatus, 9, ptBCMAlarm->ucAddressClash);
    return True;
}

static BOOLEAN HandleModuleAlarm(T_CmpakStruct *ptCmpakPara, T_BCMAlarmStruct *ptBCMAlarm)
{
    SetCmpakBit(&ptCmpakPara->wModuleAlarmStatus, 0, False);
    SetCmpakBit(&ptCmpakPara->wModuleAlarmStatus, 1, ptBCMAlarm->ucBDUBusVoltHighPrt);
    SetCmpakBit(&ptCmpakPara->wModuleAlarmStatus, 2, False);
    SetCmpakBit(&ptCmpakPara->wModuleAlarmStatus, 3, False);
    SetCmpakBit(&ptCmpakPara->wModuleAlarmStatus, 4, ptBCMAlarm->ucDischgCurrHighAlm);
    SetCmpakBit(&ptCmpakPara->wModuleAlarmStatus, 5, ptBCMAlarm->ucChgCurrHighPrt);
    SetCmpakBit(&ptCmpakPara->wModuleAlarmStatus, 6, ptBCMAlarm->ucDischgCurrHighPrt);
    SetCmpakBit(&ptCmpakPara->wModuleAlarmStatus, 7, False);
    SetCmpakBit(&ptCmpakPara->wModuleAlarmStatus, 8, False);
    SetCmpakBit(&ptCmpakPara->wModuleAlarmStatus, 9, ptBCMAlarm->ucBduEepromAlm);
    return True;
}


// 设置系统时间

Static INT32 PacketOrParseCmpakTimeAna(const T_OneDataAttrStruct *ptInDataAttr,  E_DATA_DIRECTION eInDir, BYTE *pucOutData, BYTE *pucInRsvData)
{
    INT32 slRet = APP_FAILURE;
    BYTE *pMemberAddr = NULL;
    
    if(NULL == ptInDataAttr)
    {
        return APP_FAILURE;
    }

    if(CMPAK_TIME_PART != ptInDataAttr->ucOldDataPart || MAX_DATA_TYPES <= ptInDataAttr->ucOldDataType || NULL == s_atDataCovFunc[eInDir][ptInDataAttr->ucOldDataType].Func)
    {
        return APP_FAILURE;
    }
    
    GetTime(&s_tSpecialData.tTime);
    pMemberAddr = (BYTE *)&s_tSpecialData.tTime;
    
    slRet = (*s_atDataCovFunc[eInDir][ptInDataAttr->ucOldDataType].Func)(ptInDataAttr, (void *)(pMemberAddr + ptInDataAttr->wOldDataOffset), pucOutData);

    if(APP_FAILURE != slRet && BYTEBUFF_TO_DATA == eInDir)
    {
        if(NULL != ptInDataAttr->pDealFunc)
        {
            slRet = (*ptInDataAttr->pDealFunc)(ptInDataAttr);
        }
    }

    return slRet;
}



Static BOOLEAN SetBmsName(T_CmpakStruct *ptCmpakPara)
{
    if(NULL == ptCmpakPara){return FAILURE;}

    WORD wSysName[BATTERY_SYS_NAME_LENGTH];

    rt_memset_s(wSysName, sizeof(wSysName), 0xFF, sizeof(wSysName));

    GetSysPara(&s_tSysPara);

    rt_memcpy_s(wSysName, sizeof(wSysName), s_tSysPara.acBMSSysName, (BYTE)strnlen((CHAR*)s_tSysPara.acBMSSysName, 20));
    
    for(int i = 0; i < BATTERY_SYS_NAME_LENGTH; i++) 
    {
        ptCmpakPara->awRepBattModel[i] = WordHost2Modbus(&wSysName[i]);
    }

    return SUCCESSFUL;
}



#endif

