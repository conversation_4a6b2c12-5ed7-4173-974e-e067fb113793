#include "common.h"
#include "utils_heart_beat.h"
#include "thread_id.h"
#include "led.h"
#include "para.h"
#include "comm.h"
#include "ism330dhcx.h"
#include <rtdevice.h>
#include "utils_rtthread_security_func.h"
#include "sensor.h"
#include "wireless.h"
#include "fileSys.h"
#include <stddef.h>
/***********************  变量定义  ************************/

static rt_err_t ism330Connect = RT_EINVAL ; // 保存RT_EOK代表陀螺仪通信正常
struct rt_sensor_data s_tData;
static ism330dhcx_acc_base s_tAccBase;
Static rt_device_t acce_dev = RT_NULL;
Static rt_device_t gyro_dev = RT_NULL;
static rt_device_t step_dev = RT_NULL;
Static rt_mutex_t s_ptMutexAcce = RT_NULL;
Static rt_mutex_t s_ptMutexGyro = RT_NULL;
Static rt_mutex_t s_ptMutexStep = RT_NULL;
static float s_fCalAngle = 0;
static float s_fXAcc_base = 1000.0f;
static float s_fYAcc_base = 0;
static float s_fZAcc_base = 0;
static BOOLEAN s_bCalStatus = False;
Static BOOLEAN s_bIsm300Exist = False;
Static BOOLEAN s_bSm3001Exist = False;
Static BOOLEAN s_bQMI8658Exist = False;
static T_AccSampleStruct s_tAccSample;
/*********************  静态函数原型定义  **********************/
static void InitGyroInfo(void);
static void ResetGyroInfo(void);
static rt_err_t AngleCal(void);
static WORD CalGyroInfoCrc(void);
static BYTE GetAlmCount(void);

BOOLEAN InitGyroDev(void)
{
    rt_uint8_t reg = 0xFF;
    do
    {
        s_ptMutexAcce = rt_mutex_create("acce_lock", RT_IPC_FLAG_PRIO);
        rt_thread_delay(100);
    }while(RT_NULL == s_ptMutexAcce);
    do
    {
        s_ptMutexGyro = rt_mutex_create("gyro_lock", RT_IPC_FLAG_PRIO);
        rt_thread_delay(100);
    }while(RT_NULL == s_ptMutexGyro);
    do
    {
        s_ptMutexStep = rt_mutex_create("step_lock", RT_IPC_FLAG_PRIO);
        rt_thread_delay(100);
    }while(RT_NULL == s_ptMutexStep);

    gyro_dev = rt_device_find("gyro_ism330");
    acce_dev = rt_device_find("acce_sh3001");
    if(RT_NULL != gyro_dev)
    {
        rt_device_open(gyro_dev, RT_DEVICE_FLAG_RDWR);
        step_dev = rt_device_find("step_ism330");
        if (RT_NULL == step_dev)
        {
            return False;
        }
        s_bIsm300Exist = True;
        rt_device_open(step_dev, RT_DEVICE_FLAG_RDWR);
        rt_device_control(step_dev, RT_SENSOR_CTRL_GET_ID, &reg);
    }
    else if(RT_NULL != acce_dev)
    {
        s_bSm3001Exist = True;
        rt_device_open(acce_dev, RT_DEVICE_FLAG_RDWR);
        rt_device_control(acce_dev, RT_SENSOR_CTRL_GET_ID, &reg);
    }
    else
    {
        acce_dev = rt_device_find("acce_qmi8658");
        if(RT_NULL == acce_dev)
        {
            return False;
        }
        else
        {
            s_bQMI8658Exist = True;
            rt_device_open(acce_dev, RT_DEVICE_FLAG_RDWR);
            rt_device_control(acce_dev, RT_SENSOR_CTRL_GET_ID, &reg);
        }
    }

    if(reg == 0xFF)
    {
        ism330Connect = -RT_ERROR;
    }
    else
    {
        ism330Connect = RT_EOK;
    }

    return True;
}

void Proc_G_Sensor(void* parameter)
{
    InitGyroDev();
    InitGyroInfo();
    pre_thread_beat_f(THREAD_GSENSOR);
#ifndef UNITEST
    while (1)
#endif
    {
        rt_thread_delay(60000);
        thread_beat_go_on(THREAD_GSENSOR);
        setShakeAlm(FALSE);
    }
}

static float ism330dhcx_from_fs2g_to_mg(rt_int16_t lsb)
{
    return ((float)lsb * 0.061f);
}

static rt_err_t Ism330_AngleCal(rt_device_t gyro_dev)
{
    s_bCalStatus = True;
    //基准值
    s_fXAcc_base = s_tAccBase.fXAcc_base;
    s_fYAcc_base = s_tAccBase.fYAcc_base;
    s_fZAcc_base = s_tAccBase.fZAcc_base;
    float fXacc, fYacc, fZacc;
    float fVectorPro, fVectorModulesXYZ, fVectorModulesBase;
    if(1 != rt_device_read(gyro_dev,0,&s_tData, 1))
    {
        s_bCalStatus = False;
        return -RT_ERROR;
    }
    //实时值
    fXacc = ism330dhcx_from_fs2g_to_mg(s_tData.data.gyro.x);
    fYacc = ism330dhcx_from_fs2g_to_mg(s_tData.data.gyro.y);
    fZacc = ism330dhcx_from_fs2g_to_mg(s_tData.data.gyro.z);
    fVectorPro = fXacc*s_fXAcc_base + fYacc*s_fYAcc_base + fZacc * s_fZAcc_base;
    fVectorModulesXYZ = sqrt(fXacc*fXacc + fYacc*fYacc+fZacc*fZacc);
    fVectorModulesBase = sqrt(s_fXAcc_base*s_fXAcc_base + s_fYAcc_base*s_fYAcc_base + s_fZAcc_base*s_fZAcc_base);
    s_fCalAngle = acos(fVectorPro/(fVectorModulesXYZ*fVectorModulesBase)) * 180.0 / PI;
    s_bCalStatus = False;
    return RT_EOK;
}

static float sh3001_from_fs2g_to_mg(rt_int16_t lsb)
{
    return ((float)lsb*16*1000.0f/32768);
}

static rt_err_t Sm3001_AngleCal(rt_device_t acce_dev)
{
    s_bCalStatus = True;
    //基准值
    s_fXAcc_base = s_tAccBase.fXAcc_base;
    s_fYAcc_base = s_tAccBase.fYAcc_base;
    s_fZAcc_base = s_tAccBase.fZAcc_base;
    float fXacc, fYacc, fZacc;
    float fVectorPro, fVectorModulesXYZ, fVectorModulesBase;
    if(1 != rt_device_read(acce_dev,0,&s_tData, 1))
    {
        s_bCalStatus = False;
        return -RT_ERROR;
    }
    //实时值
    fXacc = sh3001_from_fs2g_to_mg(s_tData.data.acce.x);
    fYacc = sh3001_from_fs2g_to_mg(s_tData.data.acce.y);
    fZacc = sh3001_from_fs2g_to_mg(s_tData.data.acce.z);
    fVectorPro = fXacc*s_fXAcc_base + fYacc*s_fYAcc_base + fZacc * s_fZAcc_base;
    fVectorModulesXYZ = sqrt(fXacc*fXacc + fYacc*fYacc+fZacc*fZacc);
    fVectorModulesBase = sqrt(s_fXAcc_base*s_fXAcc_base + s_fYAcc_base*s_fYAcc_base + s_fZAcc_base*s_fZAcc_base);
    s_fCalAngle = acos(fVectorPro/(fVectorModulesXYZ*fVectorModulesBase)) * 180.0 / PI;
    s_bCalStatus = False;
    return RT_EOK;
}

static rt_err_t AngleCal(void)
{
    if(s_bCalStatus)
    {
        return RT_EOK;
    }
    else
    {
        if(s_bSm3001Exist || s_bQMI8658Exist)
        {
            return Sm3001_AngleCal(acce_dev);
        }
        else
        {
            return Ism330_AngleCal(gyro_dev);
        }
    }
}

// 查询陀螺仪通信是否正常，返回1表示正常
rt_err_t CheckIsm330Connect(void)
{
    //return ism330Connect;
    return ((s_bIsm300Exist || s_bSm3001Exist || s_bQMI8658Exist) && (ism330Connect == RT_EOK));
}

//倾角应该是基于初始位姿状态计算的
BOOLEAN checkLean(BYTE bGyroscopeSensitivity)
{
    s_tAccSample.ucSensitivity = bGyroscopeSensitivity;

    // 陀螺仪灵敏度设置为禁止时，直接返回
    if (!(s_tAccSample.ucSensitivity))
    {
        s_tAccSample.ucAngleAlm = False;
        if (GetAlmCount() > 0) // 陀螺仪使能禁止后清除采样异常数据
        {
            rt_memset_s(s_tAccSample.ucSampleResult, sizeof(s_tAccSample.ucSampleResult), 0, sizeof(s_tAccSample.ucSampleResult));
        }
        return False;
    }

    // 刷新一次倾角，用于倾角阈值的判断
    if (RT_EOK == AngleCal())
    {
        if (s_fCalAngle * s_tAccSample.ucSensitivity >= ACC_ANGLEALM_THRE) // 倾角超过阈值
        {
            s_tAccSample.ucSampleResult[s_tAccSample.ucIndex] = FAULT;
        }
        else
        {
            s_tAccSample.ucSampleResult[s_tAccSample.ucIndex] = NORMAL;
        }
        s_tAccSample.ucIndex = (s_tAccSample.ucIndex + 1) % ACC_SAMPLE_LENGTH;

        if (GetAlmCount() > ACC_ALMARM_COUNT) // 超过5次倾角异常,认为电池被盗
        {
            s_tAccSample.ucAngleAlm = FAULT;
        }
        else if (GetAlmCount() < ACC_ALMARM_COUNT - 1)
        {
            s_tAccSample.ucAngleAlm = NORMAL;
        }
    }
    return s_tAccSample.ucAngleAlm;
}

/***************************************************************************
 * @brief    save gyro acc base info for position calibration
 * @return   true or false
 **************************************************************************/
BOOLEAN SaveGyroInfo(void)
{
    if(s_bSm3001Exist || s_bQMI8658Exist)
    {
        if(acce_dev != RT_NULL)
        {
            if(1 != rt_device_read(acce_dev, 0, &s_tData, 1))
            {
                return False;
            }
        }
        s_tAccBase.fXAcc_base = sh3001_from_fs2g_to_mg(s_tData.data.acce.x);
        s_tAccBase.fYAcc_base = sh3001_from_fs2g_to_mg(s_tData.data.acce.y);
        s_tAccBase.fZAcc_base = sh3001_from_fs2g_to_mg(s_tData.data.acce.z);
    }
    else
    {
        if(gyro_dev != RT_NULL)
        {
            if(1 != rt_device_read(gyro_dev,0,&s_tData, 1))
            {
                return False;
            }
        }
        s_tAccBase.fXAcc_base = ism330dhcx_from_fs2g_to_mg(s_tData.data.gyro.x);
        s_tAccBase.fYAcc_base = ism330dhcx_from_fs2g_to_mg(s_tData.data.gyro.y);
        s_tAccBase.fZAcc_base = ism330dhcx_from_fs2g_to_mg(s_tData.data.gyro.z);
    }
    s_tAccBase.wCrc = CalGyroInfoCrc();
    writeFile("/gyroinfo", (BYTE *)&s_tAccBase, sizeof(ism330dhcx_acc_base));
    return True;
}

/***************************************************************************
 * @brief    gyro info initialization
 **************************************************************************/
static void InitGyroInfo(void)
{
    rt_int32_t readCount = 0;
    CLEAR_STRUCT(s_tAccBase);
    CLEAR_STRUCT(s_tAccSample);

    readCount = readFile("/gyroinfo", (BYTE *)&s_tAccBase, sizeof(ism330dhcx_acc_base));
    if (readCount != sizeof(ism330dhcx_acc_base) || s_tAccBase.wCrc != CalGyroInfoCrc())
    {
        ResetGyroInfo();
    }
    return;
}

static WORD CalGyroInfoCrc(void)
{
    return CRC_Cal((BYTE *)&s_tAccBase, offsetof(ism330dhcx_acc_base, wCrc));
}
/***************************************************************************
 * @brief    预留接口，重置电池摆放位置基准为默认值(外部需使用时将static去掉)
 **************************************************************************/
static void ResetGyroInfo(void)
{
    s_tAccBase.fXAcc_base = 1000.0f;
    s_tAccBase.fYAcc_base = 0;
    s_tAccBase.fZAcc_base = 0;
    s_tAccBase.wCrc = CalGyroInfoCrc();
    writeFile("/gyroinfo", (BYTE *)&s_tAccBase, sizeof(ism330dhcx_acc_base));
    return;
}

/***************************************************************************
 * @brief    获取10次倾角采样数据当中，异常倾角的个数
 **************************************************************************/
static BYTE GetAlmCount(void)
{
    BYTE ucCount = 0;
    BYTE i;
    for (i = 0; i < ACC_SAMPLE_LENGTH; i++)
    {
        if (s_tAccSample.ucSampleResult[i] == FAULT)
        {
            ucCount++;
        }
    }
    return ucCount;
}

/***************************************************************************
 * @brief   周期获取陀螺仪设备通讯异常状态
 * @return  TRUE:  陀螺仪设备通讯异常 
 *          FALSE: 陀螺仪设备通讯正常或不支持陀螺仪
 **************************************************************************/
BOOLEAN GetGyrCommFailStatus(void)
{
    if(IsHardwareSupportGyro() == False)
    {
        return False;
    }

    if(s_bIsm300Exist)
    {
        if(gyro_dev == RT_NULL || s_ptMutexGyro == RT_NULL)
        {
            return True;
        }
        rt_mutex_take(s_ptMutexGyro, RT_WAITING_FOREVER);
        if(1 != rt_device_read(gyro_dev, 0, &s_tData, 1))
        {
            rt_mutex_release(s_ptMutexGyro);
            return True;
        }
        rt_mutex_release(s_ptMutexGyro);
    }
    else if(s_bSm3001Exist || s_bQMI8658Exist)
    {
        if(acce_dev == RT_NULL || s_ptMutexAcce == RT_NULL)
        {
            return True;
        }
        rt_mutex_take(s_ptMutexAcce, RT_WAITING_FOREVER);
        if(1 != rt_device_read(acce_dev, 0, &s_tData, 1))
        {
            rt_mutex_release(s_ptMutexAcce);
            return True;
        } 
        rt_mutex_release(s_ptMutexAcce);
    }
    
    return False;
}
