#ifndef _COMPRESSOR_PROTECT_LOGIC_H
#define _COMPRESSOR_PROTECT_LOGIC_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "eev_ctrl.h"

#define COMPRESSOR_START_VALUE 1
#define COMPRESSOR_STOP_VALUE  0

int compressor_protect_logic_deal(unsigned short compressor_num, unsigned short compressor_brand);
int compressor_protect_logic_invalid(unsigned short compressor_num, unsigned short compressor_brand);
int compressor_protect_logic_valid(unsigned short compressor_num, unsigned short compressor_brand);
int compressor_protect_logic_delay_close(unsigned short compressor_num, unsigned short compressor_brand, unsigned short comm_status);
int compressor_protect_logic_keep_running(unsigned short compressor_num, unsigned short compressor_brand, char* flag);

int set_protect_alm_bits(unsigned short compressor_num);
int record_compressor_protect_alarm_recover_time(int dev_index, unsigned short vfd_protect_code);
int get_set_vfd_start_stop_cmd_id_by_brand(unsigned short brand, int dev_addr_index, unsigned short vfd_switch);
int is_compressor_protect_valid(int dev_index);
eDO_t get_do_index_by_vfd_no(int vfd_no);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  // 
