#include "time_manage.h"
#include "partition_def.h"
#include "utils_data_transmission.h"
#include "msg_id.h"
#include "para_id_in.h"
#include "device_type.h"
#include "cmd.h"
#include "msg.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "para_id_in.h"
#include "utils_data_valid.h"
#include "utils_time.h"
#include "para_manage.h"
#include "ee_public_info.h"
#include "sps.h"
#include "para_common.h"
#include "utils_rtthread_security_func.h"

static char g_last_dc_ac_state = STAT_COMM_FAIL;
time_stamp_t g_first_time = {0};

static void send_autho_check_msg(void *parameter);

static inline void get_manufacturer_time(time_base_t* manufacturer_time)
{
    date_base_t date_base = {0};

    get_one_data(DAC_DATA_ID_MANUFACTURER_DATE,&date_base);
    manufacturer_time->year = date_base.year;
    manufacturer_time->month = date_base.month;
    manufacturer_time->day = date_base.day;
}

static inline void ctrl_dc_ac_state(unsigned short shutdown, unsigned char license_expire)
{
    set_one_para(DAC_PARA_ID_POWER_ON_OFF_OFFSET, &shutdown, TRUE, TRUE);
    LOG_E("license power on|off(1|0): %d", shutdown);
    send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, DC_AC_SET_REMOTE_CTRL_PARA_DATA);
    set_one_data(DAC_TRACE_ID_LICENSE_EXPIRED, &license_expire);
}


int erase_eeprom(unsigned int offset, size_t len)
{
    unsigned char* buff = (unsigned char*)rt_malloc(len);
    RETURN_VAL_IF_FAIL(buff != NULL, FAILURE);
    rt_memset(buff, 0xFF, len);
    int ret = handle_storage(write_opr, EE_PUBLIC_INFO, buff, len, offset);
    rt_free(buff);
    return ret;
}

int get_first_start_time_stamp(date_base_t *pt_time)
{
    RETURN_VAL_IF_FAIL(pt_time != NULL, FAILURE);
    time_stamp_t tmp_stamp = {0};
    if (SUCCESSFUL != handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char*)&tmp_stamp, sizeof(tmp_stamp), FIRST_START_TIME_OFFSET))
    {
        return FAILURE;
    }

    if((tmp_stamp.valid == TRUE) && (crc_cal((unsigned char*)&tmp_stamp, sizeof(time_stamp_t) - 2)) == tmp_stamp.crc)
    {
        pt_time->year = tmp_stamp.time_stamp.year;
        pt_time->month = tmp_stamp.time_stamp.month;
        pt_time->day = tmp_stamp.time_stamp.day;
        return SUCCESSFUL;
    }

    return FAILURE;
}

int set_first_start_time_stamp(date_base_t time_stamp)
{
    time_stamp_t tmp_stamp = { 0 };

    rt_memcpy_s(&tmp_stamp.time_stamp, sizeof(time_stamp), &time_stamp, sizeof(time_stamp));
    tmp_stamp.valid = TRUE;
    tmp_stamp.crc = crc_cal((unsigned char*)&tmp_stamp, sizeof(tmp_stamp) - 2);

    return handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&tmp_stamp, sizeof(tmp_stamp), FIRST_START_TIME_OFFSET);
}

int erase_first_start_time_stamp(void)
{
    unsigned char buff[sizeof(time_stamp_t)] = {0};
    rt_memset(buff, 0xFF, sizeof(time_stamp_t));
    return handle_storage(write_opr, EE_PUBLIC_INFO, buff, sizeof(buff), FIRST_START_TIME_OFFSET);
}

int set_start_time(void)
{
    static unsigned int count = 0;
    
    if(g_first_time.valid == TRUE)
    {
        // 说明第一次启动时间已经写好了，不需要检测了
        return SUCCESSFUL;
    }

    if(count % FIRST_CHECK_COUNT == 0)
    {
        first_start_time_check();
        count = 0;
    }
    count++;
    return SUCCESSFUL;
}


int is_grid_connect(unsigned short dev_status)
{
    if((dev_status >= 0x0200 && dev_status <= 0x0208) 
       || (dev_status >= 0x0401 && dev_status <= 0x0405) 
       || (dev_status == 0x800))
    {
        return TRUE;
    }
    return FALSE;
}

int first_start_time_check(void)
{
    float act_power = 0;
    unsigned short dev_status = 0;
    int grid_status = 0;

    get_one_data(DAC_DATA_ID_ACTIVE_POWER, &act_power);
    get_one_data(DAC_DATA_ID_DEV_STATUS, &dev_status);
    grid_status = is_grid_connect(dev_status);

    if((act_power > RATE_THRESH) && (grid_status == TRUE))
    {
        get_date(&g_first_time.time_stamp);

        g_first_time.valid = TRUE;
        g_first_time.crc = crc_cal((unsigned char*)&g_first_time, sizeof(g_first_time) - 2);
        set_one_data(DAC_DATA_ID_FIRST_STARTUP_TIME, &g_first_time.time_stamp);
        LOG_E("%s:%d| first_time: %ld\n",__FUNCTION__ , __LINE__, g_first_time.time_stamp);
        return handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&g_first_time, sizeof(g_first_time), FIRST_START_TIME_OFFSET);
    }
    return SUCCESSFUL;
}

int init_first_start_time(void)
{   
    if (SUCCESSFUL != handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char*)&g_first_time, sizeof(g_first_time), FIRST_START_TIME_OFFSET))
    {
        return FAILURE;
    }

    if((g_first_time.valid == TRUE) && (crc_cal((unsigned char*)&g_first_time, sizeof(time_stamp_t) - 2)) == g_first_time.crc)
    {
        set_one_data(DAC_DATA_ID_FIRST_STARTUP_TIME, &g_first_time.time_stamp);
    }

    return SUCCESSFUL;
}


int get_exfactory_time_stamp(date_base_t *pt_time)
{
    RETURN_VAL_IF_FAIL(pt_time != NULL, FAILURE);
    time_stamp_t tmp_stamp = {0};
    if (SUCCESSFUL != handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char*)&tmp_stamp, sizeof(tmp_stamp), EXFACTORY_TIME_OFFSET))
    {   
        return FAILURE;
    }

    if((tmp_stamp.valid == TRUE) && (crc_cal((unsigned char*)&tmp_stamp, sizeof(time_stamp_t) - 2)) == tmp_stamp.crc)
    {
        pt_time->year = tmp_stamp.time_stamp.year;
        pt_time->month = tmp_stamp.time_stamp.month;
        pt_time->day = tmp_stamp.time_stamp.day;
        return SUCCESSFUL;
    }

    return FAILURE;
}

int set_exfactory_time_stamp(date_base_t time_stamp)
{   
    time_stamp_t tmp_stamp = { 0 };

    rt_memcpy_s(&tmp_stamp.time_stamp, sizeof(time_stamp), &time_stamp, sizeof(time_stamp));
    tmp_stamp.valid = TRUE;
    tmp_stamp.crc = crc_cal((unsigned char*)&tmp_stamp, sizeof(tmp_stamp) - 2);

    if (SUCCESSFUL != handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&tmp_stamp, sizeof(tmp_stamp), EXFACTORY_TIME_OFFSET))
    {
        return FAILURE;
    }
    set_one_data(DAC_DATA_ID_MONITER_DELIERY_TIME, &time_stamp);
    return SUCCESSFUL;
}

int erase_exfactory_time_stamp(void)
{   
    unsigned char buff[sizeof(time_stamp_t)] = {0};
    rt_memset(buff, 0xFF, sizeof(time_stamp_t));
    return handle_storage(write_opr, EE_PUBLIC_INFO, buff, sizeof(buff), EXFACTORY_TIME_OFFSET);
}

int init_autho_manage(void)
{   
    char* timer_name = "autho_manage";

    rt_timer_t his_data_timer = rt_timer_create(timer_name, send_autho_check_msg, NULL, AUTHO_CHECK_PERIOD, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
    if(his_data_timer == NULL)
    {
        return FAILURE;
    }
    rt_timer_start(his_data_timer);
    return SUCCESSFUL;
}

static void send_autho_check_msg(void *parameter)
{
    send_msg_to_thread(AUTHO_TIME_CHECK_MSG, MOD_SYS_MANAGE, NULL, 0);
}

int process_user_auth_time(void)
{
    unsigned char is_expira = RT_FALSE;
    unsigned short user_permantly_valid = 0;
    unsigned int user_expira_time = 0;
    time_t cur_time = 0;
    unsigned short user_remain_day = 9999;

    get_one_para(DAC_PARA_ID_USER_ACCOUNT_PERMANENTLY_VALID_OFFSET, &user_permantly_valid);
    if (user_permantly_valid == RT_TRUE)
    {
        // 有权限
        set_one_data(DAC_TRACE_ID_USER_ACCOUNT_EXPIRED, &is_expira);
        set_one_data(DAC_DATA_ID_USER_ACCOUNT_REMAIN_VALID_TIME, &user_remain_day);
        return SUCCESSFUL;
    }

    get_one_para(DAC_PARA_ID_USER_ACCOUNT_EXPIRATION_DATE_OFFSET, &user_expira_time);

    cur_time = get_timestamp_s();
    if (cur_time >= user_expira_time)
    {
        // user账号超期
        is_expira = RT_TRUE;
        user_remain_day = 0;
        set_one_data(DAC_DATA_ID_USER_ACCOUNT_REMAIN_VALID_TIME, &user_remain_day);
        set_one_data(DAC_TRACE_ID_USER_ACCOUNT_EXPIRED, &is_expira);
        return FAILURE;
    }

    set_one_data(DAC_TRACE_ID_USER_ACCOUNT_EXPIRED, &is_expira);
    user_remain_day = (user_expira_time - cur_time + 86399) / 86400;    // 天数向上取整
    set_one_data(DAC_DATA_ID_USER_ACCOUNT_REMAIN_VALID_TIME, &user_remain_day);

    return SUCCESSFUL;
}

void autho_time_check(dev_inst_t* dev_dc_ac)
{   
    char dc_ac_state = STAT_COMM_FAIL;
    time_base_t manufacturer_time = {0};
    int diff_days = 0;
    short autho_months = 0;
    int license_expire = 0, license_expire_in = 0;
    power_off_reason_t* power_off_reason = NULL;

    dc_ac_state = dev_dc_ac->state;
    process_user_auth_time();
    if((STAT_NORMAL == dc_ac_state) && (STAT_COMM_FAIL == g_last_dc_ac_state))
    {
        // 发送一次消息，获取生产日期
        send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, DC_AC_GET_PRO_PARA_DATA);
        rt_thread_mdelay(1000);  // 等待1s
    }
    g_last_dc_ac_state = dc_ac_state;

    get_manufacturer_time(&manufacturer_time);
    if(check_time_valid(&manufacturer_time) == FALSE)
    {
        send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, DC_AC_GET_PRO_PARA_DATA);
        rt_thread_mdelay(1000);  // 等待1s

        get_manufacturer_time(&manufacturer_time);               
        if(check_time_valid(&manufacturer_time) == FALSE)
        {
            return;
        }
    }

    power_off_reason = get_power_off_reason();
    diff_days = get_diff_days(&manufacturer_time);
    get_one_para(DAC_PARA_ID_AUTH_USE_TIME_OFFSET, &autho_months);

    if(diff_days > autho_months * MONTH_DAYS)
    {
        // 关机，产生告警
        ctrl_dc_ac_state(FALSE, TRUE);
        license_expire_in = 0;
        set_one_data(DAC_TRACE_ID_LICENSE_EXPIRED_IN_ONE_WEEK, &license_expire_in);
        power_off_reason->time_off = TRUE;
        return;
    }
    else if((diff_days > autho_months * MONTH_DAYS - 7) && (diff_days <= autho_months * MONTH_DAYS))
    {
        license_expire_in = 1;
    }
    else
    {
        license_expire_in = 0;
    }

    // 开机，消除告警
    license_expire = 0;
    set_one_data(DAC_TRACE_ID_LICENSE_EXPIRED_IN_ONE_WEEK, &license_expire_in);
    set_one_data(DAC_TRACE_ID_LICENSE_EXPIRED, &license_expire);
    // 不是紧急关机，并且北向没有设置关机，此时才开机
    if((power_off_reason->emergy_off != TRUE) && (power_off_reason->normal_off != TRUE))
    {
        // 授权时间开机，只有当不是紧急关机或者不是参数设置关机的时候才会开机
        ctrl_dc_ac_state(TRUE, FALSE);
    }
    power_off_reason->time_off = FALSE;

    return;   
}
