/**
 * @file     device_type.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2023-01-04
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */

 
#ifndef _SAMPLE_H
#define _SAMPLE_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include <stdio.h>
#include <rtthread.h>
#include <rtdevice.h>
#include <string.h>
#include "data_type.h"
#include "dev_dc_ac.h"
#include "utils_server.h"

#define DATA_INVALID        0
#define DATA_VALID          1

#define DEV_INVALID        0
#define DEV_VALID          1

#define SAVE_GRID_TIME_PERIOD   (1000 * 60)  // 1min更新一次

#define FAULT_DEV_STATUS        0x0300
#define NO_LIGHT                0xA000    // 无光照
#define LIGHT_DETECT            0x0002    // 光照检测

typedef struct {
    rt_uint32_t msg_id;
    void (*handle)(_rt_msg_t curr_msg);
} pv_sample_msg_handle_process_t;

void *init_sample(void * param);
void sample_main(void * param);
int clean_grid_time();
int check_dev_status();
void handle_clean_his_energy_msg(_rt_msg_t curr_msg);
int grid_time_init();
void send_msg_to_sps(unsigned int msg_id, unsigned char dest);
int init_grid_time_accu_timer();
void grid_time_calcu();
void write_grid_time_period();
void handle_delete_his_energy_msg(_rt_msg_t curr_msg);
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _SAMPLE_H
