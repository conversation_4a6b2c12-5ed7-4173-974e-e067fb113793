/*
 * @file    : dc_ac_zte_can15.c
 * @brief   : DC/AC模块与逆变监控通讯南向
 * @details : 
 * <AUTHOR> 付振10303717
 * @Date    : 2022-12-26 
 * @LastEditTime: 2023-02-13
 * @version : V0.0.1
 * @para    : Copyright (c) 
 *            ZTE Corporation
 * @par     : History
 *          2023-01-06, create file
 *     version: author, date, descn
 */

#include "dev_dc_ac.h"
#include "protocol_layer.h"
#include "device_num.h"
#include "cmd.h"
#include "sps.h"
#include "msg.h"
#include "msg_id.h"
#include "softbus.h"
#include "unified_id_interface.h"

/* 命令数据存储变量 */

/* 命令请求 */
static bottom_comm_cmd_head_t cmd_req[] RAM_SECTION = {
    /* 通信协议命令请求 */
    {BOTTOM_PROTO_TYPE_COMM, CMD_NONE,                CMD_APPEND_NONE,                             BOTTOM_RTN_APP_NOACK}, //0
    {BOTTOM_PROTO_TYPE_COMM, CMD_DC_AC_GET_REAL_DATA, CMD_APPEND_NONE,                             BOTTOM_RTN_APP_ACK},   //1
    {BOTTOM_PROTO_TYPE_COMM, CMD_DC_AC_GET_DATA,      CMD_APPEND_NONE,                             BOTTOM_RTN_APP_ACK},   //2
    {BOTTOM_PROTO_TYPE_COMM, CMD_DC_AC_SET_DATA,      CMD_APPEND_NONE,                             BOTTOM_RTN_APP_ACK},   //3
    {BOTTOM_PROTO_TYPE_COMM, CMD_DC_AC_GET_VER_DATA,  CMD_APPEND_NONE,                             BOTTOM_RTN_APP_ACK},   //4

    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATA_AFT_TRIG_FRAME,   CMD_APPEND_UPDATE_REQ,                       BOTTOM_RTN_APP_NOACK},   //5
    {BOTTOM_PROTO_TYPE_DOWN, CMD_TRAN_AFT_DATA_FRAME,    CMD_APPEND_NONE,                             BOTTOM_RTN_APP_NOACK},   //6
    {BOTTOM_PROTO_TYPE_DOWN, CMD_TRAN_AFT_DATA_FRAME,     CMD_APPEND_NONE,                             BOTTOM_RTN_APP_NOACK},   //7
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_PST_BAK_TRIG,        CMD_APPEND_UPDATE_REQ,                       BOTTOM_RTN_APP_NOACK},   //8

    {BOTTOM_PROTO_TYPE_COMM, CMD_DC_AC_CONTROL_ORDER, CMD_APPEND_NONE,                             BOTTOM_RTN_APP_ACK},     //9
    {BOTTOM_PROTO_TYPE_COMM, CMD_DC_AC_GET_IV_DATA,   CMD_APPEND_NONE,                             BOTTOM_RTN_APP_ACK},     //10

    {BOTTOM_PROTO_TYPE_COMM, CMD_DC_AC_FAULT_RECORD_FIRST, CMD_APPEND_NONE,                        BOTTOM_RTN_APP_NOACK},   //11
    {BOTTOM_PROTO_TYPE_COMM, CMD_DC_AC_FAULT_RECORD_DATA, CMD_APPEND_NONE,                         BOTTOM_RTN_APP_NOACK},   //12

    {BOTTOM_PROTO_TYPE_COMM, CMD_DC_AC_GET_UPDATE_STATUS_DATA,  CMD_APPEND_NONE,                   BOTTOM_RTN_APP_ACK},     //13
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPLOAD_TRIG_FRAME,      CMD_APPEND_UPDATE_REQ,                    BOTTOM_RTN_APP_NOACK},   //14
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPLOAD_LIST_FRAME,      CMD_APPEND_NONE,                          BOTTOM_RTN_APP_NOACK},   //15
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPLOAD_FIRST_FRAME,     CMD_APPEND_NONE,                          BOTTOM_RTN_APP_NOACK},   //16
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPLOAD_DATA_FRAME,      CMD_APPEND_NONE,                          BOTTOM_RTN_APP_NOACK},   //17
};

/* 命令应答   */
static bottom_comm_cmd_head_t cmd_ack[] RAM_SECTION = {
    /* 通信协议命令应答   */
    {BOTTOM_PROTO_TYPE_COMM, CMD_NONE,                CMD_APPEND_NONE,                             BOTTOM_RTN_APP_CORRECT},  //0
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA,       CMD_APPEND_NONE,                             BOTTOM_RTN_APP_CORRECT},  //1
    {BOTTOM_PROTO_TYPE_COMM, CMD_DC_AC_GET_DATA,      CMD_APPEND_NONE,                             BOTTOM_RTN_APP_CORRECT},  //2
    {BOTTOM_PROTO_TYPE_COMM, CMD_DC_AC_SET_DATA,      CMD_APPEND_NONE,                             BOTTOM_RTN_APP_CORRECT},  //3
    {BOTTOM_PROTO_TYPE_COMM, CMD_DC_AC_GET_VER_DATA,  CMD_APPEND_NONE,                             BOTTOM_RTN_APP_CORRECT},  //4

    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATA_AFT_TRIG_FRAME,   CMD_APPEND_UPDATE_ACK,                       BOTTOM_RTN_APP_CORRECT},  //5
    {BOTTOM_PROTO_TYPE_DOWN, CMD_TRAN_AFT_DATA_FRAME,    CMD_APPEND_NONE,                             BOTTOM_RTN_APP_CORRECT},  //6
    {BOTTOM_PROTO_TYPE_DOWN, CMD_TRAN_AFT_DATA_FRAME,     CMD_APPEND_NONE,                             BOTTOM_RTN_APP_CORRECT},  //7
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_PST_BAK_TRIG,        CMD_APPEND_UPDATE_ACK,                       BOTTOM_RTN_APP_CORRECT},  //8
    {BOTTOM_PROTO_TYPE_COMM, CMD_DC_AC_CONTROL_ORDER, CMD_APPEND_NONE,                             BOTTOM_RTN_APP_CORRECT},  //9
    {BOTTOM_PROTO_TYPE_COMM, CMD_DC_AC_GET_IV_DATA,   CMD_APPEND_NONE,                             BOTTOM_RTN_APP_CORRECT},  //10

    {BOTTOM_PROTO_TYPE_COMM, CMD_DC_AC_FAULT_RECORD_FIRST, CMD_APPEND_NONE,                        BOTTOM_RTN_APP_ACK},   //11
    {BOTTOM_PROTO_TYPE_COMM, CMD_DC_AC_FAULT_RECORD_DATA,  CMD_APPEND_NONE,                        BOTTOM_RTN_APP_ACK},   //12

    {BOTTOM_PROTO_TYPE_COMM, CMD_DC_AC_GET_UPDATE_STATUS_DATA,  CMD_APPEND_NONE,                   BOTTOM_RTN_APP_CORRECT},   //13
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPLOAD_TRIG_FRAME,      CMD_APPEND_UPDATE_ACK,                    BOTTOM_RTN_APP_CORRECT},   //14
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPLOAD_LIST_FRAME,      CMD_APPEND_NONE,                          BOTTOM_RTN_APP_CORRECT},   //15
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPLOAD_FIRST_FRAME,     CMD_APPEND_NONE,                          BOTTOM_RTN_APP_CORRECT},   //16
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPLOAD_DATA_FRAME,      CMD_APPEND_NONE,                          BOTTOM_RTN_APP_CORRECT},   //17
};

/* 命令字段信息 */

static data_info_id_verison_t cmd_parse_alarm_info[] RAM_SECTION = 
{
    /* 告警信息查询 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, ALARM_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, ALARM_PARA_LEN, NOT_NEED_INTERACT}, 

    // 告警量1
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_BUS_UNDER_VOLT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PV_SLOW_UP_ERR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_DC_DISCONNECTOR_TRIP},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_BUS_SOFT_START_ERR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_IN_HARDWARE_FAULT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_VOLT + 3},     
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_VOLT + 2},  
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_VOLT + 1},  
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_VOLT}, 

    // 告警量2
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_REVERSE_CONNECT + 7}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_REVERSE_CONNECT + 6}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_REVERSE_CONNECT + 5}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_REVERSE_CONNECT + 4}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_REVERSE_CONNECT + 3}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_REVERSE_CONNECT + 2}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_REVERSE_CONNECT + 1}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_REVERSE_CONNECT},

    // 告警量3
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_CURR_WARN + 3},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_CURR_WARN + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_CURR_WARN + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_CURR_WARN},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 

    // 告警量4
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_CURR_ERROR + 3},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_CURR_ERROR + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_CURR_ERROR + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_CURR_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 

    // 告警量5
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_CURR_REVERSE_INJECT + 7},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_CURR_REVERSE_INJECT + 6},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_CURR_REVERSE_INJECT + 5},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_CURR_REVERSE_INJECT + 4},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_CURR_REVERSE_INJECT + 3},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_CURR_REVERSE_INJECT + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_CURR_REVERSE_INJECT + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_CURR_REVERSE_INJECT},

    // 告警量6
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 

    // 告警量7
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_LEAK_CURR_SELFCHECK_FAILURE_FAULT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_OUTPUT_WIRING_MISMATCH},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_ABN_RESIDUAL_CURR_LEAP},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_ABN_RESIDUAL_CURR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_IMPEDANCE_WARN},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_IMPEDANCE_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_ABNORMAL_OPER_BUILD_IN_PID},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_BUS_VOLT_IMBALANCE},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_NEG_BUS_OVER_VOLT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_POS_BUS_OVER_VOLT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_BUS_OVER_VOLT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_AFCI_SELF_CHK_FAIL},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_DC_ARC_DAULT},

    // 告警量8
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_WATER_INGRESS},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_LOW_POWER_SHUTDOWN},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_LOW_VOLT_RT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_HIGH_VOLT_RT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PV_OVER_CURR_ERROR + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PV_OVER_CURR_ERROR + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PV_OVER_CURR_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PV_OVER_CURR_WARN + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PV_OVER_CURR_WARN + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PV_OVER_CURR_WARN},

    // 告警量9
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_OUT_CURR_IMBALANCE},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PHASE_VOLT_TRANS_OVER_VOLT + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PHASE_VOLT_TRANS_OVER_VOLT + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PHASE_VOLT_TRANS_OVER_VOLT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PHASE_VOLT_OVER_VOLT + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PHASE_VOLT_OVER_VOLT + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PHASE_VOLT_OVER_VOLT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PHASE_VOLT_UNDER_VOLT + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PHASE_VOLT_UNDER_VOLT + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PHASE_VOLT_UNDER_VOLT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_UNDER_VOLT_BETWEEN_PHASE + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_UNDER_VOLT_BETWEEN_PHASE + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_UNDER_VOLT_BETWEEN_PHASE},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_OVER_VOLT_BETWEEN_PHASE + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_OVER_VOLT_BETWEEN_PHASE + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_OVER_VOLT_BETWEEN_PHASE},

    // 告警量10
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_GRID_ANTI_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_POWER_GRID_OUTAGE},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_ISLAND_PROT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_ABNORMAL_DC_COMPONENT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_OUTPUT_OVER_CIRCUIT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_OUTPUT_SHORT_CIRCUIT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_UNBALANCE_POWER_GRID},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_TEN_MINUTE_GRID_OVER_VOLT_PROT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SINGLE_PHASE_GND_SHORT_CIRCUIT + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SINGLE_PHASE_GND_SHORT_CIRCUIT + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SINGLE_PHASE_GND_SHORT_CIRCUIT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_ABNORMAL_GROUND},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_AC_PHASE_LOSS},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_GRID_FREQ_INSTABLE},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_GRID_UNDER_FREQ},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_GRID_OVER_FREQ},

    // 告警量11
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_LOSS + 7},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_LOSS + 6},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_LOSS + 5},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_LOSS + 4},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_LOSS + 3},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_LOSS + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_LOSS + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_LOSS},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PV_LOCK_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_AC_CURR_SENSOR_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_GRID_RELAY_ERROR},

    // 告警量12
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_LOW_TEMPERATURE_SHUTDOWN_PROTECTION},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_NTC_LOW_TEMPERATURE_WARNING + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_NTC_LOW_TEMPERATURE_WARNING + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_NTC_LOW_TEMPERATURE_WARNING},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_INSIDE_LOW_TEMPERATURE_WARNING},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PCB_LOW_TEMPERATURE_WARNING},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_RADIATOR_OVER_TEMP + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_RADIATOR_OVER_TEMP + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_RADIATOR_OVER_TEMP},

    // 告警量13
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_DC_LIGHTING_PROTECTION_ERR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_AC_LIGHTING_PROTECTION_ERR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_ABN_AUXI_POWER_SUPPLT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_INTER_FAN_FAIL},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_EXTER_FAN_FAIL},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_FIRE_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_LOW_TEMP_INSIDS_MACHINE},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_OVER_TEMP_INSIDS_MACHINE},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PCB_OVER_TEMP},

    // 告警量14
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_CARRIER_SYNC_ABNORMAL},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PROTOCOL_VER_MISMATCH},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MAIN_AUXI_COMMU_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MAIN_CPLD_COMMU_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_AUXI_CTRL_EEPROM_ABNORMAL},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_AUXI_CTRL_EEPROM_FAULT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MAIN_CTRL_EEPROM_ABNORMAL},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MAIN_CTRL_EEPROM_FAULT},

    // 告警量15
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SERIES_DC_ARC_FAULT+7},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SERIES_DC_ARC_FAULT+6},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SERIES_DC_ARC_FAULT+5},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SERIES_DC_ARC_FAULT+4},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SERIES_DC_ARC_FAULT+3},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SERIES_DC_ARC_FAULT+2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SERIES_DC_ARC_FAULT+1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SERIES_DC_ARC_FAULT},
};
/* 获取实时数据打包命令 */
static data_info_id_verison_t cmd_real_data_pack_info[] RAM_SECTION = {
    /* MPPT模拟信息 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ANA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ANA_ID + 1, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ANA_ID + 2, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ANA_ID + 3, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_PARA_LEN, NOT_NEED_INTERACT},       
    /* 组串模拟信息 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, SERIES_ANA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, SERIES_PARA_LEN, NOT_NEED_INTERACT},         
    /* 输出信息 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OUTPUT_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OUTPUT_PARA_LEN, NOT_NEED_INTERACT}, 
    /* 逆变器信息 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, INVERTER_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, INVERTER_PARA_LEN, NOT_NEED_INTERACT},         
    /* 辅助电源模拟信息查询 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, AUX_POWER_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, AUX_POWER_PARA_LEN, NOT_NEED_INTERACT},
    /* 辅控模拟信息查询 */ 
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, AUX_ANA_ID, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, AUX_ANA_LEN, NOT_NEED_INTERACT},
    /* 调测模式模拟信息查询 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DEBUG_MODE_ANA_ID, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DEBUG_MODE_ANA_LEN, NOT_NEED_INTERACT},
    /* 状态信息查询 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DIG_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DIG_PARA_LEN, NOT_NEED_INTERACT},  
    /* 告警信息查询 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, ALARM_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, ALARM_PARA_LEN, NOT_NEED_INTERACT},
    /* 调测用状态信息查询 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DUBUG_INFO_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DUBUG_INFO_LEN, NOT_NEED_INTERACT},
};

/* 获取实时数据解包命令 */
static data_info_id_verison_t cmd_real_data_parse_info[] RAM_SECTION = {
    /* MPPT模拟信息 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ANA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_TOTAL_INPUT_POWER},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_VOLT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_CURR},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVERSAMPLE_CURRENT},

    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ANA_ID + 1, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_TOTAL_INPUT_POWER + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_VOLT + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_CURR + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVERSAMPLE_CURRENT+1},

    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ANA_ID + 2, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_TOTAL_INPUT_POWER + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_VOLT + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_CURR + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVERSAMPLE_CURRENT+2},

    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ANA_ID + 3, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_TOTAL_INPUT_POWER + 3},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_VOLT + 3},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_CURR + 3},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVERSAMPLE_CURRENT+3},

    /* 组串模拟信息 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, SERIES_ANA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, SERIES_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_VOLT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_VOLT + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_VOLT + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_VOLT + 3},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR + 3},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_VOLT + 4},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR + 4},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_VOLT + 5},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR + 5},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_VOLT + 6},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR + 6},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_VOLT + 7},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR + 7},

    /* 输出信息 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OUTPUT_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OUTPUT_PARA_LEN, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,                   DAC_DATA_ID_ACTIVE_POWER},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,                   DAC_DATA_ID_REACTIVE_POWER},
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,          DAC_DATA_ID_APPARENT_POWER},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,                 DAC_DATA_ID_POWER_FACTOR},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,        DAC_DATA_ID_GRID_FREQ},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,        DAC_DATA_ID_GRID_AB_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,        DAC_DATA_ID_GRID_BC_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,        DAC_DATA_ID_GRID_CA_VOLT},

    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,        DAC_DATA_ID_GRID_PHASE_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,        DAC_DATA_ID_GRID_PHASE_VOLT + 1},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,        DAC_DATA_ID_GRID_PHASE_VOLT + 2},

    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,                 DAC_DATA_ID_GRID_PHASE_CURR},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,                 DAC_DATA_ID_GRID_PHASE_CURR + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,                 DAC_DATA_ID_GRID_PHASE_CURR + 2},

    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,        DAC_DATA_ID_PV_AB_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,        DAC_DATA_ID_PV_BC_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,        DAC_DATA_ID_PV_CA_VOLT},
    
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,        DAC_DATA_ID_PV_PHASE_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,        DAC_DATA_ID_PV_PHASE_VOLT + 1},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,        DAC_DATA_ID_PV_PHASE_VOLT + 2},

    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,        DAC_DATA_ID_RES_CURR_AC_COMPONENT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,        DAC_DATA_ID_RES_CURR_DC_COMPONENT},

    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_DC_COMPONENT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_DC_COMPONENT + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_DC_COMPONENT + 2},

    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_AVERAGE_VALUE},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_AVERAGE_VALUE + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_AVERAGE_VALUE + 2},
    /* 逆变器信息 */
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, INVERTER_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, INVERTER_PARA_LEN, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_TOTAL_INPUT_POWER},
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ENVIR_TEMPER},
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_INTERNAL_TEMPER},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_POS_INSULATION_IMPERD},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_EFFICIENCY},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_BUS_VOLT},
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_POS_BUS_VOLT},
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_NEG_BUS_VOLT},
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_REDIATOR_TEMPER},
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_REDIATOR_TEMPER + 1},
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_REDIATOR_TEMPER + 2},
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_REDIATOR_TEMPER + 3},
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_REDIATOR_TEMPER + 4},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_NET_NUMBER},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_NUMBER},
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_RATED_NUMBER},
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MAX_ACTIVE_POWER},
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MAX_APPARENT_POWER},
    {SEG_DATA, type_int,            ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MAX_REACTIVE_POWER},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_SHORT_CIRCUIT_POSITION_WITH_LOW_INSULAT_IMPEDANCE},
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PCB_TEMPER},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_FAULT_STRING_WITH_LOW_INSULAT_IMPEDANCE},

    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AFCI_LOG_SAVE_NUM},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AFCI_LOG_SAVED_NUM},


    /* 辅助电源模拟信息查询 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, AUX_POWER_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, AUX_POWER_PARA_LEN, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_VERF_SAMPLE},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_VERF_SAMPLE + 1},  
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_VERF_SAMPLE + 2},  
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_VCC_5V_CTL},  
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_VCC_5V_CTL + 1},  
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_VERF_SAMPLE + 3},  
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_PROTECTION_THRESHOLD},  
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_INV_PROTECTION_THRESHOLD},  
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_DC_13V_CTL},  
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AC_13V_CTL},  
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_DC_LIGHTNING_PROTECTION_ERR},  
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AC_LIGHTNING_PROTECTION_ERR},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_SWITCH_RELEASE_FB},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_V_PE_IR},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_V_PE_IR + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_V_PE_IR + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_CHECK_PID},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_RELAY_SUPPLLY_VOLT},

    /* 辅控模拟信息查询 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, AUX_ANA_ID, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, AUX_ANA_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_NET_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_NET_VOLT+1},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_NET_VOLT+2},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_NET_PHASE_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_NET_PHASE_VOLT+1},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_NET_PHASE_VOLT+2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_NET_PHASE_CURR},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_NET_PHASE_CURR+1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_NET_PHASE_CURR+2},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_INVERTER_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_INVERTER_VOLT+1},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_INVERTER_VOLT+2},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_INVERTER_PHASE_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_INVERTER_PHASE_VOLT+1},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_INVERTER_PHASE_VOLT+2},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_BUSBAR_VOL},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_POS_BUSBAR_VOL},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_NEG_BUSBAR_VOL},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_PE_VOL},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},

    /* 调测模式模拟信息查询 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DEBUG_MODE_ANA_ID, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DEBUG_MODE_ANA_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ARC_DETECTION_VOLTAGE},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ARC_DETECTION_VOLTAGE+1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ARC_DETECTION_VOLTAGE+2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ARC_DETECTION_VOLTAGE+3},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_INTERNAL_FAN_SPEED},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_EXTERNAL_FAN_SPEED},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_FT_N_PE},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_FT_TEST_PRESTAGE_FLAG},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_FT_TEST_POSTSTAGE_FLAG},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AFCI_SELF_CHECK_STAGE},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_FT_TEST_RESERVED_ANA+4},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_FT_TEST_RESERVED_ANA+5},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_FT_TEST_RESERVED_ANA+6},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_FT_TEST_RESERVED_ANA+7},
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_FLASH_ERASE_NUM},

    /* 状态信息查询 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DIG_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DIG_PARA_LEN, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_DEV_STATUS},
  
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_CURRENT_LIMITING_STATUS + 3},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_CURRENT_LIMITING_STATUS + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_CURRENT_LIMITING_STATUS + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_CURRENT_LIMITING_STATUS},

    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_DC_INPUT_LOAD_SHEDDING},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MODULATION_INDEX_LOAD_SHEDDING},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_RADIATOR_TEMPERATURE_LOAD_SHEDDING},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_INTERNAL_TEMPERATURE_LOAD_SHEDDING},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PCB_TEMPERATURE_LOAD_SHEDDING},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_GRID_VOLTAGE_LOAD_SHEDDING},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ALTITUDE_LOAD_SHEDDING},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_INPUT_UNDERVOLT_STA},

    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},   
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_SCAN_STATUS + 7},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_SCAN_STATUS + 6},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_SCAN_STATUS + 5},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_SCAN_STATUS + 4},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_SCAN_STATUS + 3},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_SCAN_STATUS + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_SCAN_STATUS + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_SCAN_STATUS },

    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},   
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CONNECTED_STATUS + 7},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CONNECTED_STATUS + 6},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CONNECTED_STATUS + 5},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CONNECTED_STATUS + 4},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CONNECTED_STATUS + 3},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CONNECTED_STATUS + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CONNECTED_STATUS + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CONNECTED_STATUS },

    /* 告警信息查询 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, ALARM_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, ALARM_PARA_LEN, NOT_NEED_INTERACT}, 

    // 告警量1
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_BUS_UNDER_VOLT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PV_SLOW_UP_ERR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_DC_DISCONNECTOR_TRIP},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_BUS_SOFT_START_ERR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_IN_HARDWARE_FAULT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_VOLT + 3},     
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_VOLT + 2},  
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_VOLT + 1},  
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_VOLT}, 

    // 告警量2
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_REVERSE_CONNECT + 7}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_REVERSE_CONNECT + 6}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_REVERSE_CONNECT + 5}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_REVERSE_CONNECT + 4}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_REVERSE_CONNECT + 3}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_REVERSE_CONNECT + 2}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_REVERSE_CONNECT + 1}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_REVERSE_CONNECT},

    // 告警量3
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_CURR_WARN + 3},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_CURR_WARN + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_CURR_WARN + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_CURR_WARN},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 

    // 告警量4
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_CURR_ERROR + 3},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_CURR_ERROR + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_CURR_ERROR + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_CURR_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 

    // 告警量5
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_CURR_REVERSE_INJECT + 7},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_CURR_REVERSE_INJECT + 6},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_CURR_REVERSE_INJECT + 5},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_CURR_REVERSE_INJECT + 4},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_CURR_REVERSE_INJECT + 3},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_CURR_REVERSE_INJECT + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_CURR_REVERSE_INJECT + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_CURR_REVERSE_INJECT},

    // 告警量6
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 

    // 告警量7
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_LEAK_CURR_SELFCHECK_FAILURE_FAULT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_OUTPUT_WIRING_MISMATCH},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_ABN_RESIDUAL_CURR_LEAP},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_ABN_RESIDUAL_CURR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_IMPEDANCE_WARN},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_IMPEDANCE_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_ABNORMAL_OPER_BUILD_IN_PID},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_BUS_VOLT_IMBALANCE},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_NEG_BUS_OVER_VOLT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_POS_BUS_OVER_VOLT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_BUS_OVER_VOLT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_AFCI_SELF_CHK_FAIL},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_DC_ARC_DAULT},

    // 告警量8
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_WATER_INGRESS},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_LOW_POWER_SHUTDOWN},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_LOW_VOLT_RT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_HIGH_VOLT_RT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PV_OVER_CURR_ERROR + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PV_OVER_CURR_ERROR + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PV_OVER_CURR_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PV_OVER_CURR_WARN + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PV_OVER_CURR_WARN + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PV_OVER_CURR_WARN},

    // 告警量9
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_OUT_CURR_IMBALANCE},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PHASE_VOLT_TRANS_OVER_VOLT + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PHASE_VOLT_TRANS_OVER_VOLT + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PHASE_VOLT_TRANS_OVER_VOLT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PHASE_VOLT_OVER_VOLT + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PHASE_VOLT_OVER_VOLT + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PHASE_VOLT_OVER_VOLT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PHASE_VOLT_UNDER_VOLT + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PHASE_VOLT_UNDER_VOLT + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PHASE_VOLT_UNDER_VOLT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_UNDER_VOLT_BETWEEN_PHASE + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_UNDER_VOLT_BETWEEN_PHASE + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_UNDER_VOLT_BETWEEN_PHASE},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_OVER_VOLT_BETWEEN_PHASE + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_OVER_VOLT_BETWEEN_PHASE + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_OVER_VOLT_BETWEEN_PHASE},

    // 告警量10
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_GRID_ANTI_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_POWER_GRID_OUTAGE},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_ISLAND_PROT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_ABNORMAL_DC_COMPONENT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_OUTPUT_OVER_CIRCUIT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_OUTPUT_SHORT_CIRCUIT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_UNBALANCE_POWER_GRID},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_TEN_MINUTE_GRID_OVER_VOLT_PROT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SINGLE_PHASE_GND_SHORT_CIRCUIT + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SINGLE_PHASE_GND_SHORT_CIRCUIT + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SINGLE_PHASE_GND_SHORT_CIRCUIT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_ABNORMAL_GROUND},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_AC_PHASE_LOSS},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_GRID_FREQ_INSTABLE},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_GRID_UNDER_FREQ},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_GRID_OVER_FREQ},

    // 告警量11
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_LOSS + 7},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_LOSS + 6},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_LOSS + 5},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_LOSS + 4},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_LOSS + 3},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_LOSS + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_LOSS + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_LOSS},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PV_LOCK_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_AC_CURR_SENSOR_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_GRID_RELAY_ERROR},

    // 告警量12
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_LOW_TEMPERATURE_SHUTDOWN_PROTECTION},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_NTC_LOW_TEMPERATURE_WARNING + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_NTC_LOW_TEMPERATURE_WARNING + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_NTC_LOW_TEMPERATURE_WARNING},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_INSIDE_LOW_TEMPERATURE_WARNING},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PCB_LOW_TEMPERATURE_WARNING},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_RADIATOR_OVER_TEMP + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_RADIATOR_OVER_TEMP + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_RADIATOR_OVER_TEMP},

    // 告警量13
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_DC_LIGHTING_PROTECTION_ERR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_AC_LIGHTING_PROTECTION_ERR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_ABN_AUXI_POWER_SUPPLT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_INTER_FAN_FAIL},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_EXTER_FAN_FAIL},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_FIRE_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_LOW_TEMP_INSIDS_MACHINE},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_OVER_TEMP_INSIDS_MACHINE},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PCB_OVER_TEMP},

    // 告警量14
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_CARRIER_SYNC_ABNORMAL},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PROTOCOL_VER_MISMATCH},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MAIN_AUXI_COMMU_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MAIN_CPLD_COMMU_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_AUXI_CTRL_EEPROM_ABNORMAL},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_AUXI_CTRL_EEPROM_FAULT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MAIN_CTRL_EEPROM_ABNORMAL},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MAIN_CTRL_EEPROM_FAULT},

    // 告警量15
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SERIES_DC_ARC_FAULT+7},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SERIES_DC_ARC_FAULT+6},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SERIES_DC_ARC_FAULT+5},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SERIES_DC_ARC_FAULT+4},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SERIES_DC_ARC_FAULT+3},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SERIES_DC_ARC_FAULT+2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SERIES_DC_ARC_FAULT+1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SERIES_DC_ARC_FAULT},

    /* 调测用状态信息查询 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DUBUG_INFO_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DUBUG_INFO_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short,ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_CURRENT_WORK_MODE},
};

/* 分段获取实时数据打包命令 */
static data_info_id_verison_t cmd_fast_real_data_pack_info[] RAM_SECTION = {  
    /* 组串模拟信息 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, SERIES_ANA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, SERIES_PARA_LEN, NOT_NEED_INTERACT},         
    /* 输出信息 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OUTPUT_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OUTPUT_PARA_LEN, NOT_NEED_INTERACT}, 
    /* 逆变器信息 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, INVERTER_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, INVERTER_PARA_LEN, NOT_NEED_INTERACT},           
    /* 状态信息查询 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DIG_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DIG_PARA_LEN, NOT_NEED_INTERACT},  
    /* 告警信息查询 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, ALARM_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, ALARM_PARA_LEN, NOT_NEED_INTERACT},   
};
static data_info_id_verison_t cmd_slow_real_data_pack_info[] RAM_SECTION = {
    /* MPPT模拟信息 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ANA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ANA_ID + 1, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ANA_ID + 2, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ANA_ID + 3, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_PARA_LEN, NOT_NEED_INTERACT},            
    /* 辅助电源模拟信息查询 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, AUX_POWER_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, AUX_POWER_PARA_LEN, NOT_NEED_INTERACT},     
    /* 辅控模拟信息查询 */ 
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, AUX_ANA_ID, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, AUX_ANA_LEN, NOT_NEED_INTERACT},
    /* 调测模式模拟信息查询 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DEBUG_MODE_ANA_ID, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DEBUG_MODE_ANA_LEN, NOT_NEED_INTERACT},
    /* 调测用状态信息查询 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DUBUG_INFO_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DUBUG_INFO_LEN, NOT_NEED_INTERACT},
};

/* 分段获取实时数据解包命令 */
static data_info_id_verison_t cmd_fast_real_data_parse_info[] RAM_SECTION = {
    /* 组串模拟信息 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, SERIES_ANA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, SERIES_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_VOLT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_VOLT + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_VOLT + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_VOLT + 3},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR + 3},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_VOLT + 4},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR + 4},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_VOLT + 5},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR + 5},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_VOLT + 6},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR + 6},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_VOLT + 7},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR + 7},

    /* 输出信息 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OUTPUT_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OUTPUT_PARA_LEN, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,                   DAC_DATA_ID_ACTIVE_POWER},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,                   DAC_DATA_ID_REACTIVE_POWER},
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,          DAC_DATA_ID_APPARENT_POWER},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,                 DAC_DATA_ID_POWER_FACTOR},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,        DAC_DATA_ID_GRID_FREQ},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,        DAC_DATA_ID_GRID_AB_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,        DAC_DATA_ID_GRID_BC_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,        DAC_DATA_ID_GRID_CA_VOLT},

    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,        DAC_DATA_ID_GRID_PHASE_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,        DAC_DATA_ID_GRID_PHASE_VOLT + 1},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,        DAC_DATA_ID_GRID_PHASE_VOLT + 2},

    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,                 DAC_DATA_ID_GRID_PHASE_CURR},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,                 DAC_DATA_ID_GRID_PHASE_CURR + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,                 DAC_DATA_ID_GRID_PHASE_CURR + 2},

    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,        DAC_DATA_ID_PV_AB_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,        DAC_DATA_ID_PV_BC_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,        DAC_DATA_ID_PV_CA_VOLT},
    
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,        DAC_DATA_ID_PV_PHASE_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,        DAC_DATA_ID_PV_PHASE_VOLT + 1},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,        DAC_DATA_ID_PV_PHASE_VOLT + 2},
    
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,        DAC_DATA_ID_RES_CURR_AC_COMPONENT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,        DAC_DATA_ID_RES_CURR_DC_COMPONENT},

    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_DC_COMPONENT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_DC_COMPONENT + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_DC_COMPONENT + 2},

    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_AVERAGE_VALUE},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_AVERAGE_VALUE + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_AVERAGE_VALUE + 2},
    /* 逆变器信息 */
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, INVERTER_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, INVERTER_PARA_LEN, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_TOTAL_INPUT_POWER},
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ENVIR_TEMPER},
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_INTERNAL_TEMPER},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_POS_INSULATION_IMPERD},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_EFFICIENCY},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_BUS_VOLT},
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_POS_BUS_VOLT},
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_NEG_BUS_VOLT},
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_REDIATOR_TEMPER},
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_REDIATOR_TEMPER + 1},
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_REDIATOR_TEMPER + 2},
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_REDIATOR_TEMPER + 3},
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_REDIATOR_TEMPER + 4},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_NET_NUMBER},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_NUMBER},
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_RATED_NUMBER},
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MAX_ACTIVE_POWER},
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MAX_APPARENT_POWER},
    {SEG_DATA, type_int,            ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MAX_REACTIVE_POWER},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_SHORT_CIRCUIT_POSITION_WITH_LOW_INSULAT_IMPEDANCE},
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PCB_TEMPER},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_FAULT_STRING_WITH_LOW_INSULAT_IMPEDANCE},

    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AFCI_LOG_SAVE_NUM},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AFCI_LOG_SAVED_NUM},

    /* 状态信息查询 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DIG_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DIG_PARA_LEN, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_DEV_STATUS},
  
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_INSULATION_IMPEDANCE_LOW_POSITION_STATE}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PID_REPAIR_STATUS}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_CURRENT_LIMITING_STATUS + 3},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_CURRENT_LIMITING_STATUS + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_CURRENT_LIMITING_STATUS + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_CURRENT_LIMITING_STATUS},

    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_DC_INPUT_LOAD_SHEDDING},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MODULATION_INDEX_LOAD_SHEDDING},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_RADIATOR_TEMPERATURE_LOAD_SHEDDING},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_INTERNAL_TEMPERATURE_LOAD_SHEDDING},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PCB_TEMPERATURE_LOAD_SHEDDING},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_GRID_VOLTAGE_LOAD_SHEDDING},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ALTITUDE_LOAD_SHEDDING},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_INPUT_UNDERVOLT_STA},

    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},   
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_SCAN_STATUS + 7},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_SCAN_STATUS + 6},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_SCAN_STATUS + 5},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_SCAN_STATUS + 4},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_SCAN_STATUS + 3},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_SCAN_STATUS + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_SCAN_STATUS + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_SCAN_STATUS },

    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},   
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CONNECTED_STATUS + 7},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CONNECTED_STATUS + 6},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CONNECTED_STATUS + 5},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CONNECTED_STATUS + 4},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CONNECTED_STATUS + 3},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CONNECTED_STATUS + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CONNECTED_STATUS + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CONNECTED_STATUS },

    /* 告警信息查询 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, ALARM_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, ALARM_PARA_LEN, NOT_NEED_INTERACT},  

    // 告警量1
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_BUS_UNDER_VOLT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PV_SLOW_UP_ERR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_DC_DISCONNECTOR_TRIP},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_BUS_SOFT_START_ERR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_IN_HARDWARE_FAULT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_VOLT + 3},     
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_VOLT + 2},  
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_VOLT + 1},  
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_VOLT}, 

    // 告警量2
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_REVERSE_CONNECT + 7}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_REVERSE_CONNECT + 6}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_REVERSE_CONNECT + 5}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_REVERSE_CONNECT + 4}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_REVERSE_CONNECT + 3}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_REVERSE_CONNECT + 2}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_REVERSE_CONNECT + 1}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_REVERSE_CONNECT},

    // 告警量3
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_CURR_WARN + 3},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_CURR_WARN + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_CURR_WARN + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_CURR_WARN},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 

    // 告警量4
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_CURR_ERROR + 3},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_CURR_ERROR + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_CURR_ERROR + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MPPT_OVER_CURR_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 

    // 告警量5
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_CURR_REVERSE_INJECT + 7},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_CURR_REVERSE_INJECT + 6},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_CURR_REVERSE_INJECT + 5},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_CURR_REVERSE_INJECT + 4},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_CURR_REVERSE_INJECT + 3},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_CURR_REVERSE_INJECT + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_CURR_REVERSE_INJECT + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_CURR_REVERSE_INJECT},

    // 告警量6
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
 
    // 告警量7
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_LEAK_CURR_SELFCHECK_FAILURE_FAULT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_OUTPUT_WIRING_MISMATCH},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_ABN_RESIDUAL_CURR_LEAP},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_ABN_RESIDUAL_CURR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_IMPEDANCE_WARN},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_IMPEDANCE_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_ABNORMAL_OPER_BUILD_IN_PID},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_BUS_VOLT_IMBALANCE},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_NEG_BUS_OVER_VOLT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_POS_BUS_OVER_VOLT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_BUS_OVER_VOLT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_AFCI_SELF_CHK_FAIL},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_DC_ARC_DAULT},

    // 告警量8
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_WATER_INGRESS},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_LOW_POWER_SHUTDOWN},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_LOW_VOLT_RT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_HIGH_VOLT_RT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PV_OVER_CURR_ERROR + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PV_OVER_CURR_ERROR + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PV_OVER_CURR_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PV_OVER_CURR_WARN + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PV_OVER_CURR_WARN + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PV_OVER_CURR_WARN},

    // 告警量9
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_OUT_CURR_IMBALANCE},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PHASE_VOLT_TRANS_OVER_VOLT + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PHASE_VOLT_TRANS_OVER_VOLT + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PHASE_VOLT_TRANS_OVER_VOLT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PHASE_VOLT_OVER_VOLT + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PHASE_VOLT_OVER_VOLT + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PHASE_VOLT_OVER_VOLT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PHASE_VOLT_UNDER_VOLT + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PHASE_VOLT_UNDER_VOLT + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PHASE_VOLT_UNDER_VOLT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_UNDER_VOLT_BETWEEN_PHASE + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_UNDER_VOLT_BETWEEN_PHASE + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_UNDER_VOLT_BETWEEN_PHASE},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_OVER_VOLT_BETWEEN_PHASE + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_OVER_VOLT_BETWEEN_PHASE + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_OVER_VOLT_BETWEEN_PHASE},

    // 告警量10
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_GRID_ANTI_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_POWER_GRID_OUTAGE},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_ISLAND_PROT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_ABNORMAL_DC_COMPONENT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_OUTPUT_OVER_CIRCUIT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_OUTPUT_SHORT_CIRCUIT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_UNBALANCE_POWER_GRID},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_TEN_MINUTE_GRID_OVER_VOLT_PROT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SINGLE_PHASE_GND_SHORT_CIRCUIT + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SINGLE_PHASE_GND_SHORT_CIRCUIT + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SINGLE_PHASE_GND_SHORT_CIRCUIT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_ABNORMAL_GROUND},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_AC_PHASE_LOSS},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_GRID_FREQ_INSTABLE},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_GRID_UNDER_FREQ},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_GRID_OVER_FREQ},

    // 告警量11
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_LOSS + 7},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_LOSS + 6},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_LOSS + 5},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_LOSS + 4},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_LOSS + 3},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_LOSS + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_LOSS + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_STR_LOSS},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PV_LOCK_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_AC_CURR_SENSOR_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_GRID_RELAY_ERROR},

    // 告警量12
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_LOW_TEMPERATURE_SHUTDOWN_PROTECTION},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_NTC_LOW_TEMPERATURE_WARNING + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_NTC_LOW_TEMPERATURE_WARNING + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_NTC_LOW_TEMPERATURE_WARNING},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_INSIDE_LOW_TEMPERATURE_WARNING},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PCB_LOW_TEMPERATURE_WARNING},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_RADIATOR_OVER_TEMP + 2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_RADIATOR_OVER_TEMP + 1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_RADIATOR_OVER_TEMP},

    // 告警量13
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_DC_LIGHTING_PROTECTION_ERR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_AC_LIGHTING_PROTECTION_ERR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_ABN_AUXI_POWER_SUPPLT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_INTER_FAN_FAIL},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_EXTER_FAN_FAIL},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_FIRE_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_LOW_TEMP_INSIDS_MACHINE},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_OVER_TEMP_INSIDS_MACHINE},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PCB_OVER_TEMP},

    // 告警量14
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_CARRIER_SYNC_ABNORMAL},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_PROTOCOL_VER_MISMATCH},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MAIN_AUXI_COMMU_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MAIN_CPLD_COMMU_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_AUXI_CTRL_EEPROM_ABNORMAL},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_AUXI_CTRL_EEPROM_FAULT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MAIN_CTRL_EEPROM_ABNORMAL},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_MAIN_CTRL_EEPROM_FAULT},

    // 告警量15
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SERIES_DC_ARC_FAULT+7},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SERIES_DC_ARC_FAULT+6},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SERIES_DC_ARC_FAULT+5},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SERIES_DC_ARC_FAULT+4},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SERIES_DC_ARC_FAULT+3},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SERIES_DC_ARC_FAULT+2},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SERIES_DC_ARC_FAULT+1},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_TRACE_ID_SERIES_DC_ARC_FAULT},
};
static data_info_id_verison_t cmd_slow_real_data_parse_info[] RAM_SECTION = {
    /* MPPT模拟信息 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ANA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_TOTAL_INPUT_POWER},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_VOLT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_CURR},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVERSAMPLE_CURRENT},

    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ANA_ID + 1, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_TOTAL_INPUT_POWER + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_VOLT + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_CURR + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVERSAMPLE_CURRENT+1},

    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ANA_ID + 2, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_TOTAL_INPUT_POWER + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_VOLT + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_CURR + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVERSAMPLE_CURRENT+2},

    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ANA_ID + 3, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_TOTAL_INPUT_POWER + 3},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_VOLT + 3},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_CURR + 3},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVERSAMPLE_CURRENT+3},

    /* 辅助电源模拟信息查询 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, AUX_POWER_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, AUX_POWER_PARA_LEN, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_VERF_SAMPLE},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_VERF_SAMPLE + 1},  
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_VERF_SAMPLE + 2},  
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_VCC_5V_CTL},  
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_VCC_5V_CTL + 1},  
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_VERF_SAMPLE + 3},  
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_PROTECTION_THRESHOLD},  
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_INV_PROTECTION_THRESHOLD},  
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_DC_13V_CTL},  
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AC_13V_CTL},  
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_DC_LIGHTNING_PROTECTION_ERR},  
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AC_LIGHTNING_PROTECTION_ERR},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_SWITCH_RELEASE_FB},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_V_PE_IR},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_V_PE_IR + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_V_PE_IR + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_CHECK_PID},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_RELAY_SUPPLLY_VOLT},

    /* 辅控模拟信息查询 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, AUX_ANA_ID, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, AUX_ANA_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_NET_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_NET_VOLT+1},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_NET_VOLT+2},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_NET_PHASE_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_NET_PHASE_VOLT+1},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_NET_PHASE_VOLT+2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_NET_PHASE_CURR},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_NET_PHASE_CURR+1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_NET_PHASE_CURR+2},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_INVERTER_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_INVERTER_VOLT+1},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_INVERTER_VOLT+2},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_INVERTER_PHASE_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_INVERTER_PHASE_VOLT+1},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_INVERTER_PHASE_VOLT+2},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_BUSBAR_VOL},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_POS_BUSBAR_VOL},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_NEG_BUSBAR_VOL},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_PE_VOL},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},

    /* 调测模式模拟信息查询 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DEBUG_MODE_ANA_ID, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DEBUG_MODE_ANA_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ARC_DETECTION_VOLTAGE},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ARC_DETECTION_VOLTAGE+1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ARC_DETECTION_VOLTAGE+2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ARC_DETECTION_VOLTAGE+3},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_INTERNAL_FAN_SPEED},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_EXTERNAL_FAN_SPEED},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_FT_N_PE},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_FT_TEST_PRESTAGE_FLAG},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_FT_TEST_POSTSTAGE_FLAG},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AFCI_SELF_CHECK_STAGE},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_FT_TEST_RESERVED_ANA+4},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_FT_TEST_RESERVED_ANA+5},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_FT_TEST_RESERVED_ANA+6},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_FT_TEST_RESERVED_ANA+7},
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_FLASH_ERASE_NUM},

    /* 调测用状态信息查询 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DUBUG_INFO_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DUBUG_INFO_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short,ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_CURRENT_WORK_MODE},

};

/* 获取版本信息打包命令 */
static data_info_id_verison_t cmd_ver_data_pack_info[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, VER_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, VER_PARA_LEN, NOT_NEED_INTERACT},    
};

/* 获取版本信息解包命令 */
static data_info_id_verison_t cmd_ver_data_parse_info[] RAM_SECTION = {
    /* 版本信息 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, VER_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, VER_PARA_LEN, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_string, DC_AC_SOFT_VER_LEN, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_CTRL_SOFTWARE_VER},
    {SEG_DATA, type_char, ARRAY_SIZE_8, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_MASTER_CTRL_SOFTWARE_VER_DATE},
    {SEG_DATA, type_string, DC_AC_SOFT_VER_LEN, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CTRL_SOFTWARE_VER},
    {SEG_DATA, type_char, ARRAY_SIZE_8, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CTRL_SOFTWARE_VER_DATE},
    {SEG_DATA, type_string, DC_AC_SOFT_VER_LEN, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_CPLD_SOFTWARE_VER},
    {SEG_DATA, type_char, ARRAY_SIZE_8, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_CPLD_SOFTWARE_VER_DATE},
    {SEG_DATA, type_char, ARRAY_SIZE_8, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_MASTER_CTRL_PROTOCOL_VERSION},
    {SEG_DATA, type_char, ARRAY_SIZE_8, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_SLAVE_CTRL_PROTOCOL_VERSION},
    {SEG_DATA, type_char, ARRAY_SIZE_8, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_CPLD_PROTOCOL_VERSION},

};

/* 获取参数打包命令 */
static data_info_id_verison_t cmd_get_para_data_pack_info[] RAM_SECTION = {
    /* 遥控开关机 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, REMOTE_CONTROL_SWITCH_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, REMOTE_CONTROL_SWITCH_PARA_LEN, NOT_NEED_INTERACT},  

    /* 电网参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, POWER_GRID_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, POWER_GRID_PARA_LEN, NOT_NEED_INTERACT},  

    /* 特性参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, CHARACTERISTIC_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, CHARACTERISTIC_PARA_LEN, NOT_NEED_INTERACT},  

    /* MPPT参数设置 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_PARA_PARA_LEN, NOT_NEED_INTERACT},  

    /* LVRT参数设置 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, LVRT_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, LVRT_PARA_LEN, NOT_NEED_INTERACT},  

    /* LVRT特征曲线 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, LVRT_CURVE_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, LVRT_CURVE_PARA_LEN, NOT_NEED_INTERACT},  

    /* HVRT参数设置 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, HVRT_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, HVRT_PARA_LEN, NOT_NEED_INTERACT},  

    /* HVRT特征曲线 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, HVRT_CURVE_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, HVRT_CURVE_PARA_LEN, NOT_NEED_INTERACT},  

    /* PID参数设置 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PID_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PID_PARA_LEN, NOT_NEED_INTERACT},  

    /* AFCI参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, AFCI_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, AFCI_PARA_LEN, NOT_NEED_INTERACT},  

    /* 功率调节参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, POWER_REGULATION_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, POWER_REGULATION_PARA_LEN, NOT_NEED_INTERACT}, 

    /* 有功功率调节参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, ACTIVE_POWER_REGULATION_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, ACTIVE_POWER_REGULATION_PARA_LEN, NOT_NEED_INTERACT},  

    /* 无功功率调节 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, REACTIVE_POWER_REGULATION_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, REACTIVE_POWER_REGULATION_PARA_LEN, NOT_NEED_INTERACT},  

    /* cosφ-P/Pn特性曲线 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, P_PN_CURVE_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, P_PN_CURVE_PARA_LEN, NOT_NEED_INTERACT},  

    /* Q-U特性曲线设置 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, Q_U_CURVE_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, Q_U_CURVE_PARA_LEN, NOT_NEED_INTERACT},  

    /* PF-U特性曲线设置 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PF_U_CURVE_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PF_U_CURVE_PARA_LEN, NOT_NEED_INTERACT},  

    /* 保护参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTION_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTION_PARA_LEN, NOT_NEED_INTERACT}, 

    /* 一级保护参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL1_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL_PARA_LEN, NOT_NEED_INTERACT}, 
    /* 二级保护参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL2_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL_PARA_LEN, NOT_NEED_INTERACT}, 
    // /* 三级保护参数 */
    // {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL3_PARA_ID, NOT_NEED_INTERACT},  
    // {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    // {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL_PARA_LEN, NOT_NEED_INTERACT},
    // /* 四级保护参数 */
    // {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL4_PARA_ID, NOT_NEED_INTERACT},  
    // {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    // {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL_PARA_LEN, NOT_NEED_INTERACT},
    // /* 五级保护参数 */
    // {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL5_PARA_ID, NOT_NEED_INTERACT},  
    // {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    // {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL_PARA_LEN, NOT_NEED_INTERACT},
    // /* 六级保护参数 */
    // {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL6_PARA_ID, NOT_NEED_INTERACT},  
    // {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    // {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL_PARA_LEN, NOT_NEED_INTERACT},
    /* 其他参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OTHER_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OTHER_PARA_LEN, NOT_NEED_INTERACT},

    /* 组串接入检测 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, STRING_IN_DETECTION, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, STRING_IN_DETECTION_LEN, NOT_NEED_INTERACT},

    /* 过欠频降额 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OVER_UNDER_FREQ_DERATING, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OVER_UNDER_FREQ_DERATING_LEN, NOT_NEED_INTERACT},

    /* 驱动测试相关参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DRIVE_TEST_PARA_ID, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DRIVE_TEST_PARA_LEN, NOT_NEED_INTERACT},

    /* ADC采样测试相关参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, ADC_SAMPLE_TEST_PARA_ID, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, ADC_SAMPLE_TEST_PARA_LEN, NOT_NEED_INTERACT},

    /* 研发测试相关参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, RD_TEST_RELATED_ID, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, RD_TEST_RELATED_LEN, NOT_NEED_INTERACT},

    /* 生产测试相关参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PRODUCTION_TEST_PARA_ID, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PRODUCTION_TEST_PARA_LEN, NOT_NEED_INTERACT},
};

/* 获取参数解包命令 */
static data_info_id_verison_t cmd_get_para_data_parse_info[] RAM_SECTION = {
    /* 遥控开关机 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, REMOTE_CONTROL_SWITCH_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, REMOTE_CONTROL_SWITCH_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_POWER_ON_OFF},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PHASE_SEQ_ADAPTIVE_ENABLE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_CONNECT_TYPE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_EN_LEAK_CURR_SELF_CHECK},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_RCD_BOOST_ENABLE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_WEAK_GRID_ENABLE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_WATER_INGRESS_ENABLE},


    /* 电网参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, POWER_GRID_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, POWER_GRID_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_GRID_STANDARD_CODE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_GRID_ISOLATION_SETTING},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OUTPUT_MODE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ATUO_START_AFTER_GRID_FAULT_RECOVER},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_CONNECT_TIME_AFTER_GRID_FAULT_RECOVER},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_DELAY_START_TIME_AFTER_GRID_FAULT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UPPER_LIMIT_GRID_RECONN_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_LOWER_LIMIT_GRID_RECONN_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UPPER_LIMIT_GRID_RECONN_FREQ},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_LOWER_LIMIT_GRID_RECONN_FREQ},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PQ_MODE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_LOWER_LIMIT_GRID_CONN_START_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UPPER_LIMIT_GRID_CONN_START_FREQ},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_LOWER_LIMIT_GRID_CONN_START_FREQ},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_RATED_GRID_VOLT},




    /* 特性参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, CHARACTERISTIC_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, CHARACTERISTIC_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_STANDBY_TIME},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_BOOT_SOFT_START_TIME},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ISLAND_DETECT_ENABLE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ENABLE_GRID_VOLT_PROT_SHEILD_DURING_VRT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_VRT_EXIT_HYSTERESIS_THRESHLOD},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_GRID_FAULT_ZERO_CURR_MODE},


    /* MPPT参数设置 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_PARA_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_ENABLE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_SCAN_INTERVAL},

    /* LVRT参数设置 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, LVRT_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, LVRT_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_LVRT_ENABLE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_LVRT_TRIGG_THRESHOLD},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_LVRT_POS_SEQ_REACTIVE_POWER_COMPEN_FACTOR},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_LVRT_NEG_SEQ_REACTIVE_POWER_COMPEN_FACTOR},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_LVRT_REACTIVE_CURR_LIMIT_PERCENT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_LVRT_ZERO_CURR_MODE_THRESHOLD},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_LVRT_MODE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_LVRT_ACTIVE_CURR_MAINTEN_COEFFICIENT},

    /* LVRT特征曲线 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, LVRT_CURVE_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, LVRT_CURVE_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_CHARACTER_CURVE_POINT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_CHARACTER_CURVE_TIME},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_VOLT_PERCENT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_CHARACTER_CURVE_TIME+1},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_VOLT_PERCENT+1},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_CHARACTER_CURVE_TIME+2},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_VOLT_PERCENT+2},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_CHARACTER_CURVE_TIME+3},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_VOLT_PERCENT+3},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_CHARACTER_CURVE_TIME+4},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_VOLT_PERCENT+4},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_CHARACTER_CURVE_TIME+5},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_VOLT_PERCENT+5},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_CHARACTER_CURVE_TIME+6},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_VOLT_PERCENT+6},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_CHARACTER_CURVE_TIME+7},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_VOLT_PERCENT+7},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_CHARACTER_CURVE_TIME+8},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_VOLT_PERCENT+8},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_CHARACTER_CURVE_TIME+9},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_VOLT_PERCENT+9},




    /* HVRT参数设置 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, HVRT_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, HVRT_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_HVRT_ENABLE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_HVRT_TRIGG_THRESHOLD},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_HVRT_POS_SEQ_REACTIVE_POWER_COMPEN_FACTOR},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_HVRT_NEG_SEQ_REACTIVE_POWER_COMPEN_FACTOR},

    /* HVRT特征曲线 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, HVRT_CURVE_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, HVRT_CURVE_PARA_LEN, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_HVRT_CHARACTER_CURVE_POINT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_HVRT_CHARACTER_CURVE_TIME},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_HVRT_CHARACTER_CURVE_VOLT_PER},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_HVRT_CHARACTER_CURVE_TIME+1},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_HVRT_CHARACTER_CURVE_VOLT_PER+1},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_HVRT_CHARACTER_CURVE_TIME+2},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_HVRT_CHARACTER_CURVE_VOLT_PER+2},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_HVRT_CHARACTER_CURVE_TIME+3},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_HVRT_CHARACTER_CURVE_VOLT_PER+3},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_HVRT_CHARACTER_CURVE_TIME+4},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_HVRT_CHARACTER_CURVE_VOLT_PER+4},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_HVRT_CHARACTER_CURVE_TIME+5},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_HVRT_CHARACTER_CURVE_VOLT_PER+5},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_HVRT_CHARACTER_CURVE_TIME+6},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_HVRT_CHARACTER_CURVE_VOLT_PER+6},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_HVRT_CHARACTER_CURVE_TIME+7},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_HVRT_CHARACTER_CURVE_VOLT_PER+7},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_HVRT_CHARACTER_CURVE_TIME+8},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_HVRT_CHARACTER_CURVE_VOLT_PER+8},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_HVRT_CHARACTER_CURVE_TIME+9},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_HVRT_CHARACTER_CURVE_VOLT_PER+9},

    /* PID参数设置 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PID_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PID_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PID_REPAIR_ENABLE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_NIGHT_PID_PROT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_NIGHT_SLEEP_ENABLE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_NIGHT_SLEEP_WAIT_TIME},



    /* AFCI参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, AFCI_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, AFCI_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AFCI_DETECT_ON_OFF},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AFCI_CHECK_DETECTION_SENSITIVITY},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AFCI_SAVE_ENABLE},


    /* 功率调节参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, POWER_REGULATION_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, POWER_REGULATION_PARA_LEN, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_REMOTE_POWER_DISPATCH_ENABLE_DISABLE},
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_SHCED_INSTRUCT_MAINTEN_TIME},
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ACTIVE_POWER_REFERENCE},
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_APPARNET_POWER_REFERENCE},
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MAX_APPARENT_POWER_PARAM},
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MAX_ACTIVE_POWER_PARAM},
    // {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_POWER_LIMIT_ZERO_PERCENT_SHUTDOWN},
    // {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_NIGHT_REACTIVE_ENABLE},
    // {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_NIGTH_REACTIVE_POWER_COMPEN},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_SHUTDOWN_GRADIENT},


    /* 有功功率调节参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, ACTIVE_POWER_REGULATION_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, ACTIVE_POWER_REGULATION_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_GRID_ACTIVE_POWER_CHANGE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ACTIVE_POWER_CONTROL_MODE},
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ACTIVE_POWER_DERATING_SETTING},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ACTIVE_POWER_DERATING_PERCENT},


    /* 无功功率调节 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, REACTIVE_POWER_REGULATION_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, REACTIVE_POWER_REGULATION_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_GRID_REACTIVE_POWER_CHANGE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_REACTIVE_POWER_COMPEN_MODE},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_REACTIVE_POWER_COMPEN_SETTING_FACTOR},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_REACTIVE_POWER_COMPEN_SETTING},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_REACTIVE_POWER_REGULATIONG_TIME},

    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_REACTIVE_POWER_VALUE},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_REACTIVE_POWER_PCT},


    /* cosφ-P/Pn特性曲线 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, P_PN_CURVE_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, P_PN_CURVE_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_QP_MODE_TRIGG_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_QP_MODE_EXIT_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_SETTING_POINT_OF_COS_CURVE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_P_PN_AXIS},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_COS_AXIS},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_P_PN_AXIS+1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_COS_AXIS+1},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_P_PN_AXIS+2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_COS_AXIS+2},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_P_PN_AXIS+3},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_COS_AXIS+3},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_P_PN_AXIS+4},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_COS_AXIS+4},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_P_PN_AXIS+5},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_COS_AXIS+5},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_P_PN_AXIS+6},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_COS_AXIS+6},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_P_PN_AXIS+7},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_COS_AXIS+7},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_P_PN_AXIS+8},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_COS_AXIS+8},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_P_PN_AXIS+9},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_COS_AXIS+9},



    /* Q-U特性曲线设置 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, Q_U_CURVE_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, Q_U_CURVE_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_QU_SCHEDU_TRIGG_POWER_PERCENT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_QU_SCHEDU_EXIT_POWER_PERCENT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_QU_CHARACTER_CURVE_MIN_PF_LIMIT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_HYSTERESIS_RATIO},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_NUMBER_OF_POINT_FOR_QU_CURVE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_U_UN_AXIS},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_QS_AXIS},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_U_UN_AXIS+1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_QS_AXIS+1},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_U_UN_AXIS+2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_QS_AXIS+2},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_U_UN_AXIS+3},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_QS_AXIS+3},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_U_UN_AXIS+4},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_QS_AXIS+4},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_U_UN_AXIS+5},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_QS_AXIS+5},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_U_UN_AXIS+6},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_QS_AXIS+6},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_U_UN_AXIS+7},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_QS_AXIS+7},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_U_UN_AXIS+8},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_QS_AXIS+8},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_U_UN_AXIS+9},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_QS_AXIS+9},


    /* PF-U特性曲线设置 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PF_U_CURVE_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PF_U_CURVE_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PF_VOLT_DETECT_FILTER_TIME},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_NUMBER_OF_POINT_FOR_PF_CURVE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PF_U_AXIS},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_PF_AXIS},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PF_U_AXIS+1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PF_AXIS+1},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PF_U_AXIS+2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PF_AXIS+2},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PF_U_AXIS+3},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PF_AXIS+3},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PF_U_AXIS+4},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PF_AXIS+4},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PF_U_AXIS+5},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PF_AXIS+5},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PF_U_AXIS+6},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PF_AXIS+6},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PF_U_AXIS+7},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PF_AXIS+7},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PF_U_AXIS+8},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PF_AXIS+8},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PF_U_AXIS+9},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PF_AXIS+9},


    /* 保护参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTION_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTION_PARA_LEN, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_GRID_VOLT_IMBALANCE_PROT_POINT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_INSULATION_IMPEDANCE_PROT},
    // {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_TEN_MINUTE_GRID_OVER_PROT_POINT},
    // {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_TEN_MINUTE_GRID_OVER_PROT_TIME},


    /* 一级保护参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL1_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL_PARA_LEN, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_VOLT_PROT_POINT_GRID},
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_VOLT_PROT_TIME_GRID},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UNDER_VOLT_PROT_POINT_GRID},
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UNDER_VOLT_PROT_TIME_GRID},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_FREQ_PROT_POINT},
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_FREQ_PROT_TIME},    
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UNDER_FREQ_PROT_POINT},
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UNDER_FREQ_PROT_TIME},    
    
    
    /* 二级保护参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL2_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_VOLT_PROT_POINT_GRID+1},
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_VOLT_PROT_TIME_GRID+1},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UNDER_VOLT_PROT_POINT_GRID+1},
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UNDER_VOLT_PROT_TIME_GRID+1},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_FREQ_PROT_POINT+1},
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_FREQ_PROT_TIME+1},    
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UNDER_FREQ_PROT_POINT+1},
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UNDER_FREQ_PROT_TIME+1},    

    // /* 三级保护参数 */
    // {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL3_PARA_ID, NOT_NEED_INTERACT},  
    // {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    // {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL_PARA_LEN, NOT_NEED_INTERACT},
    // {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_VOLT_PROT_POINT_GRID+2},
    // {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_VOLT_PROT_TIME_GRID+2},
    // {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UNDER_VOLT_PROT_POINT_GRID+2},
    // {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UNDER_VOLT_PROT_TIME_GRID+2},
    // {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_FREQ_PROT_POINT+2},
    // {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_FREQ_PROT_TIME+2},    
    // {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UNDER_FREQ_PROT_POINT+2},
    // {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UNDER_FREQ_PROT_TIME+2},   
    // /* 四级保护参数 */
    // {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL4_PARA_ID, NOT_NEED_INTERACT},  
    // {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    // {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL_PARA_LEN, NOT_NEED_INTERACT},
    // {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_VOLT_PROT_POINT_GRID+3},
    // {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_VOLT_PROT_TIME_GRID+3},
    // {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UNDER_VOLT_PROT_POINT_GRID+3},
    // {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UNDER_VOLT_PROT_TIME_GRID+3},
    // {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_FREQ_PROT_POINT+3},
    // {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_FREQ_PROT_TIME+3},    
    // {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UNDER_FREQ_PROT_POINT+3},
    // {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UNDER_FREQ_PROT_TIME+3},    

    // /* 五级保护参数 */
    // {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL5_PARA_ID, NOT_NEED_INTERACT},  
    // {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    // {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL_PARA_LEN, NOT_NEED_INTERACT},
    // {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_VOLT_PROT_POINT_GRID+4},
    // {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_VOLT_PROT_TIME_GRID+4},
    // {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UNDER_VOLT_PROT_POINT_GRID+4},
    // {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UNDER_VOLT_PROT_TIME_GRID+4},
    // {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_FREQ_PROT_POINT+4},
    // {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_FREQ_PROT_TIME+4},    
    // {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UNDER_FREQ_PROT_POINT+4},
    // {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UNDER_FREQ_PROT_TIME+4},     


    // /* 六级保护参数 */
    // {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL6_PARA_ID, NOT_NEED_INTERACT},  
    // {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    // {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL_PARA_LEN, NOT_NEED_INTERACT},
    // {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_VOLT_PROT_POINT_GRID+5},
    // {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_VOLT_PROT_TIME_GRID+5},
    // {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UNDER_VOLT_PROT_POINT_GRID+5},
    // {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UNDER_VOLT_PROT_TIME_GRID+5},
    // {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_FREQ_PROT_POINT+5},
    // {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_FREQ_PROT_TIME+5},    
    // {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UNDER_FREQ_PROT_POINT+5},
    // {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UNDER_FREQ_PROT_TIME+5},    

    /* 其他参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OTHER_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OTHER_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ALTITUDE},

    /* 组串接入检测 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, STRING_IN_DETECTION, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, STRING_IN_DETECTION_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_GROUP_STRING_LOSS_DETECTION},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_STARTING_CURRENT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_TWO_IN_ONE_STARTING_CURRENT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_GROUP_STRING_ACCESS_TYPE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_GROUP_STRING_ACCESS_TYPE + 1},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_GROUP_STRING_ACCESS_TYPE + 2},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_GROUP_STRING_ACCESS_TYPE + 3},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_GROUP_STRING_ACCESS_TYPE + 4},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_GROUP_STRING_ACCESS_TYPE + 5},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_GROUP_STRING_ACCESS_TYPE + 6},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_GROUP_STRING_ACCESS_TYPE + 7},

    /* 过欠频降额 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OVER_UNDER_FREQ_DERATING, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OVER_UNDER_FREQ_DERATING_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVERFREQUENCY_DERATING_ENABLE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVERFREQUENCY_DERATING_TRIG_FREQUENCY},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVERFREQUENCY_DERATING_EXIT_FREQUENCY},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVERFREQUENCY_DERATING_CUTOFF_FREQUENCY},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVERFREQUENCY_DERATING_RATED_POWER},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVERFREQUENCY_DERATING_RECOVERY_GRADIENT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_FREQUENCY_CONTROL_FILTERING_TIME},
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_FREQUENCY_ACTIVE_DERATING_RECOVERY_DELAY_TIME},
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_FREQUENCY_ACTIVE_DERATING_EFFECTIVE_TIME},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UNDER_FREQUENCY_BOOST_POWER_ENABLE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UNDER_FREQUENCY_BOOST_POWER_TRIG_FREQUENCY},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UNDER_FREQUENCY_BOOST_POWER_EXIT_FREQUENCY},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UNDER_FREQUENCY_BOOST_POWER_CUTOFF_FREQUENCY},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UNDER_FREQUENCY_BOOST_POWER_CUTOFF_POWER},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UNDER_FREQUENCY_BOOST_POWER_RECOVERY_GRADIENT},
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UNDER_FREQUENCY_ACTIVE_DERATING_RECOVERY_DELAY_TIME},
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UNDER_FREQUENCY_ACTIVE_DERATING_EFFECTIVE_TIME},

    /* 驱动测试相关参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DRIVE_TEST_PARA_ID, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DRIVE_TEST_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_DUTY_RATIO},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_DUTY_RATIO+1},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_DUTY_RATIO+2},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_DUTY_RATIO+3},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_VOLTAGE_PEAK},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_VOLTAGE_FREQUENCY},

    /* ADC采样测试相关参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, ADC_SAMPLE_TEST_PARA_ID, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, ADC_SAMPLE_TEST_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ADC_SAMPLING_TEST_ENABLE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_TARGET_SAMPLING_CHANNEL},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_SET_SAMPLING_VALUE},

    /* 研发测试相关参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, RD_TEST_RELATED_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, RD_TEST_RELATED_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_HARDWARE_VERSION},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_LEAK_CURR_PROTECTION_PROHIBITION},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_LOW_IMPEDANCE_PROTECTION_PROHIBITION},

    /* 生产测试相关参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PRODUCTION_TEST_PARA_ID, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PRODUCTION_TEST_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_INTERNAL_FAN_DUTY_CYCLE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_EXTERNAL_FAN_DUTY_CYCLE},

};


/* 设置参数打包命令 */
static data_info_id_verison_t cmd_set_para_data_parse_info[] RAM_SECTION = {
    /* 遥控开关机 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, REMOTE_CONTROL_SWITCH_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, REMOTE_CONTROL_SWITCH_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_POWER_ON_OFF_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PHASE_SEQ_ADAPTIVE_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_CONNECT_TYPE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_EN_LEAK_CURR_SELF_CHECK_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_RCD_BOOST_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_WEAK_GRID_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_WATER_INGRESS_ENABLE_OFFSET},



    /* 电网参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, POWER_GRID_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, POWER_GRID_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GRID_STANDARD_CODE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GRID_ISOLATION_SETTING_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OUTPUT_MODE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ATUO_START_AFTER_GRID_FAULT_RECOVER_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CONNECT_TIME_AFTER_GRID_FAULT_RECOVER_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_DELAY_START_TIME_AFTER_GRID_FAULT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UPPER_LIMIT_GRID_RECONN_VOLT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LOWER_LIMIT_GRID_RECONN_VOLT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UPPER_LIMIT_GRID_RECONN_FREQ_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LOWER_LIMIT_GRID_RECONN_FREQ_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PQ_MODE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LOWER_LIMIT_GRID_CONN_START_VOLT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UPPER_LIMIT_GRID_CONN_START_FREQ_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LOWER_LIMIT_GRID_CONN_START_FREQ_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_RATED_GRID_VOLT_OFFSET},




    /* 特性参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, CHARACTERISTIC_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, CHARACTERISTIC_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_STANDBY_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_BOOT_SOFT_START_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ISLAND_DETECT_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ENABLE_GRID_VOLT_PROT_SHEILD_DURING_VRT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_VRT_EXIT_HYSTERESIS_THRESHLOD_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GRID_FAULT_ZERO_CURR_MODE_OFFSET},


    /* MPPT参数设置 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_PARA_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_SCAN_INTERVAL_OFFSET},

    /* LVRT参数设置 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, LVRT_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, LVRT_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_TRIGG_THRESHOLD_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_POS_SEQ_REACTIVE_POWER_COMPEN_FACTOR_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_NEG_SEQ_REACTIVE_POWER_COMPEN_FACTOR_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_REACTIVE_CURR_LIMIT_PERCENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_ZERO_CURR_MODE_THRESHOLD_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_MODE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_ACTIVE_CURR_MAINTEN_COEFFICIENT_OFFSET},

    /* LVRT特征曲线 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, LVRT_CURVE_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, LVRT_CURVE_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CHARACTER_CURVE_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_VOLT_PERCENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET+1},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_VOLT_PERCENT_OFFSET+1},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET+2},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_VOLT_PERCENT_OFFSET+2},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET+3},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_VOLT_PERCENT_OFFSET+3},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET+4},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_VOLT_PERCENT_OFFSET+4},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET+5},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_VOLT_PERCENT_OFFSET+5},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET+6},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_VOLT_PERCENT_OFFSET+6},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET+7},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_VOLT_PERCENT_OFFSET+7},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET+8},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_VOLT_PERCENT_OFFSET+8},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET+9},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_VOLT_PERCENT_OFFSET+9},

    /* HVRT参数设置 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, HVRT_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, HVRT_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_TRIGG_THRESHOLD_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_POS_SEQ_REACTIVE_POWER_COMPEN_FACTOR_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_NEG_SEQ_REACTIVE_POWER_COMPEN_FACTOR_OFFSET},

    /* HVRT特征曲线 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, HVRT_CURVE_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, HVRT_CURVE_PARA_LEN, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET+1},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET+1},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET+2},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET+2},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET+3},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET+3},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET+4},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET+4},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET+5},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET+5},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET+6},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET+6},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET+7},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET+7},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET+8},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET+8},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET+9},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET+9},

    /* PID参数设置 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PID_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PID_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PID_REPAIR_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NIGHT_PID_PROT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NIGHT_SLEEP_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NIGHT_SLEEP_WAIT_TIME_OFFSET},


    /* AFCI参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, AFCI_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, AFCI_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AFCI_DETECT_ON_OFF_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AFCI_CHECK_DETECTION_SENSITIVITY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AFCI_SAVE_ENABLE_OFFSET},

    /* 功率调节参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, POWER_REGULATION_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, POWER_REGULATION_PARA_LEN, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REMOTE_POWER_DISPATCH_ENABLE_DISABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_SHCED_INSTRUCT_MAINTEN_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ACTIVE_POWER_REFERENCE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_APPARNET_POWER_REFERENCE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MAX_APPARENT_POWER_PARAM_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MAX_ACTIVE_POWER_PARAM_OFFSET},
    // {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_POWER_LIMIT_ZERO_PERCENT_SHUTDOWN_OFFSET},
    // {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NIGHT_REACTIVE_ENABLE_OFFSET},
    // {SEG_SYSTEM_PARA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NIGTH_REACTIVE_POWER_COMPEN_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_SHUTDOWN_GRADIENT_OFFSET},




    /* 有功功率调节参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, ACTIVE_POWER_REGULATION_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, ACTIVE_POWER_REGULATION_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GRID_ACTIVE_POWER_CHANGE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ACTIVE_POWER_CONTROL_MODE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ACTIVE_POWER_DERATING_SETTING_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ACTIVE_POWER_DERATING_PERCENT_OFFSET},


    /* 无功功率调节 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, REACTIVE_POWER_REGULATION_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, REACTIVE_POWER_REGULATION_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GRID_REACTIVE_POWER_CHANGE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REACTIVE_POWER_COMPEN_MODE_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REACTIVE_POWER_COMPEN_SETTING_FACTOR_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REACTIVE_POWER_COMPEN_SETTING_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REACTIVE_POWER_REGULATIONG_TIME_OFFSET},

    {SEG_SYSTEM_PARA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REACTIVE_POWER_VALUE_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REACTIVE_POWER_PCT_OFFSET},


    /* cosφ-P/Pn特性曲线 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, P_PN_CURVE_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, P_PN_CURVE_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_QP_MODE_TRIGG_VOLT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_QP_MODE_EXIT_VOLT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_SETTING_POINT_OF_COS_CURVE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_P_PN_AXIS_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_COS_AXIS_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_P_PN_AXIS_OFFSET+1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_COS_AXIS_OFFSET+1},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_P_PN_AXIS_OFFSET+2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_COS_AXIS_OFFSET+2},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_P_PN_AXIS_OFFSET+3},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_COS_AXIS_OFFSET+3},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_P_PN_AXIS_OFFSET+4},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_COS_AXIS_OFFSET+4},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_P_PN_AXIS_OFFSET+5},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_COS_AXIS_OFFSET+5},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_P_PN_AXIS_OFFSET+6},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_COS_AXIS_OFFSET+6},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_P_PN_AXIS_OFFSET+7},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_COS_AXIS_OFFSET+7},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_P_PN_AXIS_OFFSET+8},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_COS_AXIS_OFFSET+8},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_P_PN_AXIS_OFFSET+9},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_COS_AXIS_OFFSET+9},



    /* Q-U特性曲线设置 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, Q_U_CURVE_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, Q_U_CURVE_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_QU_SCHEDU_TRIGG_POWER_PERCENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_QU_SCHEDU_EXIT_POWER_PERCENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_QU_CHARACTER_CURVE_MIN_PF_LIMIT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HYSTERESIS_RATIO_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NUMBER_OF_POINT_FOR_QU_CURVE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_U_UN_AXIS_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_QS_AXIS_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_U_UN_AXIS_OFFSET+1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_QS_AXIS_OFFSET+1},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_U_UN_AXIS_OFFSET+2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_QS_AXIS_OFFSET+2},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_U_UN_AXIS_OFFSET+3},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_QS_AXIS_OFFSET+3},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_U_UN_AXIS_OFFSET+4},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_QS_AXIS_OFFSET+4},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_U_UN_AXIS_OFFSET+5},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_QS_AXIS_OFFSET+5},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_U_UN_AXIS_OFFSET+6},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_QS_AXIS_OFFSET+6},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_U_UN_AXIS_OFFSET+7},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_QS_AXIS_OFFSET+7},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_U_UN_AXIS_OFFSET+8},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_QS_AXIS_OFFSET+8},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_U_UN_AXIS_OFFSET+9},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_QS_AXIS_OFFSET+9},


    /* PF-U特性曲线设置 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PF_U_CURVE_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PF_U_CURVE_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_VOLT_DETECT_FILTER_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NUMBER_OF_POINT_FOR_PF_CURVE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_U_AXIS_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_PF_AXIS_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_U_AXIS_OFFSET+1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_AXIS_OFFSET+1},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_U_AXIS_OFFSET+2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_AXIS_OFFSET+2},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_U_AXIS_OFFSET+3},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_AXIS_OFFSET+3},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_U_AXIS_OFFSET+4},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_AXIS_OFFSET+4},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_U_AXIS_OFFSET+5},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_AXIS_OFFSET+5},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_U_AXIS_OFFSET+6},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_AXIS_OFFSET+6},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_U_AXIS_OFFSET+7},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_AXIS_OFFSET+7},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_U_AXIS_OFFSET+8},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_AXIS_OFFSET+8},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_U_AXIS_OFFSET+9},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_AXIS_OFFSET+9},


    /* 保护参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTION_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTION_PARA_LEN, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GRID_VOLT_IMBALANCE_PROT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_INSULATION_IMPEDANCE_PROT_OFFSET},
    // {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_TEN_MINUTE_GRID_OVER_PROT_POINT_OFFSET},
    // {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_TEN_MINUTE_GRID_OVER_PROT_TIME_OFFSET},




    /* 一级保护参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL1_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL_PARA_LEN, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_VOLT_PROT_POINT_GRID_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_VOLT_PROT_TIME_GRID_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_VOLT_PROT_POINT_GRID_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_VOLT_PROT_TIME_GRID_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQ_PROT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQ_PROT_TIME_OFFSET},    
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQ_PROT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQ_PROT_TIME_OFFSET},    
    
    
    /* 二级保护参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL2_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_VOLT_PROT_POINT_GRID_OFFSET+1},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_VOLT_PROT_TIME_GRID_OFFSET+1},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_VOLT_PROT_POINT_GRID_OFFSET+1},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_VOLT_PROT_TIME_GRID_OFFSET+1},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQ_PROT_POINT_OFFSET+1},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQ_PROT_TIME_OFFSET+1},    
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQ_PROT_POINT_OFFSET+1},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQ_PROT_TIME_OFFSET+1},   

    /* 三级保护参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL3_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_VOLT_PROT_POINT_GRID_OFFSET+2},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_VOLT_PROT_TIME_GRID_OFFSET+2},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_VOLT_PROT_POINT_GRID_OFFSET+2},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_VOLT_PROT_TIME_GRID_OFFSET+2},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQ_PROT_POINT_OFFSET+2},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQ_PROT_TIME_OFFSET+2},    
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQ_PROT_POINT_OFFSET+2},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQ_PROT_TIME_OFFSET+2},    

    /* 四级保护参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL4_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_VOLT_PROT_POINT_GRID_OFFSET+3},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_VOLT_PROT_TIME_GRID_OFFSET+3},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_VOLT_PROT_POINT_GRID_OFFSET+3},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_VOLT_PROT_TIME_GRID_OFFSET+3},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQ_PROT_POINT_OFFSET+3},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQ_PROT_TIME_OFFSET+3},    
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQ_PROT_POINT_OFFSET+3},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQ_PROT_TIME_OFFSET+3},    

    /* 五级保护参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL5_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_VOLT_PROT_POINT_GRID_OFFSET+4},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_VOLT_PROT_TIME_GRID_OFFSET+4},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_VOLT_PROT_POINT_GRID_OFFSET+4},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_VOLT_PROT_TIME_GRID_OFFSET+4},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQ_PROT_POINT_OFFSET+4},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQ_PROT_TIME_OFFSET+4},    
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQ_PROT_POINT_OFFSET+4},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQ_PROT_TIME_OFFSET+4},    


    /* 六级保护参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL6_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_VOLT_PROT_POINT_GRID_OFFSET+5},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_VOLT_PROT_TIME_GRID_OFFSET+5},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_VOLT_PROT_POINT_GRID_OFFSET+5},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_VOLT_PROT_TIME_GRID_OFFSET+5},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQ_PROT_POINT_OFFSET+5},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQ_PROT_TIME_OFFSET+5},    
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQ_PROT_POINT_OFFSET+5},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQ_PROT_TIME_OFFSET+5},  

    /* 其他参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OTHER_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OTHER_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ALTITUDE_OFFSET},

    /* 组串接入检测 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, STRING_IN_DETECTION, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, STRING_IN_DETECTION_LEN, NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_LOSS_DETECTION_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_STARTING_CURRENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_TWO_IN_ONE_STARTING_CURRENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 2},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 3},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 4},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 5},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 6},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 7},

    /* 过欠频降额 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OVER_UNDER_FREQ_DERATING, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OVER_UNDER_FREQ_DERATING_LEN, NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVERFREQUENCY_DERATING_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVERFREQUENCY_DERATING_TRIG_FREQUENCY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVERFREQUENCY_DERATING_EXIT_FREQUENCY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVERFREQUENCY_DERATING_CUTOFF_FREQUENCY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVERFREQUENCY_DERATING_RATED_POWER_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVERFREQUENCY_DERATING_RECOVERY_GRADIENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_FREQUENCY_CONTROL_FILTERING_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQUENCY_ACTIVE_DERATING_RECOVERY_DELAY_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQUENCY_ACTIVE_DERATING_EFFECTIVE_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_TRIG_FREQUENCY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_EXIT_FREQUENCY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_CUTOFF_FREQUENCY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_CUTOFF_POWER_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_RECOVERY_GRADIENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_ACTIVE_DERATING_RECOVERY_DELAY_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_ACTIVE_DERATING_EFFECTIVE_TIME_OFFSET},

};

/* 分段获取参数打包命令 */
static data_info_id_verison_t cmd_remote_ctrl_switch_para_data_pack_info[] RAM_SECTION = {
    /* 遥控开关机 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, REMOTE_CONTROL_SWITCH_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, REMOTE_CONTROL_SWITCH_PARA_LEN, NOT_NEED_INTERACT}, 
};
static data_info_id_verison_t cmd_power_grid_para_data_pack_info[] RAM_SECTION = {
    /* 电网参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, POWER_GRID_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, POWER_GRID_PARA_LEN, NOT_NEED_INTERACT},  
};
static data_info_id_verison_t cmd_chara_para_data_pack_info[] RAM_SECTION = {
    /* 特性参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, CHARACTERISTIC_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, CHARACTERISTIC_PARA_LEN, NOT_NEED_INTERACT},  
};
static data_info_id_verison_t cmd_mppt_para_data_pack_info[] RAM_SECTION = {
    /* MPPT参数设置 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_PARA_PARA_LEN, NOT_NEED_INTERACT},  
};
static data_info_id_verison_t cmd_lvrt_para_data_pack_info[] RAM_SECTION = {
    /* LVRT参数设置 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, LVRT_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, LVRT_PARA_LEN, NOT_NEED_INTERACT},   
};
static data_info_id_verison_t cmd_lvrt_cc_para_data_pack_info[] RAM_SECTION = {
    /* LVRT特征曲线 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, LVRT_CURVE_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, LVRT_CURVE_PARA_LEN, NOT_NEED_INTERACT},  

};
static data_info_id_verison_t cmd_hvrt_para_data_pack_info[] RAM_SECTION = {
    /* HVRT参数设置 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, HVRT_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, HVRT_PARA_LEN, NOT_NEED_INTERACT},  

};

static data_info_id_verison_t cmd_hvrt_cc_para_data_pack_info[] RAM_SECTION = {
    /* HVRT特征曲线 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, HVRT_CURVE_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, HVRT_CURVE_PARA_LEN, NOT_NEED_INTERACT},   

};

static data_info_id_verison_t cmd_pid_para_data_pack_info[] RAM_SECTION = {
    /* PID参数设置 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PID_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PID_PARA_LEN, NOT_NEED_INTERACT},  

};
static data_info_id_verison_t cmd_afci_para_data_pack_info[] RAM_SECTION = {
    /* AFCI参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, AFCI_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, AFCI_PARA_LEN, NOT_NEED_INTERACT},  

};
static data_info_id_verison_t cmd_power_regulation_para_data_pack_info[] RAM_SECTION = {
    /* 功率调节参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, POWER_REGULATION_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, POWER_REGULATION_PARA_LEN, NOT_NEED_INTERACT}, 

};

static data_info_id_verison_t cmd_act_regulation_para_data_pack_info[] RAM_SECTION = {
    /* 有功功率调节参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, ACTIVE_POWER_REGULATION_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, ACTIVE_POWER_REGULATION_PARA_LEN, NOT_NEED_INTERACT},  

};
static data_info_id_verison_t cmd_react_regulation_para_data_pack_info[] RAM_SECTION = {
    /* 无功功率调节 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, REACTIVE_POWER_REGULATION_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, REACTIVE_POWER_REGULATION_PARA_LEN, NOT_NEED_INTERACT},  

};
static data_info_id_verison_t cmd_p_pn_cc_para_data_pack_info[] RAM_SECTION = {
    /* cosφ-P/Pn特性曲线 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, P_PN_CURVE_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, P_PN_CURVE_PARA_LEN, NOT_NEED_INTERACT},  

};
static data_info_id_verison_t cmd_q_u_cc_para_data_pack_info[] RAM_SECTION = {
    /* Q-U特性曲线设置 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, Q_U_CURVE_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, Q_U_CURVE_PARA_LEN, NOT_NEED_INTERACT},  

};
static data_info_id_verison_t cmd_pf_u_cc_para_data_pack_info[] RAM_SECTION = {
    /* PF-U特性曲线设置 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PF_U_CURVE_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PF_U_CURVE_PARA_LEN, NOT_NEED_INTERACT},  

};

static data_info_id_verison_t cmd_protect_para_data_pack_info[] RAM_SECTION = {
    /* 保护参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTION_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTION_PARA_LEN, NOT_NEED_INTERACT}, 

};
static data_info_id_verison_t cmd_protect_l1_para_data_pack_info[] RAM_SECTION = {
    /* 一级保护参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL1_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL_PARA_LEN, NOT_NEED_INTERACT}, 

};
static data_info_id_verison_t cmd_protect_l2_para_data_pack_info[] RAM_SECTION = {
    /* 二级保护参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL2_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL_PARA_LEN, NOT_NEED_INTERACT}, 

};
static data_info_id_verison_t cmd_protect_l3_para_data_pack_info[] RAM_SECTION = {
    /* 三级保护参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL3_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL_PARA_LEN, NOT_NEED_INTERACT},

};
static data_info_id_verison_t cmd_protect_l4_para_data_pack_info[] RAM_SECTION = {
    /* 四级保护参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL4_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL_PARA_LEN, NOT_NEED_INTERACT},

};
static data_info_id_verison_t cmd_protect_l5_para_data_pack_info[] RAM_SECTION = {
    /* 五级保护参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL5_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL_PARA_LEN, NOT_NEED_INTERACT},

};
static data_info_id_verison_t cmd_protect_l6_para_data_pack_info[] RAM_SECTION = {
    /* 六级保护参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL6_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL_PARA_LEN, NOT_NEED_INTERACT},

};

static data_info_id_verison_t cmd_other_para_data_pack_info[] RAM_SECTION = {
    /* 其他参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OTHER_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OTHER_PARA_LEN, NOT_NEED_INTERACT},
};

static data_info_id_verison_t cmd_string_in_detection_data_pack_info[] RAM_SECTION = {
    /* 组串接入检测 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, STRING_IN_DETECTION, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, STRING_IN_DETECTION_LEN, NOT_NEED_INTERACT},
};

static data_info_id_verison_t cmd_over_under_freq_derate_data_pack_info[] RAM_SECTION = {
    /* 过欠频降额 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OVER_UNDER_FREQ_DERATING, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OVER_UNDER_FREQ_DERATING_LEN, NOT_NEED_INTERACT},
};


static data_info_id_verison_t cmd_drive_test_relate_data_pack_info[] RAM_SECTION = {
    /* 驱动测试相关参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DRIVE_TEST_PARA_ID, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DRIVE_TEST_PARA_LEN, NOT_NEED_INTERACT},
};

static data_info_id_verison_t cmd_adc_sample_test_data_pack_info[] RAM_SECTION = {
    /* ADC采样测试相关参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, ADC_SAMPLE_TEST_PARA_ID, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, ADC_SAMPLE_TEST_PARA_LEN, NOT_NEED_INTERACT},
};

static data_info_id_verison_t cmd_rd_test_relate_data_pack_info[] RAM_SECTION = {
    /* 研发测试相关参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, RD_TEST_RELATED_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, RD_TEST_RELATED_LEN, NOT_NEED_INTERACT},
};

static data_info_id_verison_t cmd_production_test_data_pack_info[] RAM_SECTION = {
    /* 生产测试相关参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PRODUCTION_TEST_PARA_ID, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PRODUCTION_TEST_PARA_LEN, NOT_NEED_INTERACT},
};

/* 分段获取参数解包命令 */

static data_info_id_verison_t cmd_remote_ctrl_switch_para_data_parse_info[] RAM_SECTION = {
    /* 遥控开关机 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, REMOTE_CONTROL_SWITCH_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, REMOTE_CONTROL_SWITCH_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_POWER_ON_OFF_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PHASE_SEQ_ADAPTIVE_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_CONNECT_TYPE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_EN_LEAK_CURR_SELF_CHECK_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_RCD_BOOST_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_WEAK_GRID_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_WATER_INGRESS_ENABLE_OFFSET},
};

static data_info_id_verison_t cmd_power_grid_para_parse_info[] RAM_SECTION = {
    /* 电网参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, POWER_GRID_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, POWER_GRID_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GRID_STANDARD_CODE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GRID_ISOLATION_SETTING_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OUTPUT_MODE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ATUO_START_AFTER_GRID_FAULT_RECOVER_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CONNECT_TIME_AFTER_GRID_FAULT_RECOVER_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_DELAY_START_TIME_AFTER_GRID_FAULT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UPPER_LIMIT_GRID_RECONN_VOLT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LOWER_LIMIT_GRID_RECONN_VOLT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UPPER_LIMIT_GRID_RECONN_FREQ_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LOWER_LIMIT_GRID_RECONN_FREQ_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PQ_MODE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LOWER_LIMIT_GRID_CONN_START_VOLT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UPPER_LIMIT_GRID_CONN_START_FREQ_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LOWER_LIMIT_GRID_CONN_START_FREQ_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_RATED_GRID_VOLT_OFFSET},


};
static data_info_id_verison_t cmd_chara_para_data_parse_info[] RAM_SECTION = {
    /* 特性参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, CHARACTERISTIC_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, CHARACTERISTIC_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_STANDBY_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_BOOT_SOFT_START_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ISLAND_DETECT_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ENABLE_GRID_VOLT_PROT_SHEILD_DURING_VRT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_VRT_EXIT_HYSTERESIS_THRESHLOD_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GRID_FAULT_ZERO_CURR_MODE_OFFSET},

};
static data_info_id_verison_t cmd_mppt_para_data_parse_info[] RAM_SECTION = {
    /* MPPT参数设置 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_PARA_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_SCAN_INTERVAL_OFFSET},

};
static data_info_id_verison_t cmd_lvrt_para_data_parse_info[] RAM_SECTION = {
    /* LVRT参数设置 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, LVRT_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, LVRT_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_TRIGG_THRESHOLD_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_POS_SEQ_REACTIVE_POWER_COMPEN_FACTOR_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_NEG_SEQ_REACTIVE_POWER_COMPEN_FACTOR_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_REACTIVE_CURR_LIMIT_PERCENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_ZERO_CURR_MODE_THRESHOLD_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_MODE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_ACTIVE_CURR_MAINTEN_COEFFICIENT_OFFSET},

};
static data_info_id_verison_t cmd_lvrt_cc_para_data_parse_info[] RAM_SECTION = {
    /* LVRT特征曲线 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, LVRT_CURVE_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, LVRT_CURVE_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CHARACTER_CURVE_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_VOLT_PERCENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET+1},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_VOLT_PERCENT_OFFSET+1},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET+2},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_VOLT_PERCENT_OFFSET+2},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET+3},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_VOLT_PERCENT_OFFSET+3},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET+4},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_VOLT_PERCENT_OFFSET+4},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET+5},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_VOLT_PERCENT_OFFSET+5},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET+6},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_VOLT_PERCENT_OFFSET+6},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET+7},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_VOLT_PERCENT_OFFSET+7},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET+8},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_VOLT_PERCENT_OFFSET+8},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET+9},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_VOLT_PERCENT_OFFSET+9},

};
static data_info_id_verison_t cmd_hvrt_para_data_parse_info[] RAM_SECTION = {
    /* HVRT参数设置 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, HVRT_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, HVRT_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_TRIGG_THRESHOLD_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_POS_SEQ_REACTIVE_POWER_COMPEN_FACTOR_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_NEG_SEQ_REACTIVE_POWER_COMPEN_FACTOR_OFFSET},

};

static data_info_id_verison_t cmd_hvrt_cc_para_data_parse_info[] RAM_SECTION = {
    /* HVRT特征曲线 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, HVRT_CURVE_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, HVRT_CURVE_PARA_LEN, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET+1},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET+1},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET+2},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET+2},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET+3},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET+3},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET+4},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET+4},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET+5},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET+5},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET+6},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET+6},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET+7},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET+7},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET+8},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET+8},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET+9},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET+9},

};

static data_info_id_verison_t cmd_pid_para_data_parse_info[] RAM_SECTION = {
    /* PID参数设置 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PID_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PID_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PID_REPAIR_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NIGHT_PID_PROT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NIGHT_SLEEP_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NIGHT_SLEEP_WAIT_TIME_OFFSET},

};
static data_info_id_verison_t cmd_afci_para_data_parse_info[] RAM_SECTION = {
    /* AFCI参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, AFCI_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, AFCI_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AFCI_DETECT_ON_OFF_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AFCI_CHECK_DETECTION_SENSITIVITY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AFCI_SAVE_ENABLE_OFFSET},

};
static data_info_id_verison_t cmd_power_regulation_para_data_parse_info[] RAM_SECTION = {
    /* 功率调节参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, POWER_REGULATION_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, POWER_REGULATION_PARA_LEN, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REMOTE_POWER_DISPATCH_ENABLE_DISABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_SHCED_INSTRUCT_MAINTEN_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ACTIVE_POWER_REFERENCE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_APPARNET_POWER_REFERENCE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MAX_APPARENT_POWER_PARAM_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MAX_ACTIVE_POWER_PARAM_OFFSET},
    // {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_POWER_LIMIT_ZERO_PERCENT_SHUTDOWN_OFFSET},
    // {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NIGHT_REACTIVE_ENABLE_OFFSET},
    // {SEG_SYSTEM_PARA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NIGTH_REACTIVE_POWER_COMPEN_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_SHUTDOWN_GRADIENT_OFFSET},


};

static data_info_id_verison_t cmd_act_regulation_para_data_parse_info[] RAM_SECTION = {
    /* 有功功率调节参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, ACTIVE_POWER_REGULATION_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, ACTIVE_POWER_REGULATION_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GRID_ACTIVE_POWER_CHANGE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ACTIVE_POWER_CONTROL_MODE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ACTIVE_POWER_DERATING_SETTING_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ACTIVE_POWER_DERATING_PERCENT_OFFSET},

};
static data_info_id_verison_t cmd_react_regulation_para_data_parse_info[] RAM_SECTION = {
    /* 无功功率调节 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, REACTIVE_POWER_REGULATION_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, REACTIVE_POWER_REGULATION_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GRID_REACTIVE_POWER_CHANGE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REACTIVE_POWER_COMPEN_MODE_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REACTIVE_POWER_COMPEN_SETTING_FACTOR_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REACTIVE_POWER_COMPEN_SETTING_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REACTIVE_POWER_REGULATIONG_TIME_OFFSET},

    {SEG_SYSTEM_PARA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REACTIVE_POWER_VALUE_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REACTIVE_POWER_PCT_OFFSET},

};
static data_info_id_verison_t cmd_p_pn_cc_para_data_parse_info[] RAM_SECTION = {
    /* cosφ-P/Pn特性曲线 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, P_PN_CURVE_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, P_PN_CURVE_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_QP_MODE_TRIGG_VOLT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_QP_MODE_EXIT_VOLT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_SETTING_POINT_OF_COS_CURVE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_P_PN_AXIS_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_COS_AXIS_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_P_PN_AXIS_OFFSET+1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_COS_AXIS_OFFSET+1},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_P_PN_AXIS_OFFSET+2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_COS_AXIS_OFFSET+2},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_P_PN_AXIS_OFFSET+3},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_COS_AXIS_OFFSET+3},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_P_PN_AXIS_OFFSET+4},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_COS_AXIS_OFFSET+4},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_P_PN_AXIS_OFFSET+5},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_COS_AXIS_OFFSET+5},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_P_PN_AXIS_OFFSET+6},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_COS_AXIS_OFFSET+6},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_P_PN_AXIS_OFFSET+7},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_COS_AXIS_OFFSET+7},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_P_PN_AXIS_OFFSET+8},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_COS_AXIS_OFFSET+8},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_P_PN_AXIS_OFFSET+9},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_COS_AXIS_OFFSET+9},

};
static data_info_id_verison_t cmd_q_u_cc_para_data_parse_info[] RAM_SECTION = {
    /* Q-U特性曲线设置 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, Q_U_CURVE_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, Q_U_CURVE_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_QU_SCHEDU_TRIGG_POWER_PERCENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_QU_SCHEDU_EXIT_POWER_PERCENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_QU_CHARACTER_CURVE_MIN_PF_LIMIT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HYSTERESIS_RATIO_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NUMBER_OF_POINT_FOR_QU_CURVE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_U_UN_AXIS_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_QS_AXIS_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_U_UN_AXIS_OFFSET+1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_QS_AXIS_OFFSET+1},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_U_UN_AXIS_OFFSET+2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_QS_AXIS_OFFSET+2},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_U_UN_AXIS_OFFSET+3},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_QS_AXIS_OFFSET+3},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_U_UN_AXIS_OFFSET+4},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_QS_AXIS_OFFSET+4},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_U_UN_AXIS_OFFSET+5},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_QS_AXIS_OFFSET+5},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_U_UN_AXIS_OFFSET+6},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_QS_AXIS_OFFSET+6},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_U_UN_AXIS_OFFSET+7},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_QS_AXIS_OFFSET+7},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_U_UN_AXIS_OFFSET+8},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_QS_AXIS_OFFSET+8},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_U_UN_AXIS_OFFSET+9},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_QS_AXIS_OFFSET+9},

};
static data_info_id_verison_t cmd_pf_u_cc_para_data_parse_info[] RAM_SECTION = {
    /* PF-U特性曲线设置 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PF_U_CURVE_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PF_U_CURVE_PARA_LEN, NOT_NEED_INTERACT},  
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_VOLT_DETECT_FILTER_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NUMBER_OF_POINT_FOR_PF_CURVE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_U_AXIS_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_PF_AXIS_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_U_AXIS_OFFSET+1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_AXIS_OFFSET+1},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_U_AXIS_OFFSET+2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_AXIS_OFFSET+2},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_U_AXIS_OFFSET+3},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_AXIS_OFFSET+3},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_U_AXIS_OFFSET+4},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_AXIS_OFFSET+4},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_U_AXIS_OFFSET+5},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_AXIS_OFFSET+5},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_U_AXIS_OFFSET+6},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_AXIS_OFFSET+6},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_U_AXIS_OFFSET+7},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_AXIS_OFFSET+7},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_U_AXIS_OFFSET+8},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_AXIS_OFFSET+8},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_U_AXIS_OFFSET+9},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_AXIS_OFFSET+9},

};

static data_info_id_verison_t cmd_protect_para_data_parse_info[] RAM_SECTION = {
    /* 保护参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTION_PARA_ID, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTION_PARA_LEN, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GRID_VOLT_IMBALANCE_PROT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_INSULATION_IMPEDANCE_PROT_OFFSET},
    // {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_TEN_MINUTE_GRID_OVER_PROT_POINT_OFFSET},
    // {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_TEN_MINUTE_GRID_OVER_PROT_TIME_OFFSET},

};
static data_info_id_verison_t cmd_protect_l1_para_data_parse_info[] RAM_SECTION = {
    /* 一级保护参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL1_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL_PARA_LEN, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_VOLT_PROT_POINT_GRID_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_VOLT_PROT_TIME_GRID_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_VOLT_PROT_POINT_GRID_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_VOLT_PROT_TIME_GRID_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQ_PROT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQ_PROT_TIME_OFFSET},    
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQ_PROT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQ_PROT_TIME_OFFSET},    
   
};
static data_info_id_verison_t cmd_protect_l2_para_data_parse_info[] RAM_SECTION = {
    /* 二级保护参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL2_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_VOLT_PROT_POINT_GRID_OFFSET+1},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_VOLT_PROT_TIME_GRID_OFFSET+1},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_VOLT_PROT_POINT_GRID_OFFSET+1},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_VOLT_PROT_TIME_GRID_OFFSET+1},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQ_PROT_POINT_OFFSET+1},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQ_PROT_TIME_OFFSET+1},    
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQ_PROT_POINT_OFFSET+1},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQ_PROT_TIME_OFFSET+1},  
};
static data_info_id_verison_t cmd_protect_l3_para_data_parse_info[] RAM_SECTION = {
    /* 三级保护参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL3_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_VOLT_PROT_POINT_GRID_OFFSET+2},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_VOLT_PROT_TIME_GRID_OFFSET+2},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_VOLT_PROT_POINT_GRID_OFFSET+2},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_VOLT_PROT_TIME_GRID_OFFSET+2},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQ_PROT_POINT_OFFSET+2},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQ_PROT_TIME_OFFSET+2},    
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQ_PROT_POINT_OFFSET+2},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQ_PROT_TIME_OFFSET+2},    

};
static data_info_id_verison_t cmd_protect_l4_para_data_parse_info[] RAM_SECTION = {
    /* 四级保护参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL4_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_VOLT_PROT_POINT_GRID_OFFSET+3},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_VOLT_PROT_TIME_GRID_OFFSET+3},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_VOLT_PROT_POINT_GRID_OFFSET+3},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_VOLT_PROT_TIME_GRID_OFFSET+3},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQ_PROT_POINT_OFFSET+3},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQ_PROT_TIME_OFFSET+3},    
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQ_PROT_POINT_OFFSET+3},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQ_PROT_TIME_OFFSET+3},    

};
static data_info_id_verison_t cmd_protect_l5_para_data_parse_info[] RAM_SECTION = {
    /* 五级保护参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL5_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_VOLT_PROT_POINT_GRID_OFFSET+4},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_VOLT_PROT_TIME_GRID_OFFSET+4},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_VOLT_PROT_POINT_GRID_OFFSET+4},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_VOLT_PROT_TIME_GRID_OFFSET+4},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQ_PROT_POINT_OFFSET+4},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQ_PROT_TIME_OFFSET+4},    
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQ_PROT_POINT_OFFSET+4},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQ_PROT_TIME_OFFSET+4},    

};
static data_info_id_verison_t cmd_protect_l6_para_data_parse_info[] RAM_SECTION = {
    /* 六级保护参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL6_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PROTECTIN_LEVEL_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_VOLT_PROT_POINT_GRID_OFFSET+5},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_VOLT_PROT_TIME_GRID_OFFSET+5},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_VOLT_PROT_POINT_GRID_OFFSET+5},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_VOLT_PROT_TIME_GRID_OFFSET+5},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQ_PROT_POINT_OFFSET+5},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQ_PROT_TIME_OFFSET+5},    
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQ_PROT_POINT_OFFSET+5},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQ_PROT_TIME_OFFSET+5},  

};

static data_info_id_verison_t cmd_other_para_data_parse_info[] RAM_SECTION = {
    /* 其他参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OTHER_PARA_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OTHER_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ALTITUDE_OFFSET},
};

static data_info_id_verison_t cmd_string_in_detection_data_parse_info[] RAM_SECTION = {
    /* 组串接入检测 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, STRING_IN_DETECTION, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, STRING_IN_DETECTION_LEN, NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_LOSS_DETECTION_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_STARTING_CURRENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_TWO_IN_ONE_STARTING_CURRENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 2},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 3},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 4},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 5},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 6},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 7},
};

static data_info_id_verison_t cmd_over_under_freq_derate_data_parse_info[] RAM_SECTION = {
    /* 过欠频降额 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OVER_UNDER_FREQ_DERATING, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OVER_UNDER_FREQ_DERATING_LEN, NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVERFREQUENCY_DERATING_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVERFREQUENCY_DERATING_TRIG_FREQUENCY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVERFREQUENCY_DERATING_EXIT_FREQUENCY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVERFREQUENCY_DERATING_CUTOFF_FREQUENCY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVERFREQUENCY_DERATING_RATED_POWER_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVERFREQUENCY_DERATING_RECOVERY_GRADIENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_FREQUENCY_CONTROL_FILTERING_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQUENCY_ACTIVE_DERATING_RECOVERY_DELAY_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQUENCY_ACTIVE_DERATING_EFFECTIVE_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_TRIG_FREQUENCY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_EXIT_FREQUENCY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_CUTOFF_FREQUENCY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_CUTOFF_POWER_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_RECOVERY_GRADIENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_ACTIVE_DERATING_RECOVERY_DELAY_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_ACTIVE_DERATING_EFFECTIVE_TIME_OFFSET},

};

static data_info_id_verison_t cmd_drive_test_relate_data_parse_info[] RAM_SECTION = {
    /* 驱动测试相关参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DRIVE_TEST_PARA_ID, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DRIVE_TEST_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_DUTY_RATIO_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_DUTY_RATIO_OFFSET+1},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_DUTY_RATIO_OFFSET+2},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_DUTY_RATIO_OFFSET+3},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PV_VOLTAGE_PEAK_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PV_VOLTAGE_FREQUENCY_OFFSET},
};

static data_info_id_verison_t cmd_adc_sample_test_data_parse_info[] RAM_SECTION = {
    /* ADC采样测试相关参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, ADC_SAMPLE_TEST_PARA_ID, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, ADC_SAMPLE_TEST_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ADC_SAMPLING_TEST_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_TARGET_SAMPLING_CHANNEL_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_SET_SAMPLING_VALUE_OFFSET},
};


static data_info_id_verison_t cmd_rd_test_relate_data_parse_info[] RAM_SECTION = {
    /* 研发测试相关参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, RD_TEST_RELATED_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, RD_TEST_RELATED_LEN, NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HARDWARE_VERSION_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LEAK_CURR_PROTECTION_PROHIBITION_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LOW_IMPEDANCE_PROTECTION_PROHIBITION_OFFSET},
};

static data_info_id_verison_t cmd_production_test_data_parse_info[] RAM_SECTION = {
    /* 生产测试相关参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PRODUCTION_TEST_PARA_ID, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PRODUCTION_TEST_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_INTERNAL_FAN_DUTY_CYCLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_EXTERNAL_FAN_DUTY_CYCLE_OFFSET},
};

// 获取生产测试相关参数
static data_info_id_verison_t cmd_get_drive_test_relate_data_parse_info[] RAM_SECTION = {
    /* 驱动测试相关参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DRIVE_TEST_PARA_ID, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DRIVE_TEST_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_DUTY_RATIO},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_DUTY_RATIO+1},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_DUTY_RATIO+2},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_DUTY_RATIO+3},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_VOLTAGE_PEAK},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_VOLTAGE_FREQUENCY},
};

static data_info_id_verison_t cmd_get_adc_sample_test_data_parse_info[] RAM_SECTION = {
    /* ADC采样测试相关参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, ADC_SAMPLE_TEST_PARA_ID, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, ADC_SAMPLE_TEST_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ADC_SAMPLING_TEST_ENABLE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_TARGET_SAMPLING_CHANNEL},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_SET_SAMPLING_VALUE},
};


static data_info_id_verison_t cmd_get_rd_test_relate_data_parse_info[] RAM_SECTION = {
    /* 研发测试相关参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, RD_TEST_RELATED_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, RD_TEST_RELATED_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_HARDWARE_VERSION},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_LEAK_CURR_PROTECTION_PROHIBITION},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_LOW_IMPEDANCE_PROTECTION_PROHIBITION},
};

static data_info_id_verison_t cmd_get_production_test_data_parse_info[] RAM_SECTION = {
    /* 生产测试相关参数 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PRODUCTION_TEST_PARA_ID, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PRODUCTION_TEST_PARA_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_INTERNAL_FAN_DUTY_CYCLE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_EXTERNAL_FAN_DUTY_CYCLE},
};

/* 获取比例零点校正参数打包命令 */
static data_info_id_verison_t cmd_get_adj_para_data_pack_info[] RAM_SECTION = {
    /* 输出电压零点比例零点校正 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OUT_VOLT_ADJ_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OUT_VOLT_ADJ_LEN, NOT_NEED_INTERACT}, 

    /* BUS电压比例零点校正 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, BUS_VOLT_ADJ_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, BUS_VOLT_ADJ_LEN, NOT_NEED_INTERACT}, 

    /* MPPT1比例零点校正 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ADJ_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ADJ_LEN, NOT_NEED_INTERACT}, 

    /* MPPT2比例零点校正 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ADJ_ID + 1, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ADJ_LEN, NOT_NEED_INTERACT}, 

    /* MPPT3比例零点校正 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ADJ_ID + 2, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ADJ_LEN, NOT_NEED_INTERACT}, 

    /* MPPT4比例零点校正 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ADJ_ID + 3, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ADJ_LEN, NOT_NEED_INTERACT}, 

    /* 辅控比例零点校正 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, AUX_ADJ_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, AUX_ADJ_LEN, NOT_NEED_INTERACT}, 

    /* 漏电流比例校正 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, LEAK_CURR_ADJ_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, LEAK_CURR_ADJ_LEN, NOT_NEED_INTERACT},

    /* 直流分量比例校正 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DC_COMPONENT_ADJ_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DC_COMPONENT_ADJ_LEN, NOT_NEED_INTERACT},
};

/* 获取比例零点校正参数解包命令 */
static data_info_id_verison_t cmd_get_adj_para_data_parse_info[] RAM_SECTION = {
    /* 输出电压比例零点校正 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OUT_VOLT_ADJ_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OUT_VOLT_ADJ_LEN, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_CORRECT_RATIO_OF_GRID_VOLT},    
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_NET_CA_VOLT_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_CORRECT_RATIO_OF_GRID_VOLT+1}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_NET_CB_VOLT_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_CORRECT_RATIO_OF_GRID_VOLT+2}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_NET_C_VOLT_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_INVERTER_VOLT_CORRECT_RATIO}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_INVERTER_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_INVERTER_VOLT_CORRECT_RATIO+1}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_INVERTER_CORRECT_POINT+1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_INVERTER_VOLT_CORRECT_RATIO+2}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_INVERTER_CORRECT_POINT+2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_SINGLE_PHASE_GRID_CONNECT_CURR_CORRECT_RATIO}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_GRID_CURR_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_SINGLE_PHASE_GRID_CONNECT_CURR_CORRECT_RATIO+1}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_GRID_CURR_CORRECT_POINT+1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_SINGLE_PHASE_GRID_CONNECT_CURR_CORRECT_RATIO+2}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_GRID_CURR_CORRECT_POINT+2},

    /* BUS电压比例零点校正 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, BUS_VOLT_ADJ_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, BUS_VOLT_ADJ_LEN, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_POS_BUS_VOLT_CORRECT_RATIO}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_POS_BUS_CORRECT_POINT}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_NEG__BUS_VOLT_CORRECT_RATIO}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_NEG_BUS_CORRECT_POINT},
    
    /* MPPT1比例零点校正 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ADJ_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ADJ_LEN, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_INPUT_VOLT_CORRECT_RATIO}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_IN_VOLT_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_TOTAL_INPUT_CORRECT_RATIO}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ALLIN_CURR_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_BRANCH_INPUT_CURR_CORRECT_RATIO}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_BRANCH_IN_CURR_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_SAMPLE_CURRENT_CORRECT_RATIO}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_SAMPLE_CURRENT_CORRECT_POINT},

    /* MPPT2比例零点校正 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ADJ_ID + 1, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ADJ_LEN, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_INPUT_VOLT_CORRECT_RATIO+1}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_IN_VOLT_CORRECT_POINT+1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_TOTAL_INPUT_CORRECT_RATIO+1}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ALLIN_CURR_CORRECT_POINT+1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_BRANCH_INPUT_CURR_CORRECT_RATIO+1}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_BRANCH_IN_CURR_CORRECT_POINT+1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_SAMPLE_CURRENT_CORRECT_RATIO+1}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_SAMPLE_CURRENT_CORRECT_POINT+1},

    /* MPPT3比例零点校正 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ADJ_ID + 2, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ADJ_LEN, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_INPUT_VOLT_CORRECT_RATIO+2}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_IN_VOLT_CORRECT_POINT+2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_TOTAL_INPUT_CORRECT_RATIO+2}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ALLIN_CURR_CORRECT_POINT+2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_BRANCH_INPUT_CURR_CORRECT_RATIO+2}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_BRANCH_IN_CURR_CORRECT_POINT+2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_SAMPLE_CURRENT_CORRECT_RATIO+2}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_SAMPLE_CURRENT_CORRECT_POINT+2},

    /* MPPT4比例零点校正 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ADJ_ID + 3, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ADJ_LEN, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_INPUT_VOLT_CORRECT_RATIO+3}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_IN_VOLT_CORRECT_POINT+3},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_TOTAL_INPUT_CORRECT_RATIO+3}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ALLIN_CURR_CORRECT_POINT+3},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_BRANCH_INPUT_CURR_CORRECT_RATIO+3}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_BRANCH_IN_CURR_CORRECT_POINT+3},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_SAMPLE_CURRENT_CORRECT_RATIO+3}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_SAMPLE_CURRENT_CORRECT_POINT+3},

    /* 辅控比例零点校正 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, AUX_ADJ_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, AUX_ADJ_LEN, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_GRID_VOLT_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_NET_CA_VOLT_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_GRID_VOLT_CORRECT_RATIO+1}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_NET_CB_VOLT_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_GRID_VOLT_CORRECT_RATIO+2}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_NET_C_VOLT_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_INVERTER_VOLT_CORRECT_RATIO}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_INVERTER_PHASE_VOLT_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_INVERTER_VOLT_CORRECT_RATIO+1}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_INVERTER_PHASE_VOLT_CORRECT_POINT+1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_INVERTER_VOLT_CORRECT_RATIO+2}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_INVERTER_PHASE_VOLT_CORRECT_POINT+2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_INVERTER_CURR_CORRECT_RATIO}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_GRID_CURR_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_INVERTER_CURR_CORRECT_RATIO+1}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_GRID_CURR_CORRECT_POINT+1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_INVERTER_CURR_CORRECT_RATIO+2}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_GRID_CURR_CORRECT_POINT+2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_POS_BUS_VOLT_CORRECT_RATIO}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_POS_BUS_VOLT_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_NEG_BUS_VOLT_CORRECT_RATIO}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_NEG_BUS_VOLT_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_DC_ARC_DETECT_CORRECT_RATIO}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_DC_ARC_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_DC_ARC_DETECT_CORRECT_RATIO+1}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_DC_ARC_CORRECT_POINT+1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_DC_ARC_DETECT_CORRECT_RATIO+2}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_DC_ARC_CORRECT_POINT+2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_DC_ARC_DETECT_CORRECT_RATIO+3}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_DC_ARC_CORRECT_POINT+3},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_PE_VOLT_CORRECT_RATIO}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PE_VOLT_CORRECT_POINT},

    /* 漏电流比例校正 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, LEAK_CURR_ADJ_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, LEAK_CURR_ADJ_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_LEAK_CURR_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_LEAK_CURR_CORRECT_POINT},

    /* 直流分量比例校正 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DC_COMPONENT_ADJ_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DC_COMPONENT_ADJ_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_DC_COMPONENT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_DC_COMPONENT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_DC_COMPONENT_RATIO + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_DC_COMPONENT_POINT + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_DC_COMPONENT_RATIO + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_DC_COMPONENT_POINT + 2},

};

/* 设置比例零点校正参数解包命令分开 */
static data_info_id_verison_t cmd_set_out_volt_adj_para_data_pack_info[] RAM_SECTION = {
    /* 输出电压比例零点校正 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OUT_VOLT_ADJ_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, OUT_VOLT_ADJ_LEN, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CORRECT_RATIO_OF_GRID_VOLT_OFFSET},    
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MASTER_NET_CA_VOLT_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CORRECT_RATIO_OF_GRID_VOLT_OFFSET+1}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MASTER_NET_CB_VOLT_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CORRECT_RATIO_OF_GRID_VOLT_OFFSET+2}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MASTER_NET_C_VOLT_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_INVERTER_VOLT_CORRECT_RATIO_OFFSET}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MASTER_INVERTER_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_INVERTER_VOLT_CORRECT_RATIO_OFFSET+1}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MASTER_INVERTER_CORRECT_POINT_OFFSET+1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_INVERTER_VOLT_CORRECT_RATIO_OFFSET+2}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MASTER_INVERTER_CORRECT_POINT_OFFSET+2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_SINGLE_PHASE_GRID_CONNECT_CURR_CORRECT_RATIO_OFFSET}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MASTER_GRID_CURR_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_SINGLE_PHASE_GRID_CONNECT_CURR_CORRECT_RATIO_OFFSET+1}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MASTER_GRID_CURR_CORRECT_POINT_OFFSET+1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_SINGLE_PHASE_GRID_CONNECT_CURR_CORRECT_RATIO_OFFSET+2}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MASTER_GRID_CURR_CORRECT_POINT_OFFSET+2},
};

static data_info_id_verison_t cmd_set_bus_volt_adj_para_data_pack_info[] RAM_SECTION = {
    /* BUS电压比例零点校正 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, BUS_VOLT_ADJ_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, BUS_VOLT_ADJ_LEN, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_POS_BUS_VOLT_CORRECT_RATIO_OFFSET}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MASTER_POS_BUS_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NEG__BUS_VOLT_CORRECT_RATIO_OFFSET}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MASTER_NEG_BUS_CORRECT_POINT_OFFSET},
};

static data_info_id_verison_t cmd_set_mppt1_volt_adj_para_data_pack_info[] RAM_SECTION = {
    /* MPPT1比例零点校正 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ADJ_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ADJ_LEN, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_INPUT_VOLT_CORRECT_RATIO_OFFSET}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_IN_VOLT_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_TOTAL_INPUT_CORRECT_RATIO_OFFSET}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ALLIN_CURR_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_BRANCH_INPUT_CURR_CORRECT_RATIO_OFFSET}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_BRANCH_IN_CURR_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_SAMPLE_CURRENT_CORRECT_RATIO_OFFSET}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_SAMPLE_CURRENT_CORRECT_POINT_OFFSET},

};

static data_info_id_verison_t cmd_set_mppt2_volt_adj_para_data_pack_info[] RAM_SECTION = {
    /* MPPT2比例零点校正 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ADJ_ID + 1, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ADJ_LEN, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_INPUT_VOLT_CORRECT_RATIO_OFFSET+1}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_IN_VOLT_CORRECT_POINT_OFFSET+1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_TOTAL_INPUT_CORRECT_RATIO_OFFSET+1}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ALLIN_CURR_CORRECT_POINT_OFFSET+1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_BRANCH_INPUT_CURR_CORRECT_RATIO_OFFSET+1}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_BRANCH_IN_CURR_CORRECT_POINT_OFFSET+1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_SAMPLE_CURRENT_CORRECT_RATIO_OFFSET+1}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_SAMPLE_CURRENT_CORRECT_POINT_OFFSET+1},
};

static data_info_id_verison_t cmd_set_mppt3_volt_adj_para_data_pack_info[] RAM_SECTION = {

    /* MPPT3比例零点校正 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ADJ_ID + 2, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ADJ_LEN, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_INPUT_VOLT_CORRECT_RATIO_OFFSET+2}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_IN_VOLT_CORRECT_POINT_OFFSET+2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_TOTAL_INPUT_CORRECT_RATIO_OFFSET+2}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ALLIN_CURR_CORRECT_POINT_OFFSET+2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_BRANCH_INPUT_CURR_CORRECT_RATIO_OFFSET+2}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_BRANCH_IN_CURR_CORRECT_POINT_OFFSET+2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_SAMPLE_CURRENT_CORRECT_RATIO_OFFSET+2}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_SAMPLE_CURRENT_CORRECT_POINT_OFFSET+2},

};

static data_info_id_verison_t cmd_set_mppt4_volt_adj_para_data_pack_info[] RAM_SECTION = {
    /* MPPT4比例零点校正 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ADJ_ID + 3, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_ADJ_LEN, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_INPUT_VOLT_CORRECT_RATIO_OFFSET+3}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_IN_VOLT_CORRECT_POINT_OFFSET+3},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_TOTAL_INPUT_CORRECT_RATIO_OFFSET+3}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ALLIN_CURR_CORRECT_POINT_OFFSET+3},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_BRANCH_INPUT_CURR_CORRECT_RATIO_OFFSET+3}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_BRANCH_IN_CURR_CORRECT_POINT_OFFSET+3},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_SAMPLE_CURRENT_CORRECT_RATIO_OFFSET+3}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_SAMPLE_CURRENT_CORRECT_POINT_OFFSET+3},

};

static data_info_id_verison_t cmd_set_aux_adj_para_data_pack_info[] RAM_SECTION = {
    /* 辅控比例零点校正 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, AUX_ADJ_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, AUX_ADJ_LEN, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUXI_CONTROL_GRID_VOLT_CORRECT_RATIO_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUX_NET_CA_VOLT_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUXI_CONTROL_GRID_VOLT_CORRECT_RATIO_OFFSET+1}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUX_NET_CB_VOLT_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUXI_CONTROL_GRID_VOLT_CORRECT_RATIO_OFFSET+2}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUX_NET_C_VOLT_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUXI_CONTROL_INVERTER_VOLT_CORRECT_RATIO_OFFSET}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUX_INVERTER_PHASE_VOLT_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUXI_CONTROL_INVERTER_VOLT_CORRECT_RATIO_OFFSET+1}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUX_INVERTER_PHASE_VOLT_CORRECT_POINT_OFFSET+1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUXI_CONTROL_INVERTER_VOLT_CORRECT_RATIO_OFFSET+2}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUX_INVERTER_PHASE_VOLT_CORRECT_POINT_OFFSET+2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUXI_CONTROL_INVERTER_CURR_CORRECT_RATIO_OFFSET}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUX_GRID_CURR_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUXI_CONTROL_INVERTER_CURR_CORRECT_RATIO_OFFSET+1}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUX_GRID_CURR_CORRECT_POINT_OFFSET+1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUXI_CONTROL_INVERTER_CURR_CORRECT_RATIO_OFFSET+2}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUX_GRID_CURR_CORRECT_POINT_OFFSET+2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUXI_CONTROL_POS_BUS_VOLT_CORRECT_RATIO_OFFSET}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUX_POS_BUS_VOLT_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUXI_CONTROL_NEG_BUS_VOLT_CORRECT_RATIO_OFFSET}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUX_NEG_BUS_VOLT_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUXI_CONTROL_DC_ARC_DETECT_CORRECT_RATIO_OFFSET}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_DC_ARC_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUXI_CONTROL_DC_ARC_DETECT_CORRECT_RATIO_OFFSET+1}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_DC_ARC_CORRECT_POINT_OFFSET+1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUXI_CONTROL_DC_ARC_DETECT_CORRECT_RATIO_OFFSET+2}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_DC_ARC_CORRECT_POINT_OFFSET+2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUXI_CONTROL_DC_ARC_DETECT_CORRECT_RATIO_OFFSET+3}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_DC_ARC_CORRECT_POINT_OFFSET+3},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUXI_CONTROL_PE_VOLT_CORRECT_RATIO_OFFSET}, 
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PE_VOLT_CORRECT_POINT_OFFSET},

};

static data_info_id_verison_t cmd_set_leak_curr_adj_para_data_pack_info[] RAM_SECTION = {
    /* 漏电流比例校正 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, LEAK_CURR_ADJ_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, LEAK_CURR_ADJ_LEN, NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_short,  ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LEAK_CURR_CORRECT_RATIO_OFFSET},
    {SEG_SYSTEM_PARA, type_short,  ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LEAK_CURR_CORRECT_POINT_OFFSET},

};
static data_info_id_verison_t cmd_set_dc_component_adj_para_data_pack_info[] RAM_SECTION = {
 /* 直流分量比例校正 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DC_COMPONENT_ADJ_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DC_COMPONENT_ADJ_LEN, NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PV_CURR_DC_COMPONENT_RATIO_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PV_CURR_DC_COMPONENT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PV_CURR_DC_COMPONENT_RATIO_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PV_CURR_DC_COMPONENT_POINT_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PV_CURR_DC_COMPONENT_RATIO_OFFSET + 2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PV_CURR_DC_COMPONENT_POINT_OFFSET + 2},
};
/* 获取生产相关参数打包命令 */
static data_info_id_verison_t cmd_pro_para_data_pack_info[] RAM_SECTION = {
    /* 生产信息写入 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PPRO_ID, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PPRO_LEN, NOT_NEED_INTERACT},

    /* 产品信息 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PRODUCT_INFO_ID, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PRODUCT_INFO_LEN, NOT_NEED_INTERACT},

    /* 硬件版本 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, POWER_HARDWARE_VERSION_ID, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, HARDWARE_VERSION_LEN, NOT_NEED_INTERACT},
};

/* 获取生产相关参数解包命令 */
static data_info_id_verison_t cmd_pro_para_data_parse_info[] RAM_SECTION = {
    /* 生产信息写入 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PPRO_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PPRO_LEN, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_RATED_POWER_FAC_PARAM},
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MAX_ACTIVE_POWER_FAC_PARAM},
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MAX_APPARENT_POWER_FAC_PARAM},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MAX_REACTIVE_POWER_FAC_PARAM},

    /* 产品信息 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PRODUCT_INFO_ID, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PRODUCT_INFO_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_string, DC_AC_PRO_MODEL_LEN, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PRODUCT_MODEL},

    /* 硬件版本 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, POWER_HARDWARE_VERSION_ID, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, HARDWARE_VERSION_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_string, DC_AC_HARDWARE_VERSION_LEN, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_POWER_HARDWARE_VERSION},
};

/* 设置生产相关参数解包命令 */
static data_info_id_verison_t cmd_set_pro_para_data_pack_info[] RAM_SECTION = {
    /* 生产信息写入 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PPRO_ID, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PPRO_LEN, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_RATED_POWER_FAC_PARAM},
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MAX_ACTIVE_POWER_FAC_PARAM},
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MAX_APPARENT_POWER_FAC_PARAM},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MAX_REACTIVE_POWER_FAC_PARAM},

    /* 产品信息 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PRODUCT_INFO_ID, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PRODUCT_INFO_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_string, DC_AC_PRO_MODEL_LEN, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PRODUCT_MODEL},

    /* 硬件版本 */
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, POWER_HARDWARE_VERSION_ID, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, HARDWARE_VERSION_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_string, DC_AC_HARDWARE_VERSION_LEN, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_POWER_HARDWARE_VERSION},
};

/*控制命令*/
static data_info_id_verison_t cmd_iv_ctrl_order_pack_info[] RAM_SECTION = {
    /*IV扫描控制命令*/
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, IV_CTRL_ORDER, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, IV_CTRL_LEN, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_CTRL_ID_PV_CTRL_ORDER},
};

static data_info_id_verison_t cmd_product_ctrl_order_pack_info[] RAM_SECTION = {
    /*生产控制命令*/
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PRODUCT_CTRL_ORDER, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PRODUCT_CTRL_LEN, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_string, DC_AC_MACH_SERIES_LEN, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MACHINE_BARCODE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,   DAC_CTRL_ID_PRODUCT},
};

static data_info_id_verison_t cmd_pv_clear_alarm_pack_info[] RAM_SECTION = {
    /*逆变器告警清除命令*/
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PV_ALARM_CLEAR_CTRL_ORDER, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PV_ALARM_CLEAR_CTRL_LEN, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_CTRL_ID_PV_CLEAR_ALARM},
};

static data_info_id_verison_t cmd_work_mode_ctrl_pack_info[] RAM_SECTION = {
    /*工作模式控制命令*/
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, WORK_MODE_CTRL_ORDER, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, WORK_MODE_CTRL_LEN, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_CTRL_ID_WORK_MODE_CTRL_CMD},
};

static data_info_id_verison_t cmd_debug_ctrl_pack_info[] RAM_SECTION = {
    /*研发调试控制命令*/
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DEBUG_CTRL_ORDER, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_START_ADDR, NOT_NEED_INTERACT},  
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, DEBUG_CTRL_LEN, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_CTRL_ID_DUBUG_CONTROL_COMMAND},
};

static cmd_parse_info_id_verison_t cmd_parse_info[] RAM_SECTION = {
    {&cmd_real_data_parse_info[0],  sizeof(cmd_real_data_parse_info)/sizeof(data_info_id_verison_t)},         // 0
    {&cmd_ver_data_parse_info[0],  sizeof(cmd_ver_data_parse_info)/sizeof(data_info_id_verison_t)},           // 1
    {&cmd_get_para_data_parse_info[0],  sizeof(cmd_get_para_data_parse_info)/sizeof(data_info_id_verison_t)}, // 2

    // 分段获取参数
    {&cmd_remote_ctrl_switch_para_data_parse_info[0],  sizeof(cmd_remote_ctrl_switch_para_data_parse_info)/sizeof(data_info_id_verison_t) },  //3
    {&cmd_power_grid_para_parse_info[0],  sizeof(cmd_power_grid_para_parse_info)/sizeof(data_info_id_verison_t) },                            //4
    {&cmd_chara_para_data_parse_info[0],  sizeof(cmd_chara_para_data_parse_info)/sizeof(data_info_id_verison_t) },                            //5
    {&cmd_mppt_para_data_parse_info[0],  sizeof(cmd_mppt_para_data_parse_info)/sizeof(data_info_id_verison_t) },                              //6
    {&cmd_lvrt_para_data_parse_info[0],  sizeof(cmd_lvrt_para_data_parse_info)/sizeof(data_info_id_verison_t) },                              //7
    {&cmd_lvrt_cc_para_data_parse_info[0],  sizeof(cmd_lvrt_cc_para_data_parse_info)/sizeof(data_info_id_verison_t) },                        //8
    {&cmd_hvrt_para_data_parse_info[0],  sizeof(cmd_hvrt_para_data_parse_info)/sizeof(data_info_id_verison_t) },                              //9
    {&cmd_pid_para_data_parse_info[0],  sizeof(cmd_pid_para_data_parse_info)/sizeof(data_info_id_verison_t) },                                //10
    {&cmd_afci_para_data_parse_info[0],  sizeof(cmd_afci_para_data_parse_info)/sizeof(data_info_id_verison_t) },                              //11
    {&cmd_power_regulation_para_data_parse_info[0],  sizeof(cmd_power_regulation_para_data_parse_info)/sizeof(data_info_id_verison_t) },      //12
    {&cmd_act_regulation_para_data_parse_info[0],  sizeof(cmd_act_regulation_para_data_parse_info)/sizeof(data_info_id_verison_t) },          //13
    {&cmd_react_regulation_para_data_parse_info[0],  sizeof(cmd_react_regulation_para_data_parse_info)/sizeof(data_info_id_verison_t) },      //14
    {&cmd_p_pn_cc_para_data_parse_info[0],  sizeof(cmd_p_pn_cc_para_data_parse_info)/sizeof(data_info_id_verison_t) },                        //15
    {&cmd_q_u_cc_para_data_parse_info[0],  sizeof(cmd_q_u_cc_para_data_parse_info)/sizeof(data_info_id_verison_t) },                          //16
    {&cmd_pf_u_cc_para_data_parse_info[0],  sizeof(cmd_pf_u_cc_para_data_parse_info)/sizeof(data_info_id_verison_t) },                        //17
    {&cmd_protect_para_data_parse_info[0],  sizeof(cmd_protect_para_data_parse_info)/sizeof(data_info_id_verison_t) },                            //18
    {&cmd_protect_l1_para_data_parse_info[0],  sizeof(cmd_protect_l1_para_data_parse_info)/sizeof(data_info_id_verison_t) },                      //19
    {&cmd_protect_l2_para_data_parse_info[0],  sizeof(cmd_protect_l2_para_data_parse_info)/sizeof(data_info_id_verison_t) },                      //20
    {&cmd_protect_l3_para_data_parse_info[0],  sizeof(cmd_protect_l3_para_data_parse_info)/sizeof(data_info_id_verison_t) },                      //21
    {&cmd_protect_l4_para_data_parse_info[0],  sizeof(cmd_protect_l4_para_data_parse_info)/sizeof(data_info_id_verison_t) },                      //22
    {&cmd_protect_l5_para_data_parse_info[0],  sizeof(cmd_protect_l5_para_data_parse_info)/sizeof(data_info_id_verison_t) },                      //23
    {&cmd_protect_l6_para_data_parse_info[0],  sizeof(cmd_protect_l6_para_data_parse_info)/sizeof(data_info_id_verison_t) },                      //24
    {&cmd_other_para_data_parse_info[0], sizeof(cmd_other_para_data_parse_info)/sizeof(data_info_id_verison_t) },                                 //25

    // 快速和普通轮询数据
    {&cmd_fast_real_data_parse_info[0],  sizeof(cmd_fast_real_data_parse_info)/sizeof(data_info_id_verison_t)},                                //26
    {&cmd_slow_real_data_parse_info[0],  sizeof(cmd_slow_real_data_parse_info)/sizeof(data_info_id_verison_t)},                                //27
    
    // 获取比例校正参数
    {&cmd_get_adj_para_data_parse_info[0],  sizeof(cmd_get_adj_para_data_parse_info)/sizeof(data_info_id_verison_t)},                          //28

    // 生产相关命令
    {&cmd_pro_para_data_parse_info[0],  sizeof(cmd_pro_para_data_parse_info)/sizeof(data_info_id_verison_t)},                                  //29

    // HVRT特征曲线
    {&cmd_hvrt_cc_para_data_parse_info[0],  sizeof(cmd_hvrt_cc_para_data_parse_info)/sizeof(data_info_id_verison_t) },                         //30
    // 组串接入检测
    {&cmd_string_in_detection_data_parse_info[0],  sizeof(cmd_string_in_detection_data_parse_info)/sizeof(data_info_id_verison_t) },           //31
    // 过欠频降额参数
    {&cmd_over_under_freq_derate_data_parse_info[0],  sizeof(cmd_over_under_freq_derate_data_parse_info)/sizeof(data_info_id_verison_t) },           //32
    {&cmd_parse_alarm_info[0],                        sizeof(cmd_parse_alarm_info)/sizeof(data_info_id_verison_t)},                    // 33
    // 研发测试相关参数
    {&cmd_rd_test_relate_data_parse_info[0], sizeof(cmd_rd_test_relate_data_parse_info)/sizeof(data_info_id_verison_t)},               //34
    // 驱动测试相关参数
    {&cmd_drive_test_relate_data_parse_info[0], sizeof(cmd_drive_test_relate_data_parse_info)/sizeof(data_info_id_verison_t)},          //35
    // ADC采样测试相关参数
    {&cmd_adc_sample_test_data_parse_info[0], sizeof(cmd_adc_sample_test_data_parse_info)/sizeof(data_info_id_verison_t)},          //36
    // 生产测试相关参数
    {&cmd_production_test_data_parse_info[0], sizeof(cmd_production_test_data_parse_info)/sizeof(data_info_id_verison_t)},          //37

    // 获取研发测试相关参数
    {&cmd_get_rd_test_relate_data_parse_info[0], sizeof(cmd_get_rd_test_relate_data_parse_info)/sizeof(data_info_id_verison_t)},               //38
    // 获取驱动测试相关参数
    {&cmd_get_drive_test_relate_data_parse_info[0], sizeof(cmd_get_drive_test_relate_data_parse_info)/sizeof(data_info_id_verison_t)},          //39
    // 获取ADC采样测试相关参数
    {&cmd_get_adc_sample_test_data_parse_info[0], sizeof(cmd_get_adc_sample_test_data_parse_info)/sizeof(data_info_id_verison_t)},          //40
    // 获取生产测试相关参数
    {&cmd_get_production_test_data_parse_info[0], sizeof(cmd_get_production_test_data_parse_info)/sizeof(data_info_id_verison_t)},          //41

};
static cmd_parse_info_id_verison_t cmd_pack_info[] RAM_SECTION = {
    {&cmd_real_data_pack_info[0],  sizeof(cmd_real_data_pack_info)/sizeof(data_info_id_verison_t)},    //0
    {&cmd_ver_data_pack_info[0],  sizeof(cmd_ver_data_pack_info)/sizeof(data_info_id_verison_t)},      //1
    {&cmd_get_para_data_pack_info[0],  sizeof(cmd_get_para_data_pack_info)/sizeof(data_info_id_verison_t)},  //2

    // 分段获取参数
    {&cmd_remote_ctrl_switch_para_data_pack_info[0],  sizeof(cmd_remote_ctrl_switch_para_data_pack_info)/sizeof(data_info_id_verison_t)},  //3
    {&cmd_power_grid_para_data_pack_info[0],  sizeof(cmd_power_grid_para_data_pack_info)/sizeof(data_info_id_verison_t),},  //4
    {&cmd_chara_para_data_pack_info[0],  sizeof(cmd_chara_para_data_pack_info)/sizeof(data_info_id_verison_t),},  //5
    {&cmd_mppt_para_data_pack_info[0],  sizeof(cmd_mppt_para_data_pack_info)/sizeof(data_info_id_verison_t), },   //6
    {&cmd_lvrt_para_data_pack_info[0],  sizeof(cmd_lvrt_para_data_pack_info)/sizeof(data_info_id_verison_t), },   //7
    {&cmd_lvrt_cc_para_data_pack_info[0],  sizeof(cmd_lvrt_cc_para_data_pack_info)/sizeof(data_info_id_verison_t), },   //8
    {&cmd_hvrt_para_data_pack_info[0],  sizeof(cmd_hvrt_para_data_pack_info)/sizeof(data_info_id_verison_t), },   //9
    {&cmd_pid_para_data_pack_info[0],  sizeof(cmd_pid_para_data_pack_info)/sizeof(data_info_id_verison_t), },     //10
    {&cmd_afci_para_data_pack_info[0],  sizeof(cmd_afci_para_data_pack_info)/sizeof(data_info_id_verison_t), },   //11
    {&cmd_power_regulation_para_data_pack_info[0],  sizeof(cmd_power_regulation_para_data_pack_info)/sizeof(data_info_id_verison_t) },   //12
    {&cmd_act_regulation_para_data_pack_info[0],  sizeof(cmd_act_regulation_para_data_pack_info)/sizeof(data_info_id_verison_t) },       //13
    {&cmd_react_regulation_para_data_pack_info[0],  sizeof(cmd_react_regulation_para_data_pack_info)/sizeof(data_info_id_verison_t) },   //14
    {&cmd_p_pn_cc_para_data_pack_info[0],  sizeof(cmd_p_pn_cc_para_data_pack_info)/sizeof(data_info_id_verison_t) },       //15
    {&cmd_q_u_cc_para_data_pack_info[0],  sizeof(cmd_q_u_cc_para_data_pack_info)/sizeof(data_info_id_verison_t) },         //16
    {&cmd_pf_u_cc_para_data_pack_info[0],  sizeof(cmd_pf_u_cc_para_data_pack_info)/sizeof(data_info_id_verison_t) },       //17
    {&cmd_protect_para_data_pack_info[0],  sizeof(cmd_protect_para_data_pack_info)/sizeof(data_info_id_verison_t) },              //18
    {&cmd_protect_l1_para_data_pack_info[0],  sizeof(cmd_protect_l1_para_data_pack_info)/sizeof(data_info_id_verison_t) },        //19
    {&cmd_protect_l2_para_data_pack_info[0],  sizeof(cmd_protect_l2_para_data_pack_info)/sizeof(data_info_id_verison_t) },        //20
    {&cmd_protect_l3_para_data_pack_info[0],  sizeof(cmd_protect_l3_para_data_pack_info)/sizeof(data_info_id_verison_t) },        //21
    {&cmd_protect_l4_para_data_pack_info[0],  sizeof(cmd_protect_l4_para_data_pack_info)/sizeof(data_info_id_verison_t) },        //22
    {&cmd_protect_l5_para_data_pack_info[0],  sizeof(cmd_protect_l5_para_data_pack_info)/sizeof(data_info_id_verison_t) },        //23
    {&cmd_protect_l6_para_data_pack_info[0],  sizeof(cmd_protect_l6_para_data_pack_info)/sizeof(data_info_id_verison_t) },        //24
    {&cmd_other_para_data_pack_info[0],  sizeof(cmd_other_para_data_pack_info)/sizeof(data_info_id_verison_t) },//25

    // 设置参数
    {&cmd_set_para_data_parse_info[0],  sizeof(cmd_set_para_data_parse_info)/sizeof(data_info_id_verison_t) },                   //26

    // 分段设置参数
    {&cmd_remote_ctrl_switch_para_data_parse_info[0],  sizeof(cmd_remote_ctrl_switch_para_data_parse_info)/sizeof(data_info_id_verison_t) },    //27
    {&cmd_power_grid_para_parse_info[0],  sizeof(cmd_power_grid_para_parse_info)/sizeof(data_info_id_verison_t) },                              //28
    {&cmd_chara_para_data_parse_info[0],  sizeof(cmd_chara_para_data_parse_info)/sizeof(data_info_id_verison_t) },                              //29
    {&cmd_mppt_para_data_parse_info[0],  sizeof(cmd_mppt_para_data_parse_info)/sizeof(data_info_id_verison_t) },                                //30
    {&cmd_lvrt_para_data_parse_info[0],  sizeof(cmd_lvrt_para_data_parse_info)/sizeof(data_info_id_verison_t) },                                //31
    {&cmd_lvrt_cc_para_data_parse_info[0],  sizeof(cmd_lvrt_cc_para_data_parse_info)/sizeof(data_info_id_verison_t) },                          //32
    {&cmd_hvrt_para_data_parse_info[0],  sizeof(cmd_hvrt_para_data_parse_info)/sizeof(data_info_id_verison_t) },                                //33
    {&cmd_pid_para_data_parse_info[0],  sizeof(cmd_pid_para_data_parse_info)/sizeof(data_info_id_verison_t) },                                  //34
    {&cmd_afci_para_data_parse_info[0],  sizeof(cmd_afci_para_data_parse_info)/sizeof(data_info_id_verison_t) },                                //35
    {&cmd_power_regulation_para_data_parse_info[0],  sizeof(cmd_power_regulation_para_data_parse_info)/sizeof(data_info_id_verison_t) },        //36
    {&cmd_act_regulation_para_data_parse_info[0],  sizeof(cmd_act_regulation_para_data_parse_info)/sizeof(data_info_id_verison_t) },            //37
    {&cmd_react_regulation_para_data_parse_info[0],  sizeof(cmd_react_regulation_para_data_parse_info)/sizeof(data_info_id_verison_t) },        //38
    {&cmd_p_pn_cc_para_data_parse_info[0],  sizeof(cmd_p_pn_cc_para_data_parse_info)/sizeof(data_info_id_verison_t) },                          //39
    {&cmd_q_u_cc_para_data_parse_info[0],  sizeof(cmd_q_u_cc_para_data_parse_info)/sizeof(data_info_id_verison_t) },                            //40
    {&cmd_pf_u_cc_para_data_parse_info[0],  sizeof(cmd_pf_u_cc_para_data_parse_info)/sizeof(data_info_id_verison_t) },                          //41
    {&cmd_protect_para_data_parse_info[0],  sizeof(cmd_protect_para_data_parse_info)/sizeof(data_info_id_verison_t) },                          //42
    {&cmd_protect_l1_para_data_parse_info[0],  sizeof(cmd_protect_l1_para_data_parse_info)/sizeof(data_info_id_verison_t) },                    //43
    {&cmd_protect_l2_para_data_parse_info[0],  sizeof(cmd_protect_l2_para_data_parse_info)/sizeof(data_info_id_verison_t) },                    //44
    {&cmd_protect_l3_para_data_parse_info[0],  sizeof(cmd_protect_l3_para_data_parse_info)/sizeof(data_info_id_verison_t) },                    //45
    {&cmd_protect_l4_para_data_parse_info[0],  sizeof(cmd_protect_l4_para_data_parse_info)/sizeof(data_info_id_verison_t) },                    //46
    {&cmd_protect_l5_para_data_parse_info[0],  sizeof(cmd_protect_l5_para_data_parse_info)/sizeof(data_info_id_verison_t) },                    //47
    {&cmd_protect_l6_para_data_parse_info[0],  sizeof(cmd_protect_l6_para_data_parse_info)/sizeof(data_info_id_verison_t) },                    //48
    {&cmd_other_para_data_parse_info[0], sizeof(cmd_other_para_data_parse_info)/sizeof(data_info_id_verison_t) },  // 49

    // 快速和普通轮询数据
    {&cmd_fast_real_data_pack_info[0],  sizeof(cmd_fast_real_data_pack_info)/sizeof(data_info_id_verison_t)},                      //50
    {&cmd_slow_real_data_pack_info[0],  sizeof(cmd_slow_real_data_pack_info)/sizeof(data_info_id_verison_t)},                      //51

    // 比例校正参数获取
    {&cmd_get_adj_para_data_pack_info[0],  sizeof(cmd_get_adj_para_data_pack_info)/sizeof(data_info_id_verison_t)},                //52

    // 生产相关命令
    {&cmd_pro_para_data_pack_info[0],  sizeof(cmd_pro_para_data_pack_info)/sizeof(data_info_id_verison_t)},                        //53
    {&cmd_set_pro_para_data_pack_info[0],  sizeof(cmd_set_pro_para_data_pack_info)/sizeof(data_info_id_verison_t)},                //54
    // 控制命令
    {&cmd_iv_ctrl_order_pack_info[0], sizeof(cmd_iv_ctrl_order_pack_info)/sizeof(data_info_id_verison_t)},                         //55
    {&cmd_product_ctrl_order_pack_info[0], sizeof(cmd_product_ctrl_order_pack_info)/sizeof(data_info_id_verison_t)},               //56
    // HVRT特征曲线
    {&cmd_hvrt_cc_para_data_pack_info[0],  sizeof(cmd_hvrt_cc_para_data_pack_info)/sizeof(data_info_id_verison_t), },         //57
    {&cmd_hvrt_cc_para_data_parse_info[0],  sizeof(cmd_hvrt_cc_para_data_parse_info)/sizeof(data_info_id_verison_t) },        //58
    // 组串接入检测
    {&cmd_string_in_detection_data_pack_info[0],  sizeof(cmd_string_in_detection_data_pack_info)/sizeof(data_info_id_verison_t), },         //59
    {&cmd_string_in_detection_data_parse_info[0],  sizeof(cmd_string_in_detection_data_parse_info)/sizeof(data_info_id_verison_t), },       //60
    // 过欠频降额参数
    {&cmd_over_under_freq_derate_data_pack_info[0],  sizeof(cmd_over_under_freq_derate_data_pack_info)/sizeof(data_info_id_verison_t), },       //61
    // 研发测试相关参数
    {&cmd_rd_test_relate_data_pack_info[0], sizeof(cmd_rd_test_relate_data_pack_info)/sizeof(data_info_id_verison_t),},                 //62
    // 逆变器告警清除命令
    {&cmd_pv_clear_alarm_pack_info[0], sizeof(cmd_pv_clear_alarm_pack_info)/sizeof(data_info_id_verison_t),},                           //63
    // 工作模式控制命令
    {&cmd_work_mode_ctrl_pack_info[0], sizeof(cmd_work_mode_ctrl_pack_info)/sizeof(data_info_id_verison_t),},                           //64
    // 研发调试控制命令
    {&cmd_debug_ctrl_pack_info[0], sizeof(cmd_debug_ctrl_pack_info)/sizeof(data_info_id_verison_t),},                           //65
    // 驱动测试相关参数
    {&cmd_drive_test_relate_data_pack_info[0], sizeof(cmd_drive_test_relate_data_pack_info)/sizeof(data_info_id_verison_t),},         //66
    // ADC采样测试相关参数
    {&cmd_adc_sample_test_data_pack_info[0], sizeof(cmd_adc_sample_test_data_pack_info)/sizeof(data_info_id_verison_t),},         //67
    // 生产测试相关参数
    {&cmd_production_test_data_pack_info[0], sizeof(cmd_production_test_data_pack_info)/sizeof(data_info_id_verison_t),},         //68
};


/* 通信协议命令表 */
static cmd_t poll_cmd_tab[] RAM_SECTION = {
    {DC_AC_GET_FAST_REAL_DATA,    CMD_POSITIVE, &cmd_req[1],  &cmd_ack[1],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[50], &cmd_parse_info[26]},
    {DC_AC_GET_SLOW_REAL_DATA,    CMD_POSITIVE, &cmd_req[1],  &cmd_ack[1],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[51], &cmd_parse_info[27]},
    {DC_AC_GET_VER_DATA,          CMD_POSITIVE, &cmd_req[4],  &cmd_ack[4],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[1], SELF_PARSE},
    {0},
};

/* 通信协议命令表 */
static cmd_t no_poll_tab[] RAM_SECTION = {
    {DC_AC_UPDATE_DATA_TRIG,        CMD_POSITIVE, &cmd_req[5],  &cmd_ack[5],  sizeof(bottom_comm_cmd_head_t), SELF_PACK, SELF_PARSE},
    {DC_AC_UPDATE_DATA,             CMD_POSITIVE, &cmd_req[6],  &cmd_ack[6],  sizeof(bottom_comm_cmd_head_t), SELF_PACK, SELF_PARSE},
    {DC_AC_UPDATE_DATA_INTERRUPT,   CMD_POSITIVE, &cmd_req[7],  &cmd_ack[7],  sizeof(bottom_comm_cmd_head_t), SELF_PACK, SELF_PARSE},
    {DC_AC_UPDATE_TRIG,             CMD_POSITIVE, &cmd_req[8],  &cmd_ack[8],  sizeof(bottom_comm_cmd_head_t), SELF_PACK, SELF_PARSE},
    // 文件上传
    {DC_AC_UPLOAD_FILE_TRIG,        CMD_POSITIVE, &cmd_req[14],  &cmd_ack[14],  sizeof(bottom_comm_cmd_head_t), SELF_PACK, SELF_PARSE},
    {DC_AC_UPLOAD_FILE_LIST,        CMD_POSITIVE, &cmd_req[15],  &cmd_ack[15],  sizeof(bottom_comm_cmd_head_t), SELF_PACK, SELF_PARSE},
    {DC_AC_UPLOAD_FILE_DATA,        CMD_POSITIVE, &cmd_req[17],  &cmd_ack[17],  sizeof(bottom_comm_cmd_head_t), SELF_PACK, SELF_PARSE},
    // 读数据
    {DC_AC_READ_DATA,                       CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, SELF_PACK,SELF_PARSE},
    {DC_AC_GET_PARA_DATA,                   CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[2], &cmd_parse_info[2]},

    // 分段获取参数
    {DC_AC_GET_REMOTE_CTRL_PARA_DATA,       CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[3], &cmd_parse_info[3]},
    {DC_AC_GET_POWER_GRID_PARA_DATA,        CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[4], &cmd_parse_info[4]},
    {DC_AC_GET_CHARA_PARA_DATA,             CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[5], &cmd_parse_info[5]},
    {DC_AC_GET_MPPT_PARA_DATA,              CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[6], &cmd_parse_info[6]},
    {DC_AC_GET_LVRT_PARA_DATA,              CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[7], &cmd_parse_info[7]},
    {DC_AC_GET_LVRT_CC_PARA_DATA,           CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[8], &cmd_parse_info[8]},
    {DC_AC_GET_HVRT_PARA_DATA,              CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[9], &cmd_parse_info[9]},
    {DC_AC_GET_PID_PARA_DATA,               CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[10], &cmd_parse_info[10]},
    {DC_AC_GET_AFCI_PARA_DATA,              CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[11], &cmd_parse_info[11]},
    {DC_AC_GET_POWER_REGULATION_PARA_DATA,  CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[12], &cmd_parse_info[12]},

    {DC_AC_GET_ACT_REGULATION_PARA_DATA,    CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[13], &cmd_parse_info[13]},
    {DC_AC_GET_REACT_REGULATION_PARA_DATA,  CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[14], &cmd_parse_info[14]},
    {DC_AC_GET_P_PN_CC_PARA_DATA,           CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[15], &cmd_parse_info[15]},
    {DC_AC_GET_Q_U_CC_PARA_DATA,            CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[16], &cmd_parse_info[16]},
    {DC_AC_GET_PF_U_CC_PARA_DATA,           CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[17], &cmd_parse_info[17]},

    {DC_AC_GET_PROTECT_PARA_DATA,           CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[18], &cmd_parse_info[18]},
    {DC_AC_GET_PROTECT_L1_PARA_DATA,        CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[19], &cmd_parse_info[19]},
    {DC_AC_GET_PROTECT_L2_PARA_DATA,        CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[20], &cmd_parse_info[20]},
    // {DC_AC_GET_PROTECT_L3_PARA_DATA,        CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[21], &cmd_parse_info[21]},
    // {DC_AC_GET_PROTECT_L4_PARA_DATA,        CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[22], &cmd_parse_info[22]},
    // {DC_AC_GET_PROTECT_L5_PARA_DATA,        CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[23], &cmd_parse_info[23]},
    // {DC_AC_GET_PROTECT_L6_PARA_DATA,        CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[24], &cmd_parse_info[24]},
    {DC_AC_GET_OTHER_PPARA_DATA,            CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[25], &cmd_parse_info[25]},
    {DC_AC_GET_HVRT_CC_PARA_DATA,           CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[57], &cmd_parse_info[30]},
    {DC_AC_GET_STRING_IN_DETECTION_DATA,    CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[59], &cmd_parse_info[31]},
    {DC_AC_GET_OVER_UNDER_FREQ_DERATE_DATA, CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[61], &cmd_parse_info[32]},
    {DC_AC_GET_RD_TEST_RELATED_DATA,        CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[62], &cmd_parse_info[38]},
    {DC_AC_GET_DRIVE_TEST_RELATED_DATA,     CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[66], &cmd_parse_info[39]},
    {DC_AC_GET_ADC_TEST_RELATED_DATA,       CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[67], &cmd_parse_info[40]},
    {DC_AC_GET_PRODUCTION_TEST_DATA,        CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[68], &cmd_parse_info[41]},


    // 分段设置参数
    {DC_AC_WRITE_DATA,                      CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL,SELF_PACK,SELF_PARSE},
    {DC_AC_SET_PARA_DATA,                   CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[26], NULL},
    {DC_AC_SET_REMOTE_CTRL_PARA_DATA,       CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[27], NULL},
    {DC_AC_SET_POWER_GRID_PARA_DATA,        CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[28], NULL},
    {DC_AC_SET_CHARA_PARA_DATA,             CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[29], NULL},
    {DC_AC_SET_MPPT_PARA_DATA,              CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[30], NULL},
    {DC_AC_SET_LVRT_PARA_DATA,              CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[31], NULL},
    {DC_AC_SET_LVRT_CC_PARA_DATA,           CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[32], NULL},
    {DC_AC_SET_HVRT_PARA_DATA,              CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[33], NULL},
    {DC_AC_SET_PID_PARA_DATA,               CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[34], NULL},
    {DC_AC_SET_AFCI_PARA_DATA,              CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[35], NULL},
    {DC_AC_SET_POWER_REGULATION_PARA_DATA,  CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[36], NULL},

    {DC_AC_SET_ACT_REGULATION_PARA_DATA,    CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[37], NULL},
    {DC_AC_SET_REACT_REGULATION_PARA_DATA,  CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[38], NULL},
    {DC_AC_SET_P_PN_CC_PARA_DATA,           CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[39], NULL},
    {DC_AC_SET_Q_U_CC_PARA_DATA,            CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[40], NULL},
    {DC_AC_SET_PF_U_CC_PARA_DATA,           CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[41], NULL},

    {DC_AC_SET_PROTECT_PARA_DATA,           CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[42], NULL},
    {DC_AC_SET_PROTECT_L1_PARA_DATA,        CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[43], NULL},
    {DC_AC_SET_PROTECT_L2_PARA_DATA,        CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[44], NULL},
    // {DC_AC_SET_PROTECT_L3_PARA_DATA,        CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[45], NULL},
    // {DC_AC_SET_PROTECT_L4_PARA_DATA,        CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[46], NULL},
    // {DC_AC_SET_PROTECT_L5_PARA_DATA,        CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[47], NULL},
    // {DC_AC_SET_PROTECT_L6_PARA_DATA,        CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[48], NULL},
    {DC_AC_SET_OTHER_PPARA_DATA,            CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[49], NULL},
    {DC_AC_SET_HVRT_CC_PARA_DATA,           CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[58], NULL},
    {DC_AC_SET_STRING_IN_DETECTION_DATA,    CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[60], NULL},
    {DC_AC_SET_OVER_UNDER_FREQ_DERATE_DATA, CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_parse_info[32], NULL},
    {DC_AC_SET_RD_TEST_RELATED_DATA,        CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_parse_info[34], NULL},
    {DC_AC_SET_DRIVE_TEST_RELATED_DATA,     CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_parse_info[35], NULL},
    {DC_AC_SET_ADC_TEST_RELATED_DATA,       CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_parse_info[36], NULL},
    {DC_AC_SET_PRODUCTION_TEST_DATA,        CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_parse_info[37], NULL},

    
    {DC_AC_GET_FAST_SLOW_REAL_DATA,         CMD_POSITIVE, &cmd_req[1],  &cmd_ack[1],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, SELF_PACK,SELF_PARSE},
    {DC_AC_GET_REAL_DATA,                   CMD_POSITIVE, &cmd_req[1],  &cmd_ack[1],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[0], &cmd_parse_info[0]},

    // 比例校正参数
    {DC_AC_GET_ADG_PARA_DATA,        CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[52], &cmd_parse_info[28]},

    // 生产相关命令
    {DC_AC_GET_PRO_PARA_DATA,        CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[53], &cmd_parse_info[29]},
    {DC_AC_SET_PRO_PARA_DATA,        CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE, NULL, NULL},
    
    //启动IV扫描
    {DC_AC_CONTROL_ORDER,            CMD_POSITIVE, &cmd_req[9], &cmd_ack[9], sizeof(bottom_comm_cmd_head_t), NULL, NULL,SELF_PACK,SELF_PARSE},
    {DC_AC_IV_CTRL_ORDER,            CMD_POSITIVE, &cmd_req[9], &cmd_ack[9], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[55], SELF_PARSE},
    {DC_AC_PRODUCT_ORDER,            CMD_POSITIVE, &cmd_req[9], &cmd_ack[9], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[56], NULL},
    // 逆变器告警清除命令
    {DC_AC_PV_ALARM_CLEAR_ORDER,     CMD_POSITIVE, &cmd_req[9], &cmd_ack[9], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[63], NULL},
    // 工作模式控制命令
    {DC_AC_WORK_MODE_CTRL_ORDER,     CMD_POSITIVE, &cmd_req[9], &cmd_ack[9], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[64], NULL},
    // 研发调试控制命令
    {DC_AC_DEBUG_CTRL_ORDER,         CMD_POSITIVE, &cmd_req[9], &cmd_ack[9], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[65], NULL},

    //读取iv数据
    {DC_AC_GET_IV_DATA,              CMD_PASSIVE, &cmd_req[10], &cmd_ack[10], sizeof(bottom_comm_cmd_head_t), SELF_PACK,SELF_PARSE},

    // 故障滤波
    {DC_AC_FAULT_RECORD_FIRST,       CMD_PASSIVE,  &cmd_req[11], &cmd_ack[11], sizeof(bottom_comm_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE, NULL, NULL},
    {DC_AC_FAULT_RECORD_DATA,        CMD_PASSIVE,  &cmd_req[12], &cmd_ack[12], sizeof(bottom_comm_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE, NULL, NULL},

    // 故障录播1数据
    {DC_AC_SET_FAULT_RECORD1_PARA,         CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, SELF_PACK, NULL},
    {DC_AC_GET_FAULT_RECORD1_PARA,         CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2], sizeof(bottom_comm_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    // 故障录播2数据
    {DC_AC_SET_FAULT_RECORD2_PARA,         CMD_POSITIVE, &cmd_req[3],  &cmd_ack[3], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, SELF_PACK, NULL},
    {DC_AC_GET_FAULT_RECORD2_PARA,         CMD_POSITIVE, &cmd_req[2],  &cmd_ack[2], sizeof(bottom_comm_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},

    // 升级状态主动上送、辅控升级帧数
    {DC_AC_POWER_UPDATE_STATUS_INFO, CMD_PASSIVE, &cmd_req[13], &cmd_ack[13], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, SELF_PARSE},

    {DC_AC_GET_ALARM_DATA,           CMD_PASSIVE, &cmd_req[1],  &cmd_ack[1],  sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL,&cmd_parse_info[33]},
    {0},
};

static dev_type_t dev_dc_ac = {
    DEV_DC_AC, DC_AC_NUM, PROTOCOL_BOTTOM_COMM, LINK_DC_AC, R_DC_AC_BUFF_LEN, S_DC_AC_BUFF_LEN, BOTTOM_DC_AC_TYPE, &no_poll_tab[0], &poll_cmd_tab[0]
};

dev_type_t* init_dev_dc_ac(void) {
    return &dev_dc_ac;
}


// 单个参数设置的设计
static cmd_parse_info_id_verison_t para_cmd_pack_info[] RAM_SECTION = {
    {&cmd_remote_ctrl_switch_para_data_parse_info[0],  sizeof(cmd_remote_ctrl_switch_para_data_parse_info)/sizeof(data_info_id_verison_t) },    //0
    {&cmd_power_grid_para_parse_info[0],  sizeof(cmd_power_grid_para_parse_info)/sizeof(data_info_id_verison_t) },                              //1
    {&cmd_chara_para_data_parse_info[0],  sizeof(cmd_chara_para_data_parse_info)/sizeof(data_info_id_verison_t) },                              //2
    {&cmd_mppt_para_data_parse_info[0],  sizeof(cmd_mppt_para_data_parse_info)/sizeof(data_info_id_verison_t) },                                //3
    {&cmd_lvrt_para_data_parse_info[0],  sizeof(cmd_lvrt_para_data_parse_info)/sizeof(data_info_id_verison_t) },                                //4
    {&cmd_lvrt_cc_para_data_parse_info[0],  sizeof(cmd_lvrt_cc_para_data_parse_info)/sizeof(data_info_id_verison_t) },                          //5
    {&cmd_hvrt_para_data_parse_info[0],  sizeof(cmd_hvrt_para_data_parse_info)/sizeof(data_info_id_verison_t) },                                //6
    {&cmd_pid_para_data_parse_info[0],  sizeof(cmd_pid_para_data_parse_info)/sizeof(data_info_id_verison_t) },                                  //7
    {&cmd_afci_para_data_parse_info[0],  sizeof(cmd_afci_para_data_parse_info)/sizeof(data_info_id_verison_t) },                                //8
    {&cmd_power_regulation_para_data_parse_info[0],  sizeof(cmd_power_regulation_para_data_parse_info)/sizeof(data_info_id_verison_t) },        //9
    {&cmd_act_regulation_para_data_parse_info[0],  sizeof(cmd_act_regulation_para_data_parse_info)/sizeof(data_info_id_verison_t) },            //10
    {&cmd_react_regulation_para_data_parse_info[0],  sizeof(cmd_react_regulation_para_data_parse_info)/sizeof(data_info_id_verison_t) },        //11
    {&cmd_p_pn_cc_para_data_parse_info[0],  sizeof(cmd_p_pn_cc_para_data_parse_info)/sizeof(data_info_id_verison_t) },                          //12
    {&cmd_q_u_cc_para_data_parse_info[0],  sizeof(cmd_q_u_cc_para_data_parse_info)/sizeof(data_info_id_verison_t) },                            //13
    {&cmd_pf_u_cc_para_data_parse_info[0],  sizeof(cmd_pf_u_cc_para_data_parse_info)/sizeof(data_info_id_verison_t) },                          //14
    {&cmd_protect_para_data_parse_info[0],  sizeof(cmd_protect_para_data_parse_info)/sizeof(data_info_id_verison_t) },                          //15
    {&cmd_protect_l1_para_data_parse_info[0],  sizeof(cmd_protect_l1_para_data_parse_info)/sizeof(data_info_id_verison_t) },                    //16
    {&cmd_protect_l2_para_data_parse_info[0],  sizeof(cmd_protect_l2_para_data_parse_info)/sizeof(data_info_id_verison_t) },                    //17
    {&cmd_protect_l3_para_data_parse_info[0],  sizeof(cmd_protect_l3_para_data_parse_info)/sizeof(data_info_id_verison_t) },                    //18
    {&cmd_protect_l4_para_data_parse_info[0],  sizeof(cmd_protect_l4_para_data_parse_info)/sizeof(data_info_id_verison_t) },                    //19
    {&cmd_protect_l5_para_data_parse_info[0],  sizeof(cmd_protect_l5_para_data_parse_info)/sizeof(data_info_id_verison_t) },                    //20
    {&cmd_protect_l6_para_data_parse_info[0],  sizeof(cmd_protect_l6_para_data_parse_info)/sizeof(data_info_id_verison_t) },                    //21
    {&cmd_other_para_data_parse_info[0], sizeof(cmd_other_para_data_parse_info)/sizeof(data_info_id_verison_t) },                               // 22    


    {&cmd_set_out_volt_adj_para_data_pack_info[0], sizeof(cmd_set_out_volt_adj_para_data_pack_info)/sizeof(data_info_id_verison_t) },           // 23  
    {&cmd_set_bus_volt_adj_para_data_pack_info[0], sizeof(cmd_set_bus_volt_adj_para_data_pack_info)/sizeof(data_info_id_verison_t) },           // 24  
    {&cmd_set_mppt1_volt_adj_para_data_pack_info[0], sizeof(cmd_set_mppt1_volt_adj_para_data_pack_info)/sizeof(data_info_id_verison_t) },       // 25 
    {&cmd_set_mppt2_volt_adj_para_data_pack_info[0], sizeof(cmd_set_mppt2_volt_adj_para_data_pack_info)/sizeof(data_info_id_verison_t) },       // 26  
    {&cmd_set_mppt3_volt_adj_para_data_pack_info[0], sizeof(cmd_set_mppt3_volt_adj_para_data_pack_info)/sizeof(data_info_id_verison_t) },       // 27 
    {&cmd_set_mppt4_volt_adj_para_data_pack_info[0], sizeof(cmd_set_mppt4_volt_adj_para_data_pack_info)/sizeof(data_info_id_verison_t) },       // 28 
    {&cmd_set_leak_curr_adj_para_data_pack_info[0], sizeof(cmd_set_leak_curr_adj_para_data_pack_info)/sizeof(data_info_id_verison_t) },         // 29        
    {&cmd_set_dc_component_adj_para_data_pack_info[0], sizeof(cmd_set_dc_component_adj_para_data_pack_info)/sizeof(data_info_id_verison_t) },   // 30
    {&cmd_set_aux_adj_para_data_pack_info[0], sizeof(cmd_set_aux_adj_para_data_pack_info)/sizeof(data_info_id_verison_t) },   // 31
    {&cmd_hvrt_cc_para_data_parse_info[0], sizeof(cmd_hvrt_cc_para_data_parse_info)/sizeof(data_info_id_verison_t) },   // 32
    {&cmd_string_in_detection_data_parse_info[0], sizeof(cmd_string_in_detection_data_parse_info)/sizeof(data_info_id_verison_t) },   // 33
    {&cmd_over_under_freq_derate_data_parse_info[0], sizeof(cmd_over_under_freq_derate_data_parse_info)/sizeof(data_info_id_verison_t) },   // 34
    {&cmd_rd_test_relate_data_parse_info[0], sizeof(cmd_rd_test_relate_data_parse_info)/sizeof(data_info_id_verison_t) },   // 35
    {&cmd_drive_test_relate_data_parse_info[0], sizeof(cmd_drive_test_relate_data_parse_info)/sizeof(data_info_id_verison_t) },   // 36
    {&cmd_adc_sample_test_data_parse_info[0], sizeof(cmd_adc_sample_test_data_parse_info)/sizeof(data_info_id_verison_t) },   // 37
    {&cmd_production_test_data_parse_info[0], sizeof(cmd_production_test_data_parse_info)/sizeof(data_info_id_verison_t) },   // 38

    // {&cmd_set_ppro_para_data_pack_info[0], sizeof(cmd_set_ppro_para_data_pack_info)/sizeof(data_info_id_verison_t) },  // 49                            
};

static para_cmd_pack_info_tab para_cmd_pack_info_tabs = 
{
    .para_cmd_pack_info = para_cmd_pack_info,
    .info_num = sizeof(para_cmd_pack_info) / sizeof(cmd_parse_info_id_verison_t),
};

para_cmd_pack_info_tab* get_para_cmd_pack_info_tabs(void)
{   
    return &para_cmd_pack_info_tabs;
}


