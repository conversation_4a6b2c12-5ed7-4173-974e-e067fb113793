#ifndef SOFTWARE_SRC_APP_USART_H_ 
#define SOFTWARE_SRC_APP_USART_H_ 

#include "common.h"

#ifdef __cplusplus
extern "C" {
#endif

void DealComm(void* parameter);
void ClearRecBuf(T_CommStruct *ptComm);
void sendCommData(T_CommStruct *ptComm);
BOOLEAN JudgeCommDisconnect(void);
BOOLEAN IsNthCommEverConnected(void);
BOOLEAN SetNthCommEverConnectedTrue(void);
BOOLEAN SetNthCommEverConnectedFalse(void);
UINT32 getBaudMode();
void setBaudMode(UINT32 baudMode);
BYTE getHandShakeCounter();
void setHandShakeCounter(BYTE handShakeCounter);
void handShakeCounterIncrement();

#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // SOFTWARE_SRC_APP_USART_H_
