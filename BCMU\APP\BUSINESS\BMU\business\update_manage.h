
#ifndef _APP_UPDATE_MANAGE_H
#define _APP_UPDATE_MANAGE_H

#ifdef __cplusplus
extern "C" {
#endif

#include "device_type.h"

#define UPDATE_FIRST_FRAME_LEN 72

#define UPDATE_FILE_NAME_LEN   40
#define UPDATE_FILE_TIME_LEN   20

#define BAUD_RATE_9600           9600

//下载部分的宏定义
#define DownloadFrameErr         0x80
#define DownFileNameErr          0x86

#define TRIGGER_COUNTER        3
#define INTERRUPT_DATA_LEN     1
#define INTERRUPT_NOT_DISCARD  0
#define INTERRUPT_DISCARD      1

#pragma pack(4)


//升级或备份文件记录数据
#define    FILE_NAME_LEN    40
#define    FILE_TIME_LEN    20
#define    FLAG_IAP         0x55    //待升级
#define    FLAG_CAN_IAP     0x56    //待升级CAN
#define    FLAG_APP         0x66    //0xAA 已烧写    //升级结束
#define    FLAG_BACKUP      0x88    //运行正常待备份
#define    FLAG_OK          0x99    //已备份

#define BMU_UPDATE_FILE_NAME          "bmu_app.bin"

#define DEVICE_ON_CHIP    "onchip_flash"
#define SPI_FALSH_DEV     "norflash0"  //spi flash设备名称

//升级或备份文件记录管理
typedef struct
{
    unsigned short    data_lenth_per_frame;
    unsigned short    total_frame_num;
    unsigned int      total_file_length;
    char     file_name[UPDATE_FILE_NAME_LEN];
    char     file_time[UPDATE_FILE_TIME_LEN];
    unsigned short    file_check;
    unsigned short    resv;
}first_frame_inf_t;

/**
 * 升级触发交互控制信息
 */
typedef struct {
    unsigned char       rtn;                                 ///<  返回码 00 正确      0x80 附加码错误 0x86 文件名错误 
    char                file_name[UPDATE_FILE_NAME_LEN];        ///<  对端下发的升级文件名称
} trig_ctr_inf_t;

 /**
 * 升级数据传输交互控制信息
 */
typedef struct {
    unsigned char       rtn;
    unsigned short      fai_counts;         ///<  数据传输命令失败计数器
    unsigned short      cur_frame_no;       ///<  期望收到的数据帧编号
    unsigned short      req_frame_no;       ///<  对端发送的数据帧编号
    unsigned short      data_offset;        ///<  文件存储偏移
    first_frame_inf_t   first_frame_inf;    ///<  首发帧信息
} trsdata_ctr_inf_t;

typedef enum {
    update_init,
    update_trans_data,
    update_interrupt
} bcmu_update_state_e;

typedef struct {
    bcmu_update_state_e update_state;            //初始态、传输中、中断状态
    unsigned short      suc_counts;              //触发成功计数
    unsigned short      fai_counts;              //触发失败计数，不需要？
    rt_base_t           di_value;                //自身di状态
    rt_base_t           do_value;                //下一个BCMU di状态
} bcmu_update_manage_t;

#pragma pack()

int update_manage_init(void);
trig_ctr_inf_t get_trig_info(void);
int south_dev_update(unsigned char dev_id, unsigned char dev_addr, unsigned char dev_num);
trsdata_ctr_inf_t get_trsdata_ctr_info(void);
int check_file_crc(void);
bcmu_update_manage_t* get_bcmu_update_manage(void);
void fill_south_update_info();
int BeginDownload(unsigned char ucMode);

#ifdef __cplusplus
}
#endif

#endif  // __APP_UPDATE_MANAGE_H

