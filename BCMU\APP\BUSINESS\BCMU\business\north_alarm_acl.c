#include "north_alarm_acl.h"
#include <stdlib.h>
#include <string.h>
#include "north_main.h"
#include "alarm_manage.h"
#include "alarm_register.h"

static alarm_protocol_t alarm_protocol[] = {
    {CLUSTER_VOLT_HIGH, ALARM_LEVEL_MINOR},
    {CLUSTER_VOLT_HIGH, ALARM_LEVEL_MAJOR},
    {CLUSTER_VOLT_HIGH, ALARM_LEVEL_CRITICAL},
    {CLUSTER_VOLT_LOW, ALARM_LEVEL_MINOR},
    {CLUSTER_VOLT_LOW, ALARM_LEVEL_MAJOR},
    {CLUSTER_VOLT_LOW, ALARM_LEVEL_CRITICAL},
    {CLUSTER_VOLT_DIFF_OVER, ALARM_LEVEL_MINOR},
    {CLUSTER_VOLT_DIFF_OVER, ALARM_LEVEL_MAJOR},
    {CLUSTER_VOLT_DIFF_OVER, ALARM_LEVEL_CRITICAL},
    {CLUSTER_CHG_HIGH_TEMP, ALARM_LEVEL_MINOR},
    {CLUSTER_CHG_HIGH_TEMP, ALARM_LEVEL_MAJOR},
    {CLUSTER_CHG_HIGH_TEMP, ALARM_LEVEL_CRITICAL},
    {CLUSTER_DISCHG_HIGH_TEMP, ALARM_LEVEL_MINOR},
    {CLUSTER_DISCHG_HIGH_TEMP, ALARM_LEVEL_MAJOR},
    {CLUSTER_DISCHG_HIGH_TEMP, ALARM_LEVEL_CRITICAL},
    {CLUSTER_CHG_LOW_TEMP, ALARM_LEVEL_MINOR},
    {CLUSTER_CHG_LOW_TEMP, ALARM_LEVEL_MAJOR},
    {CLUSTER_CHG_LOW_TEMP, ALARM_LEVEL_CRITICAL},
    {CLUSTER_DISCHG_LOW_TEMP, ALARM_LEVEL_MINOR},
    {CLUSTER_DISCHG_LOW_TEMP, ALARM_LEVEL_MAJOR},
    {CLUSTER_DISCHG_LOW_TEMP, ALARM_LEVEL_CRITICAL},
    {CLUSTER_OVER_TEMP_DIFF, ALARM_LEVEL_MINOR},
    {CLUSTER_OVER_TEMP_DIFF, ALARM_LEVEL_MAJOR},
    {CLUSTER_OVER_TEMP_DIFF, ALARM_LEVEL_CRITICAL},
    {CLUSTER_CAPACITOR_SOFT_VOLT_HIGH, ALARM_LEVEL_MINOR},
    {CLUSTER_CAPACITOR_HARD_VOLT_HIGH, ALARM_LEVEL_MINOR},
    {CLUSTER_CAPACITOR_VOLT_LOW, ALARM_LEVEL_MINOR},
    {CLUSTER_BUSBAR_SOFT_VOLT_HIGH, ALARM_LEVEL_MINOR},
    {CLUSTER_BUSBAR_HARD_VOLT_HIGH, ALARM_LEVEL_MINOR},
    {CLUSTER_BUSBAR_VOLT_LOW, ALARM_LEVEL_MINOR},
    {CLUSTER_BUSBAR_VOLT_OVER_RANGE, ALARM_LEVEL_MINOR},
    {CLUSTER_COMPEN_CAPACITOR_SOFT_VOLT_HIGH, ALARM_LEVEL_MINOR},
    {CLUSTER_COMPEN_CAPACITOR_HARD_VOLT_HIGH, ALARM_LEVEL_MINOR},
    {CLUSTER_CLLC_VOLT_HIGH, ALARM_LEVEL_MINOR},
    {CLUSTER_CLLC_VOLT_LOW, ALARM_LEVEL_MINOR},
    {CLUSTER_CONNECT_BAD, ALARM_LEVEL_MINOR},
    {CLUSTER_CHG_CURR_HIGH, ALARM_LEVEL_MINOR},
    {CLUSTER_DISCHG_CURR_HIGH, ALARM_LEVEL_MINOR},
    {CLUSTER_CURR_BALENCE_BAD, ALARM_LEVEL_MINOR},
    {CLUSTER_CHG_CIRCUIT_DAMAGE, ALARM_LEVEL_MINOR},
    {CLUSTER_DISCHG_CIRCUIT_DAMAGE, ALARM_LEVEL_MINOR},
    {CLUSTER_LIMIT_CURR_CIRCUIT_DAMAGE, ALARM_LEVEL_MINOR},
    {CLUSTER_OUTPUT_OVERLOAD, ALARM_LEVEL_MINOR},
    {CLUSTER_BUS_SHORT_CIRCUIT, ALARM_LEVEL_MINOR},
    {CLUSTER_FAN_FAULT, ALARM_LEVEL_MINOR},
    {CLUSTER_EEPROM_FAULT, ALARM_LEVEL_MINOR},
    {CLUSTER_CAN_COMM_FAULT, ALARM_LEVEL_MINOR},
    {CLUSTER_ENV_TEMP_HIGH, ALARM_LEVEL_MINOR},
    {CLUSTER_BOARD_INTER_TEMP_HIGH_PROTEC, ALARM_LEVEL_MINOR},
    {CLUSTER_SLOW_START_ERROR, ALARM_LEVEL_MINOR},
    {CLUSTER_POWER_CONNEC_SELF_CHECK_RESIS_ABNORMAL, ALARM_LEVEL_MINOR},
    {CLUSTER_RELAY_PULL_IN_EXCEP_PROTEC, ALARM_LEVEL_MINOR},
    {CLUSTER_EXTERNAL_BAT_VOL_HIGH, ALARM_LEVEL_MINOR},
    {CLUSTER_EXTERNAL_BAT_VOL_LOW, ALARM_LEVEL_MINOR},
    {CLUSTER_HIGH_VOLT_DIFF_BETWEEN_BUSBAR_AND_BAT, ALARM_LEVEL_MINOR},
    {CLUSTER_SOC_LOW, ALARM_LEVEL_MINOR},
    {CLUSTER_SOC_LOW_PROTEC, ALARM_LEVEL_MINOR},
    {CLUSTER_SOH_LOW, ALARM_LEVEL_MINOR},
    {CLUSTER_SOH_LOW_PROTEC, ALARM_LEVEL_MINOR},
    {CLUSTER_ENV_TEMP_HIGH_PROTEC, ALARM_LEVEL_MINOR},
    {CLUSTER_ENV_TEMP_LOW_PROTEC, ALARM_LEVEL_MINOR},
    {BMU_CELL_DAMAGE_PROTEC, ALARM_LEVEL_MINOR},
    {BMU_CELL_TEMP_ABNORMAL, ALARM_LEVEL_MINOR},
    {BMU_CELL_TEMP_INVALID, ALARM_LEVEL_MINOR},
    {BMU_CELL_VOLT_INVALID, ALARM_LEVEL_MINOR},
    {BMU_BALENCE_CIRCUIT_FAULT, ALARM_LEVEL_MINOR},
    {BMU_COMM_FAIL, ALARM_LEVEL_MINOR},
    {CLUSTER_INSULATION_RESIS_ALM, ALARM_LEVEL_MINOR},
    {CLUSTER_INSULATION_RESIS_ALM, ALARM_LEVEL_MAJOR},
    {CLUSTER_INSULATION_RESIS_ALM, ALARM_LEVEL_CRITICAL},
    {CLUSTER_CARBON_MONOXIDE_ALM, ALARM_LEVEL_MINOR},
    {CLUSTER_CARBON_MONOXIDE_ALM, ALARM_LEVEL_MAJOR},
    {CLUSTER_CARBON_MONOXIDE_ALM, ALARM_LEVEL_CRITICAL},
};

/**
 * @brief 获取实时告警 
 * @param[in] bsmu_data 设备数据
 * @retval  
 * @note 
 */
void get_real_alm(bsmu_data_t* bsmu_data) {
    int i = 0;
    int j = 0;
    int alm_id = 0;
    char alm_value = 0;
    unsigned char dev_addr = 0;
    unsigned char* buff = NULL;

    RETURN_IF_FAIL(bsmu_data != NULL);
    buff = malloc(BATT_CLUSTER_NUM *sizeof(BSMU_param_alm_level_t));
    RETURN_IF_FAIL(buff != NULL);
    memset(buff, 0x00, BATT_CLUSTER_NUM *sizeof(BSMU_param_alm_level_t));

    for(i = 0; i < BATT_CLUSTER_NUM; i++) {
        for(j = 0; j < sizeof(alarm_protocol) / sizeof(alarm_protocol_t); j++) {
            dev_addr = i + 1;
            alm_id = GET_ALM_ID(alarm_protocol[j].alm_code, dev_addr, alarm_protocol[j].alm_level);
            alm_value = get_realtime_alarm_value(alm_id);
            buff[i * BATT_CLUSTER_NUM + j] = alm_value;
        }
    }
    memcpy(&bsmu_data->pack_real_alm, buff, BATT_CLUSTER_NUM * sizeof(BSMU_param_alm_level_t));
    free(buff);
    return;
 }
