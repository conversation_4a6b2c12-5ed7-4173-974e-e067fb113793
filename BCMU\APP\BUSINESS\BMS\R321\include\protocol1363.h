/**************************************************************************
* Copyright (C) 2001, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：ZXDU18 CTL板软件
* 文件名称：protocol1363.h
* 文件说明：协议模块头文件
* 作    者：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/
#ifndef SOFTWARE_SRC_APP_PROTOCOL1363_H_
#define SOFTWARE_SRC_APP_PROTOCOL1363_H_

#include "para.h"

#ifdef __cplusplus
extern "C"
{
#endif

/***********************   CID2命令码   ********************/
#define   GET_ANALOG_FLOAT         0x41        //获取模拟量浮点数据
#define   GET_ANALOG_INT           0x42        //获取模拟量定点数据
#define   GET_SWITCH               0x43        //获取开关输入状态
#define   GET_ALARM                0x44        //获取告警状态
#define   REMOTE_CTRL              0x45        //遥控(输出干接点控制)  
#define   GET_PARA_FLOAT           0x46        //获取浮点参数
#define   GET_PARA_INT             0x47        //获取定点参数
#define   SET_PARA_FLOAT           0x48        //设置浮点参数
#define   SET_PARA_INT             0x49        //设置定点参数

#define   GET_SPECIAL_PARA         0x80        //获取特定参数
#define   SET_SPECIAL_PARA         0x81        //设置特定参数
#define   GET_BMS_FAC_INFO         0x82        //获取BMS厂家信息
#define   GET_BMS_ALM_LVL          0x83        //获取BMS告警级别
#define   SET_BMS_ALM_LVL          0x84        //设置BMS告警级别
#define   GET_BMS_ALM_RLY          0x85        //获取BMS告警对应干接点
#define   SET_BMS_ALM_RLY          0x86        //设置BMS告警对应干接点
#define   GET_BMS_HISDATA          0x4B        //获取BMS历史数据
#define   GET_BMS_HISALM           0x4C        //获取BMS历史告警
#define   GET_BMS_HISOPERATION     0x87        //获取BMS操作记录
#define   GET_BDU_FAC_INFO         0x88        //获取Bdu厂家信息
#define   GET_BMS_FAC_SPEC_INFO    0x89        //获取BMS特定厂家信息
#define   SET_BMS_FAC_SPEC_INFO    0x8A        //设置BMS特定厂家信息
#define   GET_BMS_DEBUG_INFO       0xEE        //获取BMS调测信息  
#define   GET_BMS_CELL_FACT        0x8C        //获取电芯厂家信息  
#define   SET_BMS_CELL_FACT        0x8D        //设置电芯厂家信息
#define   GET_BMS_ANALOG_NEW       0x90        //获取新增模拟量数据
#define   GET_BMS_PARA_NEW		   0x91        //获取新增参数
#define   SET_BMS_PARA_NEW         0x92        //设置新增参数
#define   SET_BMS_SPEPARA_NEW      0x93        //新增设置特殊参数
#define   GET_BMS_ALM_NEW          0x94        //获取新告警量
#define   GET_BMS_ALM_LVL_NEW      0x95        //获取新告警级别
#define   SET_BMS_ALM_LVL_NEW      0x96        //设置新告警级别
#define   GET_BMS_ALM_RLY_NEW      0x97        //获取新告警干接点
#define   SET_BMS_ALM_RLY_NEW      0x98        //设置新告警干接点
#define   GET_BMS_FACT_INFO_NEW    0x99        //获取新增BMS厂家信息
#define   GET_BMS_NEW_ALM_PARA	   0x9A        //获取新增告警参数
#define   SET_BMS_NEW_ALM_PARA	   0x9B        //设置新增告警参数
#define   GET_BMS_MAX_SOC          0x9C        //获取组内最大SOC
#define   REMOTE_SWAP_BAUD         0x9D        //波特率切换   
#define   REMOTE_HANDSHAKE         0x9F        //波特率握手
#define   GET_BMS_HISDATA_NEW	   0x9E        //获取BMS历史数据（new）
#define   GET_BMS_HISALM_NEW       0xA0        //获取BMS历史告警（new）
#define   GET_BMS_HISOPERATION_NEW 0xA1        //获取BMS操作记录（new）
#define   REMOTE_CTRL_NEW          0xA2        //遥控(新指令)
#define   GET_CUSTOMER_NAME        0xA3        //获取客户名称
#define   GET_SWITCH_NEW           0xA7        //获取开关量(新指令)
#define   GET_BMS_SPECIAL_PARA_NEW 0xA8        //获取新增参数（新指令）
#define   SET_BMS_SPECIAL_PARA_NEW 0xA9        //设置新增参数（新指令）
#define   GET_ADDRESS_INFO         0xAA        //获取BDU内存地址信息
#define   SET_ADDRESS_INFO         0xAB        //设定BDU内存地址信息
#define   GET_BMS_EXTREME_VALUE    0xAC        //获取极值记录（新增）
#define   GET_BMS_STATISTICS_NEW   0xAD        //获取电芯统计记录（新增）
#define   GET_BMS_NET_NEW          0xAE        //获取网络相关参数
#define   SET_BMS_NET_NEW          0xAF        //设置网络相关参数
#define   GET_BMS_HISDATA_TIME     0xB0        //获取BMS历史数据（支持时间段）
#define   GET_BMS_HISALM_TIME      0xB1        //获取BMS历史告警（支持时间段）
#define   GET_BMS_HISOPERA_TIME    0xB2        //获取BMS操作记录（支持时间段)
#define   GET_DCR_DATA_TIME        0xB4        //获取直流内阻记录(支持时间段)
#define   GET_DCR_INFO             0xB5        //获取直流内阻信息
#define   GET_BMS_BALANCE_CAPACITY 0XB6        //获取均衡容量记录
#define   GET_BAL_CAPAYITY_INFO    0XB7        //获取均衡容量信息

#define   GET_DATE_TEMPLATE        0xB8         //获取智能错峰日模板数据
#define   SET_DATE_TEMPLATE        0xB9         //设置智能错峰日模板数据
#define   GET_PEAK_SHIFT_PARA      0xBA         //获取智能错峰参数
#define   SET_PEAK_SHIFT_PARA      0xBB         //设置智能错峰参数
#define   GET_FM_PEAK_STATUS       0xBC         //获取调频错峰状态
#define   GET_ALARM_R321           0xBD         //获取告警量(R321新增)
#define   SITE_ANTITHEFT_CTRL      0xBE         //站点防盗遥控指令
#define   GET_REAL_DATA_1363       0xBF         //获取实时数据(R321新增)
#define   GET_ALARM_LEVEL_NEW      0xC0         //获取告警等级
#define   SET_ALARM_LEVEL_NEW      0xC1         //设置告警等级
#define   GET_ALARM_RLY_NEW        0xC2         //获取告警量干接点
#define   SET_ALARM_RLY_NEW        0xC3         //设置告警量干接点
#define   NET_ANTITHEFT_CTRL       0xC4         //网管防盗遥控指令
#define   NET_ANTITHEFTKEY_CHANGE  0xC5         //更换网管防盗电子钥匙
#define   GET_NET_ANTITHEFTKEY     0xC6         //获取网管防盗电子钥匙
#define   GET_GPS_INIT_INIO        0xC9         //获取初始经纬度信息
#define   SET_GPS_INIT_INIO        0xCA         //设置初始经纬度信息
#define   RECV_FM_INFO_RESPONSE    0xFD         //调频错峰状态主动上送响应
#define   GET_FACTORY_INFORMATION_EXTEND   0xC7        //获取厂家扩展信息
#define   NET_PEAKSHIFTTEMPLATE_FORWARD 0xCB    //网络错峰模板转发指令

#ifdef ENABLE_SYS_MONITOR
#define   GET_ELABORATE_MEMORY_MANAGEMENT  0xC8        //获取内存精细化管理信息
#endif

//特殊命令内部使用
#define   REGET_BMS_HISALM         0xE0        //重新获取历史告警
#define   REGET_BMS_HISDATA        0xE1        //重新获取历史数据
#define   REGET_BMS_HISOPER        0xE2        //重新获取操作记录
#define   GET_BMS_INTERNAL_PARA    0xE3        //获取内部测试参数      
#define   SET_BMS_INTERNAL_PARA    0xE4        //设置内部测试参数

#ifdef SHUTDOWNSLEEP
#define   CTRL_BDU_SLEEP_START     0x52        //控制BDU休眠启动 
#define   CTRL_BDU_SLEEP_STOP      0x53        //控制BDU休眠停止
#define   CTRL_BMS_SHUTDOWN        0x54        //控制BMS关机
#endif

#define   CLEAR_ZERO_POINT_CALIB_PARA  0x55    // 清除零点参数

#define   GET_TIME                 0x4D        //获取时间
#define   SET_TIME                 0x4E        //设置时间
#define   GET_PROTOCOL_VER         0x4F        //获取协议版本号
#define   GET_ADDR                 0x50        //获取设备地址
#define   GET_FACTORY_INFO         0x51        //获取厂家信息
#define   SET_ADDR_DEFINED         0xE0        //设置设备地址
#define   AUTO_SEND_ALARM          0xE8
//中试增加的命令
#define   GET_GUI_RESULT           0xD0        //获取按键测试信息
#define   GET_MAC                  0xD2        //获取MAC地址
#define   SET_MAC                  0xD3        //设置MAC地址
#define   START_NET_TEST           0xD4        //网络测试
#define   SET_DOWNLOAD_PROTECTTEST 0xD8        //下电保护电路测试 
#define   GET_CSUTEST_VERSION_ID   0xDD        //读取csuTest软件版本
#define   GET_PLATFORM_VERSION_ID  0xDE        //读取软件平台版本
#define   SET_SYSTEM_RESET_ID      0xDF        //软件复位CSU

//apptest新增
#define   SET_CELL_FACT_INFO       0xF0        //设置电芯厂家信息
#define   GET_SLEEP_TEST           0xED        //获取休眠测试结果

////
#define   P2P_TRIGGER              0xFF        //点对点触发命令
#define   OUTTEST_TRIGGER          0x10        //外协模式触发命令
#define   OUTTEST_EXIT             0x11        //外协模式退出命令

#define   SET_INPUT_RELAY          0x8E
#define   GET_INPUT_RELAY          0x8F

//设置地址回包中,上送给后台的扩展字节,用于后台区分不同的协议版本
#define   EXTENDED_ITEM_NUM        3
#define   TYPE_DEVICE              1
#define   TYPE_FOREGROUND          2
#define   TYPE_BACKGROUND          3

//协议兼容引入的宏定义
#define   TYPE_END                 0
#define   TYPE_BYTE                1
#define   TYPE_WORD                2
#define   TYPE_FLOAT               4
#define   TYPE_SCHAR               5
#define   TYPE_UCHAR               6
#define   TYPE_SPEC                7
#define   TYPE_INT                 8          //yyh 引入该类型，用于兼容1.01协议版本
#define   TYPE_INT_SPEC            9          //yyh 引入该类型，用于兼容1.01协议版本
#define   BRODCAST_ADR	           0

//yyh协议类型定义
#define   T1363_PROTOCOL           0
#define   T1104_PROTOCOL           1
#define   V101_PROTOCOL            2
#define   DATA_TYPE_FLOAT          4
#define   DATA_TYPE_INT            2
#define   DATA_TYPE_BYTE           1

#define   LOAD1_OFF_V101           0xE7
//#define LOAD2_OFF_V101           0xE8
#define   LOAD_BATT_V101           0xEA
#define   BATT_TEST_FAIL_V101      0xE5
#define   BATT_DISCH_V101          0xE4
#define   BATT_FAULT_V101          0xED
#define   BATT_CURR_DET_ERROR_V101 0xE2
#define   TEMP_VOID_V101           0xE3

#define   SMR_NUM_DTCL             4

#define   REMOTE_RESET                    0x10//BMS复位
#define   REQUEST_CHARGE_CONTROLLED       0x11//请求启动充电；仅受控模式用CSU控制下发
#define   REQUEST_DISCH_CONTROLLED        0x12//请求启动放电；仅受控模式用CSU控制下发
#define   SET_DISCH_OUTVOLT               0x13//控制放电输出电压(放电目标电压)；仅受控模式用CSU控制下发
#define   RESTORE_DEFAULT_PARA            0x14//恢复缺省参数；后台支持
#define   SET_CHARGE_CURR_COEF            0x15//控制充电电流系数；受控模式用CSU控制下发
#define   SET_CHARGE_PRIOR                0x16//控制充电优先使能；仅受控模式用CSU控制下发
#define   RESET_ADD_COMPETE               0x17//控制竞争地址；后台支持
#define   SET_DISCHARGE_CURR              0x18//下发混用电池电流；仅非受控模式用CSU控制下发
#define   SET_CLEAR_LOCKSTAT              0x19//解除锁定
#define   REQUEST_CHARGE_FERR             0x1A//下发来电状态；仅非受控模式用CSU控制下发
#define   REQUEST_DISCH_FREE              0x1B//下发停电状态；仅非受控模式用CSU控制下发
#define   SET_MAX_SOC                     0x1C//多组下发组间最大SOC
#define   SET_DISCH_VOLT_FREE             0x1D//下发放电输出电压；非受控模式下下发放电输出电压
#define   SET_BATT_MANUAL_UNLOCK          0x1E//控制下发电池手动解锁丢失告警
#define   SET_HIGH_BAUDRATE               0x20//波特率握手命令
#define   GET_GPS_LOCATION_TO_PHONE       0x21//遥控命令：获取GPS位置到手机短信
#define   SET_BDU_EMC_TEST_STATUS         0x22//遥控命令：控制BDU进入/退出EMC测试状态
#define   MANUAL_CTRL_ENTER_DEFENCE_STATE 0x23//手动布防
#define   BATT_IN_PLACE_FLAG              0x24//电池指示
#define   SWITCH_BATT_ADDR                0x25//地址切换
#define   SET_HEATING_START               0x26//启动加热
#define   SET_HEATING_STOP                0x27//停止加热
#define   CLEAR_DCR_FAULT_PRT             0x28//清除直流内阻异常保护
#define   RESET_CAN2_ADD_COMPETE          0x2A//控制柜间地址竞争
#define   ENTER_FM_MODE                   0x2B//进入调频模式
#define   CONTROL_FM_POWER                0x2C//下发调频功率
#define   EXIT_FM_MODE                    0x2D//退出调频模式
#define   CLEAR_4G_TRAFFIC                0x2E//清除流量记录
#define   SWITCH_CAN2_ADDR                0x2F//柜间地址切换
#define   SET_CHARGE_CURR_COEF_FREE       0x31//控制充电电流系数，精度为3，非受控模式CSU控制下发

#define   MANUAL_CANCEL_DEVICE_DEFENCE    0x1F//人工撤防

#define   POWER_STAT_INVALID       0x7F

#define CTRL_ENET_PORT_ENABLE      0xE1     // 内部调试功能：开启串口映射网口功能
#define CTRL_ENET_PORT_DISABLE     0xE2     // 内部调试功能：关闭串口映射网口功能

/*****************   历史记录 Command Type   ***************/
#define GET_RECORD_TIME_SLOT 0x00   // 获取时间段的历史记录
#define GET_RECORD_CORRECT   0x01   // 收到历史记录正确，上送下一条历史记录
#define GET_RECORD_WRONG     0x02   // 收到历史记录错误，重发上一条历史记录
#define GET_RECORD_COMPLETE  0x03   // 接受完所有的历史记录
#define GET_RECORD_NUM       0x04   // 获取特定时间段历史记录条数

/********************   历史记录数据长度    *****************/
#define HIS_RECORD_LEN_TYPE_ID          6    // 01H 02H 03H: 2 * (COMMAND_GROUP + CommandType + CommandId) = 6
#define HIS_RECORD_LEN_TYPE_TIME        32   // 04H: 2 * (COMMAND_GROUP + CommandType + CommandTime) = 32
#define HIS_RECORD_LEN_TYPE_ID_TIME     34   // 00H: 2 * (COMMAND_GROUP + CommandType + CommandId + CommandTime) = 34

/**********************   特殊字节位置    *******************/
#define COMMAND_TYPE  8      // CommandType字节位置
#define COMMAND_ID    9      // CommandId字节位置(历史记录中使用)
#define COMMAND_TIME  10     // COMMAND_TIME字节位置(历史记录中使用)

/*********************  函数原型定义  **********************/

//void  ReadBootVerDate( BYTE *p );
T_SysPara* GetSysParaPointer_1363( void );

#ifdef QUICK_RESPONSE_ENABLED
BOOLEAN ShouldSaveSysParaBy1363(void);
void SetSaveSysParaBy1363Flag(BOOLEAN flag);
BOOLEAN ExecuteSaveSysParaBy1363(void);
#endif /* QUICK_RESPONSE_ENABLED */

#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // SOFTWARE_SRC_APP_PROTOCOL_H_;
