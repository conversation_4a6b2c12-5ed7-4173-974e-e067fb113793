#include <stdio.h>
#include <string.h>
#include "cmd.h"
#include "sps.h"
#include "protocol_layer.h"
#include "dev_60x.h"


/* 设备缓冲区长度 */
#define R_BUFF_LEN                    512       ///<  接收缓冲区长度
#define S_BUFF_LEN                    512      ///<  发送缓冲区长度

//  协议默认信息
#define     IDDB_DEVICE          0x28
#define     IDDB_ADDR            0x01
#define     DEST_DEVICE          0x7D        //CSU设备类型
#define     DEST_ADDR            1           //CSU设备地址
#define     BROADCAST_ADDR       0x00
#define     SHEET_SN             0x01
#define     CTRL_VALUE           0x80

//  应用层返回码
#define     APP_RTN_TYPE            0x01    ///<  返回码标志，用于区分是应用层还是链路层
#define     APP_RTN_OK              0x00    ///<  数据正常
#define     APP_RTN_FUNC_ERR        0x01    ///<  功能码错误
#define     APP_RTN_CRC_ERR         0x08    ///<  crc错误
#define     APP_RTN_SHEET_SN_ERR    0x0A    ///<  表序号错误

//  功能码
#define     CMD_SET_PARA            0X30    ///<  设置DO状态
#define     CMD_SET_FAN             0X31    ///<  设置风扇状态（PWM占空比信号）



//  附加码
#define     CMD_APPEND_GET_AI_DATA          0x0001    ///<  获取AI数据
#define     CMD_APPEND_GET_DI_DATA          0x0002    ///<  获取DI数据
#define     CMD_APPEND_GET_BARCODE          0x0004    ///<  获取子板条形码
#define     CMD_APPEND_CTRL_DO              0x0000    ///<  do控制
#define     CMD_APPEND_SET_BARCODE          0x0003    ///<  设置子板条形码
#define     CMD_APPEND_SET_CSU_FAULT_DO     0x0000    ///<  设置csu异常DO状态
#define     CMD_APPEND_SET_FAN_PWM          0x0001    ///<  设置风扇PWM占空比



/* 命令请求头 */
static bottom_comm_cmd_head_t cmd_req[] = {
    // 通信命令
    {BOTTOM_PROTO_TYPE_COMM, CMD_LINK,          CMD_APPEND_NONE,            BOTTOM_RTN_APP_ACK},
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA, CMD_APPEND_GET_AI_DATA,      BOTTOM_RTN_APP_ACK},
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA, CMD_APPEND_GET_DI_DATA,      BOTTOM_RTN_APP_ACK},
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA, CMD_APPEND_GET_BARCODE,      BOTTOM_RTN_APP_ACK},
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL,      CMD_APPEND_CTRL_DO,         BOTTOM_RTN_APP_ACK},
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL,      CMD_APPEND_SET_BARCODE,     BOTTOM_RTN_APP_ACK},
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_PARA,      CMD_APPEND_SET_CSU_FAULT_DO, BOTTOM_RTN_APP_ACK},
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_FAN,       CMD_APPEND_SET_FAN_PWM,     BOTTOM_RTN_APP_ACK},
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_DATA_OLD, CMD_APPEND_NONE, BOTTOM_RTN_APP_ACK},
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_TRIG_OLD, CMD_APPEND_UPDATE_REQ, BOTTOM_RTN_APP_ACK},
};

/* 命令应答头 */
static bottom_comm_cmd_head_t cmd_ack[] = {
    // 通信命令
    {BOTTOM_PROTO_TYPE_COMM, CMD_LINK,          CMD_APPEND_NONE,            BOTTOM_RTN_APP_CORRECT},
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA, CMD_APPEND_GET_AI_DATA,     BOTTOM_RTN_APP_CORRECT},
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA, CMD_APPEND_GET_DI_DATA,     BOTTOM_RTN_APP_CORRECT},
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA, CMD_APPEND_GET_BARCODE,     BOTTOM_RTN_APP_CORRECT},
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL,      CMD_APPEND_CTRL_DO,         BOTTOM_RTN_APP_CORRECT},
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL,      CMD_APPEND_SET_BARCODE,     BOTTOM_RTN_APP_CORRECT},
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_PARA,      CMD_APPEND_SET_CSU_FAULT_DO, BOTTOM_RTN_APP_CORRECT},
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_FAN,       CMD_APPEND_SET_FAN_PWM,     BOTTOM_RTN_APP_CORRECT},
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_DATA_OLD, CMD_APPEND_NONE, BOTTOM_RTN_APP_CORRECT},
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_TRIG_OLD, CMD_APPEND_UPDATE_ACK, BOTTOM_RTN_APP_CORRECT},
};


/* 通信协议命令表 */
static cmd_t no_poll_cmd_tab[] = {
    {UIB_LINK,      CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(bottom_comm_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {UIB_GET_AI,    CMD_PASSIVE, &cmd_req[1], &cmd_ack[1], sizeof(bottom_comm_cmd_head_t), NULL, NULL,SELF_PACK, SELF_PARSE},
    {UIB_GET_DI,    CMD_PASSIVE, &cmd_req[2], &cmd_ack[2], sizeof(bottom_comm_cmd_head_t), NULL, NULL,SELF_PACK, SELF_PARSE},
    {UIB_GET_BARCODE, CMD_PASSIVE, &cmd_req[3], &cmd_ack[3], sizeof(bottom_comm_cmd_head_t), NULL, NULL,SELF_PACK, SELF_PARSE},
    {UIB_DO_CTRL,   CMD_PASSIVE, &cmd_req[4], &cmd_ack[4], sizeof(bottom_comm_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {UIB_SET_BARCODE, CMD_PASSIVE, &cmd_req[5], &cmd_ack[5], sizeof(bottom_comm_cmd_head_t), NULL, NULL,SELF_PACK, SELF_PARSE},
    {UIB_SET_CSU_FAULT_DO,CMD_PASSIVE, &cmd_req[6], &cmd_ack[6], sizeof(bottom_comm_cmd_head_t), NULL, NULL,SELF_PACK, SELF_PARSE},
    {UIB_SET_FAN,   CMD_PASSIVE, &cmd_req[7], &cmd_ack[7], sizeof(bottom_comm_cmd_head_t), NULL, NULL,SELF_PACK, SELF_PARSE},
    {UIB_UPDATE_DATA,  CMD_BROADCAST|CMD_PASSIVE,  &cmd_req[8], &cmd_ack[8], sizeof(bottom_comm_cmd_head_t), },
    {UIB_UPDATE_TRIG,  CMD_PASSIVE,  &cmd_req[9], &cmd_ack[9], sizeof(bottom_comm_cmd_head_t), },
    {0},
};

static dev_type_t dev_60x = {
    DEV_CSU, 1, PROTOCOL_BOTTOM_COMM, LINK_604A_MAIN, R_BUFF_LEN, S_BUFF_LEN, BOTTOM_CSU_TYPE, no_poll_cmd_tab,
};

dev_type_t* init_dev_604a_main(void){
    return &dev_60x;
}

dev_type_t* init_dev_604a_uib01(void) {
    dev_60x.link_inst_id = LINK_604A_UIB01;
    return &dev_60x;
}
