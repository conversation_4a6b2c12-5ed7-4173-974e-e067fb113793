#include <string.h>
#include <stdlib.h>
#include <rtthread.h>
#include "protocol_bottom_comm.h"
#include "utils_thread.h"
#include "device_type.h"
#include "msg.h"
#include "cmd.h"
#include "north_main.h"
#include "data_type.h"
#include "sps.h"
#include "pin_define.h"
#include "sample.h"
#include "pin_ctrl.h"
#include "pwm_ctrl.h"
#include "utils_server.h"
#include "utils_data_transmission.h"
#include "storage.h"
#include "utils_string.h"
#include "utils_rtthread_security_func.h"
#include "realdata_save.h"
#include "realdata_id_in.h"
#include "para_id_in.h"
#include "para_manage.h"
#include "control_manage.h"
#include "device_common.h"
#include "eeprom_storage.h"
#include "led.h"
#include "airswitch_alarm.h"
#include "main.h"

/* 校准类型定义 */
#define CALIBRATION_CURRENT             0   //电流校准
#define CALIBRATION_BUS_VOLTAGE         1   //母排电压校准
#define CALIBRATION_TERMINAL_VOLTAGE    2   //接线端电压校准

void* init_north(void * param);
void north_comm_th(void *param);
Static void ctrl_P2P_off(void *parameter);
Static int process_ctrl_cmd(void* dev_inst, void* cmd_buf);
Static char power_down_command_handle(cmd_buf_t* cmd_buf);
Static char power_on_command_handle(cmd_buf_t* cmd_buf);
Static char airswitch_ctrl_P2P_on_cmd(cmd_buf_t* cmd_buf);
Static int airswitch_ctrl_P2P_off_cmd(cmd_buf_t* cmd_buf);
Static int deal_apptest_trigger_exit(void* dev_inst, void* cmd_buf);
Static char airswitch_reset_handle(void);
Static int deal_apptest_cmd(void* dev_inst, void* cmd_buf);
Static int airswitch_link(void* dev_inst, void* cmd_buf);
Static int airswitch_get_realdata(void* dev_inst, void* cmd_buf);
Static int airswitch_get_table_1(void* dev_inst, void* cmd_buf);
Static int airswitch_set_table_1(void* dev_inst, void* cmd_buf);
Static int airswitch_get_table_2(void* dev_inst, void* cmd_buf);
Static int airswitch_set_table_2(void* dev_inst, void* cmd_buf);
Static char process_set_motor_duty_cycle(cmd_buf_t* cmd_buf);
Static unsigned char deal_apptest_no_respond(cmd_t* cmd_info);
Static int report_airswitch_para_crc(void);
Static char set_adjust_para_k(cmd_buf_t* cmd_buf);
Static char set_adjust_para_b(cmd_buf_t* cmd_buf);
Static int deal_apptest_trigger_end(void* dev_inst, void* cmd_buf);
Static int parse_set_hardware_version(cmd_buf_t* cmd_buf);
Static int deal_eeprom_test_result_apptest(void* dev_inst, void* cmd_buf);
Static int pack_get_real_data_info(void* dev_inst, void* cmd_buf);
Static int pack_get_digital_data(void* dev_inst, void* cmd_buf);
Static int pack_get_address(void* dev_inst, void* cmd_buf);
Static int pack_get_led_status_info_apptest(void* dev_inst, void* cmd_buf);
Static int pack_get_sn_data_info_apptest(void* dev_inst, void* cmd_buf);
Static int pack_get_sn_board_info_apptest(void* dev_inst, void* cmd_buf);
Static int pack_get_fac_data_info_apptest(void* dev_inst, void* cmd_buf);
Static int pack_get_adjust_para_apptest(void* dev_inst, void* cmd_buf);
Static int parse_set_sn_data_info_apptest(cmd_buf_t* cmd_buf);
Static int parse_set_sn_board_apptest(cmd_buf_t* cmd_buf);
Static int parse_set_sys_name_info_apptest(cmd_buf_t* cmd_buf);
Static int parse_set_E2_powerdown_save_info_apptest(cmd_buf_t* cmd_buf);
Static int pack_get_E2_powerdown_save_info_apptest(void* dev_inst, void* cmd_buf);
Static int pack_get_real_data_info_2(void* dev_inst, void* cmd_buf);
Static int pack_get_hardware_ver(void* dev_inst, void* cmd_buf);
Static int pack_get_debug_info(void* dev_inst, void* cmd_buf);
Static int deal_adjust_motor_sligntly_debug(void* dev_inst, void* cmd_buf);
Static int pack_get_board_sn(void* dev_inst, void* cmd_buf);
Static char delete_total_power(void);
Static char restore_default_params(void);
Static char clear_all_alarm();

static rt_timer_t P2P_timer = NULL,reset_delay = NULL;
static dev_inst_t* dev_airswitch = NULL;
Static int s_comm_fail_time_out_flag = 0;
Static int s_CommFailFlag = FALSE;
Static unsigned char apptest_flag = FALSE;
Static char apptest_trigger_repeat = 0;
Static rt_tick_t s_comm_fail_time_out_tick_old = 0;
Static production_info_t s_production_info = {0};

/* 生产参数默认值 */
Static production_info_t s_prod_info_default = {
    .serial_number_flag = 0,                    // 序列号标志位初始化为0
    .manufacturing_month = 1,                   // 默认1月
    .manufacturing_day = 1,                     // 默认1日
    .breaker_specification = 250,               // 空开规格默认值
    .eeprom_power_loss_test = "12345678",       // EEPROM掉电保存测试初始化
    .board_barcode = "123456789000",            // 默认单板条码
    .hardware_version = "123456789000ABC",      // 默认硬件版本
    .breaker_system_name = "iDCB-250",          // 默认系统名称
    .manufacturing_day_sequence = 1,            // 默认日序号
    .manufacturing_year = 2000,                 // 默认年份为当前年
    .current_calibration_slope = 1.0f,          // 电流校准斜率默认为1
    .current_calibration_zero = 0.0f,           // 电流校准零点默认为0
    .bus_voltage_calibration_slope = 1.0f,      // 母排电压校准斜率默认为1
    .bus_voltage_calibration_zero = 0.0f,       // 母排电压校准零点默认为0
    .terminal_voltage_calibration_slope = 1.0f, // 接线端电压校准斜率默认为1
    .terminal_voltage_calibration_zero = 0.0f   // 接线端电压校准零点默认为0
};

static cmd_handle_register_t s_cmd_handle[] = {
    {DEV_CSU,  AIRSWITCH_LINK,     CMD_TYPE_NO_POLL, NULL, airswitch_link},           //0
    {DEV_CSU,  AIRSWITCH_GET_REALDATA,  CMD_TYPE_NO_POLL, NULL, airswitch_get_realdata},      //1
    {DEV_CSU,  AIRSWITCH_GET_TABLE_1,   CMD_TYPE_NO_POLL, NULL, airswitch_get_table_1},      //2
    {DEV_CSU,  AIRSWITCH_SET_TABLE_1,   CMD_TYPE_NO_POLL, airswitch_set_table_1, NULL},      //3
    {DEV_CSU,  AIRSWITCH_GET_TABLE_2,   CMD_TYPE_NO_POLL, NULL, airswitch_get_table_2},      //4
    {DEV_CSU,  AIRSWITCH_SET_TABLE_2,   CMD_TYPE_NO_POLL, airswitch_set_table_2, NULL},      //5
    {DEV_CSU,  AIRSWITCH_CTRL_CMD1,     CMD_TYPE_NO_POLL, process_ctrl_cmd, NULL},      //6
    {DEV_CSU,  AIRSWITCH_CTRL_CMD2,     CMD_TYPE_NO_POLL, process_ctrl_cmd, NULL},      //7
    {DEV_CSU,  AIRSWITCH_CTRL_CMD3,     CMD_TYPE_NO_POLL, process_ctrl_cmd, NULL},      //8
    {DEV_CSU,  AIRSWITCH_CTRL_CMD4,     CMD_TYPE_NO_POLL, process_ctrl_cmd, NULL},      //9
    {DEV_CSU,  AIRSWITCH_CTRL_CMD5,     CMD_TYPE_NO_POLL, process_ctrl_cmd, NULL},      //10
    {DEV_CSU,  AIRSWITCH_CTRL_CMD6,     CMD_TYPE_NO_POLL, process_ctrl_cmd, NULL},      //11
    {DEV_CSU,  AIRSWITCH_TRIGGER_APPTEST,     CMD_TYPE_NO_POLL, deal_apptest_trigger_exit, deal_apptest_trigger_end},       //14
    {DEV_CSU,  AIRSWITCH_EXIT_APPTEST,        CMD_TYPE_NO_POLL, deal_apptest_trigger_exit, NULL},                           //15
    {DEV_CSU,  AIRSWITCH_GET_REALDATA_APPTEST,        CMD_TYPE_NO_POLL, NULL,pack_get_real_data_info},                      //16
    {DEV_CSU,  AIRSWITCH_GET_DIGITAL_DATA_APPTEST,        CMD_TYPE_NO_POLL, NULL,pack_get_digital_data},                    //17
    {DEV_CSU,  AIRSWITCH_GET_ADDRESS_APPTEST,        CMD_TYPE_NO_POLL, NULL,pack_get_address},                              //18
    {DEV_CSU,  AIRSWITCH_GET_LED_STATUS_APPTEST, CMD_TYPE_NO_POLL, NULL, pack_get_led_status_info_apptest},                 //19
    {DEV_CSU,  AIRSWITCH_GET_SNDATA_APPTEST, CMD_TYPE_NO_POLL, NULL, pack_get_sn_data_info_apptest},                        //20
    {DEV_CSU,  AIRSWITCH_GET_BOARD_SN_APPTEST, CMD_TYPE_NO_POLL, NULL, pack_get_sn_board_info_apptest},                     //21
    {DEV_CSU,  AIRSWITCH_GET_SOFTVERSION_APPTEST, CMD_TYPE_NO_POLL, NULL, pack_get_fac_data_info_apptest},                  //22
    {DEV_CSU,  AIRSWITCH_GET_ADJUST_PARA_APPTEST, CMD_TYPE_NO_POLL, NULL, pack_get_adjust_para_apptest},                    //23
    {DEV_CSU,  AIRSWITCH_CTRL_RESET_APPTEST,  CMD_TYPE_NO_POLL, deal_apptest_cmd, NULL},                                    //24
    {DEV_CSU,  AIRSWITCH_SET_SNDATA_APPTEST, CMD_TYPE_NO_POLL, deal_apptest_cmd, NULL},                                     //25
    {DEV_CSU,  AIRSWITCH_SET_BOARD_SN_APPTEST, CMD_TYPE_NO_POLL, deal_apptest_cmd, NULL},                                   //26
    {DEV_CSU,  AIRSWITCH_SET_MOTOR_DUTY_CYCLE_APPTEST,               CMD_TYPE_NO_POLL, deal_apptest_cmd, NULL},             //27
    {DEV_CSU,  AIRSWITCH_CTRL_SET_LED_STATUS_APPTEST, CMD_TYPE_NO_POLL, deal_apptest_cmd, NULL},                            //28
    {DEV_CSU,  AIRSWITCH_CTRL_CLEAR_FAULT_OPENING_TIMES_APPTEST,     CMD_TYPE_NO_POLL, deal_apptest_cmd, NULL},             //29
    {DEV_CSU,  AIRSWITCH_ADJUST_PARA_SET_APPTEST_K,     CMD_TYPE_NO_POLL, deal_apptest_cmd, NULL},                          //30
    {DEV_CSU,  AIRSWITCH_SET_SYS_NAME_APPTEST, CMD_TYPE_NO_POLL, deal_apptest_cmd, NULL},                                   //31
    {DEV_CSU,  AIRSWITCH_SET_HARDWARE_VER_APPTEST,     CMD_TYPE_NO_POLL, deal_apptest_cmd, NULL},                           //32
    {DEV_CSU,  AIRSWITCH_GET_E2_WR_TEST_RESULT_APPTEST,     CMD_TYPE_NO_POLL, NULL, deal_eeprom_test_result_apptest},       //33
    {DEV_CSU,  AIRSWITCH_SET_E2_POWERDOWN_SAVE_APPTEST, CMD_TYPE_NO_POLL, deal_apptest_cmd, NULL},                          //34
    {DEV_CSU,  AIRSWITCH_GET_E2_POWERDOWN_SAVE_APPTEST, CMD_TYPE_NO_POLL, NULL, pack_get_E2_powerdown_save_info_apptest},   //35
    {DEV_CSU,  AIRSWITCH_GET_REALDATA_2, CMD_TYPE_NO_POLL, NULL, pack_get_real_data_info_2},                                //36
    {DEV_CSU,  AIRSWITCH_GET_HARDWARE_VER, CMD_TYPE_NO_POLL, NULL, pack_get_hardware_ver},                                  //37
    {DEV_CSU,  AIRSWITCH_GET_DEBUG_INFO, CMD_TYPE_NO_POLL, NULL, pack_get_debug_info},                                      //38
    {DEV_CSU,  AIRSWITCH_ADJUST_MOTOR_SLIGNTLY_DEBUG, CMD_TYPE_NO_POLL, deal_adjust_motor_sligntly_debug, NULL},            //39
    {DEV_CSU,  AIRSWITCH_GET_BOARD_SN, CMD_TYPE_NO_POLL, NULL, pack_get_board_sn},                                          //40
    {DEV_CSU,  AIRSWITCH_RESTORE_DEF_PARA_APPTEST,     CMD_TYPE_NO_POLL, deal_apptest_cmd, NULL},                           //41
    {DEV_CSU,  AIRSWITCH_ADJUST_PARA_SET_APPTEST_B,     CMD_TYPE_NO_POLL, deal_apptest_cmd, NULL},                          //42
    {DEV_CSU,  AIRSWITCH_CTRL_CMD7, CMD_TYPE_NO_POLL, process_ctrl_cmd, NULL},                                              //43
};

Static msg_map north_msg_map[] =
{
         {0,NULL},//临时添加解决编译问题
};

static dev_init_t dev_init_tab[] = {
    {DEV_CSU, init_dev_airswitch, NULL},
};

static protocol_process_t protocol_process_tab[] = {
    {PROTOCOL_BOTTOM_COMM, bottom_pack, bottom_parse},
};

static link_type_t link_type_tab[] = {
    {LINK_COM, s485_dev_init, com_dev_read, com_dev_write, com_dev_set},
};

static link_inst_t link_inst_tab[] = {
    {LINK_AIRSWITCH,    LINK_COM, "usart0"},
};

char sync_product_info_to_data_id(production_info_t* pdt_info)
{
    RETURN_VAL_IF_FAIL(pdt_info != NULL, FAILURE);
    set_one_data(SWITCH_DATA_ID_SN_FLAG, &pdt_info->serial_number_flag);
    set_one_data(SWITCH_DATA_ID_MANUFACTURE_YEAR, &pdt_info->manufacturing_year);
    set_one_data(SWITCH_DATA_ID_MANUFACTURE_MONTH, &pdt_info->manufacturing_month);
    set_one_data(SWITCH_DATA_ID_MANUFACTURE_DAY, &pdt_info->manufacturing_day);
    set_one_data(SWITCH_DATA_ID_MANUFACTURE_DAY_NUM, &pdt_info->manufacturing_day_sequence);
    set_one_data(SWITCH_DATA_ID_SPECIFICATION, &pdt_info->breaker_specification);
    set_one_data(SWITCH_DATA_ID_CURRENT_CALIBRATION_SLOPE, &pdt_info->current_calibration_slope);
    set_one_data(SWITCH_DATA_ID_CURRENT_CALIBRATION_ZERO, &pdt_info->current_calibration_zero);
    set_one_data(SWITCH_DATA_ID_BUS_VOLTAGE_CALIBRATION_SLOPE, &pdt_info->bus_voltage_calibration_slope);
    set_one_data(SWITCH_DATA_ID_BUS_VOLTAGE_CALIBRATION_ZERO, &pdt_info->bus_voltage_calibration_zero);
    set_one_data(SWITCH_DATA_ID_TERMINAL_CALIBRATION_SLOPE, &pdt_info->terminal_voltage_calibration_slope);
    set_one_data(SWITCH_DATA_ID_TERMINAL_CALIBRATION_ZERO, &pdt_info->terminal_voltage_calibration_zero);
    set_one_data(SWITCH_DATA_ID_HARDWARE_VERSION, pdt_info->hardware_version);
    set_one_data(SWITCH_DATA_ID_E2_POWERDOWN_SAVE, pdt_info->eeprom_power_loss_test);
    set_one_data(SWITCH_DATA_ID_BOARD_SN, pdt_info->board_barcode);
    set_one_data(SWITCH_DATA_ID_SYSTEM_NAME, pdt_info->breaker_system_name);
    return SUCCESSFUL;
}

char init_device_para(void)
{
    char software_version[20] = "V1.00.00.00";
    date_base_t date = {2025, 4, 28};
    unsigned char specification = 0;
    float rated_current = 0.0;
    power_energy_t power_energy = {0};
    unsigned char buff[8] = {0};
    float over_current_alarm_threshold = 0.0;
    int ret = 0;

    // 初始化生产参数
    ret = eeprom_storage_read((uint8_t *)&s_production_info, sizeof(s_production_info), PART_PRODUCTINFO, TRUE);
    if (-RT_ERROR == ret)
    {
        // 初始化失败时恢复默认参数
        eeprom_storage_write((uint8_t *)&s_prod_info_default, sizeof(s_prod_info_default), PART_PRODUCTINFO, TRUE);
        rt_memcpy_s(&s_production_info, sizeof(s_production_info), &s_prod_info_default, sizeof(s_prod_info_default));
    }
    sync_product_info_to_data_id(&s_production_info);

    eeprom_storage_read(&buff[0], sizeof(buff), PART_ELECPARA, TRUE);
    rt_memcpy_s(&power_energy, sizeof(power_energy_t), &buff[0], sizeof(buff));

    //拦截初始EEPROM中的值不确定，出现电量为负值的情况
    power_energy.input_power_energy = (power_energy.input_power_energy < 0 || 
                                       power_energy.input_power_energy > SEVEN_HUNDRED_THOUSAND_KILOWATT) ? 0 : power_energy.input_power_energy;
    power_energy.output_power_energy = (power_energy.output_power_energy < 0 ||
                                       power_energy.output_power_energy > SEVEN_HUNDRED_THOUSAND_KILOWATT) ? 0 : power_energy.output_power_energy;

                                       
    set_one_data(SWITCH_DATA_ID_INPUT_POWER_ENERGY,&power_energy.input_power_energy);
    set_one_data(SWITCH_DATA_ID_OUTPUT_POWER_ENERGY,&power_energy.output_power_energy);

    set_one_data(SWITCH_DATA_ID_SOFTWARE_VERSION, software_version);
    set_one_data(SWITCH_DATA_ID_SOFTWARE_RELEASE_DATE, &date);

    specification = s_production_info.breaker_specification;
    rated_current = (float)specification;
    set_one_data(SWITCH_DATA_ID_RATED_CURRENT, &rated_current);

    //空开过流告警阈值默认值为0，如果初始化时为0，根据不同规格将默认值重新设置成对应额定电流
    get_one_para(SWITCH_PARA_ID_OVER_CURRENT_ALARM_THRESHOLD_OFFSET, &over_current_alarm_threshold);
    if (fabs(over_current_alarm_threshold - 0.0f) < EPSILON)
    {
        set_one_para(SWITCH_PARA_ID_OVER_CURRENT_ALARM_THRESHOLD_OFFSET, &rated_current, TRUE, FALSE);
    }

    return SUCCESSFUL;
}

Static north_mgr_t * init_thread_data(link_inst_t* link_inst, unsigned char mod_id) {
    north_mgr_t* north_mgr = NULL;

    north_mgr = malloc(sizeof(north_mgr_t));
    if (north_mgr == NULL)
        return NULL;
    
    north_mgr->cmd_buff = malloc(sizeof(cmd_buf_t));
    if (north_mgr->cmd_buff == NULL) {
        goto NORTH_MGR_FREE;
    }

    north_mgr->cmd_buff->addr_dev_data = 0x01;
    north_mgr->link_inst = link_inst;
    return north_mgr;
     
NORTH_MGR_FREE:
    free(north_mgr);
    return NULL;
}

/* 通讯断判断函数 */
Static short judge_comm_status(void)
{
    short ret = 0;
    unsigned char comm_status = 0;
    static rt_tick_t comm_fail_time_out_tick = 0;
    comm_fail_time_out_tick = rt_tick_get_millisecond();

    get_one_data(SWITCH_DATA_ID_COMMUNICATION_FAIL_STATUS, &comm_status);//获取当前通讯断状态

    if(s_comm_fail_time_out_flag == 0)//收到有效帧
    {
        s_CommFailFlag = RT_FALSE;
        s_comm_fail_time_out_flag = 1;
        s_comm_fail_time_out_tick_old = comm_fail_time_out_tick;
    }

    if(fabs(comm_fail_time_out_tick - s_comm_fail_time_out_tick_old) > TIMEOUT_COMMFAIL_MAXTIME)//未收到有效帧超过最大超时次数
    {
        s_CommFailFlag = RT_TRUE;
    }

    if(s_CommFailFlag != comm_status)//更新状态
    {
        ret = set_one_data(SWITCH_DATA_ID_COMMUNICATION_FAIL_STATUS, &s_CommFailFlag);
    }

    return ret;
}

/* 设备初始化*/
char init_link_tab(void)
{
    char ret = 0;
    register_link_type_info(link_type_tab, sizeof(link_type_tab) / sizeof(link_type_t));
    register_link_inst_tab_info(link_inst_tab, sizeof(link_inst_tab)/sizeof(link_inst_tab[0]));
    register_protocol_process_info(protocol_process_tab, sizeof(protocol_process_tab) / sizeof(protocol_process_t));
    ret = init_dev_tab(dev_init_tab, sizeof(dev_init_tab)/sizeof(dev_init_tab[0]));
    return ret;
}

/* 北向初始化*/
void* init_north(void * param) {
    server_info_t *server_info = (server_info_t *)param;
    north_mgr_t* north_mgr = NULL;
    unsigned char i;

    dev_airswitch = init_dev_inst(DEV_CSU);
    if(dev_airswitch == NULL)
    {
        return NULL;
    }
    server_info->server.server.map_size = sizeof(north_msg_map) / sizeof(msg_map);
    register_server_msg_map(north_msg_map, server_info);
    north_mgr = init_thread_data(dev_airswitch->dev_type->link_inst, MOD_AIRSWITCH);
    for(i = 0; i < sizeof(s_cmd_handle)/sizeof(s_cmd_handle[0]); i++) {
        register_cmd_handle(&s_cmd_handle[i]);
    }

    //创建P2P定时器
    P2P_timer = rt_timer_create(P2P_TIMER_NAME, ctrl_P2P_off,
                             NULL, P2P_TIMEOUT,
                             RT_TIMER_FLAG_ONE_SHOT); 
    return north_mgr;
}

/* 收发线程 */
void north_comm_th(void *param) {
    north_mgr_t* north_mgr = (north_mgr_t*)param;
    unsigned char time_delay = 50;

    while (is_running(TRUE)) 
    {
        if(get_setaddrflag() == RT_FALSE)
        {
            rt_thread_mdelay(time_delay);
            continue;
        }
        judge_comm_status();//通讯断判断

        if(RT_EOK == rt_sem_take(&(north_mgr->link_inst->rx_sem), THREAD_TICK * 5)){
            if (SUCCESSFUL == cmd_recv_process(dev_airswitch, north_mgr->cmd_buff)) {
                s_comm_fail_time_out_flag = 0;//通讯断清零
                if (deal_apptest_no_respond(north_mgr->cmd_buff->cmd) == FALSE)
                {
                    cmd_send_process(dev_airswitch, north_mgr->cmd_buff);
                }
            }
            continue;
        }
    }
}

Static char power_down_command_handle(cmd_buf_t* cmd_buf) 
{
    unsigned char manual_disconnection_flag = 0;
    unsigned char loop_status = 0;
    unsigned char power_on_down_command = 1;
    unsigned char switch_open_control_status = AIRSWITCH_COMMAND_STATUS_OPEN;

    get_one_para(SWITCH_PARA_ID_MANUAL_DISCONNECTION_FLAG_OFFSET, &manual_disconnection_flag);
    get_one_data(SWITCH_DATA_ID_LOOP_STATUS, &loop_status);

    if(loop_status == LOOP_CLOSE)
    {
        // 如果手动断开标志为0，回路状态闭合，则置位空开分闸控制状态
        set_one_data(SWITCH_DATA_ID_NORTH_CMD_STATUS, &switch_open_control_status);
    }
    set_one_para(SWITCH_PARA_ID_POWER_ON_POWER_DOWN_COMMAND_OFFSET, &power_on_down_command, TRUE, FALSE);

    return 0;
}

Static int report_airswitch_para_crc(void) {
    unsigned char buff[PARA1_DATA_LEN] = {0};
    unsigned short offset = 0,crc = 0;
    unsigned short  over_current_protect_delay = 0,over_current_protect_threshold = 0;
    float  up_voltage_threshold = 0,down_voltage_threshold = 0,current_alarm_threshold = 0;

    get_one_para(SWITCH_PARA_ID_POWER_DOWN_ENABLED_OFFSET, &buff[offset++]);//空开下电使能

    get_one_para(SWITCH_PARA_ID_POWER_DOWN_VOLTAGE_THRESHOLD_OFFSET, &down_voltage_threshold);//空开下电电压阈值
    put_uint16_to_buff(&buff[offset],FLOAT_TO_SHORT(down_voltage_threshold * 100));
    offset += 2;

    get_one_para(SWITCH_PARA_ID_POWER_ON_VOLTAGE_THRESHOLD_OFFSET, &up_voltage_threshold);//空开上电电压阈值
    put_uint16_to_buff(&buff[offset],FLOAT_TO_SHORT(up_voltage_threshold * 100));
    offset += 2;

    get_one_para(SWITCH_PARA_ID_OVER_CURRENT_PROTECT_ENABLED_OFFSET, &buff[offset++]);//空开过流保护使能

    get_one_para(SWITCH_PARA_ID_OVER_CURRENT_ALARM_THRESHOLD_OFFSET, &current_alarm_threshold);//空开过流告警阈值
    put_uint16_to_buff(&buff[offset],FLOAT_TO_SHORT(current_alarm_threshold * 100));
    offset += 2;

    get_one_para(SWITCH_PARA_ID_POWER_ON_AUTHORIZATION_OFFSET, &buff[offset++]);//空开上电授权

    get_one_para(SWITCH_PARA_ID_OVER_CURRENT_PROTECT_DELAY_OFFSET, &over_current_protect_delay);//空开过流保护延时
    put_uint16_to_buff(&buff[offset],over_current_protect_delay);
    offset += 2;

    get_one_para(SWITCH_PARA_ID_OVER_CURRENT_PROTECT_AUTO_RECOVERY_TIMES_OFFSET, &buff[offset++]);//空开过流保护自动恢复次数
    get_one_para(SWITCH_PARA_ID_CURRENT_PROTECT_AUTO_RECOVERY_SPACE_OFFSET, &buff[offset++]);//空开过流保护自动恢复间隔

    get_one_para(SWITCH_PARA_ID_OVER_CURRENT_PROTECT_THRESHOLD_OFFSET, &over_current_protect_threshold);//空开过流保护阈值
    put_uint16_to_buff(&buff[offset],over_current_protect_threshold);
    offset += 2;

    get_one_para(SWITCH_PARA_ID_DISCONNECT_JUDGMENT_MODE_OFFSET, &buff[offset]);//空开断开判断方式

    crc = crc_cal(buff, PARA1_DATA_LEN);

    return crc;
}

Static char power_on_command_handle(cmd_buf_t* cmd_buf)
{
    unsigned char manual_disconnection_flag = 0;
    unsigned char loop_status = 0;
    unsigned char power_on_down_command = 0;
    unsigned char switch_close_control_status = AIRSWITCH_COMMAND_STATUS_CLOSE;

    get_one_para(SWITCH_PARA_ID_MANUAL_DISCONNECTION_FLAG_OFFSET, &manual_disconnection_flag);
    get_one_data(SWITCH_DATA_ID_LOOP_STATUS, &loop_status);

    if(1 == manual_disconnection_flag)
    {
        // 如果手动断开标志为1，则置位空开合闸控制状态
        set_one_data(SWITCH_DATA_ID_NORTH_CMD_STATUS, &switch_close_control_status);
    }
    else
    {
        if(loop_status == LOOP_OPEN)
        {
            // 手动断开标志为0，回路状态断开，则置位空开合闸控制状态
            set_one_data(SWITCH_DATA_ID_NORTH_CMD_STATUS, &switch_close_control_status);
        }
    }
    set_one_para(SWITCH_PARA_ID_POWER_ON_POWER_DOWN_COMMAND_OFFSET, &power_on_down_command, TRUE, FALSE);

    return 0;
}

Static char delete_total_power(void)
{
    int airswitch_electric_input = 0;
    int airswitch_electric_output = 0;

    set_one_data(SWITCH_DATA_ID_INPUT_POWER_ENERGY, &airswitch_electric_input);
    set_one_data(SWITCH_DATA_ID_OUTPUT_POWER_ENERGY, &airswitch_electric_output);
    save_power_energy();    // 保存电量信息到eeprom中

    return 0;
}

/**
 * 空开收到一键功能开启指令以后处理
 */
Static char airswitch_ctrl_P2P_on_cmd(cmd_buf_t* cmd_buf)
{
    unsigned char  P2Pstatus = 0;

    //获取一键功能状态
    if(FAILURE == get_one_data(SWITCH_DATA_ID_P2P_STATUS,&P2Pstatus))
    {
        cmd_buf->rtn = BOTTOM_RTN_CMD_ERROR;
        return FAILURE;
    }

    if(P2Pstatus == P2P_STATUS_ON)
    {
        //如果一键功能状态已经是处于一键功能则刷新定时器
        rt_timer_stop(P2P_timer);

        rt_timer_start(P2P_timer);
    }
    else if(P2Pstatus == P2P_STATUS_OFF)
    {
        //若不处于一键功能状态，就开启定时器
        rt_timer_start(P2P_timer);
    }

    //一键功能状态位置1
    P2Pstatus = P2P_STATUS_ON;
    if(FAILURE == set_one_data(SWITCH_DATA_ID_P2P_STATUS,&P2Pstatus))
    {
        cmd_buf->rtn = BOTTOM_RTN_CMD_ERROR;
        return FAILURE;
    }

    return SUCCESSFUL;
}


/**
 * 空开收到一键功能退出指令以后处理
 */
Static int airswitch_ctrl_P2P_off_cmd(cmd_buf_t* cmd_buf)
{
    unsigned char P2Pstatus = 0;

    //获取一键功能状态
    if(FAILURE == get_one_data(SWITCH_DATA_ID_P2P_STATUS,&P2Pstatus))
    {
        cmd_buf->rtn = BOTTOM_RTN_CMD_ERROR;
        return FAILURE;
    }
   
    // 检测状态，若处于一键功能则关闭定时器，并将一键功能状态位置0
    if(P2Pstatus == P2P_STATUS_ON)
    {
        //若处于一键功能则关闭定时器
        rt_timer_stop(P2P_timer);
    }

    //一键功能状态位置0
    P2Pstatus = P2P_STATUS_OFF;
    if(FAILURE == set_one_data(SWITCH_DATA_ID_P2P_STATUS,&P2Pstatus))
    {
        cmd_buf->rtn = BOTTOM_RTN_CMD_ERROR;
        return FAILURE;
    }

    return SUCCESSFUL;
}

Static void airswitch_reset(void *parameter)
{
    NVIC_SystemReset();
}

/**
 * 空开复位处理
 */
Static char airswitch_reset_handle()
{
    if (reset_delay == NULL)
    {
        reset_delay = rt_timer_create("airswitchreset", airswitch_reset, NULL, 2000, RT_TIMER_FLAG_ONE_SHOT);
    }

    if (reset_delay != NULL)
    {
        rt_timer_stop(reset_delay);
        rt_timer_start(reset_delay);
    }

    return SUCCESSFUL;
}

/**
 * 20秒后，空开一键功能状态位自动置0
 */
Static void ctrl_P2P_off(void *parameter)
{
    unsigned char P2Pstatus = P2P_STATUS_OFF;
    set_one_data(SWITCH_DATA_ID_P2P_STATUS,&P2Pstatus);
    return;
}

/**
 * @brief 发送控制消息
 * @param[in] dev_inst 设备实例信息
 * @param[in] cmd_buf 接收的数据
 * @retval
 * @note 发送控制命令（目前硬件设备还未合入，数据暂存）
 */
Static int process_ctrl_cmd(void* dev_inst, void* cmd_buf)
{
    unsigned char authorization_status = 0;
    get_one_para(SWITCH_PARA_ID_POWER_ON_AUTHORIZATION_OFFSET, &authorization_status);
    RETURN_VAL_IF_FAIL(cmd_buf != NULL, FAILURE);
    int cmd_result = SUCCESSFUL;

    cmd_buf_t* s_cmd_buf = (cmd_buf_t*)cmd_buf;
    s_cmd_buf->data_len = 0;

    switch(s_cmd_buf->cmd->cmd_id)
    {
        case AIRSWITCH_CTRL_CMD1:
            if(authorization_status == UNAUTHORIZATION_STATUS)
            {
                return SUCCESSFUL;
            }
            cmd_result = power_down_command_handle(s_cmd_buf);
            break;
        case AIRSWITCH_CTRL_CMD2:
            if(authorization_status == UNAUTHORIZATION_STATUS)
            {
                return SUCCESSFUL;
            }
            cmd_result = power_on_command_handle(s_cmd_buf);
            break;
        case AIRSWITCH_CTRL_CMD3:
            cmd_result = delete_total_power();
            break;
        case AIRSWITCH_CTRL_CMD4:
            cmd_result = airswitch_ctrl_P2P_on_cmd(s_cmd_buf);
            break;
        case AIRSWITCH_CTRL_CMD5:
            cmd_result = airswitch_ctrl_P2P_off_cmd(s_cmd_buf);
            break;
        case AIRSWITCH_CTRL_CMD6:
            cmd_result = airswitch_reset_handle();
            break;
        case AIRSWITCH_CTRL_CMD7:
            cmd_result = clear_all_alarm();
            break;
        default:
            return FAILURE;
    }
    return cmd_result;
}

Static int deal_apptest_trigger_exit(void* dev_inst, void* cmd_buf)
{
    RETURN_VAL_IF_FAIL(cmd_buf != NULL, FAILURE);

    cmd_buf_t* s_cmd_buf = (cmd_buf_t*)cmd_buf;
    s_cmd_buf->data_len = 0;

    switch (s_cmd_buf->cmd->cmd_id)
    {
        case AIRSWITCH_TRIGGER_APPTEST:
            if (apptest_trigger_repeat < TRIGGER_TIMES)
            {
                apptest_trigger_repeat++;
            }
            break;
        case AIRSWITCH_EXIT_APPTEST:
            apptest_trigger_repeat = (apptest_flag == TRUE) ? APPTEST_TRIGGER_TO_EXIT_FLAG : 0;
            apptest_flag = FALSE;
            break;
        default:
            return FAILURE;
    }

    if (apptest_flag == FALSE && apptest_trigger_repeat >= TRIGGER_TIMES)
    {
        apptest_flag = TRUE;
        set_led_color(WHITE);   // 设置灯为白色
    }
    else if (apptest_flag == TRUE)
    {
        apptest_trigger_repeat = TRIGGER_TIMES;
    }
    set_one_data(SWITCH_DATA_ID_APPTEST_FLAG, &apptest_flag);

    return SUCCESSFUL;
}

/**
 * @brief APPTEST命令
 * @param[in] dev_inst 设备实例信息
 * @param[in] cmd_buf 接收的数据
 * @retval
 * @note 
 */

Static int deal_apptest_cmd(void* dev_inst, void* cmd_buf)
{
    // 确保cmd_buf不为NULL
    if (cmd_buf == NULL) {
        return FAILURE;
    }

    cmd_buf_t* s_cmd_buf = (cmd_buf_t*)cmd_buf;

    // 检查apptest_flag
    if (!apptest_flag) {
        s_cmd_buf->data_len = 0;
        s_cmd_buf->rtn = BOTTOM_RTN_CMD_ERROR;
        return FAILURE;
    }

    // 处理不同的命令ID
    switch(s_cmd_buf->cmd->cmd_id) {
        case AIRSWITCH_CTRL_RESET_APPTEST:
            airswitch_reset_handle();
            break;
        case AIRSWITCH_SET_SNDATA_APPTEST:
            parse_set_sn_data_info_apptest(s_cmd_buf);
            break;
        case AIRSWITCH_SET_BOARD_SN_APPTEST:
            parse_set_sn_board_apptest(s_cmd_buf);
            break;
        case AIRSWITCH_SET_MOTOR_DUTY_CYCLE_APPTEST:
            process_set_motor_duty_cycle(s_cmd_buf);
            break;
        case AIRSWITCH_CTRL_SET_LED_STATUS_APPTEST:
            set_led_color(s_cmd_buf->buf[0]);
            break;
        case AIRSWITCH_CTRL_CLEAR_FAULT_OPENING_TIMES_APPTEST:
            clear_fault_opening_times();
            break;
        case AIRSWITCH_ADJUST_PARA_SET_APPTEST_K:
            set_adjust_para_k(s_cmd_buf);
            break;
        case AIRSWITCH_SET_SYS_NAME_APPTEST:
            parse_set_sys_name_info_apptest(s_cmd_buf);
            break;
        case AIRSWITCH_SET_HARDWARE_VER_APPTEST:
            parse_set_hardware_version(s_cmd_buf);
            break;
        case AIRSWITCH_SET_E2_POWERDOWN_SAVE_APPTEST:
            parse_set_E2_powerdown_save_info_apptest(s_cmd_buf);
            break;
        case AIRSWITCH_RESTORE_DEF_PARA_APPTEST:
            restore_default_params();
            break;
        case AIRSWITCH_ADJUST_PARA_SET_APPTEST_B:
            set_adjust_para_b(s_cmd_buf);
            break;
        default:
            s_cmd_buf->data_len = 0;
            s_cmd_buf->rtn = BOTTOM_RTN_CMD_ERROR;
            return FAILURE;
    }

    // 设置data_len为0以表示处理完成
    s_cmd_buf->data_len = 0;
    return SUCCESSFUL;
}


Static char restore_default_params(void)
{
    char ret = 0;

    ret = restore_default_sys_params();
    RETURN_VAL_IF_FAIL(ret == SUCCESSFUL, FAILURE);

    ret = restore_default_alarm_params();
    RETURN_VAL_IF_FAIL(ret == SAVE_ALARM_INDEX_MAX, FAILURE);

    eeprom_storage_write((uint8_t *)&s_prod_info_default, sizeof(s_prod_info_default), PART_PRODUCTINFO, TRUE);
    // 恢复输入/输出电量
    delete_total_power();
    airswitch_reset_handle();

    return SUCCESSFUL;
}

/**
 * @brief 处理设置电机方向和转速相关命令
 * @param[in] cmd_buf 接收的数据
 * @retval
 * @note 处理设置电机方向和转速相关命令
 */
Static char process_set_motor_duty_cycle(cmd_buf_t* cmd_buf)
{
    unsigned char speed = 0, direction = 0;

    RETURN_VAL_IF_FAIL(cmd_buf != NULL, FAILURE);
    cmd_buf_t* s_cmd_buf = (cmd_buf_t*)cmd_buf;

    if(s_cmd_buf->buf[0]!=2)
    {
        s_cmd_buf->rtn = BOTTOM_RTN_CMD_ERROR;
        return FAILURE;
    }
    //data[0] 为字节数
    direction = s_cmd_buf->buf[1];//获取
    speed = s_cmd_buf->buf[2];
    if(FAILURE == airswitchMotorCtrl(speed,direction))
    {
        s_cmd_buf->rtn = BOTTOM_RTN_CMD_ERROR;
        return FAILURE; 
    }
    s_cmd_buf->rtn = BOTTOM_RTN_APP_CORRECT;
    return SUCCESSFUL;
}

Static unsigned char deal_apptest_no_respond(cmd_t* cmd_info)
{
    if (cmd_info == NULL)
    {
        return FALSE;
    }

    unsigned short cmd_id = cmd_info->cmd_id;

    if (apptest_trigger_repeat == APPTEST_TRIGGER_TO_EXIT_FLAG)
    {
        apptest_trigger_repeat = 0;
        return FALSE;
    }

    if (((cmd_id >= AIRSWITCH_EXIT_APPTEST && cmd_id <= AIRSWITCH_GET_E2_POWERDOWN_SAVE_APPTEST) ||
          cmd_id == AIRSWITCH_RESTORE_DEF_PARA_APPTEST) &&
        (apptest_flag == FALSE))
    { // 非APPTEST模式下不响应APPTEST指令
        return TRUE;
    }

    return FALSE;
}

Static char set_adjust_para_k(cmd_buf_t* cmd_buf) {
    if (!cmd_buf) {
        return FAILURE;
    }

    cmd_buf_t* cmd_buf_tmp = (cmd_buf_t*)cmd_buf;
    float calibration_k = 0.0f;
    short input_k = 0;
    int ret_write = 0;

    if (cmd_buf_tmp->buf[0]!=3)
    {
        cmd_buf_tmp->rtn = BOTTOM_RTN_DATA_ERROR;
        return FAILURE;
    }
    input_k = get_int16_data(&cmd_buf_tmp->buf[2]);
    if (input_k <= -2000 || input_k >= 2000)
    {
        cmd_buf_tmp->rtn = BOTTOM_RTN_DATA_ERROR;
        return FAILURE;
    }
    calibration_k = input_k * 0.001f;

    switch (cmd_buf_tmp->buf[1])
    {
        case CALIBRATION_CURRENT:
        {
            s_production_info.current_calibration_slope = calibration_k;
            ret_write = eeprom_storage_write((uint8_t *)&s_production_info, sizeof(s_production_info), PART_PRODUCTINFO, TRUE);
            if (ret_write !=sizeof(s_production_info))
            {
                s_production_info.current_calibration_slope = 1.0f;
            }
            break;
        }
        case CALIBRATION_BUS_VOLTAGE:
        {
            s_production_info.bus_voltage_calibration_slope = calibration_k;
            ret_write = eeprom_storage_write((uint8_t *)&s_production_info, sizeof(s_production_info), PART_PRODUCTINFO, TRUE);
            if (ret_write !=sizeof(s_production_info))
            {
                s_production_info.bus_voltage_calibration_slope = 1.0f;
            }
            break;
        }
        case CALIBRATION_TERMINAL_VOLTAGE:
        {
            s_production_info.terminal_voltage_calibration_slope = calibration_k;
            ret_write = eeprom_storage_write((uint8_t *)&s_production_info, sizeof(s_production_info), PART_PRODUCTINFO, TRUE);
            if (ret_write !=sizeof(s_production_info))
            {
                s_production_info.terminal_voltage_calibration_slope = 1.0f;
            }
            break;
        }
        default:
            cmd_buf_tmp->rtn = BOTTOM_RTN_DATA_ERROR;
            return FAILURE;
    }

    if (ret_write !=sizeof(s_production_info)) {
        cmd_buf_tmp->rtn = BOTTOM_RTN_DATA_ERROR;
        return FAILURE;
    }
    cmd_buf_tmp->rtn = BOTTOM_RTN_APP_CORRECT;
    sync_product_info_to_data_id(&s_production_info);
    return SUCCESSFUL;
}

Static char set_adjust_para_b(cmd_buf_t* cmd_buf) {
    if (!cmd_buf) {
        return FAILURE;
    }

    cmd_buf_t* cmd_buf_tmp = (cmd_buf_t*)cmd_buf;
    float calibration_b = 0.0f;
    int ret_write = 0;
    short input_b = 0;

    input_b = get_int16_data(&cmd_buf_tmp->buf[2]);
    if (input_b <= -10000 || input_b >= 10000)
    {
        cmd_buf_tmp->rtn = BOTTOM_RTN_DATA_ERROR;
        return FAILURE;
    }
    calibration_b = input_b * 0.001f;

    if (cmd_buf_tmp->buf[0]!=3)
    {
        cmd_buf_tmp->rtn = BOTTOM_RTN_DATA_ERROR;
        return FAILURE;
    }

    switch (cmd_buf_tmp->buf[1])
    {
        case CALIBRATION_CURRENT:
        {
            s_production_info.current_calibration_zero = calibration_b;
            ret_write = eeprom_storage_write((uint8_t *)&s_production_info, sizeof(s_production_info), PART_PRODUCTINFO, TRUE);
            if (ret_write !=sizeof(s_production_info))
            {
                s_production_info.current_calibration_zero = 0.0f;
            }
            break;
        }
        case CALIBRATION_BUS_VOLTAGE:
        {
            s_production_info.bus_voltage_calibration_zero = calibration_b;
            ret_write = eeprom_storage_write((uint8_t *)&s_production_info, sizeof(s_production_info), PART_PRODUCTINFO, TRUE);
            if (ret_write !=sizeof(s_production_info))
            {
                s_production_info.bus_voltage_calibration_zero = 0.0f;
            }
            break;
        }
        case CALIBRATION_TERMINAL_VOLTAGE:
        {
            s_production_info.terminal_voltage_calibration_zero = calibration_b;
            ret_write = eeprom_storage_write((uint8_t *)&s_production_info, sizeof(s_production_info), PART_PRODUCTINFO, TRUE);
            if (ret_write !=sizeof(s_production_info))
            {
                s_production_info.terminal_voltage_calibration_zero = 0.0f;
            }
            break;
        }
        default:
            cmd_buf_tmp->rtn = BOTTOM_RTN_DATA_ERROR;
            return FAILURE;
    }

    calibration_b = get_int16_data(&cmd_buf_tmp->buf[2]) * 0.001f;

    if (ret_write !=sizeof(s_production_info)) {
        cmd_buf_tmp->rtn = BOTTOM_RTN_DATA_ERROR;
        return FAILURE;
    }
    sync_product_info_to_data_id(&s_production_info);
    cmd_buf_tmp->rtn = BOTTOM_RTN_APP_CORRECT;
    return SUCCESSFUL;
}



Static int deal_apptest_trigger_end(void* dev_inst, void* cmd_buf)
{
    RETURN_VAL_IF_FAIL(cmd_buf != NULL, FAILURE);

    cmd_buf_t* s_cmd_buf = (cmd_buf_t*)cmd_buf;
    s_cmd_buf->buf[0] = 0;

    if (apptest_flag == TRUE)
    {
        s_cmd_buf->buf[0] = APPTEST_TRIGGER_SUCCESS;
    }

    s_cmd_buf->data_len = 1;
    s_cmd_buf->rtn = BOTTOM_RTN_APP_CORRECT;

    return SUCCESSFUL;
}

Static int deal_eeprom_test_result_apptest(void* dev_inst, void* cmd_buf)
{
    RETURN_VAL_IF_FAIL(cmd_buf != NULL, FAILURE);

    unsigned short r_para = 0;
    unsigned short w_para = 0xA55A;

    cmd_buf_t* cmd_buf_tmp = (cmd_buf_t*)cmd_buf;
    cmd_buf_tmp->buf[0] = 1;
    cmd_buf_tmp->data_len = 2;

    set_one_para(SWITCH_PARA_ID_E2_WR_TEST_OFFSET, &w_para, TRUE, FALSE);
    rt_thread_mdelay(1000);
    get_one_para(SWITCH_PARA_ID_E2_WR_TEST_OFFSET, &r_para);

    if (0 == rt_memcmp(&w_para ,&r_para, sizeof(unsigned short)))
    {
        cmd_buf_tmp->buf[1] = 0; //测试通过
        cmd_buf_tmp->rtn = BOTTOM_RTN_APP_CORRECT;
        return SUCCESSFUL;
    }
    cmd_buf_tmp->buf[1] = 1; //测试未通过
    cmd_buf_tmp->rtn = BOTTOM_RTN_CHECK_ERROR;
    return FAILURE;
}

/**
 * @brief apptest 获取指示灯状态
*/

Static int pack_get_led_status_info_apptest(void* dev_inst, void* cmd_buf)
{
    unsigned char* p_buf = NULL;
    cmd_buf_t* p_cmd = cmd_buf;
    unsigned char offset = 0;

    RETURN_VAL_IF_FAIL(p_cmd != NULL, FAILURE);

    p_cmd->data_len = 0;
    RETURN_VAL_IF_FAIL(TRUE == apptest_flag, FAILURE);

    p_buf = p_cmd->buf;
    RETURN_VAL_IF_FAIL(p_buf != NULL, FAILURE);

    offset++;
    get_one_data(SWITCH_DATA_ID_LED_STATUS, &p_buf[offset]);
    offset++;

    p_cmd->data_len = offset;
    p_buf[0] = offset - 1;  // 字节长度

    return SUCCESSFUL;
}


/**
 * @brief apptest 获取整机条码
*/

Static int pack_get_sn_data_info_apptest(void* dev_inst, void* cmd_buf)
{
    unsigned char* p_buf = NULL;
    cmd_buf_t* p_cmd = cmd_buf;
    unsigned char offset = 0;

    RETURN_VAL_IF_FAIL(p_cmd != NULL, FAILURE);

    p_cmd->data_len = 0;
    RETURN_VAL_IF_FAIL(TRUE == apptest_flag, FAILURE);

    p_buf = p_cmd->buf;
    RETURN_VAL_IF_FAIL(p_buf != NULL, FAILURE);

    offset++;
    p_buf[offset] = s_production_info.serial_number_flag;
    offset++;

    put_int16_to_buff(&p_buf[offset], s_production_info.manufacturing_year);
    offset += 2;

    p_buf[offset] = s_production_info.manufacturing_month;
    offset++;
    p_buf[offset] = s_production_info.manufacturing_day;
    offset++;

    put_int16_to_buff(&p_buf[offset], s_production_info.manufacturing_day_sequence);
    offset += 2;

    p_buf[offset] = s_production_info.breaker_specification;
    offset++;

    p_cmd->data_len = offset;
    p_buf[0] = offset - 1;  // 字节长度

    return SUCCESSFUL;
}


Static int pack_get_sn_board_info_apptest(void* dev_inst, void* cmd_buf)
{
    unsigned char* p_buf = NULL;
    cmd_buf_t* p_cmd = cmd_buf;
    unsigned char offset = 0;

    RETURN_VAL_IF_FAIL(p_cmd != NULL, FAILURE);

    p_cmd->data_len = 0;
    RETURN_VAL_IF_FAIL(TRUE == apptest_flag, FAILURE);

    p_buf = p_cmd->buf;
    RETURN_VAL_IF_FAIL(p_buf != NULL, FAILURE);

    offset++;
    rt_memcpy_s(&p_buf[offset], BOARD_SN_LEN, &s_production_info.board_barcode[0], BOARD_SN_LEN);
    offset += BOARD_SN_LEN;

    p_cmd->data_len = offset;
    p_buf[0] = offset - 1;  // 字节长度

    return SUCCESSFUL;
}

/**
 * @brief apptest 获取软件版本
*/

Static int pack_get_fac_data_info_apptest(void* dev_inst, void* cmd_buf)
{
    unsigned char* p_buf = NULL;
    cmd_buf_t* p_cmd = cmd_buf;
    unsigned char offset = 0;
    date_base_t date_val = {0};

    RETURN_VAL_IF_FAIL(p_cmd != NULL, FAILURE);

    p_cmd->data_len = 0;
    RETURN_VAL_IF_FAIL(TRUE == apptest_flag, FAILURE);

    p_buf = p_cmd->buf;
    RETURN_VAL_IF_FAIL(p_buf != NULL, FAILURE);

    offset++;
    rt_memcpy_s(&p_buf[offset], 32, &s_production_info.breaker_system_name[0], 32);
    offset += 32;
    get_one_data(SWITCH_DATA_ID_SOFTWARE_VERSION, &p_buf[offset]);
    offset += 20;
    get_one_data(SWITCH_DATA_ID_SOFTWARE_RELEASE_DATE, &date_val);
    put_date_to_buff(&p_buf[offset], date_val);
    offset += sizeof(date_base_t);

    p_cmd->data_len = offset;
    p_buf[0] = offset - 1;  // 字节长度

    return SUCCESSFUL;
}


/**
 * @brief apptest 获取零点校正参数
*/

Static int pack_get_adjust_para_apptest(void* dev_inst, void* cmd_buf)
{
    unsigned char* p_buf = NULL;
    cmd_buf_t* p_cmd = cmd_buf;
    unsigned char offset = 0;
    float f_value = 0;

    RETURN_VAL_IF_FAIL(p_cmd != NULL, FAILURE);

    p_cmd->data_len = 0;
    RETURN_VAL_IF_FAIL(TRUE == apptest_flag, FAILURE);

    p_buf = p_cmd->buf;
    RETURN_VAL_IF_FAIL(p_buf != NULL, FAILURE);

    offset++;
    f_value = s_production_info.current_calibration_slope;
    put_int16_to_buff(&p_buf[offset], (short)(f_value * 1000));
    offset += 2;

    f_value = s_production_info.current_calibration_zero;
    put_int16_to_buff(&p_buf[offset], (short)(f_value * 1000));
    offset += 2;

    f_value = s_production_info.bus_voltage_calibration_slope;
    put_int16_to_buff(&p_buf[offset], (short)(f_value * 1000));
    offset += 2;

    f_value = s_production_info.bus_voltage_calibration_zero;
    put_int16_to_buff(&p_buf[offset], (short)(f_value * 1000));
    offset += 2;

    f_value = s_production_info.terminal_voltage_calibration_slope;
    put_int16_to_buff(&p_buf[offset], (short)(f_value * 1000));
    offset += 2;

    f_value = s_production_info.terminal_voltage_calibration_zero;
    put_int16_to_buff(&p_buf[offset], (short)(f_value * 1000));
    offset += 2;

    p_cmd->data_len = offset;
    p_buf[0] = offset - 1;

    return SUCCESSFUL;
}


/**
 * @brief apptest 设置sn码
*/

Static int parse_set_sn_data_info_apptest(cmd_buf_t* cmd_buf)
{
    unsigned char* p_buf = NULL;
    cmd_buf_t* p_cmd = cmd_buf;
    unsigned char offset = 0;
    unsigned short st_val = 0;

    RETURN_VAL_IF_FAIL(p_cmd != NULL, FAILURE);

    p_cmd->data_len = 0;
    RETURN_VAL_IF_FAIL(TRUE == apptest_flag, FAILURE);

    p_buf = p_cmd->buf;
    RETURN_VAL_IF_FAIL(p_buf != NULL, FAILURE);

    offset++;
    s_production_info.serial_number_flag = p_buf[offset];
    offset++;

    st_val = get_int16_data(&p_buf[offset]);
    s_production_info.manufacturing_year = st_val;
    offset += 2;

    s_production_info.manufacturing_month = p_buf[offset];
    offset++;
    s_production_info.manufacturing_day = p_buf[offset];
    offset++;

    st_val = get_int16_data(&p_buf[offset]);
    s_production_info.manufacturing_day_sequence = st_val;
    offset += 2;

    s_production_info.breaker_specification = p_buf[offset];
    offset++;

    eeprom_storage_write((uint8_t *)&s_production_info, sizeof(s_production_info), PART_PRODUCTINFO, TRUE);
    sync_product_info_to_data_id(&s_production_info);

    return SUCCESSFUL;
}


Static int parse_set_sn_board_apptest(cmd_buf_t* cmd_buf)
{
    unsigned char* p_buf = NULL;
    cmd_buf_t* p_cmd = cmd_buf;
    unsigned char offset = 0;

    RETURN_VAL_IF_FAIL(p_cmd != NULL, FAILURE);

    p_cmd->data_len = 0;
    RETURN_VAL_IF_FAIL(TRUE == apptest_flag, FAILURE);

    p_buf = p_cmd->buf;
    RETURN_VAL_IF_FAIL(p_buf != NULL, FAILURE);

    for (int i = 1; i <= BOARD_SN_LEN; i++)
    {
        if (!(p_buf[i] >= '0' && p_buf[i] <= '9'))
        {
            cmd_buf->rtn = BOTTOM_RTN_DATA_ERROR;
            return FAILURE;
        }
    }

    offset++;
    rt_memcpy_s(&s_production_info.board_barcode[0], BOARD_SN_LEN, &p_buf[offset], BOARD_SN_LEN);
    offset += BOARD_SN_LEN;

    eeprom_storage_write((uint8_t *)&s_production_info, sizeof(s_production_info), PART_PRODUCTINFO, TRUE);
    sync_product_info_to_data_id(&s_production_info);

    return SUCCESSFUL;
}

/**
 * @brief ​apptest 设置系统名称
*/

Static int parse_set_sys_name_info_apptest(cmd_buf_t* cmd_buf)
{
    unsigned char* p_buf = NULL;
    cmd_buf_t* p_cmd = cmd_buf;
    unsigned char offset = 0;

    RETURN_VAL_IF_FAIL(p_cmd != NULL, FAILURE);

    p_cmd->data_len = 0;
    RETURN_VAL_IF_FAIL(TRUE == apptest_flag, FAILURE);

    p_buf = p_cmd->buf;
    RETURN_VAL_IF_FAIL(p_buf != NULL, FAILURE);

    offset++;
    rt_memcpy_s(&s_production_info.breaker_system_name[0], sizeof(s_production_info.breaker_system_name), &p_buf[offset], 32);
    offset += 32;

    eeprom_storage_write((uint8_t *)&s_production_info, sizeof(s_production_info), PART_PRODUCTINFO, TRUE);
    sync_product_info_to_data_id(&s_production_info);

    return SUCCESSFUL;
}


/**
 * @brief apptest 设置eeprom掉电保存参数
*/

Static int parse_set_E2_powerdown_save_info_apptest(cmd_buf_t* cmd_buf)
{
    unsigned char* p_buf = NULL;
    cmd_buf_t* p_cmd = cmd_buf;
    unsigned char offset = 0;

    RETURN_VAL_IF_FAIL(p_cmd != NULL, FAILURE);

    p_cmd->data_len = 0;
    RETURN_VAL_IF_FAIL(TRUE == apptest_flag, FAILURE);

    p_buf = p_cmd->buf;
    RETURN_VAL_IF_FAIL(p_buf != NULL, FAILURE);

    offset++;
    rt_memcpy_s(&s_production_info.eeprom_power_loss_test[0], sizeof(s_production_info.eeprom_power_loss_test), &p_buf[offset], 8);
    offset += 8;

    eeprom_storage_write((uint8_t *)&s_production_info, sizeof(s_production_info), PART_PRODUCTINFO, TRUE);
    sync_product_info_to_data_id(&s_production_info);

    return SUCCESSFUL;
}


/**
 * @brief apptest 获取eeprom掉电保存参数
*/

Static int pack_get_E2_powerdown_save_info_apptest(void* dev_inst, void* cmd_buf)
{
    unsigned char* p_buf = NULL;
    cmd_buf_t* p_cmd = cmd_buf;
    unsigned char offset = 0;

    RETURN_VAL_IF_FAIL(p_cmd != NULL, FAILURE);

    p_cmd->data_len = 0;
    RETURN_VAL_IF_FAIL(TRUE == apptest_flag, FAILURE);

    p_buf = p_cmd->buf;
    RETURN_VAL_IF_FAIL(p_buf != NULL, FAILURE);

    offset++;
    rt_memcpy_s(&p_buf[offset], 8, &s_production_info.eeprom_power_loss_test[0], 8);
    offset += 8;

    p_cmd->data_len = offset;
    p_buf[0] = offset - 1;  // 字节长度

    return SUCCESSFUL;
}


Static int pack_get_real_data_info(void* dev_inst, void* cmd_buf)
{
    unsigned char* p_buf = NULL;
    float terminal_voltage, bus_voltage ,current,contact_temperature,internal_temperature,bus_temperature= 0;
    unsigned char offset = 0;

    RETURN_VAL_IF_FAIL(apptest_flag == TRUE, FAILURE);
    RETURN_VAL_IF_FAIL(cmd_buf != NULL, FAILURE);
    cmd_buf_t* p_cmd = cmd_buf;

    RETURN_VAL_IF_FAIL(p_cmd->buf != NULL, FAILURE);
    p_buf = p_cmd->buf;
    offset++;

    get_one_data(SWITCH_DATA_ID_TERMINAL_VOLTAGE, &terminal_voltage);
    put_int16_to_buff(&p_buf[offset], FLOAT_TO_SHORT(terminal_voltage * 100));
    offset += 2;

    get_one_data(SWITCH_DATA_ID_BUS_VOLTAGE, &bus_voltage);
    put_int16_to_buff(&p_buf[offset], FLOAT_TO_SHORT(bus_voltage * 100));
    offset += 2;

    get_one_data(SWITCH_DATA_ID_CURRENT, &current);
    put_int32_to_buff(&p_buf[offset], (int)(current * 100));
    offset += 4;

    get_one_data(SWITCH_DATA_ID_CONTACT_TEMPERATURE, &contact_temperature);
    put_int16_to_buff(&p_buf[offset], FLOAT_TO_SHORT(contact_temperature * 10));
    offset += 2;

    get_one_data(SWITCH_DATA_ID_INTERNAL_TEMPERATURE, &internal_temperature);
    put_int16_to_buff(&p_buf[offset], FLOAT_TO_SHORT(internal_temperature * 10));
    offset += 2;

    get_one_data(SWITCH_DATA_ID_BUS_TEMPERATURE, &bus_temperature);
    put_int16_to_buff(&p_buf[offset], FLOAT_TO_SHORT(bus_temperature * 10));
    offset += 2;

    p_cmd->data_len = offset;
    p_buf[0] = offset - 1;  // 字节长度

    return SUCCESSFUL;
}

Static int pack_get_digital_data(void* dev_inst, void* cmd_buf)
{
    unsigned char* p_buf = NULL;
    unsigned char power_reverse_status, close_pos_micro_status ,free_pos_micro_status,fin_pos_micro_status= 0;
    unsigned char offset = 0;

    RETURN_VAL_IF_FAIL(apptest_flag == TRUE, FAILURE);
    RETURN_VAL_IF_FAIL(cmd_buf != NULL, FAILURE);
    cmd_buf_t* p_cmd = cmd_buf;

    RETURN_VAL_IF_FAIL(p_cmd->buf != NULL, FAILURE);
    p_buf = p_cmd->buf;
    offset++;

    get_one_data(SWITCH_DATA_ID_SWITCH_POWER_REVERSE_STATUS, &power_reverse_status);
    get_one_data(SWITCH_DATA_ID_CLOSE_POS_MICRO_STATUS, &close_pos_micro_status);
    get_one_data(SWITCH_DATA_ID_FREE_POS_MICRO_STATUS, &free_pos_micro_status);
    get_one_data(SWITCH_DATA_ID_CLOSE_FIN_POS_MICRO_STATUS, &fin_pos_micro_status);   

    unsigned char digital_data =  (fin_pos_micro_status << 3) | (free_pos_micro_status << 2) | (close_pos_micro_status << 1) | power_reverse_status;
    p_buf[offset++] = digital_data;

    p_cmd->data_len = offset;
    p_buf[0] = offset - 1;  // 字节长度

    return SUCCESSFUL;
    
}

Static int pack_get_address(void* dev_inst, void* cmd_buf)
{
    unsigned char* p_buf = NULL;
    unsigned char internal_address, group_address ,sw_slot_address = 0;
    unsigned char offset = 0;

    RETURN_VAL_IF_FAIL(apptest_flag == TRUE, FAILURE);
    RETURN_VAL_IF_FAIL(cmd_buf != NULL, FAILURE);
    cmd_buf_t* p_cmd = cmd_buf;

    RETURN_VAL_IF_FAIL(p_cmd->buf != NULL, FAILURE);
    p_buf = p_cmd->buf;
    offset++;

    get_one_data(SWITCH_DATA_ID_GROUP_INTERNAL_ADDRESS, &internal_address);
    get_one_data(SWITCH_DATA_ID_GROUP_ADDRESS, &group_address);
    get_one_data(SWITCH_DATA_ID_SW_SLOT_ADDRESS, &sw_slot_address);

    p_buf[offset++] = internal_address;
    p_buf[offset++] = group_address;
    p_buf[offset++] = sw_slot_address;
 
    p_cmd->data_len = offset;
    p_buf[0] = offset - 1;  // 字节长度

    return SUCCESSFUL;
}

/**
 * @brief 呼叫表2
*/

Static int pack_get_real_data_info_2(void* dev_inst, void* cmd_buf)
{
    unsigned char* p_buf = NULL;
    cmd_buf_t* p_cmd = cmd_buf;
    float f_value = 0;
    unsigned char offset = 1;

    RETURN_VAL_IF_FAIL(p_cmd != NULL, FAILURE);
    p_buf = p_cmd->buf;
    RETURN_VAL_IF_FAIL(p_buf != NULL, FAILURE);

    get_one_data(SWITCH_DATA_ID_CURRENT, &f_value);
    put_int32_to_buff(&p_buf[offset], (int)(f_value * 100));
    offset += 4;

    p_cmd->data_len = offset;
    p_buf[0] = offset - 1;  // 字节长度

    return SUCCESSFUL;
}


/**
 * @brief 获取硬件版本
*/

Static int pack_get_hardware_ver(void* dev_inst, void* cmd_buf)
{
    unsigned char* p_buf = NULL;
    cmd_buf_t* p_cmd = cmd_buf;
    unsigned char offset = 0;

    RETURN_VAL_IF_FAIL(p_cmd != NULL, FAILURE);
    p_buf = p_cmd->buf;
    RETURN_VAL_IF_FAIL(p_buf != NULL, FAILURE);

    offset++;
    rt_memcpy_s(&p_buf[offset], HW_VER_LEN, &s_production_info.hardware_version[0], HW_VER_LEN);
    offset += HW_VER_LEN;

    p_cmd->data_len = offset;
    p_buf[0] = offset - 1;

    return SUCCESSFUL;
}


Static int pack_get_board_sn(void* dev_inst, void* cmd_buf)
{
    unsigned char* p_buf = NULL;
    cmd_buf_t* p_cmd = cmd_buf;
    unsigned char offset = 0;

    RETURN_VAL_IF_FAIL(p_cmd != NULL, FAILURE);
    p_buf = p_cmd->buf;
    RETURN_VAL_IF_FAIL(p_buf != NULL, FAILURE);

    offset++;
    rt_memcpy_s(&p_buf[offset], HW_VER_LEN, &s_production_info.board_barcode[0], HW_VER_LEN);
    offset += BOARD_SN_LEN;

    p_cmd->data_len = offset;
    p_buf[0] = offset - 1;

    return SUCCESSFUL;
}


Static int parse_set_hardware_version(cmd_buf_t* cmd_buf) 
{
    unsigned char ret = 0;
    unsigned char hw_ver_len_12 = 12+1;
    unsigned char hw_ver_len_15 = 15+1;

    RETURN_VAL_IF_FAIL(cmd_buf != NULL, FAILURE);

    if((cmd_buf->data_len != hw_ver_len_12 ) && (cmd_buf->data_len != hw_ver_len_15))
    {
        cmd_buf->rtn = BOTTOM_RTN_DATA_ERROR;
        return FAILURE;
    }

    for (int i = 1; i <= 12; i++)
    {
        if (!(cmd_buf->buf[i] >= '0' && cmd_buf->buf[i] <= '9'))
        {
            cmd_buf->rtn = BOTTOM_RTN_DATA_ERROR;
            return FAILURE;
        }
    }
    
    for (int i = 13; i <= 15; i++)
    {
        if (cmd_buf->buf[i] == '\0')
        {
            break;
        }
        if(!((cmd_buf->buf[i] >= 'A' && cmd_buf->buf[i] <= 'Z') || (cmd_buf->buf[i] >= 'a' && cmd_buf->buf[i] <= 'z')))
        {
            cmd_buf->rtn = BOTTOM_RTN_DATA_ERROR;
            return FAILURE;
        }
    }

    rt_memcpy_s(&s_production_info.hardware_version[0], sizeof(s_production_info.hardware_version), &cmd_buf->buf[1], HW_VER_LEN);
    eeprom_storage_write((uint8_t *)&s_production_info, sizeof(s_production_info), PART_PRODUCTINFO, TRUE);
    sync_product_info_to_data_id(&s_production_info);

    cmd_buf->rtn = BOTTOM_RTN_APP_CORRECT;

    return ret;
}



Static int airswitch_link(void* dev_inst, void* cmd_buf) 
{
    unsigned char *buff = NULL;
    unsigned short offset = 1;
    date_base_t date = {0};
    unsigned short manufacture_day_num = 0;
    float rated = 0;

    // 检查cmd_buf是否为空
    RETURN_VAL_IF_FAIL(cmd_buf != NULL, FAILURE);

    cmd_buf_t* s_cmd_buf = (cmd_buf_t*)cmd_buf;
    buff = s_cmd_buf->buf;

    // 检查buff是否为空
    RETURN_VAL_IF_FAIL(buff != NULL, FAILURE);

    // 获取系统名称并写入缓冲区
    rt_memcpy_s(&buff[offset], 32, &s_production_info.breaker_system_name[0], 32);
    offset += 32;

    // 获取软件版本并写入缓冲区
    get_one_data(SWITCH_DATA_ID_SOFTWARE_VERSION, &buff[offset]);
    offset += 20;

    // 获取软件发布日期并写入缓冲区
    get_one_data(SWITCH_DATA_ID_SOFTWARE_RELEASE_DATE, &date);
    put_date_to_buff(&buff[offset], date);
    offset += sizeof(date_base_t);

    // 获取序列号标志并写入缓冲区
    buff[offset++] = s_production_info.serial_number_flag;

    // 获取制造年份并写入缓冲区
    put_uint16_to_buff(&buff[offset], s_production_info.manufacturing_year);
    offset += 2;

    // 获取制造月份并写入缓冲区
    buff[offset++] = s_production_info.manufacturing_month;

    // 获取制造日并写入缓冲区
    buff[offset++] = s_production_info.manufacturing_day;

    // 获取制造天数并写入缓冲区
    manufacture_day_num = s_production_info.manufacturing_day_sequence;
    put_uint16_to_buff(&buff[offset], manufacture_day_num);
    offset += 2;

    // 获取规格并写入缓冲区
    buff[offset++] = s_production_info.breaker_specification;

    // 获取额定电流并写入缓冲区
    get_one_data(SWITCH_DATA_ID_RATED_CURRENT, &rated);
    put_int16_to_buff(&buff[offset], FLOAT_TO_SHORT(rated * 100));
    offset += 2;

    // 设置数据长度
    buff[0] = offset - 1;
    s_cmd_buf->data_len = offset;
    return SUCCESSFUL;
}



Static int airswitch_get_realdata(void* dev_inst, void* cmd_buf)
{
    unsigned char *buff = NULL;
    unsigned char i;
    unsigned char datathree[8] = {0};
    unsigned char datafour[8] = {0};
    unsigned short offset = 2;
    int batch_parameter1_crc = 0;
    float output_voltage = 0;
    float current = 0;
    float power = 0;
    float contact_temperature = 0;
    float internal_temperature = 0;
    short fault_opening_times = 0;
    int input_power_energy = 0;
    int output_power_energy = 0;

    // 检查cmd_buf是否为空
    RETURN_VAL_IF_FAIL(cmd_buf != NULL, FAILURE);

    cmd_buf_t* s_cmd_buf = (cmd_buf_t*)cmd_buf;
    buff = s_cmd_buf->buf;

    // 检查buff是否为空
    RETURN_VAL_IF_FAIL(buff != NULL, FAILURE);

    // 设置缓冲区的初始值
    buff[0] = 3;
    buff[1] = 127;

    // 获取并设置批次参数1的CRC值
    batch_parameter1_crc = report_airswitch_para_crc();
    put_uint16_to_buff(&buff[offset], batch_parameter1_crc);
    offset += 2;

    // 设置一个固定值
    buff[offset++] = 22;

    // 获取并设置输出电压
    get_one_data(SWITCH_DATA_ID_OUTPUT_VOLTAGE, &output_voltage);
    put_int16_to_buff(&buff[offset], FLOAT_TO_SHORT(output_voltage * 100));
    offset += 2;

    // 获取并设置电流
    get_one_data(SWITCH_DATA_ID_CURRENT, &current);
    put_int16_to_buff(&buff[offset], FLOAT_TO_SHORT(current * 100));
    offset += 2;

    // 获取并设置功率
    get_one_data(SWITCH_DATA_ID_POWER, &power);
    put_int16_to_buff(&buff[offset], FLOAT_TO_SHORT(power * 1000));
    offset += 2;

    // 获取并设置输入功率能量
    get_one_data(SWITCH_DATA_ID_INPUT_POWER_ENERGY, &input_power_energy);
    put_int32_to_buff(&buff[offset], input_power_energy);
    offset += 4;

    // 获取并设置输出功率能量
    get_one_data(SWITCH_DATA_ID_OUTPUT_POWER_ENERGY, &output_power_energy);
    put_int32_to_buff(&buff[offset], output_power_energy);
    offset += 4;

    // 获取并设置接触温度
    get_one_data(SWITCH_DATA_ID_CONTACT_TEMPERATURE, &contact_temperature);
    put_int16_to_buff(&buff[offset], FLOAT_TO_SHORT(contact_temperature * 10));
    offset += 2;

    // 获取并设置内部温度
    get_one_data(SWITCH_DATA_ID_INTERNAL_TEMPERATURE, &internal_temperature);
    put_int16_to_buff(&buff[offset], FLOAT_TO_SHORT(internal_temperature * 10));
    offset += 2;

    // 获取并设置故障跳闸次数
    get_one_para(SWITCH_PARA_ID_FAULT_OPENING_TIMES_OFFSET, &fault_opening_times);
    put_int16_to_buff(&buff[offset], fault_opening_times);
    offset += 2;

    // 获取并设置组内地址
    get_one_data(SWITCH_DATA_ID_GROUP_INTERNAL_ADDRESS, &buff[offset++]);

    // 获取并设置组地址
    get_one_data(SWITCH_DATA_ID_GROUP_ADDRESS, &buff[offset++]);

    // 设置一个固定值
    buff[offset++] = 1;

    // 获取并设置P2P状态、电源状态、上电授权、下载状态和循环状态
    get_one_data(SWITCH_DATA_ID_P2P_STATUS, &datathree[3]);
    get_one_data(SWITCH_DATA_ID_POWER_STATUS, &datathree[4]);
    get_one_para(SWITCH_PARA_ID_POWER_ON_AUTHORIZATION_OFFSET, &datathree[5]);
    get_one_data(SWITCH_DATA_ID_DOWNLOAD_STATUS, &datathree[6]);
    get_one_data(SWITCH_DATA_ID_LOOP_STATUS, &datathree[7]);
    
    for (i = 0; i < 8; i++)
    {
        buff[offset] = (buff[offset] * 2) | datathree[i];
    }
    offset++;

    // 设置一个固定值
    buff[offset++] = 2;

    // 获取并设置过载跳闸报警、过温保护报警、过流保护报警、控制器故障报警、反接报警
    get_one_data(SWITCH_DATA_ID_OVERLOAD_TRIP_ALARM, &datafour[3]);
    get_one_data(SWITCH_DATA_ID_OVER_TEMPERATURE_PROTECT_ALARM, &datafour[4]);
    get_one_data(SWITCH_DATA_ID_OVER_CURRENT_PROTECT_ALARM, &datafour[5]);
    get_one_data(SWITCH_DATA_ID_CONTROLLER_FAILURE_ALARM, &datafour[6]);
    get_one_data(SWITCH_DATA_ID_CONNECTED_REVERSELY_ALARM, &datafour[7]);
    for (i = 0; i < 8; i++)
    {
        buff[offset] = (buff[offset] * 2) | datafour[i];
    }
    offset++;

    // 获取并设置开路故障报警、闭路故障报警、掉电报警、欠压掉电报警、内部过温报警、接触过温报警、断开报警、过流报警
    get_one_data(SWITCH_DATA_ID_OPENING_FAILURE_ALARM, &datafour[0]);
    get_one_data(SWITCH_DATA_ID_CLOSE_FAILURE_ALARM, &datafour[1]);
    get_one_data(SWITCH_DATA_ID_DOWN_POWER_ALARM, &datafour[2]);
    get_one_data(SWITCH_DATA_ID_LOW_VOLTAGE_DOWN_POWER_ALARM, &datafour[3]);
    get_one_data(SWITCH_DATA_ID_INTERNAL_OVER_TEMPERATURE_ALARM, &datafour[4]);
    get_one_data(SWITCH_DATA_ID_CONTACT_OVER_TEMPERATURE_ALARM, &datafour[5]);
    get_one_data(SWITCH_DATA_ID_DISCONNECTION_ALARM, &datafour[6]);
    get_one_data(SWITCH_DATA_ID_OVER_CURRENT_ALARM, &datafour[7]);
    for (i = 0; i < 8; i++)
    {
        buff[offset] = (buff[offset] * 2) | datafour[i];
    }
    offset++;

    // 设置命令缓冲区的数据长度
    s_cmd_buf->data_len = offset;

    return SUCCESSFUL;
}



Static int airswitch_get_table_1(void* dev_inst, void* cmd_buf)
{
    unsigned char *buff = NULL;
    unsigned short offset = 1;
    unsigned short over_current_protect_delay = 0;
    unsigned short over_current_protect_threshold = 0;
    float power_down_voltage_threshold = 0;
    float power_on_voltage_threshold = 0;
    float over_current_alarm_threshold = 0;

    // 检查cmd_buf是否为空
    RETURN_VAL_IF_FAIL(cmd_buf != NULL, FAILURE);

    cmd_buf_t* s_cmd_buf = (cmd_buf_t*)cmd_buf;
    buff = s_cmd_buf->buf;

    // 检查buff是否为空
    RETURN_VAL_IF_FAIL(buff != NULL, FAILURE);

    // 设置缓冲区的第一个字节为16
    buff[0] = 16;
    
    // 获取并设置掉电使能参数
    get_one_para(SWITCH_PARA_ID_POWER_DOWN_ENABLED_OFFSET, &buff[offset++]);

    // 获取并设置掉电电压阈值
    get_one_para(SWITCH_PARA_ID_POWER_DOWN_VOLTAGE_THRESHOLD_OFFSET, &power_down_voltage_threshold);
    put_uint16_to_buff(&buff[offset], FLOAT_TO_SHORT(power_down_voltage_threshold * 100));
    offset += 2;

    // 获取并设置上电电压阈值
    get_one_para(SWITCH_PARA_ID_POWER_ON_VOLTAGE_THRESHOLD_OFFSET, &power_on_voltage_threshold);
    put_uint16_to_buff(&buff[offset], FLOAT_TO_SHORT(power_on_voltage_threshold * 100));
    offset += 2;

    // 获取并设置过流保护使能参数
    get_one_para(SWITCH_PARA_ID_OVER_CURRENT_PROTECT_ENABLED_OFFSET, &buff[offset++]);

    // 获取并设置过流报警阈值
    get_one_para(SWITCH_PARA_ID_OVER_CURRENT_ALARM_THRESHOLD_OFFSET, &over_current_alarm_threshold);
    put_uint16_to_buff(&buff[offset], FLOAT_TO_SHORT(over_current_alarm_threshold * 100));
    offset += 2;

    // 获取并设置上电授权参数
    get_one_para(SWITCH_PARA_ID_POWER_ON_AUTHORIZATION_OFFSET, &buff[offset++]);

    // 获取并设置过流保护延迟时间
    get_one_para(SWITCH_PARA_ID_OVER_CURRENT_PROTECT_DELAY_OFFSET, &over_current_protect_delay);
    put_uint16_to_buff(&buff[offset], over_current_protect_delay);
    offset += 2;

    // 获取并设置过流保护自动恢复次数
    get_one_para(SWITCH_PARA_ID_OVER_CURRENT_PROTECT_AUTO_RECOVERY_TIMES_OFFSET, &buff[offset++]);

    // 获取并设置电流保护自动恢复间隔
    get_one_para(SWITCH_PARA_ID_CURRENT_PROTECT_AUTO_RECOVERY_SPACE_OFFSET, &buff[offset++]);

    // 获取并设置过流保护阈值
    get_one_para(SWITCH_PARA_ID_OVER_CURRENT_PROTECT_THRESHOLD_OFFSET, &over_current_protect_threshold);
    put_uint16_to_buff(&buff[offset], over_current_protect_threshold);
    offset += 2;

    // 获取并设置断开判断模式
    get_one_para(SWITCH_PARA_ID_DISCONNECT_JUDGMENT_MODE_OFFSET, &buff[offset++]);

    // 设置数据长度
    s_cmd_buf->data_len = offset;

    return SUCCESSFUL;
}



Static int airswitch_set_table_1(void* dev_inst, void* cmd_buf)
{
    unsigned char *buff = NULL;
    unsigned short offset = 0;
    float power_down_voltage_threshold = 0;
    float power_on_voltage_threshold = 0;
    float over_current_alarm_threshold = 0;
    unsigned short over_current_protect_delay = 0;
    unsigned short over_current_protect_threshold = 0;
    float rated_current = 0.0;

    // 检查cmd_buf是否为空
    RETURN_VAL_IF_FAIL(cmd_buf != NULL, FAILURE);

    cmd_buf_t* s_cmd_buf = (cmd_buf_t*)cmd_buf;
    buff = s_cmd_buf->buf;

    // 检查buff是否为空
    RETURN_VAL_IF_FAIL(buff != NULL, FAILURE);

    // 跳过第一个字节
    offset++;

    // 设置掉电使能参数
    set_one_para(SWITCH_PARA_ID_POWER_DOWN_ENABLED_OFFSET, &buff[offset++], TRUE, FALSE);

    // 获取并设置掉电电压阈值
    power_down_voltage_threshold = (get_int16_data(&buff[offset]) / 100.0f);
    set_one_para(SWITCH_PARA_ID_POWER_DOWN_VOLTAGE_THRESHOLD_OFFSET, &power_down_voltage_threshold, TRUE, FALSE);
    offset += 2;

    // 获取并设置上电电压阈值
    power_on_voltage_threshold = (get_int16_data(&buff[offset]) / 100.0f);
    set_one_para(SWITCH_PARA_ID_POWER_ON_VOLTAGE_THRESHOLD_OFFSET, &power_on_voltage_threshold, TRUE, FALSE);
    offset += 2;

    // 设置过流保护使能参数
    set_one_para(SWITCH_PARA_ID_OVER_CURRENT_PROTECT_ENABLED_OFFSET, &buff[offset++], TRUE, FALSE);

    // 获取并设置过流报警阈值
    over_current_alarm_threshold = (get_uint16_data(&buff[offset]) / 100.0f);
    get_one_data(SWITCH_DATA_ID_RATED_CURRENT, &rated_current);

    if (over_current_alarm_threshold < OC_THRESHOLD_MIN)
    {
        over_current_alarm_threshold = OC_THRESHOLD_MIN;
    }
    else if (over_current_alarm_threshold > rated_current)
    {
        over_current_alarm_threshold = rated_current;
    }
    
    set_one_para(SWITCH_PARA_ID_OVER_CURRENT_ALARM_THRESHOLD_OFFSET, &over_current_alarm_threshold, TRUE, FALSE);
    offset += 2;

    // 设置上电授权参数
    set_one_para(SWITCH_PARA_ID_POWER_ON_AUTHORIZATION_OFFSET, &buff[offset++], TRUE, FALSE);

    // 获取并设置过流保护延迟时间
    over_current_protect_delay = get_int16_data(&buff[offset]);
    set_one_para(SWITCH_PARA_ID_OVER_CURRENT_PROTECT_DELAY_OFFSET, &over_current_protect_delay, TRUE, FALSE);
    offset += 2;

    // 设置过流保护自动恢复次数
    set_one_para(SWITCH_PARA_ID_OVER_CURRENT_PROTECT_AUTO_RECOVERY_TIMES_OFFSET, &buff[offset++], TRUE, FALSE);

    // 设置电流保护自动恢复间隔
    set_one_para(SWITCH_PARA_ID_CURRENT_PROTECT_AUTO_RECOVERY_SPACE_OFFSET, &buff[offset++], TRUE, FALSE);

    // 获取并设置过流保护阈值
    over_current_protect_threshold = get_int16_data(&buff[offset]);
    set_one_para(SWITCH_PARA_ID_OVER_CURRENT_PROTECT_THRESHOLD_OFFSET, &over_current_protect_threshold, TRUE, FALSE);
    offset += 2;

    // 设置断开判断模式
    set_one_para(SWITCH_PARA_ID_DISCONNECT_JUDGMENT_MODE_OFFSET, &buff[offset++], TRUE, FALSE);

    // 设置数据长度
    s_cmd_buf->data_len = offset;
    buff[0] = offset - 1;  // 字节长度

    return SUCCESSFUL;
}



Static int airswitch_get_table_2(void* dev_inst, void* cmd_buf)
{
    unsigned char *buff = NULL;
    unsigned short offset = 1;
    int input_electricity_benchmark_parameters = 0;
    int output_electricity_benchmark_parameters = 0;
    // 检查cmd_buf是否为空
    RETURN_VAL_IF_FAIL(cmd_buf != NULL, FAILURE);
    cmd_buf_t* s_cmd_buf = (cmd_buf_t*)cmd_buf;
    buff = s_cmd_buf->buf;
    // 检查buff是否为空
    RETURN_VAL_IF_FAIL(buff != NULL, FAILURE);

    // 获取并设置输入电量基准参数
    get_one_para(SWITCH_PARA_ID_INPUT_ELECTRICITY_BENCHMARK_PARAMETERS_OFFSET, &input_electricity_benchmark_parameters);
    put_int32_to_buff(&buff[offset], input_electricity_benchmark_parameters);
    offset += 4;

    // 获取并设置输出电量基准参数
    get_one_para(SWITCH_PARA_ID_OUTPUT_ELECTRICITY_BENCHMARK_PARAMETERS_OFFSET, &output_electricity_benchmark_parameters);
    put_int32_to_buff(&buff[offset], output_electricity_benchmark_parameters);
    offset += 4;

    // 设置数据长度
    s_cmd_buf->data_len = offset;
    buff[0] = offset - 1;  // 字节长度
    return SUCCESSFUL;
}



Static int airswitch_set_table_2(void* dev_inst, void* cmd_buf)
{
    unsigned char *buff = NULL;
    unsigned short offset = 1;
    int input_electricity_benchmark_parameters = 0;
    int output_electricity_benchmark_parameters = 0;
    // 检查cmd_buf是否为空
    RETURN_VAL_IF_FAIL(cmd_buf != NULL, FAILURE);
    cmd_buf_t* s_cmd_buf = (cmd_buf_t*)cmd_buf;
    buff = s_cmd_buf->buf;  
    // 检查buff是否为空
    RETURN_VAL_IF_FAIL(buff != NULL, FAILURE);
    // 设置输入电量基准参数
    input_electricity_benchmark_parameters = get_long_data(&buff[offset]);
    set_one_para(SWITCH_PARA_ID_INPUT_ELECTRICITY_BENCHMARK_PARAMETERS_OFFSET, &input_electricity_benchmark_parameters, TRUE, FALSE);
    offset += 4;
    // 设置输出电量基准参数
    output_electricity_benchmark_parameters = get_long_data(&buff[offset]);
    set_one_para(SWITCH_PARA_ID_OUTPUT_ELECTRICITY_BENCHMARK_PARAMETERS_OFFSET, &output_electricity_benchmark_parameters, TRUE, FALSE);
    offset += 4;

    // 设置数据长度
    s_cmd_buf->data_len = offset;
    buff[0] = offset - 1;  // 字节长度

    set_one_data(SWITCH_DATA_ID_INPUT_POWER_ENERGY, &input_electricity_benchmark_parameters);
    set_one_data(SWITCH_DATA_ID_OUTPUT_POWER_ENERGY, &output_electricity_benchmark_parameters);
    save_power_energy();    // 保存电量信息到eeprom中

    return SUCCESSFUL;
}


Static int pack_get_debug_info(void* dev_inst, void* cmd_buf)
{
    unsigned char *buff = NULL;
    unsigned short offset = 0;

    // 检查cmd_buf是否为空
    RETURN_VAL_IF_FAIL(cmd_buf != NULL, FAILURE);
    cmd_buf_t* s_cmd_buf = (cmd_buf_t*)cmd_buf;
    buff = s_cmd_buf->buf;
    // 检查buff是否为空
    RETURN_VAL_IF_FAIL(buff != NULL, FAILURE);

    // 设置缓冲区的第一个字节为5
    buff[offset++] = 5;
    // 获取重启原因
    buff[offset++] = (unsigned char)get_reset_reason();
    // 获取小电流引起的控制器故障标志
    get_one_para(SWITCH_PARA_ID_CONTROLLER_FAULT_REASON_OFFSET, &buff[offset++]);
    // 获取上下电命令
    get_one_para(SWITCH_PARA_ID_POWER_ON_POWER_DOWN_COMMAND_OFFSET, &buff[offset++]);
    // 获取空开断开状态
    get_one_data(SWITCH_DATA_ID_DISCONNECT_STATUS, &buff[offset++]);
    // 获取过流保护已恢复次数
    get_one_para(SWITCH_PARA_ID_BEFORE_AUTO_RECOVERY_COUNT_OFFSET, &buff[offset++]);
    // 设置数据长度
    s_cmd_buf->data_len = offset;

    return SUCCESSFUL;
}

Static int deal_adjust_motor_sligntly_debug(void* dev_inst, void* cmd_buf)
{
    unsigned char *buff = NULL;
    unsigned short offset = 1;
    unsigned char speed = 0, direction = 0;

     // 检查cmd_buf是否为空
    RETURN_VAL_IF_FAIL(cmd_buf != NULL, FAILURE);
    cmd_buf_t* s_cmd_buf = (cmd_buf_t*)cmd_buf;
    buff = s_cmd_buf->buf;  
    // 检查buff是否为空
    RETURN_VAL_IF_FAIL(buff != NULL, FAILURE);

    direction = buff[offset++];
    speed = buff[offset++];
    if(FAILURE == motor_ctrl_debug(speed,direction))
    {
        s_cmd_buf->rtn = BOTTOM_RTN_CMD_ERROR;
        return FAILURE; 
    }
    s_cmd_buf->rtn = BOTTOM_RTN_APP_CORRECT;

    // 设置数据长度
    s_cmd_buf->data_len = offset;
    buff[0] = offset - 1;  // 字节长度
    return SUCCESSFUL;
}

Static char clear_all_alarm()
{
    unsigned char status = 0;

    unauthorization_clear_alarm();
    // 分闸失败告警和控制器故障告警单独清除
    clear_controller_fault_alarm();
    set_one_data(SWITCH_DATA_ID_OPENING_FAILURE_ALARM, &status); 
    
    return SUCCESSFUL;
}