#include <rtthread.h>
#include "data_type.h"
#include "app_config.h"
#include "type_define_in.h"
#include "para_manage.h"
#include "sys_para_in.h"
#include "alarm_para_in.h"
#include "gui_data_interface.h"
#include "MIBTable.h"

static unsigned short s_config_exclude_list[MAX_EXCLUDE_CONFIG_PARA_NUM] = {0};
static unsigned short s_runtime_exclude_list[MAX_EXCLUDE_RUNNING_PARA_NUM] = {0};
Static exclude_para_info_t s_exclude_config_info = {0};
Static exclude_para_info_t s_exclude_runtime_info = {0};


static para_manage_info_t acmu_para_manage = {
    SYS_NUMERIC_PARA_MAX_OFFSET,
    SYS_STRING_PARA_MAX_OFFSET,
    ALARM_ID_OFFSET_MAX,
    numeric_para_attr_tab,
    scope_tab,
    constraint_tab,
    numeric_val_tab,
    string_para_attr_tab,
    string_val_tab,
    alarm_attr_tab,
    alarm_attr_val_tab,
};

void register_product_para(void) {
    int exclude_config_count = get_exclude_para_ids_by_type(PARATYPE_CONFIG, s_config_exclude_list, MAX_EXCLUDE_CONFIG_PARA_NUM);
    int exclude_runtime_count = get_exclude_para_ids_by_type(PARATYPE_RUNNING, s_runtime_exclude_list, MAX_EXCLUDE_RUNNING_PARA_NUM);

    if (exclude_runtime_count == -1 || exclude_config_count == -1) {
        rt_kprintf("register_product_para error!! get_exclude_para_ids_by_type error");
        return;
    }

    s_exclude_config_info.sid = s_config_exclude_list;
    s_exclude_config_info.sid_num = exclude_config_count;
    s_exclude_runtime_info.sid = s_runtime_exclude_list;
    s_exclude_runtime_info.sid_num = exclude_runtime_count;

    register_exclude_para(EXCLUDE_TYPE_CONFIG, &s_exclude_config_info);
    register_exclude_para(EXCLUDE_TYPE_RUNTIME, &s_exclude_runtime_info);

    register_para_manage_info(&acmu_para_manage);
}