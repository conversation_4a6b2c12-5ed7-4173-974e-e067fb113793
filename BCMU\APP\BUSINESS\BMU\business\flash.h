#ifndef  BMU_FLASH_H
#define  BMU_FLASH_H

#include <stdio.h>
#include "gd32f403.h"
#include "gd32f403_fmc.h"

#define GD32F4_FLASH_BASE              (0x08000000UL)
#define UPDATEINFO_START               (0x08006000UL)
#define FLAG_PROGRAM                   (0x08007200UL) //烧写标记
#define APPLICATION_ADDRESS            (0x08008000UL)
#define BACKUP_ADDRESS                 (0x08080000UL)
#define GD32F4_FLASH_END_ADDR          (0x08100000UL)
#define NORFLASH_APP_START             (0x00000000UL) // norflash的起始地址处
#define MSC_MAX_PACKET  256
#define MAX_PACKET_NUM  1920

#ifdef UNITEST
static uint8_t mock_flag_program = 0XFF;
#define FLAG_PROGRAM                 (&mock_flag_program) //升级标记
#else
#define FLAG_PROGRAM                  (0x08007200UL) //升级标记
#endif

#define    FILE_NAME_LEN    40
#define    FILE_TIME_LEN    20
#define    FLAG_IAP        0x55    //待升级
#define    FLAG_CAN_IAP    0x56    //待升级CAN
#define    FLAG_NOR_IAP    0x57    //待从NorFlash升级
#define    FLAG_APP        0x66    //升级结束
#define    FLAG_RST        0x77    //升级结束待重启
#define    FLAG_BACKUP     0x88    //运行正常待备份
#define    FLAG_OK         0x99    //已备份

#define FMC_PAGE_SIZE 	400

#define NORFLASH_DEV_NAME "norflash0"

#define USER_FLASH_END_ADDRESS        0x080FFFFF

/* Error code */
typedef enum 
{
  FLASHIF_OK = 0,
  FLASHIF_ERASE_ERROR,
  FLASHIF_WRITE_ERROR,
  FLASHIF_READ_ERROR
} flash_error_status;

typedef union{
	struct{
		uint8_t data1;
		uint8_t data2;
		uint8_t data3;
		uint8_t data4;
	}bf;
	uint32_t data;
}fmc_data_t;

#pragma pack(4)
typedef struct
{
    uint16_t    wDataLenPerFrame;
    uint16_t    wTotalFrameNum;
    uint32_t    ulTotalFileLength;
    uint8_t     acFileName[FILE_NAME_LEN];
    uint8_t     acFileTime[FILE_TIME_LEN];
    uint16_t    wFileCheck;
    uint16_t    wResv;
} T_FileAttrStruct;

typedef struct
{
  T_FileAttrStruct tFileAttr;
  uint8_t  ucFlag;
  uint8_t	 ucUpdateAddr;
  uint16_t wCounter;
  uint32_t wBaudRate;
  uint16_t wBackupFrameNum;
  uint16_t wCrc;
} T_FileManageStruct;
#pragma pack()
uint16_t CheckFlashValue(uint32_t FlashAddress,uint32_t* Data,uint32_t i);
flash_error_status FLASH_If_Read(uint32_t offset, uint8_t *buf, uint32_t size);
flash_error_status FLASH_If_Write(uint32_t FlashAddress, uint32_t* Data ,uint32_t DataLength);
#endif /* BMU_FLASH_H */
