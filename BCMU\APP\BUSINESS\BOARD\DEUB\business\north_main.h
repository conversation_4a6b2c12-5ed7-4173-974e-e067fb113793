/**
 * @brief 604a main板北向通信头文件
 */

#ifndef _DEUB_NORTH_MAIN_H_
#define _DEUB_NORTH_MAIN_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "cmd.h"
#include "linklayer.h"
#include "protocol_layer.h"
#include "pb_encode.h"
#include "pb.h"


#define MAX_NORTH_COMM_BUFF_LEN  512
#define MAX_DEV_INST_NUM         3
#define MODBUS_PROTOCOL_INDEX    (MAX_DEV_INST_NUM - 1)

#define DEUB_DEFAULT_DEV_ADDR        31

typedef struct {
    void*        data;
    cmd_buf_t*   cmd_buff;
    link_inst_t* link_inst;
    rt_mq_t      north_mq;
}north_mgr_t;

void*  init_north(void * param);
void north_comm_th(void *param);

#ifdef __cplusplus
}
#endif      //< __cplusplus

#endif      //< _DEUB_NORTH_MAIN_H_
