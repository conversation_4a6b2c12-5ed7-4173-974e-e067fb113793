/*
 * @file    : bmu_zte_can.c
 * @brief   : BMU与BCMU北向CAN通信协议实现
 * @details : 
 * <AUTHOR> 付振10303717
 * @Date    : 2022-12-26 
 * @LastEditTime: 2023-04-04
 * @version : V0.0.1
 * @para    : Copyright (c) 
 *            ZTE Corporation
 * @par     : History
 *          2022-12-28, longmingxing, 增加设备实例初始化以及命令字段表 
 *     version: author, date, descn
 */

#include "dev_bmu.h"
#include "protocol_layer.h"
#include "device_num.h"
#include <string.h>
#include "realdata_id_in.h"
#include "para_id_in.h"
#include "alarm_id_in.h"
#include "sps.h"
#include "cmd.h"
#include "utils_rtthread_security_func.h"

/* 设备缓冲区长度 */
#define R_BUFF_LEN                    128     ///<  接收缓冲区长度
#define S_BUFF_LEN                    512     ///<  发送缓冲区长度






/* 缺省值 */
#define BMU_SELF_CHECK_DEF_BYTE_NUM 1          ///< 缺省BM自检状态量字节长度
#define BUM_SELF_CHECK_DEF_NUM 1               ///< 缺省BMU自检状态量数量
#define PARA_BYTE_LEN                 0x04
#define PARA_NUM                      0x02
#define PRE_ASSIGN_BYTE_LEN           0x01
#define ASSIGN_PARA_NUM               0x01
#define ADDR_BYTE_LEN                 0x01
#define ADDR_NUM                      0x01
#define CMD_SET_CTRL_LEN              0x04
#define CMD_SET_CTRL_NUM              0x01

/* 命令数据存储变量 */
static BMU_real_data_ana_t bmu_ana_data[BATT_MOD_NUM];
static BMU_real_data_dig_t bmu_dig_data[BATT_MOD_NUM];
static BMU_real_data_alm_t bmu_alm_data[BATT_MOD_NUM];
static BMU_data_fac_t      bmu_fac_data[BATT_MOD_NUM];
Static BMU_para_data_t     bmu_para_data[BATT_MOD_NUM];

/* 命令请求 */
static bottom_comm_cmd_head_t cmd_req[] = {
    /* 通信协议命令请求 */
    {BOTTOM_PROTO_TYPE_COMM, CMD_NONE, CMD_APPEND_NONE, BOTTOM_RTN_APP_NOACK},    //0

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA, CMD_APPEND_GET_ANA, BOTTOM_RTN_APP_ACK},  //1

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA, CMD_APPEND_GET_SWITCH, BOTTOM_RTN_APP_ACK},  //2

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA, CMD_APPEND_GET_ALARM, BOTTOM_RTN_APP_ACK}, //3
    
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_ADDR, CMD_APPEND_NONE, BOTTOM_RTN_APP_ACK}, //4

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL, CMD_APPEND_BATT_BALANCE, BOTTOM_RTN_APP_ACK},  //5 设置电芯均衡

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_MANUFACTOR_DATA, CMD_APPEND_NONE, BOTTOM_RTN_APP_ACK},  //6

    {BOTTOM_PROTO_TYPE_COMM, CMD_ADDR_ASSIGN, CMD_APPEND_ADDR_ASSIGN_PRE, BOTTOM_RTN_APP_ACK},  //7

    {BOTTOM_PROTO_TYPE_COMM, CMD_ADDR_ASSIGN, CMD_APPEND_ADDR_BROAD_MOD, BOTTOM_RTN_APP_ACK},  //8

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA, CMD_APPEND_SELFCHECK_STATE, BOTTOM_RTN_APP_ACK},  //9

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL, CMD_APPEND_NONE, BOTTOM_RTN_APP_NOACK},  //10
	
	{BOTTOM_PROTO_TYPE_COMM, CMD_GET_BATCH_PARA, CMD_APPEND_NONE, BOTTOM_RTN_APP_ACK},  //11
	
	{BOTTOM_PROTO_TYPE_COMM, CMD_SET_BATCH_PARA, CMD_APPEND_NONE, BOTTOM_RTN_APP_NOACK},  //12

    {BOTTOM_PROTO_TYPE_DOWN, CMD_DATA_TRIG, CMD_APPEND_UPDATE_REQ, BOTTOM_RTN_APP_ACK}, //31
    
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_DATA, CMD_APPEND_NONE, BOTTOM_RTN_APP_ACK}, //14
    
    //{BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_DATA_INTERRUPT, CMD_APPEND_UPDATE_REQ, BOTTOM_RTN_APP_ACK}, //15
    
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_CONFIRM, CMD_APPEND_UPDATE_REQ, BOTTOM_RTN_APP_ACK}, //16

};

/* 命令应答   */
static bottom_comm_cmd_head_t cmd_ack[] = {
    /* 通信协议命令应答   */
    {BOTTOM_PROTO_TYPE_COMM, CMD_NONE, CMD_APPEND_NONE, BOTTOM_RTN_APP_CORRECT},    //0

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA, CMD_APPEND_GET_ANA, BOTTOM_RTN_APP_CORRECT},  //1

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA, CMD_APPEND_GET_SWITCH, BOTTOM_RTN_APP_CORRECT},  //2

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA, CMD_APPEND_GET_ALARM, BOTTOM_RTN_APP_CORRECT}, //3
    
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_ADDR, CMD_APPEND_NONE, BOTTOM_RTN_APP_CORRECT}, //4

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL, CMD_APPEND_BATT_BALANCE, BOTTOM_RTN_APP_CORRECT},  //5 

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_MANUFACTOR_DATA, CMD_APPEND_NONE, BOTTOM_RTN_APP_CORRECT},  //6
    
    {BOTTOM_PROTO_TYPE_COMM, CMD_FIRE_ALARM_PROTECT, CMD_APPEND_NONE, BOTTOM_RTN_APP_NOACK},  // 7

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA, CMD_APPEND_SELFCHECK_STATE, BOTTOM_RTN_APP_ACK},  //8
	
	{BOTTOM_PROTO_TYPE_COMM, CMD_GET_BATCH_PARA, CMD_APPEND_NONE, BOTTOM_RTN_APP_CORRECT},  //9
    
    {BOTTOM_PROTO_TYPE_DOWN, CMD_DATA_TRIG, CMD_APPEND_UPDATE_ACK, BOTTOM_RTN_APP_CORRECT}, //10
    
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_DATA, CMD_APPEND_NONE, BOTTOM_RTN_APP_CORRECT}, //11
    
    //{BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_DATA_INTERRUPT, CMD_APPEND_UPDATE_ACK, BOTTOM_RTN_APP_CORRECT}, //12
    
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_CONFIRM, CMD_APPEND_UPDATE_ACK, BOTTOM_RTN_APP_CORRECT}, //13

};


/* 命令字段信息   */
static data_info_id_verison_t cmd_ana_data_info[] = {
    {SEG_LEN,  type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},  /// 模拟量字节长度
    {SEG_NUM,  type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},  /// 模拟量数量
    {SEG_DATA, type_unsigned_char,   ARRAY_SIZE_1, DOP_0, DATA_KEEP, BATT_MOD_CELL_NUM, NOT_NEED_INTERACT},  /// 电芯数量
    {SEG_DATA, type_short,           BATT_MOD_CELL_NUM, DOP_1, DATA_USE,  NO_DEF_VALUE, NEED_INTERACT, BCMU_DATA_ID_CELL_TEMP},   /// 电芯温度
    {SEG_DATA, type_short,           BATT_MOD_CELL_NUM, DOP_2, DATA_USE,  NO_DEF_VALUE, NEED_INTERACT, BCMU_DATA_ID_CELL_VOL},   /// 电芯电压
    {SEG_DATA, type_short,           ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_DATA_ID_CELL_TEMP_MAX},  /// 电芯温度最高
    {SEG_DATA, type_unsigned_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_DATA_ID_CELL_TEMP_MAX_CHANNEL},  /// 电芯温度最高通道
    {SEG_DATA, type_short,           ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_DATA_ID_CELL_TEMP_MIN},  /// 电芯温度最低
    {SEG_DATA, type_unsigned_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_DATA_ID_CELL_TEMP_MIN_CHANNEL},  /// 电芯温度最低通道
    {SEG_DATA, type_short,           ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_DATA_ID_CELL_AVERAGE_TEMP},  /// 电芯平均温度
    {SEG_DATA, type_short,           ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_DATA_ID_CELL_VOL_MAX},  /// 电芯电压最高
    {SEG_DATA, type_unsigned_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_DATA_ID_CELL_VOL_MAX_CHANNEL},  /// 电芯电压最高通道
    {SEG_DATA, type_short,           ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_DATA_ID_CELL_VOL_MIN},  /// 电芯电压最低
    {SEG_DATA, type_unsigned_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_DATA_ID_CELL_VOL_MIN_CHANNEL},  /// 电芯电压最低通道
    {SEG_DATA, type_short,           ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_DATA_ID_CELL_AVERAGE_VOL},  /// 电芯平均电压
    {SEG_DATA, type_short,           ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_DATA_ID_MOD_VOL_TOTAL},  /// 模块总电压
};

/* 开关量命令字段信息 */
static data_info_id_verison_t cmd_dig_data_info[] = {
    {SEG_LEN,  type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},  /// 开关量字节长度
    {SEG_NUM,  type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},  /// 开关量数量
    {SEG_DATA, type_unsigned_char,   ARRAY_SIZE_1, DOP_0, DATA_KEEP, BATT_MOD_CELL_NUM, NOT_NEED_INTERACT},  /// 电芯数量
    {SEG_DATA, type_bit,             BATT_MOD_CELL_NUM, DOP_0, DATA_USE,  NO_DEF_VALUE, NEED_INTERACT, BCMU_DATA_ID_CELL_BALANCED_STATUS},   /// 电芯均衡状态
};                                                        

/* 告警量命令字段信息 */
static data_info_id_verison_t cmd_alm_data_info[] = {
    {0},
    /* TODO: 重新开发
    {SEG_LEN,  type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},  /// 告警量字节长度
    {SEG_NUM,  type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},  /// 告警量数量
    {SEG_DATA, type_unsigned_char,   ARRAY_SIZE_1, DOP_0, DATA_KEEP, BATT_MOD_CELL_NUM, NOT_NEED_INTERACT},  /// 电芯数量
    {SEG_DATA, type_bit,             BATT_MOD_CELL_NUM, DOP_0, DATA_USE,  NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_OVER_VOL_ALM},   /// 电芯过压告警
    {SEG_DATA, type_bit,             BATT_MOD_CELL_NUM, DOP_0, DATA_USE,  NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_UNDER_VOL_ALM},   /// 电芯欠压告警
    {SEG_DATA, type_bit,             BATT_MOD_CELL_NUM, DOP_0, DATA_USE,  NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_HIGH_TEMP_ALM},   /// 电芯高温告警
    {SEG_DATA, type_bit,             BATT_MOD_CELL_NUM, DOP_0, DATA_USE,  NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_LOW_TEMP_ALM},   /// 电芯低温告警
    {SEG_DATA, type_bit,             ARRAY_SIZE_1, DOP_0, DATA_DROP,  NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_FIRE_MOD_ALARM},   /// 消防模块告警
    {SEG_DATA, type_bit,             ARRAY_SIZE_1, DOP_0, DATA_DROP,  NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_ABNORMAL_TEMP_ALM},   /// 温度检测异常告警
    {SEG_DATA, type_bit,             ARRAY_SIZE_1, DOP_0, DATA_DROP,  NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_ABNORMAL_VOL_ALM},   /// 电压检测异常告警
    */
};

/* 厂家字段信息 */
static data_info_id_verison_t cmd_fac_data_info[] = {
    {SEG_LEN,  type_unsigned_char, ARRAY_SIZE_1,      DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},  /// 厂家信息字节长度
    {SEG_NUM,  type_unsigned_char, ARRAY_SIZE_1,      DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},  /// 厂家信息字段数量
    {SEG_DATA, type_unsigned_char, BMU_CELL_FAC_NAME, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_DATA_ID_BMU_CELL_FAC_NAME},           /// 电芯厂家名称 
    {SEG_DATA, type_unsigned_char, BMU_CELL_VER,      DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_DATA_ID_CELL_VER},         /// 电芯版本 
    {SEG_DATA, type_date,          ARRAY_SIZE_1,      DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_DATA_ID_CELL_FACT_DATE},           /// 电芯出厂日期 
    {SEG_DATA, type_unsigned_char, BMU_CELL_SERIES_NUM_LEN,  DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_DATA_ID_CELL_SN},   /// 电芯序列号 
    {SEG_DATA, type_unsigned_char, BMU_SYS_NAME_LEN,  DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_DATA_ID_BMU_SYS_NAME},           /// BMU系统名称
    {SEG_DATA, type_unsigned_char, BMU_SOFT_VER,      DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_FAC_ID_BMU_SOFTWARE_VER},           /// BMU软件版本
    {SEG_DATA, type_date,          ARRAY_SIZE_1,      DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_DATA_ID_BMU_SOFTWARE_PUB_DATA},           /// BMU软件发布日期
};


/* 广播站址分配 */
static data_info_id_verison_t cmd_broad_distr_data_info[] = {
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, PRE_ASSIGN_BYTE_LEN, NOT_NEED_INTERACT},  /// 参数字节数
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ASSIGN_PARA_NUM, NOT_NEED_INTERACT},  /// 参数数量
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},  /// 广播站址分配
};

/* 广播地址占用 */
static data_info_id_verison_t cmd_broad_occupy_data_info[] = {
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, ADDR_BYTE_LEN, NOT_NEED_INTERACT},  /// 参数字节数
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ADDR_NUM, NOT_NEED_INTERACT},  /// 参数数量
    {SEG_DATA,  type_unsigned_char, ARRAY_SIZE_1,  DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},  /// 广播当前本机地址
};

/* 获取自检状态 */
static data_info_id_verison_t cmd_self_check_data_info[] = {
    {SEG_LEN,   type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, BMU_SELF_CHECK_DEF_BYTE_NUM, NOT_NEED_INTERACT},  /// 参数字节数
    {SEG_NUM,   type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, BUM_SELF_CHECK_DEF_NUM, NOT_NEED_INTERACT},  /// 参数数量
    {SEG_DATA,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,  NEED_INTERACT, BCMU_DATA_ID_BMU_SELF_CHECK_STATE},  /// BMU自检状态
};

/* 获取SOC/SOH字段信息 */
static data_info_id_verison_t cmd_soc_soh_info[] = {
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_BYTE_LEN, NOT_NEED_INTERACT},  ///< 参数字节数
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_NUM, NOT_NEED_INTERACT},  ///< 参数数量
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_DATA_ID_BATT_CLUSTER_SOC},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_DATA_ID_BATT_CLUSTER_SOH},
};

/* 设置BMU电芯均衡字段信息 */
static data_info_id_verison_t cmd_cell_balance_control_info[] = {
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, CMD_SET_CTRL_LEN, NOT_NEED_INTERACT},  ///< 参数字节数
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, CMD_SET_CTRL_NUM, NOT_NEED_INTERACT},  ///< 参数数量
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_CTRL_ID_CELL_BALANCE},
};

static cmd_parse_info_id_verison_t cmd_parse_info[] = {
    {&cmd_ana_data_info[0], sizeof(cmd_ana_data_info)/sizeof(data_info_id_verison_t)},  //0
    {&cmd_dig_data_info[0], sizeof(cmd_dig_data_info)/sizeof(data_info_id_verison_t)},  //1
    {&cmd_alm_data_info[0], sizeof(cmd_alm_data_info)/sizeof(data_info_id_verison_t)},  //2
    {&cmd_fac_data_info[0], sizeof(cmd_fac_data_info)/sizeof(data_info_id_verison_t)},  //6
    {&cmd_broad_distr_data_info[0], sizeof(cmd_broad_distr_data_info)/sizeof(data_info_id_verison_t)}, 
    {&cmd_broad_occupy_data_info[0], sizeof(cmd_broad_occupy_data_info)/sizeof(data_info_id_verison_t)}, 
    {&cmd_self_check_data_info[0], sizeof(cmd_self_check_data_info)/sizeof(data_info_id_verison_t)}, 
    {&cmd_soc_soh_info[0], sizeof(cmd_soc_soh_info)/sizeof(data_info_id_verison_t)}, 
};

static cmd_parse_info_id_verison_t cmd_pack_info[] = {
    {&cmd_soc_soh_info[0], sizeof(cmd_soc_soh_info)/sizeof(data_info_id_verison_t)}, 
    {&cmd_cell_balance_control_info[0], sizeof(cmd_cell_balance_control_info)/sizeof(data_info_id_verison_t)},
};

/* 通信协议命令表 */
static cmd_t poll_cmd_tab[] = {
    {BMU_GET_ANA_DATA,        CMD_POSITIVE,  &cmd_req[1], &cmd_ack[1], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[0]},
    {BMU_GET_SWITCH_DATA,     CMD_POSITIVE,  &cmd_req[2], &cmd_ack[2], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[1]},
    // {BMU_GET_ALARM_DATA,      CMD_POSITIVE,  &cmd_req[3], &cmd_ack[3], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[2]},
    // {BMU_GET_MANUFACTOR_DATA, CMD_POSITIVE,  &cmd_req[6], &cmd_ack[6], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[3]},
    {0},
};

static cmd_t no_poll_tab[] = {
    {BMU_CTRL_SET_BALANCE,    CMD_BROADCAST, &cmd_req[5], &cmd_req[5], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[1], NULL},
    {BMU_ADDR_ASSIGN,         CMD_BROADCAST, &cmd_req[7], NULL,        sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[4]},
    {BMU_ADDR_OCCUPY,         CMD_BROADCAST, &cmd_req[8], NULL,        sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[5]},
    //{BMU_GET_SAMPLE_VOL,      CMD_POSITIVE,  &cmd_req[1], &cmd_ack[1], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[0]},
    {BMU_FIRE_ALARM_PROTECT,  CMD_BROADCAST|CMD_PASSIVE, NULL, &cmd_ack[7], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, SELF_PARSE},
    {BMU_GET_SELF_CHECK_STATE,  CMD_POSITIVE,&cmd_req[9], &cmd_ack[8], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[6]},
    {BMU_HIS_RECORD_SAVE,     CMD_POSITIVE,&cmd_req[10], NULL, sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, NULL},
    {BMU_GET_SOC_SOH_DATA,  CMD_POSITIVE,&cmd_req[11], &cmd_ack[9], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[7]},
    {BMU_SET_SOC_SOH_DATA,  CMD_POSITIVE,&cmd_req[12], NULL, sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[0], NULL},
    {BMU_UPDATE_DATA_TRIG,       CMD_POSITIVE,  &cmd_req[13], &cmd_ack[10], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, NULL},
    {BMU_UPDATE_DATA,            CMD_BROADCAST|CMD_POSITIVE,  &cmd_req[14], &cmd_ack[11], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, NULL},
    {BMU_UPDATE_DATA_INTERRUPT,  CMD_POSITIVE,  &cmd_req[15], &cmd_ack[12], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, NULL},
    {BMU_UPDATE_TRIG,            CMD_POSITIVE,  &cmd_req[16], &cmd_ack[13], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, NULL},
    {0},
};

static dev_type_t dev_bmu = {
    DEV_BMU, BATT_MOD_NUM, PROTOCOL_BOTTOM_COMM, LINK_BMU, R_BUFF_LEN, S_BUFF_LEN, BOTTOM_BMU_TYPE, &no_poll_tab[0], &poll_cmd_tab[0]
};

dev_type_t* init_dev_bmu(void) {
    return &dev_bmu;
}

void get_bmu_data(dev_sample_data_t* dev_data, unsigned char dev_index) {
    dev_data->ana_data = &bmu_ana_data[dev_index];
    dev_data->dig_data = &bmu_dig_data[dev_index];
    dev_data->alm_data = &bmu_alm_data[dev_index];
    dev_data->fac_data = &bmu_fac_data[dev_index];
    dev_data->para_data = &bmu_para_data[dev_index];
}

void set_bmu_soc_data(BMU_para_data_t *bmu_soc_tmp) {
    rt_memcpy_s(&bmu_para_data[0], sizeof(BMU_para_data_t)*BATT_MOD_NUM, bmu_soc_tmp, sizeof(BMU_para_data_t)*BATT_MOD_NUM);
}

void set_bmu_cell_balance_status(char bmu_no, int cell_balance_control) {
    bmu_para_data[bmu_no - 1].cell_balance_control = cell_balance_control;
}