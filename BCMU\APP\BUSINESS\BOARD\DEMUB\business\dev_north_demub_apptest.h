#ifndef _DEV_NORTH_DEMUB_APPTEST_H
#define _DEV_NORTH_DEMUB_APPTEST_H

#ifdef __cplusplus
extern "C"
{
#endif /*__cplusplus */

#include "device_type.h"
#include <board.h>

#define DEMUB_SOFTWARE_NAME        "DEMUB"

#define SAMPLE_UART_NAME       "usart1"

// CID2
#define CID2_TRIG_APPTEST           0x00   // 触发apptest命令
#define CID2_EXIT_APPTEST           0x01   // 触发apptest命令
#define CID2_GET_PROTO_VER          0x4F   // 获取通讯协议版本号
#define CID2_GET_FACT_INFO          0x13   // 获取厂家信息
#define CID2_CHIP_TEST_INFO         0x14   // 获取芯片测试信息
#define CID2_SET_HARDWARE_VER       0x15   // 设置硬件版本号
#define CID2_GET_HARDWARE_VER       0x16   // 获取硬件版本号
#define CID2_SET_BOARD_MANU_DATE    0X17   // 设置单板生产日期
#define CID2_GET_BOARD_MANU_DATE    0x18   // 获取单板生产日期
#define CID2_SET_OUTPUT_RLY_STA     0x19   // 设置输出干接点状态
#define CID2_GET_OUTPUT_RLY_STA     0x1A   // 读取输出干接点状态
#define CID2_GET_AI_SIGNAL          0x1B   // 读取AI信号
#define CID2_TEST_UART_STA          0x1C   // 测试串口链路状态
#define CID2_GET_DEVICE_ADDR        0x50   // 读取通信地址
#define CID2_SET_SN                 0x1D   // 设置序列号
#define CID2_GET_SN                 0x1E   // 获取序列号

#define DEMUB_APPTEST_TRIG             1   // 触发apptest命令
#define DEMUB_APPTEST_EXIT             2   // 退出apptest命令
#define DEMUB_GET_PROTO_VER            3   // 获取通讯协议版本号
#define DEMUB_GET_FACT_INFO            4   // 获取厂家信息
#define DEMUB_CHIP_TEST_INFO           5   // 获取芯片测试信息
#define DEMUB_SET_HARDWARE_VER         6   // 设置硬件版本号
#define DEMUB_GET_HARDWARE_VER         7   // 获取硬件版本号
#define DEMUB_SET_BOARD_MANU_DATE      8   // 设置单板生产日期
#define DEMUB_GET_BOARD_MANU_DATE      9   // 获取单板生产日期
#define DEMUB_SET_OUTPUT_RLY_STA       10   // 设置输出干接点状态
#define DEMUB_GET_OUTPUT_RLY_STA       11  // 读取输出干接点状态
#define DEMUB_GET_AI_SIGNAL            12  // 读取AI信号
#define DEMUB_TEST_UART_STA            13  // 串口测试
#define DEMUB_GET_COMM_ADDR            14  // 读取通信地址
#define DEMUB_SET_SN                   15  // 设置序列号
#define DEMUB_GET_SN                   16  // 获取序列号

#define SOFTWARE_NAME_LEN              32   // 软件名称长度
#define SOFTWARE_VER_LEN               32   // 软件版本号长度
#define SOFTWARE_DATE_LEN              10    // 软件版本日期长度
#define SOFTWARE_INFO_LEN              (SOFTWARE_NAME_LEN + SOFTWARE_VER_LEN + SOFTWARE_DATE_LEN) // 软件信息长度


typedef enum
{
    RLY1_OUT = 0,
    RLY2_OUT,
    RLY_END,
} RLY_OUT_CHANNEL_INDEX;

#pragma pack(1)
typedef struct
{
    RLY_OUT_CHANNEL_INDEX       do_channel;
    int           do_pin;
} RLY_OUT_CHANNEL_INFO_STRUCT;
#pragma pack()


dev_type_t* init_demub_dev_apptest(void);
void apptest_register_cmd_table();
#ifdef __cplusplus
}
#endif //  __cplusplus

#endif //
