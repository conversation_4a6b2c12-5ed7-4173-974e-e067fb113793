#include <stdbool.h>
#include <stdint.h>
#include <string.h>
#include <math.h>
#include "sample_new.h"
#include <board.h>
#include <rtthread.h>
#include <rtdevice.h>
#include "realdata_save.h"
#include "realdata_id_in.h"
#include "para_id_in.h"
#include "para_manage.h"
#include "pin.h"
#include "data_type.h"
#include "utils_server.h"
#include <drv_mt9805.h>
#include <bspconfig.h>
#include "apptest.h"


static msg_map sample_msg_map[] =
{
     {0,NULL},
};

rt_device_t dev;
static rt_adc_device_t s_adc;
static VoltageSampleData s_sample_voltage_data;
static float s_bmu_voltage = 0;     //BMU总电压
static float s_bmu_mod_voltage[SAMPLE_CHIP_NUM] = {0,};     //BMU模块电压
static TemperatureSampleBuffer s_sample_temp_buffer;
Static TemperatureSampleData s_sample_temperature_data;
Static struct mt9805_data s_chipData[SAMPLE_CHIP_NUM]; // 驱动层获取的数据


// NTC电阻与温度对应表
const int NTC_Table2[210] = {
    459591, 432381, 406957, 383189, 360961, 340163, 320695, 302464, 285383, 269374,     //-55~-46
    254362, 240281, 227066, 214659, 203007, 192059, 181768, 172091, 162988, 154422,     //-45~-36
    146358, 138763, 131608, 124865, 118507, 112511, 106854, 101515, 96474 , 91713 ,     //-35~-26
    87215 , 82963 , 78944 , 75143 , 71546 , 68143 , 64921 , 61870 , 58980 , 56241 ,     //-25~-16
    53645 , 51184 , 48849 , 46634 , 44532 , 42537 , 40642 , 38842 , 37132 , 35506 ,     //-15~-06
    33961 , 32491 , 31093 , 29763 , 28498 , 27293 , 26145 , 25052 , 24010 , 23018 ,     //-05~4
    22072 , 21169 , 20309 , 19488 , 18705 , 17957 , 17243 , 16561 , 15910 , 15288 ,     //5~14
    14693 , 14125 , 13582 , 13062 , 12565 , 12089 , 11634 , 11199 , 10781 , 10382 ,     //15~24
    10000 , 9633  , 9282  , 8945  , 8622  , 8313  , 8016  , 7731  , 7458  , 7196  ,     //25~34
    6944  , 6703  , 6471  , 6248  , 6034  , 5828  , 5631  , 5441  , 5258  , 5083  ,     //35~44
    4914  , 4751  , 4595  , 4445  , 4300  , 4160  , 4026  , 3897  , 3773  , 3653  ,     //45~54
    3538  , 3426  , 3319  , 3216  , 3116  , 3020  , 2927  , 2838  , 2751  , 2668  ,     //55~64
    2588  , 2510  , 2435  , 2363  , 2293  , 2226  , 2161  , 2098  , 2037  , 1978  ,     //65~74
    1921  , 1866  , 1813  , 1762  , 1712  , 1664  , 1618  , 1573  , 1529  , 1487  ,     //75~84
    1446  , 1407  , 1369  , 1332  , 1296  , 1261  , 1227  , 1195  , 1163  , 1133  ,     //85~94
    1103  , 1074  , 1046  , 1019  , 993   , 968   , 943   , 919   , 896   , 873   ,     //95~104
    851   , 830   , 809   , 789   , 770   , 751   , 733   , 715   , 697   , 681   ,     //105~114
    664   , 648   , 633   , 618   , 603   , 589   , 575   , 562   , 549   , 536   ,     //115~124
    524   , 512   , 500   , 489   , 478   , 467   , 456   , 446   , 436   , 427   ,     //125~134
    417   , 408   , 399   , 390   , 382   , 374   , 366   , 358   , 350   , 343   ,     //135~144
    335   , 328   , 321   , 315   , 308   , 302   , 295   , 289   , 283   , 278   ,     //145~154
};

Static void adc_init(void);
Static rt_uint32_t tpw4051_adc_sample(rt_uint32_t channel);
Static int check_sample_chip_fault_status(VoltageSampleData* outVoltageData);
Static int handle_sample_fault(VoltageSampleData* inVoltageData);

// 初始化缓冲区
int initialize_temperature_buffer(TemperatureSampleBuffer* buffer) {
    if(buffer == NULL)
    {
        return FAILURE;
    }
    for (int i = 0; i < SAMPLE_TEMP_TATAL_NUM; ++i) {
        for (int j = 0; j < 10; ++j) {
            buffer->buffer[i][j] = 0.0f;
        }
    }
    buffer->index = 0;
    return SUCCESSFUL;
}

// 平均值滤波
void average_filter_temperature(TemperatureSampleBuffer* buffer, TemperatureSampleData* TemperatureData) {
    //  电芯温度35个，单板温度，端子温度两个，均衡电阻温度5个
    for (int i = 0; i < SAMPLE_TEMP_TATAL_NUM; ++i) {
        // 将新数据添加到缓冲区
        buffer->buffer[i][buffer->index] = TemperatureData->temperature[i];

        // 计算平均值
        float sum = 0.0f;
        for (int j = 0; j < 10; ++j) {
            sum += buffer->buffer[i][j];
        }
        TemperatureData->temperature[i] = sum / 10.0f;
    }

    // 更新缓冲区索引
    buffer->index = (buffer->index + 1) % 10;
}

// 使用双线性插值获取实际温度值
float interpolate_temperature(int resistance) {
    //NTC温度采样范围为-40-150
    if(resistance >= NTC_Table2[15])
    {
        return INVALID_SAMPLE_TEMPERATURE_MIN;
    }else if(resistance <= NTC_Table2[205])
    {
        return INVALID_SAMPLE_TEMPERATURE_MAX;
    }

    int low = 15;
    int high = 205;
    while (low < high) {
        int mid = low + (high - low) / 2;
        if (NTC_Table2[mid] > resistance) {
            low = mid + 1;
        } else {
            high = mid;
        }
    }

    float t1 = (float)(low - 56);
    float t2 = (float)(low - 55);
    float r1 = (float)NTC_Table2[low - 1];
    float r2 = (float)NTC_Table2[low];

    return t1 + (t2 - t1) * (resistance - r1) / (r2 - r1);
}

// 芯片初始化
int initialize_chip() {
    for (size_t i = 0; i < ARRAY_SIZE(s_chipData); i++)
    {
        s_chipData[i].chip_num = i;
    }
    rt_thread_delay(2000);

    dev = rt_device_find("dev_mt9805");
    if(dev == RT_NULL)
    {
        return SAMPLE_INVALID;
    }
    rt_device_open(dev, RT_NULL);
    return SAMPLE_VALID;
}

// 读取芯片采样数据
int read_chip_sample_data(VoltageSampleData* outVoltageData) {
    if(outVoltageData == RT_NULL)
    {
        return FAILURE;
    }
    float temp_voltage = 0.0f;
    float temp_temperature = 0.0f;
    float balance_sample_voltage = 0.0f;
    float sum_mod_voltage = 0;
    float sum_bmu_voltage = 0;
    // 填充chipData...
    rt_device_control(dev, GET_CV_REG, &s_chipData);
    rt_device_control(dev, GET_AUX_REG, &s_chipData);
    rt_device_control(dev, GET_CHIP_STATUS, &s_chipData);

    for (int chip = 0; chip < SAMPLE_CHIP_NUM; ++chip) {
        sum_mod_voltage = 0.0f;
        for (int cell = 0; cell < CHIP_CELL_NUM; ++cell) {
            temp_voltage = (float)s_chipData[chip].vol_data[cell] / 10000.0f;
            if(temp_voltage > 5.0f || temp_voltage < 0.0f)
            {
                temp_voltage = INVALID_VOLTAGE;
            }
            outVoltageData->voltage[chip * 13 + cell] = temp_voltage; // 将ADC值转换为电压
            sum_mod_voltage += temp_voltage;    //计算电池模组电压，为一串内13个电芯电压之和
        }
        s_bmu_mod_voltage[chip] = sum_mod_voltage;
        sum_bmu_voltage += s_bmu_mod_voltage[chip]; //计算BMU总电压，为各模块电压之和
    }
    s_bmu_voltage = sum_bmu_voltage;

    //检查芯片异常状态
    check_sample_chip_fault_status(outVoltageData);
    handle_sample_fault(outVoltageData);

    return SUCCESSFUL;
}

Static int check_sample_chip_fault_status(VoltageSampleData* outVoltageData)
{
    bool all_invalid_flag[SAMPLE_CHIP_NUM] = {0,};
    for (int chip = 0; chip < SAMPLE_CHIP_NUM; ++chip) {
        if (s_chipData[chip].chip_status != 0) {  //  芯片初始化状态为无效,无需其他判断
            outVoltageData->status[chip] = SAMPLE_INVALID;
            continue;
        }

        all_invalid_flag[chip] = true;

        for (int cell = 0; cell < CHIP_CELL_NUM; ++cell) {
            if(s_chipData[chip].vol_data[cell] != INVALID_SAMPLE_DATA)
            {
                all_invalid_flag[chip] = false;
                break;
            }
        }

        outVoltageData->status[chip] = (all_invalid_flag[chip])? SAMPLE_INVALID : SAMPLE_VALID;
    }
    return SUCCESSFUL;
}

Static int handle_sample_fault(VoltageSampleData* inVoltageData)
{
    static unsigned int time_count[SAMPLE_CHIP_NUM] = {0,};
    bool all_normal = true;
    for(int chip = 0; chip < SAMPLE_CHIP_NUM; ++chip)
    {
        if(inVoltageData->status[chip] == SAMPLE_INVALID)
        {
            time_count[chip] ++;
        }
        else
        {
            time_count[chip] = 0;
        }

        if(time_count[chip] >= TEN_SECOND_COUNT)
        {
            s_chipData[chip].need_init = 1;
            rt_device_control(dev, SINGLE_CHIP_INIT, &s_chipData);
            time_count[chip] = 0;
        }
    }

    return SUCCESSFUL;
}


// 设置电压和温度采样数据到内存
void set_sample_data(TemperatureSampleData* temperature_data, VoltageSampleData* voltage_data) {
    unsigned char status_set = 0;
    if (!temperature_data || !voltage_data) return;

    for (int i = 0; i < CELL_TEMP_SAMPLE_NUM; ++i) {
        set_one_data(BMU_DATA_ID_CELL_TEMP + i, &temperature_data->temperature[i]);
        status_set = temperature_data->status[i];
        set_one_data(BMU_DATA_ID_TEMP_DET_FAULT_STA + i, &status_set);
    }
    set_one_data(BMU_DATA_ID_BATT_POS_TEMP, &temperature_data->temperature[BATT_POS_TEMP_INDEX]);
    set_one_data(BMU_DATA_ID_BATT_NEG_TEMP, &temperature_data->temperature[BATT_NEG_TEMP_INDEX]);
    set_one_data(BMU_DATA_ID_BATT_TEMP, &temperature_data->temperature[BATT_BORAD_TEMP_INDEX]);
    set_one_data(BMU_DATA_ID_BATT_MOD_VOLT, &s_bmu_voltage);
    for(int i = 0;i < SAMPLE_CHIP_NUM; i ++)
    {
        set_one_data(BMU_DATA_ID_CELL_MOD_VOL1 + i, &s_bmu_mod_voltage[i]);
        set_one_data(BMU_DATA_ID_EQUA_RESISTOR_TEMP + i, &temperature_data->temperature[BALANCE_RESISTANCE_TEMP_INDEX + i]);
    }

    for (int i = 0; i < 65; ++i) {
        set_one_data(BMU_DATA_ID_CELL_VOLT + i, &voltage_data->voltage[i]);
    }

    for (int i = 0; i < 5; ++i) {
        status_set = voltage_data->status[i];
        set_one_data(BMU_DATA_ID_SAM_CHIP_FAULT_STA + i, &status_set);
    }
}


int set_fire_data(void)
{
    short ret = 0;
    unsigned char bmu_fire_status;
    bmu_fire_status = rt_pin_read(FIRE_BMU_PIN_1);
    bmu_fire_status &= rt_pin_read(FIRE_BMU_PIN_2);
    bmu_fire_status = (bmu_fire_status == PIN_HIGH) ? Normal : Fault;
    ret = set_one_data(BMU_DATA_ID_FIRE_TRIG_STA, &bmu_fire_status);
    return ret;
}

int set_msd_data(void)
{
    short ret = 0;
    unsigned char msd_status;
    msd_status = rt_pin_read(MSD_BMU_PIN);
    msd_status = (msd_status == PIN_LOW) ? Normal : Fault;
    ret = set_one_data(BMU_DATA_ID_MSD_CONNECTION, &msd_status);
    return ret;
}

// 采样通道初始化
Static void adc_init(void) {
    s_adc = (rt_adc_device_t)rt_device_find(ADC_DEV_NAME0);
    if(s_adc == RT_NULL)
    {
        return;
    }

    // 初始化片选引脚
    rt_pin_mode(DO_1_A0, PIN_MODE_OUTPUT);
    rt_pin_mode(DO_1_A1, PIN_MODE_OUTPUT);
    rt_pin_mode(DO_1_A2, PIN_MODE_OUTPUT);
    rt_pin_mode(FIRE_BMU_PIN_1, PIN_MODE_INPUT);
    rt_pin_mode(FIRE_BMU_PIN_2, PIN_MODE_INPUT);
    rt_pin_mode(MSD_BMU_PIN, PIN_MODE_INPUT);
}


// ADC通道采样
Static rt_uint32_t tpw4051_adc_sample(rt_uint32_t channel) {
    rt_uint32_t vol = 0;

    if(s_adc == RT_NULL)
    {
        return FAILURE;
    }

    rt_adc_enable(s_adc, channel);
    vol = rt_adc_read(s_adc, channel);
    rt_adc_disable(s_adc, channel);

    return vol;
}

// 获取温度采样值
int get_temperature_sample_values(void) {
    int index = 0;
    float adc_value = 0.0f;
    float balance_sample_voltage = 0.0f;
    float temp_temperature = 0.0f;
    float temp_data[CELL_TEMP_SAMPLE_NUM + 3] = {0};

    for (int i = 0; i < SAMPLE_CHIP_NUM; i++) {
        temp_data[index++] = s_chipData[i].vol_gpio[0]/10000.0f;
        temp_data[index++] = s_chipData[i].vol_gpio[1]/10000.0f;
        temp_data[index++] = s_chipData[i].vol_gpio[2]/10000.0f;
        temp_data[index++] = s_chipData[i].vol_gpio[3]/10000.0f;
        temp_data[index++] = s_chipData[i].vol_gpio[4]/10000.0f;
        temp_data[index++] = s_chipData[i].vol_gpio[6]/10000.0f;
        temp_data[index++] = s_chipData[i].vol_gpio[7]/10000.0f;

        balance_sample_voltage = ((float)s_chipData[i].vol_gpio[9]) / 10000.0f;
        temp_temperature = -1481.96+sqrt(2.1962*1000000+(1.8639-balance_sample_voltage)/(3.88*0.000001)); // 均衡电阻温度计算
        s_sample_temperature_data.temperature[BALANCE_RESISTANCE_TEMP_INDEX + i] = (temp_temperature < 130) ? temp_temperature : 130;
        s_sample_temperature_data.temperature[BALANCE_RESISTANCE_TEMP_INDEX + i] = (temp_temperature > -55) ? temp_temperature : -55;
    }

    for (int i = 0; i < CELL_TEMP_SAMPLE_NUM; i++) {
        if(temp_data[i] < 3)
        {
            temp_data[i] = (10000*(temp_data[i]))/(3-temp_data[i]);
            s_sample_temperature_data.temperature[i] =interpolate_temperature(temp_data[i]);
        }
    }

    temp_data[BATT_POS_TEMP_INDEX] = ((10000*(s_chipData[0].vol_gpio[8]/10000.0f))/(3.3-(s_chipData[0].vol_gpio[8]/10000.0f)));
    s_sample_temperature_data.temperature[BATT_POS_TEMP_INDEX] = interpolate_temperature(temp_data[BATT_POS_TEMP_INDEX]);   //负极端子温度的计算

    temp_data[BATT_NEG_TEMP_INDEX] = ((10000*(s_chipData[1].vol_gpio[8]/10000.0f))/(3.3-(s_chipData[1].vol_gpio[8]/10000.0f)));
    s_sample_temperature_data.temperature[BATT_NEG_TEMP_INDEX] = interpolate_temperature(temp_data[BATT_NEG_TEMP_INDEX]);   //正极端子温度的计算

    adc_value = ((float)(tpw4051_adc_sample(SAMPLE_CHANNEL_5)) * REFER_VOLTAGE / CONVERT_BITS);
    s_sample_temperature_data.temperature[BATT_BORAD_TEMP_INDEX] = -1481.96+sqrt(2.1962*1000000+(1.8639-adc_value)/(3.88*0.000001));   //单板温度的计算
    s_sample_temperature_data.temperature[BATT_BORAD_TEMP_INDEX] = (s_sample_temperature_data.temperature[BATT_BORAD_TEMP_INDEX] < 130) ? s_sample_temperature_data.temperature[BATT_BORAD_TEMP_INDEX] : 130;
    s_sample_temperature_data.temperature[BATT_BORAD_TEMP_INDEX] = (s_sample_temperature_data.temperature[BATT_BORAD_TEMP_INDEX] >-55) ? s_sample_temperature_data.temperature[BATT_BORAD_TEMP_INDEX] : -55;

    // 应用平均值滤波
    average_filter_temperature(&s_sample_temp_buffer, &s_sample_temperature_data);

    check_temperature_sample_status(&s_sample_temperature_data);

    return SUCCESSFUL;
}


// 检查温度采样状态
int check_temperature_sample_status(TemperatureSampleData* temperatureData) {
    for (int i = 0; i < CELL_TEMP_SAMPLE_NUM ; ++i) {
        if ((fabs(temperatureData->temperature[i] - INVALID_SAMPLE_TEMPERATURE_MIN) < EPSILON) || (fabs(temperatureData->temperature[i] - INVALID_SAMPLE_TEMPERATURE_MAX) < EPSILON)) { //判断浮点数相等
            temperatureData->status[i] = SAMPLE_INVALID;
        } else {
            temperatureData->status[i] = SAMPLE_VALID;
        }
    }
    return 0;
}


// 主函数或初始化函数中调用
void *init_sampling(void *param) {
    server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(sample_msg_map) / sizeof(msg_map);
    register_server_msg_map(sample_msg_map, server_info);
    adc_init();
    initialize_chip();
    initialize_temperature_buffer(&s_sample_temp_buffer);
}

//初始化版本号和发布日期
int init_bmu_soft_version(void)
{
    char bmu_version[12];
    char afe_chip_type[12];
    date_base_t bmu_soft_release_date;
    rt_strncpy_s(afe_chip_type, sizeof(AFE_CHIP_TYPE), AFE_CHIP_TYPE, sizeof(afe_chip_type)-1);
    afe_chip_type[sizeof(afe_chip_type)-1] = '\0';
    rt_strncpy_s(bmu_version,sizeof(BMU_VER), BMU_VER, sizeof(bmu_version)-1);
    bmu_version[sizeof(bmu_version)-1] = '\0';
    bmu_soft_release_date.year = SOFTWARE_RELEASE_YEAR;
    bmu_soft_release_date.month = SOFTWARE_RELEASE_MONTH;
    bmu_soft_release_date.day = SOFTWARE_RELEASE_DATE;
    set_one_data(BMU_DATA_ID_SOFT_VER, bmu_version);    /*写BMU软件版本号*/
    set_one_data(BMU_DATA_ID_AFE_CHIP_TYPE, afe_chip_type);    /*写BMU_AFE芯片型号*/
    set_one_data(BMU_DATA_ID_SOFT_REL_DATE, &bmu_soft_release_date);    /*写BMU软件发布日期*/
    return 0;
}

// 示例：在主函数中调用
void sample_main_new(void * param) {
#  ifdef UNITEST
    while (is_running())
#  else
    while (1)
#  endif
    {
        rt_thread_delay(100);
        read_chip_sample_data(&s_sample_voltage_data);
        get_temperature_sample_values();
        set_sample_data(&s_sample_temperature_data,&s_sample_voltage_data);
        set_fire_data();
        set_msd_data();
    }
}

