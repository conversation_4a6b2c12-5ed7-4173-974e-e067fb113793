#include "dev_north_seub_apptest.h"
#include "cmd.h"
#include "protocol_layer.h"
#include "protocol_1363_comm.h"
#include "sps.h"
#include "utils_rtthread_security_func.h"
#include "data_type.h"
#include "utils_time.h"
#include "utils_data_transmission.h"
#include "storage.h"
#include "parse_layer.h"
#include "realdata_id_in.h"
#include "sample.h"
#include "software_version.h"
#include "realdata_save.h"
#include "ee_public_info.h"
#include "north_main.h"

Static unsigned int s_apptest_trig_succ = FALSE;
Static char s_apptest_trig_count = 0;
Static char s_usart_test_result = TEST_FAULT; //串口回环测试结果
Static char s_usart_test_frame[] = {0x00, 0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99}; // 串口回环测试的收发帧
Static unsigned char s_factory_info[SEUB_FACTORY_INFO_EEPROM_LEN] = {0}; // apptest初始化时读取厂家信息
Static mac_addr_info_t s_mac_addr_info = {0}; // apptest初始化时读取MAC地址

Static int init_eeprom_info(void);
Static int usart_init(rt_device_t dev);
Static int usart_rev_handle(rt_device_t dev);
Static int apptest_comm_parse(void* dev_inst, void* cmd_buff);
Static int apptest_exit_parse(void* dev_inst, void* cmd_buff);
Static int apptest_trig_pack(void* dev_inst, void* cmd_buff);
Static int get_ana_input_data_pack(void* dev_inst, void* cmd_buff);
Static int get_dig_input_data_pack(void* dev_inst, void* cmd_buff);
Static int set_ana_output_data_parse(void* dev_inst, void* cmd_buff);
Static int set_dig_output_data_parse(void* dev_inst, void* cmd_buff);
Static int get_fact_info_pack(void* dev_inst, void* cmd_buff);
Static int set_fact_info_parse(void* dev_inst, void* cmd_buff);
Static int get_mac_addr_pack(void* dev_inst, void* cmd_buff);
Static int set_mac_addr_parse(void* dev_inst, void* cmd_buff);
Static int eeprom_self_test_pack(void* dev_inst, void* cmd_buff);
Static int usart_loopback_test_parse(void* dev_inst, void* cmd_buff);
Static int usart_loopback_test_pack(void* dev_inst, void* cmd_buff);


Static s1363_cmd_head_t cmd_req[] = {
    {VER_22, CMD_NONE, CMD_NONE},                        // 0
    {VER_22, CID1_COMMON, CID2_TRIG_APPTEST},            // 1 触发apptest命令
    {VER_22, CID1_COMMON, CID2_EXIT_APPTEST},            // 2 触发apptest命令
    {VER_22, CID1_COMMON, CID2_GET_ANA_INPUT_DATA},      // 3 获取模拟量AI数据
    {VER_22, CID1_COMMON, CID2_GET_DIG_INPUT_DATA},      // 4 获取数字量DI数据
    {VER_22, CID1_COMMON, CID2_SET_ANA_OUTPUT_DATA},     // 5 设置模拟量AO数据
    {VER_22, CID1_COMMON, CID2_SET_DIG_OUTPUT_DATA},     // 6 设置数字量DO数据
    {VER_22, CID1_COMMON, CID2_GET_FACTORY_INFO},        // 7 获取厂家信息
    {VER_22, CID1_COMMON, CID2_SET_FACTORY_INFO},        // 8 设置厂家信息
    {VER_22, CID1_COMMON, CID2_GET_MAC_ADDR},            // 9 获取MAC地址
    {VER_22, CID1_COMMON, CID2_SET_MAC_ADDR},            // 10 设置MAC地址
    {VER_22, CID1_COMMON, CID2_EEPROM_SELF_TEST},        // 11 EEPROM自测命令
    {VER_22, CID1_COMMON, CID2_USART_LOOPBACK_TEST},     // 12 串口自回环测试
};

/* 命令应答头 */
Static s1363_cmd_head_t cmd_ack[] = {
    {VER_22, CMD_NONE, CMD_NONE},                        // 0
    {VER_22, CID1_COMMON, CID2_TRIG_APPTEST},            // 1 触发apptest命令
    {VER_22, CID1_COMMON, CID2_EXIT_APPTEST},            // 2 触发apptest命令
    {VER_22, CID1_COMMON, CID2_GET_ANA_INPUT_DATA},      // 3 获取模拟量AI数据
    {VER_22, CID1_COMMON, CID2_GET_DIG_INPUT_DATA},      // 4 获取数字量DI数据
    {VER_22, CID1_COMMON, CID2_SET_ANA_OUTPUT_DATA},     // 5 设置模拟量AO数据
    {VER_22, CID1_COMMON, CID2_SET_DIG_OUTPUT_DATA},     // 6 设置数字量DO数据
    {VER_22, CID1_COMMON, CID2_GET_FACTORY_INFO},        // 7 获取厂家信息
    {VER_22, CID1_COMMON, CID2_SET_FACTORY_INFO},        // 8 设置厂家信息
    {VER_22, CID1_COMMON, CID2_GET_MAC_ADDR},            // 9 获取MAC地址
    {VER_22, CID1_COMMON, CID2_SET_MAC_ADDR},            // 10 设置MAC地址
    {VER_22, CID1_COMMON, CID2_EEPROM_SELF_TEST},        // 11 EEPROM自测命令
    {VER_22, CID1_COMMON, CID2_USART_LOOPBACK_TEST},     // 12 串口自回环测试
};


unsigned int get_seub_apptest_flag()
{
    return s_apptest_trig_succ;
}

Static int apptest_comm_parse(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(s_apptest_trig_succ == TRUE, NO_NEED_REPONSE);
    return SUCCESSFUL;
}

Static int apptest_exit_parse(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(s_apptest_trig_succ == TRUE, NO_NEED_REPONSE);
    cmd_buf_t send_cmd_buff;
    rt_memset_s(&send_cmd_buff, sizeof(cmd_buf_t), 0, sizeof(cmd_buf_t));
    send_cmd_buff.cmd = ((cmd_buf_t*)cmd_buff)->cmd;
    cmd_send(dev_inst, &send_cmd_buff);
    rt_thread_mdelay(1000);
    rt_hw_cpu_reset();
    return SUCCESSFUL;
}

Static int apptest_trig_pack(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    int offset = 0;
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;
    s_apptest_trig_count++;
    if (s_apptest_trig_count < 3) {
        data_buff[offset] = 0x00;
    } else {
        data_buff[offset] = 0x01;
        s_apptest_trig_succ = TRUE;
    }
    offset++;
    ((cmd_buf_t*)cmd_buff)->data_len = offset;
    return SUCCESSFUL;
}

Static int get_ana_input_data_pack(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    int offset = 0;
    int i = 0;
    unsigned short ana_input_data[ANALOG_COUNT];
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;

    rt_memset_s(data_buff, ANALOG_COUNT * 2, 0, ANALOG_COUNT * 2);
    rt_memset_s(ana_input_data, sizeof(ana_input_data), 0, sizeof(ana_input_data));

    get_analog_data(ana_input_data, sizeof(ana_input_data));

    for (i = 0; i < ANALOG_COUNT; i++)
    {
        put_uint16_to_buff(&data_buff[offset], ana_input_data[i]);
        offset += 2;
    }
    ((cmd_buf_t*)cmd_buff)->data_len = offset;

    return SUCCESSFUL;
}

Static int get_dig_input_data_pack(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    int offset = 0;
    int i = 0;
    int dig_input_data[DIGITAL_COUNT];
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;

    rt_memset_s(data_buff, DIGITAL_COUNT, 0, DIGITAL_COUNT);
    rt_memset_s(dig_input_data, sizeof(dig_input_data), 0, sizeof(dig_input_data));

    get_digital_data(dig_input_data, sizeof(dig_input_data));

    for (i = 0; i < DIGITAL_COUNT; i++)
    {
        data_buff[offset++] = (unsigned char)(dig_input_data[i] & 0xFF);
    }
    ((cmd_buf_t*)cmd_buff)->data_len = offset;

    return SUCCESSFUL;
}

Static int set_ana_output_data_parse(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(s_apptest_trig_succ == TRUE, NO_NEED_REPONSE);
    int i = 0;
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;
    unsigned int ao_value[DAC_COUNT] = {0};

    for (i = 0; i < DAC_COUNT; i++)
    {
        ao_value[i] = get_uint16_data(&data_buff[2 * i]);
        setDacValue(i, ao_value[i]);
    }

    return SUCCESSFUL;
}

Static int set_dig_output_data_parse(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(s_apptest_trig_succ == TRUE, NO_NEED_REPONSE);
    int i = 0;
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;
    unsigned int do_value[DO_COUNT] = {0};

    for (i = 0; i < DO_COUNT; i++)
    {
        do_value[i] = data_buff[i];
        setDoValue(i, do_value[i]);
    }

    return SUCCESSFUL;
}

Static int get_fact_info_pack(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    int offset = 0;
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;

    rt_memset_s(data_buff, SEUB_FACTORY_INFO_TOTEL_LEN, 0, SEUB_FACTORY_INFO_TOTEL_LEN);
    rt_memcpy_s(&data_buff[offset], SEUB_SOFTWARE_NAME_LEN, SEUB_SOFTWARE_NAME, sizeof(SEUB_SOFTWARE_NAME));
    offset += SEUB_SOFTWARE_NAME_LEN;
    rt_memcpy_s(&data_buff[offset], SEUB_SOFTWARE_VER_LEN, SEUB_SOFTWARE_VERSION, sizeof(SEUB_SOFTWARE_VERSION));
    offset += SEUB_SOFTWARE_VER_LEN;
    rt_memcpy_s(&data_buff[offset], SEUB_SOFTWARE_RELEASE_DATE_LEN, SEUB_SOFTWARE_RELEASE_DATE, sizeof(SEUB_SOFTWARE_RELEASE_DATE));
    offset += SEUB_SOFTWARE_RELEASE_DATE_LEN;

    rt_memcpy_s((char *)&data_buff[offset], SEUB_FACTORY_INFO_EEPROM_LEN, (const char*)s_factory_info, SEUB_FACTORY_INFO_EEPROM_LEN);
    offset += SEUB_FACTORY_INFO_EEPROM_LEN;

    ((cmd_buf_t*)cmd_buff)->data_len = offset;
    return SUCCESSFUL;
}


Static int set_fact_info_parse(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(s_apptest_trig_succ == TRUE, NO_NEED_REPONSE);

    int offset = 0;
    unsigned char factory_info[20] = {0};
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;
    unsigned char command_type = data_buff[0];

    offset++;

    switch (command_type)
    {
        case 0x80: // 设置序列号
            rt_memcpy_s((char *)factory_info, SEUB_SERIAL_NUMBER_LEN, (const char*)&data_buff[offset], SEUB_SERIAL_NUMBER_LEN - 1);  // 最后一位固定为0x00，作为字符串的结束符
            offset += SEUB_SERIAL_NUMBER_LEN;
            if(0 == rt_memcmp(&s_factory_info[0], factory_info, SEUB_SERIAL_NUMBER_LEN)) // 如果设置的序列号和现存的序列号地址一致，则直接返回，减少EEPROM的写次数
            {
                return SUCCESSFUL;
            }
            if(handle_storage(write_opr, EEP_DATA_PART, factory_info, SEUB_SERIAL_NUMBER_LEN, SEUB_FACTORY_INFO_OFFSET) != SUCCESSFUL)
            {
                ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA;
                return FAILURE;
            }
            rt_memcpy_s((char *)&s_factory_info[0], SEUB_SERIAL_NUMBER_LEN, (const char*)factory_info, SEUB_SERIAL_NUMBER_LEN); // 设置成功后更新全局变量s_factory_info
            break;

        case 0x81: // 设置系统名称
            rt_memcpy_s((char *)factory_info, SEUB_SYSTEM_NAME_LEN, (const char*)&data_buff[offset], SEUB_SYSTEM_NAME_LEN - 1);  // 最后一位固定为0x00，作为字符串的结束符
            offset += SEUB_SYSTEM_NAME_LEN;
            if(0 == rt_memcmp(&s_factory_info[20], factory_info, SEUB_SYSTEM_NAME_LEN)) // 如果设置的系统名称和现存的系统名称地址一致，则直接返回，减少EEPROM的写次数
            {
                return SUCCESSFUL;
            }
            if(handle_storage(write_opr, EEP_DATA_PART, factory_info, SEUB_SYSTEM_NAME_LEN, SEUB_FACTORY_INFO_OFFSET + SEUB_SYSTEM_NAME_LEN) != SUCCESSFUL)
            {
                ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA;
                return FAILURE;
            }
            rt_memcpy_s((char *)&s_factory_info[20], SEUB_SYSTEM_NAME_LEN, (const char*)factory_info, SEUB_SYSTEM_NAME_LEN); // 设置成功后更新全局变量s_factory_info
            break;

        case 0x82: // 设置硬件版本
            rt_memcpy_s((char *)factory_info, SEUB_HARDWARE_VERSION_LEN, (const char*)&data_buff[offset], SEUB_HARDWARE_VERSION_LEN - 1);  // 最后一位固定为0x00，作为字符串的结束符
            offset += SEUB_HARDWARE_VERSION_LEN;
            if(0 == rt_memcmp(&s_factory_info[40], factory_info, SEUB_HARDWARE_VERSION_LEN)) // 如果设置的硬件版本和现存的硬件版本地址一致，则直接返回，减少EEPROM的写次数
            {
                return SUCCESSFUL;
            }
            if(handle_storage(write_opr, EEP_DATA_PART, factory_info, SEUB_HARDWARE_VERSION_LEN, SEUB_FACTORY_INFO_OFFSET + SEUB_SERIAL_NUMBER_LEN + SEUB_SYSTEM_NAME_LEN) != SUCCESSFUL)
            {
                ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA;
                return FAILURE;
            }
            rt_memcpy_s((char *)&s_factory_info[40], SEUB_HARDWARE_VERSION_LEN, (const char*)factory_info, SEUB_HARDWARE_VERSION_LEN); // 设置成功后更新全局变量s_factory_info
            break;

        default:
            ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA;
            return FAILURE;
    }

    return SUCCESSFUL;
}

Static int get_mac_addr_pack(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    int offset = 0;
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;

    rt_memset_s(data_buff, SEUB_MAC_ADDR_LEN, 0, SEUB_MAC_ADDR_LEN);
    rt_memcpy_s((char *)&data_buff[offset], SEUB_MAC_ADDR_LEN, (const char*)s_mac_addr_info.mac_addr, SEUB_MAC_ADDR_LEN);
    offset += SEUB_MAC_ADDR_LEN;

    ((cmd_buf_t*)cmd_buff)->data_len = offset;

    return SUCCESSFUL;
}

Static int set_mac_addr_parse(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(s_apptest_trig_succ == TRUE, NO_NEED_REPONSE);

    int offset = 0;
    mac_addr_info_t mac_addr_info = {0};
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;

    rt_device_t ETH_dev = rt_device_find("e0");
    if (RT_NULL == ETH_dev)
    {
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_CTRL_FAIL;
        return FAILURE;
    }

    rt_memcpy_s((char *)mac_addr_info.mac_addr, SEUB_MAC_ADDR_LEN, (const char*)&data_buff[offset], SEUB_MAC_ADDR_LEN);
    offset += SEUB_MAC_ADDR_LEN;

    mac_addr_info.crc = crc_cal((unsigned char *)&mac_addr_info, offsetof(mac_addr_info_t, crc)); // 计算MAC地址的CRC

    if(0 == rt_memcmp((const char*)&s_mac_addr_info, (const char*)&mac_addr_info, SEUB_MAC_ADDR_LEN)) // 如果设置的MAC地址和现存的MAC地址一致，则直接返回，减少EEPROM的写次数
    {
        return SUCCESSFUL;
    }

    if((handle_storage(write_opr, EEP_DATA_PART, (unsigned char* )&mac_addr_info, SEUB_MAC_ADDR_LEN + 2, SEUB_MAC_ADDR_INFO_OFFSET) != SUCCESSFUL) ||
       (handle_storage(write_opr, EEP_DATA_PART, (unsigned char* )&mac_addr_info, SEUB_MAC_ADDR_LEN + 2, SEUB_MAC_ADDR_BACKUP_INFO_OFFSET) != SUCCESSFUL))
    {
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA;
        return FAILURE;
    }

    rt_memcpy_s((char *)&s_mac_addr_info, sizeof(s_mac_addr_info), (const char*)&mac_addr_info, sizeof(mac_addr_info)); // 设置成功后更新全局变量
    rt_device_control(ETH_dev, NIOCTL_SADDR, mac_addr_info.mac_addr); // 将MAC地址设置给网卡
    return SUCCESSFUL;
}

Static int eeprom_self_test_pack(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    int offset = 0;
    unsigned char eeprom_status = TEST_FAULT;
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;
    unsigned short test_data = 0xA5A5;
    if(handle_storage(write_opr, EEP_DATA_PART, (unsigned char*)&test_data, sizeof(test_data), EEPROM_TEST_OFFSET) != SUCCESSFUL)
    {
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA;
        return FAILURE;
    }
    test_data = 0;
    if(handle_storage(read_opr, EEP_DATA_PART, (unsigned char*)&test_data, sizeof(test_data), EEPROM_TEST_OFFSET) != SUCCESSFUL)
    {
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA;
        return FAILURE;
    }
    if(test_data == 0xA5A5)
    {
        eeprom_status = TEST_NORMAL;
    }
    data_buff[offset] = eeprom_status;
    offset++;
    ((cmd_buf_t*)cmd_buff)->data_len = offset;
    return SUCCESSFUL;
}

Static int usart_init(rt_device_t dev)
{
    struct serial_configure def_config = RT_SERIAL_CONFIG_DEFAULT;
    def_config.baud_rate = BAUD_RATE_9600;
    rt_device_control(dev, RT_DEVICE_CTRL_CONFIG, &def_config);

    if (rt_device_open(dev, RT_DEVICE_FLAG_RX_NON_BLOCKING | RT_DEVICE_FLAG_TX_BLOCKING) != RT_EOK) {
        return FAILURE;
    }
    return SUCCESSFUL;
}

// 串口接收到数据之后进行比对
Static int usart_rev_handle(rt_device_t dev)
{
    char temp_buff[10] = {0};
    int count = 0;
    int recv_len = 0;
    int len = 0;
    int size = 10;
    for(count = 0; count < 50; count++)
    {
        rt_thread_mdelay(10);
        len = rt_device_read(dev, 0, &temp_buff[0] + recv_len, size);
        if(len)
        {
            recv_len += len;
            size -= len;
        }
    }
    if(rt_memcmp(&s_usart_test_frame[0], &temp_buff[0], sizeof(s_usart_test_frame) / sizeof(s_usart_test_frame[0])) == 0)
    {
        return TEST_NORMAL;
    }
    return TEST_FAULT;
}

Static int usart_loopback_test_parse(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(s_apptest_trig_succ == TRUE, NO_NEED_REPONSE);
    rt_device_t usart1_dev = rt_device_find("usart1");
    rt_device_t usart2_dev = rt_device_find("usart2");
    if(usart_init(usart1_dev) != SUCCESSFUL)
    {
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA ;
        return FAILURE;
    }
    set_apptest_usart_test_flag(TRUE); // 串口回环测试开始，停止串口线程的中断
    // usart1发送，usart2接收
    rt_device_write(usart1_dev, 0, (unsigned char*)&s_usart_test_frame[0], sizeof(s_usart_test_frame));
    s_usart_test_result = usart_rev_handle(usart2_dev);
    // usart2发送，usart1接收
    rt_device_write(usart2_dev, 0, (unsigned char*)&s_usart_test_frame[0], sizeof(s_usart_test_frame));
    s_usart_test_result |= usart_rev_handle(usart1_dev);
    rt_device_close(usart1_dev);
    set_apptest_usart_test_flag(FALSE); // 串口回环测试结束，开启串口线程的中断
    return SUCCESSFUL;
}

Static int usart_loopback_test_pack(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    int offset = 0;
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;
    data_buff[offset++] = s_usart_test_result;
    ((cmd_buf_t*)cmd_buff)->data_len = offset;

    return SUCCESSFUL;
}

/* 命令总表,必须以空字符串""结束 */
Static cmd_t no_poll_cmd_tab[] = {
    {SEUB_TRIG_APPTEST, CMD_PASSIVE, &cmd_req[1], &cmd_ack[1], sizeof(s1363_cmd_head_t), NULL, NULL, apptest_trig_pack, SELF_PARSE},    // apptest触发命令
    {SEUB_EXIT_APPTEST, CMD_PASSIVE, &cmd_req[2], &cmd_ack[2], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, apptest_exit_parse},    // apptest退出命令
    {SEUB_GET_ANA_INPUT_DATA, CMD_PASSIVE, &cmd_req[3], &cmd_ack[3], sizeof(s1363_cmd_head_t), NULL, NULL, get_ana_input_data_pack, apptest_comm_parse},    // 获取模拟量AI数据
    {SEUB_GET_DIG_INPUT_DATA, CMD_PASSIVE, &cmd_req[4], &cmd_ack[4], sizeof(s1363_cmd_head_t), NULL, NULL, get_dig_input_data_pack, apptest_comm_parse},    // 获取数字量DI数据
    {SEUB_SET_ANA_OUTPUT_DATA, CMD_PASSIVE, &cmd_req[5], &cmd_ack[5], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, set_ana_output_data_parse},    // 设置模拟量AO数据
    {SEUB_SET_DIG_OUTPUT_DATA, CMD_PASSIVE, &cmd_req[6], &cmd_ack[6], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, set_dig_output_data_parse},    // 设置数字量DO数据
    {SEUB_GET_FACTORY_INFO, CMD_PASSIVE, &cmd_req[7], &cmd_ack[7], sizeof(s1363_cmd_head_t), NULL, NULL, get_fact_info_pack, apptest_comm_parse},    // 获取厂家信息
    {SEUB_SET_FACTORY_INFO, CMD_PASSIVE, &cmd_req[8], &cmd_ack[8], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, set_fact_info_parse},    // 设置厂家信息
    {SEUB_GET_MAC_ADDR, CMD_PASSIVE, &cmd_req[9], &cmd_ack[9], sizeof(s1363_cmd_head_t), NULL, NULL, get_mac_addr_pack, apptest_comm_parse},    // 获取MAC地址
    {SEUB_SET_MAC_ADDR, CMD_PASSIVE, &cmd_req[10], &cmd_ack[10], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, set_mac_addr_parse},    // 设置MAC地址
    {SEUB_EEPROM_SELF_TEST, CMD_PASSIVE, &cmd_req[11], &cmd_ack[11], sizeof(s1363_cmd_head_t), NULL, NULL, eeprom_self_test_pack, apptest_comm_parse},    // EEPROM自测命令
    {SEUB_USART_LOOPBACK_TEST, CMD_PASSIVE, &cmd_req[12], &cmd_ack[12], sizeof(s1363_cmd_head_t), NULL, NULL, usart_loopback_test_pack, usart_loopback_test_parse},    // 串口自回环测试
    {0},
};

Static dev_type_t s_seub_apptest_dev =
{
    DEV_NORTH_SEUB_APPTEST, 1, PROTOCOL_YD_1363, LINK_DAC_IP, R_BUFF_LEN_1024, S_BUFF_LEN_1024, 0, no_poll_cmd_tab, NULL
};

dev_type_t* init_seub_dev_apptest(void)
{
    init_eeprom_info();
    return &s_seub_apptest_dev;
}

// 在网口建链之后，初始化SEUB存储在eeprom上的信息（厂家信息和MAC地址）
Static int init_eeprom_info(void)
{
    eeprom_data_process(s_factory_info, read_opr, SEUB_FACTORY_INFO_EEPROM_LEN, SEUB_FACTORY_INFO_OFFSET); // 读取厂家信息
    eeprom_data_process((unsigned char *)&s_mac_addr_info, read_opr, SEUB_MAC_ADDR_LEN + 2, SEUB_MAC_ADDR_INFO_OFFSET); // 读取MAC地址
    return SUCCESSFUL;
}