#include "BattSleepCtrl.h"
#include "led.h"
#include "hisdata.h"
#include "protocol_V20.h"
#include "qtp.h"

BOOLEAN ButtonSleepModeManagement(Button_State *pButtonSta, T_BattDealInfoStruct *pBattDeal, T_BCMAlarmStruct *pAlarm)
{
    if (pButtonSta->ucCurrentMode == BUT_SLEEP || GetQtpSleepStatus())
    {
            SetQtpSleepStatus(False);
            SetTowerSleepStatus(False);
            SetChargeMode(BATT_MODE_OFFLINE);
            SaveAction(GetActionId(CONTOL_BUTTON_SLEEP), "KeyEnterSleep");
            pBattDeal->bModeChange = TRUE;
            return True;
    }
    return False;
}
