/**
 * @file     self_check.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2023-02-20
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */

 
#ifndef _SELF_CHECK_H
#define _SELF_CHECK_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include <stdio.h>

#define SELF_CHECK_NORMAL 0
#define SELF_CHECK_ABNORMAL 1

short south_self_check();

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _SELF_CHECK_H