#include <stdio.h>
#include <rtthread.h>
#include <rtdbg.h>
#include <board.h>
#include "utils_heart_beat.h"
#include "utils_thread.h"
#include "thread_id.h"
#include "msg.h"
#include "utils_data_transmission.h"
#include "utils_time.h"
#include "led.h"
#include "server_id.h"
#include "utils_server.h"
#include "syswatch.h"
#include "app_config.h"
#include "main.h"
#include "sample.h"
#include "comm_can.h"
#include "comm_can3.h"
#include "comm_usart.h"
#include "exchange_data_info.h"
#include "update_manage.h"
#include "storage.h"
#include "partition_table.h"
#include "alarm_register.h"
#include "alarm_mgr_api.h"
#include "protocol_layer.h"

static char s_led_thread_stack[LED_THREAD_STACK_SIZE] RAM_SECTION_BSS;
static char s_sample_thread_stack[SAMPLE_THREAD_STACK_SIZE] RAM_SECTION_BSS;
static char s_thread_stack_can1[THREAD_STACK_SIZE_CAN1] RAM_SECTION_BSS;
static char s_thread_stack_can3[THREAD_STACK_SIZE_CAN3] RAM_SECTION_BSS;
static char s_thread_stack_usart[THREAD_STACK_SIZE_USART] RAM_SECTION_BSS;
static char s_alarm_namage_thread_stack[ALARM_MANAGE_THREAD_STACK_SIZE] RAM_SECTION_BSS;

static protocol_process_t protocol_process_tab[] = {
    {PROTOCOL_BOTTOM_COMM, bottom_pack, bottom_parse},
};

static server_info_t g_server_group[] = {
    {{{LED_SERVER_ID,   "led",  sizeof(s_led_thread_stack),  s_led_thread_stack,  SERVER_PRIO_LOW}}, led_init_sys, led_ctrl},
    {{{SAMPLE_SERVER_ID,   "sample",  sizeof(s_sample_thread_stack),  s_sample_thread_stack,  SERVER_PRIO_MID}}, sample_init_sys, sample_main},
    {{{CANCOMM_SERVER_ID , "CanComm", sizeof(s_thread_stack_can1), s_thread_stack_can1, SERVER_PRIO_MID}}, CAN_comm_init, process_CAN_comm},
    {{{CANCOMM3_SERVER_ID, "CanComm3", sizeof(s_thread_stack_can3), s_thread_stack_can3, SERVER_PRIO_MID}}, CAN3_comm_init, process_CAN3_comm},
    {{{USARTCOMM_SERVER_ID, "UsartComm", sizeof(s_thread_stack_usart), s_thread_stack_usart, SERVER_PRIO_MID}}, usart_comm_init, process_usart_comm},
    {{{ALARM_MANAGE_SERVER_ID,   "alarm_namage",  sizeof(s_alarm_namage_thread_stack),  s_alarm_namage_thread_stack,  SERVER_PRIO_LOW}}, init_alarm_manage, alarm_main},
};

static link_type_t link_type_tab[LINK_TYPE_MAX] = {
    {LINK_CAN, can_dev_init, can_dev_read, can_dev_write, can_dev_set},  // 通过宏控制
    {LINK_COM, s485_dev_init, com_dev_read, com_dev_write, com_dev_set},
};

static link_inst_t link_inst_tab[] = {
    {LINK_BMU,      LINK_CAN, "can1",   CAN_FRAME_DATA_LEN_8},
    {LINK_BCMU,     LINK_CAN, "can4",   CAN_FRAME_DATA_LEN_8},
    {BMU_LINK_BCMU, LINK_CAN, "can0",   CAN_FRAME_DATA_LEN_8},
    {LINK_FIRE,     LINK_COM, "usart1"},
    {LINK_BSMU_BAK, LINK_CAN, "can5",   CAN_FRAME_DATA_LEN_16},
    {LINK_DAC_IP,    LINK_IP, "wlan1"},
    {LINK_AIRSWITCH,    LINK_COM, "usart0"},
    {LINK_CSU, LINK_COM, "uart1"},
    {LINK_BCCU_SCU_USART, LINK_COM, "usart1"},
    {LINK_INVERTER, LINK_COM, "uart8"},     //串口模式
    {LINK_DC_AC,      LINK_CAN, "can1",   CAN_FRAME_DATA_LEN_8},
    {LINK_BCCU_CAN1, LINK_CAN, "can0", CAN_FRAME_DATA_LEN_8},
    {LINK_BCCU_CAN3, LINK_CAN, "can1", CAN_FRAME_DATA_LEN_8},
    {LINK_604A_MAIN,  LINK_COM, "usart0"},
    {LINK_604A_UIB01,  LINK_COM, "usart0"},
    {LINK_ILVDB,  LINK_COM, "rs485dev0"},
    {LINK_BSC_UIB03,  LINK_COM, "rs485dev0"},
    {LINK_DC_DC,    LINK_CAN, "can2",   CAN_FRAME_DATA_LEN_8},
    {LINK_BSMU,     LINK_CAN, "can2",   CAN_FRAME_DATA_LEN_8},
};


int main(void){
    init_flash_page_size(FLASH_PAGE_SIZE);
    init_crc();
    if (SUCCESSFUL != storage_init(&part_tab[0], sizeof(part_tab)/sizeof(part_info_t))) {
        return FAILURE;
    }
    initFileSys();
    syswatch_init();
    init_exchange_data_info();
    register_link_type_info(link_type_tab, sizeof(link_type_tab) / sizeof(link_type_t));
    register_link_inst_tab_info(link_inst_tab, sizeof(link_inst_tab)/sizeof(link_inst_tab[0]));
    // 注册 协议所使用的函数接口
    register_protocol_process_info(protocol_process_tab, sizeof(protocol_process_tab) / sizeof(protocol_process_t));
    create_server(&g_server_group[0], sizeof(g_server_group)/sizeof(g_server_group[0]));
    rt_thread_delay(10000);
    BeginDownload(FLAG_BACKUP);
    return SUCCESSFUL;
}
