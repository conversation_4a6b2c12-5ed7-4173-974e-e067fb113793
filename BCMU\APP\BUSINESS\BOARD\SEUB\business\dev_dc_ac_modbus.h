#ifndef _DEV_DC_AC_MODBUS_H
#define _DEV_DC_AC_MODBUS_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "device_type.h"
#include "cmd.h"
#include "linklayer.h"
#include "protocol_layer.h"

/* 缓冲区长度 */
#define R_BUFF_LEN_2048                    2048       ///<  接收缓冲区长度
#define S_BUFF_LEN_2048                    2048      ///<  发送缓冲区长度

// inverter_modbus命令一标识ID 定义
#define  CMD_INVERTER_MODBUS_GET_ANA              0x01 ///<  获取模拟量
#define  CMD_INVERTER_MODBUS_SET_PARA             0x03 ///<  获取参数量
#define  CMD_INVERTER_MODBUS_GET_PARA             0x04 ///<  设置参数量


#define GET_ANA_DATA        0x04            //<获取模拟量、状态量、版本信息
#define GET_PARA_DATA       0x03            //<获取参数量
#define SET_PARA_DATA       0x10            //<设置参数量
#define SET_SINGLE_PARA_DATA       0x06            //<设置参数量


#define PV_INVERTER_MODBUS_SERVER_ID          NORTH_SERVER + 4

#define NO_MATCH_REGISTER_ADDR -1

#define DATA_LEN_2          2//数据长度为2
#define DATA_LEN_4          4 //数据长度为4
#define DATA_LEN_6          6 //数据长度为6

typedef enum {

    TYPE_INT16S_SEUB,
    TYPE_INT16U_SEUB,
    TYPE_CHAR_SEUB,
    TYPE_INT32S_SEUB,
    TYPE_INT32U_SEUB,
    TYPE_BIT_SEUB,
    TYPE_FLOAT_SEUB,
    TYPE_STR_SEUB,
    TYPE_DATE_SEUB

}data_type_e;

typedef enum {

    TYPE_ALAM = 0,
    TYPE_PARA,
    TYPE_ANA

}sid_type_e;

typedef struct{

    unsigned short      register_addr;                          //寄存器地址
    unsigned char       reserve_flag;                           //该寄存器值是否保留，1是保留
    data_type_e         old_type;                               //原始数据类型
    data_type_e         type;                                   //modbus传输数据类型
    unsigned char       precision;                              //modbus精度
    unsigned char       src_data_len;                           //南向原始数据长度
    unsigned char       data_len;                               //modbus上送数据长度
    sid_type_e          sid_type;                               //sid的类型
    unsigned int        data_addr;                              //数据取值地址
                                                                 
}modbus_addr_map_data_t;

dev_type_t* init_dev_inverter_modbus_main(void);
int parse_inverter_para_data(void* dev_inst, void *cmd_buf);
int parse_start_addr_and_reg_nums(void* dev_inst, void* cmd_buf);
int pack_inverter_ana_or_para_data(void* dev_inst, void* cmd_buf);
void set_para_signal_data(int dest_type,void *data,int index);
void modbus_register_cmd_table();
void numchar_to_bit_to_int16(unsigned char *data_buff,int total_len,int *index,int percision);
int get_dc_ac_signal_data_buff(unsigned char *data_buff,int total_len,int *index);
int find_register_start_addr_index(int reg_addr);
void is_set_time(int index);
void check_para_reserve_flag(int type , int index , unsigned char *data_buff , int data_valude_indx );
void get_para_and_set_value(unsigned int para_id ,unsigned int data_id);
void save_all_para();


#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  // 