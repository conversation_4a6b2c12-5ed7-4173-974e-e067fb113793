#ifndef _BCMU_ALARM_H
#define _BCMU_ALARM_H

#ifdef __cplusplus
extern "C" {
#endif    //  __cplusplus
#include "app_config.h"

/* 告警码定义 */
typedef enum {
    CLUSTER_VOLT_HIGH = 1,                      ///< 电芯过压告警
    CLUSTER_VOLT_LOW,                           ///< 电芯欠压告警
    CLUSTER_VOLT_DIFF_OVER,                     ///< 电芯压差过大告警
    CLUSTER_CHG_HIGH_TEMP,                      ///< 电芯充电高温告警
    CLUSTER_DISCHG_HIGH_TEMP,                   ///< 电芯放电高温告警
    CLUSTER_CHG_LOW_TEMP,                       ///< 电芯充电低温告警
    CLUSTER_DISCHG_LOW_TEMP,                    ///< 电芯放电低温告警
    CLUSTER_OVER_TEMP_DIFF,                     ///< 电芯温差过大
    CLUSTER_CAPACITOR_SOFT_VOLT_HIGH,           ///< 电池电容软过压
    CLUSTER_CAPACITOR_HARD_VOLT_HIGH,           ///< 电池电容硬过压
    CLUSTER_CAPACITOR_VOLT_LOW,                 ///< 电池电容欠压告警
    CLUSTER_BUSBAR_SOFT_VOLT_HIGH,              ///< 母排软过压
    CLUSTER_BUSBAR_HARD_VOLT_HIGH,              ///< 母排硬过压
    CLUSTER_BUSBAR_VOLT_LOW,                    ///< 母排欠压告警
    CLUSTER_BUSBAR_VOLT_OVER_RANGE,             ///< 母排电压超范围
    CLUSTER_COMPEN_CAPACITOR_SOFT_VOLT_HIGH,    ///< 补偿电容软过压
    CLUSTER_COMPEN_CAPACITOR_HARD_VOLT_HIGH,    ///< 补偿电容硬过压
    CLUSTER_CLLC_VOLT_HIGH,                     ///< CLLC过压
    CLUSTER_CLLC_VOLT_LOW,                      ///< CLLC欠压
    CLUSTER_CONNECT_BAD,                        ///< 模块连接不到位
    CLUSTER_CHG_CURR_HIGH,                      ///< 充电过流
    CLUSTER_DISCHG_CURR_HIGH,                   ///< 放电过流
    CLUSTER_CURR_BALENCE_BAD,                   ///< 均流不良告警
    CLUSTER_CHG_CIRCUIT_DAMAGE,                 ///< 充电回路损坏告警
    CLUSTER_DISCHG_CIRCUIT_DAMAGE,              ///< 放电回路损坏告警
    CLUSTER_LIMIT_CURR_CIRCUIT_DAMAGE,          ///< 限流回路损坏告警
    CLUSTER_OUTPUT_OVERLOAD,                    ///< 输出过载
    CLUSTER_BUS_SHORT_CIRCUIT,                  ///< BUS短路告警
    CLUSTER_FAN_FAULT,                          ///< 风扇故障
    CLUSTER_EEPROM_FAULT,                       ///< EEPROM故障
    CLUSTER_CAN_COMM_FAULT,                     ///< 模块间CAN通讯异常告警
    CLUSTER_ENV_TEMP_HIGH,                      ///< 环境温度高告警
    CLUSTER_BOARD_INTER_TEMP_HIGH_PROTEC,       ///< 单板内部过温保护告警
    CLUSTER_SLOW_START_ERROR,                   ///< 缓启动异常
    CLUSTER_CHG_VOLT_LOW_PROTEC,                ///< 电池充电欠压保护告警
    CLUSTER_POWER_CONNEC_SELF_CHECK_RESIS_ABNORMAL,     ///< 功率连接器自检阻值异常
    CLUSTER_RELAY_PULL_IN_EXCEP_PROTEC,                 ///< 继电器吸合异常保护
    CLUSTER_EXTERNAL_BAT_VOL_HIGH,                      ///< 外部电池过压告警
    CLUSTER_EXTERNAL_BAT_VOL_LOW,                       ///< 外部电池欠压告警
    CLUSTER_HIGH_VOLT_DIFF_BETWEEN_BUSBAR_AND_BAT,      ///< 母排与电池电压差值大告警
    CLUSTER_SOC_LOW,                                    ///< 电池簇SOC低告警
    CLUSTER_SOC_LOW_PROTEC,             ///< 电池簇SOC低保护
    CLUSTER_SOH_LOW,                    ///< 电池簇SOH低告警
    CLUSTER_SOH_LOW_PROTEC,             ///< 电池簇SOH低保护
    CLUSTER_ENV_TEMP_HIGH_PROTEC,       ///< 电池簇环境温度高保护
    CLUSTER_ENV_TEMP_LOW_PROTEC,        ///< 电池簇环境温度低保护
    BMU_CELL_DAMAGE_PROTEC,             ///< BMU电芯损坏保护
    BMU_CELL_TEMP_ABNORMAL,             ///< BMU电芯温度异常
    BMU_CELL_TEMP_INVALID,              ///< BMU电芯温度无效
    BMU_CELL_VOLT_INVALID,              ///< BMU电芯电压采样失效
    BMU_BALENCE_CIRCUIT_FAULT,          ///< BMU均衡电路故障告警
    BMU_COMM_FAIL,                      ///< BMU通信断
    CLUSTER_INSULATION_RESIS_ALM,       ///< 绝缘电阻告警
    CLUSTER_CARBON_MONOXIDE_ALM,        ///< 一氧化碳浓度告警
    BCMU_ALARM_EPO,
    CLUSTER_COMM_FAIL,                  ///< 簇控制器通信断
    BCMU_INSULATION_STATUS_FAIL,        ///< 柜绝缘检测状态
    BCMU_FIRE_STATUS,                   ///< 柜消防状态
    ALARM_BUS_INT,                      ///< 通讯断
    ALARM_BUSBAR_OVER_TEMP,             ///< 铜排过温告警
    BATT_MOD_TEMP_HIGH,                 ///< 电池模块过温告警
    TIME_SYNC_ALM,                      ///< 时间同步告警
    ALARM_CODE_MAX
}bcmu_alarm_code_e;

void register_bcmu_alarm(void);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _BCMU_ALARM_H
