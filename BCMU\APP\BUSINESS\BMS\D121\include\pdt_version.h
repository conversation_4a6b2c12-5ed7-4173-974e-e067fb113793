
#ifndef _PDT_VERSION_H_
#define _PDT_VERSION_H_

#ifdef __cplusplus
extern "C" {
#endif // __cplusplus

#define BMS_VER          "V1.00.00.00"
#define BMS_VER_SOFTBANK "V1.00.00.00s"
#define MAJOR_VER        0 //兼容PAD协议旧命令(41H,82H)，上送BMS_VER的后两位
#define MINOR_VER        0 //兼容PAD协议旧命令(41H,82H)，上送BMS_VER的后两位

#define SOFTWARE_RELEASE_YEAR  (2023)
#define SOFTWARE_RELEASE_MONTH (10)
#define SOFTWARE_RELEASE_DATE  (19)
// #define BMS_TYPE_NEW           "BR4850S1" // BMS型号
#define BMS_SYS_NAME_LITE_DEF  "D121" // BMS系统名称，短称
#define BATT_CORP_NAME_DEF     "ZTE Corporation"

#ifdef __cplusplus
}
#endif // __cplusplus

#endif // _PDT_VERSION_H_

