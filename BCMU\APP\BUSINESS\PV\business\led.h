#ifndef _PV_INVERTER_LED_H
#define _PV_INVERTER_LED_H
#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

/* 控制状态 */
#define LED_START_BLINK    1
#define LED_STOP_BLINK     2

/* 升级逻辑灯*/
#define NO_UPDATE_STATUS        1
#define UPDATING_STATUS         2
#define UPDATE_FINISH_STATUS    3
/* LED灯 */
typedef enum {
    LED_RUN_STATE = 0,  ///< 运行状态指示灯
    LED_ALM_STATE,      ///< 告警状态指示灯
    LED_GRID_STATE,     ///< 并网状态指示灯
}led_type_e;

typedef struct {
    unsigned char led_no;         ///< LED
    unsigned char ctrl_state;     ///< 控制状态
    unsigned char is_updating;    ///< 升级标志
}led_ctrl_msg_t;

void* init_led(void *param);
void led_thread_entry(void* parameter);
void grid_led_deal();
void run_led_deal();
void alarm_led_deal();
void ctrl_led_when_update(unsigned char state);
void send_ctrl_msg_to_led(unsigned int msg_id, led_ctrl_msg_t* ctrl_msg);
void recv_msg_deal();
void deal_led_recover_msg();
void deal_led_ctrl_msg(led_ctrl_msg_t* ctrl_msg);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _PV_INVERTER_LED_H
