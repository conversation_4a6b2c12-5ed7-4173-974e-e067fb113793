/**
 * @file     device_type.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2017-04-19
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */

#ifndef _ACMU_PROTOCOL_MODBUS_H
#define _ACMU_PROTOCOL_MODBUS_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "device_type.h"

/*MODBUS协议的命令唯一标识定义 */
#define ACMU_MODBUS_GET_DATA_1           1       ///<  获取0x16e数据
#define ACMU_MODBUS_GET_DATA_2           2       ///<  获取0x182数据
#define ACMU_MODBUS_GET_TOTAL_ENERGY     3       ///<  获取交流电表总电能数据
#define ACMU_MODBUS_GET_SYSTEM_VERSION   4       ///<  获取交流电表版本数据
#define ACMU_MODBUS_SET_ENERGY_EMPTY     5       ///<  设置电能清0

/* 寄存器地址定义 */
#define REG_TOTAL_ENERGY                0x100

/* 功能码定义 */
#define CMD_GET_DATA                   0x03   ///<  获取数据
#define CMD_SET_DATA                   0x06   ///<  设置单个数据
#define CMD_GET_VER_DATA               0x53   ///<  读程序版本和电表型号

#define NO_MATCH_REGISTER_ADDR -1

typedef struct{
    unsigned short cmd_id;
    unsigned char func_code;
    unsigned short register_addr;
    unsigned short register_num;
}cmd_id_to_register_info_t;

dev_type_t* init_dev_acmu_south(void);
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _ACMU_PROTOCOL_MODBUS_H

