#ifndef BALANCE_TIME_STATISTICS_H_
#define BALANCE_TIME_STATISTICS_H_
#include "battery.h"
#include "common.h"
#include "sample.h"
#include "fileSys.h"
#include "dfs_fs.h"
#include "rtdef.h"

#ifdef __cplusplus
extern "C" {
#endif

#define CELLBAL_ONEMONTH_DAYS  (30)
#define CELLBAL_HISDATE_LENGTH (176)
#define CUR_A_TO_MA (1000)
/* seconds per day */
#define SPD (24*60*60)
#define SPH (60*60)
#define SPM (60)
typedef enum
{
    JumpNone = 0,
    JumpExist,
}Em_TimeType;

typedef enum
{
    DELCAPINFO = 0,
    DELHISRECORD = 1,

}Em_DelType;
#pragma pack(push, 1)

typedef struct
{
    BYTE    ucLastState;
    BYTE    ucCurrentState;
    BOOLEAN bStateChange;
}T_CellBalState;
typedef struct
{
    ULONG  ulInitTick;
    ULONG  ulBalTime;
}T_CellBalTime;
typedef struct
{
   T_CellBalState CellBalState[CELL_VOL_NUM_MAX];
   T_CellBalTime  CellBalTime[CELL_VOL_NUM_MAX];
}CellBalance_Staticis;

typedef struct
{
    ULONG ulBalTime[CELL_VOL_NUM_MAX];
}T_HisCellBalTimeData;

typedef struct
{
    FLOAT fBalCAP[CELL_VOL_NUM_MAX];
}T_HisCellBalCAPData;
typedef struct
{
    rt_uint16_t                wReadPoint;
    time_t tStartTime;
    time_t tPowerOffTime;
    uint8_t ucDateNum;
    uint8_t ucAreaNum;
    T_HisCellBalTimeData       tCellBalTimeToday;
    WORD           wCRC;
}T_HisCellBalTimeDataStruct;
typedef struct
{
    time_t tTime;
    T_HisCellBalCAPData       tCellBalCAPHis;
} T_BalTimeRecord;


typedef struct
{
  Em_TimeType eTimeType;
  time_t tJumpTickdata;
}T_TimeJump;

#pragma pack(pop)

enum
{
    CELLBAL_TODAY = 0,
    CELLBAL_DATE ,
};
BOOLEAN GetCellBalTimeSaveFlag(void);
BOOLEAN SetCellBalTimeSaveFlag(BOOLEAN Flage);
rt_err_t  readCellBalTimeInfo(T_HisCellBalTimeDataStruct *tHisCellBalTimeDate);
rt_err_t  writeCellBalTimeInfo(T_HisCellBalTimeDataStruct *tHisCellBalTimeDate);
rt_err_t CellBalInit(T_HardwareParaStruct *tHardwarePara);
 rt_err_t CircleCheckCellBalSave(void);
rt_err_t CellBalTime_Staticise(T_HardwareParaStruct *tHardwarePara, WORD wBalanceCode);
rt_err_t BalTimeStatistics(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn);
BOOLEAN SetCellSavePowerOffFlag( BOOLEAN Flage );
BOOLEAN GetCellSavePowerOffFlag(void);
rt_uint16_t GetBalanceTimeRecordNum(void); //获取均衡容量的总记录条数
rt_uint16_t GetProtoBalanceCapacityPoint(record_north_protocol_e wNorthProtocolIndex); //获取当前的读取点
void MoveBalTimeHisDataPoint(rt_uint8_t ucIncNum); //移动读取点
BOOLEAN ReadBalTimeHisDataRecord(T_BalTimeRecord *tBalTimeRecord); //读取均衡容量记录
void ReadBalanceCapacityRecordData(rt_uint16_t wNum,rt_uint8_t *ptHisData, record_north_protocol_e wNorthProtocolIndex); //协议读取一条故事点
void SetBalTimeHisDataPoint(WORD wHisDataReadPoint);
rt_err_t DeleteBalTimeHisDataRecord(BYTE ucType);
 BYTE GetSelfDischFaultFlag(void);
 BYTE GeCapDCDRFaultFlag(void);
BOOLEAN GetRealBalCAP(T_HisCellBalCAPData *tHisCellBalCAPData,T_HisCellBalTimeDataStruct *tHisCellBalTimeData,
                      T_HardwareParaStruct *pHardwarePara,SHORT *wStaticTime);   //获取实时均衡容量统计信息
void GetCellBalTimeData(T_HisCellBalTimeDataStruct *tHisCellBalTimeData);
BOOLEAN LastBalCapRecordJudge(void); //判断是否读到最后一条数据
BOOLEAN GetRealBalCAP(T_HisCellBalCAPData *tHisCellBalCAPData,T_HisCellBalTimeDataStruct *tHisCellBalTimeData,
                      T_HardwareParaStruct *pHardwarePara,SHORT *wStaticTime);   //获取实时均衡容量统计信息
void GetCellBalTimeData(T_HisCellBalTimeDataStruct *tHisCellBalTimeData);
#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif
#endif

