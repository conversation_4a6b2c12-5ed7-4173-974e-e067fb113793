// #include "dev_north_dxcb_modbus.h"
#include "utils_data_transmission.h"
#include "realdata_id_in.h"
#include "para_id_in.h"
#include "south_dev_common.h"
#include "south_dev_modbus.h"


/* 命令请求头 */
static modbusrtu_cmd_head_t cmd_req[] = {
    {READ_HOLD_REG},
    {READ_INPUT_REG},
    {WRITE_SINGLE_REG},
    {WRITE_MULTI_REG},
};

/* 命令应答头 */
static modbusrtu_cmd_head_t cmd_ack[] = {
    {READ_HOLD_REG},
    {READ_INPUT_REG},
    {WRITE_SINGLE_REG},
    {WRITE_MULTI_REG},
};

//解析实时量寄存器地址：53264-53287
static data_info_id_verison_t parse_real_data_info[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},//数据的字节长度
    
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_STOP_STATUS},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_TEMP_MANAGE},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_IGBT_COOL_PHASE_STATUS},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_IN_SYS_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_DIR_RATATION_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_BYPASS_TEMP_MANAGE},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_FIELD_WEAK},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_DC_CURR_LIMIT},

    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_D1_STATUS},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_E1_STATUS},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_K1_STATUS},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_DC_LINK_OVER},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_IGBT_TEMP_ALARM},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_IN_TEMP_ALARM},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_DIR_CHANGE_ACTIVE},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_FAN_BAD},

    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_OC_ALARM_FAULT_CODE, 8},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_OC_ALARM_FAULT_CODE+1, 8},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_OC_ALARM_FAULT_CODE+2, 8},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_OC_ALARM_FAULT_CODE+3, 8},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_OC_ALARM_FAULT_CODE+4, 8},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_OC_ALARM_FAULT_CODE+5, 8},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_OC_ALARM_FAULT_CODE+6, 8},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_OC_ALARM_FAULT_CODE+7, 8},

    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_OC_ALARM_OVER_LIMIT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_OC_ALARM_DIRECTION},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_OC_ALARM_LIFECYCLE},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_OC_ALARM_VIBRATION},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_OC_ALARM_APP_SYS},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_OC_ALARM_MOTOR_SYS},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},

    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_IGBT_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_EARTH_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_DC_VOL_HIGH},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_DC_VOL_LOW},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_INPUT_VOL_HIGH},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_INPUT_VOL_LOW},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_MAIS_FAIL},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},

    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_POSITION_ROTOR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_MOTOR_BLOCK},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_PEAK_CURR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAFE_SHUTDOWN},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_TEMP_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_MOTOR_START},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_COM_ERROR},

    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_IDENTIFIER_ERROR_REASON, 8},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_IDENTIFIER_ERROR_REASON+1, 8},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_IDENTIFIER_ERROR_REASON+2, 8},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_IDENTIFIER_ERROR_REASON+3, 8},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_IDENTIFIER_ERROR_REASON+4, 8},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_IDENTIFIER_ERROR_REASON+5, 8},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_IDENTIFIER_ERROR_REASON+6, 8},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_IDENTIFIER_ERROR_REASON+7, 8},

    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_LIMIT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_ROTAT_DIR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_LIFETIME_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_VIBRATION_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_APP_SYS_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_MOTOR_CTRL_SYS_ERROR},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},

    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_ACTUAL_SPEED},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_MOTOR_CURR},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_DC_VOL},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_PEAK_LINE_VOL},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_IGBT_TEMP},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_IN_TEMP},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_MCU_TEMP},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_POWER},
};

static data_info_id_verison_t parse_fan_switch_info[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},//数据的字节长度
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_FAN_CTRL_FAN_SWITCH},
};

static data_info_id_verison_t parse_speed_ctrl_info[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},//数据的字节长度
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_FAN_WIND_SPEED_CTRL},
};

static data_info_id_verison_t parse_speed_ctrl_mode_info[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},//数据的字节长度
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_FAN_WIND_SPEED_CTRL_MODE},
};

static data_info_id_verison_t parse_comm_fail_speed_info[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},//数据的字节长度
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_FAN_COMM_FAIL_SPEED},
};

static data_info_id_verison_t parse_fan_speed_min_info[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},//数据的字节长度
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_FAN_SPEED_MIN},
};

static data_info_id_verison_t parse_fan_speed_max_info[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},//数据的字节长度
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_FAN_SPEED_MAX},
};

static data_info_id_verison_t parse_watch_dog_ctrl_info[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},//数据的字节长度
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_FAN_WATCH_DOG_CTRL},
};

static data_info_id_verison_t parse_change_time_info[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},//数据的字节长度
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_FAN_CHANGE_TIME},
};



static cmd_parse_info_id_verison_t cmd_parse_info[] RAM_SECTION =
{
    {&parse_real_data_info[0], sizeof(parse_real_data_info)/sizeof(data_info_id_verison_t)},
    {&parse_fan_switch_info[0], sizeof(parse_fan_switch_info)/sizeof(data_info_id_verison_t)},
    {&parse_speed_ctrl_info[0], sizeof(parse_speed_ctrl_info)/sizeof(data_info_id_verison_t)},
    {&parse_speed_ctrl_mode_info[0], sizeof(parse_speed_ctrl_mode_info)/sizeof(data_info_id_verison_t)},
    {&parse_comm_fail_speed_info[0], sizeof(parse_comm_fail_speed_info)/sizeof(data_info_id_verison_t)},
    {&parse_fan_speed_min_info[0], sizeof(parse_fan_speed_min_info)/sizeof(data_info_id_verison_t)},
    {&parse_fan_speed_max_info[0], sizeof(parse_fan_speed_max_info)/sizeof(data_info_id_verison_t)},
    {&parse_watch_dog_ctrl_info[0], sizeof(parse_watch_dog_ctrl_info)/sizeof(data_info_id_verison_t)},
    {&parse_change_time_info[0], sizeof(parse_change_time_info)/sizeof(data_info_id_verison_t)},
};


/* 命令总表,必须以空字符串""结束 */
static cmd_t no_poll_cmd_tab[] = {
    {SLB_SET_FAN_SWITCH, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data, NULL,NULL},
    {SLB_SET_SPEED_CTRL, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data, NULL,NULL},
    {SLB_SET_SPEED_CTRL_MODE, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data, NULL,NULL},
    {SLB_SET_COMM_FAIL_SPEED, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data, NULL,NULL},
    {SLB_SET_FAN_SPEED_MIN, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data, NULL,NULL},
    {SLB_SET_FAN_SPEED_MAX, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data, NULL,NULL},
    {SLB_SET_WATCH_DOG_CTRL, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data, NULL,NULL},
    {SLB_SET_CHANGE_TIME, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data, NULL,NULL},
    
    {SLB_GET_FAN_SWITCH, CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[1]},
    {SLB_GET_SPEED_CTRL, CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[2]},
    {SLB_GET_SPEED_CTRL_MODE, CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[3]},
    {SLB_GET_COMM_FAIL_SPEED, CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[4]},
    {SLB_GET_FAN_SPEED_MIN, CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[5]},
    {SLB_GET_FAN_SPEED_MAX, CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[6]},
    {SLB_GET_WATCH_DOG_CTRL, CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[7]},
    {SLB_GET_CHANGE_TIME, CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[8]},
    {0},
};

static cmd_t poll_cmd_tab[] = {
    {READ_SLB_INPUT_REG, CMD_POSITIVE, &cmd_req[1], &cmd_ack[1], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[0]},
    {0},
};


int update_fan_slb_dev_type(dev_inst_t* dev_inst, char dev_num, char is_inter_fan)
{
    return change_dev_type_info_by_diff_brand(dev_inst, SOUTH_FAN_TYPE, is_inter_fan, dev_num, no_poll_cmd_tab, poll_cmd_tab);
}






