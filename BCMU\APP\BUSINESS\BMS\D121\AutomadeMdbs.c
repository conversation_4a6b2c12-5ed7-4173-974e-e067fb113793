/**************************************************************************
* 版权信息：（C）2011-2018，中兴通讯股份有限公司动力开发部版权所有
* 系统名称：MODBUS RTU协议代码
* 文件名称：AutoMdbsCode.c
* 文件说明：自动生成带modbus寄存器值及相关属性的数组
* 作    者：fsg
* 版本信息：V1.0
* 设计日期：2018-8-20
* 修改记录：
* 日    期      版  本      修改人      修改摘要
*
* 其他说明：
***************************************************************************/
#include "stddef.h"
#include "common.h"
#include "hisdata.h"
#include "sample.h"
#include "realAlarm.h"
#include "MdbsRtu.h"
#include "AutomadeMdbs.h"

/****************************变量声明********************************/
const T_OneDataAttrStruct s_atAscDataAttr[] = {
	{0x04,0,128,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,fBattVolt),1,DATA_TYPE_INT16U,2,2,NULL,},
	{0x04,0,129,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,fExterVolt),1,DATA_TYPE_INT16U,2,2,NULL,},
	{0x04,0,130,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,fEnvTemp),1,DATA_TYPE_INT16S,1,2,NULL,},
	{0x04,0,131,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,fBattCurr),1,DATA_TYPE_INT16S,2,2,NULL,},
	{0x04,0,132,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellVolt[0]),15,DATA_TYPE_INT16U,3,2,NULL,},
	{0x04,0,133,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellVolt[1]),15,DATA_TYPE_INT16U,3,2,NULL,},
	{0x04,0,134,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellVolt[2]),15,DATA_TYPE_INT16U,3,2,NULL,},
	{0x04,0,135,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellVolt[3]),15,DATA_TYPE_INT16U,3,2,NULL,},
	{0x04,0,136,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellVolt[4]),15,DATA_TYPE_INT16U,3,2,NULL,},
	{0x04,0,137,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellVolt[5]),15,DATA_TYPE_INT16U,3,2,NULL,},
	{0x04,0,138,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellVolt[6]),15,DATA_TYPE_INT16U,3,2,NULL,},
	{0x04,0,139,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellVolt[7]),15,DATA_TYPE_INT16U,3,2,NULL,},
	{0x04,0,140,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellVolt[8]),15,DATA_TYPE_INT16U,3,2,NULL,},
	{0x04,0,141,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellVolt[9]),15,DATA_TYPE_INT16U,3,2,NULL,},
	{0x04,0,142,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellVolt[10]),15,DATA_TYPE_INT16U,3,2,NULL,},
	{0x04,0,143,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellVolt[11]),15,DATA_TYPE_INT16U,3,2,NULL,},
	{0x04,0,144,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellVolt[12]),15,DATA_TYPE_INT16U,3,2,NULL,},
	{0x04,0,145,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellVolt[13]),15,DATA_TYPE_INT16U,3,2,NULL,},
	{0x04,0,146,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellVolt[14]),15,DATA_TYPE_INT16U,3,2,NULL,},
	{0x04,0,147,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellTemp[0]),4,DATA_TYPE_INT16S,1,2,NULL,},
	{0x04,0,148,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellTemp[1]),4,DATA_TYPE_INT16S,1,2,NULL,},
	{0x04,0,149,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellTemp[2]),4,DATA_TYPE_INT16S,1,2,NULL,},
	{0x04,0,150,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellTemp[3]),4,DATA_TYPE_INT16S,1,2,NULL,},
	{0x04,0,151,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,fBusCurr),1,DATA_TYPE_INT16S,2,2,0,},
	{0x04,0,152,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_BCMDataStruct,wBattSOC),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,153,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_BCMDataStruct,wBattSOH),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,154,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_BCMDataStruct,wBattCycleTimes),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,155,DATA_TYPE_INT8U,1,BCM_PART_DATA,offsetof(T_BCMDataStruct,ucChgProtSta),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,156,DATA_TYPE_INT8U,1,BCM_PART_DATA,offsetof(T_BCMDataStruct,ucDischgProtSta),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,157,DATA_TYPE_INT8U,1,BCM_PART_DATA,offsetof(T_BCMDataStruct,ucCellEquSta),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,158,DATA_TYPE_INT8U,1,BCM_PART_DATA,offsetof(T_BCMDataStruct,ucBattPackSta),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,159,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,fBattChgReqVolt),1,DATA_TYPE_INT16U,2,2,NULL,},
	{0x04,0,160,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,fBattChgReqCurr),1,DATA_TYPE_INT16U,2,2,NULL,},
	{0x04,0,161,DATA_TYPE_INT8U,1,BCM_PART_DATA,offsetof(T_BCMDataStruct,ucBattChargeEn),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,162,DATA_TYPE_INT8U,1,BCM_PART_DATA,offsetof(T_BCMDataStruct,ucBattDischEn),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,163,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,fBattMaxCurrCap),1,DATA_TYPE_INT16U,2,2,NULL,},
	{0x04,0,164,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_BCMDataStruct,ucBattBalBits),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,165,DATA_TYPE_INT8U,1,BCM_PART_DATA,offsetof(T_BCMDataStruct,ucLimit),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,166,DATA_TYPE_INT8U,1,BCM_PART_DATA,offsetof(T_BCMDataStruct,ucBduSleep),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,167,DATA_TYPE_INT8U,1,BCM_PART_DATA,offsetof(T_BCMDataStruct,ucBduStatus),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,168,DATA_TYPE_INT8U,1,BCM_PART_DATA,offsetof(T_BCMDataStruct,ucMasterSta),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,169,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,fSetChgVolt),1,DATA_TYPE_INT16U,2,2,NULL,},
	{0x04,0,170,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,fSetDischVolt),1,DATA_TYPE_INT16U,2,2,NULL,},
	{0x04,0,171,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_BCMDataStruct,wSetChgCurr),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,172,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_BCMDataStruct,wSetDischCurr),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,173,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,fBoardTemp),1,DATA_TYPE_INT16S,1,2,NULL,},
	{0x04,0,174,DATA_TYPE_INT8U,1,BCM_PART_DATA,offsetof(T_BCMDataStruct,ucAlarmStatus),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,175,DATA_TYPE_INT8U,1,BCM_PART_DATA,offsetof(T_BCMDataStruct,ucThroughStatus),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,178,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,fDischObjVolt),1,DATA_TYPE_INT16U,2,2,NULL,},
	{0x04,0,179,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellTemp[0]),16,DATA_TYPE_INT16U,1,2,NULL,},
	{0x04,0,180,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellTemp[1]),16,DATA_TYPE_INT16U,1,2,NULL,},
	{0x04,0,181,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellTemp[2]),16,DATA_TYPE_INT16U,1,2,NULL,},
	{0x04,0,182,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellTemp[3]),16,DATA_TYPE_INT16U,1,2,NULL,},
	{0x04,0,183,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellTemp[4]),16,DATA_TYPE_INT16U,1,2,NULL,},
	{0x04,0,184,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellTemp[5]),16,DATA_TYPE_INT16U,1,2,NULL,},
	{0x04,0,185,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellTemp[6]),16,DATA_TYPE_INT16U,1,2,NULL,},
	{0x04,0,186,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellTemp[7]),16,DATA_TYPE_INT16U,1,2,NULL,},
	{0x04,0,187,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellTemp[8]),16,DATA_TYPE_INT16U,1,2,NULL,},
	{0x04,0,188,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellTemp[9]),16,DATA_TYPE_INT16U,1,2,NULL,},
	{0x04,0,189,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellTemp[10]),16,DATA_TYPE_INT16U,1,2,NULL,},
	{0x04,0,190,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellTemp[11]),16,DATA_TYPE_INT16U,1,2,NULL,},
	{0x04,0,191,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellTemp[12]),16,DATA_TYPE_INT16U,1,2,NULL,},
	{0x04,0,192,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellTemp[13]),16,DATA_TYPE_INT16U,1,2,NULL,},
	{0x04,0,193,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellTemp[14]),16,DATA_TYPE_INT16U,1,2,NULL,},
	{0x04,0,194,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,afCellTemp[15]),16,DATA_TYPE_INT16U,1,2,NULL,},
	{0x04,0,195,DATA_TYPE_INT8U,1,BCM_PART_DATA,offsetof(T_BCMDataStruct,ucDefenceStatus),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,196,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,fConnTemp),1,DATA_TYPE_INT16U,1,2,NULL,},
	{0x04,0,1107,DATA_TYPE_BOOLEAN,1,BCM_PART_DATA,offsetof(T_BCMDataStruct,bBuzzerEnable),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,1108,DATA_TYPE_BOOLEAN,1,BCM_PART_DATA,offsetof(T_BCMDataStruct,bHeatpadEnable),1,DATA_TYPE_INT16U,0,2,0,},
	{0x04,0,1109,DATA_TYPE_BOOLEAN,1,BCM_PART_DATA,offsetof(T_BCMDataStruct,bBattFull),1,DATA_TYPE_INT16U,0,2,0,},
	{0x04,0,1110,DATA_TYPE_BOOLEAN,1,BCM_PART_DATA,offsetof(T_BCMDataStruct,bChgVolReg),1,DATA_TYPE_INT16U,0,2,0,},
	{0x04,0,1113,DATA_TYPE_INT8U,1,BCM_PART_DATA,offsetof(T_BCMDataStruct,ucInputBreak),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,1114,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_BCMDataStruct,wTotalDischgQHigh),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,1115,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_BCMDataStruct,wTotalDischgQLow),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,1116,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_BCMDataStruct,wChargeLeftMinutes),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,1117,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_BCMDataStruct,wDisChargeLeftMinutes),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,1118,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_BCMDataStruct,wRemainCap),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,1119,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_BCMDataStruct,wBatteryCap),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,1120,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_BCMDataStruct,wTotalDischgCapHigh),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,1121,DATA_TYPE_INT16U,2,BCM_PART_DATA,offsetof(T_BCMDataStruct,wTotalDischgCapLow),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,1122,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,fCellVoltMax),1,DATA_TYPE_INT16U,3,2,NULL,},
	{0x04,0,1123,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,fCellVoltMin),1,DATA_TYPE_INT16U,3,2,NULL,},
	{0x04,0,1124,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,fCellTempMax),1,DATA_TYPE_INT16S,1,2,NULL,},
	{0x04,0,1125,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,fCellTempMin),1,DATA_TYPE_INT16S,1,2,NULL,},
	{0x04,0,1126,DATA_TYPE_FP32,4,BCM_PART_DATA,offsetof(T_BCMDataStruct,fBoardTemp),1,DATA_TYPE_INT16S,1,2,NULL,},
	{0x04,0,1127,DATA_TYPE_INT8U,1,BCM_PART_DATA,offsetof(T_BCMDataStruct,ucLimit),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1128,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fChgCurrHighPrtThre),1,DATA_TYPE_INT16U,2,2,NULL,},
	{0x03,0x10,1129,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fDischgCurrHighPrtThre),1,DATA_TYPE_INT16S,2,2,NULL,},
	{0x03,0x10,1130,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fBattOverVoltPrtThre),1,DATA_TYPE_INT16U,2,2,NULL,},
	{0x03,0x10,1131,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fBoardTempHighPrtThre),1,DATA_TYPE_INT16U,1,2,NULL,},
	{0x03,0x10,1132,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fChgCurrHighAlmThre),1,DATA_TYPE_INT16U,2,2,NULL,},
	{0x03,0x10,1133,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fDischgCurrHighAlmThre),1,DATA_TYPE_INT16S,2,2,NULL,},
	{0x03,0x10,1134,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fBattOverVoltAlmThre),1,DATA_TYPE_INT16U,2,2,NULL,},
	{0x03,0x10,1135,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fBoardTempHighcThre),1,DATA_TYPE_INT16S,1,2,NULL,},
	{0x03,0x10,1136,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fEnvTempHighAlmThre),1,DATA_TYPE_INT16S,1,2,NULL,},
	{0x03,0x10,1137,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fEnvTempLowAlmThre),1,DATA_TYPE_INT16S,1,2,NULL,},
	{0x03,0x10,1138,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fBattUnderVoltAlmThre),1,DATA_TYPE_INT16U,2,2,NULL,},
	{0x03,0x10,1139,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fBattUnderVoltPrtThre),1,DATA_TYPE_INT16U,2,2,NULL,},
	{0x03,0x10,1140,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fCellOverVoltAlmThre),1,DATA_TYPE_INT16U,3,2,NULL,},
	{0x03,0x10,1141,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fCellOverVoltPrtThre),1,DATA_TYPE_INT16U,3,2,NULL,},
	{0x03,0x10,1142,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fCellUnderVoltAlmThre),1,DATA_TYPE_INT16U,3,2,NULL,},
	{0x03,0x10,1143,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fCellUnderVoltPrtThre),1,DATA_TYPE_INT16U,3,2,NULL,},
	{0x03,0x10,1144,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fChgTempHighAlmThre),1,DATA_TYPE_INT16S,1,2,NULL,},
	{0x03,0x10,1145,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fChgTempHighPrtThre),1,DATA_TYPE_INT16S,1,2,NULL,},
	{0x03,0x10,1146,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fDischgTempHighAlmThre),1,DATA_TYPE_INT16S,1,2,NULL,},
	{0x03,0x10,1147,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fDischgTempHighPrtThre),1,DATA_TYPE_INT16S,1,2,NULL,},
	{0x03,0x10,1148,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fChgTempLowAlmThre),1,DATA_TYPE_INT16S,1,2,NULL,},
	{0x03,0x10,1149,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fChgTempLowPrtThre),1,DATA_TYPE_INT16S,1,2,NULL,},
	{0x03,0x10,1150,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fDischgTempLowAlmThre),1,DATA_TYPE_INT16S,1,2,NULL,},
	{0x03,0x10,1151,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fDischgTempLowPrtThre),1,DATA_TYPE_INT16S,1,2,NULL,},
	{0x03,0x10,1152,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fCellPoorConsisAlmThre),1,DATA_TYPE_INT16U,2,2,NULL,},
	{0x03,0x10,1153,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fCellPoorConsisPrtThre),1,DATA_TYPE_INT16U,2,2,NULL,},
	{0x03,0x10,1154,DATA_TYPE_INT16U,2,SYS_PARA,offsetof(T_SysPara,wBattSOCLowAlmThre),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1155,DATA_TYPE_INT16U,2,SYS_PARA,offsetof(T_SysPara,wBattSOCLowPrtThre),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1156,DATA_TYPE_INT16U,2,SYS_PARA,offsetof(T_SysPara,wBattSOHAlmThre),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1157,DATA_TYPE_INT16U,2,SYS_PARA,offsetof(T_SysPara,wBattSOHPrtThre),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1158,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fCellDamagePrtThre),1,DATA_TYPE_INT16U,2,2,NULL,},
	{0x03,0x10,1159,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fBattChgFullAverVoltThre),1,DATA_TYPE_INT16U,2,2,NULL,},
	{0x03,0x10,1160,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fBattChgFullAverCurrThre),1,DATA_TYPE_INT16U,2,2,NULL,},
	{0x03,0x10,1161,DATA_TYPE_INT16U,2,SYS_PARA,offsetof(T_SysPara,wChgMaxDura),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1162,DATA_TYPE_INT16U,2,SYS_PARA,offsetof(T_SysPara,wChgEndDura),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1163,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fBattSupplVolt),1,DATA_TYPE_INT16U,2,2,NULL,},
	{0x03,0x10,1165,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fCellEquVoltDiffThre),1,DATA_TYPE_INT16U,2,2,NULL,},
	{0x03,0x10,1166,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fChargeMaxCurr),1,DATA_TYPE_INT16U,2,2,NULL,},
	{0x03,0x10,1168,DATA_TYPE_BOOLEAN,1,SYS_PARA,offsetof(T_SysPara,bBuzzerEnable),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1169,DATA_TYPE_INT16U,2,SYS_PARA,offsetof(T_SysPara,wHisDataInter),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1174,DATA_TYPE_INT16U,2,SYS_PARA,offsetof(T_SysPara,wDischgSwitchSOC),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1176,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,ucUsageScen),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1179,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fRemoteSupplyOutVolt),1,DATA_TYPE_INT16U,2,2,NULL,},
	{0x03,0x10,1181,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,ucDODPerDischarge),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1184,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fDischgMaxCurr),1,DATA_TYPE_INT16U,2,2,NULL,},
	{0x03,0x10,1187,DATA_TYPE_BOOLEAN,1,SYS_PARA,offsetof(T_SysPara,ucCellUVPDelay),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1188,DATA_TYPE_INT16U,2,SYS_PARA,offsetof(T_SysPara,wSoftAntiTheftDelay),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1380,DATA_TYPE_CHAR,1,SYS_PARA,offsetof(T_SysPara,acDeviceName[0]),32,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1381,DATA_TYPE_CHAR,1,SYS_PARA,offsetof(T_SysPara,acDeviceName[1]),32,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1382,DATA_TYPE_CHAR,1,SYS_PARA,offsetof(T_SysPara,acDeviceName[2]),32,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1383,DATA_TYPE_CHAR,1,SYS_PARA,offsetof(T_SysPara,acDeviceName[3]),32,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1384,DATA_TYPE_CHAR,1,SYS_PARA,offsetof(T_SysPara,acDeviceName[4]),32,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1385,DATA_TYPE_CHAR,1,SYS_PARA,offsetof(T_SysPara,acDeviceName[5]),32,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1386,DATA_TYPE_CHAR,1,SYS_PARA,offsetof(T_SysPara,acDeviceName[6]),32,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1387,DATA_TYPE_CHAR,1,SYS_PARA,offsetof(T_SysPara,acDeviceName[7]),32,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1388,DATA_TYPE_CHAR,1,SYS_PARA,offsetof(T_SysPara,acDeviceName[8]),32,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1389,DATA_TYPE_CHAR,1,SYS_PARA,offsetof(T_SysPara,acDeviceName[9]),32,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1390,DATA_TYPE_CHAR,1,SYS_PARA,offsetof(T_SysPara,acDeviceName[10]),32,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1391,DATA_TYPE_CHAR,1,SYS_PARA,offsetof(T_SysPara,acDeviceName[11]),32,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1392,DATA_TYPE_CHAR,1,SYS_PARA,offsetof(T_SysPara,acDeviceName[12]),32,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1393,DATA_TYPE_CHAR,1,SYS_PARA,offsetof(T_SysPara,acDeviceName[13]),32,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1394,DATA_TYPE_CHAR,1,SYS_PARA,offsetof(T_SysPara,acDeviceName[14]),32,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1395,DATA_TYPE_CHAR,1,SYS_PARA,offsetof(T_SysPara,acDeviceName[15]),32,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1396,DATA_TYPE_CHAR,1,SYS_PARA,offsetof(T_SysPara,acDeviceName[16]),32,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1397,DATA_TYPE_CHAR,1,SYS_PARA,offsetof(T_SysPara,acDeviceName[17]),32,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1398,DATA_TYPE_CHAR,1,SYS_PARA,offsetof(T_SysPara,acDeviceName[18]),32,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1399,DATA_TYPE_CHAR,1,SYS_PARA,offsetof(T_SysPara,acDeviceName[19]),32,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1400,DATA_TYPE_CHAR,1,SYS_PARA,offsetof(T_SysPara,acDeviceName[20]),32,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1401,DATA_TYPE_CHAR,1,SYS_PARA,offsetof(T_SysPara,acDeviceName[21]),32,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1402,DATA_TYPE_CHAR,1,SYS_PARA,offsetof(T_SysPara,acDeviceName[22]),32,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1403,DATA_TYPE_CHAR,1,SYS_PARA,offsetof(T_SysPara,acDeviceName[23]),32,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1404,DATA_TYPE_CHAR,1,SYS_PARA,offsetof(T_SysPara,acDeviceName[24]),32,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1405,DATA_TYPE_CHAR,1,SYS_PARA,offsetof(T_SysPara,acDeviceName[25]),32,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1406,DATA_TYPE_CHAR,1,SYS_PARA,offsetof(T_SysPara,acDeviceName[26]),32,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1407,DATA_TYPE_CHAR,1,SYS_PARA,offsetof(T_SysPara,acDeviceName[27]),32,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1408,DATA_TYPE_CHAR,1,SYS_PARA,offsetof(T_SysPara,acDeviceName[28]),32,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1409,DATA_TYPE_CHAR,1,SYS_PARA,offsetof(T_SysPara,acDeviceName[29]),32,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1410,DATA_TYPE_CHAR,1,SYS_PARA,offsetof(T_SysPara,acDeviceName[30]),32,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1411,DATA_TYPE_CHAR,1,SYS_PARA,offsetof(T_SysPara,acDeviceName[31]),32,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1413,DATA_TYPE_T_DATESTRUCT,1,SYS_PARA,offsetof(T_SysPara,tEnableTime),1,DATA_TYPE_INT16U,0,6,NULL,},
	{0x03,0x10,1419,DATA_TYPE_BOOLEAN,1,SYS_PARA,offsetof(T_SysPara,bUVPTempCompensationEn),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1440,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,ucRelayDefaultStatus),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1442,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,ucSleepIndicator),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1445,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fEnvTempHighPrtThre),1,DATA_TYPE_INT16S,0,2,NULL,},
	{0x03,0x10,1446,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fEnvTempLowPrtThre),1,DATA_TYPE_INT16S,0,2,NULL,},
	{0x03,0x10,1447,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fBattOverVoltPrtRecoThre),1,DATA_TYPE_INT16U,2,2,NULL,},
	{0x03,0x10,1448,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fBattOverVoltAlmRecoThre),1,DATA_TYPE_INT16U,2,2,NULL,},
	{0x03,0x10,1449,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fCellOverVoltPrtRecoThre),1,DATA_TYPE_INT16U,3,2,NULL,},
	{0x03,0x10,1450,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fCellOverVoltAlmRecoThre),1,DATA_TYPE_INT16U,3,2,NULL,},
	{0x03,0x10,1451,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fCellUnderVoltPrtRecoThre),1,DATA_TYPE_INT16U,3,2,NULL,},
	{0x03,0x10,1452,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fCellUnderVoltAlmRecoThre),1,DATA_TYPE_INT16U,3,2,NULL,},
	{0x03,0x10,1453,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fChgTempHighPrtRecoThre),1,DATA_TYPE_INT16S,1,2,NULL,},
	{0x03,0x10,1454,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fChgTempHighAlmRecoThre),1,DATA_TYPE_INT16S,1,2,NULL,},
	{0x03,0x10,1455,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fDischgTempHighPrtRecoThre),1,DATA_TYPE_INT16S,1,2,NULL,},
	{0x03,0x10,1456,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fDischgTempHighAlmRecoThre),1,DATA_TYPE_INT16S,1,2,NULL,},
	{0x03,0x10,1457,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fChgTempLowPrtRecoThre),1,DATA_TYPE_INT16S,1,2,NULL,},
	{0x03,0x10,1458,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fChgTempLowAlmRecoThre),1,DATA_TYPE_INT16S,1,2,NULL,},
	{0x03,0x10,1459,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fDischgTempLowPrtRecoThre),1,DATA_TYPE_INT16S,1,2,NULL,},
	{0x03,0x10,1460,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fDischgTempLowAlmRecoThre),1,DATA_TYPE_INT16S,1,2,NULL,},
	{0x03,0x10,1461,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fEnvTempHighPrtRecoThre),1,DATA_TYPE_INT16S,0,2,NULL,},
	{0x03,0x10,1462,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fEnvTempHighAlmRecoThre),1,DATA_TYPE_INT16S,0,2,NULL,},
	{0x03,0x10,1463,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fEnvTempLowPrtRecoThre),1,DATA_TYPE_INT16S,0,2,NULL,},
	{0x03,0x10,1464,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fEnvTempLowAlmRecoThre),1,DATA_TYPE_INT16S,0,2,NULL,},
	{0x03,0x10,1465,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fBoardTempHighPrtRecoThre),1,DATA_TYPE_INT16S,1,2,NULL,},
	{0x03,0x10,1466,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fBoardTempHighAlmRecoThre),1,DATA_TYPE_INT16S,1,2,NULL,},
	{0x03,0x10,1471,DATA_TYPE_BOOLEAN,1,SYS_PARA,offsetof(T_SysPara,ucGyroAntiTheftMode),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1472,DATA_TYPE_BOOLEAN,1,SYS_PARA,offsetof(T_SysPara,ucBattUnlockMode),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1475,DATA_TYPE_FP32,4,SYS_PARA,offsetof(T_SysPara,fCellChargeFullVolt),1,DATA_TYPE_INT16U,2,2,NULL,},
	{0x03,0x10,1476,DATA_TYPE_INT16U,2,SYS_PARA,offsetof(T_SysPara,wBattSOCLowAlmRecoThre),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1480,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[5]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1481,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[7]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1482,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[3]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1483,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[18]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1484,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[4]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1485,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[6]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1486,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[1]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1487,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[36]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1488,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[37]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1489,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[0]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1490,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[2]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1491,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[20]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1492,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[22]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1493,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[19]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1494,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[21]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1495,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[27]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1496,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[29]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1497,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[31]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1498,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[33]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1499,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[28]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1500,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[30]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1501,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[32]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1502,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[34]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1503,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[23]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1504,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[24]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1505,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[13]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1506,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[14]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1507,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[15]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1508,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[16]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1509,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[26]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1510,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[49]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1511,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[42]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1512,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[25]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1513,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[11]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1514,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[10]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1515,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[12]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1516,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[8]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1517,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[9]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1519,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[50]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1520,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[41]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1521,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[60]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1522,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[61]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1524,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[43]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1525,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[44]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1526,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[45]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1528,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[51]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1529,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[46]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1530,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[47]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1532,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[38]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1533,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[39]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1534,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[17]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1535,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[48]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1536,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[40]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1538,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[62]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1539,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[56]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1540,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[57]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1541,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[58]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1542,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[59]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1543,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[35]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1544,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[52]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1545,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[53]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1546,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[54]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1547,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[55]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1549,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucRelayBit[63]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1560,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[5]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1561,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[7]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1562,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[3]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1563,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[18]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1564,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[4]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1565,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[6]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1566,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[1]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1567,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[36]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1568,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[37]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1569,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[0]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1570,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[2]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1571,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[20]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1572,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[22]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1573,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[19]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1574,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[21]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1575,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[27]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1576,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[29]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1577,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[31]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1578,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[33]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1579,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[28]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1580,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[30]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1581,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[32]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1582,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[34]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1583,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[23]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1584,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[24]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1585,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[13]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1586,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[14]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1587,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[15]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1588,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[16]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1589,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[26]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1590,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[49]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1591,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[42]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1592,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[25]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1593,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[11]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1594,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[10]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1595,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[12]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1596,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[8]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1597,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[9]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1599,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[50]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1600,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[46]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1601,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[60]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1602,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[61]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1604,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[43]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1605,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[44]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1606,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[45]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1608,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[51]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1609,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[46]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1610,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[47]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1611,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[38]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1612,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[39]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1613,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[17]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1614,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[48]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1615,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[40]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1617,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[62]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1618,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[56]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1619,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[57]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1620,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[58]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1621,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[59]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1622,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[35]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1623,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[52]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1624,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[53]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1625,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[54]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1626,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[55]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,1628,DATA_TYPE_INT8U,1,SYS_PARA,offsetof(T_SysPara,aucAlarmLevel[63]),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1640,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucChgCurrHighPrt),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1641,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucDischgCurrHighPrt),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1642,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucBattOverVoltPrt),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1643,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucBoardTempHighPrt),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1644,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucChgCurrHighAlm),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1645,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucDischgCurrHighAlm),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1646,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucBattOverVoltAlm),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1647,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucEnvTempHighAlm),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1648,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucEnvTempLowAlm),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1649,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucBattUnderVoltAlm),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1650,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucBattUnderVoltPrt),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1651,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellOverVoltAlm[0]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1652,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellOverVoltAlm[1]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1653,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellOverVoltAlm[2]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1654,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellOverVoltAlm[3]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1655,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellOverVoltAlm[4]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1656,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellOverVoltAlm[5]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1657,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellOverVoltAlm[6]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1658,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellOverVoltAlm[7]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1659,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellOverVoltAlm[8]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1660,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellOverVoltAlm[9]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1661,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellOverVoltAlm[10]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1662,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellOverVoltAlm[11]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1663,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellOverVoltAlm[12]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1664,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellOverVoltAlm[13]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1665,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellOverVoltAlm[14]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1666,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellOverVoltPrt[0]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1667,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellOverVoltPrt[1]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1668,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellOverVoltPrt[2]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1669,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellOverVoltPrt[3]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1670,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellOverVoltPrt[4]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1671,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellOverVoltPrt[5]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1672,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellOverVoltPrt[6]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1673,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellOverVoltPrt[7]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1674,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellOverVoltPrt[8]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1675,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellOverVoltPrt[9]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1676,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellOverVoltPrt[10]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1677,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellOverVoltPrt[11]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1678,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellOverVoltPrt[12]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1679,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellOverVoltPrt[13]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1680,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellOverVoltPrt[14]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1681,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellUnderVoltAlm[0]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1682,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellUnderVoltAlm[1]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1683,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellUnderVoltAlm[2]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1684,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellUnderVoltAlm[3]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1685,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellUnderVoltAlm[4]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1686,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellUnderVoltAlm[5]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1687,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellUnderVoltAlm[6]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1688,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellUnderVoltAlm[7]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1689,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellUnderVoltAlm[8]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1690,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellUnderVoltAlm[9]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1691,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellUnderVoltAlm[10]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1692,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellUnderVoltAlm[11]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1693,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellUnderVoltAlm[12]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1694,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellUnderVoltAlm[13]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1695,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellUnderVoltAlm[14]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1696,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellUnderVoltPrt[0]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1697,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellUnderVoltPrt[1]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1698,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellUnderVoltPrt[2]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1699,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellUnderVoltPrt[3]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1700,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellUnderVoltPrt[4]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1701,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellUnderVoltPrt[5]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1702,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellUnderVoltPrt[6]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1703,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellUnderVoltPrt[7]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1704,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellUnderVoltPrt[8]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1705,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellUnderVoltPrt[9]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1706,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellUnderVoltPrt[10]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1707,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellUnderVoltPrt[11]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1708,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellUnderVoltPrt[12]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1709,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellUnderVoltPrt[13]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1710,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellUnderVoltPrt[14]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1711,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucChgTempHighAlm[0]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1712,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucChgTempHighAlm[1]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1713,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucChgTempHighAlm[2]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1714,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucChgTempHighAlm[3]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1715,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucChgTempHighPrt[0]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1716,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucChgTempHighPrt[1]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1717,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucChgTempHighPrt[2]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1718,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucChgTempHighPrt[3]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1719,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucDischgTempHighAlm[0]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1720,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucDischgTempHighAlm[1]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1721,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucDischgTempHighAlm[2]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1722,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucDischgTempHighAlm[3]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1723,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucDischgTempHighPrt[0]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1724,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucDischgTempHighPrt[1]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1725,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucDischgTempHighPrt[2]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1726,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucDischgTempHighPrt[3]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1727,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucChgTempLowAlm[0]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1728,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucChgTempLowAlm[1]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1729,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucChgTempLowAlm[2]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1730,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucChgTempLowAlm[3]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1731,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucChgTempLowPrt[0]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1732,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucChgTempLowPrt[1]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1733,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucChgTempLowPrt[2]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1734,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucChgTempLowPrt[3]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1735,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucDischgTempLowAlm[0]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1736,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucDischgTempLowAlm[1]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1737,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucDischgTempLowAlm[2]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1738,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucDischgTempLowAlm[3]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1739,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucDischgTempLowPrt[0]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1740,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucDischgTempLowPrt[1]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1741,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucDischgTempLowPrt[2]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1742,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucDischgTempLowPrt[3]),4,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1743,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucCellPoorConsisAlm),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1744,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucCellPoorConsisPrt),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1745,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucBattSOCLowAlm),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1746,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucBattSOCLowPrt),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1747,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucBattSOHAlm),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1748,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucBattSOHPrt),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1749,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellDamagePrt[0]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1750,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellDamagePrt[1]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1751,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellDamagePrt[2]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1752,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellDamagePrt[3]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1753,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellDamagePrt[4]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1754,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellDamagePrt[5]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1755,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellDamagePrt[6]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1756,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellDamagePrt[7]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1757,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellDamagePrt[8]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1758,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellDamagePrt[9]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1759,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellDamagePrt[10]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1760,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellDamagePrt[11]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1761,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellDamagePrt[12]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1762,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellDamagePrt[13]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1763,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellDamagePrt[14]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1764,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucBattLoseAlm),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1765,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucChgLoopInvalid),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1766,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucDischgLoopInvalid),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1767,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucCurrLimLoopInvalid),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1768,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucBattShortCut),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1769,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucBattReverse),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1774,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucInsideTempHighPrt),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1775,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucBDUBattVoltLowPrt),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1776,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucBDUBusVoltLowPrt),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1777,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucBDUBusVoltHighPrt),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1778,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucBduEepromAlm),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1779,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucCellTempAbnormal),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1780,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucAddressClash),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1782,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucCellVoltSampleFault),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1783,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucBDUCommFail),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1785,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucLoopFault),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1786,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucBDUBattChgVoltLowPrt),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1787,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucBDUBattLockAlm),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1788,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucEnvTempHighPrt),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1789,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucEnvTempLowPrt),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1790,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucBoardTempHighAlm),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1791,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucEqualCircuitFaultAlm),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1792,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucEnvTempSensorInvalidAlm),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1921,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucCellTempSensorInvalidAlm[0]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1922,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucCellTempSensorInvalidAlm[1]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1923,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucCellTempSensorInvalidAlm[2]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1924,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucCellTempSensorInvalidAlm[3]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1925,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucCellTempSensorInvalidAlm[4]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1926,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucCellTempSensorInvalidAlm[5]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1927,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucCellTempSensorInvalidAlm[6]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1928,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucCellTempSensorInvalidAlm[7]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1929,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucCellTempSensorInvalidAlm[8]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1930,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucCellTempSensorInvalidAlm[9]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1931,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucCellTempSensorInvalidAlm[10]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1932,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucCellTempSensorInvalidAlm[11]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1933,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucCellTempSensorInvalidAlm[12]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1934,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucCellTempSensorInvalidAlm[13]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1935,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucCellTempSensorInvalidAlm[14]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1938,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellDynamicUnderVoltPrt[0]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1939,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellDynamicUnderVoltPrt[1]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1940,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellDynamicUnderVoltPrt[2]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1941,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellDynamicUnderVoltPrt[3]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1942,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellDynamicUnderVoltPrt[4]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1943,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellDynamicUnderVoltPrt[5]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1944,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellDynamicUnderVoltPrt[6]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1945,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellDynamicUnderVoltPrt[7]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1946,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellDynamicUnderVoltPrt[8]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1947,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellDynamicUnderVoltPrt[9]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1948,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellDynamicUnderVoltPrt[10]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1949,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellDynamicUnderVoltPrt[11]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1950,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellDynamicUnderVoltPrt[12]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1951,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellDynamicUnderVoltPrt[13]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1952,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,aucCellDynamicUnderVoltPrt[14]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1953,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucMainRelayFail),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1954,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucDCDCErr),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1955,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucSampleErr),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1956,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucAuxiSourceErr),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1957,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucPowerOffTimePrtAlm),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1958,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucHeaterFilmFailure),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1959,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucFuseError),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1960,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucBDUConnTempHighPrt),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0x02,0,1962,DATA_TYPE_BIT,1,BCM_PART_ALARM,offsetof(T_BCMAlarmStruct,ucHeatConnTempHighPrt),1,DATA_TYPE_INT16U,0,2,NULL,},
	{0,0x05,2152,DATA_TYPE_CTRL_DATA,1,SPECIAL_DATA_PART,offsetof(T_SpecialDataStruct,ucBmsReset),1,DATA_TYPE_INT16U,0,2,DealReset,},
	{0,0x05,2156,DATA_TYPE_CTRL_DATA,1,SPECIAL_DATA_PART,offsetof(T_SpecialDataStruct,ucLoadDefPara),1,DATA_TYPE_INT16U,0,2,DealDefPara,},
	{0,0x05,2158,DATA_TYPE_CTRL_DATA,1,SPECIAL_DATA_PART,offsetof(T_SpecialDataStruct,ucStartAddrCompete),1,DATA_TYPE_INT16U,0,2,DealAddrCompete,},
	{0,0x06,2159,DATA_TYPE_FP32,4,SPECIAL_DATA_PART,offsetof(T_SpecialDataStruct,fCsuSendCurr),1,DATA_TYPE_INT16S,2,2,DealCSUSendCurr,},
	{0,0x05,2160,DATA_TYPE_CTRL_DATA,1,SPECIAL_DATA_PART,offsetof(T_SpecialDataStruct,ucReleaseLock),1,DATA_TYPE_INT16U,0,2,DealRelaseLock,},
	{0,0x05,2161,DATA_TYPE_CTRL_DATA,1,SPECIAL_DATA_PART,offsetof(T_SpecialDataStruct,ucSendPowerOn),1,DATA_TYPE_INT16U,0,2,DealSendPowerOn,},
	{0,0x05,2162,DATA_TYPE_CTRL_DATA,1,SPECIAL_DATA_PART,offsetof(T_SpecialDataStruct,ucSendPowerOff),1,DATA_TYPE_INT16U,0,2,DealSendPowerOff,},
	{0,0x05,2163,DATA_TYPE_INT16U,1,SPECIAL_DATA_PART,offsetof(T_SpecialDataStruct,wManualUnlock),1,DATA_TYPE_INT16U,0,2,DealManualUnlock,},
	{0x04,0,2664,DATA_TYPE_T_DATESTRUCT,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,tSoftDate),1,DATA_TYPE_INT16U,0,6,NULL,},
	{0x04,0,2667,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acName[0]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2668,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acName[1]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2669,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acName[2]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2670,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acName[3]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2671,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acName[4]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2672,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acName[5]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2673,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acName[6]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2674,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acName[7]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2675,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acName[8]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2676,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acName[9]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2677,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acName[10]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2678,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acName[11]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2679,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acName[12]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2680,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acName[13]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2681,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acName[14]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2682,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acName[15]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2683,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acName[16]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2684,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acName[17]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2685,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acName[18]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2686,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acName[19]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2687,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acName[20]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2688,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acName[21]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2689,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acName[22]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2690,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acName[23]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2691,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acName[24]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2692,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acName[25]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2693,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acName[26]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2694,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acName[27]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2695,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acName[28]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2696,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acName[29]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2697,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acDigControlVer[0]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2698,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acDigControlVer[1]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2699,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acDigControlVer[2]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2700,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acDigControlVer[3]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2701,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acDigControlVer[4]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2702,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acDigControlVer[5]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2703,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acDigControlVer[6]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2704,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acDigControlVer[7]),8,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2705,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acSoftVer[0]),6,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2706,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acSoftVer[1]),6,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2707,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acSoftVer[2]),6,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2708,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acSoftVer[3]),6,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2709,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acSoftVer[4]),6,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2710,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acSoftVer[5]),6,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2711,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acBduAssetMagInfo[0]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2712,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acBduAssetMagInfo[1]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2713,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acBduAssetMagInfo[2]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2714,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acBduAssetMagInfo[3]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2715,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acBduAssetMagInfo[4]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2716,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acBduAssetMagInfo[5]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2717,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acBduAssetMagInfo[6]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2718,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acBduAssetMagInfo[7]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2719,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acBduAssetMagInfo[8]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2720,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acBduAssetMagInfo[9]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2721,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acBduAssetMagInfo[10]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2722,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acBduAssetMagInfo[11]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2723,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acBduAssetMagInfo[12]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2724,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acBduAssetMagInfo[13]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2725,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acBduAssetMagInfo[14]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2726,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acBduAssetMagInfo[15]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2727,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acBduAssetMagInfo[16]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2728,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acBduAssetMagInfo[17]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2729,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acBduAssetMagInfo[18]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2730,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acBduAssetMagInfo[19]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2731,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acBduAssetMagInfo[20]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2732,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acBduAssetMagInfo[21]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2733,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acBduAssetMagInfo[22]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2734,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acBduAssetMagInfo[23]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2735,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acBduAssetMagInfo[24]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2736,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acBduAssetMagInfo[25]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2737,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acBduAssetMagInfo[26]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2738,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acBduAssetMagInfo[27]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2739,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acBduAssetMagInfo[28]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2740,DATA_TYPE_CHAR,1,BDU_FAC_PART,offsetof(T_BduFactoryInfo,acBduAssetMagInfo[29]),30,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2880,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acCorpName[0]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2881,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acCorpName[1]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2882,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acCorpName[2]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2883,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acCorpName[3]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2884,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acCorpName[4]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2885,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acCorpName[5]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2886,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acCorpName[6]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2887,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acCorpName[7]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2888,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acCorpName[8]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2889,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acCorpName[9]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2890,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acCorpName[10]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2891,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acCorpName[11]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2892,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acCorpName[12]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2893,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acCorpName[13]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2894,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acCorpName[14]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2895,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acCorpName[15]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2896,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acCorpName[16]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2897,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acCorpName[17]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2898,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acCorpName[18]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2899,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acCorpName[19]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2900,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acBmsTypeName[0]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2901,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acBmsTypeName[1]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2902,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acBmsTypeName[2]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2903,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acBmsTypeName[3]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2904,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acBmsTypeName[4]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2905,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acBmsTypeName[5]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2906,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acBmsTypeName[6]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2907,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acBmsTypeName[7]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2908,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acBmsTypeName[8]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2909,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acBmsTypeName[9]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2910,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acBmsTypeName[10]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2911,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acBmsTypeName[11]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2912,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acBmsTypeName[12]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2913,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acBmsTypeName[13]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2914,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acBmsTypeName[14]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2915,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acBmsTypeName[15]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2916,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acBmsTypeName[16]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2917,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acBmsTypeName[17]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2918,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acBmsTypeName[18]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2919,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acBmsTypeName[19]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2920,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acSysName[0]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2921,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acSysName[1]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2922,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acSysName[2]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2923,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acSysName[3]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2924,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acSysName[4]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2925,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acSysName[5]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2926,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acSysName[6]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2927,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acSysName[7]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2928,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acSysName[8]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2929,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acSysName[9]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2930,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acSysName[10]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2931,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acSysName[11]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2932,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acSysName[12]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2933,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acSysName[13]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2934,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acSysName[14]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2935,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acSysName[15]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2936,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acSysName[16]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2937,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acSysName[17]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2938,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acSysName[18]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2939,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acSysName[19]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2940,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acVersion[0]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2941,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acVersion[1]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2942,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acVersion[2]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2943,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acVersion[3]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2944,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acVersion[4]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2945,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acVersion[5]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2946,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acVersion[6]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2947,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acVersion[7]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2948,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acVersion[8]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2949,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acVersion[9]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2950,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acVersion[10]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2951,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acVersion[11]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2952,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acVersion[12]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2953,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acVersion[13]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2954,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acVersion[14]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2955,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acVersion[15]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2956,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acVersion[16]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2957,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acVersion[17]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2958,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acVersion[18]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2959,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acVersion[19]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,2960,DATA_TYPE_T_DATESTRUCT,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,tSoftDate),1,DATA_TYPE_INT16U,0,6,NULL,},
	{0x03,0x10,2963,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acBmsFacSn[0]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2964,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acBmsFacSn[1]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2965,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acBmsFacSn[2]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2966,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acBmsFacSn[3]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2967,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acBmsFacSn[4]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2968,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acBmsFacSn[5]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2969,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acBmsFacSn[6]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2970,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acBmsFacSn[7]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2971,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acBmsFacSn[8]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2972,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acBmsFacSn[9]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2973,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acBmsFacSn[10]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2974,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acBmsFacSn[11]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2975,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acBmsFacSn[12]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2976,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acBmsFacSn[13]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2977,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acBmsFacSn[14]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2978,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[0][0]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2979,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[0][1]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2980,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[0][2]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2981,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[0][3]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2982,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[0][4]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2983,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[0][5]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2984,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[0][6]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2985,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[0][7]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2986,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[0][8]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2987,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[0][9]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2988,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[0][10]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2989,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[0][11]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2990,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[0][12]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2991,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[0][13]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2992,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[0][14]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2993,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[0][15]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2994,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[0][16]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2995,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[0][17]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2996,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[0][18]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2997,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[0][19]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2998,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[1][0]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,2999,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[1][1]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3000,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[1][2]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3001,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[1][3]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3002,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[1][4]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3003,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[1][5]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3004,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[1][6]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3005,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[1][7]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3006,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[1][8]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3007,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[1][9]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3008,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[1][10]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3009,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[1][11]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3010,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[1][12]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3011,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[1][13]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3012,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[1][14]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3013,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[1][15]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3014,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[1][16]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3015,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[1][17]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3016,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[1][18]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3017,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[1][19]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3018,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[2][0]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3019,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[2][1]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3020,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[2][2]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3021,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[2][3]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3022,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[2][4]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3023,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[2][5]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3024,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[2][6]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3025,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[2][7]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3026,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[2][8]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3027,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[2][9]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3028,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[2][10]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3029,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[2][11]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3030,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[2][12]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3031,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[2][13]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3032,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[2][14]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3033,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[2][15]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3034,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[2][16]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3035,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[2][17]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3036,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[2][18]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3037,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[2][19]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3038,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[3][0]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3039,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[3][1]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3040,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[3][2]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3041,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[3][3]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3042,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[3][4]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3043,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[3][5]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3044,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[3][6]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3045,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[3][7]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3046,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[3][8]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3047,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[3][9]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3048,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[3][10]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3049,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[3][11]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3050,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[3][12]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3051,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[3][13]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3052,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[3][14]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3053,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[3][15]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3054,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[3][16]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3055,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[3][17]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3056,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[3][18]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3057,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acPackBarCode[3][19]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3058,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acDeviceSn[0]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3059,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acDeviceSn[1]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3060,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acDeviceSn[2]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3061,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acDeviceSn[3]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3062,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acDeviceSn[4]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3063,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acDeviceSn[5]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3064,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acDeviceSn[6]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3065,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acDeviceSn[7]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3066,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acDeviceSn[8]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3067,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acDeviceSn[9]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3068,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acDeviceSn[10]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3069,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acDeviceSn[11]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3070,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acDeviceSn[12]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3071,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acDeviceSn[13]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3072,DATA_TYPE_CHAR,1,BMS_PACK_FAC_PART,offsetof(T_BmsPACKFactoryStruct,acDeviceSn[14]),15,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,3073,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acBootDate[0]),10,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,3074,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acBootDate[1]),10,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,3075,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acBootDate[2]),10,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,3076,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acBootDate[3]),10,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,3077,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acBootDate[4]),10,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,3078,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acBootDate[5]),10,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,3079,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acBootDate[6]),10,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,3080,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acBootDate[7]),10,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,3081,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acBootDate[8]),10,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,3082,DATA_TYPE_CHAR,1,BCM_FAC_PART,offsetof(T_BCMFactoryStruct,acBootDate[9]),10,DATA_TYPE_INT16U,0,2,NULL,},
	{0x03,0x10,3176,DATA_TYPE_T_TIMESTRUCT,7,SPECIAL_DATA_PART,offsetof(T_SpecialDataStruct,tTime),1,DATA_TYPE_INT16U,0,12,DealTimeSet,},
	{0x04,0,3432,DATA_TYPE_CHAR,1,PACK_MANUFACT_PART,offsetof(T_BmsPACKManufactStruct,acCellManufactruer[0]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,3433,DATA_TYPE_CHAR,1,PACK_MANUFACT_PART,offsetof(T_BmsPACKManufactStruct,acCellManufactruer[1]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,3434,DATA_TYPE_CHAR,1,PACK_MANUFACT_PART,offsetof(T_BmsPACKManufactStruct,acCellManufactruer[2]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,3435,DATA_TYPE_CHAR,1,PACK_MANUFACT_PART,offsetof(T_BmsPACKManufactStruct,acCellManufactruer[3]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,3436,DATA_TYPE_CHAR,1,PACK_MANUFACT_PART,offsetof(T_BmsPACKManufactStruct,acCellManufactruer[4]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,3437,DATA_TYPE_CHAR,1,PACK_MANUFACT_PART,offsetof(T_BmsPACKManufactStruct,acCellManufactruer[5]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,3438,DATA_TYPE_CHAR,1,PACK_MANUFACT_PART,offsetof(T_BmsPACKManufactStruct,acCellManufactruer[6]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,3439,DATA_TYPE_CHAR,1,PACK_MANUFACT_PART,offsetof(T_BmsPACKManufactStruct,acCellManufactruer[7]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,3440,DATA_TYPE_CHAR,1,PACK_MANUFACT_PART,offsetof(T_BmsPACKManufactStruct,acCellManufactruer[8]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,3441,DATA_TYPE_CHAR,1,PACK_MANUFACT_PART,offsetof(T_BmsPACKManufactStruct,acCellManufactruer[9]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,3442,DATA_TYPE_CHAR,1,PACK_MANUFACT_PART,offsetof(T_BmsPACKManufactStruct,acCellManufactruer[10]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,3443,DATA_TYPE_CHAR,1,PACK_MANUFACT_PART,offsetof(T_BmsPACKManufactStruct,acCellManufactruer[11]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,3444,DATA_TYPE_CHAR,1,PACK_MANUFACT_PART,offsetof(T_BmsPACKManufactStruct,acCellManufactruer[12]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,3445,DATA_TYPE_CHAR,1,PACK_MANUFACT_PART,offsetof(T_BmsPACKManufactStruct,acCellManufactruer[13]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,3446,DATA_TYPE_CHAR,1,PACK_MANUFACT_PART,offsetof(T_BmsPACKManufactStruct,acCellManufactruer[14]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,3447,DATA_TYPE_CHAR,1,PACK_MANUFACT_PART,offsetof(T_BmsPACKManufactStruct,acCellManufactruer[15]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,3448,DATA_TYPE_CHAR,1,PACK_MANUFACT_PART,offsetof(T_BmsPACKManufactStruct,acCellManufactruer[16]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,3449,DATA_TYPE_CHAR,1,PACK_MANUFACT_PART,offsetof(T_BmsPACKManufactStruct,acCellManufactruer[17]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,3450,DATA_TYPE_CHAR,1,PACK_MANUFACT_PART,offsetof(T_BmsPACKManufactStruct,acCellManufactruer[18]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,3451,DATA_TYPE_CHAR,1,PACK_MANUFACT_PART,offsetof(T_BmsPACKManufactStruct,acCellManufactruer[19]),20,DATA_TYPE_INT16U,0,2,NULL,},
	{0x04,0,3452,DATA_TYPE_T_DATESTRUCT,1,PACK_MANUFACT_PART,offsetof(T_BmsPACKManufactStruct,tBattDate),1,DATA_TYPE_INT16U,0,6,NULL,},
	{0x04,0,3455,DATA_TYPE_T_DATESTRUCT,1,PACK_MANUFACT_PART,offsetof(T_BmsPACKManufactStruct,tActiveDate),1,DATA_TYPE_INT16U,0,6,NULL,},
	{0x04,0,3458,DATA_TYPE_INT16U,1,PACK_MANUFACT_PART,offsetof(T_BmsPACKManufactStruct,wCellCycleTimes),1,DATA_TYPE_INT16U,0,2,NULL,},
};
/********************************************************************/

/***************************************************************************
* 函数名称：GetDataAttrPoint()
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：
* 作    者：fsg
* 设计日期：2010-01-24
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
const T_OneDataAttrStruct  *GetDataAttrPoint(void)
{
	/*if(NULL == s_atAscDataAttr)
	{
		return NULL;
	}*/
	return s_atAscDataAttr;
}

/***************************************************************************
* 函数名称：GetDataAttrNum()
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：
* 作    者：fsg
* 设计日期：2010-01-24
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
INT32  GetDataAttrNum(void)
{
	/*if(NULL == s_atAscDataAttr)
	{
		return APP_FAILURE;
	}*/
	return sizeof(s_atAscDataAttr) / sizeof(s_atAscDataAttr[0]);
}
