/**************************************************************************
* Copyright (C) 2001, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：ZXDU18 CTL板软件
* 文件名称：protocolV_20.h
* 文件说明：协议模块头文件
* 作    者：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/
#ifndef PROTOCOL_V20_H_
#define PROTOCOL_V20_H_

#ifdef __cplusplus 
extern "C" {
#endif

#define DELAY           1       // 延时型的

// #include "protocol.h"
#define     GET_ANA_DATA_V20        0x42      //获取遥测量信息
#define     GET_DIG_DATA_V20        0x44      //获取遥信量信息
#define     SET_CTRL_DATA_V20       0x45      //遥控命令
#define     GET_PARA_DATA_V20       0x47      //获取遥调量信息
#define     SET_PARA_DATA_V20       0x49      //设定遥调量信息
#define     GET_PROTOCOL_VER_V20    0x4F      //获取通信协议版本号
#define     GET_FACTORY_INFO_V20    0x51      //获取设备厂商信息
#define     GET_HISTORY_DATA_V20    0x4B      //获取历史数据
#define     GET_TIME_V20            0x4D      //获取时间
#define     SET_TIME_V20            0x4E      //同步时间

#define     DISCHG_CONTROL          0x00       //放电控制
#define     CHARGE_CONTROL          0x01       //充电控制
#define     CURR_LIMIT_CONTROL      0x02       //限流控制
#define     HEAT_CONTROL            0x03       //加热控制
#define     SYSTEM_SHUTDOWN         0x04       //系统关机
#define     RESTORE_FACTORY         0x05       //恢复出厂
#define     SYSTEM_RESTART          0x06       //系统重启

#define     CELL_WARMUP_START_OFFSET            72    //电芯加热开启参数
#define     CELL_WARMUP_STOP_OFFSET             74    //电芯加热停止参数
#define     SECOND_LEVEL_CURR_HIGH_PRT_OFFSET   108   //二级过流保护参数
#define     SECOND_LEVEL_CURR_HIGH_DELAY_OFFSET 110   //二级过流延时参数
#define     CHG_RECO_CAP_OFFSET                 128   //充电恢复容量参数
#define     EQU_HIGH_TEMP_DISABLE_OFFSET        132   //均衡高温禁止参数
#define     EQU_LOW_TEMP_DISABLE_OFFSET         134   //均衡低温禁止参数

#define        CUSTOMIZESWITCHNUM               0      //铁塔协议参数
#define        PRTBOARDTYPE                 "SmartLi"
#define        PACKLOCATION                     0

extern SHORT  FloatChangeToModbus(FLOAT fData);
extern SHORT Host2Modbus(SHORT *wTemp);
BOOLEAN GetTowerSleepStatus();
void SetTowerSleepStatus(BOOLEAN status);
WORD Word2Modbus(WORD wTemp);
void SetInvalidFlag(ULONG ulOffset, BYTE ucLen);
#ifdef __cplusplus
}
#endif
	
#endif

