/**************************************************************************
* Copyright (C) 2001, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：ZXDU18 CTL板软件
* 文件名称：protocol.c
* 文件说明：协议模块
* 作    者：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/
#include "sys/time.h"
#include "common.h"
#include "sample.h"
#include "hisdata.h"
#include "realAlarm.h"
#include "battery.h"
#include "para.h"
#include "comm.h"
#include "protocol.h"
#include "CommCan.h"
#include "Candrive.h"
#include "stdio.h"
#include "commBdu.h"
#include "led.h"
#include "apptest_product.h"
#include "apptest.h"
#include "flash.h"
#include "fileSys.h"
#include "qtp.h"
#include <rtc_PFC8563.h>
#ifdef DEVICE_USING_R321
#include "protocol1363.h"
#include "protocol_V20.h"
#include "protocolDeye.h"
#else
#include "protocol_V20.h"
#include "protocol_v25.h"
#include "protocolPAD.h"
#endif
#include <dfs.h>
#include "utils_rtthread_security_func.h"
#include "fcntl.h"
#include "pdt_version.h"
#ifdef INTELLIGENT_PEAK_SHIFTING
#include "peakshift.h"
#endif

extern rt_mutex_t s_ptMutexFile;
rt_err_t PFC8563set_date(Datebuff *Rcvdate);  // TODO: 待BSP添加对应声明后删除

/***********************  变量定义  ************************/
// 远程下载触发超时判断
BYTE g_ucTriggerRepeat = 0;
WORD g_wTriggerTimeOut = 0;

// 工装测试
BYTE s_ucTestTriggerRepeat = 0;
BYTE s_ucP2PTestTriggerRepeat = 0;
WORD s_wApptestTriggerTimeOut = 0;

// QTP测试
BYTE s_ucQtpTestTriggerRepeat = 0;
BYTE s_ucQtpP2PTestTriggerRepeat = 0;
WORD s_wQtptestTriggerTimeOut = 0;

rt_timer_t timer3MinOneShot; // 单次定时器，用于定时3Min,处理apptest模式下灯的闪烁状态

static T_SysPara s_tSysPara;

static T_RemoteTrigStruct   s_tTrgFrame;      //文件上传协议触发帧 
static T_RemoteTransStruct s_tTransFileData;  //文件上传协议传输结构体
static char s_remoteCommFlag = 0;             //文件上传（文件导出）协议标志位
Static T_FileUploadStruct s_atUploadFile;     //文件上传信息
Static rt_int32_t s_readListLseek = 0;
Static rt_int32_t s_readFileLseek = 0;
static rt_uint8_t s_aucBufTemp[REMOTE_COMM_BUFF_LEN] = {0}; //转码前组包

#ifdef SHUTDOWNSLEEP
static BYTE s_ucShutdownTriggerRepeat = 0;
static BYTE s_ucStartSleepTriggerRepeat = 0;
#endif

// 触发命令帧信息
const BYTE  s_aucTxTrigger[] = {
    0x7e, 0x32, 0x30, 0x30, 0x31, 0x34, 0x30, 0x45, 0x45,
    0x35, 0x35, 0x35, 0x41, 0x41, 0x35, 0x41, 0x41, 0x0d};
const BYTE  s_aucRxTrigger[] = {
    0x7e, 0x32, 0x30, 0x30, 0x31, 0x34, 0x30, 0x45, 0x45,
    0x35, 0x36, 0x35, 0x42, 0x41, 0x36, 0x41, 0x42, 0x0d};

// apptest 触发命令
const BYTE s_aucTestTrigger[] = {
    0x7e, 0x32, 0x31, 0x30, 0x31, 0x30, 0x30, 0x30, 0x30,
    0x30, 0x30, 0x30, 0x30, 0x46, 0x44, 0x42, 0x43, 0x0d};
const BYTE s_aucTestRxTrigger[] = {
    0x7E, 0x32, 0x31, 0x30, 0x31, 0x30, 0x30, 0x30, 0x30, 0x45,
    0x30, 0x30, 0x32, 0x30, 0x30, 0x46, 0x44, 0x34, 0x35, 0x0D}; // 进入时触发帧回复帧
const BYTE s_aucTestRxNormal[] = {
    0x7E, 0x32, 0x31, 0x30, 0x31, 0x30, 0x30, 0x30, 0x30, 0x45,
    0x30, 0x30, 0x32, 0x30, 0x31, 0x46, 0x44, 0x34, 0x34, 0x0D}; // 进入apptest后，触发帧回复帧

// QTP 触发命令
const BYTE s_aucQTPTestTrigger[] = {
    0x7E, 0x32, 0x34, 0x30, 0x31, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
    0x30, 0x30, 0x46, 0x44, 0x42, 0x39, 0x0D};
const BYTE s_aucQTPTestRxTrigger[] ={
    0x7E, 0x32, 0x34, 0x30, 0x31, 0x30, 0x30, 0x30, 0x30, 0x45, 0x30,
    0x30, 0x32, 0x30, 0x30, 0x46, 0x44, 0x34, 0x32, 0x0D}; // 进入时触发帧回复帧
const BYTE s_aucQTPTestRxNormal[] ={
    0x7E, 0x32, 0x34, 0x30, 0x31, 0x30, 0x30, 0x30, 0x30, 0x45, 0x30,
    0x30, 0x32, 0x30, 0x31, 0x46, 0x44, 0x34, 0x31, 0x0D}; // 进入QTP后，触发帧回复帧

T_ProtocolStruct s_tProtocol;    /* 通信协议层 */

/**********************  外部函数、变量  ***********************/
extern void ClearRecBuf(T_CommStruct *ptComm);

#ifdef DEVICE_USING_R321
extern const T_CmdFuncStruct s_atCID2All1363Table[];
extern const T_CmdFuncStruct s_atCID2AllTowerTable[];
extern const T_CmdFuncStruct s_atCID2AllDeyeTable[];
Static BYTE IsVariablelengthCommand2(void);
static BOOLEAN isGetRecordCommand(void);
static BYTE IsVariableDeyeCID2Command(void);
#else
extern const T_CmdFuncStruct s_atCID2AllPADTable[];
extern const T_CmdFuncStruct s_atCID2AllTowerTable[];
#endif
/*********************  静态函数原型定义  **********************/
static BYTE GetRTN(T_CommStruct *ptComm);
static void DealCommand(BYTE ucComType); // TODO: 宏分割
#ifdef DEVICE_USING_D121
static void DealPADCommand(BYTE ucComType); // TODO: 宏分割
#endif
static void DealCommand_v20(BYTE ucComType);
static void DealBMSCommand(BYTE ucPort);
static void ReadySendData(T_CommStruct *ptComm);
static BOOLEAN JudgeAPPTsetNoReturn(BYTE ucPort);
static BOOLEAN Judge1104NoReturn(BYTE ucPort);
static BOOLEAN JudgeQTPTsetNoReturn(BYTE ucPort);
static CHAR DealAppTestData(T_CommStruct *ptComm);
static CHAR DealQtpTestData(T_CommStruct *ptComm);
static void DealApptestRtnAbnormal(void);
static void DealP2PTrigger(void);
static const T_CmdFuncStruct *GetCmdFnucStruct(BYTE segment);
static BOOLEAN DealRemoteCommData(T_CommStruct *ptComm);
static BOOLEAN ParseTrgFrame(char *pucRecBuf, unsigned int uRcevLen, T_RemoteTrigStruct *ptRemoteTrig, char *pucConvertBuf);
static void InitBattParaAndStatus(void);
static BOOLEAN DealDiffPrtclCommand(T_CommStruct *ptComm);
static BOOLEAN DealPrtclCommand(T_CommStruct *ptComm);
#ifdef POWER_COMM_FAIL_ALARM
static CHAR JudgePowerComm(void);
#endif

/*************************  命令码处理  *************************/
// CID1处理
const T_CmdFuncStruct s_atCID1AllFuncTable[] =
{
    {CID1_BMS, DealBMSCommand, 0},
    {0x00, 0x0000, 0x00},
};

const T_CmdFuncStruct s_atCID2BMSFuncTable[] =
{
    {0x00, 0x0000, 0x00},
};

const T_CmdFuncStruct* s_atCID2AllFuncTable[] =
#ifdef DEVICE_USING_R321
{s_atCID2All1363Table, s_atCID2BMSFuncTable, s_atCID2AllTowerTable, s_atCID2AllDeyeTable};
#else
{s_atCID2AllPADTable, s_atCID2BMSFuncTable, s_atCID2AllTowerTable};
#endif



/****************************************************************************
 * 函数名称：
 * 输入参数：
 * 返 回 值：
 * 功能描述：如果版本号为偶数，版本号加1，否则不变
 * 作    者  ：王威
 * 版本信息：V1.0
 * 设计日期：2010-06-13
 * 修改记录：
 * 日    期      版  本      修改人      修改摘要
 ***************************************************************************/
static const T_CmdFuncStruct *GetCmdFnucStruct(BYTE segment)
{
    if (segment < sizeof(s_atCID2AllFuncTable) / sizeof(s_atCID2AllFuncTable[0]))
    {
        return s_atCID2AllFuncTable[segment];
    }

    return NULL;
}

/****************************************************************************
 * 函数名称：DealTriggerTimeOut
 * 输入参数：无
 * 返 回 值：无
 * 功能描述：远程下载触发超时判断
 * 作    者  ：王威
 * 版本信息：V1.0
 * 设计日期：2010-06-13
 * 修改记录：
 * 日    期      版  本      修改人      修改摘要
 ***************************************************************************/
void DealTriggerTimeOut(void)
{
    if (++g_wTriggerTimeOut > 300)
    {
        g_ucTriggerRepeat = 0;
        g_wTriggerTimeOut = 0;
    }

    if (0 == g_ucTriggerRepeat)
    {
        g_wTriggerTimeOut = 0;
    }

    if (++s_wApptestTriggerTimeOut > 300)
    {
        s_ucP2PTestTriggerRepeat = 0;
        s_wApptestTriggerTimeOut = 0;
    }

    if (0 == s_ucP2PTestTriggerRepeat)
    {
        s_wApptestTriggerTimeOut = 0;
    }

    return;
}

BOOLEAN is_p2p_trigger(void) {
#ifdef POWER_COMM_FAIL_ALARM
    JudgePowerComm();
#endif
    if (P2P_TRIGGER == s_tProtocol.ucCID2 &&
        CID1_COMMON == s_tProtocol.ucCID1 &&
        PROTOCOL_VER_21 == s_tProtocol.ucVer)
        return True;
   return False;    
}

BOOLEAN is_pad_protocol(void) {
    if (s_tProtocol.ucCID1 == CID1_PAD &&
        PROTOCOL_VER_20 == s_tProtocol.ucVer)
        return True;
    return False;    
}

BOOLEAN is_fixednetwork_protocol(void) {
    if (CID1_1104 == s_tProtocol.ucCID1 && 
        PROTOCOL_VER_25 == s_tProtocol.ucVer)
        return True;
    return False;    
}

BOOLEAN is_tower_protocol(void) {
    if (s_tProtocol.ucCID1 == CID1_TOWER &&
        PROTOCOL_VER_20 == s_tProtocol.ucVer)
        return True;
    return False;    
}

BOOLEAN is_deye_protocol(void) {
    if (s_tProtocol.ucCID1 == CID1_DEYE &&
        PROTOCOL_VER_DEYE == s_tProtocol.ucVer)
        return True;
    return False;    
}

/****************************************************************************
* 函数名称：DealCommData()
* 调    用：无
* 被 调 用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：处理接收到的数据包
* 作    者  ：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
void Deal1363CommData(T_CommStruct *ptComm)
{
    if ( ptComm->ucPortType >= SCI_PORT_NUM )
    {
        return;
    }
    // 处理APPTEST
    if (SUCCESSFUL == DealAppTestData(ptComm))
    {
        return;
    }
    // 处理QTP
    if (SUCCESSFUL == DealQtpTestData(ptComm))
    {
        return;
    }
    /* 处理文件上传协议 */
    if (SUCCESSFUL == DealRemoteCommData(ptComm))
    {
        return;
    }
    SetRemoteCommFlag(False); //非远程下载协议帧，则退出

    rt_memset_s(&s_tProtocol, sizeof(s_tProtocol), 0, sizeof(s_tProtocol));
    s_tProtocol.wOriginLength = ptComm->wRecLength;

    ConvertAscii2Hex(ptComm->aucRecBuf, s_tProtocol.aucRecBuf, s_tProtocol.wOriginLength);

    GetSysPara(&s_tSysPara);

    /* 根据接收到的数据包判断响应数据包的RTN值 */
    s_tProtocol.ucRTN = GetRTN(ptComm);

    /* 清空底层接收缓冲区 */
    ClearRecBuf(ptComm);

    /* 若RTN为正确，组织要发送数据 */
    if (RTN_CORRECT == s_tProtocol.ucRTN)
    {
        DealDiffPrtclCommand(ptComm);
    }
    /* 若数据有误，响应包的INFO部分为空 */
    else
    {
        s_tProtocol.wSendLenid = 0;
    }

    /* 如果需要返回数据包 */
    if (s_tProtocol.ucRTN != NO_RETURN && s_tProtocol.ucAddr != getGrpAddr())
    {
        /* 将要发送数据打包放入底层发送缓冲区 */
        ReadySendData(ptComm);
        // 如果发现是设置参数的命令时执行设置参数的命令
        if (GetParaSaveFlag())
        {
            GetTempSysPara(&s_tSysPara);
            if(SetSysPara(&s_tSysPara, TRUE, CHANGE_BY_YD1363))
            {
                SetParaSaveFlag(FALSE);
            }
        }
    }

#ifdef QUICK_RESPONSE_ENABLED
    if (ShouldSaveSysParaBy1363())
    {
        ExecuteSaveSysParaBy1363();
        SetSaveSysParaBy1363Flag(False);
    }
#endif /* QUICK_RESPONSE_ENABLED */

    return;
}

/// @brief 处理不同协议的命令
/// @param void
/// @return
static BOOLEAN DealDiffPrtclCommand(T_CommStruct *ptComm)
{
    rt_memset_s(s_tProtocol.aucInvalidFlag, sizeof(s_tProtocol.aucInvalidFlag), 0x00, sizeof(s_tProtocol.aucInvalidFlag));
    if (is_p2p_trigger())
    {
        DealP2PTrigger();
    }
#ifdef SHUTDOWNSLEEP
    else if (CTRL_BMS_SHUTDOWN == s_tProtocol.ucCID2 && CID1_BMS == s_tProtocol.ucCID1 &&
             PROTOCOL_VER_22 == s_tProtocol.ucVer)
    {
        for (WORD i = 0; i < HIGH_SEND_LEN; i++) /* 清空响应缓冲区 */
        {
            s_tProtocol.aucSendBuf[i] = 0;
        }
        s_tProtocol.wSendLenid = 2;
        s_ucShutdownTriggerRepeat++;
        if (s_ucShutdownTriggerRepeat >= TRIGGER_COUNTER)
        {
            s_ucShutdownTriggerRepeat = 0;
            DealCommand(ucPort);
        }
    }
    else if (CTRL_BDU_SLEEP_START == s_tProtocol.ucCID2 && CID1_BMS == s_tProtocol.ucCID1 &&
             PROTOCOL_VER_22 == s_tProtocol.ucVer)
    {
        for (WORD i = 0; i < HIGH_SEND_LEN; i++) /* 清空响应缓冲区 */
        {
            s_tProtocol.aucSendBuf[i] = 0;
        }
        s_tProtocol.wSendLenid = 2;
        s_ucStartSleepTriggerRepeat++;
        if (s_ucStartSleepTriggerRepeat >= TRIGGER_COUNTER)
        {
            s_ucStartSleepTriggerRepeat = 0;
            DealCommand(ucPort);
        }
    }
#endif
    else
    {
        DealPrtclCommand(ptComm);
    }
    return TRUE;
}

/// @brief 处理协议命令
/// @param void
/// @return
static BOOLEAN DealPrtclCommand(T_CommStruct *ptComm)
{
    if (GetApptestFlag() == True)
    {
        DealApptestCommand(ptComm);
        DealApptestRtnAbnormal();
    }
    else if (GetQtptestFlag() == True)
    {
        DealQtptestCommand(ptComm->ucPortType);
        DealQtptestRtnAbnormal();
    }
#ifdef DEVICE_USING_D121
    else if (is_pad_protocol())
    {
        DealPADCommand(ptComm->ucPortType);
    }
    else if (is_fixednetwork_protocol())
    {
        // 固网协议接收到命令后延时100ms回包
        rt_thread_delay(100);
        DealCommand_v25(ptComm->ucPortType);
    }
#endif
    else if (is_tower_protocol())
    {
        DealCommand_v20(ptComm->ucPortType);
    }
#ifdef DEVICE_USING_R321
    else
    {
        DealCommand(ptComm->ucPortType);
    }
#endif
    return TRUE;
}


/****************************************************************************
* 函数名称：DealAppTestData()
* 调    用：无
* 被 调 用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：处理接收到的APPTEST数据包
* 作    者  ：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
static CHAR DealAppTestData(T_CommStruct *ptComm)
{

    BYTE i;
    T_CtrlOutStruct tCtrlOut;
    GetCtrlOut(&tCtrlOut);

    if (!rt_memcmp(ptComm->aucRecBuf, s_aucTestTrigger, sizeof(s_aucTestTrigger)))
    {
#ifdef POWER_COMM_FAIL_ALARM
        SetPowerCommShieldFlag(True);
#endif
        DealDeviceUnlock(AUTO_UNLOCK);
        s_ucTestTriggerRepeat++;
    }
    else
    {
        s_ucTestTriggerRepeat = 0;
    }

    if (s_ucTestTriggerRepeat != 0 && GetApptestFlag() == False)
    {
        // 清空接收缓冲区
        ClearRecBuf(ptComm);
        rt_memcpy(ptComm->aucSendBuf, s_aucTestRxTrigger, sizeof(s_aucTestRxTrigger));
        ptComm->wSendLength = sizeof(s_aucTestRxTrigger);

        if (s_ucTestTriggerRepeat >= TRIGGER_COUNTER)
        {
            SetApptestFlag(TRUE);
            SaveAction(GetActionId(CONTOL_ENTER_APPTEST),"Enter Apptest");
            ClearCellDamagePrt();  //清除单体损坏保护
            InitBattParaAndStatus();

            for (i = 0; i < 6; i++) // 进入apptest模式后,立即将所有的指示灯点亮。
            {
                tCtrlOut.bLed[i] = LEDON;
            }
            tCtrlOut.bBuzz = FALSE;
            SetCtrlOut(&tCtrlOut);

            if (timer3MinOneShot == NULL)
            {
                timer3MinOneShot = rt_timer_create("timer_3Min_OneShot", SetLedByAddr, RT_NULL,
                                                   3 * (ONE_MINUTE * 10), // F231时钟节拍1ms
                                                   RT_TIMER_FLAG_ONE_SHOT | RT_TIMER_FLAG_SOFT_TIMER);
            }
            if (timer3MinOneShot != NULL)
            {
                // 触发单次定时器后在退出apptest协议处要删除该定时器
                rt_timer_start(timer3MinOneShot);
            }

            ClearBtnCount(); // 清除按键次数，重新计数

            rt_memcpy(ptComm->aucSendBuf, s_aucTestRxNormal, sizeof(s_aucTestRxNormal));
            ptComm->wSendLength = sizeof(s_aucTestRxNormal);
        }

        if (ptComm->ucPortType != COMM_CAN)
        {
            sendCommData(ptComm);
        }

        return SUCCESSFUL;
    }
    else if (GetApptestFlag() && s_ucTestTriggerRepeat != 0)
    {
        // 清空接收缓冲区
        ClearRecBuf(ptComm);
        rt_memcpy(ptComm->aucSendBuf, s_aucTestRxNormal, sizeof(s_aucTestRxNormal));
        ptComm->wSendLength = sizeof(s_aucTestRxNormal);

        if (ptComm->ucPortType != COMM_CAN)
        {
            sendCommData(ptComm);
        }

        return SUCCESSFUL;
    }

    return FAILED;
}

/****************************************************************************
* 函数名称：DealQtpTestData()
* 调    用：无
* 被 调 用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：处理接收到的QTPTEST数据包
* 作    者  ：
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
static CHAR DealQtpTestData(T_CommStruct *ptComm)
{
    T_CtrlOutStruct tCtrlOut;
    GetCtrlOut(&tCtrlOut);

    if (!rt_memcmp(ptComm->aucRecBuf, s_aucQTPTestTrigger, sizeof(s_aucQTPTestTrigger)))
    {
#ifdef POWER_COMM_FAIL_ALARM
        SetPowerCommShieldFlag(True);
#endif
        DealDeviceUnlock(AUTO_UNLOCK);
        s_ucQtpTestTriggerRepeat++;
    }
    else
    {
        s_ucQtpTestTriggerRepeat = 0;
    }

    if (s_ucQtpTestTriggerRepeat != 0 && GetQtptestFlag() == False)
    {
        // 清空接收缓冲区
        ClearRecBuf(ptComm);
        rt_memcpy(ptComm->aucSendBuf, s_aucQTPTestRxTrigger, sizeof(s_aucQTPTestRxTrigger));
        ptComm->wSendLength = sizeof(s_aucQTPTestRxTrigger);

        if (s_ucQtpTestTriggerRepeat >= TRIGGER_COUNTER)
        {
            CtrlReleaseLock();
            Set_Mode_Work(BUT_DEFENCE, False);      //QTP模式下禁用按键防盗查询
            SetQtptestFlag(TRUE);
            SaveAction(GetActionId(CONTOL_ENTER_QTP),"Enter Qtp");
            s_ucQtpTestTriggerRepeat = 0;

            rt_memcpy(ptComm->aucSendBuf, s_aucQTPTestRxNormal, sizeof(s_aucQTPTestRxNormal));
            ptComm->wSendLength = sizeof(s_aucQTPTestRxNormal);
        }
        sendCommData(ptComm);

        return SUCCESSFUL;
    }
    else if (GetQtptestFlag() && s_ucQtpTestTriggerRepeat != 0)
    {
        // 清空接收缓冲区
        ClearRecBuf(ptComm);
        rt_memcpy(ptComm->aucSendBuf, s_aucQTPTestRxNormal, sizeof(s_aucQTPTestRxNormal));
        ptComm->wSendLength = sizeof(s_aucQTPTestRxNormal);
        sendCommData(ptComm);

        return SUCCESSFUL;
    }

    return FAILED;
}

/****************************************************************************
* 函数名称：DataPackShift
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：发送打包码转换规则：凡是遇到0DH、7EH、7DH、10H、12H、7FH字节时，变成两个字节：7DH 和该字节的反码
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
int DataPackShift(char *sendBuf, unsigned int uSendBuflen, char *packBuf)
{
    int i;
    int packLen = 1;

    packBuf[0] = REMOTE_COMM_STANDARD_SOI;
    for (i = 1; i < uSendBuflen-1; i++)
    {
        if ((sendBuf[i] == REMOTE_COMM_STANDARD_SOI) || \
        (sendBuf[i] == REMOTE_COMM_STANDARD_EOI) || \
        (sendBuf[i] == REMOTE_COMM_SHIFT_FLG) || \
        (sendBuf[i] == REMOTE_COMM_CONVERT_INFO1) || \
        (sendBuf[i] == REMOTE_COMM_CONVERT_INFO2) || \
        (sendBuf[i] == REMOTE_COMM_CONVERT_INFO3))
        {
            packBuf[packLen++] = REMOTE_COMM_SHIFT_FLG;
            packBuf[packLen++] = ~sendBuf[i];
        }
        else
        {
            packBuf[packLen++] = sendBuf[i];
        } 
    }
    packBuf[packLen++] = REMOTE_COMM_STANDARD_EOI;
    return packLen;
}

/****************************************************************************
* 函数名称：DataParseShift
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：接收码变换规则，除帧头帧尾外，凡遇到7DH字节的取下一个字节的反码作为有效数据
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
int DataParseShift(char *pucRecvBuf, unsigned int uRcevLen, char *pucParseBuf)
{
    unsigned int i;
    int uParseLen = 1;

    pucParseBuf[0] = REMOTE_COMM_STANDARD_SOI;
    for (i = 1; i < uRcevLen - 1; i++)
    {
        if (REMOTE_COMM_SHIFT_FLG == pucRecvBuf[i])
        {
            i++;
            pucParseBuf[uParseLen++] = ~pucRecvBuf[i];
        }
        else
        {
            pucParseBuf[uParseLen++] = pucRecvBuf[i];
        }
    }
    pucParseBuf[uParseLen++] = REMOTE_COMM_STANDARD_EOI;
    return uParseLen;
}

/****************************************************************************
* 函数名称：ParseTrgFrame
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：解析触发帧
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
static BOOLEAN ParseTrgFrame(char *pucRecBuf, unsigned int uRcevLen, T_RemoteTrigStruct *ptRemoteTrig, char *pucConvertBuf)
{
    BYTE ucAddr = 0;
    WORD wRecvAddr = 0;
    BYTE ucTrgResp[] = {0x30,0x31,0x34,0x30,0x45,0x45,\
                        0x35,0x36,0x35,0x42,0x41,0x36,0x41,0x42}; //触发帧响应
    
    /* 校验地址 */
    ucAddr = GetTotalAddr();
    wRecvAddr = ASCIIToHex(pucConvertBuf[REMOTE_COMM_TRG_ADDR_INDEX]) * 16 + ASCIIToHex(pucConvertBuf[REMOTE_COMM_TRG_ADDR_INDEX + 1]);
    if (ucAddr != wRecvAddr)
    {
        return FAILURE;
    }

    /* 触发帧无CRC,通过frame_type、chip_type进行校验 */
    rt_memcpy(&ptRemoteTrig->frame_type, &pucConvertBuf[REMOTE_COMM_TRG_FRAME_INDEX], 2);
    rt_memcpy(&ptRemoteTrig->chip_type, &pucConvertBuf[REMOTE_COMM_TRG_CHIP_INDEX], 2);
    if ((ptRemoteTrig->frame_type != REMOTE_COMM_TRG_FRAME_TYPE) || \
        (ptRemoteTrig->chip_type  != REMOTE_COMM_TRG_CHIP_TYPE))
        {
            return FAILURE;
        }

    ptRemoteTrig->head = REMOTE_COMM_STANDARD_SOI;
    ptRemoteTrig->frame_type = ((REMOTE_COMM_TRG_FRAME_TYPE & 0xFF) << 8) | ((REMOTE_COMM_TRG_FRAME_TYPE & 0xFF00) >> 8);
    ptRemoteTrig->chip_type = ((REMOTE_COMM_TRG_VERS_F & 0xFF) << 8) | ((REMOTE_COMM_TRG_VERS_F & 0xFF00) >> 8);
    ptRemoteTrig->endCode = REMOTE_COMM_STANDARD_EOI;
    rt_memcpy(&ptRemoteTrig->addr, &pucConvertBuf[REMOTE_COMM_TRG_ADDR_INDEX], 2);
    rt_memcpy(&ptRemoteTrig->reserved, &pucConvertBuf[REMOTE_COMM_TRG_RESERVED_INDEX], 2);
    rt_memcpy(ptRemoteTrig->trig_code, ucTrgResp, REMOTE_COMM_TRG_CODE_LEN);

    return SUCCESSFUL;
}

/****************************************************************************
* 函数名称：ParseFrameProcess()
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：解析文件上传数据帧
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
static BOOLEAN ParseFrameProcess(char *pucRecvBuf, WORD wRcevLen, T_RemoteTransStruct *ptTransFile, char *pucConvertBuf)
{
    BYTE ucAddr = 0;
    
    ucAddr = GetTotalAddr();
    if (ucAddr != get_int16_data((const unsigned char*)&pucConvertBuf[REMOTE_COMM_ADDR_ADDR]))
    {
        return FAILURE;
    }

    ptTransFile->ucHeader = pucConvertBuf[REMOTE_COMM_HEADER_ADDR];
    ptTransFile->ucFlag = pucConvertBuf[REMOTE_COMM_FLAG_ADDR];
    ptTransFile->wAddr = get_int16_data((const unsigned char*)&pucConvertBuf[REMOTE_COMM_ADDR_ADDR]);
    ptTransFile->wReserved = get_int16_data((const unsigned char*)&pucConvertBuf[REMOTE_COMM_REVSERD_ADDR]);
    ptTransFile->wDataLen = get_int16_data((const unsigned char*)&pucConvertBuf[REMOTE_COMM_DATALEN_ADDR]);
    ptTransFile->wFN1 = get_int16_data((const unsigned char*)&pucConvertBuf[REMOTE_COMM_FN1_ADDR]);
    ptTransFile->wFN2 = get_int16_data((const unsigned char*)&pucConvertBuf[REMOTE_COMM_FN2_ADDR]);
    ptTransFile->ucDevice = pucConvertBuf[REMOTE_COMM_DEVICE_ADDR];
    ptTransFile->ucCID = pucConvertBuf[REMOTE_COMM_CID_ADDR];
    ptTransFile->ucRtnCode = pucConvertBuf[REMOTE_COMM_RTN_ADDR];
    if (ptTransFile->wDataLen <= UPLOAD_DATA_FRAME_LEN)
    {
        rt_memcpy(&ptTransFile->aucData[0], &pucConvertBuf[REMOTE_COMM_DATA_ADDR], ptTransFile->wDataLen);
    }
    else
    {
        return FAILURE;
    }
    ptTransFile->ucEndCode = REMOTE_COMM_STANDARD_EOI;

    return SUCCESSFUL;
}

/****************************************************************************
* 函数名称：GetFileList()
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：获取电池文件列表,不同功能根据CID区分，历史互备份、电芯统计记录文件等
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
void GetFileList(char *pucSendBuf, WORD *pwSendBufLen, T_RemoteTransStruct *ptTransFile)
{
    rt_int32_t fd;
    rt_uint32_t offset = 0;
    static rt_uint32_t uTotalByte = 0;

    rt_int32_t readLen = 0;
    rt_int32_t endLen = 0;
    rt_int32_t readLseek = 0;
    struct stat st;

    rt_memset_s(s_aucBufTemp, sizeof(s_aucBufTemp), 0, sizeof(s_aucBufTemp));

    /* 获取上传文件列表首发帧 */
    if (0 == ptTransFile->wFN2)
    {
        /* 将文件列表名保存成文件 */
        GetFileListInfo();

        /* 获取文件列表总大小 */
        if (stat(FILELIST, &st) == 0)
        {
            uTotalByte = st.st_size;
        }

        /* 上传的文件列表总帧数，向上取整 */
        s_atUploadFile.wListTotalFrame = (uTotalByte + UPLOAD_DATA_FRAME_LEN - 1) / UPLOAD_DATA_FRAME_LEN; 

        /* 组包响应总帧数wListTotalFrame */
        s_aucBufTemp[offset] = ptTransFile->ucHeader;
        offset += 1;

        s_aucBufTemp[offset] = ptTransFile->ucFlag;
        offset += 1;

        PutInt16ToBuff(&s_aucBufTemp[offset], ptTransFile->wAddr);
        offset += 2;

        PutInt16ToBuff(&s_aucBufTemp[offset], ptTransFile->wReserved);
        offset += 2;

        PutInt16ToBuff(&s_aucBufTemp[offset], REMOTE_COMM_DATA_LEN);
        offset += 2;

        PutInt16ToBuff(&s_aucBufTemp[offset], ptTransFile->wFN1);
        offset += 2;

        PutInt16ToBuff(&s_aucBufTemp[offset], ptTransFile->wFN2);
        offset += 2;

        s_aucBufTemp[offset] = ptTransFile->ucDevice;
        offset += 1;

        s_aucBufTemp[offset] = ptTransFile->ucCID;
        offset += 1;

        s_aucBufTemp[offset] = ptTransFile->ucRtnCode;
        offset += 1;

        PutInt16ToBuff(&s_aucBufTemp[offset], s_atUploadFile.wListTotalFrame);
        offset += 2;

        ptTransFile->wCRC = Calculate_CRC16(&s_aucBufTemp[1], offset-1);
        PutInt16ToBuff(&s_aucBufTemp[offset], ptTransFile->wCRC);
        offset += 2;

        s_aucBufTemp[offset] = ptTransFile->ucEndCode;
        offset += 1;
    }
    else //获取上传文件列表帧 FN2:上位机要请求的包序号，FN2从1开始
    {	
        rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);
        fd = open(FILELIST, O_RDONLY);
        if (fd < 0)
        {
            ptTransFile->ucRtnCode = 2;
            rt_mutex_release(s_ptMutexFile);
            return;
        }
        readLseek = lseek(fd, s_readListLseek, SEEK_SET);
        if (readLseek < 0)
        {
            close(fd);
            rt_mutex_release(s_ptMutexFile);
            return;
        }

        //分包获取文件列表
        endLen = uTotalByte % UPLOAD_DATA_FRAME_LEN;
        if (endLen == 0 || ptTransFile->wFN2 < s_atUploadFile.wListTotalFrame)
        {
            readLen = rt_read_s(fd, ptTransFile->aucData, sizeof(ptTransFile->aucData), UPLOAD_DATA_FRAME_LEN);
            if (readLen < 0)
            {
                ptTransFile->ucRtnCode = 2;
                close(fd);
                rt_mutex_release(s_ptMutexFile);
                return;
            }
            s_readListLseek += readLen;
        }
        else
        {
            readLen = rt_read_s(fd, ptTransFile->aucData, sizeof(ptTransFile->aucData), endLen);
            if (readLen < 0)
            {
                ptTransFile->ucRtnCode = 2;
                close(fd);
                rt_mutex_release(s_ptMutexFile);
                return;
            }
            s_readListLseek += readLen;
        }
        close(fd);
        rt_mutex_release(s_ptMutexFile);
        //组包响应文件列表
        offset = 0;
        s_aucBufTemp[offset] = ptTransFile->ucHeader;
        offset += 1;

        s_aucBufTemp[offset] = ptTransFile->ucFlag;
        offset += 1;

        PutInt16ToBuff(&s_aucBufTemp[offset], ptTransFile->wAddr);
        offset += 2;

        PutInt16ToBuff(&s_aucBufTemp[offset], ptTransFile->wReserved);
        offset += 2;
        
        PutInt16ToBuff(&s_aucBufTemp[offset], readLen);
        offset += 2;

        PutInt16ToBuff(&s_aucBufTemp[offset], ptTransFile->wFN1);
        offset += 2;

        PutInt16ToBuff(&s_aucBufTemp[offset], ptTransFile->wFN2);
        offset += 2;

        s_aucBufTemp[offset] = ptTransFile->ucDevice;
        offset += 1;

        s_aucBufTemp[offset] = ptTransFile->ucCID;
        offset += 1;

        s_aucBufTemp[offset] = ptTransFile->ucRtnCode;
        offset += 1;
        
        rt_memcpy_s(&s_aucBufTemp[offset], sizeof(s_aucBufTemp)-offset, ptTransFile->aucData, readLen);
        offset += readLen;
   
        if (offset <= REMOTE_COMM_BUFF_LEN) {
            ptTransFile->wCRC = Calculate_CRC16(&s_aucBufTemp[1], offset-1);
        }
        PutInt16ToBuff(&s_aucBufTemp[offset], ptTransFile->wCRC);
        offset += 2;

        if (offset < REMOTE_COMM_BUFF_LEN) {
            s_aucBufTemp[offset] = ptTransFile->ucEndCode;
        }
        offset += 1;
    }

    /* 发送前转码 */
    int packBuflen = 0;
    if (offset <= REMOTE_COMM_BUFF_LEN) {
        packBuflen = DataPackShift((char*)s_aucBufTemp, offset, (char*)pucSendBuf);
    }
    *pwSendBufLen = packBuflen;

    /* 通过包序号判断文件列表传输结束 */
    if (ptTransFile->wFN2 == s_atUploadFile.wListTotalFrame)
    {
        s_readListLseek = 0;
        SetRemoteCommFlag(False); //退出文件导出协议
    }

    return;
}

/****************************************************************************
* 函数名称：GetFileData()
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：获取上传的文件数据
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
void GetFileData(char *pucSendBuf, WORD *pwSendBufLen, T_RemoteTransStruct *ptTransFile)
{
    rt_int32_t fd = 0;
    rt_uint32_t offset = 0;
    rt_uint16_t wDataLen = 0;
    rt_uint8_t s_aucBufTemp[REMOTE_COMM_BUFF_LEN] = {0}; //转码前组包
    static rt_uint8_t s_ucFileName[UPLOAD_FILE_NAME_MAX_LEN] = {0};

    rt_int32_t readLen = 0;
    rt_int32_t endLen = 0;
    rt_int32_t readLseek = 0;
    struct stat st;
    time_t pt_time;

    static uint32_t crc_ctx = 0 ^ 0xFFFFFFFF;
    rt_uint8_t ucReqFileType = 0; //文件导出类型（0本地  1备份）

    rt_memset_s(s_aucBufTemp, sizeof(s_aucBufTemp), 0, sizeof(s_aucBufTemp));

    /* 上传文件首发信息帧 */
    if (ptTransFile->wFN2 == 0)
    {
        s_atUploadFile.wReqFileNameLen = get_int16_data(&ptTransFile->aucData[0]); //请求的文件名长度
        rt_memset_s( s_atUploadFile.ucReqFileName, UPLOAD_FILE_NAME_MAX_LEN, 0, sizeof(s_atUploadFile.ucReqFileName));
        rt_memcpy_s(s_atUploadFile.ucReqFileName, UPLOAD_FILE_NAME_MAX_LEN, &ptTransFile->aucData[2], s_atUploadFile.wReqFileNameLen); //请求的文件名
        s_atUploadFile.ucReqFileName[UPLOAD_FILE_NAME_MAX_LEN - 1] = '\0';

        wDataLen = (2 + s_atUploadFile.wReqFileNameLen + 4 + 20 + 4);

        rt_memset_s(s_ucFileName, sizeof(s_ucFileName), 0, sizeof(s_ucFileName));
        rt_memcpy(s_ucFileName, s_atUploadFile.ucReqFileName, UPLOAD_FILE_NAME_MAX_LEN);
        ucReqFileType = strchr((char*)s_atUploadFile.ucReqFileName, '_') != NULL ? 1 : 0; //请求的文件命中包含'_'，为互备份文件
        if (ucReqFileType)
        {
            rt_snprintf((char *)s_ucFileName, UPLOAD_FILE_NAME_MAX_LEN - 1, "/BACKUP/%s", (char*)s_atUploadFile.ucReqFileName);
        }

        // 获取文件总大小
        if (stat((char*)s_ucFileName, &st) == 0)
        {
            s_atUploadFile.uFileTotalLen = st.st_size;
        }
        
        //总帧数
        s_atUploadFile.wFileTotalFrame = (s_atUploadFile.uFileTotalLen + UPLOAD_DATA_FRAME_LEN - 1) / UPLOAD_DATA_FRAME_LEN; //向上取整
        PutInt16ToBuff(&ptTransFile->aucData[offset], s_atUploadFile.wFileTotalFrame);
        offset += 2;

        //上传文件名
        rt_memcpy_s(&ptTransFile->aucData[offset], UPLOAD_DATA_FRAME_LEN - offset, s_atUploadFile.ucReqFileName, s_atUploadFile.wReqFileNameLen);
        offset += s_atUploadFile.wReqFileNameLen;

        //上传文件总大小
        PutInt32ToBuff(&(ptTransFile->aucData[offset]), s_atUploadFile.uFileTotalLen);
        offset += 4;

        //上传文件时间
        time(&pt_time);
        rt_memset_s(&s_atUploadFile.ucFileTime[0], UPLOAD_FILE_TIME_LEN, 0, UPLOAD_FILE_TIME_LEN);
        put_time_t_to_buff(s_atUploadFile.ucFileTime, pt_time);
        rt_memcpy_s(&ptTransFile->aucData[offset], UPLOAD_DATA_FRAME_LEN - offset, s_atUploadFile.ucFileTime, sizeof(s_atUploadFile.ucFileTime));
        offset += UPLOAD_FILE_TIME_LEN;

        //文件校验码
        crc32_calc(&crc_ctx, ptTransFile->aucData, offset);
        s_atUploadFile.uCrc = crc_ctx;
        PutInt16ToBuff(&(ptTransFile->aucData[offset]), s_atUploadFile.uCrc);

        //组包响应文件信息
        offset = 0;
        s_aucBufTemp[offset] = ptTransFile->ucHeader;
        offset += 1;

        s_aucBufTemp[offset] = ptTransFile->ucFlag;
        offset += 1;

        PutInt16ToBuff(&s_aucBufTemp[offset], ptTransFile->wAddr);
        offset += 2;

        PutInt16ToBuff(&s_aucBufTemp[offset], ptTransFile->wReserved);
        offset += 2;

        PutInt16ToBuff(&s_aucBufTemp[offset], wDataLen);
        offset += 2;

        PutInt16ToBuff(&s_aucBufTemp[offset], ptTransFile->wFN1);
        offset += 2;

        PutInt16ToBuff(&s_aucBufTemp[offset], ptTransFile->wFN2);
        offset += 2;

        s_aucBufTemp[offset] = ptTransFile->ucDevice;
        offset += 1;

        s_aucBufTemp[offset] = ptTransFile->ucCID;
        offset += 1;

        s_aucBufTemp[offset] = ptTransFile->ucRtnCode;
        offset += 1;
        
        rt_memcpy_s(&s_aucBufTemp[offset], sizeof(s_aucBufTemp)-offset, ptTransFile->aucData, wDataLen);
        offset += wDataLen;

        ptTransFile->wCRC = Calculate_CRC16(&s_aucBufTemp[1], offset-1);
        PutInt16ToBuff(&s_aucBufTemp[offset], ptTransFile->wCRC);
        offset += 2;

        s_aucBufTemp[offset] = ptTransFile->ucEndCode;
        offset += 1;
    }
    else
    {		
        /* 打开请求的文件 */
        rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);
        fd = open((char*)s_ucFileName, O_RDONLY);
        if (fd < 0)
        {
            ptTransFile->ucRtnCode = 2;
            rt_mutex_release(s_ptMutexFile);
            return;
        }
        readLseek = lseek(fd, s_readFileLseek, SEEK_SET);
        if (readLseek < 0)
        {
            close(fd);
            rt_mutex_release(s_ptMutexFile);
            return;
        }

        endLen = s_atUploadFile.uFileTotalLen % UPLOAD_DATA_FRAME_LEN;
        if (endLen == 0 || ptTransFile->wFN2 < s_atUploadFile.wFileTotalFrame)
        {
            readLen = rt_read_s(fd, ptTransFile->aucData, sizeof(ptTransFile->aucData), UPLOAD_DATA_FRAME_LEN);
            if (readLen < 0)
            {
                ptTransFile->ucRtnCode = 2;
                close(fd);
                rt_mutex_release(s_ptMutexFile);
                return;
            }
            s_readFileLseek += readLen;
        }
        else
        {
            readLen = rt_read_s(fd, ptTransFile->aucData, sizeof(ptTransFile->aucData), endLen);
            if (readLen < 0)
            {
                ptTransFile->ucRtnCode = 2;
                close(fd);
                rt_mutex_release(s_ptMutexFile);
                return;
            }
            s_readFileLseek += readLen;
        }
        close(fd);
        rt_mutex_release(s_ptMutexFile);
        //响应文件数据
        offset = 0;
        s_aucBufTemp[offset] = ptTransFile->ucHeader;
        offset += 1;

        s_aucBufTemp[offset] = ptTransFile->ucFlag;
        offset += 1;

        PutInt16ToBuff(&s_aucBufTemp[offset], ptTransFile->wAddr);
        offset += 2;

        PutInt16ToBuff(&s_aucBufTemp[offset], ptTransFile->wReserved);
        offset += 2;

        PutInt16ToBuff(&s_aucBufTemp[offset], readLen);
        offset += 2;

        PutInt16ToBuff(&s_aucBufTemp[offset], ptTransFile->wFN1);
        offset += 2;

        PutInt16ToBuff(&s_aucBufTemp[offset], ptTransFile->wFN2);
        offset += 2;

        s_aucBufTemp[offset] = ptTransFile->ucDevice;
        offset += 1;

        s_aucBufTemp[offset] = ptTransFile->ucCID;
        offset += 1;

        s_aucBufTemp[offset] = ptTransFile->ucRtnCode;
        offset += 1;

        rt_memcpy_s(&s_aucBufTemp[offset], sizeof(s_aucBufTemp)-offset, ptTransFile->aucData, readLen);
        offset += readLen;

        if (offset <= REMOTE_COMM_BUFF_LEN) {
            ptTransFile->wCRC = Calculate_CRC16(&s_aucBufTemp[1], offset-1);
        }
        PutInt16ToBuff(&s_aucBufTemp[offset], ptTransFile->wCRC);
        offset += 2;
           
        if (offset < REMOTE_COMM_BUFF_LEN) {
            s_aucBufTemp[offset] = ptTransFile->ucEndCode;
        }
        offset += 1;
    }

    /* 发送前转码 */
    int packBuflen = 0;
    if (offset <= REMOTE_COMM_BUFF_LEN) {
        packBuflen = DataPackShift((char*)s_aucBufTemp, offset, (char*)pucSendBuf);
    }
    *pwSendBufLen = packBuflen;

    /* 通过包序号判断文件传输结束 */
    if (ptTransFile->wFN2 == s_atUploadFile.wFileTotalFrame)
    {
        s_readFileLseek = 0;
        SetRemoteCommFlag(False); //退出文件导出协议
    }

    return;
}

/****************************************************************************
* 函数名称：PackFrameProcess()
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：响应文件上传命令
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
void PackFrameProcess(char *pucSendBuf, WORD *pwSendBuflen, T_RemoteTransStruct *ptTransFile)
{
    if (HISTORY_GET_FILELIST == ptTransFile->ucCID)
    {
        /* 获取文件列表 */
        GetFileList(pucSendBuf, pwSendBuflen, ptTransFile);
    }

    if (HISTORY_GET_DATA == ptTransFile->ucCID)
    {
        /* 获取文件数据 */
        GetFileData(pucSendBuf, pwSendBuflen, ptTransFile);
    }
}

/****************************************************************************
* 函数名称：RemoteCommProtDeal()
* 调    用：无
* 被 调 用：
* 输入参数：pucRecvBuf  接收串口数据
            wRecvBufLen 接收数据长度
            pucSendBuf  最终响应上位机的数据
            wSendBufLen 最终响应上位机数据长度
            ptTransFile 文件传输结构体
* 返 回 值：
* 功能描述：处理文件上传命令
* 作    者：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
static BOOLEAN RemoteCommProtDeal(char *pucRecvBuf, WORD wRecvBufLen, char *pucSendBuf, WORD *wSendBufLen, T_RemoteTransStruct *ptTransFile, char *pucConvertBuf)
{
    char ucTmp = 0;

    /* 解析/命令 */
    ucTmp = ParseFrameProcess(pucRecvBuf, wRecvBufLen, ptTransFile, pucConvertBuf);
    if (SUCCESSFUL != ucTmp)
    {
        return FAILURE;
    }

    /* 组包/响应 */
    PackFrameProcess(pucSendBuf, wSendBufLen, ptTransFile);
    return SUCCESSFUL;
}

/****************************************************************************
*获取文件上传协议标志
***************************************************************************/
BOOLEAN GetRemoteCommFlag(void)
{
    return s_remoteCommFlag;
}

/****************************************************************************
*设置设置文件上传协议标志
***************************************************************************/
void SetRemoteCommFlag(BOOLEAN bFlag)
{
    s_remoteCommFlag = bFlag;
}

/****************************************************************************
* 函数名称：DealHisBackupRecord()
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：处理文件上传协议命令
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
static BOOLEAN DealRemoteCommData(T_CommStruct *ptComm)
{
    BYTE ucTmp = 0;
    UINT32 uBufLen = 0;
    WORD wCalCrc = 0;
    WORD wRecvCrc = 0;
    BYTE ucConvertBuf[REMOTE_COMM_BUFF_LEN] = {0}; //转码后缓存

    if ((ptComm->aucRecBuf[0] != REMOTE_COMM_STANDARD_SOI) || (ptComm->aucRecBuf[ptComm->wRecLength - 1] != REMOTE_COMM_STANDARD_EOI))
    {
        return FAILURE;
    }

    /* 转码 */
    uBufLen = DataParseShift((char*)ptComm->aucRecBuf, ptComm->wRecLength, (char*)ucConvertBuf);
    if ((uBufLen < 4) || (uBufLen > REMOTE_COMM_BUFF_LEN))
    {
        return FAILURE;
    }

    /* 触发帧 */
    if (SUCCESSFUL == ParseTrgFrame((char*)ptComm->aucRecBuf, ptComm->wRecLength, &s_tTrgFrame, (char*)ucConvertBuf))
    {
        s_readListLseek = 0;
        s_readFileLseek = 0;  //触发成功表示重新获取列表和文件，读偏移清空
        
        ClearRecBuf(ptComm);
        rt_memcpy(ptComm->aucSendBuf, &s_tTrgFrame, sizeof(s_tTrgFrame));
        ptComm->wSendLength = sizeof(s_tTrgFrame);
        sendCommData(ptComm);
        SetRemoteCommFlag(True);
        DealDeviceUnlock(AUTO_UNLOCK);        
        return SUCCESSFUL;
    }

    /* 数据帧 */
    if (True == GetRemoteCommFlag())
    {
        wCalCrc = Calculate_CRC16(&ucConvertBuf[1], uBufLen-4);
        wRecvCrc = get_int16_data((const unsigned char*)&ucConvertBuf[uBufLen-3]);
        if(wCalCrc != wRecvCrc)
        {
            return FAILURE;
        }

        if ((ucConvertBuf[1] != REMOTE_COMM_FILE_DOWNLOAD) && (ucConvertBuf[1] != REMOTE_COMM_FILE_UPLOAD ))
        {
            return FAILURE;
        }

        rt_memset_s(ptComm->aucSendBuf, sizeof(ptComm->aucSendBuf), 0, sizeof(ptComm->aucSendBuf));
        rt_memset_s(&s_tTransFileData, sizeof(T_RemoteTransStruct), 0, sizeof(T_RemoteTransStruct));
        ucTmp = RemoteCommProtDeal((char*)ptComm->aucRecBuf, ptComm->wRecLength, (char*)ptComm->aucSendBuf, &ptComm->wSendLength, &s_tTransFileData, (char*)ucConvertBuf);
        ClearRecBuf(ptComm);
        if (SUCCESSFUL == ucTmp)
        {
            sendCommData(ptComm);
            DealDeviceUnlock(AUTO_UNLOCK);
            return SUCCESSFUL;
        }
    }

    return FAILURE;
}  

/***************************************************************************
 * @brief    删除协议触发后开启的定时器
 * @return   {*}
 **************************************************************************/
void DeleteTrigTimer(void)
{
	if(timer3MinOneShot != NULL)
	{
		rt_timer_stop(timer3MinOneShot);
	}
}

/***************************************************************************
 * @brief    重置QTP触发次数计数器
 * @return   {*}
 **************************************************************************/
BYTE GetQtpTrigger()
{
    s_ucQtpTestTriggerRepeat = 0;
    return s_ucQtpTestTriggerRepeat;
}

/***************************************************************************
 * @brief    重置QTP P2P触发次数计数器
 * @return   {*}
 **************************************************************************/
BYTE GetQtpTestTriggerRepeat()
{
    s_ucQtpP2PTestTriggerRepeat = 0;
    return s_ucQtpP2PTestTriggerRepeat;
}

static BYTE JudgeProtoVerAndCID1_1()
{
    if (s_tProtocol.ucVer == PROTOCOL_VER_24 && s_tProtocol.ucCID1 == CID1_QTP)
    {
    }
    else if (s_tProtocol.ucVer == PROTOCOL_VER_25 && s_tProtocol.ucCID1 == CID1_1104)
    {
    }
    else if (s_tProtocol.ucVer == PROTOCOL_VER_DEYE && s_tProtocol.ucCID1 == CID1_DEYE)
    {
    }
    else
    {
        return NO_RETURN;
    }

    return RTN_CORRECT;

}

static BYTE JudgeProtoVerAndCID1()
{
    if (s_tProtocol.ucVer == PROTOCOL_VER_22 && s_tProtocol.ucCID1 == CID1_BMS)
    {
    }
    else if(s_tProtocol.ucVer == PROTOCOL_VER_20 && s_tProtocol.ucCID1 == CID1_TOWER)
    {
    }
    #ifndef DEVICE_USING_R321
    else if (s_tProtocol.ucVer == PROTOCOL_VER_20 && s_tProtocol.ucCID1 == CID1_PAD)
    {
    }
    #endif
    else if (s_tProtocol.ucVer == PROTOCOL_VER_21 && s_tProtocol.ucCID1 == CID1_COMMON &&
                s_tProtocol.ucCID2 == 0xFF) // 按地址的新触发帧
    {
    }
    else if (RTN_CORRECT == JudgeProtoVerAndCID1_1())
    {
    }
    else
    {
        return NO_RETURN;
    }

    return RTN_CORRECT;
}

/****************************************************************************
 * 函数名称：GetRTN()
 * 调    用：无
 * 被 调 用：
 * 输入参数：ucPort-串口号
 * 返 回 值：
 * 功能描述：判断响应数据包的RTN值
 * 作    者  ：王威
 * 版本信息：V1.0
 * 设计日期：2010-06-13
 * 修改记录：
 * 日    期      版  本      修改人      修改摘要
 ***************************************************************************/
static BYTE GetRTN(T_CommStruct *ptComm)
{
    WORD wChkSum = 1;
    WORD wAddUp = 0;
    WORD i, j;
    BYTE ucLChkSum;

    GetSysPara(&s_tSysPara);

    s_tProtocol.ucLCHKSUM = (s_tProtocol.aucRecBuf[5] >> 4) & 0x0F;
    s_tProtocol.aucRecBuf[5] = s_tProtocol.aucRecBuf[5] & 0x0F;
    s_tProtocol.wRecLenid = (WORD)GetInt16Data(&s_tProtocol.aucRecBuf[5]);

    /* 累加和判断 */
    wChkSum = (WORD)GetInt16Data(&s_tProtocol.aucRecBuf[7 + s_tProtocol.wRecLenid / 2]);

    /* CHKSUM的计算是除SOI、EOI和CHKSUM外，其它字符按ASCII码值累加求和，所得结果模65536余数取反加1 */
    j = 2 * 6 + 1 + s_tProtocol.wRecLenid;
    for (i = 1; i < j; i++)
    {
        wAddUp += ptComm->aucRecBuf[i];
    }
    wAddUp = 0 - wAddUp;

    /* 获取数据 */
    s_tProtocol.ucVer = s_tProtocol.aucRecBuf[1];
    s_tProtocol.ucAddr = s_tProtocol.aucRecBuf[2];
    s_tProtocol.ucCID1 = s_tProtocol.aucRecBuf[3];
    s_tProtocol.ucCID2 = s_tProtocol.aucRecBuf[4];

    //处理50AH和100AH并联时的数据处理
    s_tProtocol.ucAddrBak = s_tProtocol.ucAddr;

    if ((JudgeAPPTsetNoReturn(0) == True) || (Judge1104NoReturn(0) == True) || JudgeQTPTsetNoReturn(0) == True)
    {
        return NO_RETURN;
    }

    //增加中试命令处理
    if (GetApptestFlag())
    {
        if (s_tProtocol.ucVer == PROTOCOL_VER_21 &&
            ((s_tProtocol.ucCID1 == CID1_COMMON) || (s_tProtocol.ucCID1 == CID1_DC)))
        {
        }
        else
        {
            return NO_RETURN;
        }
    }
    else if (RTN_CORRECT != JudgeProtoVerAndCID1())
    {
        return NO_RETURN;
    }
    RETURN_VAL_IF_FAIL(wAddUp == wChkSum, RTN_WRONG_CHKSUM);

    /* 长度校验和判断，若错误返回长度校验错 */
    ucLChkSum = (s_tProtocol.aucRecBuf[5] & 0x0F) + (s_tProtocol.aucRecBuf[6] & 0x0F) +
                ((s_tProtocol.aucRecBuf[6] & 0xF0) >> 4);
    if ((s_tProtocol.wOriginLength != s_tProtocol.wRecLenid + 18) ||
        (((16 - ucLChkSum) & 0x0F) != s_tProtocol.ucLCHKSUM))
    {
        return RTN_WRONG_LCHKSUM; /* 长度校验错 */
    }

    /* 检查命令格式是否错，有返回RTN_WRONG_COMMAND，否则返回RTN_CORRECT */
    return RTN_CORRECT;
}

/****************************************************************************
* 函数名称：JudgeNoReturn(BYTE   ucPort)
* 调    用：无
* 被 调 用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：判断APPTEST响应数据包的是否是不用响应的帧（为了降低CCN）
* 作    者  ：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
static BOOLEAN JudgeAPPTsetNoReturn(BYTE ucPort)
{
    /* APPTEST若不是获取地址或设置地址，并且地址错误，则不返回（总线方式） */
    // 或者三次触发后，再发触发帧不响应
    if (GetApptestFlag() &&
        ((s_tProtocol.ucAddr != GetTotalAddr()) || (s_tProtocol.ucCID1 == 0x00 && s_tProtocol.ucCID2 == 0x00)))
    {
        return True;
    }
    return False;
}

#ifdef PAD_SELF_DEVELOP_PROTO_OFFSET_ADDR
static BOOLEAN GetSelfDevProtocolOffsetAddr(void)
{
    GetSysPara(&s_tSysPara);
    return s_tSysPara.fSelfDevelopProtocolOffsetAddr;
}
#endif

/*************************************************
* 德业版本：地址和普通版本映射关系：1->02H、2->12H、3->22H...10->92H 
* 新增德业测试模式参数后放开
*************************************************/
// BYTE GetBMSDLAddr(void)
// {
//     if (True == IsDeyeTestMode() && (0 < s_tMyBms.ucAddress))
//     {
//         return (s_tMyBms.ucAddress * 16 - 14); //德业测试版本
//     }
//     else
//     {
//         #ifdef PAD_SELF_DEVELOP_PROTO_OFFSET_ADDR
//             BYTE bOffSetAddr = 0;
//             bOffSetAddr = GetSelfDevProtocolOffsetAddr();
//             return GetTotalAddr() + bOffSetAddr + 0x0F;
//         #else
//             return GetTotalAddr();
//         #endif
//     }
// }

BYTE GetBMSDLAddr(void)
{
#ifdef PAD_SELF_DEVELOP_PROTO_OFFSET_ADDR
    BYTE bOffSetAddr = 0;
    bOffSetAddr = GetSelfDevProtocolOffsetAddr();
    return GetTotalAddr() + bOffSetAddr + 0x0F;
#else
    return GetTotalAddr();
#endif
}

BYTE GetCanBMSDLAddr(void)
{
#ifdef PAD_SELF_DEVELOP_PROTO_OFFSET_ADDR
    BYTE bOffSetAddr = 0;
    bOffSetAddr = GetSelfDevProtocolOffsetAddr();
    return GetTotalAddr() + bOffSetAddr;
#else
    return GetTotalAddr();
#endif
}

/****************************************************************************
* 函数名称：JudgeNoReturn(BYTE   ucPort)
* 调    用：无
* 被 调 用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：判断1363响应数据包的是否是不用响应的帧（为了降低CCN）
* 作    者  ：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
static BOOLEAN Judge1104NoReturn(BYTE ucPort)
{
    BOOLEAN bOffSetAddr = 0;
#ifdef PAD_SELF_DEVELOP_PROTO_OFFSET_ADDR
    bOffSetAddr = GetSelfDevProtocolOffsetAddr();
#endif
    if (GetApptestFlag())
    {
        return False;
    }
    else if ((s_tProtocol.ucAddr == GetTotalAddr() && (s_tProtocol.ucVer != PROTOCOL_VER_20 || s_tProtocol.ucCID1 != CID1_PAD))
        || s_tProtocol.ucAddr == getGrpAddr()
        || ((s_tProtocol.ucAddr == GetTotalAddr() + bOffSetAddr + 0x0F) && s_tProtocol.ucVer == PROTOCOL_VER_20 && s_tProtocol.ucCID1 == CID1_PAD))
    {
        return False;
    }

    return True;
}

/****************************************************************************
* 函数名称：JudgeNoReturn(BYTE   ucPort)
* 调    用：无
* 被 调 用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：判断QTPTEST响应数据包的是否是不用响应的帧（为了降低CCN）
* 作    者  ：
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
static BOOLEAN JudgeQTPTsetNoReturn(BYTE ucPort)
{
    /* QTPTEST若不是获取地址或设置地址，并且地址错误，则不返回（总线方式） */
    // 或者三次触发后，再发触发帧不响应
    if (GetQtptestFlag() &&
        ((s_tProtocol.ucAddr != GetTotalAddr()) || (s_tProtocol.ucCID1 == 0x00 && s_tProtocol.ucCID2 == 0x00)))
    {
        return True;
    }
    return False;
}

/****************************************************************************
 * 函数名称：DealCommand()
 * 调    用：无
 * 被 调 用：
 * 输入参数：ucPort-串口号
 * 返 回 值：
 * 功能描述：根据CID1和CID2，调用相应的命令处理程序
 * 作    者  ：王威
 * 版本信息：V1.0
 * 设计日期：2010-06-13
 * 修改记录：
 * 日    期      版  本      修改人      修改摘要
 ***************************************************************************/
#ifdef DEVICE_USING_R321

/***************************************************************************
 * @brief    是否为获取记录的指令
 * @return   BOOLEAN
 **************************************************************************/
static BOOLEAN isGetRecordCommand(void)
{
    if((s_tProtocol.ucCID2 == GET_BMS_HISDATA_TIME) 
        || (s_tProtocol.ucCID2 == GET_BMS_HISOPERA_TIME) 
        || (s_tProtocol.ucCID2 == GET_BMS_HISALM_TIME) 
        || (s_tProtocol.ucCID2 == GET_DCR_DATA_TIME)
        ||(s_tProtocol.ucCID2 == SET_DATE_TEMPLATE)
        ||(s_tProtocol.ucCID2 == SET_PEAK_SHIFT_PARA)  //1363获取智能错峰模板和智能错峰参数
        || (s_tProtocol.ucCID2 == GET_BMS_BALANCE_CAPACITY)) //1363获取均衡容量记录
    {
        return True;
    }
    return False;
}

static BYTE IsVariableDeyeCID2Command(void)
{
    if ((s_tProtocol.ucCID2 == GET_SYS_INFO_DEYE) 
        ||(s_tProtocol.ucCID2 == GET_ANALOG_DEYE)
        ||(s_tProtocol.ucCID2 == GET_ALARM_DEYE)
        ||(s_tProtocol.ucCID2 == GET_BATT_MANAGE_DEYE)
        ||(s_tProtocol.ucCID2 == SET_SHUTDOWN_DEYE))
    {
        return True;
    }
    return False;
}

static BYTE IsVariablelengthCommand(void) {
    if (((s_tProtocol.ucCID2 == SET_INPUT_RELAY ) && (s_tProtocol.wRecLenid == 4 || s_tProtocol.wRecLenid == 42))
        ||(s_tProtocol.ucCID2 == SET_SPECIAL_PARA)
        ||(s_tProtocol.ucCID2 == SET_BMS_FAC_SPEC_INFO)
        ||(s_tProtocol.ucCID2 == SET_CELL_FACT_INFO)
        ||(s_tProtocol.ucCID2 == SET_BMS_CELL_FACT)  //1363设置电芯厂家
        ||(s_tProtocol.ucCID2 == SET_BMS_PARA_NEW)   //1363设置新增参数
        ||(s_tProtocol.ucCID2 == SET_BMS_SPEPARA_NEW)//1363设置新增特殊数据
        ||(s_tProtocol.ucCID2 == SET_BMS_NEW_ALM_PARA)
        ||(s_tProtocol.ucCID2 == SET_GPS_INIT_INIO)
        || isGetRecordCommand()
        || IsVariablelengthCommand2()
        || IsVariableDeyeCID2Command())
    {
        return True;
    }
    return False;
}

Static BYTE IsVariablelengthCommand2(void)
{
    if ((s_tProtocol.ucCID2 == GET_BMS_HISOPERA_TIME)
        ||(s_tProtocol.ucCID2 == SET_BMS_NET_NEW) //1363设置网络相关参数
        ||(s_tProtocol.ucCID2 == GET_BMS_HISALM_TIME)
        ||(s_tProtocol.ucCID2 == SET_BMS_SPECIAL_PARA_NEW)
        ||(s_tProtocol.ucCID2 == SET_BMS_INTERNAL_PARA)  //1363设置内部测试参数 
    #ifdef INTELLIGENT_PEAK_SHIFTING
        ||(s_tProtocol.ucCID2 == SET_PEAK_SHIFT_PARA) //1363设置错峰参数
        ||(s_tProtocol.ucCID2 == SET_DATE_TEMPLATE && s_tProtocol.wRecLenid <= REC_DATE_TEMP_DATA_MAXLEN)
    #endif
        ||(s_tProtocol.ucCID2 == SITE_ANTITHEFT_CTRL)
        ||(s_tProtocol.ucCID2 == NET_ANTITHEFT_CTRL)
        ||(s_tProtocol.ucCID2 == NET_PEAKSHIFTTEMPLATE_FORWARD))
    {
        return True;
    }
    return False;
}

static void DealCommand(BYTE ucComType)
{
    WORD i = 0;
    const T_CmdFuncStruct *s_atCID2AllFuncTable;
    
    if (PROTOCOL_VER_DEYE == s_tProtocol.ucVer)
    {
        s_atCID2AllFuncTable = GetCmdFnucStruct(CID2DEYE);
    }
    else
    {
        s_atCID2AllFuncTable = GetCmdFnucStruct(CID2ALL);
    }

    for (i = 0; i < HIGH_SEND_LEN; i++) /* 清空响应缓冲区 */
    {
        s_tProtocol.aucSendBuf[i] = 0;
    }
    /* 将响应包的LENID预置为0 */
    s_tProtocol.wSendLenid = 0;

    i = 0;
    while (s_atCID2AllFuncTable != NULL && s_atCID2AllFuncTable[i].ucCmdCode)
    {
        if (s_atCID2AllFuncTable[i].ucCmdCode == s_tProtocol.ucCID2)
        {
            if (s_atCID2AllFuncTable[i].ucDataLen != s_tProtocol.wRecLenid)
            {
                if (IsVariablelengthCommand())
                {
                }
                else
                {
                    s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
                    return;
                }
            }
            (*s_atCID2AllFuncTable[i].func)(ucComType);
            DealDeviceUnlock(AUTO_UNLOCK);
            return;
        }
        i++;
    }

    i = 0;
    while (s_atCID1AllFuncTable[i].ucCmdCode)
    {
        if (s_atCID1AllFuncTable[i].ucCmdCode == s_tProtocol.ucCID1)
        {
            (*s_atCID1AllFuncTable[i].func)(ucComType);
            return;
        }
        i++;
    }

    // CID2错，返回RTN_WRONG_CID2
    s_tProtocol.ucRTN = RTN_WRONG_CID2;

    return;
}
#endif
static void InitBattParaAndStatus(void)
{
	T_DCPara tDCPara;
    rt_memset_s( &tDCPara, sizeof(T_DCPara), 0, sizeof(T_DCPara));
    Button_Mode_Set(BUT_NORMAL);
	Open_IC_Onece(TRUE); //开启IC
    SetPMOSStatus(TRUE); //开启PMOS

    BattThreadMutexTake(1000); // apptest初始化参数，使用电池管理线程互斥锁，避免多线程参数设置失败
    // tDCPara.wBusOverVolVal = 6200;                 // 母排过压保护阈值，精度2（6200）
#ifdef MONITORING_BASED_CHARGE_AND_DISCHARGE_OVERCURRENT_PROTECTION_ENABLED
    tDCPara.wChgOverCurVal = CHARGE_OVER_CURR;     // 母排充电过流保护阈值，精度2 （3000）
#endif
    tDCPara.wBatOverVolVal = BATTERY_OVER_VOLT;    // 电池组过压保护阈值，精度2（2922）
    tDCPara.wDropVol       = BUS_DROP_VOLT;        // 母排掉电阈值，精度2 （4500）
    tDCPara.wChgCurVal     = CHARGE_LIMIT_CURR;    // 当前电池侧充电限电流，千分比（1000）
    tDCPara.wChgBusCurVal  = CHARGE_LIMIT_CURR;    // 当前Bus侧充电限电流，千分比（1000）
    tDCPara.wDisChgCurVal  = DISCHARGE_LIMIT_CURR; // 当前放电限电流，千分比（1000）
    tDCPara.wChgVolVal     = CHARGE_VOLT;          // 当前充电电压，精度2（2800）
    tDCPara.wDischgVolVal  = DISCHARGE_VOLT;       // 当前放电电压，精度2（4500）

#ifdef MAX_CHARGE_AND_DISCHARGE_POWER_ENABLED
    tDCPara.wMaxChgPowerVal = MAX_CHARGE_POWER;         // 最大充电功率，精度0
    tDCPara.wMaxDisChgPowerVal = MAX_DISCHARGE_POWER;   // 最大放电功率，精度0
#endif
    SetBduPara(&tDCPara);
    BduCtrl(SCI_CTRL_CHG2CHG, DISABLE);    //充电使能禁止
    BduCtrl(SCI_CTRL_CHG_STG, DISABLE);    //非直通
    BduCtrl(SCI_CTRL_DISCHG_STG, DISABLE); //非直通
    BduCtrl(SCI_CTRL_CHG_PRT, ENABLE);     //充电保护（禁止）
    BduCtrl(SCI_CTRL_DISCHG_PRT, ENABLE);  //放电保护（禁止）
    BduCtrl(SCI_CTRL_SLEEP , DISABLE);     //非休眠
    BduCtrl(SCI_CTRL_TEST , ENABLE);       //apptest测试模式
    BattThreadMutexRelease();
}
/***************************************************************************
 * @brief    处理APPTEST 第二种触发方式（P2P触发）
 * @return   {*}
 **************************************************************************/
static void DealP2PTrigger()
{
    BYTE i = 0;
    T_CtrlOutStruct tCtrlOut;

    GetCtrlOut(&tCtrlOut);
#ifdef POWER_COMM_FAIL_ALARM
    SetPowerCommShieldFlag(True);
#endif

    rt_memset_s(&s_tProtocol.aucSendBuf[0], sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));
    /* 将响应包的LENID预置为0 */
    s_tProtocol.wSendLenid = 0;
            
    s_tProtocol.aucSendBuf[0] = 0;

    /* LENID */
    s_tProtocol.wSendLenid = 2;
    s_wApptestTriggerTimeOut = 0;
    s_ucP2PTestTriggerRepeat++;

    if (GetApptestFlag() == False)
    {
        if (s_ucP2PTestTriggerRepeat >= TRIGGER_COUNTER)
        {
            s_tProtocol.aucSendBuf[0] = 1;
            SetApptestFlag(TRUE);
            SaveAction(GetActionId(CONTOL_ENTER_APPTEST),"Enter Apptest");
            InitBattParaAndStatus();
            ClearCellDamagePrt();  //清除单体损坏保护

            for (i = 0; i < 6; i++) // 立即将所有的指示灯点亮。
            {
                tCtrlOut.bLed[i] = LEDON;
            }
            tCtrlOut.bBuzz = FALSE;
            SetCtrlOut(&tCtrlOut);

            if (timer3MinOneShot == NULL)
            {
                timer3MinOneShot = rt_timer_create("timer_3Min_OneShot", SetLedByAddr, RT_NULL,
                                                   3 * (ONE_MINUTE * 10), // F231时钟节拍1ms
                                                   RT_TIMER_FLAG_ONE_SHOT | RT_TIMER_FLAG_SOFT_TIMER);
            }
            if (timer3MinOneShot != NULL)
            {
                // 触发单次定时器后在退出apptest协议处要删除该定时器
                rt_timer_start(timer3MinOneShot);
            }

            ClearBtnCount(); //清除按键次数，重新计数
        }
        return;
    }

    else
    {
        s_tProtocol.aucSendBuf[0] = 1;
    }

    return;
}

/****************************************************************************
* 函数名称：DealBMSCommand()
* 调    用：无
* 被 调 用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：处理交流屏的命令(实际无作用)
* 作    者  ：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
static void DealBMSCommand(BYTE ucPort)
{
    BYTE i = 0;
    const T_CmdFuncStruct *s_atCID2BMSFuncTable = GetCmdFnucStruct(CID2BMS);
    while (s_atCID2BMSFuncTable != NULL && s_atCID2BMSFuncTable[i].ucCmdCode)
    {
        if (s_atCID2BMSFuncTable[i].ucCmdCode == s_tProtocol.ucCID2)
        {
            if (s_atCID2BMSFuncTable[i].ucDataLen != s_tProtocol.wRecLenid)
            {
                s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
                return;
            }
            (*s_atCID2BMSFuncTable[i].func)(ucPort);
            return;
        }
        i++;
    }
    s_tProtocol.ucRTN = RTN_WRONG_CID2; /* CID2无效 */

    return;
}

/****************************************************************************
* 函数名称：ReadySendData()
* 被 调 用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：将协议缓冲区的数据打包后放入底层发送缓冲区
* 作    者  ：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
static void ReadySendData(T_CommStruct *ptComm)
{
    CHAR cCh1;
    WORD i;
    WORD wSum;
    BYTE *p;
    BYTE aucTmp[HIGH_SEND_LEN];

    GetSysPara(&s_tSysPara);

    p = ptComm->aucSendBuf;

    p[0] = SOI; /* SOI */
    p++;

    aucTmp[0] = s_tProtocol.ucVer;  /* VER */
    aucTmp[1] = s_tProtocol.ucAddr; /* ADR */
    aucTmp[2] = s_tProtocol.ucCID1; /* CID1 */
    aucTmp[3] = s_tProtocol.ucRTN;  /* RTN */

    /* LENGTH=LCHKSUM+LENID */
    *(WORD *)&aucTmp[4] = s_tProtocol.wSendLenid;
    ExchangeIntHighLowByte(&aucTmp[4]);
    cCh1 = aucTmp[5] & 0x0F;
    cCh1 += (aucTmp[5] >> 4);
    cCh1 += (aucTmp[4] & 0x0F);
    cCh1 &= 0x0F;
    cCh1 = 16 - cCh1;
    aucTmp[4] += cCh1 * 16;

    /* INFO */
    rt_memcpy(&aucTmp[6], s_tProtocol.aucSendBuf, s_tProtocol.wSendLenid / 2);

    /* 16进制转ASCII码 */
    for (i = 0; i < 6 + s_tProtocol.wSendLenid / 2; i++)
    {
        if (i >= 6 && s_tProtocol.aucInvalidFlag[i-6] != 0)
        {
            *p++ = 0x20;
            *p++ = 0x20;
        }
        else
        {
            *p++ = HexToASCII(aucTmp[i] >> 4);
            *p++ = HexToASCII(aucTmp[i] & 0x0F);
        }
    }

    /* CHKSUM */
    wSum = 0;
    for (i = 1; i <= s_tProtocol.wSendLenid + 12; i++)
    {
        wSum += ptComm->aucSendBuf[i];
    }
    wSum = (~wSum) + 1;
    for (i = 0; i < 4; i++)
    {
        *p++ = HexToASCII((wSum >> (12 - i * 4)) & 0x0F);
    }

    *p = EOI; /* EOI */

    /* 置底层发送缓冲区头、尾指针 */
    ptComm->wSendLength = 18 + s_tProtocol.wSendLenid;

    if ((COMM_CAN != ptComm->ucPortType)&&(COMM_NET != ptComm->ucPortType))
    {
        sendCommData(ptComm);
    }

    return;
}


INT16S EncodeToYd1363Frame(void *dest, WORD destlen, T_Yd1363FrameHead tFrameHead, const void* info, WORD infolen)
{
    WORD lenid = 0;
    UINT32 encodedFrameLen = 0;
    WORD wChecksum = 0;

    if (dest == NULL || (infolen > 0 && info == NULL) || infolen > YD1363_INFO_LEN_MAX)
    {
        return -1;
    }

    lenid = infolen * 2;
    encodedFrameLen = lenid + YD1363_FRAME_LEN_MIN;
    if (destlen < encodedFrameLen)
    {
        return -1;
    }

    BYTE ucLenChecksum = 0;
    ucLenChecksum = (lenid & 0x000F) + ((lenid >> 4) & 0x000F) + ((lenid >> 8) & 0x000F);
    ucLenChecksum = 16 - (ucLenChecksum & 0x0F);

    BYTE *pucOut = dest;
    *pucOut++ = SOI;

    BYTE *p = (BYTE *)&tFrameHead;
    for (BYTE i = 0; i < 4; i++)
    {
        *pucOut++ = HexToASCII(p[i] >> 4);
        *pucOut++ = HexToASCII(p[i] & 0x0F);
    }

    *pucOut++ = HexToASCII(ucLenChecksum & 0x0F);

    for (BYTE i = 0; i < 3; i++)
    {
        *pucOut++ = HexToASCII((lenid >> (8 - i * 4)) & 0x0F);
    }

    p = (BYTE *)info;
    for (WORD i = 0; i < infolen; i++)
    {
        *pucOut++ = HexToASCII(p[i] >> 4);
        *pucOut++ = HexToASCII(p[i] & 0x0F);
    }
    for (BYTE *pIn = dest + 1; pIn < pucOut; pIn++)
    {
        wChecksum += *pIn;
    }
    wChecksum = (~wChecksum) + 1;
    for (BYTE i = 0; i < 4; i++)
    {
        *pucOut++ = HexToASCII((wChecksum >> (12 - i * 4)) & 0x0F);
    }

    *pucOut++ = EOI;

    return (WORD)(pucOut - (BYTE *)dest);
}


/***************************************************************************
 * @brief    提取出一条1104协议
 * @param    {BYTE const*} pucSrc
 * @param    {BYTE*} pucBuff
 * @param    {SHORT} sLen
 * @return   {*}
 **************************************************************************/
WORD Get1363DataFromCache(BYTE const *pucSrc, BYTE *pucBuff, SHORT sLen)
{
    WORD i = 0;

    if (NULL == pucSrc || NULL == pucBuff || sLen < 0)
    {
        return 0;
    }

    for (i = 0; i < sLen; i++)
    {
        pucBuff[i] = pucSrc[i];
        if (EOI == pucSrc[i])
        {
            return (i + 1);
        }
    }

    return 0;
}

/****************************************************************************
 * 函数名称：InitProtocolData()
 * 调    用：
 * 被 调 用：在InitVariable()中调用
 * 输入参数：无
 * 返 回 值：无
 * 功能描述：初始化通讯协议数据
 * 作    者  ：王威
 * 版本信息：V1.0
 * 设计日期：2010-06-13
 * 修改记录：
 * 日    期      版  本      修改人      修改摘要
 ***************************************************************************/
void InitProtocolData(void)
{
    rt_memset_s((BYTE *)&s_tProtocol, sizeof(s_tProtocol), 0, sizeof(s_tProtocol));
    return;
}

void InitApptestData(void)
{
    s_ucTestTriggerRepeat = 0;
    s_ucP2PTestTriggerRepeat = 0;
    s_wApptestTriggerTimeOut = 0;
    SetApptestFlag(False);

    s_ucQtpTestTriggerRepeat = 0;
    s_ucQtpP2PTestTriggerRepeat = 0;
    s_wQtptestTriggerTimeOut = 0;
    SetQtptestFlag(False);

    return;
}

static void DealApptestRtnAbnormal(void)
{
    if (RTN_CORRECT != s_tProtocol.ucRTN)
    {
        s_tProtocol.ucRTN = NO_RETURN;
    }

    return;
}

/***************************************************************************
 * @brief    获取时间信息（F231）
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
void SendSysTime(BYTE ucPort)
{
    BYTE *p = s_tProtocol.aucSendBuf;
    struct tm tTime;
    time_t t = time(RT_NULL);

    localtime_r(&t, &tTime);
    PutInt16ToBuff(p, tTime.tm_year + 1900);
    p += 2;
    *p++ = tTime.tm_mon + 1;
    *p++ = tTime.tm_mday;
    *p++ = tTime.tm_hour;
    *p++ = tTime.tm_min;
    *p++ = tTime.tm_sec;

    /* LENID */
    s_tProtocol.wSendLenid = 14;

    return;
}


void GetSysTimeQTP(time_t t)
{
    BYTE *p = s_tProtocol.aucSendBuf;
    struct tm tTime;

    localtime_r(&t, &tTime);

    // Write year, month, day, hour, minute, and second to the buffer
    PutInt16ToBuff(p, tTime.tm_year + 1900);
    p += 2;
    *p++ = tTime.tm_mon + 1;
    *p++ = tTime.tm_mday;
    *p++ = tTime.tm_hour;
    *p++ = tTime.tm_min;
    *p++ = tTime.tm_sec;

    // Set the length identifier
    s_tProtocol.wSendLenid = 14;
}


/***************************************************************************
 * @brief    设置时间信息（F231）
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
void RecSysTime(BYTE ucPort)
{
    static Datebuff tTime;
    BYTE *p = s_tProtocol.aucRecBuf + 7;
    BYTE buff[21] = {0};
    T_DateStruct tDate;
    struct tm tTimeBefore;
    time_t t = time(RT_NULL);

    rt_memset_s(&tDate, sizeof(tDate), 0x00, sizeof(tDate));
    rt_memset_s(&tTimeBefore, sizeof(tTimeBefore), 0x00, sizeof(tTimeBefore));

    localtime_r(&t, &tTimeBefore);

    /* COMMAND_TIME */
    /* 判断时间的有效性，若有效则设置系统时间 */
    if (p[0] * 256 + p[1] < 1900)
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
        return;
    }
    
    tTime.PFC8563_Date.year = p[0] * 256 + p[1] - 1900;
    p += 2;
    tTime.PFC8563_Date.month  = *(p++);
    tTime.PFC8563_Date.date   = *(p++);
    tTime.PFC8563_Date.hour   = *(p++);
    tTime.PFC8563_Date.min    = *(p++);
    tTime.PFC8563_Date.second = *(p++);
//    tTime.tm_isdst = 0; /* 禁用夏令时 */
//    t = timegm(&tTime);

    tDate.wYear = tTime.PFC8563_Date.year + 1900;
    tDate.ucMonth = tTime.PFC8563_Date.month;
    tDate.ucDay = tTime.PFC8563_Date.date;
    if (CheckDateValid(&tDate) && (PFC8563set_date(&tTime) == RT_EOK))/* 检查时间无效，则返回码失败 */
    {
        // TODO:时间检查通过，保存操作记录
        SynTimeAndMode(); // 主机修改时间后，同步从机系统时间
        rt_snprintf_s((char *)buff, sizeof(buff), "%d-%02d-%02d %02d:%02d:%02d",
                 (WORD)(tTimeBefore.tm_year + 1900),
                 tTimeBefore.tm_mon +1,
                 tTimeBefore.tm_mday,
                 tTimeBefore.tm_hour,
                 tTimeBefore.tm_min,
                 tTimeBefore.tm_sec);
        SaveAction(GetActionId(CONTOL_SET_TIME), (char *)buff);
    } else {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
        /* 检查时间无效，则返回码置无效数据 */
    }

    return;
}

/***************************************************************************
 * @brief    发送协议版本号
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
void SendProtocolVer(BYTE ucPort)
{
    s_tProtocol.wSendLenid = 0;

    return;
}


#ifdef DEVICE_USING_D121

/***************************************************************************
 * @brief    是否为121PAD协议获取记录的命令
 * @param    
 * @return   TRUE:是 FALSE:否
 **************************************************************************/
static BOOLEAN IfPadGetRecord(void)
{
    if(s_tProtocol.ucCID2 == GET_HIS_DATA_PAD || s_tProtocol.ucCID2 == GET_HIS_ALARM_PAD ||
       s_tProtocol.ucCID2 == GET_HIS_ACTION_PAD || s_tProtocol.ucCID2 == GET_DCR_RECORD_PAD ||
       s_tProtocol.ucCID2 == GET_BMS_BALANCE_CAPACITY)
    {
        return TRUE;
    }
    else
    {
        return FALSE;
    }

}

static BYTE IsVariablelengthCommand(void) {
    if ((s_tProtocol.ucCID2 == SET_SPECIAL_PARA_PAD) || (s_tProtocol.ucCID2 == SET_CMCC_PARA_PAD) ||
                    (s_tProtocol.ucCID2 == SET_SPE_FACT_PAD) || (s_tProtocol.ucCID2 == SET_NEW_PARA_PAD) || (s_tProtocol.ucCID2 == REMOTE_CONTROL_NEW_PAD) ||
                    ( IfPadGetRecord() &&
                     ((s_tProtocol.aucRecBuf[COMMAND_TYPE] == GET_RECORD_TIME_SLOT &&
                       s_tProtocol.wRecLenid == HIS_RECORD_LEN_TYPE_ID_TIME) ||
                      (s_tProtocol.aucRecBuf[COMMAND_TYPE] == GET_RECORD_NUM &&
                       s_tProtocol.wRecLenid == HIS_RECORD_LEN_TYPE_TIME))))  
    {
        return True;
    }
    return False;
}

/****************************************************************************
 * 函数名称：DealPADCommand()
 * 调    用：无
 * 被 调 用：
 * 输入参数：ucPort-串口号
 * 返 回 值：
 * 功能描述：根据CID1和CID2，调用相应的命令处理程序
 * 作    者  ：王威
 * 版本信息：V1.0
 * 设计日期：2010-06-13
 * 修改记录：
 * 日    期      版  本      修改人      修改摘要
 ***************************************************************************/
static void DealPADCommand(BYTE ucComType)
{
    WORD i = 0;
    const T_CmdFuncStruct *s_atCID2AllFuncTable = GetCmdFnucStruct(CID2PAD);

    for (i = 0; i < HIGH_SEND_LEN; i++) /* 清空响应缓冲区 */
    {
        s_tProtocol.aucSendBuf[i] = 0;
    }
    /* 将响应包的LENID预置为0 */
    s_tProtocol.wSendLenid = 0;

    i = 0;
    while (s_atCID2AllFuncTable != NULL && s_atCID2AllFuncTable[i].ucCmdCode)
    {
        if (s_atCID2AllFuncTable[i].ucCmdCode == s_tProtocol.ucCID2)
        {
            if (s_atCID2AllFuncTable[i].ucDataLen != s_tProtocol.wRecLenid)
            {
                if (IsVariablelengthCommand())
                {
                }
                else
                {
                    s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
                    return;
                }
            }
            (*s_atCID2AllFuncTable[i].func)(ucComType);
            DealDeviceUnlock(AUTO_UNLOCK);
            return;
        }
        i++;
    }

    i = 0;
    while (s_atCID1AllFuncTable[i].ucCmdCode)
    {
        if (s_atCID1AllFuncTable[i].ucCmdCode == s_tProtocol.ucCID1)
        {
            (*s_atCID1AllFuncTable[i].func)(ucComType);
            return;
        }
        i++;
    }

    // CID2错，返回RTN_WRONG_CID2
    s_tProtocol.ucRTN = RTN_WRONG_CID2;

    return;
}
#endif
/****************************************************************************
* 函数名称：DealCommand_v20()
* 调    用：DealCommand()
* 被 调 用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：根据CID1和CID2，调用相应的命令处理程序
* 作    者  ：hanhui
* 版本信息：V1.0
* 设计日期：2021-10-22
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
void DealCommand_v20( BYTE   ucPort )
{
    WORD i = 0;
    const T_CmdFuncStruct *CID2AllFuncTable_v20 = GetCmdFnucStruct(CID2Tower);
    
    for ( i = 0; i < HIGH_SEND_LEN; i++ )    /* 清空响应缓冲区 */
    {
        s_tProtocol.aucSendBuf[i] = 0;
    }
    /* 将响应包的LENID预置为0 */
    s_tProtocol.wSendLenid  = 0;
    if (GET_PROTOCOL_VER_AT == s_tProtocol.ucCID2)
       // || (GET_ADDR == s_tProtocol.ucCID2) )
    {
        return;
    }
    /*
    if (P2P_TRIGGER == s_tProtocol.ucCID2 && CID1_COMMON == s_tProtocol.ucCID1 && PROTOCOL_VER_21 == s_tProtocol.ucVer)
    {
        DealP2PTrigger(ucPort);
        return;
    }
    else
    {
        
    }
    */
    
    i = 0;
    while (CID2AllFuncTable_v20 != NULL && CID2AllFuncTable_v20[i].ucCmdCode)
    {
        if ( CID2AllFuncTable_v20[i].ucCmdCode == s_tProtocol.ucCID2 )
        {   
            if (CID2AllFuncTable_v20[i].ucDataLen != s_tProtocol.wRecLenid )
            {
                if ( ((s_tProtocol.ucCID2 == SET_INPUT_RELAY_AT )
                        && (s_tProtocol.wRecLenid == 4
                        || s_tProtocol.wRecLenid == 42))
                    ||(s_tProtocol.ucCID2 == SET_PARA_DATA_V20 && s_tProtocol.aucRecBuf[7] == 0x50)
                )
                { }
                else
                {
                    s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
                    return;
                }
            }
            (*CID2AllFuncTable_v20[i].func)(ucPort);
            DealDeviceUnlock(AUTO_UNLOCK);
            return;
        }
        i++;
    }

    i = 0;
    if (RT_NULL == CID2AllFuncTable_v20)
    {
        return;
    }
    while (CID2AllFuncTable_v20[i].ucCmdCode)
    {
        if ( CID2AllFuncTable_v20[i].ucCmdCode == s_tProtocol.ucCID1 )
        {
            (*CID2AllFuncTable_v20[i].func)( ucPort );
            return;
        }
        i++;
    }
    
    // CID2错，返回RTN_WRONG_CID2
    s_tProtocol.ucRTN   = RTN_WRONG_CID2;
    
    return;
}

#ifdef POWER_COMM_FAIL_ALARM
static CHAR JudgePowerComm(void)
{
    if((is_fixednetwork_protocol() && s_tProtocol.ucCID2 == GET_ANALOG_V25) ||
         (is_pad_protocol() && s_tProtocol.ucCID2 == GET_ALARM_PAD))
    {
        ResetPowerCommFalg(NORMAL);
    }
    return SUCCESSFUL;
}
#endif


BOOLEAN DealFaultDiagnAlarm(BYTE *p, T_DCRealData *ptDcRealData)
{
    if (p == NULL || ptDcRealData == NULL)
    {
        return False;
    }

    SetDigitalAlarmBit(0, p, 1 == ptDcRealData->tDCStatus.bDiagSts1);            // Bit0：主继电器K1粘连
    SetDigitalAlarmBit(1, p, 1 == ptDcRealData->tDCStatus.bDiagSts2);            // Bit1：主继电器K1无法闭合（驱动正常）
    SetDigitalAlarmBit(2, p, 1 == ptDcRealData->tDCStatus.bDiagSts3);            // Bit2：继电器保护浮地MOS VT36损坏短路
    SetDigitalAlarmBit(3, p, 1 == ptDcRealData->tDCStatus.bDiagSts4);            // Bit3：电池侧上管MOS失效短路
    SetDigitalAlarmBit(4, p, 1 == ptDcRealData->tDCStatus.bDiagSts5);            // Bit4：母排侧上管MOS失效短路
    SetDigitalAlarmBit(5, p, 1 == ptDcRealData->tDCStatus.bDiagSts6);            // Bit5：母排侧下管MOS失效短路
    SetDigitalAlarmBit(6, p, 1 == ptDcRealData->tDCStatus.bDiagSts7);            // Bit6：效率异常
    SetDigitalAlarmBit(7, p, 1 == ptDcRealData->tDCStatus.bDiagSts8);            // Bit7：继电器驱动短路
    p++;                                                                          // 故障诊断告警1  各Bit位取值：0：正常；1：告警

    SetDigitalAlarmBit(0, p, 1 == ptDcRealData->tDCStatus.bDiagSts9);            // Bit0：继电器驱动开路
    SetDigitalAlarmBit(1, p, 1 == ptDcRealData->tDCStatus.bDiagSts10);           // Bit1：母线侧上管驱动常关
    SetDigitalAlarmBit(2, p, 1 == ptDcRealData->tDCStatus.bDiagSts11);           // Bit2：母线侧下管驱动常关
    SetDigitalAlarmBit(3, p, 1 == ptDcRealData->tDCStatus.bDiagSts12);           // Bit3：电池侧驱动常关
    SetDigitalAlarmBit(4, p, 1 == ptDcRealData->tDCStatus.bDiagSts13);           // Bit4：零点采集异常，超出清零
    SetDigitalAlarmBit(5, p, 1 == ptDcRealData->tDCStatus.bDiagSts14);           // Bit5：采样电阻失效
    SetDigitalAlarmBit(6, p, 1 == ptDcRealData->tDCStatus.bDiagSts15);           // Bit6：电流保护电路异常告警
    SetDigitalAlarmBit(7, p, 1 == ptDcRealData->tDCStatus.bDiagSts16);           // Bit7：电池侧2个通道采集和电芯电压累加差异较大
    p++;                                                                          // 故障诊断告警2  各Bit位取值：0：正常；1：告警

    SetDigitalAlarmBit(0, p, 1 == ptDcRealData->tDCStatus.bDiagSts17);           // Bit0：母线侧POWER+和P_IN通道差异较大
    SetDigitalAlarmBit(1, p, 1 == ptDcRealData->tDCStatus.bDiagSts18);           // Bit1：主12V超出±10%范围
    SetDigitalAlarmBit(2, p, 1 == ptDcRealData->tDCStatus.bDiagSts19);           // Bit2：5V超出±10%范围
    SetDigitalAlarmBit(3, p, 1 == ptDcRealData->tDCStatus.bDiagSts20);           // Bit3：PWR 输入MOS 短路
    SetDigitalAlarmBit(4, p, 1 == ptDcRealData->tDCStatus.bDiagSts21);           // Bit4：BAT 输入MOS 短路
    SetDigitalAlarmBit(5, p, 1 == ptDcRealData->tDCStatus.bDiagSts22);           // Bit5：辅助源欠压检测电路异常，PWR MOS持续闭合
    SetDigitalAlarmBit(6, p, 1 == ptDcRealData->tDCStatus.bDiagSts23);           // Bit6：辅助源欠压检测电路异常，PWR MOS持续断开
    SetDigitalAlarmBit(7, p, 1 == ptDcRealData->tDCStatus.bDiagSts24);           // Bit7：辅助源功耗异常
    p++;                                                                          // 故障诊断告警3  各Bit位取值：0：正常；1：告警

    SetDigitalAlarmBit(0, p, 1 == ptDcRealData->tDCStatus.bDiagSts25);           // Bit0：MCU模拟基准异常
    SetDigitalAlarmBit(1, p, 1 == ptDcRealData->tDCStatus.bDiagSts26);           // Bit1：驱动芯片温度异常
    SetDigitalAlarmBit(2, p, 1 == ptDcRealData->tDCStatus.bDiagSts27);           // Bit2：防水检测异常
    SetDigitalAlarmBit(3, p, 1 == ptDcRealData->tDCStatus.bDiagSts28);           // Bit3：加热膜弱短路
    SetDigitalAlarmBit(4, p, 1 == ptDcRealData->tDCStatus.bDiagSts29);           // Bit4：加热膜MOS短路
    SetDigitalAlarmBit(5, p, 1 == ptDcRealData->tDCStatus.bDiagSts30);           // Bit5：加热膜电路MOS无法闭合
    SetDigitalAlarmBit(6, p, 1 == ptDcRealData->tDCStatus.bDiagSts31);           // Bit6：温度采集上报超限值
    SetDigitalAlarmBit(7, p, 1 == ptDcRealData->tDCStatus.bDiagSts32);           // Bit7：保留位
    p++;                                                                          // 故障诊断告警4  各Bit位取值：0：正常；1：告警

    return True;
}


/***************************************************************************
 * @brief    用于计算软件标识码
 * @param    resultArr-计算结果
 * @param    size-长度
 **************************************************************************/


BOOLEAN GetSoftwareTagInfo(BYTE *resultArr, BYTE size)
{
    if(NULL == resultArr){return False;};
    
    T_DCFactory tDcFactory = {0,};
    BYTE bmsLen = 0;
    BYTE bduLen = 0;
    BYTE aucDataBuffTemp[CUSTOMIZED_INFO_LEN] = {0,};
    BYTE aucDataBuff[CUSTOMIZED_INFO_LEN] = {0,};
    BYTE aucEncryptedData[CUSTOMIZED_INFO_LEN] = {0,};

    GetBduFact(&tDcFactory);
    
    bmsLen = removeDot((BYTE *)BMS_VER, sizeof(BMS_VER) - 1, aucDataBuffTemp, sizeof(aucDataBuffTemp), 0x20);
    MemsetBuff(aucDataBuff, aucDataBuffTemp, bmsLen, bmsLen, 0x20); //填充BMS软件版本
    
    CLEAR_STRUCT(aucDataBuffTemp);
    
    bduLen = removeDot(tDcFactory.acSoftVer, sizeof(tDcFactory.acSoftVer), aucDataBuffTemp, sizeof(aucDataBuffTemp), 0x20);
    MemsetBuff(aucDataBuff + bmsLen, aucDataBuffTemp, bduLen, bduLen, 0x20);//填充BDU软件版本

    charTransfer(aucDataBuff, aucEncryptedData, CUSTOMIZED_INFO_LEN);
    rt_memcpy_s(resultArr, size, aucEncryptedData, CUSTOMIZED_INFO_LEN);
    return True;
}

