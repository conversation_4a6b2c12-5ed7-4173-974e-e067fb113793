/**************************************************************************
* Copyright (C) 2022-2023, ZTE Corporation.
* 版权信息：（C）2022-2023，中兴通讯股份有限公司储能软件团队版权所有
* 系统名称：ZXESM BCMU
* 文件名称：common.h
* 文件说明：公共模块头文件
* 作    者  ：王威
* 版本信息：V1.0
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要
* 其他说明：
***************************************************************************/
#ifndef PIN_DEFINE_H_
#define PIN_DEFINE_H_
#ifdef __cplusplus
extern "C" {
#endif

#include "drv_common.h"

#define DO_MAX_NUM  20
/* BMU DI引脚定义 */
#define DI_BMU_PIN                   GET_PIN(A, 10)  // 站址分配DI
#define BMU_BALANCE_FAULT_DI_PIN     GET_PIN(C, 13)  // 均衡电路故障检测引脚PC13

/* BMU DO引脚定义 */
#define DO_BMU_PIN                GET_PIN(A, 9)    // 站址分配DO
#define DO_BMU_PIN_OPT_POWER      GET_PIN(E, 13)    // 优化器电源控制DO
#define RELAY_DO1_PIN			  0	    // 临时定义，解决编译报错
#define RELAY_DO2_PIN			  0	    // 临时定义，解决编译报错
#define RELAY_DO3_PIN			  0	    // 临时定义，解决编译报错
#define RELAY_DO4_PIN			  0	    // 临时定义，解决编译报错
#define ADDR_ASSSIGN_DO_PIN       0     // 临时定义，解决编译报错
#define CTL1_PIN		          0     // 临时定义，解决编译报错
#define CTL2_PIN		          0     // 临时定义，解决编译报错

/* defined the LED pin */
#define LED_RUN_PIN    GET_PIN(B, 2)    // 运行指示灯引脚PB2, 低电平有效
#define LED_ALM_PIN	   GET_PIN(B, 4)    // 运行指示灯引脚PB4, 低电平有效
#define LED_YELLOW_PIN			  0		// 临时定义，解决编译报错
#define LED_RED_PIN			      0		// 临时定义，解决编译报错

#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // PIN_DEFINE_H_  ;
