/**************************************************************************
* Copyright (C) 2001, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：FB101 BCM板软件
* 文件名称：PrtclDL.h
* 文件说明：底层通信协议头文件
* 作    者  ：   王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要
* 其他说明：
***************************************************************************/
#ifndef SOFTWARE_SRC_APP_PRTCLDL_H_
#define SOFTWARE_SRC_APP_PRTCLDL_H_

#ifdef __cplusplus
extern "C" {
#endif

#define BDU_UPDATE_TIMEOUT  400	//20s
#define BDU_PARALLE_UPDATE_TIMEOUT  7200  //6min
#define DLFRAM_MAX_LEN      300
#define CHANGE_CODE         0x7D
#define SCIFRAM_DATA_LEN    10

#define LinkRTN_ACK              0x00 //链路层RTN正常
#define LinkRTN_CRC              0x01 //链路层RTN  CRC 校验错误
#define LinkRTN_DataLen          0x03 //链路层RTN DATA段长度与DATA_LEN段长度不符

//应用层RTN 返回值
#define ApplyRTN_ACK             0x00 //应用层RTN正常
#define ApplyRTN_Fun             0x01 //应用层RTN功能码错误
#define ApplyRTN_Address         0x02 //应用层RTN控制量的ID错误返回码
#define ApplyRTN_Data            0x03 //应用层RTN不合法数据
#define ApplyRTN_CMD             0x07 //应用层RTN 不能执行命令
#define ApplyRTN_SheetERR        0x0A //应用层RTN 数据表序号错误
#define ApplyRTN_NOACK           0x0B //应用层RTN 不回包

//下载部分的宏定义
#define TriggerEnd               0x00
#define JumpBootFail             0x81
#define DownloadFrameErr         0x80
#define DownFileNameErr          0x86
#define DownloadFuncErr          0x84
#define DownloadCRCErr           0x83

#define DownloadType             0xE1
#define DOWNLOADSHEETSN          0x00

#define SU_Type                  0x7D
#define BMS_Type                 0x29  //BMS单元设备类型,智能锂电0x29,常规锂电0x30

#define DOWNLOAD_TRANS                0xFF
#define DOWNLOAD_APP_MIN_LEN          14    //底层通讯协议应用层最小升级帧长度
#define DOWNLOAD_APP_DATA_CONFIRM_LEN 16    //数据传输完成确认帧长度
#define DOWNLOAD_APP_DATA_CONFIRM_BACK_LEN 19    //数据传输完成确认帧回包长度
#define FIRST_PACK_LEN 			      86	//首发帧总长度
#define FIRST_FRAME_LEN			      72	//首发帧数据长度
#define DOWNLOAD_SWAP_BAUD_LEN        16    //切换波特率帧长度
#define FRAME_RECEIVE_COMPLETE        0     //数据帧接收完整
#define FRAME_RECEIVE_INCOMPLETE      1     //数据帧接收不完整
#define PARALLE_PROTO_BOTTOM_DATA_INDEX     12   ///< 并发升级底层通讯协议数据开始位置索引
#define PARALLE_PROTO_BOTTOM_FRM_NO_INDEX   4    ///< 并发升级底层通讯协议帧号位置索引

#define BROADCAST_COMPLETE_PROGRESS   85    // 广播阶段完成的升级进度
#define SUPPLY_COMPLETE_PROGRESS      95    // 补帧阶段完成的升级进度

#define FUNC_CODE_FRONT_TRANSFER_FRM               0x84 ///< SM前级程序更新数据传输帧功能码

#define FUNC_CODE_PARALLE_TRIGGER_FRM              0x91 ///< 程序数据传输触发帧功能码
#define FUNC_CODE_PARALLE_FRONT_TRIGGER_FRM        0x92 ///< 前级程序更新传输帧功能码
#define FUNC_CODE_PARALLE_TRANSFER_FRM             0x93 ///< 程序更新传输帧（含首发帧和数据帧）功能码
#define FUNC_CODE_PARALLE_FRONT_TRANSFER_FRM       0x94 ///< 前级程序更新传输帧（含首发帧和数据帧）功能码
#define FUNC_CODE_PARALLE_DATA_CONFIRM_FRM         0x95 ///< 程序更新数据传输完成确认帧功能码
#define FUNC_CODE_PARALLE_FRONT_DATA_CONFIRM_FRM   0x96 ///< 前级程序更新数据传输完成确认帧功能码
#define FUNC_CODE_PARALLE_UPDATE_CONFIRM_FRM       0x97 ///< 程序更新确认帧功能码
#define FUNC_CODE_PARALLE_FRONT_UPDATE_CONFIRM_FRM 0x98 ///< 前级程序更新确认帧功能码
/**********************   底层通讯协议特殊字节位置    *******************/
#define DOWNLOAD_FUNC_INDEX              3      // 功能码
#define DOWNLOAD_RSV_DEST_DEV_TYPE_INDEX 7      // 预留字节-设备类型
#define DOWNLOAD_RSV_DEST_DEV_ADR_INDEX  8      // 预留字节-设备地址
#define DOWNLOAD_RSV_TRANS_INDEX         9     // 预留字节-是否支持透传（0xFF透传）
//首发帧相关宏
#define 	SM_DLFL_NAME_LEN        		40     //文件名占40个字节
#define 	SM_DLFL_TIME_LEN        		20     //文件的时间占20个字节
#define 	SM_DLFL_FIRST_FRM_APPLE_LEN     72     //首发帧应用层长度

typedef struct
{
	BYTE    ucProtoType;
	BYTE	ucSrcDevType;
	BYTE	ucSrcDevAdr;
	BYTE	ucDestDevType;
	BYTE	ucDestDevAdr;
	BYTE    ucApplyFun;                           //应用层功能码  12
	BYTE	ucRTN;	
	BYTE	ucRsvDestDevType;   //预留目的设备类型
	BYTE	ucRsvDestDevAdr;    //预留目的设备类型
	BYTE	ucRsvTrans;         //预留字节(0xFF 透传标志) 
	BYTE	ucLCHKSUM;
	BYTE	aucDataBuf[DLFRAM_MAX_LEN];	
	WORD    wDataLength;
	WORD    wApplyAppEnd;  //附加码

    /***********   应用层数据信息   ************/
    WORD wApplyLength;						//应用层数据包长度
    WORD wApplyRecvDataLength;
    WORD wApplySendDataLength;

	BYTE aucApplyDataBuf[DLFRAM_MAX_LEN];
}T_PrtclDLStruct;

typedef struct { 
    BYTE    ucProtoType;
    BYTE	ucDestDevType;
	BYTE	ucDestDevAdr;
    BYTE    aucDataBuf[DLFRAM_MAX_LEN];  ///< 数据接收缓冲区
    WORD    wDataLength;                ///< 缓冲区数据长度
}T_PrtclCommRecStruct;

typedef enum
{
    DATA_TRANS_INIT,        // 数据传输初始状态
    DATA_TRANS_INTERRPUT,   // 数据传输中断
    DATA_TRANS_FINISH,      // 数据传输结束
}E_DATA_TRANS_STATUS;

typedef enum
{
    DIRECT_SUPPLY,     // 直接补帧：用于从机正常回包后的补帧
    TIME_OUT_SUPPLY,   // 超时补帧：用于从机回包超时的补帧
}E_DATA_SUPPLY_TYPE;

/* 并发升级传输场景mode */
typedef enum {
    PARALLE_TRANSFER_BROADCAST,         ///< 广播（不回包）
    PARALLE_TRANSFER_POINT_TO_POINT,    ///< 点对点（回包）
    PARALLE_TRANSFER_INVALID_MODE,      ///< 无效
} T_ParalleTransferMode;

/* 并发升级过程中各个状态 */
typedef enum {
    PARALLE_DL_STAT_PREPARE,                ///< 升级初始状态
    PARALLE_DL_STAT_TRANS,                  ///< 数据传输阶段
    PARALLE_DL_STAT_DATA_CONFIRM,           ///< 数据传输完成确认阶段
    PARALLE_DL_STAT_UPDATE_CONFIRM,         ///< 升级确认阶段
} T_ParalleDLStat;

/* 升级链路类型 */
typedef enum {
    DL_LINK_TYPE_NONE,
    DL_LINK_TYPE_COMM,
    DL_LINK_TYPE_CAN1,
    DL_LINK_TYPE_CAN2,
    DL_LINK_TYPE_SCI,
} T_PrtclDLLinkType;

/*从机升级状态*/
typedef enum
{
    SM_UPDATE_NOT_START,      // 0,无升级
    SM_UPDATE_SUCCESSFUL,     // 1,升级成功
    SM_UPDATE_FAILED,         // 2,升级失败
    SM_UPDATE_ONGOING,        // 3,升级进行中
} T_SMUpdateStat;

/*代理模式升级链态*/
typedef enum {
    AGENT_DL_STAT_PREPARE,          // 准备阶段
    AGENT_DL_STAT_TRIGING,          // 数据传输触发阶段(点对点)
    AGENT_DL_STAT_DATA_TRANS,       // 数据传输阶段(广播)
    AGENT_DL_STAT_DATA_CONFIRM,     // 数据传输确认阶段(点对点)
    AGENT_DL_STAT_SUPPLY_FRAME,     // 补帧阶段(点对点)
    AGENT_DL_STAT_UPDATE_CONFIRM,   // 更新确认阶段(点对点)
    AGENT_DL_STAT_PROBE,            // 触发升级阶段(主机自己进行升级)
} T_AgentParalleDLStat;

void DealCommDLData(T_CommStruct *ptComm);
void DealCanDLData(T_CommStruct *ptComm);
BOOLEAN sendDlRepData(BYTE* pucApplyData, BYTE ucLen);
BOOLEAN GetSciData(T_PrtclDLStruct *pDLFrm);
void BeginDownload( BYTE ucMode );
void CheckUpdateTimeOut(void);
BOOLEAN IsUpdate(void);
void UpgradeMode(void);
void SetSwUpgradeFlag(BOOLEAN bSwUpgradeFlag);
char *GetRcvFileName(void);
BYTE IsLastFrame();
BOOLEAN GetTriggerEndFlag(void);
void SetPrtclDLLinkType(T_PrtclDLLinkType bPrtclDLLinkType);
BOOLEAN IsMasterTrans(BYTE ucRsvDestDevAdr, BYTE ucFunc);
BOOLEAN SendCommTransFrm(T_CommStruct *ptComm);
void Process_DL_Main(T_CommStruct *ptComm);
BOOLEAN IsCommTrans(void);
BOOLEAN ClearCommTransFlag(void);
short DealParalleUpdateTimeout(void);
BYTE CalBatchUpdateProgress(BYTE *bBuff);   //升级进度条计算
BOOLEAN SetAgentUpdateFlag(BOOLEAN bAgentUpdateFlag);
void TimeOutCounterThread(void* parameter);  //超时时间
BOOLEAN GetSwUpgradeFlag(void);

#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif


#endif
