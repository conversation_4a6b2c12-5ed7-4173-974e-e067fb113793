#include <board.h>
#include  <string.h>
#include "addr_distribution.h"
#include "msg.h"
#include "pin_ctrl.h"
#include "sample.h"
#include "sps.h"
#include "dev_bsmu.h"
#include "storage.h"
#include "pin_define.h"
#include "msg_id.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "utils_rtthread_security_func.h"

#define TIMER_NAME                    "timer_addr_distr"
#define CMD_ADDR_DISTR_TIMEOUT_TIME    10000    ///<  命令超时时间
#define NOT_SAMPLE_VOLT                0 

#define WAIT_START_NOT_START           0 
#define WAIT_START_STARTED             1 

static void wait_start(void *data);
static void broad_start_to_bmu(void *data);
static void set_bmu_io(void *data);
static void broad_addr_distr(void *data);
static void wait_distr_end(void *data);
static void init_timer();
static void addr_distr_timeout();
static void dcdc_volt_sample(void *data);
static void init_topo_info(storage_operate_e opr);
static void send_msg_to_sample(unsigned int msg_id, unsigned char dest,  unsigned char dev_no);
static void send_msg_to_north(short result);


static state_id_e state_id = STATE_WAIT_START;
static rt_timer_t timer_addr_distr_timeout;
static cluster_topology_t topo;
static short ADDR_DISTR_TIMEOUT = 0;
static dev_inst_t* dev_BMU;
static module_msg_t msg_recv;
static unsigned char north_mod_id;
static unsigned char wait_start_flag = WAIT_START_NOT_START;

typedef void (*state_process_fun)(void *data);

//状态函数定义
state_process_fun state_process[] = 
{
    wait_start,
    broad_start_to_bmu,
    set_bmu_io,
    broad_addr_distr,
    wait_distr_end,
    bmu_volt_sample,
    dcdc_volt_sample,
    calc_topo
};


void init_addr_distr(){
    
    dev_BMU = init_dev_inst(DEV_BMU);
    //消息队列初始化
    //RETURN_IF_FAIL(init_msg_queue(MOD_ADDR_DISTR) != NULL);
    //定时器初始化
    init_timer();
    
    //msg_recv.dest = MOD_ADDR_DISTR;
    //初始化拓扑信息
    rt_memset_s(&topo, sizeof(topo), 0, sizeof(topo));
    topo.topo_valid = TOPO_INVALID;
    init_topo_info(read_opr);

    //调用站址分配流程
    //addr_distribution(NULL);
    
}


static void init_topo_info(storage_operate_e opr) {
    part_data_t para_part;
     
    para_part.buff = (uint8_t *)&topo;
    para_part.len = sizeof(topo);
    para_part.offset = 0;
    rt_snprintf_s(para_part.name, sizeof(para_part.name), SYSPARA_PART);
    //storage_process(&para_part, opr);  // todo :结合硬件调试
}

static void init_timer(){
	timer_addr_distr_timeout = rt_timer_create(TIMER_NAME, addr_distr_timeout,
                             NULL, CMD_ADDR_DISTR_TIMEOUT_TIME,
                             RT_TIMER_FLAG_ONE_SHOT);
}

static void addr_distr_timeout(){
    ADDR_DISTR_TIMEOUT = 1;
}

void addr_distribution(void* parameter)
{
    state_id_e state_id_temp;
    
    /*while (is_running(TRUE))
    {*/
        do
        {
            state_id_temp = state_id;
            state_process[state_id](NULL);
        }
        while(state_id_temp != state_id && state_id != STATE_MAX);
        
        if(state_id == STATE_MAX)
        {
            state_id = STATE_WAIT_START;
        }
        //rt_thread_delay(10);
    /*}*/
}

void set_wait_start_flag(module_msg_t* msg_rcv_control){
    rt_memcpy_s(&msg_recv, sizeof(module_msg_t),msg_rcv_control, sizeof(module_msg_t));
    wait_start_flag = WAIT_START_STARTED;
}

//等待北向站址分配启动的消息
static void wait_start(void *data)
{
    addr_assign_trig_data_t* addr_assign_data = NULL;
    
    // if (recv_msg(&msg_recv) == SUCCESSFUL) {
    //     if(msg_recv.msg_id == NOTIFY_ADDR_DISTRIBUTION_MSG){
    if (wait_start_flag) {
        rt_timer_start(timer_addr_distr_timeout);
        north_mod_id = msg_recv.src;
        addr_assign_data = (addr_assign_trig_data_t *)msg_recv.data;
        if(addr_assign_data != NULL && addr_assign_data->assign_type == BMU_ADDR_DISTR){
            send_msg_to_sample(START_BMU_ADDR_DISTRIBUTION_MSG, MOD_SAMPLE, NOT_SAMPLE_VOLT);
            state_id = STATE_BROAD_TO_BMU;
            free(addr_assign_data);
        }   
    }
    //     }
    // }
}

//发送消息给采样模块/设备模块
static void send_msg_to_sample(unsigned int msg_id, unsigned char dest,  unsigned char dev_no){
    module_msg_t msg_send;
    unsigned char msg_data[2] = {0};
    //unsigned char (*send_data)[2] = malloc(2*sizeof(unsigned char));  // iar compile error
    unsigned char *send_data = (unsigned char*)malloc(sizeof(msg_data));
    RETURN_IF_FAIL(send_data != NULL);

    msg_send.src = MOD_CONTROL;
    msg_send.dest = dest;
    msg_send.msg_id = msg_id;
    if(dev_no == NOT_SAMPLE_VOLT){
        msg_send.data = NULL;
    }else{
        msg_data[0] = BMU_GET_SAMPLE_VOL;
        msg_data[1] = dev_no;
        rt_memcpy_s(send_data, sizeof(msg_data), &msg_data, sizeof(msg_data));
        msg_send.data = send_data;
    }
    send_msg(&msg_send);
}



//广播通知BMU启动站址分配
static void broad_start_to_bmu(void *data)
{
    if(send_dest_cmd(dev_BMU, BMU_ADDR_ASSIGN) == SUCCESSFUL){
        state_id = STATE_SET_BMU_IO;
    }
}

//设置io
static void set_bmu_io(void *data)
{
    set_pin(ADDR_ASSSIGN_DO_PIN, PIN_LOW);
    state_id = STATE_BROAD_ADDR;
}

//发送广播命令广播地址0占用
static void broad_addr_distr(void *data)
{
    if(send_dest_cmd(dev_BMU, BMU_ADDR_OCCUPY) == SUCCESSFUL){    
        state_id = STATE_WAIT_DISTR;
    }
}

//等待站址分配结束
static void wait_distr_end(void *data)
{    
    if(ADDR_DISTR_TIMEOUT){
        send_msg_to_north(ADDR_DISTR_FAILURE);
        state_id = STATE_WAIT_START;
        wait_start_flag = WAIT_START_NOT_START;
        ADDR_DISTR_TIMEOUT = 0;
    }
    if(get_pin(BCMU_ADDR_ASSSIGN_DI_PIN) == PIN_LOW){
        send_msg_to_north(ADDR_DISTR_SUCCESS);
        state_id = STATE_BMU_VOLT_SAMPLE;
    }
}

//发送消息给北向通信模块告知BMU站址分配的结果
static void send_msg_to_north(short result){
    addr_assign_trig_data_t addr_assign_data;
    module_msg_t msg_send;

    msg_send.src = MOD_CONTROL;
    msg_send.dest = north_mod_id;
    msg_send.msg_id = FINISH_BMU_ADDR_DISTRIBUTION_MSG;
    msg_send.data = (void*)malloc(sizeof(addr_assign_trig_data_t));
    RETURN_IF_FAIL(msg_send.data != NULL);
    
    addr_assign_data.assign_type = BMU_ADDR_DISTR;
    addr_assign_data.result = result;
    rt_memcpy_s(msg_send.data, sizeof(addr_assign_trig_data_t), &addr_assign_data, sizeof(addr_assign_trig_data_t));

    send_msg(&msg_send);
    free(msg_send.data);
    rt_timer_stop(timer_addr_distr_timeout);
}

//bmu电压采样
void bmu_volt_sample(void *data)
{
    unsigned char bmu_no = 0;
    
    while(bmu_no < BATT_MOD_NUM)
    {
        //发消息通知采样模块采集数据
        send_msg_to_sample(EXE_DEST_CMD_MSG, MOD_BMU, bmu_no);
        bmu_no++;
    }
    //等待设备模块通知采集完数据
    /*if (recv_msg(&msg_recv) == SUCCESSFUL) {
        if(msg_recv.msg_id == NOTIFY_VOLT_SAMPLE_FINISH_MSG){
            state_id = STATE_DCDC_VOLT_SAMPLE;
        }   
    }*/
    state_id = STATE_DCDC_VOLT_SAMPLE;
}

//dc_dc电压采样
static void dcdc_volt_sample(void *data)
{
    unsigned char dcdc_no = 0;
    while(dcdc_no < BATT_CLUSTER_NUM)
    {
        //发消息通知采样模块采集数据
        send_msg_to_sample(EXE_DEST_CMD_MSG, MOD_DC_DC, dcdc_no);
        dcdc_no++;
    }
    //等待设备模块通知采集完数据
    /*if (recv_msg(&msg_recv) == SUCCESS) {
        if(msg_recv.msg_id == NOTIFY_VOLT_SAMPLE_FINISH_MSG){
            state_id = STATE_CALC_TOPO;
        }   
    }*/
    state_id = STATE_CALC_TOPO; 
}

static unsigned char get_addr_distribution_volt(addr_distribution_volt_t* volt_info) {
/*  todo:重新开发
    float total_batt_mod_vol = 0;
    float total_batt_cluster_vol = 0;
    unsigned char i;

    rt_memset_s(volt_info, sizeof(addr_distribution_volt_t), 0 ,sizeof(addr_distribution_volt_t));
    for (i = 0; i < BATT_CLUSTER_NUM; i++) {
        volt_info->cluster_count++;
        get_one_data(BCMU_DATA_ID_BATT_VOLT + i , &volt_info->cluster_voltage[i]);
        total_batt_cluster_vol += volt_info->cluster_voltage[i];
    }
    
    for(i = 0; i < BATT_MOD_NUM; i++) {
        volt_info->mod_count++;
        get_one_data(BCMU_DATA_ID_MOD_VOL_TOTAL + i , &volt_info->mod_voltage[i]);
        total_batt_mod_vol += volt_info->mod_voltage[i];
    }
    
    
    if (fabs(total_batt_mod_vol - total_batt_cluster_vol) > BATT_MOD_VOLT)
        return FAILURE;
*/
    return SUCCESSFUL;
}

static void change_bmu_addr_to_no(unsigned char cluster_index,
                                  unsigned char batt_mod_start_index,
                                  cluster_topology_t* new_topo) {
    unsigned char batt_mod_index = 0;
    unsigned char mod_num_of_cluster = new_topo->mod_num[cluster_index];
    unsigned char i = 0;
    
    for(i = 0; i < mod_num_of_cluster; i++) {
        if (cluster_index%2 == 0) {
            batt_mod_index = i;
        } else {
            batt_mod_index = mod_num_of_cluster - i - 1;
        }
        
        new_topo->mod_addr[cluster_index][batt_mod_index] = batt_mod_start_index + i + 1;
    }
}

void calc_topo(void* data) {
    addr_distribution_volt_t volt_info = {0};
    unsigned char batt_mod_start_index = 0;
    unsigned char i = 0;
    unsigned char j = 0;
    float batt_cluster_mod_vol = 0;
    cluster_topology_t new_topo = {0};
    module_msg_t msg_send = {0};
    
    
    //  获取所有电池模块和电池簇电压
    rt_memset_s(&new_topo, sizeof(new_topo), 0, sizeof(new_topo));
    new_topo.topo_valid = TOPO_INVALID;

    /* todo:get_addr_distribution_volt重新开发后释放开
    if (get_addr_distribution_volt(&volt_info) != SUCCESSFUL) {
         return;
    }*/
     
    rt_memset_s(&new_topo, sizeof(new_topo), 0, sizeof(new_topo)); 
    new_topo.mod_count = volt_info.mod_count;
    new_topo.cluster_count = volt_info.cluster_count;
    
     /* todo:get_addr_distribution_volt重新开发后释放开
    //  计算单簇下电池模块数目，并确认地址映射关系
    for(i = 0; i < volt_info.cluster_count; i++) {
        for(j = batt_mod_start_index; j < volt_info.mod_count; j++) {
            batt_cluster_mod_vol += volt_info.mod_voltage[j];
            if (fabs(batt_cluster_mod_vol - volt_info.cluster_voltage[i]) < BATT_MOD_VOLT) {
                new_topo.mod_num[i] = j - batt_mod_start_index + 1;
                change_bmu_addr_to_no(i, batt_mod_start_index, &new_topo);
                batt_cluster_mod_vol = 0;
                break;
            }
               
        }
        batt_mod_start_index += new_topo.mod_num[i];
    }
    
    if(batt_mod_start_index != volt_info.mod_count)
        return;
    */
    new_topo.topo_valid = TOPO_VALID;
    rt_memcpy_s(&topo, sizeof(topo), &new_topo, sizeof(topo));
    if (memcmp(&new_topo, &topo, sizeof(topo)) != 0) {
        rt_memcpy_s(&topo, sizeof(topo), &new_topo, sizeof(topo));
        init_topo_info(write_opr);
    }
    //TODO:发送TOPO计算完成的消息给主线程
    msg_send.src = MOD_CONTROL;
    msg_send.dest = MOD_SAMPLE;
    msg_send.msg_id = TOPO_CALC_FINISH_MSG;
    msg_send.data = NULL;
    if(send_msg(&msg_send)== SUCCESSFUL){
        state_id = STATE_WAIT_START;
        wait_start_flag = WAIT_START_NOT_START;
    }
}

void get_cluster_topology(cluster_topology_t *cluster_topo) {
    if (cluster_topo != NULL) {
        rt_memcpy_s(cluster_topo, sizeof(cluster_topology_t), &topo, sizeof(cluster_topology_t));
    }
}
/*
//调试时提供虚拟的拓扑结构，保证程序正常运行
void get_cluster_topology(cluster_topology_t *cluster_topo) {

    // unsigned char topo_valid;                                   //拓扑数据是否有效，1：有效，0：无效
    // unsigned char mod_count;                                    //电池模块总数
    // unsigned char cluster_count;                                //簇数
    // unsigned char mod_addr[BATT_CLUSTER_NUM][BATT_CLUSTER_MOD_NUM];    //每簇电池模块地址
    // unsigned char mod_num[BATT_CLUSTER_NUM];    
    if (cluster_topo != NULL) {
        cluster_topo->topo_valid = TOPO_VALID;
        cluster_topo->mod_count = 16;
        cluster_topo->cluster_count = 4;
        for(int i=0;i<4;i++)
        {
            cluster_topo->mod_addr[0][i] = i + 1; //1 2 3 4
            cluster_topo->mod_addr[2][i] = i + 1 + 8; // 9 10 11 12

            cluster_topo->mod_addr[1][i] = 8 - i; //8 7 6 5
            cluster_topo->mod_addr[3][i] = 16 - i; // 16 15 14 13

            cluster_topo->mod_num[i] = 4;
        }
        // memcpy(cluster_topo, &topo, sizeof(cluster_topology_t));
    }
}
*/
