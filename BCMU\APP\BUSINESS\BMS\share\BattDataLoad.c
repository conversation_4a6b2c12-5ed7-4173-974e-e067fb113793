#include <stdio.h>
#include <stddef.h>
#include "led.h"
#include "sample.h"
#include "CommCan.h"
#include "battery.h"
#include "realAlarm.h"
#include "MasterOper.h"
#include "SlaveOper.h"
#include "BattCharge.h"
#include "MultBattData.h"
#include "para.h"
#include "prtclDL.h"
#include "DataLoad_product.h"
#include "fm.h"

Static BOOLEAN IsChgTempLowPrtOnly(T_BCMAlarmStruct const *ptBcmAlarm, T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn);
Static Bool IsChargeForbidden(T_BCMAlarmStruct const *ptBcmAlarm, T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn);
Static BOOLEAN IsExistForbiddenChargeTemperature(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn);
Static void LoadBattCellTemp(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn, T_BCMDataStruct *pBcmData);
Static void ReloadBattDataIfUsageScenChange(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal, T_BCMDataStruct *pBcmData);
Static void BattDataManagement(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
Static void LoadBattCapInfo(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
Static void LoadBattCellVolt(T_BattInfo *pBattIn, T_BCMDataStruct *pBcmData, T_HardwareParaStruct *pHardwarePara);
Static void LoadBattChargeDischargeProtectInfo(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn, T_BCMAlarmStruct *pAlarm, T_BattDealInfoStruct *pBattDeal);
Static void LoadBattOtherAlarmInfo(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn, T_BCMAlarmStruct *pAlarm);
Static void LoadBattSampleData(T_BattInfo *pBattIn, T_BCMDataStruct *pBcmData);
Static void LoadBattBduData(T_BattInfo *pBattIn, T_DCRealData *pDCRealData);
Static void LoadBattConstantData(T_BattInfo *pBattIn);
Static void LoadBattControlledStatus(T_BattInfo *pBattIn);
Static void LoadBattMasterSlaveData(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
Static void LoadBattCellOCVInfo(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn);
Static BOOLEAN IsIrreversibleAlarm(T_BCMAlarmStruct const *ptBcmAlarm, T_HardwareParaStruct *pHardwarePara);

Static T_SysPara s_tSysPara;
Static BOOLEAN s_bChgPrtAlmExist = False;                 // 是否存在充电保护告警
Static BOOLEAN s_bDischgPrtAlmExist = False;              // 是否存在放电保护告警
Static BOOLEAN s_bChgPrtAlmExistOnlyChgTempLow = False;   // 充电保护，并且只有充电低温保护告警

/***************************************************************************
 * @brief    电池管理参数加载主入口函数
 **************************************************************************/
void BattDataLoad(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    BattDataManagement(pHardwarePara, pBattIn, pBattDeal);
    return;
}

BOOLEAN GetChgPrtAlmExist(void)
{
    return s_bChgPrtAlmExist;
}

BOOLEAN GetDischgPrtAlmExist(void)
{
    return s_bDischgPrtAlmExist;
}

BOOLEAN GetChgPrtAlmExistOnlyChgTempLow(void)
{
    return s_bChgPrtAlmExistOnlyChgTempLow;
}

/***************************************************************************
 * @brief    载入电池单体电压，并计算单体最大最小电压
 **************************************************************************/
Static void LoadBattCellVolt(T_BattInfo *pBattIn, T_BCMDataStruct *pBcmData, T_HardwareParaStruct *pHardwarePara)
{
    rt_memcpy(pBattIn->tData.afCellVolt, pBcmData->afCellVolt, sizeof(pBcmData->afCellVolt));
    pBattIn->tData.ucCellVoltNum = pHardwarePara->ucCellVoltNum;

    pBattIn->tData.fCellVoltMin = pBattIn->tData.afCellVolt[0];
    pBattIn->tData.fCellVoltMax = pBattIn->tData.afCellVolt[0];
    for (BYTE i = 0; i < pHardwarePara->ucCellVoltNum; i++)
    {
        pBattIn->tData.fCellVoltMin = MIN(pBattIn->tData.afCellVolt[i], pBattIn->tData.fCellVoltMin);
        pBattIn->tData.fCellVoltMax = MAX(pBattIn->tData.afCellVolt[i], pBattIn->tData.fCellVoltMax);
    }
    return;
}

/***************************************************************************
 * @brief    载入电池充放电保护、告警信息
 **************************************************************************/
Static void LoadBattChargeDischargeProtectInfo(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn,
                                               T_BCMAlarmStruct *pAlarm, T_BattDealInfoStruct *pBattDeal)
{
    pBattIn->tData.bChargePrtNotVolLow = FALSE;
    pBattIn->tData.bDischargeProtect = IsDischargeForbidden(pAlarm, pHardwarePara, pBattIn);
    pBattIn->tData.bDischargePrtAlarm = pBattIn->tData.bDischargeProtect;
    pBattIn->tData.bChargeProtect = IsChargeForbidden(pAlarm, pHardwarePara, pBattIn);
    pBattIn->tData.bChgTempLowPrtOnly = IsChgTempLowPrtOnly(pAlarm, pHardwarePara, pBattIn);
    pBattIn->tData.bChargePrtAlarm = pBattIn->tData.bChargeProtect;
    pBattIn->tData.bIrreversibleAlarm = IsIrreversibleAlarm(pAlarm, pHardwarePara);
    pBattIn->tData.bActivatePortProtect = IsActivatePortUVP(pAlarm, pHardwarePara);

    s_bChgPrtAlmExist = pBattIn->tData.bChargeProtect;                   // 是否存在充电保护告警
    s_bDischgPrtAlmExist = pBattIn->tData.bDischargeProtect;             // 是否存在放电保护告警
    s_bChgPrtAlmExistOnlyChgTempLow = pBattIn->tData.bChgTempLowPrtOnly; // 充电保护，并且只有充电低温保护告警

#ifdef BDU_CONNECTOR_TEMP_HIGH_RECOVER_ENABLE
    /* 连接器高温保护时满足强补电条件允许充电 */
    if (pBattDeal->bCellUnderVolt == True && GetBDUConnTempHighRecoverFlag() == FALSE)
    {
        pBattIn->tData.bChargeProtect = FALSE;
    }
#endif
    /* 在线非浮充时开启充电保护 */
    if (BATT_MODE_STANDBY == pBattDeal->ucBatStatus)
    {
        pBattIn->tData.bChargeProtect = TRUE;
    }

    return;
}

/***************************************************************************
 * @brief    载入电池其他告警信息（电池组欠压保护、单体欠压保护、单体损坏）
 **************************************************************************/
Static void LoadBattOtherAlarmInfo(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn, T_BCMAlarmStruct *pAlarm)
{
    pBattIn->tData.bCellDemage = FALSE;
    pBattIn->tData.bCellUnderVoltPrt = FALSE;
    pBattIn->tData.bBattLowVoltPrt = pAlarm->ucBattUnderVoltPrt != (BYTE)0 ? TRUE : FALSE;

    for (BYTE i = 0; i < pHardwarePara->ucCellVoltNum; i++)
    {
        if (pAlarm->aucCellUnderVoltPrt[i] || pAlarm->aucCellDynamicUnderVoltPrt[i])
        {
            pBattIn->tData.bBattLowVoltPrt = TRUE;
            pBattIn->tData.bCellUnderVoltPrt = TRUE;
        }

        if (pAlarm->aucCellDamagePrt[i])
        {
            pBattIn->tData.bCellDemage = TRUE;
        }
    }
    return;
}

/***************************************************************************
 * @brief    载入电池组采样数据
 **************************************************************************/
Static void LoadBattSampleData(T_BattInfo *pBattIn, T_BCMDataStruct *pBcmData)
{
    pBattIn->tData.fBusVol = pBcmData->fExterVolt;
    pBattIn->tData.fBattCurr = pBcmData->fBattCurr;
    pBattIn->tData.fBusCurr = pBcmData->fBusCurr;
    pBattIn->tData.bBduChgDisable = pBcmData->ucBattChargeEn != (BYTE)0 ? TRUE : FALSE;
    pBattIn->tData.bBduDischgDisable = pBcmData->ucBattDischEn != ((BYTE)0) ? TRUE : FALSE;
    pBattIn->tData.ucBDUStatus = (BDU_STATUS)pBcmData->ucBduStatus;
    pBattIn->tData.ucBduChargeProtectStatus = pBcmData->ucChgProtSta;
    pBattIn->tData.ucBduDischargePrtStatus = pBcmData->ucDischgProtSta;
    pBattIn->tData.fBatVol = pBcmData->fBattVolt;
    pBattIn->tData.wBattSOC = pBcmData->wBattSOC;
    pBattIn->tData.wBattSOH = pBcmData->wBattSOH;
    pBattIn->tData.wBattHighPrecSOC = pBcmData->wBattHighPrecSOC;
    pBattIn->tData.bBduSleep = pBcmData->ucBduSleep != ((BYTE)0) ? TRUE : FALSE;
    pBattIn->tData.fActivatePortVol = pBcmData->fActivatePortVol;                     // 激活口电压
    SetContactorStatus(pBcmData->bContactorStatus);
    return;
}

/***************************************************************************
 * @brief    载入电池BDU数据
 **************************************************************************/
Static void LoadBattBduData(T_BattInfo *pBattIn, T_DCRealData *pDCRealData)
{
    pBattIn->tData.bBduChargeBreak = pDCRealData->tDCStatus.bInputBreak != (BOOLEAN)0 ? TRUE : FALSE;
    pBattIn->tData.bCtrChargeFail = pDCRealData->tDCStatus.bChange2ChgEn != (BOOLEAN)0 ? TRUE : FALSE;
    pBattIn->tData.ucReceivedStaitChg = pDCRealData->tDCStatus.bChgStraight;
    pBattIn->tData.ucReceivedStaitDischg = pDCRealData->tDCStatus.bDisChgStraight;
    SetNativePowerOnStatus(pDCRealData->tDCStatus.bExternalPowerOn);
    return;
}

/***************************************************************************
 * @brief    载入电池常量数据
 **************************************************************************/
Static void LoadBattConstantData(T_BattInfo *pBattIn)
{
    pBattIn->tData.fCurrMinDet = MIN_CURR_DET_BDCU;
    return;
}

/***************************************************************************
 * @brief    载入电池受控状态
 **************************************************************************/
Static void LoadBattControlledStatus(T_BattInfo *pBattIn)
{
    if (RUN_MODE_CONTROLLED == pBattIn->tPara.ucRunMode && !IsProtoSetInfoInvalid(RUN_MODE_CONTROLLED))
    {
        pBattIn->tData.bRunModeControled = TRUE;
    }
    else
    {
        pBattIn->tData.bRunModeControled = FALSE;
    }
    return;
}

/***************************************************************************
 * @brief    加载电池主从机同步数据
 **************************************************************************/
Static void LoadBattMasterSlaveData(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    SlaveData(pHardwarePara->ucCellVoltNum, pBattIn, pBattDeal);

    pBattIn->tData.fSysVol = getSysVolt(); // 获取主从机系统的电压

    if (IsMaster())
    {
        pBattIn->tData.bSlaveChargeBreak = getChargeBreakStatus();
        pBattIn->tData.fMaxCurrInAll = getMaxCurrInAll();
        pBattIn->tData.bB3BattExist = getBattType();
        pBattIn->tData.fAveCurrInCluster = GetAveCurr();
        pBattIn->tData.wAveSocInCluster = GetAveSoc();
        pBattIn->tData.fAveBattVolt = GetAveBattVolt();
        pBattIn->tData.bCAN2DischExist = GetCAN2BattStatus();
#ifdef INTELLIGENT_PEAK_SHIFTING
        pBattIn->tData.ucFMBatteryleftcapacity = GetBatteryLeftCapacity();
        pBattIn->tData.wSlaveAvgSOC = GetSlaveAvgSOC();
        pBattIn->tData.fFMCurrentMaxChgPower = GetBatteryMaxChargePower();
        pBattIn->tData.ucFMChgAndDischgDisable = GetBatteryChangeAndDischgStatus();
        pBattIn->tData.ucFmNormalSlaveNum = GetFmNormalSlave();
#endif
        
    }
    else
    {
        pBattIn->tData.bSlaveChargeBreak = FALSE;
    }

    pBattIn->tData.fBattAverageVol = getBattAverageVol();
    pBattIn->tData.fBattTotalCurr = getBattTotalCurr();
    pBattIn->tData.fMinBattVolt = getSlaveDataMin1();
    pBattIn->tData.fBalanceCurr = getBalanceCurr();
    pBattIn->tData.ucChgStaNum = GetChgStaNum();

    return;
}

/***************************************************************************
 * @brief    载入电池电芯OCV信息
 **************************************************************************/
Static void LoadBattCellOCVInfo(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn)
{
    FLOAT fCellRes = 0.01;
    FLOAT afCellVolt[CELL_VOL_NUM_MAX];
    BYTE ucCellNum = 0;

    fCellRes = GetCellDCR(pBattIn->tData.fCellAverageTemp);

    rt_memcpy(afCellVolt, pBattIn->tData.afCellVolt, pHardwarePara->ucCellVoltNum * sizeof(pBattIn->tData.afCellVolt[0]));

    /* 排序，为了取中值，以中值计算OCV */
    BubbleSort(afCellVolt, pHardwarePara->ucCellVoltNum);

    /* ucCellNum计算用于解决kw问题，避免可能存在的数组访问越界问题，实际情况应该不存在 */
    ucCellNum = pHardwarePara->ucCellVoltNum >> 1;
    ucCellNum = (ucCellNum < sizeof(afCellVolt)/sizeof(FLOAT)) ? ucCellNum : ((sizeof(afCellVolt)/sizeof(FLOAT)) / 2);
    pBattIn->tData.fCellOCV = (afCellVolt[ucCellNum] - pBattIn->tData.fBattCurr / 1000 * fCellRes);
    return;
}

/***************************************************************************
 * @brief    电池过程数据管理
 **************************************************************************/
Static void BattDataManagement(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    T_BCMAlarmStruct tBCMAlm;
    T_BCMDataStruct tBcmData;
    T_DCRealData tRealData;

    GetRealAlarm(BCM_ALARM, (BYTE *)&tBCMAlm);
    GetSysPara(&s_tSysPara);
    GetRealData(&tBcmData);
    GetBduReal(&tRealData);
#ifdef INTELLIGENT_PEAK_SHIFTING
    GetFmDistributeData(&pBattIn->tData.tFmData);
    GetPeakShiftPara(GetShiftPeakPara());
#endif

    LoadBattSyspara(pBattIn, pBattDeal, &s_tSysPara, &tBcmData);
    ReloadBattDataIfUsageScenChange(pBattIn, pBattDeal, &tBcmData);
    LoadBattCapInfo(pBattIn, pBattDeal);
    LoadBattCellVolt(pBattIn, &tBcmData, pHardwarePara);
    LoadBattCellTemp(pHardwarePara, pBattIn, &tBcmData);
    LoadBattChargeDischargeProtectInfo(pHardwarePara, pBattIn, &tBCMAlm, pBattDeal);
    LoadBattOtherAlarmInfo(pHardwarePara, pBattIn, &tBCMAlm);
    LoadBattSampleData(pBattIn, &tBcmData);
    LoadBattBduData(pBattIn, &tRealData);
    LoadBattConstantData(pBattIn);
    LoadBattControlledStatus(pBattIn);
    LoadBattMasterSlaveData(pHardwarePara, pBattIn, pBattDeal);
    LoadBattCellOCVInfo(pHardwarePara, pBattIn);

    QtpBattDataOverWrite(pBattIn, &tBcmData, &s_tSysPara);

    VOltTwoLevelProtectEnter(&tBCMAlm,&s_tSysPara); //电压二级保护功能入口函数

    return;
}

/***************************************************************************
 * @brief    载入电池容量信息
 **************************************************************************/
Static void LoadBattCapInfo(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if (s_tSysPara.wBatteryCap != pBattIn->tPara.wBattCap)
    {
        if ((WORD)0 != pBattIn->tPara.wBattCap)
        {
            pBattDeal->fBattCap = pBattDeal->fBattCap * s_tSysPara.wBatteryCap / pBattIn->tPara.wBattCap;
        }
        pBattIn->tPara.wBattCap = s_tSysPara.wBatteryCap;
    }
    return;
}

/***************************************************************************
 * @brief    是否禁止充电
 **************************************************************************/
Static Bool IsChargeForbidden(T_BCMAlarmStruct const *ptBcmAlarm, T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn)
{
    WORD wAlarmConfigIndex = -1;
    T_AlarmConfigStruct atAlarmConfig[] = {
        {offsetof(T_BCMAlarmStruct, ucChgCurrHighPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucBattOverVoltPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucBoardTempHighPrt), 1},
        {offsetof(T_BCMAlarmStruct, aucCellOverVoltPrt[0]), pHardwarePara->ucCellVoltNum},
        {offsetof(T_BCMAlarmStruct, aucChgTempHighPrt[0]), pHardwarePara->ucCellTempNum},
        {offsetof(T_BCMAlarmStruct, aucChgTempLowPrt[0]), pHardwarePara->ucCellTempNum},
        {offsetof(T_BCMAlarmStruct, aucCellDamagePrt[0]), pHardwarePara->ucCellVoltNum},
        {offsetof(T_BCMAlarmStruct, ucBattSOHPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucBattReverse), 1},
        {offsetof(T_BCMAlarmStruct, ucBduEepromAlm), 1},
        {offsetof(T_BCMAlarmStruct, ucChgLoopInvalid), 1},
        {offsetof(T_BCMAlarmStruct, ucDischgLoopInvalid), 1},
        {offsetof(T_BCMAlarmStruct, ucCurrLimLoopInvalid), 1},
        {offsetof(T_BCMAlarmStruct, ucBDUBusVoltHighPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucInsideTempHighPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucCellVoltSampleFault), 1},
        {offsetof(T_BCMAlarmStruct, ucCellTempSensorInvalidAlm), pHardwarePara->ucCellTempNum},
        {offsetof(T_BCMAlarmStruct, ucLoopFault), 1},
        {offsetof(T_BCMAlarmStruct, ucBDUBattChgVoltLowPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucCellPoorConsisPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucEnvTempHighPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucEnvTempLowPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucBDUCommFail), 1},
        {offsetof(T_BCMAlarmStruct, ucBDUBattLockAlm), 1},
        {offsetof(T_BCMAlarmStruct, ucHeaterFilmFailure), 1},
        {offsetof(T_BCMAlarmStruct, ucDcrFaultPrt), 1},
#ifdef FIRE_CONTROL_ENABLED
        {offsetof(T_BCMAlarmStruct, ucFireControlAlm), 1},
#endif
#ifdef ACTIVATE_PORT_ENABLED
        {offsetof(T_BCMAlarmStruct, ucActivePortCurrError), 1},           //激活回路电流异常保护
#endif
        {offsetof(T_BCMAlarmStruct, ucCellTempRiseAbnormal),1}, //单体温升速率异常告警
        {offsetof(T_BCMAlarmStruct, ucBattFaultTempHighAlm),1}, //电池异常高温保护告警
        //虽然产生了充电保护，但是还是允许进行自补电的告警放到下面
        {offsetof(T_BCMAlarmStruct, ucBDUConnTempHighPrt), 1},
    };
#ifdef BDU_CONNECTOR_TEMP_HIGH_RECOVER_ENABLE
    // 引发充电保护后还能进行强补电的告警
    T_AlarmConfigStruct atAlarmConfigCanSupply[] = {
        {offsetof(T_BCMAlarmStruct, ucBDUConnTempHighPrt), 1},
    };
#endif
    if (IsUpdate())
    {
        return TRUE;
    }

    if (IsExistForbiddenAlarm(&atAlarmConfig[0], ptBcmAlarm, sizeof(atAlarmConfig) / sizeof(atAlarmConfig[0]), &wAlarmConfigIndex))
    {
        //如果BDU连接器温度高保护不可自动恢复，则可以进行强补电，否则会充电保护。
#ifdef BDU_CONNECTOR_TEMP_HIGH_RECOVER_ENABLE
        if((GetBDUConnTempHighRecoverFlag() == FALSE) && 
           IsExistSpecialChgPrt(&atAlarmConfigCanSupply[0], &atAlarmConfig[0], sizeof(atAlarmConfigCanSupply) / sizeof(atAlarmConfigCanSupply[0]), wAlarmConfigIndex))
        {
            pBattIn->tData.bChgPrtByCanSupplyAlm = True;
        }
#endif
        pBattIn->tData.bChargePrtNotVolLow = TRUE;
        return TRUE;
    }

    if (IsExistForbiddenChargeTemperature(pHardwarePara, pBattIn))
    {
        return TRUE;
    }

    return FALSE;
}

/***************************************************************************
 * @brief    充电保护中是否只存在充电低温保护
 **************************************************************************/
Static BOOLEAN IsChgTempLowPrtOnly(T_BCMAlarmStruct const *ptBcmAlarm, T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn)
{
    WORD wAlarmConfigIndex = -1;
    T_AlarmConfigStruct atAlarmConfig[] = {
        {offsetof(T_BCMAlarmStruct, ucChgCurrHighPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucBattOverVoltPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucBoardTempHighPrt), 1},
        {offsetof(T_BCMAlarmStruct, aucCellOverVoltPrt[0]), pHardwarePara->ucCellVoltNum},
        {offsetof(T_BCMAlarmStruct, aucChgTempHighPrt[0]), pHardwarePara->ucCellTempNum},
        {offsetof(T_BCMAlarmStruct, aucCellDamagePrt[0]), pHardwarePara->ucCellVoltNum},
        {offsetof(T_BCMAlarmStruct, ucBattSOHPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucBattReverse), 1},
        {offsetof(T_BCMAlarmStruct, ucBduEepromAlm), 1},
        {offsetof(T_BCMAlarmStruct, ucChgLoopInvalid), 1},
        {offsetof(T_BCMAlarmStruct, ucDischgLoopInvalid), 1},
        {offsetof(T_BCMAlarmStruct, ucCurrLimLoopInvalid), 1},
        {offsetof(T_BCMAlarmStruct, ucBDUBusVoltHighPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucInsideTempHighPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucCellVoltSampleFault), 1},
        {offsetof(T_BCMAlarmStruct, ucCellTempSensorInvalidAlm), pHardwarePara->ucCellTempNum},
        {offsetof(T_BCMAlarmStruct, ucLoopFault), 1},
        {offsetof(T_BCMAlarmStruct, ucBDUBattChgVoltLowPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucCellPoorConsisPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucEnvTempHighPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucEnvTempLowPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucBDUCommFail), 1},
        {offsetof(T_BCMAlarmStruct, ucBDUBattLockAlm), 1},
        {offsetof(T_BCMAlarmStruct, ucHeaterFilmFailure), 1},
        {offsetof(T_BCMAlarmStruct, ucDcrFaultPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucCellTempRiseAbnormal),1},
        {offsetof(T_BCMAlarmStruct, ucBDUConnTempHighPrt), 1},
        {offsetof(T_BCMAlarmStruct, aucChgTempLowPrt[0]), pHardwarePara->ucCellTempNum},
    };

    T_AlarmConfigStruct atAlarmConfigChgTempLowPrt[] = {
        {offsetof(T_BCMAlarmStruct, aucChgTempLowPrt[0]), pHardwarePara->ucCellTempNum},
    };

    if (IsExistForbiddenAlarm(&atAlarmConfig[0], ptBcmAlarm, sizeof(atAlarmConfig) / sizeof(atAlarmConfig[0]), &wAlarmConfigIndex))
    {
        //如果只存在充电低温保护，则可以退出休眠状态。
        if(IsExistSpecialChgPrt(&atAlarmConfigChgTempLowPrt[0], &atAlarmConfig[0], sizeof(atAlarmConfigChgTempLowPrt) / sizeof(atAlarmConfigChgTempLowPrt[0]), wAlarmConfigIndex))
        {
            return TRUE;
        }
    }

    return FALSE;
}

/***************************************************************************
 * @brief    是否存在不可逆告警
 **************************************************************************/
Static BOOLEAN IsIrreversibleAlarm(T_BCMAlarmStruct const *ptBcmAlarm, T_HardwareParaStruct *pHardwarePara) 
{
    T_AlarmConfigStruct atAlarmConfig[] = {
        {offsetof(T_BCMAlarmStruct, ucBDUBattLockAlm), 1},
        {offsetof(T_BCMAlarmStruct, ucBDUCommFail), 1},
        {offsetof(T_BCMAlarmStruct, ucCellPoorConsisPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucCellTempSensorInvalidAlm[0]), pHardwarePara->ucCellTempNum},
    };
    BYTE i=0, j=0;
    BYTE* pAlarm = NULL;
    
    for (i=0; i<sizeof(atAlarmConfig)/sizeof(atAlarmConfig[0]); i++)
    {
        pAlarm = (BYTE*)ptBcmAlarm + atAlarmConfig[i].offset;
        for (j=0; j<atAlarmConfig[i].ucNum; j++)
        {
            if (pAlarm[j] != (BYTE)NORMAL)
            {
                return True;
            }
        }
    }
    return False;
}

/***************************************************************************
 * @brief    加载单体温度，并且计算单体最大温度、最小温度、平均温度
 **************************************************************************/
Static void LoadBattCellTemp(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn, T_BCMDataStruct *pBcmData)
{
    BYTE i = 0;
    BYTE ucCellValidNum = 0;

    rt_memcpy(pBattIn->tData.afCellTemp, pBcmData->afCellTemp, sizeof(pBcmData->afCellTemp));

    pBattIn->tData.fCellAverageTemp = 0;
    pBattIn->tData.fCellTempMin = pBcmData->afCellTemp[0];
    pBattIn->tData.fCellTempMax = pBcmData->afCellTemp[0];
    pBattIn->tData.fCellTempMedium = pBcmData->fCellTempMedium;

    for (i = 0; i < pHardwarePara->ucCellTempNum; i++)
    {
        if (!pBcmData->abCellTempFault[i])
        {
            pBattIn->tData.fCellTempMin = MIN(pBcmData->afCellTemp[i], pBattIn->tData.fCellTempMin);
            pBattIn->tData.fCellTempMax = MAX(pBcmData->afCellTemp[i], pBattIn->tData.fCellTempMax);
            pBattIn->tData.fCellAverageTemp += pBcmData->afCellTemp[i];
            ucCellValidNum++;
        }
    }

    if (ucCellValidNum)
    {
        pBattIn->tData.fCellAverageTemp = pBattIn->tData.fCellAverageTemp / ucCellValidNum;
    }
    else
    {
        pBattIn->tData.fCellAverageTemp = 25;
    }
    return;
}

/***************************************************************************
 * @brief    电池使用场景改变时，重载电池部分数据
 **************************************************************************/
Static void ReloadBattDataIfUsageScenChange(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal, T_BCMDataStruct *pBcmData)
{
    if (pBattIn->tPara.ucUsageScen != s_tSysPara.ucUsageScen)
    {
        pBattDeal->fPowerDownVolt = pBcmData->fBattVolt * g_ProConfig.fVoltTrasRate;
        pBattDeal->fBusVoltSum = 0;
        pBattDeal->bInitMixDischg = False;
        pBattDeal->wEqualChgPowerOffTime = 0;
        pBattDeal->ucChangePowerOnDelay = 0;
        if (s_tSysPara.ucUsageScen == DISCHG_MODE_SELF_ADAPTION || s_tSysPara.ucUsageScen == DISCHG_MODE_SOLAR)
        {
            pBattIn->tPara.fSysPowerOnThreshold = MIX_INIT_POWER_ON_VOLT;
            pBattDeal->fPowerDownVolt = MIX_INIT_POWER_DOWN_VOLT;
            if (pBattDeal->ucChargeMode == BATT_MODE_DISCHARGE) // 放电时才初始化放电电压，充电阶段直接根据母排电压计算
            {
                pBattDeal->bInitMixDischg = True;
            }
        }
        else if (s_tSysPara.ucUsageScen == DISCHG_MODE_CONSTANT_VOLTAGE)
        {
            pBattDeal->fPowerDownVolt = s_tSysPara.fRemoteSupplyOutVolt;
        }
    }
    pBattIn->tPara.ucUsageScen = s_tSysPara.ucUsageScen;

    if(pBattIn->tPara.ucUsageScen == DISCHG_MODE_SOLAR && pBcmData->bSolarMode == FALSE)
    {
        if(pBattIn->tData.tHardWareStruct.ucCellVoltNum == R121_CELL_VOLT_NUM)
        {
            BduCtrl(SCI_CTRL_TEST, R121_BATT_TEST_MODE_SOLAR);
        }
        else
        {
            BduCtrl(SCI_CTRL_TEST, BATT_TEST_MODE_SOLAR);
        }
    }
    else if(pBattIn->tPara.ucUsageScen != DISCHG_MODE_SOLAR && pBcmData->bSolarMode == TRUE)
    {
        if(pBattIn->tData.tHardWareStruct.ucCellVoltNum == R121_CELL_VOLT_NUM)
        {
            BduCtrl(SCI_CTRL_TEST, R121_BATT_TEST_MODE_NORMAL);
        }
        else
        {
            BduCtrl(SCI_CTRL_TEST, BATT_TEST_MODE_NORMAL);
        }
    }

    return;
}

/***************************************************************************
 * @brief    是否存在禁止充电或放电的告警
 **************************************************************************/
BOOLEAN IsExistForbiddenAlarm(T_AlarmConfigStruct *ptAlarmConfig, T_BCMAlarmStruct const *ptBcmAlarm, BYTE num, WORD *pwAlarmConfigIndex)
{
    BYTE i = 0, j = 0;
    BYTE *pAlarm = NULL;

    for (i = 0; i < num; i++)
    {
        pAlarm = (BYTE *)ptBcmAlarm + ptAlarmConfig[i].offset;
        for (j = 0; j < ptAlarmConfig[i].ucNum; j++)
        {
            if (pAlarm[j] != (BYTE)NORMAL)
            {
                *pwAlarmConfigIndex = i;
                return True;
            }
        }
    }
    return False;
}

/***************************************************************************
 * @brief    是否有需要特殊处理的充电保护
 **************************************************************************/
BOOLEAN IsExistSpecialChgPrt(T_AlarmConfigStruct *ptAlarmConfigSpecial, T_AlarmConfigStruct *ptAlarmConfig, BYTE num, WORD wAlmConfigIndex)
{
    BYTE i = 0;
    for (i = 0; i < num; i++)
    {
        if(ptAlarmConfigSpecial[i].offset == ptAlarmConfig[wAlmConfigIndex].offset)
        {
            return True;
        }
    }
    return False;
}

/***************************************************************************
 * @brief    是否存在禁止放电的温度
 **************************************************************************/
BOOLEAN IsExistForbiddenDischargeTemperature(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn)
{
    for (BYTE i = 0; i < pHardwarePara->ucCellTempNum; i++)
    {
        if (pBattIn->tData.afCellTemp[i] < pBattIn->tPara.fDischgLowTempPrt ||
            pBattIn->tData.afCellTemp[i] > pBattIn->tPara.fDischgHighTempPrt)
        {
            return True;
        }
    }
    return False;
}

/***************************************************************************
 * @brief    是否存在禁止充电的温度
 **************************************************************************/
Static BOOLEAN IsExistForbiddenChargeTemperature(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn)
{
    for (BYTE i = 0; i < pHardwarePara->ucCellTempNum; i++)
    {
        if (pBattIn->tData.afCellTemp[i] < pBattIn->tPara.fChgLowTempPrt ||
            pBattIn->tData.afCellTemp[i] > pBattIn->tPara.fChgHighTempPrt)
        {
            return True;
        }
    }
    return False;
}

#ifndef POWER_ON_COUNTER_ENABLE
void JudgeChargeTestmode(void)
{
    T_BCMDataStruct tBcmData;

    GetRealData(&tBcmData);

    if (tBcmData.bChargeMachineTest != s_tSysPara.ucChargeMachineTestMode)
    {
        BduCtrl(SCI_CTRL_CHG_MANCHIN_TEST, s_tSysPara.ucChargeMachineTestMode);
    }
    return;
}
#endif