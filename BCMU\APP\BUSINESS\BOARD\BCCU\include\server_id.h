
#ifndef _SERVER_ID_H_
#define _SERVER_ID_H_
#ifdef __cplusplus
extern "C" {
#endif

#include "utils_server.h"


#define SAMPLE_SERVER_ID            (DATA_SERVER + 1)
#define LED_SERVER_ID               (CONTROL_SERVER + 1)
#define CANCOMM_SERVER_ID           (NORTH_SERVER + 1)
#define CANCOMM3_SERVER_ID          (NORTH_SERVER + 2)
#define USARTCOMM_SERVER_ID         (NORTH_SERVER + 3)
#define ALARM_MANAGE_SERVER_ID             (ALARM_SERVER + 1)

#ifdef __cplusplus
}
#endif

#endif  // _SERVER_ID_H_