/**
 * @file     insulation_detect.c
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2023-02-20
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */

#include <math.h>
#include <string.h>
#include <rtthread.h>
#include "insulation_detect.h"
#include "i2c_ctrl.h"

#define TCA9535_I2C_BUS1_NAME          "i2c1"  /* 传感器连接的I2C总线设备名称 */
#define TCA9535_I2C_BUS2_NAME          "i2c2"  /* 传感器连接的I2C总线设备名称 */

#define CLUSTER1   0xFE
#define CLUSTER2   0xFD
#define CLUSTER3   0xFB
#define CLUSTER4   0xF7
#define SWITCH1    0xEF
#define SWITCH2    0xDF
#define SWITCH3    0xBF
#define IDB_ADDR   0x20    /* 从机地址 */
#define SET_DATA_CMD   0x02 
#define GET_DATA_CMD   0x00

static struct rt_i2c_bus_device *i2c_bus1 = RT_NULL;     /* I2C总线设备句柄 */
static struct rt_i2c_bus_device *i2c_bus2 = RT_NULL;     /* I2C总线设备句柄 */
static rt_bool_t initialized = RT_FALSE;                /* 传感器初始化状态 */


typedef struct {
    float v_p1;
    float v_n1;
    float v_p2;
    float v_n2;
    float v_p3;
    float v_n3;
}res_calc_st;

// 函数声明
static float bus_volt_neg_calc(float v_iso1);
static float bus_volt_pos_calc(float v_out, float bus_neg_volt);
static void get_voltage_func(sample_volt_t* volt_array, res_calc_st* volt_var);
static void calc_insulation_resistance(sample_volt_t* volt_array, float* resis_pos, float* resis_neg);
static void tca_9535_init(void);
static void discon_switch(unsigned char switch_series);
static void connect_switch(unsigned char switch_series);
static void choose_cluster(unsigned char cluster_series);
static int judge_sample_abnormal(sample_volt_t *volt_array1, sample_volt_t *volt_array2);
static sample_volt_t average_volt(sample_volt_t *volt_array1, sample_volt_t *volt_array2);
static void get_insulation_sample_volt(sample_volt_t *volt_array, unsigned char cluster_num);

// 函数定义
static float bus_volt_neg_calc(float v_iso1) {
    return v_iso1*404.02/4.02;
}

static float bus_volt_pos_calc(float v_out, float bus_neg_volt) {
    return v_out - bus_neg_volt;
}

static void get_voltage_func(sample_volt_t* volt_array, res_calc_st* volt_var) {
    volt_var->v_n1 = bus_volt_neg_calc(volt_array->v_iso1[0]);
    volt_var->v_p1 = bus_volt_pos_calc(volt_array->v_out[0], volt_var->v_n1);
    volt_var->v_n2 = bus_volt_neg_calc(volt_array->v_iso1[1]);
    volt_var->v_p2 = bus_volt_pos_calc(volt_array->v_out[1], volt_var->v_n2);
    volt_var->v_n3 = bus_volt_neg_calc(volt_array->v_iso1[2]);
    volt_var->v_p3 = bus_volt_pos_calc(volt_array->v_out[2], volt_var->v_n3);
}

static void calc_insulation_resistance(sample_volt_t* volt_array, float* resis_pos, float* resis_neg) {
    float temp_a, temp_b = 0;
    res_calc_st calc_re = {0};
    get_voltage_func(volt_array, &calc_re);

    if (fabs(calc_re.v_n1 * calc_re.v_p3) < 1e-6 && fabs(calc_re.v_n2 * calc_re.v_p3) < 1e-6) {
        return ;
    }
    temp_a = (calc_re.v_p1 * calc_re.v_n3) / (calc_re.v_n1 * calc_re.v_p3);
    temp_b = (calc_re.v_p2 * calc_re.v_n3) / (calc_re.v_n2 * calc_re.v_p3);
    if (fabs(14.931 - temp_a) < 1e-6 && fabs(15.07 * temp_b - 1) < 1e-6) {
        return ;
    }
    *resis_pos = (400.0 * temp_a - 400.0) / (14.931 - temp_a);
    *resis_neg = (404.02 - 404.02 * temp_b) / (15.07 * temp_b - 1);
}


static void tca_9535_init(void)
{

    i2c_bus1 = rt_i2c_bus_device_find(TCA9535_I2C_BUS1_NAME);
    i2c_bus2 = rt_i2c_bus_device_find(TCA9535_I2C_BUS2_NAME);

    if (i2c_bus1 == RT_NULL) {
        rt_kprintf("can't find %s device!\n", TCA9535_I2C_BUS1_NAME);
    }
    else if (i2c_bus2 == RT_NULL) {
        rt_kprintf("can't find %s device!\n", TCA9535_I2C_BUS2_NAME);
    }
    else {
        initialized = RT_TRUE;
    }

}


static void choose_cluster(unsigned char cluster_series) {
    unsigned char write_cmd = SET_DATA_CMD;
    unsigned char write_data;
    switch (cluster_series)
    {
    case 1:
        write_data = CLUSTER1;
        i2c_write(i2c_bus1, IDB_ADDR, &write_cmd, 1, &write_data, 1);
        break;
    case 2:
        write_data = CLUSTER2;
        i2c_write(i2c_bus1, IDB_ADDR, &write_cmd, 1, &write_data, 1);
        break;
    case 3:
        write_data = CLUSTER3;
        i2c_write(i2c_bus1, IDB_ADDR, &write_cmd, 1, &write_data, 1);
        break;
    case 4:
        write_data = CLUSTER4;
        i2c_write(i2c_bus1, IDB_ADDR, &write_cmd, 1, &write_data, 1);
        break;
    default:
        break;
    }
}

static void connect_switch(unsigned char switch_series) {
    unsigned char write_cmd = SET_DATA_CMD;
    unsigned char write_data;
    switch (switch_series)
    {
    case 1:
        write_data = SWITCH1;
        i2c_write(i2c_bus1, IDB_ADDR, &write_cmd, 1, &write_data, 1);
        break;
    case 2:
        write_data = SWITCH2;
        i2c_write(i2c_bus1, IDB_ADDR, &write_cmd, 1, &write_data, 1);
        break;
    case 3:
        write_data = SWITCH3;
        i2c_write(i2c_bus1, IDB_ADDR, &write_cmd, 1, &write_data, 1);
        break;
    default:
        break;
    }
}

static void discon_switch(unsigned char switch_series) {
    unsigned char temp;
    unsigned char write_cmd = SET_DATA_CMD;
    switch (switch_series)
    {
    case 1:
        temp = SWITCH2 & SWITCH3;
        i2c_write(i2c_bus1, IDB_ADDR, &write_cmd, 1, &temp, 1);
        break;
    case 2:
        temp = SWITCH1 & SWITCH3;
        i2c_write(i2c_bus1, IDB_ADDR, &write_cmd, 1, &temp, 1);
        break;
    case 3:
        temp = SWITCH1 & SWITCH2;
        i2c_write(i2c_bus1, IDB_ADDR, &write_cmd, 1, &temp, 1);
        break;
    case 0:
        temp = SWITCH1 & SWITCH2 & SWITCH3;
        i2c_write(i2c_bus1, IDB_ADDR, &write_cmd, 1, &temp, 1);
        break;
    default:
        break;
    }
}

void read_voltage_value(float* volt_pos, float* volt_neg) {
    rt_uint8_t temp[3]={0};
    unsigned char read_cmd = GET_DATA_CMD;
    i2c_write(i2c_bus1, IDB_ADDR, &read_cmd, 1, 0, 0); 
    i2c_read(i2c_bus1, IDB_ADDR, &read_cmd, 1, &temp[0], 3);      
    *volt_pos = (float)(((unsigned short)temp[1])<<8 | ((unsigned short)temp[2]));

    i2c_write(i2c_bus2, IDB_ADDR, &read_cmd, 1, 0, 0); 
    i2c_read(i2c_bus2, IDB_ADDR, &read_cmd, 1, &temp[0], 3);      
    *volt_neg = (float)(((unsigned short)temp[1])<<8 | ((unsigned short)temp[2]));    
}


/**
 * @brief  计算电池簇绝缘电阻
 * @details
 * @param[in]    cluster_num 电池簇编号
 * @param[out]   resis_pos   正极绝缘电阻
 * @param[out]   resis_neg   负极绝缘电阻
 * @retval       
 */
void calc_cluster_insulation_resistance(float *resis_pos, float *resis_neg, unsigned char cluster_num)
{
    sample_volt_t volt_array;
    sample_volt_t volt_array1, volt_array2;
    unsigned char counter = 0;
    
    // 采样电压不稳定，重新采样
    do
    {
        get_insulation_sample_volt(&volt_array1, cluster_num);
        get_insulation_sample_volt(&volt_array2, cluster_num);
        if(counter>COUNTER_NUM)
        {
            counter = 0;
            return;
        }
        counter++;
    }while(judge_sample_abnormal(&volt_array1, &volt_array2));
    
    // 取两次采样的平均值
    volt_array = average_volt(&volt_array1, &volt_array2);
    
    // 计算绝缘电阻
    calc_insulation_resistance(&volt_array, resis_pos, resis_neg);

}

// 判断两次采样电压是否稳定
static int judge_sample_abnormal(sample_volt_t *volt_array1, sample_volt_t *volt_array2)
{
    unsigned int i = 0;
    // if(volt_array1==NULL || volt_array2==NULL)
    //     return FALSE;
    for(i=0; i<SAMPLE_NUM; i++)
    {
        if( fabs(volt_array1->v_iso1[i]-volt_array2->v_iso1[i]) > STABLE_THERSHOLD )
            return TRUE;
        if( fabs(volt_array1->v_out[i]-volt_array2->v_out[i]) > STABLE_THERSHOLD )
            return TRUE;
    }
    return FALSE;
}

// 取两次采样电压平均值
static sample_volt_t average_volt(sample_volt_t *volt_array1, sample_volt_t *volt_array2)
{
    sample_volt_t volt_array;
    unsigned int i = 0;
    
    for(i=0; i<SAMPLE_NUM; i++)
    {
        volt_array.v_iso1[i] = (volt_array1->v_iso1[i]+volt_array2->v_iso1[i]) / 2;
        volt_array.v_out[i] = (volt_array1->v_out[i]+volt_array2->v_out[i]) / 2;
    }
    
    return volt_array;
}


/**
 * @brief  获取第 n 簇绝缘电阻采样电压
 * @details
 * @param[in]    cluster_num 电池簇编号
 * @param[out]   volt_array 绝缘电阻采样电压
 * @retval       
 */
static void get_insulation_sample_volt(sample_volt_t *volt_array, unsigned char cluster_num)
{
    float volt_iso1, volt_out;

    tca_9535_init();
    if(initialized == RT_FALSE)
        return ;
    
    connect_switch(D1);  // 闭合开关 D1
    choose_cluster(cluster_num); // 选择第 n 簇
    
    // 第 1 次采样
    discon_switch(D2);  // 断开 D2
    read_voltage_value(&volt_iso1, &volt_out);
    volt_array->v_iso1[0] = volt_iso1;
    volt_array->v_out[0]  = volt_out;
    
    // 第 2 次采样
    discon_switch(D3);  // 断开 D3
    read_voltage_value(&volt_iso1, &volt_out);
    volt_array->v_iso1[1] = volt_iso1;
    volt_array->v_out[1]  = volt_out;
    
    // 第 3 次采样
    discon_switch(0);   // 闭合 D2 D3
    read_voltage_value(&volt_iso1, &volt_out);
    volt_array->v_iso1[2] = volt_iso1;
    volt_array->v_out[2]  = volt_out;
    
}
