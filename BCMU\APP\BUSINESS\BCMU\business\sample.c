#include <rtthread.h>
#include <rtdevice.h>
#include "sample.h"
#include "utils_thread.h"
#include "device_type.h"
#include "cmd.h"
#include "sps.h"
#include "msg.h"
#include "msg_id.h"
#include "addr_distribution.h"
#include "pin_ctrl.h"
#include "di_sample.h"
#include "insulation_detect.h"
#include "pin_define.h"
#include "utils_time.h"
#include "cleaning.h"
#include "battery.h"
#include "realdata_save.h"
#include "realdata_id_in.h"
#include "battery_management.h"
#include "utils_rtthread_security_func.h"

#define WAIT_TIMER_NAME                "wait_addr_distr"
#define WAIT_ADDR_DISTR_TIMEOUT_TIME    15000    ///<  预设恢复时间
#define BCMU_SAMPLE_TIME_DELAY          20       ///< unit ms

static void process_recv_msg(module_msg_t* msg_rcv);
static void init_timer();
static void wait_addr_distr_timeout();
static void send_msg_to_sps(unsigned int msg_id, unsigned char dest);
static void send_start_dev_poll_msg();
static void set_data_validity(unsigned char isvalid);
static void send_epo_di_msg (void* arg);
static void send_busbar_overtemp_msg (void* arg);
static void save_insulation_resistance();

static dev_inst_t* dev_BMU;
static dev_inst_t* dev_DC_DC;

static rt_timer_t wait_addr_distr_timer;
static cluster_topology_t cluster_topo;
static batt_cabinet_dig_t cabinet_dig;
static batt_cluster_ana_data_t s_cluster_ana[BATT_CLUSTER_NUM];

static rt_uint32_t last_time = 0;

static di_manage_t di_manage_info[] = {
    {BCMU_RELAY_DI1_PIN, NULL}, 
    {BCMU_RELAY_DI2_PIN, NULL},
    // {BCMU_EPO_DI_PIN, send_epo_di_msg, PIN_IRQ_MODE_FALLING},     
    {BCMU_FIRE_CTL_DI_PIN, NULL}, 
    // {BCMU_TEMP_SWITCH_DI_PIN, send_busbar_overtemp_msg, PIN_IRQ_MODE_RISING_FALLING}, 
    {BCMU_ADDR_ASSSIGN_DI_PIN, NULL}, 
};

static module_msg_t msg_rcv = {MOD_CONTROL, MOD_SAMPLE,};

static void process_para_change(const rt_msg_t pMsg){

}

static msg_map sample_msg_map[] =
{
    {PARA_CHANGE_MSG_ID,   process_para_change},         
};


void* init_sample(void * param)
{
    server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(sample_msg_map) / sizeof(msg_map);
    register_server_msg_map(sample_msg_map, server_info);
	//初始化消息队列
	init_msg_queue(MOD_SAMPLE);

    //初始化定时器
    init_timer();
    
    di_sample_init(di_manage_info, sizeof(di_manage_info)/sizeof(di_manage_info[0]));

    // //启动设备收发线程
    dev_BMU = init_dev_inst(DEV_BMU);
    // dev_DC_DC = init_dev_inst(DEV_DC_DC);
    
    send_start_dev_poll_msg();
    save_insulation_resistance();


    
    cleaning_init(&cluster_topo);

	
	/****
	调试时使用这一段
	dev_BMU = init_dev_inst(DEV_BMU);
    //dev_DC_DC = init_dev_inst(DEV_DC_DC); //调试时注释，因为找不到

    // send_start_dev_poll_msg();    //scons 调试时注释 
    // save_insulation_resistance(); //scons 报错：找不到I2C硬件
	*****/
    return NULL;
    //采样模块的主要业务
    //sample_main();
}

//预设恢复采样轮询的时间，避免系统进入死循环
static void init_timer(){
	wait_addr_distr_timer = rt_timer_create(WAIT_TIMER_NAME, wait_addr_distr_timeout,
                             NULL, WAIT_ADDR_DISTR_TIMEOUT_TIME,
                             RT_TIMER_FLAG_ONE_SHOT);
}

static void wait_addr_distr_timeout(){
    send_start_dev_poll_msg();
}

void sample_main(void * param){
    while (is_running(TRUE)){
        //接收其他模块发送的消息
        process_recv_msg(&msg_rcv);
        //TODO：其他业务
        //exe_heartbeat(last_time, MOD_SAMPLE);
        
        calc_cluster_chg_temp_max_min(&s_cluster_ana[0]);
        rt_thread_mdelay(BCMU_SAMPLE_TIME_DELAY);
    }
 }


static void process_recv_msg(module_msg_t* msg_rcv){  
    if(SUCCESSFUL == recv_msg(msg_rcv)){
        switch (msg_rcv->msg_id) {
            case START_BMU_ADDR_DISTRIBUTION_MSG:
                //设置当前的设备无效，避免上送不正确的数据
                set_data_validity(DATA_INVALID);
                send_msg_to_sps(STOP_DEV_POLL_MSG, MOD_BMU);
                send_msg_to_sps(STOP_DEV_POLL_MSG, MOD_DC_DC);
                rt_timer_start(wait_addr_distr_timer);
                break;
            case TOPO_CALC_FINISH_MSG:
                send_start_dev_poll_msg();
                rt_timer_stop(wait_addr_distr_timer);
                break;
            default:
                break;
        }
    }
}

static void set_data_validity(unsigned char isvalid){
    int mod_no;
    for(mod_no = 0; mod_no < BATT_MOD_NUM; mod_no++){
        if(isvalid == DATA_VALID){
            dev_BMU[mod_no].dev_data.valid_flag = DATA_VALID;
            
        }else{
            dev_BMU[mod_no].dev_data.valid_flag = DATA_INVALID;
        }
    }
    // for(mod_no = 0; mod_no < BATT_CLUSTER_NUM; mod_no++){
    //     if(isvalid == DATA_VALID){
    //         dev_DC_DC[mod_no].dev_data.valid_flag = DATA_VALID;
    //     }else{
    //         dev_DC_DC[mod_no].dev_data.valid_flag = DATA_INVALID;
    //     }
    // }
}

static void send_start_dev_poll_msg(){
    get_cluster_topology(&cluster_topo);
    if(cluster_topo.topo_valid == TOPO_VALID){
        set_data_validity(DATA_VALID);
        // send_msg_to_sps(START_DEV_POLL_MSG, MOD_DC_DC);
        send_msg_to_sps(START_DEV_POLL_MSG, MOD_BMU);
    }
}

//发送消息给设备模块
static void send_msg_to_sps(unsigned int msg_id, unsigned char dest){
    // module_msg_t msg_send;
    // unsigned char* mod_num;

    // mod_num = (unsigned char*)malloc(sizeof(unsigned char));
    // RETURN_IF_FAIL(mod_num != NULL);

    // msg_send.src = MOD_SAMPLE;
    // msg_send.dest = dest;
    // msg_send.msg_id = msg_id;
    
    // switch (msg_id)
    // {
    //     case START_DEV_POLL_MSG:
    //         if(dest == MOD_BMU){
    //             *mod_num = cluster_topo.mod_count;
    //             msg_send.data = mod_num;
    //         }else{
    //             *mod_num = cluster_topo.cluster_count;
    //             msg_send.data = mod_num;
    //         }
    //         break;

    //     case STOP_DEV_POLL_MSG:
    //         msg_send.data = NULL;
    //         break;
    
    //     default:
    //         break;
    // }

    // send_msg(&msg_send);
}

/**
 * @brief  获取单个电池模块模拟量数据
 * @details
 * @param[in]    mod_no 电池模块编号，取值最大为电池模块数量
 * @param[out]   ana_data 电池模块模拟量数据
 * @retval       数据有效性，0:DATA_INVALID 1:DATA_VALID
 * @note 注解
 * @par 其它
 *      无
 * @par sample code
 * @code
 * @endcode
 * @par 修改日志
 *      2022-01-10, created by 龙明星
 */
/* 不能删除是因为Z:\code1\BCMU\APP\control\addrDistribution\addr_distribution.c有调用，且因为是结构体，无法替换get_one_data() */
#if 0/*ID化屏蔽*/
unsigned char get_batt_mod_ana_data(unsigned char mod_no, batt_mod_ana_data_t* ana_data) {
    BMU_real_data_ana_t* BMU_ana_data = NULL;
    batt_result_t* batt_result;
    
    if (ana_data == NULL || mod_no > BATT_MOD_NUM || mod_no < 1)
        return DATA_INVALID;
    
    if (dev_BMU[mod_no - 1].state != STAT_NORMAL || dev_BMU[mod_no - 1].dev_data.valid_flag == DATA_INVALID)
        return DATA_INVALID;
    
    BMU_ana_data = (BMU_real_data_ana_t*)dev_BMU[mod_no - 1].dev_data.ana_data;
    memcpy(ana_data, BMU_ana_data, sizeof(BMU_real_data_ana_t));
    
    // todo 计算数据赋值
    batt_result = (batt_result_t*)get_batt_result_info(mod_no);
    ana_data->mod_soc = (unsigned short)(batt_result->batt_SOC + 0.5);
    ana_data->mod_soh = batt_result->batt_SOH;
    ana_data->mod_add_life =batt_result->added_life_decline ;
    ana_data->mod_cal_life =batt_result->calender_life_decline ;
    ana_data->mod_cyc_life = batt_result->cycle_life_decline;
   // ana_data->mod_cycle_num = ;
    return DATA_VALID;
}
#endif





/**
 * @brief  获取单个电池簇模拟量数据
 * @details
 * @param[in]    mod_no 电池簇编号，取值最大为电池簇数量
 * @param[out]   ana_data 电池簇模拟量数据
 * @retval       数据有效性，0:DATA_INVALID 1:DATA_VALID
 * @note 注解
 * @par 其它
 *      无
 * @par sample code
 * @code
 * @endcode
 * @par 修改日志
 *      2022-01-10, created by 龙明星
 */
/* 不能删除是因为Z:\code1\BCMU\APP\control\addrDistribution\addr_distribution.c有调用，且因为是结构体，无法替换get_one_data() */
#if 0/*ID化屏蔽*/
unsigned char get_batt_cluster_ana_data(unsigned char mod_no, batt_cluster_ana_data_t* ana_data) {
    //DC_DC_real_data_ana_t* DC_DC_ana_data = NULL;
    
    if (ana_data == NULL || mod_no > BATT_CLUSTER_NUM || mod_no < 1 || mod_no > cluster_topo.cluster_count)
        return DATA_INVALID;
    
    if (dev_DC_DC[mod_no - 1].state != STAT_NORMAL || dev_DC_DC[mod_no - 1].dev_data.valid_flag == DATA_INVALID)
        return DATA_INVALID;
    
    memcpy(ana_data, &s_cluster_ana[mod_no - 1], sizeof(batt_cluster_ana_data_t));
    //DC_DC_ana_data = (DC_DC_real_data_ana_t*)dev_DC_DC[mod_no - 1].dev_data.ana_data;
    memcpy(ana_data, dev_DC_DC[mod_no - 1].dev_data.ana_data, sizeof(DC_DC_real_data_ana_t));
    return DATA_VALID;
}
#endif

/**
 * @brief  获取单个电池簇数字量数据
 * @details
 * @param[in]    mod_no 电池簇编号，取值最大为电池簇数量
 * @param[out]   dig_data 电池簇数字量数据
 * @retval       数据有效性，0:DATA_INVALID 1:DATA_VALID
 * @note 注解
 * @par 其它
 *      无
 * @par sample code
 * @code
 * @endcode
 * @par 修改日志
 *      2022-01-10, created by 龙明星
 */
#if 0/*ID化屏蔽*/
unsigned char get_batt_cluster_dig_data(unsigned char mod_no, batt_cluster_dig_data_t* dig_data){
    DC_DC_real_data_dig_t* DC_DC_dig_data = NULL;
    
    if (dig_data == NULL || mod_no > BATT_CLUSTER_NUM || mod_no < 1)
        return DATA_INVALID;
    
    if (dev_DC_DC[mod_no - 1].state != STAT_NORMAL || dev_DC_DC[mod_no - 1].dev_data.valid_flag == DATA_INVALID)
        return DATA_INVALID;
    
    DC_DC_dig_data = (DC_DC_real_data_dig_t*)dev_DC_DC[mod_no - 1].dev_data.dig_data;
    memcpy(dig_data, DC_DC_dig_data, sizeof(DC_DC_real_data_dig_t));
    // todo 计算数据赋值
    return DATA_VALID;
}
#endif




unsigned char get_batt_cabinet_data(batt_cabinet_data_t* cabinet_data) {
    if (cabinet_data == NULL)
        return DATA_INVALID;

    get_batt_cabinet_dig_data(&cabinet_data->dig_data);
    return DATA_VALID;
}

unsigned char get_batt_cabinet_dig_data(batt_cabinet_dig_t* dig_data) {
    if (dig_data == NULL)
        return DATA_INVALID;

    rt_memcpy_s(dig_data, sizeof(batt_cabinet_dig_t), &cabinet_dig, sizeof(batt_cabinet_dig_t));
    return DATA_VALID;
}

static void send_epo_di_msg (void* arg) {
    module_msg_t msg_send = {MOD_SAMPLE, MOD_CONTROL, EPO_ALARM_MSG, NULL};

    send_msg(&msg_send);

    // 发往告警，进行记录
    msg_send.dest = MOD_ALARM_MANAGE;
    send_msg(&msg_send);
}

static void send_busbar_overtemp_msg (void* arg) {
    // module_msg_t msg_send = {MOD_SAMPLE, MOD_ALARM_MANAGE, DEVICE_SAMPLE_MSG, NULL};
    
    // if (cabinet_dig.busbar_overtemp)
    //     cabinet_dig.busbar_overtemp = 0;
    // else
    //     cabinet_dig.busbar_overtemp = 1; 
    
    // // 发往告警，进行记录
    // msg_send.dest = MOD_ALARM_MANAGE;
    // msg_send.data = rt_malloc(sizeof(unsigned char));
    // *(unsigned char *)msg_send.data = DEV_BCMU;
    // send_msg(&msg_send);
}

// 保存电池簇绝缘电阻阻值
static void save_insulation_resistance()
{
    /** TODO: 重新开发
    unsigned char i = 0;
    float resis_pos, resis_neg;
    
    for(i=0; i<BATT_CLUSTER_NUM; i++)
    {    
        // 计算绝缘电阻
        calc_cluster_insulation_resistance(&resis_pos, &resis_neg, i);
        
        // 保存数据
        //batt_cabinet_data.batt_cluster_data[i].ana_data.insulation_resistance.pos_value = resis_pos;
        set_one_data((BCMU_DATA_ID_POSITIVE_INSULATION_RESISTANCE + i),&resis_pos);
       // batt_cabinet_data.batt_cluster_data[i].ana_data.insulation_resistance.neg_value = resis_neg;
        set_one_data((BCMU_DATA_ID_NEGATIVE_INSULATION_RESISTANCE + i),&resis_neg);
    }
    */
    return;
}

/* 设置时间同步告警状态 */
void set_time_sync_alm_state(unsigned char value) {
    cabinet_dig.time_sync_alm = value;
}

unsigned char get_time_sync_alm_state(void) {
    return cabinet_dig.time_sync_alm;
}


