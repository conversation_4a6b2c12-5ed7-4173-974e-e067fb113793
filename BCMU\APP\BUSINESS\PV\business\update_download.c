/*
 * @file    : update_download.c
 * @brief   : 远程下载协议
 * @details : 
 * <AUTHOR> wsz
 * @Date    : 2023-04-12
 * @LastEditTime: 
 * @version : V0.0.1
 * @para    : Copyright (c) 
 *            ZTE Corporation
 * @par     : History
 *     version: author, date, descn
 */

#include <string.h>
#include "sps.h"
#include "cmd.h"
#include "download.h"
#include "protocol_layer.h"


/* 设备缓冲区长度 */
#define R_COM_BUFF_LEN                    2048     ///<  接收缓冲区长度（串口链路）
#define S_COM_BUFF_LEN                    5120     ///<  发送缓冲区长度（串口链路）

/* 命令请求 */
static download_cmd_head_t cmd_req[] = {
    /*  触发帧  */
    {CMD_TRIG_FRAME_TYPE_H, CMD_TRIG_FRAME_TYPE_L, CMD_TRIG_CHIP_TYPE_H, CMD_TRIG_CHIP_TYPE_L,},    // 0         单机场景下， 普通触发帧
    /*  数据/命令帧  */
    {CMD_DATA_DOWNLOAD,          CMD_DATA_NONE, CMD_DATA_NONE, CMD_DATA_NONE,},                     // 1
    {CMD_DATA_DOWNLOAD_EXTEND,   CMD_DATA_NONE, CMD_DATA_NONE, CMD_DATA_NONE,},                     // 2
    {CMD_DATA_VERSION_CMP,       CMD_DATA_NONE, CMD_DATA_NONE, CMD_DATA_NONE,},                     // 3
    {CMD_DATA_STOP_UPDATE,       CMD_DATA_NONE, CMD_DATA_NONE, CMD_DATA_NONE,},                     // 4
    {CMD_DATA_GET_FLAG,          CMD_DATA_NONE, CMD_DATA_NONE, CMD_DATA_NONE,},                     // 5
    {CMD_DATA_GET_UPDATE_PROG,   CMD_DATA_NONE, CMD_DATA_NONE, CMD_DATA_NONE,},                     // 6
    {CMD_DATA_GET_FRAME_MAX_LEN, CMD_DATA_NONE, CMD_DATA_NONE, CMD_DATA_NONE,},                     // 7
    {CMD_DATA_SET_FRAME_MAX_LEN, CMD_DATA_NONE, CMD_DATA_NONE, CMD_DATA_NONE,},                     // 8
    {CMD_DATA_UPLOAD_FILE_LIST,  CMD_DATA_NONE, CMD_DATA_NONE, CMD_DATA_NONE,},                     // 9
    {CMD_DATA_UPLOAD_FILE_DATA,  CMD_DATA_NONE, CMD_DATA_NONE, CMD_DATA_NONE,},                     // 10
    {CMD_TRIG_FRAME_TYPE_H, CMD_TRIG_FRAME_TYPE_L, CMD_TRIG_CHIP_TYPE_H, CMD_TRIG_CHIP_TYPE_L,},    // 11        单机场景下， 扩展触发帧
    {CMD_DATA_CONCUR_UPDATE_DATA,   CMD_DATA_NONE, CMD_DATA_NONE, CMD_DATA_NONE,},                     // 12
    {CMD_DATA_CONCUR_TRANSFER_CONFIRM,    CMD_DATA_NONE, CMD_DATA_NONE, CMD_DATA_NONE,},                      // 13
    {CMD_DATA_CONCUR_CONFIRM_UPDATE,  CMD_DATA_NONE, CMD_DATA_NONE, CMD_DATA_NONE,},                      // 14
    {0}
};

/* 命令应答   */
static download_cmd_head_t cmd_ack[] = {
    {CMD_TRIG_FRAME_TYPE_H, CMD_TRIG_FRAME_TYPE_L, CMD_TRIG_CHIP_TYPE_H, CMD_TRIG_VERSION},
};


/* 通信协议命令表 */
static cmd_t no_poll_cmd_tab[] = {
    {DOWNLOAD_DATA_TRIG,             CMD_PASSIVE,  &cmd_req[0], &cmd_ack[0], DOWNLOAD_CMD_HEADLEN, },
    {DOWNLOAD_DATA_DOWNLOAD,         CMD_PASSIVE,  &cmd_req[1], &cmd_req[1], DOWNLOAD_CMD_HEADLEN, },
    {DOWNLOAD_DATA_DOWNLOAD_EXTEND,  CMD_PASSIVE,  &cmd_req[2], &cmd_req[2], DOWNLOAD_CMD_HEADLEN, },
    {DOWNLOAD_VERSION_CMP,           CMD_PASSIVE,  &cmd_req[3], &cmd_req[3], DOWNLOAD_CMD_HEADLEN, },
    {DOWNLOAD_STOP_UPDATE,           CMD_PASSIVE,  &cmd_req[4], &cmd_req[4], DOWNLOAD_CMD_HEADLEN, },
    {DOWNLOAD_GET_FLAG,              CMD_PASSIVE,  &cmd_req[5], &cmd_req[5], DOWNLOAD_CMD_HEADLEN, },
    {DOWNLOAD_GET_UPDATE_PROG,       CMD_PASSIVE,  &cmd_req[6], &cmd_req[6], DOWNLOAD_CMD_HEADLEN, },
    {DOWNLOAD_GET_FRAME_MAX_LEN,     CMD_PASSIVE,  &cmd_req[7], &cmd_req[7], DOWNLOAD_CMD_HEADLEN, },
    {DOWNLOAD_SET_FRAME_MAX_LEN,     CMD_PASSIVE,  &cmd_req[8], &cmd_req[8], DOWNLOAD_CMD_HEADLEN, },
    {UPLOAD_FILE_LIST,               CMD_PASSIVE,  &cmd_req[9], &cmd_req[9], DOWNLOAD_CMD_HEADLEN, },
    {UPLOAD_FILE_DATA,               CMD_PASSIVE,  &cmd_req[10], &cmd_req[10], DOWNLOAD_CMD_HEADLEN, },
    {DOWNLOAD_EXTERN_TRIG,           CMD_PASSIVE,  &cmd_req[11],&cmd_ack[0], DOWNLOAD_CMD_HEADLEN, },
    {DOWNLOAD_CONCUR_DATA,           CMD_PASSIVE,  &cmd_req[12],&cmd_req[12], DOWNLOAD_CMD_HEADLEN,},
    {DOWNLOAD_CONCUR_CONFIRM_DATA,   CMD_PASSIVE,  &cmd_req[13],&cmd_req[13], DOWNLOAD_CMD_HEADLEN,},
    {DOWNLOAD_CONCUR_CONFIRM_UPDATE, CMD_PASSIVE,  &cmd_req[14],&cmd_req[14], DOWNLOAD_CMD_HEADLEN,},
    {0},
};


static dev_type_t dev_inverter = {
    DEV_DAC_NORTH_DOWNLOAD, 1, PROTOCOL_DOWNLOAD, LINK_INVERTER, R_COM_BUFF_LEN, S_COM_BUFF_LEN, 0, no_poll_cmd_tab, NULL
};

static dev_type_t dev_inverter_download_IP = {             // 收发端填1，仅仅是为了malloc申请成功，实际的收发buff长度不在这里控制
    DEV_DAC_NORTH_DOWNLOAD_IP, 1, PROTOCOL_DOWNLOAD, LINK_DAC_IP, 1, 1, BOTTOM_CSU_TYPE, no_poll_cmd_tab, NULL, 0,
};

dev_type_t* init_dev_inverter(void) {
    return &dev_inverter;
}

dev_type_t* init_dev_download_IP(void)
{
    return &dev_inverter_download_IP;
}



