#ifndef CONCURRENT_UPGRADE_H_
#define CONCURRENT_UPGRADE_H_

#ifdef __cplusplus
extern "C" {
#endif

#define WRITE_OFFSET              256        // 写入地址的偏移量


/* 并发升级过程中各个状态 */
typedef enum {
    PARALLE_STAT_PREPARE,                ///< 升级初始状态
    PARALLE_STAT_TRANS,                  ///< 数据传输阶段
    PARALLE_STAT_DATA_CONFIRM,           ///< 数据传输完成确认阶段
    PARALLE_STAT_UPDATE_CONFIRM,         ///< 升级确认阶段
} T_ParalleState;

int concur_update_init();
boolean get_trigger_flag(void);
void check_restart_delay(void);

#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // CONCURRENT_UPGRADE_H_;
