
#ifndef _PARALLEL_UPDATE_DOWNLOAD_HANDLE_H_
#define _PARALLEL_UPDATE_DOWNLOAD_HANDLE_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "device_type.h"

#define PARAEELE_UPDATE_DATA_LEN 512

// 触发帧
int parse_master_trig_data(void* dev_inst, void* cmd_buf);
int pack_master_trig_data(void* dev_inst, void* cmd_buf);
// 首帧及数据帧
int pack_master_first_data_download(cmd_buf_t* tran_cmd_buf);
int pack_master_data_download(cmd_buf_t* tran_cmd_buf);

int parse_master_update_data(void* dev_inst, void* cmd_buf);
int pack_master_update_data(void* dev_inst, void* cmd_buf);

int start_parallel_update();
int send_parallel_update_data_download();

int get_parallel_update_trig_flag();
int get_parallel_update_slave_trig_flag();
void clear_parallel_update_slave_trig_flag();
int get_parallel_update_slave_trig_times();
int set_update_file_info();
int parallel_update_init();
void parallel_timeout();
#ifdef __cplusplus
}
#endif

#endif  
