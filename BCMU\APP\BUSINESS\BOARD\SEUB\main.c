#include <stdio.h>
#include <rtthread.h>
#include "north_main.h"
#include "sample.h"
#include "utils_server.h"
#include "server_id.h"
#include "storage.h"
#include "partition_table.h"
#include "protocol_modbus_comm.h"
#include "dev_north_seub_modbus.h"
#include "dev_north_seub_apptest.h"
#include "protocol_north_modbus.h"
#include "watchdog.h"
#include "utils_data_transmission.h"
#include "utils_rtthread_security_func.h"
#include "realdata_save.h"
#include "realdata_id_in.h"
#include "software_version.h"
#include "led.h"
#include "protocol_layer.h"
#include "protocol_modbus_tcp.h"
#include "wireless_protocol_main.h"
#include "ee_public_info.h"
#include "remote_download_update_cmd.h"
#include "app_config.h"
#include "protocol_remote_download.h"
#include "remote_download_update_handle.h"
#include "protocol_north_modbus.h"

static char s_north_thread_stack[4096];
static char s_sample_thread_stack[2048];
static char s_led_thread_stack[2048];
static char s_net_wifi_thread_stack[4096];
static net_thread_data_t s_net_thread_data[CONN_MAX];  /*前三块是1363线程，第四块是update线程*/
static rt_device_t wdg_dev;
Static int init_mac_addr_info(void);
softbus_mempool_all_t softbus_mempool_info;
rt_uint8_t mempool_32[BLOCK_COUNT_32 * BLOCK_SIZE_32];
rt_uint8_t mempool_64[BLOCK_COUNT_64 * BLOCK_SIZE_64];
rt_uint8_t mempool_512[BLOCK_COUNT_512 * BLOCK_SIZE_512];

#define UPDATE_FILE_MANGGE_ADDR_OFFSET  0x00007200
#define ADDR_CONSTRAIN     0xF7

static server_info_t g_server_group[] = {
    /*   服务ID              服务名字        栈大小                           栈的起始地址                优先级*/
    {{{NORTH_SERVER_ID,      "north",        sizeof(s_north_thread_stack),    s_north_thread_stack,       SERVER_PRIO_LOW}}, init_north,               north_comm_th},
    {{{SAMPLE_SERVER_ID,     "sample",       sizeof(s_sample_thread_stack),   s_sample_thread_stack,      SERVER_PRIO_LOW}}, sample_init_sys,          sample_main},
    {{{LED_SERVER_ID,        "signal_led",   sizeof(s_led_thread_stack),      s_led_thread_stack,         SERVER_PRIO_LOW}}, init_led_blink,           led_thread_entry},
    {{{WIFI_SERVER_ID,       "wifi_mgr",     sizeof(s_net_wifi_thread_stack), s_net_wifi_thread_stack,    SERVER_PRIO_LOW}}, init_wired_server_listen, wired_network_listen_thread},
};

static protocol_process_t protocol_process_tab[] = {
    {PROTOCOL_MODBUS_RTU, north_modbus_pack, north_modbus_parse},
    {PROTOCOL_MODBUS_TCP, modbus_tcp_pack, modbus_tcp_parse},
    {PROTOCOL_YD_1363, s1363_pack, s1363_parse},
    {PROTOCOL_DOWNLOAD, download_pack, download_parse},
};

static dev_init_t dev_init_tab[] = {
    {DEV_NORTH_SEUB, init_dev_north_seub, NULL},
    {DEV_MODBUS_TCP_SEUB, init_dev_modbus_tcp_seub, NULL},
    {DEV_NORTH_SEUB_APPTEST, init_seub_dev_apptest, NULL},
    {DEV_REMOTE_DOWNLOAD, init_remote_download_dev_type, NULL},
};

static link_type_t link_type_tab[] = {
     {LINK_COM, s485_dev_init, com_dev_read, com_dev_write, com_dev_set},
     {LINK_IP_WIRED, ip_dev_init, ip_dev_read, ip_dev_write, ip_dev_set},
};

static link_inst_t link_inst_tab[] = {
    {LINK_NORTH_SEUB,   LINK_COM, "usart2"},     //串口模式
    {LINK_DAC_IP,       LINK_IP_WIRED,  "wire1"},
};

download_file_name_info_t download_file_name[] = {{0, "SEUB_app.bin", DOWNLOAD_PART}};
update_info_save_t update_info_save = {APP_RUN_PART, UPDATE_FILE_MANGGE_ADDR_OFFSET};

// SEUB网卡上的MAC地址无法掉电保存，需要在初始化时读取EEPROM上保存的MAC地址并设置给网卡
Static int init_mac_addr_info(void)
{
    mac_addr_info_t mac_addr_info_eeprom = {0};
    mac_addr_info_t mac_addr_info_default =
    {
        .mac_addr = {0x00, 0x00, 0x0e, 0x12, 0x34, 0x88}, // 默认6字节MAC地址
        .crc = crc_cal((unsigned char* )&mac_addr_info_default, offsetof(mac_addr_info_t, crc)) // 默认2字节CRC
    };
    unsigned char mac_addr_info_all_zero[SEUB_MAC_ADDR_LEN] = {0x00, 0x00, 0x00, 0x00, 0x00, 0x00};
    unsigned short mac_addr_crc = 0x0000;

    rt_device_t ETH_dev = rt_device_find("e0");
    RETURN_VAL_IF_FAIL(RT_NULL != ETH_dev, FAILURE);

    eeprom_data_process((unsigned char *)&mac_addr_info_eeprom, read_opr, SEUB_MAC_ADDR_LEN + 2, SEUB_MAC_ADDR_INFO_OFFSET);
    mac_addr_crc = crc_cal((unsigned char* )&mac_addr_info_eeprom, offsetof(mac_addr_info_t, crc));

    if(mac_addr_crc != mac_addr_info_eeprom.crc)
    {
        // 主MAC地址信息有错误，则读取备份MAC地址信息
        eeprom_data_process((unsigned char *)&mac_addr_info_eeprom, read_opr, SEUB_MAC_ADDR_LEN + 2, SEUB_MAC_ADDR_BACKUP_INFO_OFFSET);
        mac_addr_crc = crc_cal((unsigned char* )&mac_addr_info_eeprom, offsetof(mac_addr_info_t, crc));

        if(mac_addr_crc != mac_addr_info_eeprom.crc)
        {
            // 备份MAC地址信息也有错误（可能是全新的板子也可能是备份MAC地址存储错误），则只能恢复默认配置
            eeprom_data_process((unsigned char *)&mac_addr_info_default, write_opr, SEUB_MAC_ADDR_LEN + 2, SEUB_MAC_ADDR_INFO_OFFSET);
            eeprom_data_process((unsigned char *)&mac_addr_info_default, write_opr, SEUB_MAC_ADDR_LEN + 2, SEUB_MAC_ADDR_BACKUP_INFO_OFFSET);
            rt_device_control(ETH_dev, NIOCTL_SADDR, mac_addr_info_default.mac_addr);
            return SUCCESSFUL;
        }
        else
        {
            // 备份MAC地址信息有效时，恢复主区域的MAC地址信息
            eeprom_data_process((unsigned char *)&mac_addr_info_eeprom, write_opr, SEUB_MAC_ADDR_LEN + 2, SEUB_MAC_ADDR_INFO_OFFSET);
            rt_device_control(ETH_dev, NIOCTL_SADDR, mac_addr_info_eeprom.mac_addr);
            return SUCCESSFUL;
        }
    }

    // 检查MAC地址有效性（首字节为0xFF或者全零MAC地址为非法）
    if ((0xFF == mac_addr_info_eeprom.mac_addr[0]) || (0 == rt_memcmp(mac_addr_info_all_zero, mac_addr_info_eeprom.mac_addr, SEUB_MAC_ADDR_LEN)))
    {
        eeprom_data_process((unsigned char *)&mac_addr_info_default, write_opr, SEUB_MAC_ADDR_LEN + 2, SEUB_MAC_ADDR_INFO_OFFSET);
        eeprom_data_process((unsigned char *)&mac_addr_info_default, write_opr, SEUB_MAC_ADDR_LEN + 2, SEUB_MAC_ADDR_INFO_OFFSET);
        rt_device_control(ETH_dev, NIOCTL_SADDR, mac_addr_info_default.mac_addr);
        return SUCCESSFUL;
    }

    rt_device_control(ETH_dev, NIOCTL_SADDR, mac_addr_info_eeprom.mac_addr);
    return SUCCESSFUL;
}

static void feed_dog(void)
{
    rt_device_control(wdg_dev, RT_DEVICE_CTRL_WDT_KEEPALIVE, NULL);
    return;
}

int init_watchdog(void)
{
    wdg_dev = rt_device_find("wdt");
    if (RT_NULL == wdg_dev)
    {
        return -RT_ERROR;
    }
    if (RT_EOK != rt_device_control(wdg_dev, RT_DEVICE_CTRL_WDT_START, RT_NULL))
    {
        return -RT_ERROR;
    }
    rt_thread_idle_sethook(feed_dog);

    return RT_EOK;
}

int change_update_flag(unsigned char ucMode)
{
    update_file_manage_t ptFileManage;
    rt_memset_s(&ptFileManage, sizeof(update_file_manage_t), 0xFF, sizeof(update_file_manage_t));
    read_download_tmpInfo(&ptFileManage);
    ptFileManage.ucFlag = ucMode;
    ptFileManage.crc = crc_cal((unsigned char *)&ptFileManage, offsetof(update_file_manage_t, crc));
    write_download_tmpInfo(&ptFileManage);
    return SUCCESSFUL;
}

static void init_softbus_config(void)
{
    softbus_mempool_info.mempool_32.mempool_addr = &mempool_32[0];
    softbus_mempool_info.mempool_32.mempool_size = sizeof(mempool_32);
    softbus_mempool_info.mempool_32.block_size = BLOCK_SIZE_32;

    softbus_mempool_info.mempool_64.mempool_addr = &mempool_64[0];
    softbus_mempool_info.mempool_64.mempool_size = sizeof(mempool_64);
    softbus_mempool_info.mempool_64.block_size = BLOCK_SIZE_64;

    softbus_mempool_info.mempool_512.mempool_addr = &mempool_512[0];
    softbus_mempool_info.mempool_512.mempool_size = sizeof(mempool_512);
    softbus_mempool_info.mempool_512.block_size = BLOCK_SIZE_512;
    return ;
}

int init_device_addr()
{
    device_addr_t addr_temp = {0}; // 从EEPROM上读取的地址信息
    unsigned short addr_temp_crc = 0;
    device_addr_t default_addr = {0}; // 默认的地址信息
    default_addr.device_addr = 0x0001;
    default_addr.crc = crc_cal((unsigned char *)&default_addr, offsetof(device_addr_t, crc));

    // 读取主地址信息
    eeprom_data_process((unsigned char *)&addr_temp, read_opr, sizeof(device_addr_t), DEVICE_ADDR_OFFSET);
    addr_temp_crc = crc_cal((unsigned char *)&addr_temp, offsetof(device_addr_t, crc));

    if(addr_temp_crc != addr_temp.crc)
    {
        // 从主地址信息有错误，则读取备份的地址
        eeprom_data_process((unsigned char *)&addr_temp, read_opr, sizeof(device_addr_t), DEVICE_ADDR_BACKUP_OFFSET);
        addr_temp_crc = crc_cal((unsigned char *)&addr_temp, offsetof(device_addr_t, crc));

        if(addr_temp_crc != addr_temp.crc)
        {
            // 备份地址也无效（可能是全新的板子，也可能是写入备份地址时出现问题），恢复默认地址
            eeprom_data_process((unsigned char *)&default_addr, write_opr, sizeof(device_addr_t), DEVICE_ADDR_OFFSET);
            eeprom_data_process((unsigned char *)&default_addr, write_opr, sizeof(device_addr_t), DEVICE_ADDR_BACKUP_OFFSET);
        }
        else
        {
            // 备份地址有效时恢复主地址
            eeprom_data_process((unsigned char *)&addr_temp, write_opr, sizeof(device_addr_t), DEVICE_ADDR_OFFSET);
        }
    }

    if(addr_temp.device_addr == 0 || addr_temp.device_addr >= ADDR_CONSTRAIN)
    {
        // CRC校验通过但是地址本身不合法（极小概率出现），也恢复默认地址
        eeprom_data_process((unsigned char *)&default_addr, write_opr, sizeof(device_addr_t), DEVICE_ADDR_OFFSET);
        eeprom_data_process((unsigned char *)&default_addr, write_opr, sizeof(device_addr_t), DEVICE_ADDR_BACKUP_OFFSET);
    }

    return SUCCESSFUL;
}

int main(void)
{
    init_watchdog();
    init_crc();
    init_softbus_config();
    softbus_init(&softbus_mempool_info);
    if (SUCCESSFUL != storage_init(&part_tab[0], sizeof(part_tab)/sizeof(part_info_t)))
    {
        return FAILURE;
    }
    init_mac_addr_info();
    init_real_data_memory();
    init_device_addr();
    // 注册IP线程的函数指针
    register_ip_server_func(wireless_protocol);
    // 注册net_thread的栈空间内存
    register_net_thread_data(s_net_thread_data);
    register_link_inst_tab_info(link_inst_tab, sizeof(link_inst_tab)/sizeof(link_inst_tab[0]));
    register_link_type_info(link_type_tab, sizeof(link_type_tab) / sizeof(link_type_t));
    // 注册协议所使用的函数接口
    register_protocol_process_info(protocol_process_tab, sizeof(protocol_process_tab) / sizeof(protocol_process_t));
    // 注册协议
    init_dev_tab(dev_init_tab, sizeof(dev_init_tab)/sizeof(dev_init_tab[0]));
    /*
    hal_board_gpio_init();
    board_addr = get_board_addr();
    update_manage_init();
    */
    // Init_dxcb_do_check();
    create_server(&g_server_group[0], sizeof(g_server_group)/sizeof(g_server_group[0]));
    // 注册远程下载协议支持的文件名称
    register_download_file_name(download_file_name, sizeof(download_file_name) / sizeof(download_file_name_info_t), &update_info_save);
    change_update_flag(FLAG_OK);
    return 0;
}
