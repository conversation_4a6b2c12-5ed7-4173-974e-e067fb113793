#include "dev_can_comm.h"

/* 命令请求 */
static remote_comm_cmd_head_t cmd_req[] RAM_SECTION = {
    {VER_01, CMD_HAND_SHAKE},          // 1
    {VER_01, <PERSON><PERSON>_HAND_SHAKE_RESULT},   // 2
    {VER_01, C<PERSON>_PARA_CONF},           // 3
    {VER_01, CMD_PARA_CONF_RESULT},    // 4
    {VER_01, CMD_GET_CHARGE_STATUS},   // 5
    {VER_01, CMD_CHARGE_END},          // 6
    {VER_01, CMD_CHARGE_STAT},         // 7
    {0}
};

/* 命令应答   */
static remote_comm_cmd_head_t cmd_ack[] RAM_SECTION = {
    {VER_01, C<PERSON>_HAND_SHAKE},          // 1
    {VER_01, CMD_HAND_SHAKE_RESULT},   // 2
    {VER_01, CMD_PARA_CONF},           // 3
    {VER_01, <PERSON><PERSON>_PARA_CONF_RESULT},    // 4
    {VER_01, C<PERSON>_GET_CHARGE_STATUS},   // 5
    {VER_01, CMD_CHARGE_END},          // 6
    {VER_01, <PERSON><PERSON>_CHARGE_STAT},         // 7
    {0}
};

/* 握手 */
static data_info_id_verison_t hand_shake[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, HAND_SHAKE_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CHARGE_PERMIT_FLAG},
    {SEG_DATA, type_string, ARRAY_SIZE_15, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_DEVICE_SERIAL_NUMBER},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_PROTOCOL_VER},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CHARGER_TYPE},
};

static data_info_id_verison_t hand_shake_ack[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, HAND_SHAKE_ACK_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CHARGE_PERMIT_FLAG},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_AUTO_CHARGE_PORT_ALIGNMENT_STATUS},
    {SEG_DATA, type_string, ARRAY_SIZE_15, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_DEVICE_SERIAL_NUMBER},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_PROTOCOL_VER},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BATTERY_TYPE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BATTERY_CAPACITY},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_VOLTAGE_SPECIFICATION},
};

/* 握手结果 */
static data_info_id_verison_t hand_shake_result[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, HAND_SHAKE_RESULT_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_HANDSHAKE_RESULT},
};

/* 参数配置 */
static data_info_id_verison_t para_conf[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_CONF_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CHARGER_MAX_CHARGE_VOL},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CHARGER_MIN_CHARGE_VOL},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CHARGER_MAX_CHARGE_CUR},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CHARGER_MIN_CHARGE_CUR},
};

static data_info_id_verison_t para_conf_ack[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_CONF_ACK_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CELL_MAX_ALLOW_CHARGE_VOL},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_MAX_ALLOW_CHARGE_CUR},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CURR_MAX_ALLOW_CHARGE_CUR},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BATTERY_NOMINAL_TOTAL_ENERGY},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_MAX_ALLOW_TOTAL_CHARGE_VOL},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_MAX_ALLOW_TEMP},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_SOC},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CURRENT_VOLTAGE},
};

/* 参数配置结果 */
static data_info_id_verison_t para_conf_result[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_CONF_RESULT_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CONFIG_RESULT},
};

/* 充电状态 */
static data_info_id_verison_t charge_status[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, CHARGE_STATUS_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CURRENT_OUTPUT_VOLTAGE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CURRENT_OUTPUT_CURRENT},
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_ACCUMULATED_CHARGE_TIME},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CHARGE_PROHIBIT_STATUS},
};

static data_info_id_verison_t charge_status_ack[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, CHARGE_STATUS_ACK_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CHARGE_REQUEST_VOLTAGE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CHARGE_REQUEST_CURRENT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CHARGE_MODE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BATTERY_CURRENT_TOTAL_VOLTAGE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BATTERY_CURRENT_TOTAL_CURRENT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CURRENT_SOC},
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_REMAIN_CHARGE_TIME},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CELL_MAX_VOLTAGE},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CELL_MAX_VOLTAGE_GROUP_NUMBER},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CELL_MIN_VOLTAGE},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CELL_MIN_VOLTAGE_GROUP_NUMBER},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CELL_MAX_TEMPERATURE},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CELL_MAX_TEMPERATURE_GROUP_NUMBER},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CELL_MIN_TEMPERATURE},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CELL_MIN_TEMPERATURE_GROUP_NUMBER},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CHARGE_PROHIBIT_STATUS},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CHARGER_START_PERMIT},
};

/* 充电终止 */
static data_info_id_verison_t charge_end[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, CHARGE_END_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CHARGE_TERMINATE_REASON},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CHARGE_TERMINATE_FAULT_REASON},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CHARGE_TERMINATE_ERROR_REASON},
};

static data_info_id_verison_t charge_end_ack[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, CHARGE_END_ACK_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CHARGE_TERMINATE_REASON},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CHARGE_TERMINATE_FAULT_REASON},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CHARGE_TERMINATE_ERROR_REASON},
};

/* 充电统计 */
static data_info_id_verison_t charge_stat[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, CHARGE_STAT_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CURRENT_CHARGE_TIME},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_OUTPUT_ENERGY},
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CHARGER_NUMBER},
};

static data_info_id_verison_t charge_stat_ack[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, CHARGE_STAT_ACK_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CHARGE_TERMINATE_SOC},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CELL_MAX_VOLTAGE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CELL_MIN_VOLTAGE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CELL_MAX_TEMPERATURE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CELL_MIN_TEMPERATURE},
};

/* 打包命令 */
static cmd_parse_info_id_verison_t cmd_pack_info[] = {
    {hand_shake_ack,  sizeof(hand_shake_ack)/sizeof(data_info_id_verison_t)},  // 1
    {para_conf_ack,  sizeof(para_conf_ack)/sizeof(data_info_id_verison_t)},  // 2
    {charge_status_ack,  sizeof(charge_status_ack)/sizeof(data_info_id_verison_t)},  // 3
    {charge_end_ack,  sizeof(charge_end_ack)/sizeof(data_info_id_verison_t)},  // 4
    {charge_stat_ack,  sizeof(charge_stat_ack)/sizeof(data_info_id_verison_t)},  // 5
};

/* 解包命令 */
static cmd_parse_info_id_verison_t cmd_parse_info[] = {
    {hand_shake,  sizeof(hand_shake)/sizeof(data_info_id_verison_t)},  // 1
    {hand_shake_result,  sizeof(hand_shake_result)/sizeof(data_info_id_verison_t)},  // 2
    {para_conf,  sizeof(para_conf)/sizeof(data_info_id_verison_t)},  // 3
    {para_conf_result,  sizeof(para_conf_result)/sizeof(data_info_id_verison_t)},  // 4
    {charge_status,  sizeof(charge_status)/sizeof(data_info_id_verison_t)},  // 5
    {charge_end,  sizeof(charge_end)/sizeof(data_info_id_verison_t)},  // 6
    {charge_stat,  sizeof(charge_stat)/sizeof(data_info_id_verison_t)},  // 7
};


/* 通信协议命令表 */
static cmd_t no_poll_cmd_tab[] = {
    {HAND_SHAKE, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(remote_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[0], &cmd_parse_info[0]},
    {HAND_SHAKE_RESULT, CMD_PASSIVE, &cmd_req[1], &cmd_ack[1], sizeof(remote_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[1]},
    {PARA_CONF, CMD_PASSIVE, &cmd_req[2], &cmd_ack[2], sizeof(remote_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[1], &cmd_parse_info[2]},
    {PARA_CONF_RESULT, CMD_PASSIVE, &cmd_req[3], &cmd_ack[3], sizeof(remote_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[3]},
    {GET_CHARGE_STATUS, CMD_PASSIVE, &cmd_req[4], &cmd_ack[4], sizeof(remote_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[2], &cmd_parse_info[4]},
    {CHARGE_END, CMD_PASSIVE, &cmd_req[5], &cmd_ack[5], sizeof(remote_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[3], &cmd_parse_info[5]},
    {CHARGE_STAT, CMD_PASSIVE, &cmd_req[6], &cmd_ack[6], sizeof(remote_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[4], &cmd_parse_info[6]},
    {0},
};

int register_charger_tab()
{
    return register_cmd_tab(no_poll_cmd_tab, sizeof(no_poll_cmd_tab)/ sizeof(cmd_t));
}
