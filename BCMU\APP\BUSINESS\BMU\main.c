#include <stdio.h>
#include <rtthread.h>
#include <rtdevice.h>
#include "data_type.h"
#include "utils_thread.h"
#include "utils_time.h"
#include "CommCan.h"
#include "concurrent_upgrade.h"
#include "update_manage.h"
#include "msg_id.h"
#include "utils_data_transmission.h"
#include "backup_manage.h"
#include "protocol_bottom_comm.h"
#include "partition_table.h"
#include "utils_server.h"
#include "server_id.h"
#include "main.h"
#include "storage.h"
#include "protocol_layer.h"
#include "circle_buff.h"
#include "file_save.h"
#include "alarm_register.h"
#include "para_manage.h"
#include "realdata_save.h"
#include "protocol_short_comm.h"
#include "dev_bcmu.h"
#include "sample_new.h"
#include "alarm_register.h"
#include "alarm_mgr_api.h"
#include "circle_buff.h"
#include "file_save.h"
#include "unified_id_interface.h"

static char s_north_thread_stack[NORTH_THREAD_STACK_SIZE] RAM_SECTION_BSS;
static char s_north_save_thread_stack[NORTH_THREAD_STACK_SIZE] RAM_SECTION_BSS;
static char s_south_thread_stack[NORTH_THREAD_STACK_SIZE] RAM_SECTION_BSS;
static char s_led_thread_stack[LED_THREAD_STACK_SIZE] RAM_SECTION_BSS;
static char s_sample_thread_stack[SAMPLE_THREAD_STACK_SIZE] RAM_SECTION_BSS;
static char s_alarm_thread_stack[ALARM_THREAD_STACK_SIZE] RAM_SECTION_BSS;

static protocol_process_t protocol_process_tab[] = {
    {PROTOCOL_BOTTOM_COMM, bottom_pack, bottom_parse},
    {PROTOCOL_SHORT_FRM, short_frm_pack, short_frm_parse},
};

static dev_init_t dev_init_tab[] = {
    {DEV_BCMU, init_dev_bcmu, NULL},
};

static link_type_t link_type_tab[LINK_TYPE_MAX] = {
    {LINK_CAN, can_dev_init, can_dev_read, can_dev_write, can_dev_set},
};

static link_inst_t link_inst_tab[] = {
    {BMU_LINK_BCMU, LINK_CAN, "can1",   CAN_FRAME_DATA_LEN_8},
    
};

static server_info_t g_server_group[] = {
    {{{NORTH_SERVER_ID,   "north",  sizeof(s_north_thread_stack),  s_north_thread_stack,  SERVER_PRIO_MID}}, init_can_comm, north_thread},
    {{{NORTH_SAVE_SERVER_ID  ,"north_save",      sizeof(s_north_save_thread_stack),    s_north_save_thread_stack,    SERVER_PRIO_MID}}, init_file_save,  process_save_data},
    {{{SAMPLE_SERVER_ID,   "sample",  sizeof(s_sample_thread_stack),  s_sample_thread_stack,  SERVER_PRIO_MID}}, init_sampling, sample_main_new},
    {{{ALARM_SERVER_ID,    "alarm",   sizeof(s_alarm_thread_stack),  s_alarm_thread_stack,  SERVER_PRIO_MID}}, init_alarm_manage, alarm_main},
};

char init_dev_link(void)
{
    char ret = 0;
    register_link_type_info(link_type_tab, sizeof(link_type_tab) / sizeof(link_type_t));
    register_link_inst_tab_info(link_inst_tab, sizeof(link_inst_tab)/sizeof(link_inst_tab[0]));
    register_protocol_process_info(protocol_process_tab, sizeof(protocol_process_tab) / sizeof(protocol_process_t));
    ret = init_dev_tab(dev_init_tab, sizeof(dev_init_tab)/sizeof(dev_init_tab[0]));
    return ret;
}

// 定义接口表
static const data_access_interface_t id_base_interface = {
    .get_data = unified_get_data,
    .set_data = unified_set_data,    
    .linear_search = linear_search_id_index
};

int main(void){
    init_flash_page_size(FLASH_PAGE_SIZE);
    register_data_access_interface(&id_base_interface);
    init_crc();
    if (SUCCESSFUL != storage_init(&part_tab[0], sizeof(part_tab)/sizeof(part_info_t))) {
        return FAILURE;
    }
    initFileSys();
    init_circle_buff();
    register_bmu_alarm();
    register_product_para();
    init_real_data_memory();
    init_para_manage();
    init_bmu_soft_version();
    syswatch_init();
    init_dev_link();
    update_manage_init();
    create_server(&g_server_group[0], sizeof(g_server_group)/sizeof(g_server_group[0]));
    // rt_thread_delay(10000);
    BeginDownload(FLAG_OK);
    return SUCCESSFUL;
}
