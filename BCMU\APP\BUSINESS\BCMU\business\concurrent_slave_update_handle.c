#include "concurrent_slave_update_handle.h"
#include "update_download_manage.h"
#include "realdata_id_in.h"
#include "protocol_remote_download.h"
#include "partition_def.h"
#include "utils_frame_loss_statistic.h"
#include "utils_data_transmission.h"
#include "utils_rtthread_security_func.h"
#include "sps.h"
#include "thread_id.h"
#include "utils_heart_beat.h"
#include "his_data.h"
#include "msg_id.h"
#include "realdata_save.h"


download_trig_ctr_inf_t g_concurr_update_trig_info = {0};
update_file_attr_t g_concur_file_info = {0};// 首发帧文件信息
unsigned short g_last_recive_frm_no = 0xffff;
unsigned short g_req_frm_no  = 0;
unsigned char g_concur_update_stat = NO_UPDATE;
rt_timer_t g_concurr_update_timer = NULL;
static unsigned char extern_trig_frame_ack[] = {
    0x30,0x31,0x34,0x30,0x45,0x45,0x35,
    0x36,0x35,0x42,0x41,0x36,0x41,0x42
};

static int parse_first_extren_frm(cmd_buf_t* tran_cmd_buf);
static int parse_concur_data_frame(cmd_buf_t* tran_cmd_buf, unsigned short cur_frm_no);
int parse_extern_trig_data(void* dev_inst, void* cmd_buf)
{   
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    unsigned char update_status = 0;
    // 入参校验
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL)
    {
        return NO_NEED_REPONSE;
    }
    start_slave_concurr_update_timer(CONCURR_UPDATE_TIME);
    g_concurr_update_trig_info.trig_times ++;
    g_concur_update_stat = UPDATE_TRIG;
    if(g_concurr_update_trig_info.trig_times >= DOWNLOAD_TRIG_TIMES)
    {
        if(get_trig_mode() == 0x32)//并发广播模式，触发帧擦除
        {
            g_req_frm_no = 0;
            g_last_recive_frm_no = 0xffff;
            clear_frame_loss_info();
            set_one_data(DAC_DATA_ID_UPDATE_STATUS, &update_status);
            RETURN_VAL_IF_FAIL(erase_download_flash() != FAILURE,NO_NEED_REPONSE);
            rt_kprintf("parse_extern_trig_data erase success\n");
        }
        start_slave_concurr_update_timer(CONCURR_UPDATE_DATA_FRAM_TIME);
        g_concurr_update_trig_info.trig_success = TRUE;
        g_concurr_update_trig_info.trig_times = 0;
    }
    return SUCCESSFUL;
}

int pack_extern_trig_data(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    download_cmd_head_t* proto_head = NULL;
    // 入参校验
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL) {
        return NO_NEED_REPONSE;
    }
    proto_head = (download_cmd_head_t*)tran_cmd_buf->cmd->ack_head;
    proto_head->cid = CMD_TRIG_FRAME_TYPE_H;
    proto_head->frame_type_l = CMD_TRIG_FRAME_TYPE_L;
    proto_head->chip_type_h = CMD_TRIG_CHIP_TYPE_H;
    proto_head->chip_type_l = CMD_TRIG_VERSION; 
    tran_cmd_buf->data_len = (unsigned int)sizeof(extern_trig_frame_ack);
    rt_memcpy_s(tran_cmd_buf->buf, sizeof(extern_trig_frame_ack), extern_trig_frame_ack, sizeof(extern_trig_frame_ack));
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;
    return SUCCESSFUL;
}

int erase_download_flash()
{
    part_data_t part_data = {0};
    rt_snprintf_s(part_data.name, sizeof(part_data.name), UPDATE_DOWNLOAD_PART);//升级文件存储flash位置
    if (SUCCESSFUL != storage_process(&part_data, erase_opr)) 
    {
        return FAILURE;
    }
    return SUCCESSFUL;
}

int parse_concur_update_data(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    download_cmd_head_t* proto_head = NULL;
    unsigned short frm_no = 0;
    // 入参校验
    RETURN_VAL_IF_FAIL( tran_cmd_buf != NULL && tran_cmd_buf->buf != NULL, NO_NEED_REPONSE);
    RETURN_VAL_IF_FAIL( tran_cmd_buf->rtn == 0 , NO_NEED_REPONSE);//协议层解析数据错误时，作为丢帧处理
    proto_head = (download_cmd_head_t*)tran_cmd_buf->cmd->req_head;
    frm_no = proto_head->fn2;
    RETURN_VAL_IF_FAIL(g_concurr_update_trig_info.trig_success , NO_NEED_REPONSE);
    if(g_concur_update_stat != UPDATE_DATA_CONFIRM)
    {
        start_slave_concurr_update_timer(CONCURR_UPDATE_DATA_FRAM_TIME);
        g_concur_update_stat = UPDATE_TRAN_DATA;
    }
    else
    {
        start_slave_concurr_update_timer(CONCURR_UPDATE_TIME);
    }

    if(frm_no == 0)
    {
        parse_first_extren_frm(tran_cmd_buf);
    }
    else
    {
        parse_concur_data_frame(tran_cmd_buf, frm_no);
    }

    return SUCCESSFUL;
}

static int parse_first_extren_frm(cmd_buf_t* tran_cmd_buf)
{
    rt_memset(&g_concur_file_info, 0x00, sizeof(update_file_attr_t));
    g_concur_file_info.total_frames = get_int16_data(&tran_cmd_buf->buf[0]);
    g_concur_file_info.total_leng = get_ulong_data(&tran_cmd_buf->buf[2]);
    rt_kprintf("total_frm:%d\n",g_concur_file_info.total_frames);
    rt_memcpy_s(g_concur_file_info.file_name, UPDATE_FILE_EXTEND_NAME_LEN, &tran_cmd_buf->buf[6], UPDATE_FILE_EXTEND_NAME_LEN);
    rt_memcpy_s(g_concur_file_info.file_time, UPDATE_FILE_TIME_LEN, &tran_cmd_buf->buf[6+UPDATE_FILE_EXTEND_NAME_LEN], UPDATE_FILE_TIME_LEN);
    g_concur_file_info.filecrc = get_ulong_data(&tran_cmd_buf->buf[6+UPDATE_FILE_EXTEND_NAME_LEN + UPDATE_FILE_TIME_LEN]);
    rt_kprintf("parse_first_extren_frm filecrc:%d\n",g_concur_file_info.filecrc);
    g_concur_file_info.param_type = tran_cmd_buf->buf[6+UPDATE_FILE_EXTEND_NAME_LEN + UPDATE_FILE_TIME_LEN +4];
    if(g_concur_update_stat == UPDATE_DATA_CONFIRM)
    {
        reset_frame_loss(0);//清除丢帧记录
        record_loss_frm_when_confirm();
    }
    g_last_recive_frm_no = 0;
    return SUCCESSFUL;
}

static int parse_concur_data_frame(cmd_buf_t* tran_cmd_buf, unsigned short cur_frm_no)
{
    int ret = 0;
    RETURN_VAL_IF_FAIL(cur_frm_no >= g_req_frm_no, FAILURE);  // 跳帧到小于当前帧，则舍弃该帧
    unsigned int write_offset = 0;
    write_offset = (unsigned int)((cur_frm_no - 1) * 512);
    rt_kprintf("parse_concur_data_frame | write_offset:%d  data_len:%d\n",write_offset, tran_cmd_buf->data_len);
    ret = handle_storage(write_opr, UPDATE_DOWNLOAD_PART, tran_cmd_buf->buf, tran_cmd_buf->data_len, write_offset);
    RETURN_VAL_IF_FAIL(SUCCESSFUL == ret, FAILURE);  // 无法写入缓存，作为丢帧处理
    rt_kprintf("recv_frm:%d\n",cur_frm_no);
    record_loss_frm(cur_frm_no);
    return SUCCESSFUL;
}

short record_loss_frm(unsigned short cur_frame_no) {
    int i = 0, frm_start = 0;
    // 补发帧阶段
    if (g_concur_update_stat == UPDATE_DATA_CONFIRM)
    {
        // 清除当前帧丢帧记录
        reset_frame_loss(cur_frame_no);
    } 
    else
    {
        // 记录首发帧丢帧,第 0 帧
        if (0xffff == g_last_recive_frm_no)
        {
            frm_start = 0;
        } 
        else
        {
            frm_start = g_last_recive_frm_no + 1;
        }

        // 记录数据帧丢帧
        if (frm_start < cur_frame_no) 
        {
            rt_kprintf("lost part: %d ~ %d \n", frm_start, cur_frame_no - 1);
        }

        for (i = frm_start; i < cur_frame_no; i++)
        {
            set_frame_loss(i);
        }
        g_last_recive_frm_no = cur_frame_no;
        if (cur_frame_no == g_req_frm_no) {
            g_req_frm_no = (unsigned short)(cur_frame_no + 1);
        }
    }

    return SUCCESSFUL;
}

int pack_concur_update_data(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    download_cmd_head_t* proto_head = NULL;
    RETURN_VAL_IF_FAIL( g_concur_update_stat == UPDATE_DATA_CONFIRM, NO_NEED_REPONSE);//补帧阶段才回包，广播阶段不回包
    RETURN_VAL_IF_FAIL( tran_cmd_buf != NULL && tran_cmd_buf->buf != NULL, NO_NEED_REPONSE);
    tran_cmd_buf->data_len = 0;
    tran_cmd_buf->rtn = 0;
    proto_head = (download_cmd_head_t*)tran_cmd_buf->cmd->ack_head;
    proto_head->fn1 = 0;
    g_req_frm_no= get_first_frame_loss();
    proto_head->fn2 = g_req_frm_no;
    if (g_req_frm_no == 0xffff)
    {
        g_req_frm_no = 0;
        proto_head->fn2 = g_concur_file_info.total_frames;
    }
    return SUCCESSFUL;
}

int parse_concur_confirm_update_data(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    RETURN_VAL_IF_FAIL(g_concurr_update_trig_info.trig_success , NO_NEED_REPONSE);
    RETURN_VAL_IF_FAIL( tran_cmd_buf != NULL && tran_cmd_buf->buf != NULL, NO_NEED_REPONSE);
    RETURN_VAL_IF_FAIL( tran_cmd_buf->rtn == 0 , NO_NEED_REPONSE);//协议层解析数据错误
    g_concur_update_stat = UPDATE_DATA_CONFIRM;
    start_slave_concurr_update_timer(CONCURR_UPDATE_TIME);
    record_loss_frm_when_confirm();
    if(get_frame_loss_count() > 60)
    {
        LOG_E("%s:%d| loss_count:%d", __FUNCTION__ , __LINE__, get_frame_loss_count());
        concurr_update_timeout();
        return NO_NEED_REPONSE;
    }
    return SUCCESSFUL;
}

int pack_concur_confirm_update_data(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    download_cmd_head_t* proto_head = NULL;
    RETURN_VAL_IF_FAIL( tran_cmd_buf != NULL && tran_cmd_buf->buf != NULL, FAILURE);
    tran_cmd_buf->data_len = 3;
    tran_cmd_buf->rtn = 0;
    proto_head = (download_cmd_head_t*)tran_cmd_buf->cmd->ack_head;
    g_req_frm_no = 0;
    rt_kprintf("pack_concur_confirm_update_data!!!!\n");
    if(get_frame_loss_count() == 0)
    {
        rt_kprintf("success\n");
        tran_cmd_buf->buf[0] = 0;//帧完整
        g_req_frm_no = 0;
    }
    else
    {
        g_req_frm_no = get_first_frame_loss();
        tran_cmd_buf->buf[0] = 1;//帧不完整
    }
    put_uint16_to_buff(&tran_cmd_buf->buf[1], g_req_frm_no);
    proto_head->fn1 = 0;
    proto_head->fn2 = 0;
    return SUCCESSFUL;
}

int record_loss_frm_when_confirm() 
{
    int i = 0;
    int start_frm = 0, end_frm = 0;
    RETURN_VAL_IF_FAIL(UPDATE_DATA_CONFIRM == g_concur_update_stat, NO_NEED_REPONSE);
    if ((0 == get_frame_loss(0)) && (g_last_recive_frm_no != INVALID_FRM_NO)) {
        start_frm = g_last_recive_frm_no + 1;
        end_frm = g_concur_file_info.total_frames;
        for (i = start_frm; i < end_frm; i++) {
            set_frame_loss(i);
        }
    }
    return SUCCESSFUL;
}

int parse_concur_confirm_update(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    unsigned short crc  = 0;
    unsigned char read_data[512] = {0};
    unsigned int read_len = 0;
    RETURN_VAL_IF_FAIL(g_concurr_update_trig_info.trig_success , NO_NEED_REPONSE);
    RETURN_VAL_IF_FAIL( tran_cmd_buf != NULL && tran_cmd_buf->buf != NULL, NO_NEED_REPONSE);
    RETURN_VAL_IF_FAIL( tran_cmd_buf->rtn == 0 , NO_NEED_REPONSE);//协议层解析数据错误
    g_concur_update_stat = UPDATE_CONFIRM;
    start_slave_concurr_update_timer(CONCURR_UPDATE_TIME);
    for(int i = 0; i < g_concur_file_info.total_frames - 1; i++)
    {
        rt_memset_s(read_data, 512, 0, 512);
        read_len = g_concur_file_info.total_leng - i * 512 > 512? 512:(g_concur_file_info.total_leng - i * 512);
        handle_storage(read_opr, UPDATE_DOWNLOAD_PART, read_data, read_len, i * 512);
        crc = crc16_calc_with_init_crc(read_data, read_len, crc);
        rt_thread_mdelay(5);
        thread_beat_go_on(THREAD_PV_INVERTER);
    }
    rt_kprintf("file_crc:%02x   crc:%02x\n",g_concur_file_info.filecrc, crc);
    rt_kprintf("update success!!!!!!!!!!!!!!!!!!!!\n");
    RETURN_VAL_IF_FAIL(crc == g_concur_file_info.filecrc, NO_NEED_REPONSE);//校验不通过，需要退出升级模式（trig fail）
    deal_concur_update(&g_concur_file_info, dev_inst, cmd_buf);
    send_update_status("north trans success", NORTH_TRANS_SUCCESS);
    g_concurr_update_trig_info.trig_success = FALSE;
    return SUCCESSFUL;
}


int pack_concur_confirm_update(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    download_cmd_head_t* proto_head = NULL;
    RETURN_VAL_IF_FAIL( g_concur_update_stat == UPDATE_CONFIRM , NO_NEED_REPONSE);
    RETURN_VAL_IF_FAIL( tran_cmd_buf != NULL && tran_cmd_buf->buf != NULL, FAILURE);
    rt_timer_stop(g_concurr_update_timer);
    tran_cmd_buf->data_len = 0;
    tran_cmd_buf->rtn = 0;
    proto_head = (download_cmd_head_t*)tran_cmd_buf->cmd->ack_head;
    proto_head->fn1 = 0;
    proto_head->fn2 = 0;
    return SUCCESSFUL;
}

void concurr_update_timeout()
{
    g_concurr_update_trig_info.trig_success = FALSE;
    rt_timer_stop(g_concurr_update_timer);
    send_update_status("north trans failed", NORTH_TRANS_FAILED);
    return;
}

void init_concurr_update_slave()
{
    g_concurr_update_timer = rt_timer_create("concurr_update_timer", concurr_update_timeout, NULL, CONCURR_UPDATE_TIME, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
    if(g_concurr_update_timer == NULL)
    {
        return ;
    }
}

int start_slave_concurr_update_timer(unsigned int tm)
{
    rt_timer_stop(g_concurr_update_timer);
    rt_timer_control(g_concurr_update_timer , RT_TIMER_CTRL_SET_TIME ,&tm);
    rt_timer_start(g_concurr_update_timer); 
    return SUCCESSFUL;
}

