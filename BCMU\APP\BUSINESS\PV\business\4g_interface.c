#include <rtdef.h>
#include "drv_at.h"
#include <rtthread.h>
#include "at_device.h"
#include "4g_data_utils.h"
#include "4g_interface.h"
#include "utils_math.h"
#include "utils_rtthread_security_func.h"
#include "para_id_in.h"
#include "para_manage.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "partition_def.h"
#include "data_type.h"
#include "his_record.h"
#include "4g_default_netif.h"

int is_4g_info_effective()
{
    struct at_device_info_4g* net4g_device_info = get_at_device_info_4g_t();
    if(rt_strnlen_s(net4g_device_info->dev_model, 20) == 0 || 
        rt_strnlen_s(net4g_device_info->ip_addr, 20) == 0 ||
        rt_strnlen_s(net4g_device_info->dev_num, 20) == 0 ||
        rt_strnlen_s(net4g_device_info->dev_soft_ver, 20) == 0)
    {
        return FALSE;
    }
    return TRUE;
}

short set_master_slave_status(unsigned char flag)
{
    static unsigned char prev_flag = 0;
    short ret = 0;
    char info[21] = {0};

    if(flag != prev_flag)
    {
        ret = set_one_data(DAC_DATA_ID_MASTER_SLAVE_STATUS , &flag);
        if(ret == FAILURE)
        {
            LOG_E("set_master_slave_status: FAILURE");
            return FAILURE;
        }
        rt_strncpy_s(info, sizeof(info), flag == SLAVE_STATUS ? "change to salve" : "change to master", sizeof(info)-1);
        save_real_ctrl_cmd_event(DAC_DATA_ID_MASTER_SLAVE_STATUS, REALDATA_ID_TYPE, info);
        prev_flag = flag;
    }
    
    return SUCCESSFUL;
}

int check_4g_is_online(rt_device_t dev)
{
    static int offline_count = 0;
    int pin_flag = OFF_LINE;
    int ret = get_4gcc_pin_value(dev, &pin_flag);
    if(ret != RT_EOK)
    {
        LOG_E("get 4gcc pin failed!");
        return FAILURE;
    }
    if(pin_flag == ON_LINE)
    {
        offline_count++;
        if(offline_count >= MAX_OFFLINE_COUNT)
        {
            LOG_E("4G on line but not register!");
            offline_count = 0;
            set_dev_fault_handle(TRUE);
            return FAILURE;
        }
    }
    else
    {
        offline_count = 0;
    }
    return SUCCESSFUL;
}

//设备掉线时设置成无效值
int get_4g_info(short isClear)
{
    if(isClear == TRUE)
    {
        set_4g_info_invalid();
    }
    struct at_device_info_4g* net4g_device_info = get_at_device_info_4g_t();
    set_one_data(DAC_DATA_ID_4G_COMMUNICATION_ROD_MODEL , net4g_device_info->dev_model );
    set_one_data(DAC_DATA_ID_4G_COMMUNICATION_ROD_SERIAL_NUMBER , net4g_device_info->dev_num );
    set_one_data(DAC_DATA_ID_4G_COMMUNICATION_ROD_SOFT_VER , net4g_device_info->dev_soft_ver );
    set_one_data(DAC_DATA_ID_4G_COMMUNICATION_ROD_ICCID_NUMBER , net4g_device_info->iccid );
    set_one_data(DAC_DATA_ID_4G_OPERATOR_NAME , net4g_device_info->operator_name);
    set_one_data(DAC_DATA_ID_4G_SIGNAL_STRENGTH, &(net4g_device_info->signal_power));
    set_one_data(DAC_DATA_ID_WIRELESS_IP_ADDR , net4g_device_info->ip_addr );

    return SUCCESSFUL;
}

int judge_4G_ip(rt_device_t dev)
{
    static int last_ip_flag = FALSE;
    static int ip_err_count = 0;
    int curr_ip_flag = FALSE;
    get_4g_dev_info(dev, AT_DEVICE_CTRL_GET_IP_ADDR);
    struct at_device_info_4g* net4g_device_info = get_at_device_info_4g_t();
    curr_ip_flag = is_valid_ip(net4g_device_info->ip_addr);
    if(curr_ip_flag == last_ip_flag)
    {
        return TRUE;
    }

    if(curr_ip_flag == FALSE)
    {
        if(ip_err_count >= MAX_IP_ERR_COUNT)
        {
            last_ip_flag = curr_ip_flag;
            LOG_E("%s:%d|ip change Abnormal lost and reset 4G", __FUNCTION__ , __LINE__);
            set_dev_fault_handle(TRUE);
            return FALSE;
        }
        ip_err_count++;
        return TRUE;
    }
    else
    {
        last_ip_flag = curr_ip_flag;
        ip_err_count = 0;
    }
    return TRUE;
}

short judge_simcard_status(rt_device_t dev, unsigned short curr_sim_status)
{
    static unsigned short last_sim_status = SIM_CARD_NOT_INSERT;
    static int no_sim_cnt = 0;
    if(curr_sim_status == SIM_CARD_INSERT)
    {
        // 异常ip检测
        judge_4G_ip(dev);
    }
    if(curr_sim_status == last_sim_status) 
    {
        if(curr_sim_status == SIM_CARD_NOT_INSERT)
        {
            return FALSE;
        }
        return TRUE;
    }

    if(curr_sim_status == SIM_CARD_NOT_INSERT)
    {
        if(no_sim_cnt > MAX_SIM_NO_COUNT)
        {
            last_sim_status = curr_sim_status; 
            no_sim_cnt = 0;
            // sim卡从有到无且连续三次未检测到sim卡
            set_dev_fault_handle(TRUE);
            LOG_E("%s:%d|sim card lost and reset 4G", __FUNCTION__ , __LINE__);
        }
        no_sim_cnt ++;
        return FALSE;
    }
    else
    {
        LOG_E("%s:%d|sim card inserted", __FUNCTION__ , __LINE__);
        last_sim_status = curr_sim_status; 
    }
    return TRUE;
}

short is_site_id_effective()
{
    //考虑第三方网管
    char site_id1[STR_LEN_16] = {0};
    char site_id2[STR_LEN_16] = {0};
    get_one_para(DAC_PARA_ID_SITE_ID_OFFSET, site_id1);
    get_one_para(DAC_PARA_ID_SITE_ID_OFFSET+1, site_id2);
    if((rt_strnlen_s(site_id1, STR_LEN_16) == 0) && (rt_strnlen_s(site_id2, STR_LEN_16) == 0))
    {
        return FAILURE;
    }
    return SUCCESSFUL;
}

short check_sim_status_handle(rt_device_t dev)
{
    static int s_get_4g_info_cnt = GET_4G_INFO_INIT_TIME;
    unsigned short sim_status = 0;
    short ret = FALSE;
    // 模组相关信息获取指令
    int model_cmd_list[] = {AT_DEVICE_CTRL_GET_IP_ADDR,
                            AT_DEVICE_CTRL_GET_DEV_MODEL,
                            AT_DEVICE_CTRL_GET_DEV_NUM,
                            AT_DEVICE_CTRL_GET_DEV_SOFT_VER};
    // sim卡相关信息获取指令
    int sim_cmd_list[] = {AT_DEVICE_CTRL_GET_ICCID,
                          AT_DEVICE_CTRL_GET_OPERATOR_NAME,
                          AT_DEVICE_CTRL_GET_SIGNAL_POWER};
    if(is_4g_info_effective() == FALSE)
    {
        for(int i = 0; i < sizeof(model_cmd_list)/sizeof(int); i++)
        {
            get_4g_dev_info(dev, model_cmd_list[i]);
        }
        get_4g_info(FALSE);
    }

    if(s_get_4g_info_cnt % NET_4G_INTERVAL == 0)
    {
        s_get_4g_info_cnt = 0;
        sim_status = get_4g_sim_status(dev);
        ret = judge_simcard_status(dev, sim_status);
        if(sim_status == TRUE)
        {
            // sim卡信息
            for(int i = 0; i < sizeof(sim_cmd_list)/sizeof(int); i++)
            {
                get_4g_dev_info(dev, sim_cmd_list[i]);
            }
            get_4g_info(FALSE);
        }
    }
    s_get_4g_info_cnt ++;
    return ret;
}

short model_4g_offline_handle(rt_device_t dev)
{
    short ret = SUCCESSFUL;
    // 1.硬件检测到模组，但是软件无法识别，会重启模组
    ret = check_4g_is_online(dev);
    // 2.设备状态变从机
    set_master_slave_status(SLAVE_STATUS);
    // 3.清除模组缓存信息
    get_4g_info(TRUE);
    return ret;
}

short product_special_handle()
{
    // 检测站点id有效性
    if(is_site_id_effective() == SUCCESSFUL)
    {
        set_master_slave_status(MASTER_STATUS);
    }
    // 设置默认网口为4G
    switch_default_netif(PPP_DEVICE_NAME);
    return SUCCESSFUL;
}

int register_4g_func()
{
    net_4g_func_t* net_4g_func = NULL;
    net_4g_func = get_4g_func_t();
    net_4g_func->ptr_no4g_model_handle = model_4g_offline_handle;
    net_4g_func->ptr_check_sim_status_handle = check_sim_status_handle;
    net_4g_func->ptr_product_special_handle = product_special_handle;
    return SUCCESSFUL;
}