#include "batt_charge.h"
#include "pid.h"
#include "data_type.h"
#include "utils_math.h"
#include "para_manage.h"
#include "dev_bsmu.h"
#include "device_type.h"
#include "msg_id.h"
#include "msg.h"
#include "alarm_register.h"
#include "alarm_mgr_api.h"

#define DEFAULT_BATT_CHARGE_CURR_MAX  1   //1C
#define BATT_CAP                      80  //目前固定80AH，后续从参数里面获取（工控配置到参数里面）

static float get_charge_max_curr(){
    float set_charge_limit_curr_coe;
    if(FAILURE == get_one_para(PARA_ID_SET_CHG_LIMIT_CURR_COE, &set_charge_limit_curr_coe)){
        return DEFAULT_BATT_CHARGE_CURR_MAX;
    }
    return min(DEFAULT_BATT_CHARGE_CURR_MAX, set_charge_limit_curr_coe);
}

//这部分计算需要一直轮询计算然后发送到各个簇
unsigned short get_cluster_charge_curr(int index){
    //获取实时告警中的告警值
    float cell_charge_high_temp_limit_curr_coe = 0;
    float value = 0;
    
    if(get_realtime_alarm_value(GET_ALM_ID(CLUSTER_CHG_HIGH_TEMP,1, ALARM_LEVEL_MAJOR)) == 1){//充电高温二级告警 55-70℃
        value = 2.0f;
    }else if(get_realtime_alarm_value(GET_ALM_ID(CLUSTER_CHG_HIGH_TEMP,1, ALARM_LEVEL_MINOR)) == 1){//充电高温一级告警 50-55℃
        if(get_one_para(PARA_ID_CELL_CHARGE_HIGH_TEMP_LIMIT_CURR_COE, &cell_charge_high_temp_limit_curr_coe) == SUCCESSFUL){
            value = cell_charge_high_temp_limit_curr_coe*BATT_CAP;   
        }else{
            value = get_charge_max_curr()*BATT_CAP;
        }
    }else if(get_realtime_alarm_value(GET_ALM_ID(CLUSTER_CHG_LOW_TEMP,1, ALARM_LEVEL_MINOR)) == 1){//充电低温一级告警 0-10℃
        value = 3.0f;
    }else{
        value = get_charge_max_curr()*BATT_CAP; //正常工作温度(10-50℃)，按照最大充电电流
    }
    value = value/BATT_CAP * 1000 + 0.5;//千分比
    return (unsigned short)value;
}

//提供给电池管理调用, TODO:调用完成以后发送消息给南向模块进行命令的发送！
void calc_cluster_chg_curr(){
    // int index;
    // unsigned short set_chg_limit_curr;
    // dev_inst_t* dev_dc_dc = get_dev_inst(DEV_DC_DC);

    // if(dev_dc_dc == NULL){
    //     return;
    // }

    // for(index = 0; index < BATT_CLUSTER_NUM; index++){
    //     set_chg_limit_curr = get_cluster_charge_curr(index);
    //     rt_memcpy(&((DC_DC_para_t*)dev_dc_dc[index].dev_data.para_data)->set_chg_limit_curr, &set_chg_limit_curr, sizeof(unsigned short));
    //     send_msg_to_sample(EXE_DEST_CMD_MSG, MOD_DC_DC, index, DC_DC_SET_PARA_SET_CHG_LIMIT_CURR);
    // }
    return;
}







