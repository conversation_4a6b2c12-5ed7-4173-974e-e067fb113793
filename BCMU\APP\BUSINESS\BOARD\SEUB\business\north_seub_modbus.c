/*
 * @file    : north_seub_modbus.c
 * @brief   : SEUB北向modbus协议实现
 * @details :
 * <AUTHOR> penglei
 * @Date    : 2024-07-18
 * @LastEditTime: 2024-07-18
 * @version : V0.0.1
 */

#include "dev_north_seub_modbus.h"
#include "protocol_layer.h"
#include <string.h>
#include "sample.h"
#include "utils_data_transmission.h"
#include "software_version.h"
#include "storage.h"
#include "ee_public_info.h"
#include "lwip/netifapi.h"
#include "lwip/inet.h"
#include "utils_string.h"
#include "utils_rtthread_security_func.h"

/* 设备缓冲区长度 */
#define R_NORTH_MODBUS_BUFF_LEN 512 ///<  接收缓冲区长度
#define S_NORTH_MODBUS_BUFF_LEN 512 ///<  发送缓冲区长度

#define DEV_CODE_NO_USE 1
#define MAX_MODBUS_REG_NUM    123

extern struct netif *netif_default;

signed short g_reg_addr = 0;
signed short g_reg_nums = 0;
signed short g_set_val = 0;
unsigned char g_func_code = 0;

T_SheetSEUBDataStruct s_SeubDataSheet;
network_info_t g_SeubNetwork;

Static int parse_seub_start_addr_and_reg_nums(void *dev_inst, void *cmd_buff);
Static int find_seub_register_start_addr_index(int reg_addr);
Static int pack_seub_ana_and_para_data_to_buff(int index, void *cmd_buff);
Static int get_seub_signal_data_buff(unsigned char *data_buff, int total_len, int *index);
Static int pack_seub_ana_or_para_data(void *dev_inst, void *cmd_buff);
Static int parse_seub_set_single_para(void* dev_inst, void* cmd_buff);
Static int parse_seub_para_data(void* dev_inst, void* cmd_buff);
Static int pack_seub_set_single_para_data(void* dev_inst, void* cmd_buff);
Static int pack_seub_set_para_data(void* dev_inst, void* cmd_buff);
Static void update_seub_data(void);
Static void update_seub_ai(void);
Static void update_seub_di(void);
Static void update_seub_do(void);
Static void update_seub_dac(void);
Static void update_seub_fact_info(void);
Static int network_info_read_write(unsigned char * data, storage_operate_e operate, unsigned int network_offset);
Static int change_network_config(const char *ip_str, const char *netmask_str, const char *gw_str);
Static int handle_network_param(void* cmd_buff, unsigned char* new_val, unsigned char* curr_val, unsigned char* global_val);
Static int refresh_default_network_info(void);

/* 命令请求头 */
Static modbusrtu_cmd_head_t cmd_req[] = {
    {GET_ANA_DATA},
    {GET_PARA_DATA},
    {SET_SINGLE_PARA_DATA},
    {SET_PARA_DATA},
};

/* 命令应答头 */
Static modbusrtu_cmd_head_t cmd_ack[] = {
    {GET_ANA_DATA},
    {GET_PARA_DATA},
    {SET_SINGLE_PARA_DATA},
    {SET_PARA_DATA},
};

/* 命令总表,必须以空字符串""结束 */
Static cmd_t no_poll_cmd_tab[] = {

    {GET_ANA_DATA, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_seub_ana_or_para_data, parse_seub_start_addr_and_reg_nums},
    {GET_PARA_DATA, CMD_PASSIVE, &cmd_req[1], &cmd_ack[1], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_seub_ana_or_para_data, parse_seub_start_addr_and_reg_nums},
    {SET_SINGLE_PARA_DATA, CMD_PASSIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_seub_set_single_para_data, parse_seub_set_single_para},
    {SET_PARA_DATA, CMD_PASSIVE, &cmd_req[3], &cmd_ack[3], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_seub_set_para_data, parse_seub_para_data},
    {0},
};

Static dev_type_t dev_val_north_seub = {
    DEV_NORTH_SEUB,
    1,
    PROTOCOL_MODBUS_RTU,
    LINK_NORTH_SEUB,
    R_NORTH_MODBUS_BUFF_LEN,
    S_NORTH_MODBUS_BUFF_LEN,
    DEV_CODE_NO_USE,
    no_poll_cmd_tab,
    NULL,
    0
};

Static dev_type_t dev_val_modbus_tcp_seub = {   // 收发端填1，仅仅是为了malloc申请成功，实际的收发buff长度不在这里控制
    DEV_MODBUS_TCP_SEUB,
    1,
    PROTOCOL_MODBUS_TCP,
    LINK_DAC_IP,
    1,
    1,
    DEV_CODE_NO_USE,
    no_poll_cmd_tab,
    NULL,
    0
};

#define MODBUS_ADDR_AODO_START 100
#define MODBUS_ADDR_DAC_START 108
#define MODBUS_ADDR_AODO_END 109

modbus_seub_addr_map_data_t g_modbus_data_map[] = {
    {1, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAI1)},
    {2, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAI2)},
    {3, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAI3)},
    {4, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAI4)},
    {5, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAI5)},
    {6, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAI6)},
    {7, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAI7)},
    {8, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAI8)},
    {9, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAI9)},
    {10, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAI10)},
    {11, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAI11)},
    {12, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAI12)},
    {13, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAI13)},
    {14, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAI14)},
    {15, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAI15)},
    {16, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAI16)},
    {17, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAI17)},
    {18, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAI18)},
    {19, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAI19)},
    {20, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAI20)},
    {21, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAI21)},
    {22, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAI22)},
    {23, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAI23)},
    {24, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAI24)},
    {25, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAI25)},
    {26, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAI26)},
    {27, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAI27)},
    {28, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAI28)},
    {29, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAI29)},
    {30, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAI30)},
    {31, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAI31)},
    {32, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAI32)},
    {33, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usDI1_12)},
    {MODBUS_ADDR_AODO_START, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usDO1)},
    {101, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usDO2)},
    {102, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usDO3)},
    {103, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usDO4)},
    {104, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usDO5)},
    {105, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usDO6)},
    {106, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usDO7)},
    {107, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usDO8)},
    {MODBUS_ADDR_DAC_START, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAO1)},
    {MODBUS_ADDR_AODO_END, TYPE_INT16U_SEUB, S_DATA_LEN_NO_USE, TYPE_ANA, (void *)(&s_SeubDataSheet.usAO2)},

    {200, TYPE_STR_SEUB, SEUB_SOFTWARE_NAME_LEN_MODBUS, TYPE_ANA, (void *)(s_SeubDataSheet.tFacInfo.acSwName)},
    {205, TYPE_STR_SEUB, SEUB_SOFTWARE_VER_LEN_MODBUS, TYPE_ANA, (void *)(s_SeubDataSheet.tFacInfo.acSwVer)},
    {213, TYPE_STR_SEUB, SEUB_SOFTWARE_DATE_LEN_MODBUS, TYPE_ANA, (void *)(s_SeubDataSheet.tFacInfo.acSwDate)},

    {221, TYPE_STR_SEUB, SEUB_NETWORK_LEN, TYPE_PARA, (void *)(s_SeubDataSheet.tNetwork.acIPAddress)},
    {229, TYPE_STR_SEUB, SEUB_NETWORK_LEN, TYPE_PARA, (void *)(s_SeubDataSheet.tNetwork.acNetMask)},
    {237, TYPE_STR_SEUB, SEUB_NETWORK_LEN, TYPE_PARA, (void *)(s_SeubDataSheet.tNetwork.acGateway)},

    {245, TYPE_STR_SEUB, SEUB_SERIAL_NUMBER_LEN_MODBUS, TYPE_PARA, (void *)(s_SeubDataSheet.tFacInfo.acSN)},
    {255, TYPE_STR_SEUB, SEUB_SYSTEM_NAME_LEN_MODBUS, TYPE_PARA, (void *)(s_SeubDataSheet.tFacInfo.acSysName)},
    {265, TYPE_STR_SEUB, SEUB_HARDWARE_VERSION_LEN_MODBUS, TYPE_PARA, (void *)(s_SeubDataSheet.tFacInfo.acHwVer)}

};

// 恢复默认网络参数
Static int refresh_default_network_info(void)
{
    rt_memcpy_s(g_SeubNetwork.acIPAddress, SEUB_NETWORK_LEN, IP_ADDRESS_DEFAULT, sizeof(IP_ADDRESS_DEFAULT));
    rt_memcpy_s(g_SeubNetwork.acNetMask, SEUB_NETWORK_LEN, NETMASK_DEFAULT, sizeof(NETMASK_DEFAULT));
    rt_memcpy_s(g_SeubNetwork.acGateway, SEUB_NETWORK_LEN, GATEWAY_DEFAULT, sizeof(GATEWAY_DEFAULT));
    g_SeubNetwork.crc = crc_cal((unsigned char *)&g_SeubNetwork, offsetof(network_info_t, crc)); // 计算网络参数的CRC
    network_info_read_write((unsigned char *)g_SeubNetwork.acIPAddress, write_opr, SEUB_NETWORK_INFO_OFFSET); // 保存网络参数
    network_info_read_write((unsigned char *)g_SeubNetwork.acIPAddress, write_opr, SEUB_NETWORK_BACKUP_INFO_OFFSET); // 保存网络参数的备份

    return SUCCESSFUL;
}

Static int init_network_info(void)
{
    unsigned short network_crc = 0;

    // 读取主网络信息
    network_info_read_write((unsigned char *)g_SeubNetwork.acIPAddress, read_opr, SEUB_NETWORK_INFO_OFFSET);
    network_crc = crc_cal((unsigned char *)&g_SeubNetwork, offsetof(network_info_t, crc));

    if (network_crc != g_SeubNetwork.crc)
    {
        // 主网络信息有错误，则读取备份网络信息
        network_info_read_write((unsigned char *)g_SeubNetwork.acIPAddress, read_opr, SEUB_NETWORK_BACKUP_INFO_OFFSET);
        network_crc = crc_cal((unsigned char *)&g_SeubNetwork, offsetof(network_info_t, crc));

        if(network_crc != g_SeubNetwork.crc)
        {
            // 备份网络信息也有错误，则只能恢复默认配置
            refresh_default_network_info();
        }
        else
        {
            // 备份网络信息有效时，恢复主区域的网络信息
            network_info_read_write((unsigned char *)g_SeubNetwork.acIPAddress, write_opr, SEUB_NETWORK_INFO_OFFSET);
        }
    }

    // 检查网络信息有效性
    if (0xFF == g_SeubNetwork.acIPAddress[0] || 0xFF == g_SeubNetwork.acGateway[0])
    {
        refresh_default_network_info();
    }

    // 数据同步和配置更新
    rt_memcpy_s(&s_SeubDataSheet.tNetwork, sizeof(network_info_t), &g_SeubNetwork, sizeof(network_info_t));
    change_network_config(g_SeubNetwork.acIPAddress, g_SeubNetwork.acNetMask, g_SeubNetwork.acGateway);

    return SUCCESSFUL;
}

dev_type_t *init_dev_north_seub(void)
{
    init_network_info();
    return &dev_val_north_seub;
}

dev_type_t *init_dev_modbus_tcp_seub(void)
{
    return &dev_val_modbus_tcp_seub;
}

Static int parse_seub_start_addr_and_reg_nums(void *dev_inst, void *cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    unsigned char *data_buff = ((cmd_buf_t *)cmd_buff)->buf;

    g_reg_addr = get_int16_data(&data_buff[0]); // 获取数据域的寄存器首地址 2字节
    g_reg_nums = get_int16_data(&data_buff[2]); // 获取数据域的寄存器个数 2字节

    return SUCCESSFUL;
}

/*获04 03模拟量数据,参数量数据*/
Static int pack_seub_ana_or_para_data(void *dev_inst, void *cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    int reg_addr = g_reg_addr;
    update_seub_data();
    /*先找到基地址的下标索引*/
    int index = find_seub_register_start_addr_index(reg_addr);
    if (NO_MATCH_REGISTER_ADDR == index) {
        ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_ADDR;
        return SUCCESSFUL;
    }
    int data_len = pack_seub_ana_and_para_data_to_buff(index, cmd_buff);
    ((cmd_buf_t *)cmd_buff)->buf[0] = data_len;

    ((cmd_buf_t *)cmd_buff)->data_len = data_len + 1;
    return SUCCESSFUL;
}

Static int find_seub_register_start_addr_index(int reg_addr)
{
    int ret = NO_MATCH_REGISTER_ADDR;
    int star_index = 0;
    int mid_index = 0;
    int end_index = sizeof(g_modbus_data_map) / sizeof(modbus_seub_addr_map_data_t) - 1;

    while (star_index <= end_index && star_index >= 0)
    {
        mid_index = (star_index + end_index) / 2;
        if (reg_addr == g_modbus_data_map[mid_index].register_addr) {
            ret = mid_index;
        }
        star_index = (g_modbus_data_map[mid_index].register_addr < reg_addr) ? (mid_index + 1) : star_index;
        end_index = (g_modbus_data_map[mid_index].register_addr < reg_addr) ? end_index : (mid_index - 1);
    }

    return ret;
}

Static int pack_seub_ana_and_para_data_to_buff(int index, void *cmd_buff)
{
    int total_len = 0;
    int reg_nums = g_reg_nums;
    int type = 0;
    int data_len = 0;

    // +1应为第一个字节要放总长度（尽管可以直接寄存器数目计算，但是为了验证代码准性，累加长度的）
    unsigned char *data_buff = (((cmd_buf_t *)cmd_buff)->buf) + 1; // 准备往data_buff中存放数据
    while (reg_nums > 0 && (index < sizeof(g_modbus_data_map) / sizeof(modbus_seub_addr_map_data_t))) {
        type = g_modbus_data_map[index].type;
        data_len = g_modbus_data_map[index].data_len;
        total_len += get_seub_signal_data_buff(data_buff, total_len, &index);
        if (TYPE_INT32U_SEUB == type || TYPE_INT32S == type) {
            reg_nums -= 2;
        } else if (TYPE_INT16S_SEUB == type || TYPE_INT16U_SEUB == type || TYPE_BIT == type) {
            reg_nums -= 1;
        } else if (TYPE_STR_SEUB == type) {
            reg_nums -= data_len / 2;
        } else {
            reg_nums = 0;
        }
    }
    return total_len;
}

Static int get_seub_signal_data_buff(unsigned char *data_buff, int total_len, int *index)
{
    int type = g_modbus_data_map[*index].type;
    int data_len = g_modbus_data_map[*index].data_len;
    void *pDataTmp = g_modbus_data_map[*index].pData;

    if (TYPE_INT32U_SEUB == type) {
        put_uint32_to_buff(&data_buff[total_len], *((unsigned int *)pDataTmp));
        (*index)++;
        return DATA_LEN_4;
    } else if (TYPE_INT32S == type) {
        put_int32_to_buff(&data_buff[total_len], *((signed int *)pDataTmp));
        (*index)++;
        return DATA_LEN_4;
    } else if (TYPE_INT16U_SEUB == type) {
        put_uint16_to_buff(&data_buff[total_len], *((unsigned short *)pDataTmp));
        (*index)++;
        return DATA_LEN_2;
    } else if (TYPE_INT16S_SEUB == type) {
        put_int16_to_buff(&data_buff[total_len], *((signed short *)pDataTmp));
        (*index)++;
        return DATA_LEN_2;
    } else if (TYPE_STR_SEUB == type) {
        memcpy_s(&data_buff[total_len], data_len, (char *)pDataTmp, data_len);
        (*index)++;
        return data_len;
    } else {
        (*index)++;
        return 0;
    }
}

Static int parse_seub_set_single_para(void* dev_inst, void* cmd_buff)
{
    int index = 0;
    signed short ss_reg_addr = 0;
    signed short ss_set_val = 0;
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    unsigned char *data_buff = ((cmd_buf_t *)cmd_buff)->buf;

    g_reg_addr = get_int16_data(&data_buff[0]); // 获取数据域的寄存器首地址 2字节
    g_set_val = get_int16_data(&data_buff[2]); // 获取数据域的寄存器个数 2字节
    ss_reg_addr = g_reg_addr;
    ss_set_val = g_set_val;
    if (ss_reg_addr >= MODBUS_ADDR_AODO_START && ss_reg_addr < MODBUS_ADDR_DAC_START) {
        index  = ss_reg_addr - MODBUS_ADDR_AODO_START;
        setDoValue(index, ss_set_val);
    }else if (ss_reg_addr >= MODBUS_ADDR_DAC_START && ss_reg_addr <= MODBUS_ADDR_AODO_END){
        index  = ss_reg_addr - MODBUS_ADDR_DAC_START;
        setDacValue(index, ss_set_val);
    }

    return SUCCESSFUL;
}

Static int handle_network_param(void* cmd_buff, unsigned char* new_val, unsigned char* curr_val, unsigned char* global_val)
{
    if (0 == rt_memcmp(curr_val, new_val, SEUB_NETWORK_LEN))
    {
        return SUCCESSFUL;
    }

    rt_memcpy_s(global_val, SEUB_NETWORK_LEN, new_val, SEUB_NETWORK_LEN);

    if (SUCCESSFUL != change_network_config(g_SeubNetwork.acIPAddress, g_SeubNetwork.acNetMask, g_SeubNetwork.acGateway))
    {
        ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_VALUE;
        return SUCCESSFUL;
    }

    g_SeubNetwork.crc = crc_cal((unsigned char *)&g_SeubNetwork, offsetof(network_info_t, crc));
    network_info_read_write((unsigned char *)g_SeubNetwork.acIPAddress, write_opr, SEUB_NETWORK_INFO_OFFSET); // 保存网络信息
    network_info_read_write((unsigned char *)g_SeubNetwork.acIPAddress, write_opr, SEUB_NETWORK_BACKUP_INFO_OFFSET); // 保存网络信息的备份

    rt_memcpy_s(curr_val, SEUB_NETWORK_LEN, global_val, SEUB_NETWORK_LEN);
    return SUCCESSFUL;
}

Static int parse_seub_para_data(void* dev_inst, void* cmd_buff)
{
    unsigned short reg_nums = 0, reg_addr = 0;
    int cnt = 0, set_index = 0, set_val = 0, ss_reg_addr = 0;
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    unsigned char *data_buff = ((cmd_buf_t *)cmd_buff)->buf;
    g_func_code = ((modbusrtu_cmd_head_t *)(((cmd_buf_t *)cmd_buff)->cmd)->ack_head)->cs;
    g_reg_addr = get_int16_data(&data_buff[0]); // 获取数据域的寄存器首地址 2字节
    g_reg_nums = get_int16_data(&data_buff[2]); // 获取数据域的寄存器个数 2字节
    reg_nums = g_reg_nums;
    reg_addr = g_reg_addr;
    if (reg_nums >= MAX_MODBUS_REG_NUM) {
        return FAILURE;
    }

    if(reg_addr == IP_ADDRESS_REG_ADDR)
    {
        return handle_network_param(cmd_buff, &data_buff[5], s_SeubDataSheet.tNetwork.acIPAddress, g_SeubNetwork.acIPAddress);
    }
    else if(reg_addr == NETMASK_REG_ADDR)
    {
        return handle_network_param(cmd_buff, &data_buff[5], s_SeubDataSheet.tNetwork.acNetMask, g_SeubNetwork.acNetMask);
    }
    else if(reg_addr == GATEWAY_REG_ADDR)
    {
        return handle_network_param(cmd_buff, &data_buff[5], s_SeubDataSheet.tNetwork.acGateway, g_SeubNetwork.acGateway);
    }

    for (cnt = 0; cnt < reg_nums; cnt++) {
        set_val = get_int16_data(&data_buff[5 + 2*cnt]);
        ss_reg_addr = reg_addr + cnt;
        if (ss_reg_addr >= MODBUS_ADDR_AODO_START && ss_reg_addr < MODBUS_ADDR_DAC_START) {
            set_index  = ss_reg_addr - MODBUS_ADDR_AODO_START;
            setDoValue(set_index, set_val);
        }else if (ss_reg_addr >= MODBUS_ADDR_DAC_START && ss_reg_addr <= MODBUS_ADDR_AODO_END){
            set_index  = ss_reg_addr - MODBUS_ADDR_DAC_START;
            setDacValue(set_index, set_val);
        }
    }
    return SUCCESSFUL;
}

Static int pack_seub_set_single_para_data(void* dev_inst, void* cmd_buff)
{
    int offset = 0;
    // 正确响应，数据域：回复寄存器地址+寄存器个数
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    if (((cmd_buf_t *)cmd_buff)->rtn != MODBUS_RTN_OK) {
        return SUCCESSFUL;
    }

    unsigned char *data_buff = ((cmd_buf_t *)cmd_buff)->buf; // 准备往data_buff中存放数据

    put_uint16_to_buff(&data_buff[offset], g_reg_addr);
    offset += 2;
    put_uint16_to_buff(&data_buff[offset], g_set_val);
    offset += 2;
    ((cmd_buf_t *)cmd_buff)->data_len = offset;

    return SUCCESSFUL;
}

Static int pack_seub_set_para_data(void* dev_inst, void* cmd_buff)
{
    int offset = 0;
    // 正确响应，数据域：回复寄存器地址+寄存器个数
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    if (((cmd_buf_t *)cmd_buff)->rtn != MODBUS_RTN_OK) {
        return SUCCESSFUL;
    }

    unsigned char *data_buff = ((cmd_buf_t *)cmd_buff)->buf; // 准备往data_buff中存放数据

    put_uint16_to_buff(&data_buff[offset], g_reg_addr);
    offset += 2;
    put_uint16_to_buff(&data_buff[offset], g_reg_nums);
    offset += 2;
    ((cmd_buf_t *)cmd_buff)->data_len = offset;

    return SUCCESSFUL;
}

Static void update_seub_di(void)
{
    int aSwitch[DIGITAL_COUNT] = {0};
    unsigned short wTemp = 0, i = 0;

    s_SeubDataSheet.usDI1_12 = 0;
    get_digital_data(aSwitch, sizeof(aSwitch));
    for (i = 0; i < DIGITAL_COUNT; i++)
    {
        wTemp = (aSwitch[i] & 0x01) << i;
        s_SeubDataSheet.usDI1_12 += wTemp;
    }
    return;
}

Static void update_seub_ai(void)
{
    unsigned short analog_data[ANALOG_COUNT] = {0};
    get_analog_data(analog_data, sizeof(unsigned short) * ANALOG_COUNT);
    memcpy_s(&s_SeubDataSheet.usAI1, sizeof(unsigned short) * ANALOG_COUNT, analog_data, sizeof(unsigned short) * ANALOG_COUNT);
    return;
}

Static void update_seub_do(void)
{
    unsigned short do_data[DO_COUNT] = {0}, i = 0;
    if(g_reg_addr >= MODBUS_ADDR_AODO_START && g_reg_addr < MODBUS_ADDR_DAC_START)
    {
        for (i = 0; i < DO_COUNT; i++)
        {
            do_data[i] = getDoValue(i);
        }
    }
    memcpy_s(&s_SeubDataSheet.usDO1, sizeof(unsigned short) * DO_COUNT, do_data, sizeof(unsigned short) * DO_COUNT);
    return;
}

Static void update_seub_dac(void)
{
    unsigned short dac_data[DAC_COUNT] = {0}, i = 0;
    if(g_reg_addr >= MODBUS_ADDR_DAC_START && g_reg_addr <= MODBUS_ADDR_AODO_END)
    {
        for (i = 0; i < DAC_COUNT; i++)
        {
            dac_data[i] = getDacValue(i);
        }
    }
    memcpy_s(&s_SeubDataSheet.usAO1, sizeof(unsigned short) * DAC_COUNT, dac_data, sizeof(unsigned short) * DAC_COUNT);
    return;
}

Static void update_seub_fact_info(void)
{
    unsigned char factory_info_eeprom[SEUB_FACTORY_INFO_EEPROM_LEN_MODBUS] = {0}; // 存储在eeprom上的厂家信息

    rt_strncpy_s(s_SeubDataSheet.tFacInfo.acSwName, sizeof(s_SeubDataSheet.tFacInfo.acSwName), SEUB_SOFTWARE_NAME, rt_strnlen_s(SEUB_SOFTWARE_NAME, 4));
    rt_strncpy_s(s_SeubDataSheet.tFacInfo.acSwVer, sizeof(s_SeubDataSheet.tFacInfo.acSwVer), SEUB_SOFTWARE_VERSION, rt_strnlen_s(SEUB_SOFTWARE_VERSION, 11));
    rt_strncpy_s(s_SeubDataSheet.tFacInfo.acSwDate, sizeof(s_SeubDataSheet.tFacInfo.acSwDate), SEUB_SOFTWARE_RELEASE_DATE, rt_strnlen_s(SEUB_SOFTWARE_RELEASE_DATE, 10));

    if (handle_storage(read_opr, EEP_DATA_PART, factory_info_eeprom, SEUB_FACTORY_INFO_EEPROM_LEN_MODBUS, SEUB_FACTORY_INFO_OFFSET) != SUCCESSFUL)
    {
        return;
    }

    rt_memcpy_s(s_SeubDataSheet.tFacInfo.acSN, sizeof(s_SeubDataSheet.tFacInfo.acSN), &factory_info_eeprom[0], SEUB_SERIAL_NUMBER_LEN_MODBUS);
    rt_memcpy_s(s_SeubDataSheet.tFacInfo.acSysName, sizeof(s_SeubDataSheet.tFacInfo.acSysName), &factory_info_eeprom[20], SEUB_SYSTEM_NAME_LEN_MODBUS);
    rt_memcpy_s(s_SeubDataSheet.tFacInfo.acHwVer, sizeof(s_SeubDataSheet.tFacInfo.acHwVer), &factory_info_eeprom[40], SEUB_HARDWARE_VERSION_LEN_MODBUS);

    return;
}

Static int update_seub_network_info(void)
{
    network_info_read_write((unsigned char *)g_SeubNetwork.acIPAddress, read_opr, SEUB_NETWORK_INFO_OFFSET);
    rt_memcpy_s(&s_SeubDataSheet.tNetwork, sizeof(network_info_t), &g_SeubNetwork, sizeof(network_info_t));
    return SUCCESSFUL;
}

Static void update_seub_data(void)
{
    update_seub_ai();
    update_seub_di();
    update_seub_do();
    update_seub_dac();
    update_seub_fact_info();
    update_seub_network_info();
    return;
}

Static int change_network_config(const char *ip_str, const char *netmask_str, const char *gw_str)
{
    if (netif_default == RT_NULL)
    {
        rt_kprintf("Network interface not found!\n");
        return FAILURE;
    }

    ip_addr_t ipaddr, netmask, gw;

    /* 将字符串转换为IP地址,如果不符合ip地址格式，返回失败 */
    if(0 == inet_aton(ip_str, &ipaddr))
    {
        return FAILURE;
    }
    if(0 == inet_aton(netmask_str, &netmask))
    {
        return FAILURE;
    }
    if(0 == inet_aton(gw_str, &gw))
    {
        return FAILURE;
    }

    /* 先关闭网络接口 */
    netifapi_netif_set_down(netif_default);
    /* 设置新的网络参数 */
    netifapi_netif_set_addr(netif_default, &ipaddr, &netmask, &gw);
    /* 重新启用网络接口 */
    netifapi_netif_set_up(netif_default);

    return SUCCESSFUL;
}

Static int network_info_read_write(unsigned char * data, storage_operate_e operate, unsigned int network_offset)
{
    part_data_t part_data;
    rt_memset_s(&part_data, sizeof(part_data), 0, sizeof(part_data));
    rt_snprintf_s(part_data.name, sizeof(part_data.name), "%s", EEP_DATA_PART);
    part_data.buff = data;
    part_data.len = 3 * SEUB_NETWORK_LEN + 2; // IP地址16字节+子网掩码16字节+网关16字节+CRC2字节
    part_data.offset = network_offset;

    if (SUCCESSFUL != storage_process(&part_data, operate))
    {
        return FAILURE;
    }

    return SUCCESSFUL;
}