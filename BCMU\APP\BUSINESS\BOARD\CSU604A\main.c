#include <stdio.h>
#include <rtthread.h>
#include "utils_thread.h"
#include "msg.h"
#include "utils_data_transmission.h"
#include "utils_time.h"
#include "north_main.h"
#include "sample.h"
#include "hal_adc.h"
#include "led.h"
#include "utils_server.h"
#include "server_id.h"
#include "storage.h"
#include "partition_table.h"
#include "update_manage.h"
#include "main.h"
#include "app_config.h"

// #include "syswatch.h"

static char s_sample_thread_stack[SAMPLE_THREAD_STACK_SIZE] RAM_SECTION_BSS;
static char s_north_thread_stack[NORTH_THREAD_STACK_SIZE] RAM_SECTION_BSS;
static char s_signal_led_thread_stack[LED_THREAD_STACK_SIZE] RAM_SECTION_BSS;

static protocol_process_t protocol_process_tab[] = {
    {PROTOCOL_BOTTOM_COMM, bottom_pack, bottom_parse},
};

static server_info_t g_server_group[] = {
    {{{SAMPLE_SERVER_ID,   "sample",  sizeof(s_sample_thread_stack),  s_sample_thread_stack,  SERVER_PRIO_MID}}, sample_init_sys, sample_main},
    {{{NORTH_SERVER_ID,   "north",  sizeof(s_north_thread_stack),  s_north_thread_stack,  SERVER_PRIO_LOW}}, init_north, north_comm_th},
    {{{LED_SERVER_ID,   "signal_led",  sizeof(s_signal_led_thread_stack),  s_signal_led_thread_stack,  SERVER_PRIO_LOW}}, init_led_blink, led_thread_entry},
};

static link_type_t link_type_tab[LINK_TYPE_MAX] = {
    #ifdef USING_LINK_COM
    {LINK_COM, usart_dev_init, com_dev_read, com_dev_write, com_dev_set},
    #endif

    #ifdef USING_LINK_485
    {LINK_COM, s485_dev_init, com_dev_read, com_dev_write, com_dev_set},
    #endif
};

static link_inst_t link_inst_tab[] = {
    {LINK_ILVDB,  LINK_COM, "rs485dev0"},
    {LINK_BSC_UIB03,  LINK_COM, "rs485dev0"},
    {LINK_INVERTER, LINK_COM, "uart8"},     //串口模式
    {LINK_DC_AC,      LINK_CAN, "can1",   CAN_FRAME_DATA_LEN_8},
    {LINK_DAC_IP,    LINK_IP, "wlan1"},
    {BMU_LINK_BCMU, LINK_CAN, "can0",   CAN_FRAME_DATA_LEN_8},
    {LINK_FIRE,     LINK_COM, "usart1"},
    {LINK_BSMU_BAK, LINK_CAN, "can5",   CAN_FRAME_DATA_LEN_16},
    {LINK_604A_MAIN,  LINK_COM, "usart0"},
    {LINK_604A_UIB01,  LINK_COM, "usart0"},
    {LINK_AIRSWITCH,    LINK_COM, "usart0"},
    {LINK_CSU, LINK_COM, "uart1"},
    {LINK_BCCU_SCU_USART, LINK_COM, "usart1"},
    {LINK_BCCU_CAN1, LINK_CAN, "can0", CAN_FRAME_DATA_LEN_8},
    {LINK_BCCU_CAN3, LINK_CAN, "can1", CAN_FRAME_DATA_LEN_8},
    {LINK_BMU,      LINK_CAN, "can1",   CAN_FRAME_DATA_LEN_8},
    {LINK_DC_DC,    LINK_CAN, "can2",   CAN_FRAME_DATA_LEN_8},
    {LINK_BSMU,     LINK_CAN, "can2",   CAN_FRAME_DATA_LEN_8},
    {LINK_BCMU,     LINK_CAN, "can4",   CAN_FRAME_DATA_LEN_8},
};

int main(void){
    init_flash_page_size(FLASH_PAGE_SIZE);
    init_crc();
    if (SUCCESSFUL != storage_init(&part_tab[0], sizeof(part_tab)/sizeof(part_info_t))) {
    	return FAILURE;
    }
    hal_board_gpio_init();
    register_link_type_info(link_type_tab, sizeof(link_type_tab) / sizeof(link_type_t));
    register_link_inst_tab_info(link_inst_tab, sizeof(link_inst_tab)/sizeof(link_inst_tab[0]));
    // 注册 协议所使用的函数接口
    register_protocol_process_info(protocol_process_tab, sizeof(protocol_process_tab) / sizeof(protocol_process_t));
    init_dev_init_tab();
    update_manage_init();
    // syswatch_init();
    create_server(&g_server_group[0], sizeof(g_server_group)/sizeof(g_server_group[0]));
}
