#include "south_dev_common.h"
#include "realdata_id_in.h"
#include "south_dev_modbus.h"

/* 命令请求头 */
static modbusrtu_cmd_head_t cmd_req[] = {
    {READ_HOLD_REG},
};

/* 命令应答头 */
static modbusrtu_cmd_head_t cmd_ack[] = {
    {READ_HOLD_REG},
};

static data_info_id_verison_t parse_real_data[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},//数据的字节长度
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_INPUT_DRY_POINT - 4},          // DI1
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_INPUT_DRY_POINT + 1 - 4},      // DI2
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_INPUT_DRY_POINT + 2 - 4},      // DI3
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_INPUT_DRY_POINT + 3 - 4},      // DI4
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_INPUT_DRY_POINT + 4 - 4},      // DI5
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_INPUT_DRY_POINT + 5 - 4},      // DI6
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_INPUT_DRY_POINT + 6 - 4},      // DI7
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_INPUT_DRY_POINT + 7 - 4},      // DI8
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_INPUT_DRY_POINT + 8 - 4},      // DI9
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_INPUT_DRY_POINT + 9 - 4},      // DI10
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_INPUT_DRY_POINT + 10 - 4},     // DI11
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_INPUT_DRY_POINT + 11 - 4},     // DI12
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_INPUT_DRY_POINT + 12 - 4},     // DI13
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_INPUT_DRY_POINT + 13 - 4},     // DI14
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_INPUT_DRY_POINT + 14 - 4},     // DI15
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_INPUT_DRY_POINT + 15 - 4},     // DI16
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_INPUT_DRY_POINT + 16 - 4},     // DI17
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAMPLE_AI_SIGNAL - 4},         // AI1
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 1 - 4},     // AI2
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 2 - 4},     // AI3
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 3 - 4},     // AI4
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 4 - 4},     // AI5
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 5 - 4},     // AI6
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 6 - 4},     // AI7
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 7 - 4},     // AI8
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 8 - 4},     // AI9
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 9 - 4},     // AI10
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 10 - 4},    // AI11
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 11 - 4},    // AI12
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 12 - 4},    // AI13
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 13 - 4},    // AI14
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 14 - 4},    // AI15
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 15 - 4},    // AI16
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL - 4},        // NTC1
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 1 - 4},    // NTC2
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 2 - 4},    // NTC3
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 3 - 4},    // NTC4
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 4 - 4},    // NTC5
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 5 - 4},    // NTC6
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 6 - 4},    // NTC7
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 7 - 4},    // NTC8
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 8 - 4},    // NTC9
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 9 - 4},    // NTC10
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 10 - 4},   // NTC11
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 11 - 4},   // NTC12
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 12 - 4},   // NTC13
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 13 - 4},   // NTC14
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 14 - 4},   // NTC15
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 15 - 4},   // NTC16
};

static cmd_parse_info_id_verison_t cmd_parse_info[] RAM_SECTION =
{
    {parse_real_data, sizeof(parse_real_data)/sizeof(data_info_id_verison_t)},
};

static cmd_t poll_cmd_tab[] = {
    {READ_MOCK_HOLD_REG,  CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[0]},
    {0},
};

int update_south_mock_dev_type(dev_inst_t* dev_inst)
{
    return handle_mock_south(dev_inst, poll_cmd_tab);
}
