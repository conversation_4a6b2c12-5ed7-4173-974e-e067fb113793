#include <string.h>
#include <stdlib.h>
#include <rtthread.h>
#include "storage.h"
#include "utils_server.h"
#include "utils_data_transmission.h"
#include "utils_thread.h"
#include "utils_string.h"
#include "utils_time.h"
#include "utils_rtthread_security_func.h"
#include "utils_data_type_conversion.h"
#include "device_type.h"
#include "msg.h"
#include "north_main.h"
#include "data_type.h"
#include "sps.h"
#include "dev_acmu.h"
#include "io_control.h"
#include "acmu_watchdog.h"
#include "const_define_in.h"
#include "realdata_id_in.h"
#include "utils_heart_beat.h"
#include "update_and_upload_manage.h"
#include "para_id_in.h"

static dev_inst_t* dev_acmu_north = NULL;
static int s_com_time_out_count = 0;
static dev_inst_t* s_north_dev_inst[PROTOCOL_INDEX_MAX] = {NULL};

static unsigned char s_dev_type[] = {
    DEV_CSU,
    DEV_NORTH_ACMU_APPTEST,
    DEV_BOTTOM_COMM_UPDATE_UPLOAD,
};

Static msg_map north_msg_map[] =
{
    {0,NULL},//临时添加解决编译问题
};

Static protocol_north_mgr_t * init_thread_data(link_inst_t* link_inst, unsigned char mod_id) {
    protocol_north_mgr_t* acmu_north_mgr = NULL;

    acmu_north_mgr = rt_malloc(sizeof(protocol_north_mgr_t));
    if (acmu_north_mgr == NULL)
        return NULL;
    
    acmu_north_mgr->cmd_buff = rt_malloc(sizeof(cmd_buf_t));
    if (acmu_north_mgr->cmd_buff == NULL) {
        goto NORTH_MGR_FREE;
    }

    acmu_north_mgr->cmd_buff->addr_dev_data = 0x01;
    acmu_north_mgr->link_inst = link_inst;
    return acmu_north_mgr;
     
NORTH_MGR_FREE:
    free(acmu_north_mgr);
    return NULL;
}

Static int handle_received_data(protocol_north_mgr_t* north_mgr) {

    for(int loop = 0; loop < PROTOCOL_INDEX_MAX ; loop ++)
    {
        if(SUCCESSFUL == protocol_parse_recv_process(s_north_dev_inst[loop], north_mgr->cmd_buff))
        {
            led_set_com_by_status(1);
            cmd_send(s_north_dev_inst[loop], north_mgr->cmd_buff);
            led_set_com_by_status(0);
            rt_sem_clear_value(&(north_mgr->link_inst->rx_sem));
            break;
        }
    }
    return 0;
}

/***************************************************************************
* 函数名称：TraceRS485Debug
* 功能描述：显示RS485调试信息      
***************************************************************************/
static int trace_rs485_debug( void )
{
	char buff[MAX_LETTERS];
    unsigned short comm_debug_cnt1 = 0;
    unsigned short comm_debug_cnt2 = 0;
    unsigned char prtcl_mode = 0;
    unsigned char data_flag1 = 0;
    unsigned char data_flag2 = 0;

    rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));
	
    //接收实时数据和实时告警数量
    get_one_data(ACMU_DATA_ID_REC_CALL_CMD_CNT, &comm_debug_cnt1);
    get_one_data(ACMU_DATA_ID_GET_REAL_ALM_CNT, &comm_debug_cnt2);
    rt_snprintf_s( buff, sizeof(buff), "20H=%u 21H=%u", comm_debug_cnt1, comm_debug_cnt2 );
    SetTraceStr( 3, buff );
	
    //接收历史告警和厂家
    rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));
    get_one_data(ACMU_DATA_ID_GET_HIS_ALM_CNT, &comm_debug_cnt1);
    get_one_data(ACMU_DATA_ID_GET_FACT_INFO_CNT, &comm_debug_cnt2);
    rt_snprintf_s( buff, sizeof(buff), "22H=%u 23H=%u", comm_debug_cnt1, comm_debug_cnt2);
    SetTraceStr( 4, buff );
	
    //接收公告参数和设置时间数量
    rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));
    get_one_data(ACMU_DATA_ID_GET_COMM_PARA_CNT, &comm_debug_cnt1);
    get_one_data(ACMU_DATA_ID_SET_TIME_CNT, &comm_debug_cnt2);
    rt_snprintf_s( buff, sizeof(buff), "24H=%u 25H=%u", comm_debug_cnt1, comm_debug_cnt2);
    SetTraceStr( 5, buff );
	
    //控制命令和设置公共参数
    rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));
    get_one_data(ACMU_DATA_ID_REC_CTRL_CMD_CNT, &comm_debug_cnt1);
    get_one_data(ACMU_DATA_ID_SET_COMM_PARA_CNT, &comm_debug_cnt2);
    get_one_data(ACMU_DATA_ID_PRTCL_MODE, &prtcl_mode);
    rt_snprintf_s( buff, sizeof(buff), "06H=%u 26H=%u %u", comm_debug_cnt1, comm_debug_cnt2, prtcl_mode);
    SetTraceStr( 6, buff );

    //记录数据标志
    rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));
    get_one_data(ACMU_DATA_ID_DATA_FLAG1, &data_flag1);
    get_one_data(ACMU_DATA_ID_DATA_FLAG2, &data_flag2);
    rt_snprintf_s( buff, sizeof(buff), "FLAG1:%x  FLAG2:%x", data_flag1, data_flag2);
    SetTraceStr( 7, buff );
	
	return SUCCESSFUL;
}

/***************************************************************************
* 函数名称：初始化RS485通讯重要标志位     
***************************************************************************/
static int init_rs485_data_flag( void ) {
    unsigned char data_flag1 = 0;
    unsigned char data_flag2 = 0;
    char buff[MAX_LETTERS];

    rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));

    get_one_data(ACMU_DATA_ID_DATA_FLAG1, &data_flag1);
    get_one_data(ACMU_DATA_ID_DATA_FLAG2, &data_flag2);

    data_flag1 |= FLAG1_ALMCH;		// 置位实时告警变化标志位
	data_flag1 |= FLAG1_TIMECH;		// 置位时间重置标志位
	data_flag2 |= FLAG2_HISALM;		// 置位历史告警变化标志位
	data_flag2 |= FLAG2_RST;		// 置位系统复位标志位

    set_one_data(ACMU_DATA_ID_DATA_FLAG1, &data_flag1);
    set_one_data(ACMU_DATA_ID_DATA_FLAG2, &data_flag2);

    rt_snprintf_s( buff, sizeof(buff), "FLAG1:%x  FLAG2:%x", data_flag1, data_flag2);
    SetTraceStr( 7, buff );

    return SUCCESSFUL;
}

void* init_protocol_north_comm(void * param) {
    server_info_t *server_info = (server_info_t *)param;
    protocol_north_mgr_t* north_mgr = NULL;

    for( int loop = 0; loop < ARR_SIZE(s_dev_type); loop++)
    {
        dev_acmu_north = init_dev_inst(s_dev_type[loop]);
        RETURN_VAL_IF_FAIL(dev_acmu_north != NULL, NULL);
        s_north_dev_inst[loop] = dev_acmu_north;
    }
    acmu_update_host_address();
    set_base_addr(ACMU_BASE_ADDR);
    server_info->server.server.map_size = sizeof(north_msg_map) / sizeof(msg_map);
    register_server_msg_map(north_msg_map, server_info);
    north_mgr = init_thread_data(dev_acmu_north->dev_type->link_inst, MOD_ACMU_NORTH);
    upload_manage_init();
    trace_rs485_debug();
    acmu_update_baudrate();
    init_rs485_data_flag();
    return north_mgr;
}

/* 收发线程 */
void protocol_north_comm_th(void *param) {
    protocol_north_mgr_t* north_mgr = (protocol_north_mgr_t*)param;
    thread_monitor_register("protocol_north");
    while (is_running(TRUE)) 
    {
        thread_monitor_update_heartbeat();
        if(RT_EOK == rt_sem_take(&(north_mgr->link_inst->rx_sem), THREAD_TICK * 5)){
            s_com_time_out_count = 0;
              if (FAILURE == linker_recv(s_north_dev_inst[0], north_mgr->cmd_buff)) {
                continue;
            }
            handle_received_data(north_mgr);
        }
        if(s_com_time_out_count > 10) {
            s_com_time_out_count = 0;
            north_mgr->link_inst->r_cache.index = 0;
            north_mgr->link_inst->r_cache.len = 0;
        }
        s_com_time_out_count++;
        trace_rs485_debug();
        continue;
    }
}