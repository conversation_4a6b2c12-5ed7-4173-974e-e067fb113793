#include <rtthread.h>
#include <rtdevice.h>
#include <math.h>
#include <string.h>
#include "sample.h"
#include "msg.h"
#include "utils_server.h"
#include "utils_thread.h"
#include "utils_math.h"
#include "pin.h"
// #include "realdata_save.h"
// #include "realdata_id_in.h"
// #include "para_id_in.h"
#include "adc_ctrl.h"
#include "meter.h"
// #include "para_manage.h"
// #include "device_common.h"


typedef struct
{
    adc_sample_channel     sample_channel;
    rt_base_t              select_channel;
    int                    select_status;
}select_sample_pin_t;

static select_sample_pin_t s_select_sample[] = {
    {VTA, 0, 0},    //环境温度采样通道
    {VT0, 3, 0},    //基准电压采样通道

};

Static float s_refer_volt = 0;  // 基准电压
Static unsigned char s_state_temp_sample_configured = FALSE;
Static float s_environment_temperature = 0;

/* 静态函数声明 */
Static float linear_insert(int data, const int *TABLE, int num);
Static float get_environment_temp(void);

static msg_map sample_msg_map[] =
{
     {0,NULL},//临时添加解决编译问题
};

//电阻-温度对应表
const int  NTC_Table[180]=
{
    459591, 432381, 406957, 383189, 360961, 340163, 320695, 302464, 285383, 269374,     //-55~-46
    254362, 240281, 227066, 214659, 203007, 192059, 181768, 172091, 162988, 154422,     //-45~-36
    146358, 138763, 131608, 124865, 118507, 112511, 106854, 101515, 96474 , 91713 ,     //-35~-26
    87215 , 82963 , 78944 , 75143 , 71546 , 68143 , 64921 , 61870 , 58980 , 56241 ,     //-25~-16
    53645 , 51184 , 48849 , 46634 , 44532 , 42537 , 40642 , 38842 , 37132 , 35506 ,     //-15~-06
    33961 , 32491 , 31093 , 29763 , 28498 , 27293 , 26145 , 25052 , 24010 , 23018 ,     //-05~4
    22072 , 21169 , 20309 , 19488 , 18705 , 17957 , 17243 , 16561 , 15910 , 15288 ,     //5~14
    14693 , 14125 , 13582 , 13062 , 12565 , 12089 , 11634 , 11199 , 10781 , 10382 ,     //15~24
    10000 , 9633  , 9282  , 8945  , 8622  , 8313  , 8016  , 7731  , 7458  , 7196  ,     //25~34
    6944  , 6703  , 6471  , 6248  , 6034  , 5828  , 5631  , 5441  , 5258  , 5083  ,     //35~44
    4914  , 4751  , 4595  , 4445  , 4300  , 4160  , 4026  , 3897  , 3773  , 3653  ,     //45~54
    3538  , 3426  , 3319  , 3216  , 3116  , 3020  , 2927  , 2838  , 2751  , 2668  ,     //55~64
    2588  , 2510  , 2435  , 2363  , 2293  , 2226  , 2161  , 2098  , 2037  , 1978  ,     //65~74
    1921  , 1866  , 1813  , 1762  , 1712  , 1664  , 1618  , 1573  , 1529  , 1487  ,     //75~84
    1446  , 1407  , 1369  , 1332  , 1296  , 1261  , 1227  , 1195  , 1163  , 1133  ,     //85~94
    1103  , 1074  , 1046  , 1019  , 993   , 968   , 943   , 919   , 896   , 873   ,     //95~104
    851   , 830   , 809   , 789   , 770   , 751   , 733   , 715   , 697   , 681   ,     //105~114
    664   , 648   , 633   , 618   , 603   , 589   , 575   , 562   , 549   , 536   ,     //115~124
};

/**
 * @brief  线性插值
 * @param[in]    data  电阻值
 * @param[in]    TABLE 电阻-温度对应表
 * @param[in]    num   电阻-温度对应表元素数量
 * @retval       真实温度(float)
 */
Static float linear_insert(int data, const int *TABLE, int num)
{
    float Temp;
    int Array[3];
    // 温度有效值 -40~125
    if (data < TABLE[LOW_TEMP+55])                 //Beyond Lowest Temperature
    {
        if (data > TABLE[HIGH_TEMP+55])            //Beyond Highest Temperature
        {
            lookup_TAB(data, TABLE, num, Array);      //Lookup data

            if (data == Array[2])
                Temp = (Array[0] * 1.0- 55);   //55为开始点对应的温度，也就是0度是第几个，此处用的表0度为第56个，向下取整，则-55.
            else
                Temp = (Array[0] * 1.0 - 55) + (data * 1.0 - Array[2]) * 1.0 / (Array[1] * 1.0 - Array[2]);  //线性插值计算
        }
        else
            Temp = TEMP_INVALID_HIGH;//130.0; //mark Underflow
    }
    else
        Temp = TEMP_INVALID_LOW;//-45.0; //mark Overflow

    return(Temp);
}

// 获取环境温度
Static float get_environment_temp(void)
{
    rt_adc_device_t adc_dev = RT_NULL;
    float sample_temp = 0;    // 采样获得的环境温度
    int   resistance = 0;     // 热敏电阻电阻值
    float v_sample = 0;             // 采样电压
    int num = sizeof(NTC_Table) / sizeof(NTC_Table[0]);

    adc_dev = (rt_adc_device_t)rt_device_find(TEMP_ADC_DEV_NAME);
    RETURN_VAL_IF_FAIL(adc_dev != RT_NULL, TEMP_INVALID_LOW);

    if (FALSE == s_state_temp_sample_configured) {
        //获取基准电压
        v_sample = get_adc_volt(adc_dev, s_select_sample[VT0].select_channel);
        s_refer_volt = v_sample * 17.5 / 7.5;
        s_state_temp_sample_configured = TRUE;
    }

    v_sample = get_adc_volt(adc_dev, s_select_sample[VTA].select_channel);
    resistance = (int)( (2.21 * s_refer_volt / v_sample - 4.26) * 1000 );           // 计算热敏电阻值
    sample_temp = linear_insert(resistance, NTC_Table, num);          // 由电阻值得到对应的温度值

    // 低温下电阻分压采集值接近0，抖动会较大，通过加权滤波抑制下
    if( ((sample_temp - s_environment_temperature > 0.3) || (sample_temp - s_environment_temperature < -0.3)) && \
        (sample_temp < -30))
    {
        s_environment_temperature = (s_environment_temperature * 4 + sample_temp) / 5;
    }
    else
    {
        s_environment_temperature = (s_environment_temperature + sample_temp) / 2;
    }

    return s_environment_temperature;
}

void* sample_init_sys(void *param){
    server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(sample_msg_map) / sizeof(msg_map);
    register_server_msg_map(sample_msg_map, server_info);
}

void sample_main(void* parameter)
{
    float temp = 0;
    while (is_running(TRUE))
    {
        temp = get_environment_temp();
        rt_thread_mdelay(SAMPLE_THREAD_DELAY_TIME);
    }
}
