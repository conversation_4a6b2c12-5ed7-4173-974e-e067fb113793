#ifndef __DOWNLOAD_TCP_H_
#define __DOWNLOAD_TCP_H_


#include "lwip/err.h"
#include "lwip/pbuf.h"
#include "lwip/tcp.h"
#include "common.h"

#ifdef __cplusplus
extern "C" {
#endif

#define TCP_THREAD_PRIORITY_UPDATE  15
#define TCP_THREAD_TIMESLICE_UPDATE  5
#define TCP_REMOTE_START_NET_UPDATE_MAX_TIME 60

#define TIMEOUT_WAITTING_MAX_TIME 600   //进入近端网口模式后的超时等待时间
#define TIME_NETREMOTENORTH_MAX  60    // 网口远程下载60S无通信判为通信断

typedef struct tcp_pcb download_tcp_pcb;

//客户端链接管理结构体 by hxk
typedef struct
{
    int MaxLinkCnt;                  //链接中断时，连续重发建立链接最大的次数
    download_tcp_pcb *pcb;           //接受消息的套接字
    rt_timer_t Client_Manage_timer;    //启动升级定时器
}ClientLinkMangageStruct;

void processDownload(void* parameter);
void StartNetUpdateDL(void);
//void NetConnTimeOutCallBack(void *download_pcb);
//void NetTranTimeOutCallBack(void *download_pcb);
BOOLEAN GetClientUpdateFlag(void); //判断是客户端升级还是服务器端升级 by hxk
BOOLEAN getTimeoutQuitFlag(void);
BOOLEAN setTimeoutQuitFlag(BOOLEAN flag_timeout);

void SetTimeOutCounter(void);
void ClearTimeOutCounter(void);
WORD GetTimeOutCounter(void);
BOOLEAN JudgeNetRemoteCommDisconnect(void);
BOOLEAN NetRemoteNorthTimeCount(void);
err_t download_accept(void *arg, download_tcp_pcb *newpcb, err_t err);
err_t download_callback(void *arg, download_tcp_pcb *pcb, struct pbuf *tcp_recv_pbuf, err_t err);
void TimeOutCounterThread(void *parameter);
void CloseUpdateLink(void);
void DeleteTempThread(void);
BOOLEAN GetRemoteUpdateFlag(void);

#ifdef __cplusplus
}
#endif

#endif
