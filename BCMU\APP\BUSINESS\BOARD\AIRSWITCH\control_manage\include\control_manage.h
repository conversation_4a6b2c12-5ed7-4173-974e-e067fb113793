/**
 * @brief
 */

#ifndef _AIRSWITCH_CONTROL_MANAGE_H_
#define _AIRSWITCH_CONTROL_MANAGE_H_

#ifdef __cplusplus
extern "C" {
#endif

typedef enum {
    INDEX_SWITCH_OPEN_CTRL_STATE = 0,    ///< 空开分闸控制状态
    INDEX_SWITCH_CLOSE_CTRL_STATE,       ///< 空开合闸控制状态
    SWITCH_CLOSE_CTRL_STATE_NUM
} switch_ctrl_state_e;

#define CONTROLLER_FAIL_TICK (1000*60*5)   // 控制分合闸失败次数累计时间 5min

void* init_control_manage(void *param);
void control_manage_main(void* parameter);
char ctrl_closing(void);
char ctrl_opening(void);
unsigned char get_loop_status(void);
char airswitch_brake_ctrl(int brake);
char airswitchMotorCtrl(int speed, int direction);
int motor_ctrl_debug(int speed, int direction);

#ifdef __cplusplus
}
#endif      //< __cplusplus

#endif      //< __AIRSWITCH_CONTROL_MANAGE_H_
