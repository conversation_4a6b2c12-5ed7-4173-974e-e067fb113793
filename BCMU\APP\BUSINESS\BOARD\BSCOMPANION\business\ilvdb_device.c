#include <stdio.h>
#include <string.h>
#include "cmd.h"
#include "sps.h"
#include "dev_ilvdb.h"
#include "protocol_layer.h"

/* 设备缓冲区长度 */
#define R_BUFF_LEN                    512      ///<  接收缓冲区长度
#define S_BUFF_LEN                    512      ///<  发送缓冲区长度

//  协议默认信息
#define     AIRSWITCH_DEVICE          0x2F
#define     AIRSWITCH_ADDR            0x01
#define     DEST_DEVICE          0x7D        //CSU设备类型
#define     DEST_ADDR            1           //CSU设备地址
#define     BROADCAST_ADDR       0x00
#define     SHEET_SN             0x05
#define     CTRL_VALUE           0x80

//  应用层返回码
#define     APP_RTN_TYPE            0x01    ///<  返回码标志，用于区分是应用层还是链路层
#define     APP_RTN_OK              0x00    ///<  数据正常
#define     APP_RTN_FUNC_ERR        0x01    ///<  功能码错误
#define     APP_RTN_CRC_ERR         0x08    ///<  crc错误
#define     APP_RTN_SHEET_SN_ERR    0x0A    ///<  表序号错误

//  附加码
#define     CMD_APPEND_TABLE_1            0x0010          ///<  参数表1
#define     CMD_APPEND_TABLE_2            0x0011          ///<  参数表2
#define     CMD_APPEND_CTRL               0xFFFF          ///<  遥控命令

static unsigned char s_switch_data_config = SWITCH_CONFIG_MAX_NUMBER;

/* 命令数据存储变量 */
static Ilvdb_sync_data_t    ilvdb_syncflag_data = {0};
static Ilvdb_board_data_t   ilvdb_board_data = {0};
//static Ilvdb_switch_data_t  Ilvdb_switch_data = {0};
static Ilvdb_real_data_t    ilvdb_real_data[SWITCH_CONFIG_MAX_NUMBER] = {0};
static Ilvdb_para1_data_t   ilvdb_para1_data[SWITCH_CONFIG_MAX_NUMBER] = {0};
static Ilvdb_para2_data_t   ilvdb_para2_data[SWITCH_CONFIG_MAX_NUMBER] = {0};
static Ilvdb_fac_data_t     ilvdb_fac_data = {0};
static Ilvdb_data_t         ilvdb_data = {0};


/* 命令请求头 */
static bottom_comm_cmd_head_t cmd_req[] = {
    // 通信命令
    {BOTTOM_PROTO_TYPE_COMM, CMD_LINK,           CMD_APPEND_NONE,    BOTTOM_RTN_APP_ACK},
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA,  CMD_APPEND_NONE,    BOTTOM_RTN_APP_ACK},
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_BATCH_PARA, CMD_APPEND_TABLE_1, BOTTOM_RTN_APP_ACK},
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_BATCH_PARA, CMD_APPEND_TABLE_1, BOTTOM_RTN_APP_ACK},
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_BATCH_PARA, CMD_APPEND_TABLE_2, BOTTOM_RTN_APP_ACK},
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_BATCH_PARA, CMD_APPEND_TABLE_2, BOTTOM_RTN_APP_ACK},
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL,       CMD_APPEND_CTRL,    BOTTOM_RTN_APP_ACK},
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_DATA_OLD, CMD_APPEND_NONE, BOTTOM_RTN_APP_ACK},
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_TRIG_OLD, CMD_APPEND_UPDATE_REQ, BOTTOM_RTN_APP_ACK},
};

/* 命令应答头 */
static bottom_comm_cmd_head_t cmd_ack[] = {
    // 通信命令
    {BOTTOM_PROTO_TYPE_COMM, CMD_LINK,           CMD_APPEND_NONE,    BOTTOM_RTN_APP_CORRECT},
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA,  CMD_APPEND_NONE,    BOTTOM_RTN_APP_CORRECT},
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_BATCH_PARA, CMD_APPEND_TABLE_1, BOTTOM_RTN_APP_CORRECT},
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_BATCH_PARA, CMD_APPEND_TABLE_1, BOTTOM_RTN_APP_CORRECT},
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_BATCH_PARA, CMD_APPEND_TABLE_2, BOTTOM_RTN_APP_CORRECT},
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_BATCH_PARA, CMD_APPEND_TABLE_2, BOTTOM_RTN_APP_CORRECT},
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL,       CMD_APPEND_CTRL,    BOTTOM_RTN_APP_CORRECT},
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_DATA_OLD, CMD_APPEND_NONE, BOTTOM_RTN_APP_CORRECT},
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_TRIG_OLD, CMD_APPEND_UPDATE_ACK, BOTTOM_RTN_APP_CORRECT},
};

/* 命令字段信息   */

static data_info_t cmd_real_data_info[] = {
    {SEG_DATA, SW_TYPE_INT8U,          ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT},  ///段1（同步标志）字节长度
    {SEG_DATA, SW_TYPE_INT8U,          ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_syncflag_data.LINK_Master_Address)},  /// 建链主机地址
    {SEG_DATA, SW_TYPE_INT16U,         ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_syncflag_data.CRC_Code)},    /// 批量公共参数段1的CRC码
    {SEG_DATA, SW_TYPE_INT8U,          ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_syncflag_data.Sync_Flag)},  /// 空开同步标志

    {SEG_DATA, SW_TYPE_INT16S,         ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_board_data.Board_temp1)},  /// 建链主机地址
    {SEG_DATA, SW_TYPE_BIT,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_board_data.Board_over_temp1)},    /// 批量公共参数段1的CRC码
    {SEG_DATA, SW_TYPE_INT16S,         ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_board_data.Board_temp2)},  /// 建链主机地址
    {SEG_DATA, SW_TYPE_BIT,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_board_data.Board_over_temp2)},    /// 批量公共参数段1的CRC码

    {SEG_LOOP, SW_TYPE_CHAR,           ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT, &s_switch_data_config}, //电池簇 LOOP开始
    {SEG_DATA, SW_TYPE_INT8U,          ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_real_data[0].AirSwitch_Address)},
    {SEG_DATA, SW_TYPE_INT8U,          ARRAY_SIZE_1, DOP_0, DATA_DROP, 16, NOT_NEED_INTERACT},    /// 段2（模拟量）字节长度
    {SEG_DATA, SW_TYPE_INT16S,         ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_real_data[0].Switch_Output_Voltage)},  /// 空开输出电压
    {SEG_DATA, SW_TYPE_INT16S,         ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_real_data[0].Switch_Current)},   /// 空开电流
    {SEG_DATA, SW_TYPE_INT16S,         ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_real_data[0].Switch_Power)},   /// 空开功率
    {SEG_DATA, SW_TYPE_INT32S,         ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_real_data[0].Switch_Input_Power_Energy)},  /// 空开输入电量
    {SEG_DATA, SW_TYPE_INT32S,         ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_real_data[0].Switch_Output_Power_Energy)},  /// 空开输入电量
    {SEG_DATA, SW_TYPE_INT16S,         ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_real_data[0].Fault_Opening_Times)},  /// 空开触点温度
    
    {SEG_DATA, SW_TYPE_INT8U,          ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT},  /// 段3（状态量）字节长度
    {SEG_DATA, SW_TYPE_BIT,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_real_data[0].Switch_Loop_Status)},  /// 空开回路状态
    {SEG_DATA, SW_TYPE_BIT,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_real_data[0].Switch_DownLoad_Status)},   /// 空开下电状态
    {SEG_DATA, SW_TYPE_BIT,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_real_data[0].Connector_status)},  /// 授权状态
    {SEG_DATA, SW_TYPE_BIT,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_real_data[0].Authorized_status)},   /// 空开有电状态
    {SEG_DATA, SW_TYPE_BIT,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_real_data[0].Switch_Power_Status)},  /// 空开一键功能状态
    {SEG_DATA, SW_TYPE_BIT,            ARRAY_SIZE_3, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_real_data[0].Switch_Digital_Reserve[0])}, 

    {SEG_DATA, SW_TYPE_INT8U,          ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT},  /// 段4（告警量）字节长度
    {SEG_DATA, SW_TYPE_BIT,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_real_data[0].Switch_Over_Current_Alarm)},  /// 空开过流告警
    {SEG_DATA, SW_TYPE_BIT,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_real_data[0].Switch_Open)},   /// 空开断开
    {SEG_DATA, SW_TYPE_BIT,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_real_data[0].Switch_Low_Voltage_Download)},  /// 空开低压下电
    {SEG_DATA, SW_TYPE_BIT,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_real_data[0].Switch_Download)},  /// 空开下电
    {SEG_DATA, SW_TYPE_BIT,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_real_data[0].Switch_Close_Failure)},   /// 空开合闸失败
    {SEG_DATA, SW_TYPE_BIT,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_real_data[0].Switch_Opening_Failure)},  /// 空开分闸失败
    {SEG_DATA, SW_TYPE_BIT,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_real_data[0].Switch_Over_Current_Protect)},   /// 空开过流保护告警
    {SEG_DATA, SW_TYPE_BIT,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_real_data[0].Switch_Alarm_Reserve[0])}, 
    {SEG_LOOP_OVER},

};

static data_info_t cmd_para1_data_info[] = {
    {SEG_LOOP, SW_TYPE_CHAR,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT, &s_switch_data_config}, //电池簇 LOOP开始
    {SEG_DATA, SW_TYPE_INT8U,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_para1_data[0].AirSwitch_Address)},
    {SEG_DATA, SW_TYPE_INT8U,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 16, NOT_NEED_INTERACT},           /// 段1（私有参数1）字节长度
    {SEG_DATA, SW_TYPE_INT8U,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_para1_data[0].Switch_Download_Enabled)},  /// 空开下电使能
    {SEG_DATA, SW_TYPE_INT16U, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_para1_data[0].Switch_Download_Voltage_Threshold)},   /// 空开下电电压阈值
    {SEG_DATA, SW_TYPE_INT16U, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_para1_data[0].Switch_Upload_Voltage_Threshold)},  /// 空开上电电压阈值
    {SEG_DATA, SW_TYPE_INT8U,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_para1_data[0].Switch_Over_Current_Protect_Enabled)},   /// 空开过流保护使能
    {SEG_DATA, SW_TYPE_INT16U, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_para1_data[0].Switch_Over_Current_Protect_Enabled)},   /// 空开过流告警阈值  
    {SEG_DATA, SW_TYPE_INT8U,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_para1_data[0].Switch_Upload_Authorization)},  /// 空开上电授权
    {SEG_DATA, SW_TYPE_INT16U, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_para1_data[0].Switch_Over_Current_Protect_Delay)},  /// 空开过流保护延时
    {SEG_DATA, SW_TYPE_INT8U,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_para1_data[0].Switch_Over_Current_Resume_Times)},   /// 空开过流保护自动恢复次数
    {SEG_DATA, SW_TYPE_INT8U,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_para1_data[0].Switch_Over_Current_Resume_Space)},  /// 空开过流保护自动恢复间隔
    {SEG_DATA, SW_TYPE_INT16U, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_para1_data[0].Switch_Over_Current_Protect_Threshold)},   /// 空开过流保护阈值
    {SEG_DATA, SW_TYPE_INT8U,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_para1_data[0].Switch_Disconnect_judgment_mode)},  /// 空开断开判断方式
    {SEG_LOOP_OVER},
};

static data_info_t cmd_para2_data_info[] = {
    {SEG_LOOP, SW_TYPE_CHAR,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT, &s_switch_data_config}, //电池簇 LOOP开始
    {SEG_DATA, SW_TYPE_INT8U,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_para2_data[0].AirSwitch_Address)},
    {SEG_DATA, SW_TYPE_INT8U,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 12, NOT_NEED_INTERACT},  /// 段1（私有参数2）字节长度
    {SEG_DATA, SW_TYPE_INT32S, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_para2_data[0].OutPut_Electricity_Benchmark_Parameters)},  /// 空开输出电量基准参数
    {SEG_DATA, SW_TYPE_INT32S, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_para2_data[0].Input_Electricity_Benchmark_Parameters)},   /// 空开输入电量基准参数
    {SEG_DATA, SW_TYPE_INT16U, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_para2_data[0].Switch_Current_Slope)},  /// 空开电流斜率
    {SEG_DATA, SW_TYPE_INT16S, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_para2_data[0].Switch_Current_Offset)},   /// 空开电流零点
    {SEG_LOOP_OVER},
};

static data_info_t cmd_fac_data_info[] = {
    {SEG_DATA, SW_TYPE_INT8U, ARRAY_SIZE_1,  DOP_0, DATA_DROP, 89, NOT_NEED_INTERACT},           /// 段1（版本信息）字节长度
    {SEG_DATA, SW_TYPE_CHAR,  ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_fac_data.Ilvdb_System_Name[0])},        /// 空开系统名称
    {SEG_DATA, SW_TYPE_CHAR,  ARRAY_SIZE_20, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_fac_data.Ilvdb_SoftwareVersion[0])},    /// 空开软件版本
    {SEG_DATA, SW_TYPE_DATESTRUCT, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_fac_data.Ilvdb_Software_ReleaseDate)},  /// 空开软件发布日期
    {SEG_DATA, SW_TYPE_INT8U,  ARRAY_SIZE_1,  DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_fac_data.Ilvdb_Device_Type)},          /// 空开序列号
    {SEG_DATA, SW_TYPE_CHAR,   ARRAY_SIZE_12, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_fac_data.Ilvdb_SN[0])},                /// 空开序列号
    {SEG_DATA, SW_TYPE_INT16S, ARRAY_SIZE_10, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &(ilvdb_fac_data.Ilvdb_Rated_Current[0])},     /// 空开额定电流 
};

static cmd_parse_info_t cmd_parse_info[] = {
    {&cmd_real_data_info[0],    sizeof(cmd_real_data_info)/sizeof(data_info_t),     &ilvdb_data.Ilvdb_real_data,    sizeof(Ilvdb_real_data_t)},
    {&cmd_para1_data_info[0],   sizeof(cmd_para1_data_info)/sizeof(data_info_t),    &ilvdb_data.Ilvdb_para1_data,   sizeof(Ilvdb_para1_data_t)},
    {&cmd_para2_data_info[0],   sizeof(cmd_para2_data_info)/sizeof(data_info_t),    &ilvdb_data.Ilvdb_para2_data,   sizeof(Ilvdb_para2_data_t)},
    {&cmd_fac_data_info[0],     sizeof(cmd_fac_data_info)/sizeof(data_info_t),      &ilvdb_data.Ilvdb_fac_data,     sizeof(Ilvdb_fac_data_t)},
};

static cmd_parse_info_t cmd_pack_info[] = {
    {&cmd_para1_data_info[0],   sizeof(cmd_para1_data_info)/sizeof(data_info_t),    &ilvdb_data.Ilvdb_para1_data,   sizeof(Ilvdb_para1_data_t)},
    {&cmd_para2_data_info[0],   sizeof(cmd_para2_data_info)/sizeof(data_info_t),    &ilvdb_data.Ilvdb_para2_data,   sizeof(Ilvdb_para2_data_t)},
};

/* 通信协议命令表 */
static cmd_t no_poll_cmd_tab[] = {
    {UIB_LINK,      CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(bottom_comm_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {UIB_GET_AI,    CMD_PASSIVE, &cmd_req[1], &cmd_ack[1], sizeof(bottom_comm_cmd_head_t), NULL, NULL,SELF_PACK, SELF_PARSE},
    {UIB_GET_DI,    CMD_PASSIVE, &cmd_req[2], &cmd_ack[2], sizeof(bottom_comm_cmd_head_t), NULL, NULL,SELF_PACK, SELF_PARSE},
    {UIB_GET_BARCODE, CMD_PASSIVE, &cmd_req[3], &cmd_ack[3], sizeof(bottom_comm_cmd_head_t), NULL, NULL,SELF_PACK, SELF_PARSE},
    {UIB_DO_CTRL,   CMD_PASSIVE, &cmd_req[4], &cmd_ack[4], sizeof(bottom_comm_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {UIB_SET_BARCODE, CMD_PASSIVE, &cmd_req[5], &cmd_ack[5], sizeof(bottom_comm_cmd_head_t), NULL, NULL,SELF_PACK, SELF_PARSE},
    {UIB_SET_CSU_FAULT_DO,CMD_PASSIVE, &cmd_req[6], &cmd_ack[6], sizeof(bottom_comm_cmd_head_t), NULL, NULL,SELF_PACK, SELF_PARSE},
    {UIB_SET_FAN,   CMD_PASSIVE, &cmd_req[7], &cmd_ack[7], sizeof(bottom_comm_cmd_head_t), NULL, NULL,SELF_PACK, SELF_PARSE},
    {UIB_UPDATE_DATA,  CMD_BROADCAST|CMD_PASSIVE,  &cmd_req[8], &cmd_ack[8], sizeof(bottom_comm_cmd_head_t), },
    {UIB_UPDATE_TRIG,  CMD_PASSIVE,  &cmd_req[9], &cmd_ack[9], sizeof(bottom_comm_cmd_head_t), },
    {0},
};

static dev_type_t dev_ilvdb = {
    DEV_CSU, 1, PROTOCOL_BOTTOM_COMM, LINK_ILVDB, R_BUFF_LEN, S_BUFF_LEN, BOTTOM_CSU_TYPE, no_poll_cmd_tab,
};

dev_type_t* init_dev_ilvdb(void){
    return &dev_ilvdb;
}