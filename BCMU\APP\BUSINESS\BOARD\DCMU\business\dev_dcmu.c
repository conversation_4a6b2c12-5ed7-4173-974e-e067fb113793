#include "dev_dcmu.h"
#include <rtthread.h>
#include <rtdevice.h>
#include "data_type.h"
#include "linklayer.h"
#include "para_id_in.h"
#include "para_manage.h"

Static dcmu_comm_baudrate_type_e s_last_baudrate = COMM_BAUD_RATE_9600;

int dcmu_update_baudrate(void) {
    safe_sid_data_t baudrate = {0};
    signed char ret = 0;
    struct serial_configure def_config = RT_SERIAL_CONFIG_DEFAULT;
    link_inst_t* link_inst = NULL;

    unsigned int baud_tab[COMM_BAUD_RATE_NUM] = {
        1200, 2400, 4800, 9600, 19200, 38400
    };

    ret = get_one_para(DCMU_PARA_ID_UART_BAUDRATE_OFFSET + 1, &baudrate.uc_val);
    if (FAILURE == ret || baudrate.uc_val >= COMM_BAUD_RATE_NUM) {
        return FAILURE;
    }

    if (baudrate.uc_val != s_last_baudrate) {
        link_inst = get_link_inst(LINK_DCMU);
        if (link_inst == NULL) {
            return FAILURE;
        }
        def_config.baud_rate = baud_tab[baudrate.uc_val];
        ret = uart_dev_config(link_inst->name, &def_config);
        if (ret != SUCCESSFUL) {
            rt_kprintf("change baudrate from %d to %d failure\n", baud_tab[s_last_baudrate], baud_tab[baudrate.uc_val]);
            return FAILURE;
        }
        rt_kprintf("change baudrate from %d to %d success\n", baud_tab[s_last_baudrate], baud_tab[baudrate.uc_val]);
        s_last_baudrate = baudrate.uc_val;
    }

    return SUCCESSFUL;
}

int dcmu_update_host_address(void) {
    safe_sid_data_t address = {0};
    signed char ret = 0;
    const unsigned short max_address = 254;
    unsigned char host_address = 1;

    ret = get_one_para(DCMU_PARA_ID_DEVICE_ADDR_OFFSET, &address.us_val);
    if (FAILURE == ret || address.us_val > max_address) {
        return FAILURE;
    }

    host_address = address.us_val;
    set_host_addr(host_address);
    return SUCCESSFUL;
}
