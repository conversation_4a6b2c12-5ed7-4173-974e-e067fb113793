#ifndef SOFTWARE_SRC_APP_SLAVEDATA_H_
#define SOFTWARE_SRC_APP_SLAVEDATA_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "common.h"
#include "battery.h"

typedef struct
{
    BYTE      ucNormalSlaveNum;  //正常从机数量
    BYTE      ucNormalSalveNumNoMaster; //排除主机后，正常从机的数量
    BYTE      ucBusCurrValidNum; //电流异常数量
    FLOAT     fMaxCurrInAll;    //各组电流中最大值
    FLOAT     fMaxBusCurrInAll; //各组母排电流中最大值
    FLOAT     fBusVolMax;
    FLOAT     fSysMaxBattVol;   //并联电池最大电压
    FLOAT     fMinBattVolt;
    FLOAT     fSysVol_slave;  //the bus voltage of system, calculate considering current, meanless for slave battery.
    FLOAT     fBattTotalCurr;         //电池总电流
    BOOLEAN   bSlaveChargeBreak;    //充电输入断
    BOOLEAN   bMultBattDisCharge;      //是否有放电电流
    BOOLEAN   bSysLimit;
    BOOLEAN   bMyLimit;
	FLOAT     fBattAverageVol;            ///电池平均电压
    BOOLEAN   bB3BattExist;
    FLOAT     fBattBalanceCurr;
    BYTE      ucChgStaNum;
    FLOAT     fChgDischgAveCurr;
    WORD      wAveSoc; //簇内平均soc
    FLOAT     fAveBattVolt;
    WORD      wAveSocInAllCluster; //所有簇的平均soc
    WORD      wSlaveAvgSOC;
    UINT32    ulTotalSlaveSOC;         //除主机外，所有正常从机的SOC之和
    BOOLEAN   bCAN2DischgExist;
    BYTE      ucDischgNormalNum;
    BYTE      ucBattExistNum;
    BYTE      ucFmNormalSlaveNum;      //调频时正常从机数量统计
    BYTE      ucFMChgAndDischgDisable; //充放电禁止状态
    BYTE      ucFMBatteryleftcapacity; //整个站点的剩余SOC
    UINT32    ulTotalSOC;             //整个站点总的SOC
    FLOAT     fFMCurrentMaxChgPower;   //整个站点的最大功率
}T_SlaveDataResult;

typedef struct
{
    BYTE   ucIndex;
    FLOAT  fCurrSum;
    WORD   wCapSum;
    BYTE   ucDischBattNum;
    ULONG  ulHighSOCSum;
    FLOAT  fBattVoltSum;
}T_MultBattEqualPara;

/*********************  函数原型定义  **********************/
BOOLEAN getSysLimit(void);
FLOAT getSysVolt(void);
FLOAT getMaxCurrInAll(void);
FLOAT getMaxBusCurrInAll(void);
FLOAT getBattAverageVol(void);
FLOAT getBattTotalCurr(void);
FLOAT getSlaveDataMin1(void);
FLOAT getSlaveDataMax(FLOAT fMaxBatVol);
FLOAT getSlaveDataMin(FLOAT fMinBatVol);
BYTE getSlaveNum(void);
BOOLEAN IfMasterChgFull(BYTE ucChargeMode);
BYTE getBusCurrValidNum(void);
BOOLEAN getMaxVolAndMaxSoc(FLOAT *pfMaxBatVol, WORD *pwMaxBatSOC, BYTE *pucSlaveNum);
void SlaveData(BYTE ucCellVoltNum, T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
void getBoostVol(FLOAT *pfBoostVol, WORD *pwMaxBatSOC, FLOAT fDischargeHopeVol,FLOAT fCompVol);
FLOAT getChgVolt(FLOAT fChargeFullVol);
void getSlavesRealData(T_SlavesRealData *ptSlavesRealData);
void getSlavesDataResult(T_SlaveDataResult *ptSlavesDataResult);
void setTestSlaveResult(T_SlaveDataResult *ptSlavesDataResult);
BOOLEAN isSingleBatt(void);
BOOLEAN getChargeBreakStatus(void);
BOOLEAN getMultBattDisChargeStatus(void);
BOOLEAN isExternalPowerOn(void);
BOOLEAN MultBattDataUnitest(BOOLEAN bFlag);
BOOLEAN IfExistExternalPowerOn(VOID);
BOOLEAN getBattType(void);
FLOAT getBalanceCurr(void);
BYTE GetChgStaNum(void);
BOOLEAN IsBattInSys(BYTE ucAddr);
BOOLEAN IsConnectedToSnmpAgent(BYTE ucAddr);
FLOAT GetAveCurr(void);
WORD GetAveSoc(void);
FLOAT GetAveBattVolt(void);
WORD GetAveSocInAllCluster(void);
BOOLEAN GetCAN2BattStatus(void);
BYTE getDischgNum(void);
BYTE GetBattExistNum(void);
BYTE GetBatteryLeftCapacity(void);
WORD GetSlaveAvgSOC(void);
FLOAT GetBatteryMaxChargePower(void);
BYTE GetBatteryChangeAndDischgStatus(void);
BYTE GetFmNormalSlave(void);
BOOLEAN GetMasterDischgProtect(void);
#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // SOFTWARE_SRC_APP_SAMPLE_H_;
