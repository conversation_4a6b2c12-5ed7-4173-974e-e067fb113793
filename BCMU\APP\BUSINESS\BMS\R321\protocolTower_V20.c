/**************************************************************************
* Copyright (C) 2001, ZTE Corporation.
* 版权信息：（C）2010-2021，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：ZXDU18 CTL板软件
* 文件名称：protocol_v20.c
* 文件说明：协议模块
* 作    者：hanhui
* 版本信息：V1.0
* 设计日期：2021-10-22
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/

#include "common.h"
#include "sample.h"
#include "hisdata.h"
#include "realAlarm.h"
#include "battery.h"
#include "para.h"
#include "comm.h"
#include "protocol.h"
#include "CommCan.h"
#include "Candrive.h"
#include "led.h"
#include "apptest.h"
#include "flash.h"
#include "fileSys.h"
#include "realAlarm.h"
#include "qtp.h"
#include "protocol_V20.h"
#include "commBdu.h"
#include "HeaterControl.h"
#include "protocolTower_V20.h"
#include <sys/time.h>



/****************************************************************************
* 函数名称：SendParaData_V20
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：获取遥调量信息
* 作    者  ：Chang Qing
* 版本信息：V1.0
* 设计日期：2021-10-23
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
void SendParaDataTower_V20(T_SysPara *ptSysPara)
{
    BYTE i = 0;
    BYTE *p = NULL;
    WORD wSendLen = 326;//163*2
    FLOAT fCellVoltMin = 3.4;
    T_BCMDataStruct    tBCMAnaData;

    rt_memset(ptSysPara, 0, sizeof(T_SysPara));
    rt_memset(&tBCMAnaData, 0, sizeof(T_BCMDataStruct));
    GetSysPara(ptSysPara);
    GetRealData(&tBCMAnaData);

    fCellVoltMin = tBCMAnaData.afCellVolt[0];

#ifdef DEVICE_USING_R321
    for (i=0; i<CELL_VOL_NUM + 1; i++)
	{
		fCellVoltMin = MIN(tBCMAnaData.afCellVolt[i], fCellVoltMin);
	}
#else
    for (i=0; i<CELL_VOL_NUM_TOWER; i++)
	{
		fCellVoltMin = MIN(tBCMAnaData.afCellVolt[i], fCellVoltMin);
	}
#endif

    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    rt_memset(s_tProtocol.aucInvalidFlag, 0x00, sizeof(s_tProtocol.aucInvalidFlag));

    *p++ = PACKLOCATION;                                              //上位机需要获取的pack组位置
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fCellOverVoltAlmThre*1000);        //单体高压告警参数(单体过压告警阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fCellOverVoltAlmRecoThre*1000);    //单体高压恢复参数(单体过压告警恢复值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fCellUnderVoltAlmRecoThre*1000);   //单体低压恢复参数(单体欠压告警恢复值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fCellUnderVoltAlmThre*1000);       //单体低压告警参数(单体欠压告警阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fCellOverVoltPrtThre*1000);        //单体过压保护参数(单体过压保护阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fCellOverVoltPrtRecoThre*1000);    //单体过压恢复参数(单体过压保护恢复值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fCellUnderVoltPrtRecoThre*1000);   //单体欠压恢复参数(单体欠压保护恢复值)
    p += 2;
    *p++ = 64;                                                                     //自定义参数数量，不给参数，默认传送64
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fCellUnderVoltPrtThre*1000);       //单体欠压保护参数(单体欠压保护阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fCellPoorConsisPrtThre*1000);      //单体失效压差参数(单体一致性差保护阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus((ptSysPara->fCellPoorConsisPrtThre-0.2)*1000);//失效恢复压差参数，不给参数，默认传送单体一致性差保护阈值-0.2
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fBattOverVoltAlmThre*100);         //总压高压告警参数(电池组过压告警阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fBattOverVoltAlmRecoThre*100);     //总压高压恢复参数(电池组过压告警恢复值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus((ptSysPara->fBattUnderVoltAlmThre****)*100);  //总压低压恢复参数，不给参数，默认传送电池组欠压告警阈值****
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fBattUnderVoltAlmThre*100);        //总压低压告警参数(电池组欠压告警阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fBattOverVoltPrtThre*100);         //总压过压保护参数(电池组过压保护阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fBattOverVoltPrtRecoThre*100);     //总压过压恢复参数(电池组过压保护恢复值)
    p += 2;
    *(SHORT*)p = Word2Modbus(5100);                                                //总压欠压恢复参数，不给参数，默认传送CELL_VOL_NUM * 3.4
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fBattUnderVoltPrtThre*100);        //总压欠压保护参数(电池组欠压保护阈值)
    p += 2;
    *(SHORT*)p = Word2Modbus(16);                                                  //串联电池节数参数，不给参数，默认传送16
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fChgTempHighAlmThre*10+2731);      //充电高温告警参数(充电高温告警阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fChgTempHighAlmRecoThre*10+2731);  //充电高温恢复参数(充电高温告警恢复阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fChgTempLowAlmRecoThre*10+2731);   //充电低温恢复参数(充电低温告警恢复阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fChgTempLowAlmThre*10+2731);       //充电低温告警参数(充电低温告警阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fChgTempHighPrtThre*10+2731);      //充电过温保护参数(充电高温保护阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fChgTempHighPrtRecoThre*10+2731);  //充电过温恢复参数(充电高温保护恢复阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fChgTempLowPrtRecoThre*10+2731);   //充电欠温恢复参数(充电低温保护恢复阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fChgTempLowPrtThre*10+2731);       //充电欠温保护参数(充电低温保护阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fDischgTempHighAlmThre*10+2731);   //放电高温告警参数(放电高温告警阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fDischgTempHighAlmRecoThre*10+2731);//放电高温恢复参数(放电高温告警恢复阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fDischgTempLowAlmRecoThre*10+2731);//放电低温恢复参数(放电低温告警恢复阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fDischgTempLowAlmThre*10+2731);    //放电低温告警参数(放电低温告警阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fDischgTempHighPrtThre*10+2731);   //放电过温保护参数(放电高温保护阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fDischgTempHighPrtRecoThre*10+2731);//放电过温恢复参数(放电高温保护恢复阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fDischgTempLowPrtRecoThre*10+2731);//放电欠温恢复参数(放电低温保护恢复阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fDischgTempLowPrtThre*10+2731);    //放电欠温保护参数(放电低温保护阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(MIN(ptSysPara->fChgHeaterStartupTemp, ptSysPara->fDischgHeaterStartupTemp)*10+2731);    //电芯加热开启参数，上送加热膜启动温度(充电加热膜启动温度和放电加热膜启动温度两个的最小值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(MAX(ptSysPara->fChgHeaterShutdownTemp, ptSysPara->fDischgHeaterShutdownTemp)*10+2731);  //电芯加热停止参数，上送加热膜停止温度充(充电加热膜关闭温度和放电加热膜关闭温度两个的最大值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fEnvTempHighAlmThre*10+2731);      //环境高温告警参数(环境温度高告警阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fEnvTempLowAlmThre*10+2731);       //环境低温告警参数(环境温度低告警阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fEnvTempHighPrtThre*10+2731);      //环境过温保护参数(环境温度高保护阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fEnvTempHighPrtRecoThre*10+2731);  //环境过温恢复参数(环境温度高保护恢复阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fEnvTempLowPrtRecoThre*10+2731);   //环境欠温恢复参数(环境温度低保护恢复阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fEnvTempLowPrtThre*10+2731);       //环境欠温保护参数(环境温度低保护阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fBoardTempHighPrtThre*10+2731);    //功率过温保护参数(单板过温保护阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fBoardTempHighPrtRecoThre*10+2731);//功率过温恢复参数(单板过温保护恢复阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fChgCurrHighAlmThre*ptSysPara->wBatteryCap*100);           //充电过流告警参数(充电过流告警阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus((ptSysPara->fChgCurrHighAlmThre*ptSysPara->wBatteryCap-2)*100);       //充电过流恢复参数，不给参数，默认传送充电过流告警阈值-2/ptSysPara->wBatteryCap
    p += 2;
    *(SHORT*)p = FloatChangeToModbus((ptSysPara->fDischgCurrHighAlmThre*ptSysPara->wBatteryCap-2)*100);    //放电过流恢复参数，不给参数，默认传送放电过流告警阈值-2/ptSysPara->wBatteryCap
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fDischgCurrHighAlmThre*ptSysPara->wBatteryCap*100);        //放电过流告警参数(放电过流告警阈值)
    p += 2;
    #ifdef DEVICE_USING_R321
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fChgCurrHighPrtThre*ptSysPara->wBatteryCap*100);           //充电过流保护参数(充电过流保护阈值)
    #else
    *(SHORT*)p = FloatChangeToModbus(600);              //充电电过流保护参数(放电过流保护阈值)缺省值0.6
    #endif
    p += 2;
    *(SHORT*)p = Word2Modbus(6);                                                                           //充电过流延时参数，不给参数，默认传送6秒
    p += 2;
    #ifdef DEVICE_USING_R321
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fDischgCurrHighPrtThre*ptSysPara->wBatteryCap*100);        //放电过流保护参数(放电过流保护阈值)
    #else
    *(SHORT*)p = FloatChangeToModbus(1360);          //放电过流保护参数(放电过流保护阈值)缺省值1.36
    #endif
    p += 2;
    *(SHORT*)p = Word2Modbus(60);                                                  //放电过流延时参数，不给参数，默认传送60秒
    p += 2;
    SetInvalidFlag(SECOND_LEVEL_CURR_HIGH_PRT_OFFSET, 2);                          //二级过流保护参数，不给参数，传送十六进制数值20H来填充
    p += 2;
    SetInvalidFlag(SECOND_LEVEL_CURR_HIGH_DELAY_OFFSET, 2);                        //二级过流延时参数，不给参数，传送十六进制数值20H来填充
    p += 2;
    *(SHORT*)p = Word2Modbus(11700);                                               //输出短路保护参数，不给参数，默认传送117A
    p += 2;
    *(SHORT*)p = Word2Modbus(10);                                                  //输出短路延时参数，不给参数，默认传送10秒
    p += 2;
    *(SHORT*)p = Word2Modbus(60);                                                  //过流恢复延时参数，不给参数，默认传送60秒
    p += 2;
    *(SHORT*)p = Word2Modbus(3);                                                   //过流锁定次数参数，不给参数，默认传送3次
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fChargeMaxCurr*ptSysPara->wBatteryCap*100);//充电限流设置参数(充电最大电流)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->wBatteryCap*100);                  //电池额定容量参数(电池容量)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->wBattSOHPrtThre*100); //电池剩余容量参数(电池SOH保护阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->wBattSOCLowPrtThre*100);//荷电状态(SOC)告警参数(上送电池SOC低保护阈值)
    p += 2;
    SetInvalidFlag(CHG_RECO_CAP_OFFSET, 2);                                        //充电恢复容量参数，不给参数，传送十六进制数值20H来填充
    p += 2;
    *(SHORT*)p = Word2Modbus(1440);                                                //待机休眠定时参数，不给参数，默认传送24*60分钟
    p += 2;
    SetInvalidFlag(EQU_HIGH_TEMP_DISABLE_OFFSET, 2);                               //均衡高温禁止参数，不给参数，传送十六进制数值20H来填充
    p += 2;
    SetInvalidFlag(EQU_LOW_TEMP_DISABLE_OFFSET, 2);                                //均衡低温禁止参数，不给参数，传送十六进制数值20H来填充
    p += 2;
    *(SHORT*)p = Word2Modbus(10);                                                  //静态均衡定时参数，不给参数，默认传送10
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(MAX((fCellVoltMin+ptSysPara->fCellEquVoltDiffThre),BANLANCE_CELL_VOLT_START)*1000);//均衡开启电压参数，不给参数，默认传送(单体最小电压+单体均衡启动压差阈值)或BANLANCE_CELL_VOLT_START最大
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(ptSysPara->fCellEquVoltDiffThre*1000);        //均衡开启压差参数(单体均衡启动压差阈值)
    p += 2;
    *(SHORT*)p = FloatChangeToModbus((ptSysPara->fCellEquVoltDiffThre-0.01f)*1000);//均衡结束压差参数，不给参数，默认传送单体均衡启动压差阈值-0.01
    p += 2;
    //电压功能开关参数，给参数，但默认使用告警级别参数
    SetDigitalAlarmBit(0, p, ptSysPara->aucAlarmLevel[11] > 0); //Bit0:单体过压告警功能(单体过压告警)
    SetDigitalAlarmBit(1, p, ptSysPara->aucAlarmLevel[12] > 0); //Bit1:单体过压保护功能(单体过压保护)
    SetDigitalAlarmBit(2, p, ptSysPara->aucAlarmLevel[13] > 0); //Bit2:单体欠压告警功能(单体欠压告警)
    SetDigitalAlarmBit(3, p, ptSysPara->aucAlarmLevel[14] > 0); //Bit3:单体欠压保护功能(单体欠压保护)
    SetDigitalAlarmBit(4, p, ptSysPara->aucAlarmLevel[6]  > 0); //Bit4:总压过压告警功能(电池组过压告警)
    SetDigitalAlarmBit(5, p, ptSysPara->aucAlarmLevel[2]  > 0); //Bit5:总压过压保护功能(电池组过压保护)
    SetDigitalAlarmBit(6, p, ptSysPara->aucAlarmLevel[9]  > 0); //Bit6:总压欠压告警功能(电池组欠压告警)
    SetDigitalAlarmBit(7, p, ptSysPara->aucAlarmLevel[10]  > 0); //Bit7:总压欠压保护功能(电池组欠压保护)
    p += 1;

    //温度功能开关参数，给参数，但默认使用告警级别参数
    SetDigitalAlarmBit(0, p, ptSysPara->aucAlarmLevel[7]  > 0);    //Bit8:环境高温告警功能(环境温度高告警)
    SetDigitalAlarmBit(1, p, ptSysPara->aucAlarmLevel[51] > 0);     //Bit9:环境高温保护功能(环境温度高保护)
    SetDigitalAlarmBit(2, p, ptSysPara->aucAlarmLevel[8] > 0);     //Bit10:环境低温告警功能(环境温度低告警)
    SetDigitalAlarmBit(3, p, ptSysPara->aucAlarmLevel[52] > 0);     //Bit11:环境低温保护功能(环境温度低保护)
    SetDigitalAlarmBit(4, p, ptSysPara->aucAlarmLevel[3]  > 0);    //Bit12:功率高温保护功能(单板过温保护)
    SetDigitalAlarmBit(5, p, ptSysPara->bHeatingPadEnable > 0);     //Bit13:电芯加热功能
    p += 1;
    SetDigitalAlarmBit(0, p, ptSysPara->aucAlarmLevel[15] > 0);     //Bit0:充电高温告警功能(单体充电高温告警)
    SetDigitalAlarmBit(1, p, ptSysPara->aucAlarmLevel[16] > 0);     //Bit1:充电高温保护功能(单体充电高温保护)
    SetDigitalAlarmBit(2, p, ptSysPara->aucAlarmLevel[19] > 0);     //Bit2:充电低温告警功能(单体充电低温告警)
    SetDigitalAlarmBit(3, p, ptSysPara->aucAlarmLevel[20] > 0);     //Bit3:充电低温保护功能(单体充电低温保护)
    SetDigitalAlarmBit(4, p, ptSysPara->aucAlarmLevel[17] > 0);     //Bit4:放电高温告警功能(单体放电高温告警)
    SetDigitalAlarmBit(5, p, ptSysPara->aucAlarmLevel[18] > 0);     //Bit5:放电高温保护功能(单体放电高温保护)
    SetDigitalAlarmBit(6, p, ptSysPara->aucAlarmLevel[21] > 0);     //Bit6:放电低温告警功能(单体放电低温告警)
    SetDigitalAlarmBit(7, p, ptSysPara->aucAlarmLevel[22] > 0);     //Bit7:放电低温保护功能(单体放电低温保护)
    p += 1;

    //电流功能开关参数，给参数，但默认使用告警级别参数
    SetDigitalAlarmBit(0, p, ptSysPara->aucAlarmLevel[4]  > 0);     //Bit0:充电电流告警功能(充电过流告警)
    SetDigitalAlarmBit(1, p, ptSysPara->aucAlarmLevel[0] > 0);      //Bit1:充电过流保护功能(充电过流保护)
    SetDigitalAlarmBit(2, p, ptSysPara->aucAlarmLevel[5] > 0);      //Bit2:放电电流告警功能(放电过流告警)
    SetDigitalAlarmBit(3, p, ptSysPara->aucAlarmLevel[1] > 0);      //Bit3:放电过流保护功能(放电过流保护)
    SetDigitalAlarmBit(5, p, ptSysPara->aucAlarmLevel[36]  > 0);     //Bit5:输出短路保护功能(电池短路)
    //Bit4:二级过流保护功能 Bit6:二级过流锁定功能 Bit7:输出短路锁定功能
    p += 1;

    //容量及其它功能开关参数，给参数，但默认使用告警级别参数
    *p = ((BYTE)((ptSysPara->aucAlarmLevel[26] > 0) ? 1 : 0) | 0x38) & 0xff;        //Bit0:荷电状态（SOC）告警功能 Bit3:静态待机休眠功能 Bit4:历史数据记录功能 Bit5:限流模式（1 为主动限流，0 为被动限流）
    //Bit1:间歇式的充电功能 Bit2:外部开关控制功能 Bit6:保留 Bit7:保留  备注：从数据字典的偏移来看，14代表的是荷电状态(SOC)
    p += 1;

    //均衡功能开关参数，不给参数，传送十六进制数值20H来填充
    *p = 0x07;                                                                      //Bit0:电池均衡功能 Bit1:静态均衡功能 Bit2:静态均衡定时功能
    //Bit3:均衡温度限制功能 Bit4:电芯失效告警功能 Bit5:电池组失效告警功能 Bit6:电流传感器失效告警功能 Bit7:保留
    p += 1;

    //指示功能开关参数，给参数，但默认使用蜂鸣器使能参数
    *p = ((BYTE)((ptSysPara->bBuzzerEnable > 0) ? 1 : 0));                          //Bit0:蜂鸣器声指示
    //Bit1:LCD 显示功能 Bit2:保留 Bit3:保留 Bit4:保留 Bit5:保留 Bit6:保留 Bit7:保留
    p += 1;

    MemsetBuff(p, PRTBOARDTYPE, 10, 10, 0x20);                            //其它参数（保护板型号）
    p += 10;
    *(WORD*)p = Word2Modbus(CUSTOMIZESWITCHNUM);                        //自定义开关数量参数
    p += 2;

    /* LENID */
    s_tProtocol.wSendLenid = wSendLen;
    return;
}


void SetBmsSwitchPara(WORD wPara,T_SysPara *ptSysPara)
{
    switch (s_tProtocol.ucCommandType)
    {
        case 0x4A:                                                       //电压功能开关参数，给参数，但默认使用告警级别参数
            ptSysPara->aucAlarmLevel[11] = (BYTE)wPara & 0x01;                 //Bit0:单体过压告警功能(单体过压告警)
            ptSysPara->aucAlarmLevel[12] = ((BYTE)wPara & 0x02) >> 1;          //Bit1:单体过压保护功能(单体过压保护)
            ptSysPara->aucAlarmLevel[13] = ((BYTE)wPara & 0x04) >> 2;          //Bit2:单体欠压告警功能(单体欠压告警)
            ptSysPara->aucAlarmLevel[14] = ((BYTE)wPara & 0x08) >> 3;          //Bit3:单体欠压保护功能(单体欠压保护)
            ptSysPara->aucAlarmLevel[6]  = ((BYTE)wPara & 0x10) >> 4;          //Bit4:总压过压告警功能(电池组过压告警)
            ptSysPara->aucAlarmLevel[2]  = ((BYTE)wPara & 0x20) >> 5;          //Bit5:总压过压保护功能(电池组过压保护)
            ptSysPara->aucAlarmLevel[9]  = ((BYTE)wPara & 0x40) >> 6;          //Bit6:总压欠压告警功能(电池组欠压告警)
            ptSysPara->aucAlarmLevel[10] = ((BYTE)wPara & 0x80) >> 7;          //Bit7:总压欠压保护功能(电池组欠压保护)
            break;
        case 0x4B:                                                       //温度功能开关参数，给参数，但默认使用告警级别参数
            ptSysPara->aucAlarmLevel[15] = (WORD)wPara & 0x01;                 //Bit0:充电高温告警功能(单体充电高温告警)
            ptSysPara->aucAlarmLevel[16] = ((WORD)wPara & 0x02) >> 1;          //Bit1:充电高温保护功能(单体充电高温保护)
            ptSysPara->aucAlarmLevel[19] = ((WORD)wPara & 0x04) >> 2;          //Bit2:充电低温告警功能(单体充电低温告警)
            ptSysPara->aucAlarmLevel[20] = ((WORD)wPara & 0x08) >> 3;          //Bit3:充电低温保护功能(单体充电低温保护)
            ptSysPara->aucAlarmLevel[17] = ((WORD)wPara & 0x10) >> 4;          //Bit4:放电高温告警功能(单体放电高温告警)
            ptSysPara->aucAlarmLevel[18] = ((WORD)wPara & 0x20) >> 5;          //Bit5:放电高温保护功能(单体放电高温保护)
            ptSysPara->aucAlarmLevel[21] = ((WORD)wPara & 0x40) >> 6;          //Bit6:放电低温告警功能(单体放电低温告警)
            ptSysPara->aucAlarmLevel[22] = ((WORD)wPara & 0x80) >> 7;          //Bit7:放电低温保护功能(单体放电低温保护)
            ptSysPara->aucAlarmLevel[7]  = ((WORD)wPara >> 8) & 0x01;          //Bit8:环境高温告警功能(环境温度高告警)
            ptSysPara->aucAlarmLevel[51] = (((WORD)wPara >> 8) & 0x02) >> 1;   //Bit9:环境高温保护功能(环境温度高保护)
            ptSysPara->aucAlarmLevel[8]  = (((WORD)wPara >> 8) & 0x04) >> 2;   //Bit10:环境低温告警功能(环境温度低告警)
            ptSysPara->aucAlarmLevel[52] = (((WORD)wPara >> 8) & 0x08) >> 3;   //Bit11:环境低温保护功能(环境温度低保护)
            ptSysPara->aucAlarmLevel[3]  = (((WORD)wPara >> 8) & 0x10) >> 4;   //Bit12:功率高温保护功能(单板过温保护)
            ptSysPara->bHeatingPadEnable = (((WORD)wPara >> 8) & 0x20) >> 5;   //Bit13:电芯低温加热功能(加热垫使能)
            //Bit14:保留 Bit15:保留
            break;
        case 0x4C:                                                       //电流功能开关参数，给参数，但默认使用告警级别参数
            ptSysPara->aucAlarmLevel[4]  = (BYTE)wPara & 0x01;                 //Bit0:充电电流告警功能(充电过流告警)
            ptSysPara->aucAlarmLevel[0]  = ((BYTE)wPara & 0x02) >> 1;          //Bit1:充电过流保护功能(充电过流保护)
            ptSysPara->aucAlarmLevel[5]  = ((BYTE)wPara & 0x04) >> 2;          //Bit2:放电电流告警功能(放电过流告警)
            ptSysPara->aucAlarmLevel[1]  = ((BYTE)wPara & 0x08) >> 3;          //Bit3:放电过流保护功能(放电过流保护)
            //Bit4:二级过流保护功能()
            ptSysPara->aucAlarmLevel[36] = ((BYTE)wPara & 0x20) >> 5;          //Bit5:输出短路保护功能(电池短路)
            //Bit6:二级过流锁定功能()
            //Bit7:输出短路锁定功能()
            break;
        case 0x4D:                                                       //容量及其它功能开关参数，给参数，但默认使用告警级别参数
            ptSysPara->aucAlarmLevel[26] = (BYTE)wPara & 0x01;                 //Bit0:荷电状态（SOC）告警功能
            //Bit1:间歇式的充电功能
            //Bit2:外部开关控制功能
            //Bit3:静态待机休眠功能
            //Bit4:历史数据记录功能
            //Bit5:限流模式（1 为主动限流，0 为被动限流）
            //Bit6:保留 Bit7:保留
            break;
        case 0x4F:                                                       //指示功能开关参数，给参数，但默认使用蜂鸣器使能参数
            ptSysPara->bBuzzerEnable = (BYTE)wPara & 0x01;                     //Bit0:蜂鸣器声指示
            //Bit1:LCD 显示功能
            //Bit2:保留
            //Bit3:保留
            //Bit4:保留
            //Bit5:保留
            //Bit6:保留
            //Bit7:保留
            break;
        case 0x50:
            ParaStrCpy((BYTE *)PRTBOARDTYPE, (s_tProtocol.aucRecBuf+8), sizeof(PRTBOARDTYPE)); //其它参数（保护板型号）
            break;
        case 0x51:
            // CUSTOMIZESWITCHNUM = wPara;                     //自定义开关数量参数
            break;
    }
}

