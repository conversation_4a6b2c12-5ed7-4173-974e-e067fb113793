#ifndef _AIRSWITCH_LED_H
#define _AIRSWITCH_LED_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

/* 控制状态 */

#define LED_OFF_MODE      0       ///< 常灭
#define LED_ON_MODE       1       ///< 常亮
#define LED_BLINK1HZ_MODE 2       ///< 1Hz闪烁
#define LED_BLINK4HZ_MODE 3       ///< 4Hz闪烁


#define T_LED_STATUS_LEN        (sizeof(airswitch_led_status_t))

// 定义告警的枚举类型
typedef enum {
    LED_NORMAL,  ///< 无告警
    LED_FAULT, ///< 有告警
} airswitch_Alarm_e;

// 定义授权状态的枚举类型
typedef enum {
    LED_UNAUTHORIZED, // 未授权
    LED_AUTHORIZED, // 已授权
} airswitch_Empower_e;

// 定义闸状态的枚举类型
typedef enum {
    LED_OPENING, // 分闸
    LED_CLOSING, // 合闸
} airswitch_Brake_e;

// 定义特殊状态的枚举类型
typedef enum {
    NORMAL_MODE, // 正常显示
    ONEKEY_MODE, // 一键指示
    BUSINT_MODE, // 通讯断开
    GUIDE_MODE, // 在引导程序中
    OTAUPGRADE_MODE, // OTA升级
} airswitch_special_e;

// 定义灯的枚举类型
typedef enum {
    NULL_LED, // 灭
    YELLOW, // 黄灯
    RED, // 红灯
    GREEN, // 绿灯
    BLUE, // 蓝灯
    WHITE, // 白灯
} airswitch_light_e;

// 定义闪烁状态的枚举类型
typedef enum {
    CONSTANTLY_ON, // 常亮
    FAST_4HZ_FLASH, // 4Hz快闪
    SLOW_1HZ_FLASH, // 1Hz慢闪
    CONSTANTLY_OFF, // 常灭
    NULL_BLINK, // 空状态
} airswitch_blinking_e;

typedef struct {
    airswitch_special_e special;
    airswitch_Empower_e empower;
    airswitch_Brake_e brake;
    airswitch_light_e light;
    airswitch_blinking_e blinking;
} airswitch_led_Onekey_status_t;

typedef struct {
    airswitch_special_e special;
    airswitch_Alarm_e alarm;
    airswitch_Empower_e empower;
    airswitch_Brake_e brake;
    airswitch_light_e light;
    airswitch_blinking_e blinking;
} airswitch_led_status_t;

typedef struct {
    airswitch_special_e special;
    airswitch_light_e light;
    airswitch_blinking_e blinking;
} airswitch_led_special_status_t;


void* init_led_blink(void *param);
void led_thread_entry(void* parameter);
char set_led_color(unsigned char led_status);


#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _604A_LED_H
