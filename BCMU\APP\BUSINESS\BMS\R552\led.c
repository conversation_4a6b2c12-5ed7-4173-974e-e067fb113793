#include "utils_heart_beat.h"
#include "thread_id.h"
#include "led_R552.h"
#include "interface.h"
#include "io_ctrl_api.h"
#include "msg_id.h"
#include "data_type.h"
#include "alarm_id_in.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "alarm_mgr_api.h"

static const rt_uint32_t s_alLedPin[] = {LED_RUN_PIN, LED_ALM_PIN, LED_SOC4_PIN, LED_SOC3_PIN, LED_SOC2_PIN, LED_SOC1_PIN};
static T_ShowConfig s_currentShow;
static unsigned char s_bShowFlag = 0;

/*****************************静态函数声明***************************************/

static T_ShowConfig s_atShowConfig[] =
{
    {
      .ucShowType =  BMS_SHUTDOWN,
      .aucLedMode   =  {LEDOFF, LEDOFF, LEDOFF, LEDOFF, LEDOFF, LEDOFF},
      .ucBuzzMode = BUZZOFF,
    },
    {
      .ucShowType =  BMS_SLEEP,
      .aucLedMode   =  {LEDSLOWSH2, LEDOFF, LEDOFF, LEDOFF, LEDOFF, LEDOFF},
      .ucBuzzMode = BUZZOFF,
    },
    {
      .ucShowType =  BMS_STANDBY,
      .aucLedMode   =  {LEDSLOWSH, LEDBYALM, LEDBYSOC, LEDBYSOC, LEDBYSOC, LEDBYSOC},
      .ucBuzzMode = BUZZBYALM,
    },
    {
      .ucShowType =  BMS_CHARGE,
      .aucLedMode   =  {LEDON, LEDBYALM, LEDBYSOC, LEDBYSOC, LEDBYSOC, LEDBYSOC},
      .ucBuzzMode = BUZZBYALM,
    },
    {
      .ucShowType =  BMS_DISCH,
      .aucLedMode   =  {LEDQUICKSH, LEDBYALM, LEDBYSOC, LEDBYSOC, LEDBYSOC, LEDBYSOC},
      .ucBuzzMode = BUZZBYALM,
    },
    {
      .ucShowType =  BMS_ADDR,
      .aucLedMode   =  {LEDON, LEDOFF, LEDBYADDR, LEDBYADDR, LEDBYADDR, LEDBYADDR},
      .ucBuzzMode = BUZZOFF,
    },
};


/**
 * @brief 初始化LED输出引脚
 * 
 * 该函数初始化所有LED输出引脚，设置它们的初始状态为低电平。
 * 它使用预定义的LED引脚数组 `s_alLedPin` 来注册每个LED的IO信息。
 */
void InitLedOut(void)
{
    // 创建一个LED信息结构体，并初始化其字段
    io_item_info_t ledInfo = {
        .eIoType = OUTPUT_LED,      // 设置IO类型为LED输出
        .initialVal = PIN_LOW,      // 设置初始值为低电平
        .msg_id = LED_CTRL_MSG      // 设置消息ID
    };

    // 遍历所有LED引脚
    for (unsigned char i = 0; i < LED_NUM; i++)
    {
        ledInfo.pin_no = s_alLedPin[i];  // 更新当前LED引脚号
        register_io_item_info(&ledInfo); // 注册当前LED的IO信息
    }
}



static unsigned char getLedModeBySOC(unsigned char ucIndex, unsigned short wSOC)
{
    // 计算SOC类型
    unsigned char ucSocType = (wSOC + 2499) / 2500; // 使用整数除法和舍入

    // 特殊处理SOC为10000的情况
    if (wSOC == 10000)
    {
        ucSocType += 1;
    }

    // 根据ucIndex和ucSocType确定LED模式
    if (ucIndex >= LED_NUM - ucSocType)
    {
        return (ucIndex == LED_NUM - ucSocType) ? LEDSLOWSH : LEDON;
    }
    else
    {
        return LEDOFF;
    }
}


static unsigned char getLedModeByAlm(void)
{
/*    switch (JudgeAlarmType())
    {
        case ALM_NORMAL:
            return LEDOFF;
        case ALM_SYS_LOCK:
            return LEDSLOWSH;
        case ALM_ABNORMAL:
            return LEDON;
    }*/
    return LEDON;
}


static unsigned char getBuzzModeByAlm(void)
{
    // 检查电池丢失报警
    if (get_realtime_alarm_value(ALARM_ID_BATT_LOSE_ALM))
    {
        return BUZZMODE2;
    }
    // 检查是否有其他报警
    if (get_realtime_alarm_count() > 0)
    {
        return BUZZMODE1;
    }

    // 如果没有报警，关闭蜂鸣器
    return BUZZOFF;
}



static void sendLedMsg(unsigned char ucIndex, unsigned char ucLedMode)
{
    // 如果LED模式没有变化，直接返回
    if (s_currentShow.aucLedMode[ucIndex] == ucLedMode)
    {
        return;
    }

    // 更新当前显示模式
    s_currentShow.aucLedMode[ucIndex] = ucLedMode;

    // 初始化LED控制消息
    led_ctrl_msg msg = {
        .pin_no = s_alLedPin[ucIndex],
        .lighton_time = 0,
        .lightoff_time = 100  // 默认关闭状态
    };

    // 根据LED模式设置具体的时间参数
    switch (ucLedMode)
    {
        case LEDON:
            msg.lighton_time = 100;
            msg.lightoff_time = 0;
            break;
        case LEDOFF:
            // 已经是默认值，不需要修改
            break;
        case LEDSLOWSH:
            msg.lighton_time = 100;
            msg.lightoff_time = 100;
            break;
        case LEDSLOWSH2:
            msg.lighton_time = 25;
            msg.lightoff_time = 375;
            break;
        case LEDQUICKSH:
            msg.lighton_time = 25;
            msg.lightoff_time = 25;
            break;
        default:
            // 默认值已经设置，不需要额外处理
            break;
    }

    // 发送消息到线程
    pub_msg_to_thread(LED_CTRL_MSG, &msg, sizeof(msg));
}



static void ctrlShow(T_ShowConfig *pShowCfg)
{
    if (pShowCfg == NULL) {
        return; // 防止空指针访问
    }

    unsigned char ucAddr = 1; 
    unsigned short wSoc = 0;
    get_one_data(BMS_DATA_ID_CAN1_ADDRESS, &ucAddr);
    get_one_data(BMS_DATA_ID_BATT_SOC, &wSoc);

    for (unsigned char i = 0; i < LED_NUM; ++i)
    {
        unsigned char ucMode;

        switch (pShowCfg->aucLedMode[i])
        {
            case LEDBYALM:
                ucMode = getLedModeByAlm();
                break;
            case LEDBYSOC:
                ucMode = getLedModeBySOC(i, wSoc);
                break;
            case LEDBYADDR:
                ucMode = (ucAddr & (1 << (LED_NUM - 1 - i))) ? LEDSLOWSH : LEDOFF;
                break;
            default:
                ucMode = pShowCfg->aucLedMode[i];
        }

        sendLedMsg(i, ucMode);
    }

    unsigned char ucBuzzMode = (pShowCfg->ucBuzzMode == BUZZBYALM) ? getBuzzModeByAlm() : pShowCfg->ucBuzzMode;

    s_currentShow.ucBuzzMode = ucBuzzMode;
    s_currentShow.ucShowType = pShowCfg->ucShowType;
}



void SetLedShow(unsigned char ucShowType)
{
    // 如果显示类型超出范围，直接返回
    if (ucShowType > BMS_ADDR)
    {
        return;
    }

    // 更新当前显示类型
    s_currentShow.ucShowType = ucShowType;

    // 如果特殊显示标志已设置，直接返回
    if (s_bShowFlag)
    {
        return;
    }

    // 控制显示配置
    ctrlShow(&s_atShowConfig[ucShowType]);
}



void SetLedByAddr()
{
    s_bShowFlag = 0;  // 关闭特殊显示标志
    SetLedShow(BMS_ADDR); // 设置LED显示地址
}



void SetShowSpecial(T_ShowConfig *pSpecialMode)
{
    // 如果传入的特殊模式配置为空，则恢复默认显示并关闭特殊显示标志
    if (pSpecialMode == NULL)
    {
        SetLedShow(s_currentShow.ucShowType);
        s_bShowFlag = 0;
        return;
    }

    // 遍历所有LED灯，并根据特殊模式配置发送消息
    for (unsigned char i = 0; i < LED_NUM; ++i)
    {
        sendLedMsg(i, pSpecialMode->aucLedMode[i]);
    }

    // 设置特殊显示标志为开启状态
    s_bShowFlag = 1;
}

unsigned char GetBuzzMode()
{
    return s_currentShow.ucBuzzMode;
}

/***缺少的接口函数，暂时放在这里*****/
unsigned char GetDefenceStatus(void)
{
    return 0;
}

unsigned char GetApptestFlag(void)
{
    return 0;
}

unsigned char GetQtptestFlag(void)
{
    return 0;
}

unsigned char JudgeAlarmType(void)
{
    return 0;
}
/***************/