#ifndef _DEV_NORTH_DEMUB_MODBUS_H
#define _DEV_NORTH_DEMUB_MODBUS_H

#ifdef __cplusplus
extern "C"
{
#endif /*__cplusplus */

#include "device_type.h"
#include "cmd.h"
#include "linklayer.h"
#include "protocol_layer.h"


#define NORTH_PRECISION_ZERO 0
#define NORTH_PRECISION_ONE 1
#define NORTH_PRECISION_TWO 2
#define NORTH_PRECISION_THREE 3

#define R_DATA_LEN_NO_USE 0
#define S_DATA_LEN_NO_USE 0

#define DEMUB_NAME_STRING "DEMUB"
#define DEMUB_VER_STRING "V1.00.01.00"
#define DEMUB_DATE_STRING "2024-12-17"

#define DATA_LEN_2          2//数据长度为2
#define DATA_LEN_4          4 //数据长度为4
#define DATA_LEN_6          6 //数据长度为6

#define GET_ANA_DATA                0x04            //<获取模拟量、状态量、版本信息
#define GET_PARA_DATA               0x03            //<获取参数量
#define SET_PARA_DATA               0x10            //<设置参数量
#define SET_SINGLE_PARA_DATA        0x06            //<设置参数量

typedef enum {
    TYPE_ALAM = 0,
    TYPE_PARA,
    TYPE_ANA,
    TYPE_ALAM_PARA
}sid_type_e;


typedef struct {
    unsigned short register_addr; // 寄存器地址
    unsigned char reserve_flag;   // 该寄存器值是否保留，1表示保留
    char old_type;                // 原始数据类型
    char type;                    // Modbus传输数据类型
    unsigned char precision;      // Modbus精度
    unsigned char data_len;       // Modbus上送数据长度
    sid_type_e sid_type;          // SID的类型
    unsigned int data_addr;       // 数据取值地址
} modbus_addr_map_data_t;



typedef struct {
    char src_type; // 源数据类型
    char dst_type; // 目标数据类型
    int (*pack_fun)(int* index, unsigned char* cmd_buff, int* reg_num, int data_len, int precision, int total_len, modbus_addr_map_data_t* data_map);
} pack_fun_map_t;

typedef struct {
    char src_type; // 源数据类型
    char dst_type; // 目标数据类型
    int (*parse_func)(int* index, unsigned char* cmd_buff, int* data_value_index, int precision, int* reg_nums, modbus_addr_map_data_t* data_map);
} parse_fun_map_t;


#define DEMUB_SOFTWARE_NAME_LEN 20
#define DEMUB_SOFTWARE_VER_LEN 20
#define DEMUB_SOFTWARE_DATE_LEN 20
#define NUM_HARMONIC 30
#define PHASE_NUM 3

typedef struct
{
    char acSwName[DEMUB_SOFTWARE_NAME_LEN];
    char acSwVer[DEMUB_SOFTWARE_VER_LEN];
    char acSwDate[DEMUB_SOFTWARE_DATE_LEN];
} T_DEMUBFactInfoStruct;

typedef struct
{
    short sAC1PhaseVolt[PHASE_NUM];           // 相电压
    unsigned int uiAC1PhaseCurr[PHASE_NUM];   // 相电流
    short sAC1LineVolt[PHASE_NUM];            // 线电压
    unsigned short usAC1V_V_Angle[PHASE_NUM]; // 电压角度差
    unsigned short usAC1I_I_Angle[PHASE_NUM]; // 电流角度差
    unsigned short usAC1V_I_Angle[PHASE_NUM]; // 电压电流角度差
    unsigned short usAC1Frequen;              // 频率
    unsigned short usAC1VZero;                // 零地电压
    unsigned short usAC1Yv;                   // 三相电压不平衡度

    short sAC2PhaseVolt[PHASE_NUM];           // 相电压
    unsigned int uiAC2PhaseCurr[PHASE_NUM];   // 相电流
    short sAC2LineVolt[PHASE_NUM];            // 线电压
    unsigned short usAC2V_V_Angle[PHASE_NUM]; // 电压角度差
    unsigned short usAC2I_I_Angle[PHASE_NUM]; // 电流角度差
    unsigned short usAC2V_I_Angle[PHASE_NUM]; // 电压电流角度差
    unsigned short usAC2Frequen;              // 频率
    unsigned short usAC2VZero;                // 零地电压
    unsigned short usAC2Yv;                   // 三相电压不平衡度

    unsigned int uiIn;                      // 零序电流
    unsigned short usYi;                    // 电流不平衡度
    int iActivePower[PHASE_NUM];            // 有功功率
    int usSysActivePower;                 // 系统有功功率
    int iReactivePower[PHASE_NUM];          // 无功功率
    int iSysReactivePower;                  // 系统无功功率
    short sApparentPower[PHASE_NUM];        // 视在功率
    short sSysApparentPower;                // 系统视在功率
    short sPowerFactor[PHASE_NUM];          // 功率因数
    short sSysPowerFactor;                  // 系统功率因数
    unsigned int uiActiveEnergy[PHASE_NUM]; // 有功电能
    unsigned int uiSysActiveEnergy;
    unsigned int uiPosReactiveEnergy[PHASE_NUM];   // 正向无功电能
    unsigned int uiSysPosReactiveEnergy;           // 系统正向无功电能
    unsigned int uiNegReactiveEnergy[PHASE_NUM];   // 负向无功电能
    unsigned int uiSysNegReactiveEnergy;           // 系统负向无功电能
    unsigned int uiTotalReactiveEnergy[PHASE_NUM]; // 总无功电能
    unsigned int uiSysTotalReactiveEnergy;         // 系统总无功电能
    unsigned int uiPhaseCurrDemand[PHASE_NUM];     // 相电流需量
    unsigned int uiPhaseCurrMaxDemand[PHASE_NUM];  // 相电流最大需量
    int iActivePowerDemand[PHASE_NUM];             // 有功功率需量
    int iSysActivePowerDemand;                     // 系统有功功率需量
    int iActivePowerMaxDemand[PHASE_NUM];          // 有功功率最大需量
    int iSysActivePowerMaxDemand;                  // 系统有功功率最大需量

    unsigned short usPhaseCurrTotalTHD[PHASE_NUM]; // 相电流总谐波畸变率
    unsigned short usPhaseCurrAverageTHD;          // 相电流平均总谐波畸变率
    unsigned short usPhaseVoltTotalTHD[PHASE_NUM]; // 相电压总谐波畸变率
    unsigned short usPhaseVoltAverageTHD;          // 相电压平均总谐波畸变率
    unsigned short usVoltHarmonics1[NUM_HARMONIC]; // 相电压谐波含有率
    unsigned short usCurrHarmonics1[NUM_HARMONIC]; // 相电流谐波含有率
    unsigned short usVoltHarmonics2[NUM_HARMONIC];
    unsigned short usCurrHarmonics2[NUM_HARMONIC];
    unsigned short usVoltHarmonics3[NUM_HARMONIC];
    unsigned short usCurrHarmonics3[NUM_HARMONIC];
} T_DEMUBAnalogDataStruct;

typedef struct
{
    unsigned short usI_Change;
    unsigned short usMTSDetType;
    unsigned short usInputMccbCfg;
    short sAC1CurrZero[PHASE_NUM];
    short sAC1CurrSlope[PHASE_NUM];
    short sAC1VoltZero[PHASE_NUM];
    short sAC1VoltSlope[PHASE_NUM];
    short sAC2CurrZero[PHASE_NUM];
    short sAC2CurrSlope[PHASE_NUM];
    short sAC2VoltZero[PHASE_NUM];
    short sAC2VoltSlope[PHASE_NUM];
} T_DEMUBParaDataStruct;


typedef struct
{
    unsigned short usParaCrc1;
    unsigned short usParaCrc2;
    unsigned int uiDI1_24; // 24个数字量
    T_DEMUBAnalogDataStruct s_demubAnalogSheet;
    T_DEMUBFactInfoStruct tFacInfo;
    T_DEMUBParaDataStruct s_demubParaSheet;
    unsigned short usDO1;
    unsigned short usDO2;
} T_SheetDEMUBDataStruct;

dev_type_t *init_dev_north_demub(void);
void update_demub_fact_info(void);

int floattoint32u(int* index , unsigned char* data_buff , int* reg_nums , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map  );
int floattoint32s(int* index , unsigned char* data_buff, int* reg_nums , int data_len ,  int percision, int total_len, modbus_addr_map_data_t* data_map  );
int floattoint16u(int* index , unsigned char* data_buff , int* reg_nums , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map );
int floattoint16s(int* index , unsigned char* data_buff , int* reg_nums , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map );
int int16utoint16u(int* index , unsigned char* data_buff , int* reg_nums , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map );
int int16stoint16s(int* index , unsigned char* data_buff , int* reg_nums , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map );
int int32utoint32u(int* index , unsigned char* data_buff , int* reg_nums, int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map );
int stringtostring(int* index , unsigned char* data_buff , int* reg_nums , int data_len,  int percision  , int total_len, modbus_addr_map_data_t* data_map );
int datetoint16u(int* index, unsigned char* data_buff, int* reg_nums, int data_len, int percision, int total_len, modbus_addr_map_data_t* data_map);

int parse_int16utofloat( int* index , unsigned char* data_buff , int* data_valude_index ,int percision ,int* reg_nums, modbus_addr_map_data_t* data_map);
int parse_int16stofloat( int* index , unsigned char* data_buff , int* data_valude_index , int percision ,int* reg_nums, modbus_addr_map_data_t* data_map);
int parse_int32utofloat( int* index , unsigned char* data_buff , int* data_valude_index ,  int percision ,int* reg_nums, modbus_addr_map_data_t* data_map);
int parse_int32stofloat( int* index , unsigned char* data_buff , int* data_valude_index , int percision ,int* reg_nums, modbus_addr_map_data_t* data_map);
int parse_int16utoint16u( int* index , unsigned char* data_buff , int* data_valude_index , int percision ,int* reg_nums, modbus_addr_map_data_t* data_map);
int parse_int32utoint32u( int* index , unsigned char* data_buff , int* data_valude_index ,int percision , int* reg_nums, modbus_addr_map_data_t* data_map);
int parse_stringtostring( int* index , unsigned char* data_buff , int* data_valude_index , int percision , int* reg_nums, modbus_addr_map_data_t* data_map);

int new_pack_ana_para_data(void* dev_inst, void* cmd_buff);
int new_parse_para_data_from_buff(void* dev_inst, void* cmd_buff);
int parse_start_addr_and_reg_nums(void* dev_inst, void* cmd_buff);
int pack_set_para_data(void* dev_inst, void* cmd_buff);
char set_data_by_id_type(int index , void* data, modbus_addr_map_data_t* data_map);
int int32stoint32s(int* index , unsigned char* data_buff , int* reg_nums, int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map );
int parse_int32stoint32s(int* index, unsigned char* data_buff, int* data_valude_index, int percision, int* reg_nums, modbus_addr_map_data_t* data_map);
void get_data_by_id_type(int index , void* data, modbus_addr_map_data_t* data_map);
int find_register_start_addr_index(int reg_addr);

#ifdef __cplusplus
}
#endif //  __cplusplus

#endif //
