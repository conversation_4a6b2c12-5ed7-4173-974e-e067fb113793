/**************************************************************************
 * 版权信息：（C）2008-2009，中兴通讯股份有限公司电源开发部版权所有
 * 系统名称：ZXDU88 S402 DCMU CORE板软件
 * 文件名称：graphic.h
 * 文件说明：液晶显示模块头文件
 * 作    者：熊勇
 * 版本信息：V5.0
 * 设计日期：2008-08-01
 * 修改记录：
 * 日    期		版	本		修改人		修改摘要
 * 其他说明：
 ***************************************************************************/

#ifndef GRAPHIC_H
#define GRAPHIC_H

#include <rtthread.h>

#define XZK_ADDR_HI ((volatile rt_uint8_t *)0xF720)
#define XZK_ADDR_CS ((volatile rt_uint8_t *)0xF000)

#define LCD_BASE_LEFT ADDR_CS1  // LCD左半屏地址
#define LCD_BASE_RIGHT ADDR_CS2 // LCD右半屏地址

#define XZK_ROM_ASC8 0          // size=1k
#define XZK_ROM_ASC16 0x00400   // size=2k
#define XZK_ROM_ICON 0x00C00    // size=5k
#define XZK_ROM_BMP 0x02000     // size=56k  --- 1*64KB
#define XZK_ROM_CHINESE 0x10000 // size=32k
#define XZK_ROM_ENGLISH 0x18000 // size=32k  --- 2*64KB
#define XZK_ROM_HZK14 0x40000   // size=256k --- 5~8*64kb

#define WIDTH_ASC 7
#define WIDTH_HZ 14
#define LCD_LEFT 0
#define LCD_RIGHT 1
#define PORT_ORDER 0
#define PORT_DATA 1
#define MAX_LCD_WAIT 20
#define IMAGE_SIZE 1024
#define MIRROR_SIZE (2 * IMAGE_SIZE)

#define DISPLAY_ON 0x3f        // 显示开
#define DISPLAY_OFF 0x3e       // 显示关
#define DISPLAY_LINE0 0xc0     // 起始行位置第0行
#define DISPLAY_FIRSTPAGE 0xB8 // 第0页
#define DISPLAY_FIRSTLINES 0x40

#define PAGES 0x08 // 共8页
#define LINES 0x40 // 每片SED1520控制61列

#define LCDSTARTPAGES 2
#define LCDENDPAGES 5
#define LCDSTARTLINES 3
#define LCDENDLINES 0x3c
#define MAX_X 128
#define MAX_Y 64

#define CURSOR_OFF 0
#define CURSOR_ON 1

#define EMPTY_FILL 0
#define SOLID_FILL 1
#define DOT_FILL 2
#define LIGHT_FILL 3

#define ICON_REAL 0
#define ICON_ALARM 1
#define ICON_PARA 2
#define ICON_CONTROL 3
#define ICON_RECORD 4
#define ICON_QUERY 5
#define ICON_CLOCK 6
#define ICON_VERSION 7
#define ICON_KEY 8
#define ICON_SAVE 9
#define ICON_CRY 10
#define ICON_SMILE 11
#define ICON_ARROW 12
#define ICON_CHANGE 13
#define ICON_OFF 14
#define ICON_ON 15
#define ICON_HELP 16
#define ICON_TRACE 17
#define ICON_MAIN 18
#define ICON_SAVESCREEN 19
#define ICON_WARNING 20
#define ICON_BATTERY 21
#define ICON_AC1 22
#define ICON_AC2 23

/*********************  函数原型定义  **********************/
void SaveScreen(void);
void RestoreScreen(void);
char *GetDestStr(rt_uint16_t wId);
void MoveMouse(rt_uint8_t x, rt_uint8_t y);
void InitCursor(rt_uint8_t ucWidth, rt_uint8_t ucHeight, rt_uint8_t ucStyle);
void MoveCursor(rt_uint8_t x, rt_uint8_t y);
void FlashCursor(void);
void ClrScreen(void);
void InitLcd(void);
void InitLcdRecover(void);
void DispCh8(rt_uint8_t x, rt_uint8_t y, rt_uint8_t ch);
void DispCh16(rt_uint8_t x, rt_uint8_t y, rt_uint8_t ch);
void TextOut8(rt_uint8_t x, rt_uint8_t y, rt_uint8_t *pStr);
void TextOut16(rt_uint8_t x, rt_uint8_t y, rt_uint8_t *pStr);
void Line(rt_uint8_t x1, rt_uint8_t y1, rt_uint8_t x2, rt_uint8_t y2);
void DashedScroll(float fPara, float fMax, float fMin);
void Rectangle(rt_uint8_t x1, rt_uint8_t y1, rt_uint8_t x2, rt_uint8_t y2);
void Bar(rt_uint8_t x1, rt_uint8_t y1, rt_uint8_t x2, rt_uint8_t y2);
void FillRec(rt_uint8_t x1, rt_uint8_t y1, rt_uint8_t x2, rt_uint8_t y2, rt_uint8_t ucStyle);
void PutIcon(rt_uint8_t x, rt_uint8_t y, rt_uint8_t ucIconId);
// void	TextOut24(rt_uint8_t x, rt_uint8_t y, char * pStr);
void DisplayBmp(rt_uint8_t ucId);
void LcdDisplay(void);
rt_uint8_t *GetImageBuf(rt_uint8_t ucId);
void LCD_Power(rt_uint8_t ucCtrl);
void SpecicalDisplay(rt_uint8_t ucStyle);
rt_uint8_t *GetRomMsg(rt_uint32_t lOffset, rt_uint8_t ucNum);
void LcdCheck(void);
char *GetHisPeakSpec(void);

#endif
