#ifndef _CONCURRENT_UPDATE_MASTER_HANDLE_H_
#define _CONCURRENT_UPDATE_MASTER_HANDLE_H_

#ifdef __cplusplus
extern "C" {
#endif
#include "device_type.h"

#define CONCURRENT_UPDATE_DATA_LEN 512

int parse_extern_trig_data(void* dev_inst, void* cmd_buf);
int pack_extern_trig_data(void* dev_inst, void *cmd_buf);
int parse_concur_update_data(void* dev_inst, void *cmd_buf);
int pack_concur_update_data(void* dev_inst, void *cmd_buf);
int parse_concur_confirm_update_data(void* dev_inst, void *cmd_buf);
int pack_concur_confirm_update_data(void* dev_inst, void *cmd_buf);
int parse_concur_confirm_update(void* dev_inst, void *cmd_buf);
int pack_concur_confirm_update(void* dev_inst, void *cmd_buf);
int parse_concurrent_master_trig(void* dev_inst, void *cmd_buf);
int parse_concurrent_master_data(void* dev_inst, void *cmd_buf);
int pack_concurrent_master_data(void* dev_inst, void *cmd_buf);
int parse_concurrent_update_ack_frame(void* dev_inst, void *cmd_buf);
int pack_concurrent_update_ack_frame(void* dev_inst, void *cmd_buf);
int parse_concurrent_update_frame(void* dev_inst, void *cmd_buf);
int pack_concurrent_update_frame(void* dev_inst, void *cmd_buf);
int set_concurrent_update_ack_info(unsigned char flag, unsigned short frame_no);
int pack_concurrent_master_trig(void* dev_inst, void *cmd_buf);
int fill_update_file_info(unsigned char* file_name, unsigned int file_size, unsigned int file_crc);
int get_concurrent_update_ack_info(unsigned char *flag, unsigned short* frame_no);
int set_concurrent_update_ack_info(unsigned char flag, unsigned short frame_no);
int send_concurrent_update_cmd(char addr, unsigned short cmd_id);
int master_download_init() ;
int get_concurrent_update_process_status();
int set_concurrent_update_process_status(int status);
int clear_concurrent_update_trig_info();
int get_update_file_frames();
int get_concurrent_update_slave_trig_times();
int get_concurrent_update_trig_flag();
int get_concurrent_update_process_status();
int master_deal_data_after_recv_pkt();
int supply_frames_process(dev_inst_t* download_inst);
void concurrent_timeout();
int deal_ack_frame_data_frame_after_rcv_pkt(dev_inst_t* download_inst);
int deal_trig_frame_after_rcv_pkt(unsigned short dev_addr);
void trig_frame_timeout_process(dev_inst_t* download_inst);
void ack_frame_timeout_process(dev_inst_t* download_inst);
void ack_data_frame_timeout_process(dev_inst_t* download_inst);
void update_frame_timeout_process(dev_inst_t* download_inst);
int clear_concurrent_index();
int enter_broadcast_frame_process(dev_inst_t* download_inst, unsigned char concurrent_addr);
int start_next_device_update();
#ifdef __cplusplus
}
#endif

#endif  


