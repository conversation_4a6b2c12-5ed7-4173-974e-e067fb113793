#include <string.h>
#include <stdlib.h>
#include <rtthread.h>
#include "pdt_version.h"
#include "sps.h"
#include "dev_acmu.h"
#include "utils_time.h"
#include "acmu_protocol_1104.h"
#include "protocol_layer.h"
#include "alarm_mgr_api.h"
#include "realdata_id_in.h"
#include "alarm_id_in.h"
#include "para_id_in.h"
#include "para_manage.h"
#include "utils_data_transmission.h"
#include "utils_rtthread_security_func.h"
#include "utils_data_type_conversion.h"
#include "realdata_save.h"
#include "data_type.h"
#include "alarm_manage.h"
#include "alarm_register.h"

Static unsigned char s_command_group = 0x00;

Static int check_ac_command_group(void* dev_inst, void *cmd_buf);
Static int pack_ac_analog_float_data(void* dev_inst, void *cmd_buf);
Static int pack_ac_digital_data(void* dev_inst, void *cmd_buf);
Static int pack_acem_fact_data(void* dev_inst, void *cmd_buf);
Static int pack_monitor_fact_data(void* dev_inst, void *cmd_buf);
Static int pack_sys_time_data(void* dev_inst, void* cmd_buff);
Static int parse_sys_time_data(void* dev_inst, void* cmd_buff);
Static int pack_env_analog_float_data(void* dev_inst, void* cmd_buff);
Static int pack_env_digital_data(void* dev_inst, void* cmd_buff);
Static int pack_env_alm_data(void* dev_inst, void* cmd_buff);
Static int pack_env_para(void* dev_inst, void* cmd_buff);
Static int parse_env_para(void* dev_inst, void* cmd_buff);
Static int pack_ac_para_data(void* dev_inst, void *cmd_buf);
Static int parse_ac_para_data(void* dev_inst, void* cmd_buf);
Static int pack_ac_alm_data(void* dev_inst, void* cmd_buff);
Static int judge_volt(unsigned char* buff, unsigned char offset);

static s1363_cmd_head_t cmd_req[] RAM_SECTION = 
{
    {VER_20 , CID1_AC , ACMU_CMD_GET_SYS_TIME},                  //0
    {VER_20 , CID1_AC , ACMU_CMD_SET_SYS_TIME},                  //1
    {VER_20 , CID1_AC , ACMU_CMD_GET_COMMPROTOCOL_VER},          //2
    {VER_20 , CID1_AC , ACMU_CMD_GET_DEVICE_ADDR},               //3
    {VER_20 , CID1_AC , ACMU_CMD_GET_ANALOG_FLOAT},              //4
    {VER_20 , CID1_AC , ACMU_CMD_GET_SWITCH},                    //5
    {VER_20 , CID1_AC , ACMU_CMD_GET_ACEMFACTORY_INFO},          //6
    {VER_20 , CID1_AC , ACMU_CMD_GET_PARA},                      //7
    {VER_20 , CID1_AC , ACMU_CMD_SET_PARA},                      //8
    {VER_20 , CID1_AC , ACMU_CMD_GET_ALM},                       //9
    {VER_20 , CID1_AC , ACMU_CMD_GET_MONITORFACTORY_INFO},       //10
    {VER_20 , CID1_ENV , ACMU_CMD_GET_ANALOG_FLOAT},                 //11
    {VER_20 , CID1_ENV , ACMU_CMD_GET_SWITCH},                       //12
    {VER_20 , CID1_ENV , ACMU_CMD_GET_ALM},                          //13
    {VER_20 , CID1_ENV , ACMU_CMD_GET_PARA},                         //14
    {VER_20 , CID1_ENV , ACMU_CMD_SET_PARA},                         //15

};

static s1363_cmd_head_t cmd_ack[] RAM_SECTION = 
{
    {VER_20 , CID1_AC , ACMU_CMD_GET_SYS_TIME},                  //0
    {VER_20 , CID1_AC , ACMU_CMD_SET_SYS_TIME},                  //1
    {VER_20 , CID1_AC , ACMU_CMD_GET_COMMPROTOCOL_VER},          //2
    {VER_20 , CID1_AC , ACMU_CMD_GET_DEVICE_ADDR},               //3
    {VER_20 , CID1_AC , ACMU_CMD_GET_ANALOG_FLOAT},              //4
    {VER_20 , CID1_AC , ACMU_CMD_GET_SWITCH},                    //5
    {VER_20 , CID1_AC , ACMU_CMD_GET_ACEMFACTORY_INFO},          //6
    {VER_20 , CID1_AC , ACMU_CMD_GET_PARA},                      //7
    {VER_20 , CID1_AC , ACMU_CMD_SET_PARA},                      //8
    {VER_20 , CID1_AC , ACMU_CMD_GET_ALM},                       //9
    {VER_20 , CID1_AC , ACMU_CMD_GET_MONITORFACTORY_INFO},       //10
    {VER_20 , CID1_ENV , ACMU_CMD_GET_ANALOG_FLOAT},                 //11
    {VER_20 , CID1_ENV , ACMU_CMD_GET_SWITCH},                       //12
    {VER_20 , CID1_ENV , ACMU_CMD_GET_ALM},                          //13
    {VER_20 , CID1_ENV , ACMU_CMD_GET_PARA},                         //14
    {VER_20 , CID1_ENV , ACMU_CMD_SET_PARA},                         //15
};


static cmd_t no_poll_cmd_tab[] = 
{
    {ACMU_GET_SYS_TIME, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),   NULL, NULL, pack_sys_time_data, NULL,},
    {ACMU_SET_SYS_TIME, CMD_PASSIVE, &cmd_req[1], &cmd_ack[1], sizeof(s1363_cmd_head_t),   NULL, NULL,  NULL,parse_sys_time_data, },
    {ACMU_GET_COMMPROTOCOL_VER, CMD_PASSIVE, &cmd_req[2], &cmd_ack[2], sizeof(s1363_cmd_head_t),   NULL, NULL, NULL, NULL, },
    {ACMU_GET_DEVICE_ADDR,  CMD_PASSIVE, &cmd_req[3], &cmd_ack[3], sizeof(s1363_cmd_head_t),   NULL, NULL, NULL, NULL,},
    {ACMU_GET_AC_ANALOG_FLOAT, CMD_PASSIVE, &cmd_req[4], &cmd_ack[4], sizeof(s1363_cmd_head_t),   NULL, NULL, pack_ac_analog_float_data,check_ac_command_group,},
    {ACMU_GET_AC_SWITCH, CMD_PASSIVE, &cmd_req[5], &cmd_ack[5], sizeof(s1363_cmd_head_t),   NULL, NULL, pack_ac_digital_data,check_ac_command_group, },
    {ACMU_GET_ACEMFACTORY_INFO,  CMD_PASSIVE, &cmd_req[6], &cmd_ack[6], sizeof(s1363_cmd_head_t),   NULL, NULL, pack_acem_fact_data, NULL, },
    {ACMU_GET_AC_PARA, CMD_PASSIVE, &cmd_req[7], &cmd_ack[7], sizeof(s1363_cmd_head_t),   NULL, NULL, pack_ac_para_data, NULL, NULL, NULL},
    {ACMU_SET_AC_PARA, CMD_PASSIVE, &cmd_req[8], &cmd_ack[8], sizeof(s1363_cmd_head_t),   NULL, NULL, NULL, parse_ac_para_data, NULL, NULL},
    {ACMU_GET_AC_ALM_DATA,  CMD_PASSIVE, &cmd_req[9], &cmd_ack[9], sizeof(s1363_cmd_head_t),   NULL, NULL, pack_ac_alm_data, NULL, },
    {ACMU_GET_ENV_ANALOG_FLOAT,  CMD_PASSIVE, &cmd_req[10], &cmd_ack[10], sizeof(s1363_cmd_head_t),   NULL, NULL, pack_monitor_fact_data,NULL,},
    {ACMU_GET_ENV_ANALOG_FLOAT,  CMD_PASSIVE, &cmd_req[11], &cmd_ack[11], sizeof(s1363_cmd_head_t),   NULL, NULL, pack_env_analog_float_data,NULL,},
    {ACMU_GET_ENV_SWITCH, CMD_PASSIVE, &cmd_req[12], &cmd_ack[12], sizeof(s1363_cmd_head_t),   NULL, NULL, pack_env_digital_data,check_ac_command_group,},
    {ACMU_GET_ENV_ALM_DATA, CMD_PASSIVE, &cmd_req[13], &cmd_ack[13], sizeof(s1363_cmd_head_t),   NULL, NULL, pack_env_alm_data,NULL, },
    {ACMU_GET_ENV_PARA,  CMD_PASSIVE, &cmd_req[14], &cmd_ack[14], sizeof(s1363_cmd_head_t),   NULL, NULL, pack_env_para,NULL, },
    {ACMU_SET_ENV_PARA,  CMD_PASSIVE, &cmd_req[15], &cmd_ack[15], sizeof(s1363_cmd_head_t),   NULL, NULL, NULL,parse_env_para, },
    {0},
};

Static dev_type_t dev_acmu_1104 = 
{
    DEV_NORTH_1104, 1, PROTOCOL_YD_1363, LINK_ACMU, R_BUFF_LEN, S_BUFF_LEN, 0, &no_poll_cmd_tab[0],
};

dev_type_t* init_dev_acmu_1104(void){
    return &dev_acmu_1104;
}

Static int check_ac_command_group(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    unsigned char* buff;
    
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf_temp != NULL, FAILURE);

    buff = cmd_buf_temp->buf;

    RETURN_VAL_IF_FAIL(buff != NULL, FAILURE);

    if(cmd_buf_temp->data_len > 0) {
        s_command_group = buff[0];
    } else {
        s_command_group = WRONG_DATA_LENGTH;
    }

    return SUCCESSFUL;
}


Static int pack_ac_analog_float_data(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    unsigned char* buff;
    unsigned int offset = 0;
    float f_data = 0;
    int i = 0;
    acem_analog_data_t acem_show_data;

    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf_temp != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(cmd_buf_temp->buf != NULL, FAILURE);
    
    if(s_command_group != 0x00) {
        ((cmd_buf_t*)cmd_buf)->rtn = RTN_WRONG_COMMAND;
        ((cmd_buf_t*)cmd_buf)->data_len = 0;
        return SUCCESSFUL;
    }

    rt_memset_s(&acem_show_data, sizeof(acem_show_data), 0, sizeof(acem_show_data));

    get_acem_show_data(&acem_show_data);
    
    buff = cmd_buf_temp->buf;

    buff[offset] = 0x01;                      // 本屏交流配电数量
    offset += 1;

    for(i = 0; i < AC_PHASE_NUM; i++) {
        get_one_data(ACMU_DATA_ID_PHASE_VOLT + i, &f_data);
        put_float_to_buff(&buff[offset], f_data);                         //相电压
        offset += 4;
    }

    buff[offset++]    = 0x20;
    buff[offset++]    = 0x20;
    buff[offset++]    = 0x20;
    buff[offset++]    = 0x20;              //交流频率，未使用，使用20H填充

    buff[offset]    = 0x10;                             // 自定义遥测数量为16
    offset += 1;

    for(i = 0; i < AC_PHASE_NUM; i++) {
        get_one_data(ACMU_DATA_ID_LINE_VOLT + i, &f_data);
        put_float_to_buff(&buff[offset], f_data);                         //线电压
        offset += 4;
    }

    for(i = 0; i < AC_PHASE_NUM; i++) {;
        put_float_to_buff(&buff[offset], acem_show_data.phase_volt[i]);                         //交流电表相电压
        offset += 4;
    }

    for(i = 0; i < AC_PHASE_NUM; i++) {
        put_float_to_buff(&buff[offset], acem_show_data.phase_curr[i]);                         //交流电表相电流
        offset += 4;
    }

    for(i = 0; i < AC_PHASE_NUM; i++) {
        put_float_to_buff(&buff[offset], acem_show_data.power_factor[i]);                         //交流电表功率因数
        offset += 4;
    }

    for(i = 0; i < AC_PHASE_NUM; i++) {
        put_float_to_buff(&buff[offset], acem_show_data.total_act_power);                         //交流电表相有功功率
        offset += 4;
    }

    put_float_to_buff(&buff[offset], acem_show_data.total_energy);                         //交流电表电能
    offset += 4;

    buff[offset]    = 0x03;                             // 用户自定义字节为3
    offset += 1;

    for(i = 0; i < AC_PHASE_NUM; i++) {
        get_one_data(ACMU_DATA_ID_PHASE_CURR + i, &f_data);
        put_float_to_buff(&buff[offset], f_data);                         //相电流
        offset += 4;
    }

    ((cmd_buf_t*)cmd_buf)->data_len = offset;
    return SUCCESSFUL;
}

Static int pack_ac_digital_data(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    unsigned char* buff;
    unsigned int offset = 0;
    unsigned char uc_data = 0;
    int i = 0;

    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf_temp != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(cmd_buf_temp->buf != NULL, FAILURE);
    
    if(s_command_group != 0x00) {
        ((cmd_buf_t*)cmd_buf)->rtn = RTN_WRONG_COMMAND;
        ((cmd_buf_t*)cmd_buf)->data_len = 0;
        return SUCCESSFUL;
    }
    
    buff = cmd_buf_temp->buf;
    
    buff[offset++]    = 0x00;                            // 监测的开关数量
    buff[offset++]    = 0x14;                            // 自定义开关数量
    
    get_one_data(ACMU_DATA_ID_AC_INPUT_SWITCH, &uc_data);
    buff[offset++]    = uc_data;                        // 交流输入空开状态 

    get_one_data(ACMU_DATA_ID_POWER_SOURCE, &uc_data);
    buff[offset++]    = uc_data;                    //系统供电 0市电1；1市电2；2电池
    for( i = 0; i < RELAY_NUM; i++ )
    {
        get_one_data(ACMU_DATA_ID_INPUT_RELAY + i, &uc_data);
        buff[offset++]    = uc_data;                    // ACMU输入干结点
    }

    for( i = 0; i < SWITCH_NUM; i++ )
    {
        get_one_data(ACMU_DATA_ID_AC_OUT_SWITCH + i, &uc_data);
        buff[offset++]    = uc_data;                    // 交流输出空开
    }

    get_one_data(ACMU_DATA_ID_EMERGENCY_LAMP_STATE, &uc_data);
    buff[offset++]    = uc_data;                        // 应急照明灯状态 

    get_one_data(ACMU_DATA_ID_LIGHT_PRO_CIRCUIT_C, &uc_data);
    buff[offset++]    = uc_data;                    //C级防雷回路

    ((cmd_buf_t*)cmd_buf)->data_len = offset;
    return SUCCESSFUL;
}

Static int pack_acem_fact_data(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    unsigned char* buff;
    unsigned int offset = 0;
    char acem_sys_version[10];                    //交流电表版本
    char acem_device_name[20] = ACEMDEVICES;      //交流电表型号

    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf_temp != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(cmd_buf_temp->buf != NULL, FAILURE);

    buff = cmd_buf_temp->buf;
    
    rt_memset_s(&buff[offset], 10, 0x20, 10);
    get_one_data(ACMU_DATA_ID_ACEM_SYS_VERSION, acem_sys_version);
    rt_memcpy_s(&buff[offset], 10, acem_sys_version, rt_strnlen_s(acem_sys_version, 10));

    offset += 10;
    rt_memset_s(&buff[offset], 20, 0x20, 20);

    if (is_show_acem_fact() ==  TRUE) {
        rt_memcpy_s(&buff[offset], 20, acem_device_name, rt_strnlen_s(acem_device_name, 20));
    }
    offset += 20;

    ((cmd_buf_t*)cmd_buf)->data_len = offset;
    return SUCCESSFUL;
};

Static int pack_monitor_fact_data(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    unsigned char* buff;
    unsigned int offset = 0;
    unsigned char sm_nameLen = 20;             //SM软件名称长度
    unsigned char fact_nameLen = 20;           //厂家名称长度
    char aucDeviceName[] = SOFTWARE_NAME;
    unsigned char    ucMajorVer = MAJOR_VER;
    unsigned char    ucMinorVer = MINOR_VER;
	char ucCorpName[] = CORPERATION_NAME;

    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf_temp != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(cmd_buf_temp->buf != NULL, FAILURE);

    buff = cmd_buf_temp->buf;
    
    rt_memset_s( &buff[offset], sm_nameLen,0x20, sm_nameLen );
    rt_memcpy_s( &buff[offset], rt_strnlen_s(aucDeviceName, sm_nameLen),aucDeviceName, rt_strnlen_s(aucDeviceName, sm_nameLen));     /* 监控模块名称 */
    offset	+= sm_nameLen;
	
    buff[offset++]	= ucMajorVer;			/* 厂家软件版本 */
    buff[offset++]	= ucMinorVer;

    rt_memset_s( &buff[offset], fact_nameLen,0x20, fact_nameLen );
    rt_memcpy_s( &buff[offset], rt_strnlen_s(ucCorpName, fact_nameLen),ucCorpName, rt_strnlen_s(ucCorpName, fact_nameLen));                /* 厂家名称 */
    offset  += fact_nameLen;

    ((cmd_buf_t*)cmd_buf)->data_len = offset;
    return SUCCESSFUL;
};

Static int pack_sys_time_data(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    time_base_t  pt_time={0};
    int offset=0;
    get_time(&pt_time);

    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;

    put_int16_to_buff(&data_buff[offset],pt_time.year);
    offset+=2;

    data_buff[offset++] = pt_time.month;
    data_buff[offset++] = pt_time.day;
    data_buff[offset++] = pt_time.hour;
    data_buff[offset++] = pt_time.minute;
    data_buff[offset++] = pt_time.second;

    ((cmd_buf_t*)cmd_buff)->data_len=offset;

    return SUCCESSFUL;
}

Static int parse_sys_time_data(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    int offset = 0;
    time_base_t tm = {0};
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;

    tm.year = get_int16_data(&data_buff[offset]);
    offset+=2;
    tm.month  = data_buff[offset++];
    tm.day = data_buff[offset++];
    tm.hour = data_buff[offset++];
    tm.minute  = data_buff[offset++];
    tm.second  = data_buff[offset++];
    if( check_time_range(tm) == FAILURE )
    {
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA ;
        return SUCCESSFUL;
    }

    /* 设置系统时间 */
    if (set_system_time(&tm) != SUCCESSFUL) {
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA;
        return SUCCESSFUL;
    }
    
    return SUCCESSFUL;
}

Static int pack_env_analog_float_data(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff;
    unsigned char offset = 0;
    float f_data = 0.0f;

    buff = cmd_buf_temp->buf;

    /* DATA_FLAG */
    get_one_data(ACMU_DATA_ID_DATA_FLAG,&buff[offset++]);
    
    /* DATAF */
    buff[offset++] = 0x01;            // 温度传感器数量m=1
    get_one_data(ACMU_DATA_ID_ENV_TEMP, &f_data); // 环境温度
    put_float_to_buff(&buff[offset], f_data);
    offset += 4;   
    

    buff[offset++] = 0x01;            // 湿度传感器数量n=1
    get_one_data(ACMU_DATA_ID_ENV_HUMIDITY, &f_data); // 环境湿度
    put_float_to_buff(&buff[offset], f_data);
    offset += 4;   
    
    buff[offset++]    = 0x00;                /* 自定义遥测数量为0 */

    /* LENID */
    cmd_buf_temp->data_len = offset;
    
    return SUCCESSFUL;
}


Static int pack_ac_para_data(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    unsigned char* buff;
    unsigned int offset = 0;
    float ac_phase_voltage_max = 0;
    float ac_phase_voltage_min = 0;
    float ac_current_max = 0;
    short ac_buff_short = 0;
    float ac_buff_float = 0;
    char ac_para_bit = 0;

    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf_temp != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(cmd_buf_temp->buf != NULL, FAILURE);

    buff = cmd_buf_temp->buf;
    
    get_one_para(ACMU_PARA_ID_AC_PHASE_VOLTAGE_MAX_OFFSET, &ac_phase_voltage_max);  // 交流输入线/相电压上限
    put_float_to_buff(&buff[offset], roundf(ac_phase_voltage_max));
    offset += 4;

    get_one_para(ACMU_PARA_ID_AC_PHASE_VOLTAGE_MIN_OFFSET, &ac_phase_voltage_min);  // 交流输入线/相电压下限 
    put_float_to_buff(&buff[offset], roundf(ac_phase_voltage_min));
    offset += 4;

    get_one_para(ACMU_PARA_ID_AC_IN_CURRENT_MAX_OFFSET, &ac_current_max);  // 交流输入电流上限 
    put_float_to_buff(&buff[offset], roundf(ac_current_max));
    offset += 4;

    get_one_para(ACMU_PARA_ID_AC_FREQUENCY_MAX_OFFSET, &ac_buff_short);   // 交流输入频率上限(未用项) 
    put_float_to_buff(&buff[offset], roundf(ac_buff_short));
    offset += 4;

    get_one_para(ACMU_PARA_ID_AC_FREQUENCY_MIN_OFFSET, &ac_buff_short);   // 交流输入频率下限(未用项) 
    put_float_to_buff(&buff[offset], roundf(ac_buff_short));
    offset += 4;

    buff[offset] = 5;    //用户自定义参数数量
    offset += 1;

    get_one_para(ACMU_PARA_ID_PHASE_VOLTAGE_UNBALANCE_OFFSET, &ac_buff_float);    // 用户自定义(相电压不平衡阀值) 
    put_float_to_buff(&buff[offset], roundf(ac_buff_float));
    offset += 4;

    get_one_para(ACMU_PARA_ID_AC_MUTUAL_INDUCTANCE_TYPE_OFFSET, &ac_buff_short);    // 用户自定义(交流互感类型-1#) 
    put_float_to_buff(&buff[offset], roundf(ac_buff_short));
    offset += 4;

    get_one_para(ACMU_PARA_ID_AC_MUTUAL_INDUCTANCE_TYPE_OFFSET + 1, &ac_buff_short);    // 用户自定义(交流互感类型-2#) 
    put_float_to_buff(&buff[offset], roundf(ac_buff_short));
    offset += 4;

    get_one_para(ACMU_PARA_ID_AC_MUTUAL_INDUCTANCE_TYPE_OFFSET + 2, &ac_buff_short);    // 用户自定义(交流互感类型-3#) 
    put_float_to_buff(&buff[offset], roundf(ac_buff_short));
    offset += 4;

    get_one_para(ACMU_PARA_ID_AC_METER_CONFIGURATION_OFFSET, &ac_para_bit); // 用户自定义(交流电表配置) 
    put_float_to_buff(&buff[offset], roundf(ac_para_bit));
    offset += 4;

    ((cmd_buf_t*)cmd_buf)->data_len = offset;
    return SUCCESSFUL;
}



Static int parse_ac_para_data(void* dev_inst, void* cmd_buf)
{
    unsigned char* buff;
    unsigned int offset = 1;
    float ac_phase_voltage_max = 0.0f;
    float ac_phase_voltage_min = 0.0f;
    float ac_current_max = 0;
    short ac_buff_short = 0;
    float ac_buff_float = 0;    
    char ac_para_bit = 0;
    int ucCommandType = 0;
    int rst = 0;

    RETURN_VAL_IF_FAIL(dev_inst != NULL, FAILURE);

    // 检查cmd_buf是否为空
    RETURN_VAL_IF_FAIL(cmd_buf != NULL, FAILURE);
    cmd_buf_t* s_cmd_buf = (cmd_buf_t*)cmd_buf;
    buff = s_cmd_buf->buf;

    // 检查buff是否为空
    RETURN_VAL_IF_FAIL(buff != NULL, FAILURE);

    /* COMMAND_TYPE */
    ucCommandType = buff[0];
    /* 获取参数在参数区中的序号 */
    if( ucCommandType == 0x80 )
    {
        ac_phase_voltage_max = get_float_data(&buff[offset]);
        rst = set_one_para(ACMU_PARA_ID_AC_PHASE_VOLTAGE_MAX_OFFSET, &ac_phase_voltage_max, TRUE, FALSE);  // 交流输入线/相电压上限
    }
    else if( ucCommandType == 0x81 )
    {
        ac_phase_voltage_min = get_float_data(&buff[offset]);
        rst = set_one_para(ACMU_PARA_ID_AC_PHASE_VOLTAGE_MIN_OFFSET, &ac_phase_voltage_min, TRUE, FALSE);  // 交流输入线/相电压下限
    }
    else if( ucCommandType == 0x82 )
    {
        ac_current_max = roundf(get_float_data(&buff[offset]));
        rst = set_one_para(ACMU_PARA_ID_AC_IN_CURRENT_MAX_OFFSET, &ac_current_max, TRUE, FALSE);  // 交流输入电流上限
    }
    else if( ucCommandType == 0x83 )
    {
        ac_buff_short = round(get_float_data(&buff[offset]));
        rst = set_one_para(ACMU_PARA_ID_AC_FREQUENCY_MAX_OFFSET, &ac_buff_short, TRUE, FALSE);  // 交流输入频率上限(未用项) 
    }
    else if( ucCommandType == 0x84 )
    {
        ac_buff_short = round(get_float_data(&buff[offset])); 
        rst = set_one_para(ACMU_PARA_ID_AC_FREQUENCY_MIN_OFFSET, &ac_buff_short, TRUE, FALSE);  // 交流输入频率下限(未用项)
    }
    else if( ucCommandType == 0xE0 )
    {
        ac_buff_float = roundf(get_float_data(&buff[offset])); 
        rst = set_one_para(ACMU_PARA_ID_PHASE_VOLTAGE_UNBALANCE_OFFSET, &ac_buff_float, TRUE, FALSE);  // 用户自定义(相电压不平衡阀值) 
    }
    else if( ucCommandType == 0xE1 )
    {
        ac_buff_short = round(get_float_data(&buff[offset]));
        rst = set_one_para(ACMU_PARA_ID_AC_MUTUAL_INDUCTANCE_TYPE_OFFSET, &ac_buff_short, TRUE, FALSE);  // 用户自定义(交流互感类型-1#) 
    }
    else if( ucCommandType == 0xE2 )
    {
        ac_buff_short = round(get_float_data(&buff[offset]));
        rst = set_one_para(ACMU_PARA_ID_AC_MUTUAL_INDUCTANCE_TYPE_OFFSET + 1, &ac_buff_short, TRUE, FALSE);  // 用户自定义(交流互感类型-2#) 
    }
    else if( ucCommandType == 0xE3 )
    {
        ac_buff_short = round(get_float_data(&buff[offset]));
        rst = set_one_para(ACMU_PARA_ID_AC_MUTUAL_INDUCTANCE_TYPE_OFFSET + 2, &ac_buff_short, TRUE, FALSE);  // 用户自定义(交流互感类型-3#) 
    }
    else if( ucCommandType == 0xE4 )
    {
        ac_para_bit = round(get_float_data(&buff[offset]));
        rst = set_one_para(ACMU_PARA_ID_AC_METER_CONFIGURATION_OFFSET, &ac_para_bit, TRUE, FALSE);  // 用户自定义(交流电表配置) 
    }
    else
    {
        ((cmd_buf_t*)cmd_buf)->rtn = RTN_INVALID_DATA; /* 命令格式错 */
        return FALSE;
    }

    if(rst < 0)
    {
        ((cmd_buf_t*)cmd_buf)->rtn = RTN_WRONG_COMMAND;
    }
    else
    {
        update_para();
    }


    return SUCCESSFUL;
}


Static int pack_env_digital_data(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff;
    unsigned char offset = 0,data_flag;
    
    buff = cmd_buf_temp->buf;
    /* COMMAND_GROUP */
    if(s_command_group != 0x00) {
        cmd_buf_temp->rtn = RTN_WRONG_COMMAND;
        cmd_buf_temp->data_len = 0;
        return SUCCESSFUL;
    }

    /* DATA_FLAG */
    get_one_data(ACMU_DATA_ID_DATA_FLAG,&data_flag);
    buff[offset++] = data_flag & 0xEF;

    buff[offset++] = 0x01;       // 交流屏数量

    /* RUN_STATE */
    buff[offset++] = 0x00;       // 监测的开关数量

    buff[offset++] = 0x05;       // 自定义开关数量
    
    get_one_data(ACMU_DATA_ID_FUMES_SWITCH_STATUS, &buff[offset++]);            //烟雾 
    get_one_data(ACMU_DATA_ID_FLOOD_SWITCH_STATUS, &buff[offset++]);            //水淹
    get_one_data(ACMU_DATA_ID_DOORMAT_SWITCH_STATUS, &buff[offset++]);          //门磁 
    buff[offset++] = 0x00;            //门禁
    buff[offset++] = 0x00;            //玻璃碎

    /* LENID */
    cmd_buf_temp->data_len = offset;

    return SUCCESSFUL;
}

Static int pack_env_alm_data(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    int alm_id = 0;
    unsigned char* buff;
    unsigned char offset = 0,data_flag;
    char alarm_status = 0;
    buff = cmd_buf_temp->buf;

    /* DATA_FLAG */
    get_one_data(ACMU_DATA_ID_DATA_FLAG,&data_flag);
    buff[offset++] = data_flag & 0xFE;
    
    /* WARN_STATE */
    /* WARN_STATE */
    buff[offset++] = 0x01;                    // 温度传感器
    buff[offset++] = temperature_sensor_alarm_judge();
            
    buff[offset++] = 0x01;                    // 湿度传感器
    buff[offset++] = humidity_sensor_alarm_judge();
                        
    buff[offset++] = 0x01;                    // 烟雾传感器
    alm_id = GET_ALM_ID(ACMU_ALM_ID_FUMES_SENSOR_ALARM,1,1);
    alarm_status = get_realtime_alarm_value(alm_id);
    buff[offset++] = alarm_status == TRUE ? ENV_FAULT : 0; 

    buff[offset++] = 0x01;                    // 水浸传感器
    alm_id = GET_ALM_ID(ACMU_ALM_ID_FLOOD_SENSOR_ALARM,1,1);
    alarm_status = get_realtime_alarm_value(alm_id);
    buff[offset++] = alarm_status == TRUE ? ENV_FAULT : 0; 

    buff[offset++] = 0x01;                    // 红外（门禁）传感器告警,已删除
    // alm_id = GET_ALM_ID(ACMU_ALM_ID_DOORINFRARED_SENSOR_ALARM,1,1);
    // alarm_status = get_realtime_alarm_value(alm_id);
    buff[offset++] = 0; 
    
    buff[offset++] = 0x01;                    // 门窗（门磁）传感器告警
    alm_id = GET_ALM_ID(ACMU_ALM_ID_DOORMAT_SENSOR_ALARM,1,1);
    alarm_status = get_realtime_alarm_value(alm_id);
    buff[offset++] = alarm_status == TRUE ? ENV_FAULT : 0; 

    buff[offset++] = 0x01;                    // 玻璃碎传感器告警，已删除
    // alm_id = GET_ALM_ID(ACMU_ALM_ID_GLASS_SENSOR_ALARM,1,1);
    // alarm_status = get_realtime_alarm_value(alm_id);
    buff[offset++] = 0; 
        
    buff[offset++] = 0x00;        // 自定义告警数量为0

    /* LENID */
    cmd_buf_temp->data_len = offset;
    
    return SUCCESSFUL;
}

Static int pack_env_para(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff;
    unsigned char offset = 0;
    float f_data = 0.0f;

    buff = cmd_buf_temp->buf;

    /* DATAF */
    buff[offset++] = 0x01;                            /* 温度传感器 */
    get_one_para(ACMU_PARA_ID_TEMPERATURE_SENSOR_UPPER_OFFSET,&f_data);   /* 温度上限 */
    put_float_to_buff(&buff[offset], f_data);
    offset += 4;

    get_one_para(ACMU_PARA_ID_TEMPERATURE_SENSOR_LOWER_OFFSET,&f_data);   /* 温度下限 */
    put_float_to_buff(&buff[offset], f_data);
    offset += 4;

    buff[offset++] = 0x01;                            /* 湿度传感器 */
    get_one_para(ACMU_PARA_ID_HUMIDITY_SENSOR_UPPER_OFFSET,&f_data);   /* 湿度上限 */
    put_float_to_buff(&buff[offset], f_data);
    offset += 4;

    get_one_para(ACMU_PARA_ID_HUMIDITY_SENSOR_LOWER_OFFSET,&f_data);   /* 湿度下限 */
    put_float_to_buff(&buff[offset], f_data);
    offset += 4;
    
    buff[offset++] = 0x00;                            /* 用户自定义数量 */
    
    /* LENID */
    cmd_buf_temp->data_len = offset;
    
    return SUCCESSFUL;
}


Static int parse_env_para(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff;
    unsigned char command_type = 0;
    float f_data = 0.0f;
    signed char rst = 0;
    buff = cmd_buf_temp->buf;

    /* COMMAND_GROUP */
    if(buff[0] != 0x01) 
    {
        cmd_buf_temp->rtn = RTN_WRONG_COMMAND;
        return SUCCESSFUL;
    }

    /* COMMAND_TYPE */
    command_type = buff[1];
    switch(command_type)
    {
        case 0x80:          //设置温度传感器告警上限
            f_data = get_float_data(&buff[2]);          
            rst = set_one_para(ACMU_PARA_ID_TEMPERATURE_SENSOR_UPPER_OFFSET,&f_data,TRUE,FALSE);
            break;
        case 0x81:          //设置温度传感器告警下限
            f_data = get_float_data(&buff[2]);
            rst = set_one_para(ACMU_PARA_ID_TEMPERATURE_SENSOR_LOWER_OFFSET,&f_data,TRUE,FALSE);
            break;
        case 0x82:          //设置湿度传感器告警上限
            f_data = get_float_data(&buff[2]);
            rst = set_one_para(ACMU_PARA_ID_HUMIDITY_SENSOR_UPPER_OFFSET,&f_data,TRUE,FALSE);
            break;
        case 0x83:          //设置湿度传感器告警下限
            f_data = get_float_data(&buff[2]);
            rst = set_one_para(ACMU_PARA_ID_HUMIDITY_SENSOR_LOWER_OFFSET,&f_data,TRUE,FALSE);
            break;
        default:
            cmd_buf_temp->rtn = RTN_WRONG_COMMAND;
            break;
    }
    if(rst < 0)
    {
        cmd_buf_temp->rtn = RTN_WRONG_COMMAND;
    }
    else
    {
        update_para();
    }

    return SUCCESSFUL;
}



Static int judge_volt(unsigned char* buff, unsigned char offset)
{
    char alarm_status_high, alarm_status_low, alarm_status_lost[3];
    int alm_id = 0;
    alm_id = GET_ALM_ID(ACMU_ALM_ID_AC_PHASE_LOST, 1, 1); // 交流缺相
    alarm_status_lost[0] = get_realtime_alarm_value(alm_id);
    alm_id = GET_ALM_ID(ACMU_ALM_ID_AC_PHASE_LOST, 2, 1); // 交流缺相
    alarm_status_lost[1] = get_realtime_alarm_value(alm_id);
    alm_id = GET_ALM_ID(ACMU_ALM_ID_AC_PHASE_LOST, 3, 1); // 交流缺相
    alarm_status_lost[2] = get_realtime_alarm_value(alm_id);

    for (int i = 0; i < 3; i++) // 3相交流电压
    {
        alm_id = GET_ALM_ID(ACMU_ALM_ID_PHASE_VOLT_LOW_ALARM, i + 1, 1); // 交流相电压低
        alarm_status_low = get_realtime_alarm_value(alm_id);
        alm_id = GET_ALM_ID(ACMU_ALM_ID_PHASE_VOLT_HIGH_ALARM, i + 1, 1); // 交流相电压高
        alarm_status_high = get_realtime_alarm_value(alm_id);

        if (alarm_status_low != ALM_NORMAL)
        {
            buff[offset + i] = LOW;
        }
        else if (alarm_status_high != ALM_NORMAL)
        {
            buff[offset + i] = HIGH;
        }
        else if (alarm_status_lost[i] != ALM_NORMAL)
        {
            buff[offset + i] = LACK_PHASE;
        }
        else
        {
            buff[offset + i] = ALM_NORMAL;
        }

    }
    return SUCCESSFUL;
}

Static int pack_ac_alm_data(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    int alm_id = 0;
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff = cmd_buf_temp->buf;
    unsigned char offset = 0, data_flag;

    /* DATA_FLAG */
    get_one_data(ACMU_DATA_ID_DATA_FLAG, &data_flag);
    buff[offset++] = data_flag & 0xFE;

    /* COMMAND_GROUP */
    if (buff[0] != 0x00) 
    {
        cmd_buf_temp->rtn = RTN_WRONG_COMMAND;
        return SUCCESSFUL;
    }

    /* WARN_STATE */
    buff[offset++] = 0x01; // 本屏交流配电数量
    judge_volt(buff, offset);
    offset += 3;
    buff[offset++] = 0x20; // 输入频率
    buff[offset++] = 0; // 监测熔丝/开关数量
    buff[offset++] = 19; // 用户自定义告警数量

    alm_id = GET_ALM_ID(ACMU_ALM_ID_AC_POWER_OFF, 1, 1); // 交流停电
    buff[offset++] = get_realtime_alarm_value(alm_id);

    alm_id = GET_ALM_ID(ACMU_ALM_ID_PHASE_VOLT_UNBALANCE, 1, 1); // 相电压不平衡
    buff[offset++] = get_realtime_alarm_value(alm_id);
    
    alm_id = GET_ALM_ID(ACMU_ALM_ID_SPD_C, 1, 1); // C级防雷器坏
    buff[offset++] = get_realtime_alarm_value(alm_id);

    for (int i = 0; i < 12; i++)
    {
        alm_id = GET_ALM_ID(ACMU_ALM_ID_AC_OUTPUT_SWITCH_DISCONNECTION, 1 + i, 1); // 交流辅助输出开关断
        buff[offset++] = get_realtime_alarm_value(alm_id);
    }

    for (int i = 0; i < 3; i++)
    {
        alm_id = GET_ALM_ID(ACMU_ALM_ID_PHASE_CURR_HIGH_ALARM, 1 + i, 1); // 交流输入电流
        buff[offset++] = get_realtime_alarm_value(alm_id);
    }

    alm_id = GET_ALM_ID(ACMU_ALM_ID_AC_METER_DISCONNECTED, 1, 1); // 用户自定义(交流电表通讯断)
    buff[offset++] = get_realtime_alarm_value(alm_id);

    for (int i = 0; i < 3; i++)
    {
        alm_id = GET_ALM_ID(ACMU_ALM_ID_OUTPUT_CURRENT, 1 + i, 1); // 输出电流
        buff[offset++] = get_realtime_alarm_value(alm_id);
    }

    /* LENID */
    cmd_buf_temp->data_len = offset;

    return SUCCESSFUL;
}

