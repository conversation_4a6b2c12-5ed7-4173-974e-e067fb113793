/*
 * @file    : north_dxcb_modbus.c
 * @brief   : DXCB北向modbus协议实现
 * @details :
 * <AUTHOR> 邵敏10132013
 * @Date    : 2023-08-30
 * @LastEditTime: 2023-08-30
 * @version : V0.0.1
 * @para    : Copyright (c)
 *            ZTE Corporation
 * @par     : History
 *
 *     version: author, date, descn
 */

#include "dev_north_dxcb_modbus.h"
#include "protocol_layer.h"
#include <string.h>
#include "utils_data_transmission.h"
#include "utils_rtthread_security_func.h"
#include "realdata_save.h"
#include "realdata_id_in.h"
#include "para_id_in.h"
#include "eev_ctrl.h"
#include "utils_math.h"
#include "para_manage.h"
#include "msg_id.h"
#include "softbus.h"
#include "sps.h"
#include "south_dev_common.h"
#include "compressor_protect_logic.h"
#include "led.h"
#include "storage.h"
#include "ee_public_info.h"

/* 设备缓冲区长度 */
#define R_NORTH_MODBUS_BUFF_LEN 512 ///<  接收缓冲区长度
#define S_NORTH_MODBUS_BUFF_LEN 512 ///<  发送缓冲区长度

#define DEV_CODE_NO_USE 1
char g_is_ctrl_cmd = FALSE;
char g_eev_ctrl_flag = FALSE;
char g_is_do_ctrl = FALSE;

#define MODBUS_DATA_MAP_LEN sizeof(g_modbus_data_map) / sizeof(modbus_addr_map_data_t)
#define PARSE_FUN_MAP_LEN   sizeof(parse_fun_map)/sizeof(parse_fun_map_t)



/*******************************打包映射表*****************/
pack_fun_map_t pack_fun_map[] = {
    {TYPE_FLOAT,  TYPE_INT32U, floattoint32u },
    {TYPE_FLOAT,  TYPE_INT32S, floattoint32s},
    {TYPE_FLOAT,  TYPE_INT16U, floattoint16u},
    {TYPE_FLOAT,  TYPE_INT16S, floattoint16s},
    {TYPE_INT16U, TYPE_INT16U, int16utoint16u},
    {TYPE_INT16S, TYPE_INT16S, int16stoint16s},
    {TYPE_INT32U, TYPE_INT32U, int32utoint32u} ,
    {TYPE_INT32S, TYPE_INT32S, int32stoint32s} ,  
    {TYPE_STRING, TYPE_STRING, stringtostring},
    {TYPE_DATE_T, TYPE_INT16U, datetoint16u},
    {TYPE_INT8S,  TYPE_BIT,    chartobit},
    {TYPE_INT8U,  TYPE_INT16U, int8utoint16u},
    {TYPE_MAX,    TYPE_MAX,    NULL},
};


/*****************************解包映射表*****************/
parse_fun_map_t parse_fun_map[] = {
    {TYPE_INT16U, TYPE_FLOAT,  parse_int16utofloat},
    {TYPE_INT16S, TYPE_FLOAT,  parse_int16stofloat },
    {TYPE_INT32U, TYPE_FLOAT,  parse_int32utofloat },
    {TYPE_INT32S, TYPE_FLOAT,  parse_int32stofloat},
    {TYPE_INT16U, TYPE_INT16U, parse_int16utoint16u},
    {TYPE_INT32U, TYPE_INT32U, parse_int32utoint32u},
    {TYPE_STRING, TYPE_STRING, parse_stringtostring },
    {TYPE_INT16U, TYPE_INT8U,  parse_int16utoint8u},
    {TYPE_MAX,    TYPE_MAX,    NULL},
};


/* 命令请求头 */
static modbusrtu_cmd_head_t cmd_req[] = {
    {GET_ANA_DATA},
    {GET_PARA_DATA},
    {SET_SINGLE_PARA_DATA},
    {SET_PARA_DATA},
};

/* 命令应答头 */
static modbusrtu_cmd_head_t cmd_ack[] = {
    {GET_ANA_DATA},
    {GET_PARA_DATA},
    {SET_SINGLE_PARA_DATA},
    {SET_PARA_DATA},
};

/* 命令总表,必须以空字符串""结束 */
static cmd_t no_poll_cmd_tab[] = {
    {GET_ANA_DATA, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, new_pack_ana_para_data, parse_start_addr_and_reg_nums},
    {GET_PARA_DATA, CMD_PASSIVE, &cmd_req[1], &cmd_ack[1], sizeof(modbusrtu_cmd_head_t), NULL, NULL, new_pack_ana_para_data, parse_start_addr_and_reg_nums},
    {SET_SINGLE_PARA_DATA, CMD_PASSIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_set_single_para_data, parse_single_para_data_from_buff},//后续处理
    {SET_PARA_DATA, CMD_PASSIVE, &cmd_req[3], &cmd_ack[3], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_set_para_data, new_parse_para_data_from_buff},
    {0},
};


static dev_type_t dev_val_north_dxcb = {
    DEV_NORTH_DXCB_MODBUS, 
    1, 
    PROTOCOL_MODBUS_RTU, 
    LINK_NORTH_DXCB, 
    R_NORTH_MODBUS_BUFF_LEN, 
    S_NORTH_MODBUS_BUFF_LEN, 
    DEV_CODE_NO_USE, 
    no_poll_cmd_tab, 
    NULL,
    0
};


modbus_addr_map_data_t g_modbus_data_map[] = {
    /************************************数字量**************************************************************/ 
    {0, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INPUT_DRY_POINT},                  // DI1
    {1, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INPUT_DRY_POINT + 1},              // DI2
    {2, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INPUT_DRY_POINT + 2},              // DI3
    {3, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INPUT_DRY_POINT + 3},              // DI4
    {4, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INPUT_DRY_POINT + 4},              // DI5
    {5, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INPUT_DRY_POINT + 5},              // DI6
    {6, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INPUT_DRY_POINT + 6},              // DI7
    {7, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INPUT_DRY_POINT + 7},              // DI8
    {8, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INPUT_DRY_POINT + 8},              // DI9
    {9, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INPUT_DRY_POINT + 9},              // DI10
    {10, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INPUT_DRY_POINT + 10},            // DI11
    {11, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INPUT_DRY_POINT + 11},            // DI12
    {12, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INPUT_DRY_POINT + 12},            // DI13
    {13, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INPUT_DRY_POINT + 13},            // DI14
    {14, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INPUT_DRY_POINT + 14},            // DI15
    {15, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INPUT_DRY_POINT + 15},            // DI16
    {16, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INPUT_DRY_POINT + 16},            // DI17

    {17, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DCXB_TRACE_ID_FAN_COMM_FAIL_ALARM},            // 内风机1通信状态
    {18, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DCXB_TRACE_ID_FAN_COMM_FAIL_ALARM + 1},        // 内风机2通信状态
    {19, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DCXB_TRACE_ID_FAN_COMM_FAIL_ALARM + 2},        // 内风机3通信状态
    {20, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DCXB_TRACE_ID_FAN_COMM_FAIL_ALARM + 3},        // 内风机4通信状态
    {21, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DCXB_TRACE_ID_FAN_COMM_FAIL_ALARM + 4},        // 内风机5通信状态
    {22, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DCXB_TRACE_ID_FAN_COMM_FAIL_ALARM + 5},        // 内风机6通信状态
    {23, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DCXB_TRACE_ID_FAN_COMM_FAIL_ALARM + 6},        // 内风机7通信状态
    {24, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DCXB_TRACE_ID_FAN_COMM_FAIL_ALARM + 7},        // 内风机8通信状态
    {25, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DCXB_TRACE_ID_FAN_COMM_FAIL_ALARM + 8},        // 外风机1通信状态
    {26, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DCXB_TRACE_ID_FAN_COMM_FAIL_ALARM + 9},        // 外风机2通信状态
    {27, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DCXB_TRACE_ID_FAN_COMM_FAIL_ALARM + 10},       // 外风机3通信状态
    {28, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DCXB_TRACE_ID_FAN_COMM_FAIL_ALARM + 11},       // 外风机4通信状态
    {29, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DCXB_TRACE_ID_FAN_COMM_FAIL_ALARM + 12},       // 外风机5通信状态
    {30, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DCXB_TRACE_ID_FAN_COMM_FAIL_ALARM + 13},       // 外风机6通信状态
    {31, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DCXB_TRACE_ID_FAN_COMM_FAIL_ALARM + 14},       // 外风机7通信状态
    {32, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DCXB_TRACE_ID_FAN_COMM_FAIL_ALARM + 15},       // 外风机8通信状态
    {33, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DCXB_TRACE_ID_VFD_COMM_FAIL_ALARM},            // 变频器1通信状态
    {34, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DCXB_TRACE_ID_VFD_COMM_FAIL_ALARM + 1},        // 变频器2通信状态
    {35, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DCXB_TRACE_ID_VFD_COMM_FAIL_ALARM + 2},        // 变频器3通信状态
    {36, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DCXB_TRACE_ID_VFD_COMM_FAIL_ALARM + 3},        // 变频器4通信状态


    /***************************************模拟量***************************************************************/
    {100, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_AI_SIGNAL},               // AI1
    {101, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 1},           // AI2
    {102, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 2},           // AI3
    {103, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 3},           // AI4
    {104, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 4},           // AI5
    {105, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 5},           // AI6
    {106, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 6},           // AI7
    {107, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 7},           // AI8
    {108, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 8},           // AI9
    {109, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 9},           // AI10
    {110, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 10},          // AI11
    {111, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 11},          // AI12
    {112, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 12},          // AI13
    {113, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 13},          // AI14
    {114, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 14},          // AI15
    {115, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 15},          // AI16
    {116, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL},              // NTC1
    {117, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 1},          // NTC2
    {118, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 2},          // NTC3
    {119, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 3},          // NTC4
    {120, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 4},          // NTC5
    {121, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 5},          // NTC6
    {122, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 6},          // NTC7
    {123, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 7},          // NTC8
    {124, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 8},          // NTC9
    {125, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 9},          // NTC10
    {126, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 10},         // NTC11
    {127, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 11},         // NTC12
    {128, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 12},         // NTC13
    {129, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 13},         // NTC14
    {130, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 14},         // NTC15
    {131, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 15},         // NTC16
    {132, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EEV_FEEDBACK_STEP} ,            // 电子膨胀阀1反馈步数
    {133, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EEV_FEEDBACK_STEP + 1} ,        // 电子膨胀阀2反馈步数
    {134, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EEV_FEEDBACK_STEP + 2} ,        // 电子膨胀阀3反馈步数
    {135, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EEV_FEEDBACK_STEP + 3} ,        // 电子膨胀阀4反馈步数
    {136, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EEV_FEEDBACK_STEP + 4} ,        // 电子膨胀阀5反馈步数
    {137, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EEV_FEEDBACK_STEP + 5} ,        // 电子膨胀阀6反馈步数
    {138, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EEV_FEEDBACK_STEP + 6} ,        // 电子膨胀阀7反馈步数
    {139, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EEV_FEEDBACK_STEP + 7} ,        // 电子膨胀阀8反馈步数


    {200, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_SPEED_PERCENT},        // 内风机1转速PER
    {201, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_SPEED},                // 内风机1转速PRM
    {202, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_POWER},                // 内风机1功率
    {203, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_CURR},                 // 内风机1电流
    {204, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE} ,            // 内风机1错误码1
    {205, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 1} ,        // 内风机1错误码2
    {206, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 2} ,        // 内风机1错误码3
    {207, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 3} ,        // 内风机1错误码4
    {208, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_SPEED_PERCENT + 1},    // 内风机2转速PER
    {209, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_SPEED + 1},            // 内风机2转速PRM
    {210, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_POWER + 1},            // 内风机2功率
    {211, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_CURR + 1},             // 内风机2电流
    {212, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 4} ,        // 内风机2错误码1
    {213, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 5} ,        // 内风机2错误码2
    {214, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 6} ,        // 内风机2错误码3
    {215, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 7} ,        // 内风机2错误码4
    {216, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_SPEED_PERCENT + 2},    // 内风机3转速PER
    {217, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_SPEED + 2},            // 内风机3转速PRM
    {218, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_POWER + 2},            // 内风机3功率
    {219, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_CURR + 2},             // 内风机4电流
    {220, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 8} ,        // 内风机3错误码1
    {221, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 9} ,        // 内风机3错误码2
    {222, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 10} ,       // 内风机3错误码3
    {223, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 11} ,       // 内风机3错误码4
    {224, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_SPEED_PERCENT + 3},    // 内风机4转速PER
    {225, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_SPEED + 3},            // 内风机4转速PRM
    {226, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_POWER + 3},            // 内风机4功率
    {227, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_CURR + 3},             // 内风机4电流
    {228, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 12} ,       // 内风机4错误码1
    {229, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 13} ,       // 内风机4错误码2
    {230, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 14} ,       // 内风机4错误码3
    {231, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 15} ,       // 内风机4错误码4
    {232, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_SPEED_PERCENT + 4},        // 内风机5转速PER
    {233, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_SPEED + 4},                // 内风机5转速PRM
    {234, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_POWER + 4},                // 内风机5功率
    {235, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_CURR + 4},                 // 内风机5电流
    {236, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 16} ,           // 内风机5错误码1
    {237, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 17} ,           // 内风机5错误码2
    {238, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 18} ,           // 内风机5错误码3
    {239, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 19} ,           // 内风机5错误码4
    {240, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_SPEED_PERCENT + 5},        // 内风机6转速PER
    {241, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_SPEED + 5},                // 内风机6转速PRM
    {242, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_POWER + 5},                // 内风机6功率
    {243, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_CURR + 5},                 // 内风机6电流
    {244, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 20} ,           // 内风机6错误码1
    {245, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 21} ,           // 内风机6错误码2
    {246, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 22} ,           // 内风机6错误码3
    {247, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 23} ,           // 内风机6错误码4
    {248, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_SPEED_PERCENT + 6},        // 内风机7转速PER
    {249, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_SPEED + 6},                // 内风机7转速PRM
    {250, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_POWER + 6},                // 内风机7功率
    {251, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_CURR + 6},                 // 内风机7电流
    {252, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 24} ,           // 内风机7错误码1
    {253, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 25} ,           // 内风机7错误码2
    {254, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 26} ,           // 内风机7错误码3
    {255, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 27} ,           // 内风机7错误码4
    {256, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_SPEED_PERCENT + 7},        // 内风机8转速PER
    {257, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_SPEED + 7},                // 内风机8转速PRM
    {258, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_POWER + 7},                // 内风机8功率
    {259, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_CURR + 7},                 // 内风机8电流
    {260, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 28} ,           // 内风机8错误码1
    {261, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 29} ,           // 内风机8错误码2
    {262, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 30} ,           // 内风机8错误码3
    {263, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 31} ,           // 内风机8错误码4



    {264, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_SPEED_PERCENT},       // 外风机1转速PER
    {265, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_SPEED},               // 外风机1转速PRM
    {266, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_POWER},               // 外风机1功率
    {267, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_CURR},                // 外风机1电流
    {268, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE} ,           // 外风机1错误码1
    {269, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 1} ,       // 外风机1错误码2
    {270, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 2} ,       // 外风机1错误码3
    {271, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 3} ,       // 外风机1错误码4
    {272, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_SPEED_PERCENT + 1},   // 外风机2转速PER
    {273, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_SPEED + 1},           // 外风机2转速PRM
    {274, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_POWER + 1},           // 外风机2功率
    {275, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_CURR + 1},            // 外风机2电流
    {276, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 4} ,       // 外风机2错误码1
    {277, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 5} ,       // 外风机2错误码2
    {278, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 6} ,       // 外风机2错误码3
    {279, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 7} ,       // 外风机2错误码4
    {280, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_SPEED_PERCENT + 2},   // 外风机3转速PER
    {281, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_SPEED + 2},           // 外风机3转速PRM
    {282, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_POWER + 2},           // 外风机3功率
    {283, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_CURR + 2},            // 外风机3电流
    {284, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 8} ,       // 外风机3错误码1
    {285, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 9} ,       // 外风机3错误码2
    {286, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 10} ,      // 外风机3错误码3
    {287, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 11} ,      // 外风机3错误码4
    {288, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_SPEED_PERCENT + 3},   // 外风机4转速PER
    {289, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_SPEED + 3},           // 外风机4转速PRM
    {290, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_POWER + 3},           // 外风机4功率
    {291, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_CURR + 3},            // 外风机4电流
    {292, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 12} ,      // 外风机4错误码1
    {293, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 13} ,      // 外风机4错误码2
    {294, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 14} ,      // 外风机4错误码3
    {295, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 15} ,      // 外风机4错误码4
    {296, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_SPEED_PERCENT + 4},    // 外风机5转速PER
    {297, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_SPEED + 4},            // 外风机5转速PRM
    {298, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_POWER + 4},            // 外风机5功率
    {299, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_CURR + 4},             // 外风机5电流
    {300, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 16} ,       // 外风机5错误码1
    {301, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 17} ,       // 外风机5错误码2
    {302, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 18} ,       // 外风机5错误码3
    {303, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 19} ,       // 外风机5错误码4
    {304, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_SPEED_PERCENT + 5},    // 外风机6转速PER
    {305, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_SPEED + 5},            // 外风机6转速PRM
    {306, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_POWER + 5},            // 外风机6功率
    {307, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_CURR + 5},             // 外风机6电流
    {308, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 20} ,       // 外风机6错误码1
    {309, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 21} ,       // 外风机6错误码2
    {310, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 22} ,       // 外风机6错误码3
    {311, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 23} ,       // 外风机6错误码4
    {312, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_SPEED_PERCENT + 6},    // 外风机7转速PER
    {313, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_SPEED + 6},            // 外风机7转速PRM
    {314, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_POWER + 6},            // 外风机7功率
    {315, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_CURR + 6},             // 外风机7电流
    {316, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 24} ,       // 外风机7错误码1
    {317, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 25} ,       // 外风机7错误码2
    {318, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 26} ,       // 外风机7错误码3
    {319, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 27} ,       // 外风机7错误码4
    {320, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_SPEED_PERCENT + 7},    // 外风机8转速PER
    {321, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_SPEED + 7},            // 外风机8转速PRM
    {322, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_POWER + 7},            // 外风机8功率
    {323, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_CURR + 7},             // 外风机8电流
    {324, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 28} ,       // 外风机8错误码1
    {325, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 29} ,       // 外风机8错误码2
    {326, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 30} ,       // 外风机8错误码3
    {327, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 31} ,       // 外风机8错误码4



    {328, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_COMPRESSOR_RUN_FREQUENCY},                    // 压缩机1运行频率
    {329, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_COMPRESSOR_OUTPUT_FREQUENCY},                 // 压缩机1输出频率
    {330, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_COMPRESSOR_RUN_SPEED},                        // 压缩机1运行转速
    {331, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_FREQUENCY_CONVERTER_BOARD_CURR},              // 变频板1电流
    {332, 0, TYPE_FLOAT,  TYPE_INT16S, 1, 2, TYPE_ANA, DXCB_DATA_ID_FREQUENCY_CONVERTER_BOARD_TEMPERATURE},       // 变频板1温度
    {333, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_FREQUENCY_CONVERTER_ERR_CODE} ,               // 变频器1故障码1
    {334, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_FREQUENCY_CONVERTER_ERR_CODE + 1},            // 变频器1故障码2
    {335, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_FREQUENCY_CONVERTER_ERR_CODE + 2},            // 变频器1故障码3
    {336, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_FREQUENCY_CONVERTER_ERR_CODE + 3},            // 变频器1故障码4
    {337, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_COMPRESSOR_RUN_FREQUENCY + 1},                // 压缩机2运行频率
    {338, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_COMPRESSOR_OUTPUT_FREQUENCY + 1},             // 压缩机2输出频率
    {339, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_COMPRESSOR_RUN_SPEED + 1},                    // 压缩机2运行转速
    {340, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_FREQUENCY_CONVERTER_BOARD_CURR + 1},          // 变频板2电流
    {341, 0, TYPE_FLOAT,  TYPE_INT16S, 1, 2, TYPE_ANA, DXCB_DATA_ID_FREQUENCY_CONVERTER_BOARD_TEMPERATURE + 1},   // 变频板2温度
    {342, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_FREQUENCY_CONVERTER_ERR_CODE + 4} ,           // 变频器2故障码1
    {343, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_FREQUENCY_CONVERTER_ERR_CODE + 5},            // 变频器2故障码2
    {344, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_FREQUENCY_CONVERTER_ERR_CODE + 6},            // 变频器2故障码3
    {345, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_FREQUENCY_CONVERTER_ERR_CODE + 7},            // 变频器2故障码4
    {346, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_COMPRESSOR_RUN_FREQUENCY + 2},                // 压缩机3运行频率
    {347, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_COMPRESSOR_OUTPUT_FREQUENCY + 2},             // 压缩机3输出频率
    {348, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_COMPRESSOR_RUN_SPEED + 2},                    // 压缩机3运行转速
    {349, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_FREQUENCY_CONVERTER_BOARD_CURR + 2},          // 变频板3电流
    {350, 0, TYPE_FLOAT,  TYPE_INT16S, 1, 2, TYPE_ANA, DXCB_DATA_ID_FREQUENCY_CONVERTER_BOARD_TEMPERATURE + 2},   // 变频板3温度
    {351, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_FREQUENCY_CONVERTER_ERR_CODE + 8} ,           // 变频器3故障码1
    {352, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_FREQUENCY_CONVERTER_ERR_CODE + 9},            // 变频器3故障码2
    {353, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_FREQUENCY_CONVERTER_ERR_CODE + 10},           // 变频器3故障码3
    {354, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_FREQUENCY_CONVERTER_ERR_CODE + 11},           // 变频器3故障码4
    {355, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_COMPRESSOR_RUN_FREQUENCY + 3},                // 压缩机4运行频率
    {356, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_COMPRESSOR_OUTPUT_FREQUENCY + 3},             // 压缩机4输出频率
    {357, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_COMPRESSOR_RUN_SPEED + 3},                    // 压缩机4运行转速
    {358, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_FREQUENCY_CONVERTER_BOARD_CURR + 3},          // 变频板4电流
    {359, 0, TYPE_FLOAT,  TYPE_INT16S, 1, 2, TYPE_ANA, DXCB_DATA_ID_FREQUENCY_CONVERTER_BOARD_TEMPERATURE + 3},   // 变频板4温度
    {360, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_FREQUENCY_CONVERTER_ERR_CODE + 12} ,          // 变频器4故障码1
    {361, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_FREQUENCY_CONVERTER_ERR_CODE + 13},           // 变频器4故障码2
    {362, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_FREQUENCY_CONVERTER_ERR_CODE + 14},           // 变频器4故障码3
    {363, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_FREQUENCY_CONVERTER_ERR_CODE + 15},           // 变频器4故障码4


    {364, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_COMPRESSOR_PROTECT_CODE},        // 压缩机1保护码
    {365, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_COMPRESSOR_PROTECT_CODE + 1} ,   // 压缩机2保护码
    {366, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_COMPRESSOR_PROTECT_CODE + 2} ,   // 压缩机3保护码
    {367, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_COMPRESSOR_PROTECT_CODE + 3} ,   // 压缩机4保护码
    {368, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_PARA_CRC},                       // 参数CRC1
    {369, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_PARA_CRC + 1},                   // 参数CRC2
    {370, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_PARA_CRC + 2},                   // 参数CRC3

    {400,  0, TYPE_STRING, TYPE_STRING, 0, 16, TYPE_ANA, DXCB_DATA_ID_SOFTWARE_NAME},                // 软件名称
    {408,  0, TYPE_STRING, TYPE_STRING, 0, 16, TYPE_ANA, DXCB_DATA_ID_SOFTWARE_VERSION},             // 软件版本
    {416,  0, TYPE_STRING, TYPE_STRING, 0, 16, TYPE_ANA, DXCB_DATA_ID_SOFTWARE_DATE},                // 软件日期
    {424,  0, TYPE_STRING, TYPE_STRING, 0, 20, TYPE_PARA, DXCB_PARA_ID_HARDWARE_VERSION_OFFSET},     // 硬件版本号
    {434,  0, TYPE_STRING, TYPE_STRING, 0, 16, TYPE_ANA, DXCB_DATA_ID_BOOT_SOFTWARE_VERSION},        // BOOT软件版本
    {442,  0, TYPE_STRING, TYPE_STRING, 0, 16, TYPE_ANA, DXCB_DATA_ID_BOOT_SOFTWARE_DATE},           // BOOT软件日期
    {450,  0, TYPE_STRING, TYPE_STRING, 0, 16, TYPE_PARA, DXCB_PARA_ID_SERIAL_NUMBER_OFFSET},        // 序列号

    /****************************************模拟量支持V3.1*******************************************************/
    {600, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INPUT_DRY_POINT},                    // DI1-高压开关
    {601, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INPUT_DRY_POINT + 1},                // DI2-低压开关
    {602, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INPUT_DRY_POINT + 4},                // DI5-水泵过载反馈
    {603, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INPUT_DRY_POINT + 5},                // DI6-水泵状态反馈
    {604, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INPUT_DRY_POINT + 6},                // DI7-排空电动阀反馈
    {605, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INPUT_DRY_POINT + 8},                // DI9-烟感反馈
    {606, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DCXB_TRACE_ID_FAN_COMM_FAIL_ALARM},               // 内风机1通信状态
    {607, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DCXB_TRACE_ID_FAN_COMM_FAIL_ALARM + 1},           // 内风机2通信状态
    {608, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DCXB_TRACE_ID_FAN_COMM_FAIL_ALARM + 8},           // 外风机1通信状态
    {609, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DCXB_TRACE_ID_FAN_COMM_FAIL_ALARM + 9},           // 外风机2通信状态
    {610, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_ANA, DCXB_TRACE_ID_VFD_COMM_FAIL_ALARM},               // 变频器1通信状态
    {611, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_AI_SIGNAL},                   // AI1，冷凝压力
    {612, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 1},               // AI2，蒸发压力
    {613, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 8},               // AI9，循环水压
    {614, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 10},              // AI11，室内送风温度1
    {615, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 11},              // AI12，室内送风湿度1
    {616, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 14},              // AI15，室内送风温度2
    {617, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_AI_SIGNAL + 15},              // AI16，室内送风湿度2
    {618, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL},                  // NTC1，排气温度
    {619, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 1},              // NTC2，吸气温度
    {620, 0, TYPE_FLOAT, TYPE_INT16U, 3, 2, TYPE_ANA, DXCB_DATA_ID_SAMPLE_NTC_SIGNAL + 2},              // NTC3，液管温度
    {621, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EEV_FEEDBACK_STEP} ,                // 电子膨胀阀1反馈步数
    {622, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EEV_FEEDBACK_STEP + 1} ,            // 电子膨胀阀2反馈步数
    {623, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_SPEED_PERCENT},        // 内风机1转速PER
    {624, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_SPEED},                // 内风机1转速PRM
    {625, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_POWER},                // 内风机1功率
    {626, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_CURR},                 // 内风机1电流
    {627, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE} ,            // 内风机1错误码1
    {628, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 1} ,        // 内风机1错误码2
    {629, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 2} ,        // 内风机1错误码3
    {630, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 3} ,        // 内风机1错误码4
    {631, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_SPEED_PERCENT + 1},    // 内风机2转速PER
    {632, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_SPEED + 1},            // 内风机2转速PRM
    {633, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_POWER + 1},            // 内风机2功率
    {634, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_CURR + 1},             // 内风机2电流
    {635, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 4} ,        // 内风机2错误码1
    {636, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 5} ,        // 内风机2错误码2
    {637, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 6} ,        // 内风机2错误码3
    {638, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_ERR_CODE + 7} ,        // 内风机2错误码4

    {639, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_SPEED_PERCENT},        // 外风机1转速PER
    {640, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_SPEED},                // 外风机1转速PRM
    {641, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_POWER},                // 外风机1功率
    {642, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_CURR},                 // 外风机1电流
    {643, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE} ,            // 外风机1错误码1
    {644, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 1} ,        // 外风机1错误码2
    {645, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 2} ,        // 外风机1错误码3
    {646, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 3} ,        // 外风机1错误码4
    {647, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_SPEED_PERCENT + 1},    // 外风机2转速PER
    {648, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_SPEED + 1},            // 外风机2转速PRM
    {649, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_POWER + 1},            // 外风机2功率
    {650, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_CURR + 1},             // 外风机2电流
    {651, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 4} ,        // 外风机2错误码1
    {652, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 5} ,        // 外风机2错误码2
    {653, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 6} ,        // 外风机2错误码3
    {654, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EXTERNAL_FAN_ERR_CODE + 7} ,        // 外风机2错误码4
    {655, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_COMPRESSOR_RUN_FREQUENCY},                    // 压缩机1运行频率
    {656, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_COMPRESSOR_OUTPUT_FREQUENCY},                 // 压缩机1输出频率
    {657, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_COMPRESSOR_RUN_SPEED},                        // 压缩机1运行转速
    {658, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_FREQUENCY_CONVERTER_BOARD_CURR},              // 变频板1电流
    {659, 0, TYPE_FLOAT,  TYPE_INT16S, 1, 2, TYPE_ANA, DXCB_DATA_ID_FREQUENCY_CONVERTER_BOARD_TEMPERATURE},       // 变频板1温度
    {660, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_FREQUENCY_CONVERTER_ERR_CODE} ,               // 变频器1故障码1
    {661, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_FREQUENCY_CONVERTER_ERR_CODE + 1},            // 变频器1故障码2
    {662, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_FREQUENCY_CONVERTER_ERR_CODE + 2},            // 变频器1故障码3
    {663, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_FREQUENCY_CONVERTER_ERR_CODE + 3},            // 变频器1故障码4
    {664, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_COMPRESSOR_PROTECT_CODE},                     // 压缩机1保护码
    {665, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_PARA_CRC},                                    // 参数CRC1
    {666, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_PARA_CRC + 1},                                // 参数CRC2
    {667, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_PARA_CRC + 2},                                // 参数CRC3
    {668, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_SPEED_VALUE},                    // 内风机1转速设定值
    {669, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_SPEED_VALUE + 1},                // 内风机2转速设定值
    {670, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_OUT_FAN_SPEED_VALUE},                         // 外风机1转速设定值
    {671, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_OUT_FAN_SPEED_VALUE + 1},                     // 外风机2转速设定值
    {672, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_COMPRESSOR_OUT_FREQ_VALUE},                   // 压缩机1输出频率设定值
    {673, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_COMPRESSOR_SWITCH_VALUE},                     // 压缩机1开关设定值
    {674, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_ELECTRONIC_EXPANSION_OUT_STEP},               // 电子膨胀阀输出步数1
    {675, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_ELECTRONIC_EXPANSION_OUT_STEP + 1},           // 电子膨胀阀输出步数2
    {676, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_DO_CTRL + 7},                                 // DO8-水泵控制
    {677, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_DO_CTRL + 8},                                 // DO9-加热带控制
    {678, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_DO_CTRL + 9},                                 // DO10-水管加热带控制
    {679, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_DO_CTRL + 11},                                // DO12-水泵排空阀控制



    /***************************************参数量***************************************************************/

    {1000, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_DEV_ADDR_OFFSET},               // 设备地址
    {1001, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_BAUD_RATE_OFFSET},              // 波特率
    {1002, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_PARITY_BIT_OFFSET},             // 奇偶校验位设置

    {1100, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_FAN_BRAND_OFFSET},                               // 内风机品牌
    {1101, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_FAN_BRAND_OFFSET + 1},                           // 外风机品牌
    {1102, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_FREQUENCY_CONVERTER_BRAND_OFFSET},               // 变频器品牌
    {1103, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_COMM_INTERRUPT_JUDGE_DELAY_OFFSET},              // 通讯断判断延时
    {1104, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_OFFLINE_PROCESS_STRATEGY_OFFSET},                // 离线处理策略
    {1105, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_NORMAL_COMM_PROTECT_STRATEGY_OFFSET},            // 通讯正常保护处理策略
    {1106, 0, TYPE_INT8U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_HIGH_PRESSURE_SENSOR_MODE_OFFSET},               // 高压压力传感器型号
    {1107, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_INTERNAL_FAN_MAX_SPEED_OFFSET},                  // 内风机最大转速
    {1108, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_EXTERNAL_FAN_MAX_SPEED_OFFSET},                  // 外风机最大转速
    {1109, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_INTERNAL_FAN_NUM_OFFSET},                        // 内风机数量
    {1110, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_EXTERNAL_FAN_NUM_OFFSET},                        // 外风机数量
    {1111, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_PUMPS_NUM_OFFSET},                               // 水泵数量
    {1112, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_COMPRESSORS_NUM_OFFSET},                         // 压缩机数量
    {1113, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_PUMPS_ACTION_DEFAULT_OFFSET},                    // 水泵动作默认值
    {1114, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_HEAT_BELT_ACTION_DEFAULT_OFFSET},                // 加热带动作默认值
    {1115, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_WATER_PIPE_HEAT_BELT_ACTION_DEFAULT_OFFSET},     // 水管加热带动作默认值
    {1116, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_PUMP_EMPTY_VALVE_ACTION_DEFAULT_OFFSET},         // 水泵排空阀动作默认值
    {1117, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_FAN_BAUD_RATE_OFFSET},                           // 风机波特率
    {1118, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_FAN_PARITY_BIT_OFFSET},                          // 风机奇偶校验位
    {1119, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_VFD_BAUD_RATE_OFFSET},                           // 变频器波特率
    {1120, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_VFD_PARITY_BIT_OFFSET},                          // 变频器奇偶校验位
    {1121, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_DFS_START_SPEED_OFFSET},                         // 丹佛斯启动转速
    {1122, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_DFS_START_DURATION_OFFSET},                      // 丹佛斯启动转速维持时间
    {1123, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_VFD_FIRST_ACCELERATE_TIME_OFFSET},               // 变频器第一加速时间

    {1200, 0, TYPE_FLOAT,  TYPE_INT16S, 1, 2, TYPE_PARA, DXCB_PARA_ID_EXHAUST_TEMP_HIGH_ALARM_THRED_OFFSET},                   // 排气温度高告警阈值
    {1201, 0, TYPE_FLOAT,  TYPE_INT16S, 1, 2, TYPE_PARA, DXCB_PARA_ID_EXHAUST_TEMP_HIGH_RECOVER_THRED_OFFSET},                 // 排气温度高恢复阈值
    {1202, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_EXHAUST_TEMP_HIGH_ALARM_GENERATE_DELAY_OFFSET},          // 排气温度高告警产生延时
    {1203, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_EXHAUST_TEMP_HIGH_ALARM_RECOVER_DELAY_OFFSET},           // 排气温度高告警恢复延时
    {1204, 0, TYPE_FLOAT,  TYPE_INT16S, 1, 2, TYPE_PARA, DXCB_PARA_ID_EVAPORAT_PRESSURE_LOW_ALARM_THRED_OFFSET},               // 蒸发压力低告警阈值
    {1205, 0, TYPE_FLOAT,  TYPE_INT16S, 1, 2, TYPE_PARA, DXCB_PARA_ID_EVAPORAT_PRESSURE_LOW_RECOVER_THRED_OFFSET},             // 蒸发压力低恢复阈值
    {1206, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_EVAPORAT_PRESSURE_LOW_ALARM_GENERATE_DELAY_OFFSET},      // 蒸发压力低告警产生延时
    {1207, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_EVAPORAT_PRESSURE_LOW_ALARM_RECOVER_DELAY_OFFSET},       // 蒸发压力低告警恢复延时
    {1208, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_PARA, DXCB_PARA_ID_CONDEN_PRESSURE_HIGH_ALARM_THRED_OFFSET},                // 冷凝压力高告警阈值
    {1209, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_PARA, DXCB_PARA_ID_CONDEN_PRESSURE_HIGH_RECOVER_THRED_OFFSET},              // 冷凝压力高恢复阈值
    {1210, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_CONDEN_PRESSURE_HIGH_ALARM_GENERATE_DELAY_OFFSET},       // 冷凝压力高告警产生延时
    {1211, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_CONDEN_PRESSURE_HIGH_ALARM_RECOVER_DELAY_OFFSET},        // 冷凝压力高告警恢复延时
    {1212, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_PARA, DXCB_PARA_ID_FREQ_CURR_HIGH_ALARM_THRED_OFFSET},                      // 变频器电流高告警阈值
    {1213, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_PARA, DXCB_PARA_ID_FREQ_CURR_HIGH_RECOVER_THRED_OFFSET},                    // 变频器电流高恢复阈值
    {1214, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_FREQ_CURR_HIGH_ALARM_GENERATE_DELAY_OFFSET},             // 变频器电流高告警产生延时
    {1215, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_FREQ_CURR_HIGH_ALARM_RECOVER_DELAY_OFFSET},              // 变频器电流高告警恢复延时
    {1216, 0, TYPE_FLOAT,  TYPE_INT16S, 1, 2, TYPE_PARA, DXCB_PARA_ID_FREQ_TEMP_HIGH_ALARM_THRED_OFFSET},                      // 变频器温度高告警阈值
    {1217, 0, TYPE_FLOAT,  TYPE_INT16S, 1, 2, TYPE_PARA, DXCB_PARA_ID_FREQ_TEMP_HIGH_RECOVER_THRED_OFFSET},                    // 变频器温度高恢复阈值
    {1218, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_FREQ_TEMP_HIGH_ALARM_GENERATE_DELAY_OFFSET},             // 变频器温度高告警产生延时
    {1219, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_FREQ_TEMP_HIGH_ALARM_RECOVER_DELAY_OFFSET},              // 变频器温度高告警恢复延时
    {1220, 0, TYPE_FLOAT,  TYPE_INT16S, 1, 2, TYPE_PARA, DXCB_PARA_ID_EXHAUST_OVERHEAT_LOW_ALARM_THRED_OFFSET},                // 排气过热度低告警阈值
    {1221, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_EXHAUST_OVERHEAT_LOW_ALARM_GENERATE_DELAY_OFFSET},       // 排气过热度低告警产生延时
    {1222, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_EXHAUST_OVERHEAT_LOW_ALARM_RECOVER_DELAY_OFFSET},        // 排气过热度低告警恢复延时
    {1223, 0, TYPE_FLOAT,  TYPE_INT16S, 1, 2, TYPE_PARA, DXCB_PARA_ID_INHALE_OVERHEAT_LOW_ALARM_THRED_OFFSET},                 // 吸气过热度低告警阈值
    {1224, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_INHALE_OVERHEAT_LOW_ALARM_GENERATE_DELAY_OFFSET},        // 吸气过热度低告警产生延时
    {1225, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_INHALE_OVERHEAT_LOW_ALARM_RECOVER_DELAY_OFFSET},         // 吸气过热度低告警恢复延时
    {1226, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_HIGH_VOL_SWITCH_DISCONN_ALARM_GENERATE_DELAY_OFFSET},    // 高压开关断开告警产生延时
    {1227, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_HIGH_VOL_SWITCH_DISCONN_ALARM_RECOVER_DELAY_OFFSET},     // 高压开关断开告警恢复延时
    {1228, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_LOW_VOL_SWITCH_DISCONN_ALARM_GENERATE_DELAY_OFFSET},     // 低压开关断开告警产生延时
    {1229, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_LOW_VOL_SWITCH_DISCONN_ALARM_RECOVER_DELAY_OFFSET},      // 低压开关断开告警恢复延时
    {1230, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_PARA, DXCB_PARA_ID_CONDEN_PRESS_SENSOR_FAULT_ALARM_HIGH_THRED_OFFSET},      // 冷凝压力传感器故障告警上限阈值
    {1231, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_PARA, DXCB_PARA_ID_CONDEN_PRESS_SENSOR_FAULT_ALARM_LOW_THRED_OFFSET},       // 冷凝压力传感器故障告警下限阈值
    {1232, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_CONDEN_PRESS_SENSOR_FAULT_ALARM_GENERATE_DELAY_OFFSET},  // 冷凝压力传感器故障告警产生延时
    {1233, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_CONDEN_PRESS_SENSOR_FAULT_ALARM_RECOVER_DELAY_OFFSET},   // 冷凝压力传感器故障告警恢复延时
    {1234, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_PARA, DXCB_PARA_ID_EVA_PRESS_SENSOR_FAULT_ALARM_HIGH_THRED_OFFSET},         // 蒸发压力传感器故障告警上限阈值
    {1235, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_PARA, DXCB_PARA_ID_EVA_PRESS_SENSOR_FAULT_ALARM_LOW_THRED_OFFSET},          // 蒸发压力传感器故障告警下限阈值
    {1236, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_EVA_PRESS_SENSOR_FAULT_ALARM_GENERATE_DELAY_OFFSET},     // 蒸发压力传感器故障告警产生延时
    {1237, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_EVA_PRESS_SENSOR_FAULT_ALARM_RECOVER_DELAY_OFFSET},      // 蒸发压力传感器故障告警恢复延时
    {1238, 0, TYPE_FLOAT,  TYPE_INT16S, 1, 2, TYPE_PARA, DXCB_PARA_ID_INHALE_TEMP_SENSOR_FAULT_ALARM_HIGH_THRED_OFFSET},       // 吸气温度传感器故障告警上限阈值
    {1239, 0, TYPE_FLOAT,  TYPE_INT16S, 1, 2, TYPE_PARA, DXCB_PARA_ID_INHALE_TEMP_SENSOR_FAULT_ALARM_LOW_THRED_OFFSET},        // 吸气温度传感器故障告警下限阈值
    {1240, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_INHALE_TEMP_SENSOR_FAULT_ALARM_GENERATE_DELAY_OFFSET},   // 吸气温度传感器故障告警产生延时
    {1241, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_INHALE_TEMP_SENSOR_FAULT_ALARM_RECOVER_DELAY_OFFSET},    // 吸气温度传感器故障告警恢复延时
    {1242, 0, TYPE_FLOAT,  TYPE_INT16S, 1, 2, TYPE_PARA, DXCB_PARA_ID_EXHAUST_TEMP_SENSOR_FAULT_ALARM_HIGH_THRED_OFFSET},      // 排气温度传感器故障告警上限阈值
    {1243, 0, TYPE_FLOAT,  TYPE_INT16S, 1, 2, TYPE_PARA, DXCB_PARA_ID_EXHAUST_TEMP_SENSOR_FAULT_ALARM_LOW_THRED_OFFSET},       // 排气温度传感器故障告警下限阈值
    {1244, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_EXHAUST_TEMP_SENSOR_FAULT_ALARM_GENERATE_DELAY_OFFSET},  // 排气温度传感器故障告警产生延时
    {1245, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_EXHAUST_TEMP_SENSOR_FAULT_ALARM_RECOVER_DELAY_OFFSET},   // 排气温度传感器故障告警恢复延时
    {1246, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_FREQ_CONVERTER_FAULT_ALARM_GENERATE_DELAY_OFFSET},       // 排气温度传感器故障告警产生延时
    {1247, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_FREQ_CONVERTER_FAULT_ALARM_RECOVER_DELAY_OFFSET},        // 排气温度传感器故障告警恢复延时
    {1248, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_DXCB_PROTECT_ALARM_RECOVER_DELAY_OFFSET},                // DXCB保护告警恢复延时



    {1400, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_EXHAUST_TEMP_SAMPLE_CHANNEL_OFFSET},                     // 排气温度1采样通道
    {1401, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_EXHAUST_TEMP_SAMPLE_CHANNEL_OFFSET + 1},                 // 排气温度2采样通道
    {1402, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_EXHAUST_TEMP_SAMPLE_CHANNEL_OFFSET + 2},                 // 排气温度3采样通道
    {1403, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_EXHAUST_TEMP_SAMPLE_CHANNEL_OFFSET + 3},                 // 排气温度4采样通道
    {1404, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_INHALE_TEMP_SAMPLE_CHANNEL_OFFSET},                      // 吸气温度1采样通道
    {1405, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_INHALE_TEMP_SAMPLE_CHANNEL_OFFSET + 1},                  // 吸气温度2采样通道
    {1406, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_INHALE_TEMP_SAMPLE_CHANNEL_OFFSET + 2},                  // 吸气温度3采样通道
    {1407, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_INHALE_TEMP_SAMPLE_CHANNEL_OFFSET + 3},                  // 吸气温度4采样通道
    {1408, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_CONDEN_PRESSURE_SAMPLE_CHANNEL_OFFSET},                  // 冷凝压力1采样通道
    {1409, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_CONDEN_PRESSURE_SAMPLE_CHANNEL_OFFSET + 1},              // 冷凝压力2采样通道
    {1410, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_CONDEN_PRESSURE_SAMPLE_CHANNEL_OFFSET + 2},              // 冷凝压力3采样通道
    {1411, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_CONDEN_PRESSURE_SAMPLE_CHANNEL_OFFSET + 3},              // 冷凝压力4采样通道
    {1412, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_EVAPORATION_PRESSURE_SAMPLE_CHANNEL_OFFSET},             // 蒸发压力1采样通道
    {1413, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_EVAPORATION_PRESSURE_SAMPLE_CHANNEL_OFFSET + 1},         // 蒸发压力2采样通道
    {1414, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_EVAPORATION_PRESSURE_SAMPLE_CHANNEL_OFFSET + 2},         // 蒸发压力3采样通道
    {1415, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_EVAPORATION_PRESSURE_SAMPLE_CHANNEL_OFFSET + 3},         // 蒸发压力4采样通道
    {1416, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_HIGH_TENSION_SWITCH_SAMPLE_CHANNEL_OFFSET},              // 高压开关1采样通道
    {1417, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_HIGH_TENSION_SWITCH_SAMPLE_CHANNEL_OFFSET + 1},          // 高压开关2采样通道
    {1418, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_HIGH_TENSION_SWITCH_SAMPLE_CHANNEL_OFFSET + 2},          // 高压开关3采样通道
    {1419, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_HIGH_TENSION_SWITCH_SAMPLE_CHANNEL_OFFSET + 3},          // 高压开关4采样通道
    {1420, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_LOW_TENSION_SWITCH_SAMPLE_CHANNEL_OFFSET},               // 低压开关1采样通道
    {1421, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_LOW_TENSION_SWITCH_SAMPLE_CHANNEL_OFFSET + 1},           // 低压开关2采样通道
    {1422, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_LOW_TENSION_SWITCH_SAMPLE_CHANNEL_OFFSET + 2},           // 低压开关3采样通道
    {1423, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_LOW_TENSION_SWITCH_SAMPLE_CHANNEL_OFFSET + 3},           // 低压开关4采样通道

    {1600, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_SPEED_VALUE},                        // 内风机1转速设定值
    {1601, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_SPEED_VALUE + 1},                    // 内风机2转速设定值
    {1602, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_SPEED_VALUE + 2},                    // 内风机3转速设定值
    {1603, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_SPEED_VALUE + 3},                    // 内风机4转速设定值
    {1604, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_SPEED_VALUE + 4},                    // 内风机5转速设定值
    {1605, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_SPEED_VALUE + 5},                    // 内风机6转速设定值
    {1606, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_SPEED_VALUE + 6},                    // 内风机7转速设定值
    {1607, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_INTERNAL_FAN_SPEED_VALUE + 7},                    // 内风机8转速设定值
    {1608, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_OUT_FAN_SPEED_VALUE},                             // 外风机1转速设定值
    {1609, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_OUT_FAN_SPEED_VALUE + 1},                         // 外风机2转速设定值
    {1610, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_OUT_FAN_SPEED_VALUE + 2},                         // 外风机3转速设定值
    {1611, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_OUT_FAN_SPEED_VALUE + 3},                         // 外风机4转速设定值
    {1612, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_OUT_FAN_SPEED_VALUE + 4},                         // 外风机5转速设定值
    {1613, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_OUT_FAN_SPEED_VALUE + 5},                         // 外风机6转速设定值
    {1614, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_OUT_FAN_SPEED_VALUE + 6},                         // 外风机7转速设定值
    {1615, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_OUT_FAN_SPEED_VALUE + 7},                         // 外风机8转速设定值
    {1616, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_COMPRESSOR_OUT_FREQ_VALUE},                       // 压缩机1输出频率设定值
    {1617, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_COMPRESSOR_SWITCH_VALUE},                         // 压缩机1开关设定值
    {1618, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_COMPRESSOR_OUT_FREQ_VALUE + 1},                   // 压缩机2输出频率设定值
    {1619, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_COMPRESSOR_SWITCH_VALUE + 1},                     // 压缩机2开关设定值
    {1620, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_COMPRESSOR_OUT_FREQ_VALUE + 2},                   // 压缩机3输出频率设定值
    {1621, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_COMPRESSOR_SWITCH_VALUE + 2},                     // 压缩机3开关设定值
    {1622, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 2, TYPE_ANA, DXCB_DATA_ID_COMPRESSOR_OUT_FREQ_VALUE + 3},                   // 压缩机4输出频率设定值
    {1623, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_COMPRESSOR_SWITCH_VALUE + 3},                     // 压缩机4开关设定值
    {1624, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_ELECTRONIC_EXPANSION_OUT_STEP},                   // 电子膨胀阀输出步数1
    {1625, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_ELECTRONIC_EXPANSION_OUT_STEP + 1},               // 电子膨胀阀输出步数2
    {1626, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_ELECTRONIC_EXPANSION_OUT_STEP + 2},               // 电子膨胀阀输出步数3
    {1627, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_ELECTRONIC_EXPANSION_OUT_STEP + 3},               // 电子膨胀阀输出步数4
    {1628, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_ELECTRONIC_EXPANSION_OUT_STEP + 4},               // 电子膨胀阀输出步数5
    {1629, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_ELECTRONIC_EXPANSION_OUT_STEP + 5},               // 电子膨胀阀输出步数6
    {1630, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_ELECTRONIC_EXPANSION_OUT_STEP + 6},               // 电子膨胀阀输出步数7
    {1631, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_ELECTRONIC_EXPANSION_OUT_STEP + 7},               // 电子膨胀阀输出步数8
    {1632, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_DO_CTRL},                                         // DO1-预留
    {1633, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_DO_CTRL + 1},                                     // DO2-预留
    {1634, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_DO_CTRL + 2},                                     // DO3-预留
    {1635, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_DO_CTRL + 3},                                     // DO4-预留
    {1636, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_DO_CTRL + 4},                                     // DO5-预留
    {1637, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_DO_CTRL + 5},                                     // DO6-预留
    {1638, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_DO_CTRL + 6},                                     // DO7-预留
    {1639, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_DO_CTRL + 7},                                     // DO8-水泵控制
    {1640, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_DO_CTRL + 8},                                     // DO9-加热带控制
    {1641, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_DO_CTRL + 9},                                     // DO10-水管加热带控制
    {1642, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_DO_CTRL + 10},                                    // DO11-预留
    {1643, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_DO_CTRL + 11},                                    // DO12-水泵排空阀控制
    {1644, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_DO_CTRL + 12},                                    // DO13-预留
    {1645, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_DO_CTRL + 13},                                    // DO14-预留
    {1646, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_DO_CTRL + 14},                                    // DO15-预留
    {1647, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EEV_CTRL},                                        // EEV1-EEV4
    {1648, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_EEV_CTRL + 1},                                    // EEV5-EEV8
    {1649, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DXCB_DATA_ID_DO_CTRL_APPTEST},                                 // apptest控制DO

    {1700, 0, TYPE_STRING, TYPE_STRING, 0, 20, TYPE_PARA, DXCB_PARA_ID_HARDWARE_VERSION_OFFSET},                       // 硬件版本号
    {1710, 0, TYPE_STRING, TYPE_STRING, 0, 16, TYPE_PARA, DXCB_PARA_ID_SERIAL_NUMBER_OFFSET},                          // 序列号

    {ADDR_SET_REG, 0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_PARA, DXCB_PARA_ID_DEV_ADDR_OFFSET},                        // 设备地址


};



static ctrl_cmd_modbus_t s_modbus_ctrl_cmd[] = 
{
    {2000,  DXCB_CTRL_ID_RESET_PARA,          0,     restore_default_para},           // 参数恢复默认值
    {2001,  DXCB_CTRL_ID_UNLOCK_FAULT,        0,     NULL},                           // 故障解除锁定
    {2002,  DXCB_CTRL_ID_RESET_DXCB,          0,     reset_dxcb},                     // DXCB重启复位
    {2003,  DXCB_CTRL_ID_CLEAN_AMS_VFD_ALARM, 0,     clean_ams_hc_vfd_alarm},            // 清除谷轮变频器告警
    {2004,  DXCB_CTRL_ID_CTRL_LED,            0,     ctrl_led},                       // 控制LED灯
    {2005,  DXCB_CTRL_ID_EEPROM_READ_WRITE,   0,     ctrl_eeprom_read_write},         // 控制EEPROM读写
};



dev_type_t* init_dev_north_dxcb(void) {
    return &dev_val_north_dxcb;
}

void check_ana_reserve_flag(int index , void* data, modbus_addr_map_data_t* data_map)
{
    if (data_map[index].reserve_flag != 1)
    {
        get_data_by_id_type(index, data, data_map);
        return;
    }
    return;
}


/*******************************打包函数定义*****************/
int floattoint32u(int* index , unsigned char* data_buff , int* reg_nums , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map  )
{
    float data = 0.0 ;  
    int tmp = 0;
    check_ana_reserve_flag( *index ,&data,data_map);    
    tmp =round(data * percision );
    put_uint32_to_buff(&data_buff[total_len], (unsigned int)(int)(tmp));
    (*index)++;
    (*reg_nums) -= 2;
    return DATA_LEN_4;
}
int floattoint32s(int* index , unsigned char* data_buff, int* reg_nums , int data_len ,  int percision, int total_len, modbus_addr_map_data_t* data_map  )
{
    float data = 0.0 ;  
    int tmp = 0;
    check_ana_reserve_flag( *index ,&data, data_map);    
    tmp =round(data * percision );
    put_int32_to_buff(&data_buff[total_len], (signed int)(tmp));
    (*index)++;
    (*reg_nums) -= 2;
    return DATA_LEN_4;
}
int floattoint16u(int* index , unsigned char* data_buff , int* reg_nums , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map )
{
    float data = 0.0 ;  
    int tmp = 0;
    check_ana_reserve_flag( *index ,&data, data_map);    
    tmp =round(data * percision );
    put_uint16_to_buff(&data_buff[total_len], (unsigned short)(short)(tmp));
    (*index)++;
    (*reg_nums) -= 1;
    return DATA_LEN_2;
}
int floattoint16s(int* index , unsigned char* data_buff , int* reg_nums , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map )
{
    float data = 0.0 ;  
    int tmp = 0;
    check_ana_reserve_flag( *index ,&data, data_map);    
    tmp =round(data * percision );
    put_int16_to_buff(&data_buff[total_len], (signed short)(tmp));
    (*index)++;
    (*reg_nums) -= 1;
    return DATA_LEN_2;
}
int int16utoint16u(int* index , unsigned char* data_buff , int* reg_nums , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map )
{
    unsigned short data = 0;
    check_ana_reserve_flag(*index ,&data, data_map);  
    put_uint16_to_buff(&data_buff[total_len], data * percision);
    (*index)++;
    (*reg_nums) -= 1;
    return DATA_LEN_2;
}

int int16stoint16s(int* index , unsigned char* data_buff , int* reg_nums , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map )
{
    signed short data = 0;
    check_ana_reserve_flag( *index ,&data, data_map);  
    put_int16_to_buff(&data_buff[total_len], data * percision);

    (*index)++;
    (*reg_nums) -= 1;
    return DATA_LEN_2;
}

int int32utoint32u(int* index , unsigned char* data_buff , int* reg_nums, int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map )
{
    unsigned int data = 0;
    check_ana_reserve_flag( *index ,&data, data_map);  

    put_uint32_to_buff(&data_buff[total_len], data * percision );
    (*index)++;
    (*reg_nums) -= 2;
    return DATA_LEN_4;
}

int int32stoint32s(int* index , unsigned char* data_buff , int* reg_nums, int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map )
{
    int data = 0;
    check_ana_reserve_flag( *index ,&data, data_map);  

    put_int32_to_buff(&data_buff[total_len], data * percision );
    (*index)++;
    (*reg_nums) -= 2;
    return DATA_LEN_4;
}

int stringtostring(int* index , unsigned char* data_buff , int* reg_nums , int data_len,  int percision  , int total_len, modbus_addr_map_data_t* data_map )
{
    unsigned char data[64]={0};
    check_ana_reserve_flag( *index ,&data, data_map);  
    rt_memcpy_s(&data_buff[total_len], data_len, data, data_len);
    (*reg_nums) -= data_len / 2;
    (*index)++;
    return data_len;
}
int datetoint16u(int* index, unsigned char* data_buff, int* reg_nums, int data_len, int percision, int total_len, modbus_addr_map_data_t* data_map)
{
    date_base_t data = {0};
    get_one_data(data_map[*index].data_addr, &data);
    put_uint16_to_buff(&data_buff[total_len], data.year);
    put_uint16_to_buff(&data_buff[total_len+2], data.month);
    put_uint16_to_buff(&data_buff[total_len+4], data.day);
    (*index) += 3;
    (*reg_nums) -= 3;
    return DATA_LEN_6;
}



int int8utoint16u(int* index, unsigned char* data_buff, int* reg_nums, int data_len, int percision, int total_len, modbus_addr_map_data_t* data_map) 
{
    unsigned char data = 0;
    check_ana_reserve_flag(*index, &data, data_map);  
    put_uint16_to_buff(&data_buff[total_len], data * percision);
    (*index)++;
    (*reg_nums)--;
    return DATA_LEN_2;
}



void numchar_to_bit_to_int16(unsigned char* data_buff, int total_len, int* index, int percision, modbus_addr_map_data_t* data_map)
{
    unsigned short data = 0;
    unsigned char bit[16] = {0};
    unsigned int result = 0;
    int i = 0;
    while( i < 16 )
    {
        if (data_map[*index].reserve_flag == 1)
        {
            i += data_map[*index].data_len;
            //rt_kprintf(" addr:%x | value: 0 \n",g_modbus_data_map[*index].register_addr);
            
        }
        else
        {
            get_data_by_id_type( *index, &result, data_map);
            //rt_kprintf(" addr:%x | value: %d \n",g_modbus_data_map[*index].register_addr,result);
            bit[i] = result & 0x01;
            i++;
        }
        (*index)++;
    }
    for (int i = 15; i >= 0; i--)
    {
        data += bit[i] << i;
    }
    put_int16_to_buff(&data_buff[total_len], data);
    //rt_kprintf("-------------\n");
}



int chartobit(int* index, unsigned char* data_buff, int* reg_nums, int data_len, int percision, int total_len, modbus_addr_map_data_t* data_map)
{
    numchar_to_bit_to_int16(data_buff, total_len, index, percision, data_map);
    (*reg_nums) -= 1;
    return DATA_LEN_2;
}



/*********************************解包函数定义***********************************************/
int parse_int16utofloat( int* index , unsigned char* data_buff , int* data_valude_index ,int percision ,int* reg_nums, modbus_addr_map_data_t* data_map)
{
    unsigned short data = 0;
    float result = 0.0, data_now = 0.0; 
    data = get_uint16_data(&data_buff[*data_valude_index]);
    result = data*1.0 / percision;
    (*reg_nums) -= 1;
    (*data_valude_index) += 2;
    get_data_by_id_type(*index, &data_now, data_map);
    RETURN_VAL_IF_FAIL(!FLOAT_EQ(result,data_now), SUCCESSFUL);
    return set_data_by_id_type(*index , &result, data_map);
}

int parse_int16stofloat( int* index , unsigned char* data_buff , int* data_valude_index , int percision ,int* reg_nums, modbus_addr_map_data_t* data_map)
{
    signed short data = 0;
    float result = 0.0, data_now = 0.0;
    data = get_int16_data(&data_buff[*data_valude_index]);
    result  = data*1.0 / percision;
    (*reg_nums) -= 1;
    (*data_valude_index) += 2;
    get_data_by_id_type(*index, &data_now, data_map);
    RETURN_VAL_IF_FAIL(!FLOAT_EQ(result,data_now), SUCCESSFUL);
    return set_data_by_id_type(*index , &result, data_map);
}

int parse_int32utofloat( int* index , unsigned char* data_buff , int* data_valude_index ,  int percision ,int* reg_nums, modbus_addr_map_data_t* data_map)
{
    unsigned int data = 0;
    float result = 0.0, data_now = 0.0;
    data = get_ulong_data(&data_buff[*data_valude_index]);
    result = data*1.0 / percision;
    (*reg_nums) -= 2;
    (*data_valude_index) += 4;
    get_data_by_id_type(*index, &data_now, data_map);
    RETURN_VAL_IF_FAIL(!FLOAT_EQ(result,data_now), SUCCESSFUL);
    return set_data_by_id_type(*index , &result, data_map);
}

int parse_int32stofloat( int* index , unsigned char* data_buff , int* data_valude_index , int percision ,int* reg_nums, modbus_addr_map_data_t* data_map)
{
    signed int data = 0;
    float result = 0.0, data_now = 0.0;
    data = get_ulong_data(&data_buff[*data_valude_index]);
    result  = data *1.0 / percision;
    (*reg_nums) -= 2;
    (*data_valude_index) += 4;
    get_data_by_id_type(*index, &data_now, data_map);
    RETURN_VAL_IF_FAIL(!FLOAT_EQ(result,data_now), SUCCESSFUL);
    return set_data_by_id_type(*index , &result, data_map);
}

int parse_int16utoint16u( int* index , unsigned char* data_buff , int* data_valude_index , int percision ,int* reg_nums, modbus_addr_map_data_t* data_map)
{
    unsigned short data = 0;
    unsigned short result = 0, data_now = 0;
    data = get_uint16_data(&data_buff[*data_valude_index]);
    result  = data *1.0 / percision;
    (*reg_nums) -= 1;
    (*data_valude_index) += 2;
    get_data_by_id_type(*index, &data_now, data_map);
    RETURN_VAL_IF_FAIL(result != data_now, SUCCESSFUL);
    return set_data_by_id_type(*index , &result, data_map);
}


int parse_int16utoint8u(int* index, unsigned char* data_buff, int* data_valude_index, int percision, int* reg_nums, modbus_addr_map_data_t* data_map) {
    // Assuming get_uint16_data is a macro or inline function for optimal performance
    unsigned char data_now = 0;
    unsigned short data = get_uint16_data(&data_buff[*data_valude_index]);
    unsigned char result = (unsigned char)(data * 1.0 / percision); // Use integer division

    (*reg_nums) -= 1;
    (*data_valude_index) += 2;

    get_data_by_id_type(*index, &data_now, data_map);
    RETURN_VAL_IF_FAIL(result != data_now, SUCCESSFUL);

    return set_data_by_id_type(*index, &result, data_map);
}


int parse_int32utoint32u( int* index , unsigned char* data_buff , int* data_valude_index ,int percision , int* reg_nums, modbus_addr_map_data_t* data_map)
{
    unsigned int data = 0;
    unsigned int result = 0, data_now = 0;
    data = get_ulong_data(&data_buff[*data_valude_index]);
    result  = data *1.0 / percision;
    (*reg_nums) -= 2;
    (*data_valude_index) += 4;
    get_data_by_id_type(*index, &data_now, data_map);
    RETURN_VAL_IF_FAIL(result != data_now, SUCCESSFUL);
    return set_data_by_id_type(*index , &result, data_map);
}

int parse_stringtostring( int* index , unsigned char* data_buff , int* data_valude_index , int percision , int* reg_nums, modbus_addr_map_data_t* data_map)
{
    unsigned char data[STR_LEN_64] = {0};
    unsigned char data_now[STR_LEN_64] = {0};

    rt_memcpy_s(data, data_map[*index].data_len  , &data_buff[*data_valude_index], data_map[*index].data_len);
    (*reg_nums) -= data_map[*index].data_len/2;
    (*data_valude_index) += data_map[*index].data_len;
    get_data_by_id_type(*index, data_now, data_map);
    RETURN_VAL_IF_FAIL(rt_strncmp((char*)data, (char*)data_now, data_map[*index].data_len) != 0, SUCCESSFUL);
    return set_data_by_id_type(*index , data, data_map);
}

static special_data_scope_t special_data_scope[] = 
{
    {TYPE_INT16U, 1600, 1615, 0, 100},      // 内外风扇转速设定值
    {TYPE_FLOAT,  1616, 1616, 0, 1000},     // 压缩机1输出频率设定值
    {TYPE_INT16U, 1617, 1617, 0, 1},        // 压缩机1开关设定值
    {TYPE_FLOAT,  1618, 1618, 0, 1000},     // 压缩机2输出频率设定值
    {TYPE_INT16U, 1619, 1619, 0, 1},        // 压缩机2开关设定值
    {TYPE_FLOAT,  1620, 1620, 0, 1000},     // 压缩机3输出频率设定值
    {TYPE_INT16U, 1621, 1621, 0, 1},        // 压缩机3开关设定值
    {TYPE_FLOAT,  1622, 1622, 0, 1000},     // 压缩机4输出频率设定值
    {TYPE_INT16U, 1623, 1623, 0, 1},        // 压缩机4开关设定值
    {TYPE_INT16U, 1624, 1631, 0, 480},      // 电子膨胀阀输出步数1-8
    {TYPE_INT16U, 1632, 1646, 0, 1},        // DO设定1-15
    {TYPE_INT16U, 1647, 1648, 0, 65535},    // X32-X35 X36-X39
    {TYPE_INT16U, 1649, 1649, 0, 65535},    // apptest控制DO
};


char check_data_scope_by_type(int index, void* set_data)
{
    switch(special_data_scope[index].data_type)
    {
        case TYPE_INT16U: {
            unsigned short uint_data = *(unsigned short*)set_data;
            return (uint_data < special_data_scope[index].low_limit || uint_data > special_data_scope[index].high_limit) ? FAILURE : SUCCESSFUL;
        }
        case TYPE_FLOAT: {
            float float_data = *(float*)set_data;
            return (float_data < special_data_scope[index].low_limit || float_data > special_data_scope[index].high_limit) ? FAILURE : SUCCESSFUL;
        }
        case TYPE_INT16S: {
            short int_data = *(short*)set_data;
            return (int_data < special_data_scope[index].low_limit || int_data > special_data_scope[index].high_limit) ? FAILURE : SUCCESSFUL;
        }
        default:
            return SUCCESSFUL;
    }
}

char special_data_judge_scope(int index, void* data, modbus_addr_map_data_t* data_map)
{
    RETURN_VAL_IF_FAIL(data_map[index].register_addr >= 1600 && data_map[index].register_addr <= 1648, SUCCESSFUL);

    for(int i = 0; i < sizeof(special_data_scope)/sizeof(special_data_scope_t); i++)
    {
        if(data_map[index].register_addr >= special_data_scope[i].start_reg_addr && 
        data_map[index].register_addr <= special_data_scope[i].end_reg_addr)
        {
            return check_data_scope_by_type(i, data);
        }
    }
    return SUCCESSFUL;
}

char set_data_by_id_type(int index, void* data, modbus_addr_map_data_t* data_map)
{
    unsigned char id_type = data_map[index].sid_type;
    if (id_type == TYPE_ANA)
    {
        if(special_data_judge_scope(index, data, data_map) != SUCCESSFUL)
        {
            return FAILURE;
        }
        if (set_one_data(data_map[index].data_addr, data) == FAILURE)
        {
            return FAILURE;
        }
    }
    else if(id_type == TYPE_PARA)
    {
        if (set_one_para(data_map[index].data_addr, data, FALSE, FALSE) < 0)
        {
            return FAILURE;
        }
    }
    modbusrtn_var_t* modbusrtn_var = get_modbusrtn_var();
    modbusrtn_var->is_para_change = TRUE;
    return SUCCESSFUL;
}


void get_data_by_id_type(int index , void* data, modbus_addr_map_data_t* data_map)
{   
    unsigned char id_type = data_map[index].sid_type;
    modbusrtn_var_t* modbusrtn_var = get_modbusrtn_var();
    // rt_kprintf("get_data_by_id_type|thread_name:%s, dev_offset:%d\n", modbusrtn_var->name, modbusrtn_var->dev_offset);
    data_map->cur_dev_offset = modbusrtn_var->dev_offset;

    //rt_kprintf("get_data_by_id_type|data_addr:0x%x, dev_offset:%d\n", data_map[index].data_addr, data_map->cur_dev_offset);

    if(id_type == TYPE_ANA )
    {
        get_one_data( data_map[index].data_addr + data_map->cur_dev_offset, data);
    }
    else if(id_type == TYPE_PARA)
    {
        get_one_para(data_map[index].data_addr + data_map->cur_dev_offset, data);
    }
}



//总入口函数
//打包入口函数
int new_pack_data_to_buff(void* cmd_buff , int* index, int offset_value, modbus_addr_map_data_t* data_map, unsigned char dev_offset)
{
    modbusrtn_var_t* modbusrtn_var = get_modbusrtn_var();
    int type = 0;
    int old_type = 0;
    int percision = 0;
    int data_len = 0;
    int total_len = 0;
    int reg_nums = modbusrtn_var->modbus_reg_nums;

    //+1应为第一个字节要放总长度（尽管可以直接寄存器数目计算，但是为了验证代码准性，累加长度的）
    unsigned char *data_buff = (((cmd_buf_t *)cmd_buff)->buf) + offset_value; // 准备往data_buff中存放数据
    // rt_kprintf("new_pack_data_to_buff|thread_name:%s, dev_offset:%d\n", modbusrtn_var->name, dev_offset);
    modbusrtn_var->dev_offset = dev_offset;
    while (reg_nums > 0 )
    {
        // is_contain_time(*index , 0, data_map);
        type = data_map[*index].type;
        old_type = data_map[*index].old_type;
        percision = pow(10, data_map[*index].precision);
        data_len = data_map[*index].data_len;

        for( int i = 0 ;i < sizeof(pack_fun_map)/sizeof(pack_fun_map_t); i++)
        {
            if (pack_fun_map[i].src_type == TYPE_MAX)
            {
                LOG_E("pack|register_addr:%d, type:%d, old_type:%d, not this data type!\n", data_map[*index].register_addr, type, old_type);
                ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_VALUE;
                return FAILURE;
            }
            if(pack_fun_map[i].src_type == old_type && pack_fun_map[i].dst_type == type)
            {
                total_len += pack_fun_map[i].pack_fun(index , data_buff , &reg_nums , data_len , percision , total_len,data_map);
                break;
            }
        }
    }
    return total_len;
}
int new_pack_ana_para_data(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    modbusrtn_var_t* modbusrtn_var = get_modbusrtn_var();
    int reg_addr = modbusrtn_var->modbus_reg_addr;
    /*先找到基地址的下标索引*/
    int index = find_index_from_map(reg_addr, g_modbus_data_map, sizeof(g_modbus_data_map) / sizeof(modbus_addr_map_data_t), sizeof(modbus_addr_map_data_t), get_modbus_addr);

    if (index == NO_MATCH_REGISTER_ADDR)
    {
        ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_ADDR;
        return SUCCESSFUL;
    }
    int data_len = new_pack_data_to_buff(cmd_buff, &index, 1, g_modbus_data_map, 0);   // 北向全量展开，偏移为0

    ((cmd_buf_t *)cmd_buff)->buf[0] = data_len;
    ((cmd_buf_t *)cmd_buff)->data_len = data_len + 1;

    return SUCCESSFUL;
}

int get_set_fan_speed_cmd_id_by_brand(unsigned short brand, int dev_addr_index, unsigned short fan_speed_percent)
{
    unsigned short actal_speed = 0;
    if(brand == FAN_EBM_BRAND)
    {
        actal_speed = (fan_speed_percent * 64000) / 100;
        set_one_data(DXCB_DATA_ID_FAN_WIND_SPEED_CTRL + dev_addr_index, &actal_speed);
        return EBM_SET_WIND_SPEED_CTRL;
    }
    else if(brand == FAN_AVC_BRAND)
    {
        actal_speed = fan_speed_percent * 10;
        set_one_data(DXCB_DATA_ID_FAN_WIND_SPEED_CTRL_AVC + dev_addr_index, &actal_speed);
        return AVC_SET_SPEED_CTRL;
    }
    else if(brand == FAN_SLB_BRAND)
    {
        set_one_data(DXCB_DATA_ID_FAN_WIND_SPEED_CTRL + dev_addr_index, &fan_speed_percent);
        return SLB_SET_SPEED_CTRL;
    }
    else if(brand == FAN_FSD_BRAND)
    {
        actal_speed = (fan_speed_percent * 64000) / 100;
        set_one_data(DXCB_DATA_ID_FAN_WIND_SPEED_CTRL + dev_addr_index, &actal_speed);
        return FSD_SET_WIND_SPEED_CTRL;
    }

    return FAILURE;
}

int get_set_vfd_freq_cmd_id_by_brand(unsigned short brand, int dev_addr_index, unsigned short vfd_output_freq)
{
    unsigned short speed = 0;
    if(brand == VFD_AMS_BRAND || brand == VFD_HC_BRAND)
    {
        speed = 60 * vfd_output_freq;
        set_one_data(DXCB_DATA_ID_VFD_TARGET_SPEED + dev_addr_index, &speed);
        return AMS_HC_SET_TARGET_SPEED;
    }
    else if(brand == VFD_DFS_BRAND)
    {
        speed = 60 * vfd_output_freq;
        set_one_data(DXCB_DATA_ID_VFD_FREQUENCY + dev_addr_index, &speed);
        return DFS_SET_FRE;
    }
    return FAILURE; 
}

int ctrl_inter_fan_speed(unsigned short register_addr)
{
    unsigned short inter_fan_num = 0;
    unsigned short inter_fan_brand = 0;
    unsigned short inter_fan_speed_percent = 0;
    unsigned short index = register_addr - 1600;
    unsigned short fan_status = 0;

    if(!(register_addr >= 1600 && register_addr <= 1607))   
    {   
        return SUCCESSFUL;
    }
    get_one_para(DXCB_PARA_ID_INTERNAL_FAN_NUM_OFFSET, &inter_fan_num);
    if(index + 1 > inter_fan_num)
    {
        return SUCCESSFUL;
    }
    get_one_data(DCXB_TRACE_ID_FAN_COMM_FAIL_ALARM + index, &fan_status);
    if(fan_status == TRUE)
    {
        return SUCCESSFUL;
    }
    get_one_para(DXCB_PARA_ID_FAN_BRAND_OFFSET, &inter_fan_brand);
    get_one_data(DXCB_DATA_ID_INTERNAL_FAN_SPEED_VALUE + index, &inter_fan_speed_percent);
    pub_msg_to_sps_cmd(NORTH_CTRL_SOUTH_FAN_MSG, index + 1, get_set_fan_speed_cmd_id_by_brand(inter_fan_brand, index, inter_fan_speed_percent));

    if(RT_EOK != rt_sem_take(get_rtn_sem(), 2000))
    {
        rt_kprintf("ctrl_inter_fan_speed|south timeout\n");
        return FAILURE;
    }
    else
    {
        rt_kprintf("ctrl_inter_fan_speed|dev_addr:%d, brand:%d, inter_fan_speed_percent: %d, tick:%d\n", index + 1, inter_fan_brand, inter_fan_speed_percent, rt_tick_get_millisecond());
        return SUCCESSFUL; 
    }
}

int ctrl_outer_fan_speed(unsigned short register_addr)
{
    unsigned short out_fan_num = 0;
    unsigned short inter_fan_num = 0;
    unsigned short out_fan_brand = 0;
    unsigned short out_fan_speed_percent = 0;
    unsigned short index = register_addr - 1608;
    unsigned short fan_status = 0;

    if(!(register_addr >= 1608 && register_addr <= 1615))   
    {   
        return SUCCESSFUL;
    }
    get_one_para(DXCB_PARA_ID_EXTERNAL_FAN_NUM_OFFSET, &out_fan_num);
    if(index + 1 > out_fan_num)
    {
        return SUCCESSFUL;
    }
    get_one_data(DCXB_TRACE_ID_FAN_COMM_FAIL_ALARM + 8 + index, &fan_status);
    if(fan_status == TRUE)
    {
        return SUCCESSFUL;
    }
    get_one_para(DXCB_PARA_ID_FAN_BRAND_OFFSET + 1, &out_fan_brand);
    get_one_para(DXCB_PARA_ID_INTERNAL_FAN_NUM_OFFSET, &inter_fan_num);
    get_one_data(DXCB_DATA_ID_OUT_FAN_SPEED_VALUE + index, &out_fan_speed_percent);
    pub_msg_to_sps_cmd(NORTH_CTRL_SOUTH_FAN_MSG, inter_fan_num + index + 1, get_set_fan_speed_cmd_id_by_brand(out_fan_brand, index + 8, out_fan_speed_percent));
    if(RT_EOK != rt_sem_take(get_rtn_sem(), 2000))
    {
        rt_kprintf("ctrl_outer_fan_speed|south timeout\n");
        return FAILURE;
    }
    else
    {
        rt_kprintf("north_ctrl_south_dev|dev_addr:%d, brand:%d, out_fan_speed_percent:%d\n", index + 9 ,out_fan_brand, out_fan_speed_percent);
        return SUCCESSFUL; 
    }
}

int ctrl_compressor_start_stop_freq(unsigned short register_addr)
{
    unsigned short compressor_num = 0;
    unsigned short vfd_brand = 0;
    unsigned short vfd_switch = 0;
    unsigned short vfd_status = 0;
    float vfd_output_freq = 0.0;

    unsigned short dev_addr_index = (register_addr - 1616) / 2;           
    if(!(register_addr >= 1616 && register_addr <= 1623))    // 压缩机启动和频率设定
    {   
        return SUCCESSFUL;
    }

    get_one_para(DXCB_PARA_ID_COMPRESSORS_NUM_OFFSET, &compressor_num);
    if(dev_addr_index + 1 > compressor_num)
    {
        return SUCCESSFUL;
    }
    get_one_data(DCXB_TRACE_ID_VFD_COMM_FAIL_ALARM + dev_addr_index, &vfd_status);
    if(vfd_status == TRUE)
    {
        get_one_data(DXCB_DATA_ID_COMPRESSOR_SWITCH_VALUE + dev_addr_index, &vfd_switch);
        if(vfd_switch == 0)    // 南向通信断的时候，如果北向是关机指令，直接动作干接点
        {
            set_do_value(get_do_index_by_vfd_no(dev_addr_index), 1);       // 压缩机的干接点(闭合)-低电平是启动
            set_do_value(DO_11, 1);  //散热风扇
        }
        return SUCCESSFUL;
    }
    get_one_para(DXCB_PARA_ID_FREQUENCY_CONVERTER_BRAND_OFFSET, &vfd_brand);
    if((register_addr - 1616) % 2 == 0)
    {
        get_one_data(DXCB_DATA_ID_COMPRESSOR_OUT_FREQ_VALUE + dev_addr_index, &vfd_output_freq);
        pub_msg_to_sps_cmd(NORTH_CTRL_SOUTH_VFD_MSG, dev_addr_index + 1, get_set_vfd_freq_cmd_id_by_brand(vfd_brand, dev_addr_index, vfd_output_freq));
        if(RT_EOK != rt_sem_take(get_rtn_sem(), 2000))
        {
            rt_kprintf("ctrl_vfd_freq|south timeout\n"); 
            return FAILURE;
        }
        else
        {
            rt_kprintf("north_ctrl_south_dev|dev_addr:%d, brand:%d, vfd_output_freq: %.3f, tick:%d\n", dev_addr_index + 1, vfd_brand, vfd_output_freq, rt_tick_get_millisecond());
            return SUCCESSFUL; 
        } 
    }
    else
    {
        get_one_data(DXCB_DATA_ID_COMPRESSOR_SWITCH_VALUE + dev_addr_index, &vfd_switch);
        if(FAILURE == is_compressor_protect_valid(dev_addr_index))        
        {
            return FAILURE;
        }
        pub_msg_to_sps_cmd(NORTH_CTRL_SOUTH_VFD_MSG, dev_addr_index + 1, get_set_vfd_start_stop_cmd_id_by_brand(vfd_brand, dev_addr_index, vfd_switch));
        if(RT_EOK != rt_sem_take(get_rtn_sem(), 2000))
        {
            rt_kprintf("ctrl_vfd_start_stop|south timeout\n");
            return FAILURE;
        }
        else
        {
            set_do_value(get_do_index_by_vfd_no(dev_addr_index), 0);       // 压缩机的干接点(闭合)-低电平是启动
            set_do_value(DO_11, (unsigned char)!vfd_switch);  //散热风扇
            set_one_data(DXCB_DATA_ID_VFD_COMPRESSOR_RUN_STATUS + dev_addr_index, (unsigned char*)&vfd_switch);
            rt_kprintf("north_ctrl_south_dev|dev_addr:%d, brand:%d, vfd_switch: %d, tick:%d\n", dev_addr_index + 1, vfd_brand, vfd_switch, rt_tick_get_millisecond());
            return SUCCESSFUL; 
        } 
    }

}

int get_dfs_start_para_cmd_id(int dev_index, int sid_index, unsigned short set_value)
{
    if(sid_index == 0)
    {
        set_one_data(DXCB_DATA_ID_VFD_START_FREQUENCY + dev_index, &set_value);
        return DFS_SET_START_FRE;           // 丹佛斯启动转速(6DBH)
    }
    else if(sid_index == 1)
    {
        set_one_data(DXCB_DATA_ID_VFD_START_FRE_HOLD_TIME + dev_index, &set_value);
        return DFS_SET_STARTFRE_HOLDTIME;   // 丹佛斯启动转速维护时间(6ADH)
    }
    else
    {
        float set_float = (float)set_value;
        set_one_data(DXCB_DATA_ID_VFD_FIRST_ACCE_TIME + dev_index, &set_float);
        return DFS_SET_FIRST_ACCE_TIME;     // 丹佛斯第一加速时间(D51H)
    }
}

int ctrl_hc_start_para(unsigned short register_addr)
{
    unsigned short value = 0;
    float set_float = 0.0;
    unsigned short compressor_num = 0;
    unsigned short vfd_commu_sta = 0;
    int ret = SUCCESSFUL;
    if(register_addr != 1123)
    {
        return FAILURE;
    }
    get_one_para(DXCB_PARA_ID_VFD_FIRST_ACCELERATE_TIME_OFFSET, &value);
    get_one_para(DXCB_PARA_ID_COMPRESSORS_NUM_OFFSET, &compressor_num);
    for(int dev_addr = 0; dev_addr < compressor_num; dev_addr++)
    {
        get_one_data(DCXB_TRACE_ID_VFD_COMM_FAIL_ALARM + dev_addr, &vfd_commu_sta);
        if(vfd_commu_sta == TRUE)
        {
            continue;
        }
        set_float = (float)value;
        set_one_data(DXCB_DATA_ID_VFD_FIRST_ACCE_TIME + dev_addr, &set_float);
        pub_msg_to_sps_cmd(NORTH_CTRL_SOUTH_VFD_MSG, dev_addr + 1, HC_SET_FIRST_ACCE_TIME);
        if(RT_EOK != rt_sem_take(get_rtn_sem(), 2000))
        {
            rt_kprintf("ctrl_hc_start_para|dev_addr:%d, south timeout\n", dev_addr + 1);
            ret |= FAILURE;
        }
        else
        {
            rt_kprintf("ctrl_hc_start_para|dev_addr:%d, value:%d\n", dev_addr + 1, value);
        } 
    }
    return ret;
}

int ctrl_dfs_start_para(unsigned short register_addr)
{
                                
    unsigned short sid_list[] = {DXCB_PARA_ID_DFS_START_SPEED_OFFSET,               // 丹佛斯启动转速
                                DXCB_PARA_ID_DFS_START_DURATION_OFFSET,             // 丹佛斯启动转速维持时间
                                DXCB_PARA_ID_VFD_FIRST_ACCELERATE_TIME_OFFSET};     // 变频器第一加速时间
    unsigned int sid_index = register_addr - 1121;
    unsigned short value = 0;
    unsigned short compressor_num = 0;
    unsigned short vfd_commu_sta = 0;
    int ret = SUCCESSFUL;

    if(sid_index >= 3) 
    {
        return FAILURE;
    }
    get_one_para(sid_list[sid_index], &value);
    get_one_para(DXCB_PARA_ID_COMPRESSORS_NUM_OFFSET, &compressor_num);

    for(int dev_addr_index = 0; dev_addr_index < compressor_num; dev_addr_index ++)
    {
        get_one_data(DCXB_TRACE_ID_VFD_COMM_FAIL_ALARM + dev_addr_index, &vfd_commu_sta);
        if(vfd_commu_sta == TRUE)
        {
            continue;
        }
        pub_msg_to_sps_cmd(NORTH_CTRL_SOUTH_VFD_MSG, dev_addr_index + 1, get_dfs_start_para_cmd_id(dev_addr_index, sid_index, value));
        if(RT_EOK != rt_sem_take(get_rtn_sem(), 2000))
        {
            rt_kprintf("ctrl_dfs_start_para|dev_addr:%d, south timeout\n", dev_addr_index + 1);
            ret |= FAILURE;
        }
        else
        {
            rt_kprintf("ctrl_dfs_start_para|dev_addr:%d, sid_index:%d, value:%d\n", dev_addr_index + 1, sid_index, value);
        } 
    }
    return ret;
}

int handle_north_comm_para(unsigned short cur_register_addr, special_flag_t* special_flag, unsigned char is_para_change)
{
    special_flag->is_nor_commu_para = TRUE;
    return SUCCESSFUL;
}

int handle_south_fan_para(unsigned short cur_register_addr, special_flag_t* special_flag, unsigned char is_para_change)
{
    special_flag->is_fan_commu_para = TRUE;
    return SUCCESSFUL;
}

int handle_south_vfd_para(unsigned short cur_register_addr, special_flag_t* special_flag, unsigned char is_para_change)
{
    special_flag->is_vfd_commu_para = TRUE;
    return SUCCESSFUL;
}

int handle_alm_para_flag(unsigned short cur_register_addr, special_flag_t* special_flag, unsigned char is_para_change)
{
    if(is_para_change)
    {
        special_flag->is_numeric_para_save_flag = TRUE;
    }
    special_flag->is_alm_para_flag = TRUE;
    return SUCCESSFUL;
}

int handle_dfs_start_para(unsigned short cur_register_addr, special_flag_t* special_flag, unsigned char is_para_change)
{
    unsigned short vfd_brand = 0;
    get_one_para(DXCB_PARA_ID_FREQUENCY_CONVERTER_BRAND_OFFSET, &vfd_brand);
    RETURN_VAL_IF_FAIL(vfd_brand == VFD_DFS_BRAND, SUCCESSFUL);
    if(SUCCESSFUL != ctrl_dfs_start_para(cur_register_addr))
    {
        special_flag->rtn_flag = MODBUS_RTN_ILLEGAL_VALUE;
    }
    else
    {
        special_flag->rtn_flag = MODBUS_RTN_OK;
    }
    return SUCCESSFUL;
}

int handle_hc_start_para(unsigned short cur_register_addr, special_flag_t* special_flag, unsigned char is_para_change)
{
    unsigned short vfd_brand = 0;
    get_one_para(DXCB_PARA_ID_FREQUENCY_CONVERTER_BRAND_OFFSET, &vfd_brand);
    RETURN_VAL_IF_FAIL(vfd_brand == VFD_HC_BRAND, SUCCESSFUL);
    if(SUCCESSFUL != ctrl_hc_start_para(cur_register_addr))
    {
        special_flag->rtn_flag = MODBUS_RTN_ILLEGAL_VALUE;
    }
    else
    {
        special_flag->rtn_flag = MODBUS_RTN_OK;
    }
    return SUCCESSFUL;
}

int handle_do_ctrl(unsigned short cur_register_addr, special_flag_t* special_flag, unsigned char is_para_change)
{
    special_flag->is_do_ctrl = TRUE;
    return SUCCESSFUL;
}

int handle_do_ctrl_apptest(unsigned short cur_register_addr, special_flag_t* special_flag, unsigned char is_para_change)
{
    g_is_do_ctrl = TRUE;
    return SUCCESSFUL;
}

int handle_ctrl_inter_fan_speed(unsigned short cur_register_addr, special_flag_t* special_flag, unsigned char is_para_change)
{
    if(SUCCESSFUL != ctrl_inter_fan_speed(cur_register_addr))
    {
        special_flag->rtn_flag = MODBUS_RTN_ILLEGAL_VALUE;
    }
    return SUCCESSFUL;
}

int handle_ctrl_outer_fan_speed(unsigned short cur_register_addr, special_flag_t* special_flag, unsigned char is_para_change)
{
    if(SUCCESSFUL != ctrl_outer_fan_speed(cur_register_addr))
    {
        special_flag->rtn_flag = MODBUS_RTN_ILLEGAL_VALUE;
    }
    return SUCCESSFUL;
}

int handle_ctrl_compress_start_stop_freq(unsigned short cur_register_addr, special_flag_t* special_flag, unsigned char is_para_changeg)
{
    if(SUCCESSFUL != ctrl_compressor_start_stop_freq(cur_register_addr))
    {
        special_flag->rtn_flag = MODBUS_RTN_ILLEGAL_VALUE;
    }
    return SUCCESSFUL;
}

int handle_save_numeric_para_flag(unsigned short cur_register_addr, special_flag_t* special_flag, unsigned char is_para_change)
{
    if(is_para_change)
    {
        special_flag->is_numeric_para_save_flag = TRUE;
    }
    return SUCCESSFUL;
}

int handle_save_string_para_flag(unsigned short cur_register_addr, special_flag_t* special_flag, unsigned char is_para_change )
{
    if(is_para_change)
    {
        special_flag->is_string_para_save_flag = TRUE;
    }
    return SUCCESSFUL;
}

int handle_eev_ctrl(unsigned short cur_register_addr, special_flag_t* special_flag, unsigned char is_para_change)
{
    g_eev_ctrl_flag = TRUE;
    return SUCCESSFUL;
}


static special_flag_config_t special_flag_config[] = 
{
    {1001, 1002, handle_north_comm_para},
    {1117, 1118, handle_south_fan_para},
    {1119, 1120, handle_south_vfd_para},
    {1000, 1423, handle_alm_para_flag},
    {1121, 1123, handle_dfs_start_para},
    {1123, 1123, handle_hc_start_para},
    {1632, 1646, handle_do_ctrl},
    {1647, 1648, handle_eev_ctrl},
    {1649, 1649, handle_do_ctrl_apptest},
    {1600, 1607, handle_ctrl_inter_fan_speed},
    {1608, 1615, handle_ctrl_outer_fan_speed},
    {1616, 1623, handle_ctrl_compress_start_stop_freq},
    {1700, 1710, handle_save_string_para_flag},
    {ADDR_SET_REG, ADDR_SET_REG, handle_save_numeric_para_flag},
};

special_flag_t is_contain_spec_addr(int index, modbus_addr_map_data_t* data_map, special_flag_t spe_flag, unsigned char is_para_change)
{
    for(int loop = 0; loop < sizeof(special_flag_config) / sizeof(special_flag_config_t); loop ++)
    {
        if(data_map[index].register_addr >= special_flag_config[loop].lower_limit && data_map[index].register_addr <= special_flag_config[loop].upper_limit)
        {
            special_flag_config[loop].handle_north_func(data_map[index].register_addr, &spe_flag, is_para_change);
        }
    }

    return spe_flag;
}

void set_do_ctrl()
{
    unsigned short do_sta;
    int i = 0;
    for(; i < DO_MAX; i++)
    {
        get_one_data(DXCB_DATA_ID_DO_CTRL + i, &do_sta);
        set_do_value(i, !do_sta);   
    }
    return;
}

void set_do_ctrl_apptest()
{
    static int s_do_sta = -1;
    unsigned short do_sta = 0;
    get_one_data(DXCB_DATA_ID_DO_CTRL_APPTEST, &do_sta);
    RETURN_IF_FAIL(s_do_sta != do_sta);
    set_all_do_value(do_sta);
    s_do_sta = do_sta;
    return;
}

int update_south_commu_para(char* usart_name, int baud_rate_offset, int parity_bit_offset)
{
    RETURN_VAL_IF_FAIL(usart_name != NULL, FAILURE);
    unsigned short baud_rate = 0;
    unsigned short parity_bit = 0;
    static const unsigned int baud_rate_list[] = {2400, 4800, 9600, 19200};
    static const unsigned short parity_bit_list[] = {PARITY_NONE, PARITY_ODD, PARITY_EVEN};
    struct serial_configure serial_config = RT_SERIAL_CONFIG_DEFAULT;
    get_one_para(baud_rate_offset, &baud_rate);
    get_one_para(parity_bit_offset, &parity_bit);
    serial_config.baud_rate = baud_rate_list[baud_rate];
    serial_config.parity = parity_bit_list[parity_bit];
    uart_dev_config(usart_name, &serial_config);
    return SUCCESSFUL;

}

void set_eev_ctrl()
{
    int i = 0;
    unsigned short eev_sta;
    for(; i < 2; i++)
    {
        get_one_data(DXCB_DATA_ID_EEV_CTRL + i, &eev_sta);
        set_eev_value(i, eev_sta);
    }
    return;
}

void special_process(special_flag_t spe_flag)
{
    if(spe_flag.is_numeric_para_save_flag == TRUE)
    {
        save_numeric_para( );
    }
    if(spe_flag.is_string_para_save_flag == TRUE)
    {
        save_string_para( );
    }
    if(spe_flag.is_alm_para_flag)
    {
        pub_msg_to_thread(PARA_CHANGE_MSG_ID, NULL, 0);
    }
    if (spe_flag.is_do_ctrl)
    {
        set_do_ctrl(); 
    }

    if(spe_flag.is_nor_commu_para)
    {
        update_south_commu_para(NORTH_EDU_UART_NAME, DXCB_PARA_ID_BAUD_RATE_OFFSET, DXCB_PARA_ID_PARITY_BIT_OFFSET);
    }

    if (spe_flag.is_fan_commu_para)
    {
        update_south_commu_para(SOUTH_FAN_UART_NAME, DXCB_PARA_ID_FAN_BAUD_RATE_OFFSET, DXCB_PARA_ID_FAN_PARITY_BIT_OFFSET);
    }

    if (spe_flag.is_vfd_commu_para)
    {
        update_south_commu_para(SOUTH_VFD_UART_NAME, DXCB_PARA_ID_VFD_BAUD_RATE_OFFSET, DXCB_PARA_ID_VFD_PARITY_BIT_OFFSET);
    }

    if (g_eev_ctrl_flag)
    {
        set_eev_ctrl();
    }

    if (g_is_do_ctrl)
    {
        set_do_ctrl_apptest(); 
    }

    return;
}


int parse_start_addr_and_reg_nums(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    unsigned char *data_buff = ((cmd_buf_t *)cmd_buff)->buf;
    modbusrtn_var_t* modbusrtn_var = get_modbusrtn_var();

    modbusrtn_var->modbus_reg_addr = get_uint16_data(&data_buff[0]); // 获取数据域的寄存器首地址 2字节
    modbusrtn_var->modbus_reg_nums = get_uint16_data(&data_buff[2]); // 获取数据域的寄存器个数 2字节
    return SUCCESSFUL;
}


// 获取 modbus_addr_map_data_t 结构体的 register_addr
int get_modbus_addr(const void* data)
{
    return ((const modbus_addr_map_data_t*)data)->register_addr;
}

// 获取 ctrl_cmd_modbus_t 结构体的 register_addr
int get_ctrl_cmd_addr(const void* data)
{
    return ((const ctrl_cmd_modbus_t*)data)->register_addr;
}




unsigned char parse_ctrl_cmd_handle(unsigned char *data_buff, int ctrl_map_index)
{
    unsigned char ret = SUCCESSFUL;
    unsigned short register_value = get_uint16_data(&data_buff[2]);
    set_one_data(s_modbus_ctrl_cmd[ctrl_map_index].ctrl_id, (unsigned char*)&register_value);
    if(s_modbus_ctrl_cmd[ctrl_map_index].func != NULL)
    {
        ret = s_modbus_ctrl_cmd[ctrl_map_index].func(data_buff);
    }

    return ret;
}


int parse_single_para_data_from_buff(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    g_is_ctrl_cmd = FALSE;
    unsigned char *data_buff = ((cmd_buf_t *)cmd_buff)->buf;
    modbusrtn_var_t* modbusrtn_var = get_modbusrtn_var();
    int reg_addr = get_uint16_data(&data_buff[0]); // 获取数据域的寄存器首地址 2字节;
    int index = NO_MATCH_REGISTER_ADDR;
    int type = 0;
    int old_type = 0;
    int percision = 0;
    int reg_nums = 0;
    special_flag_t special_flag = {FALSE};
    modbusrtn_var->modbus_reg_addr = reg_addr;
    modbusrtn_var->is_para_change = FALSE;
    index = find_index_from_map(reg_addr, s_modbus_ctrl_cmd, sizeof(s_modbus_ctrl_cmd) / sizeof(ctrl_cmd_modbus_t), sizeof(ctrl_cmd_modbus_t), get_ctrl_cmd_addr);
    if(index != NO_MATCH_REGISTER_ADDR)
    {
        g_is_ctrl_cmd = TRUE;
        if(parse_ctrl_cmd_handle(data_buff, index) != SUCCESSFUL)
        {
            ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_ADDR;
        }
        return SUCCESSFUL;
    }
    index = find_index_from_map(reg_addr, g_modbus_data_map, sizeof(g_modbus_data_map) / sizeof(modbus_addr_map_data_t), sizeof(modbus_addr_map_data_t), get_modbus_addr);
    if (index == NO_MATCH_REGISTER_ADDR)
    {
        ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_ADDR;
        return SUCCESSFUL;
    }
    unsigned char flag_result = g_modbus_data_map[index].reserve_flag;
    if(flag_result == 1)
    {
        return SUCCESSFUL;
    }
    int data_valude_indx = 2;
    type = g_modbus_data_map[index].type;
    old_type = g_modbus_data_map[index].old_type;
    percision = pow(10, g_modbus_data_map[index].precision);
    for(int i = 0; i < PARSE_FUN_MAP_LEN; i++)
    {
        if (parse_fun_map[i].src_type == TYPE_MAX)
        {
            LOG_E("parse | not this data type!\n");
            ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_VALUE;
            return SUCCESSFUL;
        }

        if(parse_fun_map[i].src_type == type && parse_fun_map[i].dst_type == old_type)
        {
            if(parse_fun_map[i].parse_func(&index, data_buff, &data_valude_indx, percision, &reg_nums, g_modbus_data_map) != SUCCESSFUL)
            {
                ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_VALUE;
                return SUCCESSFUL;
            }
            break;
        }
    }

    special_flag = is_contain_spec_addr(index, g_modbus_data_map, special_flag, modbusrtn_var->is_para_change);
    special_process(special_flag);
    deal_para_crc();
    ((cmd_buf_t *)cmd_buff)->rtn = special_flag.rtn_flag;
    return SUCCESSFUL;
}



//解包入口函数
int new_parse_para_data_from_buff(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    unsigned char *data_buff = ((cmd_buf_t *)cmd_buff)->buf;
    modbusrtn_var_t* modbusrtn_var = get_modbusrtn_var();

    modbusrtn_var->is_para_change = FALSE;
    int data_valude_indx = 5;
    int reg_addr = get_uint16_data(&data_buff[0]); // 获取数据域的寄存器首地址 2字节;
    int reg_nums = get_uint16_data(&data_buff[2]); // 获取数据域的寄存器个数 2字节;
    int type = 0;
    int old_type = 0;
    int percision = 0;
    special_flag_t spe_flag = {FALSE};
    modbusrtn_var->modbus_reg_addr = reg_addr;
    modbusrtn_var->modbus_reg_nums = reg_nums;
    /*先找到基地址的下标索引*/
    int index = find_index_from_map(reg_addr, g_modbus_data_map, sizeof(g_modbus_data_map) / sizeof(modbus_addr_map_data_t), sizeof(modbus_addr_map_data_t), get_modbus_addr);
    if (index == NO_MATCH_REGISTER_ADDR)
    {
        ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_ADDR;
        return SUCCESSFUL;
    }

    while ((reg_nums > 0) && index < MODBUS_DATA_MAP_LEN)
    {
        unsigned char flag_result = g_modbus_data_map[index].reserve_flag;
        if(flag_result == 1)
        {
            index++;
            reg_nums--;
            data_valude_indx += 2;
            continue;
        }
        type = g_modbus_data_map[index].type;
        old_type = g_modbus_data_map[index].old_type;
        percision = pow(10, g_modbus_data_map[index].precision);
        for(int i = 0; i < PARSE_FUN_MAP_LEN; i++)
        {
            if (parse_fun_map[i].src_type == TYPE_MAX)
            {
                LOG_E("parse | not this data type!\n");
                ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_VALUE;
            }

            if(parse_fun_map[i].src_type == type && parse_fun_map[i].dst_type == old_type)
            {
                if(parse_fun_map[i].parse_func(&index, data_buff, &data_valude_indx, percision, &reg_nums, g_modbus_data_map) != SUCCESSFUL)
                {
                    ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_VALUE;
                }
                spe_flag = is_contain_spec_addr(index, g_modbus_data_map, spe_flag, modbusrtn_var->is_para_change);
                if(spe_flag.rtn_flag != SUCCESSFUL)
                {
                    ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_VALUE;
                }
                break;
            }
        }

        index++;
    }

    special_process(spe_flag);
    deal_para_crc();
    return SUCCESSFUL;
}

int pack_set_para_data(void* dev_inst, void* cmd_buff)
{
    int offset = 0;
    // 正确响应，数据域：回复寄存器地址+寄存器个数
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    modbusrtn_var_t* modbusrtn_var = get_modbusrtn_var();

    unsigned char *data_buff = ((cmd_buf_t *)cmd_buff)->buf; // 准备往data_buff中存放数据

    put_uint16_to_buff(&data_buff[offset], modbusrtn_var->modbus_reg_addr);
    offset += 2;

    put_uint16_to_buff(&data_buff[offset], modbusrtn_var->modbus_reg_nums);
    offset += 2;

    ((cmd_buf_t *)cmd_buff)->data_len = offset;
    return SUCCESSFUL;
}



int pack_set_single_para_data(void* dev_inst, void* cmd_buff)
{
    int offset = 0;
    int type = 0;
    int old_type = 0;
    int percision = 0;
    int data_len = 0;
    int reg_nums = 1;
    unsigned short integer_data = 0;
    int index = NO_MATCH_REGISTER_ADDR;
    // 正确响应，数据域：回复寄存器地址+寄存器数值
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    modbusrtn_var_t* modbusrtn_var = get_modbusrtn_var();

    unsigned char *data_buff = ((cmd_buf_t *)cmd_buff)->buf; // 准备往data_buff中存放数据

    put_uint16_to_buff(&data_buff[offset], modbusrtn_var->modbus_reg_addr);
    offset += 2;

    if(g_is_ctrl_cmd == TRUE)
    {
        index = find_index_from_map(modbusrtn_var->modbus_reg_addr, s_modbus_ctrl_cmd, sizeof(s_modbus_ctrl_cmd) / sizeof(ctrl_cmd_modbus_t), sizeof(ctrl_cmd_modbus_t), get_ctrl_cmd_addr);
        RETURN_VAL_IF_FAIL(index != NO_MATCH_REGISTER_ADDR, FAILURE);
        get_one_data(s_modbus_ctrl_cmd[index].ctrl_id, (unsigned char*)&integer_data);
        put_uint16_to_buff(&data_buff[offset], integer_data);
        offset += 2;
    }
    else
    {
        index = find_index_from_map(modbusrtn_var->modbus_reg_addr, g_modbus_data_map, sizeof(g_modbus_data_map) / sizeof(modbus_addr_map_data_t), sizeof(modbus_addr_map_data_t), get_modbus_addr);
        RETURN_VAL_IF_FAIL(index != NO_MATCH_REGISTER_ADDR, FAILURE);
        type = g_modbus_data_map[index].type;
        old_type = g_modbus_data_map[index].old_type;
        percision = pow(10, g_modbus_data_map[index].precision);
        data_len = g_modbus_data_map[index].data_len;

        for( int i = 0 ;i < sizeof(pack_fun_map)/sizeof(pack_fun_map_t); i++)
        {
            if (pack_fun_map[i].src_type == TYPE_MAX)
            {
                LOG_E("modbus 0x06 pack |register_addr:%d, type:%d, old_type:%d, not this data type!\n", g_modbus_data_map[index].register_addr, g_modbus_data_map[index].type, g_modbus_data_map[index].old_type);
                ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_VALUE;
                return FAILURE;
            }
            if(pack_fun_map[i].src_type == old_type && pack_fun_map[i].dst_type == type)
            {
                offset += pack_fun_map[i].pack_fun(&index, data_buff, &reg_nums, data_len, percision, data_len, g_modbus_data_map);
                break;
            }
        }
    }

    ((cmd_buf_t *)cmd_buff)->data_len = offset;
    return SUCCESSFUL;
}

int cal_para_crc(crc_type_e crc_no)
{
    int start_index = 0;
    int end_index = 0;
    unsigned char temp_para[200] = {0};
    unsigned short offset = 0;
    unsigned short uint_value = 0;
    unsigned char uchar_value = 0;
    short int_value = 0;
    float float_value = 0.0;
    int precission = 0;
    unsigned short para_crc = 0;
    switch (crc_no)
    {
        case PARA_CRC1:
            start_index = PARA_CRC1_START_ADDR;
            end_index = PARA_CRC1_END_ADDR;
            break;
        case PARA_CRC2:
            start_index = PARA_CRC2_START_ADDR;
            end_index = PARA_CRC2_END_ADDR;
            break;
        case PARA_CRC3:
            start_index = PARA_CRC3_START_ADDR;
            end_index = PARA_CRC3_END_ADDR;
            break;
        default:
            return FAILURE;
    }

    start_index = find_index_from_map(start_index, g_modbus_data_map, sizeof(g_modbus_data_map) / sizeof(modbus_addr_map_data_t), sizeof(modbus_addr_map_data_t), get_modbus_addr);
    end_index = find_index_from_map(end_index, g_modbus_data_map, sizeof(g_modbus_data_map) / sizeof(modbus_addr_map_data_t), sizeof(modbus_addr_map_data_t), get_modbus_addr);
    for(int i = start_index; i <= end_index; i++)
    {
        if (i >= sizeof(g_modbus_data_map)/sizeof(g_modbus_data_map[0]))
        {
            // 处理越界情况
            break;
        }
        precission = g_modbus_data_map[i].precision;
        if(precission != 0)
        {
            get_one_para(g_modbus_data_map[i].data_addr, &float_value);
            if(g_modbus_data_map[i].type == TYPE_INT16U)
            {
                uint_value = float_value * pow(10, precission);
                put_uint16_to_buff(temp_para + offset, uint_value);
            }
            else
            {
                int_value = float_value * pow(10, precission);
                put_int16_to_buff(temp_para + offset, int_value);
            }
            
        }
        else
        {
            if(g_modbus_data_map[i].type == TYPE_INT16U)
            {
                if(g_modbus_data_map[i].old_type == TYPE_INT8U)
                {
                    get_one_para(g_modbus_data_map[i].data_addr, &uchar_value);
                    temp_para[offset] = 0;
                    temp_para[offset + 1] = uchar_value;
                }
                else
                {
                    get_one_para(g_modbus_data_map[i].data_addr, &uint_value);
                    put_uint16_to_buff(temp_para + offset, uint_value);
                }
            }
            else
            {
                get_one_para(g_modbus_data_map[i].data_addr, &int_value);
                put_int16_to_buff(temp_para + offset, int_value);
            }
        }
        offset += 2;
    }
    para_crc = modbusrtu_get_crc(temp_para, offset);
    para_crc = ((para_crc & 0xFF) <<8) | (para_crc >> 8);
    set_one_data(DXCB_DATA_ID_PARA_CRC + crc_no, &para_crc);

    // rt_kprintf("crc_no:%d, para_crc:0x%x, len:%d\n", crc_no + 1, para_crc, offset);
    // for(int loop = 0; loop < offset; loop ++)
    // {
    //     rt_kprintf("%02x ", temp_para[loop]);
    // }
    // rt_kprintf("\n");
    return SUCCESSFUL;
}


void deal_para_crc()
{
    for(int i = PARA_CRC1; i < PARA_CRC_MAX; i++)
    {
        cal_para_crc(i);
    }
}


unsigned char restore_default_para(unsigned char* buff)
{
    restore_default_sys_params();
    return SUCCESSFUL;
}

unsigned char reset_dxcb(unsigned char* buff)
{
    pub_msg_to_thread(NORTH_CTRL_RESET_DEVICE_MSG, NULL, 0);
    return SUCCESSFUL;
}

unsigned char clean_ams_hc_vfd_alarm(unsigned char* buff)
{
    unsigned short fault_clean_val = 1;
    unsigned short compressor_num = 0;
    unsigned short vfd_brand = 0;
    unsigned short vfd_commu_sta = 0;
    int ret = SUCCESSFUL;
    get_one_para(DXCB_PARA_ID_FREQUENCY_CONVERTER_BRAND_OFFSET, &vfd_brand);
    if(vfd_brand != VFD_AMS_BRAND && vfd_brand != VFD_HC_BRAND)
    {
        return FAILURE;
    }

    get_one_para(DXCB_PARA_ID_COMPRESSORS_NUM_OFFSET, &compressor_num);
    for(int dev_addr_index = 0; dev_addr_index < compressor_num; dev_addr_index ++)
    {
        get_one_data(DCXB_TRACE_ID_VFD_COMM_FAIL_ALARM + dev_addr_index, &vfd_commu_sta);
        if(vfd_commu_sta == TRUE)
        {
            continue;
        }
        set_one_data(DXCB_DATA_ID_VFD_FAULT_CLEAN + dev_addr_index, &fault_clean_val);
        pub_msg_to_sps_cmd(NORTH_CTRL_SOUTH_VFD_MSG, dev_addr_index + 1, AMS_HC_SET_FAULT_CLEAN);
        if(RT_EOK != rt_sem_take(get_rtn_sem(), 2000))
        {
            rt_kprintf("clean_ams_hc_vfd_alarm|dev_addr:%d, south timeout\n", dev_addr_index + 1);
            ret |= FAILURE;
        }
        else
        {
            rt_kprintf("clean_ams_hc_vfd_alarm|dev_addr:%d\n", dev_addr_index + 1);
        } 
    }
    return ret;
}

unsigned char ctrl_led(unsigned char* buff)
{
    unsigned char sta = 0;
    get_one_data(DXCB_CTRL_ID_CTRL_LED, &sta);
    RETURN_VAL_IF_FAIL(sta <= 1, FAILURE);
    sta += 2;  // 协议:0:常灭，1:常亮  业务:2:常灭，3:常亮，存在2个偏移
    send_led_ctrl_msg(sta);
    return SUCCESSFUL;
}

unsigned char ctrl_eeprom_read_write(unsigned char* buff)
{
    unsigned char chip_test = 0x55; //01010101
    RETURN_VAL_IF_FAIL(handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&chip_test, sizeof(chip_test), CHIP_TEST_OFFSET) == SUCCESSFUL, FAILURE);
    chip_test = 0xAA; //10101010
    RETURN_VAL_IF_FAIL(handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&chip_test, sizeof(chip_test), CHIP_TEST_OFFSET) == SUCCESSFUL, FAILURE);
    return SUCCESSFUL;
}

char get_eev_ctrl_flag()
{
    return g_eev_ctrl_flag;
}

char get_do_ctrl_flag()
{
    return g_is_do_ctrl;
}