#ifndef _SNMPDATA_IN_H_
#define _SNMPDATA_IN_H_

#include "common.h"


#define SNMP_REAL_DATA_NUM     59
#define SNMP_REAL_ALARM_NUM    79
#define SNMP_PARA_NUM         124
#define SNMP_PARA_STR_NUM     23
#define SNMP_FACT_INFO_NUM    14
#define SNMP_CTRL_CMD_NUM     4

#define SNMP_STRING_NUM  20
#define SNMP_OTHER_STRING_NUM  32



typedef struct
{
    BYTE acCorpName[SNMP_STRING_NUM];               // 电池厂家名称
    BYTE acDeviceSn[SNMP_STRING_NUM];               // 整机序列号
    BYTE acBmsTypeName[SNMP_STRING_NUM];            // BMS型号
    BYTE acBmsFacSn[SNMP_STRING_NUM];               // BMS序列号
    BYTE acBMSSysName[SNMP_STRING_NUM];             // BMS系统名称
    BYTE acBootDate[SNMP_STRING_NUM];               // BOOT版本日期
    BYTE acBMSVersion[SNMP_STRING_NUM];             // BMS软件版本
    BYTE acBMSSoftDate[SNMP_STRING_NUM];            // BMS版本日期
    BYTE acBDUSoftVer[SNMP_STRING_NUM];             // BDU软件版本
    BYTE acBDUSoftDate[SNMP_STRING_NUM];            // BDU版本日期
    BYTE acCellManufactruer[SNMP_STRING_NUM];       // 电芯厂家名称
    BYTE acPackDataEnable[SNMP_STRING_NUM];         // 电芯出厂日期
    BYTE acIMEISn[SNMP_STRING_NUM];                 // IMEI
    BYTE acMacAddress[SNMP_STRING_NUM];             // MAC
    BYTE acSoftwareTag[SNMP_STRING_NUM];            // 软件标识码
} T_SNMPFactoryInfo;


typedef struct
{
    WORD wOffset;
    WORD ucDataType;
    BYTE ucPrecision;
    // BYTE ucByteNum;
}T_SnmpRealDataStruct;

typedef struct
{
    WORD wOffset;
    BYTE ucNum;
}T_SnmpRealAlarmStruct;

typedef struct
{
    WORD wLocalParaDataOffset;
    WORD ucDataType;
    BYTE ucPrecision;
    BYTE ucParaType;
}T_SnmpSyncParaStruct;

typedef struct
{
    WORD wOffset;
    BYTE ucDataType;
    BYTE ucLen;
    BOOLEAN bIsSync;
    BYTE ucParaType;
}T_SnmpSyncParaStrStruct;

typedef struct
{
    WORD wOffset;
    BYTE ucLen;
}T_SnmpFactInfoStruct;

typedef struct
{
    BYTE    ucAddr;
    BYTE    ucCmd;
    INT32   iData;
    BOOLEAN bSetFlag;
} T_SnmpCtrlPara;

typedef struct
{
    BYTE    ucId;
    BOOLEAN (*pSnmpCtrlFunc)(void *para);
}T_SnmpCtrlCmd;



typedef struct
{
    BYTE aucAntiThefDefenceOn[LEN_TYPE_STRING_32];
    BYTE aucAntiThefHeartbeat[LEN_TYPE_STRING_32];
    BYTE aucAntiThefDefenceOff[LEN_TYPE_STRING_32];
    BYTE aucAntiThefOldKey[LEN_TYPE_STRING_32];
    BYTE aucAntiThefChangeKey[LEN_TYPE_STRING_32];
} T_SnmpNetAntiTheftStruct;


const T_SnmpRealDataStruct  *GetSnmpRealDataPoint(void);
const T_SnmpRealAlarmStruct  *GetSnmpRealAlarmPoint(void);
const T_SnmpSyncParaStruct  *GetSnmpParaPoint(void);
const T_SnmpSyncParaStrStruct  *GetSnmpParaStrPoint(void);
const T_SnmpFactInfoStruct  *GetSnmpFactInfoPoint(void);
const T_SnmpCtrlCmd  *GetSnmpCtrlCmdPoint(void);

BOOLEAN SnmpCtrl(T_SnmpCtrlPara *ptCtrl);
BOOLEAN SnmpCtrlDefPara(void* para);
BOOLEAN SnmpCtrlStartNetUpdateDL(void* para);
BOOLEAN SnmpCtrlDeviceStatistic(void* para);
BOOLEAN SnmpCtrlReset(void* para);
#endif
