#include <rtthread.h>
#include <rtdevice.h>
#include <board.h>
#include <drv_common.h>
#include <math.h>
#include <string.h>
#include "sample.h"
#include "data_type.h"
#include "utils_thread.h"
#include "adc_ctrl.h"
#include "hal_adc.h"
#include "pin_define.h"
#include "utils_server.h"
#include "pin_ctrl.h"
#include "pwm_ctrl.h"

#define GPIO_PIN_SET   1
#define GPIO_PIN_RESET 0

/***********************  变量定义  ************************/
static unsigned int   s_adc_data[ANALOG_COUNT+DIGITAL_COUNT][ADCCONVERTEDVALUES_SAMPLING_NUM];
static unsigned int   s_adc_data_filter[ANALOG_COUNT+DIGITAL_COUNT];
static unsigned int   s_digital_data[DIGITAL_COUNT];
Static unsigned int   s_analog_data[ANALOG_COUNT];

static struct rt_thread sample_thread;
static rt_uint8_t sample_thread_stack[1024];

/*********************  静态函数原型定义  **********************/
static void sample(void);
Static void get_mid_data(unsigned int data[ADCCONVERTEDVALUES_SAMPLING_NUM]);
static void analog_sample(void);
static void digital_sample(void);
static unsigned int temper_special_process(unsigned int base_volt_t, unsigned int sample_volt_t);
Static unsigned int median_filter(unsigned int data[ADCCONVERTEDVALUES_SAMPLING_NUM],int size);

static msg_map sample_msg_map[] =
{
     {0,NULL},//临时添加解决编译问题
};

void* sample_init_sys(void *param){
	server_info_t *server_info = (server_info_t *)param;
    register_server_msg_map(sample_msg_map, server_info);
    humi_sensor_init();
    return NULL;
}

void sample_main(void* parameter)
{
    while (is_running(TRUE)){
        rt_thread_mdelay(400);
        sample();
    }
}

/* 采样数据 */
static void sample(void) {
    digital_sample();
    analog_sample();
}

static void digital_sample(void) {
    return;
}

static void analog_sample(void) {
    unsigned int i, j;

    for(i = 0; i < SAMPLE_CHANNL_END; i++) {
		for(j = 0; j < ADCCONVERTEDVALUES_SAMPLING_NUM; j++) //ADC采样次数 6
		{
#ifdef USING_DEVICE_60XA_UIB01
			if (i == UIB01_HUM_IN) {
				s_adc_data[i][j] = hum_sample_func();
				continue;
			}
#endif
			s_adc_data[i][j] = adc_get_data_by_channel(i);

		}
		//get_mid_data(s_adc_data[i]);
		s_adc_data_filter[i] = median_filter(s_adc_data[i],ADCCONVERTEDVALUES_SAMPLING_NUM);

    }

    for(j = 0;j < ANALOG_COUNT; j++)
	{
		s_analog_data[j] = s_adc_data_filter[j];
	}

    #ifndef USING_DEVICE_60XA_UIB01
    for(j = 0;j < DIGITAL_COUNT; j++)
	{
		s_digital_data[j] = s_adc_data_filter[j+ANALOG_COUNT];
	}
    #endif
}

//取中值
Static void get_mid_data(unsigned int data[ADCCONVERTEDVALUES_SAMPLING_NUM])
{
	unsigned char j = 0 ,k = 0;
	static unsigned char count = 0;
	unsigned int temp = 0;
	for( k = 1; k < ADCCONVERTEDVALUES_SAMPLING_NUM; k++ )//排序
	{
	 for( j = k + 1; j < ADCCONVERTEDVALUES_SAMPLING_NUM; j++ )
	 {
		 if( data[j] < data[k] )
		 {
			 temp = data[k];
			 data[k] = data[j];
			 data[j] = temp;
		 }
	 }
	}
	s_adc_data_filter[count++] = data[3];
	if(count == (ANALOG_COUNT+DIGITAL_COUNT)) count = 0;
}


Static unsigned int median_filter(unsigned int data[ADCCONVERTEDVALUES_SAMPLING_NUM],int size)
{
    unsigned int max,min,sum;
    if(size>2)
    {
        max = data[0];
        min = max;
        sum = 0;
        for(int i=0;i<size;i++)
        {
            sum += data[i];
            if(data[i]>max)
            {
                max = data[i];
            }

            if(data[i]<min)
            {
                min = data[i];
            }
        }

        sum = sum-max-min;
        return sum/(size-2);
    }

    return 0;
}


// 采集温度特殊处理
static unsigned int temper_special_process(unsigned int base_volt_t, unsigned int sample_volt_t) {
	if (base_volt_t == 0 || sample_volt_t==0)
		return sample_volt_t;
	return (20.0*(float)base_volt_t/(float)sample_volt_t-15.88)*4095.0/3.0;
}

// 获取模拟量数据
unsigned short get_analog_data(int *data, unsigned short data_size) {
    unsigned short real_size = 0;
    unsigned int temp_var1,  temp_var2;
#ifdef USING_DEVICE_60XA_UIB01
    temp_var1 = temper_special_process(s_analog_data[__2V5REF], s_analog_data[UIB01_TB_IN_3]);
    temp_var2 = temper_special_process(s_analog_data[__2V5REF], s_analog_data[UIB01_TB_IN_4]);
#else
    temp_var1 = temper_special_process(s_analog_data[_2VREF], s_analog_data[TBIN_AD_01]);
//    rt_kprintf("s_analog_data[_2VREF]:%d  --- temp_var:%d --- s_analog_data[TBIN_AD_01]:%d\n", s_analog_data[_2VREF], temp_var, temp_var1);
    temp_var2 = temper_special_process(s_analog_data[_2VREF], s_analog_data[TBIN_AD_02]);
#endif
	if(data_size < sizeof(s_analog_data)) {
		real_size = data_size;
	} else {
		real_size = sizeof(s_analog_data);
	}

	rt_memcpy_s(data, real_size, s_analog_data, real_size);
#ifdef USING_DEVICE_60XA_UIB01
	data[UIB01_TB_IN_3] = temp_var1;
	data[UIB01_TB_IN_4] = temp_var2;
#else
	data[TBIN_AD_01] = temp_var1;
	data[TBIN_AD_02] = temp_var2;
#endif
    return real_size/sizeof(unsigned int);
}

// 获取数字量数据
unsigned short get_digital_data(int *data, unsigned short data_size) {
    unsigned short real_size = 0;
	if(data_size < sizeof(s_digital_data)) {
		real_size = data_size;
	} else {
		real_size = sizeof(s_digital_data);
	}
	rt_memcpy_s(data, real_size, s_digital_data, real_size);

    return real_size/sizeof(unsigned int);
}

