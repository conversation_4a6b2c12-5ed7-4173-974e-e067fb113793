/**************************************************************************
* Copyright (C) 2001, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：ZXDU18 CTL板软件
* 文件名称：led.c
* 文件说明：指示灯模块
* 作    者：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要
* 其他说明：
***************************************************************************/

#ifndef LED_H_
#define LED_H_

#include <rtdevice.h>
#include <board.h>
#include "common.h"
#include "SingleButton.h"


#define DIGIT_PORT_NUM  4      //数字量输入端口个数

#define SOCFAULT  10    //SOC错误
#define CAPSLEEP  0xFF
// #define SW_B1_Pin       GET_PIN(E, 12)  //GET_PIN(E, 7)

enum
{
	CSU_SLEEP = 0,   //关机/休眠                全灭
	CSU_STANDBY,     //待机正常                 run：慢闪；alm:灭；cap：根据容量显示
	CSU_STANDBY_PRT, //待机告警&保护            run：慢闪；alm:慢闪；cap：根据容量显示
	CSU_CHARGE,      //充电正常                 run：长亮；alm:灭；cap：根据容量显示，最高位快闪
	CSU_CHARGE_PRT,  //充电保护                 run：长亮；alm:慢闪；cap：根据容量显示，最高位快闪
	CSU_DISCH,       //放电正常                 run：快闪；alm:灭；cap：根据容量显示
	CSU_DISCH_PRT, 	 //放电保护                 run：快闪；alm:慢闪；cap：根据容量显示
	CSU_SYS_ABN,     //系统失效                 run：慢闪；alm:长亮；cap：根据容量显示
	CSU_SYS_LOCK,    //系统锁死                 run：慢闪；alm:快闪；cap：根据容量显示
}; //RUN&CAPLED灯状态类型


enum
{
    RUN_SLEEP_ON = 0,  //休眠            run: 慢闪
    RUN_SLEEP_OFF,     //休眠            run: 灭
    RUN_SHUTDOWN,      //关机            run: 灭
    RUN_STANDBY,       //待机            run：慢闪
    RUN_CHARGE,        //充电            run：长亮
    RUN_DISCHARGE,     //放电            run：快闪
    RUN_SYS_ABN,       //系统失效        run：慢闪
    RUN_SYS_LOCK,      //系统锁死        run：长亮
    RUN_WIFI,          //WIFI测试模式    run：快闪
    RUN_GPS_TEST,      //GPS测试模式     run：长亮
    RUN_DEFENCE_ON,    //布防成功        run：长亮
    RUN_DEFENCE_OFF,   //布防失败        run：快闪
    RUN_UPGRADE,       //升级            run：慢闪
    RUN_STATUS_NUM     //新增项，用于表示运行状态的总数
}; //运行灯状态



enum 
{
    ALM_NORMAL = 0,   //无告警  alm：灭
    ALM_ABNORMAL,     //有告警  alm：慢闪
    ALM_PROT,         //有保护  alm：慢闪
    ALM_COMM_FAIL,    //通信断  alm：快闪
    ALM_SYS_ABNORMAL, //系统异常 alm：长亮
    ALM_SLEEP,        //休眠    alm ：灭
    ALM_WIFI,         //WIFI测试模式
    ALM_SYS_LOCK,     //系统锁死 alm：快闪
    ALM_DEFENCE_ON,   //布防成功 alm：长亮
    ALM_DEFENCE_OFF,  //布防失败 alm：快闪
    ALM_UPGRADE,      //升级 alm：慢闪
    ALM_CRITICAL,    //严重类告警：快闪
    ALM_STATUS_NUM    //新增项，用于表示告警状态的总数
}; //告警灯状态


enum
{
	LED_RUN = 0,
	LED_ALARM,
	LED_CAP1,
	LED_CAP2,
	LED_CAP3,
	LED_CAP4,
}; //LED灯编号


enum
{
    LEDOFF = 0,   //关
    LEDON,        //亮
    LEDSLOWSH,    //慢闪
    LEDQUICKSH,   //快闪
    LEDBLINK,     //眨眼
    LEDSWITCH,    //交替
    LEDSTATUSNUM  //新增项，用于表示灯状态的总数
}; //LED灯状态


typedef	struct
{
	BOOLEAN bOFF;          //灯灭
	BOOLEAN bON;           //灯亮
	LONG    lSlowShine;    //慢闪: 1s亮，1s灭
	LONG    lQuickShine;   //快闪：0.25s亮，0.25s灭
	LONG    lBlinkShine;   //快闪：0.25s亮，3.75s灭
}T_LEDStruct;

typedef enum
{
  BUT_NORMAL = 0,
  BUT_SHUTDOWN,
  BUT_SLEEP,
  BUT_WIFIMODE,
  BUT_GPSMODE,
  BUT_UPGRADEMODE,
  BUT_DEFENCE,
}Button_mode;//按键模式

BYTE UnitestGetLEDStatus(BYTE ucLedType);
void InitCtrlOut( void );  //TODO
void SetLedByAddr(void *parameter);
void ClearLedByAddr(void);
void SetLedStatus( BYTE LedID, BYTE Status );
void CtrlLedStatus(void);  //TODO
void GetCtrlOut( T_CtrlOutStruct * tCtrlOut );
void SetCtrlOut( T_CtrlOutStruct * tCtrlOut );
void CtrlOut(void* parameter);
void CtrlSleep( BOOLEAN bOnoff );
void SetPMOSStatus(BOOLEAN status);
// void WakeupSampleIc( void );  无函数实体，屏蔽
void ON_BUZZ( BYTE ucAlm );
BYTE getPortInput(BYTE ucPinInex);
void DefenTigger(void);

BYTE GetSPowerBtnCount(void);
BYTE GetSWSleepBtnCount(void);
void ClearBtnCount(void);

BOOLEAN IsEqualCircuitFaultDetected(void);
void CtrlTurnOn(BOOLEAN bOnoff);  //TODO
BOOLEAN GetPMOSStatus(void);

BOOLEAN CheckSW_new(BYTE ucStart, BYTE ucEnd);
void LoadNetParaWhenUpgradeToNormal(Button_State s_tButSta);
BOOLEAN SetBattIndicationStatus(BOOLEAN status);
BYTE GetFireControlStatus(void);
BYTE GetBuzzerStatus(void);
void SetRelayCtrl(WORD wRelay);
void SetRelayCtrl_new(BYTE* ucAlarmTemp, WORD wRelay);
BYTE GetRelayCtrl(void);
void SynRelayCtrl(BYTE ucRelayCtrl);
void CloseBattIndication(void *parameter);

#endif /* LED_H_ */
