/*
 * @file    : bsmu_zte_can.c
 * @brief   : BCMU与BSMU北向CAN通信协议实现
 * @details : 
 * <AUTHOR> 邹绍云10326737
 * @Date    : 2023-01-29
 * @LastEditTime: 2023-04-11
 * @version : V0.0.1
 * @para    : Copyright (c) 
 *            ZTE Corporation
 * @par     : History
 *     version: author, date, descn
 */
#include <string.h>
#include "dev_bsmu.h"
#include "protocol_layer.h"
#include "parse_layer.h"
#include "cmd.h"
#include "sps.h"
#include "realdata_id_in.h"
#include "para_id_in.h"
#include "alarm_id_in.h"
/* 设备缓冲区长度 */
#define R_BUFF_LEN                    256     ///<  接收缓冲区长度
#define S_BUFF_LEN                    1536    ///<  发送缓冲区长度



// 以下为单个参数设置附加码,功能码为 CMD_SET_A_PARA
#define CMD_APPEND_SET_CELL_OVA_LV1         0x0001  ///< 设置电芯过压告警（一级）
#define CMD_APPEND_SET_CELL_OVA_LV2         0x0002  ///< 设置电芯过压告警（二级）
#define CMD_APPEND_SET_CELL_OVA_LV3         0x0003  ///< 设置电芯过压告警（三级）
#define CMD_APPEND_SET_CELL_UVA_LV1         0x0004  ///< 设置电芯欠压告警（一级）
#define CMD_APPEND_SET_CELL_UVA_LV2         0x0005  ///< 设置电芯欠压告警（二级）
#define CMD_APPEND_SET_CELL_UVA_LV3         0x0006  ///< 设置电芯欠压告警（三级）
#define CMD_APPEND_SET_CELL_VDOA_LV1        0x0007  ///< 电芯压差过大告警（一级）
#define CMD_APPEND_SET_CELL_VDOA_LV2        0x0008  ///< 电芯压差过大告警（二级）
#define CMD_APPEND_SET_CELL_VDOA_LV3        0x0009  ///< 电芯压差过大告警（三级）
#define CMD_APPEND_SET_CELL_CHTA_LV1        0x000A  ///< 电芯充电高温告警（一级）
#define CMD_APPEND_SET_CELL_CHTA_LV2        0x000B  ///< 电芯充电高温告警（二级）
#define CMD_APPEND_SET_CELL_CHTA_LV3        0x000C  ///< 电芯充电高温告警（三级）
#define CMD_APPEND_SET_CELL_DHTA_LV1        0x000D  ///< 电芯放电高温告警（一级）
#define CMD_APPEND_SET_CELL_DHTA_LV2        0x000E  ///< 电芯放电高温告警（二级）
#define CMD_APPEND_SET_CELL_DHTA_LV3        0x000F  ///< 电芯放电高温告警（三级）
#define CMD_APPEND_SET_CELL_CLTA_LV1        0x0010  ///< 电芯充电低温告警（一级）
#define CMD_APPEND_SET_CELL_CLTA_LV2        0x0011  ///< 电芯充电低温告警（二级）
#define CMD_APPEND_SET_CELL_CLTA_LV3        0x0012  ///< 电芯充电低温告警（三级）
#define CMD_APPEND_SET_CELL_DLTA_LV1        0x0013  ///< 电芯放电低温告警（一级）
#define CMD_APPEND_SET_CELL_DLTA_LV2        0x0014  ///< 电芯放电低温告警（二级）
#define CMD_APPEND_SET_CELL_DLTA_LV3        0x0015  ///< 电芯放电低温告警（三级）
#define CMD_APPEND_SET_CELL_OTDA_LV1        0x0016  ///< 电芯温差过大告警（一级）
#define CMD_APPEND_SET_CELL_OTDA_LV2        0x0017  ///< 电芯温差过大告警（二级）
#define CMD_APPEND_SET_CELL_OTDA_LV3        0x0018  ///< 电芯温差过大告警（三级）
#define CMD_APPEND_SET_CLUSTER_SOC_LOW_ALM  0x0019  ///< 电池簇SOC低告警
#define CMD_APPEND_SET_CLUSTER_SOC_LOW_PROT 0x001A  ///< 电池簇SOC低保护
#define CMD_APPEND_SET_CLUSTER_SOH_LOW_ALM  0x001B  ///< 电池簇SOH低告警
#define CMD_APPEND_SET_CLUSTER_SOH_LOW_PROT 0x001C  ///< 电池簇SOC低保护
#define CMD_APPEND_SET_CLUSTER_ETH_PROT     0x001D  ///< 电池簇环境温度高保护
#define CMD_APPEND_SET_CLUSTER_ETL_PROT     0x001E  ///< 电池簇环境温度低保护
#define CMD_APPEND_SET_BMU_CELL_DAM_PROT    0x001F  ///< BMU电芯损坏保护
#define CMD_APPEND_SET_IRA_ALM_LV1          0x0020  ///< 绝缘电阻告警（一级）
#define CMD_APPEND_SET_IRA_ALM_LV2          0x0021  ///< 绝缘电阻告警（二级）
#define CMD_APPEND_SET_IRA_ALM_LV3          0x0022  ///< 绝缘电阻告警（三级）
#define CMD_APPEND_SET_COC_ALM_LV1          0x0023  ///< 一氧化碳浓度告警（一级）
#define CMD_APPEND_SET_COC_ALM_LV2          0x0024  ///< 一氧化碳浓度告警（二级）
#define CMD_APPEND_SET_COC_ALM_LV3          0x0025  ///< 一氧化碳浓度告警（三级）
#define CMD_APPEND_CELL_BALANCE_VOL_DIFF    0x0026  ///< 均衡启动电压差阈值设置
#define CMD_APPEND_CELL_BALANCE_LOW_VOL     0x0027  ///< 均衡最低启动电压值设置

#define CMD_APPEND_SET_CLUSTER_OVP_THRE     0x1001  ///< 电池过压保护阈值
#define CMD_APPEND_SET_CLUSTER_CL_CURR      0x1002  ///< 设定充电限电流系数
#define CMD_APPEND_SET_CLUSTER_DL_CURR      0x1003  ///< 设定放电限电流
#define CMD_APPEND_SET_CLUSTER_CHG_VO       0x1004  ///< 设定充电电压
#define CMD_APPEND_SET_CLUSTER_DISCHG_VOL   0x1005  ///< 设定放电电压
#define CMD_APPEND_SET_CLUSTER_BUS_OVP_THRE 0x1006  ///< 母排过压保护阈值
#define CMD_APPEND_SET_CLUSTER_BAT_UVP_THRE 0x1007  ///< 电池欠压保护阈值
#define CMD_APPEND_SET_CLUSTER_BUS_UVP_THRE 0x1008  ///< 母排欠压保护阈值
#define CMD_APPEND_SET_CLUSTER_BBDP_THRE    0x1009  ///< 母排与电池差值保护阈值
#define CMD_APPEND_CLUSTER_BAT_NUM          0x100A  ///< 电池个数
#define CMD_APPEND_BCMU_ADDR                0x100B  ///< BCMU地址
//#define CMD_APPEND_BUS_CHG_LOW_VOL          0x100C ///< BUS充电最低电压阈值
#define CMD_APPEND_DISCHG_CUR_BALANCE_CUR   0x100C ///< 放电均流电流
#define CMD_APPEND_CELL_CHG_HIGH_TEMP_LIMIT_CURR_COE  0x100D ///< 放电均流电流
#define CMD_APPEND_POWER_DOWN_VOL_THRE     0x100E ///< 停电电压阈值

//遥控附加码
#define CMD_APPEND_CTRL_POWER_ON           0x0001  ///< 来电

/* 参数默认值 */
#define BCMU_CONFIG_BYTE_LEN    100     ///< BCMU 配置字节长度默认值
#define BCMU_CONFIG_NUM         6       ///< BCMU 配置数量默认值
#define DC_DC_CONFIG_BYTE_LEN   96      ///< 簇控制器配置字节长度
#define DC_DC_CONFIG_NUM        6       ///< 簇控制器配置数量
#define BMU_CONFIG_BYTE_LEN     54      ///< BMU 配置字节长度
#define BMU_CONFIG_NUM          3       ///< BMU 配置字节数量
#define BMS_CONFIG_BYTE_LEN     28      ///< 整机配置字节长度
#define BMS_CONFIG_NUM          3       ///< 整机配置数量
/* 循环次数 */
// static short dc_dc_loop_num = BATT_CLUSTER_NUM;
// static short bmu_loop_num = BATT_MOD_NUM;


/* 缺省值 */
#define BMU_ANA_DEF_BYTE_NUM 25 + 4 * BATT_MOD_CELL_NUM      ///< 缺省BMU模拟量字节长度
#define BUM_ANA_DEF_NUM 18                                   ///< 缺省BMU模拟量数量
#define BMU_DIG_DEF_BYTE_NUM 9                               ///< 缺省BMU数字量字节长度
#define BUM_DIG_DEF_NUM 6                                    ///< 缺省BMU数字量数量
#define BUM_DIG_RESERVED_NUM 64-BATT_MOD_CELL_NUM            ///< BMU数字量数组保留位
#define BATT_CLU_ANA_DEF_BYTE_NUM 62                         ///< 缺省电池簇模拟量字节长度
#define BATT_CLU_ANA_DEF_NUM 29                              ///< 缺省电池簇模拟量数量
#define BATT_CLU_DIG_DEF_BYTE_NUM 3                          ///< 缺省电池簇数字量字节长度
#define BATT_CLU_DIG_DEF_NUM 17                              ///< 缺省电池簇数字量数量
#define BATT_CABINET_DEF_BYTE_NUM 9                          ///< 缺省电池柜模拟量字节长度
#define BATT_CABINET_DEF_NUM 6                               ///< 缺省电池柜模拟量数量
#define BATT_CABINET_DEF_WORK_STATUS 3                       ///< 缺省电池柜运行状态

#define PARA_BCMU_THRESHHOLE_DEF_BYTE_NUM     152            ///< BCMU参数阈值字节长度
#define PARA_BCMU_THRESHHOLE_DEF_NUM          39             ///< BCMU参数阈值数量
#define PARA_DC_DC_THRESHHOLE_DEF_BYTE_NUM    26             ///< 簇控制器参数阈值字节长度
#define PARA_DC_DC_THRESHHOLE_DEF_NUM         14             ///< 簇控制器参数阈值数量
#define DEF_BATT_OVER_VOL_PROTECT_THRESH      2900           ///< 缺省电池过压保护阈值
#define DEF_SET_CHG_VOL                       2800           ///< 缺省设定充电电压
#define DEF_BUS_OVER_VOL_PROTECT_THRESH       2930           ///< 缺省母排过压保护阈值
#define DEF_BATT_UNDER_VOL_PROTECT_THRESH     2160           ///< 缺省电池欠压保护阈值
#define DEF_BUS_UNDER_VOL_PROTECT_THRESH      1950           ///< 缺省母排欠压保护阈值
#define DEF_BUS_AND_BATT_DIFF_PROTECT_THRESH  600            ///< 缺省母排与电池差值保护阈值
#define DEF_BATT_NUM                          4              ///< 缺省电池个数
#define DEF_BCMU_ADDR                         1              ///< 缺省BCMU地址
#define DEF_BMU_CELL_DAMAGE_PROTECT           1.5            ///< 缺省BMU电芯损坏保护
#define DEF_INSULATION_RESISTANCE_ALM_LEVEL_1 8              ///< 缺省绝缘电阻告警（一级）
#define DEF_INSULATION_RESISTANCE_ALM_LEVEL_2 9              ///< 缺省绝缘电阻告警（二级）
#define DEF_INSULATION_RESISTANCE_ALM_LEVEL_3 10             ///< 缺省绝缘电阻告警（三级）
#define DEF_CO_CONCENTRATION_ALM_LEVEL_1      50             ///< 一氧化碳浓度告警（一级）
#define DEF_CO_CONCENTRATION_ALM_LEVEL_2      100            ///< 一氧化碳浓度告警（二级）
#define DEF_CO_CONCENTRATION_ALM_LEVEL_3      190            ///< 一氧化碳浓度告警（三级）

#define PARA_ALM_LEVEL_CONFIG_DEF_BYTE_NUM    21              ///< 告警级别配置字节长度
#define PARA_ALM_LEVEL_CONFIG_DEF_NUM         77              ///< 告警级别配置数量
#define PARA_ALM_RELAY_CONFIG_DEF_BYTE_NUM    37              ///< 告警干接点配置字节长度
#define PARA_ALM_RELAY_CONFIG_DEF_NUM         74              ///< 告警干接点配置数量

#define ALM_DEF_BYTE_NUM                      12              ///< 缺省实时告警字节长度
#define ALM_DEF_NUM                           77              ///< 缺省实时告警数量

#define BMU_SELF_CHECK_DEF_BYTE_NUM            6              ///< 缺省电池柜自检状态量字节长度
#define BMU_SELF_CHECK_DEF_NUM                 3              //< 缺省电池柜自检状态量数量
#define BATT_CLU_SELF_CHECK_DEF_BYTE_NUM       1              //< 缺省电池簇自检状态量字节长度
#define BATT_CLU_SELF_CHECK_DEF_DIG_DEF_NUM    1              ///< 缺省电池簇自检状态量数量
#define BATT_CLU_BMU_SELF_CHECK_DEF_BYTE_NUM   1              ///< 缺省电池簇-BMU自检状态量字节长度
#define BATT_CLU_BMU_SELF_CHECK_DEF_DIG_DEF_NUM 1             ///< 缺省电池簇-BMU自检状态量数量



/* BCMU厂家信息 */
#define BCMU_FAC_ID_VERSION_DATE            0x0001   //BCMU版本日期//////定义值需要确认？？？？
#define BCMU_FAC_ID_SYSTEM_NAME            0x0002   //BCMU系统名称//////定义值需要确认？？？？
#define BCMU_FAC_ID_SOFTWARE_VERSION            0x0003   //BCMU软件版本//////定义值需要确认？？？？
#define BCMU_FAC_ID_BOOT_DATE            0x0005   //BCMU_BOOT日期//////定义值需要确认？？？？


/* BSMU设备实例 */


/* 命令数据存储变量 */
static bsmu_data_t bsmu_data ={0};
// static unsigned char batt_cluster_num = 4;

/* 答复数据存储变量 */
//static bsmu_get_fac_data_t bsmu_get_fac_data;

/* 命令请求 */
static bottom_comm_cmd_head_t cmd_req[] = {
    {BOTTOM_PROTO_TYPE_COMM, CMD_NONE, CMD_APPEND_NONE, BOTTOM_RTN_APP_NOACK}, //0
    {BOTTOM_PROTO_TYPE_COMM, CMD_ADDR_ASSIGN, CMD_APPEND_ADDR_ASSIGN_TRIG, BOTTOM_RTN_APP_ACK}, //1
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA, CMD_APPEND_GET_ALL,     BOTTOM_RTN_APP_ACK},//2
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA, CMD_APPEND_GET_CABINET, BOTTOM_RTN_APP_ACK},//3
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA, CMD_APPEND_GET_CLUSTER, BOTTOM_RTN_APP_ACK},//4
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA, CMD_APPEND_GET_MOD,     BOTTOM_RTN_APP_ACK},//5
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_MANUFACTOR_DATA, CMD_APPEND_NONE, BOTTOM_RTN_APP_ACK},//6
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_BATCH_PARA, CMD_APPEND_NONE, BOTTOM_RTN_APP_ACK},//7
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_OVA_LV1, BOTTOM_RTN_APP_ACK}, //8 设置电芯过压告警（一级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_OVA_LV2, BOTTOM_RTN_APP_ACK},//9 设置电芯过压告警（二级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_OVA_LV3, BOTTOM_RTN_APP_ACK},//10 设置电芯过压告警（三级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_UVA_LV1, BOTTOM_RTN_APP_ACK},//11 设置电芯欠压告警（一级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_UVA_LV2, BOTTOM_RTN_APP_ACK},//12 设置电芯欠压告警（二级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_UVA_LV3, BOTTOM_RTN_APP_ACK},//13 设置电芯欠压告警（三级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_VDOA_LV1, BOTTOM_RTN_APP_ACK},//14 电芯压差过大告警（一级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_VDOA_LV2, BOTTOM_RTN_APP_ACK},//15 电芯压差过大告警（二级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_VDOA_LV3, BOTTOM_RTN_APP_ACK},//16 电芯压差过大告警（三级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_CHTA_LV1, BOTTOM_RTN_APP_ACK},//17 电芯充电高温告警（一级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_CHTA_LV2, BOTTOM_RTN_APP_ACK},//18 电芯充电高温告警（二级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_CHTA_LV3, BOTTOM_RTN_APP_ACK},//19 电芯充电高温告警（三级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_DHTA_LV1, BOTTOM_RTN_APP_ACK},//20 电芯放电高温告警（一级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_DHTA_LV2, BOTTOM_RTN_APP_ACK},//21 电芯放电高温告警（二级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_DHTA_LV3, BOTTOM_RTN_APP_ACK},//22 电芯放电高温告警（三级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_CLTA_LV1, BOTTOM_RTN_APP_ACK},//23 电芯充电低温告警（一级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_CLTA_LV2, BOTTOM_RTN_APP_ACK},//24 电芯充电低温告警（二级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_CLTA_LV3, BOTTOM_RTN_APP_ACK},//25 电芯充电低温告警（三级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_DLTA_LV1, BOTTOM_RTN_APP_ACK},//26 电芯放电低温告警（一级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_DLTA_LV2, BOTTOM_RTN_APP_ACK},//27 电芯放电低温告警（二级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_DLTA_LV3, BOTTOM_RTN_APP_ACK},//28 电芯放电低温告警（三级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_OTDA_LV1, BOTTOM_RTN_APP_ACK},//29 电芯温差过大告警（一级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_OTDA_LV2, BOTTOM_RTN_APP_ACK},//30 电芯温差过大告警（二级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_OTDA_LV3, BOTTOM_RTN_APP_ACK},//31 电芯温差过大告警（三级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CLUSTER_OVP_THRE, BOTTOM_RTN_APP_ACK},//32 电池过压保护阈值
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CLUSTER_CL_CURR, BOTTOM_RTN_APP_ACK},//33 设定充电限电流系数
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CLUSTER_DL_CURR, BOTTOM_RTN_APP_ACK},//34 设定放电限电流
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CLUSTER_CHG_VO, BOTTOM_RTN_APP_ACK},//35 设定充电电压
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CLUSTER_DISCHG_VOL, BOTTOM_RTN_APP_ACK},//36 设定放电电压
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CLUSTER_BUS_OVP_THRE, BOTTOM_RTN_APP_ACK},//37 母排过压保护阈值
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CLUSTER_BAT_UVP_THRE, BOTTOM_RTN_APP_ACK},//38 电池欠压保护阈值
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CLUSTER_BUS_UVP_THRE, BOTTOM_RTN_APP_ACK},//39 母排欠压保护阈值
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CLUSTER_BBDP_THRE, BOTTOM_RTN_APP_ACK},//40 母排与电池差值保护阈值
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_CLUSTER_BAT_NUM, BOTTOM_RTN_APP_ACK},//41 电池个数
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_BCMU_ADDR, BOTTOM_RTN_APP_ACK},//42 BCMU地址
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CLUSTER_SOC_LOW_ALM, BOTTOM_RTN_APP_ACK},//43 电池簇SOC低告警
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CLUSTER_SOC_LOW_PROT, BOTTOM_RTN_APP_ACK},//44 电池簇SOC低保护
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CLUSTER_SOH_LOW_ALM, BOTTOM_RTN_APP_ACK},//45 电池簇SOH低告警
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CLUSTER_SOH_LOW_PROT, BOTTOM_RTN_APP_ACK},//46 电池簇SOC低保护
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CLUSTER_ETH_PROT, BOTTOM_RTN_APP_ACK},//47 电池簇环境温度高保护
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CLUSTER_ETL_PROT, BOTTOM_RTN_APP_ACK},//48 电池簇环境温度低保护
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_BMU_CELL_DAM_PROT, BOTTOM_RTN_APP_ACK},//49 BMU电芯损坏保护
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_IRA_ALM_LV1, BOTTOM_RTN_APP_ACK},//50 绝缘电阻告警（一级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_IRA_ALM_LV2, BOTTOM_RTN_APP_ACK},//51 绝缘电阻告警（二级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_IRA_ALM_LV3, BOTTOM_RTN_APP_ACK},//52 绝缘电阻告警（三级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_COC_ALM_LV1, BOTTOM_RTN_APP_ACK},//53 一氧化碳浓度告警（一级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_COC_ALM_LV2, BOTTOM_RTN_APP_ACK},//54 一氧化碳浓度告警（二级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_COC_ALM_LV3, BOTTOM_RTN_APP_ACK},//55 一氧化碳浓度告警（三级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_ALM_LEVEL_CONFIG, CMD_APPEND_NONE, BOTTOM_RTN_APP_ACK},//56
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_ALM_RELAY_CONFIG, CMD_APPEND_NONE, BOTTOM_RTN_APP_ACK},//57
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_ALARM, CMD_APPEND_NONE, BOTTOM_RTN_APP_ACK},//58
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA, CMD_APPEND_SELFCHECK_STATE, BOTTOM_RTN_APP_ACK},//59
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_ALM_LEVEL_CONFIG, CMD_APPEND_NONE, BOTTOM_RTN_APP_ACK}, //60 获取告警级别配置
    {BOTTOM_PROTO_TYPE_DOWN, CMD_DATA_TRIG, CMD_APPEND_UPDATE_REQ, BOTTOM_RTN_APP_ACK}, //61 数据传输触发帧
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_DATA, CMD_APPEND_NONE, BOTTOM_RTN_APP_ACK}, //62 程序更新数据传输帧
    {BOTTOM_PROTO_TYPE_DOWN, CMD_PRE_UPDATE_DATA_COMPLETE_ACK, CMD_APPEND_UPDATE_REQ, BOTTOM_RTN_APP_ACK}, //63 数据传输完成确认帧
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_CONFIRM, CMD_APPEND_UPDATE_REQ, BOTTOM_RTN_APP_ACK}, //64 程序更新触发帧
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_CELL_BALANCE_VOL_DIFF, BOTTOM_RTN_APP_ACK},//65 均衡启动电压差阈值设置
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_CELL_BALANCE_LOW_VOL, BOTTOM_RTN_APP_ACK},//66 均衡最低启动电压值设置
    //{BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_BUS_CHG_LOW_VOL, BOTTOM_RTN_APP_ACK},//67 BUS充电最低电压阈值
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_DISCHG_CUR_BALANCE_CUR, BOTTOM_RTN_APP_ACK},//67 放电均流电流
    {BOTTOM_PROTO_TYPE_COMM, CMD_CALIBRATION_TIME, CMD_APPEND_TIME_SYNC, BOTTOM_RTN_APP_NOACK},//68 时间同步
    {BOTTOM_PROTO_TYPE_COMM, CMD_CALIBRATION_TIME, CMD_APPEND_NONE, BOTTOM_RTN_APP_ACK}, //69 设置时间
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_CELL_CHG_HIGH_TEMP_LIMIT_CURR_COE, BOTTOM_RTN_APP_ACK},//70 电芯充电高温限电流
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_POWER_DOWN_VOL_THRE, BOTTOM_RTN_APP_ACK},//71 停电电压阈值
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL, CMD_APPEND_CTRL_POWER_ON, BOTTOM_RTN_APP_ACK},//72 来电状态
    {BOTTOM_PROTO_TYPE_COMM, CMD_CALIBRATION_TIME, CMD_APPEND_TIME_GET, BOTTOM_RTN_APP_ACK}, //73 获取系统时间
};

/* 命令应答 */
static bottom_comm_cmd_head_t cmd_ack[] = {
    {BOTTOM_PROTO_TYPE_COMM, CMD_NONE, CMD_APPEND_NONE, BOTTOM_RTN_APP_CORRECT}, //0
    {BOTTOM_PROTO_TYPE_COMM, CMD_ADDR_ASSIGN, CMD_APPEND_ADDR_ASSIGN_TRIG, BOTTOM_RTN_APP_CORRECT}, //1
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA, CMD_APPEND_GET_ALL,     BOTTOM_RTN_APP_CORRECT}, //2
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA, CMD_APPEND_GET_CABINET, BOTTOM_RTN_APP_CORRECT}, //3
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA, CMD_APPEND_GET_CLUSTER, BOTTOM_RTN_APP_CORRECT}, //4
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA, CMD_APPEND_GET_MOD,     BOTTOM_RTN_APP_CORRECT}, //5
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_MANUFACTOR_DATA, CMD_APPEND_NONE, BOTTOM_RTN_APP_CORRECT}, //6
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_BATCH_PARA, CMD_APPEND_NONE, BOTTOM_RTN_APP_CORRECT}, //7
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_OVA_LV1, BOTTOM_RTN_APP_CORRECT}, //8 设置电芯过压告警（一级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_OVA_LV2, BOTTOM_RTN_APP_CORRECT},//9 设置电芯过压告警（二级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_OVA_LV3, BOTTOM_RTN_APP_CORRECT},//10 设置电芯过压告警（三级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_UVA_LV1, BOTTOM_RTN_APP_CORRECT},//11 设置电芯欠压告警（一级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_UVA_LV2, BOTTOM_RTN_APP_CORRECT},//12 设置电芯欠压告警（二级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_UVA_LV3, BOTTOM_RTN_APP_CORRECT},//13 设置电芯欠压告警（三级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_VDOA_LV1, BOTTOM_RTN_APP_CORRECT},//14 电芯压差过大告警（一级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_VDOA_LV2, BOTTOM_RTN_APP_CORRECT},//15 电芯压差过大告警（二级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_VDOA_LV3, BOTTOM_RTN_APP_CORRECT},//16 电芯压差过大告警（三级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_CHTA_LV1, BOTTOM_RTN_APP_CORRECT},//17 电芯充电高温告警（一级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_CHTA_LV2, BOTTOM_RTN_APP_CORRECT},//18 电芯充电高温告警（二级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_CHTA_LV3, BOTTOM_RTN_APP_CORRECT},//19 电芯充电高温告警（三级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_DHTA_LV1, BOTTOM_RTN_APP_CORRECT},//20 电芯放电高温告警（一级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_DHTA_LV2, BOTTOM_RTN_APP_CORRECT},//21 电芯放电高温告警（二级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_DHTA_LV3, BOTTOM_RTN_APP_CORRECT},//22 电芯放电高温告警（三级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_CLTA_LV1, BOTTOM_RTN_APP_CORRECT},//23 电芯充电低温告警（一级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_CLTA_LV2, BOTTOM_RTN_APP_CORRECT},//24 电芯充电低温告警（二级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_CLTA_LV3, BOTTOM_RTN_APP_CORRECT},//25 电芯充电低温告警（三级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_DLTA_LV1, BOTTOM_RTN_APP_CORRECT},//26 电芯放电低温告警（一级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_DLTA_LV2, BOTTOM_RTN_APP_CORRECT},//27 电芯放电低温告警（二级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_DLTA_LV3, BOTTOM_RTN_APP_CORRECT},//28 电芯放电低温告警（三级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_OTDA_LV1, BOTTOM_RTN_APP_CORRECT},//29 电芯温差过大告警（一级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_OTDA_LV2, BOTTOM_RTN_APP_CORRECT},//30 电芯温差过大告警（二级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CELL_OTDA_LV3, BOTTOM_RTN_APP_CORRECT},//31 电芯温差过大告警（三级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CLUSTER_OVP_THRE, BOTTOM_RTN_APP_CORRECT},//32 电池过压保护阈值
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CLUSTER_CL_CURR, BOTTOM_RTN_APP_CORRECT},//33 设定充电限电流系数
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CLUSTER_DL_CURR, BOTTOM_RTN_APP_CORRECT},//34 设定放电限电流
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CLUSTER_CHG_VO, BOTTOM_RTN_APP_CORRECT},//35 设定充电电压
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CLUSTER_DISCHG_VOL, BOTTOM_RTN_APP_CORRECT},//36 设定放电电压
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CLUSTER_BUS_OVP_THRE, BOTTOM_RTN_APP_CORRECT},//37 母排过压保护阈值
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CLUSTER_BAT_UVP_THRE, BOTTOM_RTN_APP_CORRECT},//38 电池欠压保护阈值
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CLUSTER_BUS_UVP_THRE, BOTTOM_RTN_APP_CORRECT},//39 母排欠压保护阈值
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CLUSTER_BBDP_THRE, BOTTOM_RTN_APP_CORRECT},//40 母排与电池差值保护阈值
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_CLUSTER_BAT_NUM, BOTTOM_RTN_APP_CORRECT},//41 电池个数
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_BCMU_ADDR, BOTTOM_RTN_APP_CORRECT},//42 BCMU地址
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CLUSTER_SOC_LOW_ALM, BOTTOM_RTN_APP_CORRECT},//43 电池簇SOC低告警
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CLUSTER_SOC_LOW_PROT, BOTTOM_RTN_APP_CORRECT},//44 电池簇SOC低保护
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CLUSTER_SOH_LOW_ALM, BOTTOM_RTN_APP_CORRECT},//45 电池簇SOH低告警
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CLUSTER_SOH_LOW_PROT, BOTTOM_RTN_APP_CORRECT},//46 电池簇SOC低保护
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CLUSTER_ETH_PROT, BOTTOM_RTN_APP_CORRECT},//47 电池簇环境温度高保护
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_CLUSTER_ETL_PROT, BOTTOM_RTN_APP_CORRECT},//48 电池簇环境温度低保护
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_BMU_CELL_DAM_PROT, BOTTOM_RTN_APP_CORRECT},//49 BMU电芯损坏保护
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_IRA_ALM_LV1, BOTTOM_RTN_APP_CORRECT},//50 绝缘电阻告警（一级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_IRA_ALM_LV2, BOTTOM_RTN_APP_CORRECT},//51 绝缘电阻告警（二级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_IRA_ALM_LV3, BOTTOM_RTN_APP_CORRECT},//52 绝缘电阻告警（三级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_COC_ALM_LV1, BOTTOM_RTN_APP_CORRECT},//53 一氧化碳浓度告警（一级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_COC_ALM_LV2, BOTTOM_RTN_APP_CORRECT},//54 一氧化碳浓度告警（二级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_SET_COC_ALM_LV3, BOTTOM_RTN_APP_CORRECT},//55 一氧化碳浓度告警（三级）
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_ALM_LEVEL_CONFIG, CMD_APPEND_NONE, BOTTOM_RTN_APP_CORRECT},//56
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_ALM_RELAY_CONFIG, CMD_APPEND_NONE, BOTTOM_RTN_APP_CORRECT},//57
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_ALARM, CMD_APPEND_NONE, BOTTOM_RTN_APP_CORRECT},//58
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA, CMD_APPEND_SELFCHECK_STATE, BOTTOM_RTN_APP_CORRECT},//59
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_ALM_LEVEL_CONFIG, CMD_APPEND_NONE, BOTTOM_RTN_APP_CORRECT}, //60 获取告警级别配置
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_DATA, CMD_APPEND_UPDATE_ACK, BOTTOM_RTN_APP_CORRECT}, //61 数据传输触发帧
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_DATA, CMD_APPEND_NONE, BOTTOM_RTN_APP_CORRECT}, //62 程序更新数据传输帧
    {BOTTOM_PROTO_TYPE_DOWN, CMD_PRE_UPDATE_DATA_COMPLETE_ACK, CMD_APPEND_UPDATE_ACK, BOTTOM_RTN_APP_CORRECT}, //63 数据传输完成确认帧
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_CONFIRM, CMD_APPEND_UPDATE_ACK, BOTTOM_RTN_APP_CORRECT}, //64 程序更新触发帧
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_CELL_BALANCE_VOL_DIFF, BOTTOM_RTN_APP_CORRECT},//65 均衡启动电压差阈值设置
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_CELL_BALANCE_LOW_VOL, BOTTOM_RTN_APP_CORRECT},//66 均衡最低启动电压值设置
    //{BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_BUS_CHG_LOW_VOL, BOTTOM_RTN_APP_CORRECT},//67 BUS充电最低电压阈值
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_DISCHG_CUR_BALANCE_CUR, BOTTOM_RTN_APP_CORRECT},//67 放电均流电流
    {BOTTOM_PROTO_TYPE_COMM, CMD_CALIBRATION_TIME, CMD_APPEND_NONE, BOTTOM_RTN_APP_CORRECT}, //68 校准时间
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_CELL_CHG_HIGH_TEMP_LIMIT_CURR_COE, BOTTOM_RTN_APP_CORRECT},//69 电芯充电高温限电流
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_A_PARA, CMD_APPEND_POWER_DOWN_VOL_THRE, BOTTOM_RTN_APP_CORRECT},//70 停电电压阈值
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL, CMD_APPEND_CTRL_POWER_ON, BOTTOM_RTN_APP_CORRECT},//71 来电状态
    {BOTTOM_PROTO_TYPE_COMM, CMD_CALIBRATION_TIME, CMD_APPEND_TIME_GET, BOTTOM_RTN_APP_CORRECT},//72 获取系统时间
};

/* 校准系统时间命令信息 */
static data_info_id_verison_t cmd_parse_system_time_info[] = {
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 7, NOT_NEED_INTERACT},  ///< 系统时间字节长度
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT},  ///< 参数数量
    {SEG_DATA, type_time, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_DATA_ID_SYS_TIME_SET},   ///< 年月日时分秒
};

/* 获取站址分配命令字段信息 */
static data_info_id_verison_t cmd_addr_assign_trig_data_info[] = {
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_KEEP, NO_DEF_VALUE, NOT_NEED_INTERACT},  ///< 参数字节数
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_KEEP, NO_DEF_VALUE, NOT_NEED_INTERACT},  ///< 参数数量
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_KEEP, NO_DEF_VALUE, NEED_INTERACT, BCMU_DATA_ID_ASSIGN_TYPE}, ///< 触发站址分配
};

/* 获取站址分配命令答复信息 */
static data_info_id_verison_t reply_addr_assign_trig_data_info[] = {
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 2, NOT_NEED_INTERACT},  ///< 参数字节数
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 2, NOT_NEED_INTERACT},  ///< 参数数量
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 3, NOT_NEED_INTERACT},  ///< 触发站址分配
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_DATA_ID_ASSIGN_HOST_ADDR}, ///< 站址分配结果
};

/*获取电池柜实时数据*/
static data_info_id_verison_t cmd_pack_batt_cabinet_real_data_info[] = {
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, BATT_CABINET_DEF_BYTE_NUM,    NOT_NEED_INTERACT},  ///< 电池柜信息字节长度
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, BATT_CABINET_DEF_NUM,         NOT_NEED_INTERACT},  ///< 电池柜信息数量
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, BATT_CLUSTER_NUM,             NEED_INTERACT,     BATT_CLUSTER_NUM},  ///< 电池簇数量
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_4, DOP_0, DATA_DROP, BATT_CLUSTER_MOD_NUM,         NEED_INTERACT,     BATT_MOD_NUM},  ///< 电池簇_BMU数量
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, BATT_MOD_CELL_NUM,            NEED_INTERACT,     BATT_MOD_CELL_NUM},  ///< 单个BMU电芯数量
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, BATT_CABINET_DEF_WORK_STATUS, NEED_INTERACT,     BCMU_DATA_ID_BATT_CABINET_WORK_STATUS},  ///< 电池柜运行状态
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,                 NEED_INTERACT,     BCMU_DATA_ID_MASTER_SLAVE_STATUS},  ///< 主从机状态
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,                 NEED_INTERACT,     BCMU_DATA_ID_FIRE_CONTROL_STATUS},  ///< 柜内消防状态
    
};


/* 获取电池簇实时数据命令字段信息 */
static data_info_id_verison_t cmd_parse_batt_cluster_real_data_info[] = {
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},  ///< 电池簇的字节长度
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},  ///< 电池簇的数量
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_DATA_ID_BATT_CLUSTER_ADDR_REQ},      ///< 电池簇地址
};


/*获取电池簇实时数据*/
static data_info_id_verison_t cmd_pack_batt_cluster_real_data_info[] = {
    /*模拟量*/
    {0},
    /* TODO: 重新开发
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, BATT_CLU_ANA_DEF_BYTE_NUM, NOT_NEED_INTERACT},  ///< 电池簇的模拟量字节数
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, BATT_CLU_ANA_DEF_NUM,      NOT_NEED_INTERACT},  ///< 电池簇的模拟量数量
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_BATT_CLUSTER_SOC},  ///< 电池簇SOC
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_BATT_CLUSTER_SOH},  ///< 电池簇SOH
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_BATT_CLUSTER_CHARGE_POWER},  ///< 电池簇充电功率
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_BATT_CLUSTER_DISCHARGE_POWER},  ///< 电池簇放电功率
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_CURRENT_REMAIN_CAPACITY},  ///< 当前剩余容量
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_DISCHARGE_REMAIN_TIME},  ///< 放电剩余时间
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_CHARGE_REMAIN_TIME},  ///< 充电剩余时间
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_DISCHARGE_TOATAL_CAPACITY},  ///< 累计放电容量
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_CHARGE_TOATAL_CAPACITY},  ///< 累计充电容量
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_BATT_CYCLE_TIMES},  ///< 电池循环次数
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_BATT_CHARGE_REQUEST_VOLTAGE},  ///< 电池充电请求电压
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_BATT_CHARGE_REQUEST_CURRENT},  ///< 电池充电请求电流
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_CURRENT_DISCHARGE_TARGET_VOLTAGE},  ///< 当前放电目标电压
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_CURRENT_SET_DISCHARGE_VOLTAGE},  ///< 当前设定放电电压
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_CURRENT_SET_CHARGE_VOLTAGE},  ///< 当前设定充电电压
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_CURRENT_CHARGING_LIMIT_RATIO},  ///< 设定充电限电流比例
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_CURRENT_DISCHARGING_LIMIT_RATIO},  ///< 设定放电限电流比例
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_CARBON_MONOXIDE_CONCENTRATION},  ///< 一氧化碳浓度
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_BATT_CURRENT},  ///< 电池电流（有符号）
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_BATT_VOLTAGE},  ///< 电池电压（无符号）
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_BATT_BOARD_VOLTAGE},  ///< 电池单板电压（无符号）
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_BUS_CURRENT},  ///< BUS电流（有符号）
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_BUS_BOARD_VOLTAGE},  ///< BUS单板电压(无符号)
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_COMPENSATION_CAPACITANCE_VOLTAGE},  ///< 补偿电容电压(有符号)
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_CLLC_VOLTAGE},  ///< CLLC电压(无符号)
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_ENV_TEMP},  ///< 环境温度
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_BOARD_TEMP},  ///< 单板温度
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_POSITIVE_INSULATION_RESISTANCE},  ///< 正极绝缘电阻
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_NEGATIVE_INSULATION_RESISTANCE},  ///< 负极绝缘电阻

    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, BATT_CLU_DIG_DEF_BYTE_NUM, NOT_NEED_INTERACT},///< 电池簇的状态量字节长度
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, BATT_CLU_DIG_DEF_NUM,      NOT_NEED_INTERACT}, ///< 电池簇的状态量数量
    {SEG_DATA, type_bit,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_CHARGE_STOP_STATUS}, ///< 充电停止状态
    {SEG_DATA, type_bit,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_DISCHARGE_STOP_STATUS}, ///< 放电停止状态
    {SEG_DATA, type_bit,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_CHG_ENABLE_STATUS}, ///< 充电使能状态
    {SEG_DATA, type_bit,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_DISCHG_ENABLE_STATUS}, ///< 放电使能状态
    {SEG_DATA, type_bit,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_LOCKOUT_STATUS}, ///< 闭锁状态
    {SEG_DATA, type_bit,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_SLEEP_STATUS}, ///< 休眠状态
    {SEG_DATA, type_bit,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_TEMP_LIMIT_POWER_STATUS}, ///< 温度限功率状态
    {SEG_DATA, type_bit,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NOT_NEED_INTERACT}, ///< 风扇控制状态-按保留位上送
    {SEG_DATA, type_multi_bit,      ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_SMR_WORK_STATUS}, ///< 整流器工作状态
    {SEG_DATA, type_bit,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_CHG_INPUT_BREAK_STATUS}, ///< 充电输入断状态
    {SEG_DATA, type_bit,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_OPEN_LOOP_STATUS}, ///< 开环状态
    {SEG_DATA, type_bit,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_BATT_NUM}, ///< 电池个数
    {SEG_DATA, type_bit,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_ALLOW_DOWNLOAD}, ///< 下载允许
    {SEG_DATA, type_bit,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_CHARGE_STATUS}, ///< 充电状态
    {SEG_DATA, type_bit,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_DISCHARGE_STATUS}, ///< 放电状态
    {SEG_DATA, type_bit,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_ONLINE_NONFLOATING_STATUS}, ///< 在线非浮充状态
    {SEG_DATA, type_bit,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_FLOATING_STATUS}, ///< 浮充状态
    */
};


/* 获取实时数据命令字段信息 */
static data_info_id_verison_t cmd_parse_bmu_real_data_info[] = {
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},  ///< BMU编号字节长度
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},  ///< BMU编号数量
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_DATA_ID_BATT_CLUSTER_ADDR_REQ}, ///< 电池簇地址
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_DATA_ID_BMU_ADDR_REQ}, ///< BMU地址
};

/*获取bmu实时数据*/
static data_info_id_verison_t cmd_pack_bmu_real_data_info[] = {
    /* 模拟量 */
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1,         DOP_0, DATA_DROP, BMU_ANA_DEF_BYTE_NUM, NOT_NEED_INTERACT},///< 电池簇_BMU-模拟量字节长度
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1,         DOP_0, DATA_DROP, BUM_ANA_DEF_NUM,      NOT_NEED_INTERACT}, ///< 电池簇_BMU-模拟量数量
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1,         DOP_0, DATA_DROP, NO_DEF_VALUE,         NEED_INTERACT,     BATT_MOD_CELL_NUM}, ///< 电芯数量
    {SEG_DATA, type_short,          BATT_MOD_CELL_NUM,    DOP_1, DATA_DROP, NO_DEF_VALUE,         NEED_INTERACT,     BCMU_DATA_ID_CELL_TEMP}, ///< 电芯温度
    {SEG_DATA, type_unsigned_short, BATT_MOD_CELL_NUM,    DOP_3, DATA_DROP, NO_DEF_VALUE,         NEED_INTERACT,     BCMU_DATA_ID_CELL_VOL}, ///< 电芯电压
    {SEG_DATA, type_short,          ARRAY_SIZE_1,         DOP_1, DATA_DROP, NO_DEF_VALUE,         NEED_INTERACT,     BCMU_DATA_ID_CELL_TEMP_MAX}, ///< 电芯温度最高
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1,         DOP_0, DATA_DROP, NO_DEF_VALUE,         NEED_INTERACT,     BCMU_DATA_ID_CELL_TEMP_MAX_CHANNEL}, ///< 电芯温度最高通道
    {SEG_DATA, type_short,          ARRAY_SIZE_1,         DOP_1, DATA_DROP, NO_DEF_VALUE,         NEED_INTERACT,     BCMU_DATA_ID_CELL_TEMP_MIN}, ///< 电芯温度最低
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1,         DOP_0, DATA_DROP, NO_DEF_VALUE,         NEED_INTERACT,     BCMU_DATA_ID_CELL_TEMP_MIN_CHANNEL}, ///< 电芯温度最低通道
    {SEG_DATA, type_short,          ARRAY_SIZE_1,         DOP_1, DATA_DROP, NO_DEF_VALUE,         NEED_INTERACT,     BCMU_DATA_ID_CELL_AVERAGE_TEMP}, ///< 电芯平均温度
    {SEG_DATA, type_short,          ARRAY_SIZE_1,         DOP_3, DATA_DROP, NO_DEF_VALUE,         NEED_INTERACT,     BCMU_DATA_ID_CELL_VOL_MAX}, ///< 电芯电压最高
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1,         DOP_0, DATA_DROP, NO_DEF_VALUE,         NEED_INTERACT,     BCMU_DATA_ID_CELL_VOL_MAX_CHANNEL}, ///< 电芯电压最高通道
    {SEG_DATA, type_short,          ARRAY_SIZE_1,         DOP_3, DATA_DROP, NO_DEF_VALUE,         NEED_INTERACT,     BCMU_DATA_ID_CELL_VOL_MIN}, ///< 电芯电压最低
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1,         DOP_0, DATA_DROP, NO_DEF_VALUE,         NEED_INTERACT,     BCMU_DATA_ID_CELL_VOL_MIN_CHANNEL}, ///< 电芯电压最低通道
    {SEG_DATA, type_short,          ARRAY_SIZE_1,         DOP_3, DATA_DROP, NO_DEF_VALUE,         NEED_INTERACT,     BCMU_DATA_ID_CELL_AVERAGE_VOL}, ///< 电芯平均电压
    {SEG_DATA, type_short,          ARRAY_SIZE_1,         DOP_2, DATA_DROP, NO_DEF_VALUE,         NEED_INTERACT,     BCMU_DATA_ID_MOD_VOL_TOTAL}, ///< 模块总电压

    /* 数字量 */
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1,         DOP_0, DATA_DROP, BMU_DIG_DEF_BYTE_NUM, NOT_NEED_INTERACT},///< 电池簇_BMU-模拟量字节长度
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1,         DOP_0, DATA_DROP, BUM_DIG_DEF_NUM,      NOT_NEED_INTERACT}, ///< 电池簇_BMU-模拟量数量
    {SEG_DATA, type_bit,            BATT_MOD_CELL_NUM,    DOP_0, DATA_DROP, NO_DEF_VALUE,         NEED_INTERACT,     BCMU_DATA_ID_CELL_BALANCED_STATUS}, ///< 电芯均衡状态
    {SEG_DATA, type_bit,            BUM_DIG_RESERVED_NUM, DOP_0, DATA_DROP, NO_DEF_VALUE,         NOT_NEED_INTERACT}, ///< 保留位
    {SEG_DATA, type_bit,            ARRAY_SIZE_1,         DOP_0, DATA_DROP, NO_DEF_VALUE,        NOT_NEED_INTERACT}, ///< 风扇1状态--按保留位上送
    {SEG_DATA, type_bit,            ARRAY_SIZE_1,         DOP_0, DATA_DROP, NO_DEF_VALUE,        NOT_NEED_INTERACT}, ///< 风扇2状态--按保留位上送
    {SEG_DATA, type_bit,            ARRAY_SIZE_4,         DOP_0, DATA_DROP, NO_DEF_VALUE,        NOT_NEED_INTERACT}, ///< 保留位
};

/* 回复获取所有簇和所有BMU实时数据命令字段信息 命令码0x20 附加码0x0000 */
static data_info_id_verison_t cmd_pack_real_data_info[] = {
    /*电池柜信息*/
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, BATT_CABINET_DEF_BYTE_NUM,    NOT_NEED_INTERACT},  ///< 电池柜信息字节长度
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, BATT_CABINET_DEF_NUM,         NOT_NEED_INTERACT},  ///< 电池柜信息数量
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, BATT_CLUSTER_NUM,             NEED_INTERACT,     BATT_CLUSTER_NUM},  ///< 电池簇数量
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_4, DOP_0, DATA_DROP, BATT_CLUSTER_MOD_NUM,         NEED_INTERACT,     BATT_MOD_NUM},  ///< 电池簇_BMU数量
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, BATT_MOD_CELL_NUM,            NEED_INTERACT,     BATT_MOD_CELL_NUM},  ///< 单个BMU电芯数量
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, BATT_CABINET_DEF_WORK_STATUS, NEED_INTERACT,     BCMU_DATA_ID_BATT_CABINET_WORK_STATUS},  ///< 电池柜运行状态
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,                 NEED_INTERACT,     BCMU_DATA_ID_MASTER_SLAVE_STATUS},  ///< 主从机状态
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,                 NEED_INTERACT,     BCMU_DATA_ID_FIRE_CONTROL_STATUS},  ///< 柜内消防状态

    /*电池簇信息*/
   /*模拟量*/
   /* TODO:重新开发
    {SEG_LOOP, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,  BATT_CLUSTER_NUM}, //电池簇 LOOP开始
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, BATT_CLU_ANA_DEF_BYTE_NUM, NOT_NEED_INTERACT},  ///< 电池簇的模拟量字节数
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, BATT_CLU_ANA_DEF_NUM,      NOT_NEED_INTERACT},  ///< 电池簇的模拟量数量
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_BATT_CLUSTER_SOC},  ///< 电池簇SOC
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_BATT_CLUSTER_SOH},  ///< 电池簇SOH
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_BATT_CLUSTER_CHARGE_POWER},  ///< 电池簇充电功率
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_BATT_CLUSTER_DISCHARGE_POWER},  ///< 电池簇放电功率
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_CURRENT_REMAIN_CAPACITY},  ///< 当前剩余容量
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_DISCHARGE_REMAIN_TIME},  ///< 放电剩余时间
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_CHARGE_REMAIN_TIME},  ///< 充电剩余时间
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_DISCHARGE_TOATAL_CAPACITY},  ///< 累计放电容量
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_CHARGE_TOATAL_CAPACITY},  ///< 累计充电容量
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_BATT_CYCLE_TIMES},  ///< 电池循环次数
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_BATT_CHARGE_REQUEST_VOLTAGE},  ///< 电池充电请求电压
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_BATT_CHARGE_REQUEST_CURRENT},  ///< 电池充电请求电流
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_CURRENT_DISCHARGE_TARGET_VOLTAGE},  ///< 当前放电目标电压
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_CURRENT_SET_DISCHARGE_VOLTAGE},  ///< 当前设定放电电压
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_CURRENT_SET_CHARGE_VOLTAGE},  ///< 当前设定充电电压
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_CURRENT_CHARGING_LIMIT_RATIO},  ///< 设定充电限电流比例
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_CURRENT_DISCHARGING_LIMIT_RATIO},  ///< 设定放电限电流比例
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_CARBON_MONOXIDE_CONCENTRATION},  ///< 一氧化碳浓度
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_BATT_CURRENT},  ///< 电池电流（有符号）
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_BATT_VOLTAGE},  ///< 电池电压（无符号）
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_BATT_BOARD_VOLTAGE},  ///< 电池单板电压（无符号）
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_BUS_CURRENT},  ///< BUS电流（有符号）
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_BUS_BOARD_VOLTAGE},  ///< BUS单板电压(无符号)
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_COMPENSATION_CAPACITANCE_VOLTAGE},  ///< 补偿电容电压(有符号)
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_CLLC_VOLTAGE},  ///< CLLC电压(无符号)
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_ENV_TEMP},  ///< 环境温度
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_BOARD_TEMP},  ///< 单板温度
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_POSITIVE_INSULATION_RESISTANCE},  ///< 正极绝缘电阻
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_NEGATIVE_INSULATION_RESISTANCE},  ///< 负极绝缘电阻
    */
    /*数字量*/
    /* TODO:重新开发
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, BATT_CLU_DIG_DEF_BYTE_NUM, NOT_NEED_INTERACT},///< 电池簇的状态量字节长度
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, BATT_CLU_DIG_DEF_NUM,      NOT_NEED_INTERACT}, ///< 电池簇的状态量数量
    {SEG_DATA, type_bit,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_CHARGE_STOP_STATUS}, ///< 充电停止状态
    {SEG_DATA, type_bit,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_DISCHARGE_STOP_STATUS}, ///< 放电停止状态
    {SEG_DATA, type_bit,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_CHG_ENABLE_STATUS}, ///< 充电使能状态
    {SEG_DATA, type_bit,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_DISCHG_ENABLE_STATUS}, ///< 放电使能状态
    {SEG_DATA, type_bit,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_LOCKOUT_STATUS}, ///< 闭锁状态
    {SEG_DATA, type_bit,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_SLEEP_STATUS}, ///< 休眠状态
    {SEG_DATA, type_bit,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_TEMP_LIMIT_POWER_STATUS}, ///< 温度限功率状态
    {SEG_DATA, type_bit,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_CHG_INPUT_BREAK_STATUS}, ///< 充电输入断状态
    {SEG_DATA, type_bit,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_OPEN_LOOP_STATUS}, ///< 开环状态
    {SEG_DATA, type_bit,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_BATT_NUM}, ///< 电池个数,<按照电池柜信息-电池簇数量循环展开>
    {SEG_DATA, type_bit,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_ALLOW_DOWNLOAD}, ///< 下载允许
    {SEG_DATA, type_bit,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_CHARGE_STATUS}, ///< 充电状态
    {SEG_DATA, type_bit,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_DISCHARGE_STATUS}, ///< 放电状态
    {SEG_DATA, type_bit,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_ONLINE_NONFLOATING_STATUS}, ///< 在线非浮充状态
    {SEG_DATA, type_bit,            ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,              NEED_INTERACT,     BCMU_DATA_ID_FLOATING_STATUS}, ///< 浮充状态
    */
    {SEG_LOOP_OVER},  //电池簇 LOOP结束

    /*BMU信息*/
    {SEG_LOOP, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,  BATT_MOD_NUM}, //BMU LOOP开始
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1,         DOP_0, DATA_DROP, BMU_ANA_DEF_BYTE_NUM, NOT_NEED_INTERACT},///< 电池簇_BMU-模拟量字节长度
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1,         DOP_0, DATA_DROP, BUM_ANA_DEF_NUM,      NOT_NEED_INTERACT}, ///< 电池簇_BMU-模拟量数量
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1,         DOP_0, DATA_DROP, NO_DEF_VALUE,         NEED_INTERACT,     BATT_MOD_CELL_NUM}, ///< 电芯数量
    {SEG_DATA, type_short,          BATT_MOD_CELL_NUM,    DOP_0, DATA_DROP, NO_DEF_VALUE,         NEED_INTERACT,     BCMU_DATA_ID_CELL_TEMP}, ///< 电芯温度
    {SEG_DATA, type_unsigned_short, BATT_MOD_CELL_NUM,    DOP_0, DATA_DROP, NO_DEF_VALUE,         NEED_INTERACT,     BCMU_DATA_ID_CELL_VOL}, ///< 电芯电压
    {SEG_DATA, type_short,          ARRAY_SIZE_1,         DOP_0, DATA_DROP, NO_DEF_VALUE,         NEED_INTERACT,     BCMU_DATA_ID_CELL_TEMP_MAX}, ///< 电芯温度最高
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1,         DOP_0, DATA_DROP, NO_DEF_VALUE,         NEED_INTERACT,     BCMU_DATA_ID_CELL_TEMP_MAX_CHANNEL}, ///< 电芯温度最高通道
    {SEG_DATA, type_short,          ARRAY_SIZE_1,         DOP_0, DATA_DROP, NO_DEF_VALUE,         NEED_INTERACT,     BCMU_DATA_ID_CELL_TEMP_MIN}, ///< 电芯温度最低
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1,         DOP_0, DATA_DROP, NO_DEF_VALUE,         NEED_INTERACT,     BCMU_DATA_ID_CELL_TEMP_MIN_CHANNEL}, ///< 电芯温度最低通道
    {SEG_DATA, type_short,          ARRAY_SIZE_1,         DOP_0, DATA_DROP, NO_DEF_VALUE,         NEED_INTERACT,     BCMU_DATA_ID_CELL_AVERAGE_TEMP}, ///< 电芯平均温度
    {SEG_DATA, type_short,          ARRAY_SIZE_1,         DOP_0, DATA_DROP, NO_DEF_VALUE,         NEED_INTERACT,     BCMU_DATA_ID_CELL_VOL_MAX}, ///< 电芯电压最高
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1,         DOP_0, DATA_DROP, NO_DEF_VALUE,         NEED_INTERACT,     BCMU_DATA_ID_CELL_VOL_MAX_CHANNEL}, ///< 电芯电压最高通道
    {SEG_DATA, type_short,          ARRAY_SIZE_1,         DOP_0, DATA_DROP, NO_DEF_VALUE,         NEED_INTERACT,     BCMU_DATA_ID_CELL_VOL_MIN}, ///< 电芯电压最低
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1,         DOP_0, DATA_DROP, NO_DEF_VALUE,         NEED_INTERACT,     BCMU_DATA_ID_CELL_VOL_MIN_CHANNEL}, ///< 电芯电压最低通道
    {SEG_DATA, type_short,          ARRAY_SIZE_1,         DOP_0, DATA_DROP, NO_DEF_VALUE,         NEED_INTERACT,     BCMU_DATA_ID_CELL_AVERAGE_VOL}, ///< 电芯平均电压
    {SEG_DATA, type_short,          ARRAY_SIZE_1,         DOP_0, DATA_DROP, NO_DEF_VALUE,         NEED_INTERACT,     BCMU_DATA_ID_MOD_VOL_TOTAL}, ///< 模块总电压

    /* 数字量 */
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1,         DOP_0, DATA_DROP, BMU_DIG_DEF_BYTE_NUM, NOT_NEED_INTERACT},///< 电池簇_BMU-模拟量字节长度
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1,         DOP_0, DATA_DROP, BUM_DIG_DEF_NUM,      NOT_NEED_INTERACT}, ///< 电池簇_BMU-模拟量数量
    {SEG_DATA, type_bit,            BATT_MOD_CELL_NUM,    DOP_0, DATA_DROP, NO_DEF_VALUE,         NEED_INTERACT,     BCMU_DATA_ID_CELL_BALANCED_STATUS}, ///< 电芯均衡状态
    {SEG_DATA, type_bit,            BUM_DIG_RESERVED_NUM, DOP_0, DATA_DROP, NO_DEF_VALUE,        NOT_NEED_INTERACT}, ///< 保留位
    {SEG_DATA, type_bit,            ARRAY_SIZE_1,         DOP_0, DATA_DROP, NO_DEF_VALUE,       NOT_NEED_INTERACT}, ///< 风扇1状态
    {SEG_DATA, type_bit,            ARRAY_SIZE_1,         DOP_0, DATA_DROP, NO_DEF_VALUE,       NOT_NEED_INTERACT}, ///< 风扇2状态
    {SEG_DATA, type_bit,            2,                 DOP_0, DATA_DROP, NO_DEF_VALUE,            NOT_NEED_INTERACT}, ///< 保留
    {SEG_LOOP_OVER},
};

/* 获取厂家信息命令答复信息 */
static data_info_id_verison_t reply_fac_data_info[] = {
    // BCMU 信息
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1,           DOP_0, DATA_DROP, BCMU_CONFIG_BYTE_LEN,  NOT_NEED_INTERACT},  ///< BCMU 配置字节长度
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1,           DOP_0, DATA_DROP, BCMU_CONFIG_NUM,       NOT_NEED_INTERACT},  ///< BCMU 配置数量
    {SEG_DATA, type_date,           ARRAY_SIZE_1,           DOP_0, DATA_DROP, NO_DEF_VALUE,          NEED_INTERACT,     BCMU_FAC_ID_VERSION_DATE},       ///< BCMU 版本日期
    {SEG_DATA, type_char,           BCMU_SYS_NAME_LEN,      DOP_0, DATA_DROP, NO_DEF_VALUE,          NEED_INTERACT,     BCMU_FAC_ID_SYSTEM_NAME},     ///< BCMU 系统名称
    {SEG_DATA, type_char,           BCMU_SOFT_VER_LEN,      DOP_0, DATA_DROP, NO_DEF_VALUE,          NEED_INTERACT,     BCMU_FAC_ID_SOFTWARE_VERSION},    ///< BCMU 软件版本
    {SEG_DATA, type_char,           BCMU_SERIES_NUM_LEN,    DOP_0, DATA_DROP, NO_DEF_VALUE,          NEED_INTERACT,     BCMU_FAC_ID_BCMU_SERIAL_NUMBER},   ///< BCMU 出厂序列号
    {SEG_DATA, type_char,           BMS_SERIES_NUM_LEN,     DOP_0, DATA_DROP, NO_DEF_VALUE,          NEED_INTERACT,     BCMU_FAC_ID_SERIAL_NUM_OF_WHOLE_DEV_DELIVERED_FROM_FAC},    ///< 整机出厂系列号
    {SEG_DATA, type_date,           ARRAY_SIZE_1,           DOP_0, DATA_DROP, NO_DEF_VALUE,          NEED_INTERACT,     BCMU_FAC_ID_BOOT_DATE},          ///< BCMU BOOT 日期
 
    {SEG_LOOP_OVER},
    // BMU 信息
    {SEG_LOOP, type_char,           ARRAY_SIZE_1,           DOP_0, DATA_DROP, NO_DEF_VALUE,          NOT_NEED_INTERACT, BATT_MOD_NUM},         ///< 循环开始标志
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1,           DOP_0, DATA_DROP, BMU_CONFIG_BYTE_LEN,   NOT_NEED_INTERACT},                  ///< BMU 配置字节长度
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1,           DOP_0, DATA_DROP, BMU_CONFIG_NUM,        NOT_NEED_INTERACT},                  ///< BMU 配置数量
    {SEG_DATA, type_char,           BMU_SOFT_VER_LEN,       DOP_0, DATA_DROP, NO_DEF_VALUE,          NEED_INTERACT,     BCMU_FAC_ID_BMU_SOFTWARE_VER},              ///< BMU 软件版本
    {SEG_DATA, type_date,           ARRAY_SIZE_1,           DOP_0, DATA_DROP, NO_DEF_VALUE,          NEED_INTERACT,     BCMU_FAC_ID_BMU_SOFTWARE_DATE},                    ///< BMU 软件日期
   // {SEG_DATA, type_char,           BMU_ASSET_MANAGE_LEN,   DOP_0, DATA_DROP, NO_DEF_VALUE,          NEED_INTERACT,     &bsmu_data.bsmu_get_fac_data.bmu_fac_data[0].bmu_asset_manage[0]},              ///< BMU 资产管理信息
    {SEG_LOOP_OVER},
    // 整机信息
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1,           DOP_0, DATA_DROP, BMS_CONFIG_BYTE_LEN,   NOT_NEED_INTERACT},                        ///< 整机配置字节长度
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1,           DOP_0, DATA_DROP, BMS_CONFIG_NUM,        NOT_NEED_INTERACT},                        ///< 整机配置数量
    {SEG_DATA, type_date,           ARRAY_SIZE_1,           DOP_0, DATA_DROP, NO_DEF_VALUE,          NEED_INTERACT,     BCMU_FAC_ID_DEVICE_PRODUCTION_DATE},                   ///< 整机生产日期
    {SEG_DATA, type_date,           ARRAY_SIZE_1,           DOP_0, DATA_DROP, NO_DEF_VALUE,          NEED_INTERACT,     BCMU_FAC_ID_START_DATE},                     ///< 整机启用日期
    {SEG_DATA, type_char,           BMS_TYPE_LEN,           DOP_0, DATA_DROP, NO_DEF_VALUE,          NEED_INTERACT,     BCMU_FAC_ID_BMS_MODEL},                        ///< BMS 型号
};

static data_info_id_verison_t cmd_parse_para_set_data_info[] = {
  {0},
  /* TODO:重新开发
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_BCMU_THRESHHOLE_DEF_BYTE_NUM, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_BCMU_THRESHHOLE_DEF_NUM, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_OVER_VOL_THRESHOLD}, // 电芯过压告警阈值 
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_OVER_VOL_THRESHOLD + 1}, 
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_OVER_VOL_THRESHOLD + 2}, 
    {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_UNDER_VOL_THRESHOLD},  // 电芯欠压告警阈值 
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_UNDER_VOL_THRESHOLD + 1}, 
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,  BCMU_PARA_ID_CELL_UNDER_VOL_THRESHOLD + 2}, 
    {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_VOL_DIFF_OVER_THRESHOLD}, // 电芯压差过大告警阈值 
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_VOL_DIFF_OVER_THRESHOLD + 1}, 
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_VOL_DIFF_OVER_THRESHOLD + 2}, 
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_CHG_HIGH_TEMP_THRESHOLD}, // 电芯充电高温告警阈值 
    {SEG_DATA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_CHG_HIGH_TEMP_THRESHOLD + 1}, 
    {SEG_DATA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_CHG_HIGH_TEMP_THRESHOLD + 2}, 
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_DISCHG_HIGH_TEMP_THRESHOLD}, // 电芯放电高温告警阈值 
    {SEG_DATA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_DISCHG_HIGH_TEMP_THRESHOLD + 1}, 
    {SEG_DATA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_DISCHG_HIGH_TEMP_THRESHOLD + 2}, 
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_CHG_LOW_TEMP_THRESHOLD}, // 电芯充电低温告警阈值 
     {SEG_DATA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_CHG_LOW_TEMP_THRESHOLD + 1}, 
     {SEG_DATA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_CHG_LOW_TEMP_THRESHOLD + 2},
   {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_DISCHG_LOW_TEMP_THRESHOLD}, // 电芯放电低温告警阈值 
     {SEG_DATA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_DISCHG_LOW_TEMP_THRESHOLD + 1}, 
     {SEG_DATA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_DISCHG_LOW_TEMP_THRESHOLD + 2}, 
   {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_OVER_TEMP_DIFF_THRESHOLD},  // 电芯温差过大告警阈值 
     {SEG_DATA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_OVER_TEMP_DIFF_THRESHOLD + 1}, 
     {SEG_DATA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_OVER_TEMP_DIFF_THRESHOLD + 2}, 
   {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_BATT_CLUSTER_LOW_SOC_ALM},  // 电池簇SOC低告警 
   {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_BATT_CLUSTER_LOW_SOC_PROTECT},  // 电池簇SOC低保护 
   {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_BATT_CLUSTER_LOW_SOH_ALM}, // 电池簇SOH低告警 
   {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_BATT_CLUSTER_LOW_SOH_PROTECT}, // 电池簇SOH低保护 
   {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_BATT_CLUSTER_ENV_TEMP_HIGH_PROTECT}, // 电池簇环境温度高保护 
   {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_BATT_CLUSTER_ENV_TEMP_LOW_PROTECT},  // 电池簇环境温度低保护 
   {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, DEF_BMU_CELL_DAMAGE_PROTECT, NEED_INTERACT, BCMU_PARA_ID_BMU_CELL_DAMAGE_PROTECT}, // BMU电芯损坏保护 
   {SEG_SYSTEM_PARA, type_unsigned_int,     ARRAY_SIZE_1, DOP_0, DATA_DROP, DEF_INSULATION_RESISTANCE_ALM_LEVEL_1, NEED_INTERACT, BCMU_PARA_ID_INSULATION_RESISTANCE_ALM_THRESHOLD}, // 绝缘电阻告警阈值 
     {SEG_DATA, type_unsigned_int,     ARRAY_SIZE_1, DOP_0, DATA_DROP, DEF_INSULATION_RESISTANCE_ALM_LEVEL_2, NEED_INTERACT, BCMU_PARA_ID_INSULATION_RESISTANCE_ALM_THRESHOLD + 1}, 
     {SEG_DATA, type_unsigned_int,     ARRAY_SIZE_1, DOP_0, DATA_DROP, DEF_INSULATION_RESISTANCE_ALM_LEVEL_3, NEED_INTERACT, BCMU_PARA_ID_INSULATION_RESISTANCE_ALM_THRESHOLD + 2},
   {SEG_SYSTEM_PARA, type_unsigned_int,     ARRAY_SIZE_1, DOP_0, DATA_DROP, DEF_CO_CONCENTRATION_ALM_LEVEL_1, NEED_INTERACT, BCMU_PARA_ID_CO_CONCENTRATION_ALM_THRESHOLD}, // 一氧化碳浓度告警阈值 
     {SEG_DATA, type_unsigned_int,     ARRAY_SIZE_1, DOP_0, DATA_DROP, DEF_CO_CONCENTRATION_ALM_LEVEL_2, NEED_INTERACT, BCMU_PARA_ID_CO_CONCENTRATION_ALM_THRESHOLD + 1}, 
     {SEG_DATA, type_unsigned_int,     ARRAY_SIZE_1, DOP_0, DATA_DROP, DEF_CO_CONCENTRATION_ALM_LEVEL_3, NEED_INTERACT, BCMU_PARA_ID_CO_CONCENTRATION_ALM_THRESHOLD + 2},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_BALANCE_VOL_DIFF},   // 均衡启动电压差阈值设置 
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_BALANCE_LOW_VOL},    // 均衡最低启动电压值设置  
   
    {SEG_LEN,  type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_DC_DC_THRESHHOLE_DEF_BYTE_NUM, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,    ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_DC_DC_THRESHHOLE_DEF_NUM, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, DEF_BATT_OVER_VOL_PROTECT_THRESH, NEED_INTERACT, BCMU_PARA_ID_CELL_OVER_VOL_PROTECT_THRESH}, // 电池过压保护阈值 
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_SET_CHG_LIMIT_CURR_COE}, // 设定充电限电流系数 
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_SET_DISCHG_LIMIT_CURR}, // 设定放电限电流 
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, DEF_SET_CHG_VOL, NEED_INTERACT, BCMU_PARA_ID_SET_CHG_VOL}, // 设定充电电压 
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_SET_DISCHG_VOL}, // 设定放电电压 
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, DEF_BUS_OVER_VOL_PROTECT_THRESH, NEED_INTERACT, BCMU_PARA_ID_BUS_OVER_VOL_PROTECT_THRESH}, // 母排过压保护阈值 
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, DEF_BATT_UNDER_VOL_PROTECT_THRESH, NEED_INTERACT, BCMU_PARA_ID_BATT_UNDER_VOL_PROTECT_THRESH},  // 电池欠压保护阈值 
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, DEF_BUS_UNDER_VOL_PROTECT_THRESH, NEED_INTERACT, BCMU_PARA_ID_BUS_UNDER_VOL_PROTECT_THRESH}, // 母排欠压保护阈值 
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, DEF_BUS_AND_BATT_DIFF_PROTECT_THRESH, NEED_INTERACT, BCMU_PARA_ID_BUS_AND_BATT_DIFF_PROTECT_THRESH}, // 母排与电池差值保护阈值 
    {SEG_SYSTEM_PARA, type_unsigned_char,    ARRAY_SIZE_1, DOP_0, DATA_DROP, DEF_BATT_NUM, NEED_INTERACT, BCMU_PARA_ID_BATTERY_NUM},  // 电池个数 
    {SEG_SYSTEM_PARA, type_unsigned_char,    ARRAY_SIZE_1, DOP_0, DATA_DROP, DEF_BCMU_ADDR, NEED_INTERACT, BCMU_PARA_ID_BCMU_ADDR}, // BCMU地址 
    //{SEG_DATA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, &bsmu_data.parse_bsmu_param.bus_chg_low_vol}, 
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_DISCHG_CUR_BALANCE_CUR},  // 放电均流电流 
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_CHG_HIGH_TEMP_LIMIT_CURR_CEO},  // 电芯充电高温限电流系数 
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_POWER_DOWN_VOL_THRESH},
    */
};



static data_info_id_verison_t cmd_parse_single_para_set_data_info[] = {
  {0},
  /* TODO: 重新开发
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_OVER_VOL_THRESHOLD},//电芯过压告警阈值
    
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_OVER_VOL_THRESHOLD + 1}, //电芯过压告警（二级）
  
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_OVER_VOL_THRESHOLD + 2}, //电芯过压告警（三级）
  
  {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
  {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT},    
  {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_UNDER_VOL_THRESHOLD}, //电芯欠压告警阈值
  
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_UNDER_VOL_THRESHOLD + 1},//电芯欠压告警（二级）
  
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_UNDER_VOL_THRESHOLD + 2}, //电芯欠压告警（三级）
  
  {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
  {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
  {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_VOL_DIFF_OVER_THRESHOLD}, //电芯压差过大告警阈值
  
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_VOL_DIFF_OVER_THRESHOLD + 1}, //电芯压差过大告警（二级）
  
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_VOL_DIFF_OVER_THRESHOLD + 2}, //电芯压差过大告警（三级）
  
  {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
  {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
  {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_CHG_HIGH_TEMP_THRESHOLD},//电芯充电高温告警阈值
  
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_VOL_DIFF_OVER_THRESHOLD + 1}, //电芯充电高温告警（二级）
  
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_VOL_DIFF_OVER_THRESHOLD + 2}, //电芯充电高温告警（三级）
  
  {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
  {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
  {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_DISCHG_HIGH_TEMP_THRESHOLD}, //电芯放电高温告警（一级）
  
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_DISCHG_HIGH_TEMP_THRESHOLD + 1}, //电芯放电高温告警（二级）
  
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_DISCHG_HIGH_TEMP_THRESHOLD + 2}, //电芯放电高温告警（三级）
  
  {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
  {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
  {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_CHG_LOW_TEMP_THRESHOLD}, //电芯充电低温告警（一级）
  
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_CHG_LOW_TEMP_THRESHOLD + 1}, //电芯充电低温告警（二级）
  
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_CHG_LOW_TEMP_THRESHOLD + 2},//电芯充电低温告警（三级）
  
  
  {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
  {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
  {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_DISCHG_LOW_TEMP_THRESHOLD}, //电芯放电低温告警（一级）
  
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_DISCHG_LOW_TEMP_THRESHOLD + 1}, //电芯放电低温告警（二级）
  
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_DISCHG_LOW_TEMP_THRESHOLD + 2}, //电芯放电低温告警（三级）
  
  {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
  {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
  {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_OVER_TEMP_DIFF_THRESHOLD}, //电芯温差过大告警（一级）
  
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_OVER_TEMP_DIFF_THRESHOLD + 1}, //电芯温差过大告警（二级）
  
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_OVER_TEMP_DIFF_THRESHOLD + 2}, //电芯温差过大告警（三级）
    
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 2, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_OVER_VOL_PROTECT_THRESH}, //电池过压保护阈值
    
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 2, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_SET_CHG_LIMIT_CURR_COE}, //设定充电限电流系数
    
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 2, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_SET_DISCHG_LIMIT_CURR}, //设定放电限电流
    
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 2, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_SET_CHG_VOL}, //设定充电电压
    
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 2, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_SET_DISCHG_VOL}, //设定放电电压
    
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 2, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_BUS_OVER_VOL_PROTECT_THRESH}, //母排过压保护阈值
    
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 2, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_BATT_UNDER_VOL_PROTECT_THRESH}, //电池欠压保护阈值
    
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 2, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_BUS_UNDER_VOL_PROTECT_THRESH}, //母排欠压保护阈值
    
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 2, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_BUS_AND_BATT_DIFF_PROTECT_THRESH}, //母排与电池差值保护阈值
    
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_BATTERY_NUM}, //电池个数
    
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_unsigned_char,    ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_BCMU_ADDR},//BCMU地址 
    
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_BATT_CLUSTER_LOW_SOC_ALM}, //电池簇SOC低告警
    
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_BATT_CLUSTER_LOW_SOC_PROTECT}, //电池簇SOC低保护
    
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_BATT_CLUSTER_LOW_SOH_ALM}, //电池簇SOH低告警
    
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_BATT_CLUSTER_LOW_SOH_PROTECT}, //电池簇SOH低保护
    
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_BATT_CLUSTER_ENV_TEMP_HIGH_PROTECT}, //电池簇环境温度高保护
    
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_BATT_CLUSTER_ENV_TEMP_LOW_PROTECT}, //电池簇环境温度低保护
    
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, DEF_BMU_CELL_DAMAGE_PROTECT, NEED_INTERACT, BCMU_PARA_ID_BMU_CELL_DAMAGE_PROTECT}, //BMU电芯损坏保护
    
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_INSULATION_RESISTANCE_ALM_THRESHOLD},// 绝缘电阻告警阈值 
    
   {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
   {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
   {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_INSULATION_RESISTANCE_ALM_THRESHOLD + 1}, //绝缘电阻告警（二级）
 
   {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
   {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
   {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_INSULATION_RESISTANCE_ALM_THRESHOLD + 2},//绝缘电阻告警（三级）
 
   {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
   {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
   {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CO_CONCENTRATION_ALM_THRESHOLD},// 一氧化碳浓度告警阈值 
 
   {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
   {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
   {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CO_CONCENTRATION_ALM_THRESHOLD + 1}, //一氧化碳浓度告警（二级）
 
   {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 4, NOT_NEED_INTERACT}, 
   {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
   {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CO_CONCENTRATION_ALM_THRESHOLD + 2},   //一氧化碳浓度告警（三级）

    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 2, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_BALANCE_VOL_DIFF},   //均衡启动电压差阈值设置

    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 2, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 2, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_BALANCE_LOW_VOL},   //均衡最低启动电压值设置

    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 2, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_DISCHG_CUR_BALANCE_CUR},   //放电均流电流

    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 2, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_unsigned_short,  ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_CELL_CHG_HIGH_TEMP_LIMIT_CURR_CEO},  //电芯高温限电流系数

    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 2, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_PARA_ID_POWER_DOWN_VOL_THRESH},  //停电电压阈值
    */
};

static data_info_id_verison_t cmd_parse_para_alm_level_data_info[] = {
  {0},
  /* TODO:重新开发
    {SEG_LEN,  type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_ALM_LEVEL_CONFIG_DEF_BYTE_NUM, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_ALM_LEVEL_CONFIG_DEF_NUM, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_OVER_VOL_ALM}, //电芯过压告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_OVER_VOL_ALM + 1}, 
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_OVER_VOL_ALM + 2}, 
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_UNDER_VOL_ALM}, //电芯欠压告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_UNDER_VOL_ALM + 1}, 
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_UNDER_VOL_ALM + 2}, 
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_VOL_DIFF_OVER_ALM}, //电芯压差过大告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_VOL_DIFF_OVER_ALM + 1}, 
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_VOL_DIFF_OVER_ALM + 2}, 
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_CHG_HIGH_TEMP_ALM}, //电芯充电高温告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_CHG_HIGH_TEMP_ALM + 1}, 
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_CHG_HIGH_TEMP_ALM + 2}, 
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_DISCHG_HIGH_TEMP_ALM}, //电芯放电高温告警
      {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_DISCHG_HIGH_TEMP_ALM + 1}, 
      {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_DISCHG_HIGH_TEMP_ALM + 2}, 
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_CHG_LOW_TEMP_ALM},  //电芯充电低温告警
      {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_CHG_LOW_TEMP_ALM + 1}, 
      {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_CHG_LOW_TEMP_ALM + 2},
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_DISCHG_LOW_TEMP_ALM}, //电芯放电低温告警
      {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_DISCHG_LOW_TEMP_ALM + 1}, 
      {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_DISCHG_LOW_TEMP_ALM + 2}, 
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_OVER_TEMP_DIFF_ALM}, //电芯温差过大告警
      {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_OVER_TEMP_DIFF_ALM + 1}, 
      {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_OVER_TEMP_DIFF_ALM + 2}, 
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CAPA_SOFT_OVER_VOL_ALM}, //电池电容软过压告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CAPACITY_HARD_OVER_VOLT_ALM},  //电池电容硬过压告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CAPA_UNDER_VOL_ALM}, 	 //电池电容欠压告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BUS_SOFT_OVER_VOLT_ALM}, //母排软过压告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BUS_HARD_OVER_VOLT_ALM}, //母排硬过压告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BUS_UNDER_VOLT_ALM},  //母排欠压告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BUS_VOL_OUT_OF_RANGE_ALM}, 	//母排电压超范围告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_COMPEN_CAPA_SOFT_OVER_VOL_ALM}, //补偿电容软过压告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_COMPEN_CAPA_HARD_OVER_VOL_ALM}, //补偿电容硬过压告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CLLC_OVER_VOLT_ALM},  //CLLC过压告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CLLC_UNDER_VOLT_ALM}, 	 //CLLC欠压告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_MOD_CONN_FAILURE_ALM},  //模块连接不到位告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CHARGE_OVER_CURR_ALM}, //充电过流告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_DISCHARGE_OVER_CURR_ALM}, //放电过流告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BAD_CURR_SHARE_ALM}, //均流不良告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CHG_CIRCUIT_DAMAGE_ALM}, //充电回路损坏告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_DISCHG_CIRCUIT_DAMAGE_ALM}, //放电回路损坏告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CURR_LIMIT_CIRCUIT_DAMAGE_ALM}, //限流回路损坏告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_OUTPUT_OVER_LOAD}, //输出过载
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BUS_SHORT_CIRCUIT_ALM}, 		 //BUS短路告警		
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, //按保留位上送
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_EEPROM_FAULT_ALM}, //EEPROM故障
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_MODULE_CAN_COMM_FAULT_ALM},  //模块间CAN通讯异常告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_HIGH_ENV_TEMP_ALM}, 	//环境温度高告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BOARD_INTER_OVER_TEMP_PROTECT_ALM}, //单板内部过温保护告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_SLOW_START_EXCEPTION}, //缓启动异常
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CHARGE_UNDER_VOLT_PROTECT_ALM}, //电池充电欠压保护告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_POWER_CONNECTOR_SELF_INSPECTION_RESISTANCE_ABNORMAL_ALM},  //功率连接器自检阻值异常
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_ABNORMAL_RELAY_CLOSING_PROTECTION}, //继电器吸合异常保护
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_EXTER_BATT_OVER_VOL_ALM}, //外部电池过压告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_EXTER_BATT_UNDER_VOL_ALM}, //外部电池欠压告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_LARGE_VOL_DIFFERENCE_BETWEEN_BUS_AND_BATT_ALM},//母排与电池电压差值大告警
	{SEG_DATA, type_bit,   ARRAY_SIZE_8, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
	{SEG_DATA, type_bit,   ARRAY_SIZE_8, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CLUSTER_LOW_SOC_ALM}, //电池簇SOC低告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CLUSTER_LOW_SOC_PROTECT}, //电池簇SOC低告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CLUSTER_LOW_SOH_ALM}, //电池簇SOH低告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CLUSTER_LOW_SOH_PROTECT},  //电池簇SOH低保护
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CLUSTER_ENV_TEMP_HIGH_PROTECT}, //电池簇环境温度高保护
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CLUSTER_ENV_TEMP_LOW_PROTECT}, //电池簇环境温度低保护
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BMU_CELL_DAMAGE_PROTECT},  //BMU电芯损坏保护
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BMU_CELL_TEMP_ABNORMAL},  //BMU电芯温度异常
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BMU_CELL_TEMP_INVALID}, //BMU电芯温度无效
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BMU_CELL_VOL_SAMPLING_FAILURE},  //BMU电芯电压采样失效
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BMU_EQUALIZE_CIRCUIT_FAULT_ALM}, //BMU均衡电路故障告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BMU_COMM_FAIL}, 	 //BMU通信断
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_INSULATION_RESISTANCE_ALM},//绝缘电阻告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_INSULATION_RESISTANCE_ALM + 1}, 
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_INSULATION_RESISTANCE_ALM + 2},
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CO_CONCENTRATION_ALM},//一氧化碳浓度告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CO_CONCENTRATION_ALM + 1}, 
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CO_CONCENTRATION_ALM + 2},   
	{SEG_DATA, type_bit,   ARRAY_SIZE_8, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
  */
};

static data_info_id_verison_t cmd_parse_para_alm_relay_data_info[] = {
  {0},
  /** TODO:重新开发
	{SEG_LEN,  type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_ALM_RELAY_CONFIG_DEF_BYTE_NUM, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_ALM_RELAY_CONFIG_DEF_NUM, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_OVER_VOL_ALM}, //电芯过压告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_OVER_VOL_ALM + 1}, 
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_OVER_VOL_ALM + 2}, 
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_UNDER_VOL_ALM},  //电芯欠压告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_UNDER_VOL_ALM + 1}, 
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_UNDER_VOL_ALM + 2}, 
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_VOL_DIFF_OVER_ALM},  //电芯压差过大告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_VOL_DIFF_OVER_ALM + 1}, 
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_VOL_DIFF_OVER_ALM + 2}, 
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_CHG_HIGH_TEMP_ALM},  //电芯充电高温告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_CHG_HIGH_TEMP_ALM + 1}, 
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_CHG_HIGH_TEMP_ALM + 2}, 
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_DISCHG_HIGH_TEMP_ALM}, //电芯放电高温告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_DISCHG_HIGH_TEMP_ALM + 1}, 
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_DISCHG_HIGH_TEMP_ALM + 2}, 
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_CHG_LOW_TEMP_ALM},  //电芯充电低温告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_CHG_LOW_TEMP_ALM + 1}, 
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_CHG_LOW_TEMP_ALM + 2},
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_DISCHG_LOW_TEMP_ALM},  //电芯放电低温告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_DISCHG_LOW_TEMP_ALM + 1}, 
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_DISCHG_LOW_TEMP_ALM + 2}, 
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_OVER_TEMP_DIFF_ALM},  //电芯温差过大告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_OVER_TEMP_DIFF_ALM + 1}, 
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_OVER_TEMP_DIFF_ALM + 2}, 
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CAPA_SOFT_OVER_VOL_ALM},  //电池电容软过压告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CAPACITY_HARD_OVER_VOLT_ALM}, //电池电容硬过压告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CAPA_UNDER_VOL_ALM}, 	 //电池电容欠压告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BUS_SOFT_OVER_VOLT_ALM},  //母排软过压告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BUS_HARD_OVER_VOLT_ALM}, //母排硬过压告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BUS_UNDER_VOLT_ALM},  //母排欠压告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BUS_VOL_OUT_OF_RANGE_ALM}, 	//母排电压超范围告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_COMPEN_CAPA_SOFT_OVER_VOL_ALM},  //补偿电容软过压告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_COMPEN_CAPA_HARD_OVER_VOL_ALM}, //补偿电容硬过压告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CLLC_OVER_VOLT_ALM}, //CLLC过压告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CLLC_UNDER_VOLT_ALM}, 	 //CLLC欠压告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_MOD_CONN_FAILURE_ALM},  //模块连接不到位告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CHARGE_OVER_CURR_ALM},  //充电过流告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_DISCHARGE_OVER_CURR_ALM},  //放电过流告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BAD_CURR_SHARE_ALM},  //均流不良告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CHG_CIRCUIT_DAMAGE_ALM},  //充电回路损坏告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_DISCHG_CIRCUIT_DAMAGE_ALM},   //放电回路损坏告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CURR_LIMIT_CIRCUIT_DAMAGE_ALM},  //限流回路损坏告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_OUTPUT_OVER_LOAD}, //输出过载
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BUS_SHORT_CIRCUIT_ALM}, 		 //BUS短路告警		
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_EEPROM_FAULT_ALM}, //EEPROM故障
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_MODULE_CAN_COMM_FAULT_ALM},  //模块间CAN通讯异常告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_HIGH_ENV_TEMP_ALM}, 	//环境温度高告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BOARD_INTER_OVER_TEMP_PROTECT_ALM},  //单板内部过温保护告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_SLOW_START_EXCEPTION}, //缓启动异常
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CHARGE_UNDER_VOLT_PROTECT_ALM}, //电池充电欠压保护告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_POWER_CONNECTOR_SELF_INSPECTION_RESISTANCE_ABNORMAL_ALM}, //功率连接器自检阻值异常
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_ABNORMAL_RELAY_CLOSING_PROTECTION}, //继电器吸合异常保护
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_EXTER_BATT_OVER_VOL_ALM},  //外部电池过压告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_EXTER_BATT_UNDER_VOL_ALM},  //外部电池欠压告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_LARGE_VOL_DIFFERENCE_BETWEEN_BUS_AND_BATT_ALM},  //母排与电池电压差值大告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CLUSTER_LOW_SOC_ALM},  //电池簇SOC低告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CLUSTER_LOW_SOC_PROTECT}, //电池簇SOC低保护
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CLUSTER_LOW_SOH_ALM}, //电池簇SOH低告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CLUSTER_LOW_SOH_PROTECT},  //电池簇SOH低保护
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CLUSTER_ENV_TEMP_HIGH_PROTECT}, //电池簇环境温度高保护
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CLUSTER_ENV_TEMP_LOW_PROTECT}, //电池簇环境温度低保护
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BMU_CELL_DAMAGE_PROTECT},  //BMU电芯损坏保护
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BMU_CELL_TEMP_ABNORMAL}, //BMU电芯温度异常
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BMU_CELL_TEMP_INVALID}, //BMU电芯温度无效
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BMU_CELL_VOL_SAMPLING_FAILURE}, //BMU电芯电压采样失效
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BMU_EQUALIZE_CIRCUIT_FAULT_ALM},  //BMU均衡电路故障告警
	{SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BMU_COMM_FAIL}, 	//BMU通信断
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_INSULATION_RESISTANCE_ALM},//绝缘电阻告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_INSULATION_RESISTANCE_ALM + 1}, 
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_INSULATION_RESISTANCE_ALM + 2},
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CO_CONCENTRATION_ALM},//一氧化碳浓度告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CO_CONCENTRATION_ALM + 1}, 
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CO_CONCENTRATION_ALM + 2},   
    */
};


/*查询实时告警数据*/
static data_info_id_verison_t cmd_pack_real_alm_info[] = {
  {0},
  /* TODO:重新开发
    {SEG_LOOP, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,  BATT_CLUSTER_NUM}, //电池簇 LOOP开始
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, ALM_DEF_BYTE_NUM, NOT_NEED_INTERACT},  ///< // 实时告警字节数
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ALM_DEF_NUM,      NOT_NEED_INTERACT},  ///< // 实时告警数量
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_OVER_VOL_ALM},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_OVER_VOL_ALM + 1},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_OVER_VOL_ALM + 2},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_UNDER_VOL_ALM},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_UNDER_VOL_ALM + 1},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_UNDER_VOL_ALM + 2},
  {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_VOL_DIFF_OVER_ALM},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_VOL_DIFF_OVER_ALM + 1},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_VOL_DIFF_OVER_ALM + 2},
  {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_CHG_HIGH_TEMP_ALM},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_CHG_HIGH_TEMP_ALM + 1},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_CHG_HIGH_TEMP_ALM + 2},
  {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_DISCHG_HIGH_TEMP_ALM},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_DISCHG_HIGH_TEMP_ALM + 1},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_DISCHG_HIGH_TEMP_ALM + 2},
  {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_CHG_LOW_TEMP_ALM},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_CHG_LOW_TEMP_ALM + 1},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_CHG_LOW_TEMP_ALM + 2},
  {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_DISCHG_LOW_TEMP_ALM},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_DISCHG_LOW_TEMP_ALM + 1},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_DISCHG_LOW_TEMP_ALM + 2},
  {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_OVER_TEMP_DIFF_ALM},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_OVER_TEMP_DIFF_ALM + 1},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_OVER_TEMP_DIFF_ALM + 2},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CAPA_SOFT_OVER_VOL_ALM},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CAPACITY_HARD_OVER_VOLT_ALM},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CAPA_UNDER_VOL_ALM},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BUS_SOFT_OVER_VOLT_ALM},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BUS_HARD_OVER_VOLT_ALM},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BUS_UNDER_VOLT_ALM},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BUS_VOL_OUT_OF_RANGE_ALM},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_COMPEN_CAPA_SOFT_OVER_VOL_ALM},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_COMPEN_CAPA_HARD_OVER_VOL_ALM},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CLLC_OVER_VOLT_ALM},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CLLC_UNDER_VOLT_ALM},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_MOD_CONN_FAILURE_ALM},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CHARGE_OVER_CURR_ALM},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_DISCHARGE_OVER_CURR_ALM},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BAD_CURR_SHARE_ALM},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CHG_CIRCUIT_DAMAGE_ALM},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_DISCHG_CIRCUIT_DAMAGE_ALM},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CURR_LIMIT_CIRCUIT_DAMAGE_ALM}, 
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_OUTPUT_OVER_LOAD}, 
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BUS_SHORT_CIRCUIT_ALM},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_EEPROM_FAULT_ALM},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_MODULE_CAN_COMM_FAULT_ALM}, 
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_HIGH_ENV_TEMP_ALM},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BOARD_INTER_OVER_TEMP_PROTECT_ALM}, 
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_SLOW_START_EXCEPTION}, 
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CHARGE_UNDER_VOLT_PROTECT_ALM}, 
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_POWER_CONNECTOR_SELF_INSPECTION_RESISTANCE_ABNORMAL_ALM}, 
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_ABNORMAL_RELAY_CLOSING_PROTECTION}, 
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_EXTER_BATT_OVER_VOL_ALM}, 
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_EXTER_BATT_UNDER_VOL_ALM}, 
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_LARGE_VOL_DIFFERENCE_BETWEEN_BUS_AND_BATT_ALM},
    {SEG_DATA, type_bit,   ARRAY_SIZE_8, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit,   ARRAY_SIZE_8, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CLUSTER_LOW_SOC_ALM}, 
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CLUSTER_LOW_SOC_PROTECT}, 
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CLUSTER_LOW_SOH_ALM}, 
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CLUSTER_LOW_SOH_PROTECT}, 
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CLUSTER_ENV_TEMP_HIGH_PROTECT}, 
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CLUSTER_ENV_TEMP_LOW_PROTECT}, 
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BMU_CELL_DAMAGE_PROTECT}, 
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BMU_CELL_TEMP_ABNORMAL}, 
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BMU_CELL_TEMP_INVALID}, 
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BMU_CELL_VOL_SAMPLING_FAILURE}, 
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BMU_EQUALIZE_CIRCUIT_FAULT_ALM}, 
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BMU_COMM_FAIL}, 	
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_INSULATION_RESISTANCE_ALM},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_INSULATION_RESISTANCE_ALM + 1}, 
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_INSULATION_RESISTANCE_ALM + 2},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CO_CONCENTRATION_ALM},
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CO_CONCENTRATION_ALM + 1}, 
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CO_CONCENTRATION_ALM + 2},   
    {SEG_DATA, type_bit,   ARRAY_SIZE_6, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_LOOP_OVER},
    */
};

/* 获取告警级别配置 */
static data_info_id_verison_t cmd_pack_para_alm_level_data_info[] = {
  {0},
  /* 重新开发
    {SEG_LEN,  type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_ALM_LEVEL_CONFIG_DEF_BYTE_NUM, NOT_NEED_INTERACT}, 
    {SEG_NUM,  type_unsigned_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_ALM_LEVEL_CONFIG_DEF_NUM, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_OVER_VOL_ALM}, //电芯过压告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_OVER_VOL_ALM + 1},
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_OVER_VOL_ALM + 2},
  {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_UNDER_VOL_ALM},//电芯欠压告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_UNDER_VOL_ALM + 1},
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_UNDER_VOL_ALM + 2},
  {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_VOL_DIFF_OVER_ALM}, //电芯压差过大告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_VOL_DIFF_OVER_ALM + 1},
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_VOL_DIFF_OVER_ALM + 2},
  {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_CHG_HIGH_TEMP_ALM},//电芯充电高温告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_CHG_HIGH_TEMP_ALM + 1},
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_CHG_HIGH_TEMP_ALM + 2},
  {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_DISCHG_HIGH_TEMP_ALM},//电芯放电高温告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_DISCHG_HIGH_TEMP_ALM + 1},
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_DISCHG_HIGH_TEMP_ALM + 2},
  {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_CHG_LOW_TEMP_ALM}, //电芯充电低温告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_CHG_LOW_TEMP_ALM + 1},
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_CHG_LOW_TEMP_ALM + 2},
  {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_DISCHG_LOW_TEMP_ALM}, //电芯放电低温告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_DISCHG_LOW_TEMP_ALM + 1},
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_DISCHG_LOW_TEMP_ALM + 2},
  {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_OVER_TEMP_DIFF_ALM},//电芯温差过大告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_OVER_TEMP_DIFF_ALM + 1},
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CELL_OVER_TEMP_DIFF_ALM + 2},
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CAPA_SOFT_OVER_VOL_ALM},//电池电容软过压告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CAPACITY_HARD_OVER_VOLT_ALM},//电池电容硬过压告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CAPA_UNDER_VOL_ALM},//电池电容欠压告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BUS_SOFT_OVER_VOLT_ALM},//母排软过压告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BUS_HARD_OVER_VOLT_ALM}, //母排硬过压告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BUS_UNDER_VOLT_ALM},//母排欠压告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BUS_VOL_OUT_OF_RANGE_ALM}, //母排电压超范围告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_COMPEN_CAPA_SOFT_OVER_VOL_ALM},//补偿电容软过压告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_COMPEN_CAPA_HARD_OVER_VOL_ALM}, //补偿电容硬过压告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CLLC_OVER_VOLT_ALM},//CLLC过压告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CLLC_UNDER_VOLT_ALM}, //CLLC欠压告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_MOD_CONN_FAILURE_ALM}, //模块连接不到位告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CHARGE_OVER_CURR_ALM},//充电过流告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_DISCHARGE_OVER_CURR_ALM}, //放电过流告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BAD_CURR_SHARE_ALM}, //均流不良告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CHG_CIRCUIT_DAMAGE_ALM},//充电回路损坏告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_DISCHG_CIRCUIT_DAMAGE_ALM}, //放电回路损坏告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CURR_LIMIT_CIRCUIT_DAMAGE_ALM},//限流回路损坏告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_OUTPUT_OVER_LOAD},//输出过载
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BUS_SHORT_CIRCUIT_ALM},//BUS短路告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},//EEPROM故障
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_EEPROM_FAULT_ALM}, //EEPROM故障
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_MODULE_CAN_COMM_FAULT_ALM},//模块间CAN通讯异常告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_HIGH_ENV_TEMP_ALM}, //环境温度高告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BOARD_INTER_OVER_TEMP_PROTECT_ALM},//单板内部过温保护告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_SLOW_START_EXCEPTION},//缓启动异常
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CHARGE_UNDER_VOLT_PROTECT_ALM}, //电池充电欠压保护告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_POWER_CONNECTOR_SELF_INSPECTION_RESISTANCE_ABNORMAL_ALM}, //功率连接器自检阻值异常
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_ABNORMAL_RELAY_CLOSING_PROTECTION},//继电器吸合异常保护
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_EXTER_BATT_OVER_VOL_ALM},//外部电池过压告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_EXTER_BATT_UNDER_VOL_ALM},//外部电池欠压告警
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_LARGE_VOL_DIFFERENCE_BETWEEN_BUS_AND_BATT_ALM}, //母排与电池电压差值大告警
    {SEG_DATA, type_bit,         ARRAY_SIZE_8, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit,         ARRAY_SIZE_8, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CLUSTER_LOW_SOC_ALM},
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CLUSTER_LOW_SOC_PROTECT},
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CLUSTER_LOW_SOH_ALM},
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CLUSTER_LOW_SOH_PROTECT},
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CLUSTER_ENV_TEMP_HIGH_PROTECT},
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BATT_CLUSTER_ENV_TEMP_LOW_PROTECT},
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BMU_CELL_DAMAGE_PROTECT},
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BMU_CELL_TEMP_ABNORMAL},
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BMU_CELL_TEMP_INVALID},
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BMU_CELL_VOL_SAMPLING_FAILURE},
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BMU_EQUALIZE_CIRCUIT_FAULT_ALM},
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_BMU_COMM_FAIL},
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_INSULATION_RESISTANCE_ALM},
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_INSULATION_RESISTANCE_ALM + 1},
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_INSULATION_RESISTANCE_ALM + 2},
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CO_CONCENTRATION_ALM},
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CO_CONCENTRATION_ALM + 1},
    {SEG_DATA, type_multi_bit,   ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_ALARM_ID_CO_CONCENTRATION_ALM + 2},
    {SEG_DATA, type_bit,         ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    */
};

/* 获取系统时间 */
static data_info_id_verison_t cmd_pack_sys_time_info[] = {
    {SEG_LEN,  type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, 7, NOT_NEED_INTERACT},  ///< 系统时间字节长度
    {SEG_NUM,  type_unsigned_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT},  ///< 字段数量
    {SEG_DATA, type_time,            ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BCMU_DATA_ID_SYS_TIME_SET},  ///< 年月日时分秒
};

/* 获取自检状态 */
static data_info_id_verison_t cmd_self_check_data_info[] = {
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, BMU_SELF_CHECK_DEF_BYTE_NUM,    NOT_NEED_INTERACT},  ///< 电池柜信息字节长度
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, BMU_SELF_CHECK_DEF_NUM,         NOT_NEED_INTERACT},  ///< 电池柜信息数量
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, BATT_CLUSTER_NUM,               NEED_INTERACT,     BATT_CLUSTER_NUM},           ///< 电池簇数量
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_4, DOP_0, DATA_DROP, BATT_CLUSTER_MOD_NUM,           NEED_INTERACT,     BATT_MOD_NUM},    ///< 电池簇_BMU数量
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,                   NEED_INTERACT,     BCMU_DATA_ID_BCMU_SELF_CHECK_STATE},      ///< BCMU自检状态

    /*电池簇信息*/
    {SEG_LOOP, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,  BATT_CLUSTER_NUM}, //电池簇 LOOP开始
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, BATT_CLU_SELF_CHECK_DEF_BYTE_NUM,        NOT_NEED_INTERACT},///< 电池簇的状态量字节长度
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, BATT_CLU_SELF_CHECK_DEF_DIG_DEF_NUM,     NOT_NEED_INTERACT}, ///< 电池簇的状态量数量
   // {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,                   NEED_INTERACT,     &bsmu_data.bsmu_self_check_state.dc_dc_self_check_state[0]},  ///< 簇控制器自检状态
    {SEG_LOOP_OVER},  //电池簇 LOOP结束

    /*BMU信息*/
    {SEG_LOOP, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,  BATT_MOD_NUM}, //BMU LOOP开始
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, BATT_CLU_BMU_SELF_CHECK_DEF_BYTE_NUM,    NOT_NEED_INTERACT},///< 电池簇的状态量字节长度
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, BATT_CLU_BMU_SELF_CHECK_DEF_DIG_DEF_NUM, NOT_NEED_INTERACT}, ///< 电池簇的状态量数量
    {SEG_DATA, type_bit,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,                   NEED_INTERACT,     BCMU_DATA_ID_BMU_SELF_CHECK_STATE},    ///< BMU自检状态
    {SEG_LOOP_OVER},  //BMU LOOP结束
};


/* 请求信息 */
static cmd_parse_info_id_verison_t cmd_parse_info[] = {
    {&cmd_addr_assign_trig_data_info[0],        sizeof(cmd_addr_assign_trig_data_info)/sizeof(data_info_id_verison_t)},
    {&cmd_parse_batt_cluster_real_data_info[0], sizeof(cmd_parse_batt_cluster_real_data_info)/sizeof(data_info_id_verison_t)},
    {&cmd_parse_bmu_real_data_info[0],          sizeof(cmd_parse_bmu_real_data_info)/sizeof(data_info_id_verison_t)},
    {&cmd_parse_para_set_data_info[0],          sizeof(cmd_parse_para_set_data_info)/sizeof(data_info_id_verison_t)},
    {&cmd_parse_single_para_set_data_info[0*3],ARRAY_SIZE_3}, //电芯过压告警（一级）
    {&cmd_parse_single_para_set_data_info[1*3],ARRAY_SIZE_3}, //电芯过压告警（二级）
    {&cmd_parse_single_para_set_data_info[2*3],ARRAY_SIZE_3}, //电芯过压告警（三级）
    {&cmd_parse_single_para_set_data_info[3*3],ARRAY_SIZE_3}, //电芯欠压告警（一级）
    {&cmd_parse_single_para_set_data_info[4*3],ARRAY_SIZE_3}, //电芯欠压告警（二级）
    {&cmd_parse_single_para_set_data_info[5*3],ARRAY_SIZE_3}, //电芯欠压告警（三级）
    {&cmd_parse_single_para_set_data_info[6*3],ARRAY_SIZE_3}, //电芯压差过大告警（一级）
    {&cmd_parse_single_para_set_data_info[7*3],ARRAY_SIZE_3}, //电芯压差过大告警（二级）
    {&cmd_parse_single_para_set_data_info[8*3],ARRAY_SIZE_3}, //电芯压差过大告警（三级）
    {&cmd_parse_single_para_set_data_info[9*3],ARRAY_SIZE_3}, //电芯充电高温告警（一级）
    {&cmd_parse_single_para_set_data_info[10*3],ARRAY_SIZE_3}, //电芯充电高温告警（二级）
    {&cmd_parse_single_para_set_data_info[11*3],ARRAY_SIZE_3}, //电芯充电高温告警（三级）
    {&cmd_parse_single_para_set_data_info[12*3],ARRAY_SIZE_3}, //电芯放电高温告警（一级）
    {&cmd_parse_single_para_set_data_info[13*3],ARRAY_SIZE_3}, //电芯放电高温告警（二级）
    {&cmd_parse_single_para_set_data_info[14*3],ARRAY_SIZE_3}, //电芯放电高温告警（三级）
    {&cmd_parse_single_para_set_data_info[15*3],ARRAY_SIZE_3}, //电芯充电低温告警（一级）
    {&cmd_parse_single_para_set_data_info[16*3],ARRAY_SIZE_3}, //电芯充电低温告警（二级）
    {&cmd_parse_single_para_set_data_info[17*3],ARRAY_SIZE_3}, //电芯充电低温告警（三级）
    {&cmd_parse_single_para_set_data_info[18*3],ARRAY_SIZE_3}, //电芯放电低温告警（一级）
    {&cmd_parse_single_para_set_data_info[19*3],ARRAY_SIZE_3}, //电芯放电低温告警（二级）
    {&cmd_parse_single_para_set_data_info[20*3],ARRAY_SIZE_3}, //电芯放电低温告警（三级）
    {&cmd_parse_single_para_set_data_info[21*3],ARRAY_SIZE_3}, //电芯温差过大告警（一级）
    {&cmd_parse_single_para_set_data_info[22*3],ARRAY_SIZE_3}, //电芯温差过大告警（二级）
    {&cmd_parse_single_para_set_data_info[23*3],ARRAY_SIZE_3}, //电芯温差过大告警（三级）
    {&cmd_parse_single_para_set_data_info[24*3],ARRAY_SIZE_3}, //电池过压保护阈值
    {&cmd_parse_single_para_set_data_info[25*3],ARRAY_SIZE_3}, //设定充电限电流系数
    {&cmd_parse_single_para_set_data_info[26*3],ARRAY_SIZE_3}, //设定放电限电流
    {&cmd_parse_single_para_set_data_info[27*3],ARRAY_SIZE_3}, //设定充电电压
    {&cmd_parse_single_para_set_data_info[28*3],ARRAY_SIZE_3}, //设定放电电压
    {&cmd_parse_single_para_set_data_info[29*3],ARRAY_SIZE_3}, //母排过压保护阈值
    {&cmd_parse_single_para_set_data_info[30*3],ARRAY_SIZE_3}, //电池欠压保护阈值
    {&cmd_parse_single_para_set_data_info[31*3],ARRAY_SIZE_3}, //母排欠压保护阈值
    {&cmd_parse_single_para_set_data_info[32*3],ARRAY_SIZE_3}, //母排与电池差值保护阈值
    {&cmd_parse_single_para_set_data_info[33*3],ARRAY_SIZE_3}, //电池个数
    {&cmd_parse_single_para_set_data_info[34*3],ARRAY_SIZE_3}, //BCMU地址
    {&cmd_parse_single_para_set_data_info[35*3],ARRAY_SIZE_3}, //电池簇SOC低告警
    {&cmd_parse_single_para_set_data_info[36*3],ARRAY_SIZE_3}, //电池簇SOC低保护
    {&cmd_parse_single_para_set_data_info[37*3],ARRAY_SIZE_3}, //电池簇SOH低告警
    {&cmd_parse_single_para_set_data_info[38*3],ARRAY_SIZE_3}, //电池簇SOH低保护
    {&cmd_parse_single_para_set_data_info[39*3],ARRAY_SIZE_3}, //电池簇环境温度高保护
    {&cmd_parse_single_para_set_data_info[40*3],ARRAY_SIZE_3}, //电池簇环境温度低保护
    {&cmd_parse_single_para_set_data_info[41*3],ARRAY_SIZE_3}, //BMU电芯损坏保护
    {&cmd_parse_single_para_set_data_info[42*3],ARRAY_SIZE_3}, //绝缘电阻告警（一级）
    {&cmd_parse_single_para_set_data_info[43*3],ARRAY_SIZE_3}, //绝缘电阻告警（二级）
    {&cmd_parse_single_para_set_data_info[44*3],ARRAY_SIZE_3}, //绝缘电阻告警（三级）
    {&cmd_parse_single_para_set_data_info[45*3],ARRAY_SIZE_3}, //一氧化碳浓度告警（一级）
    {&cmd_parse_single_para_set_data_info[46*3],ARRAY_SIZE_3}, //一氧化碳浓度告警（二级）
    {&cmd_parse_single_para_set_data_info[47*3],ARRAY_SIZE_3}, //一氧化碳浓度告警（三级） 51
    {&cmd_parse_para_alm_level_data_info[0],    sizeof(cmd_parse_para_alm_level_data_info)/sizeof(data_info_id_verison_t)},
    {&cmd_parse_para_alm_relay_data_info[0],    sizeof(cmd_parse_para_alm_relay_data_info)/sizeof(data_info_id_verison_t)},
    {&cmd_parse_single_para_set_data_info[48*3],ARRAY_SIZE_3}, //均衡启动电压差阈值设置 54
    {&cmd_parse_single_para_set_data_info[49*3],ARRAY_SIZE_3}, //均衡最低启动电压值设置 55
    //{&cmd_parse_single_para_set_data_info[50*3],ARRAY_SIZE_3,NULL,0}, //BUS充电最低电压阈值 56
    {&cmd_parse_single_para_set_data_info[50*3],ARRAY_SIZE_3}, //放电均流电流 56
    {&cmd_parse_system_time_info[0], sizeof(cmd_parse_system_time_info)/sizeof(data_info_id_verison_t)}, //57 校准系统时间
    {&cmd_parse_single_para_set_data_info[51*3],ARRAY_SIZE_3}, //电芯高温限电流系数 58
    {&cmd_parse_single_para_set_data_info[52*3],ARRAY_SIZE_3}, //停电电压阈值 59
};



/* 答复信息 */
static cmd_parse_info_id_verison_t cmd_pack_info[] = {
    {&reply_addr_assign_trig_data_info[0],     sizeof(reply_addr_assign_trig_data_info)/sizeof(data_info_id_verison_t)},
    {&cmd_pack_batt_cabinet_real_data_info[0], sizeof(cmd_pack_batt_cabinet_real_data_info)/sizeof(data_info_id_verison_t)},
    {&cmd_pack_batt_cluster_real_data_info[0], sizeof(cmd_pack_batt_cluster_real_data_info)/sizeof(data_info_id_verison_t)},
    {&cmd_pack_bmu_real_data_info[0],          sizeof(cmd_pack_bmu_real_data_info)/sizeof(data_info_id_verison_t)},
    {&cmd_pack_real_data_info[0],              sizeof(cmd_pack_real_data_info)/sizeof(data_info_id_verison_t)},
    {&reply_fac_data_info[0],                  sizeof(reply_fac_data_info)/sizeof(data_info_id_verison_t)},
    {&cmd_pack_real_alm_info[0],               sizeof(cmd_pack_real_alm_info)/sizeof(data_info_id_verison_t)},
    {&cmd_self_check_data_info[0],             sizeof(cmd_self_check_data_info)/sizeof(data_info_id_verison_t)},
    {&cmd_pack_para_alm_level_data_info[0],    sizeof(cmd_pack_para_alm_level_data_info)/sizeof(data_info_id_verison_t)},
    {&cmd_pack_sys_time_info[0],               sizeof(cmd_pack_sys_time_info) / sizeof(data_info_id_verison_t)},  // 9 获取系统日期时间
};

/* 通信协议命令表 */
static cmd_t no_poll_tab[] = {
    {BSMU_ADDR_ASSIGN_TRIG,      CMD_BROADCAST, &cmd_req[0], &cmd_ack[0], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[0], &cmd_parse_info[0]},
    {BSMU_GET_ALL_REAL_DATA,     CMD_PASSIVE,  &cmd_req[2], &cmd_ack[2], sizeof(bottom_comm_cmd_head_t),  NULL, NULL, NULL, NULL, &cmd_pack_info[4], &cmd_parse_info[1]},
    {BSMU_GET_CABINET_REAL_DATA, CMD_PASSIVE,  &cmd_req[3], &cmd_ack[3], sizeof(bottom_comm_cmd_head_t),  NULL, NULL, NULL, NULL, &cmd_pack_info[1]},
    {BSMU_GET_CLUSTER_REAL_DATA, CMD_PASSIVE,  &cmd_req[4], &cmd_ack[4], sizeof(bottom_comm_cmd_head_t),  NULL, NULL, NULL, NULL, &cmd_pack_info[2], &cmd_parse_info[1]},
    {BSMU_GET_BMU_REAL_DATA,     CMD_PASSIVE,  &cmd_req[5], &cmd_ack[5], sizeof(bottom_comm_cmd_head_t),  NULL, NULL, NULL, NULL, &cmd_pack_info[3], &cmd_parse_info[2]},
    {BSMU_GET_FAC_DATA,          CMD_PASSIVE,  &cmd_req[6], &cmd_ack[6], sizeof(bottom_comm_cmd_head_t),  NULL, NULL, NULL, NULL, &cmd_pack_info[5], NULL},
    {BSMU_SET_BATCH_PARA,        CMD_PASSIVE,  &cmd_req[7], &cmd_ack[7], sizeof(bottom_comm_cmd_head_t),  NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[3]},
    {BSMU_SET_CELL_OVA_LV1,      CMD_PASSIVE,  &cmd_req[8], &cmd_ack[8], sizeof(bottom_comm_cmd_head_t),  NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[4]},
    {BSMU_SET_CELL_OVA_LV2,      CMD_PASSIVE,  &cmd_req[9], &cmd_ack[9], sizeof(bottom_comm_cmd_head_t),  NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[5]},
    {BSMU_SET_CELL_OVA_LV3,      CMD_PASSIVE,  &cmd_req[10], &cmd_ack[10], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[6]},
    {BSMU_SET_CELL_UVA_LV1,      CMD_PASSIVE,  &cmd_req[11], &cmd_ack[11], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[7]},
    {BSMU_SET_CELL_UVA_LV2,      CMD_PASSIVE,  &cmd_req[12], &cmd_ack[12], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[8]},
    {BSMU_SET_CELL_UVA_LV3,      CMD_PASSIVE,  &cmd_req[13], &cmd_ack[13], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[9]},
    {BSMU_SET_CELL_VDOA_LV1,     CMD_PASSIVE,  &cmd_req[14], &cmd_ack[14], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[10]},
    {BSMU_SET_CELL_VDOA_LV2,     CMD_PASSIVE,  &cmd_req[15], &cmd_ack[15], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[11]},
    {BSMU_SET_CELL_VDOA_LV3,     CMD_PASSIVE,  &cmd_req[16], &cmd_ack[16], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[12]},
    {BSMU_SET_CELL_CHTA_LV1,     CMD_PASSIVE,  &cmd_req[17], &cmd_ack[17], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[13]},
    {BSMU_SET_CELL_CHTA_LV2,     CMD_PASSIVE,  &cmd_req[18], &cmd_ack[18], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[14]},
    {BSMU_SET_CELL_CHTA_LV3,     CMD_PASSIVE,  &cmd_req[19], &cmd_ack[19], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[15]},
    {BSMU_SET_CELL_DHTA_LV1,     CMD_PASSIVE,  &cmd_req[20], &cmd_ack[20], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[16]},
    {BSMU_SET_CELL_DHTA_LV2,     CMD_PASSIVE,  &cmd_req[21], &cmd_ack[21], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[17]},
    {BSMU_SET_CELL_DHTA_LV3,     CMD_PASSIVE,  &cmd_req[22], &cmd_ack[22], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[18]},
    {BSMU_SET_CELL_CLTA_LV1,     CMD_PASSIVE,  &cmd_req[23], &cmd_ack[23], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[19]},
    {BSMU_SET_CELL_CLTA_LV2,     CMD_PASSIVE,  &cmd_req[24], &cmd_ack[24], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[20]},
    {BSMU_SET_CELL_CLTA_LV3,     CMD_PASSIVE,  &cmd_req[25], &cmd_ack[25], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[21]},
    {BSMU_SET_CELL_DLTA_LV1,     CMD_PASSIVE,  &cmd_req[26], &cmd_ack[26], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[22]},
    {BSMU_SET_CELL_DLTA_LV2,     CMD_PASSIVE,  &cmd_req[27], &cmd_ack[27], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[23]},     
    {BSMU_SET_CELL_DLTA_LV3,     CMD_PASSIVE,  &cmd_req[28], &cmd_ack[28], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[24]},
    {BSMU_SET_CELL_OTDA_LV1,     CMD_PASSIVE,  &cmd_req[29], &cmd_ack[29], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[25]},
    {BSMU_SET_CELL_OTDA_LV2,     CMD_PASSIVE,  &cmd_req[30], &cmd_ack[30], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[26]},
    {BSMU_SET_CELL_OTDA_LV3,     CMD_PASSIVE,  &cmd_req[31], &cmd_ack[31], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[27]},
    {BSMU_SET_CLUSTER_OVP_THRE,  CMD_PASSIVE,  &cmd_req[32], &cmd_ack[32], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[28]},
    {BSMU_SET_CLUSTER_CL_CURR,   CMD_PASSIVE,  &cmd_req[33], &cmd_ack[33], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[29]},
    {BSMU_SET_CLUSTER_DL_CURR,   CMD_PASSIVE,  &cmd_req[34], &cmd_ack[34], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[30]},
    {BSMU_SET_CLUSTER_CHG_VO,    CMD_PASSIVE,  &cmd_req[35], &cmd_ack[35], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[31]},
    {BSMU_SET_CLUSTER_DISCHG_VOL,CMD_PASSIVE,  &cmd_req[36], &cmd_ack[36], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[32]},
    {BSMU_SET_CLUSTER_BUS_OVP_THRE, CMD_PASSIVE,  &cmd_req[37], &cmd_ack[37], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[33]},
    {BSMU_SET_CLUSTER_BAT_UVP_THRE, CMD_PASSIVE,  &cmd_req[38], &cmd_ack[38], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[34]},
    {BSMU_SET_CLUSTER_BUS_UVP_THRE, CMD_PASSIVE,  &cmd_req[39], &cmd_ack[39], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[35]},
    {BSMU_SET_CLUSTER_BBDP_THRE,    CMD_PASSIVE,  &cmd_req[40], &cmd_ack[40], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[36]},
    {BSMU_SET_CLUSTER_BAT_NUM,      CMD_PASSIVE,  &cmd_req[41], &cmd_ack[41], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[37]},
    {BSMU_SET_BCMU_ADDR,            CMD_PASSIVE,  &cmd_req[42], &cmd_ack[42], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[38]},
    {BSMU_SET_CLUSTER_SOC_LOW_ALM,  CMD_PASSIVE,  &cmd_req[43], &cmd_ack[43], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[39]},
    {BSMU_SET_CLUSTER_SOC_LOW_PROT, CMD_PASSIVE,  &cmd_req[44], &cmd_ack[44], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[40]},
    {BSMU_SET_CLUSTER_SOH_LOW_ALM,  CMD_PASSIVE,  &cmd_req[45], &cmd_ack[45], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[41]},
    {BSMU_SET_CLUSTER_SOH_LOW_PROT, CMD_PASSIVE,  &cmd_req[46], &cmd_ack[46], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[42]},
    {BSMU_SET_CLUSTER_ETH_PROT,     CMD_PASSIVE,  &cmd_req[47], &cmd_ack[47], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[43]},        
    {BSMU_SET_CLUSTER_ETL_PROT,     CMD_PASSIVE,  &cmd_req[48], &cmd_ack[48], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[44]},
    {BSMU_SET_BMU_CELL_DAM_PROT,    CMD_PASSIVE,  &cmd_req[49], &cmd_ack[49], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[45]},
    {BSMU_SET_IRA_ALM_LV1,          CMD_PASSIVE,  &cmd_req[50], &cmd_ack[50], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[46]},
    {BSMU_SET_IRA_ALM_LV2,          CMD_PASSIVE,  &cmd_req[51], &cmd_ack[51], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[47]},
    {BSMU_SET_IRA_ALM_LV3,          CMD_PASSIVE,  &cmd_req[52], &cmd_ack[52], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[48]},
    {BSMU_SET_COC_ALM_LV1,          CMD_PASSIVE,  &cmd_req[53], &cmd_ack[53], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[49]},
    {BSMU_SET_COC_ALM_LV2,          CMD_PASSIVE,  &cmd_req[54], &cmd_ack[54], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[50]},
    {BSMU_SET_COC_ALM_LV3,          CMD_PASSIVE,  &cmd_req[55], &cmd_ack[55], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[51]}, 
    {BSMU_SET_ALM_LEVEL,            CMD_PASSIVE,  &cmd_req[56], &cmd_ack[56], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[52]},
    {BSMU_SET_ALM_RELAY,            CMD_PASSIVE,  &cmd_req[57], &cmd_ack[57], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[53]},
    {BSMU_GET_REAL_ALM,             CMD_PASSIVE,  &cmd_req[58], &cmd_ack[58], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[6]},
    {BSMU_GET_SELF_CHECK_STATE,     CMD_PASSIVE,  &cmd_req[59], &cmd_ack[59], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[7],  NULL},
    {BSMU_GET_ALM_LEVEL,            CMD_PASSIVE,  &cmd_req[60], &cmd_ack[60], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[8]},
    {BSMU_UPDATE_DATA_TRIG,         CMD_PASSIVE,  &cmd_req[61], &cmd_ack[61], sizeof(bottom_comm_cmd_head_t), NULL,NULL, SELF_PACK,  SELF_PARSE},
    {BSMU_UPDATE_DATA,              CMD_PASSIVE,  &cmd_req[62], &cmd_ack[62], sizeof(bottom_comm_cmd_head_t), NULL,NULL, SELF_PACK,  SELF_PARSE},
    {BSMU_UPDATE_DATA_INTERRUPT,    CMD_PASSIVE,  &cmd_req[63], &cmd_ack[63], sizeof(bottom_comm_cmd_head_t), NULL,NULL, SELF_PACK,  SELF_PARSE},
    {BSMU_UPDATE_TRIG,              CMD_PASSIVE,  &cmd_req[64], &cmd_ack[64], sizeof(bottom_comm_cmd_head_t), NULL,NULL, SELF_PACK,  SELF_PARSE},
    {BSMU_CELL_BALANCE_VOL_DIFF, CMD_PASSIVE,  &cmd_req[65], &cmd_ack[65], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[54]},
    {BSMU_CELL_BALANCE_LOW_VOL,  CMD_PASSIVE,  &cmd_req[66], &cmd_ack[66], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[55]},
    //{BSMU_BUS_CHG_LOW_VOL,   CMD_PASSIVE,  &cmd_req[67], &cmd_ack[67], sizeof(bottom_comm_cmd_head_t), NULL, &cmd_parse_info[56]},
    {BSMU_DISCHG_CUR_BALANCE_CUR,CMD_PASSIVE,  &cmd_req[67], &cmd_ack[67], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[56]},
    {BSMU_TIME_SYNC, CMD_POSITIVE,  &cmd_req[68], NULL, sizeof(bottom_comm_cmd_head_t), NULL, NULL},
    {BSMU_SET_CALIBRATED_TIME,      CMD_PASSIVE,  &cmd_req[69], &cmd_ack[68], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[57]},
    {BSMU_CELL_CHG_HIGH_TEMP_LIMIT_CURR_COE,  CMD_PASSIVE,  &cmd_req[70], &cmd_ack[69], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[58]},
    {BSMU_SET_CLUSTER_POWRE_DOWN_VOL_THRE,  CMD_PASSIVE,  &cmd_req[71], &cmd_ack[70], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[59]},
    {BSMU_CTRL_POWER_ON,  CMD_PASSIVE,  &cmd_req[72], &cmd_ack[71], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, NULL},
    {BSMU_GET_SYS_TIME,            CMD_PASSIVE,  &cmd_req[73], &cmd_ack[72], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[9], NULL},
    {0},
};


static dev_type_t dev_bsmu_type = {
    DEV_BSMU, BSMU_NUM, PROTOCOL_BOTTOM_COMM, LINK_BSMU, R_BUFF_LEN, S_BUFF_LEN, BOTTOM_BSMU_TYPE, &no_poll_tab[0], NULL, LINK_BSMU_BAK,
};

dev_type_t* init_dev_bsmu(void) {
    return &dev_bsmu_type;
}

void get_bsmu_data(dev_sample_data_t* dev_data, unsigned char dev_index) {
    dev_data->bsmu_data = &bsmu_data;
}

