/*
 * @file    :
 * @brief   :
 * @details :
 * <AUTHOR> libixuan
 * @Date    : 2021-12-29
 * @LastEditTime: 2023-10-23 10:30:00
 * @version : V0.0.1
 * @para    : Copyright (c)
 *            ZTE Corporation
 * @par     : History
 *     version: author, date, descn
 */
/*
 * Copyright (c) 2006-2018, RT-Thread Development Team
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Change Logs:
 * Date           Author       Notes
 * 2018-11-7      SummerGift   first version
 */

#include "drv_common.h"
#include "drv_utils.h"
#include "board.h"

// 看门狗中断喂狗配置
#define WDI_PIN_PORT                    GPIOE
#define WDI_PIN_NUM                     GPIO_PIN_2

extern unsigned char s_hardware_dog_feeding;

uint32_t HAL_GetTick(void)
{
    return rt_tick_get() * 1000 / RT_TICK_PER_SECOND;
}

void HAL_SuspendTick(void)
{
}

void HAL_ResumeTick(void)
{
}

void HAL_Delay(__IO uint32_t Delay)
{
}

void _Error_Handler(char *s, int num)
{
    RETURN_IF_FAIL(s != RT_NULL);
    /* USER CODE BEGIN Error_Handler */
    /* User can add his own implementation to report the HAL error return state */
    while(bsp_runing(RT_TRUE))
    {
    }
    /* USER CODE END Error_Handler */
}

/**
 * This is the timer interrupt service routine.
 *
 */
void SysTick_Handler(void)
{
    /* enter interrupt */
    rt_interrupt_enter();

    static uint16_t watchdog_counter = 0;
    if (++watchdog_counter >= 100)
    {
        watchdog_counter = 0;
        if (s_hardware_dog_feeding)
        {
            gpio_bit_write(WDI_PIN_PORT, WDI_PIN_NUM, RESET);
            // 延时约1ms确保脉冲宽度
            for(volatile int i = 0; i < (SystemCoreClock/1000000); i++);
            gpio_bit_write(WDI_PIN_PORT, WDI_PIN_NUM, SET);
        }
    }

    rt_tick_increase();

    /* leave interrupt */
    rt_interrupt_leave();
}

/**
 * This function will delay for some us.
 *
 * @param us the delay time of us
 */
void rt_hw_us_delay(rt_uint32_t us)
{
    rt_uint32_t start = 0, now = 0, delta = 0, reload = 0, us_tick = 0;
    start = SysTick->VAL;
    reload = SysTick->LOAD;
    us_tick = SystemCoreClock / 1000000UL;
    do {
        now = SysTick->VAL;
        delta = start > now ? start - now : reload + start - now;
    } while(delta < us_tick * us);
}

static int up_char(char *c)
{
    RETURN_VAL_IF_FAIL(c != RT_NULL, -RT_ERROR);
    if ((*c >= 'a') && (*c <= 'z'))
    {
        *c = *c - 32;
    }
    return RT_EOK;
}

rt_base_t get_pin_by_name(const char* pin_name, rt_uint32_t *port, rt_uint32_t *pin)
{
    //这个函数比较特殊，port和pin可以传入空指针，有些地方只需要用到返回值，因此只对pin_name判空
    RETURN_VAL_IF_FAIL(pin_name != RT_NULL, -RT_ERROR);
    rt_uint32_t tempPort = 0;
    rt_uint32_t tempPin = 0;

    RETURN_VAL_IF_FAIL(rt_bsp_strnlen(pin_name,PIN_NAME_MAX) >= 2, -RT_ERROR);

    char port_name = pin_name[1];
    int pin_num = atoi((char*) &pin_name[2]);

    RETURN_VAL_IF_FAIL((pin_num >= PIN_NUM_MIN) && (pin_num <= PIN_NUM_MAX), -RT_ERROR);

    up_char(&port_name);
    up_char(&port_name);
    tempPort = (((uint32_t) GPIOA
            + (uint32_t) (port_name - 'A') * ((uint32_t) GPIOB - (uint32_t) GPIOA)));
    tempPin = (GPIO_PIN_0 << pin_num);

    if(port != RT_NULL && pin != RT_NULL)
    {
        *port = tempPort;
        *pin  = tempPin;
    }

    return (rt_base_t)((16 * ( ((rt_base_t)(tempPort) - (rt_base_t)GPIOA)/(0x0400UL) )) + pin_num);
}


