#ifndef SOFTWARE_SRC_APP_BDUTEST_H_ 
#define SOFTWARE_SRC_APP_BDUTEST_H_ 

#ifdef __cplusplus
extern "C" {
#endif

#define RECORD_TOTAL_NUM         10
#define RECORD_POINT_MAX         23

#define SCI_REAL_SEG_LEN_ANA     16
#define SCI_REAL_SEG_LEN_STS     8
#define SCI_REAL_SEG_LEN_ALM     3
#define SCI_REAL_SEG_LEN_CRC     4

#define SCI_TEST_SEG_LEN_ANA     32
#define SCI_TEST_SEG_LEN_STS     1
#define SCI_TEST_SEG_LEN_CRC     2

#define SCI_BDUADJ_MAX_LEN       30   // 定义最大的BDU校正通道数

#define SCI_DATA_LEN_PARA    18

typedef struct
{
    uint16_t wBusVoltScal;         // 母排侧电压校正
    uint16_t wBusPinVoltScal;      // 母排侧PIN+/-防反接电压比例校正
    uint16_t wBatVoltScal;         // 电池侧电压（BAT_DET）校正
    uint16_t wBusChaCurrScal;      // 母排侧充电电流校正
    uint16_t wBusDischaCurrScal;   // 母排侧放电电流校正
    uint16_t wBatChaCurrScal;      // 电池侧充电电流校正
    uint16_t wBatDischaCurrLScal;  // 电池侧放电80A电流校正
    uint16_t wBatDischaCurrHScal;  // 电池侧放电200A电流校正

    uint16_t wBusVoltZero;         // 母排侧电压校正
    uint16_t wBusPinVoltZero;      // 母排侧PIN+/-防反接电压校正
    uint16_t wBatVoltZero;         // 电池侧电压（BAT_DET）校正
    uint16_t wBusChaCurrZero;      // 母排侧充电电流校正
    uint16_t wBusDischaCurrZero;   // 母排侧放电电流校正
    uint16_t wBatChaCurrZero;      // 电池侧充电电流校正
    uint16_t wBatDischaCurrLZero;  // 电池侧放电80A电流校正
    uint16_t wBatDischaCurrHZero;  // 电池侧放电200A电流校正
}T_DCCaliGet;

typedef struct
{
    int16_t     sCurrentInfo1;         // 电流信息1：ISE1_DISCHAGE_DSC
    int16_t     sCurrentInfo2;         // 电流信息2：ISE2_DISCHARGE_80A_DSC
    int16_t     sCurrentInfo3;         // 电流信息3：ISE2_DISCHARGE_DSC
    int16_t     sCurrentInfo4;         // 电流信息4：ISE1_CHARGE_DSC
    int16_t     sCurrentInfo5;         // 电流信息5：ISE2_CHARGE_DSC
    uint16_t    wVoltageInfo1;         // 电压信息1：BAT_DSC
    uint16_t    wVoltageInfo2;         // 电压信息2：BAT_DET_DSC
    uint16_t    wVoltageInfo3;         // 电压信息3：POWER_DSC
    uint16_t    wVoltageInfo4;         // 电压信息4：POWER_DIR_DSC

    int16_t     sBoardTemp1;           // 单板温度：TEMP_MOS_DSC
    int16_t     sBoardTemp2;           // 单板温度：TEMP_BAT+_DSC
    int16_t     sBoardTemp3;           // 单板温度：TEMP_BAT-_DSC
    int16_t     sConnTemp1;            // 连接器温度：TEMP_DZ+_DSC
    int16_t     sConnTemp2;            // 连接器温度：TEMP_DZ-_DSC

    int16_t     sEnvTemp;              // 环境温度
    uint16_t    wHeaterVol;            // 加热膜电压

    uint16_t    bBusRelayTestSts : 2;  // 母排继电器驱动检测状态
    uint16_t    bWavePrtTestSts  : 1;  // 逐波保护电压测试状态
    uint16_t    bRsvd            : 5;  // 保留位

    uint16_t    wTestParaCrc;          // 测试参数校验值
}T_DCTestData;

//查询/设置模块参数：
typedef struct
{
    uint16_t wChgOverCurVal;         // 充电过流保护阈值，精度2
    uint16_t wDischOverCurVal;       // 放电过流保护阈值，精度2
    uint16_t wBatOverVolVal;         // 电池组过压保护阈值，精度2
    uint16_t wDropVol;               // 母排掉电阈值，精度2
    uint16_t wChgCurVal;             // 当前BAT充电限电流，千分比
    uint16_t wDisChgCurVal;          // 当前BUS放电限电流，千分比
    uint16_t wChgVolVal;             // 当前充电电压，精度2
    uint16_t wDischgVolVal;          // 当前放电电压，精度2
    uint16_t wChgBusCurVal;          // 当前BUS充电限电流，千分比
    uint16_t wCrc;                   // 校验值
}T_DCPara;

#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif
#endif  //SOFTWARE_SRC_APP_BDUTEST_H_;
