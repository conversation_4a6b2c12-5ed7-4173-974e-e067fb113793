/**
 * @file     dcmu_protocol_apptest.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2025-09-1
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */

#ifndef _DCMU_PROTOCOL_APPTEST_H
#define _DCMU_PROTOCOL_APPTEST_H
 
#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */
#include "device_type.h"

#define TEST_FAULT  1 //测试结果异常
#define TEST_NORMAL 0 //测试结果正常
 
/*north protocol的命令唯一标识定义 */
#define DCMU_APPTEST_ENTER_APPTEST                  1   ///< 进入apptest命令
#define DCMU_APPTEST_EXIT_APPTEST                   2   ///< 退出apptest命令
#define DCMU_APPTEST_GET_COMMPROTOCOL_VER           3   ///<  获取通信协议版本号
#define DCMU_APPTEST_GET_MANUFACTURE_DATE           4   ///<  获取单板生产日期
#define DCMU_APPTEST_SET_MANUFACTURE_DATE           5   ///<  设置单板生产日期
#define DCMU_APPTEST_GET_HARDWARE_VER               6   ///<  获取硬件版本号
#define DCMU_APPTEST_SET_HARDWARE_VER               7   ///<  设置硬件版本号
#define DCMU_APPTEST_GET_SERIAL_NUMBER              8   ///<  获取序列号
#define DCMU_APPTEST_SET_SERIAL_NUMBER              9   ///<  设置序列号
#define DCMU_APPTEST_SET_DO_STATE                   10  ///<  设置输出干接点状态命令
#define DCMU_APPTEST_GET_DI_STATE                   11  ///<  获取输入干接点状态命令
#define DCMU_APPTEST_GET_AI_DATA                    12  ///<  获取模拟量
#define DCMU_APPTEST_USART_LOOPBACK_TEST            13  ///<  串口回环测试
#define DCMU_APPTEST_CAN_LOOPBACK_TEST              14  ///<  CAN口回环测试
#define DCMU_APPTEST_SET_LED_STATUS                 15  ///<  控制液晶面板灯
#define DCMU_APPTEST_SET_BUZZ_STATUS                16  ///<  控制蜂鸣器
#define DCMU_APPTEST_SET_START_BUTTON_TEST          17  ///<  启动按键测试
#define DCMU_APPTEST_GET_BUTTON_TEST_STATE          18  ///<  获取按键测试结果
#define DCMU_APPTEST_GET_TIME                       19  ///<  获取时间
#define DCMU_APPTEST_SET_TIME                       20  ///<  设置时间
#define DCMU_APPTEST_GET_ZERO_SLOPE                 21  ///<  获取零点、斜率
#define DCMU_APPTEST_SET_ZERO_SLOPE                 22  ///<  设置零点、斜率
#define DCMU_APPTEST_GET_CHIP_STATUS                23  ///<  获取芯片测试信息
#define DCMU_APPTEST_GET_SOFTINFO                   24  ///<  获取软件信息命令
#define DCMU_APPTEST_RESET_CONFIG_PARA              25  ///<  恢复配置参数
#define DCMU_APPTEST_DELETE_HISRECORD               26  ///<  删除历史记录

/* cid2 功能码定义 */
#define CMD_APPTEST_ENTER                   0x00  // 进入apptest命令
#define CMD_APPTEST_EXIT                    0x61  // 退出apptest命令
#define CMD_GET_COMMPROTOCOL_VER            0x4F    ///<  获取通信协议版本号
#define CMD_GET_MANUFACTURE_DATE            0x7F    ///<  获取单板生产日期
#define CMD_SET_MANUFACTURE_DATE            0x7E    ///<  设置单板生产日期
#define CMD_GET_HARDWARE_VER                0x42    ///<  获取硬件版本号
#define CMD_SET_HARDWARE_VER                0x41    ///<  设置硬件版本号
#define CMD_GET_SERIAL_NUMBER_APPTEST       0x7B    ///<  获取序列号
#define CMD_SET_SERIAL_NUMBER_APPTEST       0xD7    ///<  设置序列号
#define CMD_SET_DO_STATE                    0x62    ///<  设置输出干接点状态命令
#define CMD_GET_DI_STATE                    0x68    ///<  获取输入干接点状态命令
#define CMD_GET_AI_DATA                     0x4A    ///<  获取模拟量
#define CMD_USART_LOOPBACK_TEST             0x44    ///<  串口回环测试
#define CMD_CAN_LOOPBACK_TEST               0x45    ///<  CAN回环测试
#define CMD_SET_LED_STATE                   0x47    ///<  控制液晶面板灯
#define CMD_SET_BUZZ_STATE                  0x48    ///<  控制蜂鸣器
#define CMD_SET_START_BUTTON_TEST           0x49    ///<  启动按键测试
#define CMD_GET_BUTTON_TEST_STATE           0x50    ///<  获取按键测试结果
#define CMD_GET_TIME                        0x4D    ///<  获取时间
#define CMD_SET_TIME                        0x4E    ///<  设置时间
#define CMD_GET_ZERO_SLOPE                  0x51    ///<  获取零点、斜率
#define CMD_SET_ZERO_SLOPE                  0x52    ///<  设置零点、斜率
#define CMD_GET_CHIP_STATUS                 0xD1    ///<  获取芯片测试信息
#define CMD_GET_SOFTINFO                    0x82    ///<  获取软件信息命令
#define CMD_RESET_CONFIG_PARA               0x46    ///<  恢复配置参数
#define CMD_DELETE_HISRECORD                0xF9    ///<  删除历史记录

#define SM_MANUFACTURE_DATE_LEN_APPTEST      4       ///<  单板生产日期长度
#define SM_HARDWARE_VERSION_LEN_APPTEST      20      ///<  硬件版本号长度
#define SM_SERIAL_NUMBER_LEN_APPTEST         20      ///<  序列号长度

#define SOFTNAME_LEN_APPTEST         32      ///<  软件名称长度
#define SOFTVER_LEN_APPTEST          32      ///<  软件版本长度

#define COMMAND_TYPE_RUN_LED    0x80
#define COMMAND_TYPE_ALM_LED    0x81
#define COMMAND_TYPE_CHA_LED    0x82
#define COMMAND_TYPE_COMM_LED   0x83

typedef enum {
    DO_TYPE_RLY1 = 0,
    DO_TYPE_RLY2,
    DO_TYPE_RLY3,
    DO_TYPE_RLY4,
    DO_TYPE_RLY5,
    DO_TYPE_RLY6,
    DO_TYPE_RLY7,
    DO_TYPE_RLY8,
    DO_TYPE_CTRL_ALM_LED,    ///< 机架告警灯
    DO_TYPE_STOP_FEED_DOG,   ///< 停止喂狗
    DO_TYPE_NUM
} e_apptest_do_type;

typedef enum {
    esc_button_index = 0,
    left_button_index,
    up_button_index,
    down_button_index,
    enter_button_index,
    right_button_index,
} e_apptest_led_butoon_index;   //led按键数组索引，与全局按键数组顺序保持一致

typedef struct {
    unsigned short  command_type;
    unsigned short sid; 
}dcmu_zero_slope_info;

dev_type_t* init_dev_dcmu_apptest(void);
#ifdef __cplusplus
}
#endif  //  __cplusplus
 
#endif  //  _DCMU_PROTOCOL_APPTEST_H