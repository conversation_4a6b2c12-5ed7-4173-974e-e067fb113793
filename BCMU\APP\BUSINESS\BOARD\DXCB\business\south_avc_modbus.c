#include "south_dev_common.h"
#include "realdata_id_in.h"
#include "para_id_in.h"
#include "south_dev_modbus.h"



/* 命令请求头 */
static modbusrtu_cmd_head_t cmd_req[] = {
    {READ_HOLD_REG},
    {READ_INPUT_REG},
    {WRITE_SINGLE_REG},
    {WRITE_MULTI_REG},
};

/* 命令应答头 */
static modbusrtu_cmd_head_t cmd_ack[] = {
    {READ_HOLD_REG},
    {READ_INPUT_REG},
    {WRITE_SINGLE_REG},
    {WRITE_MULTI_REG},
};

//解析实时量寄存器地址：53264-53287
static data_info_id_verison_t parse_real_data_info[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},//数据的字节长度
    
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_OC_ALARM_FAULT_CODE, 8},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_OC_ALARM_FAULT_CODE+1, 8},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_OC_ALARM_FAULT_CODE+2, 8},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_OC_ALARM_FAULT_CODE+3, 8},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_OC_ALARM_FAULT_CODE+4, 8},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_OC_ALARM_FAULT_CODE+5, 8},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_OC_ALARM_FAULT_CODE+6, 8},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_OC_ALARM_FAULT_CODE+7, 8},

    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_OC_ALARM_OVER_LIMIT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_OC_ALARM_DIRECTION},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_OC_ALARM_LIFECYCLE},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_OC_ALARM_VIBRATION},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_OC_ALARM_APP_SYS},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_OC_ALARM_MOTOR_SYS},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},

    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_MOTOR_CURR},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_DC_VOL},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_POWER},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_PEAK_LINE_VOL},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_FAN_SPEED_PERCENT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_IN_TEMP},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
};


static data_info_id_verison_t parse_get_fan_switch[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},//数据的字节长度
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_FAN_CTRL_FAN_SWITCH},
};

static data_info_id_verison_t parse_get_speed_ctrl[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},//数据的字节长度
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_FAN_WIND_SPEED_CTRL_AVC},
};

static cmd_parse_info_id_verison_t cmd_parse_info[] RAM_SECTION =
{
    {&parse_real_data_info[0], sizeof(parse_real_data_info)/sizeof(data_info_id_verison_t)},

    {&parse_get_fan_switch[0], sizeof(parse_get_fan_switch)/sizeof(data_info_id_verison_t)},
    {&parse_get_speed_ctrl[0], sizeof(parse_get_speed_ctrl)/sizeof(data_info_id_verison_t)},
};




/* 命令总表,必须以空字符串""结束 */
static cmd_t no_poll_cmd_tab[] = {
    {WRITE_EBM_SINGLE_REG, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, NULL, NULL,NULL,NULL},
    {WRITE_EBM_MULTI_REG, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, NULL, NULL,NULL,NULL},
    {AVC_SET_FAN_SWITCH, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data, NULL,NULL},
    {AVC_SET_SPEED_CTRL, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data, NULL,NULL},
    
    {AVC_GET_FAN_SWITCH, CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[1]},
    {AVC_GET_SPEED_CTRL, CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[2]},
    {0},
};

static cmd_t poll_cmd_tab[] = {
    {READ_AVC_INPUT_REG, CMD_POSITIVE, &cmd_req[1], &cmd_ack[1], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[0]},
    {0},
};


int update_fan_avc_dev_type(dev_inst_t* dev_inst, char dev_num, char is_inter_fan)
{
    return change_dev_type_info_by_diff_brand(dev_inst, SOUTH_FAN_TYPE, is_inter_fan, dev_num, no_poll_cmd_tab, poll_cmd_tab);
}






