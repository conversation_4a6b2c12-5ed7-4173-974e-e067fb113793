#include "common.h"
#include "battery.h"
#include "BattCharge_product.h"
#include "BattSOXCalc.h"
#include "MasterOper.h"
#include "SlaveOper.h"

SetChgParaOper_t SetChgParaOper;
BMSChgButBDUDischgOper_t BMSChgButBDUDischgOper;
SmartLi_CalcSysMaxChargeCurrOper_t SmartLi_CalcSysMaxChargeCurrOper;
MultBattMixPowerOnAndOffVolt_t MultBattMixPowerOnAndOffVolt;
SmartLi_CalDischargeOutputVoltOper_t SmartLi_CalDischargeOutputVoltOper;
GetPowerdownVoltOper_t GetPowerdownVoltOper;

/***************************************************************************
 * @brief    状态切换，进入在线非浮充时的初始化
 **************************************************************************/
void EnterStandbyInit(T_BattDealInfoStruct *pBattDeal)
{
    pBattDeal->ucBatStatus = BATT_MODE_STANDBY;
    pBattDeal->bSelfRechargeTimerEnable = False;
    pBattDeal->slSelfRechargeTimer = 0;
    pBattDeal->wBattFullHoldMinutes = 0; // 电池SOC置满计数初始化
    pBattDeal->slSelfDischgCalcTime = 0;
    pBattDeal->bChargeCurrMin = FALSE;
    pBattDeal->bChargeThroughEnable = True;
    pBattDeal->bInitMixDischg = False;
    return;
}

/***************************************************************************
 * @brief    电池自补电判断
 **************************************************************************/
BOOLEAN BattSelfRechargeJudgement(T_BattInfo *pBattIn)
{
    return (pBattIn->tData.fBatVol < pBattIn->tPara.fBattRefreshVoltThreshold &&
            pBattIn->tData.fCellVoltMax < CELL_SUPPL_MAX_VOLT &&
            pBattIn->tData.fCellVoltMin < pBattIn->tPara.fCellSupplVolt);
}

/***************************************************************************
 * @brief    判断充电是否进入直通
 **************************************************************************/
void JudgeChargeThrough(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    pBattDeal->bChargeThroughBak = pBattDeal->bThroughChg;
    pBattDeal->bChargeThroughEnable = False;
    pBattDeal->bThroughChg = False;
    return;
}

/***************************************************************************
 * @brief    判断是否转充电
 **************************************************************************/
BOOLEAN IfShouldRecharge(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    WORD wSoc = CalcSOCFromCap(pBattDeal, pBattIn->tPara.wBattCap, 0);

    if (pBattDeal->slSelfRechargeTimer >= ONE_HOUR_MINUTES)
    {
        return True;
    }
    if (wSoc <= pBattIn->tPara.wRechargeSOC)
    {
        return True;
    }

    return False;
}

/***************************************************************************
 * @brief    设置主机操作函数
 **************************************************************************/
void SetMasterOperFunc(void)
{
    SetChgParaOper = SetChgPara_Master;
    BMSChgButBDUDischgOper = BMSChgButBDUDischg_Master;
    MultBattMixPowerOnAndOffVolt = NULL;
    SmartLi_CalDischargeOutputVoltOper = SmartLi_CalcDischargeOutputVolt_Master;
    SmartLi_CalcSysMaxChargeCurrOper = SmartLi_CalcSysMaxChargeCurr_Master;
    GetPowerdownVoltOper = GetPowerdownVolt_Master;
    return;
}

/***************************************************************************
 * @brief    设置从机操作函数
 **************************************************************************/
void SetSlaveOperFunc(void)
{
    SetChgParaOper = SetChgPara_Slave;
    BMSChgButBDUDischgOper = BMSChgButBDUDischg_Slave;
    MultBattMixPowerOnAndOffVolt = NULL;
    SmartLi_CalDischargeOutputVoltOper = SmartLi_CalcDischargeOutputVolt_Slave;
    SmartLi_CalcSysMaxChargeCurrOper = SmartLi_CalcSysMaxChargeCurr_Slave;
    GetPowerdownVoltOper = GetPowerdownVolt_Slave;
    return;
}

/***************************************************************************
 * @brief    计算系统来电电压阈值、停电电压阈值、掉电电压阈值
 **************************************************************************/
void GetMixOnOffVolt(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal, T_SysPara *pSysPara)
{
    return;
}