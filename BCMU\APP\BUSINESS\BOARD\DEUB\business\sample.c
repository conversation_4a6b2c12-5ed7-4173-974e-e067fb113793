#include <rtthread.h>
#include <rtdevice.h>
#include <board.h>
#include <drv_common.h>
#include <math.h>
#include <string.h>
#include "sample.h"
#include "data_type.h"
#include "utils_thread.h"
#include "adc_ctrl.h"
//#include "hal_adc.h"
//#include "pin_define.h" 补充hal相关引脚定义，用于采样，do，片选
#include "utils_server.h"
#include "pin_ctrl.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "ee_public_info.h"

#define GOTO_IF_FAIL(expr, position)  do{ if(RT_EOK != (expr)) { goto position; } }while(0)     ///< expr失败跳转到position位置

/***********************  变量定义  ************************/
static unsigned int   s_adc_data[AI_END][ADCCONVERTEDVALUES_SAMPLING_NUM];
static unsigned int   s_digital_data[DI_END] = {0}; // 先交流DI后直流DI
static unsigned int   s_analog_data[AI_END];
static unsigned int   s_iso_switch_status[ISO_SWITCH_NUM] = {PIN_LOW};

static struct rt_device *hwtimer_dev = RT_NULL;
static T_AcSampleStruct AcSampleData;
volatile rt_bool_t AcSampleFlag = DISABLE;

static float s_fFreq = SAMPLE_DEFAULT_FREQ_50;
static int s_b_frq_sm = b_FREQ_SM_STATUS_CALC;
static int s_calc_status = B_FREQ_CALC_NORMAL;
static float a_fFreq[FREQ_CALC_NUM] = {0};
static rt_uint32_t b_Freq_count = 0;

/*********************  静态函数原型定义  **********************/
static int DcAnalogSample(void);
static void CalAcDiSampleData(void);
static void DcDigitalSample(void);
static unsigned int MedianFilter(unsigned int data[ADCCONVERTEDVALUES_SAMPLING_NUM]);
static void hal_board_gpio_init(void);
static int adc_get_data_by_channel(ADC_SAMPLE_CHANNEL channel, rt_adc_device_t adc_dev);
static int di_get_data_by_channel(eDI_SAMPLE channel);
static void set_selection_sample_gpio(ADC_SAMPLE_CHANNEL channel);
static void AcSampleInSys(void);
static rt_bool_t ACAiSample(rt_uint8_t index, rt_adc_device_t adc_dev);
static rt_bool_t ACDiSample(rt_uint8_t index);
static void CalAcAiSampleData(void);
static float SimpleCalcFreq(float *pAVol, int *pStatus);
static float GetBaseFreq(void);
static void fArraySort(float a[], int len);
static rt_err_t fDivision(float dividend, float divisor, float *result);
int judge_iso_switch_status(int iso_type, int iso_status);
int cal_unbalance_res(unbalance_info_t* unbalance_info);
int deal_balance_iso(balance_info_t* balance_info);
int cal_balance_res(balance_info_t* balance_info);

static DI_CHANNEL_INFO_STRUCT s_di_sample_info[DI_END] =
{
     {DI1, 0, PIN_D2_S1},
     {DI2, 0, PIN_D1_S1},
     {DI3, 0, PIN_D0_S1},
};

static ADC_CHANNEL_INFO_STRUCT s_sample_info[AI_END] =
{
     {AD0,    0, 0, DEUB_DATA_ID_PHASE_VOL             },           // 相电压
     {AD1,    0, 1, DEUB_DATA_ID_PHASE_VOL + 1         },           // 相电压
     {AD2,    0, 2, DEUB_DATA_ID_PHASE_VOL + 2         },           // 相电压
     {V_ISO,  0, 3, DEUB_DATA_ID_INSULATION_DETECT_VOL },           // 绝缘检测电压采样
     {V_OUT,  0, 4, DEUB_DATA_ID_BUS_VOL               },           // 母排电压检测
     {I_BAT1, 0, 5, DEUB_DATA_ID_BATTERY_CUR           },           // 电池电流
     {I_BAT2, 1, 5, DEUB_DATA_ID_BATTERY_CUR + 1       },           // 电池电流
     {I_BAT3, 2, 5, DEUB_DATA_ID_BATTERY_CUR + 2       },           // 电池电流
     {I_BAT4, 3, 5, DEUB_DATA_ID_BATTERY_CUR + 3       },           // 电池电流
};


static msg_map sample_msg_map[] =
{
     {0,NULL},//临时添加解决编译问题
};

void* sample_init_sys(void *param)
{
    server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(sample_msg_map) / sizeof(msg_map);
    register_server_msg_map(sample_msg_map, server_info);
    hal_board_gpio_init();//初始化IO模式
    //humi_sensor_init();
    return NULL;
}

static rt_err_t timeout_function(rt_device_t dev, rt_size_t size)
{
    AcSampleFlag = ENABLE;
    return RT_EOK;
}

static int hwtimer_init(rt_hwtimerval_t timeout_s)
{
    rt_err_t ret = RT_EOK;
    // struct rt_device_pwm *hwtimer_dev;         /* 定时器设备句柄 */
    rt_hwtimer_mode_t mode = HWTIMER_MODE_PERIOD; /* 定时器模式 */
    // rt_hwtimerval_t timeout_s;                 /* 定时器超时值 */
    rt_uint32_t freq = 1000000;                   /* 计数频率 */

    /* 查找定时器设备 */
    hwtimer_dev = (struct rt_device *)rt_device_find(HWTIMER_DEV_NAME);
    if (hwtimer_dev == RT_NULL)
    {
        rt_kprintf("can't find %s device!\n", HWTIMER_DEV_NAME);
        return RT_ERROR;
    }

    /* 以读写方式打开设备 */
    ret = rt_device_open(hwtimer_dev, RT_DEVICE_OFLAG_RDWR);
    if (ret != RT_EOK)
    {
        rt_kprintf("open %s device failed!\n", HWTIMER_DEV_NAME);
        return ret;
    }

    /* 设置超时回调函数 */
    rt_device_set_rx_indicate(hwtimer_dev, timeout_function);
    /* 设置计数频率 */
    ret = rt_device_control(hwtimer_dev, HWTIMER_CTRL_FREQ_SET, &freq);
    if (ret != RT_EOK)
    {
        rt_kprintf("set frequency failed! ret is :%d\n", ret);
        return ret;
    }
    /* 设置模式为周期性定时器 */
    ret = rt_device_control(hwtimer_dev, HWTIMER_CTRL_MODE_SET, &mode);
    if (ret != RT_EOK)
    {
        rt_kprintf("set mode failed! ret is :%d\n", ret);
        return ret;
    }

    if (rt_device_write(hwtimer_dev, 0, &timeout_s, sizeof(timeout_s)) != sizeof(timeout_s))
    {
        rt_kprintf("set timeout value failed\n");
        return RT_ERROR;
    }

    return ret;
}

static int hwtimer_close(void)
{
    rt_device_close(hwtimer_dev);
    hwtimer_dev = RT_NULL;
    return SUCCESSFUL;
}

static int iso_switch_control(void)
{
    rt_pin_write(PIN_ISO1, s_iso_switch_status[0]);
    rt_pin_write(PIN_ISO2, s_iso_switch_status[1]);
    rt_pin_write(PIN_ISO3, s_iso_switch_status[2]);
    rt_thread_mdelay(5);
    return RT_EOK;
}

/* 采样数据 */
void sample_main(void *parameter)
{
    unbalance_info_t unbalance_info = {0};
    balance_info_t balance_info = {0};
    while (is_running(TRUE))
    {
        /* 交流采样 */
        AcSampleInSys();
        CalAcAiSampleData();
        CalAcDiSampleData();

        /* 直流采样 */
        iso_switch_control();
        DcDigitalSample();
        DcAnalogSample();

        cal_iso_value(&balance_info, &unbalance_info);
        rt_thread_mdelay(20);
    }
}

void AcSampleInSys(void)
{
    static float last_freq = 0;
    rt_uint32_t index = 0;
    const rt_uint32_t AiSampleChannel[AC_VOLTAGE_CHANNEL_NUM] = {AD0, AD1, AD2};
    rt_adc_device_t adc_dev = RT_NULL;
    rt_hwtimerval_t timeout_s;

    adc_dev = (rt_adc_device_t)rt_device_find(ADC_DEV_NAME);
    if (adc_dev == RT_NULL)
    {
        rt_kprintf("AC sample failed! can't find %s device!\n", ADC_DEV_NAME);
        return;
    }

    // 频率发生变化时, 需要重新初始化定时器
    if(if_float_equal(last_freq, 0) || !if_float_equal(last_freq, GetBaseFreq())) {
        if(hwtimer_dev != RT_NULL) {
            hwtimer_close();
        }
        timeout_s.sec = 0;
        timeout_s.usec = 1000000 / (AC_SAMPLE_TIMES * GetBaseFreq());
        hwtimer_init(timeout_s);
        if (hwtimer_dev == RT_NULL)
        {
            rt_kprintf("AC sample failed! can't find %s device!\n", HWTIMER_DEV_NAME);
            return;
        }
        last_freq = GetBaseFreq();
    }

    // 交流AI采样
    for (index = 0; index < AC_VOLTAGE_CHANNEL_NUM; index++)
    {
        AcSampleData.ucChannelID = AiSampleChannel[index];
        rt_adc_enable(adc_dev, s_sample_info[AcSampleData.ucChannelID].adc_channel);

        AcSampleFlag = DISABLE;
        while (ACAiSample(index, adc_dev));

        rt_adc_disable(adc_dev, s_sample_info[AcSampleData.ucChannelID].adc_channel);

        /* 采样次数清零 */
        if (AcSampleData.ucCurrTimes >= AC_SAMPLE_TIMES)
        {
            AcSampleData.ucCurrTimes = 0;
        }
    }

    // 交流DI采样
    for (index = 0; index < DI_END; index++)
    {
        AcSampleData.ucChannelID = index;

        AcSampleFlag = DISABLE;
        while (ACDiSample(index));

        /* 采样次数清零 */
        if (AcSampleData.ucCurrTimes >= AC_SAMPLE_TIMES)
        {
            AcSampleData.ucCurrTimes = 0;
        }
    }
}

/* 交流AI采样 */
static rt_bool_t ACAiSample(rt_uint8_t index, rt_adc_device_t adc_dev)
{
    if (AcSampleData.ucCurrTimes >= AC_SAMPLE_TIMES)
    {
        return 0;
    }
    while (AcSampleFlag == DISABLE);

    AcSampleData.ucVoltageData[index][AcSampleData.ucCurrTimes] = rt_adc_read(adc_dev, s_sample_info[AcSampleData.ucChannelID].adc_channel);
    AcSampleData.ucCurrTimes++;
    AcSampleFlag = DISABLE; // 禁止采样

    return 1;
}

/* 交流DI采样 */
static rt_bool_t ACDiSample(rt_uint8_t index)
{
    if (AcSampleData.ucCurrTimes >= AC_SAMPLE_TIMES)
    {
        return 0;
    }
    while (AcSampleFlag == DISABLE);

    AcSampleData.ucDIData[index][AcSampleData.ucCurrTimes] = rt_pin_read(s_di_sample_info[index].di_pin);
    AcSampleData.ucCurrTimes++;
    AcSampleFlag = DISABLE; // 禁止采样

    return 1;
}

static int GetFreqSM(void)
{
    return s_b_frq_sm;
}

static void SetFreqSM(int slVal)
{
    s_b_frq_sm = slVal;
}

static float GetBaseFreq(void)
{
    return s_fFreq;
}

static void SetFinalFreq(float fVal)
{
    unsigned short mock_switch = 0;
    // 特征点平滑
    if (fVal >= 49.5 && fVal <= 50.5)
    {
        s_fFreq = SAMPLE_DEFAULT_FREQ_50;
    }
    else if (fVal >= 59.5 && fVal <= 60.5)
    {
        s_fFreq = SAMPLE_DEFAULT_FREQ_60;
    }
    else
    {
        s_fFreq = fVal;
    }
    get_one_data(DEUB_DATA_ID_MOCK_SWITCH, &mock_switch);
    if(mock_switch != TRUE)
    {
        set_one_data(DEUB_DATA_ID_AC_FREQUENCY, &s_fFreq);
    }

}

static float SimpleCalcFreq(float *pAVol, int *pStatus)
{
    int index1 = -1, index2 = -1, i, diff_index;
    float a1, b1, a2, b2, temp1, temp2;
    float fT = 0, fSegT, fRetFreq;

    GOTO_IF_FAIL(fDivision(1.0, (GetBaseFreq()*AC_SAMPLE_TIMES), &fSegT), END);
    for (i = 0; i < AC_SAMPLE_TIMES - 2; i++)
    {
        if (pAVol[i] * pAVol[i + 1] < 0 && pAVol[i + 1] * pAVol[i + 2] > 0)
        {
            if (-1 == index1) // 第一次零点
            {
                index1 = i;
                a1 = fabs(pAVol[i]);
                b1 = fabs(pAVol[i + 1]);
            }
            else // 第二次零点
            {
                index2 = i;
                a2 = fabs(pAVol[i]);
                b2 = fabs(pAVol[i + 1]);
                break;
            }
        }
    }
    if (-1 != index1 && -1 != index2)
    {
        diff_index = index2 - index1;
        GOTO_IF_FAIL(fDivision(a1, (a1 + b1), &temp1), END);
        GOTO_IF_FAIL(fDivision(a2, (a2 + b2), &temp2), END);
        fT = (diff_index + temp2 - temp1) * fSegT * 2;
        GOTO_IF_FAIL(fDivision(1.0, fT, &fRetFreq), END);

        if (fRetFreq > VALID_FREQ_MIN && fRetFreq < VALID_FREQ_MAX)
        {
            *pStatus = B_FREQ_CALC_NORMAL;
            return fRetFreq;
        }
        else
        {
            *pStatus = B_FREQ_CALC_ABNORMAL;
            return GetBaseFreq();
        }
    }
    else
    {
END:
        *pStatus = B_FREQ_CANNOT_CALC;
        return GetBaseFreq();
    }
}

static void DoFreqSMStable(float *pAVol)
{
    float fTmpFreq = 0;
    static int s_unstable_cnt = 0;
    static int s_uncalc_cnt = 0;
    fTmpFreq = SimpleCalcFreq(pAVol, &s_calc_status);

    // 计算异常, 或连续n次不稳定, 或连续n次采不到值
    if (B_FREQ_CALC_ABNORMAL == s_calc_status || s_unstable_cnt >= FREQ_CALC_UNSTABLE_TIMES || s_uncalc_cnt >= FREQ_CALC_UNSTABLE_TIMES)
    {
        s_unstable_cnt = 0;
        s_uncalc_cnt = 0;
        SetFreqSM(b_FREQ_SM_STATUS_CALC);
        return;
    }

    if (B_FREQ_CANNOT_CALC == s_calc_status)
    {
        s_uncalc_cnt++;
    }
    else
    {
        s_uncalc_cnt = 0;
        if (fabs(GetBaseFreq() - fTmpFreq) >= STABLE_THR_STABLE_FREQ)
        {
            s_unstable_cnt++;
        }
        else
        {
            s_unstable_cnt = 0;
        }
    }
}

static void DoFreqSMCalc(float *pAVol)
{
    float fTmpFreq = 0, AverageFreq = 0;
    fTmpFreq = SimpleCalcFreq(pAVol, &s_calc_status);

    // 采样n次, 排序后取中间4个数据的平均值
    // if (B_FREQ_CANNOT_CALC != s_calc_status)
    if (B_FREQ_CALC_NORMAL == s_calc_status)
    {
        a_fFreq[b_Freq_count] = fTmpFreq;
        b_Freq_count++;
        if (b_Freq_count >= FREQ_CALC_NUM)
        {
            b_Freq_count = 0;
            fArraySort(a_fFreq, sizeof(a_fFreq) / sizeof(a_fFreq[0]));
            if (FREQ_CALC_NUM < 4) {
                AverageFreq = a_fFreq[FREQ_CALC_NUM / 2];
            }
            else {
                AverageFreq = (a_fFreq[FREQ_CALC_NUM / 2 - 2] + a_fFreq[FREQ_CALC_NUM / 2 - 1] + a_fFreq[FREQ_CALC_NUM / 2] + a_fFreq[FREQ_CALC_NUM / 2 + 1]) / 4;
            }

            SetFinalFreq(AverageFreq);
            SetFreqSM(b_FREQ_SM_STATUS_STABLE);
        }
    }
}

/* 交流频率计算状态机 */
static void AcFreqCalcSM(float *pAVol)
{
    switch (GetFreqSM())
    {
    case b_FREQ_SM_STATUS_CALC:
        DoFreqSMCalc(pAVol);
        break;
    case b_FREQ_SM_STATUS_STABLE:
        DoFreqSMStable(pAVol);
        break;
    default:
        break;
    }
}

/* 交流AI采样数据计算 */
static void CalAcAiSampleData(void)
{
    int i, index = 0;
    float fVolTmp[AC_SAMPLE_TIMES], VolSum = 0, fSample = 0.0, afTmp[3] = {0.0};
    unsigned short mock_switch = 0;
    for (index = 0; index < 3; index++)
    {
        VolSum = 0.0;
        for (i = 0; i < AC_SAMPLE_TIMES; i++)
        {
            fSample = AcSampleData.ucVoltageData[index][i];
            fVolTmp[i] = (2555.0 / 9.0) * (fSample * 3.0 / 4095.0 - 1.5);
            // fVolTmp[i] = (511.0 / 2457.0) * fSample - (2555.0 / 6.0);
            VolSum += pow(fVolTmp[i], 2);
        }
        afTmp[index] = VolSum / AC_SAMPLE_TIMES;
        AcSampleData.AcVoltageValue[index] = sqrt(afTmp[index]);
        get_one_data(DEUB_DATA_ID_MOCK_SWITCH, &mock_switch);
        if(mock_switch != TRUE)
        {
            set_one_data(s_sample_info[index].sid, &AcSampleData.AcVoltageValue[index]);
        }


        // 计算交流A路频率
        if(0 == index) {
            if(AcSampleData.AcVoltageValue[0] >= MIN_VALID_AC_VOLT && AcSampleData.AcVoltageValue[0] <= MAX_VALID_AC_VOLT) {
                AcFreqCalcSM(fVolTmp);
            }
            else {
                SetFinalFreq(SAMPLE_DEFAULT_FREQ_50);
            }
        }
    }

    return;
}


/* 交流DI采样数据计算 */
static void CalAcDiSampleData(void)
{
    static int di_count[DI3] = {0};
    int i, j, last_di_status, di_convert_times, result;
    unsigned short mock_switch = 0;
    // 后两路为两路交流DI信号
    for(i = DI2; i < DI_END; i++) {
        last_di_status = AcSampleData.ucDIData[i][0];
        di_convert_times = 0;
        result = 1;
        for(j = 0; j < AC_SAMPLE_TIMES; j++) {
            if(AcSampleData.ucDIData[i][j] != last_di_status) { // 当前DI状态与上一次不同,即存在状态翻转
                di_convert_times++;
            }
            last_di_status = AcSampleData.ucDIData[i][j];
        }
        if(di_convert_times == 0) { // DI状态检测为断开
            result = 1;
        }
        else if(di_convert_times >= AC_DI_CONVERT_MIN_TIMES && di_convert_times <= AC_DI_CONVERT_MAX_TIMES) { // DI状态检测为闭合
            result = 0;
        }
        else { // DI采样数据无效
            return;
        }
        if(s_digital_data[i] == result) {
            di_count[i - DI2] = 0;
        }
        else {
            di_count[i - DI2]++;
        }
        // 连续检测DI状态n次相同, 赋值
        if(di_count[i - DI2] >= DI_SAMPLE_STABLE_TIMES) {
            get_one_data(DEUB_DATA_ID_MOCK_SWITCH, &mock_switch);
            if(mock_switch != TRUE)
            {
                set_one_data(DEUB_DATA_ID_INPUT_DRY_CONTACT + i,&result);
            }
            
            s_digital_data[i] = result;
            di_count[i - DI2] = 0;
        }
    }

    return;
}



static void DcDigitalSample(void)
{
    // 一路直流DI检测，第一路
    static int di_count = 0;
    int result = 0;

    result = di_get_data_by_channel(DI1);
    if(s_digital_data[0] == result) {
        di_count = 0;
    }
    else
    {
        di_count++;
    }

    if(di_count >= DI_SAMPLE_STABLE_TIMES)
    {
        set_one_data(DEUB_DATA_ID_INPUT_DRY_CONTACT, &result);
        s_digital_data[0] = result;
        di_count = 0;
    }

    return;
}



float get_cali_batt_curr(int index, float origin_curr)
{
    float batt_curr_zero_point = 0.0;
    float batt_curr_reset_threshold = 0.0;
    float calibrated_value = origin_curr;
    get_one_data(DEUB_DATA_ID_BATTERY_CURR_ZERO_POINT + index, &batt_curr_zero_point);
    get_one_data(DEUB_DATA_ID_BATTERY_CURR_RESET_THRESHOLD + index, &batt_curr_reset_threshold);

    // 电池电流在+-0.2%的读数置零
    if(fabs(origin_curr) < batt_curr_reset_threshold * ZERO_DRIFT_THRESHOLD)
    {
        calibrated_value = 0.0f;
        return calibrated_value;
    }

    // 检测零漂大于+-0.2的分流器满量程，进行零点校准
    if(fabs(batt_curr_zero_point) > batt_curr_reset_threshold * ZERO_DRIFT_THRESHOLD)
    {
        // 读数大于0.3%满量程，减去零漂
        calibrated_value -= batt_curr_zero_point;
    }

    return calibrated_value;
}


static int DcAnalogSample(void) {
    unsigned int i,j;
    float tmp_data = 0.0;
    float ac_vol_cali_slope = 0.0;
    rt_adc_device_t adc_dev;
    unsigned short mock_switch = 0;
    adc_dev = (rt_adc_device_t)rt_device_find(ADC_DEV_NAME);
    if (adc_dev == RT_NULL)
    {
        rt_kprintf("adc sample run failed! can't find %s device!\n", ADC_DEV_NAME);
        return RT_ERROR;
    }

    for(i = V_ISO; i < AI_END; i++)
    {
        for(j = 0; j < ADCCONVERTEDVALUES_SAMPLING_NUM; j++)
        {
            s_adc_data[i][j] = adc_get_data_by_channel(i, adc_dev);
        }
        s_analog_data[i] = MedianFilter(s_adc_data[i]);
        switch (i)
        {
            case V_ISO:
                get_one_data(DEUB_DATA_ID_AC_VOL_CALIBRATE_SLOPE, &ac_vol_cali_slope);            // 斜率校准参数
                tmp_data = ac_vol_cali_slope * REFER_VOLTAGE * s_analog_data[V_ISO] / 4095.0;     // 绝缘检测电压
                break;
            case V_OUT:
                tmp_data = (1.0 * 150.0 * REFER_VOLTAGE * s_analog_data[V_OUT] / 4095.0);   // 母排电压
                break;
            default:
                tmp_data =((1.0 * REFER_VOLTAGE * s_analog_data[i] / 4095.0 - 1.5) / 50.4 * mVOLT_PER_VOLT) * 100;   // 电池电流
                tmp_data = get_cali_batt_curr(i - I_BAT1, tmp_data);
                break;
        }
        get_one_data(DEUB_DATA_ID_MOCK_SWITCH, &mock_switch);
        if(mock_switch != TRUE)
        {
            set_one_data(s_sample_info[i].sid, &tmp_data);
        }
        
    }

    return RT_EOK;
}

//功能引脚初始化
static void hal_board_gpio_init(void)
{
    rt_pin_mode(PIN_A0_S1, PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_A1_S1, PIN_MODE_OUTPUT);

    rt_pin_mode(PIN_ISO1, PIN_MODE_OUTPUT); /* 绝缘检测开关量 */
    rt_pin_mode(PIN_ISO2, PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_ISO3, PIN_MODE_OUTPUT);

    rt_pin_mode(PIN_D0_S1, PIN_MODE_INPUT_PULLUP); /* 设置引脚模式 */
    rt_pin_mode(PIN_D1_S1, PIN_MODE_INPUT_PULLUP);
    rt_pin_mode(PIN_D2_S1, PIN_MODE_INPUT_PULLUP);

    rt_pin_mode(PIN_LED_CONTROL, PIN_MODE_OUTPUT);//此处灯的控制需要补充其用途
}

// 采样通道选择
static void set_selection_sample_gpio(ADC_SAMPLE_CHANNEL channel)
{
    if((s_sample_info[channel].select_status & 0x01) == 1)
        rt_pin_write(PIN_A0_S1, 1);
    else
        rt_pin_write(PIN_A0_S1, 0);

    if((s_sample_info[channel].select_status>>1 & 0x01) == 1)
        rt_pin_write(PIN_A1_S1, 1);
    else
        rt_pin_write(PIN_A1_S1, 0);

     return ;
}
//从通道获取AI数据
static int adc_get_data_by_channel(ADC_SAMPLE_CHANNEL channel, rt_adc_device_t adc_dev)
{
    rt_uint32_t value;

    set_selection_sample_gpio(channel);
    rt_thread_mdelay(10);
    rt_adc_enable(adc_dev, s_sample_info[channel].adc_channel);
    value = rt_adc_read(adc_dev, s_sample_info[channel].adc_channel);
    rt_adc_disable(adc_dev, s_sample_info[channel].adc_channel);
    return value;
}
//从通道获取DI数据
static int di_get_data_by_channel(eDI_SAMPLE channel)
{
    rt_uint32_t value;

    rt_thread_mdelay(5);
    value = rt_pin_read(s_di_sample_info[channel].di_pin);

    return value;
}

// 设置绝缘检测开关通断(1为通,0为断)
int set_iso_switch_status(unsigned int index, unsigned int status)
{
    status = status ? PIN_HIGH : PIN_LOW;
    s_iso_switch_status[index - 1] = status;
    return SUCCESSFUL;
}

unsigned int get_iso_switch_status(unsigned int index)
{
    return s_iso_switch_status[index - 1];
}

// 采样滤波
static unsigned int MedianFilter(unsigned int data[ADCCONVERTEDVALUES_SAMPLING_NUM])
{
    unsigned int temp, average;
    for (int i = 0; i < ADCCONVERTEDVALUES_SAMPLING_NUM - 1; i++)
    {
        for (int j = i + 1; j < ADCCONVERTEDVALUES_SAMPLING_NUM; j++)
        {
            if (data[j] < data[i])
            {
                temp = data[i];
                data[i] = data[j];
                data[j] = temp;
            }
        }
    }
    if (ADCCONVERTEDVALUES_SAMPLING_NUM < 4)
    {
        average = data[ADCCONVERTEDVALUES_SAMPLING_NUM / 2];
    }
    else
    {
        average = (data[ADCCONVERTEDVALUES_SAMPLING_NUM / 2 - 2] + data[ADCCONVERTEDVALUES_SAMPLING_NUM / 2 - 1] + data[ADCCONVERTEDVALUES_SAMPLING_NUM / 2] + data[ADCCONVERTEDVALUES_SAMPLING_NUM / 2 + 1]) / 4;
    }
    return average;
}

static void fArraySort(float a[], int len)
{
    int k, j;
    float temp;
    for (k = 0; k < len - 1; k++)
    {
        for (j = 0; j < len - 1 - k; j++)
        {
            if (a[j] < a[j + 1])
            {
                temp = a[j];
                a[j] = a[j + 1];
                a[j + 1] = temp;
            }
        }
    }
}

/**
 * 除法运算
 *
 * @param dividend - 被除数
 * @param divisor - 除数
 * @param result - 商
 *
 * @return 除数等于0返回 RT_ERROR, 否则返回 RT_EOK
 * 
 */
static rt_err_t fDivision(float dividend, float divisor, float *result) {
    rt_err_t ret = RT_EOK;

    if(divisor >= 0 && divisor <= 0) {
        ret = RT_ERROR;
    }else {
        *result = dividend / divisor;
    }
    return ret;
}



static inline float get_dc_vol()
{
    return (1.0 * 150.0 * REFER_VOLTAGE * s_analog_data[V_OUT] / 4095.0);
}

static inline float get_iso_vol()
{   
    return (1.0 * REFER_VOLTAGE * s_analog_data[V_ISO] / 4095.0);
}



// 阻抗计算
int deal_unbalance_iso1(unbalance_info_t* unbalance_info)
{
    float iso_vol = 0, dc_vol = 0;
    static unsigned int count = 0;
    count++;
    if(count > UNBALANCE_WAIT_TIMES)
    {
        count = 0;
    }
    else
    {
        return SUCCESSFUL;
    }

    get_one_data(DEUB_DATA_ID_INSULATION_DETECT_VOL, &iso_vol);
    get_one_data(DEUB_DATA_ID_BUS_VOL, &dc_vol);

    unbalance_info->iso_switch = UNBALANCE_D1CLOSE_D2CLOSE_D3OPEN;
    unbalance_info->s_fV_step1_neg = iso_vol * 302 / 2;
    unbalance_info->s_fV_step1_pos = dc_vol - unbalance_info->s_fV_step1_neg;
    return SUCCESSFUL;
}

int deal_unbalance_iso2(unbalance_info_t* unbalance_info)
{
    float iso_vol = 0, dc_vol = 0;
    static unsigned int count = 0;
    count++;
    if(count > UNBALANCE_WAIT_TIMES)
    {
        count = 0;
    }
    else
    {
        return SUCCESSFUL;
    }
    get_one_data(DEUB_DATA_ID_INSULATION_DETECT_VOL, &iso_vol);
    get_one_data(DEUB_DATA_ID_BUS_VOL, &dc_vol);

    unbalance_info->iso_switch = UNBALANCE_D1CLOSE_D2CLOSE_D3CLOSE;
    unbalance_info->s_fV_step2_neg = iso_vol * 302 / 2;
    unbalance_info->s_fV_step2_pos = dc_vol - unbalance_info->s_fV_step2_neg;
    return SUCCESSFUL;
}

int deal_unbalance_iso3(unbalance_info_t* unbalance_info)
{
    float iso_vol = 0, dc_vol = 0;
    static unsigned int count = 0;
    count++;
    if(count > UNBALANCE_WAIT_TIMES)
    {
        count = 0;
    }
    else
    {
        return SUCCESSFUL;
    }
    
    get_one_data(DEUB_DATA_ID_INSULATION_DETECT_VOL, &iso_vol);
    get_one_data(DEUB_DATA_ID_BUS_VOL, &dc_vol);

    unbalance_info->iso_switch = UNBALANCE_ISNCAL;
    unbalance_info->s_fV_step3_neg = iso_vol * 302 / 2;
    unbalance_info->s_fV_step3_pos = dc_vol - unbalance_info->s_fV_step3_neg;
    return SUCCESSFUL;
}




int deal_unbalance_res(unbalance_info_t* unbalance_info)
{
    if(unbalance_info->iso_switch == UNBALANCE_NONE)
    {
        unbalance_info->iso_switch = UNBALANCE_D1CLOSE_D2OPEN_D3CLOSE;
        judge_iso_switch_status(ISO_TYPE_UNBALANCE, unbalance_info->iso_switch);
        return SUCCESSFUL;
    }

    switch (unbalance_info->iso_switch)
    {
        case UNBALANCE_D1CLOSE_D2OPEN_D3CLOSE:
            deal_unbalance_iso1(unbalance_info);
            break;

        case UNBALANCE_D1CLOSE_D2CLOSE_D3OPEN:
            deal_unbalance_iso2(unbalance_info);
            break;

        case UNBALANCE_D1CLOSE_D2CLOSE_D3CLOSE:
            deal_unbalance_iso3(unbalance_info);
            break;
        
        case UNBALANCE_ISNCAL:
            set_iso_stable_vol();
            cal_unbalance_res(unbalance_info);
            unbalance_info->iso_switch = UNBALANCE_D1CLOSE_D2OPEN_D3CLOSE;
            break;
        
        default:
            break;
    }
    judge_iso_switch_status(ISO_TYPE_UNBALANCE, unbalance_info->iso_switch);
    return SUCCESSFUL;
}

int judge_iso_switch_status(int iso_type, int iso_status)
{
    if(iso_type == ISO_TYPE_BALANCE)
    {
        set_iso_switch_status(1, PIN_HIGH);
        set_iso_switch_status(2, PIN_HIGH);
        set_iso_switch_status(3, PIN_HIGH);
    }
    else if(iso_type == ISO_TYPE_UNBALANCE)
    {
        switch (iso_status)
        {
            case UNBALANCE_D1CLOSE_D2OPEN_D3CLOSE:
            {
                set_iso_switch_status(1, PIN_HIGH);
                set_iso_switch_status(2, PIN_LOW);
                set_iso_switch_status(3, PIN_HIGH);
                break;
            }

            case UNBALANCE_D1CLOSE_D2CLOSE_D3OPEN:
            {
                set_iso_switch_status(1, PIN_HIGH);
                set_iso_switch_status(2, PIN_HIGH);
                set_iso_switch_status(3, PIN_LOW);
                break;
            }

            case UNBALANCE_D1CLOSE_D2CLOSE_D3CLOSE:
            case UNBALANCE_ISNCAL:
            {
                set_iso_switch_status(1, PIN_HIGH);
                set_iso_switch_status(2, PIN_HIGH);
                set_iso_switch_status(3, PIN_HIGH);
                break;
            }
            
            default:
                break;
        }
    }
    return SUCCESSFUL;
}



int cal_unbalance_res(unbalance_info_t* unbalance_info)
{
    double a,b;
    int slPosVal=0 ,slNegVal=0;
    double fRp = 0.0,fRn = 0.0;
    static float s_step1_pos_v = 0.0,s_step1_neg_v = 0.0,s_step2_pos_v = 0.0,s_step2_neg_v = 0.0,s_step3_pos_v = 0.0,s_step3_neg_v = 0.0;

    if (judge_iso_vol_stable(s_step1_pos_v, unbalance_info->s_fV_step1_pos)
    && judge_iso_vol_stable(s_step1_neg_v, unbalance_info->s_fV_step1_neg)
    && judge_iso_vol_stable(s_step2_pos_v, unbalance_info->s_fV_step2_pos)
    && judge_iso_vol_stable(s_step2_neg_v, unbalance_info->s_fV_step2_neg)
    && judge_iso_vol_stable(s_step3_pos_v, unbalance_info->s_fV_step3_pos)
    && judge_iso_vol_stable(s_step3_neg_v, unbalance_info->s_fV_step3_neg))
    {
        a=(unbalance_info->s_fV_step1_pos*unbalance_info->s_fV_step3_neg)/(unbalance_info->s_fV_step1_neg*unbalance_info->s_fV_step3_pos);
        b=(unbalance_info->s_fV_step2_pos*unbalance_info->s_fV_step3_neg)/(unbalance_info->s_fV_step2_neg*unbalance_info->s_fV_step3_pos);
        fRp = (double)(300.0*a-300.0)/(10.0361-a);
        fRn = (double)(302 - 302*b)/(10.0964*b - 1);
        slPosVal = (int)(fabs(fRp));
        slNegVal = (int)(fabs(fRn));
        set_one_data(DEUB_DATA_ID_INSULATION_RES, &slPosVal);
        set_one_data(DEUB_DATA_ID_INSULATION_RES + 1, &slNegVal);
    }
    s_step1_pos_v = unbalance_info->s_fV_step1_pos;
    s_step1_neg_v = unbalance_info->s_fV_step1_neg;
    s_step2_pos_v = unbalance_info->s_fV_step2_pos;
    s_step2_neg_v = unbalance_info->s_fV_step2_neg;
    s_step3_pos_v = unbalance_info->s_fV_step3_pos;
    s_step3_neg_v = unbalance_info->s_fV_step3_neg;
    return SUCCESSFUL;
}

int judge_iso_vol_stable(float f1, float f2)
{
    return (fabs(f1 - f2) <= ISO_DIFF_VOL);
}



int deal_balance_res(balance_info_t* balance_info)
{
    if (balance_info->iso_switch == UNBALANCE_NONE)
    {
        balance_info->iso_switch = BALANCE_GET_VOL;
        judge_iso_switch_status(ISO_TYPE_BALANCE, balance_info->iso_switch);
        return SUCCESSFUL;
    }

    switch (balance_info->iso_switch)
    {
        case BALANCE_GET_VOL:
            deal_balance_iso(balance_info);
            break;

        case BALANCE_ISNCAL:
            set_iso_stable_vol();
            cal_balance_res(balance_info);
            balance_info->iso_switch = BALANCE_GET_VOL;
            break;

        default:
            break;
    }
    judge_iso_switch_status(ISO_TYPE_BALANCE, balance_info->iso_switch);
    return SUCCESSFUL;
}



int deal_balance_iso(balance_info_t* balance_info)
{
    float iso_vol = 0, dc_vol = 0;
    dc_vol = get_dc_vol();
    iso_vol = get_iso_vol();

    balance_info->iso_switch = BALANCE_ISNCAL;
    balance_info->s_fV_step1_neg = iso_vol * 302 / 2;
    balance_info->s_fV_step1_pos = dc_vol - balance_info->s_fV_step1_neg;
    return SUCCESSFUL;
}

int cal_balance_res(balance_info_t* balance_info)
{
    int slPosVal=0, slNegVal=0;
    double fRp = 0.0, fRn = 0.0;
    float s_fV_step1_neg = 0.0, s_fV_step1_pos = 0.0;

    s_fV_step1_neg = balance_info->s_fV_step1_neg;
    s_fV_step1_pos = balance_info->s_fV_step1_pos;

    if(s_fV_step1_pos > s_fV_step1_neg)
    {
        fRn = (double)29.8920*29.9117 / (29.9117 * (s_fV_step1_pos / s_fV_step1_neg) - 29.8920);
        slNegVal = (int)(fabs(fRn));
        slPosVal = ISO_RES_MAX;
        set_one_data(DEUB_DATA_ID_INSULATION_RES, &slPosVal);
        set_one_data(DEUB_DATA_ID_INSULATION_RES + 1, &slNegVal);
    }
    else if(s_fV_step1_pos < s_fV_step1_neg)
    {
        fRp = (double)29.9117*29.8920 / (29.8920 * (s_fV_step1_neg / s_fV_step1_pos) - 29.9117);
        slNegVal = ISO_RES_MAX;
        slPosVal = (int)(fabs(fRp));
        set_one_data(DEUB_DATA_ID_INSULATION_RES, &slPosVal);
        set_one_data(DEUB_DATA_ID_INSULATION_RES + 1, &slNegVal);
    }
    else if(fabs(s_fV_step1_pos) < 0.000001 && fabs(s_fV_step1_neg) < 0.000001)
    {
        slPosVal = ISO_RES_MAX;
        slNegVal = ISO_RES_MAX;
        set_one_data(DEUB_DATA_ID_INSULATION_RES, &slPosVal);
        set_one_data(DEUB_DATA_ID_INSULATION_RES + 1, &slNegVal);
    }

    return SUCCESSFUL;
}

int cal_iso_value(balance_info_t* balance_info, unbalance_info_t* unbalance_info)
{
    unsigned short iso_type = ISO_TYPE_UNBALANCE;
    get_one_data(DEUB_DATA_ID_INSULATION_RES_MODE, &iso_type);

    if(iso_type == ISO_TYPE_BALANCE)
    {
        deal_balance_res(balance_info);
    }
    else if(iso_type == ISO_TYPE_UNBALANCE)
    {
        deal_unbalance_res(unbalance_info);
    }
    return SUCCESSFUL;
}


int set_iso_stable_vol()
{
    float iso_vol = 0.0;
    get_one_data(DEUB_DATA_ID_INSULATION_DETECT_VOL, &iso_vol);
    set_one_data(DEUB_DATA_ID_INSULATION_DETECT_STABLE_VOL, &iso_vol);
    return SUCCESSFUL;
}