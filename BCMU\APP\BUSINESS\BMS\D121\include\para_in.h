#ifndef SOFTWARE_SRC_APP_PARA_IN_H_
#define SOFTWARE_SRC_APP_PARA_IN_H_
#include "common.h"

#ifdef __cplusplus
extern "C" {
#endif

/// 参数最大值数组
FLOAT	g_afMaxPara[PARA_NUM]	=
{
	5.5, // 充电过流告警阈值
	5.5, // 充电过流保护阈值
	5.5, // 放电过流告警阈值
	4, // 电池组过压告警阈值
	4, // 电池组过压保护阈值
	127, // 单板过温保护阈值,  参数范围不具备参考
	125, // 单板过温告警阈值,  参数范围不具备参考
	80, // 环境温度高告警阈值
	10, // 环境温度低告警阈值
	3.3375, // 电池组欠压告警阈值
	3.3375, // 电池组欠压保护阈值
	4.5, // 单体过压告警阈值
	4.5, // 单体过压保护阈值
	3.2, // 单体欠压告警阈值
	3.2, // 单体欠压保护阈值
	70, // 充电高温告警阈值
	70, // 充电高温保护阈值
	70, // 放电高温告警阈值
	70, // 放电高温保护阈值
	10, // 充电低温告警阈值
	10, // 充电低温保护阈值
	10, // 放电低温告警阈值
	10, // 放电低温保护阈值
	1.5, // 单体一致性差告警阈值
	1.5, // 单体一致性差保护阈值
	60, // 电池SOC低告警阈值
	60, // 电池SOC低保护阈值
	70, // 电池SOH低告警阈值
	70, // 电池SOH低保护阈值
	2.5, // 单体损坏保护阈值
	0.25, // 电池充满电流
	2280, // 充电最长时间
	60, // 充电末期维持时间
	3.53333, // 电池补充电电压
	0.1, // 单体均衡启动压差阈值
	1440, // 历史数据保存间隔
	1, // 蜂鸣器使能
	200, // 电池容量
	4320, // 软件防盗延时
	'z', // 设备名称
	100, // 单次放电DOD
	1, // 单体动态欠压保护
	1, // 告警干接点
	2, // 告警级别
	133499934, // 启用日期
	1, // 整组欠压保护温度补偿
	'z', // BMS系统名称
	1, // 干接点默认状态
	85, // 环境温度高保护阈值
	5, // 环境温度低保护阈值
	3.75, // 电池组过压告警恢复阈值
	3.75, // 电池组过压保护恢复阈值
	3.9, // 单体过压告警恢复阈值
	3.9, // 单体过压保护恢复阈值
	3.6, // 单体欠压告警恢复阈值
	3.6, // 单体欠压保护恢复阈值
	60, // 充电高温告警恢复阈值
	60, // 充电高温保护恢复阈值
	65, // 放电高温告警恢复阈值
	65, // 放电高温保护恢复阈值
	15, // 充电低温告警恢复阈值
	15, // 充电低温保护恢复阈值
	15, // 放电低温告警恢复阈值
	15, // 放电低温保护恢复阈值
	75, // 环境温度高告警恢复阈值
	80, // 环境温度高保护恢复阈值
	15, // 环境温度低告警恢复阈值
	10, // 环境温度低保护恢复阈值
	110, // 单板过温告警恢复阈值,  参数范围不具备参考
	110, // 单板过温保护恢复阈值,  参数范围不具备参考
	1, // 加热垫使能
	90, // 陀螺仪倾角
	65, // 电池SOC低告警恢复阈值,  参数范围不具备参考
	1, // 陀螺仪防盗方式
	1, // 电池解锁方式
	5.5, // 放电过流告警恢复阈值,  参数范围不具备参考
	3.5, // 电池组欠压告警恢复阈值,  不允许设置，参数范围不具备参考性
	3.5, // 电池组欠压保护恢复阈值,  不允许设置，参数范围不具备参考性
	1, // 单体一致性差保护恢复阈值,  不允许设置，参数范围不具备参考性
	65535, // 数据保存时间,  参数范围不具备参考性
	3.9, // 单体充电截止电压
	3.73333, // 电池充满电压
	2, // 充电最大电流
	100, // 放电切换SOC
	2, // 放电模式
	58, // 恒压放电输出电压
	3.3, // 放电最大电流
	10, // 充电加热膜启动温度
	20, // 充电加热膜关闭温度
	80, // 加热膜过温阈值
	80, // 加热膜过温解除
	95, // 补充电SOC
	1, // 智能间歇充电使能
	1, // 电池组充电限流使能
	2000, // 停电时间保护阈值
	1, // 停电时间保护使能
	127, // 均流SOC补偿幅值
	127, // 均流SOC补偿斜率
	1, // 均流SOC补偿使能
	1, // 休眠指示灯
	1, // 电池地址获取方式
	32, // 电池切换地址
	1, // 充电轮换使能
	3.5, // 单体补充电电压
	48, // 末期放电电压1
	48, // 末期放电电压2
	16, // 单体温度保护屏蔽
	1, // 充放电机测试模式
	65535, // 故障前录波时间间隔
	65535, // 故障后录波时间间隔
	10, // 故障前录波条数
	10, // 故障后录波条数
	23, // 测点数量X
	255, // 可变测点ID
	6.25, // 放电过流保护阈值,  不允许设置，参数范围不具备参考性
	20, // 直流内阻异常告警阈值
	20, // 直流内阻异常保护阈值
	10, // 单体温升速率异常阈值
	20, // 自放电容量比率
	8, // 自研协议偏移地址
	20, // 容量衰减一致性差告警比率阈值
	100, // 电池异常温度高保护阈值
	1, // 充电MAP使能
	3, // 严重告警闪烁方式
	3, // 次级告警闪烁方式
	4, // 电芯类型
	10, // 放电加热膜启动温度
	20, // 放电加热膜关闭温度
	2, // NTC无效屏蔽路数
	100, // 放电末期切换SOC2,  该参数暂不使用，值随放电模式联动，恒压放电模式为0%，混用为10%
}; 

/// 参数最小值数组
FLOAT	g_afMinPara[PARA_NUM]	=
{
	0.03, // 充电过流告警阈值
	0.05, // 充电过流保护阈值
	0.25, // 放电过流告警阈值
	3.3125, // 电池组过压告警阈值
	3.3125, // 电池组过压保护阈值
	45, // 单板过温保护阈值,  参数范围不具备参考
	45, // 单板过温告警阈值,  参数范围不具备参考
	30, // 环境温度高告警阈值
	-20, // 环境温度低告警阈值
	2.625, // 电池组欠压告警阈值
	2.625, // 电池组欠压保护阈值
	3.2, // 单体过压告警阈值
	3.3, // 单体过压保护阈值
	2, // 单体欠压告警阈值
	2, // 单体欠压保护阈值
	35, // 充电高温告警阈值
	35, // 充电高温保护阈值
	35, // 放电高温告警阈值
	35, // 放电高温保护阈值
	-40, // 充电低温告警阈值
	-40, // 充电低温保护阈值
	-40, // 放电低温告警阈值
	-40, // 放电低温保护阈值
	0.05, // 单体一致性差告警阈值
	0.05, // 单体一致性差保护阈值
	0, // 电池SOC低告警阈值
	0, // 电池SOC低保护阈值
	0, // 电池SOH低告警阈值
	0, // 电池SOH低保护阈值
	1, // 单体损坏保护阈值
	0.01, // 电池充满电流
	0, // 充电最长时间
	1, // 充电末期维持时间
	3.26667, // 电池补充电电压
	0.01, // 单体均衡启动压差阈值
	1, // 历史数据保存间隔
	0, // 蜂鸣器使能
	20, // 电池容量
	0, // 软件防盗延时
	' ', // 设备名称
	10, // 单次放电DOD
	0, // 单体动态欠压保护
	0, // 告警干接点
	0, // 告警级别
	131727617, // 启用日期
	0, // 整组欠压保护温度补偿
	' ', // BMS系统名称
	0, // 干接点默认状态
	35, // 环境温度高保护阈值
	-25, // 环境温度低保护阈值
	3.0625, // 电池组过压告警恢复阈值
	3.0625, // 电池组过压保护恢复阈值
	3, // 单体过压告警恢复阈值
	3, // 单体过压保护恢复阈值
	2.2, // 单体欠压告警恢复阈值
	2.2, // 单体欠压保护恢复阈值
	25, // 充电高温告警恢复阈值
	25, // 充电高温保护恢复阈值
	30, // 放电高温告警恢复阈值
	30, // 放电高温保护恢复阈值
	0, // 充电低温告警恢复阈值
	0, // 充电低温保护恢复阈值
	-20, // 放电低温告警恢复阈值
	-20, // 放电低温保护恢复阈值
	25, // 环境温度高告警恢复阈值
	30, // 环境温度高保护恢复阈值
	-15, // 环境温度低告警恢复阈值
	-20, // 环境温度低保护恢复阈值
	30, // 单板过温告警恢复阈值,  参数范围不具备参考
	30, // 单板过温保护恢复阈值,  参数范围不具备参考
	0, // 加热垫使能
	0, // 陀螺仪倾角
	0, // 电池SOC低告警恢复阈值,  参数范围不具备参考
	0, // 陀螺仪防盗方式
	0, // 电池解锁方式
	0.18, // 放电过流告警恢复阈值,  参数范围不具备参考
	2.8, // 电池组欠压告警恢复阈值,  不允许设置，参数范围不具备参考性
	2.8, // 电池组欠压保护恢复阈值,  不允许设置，参数范围不具备参考性
	0, // 单体一致性差保护恢复阈值,  不允许设置，参数范围不具备参考性
	1, // 数据保存时间,  参数范围不具备参考性
	3.4, // 单体充电截止电压
	3.46667, // 电池充满电压
	0.01, // 充电最大电流
	0, // 放电切换SOC
	0, // 放电模式
	47, // 恒压放电输出电压
	0.01, // 放电最大电流
	-40, // 充电加热膜启动温度
	0, // 充电加热膜关闭温度
	30, // 加热膜过温阈值
	30, // 加热膜过温解除
	75, // 补充电SOC
	0, // 智能间歇充电使能
	0, // 电池组充电限流使能
	0, // 停电时间保护阈值
	0, // 停电时间保护使能
	0, // 均流SOC补偿幅值
	0, // 均流SOC补偿斜率
	0, // 均流SOC补偿使能
	0, // 休眠指示灯
	0, // 电池地址获取方式
	1, // 电池切换地址
	0, // 充电轮换使能
	3.3, // 单体补充电电压
	42, // 末期放电电压1
	42, // 末期放电电压2
	0, // 单体温度保护屏蔽
	0, // 充放电机测试模式
	0, // 故障前录波时间间隔
	0, // 故障后录波时间间隔
	1, // 故障前录波条数
	1, // 故障后录波条数
	15, // 测点数量X
	0, // 可变测点ID
	0.3, // 放电过流保护阈值,  不允许设置，参数范围不具备参考性
	2, // 直流内阻异常告警阈值
	2, // 直流内阻异常保护阈值
	0.1, // 单体温升速率异常阈值
	0.1, // 自放电容量比率
	0, // 自研协议偏移地址
	0.1, // 容量衰减一致性差告警比率阈值
	60, // 电池异常温度高保护阈值
	0, // 充电MAP使能
	0, // 严重告警闪烁方式
	0, // 次级告警闪烁方式
	0, // 电芯类型
	-40, // 放电加热膜启动温度
	-40, // 放电加热膜关闭温度
	0, // NTC无效屏蔽路数
	0, // 放电末期切换SOC2,  该参数暂不使用，值随放电模式联动，恒压放电模式为0%，混用为10%
}; 

/// 参数ID数组
const WORD s_awParaID[] =
{
	PARA_ID_CHG_CURR_HIGH_ALM, // 充电过流告警阈值
	PARA_ID_CHG_CURR_HIGH_PRT, // 充电过流保护阈值
	PARA_ID_DISCHG_CURR_HIGH_ALM, // 放电过流告警阈值
	PARA_ID_BATT_OVER_VOLT_ALM, // 电池组过压告警阈值
	PARA_ID_BATT_OVER_VOLT_PRT, // 电池组过压保护阈值
	PARA_ID_BOARD_TEMP_HIGH_PRT, // 单板过温保护阈值,  参数范围不具备参考
	PARA_ID_BOARD_TEMP_HIGH_ALM, // 单板过温告警阈值,  参数范围不具备参考
	PARA_ID_ENV_TEMP_HIGH_ALM, // 环境温度高告警阈值
	PARA_ID_ENV_TEMP_LOW_ALM, // 环境温度低告警阈值
	PARA_ID_BATT_UNDER_VOLT_ALM, // 电池组欠压告警阈值
	PARA_ID_BATT_UNDER_VOLT_PRT, // 电池组欠压保护阈值
	PARA_ID_CELL_OVER_VOLT_ALM, // 单体过压告警阈值
	PARA_ID_CELL_OVER_VOLT_PRT, // 单体过压保护阈值
	PARA_ID_CELL_UNDER_VOLT_ALM, // 单体欠压告警阈值
	PARA_ID_CELL_UNDER_VOLT_PRT, // 单体欠压保护阈值
	PARA_ID_CHG_TEMP_HIGH_ALM, // 充电高温告警阈值
	PARA_ID_CHG_TEMP_HIGH_PRT, // 充电高温保护阈值
	PARA_ID_DISCHG_TEMP_HIGH_ALM, // 放电高温告警阈值
	PARA_ID_DISCHG_TEMP_HIGH_PRT, // 放电高温保护阈值
	PARA_ID_CHG_TEMP_LOW_ALM, // 充电低温告警阈值
	PARA_ID_CHG_TEMP_LOW_PRT, // 充电低温保护阈值
	PARA_ID_DISCHG_TEMP_LOW_ALM, // 放电低温告警阈值
	PARA_ID_DISCHG_TEMP_LOW_PRT, // 放电低温保护阈值
	PARA_ID_CELL_POOR_CONSIS_ALM, // 单体一致性差告警阈值
	PARA_ID_CELL_POOR_CONSIS_PRT, // 单体一致性差保护阈值
	PARA_ID_BATT_SOC_LOW_ALM, // 电池SOC低告警阈值
	PARA_ID_BATT_SOC_LOW_PRT, // 电池SOC低保护阈值
	PARA_ID_BATT_SOH_ALM, // 电池SOH低告警阈值
	PARA_ID_BATT_SOH_PRT, // 电池SOH低保护阈值
	PARA_ID_CELL_DAMAGE_PRT, // 单体损坏保护阈值
	PARA_ID_BATT_CHG_FULL_AVER_CURR, // 电池充满电流
	PARA_ID_CHG_MAX_DURA, // 充电最长时间
	PARA_ID_CHG_END_DURA, // 充电末期维持时间
	PARA_ID_BATT_SUPPL_VOLT, // 电池补充电电压
	PARA_ID_CELL_EQU_VOLT_DIFF, // 单体均衡启动压差阈值
	PARA_ID_HIS_DATA_INTER, // 历史数据保存间隔
	PARA_ID_BUZZER_ENABLE, // 蜂鸣器使能
	PARA_ID_BATT_CAP, // 电池容量
	PARA_ID_SOFT_ANTI_THEFT_DELAY, // 软件防盗延时
	PARA_ID_DEVICE_NAME, // 设备名称
	PARA_ID_DOC_PER_DISCHG, // 单次放电DOD
	PARA_ID_CELL_UVP_DELAY, // 单体动态欠压保护
	PARA_ID_RELAY, // 告警干接点
	PARA_ID_ALARM_CLASS, // 告警级别
	PARA_ID_ENABLE_DATE, // 启用日期
	PARA_ID_UVP_TEMP_COMPENSATION_EN, // 整组欠压保护温度补偿
	PARA_ID_BMS_SYSTEM_NAME, // BMS系统名称
	PARA_ID_RELAY_DEFAULT_STATUS, // 干接点默认状态
	PARA_ID_ENV_TEMP_HIGH_PRT, // 环境温度高保护阈值
	PARA_ID_ENV_TEMP_LOW_PRT, // 环境温度低保护阈值
	PARA_ID_BATT_OVER_VOLT_ALM_RECO, // 电池组过压告警恢复阈值
	PARA_ID_BATT_OVER_VOLT_PRT_RECO, // 电池组过压保护恢复阈值
	PARA_ID_CELL_OVER_VOLT_ALM_RECO, // 单体过压告警恢复阈值
	PARA_ID_CELL_OVER_VOLT_PRT_RECO, // 单体过压保护恢复阈值
	PARA_ID_CELL_UNDER_VOLT_ALM_RECO, // 单体欠压告警恢复阈值
	PARA_ID_CELL_UNDER_VOLT_PRT_RECO, // 单体欠压保护恢复阈值
	PARA_ID_CHG_TEMP_HIGH_ALM_RECO, // 充电高温告警恢复阈值
	PARA_ID_CHG_TEMP_HIGH_PRT_RECO, // 充电高温保护恢复阈值
	PARA_ID_DISCHG_TEMP_HIGH_ALM_RECO, // 放电高温告警恢复阈值
	PARA_ID_DISCHG_TEMP_HIGH_PRT_RECO, // 放电高温保护恢复阈值
	PARA_ID_CHG_TEMP_LOW_ALM_RECO, // 充电低温告警恢复阈值
	PARA_ID_CHG_TEMP_LOW_PRT_RECO, // 充电低温保护恢复阈值
	PARA_ID_DISCHG_TEMP_LOW_ALM_RECO, // 放电低温告警恢复阈值
	PARA_ID_DISCHG_TEMP_LOW_PRT_RECO, // 放电低温保护恢复阈值
	PARA_ID_ENV_TEMP_HIGH_ALM_RECO, // 环境温度高告警恢复阈值
	PARA_ID_ENV_TEMP_HIGH_PRT_RECO, // 环境温度高保护恢复阈值
	PARA_ID_ENV_TEMP_LOW_ALM_RECO, // 环境温度低告警恢复阈值
	PARA_ID_ENV_TEMP_LOW_PRT_RECO, // 环境温度低保护恢复阈值
	PARA_ID_BOARD_TEMP_HIGH_ALM_RECO, // 单板过温告警恢复阈值,  参数范围不具备参考
	PARA_ID_BOARD_TEMP_HIGH_PRT_RECO, // 单板过温保护恢复阈值,  参数范围不具备参考
	PARA_ID_HEATING_PAD_ENABLE, // 加热垫使能
	PARA_ID_GYRO_ANGLE, // 陀螺仪倾角
	PARA_ID_BATT_SOC_LOW_ALM_RECO, // 电池SOC低告警恢复阈值,  参数范围不具备参考
	PARA_ID_GYRO_ANTI_THEFT_MODE, // 陀螺仪防盗方式
	PARA_ID_BATT_UNLOCK_MODE, // 电池解锁方式
	PARA_ID_DISCHG_CURR_HIGH_ALM_RECO, // 放电过流告警恢复阈值,  参数范围不具备参考
	PARA_ID_BATT_UNDER_VOLT_ALM_RECO, // 电池组欠压告警恢复阈值,  不允许设置，参数范围不具备参考性
	PARA_ID_BATT_UNDER_VOLT_PRT_RECO, // 电池组欠压保护恢复阈值,  不允许设置，参数范围不具备参考性
	PARA_ID_CELL_POOR_CONSIS_PRT_RECO, // 单体一致性差保护恢复阈值,  不允许设置，参数范围不具备参考性
	PARA_ID_HIS_DATA_INTER_BK, // 数据保存时间,  参数范围不具备参考性
	PARA_ID_CELL_CHARGE_FULL_VOLT, // 单体充电截止电压
	PARA_ID_BATT_CHG_FULL_AVER_VOLT, // 电池充满电压
	PARA_ID_CHRG_MAX_CURR, // 充电最大电流
	PARA_ID_SWITCH_SOC, // 放电切换SOC
	PARA_ID_USAGE_SCENARIO, // 放电模式
	PARA_ID_REMOTE_SUPPLY_OUT_VOLT, // 恒压放电输出电压
	PARA_ID_DISCHRG_MAX_CURR, // 放电最大电流
	PARA_ID_CHG_HEATER_STARTUP_TEMP, // 充电加热膜启动温度
	PARA_ID_CHG_HEATER_SHUTDOWN_TEMP, // 充电加热膜关闭温度
	PARA_ID_HEATER_TEMP_HIGH_THRE, // 加热膜过温阈值
	PARA_ID_HEATER_TEMP_HIGH_RELEASE, // 加热膜过温解除
	PARA_ID_RECHARGE_SOC, // 补充电SOC
	PARA_ID_INTELLIGENT_INTER_CHARGE_EN, // 智能间歇充电使能
	PARA_ID_BATT_CHARGE_CURR_LIMIT_EN, // 电池组充电限流使能
	PARA_ID_POWER_OFF_TIME_PRT_THRE, // 停电时间保护阈值
	PARA_ID_POWER_OFF_TIME_PRT_EN, // 停电时间保护使能
	PARA_ID_CURR_BALANCE_AMPLITUDE, // 均流SOC补偿幅值
	PARA_ID_CURR_BALANCE_SLOPE, // 均流SOC补偿斜率
	PARA_ID_CURR_BALANCE_SOC_EN, // 均流SOC补偿使能
	PARA_ID_SLEEP_INDICATOR, // 休眠指示灯
	PARA_ID_BATT_ADDRESS_MODE, // 电池地址获取方式
	PARA_ID_BATT_SWITCH_ADDRESS, // 电池切换地址
	PARA_ID_CHARGE_ROTATE_ENABLE, // 充电轮换使能
	PARA_ID_CELL_SUPPL_VOLT, // 单体补充电电压
	PARA_ID_DISCHARGE_REMO_END_OUTPUT_VOLT_FIRST, // 末期放电电压1
	PARA_ID_DISCHARGE_REMO_END_OUTPUT_VOLT_SECOND, // 末期放电电压2
	PARA_ID_CELL_TEMP_PRT_SHIELD, // 单体温度保护屏蔽
	PARA_ID_CHARGE_DISCHARGE_MACHINE_TEST_MODE, // 充放电机测试模式
	PARA_ID_PREFAULT_RECORDING_TIME_INTERVAL, // 故障前录波时间间隔
	PARA_ID_POSTFAULT_RECORDING_TIME_INTERVAL, // 故障后录波时间间隔
	PARA_ID_PREFAULT_RECORDING_NUMBER, // 故障前录波条数
	PARA_ID_POSTFAULT_RECORDING_NUMBER, // 故障后录波条数
	PARA_ID_MEASUREMENT_POINTS_NUMBER, // 测点数量X
	PARA_ID_VARIABLE_MEASUREMENT_POINT_ID, // 可变测点ID
	PARA_ID_DISCHG_CURR_HIGH_PRT, // 放电过流保护阈值,  不允许设置，参数范围不具备参考性
	PARA_ID_DCR_FAULT_ALM_THRE, // 直流内阻异常告警阈值
	PARA_ID_DCR_FAULT_PRT_THRE, // 直流内阻异常保护阈值
	PARA_ID_CELL_TEMP_RISE_ABNORMAL, // 单体温升速率异常阈值
	PARA_ID_SELFDISCHG_ACR, // 自放电容量比率
	PARA_ID_SELFDEVELOP_PROTOCOL_OFFSET_ADDR, // 自研协议偏移地址
	PARA_ID_CAP_DCDR, // 容量衰减一致性差告警比率阈值
	PARA_ID_BATT_FAULT_TEMP_HIGH_PRT, // 电池异常温度高保护阈值
	PARA_ID_CHARGE_MAP_ENABLE, // 充电MAP使能
	PARA_ID_EMERGENCY_FLASH_MODE, // 严重告警闪烁方式
	PARA_ID_SECONDARY_FLASH_MODE, // 次级告警闪烁方式
	PARA_ID_CELL_TYPE, // 电芯类型
	PARA_ID_DISCHG_HEATER_STARTUP_TEMP, // 放电加热膜启动温度
	PARA_ID_DISCHG_HEATER_SHUTDOWN_TEMP, // 放电加热膜关闭温度
	PARA_ID_NTC_INVALID_SHIELD_NUM, // NTC无效屏蔽路数
	PARA_ID_SWITCH_SOC2, // 放电末期切换SOC2,  该参数暂不使用，值随放电模式联动，恒压放电模式为0%，混用为10%
};

/// 参数默认值
T_SysPara   s_tDefaultPara	= 
{
	0.56, // 充电过流告警阈值
	0.6, // 充电过流保护阈值
	1.36, // 放电过流告警阈值
	3.6, // 电池组过压告警阈值
	3.65, // 电池组过压保护阈值
	127, // 单板过温保护阈值,  参数范围不具备参考
	85, // 单板过温告警阈值,  参数范围不具备参考
	80, // 环境温度高告警阈值
	-20, // 环境温度低告警阈值
	3.0625, // 电池组欠压告警阈值
	2.875, // 电池组欠压保护阈值
	3.75, // 单体过压告警阈值
	3.8, // 单体过压保护阈值
	3, // 单体欠压告警阈值
	2.5, // 单体欠压保护阈值
	55, // 充电高温告警阈值
	60, // 充电高温保护阈值
	60, // 放电高温告警阈值
	65, // 放电高温保护阈值
	5, // 充电低温告警阈值
	0, // 充电低温保护阈值
	-15, // 放电低温告警阈值
	-20, // 放电低温保护阈值
	0.3, // 单体一致性差告警阈值
	0.6, // 单体一致性差保护阈值
	20, // 电池SOC低告警阈值
	15, // 电池SOC低保护阈值
	50, // 电池SOH低告警阈值
	30, // 电池SOH低保护阈值
	1.5, // 单体损坏保护阈值
	0.1, // 电池充满电流
	900, // 充电最长时间
	1, // 充电末期维持时间
	3.35, // 电池补充电电压
	0.02, // 单体均衡启动压差阈值
	480, // 历史数据保存间隔
	1, // 蜂鸣器使能
	50, // 电池容量
	10, // 软件防盗延时
	{'Z', 'T', 'E', ' ', 'B', 'M', 'S', } , // 设备名称
	70, // 单次放电DOD
	1, // 单体动态欠压保护
	/// 告警干接点默认值
	{
		0, // 无,  电池组欠压告警
		0, // 无,  电池组过压告警
		0, // 无,  电池组欠压保护
		0, // 无,  电池组过压保护
		0, // 无,  充电过流告警
		0, // 无,  充电过流保护
		0, // 无,  放电过流告警
		0, // 无,  放电过流保护
		0, // 无,  电池短路
		1, // A1,  电池反接
		1, // A1,  放电回路失效
		1, // A1,  充电回路失效
		1, // A1,  限流回路失效
		0, // 无,  电池SOC低告警
		0, // 无,  电池SOC低保护
		0, // 无,  电池SOH低告警
		1, // A1,  电池SOH低保护
		0, // 无,  单板过温告警
		1, // A1,  单板过温保护
		0, // 无,  单体欠压告警
		0, // 无,  单体过压告警
		0, // 无,  单体欠压保护
		0, // 无,  单体过压保护
		0, // 无,  单体一致性差告警
		1, // A1,  单体一致性差保护
		1, // A1,  单体电压采样异常
		1, // A1,  单体损坏保护
		0, // 无,  单体充电高温告警
		0, // 无,  单体充电低温告警
		0, // 无,  单体充电高温保护
		0, // 无,  单体充电低温保护
		0, // 无,  单体放电高温告警
		0, // 无,  单体放电低温告警
		0, // 无,  单体放电高温保护
		0, // 无,  单体放电低温保护
		1, // A1,  单体温度传感器无效告警
		0, // 无,  环境温度高告警
		0, // 无,  环境温度低告警
		0, // 无,  环境温度高保护
		0, // 无,  环境温度低保护
		1, // A1,  环境温度传感器无效告警
		0, // 无,  BDU电池欠压保护
		1, // A1,  BDU EEPROM故障
		0, // 无,  BDU母排欠压保护
		0, // 无,  BDU母排过压保护
		1, // A1,  BDU通信断
		1, // A1,  BDU电池充电欠压保护
		1, // A1,  BDU电池闭锁告警
		1, // A1,  均衡电路故障告警
		0, // 无,  电池丢失告警
		0, // 无,  机内过温保护
		1, // A1,  回路异常
		0, // 无,  停电时间保护告警
		1, // A1,  加热膜失效
		0, // 无,  熔丝损坏
		1, // A1,  连接器温度高保护
		1, // A1,  主继电器失效
		1, // A1,  DCDC故障
		1, // A1,  采集异常
		1, // A1,  辅助源故障
		0, // 无,  单体温度异常
		0, // 无,  地址冲突
		0, // 无,  单体动态欠压保护
		1, // A1,  加热膜连接器高温
		1, // A1,  单体温升速率异常
		1, // A1,  直流内阻异常告警
		1, // A1,  直流内阻异常保护
		1, // A1,  自放电异常
		0, // 无,  电源通信断
		0, // 无,  容量衰减一致性差告警
		0, // 无,  电池异常高温保护告警
	},

	/// 告警级别默认值
	{
		0, // 屏蔽,  电池组欠压告警
		0, // 屏蔽,  电池组过压告警
		1, // 严重,  电池组欠压保护
		1, // 严重,  电池组过压保护
		1, // 严重,  充电过流告警
		1, // 严重,  充电过流保护
		1, // 严重,  放电过流告警
		1, // 严重,  放电过流保护
		1, // 严重,  电池短路
		1, // 严重,  电池反接
		1, // 严重,  放电回路失效
		1, // 严重,  充电回路失效
		1, // 严重,  限流回路失效
		0, // 屏蔽,  电池SOC低告警
		0, // 屏蔽,  电池SOC低保护
		1, // 严重,  电池SOH低告警
		1, // 严重,  电池SOH低保护
		0, // 屏蔽,  单板过温告警
		1, // 严重,  单板过温保护
		0, // 屏蔽,  单体欠压告警
		0, // 屏蔽,  单体过压告警
		1, // 严重,  单体欠压保护
		1, // 严重,  单体过压保护
		0, // 屏蔽,  单体一致性差告警
		1, // 严重,  单体一致性差保护
		1, // 严重,  单体电压采样异常
		1, // 严重,  单体损坏保护
		0, // 屏蔽,  单体充电高温告警
		0, // 屏蔽,  单体充电低温告警
		1, // 严重,  单体充电高温保护
		1, // 严重,  单体充电低温保护
		1, // 严重,  单体放电高温告警
		1, // 严重,  单体放电低温告警
		1, // 严重,  单体放电高温保护
		1, // 严重,  单体放电低温保护
		1, // 严重,  单体温度传感器无效告警
		0, // 屏蔽,  环境温度高告警
		0, // 屏蔽,  环境温度低告警
		0, // 屏蔽,  环境温度高保护
		1, // 严重,  环境温度低保护
		1, // 严重,  环境温度传感器无效告警
		1, // 严重,  BDU电池欠压保护
		1, // 严重,  BDU EEPROM故障
		1, // 严重,  BDU母排欠压保护
		1, // 严重,  BDU母排过压保护
		1, // 严重,  BDU通信断
		1, // 严重,  BDU电池充电欠压保护
		1, // 严重,  BDU电池闭锁告警
		1, // 严重,  均衡电路故障告警
		1, // 严重,  电池丢失告警
		1, // 严重,  机内过温保护
		1, // 严重,  回路异常
		0, // 屏蔽,  停电时间保护告警
		1, // 严重,  加热膜失效
		1, // 严重,  熔丝损坏
		1, // 严重,  连接器温度高保护
		1, // 严重,  主继电器失效
		1, // 严重,  DCDC故障
		1, // 严重,  采集异常
		1, // 严重,  辅助源故障
		1, // 严重,  单体温度异常
		0, // 屏蔽,  地址冲突
		1, // 严重,  单体动态欠压保护
		1, // 严重,  加热膜连接器高温
		1, // 严重,  单体温升速率异常
		1, // 严重,  直流内阻异常告警
		1, // 严重,  直流内阻异常保护
		0, // 屏蔽,  自放电异常
		0, // 屏蔽,  电源通信断
		0, // 屏蔽,  容量衰减一致性差告警
		1, // 严重,  电池异常高温保护告警
	}, 

	{2010,1,1}, // 启用日期
	1, // 整组欠压保护温度补偿
	{'Z', 'X', 'E', 'S', 'M', ' ', 'D', '1', '2', '1', } , // BMS系统名称
	1, // 干接点默认状态
	85, // 环境温度高保护阈值
	-25, // 环境温度低保护阈值
	3.35, // 电池组过压告警恢复阈值
	3.51875, // 电池组过压保护恢复阈值
	3.5, // 单体过压告警恢复阈值
	3.5, // 单体过压保护恢复阈值
	3.2, // 单体欠压告警恢复阈值
	3.4, // 单体欠压保护恢复阈值
	52, // 充电高温告警恢复阈值
	57, // 充电高温保护恢复阈值
	57, // 放电高温告警恢复阈值
	62, // 放电高温保护恢复阈值
	8, // 充电低温告警恢复阈值
	3, // 充电低温保护恢复阈值
	-12, // 放电低温告警恢复阈值
	-17, // 放电低温保护恢复阈值
	75, // 环境温度高告警恢复阈值
	80, // 环境温度高保护恢复阈值
	-15, // 环境温度低告警恢复阈值
	-20, // 环境温度低保护恢复阈值
	68, // 单板过温告警恢复阈值,  参数范围不具备参考
	110, // 单板过温保护恢复阈值,  参数范围不具备参考
	1, // 加热垫使能
	0, // 陀螺仪倾角
	22, // 电池SOC低告警恢复阈值,  参数范围不具备参考
	0, // 陀螺仪防盗方式
	0, // 电池解锁方式
	1.26, // 放电过流告警恢复阈值,  参数范围不具备参考
	3.1, // 电池组欠压告警恢复阈值,  不允许设置，参数范围不具备参考性
	3.1, // 电池组欠压保护恢复阈值,  不允许设置，参数范围不具备参考性
	0.5, // 单体一致性差保护恢复阈值,  不允许设置，参数范围不具备参考性
	3600, // 数据保存时间,  参数范围不具备参考性
	3.65, // 单体充电截止电压
	3.5, // 电池充满电压
	0.2, // 充电最大电流
	0, // 放电切换SOC
	0, // 放电模式
	55.5, // 恒压放电输出电压
	1.32, // 放电最大电流
	0, // 充电加热膜启动温度
	16, // 充电加热膜关闭温度
	45, // 加热膜过温阈值
	30, // 加热膜过温解除
	95, // 补充电SOC
	1, // 智能间歇充电使能
	1, // 电池组充电限流使能
	600, // 停电时间保护阈值
	0, // 停电时间保护使能
	5, // 均流SOC补偿幅值
	2, // 均流SOC补偿斜率
	1, // 均流SOC补偿使能
	1, // 休眠指示灯
	0, // 电池地址获取方式
	1, // 电池切换地址
	0, // 充电轮换使能
	3.34, // 单体补充电电压
	45.8, // 末期放电电压1
	43.8, // 末期放电电压2
	0, // 单体温度保护屏蔽
	0, // 充放电机测试模式
	120, // 故障前录波时间间隔
	200, // 故障后录波时间间隔
	5, // 故障前录波条数
	5, // 故障后录波条数
	18, // 测点数量X
	/// 可变测点ID默认值
	{
		1,
		2,
		3,
		4,
		5,
		6,
		7,
		8,
		9,
		10,
		11,
		12,
		13,
		14,
		15,
		16,
		17,
		18,
		0,
		0,
		0,
		0,
		0,
	},

	1.15, // 放电过流保护阈值,  不允许设置，参数范围不具备参考性
	10, // 直流内阻异常告警阈值
	16, // 直流内阻异常保护阈值
	1, // 单体温升速率异常阈值
	2, // 自放电容量比率
	0, // 自研协议偏移地址
	8, // 容量衰减一致性差告警比率阈值
	70, // 电池异常温度高保护阈值
	1, // 充电MAP使能
	2, // 严重告警闪烁方式
	2, // 次级告警闪烁方式
	0, // 电芯类型
	0, // 放电加热膜启动温度
	16, // 放电加热膜关闭温度
	2, // NTC无效屏蔽路数
	0, // 放电末期切换SOC2,  该参数暂不使用，值随放电模式联动，恒压放电模式为0%，混用为10%
}; 

/// 参数占用字节数数组
const BYTE g_aucParaSpace[] =
{
	4, // 充电过流告警阈值
	4, // 充电过流保护阈值
	4, // 放电过流告警阈值
	4, // 电池组过压告警阈值
	4, // 电池组过压保护阈值
	4, // 单板过温保护阈值,  参数范围不具备参考
	4, // 单板过温告警阈值,  参数范围不具备参考
	4, // 环境温度高告警阈值
	4, // 环境温度低告警阈值
	4, // 电池组欠压告警阈值
	4, // 电池组欠压保护阈值
	4, // 单体过压告警阈值
	4, // 单体过压保护阈值
	4, // 单体欠压告警阈值
	4, // 单体欠压保护阈值
	4, // 充电高温告警阈值
	4, // 充电高温保护阈值
	4, // 放电高温告警阈值
	4, // 放电高温保护阈值
	4, // 充电低温告警阈值
	4, // 充电低温保护阈值
	4, // 放电低温告警阈值
	4, // 放电低温保护阈值
	4, // 单体一致性差告警阈值
	4, // 单体一致性差保护阈值
	2, // 电池SOC低告警阈值
	2, // 电池SOC低保护阈值
	2, // 电池SOH低告警阈值
	2, // 电池SOH低保护阈值
	4, // 单体损坏保护阈值
	4, // 电池充满电流
	2, // 充电最长时间
	2, // 充电末期维持时间
	4, // 电池补充电电压
	4, // 单体均衡启动压差阈值
	2, // 历史数据保存间隔
	1, // 蜂鸣器使能
	2, // 电池容量
	2, // 软件防盗延时
	LEN_TYPE_STRING_32, // 设备名称
	1, // 单次放电DOD
	1, // 单体动态欠压保护
	ALARM_CLASS, // 告警干接点
	ALARM_CLASS, // 告警级别
	4, // 启用日期
	1, // 整组欠压保护温度补偿
	LEN_TYPE_STRING_20, // BMS系统名称
	1, // 干接点默认状态
	4, // 环境温度高保护阈值
	4, // 环境温度低保护阈值
	4, // 电池组过压告警恢复阈值
	4, // 电池组过压保护恢复阈值
	4, // 单体过压告警恢复阈值
	4, // 单体过压保护恢复阈值
	4, // 单体欠压告警恢复阈值
	4, // 单体欠压保护恢复阈值
	4, // 充电高温告警恢复阈值
	4, // 充电高温保护恢复阈值
	4, // 放电高温告警恢复阈值
	4, // 放电高温保护恢复阈值
	4, // 充电低温告警恢复阈值
	4, // 充电低温保护恢复阈值
	4, // 放电低温告警恢复阈值
	4, // 放电低温保护恢复阈值
	4, // 环境温度高告警恢复阈值
	4, // 环境温度高保护恢复阈值
	4, // 环境温度低告警恢复阈值
	4, // 环境温度低保护恢复阈值
	4, // 单板过温告警恢复阈值,  参数范围不具备参考
	4, // 单板过温保护恢复阈值,  参数范围不具备参考
	1, // 加热垫使能
	1, // 陀螺仪倾角
	2, // 电池SOC低告警恢复阈值,  参数范围不具备参考
	1, // 陀螺仪防盗方式
	1, // 电池解锁方式
	4, // 放电过流告警恢复阈值,  参数范围不具备参考
	4, // 电池组欠压告警恢复阈值,  不允许设置，参数范围不具备参考性
	4, // 电池组欠压保护恢复阈值,  不允许设置，参数范围不具备参考性
	4, // 单体一致性差保护恢复阈值,  不允许设置，参数范围不具备参考性
	2, // 数据保存时间,  参数范围不具备参考性
	4, // 单体充电截止电压
	4, // 电池充满电压
	4, // 充电最大电流
	2, // 放电切换SOC
	1, // 放电模式
	4, // 恒压放电输出电压
	4, // 放电最大电流
	4, // 充电加热膜启动温度
	4, // 充电加热膜关闭温度
	4, // 加热膜过温阈值
	4, // 加热膜过温解除
	2, // 补充电SOC
	1, // 智能间歇充电使能
	1, // 电池组充电限流使能
	4, // 停电时间保护阈值
	1, // 停电时间保护使能
	1, // 均流SOC补偿幅值
	1, // 均流SOC补偿斜率
	1, // 均流SOC补偿使能
	1, // 休眠指示灯
	1, // 电池地址获取方式
	1, // 电池切换地址
	1, // 充电轮换使能
	4, // 单体补充电电压
	4, // 末期放电电压1
	4, // 末期放电电压2
	1, // 单体温度保护屏蔽
	1, // 充放电机测试模式
	2, // 故障前录波时间间隔
	2, // 故障后录波时间间隔
	1, // 故障前录波条数
	1, // 故障后录波条数
	1, // 测点数量X
	NUM_OF_MEASUREPOINTS, // 可变测点ID
	4, // 放电过流保护阈值,  不允许设置，参数范围不具备参考性
	1, // 直流内阻异常告警阈值
	1, // 直流内阻异常保护阈值
	4, // 单体温升速率异常阈值
	4, // 自放电容量比率
	1, // 自研协议偏移地址
	4, // 容量衰减一致性差告警比率阈值
	4, // 电池异常温度高保护阈值
	1, // 充电MAP使能
	1, // 严重告警闪烁方式
	1, // 次级告警闪烁方式
	1, // 电芯类型
	4, // 放电加热膜启动温度
	4, // 放电加热膜关闭温度
	1, // NTC无效屏蔽路数
	2, // 放电末期切换SOC2,  该参数暂不使用，值随放电模式联动，恒压放电模式为0%，混用为10%
}; 

/// 参数单位数组
/// 参数单位数组未用到，且有些字符会引起编译错误，因此屏蔽掉
/*
const CHAR	g_acParaUints[PARA_NUM][10] =
{
	"C5", // 充电过流告警阈值
	"C5", // 充电过流保护阈值
	"C5", // 放电过流告警阈值
	"V", // 电池组过压告警阈值
	"V", // 电池组过压保护阈值
	"℃", // 单板过温保护阈值,  参数范围不具备参考
	"℃", // 单板过温告警阈值,  参数范围不具备参考
	"℃", // 环境温度高告警阈值
	"℃", // 环境温度低告警阈值
	"V", // 电池组欠压告警阈值
	"V", // 电池组欠压保护阈值
	"V", // 单体过压告警阈值
	"V", // 单体过压保护阈值
	"V", // 单体欠压告警阈值
	"V", // 单体欠压保护阈值
	"℃", // 充电高温告警阈值
	"℃", // 充电高温保护阈值
	"℃", // 放电高温告警阈值
	"℃", // 放电高温保护阈值
	"℃", // 充电低温告警阈值
	"℃", // 充电低温保护阈值
	"℃", // 放电低温告警阈值
	"℃", // 放电低温保护阈值
	"V", // 单体一致性差告警阈值
	"V", // 单体一致性差保护阈值
	"%", // 电池SOC低告警阈值
	"%", // 电池SOC低保护阈值
	"%", // 电池SOH低告警阈值
	"%", // 电池SOH低保护阈值
	"V", // 单体损坏保护阈值
	"C5", // 电池充满电流
	"min", // 充电最长时间
	"min", // 充电末期维持时间
	"V", // 电池补充电电压
	"V", // 单体均衡启动压差阈值
	"Min", // 历史数据保存间隔
	{0}, // 蜂鸣器使能
	"AH", // 电池容量
	"Min", // 软件防盗延时
	{0}, // 设备名称
	"%", // 单次放电DOD
	{0}, // 单体动态欠压保护
	{0}, // 告警干接点
	{0}, // 告警级别
	{0}, // 启用日期
	{0}, // 整组欠压保护温度补偿
	{0}, // BMS系统名称
	{0}, // 干接点默认状态
	"℃", // 环境温度高保护阈值
	"℃", // 环境温度低保护阈值
	"V", // 电池组过压告警恢复阈值
	"V", // 电池组过压保护恢复阈值
	"V", // 单体过压告警恢复阈值
	"V", // 单体过压保护恢复阈值
	"V", // 单体欠压告警恢复阈值
	"V", // 单体欠压保护恢复阈值
	"℃", // 充电高温告警恢复阈值
	"℃", // 充电高温保护恢复阈值
	"℃", // 放电高温告警恢复阈值
	"℃", // 放电高温保护恢复阈值
	"℃", // 充电低温告警恢复阈值
	"℃", // 充电低温保护恢复阈值
	"℃", // 放电低温告警恢复阈值
	"℃", // 放电低温保护恢复阈值
	"℃", // 环境温度高告警恢复阈值
	"℃", // 环境温度高保护恢复阈值
	"℃", // 环境温度低告警恢复阈值
	"℃", // 环境温度低保护恢复阈值
	"℃", // 单板过温告警恢复阈值,  参数范围不具备参考
	"℃", // 单板过温保护恢复阈值,  参数范围不具备参考
	{0}, // 加热垫使能
	"°", // 陀螺仪倾角
	"%", // 电池SOC低告警恢复阈值,  参数范围不具备参考
	{0}, // 陀螺仪防盗方式
	{0}, // 电池解锁方式
	"C5", // 放电过流告警恢复阈值,  参数范围不具备参考
	"V", // 电池组欠压告警恢复阈值,  不允许设置，参数范围不具备参考性
	"V", // 电池组欠压保护恢复阈值,  不允许设置，参数范围不具备参考性
	"V", // 单体一致性差保护恢复阈值,  不允许设置，参数范围不具备参考性
	"S", // 数据保存时间,  参数范围不具备参考性
	"V", // 单体充电截止电压
	"V", // 电池充满电压
	"C3", // 充电最大电流
	{0}, // 放电切换SOC
	{0}, // 放电模式
	"V", // 恒压放电输出电压
	"C3", // 放电最大电流
	"℃", // 充电加热膜启动温度
	"℃", // 充电加热膜关闭温度
	"℃", // 加热膜过温阈值
	"℃", // 加热膜过温解除
	{0}, // 补充电SOC
	{0}, // 智能间歇充电使能
	{0}, // 电池组充电限流使能
	"Min", // 停电时间保护阈值
	{0}, // 停电时间保护使能
	{0}, // 均流SOC补偿幅值
	{0}, // 均流SOC补偿斜率
	{0}, // 均流SOC补偿使能
	{0}, // 休眠指示灯
	{0}, // 电池地址获取方式
	{0}, // 电池切换地址
	{0}, // 充电轮换使能
	"V", // 单体补充电电压
	"V", // 末期放电电压1
	"V", // 末期放电电压2
	{0}, // 单体温度保护屏蔽
	{0}, // 充放电机测试模式
	"us", // 故障前录波时间间隔
	"us", // 故障后录波时间间隔
	{0}, // 故障前录波条数
	{0}, // 故障后录波条数
	{0}, // 测点数量X
	{0}, // 可变测点ID
	"C5", // 放电过流保护阈值,  不允许设置，参数范围不具备参考性
	{0}, // 直流内阻异常告警阈值
	{0}, // 直流内阻异常保护阈值
	"℃", // 单体温升速率异常阈值
	"%", // 自放电容量比率
	{0}, // 自研协议偏移地址
	"%", // 容量衰减一致性差告警比率阈值
	"℃", // 电池异常温度高保护阈值
	{0}, // 充电MAP使能
	{0}, // 严重告警闪烁方式
	{0}, // 次级告警闪烁方式
	{0}, // 电芯类型
	"℃", // 放电加热膜启动温度
	"℃", // 放电加热膜关闭温度
	{0}, // NTC无效屏蔽路数
	{0}, // 放电末期切换SOC2,  该参数暂不使用，值随放电模式联动，恒压放电模式为0%，混用为10%
}; 
*/

/// 参数精度数组
const BYTE g_aucParaWei[] =
{
	3, // 充电过流告警阈值
	3, // 充电过流保护阈值
	3, // 放电过流告警阈值
	1, // 电池组过压告警阈值
	1, // 电池组过压保护阈值
	0, // 单板过温保护阈值,  参数范围不具备参考
	0, // 单板过温告警阈值,  参数范围不具备参考
	0, // 环境温度高告警阈值
	0, // 环境温度低告警阈值
	1, // 电池组欠压告警阈值
	1, // 电池组欠压保护阈值
	2, // 单体过压告警阈值
	2, // 单体过压保护阈值
	2, // 单体欠压告警阈值
	2, // 单体欠压保护阈值
	0, // 充电高温告警阈值
	0, // 充电高温保护阈值
	0, // 放电高温告警阈值
	0, // 放电高温保护阈值
	0, // 充电低温告警阈值
	0, // 充电低温保护阈值
	0, // 放电低温告警阈值
	0, // 放电低温保护阈值
	2, // 单体一致性差告警阈值
	2, // 单体一致性差保护阈值
	0, // 电池SOC低告警阈值
	0, // 电池SOC低保护阈值
	0, // 电池SOH低告警阈值
	0, // 电池SOH低保护阈值
	2, // 单体损坏保护阈值
	2, // 电池充满电流
	0, // 充电最长时间
	0, // 充电末期维持时间
	2, // 电池补充电电压
	2, // 单体均衡启动压差阈值
	0, // 历史数据保存间隔
	0, // 蜂鸣器使能
	0, // 电池容量
	0, // 软件防盗延时
	0, // 设备名称
	0, // 单次放电DOD
	0, // 单体动态欠压保护
	0, // 告警干接点
	0, // 告警级别
	0, // 启用日期
	0, // 整组欠压保护温度补偿
	0, // BMS系统名称
	0, // 干接点默认状态
	1, // 环境温度高保护阈值
	1, // 环境温度低保护阈值
	1, // 电池组过压告警恢复阈值
	1, // 电池组过压保护恢复阈值
	2, // 单体过压告警恢复阈值
	2, // 单体过压保护恢复阈值
	2, // 单体欠压告警恢复阈值
	2, // 单体欠压保护恢复阈值
	0, // 充电高温告警恢复阈值
	2, // 充电高温保护恢复阈值
	0, // 放电高温告警恢复阈值
	2, // 放电高温保护恢复阈值
	0, // 充电低温告警恢复阈值
	2, // 充电低温保护恢复阈值
	0, // 放电低温告警恢复阈值
	2, // 放电低温保护恢复阈值
	2, // 环境温度高告警恢复阈值
	1, // 环境温度高保护恢复阈值
	2, // 环境温度低告警恢复阈值
	1, // 环境温度低保护恢复阈值
	0, // 单板过温告警恢复阈值,  参数范围不具备参考
	0, // 单板过温保护恢复阈值,  参数范围不具备参考
	0, // 加热垫使能
	0, // 陀螺仪倾角
	0, // 电池SOC低告警恢复阈值,  参数范围不具备参考
	0, // 陀螺仪防盗方式
	0, // 电池解锁方式
	3, // 放电过流告警恢复阈值,  参数范围不具备参考
	1, // 电池组欠压告警恢复阈值,  不允许设置，参数范围不具备参考性
	1, // 电池组欠压保护恢复阈值,  不允许设置，参数范围不具备参考性
	2, // 单体一致性差保护恢复阈值,  不允许设置，参数范围不具备参考性
	0, // 数据保存时间,  参数范围不具备参考性
	2, // 单体充电截止电压
	3, // 电池充满电压
	2, // 充电最大电流
	0, // 放电切换SOC
	0, // 放电模式
	2, // 恒压放电输出电压
	2, // 放电最大电流
	0, // 充电加热膜启动温度
	0, // 充电加热膜关闭温度
	0, // 加热膜过温阈值
	0, // 加热膜过温解除
	0, // 补充电SOC
	0, // 智能间歇充电使能
	0, // 电池组充电限流使能
	0, // 停电时间保护阈值
	0, // 停电时间保护使能
	0, // 均流SOC补偿幅值
	0, // 均流SOC补偿斜率
	0, // 均流SOC补偿使能
	0, // 休眠指示灯
	0, // 电池地址获取方式
	0, // 电池切换地址
	0, // 充电轮换使能
	2, // 单体补充电电压
	2, // 末期放电电压1
	2, // 末期放电电压2
	0, // 单体温度保护屏蔽
	0, // 充放电机测试模式
	0, // 故障前录波时间间隔
	0, // 故障后录波时间间隔
	0, // 故障前录波条数
	0, // 故障后录波条数
	0, // 测点数量X
	0, // 可变测点ID
	3, // 放电过流保护阈值,  不允许设置，参数范围不具备参考性
	0, // 直流内阻异常告警阈值
	0, // 直流内阻异常保护阈值
	1, // 单体温升速率异常阈值
	1, // 自放电容量比率
	0, // 自研协议偏移地址
	1, // 容量衰减一致性差告警比率阈值
	0, // 电池异常温度高保护阈值
	0, // 充电MAP使能
	0, // 严重告警闪烁方式
	0, // 次级告警闪烁方式
	0, // 电芯类型
	0, // 放电加热膜启动温度
	0, // 放电加热膜关闭温度
	0, // NTC无效屏蔽路数
	0, // 放电末期切换SOC2,  该参数暂不使用，值随放电模式联动，恒压放电模式为0%，混用为10%
}; 

/// 参数类型数组(运行参数，配置参数，校准参数)
const BYTE s_aucParaType[PARA_NUM] =
{
	PARATYPE_RUNNING, // 充电过流告警阈值
	PARATYPE_RUNNING, // 充电过流保护阈值
	PARATYPE_RUNNING, // 放电过流告警阈值
	PARATYPE_RUNNING, // 电池组过压告警阈值
	PARATYPE_RUNNING, // 电池组过压保护阈值
	PARATYPE_RUNNING, // 单板过温保护阈值,  参数范围不具备参考
	PARATYPE_RUNNING, // 单板过温告警阈值,  参数范围不具备参考
	PARATYPE_RUNNING, // 环境温度高告警阈值
	PARATYPE_RUNNING, // 环境温度低告警阈值
	PARATYPE_RUNNING, // 电池组欠压告警阈值
	PARATYPE_RUNNING, // 电池组欠压保护阈值
	PARATYPE_RUNNING, // 单体过压告警阈值
	PARATYPE_RUNNING, // 单体过压保护阈值
	PARATYPE_RUNNING, // 单体欠压告警阈值
	PARATYPE_RUNNING, // 单体欠压保护阈值
	PARATYPE_RUNNING, // 充电高温告警阈值
	PARATYPE_RUNNING, // 充电高温保护阈值
	PARATYPE_RUNNING, // 放电高温告警阈值
	PARATYPE_RUNNING, // 放电高温保护阈值
	PARATYPE_RUNNING, // 充电低温告警阈值
	PARATYPE_RUNNING, // 充电低温保护阈值
	PARATYPE_RUNNING, // 放电低温告警阈值
	PARATYPE_RUNNING, // 放电低温保护阈值
	PARATYPE_RUNNING, // 单体一致性差告警阈值
	PARATYPE_RUNNING, // 单体一致性差保护阈值
	PARATYPE_RUNNING, // 电池SOC低告警阈值
	PARATYPE_RUNNING, // 电池SOC低保护阈值
	PARATYPE_RUNNING, // 电池SOH低告警阈值
	PARATYPE_RUNNING, // 电池SOH低保护阈值
	PARATYPE_RUNNING, // 单体损坏保护阈值
	PARATYPE_RUNNING, // 电池充满电流
	PARATYPE_RUNNING, // 充电最长时间
	PARATYPE_RUNNING, // 充电末期维持时间
	PARATYPE_RUNNING, // 电池补充电电压
	PARATYPE_RUNNING, // 单体均衡启动压差阈值
	PARATYPE_RUNNING, // 历史数据保存间隔
	PARATYPE_CONFIG, // 蜂鸣器使能
	PARATYPE_CONFIG, // 电池容量
	PARATYPE_CONFIG, // 软件防盗延时
	PARATYPE_CONFIG, // 设备名称
	PARATYPE_CONFIG, // 单次放电DOD
	PARATYPE_CONFIG, // 单体动态欠压保护
	PARATYPE_CONFIG, // 告警干接点
	PARATYPE_CONFIG, // 告警级别
	PARATYPE_CONFIG, // 启用日期
	PARATYPE_CONFIG, // 整组欠压保护温度补偿
	PARATYPE_CONFIG, // BMS系统名称
	PARATYPE_CONFIG, // 干接点默认状态
	PARATYPE_RUNNING, // 环境温度高保护阈值
	PARATYPE_RUNNING, // 环境温度低保护阈值
	PARATYPE_RUNNING, // 电池组过压告警恢复阈值
	PARATYPE_RUNNING, // 电池组过压保护恢复阈值
	PARATYPE_RUNNING, // 单体过压告警恢复阈值
	PARATYPE_RUNNING, // 单体过压保护恢复阈值
	PARATYPE_RUNNING, // 单体欠压告警恢复阈值
	PARATYPE_RUNNING, // 单体欠压保护恢复阈值
	PARATYPE_RUNNING, // 充电高温告警恢复阈值
	PARATYPE_RUNNING, // 充电高温保护恢复阈值
	PARATYPE_RUNNING, // 放电高温告警恢复阈值
	PARATYPE_RUNNING, // 放电高温保护恢复阈值
	PARATYPE_RUNNING, // 充电低温告警恢复阈值
	PARATYPE_RUNNING, // 充电低温保护恢复阈值
	PARATYPE_RUNNING, // 放电低温告警恢复阈值
	PARATYPE_RUNNING, // 放电低温保护恢复阈值
	PARATYPE_RUNNING, // 环境温度高告警恢复阈值
	PARATYPE_RUNNING, // 环境温度高保护恢复阈值
	PARATYPE_RUNNING, // 环境温度低告警恢复阈值
	PARATYPE_RUNNING, // 环境温度低保护恢复阈值
	PARATYPE_RUNNING, // 单板过温告警恢复阈值,  参数范围不具备参考
	PARATYPE_RUNNING, // 单板过温保护恢复阈值,  参数范围不具备参考
	PARATYPE_CONFIG, // 加热垫使能
	PARATYPE_CONFIG, // 陀螺仪倾角
	PARATYPE_RUNNING, // 电池SOC低告警恢复阈值,  参数范围不具备参考
	PARATYPE_CONFIG, // 陀螺仪防盗方式
	PARATYPE_CONFIG, // 电池解锁方式
	PARATYPE_RUNNING, // 放电过流告警恢复阈值,  参数范围不具备参考
	PARATYPE_RUNNING, // 电池组欠压告警恢复阈值,  不允许设置，参数范围不具备参考性
	PARATYPE_RUNNING, // 电池组欠压保护恢复阈值,  不允许设置，参数范围不具备参考性
	PARATYPE_RUNNING, // 单体一致性差保护恢复阈值,  不允许设置，参数范围不具备参考性
	PARATYPE_RUNNING, // 数据保存时间,  参数范围不具备参考性
	PARATYPE_RUNNING, // 单体充电截止电压
	PARATYPE_RUNNING, // 电池充满电压
	PARATYPE_RUNNING, // 充电最大电流
	PARATYPE_CONFIG, // 放电切换SOC
	PARATYPE_CONFIG, // 放电模式
	PARATYPE_CONFIG, // 恒压放电输出电压
	PARATYPE_CONFIG, // 放电最大电流
	PARATYPE_CONFIG, // 充电加热膜启动温度
	PARATYPE_CONFIG, // 充电加热膜关闭温度
	PARATYPE_CONFIG, // 加热膜过温阈值
	PARATYPE_CONFIG, // 加热膜过温解除
	PARATYPE_CONFIG, // 补充电SOC
	PARATYPE_CONFIG, // 智能间歇充电使能
	PARATYPE_CONFIG, // 电池组充电限流使能
	PARATYPE_CONFIG, // 停电时间保护阈值
	PARATYPE_CONFIG, // 停电时间保护使能
	PARATYPE_CONFIG, // 均流SOC补偿幅值
	PARATYPE_CONFIG, // 均流SOC补偿斜率
	PARATYPE_CONFIG, // 均流SOC补偿使能
	PARATYPE_CONFIG, // 休眠指示灯
	PARATYPE_CONFIG, // 电池地址获取方式
	PARATYPE_CONFIG, // 电池切换地址
	PARATYPE_CONFIG, // 充电轮换使能
	PARATYPE_RUNNING, // 单体补充电电压
	PARATYPE_CONFIG, // 末期放电电压1
	PARATYPE_CONFIG, // 末期放电电压2
	PARATYPE_RUNNING, // 单体温度保护屏蔽
	PARATYPE_CONFIG, // 充放电机测试模式
	PARATYPE_CONFIG, // 故障前录波时间间隔
	PARATYPE_CONFIG, // 故障后录波时间间隔
	PARATYPE_CONFIG, // 故障前录波条数
	PARATYPE_CONFIG, // 故障后录波条数
	PARATYPE_CONFIG, // 测点数量X
	PARATYPE_CONFIG, // 可变测点ID
	PARATYPE_RUNNING, // 放电过流保护阈值,  不允许设置，参数范围不具备参考性
	PARATYPE_CONFIG, // 直流内阻异常告警阈值
	PARATYPE_CONFIG, // 直流内阻异常保护阈值
	PARATYPE_RUNNING, // 单体温升速率异常阈值
	PARATYPE_RUNNING, // 自放电容量比率
	PARATYPE_CONFIG, // 自研协议偏移地址
	PARATYPE_RUNNING, // 容量衰减一致性差告警比率阈值
	PARATYPE_RUNNING, // 电池异常温度高保护阈值
	PARATYPE_CONFIG, // 充电MAP使能
	PARATYPE_CONFIG, // 严重告警闪烁方式
	PARATYPE_CONFIG, // 次级告警闪烁方式
	PARATYPE_CONFIG, // 电芯类型
	PARATYPE_CONFIG, // 放电加热膜启动温度
	PARATYPE_CONFIG, // 放电加热膜关闭温度
	PARATYPE_CONFIG, // NTC无效屏蔽路数
	PARATYPE_CONFIG, // 放电末期切换SOC2,  该参数暂不使用，值随放电模式联动，恒压放电模式为0%，混用为10%
}; 

/// 参数数据类型数组
const BYTE aucVarType[PARA_NUM] =
{
	PARA_TYPE_FLOAT, // 充电过流告警阈值
	PARA_TYPE_FLOAT, // 充电过流保护阈值
	PARA_TYPE_FLOAT, // 放电过流告警阈值
	PARA_TYPE_FLOAT, // 电池组过压告警阈值
	PARA_TYPE_FLOAT, // 电池组过压保护阈值
	PARA_TYPE_FLOAT, // 单板过温保护阈值,  参数范围不具备参考
	PARA_TYPE_FLOAT, // 单板过温告警阈值,  参数范围不具备参考
	PARA_TYPE_FLOAT, // 环境温度高告警阈值
	PARA_TYPE_FLOAT, // 环境温度低告警阈值
	PARA_TYPE_FLOAT, // 电池组欠压告警阈值
	PARA_TYPE_FLOAT, // 电池组欠压保护阈值
	PARA_TYPE_FLOAT, // 单体过压告警阈值
	PARA_TYPE_FLOAT, // 单体过压保护阈值
	PARA_TYPE_FLOAT, // 单体欠压告警阈值
	PARA_TYPE_FLOAT, // 单体欠压保护阈值
	PARA_TYPE_FLOAT, // 充电高温告警阈值
	PARA_TYPE_FLOAT, // 充电高温保护阈值
	PARA_TYPE_FLOAT, // 放电高温告警阈值
	PARA_TYPE_FLOAT, // 放电高温保护阈值
	PARA_TYPE_FLOAT, // 充电低温告警阈值
	PARA_TYPE_FLOAT, // 充电低温保护阈值
	PARA_TYPE_FLOAT, // 放电低温告警阈值
	PARA_TYPE_FLOAT, // 放电低温保护阈值
	PARA_TYPE_FLOAT, // 单体一致性差告警阈值
	PARA_TYPE_FLOAT, // 单体一致性差保护阈值
	PARA_TYPE_WORD, // 电池SOC低告警阈值
	PARA_TYPE_WORD, // 电池SOC低保护阈值
	PARA_TYPE_WORD, // 电池SOH低告警阈值
	PARA_TYPE_WORD, // 电池SOH低保护阈值
	PARA_TYPE_FLOAT, // 单体损坏保护阈值
	PARA_TYPE_FLOAT, // 电池充满电流
	PARA_TYPE_WORD, // 充电最长时间
	PARA_TYPE_WORD, // 充电末期维持时间
	PARA_TYPE_FLOAT, // 电池补充电电压
	PARA_TYPE_FLOAT, // 单体均衡启动压差阈值
	PARA_TYPE_WORD, // 历史数据保存间隔
	PARA_TYPE_BOOLEAN, // 蜂鸣器使能
	PARA_TYPE_WORD, // 电池容量
	PARA_TYPE_WORD, // 软件防盗延时
	PARA_TYPE_BYTE, // 设备名称
	PARA_TYPE_BYTE, // 单次放电DOD
	PARA_TYPE_BYTE, // 单体动态欠压保护
	PARA_TYPE_BYTE, // 告警干接点
	PARA_TYPE_BYTE, // 告警级别
	PARA_TYPE_T_DateStruct, // 启用日期
	PARA_TYPE_BYTE, // 整组欠压保护温度补偿
	PARA_TYPE_BYTE, // BMS系统名称
	PARA_TYPE_BYTE, // 干接点默认状态
	PARA_TYPE_FLOAT, // 环境温度高保护阈值
	PARA_TYPE_FLOAT, // 环境温度低保护阈值
	PARA_TYPE_FLOAT, // 电池组过压告警恢复阈值
	PARA_TYPE_FLOAT, // 电池组过压保护恢复阈值
	PARA_TYPE_FLOAT, // 单体过压告警恢复阈值
	PARA_TYPE_FLOAT, // 单体过压保护恢复阈值
	PARA_TYPE_FLOAT, // 单体欠压告警恢复阈值
	PARA_TYPE_FLOAT, // 单体欠压保护恢复阈值
	PARA_TYPE_FLOAT, // 充电高温告警恢复阈值
	PARA_TYPE_FLOAT, // 充电高温保护恢复阈值
	PARA_TYPE_FLOAT, // 放电高温告警恢复阈值
	PARA_TYPE_FLOAT, // 放电高温保护恢复阈值
	PARA_TYPE_FLOAT, // 充电低温告警恢复阈值
	PARA_TYPE_FLOAT, // 充电低温保护恢复阈值
	PARA_TYPE_FLOAT, // 放电低温告警恢复阈值
	PARA_TYPE_FLOAT, // 放电低温保护恢复阈值
	PARA_TYPE_FLOAT, // 环境温度高告警恢复阈值
	PARA_TYPE_FLOAT, // 环境温度高保护恢复阈值
	PARA_TYPE_FLOAT, // 环境温度低告警恢复阈值
	PARA_TYPE_FLOAT, // 环境温度低保护恢复阈值
	PARA_TYPE_FLOAT, // 单板过温告警恢复阈值,  参数范围不具备参考
	PARA_TYPE_FLOAT, // 单板过温保护恢复阈值,  参数范围不具备参考
	PARA_TYPE_BOOLEAN, // 加热垫使能
	PARA_TYPE_BYTE, // 陀螺仪倾角
	PARA_TYPE_WORD, // 电池SOC低告警恢复阈值,  参数范围不具备参考
	PARA_TYPE_BYTE, // 陀螺仪防盗方式
	PARA_TYPE_BYTE, // 电池解锁方式
	PARA_TYPE_FLOAT, // 放电过流告警恢复阈值,  参数范围不具备参考
	PARA_TYPE_FLOAT, // 电池组欠压告警恢复阈值,  不允许设置，参数范围不具备参考性
	PARA_TYPE_FLOAT, // 电池组欠压保护恢复阈值,  不允许设置，参数范围不具备参考性
	PARA_TYPE_FLOAT, // 单体一致性差保护恢复阈值,  不允许设置，参数范围不具备参考性
	PARA_TYPE_WORD, // 数据保存时间,  参数范围不具备参考性
	PARA_TYPE_FLOAT, // 单体充电截止电压
	PARA_TYPE_FLOAT, // 电池充满电压
	PARA_TYPE_FLOAT, // 充电最大电流
	PARA_TYPE_WORD, // 放电切换SOC
	PARA_TYPE_BYTE, // 放电模式
	PARA_TYPE_FLOAT, // 恒压放电输出电压
	PARA_TYPE_FLOAT, // 放电最大电流
	PARA_TYPE_FLOAT, // 充电加热膜启动温度
	PARA_TYPE_FLOAT, // 充电加热膜关闭温度
	PARA_TYPE_FLOAT, // 加热膜过温阈值
	PARA_TYPE_FLOAT, // 加热膜过温解除
	PARA_TYPE_WORD, // 补充电SOC
	PARA_TYPE_BOOLEAN, // 智能间歇充电使能
	PARA_TYPE_BOOLEAN, // 电池组充电限流使能
	PARA_TYPE_ULONG, // 停电时间保护阈值
	PARA_TYPE_BOOLEAN, // 停电时间保护使能
	PARA_TYPE_BYTE, // 均流SOC补偿幅值
	PARA_TYPE_BYTE, // 均流SOC补偿斜率
	PARA_TYPE_BOOLEAN, // 均流SOC补偿使能
	PARA_TYPE_BYTE, // 休眠指示灯
	PARA_TYPE_BYTE, // 电池地址获取方式
	PARA_TYPE_BYTE, // 电池切换地址
	PARA_TYPE_BOOLEAN, // 充电轮换使能
	PARA_TYPE_FLOAT, // 单体补充电电压
	PARA_TYPE_FLOAT, // 末期放电电压1
	PARA_TYPE_FLOAT, // 末期放电电压2
	PARA_TYPE_BYTE, // 单体温度保护屏蔽
	PARA_TYPE_BYTE, // 充放电机测试模式
	PARA_TYPE_WORD, // 故障前录波时间间隔
	PARA_TYPE_WORD, // 故障后录波时间间隔
	PARA_TYPE_BYTE, // 故障前录波条数
	PARA_TYPE_BYTE, // 故障后录波条数
	PARA_TYPE_BYTE, // 测点数量X
	PARA_TYPE_BYTE, // 可变测点ID
	PARA_TYPE_FLOAT, // 放电过流保护阈值,  不允许设置，参数范围不具备参考性
	PARA_TYPE_BYTE, // 直流内阻异常告警阈值
	PARA_TYPE_BYTE, // 直流内阻异常保护阈值
	PARA_TYPE_FLOAT, // 单体温升速率异常阈值
	PARA_TYPE_FLOAT, // 自放电容量比率
	PARA_TYPE_BYTE, // 自研协议偏移地址
	PARA_TYPE_FLOAT, // 容量衰减一致性差告警比率阈值
	PARA_TYPE_FLOAT, // 电池异常温度高保护阈值
	PARA_TYPE_BOOLEAN, // 充电MAP使能
	PARA_TYPE_BYTE, // 严重告警闪烁方式
	PARA_TYPE_BYTE, // 次级告警闪烁方式
	PARA_TYPE_BYTE, // 电芯类型
	PARA_TYPE_FLOAT, // 放电加热膜启动温度
	PARA_TYPE_FLOAT, // 放电加热膜关闭温度
	PARA_TYPE_BYTE, // NTC无效屏蔽路数
	PARA_TYPE_WORD, // 放电末期切换SOC2,  该参数暂不使用，值随放电模式联动，恒压放电模式为0%，混用为10%
}; 

/// 操作记录ID-CID2
const BYTE g_aucParaCID2[] =
{
	0x96, // 充电过流告警阈值
	0x96, // 充电过流保护阈值
	0x49, // 放电过流告警阈值
	0x88, // 电池组过压告警阈值
	0x49, // 电池组过压保护阈值
	0x49, // 单板过温保护阈值,  参数范围不具备参考
	0x96, // 单板过温告警阈值,  参数范围不具备参考
	0x49, // 环境温度高告警阈值
	0x49, // 环境温度低告警阈值
	0x49, // 电池组欠压告警阈值
	0x49, // 电池组欠压保护阈值
	0x96, // 单体过压告警阈值
	0x49, // 单体过压保护阈值
	0x49, // 单体欠压告警阈值
	0x49, // 单体欠压保护阈值
	0x88, // 充电高温告警阈值
	0x49, // 充电高温保护阈值
	0x49, // 放电高温告警阈值
	0x49, // 放电高温保护阈值
	0x88, // 充电低温告警阈值
	0x49, // 充电低温保护阈值
	0x49, // 放电低温告警阈值
	0x49, // 放电低温保护阈值
	0x96, // 单体一致性差告警阈值
	0x49, // 单体一致性差保护阈值
	0x88, // 电池SOC低告警阈值
	0x88, // 电池SOC低保护阈值
	0x96, // 电池SOH低告警阈值
	0x49, // 电池SOH低保护阈值
	0x96, // 单体损坏保护阈值
	0x49, // 电池充满电流
	0x96, // 充电最长时间
	0x96, // 充电末期维持时间
	0x49, // 电池补充电电压
	0x96, // 单体均衡启动压差阈值
	0x96, // 历史数据保存间隔
	0x84, // 蜂鸣器使能
	0x84, // 电池容量
	0x96, // 软件防盗延时
	0x96, // 设备名称
	0x96, // 单次放电DOD
	0x96, // 单体动态欠压保护
	0x9B, // 告警干接点
	0x99, // 告警级别
	0x96, // 启用日期
	0x96, // 整组欠压保护温度补偿
	0x96, // BMS系统名称
	0x96, // 干接点默认状态
	0x88, // 环境温度高保护阈值
	0x88, // 环境温度低保护阈值
	0x88, // 电池组过压告警恢复阈值
	0x49, // 电池组过压保护恢复阈值
	0x96, // 单体过压告警恢复阈值
	0x49, // 单体过压保护恢复阈值
	0x49, // 单体欠压告警恢复阈值
	0x49, // 单体欠压保护恢复阈值
	0x88, // 充电高温告警恢复阈值
	0x49, // 充电高温保护恢复阈值
	0x49, // 放电高温告警恢复阈值
	0x49, // 放电高温保护恢复阈值
	0x88, // 充电低温告警恢复阈值
	0x49, // 充电低温保护恢复阈值
	0x49, // 放电低温告警恢复阈值
	0x49, // 放电低温保护恢复阈值
	0x49, // 环境温度高告警恢复阈值
	0x88, // 环境温度高保护恢复阈值
	0x49, // 环境温度低告警恢复阈值
	0x88, // 环境温度低保护恢复阈值
	0x96, // 单板过温告警恢复阈值,  参数范围不具备参考
	0x49, // 单板过温保护恢复阈值,  参数范围不具备参考
	0x84, // 加热垫使能
	0x96, // 陀螺仪倾角
	0x88, // 电池SOC低告警恢复阈值,  参数范围不具备参考
	0x96, // 陀螺仪防盗方式
	0x96, // 电池解锁方式
	0x49, // 放电过流告警恢复阈值,  参数范围不具备参考
	0x49, // 电池组欠压告警恢复阈值,  不允许设置，参数范围不具备参考性
	0x49, // 电池组欠压保护恢复阈值,  不允许设置，参数范围不具备参考性
	0x49, // 单体一致性差保护恢复阈值,  不允许设置，参数范围不具备参考性
	0x49, // 数据保存时间,  参数范围不具备参考性
	0x96, // 单体充电截止电压
	0x49, // 电池充满电压
	0x49, // 充电最大电流
	0x96, // 放电切换SOC
	0x96, // 放电模式
	0x96, // 恒压放电输出电压
	0x96, // 放电最大电流
	0x49, // 充电加热膜启动温度
	0x49, // 充电加热膜关闭温度
	0x49, // 加热膜过温阈值
	0x49, // 加热膜过温解除
	0x49, // 补充电SOC
	0x84, // 智能间歇充电使能
	0x84, // 电池组充电限流使能
	0x88, // 停电时间保护阈值
	0x88, // 停电时间保护使能
	0x96, // 均流SOC补偿幅值
	0x96, // 均流SOC补偿斜率
	0x96, // 均流SOC补偿使能
	0x96, // 休眠指示灯
	0x96, // 电池地址获取方式
	0x96, // 电池切换地址
	0x96, // 充电轮换使能
	0x49, // 单体补充电电压
	0x96, // 末期放电电压1
	0x96, // 末期放电电压2
	0x96, // 单体温度保护屏蔽
	0x96, // 充放电机测试模式
	0x96, // 故障前录波时间间隔
	0x96, // 故障后录波时间间隔
	0x96, // 故障前录波条数
	0x96, // 故障后录波条数
	0x96, // 测点数量X
	0x96, // 可变测点ID
	0x96, // 放电过流保护阈值,  不允许设置，参数范围不具备参考性
	0x96, // 直流内阻异常告警阈值
	0x96, // 直流内阻异常保护阈值
	0x96, // 单体温升速率异常阈值
	0x96, // 自放电容量比率
	0x96, // 自研协议偏移地址
	0x96, // 容量衰减一致性差告警比率阈值
	0x96, // 电池异常温度高保护阈值
	0x96, // 充电MAP使能
	0x96, // 严重告警闪烁方式
	0x96, // 次级告警闪烁方式
	0x96, // 电芯类型
	0x96, // 放电加热膜启动温度
	0x96, // 放电加热膜关闭温度
	0x96, // NTC无效屏蔽路数
	0x96, // 放电末期切换SOC2,  该参数暂不使用，值随放电模式联动，恒压放电模式为0%，混用为10%
}; 

/// 操作记录ID-command type
const BYTE g_aucParaCommandType[] =
{
	0x88, // 充电过流告警阈值
	0x89, // 充电过流保护阈值
	0xA2, // 放电过流告警阈值
	0x60, // 电池组过压告警阈值
	0x84, // 电池组过压保护阈值
	0x90, // 单板过温保护阈值,  参数范围不具备参考
	0x85, // 单板过温告警阈值,  参数范围不具备参考
	0xA8, // 环境温度高告警阈值
	0xAA, // 环境温度低告警阈值
	0xA0, // 电池组欠压告警阈值
	0x86, // 电池组欠压保护阈值
	0x83, // 单体过压告警阈值
	0x80, // 单体过压保护阈值
	0x9E, // 单体欠压告警阈值
	0x82, // 单体欠压保护阈值
	0x62, // 充电高温告警阈值
	0x88, // 充电高温保护阈值
	0xA6, // 放电高温告警阈值
	0x8C, // 放电高温保护阈值
	0x64, // 充电低温告警阈值
	0x8A, // 充电低温保护阈值
	0xA4, // 放电低温告警阈值
	0x8E, // 放电低温保护阈值
	0x8A, // 单体一致性差告警阈值
	0x9A, // 单体一致性差保护阈值
	0x6A, // 电池SOC低告警阈值
	0x6C, // 电池SOC低保护阈值
	0x8B, // 电池SOH低告警阈值
	0x9D, // 电池SOH低保护阈值
	0x8C, // 单体损坏保护阈值
	0x97, // 电池充满电流
	0x8E, // 充电最长时间
	0x8F, // 充电末期维持时间
	0x99, // 电池补充电电压
	0x8D, // 单体均衡启动压差阈值
	0x9B, // 历史数据保存间隔
	0xCA, // 蜂鸣器使能
	0xC0, // 电池容量
	0x9C, // 软件防盗延时
	0x9D, // 设备名称
	0x98, // 单次放电DOD
	0x93, // 单体动态欠压保护
	0x80, // 告警干接点
	0x80, // 告警级别
	0x9E, // 启用日期
	0x94, // 整组欠压保护温度补偿
	0x99, // BMS系统名称
	0xA4, // 干接点默认状态
	0x66, // 环境温度高保护阈值
	0x68, // 环境温度低保护阈值
	0x61, // 电池组过压告警恢复阈值
	0x85, // 电池组过压保护恢复阈值
	0x84, // 单体过压告警恢复阈值
	0x81, // 单体过压保护恢复阈值
	0x9F, // 单体欠压告警恢复阈值
	0x83, // 单体欠压保护恢复阈值
	0x63, // 充电高温告警恢复阈值
	0x89, // 充电高温保护恢复阈值
	0xA7, // 放电高温告警恢复阈值
	0x8D, // 放电高温保护恢复阈值
	0x65, // 充电低温告警恢复阈值
	0x8B, // 充电低温保护恢复阈值
	0xA5, // 放电低温告警恢复阈值
	0x8F, // 放电低温保护恢复阈值
	0xA9, // 环境温度高告警恢复阈值
	0x67, // 环境温度高保护恢复阈值
	0xAB, // 环境温度低告警恢复阈值
	0x69, // 环境温度低保护恢复阈值
	0x86, // 单板过温告警恢复阈值,  参数范围不具备参考
	0x91, // 单板过温保护恢复阈值,  参数范围不具备参考
	0xCB, // 加热垫使能
	0x9A, // 陀螺仪倾角
	0x6B, // 电池SOC低告警恢复阈值,  参数范围不具备参考
	0x9F, // 陀螺仪防盗方式
	0xA0, // 电池解锁方式
	0xA3, // 放电过流告警恢复阈值,  参数范围不具备参考
	0xA1, // 电池组欠压告警恢复阈值,  不允许设置，参数范围不具备参考性
	0x87, // 电池组欠压保护恢复阈值,  不允许设置，参数范围不具备参考性
	0x9B, // 单体一致性差保护恢复阈值,  不允许设置，参数范围不具备参考性
	0x9C, // 数据保存时间,  参数范围不具备参考性
	0x91, // 单体充电截止电压
	0x96, // 电池充满电压
	0xAC, // 充电最大电流
	0x97, // 放电切换SOC
	0x95, // 放电模式
	0x96, // 恒压放电输出电压
	0x92, // 放电最大电流
	0x92, // 充电加热膜启动温度
	0x93, // 充电加热膜关闭温度
	0x94, // 加热膜过温阈值
	0x95, // 加热膜过温解除
	0x98, // 补充电SOC
	0xCE, // 智能间歇充电使能
	0xD0, // 电池组充电限流使能
	0x6D, // 停电时间保护阈值
	0x71, // 停电时间保护使能
	0x80, // 均流SOC补偿幅值
	0x81, // 均流SOC补偿斜率
	0x82, // 均流SOC补偿使能
	0xA1, // 休眠指示灯
	0xA2, // 电池地址获取方式
	0xA3, // 电池切换地址
	0xA5, // 充电轮换使能
	0xAD, // 单体补充电电压
	0xA6, // 末期放电电压1
	0xA7, // 末期放电电压2
	0xA8, // 单体温度保护屏蔽
	0xA9, // 充放电机测试模式
	0xAA, // 故障前录波时间间隔
	0xAB, // 故障后录波时间间隔
	0xAC, // 故障前录波条数
	0xAD, // 故障后录波条数
	0xAE, // 测点数量X
	0xAF, // 可变测点ID
	0x87, // 放电过流保护阈值,  不允许设置，参数范围不具备参考性
	0xB0, // 直流内阻异常告警阈值
	0xB1, // 直流内阻异常保护阈值
	0xB2, // 单体温升速率异常阈值
	0xB3, // 自放电容量比率
	0xB4, // 自研协议偏移地址
	0xB5, // 容量衰减一致性差告警比率阈值
	0xB6, // 电池异常温度高保护阈值
	0xB7, // 充电MAP使能
	0xB8, // 严重告警闪烁方式
	0xB9, // 次级告警闪烁方式
	0xBA, // 电芯类型
	0xBB, // 放电加热膜启动温度
	0xBC, // 放电加热膜关闭温度
	0xBD, // NTC无效屏蔽路数
	0xBE, // 放电末期切换SOC2,  该参数暂不使用，值随放电模式联动，恒压放电模式为0%，混用为10%
}; 

/// 告警记录ID
const BYTE g_aucAlarmHistoryID[] =
{
	0x80, // 电池组欠压告警
	0x81, // 电池组过压告警
	0x82, // 电池组欠压保护
	0x83, // 电池组过压保护
	0x84, // 充电过流告警
	0x85, // 充电过流保护
	0x86, // 放电过流告警
	0x87, // 放电过流保护
	0x88, // 电池短路
	0x89, // 电池反接
	0x8A, // 放电回路失效
	0x8B, // 充电回路失效
	0x8C, // 限流回路失效
	0x8D, // 电池SOC低告警
	0x8E, // 电池SOC低保护
	0x8F, // 电池SOH低告警
	0x90, // 电池SOH低保护
	0x91, // 单板过温告警
	0x92, // 单板过温保护
	0x93, // 单体欠压告警
	0x94, // 单体过压告警
	0x95, // 单体欠压保护
	0x96, // 单体过压保护
	0x97, // 单体一致性差告警
	0x98, // 单体一致性差保护
	0x99, // 单体电压采样异常
	0x9A, // 单体损坏保护
	0x9B, // 单体充电高温告警
	0x9C, // 单体充电低温告警
	0x9D, // 单体充电高温保护
	0x9E, // 单体充电低温保护
	0x9F, // 单体放电高温告警
	0xA0, // 单体放电低温告警
	0xA1, // 单体放电高温保护
	0xA2, // 单体放电低温保护
	0xA3, // 单体温度传感器无效告警
	0xA4, // 环境温度高告警
	0xA5, // 环境温度低告警
	0xA6, // 环境温度高保护
	0xA7, // 环境温度低保护
	0xA8, // 环境温度传感器无效告警
	0xA9, // BDU电池欠压保护
	0xAA, // BDU EEPROM故障
	0xAB, // BDU母排欠压保护
	0xAC, // BDU母排过压保护
	0xAD, // BDU通信断
	0xAE, // BDU电池充电欠压保护
	0xAF, // BDU电池闭锁告警
	0xB0, // 均衡电路故障告警
	0xB1, // 电池丢失告警
	0xB2, // 机内过温保护
	0xB3, // 回路异常
	0xB4, // 停电时间保护告警
	0xB5, // 加热膜失效
	0xB6, // 熔丝损坏
	0xB7, // 连接器温度高保护
	0xB8, // 主继电器失效
	0xB9, // DCDC故障
	0xBA, // 采集异常
	0xBB, // 辅助源故障
	0xBC, // 单体温度异常
	0xBD, // 地址冲突
	0xBE, // 单体动态欠压保护
	0xBF, // 加热膜连接器高温
	0xC0, // 单体温升速率异常
	0xC1, // 直流内阻异常告警
	0xC2, // 直流内阻异常保护
	0xC3, // 自放电异常
	0xC4, // 电源通信断
	0xC5, // 容量衰减一致性差告警
	0xC6, // 电池异常高温保护告警
}; 

const BYTE g_aucParaIdSection[PARA_NUM] = 
{
    0x02, // 充电过流告警阈值
    0x02, // 充电过流保护阈值
    0x02, // 放电过流告警阈值
    0x02, // 电池组过压告警阈值
    0x02, // 电池组过压保护阈值
    0x02, // 单板过温保护阈值,  参数范围不具备参考
    0x02, // 单板过温告警阈值,  参数范围不具备参考
    0x02, // 环境温度高告警阈值
    0x02, // 环境温度低告警阈值
    0x02, // 电池组欠压告警阈值
    0x02, // 电池组欠压保护阈值
    0x02, // 单体过压告警阈值
    0x02, // 单体过压保护阈值
    0x02, // 单体欠压告警阈值
    0x02, // 单体欠压保护阈值
    0x02, // 充电高温告警阈值
    0x02, // 充电高温保护阈值
    0x02, // 放电高温告警阈值
    0x02, // 放电高温保护阈值
    0x02, // 充电低温告警阈值
    0x02, // 充电低温保护阈值
    0x02, // 放电低温告警阈值
    0x02, // 放电低温保护阈值
    0x02, // 单体一致性差告警阈值
    0x02, // 单体一致性差保护阈值
    0x02, // 电池SOC低告警阈值
    0x02, // 电池SOC低保护阈值
    0x02, // 电池SOH告警阈值
    0x02, // 电池SOH保护阈值
    0x02, // 单体损坏保护阈值
    0x03, // 电池充满电流
    0x03, // 充电最长时间
    0x03, // 充电末期维持时间
    0x03, // 电池补充电电压
    0x03, // 单体均衡启动压差阈值
    0x03, // 历史数据保存间隔
    0x03, // 蜂鸣器使能
    0x03, // 电池容量
    0x01, // 软件防盗延时/通信断延时
    0x01, // 设备名称
    0x01, // 单次放电DOD
    0x03, // 单体动态欠压保护
    0x04, // 告警干接点
    0x05, // 告警级别
    0x03, // 启用日期
    0x03, // 整组欠压保护温度补偿
    0x03, // BMS系统名称
    0x01, // 干接点默认状态
    0x02, // 环境温度高保护阈值
    0x02, // 环境温度低保护阈值
    0x02, // 电池组过压告警恢复值
    0x02, // 电池组过压保护恢复值
    0x02, // 单体过压告警恢复值
    0x02, // 单体过压保护恢复值
    0x02, // 单体欠压告警恢复值
    0x02, // 单体欠压保护恢复值
    0x02, // 充电高温告警恢复阈值
    0x02, // 充电高温保护恢复阈值
    0x02, // 放电高温告警恢复阈值
    0x02, // 放电高温保护恢复阈值
    0x02, // 充电低温告警恢复阈值
    0x02, // 充电低温保护恢复阈值
    0x02, // 放电低温告警恢复阈值
    0x02, // 放电低温保护恢复阈值
    0x02, // 环境温度高告警恢复阈值
    0x02, // 环境温度高保护恢复阈值
    0x02, // 环境温度低告警恢复阈值
    0x02, // 环境温度低保护恢复阈值
    0x02, // 单板过温告警恢复阈值,  参数范围不具备参考
    0x02, // 单板过温保护恢复阈值,  参数范围不具备参考
    0x01, // 加热垫使能
    0x03, // 陀螺仪倾角
    0x02, // 电池SOC低告警恢复阈值,  参数范围不具备参考
    0x03, // 陀螺仪防盗方式
    0x03, // 电池解锁方式
    0x02, // 放电过流告警恢复阈值,  参数范围不具备参考
    0x02, // 电池组欠压告警恢复阈值,  不允许设置，参数范围不具备参考性
    0x02, // 电池组欠压保护恢复阈值,  不允许设置，参数范围不具备参考性
    0x02, // 单体一致性差保护恢复阈值,  不允许设置，参数范围不具备参考性
    0x03, // 数据保存时间,  参数范围不具备参考性
    0x01, // 单体充电截止电压
    0x03, // 电池组充满电压
    0x03, // 充电最大电流
    0x01, // 放电切换SOC
    0x01, // 放电模式
    0x01, // 恒压放电输出电压
    0x03, // 放电最大电流
    0x03, // 加热膜启动温度
    0x03, // 加热膜关闭温度
    0x03, // 加热膜过温阈值
    0x03, // 加热膜过温解除
    0x03, // 补充电SOC
    0x03, // 智能间歇充电使能
    0x03, // 电池组充电限流使能
    0x03, // 停电时间保护阈值
    0x03, // 停电时间保护使能
    0x03, // 均流SOC补偿幅值
    0x03, // 均流SOC补偿斜率
    0x03, // 均流SOC补偿使能
    0x01, // 休眠指示灯
    0x01, // 电池地址获取方式
    0x01, // 电池切换地址
    0x03, // 充电轮换使能
    0x03, // 单体补充电电压
    0x03, // 末期放电电压1
    0x03, // 末期放电电压2
    0x03, // 单体温度保护屏蔽
    0x01, // 充放电机测试模式
    0x01, // 故障前录波时间间隔
    0x01, // 故障后录波时间间隔
    0x01, // 故障前录波条数
    0x01, // 故障后录波条数
    0x01, // 测点数量X
    0x01, // 可变测点ID
    0x02, // 放电过流保护阈值
    0x02, // 直流内阻异常告警阈值
    0x02, // 直流内阻异常保护阈值
    0x01, // 单体温升速率异常阈值
    0x01, // 自放电容量比率
    0x01, // 自研协议偏移地址
    0x02, // 容量衰减一致性差告警比率阈值
    0x02, // 电池异常温度高保护阈值
    0x03, // 充电MAP使能
    0x01, // 严重告警闪烁方式
    0x01, // 次级告警闪烁方式
    0x01, // 电芯类型
    0x03, // 放电加热膜启动温度
    0x03, // 放电加热膜关闭温度
    0x01, // NTC无效屏蔽路数
    0x01, // 放电末期切换SOC2
};

#ifdef __cplusplus
} /* end of the extern "C" block */
#endif

#endif

