#include <string.h>
#include <stdlib.h>
#include <rtthread.h>
#include "utils_thread.h"
#include "device_type.h"
#include "msg.h"
#include "cmd.h"
#include "north_main.h"
#include "data_type.h"
#include "sps.h"
#include "utils_server.h"
#include "utils_string.h"
#include "protocol_north_modbus.h"
#include "dev_north_seub_apptest.h"

#define commun_timeout_cnt 1200
Static dev_inst_t *north_dev_seub = NULL;
Static unsigned int reset_counter = 0;
Static unsigned char s_apptest_usart_test_flag = FALSE; // 是否正在执行apptest协议的串口回环测试

north_mgr_t *init_thread_data(link_inst_t *link_inst, unsigned char mod_id)
{
    north_mgr_t *north_mgr = NULL;

    north_mgr = rt_malloc(sizeof(north_mgr_t));
    if (NULL == north_mgr)
    return NULL;

    north_mgr->cmd_buff = rt_malloc(sizeof(cmd_buf_t));
    if (NULL == north_mgr->cmd_buff)
    {
        goto NORTH_MGR_FREE;
    }

    north_mgr->north_mq = init_msg_queue(mod_id);
    if (NULL == north_mgr->north_mq)
    {
        goto CMD_BUFF_FREE;
    }

    north_mgr->link_inst = link_inst;
    return north_mgr;

CMD_BUFF_FREE:
    free(north_mgr->cmd_buff);

NORTH_MGR_FREE:
    free(north_mgr);
    return NULL;
}

Static msg_map s_north_msg_map[] =
{
    {0, NULL},
};


/* 北向初始化*/
void* init_north(void * param) {
    RETURN_VAL_IF_FAIL(param != NULL, NULL);
    server_info_t *server_info = (server_info_t *)param;
    north_mgr_t* north_mgr = NULL;

    update_north_init();
    north_dev_seub = init_dev_inst(DEV_NORTH_SEUB);
    RETURN_VAL_IF_FAIL(north_dev_seub != NULL, NULL);

    server_info->server.server.map_size = sizeof(s_north_msg_map) / sizeof(msg_map);
    register_server_msg_map(s_north_msg_map, server_info);

    north_mgr = init_thread_data(north_dev_seub->dev_type->link_inst, MOD_NORTH_SEUB_COMM);
    return north_mgr;
}

/* NORTH_MAIN收发线程 */
void north_comm_th(void *param)
{
    unsigned short i = 0;
    unsigned char time_delay = 50;
    int data_len, result = FAILURE;
    north_mgr_t *north_mgr = (north_mgr_t *)param;
    module_msg_t msg;
    cmd_buf_t *cmd_buff;

    rt_device_t g_serial;
    rt_uint8_t byte;

    while (is_running(TRUE))
    {
        rt_memset_s(&msg, sizeof(module_msg_t),0x00, sizeof(module_msg_t));
        reset_counter++;
        if (reset_counter > commun_timeout_cnt) {
            reset_counter = 0;
        }

        // 如果正在进行apptest协议的串口回环测试，则跳过中断，避免影响测试结果
        if(s_apptest_usart_test_flag == TRUE)
        {
            rt_sem_clear_value(&(north_mgr->link_inst->rx_sem));
            rt_thread_mdelay(time_delay);
            continue;
        }

        if (RT_EOK == rt_sem_take(&(north_mgr->link_inst->rx_sem), THREAD_TICK))
        {
            if (SUCCESSFUL == cmd_recv(north_dev_seub, north_mgr->cmd_buff))
            {
                reset_counter = 0;
                cmd_send(north_dev_seub, north_mgr->cmd_buff);
            }
            else
            {
                north_mgr->link_inst->r_cache.index = 0;//发送错误帧以后r_cache.buff没有清导致正确帧也不回包
            }
            continue;
        }
        rt_thread_mdelay(time_delay);
    }
}


unsigned char set_apptest_usart_test_flag(unsigned char flag_set)
{
    s_apptest_usart_test_flag = flag_set;
    return SUCCESSFUL;
}

