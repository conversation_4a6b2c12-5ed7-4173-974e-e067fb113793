/**************************************************************************
* Copyright (C) 2001, ZTE Corporation.
* 版权信息：（C）2009，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：BMS
* 文件名称：MdbsRtu.h
* 文件说明：
* 作    者：
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/

#ifndef CM7_APPLICATIONS_INCLUDE_MDBSRTU_H_
#define CM7_APPLICATIONS_INCLUDE_MDBSRTU_H_

#include "comm.h"
#include "common.h"

#ifdef __cplusplus
extern "C" {
#endif

#define APP_FAILURE -1
#define APP_SUCCESS 0

#define COMM_FAIL_COUNT 1000
#define BIT_NUM_IN_ONE_BYTE 8
#define MODBUSRTU_TEMP_LEN 1024
#define BIT_NUM_SIXTEEN 16
#define DEFAULT_WRITE_ANA_SINGLE_CNT 1
#define DEFAULT_WRITE_DIG_SINGLE_CNT 1
#define DEFAULT_WRITE_ANA_SINGLE_BYTECNT 2
#define DEFAULT_WRITE_DIG_SINGLE_BYTECNT 2
#define DEFAULT_WRITE_ANA_SINGLE_REGCNT 2
#define DEFAULT_SND_ANA_SINGLE_LEN (DEFAULT_WRITE_ANA_SINGLE_REGCNT + DEFAULT_WRITE_ANA_SINGLE_BYTECNT)

#define ILL_DATA_ADDR_FAILURE 2
#define ILL_DATA_VAL_FAILURE 3
#define ILL_FUNC_FAILURE 4
#define MODBUS_ERRORCODE_MASK   0x80
#define MIN_MDBSREC_BYTE_CNT    8
#define MAX_MDBSREC_BYTE_CNT    265
#define MIN_MDBSWRITE_MUL_BYTE_CNT    9
#define MAX_MDBSWRITE_MUL_BYTE_CNT    265
#define DEFAULT_MDBSWRITE_SGL_BYTE_CNT    8
#define DEFAULT_MDBSWRITE_RESETHISDATA_BYTE_CNT    8
#define MDBSWRITE_SGL_NET_ANTITHEFT_BYTE_CNT    40
#define MDBSWRITE_HEART_NET_ANTITHEFT_BYTE_CNT    55
#define MDBSWRITE_CHANGE_NET_ANTITHEFT_BYTE_CNT    72
#define DATA_COIL_ON    0xFF00
#define DATA_COIL_OFF   0x0000
#define REC_CTRL_INVALID    0
#define REC_CTRL_VALID      1

#define WRITE_DEFAULT		0x0000
#define WRITE_SYSPARA_PART	0x0001
#define WRITE_BMSPACK_PART	0x0010
/*********************   无效数据   **********************/
#define WORD_INVALID 0xFFFF
/*********************   缓冲区长度   **********************/
#define MODBUS_REC_BUF_LEN                 304     //协议层接收缓冲区长度

/*********************   应用层偏移地址  **********************/
#define MODBUSRTU_REC_DEVTYPE_OFFSET    0
#define MODBUSRTU_REC_FUNC_OFFSET       1
#define MODBUSRTU_REC_REGADDR_OFFSET    2
#define MODBUSRTU_REC_REGCNT_OFFSET     4
#define MODBUSRTU_REC_BYTECNT_OFFSET    6

#define MODBUSRTU_REC_WRITE_SINGLE_OFFSET 4
/*************************  功能码  ********************/
#define MODBUS_READ_DIG         0x01    // 读数字量(可读写)
#define MODBUS_READ_DIG_IN      0x02    // 读数字量(只读)

#if defined(PAKISTAN_CMPAK_PROTOCOL)
#define MODBUS_READ_ANA         0x04    // 读模拟量(可读写)
#define MODBUS_READ_ANA_IN      0x03    // 读模拟量(只读)
#else
#define MODBUS_READ_ANA         0x03    // 读模拟量(可读写)
#define MODBUS_READ_ANA_IN      0x04    // 读模拟量(只读)
#endif

#define MODBUS_WRITE_DIG_SINGLE 0x05    // 写状态量(单个)
#define MODBUS_WRITE_ANA_SINGLE 0x06    // 写模拟量(单个)
#define MODBUS_WRITE_DIG_MUL    0x0f    // 写状态量(多个)
#define MODBUS_WRITE_ANA_MUL    0x10    // 写模拟量(多个)

/************************   返回码   ***********************/
#define RTN_MODBUS_CORRECT          0x00
#define RTN_MODBUS_WRONG_FUNC       0x01    // 非法的功能码服务器不了解功能码
#define RTN_MODBUS_WRONG_REG_ADDR   0x02    // 非法的数据地址与请求有关
#define RTN_MODBUS_WRONG_VALUE      0x03    // 非法的数据值与请求有关(寄存器时数据内容不合法)
#define RTN_MODBUS_WRONG_TASK       0x04    // 服务故障逆变器通讯模块在执行过程中无法取出数据故障
#define NO_RETURN                   0xFF    // 不用返回响应包


#ifdef PAKISTAN_CMPAK_PROTOCOL
#define REGADDR_RESERVED_2             (7)
#define REGADDR_RESERVED_3             (3)
#define REGADDR_RESERVED_4             (16)
#define EXTRAL_CELL_TEMP               (8)
#define EXTRAL_CELL_VOLT               (8)
#define LENGTH_BATTERY_MODEL           (12)
#define REG_DEFAULT_VALUE_1            (0xFFFF)
#define REG_DEFAULT_VALUE_2            (0x8000)
#define OFFLINE_BATTERY_STATUS_CMPAK   (0)
#define CONSRV_BATTERY_STATUS_CMPAK    (1)
#define CHARGE_BATTERY_STATUS_CMPAK    (2)
#define DISCHARGE_BATTERY_STATUS_CMPAK (3)
#define STANDBY_BATTERY_STATUS_CMPAK   (4)
#define INVALID_BATTERY_STATUS_CMPAK   (0xFFFF)
#define BATTERY_SOFTWARE_VERSION_CMPAK (0x1031)
#define BATTERY_HARDWARE_VERSION_CMPAK (0x02) 
#define BATTERY_BOOT_VERSION_CMPAK     (0x02)
#define BATTERY_DEVICE_TYPE_CMPAK      (1)
#define BATTERY_MANUFATURE             (1)
#define BATTERY_SUB_SOFTWARE_ID        (0)
#define BATTERY_SYS_NAME_LENGTH        (12)

enum
{
    CRITICAL_BATTERY_ALARM_1 = 0, // Cmpak严重告警-1
    CRITICAL_BATTERY_ALARM_2,     // Cmpak严重告警-2
    CRITICAL_BATTERY_ALARM_3,     // Cmpak严重告警-3
    MAJOR_BATTERY_ALARM,          // 主要告警
    MINOR_BATTERY_ALARM,          // 次要告警
    MODULE_BATTERY_ALARM,         // 模块告警
};

enum
{
    RESERVE_REG_2 = 0, //保留寄存器
    RESERVE_REG_3,
    RESERVE_REG_4,
    CELL_TEMP_EXTRAL,
    CELL_VOLT_EXTRAL,
};
#endif



/************************  数据字典 数据类型 ********/
typedef enum
{
	DATA_TYPE_VOID = 0,
	DATA_TYPE_BOOLEAN,
	DATA_TYPE_CHAR,
	DATA_TYPE_INT8U,
	DATA_TYPE_INT8S,
	DATA_TYPE_INT16U,
	DATA_TYPE_INT16S,
	DATA_TYPE_INT32U,
	DATA_TYPE_INT32S,
	DATA_TYPE_FP32,
	DATA_TYPE_INT64S,
	DATA_TYPE_T_TIMESTRUCT,
	DATA_TYPE_T_DATESTRUCT,
	DATA_TYPE_T_PROCTIMESTRUCT,
	DATA_TYPE_BIT,
	DATA_TYPE_CTRL_DATA,
	MAX_DATA_TYPES,
}Enum_DataType;
/************************  通讯数据表上的精度********/
typedef enum
{
    APP_RTN_CORRECT = 0,
    APP_RTN_INCORRECT,
    APP_RTN_NO_ANSWER,
}Enum_MdbsRTN;


#if defined(PAKISTAN_CMPAK_PROTOCOL)
enum
{   
    ERRORCODE_NULL = 0,//correct
    ERRORCODE_ILL_FUNC,//功能码错误：0x01
    ERRORCODE_ILL_DATA_ADDR, //地址错误：0x02
    ERRORCODE_ILL_DATA_VAL, //数值错误：0x03
    ERRORCODE_FAIL_ASSOCIATED_DEV, //无法响应：0x04
    ERRORCODE_BUSY,
};
#else
enum
{   
    ERRORCODE_NULL = 0,//correct
    ERRORCODE_ILL_FUNC,//ILLEGAL FUNCTION(The )
    ERRORCODE_ILL_DATA_ADDR,
    ERRORCODE_ILL_DATA_VAL,
    ERRORCODE_FAIL_ASSOCIATED_DEV,
    ERRORCODE_ACK_WAIT,
    ERRORCODE_BUSY,
    ERRORCODE_NAK,
    ERRORCODE_FAIL_COMMAND,
};
#endif


enum
{
    COMM_NORMAL = 0,
    COMM_FAIL,
};

typedef enum
{
    COMM_NORMAL_STATE = 0,
    COMM_FAIL_STATE,
}Enum_MdbsState;
    
enum
{
    BIT_NUM_ZERO = 0,
    BIT_NUM_ONE,
    BIT_NUM_TWO,
    BIT_NUM_THREE,
    BIT_NUM_FOUR,
    BIT_NUM_FIVE,
    BIT_NUM_SIX,
    BIT_NUM_SEVEN,
    BIT_NUM_EIGHT,
};

typedef enum
{
	NULL_DATA=0,
	BCM_PART_DATA,
	DC_PART_DATA,
	BCM_PART_ALARM,
	DC_PART_ALARM,
	SYS_PARA,
	HIS_DATA,
	BDU_FAC_PART,
	BCM_FAC_PART,
	SPECIAL_DATA_PART,
	BMS_PACK_FAC_PART,
	PACK_MANUFACT_PART,
#if defined(PAKISTAN_CMPAK_PROTOCOL)
    CMPAK_TIME_PART,
#endif
	//BCM_RESULT_DATA,
	MAX_PART_NUM,
}Enum_MdbsDataType;

typedef enum
{
    DATA_TO_BYTEBUFF = 0,
    BYTEBUFF_TO_DATA,
    MAX_COV_TYPE,
}E_DATA_DIRECTION;

typedef enum
{
    ANSWER_NO = 0,
    ANSWER_YES,
}E_ANSWER_STATE;

/********************* 底层通信数据结构体定义 ************************/
typedef struct
{
    CHAR acBduFacSn[8];
    CHAR acBduPropertyInfo[30];
    BYTE ucBmsReset;
    BYTE ucStartChg;
    BYTE ucStartDisc;
    FLOAT fOutputVolt;
    FLOAT fChgCurrCoeff;
    BYTE ucLoadDefPara;
    BYTE ucStartAddrCompete;
    T_TimeStruct tTime;
    FLOAT fCsuSendCurr;
    BYTE ucReleaseLock;
    BYTE ucSendPowerOn;
    BYTE ucSendPowerOff;
    WORD wManualUnlock;
    BYTE ucFindBattery;
    BYTE ucClearDcrFaultPrt;
    WORD wMaxSOC;
    BYTE ucControlSwitchAddr;
    BYTE aucStartBinding[SITE_ANTITHEFT_KEY_LEN];
    BYTE aucHeartbeatBinding[SITE_ANTITHEFT_KEY_LEN];
    BYTE aucCloseBinding[SITE_ANTITHEFT_KEY_LEN];
    BYTE aucChangeSiteAntitheftKey[SITE_ANTITHEFT_KEY_LEN * 2];
    BYTE ucClear4GTraffic;
    FLOAT fOutputVoltFree;
    FLOAT fChgCurrCoeffFree;
    BYTE aucStartBindingKey[NETANTITHEFT_KEY_LEN];
    BYTE aucHeartbeatBindingKey[NETANTITHEFT_KEY_LEN + NETANTITHEFT_SN_LEN];
    BYTE aucCloseBindingKey[NETANTITHEFT_KEY_LEN];
    BYTE aucChangeBindingKey[NETANTITHEFT_KEY_LEN * 2];
}T_SpecialDataStruct;

#pragma pack(push,1)
typedef struct T_tagOneDataAttrStruct
{
	BYTE ucRdFunc;
	BYTE ucWrFunc;
	WORD wRegAddr;
	BYTE ucOldDataType;
	BYTE ucOldDataLen;
	Enum_MdbsDataType ucOldDataPart;
	WORD wOldDataOffset;
	WORD wDataNum;
	Enum_DataType ucNewDataType;
	BYTE ucNewPrecision;
	BYTE ucNewDataLen;
	INT32 (*pDealFunc)(const struct T_tagOneDataAttrStruct *ptInDataAttr);
}T_OneDataAttrStruct;
#pragma pack(pop)

typedef struct
{
	Enum_NorthType ucNorthType;
    BYTE ucPort;
    Enum_MdbsState ucCommFail;
    Enum_MdbsRTN ucAnswerRtn;
    BOOLEAN bSetSysTime;
    WORD wMaxHisDataRecCnt;
    WORD wMaxHisAlmRecCnt;
    WORD wMaxHisEnergeSnapRecCnt;
    WORD wCommFailCnt;
    INT32 slPacketUseable;
	const T_OneDataAttrStruct *ptDataAttr;
	INT32 slDataAttrNum;
	INT32 slTotalCnt;
}T_MdbsRtuManageStruct;

typedef struct {
    /***********   接收数据   ************/
    BYTE   aucRecBuff[MODBUS_REC_BUF_LEN]; //  缓存区
    WORD  wRecLength;                      //  缓存区数据长度
    /***********   发送数据   ************/
    BYTE   aucSendBuff[MODBUS_REC_BUF_LEN]; //  缓存区
    WORD  wSendLength;                      //  缓存区数据长度
}T_MdbsRtuLinkStruct;

typedef struct {
    /***********   接收数据   ************/
    WORD    wRecLen;                        // 接收到原始数据包长度
    BYTE    aucRecBuf[MODBUS_REC_BUF_LEN];     //接收到的16进制数据帧
    WORD    wTransactionFlag;//事物处理标识符
    WORD    wProType;//MODBUS中对应的协议类型
    WORD    wLen;//协议应用层中长度
    BYTE    ucDevType;//协议应用层中设备地址
    BYTE    ucFunc;
    WORD    wRegAddr;       // 寄存器起始地址
    WORD    wRegCnt;        // 寄存器数量
    WORD    wCRC;   
    /***********   发送数据   ************/
    BYTE    ucErrCode;
    WORD    wSendLen;
    BYTE    ucNeedRespond;      // 是否需要响应命令,YES需要响应命令,NO不需要响应命令
    BYTE    aucSendBuf[MODBUS_REC_BUF_LEN];   //响应数据包发送前的16进制数据帧
}T_MdbsRtuApp;

typedef struct
{
    BYTE ucFunc;
    INT32 (*func)(T_MdbsRtuApp *ptInApp);
    E_ANSWER_STATE EAnswer;
}T_MdbsRtuFnucStruct;

typedef struct
{
    INT32 (*Func)(BYTE ucArrayIndex);
}T_CtrlFuncStruct;

typedef struct
{
    INT32 (*Func)(const T_OneDataAttrStruct *ptInDataAttr,  E_DATA_DIRECTION eInDir, BYTE *pucOutData, BYTE *pucInRsvData);
}T_PacketOrParseFuncStruct;

typedef struct
{
	INT32 (*Func)(const T_OneDataAttrStruct *ptInDataAttr, void *pInData, void *pOutByteBuff);
}T_DataCovFuncStruct;


#ifdef PAKISTAN_CMPAK_PROTOCOL
typedef struct
{
    FLOAT fBusbarVolt;                          // 母排电压
    FLOAT fBattVolt;                            // 电池组电压
    FLOAT fBattCurr;                            // 电池组电流
    WORD  wBattSOC;                             // SOC
    WORD  wBattSOH;                             // SOH
    FLOAT fMaxCellTemp;                         // 电芯最高温度
    FLOAT fMinCellTemp;                         // 电芯最低温度
    WORD  awReserved2[REGADDR_RESERVED_2];      // 保留
    WORD  wModuleMode;                          // 电池状态，例如充电、放电等
    WORD  awReserved3[REGADDR_RESERVED_3];      // 保留
    FLOAT afCellTemp[CELL_TEMP_NUM_MAX];        // 电芯温度
    FLOAT afCellVolt[CELL_VOL_NUM_MAX];         // 电芯电压
    WORD  awReserved4[REGADDR_RESERVED_4];      // 保留
    WORD  wDischgTimesHigh;                     // 累计放电次数高字节
    WORD  wDischgTimesLow;                      // 累计放电次数低字节
    WORD  wTotalDischgCapHigh;                  // 累计放电容量高字节
    WORD  wTotalDischgCapLow;                   // 累计放电容量低字节

    WORD  wCriticalAlarmStatus1;                // 严重告警-1
    WORD  wCriticalAlarmStatus2;                // 严重告警-2
    WORD  wMajorAlarmStatus;                    // 主要告警
    WORD  wMinorAlarmStatus;                    // 次要告警
    WORD  wModuleAlarmStatus;                   // 模块告警
    WORD  wVersion;                             // 软件版本
    WORD  wHardwareVer;                         // 硬件版本
    WORD  wBootLoaderVer;                       // boot版本
    BYTE  ucDeviceType;                         // 设备类型，分离或者集成
    BYTE  ucManufature;                         // 制造商
    WORD  wSubSoftwareID;                       // 子软件ID
    WORD  wBatteryCap;                          // 电池容量
    BYTE  ucCellNum;                            // 单体数量
    WORD  wOutputPower;                         // 输出功率
    FLOAT fExtBusbarVolt;                       // 外部电压

    WORD  wAccuBattRunTimeHigh;                 // 累计运行时间高字节
    WORD  wAccuBattRunTimeLow;                  // 累计运行时间低字节
    FLOAT afCellTempAdd[EXTRAL_CELL_TEMP];      // 额外单体温度17~24
    FLOAT afCellVoltAdd[EXTRAL_CELL_VOLT];      // 额外单体电压17~24

    WORD  wCriticalAlarmStatus3;                // 严重告警-3
    WORD  awRepBattModel[LENGTH_BATTERY_MODEL]; // 电池型号
    WORD  wDischgWHHigh;                        // 累计放电能量高字节
    WORD  wDischgWHLow;                         // 累计放电能量低字节
    WORD  wBattCycTimeHigh;                     // 电池循环次数高字节
    WORD  wBattCycTimeLow;                      // 电池循环次数低字节
}T_CmpakStruct;
#endif


/***********************  函数声明************************/
void MdbsRtuCommMain(T_CommStruct *ptComm);
void Interface_ClearCommRecBuff(T_CommStruct *ptComm);
void    InitMdbsRtu(void);
INT32 ReadOrWriteData(E_DATA_DIRECTION eInDir, BYTE ucInDataType, void *pInAddr, BYTE *pucInOutBuff);
INT32 DealReset(const T_OneDataAttrStruct *ptInDataAttr);
INT32 DealStartChg(const T_OneDataAttrStruct *ptInDataAttr);
INT32 DealStartDischg(const T_OneDataAttrStruct *ptInDataAttr);
INT32S DealOutVolSet(const T_OneDataAttrStruct *ptInDataAttr);
INT32S DealChargeCurr(const T_OneDataAttrStruct *ptInDataAttr);
INT32 DealDefPara(const T_OneDataAttrStruct *ptInDataAttr);
INT32S DealTimeSet(const T_OneDataAttrStruct *ptInDataAttr);
INT32 DealAddrCompete(const T_OneDataAttrStruct *ptInDataAttr);
INT32 DealMdbsRtuCommData(BYTE *pInBuff, WORD wInLen, BYTE ucInNorthType, T_CommStruct* ptComm);
INT32S DealCSUSendCurr(const T_OneDataAttrStruct *ptInDataAttr);
INT32S DealRelaseLock(const T_OneDataAttrStruct *ptInDataAttr);
INT32 DealSendPowerOn(const T_OneDataAttrStruct *ptInDataAttr);
INT32 DealSendPowerOff(const T_OneDataAttrStruct *ptInDataAttr);
INT32S DealManualUnlock(const T_OneDataAttrStruct *ptInDataAttr);
INT32S DealBatteryIndication(const T_OneDataAttrStruct *ptInDataAttr);
INT32S DealClearDcrFaultPrt(const T_OneDataAttrStruct *ptInDataAttr);
INT32S DealClear4GTraffic(const T_OneDataAttrStruct *ptInDataAttr);
INT32S DealMaxSOC(const T_OneDataAttrStruct *ptInDataAttr);
INT32 DealAddrSwitchBroadcast(const T_OneDataAttrStruct *ptInDataAttr);
INT32S DealStartBinding(const T_OneDataAttrStruct *ptInDataAttr);
INT32S DealHeartbeatBinding(const T_OneDataAttrStruct *ptInDataAttr);
INT32S DealCloseBinding(const T_OneDataAttrStruct *ptInDataAttr);
INT32S DealChangeSiteAntitheftKey(const T_OneDataAttrStruct *ptInDataAttr);
INT32S DealNetStartBinding(const T_OneDataAttrStruct *ptInDataAttr);
INT32S DealNetbeatBinding(const T_OneDataAttrStruct *ptInDataAttr);
INT32S DealNetCloseBinding(const T_OneDataAttrStruct *ptInDataAttr);
INT32S DealNetChangeBinding(const T_OneDataAttrStruct *ptInDataAttr);
#ifdef RUN_MODE_CONTROLLED_ENABLED
INT32S DealOutVolSetFree(const T_OneDataAttrStruct *ptInDataAttr);
INT32S DealChargeCurrFree(const T_OneDataAttrStruct *ptInDataAttr);
#endif
#ifdef __cplusplus
}
#endif

#endif  /* CM7_APPLICATIONS_INCLUDE_MDBSRTU_H_ */
