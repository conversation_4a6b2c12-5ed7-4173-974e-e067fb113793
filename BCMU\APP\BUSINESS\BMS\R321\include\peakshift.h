/**************************************************************************
* Copyright (C) 2010-2020, ZTE Corporation.
* 版权信息：（C）2011-2020，中兴通讯股份有限公司动力开发部版权所有
* 系统名称：ZXDUPA-PMSA V2.1平台软件
* 文件名称：peakshift.h
* 文件说明：错峰模块头文件
* 作    者：刘俊
* 版本信息：V1.0
* 设计日期：2022-3-17
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/
#ifndef SOFTWARE_PEAK_SHIFT_H_
#define SOFTWARE_PEAK_SHIFT_H_
#include "common.h"

#ifdef __cplusplus
extern "C" {
#endif


#define ELECTRICITY_PRICE_PEAK   0    //电价峰阶段
#define ELECTRICITY_PRICE_VALLEY 1    //电价谷阶段
#define ELECTRICITY_PRICE_LEVEL  2    //电价平阶段
#define ELECTRICITY_PRICE_NULL   3    //电价为空

#define FILE_NAME_PEAKSHIFTPARA  "/peakshift"  // 错峰参数文件
#define FILE_NAME_PEAKTEMPLATE   "/peakTempla"  // 错峰模板文件
#define NET_PEAKTEMPLATE         "/netpeaktpl"  // 网管传输的错峰模板
#define NET_PEAKTEMPLATE_2       "netpeaktpl"  // 网管传输的错峰模板

#define PEAK_DISCHARGE_NUM    (210)
#define PEAK_SHIFT_PARA_NUM   9
#define REC_DATE_TEMP_DATA_MAXLEN 486
#define TEMPLATE_DURATION_MAX 48
#define WORDTYPE_MAX_VALUE  65535

#define DEFAULT_PEAKSHIFT_DOD              40   //错峰放电深度默认值
#define DEFAULT_POWER_OFF_SHIFT_DELAY      24   //停电错峰延时默认值

/*************错峰参数在文件中的偏移***********/

#define OFFSET_PEAK_SHIFT_ENABLE           0
#define OFFSET_PEAK_SHIFT_DOD              1
#define OFFSET_PEAK_SHIFT_MODE             2
#define OFFSET_DAY_MODE_TEMPLATE_NUM       3
#define OFFSET_WEEK_MODE_TEMPLATE_NUM      4
#define OFFSET_MONTH_MODE_TEMPLATE_NUMU    11
#define OFFSET_HOLIDAY_TEMPLATE            42
#define OFFSET_HOLIDAY_DATE                43
#define OFFSET_POWER_OFF_DELAY             103

/********************************************/

typedef struct 
{
    BYTE ucMonth;
    BYTE ucDay;
}T_HolidayStruct;

typedef struct
{
    UINT32  FromHour : 5; 
    UINT32  FromMinute: 6;     
    UINT32  ToHour : 5; 
    UINT32  ToMinute: 6; 
    UINT32  PriceClass: 10; 
}T_DurationStruct;

typedef struct
{
    BYTE ucDurationNum;   //时间段数量
    T_DurationStruct atDura[48];
}T_DateTemplateStruct;

typedef struct
{
    T_DateTemplateStruct atDateTemp[8];
}T_WeekTemplateStruct;

typedef T_WeekTemplateStruct T_MonthTemplateStruct;

typedef union
{
    T_DateTemplateStruct tDateTemp;
    T_WeekTemplateStruct tWeekTemp;    
    T_MonthTemplateStruct tMonthTemp;
}U_TemplateInfo;

typedef struct
{
    U_TemplateInfo uTemplateInfo;
    BYTE ucDayTemplateNum;  //日模板数量
    WORD           wCRC;
}T_TemplateInfo;   //保存的模板数据

typedef struct
{
    BOOLEAN             bPeakShift;                     //智能错峰使能
    BYTE                ucPeakShiftDOD;                 //错峰放电深度
    BYTE                ucElectricPricePattern;         //电价模式
    BYTE                ucDayPatterTempNum;             //日模式对应模板序号
    BYTE                aucWeekPatternTempNum[7];       //周模式对应模板序号
    BYTE                aucMonthPatternTempNum[31];     //月模式对应模板序号
    BYTE                ucHolidayPatternTempNum;        //节假日模式对应模板序号
    T_HolidayStruct     atHolidayPatternDate[30];       //节假日模式对应日期
    BYTE                ucPoweroffPeakDelay;             //停电错峰延时，范围[0,24]只能取整
    CHAR                acReserve[19];                  //保留位
    WORD                wCRC; 
}T_PeakShiftPara;

/********************* 函数原型定义 ************************/
BOOLEAN GetPeakShiftPara(T_PeakShiftPara* ptPeakShiftPara);
BOOLEAN SetPeakShiftPara(T_PeakShiftPara* ptPeakShiftPara, BYTE ucParaID);
BOOLEAN GetPeakShiftTemplate(T_TemplateInfo* ptPeakShiftTemp);
BOOLEAN SetPeakShiftTemplate(T_TemplateInfo* ptPeakShiftTemp);
INT8S GetElectricityClass(void);
BOOLEAN InitPeakShiftPara( void );
WORD GetValleyTime(void);
BOOLEAN RestorePeakShiftPara(void);
BOOLEAN IsPeakShiftEnabled(void);
BOOLEAN SetPeakShiftParaFromNet(T_PeakShiftPara* ptPeakShiftPara,T_TemplateInfo* ptPeakShiftTemplate); //通过网络设置错峰参数

/**********************模板转发相关函数-start****************/
void TemplateForwardMain(T_CommStruct *ptComm);
BOOLEAN SetPeakShiftAllPara(T_PeakShiftPara* ptPeakShiftPara);
BOOLEAN TempForwardCheck(T_DateTemplateStruct* ptDateTemp, BYTE* p);
BOOLEAN TemplateCompared(T_TemplateInfo* ptPeakShiftTemp);
/**********************模板转发相关函数-end******************/

#ifdef __cplusplus
}
#endif

#endif


