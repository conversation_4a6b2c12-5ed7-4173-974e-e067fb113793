#include "data_type.h"
#include "dev_acmu.h"
#include "realdata_id_in.h"
#include "para_id_in.h"
#include "utils_rtthread_security_func.h"
#include "realdata_save.h"
#include "alarm_id_in.h"
#include "alarm_mgr_api.h"
#include <rtthread.h>
#include <rtdevice.h>
#include "linklayer.h"
#include "para_manage.h"

#define GET_DATA(data_id, dest, factor) \
    do { \
        float f_data = 0.0; \
        get_one_data(data_id, &f_data); \
        dest = (factor) * f_data; \
    } while (0)

Static unsigned char s_comm_fail_time_out_flag = 0;
Static acmu_comm_baudrate_type_e s_last_baudrate = COMM_BAUD_RATE_9600;

int acmu_update_baudrate(void) {
    safe_sid_data_t baudrate = {0};
    signed char ret = 0;
    struct serial_configure def_config = RT_SERIAL_CONFIG_DEFAULT;
    link_inst_t* link_inst = NULL;

    unsigned int baud_tab[COMM_BAUD_RATE_NUM] = {
        1200, 2400, 4800, 9600, 19200, 38400
    };

    ret = get_one_para(ACMU_PARA_ID_SYS_SERIAL_PORT_BAUD_RATE_SETTING_OFFSET + 1, &baudrate.uc_val);
    if (FAILURE == ret || baudrate.uc_val >= COMM_BAUD_RATE_NUM) {
        return FAILURE;
    }

    if (baudrate.uc_val != s_last_baudrate) {
        link_inst = get_link_inst(LINK_ACMU);
        if (link_inst == NULL) {
            return FAILURE;
        }
        def_config.baud_rate = baud_tab[baudrate.uc_val];
        ret = uart_dev_config(link_inst->name, &def_config);
        if (ret != SUCCESSFUL) {
            rt_kprintf("change baudrate from %d to %d failure\n", baud_tab[s_last_baudrate], baud_tab[baudrate.uc_val]);
            return FAILURE;
        }
        rt_kprintf("change baudrate from %d to %d success\n", baud_tab[s_last_baudrate], baud_tab[baudrate.uc_val]);
        s_last_baudrate = baudrate.uc_val;
    }

    return SUCCESSFUL;
}

int acmu_update_host_address(void) {
    safe_sid_data_t address = {0};
    signed char ret = 0;
    const unsigned short max_address = 254;
    unsigned char host_address = 1;

    ret = get_one_para(ACMU_PARA_ID_SYS_LOCAL_MACHINE_ADDRESS_OFFSET, &address.us_val);
    if (FAILURE == ret || address.us_val > max_address) {
        return FAILURE;
    }

    host_address = address.us_val;
    set_host_addr(host_address);
    return SUCCESSFUL;
}

unsigned char is_show_acem_fact(void)
{
    unsigned char fact_get_status = FALSE;

    get_one_data(ACMU_DATA_ID_ACEM_MANUFACTURE_GET_STATUS, &fact_get_status);

    return ((ACEM_NORMAL == get_acem_state())
        && (fact_get_status == TRUE));
}

int get_acem_state(void)
{
    unsigned char acem_comm_status = ACEM_NORMAL;

    get_one_data(ACMU_DATA_ID_ACEM_COMMUNICATION_STATUS, &acem_comm_status); 

    if (get_acem_cfg() == FALSE)
    {
        return ACEM_NO_CONFIG;
    }

    if (acem_comm_status == ACEM_COMM_FAIL)
    {
        return ACEM_COMM_FAIL;
    }

    return ACEM_NORMAL;
}

unsigned char get_acem_cfg(void)
{
    unsigned char acem_config_status = FALSE;

    get_one_para(ACMU_PARA_ID_AC_METER_CONFIGURATION_OFFSET, &acem_config_status);

    return acem_config_status;
}

int updata_acem_fact_info( void )
{    
    short version = 0;
    char acem_gui_version[10];
    char acem_sys_version[10];

    rt_memset_s(&acem_gui_version[0], sizeof(acem_gui_version), 0x0, sizeof(acem_gui_version));
    
    if(is_show_acem_fact()== TRUE )
    {
        get_one_data(ACMU_DATA_ID_ACEM_SYS_VERSION, &acem_sys_version);
        version = acem_sys_version[0]*256+acem_sys_version[1];   //变量到数组
        rt_snprintf_s(acem_gui_version, sizeof(acem_gui_version), "%d",version);
        acem_gui_version[9] = 0;
    }
    else
    {
        rt_memset_s(&acem_gui_version[0], sizeof(acem_gui_version), 0x0, sizeof(acem_gui_version));
    }
    set_one_data(ACMU_DATA_ID_ACEM_VERSION_IN_GUI, acem_gui_version);
    
    return SUCCESSFUL;
}

/**
 * @description: 获取电表数据
 * @param {acem_analog_data_t *} acemShowData
 * @return {int} 
 */
int get_acem_show_data(acem_analog_data_t *acemShowData)
{
    int i = 0;
    float f_data = 0;
    char acem_cfg = FALSE;
    unsigned short output_mutual_type = 600;
    float mutual_factor_5 = 0.0;
    float mutual_factor_50 = 0.0;

    RETURN_VAL_IF_FAIL(acemShowData != NULL, FAILURE);

    get_one_para(ACMU_PARA_ID_AC_METER_CONFIGURATION_OFFSET, &acem_cfg);
    get_one_para(ACMU_PARA_ID_AC_OUTPUT_MUTUAL_INDUCTANCE_TYPE_OFFSET, &output_mutual_type);

    if ((int)acem_cfg == FALSE || get_acem_comm_fail() != ALARMCLASS_MASK) {
        rt_memset_s(acemShowData, sizeof(acem_analog_data_t), 0, sizeof(acem_analog_data_t));
        return FAILURE;
    }

    // Precompute the factors to avoid repeated calculations
    mutual_factor_5 = ((float)output_mutual_type / 5);
    mutual_factor_50 = ((float)output_mutual_type / 50);

    for (i = 0; i < 3; i++) {
        GET_DATA(ACMU_DATA_ID_ACEM_PHASE_VOLT + i, acemShowData->phase_volt[i], 1.0f);
        GET_DATA(ACMU_DATA_ID_ACEM_PHASE_CURR + i, acemShowData->phase_curr[i], mutual_factor_5);
        GET_DATA(ACMU_DATA_ID_ACEM_PHASE_ACT_POWER + i, acemShowData->phase_act_power[i], mutual_factor_5);
        GET_DATA(ACMU_DATA_ID_ACEM_POWER_FACTOR + i, acemShowData->power_factor[i], 1.0f);
    }

    GET_DATA(ACMU_DATA_ID_TOTAL_ACT_POWER, acemShowData->total_act_power, mutual_factor_5);
    GET_DATA(ACMU_DATA_ID_TOTAL_REACT_POWER, acemShowData->total_react_power, mutual_factor_5);
    GET_DATA(ACMU_DATA_ID_TOTAL_APPARENT_POWER, acemShowData->total_apparent_power, mutual_factor_5);
    GET_DATA(ACMU_DATA_ID_TOTAL_POWER_FACTOR, acemShowData->total_power_factor, 1.0f);
    GET_DATA(ACMU_DATA_ID_ACEM_TOTAL_ENERGY, acemShowData->total_energy, mutual_factor_50);
    get_one_data(ACMU_DATA_ID_FREQUENCY, &f_data);
    acemShowData->frequency = round(f_data);

    return SUCCESSFUL;
}

/**
 * @description: 获取电表通讯告警值
 * @return {*}
 */
unsigned char get_acem_comm_fail(void)
{
    char acem_comm_alarm_value = 0;
    int alm_id = GET_ALM_ID(ACMU_ALM_ID_AC_METER_DISCONNECTED,1,1);
    acem_comm_alarm_value = get_realtime_alarm_value(alm_id);
    return acem_comm_alarm_value;
}


unsigned char set_acem_timeout_flag(unsigned char acem_comm)
{
    s_comm_fail_time_out_flag = acem_comm;
    return 0;
}

unsigned char get_acem_timeout_flag(void)
{
    return s_comm_fail_time_out_flag;
}