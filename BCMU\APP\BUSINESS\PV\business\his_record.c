#include <unistd.h>
#include "his_record.h"
#include "storage.h"
#include "his_data.h"
#include "realdata_id_in.h"
#include "hisdata_in.h"
#include "realdata_save.h"
#include "partition_def.h"
#include "sys/stat.h"
#include "utils_data_transmission.h"
#include <fal.h>
#include "alm_his_data.h"
#include "his_record_test.h"
#include "reset.h"
#include "realdata_id_tab.h"
#include "control_tab_in.h"
#include "msg_id.h"
#include "sdram_memheap.h"
#include "utils_rtthread_security_func.h"
#include "dc_ac_handle.h"
#include "alarm_manage.h"
#include "ee_public_info.h"
#include "thread_id.h"
#include "utils_heart_beat.h"
#include "base64.h"
#include "utils_encrypt_gcm.h"
#include "utils_encrypt_ecb.h"
#include "aes_interface.h"
#include "para_manage.h"

extern reset_reason_t g_reset_reason_table[];
char g_client_key[CLIENT_KEY_DATA_LEN]RAM_SECTION_BSS = {0};
char g_client_other_key[CLIENT_KEY_DATA_LEN]RAM_SECTION_BSS = {0};
static int s_bit_num = 0;
static unsigned char s_save_iv_index = 0;
static unsigned short g_psc_final_type = INVALID_DEV_TYPE;
static unsigned char s_buff[1000] RAM_SECTION_BSS = {0};
static unsigned char s_fault_pad_buff[7 + RF_HEAD_SIZE + 2 * OBSER_NUM_PAD + (BEFORE_NUM_PAD + AFTER_NUM_PAD) * (2 + 2*OBSER_NUM_PAD)] RAM_SECTION_BSS = {0};
static his_record_t s_his_record[] = {
    {  // 历史告警
        .his_record_info = {0},
        .mix = FALSE,
        .record_size = sizeof(his_alm_save_t),
        .data = NULL,
        .part_name = HIS_ALARM_FILE,
        .temp_part_name = HIS_ALARM_FILE,
        .min_save_num = MIN_HIS_ALARM_NUM,
        .max_save_num = MAX_HIS_ALARM_NUM,
        .save_num_per_file = MAX_HIS_ALARM_NUM_PER_FILE,
        .one_sect_num = 0,
        .file_num = HIS_ALARM_FILE_NUM,
        .file_name = {HIS_ALARM_FILE_0, HIS_ALARM_FILE_1, HIS_ALARM_FILE_2, HIS_ALARM_FILE_3, HIS_ALARM_FILE_4, HIS_ALARM_FILE_5, HIS_ALARM_FILE_6, },
    }, 
    {  //历史数据
        .his_record_info = {0},
        .mix = FALSE,
        // .record_size = sizeof(time_base_t) + HISDATA_LEN + 3,     // 时间头 + 序号 + data_len + crc(2字节)
        .record_size = 0,
        .data = NULL,
        .part_name = HIS_DATA_FILE,
        .temp_part_name = HIS_DATA_FILE,
        .min_save_num = MIN_HIS_DATA_NUM,
        .max_save_num = MAX_HIS_DATA_NUM,
        .save_num_per_file = MAX_HIS_DATA_NUM_PER_FILE,
        .one_sect_num = 0,
        .file_num = HIS_DATA_FILE_NUM,
        .file_name = {HIS_DATA_FILE_0, HIS_DATA_FILE_1, HIS_DATA_FILE_2, HIS_DATA_FILE_3, HIS_DATA_FILE_4, HIS_DATA_FILE_5, HIS_DATA_FILE_6, },
    },
    {  // 实时告警
        .his_record_info = {0},
        .mix = FALSE,
        .record_size = sizeof(real_alm_record_t),
        .data = NULL,
        .part_name = REAL_ALARM_PART,
        .temp_part_name = REAL_ALARM_PART,
        .min_save_num = MIN_REAL_ALARM_NUM,
        .max_save_num = MAX_REAL_ALARM_NUM,
        .save_num_per_file = MAX_REAL_ALARM_NUM_PER_FILE,
        .one_sect_num = 0,
        .file_num = 0,
        .file_name = {0},
    },
    {  //历史事件
        .his_record_info = {0},
        .mix = FALSE,
        .record_size = sizeof(event_record_t),
        .data = NULL,
        .part_name = HIS_EVENT_FILE,
        .temp_part_name = NULL,
        .min_save_num = MIN_HIS_EVENT_NUM,
        .max_save_num = MAX_HIS_EVENT_NUM,
        .save_num_per_file = MAX_HIS_EVENT_NUM_PER_FILE,
        .one_sect_num = 0,
        .file_num = HIS_EVENT_FILE_NUM,
        .file_name = {HIS_EVENT_FILE_0, HIS_EVENT_FILE_1, HIS_EVENT_FILE_2, HIS_EVENT_FILE_3, HIS_EVENT_FILE_4, HIS_EVENT_FILE_5, HIS_EVENT_FILE_6, },
    },
    {  //录波数据
        .his_record_info = {0},
        .mix = FALSE,
        .record_size = 0,    // time_base + trig_id + 数据，由于采样的数据与头文件参数有关，record_size是动态的
        .data = NULL,
        .part_name = RECORD_FAULT_DATA,
        .temp_part_name = NULL,
        .min_save_num = MIN_RECORD_FAULT_NUM,
        .max_save_num = MAX_RECORD_FAULT_NUM,
        .save_num_per_file = MAX_RECORD_FAULT_NUM_PER_FILE,
        .one_sect_num = 0,
        .file_num = RECORD_FAULT_FILE_NUM,
        .file_name = {RECORD_FAULT_FILE_0, RECORD_FAULT_FILE_1, RECORD_FAULT_FILE_2, RECORD_FAULT_FILE_3, RECORD_FAULT_FILE_4, RECORD_FAULT_FILE_5, RECORD_FAULT_FILE_6, RECORD_FAULT_FILE_7, RECORD_FAULT_FILE_8 ,RECORD_FAULT_FILE_9},
    }
};

static alm_his_data_config_t s_alm_his_data_config = {
    .queue_data_max = 5,
    .data_num_bf_alm = 5,
    .data_num_af_alm = 4,
    .frequency = 5,
    .thread_period = 1000,
    .his_record_len = sizeof(time_base_t) + HISDATA_LEN + 3,     // 时间头 + 序号 + data_len + crc
    .make_his_data = save_sample_data_to_his_data_with_time_stamp,
    .save_his_data = save_his_data_had_time_stamp,
    .if_filter = FALSE,
    .timestamp_len = sizeof(time_base_t),
};

static char para_file_list[][UPDATE_FILE_EXTEND_NAME_LEN] = {ALMPARA_FILE_NAME, NUMPARA_FILE_NAME, STRPARA_FILE_NAME,};
#define PARA_FILE_LIST_LEN sizeof(para_file_list)/sizeof(para_file_list[0])

/*
* @brief: 初始化历史数据存储
* @note:  含历史数据、历史告警、事件记录
*/
void del_his_data_when_update()
{
    // unlink(HIS_DATA_FILE_0);
    // unlink(HIS_DATA_FILE_1);
    // unlink(HIS_DATA_FILE_2);
    // unlink(HIS_DATA_FILE_3);
    // unlink(HIS_DATA_FILE_4);
    // unlink(HIS_DATA_FILE_5);
    // unlink(HIS_RECORD_INFO_FILE);
    // LOG_E("del file and reboot when update");
    rt_thread_mdelay(5000);
    rt_hw_cpu_reset();
}

/**
 * @brief 清除历史告警
 * @retval SUCCESSFUL 成功, FAILURE 失败
*/
short clean_his_alarm(void) {
    // 清记录文件
    storage_unlink(HIS_ALARM_FILE_0);
    storage_unlink(HIS_ALARM_FILE_1);
    storage_unlink(HIS_ALARM_FILE_2);
    storage_unlink(HIS_ALARM_FILE_3);
    storage_unlink(HIS_ALARM_FILE_4);
    storage_unlink(HIS_ALARM_FILE_5);
    storage_unlink(HIS_ALARM_FILE_6);

    // 清记录信息
    s_his_record[HIS_ALARM_REC_INDEX].his_record_info.saved_num = 0;
    s_his_record[HIS_ALARM_REC_INDEX].his_record_info.temp_saved_num = 0;
    return SUCCESSFUL;
}
/**
 * @brief 清除历史事件
 * @retval SUCCESSFUL 成功, FAILURE 失败
*/
short clean_his_event(void) {
    // 清记录文件
    storage_unlink(HIS_EVENT_FILE_0);
    storage_unlink(HIS_EVENT_FILE_1);
    storage_unlink(HIS_EVENT_FILE_2);
    storage_unlink(HIS_EVENT_FILE_3);
    storage_unlink(HIS_EVENT_FILE_4);
    storage_unlink(HIS_EVENT_FILE_5);
    storage_unlink(HIS_EVENT_FILE_6);

    // 清记录信息
    s_his_record[EVENT_REC_INDEX].his_record_info.saved_num = 0;
    s_his_record[EVENT_REC_INDEX].his_record_info.temp_saved_num = 0;
    save_real_ctrl_cmd_event(DAC_CTRL_ID_CLEAN_HIS_EVENT, CTRL_ID_TYPE, "clean his_event");//保存操作记录
    return SUCCESSFUL;
}
/**
 * @brief 清除历史数据
 * @retval SUCCESSFUL 成功, FAILURE 失败
*/
short clean_his_data(void) {
    // 清记录文件
    storage_unlink(HIS_DATA_FILE_0);
    storage_unlink(HIS_DATA_FILE_1);
    storage_unlink(HIS_DATA_FILE_2);
    storage_unlink(HIS_DATA_FILE_3);
    storage_unlink(HIS_DATA_FILE_4);
    storage_unlink(HIS_DATA_FILE_5);
    storage_unlink(HIS_DATA_FILE_6);
    //清除录波文件
    storage_unlink(RECORD_FAULT_FILE_0);
    storage_unlink(RECORD_FAULT_FILE_1);
    storage_unlink(RECORD_FAULT_FILE_2);
    storage_unlink(RECORD_FAULT_FILE_3);
    storage_unlink(RECORD_FAULT_FILE_4);
    storage_unlink(RECORD_FAULT_FILE_5);
    storage_unlink(RECORD_FAULT_FILE_6);
    storage_unlink(RECORD_FAULT_FILE_7);
    storage_unlink(RECORD_FAULT_FILE_8);
    storage_unlink(RECORD_FAULT_FILE_9);
    //清除电量文件
    storage_unlink(YEAR_FILE);
    storage_unlink(MONTH_FILE);
    storage_unlink(DAY_FILE);
    storage_unlink(HOUR_FILE);
    storage_unlink(HIS_FILE);

    // 清记录信息
    s_his_record[HIS_DATA_REC_INDEX].his_record_info.saved_num = 0;
    s_his_record[HIS_DATA_REC_INDEX].his_record_info.temp_saved_num = 0;
    return SUCCESSFUL;
}
int init_dir(void)
{
    mkdir(HIS_DATA_DIR, 0);
    mkdir(HIS_ALARM_DIR, 0);
    mkdir(HIS_EVENT_DIR, 0);
    mkdir(POWER_REC_DIR, 0);
    return RT_EOK;
}

int get_his_data_saved_num(char* file_name, unsigned int file_size, unsigned char* read_his_data, unsigned int read_data_len)
{
    unsigned int loop_size = 0;
    unsigned int loop = 0;
    his_data_map_t his_data_map = {0};
    unsigned short crc = 0;
    unsigned short calc_crc = 0;
    unsigned short data_len = 0;
    unsigned char his_data_number = 0;
    unsigned short his_data_saved_num = 0;
    unsigned int total_len = 0;
    unsigned int protect_cnt1 = 0;
    unsigned int protect_cnt2 = 0;
    rt_memset_s(&his_data_map, sizeof(his_data_map_t), 0, sizeof(his_data_map_t));
    handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char*)&his_data_map, sizeof(his_data_map), HIS_DATA_MAP_OFFSET);
    while(loop_size < file_size && protect_cnt1 < 4000)
    {
        protect_cnt1 ++;
        rt_memset_s(read_his_data, read_data_len, 0, read_data_len);
        handle_storage(read_opr, file_name, read_his_data, read_data_len, loop_size);   // 三条
        // LOG_E("%s:%d|file:%s, size:%d, saved_num:%d", __FUNCTION__, __LINE__, file_name, loop_size, his_data_saved_num);
        // rt_kprintf("file:%s, file_size:%d, loop_size:%d, saved_num:%d\n", file_name, file_size, loop_size, his_data_saved_num);
        while(loop < read_data_len && protect_cnt2 < 100)
        {
            protect_cnt2 ++;
            his_data_number = read_his_data[loop + 7];
            data_len = his_data_map.number_his_data_len_map[his_data_number];
            crc = (read_his_data[loop + data_len - 1] << 8) + read_his_data[loop + data_len - 2];
            calc_crc = crc_cal(read_his_data + loop, data_len - 2);
            // rt_kprintf("get_save_num|loop:%d, crc:0x%x, calc_crc:0x%x, data_len:%d\n", loop, crc, calc_crc, data_len);
            if(crc == calc_crc && crc != 0)
            {
                his_data_saved_num ++;
                total_len += data_len;
                loop += data_len;
                // LOG_E("get_save_num|saved_num:%d, total_len:%d\n", his_data_saved_num, total_len);
                continue;
            }
            loop ++;
        }
        protect_cnt2 = 0;
        loop_size = total_len;
        loop = 0;
    }
    LOG_E("%s:%d|file_name:%s, size:%d, his_data_saved_num:%d", __FUNCTION__, __LINE__, file_name, file_size, his_data_saved_num);
    return his_data_saved_num;
}

int check_his_data_file()
{
    int i = 0;
    part_data_t part_data = {0};
    struct stat file_stat = {0};
    unsigned int saved_num = 0;
    unsigned int total_saved_num = 0;
    his_record_t* his_data_record = &s_his_record[HIS_DATA_REC_INDEX];
    unsigned char* read_his_data = (unsigned char*)RAM_MALLOC(900);
    if(NULL == read_his_data)
    {
        LOG_E("%s:%d|malloc fail", __FUNCTION__, __LINE__);
        return FAILURE;
    }
    for(i = 0; i < his_data_record->file_num; i++)
    {
        // 判断文件在不在
        rt_memset(&part_data, 0x00, sizeof(part_data));
        rt_memset(&file_stat, 0x00, sizeof(file_stat));
        rt_snprintf_s(part_data.name, sizeof(part_data.name),"%s", his_data_record->file_name[i]);
        if(FAILURE == storage_stat(part_data.name, &file_stat))
        {
            continue;
        }

        saved_num = get_his_data_saved_num(his_data_record->file_name[i], file_stat.st_size, read_his_data, 900);
        if(saved_num == FAILURE)
        {
            LOG_E("%s:%d|file_name:%s error", __FUNCTION__, __LINE__, his_data_record->file_name[i]);
            // 需要删除文件？？
            RAM_FREE(read_his_data);
            return FAILURE;
        }
        total_saved_num += saved_num;
    }
    his_data_record->his_record_info.saved_num = total_saved_num;
    his_data_record->record_size = HISDATA_LEN + 10;
    RAM_FREE(read_his_data);
    LOG_E("%s:%d|total_saved_num:%d", __FUNCTION__, __LINE__, total_saved_num);
    return SUCCESSFUL;
}
unsigned char  is_his_record_exist(void)
{
    part_data_t part_data = {0};
    struct stat file_stat = {0};
    his_record_t* his_data_record = NULL;
     for(int j = 0;j <= WAVE_LD_REC_INDEX ; j++)
     {
        his_data_record = &s_his_record[j];
        for(int i = 0; i < his_data_record->file_num; i++)
        {
            // 判断文件在不在
            rt_memset(&part_data, 0x00, sizeof(part_data));
            rt_memset(&file_stat, 0x00, sizeof(file_stat));
            rt_snprintf_s(part_data.name, sizeof(part_data.name),"%s", his_data_record->file_name[i]);
            if(FAILURE == storage_stat(part_data.name, &file_stat))
            {
                continue;
            }
            return TRUE;
        }
     }
    return FALSE;
}
unsigned char is_energe_record_exist(void)
{
    struct stat file_stat = {0};
    int checksize = 64 ;
    const char *penergefile_flash[] = {YEAR_FILE, YEAR_FILE_TMP,  MONTH_FILE,     MONTH_FILE_TMP,\
                                       DAY_FILE ,  DAY_FILE_TMP,  HOUR_FILE,      HOUR_FILE_TMP ,\
                                       HIS_FILE ,  HIS_BAK_FILE,  ACT_POWER_FILE, HIS_RECORD_INFO_FILE, IV_DATA_FILE};
    const char *penergepart_ee[] = {ENERGY_PART , EE_UPDATE_INFO, EE_ENERGY_CYCLE, EE_REAL_ALARM, EE_PARALLEL_ALARM, EE_PUBLIC_INFO}; 

    int count_num = sizeof(penergefile_flash)/sizeof(penergefile_flash[0]) ;                    
    for(int i = 0 ; i < count_num ; i++)
    {
        if(FAILURE == storage_stat(penergefile_flash[i], &file_stat))
        {
            continue;
        }
        return TRUE;
    }

    unsigned char *ee_data = (unsigned char*)rt_malloc(checksize);
    if(ee_data == NULL)
    {
        return TRUE;
    }
    unsigned char *ee_checkdata = (unsigned char*)rt_malloc(checksize);
    if(ee_checkdata == NULL)
    {
        free(ee_data);
        return TRUE;
    }
    rt_memset_s(ee_checkdata , checksize , 0XFF , checksize);
    rt_memset_s(ee_data , checksize , 0 , checksize);
    count_num = sizeof(penergepart_ee)/sizeof(penergepart_ee[0]) ;   
    for(int j = 0 ; j < count_num - 1  ; j ++)
    {
       handle_storage(read_opr, penergepart_ee[j] , ee_data , checksize , 0);
       if(rt_memcmp(ee_data , ee_checkdata , checksize) ==0)
       {
         rt_memset_s(ee_data , checksize , 0 , checksize);
         continue;
       }
        rt_free(ee_data);
        rt_free(ee_checkdata);
        return TRUE;
    }
    handle_storage(read_opr, penergepart_ee[count_num - 1] , ee_data , checksize , DEV_STATUS_OFFSET);//最后的EE_PUBLIC_INFO保留了一部分数据
    if(rt_memcmp(ee_data , ee_checkdata , checksize) != 0)
    {
        rt_free(ee_data);
        rt_free(ee_checkdata);
        return TRUE;
    }
    rt_free(ee_data);
    rt_free(ee_checkdata);
    return FALSE;
}
int pack_hisdata_config_data(unsigned char** config_data, unsigned int* config_data_len)
{
    unsigned int offset = 0;
    int loop = 0;
    int id_loop = 0;
    *config_data = (unsigned char*)RAM_MALLOC(HIS_DATA_CONFIG_FILE_LEN);  
    if(NULL == *config_data)
    {
        LOG_E("%s,%d|malloc fail", __FUNCTION__, __LINE__);
        return FAILURE;
    }
    for(loop = 0; loop < sizeof(realdata_info_tab) / sizeof(realdata_info_t); loop ++)
    {
        for(id_loop = 0; id_loop < realdata_info_tab[loop].id_num; id_loop ++)
        {
            if(offset > HIS_DATA_CONFIG_FILE_LEN)
            {
                //printf("save_hisdata_config_file|memory smaller, offset:%d, num:%d\n", offset, num);
                rt_free(*config_data);
                return FAILURE;
            }
            put_uint16_to_buff(*config_data + HIS_DATA_CONFIG_HEAD_LEN + offset, realdata_info_tab[loop].realdata_id + id_loop);
            offset += 2;
        }
    }

    *config_data_len = HIS_DATA_CONFIG_HEAD_LEN + offset + 2;   // head + data + crc
    return SUCCESSFUL;
}

int find_hisdata_config_data(unsigned int file_size, unsigned char* config_data, unsigned int new_config_data_len, unsigned char* config_data_number)
{
    int loop = 0;
    unsigned int config_data_len = 0;
    unsigned char* read_config_data = (unsigned char*)RAM_MALLOC(file_size);  
    if(NULL == read_config_data)
    {
        LOG_E("%s:%d|size:%d, malloc fail", __FUNCTION__, __LINE__, file_size);
        return FAILURE;
    }
    
    handle_storage(read_opr, HISDATA_CONFIG_FILE, read_config_data, file_size, 0);

    while(loop < file_size)
    {
        if(read_config_data[loop] == 0x7E && read_config_data[loop + 1] == 0x0D)
        {
            config_data_len = (read_config_data[loop + 3] << 8) + read_config_data[loop + 4];
            *config_data_number = read_config_data[loop + 2]; 
            if(new_config_data_len == config_data_len)
            {
                if(0 == rt_memcmp(read_config_data + loop + HIS_DATA_CONFIG_HEAD_LEN, config_data + HIS_DATA_CONFIG_HEAD_LEN, config_data_len))
                {
                    RAM_FREE(read_config_data);
                    LOG_E("%s:%d|find config file", __FUNCTION__, __LINE__);
                    return SUCCESSFUL;
                }
            }
            
        }
        loop ++;
    }
    RAM_FREE(read_config_data);
    LOG_E("%s:%d|not find config file|number:%d", __FUNCTION__, __LINE__, *config_data_number);
    return FAILURE;
}

//保存历史数据说明文件
int save_hisdata_config_file()
{
    unsigned char config_data_number = 0;
    struct stat stat_buff = {0};
    unsigned char* config_data = NULL;
    unsigned int config_data_len = 0;
    unsigned short crc = 0;
    his_data_map_t his_data_map = {0};
    if(FAILURE == pack_hisdata_config_data(&config_data, &config_data_len))
    {
        return FAILURE;
    }

    config_data[0] = 0x7E;
    config_data[1] = 0x0D;  //帧头
    config_data[2] = 0;     //序号
    put_uint16_to_buff(config_data + 3, config_data_len - HIS_DATA_CONFIG_HEAD_LEN -2);  //data_len
    crc = crc_cal(config_data, config_data_len - 2);
    put_uint16_to_buff(config_data + config_data_len - 2, crc);

    if(0 != storage_stat(HISDATA_CONFIG_FILE, &stat_buff))
    {
        // 直接把说明文件写入文件系统中
        handle_storage(write_opr, HISDATA_CONFIG_FILE, config_data, config_data_len, 0);
        RAM_FREE(config_data);

        // 记录说明文件的序号与data_len的映射关系，数据文件的序号与data_len的映射关系
        rt_memset_s(&his_data_map, sizeof(his_data_map), 0xFF, sizeof(his_data_map));

        his_data_map.number_config_data_len_map[0] = config_data_len;
        his_data_map.number_his_data_len_map[0] = HISDATA_LEN + 10;

        handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&his_data_map, sizeof(his_data_map), HIS_DATA_MAP_OFFSET);

        set_his_data_number(0);
        LOG_E("%s:%d|config file not exist|write new file", __FUNCTION__, __LINE__);
        return SUCCESSFUL;
    }

    if(SUCCESSFUL == find_hisdata_config_data(stat_buff.st_size, config_data, config_data_len - HIS_DATA_CONFIG_HEAD_LEN -2, &config_data_number))
    {
        RAM_FREE(config_data);
        LOG_E("%s:%d|config file exist|not update", __FUNCTION__, __LINE__);
        set_his_data_number(config_data_number);
        return SUCCESSFUL;   // 在文件系统找到了，可以不用存
    }

    // 在文件系统搜索不到当前的，需要将数据写入到文件系统
    config_data[2] = config_data_number + 1;
    crc = crc_cal(config_data, config_data_len - 2);
    put_uint16_to_buff(config_data + config_data_len - 2, crc);
    if(SUCCESSFUL != handle_storage(write_opr, HISDATA_CONFIG_FILE, config_data, config_data_len, stat_buff.st_size))
    {
        RAM_FREE(config_data);
        return FAILURE;
    }
    RAM_FREE(config_data);

    // 记录说明文件的序号与data_len的映射关系，数据文件的序号与data_len的映射关系
    rt_memset_s(&his_data_map, sizeof(his_data_map_t), 0, sizeof(his_data_map_t));
    handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char*)&his_data_map, sizeof(his_data_map), HIS_DATA_MAP_OFFSET);
    his_data_map.number_config_data_len_map[config_data_number + 1] = config_data_len;
    his_data_map.number_his_data_len_map[config_data_number + 1] = HISDATA_LEN + 10;
    handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&his_data_map, sizeof(his_data_map), HIS_DATA_MAP_OFFSET);

    set_his_data_number(config_data_number + 1);
    LOG_E("config file exist|need update|number:%d", config_data_number + 1);
    return SUCCESSFUL;
}

int init_his_data_info()
{
    save_his_record_info_t his_record_info = {0};
    handle_storage(read_opr, HIS_RECORD_INFO_FILE, (unsigned char*)&his_record_info, sizeof(save_his_record_info_t), 0);
    s_his_record[HIS_DATA_REC_INDEX].his_record_info.saved_num = his_record_info.saved_num;
    s_his_record[HIS_DATA_REC_INDEX].record_size = HISDATA_LEN + 10;
    LOG_E("his_data|saved_num:%d, record_size:%d", his_record_info.saved_num, HISDATA_LEN + 10);
    return SUCCESSFUL;
}
short fill_event_record_callback(event_record_cb_param_t *event_record_cb_param)
{
    event_record_t save_event_info = {0};

    RETURN_VAL_IF_FAIL(event_record_cb_param != NULL, FAILURE);

    rt_memset_s(&save_event_info, sizeof(save_event_info), 0, sizeof(save_event_info));
    save_event_info.type = event_record_cb_param->type;  
    rt_memcpy_s(save_event_info.info, sizeof(save_event_info.info), event_record_cb_param->info, event_record_cb_param->info_len);  
    put_int16_to_buff((unsigned char*)&save_event_info.event_id , event_record_cb_param->event_id);  

    pub_msg_to_thread(SAVE_EVENT_MSG, &save_event_info, sizeof(save_event_info));
    return SUCCESSFUL;
}

void init_his_record(void) {
    register_fill_event_record_cb(fill_event_record_callback);
    register_his_record_tab(s_his_record, sizeof(s_his_record)/sizeof(s_his_record[0]));  //注册历史记录的信息到s_his_record中
    save_hisdata_config_file();     //保存历史数据说明文件到文件系统中
    init_his_record_from_filesys(); //从文件系统中读取saved_num
    // check_his_data_file();          // 初始化历史数据的saved_num
    init_his_data_info();
    set_alm_his_data_config(&s_alm_his_data_config);
    check_all_record_fault_file(); // 检查故障录波文件，并初始化saved_num
}

void init_test_his_record(void)
{
    register_test_his_record_tab(s_his_record, save_sample_data_to_his_data);
}

int save_pv_his_data()
{
    unsigned char buff[HISDATA_LEN] = {};
    rt_memset_s(buff, HISDATA_LEN, 0, HISDATA_LEN);
    if(SUCCESSFUL != save_sample_data_to_his_data(buff))
    {
        return FAILURE;
    }
    if(SUCCESSFUL != save_his_data(buff)){
        return FAILURE;
    }

    return SUCCESSFUL;
}

void pack_int32u_data(unsigned char* buff, unsigned int* offset, char precision, void* data)
{
    unsigned int ui_data = 0;
    if(0 != precision)
    {
        ui_data = (unsigned int)(*(float*)data * pow(10, precision));
    }
    else
    {
        ui_data = *(unsigned int*)data;
    }
    
    put_uint32_to_buff(buff + *offset, ui_data);
    *offset += sizeof(unsigned int);
}

void pack_int32s_data(unsigned char* buff, unsigned int* offset, char precision, void* data)
{
    int ui_data = 0;
    if(0 != precision)
    {
        ui_data = (int)(*(float*)data * pow(10, precision));
    }
    else
    {
        ui_data = *(int*)data;
    }
    
    put_int32_to_buff(buff + *offset, ui_data);
    *offset += sizeof(int);
}

void pack_int16u_data(unsigned char* buff, unsigned int* offset, char precision, void* data)
{
    unsigned short si_data = 0;
    if(0 != precision)
    {
        si_data = (unsigned short)(*(float*)data * pow(10, precision));
    }
    else
    {
        si_data = (unsigned short)(*(unsigned int*)data);
    }
    
    put_uint16_to_buff(buff + *offset, si_data);
    *offset += sizeof(unsigned short);
}

void pack_int16s_data(unsigned char* buff, unsigned int* offset, char precision, void* data)
{
    short si_data = 0;
    if(0 != precision)
    {
        si_data = (short)(*(float*)data * pow(10, precision));
    }
    else
    {
        si_data = (short)(*(unsigned int*)data);
    }
    
    put_int16_to_buff(buff + *offset, si_data);
    *offset += sizeof(short);
}

void pack_int8u_data(unsigned char* buff, unsigned int* offset, char precision, void* data)
{
    if(0 != precision)
    {
        *(buff + *offset) = (unsigned char)(*(float*)data * pow(10, precision));
    }
    else
    {
        *(buff + *offset) = (unsigned char)(*(unsigned int*)data);
    }

    *offset += sizeof(unsigned char);
}

void pack_bit_data(unsigned char* buff, unsigned int* offset, char precision, void* data)
{
    s_bit_num ++;
    *(buff + *offset) += (*(unsigned int*)data & 0x01) << (8 - s_bit_num);
    if(s_bit_num == 8)
    {
        s_bit_num = s_bit_num % 8;
        *offset += 1;
    }
}

his_data_pack_t his_data_pack[] = 
{
    {1, TYPE_INT32U, pack_int32u_data},
    {1, TYPE_INT32S, pack_int32s_data},
    {1, TYPE_INT16U, pack_int16u_data},
    {1, TYPE_INT16S, pack_int16s_data},
    {1, TYPE_INT8U,  pack_int8u_data},
    {0, TYPE_INT32U, pack_int32u_data},
    {0, TYPE_INT16U, pack_int16u_data},
    {0, TYPE_INT8U,  pack_int8u_data},
    {0, TYPE_BIT,    pack_bit_data},
};

unsigned char find_pack_tab_index(unsigned char precision, unsigned char storage_type)
{
    int tab_num = sizeof(his_data_pack) / sizeof(his_data_pack_t);
    char precision_flag = 0;
    int loop = 0;
    if(precision != 0)
    {
        precision_flag = 1;
    }
    for(loop = 0; loop < tab_num; loop ++)
    {
        if(his_data_pack[loop].storage_type == storage_type && his_data_pack[loop].precision_flag == precision_flag)
        {
            return loop;
        }
        
    }
    return 0xFF;
}


int save_sample_data_to_his_data(unsigned char* buff)
{
    unsigned int offset = 0;
    int loop = 0;
    int id_loop = 0;
    unsigned char index = 0;
    s_bit_num = 0;
    unsigned char data[4] = {};

    for(loop = 0; loop < sizeof(realdata_info_tab) / sizeof(realdata_info_t); loop ++)
    {
        for(id_loop = 0; id_loop < realdata_info_tab[loop].id_num; id_loop ++)
        {
            rt_memset_s(data, sizeof(data), 0, sizeof(data));
            get_one_data(realdata_info_tab[loop].realdata_id_offset + id_loop, (void*)data);
            index = find_pack_tab_index(realdata_info_tab[loop].precision, realdata_info_tab[loop].storage_type);
            if(index == 0xFF)
            {
                return FAILURE;
            }
            his_data_pack[index].pack_data(buff, &offset, realdata_info_tab[loop].precision, data);
        } 
    }
    if(s_bit_num != 0)    //当bit型数据不满足8个，也按1字节计算
    {
        offset += 1;
    }
    if(offset != HISDATA_LEN)
    {
        return FAILURE;
    }
    return SUCCESSFUL;
}

/**
 * @brief 将采样数据打包成历史数据并附加时间戳
 * @param[out] buff 历史数据存储区
 * @retval SUCCESSFUL 成功， FAILURE 失败
*/
int save_sample_data_to_his_data_with_time_stamp(unsigned char* buff) {
    int rtn = 0;
    time_base_t stamp = {0};
    unsigned short time_head_len = sizeof(time_base_t);
    RETURN_VAL_IF_FAIL(buff != NULL, FAILURE);
    rtn = save_sample_data_to_his_data(buff);
    RETURN_VAL_IF_FAIL(SUCCESSFUL == rtn, FAILURE);

    // 将历史数据后移, 空出放时间戳的空间
    rt_memcpy_s(&buff[time_head_len + 1], HISDATA_LEN, buff, HISDATA_LEN);
    // 放置时间戳
    get_time(&stamp);
    put_time_to_buff(buff, stamp);
    buff[time_head_len] = get_his_data_number();


    return SUCCESSFUL;
}

int parse_iv_data(unsigned char* buff, unsigned char* iv_data)
{

    RETURN_VAL_IF_FAIL(buff != NULL && iv_data != NULL, FAILURE);
    time_t save_time = {0};//保存时间
    static unsigned char last_block_id = 0;
    unsigned char block_id = 0;//PV序号
    unsigned char precision = 0;
    unsigned short para_num = 0;
    unsigned short para_len = 0;

    //获取数据
    save_time = time(RT_NULL);
    block_id = buff[0];
    if(last_block_id == block_id)
    {
        LOG_E("same iv block id | block_id:%d",block_id);
        return FAILURE;
    }
    last_block_id = block_id;
    if(block_id % 2 == 0)
    {
        precision = 2;//电流精度
    }
    else
    {
        precision = 1;//电压精度
    }
    para_num = get_uint16_data(&buff[IV_PARA_NUM_INDEX]);
    para_len = para_num * 2;
    //组合数据
    put_time_t_to_buff(iv_data, save_time);
    iv_data[SAVE_IV_BLOCK_ID_INDEX] = block_id;
    iv_data[SAVE_IV_PRECISION_INDEX] = precision;
    put_uint16_to_buff(&iv_data[SAVE_IV_DATA_NUM_INDEX], para_num);

    rt_memcpy_s(&iv_data[SAVE_IV_DATA_INDEX], para_len, &buff[IV_DATA_INDEX], para_len);//PV电压或者pv电流数据
    return SUCCESSFUL; 
}

int save_iv_data(unsigned char* buff)//传过来的buff为一块的数据（PV电压：（3+128*2）或者 PV电流（3+128*2） 存文件格式：时间为(7字节)+PV序号(1字节) + 精度(1字节) + 参数个数(2字节) + PV数据（参数个数 * 2字节）
{
    RETURN_VAL_IF_FAIL(buff != NULL, FAILURE);
    unsigned char save_pv_num = 0;
    unsigned short pv_num = 0;
    part_data_t part_data = {0};
    struct stat stat_buff = {0};
    unsigned char iv_complete_status = 0;
    unsigned short para_num = get_uint16_data(&buff[IV_PARA_NUM_INDEX]);
    unsigned short iv_data_len = para_num * 2 + SAVE_IV_DATA_INDEX;
    unsigned char* iv_data = rt_sdram_malloc(SAVE_IV_DATA_MAX_LEN);
    RETURN_VAL_IF_FAIL(iv_data != NULL, FAILURE);
    rt_memset_s(iv_data, SAVE_IV_DATA_MAX_LEN, 0, SAVE_IV_DATA_MAX_LEN);
    //iv数据解包
    if(parse_iv_data(buff, iv_data) == FAILURE)
    {
        rt_sdram_free(iv_data);
        iv_data = NULL;
        return FAILURE;
    }
    //存储iv数据
    if(storage_stat(IV_DATA_FILE,&stat_buff) != SUCCESSFUL)
    {
        stat_buff.st_size = 0;
    }
    part_data.buff = iv_data;
    part_data.len = iv_data_len;
    part_data.offset = stat_buff.st_size;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), IV_DATA_FILE);
    if(SUCCESSFUL != storage_process(&part_data, write_opr))
    {
        rt_sdram_free(iv_data);
        iv_data = NULL;
        return FAILURE;
    }

    //判断数据是否全部收完
    s_save_iv_index++;
    save_pv_num = s_save_iv_index / 2;
    get_one_data(DAC_DATA_ID_PV_NET_NUMBER,&pv_num);
    if(save_pv_num == pv_num)
    {
        iv_complete_status = 1;
        s_save_iv_index = 0;
        set_one_data(DAC_DATA_ID_IV_COMPLETE_STATUS, &iv_complete_status);
        send_msg_to_thread(MQTT_REAL_UP_MSG, MOD_MQTT, NULL, 0);
        LOG_E("IV_DATA_SCAN_COMPLETE\n");
    }
    rt_sdram_free(iv_data);
    iv_data = NULL;
    rt_sdram_free(buff);
    buff = NULL;
    return SUCCESSFUL;
}

void del_save_iv_index()
{
    s_save_iv_index = 0;
    return;
}

void delete_iv_data()
{
    del_save_iv_index();
    storage_unlink(IV_DATA_FILE);
    return;
}

int save_record_fault_to_file(char* part_name, record_fault_msg_t* record_fault_msg)
{
    part_data_t part_data = {0};
    struct stat file_stat = {0};

    rt_snprintf_s(part_data.name, sizeof(part_data.name),"%s", part_name);
    storage_stat(part_data.name, &file_stat);

    rt_kprintf("save_record_fault_data_file | file_name: %s  file_size: %d",part_data.name, file_stat.st_size);
    if(SUCCESSFUL != handle_storage(write_opr, part_name, (unsigned char*)record_fault_msg->rf_data, record_fault_msg->rf_size, file_stat.st_size))
    {   
        LOG_E("%s:%d| write %s fail", __FUNCTION__ , __LINE__, part_name);
        return FAILURE;
    }

    return SUCCESSFUL;
}


int save_record_fault_file(record_fault_msg_t* record_fault_msg)
{   
    unsigned short saved_num_per_file = 0;
    char part_name[32] = {0};

    his_record_t* p_record =&s_his_record[WAVE_LD_REC_INDEX];
    unsigned short save_num = p_record->his_record_info.saved_num;
    if(save_num == p_record->max_save_num)
    {   
        rotate_file_when_max_save_num(p_record->his_record_info.saved_num, p_record->part_name);
        p_record->his_record_info.saved_num = p_record->min_save_num;
        save_num = p_record->min_save_num;
    }

    if(SUCCESSFUL != judge_save_record_file_name(p_record->part_name, part_name, save_num, &saved_num_per_file))
    {   
        LOG_E("%s:%d| file name cannot find", __FUNCTION__ , __LINE__);
        RAM_FREE(record_fault_msg->rf_data);
        return FAILURE;
    }
    
    rt_kprintf("save_record_fault_data_file |  write before");
    rt_kprintf("save_record_fault_data_file |  %02x   %d\n",record_fault_msg->rf_data, record_fault_msg->rf_size);
    if(SUCCESSFUL != save_record_fault_to_file(part_name, record_fault_msg))
    {   
        RAM_FREE(record_fault_msg->rf_data);
        return FAILURE;
    }
    
    p_record->his_record_info.saved_num++;
    RAM_FREE(record_fault_msg->rf_data);
    LOG_E("%s:%d| write fault record success", __FUNCTION__ , __LINE__);
    return SUCCESSFUL;

}

int fault_pad_zero(part_data_t* part_data, int read_offset, int read_loop, unsigned int rf_data_size)
{
    // 不缺头，缺数据
    unsigned char* rf_data = (unsigned char*)RAM_MALLOC(rf_data_size);
    if(rf_data == NULL)
    {
        LOG_E("%s:%d| malloc fail", __FUNCTION__ , __LINE__);
        return 0;
    }
    rt_memset(rf_data, 0x00, rf_data_size);
    part_data->buff = rf_data;
    part_data->len = rf_data_size;
    part_data->offset = read_offset;
    if(SUCCESSFUL != storage_process(part_data, read_opr))
    {   
        RAM_FREE(rf_data);
        LOG_E("%s:%d| %s read fail", __FUNCTION__ , __LINE__, part_data->name);
        return 0;
    }

    part_data->len = rf_data_size;
    if(SUCCESSFUL != storage_process(part_data, write_opr))
    {   
        RAM_FREE(rf_data);
        LOG_E("%s:%d| %s write_opr fail", __FUNCTION__ , __LINE__, part_data->name);
        return 0;
    }
    RAM_FREE(rf_data);
    // 补零完读取文件大小，应该一致
    return read_loop + 1;
}


int all_fault_pad_zero(part_data_t* part_data, int read_offset, int read_loop)
{
    // 全部补零
    part_data->buff = s_fault_pad_buff;
    part_data->len = sizeof(s_fault_pad_buff);
    part_data->offset = read_offset;
    if(SUCCESSFUL != storage_process(part_data, write_opr))
    {   
        LOG_E("%s:%d| %s write_opr fail", __FUNCTION__ , __LINE__, part_data->name);
        return 0;
    }
    return read_loop + 1;
}

// 文件正确返回录波条数，文件错误返回0
int check_one_record_fault_file(unsigned file_size, char* file_name)
{   
    part_data_t part_data = {0};
    int read_offset = 0, read_loop = 0, next_read_offset = 0;
    unsigned int rf_data_size = 0;
    unsigned char* data_p = NULL;
    rt_snprintf_s(part_data.name, sizeof(part_data.name),"%s", file_name);

    for(read_loop = 0; read_loop < MAX_RECORD_FAULT_NUM_PER_FILE; read_loop++)
    {
        rt_memset(s_buff, 0x00, sizeof(s_buff));
        part_data.buff = s_buff;
        part_data.len = sizeof(s_buff);
        part_data.offset = read_offset;
        if(SUCCESSFUL != storage_process(&part_data, read_opr))
        {   
            LOG_E("%s:%d| %s read fail", __FUNCTION__ , __LINE__, file_name);
            return 0;
        }

        // 解析故障录波
        data_p = s_buff;
        data_p += BEFORE_NUM_OFFSET;
        unsigned short  before_num = get_uint16_data(data_p);

        data_p += 6;
        unsigned short  after_num = get_uint16_data(data_p);

        data_p += 6;
        unsigned short  obser_num = get_uint16_data(data_p);

        next_read_offset = read_offset + 7 + RF_HEAD_SIZE + 2 * obser_num  + (before_num + after_num) * (2 +  2*obser_num);
        if(next_read_offset == file_size)
        {
            // LOG_E("%s:%d| %s is good", __FUNCTION__ , __LINE__, file_name);
            return read_loop + 1;
        }

        if((before_num == 0) || (after_num == 0) || (obser_num == 0))
        {   
            LOG_E("%s:%d| before_num: %d   after_num: %d   obser_num: %d", __FUNCTION__ , __LINE__, before_num, after_num, obser_num);
            return all_fault_pad_zero(&part_data, read_offset, read_loop);
        }

        if(next_read_offset > file_size)
        {   
            LOG_E("%s:%d| next_read_offset: %d   file_size: %d", __FUNCTION__ , __LINE__, next_read_offset, file_size);
            LOG_E("%s:%d| before_num: %d   after_num: %d   obser_num: %d", __FUNCTION__ , __LINE__, before_num, after_num, obser_num);
            rf_data_size = 7 + RF_HEAD_SIZE + 2 * obser_num  + (before_num + after_num) * (2 +  2 * obser_num);
            return fault_pad_zero(&part_data, read_offset, read_loop, rf_data_size);
        }

        read_offset = next_read_offset;
    }

    return 0;
}

int init_pad_zero_buff()
{   
    put_uint16_to_buff(&s_fault_pad_buff[TRIG_ID_OFFSET], TRIG_ID_PAD);
    put_uint16_to_buff(&s_fault_pad_buff[BEFORE_NUM_OFFSET], BEFORE_NUM_PAD);
    put_uint16_to_buff(&s_fault_pad_buff[AFTER_NUM_OFFSET], AFTER_NUM_PAD);
    put_uint16_to_buff(&s_fault_pad_buff[OBSER_NUM_OFFSET], OBSER_NUM_PAD);
    return SUCCESSFUL;
}


int check_record_file_number(his_record_t* p_record, char* number_format)
{
    RETURN_VAL_IF_FAIL(p_record != NULL, FAILURE);
    int i = 0, file_num = 0;
    part_data_t part_data = {0};
    struct stat file_stat = {0};
    char file_names[10][32] = {0};
    char old_name[32] = {0};
    char new_name[32] = {0};
    
    for(i = 0; i < p_record->file_num; i++)
    {
        // 判断文件是否存在
        rt_memset(&part_data, 0x00, sizeof(part_data));
        rt_memset(&file_stat, 0x00, sizeof(file_stat));
        rt_snprintf_s(part_data.name, sizeof(part_data.name),"%s", p_record->file_name[i]);
        if(FAILURE == storage_stat(part_data.name, &file_stat) || file_stat.st_size == 0)
        {
            continue;
        }

        // 文件存在
        rt_snprintf_s(file_names[file_num], sizeof(file_names[file_num]),"%s", p_record->file_name[i]);
        LOG_E("%s:%d| %s exist", __FUNCTION__ , __LINE__, file_names[file_num]);
        file_num++;
        
    }

    for(i = 0; i < file_num; i++)
    {
        rt_memset(old_name, 0x00, sizeof(old_name));
        rt_memset(new_name, 0x00, sizeof(new_name));

        rt_snprintf_s(old_name, sizeof(old_name),"%s", file_names[i]);
        rt_snprintf_s(new_name, sizeof(new_name),"%s%d", number_format, i);
        if(rt_strcmp(old_name, new_name) == 0)
        {
            // 文件名相同，说明序号正确
            continue;
        }

        if(storage_rename(old_name, new_name) != SUCCESSFUL)
        {
            LOG_E("%s:%d| rename %s  %s  fail", __FUNCTION__ , __LINE__, old_name, new_name);
            return FAILURE;
        }
        LOG_E("%s:%d| rename %s  %s  success", __FUNCTION__ , __LINE__, old_name, new_name);
    }

    return SUCCESSFUL;
}



// 每次重启检查故障录波文件的正确性,通过计算计算更新saved_num
int check_all_record_fault_file()
{
    his_record_t* p_record = &s_his_record[WAVE_LD_REC_INDEX];
    int i = 0;
    part_data_t part_data = {0};
    struct stat file_stat = {0};
    int total_record_num = 0, file_record_num = 0;

    check_record_file_number(p_record, RECORD_FAULT_FILE_FORMAT);
    init_pad_zero_buff();
    for(i = 0; i < p_record->file_num; i++)
    {
        // 判断文件在不在
        rt_memset(&part_data, 0x00, sizeof(part_data));
        rt_memset(&file_stat, 0x00, sizeof(file_stat));
        rt_snprintf_s(part_data.name, sizeof(part_data.name),"%s", p_record->file_name[i]);
        storage_stat(part_data.name, &file_stat);
        if(file_stat.st_size == 0)
        {
            continue;
        }

        file_record_num = check_one_record_fault_file(file_stat.st_size, p_record->file_name[i]);
        if(file_record_num == 0)
        {
            // 说明当前文件损坏
            continue;
        }

        total_record_num += file_record_num;
    }

    // 更新saved_num
    p_record->his_record_info.saved_num = total_record_num;
    LOG_E("%s:%d| saved num : %d", __FUNCTION__ , __LINE__, p_record->his_record_info.saved_num);
    return SUCCESSFUL;
}

void delete_other_file()
{
    storage_unlink(ACT_POWER_FILE);
    storage_unlink(HIS_RECORD_INFO_FILE);
    storage_unlink(IV_DATA_FILE);
    return ;
}
void delete_energy_file()
{   
    char* erase_data = NULL;
    storage_unlink(YEAR_FILE);
    storage_unlink(YEAR_FILE_TMP);
    storage_unlink(MONTH_FILE);
    storage_unlink(MONTH_FILE_TMP);
    storage_unlink(DAY_FILE);
    storage_unlink(DAY_FILE_TMP);
    storage_unlink(HOUR_FILE);
    storage_unlink(HOUR_FILE_TMP);
    storage_unlink(HIS_FILE);
    storage_unlink(HIS_BAK_FILE);

    // 清除存储在eeprom的电量数据
    erase_data = (char*)RAM_MALLOC(ENERGY_PART_SIZE);
    RETURN_IF_FAIL(erase_data != NULL);
    rt_memset(erase_data, 0x00, ENERGY_PART_SIZE);
    part_data_t part_data = {0};
    part_data.buff = (unsigned char*)erase_data;
    rt_snprintf(part_data.name, sizeof(part_data.name), "%s", ENERGY_PART);
    part_data.len = ENERGY_PART_SIZE;

    if (SUCCESSFUL != storage_process(&part_data, write_opr))
    {   
        RAM_FREE(erase_data);
        LOG_E("erase energy part fail");
        return;
    }
    RAM_FREE(erase_data);
    return ;
}


void delete_para_file()
{
    storage_unlink(ALMPARA_FILE_NAME);
    storage_unlink(NUMPARA_FILE_NAME);
    storage_unlink(STRPARA_FILE_NAME);
    return ;
}

void delete_his_data_file()
{
    storage_unlink(HISDATA_CONFIG_FILE);
    storage_unlink(HIS_DATA_FILE_0);
    storage_unlink(HIS_DATA_FILE_1);
    storage_unlink(HIS_DATA_FILE_2);
    storage_unlink(HIS_DATA_FILE_3);
    storage_unlink(HIS_DATA_FILE_4);
    storage_unlink(HIS_DATA_FILE_5);
    storage_unlink(HIS_DATA_FILE_6);
    storage_unlink(HIS_RECORD_INFO_FILE);
    // 清记录信息
    s_his_record[HIS_DATA_REC_INDEX].his_record_info.saved_num = 0;
    s_his_record[HIS_DATA_REC_INDEX].his_record_info.temp_saved_num = 0;
    return;
}
void delete_his_alarm_file()
{
    storage_unlink(HIS_ALARM_FILE_0);
    storage_unlink(HIS_ALARM_FILE_1);
    storage_unlink(HIS_ALARM_FILE_2);
    storage_unlink(HIS_ALARM_FILE_3);
    storage_unlink(HIS_ALARM_FILE_4);
    storage_unlink(HIS_ALARM_FILE_5);
    storage_unlink(HIS_ALARM_FILE_6);
    return;
}

void delete_his_event_file()
{
    storage_unlink(HIS_EVENT_FILE_0);
    storage_unlink(HIS_EVENT_FILE_1);
    storage_unlink(HIS_EVENT_FILE_2);
    storage_unlink(HIS_EVENT_FILE_3);
    storage_unlink(HIS_EVENT_FILE_4);
    storage_unlink(HIS_EVENT_FILE_5);
    storage_unlink(HIS_EVENT_FILE_6);
    return;
}

void delete_fault_record_file()
{
    storage_unlink(RECORD_FAULT_FILE_0);
    storage_unlink(RECORD_FAULT_FILE_1);
    storage_unlink(RECORD_FAULT_FILE_2);
    storage_unlink(RECORD_FAULT_FILE_3);
    storage_unlink(RECORD_FAULT_FILE_4);
    storage_unlink(RECORD_FAULT_FILE_5);
    storage_unlink(RECORD_FAULT_FILE_6);
    storage_unlink(RECORD_FAULT_FILE_7);
    storage_unlink(RECORD_FAULT_FILE_8);
    storage_unlink(RECORD_FAULT_FILE_9);
    // 清记录信息
    s_his_record[WAVE_LD_REC_INDEX].his_record_info.saved_num = 0;
    s_his_record[WAVE_LD_REC_INDEX].his_record_info.temp_saved_num = 0;
    return;
}

void delete_all_file()
{
    delete_energy_file();
    delete_his_data_file();
    delete_his_alarm_file();
    delete_his_event_file();
    delete_fault_record_file();
    delete_all_ee_data();
    delete_other_file();
}

int delete_all_ee_data()
{
    unsigned char* data = (unsigned char*)rt_malloc(DEL_EE_SINGLE_SIZE);
    RETURN_VAL_IF_FAIL(data != NULL, FAILURE);
    rt_memset_s(data, DEL_EE_SINGLE_SIZE, 0xff, DEL_EE_SINGLE_SIZE);

    //UPDATE_INFO_PART
    delete_ee_part_data(EE_UPDATE_INFO, data, 0, EE_UPDATE_INFO_SIZE);

    //ENERGY_PART
    delete_ee_part_data(EE_ENERGY_CYCLE, data, 0, EE_ENERGY_CYCLE_SIZE);

    //EE_REAL_ALARM
    delete_ee_part_data(EE_REAL_ALARM, data, 0, EE_REAL_ALARM_SIZE);

    //PARALLEL_ALARM_PART
    delete_ee_part_data(EE_PARALLEL_ALARM, data, 0, EE_PARALLEL_ALARM_SIZE);

    // 删除更改信息里面的数据，需要排除首次启动时间和出厂时间
    delete_ee_part_data(EE_PUBLIC_INFO, data, DEV_STATUS_OFFSET, EE_PUBLIC_INFO_SIZE - DEV_STATUS_OFFSET);

    rt_free(data);
    data = NULL;
    return SUCCESSFUL;
}

int delete_ee_part_data(char* part_name, unsigned char* data, unsigned int offset, size_t size)
{
    int count = size / DEL_EE_SINGLE_SIZE;
    for(int i = 0; i < count; i++)
    {
        handle_storage(write_opr, part_name, data, DEL_EE_SINGLE_SIZE, i * DEL_EE_SINGLE_SIZE + offset);
    }
    handle_storage(write_opr, part_name, data, size % DEL_EE_SINGLE_SIZE, count * DEL_EE_SINGLE_SIZE + offset);
    return SUCCESSFUL;
}

//恢复出厂设置
void restore_factory()
{
    char info[20] = "success";
    // delete_para_file();
    restore_para_to_fact(EXCLUDE_TYPE_NONE);
    delete_all_file();
    s_his_record[EVENT_REC_INDEX].his_record_info.saved_num = 0;
    save_real_ctrl_cmd_event(DAC_CTRL_ID_RESTORE_FACTORY, CTRL_ID_TYPE, info);
    send_msg_to_thread(CSU_RESET, MOD_SYS_MANAGE, NULL,0);//系统重启
    return ;
}


int save_reset_data(unsigned char data)
{
    part_data_t part_data = {0};
    part_data.buff = &data;
    part_data.len = sizeof(data);
    part_data.offset = 0;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), "%s", RESET_FILE);
    if (SUCCESSFUL != storage_process(&part_data, write_opr))
    {
        return FAILURE;
    }
    return SUCCESSFUL;
}

unsigned char get_reset_data(void)
{
    unsigned char reset_reason = 0;
    part_data_t part_data = { 0 };
    part_data.buff = &reset_reason;
    part_data.len = sizeof(reset_reason);
    part_data.offset = 0;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), "%s", RESET_FILE);
    if (SUCCESSFUL != storage_process(&part_data, read_opr))
    {
        reset_reason = NO_RESET_UNKNOW;
    }
    
    //文件读取成功，删除复位记录文件
    storage_unlink(RESET_FILE_NAME);
    return reset_reason;
}

/***************************************************************************
 * @brief    开机保存重启原因
 **************************************************************************/
int save_reset_his_record(void)
{
    unsigned char mcu_reset_reason = 0;
    unsigned char software_reset_reasion = 0;
    unsigned char reset_reason = 0;
    event_record_t event_data = {0};

    // 读取硬件复位原因
    get_mcu_reset_reason(&mcu_reset_reason);
    // 读取软件复位原因
    software_reset_reasion = get_reset_data();
    // 合并硬件，软件原因
    reset_reason = merge_software_mcu_reason(software_reset_reasion, mcu_reset_reason);

    rt_memset_s(&event_data, sizeof(event_record_t), 0, sizeof(event_record_t));
    put_int16_to_buff((unsigned char*)&event_data.event_id, 0x601);
    event_data.type = 0x06;
    for (int loop = 0; loop < NO_RESET_UNKNOW; loop++)
    {
        if(g_reset_reason_table[loop].reason_index == reset_reason)
        {
            rt_memcpy_s(event_data.info, MAX_EVENT_INFO_LEN, g_reset_reason_table[loop].reason, MAX_EVENT_INFO_LEN);
        }
    }
    save_event_record(&event_data);
    return SUCCESSFUL;
}

// 二分查找第一个大于x的位置索引
int binarySearch_offset(map_between_offset_id arr[], unsigned int len, unsigned int x) 
{
    RETURN_VAL_IF_FAIL(len >= 1, FAILURE);
    if(arr[len-1].offset < x)
    {
        return len;
    }

    int left = 0;
    int right = len - 1;
    int mid = -1;
    while (left <= right)
    {
        mid = left + (right - left)/2;
        if (x == arr[mid].offset)
        {
            return mid + 1;
        }
        else if(x < arr[mid].offset)
        {
            if(x > arr[mid - 1].offset)
            {
                return mid;
            }
            right = mid;
        }
        else if(x > arr[mid].offset)
        {
            left = mid + 1;
        }
    }
    return -1;

}

short save_real_ctrl_cmd_event(unsigned int id, unsigned char id_type, char* info)
{
    event_record_t event_data = {{0}};
    int res = -1;
    int index = 0;
    unsigned short offset_id = 0;
    int len = 0;

    unsigned char type_tab[] = {0x01, 0x06};
    map_between_offset_id* data_tab = NULL;

    switch (id_type)
    {
    case REALDATA_ID_TYPE:
        len = sizeof(offset_and_id_tab)/sizeof(map_between_offset_id);
        data_tab = offset_and_id_tab;
        break;
    case CTRL_ID_TYPE:
        len = sizeof(ctrl_tab)/sizeof(map_between_offset_id);
        data_tab = ctrl_tab;
        break;
    
    default:
        return FAILURE;
    }

    res = binarySearch_offset(data_tab, len, id);

    RETURN_VAL_IF_FAIL(res != -1, FAILURE);
    index = id - data_tab[res-1].offset;
    RETURN_VAL_IF_FAIL(index < data_tab[res-1].index, FAILURE);
    offset_id = data_tab[res-1].id + index;
    event_data.type = type_tab[id_type];

    if(info != NULL)
    {
        rt_memcpy_s(event_data.info, MAX_EVENT_INFO_LEN, info, rt_strnlen_s(info, MAX_EVENT_INFO_LEN));
    }
    put_uint16_to_buff((unsigned char*)&event_data.event_id, offset_id);
    send_msg_to_thread(SAVE_EVENT_MSG, MOD_SYS_MANAGE, &event_data, sizeof(event_data));
    return SUCCESSFUL;

}

void init_fac_data_handle()
{
    unsigned short temp_crc = 0;
    fac_data_t init_fac_data = {0};
    handle_storage(read_opr, EE_FAC_DATA, (unsigned char*)&init_fac_data, sizeof(fac_data_t), 0);
    temp_crc = crc_cal((unsigned char *)&init_fac_data, sizeof(init_fac_data)-2);
    if(temp_crc != init_fac_data.crc)
    {
        LOG_E("fac_data error: write crc: %d | read crc: %d", init_fac_data.crc, temp_crc);
        return;
    }
    // set_one_para(DAC_PARA_ID_MACHINE_BARCODE_OFFSET, &(init_fac_data.machine_code),TRUE,FALSE);
    // set_one_para(DAC_COMMON_ID_WIFI_NAME_OFFSET, init_fac_data.wifi_name,TRUE,FALSE);
    set_one_data(DAC_DATA_ID_PRODUCT_MODEL, &(init_fac_data.pro_mod));
    set_one_data(DAC_DATA_ID_MANUFACTURER_ID, &(init_fac_data.manu_id));
    set_one_data(DAC_DATA_ID_PRODUCT_NUMBER, &(init_fac_data.pro_num));
    set_one_data(DAC_DATA_ID_MANUFACTURER_ADDRESS, &(init_fac_data.manu_address));
    set_one_data(DAC_DATA_ID_MANUFACTURER_DATE, &(init_fac_data.manu_date));
    set_one_data(DAC_DATA_ID_DELIERY_TIME, &(init_fac_data.dil_time));
    set_one_data(DAC_DATA_ID_FACTORY_NAME, &(init_fac_data.fac_name));
    set_one_data(DAC_DATA_ID_RATED_POWER_FAC_PARAM, &(init_fac_data.rated_power));
    set_one_data(DAC_DATA_ID_MAX_ACTIVE_POWER_FAC_PARAM, &(init_fac_data.max_act_power));
    set_one_data(DAC_DATA_ID_MAX_REACTIVE_POWER_FAC_PARAM, &(init_fac_data.max_react_power));
    set_one_data(DAC_DATA_ID_MAX_APPARENT_POWER_FAC_PARAM, &(init_fac_data.max_ap_power));
    set_one_data(DAC_DATA_ID_POWER_HARDWARE_VERSION, &(init_fac_data.hardware_version));
}

void save_fac_data()
{
    part_data_t part_data = {0};
    fac_data_t fac_data = {0};

    // 获取数据并填充到fac_data结构体中
    get_one_data(DAC_DATA_ID_MANUFACTURER_ADDRESS, &fac_data.manu_address);
    get_one_para(DAC_PARA_ID_MACHINE_BARCODE_OFFSET, fac_data.machine_code);
    get_one_para(DAC_COMMON_ID_WIFI_NAME_OFFSET,fac_data.wifi_name);
    get_one_data(DAC_DATA_ID_MANUFACTURER_ID, &fac_data.manu_id);
    get_one_data(DAC_DATA_ID_PRODUCT_NUMBER, &fac_data.pro_num);
    get_one_data(DAC_DATA_ID_PRODUCT_MODEL, fac_data.pro_mod);
    get_one_data(DAC_DATA_ID_FACTORY_NAME, fac_data.fac_name);
    get_one_data(DAC_DATA_ID_MANUFACTURER_DATE, fac_data.manu_date);
    get_one_data(DAC_DATA_ID_DELIERY_TIME, fac_data.dil_time);
    get_one_data(DAC_DATA_ID_POWER_HARDWARE_VERSION, fac_data.hardware_version);

    int data;
    get_one_data(DAC_DATA_ID_MAX_ACTIVE_POWER_FAC_PARAM, &data);
    fac_data.max_act_power = data;
    get_one_data(DAC_DATA_ID_RATED_POWER_FAC_PARAM, &data);
    fac_data.rated_power = data;
    get_one_data(DAC_DATA_ID_MAX_APPARENT_POWER_FAC_PARAM, &data);
    fac_data.max_ap_power = data;
    get_one_data(DAC_DATA_ID_MAX_REACTIVE_POWER_FAC_PARAM, &data);
    fac_data.max_react_power = data;

    // 计算CRC
    fac_data.crc = crc_cal((unsigned char *)&fac_data, sizeof(fac_data) - 2);

    // 初始化part_data结构体
    part_data.buff = (unsigned char *)&fac_data;
    part_data.len = sizeof(fac_data_t);
    part_data.offset = 0;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), EE_FAC_DATA);

    // 存储数据
    if (storage_process(&part_data, write_opr) != SUCCESSFUL)
    {
        LOG_E("fac_data write error");
    }
}


void clear_update_uuid()
{
    update_app_info_t update_data = {0};
    handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char*)&update_data, sizeof(update_data), MQTT_UPDATE_INFO_OFFSET);
    rt_memset_s(update_data.sub_uuid, sizeof(update_data.sub_uuid), 0x00, sizeof(update_data.sub_uuid));
    handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&update_data, sizeof(update_data), MQTT_UPDATE_INFO_OFFSET);
}

int set_psc_final_type(unsigned short psc_final_type_s)
{
    g_psc_final_type = psc_final_type_s;
    return SUCCESSFUL;
}

void write_event_handle(update_sta_msg_t* sta_msg)
{
    unsigned int dev_sta_id[MAX_DEV_TYPE] = {DAC_DATA_ID_MAIN_CTRL_UPDATE_STATUS,
                                             DAC_DATA_ID_AUXI_CTRL_UPDATE_STATUS,
                                             DAC_DATA_ID_CPLD_CTRL_UPDATE_STATUS,};

    char info[20] = {0};
    char erase_version_flag = FALSE;
    if(sta_msg->south_dev != INVALID_DEV_TYPE && sta_msg->data_s != SOUTH_UPDATE_SUCCESS)
    {
        unsigned short south_update_sta = POWER_UPDATE_FAILED_STA;
        set_one_data(dev_sta_id[sta_msg->south_dev], &south_update_sta);
    }
    set_one_data(DAC_DATA_ID_UPDATE_STATUS, &(sta_msg->data_s));
    save_real_ctrl_cmd_event(DAC_DATA_ID_UPDATE_STATUS, REALDATA_ID_TYPE, sta_msg->info);
    LOG_E("%s", sta_msg->info);
    restore_pv_update_status(sta_msg);

    update_app_info_t data = {0};
    handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char*)&data, sizeof(data), MQTT_UPDATE_INFO_OFFSET);
    RETURN_IF_FAIL(data.version[0] != '\0' && data.sub_uuid[0] == '\0');
    if(sta_msg->data_s == SOUTH_UPDATE_SUCCESS && g_psc_final_type == sta_msg->south_dev && strstr(data.version, "PSC") != NULL)
    {
        set_one_para(DAC_PARA_ID_PSC_VERSION_OFFSET, data.version, TRUE, FALSE);
        rt_memcpy_s(info, 20 ,&(data.version[VER_START_INDEX - 1]), VER_END_INDEX);
        save_version_event(DAC_PARA_ID_PSC_VERSION_OFFSET, info, PARA_TYPE);
        erase_version_flag = TRUE;
    }
    if(sta_msg->data_s == MONITER_UPDATE_SUCCESS && strstr(data.version, "CSC") != NULL)
    {
        set_one_para(DAC_PARA_ID_CSC_VERSION_OFFSET, data.version, TRUE, FALSE);
        rt_memcpy_s(info, 20 ,&(data.version[VER_START_INDEX - 1]), VER_END_INDEX);
        save_version_event(DAC_PARA_ID_CSC_VERSION_OFFSET, info, PARA_TYPE);
        erase_version_flag = TRUE;
    }
    if(erase_version_flag == TRUE || sta_msg->data_s == MONITER_UPDATE_FAILED ||sta_msg->data_s == NORTH_TRANS_FAILED || 
                                        sta_msg->data_s == SOUTH_TRANS_FAILED || sta_msg->data_s == SOUTH_UPDATE_FAILED)
    {
        rt_memset(data.version, 0, sizeof(data.version));
        handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&data, sizeof(data), MQTT_UPDATE_INFO_OFFSET);
    }
}


void restore_pv_update_status(update_sta_msg_t* sta_msg)
{
    unsigned char update_status = DEVICE_NO_UPDATE;
    get_one_data(DAC_DATA_ID_PV_UPDATE_STATUS, &update_status);
    if(sta_msg->data_s != TRIG_FAILED && sta_msg->data_s != NORTH_TRANS_SUCCESS && sta_msg->data_s != SOUTH_TRANS_SUCCESS && update_status != DEVICE_MQTT_UPDATING)
    {
        update_status = DEVICE_NO_UPDATE;
        rt_kprintf("restore_pv_update_status\n");
        set_one_data(DAC_DATA_ID_PV_UPDATE_STATUS, &update_status);
    }
    return;
}
int north_thread_beat_go_on()
{
    thread_beat_go_on(THREAD_PV_INVERTER);
    return SUCCESSFUL;
}

void set_pv_update_status(unsigned char update_status)
{
    set_one_data(DAC_DATA_ID_PV_UPDATE_STATUS, &update_status);
}

void get_pv_update_status(unsigned char* update_status)
{
    get_one_data(DAC_DATA_ID_PV_UPDATE_STATUS, update_status);
}

unsigned short is_salve_comm_err_alm(int alm_id)
{
    unsigned short alm_code = ALM_ID_GET_ALM_CODE(alm_id) + ALM_ID_GET_DEV(alm_id);
    for(int i = 1; i <= MAX_SLAVE_NUM; i++)
    {
        if(alm_code == DAC_ALARM_ID_SLAVE_COMM_INTR + i)
        {
            return TRUE;
        }
    }
    return FALSE;
}

unsigned short is_all_slave_comm_err()
{
    int count = 0;
    real_alarm_info_t real_alarm = {0};
    count = get_realtime_alarm_count();
    for(int i = 0; i < count; i++)
    {
        if(FAILURE == get_nth_realtime_alarm_info(&real_alarm, i))
        {
            continue;
        }
        if(is_salve_comm_err_alm(real_alarm.alm_id) == FALSE)
        {
            // 含非从机通讯断告警
            return FALSE;
        }
    }
    return TRUE;
}

int handle_total_alarm_event()
{
    int count = 0;
    int ala_flag = 0;
    int only_slave_comm_err = FALSE;
    real_alarm_info_t real_alarm = {0};

    count = get_realtime_alarm_count();

    // 处理从机通讯断
    only_slave_comm_err = is_all_slave_comm_err();

    if(count == 0 || only_slave_comm_err == TRUE)
    {
        set_one_data(DAC_DATA_ID_ALARM_TOTAL_SIGNAL, &ala_flag);
        set_one_data(DAC_DATA_ID_ACCI_TOTAL_SIGNAL, &ala_flag);
        return count;
    }

    ala_flag = 1;
    set_one_data(DAC_DATA_ID_ALARM_TOTAL_SIGNAL, &ala_flag);

    for(int i = 0; i < count; i++)
    {
        if(FAILURE == get_nth_realtime_alarm_info(&real_alarm, i))
        {
            continue;
        }
        if(real_alarm.alm_level == CRITICAL_LEVEL)
        {
            set_one_data(DAC_DATA_ID_ACCI_TOTAL_SIGNAL, &ala_flag);
            return count;
        }
    }
    ala_flag = 0;
    set_one_data(DAC_DATA_ID_ACCI_TOTAL_SIGNAL, &ala_flag);
    return count;

}

int para_file_export_handle()
{
    save_real_ctrl_cmd_event(DAC_DATA_ID_PARA_FILE_STATUS, REALDATA_ID_TYPE, "paraFile export");
    return SUCCESSFUL;
}

int para_file_import_parse(char* para_msg)
{
    int ret = FAILURE;
    int data_s = 0;

    for(int i = 0; i < PARA_FILE_LIST_LEN; i++)
    {
        if(rt_strcmp(para_file_list[i], para_msg) == 0)
        {
            ret = para_file_import_handle(i);
            break;
        }
    }
    if(ret != SUCCESSFUL)
    {
        data_s = PARA_UPDATE_FAILED;
        set_one_data(DAC_DATA_ID_UPDATE_STATUS, &data_s);
        save_real_ctrl_cmd_event(DAC_DATA_ID_UPDATE_STATUS, REALDATA_ID_TYPE, "paraFile parse fail");
        return FAILURE;
    }
    data_s = PARA_UPDATE_SUCCESS;
    set_one_data(DAC_DATA_ID_UPDATE_STATUS, &data_s);
    save_real_ctrl_cmd_event(DAC_DATA_ID_UPDATE_STATUS, REALDATA_ID_TYPE, "paraFile parse succ");
    send_msg_to_thread(GRID_CODE_STATUS_CHECK, MOD_SYS_MANAGE, NULL, 0);  // 参数文件导入后检查电网标准码
    return SUCCESSFUL;
}


char* get_client_key_buf()
{
    return g_client_key;
}

char* get_client_other_key_buf()
{
    return g_client_other_key;
}

int deal_client_key_msg(char* file_name)
{
    RETURN_VAL_IF_FAIL(file_name != NULL, FAILURE);
    char* key_buf = NULL;
    char file_path_tmp[STR_LEN_32] = {0};
    int ret = 0;

    if(rt_strcmp(file_name, "client.key") == 0)
    {
        key_buf = get_client_key_buf();
        rt_memcpy_s(file_path_tmp, sizeof(file_path_tmp), MQTT_CLIENT_KEY_FILE, sizeof(MQTT_CLIENT_KEY_FILE));
    }
    else
    {
        key_buf = get_client_other_key_buf();
        rt_memcpy_s(file_path_tmp, sizeof(file_path_tmp), MQTT_CLIENT_KEY_OTHER_FILE, sizeof(MQTT_CLIENT_KEY_OTHER_FILE));
    }
    ret = key_encrypt(key_buf, rt_strnlen_s(key_buf, CLIENT_KEY_DATA_LEN), file_path_tmp);
    if(ret != SUCCESSFUL)
    {
        LOG_E("%s | %d | key_encrypt fail\n" , __FUNCTION__ , __LINE__);
        return FAILURE;
    }
    return SUCCESSFUL;
}

int key_encrypt(char *in_buff, int data_len, char *file_path)
{
    RETURN_VAL_IF_FAIL(in_buff != NULL && file_path != NULL, FAILURE);
    char* encrypt_buff = rt_sdram_malloc(CLIENT_KEY_DATA_LEN);
    RETURN_VAL_IF_FAIL(encrypt_buff != NULL, FAILURE);
    char* encode_buff= rt_sdram_malloc(KEY_ENCODE_BUFF_LEN);
    size_t encrypt_len = 0;
    int ret = 0;
    if(encode_buff == NULL)
    {
        rt_sdram_free(encrypt_buff);
        return FAILURE;
    }
    rt_memset_s(encrypt_buff, CLIENT_KEY_DATA_LEN, 0, CLIENT_KEY_DATA_LEN);
    rt_memset_s(encode_buff, KEY_ENCODE_BUFF_LEN, 0, KEY_ENCODE_BUFF_LEN);

    if(aes_encrypt_gcm(DEFAULT_KEY, (unsigned char*)in_buff, data_len, (unsigned char*)encrypt_buff) != 0)
    {
        rt_sdram_free(encrypt_buff);
        rt_sdram_free(encode_buff);
        return FAILURE;
    }

    if(mbedtls_base64_encode((unsigned  char*)encode_buff, KEY_ENCODE_BUFF_LEN, &encrypt_len, (unsigned char*)encrypt_buff, data_len + ENCRYPT_TAG_LEN) != 0)
    {
        rt_sdram_free(encrypt_buff);
        rt_sdram_free(encode_buff);
        return FAILURE;
    }

    storage_unlink(file_path);
    ret = handle_storage(write_opr, file_path, (unsigned char*)encode_buff, encrypt_len, 0);
    if (SUCCESSFUL != ret) {
        rt_sdram_free(encrypt_buff);
        rt_sdram_free(encode_buff);
        return FAILURE;
    }
    rt_sdram_free(encrypt_buff);
    rt_sdram_free(encode_buff);
    return SUCCESSFUL;
}

int key_decrypt(char *data_buff, unsigned int data_len,char *data_out_buff)
{
    RETURN_VAL_IF_FAIL(data_buff != NULL && data_out_buff != NULL, FAILURE);
    char* decrypt_buff = rt_sdram_malloc(CLIENT_KEY_DATA_LEN);
    RETURN_VAL_IF_FAIL(decrypt_buff != NULL, FAILURE);
    char* decode_buff = rt_sdram_malloc(KEY_DECODE_BUFF_LEN);
    if(decode_buff == NULL)
    {
        rt_sdram_free(decrypt_buff);
        return FAILURE;
    }
    rt_memset_s(decode_buff, KEY_DECODE_BUFF_LEN, 0, KEY_DECODE_BUFF_LEN);
    rt_memset_s(decrypt_buff, CLIENT_KEY_DATA_LEN, 0, CLIENT_KEY_DATA_LEN);

    if(mbedtls_base64_decode((unsigned char*)decode_buff, KEY_DECODE_BUFF_LEN, &data_len, (unsigned char*)data_buff, (unsigned int)data_len) != 0)
    {
        rt_sdram_free(decrypt_buff);
        rt_sdram_free(decode_buff);
        return FAILURE;
    }

    if(aes_decrypt_gcm(DEFAULT_KEY, (unsigned char*)decode_buff, data_len, (unsigned char*)decrypt_buff) != 0 &&
       aes_decrypt_ecb(DEFAULT_KEY, (unsigned char*)decode_buff, CLIENT_KEY_DATA_LEN, (unsigned char*)decrypt_buff) != 0)
    {
        rt_sdram_free(decrypt_buff);
        rt_sdram_free(decode_buff);
        return FAILURE;
    }
    rt_memset_s(data_out_buff, CLIENT_KEY_DATA_LEN, 0, CLIENT_KEY_DATA_LEN);
    rt_memcpy_s(data_out_buff, CLIENT_KEY_DATA_LEN, decrypt_buff, CLIENT_KEY_DATA_LEN);
    rt_sdram_free(decrypt_buff);
    rt_sdram_free(decode_buff);
    return SUCCESSFUL;
}

int decrypt_client_key_file(char* en_key_file, char* de_key_data)
{
    RETURN_VAL_IF_FAIL(en_key_file != NULL && de_key_data != NULL, FAILURE);
    int ret = 0;
    struct stat stat_buff = {0};
    if(storage_stat(en_key_file,&stat_buff) != SUCCESSFUL)
    {
        rt_memset_s(de_key_data, CLIENT_KEY_DATA_LEN, 0, CLIENT_KEY_DATA_LEN);
        LOG_E("no have %s", en_key_file);
        return FAILURE;
    }
    char* en_key_data = rt_sdram_malloc(stat_buff.st_size + 1);
    RETURN_VAL_IF_FAIL(en_key_data != NULL, FAILURE);
    rt_memset_s(en_key_data, stat_buff.st_size, 0, stat_buff.st_size);
    ret = handle_storage(read_opr, en_key_file, (unsigned char*)en_key_data, stat_buff.st_size, 0);
    if (SUCCESSFUL != ret) {
        rt_sdram_free(en_key_data);
        return FAILURE;
    }
    ret = key_decrypt(en_key_data, stat_buff.st_size, de_key_data);
    if(ret != SUCCESSFUL)
    {
        rt_sdram_free(en_key_data);
        LOG_E("%s | %d | key_decrypt fail\n" , __FUNCTION__ , __LINE__);
        return FAILURE;
    }
    rt_sdram_free(en_key_data);
    return SUCCESSFUL;
}

int decrypt_all_client_key_file()
{
    decrypt_client_key_file(MQTT_CLIENT_KEY_FILE, g_client_key);
    decrypt_client_key_file(MQTT_CLIENT_KEY_OTHER_FILE, g_client_other_key);
    return SUCCESSFUL;
}

void save_version_event(unsigned short event_id_offset, char* info, unsigned char type)
{
    event_record_t save_event_info = {{0}};
    unsigned short event_id = DAC_PARA_ID_CSC_VERSION;
    if(event_id_offset == DAC_PARA_ID_PSC_VERSION_OFFSET)
    {
        event_id = DAC_PARA_ID_PSC_VERSION;
    }
    save_event_info.type = type;    // 参数
    rt_memcpy_s(save_event_info.info, MAX_EVENT_INFO_LEN, info, MAX_EVENT_INFO_LEN);  // info 内容
    put_int16_to_buff((unsigned char*)&save_event_info.event_id ,event_id);           // event id 
    send_msg_to_thread(SAVE_EVENT_MSG, MOD_SYS_MANAGE, &save_event_info, sizeof(save_event_info));

}