#include "deub_update.h"
#include "cmd.h"
#include "sps.h"
#include "linklayer.h"
#include "protocol_layer.h"
#include "storage.h"
#include "parse_layer.h"
#include "protocol_bottom_comm.h"
#include "utils_rtthread_security_func.h"
#include "utils_data_transmission.h"
#include "partition_def.h"
#include "ee_public_info.h"
#include "rthw.h"


static bottom_comm_cmd_head_t cmd_req[] = {
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATA_AFT_TRIG_FRAME,   CMD_APPEND_UPDATE_REQ,   BOTTOM_RTN_APP_NOACK},   //0
};

static bottom_comm_cmd_head_t cmd_ack[] = {
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATA_AFT_TRIG_FRAME,   CMD_APPEND_UPDATE_ACK,   BOTTOM_RTN_APP_NOACK},   //0
};

/* 命令总表,必须以空字符串""结束 */
static cmd_t no_poll_cmd_tab[] = {
    {UPDATE_TRIG_FRAME,             CMD_PASSIVE,  &cmd_req[0], &cmd_ack[0], sizeof(bottom_comm_cmd_head_t), },
    {0},
};

static dev_type_t s_deub_north_update_dev = {
    DEV_NORTH_DEUB_UPDATE, 1, PROTOCOL_BOTTOM_COMM, LINK_NORTH_DEUB, R_BUFF_LEN_512, S_BUFF_LEN_512, 0, no_poll_cmd_tab, NULL
};



dev_type_t* init_deub_dev_north_update(void)
{
    return &s_deub_north_update_dev;
}



rt_timer_t  g_update_timer = NULL;
trig_ctr_inf_t g_trig_ctr_info = {0};
static cmd_handle_register_t s_bottom_update_cmd_handle[] = {
    {DEV_NORTH_DEUB_UPDATE, UPDATE_TRIG_FRAME,   CMD_TYPE_NO_POLL, parse_update_trig, pack_update_trig},
    {0} // 结束符
};



void time_out_ctrl_update()
{   
    rt_timer_stop(g_update_timer);
    g_trig_ctr_info.trig_success = FALSE;
    g_trig_ctr_info.trig_times = 0;
    //触发失败写操作记录
    return;
}




int bottom_update_init(void)
{
    int i = 0;
    for(i = 0; i < sizeof(s_bottom_update_cmd_handle)/sizeof(s_bottom_update_cmd_handle[0]); i++) 
    {
        register_cmd_handle(&s_bottom_update_cmd_handle[i]);
    }
    g_update_timer = rt_timer_create("ctrl_update_timer", time_out_ctrl_update, NULL, 30000, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
    if(g_update_timer == NULL)
    {
        return FAILURE;
    }
    return SUCCESSFUL;
}




int enter_boot_to_update(dev_inst_t* dev_inst, cmd_buf_t* cmd_buf)
{
    cmd_buf_t send_cmd_buff;
    rt_memset_s(&send_cmd_buff, sizeof(cmd_buf_t), 0, sizeof(cmd_buf_t));
    unsigned char update_flag = TRUE;
    unsigned char backup_flag = FALSE;

    // 备份标志
    if(handle_storage(write_opr, EE_PUBLIC_INFO, &backup_flag, sizeof(backup_flag), DEUB_BACKUP_FLAG_OFFSET) != SUCCESSFUL)
    {
        return FAILURE;
    }
    // 升级标志
    if(handle_storage(write_opr, EE_PUBLIC_INFO, &update_flag, sizeof(update_flag), DEUB_UPDATE_FLAG_OFFSET) != SUCCESSFUL)
    {
        return FAILURE;
    }
    rt_timer_stop(g_update_timer);
    send_cmd_buff.cmd = cmd_buf->cmd;
    cmd_send_process(dev_inst, &send_cmd_buff);
    rt_thread_mdelay(5000);
    rt_hw_cpu_reset();
    return SUCCESSFUL;
}



int parse_update_trig(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    rt_timer_start(g_update_timer);
    g_trig_ctr_info.trig_success = FALSE;
    if(rt_strncmp((char*)(tran_cmd_buf->buf), UPDATE_FILE_NAME, UPDATE_FILE_NAME_LEN) != 0)
    {
        tran_cmd_buf->rtn = BOTTOM_RTN_DOWN_FILE_NAME_ERR;
        return SUCCESSFUL;
    }

    g_trig_ctr_info.trig_times++;
    //触发成功
    if(g_trig_ctr_info.trig_times >= UPDATE_TRIG_COUNT){
        g_trig_ctr_info.trig_success = TRUE;
        if(enter_boot_to_update((dev_inst_t*)dev_inst, (cmd_buf_t*)cmd_buf) == FAILURE)
        {
            LOG_E("enter update boot failed\n");
            tran_cmd_buf->rtn = BOTTOM_RTN_CMD_ERROR;
        }
    }
    return SUCCESSFUL;
}



int pack_update_trig(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    rt_memset_s(tran_cmd_buf->buf, UPDATE_FILE_NAME_LEN, 0x00, UPDATE_FILE_NAME_LEN);
    rt_memcpy_s(tran_cmd_buf->buf, UPDATE_FILE_NAME_LEN, UPDATE_FILE_NAME, sizeof(UPDATE_FILE_NAME));
    tran_cmd_buf->data_len = UPDATE_FILE_NAME_LEN;
    return SUCCESSFUL;
}

