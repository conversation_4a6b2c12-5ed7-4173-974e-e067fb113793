
/**
 * @file     gui_data_interface.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2025-06-13
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */

#ifndef _GUI_DATA_INTERFACE_H
#define _GUI_DATA_INTERFACE_H

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#include "dev_dcmu.h"
#include "rtdef.h"
#include "data_type.h"
#include "his_record.h"
#include "const_define_in.h"
#include "para_struct_define_in.h"
#include "io_control.h"
#include "MIBTable.h"

#define LEN_PASER            4     // 菜单口令长度
#define MAX_EXCLUDE_CONFIG_PARA_NUM   41  //排除配置参数的最大参数数量
#define MAX_EXCLUDE_RUNNING_PARA_NUM  178  //排除运行参数的最大参数数量

// 参数类型定义
#define PARATYPE_NULL        0
#define PARATYPE_SYSTEM      1     // 系统参数
#define PARATYPE_ATRRIBUTE   2
#define PARATYPE_INRELAY     3
#define PARATYPE_DC          4     // 交流参数
#define PARATYPE_ENV         5     // 环境参数
#define PARATYPE_ADJUST      6
#define PARATYPE_ALL         7
#define PARATYPE_ALARM_RELAY 8
#define ALARM_CLASS_NUM     29

// 告警类型定义
#define ALL_PART             0
#define AC_PART              1
#define SMR_PART             2
#define DC_PART              3
#define ENV_PART             4
#define CSU_PART             5

#define KEY_BUFF_LEN         6

#define KEY_ESC             0x37
#define KEY_ENTER           0x2F
#define KEY_UP              0x3D
#define KEY_DOWN            0x3B
#define KEY_NOTHING         0x3F
#define KEY_UP_DOWN         0x39
#define KEY_UP_ENTER        0x2D
#define KEY_DOWN_ENTER      0x2B
#define KEY_LERI_ENTER      0x0E
#define KEY_LEFT            0x1F
#define KEY_RIGHT           0x3E

#define CTRL_NULL       0x00
#define CTRL_ON         0x01
#define CTRL_OFF        0x02

/* 按键数据结构 */
struct key_info {
    rt_base_t pin;          // 引脚号
    rt_uint8_t id;          // 按键ID
    rt_bool_t last_state;   // 上次状态
    rt_tick_t last_press_time; // 上次按下时间
    rt_tick_t last_release_time; // 上次松开时间
};

struct keys_state {
    rt_uint8_t last_keys_state;    // 组合按键上次按键状态
    rt_uint8_t current_keys_state;
    rt_tick_t last_keys_press_time; // 上次按下时间
};

typedef struct {
    time_base_t    tTime;
    unsigned char  ucID1;
    unsigned char  ucID2;
    unsigned char  ucIndex;
    char           aucMsg[LEN_EVENTMSG];
} T_EventRecord;

typedef struct {
    unsigned char        ucAlarmSn;            // 告警序号
    unsigned char        ucIDIndex;            // 告警设备索引(如SMR1/2/3)
    unsigned char        ucAlarmState;         // 告警值 TRUE表示告警 FALSE表示正常
    time_base_t          tStartTime;           // 告警开始时间
} T_RealAlarmStruct;

/************************************************************
 ** 结构名: T_CommonParaStruct
** 描  述: 通用系统参数结构定义
**************************************************************/
#pragma pack(push, 1)
typedef struct {
    unsigned char        bBattSetup[BATT_NUM];    // 电池组配置
    unsigned char        bLoadSetup[LOAD_NUM];    // 负载配置
    unsigned char        bLoadStateSetup;         // 负载回路状态配置
} T_CommonParaStruct1;

typedef struct {
    short         aiBatShunt[BATT_NUM];     // 电池分流器
    short         aiLoadShunt[LOAD_NUM];    // 负载分流器
} T_CommonParaStruct2;

typedef struct {
    unsigned char     ucBeepOn;                       // 蜂鸣器开关
    unsigned char     ucHisDataInterval;              // 历史数据保存间隔时间
    unsigned char     ucRS232Bps[COMM_PORT_NUM];      // RS232波特率
    unsigned short    awDeviceAddr;                   // 设备地址编号
    unsigned char     ucLanguage;                     // 语言版本
    char              aucPaserword[LEN_PASER];        // 操作口令
} T_CommonParaStruct4;

typedef struct
{
    unsigned char    usageScenario;         // 使用场景

}T_CommonParaStruct5;

/************************************************************
 ** 结构名: T_DcParaStruct
** 描  述: 直流参数结构定义
**************************************************************/
typedef struct {
    short        iDcVolMax;                // 直流电压高阈值
    short        iDcVolMin;                // 直流电压低阈值
    short        iBattVolLow;              // 电池电压低阈值
    short        iBattVolTooLow;           // 电池电压过低阈值
    unsigned char ucBattCurrFault;         // 电池电流异常阈值
    char         scBattTempMax;            // 电池温度高阈值
    char         scBattTempMin;            // 电池温度低阈值
    short        iFuseValveVol;            // 电池回路断告警阈值
} T_DcParaStruct1;

typedef struct {
    short        aiBattDisCharge;          //电池放电阈值
} T_DcParaStruct2;

typedef struct {
    char         scBatTempExMax;           // 电池温度过高阈值
    short        aiDcVoltExLow;            // 直流电压过低阈值
    short        aiDcVoltExHigh;           // 直流电压过高阈值
} T_DcParaStruct3;

/************************************************************
 ** 结构名: T_EnvParaStruct
** 描  述: 环境参数结构定义
**************************************************************/
typedef struct {
    short         scEnvTempMax;            // 环境温度高告警值
    short         scEnvTempMin;            // 环境温度低告警值
    short         scEnvHumMax;             // 环境湿度高告警值
    short         scEnvHumMin;             // 环境湿度低告警值
} T_EnvParaStruct;

/************************************************************
 ** 结构名: T_AlarmRelayParaStruct
** 描  述: 告警干节点参数结构定义
**************************************************************/
typedef struct {
    unsigned char aucRlyAlarmTTL[IN_RELAY_NUM];    // 输入干结点告警电平
    unsigned char aucRlyAlarmGrade[IN_RELAY_NUM];  // 输入干结点告警级别
    char          acRlyName[IN_RELAY_NUM][LEN_RELAYNAME]; // 输入干结点名称
} T_AlarmRelayParaStruct;

/************************************************************
 ** 结构名: T_AlarmGradeParaStruct
** 描  述: 告警级别参数结构定义
**************************************************************/
typedef struct {
    unsigned char aucAlmGrade[ALARM_CLASS_NUM];    // 告警级别
    unsigned char aucAlmOutRly[ALARM_CLASS_NUM];   // 告警输出干结点
} T_AlarmGradeParaStruct;

typedef struct {
    short        iDcVoltZero;                // 直流输出电压零点
    short        iDcCurrZero[LOAD_NUM];      // 负载电流零点
    short        iDcCurrSlope[LOAD_NUM];     // 负载电流斜率
    short        aiBattVoltZero[BATT_NUM];   // 电池电压零点
    short        aiBattCurrZero[BATT_NUM];   // 电池电流零点
    short        aiBattCurrSlope[BATT_NUM];  // 电池电流斜率
    char        ascBattTempZero[BATT_NUM];  // 电池温度零点
} T_AdjustParaStruct1;

typedef struct {
    char        scEnvTempZero;  // 环境温度零点
    char        scEnvHumZero;   // 环境湿度零点
} T_AdjustParaStruct2;

/************************************************************
 ** 结构名: T_SysPara
** 描  述: 系统参数总结构定义（分类后的系统参数）
**************************************************************/
typedef struct {
    T_DcParaStruct1            tDcPara1;            // 直流参数
    T_CommonParaStruct1        tCommonPara1;        // 通用参数
    T_AdjustParaStruct1        tAdjustPara1;        // 校准参数
    T_DcParaStruct2            tDcPara2;            // 直流参数
    T_DcParaStruct3            tDcPara3;            // 直流参数
    T_AlarmRelayParaStruct     tAlarmRelayPara;     // 告警干节点参数
    T_CommonParaStruct4        tCommonPara4;        // 通用参数
    T_EnvParaStruct            tEnvPara;            // 环境参数
    T_AdjustParaStruct2        tAdjustPara2;        // 校准参数
    T_CommonParaStruct2        tCommonPara2;        // 通用参数
    T_CommonParaStruct5        tCommonPara5;        // 通用参数
    T_AlarmGradeParaStruct     tAlarmGradePara;     // 告警级别参数
} T_SysPara;
#pragma pack(pop)

/******************输出控制结构*******************/
typedef struct {
    unsigned char     bScreenPower;
    unsigned char     bLcdLight;           /* 液晶背光 TRUE:点亮 FALSE:关闭*/
    unsigned char     bBuzz;               /* 蜂鸣器 TRUE:鸣叫 FALSE:关闭 */
    unsigned char     ucBuzzCtrl;          /* 蜂鸣器 ALMCTL_MINOR:一般表现/ ALMCTL_CRITICAL:紧急表现/ALMCTL_OFF:关闭 */

    unsigned char     bRunLed;             /* 运行灯 TRUE:点亮 FALSE:关闭 */
    unsigned char     ucAlarmLed;          /* 机架故障灯 监控箱故障灯 ALMCTL_MINOR:一般表现/ ALMCTL_CRITICAL:紧急表现/ALMCTL_OFF:关闭 */
    unsigned char     aucRelay[OUT_RELAY_MAX_NUM]; // 输出干节点控制字
    unsigned char     bManiCtrlRelay;      // 是否手动控制输出干节点
    unsigned int      wRelayOut;           // 输出多路干结点
} T_CtrlOutStruct;

typedef struct {
    unsigned char     ucAlarmSn;           // 告警序号
    unsigned char     ucIDIndex;           // 告警设备索引(如SMR1/2/3)
    unsigned char     ucAlarmState;        // 告警值 TRUE表示告警 FALSE表示正常
    time_base_t       tStartTime;          // 告警开始时间
    time_base_t       tEndTime;            // 告警开始时间
} T_HisAlarmStruct;

/************************************************************
 ** 结构名: T_DcAnalogDataStruct
 ** 描  述: DC实时模拟量数据结构定义
 **************************************************************/
typedef struct
{
    rt_int16_t iVout;                // 直流输出电压
    rt_int16_t iLoadTotalIout;       // 负载总电流
    rt_int16_t iIout[LOAD_NUM];      // 分路负载电流
    rt_int16_t aiBattVolt[BATT_NUM]; // 电池电压
    rt_int16_t aiBattCurr[BATT_NUM]; // 电池电流
    rt_int8_t ascBattTemp[BATT_NUM]; // 电池温度
} T_DcAnalogDataStruct;

/************************************************************
 ** 结构名: T_EnvAnalogDataStruct
 ** 描  述: ENV实时模拟量数据结构定义
 **************************************************************/
typedef struct
{
    rt_int8_t scTemp; // 环境温度
    rt_int8_t scHum;  // 环境湿度
} T_EnvAnalogDataStruct;

typedef struct
{
    T_DcAnalogDataStruct tDcAnalogData;
    T_EnvAnalogDataStruct tEnvAnalogData;
} T_AnalogDataStruct;

/************************************************************
** 结构名: T_DCDigitalDataStruct
** 描  述: 直流段实时状态量数据结构定义,用于保存采样结果
**************************************************************/
typedef struct
{
    rt_uint8_t abBattTempExist[BATT_NUM];   // 温度传感器存在标志
    rt_uint8_t aucLoadLoop[FUSE_NUM];       // 负载回路：0正常; 1断开
    rt_uint8_t aucBattLoop[BATT_NUM];       // 电池回路：0正常；1断开
    rt_uint8_t ucDCSPD;                     // 直流防雷回路：0正常；1故障
    rt_uint8_t aucInputRelay[IN_RELAY_NUM];  // 输入干接点状态：0闭合；1断开
} T_DCDigitalDataStruct;

/************************************************************
** 结构名: T_ENVDigitalDataStruct
** 描  述: 环境实时状态量数据结构定义,用于保存采样结果
**************************************************************/
typedef struct
{
    rt_uint8_t ucENVSmog;        // 烟雾：0正常；1故障
    rt_uint8_t ucENVFlood;       // 水淹：0正常；1故障
    rt_uint8_t ucENCIntrusion;   // 门禁: 0正常；1故障
    rt_uint8_t ucENVDoorMag;     // 门磁：0正常；1故障
    rt_uint8_t ucENVGlassBroken; // 玻璃碎: 0正常；1故障
} T_ENVDigitalDataStruct;

/************************************************************
** 结构名: T_DigitalDataStruct
** 描  述: 实时状态量数据结构定义,用于保存采样结果
**************************************************************/
typedef struct
{
    T_DCDigitalDataStruct tDCData;
    T_ENVDigitalDataStruct tENVData;
} T_DigitalDataStruct;

typedef struct
{
    struct
    {
        time_base_t tTime;
        rt_int16_t iCurr;
    } tMaxTotalLoadCurr; // 负载总电流最大值

    struct
    {
        time_base_t tTime;
        rt_int16_t iCurr;
    } tMaxDischgBattCurr; // 电池放电电流最大值

    struct
    {
        time_base_t tTime;
        rt_int8_t scBattTemp;
    } tMaxBattTemp; // 电池温度最大值

    struct
    {
        time_base_t tTime;
        rt_int8_t scEnvTemp;
    } tMaxEnvTemp; // 环境温度最大值

    struct
    {
        time_base_t tTime;
        rt_int8_t scEnvTemp;
    } tMinEnvTemp; // 环境温度最大值

    struct
    {
        time_base_t tTime;
        rt_int16_t iDcVolt;
    } tMaxDcVolt; // 直流电压最大值

    rt_uint8_t uCrcHi;
    rt_uint8_t uCrcLo;
} T_PeakDataStruct;

/* 函数声明 */
void key_buffer_init(void);
unsigned char DelKey(void);
void AddKey(unsigned char ucKeyID);

unsigned char CalRealAlarmTotal(void);
T_RealAlarmStruct* GetDisRealAlarm(unsigned char ucItem);
int convert_gui_to_raw(void *pGuiValue, int guiType, int rawType, unsigned char precision, u_value *pRawValue);
short GetSysPara(unsigned char ucParaType, char* pDest);
int GetDigitalData(unsigned char ucPart, char* pDest);
int GetAnalogData(unsigned char ucPart, char* pDest);
unsigned char GetCtrlOut(T_CtrlOutStruct* tCtrlOut);
unsigned char SetCtrlOut(T_CtrlOutStruct* tCtrlOut);
unsigned char clear_ctrl_out(void);
unsigned short GetHisAlmNum(void);
unsigned short GetHisEventNum(void);
short GetDisHisAlarmAndNode(unsigned short index, T_HisAlarmStruct* dest, MIB_AlarmDataNode* alarm_node);
short GetDisHisEvent(unsigned short index, T_EventRecord* dest);
int GetPeakData(char* pDest);
unsigned char get_precision(unsigned char raw_type, unsigned short para_id_offset_base);
int get_para(unsigned short para_id_offset_base, unsigned char index, void* pSysPara);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* _GUI_DATA_INTERFACE_H */
