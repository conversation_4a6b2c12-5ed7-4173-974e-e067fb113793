/**************************************************************************
* Copyright (C) 2001, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：ZXDU18 CTL板软件
* 文件名称：apptest.h
* 文件说明：apptest协议模块头文件
* 作    者：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/


#ifndef SOFTWARE_SRC_APP_APPTEST_H_
#define SOFTWARE_SRC_APP_APPTEST_H_

#ifdef __cplusplus
extern "C" {
#endif

/***********************  常量定义      ************************/
/***********************   CID2命令码   ********************/

#define   GET_ANALOG_FLOAT_AT         0x41        //获取模拟量浮点数据
#define   GET_ANALOG_INT_AT           0x42        //获取模拟量定点数据
#define   GET_SWITCH_AT               0x43        //获取开关输入状态
#define   GET_ALARM_AT                0x44        //获取告警状态
#define   REMOTE_CTRL_AT              0x45        //遥控(输出干接点控制)
#define   GET_PARA_FLOAT_AT           0x46        //获取浮点参数
#define   GET_PARA_INT_AT             0x47        //获取定点参数
#define   SET_PARA_FLOAT_AT           0x48        //设置浮点参数
#define   SET_PARA_INT_AT             0x49        //设置定点参数


#define   GET_TIME_AT                   0x4D        //获取时间
#define   SET_TIME_AT                   0x4E        //设置时间
#define   GET_PROTOCOL_VER_AT           0x4F        //获取协议版本号
#define   GET_ADDR_AT                   0x50        //获取设备地址
#define   GET_FACTORY_INFO_AT           0x51        //获取厂家信息
#define   SET_ADDR_DEFINED_AT           0xE0        //设置设备地址
#define   AUTO_SEND_ALARM_AT            0xE8
//中试增加的命令
#define     GET_GUI_RESULT_AT                0xD0    //获取按键测试信息
#define     CHIP_TEST_AT                     0xD1    //获取芯片测试信息
#define     GET_MAC_AT                       0xD2    //获取MAC地址
#define     SET_MAC_AT                       0xD3    //设置MAC地址
#define     START_NET_TEST_AT                0xD4    //网络测试
#define     START_CAN_BUS_TEST_AT            0xD5    //启动CAN总线命令信息
#define     GET_CAN_BUS_RESULT_AT            0xD6    //获取CAN总线测试信息
#define     SET_CSU_ID_AT                    0xD7    //设置整机序列号命令
#define     SET_DOWNLOAD_PROTECTTEST_AT      0xD8    //下电保护电路测试

#define     SET_APPTEST_RESET_AT             0xD9    //APPTEST重启（新增）

#define     GET_BOARD_TEMP                   0xDA    //获取单板温度
#define     GET_TEST_DATA                    0xDB    //获取测试数据，CID1为42，直接使用下面的0xDB,名称容易误导，重新定义一个
#define     SET_FAT_ID_AT                    0xDB    //设置出厂测试记录，CID1为40
#define     GET_FAT_ID_AT                    0xDC    //读取出厂测试记录
#define     GET_CSUTEST_VERSION_ID_AT        0xDD    //读取csuTest软件版本
#define     GET_PLATFORM_VERSION_ID_AT       0xDE    //读取软件平台版本
#define     SET_SYSTEM_RESET_ID_AT           0xDF    //软件复位CSU

//new order
#define     SET_QUIT_TEST_AT                 0x61     //设置退出测试模式
#define     GET_CSU_ID_AT                    0x7B     //获取整机序列号

//新增命令
#define    START_BMS_CTRL_AT                 0xE4    //BMS控制
#define    GET_BMS_PACK_INFO_AT              0xE5    //获取BMS电池PACK厂家信息
#define    SET_BMS_PACK_INFO_AT              0xE6    //设置BMS电池PACK厂家信息
#define    SET_BMS_BATT_CAP_AT               0xE7    //设置BMS电池容量

#define    BMS_ADJUST_DATA_AT                0xE8    //BMS校正
//apptest新增
#define    GET_BMS_ID_AT                     0xE9    //获取BMS序列号
#define    SET_BMS_ID_AT                     0xEA    //设置BMS序列号
#define    GET_BDU_FACT_AT                   0xEB    //获取BDU厂家信息
#define    GET_THEFT_TEST_AT                 0xEC   //获取防盗测试结果
#define    GET_SLEEP_TEST_AT                 0xED   //获取休眠测试结果
#define    COMM_LI_EFFICIENCY_TEST_AT        0xEE   //常规锂效率测试
#define    GET_CELL_FACT_INFO_AT             0xEF   //获取电芯厂家信息
#define    SET_CELL_FACT_INFO_AT             0xF0   //设置电芯厂家信息

#define    GET_BMS_FACT_AT                   0xF1   //获取BMS厂家信息
#define    SET_BMS_FACT_AT                   0xF6   //设置BMS厂家信息
#define    SET_CUSTOMER_NAME_AT              0x3C   //设置客户名称 CID1:40
#define    GET_CUSTOMER_NAME_AT              0x3D   //获取客户名称 CID1:40
#define    GET_HARDWARE_INFORMATION_EX       0x64   //获取硬件版本信息
#define    SET_HARDWARE_INFORMATION_EX       0x65   //设置硬件版本信息
#define    GET_4G_MODE_REAL_DATA             0x66   //获取4G模块实时数据
//#define    P2P_TRIGGER                    0xFF   //点对点触发命令

#define    SET_DEL_HISDATA_AT                0xF9    //清除历史记录

#define    GET_ALARM_LEVEL                   0xFB    //获取告警级别
#define    SET_ALARM_LEVEL                   0xFC    //设置告警级别
#define    GET_ALARM_RELAY_APPTEST           0xFD    //获取告警干接点
#define    SET_ALARM_RELAY_APPTEST           0xFE    //设置告警干接点
#define    SET_INPUT_RELAY_AT                0x8E
#define    GET_INPUT_RELAY_AT                0x8F

#define    GET_BMS_CHARGE_VOLT               0x3C    //获取充电电压
#define    SET_BMS_CHARGE_VOLT               0x3D    //设置充电电压
#define    GET_HARDWARE_PARA                 0X3E    //获取硬件板卡参数
#define    SET_HARDWARE_PARA                 0X3F    //设置硬件板卡参数
#define    SET_BMS_FACT_AT_NEW               0X40    //设置BMS厂家信息新增
#define    GET_BMS_FACT_AT_NEW               0X41    //获取BMS厂家信息新增
#define    SET_BDU_HEAT                      0x52    //加热控制
#ifndef SHIELD_BALANCE_CIRCUIT_OPENALM
#define    START_BALA_CIRC_CHECK             0x53    //启动均衡电路故障检测
#define    GET_BALA_CIRC_FAULT               0x54    //获取均衡电路故障状态
#endif
#define    GET_BDU_FACT_HEAT                 0x55    //获取BDU厂家信息(带有是否支持加热器信息)
#define    START_CAN2_TEST                   0x56    //启动CAN2总线测试
#define    GET_CAN2_TEST                     0x57    //获取CAN2总线测试
#define    SET_FAC_TIME                      0x58    //设置出厂时间
#define    GET_FAC_TIME                      0x59    //获取出厂时间
#define    SET_FIRST_BOOT_TIME               0x5A    //设置第一次开机时间
#define    GET_FIRST_BOOT_TIME               0x5B    //获取第一次开机时间
#define    START_CAN_INTERCONNECT_TEST       0x5C    //启动CAN总线对插测试
#define    GET_CAN_INTERCONNECT_TEST         0x5E    //获取CAN总线对插测试状态
#define    BDU_CTRL                          0X5F    //充放电直通控制

//new apptest added by xzx
#define    BMS_SEND_SMS                   0x18   //启动BMS发送短信
#define    GET_BTN_COUNT                  0x20   //获取按键点击次数
#define    SET_BMS_SHUTDOWN               0x21   //控制BMS关机
#define    GET_GPRS_STATUS                0x22   //获取GPRS状态
#define    BMS_ADJUST_DATA_NEW            0x23   //BMS校正新指令 R321不支持BMS浮点数校正，命令占位保留
#define    REFRESH_DEFAULT_PARA           0x24   //恢复缺省参数
#define    SET_BDU_OPENCYCLE              0x25   //BDU开环控制
#define    GET_BDU_ID_AT                  0x26   //获取BDU序列号
#define    SET_BDU_ID_AT                  0x27   //设置BDU序列号
#define    GET_GPS_LOCATED                0x28   //获取GPS定位状态
#define    GET_BDPB_ID                    0x29   //获取BDPB板序列号
#define    SET_BDPB_ID                    0x30   //设置BDPB板序列号
#define    GET_MAIN_ID					  0x2A   //获取MAIN板序列号15位
#define    SET_MAIN_ID					  0x2B   //设置MAIN板序列号15位


#define    GET_OUT_CHGTEST_DATA_AT           0x2D   //获取外协充电测试数据 0907 mwl
#define    GET_OUT_STOPTEST_DATA_AT          0x2E   //获取外协静置测试数据
#define    GET_OUT_DISCHGTEST_DATA_AT        0x2F   //获取外协放电测试数据

#define    GET_IMPE_CMPN_INFO        0x31   //获取电芯阻抗补偿信息
#define    SET_IMPE_CMPN_INFO        0x32   //获取电芯阻抗补偿信息
#define	   GET_BMS_BATT_CAP			 0x33   //获取BMS电池容量
#define	   SET_RELEASE_LOCK			 0x34   //解除闭锁

#define   GET_PARA_NEW_APPTEST                0x37       //获取新增参数     CID1:42
#define   SET_PARA_NEW_APPTEST                0x38       //设置新增参数     CID1:42
#define   GET_BDU_ADJ_PARA            0x39       //获取BDU校正参数  CID1:42
#define   GET_SPECIFIC_PARA           0x3A       //获取特定参数 CID1:42
#define   SET_SPECIFIC_PARA           0x3B       //设置特定参数 CID1:42
#define   GET_IMEI_CODE               0x5D       //获取IMEI码
#define   SET_BDU_TEST_PARA           0x3E       //设置功率测试项参数 CID1:42
#define   GET_BDU_TEST_DATA           0x3F       //获取功率测试数据 CID1:42
#define   GET_BAT_PARA_BY_ID          0x50       //获取电池系统参数（ID化） CID1:42
#define   SET_BAT_PARA_BY_ID          0x51       //设置电池系统参数（ID化） CID1:42
#define   SET_BAT_BMS_INFO            0x52       //设置电池厂家            CID1:42
#define   GET_BAT_BMS_INFO            0x53       //获取电池厂家            CID1:42
#define   CLEAR_4G_TRAFFIC_APPTEST    0x54       //清除流量记录            CID1:42

//外协测试记录命令


//设置地址回包中,上送给后台的扩展字节,用于后台区分不同的协议版本
//#define EXTENDED_ITEM_NUM   3
//#define TYPE_DEVICE         1
//#define TYPE_FOREGROUND     2
//#define TYPE_BACKGROUND     3


//中试测试命令增加
/*出厂测试记录*/
#pragma pack(push, 1)
typedef struct
{
    BYTE            fatRecordType;      //出厂测试类型
    T_TimeStruct    tTime;              //出厂测试时间
//    BYTE            aucMcuNumber[6];    //监控条码号
    BYTE            fatRecordResult;    //出厂测试结果
    BOOLEAN         bRecValid;
    WORD            wCheckSum;  
}T_FatRecordStruct;
#pragma pack(pop)

enum
{
    BMS_FT_TEST,//FT测试
    MODILE_TEST,  //整机测试
};

enum
{
    RESULT_NULL,
    RESULT_FAILED,
    RESULT_PASS,
};

typedef struct
{
    CHAR           acBDPBSn[15];   //BDPB板序列号
    WORD           wCheckSum;  
}T_BDPBSnStruct;

/*********************  函数原型定义  **********************/

//APPTEST命令
void GetFactoryInfo( BYTE ucPort );
void RecCanBusCtrl(BYTE ucPort);
void SendCanReport(BYTE ucPort);
void SetBmsId(BYTE ucPort);
void GetBmsId(BYTE ucPort);
void SetQuitAPPTest(BYTE ucPort);
void SendBMSCtrl(BYTE ucPort);
void SetBMSPACKFactoryInfo(BYTE ucPort);
void GetBMSPACKFactoryInfo(BYTE ucPort);
void SetDcControl(BYTE ucPort);
void SetBMSBattCap( BYTE ucPort );
void GetBDUFact(BYTE ucPort);
void GetBDUFactHeat(BYTE ucPort);
void GetTheftTest(BYTE ucPort);
void SetEfficiencyTest(BYTE ucPort);
void RecResetCmd(BYTE ucPort);
void SetDelHisData( BYTE ucPort );
void GetBmsFactInfo(BYTE ucPort);
void SetBmsFactInfo(BYTE ucPort);
void GetBmsFactInfoNew(BYTE ucPort);
void SetBmsFactInfoNew(BYTE ucPort);
void GetBtnCount(BYTE ucPort);
void SetBMSShutdown(BYTE ucPort);
void RefreshDefaultPara(BYTE ucPort);
void SetOpenCycleCtrl(BYTE ucPort);
void SetHeatCtrl(BYTE ucPort);
void GetBduIdNo(BYTE ucPort);
void SetBduIdNo(BYTE ucPort);
void StartBalanceCircCheck(BYTE ucPort);
void GetBalanceCircFault(BYTE ucPort);
void GetBDPBSn(BYTE ucPort);
void SetBDPBSn(BYTE ucPort);
void GetMainSn(BYTE ucPort);
void SetMainSn(BYTE ucPort);
void GetBMSBattCap(BYTE ucPort);
void SetBMSReleaseLock(BYTE ucPort);
void GetBMSChgVolt(BYTE ucPort);
void GetBMSHardwarePara(BYTE ucPort);
void SetBMSHardwarePara(BYTE ucPort);
void GetHWMacAddr(BYTE ucPort);
void SetHWMacAddr(BYTE ucPort);
void SetNetTest(BYTE ucPort);
void SetFactoryAcceptanceTest( BYTE ucPort );
void GetFactoryAcceptanceTest( BYTE ucPort );
//qtp与apptest共用
void SetCsuId(BYTE ucPort);
void GetCsuId(BYTE ucPort);
void SendCellFactInfo(BYTE ucPort);
void RecCellFactInfo(BYTE ucPort);
void  GetBmsSn(CHAR *p);
BOOLEAN RangeValidationFloat(FLOAT fData, FLOAT fMinValue, FLOAT fMaxValue);
void SetFirstBootTime(BYTE ucPort);
void SetFacTime(BYTE ucPort);
void GetFirstBootTime(BYTE ucPort);
void GetFacTime(BYTE ucPort);
void SetBduAppTestPara(BYTE ucPort);
void PackInfoSync(void);
void SetBmsInfo(BYTE ucPort);
void GetBmsInfo(BYTE ucPort);

BOOLEAN GetGpsStatus(BYTE ucPort);


void GetHardwareInfo(BYTE ucPort);
void SetHardwareInfo(BYTE ucPort);
void Get4GModeRealData(BYTE ucPort);

#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // SOFTWARE_SRC_APP_APPTEST_H_
