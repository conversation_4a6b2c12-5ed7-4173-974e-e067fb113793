/*******************************************************************************
  * @file        sample.h
  * @version     v1.0.0
  * @copyright   COPYRIGHT &copy; 2023 CSG
  * <AUTHOR>
  * @date        2023-4-13
  * @brief
  * @attention
  * Modification History
  * DATE         DESCRIPTION
  * ------------------------
  * - 2023-4-13  dongmengling Created
*******************************************************************************/

#ifndef A604_MAIN_SAMPLE_H_
#define A604_MAIN_SAMPLE_H_

#ifdef __cplusplus
extern "C" {
#endif

#include <rtdevice.h>
#include <spi.h>
#include <math.h>

/***********************  常量定义  ************************/
#define ADCCONVERTEDVALUES_CHANNEL_NUM   9   //ADC采样通道数
#define ADCCONVERTEDVALUES_SAMPLING_NUM  7   //ADC采样次数
#define ADCCONVERTEDVALUES_ROAD_NUM      8  //单路通道采样路数

#ifdef USING_DEVICE_60XA_UIB01
#define ANALOG_COUNT                    11    ///< UIB01扩展板ADC采集的模拟量数量
#define DIGITAL_COUNT                   0    ///< UIB01扩展板采集的开关量的数量
#else
#define DIGITAL_COUNT                    0
#define ANALOG_COUNT                     41
#endif


/*********************  数据结构定义  **********************/


/*********************  函数原型定义  **********************/
void *sample_init_sys(void * param);
void sample_main(void * parameter);
unsigned short get_analog_data(int *data, unsigned short data_size);
unsigned short get_digital_data(int *data, unsigned short data_size);



#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // A604_MAIN_SAMPLE_H_;
