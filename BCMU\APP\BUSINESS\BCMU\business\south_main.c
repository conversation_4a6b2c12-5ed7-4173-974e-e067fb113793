#include "sample.h"
#include "utils_thread.h"
#include "device_type.h"
#include "cmd.h"
#include "msg.h"
#include "msg_id.h"
#include "south_main.h"
#include "utils_time.h"

#define BCMU_SOUTH_MAIN_TIME_DELAY          20       ///< unit ms

static void process_recv_msg(dev_comm_t* dev_comm);
static dev_comm_t* south_comm_thread_init(unsigned char dev_type_id, module_id_e mod_id);
static void exe_south_heartbeat(unsigned char inst_id);

void process_para_change(const rt_msg_t pMsg){

}

static msg_map sample_msg_map[] =
{
    {PARA_CHANGE_MSG_ID,   process_para_change,  },         
};

static dev_init_t dev_init_tab[] = {
    #ifdef USING_DEVICE_BMU
    {DEV_BMU, init_dev_bmu, get_bmu_data , NULL},
    #endif
};

void*  init_bmu(void * param)
{
    init_dev_tab(dev_init_tab, sizeof(dev_init_tab)/sizeof(dev_init_tab[0]));
    server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(sample_msg_map) / sizeof(msg_map);
    register_server_msg_map(sample_msg_map, server_info);
    dev_comm_t* dev_comm = south_comm_thread_init(DEV_BMU, MOD_BMU);
    return dev_comm;
    
}

void*  init_dc_dc(void * param)
{
    server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(sample_msg_map) / sizeof(msg_map);
    register_server_msg_map(sample_msg_map, server_info);
    dev_comm_t* dev_comm = south_comm_thread_init(DEV_DC_DC, MOD_DC_DC);
    return dev_comm;
}


static dev_comm_t* south_comm_thread_init(unsigned char dev_type_id, module_id_e mod_id) {
    dev_comm_t* dev_comm = NULL;
    
    
    dev_comm = init_dev_comm_info(dev_type_id);
    if (dev_comm == NULL)
        return NULL;
    
    dev_comm->thread_mq = init_msg_queue(mod_id);
    if (dev_comm->thread_mq == RT_NULL) {
        free_dev_comm(dev_comm);
        rt_free(dev_comm);
        dev_comm = NULL;
        return NULL;
    }
    return dev_comm;
}



static void process_recv_msg(dev_comm_t* dev_comm) {
    module_msg_t msg_rcv;
    
    
    if (rt_mq_recv(dev_comm->thread_mq, &msg_rcv, sizeof(msg_rcv), 0) != RT_EOK)
        return;
    
    switch (msg_rcv.msg_id) {
        case START_DEV_POLL_MSG:
            start_dev_poll(dev_comm, (unsigned char*)msg_rcv.data);
            rt_free(msg_rcv.data);
            break;
            
        case STOP_DEV_POLL_MSG:
            dev_comm->dev_mgr->poll_opt = POLL_DISABLE;
            break;
            
        case EXE_DEST_CMD_MSG:
            process_cmd_exe_msg((unsigned char*)msg_rcv.data, dev_comm);
            rt_free(msg_rcv.data);
            break;

        default:
            break;
    }
}


void comm_thread_entry(void* thread_data) {
    dev_comm_t* dev_comm = (dev_comm_t*)thread_data;
    link_inst_t* link_inst = dev_comm->dev_inst[0].dev_type->link_inst;
    // cmd_t* cmd = NULL;

    while (is_running(TRUE)) {
        // 接收命令应答数据
        if(rt_sem_take(&(link_inst->rx_sem), 10) == RT_EOK) {
            update_dev_mgr_info_id_version(dev_comm);
            continue;
        }
        
        if (dev_comm->dev_mgr->cmd_sta == CMD_FINISH) {
            process_recv_msg(dev_comm);
            exe_next_cmd_id_version(dev_comm);
        }

        exe_south_heartbeat(link_inst->id);
        rt_thread_mdelay(BCMU_SOUTH_MAIN_TIME_DELAY);
    }
}


static void exe_south_heartbeat(unsigned char inst_id)
{
    static rt_uint32_t last_time = 0;
    rt_int32_t  ltdif = 0;
    rt_uint32_t curr_time = get_sys_runtime();

    if (last_time == 0) {
        last_time = curr_time;
        if(inst_id == LINK_BMU){
            send_heart_beat_msg(MOD_BMU);
        }else{
            send_heart_beat_msg(MOD_DC_DC);
        }
        return;
    }

    ltdif = curr_time - last_time;
    if (ltdif < 0) {
        ltdif = (~ltdif) + 1;
    }

    if (ltdif >= 5000) {
        if(inst_id == LINK_BMU){
            send_heart_beat_msg(MOD_BMU);
        }else{
            send_heart_beat_msg(MOD_DC_DC);
        }
        last_time = curr_time;
    }
}




