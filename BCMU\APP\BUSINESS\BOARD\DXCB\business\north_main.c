#include <string.h>
#include <stdlib.h>
#include <rtthread.h>
#include "utils_thread.h"
#include "device_type.h"
#include "msg.h"
#include "cmd.h"
#include "north_main.h"
#include "data_type.h"
#include "sps.h"
#include "utils_time.h"
#include "utils_server.h"
#include "utils_string.h"
#include "protocol_north_modbus.h"
#include "utils_rtthread_security_func.h"
#include "bottom_comm_update_handle.h"
#include "para_id_in.h"
#include "para_manage.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "south_dev_common.h"    // 后面需要调整下

static unsigned char s_dev_type[] = {
    DEV_NORTH_DXCB_UPDATE,
    DEV_NORTH_DXCB_MODBUS, //modbus协议必须放最后
};

#define MAX_DEV_INST_NUM         (sizeof(s_dev_type) / sizeof(unsigned char))
#define MODBUS_PROTOCOL_INDEX    (MAX_DEV_INST_NUM - 1)

static dev_inst_t* north_dev_dxcb;
static dev_inst_t* s_north_dev_inst[MAX_DEV_INST_NUM] = {NULL};
static unsigned char s_backup_buff[MAX_NORTH_COMM_BUFF_LEN] = {0};
static unsigned int s_backup_len = 0;



static north_mgr_t *init_thread_data(link_inst_t* link_inst, unsigned char mod_id) {
    north_mgr_t* north_mgr = rt_malloc(sizeof(north_mgr_t));
    if (north_mgr == NULL) {
        // Handle allocation failure for north_mgr
        return NULL;
    }

    north_mgr->cmd_buff = rt_malloc(sizeof(*north_mgr->cmd_buff));
    if (north_mgr->cmd_buff == NULL) {
        // Handle allocation failure for cmd_buff
        rt_free(north_mgr);
        return NULL;
    }

    north_mgr->link_inst = link_inst;
    return north_mgr;
}


static msg_map north_msg_map[] =
{
    {0, NULL},//临时添加解决编译问题
};


void* init_north(void * param) {
    server_info_t *server_info = (server_info_t *)param;
    north_mgr_t* north_mgr = NULL;

    for(int loop = 0; loop < MAX_DEV_INST_NUM; loop++) {
        north_dev_dxcb = init_dev_inst(s_dev_type[loop]);
        RETURN_VAL_IF_FAIL(north_dev_dxcb != NULL, NULL);
        s_north_dev_inst[loop] = north_dev_dxcb;
    }
    bottom_update_init();
    init_rtn_sem();
    server_info->server.server.map_size = sizeof(north_msg_map) / sizeof(msg_map);
    register_server_msg_map(north_msg_map, server_info);
    north_mgr = init_thread_data(north_dev_dxcb->dev_type->link_inst, MOD_NORTH_DXCB_COMM);

    return north_mgr;
}

void handle_received_data(north_mgr_t* north_mgr, rt_tick_t* comm_fail_time) {
    unsigned char dev_addr = 1;
    dev_inst_t north_dev_inst_tmp = {0};
    char comm_fail = FALSE;
    for(int loop = 0; loop < MODBUS_PROTOCOL_INDEX ; loop++) {
        rt_memcpy_s(&north_dev_inst_tmp, sizeof(dev_inst_t), s_north_dev_inst[loop], sizeof(dev_inst_t));
        // Directly use the buffer in cmd_buff to avoid redundant memcpy
        north_mgr->cmd_buff->data_len = s_backup_len;
        get_one_para(DXCB_PARA_ID_DEV_ADDR_OFFSET, &dev_addr);
        set_host_addr(dev_addr);
        if(SUCCESSFUL == protocol_parse_recv(&north_dev_inst_tmp, north_mgr->cmd_buff)) {
            cmd_send(&north_dev_inst_tmp, north_mgr->cmd_buff);
            rt_memset_s(s_backup_buff, MAX_NORTH_COMM_BUFF_LEN, 0, MAX_NORTH_COMM_BUFF_LEN);
            s_backup_len = 0;
            rt_sem_clear_value(&(north_mgr->link_inst->rx_sem));
            *comm_fail_time = rt_tick_get();
            set_one_data(DCXB_TRACE_ID_NORTH_COMM_FAIL_ALARM, &comm_fail);
            break;
        }
    }
}

void handle_modbus_data(north_mgr_t* north_mgr, rt_tick_t* comm_fail_time) {
    // Directly use the buffer in cmd_buff to avoid redundant memcpy
    char comm_fail = FALSE;
    north_mgr->cmd_buff->data_len = s_backup_len;
    north_mgr->cmd_buff->cmd = NULL;
    unsigned char src_addr = 1;
    get_one_para(DXCB_PARA_ID_DEV_ADDR_OFFSET, &src_addr);
    s_north_dev_inst[MODBUS_PROTOCOL_INDEX]->dev_addr = src_addr;

    if (SUCCESSFUL == protocol_parse_recv(s_north_dev_inst[MODBUS_PROTOCOL_INDEX], north_mgr->cmd_buff)) {
        cmd_send(s_north_dev_inst[MODBUS_PROTOCOL_INDEX], north_mgr->cmd_buff);
        *comm_fail_time = rt_tick_get();
        set_one_data(DCXB_TRACE_ID_NORTH_COMM_FAIL_ALARM, &comm_fail);
    }
}

/* 收发线程 */
void north_comm_th(void *param) {
    RETURN_IF_FAIL(param != NULL);
    north_mgr_t* north_mgr = (north_mgr_t*)param;
    unsigned char time_delay = 20;
    rt_tick_t comm_fail_time = 0;

    init_modbusrtn_var();
    while (is_running(TRUE)) {
        if (RT_EOK == rt_sem_take(&(north_mgr->link_inst->rx_sem), THREAD_TICK)) {
            if (FAILURE == linker_recv(s_north_dev_inst[0], north_mgr->cmd_buff)) {
                continue;
            }
            s_backup_len = north_mgr->cmd_buff->data_len;
            rt_memcpy_s(s_backup_buff, MAX_NORTH_COMM_BUFF_LEN, north_mgr->cmd_buff->buf, s_backup_len);
            handle_received_data(north_mgr, &comm_fail_time);
        } else {
            if (s_backup_len > 0) {
                handle_modbus_data(north_mgr, &comm_fail_time);
            }
            s_north_dev_inst[0]->dev_type->link_inst->r_cache.index = 0;
            rt_memset_s(s_backup_buff, MAX_NORTH_COMM_BUFF_LEN, 0, MAX_NORTH_COMM_BUFF_LEN);
            s_backup_len = 0;
        }
        judge_north_comm_status(comm_fail_time);
        rt_thread_mdelay(time_delay);
        
    }
}

int judge_north_comm_status(rt_tick_t comm_fail_time)
{
    unsigned short delay = 0;
    get_one_para(DXCB_PARA_ID_COMM_INTERRUPT_JUDGE_DELAY_OFFSET, &delay);
    RETURN_VAL_IF_FAIL(get_tick_difference(rt_tick_get(), comm_fail_time) / 1000 > delay, SUCCESSFUL);
    char comm_fail = TRUE;
    set_one_data(DCXB_TRACE_ID_NORTH_COMM_FAIL_ALARM, &comm_fail);
    return SUCCESSFUL;
}

