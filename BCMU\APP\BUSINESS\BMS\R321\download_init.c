#include "download_init.h"
/*
#include "lwip/err.h"
#include "lwip/pbuf.h"
#include "lwip/tcp.h"
#include "lwip/tcpip.h"
#include "netif/etharp.h"
#include "netif/ethernetif.h"
#include "netif/ethernet.h"
*/
#define IP_ADDR0                    10
#define IP_ADDR1                    239
#define IP_ADDR2                    92
#define IP_ADDR3                    67
  
  /*NETMASK*/
#define NETMASK_ADDR0               255
#define NETMASK_ADDR1               255
#define NETMASK_ADDR2               255
#define NETMASK_ADDR3                 0
 
 /*Gateway Address*/
#define GW_ADDR0                    10
#define GW_ADDR1                    239
#define GW_ADDR2                    92
#define GW_ADDR3                    1
/*
struct netif gnetif;
ip4_addr_t ipaddr;
ip4_addr_t netmask;
ip4_addr_t gw;
*/
void download_init(void)
{
    //TODO:清理之前的连接资源（网卡配置待解决）
    /*tcpip_init(NULL, NULL);

    IP4_ADDR(&ipaddr,IP_ADDR0,IP_ADDR1,IP_ADDR2,IP_ADDR3);
    IP4_ADDR(&netmask,NETMASK_ADDR0,NETMASK_ADDR1,NETMASK_ADDR2,NETMASK_ADDR3);
    IP4_ADDR(&gw,GW_ADDR0,GW_ADDR1,GW_ADDR2,GW_ADDR3);

    netif_add(&gnetif, &ipaddr, &netmask, &gw, NULL, eth_netif_device_init, tcpip_input);
    netif_set_default(&gnetif);

     if (netif_is_link_up(&gnetif))
    {
        netif_set_up(&gnetif);
    }
    else
    {
        netif_set_down(&gnetif);
    }*/
}
