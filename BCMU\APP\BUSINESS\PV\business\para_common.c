#include "para_common.h"
#include "data_type.h"
#include "msg_id.h"
#include "msg.h"
#include "cmd.h"
#include "softbus.h"
#include "para_id_in.h"
#include "para_manage.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "storage.h" 
#include "partition_def.h"
#include "his_data.h"
#include "utils_rtthread_security_func.h"
#include "utils_data_transmission.h"
#include "his_record.h"
#include "partition_def.h"

unsigned char g_set_485_conf_flg = 0;
unsigned char g_rtn_south_to_north = 0;
power_off_reason_t g_power_off_reason = {0};

static rt_sem_t rtn_sem;

void init_rtn_sem()
{
    rtn_sem = rt_sem_create("rtn_sem", 0, RT_IPC_FLAG_FIFO);
}

rt_sem_t get_rtn_sem()
{
    return rtn_sem;
}

unsigned char get_rtn_south_to_north_flag()
{
    return g_rtn_south_to_north;
}

unsigned char set_rtn_south_to_north_flag(unsigned char flag)
{
    // 当g_rtn_south_to_north为1的时候，表示命令成功，非1的时候表示失败。为了兼容南向设备通讯断的情况。
    g_rtn_south_to_north = flag;
    return g_rtn_south_to_north;
}

int get_restart_south_rtn()
{
    unsigned short status = RESTART_POWER_VAL;
    unsigned char ctrl_rtn = RTN_FAILURE;
    unsigned char send_data[3] = {0};
    send_data[0] = DC_AC_IV_CTRL_ORDER & 0xFF;
    send_data[1] = DC_AC_IV_CTRL_ORDER >> 8;
    send_data[2] = 1;
    set_one_data(DAC_CTRL_ID_PV_CTRL_ORDER, &status);
    send_msg_to_thread(EXE_DEST_CMD_MSG, MOD_DC_AC, send_data, sizeof(send_data));
    if(RT_EOK == rt_sem_take(rtn_sem, 3000))
    {
        ctrl_rtn = g_rtn_south_to_north;
    }
    if(ctrl_rtn != RTN_SUCCEFUL)
    {
        g_rtn_south_to_north = 0; // 恢复默认南向rtn标志
        save_real_ctrl_cmd_event(DAC_CTRL_ID_SYSTEM_RESTART, CTRL_ID_TYPE, "system restart fail");//保存操作记录
        LOG_E("%s:%d|system restart fail", __FUNCTION__ , __LINE__);
        return FAILURE;
    }
    g_rtn_south_to_north = 0; // 恢复默认南向rtn标志
    save_real_ctrl_cmd_event(DAC_CTRL_ID_SYSTEM_RESTART, CTRL_ID_TYPE, "system restart succ");//保存操作记录
    LOG_E("%s:%d|system restart succ", __FUNCTION__ , __LINE__);
    reset_csu();
    return SUCCESSFUL;
}

int set_rs485_flag()
{
    g_set_485_conf_flg = 1;
    return SUCCESSFUL;
}

unsigned char* get_rs485_flag()
{
    return &g_set_485_conf_flg;
}

int send_set_para_cmd_msg(unsigned int* sid_list, unsigned int sid_num)
{
    // 说明有参数需要下发给功率
    single_para_msg_t single_para_msg = {0};

    if(sid_num == 0)
    {
        return SUCCESSFUL;
    }
    unsigned char send_data[3] = {0};
    single_para_msg.cmd_id = DC_AC_WRITE_DATA;
    single_para_msg.dev_no = 1;
    single_para_msg.sid_list = sid_list;
    single_para_msg.sid_list_len = sid_num;
    send_msg_to_thread(SET_CHG_PARA_MSG, MOD_DC_AC, &single_para_msg, sizeof(single_para_msg));
    rt_sem_t rtn_sem = get_rtn_sem();
    unsigned char set_para_rtn = RTN_FAILURE;
    if(RT_EOK == rt_sem_take(rtn_sem, 3000))
    {
        set_para_rtn = g_rtn_south_to_north;
    }
    else
    {
        save_real_ctrl_cmd_event(DAC_DATA_ID_SET_PARA_RTN_STATUS, REALDATA_ID_TYPE, "return rtn timeout");
        LOG_E("%s:%d|set para power return rtn timeout!", __FUNCTION__ , __LINE__);
    }
    g_rtn_south_to_north = 0; // 恢复默认南向rtn标志
    send_data[0] = DC_AC_GET_PARA_DATA & 0xFF;
    send_data[1] = DC_AC_GET_PARA_DATA >> 8;
    send_data[2] = 1;
    send_msg_to_thread(EXE_DEST_CMD_MSG, MOD_DC_AC, send_data, sizeof(send_data));
    if(set_para_rtn != RTN_SUCCEFUL)
    {
        return FAILURE;
    }
    return SUCCESSFUL;
}

void backup_power_ctrl_para(int south_cmd_id)
{
    // 保存有功功率调节备份
    if(south_cmd_id == DC_AC_SET_ACT_REGULATION_PARA_DATA)
    {
        short data_s = 0;
        float data_f = 0.0;
        get_one_para(DAC_PARA_ID_GRID_ACTIVE_POWER_CHANGE_OFFSET, &data_f);
        set_one_para(DAC_PARA_ID_GRID_ACTIVE_POWER_CHANGE_OFFSET+1, &data_f, TRUE, FALSE);

        get_one_para(DAC_PARA_ID_ACTIVE_POWER_CONTROL_MODE_OFFSET, &data_s);
        set_one_para(DAC_PARA_ID_ACTIVE_POWER_CONTROL_MODE_OFFSET+1, &data_s, TRUE, FALSE);

        get_one_para(DAC_PARA_ID_ACTIVE_POWER_DERATING_SETTING_OFFSET, &data_f);
        set_one_para(DAC_PARA_ID_ACTIVE_POWER_DERATING_SETTING_OFFSET+1, &data_f, TRUE, FALSE);

        get_one_para(DAC_PARA_ID_ACTIVE_POWER_DERATING_PERCENT_OFFSET, &data_f);
        set_one_para(DAC_PARA_ID_ACTIVE_POWER_DERATING_PERCENT_OFFSET+1, &data_f, TRUE, FALSE);
    }

    // 保存无功功率调节备份
    if(south_cmd_id == DC_AC_SET_REACT_REGULATION_PARA_DATA)
    {
        short data_s = 0;
        float data_f = 0.0;
        get_one_para(DAC_PARA_ID_GRID_REACTIVE_POWER_CHANGE_OFFSET, &data_f);
        set_one_para(DAC_PARA_ID_GRID_REACTIVE_POWER_CHANGE_OFFSET+1, &data_f, TRUE, FALSE);

        get_one_para(DAC_PARA_ID_REACTIVE_POWER_COMPEN_MODE_OFFSET, &data_s);
        set_one_para(DAC_PARA_ID_REACTIVE_POWER_COMPEN_MODE_OFFSET+1, &data_s, TRUE, FALSE);

        get_one_para(DAC_PARA_ID_REACTIVE_POWER_COMPEN_SETTING_FACTOR_OFFSET, &data_f);
        set_one_para(DAC_PARA_ID_REACTIVE_POWER_COMPEN_SETTING_FACTOR_OFFSET+1, &data_f, TRUE, FALSE);

        get_one_para(DAC_PARA_ID_REACTIVE_POWER_COMPEN_SETTING_OFFSET, &data_f);
        set_one_para(DAC_PARA_ID_REACTIVE_POWER_COMPEN_SETTING_OFFSET+1, &data_f, TRUE, FALSE);

        get_one_para(DAC_PARA_ID_REACTIVE_POWER_REGULATIONG_TIME_OFFSET, &data_f);
        set_one_para(DAC_PARA_ID_REACTIVE_POWER_REGULATIONG_TIME_OFFSET+1, &data_f, TRUE, FALSE);
    }
}

void start_iv_callback() 
{
    unsigned char iv_complete_status = 0;
    set_one_data(DAC_DATA_ID_IV_COMPLETE_STATUS, &iv_complete_status);
    send_msg_to_thread(STOP_IV_SCAN_MSG, MOD_SYS_MANAGE, NULL, 0);
    send_msg_to_thread(MQTT_REAL_UP_MSG, MOD_MQTT, NULL, 0);
    storage_unlink(IV_DATA_FILE);
    return ;
}

void stop_iv_callback() 
{
    unsigned char iv_complete_status = 1;
    set_one_data(DAC_DATA_ID_IV_COMPLETE_STATUS, &iv_complete_status);
    send_msg_to_thread(STOP_IV_SCAN_MSG, MOD_SYS_MANAGE, NULL, 0);
    send_msg_to_thread(MQTT_REAL_UP_MSG, MOD_MQTT, NULL, 0);
    return ;
}

void reset_csu(void)
{
     send_msg_to_thread(CSU_RESET, MOD_SYS_MANAGE, NULL, 0);
}

void deal_restore_factory(void)
{
    send_msg_to_thread(RESTORE_FACTORY, MOD_SYS_MANAGE, NULL, 0);
}

power_off_reason_t* get_power_off_reason()
{
    return &g_power_off_reason;
}



int get_afci_file_handle()
{
    // 获取信号量
    rt_sem_t rtn_sem = get_rtn_sem();
    char afci_south_ret = TRUE;
    char afci_is_valid = FALSE;
    // 文件名定义
    char fime_name[] = "afci_data.bin";
    if (rtn_sem == RT_NULL)
    {
        LOG_E("%s:%d|Failed to get semaphore!", __FUNCTION__, __LINE__);
        return FAILURE;
    }

    // 尝试获取信号量，超时时间为3000毫秒
    if (rt_sem_take(rtn_sem, 3000) != RT_EOK)
    {
        LOG_E("%s:%d|Failed to take ctrlCmd semaphore!", __FUNCTION__, __LINE__);
        afci_south_ret = FALSE;
    }

    // 获取南北向传输标志
    if (get_rtn_south_to_north_flag() != RTN_SUCCEFUL)
    {
        LOG_E("%s:%d|Get AFCI FILE ctrlCmd Failed!", __FUNCTION__, __LINE__);
        afci_south_ret = FALSE;
    }

    // 发送消息触发文件上传
    send_msg_to_thread(TRIG_SOUTH_FILE_UPLOAD, MOD_SYS_MANAGE, fime_name, sizeof(fime_name));

    // 获取功率文件是否更新
    // 尝试获取信号量，超时时间为3000毫秒
    if (rt_sem_take(rtn_sem, 3000) != RT_EOK)
    {
        LOG_E("%s:%d|Failed to take File semaphore!", __FUNCTION__, __LINE__);
        afci_south_ret = FALSE;
    }

    // 获取功率文件是否存在
    if (get_rtn_south_to_north_flag() != RTN_SUCCEFUL)
    {
        LOG_E("%s:%d|Power AFCI FILE no update!", __FUNCTION__, __LINE__);
        afci_south_ret = FALSE;
    }

    if(afci_south_ret == FALSE)
    {
        struct stat file_stat = {0};
        int ret = 0;
        ret = storage_stat(AFCI_FILE_PART, &file_stat);
        afci_is_valid = (ret >= 0 && file_stat.st_size > 0) ? TRUE : FALSE;
        // 功率返回错误rtn，且文件存在监控时，不触发南向导出
        if(afci_is_valid == TRUE)
        {
            unsigned char afci_sta = 1;
            set_one_data(DAC_DATA_ID_AFCI_FILE_STATUS, &afci_sta); // AFCI文件存在
            return SUCCESSFUL;
        }
        else
        {
            return FAILURE;
        }

    }

    return SUCCESSFUL;
}



void send_clean_his_data_msg()
{
    send_msg_to_thread(CLEAN_HIS_DATA_MSG, MOD_SYS_MANAGE, NULL, 0);
}

void send_clean_his_event_msg()
{
    send_msg_to_thread(CLEAN_HIS_EVENT_MSG, MOD_SYS_MANAGE, NULL, 0);
}

void send_clean_iv_data_msg()
{
    send_msg_to_thread(CLEAN_IV_DATA_MSG, MOD_SYS_MANAGE, NULL, 0);
}

void send_clean_fault_record_msg()
{
    send_msg_to_thread(CLEAN_FAULT_RECORD_MSG, MOD_SYS_MANAGE, NULL, 0);
}

/**
 * @brief 发送清除告警消息
 * @note  清除历史告警和指定的实时告警
*/
void send_clean_alarm_msg() {
    // 清除实时告警
    send_msg_to_thread(CLEAN_REAL_ALARM_MSG, MOD_ALARM_MANAGE, NULL, 0);
    // 清除历史告警
    send_msg_to_thread(CLEAN_HIS_ALARM_MSG, MOD_SYS_MANAGE, NULL, 0);
}

void send_delete_his_energy_msg()
{
    send_msg_to_thread(DELETE_HIS_ENERGY_MSG, MOD_SAMPLE, NULL, 0);//发消息到sample清除历史电量
    return ;
}

