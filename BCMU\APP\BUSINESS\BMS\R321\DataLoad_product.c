#include "alarm_in.h"
#include "battery.h"
#include "prtclDL.h"
#include "realAlarm.h"
#include "DataLoad_product.h"
#include "sample.h"
#include <stddef.h>


/***************************************************************************
 * @brief    是否禁止放电
 **************************************************************************/
Bool IsDischargeForbidden(T_BCMAlarmStruct const *ptBcmAlarm, T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn)
{
    if((ptBcmAlarm == NULL) || (pHardwarePara==NULL) || (pBattIn == NULL))
    {
        return FALSE;
    }
    WORD wAlmIndex = -1;
    T_AlarmConfigStruct atAlarmConfig[] = {
        {offsetof(T_BCMAlarmStruct, ucDischgCurrHighPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucBoardTempHighPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucBattUnderVoltPrt), 1},
        {offsetof(T_BCMAlarmStruct, aucCellUnderVoltPrt[0]), pHardwarePara->ucCellVoltNum},
        {offsetof(T_BCMAlarmStruct, aucCellDynamicUnderVoltPrt[0]), pHardwarePara->ucCellVoltNum},
        {offsetof(T_BCMAlarmStruct, aucDischgTempHighPrt[0]), pHardwarePara->ucCellTempNum},
        {offsetof(T_BCMAlarmStruct, aucDischgTempLowPrt[0]), pHardwarePara->ucCellTempNum},
        {offsetof(T_BCMAlarmStruct, ucBattSOCLowPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucBattSOHPrt), 1},
        {offsetof(T_BCMAlarmStruct, aucCellDamagePrt[0]), pHardwarePara->ucCellVoltNum},
        {offsetof(T_BCMAlarmStruct, ucBattShortCut), 1},
        {offsetof(T_BCMAlarmStruct, ucBattReverse), 1},
        {offsetof(T_BCMAlarmStruct, ucBduEepromAlm), 1},
        {offsetof(T_BCMAlarmStruct, ucChgLoopInvalid), 1},
        {offsetof(T_BCMAlarmStruct, ucDischgLoopInvalid), 1},
        {offsetof(T_BCMAlarmStruct, ucCurrLimLoopInvalid), 1},
        {offsetof(T_BCMAlarmStruct, ucBDUBusVoltHighPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucInsideTempHighPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucBDUBattVoltLowPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucBDUBusVoltLowPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucBattLoseAlm), 1},
        {offsetof(T_BCMAlarmStruct, ucCellVoltSampleFault), 1},
        {offsetof(T_BCMAlarmStruct, ucCellPoorConsisPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucCellTempSensorInvalidAlm), pHardwarePara->ucCellTempNum},
        {offsetof(T_BCMAlarmStruct, ucEnvTempHighPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucEnvTempLowPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucBDUCommFail), 1},
        {offsetof(T_BCMAlarmStruct, ucBDUBattLockAlm), 1},
        {offsetof(T_BCMAlarmStruct, ucEqualCircuitFaultAlm), 1},
        {offsetof(T_BCMAlarmStruct, ucBDUConnTempHighPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucHeaterFilmFailure), 1},
        {offsetof(T_BCMAlarmStruct, ucFireControlAlm), 1},
        {offsetof(T_BCMAlarmStruct, ucCellTempRiseAbnormal),1}, //单体温升速率异常告警
        {offsetof(T_BCMAlarmStruct, ucDcrFaultPrt), 1},
        {offsetof(T_BCMAlarmStruct, ucBattFaultTempHighAlm),1}, //电池异常高温保护告警
        {offsetof(T_BCMAlarmStruct, ucSiteAntitheftAlm), 1},
        {offsetof(T_BCMAlarmStruct, ucNetAntitheftAlm), 1},
    };

    /* 升级过程禁止放电 */
    if (IsUpdate())
    {
        return TRUE;
    }

    if (IsExistForbiddenAlarm(&atAlarmConfig[0], ptBcmAlarm, sizeof(atAlarmConfig) / sizeof(atAlarmConfig[0]), &wAlmIndex))
    {
        return TRUE;
    }

    if (IsExistForbiddenDischargeTemperature(pHardwarePara, pBattIn))
    {
        return TRUE;
    }

    return FALSE;
}

/***************************************************************************
 * @brief    是否存在激活口需要的欠压保护
 **************************************************************************/
BOOLEAN IsActivatePortUVP(T_BCMAlarmStruct const *ptBcmAlarm, T_HardwareParaStruct *pHardwarePara)
{
    if(ptBcmAlarm == NULL || pHardwarePara == NULL)
    {
        return FALSE;
    }
    WORD wAlmIndex = -1;
    T_AlarmConfigStruct atAlarmOtherConfig[] = {
        {offsetof(T_BCMAlarmStruct, ucDischgCurrHighPrt), 1},                                        //  放电过流保护
        {offsetof(T_BCMAlarmStruct, ucBoardTempHighPrt), 1},                                         //  单板过温保护
        {offsetof(T_BCMAlarmStruct, aucDischgTempHighPrt[0]), pHardwarePara->ucCellTempNum},         //  单体放电高温保护
        {offsetof(T_BCMAlarmStruct, aucDischgTempLowPrt[0]), pHardwarePara->ucCellTempNum},          //  单体放电低温保护
        {offsetof(T_BCMAlarmStruct, ucCellPoorConsisPrt), 1},                                        //  单体一致性差保护
        {offsetof(T_BCMAlarmStruct, ucBattSOHPrt), 1},                                               //  电池SOH低保护
        {offsetof(T_BCMAlarmStruct, aucCellDamagePrt[0]), pHardwarePara->ucCellVoltNum},             //  单体损坏保护
        {offsetof(T_BCMAlarmStruct, ucBattLoseAlm), 1},                                              //  电池丢失告警
        {offsetof(T_BCMAlarmStruct, ucBduEepromAlm), 1},                                             //  BDU EEPROM故障
        {offsetof(T_BCMAlarmStruct, ucCellVoltSampleFault), 1},                                      //  单体电压采样异常
        {offsetof(T_BCMAlarmStruct, ucChgLoopInvalid), 1},                                           //  充电回路失效
        {offsetof(T_BCMAlarmStruct, ucDischgLoopInvalid), 1},                                        //  放电回路失效
        {offsetof(T_BCMAlarmStruct, ucCurrLimLoopInvalid), 1},                                       //  限流回路失效
        {offsetof(T_BCMAlarmStruct, ucBattShortCut), 1},                                             //  电池短路
        {offsetof(T_BCMAlarmStruct, ucBattReverse), 1},                                              //  电池反接
        {offsetof(T_BCMAlarmStruct, ucCellTempSensorInvalidAlm[0]), pHardwarePara->ucCellTempNum},   //  单体温度无效
        {offsetof(T_BCMAlarmStruct, ucInsideTempHighPrt), 1},                                        //  机内过温保护
        {offsetof(T_BCMAlarmStruct, ucBDUBattVoltLowPrt), 1},                                        //  BDU电池欠压保护
        {offsetof(T_BCMAlarmStruct, ucBDUBusVoltLowPrt), 1},                                         //  BDU母排欠压保护
        {offsetof(T_BCMAlarmStruct, ucBDUBusVoltHighPrt), 1},                                        //  BDU母排过压保护
        {offsetof(T_BCMAlarmStruct, ucBDUCommFail), 1},                                              //  BDU通信断
        {offsetof(T_BCMAlarmStruct, ucBattVoltSampleAlm), 1},                                        //  电压采样故障
        {offsetof(T_BCMAlarmStruct, ucBDUBattChgVoltLowPrt), 1},                                     //  BDU电池充电欠压保护
        {offsetof(T_BCMAlarmStruct, ucBDUBattLockAlm), 1},                                           //  BDU电池闭锁告警
        {offsetof(T_BCMAlarmStruct, ucEnvTempHighPrt), 1},                                           //  环境温度高保护
        {offsetof(T_BCMAlarmStruct, ucEnvTempLowPrt), 1},                                            //  环境温度低保护
        {offsetof(T_BCMAlarmStruct, ucEqualCircuitFaultAlm), 1},                                     //  均衡电路故障告警
        {offsetof(T_BCMAlarmStruct, ucBattVoltSampleAlm), 1},                                        //  电压采样故障
        {offsetof(T_BCMAlarmStruct, ucHeaterFilmFailure), 1},                                        //  加热膜失效
        {offsetof(T_BCMAlarmStruct, ucBDUConnTempHighPrt), 1},                                       //  连接器温度高保护
        {offsetof(T_BCMAlarmStruct, ucMainRelayFail), 1},                                            //  主继电器失效
        {offsetof(T_BCMAlarmStruct, ucDCDCErr), 1},                                                  //  DCDC故障
        {offsetof(T_BCMAlarmStruct, ucSampleErr), 1},                                                //  采集异常
        {offsetof(T_BCMAlarmStruct, ucAuxiSourceErr), 1},                                            //  辅助源故障
        {offsetof(T_BCMAlarmStruct, ucFireControlAlm), 1},                                           //  消防告警
        {offsetof(T_BCMAlarmStruct, ucSiteAntitheftAlm), 1},                                         //  站点防盗告警
        {offsetof(T_BCMAlarmStruct, ucNetAntitheftAlm), 1},                                          //  网管防盗告警
    };

    if (IsExistForbiddenAlarm(&atAlarmOtherConfig[0], ptBcmAlarm, sizeof(atAlarmOtherConfig) / sizeof(atAlarmOtherConfig[0]), &wAlmIndex))
    {
        return TRUE;
    }

    return FALSE;
}

/***************************************************************************
 * @brief    载入产品特性电池数据
 **************************************************************************/
void LoadProductCharacteristicData(T_BattInfo *pBattIn, T_SysPara *pSysPara)
{
    return;
}

/***************************************************************************
 * @brief    qtp模式下对部分电池数据覆写(注意位置)
 **************************************************************************/
void QtpBattDataOverWrite(T_BattInfo *pBattIn, T_BCMDataStruct *pBcmData, T_SysPara *pSysPara)
{
    if((pBattIn == NULL) || (pBcmData==NULL) || (pSysPara == NULL))
    {
        return;
    }

    if (GetQtptestFlag()) // 在外协模式下,采用纯锂电场景
    {
        pBattIn->tPara.fChargeMaxCurr = pSysPara->fChgCurrHighAlmThre - 0.05;
        pBattIn->tPara.ucUsageScen = DISCHG_MODE_BATT_CHARACTERISTIC;
        if (pBcmData->ucBattPackSta == BATT_MODE_DISCHARGE)
        {
            pBattIn->tPara.ucDischargeMode = BATT_DISCHARGE_FOLLOW;
            pBattIn->tPara.bThroughDischgEnable = True;
        }
    }
    return;
}

/***************************************************************************
 * @brief    载入电池系统参数
 **************************************************************************/
void LoadBattSyspara(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal, T_SysPara *pSyspara, T_BCMDataStruct *pBcmData)
{
    if((pBattIn == NULL) || (pBattDeal==NULL) || (pSyspara == NULL) || (pBcmData == NULL))
    {
        return;
    }

    pBattIn->tPara.fChargeFullVol = pSyspara->fBattChgFullAverVoltThre / g_ProConfig.fVoltTrasRate;
    pBattIn->tPara.fChagEndCurCoeff = pSyspara->fBattChgFullAverCurrThre;
    pBattIn->tPara.wChagMaxMinute = pSyspara->wChgMaxDura;
    pBattIn->tPara.wChagEndHoldMinute = pSyspara->wChgEndDura;
    pBattIn->tPara.fBattRefreshVoltThreshold = pSyspara->fBattSupplVolt / g_ProConfig.fVoltTrasRate;
    pBattIn->tPara.wBattRefreshPeriod = pSyspara->wBattSupplPeriod;
    pBattIn->tPara.fCellSupplVolt = pSyspara->fCellSupplVolt;
    pBattIn->tPara.ucDischargeMode = BATT_DISCHARGE_FOLLOW; // R321删除放电方式参数，只支持15串跟随方式放电
    pBattIn->tPara.bThroughDischgEnable = pSyspara->bThroughDischgEnable;
    pBattIn->tPara.fEqulizationStartThreshold = pSyspara->fCellEquVoltDiffThre;
    pBattIn->tPara.fChargeMaxCurr = pSyspara->fChargeMaxCurr;
    pBattIn->tPara.fCellChargeFullVolt = pSyspara->fCellChargeFullVolt;
    pBattIn->tPara.wSwitchSOC = pSyspara->wDischgSwitchSOC;
    pBattIn->tPara.wSwitchSOC2 = pSyspara->wDischgSwitchSOC2;
    pBattIn->tPara.ucDODOfDischarge = pSyspara->ucDODPerDischarge;
    pBattIn->tPara.fRemoteSuplyVolt = pSyspara->fRemoteSupplyOutVolt;
    pBattIn->tPara.fChgHighTempPrt = pSyspara->fChgTempHighPrtThre;
    pBattIn->tPara.fChgLowTempPrt = pSyspara->fChgTempLowPrtThre;
    pBattIn->tPara.fDischgHighTempPrt = pSyspara->fDischgTempHighPrtThre;
    pBattIn->tPara.fDischgLowTempPrt = pSyspara->fDischgTempLowPrtThre;
    pBattIn->tPara.bChargeRotate = pSyspara->bChargeRotate;
    pBattIn->tPara.ucCycleMode = pSyspara->ucCycleMode;
    pBattIn->tPara.fLASwitchVolt = pSyspara->fLABattSwitchVolt;
    pBattIn->tPara.ucDischargeRate = pSyspara->ucLiAcidDischargeRate;
    pBattIn->tPara.wLABattDurPerDischg = pSyspara->wLABattDurationPerDischarge;
    pBattIn->tPara.fOutVoltOffset = pSyspara->fOutputOffsetVoltThre;
    pBattIn->tPara.bRemoSupplyDischgSwitchSOC = pSyspara->wDischgSwitchSOC;
    pBattIn->tPara.bRemoSupplyDischgSwitchSOC2 = pSyspara->wDischgSwitchSOC2;
    pBattIn->tPara.bBoostChg = pSyspara->bBoostCharge;
    pBattIn->tPara.fDischargeEndVolt1 = pSyspara->fDischargeEndVolt1;
    pBattIn->tPara.fDischargeEndVolt2 = pSyspara->fDischargeEndVolt2;
    pBattIn->tPara.fSelfDischgACR= pSyspara->fSelfDischgACR/100;//自放电容量比率
    pBattIn->tPara.fCapDCDR= pSyspara->fCapDCPRFaultAlmThre/100;//容量衰减一致性差告警比率阈值
    if (pBattIn->tPara.ucRunMode != pSyspara->ucRunMode)
    {
        pBattIn->tPara.ucRunMode = pSyspara->ucRunMode;
        SetProtoSetInfoInvalid(INFO_TYPE_ALL);
    }
    if (pSyspara->ucUsageScen == DISCHG_MODE_BATT_CHARACTERISTIC)
    {
        // pBattDeal->fPowerDownVolt = pBcmData->fBattVolt * BATT_SOC_MAX;

        pBattIn->tPara.fSysPowerOnThreshold = MAX(pBcmData->fBattVolt * g_ProConfig.fVoltTrasRate, pBattDeal->fDischargeHopeVol) + 0.6;
        pBattIn->tPara.fSysPowerOffThreshold = pBcmData->fBattVolt * g_ProConfig.fVoltTrasRate;

    }
    else if (pSyspara->ucUsageScen == DISCHG_MODE_CONSTANT_VOLTAGE)
    {
        // pBattDeal->fPowerDownVolt = pSyspara->fRemoteSupplyOutVolt;
        pBattIn->tPara.fSysPowerOnThreshold = pSyspara->fRemoteSupplyOutVolt+0.6;
        pBattIn->tPara.fSysPowerOffThreshold = pSyspara->fRemoteSupplyOutVolt;
    }
    
    pBattIn->tPara.wPowerOnCheckTime = pSyspara->wPowerOnDetermineTime;
    pBattIn->tPara.ucChargeMap = pSyspara->bChargeMapEnable;
    pBattIn->tPara.bSagEqualCurr = pSyspara->bSagEqualCurr;
    return;
}