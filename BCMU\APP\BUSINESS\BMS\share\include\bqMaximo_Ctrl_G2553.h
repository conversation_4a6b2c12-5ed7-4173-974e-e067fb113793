/**************************************************************************
* Copyright (C) 2001, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
 * bq769x0 CRC code example header file
 *
***************************************************************************/
#ifndef SOFTWARE_SRC_APP_BQMAXIMO_H_
#define SOFTWARE_SRC_APP_BQMAXIMO_H_

#include "bsp_bq769x.h"

#define LOWBYTE(Data)  (unsigned char)(0x00ff & Data)
#define HIGHBYTE(Data) (unsigned char)((0xff00 & Data)>>8)

#define CRC_KEY 7

#define DISABLE_INT asm(" BIC #8,SR")

#define DELAY_LIMIT 0xffff

//#define SYS_STAT 0
//#define CELLBAL1 1
//#define CELLBAL2 2
//#define CELLBAL3 3
//#define SYS_CTRL1 4
//#define SYS_CTRL2 5
#define PROTECT1    0x06
#define PROTECT2    0x07
#define PROTECT3    0x08
#define OV_TRIP     0x09
#define UV_TRIP     0x0A
#define VC1_HI_BYTE 0x0C

#define ADCGAIN1    0x50
#define ADCOFFSET   0x51
#define ADCGAIN2    0x59

#define SCD_DELAY_50us        0x0
#define SCD_DELAY_100us       0x1
#define SCD_DEALY_200us       0x2
#define SCD_DELAY_400us       0x3

#define OV_THRESH_BASE        0x2008
#define UV_THRESH_BASE        0x1000
#define OV_STEP               0x10
#define UV_STEP               0x10

#define ADCGAIN_BASE          365

#define LOW_BYTE(Data)        (unsigned char)(0xff & Data)
#define HIGH_BYTE(Data)       (unsigned char)(0xff & (Data >> 8))

#endif  //SOFTWARE_SRC_APP_BQMAXIMO_H_
