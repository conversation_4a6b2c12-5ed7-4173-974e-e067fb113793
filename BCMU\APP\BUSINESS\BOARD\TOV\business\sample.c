#include "sample.h"
#include <rtthread.h>
#include "data_type.h"
#include "utils_thread.h"
#include "utils_server.h"
#include "msg_id.h"
#include "storage.h"
#include "partition_def.h"
#include "utils_data_transmission.h"
#include "utils_rtthread_security_func.h"
#include "para_manage.h"
#include "para_id_in.h"
#include "math.h"
#include "pin_define.h"
#include "pin_ctrl.h"
#include "io_ctrl_api.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "log_mgr_api.h"

unsigned char s_continue_trig_count[11] = {0};
static unsigned int s_sample_data_index = 0;
static record_data_sample_info_t s_sample_info = {};
static server_info_t *s_server_info = NULL;
rt_device_t g_ad_device;
_rt_server_t g_sample_server;

Static msg_map s_sample_msg_map[] = 
{
    {UPDATE_THRESHOLD,   msg_handle_nothing},
    {UPDATE_CHAN_VALID,  msg_handle_nothing},
    {CTRL_SAMPLE_SWITCH, msg_handle_nothing},
};

Static sample_msg_handle_process_t s_msg_handle[] =
{
    {UPDATE_THRESHOLD,      handle_update_threshold},
    {UPDATE_CHAN_VALID,     handle_update_chan_valid},
    {CTRL_SAMPLE_SWITCH,    handle_ctrl_sample_switch},
};

static unsigned short s_phase_a_vol_buff[MAX_POINT_NUM_PER_CHANNEL]  = {0};
static unsigned short s_phase_b_vol_buff[MAX_POINT_NUM_PER_CHANNEL]  = {0};
static unsigned short s_phase_c_vol_buff[MAX_POINT_NUM_PER_CHANNEL]  = {0};
static unsigned short s_phase_n_vol_buff[MAX_POINT_NUM_PER_CHANNEL]  = {0};
static unsigned short s_phase_a_curr_buff[MAX_POINT_NUM_PER_CHANNEL] = {0};
static unsigned short s_phase_b_curr_buff[MAX_POINT_NUM_PER_CHANNEL] = {0};
static unsigned short s_phase_c_curr_buff[MAX_POINT_NUM_PER_CHANNEL] = {0};
static unsigned short s_phase_n_curr_buff[MAX_POINT_NUM_PER_CHANNEL] = {0};
static unsigned short s_digit_vol_buff[MAX_POINT_NUM_PER_CHANNEL]    = {0};
static unsigned short s_digit_curr_buff[MAX_POINT_NUM_PER_CHANNEL]   = {0};

static unsigned short* s_phase_a_vol  = s_phase_a_vol_buff;
static unsigned short* s_phase_a_curr = s_phase_a_curr_buff;
static unsigned short* s_phase_b_vol  = s_phase_b_vol_buff;
static unsigned short* s_phase_b_curr = s_phase_b_curr_buff;
static unsigned short* s_phase_c_vol  = s_phase_c_vol_buff;
static unsigned short* s_phase_c_curr = s_phase_c_curr_buff;
static unsigned short* s_phase_n_vol  = s_phase_n_vol_buff;
static unsigned short* s_phase_n_curr = s_phase_n_curr_buff;
static unsigned short* s_digit_vol    = s_digit_vol_buff;
static unsigned short* s_digit_curr   = s_digit_curr_buff;

short  g_adc_trig_data_index = -1;         //录波触发的缓存数据偏移 
unsigned short   g_trig_cause = 0;
static unsigned int s_low_check_count = 0;
int g_led_count = -1;
sample_data_threshold_t g_data_threshold = {0};
sample_data_valid_t g_vaild_data_flag  = {0};
static unsigned int s_mqtt_upfile_count = 0;
static unsigned char s_save_num = 0;
static struct rt_semaphore sync_adc_rx_sem;

int print_threshold_info()
{
    rt_device_control(g_ad_device, SET_SYNC_ADC_STOP, RT_NULL);
    rt_sem_clear_value(&sync_adc_rx_sem);
    rt_kprintf("AC vol high |A:%d %d B:%d %d C:%d %d N:%d %d\n", 
               g_data_threshold.adc_a_ac_high_up_vol, g_data_threshold.adc_a_ac_high_down_vol, 
               g_data_threshold.adc_b_ac_high_up_vol, g_data_threshold.adc_b_ac_high_down_vol,
               g_data_threshold.adc_c_ac_high_up_vol, g_data_threshold.adc_c_ac_high_down_vol,
               g_data_threshold.adc_n_ac_high_up_vol, g_data_threshold.adc_n_ac_high_down_vol);

    rt_kprintf("AC vol low |A:%d B:%d C:%d\n", 
               g_data_threshold.adc_a_ac_low_vol, g_data_threshold.adc_b_ac_low_vol,
               g_data_threshold.adc_c_ac_low_vol);

    rt_kprintf("AC cur high|A:%d %d B:%d %d C:%d %d\n",
               g_data_threshold.adc_a_ac_high_up_curr, g_data_threshold.adc_a_ac_high_down_curr,
               g_data_threshold.adc_b_ac_high_up_curr, g_data_threshold.adc_b_ac_high_down_curr,
               g_data_threshold.adc_c_ac_high_up_curr, g_data_threshold.adc_c_ac_high_down_curr);

    rt_kprintf("DC cur: %d %d vol:high:%d %d low:%d %d\n",
               g_data_threshold.adc_dc_high_up_curr, g_data_threshold.adc_dc_high_down_curr, 
               g_data_threshold.adc_dc_high_up_vol, g_data_threshold.adc_dc_high_down_vol, 
               g_data_threshold.adc_dc_low_up_vol, g_data_threshold.adc_dc_low_down_vol);
    if(g_trig_cause == AC_A_LOW_VOL_CAUSE || g_trig_cause == AC_B_LOW_VOL_CAUSE || g_trig_cause == AC_C_LOW_VOL_CAUSE  || g_trig_cause == DC_LOW_VOL_CAUSE)
    {
        return FAILURE;
    }
    char ctrl_sample_switch[2] = {0};
    ctrl_sample_switch[0] = TRUE;
    ctrl_sample_switch[1] = FALSE;
    pub_msg_to_thread(CTRL_SAMPLE_SWITCH, &ctrl_sample_switch, sizeof(ctrl_sample_switch));
    return SUCCESSFUL;
}

rt_err_t sync_adc_rx_indicate(rt_device_t dev, rt_size_t size)
{
    rt_sem_release(&sync_adc_rx_sem);
    return RT_EOK;
}


// 定义一个辅助函数来计算平方和，以减少代码重复并提高可读性
unsigned long long calculate_squared_sum(const unsigned short* vol, unsigned int start, unsigned int end) {
    unsigned long long sum = 0;
    for (unsigned int i = start; i < end; ++i) {
        unsigned long long val = 14035 * vol[i] - 28744375; // 实际为val = 1.4035 * vol[i] - 2874.4375扩大了10000倍，后面整体再缩小
        sum += val * val;
    }
    return sum;
}

int find_low_trig_point(unsigned int su32_index, 
                        unsigned short* phase_a_vol, unsigned short* phase_b_vol, 
                        unsigned short* phase_c_vol)
{
    // 检查触发索引是否有效且索引是否对齐
    if (g_adc_trig_data_index != -1) {
        return FAILURE;
    }

    static unsigned short pre_phase_a_valid_value = 4096 + 100;
    static unsigned short pre_phase_b_valid_value = 4096 + 100;
    static unsigned short pre_phase_c_valid_value = 4096 + 100;

    unsigned long long  phase_a_temp_value = 0;
    unsigned long long phase_b_temp_value = 0;
    unsigned long long phase_c_temp_value = 0;

    unsigned int range_start = (su32_index >= 2000) ? su32_index - 2000 : 0;
    unsigned int range_end = su32_index;
    unsigned int wrap_start = 3000 - (2000 - su32_index);
    const unsigned int wrap_end = 3000;
    // 计算平方和
    phase_a_temp_value = calculate_squared_sum(phase_a_vol, range_start, range_end);
    phase_b_temp_value = calculate_squared_sum(phase_b_vol, range_start, range_end);
    phase_c_temp_value = calculate_squared_sum(phase_c_vol, range_start, range_end);

    if (su32_index < 2000) {
        phase_a_temp_value += calculate_squared_sum(phase_a_vol, wrap_start, wrap_end);
        phase_b_temp_value += calculate_squared_sum(phase_b_vol, wrap_start, wrap_end);
        phase_c_temp_value += calculate_squared_sum(phase_c_vol, wrap_start, wrap_end);
    }

    // 计算有效值
    // 2000 * 10000 * 10000 (前面整体扩大，这个整体缩小值不变)
    const unsigned long long divisor = 200000000000;
    unsigned short phase_a_valid_value = (unsigned short)(sqrt(phase_a_temp_value / divisor));
    unsigned short phase_b_valid_value = (unsigned short)(sqrt(phase_b_temp_value / divisor));
    unsigned short phase_c_valid_value = (unsigned short)(sqrt(phase_c_temp_value / divisor));

    unsigned short start_index = (su32_index >= 2000) ? (su32_index - 2000) : (3000 - (2000 - su32_index));

    // 检查阈值
    if (phase_a_valid_value < g_data_threshold.adc_a_ac_low_vol && abs(phase_a_valid_value - pre_phase_a_valid_value) >= g_data_threshold.adc_ac_low_vol_dev && g_vaild_data_flag.phase_a_vol_valid_flag) {
        rt_kprintf("A:%d, %d\n", phase_a_valid_value, pre_phase_a_valid_value);
        g_adc_trig_data_index = start_index;
        g_trig_cause = AC_A_LOW_VOL_CAUSE;
    } else if (phase_b_valid_value < g_data_threshold.adc_b_ac_low_vol && abs(phase_b_valid_value - pre_phase_b_valid_value) >= g_data_threshold.adc_ac_low_vol_dev && g_vaild_data_flag.phase_b_vol_valid_flag) {
        rt_kprintf("B:%d, %d\n", phase_b_valid_value, pre_phase_b_valid_value);
        g_adc_trig_data_index = start_index;
        g_trig_cause = AC_B_LOW_VOL_CAUSE;
    } else if (phase_c_valid_value < g_data_threshold.adc_c_ac_low_vol && abs(phase_c_valid_value - pre_phase_c_valid_value) >= g_data_threshold.adc_ac_low_vol_dev && g_vaild_data_flag.phase_c_vol_valid_flag) {
        rt_kprintf("C:%d, %d\n", phase_c_valid_value, pre_phase_c_valid_value);
        g_adc_trig_data_index = start_index;
        g_trig_cause = AC_C_LOW_VOL_CAUSE;
    }
    pre_phase_a_valid_value = phase_a_valid_value;
    pre_phase_b_valid_value = phase_b_valid_value;
    pre_phase_c_valid_value = phase_c_valid_value;

    return SUCCESSFUL;
}



int is_over_threshold(unsigned short *buf, unsigned short up_threshold, unsigned short down_threshold, 
                        unsigned int su32_index, unsigned short cause, unsigned char valid_flag, int sample_channel_no) {
    unsigned char overVol_trig_point_num = 5;
    if (valid_flag && (*buf > up_threshold || *buf < down_threshold)) {
        s_continue_trig_count[sample_channel_no] ++;
        get_one_para(TOV_PARA_ID_VOL_HIGH_POINT_NUMBER_OFFSET, &overVol_trig_point_num);
        if((cause == AC_A_HIGH_CUR_CAUSE || cause == AC_B_HIGH_CUR_CAUSE || cause == AC_C_HIGH_CUR_CAUSE || cause == AC_N_HIGH_CUR_CAUSE || cause == DC_HIGH_CUR_CAUSE) 
            || s_continue_trig_count[sample_channel_no] >= overVol_trig_point_num)
        {
            g_adc_trig_data_index = su32_index;
            g_trig_cause = cause;
            s_continue_trig_count[sample_channel_no] = 0;
            return TRUE;
        }
    }
    else
    {
        s_continue_trig_count[sample_channel_no] = 0;
    }
    return FALSE;
}




int check_dc_low_vol(unsigned short *buf, unsigned int su32_index, int sample_channel_no) {
    RETURN_VAL_IF_FAIL(g_adc_trig_data_index == -1, FAILURE);
    unsigned char underVol_trig_point_num = 5;
    if (g_vaild_data_flag.phase_dc_vol_valid_flag && *buf > g_data_threshold.adc_dc_low_down_vol && *buf < g_data_threshold.adc_dc_low_up_vol)
    {
        s_continue_trig_count[sample_channel_no] ++;
        get_one_para(TOV_PARA_ID_VOL_LOW_POINT_NUMBER_OFFSET, &underVol_trig_point_num);
        if(s_continue_trig_count[sample_channel_no] >= underVol_trig_point_num)
        {
            g_adc_trig_data_index = su32_index;
            g_trig_cause = DC_LOW_VOL_CAUSE;
            s_continue_trig_count[sample_channel_no] = 0;
            return SUCCESSFUL;
        }
    }
    else
    {
        s_continue_trig_count[sample_channel_no] = 0;
    }
    return FAILURE;
}



int find_high_vol_trig_point(unsigned short *adc1_dma_buf1, unsigned short *adc2_dma_buf1, unsigned short *adc3_dma_buf1, unsigned int su32_index)
{
    RETURN_VAL_IF_FAIL(g_adc_trig_data_index == -1, FAILURE);
    if (is_over_threshold(adc2_dma_buf1 + 2, g_data_threshold.adc_dc_high_up_vol, g_data_threshold.adc_dc_high_down_vol, 
        su32_index, DC_HIGH_VOL_CAUSE, g_vaild_data_flag.phase_dc_vol_valid_flag, 0))      { return SUCCESSFUL; }

    if (is_over_threshold(adc3_dma_buf1 + 3, g_data_threshold.adc_dc_high_up_curr, g_data_threshold.adc_dc_high_down_curr, 
        su32_index, DC_HIGH_CUR_CAUSE, g_vaild_data_flag.phase_dc_curr_valid_flag, 1))     { return SUCCESSFUL; }

    if (is_over_threshold(adc3_dma_buf1, g_data_threshold.adc_a_ac_high_up_vol, g_data_threshold.adc_a_ac_high_down_vol, 
        su32_index, AC_A_HIGH_VOL_CAUSE, g_vaild_data_flag.phase_a_vol_valid_flag, 2))     { return SUCCESSFUL; }
    
    if (is_over_threshold(adc2_dma_buf1, g_data_threshold.adc_b_ac_high_up_vol, g_data_threshold.adc_b_ac_high_down_vol, 
        su32_index, AC_B_HIGH_VOL_CAUSE, g_vaild_data_flag.phase_b_vol_valid_flag, 3))     { return SUCCESSFUL; }
    
    if (is_over_threshold(adc1_dma_buf1, g_data_threshold.adc_c_ac_high_up_vol, g_data_threshold.adc_c_ac_high_down_vol, 
        su32_index, AC_C_HIGH_VOL_CAUSE, g_vaild_data_flag.phase_c_vol_valid_flag, 4))     { return SUCCESSFUL; }

    if (is_over_threshold(adc3_dma_buf1 + 2, g_data_threshold.adc_n_ac_high_up_vol, g_data_threshold.adc_n_ac_high_down_vol, 
        su32_index, AC_N_HIGH_VOL_CAUSE, g_vaild_data_flag.phase_n_vol_valid_flag, 5))     { return SUCCESSFUL; }

    if (is_over_threshold(adc1_dma_buf1 + 1, g_data_threshold.adc_a_ac_high_up_curr, g_data_threshold.adc_a_ac_high_down_curr, 
        su32_index, AC_A_HIGH_CUR_CAUSE, g_vaild_data_flag.phase_a_curr_valid_flag, 6))    { return SUCCESSFUL; }
    
    if (is_over_threshold(adc3_dma_buf1 + 1, g_data_threshold.adc_b_ac_high_up_curr, g_data_threshold.adc_b_ac_high_down_curr, 
        su32_index, AC_B_HIGH_CUR_CAUSE, g_vaild_data_flag.phase_b_curr_valid_flag, 7))    { return SUCCESSFUL; }
    
    if (is_over_threshold(adc2_dma_buf1 + 1, g_data_threshold.adc_c_ac_high_up_curr, g_data_threshold.adc_c_ac_high_down_curr, 
    su32_index, AC_C_HIGH_CUR_CAUSE, g_vaild_data_flag.phase_c_curr_valid_flag, 8))        { return SUCCESSFUL; }
    
    if (is_over_threshold(adc1_dma_buf1 + 2, g_data_threshold.adc_n_ac_high_up_curr, g_data_threshold.adc_n_ac_high_down_curr, 
        su32_index, AC_N_HIGH_CUR_CAUSE, g_vaild_data_flag.phase_n_curr_valid_flag, 9))    { return SUCCESSFUL; }
    
    return  FAILURE;
}



int clear_all_sample_data(unsigned int* current_index)
{
    rt_memset_s(s_phase_a_vol_buff,  MAX_POINT_NUM_PER_CHANNEL_BUFF_SIZE, 0, MAX_POINT_NUM_PER_CHANNEL_BUFF_SIZE);
    rt_memset_s(s_phase_a_curr_buff, MAX_POINT_NUM_PER_CHANNEL_BUFF_SIZE, 0, MAX_POINT_NUM_PER_CHANNEL_BUFF_SIZE);
    rt_memset_s(s_phase_n_curr_buff, MAX_POINT_NUM_PER_CHANNEL_BUFF_SIZE, 0, MAX_POINT_NUM_PER_CHANNEL_BUFF_SIZE);
    rt_memset_s(s_phase_n_vol_buff,  MAX_POINT_NUM_PER_CHANNEL_BUFF_SIZE, 0, MAX_POINT_NUM_PER_CHANNEL_BUFF_SIZE);
    rt_memset_s(s_phase_b_vol_buff,  MAX_POINT_NUM_PER_CHANNEL_BUFF_SIZE, 0, MAX_POINT_NUM_PER_CHANNEL_BUFF_SIZE);
    rt_memset_s(s_phase_b_curr_buff, MAX_POINT_NUM_PER_CHANNEL_BUFF_SIZE, 0, MAX_POINT_NUM_PER_CHANNEL_BUFF_SIZE);
    rt_memset_s(s_phase_c_vol_buff,  MAX_POINT_NUM_PER_CHANNEL_BUFF_SIZE, 0, MAX_POINT_NUM_PER_CHANNEL_BUFF_SIZE);
    rt_memset_s(s_phase_c_curr_buff, MAX_POINT_NUM_PER_CHANNEL_BUFF_SIZE, 0, MAX_POINT_NUM_PER_CHANNEL_BUFF_SIZE);
    rt_memset_s(s_digit_curr_buff,   MAX_POINT_NUM_PER_CHANNEL_BUFF_SIZE, 0, MAX_POINT_NUM_PER_CHANNEL_BUFF_SIZE);
    rt_memset_s(s_digit_vol_buff,    MAX_POINT_NUM_PER_CHANNEL_BUFF_SIZE, 0, MAX_POINT_NUM_PER_CHANNEL_BUFF_SIZE);

    s_phase_a_vol  = s_phase_a_vol_buff;
    s_phase_a_curr = s_phase_a_curr_buff;
    s_phase_n_curr = s_phase_n_curr_buff;
    s_phase_n_vol  = s_phase_n_vol_buff;
    s_phase_b_vol  = s_phase_b_vol_buff;
    s_phase_b_curr = s_phase_b_curr_buff;
    s_phase_c_vol  = s_phase_c_vol_buff;
    s_phase_c_curr = s_phase_c_curr_buff;
    s_digit_curr   = s_digit_curr_buff;
    s_digit_vol    = s_digit_vol_buff;
    *current_index = 0;
    return SUCCESSFUL;
}


int check_after_DC_low_vol_trig(unsigned int* current_index)
{
    RETURN_VAL_IF_FAIL (g_adc_trig_data_index >= 0, FAILURE);
    RETURN_VAL_IF_FAIL (DC_LOW_VOL_CAUSE == g_trig_cause, FAILURE);
    RETURN_VAL_IF_FAIL (0 != g_data_threshold.adc_dc_low_vol_dev, FAILURE);

    unsigned short min_val = 4096;
    unsigned short max_val = 0;
    unsigned short diff = (*current_index - g_adc_trig_data_index + MAX_POINT_NUM_PER_CHANNEL) % MAX_POINT_NUM_PER_CHANNEL;

    float dc_right_window_point_num = 0;
    float dc_left_window_point_num = 0;
    get_one_para(TOV_PARA_ID_DC_T1_SIZE_OFFSET, &dc_right_window_point_num);
    get_one_para(TOV_PARA_ID_DC_T0_SIZE_OFFSET, &dc_left_window_point_num);

    unsigned short dc_right_window_point = (unsigned short)(g_adc_trig_data_index + dc_right_window_point_num * 100) % MAX_POINT_NUM_PER_CHANNEL;
    unsigned short dc_left_window_point = (unsigned short)(g_adc_trig_data_index - dc_left_window_point_num * 100 + MAX_POINT_NUM_PER_CHANNEL) % MAX_POINT_NUM_PER_CHANNEL;

    if (diff >= dc_right_window_point_num)
    {
        if(dc_left_window_point <= dc_right_window_point)
        {
            find_min_max(s_digit_vol_buff + dc_left_window_point, dc_right_window_point - dc_left_window_point, &min_val, &max_val);
        }
        else
        {
            find_min_max(s_digit_vol_buff, dc_right_window_point, &min_val, &max_val);
            find_min_max(s_digit_vol_buff + dc_left_window_point, MAX_POINT_NUM_PER_CHANNEL - dc_left_window_point, &min_val, &max_val);
        }

        if(abs(max_val - min_val) < g_data_threshold.adc_dc_low_vol_dev)
        {
            g_trig_cause = 0;
            g_adc_trig_data_index = -1;
        }
        return SUCCESSFUL;
    }

    return FAILURE;
}


int save_record_data_after_trig(unsigned int* current_index, unsigned char* save_num)
{
    // 检查触发索引是否有效
    RETURN_VAL_IF_FAIL (g_adc_trig_data_index >= 0, FAILURE);
    // 计算差值，确保循环缓冲区的正确处理
    unsigned char ac_right_window_time = 0;
    float dc_right_window_time = 0;
    unsigned short diff = (*current_index - g_adc_trig_data_index + MAX_POINT_NUM_PER_CHANNEL) % MAX_POINT_NUM_PER_CHANNEL;
    // 检查右边的窗口是否采够窗口数据
    get_one_para(TOV_PARA_ID_AC_T1_SIZE_OFFSET, &ac_right_window_time);
    get_one_para(TOV_PARA_ID_DC_T1_SIZE_OFFSET, &dc_right_window_time);

    if ((diff >= ac_right_window_time * 100 && diff >= dc_right_window_time * 100) || // 正常录波场景
        (diff >= 500 && g_trig_cause == POWER_DOWN_CAUSE))                             // 掉电录波场景
    {
        // 交流和直流都需要满足条件才停止采样
        rt_device_control(g_ad_device, SET_SYNC_ADC_STOP, RT_NULL); // 触发录波后，采够窗口数据就停止采样并通知应用层处理数据
        rt_sem_clear_value(&sync_adc_rx_sem);
        // 控制故障灯闪烁5秒
        send_ctrl_led_msg(20, 20, LED_FAULT_PIN);
        g_led_count = 0;

        // 记录触发信息
        s_sample_info.trig_cause = g_trig_cause;
        s_sample_info.trig_index = g_adc_trig_data_index;
        get_ms_time_base(&s_sample_info.trig_time);

        // 保存记录数据
        if(save_record_data(&s_sample_info, *save_num, g_trig_cause) == SUCCESSFUL)
        {
            (*save_num)++;
        }
        // 1) 存储条数满10条 
        // 2) 触发原因是交流欠压或者直流欠压 且 实际保存不少于1条
        if(*save_num == MAX_SAMPLE_DATA_SAVE_NUM || 
            ((g_trig_cause == POWER_DOWN_CAUSE || g_trig_cause == AC_A_LOW_VOL_CAUSE || g_trig_cause == AC_B_LOW_VOL_CAUSE 
                || g_trig_cause == AC_C_LOW_VOL_CAUSE || g_trig_cause == DC_LOW_VOL_CAUSE) && *save_num != 0) 
            )
        {
            save_record_data_to_file(save_num);
            is_mqtt_upfile(current_index);
        }
        else
        {
            clear_all_sample_data(current_index);
            //重新启动ADC同步采样
            rt_device_control(g_ad_device, SET_SYNC_ADC_START, RT_NULL);
        }
        // 重置触发原因和索引
        g_adc_trig_data_index = -1;
        s_low_check_count = 0;
        s_mqtt_upfile_count = 0;
    }
    return SUCCESSFUL;
}


rt_err_t adc_sample_freq_set(unsigned char sample_freq)
{
    RETURN_VAL_IF_FAIL(sample_freq >= ADC_SAMPLE_FREQ_MIN && sample_freq <= ADC_SAMPLE_FREQ_MAX, FAILURE);
    RETURN_VAL_IF_FAIL(g_ad_device != NULL, FAILURE);
    rt_device_control(g_ad_device, SET_SYNC_ADC_PARAM, (uint32_t)(10*sample_freq));
    return RT_EOK;
}


int get_threshold()//消息的方式更新阈值
{
    static unsigned char s_sample_freq = 1;   //采样频率默认1
    unsigned char sample_freq_temp = 101;
    short tmp_vol_data = 0;
    char tmp_curr_data = 0;
    unsigned char dc_vol_range = 0;
    char ctrl_sample_switch[2] = {0};
    ctrl_sample_switch[0] = TRUE;
    ctrl_sample_switch[1] = FALSE;
    s_low_check_count = 0;
    rt_device_control(g_ad_device, SET_SYNC_ADC_STOP, RT_NULL);
    rt_sem_clear_value(&sync_adc_rx_sem);
    get_one_para(TOV_PARA_ID_AC_PHASE_VOL_HIGH_THRESHOLD_OFFSET,     &tmp_vol_data);
    g_data_threshold.adc_a_ac_high_up_vol = (tmp_vol_data * AC_VOLT_COMPRESSION_RATIO + VOLT_BASE) * 4096 /3;
    g_data_threshold.adc_a_ac_high_down_vol = 4096 - g_data_threshold.adc_a_ac_high_up_vol;
    get_one_para(TOV_PARA_ID_AC_PHASE_VOL_HIGH_THRESHOLD_OFFSET + 1, &tmp_vol_data);
    g_data_threshold.adc_b_ac_high_up_vol = (tmp_vol_data * AC_VOLT_COMPRESSION_RATIO + VOLT_BASE) * 4096 /3;
    g_data_threshold.adc_b_ac_high_down_vol = 4096 - g_data_threshold.adc_b_ac_high_up_vol;
    get_one_para(TOV_PARA_ID_AC_PHASE_VOL_HIGH_THRESHOLD_OFFSET + 2, &tmp_vol_data);
    g_data_threshold.adc_c_ac_high_up_vol = (tmp_vol_data * AC_VOLT_COMPRESSION_RATIO + VOLT_BASE) * 4096 /3;
    g_data_threshold.adc_c_ac_high_down_vol = 4096 - g_data_threshold.adc_c_ac_high_up_vol;
    get_one_para(TOV_PARA_ID_AC_PHASE_VOL_HIGH_THRESHOLD_OFFSET + 3, &tmp_vol_data);
    g_data_threshold.adc_n_ac_high_up_vol = (tmp_vol_data * AC_N_VOLT_COMPRESSION_RATIO + VOLT_BASE) * 4096 /3;
    g_data_threshold.adc_n_ac_high_down_vol = 4096 - g_data_threshold.adc_n_ac_high_up_vol;

    get_one_para(TOV_PARA_ID_AC_PHASE_VOL_LOW_THRESHOLD_OFFSET,     &tmp_vol_data);
    g_data_threshold.adc_a_ac_low_vol = tmp_vol_data;
    get_one_para(TOV_PARA_ID_AC_PHASE_VOL_LOW_THRESHOLD_OFFSET + 1, &tmp_vol_data);
    g_data_threshold.adc_b_ac_low_vol = tmp_vol_data;
    get_one_para(TOV_PARA_ID_AC_PHASE_VOL_LOW_THRESHOLD_OFFSET + 2, &tmp_vol_data);
    g_data_threshold.adc_c_ac_low_vol = tmp_vol_data;

    get_one_para(TOV_PARA_ID_AC_PHASE_CURR_HIGH_THRESHOLD_OFFSET,     &tmp_curr_data);
    g_data_threshold.adc_a_ac_high_up_curr = (tmp_curr_data * CURR_COMPRESSION_RATIO + VOLT_BASE) * 4096 /3;
    g_data_threshold.adc_a_ac_high_down_curr = 4096 - g_data_threshold.adc_a_ac_high_up_curr;
    get_one_para(TOV_PARA_ID_AC_PHASE_CURR_HIGH_THRESHOLD_OFFSET + 1, &tmp_curr_data);
    g_data_threshold.adc_b_ac_high_up_curr = (tmp_curr_data * CURR_COMPRESSION_RATIO + VOLT_BASE) * 4096 /3;
    g_data_threshold.adc_b_ac_high_down_curr = 4096 -  g_data_threshold.adc_b_ac_high_up_curr;
    get_one_para(TOV_PARA_ID_AC_PHASE_CURR_HIGH_THRESHOLD_OFFSET + 2, &tmp_curr_data);
    g_data_threshold.adc_c_ac_high_up_curr = (tmp_curr_data * CURR_COMPRESSION_RATIO + VOLT_BASE) * 4096 /3;
    g_data_threshold.adc_c_ac_high_down_curr = 4096 - g_data_threshold.adc_c_ac_high_up_curr;
    get_one_para(TOV_PARA_ID_AC_PHASE_CURR_HIGH_THRESHOLD_OFFSET + 3, &tmp_curr_data);
    g_data_threshold.adc_n_ac_high_up_curr = (tmp_curr_data * CURR_COMPRESSION_RATIO + VOLT_BASE) * 4096 /3;
    g_data_threshold.adc_n_ac_high_down_curr = 4096 - g_data_threshold.adc_n_ac_high_up_curr;

    get_one_para(TOV_PARA_ID_DC_CURR_HIGH_THRESHOLD_OFFSET, &tmp_curr_data);
    g_data_threshold.adc_dc_high_up_curr = (tmp_curr_data * CURR_COMPRESSION_RATIO + VOLT_BASE) * 4096 /3;
    g_data_threshold.adc_dc_high_down_curr = 4096 - g_data_threshold.adc_dc_high_up_curr;
    
    get_one_para(TOV_PARA_ID_SAMPLE_FREQ_OFFSET,  &sample_freq_temp);
    if(sample_freq_temp != s_sample_freq)    //值成功获取，且变更，进行采样频率更新与设置
    {
        s_sample_freq = sample_freq_temp;
        adc_sample_freq_set(s_sample_freq);
    }

    get_one_para(TOV_PARA_ID_AC_VOL_LOW_DEVIATION_OFFSET, &tmp_vol_data);
    g_data_threshold.adc_ac_low_vol_dev = (tmp_vol_data * AC_VOLT_COMPRESSION_RATIO + VOLT_BASE) * 4096 /3 - 2048;
    
    get_one_para(TOV_PARA_ID_DC_VOL_MODE_OFFSET, &dc_vol_range);
    if(dc_vol_range == 0)
    {
        get_one_para(TOV_PARA_ID_DC_VOL_LOW_THRESHOLD_OFFSET,   &tmp_vol_data);
        tmp_vol_data = tmp_vol_data > 300 ? 300:tmp_vol_data;
        g_data_threshold.adc_dc_low_up_vol = (tmp_vol_data * DC300_VOLT_COMPRESSION_RATIO + VOLT_BASE) * 4096 /3;
        g_data_threshold.adc_dc_low_down_vol = 4096 - g_data_threshold.adc_dc_low_up_vol;

        get_one_para(TOV_PARA_ID_DC_VOL_HIGH_THRESHOLD_OFFSET,  &tmp_vol_data);
        tmp_vol_data = tmp_vol_data > 300 ? 300:tmp_vol_data;
        g_data_threshold.adc_dc_high_up_vol = (tmp_vol_data * DC300_VOLT_COMPRESSION_RATIO + VOLT_BASE) * 4096 /3;
        g_data_threshold.adc_dc_high_down_vol = 4096 - g_data_threshold.adc_dc_high_up_vol;

        get_one_para(TOV_PARA_ID_DC_VOL_LOW_DEVIATION_OFFSET, &tmp_vol_data);
        tmp_vol_data = tmp_vol_data > 300 ? 300:tmp_vol_data;
        g_data_threshold.adc_dc_low_vol_dev = (tmp_vol_data * DC300_VOLT_COMPRESSION_RATIO + VOLT_BASE) * 4096 /3 - 2048;

        pub_msg_to_thread(CTRL_SAMPLE_SWITCH, &ctrl_sample_switch, sizeof(ctrl_sample_switch));
        return SUCCESSFUL;
    }
    get_one_para(TOV_PARA_ID_DC_VOL_LOW_THRESHOLD_OFFSET,   &tmp_vol_data);
    g_data_threshold.adc_dc_low_up_vol =  (tmp_vol_data * DC2500_VOLT_COMPRESSION_RATIO + VOLT_BASE) * 4096 /3;
    g_data_threshold.adc_dc_low_down_vol = 4096 - g_data_threshold.adc_dc_low_up_vol;
    get_one_para(TOV_PARA_ID_DC_VOL_HIGH_THRESHOLD_OFFSET,  &tmp_vol_data);
    g_data_threshold.adc_dc_high_up_vol = (tmp_vol_data * DC2500_VOLT_COMPRESSION_RATIO + VOLT_BASE) * 4096 /3;
    g_data_threshold.adc_dc_high_down_vol = 4096 - g_data_threshold.adc_dc_high_up_vol;

    get_one_para(TOV_PARA_ID_DC_VOL_LOW_DEVIATION_OFFSET, &tmp_vol_data);
    g_data_threshold.adc_dc_low_vol_dev = (tmp_vol_data * DC2500_VOLT_COMPRESSION_RATIO + VOLT_BASE) * 4096 /3 - 2048;

    pub_msg_to_thread(CTRL_SAMPLE_SWITCH, &ctrl_sample_switch, sizeof(ctrl_sample_switch));
    return SUCCESSFUL;
}



int check_power_down_trig(unsigned int su32_index)
{
    RETURN_VAL_IF_FAIL(g_trig_cause != POWER_DOWN_CAUSE, FAILURE);
    if (get_pin(POWER_DOWN_PIN)) {
        rt_kprintf("POWER_DOWN_PIN!!!!\n");
        g_adc_trig_data_index = su32_index;
        g_trig_cause = POWER_DOWN_CAUSE;
    }
    return SUCCESSFUL;
}




int save_val_adc_data(data_base_address_t *data_base_addr, unsigned int* sample_data_index, unsigned char* save_num)
{
    static unsigned int s_start_sample_delay = 0;
    unsigned int index = 0;
    unsigned short *adc1_dma_buf1 = data_base_addr->adc1_addr;
    unsigned short *adc2_dma_buf1 = data_base_addr->adc2_addr;
    unsigned short *adc3_dma_buf1 = data_base_addr->adc3_addr;
    unsigned char sample_freq = 1;   //采样频率默认1

    get_one_para(TOV_PARA_ID_SAMPLE_FREQ_OFFSET,  &sample_freq);
    if(0 == sample_freq)
    {
        sample_freq = 1;
    }

    //3s稳定期不触发
    if(s_start_sample_delay < (600 / sample_freq)) //依据周期调整，频率1*10us，计数600；频率100*10us，计数6
    {
        s_start_sample_delay ++;
        return SUCCESSFUL;
    }

    for (index = 0; index < 500; index++)
    {
        find_high_vol_trig_point(adc1_dma_buf1, adc2_dma_buf1, adc3_dma_buf1, *sample_data_index);
        check_dc_low_vol(adc2_dma_buf1 + 2, *sample_data_index, 10);
        *s_phase_c_vol++  = *adc1_dma_buf1++;
        *s_phase_a_curr++ = *adc1_dma_buf1++;
        *s_phase_n_curr++ = *adc1_dma_buf1++;

        *s_phase_b_vol++  = *adc2_dma_buf1++;
        *s_phase_c_curr++ = *adc2_dma_buf1++;
        *s_digit_vol++    = *adc2_dma_buf1++;

        *s_phase_a_vol++  = *adc3_dma_buf1++;
        *s_phase_b_curr++ = *adc3_dma_buf1++;
        *s_phase_n_vol++  = *adc3_dma_buf1++;
        *s_digit_curr++   = *adc3_dma_buf1++;
        (*sample_data_index)++;
    }
    s_low_check_count++;
    if (s_low_check_count == LOW_VOL_CHECK_POINT_TIMES)
    {
        s_low_check_count = 0;
        find_low_trig_point(*sample_data_index, s_phase_a_vol_buff, s_phase_b_vol_buff, s_phase_c_vol_buff);
    }

    check_power_down_trig(*sample_data_index);

    if (*sample_data_index == MAX_POINT_NUM_PER_CHANNEL) // 数据缓冲区存满后重置指针
    {
        s_phase_a_vol  = s_phase_a_vol_buff;
        s_phase_a_curr = s_phase_a_curr_buff;
        s_phase_n_curr = s_phase_n_curr_buff;
        s_phase_b_vol  = s_phase_b_vol_buff;
        s_phase_b_curr = s_phase_b_curr_buff;
        s_phase_c_vol  = s_phase_c_vol_buff;
        s_phase_c_curr = s_phase_c_curr_buff;
        s_phase_n_vol  = s_phase_n_vol_buff;
        s_digit_curr   = s_digit_curr_buff;
        s_digit_vol    = s_digit_vol_buff;
        *sample_data_index = 0;
    }
    check_after_DC_low_vol_trig(sample_data_index);
    save_record_data_after_trig(sample_data_index, save_num);
    return SUCCESSFUL;
}




int fill_record_data_info(record_data_sample_info_t* sample_data )
{
    sample_data->ac_sample_data[0].data_id = AC_A_VOL_ID;
    sample_data->ac_sample_data[0].data_len = 2;
    sample_data->ac_sample_data[0].sample_data = s_phase_a_vol_buff;
    sample_data->ac_sample_data[1].data_id = AC_B_VOL_ID;
    sample_data->ac_sample_data[1].data_len = 2;
    sample_data->ac_sample_data[1].sample_data = s_phase_b_vol_buff;
    sample_data->ac_sample_data[2].data_id = AC_C_VOL_ID;
    sample_data->ac_sample_data[2].data_len = 2;
    sample_data->ac_sample_data[2].sample_data = s_phase_c_vol_buff;
    sample_data->ac_sample_data[3].data_id = AC_N_VOL_ID;
    sample_data->ac_sample_data[3].data_len = 2;
    sample_data->ac_sample_data[3].sample_data = s_phase_n_vol_buff;
    sample_data->ac_sample_data[4].data_id = AC_A_CUR_ID;
    sample_data->ac_sample_data[4].data_len = 2;
    sample_data->ac_sample_data[4].sample_data = s_phase_a_curr_buff;
    sample_data->ac_sample_data[5].data_id = AC_B_CUR_ID;
    sample_data->ac_sample_data[5].data_len = 2;
    sample_data->ac_sample_data[5].sample_data = s_phase_b_curr_buff;
    sample_data->ac_sample_data[6].data_id = AC_C_CUR_ID;
    sample_data->ac_sample_data[6].data_len = 2;
    sample_data->ac_sample_data[6].sample_data = s_phase_c_curr_buff;
    sample_data->ac_sample_data[7].data_id = AC_N_CUR_ID;
    sample_data->ac_sample_data[7].data_len = 2;
    sample_data->ac_sample_data[7].sample_data = s_phase_n_curr_buff;

    sample_data->dc_sample_data[0].data_id = DC_VOL_ID;
    sample_data->dc_sample_data[0].data_len = 2;
    sample_data->dc_sample_data[0].sample_data = s_digit_vol_buff;
    sample_data->dc_sample_data[1].data_id = DC_CUR_ID;
    sample_data->dc_sample_data[1].data_len = 2;
    sample_data->dc_sample_data[1].sample_data = s_digit_curr_buff;
    return SUCCESSFUL;
}


void* init_sample(void* param)
{
    RETURN_VAL_IF_FAIL(param != NULL, NULL);
    s_server_info = (server_info_t *)param;
    s_server_info->server.server.map_size = sizeof(s_sample_msg_map) / sizeof(msg_map);
    register_server_msg_map(s_sample_msg_map, s_server_info);
    init_extreme_data();
    fill_record_data_info(&s_sample_info);
    rt_sem_init(&sync_adc_rx_sem, "sync_adc_rx", 0, RT_IPC_FLAG_FIFO);
    g_ad_device = rt_device_find("sync_adc");
    rt_device_set_rx_indicate(g_ad_device, sync_adc_rx_indicate);
    get_threshold();
    deal_channel_valid();
    return param;
}
void handle_update_threshold(_rt_msg_t curr_msg)
{
    get_threshold();
    return;
}


int deal_channel_valid()
{
    rt_device_control(g_ad_device, SET_SYNC_ADC_STOP, RT_NULL);
    rt_sem_clear_value(&sync_adc_rx_sem);
    s_low_check_count = 0;
    unsigned char obser_num = 0, obser_data = 0;
    unsigned char obser_point[CHANNEL_TOTAL_NUM] = {0};
    char ctrl_sample_switch[2] = {0};
    get_one_para(TOV_PARA_ID_OBSER_NUM_OFFSET, &obser_num);
    for(int i = 0; i < obser_num; i++)
    {
        get_one_para(TOV_PARA_ID_OBSER_OFFSET + i, &obser_data);
        obser_point[obser_data - 1] = obser_data;
    }
    rt_memcpy_s(&g_vaild_data_flag, sizeof(sample_data_valid_t), obser_point, sizeof(obser_point));
    if(g_trig_cause == AC_A_LOW_VOL_CAUSE || g_trig_cause == AC_B_LOW_VOL_CAUSE || g_trig_cause == AC_C_LOW_VOL_CAUSE  || g_trig_cause == DC_LOW_VOL_CAUSE)
    {
        return FAILURE;
    }
    ctrl_sample_switch[0] = TRUE;
    ctrl_sample_switch[1] = FALSE;
    pub_msg_to_thread(CTRL_SAMPLE_SWITCH, &ctrl_sample_switch, sizeof(ctrl_sample_switch));
    return SUCCESSFUL;
}



void handle_update_chan_valid(_rt_msg_t curr_msg)
{
    deal_channel_valid();
    return;
}


unsigned char is_undervoltage_mode()
{
    if(g_trig_cause == AC_A_LOW_VOL_CAUSE 
    || g_trig_cause == AC_B_LOW_VOL_CAUSE 
    || g_trig_cause == AC_C_LOW_VOL_CAUSE  
    || g_trig_cause == DC_LOW_VOL_CAUSE)
    {
            return TRUE;
    }
    return FALSE;
}


void handle_ctrl_sample_switch(_rt_msg_t curr_msg)
{
    // FALSE, FALSE: 关闭采样
    // FALSE, TRUE:  关闭采样
    // TRUE, FALSE:  如果是欠压模式则关闭，否则开启
    // TRUE,  TRUE:  开启采样
    unsigned char upfile_sta = 0, update_sta = 0;
    get_one_data(TOV_DATA_ID_RECORD_UPFILE_STATUS, &upfile_sta);
    get_one_data(TOV_DATA_ID_UPDATA_STATUS, &update_sta);
    if (((char*)curr_msg->msg.data)[0] == FALSE)
    {
        rt_device_control(g_ad_device, SET_SYNC_ADC_STOP, RT_NULL);
        rt_sem_clear_value(&sync_adc_rx_sem);
        clear_all_sample_data(&s_sample_data_index);
    }
    else if(((char*)curr_msg->msg.data)[1] == FALSE)
    {
        
        if(is_undervoltage_mode())
        {
            rt_device_control(g_ad_device, SET_SYNC_ADC_STOP, RT_NULL);
            rt_sem_clear_value(&sync_adc_rx_sem);
            clear_all_sample_data(&s_sample_data_index);
        }
        else if(upfile_sta == 0 && update_sta == 0)
        {
            rt_device_control(g_ad_device, SET_SYNC_ADC_START, RT_NULL);
            g_trig_cause = 0;
        }
    }
    else     // 开启采样
    {
        upfile_sta = 0;
        set_one_data(TOV_DATA_ID_RECORD_UPFILE_STATUS, &upfile_sta);
        g_trig_cause = 0;
        RETURN_IF_FAIL(update_sta == 0);
        unsigned char auth_code = 0x10;
        rt_thread_mdelay(100); 
        delete_record_data_new(auth_code, RECORD_DATA_INDEX);//删除文件
        rt_device_control(g_ad_device, SET_SYNC_ADC_START, RT_NULL);

    }
}


int process_recv_sample_msg(void)
{
    int i = 0;
    if ((rt_sem_take(&g_sample_server->msg_sem, 0) != RT_EOK) || (g_sample_server == NULL) || (g_sample_server->msg_node == RT_NULL))
    {
        return FAILURE;
    }

    _rt_msg_t curr_msg = g_sample_server->msg_node ;
    for (i = 0; i < sizeof(s_msg_handle) / sizeof(s_msg_handle[0]); i++)
    {
        if (s_msg_handle[i].msg_id == curr_msg->msg.msg_id)
        {
            s_msg_handle[i].handle(curr_msg);
        }
    }

    rt_mutex_take(&g_sample_server->mutex, RT_WAITING_FOREVER);
    g_sample_server->msg_node = curr_msg->next;
    g_sample_server->msg_count--;
    rt_mutex_release(&g_sample_server->mutex);

    softbus_free(curr_msg);
    return SUCCESSFUL;
}

int is_mqtt_upfile(unsigned int* current_index)
{
    unsigned char sim_sta = 0, model_sta = 0, upfile_sta = 0;
    get_one_data(TOV_DATA_ID_SIM_EXIST, &sim_sta);
    get_one_data(TOV_DATA_ID_4G_EXIST, &model_sta);
    get_one_data(TOV_DATA_ID_RECORD_UPFILE_STATUS, &upfile_sta);
    if(sim_sta && model_sta && upfile_sta == 0)
    {
        upfile_sta = 1;
        set_one_data(TOV_DATA_ID_RECORD_UPFILE_STATUS, &upfile_sta);
        pub_msg_to_thread(MQTT_UPFILE_MSG, NULL, 0);
        clear_all_sample_data(current_index);
        rt_kprintf("upload data to network\n");
        return SUCCESSFUL;
    }
    // 如果是交流欠压或者直流欠压场景，不能再开启采样
    if(g_trig_cause == AC_A_LOW_VOL_CAUSE || g_trig_cause == AC_B_LOW_VOL_CAUSE || g_trig_cause == AC_C_LOW_VOL_CAUSE  || g_trig_cause == DC_LOW_VOL_CAUSE)
    {
        return FAILURE;
    }
    rt_device_control(g_ad_device, SET_SYNC_ADC_START, RT_NULL);
    return FAILURE;
}



int period_deal(unsigned int* sample_data_index, unsigned char* save_num)
{
    g_led_count++;
    if(g_led_count >= 1000)
    {
        g_led_count = -1;
        send_ctrl_led_msg(20,0,LED_FAULT_PIN);
        // 产生的录播数据不满足10条，且时间超过20s无数据，会启动上报给网管
        if(*save_num != 0)
        {
            rt_device_control(g_ad_device, SET_SYNC_ADC_STOP, RT_NULL);
            rt_sem_clear_value(&sync_adc_rx_sem);
            save_record_data_to_file(save_num);
            clear_all_sample_data(sample_data_index);
            is_mqtt_upfile(sample_data_index);
            s_mqtt_upfile_count = 0;
        }
        return SUCCESSFUL;
    }
    return FAILURE;
}



int clear_memory_record_data()
{
    s_save_num = 0;
    return SUCCESSFUL;
}

int init_adc_sample_dev()
{
    unsigned char sample_freq = 1;   //采样频率默认1
    rt_device_open(g_ad_device, RT_DEVICE_FLAG_INT_RX);
    get_one_para(TOV_PARA_ID_SAMPLE_FREQ_OFFSET,  &sample_freq);
    rt_device_control(g_ad_device, SET_SYNC_ADC_STOP, NULL);
    adc_sample_freq_set(sample_freq);
    rt_device_control(g_ad_device, SET_SYNC_ADC_START, NULL);
    return SUCCESSFUL;
}

int is_update_upfile_sta()
{
    unsigned char update_sta = 0;
    unsigned char upfile_sta = 0;
    get_one_data(TOV_DATA_ID_UPDATA_STATUS, &update_sta);
    get_one_data(TOV_DATA_ID_RECORD_UPFILE_STATUS, &upfile_sta);

    RETURN_VAL_IF_FAIL(0 != update_sta || 0 != upfile_sta, FAILURE);
    return SUCCESSFUL;
}

void sample_main(void* param)
{
    struct stat stat_buff = {0};
    unsigned int low_vol_count = 0;
    // BSP
    g_sample_server = _curr_server_get();
    
    init_adc_sample_dev();
    
    data_base_address_t *data_base_addr = g_ad_device->user_data;
    while(is_running(TRUE)) {
        if(rt_sem_take(&sync_adc_rx_sem, 20) == RT_EOK)
        {
            low_vol_count = 0;
            save_val_adc_data(data_base_addr, &s_sample_data_index, &s_save_num);
        }
        process_recv_sample_msg();
        if(g_led_count != -1)
        {
            period_deal(&s_sample_data_index, &s_save_num);
        }
        
        //没有新的故障录波产生，达到刷新周期，有文件，则上传文件
        if(is_update_upfile_sta() && s_save_num == 0 && s_mqtt_upfile_count++ > MQTT_UPFILE_TIME_10MIN_COUNT)
        {
            s_mqtt_upfile_count = 0;
            if(storage_stat(RECORD_FAULT_FILE_0, &stat_buff) == SUCCESSFUL)
            {
                rt_device_control(g_ad_device, SET_SYNC_ADC_STOP, RT_NULL);
                rt_sem_clear_value(&sync_adc_rx_sem);
                is_mqtt_upfile(&s_sample_data_index);
            }
        }
        // 触发原因是交流欠压和直流欠压，且时间超过1小时，自动打开采样(防止欠压场景，采样无法打开)
        if(is_update_upfile_sta() && (g_trig_cause == AC_A_LOW_VOL_CAUSE || g_trig_cause == AC_B_LOW_VOL_CAUSE || g_trig_cause == AC_C_LOW_VOL_CAUSE  || g_trig_cause == DC_LOW_VOL_CAUSE)
            && low_vol_count++ >= LOW_VOL_START_SAMPLE_COUNT)
        {
            low_vol_count = 0;
            g_trig_cause = 0;
            rt_device_control(g_ad_device, SET_SYNC_ADC_START, RT_NULL);
        }
    }
}

