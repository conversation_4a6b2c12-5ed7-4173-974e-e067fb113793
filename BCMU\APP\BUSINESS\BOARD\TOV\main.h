#ifndef _TOV_MAIN_H
#define _TOV_MAIN_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

#include "utils_server.h"
#include "server_id.h"

#define SAMPLE_THREAD_STACK_SIZE    3072
#define NORTH_THREAD_STACK_SIZE     2048
#define LOG_MGR_THREAD_STACK_SIZE   2048
#define NET_4G_THREAD_STACK_SIZE    2048
#define MQTT_THREAD_STACK_SIZE      6144

int init_main(void);
server_info_t* get_server_group(int *num);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _TOV_MAIN_H
