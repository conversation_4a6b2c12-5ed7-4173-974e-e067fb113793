/**
 * @file     battery_manage.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2023-03-02
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */

 
#ifndef _BATTERY_CHARGE_H
#define _BATTERY_CHARGE_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "device_num.h"
#include "battery.h"

typedef struct {
     unsigned short ChargeEndHoldSeconds;
     unsigned short cluster_lockout_seconds;
     unsigned char  batt_mod_num; 
     unsigned char  fChargeCurrHope;
}batt_ctrl_data_t;


typedef struct {
    batt_state_e   batt_ctrl_state;
    unsigned char  lockout_status;          /// 闭锁状态
    unsigned char  discharge_status;        /// 放电状态
    float batt_curr;                        /// 电池电流
    float batt_vol;                         /// 电池电压
    float cell_volt[BATT_MOD_NUM][BATT_MOD_CELL_NUM];/// 电芯电压
}batt_real_data_t;

typedef struct {
    float fChargeFullVol;                   /// 电池充满电压
    unsigned short wBattCap;                /// 电池容量
    float fChagEndCurCoeff;                 /// 电池充电末期电流系数                        
}batt_para_data_t;

typedef struct {
    batt_state_e curr_batt_state;
    batt_state_e next_batt_state;
    unsigned char cluster_index;
    char       state_result[32];
    batt_ctrl_data_t manage_data;
    batt_real_data_t real_data;
    batt_para_data_t para_data;
    //其他数据
}cluster_manage_info_t;


typedef void (*state_action)(cluster_manage_info_t* data);

typedef struct {
    batt_state_e batt_state;
    char  state_name[16];
    state_action state_in;
    state_action state_act;
    state_action state_exit;
    state_action state_judge;
    char  state_change_result[16];
}state_map_t;


void batt_charge_init(void);
void batt_charge_manage(void* parameter);
batt_state_e get_batt_state(unsigned char index);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _BATTERY_CHARGE_H