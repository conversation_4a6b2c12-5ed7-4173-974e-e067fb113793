
/*
 * @Author: dml00265523
 * @Date: 2025-06-24 16:43:27
 * @Description:
 * @FilePath: \BCMU\APP\BUSINESS\BOARD\DCMU\business\his_record.c
 */
#include <rtthread.h>
#include <rtdef.h>
#include <rtdevice.h>
#include "sys/stat.h"
#include "softbus.h"
#include "data_type.h"
#include "his_record.h"
#include "utils_rtthread_security_func.h"
#include "log_mgr_api.h"
#include "partition_def.h"
#include "para_manage.h"
#include "const_define_in.h"
#include "type_define_in.h"
#include "utils_time.h"
#include "utils_data_transmission.h"
#include "realdata_save.h"
#include "app_config.h"
#include "hisdata_in.h"
#include "alarm_mgr_api.h"
#include "remote_download_update_handle.h"
#include "drv_utils.h"
#include "MIBTable.h"
#include "protocol_1363_comm.h"
#include "dev_dcmu.h"

Static int save_sample_data_to_his_data(void * his_data);
Static void pack_int32u_data(unsigned char* buff, unsigned int* offset, char precision, void* data);
Static void pack_int32s_data(unsigned char* buff, unsigned int* offset, char precision, void* data);
Static void pack_int16u_data(unsigned char* buff, unsigned int* offset, char precision, void* data);
Static void pack_int16s_data(unsigned char* buff, unsigned int* offset, char precision, void* data);
Static void pack_int8u_data(unsigned char* buff, unsigned int* offset, char precision, void* data);
Static void pack_int8s_data(unsigned char* buff, unsigned int* offset, char precision, void* data);
Static void pack_bit_data(unsigned char* buff, unsigned int* offset, char precision, void* data);
Static unsigned char find_pack_tab_index(unsigned char precision, unsigned char storage_type);
Static short fill_event_record_callback(event_record_cb_param_t *event_record_cb_param);

typedef struct
{
    time_t save_time;
    char msg[HISDATA_LEN];
} his_data_record_info;

Static int s_bit_num = 0;
static his_action_record_info s_hisact_msg = {0};
static his_action_record_info s_hisact_callback_msg = {0};
static his_alarm_info_t s_hisalarm_msg = {0};
static his_extreme_data_t s_extreme_msg = {0};
static his_data_record_info s_hisdata_record = {0};
Static unsigned short s_hisalarm_saved_num = 0;

Static his_record_new_t s_his_record[] = {
    { // 操作记录
        .his_record_info    = {0},
        .auth_code          = AUTH_CODE_HIS_ACTION,
        .min_save_num       = MIN_HIS_ACTION_NUM,
        .max_save_num       = MAX_HIS_ACTION_NUM,
        .data               = NULL,
        .part_name          = HIS_ACTION_FILE,
        .storage_type       = ACC,
        .size_type          = FLD,
        .record_size        = sizeof(his_action_record_info),
        .info_name          = HIS_ACTION_FILE,
        .save_num_per_file  = SAVE_NUM_PER_FILE_HIS_ACTION,
        .file_num           = FILE_NUM_HIS_ACTION,
        .file_name          = {HIS_ACTION_FILE_0, HIS_ACTION_FILE_1, HIS_ACTION_FILE_2, HIS_ACTION_FILE_3, HIS_ACTION_FILE_4, HIS_ACTION_FILE_5, }
    },
    { // 历史告警记录
        .his_record_info    = {0},
        .auth_code          = AUTH_CODE_HIS_ALARM,
        .min_save_num       = MIN_HIS_ALARM_NUM,
        .max_save_num       = MAX_HIS_ALARM_NUM,
        .data               = NULL,
        .part_name          = HIS_ALARM_FILE,
        .storage_type       = ACC,
        .size_type          = FLD,
        .record_size        = sizeof(his_alarm_info_t),
        .info_name          = HIS_ALARM_FILE,
        .save_num_per_file  = SAVE_NUM_PER_FILE_HIS_ALARM,
        .file_num           = FILE_NUM_HIS_ALARM,
        .file_name          = {HIS_ALARM_FILE_0, HIS_ALARM_FILE_1, HIS_ALARM_FILE_2, HIS_ALARM_FILE_3, HIS_ALARM_FILE_4, HIS_ALARM_FILE_5, }
    },
    { // 历史数据记录
        .his_record_info    = {0},
        .auth_code          = AUTH_CODE_HIS_DATA,
        .min_save_num       = MIN_HIS_DATA_NUM,
        .max_save_num       = MAX_HIS_DATA_NUM,
        .data               = NULL,
        .part_name          = HIS_DATA_FILE,
        .storage_type       = ACC,
        .size_type          = FLD,
        .record_size        = sizeof(his_data_record_info),
        .info_name          = HIS_DATA_FILE,
        .save_num_per_file  = SAVE_NUM_PER_FILE_HIS_DATA,
        .file_num           = FILE_NUM_HIS_DATA,
        .file_name          = {HIS_DATA_FILE_0, HIS_DATA_FILE_1, HIS_DATA_FILE_2, HIS_DATA_FILE_3, HIS_DATA_FILE_4, HIS_DATA_FILE_5, }
    },
    { // 极值数据
        .his_record_info = {0},
        .auth_code = AUTH_CODE_EXTREME_DATA,
        .storage_type = NACC,
        .size_type = FLD,
        .record_size = sizeof(his_extreme_data_t),
        .data = NULL,
        .part_name = EXTREME_DATA_FILE_NAME,
        .min_save_num = 1,
        .max_save_num = 1,
        .save_num_per_file = 1,
        .file_num = 1,
        .file_name = {EXTREME_DATA_FILE},
    },
};

int init_dir(void)
{
    int res = SUCCESSFUL;
    struct stat st;
    /* 检查并创建 HIS_DATA_DIR */
    if (stat(HIS_DATA_DIR, &st) != 0)
    {
        res |= mkdir(HIS_DATA_DIR, 0);
    }

    /* 检查并创建 HIS_ALARM_DIR */
    if (stat(HIS_ALARM_DIR, &st) != 0)
    {
        res |= mkdir(HIS_ALARM_DIR, 0);
    }

    /* 检查并创建 HIS_ACTION_DIR */
    if (stat(HIS_ACTION_DIR, &st) != 0)
    {
        res |= mkdir(HIS_ACTION_DIR, 0);
    }
    return res;
}

int init_his_record(void)
{
    register_fill_event_record_cb(fill_event_record_callback);
    // 注册操作记录保存回调
    register_save_action_record_cb(fill_event_record_callback);
    if(FAILURE == register_his_record_tab_new(s_his_record, sizeof(s_his_record)/sizeof(s_his_record[0])))
    {
        rt_kprintf("init_his_record fail\n");
        return FAILURE;
    }

    return SUCCESSFUL;
}

/**
 * @description: 发布获取记录数目的读取消息，阻塞方式
 * @param {unsigned char} record_type - 记录类型
 * @param {unsigned short*} saved_num - 输出参数保存记录数量
 * @return {int} 失败返回FAILURE(-1)，成功返回SUCCESSFUL（0）
 */
short pub_get_saved_record_num_msg(unsigned char record_type, unsigned short *saved_num)
{
    rt_msg_t req_msg = NULL;
    rt_msg_t res_msg = NULL;
    unsigned char send_msg = record_type;
    unsigned short rec_msg = 0;
    short ret = RT_EOK;

    RETURN_VAL_IF_FAIL(saved_num != NULL, FAILURE);

    req_msg = rt_msg_create(LOG_MGR_GET_SAVED_RECORD_NUM, &send_msg, sizeof(send_msg));
    if(RT_NULL == req_msg)
    {
        return FAILURE;
    }
    res_msg = rt_msg_create(LOG_MGR_GET_SAVED_RECORD_NUM, &rec_msg, sizeof(rec_msg));
    if(RT_NULL == res_msg)
    {
        rt_msg_delete(req_msg);
        return FAILURE;
    }

    ret = rt_msg_send_req_block(THREAD_LOG, req_msg, res_msg, 10*READ_HIS_RECORD_TIMEOUT);

    if(ret != RT_EOK) {
        rt_msg_delete(res_msg);
        return FAILURE;
    }

    if(res_msg->data == NULL) {
        rt_msg_delete(req_msg);
        rt_msg_delete(res_msg);
        return FAILURE;
    }

    *saved_num = *(unsigned short *)res_msg->data;

    rt_msg_delete(req_msg);
    rt_msg_delete(res_msg);

    if (record_type == RECORD_TYPE_HIS_ALARM) {
        s_hisalarm_saved_num = *saved_num;
    }

    return SUCCESSFUL;
}

//发布操作记录读取消息，阻塞方式
short pub_hisrecord_read_msg(unsigned char rec_type, unsigned short rec_num, unsigned short offset, void *buff)
{
    rt_msg_t req_msg = NULL;
    rt_msg_t res_msg = NULL;
    if(buff == NULL)
    {
        return FAILURE;
    }
    read_record_msg_t rec_msg = {0, };

    rec_msg.record_type = rec_type;
    rec_msg.record_num = rec_num;
    rec_msg.offset = offset;

    switch(rec_type) {
        case RECORD_TYPE_HIS_ACTION:
            rec_msg.len = rec_num * sizeof(his_action_record_info);
            break;
        case RECORD_TYPE_HIS_DATA:
            rec_msg.len = rec_num * sizeof(his_data_record_info);
            break;
        case RECORD_TYPE_EXTREME_DATA:
            rec_msg.len = sizeof(his_extreme_data_t);
            break;
        case RECORD_TYPE_HIS_ALARM:
            rec_msg.len = rec_num * sizeof(his_alarm_info_t);
            break;
        default:
            return FAILURE;
    }

    req_msg = rt_msg_create(LOG_MGR_READ_RECORD, &rec_msg, sizeof(read_record_msg_t));
    if(RT_NULL == req_msg)
    {
        return FAILURE;
    }

    unsigned char temp_buf[64];  /* 足够大的缓冲区，最大结构体是24字节 */
    rt_memset_s(temp_buf, sizeof(temp_buf), 0, sizeof(temp_buf));
    res_msg = rt_msg_create(LOG_MGR_READ_RECORD, temp_buf, rec_msg.len);
    if(RT_NULL == res_msg)
    {
        rt_msg_delete(req_msg);
        return FAILURE;
    }

    int ret = rt_msg_send_req_block(THREAD_LOG, req_msg, res_msg, 10*READ_HIS_RECORD_TIMEOUT);

    if(ret != RT_EOK) {
        rt_msg_delete(res_msg);
        return FAILURE;
    }

    if(res_msg->data == NULL) {
        rt_msg_delete(req_msg);
        rt_msg_delete(res_msg);
        return FAILURE;
    }

    if(res_msg->data_size < rec_msg.len) {
        rt_msg_delete(req_msg);
        rt_msg_delete(res_msg);
        return FAILURE;
    }

    rt_size_t copy_len = (rec_msg.len < res_msg->data_size) ? rec_msg.len : res_msg->data_size;
    rt_memcpy_s(buff, rec_msg.len, res_msg->data, copy_len);

    rt_msg_delete(req_msg);
    rt_msg_delete(res_msg);
    return SUCCESSFUL;
}

/**
 * @description: 发布操作记录消息给历史记录组件存储
 * @param {unsigned char} id1   ID1
 * @param {unsigned char} id2   ID2
 * @param {unsigned char} index  序号
 * @param {char} *str 存储的内容
 * @return {short}  成功 SUCCESSFUL
 */
short pub_hisaction_save_msg(unsigned char id1, unsigned char id2,  unsigned char index, char *str)
{
    save_record_msg_t  rec_msg = {0,};

    rt_memset_s(&s_hisact_msg, sizeof(s_hisact_msg), 0, sizeof(s_hisact_msg));

    s_hisact_msg.id1 = id1;
    s_hisact_msg.id2 = id2;
    s_hisact_msg.index = index;

    time(&s_hisact_msg.save_time);

    rt_memcpy_s(s_hisact_msg.msg, sizeof(s_hisact_msg.msg), str, sizeof(s_hisact_msg.msg));

    rec_msg.auth_code = AUTH_CODE_HIS_ACTION;
    rec_msg.record_type = RECORD_TYPE_HIS_ACTION;
    rec_msg.len = sizeof(his_action_record_info);
    rec_msg.hisdata = &s_hisact_msg;

    pub_msg_to_thread(LOG_MGR_SAVE_ONE_RECORD, &rec_msg, sizeof(save_record_msg_t));

    return SUCCESSFUL;
}

/**
 * @description: 发布极值记录消息给历史记录组件存储
 * @param {his_extreme_data_t} *str 存储的内容
 * @return {short}  成功 SUCCESSFUL
 */
short pub_extremedata_save_msg(his_extreme_data_t *str)
{
    save_record_msg_t  rec_msg = {0,};

    RETURN_VAL_IF_FAIL(str != NULL, FAILURE);
    rt_memset_s(&s_extreme_msg, sizeof(s_extreme_msg), 0, sizeof(s_extreme_msg));
    rt_memcpy_s(&s_extreme_msg, sizeof(s_extreme_msg), str, sizeof(his_extreme_data_t));

    rec_msg.auth_code = AUTH_CODE_EXTREME_DATA;
    rec_msg.record_type = RECORD_TYPE_EXTREME_DATA;
    rec_msg.len = sizeof(his_extreme_data_t);
    rec_msg.hisdata = &s_extreme_msg;

    pub_msg_to_thread(LOG_MGR_SAVE_ONE_RECORD, &rec_msg, sizeof(save_record_msg_t));

    return SUCCESSFUL;
}


/**
 * @description: 发布历史告警消息给历史记录组件存储
 * @param {his_alarm_info_t} *str 存储的内容
 * @return {short}  成功 SUCCESSFUL
 */
short pub_hisalarm_save_msg(his_alarm_info_t *str)
{
    save_record_msg_t  rec_msg = {0,};
    unsigned char data_flag1 = 0;
    unsigned char data_flag2 = 0;
    char buff[MAX_LETTERS];

    rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));

    RETURN_VAL_IF_FAIL(str != NULL, FAILURE);
    rt_memset_s(&s_hisalarm_msg, sizeof(s_hisalarm_msg), 0, sizeof(s_hisalarm_msg));
    rt_memcpy_s(&s_hisalarm_msg, sizeof(s_hisalarm_msg), str, sizeof(s_hisalarm_msg));
    get_one_data(DCMU_DATA_ID_DATA_FLAG2, &data_flag2);
    data_flag2 |= FLAG2_HISALM;
    set_one_data(DCMU_DATA_ID_DATA_FLAG2, &data_flag2);

    rec_msg.auth_code = AUTH_CODE_HIS_ALARM;
    rec_msg.record_type = RECORD_TYPE_HIS_ALARM;
    rec_msg.len = sizeof(his_alarm_info_t);
    rec_msg.hisdata = (char*)&s_hisalarm_msg;

    pub_msg_to_thread(LOG_MGR_SAVE_ONE_RECORD, &rec_msg, sizeof(save_record_msg_t));

    if (s_hisalarm_saved_num++ == 0) {
        //没有调用获取历史告警数量或者没有历史告警
        pub_get_saved_record_num_msg(RECORD_TYPE_HIS_ALARM, &s_hisalarm_saved_num);
    }
    if (MAX_HIS_ALARM_NUM - s_hisalarm_saved_num <=  5) {
        get_one_data(DCMU_DATA_ID_DATA_FLAG2, &data_flag2);
        data_flag2 |= FLAG2_HISALMOV;		// 位DATAFLAG历史告警溢出
        set_one_data(DCMU_DATA_ID_DATA_FLAG2, &data_flag2);
    }
    get_one_data(DCMU_DATA_ID_DATA_FLAG1, &data_flag1);
    rt_snprintf_s( buff, sizeof(buff), "FLAG1:%x  FLAG2:%x", data_flag1, data_flag2);
    SetTraceStr( 10, buff );

    return SUCCESSFUL;
}

Static unsigned char index_special_proc(unsigned short alm_code, unsigned char id1, unsigned char id2, unsigned short alm_code_no_index)
{
    if (id1 == ID1_DCMU_ALARM && id2 == ID2_ALM_INRELAY)
    {
        return GET_ALM_PARA_BY_ALM_CODE(alm_code, 0x0000) - DCMU_ALM_ID_INPUT_RELAY1_ALARM_LEVEL + 1;
    }
    if (id1 == ID1_DCMU_OUTRLY && id2 == ID2_ALM_INRELAY)
    {
        return GET_ALM_PARA_BY_ALM_CODE(alm_code, 0x4000) - DCMU_ALM_ID_INPUT_RELAY1_ALARM_RELAY + 1;
    }

    return alm_code - alm_code_no_index;
}

Static short fill_event_record_callback(event_record_cb_param_t *event_record_cb_param)
{
    save_record_msg_t  rec_msg = {0,};
    event_record_cb_param_t *rec;
    MIBTable_ParaNode* ptParaNode = NULL;

    RETURN_VAL_IF_FAIL(event_record_cb_param != NULL, FAILURE);

    rec = (event_record_cb_param_t *)event_record_cb_param;
    rt_memset_s(&s_hisact_callback_msg, sizeof(s_hisact_callback_msg), 0, sizeof(s_hisact_callback_msg));

    // 获取参数节点，rec->event_id:set_one_para携带的参数ID,不带offset
    if (rec->type != 0xFF && rec->type != 0xFE)
    {
        ptParaNode = GetParaNodeByType(rec->event_id, rec->type);
        RETURN_VAL_IF_FAIL(ptParaNode != NULL, FAILURE);
    }

    switch(rec->type) {
        case 0x02:  // 参数
        case 0x04:  // 告警级别
        case 0x05:  // 告警干接点            
            s_hisact_callback_msg.id1 = ptParaNode->ucID1;
            s_hisact_callback_msg.id2 = ptParaNode->ucID2;
            s_hisact_callback_msg.index = index_special_proc(rec->event_id, ptParaNode->ucID1, ptParaNode->ucID2, ptParaNode->wParaId);
            break;
        case 0xFF:  //升级
            s_hisact_callback_msg.id1 = ID1_DCMU_CTRL;
            s_hisact_callback_msg.id2 = ID2_CTL_UPDATE_RST;
            s_hisact_callback_msg.index = rec->info[0];

            if (s_hisact_callback_msg.index == APP_DOWNLOAD_SUCCESS) {
                set_software_reset_reason(RESET_REASON_UPDATE_PROGRAM);
            } else if (s_hisact_callback_msg.index == ZK_DOWNLOAD_SUCCESS) {
                set_software_reset_reason(RESET_REASON_UPDATE_ZK);
            }
            break;
        default:
            break;
    }

    if (rec->info_len > LEN_EVENTMSG) // rt_memcpy_s长度校验，因为液晶最大显示宽度（msg）是18
    {
        rec->info_len = LEN_EVENTMSG;
    }
    time(&s_hisact_callback_msg.save_time);
    rt_memcpy_s(s_hisact_callback_msg.msg, sizeof(s_hisact_callback_msg.msg), rec->info, rec->info_len);

    rec_msg.auth_code = AUTH_CODE_HIS_ACTION;
    rec_msg.record_type = RECORD_TYPE_HIS_ACTION;
    rec_msg.len = sizeof(his_action_record_info);
    rec_msg.hisdata = &s_hisact_callback_msg;

    pub_msg_to_thread(LOG_MGR_SAVE_ONE_RECORD, &rec_msg, sizeof(save_record_msg_t));
    return SUCCESSFUL;
}

Static void pack_int32u_data(unsigned char* buff, unsigned int* offset, char precision, void* data)
{
    unsigned int ui_data = 0;
    if(0 != precision)
    {
        ui_data = (unsigned int)(*(float*)data * pow(10, precision));
    }
    else
    {
        ui_data = *(unsigned int*)data;
    }

    put_uint32_to_buff(buff + *offset, ui_data);
    *offset += sizeof(unsigned int);
}

Static void pack_int32s_data(unsigned char* buff, unsigned int* offset, char precision, void* data)
{
    int ui_data = 0;
    if(0 != precision)
    {
        ui_data = (int)(*(float*)data * pow(10, precision));
    }
    else
    {
        ui_data = *(int*)data;
    }

    put_int32_to_buff(buff + *offset, ui_data);
    *offset += sizeof(int);
}

Static void pack_int16u_data(unsigned char* buff, unsigned int* offset, char precision, void* data)
{
    unsigned short si_data = 0;
    if(0 != precision)
    {
        si_data = (unsigned short)(*(float*)data * pow(10, precision));
    }
    else
    {
        si_data = (unsigned short)(*(unsigned int*)data);
    }

    put_uint16_to_buff(buff + *offset, si_data);
    *offset += sizeof(unsigned short);
}

Static void pack_int16s_data(unsigned char* buff, unsigned int* offset, char precision, void* data)
{
    short si_data = 0;
    if(0 != precision)
    {
        si_data = (short)(*(float*)data * pow(10, precision));
    }
    else
    {
        si_data = (short)(*(unsigned int*)data);
    }

    put_int16_to_buff(buff + *offset, si_data);
    *offset += sizeof(short);
}

Static void pack_int8u_data(unsigned char* buff, unsigned int* offset, char precision, void* data)
{
    if(0 != precision)
    {
        *(buff + *offset) = (unsigned char)(*(float*)data * pow(10, precision));
    }
    else
    {
        *(buff + *offset) = (unsigned char)(*(unsigned int*)data);
    }

    *offset += sizeof(unsigned char);
}

Static void pack_int8s_data(unsigned char* buff, unsigned int* offset, char precision, void* data)
{
    if(0 != precision)
    {
        *(buff + *offset) = (char)(*(float*)data * pow(10, precision));
    }
    else
    {
        *(buff + *offset) = (char)(*(int*)data);
    }

    *offset += sizeof(char);
}

Static void pack_bit_data(unsigned char* buff, unsigned int* offset, char precision, void* data)
{
    s_bit_num ++;
    *(buff + *offset) += (*(unsigned int*)data & 0x01) << (8 - s_bit_num);
    if(s_bit_num == 8)
    {
        s_bit_num = s_bit_num % 8;
        *offset += 1;
    }
}

Static his_data_pack_t s_his_data_pack[] =
{
    {1, TYPE_INT32U, pack_int32u_data},
    {1, TYPE_INT32S, pack_int32s_data},
    {1, TYPE_INT16U, pack_int16u_data},
    {1, TYPE_INT16S, pack_int16s_data},
    {1, TYPE_INT8U,  pack_int8u_data},
    {1, TYPE_INT8S,  pack_int8s_data},
    {0, TYPE_INT32U, pack_int32u_data},
    {0, TYPE_INT32S, pack_int32s_data},
    {0, TYPE_INT16U, pack_int16u_data},
    {0, TYPE_INT16S, pack_int16s_data},
    {0, TYPE_INT8U,  pack_int8u_data},
    {0, TYPE_INT8S,  pack_int8s_data},
    {0, TYPE_BIT,    pack_bit_data},
};

Static unsigned char find_pack_tab_index(unsigned char precision, unsigned char storage_type)
{
    int tab_num = sizeof(s_his_data_pack) / sizeof(his_data_pack_t);
    char precision_flag = 0;
    int loop = 0;
    if(precision != 0)
    {
        precision_flag = 1;
    }
    for(loop = 0; loop < tab_num; loop ++)
    {
        if(s_his_data_pack[loop].storage_type == storage_type && s_his_data_pack[loop].precision_flag == precision_flag)
        {
            return loop;
        }
    }
    return 0xFF;
}

Static int save_sample_data_to_his_data(void * his_data)
{
    unsigned int offset = 0;
    int loop = 0;
    int id_loop = 0;
    unsigned char index = 0;
    s_bit_num = 0;
    unsigned char data[4] = {0};
    his_data_record_info* his_data_record = (his_data_record_info *)his_data;
    time(&his_data_record->save_time);
    int time_head_len = sizeof(time_t);
    unsigned char* buff = (unsigned char*)his_data_record;

    for(loop = 0; loop < sizeof(realdata_info_tab) / sizeof(realdata_info_t); loop ++)
    {
        for(id_loop = 0; id_loop < realdata_info_tab[loop].id_num; id_loop ++)
        {
            rt_memset_s(data, sizeof(data), 0, sizeof(data));
            get_one_data(realdata_info_tab[loop].realdata_id_offset + id_loop, (void*)data);
            index = find_pack_tab_index(realdata_info_tab[loop].precision, realdata_info_tab[loop].storage_type);
            if(index == 0xFF)
            {
                return FAILURE;
            }
            s_his_data_pack[index].pack_data(buff + time_head_len, &offset, realdata_info_tab[loop].precision, data);
        }
    }
    if(s_bit_num != 0)    // 当bit型数据不满足8个，也按1字节计算
    {
        offset += 1;
    }
    if(offset != HISDATA_LEN)
    {
        return FAILURE;
    }
    return SUCCESSFUL;
}

int pub_his_data_save_msg(void)
{
    save_record_msg_t  rec_msg = {0,};
    rt_memset_s(&s_hisdata_record, sizeof(s_hisdata_record), 0, sizeof(s_hisdata_record));

    if(SUCCESSFUL != save_sample_data_to_his_data(&s_hisdata_record))
    {
        return FAILURE;
    }

    rec_msg.auth_code = AUTH_CODE_HIS_DATA;
    rec_msg.record_type = RECORD_TYPE_HIS_DATA;
    rec_msg.len = sizeof(his_data_record_info);
    rec_msg.hisdata = &s_hisdata_record;
    pub_msg_to_thread(LOG_MGR_SAVE_ONE_RECORD, &rec_msg, sizeof(save_record_msg_t));
    return SUCCESSFUL;
}

unsigned char judge_reset_reason(unsigned char mcu_reset_reason, unsigned char software_reset_reason)
{
    unsigned char i = 0;
    char msg[LEN_EVENTMSG] = {0};

    Static const mcu_reset_reason_t mcu_reset_map[] =
    {
        {RESET_REASON_POWER_POR, "Power-on"},   // 上电复位
        {RESET_REASON_POWER_BOR, "Brownout"},   // 欠压复位
        {RESET_REASON_WWDT_OUT,  "WWDT"},       // 看门狗复位
        {RESET_REASON_IWDT_OUT,  "IWDT"},       // 独立看门狗复位
        {RESET_REASON_PIN,       "PIN"},        // 引脚复位
        {RESET_REASON_SOFTWARE,  "Software"},   // 软件复位（原因需要进一步判断，放到最后）
    };

    Static const mcu_reset_reason_t software_reset_map[] =
    {
        {RESET_REASON_UNKNOWN,        "Unknown"},        // 未知原因复位
        {RESET_REASON_UPDATE_ZK,      "Update ZK"},      // 更新字库复位
        {RESET_REASON_UPDATE_PROGRAM, "Update Program"}, // 更新程序复位
        {RESET_REASON_REMOTE_CTRL,    "Remote Ctrl"},    // 远程控制复位
        {RESET_REASON_APPTEST,        "Apptest"},        // Apptest复位
    };

    if(mcu_reset_reason == mcu_reset_map[5].id) // 如果是软件复位，则进一步判断软件复位原因
    {
        for (i = 0; i < (sizeof(software_reset_map) / sizeof(mcu_reset_reason_t)); i++)
        {
            if (software_reset_reason == software_reset_map[i].id)
            {
                rt_strncpy_s(msg, sizeof(msg), software_reset_map[i].msg, sizeof(msg) - 1);
                break;
            }
        }

        pub_hisaction_save_msg(ID1_DCMU_CTRL, ID2_CTL_RESET, 0xFF, msg);
        // rt_kprintf("reset reason: %s\n", msg);

        return software_reset_reason; // 返回软件复位原因
    }

    for (i = 0; i < (sizeof(mcu_reset_map) / sizeof(mcu_reset_reason_t) - 1); i++)
    {
        if (mcu_reset_reason == mcu_reset_map[i].id)
        {
            rt_strncpy_s(msg, sizeof(msg), mcu_reset_map[i].msg, sizeof(msg) - 1);
            break;
        }
    }

    pub_hisaction_save_msg(ID1_DCMU_CTRL, ID2_CTL_RESET, 0xFF, msg);
    // rt_kprintf("reset reason: %s\n", msg);

    return mcu_reset_reason; // 返回硬件复位原因
}

// 保存软件复位原因到片外flash
char set_software_reset_reason(unsigned char reset_reason)
{
    part_data_t part_data = {0};
    part_data.buff = &reset_reason;
    part_data.len = sizeof(reset_reason);
    part_data.offset = 0;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), "%s", RESET_FILE);
    if (SUCCESSFUL != storage_process(&part_data, write_opr))
    {
        return FAILURE;
    }
    return SUCCESSFUL;
}

// 从片外flash获取软件复位原因
unsigned char get_software_reset_reason(void)
{
    unsigned char reset_reason = RESET_REASON_UNKNOWN;
    part_data_t part_data = {0};
    part_data.buff = &reset_reason;
    part_data.len = sizeof(reset_reason);
    part_data.offset = 0;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), "%s", RESET_FILE);
    if (SUCCESSFUL != storage_process(&part_data, read_opr))
    {
        reset_reason = RESET_REASON_UNKNOWN;
    }

    set_software_reset_reason(RESET_REASON_UNKNOWN); // 文件读取成功或失败，都要将复位原因置为未知，防止异常重启后软件复位原因显示的是上次的复位原因

    return reset_reason;
}

// 开机后保存复位原因
unsigned char save_reset_reason(void)
{
    unsigned char mcu_reset_reason = 0;
    unsigned char software_reset_reason = 0;
    unsigned char reset_reason = 0;

    get_mcu_reset_reason(&mcu_reset_reason); // 硬件复位原因
    software_reset_reason = get_software_reset_reason(); // 软件复位原因
    reset_reason = judge_reset_reason(mcu_reset_reason, software_reset_reason);

    return reset_reason;
}