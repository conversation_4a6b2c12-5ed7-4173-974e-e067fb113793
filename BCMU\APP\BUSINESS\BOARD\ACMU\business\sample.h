/**
 * @brief
 */

#ifndef _ACMU_SAMPLE_H_
#define _ACMU_SAMPLE_H_

#ifdef __cplusplus
extern "C" {
#endif
#define ADC_DEV_NAME        "sync_adc"
#define PWM_DEV_NAME        "pwm11"

#define ADC_SAMPLE_COUNT     (15 * 200)

#define SAMPLE_THREAD_DELAY_TIME 200 // sample线程延时时间200ms
#define RECORDER_THREAD_DELAY_TIME 20 // 电压电流录波时间，不可随意变更，与采样频率相关

#define ALARM_EXSIT 1
#define ALARM_NOT_EXSIT 0

#define TEMPERATURE_MAX     100
#define TEMPERATURE_MIN     -40

#define HUMIDITY_MAX     95
#define HUMIDITY_MIN     10

typedef struct {
    int humidity;   // 湿度值（%RH）
    int frequency;  // 对应的输出频率（Hz）
} HumidityData;

// 相序枚举（A/B/C相）
typedef enum {
    PHASE_A,  // PB4=0, PB1=0
    PHASE_B,  // PB4=0, PB1=1 (0x01)
    PHASE_C   // PB4=1, PB1=0 (0x10)
} PhaseType;

// 单相测量结果（电流/电压有效值）
typedef struct {
    float current;  // 通道9（电流）转换后的值（A）
    float voltage;  // 通道11（电压）转换后的值（V）
} SinglePhaseValue;

// 三相测量结果集
typedef struct {
    SinglePhaseValue a_phase;  // A相结果
    SinglePhaseValue b_phase;  // B相结果
    SinglePhaseValue c_phase;  // C相结果
} ThreePhaseValues;

typedef enum {
    UNCONFIRMED_MODE,    // 未确认模式
    SENSOR_SAMPLE_MODE,  // 传感器模式
    TRANSMIT_SAMPLE_MODE // 变送器模式
} sample_mode_t;

void* sample_init(void *param);
void sample_main(void* parameter);
int init_extreme_data(void);
int delete_extreme_data(void);
#ifdef __cplusplus
}
#endif      //< __cplusplus

#endif      //< __ACMU_SAMPLE_H_
