#include <string.h>
#include <stdlib.h>
#include <rtthread.h>
#include "north_main.h"
#include "io_control.h"
#include "const_define_in.h"
#include "realdata_id_in.h"
#include "update_and_upload_manage.h"
#include "para_id_in.h"
#include "utils_heart_beat.h"

Static dev_inst_t* dev_dcmu = NULL;
Static int s_com_time_out_count = 0;
Static unsigned char s_apptest_can_test_flag = FALSE; // 是否正在执行apptest协议的can口回环测试
Static dev_inst_t* s_north_dev_inst[PROTOCOL_INDEX_MAX] = {NULL};

Static unsigned char s_dev_type[] =
{
    DEV_NORTH_DCMU_CAN0,
};

Static msg_map north_msg_map[] =
{
    {0, NULL}, // 临时添加解决编译问题
};

Static dcmu_mgr_t* init_thread_data(link_inst_t* link_inst, unsigned char mod_id)
{
    dcmu_mgr_t* dcmu_mgr = NULL;

    dcmu_mgr = rt_malloc(sizeof(dcmu_mgr_t));
    if (dcmu_mgr == NULL)
        return NULL;

    dcmu_mgr->cmd_buff = rt_malloc(sizeof(cmd_buf_t));
    if (dcmu_mgr->cmd_buff == NULL)
    {
        goto MGR_FREE;
    }

    dcmu_mgr->cmd_buff->addr_dev_data = 0x01;
    dcmu_mgr->link_inst = link_inst;
    return dcmu_mgr;

MGR_FREE:
    free(dcmu_mgr);
    return NULL;
}

Static int handle_received_data(dcmu_mgr_t* north_mgr)
{
    for (int loop = 0; loop < ARR_SIZE(s_dev_type); loop++)
    {
        if (SUCCESSFUL == protocol_parse_recv(s_north_dev_inst[loop], north_mgr->cmd_buff))
        {
            cmd_send(s_north_dev_inst[loop], north_mgr->cmd_buff);
            rt_sem_clear_value(&(north_mgr->link_inst->rx_sem));
            break;
        }
    }
    return 0;
}

void* init_can0_comm(void* param)
{
    server_info_t* server_info = (server_info_t*)param;
    dcmu_mgr_t* north_mgr = NULL;

    for (int loop = 0; loop < ARR_SIZE(s_dev_type); loop++)
    {
        dev_dcmu = init_dev_inst(s_dev_type[loop]);
        RETURN_VAL_IF_FAIL(dev_dcmu != NULL, NULL);
        s_north_dev_inst[loop] = dev_dcmu;
    }

    server_info->server.server.map_size = sizeof(north_msg_map) / sizeof(msg_map);
    register_server_msg_map(north_msg_map, server_info);
    north_mgr = init_thread_data(dev_dcmu->dev_type->link_inst, MOD_DCMU_NORTH);
    upload_manage_init();

    return north_mgr;
}

void can0_comm_thread(void* param)
{
    dcmu_mgr_t* north_mgr = (dcmu_mgr_t*)param;
    thread_monitor_register("can0");

    while (is_running(TRUE))
    {
        thread_monitor_update_heartbeat();

        // 如果正在进行apptest协议的串口回环测试，则跳过中断，避免影响测试结果
        if(s_apptest_can_test_flag == TRUE)
        {
            s_com_time_out_count = 0;
            rt_thread_mdelay(10);  // 避免空转
            continue;
        }

        if (RT_EOK == rt_sem_take(&(north_mgr->link_inst->rx_sem), THREAD_TICK * 5))
        {
            s_com_time_out_count = 0;

            if (FAILURE == linker_recv(s_north_dev_inst[0], north_mgr->cmd_buff))
            {
                continue;
            }

            handle_received_data(north_mgr);
        }

        if (s_com_time_out_count > 10)
        {
            s_com_time_out_count = 0;
            north_mgr->link_inst->r_cache.index = 0;
            north_mgr->link_inst->r_cache.len = 0;
        }

        s_com_time_out_count++;
    }
}

unsigned char set_apptest_can_test_flag(unsigned char flag_set)
{
    s_apptest_can_test_flag = flag_set;
    return SUCCESSFUL;
}