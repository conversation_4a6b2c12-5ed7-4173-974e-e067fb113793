/**************************************************************************
 * 文件名称：protocolPAD.h
 * 文件说明：PAD协议模块头文件（协议组包与协议判断等过程隔离）
 ***************************************************************************/

#ifndef PROTOCOLPAD_H_
#define PROTOCOLPAD_H_

#include "common.h"

#ifdef __cplusplus
extern "C"
{
#endif

/************************   CID2码   ***********************/
#define GET_ANALOG_PAD 0x80               // 获取模拟量量化后的数据(定点数)
#define GET_ALARM_PAD 0x81                // 获取告警量
#define GET_FACTORY_INFO_PAD 0x82         // 获取设备厂家信息
#define GET_PARA_PAD 0x47                 // 获取系统参数(定点数)
#define SET_PARA_PAD 0x49                 // 设定系统参数(定点数)
#define GET_SPECIAL_PARA_PAD 0x83         // 获取电池特定参数
#define SET_SPECIAL_PARA_PAD 0x84         // 设置电池特定参数
#define GET_BATT_NUM_INFO_PAD 0x86        // 获取电池数量
#define REMOTE_CONTROL_PAD 0x45           // 遥控
#define GET_CMCC_PARA_PAD 0x87            // 获取中移新增参数
#define SET_CMCC_PARA_PAD 0x88            // 设定中移新增参数
#define GET_HIS_DATA_PAD 0x89             // 获取历史数据
#define GET_HIS_ALARM_PAD 0x8A            // 获取历史告警
#define GET_HIS_ACTION_PAD 0x8B           // 获取历史操作
#define GET_NEW_ANALOG_PAD 0x8E           // 获取模拟量量化后的数据(定点数)(D121新增)
#define GET_SPE_FACT_PAD 0x92             // 获取特定设备厂家信息(D121新增)
#define SET_SPE_FACT_PAD 0x93             // 设置特定设备厂家信息(D121新增)
#define GET_NEW_ALARM_PAD 0x94            // 获取告警量(D121新增)
#define GET_NEW_PARA_PAD 0x95             // 获取系统参数（D121新增）
#define SET_NEW_PARA_PAD 0x96             // 设置系统参数（D121新增）
#define REMOTE_CONTROL_NEW_PAD  0x97      // 遥控(D121新增)
#define GET_ALARM_LEVEL_PAD 0x98          // 获取告警级别(D121新增)
#define SET_ALARM_LEVEL_PAD 0x99          // 设置告警级别(D121新增)
#define GET_ALARM_RLY_PAD 0x9A            // 获取告警干接点(D121新增)
#define SET_ALARM_RLY_PAD 0x9B            // 设置告警干接点(D121新增)
#define GET_TIME_PAD  0x9C                // 获取时间(D121新增)
#define SET_TIME_PAD  0x9D                // 设置时间(D121新增)
#define REMOTE_SWAP_BAUD_PAD 0x9E         // 波特率切换
#define GET_BSDIFF_UPGRADE_VER_PAD 0x9F   // 获取差分升级版本标志
#define GET_DCR_RECORD_PAD 0xA0			  // 获取直流内阻记录
#define GET_BMS_BALANCE_CAPACITY 0xA2     // 获取均衡容量记录
#define GET_DCR_INFO_PAD 0xA1			  // 获取直流内阻信息
#define GET_REAL_ALARM_PAD 0xA3           // 获取真实告警量(D121 新增)-软银版本支持
#define GET_BMS_STATISTICS_NEW 0xA4       // 获取电芯统计记录（新增）
#define GET_BALANCE_CAPACITY   0xA5       // 获取均衡容量实时信息  CID1:41H CID2:A5H
#define GET_CUSTOMER_NAME_PAD   0xA6      // 获取客户名称
#define GET_BMS_EXTREME_RECORD  0xA7      // 获取极值记录
#define GET_FACTORY_INFO_EXTEND 0xA8      // 获取厂家扩展信息
#define GET_ELABORATE_MEMORY_MANAGEMENT  0xA9        //获取内存精细化管理信息
/*******************   遥控 Command Type   *****************/
#define BATT_IN_PLACE_FLAG 0x10    // 电池在位指示
#define REMOTE_CHARGE_ON 0x11      // 遥控充电回路闭合
#define REMOTE_CHARGE_OFF 0x12     // 遥控充电回路断开
#define REMOTE_DISCHARGE_ON 0x13   // 遥控放电回路闭合
#define REMOTE_DISCHARGE_OFF 0x14  // 遥控放电回路断开
#define SET_BATT_MANUAL_UNLOCK          0x24  //人工防盗解锁
#define SWITCH_BATT_ADDR                0x25  // 电池地址切换
#define MANUAL_CANCEL_DEVICE_DEFENCE    0x2A  //人工撤防

/*******************   波特率 Command Type   *****************/
#define SET_HIGH_BAUDRATE  0x20    //波特率握手命令

/*****************   历史记录 Command Type   ***************/
#define GET_RECORD_TIME_SLOT 0x00 // 获取时间段的历史记录
#define GET_RECORD_CORRECT 0x01   // 收到历史记录正确，上送下一条历史记录
#define GET_RECORD_WRONG 0x02     // 收到历史记录错误，重发上一条历史记录
#define GET_RECORD_COMPLETE 0x03  // 接受完所有的历史记录
#define GET_RECORD_NUM 0x04       // 获取特定时间段历史记录条数

/********************   历史记录数据长度    *****************/
#define HIS_RECORD_LEN_TYPE_ID 4       // 01H 02H 03H: 2 * (CommandType + CommandId) = 4
#define HIS_RECORD_LEN_TYPE_TIME 30    // 04H: 2 * (CommandType + CommandTime) = 30
#define HIS_RECORD_LEN_TYPE_ID_TIME 32 // 00H: 2 * (CommandType + CommandId + CommandTime) = 32

/**********************   特殊字节位置    *******************/
#define COMMAND_TYPE 7 // CommandType字节位置
#define COMMAND_ID 8   // CommandId字节位置(历史记录中使用)

#define DCR_RECORD_LEN 24 // 7byte(时间) + 1byte(cellVoltNUm) + 2*8byte(8个单体DCR)

typedef struct
{
    BYTE	ucHeaterFilmFailure; //  加热膜失效
    BYTE	ucCellDamagePrt; //  单体损坏保护
    BYTE	ucCellTempSensorInvalidAlm; //  单体温度传感器无效告警
    BYTE	ucChgLimitLoopInvalid; //  充电/限流回路失效
    BYTE	ucFuseError; //  熔丝损坏
    BYTE	ucEnvTempHighAlm; //  环境温度高告警
}T_SoftBankAlarmStruct;

typedef struct
{
    size_t offset;
    BYTE*   pucAlarmValue;
}T_SoftBankAlarmConfigStruct;

BYTE GetChargeTestMode(void);
#ifdef __cplusplus
} /* end of the 'extern "C"' block */
#endif

#endif
