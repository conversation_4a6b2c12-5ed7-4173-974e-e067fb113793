
#include <rtthread.h>
#include "sfud.h"
#include "flash.h"
#include "storage.h"
#include "data_type.h"
#include "spi_flash.h"
#include "circle_buff.h"
#include "partition_def.h"
#include "concurrent_upgrade.h"
#include "utils_server.h"

#define EARSE_FAN_BYTES       0x1000       //需要擦除的扇区的大小
static msg_map file_msg_map[] =
{
     {0,NULL},
};

Static int s_erase_flag = FALSE;
Static T_AppDataSaveStruct s_update_buff = {0};

Static int deal_norflash_erase(void)
{
    rt_tick_t start = rt_tick_get();
    rt_tick_t end = 0;
    int ret = 0;
    unsigned char* data = NULL;
    // 获取触发成功标志位后，擦除升级需要使用的norflash
    if((!s_erase_flag) && get_trigger_flag())
    {
        ret = handle_storage(erase_opr, UPDATE_DOWNLOAD_PART, data, 0, 0);
        if(ret == SUCCESSFUL){
            s_erase_flag = TRUE;
            end = rt_tick_get();
            rt_kprintf("norflash擦除耗费的时间, %d ms\n", end - start);
        }
    }
    return ret;
}

Static int deal_update_data(T_AppDataSaveStruct* s_update_buff)
{
    return handle_storage(write_opr, UPDATE_DOWNLOAD_PART,  s_update_buff->aucData, APP_DATA_LEN, (s_update_buff->ulFrameNo - 1)*APP_DATA_LEN);
}

Static boolean deal_update_process(void)
{
    unsigned short i = 0;
    deal_norflash_erase();
    if(s_erase_flag)
    {
        for(i = 0; i< MAX_UPDATE_DATA_CACHE_NUM; i++)
        {
            if (read_circle_buff(&s_update_buff, 1))
            {
                deal_update_data(&s_update_buff);
            }
            else
            {
                return FALSE;
            }
        }    
        return TRUE;
    }

    return FALSE;
}

//升级流程结束（无论成功与否）,调用此接口重置到初始化状态
boolean reset_to_init_update(void)
{
    s_erase_flag = FALSE;
    reset_circle_buff();
    return TRUE;
}

void* init_file_save(void *param)
{
    server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(file_msg_map) / sizeof(msg_map);
    register_server_msg_map(file_msg_map, server_info);

}

/* 备份数据接收主函数 */
void process_save_data(void* parameter)
{
    while (is_running(TRUE)) {
        rt_thread_delay(10);
        deal_update_process();
    }
}