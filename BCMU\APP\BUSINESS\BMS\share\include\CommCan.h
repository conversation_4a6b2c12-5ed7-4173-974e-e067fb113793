#ifndef SOFTWARE_SRC_APP_COMMCAN_H_
#define SOFTWARE_SRC_APP_COMMCAN_H_

#include <rtdevice.h>
#include "hisdata.h"
#include "battery.h"

#ifdef __cplusplus
extern "C" {
#endif
typedef struct {
    BYTE ucMasterAddr;
    BYTE ucAddress;
    BYTE ucGroup;
    BOOLEAN bMaster;
    ULONG ulSn;
    WORD wCnt;
}T_BmsTCB;

/****************定义协议宏*******************************/
#define SET_PARA_CYCLE  6000
#define REALDATA_CYCLE  100
#define SNMPDATA_CYCLE  1500
#define CONN_TEMP_REFRESH 3
#define CONN_TEMP_HIGH (95.0f)
#define CONN_TEMP_HIGH_RESTORE (60.0f)
#define SLAVE_MAX_NUM 32   //不大于SALVE_NUM

#define CAN_DEV 0
#define CAN2_DEV 1

////地址和序号是相差为1的关系
#define DEV_ADDR_NORTH      0x01
#define DEV_ADDR_SC        	0x01


#define FRAG_MAX_NUM        10          // 最大分段数量

#ifdef RT_CAN_USING_NORMAL_CAN
#define CAN_FRAME_MAX_LEN   8
#else
#define CAN_FRAME_MAX_LEN   64
#endif

//CAN接收数据缓冲区定义
#define COMM_CAN_MAX_BUFF_LEN        650     // 数据帧最大长度是650Bytes大于40*16
#define LNK_LEVEL    0x01     //SU-SM通讯最高位yinlu
#define DEV_TYPE_SC                     0x7D
#define DEV_TYPE_BMS                    0x29   
#define BROAD_CAST_ADDR     0
#define DLFRAM_MAX_NUM      50

//组间并机通信ID
#define ID_SET_PARA         0x01
#define ID_GET_REALDATA     0x02
#define ID_SET_CHRGCURR     0x04
#define ID_SET_CHRGCURR_ACK 0x08
#define ID_SET_WORKMODE     0x0F
#define ID_SET_WORKMODE1    0x1F    //兼容迭代1
#define ID_GET_HIGHSOC      0x1E    //新增
#define ID_SET_DISCHRGVOL   0x1C    //放电电压
#define ID_SET_FM_CTRL      0X1B   //调频控制命令
#define ID_GET_SOC_POWER    0x1A   //获取电池充电功率

#define ID_SET_EQUAL_CURR   0x1D    ////均流电流

#define ID_SET_CONSRV       0x20
#define ID_GET_BATT_CAPACITY     0x21   // 获取额定容量

#define ID_SET_SYN_TIME_MODE     0x22  // 同步主机系统时间及放电模式
#define ID_SET_POWERDOWN_VOLT 0x23        // 设置从机的掉电电压
#define ID_SET_EXTERAL_POWER_STATUS 0x25  //
#define ID_SCAN_SLAVE_DEV           0x26  ///< 设备统计指令
#define ID_SET_UPDATE_FLAG          0x27  ///< 升级标志指令
#define ID_SET_ROTATE_CTRL  0x30
#define FN_ADDR_COMPETE     0x70

#define FN_FORCE_MASTER       0x71
#define FN_SITE_ANTITHEFT     0x73 // 站点防盗功能(0x72预留)

#define ID_GET_REALDATA3      0x31
#define ID_SET_CONN_TEMP_HIGH 0x32
#define ID_CAN1_FIRE_LOCK     0x33

#define ID_CAN1_ALARM_OCCUR_UPLOAD    0x34 // CAN1告警产生上送给主机
#define ID_CAN1_ALARM_MISS_UPLOAD     0x35 // CAN1告警消失上送给主机

#define NORTH_FRAM_MAX_NUM          (75)
#define MASTER_SLAVE_COMM_FAIL_CNT  (10)
#define COMM_FAIL_TO_SNMP_AGENT_CNT (8)
#define BMS_UPDATE_POLL_TIME_OUT_CNT    (6000)      // 1分钟轮询
#define BMS_UPDATE_TIME_OUT_CNT     (30000)         // 5分钟升级计时
#define BMS_UPDATE_POLL_INTERVAL_CNT    (20)        // 200ms轮询间隔
#define BMS_UPDATE_POLL_SLAVE_CNT       (1)         // 单台轮询最大次数
#define FUNC_BMS_UPDATE_POLL_MASTER_FRM  0xFF       // 并机轮询主机帧功能码
#define FUNC_BMS_UPDATE_POLL_SLAVE_FRM   0xFE       // 并机轮询从机帧功能码

/************************************
 * 互备份文件类型（互备份CAN链路长帧DATA首字节）
 * 因CAN链路最大接收一包长帧为530B，如果互备份一包数据超过530B，需要分包发送、接收
 * 例如HISEXTREME一包数据大小920B，需要分两包，因此定义两个文件类型:0xE2、0xE3（0xE1底层通讯协议已占用）
 * ***********************************/
#define ID_BACKEUP_HISDATA           0xA1
#define ID_BACKEUP_HISALARM          0xB1
#define ID_BACKEUP_HISACT            0xC1
#define ID_BACKEUP_BDURECORD         0xD1
#define ID_BACKEUP_HISEXTREME_PACK1  0xE2
#define ID_BACKEUP_HISEXTREME_PACK2  0xE3
#define ID_BACKEUP_ANALYSE           0xF1

#define ID_SNMP_PARA_SET          0xF8
#define ID_SNMP_PARA_STR_SET      0xF9
#define ID_SNMP_REALDATA          0xFA
#define ID_SNMP_REALALARM         0xFB
#define ID_SNMP_SYNCPARA          0xFC
#define ID_SNMP_SYNCPARA_STR      0xFD
#define ID_SNMP_FACTINFO          0xFE
#define ID_SNMP_CTRL              0xFF

/*电池地址获取方式*/
#define AUTO_MODE   0x00   // 自动获取
#define MANUAL_MODE 0x01   // 手动获取

/* CAN接收互备份数据缓存大小及条数 */
#define CAN_BACKUP_DATA_BUFF (530)
/* 互备份DATA最大长度<=(NORTH_FRAM_MAX_NUM-1)*7-13,当前定为500,13:互备份DATA以外字节数 */
#define CAN_BACKUP_DATA_MAX  (500)
#define CAN_BACKUP_NUM_MAX   (5)

#define CAN_FRM_FIX_DSTADDR  0x7F    //CAN长帧固定目标设备地址（用于解决SNMP和互备份长帧分帧后,误触发B3休眠问题）

/*********************  数据结构定义  **********************/
//位段定义
#pragma pack(push, 1)
typedef struct
{
    rt_uint32_t DstAdr  : 7; //目标设备地址
    rt_uint32_t DstDev  : 7; //目标设备类型
    rt_uint32_t SrcAdr  : 7; //源设备地址
    rt_uint32_t SrcDev  : 7; //源设备类型
    rt_uint32_t Level : 1;  //报文发送优先级
    rt_uint32_t IDE:1;    //是否为扩展帧
    rt_uint32_t RTR:1;    //是否为远程帧
    rt_uint32_t rsv:1;   
    rt_uint32_t len : 8;
    rt_uint32_t priv : 8;
    rt_int32_t hdr : 8;
    rt_uint32_t reserved : 8;
}T_CAN_Header;
#pragma pack(pop)

#pragma pack(push, 1)
typedef struct
{
    rt_uint8_t SegNum : 7;                     // 低７位SegNum，先定义的在低位 
    rt_uint8_t SegFlg : 1;                     // 最高位SegFlg       
    rt_uint8_t aucData[CAN_FRAME_MAX_LEN-1];   // 数据
}T_CAN_DataFrame; // CAN数据帧DATA段结构定义
#pragma pack(pop)

/**
 * struct can_frame - CAN frame header fields and data
 * @header: CAN frame header
 * @data: 8 bytes of data
 *
 * User space transmits and receives struct can_frame's via the network device
 * socket interface.
 *
 * Note: The memory layout does not correspond to the on-the-wire format for a
 * CAN frame.
 */
#pragma pack(push, 1)
typedef struct
{
    T_CAN_Header    tHeader;              //CAN数据帧的帧头
    T_CAN_DataFrame tDataFrame;           //CAN数据帧的数据段
}T_Can_Frame; // CAN数据帧结构定义
#pragma pack(pop)

typedef struct {
    BYTE    aucDataBuff[COMM_CAN_MAX_BUFF_LEN];// 协议层数据接收缓冲区
    WORD    wDataLength;              // 缓冲区数据长度(不包括帧头和帧尾)
}T_PACKETBUFF;

typedef struct
{
     BYTE       SrcTyp;                 ///< 源设备类型

} T_AppSuMo;          

// 结构体定义
typedef struct
{
	BYTE	ucFuncCode;			// 分段数据的功能码
	BYTE	ucExpectFragNum;	// 期望收到的分段计数器
    BYTE	aucDataBuf[CAN_BACKUP_DATA_BUFF];//数据缓冲区
}T_CAN_DataCache; // CAN数据缓存区定义

typedef struct
{
	BYTE	ucSendedFlag;			// 已发送标志
	BYTE	ucMasterReceivedNum;	// 收到主机的连接器温度次数
	BYTE	ucSlaveReceivedNum[SLAVE_MAX_NUM];//收到从机的连接器温度次数
}T_ConnTempHighPrt; // 连接器高温保护结构体定义

typedef struct
{
    BYTE ucData[CAN_BACKUP_DATA_BUFF]; //接收数据
    SHORT wDataFlag;                   //接收标志
    SHORT wDataLen;                    //接收长度
}T_CanBackupStruct;                    //CAN接收互备份缓存结构体

typedef struct
{
    BYTE cmdID;
    BYTE func;
    SHORT (*handle)(T_Can_Frame* ptCan_Frame);
}T_CAN_BMSFrameHandle;

typedef struct
{
    BOOLEAN bPolling;                    ///< 是否正在轮询进行从机统计和设置升级标志
    BOOLEAN bPollTimeOut;                ///< 轮询结束时间到
    UINT32 pollTimeCount;                ///< 随CAN1周期计算节拍, 轮询超时计数
    UINT32 intervalCount;                ///< 随CAN1周期计算节拍, 发送间隔计数
    UINT32 updateTimeCount;              ///< 随CAN1周期计算节拍, 发送间隔计数
    SHORT slavePollDevCount;             ///< 单台从机轮询设备统计次数
    SHORT slavePollFlagCount;            ///< 单台从机轮询设置标志次数
    SHORT currentPollIndex;              ///< 当前轮询从机索引：0 ~ (SLAVE_NUM - 1)
    BYTE battInPlaceFlag[SLAVE_NUM];     ///< 电池在位状态标志
    BYTE stateSetUpdateFlag[SLAVE_NUM];  ///< 从机收到升级标志指令并回复了的状态
}T_CAN_BMSUpdateScanState;

typedef struct
{
    BYTE numInPlace;                     ///< 电池在位个数
    BYTE battInPlaceAddr[SLAVE_NUM];     ///< 电池在位地址
}T_CAN_BMSUpdateScanResult;

typedef struct FmInfo
{
    BYTE ucChgAndDischgStatus;
    BYTE ucFmStatus;
} T_FmInfo;

typedef struct FmInfoNotice
{
    T_FmInfo tCurrentFmInfo;
    T_FmInfo tPreviousFmInfo;
    BYTE ucTimer;
    BYTE ucTimerMax;
    BYTE ucSendCount;
    BYTE ucSendCountMax;
    BYTE ucFrameId;
} T_FmInfoNotice;

/*********************  函数原型定义  **********************/
void InitCan1(void);
void Process_CAN_Comm(void* parameter);  
void processBackupData( void* parameter );
BOOLEAN InitSuData( void );
INT32S CanRecCon(void);
BOOLEAN InitCanProto( void );
BOOLEAN IsMaster( void );
void setBMSMode(BYTE ucMode, SHORT iVol, WORD wRef);
void ChangeIntoCanFrm(BYTE *dataComm, WORD wInLen, BYTE ucInScrDev, BYTE ucInSrcAdr);
BYTE GetBMSAddr(void);
BYTE GetBMSDLAddr(void);
void SetBattAddr(BYTE ucBattAddr);
BYTE getGrpAddr(void);
void GetSmrRealData(BYTE ucDevType);
void restartAddrCompete(void);
void setChargeCurr(WORD wCurr, WORD wMaxVol, SHORT sMaxCurr);
void ctrlBMSConsrv(BYTE ucAddr, BOOLEAN bConsrv);
void SetRotateCtrlSlave(BYTE ucCurr, BOOLEAN bChargeDisable, BYTE ucAddr);
void SetEqualizeCurr(FLOAT fCurr, WORD wBattCap, BYTE bMasterFull);
void SynTimeAndMode(void);
BOOLEAN broadSendFireAlm(BOOLEAN flag);
BYTE GetTotalAddr(void);
BYTE StatisticUpdateSlaveNum(BYTE *SaveSlaveAddr);
#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
    void SetExternalPowerOnExistFlag(BOOLEAN ExternalPoweronFlag, BOOLEAN bB3CurrBalance);
#else
    void SetExternalPowerOnExistFlag(BOOLEAN ExternalPoweronFlag);
#endif

void setBackUpHisData(T_HisDataStruct *ptHisData);
void setBackUpHisAlm(T_HisAlarmSaveStruct *ptDisHisAlarm);
void setBackUpHisAct(T_ActionRecord *ptHisAct);
void setBduRecordBackUp(BYTE *pucData, UINT32 uSize);
BOOLEAN setBackupHisExtreme(BYTE *pucData, WORD wSize, BYTE ucPackNum);
void setBackupAnalyseInfo(BYTE *pucData, UINT32 uSize);
BOOLEAN DealBMSBackUpData(BYTE* pInBuff);
BOOLEAN BuildBackupFileName(BYTE *pucSN_fileName, BYTE *suffix, BYTE *pucFileName);
BOOLEAN HandleBackupData(BYTE *pInBuff, WORD wLen, BYTE *suffix, BYTE *pucFileName);
BOOLEAN Get_CanBuff_Backup(void *pCanRecvBuff, BYTE num);
void Set_CanBuff_Backup(void *pCanSendBuff, SHORT wlen);
void DealBackupProcess();
BOOLEAN boardCaseSwitch(BYTE byte);
void getSlaveData(T_SlavesRealData * ptSlavesRealData);
void GetBaseTime(struct tm *tBaseTime);

void setVol(BYTE ucMode, SHORT iVol);
void SetPowerdownVoltSlave(SHORT iVol); //设置掉电电压
BYTE GetBattNum(void);       // 获取电池数量
BOOLEAN JudgeCanCommDisconnect(void);
INT32S Can2RecCon(void);
void SetMasterExist(BOOLEAN bFlag);
BOOLEAN IsMasterExist(void);

void SendCan1InterconnectTestFrm(void);
UINT32 GetCan1InterconnetTestCounter(void);  // 获取CAN1对接测试计数
void ClearCan1InterconnetTestCounter(void);
void SendForceMasterFrm(void);
ULONG getSNPart(void);
ULONG getMaskedSNPart(void);

BOOLEAN SendAlarmToMasterByCan1(BYTE usAlarmId, BOOLEAN ucAlarmOccurFlag);

BOOLEAN SetBattAddrNet(BYTE ucAddr);
void CheckIsCanInterconnectTestCAN1Frm(T_Can_Frame * ptCan_Frame);

BOOLEAN SnmpSetParaToOtherBatt(BYTE ucCmd, BYTE ucOffset, BYTE *p, BYTE ucLen);

BOOLEAN SetBattAddrNet(BYTE ucAddr);
BOOLEAN SendSnmpCtrl(BYTE DstAdr, BYTE ucCmd, INT32 iData);
BOOLEAN MasterBroadCastToSlave(BYTE *aucRecBuf, BYTE len);

#ifdef UNITEST
Static BOOLEAN MasterCompete(T_Can_Frame * ptCan_Frame);
Static BOOLEAN SwitchToSlave(void);
BOOLEAN DealCan2Connected(int Cnt);
#endif
BYTE GetMasterAddr(void);

// 远程升级 BMS主机检测从机相关接口
SHORT ResetBMSUpdateScanState(void);
SHORT StartBMSUpdateScan(void);
T_CAN_BMSUpdateScanResult GetBMSUpdateScanState(void);

BOOLEAN GetParallUpdateFlag(void);
BOOLEAN SetParallUpdateFlag(BOOLEAN Flag);
BOOLEAN GetBduParallUpdateFlag(void);
BOOLEAN SetBduParallUpdateFlag(BOOLEAN Flag);
BOOLEAN IsParallUpdate(void);
void setFmCtrl(BOOLEAN bChgDisable, BOOLEAN bChgDischg, FLOAT fAvePower);

#ifdef INTELLIGENT_PEAK_SHIFTING
T_FmInfoNotice *GetFmInfoNotice(void);
void InitFmInfoNotice(T_FmInfoNotice *ptNotice);
BOOLEAN DealFmInfoNotice(T_FmInfoNotice *ptNotice);
BOOLEAN RefreshFmInfo(T_FmInfoNotice *ptNotice);
BOOLEAN IsFmInfoChanged(T_FmInfoNotice *ptNotice);
BOOLEAN SendFmInfo(T_FmInfoNotice *ptNotice);
BOOLEAN ParseFmInfoResponse(T_FmInfoNotice *ptNotice, T_Can_Frame *ptCanFrame);
#endif /* INTELLIGENT_PEAK_SHIFTING */

BOOLEAN IsBindingInfoChange(void);
void ClearBindingBmsInfo(void);

WORD GetCANCommLostCnt(void);
void ClearCANCommLostCnt(void);
void IncCANCommLostCnt(void);
void SetCANCommLostCnt(WORD value);
void TemplateForCanFrm(BYTE *dataComm, WORD wInLen, BYTE ucInScrDev, BYTE ucInSrcAdr);

#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // SOFTWARE_SRC_APP_COMMCAN_H_;
