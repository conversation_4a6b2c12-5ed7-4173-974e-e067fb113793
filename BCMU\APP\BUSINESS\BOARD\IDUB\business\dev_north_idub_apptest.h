#ifndef _DEV_NORTH_IDUB_APPTEST_H
#define _DEV_NORTH_IDUB_APPTEST_H

#ifdef __cplusplus
extern "C"
{
#endif /*__cplusplus */

#include "device_type.h"

#define IDUB_SOFTWARE_NAME             "IDUB"

// CID2
#define CID2_TRIG_APPTEST           0x00   // 触发apptest命令
#define CID2_EXIT_APPTEST           0x01   // 退出apptest命令
#define CID2_GET_PROTO_VER          0x4F   // 获取通讯协议版本号
#define CID2_GET_FACT_INFO          0x11   // 获取厂家信息
#define CID2_CHIP_TEST_INFO         0x12   // 获取芯片测试信息
#define CID2_SET_HARDWARE_VER       0x13   // 设置硬件版本号
#define CID2_GET_HARDWARE_VER       0x14   // 获取硬件版本号
#define CID2_SET_BOARD_MANU_DATE    0x15   // 设置单板生产日期
#define CID2_GET_BOARD_MANU_DATE    0x16   // 获取单板生产日期
#define CID2_GET_AI_SIGNAL          0x17   // 读取AI信号
#define CID2_GET_DEVICE_ADDR        0x50   // 获取设备地址
#define CID2_SET_SN                 0x18   // 设置序列号
#define CID2_GET_SN                 0x19   // 获取序列号



#define IDUB_APPTEST_TRIG             1   // 触发apptest命令
#define IDUB_APPTEST_EXIT             2   // 退出apptest命令
#define IDUB_GET_PROTO_VER            3   // 获取通讯协议版本号
#define IDUB_GET_FACT_INFO            4   // 获取厂家信息
#define IDUB_CHIP_TEST_INFO           5   // 获取芯片测试信息
#define IDUB_SET_HARDWARE_VER         6   // 设置硬件版本号
#define IDUB_GET_HARDWARE_VER         7   // 获取硬件版本号
#define IDUB_SET_BOARD_MANU_DATE      8   // 设置单板生产日期
#define IDUB_GET_BOARD_MANU_DATE      9   // 获取单板生产日期
#define IDUB_GET_AI_SIGNAL            10  // 读取AI信号
#define IDUB_GET_DEVICE_ADDR          11  // 获取设备地址

#define IDUB_SET_SN                   12  // 设置序列号
#define IDUB_GET_SN                   13  // 获取序列号



#define SOFTWARE_NAME_LEN              32   // 软件名称长度
#define SOFTWARE_VER_LEN               32   // 软件版本号长度
#define SOFTWARE_DATE_LEN              10   // 软件版本日期长度
#define SOFTWARE_INFO_LEN              (SOFTWARE_NAME_LEN + SOFTWARE_VER_LEN + SOFTWARE_DATE_LEN) // 软件信息长度



dev_type_t* init_idub_dev_apptest(void);
void apptest_register_cmd_table();


int apptest_comm_parse(void* dev_inst, void* cmd_buff);
int get_fact_info_pack(void* dev_inst, void* cmd_buff);
int set_hardware_version_parse(void* dev_inst, void* cmd_buff);
int get_hardware_version_pack(void* dev_inst, void* cmd_buff);
int apptest_exit_parse(void* dev_inst, void* cmd_buff);
int apptest_trig_pack(void* dev_inst, void* cmd_buff);
int get_chip_test_info_pack(void* dev_inst, void* cmd_buff);
int set_board_manu_date_parse(void* dev_inst, void* cmd_buff);
int get_board_manu_date_pack(void* dev_inst, void* cmd_buff);
int get_addr_pack(void* dev_inst, void* cmd_buff);

int set_sn_parse(void* dev_inst, void* cmd_buff);
int get_sn_pack(void* dev_inst, void* cmd_buff);
#ifdef __cplusplus
}
#endif //  __cplusplus

#endif //_DEV_NORTH_IDUB_APPTEST_H
