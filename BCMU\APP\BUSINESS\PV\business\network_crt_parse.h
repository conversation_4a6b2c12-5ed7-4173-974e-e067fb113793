/**
 * @file     TIME_MANAGE.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2023-08-28
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */


#ifndef _NETWORK_CRT_PARSE_H
#define _NETWORK_CRT_PARSE_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include <stdio.h>
#include <rtthread.h>
#include <rtdevice.h>
#include <string.h>
#include "data_type.h"
#include "utils_server.h"
#include "storage.h"
typedef struct{
    unsigned int cert_time_year;
    unsigned int cert_time_month;
    unsigned int cert_time_day;
    unsigned int cert_time_hour;
    unsigned int cert_time_minute;
    unsigned int cert_time_second;
    unsigned int cert_time;
}cert_time_sid_t;

int crt_date_parse(void);
int parse_crt(cert_time_sid_t* start_sid, cert_time_sid_t* end_sid, char* file_name);


#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _TIME_MANAGE_H