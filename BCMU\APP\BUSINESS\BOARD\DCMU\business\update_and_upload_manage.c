/*******************************************************************************
  * @file        update_manage.c
  * @version     v1.0.0
  * @copyright   COPYRIGHT &copy; 2023 CSG
  * <AUTHOR>
  * @date        2023-4-6
  * @brief
  * @attention
  * Modification History
  * DATE         DESCRIPTION
  * ------------------------
  * - 2023-4-6  dongmengling Created
*******************************************************************************/
#include <string.h>
#include <rtthread.h>
#include <rtdevice.h>
#include <stdlib.h>
#include <stddef.h>
#include "storage.h"
#include "sps.h"
#include "msg.h"
#include "cmd.h"
#include "dev_dcmu.h"
#include "para_id_in.h"
#include "device_type.h"
#include "para_manage.h"
#include "update_and_upload_manage.h"
#include "protocol_layer.h"
#include "partition_def.h"
#include "utils_data_transmission.h"
#include "utils_rtthread_security_func.h"
#include "log_mgr_api.h"
#include "utils_time.h"
#include "his_record.h"
#include "gd32f4xx.h"

Static int parse_update_trig_bottom(void* dev_inst, void *cmd_buf);
Static int pack_update_trig_bottom(void* dev_inst, void *cmd_buf);
Static int get_app_download_rtn(void *cmd_buf);
Static int parse_upload_file_list(void* dev_inst, void *cmd_buf);
Static int parse_upload_file_data(void* dev_inst, void *cmd_buf);
Static int parse_filedata_first_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no);
Static int pack_upload_file_list(void* dev_inst, void *cmd_buf);
Static int pack_filelist_first_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no);
Static int pack_filelist_other_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no);
Static int pack_upload_file_data(void* dev_inst, void *cmd_buf);
Static int pack_filedata_first_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no);
Static int pack_filedata_next_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no);
Static int parse_update_trig(void* dev_inst, void *cmd_buf);
Static int pack_update_trig(void* dev_inst, void *cmd_buf);
Static void upload_trigTimer_timeout();
Static void upload_trig_end();
Static int pack_zk_update_trig_bottom(void* dev_inst, void *cmd_buf);
Static int parse_update_data(void* dev_inst, void *cmd_buf);
Static int pack_update_data(void* dev_inst, void *cmd_buf);
Static int is_expect_date_frame_no(unsigned short frm_no, bottom_update_file_manage_t* trsdata_ctr_inf, cmd_buf_t* tran_cmd_buf);
Static int erase_flash_in_first_frame(bottom_update_file_manage_t* trsdata_ctr_inf, cmd_buf_t* tran_cmd_buf);
Static int parse_data_frame(void* dev_inst, cmd_buf_t *cmd_buf, cmd_buf_t* tran_cmd_buf, unsigned short frm_no);
Static void time_out_ctrl_update(void);
Static void deal_last_frame(unsigned short frm_no, bottom_update_file_manage_t* trsdata_ctr_inf, cmd_buf_t *cmd_buf, void* dev_inst);
Static int get_zk_download_rtn(void *cmd_buf);
Static int bottom_update_file_name_cmp(char* file_name);
Static int parse_zk_update_trig(void* dev_inst, void *cmd_buf);
Static int is_first_frame_deal(unsigned short frm_no, cmd_buf_t* tran_cmd_buf);
Static int switch_com_baudrate(void* dev_inst, unsigned int baudrate);
Static int self_update_deal(bottom_update_file_manage_t* trsdata_ctr_inf, cmd_buf_t *cmd_buf, void* dev_inst);
Static void is_need_restart(void* dev_inst, cmd_buf_t *cmd_buf);
Static void down_trig_timeout(void);
Static int parse_first_frame(cmd_buf_t* tran_cmd_buf,unsigned short frm_no);

Static trig_ctr_inf_t s_trig_ctr_inf;
Static bottom_update_file_manage_t s_tFileManage;
T_BottomFileUploadStruct s_upload_trsdata_inf = {0};
Static bottom_download_trig_ctr_inf_t s_upload_trig_inf = {0};
Static rt_timer_t upload_trigTimer = NULL;
Static bottom_update_file_manage_t s_download_trsdata_ctr_inf;
Static rt_timer_t  g_update_timer = NULL;
Static rt_timer_t  s_update_trig_timer = NULL;
Static unsigned char g_first_frame_flag = FALSE;
Static bottom_transfer_file_name_info_t* s_register_download_file_name[BOTTOM_MAX_DOWNLOAD_FILE_NUM] = {0};
Static bottom_cur_download_file_info_t s_cur_download_file_info = {0};
Static char file_name_tmp[UPDATE_FILE_EXTEND_NAME_LEN] = {0};      ///< 文件名称
Static void* s_dev_inst_change_baudrate = NULL;
Static unsigned char g_bottom_zk_update_flag = FALSE;
Static unsigned int g_bottom_download_total_size = 0;

Static unsigned char upfile_sta = NOTUPLOAD;

static bottom_comm_cmd_head_t cmd_req[] RAM_SECTION = {
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_TRIG_OLD,      CMD_APPEND_UPDATE_REQ,  BOTTOM_RTN_APP_NOACK},   // 升级触发帧
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPLOAD_TRIG_FRAME,    CMD_APPEND_UPDATE_REQ,  BOTTOM_RTN_APP_NOACK},   // 上传文件触发帧
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPLOAD_LIST_FRAME,    CMD_APPEND_NONE,        BOTTOM_RTN_APP_NOACK},   // 上传文件列表传输帧
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPLOAD_DATA_FRAME,    CMD_APPEND_NONE,        BOTTOM_RTN_APP_NOACK},   // 上传文件数据传输帧
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_FONT_LIB_TRIG, CMD_APPEND_UPDATE_REQ,  BOTTOM_RTN_APP_NOACK},   // 字库升级触发帧
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_FONT_LIB_DATA, CMD_APPEND_NONE,        BOTTOM_RTN_APP_NOACK},   // 字库升级数据传输帧
};

static bottom_comm_cmd_head_t cmd_ack[] RAM_SECTION = {
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_TRIG_OLD,      CMD_APPEND_UPDATE_ACK,  BOTTOM_RTN_APP_NOACK},   // 升级触发帧
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPLOAD_TRIG_FRAME,    CMD_APPEND_UPDATE_ACK,  BOTTOM_RTN_APP_NOACK},   // 上传文件触发帧
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPLOAD_LIST_FRAME,    CMD_APPEND_NONE,        BOTTOM_RTN_APP_NOACK},   // 上传文件列表传输帧
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPLOAD_DATA_FRAME,    CMD_APPEND_NONE,        BOTTOM_RTN_APP_NOACK},   // 上传文件数据传输帧
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_FONT_LIB_TRIG, CMD_APPEND_UPDATE_ACK,  BOTTOM_RTN_APP_NOACK},   // 字库升级触发帧
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_FONT_LIB_DATA, CMD_APPEND_NONE,        BOTTOM_RTN_APP_NOACK},   // 字库升级数据传输帧
};

static cmd_t no_poll_cmd_tab[] = {
    {BOTTOM_UPDATE_FILE_TRIG, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(bottom_comm_cmd_head_t), NULL, NULL, parse_update_trig_bottom,pack_update_trig_bottom,}, // 升级触发命令
    {BOTTOM_UPLOAD_FILE_TRIG, CMD_PASSIVE, &cmd_req[1], &cmd_ack[1], sizeof(bottom_comm_cmd_head_t), }, // 文件上传触发命令
    {BOTTOM_UPLOAD_FILE_LIST, CMD_PASSIVE, &cmd_req[2], &cmd_ack[2], sizeof(bottom_comm_cmd_head_t), }, // 文件列表上传命令
    {BOTTOM_UPLOAD_FILE_DATA, CMD_PASSIVE, &cmd_req[3], &cmd_ack[3], sizeof(bottom_comm_cmd_head_t), }, // 文件数据上传命令
    {BOTTOM_ZK_UPDATE_TRIG,   CMD_PASSIVE, &cmd_req[4], &cmd_ack[4], sizeof(bottom_comm_cmd_head_t), }, // 字库升级触发命令
    {BOTTOM_UPDATE_DATA_FRM,  CMD_PASSIVE, &cmd_req[5], &cmd_ack[5], sizeof(bottom_comm_cmd_head_t), }, // 字库升级数据传输命令
    {0,}, // 结束标记
};

static cmd_handle_register_t s_upload_cmd_handle[] = {
    {DEV_BOTTOM_COMM_UPDATE_UPLOAD, BOTTOM_UPLOAD_FILE_TRIG,    CMD_TYPE_NO_POLL, parse_update_trig,      pack_update_trig},      // 文件上传触发处理
    {DEV_BOTTOM_COMM_UPDATE_UPLOAD, BOTTOM_UPLOAD_FILE_LIST,    CMD_TYPE_NO_POLL, parse_upload_file_list, pack_upload_file_list}, // 文件列表上传处理
    {DEV_BOTTOM_COMM_UPDATE_UPLOAD, BOTTOM_UPLOAD_FILE_DATA,    CMD_TYPE_NO_POLL, parse_upload_file_data, pack_upload_file_data}, // 文件数据上传处理
    {DEV_BOTTOM_COMM_UPDATE_UPLOAD, BOTTOM_ZK_UPDATE_TRIG,      CMD_TYPE_NO_POLL, parse_zk_update_trig,   pack_zk_update_trig_bottom}, // 字库升级处理
    {DEV_BOTTOM_COMM_UPDATE_UPLOAD, BOTTOM_UPDATE_DATA_FRM,     CMD_TYPE_NO_POLL, parse_update_data,      pack_update_data},     // 字库升级数据传输命令
    {DEV_NORTH_DCMU_CAN0,           BOTTOM_UPLOAD_FILE_TRIG,    CMD_TYPE_NO_POLL, parse_update_trig,      pack_update_trig},      // 文件上传触发处理-CAN0
    {DEV_NORTH_DCMU_CAN0,           BOTTOM_UPLOAD_FILE_LIST,    CMD_TYPE_NO_POLL, parse_upload_file_list, pack_upload_file_list}, // 文件列表上传处理-CAN0
    {DEV_NORTH_DCMU_CAN0,           BOTTOM_UPLOAD_FILE_DATA,    CMD_TYPE_NO_POLL, parse_upload_file_data, pack_upload_file_data}, // 文件数据上传处理-CAN0
};

Static dev_type_t dev_dcmu_bottom_comm = {
    DEV_BOTTOM_COMM_UPDATE_UPLOAD, 1, PROTOCOL_BOTTOM_COMM, LINK_DCMU_NORTH, R_BUFF_LEN, S_BUFF_LEN, 0, &no_poll_cmd_tab[0],
};

dev_type_t* init_dcmu_bottom_comm(void)
{
    return &dev_dcmu_bottom_comm;
}

Static dev_type_t dev_dcmu_bottom_comm_can0 = {
    DEV_NORTH_DCMU_CAN0, 1, PROTOCOL_BOTTOM_COMM, LINK_DCMU_CAN, R_BUFF_LEN, S_BUFF_LEN, 0, &no_poll_cmd_tab[0],
};

dev_type_t* init_dcmu_bottom_comm_can0(void)
{
    return &dev_dcmu_bottom_comm_can0;
}

Static int get_app_download_rtn(void *cmd_buf) {
    bottom_comm_cmd_head_t* proto_head = NULL;
    cmd_buf_t* trig_cmd_buf = (cmd_buf_t*)cmd_buf;

    // 入参校验
    if (trig_cmd_buf == NULL || trig_cmd_buf->buf == NULL) {
        return FAILURE;
    }

    proto_head = (bottom_comm_cmd_head_t*)trig_cmd_buf->cmd->req_head;
    char *p_file_name = s_trig_ctr_inf.file_name;
    unsigned char *p = trig_cmd_buf->buf;

    if (proto_head->append != 0xAA55)
    {
        return DownloadFrameErr;
    }

    if (trig_cmd_buf->data_len == UPDATE_FILE_NAME_LEN)
    {
        rt_snprintf_s(p_file_name, UPDATE_FILE_NAME_LEN, DCMU_UPDATE_FILE_NAME);
        if (strncmp((char *)p, DCMU_UPDATE_FILE_NAME, UPDATE_FILE_NAME_LEN) == 0) {
            return BOTTOM_RTN_APP_CORRECT;
        }
    }
    return DownFileNameErr;
}

Static int parse_update_trig_bottom(void* dev_inst, void *cmd_buf) {

    s_trig_ctr_inf.rtn = get_app_download_rtn(cmd_buf);
    return SUCCESSFUL;
}

Static int get_zk_download_rtn(void *cmd_buf) {
    bottom_comm_cmd_head_t* proto_head = NULL;
    cmd_buf_t* trig_cmd_buf = (cmd_buf_t*)cmd_buf;

    // 入参校验
    if (trig_cmd_buf == NULL || trig_cmd_buf->buf == NULL) {
        return FAILURE;
    }

    proto_head = (bottom_comm_cmd_head_t*)trig_cmd_buf->cmd->req_head;
    char *p_file_name = s_trig_ctr_inf.file_name;
    unsigned char *p = trig_cmd_buf->buf;

    if (proto_head->append != 0xAA55)
    {
        return DownloadFrameErr;
    }

    if (trig_cmd_buf->data_len == ZK_UPDATE_FILE_NAME_LEN)
    {
        rt_snprintf_s(p_file_name, ZK_UPDATE_FILE_NAME_LEN, DCMU_ZK_UPDATE_FILE_NAME);
        if (rt_strncmp((char *)p, DCMU_ZK_UPDATE_FILE_NAME, ZK_UPDATE_FILE_NAME_LEN) == 0) {
            return BOTTOM_RTN_APP_CORRECT;
        }
    }
    return DownFileNameErr;
}

Static int pack_zk_update_trig_bottom(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* trig_cmd_buf = (cmd_buf_t*)cmd_buf;
    bottom_comm_cmd_head_t* proto_head = NULL;

    if (trig_cmd_buf == NULL || trig_cmd_buf->buf == NULL ||
        trig_cmd_buf->cmd == NULL || trig_cmd_buf->cmd->req_head == NULL||
        dev_inst == NULL || ((dev_inst_t*)dev_inst)->dev_type == NULL) {
        return FAILURE;
    }

    if(s_trig_ctr_inf.rtn == DownloadFrameErr) {
        //附加码错误
        trig_cmd_buf->data_len = 0;
        trig_cmd_buf->rtn = BOTTOM_RTN_CMD_ERROR;
        return SUCCESSFUL;
    }else{
        proto_head = (bottom_comm_cmd_head_t*)trig_cmd_buf->cmd->req_head;
        proto_head->append = 0x55AA;
        //回复正确文件名
        trig_cmd_buf->data_len = ZK_UPDATE_FILE_NAME_LEN;
        rt_memset_s(trig_cmd_buf->buf, trig_cmd_buf->data_len, 0x00, trig_cmd_buf->data_len);
        rt_memcpy_s(trig_cmd_buf->buf, ZK_UPDATE_FILE_NAME_LEN,  DCMU_ZK_UPDATE_FILE_NAME, sizeof(DCMU_ZK_UPDATE_FILE_NAME)-1);
        // 回包后切换波特率
        if (s_upload_trig_inf.trig_success == TRUE) {
            // 先按9600波特率回包
            if (protocol_layer_send(dev_inst, trig_cmd_buf) != SUCCESSFUL) {
                trig_cmd_buf->data_len = 0;
                trig_cmd_buf->rtn = BOTTOM_RTN_CMD_ERROR;
            }
            if (linklayer_send(((dev_inst_t*)dev_inst)->dev_type->link_inst) != SUCCESSFUL) {
                trig_cmd_buf->data_len = 0;
                trig_cmd_buf->rtn = BOTTOM_RTN_CMD_ERROR;
            }
            // 再切换到19200波特率
            switch_com_baudrate(dev_inst, BAUD_RATE_19200);
        }
    }

    return SUCCESSFUL;
}

/* 解析下载帧 */
Static int parse_update_data(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    unsigned short frm_no = 0;
    bottom_comm_cmd_head_t* proto_head = NULL;
    bottom_update_file_manage_t* trsdata_ctr_inf = NULL;
    trsdata_ctr_inf = &s_download_trsdata_ctr_inf;
    trsdata_ctr_inf->rtn = 0;

    rt_timer_stop(g_update_timer);

    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL) {
        return FAILURE;
    }

    //判断协议层解析数据是否正确
    if(tran_cmd_buf->rtn != DOWNLOAD_FRAME_CORRECT){
        return SUCCESSFUL;
    }
    proto_head = (bottom_comm_cmd_head_t*)tran_cmd_buf->cmd->req_head;
    frm_no = proto_head->append;

    rt_kprintf("frame_no:%d\n", frm_no);

    // 首发帧处理
    RETURN_VAL_IF_FAIL(is_first_frame_deal(frm_no, tran_cmd_buf) == SUCCESSFUL, SUCCESSFUL);
    // 是否期望帧
    RETURN_VAL_IF_FAIL(is_expect_date_frame_no(frm_no, trsdata_ctr_inf, tran_cmd_buf) == SUCCESSFUL, SUCCESSFUL);
    // 第一帧处理(擦除flash)
    RETURN_VAL_IF_FAIL(erase_flash_in_first_frame(trsdata_ctr_inf, tran_cmd_buf) == SUCCESSFUL, SUCCESSFUL);
    g_first_frame_flag = FALSE;
    parse_data_frame(dev_inst, cmd_buf, tran_cmd_buf, frm_no);
    tran_cmd_buf->rtn = trsdata_ctr_inf->rtn;
    return SUCCESSFUL;
}

Static void down_trig_timeout(void)
{
    s_upload_trig_inf.trig_times = 0;
    s_upload_trig_inf.trig_success = FALSE;
    rt_timer_stop(s_update_trig_timer);
}

Static int pack_update_data(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    bottom_comm_cmd_head_t* proto_head = NULL;

    if (tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL ||
        (bottom_comm_cmd_head_t*)tran_cmd_buf->cmd == NULL ||
        (bottom_comm_cmd_head_t*)tran_cmd_buf->cmd->ack_head == NULL) {
        return FAILURE;
    }

    tran_cmd_buf->data_len = 0;
    proto_head = (bottom_comm_cmd_head_t*)tran_cmd_buf->cmd->ack_head;
    proto_head->append = s_download_trsdata_ctr_inf.cur_frame_no;
    return SUCCESSFUL;
}

Static int is_expect_date_frame_no(unsigned short frm_no, bottom_update_file_manage_t* trsdata_ctr_inf, cmd_buf_t* tran_cmd_buf)
{
    if (tran_cmd_buf == NULL)
    {
        return FAILURE;
    }

    if (trsdata_ctr_inf == NULL || frm_no != trsdata_ctr_inf->cur_frame_no || trsdata_ctr_inf->update_status != BOTTOM_UPDATEING)
    {
        tran_cmd_buf->rtn = DOWNLOAD_FRAME_ERR;
        return FAILURE;
    }
    return SUCCESSFUL;
}

Static int erase_flash_in_first_frame(bottom_update_file_manage_t* trsdata_ctr_inf, cmd_buf_t* tran_cmd_buf)
{
    part_data_t part_data = {0};
    int ret = 0;

    if (trsdata_ctr_inf == NULL || tran_cmd_buf == NULL)
    {
        return FAILURE;
    }

    if(trsdata_ctr_inf->cur_frame_no == 1)
    {
        rt_snprintf_s(part_data.name, sizeof(part_data.name), s_cur_download_file_info.cur_store_file_name);
        part_data.len = g_bottom_download_total_size;
        ret = storage_erase_with_step(&part_data, 1, s_cur_download_file_info.feed_dog);
        if (SUCCESSFUL != ret)
        {
            tran_cmd_buf->rtn  = DOWNLOAD_FRAME_ERR;
            return FAILURE;
        }
    }
    return SUCCESSFUL;
}

int bottom_register_download_file_name(bottom_transfer_file_name_info_t* download_file_name, int download_file_num, bottom_update_info_save_t* update_info_save)
{
    if(download_file_num > BOTTOM_MAX_DOWNLOAD_FILE_NUM)
    {
        LOG_E("download_file_num is %d over %d\n", download_file_num, BOTTOM_MAX_DOWNLOAD_FILE_NUM);
        return FAILURE;
    }

    if (download_file_name == NULL)
    {
        return FAILURE;
    }

    // 将结构体数组转换为指针数组
    for (int i = 0; i < download_file_num; i++) {
        s_register_download_file_name[i] = &download_file_name[i];
    }
    s_cur_download_file_info.cur_store_info_name = update_info_save->update_info_part_name;
    s_cur_download_file_info.cur_store_info_offset =  update_info_save->offset;
    s_cur_download_file_info.feed_dog = update_info_save->feed_dog;
    return SUCCESSFUL;
}

Static int parse_data_frame(void* dev_inst, cmd_buf_t *cmd_buf, cmd_buf_t* tran_cmd_buf, unsigned short frm_no){
    part_data_t part_data = {0};
    bottom_update_file_manage_t* trsdata_ctr_inf = NULL;
    trsdata_ctr_inf = &s_download_trsdata_ctr_inf;
    unsigned short crc = trsdata_ctr_inf->file_info.filecrc;
    //存储数据帧
    part_data.buff = tran_cmd_buf->buf;
    part_data.len = tran_cmd_buf->data_len;
    part_data.offset = trsdata_ctr_inf->data_offset;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), "%s", s_cur_download_file_info.cur_store_file_name);
    if (SUCCESSFUL != storage_process(&part_data, write_opr))
    {
        trsdata_ctr_inf->rtn = DOWNLOAD_FRAME_ERR;
        return SUCCESSFUL;
    }

    //计算校验和,判断升级文件是否有问题
    crc = crc16_calc_with_init_crc(tran_cmd_buf->buf, tran_cmd_buf->data_len, crc);

    //更新数据
    trsdata_ctr_inf->cur_frame_no = frm_no + 1;
    trsdata_ctr_inf->data_offset += tran_cmd_buf->data_len;
    trsdata_ctr_inf->rtn = DOWNLOAD_FRAME_CORRECT;
    trsdata_ctr_inf->file_info.filecrc = crc;
    if(frm_no % BOTTOM_SAVE_UPDATE_DATA_INDEX == 0)
    {
        write_download_tmpInfo(trsdata_ctr_inf);
    }

    rt_timer_start(g_update_timer);
    //判断是否是最后一帧
    RETURN_VAL_IF_FAIL(frm_no == trsdata_ctr_inf->file_info.total_frames - 1, SUCCESSFUL);
    rt_kprintf("last frm %d == %d - 1\n",frm_no, trsdata_ctr_inf->file_info.total_frames);

    LOG_E("update crc:%d\n",trsdata_ctr_inf->file_info.filecrc);
    deal_last_frame(frm_no, trsdata_ctr_inf, cmd_buf, dev_inst);
    return SUCCESSFUL;
}

Static void time_out_ctrl_update(void)
{
    s_upload_trig_inf.trig_times = 0;
    s_upload_trig_inf.trig_success = FALSE;
    rt_timer_stop(g_update_timer);
    switch_com_baudrate(s_dev_inst_change_baudrate, BAUD_RATE_9600);
    s_dev_inst_change_baudrate = NULL;
    return;
}

void deal_last_frame(unsigned short frm_no, bottom_update_file_manage_t* trsdata_ctr_inf, cmd_buf_t *cmd_buf, void* dev_inst)
{
    trsdata_ctr_inf->update_status = BOTTOM_TRANSFER_SUCCESS;
    rt_timer_stop(g_update_timer);
    if(s_cur_download_file_info.cur_file_type == BOTTOM_MONITOR_FILE_TYPE)
    {
        self_update_deal(trsdata_ctr_inf, cmd_buf, dev_inst);
    }
    return;
}

Static int self_update_deal(bottom_update_file_manage_t* trsdata_ctr_inf, cmd_buf_t *cmd_buf, void* dev_inst)
{
    trsdata_ctr_inf->sys_run_flag = FALSE;
    trsdata_ctr_inf->backup_flag = FALSE;
    trsdata_ctr_inf->count = 0;

    if(g_bottom_zk_update_flag) {
        trsdata_ctr_inf->update_flag = FLAG_APP_UPDATE_ZK;
        trsdata_ctr_inf->ucFlag = FLAG_DOWNLOAD_ZK_IAP;
        g_bottom_zk_update_flag = FALSE;
    }else{
        trsdata_ctr_inf->update_flag = FLAG_APP_UPDATE_APP;
        trsdata_ctr_inf->ucFlag = FLAG_DOWNLOAD_IAP;
    }
    write_download_tmpInfo(trsdata_ctr_inf);// 完成标记,文件传输完成

    is_need_restart(dev_inst, cmd_buf);   // 保证最后一包传输完成
    return SUCCESSFUL;
}

Static void is_need_restart(void* dev_inst, cmd_buf_t *cmd_buf)
{
    bottom_update_file_manage_t tFileManage = {0};
    cmd_buf_t send_cmd_buff = {0};
    int i = 0;
    int delay_count = 10;

    read_download_tmpInfo(&tFileManage);
    LOG_E("is_need_restart | tFileManage.update_flag: %d", tFileManage.update_flag);
    if(tFileManage.update_flag == FLAG_APP_UPDATE_ZK || tFileManage.update_flag == FLAG_APP_UPDATE_APP)
    {
        rt_kprintf("system soft reset");
        send_cmd_buff.cmd = cmd_buf->cmd;
        cmd_send(dev_inst, &send_cmd_buff);
        tFileManage.cur_frame_no = 0;
        write_download_tmpInfo(&tFileManage);
        for (i = 0; i < delay_count; i++)
        {
            if (s_cur_download_file_info.feed_dog != NULL)
            {
                s_cur_download_file_info.feed_dog();
            }
            rt_thread_mdelay(500);
        }
        if (tFileManage.update_flag == FLAG_APP_UPDATE_ZK)
        {
            set_software_reset_reason(RESET_REASON_UPDATE_ZK);
        } else if (tFileManage.update_flag == FLAG_APP_UPDATE_APP)
        {
            set_software_reset_reason(RESET_REASON_UPDATE_PROGRAM);
        }
        rt_hw_cpu_reset();
    }
    return ;
}

Static int bottom_update_file_name_cmp(char* file_name)
{
    for(int loop = 0; loop < BOTTOM_MAX_DOWNLOAD_FILE_NUM; loop ++)
    {
        if(s_register_download_file_name[loop] == NULL || s_register_download_file_name[loop]->file_name == NULL)
        {
            continue;
        }
        if(rt_strcmp(s_register_download_file_name[loop]->file_name, file_name) == 0)
        {
            if(rt_strcmp(DCMU_ZK_UPDATE_FILE_NAME, file_name ) == 0){
                g_bottom_zk_update_flag = TRUE;
            }
            s_cur_download_file_info.cur_file_type = s_register_download_file_name[loop]->file_type;
            s_cur_download_file_info.cur_file_name = s_register_download_file_name[loop]->file_name;
            s_cur_download_file_info.cur_store_file_name = s_register_download_file_name[loop]->store_name;
            LOG_E("file_type:%d, file_name:%s, store_name:%s\n", s_cur_download_file_info.cur_file_type,
                s_cur_download_file_info.cur_file_name, s_cur_download_file_info.cur_store_file_name);
            return SUCCESSFUL;
        }
    }
    return FAILURE;
}

Static int parse_first_frame(cmd_buf_t* tran_cmd_buf,unsigned short frm_no){
    int update_first_max_len = 0;
    int update_file_name_len = 0;
    bottom_update_file_manage_t* trsdata_ctr_inf = NULL;
    trsdata_ctr_inf = &s_download_trsdata_ctr_inf;
    bottom_update_file_attr_t file_info = {0};
    update_first_max_len = DOWNLOAD_UPDATE_FIRST_FRAME_LEN;
    update_file_name_len = UPDATE_FILE_NAME_LEN;

    //data域解析
    if(tran_cmd_buf->data_len != update_first_max_len) {
        trsdata_ctr_inf->rtn = DOWNLOAD_FIRST_FRAME_ERR;
        LOG_E("%s:%d|first_frame data_len:%d", __FUNCTION__ , __LINE__, tran_cmd_buf->data_len);
        return SUCCESSFUL;
    }

    file_info.per_frame_len = get_int16_data(&tran_cmd_buf->buf[0]);
    file_info.total_frames = get_int16_data(&tran_cmd_buf->buf[2]);
    file_info.total_leng = get_ulong_data(&tran_cmd_buf->buf[4]);
    g_bottom_download_total_size = file_info.total_leng;
    if(file_info.total_leng > 1.5*1024*1024)
    {
        LOG_E("%s:%d|first_frame total_leng:%d", __FUNCTION__ , __LINE__, file_info.total_leng);
        trsdata_ctr_inf->rtn = DOWNLOAD_FIRST_FRAME_ERR;
        return SUCCESSFUL;
    }
    rt_memcpy_s(file_info.file_name, update_file_name_len, &tran_cmd_buf->buf[8], update_file_name_len);
    rt_memcpy_s(file_name_tmp, update_file_name_len, &tran_cmd_buf->buf[8], update_file_name_len);
    if(bottom_update_file_name_cmp(file_info.file_name) == FAILURE)
    {
        LOG_E("%s:%d|first_frame file_name:%s", __FUNCTION__ , __LINE__, file_info.file_name);
        trsdata_ctr_inf->rtn = DOWNLOAD_FIRST_FRAME_ERR;
        return SUCCESSFUL;
    }

    rt_memcpy_s(file_info.file_time, UPDATE_FILE_TIME_LEN, &tran_cmd_buf->buf[8+update_file_name_len], UPDATE_FILE_TIME_LEN);

    //支持断点续传
    if (file_change_check(&trsdata_ctr_inf->file_info,&file_info) == TRUE ||
        trsdata_ctr_inf->cur_frame_no == 0) {
        trsdata_ctr_inf->file_info.filecrc = 0;
        rt_memset_s(&trsdata_ctr_inf->file_info, sizeof(bottom_update_file_attr_t), 0x00, sizeof(bottom_update_file_attr_t));
        rt_memcpy_s(&trsdata_ctr_inf->file_info, sizeof(bottom_update_file_attr_t), &file_info, sizeof(bottom_update_file_attr_t));
        trsdata_ctr_inf->data_offset = 0;
        trsdata_ctr_inf->cur_frame_no = 1;
    }
    rt_kprintf("total_frames:%d,total_leng:%d k\n", file_info.total_frames, file_info.total_leng/1024);
    trsdata_ctr_inf->rtn = DOWNLOAD_FRAME_CORRECT;
    trsdata_ctr_inf->update_status = BOTTOM_UPDATEING;

    //断点续传数据(存放在芯片里面，不用额外增加flash分区)
    write_download_tmpInfo(trsdata_ctr_inf);
    return SUCCESSFUL;
}

Static int parse_zk_update_trig(void* dev_inst, void *cmd_buf) {
    int ret = get_zk_download_rtn(cmd_buf);
    s_trig_ctr_inf.rtn = ret;
    if (ret != SUCCESSFUL) {
        rt_kprintf("ziku trig check fail,errno:%x\n", ret);
        s_upload_trig_inf.trig_success = FALSE;
        s_upload_trig_inf.trig_times = 0;
        return SUCCESSFUL;
    }
    //开启触发定时器
    rt_timer_start(s_update_trig_timer);
    s_upload_trig_inf.trig_times++;
    //触发成功
    if(s_upload_trig_inf.trig_times >= DOWNLOAD_TRIG_TIMES){
        s_upload_trig_inf.trig_times = 0;
        s_upload_trig_inf.trig_success = TRUE;
        rt_kprintf("ziku download trig success\n");
        rt_timer_stop(s_update_trig_timer);
        rt_timer_start(g_update_timer);
    }
    return SUCCESSFUL;
}

Static int switch_com_baudrate(void* dev_inst, unsigned int baudrate) {
    struct serial_configure def_config = RT_SERIAL_CONFIG_DEFAULT;
    def_config.baud_rate = BAUD_RATE_19200;
    dev_inst_t* inst = dev_inst;
    rt_err_t ret = RT_EOK;

    if (inst == NULL || inst->dev_type == NULL || inst->dev_type->link_inst == NULL) {
        return FAILURE;
    }
    link_inst_t* link_inst = inst->dev_type->link_inst;
    ret = uart_dev_config(link_inst->name, &def_config);
    if (ret != SUCCESSFUL) {
        rt_kprintf("change baud rate to %d failure\n", baudrate);
        return FAILURE;
    }
    rt_kprintf("change baud rate to %d success\n", baudrate);
    s_dev_inst_change_baudrate = dev_inst;
    return SUCCESSFUL;
}

Static int is_first_frame_deal(unsigned short frm_no, cmd_buf_t* tran_cmd_buf)
{
    if (frm_no == 0)
    {
        read_download_tmpInfo(&s_download_trsdata_ctr_inf);
        g_first_frame_flag = TRUE;
        rt_timer_start(g_update_timer);
        parse_first_frame(tran_cmd_buf, frm_no);
        tran_cmd_buf->rtn = s_download_trsdata_ctr_inf.rtn;
        return FAILURE;
    }
    return SUCCESSFUL;
}

Static int pack_update_trig_bottom(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* trig_cmd_buf = (cmd_buf_t*)cmd_buf;

    // 入参校验
    if (trig_cmd_buf == NULL || trig_cmd_buf->buf == NULL) {
        return FAILURE;
    }

    if(s_trig_ctr_inf.rtn == DownloadFrameErr) {
        //附加码错误
        trig_cmd_buf->data_len = 0;
        trig_cmd_buf->rtn = BOTTOM_RTN_CMD_ERROR;
        return SUCCESSFUL;
    }else{
        //回复正确文件名
        trig_cmd_buf->data_len = UPDATE_FILE_NAME_LEN;
        rt_memset_s(trig_cmd_buf->buf, trig_cmd_buf->data_len, 0x00, trig_cmd_buf->data_len);
        rt_memcpy_s(trig_cmd_buf->buf, UPDATE_FILE_NAME_LEN,  DCMU_UPDATE_FILE_NAME, sizeof(DCMU_UPDATE_FILE_NAME)-1);
    }

    set_software_reset_reason(RESET_REASON_UPDATE_PROGRAM);
    BeginDownload(FLAG_BACKUP);

    return SUCCESSFUL;
}

int BeginDownload(unsigned char ucMode)
{
    part_data_t part_data = {0};

    part_data.buff = (unsigned char *)&s_tFileManage;
    part_data.len = sizeof(bottom_update_file_manage_t);
    part_data.offset = 0;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), UPDATE_INFO);
    rt_memset_s(&s_tFileManage,sizeof(bottom_update_file_manage_t),0xFF,sizeof(bottom_update_file_manage_t));
    if (SUCCESSFUL != storage_process(&part_data, read_opr)) {
        return FALSE;
    }
    s_tFileManage.ucUpdateAddr = get_host_addr();
    s_tFileManage.ucFlag = ucMode;
    s_tFileManage.crc = crc_cal((unsigned char *)&s_tFileManage, offsetof(bottom_update_file_manage_t,crc));
    if (SUCCESSFUL != storage_process(&part_data, erase_write_opr)) {
        return FALSE;
    }

    NVIC_SystemReset();
    return TRUE;
}

/* 解析上传文件列表帧 首发帧和其他帧DATA均为空 */
int parse_upload_file_list(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = NULL;
    T_BottomFileUploadStruct* trsdata_ctr_inf = NULL;
    bottom_comm_cmd_head_t* proto_head = NULL;
    trsdata_ctr_inf = &s_upload_trsdata_inf;

    //入参校验
    if ( cmd_buf == NULL || ((cmd_buf_t*)cmd_buf)->buf == NULL)
    {
        return FAILURE;
    }

    tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    //判断协议层解析数据是否正确
    if (tran_cmd_buf->rtn != DOWNLOAD_FRAME_CORRECT)
    {
        return FAILURE;
    }
    proto_head = (bottom_comm_cmd_head_t*)tran_cmd_buf->cmd->req_head;
    trsdata_ctr_inf->cur_frame_no = proto_head->append;
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;

    return SUCCESSFUL;
}

/* 打包上传文件列表帧 打包首发帧和其他帧 */
int pack_upload_file_list(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    unsigned short frm_no = 0;
    bottom_comm_cmd_head_t* req_head = NULL;
    bottom_comm_cmd_head_t* ack_head = NULL;

    // 入参校验
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL)
    {
        return FAILURE;
    }

    //判断协议层解析数据是否正确
    if (tran_cmd_buf->rtn != DOWNLOAD_FRAME_CORRECT)
    {
        return FAILURE;
    }

    req_head = (bottom_comm_cmd_head_t*)tran_cmd_buf->cmd->req_head;
    ack_head = (bottom_comm_cmd_head_t*)tran_cmd_buf->cmd->ack_head;
    frm_no = req_head->append;
    ack_head->append = frm_no;

    if (frm_no == 0)
    {
        upload_trig_end();
        pack_filelist_first_frame(tran_cmd_buf, frm_no);
    }
    else
    {
        pack_filelist_other_frame(tran_cmd_buf, frm_no);
    }

    if (s_upload_trsdata_inf.tFileList.wListTotalFrame ==  req_head->append &&
        s_upload_trsdata_inf.tFileList.wListTotalFrame != 0)
    {
        rt_memset_s(&s_upload_trsdata_inf, sizeof(T_BottomFileUploadStruct), 0, sizeof(T_BottomFileUploadStruct));
    }

    return SUCCESSFUL;
}

/* 打包上传文件列表首发帧 */
Static int pack_filelist_first_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no)
{
    int i = 0;
    int offset = 0;
    int totalByte = 0;

    //获取文件列表
    get_record_file_list_new((char**)s_upload_trsdata_inf.tFileList.pucListName, (char*)s_upload_trsdata_inf.tFileList.aucListNameLen, 
                    &s_upload_trsdata_inf.tFileList.ucListTotalNum);

    //计算总帧数
    for (i=0; i<s_upload_trsdata_inf.tFileList.ucListTotalNum; i++)
    {
        totalByte  += rt_strnlen_s(s_upload_trsdata_inf.tFileList.pucListName[i], s_upload_trsdata_inf.tFileList.aucListNameLen[i]);
    }
    s_upload_trsdata_inf.tFileList.wListTotalFrame = (totalByte + BOTTOM_UPLOAD_DATA_FRAME_LEN - 1) / BOTTOM_UPLOAD_DATA_FRAME_LEN;

    s_upload_trsdata_inf.tFileList.wListTotalFrame++;

    put_int16_to_buff(&tran_cmd_buf->buf[offset], s_upload_trsdata_inf.tFileList.wListTotalFrame);

    offset += 2;
    tran_cmd_buf->data_len = offset;
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;

    return SUCCESSFUL;
}

/* 打包上传文件列表其他帧 */
Static int pack_filelist_other_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no)
{
    int i = 0;
    int offset = 0;

    // 有几个文件
    tran_cmd_buf->buf[offset] = s_upload_trsdata_inf.tFileList.ucListTotalNum;
    offset += 1;

    //获取文件列表，每个文件名占有40字节
    for (i=0; i<s_upload_trsdata_inf.tFileList.ucListTotalNum; i++)
    {
        rt_memset_s(&tran_cmd_buf->buf[offset], UPLOAD_FILE_NAME_MAX_LEN, 0x00, UPLOAD_FILE_NAME_MAX_LEN);
        rt_memcpy_s(&tran_cmd_buf->buf[offset], s_upload_trsdata_inf.tFileList.aucListNameLen[i], 
            s_upload_trsdata_inf.tFileList.pucListName[i], s_upload_trsdata_inf.tFileList.aucListNameLen[i]);
        offset += UPLOAD_FILE_NAME_MAX_LEN;
    }

    tran_cmd_buf->data_len = offset;
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;

    return SUCCESSFUL;
}

/* 解析上传文件数据帧 解析首发帧，其他帧DATA为空 */
Static int parse_upload_file_data(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    T_BottomFileUploadStruct* trsdata_ctr_inf = NULL;
    bottom_comm_cmd_head_t* proto_head = NULL;
    trsdata_ctr_inf = &s_upload_trsdata_inf;
    // unsigned char ctrl_sample_switch[2] = {0};
    // 入参校验
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL)
    {
        trsdata_ctr_inf->rtn = DOWNLOAD_FRAME_ERR;
        return FAILURE;
    }

    //判断协议层解析数据是否正确
    if (tran_cmd_buf->rtn != DOWNLOAD_FRAME_CORRECT)
    {
        trsdata_ctr_inf->rtn = DOWNLOAD_FRAME_ERR;
        return FAILURE;
    }
    proto_head = (bottom_comm_cmd_head_t*)tran_cmd_buf->cmd->req_head;
    trsdata_ctr_inf->cur_frame_no = proto_head->append;
    trsdata_ctr_inf->rtn = DOWNLOAD_FRAME_CORRECT;
    rt_timer_start(upload_trigTimer);
    if (trsdata_ctr_inf->cur_frame_no == 0)
    {
        s_upload_trig_inf.trig_times = 0;
        upfile_sta = UPLOADING;
        parse_filedata_first_frame(tran_cmd_buf, trsdata_ctr_inf->cur_frame_no);
        // 关闭采样
        // ctrl_sample_switch[0] = FALSE;
        // ctrl_sample_switch[1] = FALSE;
        // pub_msg_to_thread(CTRL_SAMPLE_SWITCH, &ctrl_sample_switch, sizeof(ctrl_sample_switch));
    }

    return SUCCESSFUL;
}

/* 解析上传文件数据首发帧 */
Static int parse_filedata_first_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no)
{
    s_upload_trsdata_inf.tFileName.wReqFileNameLen = UPLOAD_FILE_NAME_MAX_LEN; //请求的文件名长度
    rt_memset_s( s_upload_trsdata_inf.tFileName.ucReqFileName , UPLOAD_FILE_NAME_MAX_LEN , 0 , UPLOAD_FILE_NAME_MAX_LEN );
    rt_memcpy_s(s_upload_trsdata_inf.tFileName.ucReqFileName, sizeof(s_upload_trsdata_inf.tFileName.ucReqFileName), 
        &tran_cmd_buf->buf[0], s_upload_trsdata_inf.tFileName.wReqFileNameLen); //请求的文件名

    return SUCCESSFUL;
}

/* 打包上传文件数据帧 打包首发帧和其他帧 */
Static int pack_upload_file_data(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    unsigned short frm_no = 0;
    bottom_comm_cmd_head_t* proto_head = NULL;
    bottom_comm_cmd_head_t* ack_head = NULL;

    // 入参校验
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL)
    {
        return FAILURE;
    }

    //判断协议层解析数据是否正确
    if (tran_cmd_buf->rtn != DOWNLOAD_FRAME_CORRECT)
    {
        return FAILURE;
    }

    proto_head = (bottom_comm_cmd_head_t*)tran_cmd_buf->cmd->req_head;
    ack_head = (bottom_comm_cmd_head_t*)tran_cmd_buf->cmd->ack_head;
    frm_no = proto_head->append;
    ack_head->append = frm_no;

    if (frm_no == 0)
    {
        pack_filedata_first_frame(tran_cmd_buf, frm_no);
    }
    else
    {
        pack_filedata_next_frame(tran_cmd_buf, frm_no);
    }
    return SUCCESSFUL;
}

/* 打包上传文件数据首发帧 */
Static int pack_filedata_first_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no)
{
    int offset = 0;
    time_t pt_time;
    static unsigned int crc_ctx = 0 ^ 0xFFFFFFFF; // 校验和

    if (s_upload_trsdata_inf.rtn == DOWNLOAD_FRAME_ERR)
    {
        tran_cmd_buf->rtn = DOWNLOAD_FRAME_ERR;
        return FAILURE;
    }

    // 获取文件总长度，his_data.c提交后放开
    s_upload_trsdata_inf.uFileTotalLen = get_record_file_len_new(s_upload_trsdata_inf.tFileName.ucReqFileName);

    // 上传帧字节数
    put_int16_to_buff(&(tran_cmd_buf->buf[offset]), BOTTOM_UPLOAD_DATA_FRAME_LEN);
    offset += 2;

    //总帧数
    s_upload_trsdata_inf.wFileTotalFrame = 1 + (s_upload_trsdata_inf.uFileTotalLen + BOTTOM_UPLOAD_DATA_FRAME_LEN - 1) 
                                                / BOTTOM_UPLOAD_DATA_FRAME_LEN; //向上取整
    put_int16_to_buff(&(tran_cmd_buf->buf[offset]), s_upload_trsdata_inf.wFileTotalFrame);
    offset += 2;

    //上传文件总大小
    put_int32_to_buff(&(tran_cmd_buf->buf[offset]), s_upload_trsdata_inf.uFileTotalLen);
    offset += 4;

    //上传文件名
    rt_memcpy_s(&tran_cmd_buf->buf[offset], s_upload_trsdata_inf.tFileName.wReqFileNameLen, 
        s_upload_trsdata_inf.tFileName.ucReqFileName, s_upload_trsdata_inf.tFileName.wReqFileNameLen);
    offset += s_upload_trsdata_inf.tFileName.wReqFileNameLen;

    //上传文件时间
    pt_time = get_timestamp_s();
    rt_memset_s(&s_upload_trsdata_inf.ucFileTime[0], UPLOAD_FILE_TIME_LEN, 0, UPLOAD_FILE_TIME_LEN);
    put_time_t_to_buff(s_upload_trsdata_inf.ucFileTime, pt_time);
    rt_memcpy_s(&tran_cmd_buf->buf[offset], sizeof(s_upload_trsdata_inf.ucFileTime), s_upload_trsdata_inf.ucFileTime, sizeof(s_upload_trsdata_inf.ucFileTime));
    offset += UPLOAD_FILE_TIME_LEN;

    //文件校验码
    crc32_calc(&crc_ctx, tran_cmd_buf->buf, offset);
    s_upload_trsdata_inf.uCrc = crc_ctx;
    put_int16_to_buff(&(tran_cmd_buf->buf[offset]), s_upload_trsdata_inf.uCrc);
    offset += 4;

    tran_cmd_buf->data_len = offset;
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;

    return SUCCESSFUL;
}

/* 打包上传文件数据其他帧 */
Static int pack_filedata_next_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no)
{
    int offset = 0;
    int actual_len = 0;
    int tmp = 0;
    // unsigned char ctrl_sample_switch[2] = {0};
    if (s_upload_trsdata_inf.rtn == DOWNLOAD_FRAME_ERR)
    {
        tran_cmd_buf->rtn = DOWNLOAD_FRAME_ERR;
        return FAILURE;
    }

    //当请求的数据帧号为最后一帧，打开采样控制开关
    if(s_upload_trsdata_inf.wFileTotalFrame - 1 == frm_no)
    {
        upfile_sta = NOTUPLOAD;
        rt_timer_stop(upload_trigTimer);
        // ctrl_sample_switch[0] = TRUE;     // 开启采样
        // if(rt_memcmp((char*)(s_upload_trsdata_inf.tFileName.ucReqFileName), RECORD_FAULT_DATA, sizeof(RECORD_FAULT_DATA)) == 0)
        // {
        //     // rt_kprintf("upload record data success\n");
        //     // ctrl_sample_switch[1] = TRUE;     // 上传成功
        //     // unsigned char auth_code = 0x10;
        //     // delete_record_data_new(auth_code, RECORD_DATA_ACC_INDEX);//删除文件
        // }
        // else
        // {
        //     // rt_kprintf("upload extreme data success\n");
        //     // ctrl_sample_switch[1] = FALSE;    // 维持原样
        // }
        // pub_msg_to_thread(CTRL_SAMPLE_SWITCH, &ctrl_sample_switch, sizeof(ctrl_sample_switch));
    }

    //求当前文件读取偏移
    offset = (frm_no - 1) * BOTTOM_UPLOAD_DATA_FRAME_LEN;
    rt_memset_s(tran_cmd_buf->buf, S_BUFF_LEN_1024, 0, S_BUFF_LEN_1024);
    tmp = s_upload_trsdata_inf.uFileTotalLen - offset ;
    actual_len =( tmp >= BOTTOM_UPLOAD_DATA_FRAME_LEN ) ? BOTTOM_UPLOAD_DATA_FRAME_LEN : tmp ;
    //获取文件数据，his_data.c提交后放开
    actual_len = get_frame_from_record_file_new(s_upload_trsdata_inf.tFileName.ucReqFileName, offset, actual_len, tran_cmd_buf->buf);

    tran_cmd_buf->data_len = actual_len;
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;
    return SUCCESSFUL;
}

Static int parse_update_trig(void* dev_inst, void *cmd_buf) {
    if(upfile_sta == UPLOADING)
    {
        rt_kprintf("record file is uploading\n");
        return NO_NEED_REPONSE;
    }
    //开启触发定时器
    rt_timer_start(upload_trigTimer);
    s_upload_trig_inf.trig_success = FALSE;
    s_upload_trig_inf.trig_times++;
    //触发成功
    if(s_upload_trig_inf.trig_times >= DOWNLOAD_TRIG_TIMES){
        s_upload_trig_inf.trig_success = TRUE;
    }
    return SUCCESSFUL;
}

Static int pack_update_trig(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    // 入参校验
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL) {
        return FAILURE;
    }

    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;
    return SUCCESSFUL;
}

void upload_manage_init(void) {

    rt_uint32_t trig_tick_num   = TRIG_TIMEOUT * 1000;      //30秒
    short i = 0;

    for(i = 0; i < sizeof(s_upload_cmd_handle)/sizeof(s_upload_cmd_handle[0]); i++) 
    {
        register_cmd_handle(&s_upload_cmd_handle[i]);
    }
    s_update_trig_timer = rt_timer_create("update_trigTimer", down_trig_timeout, RT_NULL, 30000, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
    if(s_update_trig_timer == NULL)
    {
        return ;
    }
    g_update_timer = rt_timer_create("ctrl_update_timer", time_out_ctrl_update, NULL, 30000, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
    if(g_update_timer == NULL)
    {
        return ;
    }
    upload_trigTimer = rt_timer_create("upload_trigTimer", upload_trigTimer_timeout, RT_NULL, trig_tick_num, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
    return;
}

Static void upload_trigTimer_timeout()
{
    // unsigned char ctrl_sample_switch[2] = {0};
    upfile_sta = NOTUPLOAD;
    s_upload_trig_inf.trig_times = 0;
    s_upload_trig_inf.trig_success = FALSE;
    rt_timer_stop(upload_trigTimer);
    // 维持原样
    // ctrl_sample_switch[0] = TRUE;
    // ctrl_sample_switch[1] = FALSE;
    // pub_msg_to_thread(CTRL_SAMPLE_SWITCH, &ctrl_sample_switch, sizeof(ctrl_sample_switch));
}

Static void upload_trig_end()
{
    s_upload_trig_inf.trig_times = 0;
    rt_timer_stop(upload_trigTimer);
}