/**************************************************************************
* Copyright (C) 2010-2011, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：ZXDU18 CTL板软件
* 文件名称：alarm.c
* 文件说明：告警模块
* 作    者  ：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/
#include "common.h"
#include "rtthread.h"
#include "sample.h"
#include "utils_heart_beat.h"
#include "thread_id.h"
#include "hisdata.h"
#include "realAlarm.h"
#include "alarm_in.h"
#include "para.h"
#include "comm.h"
#include "protocol.h"
#include "CommCan.h"
#include "CommCan2.h"
#include "commBdu.h"
#include "battery.h"
#include "ism330dhcx.h"
#include "comm.h"
#include "led.h"
#include "fileSys.h"
#include "usart.h"
#include "const_define.h"
#include "wireless.h"
#include "BalanceTimeStatistics.h"
#include "utils_rtthread_security_func.h"

#define ALM_JUDGE_FINISH 2
#define ALM_FIRE_CONTROL 288

#define ANALOG_ALARM_INIT(tAlmInfo, alarmName) do{	\
    tAlmInfo.pucRealAlarm = &s_tBcmAlarmReal.alarmName; \
    tAlmInfo.pucStateAlarm = &s_tBcmAlarmFlag.alarmName;    \
    tAlmInfo.pucCounter = &s_tBcmAlarmCounter.alarmName;    \
    tAlmInfo.pucCounterThreshold = &s_tBcmAlarmCounterMax.alarmName;    \
    tAlmInfo.pExceptionHandling  = NULL;        \
    tAlmInfo.ucAlarmType = FAULT;   \
    tAlmInfo.pwRetryCounter = NULL;    \
    tAlmInfo.pwRetryCounterMax = NULL;    \
    }while(0)

#define DIGITAL_ALARM_INIT(tAlmInfo, alarmName) do{	\
    tAlmInfo.pucRealAlarm = &s_tBcmAlarmReal.alarmName; \
    tAlmInfo.pucStateAlarm = &s_tBcmAlarmFlag.alarmName;    \
    tAlmInfo.pucCounter = &s_tBcmAlarmCounter.alarmName;    \
    tAlmInfo.pucCounterThreshold = &s_tBcmAlarmCounterMax.alarmName;    \
    tAlmInfo.pExceptionHandling  = NULL;        \
    tAlmInfo.ucAlarmType = FAULT;   \
    tAlmInfo.ucState = s_tBcmAlarmTmp.alarmName;    \
    tAlmInfo.pwRetryCounter = NULL;    \
    tAlmInfo.pwRetryCounterMax = NULL;    \
    }while(0)

#define PROTECT_SHIELD_ALARM(_prtName, _almName) do{ \
    if( s_tBcmAlarm._prtName == 1 ) {	\
    s_tBcmAlarm._almName = NORMAL;	\
        }	\
}while(0)

#define CLEAR_ALAEM(_almName) do{   \
    s_tBcmAlarmReal._almName = NORMAL; \
    s_tBcmAlarm._almName = NORMAL; \
    s_tBcmAlarmCounter._almName = 0; \
    s_tBcmAlarmFlag._almName = 0; \
}while(0)

/***********************  变量定义  ************************/
/*  Bak--代表上次数据或告警
    Flag 告警状态标志
    Counter 告警计数器
    Shield 告警屏蔽开关 */
    
Static T_AlmDataStruct s_tAlarmData;
Static T_AlmParaStruct s_tAlarmPara;
Static T_BCMAlarmStruct s_tBcmAlarm = {0, };      //屏蔽后的告警
Static T_BCMAlarmStruct s_tBcmAlarmReal = {0, };  //未屏蔽的告警
static T_BCMAlarmStruct s_tBcmAlarmBak = {0, };
static T_BCMAlarmStruct s_tBcmAlarmFlag = {0, };
static T_BCMAlarmStruct s_tBcmAlarmCounter = {0, };
static T_BCMAlarmStruct s_tBcmAlarmCounterMax = {0, };
Static T_BCMAlarmStruct s_tBcmAlarmShield = {0, };
static T_BCMAlarmStruct s_tBcmAlarmTmp = {0, };
static T_BCMAlarmStruct s_tBcmAlarmStartUp = {0, };   //启动最初阶段的告警
Static T_DefenceInfoStruct s_tDefenceInfo = {0, };  //布防信息
static T_SysPara       s_tSysPara;

static WORD s_wBcmAlmStartUpTimer = 0;          //启动最初阶段告警判断计数器
static WORD iterFlag = 0;          //启动最初阶段告警判断计数器
Static BOOLEAN s_bBcmAlmStartUpFlag = True;     //启动最初阶段告警判断标志

static BOOLEAN s_bGprsBattAlmEn = False;       //GPRS电池锁死/解除
static BOOLEAN s_bMainAlarmChanged = False;        // 重要告警变化标志，保留，20110406，yang.an
Static BOOLEAN s_bMainAlarmExist = False;        // 重要告警存在标志
static BOOLEAN s_bBalanceFault = False;
static BYTE s_aucBcmDataFlag[6] = { 1, 1, 1, 1, 1, 1 };
BYTE   g_ucAlmLevelChge = False;
static WORD s_wCommLostCntWire = 0;      // 防盗线防盗计时器
Static WORD s_wCommLostCntGyro = 0;      // 陀螺仪防盗计时器
Static WORD s_wCommLostCntGyro_1min = 0;         // 陀螺仪防盗计时器(固定的1min计时器)
Static BOOLEAN s_bCommLostCntGyroFlag = False;     // 陀螺仪防盗计时标志位
Static WORD s_wCommLostBinding = 0;      // 站点防盗延时时间
Static UINT32 s_ulNetCommLostBinding = 0;   // 网管绑定防盗计时器
static BOOLEAN s_bGprsAutoUpDataFlag = False;

static BYTE s_ucDisCurrHighFlag = 0;
static BYTE s_ucChgCurrHighFlag = 0;
static BYTE s_ucLastCurrHighFlag = 0;
static FLOAT s_fBattVoltThresh = 20.0f;

#ifdef PAKISTAN_CMPAK_PROTOCOL
Static BOOLEAN s_bOverLoadLockStatus = False;
#endif
/* 实时、历史告警显示结构数组 */
Static T_DisRealAlarmStruct s_atDisRealAlarm[ALARM_NUM + 1];
static T_RealAlm256SaveStruct s_tRealAlmSave;
Static T_HardwareParaStruct   s_tHardwarePara;
static T_NewBattSaveStruct s_tCellDamageSave;

static T_StateListenStruct s_atCellVoltTooHigh[CELL_VOL_NUM];
static T_StateListenStruct s_atCellVoltPoor[CELL_VOL_NUM];
static BOOLEAN s_bExistAlarmFlag = False; 
static BYTE s_ucAlarmStatusMap[] = {ALM_NORMAL,ALM_ABNORMAL,ALM_CRITICAL,ALM_SYS_ABNORMAL};

/* 均衡回路判断接口定义的静态变量 */
// Static WORD s_wCircOpenCnt = 0;
// Static WORD s_wCircShortCnt = 0;
// Static BYTE s_uCircOpenTimes = 0;
Static BOOLEAN s_bRestartIC = False;

/* 站点防盗结构体 */
Static T_SiteAntiTheftStruct s_tSiteAntiTheft;
Static T_NetAntiTheftStruct  s_tNetAntiTheft;
Static BOOLEAN s_bNetAntiKeyMatchStatus = TRUE;

/*********************  静态函数原型定义  **********************/
Static BOOLEAN  JudgeBcmAlarm( void );
static void     JudgeBcmAnalogAlarm( void );
static void     JudgeBcmDigitalAlarm( void );
static void     JudgeBcmAlarmPriority( void );
Static void     DealDisAlarm( WORD  wID, BYTE ucState );

static void     AnalyticalAlmCheck( WORD  wID );
Static INT32S DisCurrH_Short_PrtHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus);
static BOOLEAN  ClearSaveRealAlm(WORD wID);
static void     SetAlarmJudgeCounter(void);
static void     GetAlarmInfo(void);
Static INT32S   CellLowPrtHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus);
static INT32S   CellDynamicLowPrtHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus);
Static INT32S   BattVoltLowPrtHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus);
// static INT32S   BattPowerOffPrtHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus);
static BOOLEAN  IsBattTheft(void);
static INT32S   CurrHighPrtDelay(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus, WORD wRetryDelay, BYTE ucLastPrtDelayFlag);
static INT32S   CellPoorConsisHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus);
static INT32S BattUnderVoltALarmHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus);
static void     checkSavedAlarm( void );
static void SetTheftAlm(BOOLEAN bAlm, BYTE ucTheftType);
static void InitSavedTheftAlarm(void);
static BOOLEAN  IsCellPoorConsisStart(FLOAT fBattVol, FLOAT fBattCurr);
//Static BOOLEAN  IsEqualCircuitFault(void);
static INT32S   CellDamageHandle(struct T_AlmInfoTmp const *pAlmInfo, BOOLEAN *pbTmpStatus);
Static BOOLEAN 	JudgeGyroAntiTheft(void);
static void 		ReadDefenceStatus(void);
Static void     UpdateDefenceStatus(void);
static FLOAT CalCellVoltLowPrtThre(FLOAT fCellTempMin, FLOAT fCellVoltLowPrt);
static BOOLEAN JudgeIfExixtAlarm(BYTE *pAlarm,WORD wLength);
static FLOAT CalCellDynamicLowPrtThre(BYTE Num , FLOAT MinCellTemp);
static BOOLEAN IsAnyAlarmExist();
static BOOLEAN  IsAnyAlarmExistNew();
static BOOLEAN JudgeAlarmInfoType(BOOLEAN condition);
static void InitBindingAntiTheft(void);
Static BOOLEAN JudgeBindingAntiTheft(void);
Static void InfoWriteBindingAntiTheft(BOOLEAN stat, BYTE *pucSiteAntiTheftKey);
Static void InitNetAntiTheft(void);
Static BOOLEAN JudgeNetAntiTheft(void);
static BYTE AlarmIndicatorStatus(BYTE IndicatorValue);
static void HandleNonZeroSequenceNumber(BYTE *pucNetAntiTheftSN, BYTE *ucResult);
Static void JudgeBattAlarm(void);
#ifdef ALARM_UPLOAD_ACTIVE
static BOOLEAN AlarmUploadActive(SHORT *pAlarmStrucOld, WORD uindex, BOOLEAN ucAlarmOccurFlag);
#endif

#ifdef BOARD_Temp_High_Prt_BDPB
static INT32S BoardTempHighPrtHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus);
#endif

/******************************************************************************
**函 数 名：ALarmCommon()
**输    入： T_AlmInfoStruct *tAlmInfo 
**输    出：无
**调用模块：
**功能描述：根据输入数据，告警门限判断告警
* 作    者：刘斌
**时    间：2012-6-28
**修改记录：
* 日    期      版  本      修改人      修改摘要      
******************************************************************************/
#if 1
Static BOOLEAN ALarmCommon( T_AlmInfoStruct *tAlmInfo , BOOLEAN   bStatus)
{
    T_AlmInfoStruct *pAlmInfo;

    if (tAlmInfo->pucRealAlarm == NULL)
    {
        return  FAILURE;
    }
    pAlmInfo = (T_AlmInfoStruct *)tAlmInfo;
    if (*(pAlmInfo->pucStateAlarm) == bStatus ) //与上次判断结果相同，累计次数加1
    {//ALARM_COUNTER
        if (*(pAlmInfo->pucCounter) < *(pAlmInfo->pucCounterThreshold) )
        {
            (*(pAlmInfo->pucCounter))++;
        }
    }
    else    //与上次判断结果不同，累计次数清0
    {
        *(pAlmInfo->pucStateAlarm) = bStatus;
        *(pAlmInfo->pucCounter)  = 0;
    }
            
    return SUCCESSFUL;
}

/******************************************************************************
**函 数 名：InitAlarm(void)
**输    入：无
**输    出：无
**调用模块：
**功能描述：初始化
* 作    者：余志刚
**时    间：2018.11.9
**修改记录：
* 日    期      版  本      修改人      修改摘要      
******************************************************************************/ 

void InitAlarm(void)
{
    BYTE ucDcrFaultSta = 0;

    rt_memset_s( &s_tBcmAlarm, sizeof(T_BCMAlarmStruct), 0, sizeof(T_BCMAlarmStruct));
    rt_memset_s( &s_tBcmAlarmReal, sizeof(T_BCMAlarmStruct), 0, sizeof(T_BCMAlarmStruct));
    rt_memset_s( &s_tBcmAlarmBak, sizeof(T_BCMAlarmStruct), 0, sizeof(T_BCMAlarmStruct));
    rt_memset_s( &s_tBcmAlarmFlag, sizeof(T_BCMAlarmStruct), 0, sizeof(T_BCMAlarmStruct));
    rt_memset_s( &s_tBcmAlarmCounter, sizeof(T_BCMAlarmStruct), 0, sizeof(T_BCMAlarmStruct));
    rt_memset_s( &s_tBcmAlarmShield, sizeof(T_BCMAlarmStruct), 0, sizeof(T_BCMAlarmStruct));
    rt_memset_s( &s_tBcmAlarmTmp, sizeof(T_BCMAlarmStruct), 0, sizeof(T_BCMAlarmStruct));
    rt_memset_s( &s_tBcmAlarmStartUp, sizeof(T_BCMAlarmStruct), 0, sizeof(T_BCMAlarmStruct));
    rt_memset_s( &s_tHardwarePara, sizeof(T_HardwareParaStruct), 0, sizeof(T_HardwareParaStruct));

    readBmsHWPara(&s_tHardwarePara);
    InitBindingAntiTheft();
    InitNetAntiTheft();
    checkSavedAlarm();
    InitSavedTheftAlarm();

    ucDcrFaultSta = getResetData(DCR_FAULT_ALM_PRT);
    // 如果掉电保存了直流内阻异常保护，那么继续保持保护状态；
    if(ucDcrFaultSta == DCR_PROTECT)
    {
        s_tBcmAlarm.ucDcrFaultPrt = FAULT;
        s_tBcmAlarmReal.ucDcrFaultPrt = FAULT;
    }

    rt_memset_s( &s_tCellDamageSave, sizeof(T_NewBattSaveStruct), 0, sizeof(T_NewBattSaveStruct));
    s_tAlarmData.wDischgCurrHighPrtCntMax   = OVERCURR_RERTY_DELAY;
    s_tAlarmData.wBduBusVoltLowPrtCntMax    = OVERCURR_RERTY_DELAY;
    s_tAlarmData.wBattShortCutCntMax        = OVERCURR_RERTY_DELAY;
    s_tAlarmData.wChgCurrHighPrtCntMax      = OVERCURR_RERTY_DELAY;
    s_tAlarmData.wCurrLimitInvalidCntMax    = OVERCURR_RERTY_DELAY;

    s_bBalanceFault = False;  //均衡电路发生故障后，重启才能恢复   
    ReadDefenceStatus();//重启时读取保存的布防状态
    return;
}

/***************************************************************************
 * @brief    初始化保存的防盗类告警
 **************************************************************************/
static void InitSavedTheftAlarm(void)
{
    BYTE aucTheftAlm[3] = {0};
    BYTE aucTheftType[3] = {BATT_THEFT, SITE_THEFT, NET_THEFT};
    BYTE i = 0;

    for (i = 0; i < 3; i++)
    {
        aucTheftAlm[i] = getResetData(aucTheftType[i]);

        if (aucTheftAlm[i] == FAULT)
        {
            SetTheftAlm(FAULT, aucTheftType[i]);
        }
        else
        {
            SetTheftAlm(NORMAL, aucTheftType[i]);
        }
    }

    return;
}

/******************************************************************************
**函 数 名：BOOLEAN JudgeAnalogStatus(  T_AlmInfoStruct *tAlmInfo  )
**输    入：无
**输    出：无
**调用模块：
**功能描述：根据模拟量判断告警状态
* 作    者：刘斌
**时    间：2012-6-28
**修改记录：
* 日    期      版  本      修改人      修改摘要      
******************************************************************************/ 
static BOOLEAN JudgeAnalogStatus( T_AlmInfoStruct *tAlmInfo )
{
    T_AlmInfoStruct *pAlmInfo;
    FLOAT fDelta = 0;
    BOOLEAN   bStatus = NORMAL, bTempStatus=NORMAL;
    INT32S  slRet = 0;

    if ( tAlmInfo == NULL )//模拟量告警给不出修改
    {
        return FAILURE;
    }   

    pAlmInfo = (T_AlmInfoStruct *)tAlmInfo;

    if (*(tAlmInfo->pucRealAlarm) != NORMAL )  
    {
        fDelta = tAlmInfo->fValueAdd;
    }
    else 
    {
        fDelta = 0;
    }

    if (NULL != tAlmInfo->pExceptionHandling)
    {
        slRet = tAlmInfo->pExceptionHandling(tAlmInfo, &bTempStatus);
    }

    if(slRet < 0)
    {
        return FAILURE;
    }
    else if (RTN_ALM_STATUE == slRet)
    {
        bStatus = bTempStatus;
    }
    else
    {
        if (tAlmInfo->fValueAdd < 0)   //判断为高，回差为负
        {
            if (tAlmInfo->fValue > (tAlmInfo->fThread+fDelta))
            {
                bStatus = tAlmInfo->ucAlarmType;
            }
            else 
            {
                bStatus = NORMAL;
            }   
        }
        else
        {
            if ( tAlmInfo->fValue < (tAlmInfo->fThread+fDelta))
            {
                bStatus = tAlmInfo->ucAlarmType;
            }
            else 
            {
                bStatus = NORMAL;
            }   
        }   
    }
    ALarmCommon(pAlmInfo, bStatus);  //yyhpclint 

    return SUCCESSFUL;
}

/******************************************************************************
**函 数 名：BOOLEAN JudgeDigitalAlarm( BYTE ucJudgeTrueInPut,BOOLEAN bTrueFlag )
**输    入：无
**输    出：无
**调用模块：
**功能描述：根据是否开关状态，返回开关表示意义
* 作    者：刘斌
**时    间：2012-6-28
**修改记录：
* 日    期      版  本      修改人      修改摘要      
******************************************************************************/ 
Static BOOLEAN JudgeDigitalAlarm( T_AlmInfoStruct *tAlmInfo )
{
    BOOLEAN   bStatus = 0, bTempStatus, btemp;  
    INT32S  slRet = 0;
    
    if (tAlmInfo->ucState == NORMAL)
    {
        bStatus = NORMAL;  
    }
    else
    {
        bStatus = tAlmInfo->ucAlarmType;
    }

    if (NULL != tAlmInfo->pExceptionHandling)
    {
        slRet = tAlmInfo->pExceptionHandling(tAlmInfo, &bTempStatus);
    }

    if(slRet < 0)
    {
        return FAILURE;
    }
    
    if (RTN_ALM_STATUE == slRet)
    {
        bStatus = bTempStatus;
    }

    btemp = ALarmCommon(tAlmInfo, bStatus);  

    return (btemp);
}

/****************************************************************************
* 函数名称：JudgeAlarm
* 调    用：
* 被 调 用：
* 输入参数：无
* 返 回 值：无
* 功能描述：告警管理模块主函数，根据采样结果判断是否有告警产生或消除
            并保存历史数据
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要        
***************************************************************************/
void JudgeAlarm( void* parameter )
{   
    WORD  wRealAlarmTotal  = 0;    //实时告警总条数
    T_CtrlOutStruct tCtrl;
	Button_State u_tButSta = {0};

    InitAlarm();
    pre_thread_beat_f(THREAD_ALARM_MANAGE);
#ifdef UNITEST
    while (IsRunning())
#else
    while(1)
#endif			
    {
        thread_beat_go_on(THREAD_ALARM_MANAGE);
        if(GetApptestFlag())
        {
            rt_thread_delay(1000);
            continue;
        }
        rt_thread_delay(ALM_TASK_PERIOD);

        s_bMainAlarmChanged = False;
        s_bMainAlarmExist = False;   

        if(s_wBcmAlmStartUpTimer < ALARM_DELAY_COUNTER)    //重启后,先按照以前实时告警判断
        {
            s_wBcmAlmStartUpTimer++;
            s_bBcmAlmStartUpFlag = True;
        }
        else
        {
            s_bBcmAlmStartUpFlag = False;
        }
        JudgeBcmAlarm();
        //PrintNowStatus(ALM_JUDGE_FINISH);

        GetButtonState(&u_tButSta);

		// 更新布防状态
        UpdateDefenceStatus();

        GetCtrlOut( &tCtrl );   
        wRealAlarmTotal = CalRealAlarmTotal(EXTERNAL_ALARM, NULL);   //  计算实时告警总条数
        //若没有实时告警，关闭声/光告警，保留
        if ( 0 == wRealAlarmTotal || IsSleep() ) 
        {
            s_tAlarmData.bBuzze = False;
        }
        else
        {
            //重要告警存在且发生变化或告警级别变化才打开蜂鸣器modified by xueb
            if ( (g_ucAlmLevelChge == True) || (s_bMainAlarmChanged == True) )
            {
                g_ucAlmLevelChge = False;           
            }

            s_tAlarmData.bBuzze = IfSysInvalid();	//系统异常告警发生才响蜂鸣器 Added by fengfj, 2019-09-07 21:01:37
        }
        if(tCtrl.bCtrlFlag == 0x01) //QTP蜂鸣器控制
        {
        }
        else if (!s_tAlarmPara.bBuzzEnable)
        {
            tCtrl.bBuzz = 0;
        }
        else if (IsExistLostAlarm(&s_tBcmAlarm) == True)
        {
            tCtrl.bBuzz = 2;
        }
        else if (u_tButSta.ucCurrentMode == 0 && u_tButSta.ucLed_CurrentMode == 0) //正常模式
        {
            tCtrl.bBuzz =  s_tAlarmData.bBuzze;
        }
        SetCtrlOut( &tCtrl );       
        SetCtrlOutRelay();
        
        //若重要告警变化，保存历史数据
        if(s_bMainAlarmChanged == True)
        {
            if ( s_bMainAlarmExist == True )
            {//告警产生保存2条历史数据
                SaveTempHisData();
            }
        }
    }
}

/***************************************************************************
 * @brief    写入布防状态
 * @return   SUCCESSFUL-写入成功 FAILURE-写入失败
 **************************************************************************/

BOOLEAN WriteDefenceStatus(BOOLEAN bDefenceStatus){
    UINT32 len = 0;
    if(GPIO_OFF == GetPMOSStatus())
    {
        return FAILURE;
    }
    if(bDefenceStatus == True){
        SaveAction(GetActionId(CONTOL_DEFENCE_STATUS_CHANGE), "Normal->Defence");
    }else{
        SaveAction(GetActionId(CONTOL_DEFENCE_STATUS_CHANGE), "Defence->Normal");
    }
    
    s_tDefenceInfo.wDefenceStatus = bDefenceStatus;
    s_tDefenceInfo.wCheckSum = CRC_Cal((BYTE*)&s_tDefenceInfo, (offsetof(T_DefenceInfoStruct,wCheckSum)));
    len = writeFile(FILE_NAME_DEFENCEINFO, (BYTE*)&s_tDefenceInfo, sizeof(T_DefenceInfoStruct));

    SaveGyroInfo(); // 保存当前电池位置信息作为位置基准

    if (len == 0) {
        // handle error, for example, log the error
        return FAILURE;
    }
    
    return SUCCESSFUL;
}


static BYTE checkTempAlmGroup(BYTE *p, T_BCMAlarmStruct *pBase)
{
    if (((p >= &pBase->aucChgTempHighAlm[0]) && (p <= &pBase->aucChgTempHighAlm[15])) ||
        ((p >= &pBase->aucChgTempLowAlm[0]) && (p <= &pBase->aucChgTempLowAlm[15])) ||
        ((p >= &pBase->aucDischgTempHighAlm[0]) && (p <= &pBase->aucDischgTempHighAlm[15])) ||
        ((p >= &pBase->aucDischgTempLowAlm[0]) && (p <= &pBase->aucDischgTempLowAlm[15])) ||
        ((p >= &pBase->ucCellTempSensorInvalidAlm[0]) && (p <= &pBase->ucCellTempSensorInvalidAlm[15])))
    {
        return CELL_GROUP_TEMP_NUM_MAX; // CELL_TEMP_NUM_MAX
    }
    return CELL_GROUP_NOT_FOUND;
}

static BYTE checkTempPrtGroup(BYTE *p, T_BCMAlarmStruct *pBase)
{
    if (((p >= &pBase->aucChgTempHighPrt[0]) && (p <= &pBase->aucChgTempHighPrt[15])) ||
        ((p >= &pBase->aucChgTempLowPrt[0]) && (p <= &pBase->aucChgTempLowPrt[15])) ||
        ((p >= &pBase->aucDischgTempHighPrt[0]) && (p <= &pBase->aucDischgTempHighPrt[15])) ||
        ((p >= &pBase->aucDischgTempLowPrt[0]) && (p <= &pBase->aucDischgTempLowPrt[15])))
    {
        return CELL_GROUP_TEMP_NUM_MAX; // CELL_TEMP_NUM_MAX
    }
    return CELL_GROUP_NOT_FOUND;
}

static BYTE checkCellAlmGroup(BYTE *p, T_BCMAlarmStruct *pBase)
{
    if (((p >= &pBase->aucCellUnderVoltAlm[0]) && (p <= &pBase->aucCellUnderVoltAlm[15])) ||
        ((p >= &pBase->aucCellOverVoltAlm[0]) && (p <= &pBase->aucCellOverVoltAlm[15])))
    {
        return CELL_GROUP_VOLT_NUM_MAX; // CELL_VOL_NUM_MAX
    }

    return CELL_GROUP_NOT_FOUND;
}

static BYTE checkCellPrtGroup(BYTE *p, T_BCMAlarmStruct *pBase)
{
    if (((p >= &pBase->aucCellUnderVoltPrt[0]) && (p <= &pBase->aucCellUnderVoltPrt[15])) ||
        ((p >= &pBase->aucCellOverVoltPrt[0]) && (p <= &pBase->aucCellOverVoltPrt[15])) ||
        ((p >= &pBase->aucCellDamagePrt[0]) && (p <= &pBase->aucCellDamagePrt[15])) ||
        ((p >= &pBase->aucCellDynamicUnderVoltPrt[0]) && (p <= &pBase->aucCellDynamicUnderVoltPrt[15])))
    {
        return CELL_GROUP_VOLT_NUM_MAX; // CELL_VOL_NUM_MAX
    }

    return CELL_GROUP_NOT_FOUND;
}

static BYTE checkAlmGroup(BYTE *p, T_BCMAlarmStruct *pBase)
{
    BYTE ucRet = 0;
    if(checkTempAlmGroup(p, pBase))
    {
        ucRet = checkTempAlmGroup(p, pBase);
    }
    else if(checkTempPrtGroup(p, pBase))
    {
        ucRet = checkTempPrtGroup(p, pBase);
    }
    else if(checkCellAlmGroup(p, pBase))
    {
        ucRet = checkCellAlmGroup(p, pBase);
    }
    else if(checkCellPrtGroup(p, pBase))
    {
        ucRet = checkCellPrtGroup(p, pBase);
    }
    return ucRet;
}

/******************************************************************************
**函 数 SetAlarmJudgeCounter()
**输    入： 
**输    出：无
**调用模块：
**功能描述：设定告警判断次数
* 作    者：
**时    间：
**修改记录：
* 日    期      版  本      修改人      修改摘要      
******************************************************************************/

static void SetAlarmJudgeCounter(void)
{
    BYTE* p = (BYTE*)&s_tBcmAlarmCounterMax;
    size_t i = 0;

    for (i=0; i<sizeof(s_tBcmAlarmCounterMax); i++)
    {
        *p++ = ALARM_COUNTER;
    }

    for (i=0; i<s_tHardwarePara.ucCellVoltNum; i++)
    {
        s_tBcmAlarmCounterMax.aucCellOverVoltPrt[i] = 3;    //单体过压保护判断时间减少为1s
        s_tBcmAlarmCounterMax.aucCellDamagePrt[i] = 3;
    }
	s_tBcmAlarmCounterMax.ucBDUBattVoltLowPrt = 20;
	s_tBcmAlarmCounterMax.ucBattShortCut 		= SHORT_CUT_ALM_COUNTER;
    s_tBcmAlarmCounterMax.ucBDUBusVoltLowPrt 	= SHORT_CUT_ALM_COUNTER;    
    s_tBcmAlarmCounterMax.ucDischgCurrHighPrt 	= SHORT_CUT_ALM_COUNTER;
    s_tBcmAlarmCounterMax.ucActivePortCurrError = 3;         //激活回路异常告警事件为1s
    s_tBcmAlarmCounterMax.ucBDUCommFail = BDUCOMMFAIL_ALM_COUNTER;   //BDU通信断为15s
    s_tBcmAlarmCounterMax.ucBattCellTempInvalidAlm = 1;     //电池单体温度无效告警
    s_tBcmAlarmCounterMax.ucBattCellDamagePrt = 1;      //  电池单体损坏保护
}

Static void fillAlarmShield( void )
{
    BYTE i, j, ucRet=0;
    BYTE *p;

    //获取系统参数
    GetSysPara( &s_tSysPara );
    p = ( BYTE * )&s_tBcmAlarmShield;
    for( i=0; i<ALARM_CLASS; i++ )
    {
        ucRet = checkAlmGroup(p, &s_tBcmAlarmShield);
        if (1 == ucRet)
        {
            for( j=0; j<CELL_VOL_NUM_MAX; j++ )
            {
                *p = s_tSysPara.aucAlarmLevel[i];
                p = p+1;
            }
        }
        else if (2 == ucRet)
        {
            for( j=0; j<CELL_TEMP_NUM_MAX; j++ )
            {
                *p = s_tSysPara.aucAlarmLevel[i];
                p = p+1;
            }
        }
        else
        {
            *p = s_tSysPara.aucAlarmLevel[i];
            p = p+1;
        }
    }
    
    return;
}

Static BOOLEAN IsCellVoltTooHigh(T_StateListenStruct* ptState, BYTE i)
{
#define CELL_VOLT_HIGH_THRESHOLD (4.2f)
    FLOAT fDelta = 0.0;
    
    if(i >= s_tHardwarePara.ucCellVoltNum || s_tAlarmData.fBattVolt < GetBattVoltThresh())
    {
        return False;
    }

    if(ptState->bState)
    {
        fDelta = -0.05f;
    }

    return s_tAlarmData.afCellVolt[i]>(CELL_VOLT_HIGH_THRESHOLD + fDelta);
}

Static BOOLEAN IsCellVoltPoor(T_StateListenStruct* ptState, BYTE ucIndex)
{
    if(ucIndex >= s_tHardwarePara.ucCellVoltNum || s_tAlarmData.fBattVolt < GetBattVoltThresh())
    {
        return False;
    }

    return ((s_tAlarmData.fCellVoltMin <= 2.7f) && s_tAlarmData.fCellVoltMax >= 3.3f);
}

void CheckCellDamegeFlag(void)
{
    size_t i=0;
    size_t j=0;
    FLOAT fCellVolt[CELL_VOL_NUM_MAX] = {0.0,};
    FLOAT fMedianCellVolt = 0.0; // 单体电压中位数
    FLOAT fMaxCellVoltDiff = 0.0; // 最大中位数压差

    if (s_tHardwarePara.ucCellVoltNum <1 || s_tHardwarePara.ucCellVoltNum > 16)
    {
        return;
    }

    for(j = 0; j < s_tHardwarePara.ucCellVoltNum; j++)
    {
        fCellVolt[j] = s_tAlarmData.afCellVolt[j];
    }
    BubbleSort(fCellVolt, s_tHardwarePara.ucCellVoltNum);

    if(s_tHardwarePara.ucCellVoltNum % 2 == 0)
    {
        fMedianCellVolt = (fCellVolt[s_tHardwarePara.ucCellVoltNum/2 - 1] + fCellVolt[s_tHardwarePara.ucCellVoltNum/2])/2.0;
    }
    else
    {
        fMedianCellVolt = fCellVolt[(s_tHardwarePara.ucCellVoltNum - 1)/2];
    }
            
    fMaxCellVoltDiff = MAX(fabs(fCellVolt[s_tHardwarePara.ucCellVoltNum - 1] - fMedianCellVolt), fabs(fCellVolt[0] - fMedianCellVolt));

    for(i = 0; i < s_tHardwarePara.ucCellVoltNum; i++)
    {
        InitListener(&s_atCellVoltTooHigh[i], IsCellVoltTooHigh, 2);  //(2 + 3) 持续2s
        InitListener(&s_atCellVoltPoor[i], IsCellVoltPoor, 11);  //(11 + 3)持续5s

        if (RunListener(&s_atCellVoltTooHigh[i], i) && FAULT != s_tCellDamageSave.abCellDamagePrt[i])
        {
            s_tCellDamageSave.abCellDamagePrt[i] = FAULT;
        }

        if (RunListener(&s_atCellVoltPoor[i], i) && FAULT != s_tCellDamageSave.abCellDamagePrt[i] && IsCellVoltPoor(&s_atCellVoltPoor[i], i))
        {// RunListener在条件恢复时仍需要计数才能恢复
            if(fabs(fabs(s_tAlarmData.afCellVolt[i] - fMedianCellVolt) - fMaxCellVoltDiff) < 1e-6)
            {
                s_tCellDamageSave.abCellDamagePrt[i] = FAULT;
            }
        }
    }

    return;
}

static void GetAlarmInfo(void)
{
    size_t i=0;
    T_BCMDataStruct tBcmData = {0, };
    T_BattResult tBattOut = {0, };

    //------------para-------------------
    GetRealData(&tBcmData);
    GetSysPara(&s_tSysPara);
    GetBattResult(&tBattOut);

    for(i = 0; i < s_tHardwarePara.ucCellTempNum; i++)
    {
        s_tAlarmData.afCellTemp[i] = tBcmData.afCellTemp[i];
    }

    for(i = 0; i < s_tHardwarePara.ucCellVoltNum; i++)
    {
        s_tAlarmData.afCellVolt[i] = tBcmData.afCellVolt[i];
    }
    s_tAlarmData.fBattVolt = tBcmData.fBattVolt;
    s_tAlarmData.fCellVoltMin = tBcmData.fCellVoltMin;
    s_tAlarmData.fCellVoltMax = tBcmData.fCellVoltMax;
    s_tAlarmData.fBattCurr = tBcmData.fBattCurr;
    if (s_tSysPara.wBatteryCap)
    {
        s_tAlarmData.fCurrCoef = fabs(tBcmData.fBattCurr)/((FLOAT)s_tSysPara.wBatteryCap*g_ProConfig.fCapTransRate);
    }
    else
    {
        s_tAlarmData.fCurrCoef = fabs(tBcmData.fBattCurr)/100.0;
    }
    
    SetAlarmJudgeCounter();

    //------------para-------------------
    GetSysPara(&s_tSysPara);
    s_tAlarmPara.bBuzzEnable = s_tSysPara.bBuzzerEnable;
    s_tAlarmData.fCurrMinDet = MIN_CURR_DET_BDCU;
    s_tAlarmData.bLoopOff = tBattOut.bLoopOff;
    s_tAlarmData.ucBDUBattLockAlm = s_tBcmAlarmTmp.ucBDUBattLockAlm;
    s_tAlarmData.ucBDUConnTempHighPrt = s_tBcmAlarmTmp.ucBDUConnTempHighPrt;
    s_tAlarmData.ucHeaterFilmFailure = s_tBcmAlarmTmp.ucHeaterFilmFailure;
    s_tAlarmData.bDcrFaultAlm = tBattOut.bBattDcrFaultAlmSta;
    s_tAlarmData.bDcrFaultPrt = tBattOut.bBattDcrFaultPrtSta;
    s_tAlarmData.ucBalanceResisTempHighPrt = s_tBcmAlarmTmp.ucBalanceResisTempHighPrt;
    //s_tAlarmData.ucBDUBattLockAlm = (tBcmData.ucBduStatus == BDU_STATUS_FAILURE)? 1:0;
    tBcmData.bNetAntiTheftStatus = GetNetAntiTheftStatus();
    rt_memcpy_s(tBcmData.ucNetAntitheftKey, NETANTITHEFT_KEY_LEN, s_tNetAntiTheft.aucNetAntiTheftKey, NETANTITHEFT_KEY_LEN);
    NetAntiTheftKeySha256(&tBcmData);
    tBcmData.bNetAntiKeyMatchStatus = s_bNetAntiKeyMatchStatus;
    SetRealData(&tBcmData);
    CheckCellDamegeFlag();
    return;
}

static INT32S CellDamageHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus)
{
    BOOLEAN  bStatus = NORMAL;

    BYTE ucIndex = 0;
    FLOAT fDelta1 = 0.0f;

    if (NULL == pAlmInfo || NULL == pbTmpStatus)
    {
        return FAILURE;
    }
    ucIndex = pAlmInfo->ucReserve;
    if(NORMAL != *(pAlmInfo->pucRealAlarm))
    {
        fDelta1 = pAlmInfo->fValueAdd;

    }
    if(NORMAL != s_tCellDamageSave.abCellDamagePrt[ucIndex])
    {
        bStatus = FAULT;
    }

    if (s_tAlarmData.fBattVolt > GetBattVoltThresh() && s_tAlarmData.fCellVoltMax > 1.5f && pAlmInfo->fValue < (fDelta1 + pAlmInfo->fThread))//电池电压大于12V，单体最高电压大于1.5V，并且任意单节低于单体损坏阈值
    {
        bStatus = FAULT;
    }

    *pbTmpStatus = bStatus;
    return RTN_ALM_STATUE;
}

#ifdef ALARM_UPLOAD_ACTIVE
static BOOLEAN AlarmUploadActive(SHORT *pAlarmStrucOld, WORD uindex, BOOLEAN ucAlarmOccurFlag)
{
    WORD wTempOffSet;
    if (!IsMaster()) 
    {   
        wTempOffSet = pAlarmStrucOld[uindex] + 1;
        SendAlarmToMasterByCan1(wTempOffSet, ucAlarmOccurFlag);
    }
    else
    {
        wTempOffSet = pAlarmStrucOld[uindex] + 1;
        DirectSendAlarmToSCUByCan2(wTempOffSet, ucAlarmOccurFlag);
    }
    return True;
}
#endif

/******************************************************************************
* 函数名称：JudgeBcmAlarm
* 调    用：
* 被 调 用：
* 输入参数：无
* 返 回 值：有告警变化返回TRUE，否则返回FALSE
* 功能描述：根据采样结果判断整流模块告警
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要           
******************************************************************************/ 
Static BOOLEAN  JudgeBcmAlarm( void )
{
    WORD    i;
    BYTE    *pAlarm;
    BYTE    *pAlarmReal;
    BYTE    *pAlarmBak;
    BYTE    *pAlarmFlag;
    BYTE    *pAlarmCounter;
    BYTE    *pAlarmShield;
    BYTE    *pAlarmCounterMax;
    SHORT   *pAlarmStrucOld;
    WORD    wOffset;

    BOOLEAN bReturnValue = False;

    GetAlarmInfo();
    JudgeBcmAnalogAlarm();
    JudgeBcmDigitalAlarm();
    pAlarmStrucOld = CopyBCMAlarmStructOld();

    pAlarm              = ( BYTE * )&s_tBcmAlarm;
    pAlarmReal          = ( BYTE * )&s_tBcmAlarmReal;
    pAlarmBak           = ( BYTE * )&s_tBcmAlarmBak;
    pAlarmFlag          = ( BYTE * )&s_tBcmAlarmFlag;
    pAlarmCounter       = ( BYTE * )&s_tBcmAlarmCounter;
    pAlarmShield        = ( BYTE * )&s_tBcmAlarmShield;
    pAlarmCounterMax    = ( BYTE * )&s_tBcmAlarmCounterMax;

    fillAlarmShield();
    wOffset = COMMON_ALARM_NUM;
    
    //连续ALARM_COUNTER次判断结果一样且该项告警未屏蔽，置实时告警
    for( i=0; i<sizeof( s_tBcmAlarm ); i++ )
    {
        if ( pAlarmCounter[i] >= pAlarmCounterMax[i] )
        {
            pAlarmReal[i] = pAlarmFlag[i];
        }
        pAlarm[i] = ( pAlarmShield[i]==IGNORE )? NORMAL:pAlarmReal[i];
        
        if ( pAlarm[i] && (EMERGENCY==pAlarmShield[i]) )
        {
            s_bMainAlarmExist = True;    
        }        
    }
    JudgeBattAlarm();
    JudgeBcmAlarmPriority();
    
    for( i=0; i<sizeof( s_tBcmAlarm ); i++ )
    {
    //实时告警有变化
        if( pAlarm[i]!=pAlarmBak[i] )			
        {
            // 有重要告警变化
            if (EMERGENCY==pAlarmShield[i])
            {
                s_bMainAlarmChanged	= True;// 重要告警变化标志，保留，20110406，yang.an	
            }
            //g_bAutoSendAlarmFlag = True;
            //处理告警显示
            DealDisAlarm( i+wOffset, pAlarm[i] );  //处理实时、历史告警显示
            bReturnValue	= True;		//置函数返回值为TRUE
        #ifdef ALARM_UPLOAD_ACTIVE
            if (pAlarm[i] && ALM_FIRE_CONTROL == i)
            {
                AlarmUploadActive(pAlarmStrucOld, i, True);
            }
            else if (!pAlarm[i] && ALM_FIRE_CONTROL == i)
            {
                AlarmUploadActive(pAlarmStrucOld, i, False);
            }
            
        #endif
        }
    }

    if( True==bReturnValue )	
    {
        rt_memcpy( pAlarmBak, pAlarm, sizeof(s_tBcmAlarm) );
        SaveRealAlarm(&s_tRealAlmSave);    //保存实时告警
        //置有未读的告警量变化标志
        SetDataFlag( BCM_ALARM, 0x01 );		
    }

    s_bExistAlarmFlag = JudgeIfExixtAlarm(pAlarm,sizeof(s_tBcmAlarm));	
    return bReturnValue;
}


Static void JudgeBattAlarm(void)
{
    s_tBcmAlarm.ucBattCellTempInvalidAlm = NORMAL;
    s_tBcmAlarm.ucBattCellDamagePrt = NORMAL;
    for(int i = 0; i < s_tHardwarePara.ucCellTempNum; i++)
    {
        if(s_tBcmAlarmReal.ucCellTempSensorInvalidAlm[i] != NORMAL)
        {
            //电池单体温度无效
            if (s_tBcmAlarmShield.ucBattCellTempInvalidAlm != IGNORE)
            {
                s_tBcmAlarm.ucBattCellTempInvalidAlm = s_tBcmAlarmReal.ucCellTempSensorInvalidAlm[i];
                
                if (s_tBcmAlarm.ucBattCellTempInvalidAlm && (EMERGENCY == s_tBcmAlarmShield.ucBattCellTempInvalidAlm))
                {
                    s_bMainAlarmExist = True;
                }
            }
            break;
        }
    }

    for(int i = 0; i < s_tHardwarePara.ucCellTempNum; i++)
    {
        if(s_tBcmAlarmReal.aucCellDamagePrt[i] != NORMAL)
        {
            //电池单体损坏保护
            if (s_tBcmAlarmShield.ucBattCellDamagePrt != IGNORE)
            {
                s_tBcmAlarm.ucBattCellDamagePrt = s_tBcmAlarmReal.aucCellDamagePrt[i];
                
                if (s_tBcmAlarm.ucBattCellDamagePrt && (EMERGENCY == s_tBcmAlarmShield.ucBattCellDamagePrt))
                {
                    s_bMainAlarmExist = True;
                }
            }
            break;
        }
    }
}


static void JudgeCellTempAlarm( void )
{
    BYTE i;
    BYTE ucInvalidCellTempNum = 0;
    FLOAT  fCellTempMin, fCellTempMax;
    T_BCMDataStruct tBcmData = {0, };
    T_AlmInfoStruct tAlmInfo = {0, };
    T_BattResult tBattOut = {0, };

    GetBattResult(&tBattOut);
    GetRealData(&tBcmData);
    GetSysPara(&s_tSysPara);

    fCellTempMin = CELLTEMP_MAX;
    fCellTempMax = CELLTEMP_MIN;

    if (s_tHardwarePara.ucCellTempNum <1 || s_tHardwarePara.ucCellTempNum > CELL_TEMP_NUM_MAX)
    {//解决kw故障
        return;
    }

    for (i = 0; i < s_tHardwarePara.ucCellTempNum; i++)   //4路单体温度
    {
        // 找出温度正常的单体中最大和最小单体温度
        if (fCellTempMin > tBcmData.afCellTemp[i] && NORMAL == s_tBcmAlarm.ucCellTempSensorInvalidAlm[i])
        {
            fCellTempMin    = tBcmData.afCellTemp[i];
        }

        if (fCellTempMax < tBcmData.afCellTemp[i] && NORMAL == s_tBcmAlarm.ucCellTempSensorInvalidAlm[i])
        {
            fCellTempMax    = tBcmData.afCellTemp[i];
        }

        if(s_tBcmAlarm.ucCellTempSensorInvalidAlm[i] == FAULT)
        {
            ucInvalidCellTempNum++;
        }


        //  充电高温告警
        ANALOG_ALARM_INIT(tAlmInfo, aucChgTempHighAlm[i]);
        tAlmInfo.fValue = tBcmData.afCellTemp[i];
        tAlmInfo.fValueAdd = s_tSysPara.fChgTempHighAlmRecoThre - s_tSysPara.fChgTempHighAlmThre;//-3.0;
        tAlmInfo.fThread = s_tSysPara.fChgTempHighAlmThre;
        tAlmInfo.ucAlarmType = JudgeAlarmInfoType(BATT_MODE_CHARGE == tBattOut.ucBatStatus); //仅在充电时产生告警
        JudgeAnalogStatus(&tAlmInfo);
        
        //	充电高温保护
        ANALOG_ALARM_INIT(tAlmInfo, aucChgTempHighPrt[i]);
        tAlmInfo.fValue = tBcmData.afCellTemp[i];
        tAlmInfo.fValueAdd = s_tSysPara.fChgTempHighPrtRecoThre - s_tSysPara.fChgTempHighPrtThre;//-3.0;
        tAlmInfo.fThread = s_tSysPara.fChgTempHighPrtThre;
    	// Added by fengfj, 2020-09-28 22:40:41
        tAlmInfo.ucAlarmType = JudgeAlarmInfoType(BATT_MODE_CHARGE == tBattOut.ucBatStatus); //仅在充电时产生告警
        JudgeAnalogStatus(&tAlmInfo); 

        //   充电低温告警
        ANALOG_ALARM_INIT(tAlmInfo, aucChgTempLowAlm[i]);
        tAlmInfo.fValue = tBcmData.afCellTemp[i];
        tAlmInfo.fValueAdd = s_tSysPara.fChgTempLowAlmRecoThre - s_tSysPara.fChgTempLowAlmThre;//3.0;
        tAlmInfo.fThread = s_tSysPara.fChgTempLowAlmThre;
        tAlmInfo.ucAlarmType = JudgeAlarmInfoType(BATT_MODE_CHARGE == tBattOut.ucBatStatus);  //仅在充电时产生告警
        JudgeAnalogStatus(&tAlmInfo);
        
        //	充电低温保护
        ANALOG_ALARM_INIT(tAlmInfo, aucChgTempLowPrt[i]);
        tAlmInfo.fValue = tBcmData.afCellTemp[i];
        tAlmInfo.fValueAdd = s_tSysPara.fChgTempLowPrtRecoThre - s_tSysPara.fChgTempLowPrtThre;//3.0;
        tAlmInfo.fThread = s_tSysPara.fChgTempLowPrtThre;
        // Added by fengfj, 2020-09-28 22:41:06
        tAlmInfo.ucAlarmType = JudgeAlarmInfoType(BATT_MODE_CHARGE == tBattOut.ucBatStatus); //仅在充电时产生告警	
        JudgeAnalogStatus(&tAlmInfo);    
        
        //  放电高温告警
        ANALOG_ALARM_INIT(tAlmInfo, aucDischgTempHighAlm[i]);
        tAlmInfo.fValue = tBcmData.afCellTemp[i];
        tAlmInfo.fValueAdd = s_tSysPara.fDischgTempHighAlmRecoThre - s_tSysPara.fDischgTempHighAlmThre;//-3.0;
        tAlmInfo.fThread = s_tSysPara.fDischgTempHighAlmThre;
        tAlmInfo.ucAlarmType = JudgeAlarmInfoType(BATT_MODE_DISCHARGE == tBattOut.ucBatStatus);  //仅在放电时产生告警
        JudgeAnalogStatus(&tAlmInfo); 

        //  放电高温保护
        ANALOG_ALARM_INIT(tAlmInfo, aucDischgTempHighPrt[i]);
        tAlmInfo.fValue = tBcmData.afCellTemp[i];
        tAlmInfo.fValueAdd = s_tSysPara.fDischgTempHighPrtRecoThre - s_tSysPara.fDischgTempHighPrtThre;//-3.0;
        tAlmInfo.fThread = s_tSysPara.fDischgTempHighPrtThre;
    	// Added by fengfj, 2020-09-28 22:41:45
        tAlmInfo.ucAlarmType = JudgeAlarmInfoType(BATT_MODE_DISCHARGE == tBattOut.ucBatStatus); //仅在放电时产生告警
        JudgeAnalogStatus(&tAlmInfo); 

        //  放电低温告警
        ANALOG_ALARM_INIT(tAlmInfo, aucDischgTempLowAlm[i]);
        tAlmInfo.fValue = tBcmData.afCellTemp[i];
        tAlmInfo.fValueAdd = s_tSysPara.fDischgTempLowAlmRecoThre - s_tSysPara.fDischgTempLowAlmThre;//3.0;
        tAlmInfo.fThread = s_tSysPara.fDischgTempLowAlmThre;
        tAlmInfo.ucAlarmType = JudgeAlarmInfoType(BATT_MODE_DISCHARGE == tBattOut.ucBatStatus);  //仅在放电时产生告警
        JudgeAnalogStatus(&tAlmInfo); 

        //  放电低温保护
        ANALOG_ALARM_INIT(tAlmInfo, aucDischgTempLowPrt[i]);
        tAlmInfo.fValue = tBcmData.afCellTemp[i];
        tAlmInfo.fValueAdd = s_tSysPara.fDischgTempLowPrtRecoThre - s_tSysPara.fDischgTempLowPrtThre;//3.0;
        tAlmInfo.fThread = s_tSysPara.fDischgTempLowPrtThre;
    	// Added by fengfj, 2020-09-28 22:42:05
        tAlmInfo.ucAlarmType = JudgeAlarmInfoType(BATT_MODE_DISCHARGE == tBattOut.ucBatStatus); //仅在放电时产生告警
        JudgeAnalogStatus(&tAlmInfo); 
    }
    // 至少两个单体温度正常才进行单体温度异常判断
    if(ucInvalidCellTempNum < 3)
    {
        //  电芯温度异常告警
        ANALOG_ALARM_INIT(tAlmInfo, ucCellTempAbnormal);
        tAlmInfo.fValue = fCellTempMax - fCellTempMin;
        tAlmInfo.fValueAdd = -2.0;
        tAlmInfo.fThread = 10.0;
        JudgeAnalogStatus(&tAlmInfo);
    }
    else
    {
        ////避免无法恢复
        CLEAR_ALAEM(ucCellTempAbnormal);
    }

    //电池异常温度高保护告警
    if (!GetQtptestFlag())
    {
        ANALOG_ALARM_INIT(tAlmInfo, ucBattFaultTempHighAlm);
        tAlmInfo.fValue = fCellTempMax;
        tAlmInfo.fValueAdd = -3.0;
        tAlmInfo.fThread = s_tSysPara.fBattFaultTempHighPrtThre;
        JudgeAnalogStatus(&tAlmInfo);
    }

    return;
}
/******************************************************************************
* 函数名称：JudgeBcmAnalogAlarm()
* 调    用：
* 被 调 用：
* 输入参数：无
* 返 回 值：无
* 功能描述：根据模拟量采样结果判断是否有告警产生或消除     
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要        
******************************************************************************/ 
static void JudgeBcmAnalogAlarm( void )
{
    BYTE i;
    FLOAT fCellVolMax = 0.0, fCellVolMin = 0.0, fCellTempMin = 0.0, fCellAvgTemp = 0.0, fCellVoltLowPrt = 0.0;
    T_BCMDataStruct tBcmData;
    T_AlmInfoStruct tAlmInfo;

    GetRealData(&tBcmData );
    GetSysPara( &s_tSysPara );

    fCellVolMax = tBcmData.fCellVoltMax;
    fCellVolMin = tBcmData.fCellVoltMin;
    fCellTempMin = tBcmData.fCellTempMin;

    //  电池组过压告警
    ANALOG_ALARM_INIT(tAlmInfo, ucBattOverVoltAlm);
    tAlmInfo.fValue = tBcmData.fBattVolt;
    tAlmInfo.fValueAdd = s_tSysPara.fBattOverVoltAlmRecoThre/g_ProConfig.fVoltTrasRate - s_tSysPara.fBattOverVoltAlmThre/g_ProConfig.fVoltTrasRate; //54-3.75;
    tAlmInfo.fThread = s_tSysPara.fBattOverVoltAlmThre/g_ProConfig.fVoltTrasRate;
    JudgeAnalogStatus(&tAlmInfo);
    
    //  放电过流告警
    ANALOG_ALARM_INIT(tAlmInfo, ucDischgCurrHighAlm);
    tAlmInfo.fValue = tBcmData.fBusCurr;
    tAlmInfo.fValueAdd = 2.0;
    tAlmInfo.fThread = 0.0 - s_tSysPara.fDischgCurrHighAlmThre*s_tSysPara.wBatteryCap;
    JudgeAnalogStatus(&tAlmInfo);

    //  充电过流告警
    ANALOG_ALARM_INIT(tAlmInfo, ucChgCurrHighAlm);
    tAlmInfo.fValue = tBcmData.fBusCurr;
    tAlmInfo.fValueAdd = -2.0;
    tAlmInfo.fThread = s_tSysPara.fChgCurrHighAlmThre*s_tSysPara.wBatteryCap;
    JudgeAnalogStatus(&tAlmInfo);

    //  单板过温告警 
    ANALOG_ALARM_INIT(tAlmInfo, ucBoardTempHighAlm);
    tAlmInfo.fValue = tBcmData.fBoardTemp;
    tAlmInfo.fValueAdd = s_tSysPara.fBoardTempHighAlmRecoThre - s_tSysPara.fBoardTempHighcThre;//-17.0;
    tAlmInfo.fThread = s_tSysPara.fBoardTempHighcThre;
    JudgeAnalogStatus(&tAlmInfo);

    //  单板过温保护
#ifdef BOARD_Temp_High_Prt_BDPB
    ANALOG_ALARM_INIT(tAlmInfo, ucBoardTempHighPrt);
    tAlmInfo.fValue = tBcmData.fBoardTemp;
    tAlmInfo.fValueAdd = s_tSysPara.fBoardTempHighPrtRecoThre - s_tSysPara.fBoardTempHighPrtThre;//-17.0;
    tAlmInfo.fThread = s_tSysPara.fBoardTempHighPrtThre;
    tAlmInfo.pExceptionHandling = BoardTempHighPrtHandle;
    JudgeAnalogStatus(&tAlmInfo);
#endif

    //  环境温度高告警
    ANALOG_ALARM_INIT(tAlmInfo, ucEnvTempHighAlm);
    tAlmInfo.fValue = tBcmData.fEnvTemp;
    tAlmInfo.fValueAdd = s_tSysPara.fEnvTempHighAlmRecoThre - s_tSysPara.fEnvTempHighAlmThre;//-5.0;
    tAlmInfo.fThread = s_tSysPara.fEnvTempHighAlmThre;
    JudgeAnalogStatus(&tAlmInfo);

    //  环境温度低告警
    ANALOG_ALARM_INIT(tAlmInfo, ucEnvTempLowAlm);
    tAlmInfo.fValue = tBcmData.fEnvTemp;
    tAlmInfo.fValueAdd = s_tSysPara.fEnvTempLowAlmRecoThre - s_tSysPara.fEnvTempLowAlmThre;//5.0;
    tAlmInfo.fThread = s_tSysPara.fEnvTempLowAlmThre;
    JudgeAnalogStatus(&tAlmInfo);

    //  环境温度高保护
    ANALOG_ALARM_INIT(tAlmInfo, ucEnvTempHighPrt);
    tAlmInfo.fValue = tBcmData.fEnvTemp;
    tAlmInfo.fValueAdd = s_tSysPara.fEnvTempHighPrtRecoThre - s_tSysPara.fEnvTempHighPrtThre;//-5.0;
    tAlmInfo.fThread = s_tSysPara.fEnvTempHighPrtThre;
    JudgeAnalogStatus(&tAlmInfo);

    //  环境温度低保护
    ANALOG_ALARM_INIT(tAlmInfo, ucEnvTempLowPrt);
    tAlmInfo.fValue = tBcmData.fEnvTemp;
    tAlmInfo.fValueAdd = s_tSysPara.fEnvTempLowPrtRecoThre - s_tSysPara.fEnvTempLowPrtThre;//5.0;
    tAlmInfo.fThread = s_tSysPara.fEnvTempLowPrtThre;
    JudgeAnalogStatus(&tAlmInfo);
    //  电池组欠压保护
    if(!IsSleep())//休眠条件下不判断电池组欠压保护
    {
        ANALOG_ALARM_INIT(tAlmInfo, ucBattUnderVoltPrt);
        tAlmInfo.fValue = tBcmData.fBattVolt;
        if(GetQtptestFlag())   //QTP模式，电池组欠压保护阈值为40V
        {
            s_tSysPara.fBattUnderVoltPrtThre = OUT_ASSIST_VOLT_MIN/15.0*s_tHardwarePara.ucCellVoltNum*g_ProConfig.fVoltTrasRate;
        }

        if (s_tSysPara.bUVPTempCompensationEn)
        {
            for(i=0; i<s_tHardwarePara.ucCellTempNum; i++)
            {
                fCellAvgTemp += tBcmData.afCellTemp[i];
            }
            fCellAvgTemp = fCellAvgTemp / s_tHardwarePara.ucCellTempNum;

            if(fCellAvgTemp < 0)
            {
                tAlmInfo.fThread = MAX(s_tSysPara.fBattUnderVoltPrtThre/g_ProConfig.fVoltTrasRate - 3/15.0*s_tHardwarePara.ucCellVoltNum, 40.0f/15.0*s_tHardwarePara.ucCellVoltNum); //温度小于0则按照欠压保护阈值-3处理
            }
            else
            {
                tAlmInfo.fThread = s_tSysPara.fBattUnderVoltPrtThre/g_ProConfig.fVoltTrasRate;
            }
        }
        else
        {
            tAlmInfo.fThread = s_tSysPara.fBattUnderVoltPrtThre/g_ProConfig.fVoltTrasRate;
        }
        tAlmInfo.fValueAdd = s_tHardwarePara.ucCellVoltNum * 3.4- tAlmInfo.fThread;  //MIN(51.0 - s_tSysPara.fBattUnderVoltPrtThre, 8.0);
        tAlmInfo.pExceptionHandling = BattVoltLowPrtHandle;
        JudgeAnalogStatus(&tAlmInfo);
    }


    //  电池组欠压告警
    ANALOG_ALARM_INIT(tAlmInfo, ucBattUnderVoltAlm);
    tAlmInfo.fValue = tBcmData.fBattVolt;
    //tAlmInfo.fValueAdd = MIN( 4.5/15.0*s_tHardwarePara.ucCellVoltNum, s_tHardwarePara.ucCellVoltNum * 3.4 - s_tSysPara.fBattUnderVoltAlmThre/g_ProConfig.fVoltTrasRate );
    tAlmInfo.fThread = s_tSysPara.fBattUnderVoltAlmThre/g_ProConfig.fVoltTrasRate;
    tAlmInfo.ucAlarmType = JudgeAlarmInfoType(GetQtptestFlag() == False); //仅在非QTP模式下产生告警
    tAlmInfo.pExceptionHandling = BattUnderVoltALarmHandle;
    JudgeAnalogStatus(&tAlmInfo);
    
    //  电池SOC低告警
    ANALOG_ALARM_INIT(tAlmInfo, ucBattSOCLowAlm);
    tAlmInfo.fValue = tBcmData.wBattSOC;
    tAlmInfo.fValueAdd = 2.0;
    tAlmInfo.fThread = s_tSysPara.wBattSOCLowAlmThre;
    tAlmInfo.ucAlarmType = JudgeAlarmInfoType(GetQtptestFlag() == False); //仅在非QTP模式下产生告警
    JudgeAnalogStatus(&tAlmInfo);
    
    //  电池SOH告警
    ANALOG_ALARM_INIT(tAlmInfo, ucBattSOHAlm);
    tAlmInfo.fValue = tBcmData.wBattSOH;
    tAlmInfo.fValueAdd = 10.0;
    tAlmInfo.fThread = s_tSysPara.wBattSOHAlmThre;
    JudgeAnalogStatus(&tAlmInfo);

    //  电池SOC低保护
    if(!GetQtptestFlag()) //QTP模式下屏蔽电池SOC低保护
    {
        ANALOG_ALARM_INIT(tAlmInfo, ucBattSOCLowPrt);
        tAlmInfo.fValue = tBcmData.wBattSOC;
        tAlmInfo.fValueAdd = 1.0;
        tAlmInfo.fThread = s_tSysPara.wBattSOCLowPrtThre;
        JudgeAnalogStatus(&tAlmInfo);
    }
    else
    {
        ANALOG_ALARM_INIT(tAlmInfo, ucBattSOCLowPrt);
        ALarmCommon(&tAlmInfo, NORMAL);
    }
    
    //  电池SOH保护
    ANALOG_ALARM_INIT(tAlmInfo, ucBattSOHPrt);
    tAlmInfo.fValue = tBcmData.wBattSOH;
    tAlmInfo.fValueAdd = 10.0;
    tAlmInfo.fThread = s_tSysPara.wBattSOHPrtThre;
    JudgeAnalogStatus(&tAlmInfo);
    for (i = 0; i < s_tHardwarePara.ucCellVoltNum; i++)
    {
        //  电芯过压告警
        ANALOG_ALARM_INIT(tAlmInfo, aucCellOverVoltAlm[i]);
        tAlmInfo.fValue = tBcmData.afCellVolt[i];
        tAlmInfo.fValueAdd = s_tSysPara.fCellOverVoltAlmRecoThre - s_tSysPara.fCellOverVoltAlmThre;//-0.15;
        tAlmInfo.fThread = s_tSysPara.fCellOverVoltAlmThre;
        JudgeAnalogStatus(&tAlmInfo);

        //  电芯过压保护
        ANALOG_ALARM_INIT(tAlmInfo, aucCellOverVoltPrt[i]);
        tAlmInfo.fValue = tBcmData.afCellVolt[i];
        tAlmInfo.fValueAdd = s_tSysPara.fCellOverVoltPrtRecoThre - s_tSysPara.fCellOverVoltPrtThre;//-0.3;
        tAlmInfo.fThread = s_tSysPara.fCellOverVoltPrtThre;
        JudgeAnalogStatus(&tAlmInfo);

        //  电芯欠压告警
        ANALOG_ALARM_INIT(tAlmInfo, aucCellUnderVoltAlm[i]);
        tAlmInfo.fValue = tBcmData.afCellVolt[i];
        tAlmInfo.fValueAdd = s_tSysPara.fCellUnderVoltAlmRecoThre - s_tSysPara.fCellUnderVoltAlmThre;  //0.2;
        tAlmInfo.fThread = s_tSysPara.fCellUnderVoltAlmThre;
        tAlmInfo.ucAlarmType = JudgeAlarmInfoType(GetQtptestFlag() == False); //仅在非QTP模式下产生告警
        JudgeAnalogStatus(&tAlmInfo);

        //  电芯欠压保护
        if(GetQtptestFlag())
        {
            fCellVoltLowPrt = 2.50f;//QTP模式，单体欠压保护阈值为2.5V
        }
        else
        {
            fCellVoltLowPrt = s_tSysPara.fCellUnderVoltPrtThre;
        }
        ANALOG_ALARM_INIT(tAlmInfo, aucCellUnderVoltPrt[i]);
        tAlmInfo.fValue = tBcmData.afCellVolt[i];
        tAlmInfo.fValueAdd = s_tSysPara.fCellUnderVoltPrtRecoThre-fCellVoltLowPrt;//3.4 - s_tSysPara.fCellUnderVoltPrtThre;////51V恢复
        tAlmInfo.fThread = fCellVoltLowPrt;
        tAlmInfo.pExceptionHandling  = CellLowPrtHandle;
        JudgeAnalogStatus(&tAlmInfo);

        //  电芯动态欠压保护
        if(GetQtptestFlag())
        {
            s_tSysPara.ucCellUVPDelay = 0;//Qtp模式，动态欠压保护自动禁止
        }

        fCellVoltLowPrt = CalCellDynamicLowPrtThre(i , fCellTempMin);
        ANALOG_ALARM_INIT(tAlmInfo, aucCellDynamicUnderVoltPrt[i]);
        tAlmInfo.fValue = tBcmData.afCellVolt[i];
        tAlmInfo.fValueAdd = s_tSysPara.fCellUnderVoltPrtRecoThre-fCellVoltLowPrt;
        tAlmInfo.fThread = fCellVoltLowPrt;
        tAlmInfo.ucReserve = i;
        tAlmInfo.pExceptionHandling  = CellDynamicLowPrtHandle;
        JudgeAnalogStatus(&tAlmInfo);
         //  电芯损坏保护
        ANALOG_ALARM_INIT(tAlmInfo, aucCellDamagePrt[i]);
        tAlmInfo.fValue = tBcmData.afCellVolt[i];
        tAlmInfo.fValueAdd = 3.4-s_tSysPara.fCellDamagePrtThre;
        tAlmInfo.fThread = s_tSysPara.fCellDamagePrtThre;
        tAlmInfo.ucReserve = i;
        tAlmInfo.pExceptionHandling  = CellDamageHandle;
        JudgeAnalogStatus(&tAlmInfo);
    } 

    //  电芯一致性差告警
    ANALOG_ALARM_INIT(tAlmInfo, ucCellPoorConsisAlm);
    tAlmInfo.fValue = fCellVolMax - fCellVolMin;
    tAlmInfo.fValueAdd = -0.1;
    tAlmInfo.fThread = s_tSysPara.fCellPoorConsisAlmThre;
    tAlmInfo.ucReserve = (tBcmData.fBattVolt>s_tHardwarePara.ucCellVoltNum*3.1f && tBcmData.fBattVolt<s_tHardwarePara.ucCellVoltNum*3.4f);
    tAlmInfo.pExceptionHandling  = CellPoorConsisHandle;
    JudgeAnalogStatus(&tAlmInfo);
    
    //  电芯一致性差保护
    ANALOG_ALARM_INIT(tAlmInfo, ucCellPoorConsisPrt);
    tAlmInfo.fValue = fCellVolMax - fCellVolMin;
    tAlmInfo.fValueAdd = -0.2;
    tAlmInfo.fThread = s_tSysPara.fCellPoorConsisPrtThre;
    tAlmInfo.ucReserve = IsCellPoorConsisStart(tBcmData.fBattVolt, tBcmData.fBattCurr);
    tAlmInfo.pExceptionHandling  = CellPoorConsisHandle;
    JudgeAnalogStatus(&tAlmInfo);

#ifdef USING_HEAT_CONNECT
    //加热膜连接器高温保护
    ANALOG_ALARM_INIT(tAlmInfo, ucHeatConnTempHighPrt);
    tAlmInfo.fValue = tBcmData.fHeatConnTempMax;
    tAlmInfo.fValueAdd = -20.0;
    tAlmInfo.fThread = 100.0;
    JudgeAnalogStatus(&tAlmInfo);
#endif

    //  电压采样故障 20200820
    /*ANALOG_ALARM_INIT(tAlmInfo, ucBattVoltSampleAlm);    屏蔽电压采样故障告警
    for(i = 0; i < CELL_VOL_NUM_MAX; i++)
    {
        fCellVoltTotal += tBcmData.afCellVolt[i];
    }
    tAlmInfo.fValue = fabs(tBcmData.fBattVolt - fCellVoltTotal); //电池组电压 与 电芯总电压之和 差值
    tAlmInfo.fValueAdd = -0.2;
    tAlmInfo.fThread = BATT_VOLT_SAMPLE_DEFAULT_DTECT;
    JudgeAnalogStatus(&tAlmInfo);*/

    JudgeCellTempAlarm();

    return;
}

static FLOAT CalCellDynamicLowPrtThre(BYTE Num , FLOAT MinCellTemp)
{
        BYTE     ucCellSN = 0, ucTempSN=0;
        FLOAT fCellVoltLowPrt = 0;
        ucCellSN = Num %(s_tHardwarePara.ucCellVoltNum+1);
        ucTempSN = (ucCellSN/s_tHardwarePara.ucCellTempNum)%s_tHardwarePara.ucCellTempNum;	//电芯温度和电芯数量不是一一对应 Added by fengfj, 2019-07-13 16:50:56
        fCellVoltLowPrt = DoubleLinearInsertValue(s_tAlarmData.afCellTemp[ucTempSN], s_tAlarmData.fCurrCoef);
        fCellVoltLowPrt = CalCellVoltLowPrtThre(MinCellTemp, fCellVoltLowPrt);
        return fCellVoltLowPrt;
}

static FLOAT CalCellVoltLowPrtThre(FLOAT fCellTempMin, FLOAT fCellVoltLowPrt)
{
    FLOAT fMinCellPrtThre = 0.0;

    if(GetChargeNotFullOneWeek())//7天未充满过
    {
        if (fCellTempMin >= 10)
        {
            fMinCellPrtThre = 3.0;
        }
        else if (fCellTempMin >= 0)
        {
            fMinCellPrtThre = 2.9;
        }
        else if (fCellTempMin >= -10)
        {
            fMinCellPrtThre = 2.8;
        }
        else if (fCellTempMin >= -20)
        {
            fMinCellPrtThre = 2.7;
        }

        fCellVoltLowPrt = MAX(fCellVoltLowPrt, fMinCellPrtThre);
    }

    return fCellVoltLowPrt;
}

Static INT32S BattVoltLowPrtHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus)
{
    BOOLEAN  bStatus;
    FLOAT    fDelta = 0.0;
    
    if (NULL == pAlmInfo || NULL == pbTmpStatus)
    {
        return FAILURE;
    }

    if(NORMAL != *(pAlmInfo->pucRealAlarm))
    {
        fDelta = pAlmInfo->fValueAdd;
    }

    if (s_tAlarmData.fBattCurr > s_tAlarmData.fCurrMinDet + UNDERVOLL_CHARGE_MIN_CURRENT ) ///充电则告警恢复
    {
        bStatus = NORMAL;
    }
    else if (pAlmInfo->fValue < (fDelta + pAlmInfo->fThread))
    {
        bStatus = FAULT;
    }
    else
    {
        bStatus = NORMAL;
    }
    *pbTmpStatus = bStatus;

    return RTN_ALM_STATUE;
}

// static INT32S BattPowerOffPrtHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus)
// {
//     BOOLEAN  bStatus = 0;
    
//     if (NULL == pAlmInfo || NULL == pbTmpStatus)
//     {
//         return FAILURE;
//     }
    
//     if (pAlmInfo->fValue >= (pAlmInfo->fThread))
//     {
//         if (!GetPowerOffStatus())
//         {
//             bStatus = NORMAL;
//         }
//         else
//         {
//             bStatus = FAULT;
//         }
//     }
 
//     *pbTmpStatus = bStatus;

//     return RTN_ALM_STATUE;
// }


Static BOOLEAN checkPointer(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus)
{
    if (NULL == pAlmInfo || NULL == pbTmpStatus)
    {
        return FAILURE;
    }

    return SUCCESSFUL;
}

Static INT32S CellLowPrtHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus)
{
    BOOLEAN  bStatus = NORMAL;
    FLOAT    fDelta1 = 0.0;

    if (FAILURE == checkPointer(pAlmInfo, pbTmpStatus))
    {
        return FAILURE;
    }
    
    if(NORMAL != *(pAlmInfo->pucRealAlarm))
    {
        fDelta1 = pAlmInfo->fValueAdd;
    }

    if (s_tAlarmData.fBattCurr < s_tAlarmData.fCurrMinDet + UNDERVOLL_CHARGE_MIN_CURRENT) ///充电则告警恢复
    {
        if (pAlmInfo->fValue < MIN(fDelta1 + pAlmInfo->fThread, 3.4f))
        {
            bStatus = FAULT;
        }

    }
    *pbTmpStatus = bStatus;

    return RTN_ALM_STATUE;
}


static INT32S CellDynamicLowPrtHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus)
{
    BOOLEAN  bStatus;
    FLOAT    fDelta1 = 0.0;
    if (FAILURE == checkPointer(pAlmInfo, pbTmpStatus) || s_tHardwarePara.ucCellTempNum < 4)
    {
        return FAILURE;
    }
    if(NORMAL != *(pAlmInfo->pucRealAlarm))
    {
        fDelta1 = 0.1;
    }
    bStatus = NORMAL;
    if (s_tAlarmData.fBattCurr < s_tAlarmData.fCurrMinDet) ///充电则告警恢复
    {
        if ( (0 < s_tSysPara.ucCellUVPDelay) &&  (pAlmInfo->fValue < MIN(pAlmInfo->fThread + fDelta1, 3.4f)))
        {
            bStatus = FAULT;
        }
    }
    *pbTmpStatus = bStatus;

    return RTN_ALM_STATUE;
}

static INT32S CurrHighPrtDelay(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus, WORD wRetryDelay, BYTE ucLastPrtDelayFlag)
{
//    static WORD s_wCnt = 0;

    if (NULL == pAlmInfo || NULL == pAlmInfo->pwRetryCounter || NULL == pAlmInfo->pwRetryCounterMax)
    {
        return SUCCESSFUL;
    }

    if ((pAlmInfo->ucState != NORMAL) || (ucLastPrtDelayFlag == CURR_HIGH_BDPB))
    {
        if (*(pAlmInfo->pwRetryCounter) >= wRetryDelay)    //重试失败下次重试延时60s
        {
            (*(pAlmInfo->pwRetryCounterMax)) += wRetryDelay;
        }
        *(pAlmInfo->pwRetryCounter) = 0;
        return SUCCESSFUL;
    }

    if ( *pAlmInfo->pucRealAlarm == NORMAL )
    {
        return SUCCESSFUL;
    }

    if ( *(pAlmInfo->pwRetryCounterMax) >= 4 * wRetryDelay  //重试3次不成功就不再重试
        || (*(pAlmInfo->pwRetryCounter))++ < wRetryDelay )
    {
        *pbTmpStatus = pAlmInfo->ucAlarmType;
        return RTN_ALM_STATUE;
    }

    return SUCCESSFUL;
}


Static INT32S DisCurrH_Short_PrtHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus)
{
    INT32S slTmp = 0;

    if (NULL == pAlmInfo || NULL == pbTmpStatus)
    {
        return FAILURE;
    }

    if (s_tAlarmData.fBattCurr > s_tAlarmData.fCurrMinDet) ///充电时不做特殊处理
    {
        *(pAlmInfo->pwRetryCounterMax) = OVERCURR_RERTY_DELAY;
    #ifdef PAKISTAN_CMPAK_PROTOCOL
        s_bOverLoadLockStatus = False;
    #endif
        return SUCCESSFUL;
    }

    slTmp = CurrHighPrtDelay( pAlmInfo, pbTmpStatus, OVERCURR_RERTY_DELAY, s_ucLastCurrHighFlag);

#ifdef PAKISTAN_CMPAK_PROTOCOL
   if(slTmp == RTN_ALM_STATUE && (*(pAlmInfo->pwRetryCounterMax) >= 128 * OVERCURR_RERTY_DELAY))
   {
       s_bOverLoadLockStatus = True;
   }
#endif

    return slTmp;
}


static INT32S ChgCurrHighPrtHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus)
{

    if (NULL == pAlmInfo || NULL == pbTmpStatus || NULL == pAlmInfo->pwRetryCounterMax)
    {
        return FAILURE;
    }

    if (s_tAlarmData.fBattCurr < -1*s_tAlarmData.fCurrMinDet) ///放电时不做特殊处理
    {
        *(pAlmInfo->pwRetryCounterMax) = OVERCURR_RERTY_DELAY;
        return SUCCESSFUL;
    }

    return CurrHighPrtDelay( pAlmInfo, pbTmpStatus, OVERCURR_RERTY_DELAY, s_ucLastCurrHighFlag);
}

static INT32S CurrLimLoopInvalidHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus)
{
    static BYTE s_ucCnt = 0;
    INT32S slRet;

    if (NULL == pAlmInfo || NULL == pbTmpStatus||NULL == pAlmInfo->pwRetryCounterMax || NULL == pAlmInfo->pwRetryCounter)
    {
        return SUCCESSFUL;
    }

    slRet = CurrHighPrtDelay(pAlmInfo, pbTmpStatus, OVERCURR_RERTY_DELAY, s_ucLastCurrHighFlag);

    if ( SUCCESSFUL == slRet )
    {
        if (s_ucCnt++ > 200)
        {
            s_ucCnt = 0;
            *(pAlmInfo->pwRetryCounterMax) = OVERCURR_RERTY_DELAY;
        }
    }
    else
    {
        s_ucCnt = 0;
    }

    return slRet;
}

static INT32S CellPoorConsisHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus)
{
    if (NULL == pAlmInfo || NULL == pbTmpStatus)
    {
        return FAILURE;
    }

    if (pAlmInfo->ucReserve) 
    {
        return SUCCESSFUL;
    }

    *pbTmpStatus = NORMAL;
    return RTN_ALM_STATUE;
}

static INT32S BattUnderVoltALarmHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus)
{
    BOOLEAN  bStatus;
    FLOAT    fDelta = 0.0;
    
    if (NULL == pAlmInfo || NULL == pbTmpStatus)
    {
        return FAILURE;
    }

    if(NORMAL == *(pAlmInfo->pucRealAlarm))
    {
        if ( pAlmInfo->fValue < pAlmInfo->fThread)
        {
            bStatus = pAlmInfo->ucAlarmType;
        }
        else 
        {
            bStatus = NORMAL;
        }   

    }
    else
    {
        fDelta = 0.5;
        if((s_tAlarmData.fBattCurr > s_tAlarmData.fCurrMinDet) && (pAlmInfo->fValue >= pAlmInfo->fThread + fDelta))
        {
            bStatus = NORMAL;
        }
        else
        {
            bStatus = pAlmInfo->ucAlarmType;
        }
    }
    *pbTmpStatus = bStatus;
    return RTN_ALM_STATUE;
}


static BOOLEAN JudgeCellTempFaultSta(T_BCMDataStruct *ptBcmData)
{
    BYTE i;
    for (i = 0; i < s_tHardwarePara.ucCellTempNum; i++)
    {
        if ( (ptBcmData->afCellTemp[i] >= CELLTEMP_MIN &&  ptBcmData->afCellTemp[i] <= CELLTEMP_MAX))
        { // 超过温度阈值且非屏蔽时判断告警
            ptBcmData->abCellTempFault[i] = False;
        }
        else
        {
            ptBcmData->abCellTempFault[i] = True;
        }
    }
    return TRUE;
}


/******************************************************************************
* 函数名称：JudgeBcmDigitalAlarm()
* 调    用：
* 被 调 用：
* 输入参数：无
* 返 回 值：无
* 功能描述：根据开关量采样结果判断是否有告警产生或消除     
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要        
******************************************************************************/ 
static void JudgeBcmDigitalAlarm( void )
{
    BYTE i;
    T_BCMDataStruct tBcmData;
    T_AlmInfoStruct tAlmInfo;
    T_DCRealData tDcRealData;
    rt_memset_s(&tDcRealData, sizeof(T_DCRealData), 0, sizeof(T_DCRealData));
    GetBduReal(&tDcRealData);
    GetRealData(&tBcmData );
    //  电芯温度无效
    if(GetQtptestFlag())
    {
       JudgeCellTempFaultSta(&tBcmData);
    }
    for (i = 0; i < s_tHardwarePara.ucCellTempNum; i++)
    {
        DIGITAL_ALARM_INIT(tAlmInfo, ucCellTempSensorInvalidAlm[i]);
        tAlmInfo.ucState = tBcmData.abCellTempFault[i];
        JudgeDigitalAlarm(&tAlmInfo);  
    }


    //  充电过流保护
    if(tDcRealData.tDCAlarm.bChgOverCur == FAULT || s_tBcmAlarmReal.ucChgCurrHighPrt)//功率侧上送的充电过流保护告警产生时，使用功率上送的进行数字量逻辑处理
    {
        s_ucLastCurrHighFlag = s_ucChgCurrHighFlag;
        DIGITAL_ALARM_INIT(tAlmInfo, ucChgCurrHighPrt);
        tAlmInfo.ucState = tDcRealData.tDCAlarm.bChgOverCur;
        tAlmInfo.pwRetryCounter     = &s_tAlarmData.wChgCurrHighPrtCnt;
        tAlmInfo.pwRetryCounterMax  = &s_tAlarmData.wChgCurrHighPrtCntMax;
        tAlmInfo.pExceptionHandling = ChgCurrHighPrtHandle;
        JudgeDigitalAlarm(&tAlmInfo);
        s_ucChgCurrHighFlag = CURR_HIGH_BDCU;
    }
    else   //功率侧上送的充电过流保护告警未产生时，监控根据阈值进行模拟量逻辑处理
    {
        ANALOG_ALARM_INIT(tAlmInfo, ucChgCurrHighPrt);
        tAlmInfo.fValue = tBcmData.fBusCurr;
        tAlmInfo.fValueAdd = -2.0; //研制规范中规定固定是2
        tAlmInfo.fThread = s_tSysPara.fChgCurrHighPrtThre*s_tSysPara.wBatteryCap;
        JudgeAnalogStatus(&tAlmInfo);
        s_ucChgCurrHighFlag = CURR_HIGH_BDPB;
    }

    //  放电过流保护
    if(tDcRealData.tDCAlarm.bDischOverCur == FAULT || s_tBcmAlarmReal.ucDischgCurrHighPrt) //功率侧上送的放电过流保护告警产生时，使用功率上送的进行数字量逻辑处理
    {
        s_ucLastCurrHighFlag = s_ucDisCurrHighFlag;
        DIGITAL_ALARM_INIT(tAlmInfo, ucDischgCurrHighPrt);
        tAlmInfo.ucState = tDcRealData.tDCAlarm.bDischOverCur;
        tAlmInfo.pwRetryCounter     = &s_tAlarmData.wDischgCurrHighPrtCnt;
        tAlmInfo.pwRetryCounterMax  = &s_tAlarmData.wDischgCurrHighPrtCntMax;
        tAlmInfo.pExceptionHandling = DisCurrH_Short_PrtHandle;
        JudgeDigitalAlarm(&tAlmInfo);
        s_ucDisCurrHighFlag = CURR_HIGH_BDCU;
    }
    else  //功率侧上送的放电过流保护告警未产生时，监控根据阈值进行模拟量逻辑处理
    {
        ANALOG_ALARM_INIT(tAlmInfo, ucDischgCurrHighPrt);
        tAlmInfo.fValue = tBcmData.fBusCurr;
        tAlmInfo.fValueAdd = 2.0;
        tAlmInfo.fThread = 0.0 - s_tSysPara.fDischgCurrHighPrtThre*s_tSysPara.wBatteryCap;
        JudgeAnalogStatus(&tAlmInfo);
        s_ucDisCurrHighFlag = CURR_HIGH_BDPB;
    }
    s_ucLastCurrHighFlag = CURR_HIGH_FAULT;

    //  电池组过压保护
    DIGITAL_ALARM_INIT(tAlmInfo, ucBattOverVoltPrt);
    JudgeDigitalAlarm(&tAlmInfo);

    //  充电回路失效
    DIGITAL_ALARM_INIT(tAlmInfo, ucChgLoopInvalid);
    JudgeDigitalAlarm(&tAlmInfo);

    //  放电回路失效
    DIGITAL_ALARM_INIT(tAlmInfo, ucDischgLoopInvalid);
    JudgeDigitalAlarm(&tAlmInfo);

    //  限流回路失效
    DIGITAL_ALARM_INIT(tAlmInfo, ucCurrLimLoopInvalid);
    tAlmInfo.pwRetryCounter     = &s_tAlarmData.wCurrLimitInvalidCnt;
    tAlmInfo.pwRetryCounterMax  = &s_tAlarmData.wCurrLimitInvalidCntMax;    
    tAlmInfo.pExceptionHandling = CurrLimLoopInvalidHandle;
    JudgeDigitalAlarm(&tAlmInfo);

    //  电池反接
    DIGITAL_ALARM_INIT(tAlmInfo, ucBattReverse);
    JudgeDigitalAlarm(&tAlmInfo);

    //  电池短路
    DIGITAL_ALARM_INIT(tAlmInfo, ucBattShortCut);
    tAlmInfo.pwRetryCounter     = &s_tAlarmData.wBattShortCutCnt;
    tAlmInfo.pwRetryCounterMax  = &s_tAlarmData.wBattShortCutCntMax;
    tAlmInfo.pExceptionHandling = DisCurrH_Short_PrtHandle;
    JudgeDigitalAlarm(&tAlmInfo);

    //  机内过温保护
    DIGITAL_ALARM_INIT(tAlmInfo, ucInsideTempHighPrt);
    JudgeDigitalAlarm(&tAlmInfo);

    //  BDU EEPROM故障
    DIGITAL_ALARM_INIT(tAlmInfo, ucBduEepromAlm);
    JudgeDigitalAlarm(&tAlmInfo);

    //  BDU电池欠压保护
    DIGITAL_ALARM_INIT(tAlmInfo, ucBDUBattVoltLowPrt);
    JudgeDigitalAlarm(&tAlmInfo);
    if(!IsSleep())
    {
        /*主机发现有电池放电电流大于最小检测电流时，会控制从机退出静置休眠。从机退出静置休眠时由于功率上送的
          BDU电池欠压保护没有及时恢复，此处会判断出电池组欠压保护。而退出静置休眠时电池组电压没有达到电池组欠压保护的恢复
          电压，所以电池组欠压保护一直持续，五分钟后会由于欠压保护UVP原因再次进入休眠，只有充电才能唤醒。*/
        if ( s_tBcmAlarm.ucBDUBattVoltLowPrt != NORMAL )
        {
            s_tBcmAlarmFlag.ucBattUnderVoltPrt = s_tBcmAlarm.ucBDUBattVoltLowPrt;
            s_tBcmAlarmReal.ucBattUnderVoltPrt = s_tBcmAlarm.ucBDUBattVoltLowPrt;
        }
    }

    // BDU电池充电欠压保护
	DIGITAL_ALARM_INIT(tAlmInfo,ucBDUBattChgVoltLowPrt);
	tAlmInfo.ucAlarmType = (GPIO_ON == GetPMOSStatus()) ? FAULT : NORMAL;  //仅在PMOS打开时产生告警
	JudgeDigitalAlarm(&tAlmInfo);
    
    //  BDU母排欠压保护
    DIGITAL_ALARM_INIT(tAlmInfo, ucBDUBusVoltLowPrt);
    tAlmInfo.pwRetryCounter     = &s_tAlarmData.wBduBusVoltLowPrtCnt;
    tAlmInfo.pwRetryCounterMax  = &s_tAlarmData.wBduBusVoltLowPrtCntMax;
    tAlmInfo.pExceptionHandling = DisCurrH_Short_PrtHandle;    //母排欠压与放电过流做相同处理
    JudgeDigitalAlarm(&tAlmInfo);
    
    //  BDU母排过压保护
    DIGITAL_ALARM_INIT(tAlmInfo, ucBDUBusVoltHighPrt);
    JudgeDigitalAlarm(&tAlmInfo);

    // //  地址冲突
    // DIGITAL_ALARM_INIT(tAlmInfo, ucAddressClash);
    // JudgeDigitalAlarm(&tAlmInfo);  

    //	电池被盗告警
    DIGITAL_ALARM_INIT(tAlmInfo, ucBattLoseAlm);
    tAlmInfo.ucState = (IsBattTheft() || s_bGprsBattAlmEn);
    JudgeDigitalAlarm(&tAlmInfo);

    //	站点防盗告警
    DIGITAL_ALARM_INIT(tAlmInfo, ucSiteAntitheftAlm);
    tAlmInfo.ucState = JudgeBindingAntiTheft();
    JudgeDigitalAlarm(&tAlmInfo);

    //  单体电压采样异常
    DIGITAL_ALARM_INIT(tAlmInfo, ucCellVoltSampleFault);
    JudgeDigitalAlarm(&tAlmInfo);  

    //  BDU通信断
    DIGITAL_ALARM_INIT(tAlmInfo, ucBDUCommFail);
    JudgeDigitalAlarm(&tAlmInfo);  

    //  震动告警
    DIGITAL_ALARM_INIT(tAlmInfo, ucShakeAlarm);
    JudgeDigitalAlarm(&tAlmInfo); 

    // 直流内阻异常告警
    DIGITAL_ALARM_INIT(tAlmInfo, ucDcrFaultAlm);
    // 仅仅通过电池管理中的直流内阻异常告警状态进行判断，重启后需要重新进行一次DCR检测才能判断出是否需要产生告警
    tAlmInfo.ucState = s_tAlarmData.bDcrFaultAlm;
    JudgeDigitalAlarm(&tAlmInfo);

    // 直流内阻异常保护
    DIGITAL_ALARM_INIT(tAlmInfo, ucDcrFaultPrt);
    // 已经处于直流内阻异常保护的情况下继续保持，除非使用命令主动清除直流内阻异常保护。
    tAlmInfo.ucState = (s_tBcmAlarmReal.ucDcrFaultPrt == FAULT)?FAULT:s_tAlarmData.bDcrFaultPrt;
    JudgeDigitalAlarm(&tAlmInfo);

    //  回路异常
    DIGITAL_ALARM_INIT(tAlmInfo, ucLoopFault);
    tAlmInfo.ucState = s_tAlarmData.bLoopOff;
    JudgeDigitalAlarm(&tAlmInfo); 

    //	BDU电池闭锁告警
    DIGITAL_ALARM_INIT(tAlmInfo, ucBDUBattLockAlm);
    tAlmInfo.ucState = s_tAlarmData.ucBDUBattLockAlm;
    JudgeDigitalAlarm(&tAlmInfo);

    //	BDU连接器温度高保护
    DIGITAL_ALARM_INIT(tAlmInfo, ucBDUConnTempHighPrt);
    tAlmInfo.ucState = s_tAlarmData.ucBDUConnTempHighPrt;
    JudgeDigitalAlarm(&tAlmInfo);

    // 均衡电阻温度高保护
    DIGITAL_ALARM_INIT(tAlmInfo, ucBalanceResisTempHighPrt);
    tAlmInfo.ucState = s_tAlarmData.ucBalanceResisTempHighPrt;
    JudgeDigitalAlarm(&tAlmInfo);

    // //  均衡电路故障告警
    // DIGITAL_ALARM_INIT(tAlmInfo, ucEqualCircuitFaultAlm);
    // tAlmInfo.ucState = IsEqualCircuitFault();
    // JudgeDigitalAlarm(&tAlmInfo);

    //	加热膜失效故障
    DIGITAL_ALARM_INIT(tAlmInfo, ucHeaterFilmFailure);
    tAlmInfo.ucState = s_tAlarmData.ucHeaterFilmFailure;
    JudgeDigitalAlarm(&tAlmInfo);

    //  主继电器失效
    DIGITAL_ALARM_INIT(tAlmInfo, ucMainRelayFail);
    JudgeDigitalAlarm(&tAlmInfo);

    //  DCDC故障
    DIGITAL_ALARM_INIT(tAlmInfo, ucDCDCErr);
    JudgeDigitalAlarm(&tAlmInfo);

    //  采样异常
    DIGITAL_ALARM_INIT(tAlmInfo, ucSampleErr);
    JudgeDigitalAlarm(&tAlmInfo);

    //  辅助源故障
    DIGITAL_ALARM_INIT(tAlmInfo, ucAuxiSourceErr);
    JudgeDigitalAlarm(&tAlmInfo);

    //  自放电异常
    DIGITAL_ALARM_INIT(tAlmInfo, ucSelfDischFualt);
    tAlmInfo.ucState = GetSelfDischFaultFlag();
    JudgeDigitalAlarm(&tAlmInfo);  

    //  容量一致性差告警
    DIGITAL_ALARM_INIT(tAlmInfo, ucCapDCPRFaultAlm);
    tAlmInfo.ucState = GeCapDCDRFaultFlag();
    JudgeDigitalAlarm(&tAlmInfo); 
    
    //  消防告警
    DIGITAL_ALARM_INIT(tAlmInfo, ucFireControlAlm);
    JudgeDigitalAlarm(&tAlmInfo);

    //  激活回路电流异常
    DIGITAL_ALARM_INIT(tAlmInfo, ucActivePortCurrError);
    JudgeDigitalAlarm(&tAlmInfo);

    //单体温升速率异常告警
    DIGITAL_ALARM_INIT(tAlmInfo, ucCellTempRiseAbnormal);
    tAlmInfo.ucState = GetTempRiseRateFault();
    JudgeDigitalAlarm(&tAlmInfo);

#ifdef BOARD_Temp_High_Prt_BDU
    ANALOG_ALARM_INIT(tAlmInfo, ucBoardTempHighPrt);
    JudgeDigitalAlarm(&tAlmInfo);
#endif

    //  消防故障告警
    DIGITAL_ALARM_INIT(tAlmInfo, ucFireControlFaultAlm);
    JudgeDigitalAlarm(&tAlmInfo);

    //激活口反接告警
    DIGITAL_ALARM_INIT(tAlmInfo, ucActivatePortReverseAlm);
    JudgeDigitalAlarm(&tAlmInfo);
    
    //GPS故障告警
    DIGITAL_ALARM_INIT(tAlmInfo, ucGpsFaultAlm);
    tAlmInfo.ucState = GetGpsModuleStatus();
    JudgeDigitalAlarm(&tAlmInfo);

    //陀螺仪故障告警
    DIGITAL_ALARM_INIT(tAlmInfo, ucGyrFaultAlm);
    tAlmInfo.ucState = GetGyrCommFailStatus();
    JudgeDigitalAlarm(&tAlmInfo);

    //	网管防盗告警
    DIGITAL_ALARM_INIT(tAlmInfo, ucNetAntitheftAlm);
    tAlmInfo.ucState = JudgeNetAntiTheft();
    JudgeDigitalAlarm(&tAlmInfo);

    
    // 防水告警
    DIGITAL_ALARM_INIT(tAlmInfo, ucWaterIngrAlm);
    JudgeDigitalAlarm(&tAlmInfo);
    
    return; 
}

/******************************************************************************
* 函数名称：JudgeBcmAlarmPriority()
* 调    用：
* 被 调 用：
* 输入参数：无
* 返 回 值：无
* 功能描述：屏蔽关系判断  
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要        
******************************************************************************/ 
static void JudgeBcmAlarmPriority( void )
{
    BYTE i;

    PROTECT_SHIELD_ALARM(ucBattUnderVoltPrt, ucBattUnderVoltAlm);
    PROTECT_SHIELD_ALARM(ucBattOverVoltPrt, ucBattOverVoltAlm);
    PROTECT_SHIELD_ALARM(ucBattSOCLowPrt, ucBattSOCLowAlm);
    PROTECT_SHIELD_ALARM(ucBattSOHPrt, ucBattSOHAlm);
    PROTECT_SHIELD_ALARM(ucCellPoorConsisPrt, ucCellPoorConsisAlm);
    PROTECT_SHIELD_ALARM(ucChgCurrHighPrt, ucChgCurrHighAlm);
    PROTECT_SHIELD_ALARM(ucDischgCurrHighPrt, ucDischgCurrHighAlm);
    PROTECT_SHIELD_ALARM(ucBoardTempHighPrt, ucBoardTempHighAlm);
    PROTECT_SHIELD_ALARM(ucDcrFaultPrt, ucDcrFaultAlm);
    PROTECT_SHIELD_ALARM(ucFireControlFaultAlm, ucFireControlAlm);

    for (i = 0; i < CELL_TEMP_NUM_MAX; i++)
    {
        PROTECT_SHIELD_ALARM(aucChgTempHighPrt[i], aucChgTempHighAlm[i]);
        PROTECT_SHIELD_ALARM(aucChgTempLowPrt[i], aucChgTempLowAlm[i]);
        PROTECT_SHIELD_ALARM(aucDischgTempHighPrt[i], aucDischgTempHighAlm[i]);
        PROTECT_SHIELD_ALARM(aucDischgTempLowPrt[i], aucDischgTempLowAlm[i]);

        /* 出现温度采样异常时，屏蔽单体充/放电低温告警与保护 */
        // PROTECT_SHIELD_ALARM( ucCellTempAbnormal, aucChgTempLowAlm[i]);
        // PROTECT_SHIELD_ALARM( ucCellTempAbnormal, aucChgTempLowPrt[i]);
        // PROTECT_SHIELD_ALARM( ucCellTempAbnormal, aucDischgTempLowAlm[i]);
        // PROTECT_SHIELD_ALARM( ucCellTempAbnormal, aucDischgTempLowPrt[i]);
        /* 出现温度失效时，屏蔽单体充/放电低温告警与保护 */
        PROTECT_SHIELD_ALARM(ucCellTempSensorInvalidAlm[i], aucChgTempLowAlm[i]);
        PROTECT_SHIELD_ALARM(ucCellTempSensorInvalidAlm[i], aucChgTempLowPrt[i]);
        PROTECT_SHIELD_ALARM(ucCellTempSensorInvalidAlm[i], aucDischgTempLowAlm[i]);
        PROTECT_SHIELD_ALARM(ucCellTempSensorInvalidAlm[i], aucDischgTempLowPrt[i]);
    }
    for (i = 0; i < CELL_VOL_NUM_MAX; i++)
    {
        PROTECT_SHIELD_ALARM(ucCellVoltSampleFault, aucCellOverVoltAlm[i]);
        PROTECT_SHIELD_ALARM(ucCellVoltSampleFault, aucCellUnderVoltAlm[i]);
        PROTECT_SHIELD_ALARM(ucCellVoltSampleFault, aucCellOverVoltPrt[i]);
        PROTECT_SHIELD_ALARM(ucCellVoltSampleFault, aucCellUnderVoltPrt[i]);
        PROTECT_SHIELD_ALARM(ucCellVoltSampleFault, aucCellDamagePrt[i]);
        PROTECT_SHIELD_ALARM(aucCellOverVoltPrt[i], aucCellOverVoltAlm[i]);
        PROTECT_SHIELD_ALARM(aucCellUnderVoltPrt[i], aucCellUnderVoltAlm[i]);
    }

    return;
}

/****************************************************************************
* 函数名称：GetMainAlarmExistFlag
* 调    用：
* 被 调 用：
* 输入参数：无
* 返  回    值：重要告警存在标志
* 功能描述：获取重要告警存在标志
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要            
***************************************************************************/
BOOLEAN GetMainAlarmExistFlag( void )
{
    return s_bMainAlarmExist;
}

/****************************************************************************
* 函数名称：CalRealAlarmTotal()
* 调    用：
* 被 调 用：
* 输入参数：无
* 返   回   值：实时告警总条数
* 功能描述：计算实时告警总条数
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要               
***************************************************************************/
WORD  CalRealAlarmTotal( BYTE ucAlarmType, BYTE *pucOutCommonAlm)
{
    BYTE ucCommonAlm = NORMAL, *pAlarmShield = NULL;
    WORD    i;  
    WORD   wRealAlarmTotal  = 0;
    
    GetSysPara( &s_tSysPara );
    pAlarmShield = s_tSysPara.aucAlarmLevel;
    for (i = 1; i < ALARM_NUM + 1; i++)
    {
        if ((s_atDisRealAlarm[i].ucState != NORMAL) && (s_atDisRealAlarm[i].ucInnerFlag == ucAlarmType))
        {
            wRealAlarmTotal++;
        }
    }
    ucCommonAlm = 0 < wRealAlarmTotal ? FAULT : NORMAL;
    if (NULL != pucOutCommonAlm)
    {
        *pucOutCommonAlm = IGNORE == pAlarmShield[0] ? NORMAL : ucCommonAlm;
        DealDisAlarm(0, *pucOutCommonAlm);
    }

    return wRealAlarmTotal;
}

Static void alarmToClass(BYTE *pucClass)
{
    BYTE i, j, *p;

    p = ( BYTE * )&s_tBcmAlarm;
    for ( i = 0; i < ALARM_CLASS; i++ )
    {
        pucClass[i] = 0;
        if ( 1 == checkAlmGroup(p, &s_tBcmAlarm) )
        {
            for( j=0; j<CELL_VOL_NUM_MAX; j++ )
            {
                pucClass[i] |= (0 != *(p++));
            }
        }
        else if ( 2 == checkAlmGroup(p, &s_tBcmAlarm) )
        {
            for( j=0; j<CELL_TEMP_NUM_MAX; j++ )
            {
                pucClass[i] |= (0 != *(p++));
            }
        }
        else
        {
            pucClass[i] = *(p++);
        }
    }
    return;
}

/****************************************************************************
* 函数名称：SetCtrlOutRelay
* 调    用：
* 被 调 用：
* 输入参数：无
* 返   回   值：无
* 功能描述：获取告警输出干结点控制字\
* 作    者：
* 设计日期：
* 修改记录：// yang_an,整理协议修改，删掉油机启动部分干接点 
* 日    期      版  本      修改人      修改摘要            
***************************************************************************/
void    SetCtrlOutRelay( void )
{
    WORD    wtmp = 0;
    BYTE    i, k;
    BYTE    aucTemp[ALARM_CLASS];
    //T_CtrlOutStruct     tCtrl;

    GetSysPara( &s_tSysPara );
    alarmToClass(aucTemp);
    if(GetApptestFlag())
    {
        return;
    }

    for ( i = 0; i < ALARM_CLASS; i++ )
    {
        k = s_tSysPara.aucRelayBit[i];
        if ( (k <= RELAY_OUT) && (k > 0) )
        {
            if ( aucTemp[i] )
            {
                wtmp |= BIT(k-1);//PCLINT检查:经确认，对程序正确性无影响。
            }
        }
    }
    SetRelayCtrl_new(&aucTemp[0],wtmp);

    return; 
}
#endif


/****************************************************************************
* 函数名称：GetDataFlag
* 调    用：
* 被 调 用：
* 输入参数：ucDataFlagID-交流/整流/直流等ID号；ucCommPort-串口号
* 返 回 值：DATA_FLAG
* 功能描述：获取DATA_FLAG
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要                   
***************************************************************************/
BYTE GetDataFlag( BYTE ucDataFlagID, BYTE ucCommPort )
{
    switch ( ucDataFlagID )
    {
        case    BCM_ALARM:
            return  s_aucBcmDataFlag[ucCommPort];
        default:
            return 0x00;
    }   
}

/****************************************************************************
* 函数名称：SetDataFlag
* 调    用：
* 被 调 用：
* 输入参数：ucDataFlagID-交流/整流/直流等ID号；ucValue-设定值
* 返 回 值：无
* 功能描述：设置DATA_FLAG
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要               
***************************************************************************/
void SetDataFlag( BYTE ucDataFlagID, BYTE ucValue )
{
    switch ( ucDataFlagID )
    {
        case BCM_ALARM:
            s_aucBcmDataFlag[COMM_RS485]  |= ucValue;
            s_aucBcmDataFlag[COMM_PORT1]  |= ucValue;
            s_aucBcmDataFlag[COMM_PORT2]  |= ucValue;
            s_aucBcmDataFlag[COMM_CAN]    |= ucValue;
            break;
        default:
            break;
    }
    
    return; 
}

/****************************************************************************
* 函数名称：ClearDataFlag
* 调    用：
* 被 调 用：
* 输入参数：ucDataFlagID-交流/整流/直流等ID号；
                     ucValue-数值；
                     ucCommPort-串口号
* 返 回 值    ：无
* 功能描述：清掉DATA_FLAG的相应位
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要               
***************************************************************************/
void ClearDataFlag( BYTE ucDataFlagID, BYTE ucValue, BYTE ucCommPort )
{
    switch ( ucDataFlagID )
    {
        case BCM_ALARM:
            s_aucBcmDataFlag[ucCommPort] &= ucValue;             
        default:
            break;
    }
    
    return; 
}

/****************************************************************************
* 函数名称：GetRealAlarm
* 调    用：
* 被 调 用：
* 输入参数：ucUnitID-交流/整流/直流等ID号；pDest-目标地址
* 返 回 值：无
* 功能描述：获取实时告警
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要               
***************************************************************************/
void GetRealAlarm( BYTE ucUnitID, BYTE *pDest )
{
    if(NULL == pDest)
    {
        return;
    }
    
    if (BCM_ALARM == ucUnitID)
    {
    	if(s_bBcmAlmStartUpFlag)
    	{
            rt_memcpy( (BYTE *)pDest, (BYTE *)&s_tBcmAlarmStartUp, sizeof(T_BCMAlarmStruct) );
            iterFlag++;
            if (iterFlag > ALARM_DELAY_COUNTER) {
                s_bBcmAlmStartUpFlag = False;
            }
            return;
    	}
    	else
    	{
            rt_memcpy( (BYTE *)pDest, (BYTE *)&s_tBcmAlarm, sizeof(T_BCMAlarmStruct) );
            return;
    	}

    }

    if (BCM_ALARM_REAL == ucUnitID)
    {
        rt_memcpy( (BYTE *)pDest, (BYTE *)&s_tBcmAlarmReal, sizeof(T_BCMAlarmStruct) );
    }    
    
    return;
}

/****************************************************************************
* 函数名称：GetBcmRealAlarm
* 调    用：
* 被 调 用：
* 输入参数：Dest-目标地址
* 返 回 值：无
* 功能描述：获取Bcm未判断实时告警
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要               
***************************************************************************/
void GetBcmRealAlarm(T_BCMAlarmStruct *pDest)
{
    rt_memcpy((BYTE *)pDest, (BYTE *)&s_tBcmAlarmTmp, sizeof(T_BCMAlarmStruct)); 
}

/***************************************************************************
* 函数名称：TransBcmAlarm
* 调    用：
* 被 调 用：
* 输入参数：tBcmAlarm---整流器告警指针，ucSmrNo---整流器编号
* 返 回 值：无 
* 功能描述：传送CAN通讯BCM告警
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要                    
***************************************************************************/
void TransBcmAlarm( T_BCMAlarmStruct* tBcmAlarm )
{
    rt_memcpy((BYTE *)&s_tBcmAlarmTmp, (BYTE *)tBcmAlarm, sizeof(T_BCMAlarmStruct)); 
}

void setShakeAlm(BOOLEAN bSet)
{
    static WORD s_wDelay = 0;

    if ( False == bSet && s_wDelay < CLEARSHAKEALM_DELAY )
    {
        s_wDelay++;
        return;
    }

    s_wDelay = 0;
    if (s_tSysPara.bVibrationAlarmEn)
    {
        s_tBcmAlarmTmp.ucShakeAlarm = bSet;
    }
    return;
}

static void AnalyticalAlmCheck( WORD  wID )
{
    T_AnalyseInfoStruct tAnalyseInfo;
    rt_memset_s(&tAnalyseInfo, offsetof(T_AnalyseInfoStruct,wCheckSum)+2, 0, offsetof(T_AnalyseInfoStruct,wCheckSum)+2);
    GetAnalyticalAlm( &tAnalyseInfo );
    //特定告警被触发时，保存用于电芯统计记录
    if ( wID >= 60 && wID <= 75 )//wID = aucCellUnderVoltPrt[CELL_VOL_NUM_MAX];
    {
        tAnalyseInfo.bBattLowVolPrtTriggered = 1;
    }
    if ( wID >= 28 && wID <= 43 )//wID = aucCellOverVoltPrt[CELL_VOL_NUM_MAX];
    {
        tAnalyseInfo.bBattOverVolPrtTriggered = 1;
    }
    if ( wID == 1 || wID == 2 )//wID = ucChgCurrHighPrt or ucDischgCurrHighPrt
    {
        tAnalyseInfo.bBattOverCurrPrtTriggered = 1;
    }
    TransAnalyticalAlm( &tAnalyseInfo, INFO_WRITTEN );
    return;
}

/******************************************************************************
**函 数 名：DealDisAlarm()
**输    入：ucID-告警ID号  ucState-当前状态
**输    出：无
**调用模块：
**功能描述：处理实时、历史告警显示
* 作    者：严宏明
**时    间：2004-06-07
**修改记录：
* 日    期      版  本      修改人      修改摘要      
******************************************************************************/ 
Static void DealDisAlarm( WORD  wID, BYTE ucState )
{
//    T_TimeStruct    tTime;
    WORD wRealAlmLen = 0;
    T_HisAlarmSaveStruct tDisHisAlarm = {0};

    //若告警消除，保存历史告警
    if ( s_atDisRealAlarm[wID].ucState && !ucState )
    {   
        tDisHisAlarm.wAlarmID   = wID;
        //tDisHisAlarm.ucState  = s_atDisRealAlarm[wID].ucState;
        //告警消除时以下状态需要同步恢复，避免统计实时告警条数时有误。
        s_atDisRealAlarm[wID].ucState = ucState;
        tDisHisAlarm.tStartTime = s_atDisRealAlarm[wID].tTime;
        time(&tDisHisAlarm.tEndTime);

        //将历史告警保存至EEPROM
        if (s_atDisRealAlarm[wID].ucInnerFlag == 0)
        {
            SaveHisAlarm( &tDisHisAlarm);
            ClearSaveRealAlm(wID);
            AnalyticalAlmCheck(wID);
        }
    }

    if (ucState)
    {//将实时告警存放在缓存中
        s_atDisRealAlarm[wID].ucState = ucState;
        time(&s_atDisRealAlarm[wID].tTime);

        TimerPlus(s_tRealAlmSave.ucRealAlmLen, REALALM_MAX_NUM);
        wRealAlmLen = MIN(s_tRealAlmSave.ucRealAlmLen, REALALM_MAX_NUM) - 1;
        s_tRealAlmSave.atRealAlm[wRealAlmLen].wAlarmID = wID;
        s_tRealAlmSave.atRealAlm[wRealAlmLen].ucInnerFlag = s_atDisRealAlarm[wID].ucInnerFlag;
        time(&s_tRealAlmSave.atRealAlm[wRealAlmLen].tTime);
    }
    return;
}

static void checkSavedAlarm( void )
{
	BYTE	i;
	BYTE    *pAlarm;
	WORD    wAlarmID = 0, wOffset;

    T_HisAlarmSaveStruct tDisHisAlarm = {0};

    wOffset = COMMON_ALARM_NUM;
    pAlarm  = ( BYTE * )&s_tBcmAlarmStartUp;

	readBmsRealAlarm(&s_tRealAlmSave);
	
    if ( 0 == CRC_Cal((BYTE*)&s_tRealAlmSave, offsetof(T_RealAlm256SaveStruct, wCrc) + 2) )
    {
        //上电后，保存的实时告警转存历史告警
        for (i = 0; i < MIN(s_tRealAlmSave.ucRealAlmLen, REALALM_MAX_NUM); i++)
        {
            wAlarmID = s_tRealAlmSave.atRealAlm[i].wAlarmID - wOffset; //告警ID,需减偏移
            if (wAlarmID >= sizeof(s_tBcmAlarmStartUp))
            {
                break;
            }
            pAlarm[wAlarmID] = FAULT;

            tDisHisAlarm.wAlarmID   = s_tRealAlmSave.atRealAlm[i].wAlarmID;
            tDisHisAlarm.tStartTime = s_tRealAlmSave.atRealAlm[i].tTime;
            time(&tDisHisAlarm.tEndTime);
            SaveHisAlarm( &tDisHisAlarm);
        }
    }
    rt_memset_s( (BYTE *)&s_tRealAlmSave, sizeof(s_tRealAlmSave), 0, sizeof(s_tRealAlmSave) );
    SaveRealAlarm(&s_tRealAlmSave);

	return;
}

static BOOLEAN ClearSaveRealAlm(WORD wID)
{
    BYTE i;
    BYTE ucLen = s_tRealAlmSave.ucRealAlmLen;
    BOOLEAN bRet = False;
    if (wID > ALARM_NUM)
    {
        return False;
    }
    for (i = 0; i < ucLen; i++)
    {
        if (s_tRealAlmSave.atRealAlm[i].wAlarmID == wID)
        {
            rt_memset_s(&s_tRealAlmSave.atRealAlm[i], sizeof(T_RealAlarmStruct), 0x00, sizeof(T_RealAlarmStruct));
            rt_memcpy(&s_tRealAlmSave.atRealAlm[i], &s_tRealAlmSave.atRealAlm[ucLen-1], sizeof(T_RealAlarmStruct));
            s_tRealAlmSave.ucRealAlmLen -= 1;
            bRet = True;
            break;
        }
    }
    return bRet;
}


BYTE JudgeAlarmType(T_BattResult const* pBattOut)
{
    WORD i;
    BYTE *p;
    BYTE *pAlarmLevel;
    BYTE ucAlmLevelFlag = 0;
    p = (BYTE *)&s_tBcmAlarm;
    pAlarmLevel = (BYTE *)&s_tBcmAlarmShield;

    if(IsExistLostAlarm(&s_tBcmAlarm) == True)
    {
        return ALM_SYS_LOCK;
    }

    if(IfSysInvalid())
    {
        return ALM_SYS_ABNORMAL;
    }

    for(i = 0;i < sizeof(s_tBcmAlarm);i++)
    {
        if(p[i] != NORMAL)
        {
            if(pAlarmLevel[i] == EMERGENCY)
            {
                // 重要告警快闪
                ucAlmLevelFlag = EMERGENCY;
                break;
            }
            else if(pAlarmLevel[i] == Minor)
            {
                // 次要告警慢闪
                ucAlmLevelFlag = Minor;
            }
        }
    }

    if(ucAlmLevelFlag == EMERGENCY)
    {
        // 重要告警快闪
        return AlarmIndicatorStatus(s_tSysPara.ucCriticalAlarmlight);
    }
    else if(ucAlmLevelFlag == Minor)
    {
        // 次要告警慢闪
        return AlarmIndicatorStatus(s_tSysPara.ucSecondaryAlarmlight);
    }

    /*if (IsNorthCommFail())
    {
        return ALM_COMM_FAIL;
    }*/ //软银不需要，暂去掉

    return ALM_NORMAL;
}


static BOOLEAN IsAnyAlarmExist()
{
    if (s_tBcmAlarm.ucChgLoopInvalid
       ||s_tBcmAlarm.ucDischgLoopInvalid
       ||s_tBcmAlarm.ucCurrLimLoopInvalid
       ||s_tBcmAlarm.ucBduEepromAlm
       ||s_tBcmAlarm.ucBDUCommFail
       ||s_tBcmAlarm.ucCellVoltSampleFault
       ||s_tBcmAlarm.ucBoardTempHighPrt  //add by xzx
       ||s_tBcmAlarm.ucBattSOHPrt
       ||s_tBcmAlarm.ucBDUBattLockAlm   //add by xzx
       ||s_tBcmAlarm.ucCellTempAbnormal
       ||s_tBcmAlarm.ucBDUBattChgVoltLowPrt
    //    ||s_tBcmAlarm.ucEqualCircuitFaultAlm
       ||s_tBcmAlarm.ucBattReverse)
    {
        return True;
    }
    return False;

}


static BOOLEAN IsAnyAlarmExistNew()
{
    if (s_tBcmAlarm.ucBDUConnTempHighPrt
       || s_tBcmAlarm.ucBalanceResisTempHighPrt
       || s_tBcmAlarm.ucHeaterFilmFailure
       || s_tBcmAlarm.ucMainRelayFail
       || s_tBcmAlarm.ucDCDCErr
       || s_tBcmAlarm.ucSampleErr
       || s_tBcmAlarm.ucAuxiSourceErr
       || s_tBcmAlarm.ucFireControlAlm
       || s_tBcmAlarm.ucFireControlFaultAlm)
    {
        return True;
    }
    return False;
}



BOOLEAN IfSysInvalid(void)
{
    BYTE i;
    if (IsAnyAlarmExist() == True || (IsAnyAlarmExistNew() == True))
    {
        return True;
    }
     
    for(i = 0; i < s_tHardwarePara.ucCellVoltNum; i++)
    {
        if(s_tBcmAlarm.aucCellDamagePrt[i])
        {
           return True;
        }
    }
    for(i = 0; i < s_tHardwarePara.ucCellTempNum; i++)
    {
        //单体温度一个无效则长亮
        if(NORMAL != s_tBcmAlarm.ucCellTempSensorInvalidAlm[i])
        {
            return True;
        }
    }
    return False;
}


/***************************************************************************
 * @brief    对设备级、站点级、网管级告警设置告警
 * @param    {BOOLEAN} bAlm 告警有无
 * @param    {BYTE} ucTheftType 告警层级
 **************************************************************************/
static void SetTheftAlm(BOOLEAN bAlm, BYTE ucTheftType)
{
    BYTE *pBcmAlm = NULL;
    BYTE *pBcmAlmReal = NULL;

    if (bAlm != NORMAL && bAlm != FAULT)
    {
        return;
    }

    if (ucTheftType == BATT_THEFT)
    {
        pBcmAlm = &s_tBcmAlarm.ucBattLoseAlm;
        pBcmAlmReal = &s_tBcmAlarmReal.ucBattLoseAlm;
    }
    else if (ucTheftType == SITE_THEFT)
    {
        pBcmAlm = &s_tBcmAlarm.ucSiteAntitheftAlm;
        pBcmAlmReal = &s_tBcmAlarmReal.ucSiteAntitheftAlm;
    }
    else if (ucTheftType == NET_THEFT)
    {
        pBcmAlm = &s_tBcmAlarm.ucNetAntitheftAlm;
        pBcmAlmReal = &s_tBcmAlarmReal.ucNetAntitheftAlm;
    }
    else
    {
        return;
    }

    // 告警值未发生变化则提前return
    if (*pBcmAlm == bAlm && *pBcmAlmReal == bAlm)
    {
        return;
    }

    *pBcmAlm = bAlm;
    *pBcmAlmReal = bAlm;

    saveResetData(ucTheftType, bAlm);
    return;
}

static BOOLEAN  IsBattTheft(void)
{
    BOOLEAN bIsBattTheft = False;
    GetSysPara( &s_tSysPara );
    if (GetDefenceStatus() == False)
    {
        return False;
    }
    //需要特殊处理，系统掉电电池丢失告警也不能清除
    if(s_tBcmAlarmReal.ucBattLoseAlm != NORMAL)
    {
        return True;
    }
    if (JudgeGyroAntiTheft() == True || JudgeWireAntiTheft() == True)
    {
        bIsBattTheft = True;
    }

    if (bIsBattTheft == True)
    {
        SetGprsAutoUpStatus(True);
        SetTheftAlm(FAULT, BATT_THEFT);
        return True;
    }

    return False;
}

/***************************************************************************
 * @brief    陀螺仪防盗判断
 * @return   True-产生告警 False-不产生告警
 **************************************************************************/

Static BOOLEAN JudgeGyroAntiTheft(void)
{
    BOOLEAN bLeanCheckRtn = False;
    if (s_tSysPara.ucGyroAntiTheftMode == 1) // 倾角
    {
        return checkLean(s_tSysPara.bGyroscopeSensitivity);
    }
    else // 通信和倾角
    {
        if (IsNthCommEverConnected() == False) // 判断通信断前，必须曾经有过正常通信
        {
            return False;
        }

        TimerPlus(s_wCommLostCntGyro_1min, 168);
        if (s_bCommLostCntGyroFlag == True)
        {
            TimerPlus(s_wCommLostCntGyro, 40320); //40320 = 240（陀螺仪防盗延时最大值）* 168
        }
        bLeanCheckRtn = checkLean(s_tSysPara.bGyroscopeSensitivity);
        if ((bLeanCheckRtn == True && s_wCommLostCntGyro_1min >= 167) || s_bCommLostCntGyroFlag == True)
        {
            s_bCommLostCntGyroFlag = True;
            s_wCommLostCntGyro_1min = 167;
            if (s_wCommLostCntGyro >= s_tSysPara.ucGyroAntiTheftTime * 167)
            {
                return True;
            }
        }

        return False;
    }
}



/***************************************************************************
 * @brief    防盗线防盗判断
 * @return   True-产生告警 False-不产生告警
 **************************************************************************/
BOOLEAN JudgeWireAntiTheft(void)
{
    if (IsSupportsAntiTheftLine() && PIN_LOW == getPortInput(Digit_THEFT_DET)) // 防盗线正确连接
    {
        s_wCommLostCntWire = 0;
        return False;
    }
    else
    {
        if (IsNthCommEverConnected() == False) // 判断通信断前，必须曾经有过正常通信
        {
            return False;
        }

        if (s_tSysPara.wSoftAntiTheftDelay == 0) // 防盗延时屏蔽
        {
            return False;
        }

        // 具备防盗线电路，先判断防盗线断，再判断北向通信断
        if (IsSupportsAntiTheftLine())
        {
            s_wCommLostCntWire++;
            if (s_wCommLostCntWire > s_tSysPara.wSoftAntiTheftDelay * 167) // 167 = 1min = 60s
            {
                s_wCommLostCntWire = s_tSysPara.wSoftAntiTheftDelay * 167;
                return True;
            }

            return False;
        }
        // 不具备防盗线电路，先判断北向通信断，再判断并机通信断
        else
        {
            s_wCommLostCntWire++;
            if (s_wCommLostCntWire >= s_tSysPara.wSoftAntiTheftDelay * 167) // 167 = 1min = 60s
            {
                s_wCommLostCntWire = s_tSysPara.wSoftAntiTheftDelay * 167;
                IncCANCommLostCnt();
            }
            else
            {
                ClearCANCommLostCnt();
            }

            if (s_wCommLostCntWire >= s_tSysPara.wSoftAntiTheftDelay * 167 && GetCANCommLostCnt() >= 5 * 167)
            {
                SetCANCommLostCnt(5 * 167);
                return True;
            }

            return False;
        }
    }
}


/***************************************************************************
 * @brief    电池设备级防盗统一解锁入口
 * @param    {BYTE} ucAddr  本机地址
 * @param    {BYTE} ucMode  使用的解锁方式
 **************************************************************************/
SHORT DealDeviceUnlock(BYTE ucMode)
{
    if (NORMAL == s_tBcmAlarm.ucBattLoseAlm)
    {
        return FAILURE;  // 无防盗告警则不处理,防止重复执行导致通讯延迟
    }
    GetSysPara(&s_tSysPara);

    // 存在通信就需要清除防盗计时器
    ClearBattTheftAlmCnt();

    if (s_tSysPara.ucBattUnlockMode != ucMode)
    {
        return FAILURE; // 使用的解锁方式与系统设定的不同则不处理
    }

    // 策略：采样十次陀螺仪设备倾角，如果其中五次超过倾角阈值则判断设备丢失
    if (s_tBcmAlarm.ucBattLoseAlm == FAULT || s_tBcmAlarmReal.ucBattLoseAlm == FAULT)
    {
        for (BYTE i = 0; i < 10; i++)
        {
            checkLean(s_tSysPara.bGyroscopeSensitivity);
        }
    }

    ClearBattTheftAlm();
    return SUCCESSFUL;
}

/***************************************************************************
 * @brief    清除直流内阻异常保护告警
 * @param
 **************************************************************************/
BOOLEAN ClearDcrFaultPrt(void)
{
    s_tBcmAlarm.ucDcrFaultPrt = NORMAL;
    s_tBcmAlarmReal.ucDcrFaultPrt = NORMAL;
    // 需要将battout里对应的直流内阻保护状态置为正常
    SetDcrFaultPrtNormal();
    saveResetData(DCR_FAULT_ALM_PRT, DCR_NORMAL);
    SaveAction(GetActionId(CONTOL_CLEAR_DCR_PRT), "Clear DCR Prt");
    return TRUE;
}

/***************************************************************************
 * @brief    直流内阻异常告警支持自动恢复，重启后需要重新判断
 * @param
 **************************************************************************/
BOOLEAN ClearDcrFaultAlm(void)
{
    s_tBcmAlarm.ucDcrFaultAlm = NORMAL;
    s_tBcmAlarmReal.ucDcrFaultAlm = NORMAL;
    // 需要将battout里对应的直流内阻告警状态置为正常
    SetDcrFaultAlmNormal();
    return TRUE;
}

/***************************************************************************
 * @brief    清除设备级电池丢失告警
 * @param    {BYTE} ucAddr  本机地址
 **************************************************************************/
void ClearBattTheftAlm(void)
{
    SetGprsAutoUpStatus(False);
    SetTheftAlm(NORMAL, BATT_THEFT);

    return;
}


/***************************************************************************
 * @brief    清除防盗线防盗计时器、陀螺仪防盗计时器
 **************************************************************************/
WORD ClearBattTheftAlmCnt(void)
{
    // 清除所有相关计时器
    s_wCommLostCntWire = 0;           // 清除防盗线防盗计时器
    s_wCommLostCntGyro_1min = 0;      // 清除陀螺仪防盗1min计时器
    s_wCommLostCntGyro = 0;           // 清除陀螺仪防盗计时器
    ClearCANCommLostCnt();            // 清除CAN并机通信断5min计时器
    s_bCommLostCntGyroFlag = FALSE;   // 重置陀螺仪防盗计时标志位
    return s_wCommLostCntWire;

}


BOOLEAN SetGprsAutoUpStatus(BOOLEAN bStatus)
{
	if(bStatus == True)
	{
		s_bGprsAutoUpDataFlag = bStatus;
	}
	else
	{
		s_bGprsAutoUpDataFlag = False;
	}
	return s_bGprsAutoUpDataFlag;
}

/***************************************************************************
* @brief    设置GPRS主动上送状态
 **************************************************************************/
BOOLEAN GetGprsAutoUpStatus(void)
{
	return s_bGprsAutoUpDataFlag;
}

void GprsSetBattTheftAlm(void)
{
    SetGprsAutoUpStatus(True);
    SetTheftAlm(FAULT, BATT_THEFT);

    s_bGprsBattAlmEn = True;
    return;
}

void GprsClearBattTheftAlm(void)
{
    s_bGprsBattAlmEn = False;
    ClearBattTheftAlm();
}

#ifdef BOARD_Temp_High_Prt_BDPB
static INT32S BoardTempHighPrtHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus)
{
    BOOLEAN  bStatus;
    FLOAT    fDelta = 0.0;

    if (NULL == pAlmInfo || NULL == pbTmpStatus)
    {
        return FAILURE;
    }

    if(NORMAL != *(pAlmInfo->pucRealAlarm))
    {
        fDelta = pAlmInfo->fValueAdd;
    }

    if (pAlmInfo->fValue > (fDelta + pAlmInfo->fThread))
    {
        bStatus = FAULT;
    }
    else
    {
        bStatus = s_tBcmAlarmTmp.ucBoardTempHighPrt;
    }
    *pbTmpStatus = bStatus;

    return RTN_ALM_STATUE;
}
#endif /* BOARD_Temp_High_Prt_BDPB */

static BOOLEAN IsCellPoorConsisStart(FLOAT fBattVol, FLOAT fBattCurr)
{
	if(fBattCurr > s_tAlarmData.fCurrMinDet)
	{////充电并且有充电电流时
		if(fBattVol > fBattCurr * 0.02 + 3.17*s_tHardwarePara.ucCellVoltNum && fBattVol < fBattCurr * 0.02 + 3.4*s_tHardwarePara.ucCellVoltNum)
		{
			return True;
		}
	}
	else if(fBattVol > 3.17*s_tHardwarePara.ucCellVoltNum && fBattVol < 3.4*s_tHardwarePara.ucCellVoltNum)
	{
		return True;
	}

	return False;
}

BOOLEAN GetICRestartFlag(void)
{
    return s_bRestartIC;
}

void SetICRestartFlag(BOOLEAN Flag)
{
    s_bRestartIC = Flag;
}

// Static BOOLEAN IsEqualCircuitFault(void)
// {
//     BYTE ucEqualCircuitFault = 0;
//     BOOLEAN bEqualCircuitHigh;
//     if ( s_bBalanceFault )
//     {
//         return True;  // 不能自动恢复
//     }

//     ucEqualCircuitFault = GetEqualCircuitFault();
//     if(ucEqualCircuitFault == 1 || ucEqualCircuitFault == 2)  //当24小时周期检测时检测到短路或者断路时，产生均衡故障告警
//     {
//         return True;
//     }

//     bEqualCircuitHigh = IsEqualCircuitFaultDetected();
//     if (!IsCellEqualOn())
//     {
//         if (bEqualCircuitHigh)
//         {
//             s_wCircShortCnt++;
//         }
//         else
//         {
//             s_wCircShortCnt = 0;
//         }
//     }
//     else
//     {
//         if (bEqualCircuitHigh)
//         {
//             s_wCircOpenCnt = 0;
//             s_uCircOpenTimes = 0;
//         }
//         else
//         {
//             s_wCircOpenCnt++;
// #ifdef SHIELD_BALANCE_CIRCUIT_OPENALM
//             s_wCircOpenCnt = 0; // R321成果鉴定不验收断路故障
//             s_uCircOpenTimes = 0;
// #endif
//         }
//     }

//      if(500 < s_wCircOpenCnt && s_uCircOpenTimes < 1 && s_bRestartIC == False)
//     {
//         s_wCircOpenCnt = 0;
//         s_uCircOpenTimes++;
//         s_bRestartIC  = True;
//         SaveAction(GetActionId(CONTOL_SAMPLE_IC_RESTART), "CircuitOpenICRst");
//     }
    
//    if ((166 < s_wCircShortCnt)||((500 < s_wCircOpenCnt)&&(s_bRestartIC == True)))
//     {
//         s_bBalanceFault = True;
//     }

//     return s_bBalanceFault;
// }

void ClearCellDamagePrt(void)
{
    BYTE i = 0;

    for (i = 0; i < s_tHardwarePara.ucCellVoltNum; i++)
    {
        s_tCellDamageSave.abCellDamagePrt[i] = NORMAL;
        s_atCellVoltPoor[i].bState = NORMAL;
        s_atCellVoltPoor[i].bFlag = NORMAL;
        s_atCellVoltPoor[i].ucCounter = 0;
        s_atCellVoltTooHigh[i].bState = NORMAL;
        s_atCellVoltTooHigh[i].bFlag = NORMAL;
        s_atCellVoltTooHigh[i].ucCounter = 0;
        CLEAR_ALAEM(aucCellDamagePrt[i]);
    }
    return;
}

static void ReadDefenceStatus(void)
{
	LONG lDefenceInfoReadRtn;
	rt_memset_s(&s_tDefenceInfo, sizeof(T_DefenceInfoStruct), 0, sizeof(T_DefenceInfoStruct));

	lDefenceInfoReadRtn = readFile(FILE_NAME_DEFENCEINFO, (BYTE*)&s_tDefenceInfo, sizeof(T_DefenceInfoStruct));
    if ( s_tDefenceInfo.wCheckSum != CRC_Cal((BYTE*)&s_tDefenceInfo, offsetof(T_DefenceInfoStruct,wCheckSum)) || (lDefenceInfoReadRtn != sizeof(T_DefenceInfoStruct)) )
    {
		rt_memset_s(&s_tDefenceInfo, sizeof(T_DefenceInfoStruct), 0, sizeof(T_DefenceInfoStruct));//数据异常时直接将布防状态处理为未布防
    }
}


static BOOLEAN DealClearDefence(void)
{
    if (s_tDefenceInfo.wDefenceStatus == True)
    {
        if (s_tSysPara.aucAlarmLevel[BATT_LOSE_ALM_INDEX] == 0)
        {
            ClearDefenceStatus(DEFENCE_CLEAR_BY_ALARMLVL);
        }
        else if (s_tSysPara.wSoftAntiTheftDelay == 0 && s_tSysPara.bGyroscopeSensitivity == 0)
        {
            ClearDefenceStatus(DEFENCE_CLEAR_BY_SYSPARA);
        }
        else if (GetApptestFlag())
        {
            ClearDefenceStatus(DEFENCE_CLEAR_BY_APPTEST);
        }
        else if (GetQtptestFlag())
        {
            ClearDefenceStatus(DEFENCE_CLEAR_BY_QTP);
        }
        return True;
    }
    else
    {
        return False;
    }
}

static BOOLEAN NoDefenceCondition(void)
{
    if (s_tSysPara.aucAlarmLevel[BATT_LOSE_ALM_INDEX] == 0
       || (s_tSysPara.wSoftAntiTheftDelay == 0 && s_tSysPara.bGyroscopeSensitivity == 0)
       || GetApptestFlag() || GetQtptestFlag())
    {
        return True;
    }
    else
    {
        return False;
    }
}


Static void UpdateDefenceStatus(void)
{
    T_BattResult tBattOut;

    // 电池丢失告警级别判断为屏蔽或者（软件防盗延时&&陀螺仪灵敏度）为0则关闭布防
    GetSysPara(&s_tSysPara);

    // 布防完成情况下，确认是否需要关闭布防
    if (DealClearDefence() == True)
    {
        return;
    }

    // 电池丢失告警级别判断为屏蔽 或者（软件防盗延时&&陀螺仪灵敏度）为0 或者 apptest 或者 qtp不支持进布防
    if (NoDefenceCondition() == True)
    {
        s_tDefenceInfo.wDefenceStatus = False;
        return;
    }

    // 防盗线判断：判断是否支持防盗线,如果支持防盗线并且防盗线插好则满足自动布防，否则不满足自动布防条件
    if (IsSupportsAntiTheftLine())
    {
        if (PIN_HIGH == getPortInput(Digit_THEFT_DET))
        {
            s_tDefenceInfo.wDefenceStatus = False;
            return;
        }
    }

    // 充电时间3分钟判断：不足3分钟
    rt_memset_s(&tBattOut, sizeof(T_BattResult), 0, sizeof(T_BattResult));
    GetBattResult(&tBattOut);
    if (tBattOut.slDefenseBattChargeMinutes < 3)
    {
        s_tDefenceInfo.wDefenceStatus = False;
        return;
    }

    // 布防成功并保存布防状态
    s_tDefenceInfo.wDefenceStatus = True;
    s_tDefenceInfo.wCheckSum = CRC_Cal((BYTE*)&s_tDefenceInfo, (offsetof(T_DefenceInfoStruct,wCheckSum)));
    if(writeFile(FILE_NAME_DEFENCEINFO, (BYTE*)&s_tDefenceInfo, sizeof(T_DefenceInfoStruct)))
    {
        SaveAction(GetActionId(CONTOL_DEFENCE_STATUS_CHANGE), "Normal->Defence");
        SaveGyroInfo(); // 保存当前电池位置信息作为位置基准
    }
    return;
}


/***************************************************************************
 * @brief    清除布防状态
 **************************************************************************/
BOOLEAN ClearDefenceStatus(BYTE ucFlag)
{
    //当前保存的布防状态为TRUE时更新布防状态为FALSE
    if(s_tDefenceInfo.wDefenceStatus == True)
    {
        s_tDefenceInfo.wDefenceStatus = False;
        s_tDefenceInfo.wCheckSum = CRC_Cal((BYTE*)&s_tDefenceInfo, (offsetof(T_DefenceInfoStruct,wCheckSum)));
        if (writeFile(FILE_NAME_DEFENCEINFO, (BYTE*)&s_tDefenceInfo, sizeof(T_DefenceInfoStruct)))
        {
            char* actionName;
            switch (ucFlag)
            {
                case DEFENCE_CLEAR_BY_ALARMLVL:
                    actionName = "AlarmClearDefence";
                    break;
                case DEFENCE_CLEAR_BY_SYSPARA:
                    actionName = "SysparaClearDefence";
                    break;
                case DEFENCE_CLEAR_BY_ENTERAPPTEST:
                    actionName = "EntAppClearDefence";
                    break;
                case DEFENCE_CLEAR_BY_APPTEST:
                    actionName = "ApptestClearDefence";
                    break;
                case DEFENCE_CLEAR_BY_EXITAPPTEST:
                    actionName = "ExitAppClearDefence";
                    break;
                case DEFENCE_CLEAR_BY_ENTERQTP:
                    actionName = "EntQTPClearDefence";
                    break;
                case DEFENCE_CLEAR_BY_QTP:
                    actionName = "QTPClearDefence";
                    break;
                case DEFENCE_CLEAR_BY_EXITQTP:
                    actionName = "ExitQTPClearDefence";
                    break;
                default:
                    actionName = "TestClearDefence";
                    break;
            }
            SaveAction(GetActionId(CONTOL_DEFENCE_STATUS_CHANGE), actionName);
            return True;
        }
    }
    return False;
}


/***************************************************************************
 * @brief    返回布防状态
 * @return   True-已布防 False-未布防
 **************************************************************************/
WORD GetDefenceStatus(void)
{
    return s_tDefenceInfo.wDefenceStatus;
}

BOOLEAN IsBattLock(void)
{
    return (s_tBcmAlarmReal.ucBDUBattLockAlm != NORMAL);
}

//判断是否有告警产生
static BOOLEAN JudgeIfExixtAlarm(BYTE *pAlarm,WORD wLength)
{
    for(int i = 0;i < wLength ;i++)
    {
        if(pAlarm[i] == True)
        {
            return True;
        }
    }

    return False;
}

//获取存在告警的标志位
BOOLEAN GetExistAlarmFlag(VOID)
{
  return s_bExistAlarmFlag; 
}

/**
 * @brief 判断单体损坏闭锁条件
 * 
 */
FLOAT GetBattVoltThresh(void)
{

    if(s_tHardwarePara.ucCellVoltNum == R121_CELL_VOLT_NUM)
    {
        s_fBattVoltThresh = 12.0f;
    }

    return s_fBattVoltThresh;
}

/***************************************************************************
 * @brief    根据条件判断告警的AlarmType
 * @return   FAULT-异常 NORMAL-正常
 **************************************************************************/
static BOOLEAN JudgeAlarmInfoType(BOOLEAN condition)
{
    if(condition)
    {
        return FAULT;
    }
    else
    {
        return NORMAL;
    }
}

/* 判断是否有均衡电阻温度高保护告警 */
BOOLEAN IsExistBalanceResisTempHighPrt(void)
{
    return s_tBcmAlarm.ucBalanceResisTempHighPrt;
}

/***************************************************************************
 * @brief    判断布防条件
 **************************************************************************/
WORD IsDefenseCondition(void)
{
    // 布防完成情况下，手动布防不操作
    if(GetDefenceStatus())
    {
        return RTN_SUCCESS_DEFENCE;
    }

    // 布防失败，当前是apptest测试模式
    if(GetApptestFlag())
    {
        return RTN_FAIL_DEFENCE_APPTEST;
    }

    // 布防失败，当前是qtp测试模式
    if(GetQtptestFlag())
    {
        return RTN_FAIL_DEFENCE_QTP;
    }

    // 电池丢失告警级别判断：屏蔽
    GetSysPara(&s_tSysPara);

    if (s_tSysPara.aucAlarmLevel[BATT_LOSE_ALM_INDEX] == 0
        && (IsSupportsAntiTheftLine() && PIN_HIGH == getPortInput(Digit_THEFT_DET))
        && (s_tSysPara.wSoftAntiTheftDelay == 0 && s_tSysPara.bGyroscopeSensitivity == 0))
    {
        return RTN_FAIL_DEFENCE_WIRE_LEVEL_PARA;
    }

    if (s_tSysPara.aucAlarmLevel[BATT_LOSE_ALM_INDEX] == 0)
    {
	    return RTN_FAIL_DEFENCE_LEVEL;
    }
	
    // 防盗线判断：支持防盗线，没接防盗线
    if (IsSupportsAntiTheftLine())
    {
        if (PIN_HIGH == getPortInput(Digit_THEFT_DET))
        {
            return  RTN_FAIL_DEFENCE_WIRE;
        }
    }
    // 软件防盗延时和陀螺仪灵敏度参数判断，如果这两个参数都为0则不可能触发防盗丢失告警
    if ((s_tSysPara.wSoftAntiTheftDelay == 0 && s_tSysPara.bGyroscopeSensitivity == 0))
    {
        return RTN_FAIL_DEFENCE_PARA;
    }
    // PMOS判断，如果PMOS关闭禁止写flash
    if (GPIO_OFF == GetPMOSStatus())
    {
        return RTN_FAIL_COMMAND;
    }

    return RTN_SATISFY_DEFENCE;
}


/***************************************************************************
 * @brief    获取站点防盗布防状态(0:未布防，1:已布防)
 **************************************************************************/
BOOLEAN GetSiteAntiTheftStatus(void)
{
    return s_tSiteAntiTheft.bSiteAntiTheftStat;
}

/***************************************************************************
 * @brief    开启站点防盗
 **************************************************************************/
BOOLEAN DealSiteAntiTheftStart(BYTE *pucSiteAntiTheftKey)
{
    GetSysPara(&s_tSysPara);
    if (s_tSysPara.wSiteAntiTheftDelayTime == 0 || s_tSysPara.aucAlarmLevel[73] == 0)
    {
        return False;
    }

    s_wCommLostBinding = 0;

    InfoWriteBindingAntiTheft(True, pucSiteAntiTheftKey);

    return True;
}

/***************************************************************************
 * @brief    站点防盗心跳延续
 **************************************************************************/
BOOLEAN DealSiteAntiTheftHeartbeat(BYTE *pucSiteAntiTheftKey)
{
    if (rt_memcmp(pucSiteAntiTheftKey, s_tSiteAntiTheft.aucSiteAntiTheftKey, SITE_ANTITHEFT_KEY_LEN) != 0)
    {
        return False;
    }

    s_wCommLostBinding = 0;
    SetTheftAlm(NORMAL, SITE_THEFT);

    GetSysPara(&s_tSysPara);
    if (s_tSysPara.wSiteAntiTheftDelayTime != s_tSiteAntiTheft.wSiteAntiTheftDelay)
    {
        InfoWriteBindingAntiTheft(True, pucSiteAntiTheftKey);
    }

    return True;
}

/***************************************************************************
 * @brief    关闭站点防盗并且清除站点防盗告警
 **************************************************************************/
BOOLEAN DealSiteAntiTheftEnd(BYTE *pucSiteAntiTheftKey)
{
    BYTE aucKeyBlank[SITE_ANTITHEFT_KEY_LEN] = {0,};
    if (rt_memcmp(pucSiteAntiTheftKey, s_tSiteAntiTheft.aucSiteAntiTheftKey, SITE_ANTITHEFT_KEY_LEN) != 0)
    {
        return False;
    }

    InfoWriteBindingAntiTheft(False, aucKeyBlank);
    ClearBindingBmsInfo();
    SetTheftAlm(NORMAL, SITE_THEFT);
    return True;
}

/***************************************************************************
 * @brief    站点防盗-更换电子钥匙
 * @param    {BYTE} *pucSiteAntiTheftKey 旧电子钥匙
 * @param    {BYTE} *pucSiteAntiTheftKeyNew 新电子钥匙
 **************************************************************************/
BOOLEAN DealSiteAntiTheftChangeKey(BYTE *pucSiteAntiTheftKey, BYTE *pucSiteAntiTheftKeyNew)
{
    if (rt_memcmp(pucSiteAntiTheftKey, s_tSiteAntiTheft.aucSiteAntiTheftKey, SITE_ANTITHEFT_KEY_LEN) != 0)
    {
        return False;
    }

    s_wCommLostBinding = 0;
    SetTheftAlm(NORMAL, SITE_THEFT);
    InfoWriteBindingAntiTheft(True, pucSiteAntiTheftKeyNew);

    return True;
}

/***************************************************************************
 * @brief    初始化绑定防盗状态
 **************************************************************************/
static void InitBindingAntiTheft(void)
{
    rt_int32_t ReadRtn = 0;
    BYTE aucKeyBlank[SITE_ANTITHEFT_KEY_LEN] = {0,};
    rt_memset_s(&s_tSiteAntiTheft, sizeof(T_SiteAntiTheftStruct), 0, sizeof(T_SiteAntiTheftStruct));

    ReadRtn = readFile("/bindantitheft", (BYTE *)&s_tSiteAntiTheft, sizeof(T_SiteAntiTheftStruct));
    if (ReadRtn != sizeof(T_SiteAntiTheftStruct) || s_tSiteAntiTheft.wCrc != CRC_Cal((BYTE *)&s_tSiteAntiTheft, offsetof(T_SiteAntiTheftStruct, wCrc)))
    {
        InfoWriteBindingAntiTheft(False, aucKeyBlank);
    }
    return;
}

/***************************************************************************
 * @brief    判断绑定防盗是否触发电池丢失告警
 **************************************************************************/
Static BOOLEAN JudgeBindingAntiTheft(void)
{
    if (s_tBcmAlarmReal.ucSiteAntitheftAlm != NORMAL)
    {
        return True;
    }

    if (s_tSiteAntiTheft.bSiteAntiTheftStat == False ||
        s_wCommLostBinding++ < s_tSiteAntiTheft.wSiteAntiTheftDelay * 167 || IsBindingInfoChange() == False)
    {
        return False;
    }

    s_wCommLostBinding = s_tSiteAntiTheft.wSiteAntiTheftDelay * 167;
    SetTheftAlm(FAULT, SITE_THEFT);
    return True;
}

/***************************************************************************
 * @brief    写绑定防盗信息
 * @param    {BOOLEAN} stat 状态
 * @param    {BYTE *} pucSiteAntiTheftKey 32字节的电子钥匙
 **************************************************************************/
Static void InfoWriteBindingAntiTheft(BOOLEAN stat, BYTE *pucSiteAntiTheftKey)
{
    GetSysPara(&s_tSysPara);
    s_tSiteAntiTheft.bSiteAntiTheftStat = stat;
    rt_memcpy(s_tSiteAntiTheft.aucSiteAntiTheftKey, pucSiteAntiTheftKey, SITE_ANTITHEFT_KEY_LEN);
    s_tSiteAntiTheft.wSiteAntiTheftDelay = s_tSysPara.wSiteAntiTheftDelayTime;
    s_tSiteAntiTheft.wCrc = CRC_Cal((BYTE *)&s_tSiteAntiTheft, offsetof(T_SiteAntiTheftStruct, wCrc));
    writeFile("/bindantitheft", (BYTE *)&s_tSiteAntiTheft, sizeof(T_SiteAntiTheftStruct));
    return;
}

/***************************************************************************
 * @brief    是否存在防盗类告警(设备级、站点级、网管级)
 * @return   True-存在 False-不存在
 **************************************************************************/
BOOLEAN IsExistLostAlarm(T_BCMAlarmStruct *ptBcmAlarm)
{
    if (ptBcmAlarm->ucBattLoseAlm == NORMAL && ptBcmAlarm->ucSiteAntitheftAlm == NORMAL && ptBcmAlarm->ucNetAntitheftAlm == NORMAL)
    {
        return False;
    }
    return True;
}

/***************************************************************************
 * @brief    获取网管防盗布防状态(0:未布防，1:已布防)
 **************************************************************************/
BOOLEAN GetNetAntiTheftStatus(void)
{
    return s_tNetAntiTheft.bNetAntiTheftStat;
}

/***************************************************************************
 * @brief    开启站点防盗
 **************************************************************************/
BYTE DealNetAntiTheftStart(BYTE *pucNetAntiTheftKey)
{
    GetSysPara(&s_tSysPara);

    if (s_tSysPara.wNetAntiTheftDelayTime == 0)
    {
        return NET_RESPONSE_FAILURE_BY_PARA;
    }
    else if(s_tSysPara.aucAlarmLevel[ALM_SN_NET_ANTITHEFT_ALM] == 0)
    {
        return NET_RESPONSE_FAILURE_BY_ALA;
    }

    s_ulNetCommLostBinding = 0;

    return WriteNetAntiTheftInfo(NETANTITHEFT_ON, pucNetAntiTheftKey);
}

/***************************************************************************
 * @brief    判断电子钥匙和保存的电子钥匙是否相等
 **************************************************************************/
rt_int32_t JudgeNetAntiTheftKey(BYTE *pucNetAntiTheftKey)
{
    return rt_memcmp(s_tNetAntiTheft.aucNetAntiTheftKey, pucNetAntiTheftKey, NETANTITHEFT_KEY_LEN);
}


/**
 * @brief 网管防盗心跳延续
 */
BYTE DealNetAntiTheftHeartbeat(BYTE *pucNetAntiTheftKey, BYTE *pucNetAntiTheftSN)
{
    BYTE ucResult = FAILURE;

    // 检查密钥是否匹配
    if (JudgeNetAntiTheftKey(pucNetAntiTheftKey) == 0)
    {
        s_ulNetCommLostBinding = 0;
        SetTheftAlm(NORMAL, NET_THEFT);
        s_bNetAntiKeyMatchStatus = TRUE;
        ucResult = SUCCESSFUL;
    }
    else
    {
        s_bNetAntiKeyMatchStatus = FALSE;
        ucResult = NET_RESPONSE_FAILURE_BY_KEY;
    }

    // 检查序列号是否全为零
    if (!IsAllZeros(pucNetAntiTheftSN, NETANTITHEFT_SN_LEN))
    {
        // 处理序列号不全为零的情况
        HandleNonZeroSequenceNumber(pucNetAntiTheftSN, &ucResult);
    }

    return ucResult;
}

/**
 * @brief 处理非零序列号的逻辑
 */
static void HandleNonZeroSequenceNumber(BYTE *pucNetAntiTheftSN, BYTE *ucResult)
{
    // 检查 s_tNetAntiTheft.aucNetAntiTheftSN 是否全为零
    if (IsAllZeros(s_tNetAntiTheft.aucNetAntiTheftSN, NETANTITHEFT_SN_LEN))
    {
        WriteNetAntiTheftSN(pucNetAntiTheftSN);
        s_ulNetCommLostBinding = 0;
        *ucResult = (*ucResult == SUCCESSFUL) ? SUCCESSFUL : NET_RESPONSE_FAILURE_BY_KEY_SNCORRECT;
    }
    // 检查 pucNetAntiTheftSN 是否与 s_tNetAntiTheft.aucNetAntiTheftSN 相同
    else if (rt_memcmp(pucNetAntiTheftSN, s_tNetAntiTheft.aucNetAntiTheftSN, NETANTITHEFT_SN_LEN) == 0)
    {
        s_ulNetCommLostBinding = 0;
        *ucResult = (*ucResult == SUCCESSFUL) ? SUCCESSFUL : NET_RESPONSE_FAILURE_BY_KEY_SNCORRECT;
    }
}


/***************************************************************************
 * @brief    关闭站点防盗并且清除站点防盗告警
 **************************************************************************/
BYTE DealNetAntiTheftEnd(BYTE *pucNetAntiTheftKey)
{
    BYTE ucResult = 0;
    if (JudgeNetAntiTheftKey(pucNetAntiTheftKey) != 0)
    {
        return NET_RESPONSE_FAILURE_BY_KEY;
    }
    rt_memset_s(s_tNetAntiTheft.aucNetAntiTheftKey, sizeof(s_tNetAntiTheft.aucNetAntiTheftKey), 0, NETANTITHEFT_KEY_LEN);
    rt_memset_s(s_tNetAntiTheft.aucNetAntiTheftSN, sizeof(s_tNetAntiTheft.aucNetAntiTheftSN), 0, NETANTITHEFT_SN_LEN);
    ucResult = WriteNetAntiTheftInfo(False, s_tNetAntiTheft.aucNetAntiTheftKey);
    if (ucResult == SUCCESSFUL)
    {
        ClearBindingBmsInfo();
        SetTheftAlm(NORMAL, NET_THEFT);
    }
    return ucResult;
}

/***************************************************************************
 * @brief    初始化网管防盗状态
 **************************************************************************/
Static void InitNetAntiTheft(void)
{
    rt_int32_t ReadRtn = 0;
    rt_memset_s(&s_tNetAntiTheft, sizeof(T_NetAntiTheftStruct), 0, sizeof(T_NetAntiTheftStruct));

    ReadRtn = readFile("/netantitheft", (BYTE *)&s_tNetAntiTheft, sizeof(T_NetAntiTheftStruct));
    if (ReadRtn != sizeof(T_NetAntiTheftStruct) || s_tNetAntiTheft.wCrc != CRC_Cal((BYTE *)&s_tNetAntiTheft, offsetof(T_NetAntiTheftStruct, wCrc)))
    {
        rt_memcpy_s(s_tNetAntiTheft.aucNetAntiTheftKey, sizeof(s_tNetAntiTheft.aucNetAntiTheftKey), 0, NETANTITHEFT_KEY_LEN);
        rt_memcpy_s(s_tNetAntiTheft.aucNetAntiTheftSN, sizeof(s_tNetAntiTheft.aucNetAntiTheftSN), 0, NETANTITHEFT_SN_LEN);
        WriteNetAntiTheftInfo(False, s_tNetAntiTheft.aucNetAntiTheftKey);
    }
    return;
}

/***************************************************************************
 * @brief    判断绑定防盗是否触发电池丢失告警
 **************************************************************************/
Static BOOLEAN JudgeNetAntiTheft(void)
{
    if (s_tBcmAlarmReal.ucNetAntitheftAlm != NORMAL)
    {
        return True;
    }

    if (s_tNetAntiTheft.bNetAntiTheftStat == False ||
        s_ulNetCommLostBinding++ < s_tSysPara.wNetAntiTheftDelayTime * ONE_MINUTE_CNT_ALARM_THREAD || IsBindingInfoChange() == False)
    {
        return False;
    }

    s_ulNetCommLostBinding = s_tSysPara.wNetAntiTheftDelayTime * ONE_MINUTE_CNT_ALARM_THREAD;
    SetTheftAlm(FAULT, NET_THEFT);
    return True;
}

/***************************************************************************
 * @brief    写网管防盗信息
 * @param    {BOOLEAN} stat 状态
 * @param    {BYTE*} aucNetAntiTheftKey
 **************************************************************************/
BYTE WriteNetAntiTheftInfo(BOOLEAN stat, BYTE *pucNetAntiTheftKey)
{
    rt_int32_t len = 0;
    s_tNetAntiTheft.bNetAntiTheftStat = stat;
    rt_memcpy_s(s_tNetAntiTheft.aucNetAntiTheftKey, NETANTITHEFT_KEY_LEN, pucNetAntiTheftKey, NETANTITHEFT_KEY_LEN);
    s_tNetAntiTheft.wCrc = CRC_Cal((BYTE *)&s_tNetAntiTheft, offsetof(T_NetAntiTheftStruct, wCrc));
    len = writeFile("/netantitheft", (BYTE *)&s_tNetAntiTheft, sizeof(T_NetAntiTheftStruct));
    if (len == 0)
    {
        return NET_RESPONSE_FAILURE_BY_FILE;
    }
    return SUCCESSFUL;
}

/***************************************************************************
 * @brief    写网管防盗设置SN信息
 * @param    {BYTE*} pucNetAntiTheftSN SN信息
 **************************************************************************/

size_t WriteNetAntiTheftSN(BYTE *pucNetAntiTheftSN)
{
    // 直接拷贝序列号到结构体中
    rt_memcpy_s(s_tNetAntiTheft.aucNetAntiTheftSN, NETANTITHEFT_SN_LEN, pucNetAntiTheftSN, NETANTITHEFT_SN_LEN);
    
    // 计算 CRC 校验值
    s_tNetAntiTheft.wCrc = CRC_Cal((BYTE *)&s_tNetAntiTheft, offsetof(T_NetAntiTheftStruct, wCrc));
    
    // 写入文件
    return writeFile("/netantitheft", (BYTE *)&s_tNetAntiTheft, sizeof(T_NetAntiTheftStruct));
}


/***************************************************************************
 * @brief    告警指示灯状态显示方式：常灭、慢闪、快闪、常亮
 **************************************************************************/

static BYTE AlarmIndicatorStatus(BYTE IndicatorValue)
{
    if (IndicatorValue <= 3) 
    {
        return s_ucAlarmStatusMap[IndicatorValue];
    } 
    else 
    {
        // 其他值不控制告警告警灯
        return ALM_NORMAL;
    }
}



#ifdef PAKISTAN_CMPAK_PROTOCOL
/*cmpak过载判断*/
BOOLEAN GetOverLoadLockStatus(void)
{
    return s_bOverLoadLockStatus;
}
#endif

