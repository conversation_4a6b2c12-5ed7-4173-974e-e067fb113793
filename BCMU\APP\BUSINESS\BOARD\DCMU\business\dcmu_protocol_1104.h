/**
 * @file     device_type.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2017-04-19
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */

#ifndef _DCMU_PROTOCOL_1104_H
#define _DCMU_PROTOCOL_1104_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "device_type.h"



/*1104 protocol的命令唯一标识定义 */
#define DCMU_GET_DC_ALM_DATA                1	///<  获取直流告警
#define DCMU_GET_ENV_ALM_DATA               2	///<  获取环境告警
#define DCMU_GET_ENV_ANALOG_FLOAT           3	///<  获取环境设备模拟量（浮点）
#define DCMU_GET_DCEMFACTORY_INFO           4	///<  获取厂家信息
#define DCMU_GET_SYS_TIME                   5	///<  获取系统时间
#define DCMU_SET_SYS_TIME                   6	///<  设置系统时间
#define DCMU_GET_COMMPROTOCOL_VER           7   ///<  获取通信协议版本号
#define DCMU_GET_DEVICE_ADDR                8   ///<  获取设备地址
#define DCMU_GET_DC_ANALOG_FLOAT            9   ///<  获取直流配电模拟量（浮点）
#define DCMU_GET_DC_SWITCH_STATUS          10   ///<  获取直流配电开关量
#define DCMU_GET_DC_PARA_FLOAT             11   ///<  获取直流配电参数（浮点）
#define DCMU_SET_DC_PARA_FLOAT             12   ///<  设置直流配电参数（浮点）
#define DCMU_GET_ENV_PATA_FLOAT            13   ///<  获取环境设备参数（浮点）
#define DCMU_SET_ENV_PARA_FLOAT            14   ///<  设置环境设备参数（浮点）


/* cid2 功能码定义 */
/* DC */
#define DCMU_CMD_GET_ANALOG                      0x41    ///<  获取模拟量
#define DCMU_CMD_GET_SWITCH_STATUS               0x43    ///<  获取开关量
#define DCMU_CMD_GET_ALM                         0x44    ///<  获取告警
#define DCMU_CMD_GET_PARA                        0x46    ///<  获取参数
#define DCMU_CMD_SET_PARA                        0x48    ///<  设置参数
#define DCMU_CMD_GET_SYS_TIME                    0x4D    ///<  获取系统时间
#define DCMU_CMD_SET_SYS_TIME                    0x4E    ///<  设置系统时间
#define DCMU_CMD_GET_COMMPROTOCOL_VER            0x4F    ///<  获取通信协议版本号
#define DCMU_CMD_GET_DEVICE_ADDR                 0x50    ///<  获取设备地址
#define DCMU_CMD_GET_DCEMFACTORY_INFO            0x51    ///<  获取厂家信息

#define ENV_FAULT                           0x04  /* 环境数字量告警 */

dev_type_t* init_dev_dcmu_1104(void);
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _DCMU_PROTOCOL_1104_H

