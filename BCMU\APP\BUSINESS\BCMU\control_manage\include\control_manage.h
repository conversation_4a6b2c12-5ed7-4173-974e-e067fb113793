/**
 * @file     control_manage.h
 * @brief    控制管理
 * @details  This is the detail description.
 * <AUTHOR> 
 * @date     2023-01-29
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation 
 * @par History:
 *   version: author, date, descn
 */ 
#ifndef _CONTROL_MANAGE_H
#define _CONTROL_MANAGE_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus
#include "do_ctrl.h"
#include "emergency_power_off.h"
#include "fire_protect.h"
#include "device_type.h"
#include "dev_bmu.h"
#include "utils_server.h"

#pragma pack(1)
/* soc消息结构体 */
typedef struct {
    dev_inst_t* dev_inst;
    BMU_para_data_t* bmu_para;
} para_msg_t;
#pragma pack()

void* init_ctrl_manage(void * param);
int ctrl_manage_init(void *param);
void ctrl_manage_main(void *param);
void soc_soh_set_proc(void* data);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _CONTROL_MANAGE_H