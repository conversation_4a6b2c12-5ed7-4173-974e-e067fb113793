#ifndef _mqtt_common_h_
#define _mqtt_common_h_

#ifdef __cplusplus
extern "C" {
#endif    //  __cplusplus

#include <cJSON.h>
#include "data_type.h"
#include "type_define_in.h"
#include "utils_uuid.h"
#include "alarm_manage.h"
#include "mqttclient.h"
#include "net_connect_mode.h"
#include "north_data_utils.h"

#define SEND_ALARM_WAITE                1000                // 收集1s内的告警
#define SEND_REAL_DATA_NUM_ONCE         50                  // 每次只上送50个数据
#define ALARM_ID_MQTT_BASE              0x300000            // mqtt上送告警
#define ALARM_ACCU_MAX                  20                  // 累计20个告警就上送
#define TIMER_5MIN                      (5*60*1000)         // 5分钟定时器
#define SEC_10                          10                  // 表示10秒
// #define GET_SLAVE_DATA_TIMEOUT_CNT      200                 // 获取从机数据超时计数
#define GET_SLAVE_DATA_TIMEOUT_CNT      1500                 // 获取从机数据超时计数

#define MASTER                          1                   // 逆变器主机
#define SLAVE                           0                   // 逆变器从机

#define ONLINE                          1                   // 设备上线
#define OFFLINE                         0                   // 设备下线

#define ZTE_CLIENT                      0                   // 自研网管
#define OTH_CLIENT                      1                   // 外部网管
#define MAX_CLIENT                      2                   // MQTT客户端最大支持数量


#define SITE_ID_LEN                     17
#define MIN_IP_LEN                      7

#define BAD_DATA                        0xBD                // 将数据设置为未初始化

#define GET_PARA_TYPE(val)              (((val) >> 12) & 0xF)       // 参数量取数据类型
#define GET_CONTROL_TYPE(val)           (((val) >> 28) & 0xF)       // 控制量取数据类型
#define GET_REAL_TYPE(val)              (((val) >> 28) & 0xF)       // 实时量取数据类型

#define GET_DATA_NUM(val)               ((val) & 0xFF)              // 获取数据数量
#define GET_DATA_ID_NO_NUM(val)         ((val) & 0xFFFFFF00)        // 获取除去数据数量之后的值
#define GET_CMD(val)                    (((val) >> 20) & 0xF)       // 获取对外SID数据类别
#define GET_TYPE(val)                   (((val) >> 16) & 0xF)       // 获取对外SID数据类型

#define CTRL_START_IV                   1       // 启动IV扫描
#define CTRL_CLOSE_IV                   2       // 停止IV扫描
#define CTRL_POWER_ON                   1       // 控制功率开
#define CTRL_POWER_OFF                  0       // 控制功率关
#define CTRL_POWER_ON_VAL               1       // 控制功率开的val值
#define CTRL_POWER_OFF_VAL              2       // 控制功率关的val值
#define CTRL_RESTART_SYSTEM             1       // 系统重启的val值
#define CTRL_START_CHECK_ISOLATE        3       // 启动绝缘阻抗检测的val值
#define CTRL_START_CHECK_AFCI           4       // 启动AFCI自检的val值
#define CTRL_GET_AFCI_FILE              1       // 获取AFCI文件的val值

#define PROPERTIES_OBJ                 "properties"
#define RESULT_OBJ                     "result"
#define TIME_OBJ                       "time"
#define MSGID_OBJ                      "msgId"

#define DEV_OFF                        0        // 设备关机状态
#define DEV_ON                         1        // 设备开机状态
#define DEV_STANDBY                    2        // 设备待机中

#define MAX_ALARM_COUNT                100      // 主机最大存储告警数量

typedef enum {
    CMD_REALDATA = 1,
    CMD_PARA,
    CMD_ALARM,
    CMD_ALARM_LEVER,
    CMD_ALARM_RELAY,
    CMD_CONTROL,
    CMD_MAX
} cmd_id_e;

typedef enum {
    MQTT_SUCCESS = 0,
    MQTT_NULL_ERR,        // 传参为空
    MQTT_SID_ERR,         // sid有误
    MQTT_SCOPE_ERR,       // 不在参数范围内
    MQTT_CMD_ERR,         // 功能不支持
    MQTT_FORMAT_ERR,      // 格式不正确
    MQTT_NO_FILE_ERR,     // 文件不存在
    MQTT_REQ_ERR,         // 请求异常
    MQTT_CONNECT_ERR,     // 无法访问
    MQTT_REQ_TIMEOUT_ERR, // 请求超时
    MQTT_ERR_MAX,
} mqtt_err_e;

typedef struct
{
    char mnte_ver[STR_LEN_32];  // 监控版本
    char mstc_ver[STR_LEN_32];  // 主控版本
    char auxc_ver[STR_LEN_32];  // 辅控版本
    char cpld_ver[STR_LEN_32];  // cpld版本
} ms_ver_t;

typedef struct
{
    int sid;
    int (*sp_func)(int is_mst, int addr, int val);
} control_sp_t;

typedef struct
{
    char type;
    int (*get_data)(int is_mst, char addr, int sid, int cmd, cJSON* val);
    int (*set_data)(int is_mst, int addr, int sid, int type, cJSON* val);
} data_tab_t;

typedef struct
{
    unsigned int sid_offset;     /// 实时量sid
    void* values;       /// 存储值的指针,有多维数据，依次存储
} real_data_tab_t;

void set_uuid(const char* uuid);
void get_uuid(char* uuid);
void set_site_id(int which, const char* id);
void get_site_id(int which, char* id);
void set_device_id(int idx, const char* id, int len);
void get_device_id(int idx, char* id);
int get_master_addr(void);
void set_master_addr(int addr);
int set_soft_ver(int addr);
ms_ver_t* get_soft_ver(int addr);
unsigned short get_slave_msg_cnt(void);
void set_slave_msg_cnt(unsigned short cnt);
void set_mqtt_client(int which, mqtt_client_t* client);
mqtt_client_t* get_mqtt_client(int which);
int which_client(mqtt_client_t* client);
int is_client_conn_normal(int which);
mqtt_err_e mqtt_get_data(int is_mst, char addr, int sid, cJSON* val);
mqtt_err_e mqtt_set_data(int is_mst, int dev_id, int sid, cJSON* val);
uint64_t mqtt_get_timestamp(void);
cJSON* mqtt_parse_msg(const char* in, char* uuid);
unsigned char get_mode_addr(unsigned char* addr);
mqtt_err_e parse_msg_sn(const char* topic, int* sn_idx);
int send_mqtt_data(mqtt_client_t* client, const char* topic_name, cJSON* json_data);
int search_alarm_for_po(int po_alarm_id);
int alarm_is_mqtt_alarm(int alarm_id);
cJSON* pack_property(int is_mst, char addr, char is_immediate_submit);
void send_get_msg_to_slave(char addr, int msg);
int is_slave_data_invalid(int msg);
int judge_iv_file_exit();
int check_control_id(int sid, int id);
unsigned char get_addr_by_device_id(char* id);
void mqtt_backup_power_ctrl_para(int sid);
int set_master_soft_ver();
char is_immediate_submit_sid(unsigned int sid);
char is_immediate_submit_master_sid(unsigned int sid);
int mqtt_get_master_slave_data(int is_mst, char is_immediate_submit, cJSON* item, char addr, int sid_index, int id_loop);
int collect_para_sids(sid_list_info_t* sid_list_info, int sid, int is_mst);
int mqtt_map_dev_status(int sid, cJSON* item);
int save_alarm_when_disconn(alarm_mqtt_t* alarm_mqtt);
void clean_s_alarm_list();
cJSON* pack_alarm_property(void);
int get_latest_ala_list(void);
int get_one_comm(int is_mst, char addr, int sid, int cmd, void* data);
int set_power_off_on_para(int val);
int mqtt_restart_system(int is_mst, int addr, int val);
int  deal_other_pv_control_cmd(int is_mst, int addr, int val);
int handle_inform_4g_reset_device();
int mqtt_get_afci_file(int is_mst, int addr, int val);
int mqtt_clean_his_data(int is_mst, int addr, int val);
int mqtt_clean_alarm(int is_mst, int addr, int val);
int mqtt_clean_his_event(int is_mst, int addr, int val);
int mqtt_clean_his_energy(int is_mst, int addr, int val);
int mqtt_clean_iv_data(int is_mst, int addr, int val);
int mqtt_clean_fault_record(int is_mst, int addr, int val);
int trig_fault_record(int is_mst, int addr, int val);
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif // _mqtt_common_h_
