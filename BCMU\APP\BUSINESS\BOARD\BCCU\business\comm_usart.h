#ifndef _COMM_USART_H_
#define _COMM_USART_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "device_type.h"

#define SAMPLE_USART_DELAY    10  // unit:ms

typedef struct {
    void*        data;
    cmd_buf_t*   cmd_buff;
    link_inst_t* link_inst;
    rt_mq_t      north_mq;
}north_mgr_t;

void* usart_comm_init(void* param);
void process_usart_comm(void* parameter);

#ifdef __cplusplus
}
#endif      //< __cplusplus

#endif      //< _COMM_CAN_H_
