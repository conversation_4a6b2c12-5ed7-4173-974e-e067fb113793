#ifndef _NET_CONNECT_MODE_H
#define _NET_CONNECT_MODE_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

#include "data_type.h"

#define NET_CONN_WAITE      (10*60*1000)                //10分钟无通信判断超时
#define NET_MODE_WIFI       3
#define NET_MODE_4G         2
#define NET_MODE_RS485      1
#define NET_MODE_NO_CONN    0

#define NET_MODE_MQTT       1
#define NET_MODE_MODBUS     0
#define DEV_NAME_WIFI       "----"      //wifi通讯棒名称待确认
#define DEV_NAME_4G         "p0"


void net_connect_mode_handel(unsigned char conn_protocol, unsigned int net_conn_mode_sid);
int create_net_conn_status_timer(void);
void reset_net_conn_status_timer(void);
void net_connect_timeout(void *parameter);
unsigned char judge_net_connect_mode(unsigned char conn_protocol);

#ifdef __cplusplus
}
#endif  //  __cplusplus
#endif //_NET_CONNECT_MODE_H