#ifndef _CIRCLE_BUFF_H
#define _CIRCLE_BUFF_H

#ifdef __cplusplus
extern "C" {
#endif    //  __cplusplus

#include "common.h"

#define	APP_DATA_LEN	          256
#define	APP_FRAME_HEAD_LEN	      14
#define MAX_FAIL_NUM              50
#define MAX_UPDATE_DATA_CACHE_NUM 10
#define MAX_UPDATE_DATA_CACHE_VIRTURE_NUM (MAX_UPDATE_DATA_CACHE_NUM * 2)

typedef struct
{
	uint32_t   ulFrameNo;
	uint8_t	   aucData[APP_DATA_LEN];
	uint8_t    auFrameHead[APP_FRAME_HEAD_LEN];
}T_AppDataSaveStruct;

uint32_t read_circle_buff(T_AppDataSaveStruct* buff, int32_t num);
uint32_t write_circle_buff(T_AppDataSaveStruct* buff, int32_t num);
uint32_t init_circle_buff(void);
uint32_t reset_circle_buff(void);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _CIRCLE_BUFF_H
