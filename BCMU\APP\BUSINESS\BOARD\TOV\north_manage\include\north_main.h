/**
 * @brief dev_inverter main板北向通信头文件
 */

#ifndef _TOV_NORTH_MAIN_H_
#define _TOV_NORTH_MAIN_H_

#ifdef __cplusplus
extern "C" {
#endif

#include <stdio.h>
#include <rtthread.h>
#include <rtdevice.h>
#include "linklayer.h"
#include "protocol_layer.h"
#include "device_type.h"
#include "sps.h"
#include "parse_layer.h"

#define MAX_NORTH_COMM_BUFF_LEN  1024

typedef struct {
    void*        data;
    cmd_buf_t*   cmd_buff;
    link_inst_t* link_inst;
    rt_mq_t      north_mq;
} north_mgr_t;

void* init_north(void* param);
void north_main(void* param);
char init_dev_init_tab(void);
#ifdef __cplusplus
}
#endif      //< __cplusplus

#endif      //< _TOV_NORTH_MAIN_H_
