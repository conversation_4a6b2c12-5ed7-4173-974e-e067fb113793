
#include "comm_can.h"
#include "protocol_bottom_comm.h"
#include "sps.h"
#include "device_num.h"
#include "cmd.h"
#include "flash.h"

static bottom_comm_cmd_head_t cmd_req[] = 
{
    {BOTTOM_PROTO_TYPE_DOWN, R552_UPDATE_CAN_TRIG, CMD_APPEND_UPDATE_REQ, BOTTOM_RTN_APP_NOACK},//升级触发请求帧
};
/* 命令应答   */
static bottom_comm_cmd_head_t cmd_ack[] = 
{
    {BOTTOM_PROTO_TYPE_DOWN, R552_UPDATE_CAN_TRIG, CMD_APPEND_UPDATE_ACK, BOTTOM_RTN_APP_CORRECT},//升级触发应答帧
};
/* 通信协议命令表 */
static cmd_t no_poll_tab[] = 
{
    {R552_UPDATE_CAN_TRIG, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(bottom_comm_cmd_head_t), SELF_PACK, SELF_PARSE},
};
/* 通信协议命令表 */
static cmd_t poll_cmd_tab[] = 
{
    {0,}
};
/* 北向CAN通信设备表 */
static dev_type_t dev_update_can = 
{
    DEV_BMS_CAN_DOWNLOAD, R552_NUM, PROTOCOL_BOTTOM_COMM, LINK_BMS_CAN, CAN1_R_BUFF_LEN, CAN1_S_BUFF_LEN, BOTTOM_R321_TYPE, &no_poll_tab[0], &poll_cmd_tab[0]
};
/* 北向CAN通信设备功能表初始化 */
dev_type_t* init_dev_update_can(void) 
{
    return &dev_update_can;
}

