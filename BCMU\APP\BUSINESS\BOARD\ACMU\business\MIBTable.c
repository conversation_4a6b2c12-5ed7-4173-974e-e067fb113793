/*
 * @Author: dml00265523
 * @Date: 2025-06-13 10:35:36
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-09-18 16:31:51
 * @Description: 
 * @FilePath: \BCMU\APP\BUSINESS\BOARD\ACMU\business\gui_data_interface.c
 */
#include "data_type.h"
#include "realdata_id_in.h"
#include "para_id_in.h"
#include "realdata_save.h"
#include "para_manage.h"
#include "gui_data_interface.h"
#include "utils_rtthread_security_func.h"
#include "alarm_manage.h"
#include "alarm_id_in.h"
#include "his_record.h"
#include "const_define_in.h"
#include "utils_data_type_conversion.h"

MIBTable_ParaNode Para_MIBTable[] =
{
    // 系统参数
    {ACMU_PARA_ID_AC_PHASE_VOLTAGE_MAX_OFFSET, ACMU_PARA_ID_AC_PHASE_VOLTAGE_MAX, ID1_ACMU_PARA, ID2_PR_VOLTHIGH, 1, TYPE_INT16U, 2, PARATYPE_RUNNING, 1, "V"}, // 交流相电压高阈值
    {ACMU_PARA_ID_AC_PHASE_VOLTAGE_MIN_OFFSET, ACMU_PARA_ID_AC_PHASE_VOLTAGE_MIN, ID1_ACMU_PARA, ID2_PR_VOLTLOW, 1, TYPE_INT16U, 2, PARATYPE_RUNNING, 1, "V"}, // 交流相电压低阈值
    {ACMU_PARA_ID_PHASE_VOLTAGE_UNBALANCE_OFFSET, ACMU_PARA_ID_PHASE_VOLTAGE_UNBALANCE, ID1_ACMU_PARA, ID2_PR_PHUNBL, 1, TYPE_INT16U, 2, PARATYPE_RUNNING, 1, ""}, // 相电压不平衡阈值
    {ACMU_PARA_ID_AC_IN_CURRENT_MAX_OFFSET, ACMU_PARA_ID_AC_IN_CURRENT_MAX, ID1_ACMU_PARA, ID2_PR_CURRHIGH, 1, TYPE_INT16U, 2, PARATYPE_RUNNING, 1, "A"}, // 交流电流高阈值
    {ACMU_PARA_ID_AC_LINE_VOLTAGE_MAX_OFFSET, ACMU_PARA_ID_AC_LINE_VOLTAGE_MAX, ID1_ACMU_PARA, ID2_PR_PPVOLTHIGH, 1, TYPE_INT16S, 2, PARATYPE_RUNNING, 1, "V"}, // 交流线电压高阈值
    {ACMU_PARA_ID_AC_LINE_VOLTAGE_MIN_OFFSET, ACMU_PARA_ID_AC_LINE_VOLTAGE_MIN, ID1_ACMU_PARA, ID2_PR_PPVOLTLOW, 1, TYPE_INT16S, 2, PARATYPE_RUNNING, 1, "V"}, // 交流线电压低阈值
    {ACMU_PARA_ID_SYS_AC_MUTUAL_INDUCTANCE_CONFIGURATION_OFFSET, ACMU_PARA_ID_SYS_AC_MUTUAL_INDUCTANCE_CONFIGURATION, ID1_ACMU_PARA, ID2_PR_CURRCFG, 3, TYPE_INT8U, 1, PARATYPE_CONFIG}, // 用户自定义(交流互感配置-1#)
    {ACMU_PARA_ID_SYS_AC_OUT_SWITCH_CONFIGURATION_OFFSET, ACMU_PARA_ID_SYS_AC_OUT_SWITCH_CONFIGURATION, ID1_ACMU_PARA, ID2_PR_OUTSWCFG, 12, TYPE_INT8U, 1, PARATYPE_CONFIG}, // 输出空开配置
    {ACMU_PARA_ID_AC_MUTUAL_INDUCTANCE_TYPE_OFFSET, ACMU_PARA_ID_AC_MUTUAL_INDUCTANCE_TYPE, ID1_ACMU_PARA, ID2_PR_CURRTRANSTYPE, 3, TYPE_INT16U, 2, PARATYPE_CONFIG}, // 用户自定义(交流互感类型-1#)
    {ACMU_PARA_ID_AC_METER_CONFIGURATION_OFFSET, ACMU_PARA_ID_AC_METER_CONFIGURATION, ID1_ACMU_PARA, ID2_PR_ACEMCFG, 1, TYPE_INT8U, 1, PARATYPE_CONFIG}, // 用户自定义(交流电表配置) 
    {ACMU_PARA_ID_AC_PHASE_VOLT_ZERO_OFFSET, ACMU_PARA_ID_AC_PHASE_VOLT_ZERO, ID1_ACMU_PARA, ID2_PR_VOLTOFFST, 3, TYPE_INT8S, 1, PARATYPE_CONFIG, 1, ""}, // 交流相电压零点
    {ACMU_PARA_ID_AC_PHASE_VOLT_SLOPE_OFFSET, ACMU_PARA_ID_AC_PHASE_VOLT_SLOPE, ID1_ACMU_PARA, ID2_PR_VOLTSLOPE, 3, TYPE_INT16S, 2, PARATYPE_CONFIG, 1, ""}, // 交流相电压斜率
    {ACMU_PARA_ID_AC_PHASE_CURR_ZERO_OFFSET, ACMU_PARA_ID_AC_PHASE_CURR_ZERO, ID1_ACMU_PARA, ID2_PR_CURROFFST, 3, TYPE_INT8S, 1, PARATYPE_CONFIG, 1, ""}, // 交流相电流零点
    {ACMU_PARA_ID_AC_PHASE_CURR_SLOPE_OFFSET, ACMU_PARA_ID_AC_PHASE_CURR_SLOPE, ID1_ACMU_PARA, ID2_PR_CURRSLOPE, 3, TYPE_INT16S, 2, PARATYPE_CONFIG, 1, ""}, // 交流相电流斜率
    {ACMU_PARA_ID_ENV_TEMP_ZERO_POINT_OFFSET, ACMU_PARA_ID_ENV_TEMP_ZERO_POINT, ID1_ENV_PARA, ID2_PR_ETOFFSET, 1, TYPE_INT8S, 1, PARATYPE_CONFIG, 1, ""}, // 环境温度零点
    {ACMU_PARA_ID_ENV_HUMIDITY_ZERO_POINT_OFFSET, ACMU_PARA_ID_ENV_HUMIDITY_ZERO_POINT, ID1_ENV_PARA, ID2_PR_EHOFFSET, 1, TYPE_INT8S, 1, PARATYPE_CONFIG, 1, ""}, // 环境湿度零点
    {ACMU_PARA_ID_INRELAYTTL_OFFSET, ACMU_PARA_ID_INRELAYTTL, ID1_ACMU_PARA, ID2_PR_INRLYALMTTL, RELAY_NUM, TYPE_INT8U, 1, PARATYPE_CONFIG }, // 输入干节点告警电平
    {ACMU_PARA_ID_INRELAYNAME_OFFSET, ACMU_PARA_ID_INRELAYNAME, ID1_ACMU_PARA, ID2_PR_INRLYNAME, RELAY_NUM, TYPE_STRING, LEN_RELAYNAME, PARATYPE_CONFIG }, // 输入干节点名称
    {ACMU_PARA_ID_INRELAYLEVEL_OFFSET, ACMU_PARA_ID_INRELAYLEVEL, ID1_ACMU_PARA, ID2_PR_INRLYALMLEVEL, RELAY_NUM, TYPE_INT8U, 1, PARATYPE_CONFIG },    // 输入干节点告警级别
    {ACMU_PARA_ID_SYS_BUZZER_SWITCH_OFFSET, ACMU_PARA_ID_SYS_BUZZER_SWITCH, ID1_ACMU_PARA, ID2_PR_BUZZER, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 蜂鸣器开关
    {ACMU_PARA_ID_HISDATA_SAVE_INTERVAL_OFFSET, ACMU_PARA_ID_HISDATA_SAVE_INTERVAL, ID1_ACMU_PARA, ID2_PR_HISDATAINV, 1, TYPE_INT8U, 1, PARATYPE_CONFIG, 1, "H"},    // 历史数据保存时间间隔
    {ACMU_PARA_ID_SYS_SERIAL_PORT_BAUD_RATE_SETTING_OFFSET, ACMU_PARA_ID_SYS_SERIAL_PORT_BAUD_RATE_SETTING, ID1_ACMU_PARA, ID2_PR_COMRATE, 2, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 串口波特率设置
    {ACMU_PARA_ID_SYS_LOCAL_MACHINE_ADDRESS_OFFSET, ACMU_PARA_ID_SYS_LOCAL_MACHINE_ADDRESS, ID1_ACMU_PARA, ID2_PR_LOCALADD, 1, TYPE_INT16U, 2, PARATYPE_CONFIG },    // 本机地址
    {ACMU_PARA_ID_SYS_LANGUAGE_SETTING_OFFSET, ACMU_PARA_ID_SYS_LANGUAGE_SETTING, ID1_ACMU_PARA, ID2_PR_LANGUAGE, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 语言设置
    {ACMU_PARA_ID_SYS_MENU_PASERWORD_SETTING_OFFSET, ACMU_PARA_ID_SYS_MENU_PASERWORD_SETTING, ID1_ACMU_PARA, ID2_PR_PASERWORD, 1, TYPE_STRING, 4, PARATYPE_CONFIG},    // 菜单口令设置
    {ACMU_PARA_ID_USAGE_SCENARIO_OFFSET, ACMU_PARA_ID_USAGE_SCENARIO, ID1_ACMU_PARA, ID2_PR_USAGESCENARIO, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 使用场景
    {ACMU_PARA_ID_AC_OUTPUT_MUTUAL_INDUCTANCE_TYPE_OFFSET, ACMU_PARA_ID_AC_OUTPUT_MUTUAL_INDUCTANCE_TYPE, ID1_ACMU_PARA, ID2_PR_OUTCURRTRANSTYPE, 1, TYPE_INT16U, 2, PARATYPE_CONFIG, 1, ""}, // 用户自定义(输出电流互感类型)

    // 环境参数
    {ACMU_PARA_ID_TEMPERATURE_SENSOR_UPPER_OFFSET, ACMU_PARA_ID_TEMPERATURE_SENSOR_UPPER, ID1_ENV_PARA, ID2_PR_ETHIGH, 1, TYPE_INT16S, 2, PARATYPE_RUNNING, 1, "℃" }, // 环境温度高阈值
    {ACMU_PARA_ID_TEMPERATURE_SENSOR_LOWER_OFFSET, ACMU_PARA_ID_TEMPERATURE_SENSOR_LOWER, ID1_ENV_PARA, ID2_PR_ETLOW, 1, TYPE_INT16S, 2, PARATYPE_RUNNING, 1, "℃" },  // 环境温度低阈值
    {ACMU_PARA_ID_HUMIDITY_SENSOR_UPPER_OFFSET, ACMU_PARA_ID_HUMIDITY_SENSOR_UPPER, ID1_ENV_PARA, ID2_PR_EHHIGH, 1, TYPE_INT16U, 2, PARATYPE_RUNNING, 1, "%" },    // 环境湿度高阈值
    {ACMU_PARA_ID_HUMIDITY_SENSOR_LOWER_OFFSET, ACMU_PARA_ID_HUMIDITY_SENSOR_LOWER, ID1_ENV_PARA, ID2_PR_EHLOW, 1, TYPE_INT16U, 2, PARATYPE_RUNNING, 1, "%" },     // 环境湿度低阈值

    // 告警级别，总共12+12个
    // 告警级别 12
    {ACMU_ALM_ID_TOTAL_ALARM_LEVEL, ACMU_ALM_ID_TOTAL_ALARM_LEVEL, ID1_ACMU_ALARM, ID2_ALM_COMMON, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 总告警
    {ACMU_ALM_ID_AC_POWER_OFF_LEVEL, ACMU_ALM_ID_AC_POWER_OFF_LEVEL, ID1_ACMU_ALARM, ID2_ALM_PWROFF, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 交流停电
    {ACMU_ALM_ID_AC_PHASE_LOST_LEVEL, ACMU_ALM_ID_AC_PHASE_LOST_LEVEL, ID1_ACMU_ALARM, ID2_ALM_PHASELOST, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 交流缺相
    {ACMU_ALM_ID_PHASE_VOLT_UNBALANCE_LEVEL, ACMU_ALM_ID_PHASE_VOLT_UNBALANCE_LEVEL, ID1_ACMU_ALARM, ID2_ALM_PHASEUNBL, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 交流相电压不平衡
    {ACMU_ALM_ID_PHASE_VOLT_HIGH_ALARM_LEVEL, ACMU_ALM_ID_PHASE_VOLT_HIGH_ALARM_LEVEL, ID1_ACMU_ALARM, ID2_ALM_ACVOLTHIGH, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 交流相电压高
    {ACMU_ALM_ID_PHASE_VOLT_LOW_ALARM_LEVEL, ACMU_ALM_ID_PHASE_VOLT_LOW_ALARM_LEVEL, ID1_ACMU_ALARM, ID2_ALM_ACVOLTLOW, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 交流相电压低
    {ACMU_ALM_ID_PHASE_CURR_HIGH_ALARM_LEVEL, ACMU_ALM_ID_PHASE_CURR_HIGH_ALARM_LEVEL, ID1_ACMU_ALARM, ID2_ALM_ACCURRHIGH, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 交流相电流高
    {ACMU_ALM_ID_SPD_C_LEVEL, ACMU_ALM_ID_SPD_C_LEVEL, ID1_ACMU_ALARM, ID2_ALM_SPD_C, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // C级防雷器异常
    {ACMU_ALM_ID_AC_OUTPUT_SWITCH_DISCONNECTION_LEVEL, ACMU_ALM_ID_AC_OUTPUT_SWITCH_DISCONNECTION_LEVEL, ID1_ACMU_ALARM, ID2_ALM_OUTSWITCH, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 输出空开断
    {ACMU_ALM_ID_AC_LINE_VOLT_HIGH_LEVEL, ACMU_ALM_ID_AC_LINE_VOLT_HIGH_LEVEL, ID1_ACMU_ALARM, ID2_ALM_ACVOLTPPHIGH, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 交流线电压高
    {ACMU_ALM_ID_AC_LINE_VOLT_LOW_LEVEL, ACMU_ALM_ID_AC_LINE_VOLT_LOW_LEVEL, ID1_ACMU_ALARM, ID2_ALM_ACVOLTPPLOW, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 交流线电压低
    {ACMU_ALM_ID_AC_METER_DISCONNECTED_LEVEL, ACMU_ALM_ID_AC_METER_DISCONNECTED_LEVEL, ID1_ACMU_ALARM, ID2_ALM_ACEMCOMFAIL, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 交流电表通讯断
    // ENV告警级别 12
    {ACMU_ALM_ID_ENV_TEMP_HIGH_ALARM_LEVEL, ACMU_ALM_ID_ENV_TEMP_HIGH_ALARM_LEVEL, ID1_ENV_ALARM, ID2_ALM_ETHIGH, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 环境温度高
    {ACMU_ALM_ID_ENV_TEMP_LOW_ALARM_LEVEL, ACMU_ALM_ID_ENV_TEMP_LOW_ALARM_LEVEL, ID1_ENV_ALARM, ID2_ALM_ETLOW, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 环境温度低
    {ACMU_ALM_ID_ENV_HUMIDITY_HIGH_ALARM_LEVEL, ACMU_ALM_ID_ENV_HUMIDITY_HIGH_ALARM_LEVEL, ID1_ENV_ALARM, ID2_ALM_EHHIGH, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 环境湿度高
    {ACMU_ALM_ID_ENV_HUMIDITY_LOW_ALARM_LEVEL, ACMU_ALM_ID_ENV_HUMIDITY_LOW_ALARM_LEVEL, ID1_ENV_ALARM, ID2_ALM_EHLOW, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 环境湿度低
    {ACMU_ALM_ID_FUMES_SENSOR_ALARM_LEVEL, ACMU_ALM_ID_FUMES_SENSOR_ALARM_LEVEL, ID1_ENV_ALARM, ID2_ALM_SMOG, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 烟雾
    {ACMU_ALM_ID_FLOOD_SENSOR_ALARM_LEVEL, ACMU_ALM_ID_FLOOD_SENSOR_ALARM_LEVEL, ID1_ENV_ALARM, ID2_ALM_FLOOD, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 水淹
    {ACMU_ALM_ID_DOORMAT_SENSOR_ALARM_LEVEL, ACMU_ALM_ID_DOORMAT_SENSOR_ALARM_LEVEL, ID1_ENV_ALARM, ID2_ALM_DOORMAG, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 门磁告警
    {ACMU_ALM_ID_ENV_TEMP_SENSOR_INVALID_ALARM_LEVEL, ACMU_ALM_ID_ENV_TEMP_SENSOR_INVALID_ALARM_LEVEL, ID1_ENV_ALARM, ID2_ALM_ETSENSOR, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 环境温度失效
    {ACMU_ALM_ID_ENV_HUMIDITY_SENSOR_INVALID_ALARM_LEVEL, ACMU_ALM_ID_ENV_HUMIDITY_SENSOR_INVALID_ALARM_LEVEL, ID1_ENV_ALARM, ID2_ALM_EHSENSOR, 1, TYPE_INT8U, 1, PARATYPE_CONFIG },    // 环境湿度失效
    {ACMU_ALM_ID_OIL_ENGINE_START_UP_ALARM_LEVEL, ACMU_ALM_ID_OIL_ENGINE_START_UP_ALARM_LEVEL, ID1_ACMU_ALARM, ID2_ALM_STARTGEN, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 油机启动

    {ACMU_ALM_ID_INPUT_RELAY1_ALARM_LEVEL, ACMU_ALM_ID_INPUT_RELAY1_ALARM_LEVEL, ID1_ACMU_ALARM, ID2_ALM_INRELAY, 1, TYPE_INT8U, 1, PARATYPE_CONFIG },    // 输入干节点1告警级别
    {ACMU_ALM_ID_INPUT_RELAY2_ALARM_LEVEL, ACMU_ALM_ID_INPUT_RELAY2_ALARM_LEVEL, ID1_ACMU_ALARM, ID2_ALM_INRELAY, 1, TYPE_INT8U, 1, PARATYPE_CONFIG },    // 输入干节点2告警级别
    {ACMU_ALM_ID_INPUT_RELAY3_ALARM_LEVEL, ACMU_ALM_ID_INPUT_RELAY3_ALARM_LEVEL, ID1_ACMU_ALARM, ID2_ALM_INRELAY, 1, TYPE_INT8U, 1, PARATYPE_CONFIG },    // 输入干节点3告警级别
    {ACMU_ALM_ID_INPUT_RELAY4_ALARM_LEVEL, ACMU_ALM_ID_INPUT_RELAY4_ALARM_LEVEL, ID1_ACMU_ALARM, ID2_ALM_INRELAY, 1, TYPE_INT8U, 1, PARATYPE_CONFIG },    // 输入干节点4告警级别

    // 告警干接点，总共12+12个
    // 告警干接点 12
    {ACMU_ALM_ID_TOTAL_ALARM_RELAY, ACMU_ALM_ID_TOTAL_ALARM_RELAY,ID1_ACMU_OUTRLY, ID2_ALM_COMMON, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 总告警
    {ACMU_ALM_ID_AC_POWER_OFF_RELAY, ACMU_ALM_ID_AC_POWER_OFF_RELAY,ID1_ACMU_OUTRLY, ID2_ALM_PWROFF, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 交流停电
    {ACMU_ALM_ID_AC_PHASE_LOST_RELAY, ACMU_ALM_ID_AC_PHASE_LOST_RELAY, ID1_ACMU_OUTRLY, ID2_ALM_PHASELOST, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 交流缺相
    {ACMU_ALM_ID_PHASE_VOLT_UNBALANCE_RELAY, ACMU_ALM_ID_PHASE_VOLT_UNBALANCE_RELAY, ID1_ACMU_OUTRLY, ID2_ALM_PHASEUNBL, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 交流相电压不平衡
    {ACMU_ALM_ID_PHASE_VOLT_HIGH_ALARM_RELAY, ACMU_ALM_ID_PHASE_VOLT_HIGH_ALARM_RELAY,ID1_ACMU_OUTRLY, ID2_ALM_ACVOLTHIGH, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 交流相电压高
    {ACMU_ALM_ID_PHASE_VOLT_LOW_ALARM_RELAY, ACMU_ALM_ID_PHASE_VOLT_LOW_ALARM_RELAY, ID1_ACMU_OUTRLY, ID2_ALM_ACVOLTLOW, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 交流相电压低
    {ACMU_ALM_ID_PHASE_CURR_HIGH_ALARM_RELAY, ACMU_ALM_ID_PHASE_CURR_HIGH_ALARM_RELAY, ID1_ACMU_OUTRLY, ID2_ALM_ACCURRHIGH, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 交流相电流高
    {ACMU_ALM_ID_SPD_C_RELAY, ACMU_ALM_ID_SPD_C_RELAY, ID1_ACMU_OUTRLY, ID2_ALM_SPD_C, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // C级防雷器异常
    {ACMU_ALM_ID_AC_OUTPUT_SWITCH_DISCONNECTION_RELAY, ACMU_ALM_ID_AC_OUTPUT_SWITCH_DISCONNECTION_RELAY, ID1_ACMU_OUTRLY, ID2_ALM_OUTSWITCH, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 输出空开断
    {ACMU_ALM_ID_AC_LINE_VOLT_HIGH_RELAY, ACMU_ALM_ID_AC_LINE_VOLT_HIGH_RELAY, ID1_ACMU_OUTRLY, ID2_ALM_ACVOLTPPHIGH, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 交流线电压高
    {ACMU_ALM_ID_AC_LINE_VOLT_LOW_RELAY, ACMU_ALM_ID_AC_LINE_VOLT_LOW_RELAY,ID1_ACMU_OUTRLY, ID2_ALM_ACVOLTPPLOW, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 交流线电压低
    {ACMU_ALM_ID_AC_METER_DISCONNECTED_RELAY, ACMU_ALM_ID_AC_METER_DISCONNECTED_RELAY, ID1_ACMU_OUTRLY, ID2_ALM_ACEMCOMFAIL, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 交流电表通讯断
    // ENV告警对应输出干结点 12
    {ACMU_ALM_ID_ENV_TEMP_HIGH_ALARM_RELAY, ACMU_ALM_ID_ENV_TEMP_HIGH_ALARM_RELAY, ID1_ENV_OUTRLY, ID2_ALM_ETHIGH, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 环境温度高
    {ACMU_ALM_ID_ENV_TEMP_LOW_ALARM_RELAY, ACMU_ALM_ID_ENV_TEMP_LOW_ALARM_RELAY, ID1_ENV_OUTRLY, ID2_ALM_ETLOW, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 环境温度低
    {ACMU_ALM_ID_ENV_HUMIDITY_HIGH_ALARM_RELAY, ACMU_ALM_ID_ENV_HUMIDITY_HIGH_ALARM_RELAY, ID1_ENV_OUTRLY, ID2_ALM_EHHIGH, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 环境湿度高
    {ACMU_ALM_ID_ENV_HUMIDITY_LOW_ALARM_RELAY, ACMU_ALM_ID_ENV_HUMIDITY_LOW_ALARM_RELAY, ID1_ENV_OUTRLY, ID2_ALM_EHLOW, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 环境湿度低
    {ACMU_ALM_ID_FUMES_SENSOR_ALARM_RELAY, ACMU_ALM_ID_FUMES_SENSOR_ALARM_RELAY, ID1_ENV_OUTRLY, ID2_ALM_SMOG, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 烟雾
    {ACMU_ALM_ID_FLOOD_SENSOR_ALARM_RELAY, ACMU_ALM_ID_FLOOD_SENSOR_ALARM_RELAY, ID1_ENV_OUTRLY, ID2_ALM_FLOOD, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 水淹
    {ACMU_ALM_ID_DOORMAT_SENSOR_ALARM_RELAY, ACMU_ALM_ID_DOORMAT_SENSOR_ALARM_RELAY, ID1_ENV_OUTRLY, ID2_ALM_DOORMAG, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 门磁
    {ACMU_ALM_ID_ENV_TEMP_SENSOR_INVALID_ALARM_RELAY, ACMU_ALM_ID_ENV_TEMP_SENSOR_INVALID_ALARM_RELAY, ID1_ENV_OUTRLY, ID2_ALM_ETSENSOR, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 环境温度失效
    {ACMU_ALM_ID_ENV_HUMIDITY_SENSOR_INVALID_ALARM_RELAY, ACMU_ALM_ID_ENV_HUMIDITY_SENSOR_INVALID_ALARM_RELAY, ID1_ENV_OUTRLY, ID2_ALM_EHSENSOR, 1, TYPE_INT8U, 1, PARATYPE_CONFIG },    // 环境湿度失效
    {ACMU_ALM_ID_OIL_ENGINE_START_UP_ALARM_RELAY, ACMU_ALM_ID_OIL_ENGINE_START_UP_ALARM_RELAY, ID1_ACMU_STATUS_OUTRLY, ID2_ALM_STARTGEN, 1, TYPE_INT8U, 1, PARATYPE_CONFIG},    // 油机启动

    {ACMU_ALM_ID_INPUT_RELAY1_ALARM_RELAY, ACMU_ALM_ID_INPUT_RELAY1_ALARM_RELAY, ID1_ACMU_OUTRLY, ID2_ALM_INRELAY, 1, TYPE_INT8U, 1, PARATYPE_CONFIG },    // 输入干节点1告警级别
    {ACMU_ALM_ID_INPUT_RELAY2_ALARM_RELAY, ACMU_ALM_ID_INPUT_RELAY2_ALARM_RELAY, ID1_ACMU_OUTRLY, ID2_ALM_INRELAY, 1, TYPE_INT8U, 1, PARATYPE_CONFIG },    // 输入干节点2告警级别
    {ACMU_ALM_ID_INPUT_RELAY3_ALARM_RELAY, ACMU_ALM_ID_INPUT_RELAY3_ALARM_RELAY, ID1_ACMU_OUTRLY, ID2_ALM_INRELAY, 1, TYPE_INT8U, 1, PARATYPE_CONFIG },    // 输入干节点3告警级别
    {ACMU_ALM_ID_INPUT_RELAY4_ALARM_RELAY, ACMU_ALM_ID_INPUT_RELAY4_ALARM_RELAY, ID1_ACMU_OUTRLY, ID2_ALM_INRELAY, 1, TYPE_INT8U, 1, PARATYPE_CONFIG },    // 输入干节点4告警级别

    {0, 0, 0, 0, 0, 0, 0, 0},
};

/* 
   map表告警顺序固定，通过序号可以索引到字库中的字节
 */ 
const alarm_map_t alarm_map[] = {
  	{ACMU_ALM_ID_TOTAL_ALARM, ID1_ACMU_ALARM, ID2_ALM_COMMON, 1 },	// 总告警
	{ACMU_ALM_ID_AC_POWER_OFF, ID1_ACMU_ALARM, ID2_ALM_PWROFF, 1 },	// 交流停电
	{ACMU_ALM_ID_AC_PHASE_LOST, ID1_ACMU_ALARM, ID2_ALM_PHASELOST, 3 },	// 交流UΦ1/UΦ2/UΦ3缺相
	{ACMU_ALM_ID_PHASE_VOLT_UNBALANCE, ID1_ACMU_ALARM, ID2_ALM_PHASEUNBL, 1 },	// 交流相电压不平衡
	{ACMU_ALM_ID_PHASE_VOLT_HIGH_ALARM, ID1_ACMU_ALARM, ID2_ALM_ACVOLTHIGH, 3 },	// 交流UΦ1/UΦ2/UΦ3电压高
	{ACMU_ALM_ID_PHASE_VOLT_LOW_ALARM, ID1_ACMU_ALARM, ID2_ALM_ACVOLTLOW, 3 },	// 交流UΦ1/UΦ2/UΦ3电压低
	{ACMU_ALM_ID_PHASE_CURR_HIGH_ALARM, ID1_ACMU_ALARM, ID2_ALM_ACCURRHIGH, 3 },	// 交流IL1/IL2/IL3电流高
	{ACMU_ALM_ID_SPD_C, ID1_ACMU_ALARM, ID2_ALM_SPD_C, 1 },	// C级防雷器损坏
	{ACMU_ALM_ID_AC_OUTPUT_SWITCH_DISCONNECTION, ID1_ACMU_ALARM, ID2_ALM_OUTSWITCH, SWITCH_NUM },	// 交流输出空开断
	{ACMU_ALM_ID_AC_LINE_VOLT_HIGH, ID1_ACMU_ALARM, ID2_ALM_ACVOLTPPHIGH, 3 },	// 交流线电压高
	{ACMU_ALM_ID_AC_LINE_VOLT_LOW, ID1_ACMU_ALARM, ID2_ALM_ACVOLTPPLOW, 3 },	// 交流线电压低
	{ACMU_ALM_ID_AC_METER_DISCONNECTED, ID1_ACMU_ALARM, ID2_ALM_ACEMCOMFAIL, 1 },	// 交流电表通讯断
	// ENV告警量		
	{ACMU_ALM_ID_ENV_TEMP_HIGH_ALARM, ID1_ENV_ALARM, ID2_ALM_ETHIGH, 1 },	// 环境温度高
	{ACMU_ALM_ID_ENV_TEMP_LOW_ALARM, ID1_ENV_ALARM, ID2_ALM_ETLOW, 1 },	// 环境温度低
	{ACMU_ALM_ID_ENV_HUMIDITY_HIGH_ALARM, ID1_ENV_ALARM, ID2_ALM_EHHIGH, 1 },	// 环境湿度高
	{ACMU_ALM_ID_ENV_HUMIDITY_LOW_ALARM, ID1_ENV_ALARM, ID2_ALM_EHLOW, 1 },	// 环境湿度低
	{ACMU_ALM_ID_FUMES_SENSOR_ALARM, ID1_ENV_ALARM, ID2_ALM_SMOG, 1 },	// 烟雾告警
	{ACMU_ALM_ID_FLOOD_SENSOR_ALARM, ID1_ENV_ALARM, ID2_ALM_FLOOD, 1 },	// 水淹告警
	{ACMU_ALM_ID_DOORMAT_SENSOR_ALARM, ID1_ENV_ALARM, ID2_ALM_DOORMAG, 1 },	// 门磁告警
	{ACMU_ALM_ID_ENV_TEMP_SENSOR_INVALID_ALARM, ID1_ENV_ALARM, ID2_ALM_ETSENSOR, 1 },	// 环境温度失效告警
	{ACMU_ALM_ID_ENV_HUMIDITY_SENSOR_INVALID_ALARM, ID1_ENV_ALARM, ID2_ALM_EHSENSOR, 1 },	// 环境湿度失效告警
	{ACMU_ALM_ID_OIL_ENGINE_START_UP_ALARM, ID1_ACMU_ALARM, ID2_ALM_STARTGEN, 1 },	// 油机启动
	{ACMU_ALM_ID_INPUT_RELAY1_ALARM, ID1_ACMU_ALARM, ID2_ALM_INRELAY, 1 },	// 输入干结点1告警
	{ACMU_ALM_ID_INPUT_RELAY2_ALARM, ID1_ACMU_ALARM, ID2_ALM_INRELAY, 1 },	// 输入干结点2告警
	{ACMU_ALM_ID_INPUT_RELAY3_ALARM, ID1_ACMU_ALARM, ID2_ALM_INRELAY, 1 },	// 输入干结点3告警
	{ACMU_ALM_ID_INPUT_RELAY4_ALARM, ID1_ACMU_ALARM, ID2_ALM_INRELAY, 1 },	// 输入干结点4告警
};


// 操作记录ID MIB表
const MIB_CTRLNode CTRL_MIBTable[]=
{
	{ID1_ACMU_SETSTATUS, ID2_PR_TIME,1 },	// 系统时间设置

	{ID1_ACMU_CTRL, ID2_CTL_RLYRESET, RELAY_MAX },	// 输出干节点恢复
	{ID1_ACMU_CTRL, ID2_CTL_RLYACT, RELAY_MAX },	// 输出干节点动作
	{ID1_ACMU_CTRL, ID2_CTL_LDRUNPARA,1 },	// 恢复默认运行参数
	{ID1_ACMU_CTRL, ID2_CTL_LDCFGPARA,1 },	// 恢复默认配置参数 
	{ID1_ACMU_CTRL, ID2_CTL_DOWMPROG,1 },	// 下载程序
	{ID1_ACMU_CTRL, ID2_CTL_DOWNZK,1 },	// 下载字库
	{ID1_ACMU_CTRL, ID2_CTL_RESET,1 },	// 系统复位
    {0xFF, 0xFF, 1 }, //没有历史记录（预留）
    {0xFF, 0xFF, 1 }, //正在初始化（预留）
    {0xFF, 0xFF, 1 }, //此记录无效（预留）
    {ID1_ACMU_CTRL, ID2_CTL_ENTERAPPTEST, 1},       // 进入APPTEST
    {ID1_ACMU_CTRL, ID2_CTL_QUITAPPTEST, 1},        // 退出APPTEST
    {ID1_ACMU_CTRL, ID2_CTL_DELACTIONREC, 1},       // 删除事件记录
    {ID1_ACMU_CTRL, ID2_CTL_DELHISALARMREC, 1},     // 删除历史告警
    {ID1_ACMU_CTRL, ID2_CTL_DELHISDATA, 1},         // 删除历史数据
    {ID1_ACMU_CTRL, ID2_CTL_DELPEAKREC, 1},         // 删除极值记录       
    {ID1_ACMU_CTRL, ID2_CTL_DELALLREC, 1},          // 删除所有记录数据
    {ID1_ACMU_CTRL, ID2_CTL_FACTORYRESET, 1},          // 恢复厂家设置
	{0xFF, 0xFF, 0 },	// IDNumber为0表明数组结束
};

static MIB_CTRLInstanceNode s_tCtrlInstance;
Static MIBTable_ParaInstance s_tParaInstance = {0};		// 参数偏移
Static T_ParaWnd * s_pParaAttr = {0};					// 当前参数属性指针

/**
 * @description: 获取参数实例
 * @param {unsigned char} ucID1 参数ID1
 * @param {unsigned char} ucID2 参数ID2
 * @param {unsigned char} ucIndex 参数索引
 * @return {MIBTable_ParaInstance*} 成功返回参数实例指针，失败返回NULL
 */
MIBTable_ParaInstance* GetParaInstance( unsigned char ucID1, unsigned char ucID2, unsigned char ucIndex )
{
	unsigned short wOffset;
    int ret = 0;

	// 获取参数节点
	ret = GetParaNode( ucID1, ucID2, &s_tParaInstance);
	if ( ret != SUCCESSFUL )
	{
		return NULL;
	}

	if ( ucIndex >= s_tParaInstance.tParaNode.ucIDNumber )
	{
		return NULL;
	}

	wOffset = GetParaOffset( ucID1, ucID2, ucIndex );
	s_tParaInstance.wOffset = wOffset;
	return &s_tParaInstance;
}

MIBTable_ParaInstance* GetRelayAlmParaInstance( unsigned char ucID1, unsigned char ucID2, unsigned char ucIndex, unsigned char relayidx)
{
	unsigned short wOffset;
    int ret = 0;

	// 获取参数节点
	ret = GetRelayAlmParaNode( ucID1, ucID2, &s_tParaInstance, relayidx);
	if ( ret != SUCCESSFUL )
	{
		return NULL;
	}

	if ( ucIndex >= s_tParaInstance.tParaNode.ucIDNumber )
	{
		return NULL;
	}

	wOffset = GetInRelayAlmParaOffset( ucID1, ucID2, ucIndex, relayidx);
	s_tParaInstance.wOffset = wOffset;
	return &s_tParaInstance;
}

/**
 * @description: 获取参数节点
 * @param {unsigned char} ucID1 参数ID1
 * @param {unsigned char} ucID2 参数ID2
 * @return {const MIBTable_ParaNode*} 成功返回参数节点指针，失败返回NULL
 */
int GetParaNode( unsigned char ucID1, unsigned char ucID2, MIBTable_ParaInstance* inst)
{
	unsigned short i = 0;

	while ( Para_MIBTable[i].ucIDNumber )
	{
		if ( Para_MIBTable[i].ucID2 == ucID2 && Para_MIBTable[i].ucID1 == ucID1 )
		{
            inst->tParaNode = Para_MIBTable[i];
            inst->wZkSn = i;
            return SUCCESSFUL;
		}
		i++;
	}

	return FAILURE;
}

int GetRelayAlmParaNode( unsigned char ucID1, unsigned char ucID2, MIBTable_ParaInstance* inst, unsigned char relayidx)
{
	unsigned short i = 0;

	while ( Para_MIBTable[i].ucIDNumber )
	{
		if ( Para_MIBTable[i].ucID2 == ucID2 && Para_MIBTable[i].ucID1 == ucID1 )
		{
            inst->tParaNode = Para_MIBTable[i + relayidx];
            inst->wZkSn = i + relayidx;
            return SUCCESSFUL;
		}
		i++;
	}

	return FAILURE;
}

/***************************************************************************
* 函数名称：GetParaNodeByParaId
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：获取参数属性节点
***************************************************************************/
MIBTable_ParaNode* GetParaNodeByParaId(unsigned short wOffsetParaId)
{
    unsigned short i = 0;

    while ( Para_MIBTable[i].ucIDNumber )
    {
        if (Para_MIBTable[i].wParaOffsetId == wOffsetParaId)
        {
            return &Para_MIBTable[i];
        }
        i++;
    }

    return (MIBTable_ParaNode *)NULL;
}

/***************************************************************************
* 函数名称：GetParaNodeByParaIdNoOffset
* 调    用：
* 被 调 用：
* 输入参数：参数ID,不带offset
* 返 回 值：
* 功能描述：获取参数属性节点
***************************************************************************/
static MIBTable_ParaNode* GetParaNodeByParaIdNoOffset(unsigned short wParaId)
{
    unsigned short i = 0;
    unsigned short wParaId_no_index = wParaId & 0xFFF0;
    while ( Para_MIBTable[i].ucIDNumber )
    {
        {
            if (Para_MIBTable[i].wParaId == wParaId_no_index && (Para_MIBTable[i].ucID1 == ID1_ACMU_PARA || Para_MIBTable[i].ucID1 == ID1_ENV_PARA))
            {
                return &Para_MIBTable[i];
            }
        }
        i++;
    }

    return (MIBTable_ParaNode *)NULL;
}

/***************************************************************************
* 函数名称：GetParaNodeByParaIdNoOffset
* 调    用：
* 被 调 用：
* 输入参数：参数ID,不带offset
* 返 回 值：
* 功能描述：获取参数属性节点
***************************************************************************/
Static MIBTable_ParaNode* GetAlmNodeByParaIdNoOffset(unsigned short wParaId, unsigned char uctype)
{
    unsigned short i = 0;
    if (uctype == 0x04)
    {
        wParaId = GET_ALM_PARA_BY_ALM_CODE(wParaId, 0x0000);
    }
    else if (uctype == 0x05)
    {
        wParaId = GET_ALM_PARA_BY_ALM_CODE(wParaId, 0x4000);
    }
    
    while ( Para_MIBTable[i].ucIDNumber )
    {
        {
            if (Para_MIBTable[i].wParaId == wParaId && (Para_MIBTable[i].ucID1 ==  ID1_ACMU_ALARM|| Para_MIBTable[i].ucID1 == ID1_ENV_ALARM
               || Para_MIBTable[i].ucID1 ==  ID1_ACMU_OUTRLY || Para_MIBTable[i].ucID1 ==  ID1_ENV_OUTRLY))
            {
                return &Para_MIBTable[i];
            }
        }
        i++;
    }

    return (MIBTable_ParaNode *)NULL;
}

/***************************************************************************
* 函数名称：GetParaNodeByParaIdNoOffset
* 调    用：
* 被 调 用：
* 输入参数：参数ID,不带offset
* 返 回 值：
* 功能描述：获取参数属性节点
***************************************************************************/
MIBTable_ParaNode* GetParaNodeByType(unsigned short wParaId, unsigned char type)
{
    MIBTable_ParaNode* ptParaNode = NULL;

    switch(type) {
        case 0x02:  // 参数
            ptParaNode = GetParaNodeByParaIdNoOffset(wParaId);
            break;
        case 0x04:  // 告警级别
        case 0x05:  // 告警干接点            
            ptParaNode = GetAlmNodeByParaIdNoOffset(wParaId, type);
            break;
        default:
            break;
    }

    return ptParaNode;
}

/**
 * @description: 获取参数在GUI结构体中的偏移量
 * @param {unsigned char} ucID1 参数ID1
 * @param {unsigned char} ucID2 参数ID2
 * @param {unsigned char} ucIDIndex 参数索引
 * @return {unsigned short} 参数偏移量
 */
unsigned short GetParaOffset( unsigned char ucID1, unsigned char ucID2, unsigned char ucIDIndex )
{
	unsigned short wMIBOffset = 0;
	unsigned short i = 0;

	while ( Para_MIBTable[i].ucIDNumber )
	{
		if ( Para_MIBTable[i].ucID2 == ucID2 && Para_MIBTable[i].ucID1 == ucID1 )
		{
			if ( ucIDIndex != 0xff )
			{
				wMIBOffset += (unsigned short)Para_MIBTable[i].ucDataLen * ucIDIndex;
			}
			return wMIBOffset;
		}
		else
		{
			wMIBOffset += (unsigned short)Para_MIBTable[i].ucDataLen * Para_MIBTable[i].ucIDNumber;
		}
		i++;
	}

	return 0;
}

unsigned short GetInRelayAlmParaOffset(unsigned char ucID1, unsigned char ucID2, unsigned char ucIDIndex, unsigned char ucinrelayIdx)
{
    unsigned short wMIBOffset = 0;
    unsigned short i = 0;

    while (Para_MIBTable[i].ucIDNumber)
    {
        if ( Para_MIBTable[i].ucID2 == ucID2 && Para_MIBTable[i].ucID1 == ucID1 )
        {
            if ( ucIDIndex!= 0xff )
            {
                wMIBOffset += (unsigned short)Para_MIBTable[i].ucDataLen * ucIDIndex;
                wMIBOffset += ucinrelayIdx * Para_MIBTable[i].ucDataLen;
            }
            return wMIBOffset;
        }
        else
        {
            wMIBOffset += (unsigned short)Para_MIBTable[i].ucDataLen * Para_MIBTable[i].ucIDNumber;
        }

        i++;
    }

    return 0;
}

/**
 * @description: GUI值转换为原始值
 * @param {void*} pGuiValue GUI值指针
 * @param {int} guiType GUI值类型
 * @param {int} rawType 原始值类型
 * @param {unsigned char} precision 精度
 * @param {u_value*} pRawValue 输出的原始值
 * @return {int} 成功返回SUCCESSFUL，失败返回FAILURE
 */
int convert_gui_to_raw(void *pGuiValue, int guiType, int rawType, unsigned char precision, u_value *pRawValue)
{
    float temp = 0.0f;

    RETURN_VAL_IF_FAIL(pGuiValue != NULL && pRawValue != NULL, FAILURE);

    switch (guiType) {
        case TYPE_INT8U:
            temp = (float)(*(unsigned char*)pGuiValue);
            break;
        case TYPE_INT8S:
            temp = (float)(*(char*)pGuiValue);
            break;
        case TYPE_INT16S:
            temp = (float)(*(short*)pGuiValue);
            break;
        case TYPE_INT16U:
            temp = (float)(*(unsigned short*)pGuiValue);
            break;
        default:
            return FAILURE;
    }

    switch (rawType) {
        case TYPE_FLOAT:
            pRawValue->f_val = temp / pow(10, precision);
            break;
        case TYPE_INT8S:
            pRawValue->sc_val = (signed char)(temp / pow(10, precision));
            break;
        case TYPE_INT8U:
            pRawValue->uc_val = (unsigned char)(temp / pow(10, precision));
            break;
        case TYPE_INT16S:
            pRawValue->ss_val = (signed short)(temp / pow(10, precision));
            break;
        case TYPE_INT16U:
            pRawValue->us_val = (unsigned short)(temp / pow(10, precision));
            break;
        default:
            return FAILURE;
    }

    return SUCCESSFUL;
}


/**
 * @description: 通过参数类型获取排除参数类型的参数ID列表
 * @param {unsigned char} para_type - 要排除的参数类型
 * @param {unsigned short*} out_id_list - 输出参数ID列表缓冲区
 * @param {int} max_num - 缓冲区最大容量
 * @return {int} 失败返回FAILURE(-1)，成功返回实际匹配的参数个数
 */
int get_exclude_para_ids_by_type(unsigned char para_type, unsigned short* out_id_list, int max_num) {
    unsigned short i = 0;
	unsigned short j = 0;
    unsigned short count = 0;

    // 参数有效性检查
    if (out_id_list == NULL || max_num <= 0) {
        return FAILURE;
    }

    // 遍历参数表（假设Para_MIBTable以ucIDNumber=0作为结束标志）
    for (i = 0; i < sizeof(Para_MIBTable)/sizeof(MIBTable_ParaNode) - 1; i++) {
        // 类型不匹配时记录ID
        if (Para_MIBTable[i].ucParaType != para_type) {
            // 检查缓冲区容量
            if (count >= (unsigned short)max_num) {
                return FAILURE; // 缓冲区溢出
            }
			if (Para_MIBTable[i].ucID1 == ID1_ACMU_ALARM || Para_MIBTable[i].ucID1 == ID1_ENV_ALARM || Para_MIBTable[i].ucID1 == ID1_ACMU_OUTRLY || Para_MIBTable[i].ucID1 == ID1_ENV_OUTRLY) {
				continue; 
			}
			for (j = 0; j < Para_MIBTable[i].ucIDNumber; j++) {
				out_id_list[count++] = Para_MIBTable[i].wParaId + j;
			} 
        }
    }


    return count;
}


/**
 * @description: 发布历史告警消息给历史记录组件存储
 * @param {his_alarm_info_t} *str 存储的内容
 * @return {short}  成功 SUCCESSFUL
 */

int get_alarm_sn(unsigned short alarm_id, alarm_map_t** out_map) {
    int i = 0;
    for (i = 0; i < sizeof(alarm_map)/sizeof(alarm_map[0]); i++) {
        if (alarm_map[i].alarm_id == alarm_id) {
            *out_map = &alarm_map[i];
            return i; // 返回数组索引作为序列号
        }
    }
    return -1;
}



const MIB_AlarmDataNode * GetAlarmNode(unsigned char ucAlarmSn)
{
    if (ucAlarmSn >= sizeof(alarm_map) / sizeof(alarm_map[0])) {
        return NULL;
    }

    static MIB_AlarmDataNode alarm_data_node; // 使用静态变量
    rt_memset_s(&alarm_data_node, sizeof(alarm_data_node), 0, sizeof(alarm_data_node));

    alarm_data_node.ucID1 = alarm_map[ucAlarmSn].id1;
    alarm_data_node.ucID2 = alarm_map[ucAlarmSn].id2;
    alarm_data_node.ucIDNumber = alarm_map[ucAlarmSn].number;

    return &alarm_data_node;
}

/***************************************************************************
* 函数名称：GetAlarmGradeZkSn
* 被 调 用：
* 输入参数：告警级别ID1,ID2
* 返 回 值：根据告警级别ID1,ID2计算该参数的页码
* 功能描述：告警级别页码  
***************************************************************************/
unsigned char GetAlarmGradeZkSn( unsigned char ucID1, unsigned char ucID2 )
{
	unsigned short i = 0,wBaseAdd=0;

	while ( Para_MIBTable[i].ucIDNumber )
	{
		if( Para_MIBTable[i].ucID1 == ID1_ACMU_ALARM && Para_MIBTable[i].ucID2 == ID2_ALM_COMMON )
		{
			wBaseAdd = i;
		}
		else if( Para_MIBTable[i].ucID1 == ucID1 && Para_MIBTable[i].ucID2 == ucID2 )
		{
			return ( unsigned char  )( i - wBaseAdd );
		}
		i++;
	}
	return 0;
}
/***************************************************************************
* 函数名称：GetAlmOutRlyZkSn
* 被 调 用：
* 输入参数：告警对应输出干结点ID1,ID2
* 返 回 值：根据告警对应输出干结点ID1,ID2计算该参数的页码
* 功能描述：告警对应输出干结点页码   
***************************************************************************/
unsigned char GetAlmOutRlyZkSn( unsigned char ucID1, unsigned char ucID2 )
{
	unsigned short i = 0,wBaseAdd=0;

	while ( Para_MIBTable[i].ucIDNumber )
	{
		if( Para_MIBTable[i].ucID1 == ID1_ACMU_OUTRLY && Para_MIBTable[i].ucID2 == ID2_ALM_COMMON )
		{
			wBaseAdd = i;
		}
		else if( Para_MIBTable[i].ucID1 == ucID1 && Para_MIBTable[i].ucID2 == ucID2 )
		{
			return ( unsigned char  )( i - wBaseAdd );
		}
		i++;
	}
	return 0;
}

/***************************************************************************
* 函数名称：GetCTRLInstance
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：根据ID获取事件记录在字库中的序号
* 作    者：武希洲
* 设计日期：2008-09-02
* 修改记录：
* 日    期		版	本		修改人	潘奇银	修改摘要      
***************************************************************************/
MIB_CTRLInstanceNode * GetCTRLInstance( unsigned char ucID1, unsigned char ucID2 )
{
	unsigned char i = 0;

	while ( CTRL_MIBTable[i].ucIDNumber )
	{
		if ( CTRL_MIBTable[i].ucID2 == ucID2 && CTRL_MIBTable[i].ucID1 == ucID1 )
		{
			s_tCtrlInstance.tNode = CTRL_MIBTable[i];
			s_tCtrlInstance.ucZkSn = i;
			return &s_tCtrlInstance;
		}
		i++;
	}
	return ( MIB_CTRLInstanceNode * )NULL;
}

unsigned char init_mib_table_para_from_dic(void)
{
    int i = 0;
    scope_t scope_v = {0};
    unsigned short para_id_offset = 0;
    unsigned char para_raw_type = 0;
    unsigned char para_precision = 0;
    unsigned short multiple = 0;

    for (i = 0; i < sizeof(Para_MIBTable) / sizeof(MIBTable_ParaNode); i++)
    {
        para_id_offset = Para_MIBTable[i].wParaOffsetId;
        scope_v = get_para_scope_by_para_id_offset(para_id_offset);
        para_raw_type = GET_SYS_PARA_DATA_TYPE(para_id_offset);
        para_precision = get_precision(para_raw_type, para_id_offset);
        Para_MIBTable[i].scPrecision = para_precision;
        multiple = round(pow(10, para_precision));

        switch (para_raw_type)
        {
            case TYPE_STRING:
                break;
            case TYPE_INT16S:
                Para_MIBTable[i].iMaxVal = scope_v.max.ss_val * multiple;
                Para_MIBTable[i].iMinVal = scope_v.min.ss_val * multiple;
                break;
            case TYPE_INT16U:
                Para_MIBTable[i].iMaxVal = scope_v.max.us_val * multiple;
                Para_MIBTable[i].iMinVal = scope_v.min.us_val * multiple;
                break;
            case TYPE_INT8S:
                Para_MIBTable[i].iMaxVal = scope_v.max.sc_val * multiple;
                Para_MIBTable[i].iMinVal = scope_v.min.sc_val * multiple;
                break;
            case TYPE_INT8U:
                Para_MIBTable[i].iMaxVal = scope_v.max.uc_val * multiple;
                Para_MIBTable[i].iMinVal = scope_v.min.uc_val * multiple;
                break;
            case TYPE_FLOAT:
                Para_MIBTable[i].iMaxVal = scope_v.max.f_val * multiple;
                Para_MIBTable[i].iMinVal = scope_v.min.f_val * multiple;
                break;
            default:
                break;
        }
        // rt_kprintf("MIB[%d],id:0x%x, precision:%d, min:%d, max:%d\n",i,para_id_offset, para_precision, Para_MIBTable[i].iMinVal, Para_MIBTable[i].iMaxVal);
    }
    return SUCCESSFUL;
}
