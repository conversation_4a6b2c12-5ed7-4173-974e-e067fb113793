/*******************************************************************************
  * @file        update_manage.c
  * @version     v1.0.0
  * @copyright   COPYRIGHT &copy; 2023 CSG
  * <AUTHOR>
  * @date        2023-4-6
  * @brief
  * @attention
  * Modification History
  * DATE         DESCRIPTION
  * ------------------------
  * - 2023-4-6  dongmengling Created
*******************************************************************************/
#include <string.h>
#include <rtthread.h>
#include <rtdevice.h>
#include <stdlib.h>
#include "update_manage.h"
#include "device_type.h"
#include "dev_ilvdb.h"
#include "dev_uib03.h"
#include "sps.h"
#include "cmd.h"
#include "protocol_layer.h"
#include "utils_data_transmission.h"
#include "flash.h"
#include "gd32c10x_fmc.h"


#ifdef USING_DEVICE_BSC_UIB03
#define UPDATE_FILE_NAME    "GD_UIB03_app.bin"
#else
#define UPDATE_FILE_NAME    "GD_ILVDB_app.bin"
#endif

Static int parse_update_trig_old(void* dev_inst, void *cmd_buf);
Static int pack_update_trig_old(void* dev_inst, void *cmd_buf);
Static int get_app_download_rtn(void *cmd_buf);
Static int BeginDownload( char ucMode );

// extern int board_addr;
#ifdef UNITEST
unsigned char dest_dev;
#else
extern unsigned char dest_dev;
#endif

Static trig_ctr_inf_t s_trig_ctr_inf;

#ifdef USING_DEVICE_BSC_UIB03
Static cmd_handle_register_t update_cmd_handle[] = {
	{DEV_UIB03, UIB_UPDATE_TRIG, CMD_TYPE_NO_POLL, parse_update_trig_old, pack_update_trig_old},
};
#else
Static cmd_handle_register_t update_cmd_handle[] = {
	{DEV_ILVDB, UIB_UPDATE_TRIG, CMD_TYPE_NO_POLL, parse_update_trig_old, pack_update_trig_old},
};
#endif

Static int get_app_download_rtn(void *cmd_buf) {
    bottom_comm_cmd_head_t* proto_head = NULL;
    cmd_buf_t* trig_cmd_buf = (cmd_buf_t*)cmd_buf;
    unsigned char dev_type = 0;

    // 入参校验
    if ( trig_cmd_buf == NULL || trig_cmd_buf->buf == NULL) {
        return FAILURE;
    }

    proto_head = (bottom_comm_cmd_head_t*)trig_cmd_buf->cmd->req_head;
    dev_type = dest_dev;
    char *p_file_name = s_trig_ctr_inf.file_name;
    unsigned char *p = trig_cmd_buf->buf;

    if (proto_head->append != 0xAA55)
    {
        return DownloadFrameErr;
    }

    if (trig_cmd_buf->data_len == UPDATE_FILE_NAME_LEN)
    {
        switch(dev_type) {
            case BOTTOM_ILVDB_TYPE:
                rt_snprintf_s(p_file_name, UPDATE_FILE_NAME_LEN, UPDATE_FILE_NAME);
                if (rt_strncmp((char*)p, UPDATE_FILE_NAME, UPDATE_FILE_NAME_LEN) == 0) {
                    return BOTTOM_RTN_APP_CORRECT;
                }
                break;
            case BOTTOM_UIB03_TYPE:
                rt_snprintf_s(p_file_name, UPDATE_FILE_NAME_LEN, UPDATE_FILE_NAME);
                if (rt_strncmp((char*)p, UPDATE_FILE_NAME, UPDATE_FILE_NAME_LEN) == 0){
                    return BOTTOM_RTN_APP_CORRECT;
                }
                break;
        }
    }
    return DownFileNameErr;
}


Static int parse_update_trig_old(void* dev_inst, void *cmd_buf) {

    s_trig_ctr_inf.rtn = get_app_download_rtn(cmd_buf);
    
    return SUCCESSFUL;
}

Static int pack_update_trig_old(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* trig_cmd_buf = (cmd_buf_t*)cmd_buf;
    bottom_comm_cmd_head_t* proto_head = NULL;

    // 入参校验
    if ( trig_cmd_buf == NULL || trig_cmd_buf->buf == NULL) {
        return FAILURE;
    }

    proto_head = (bottom_comm_cmd_head_t*)trig_cmd_buf->cmd->ack_head;
    proto_head->append = 0x55AA;
    if(s_trig_ctr_inf.rtn == DownloadFrameErr) {
        //附加码错误
        trig_cmd_buf->data_len = 0;
        trig_cmd_buf->rtn = BOTTOM_RTN_CMD_ERROR;
        return SUCCESSFUL;
    } else {
        //文件名错误或正确，回复正确文件名
        trig_cmd_buf->data_len = UPDATE_FILE_NAME_LEN;
        rt_memset_s(trig_cmd_buf->buf, 0x00, trig_cmd_buf->data_len);
        rt_memcpy_s(trig_cmd_buf->buf, UPDATE_FILE_NAME, sizeof(UPDATE_FILE_NAME)-1);
    }

	BeginDownload(FLAG_IAP);

    return SUCCESSFUL;
}


void update_manage_init(void) {
    short i = 0;
    for(i = 0; i < sizeof(update_cmd_handle)/sizeof(update_cmd_handle[0]); i++) {
        register_cmd_handle(&update_cmd_handle[i]);
    }

}

Static int  BeginDownload( char ucMode )
{
    uint8_t aucBuff[MSC_MAX_PACKET];
    T_FileManageStruct* ptFileManage;


    fmc_read_data(UPDATEINFO_STATRT,(uint8_t*)aucBuff, MSC_MAX_PACKET);
    ptFileManage = (T_FileManageStruct*)aucBuff;
    if (ucMode == FLAG_BACKUP && ptFileManage->ucFlag != FLAG_APP)
    {
        return FAILURE;
    }
    ptFileManage->ucUpdateAddr = get_host_addr();
    ptFileManage->ucFlag = ucMode;
    ptFileManage->wCrc = crc_cal((unsigned char *)ptFileManage, sizeof(T_FileManageStruct)-2);
    fmc_erase_pages(UPDATEINFO_STATRT);
    fmc_program(UPDATEINFO_STATRT, (unsigned char *)aucBuff, sizeof(T_FileManageStruct));

    NVIC_SystemReset();

    return SUCCESSFUL;
}

