#ifndef _SNMPOID32_H_
#define _SNMPOID32_H_

#include "common.h"
#include "stddef.h"
#include "CommCan.h"
#include "MdbsRtu.h"

#define ParaOffset    0x3301
#define OtherOffset   0x3501

typedef struct
{
	BYTE ucIsUse;
	WORD wLocalParaDataOffset;
//	WORD wSnmpParaDataOffset;
	WORD ucDataType;
	BYTE ucPrecision;
	WORD modbusAddr;
}T_OneParaSnmpStruct;

typedef struct
{
	BYTE ucIsUse;
	BYTE ucIsSend;
	WORD wLocalParaDataOffset;
//	WORD wSnmpParaDataOffset;
	WORD ucDataType;
	BYTE ucPrecision;
	WORD modbusAddr;
}T_OneOtherSnmpStruct;

const T_OneParaSnmpStruct s_atParaSnmp[] = {
	{1,offsetof(T_SysPara,wBattSOCLowAlmThre),DATA_TYPE_INT16U, 0, 1154},
	{1,offsetof(T_SysPara,wBattSOCLowPrtThre),DATA_TYPE_INT16U, 0, 1155},
	{1,offsetof(T_SysPara,wBattSOHAlmThre),DATA_TYPE_INT16U, 0, 1156},
	{1,offsetof(T_SysPara,wBattSOHPrtThre),DATA_TYPE_INT16U, 0, 1157},
	{1,offsetof(T_SysPara,fCellDamagePrtThre),DATA_TYPE_FP32, 2, 1158},
	{1,offsetof(T_SysPara,fBattChgFullAverCurrThre),DATA_TYPE_FP32, 2, 1160},
	{1,offsetof(T_SysPara,fCellEquVoltDiffThre),DATA_TYPE_FP32, 2, 1165},
	{1,offsetof(T_SysPara,wHisDataInter),DATA_TYPE_INT16U, 0, 1169},
	{1,offsetof(T_SysPara,wHeartbeatCycle),DATA_TYPE_INT16U, 0, 1418},
	{1,offsetof(T_SysPara,fEnvTempHighPrtThre),DATA_TYPE_FP32, 0, 1445},
	{1,offsetof(T_SysPara,fEnvTempLowPrtThre),DATA_TYPE_FP32, 0, 1446},
	{1,offsetof(T_SysPara,fEnvTempHighPrtRecoThre),DATA_TYPE_FP32, 0, 1461},
	{1,offsetof(T_SysPara,fEnvTempLowPrtRecoThre),DATA_TYPE_FP32, 0, 1463},
	{1,offsetof(T_SysPara,ucGyroAntiTheftMode),DATA_TYPE_INT8U, 0, 1471},
	{1,offsetof(T_SysPara,ucBattUnlockMode),DATA_TYPE_INT8U, 0, 1472},
	{1,offsetof(T_SysPara,wBattSOCLowAlmRecoThre),DATA_TYPE_INT16U, 0, 1474},
	{1,offsetof(T_SysPara,wBattSOCLowPrtRecoThre),DATA_TYPE_INT16U, 0, 1475},
	{1,offsetof(T_SysPara,wBattSOHAlmRecoThre),DATA_TYPE_INT16U, 0, 1476},
	{1,offsetof(T_SysPara,wBattSOHPrtRecoThre),DATA_TYPE_INT16U, 0, 1477},
	{1,offsetof(T_SysPara,wBatteryCap),DATA_TYPE_INT16U, 0, 4000},
	{0,0,DATA_TYPE_INT16U, 0, 4001},
	{0,0,DATA_TYPE_INT16U, 0, 4002},
	{0,0,DATA_TYPE_INT16U, 0, 4003},
	{0,0,DATA_TYPE_INT16U, 0, 4004},
	{0,0,DATA_TYPE_INT16U, 0, 4005},
	{0,0,DATA_TYPE_INT16U, 0, 4006},
	{0,0,DATA_TYPE_INT16U, 0, 4007},
	{0,0,DATA_TYPE_INT16U, 0, 4008},
	{0,0,DATA_TYPE_INT16U, 0, 4009},
	{1,offsetof(T_SysPara,bBuzzerEnable),DATA_TYPE_INT8U, 0, 4010},
	{1,offsetof(T_SysPara,fCellOverVoltAlmThre),DATA_TYPE_FP32, 3, 4012},
	{1,offsetof(T_SysPara,fCellOverVoltAlmRecoThre),DATA_TYPE_FP32, 3, 4013},
	{1,offsetof(T_SysPara,fCellOverVoltPrtThre),DATA_TYPE_FP32, 3, 4014},
	{1,offsetof(T_SysPara,fCellOverVoltPrtRecoThre),DATA_TYPE_FP32, 3, 4015},
	{1,offsetof(T_SysPara,fCellUnderVoltAlmThre),DATA_TYPE_FP32, 3, 4016},
	{1,offsetof(T_SysPara,fCellUnderVoltAlmRecoThre),DATA_TYPE_FP32, 3, 4017},
	{1,offsetof(T_SysPara,fCellUnderVoltPrtThre),DATA_TYPE_FP32, 3, 4018},
	{1,offsetof(T_SysPara,fCellUnderVoltPrtRecoThre),DATA_TYPE_FP32, 3, 4019},
	{1,offsetof(T_SysPara,fBattOverVoltAlmThre),DATA_TYPE_FP32, 2, 4020},
	{1,offsetof(T_SysPara,fBattOverVoltAlmRecoThre),DATA_TYPE_FP32, 2, 4021},
	{1,offsetof(T_SysPara,fBattOverVoltPrtThre),DATA_TYPE_FP32, 2, 4022},
	{1,offsetof(T_SysPara,fBattOverVoltPrtRecoThre),DATA_TYPE_FP32, 2, 4023},
	{1,offsetof(T_SysPara,fBattUnderVoltAlmThre),DATA_TYPE_FP32, 2, 4024},
	{1,offsetof(T_SysPara,fBattUnderVoltPrtThre),DATA_TYPE_FP32, 2, 4026},
	{1,offsetof(T_SysPara,fBattUnderVoltPrtRecoThre),DATA_TYPE_FP32, 2, 4027},
	{1,offsetof(T_SysPara,fCellPoorConsisAlmThre),DATA_TYPE_FP32, 2, 4028},
	{1,offsetof(T_SysPara,fCellPoorConsisPrtThre),DATA_TYPE_FP32, 2, 4030},
	{1,offsetof(T_SysPara,fChgTempHighAlmThre),DATA_TYPE_FP32, 1, 4032},
	{1,offsetof(T_SysPara,fChgTempHighAlmRecoThre),DATA_TYPE_FP32, 1, 4033},
	{1,offsetof(T_SysPara,fChgTempHighPrtThre),DATA_TYPE_FP32, 1, 4034},
	{1,offsetof(T_SysPara,fChgTempHighPrtRecoThre),DATA_TYPE_FP32, 1, 4035},
	{1,offsetof(T_SysPara,fChgTempLowAlmThre),DATA_TYPE_FP32, 1, 4036},
	{1,offsetof(T_SysPara,fChgTempLowAlmRecoThre),DATA_TYPE_FP32, 1, 4037},
	{1,offsetof(T_SysPara,fChgTempLowPrtThre),DATA_TYPE_FP32, 1, 4038},
	{1,offsetof(T_SysPara,fChgTempLowPrtRecoThre),DATA_TYPE_FP32, 1, 4039},
	{1,offsetof(T_SysPara,fDischgTempHighAlmThre),DATA_TYPE_FP32, 1, 4040},
	{1,offsetof(T_SysPara,fDischgTempHighAlmRecoThre),DATA_TYPE_FP32, 1, 4041},
	{1,offsetof(T_SysPara,fDischgTempHighPrtThre),DATA_TYPE_FP32, 1, 4042},
	{1,offsetof(T_SysPara,fDischgTempHighPrtRecoThre),DATA_TYPE_FP32, 1, 4043},
	{1,offsetof(T_SysPara,fDischgTempLowAlmThre),DATA_TYPE_FP32, 1, 4044},
	{1,offsetof(T_SysPara,fDischgTempLowAlmRecoThre),DATA_TYPE_FP32, 1, 4045},
	{1,offsetof(T_SysPara,fDischgTempLowPrtThre),DATA_TYPE_FP32, 1, 4046},
	{1,offsetof(T_SysPara,fDischgTempLowPrtRecoThre),DATA_TYPE_FP32, 1, 4047},
	{1,offsetof(T_SysPara,fEnvTempHighAlmThre),DATA_TYPE_FP32, 0, 4048},
	{1,offsetof(T_SysPara,fEnvTempHighAlmRecoThre),DATA_TYPE_FP32, 0, 4049},
	{1,offsetof(T_SysPara,fEnvTempLowAlmThre),DATA_TYPE_FP32, 0, 4050},
	{1,offsetof(T_SysPara,fEnvTempLowAlmRecoThre),DATA_TYPE_FP32, 0, 4051},
	{1,offsetof(T_SysPara,fBoardTempHighcThre),DATA_TYPE_FP32, 1, 4052},
	{1,offsetof(T_SysPara,fBoardTempHighAlmRecoThre),DATA_TYPE_FP32, 1, 4053},
	{1,offsetof(T_SysPara,fBoardTempHighPrtThre),DATA_TYPE_FP32, 1, 4054},
	{1,offsetof(T_SysPara,fBoardTempHighPrtRecoThre),DATA_TYPE_FP32, 1, 4055},
	{1,offsetof(T_SysPara,fChgCurrHighAlmThre),DATA_TYPE_FP32, 2, 4056},
	{1,offsetof(T_SysPara,fChgCurrHighAlmRecThre),DATA_TYPE_FP32, 2, 4057},
	{1,offsetof(T_SysPara,fChgCurrHighPrtThre),DATA_TYPE_FP32, 2, 4058},
	{1,offsetof(T_SysPara,fChgCurrHighPrtRecThre),DATA_TYPE_FP32, 2, 4059},
	{1,offsetof(T_SysPara,fDischgCurrHighAlmThre),DATA_TYPE_FP32, 2, 4060},
	{1,offsetof(T_SysPara,fDischgCurrHighAlmRecThre),DATA_TYPE_FP32, 2, 4061},
	{1,offsetof(T_SysPara,fDischgCurrHighPrtThre),DATA_TYPE_FP32, 2, 4062},
	{1,offsetof(T_SysPara,fDischgCurrHighPrtRecThre),DATA_TYPE_FP32, 2, 4063},
	{1,offsetof(T_SysPara,ucGyroAngle),DATA_TYPE_INT8U, 0, 4067},
    {1,offsetof(T_SysPara,bAntiTheftWireEnable),DATA_TYPE_INT8U, 0, 4068},
	{1,offsetof(T_SysPara,wSoftAntiTheftDelay),DATA_TYPE_INT16U, 0, 4069},
	{1,offsetof(T_SysPara,wGPSAntiTheftDistance),DATA_TYPE_INT16U, 0, 4070},
	{1,offsetof(T_SysPara,fCellChargeFullVolt),DATA_TYPE_FP32, 2, 1478},
};

const T_OneOtherSnmpStruct s_atOtherSnmp[] = {
	{1,1,offsetof(T_SysPara,acBackstageIpAddr),DATA_TYPE_CHAR, 16, 1189},
	{1,1,offsetof(T_SysPara,wBackstagePort),DATA_TYPE_INT16U, 0, 1209},
	{1,1,offsetof(T_SysPara,acGPGSAPN),DATA_TYPE_CHAR, 20, 1240},
	{1,1,offsetof(T_SysPara,acAlmPhoneNum_1),DATA_TYPE_CHAR, 20, 1260},
	{1,1,offsetof(T_SysPara,acAlmPhoneNum_2),DATA_TYPE_CHAR, 20, 1280},
	{1,1,offsetof(T_SysPara,acAlmPhoneNum_3),DATA_TYPE_CHAR, 20, 1300},
	{1,1,offsetof(T_SysPara,acAlmPhoneNum_4),DATA_TYPE_CHAR, 20, 1320},
	{1,1,offsetof(T_SysPara,acAlmPhoneNum_5),DATA_TYPE_CHAR, 20, 1340},
	{1,1,offsetof(T_SysPara,acDeviceName),DATA_TYPE_CHAR, 20, 1380},
	{1,0,offsetof(T_SysPara,acSNMPTrapIP),DATA_TYPE_CHAR, 16, 65535},
	{1,0,offsetof(T_SysPara,acSNMPReadCommunity),DATA_TYPE_CHAR, 16, 65535},
	{1,0,offsetof(T_SysPara,acSNMPSetCommunity),DATA_TYPE_CHAR, 16, 65535},
	{1,0,offsetof(T_SysPara,ucSNMPV3UserLevel),DATA_TYPE_INT8U, 0, 65535},
	{1,0,offsetof(T_SysPara,acSNMPV3UserName),DATA_TYPE_CHAR, 16, 65535},
	{1,0,offsetof(T_SysPara,acSNMPV3AuthPass),DATA_TYPE_CHAR, 16, 65535},
	{1,0,offsetof(T_SysPara,acSNMPV3PrivPass),DATA_TYPE_CHAR, 16, 65535},
	{1,0,offsetof(T_SysPara,wSNMPTrapPort),DATA_TYPE_INT16U, 0, 65535},
	{1,0,offsetof(T_SysPara,wSNMPAgentPort),DATA_TYPE_INT16U, 0, 65535},
	{1,0,offsetof(T_SysPara,acLocalIPAddr),DATA_TYPE_CHAR, 16, 65535},
	{1,0,offsetof(T_SysPara,acMask),DATA_TYPE_CHAR, 16, 65535},
	{1,0,offsetof(T_SysPara,acGateway),DATA_TYPE_CHAR, 16, 65535},
	{1,0,offsetof(T_SysPara,ucLocalIPGetMode),DATA_TYPE_INT8U, 0, 65535},
};
#endif
