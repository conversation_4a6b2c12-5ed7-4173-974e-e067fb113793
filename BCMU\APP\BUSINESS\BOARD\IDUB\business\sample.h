/**************************************************************************
* Copyright (C) 2001, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：E260 DXCB板软件
* 文件名称：sample.h
* 文件说明：数据采集模块头文件
* 作    者：hlb
* 版本信息：V1.0
* 设计日期：2023-09-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/
#ifndef IDCB_MAIN_SAMPLE_H_
#define IDCB_MAIN_SAMPLE_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "pin_define.h"

/***********************  常量定义  ************************/
#define ADCCONVERTEDVALUES_SAMPLING_NUM  16   //ADC采样次数
#define ANALOG_COUNT                     62
#define ADC_DEV_NAME        "adc0"
#define REFER_VOLTAGE       3

/*********************  数据结构定义  **********************/
typedef enum 
{
    IO1=0, 
	IO9, 
	IO17,
    IO25,
	IO32,
	IO40,
	IO48,
    IO56,
	IO2, 
    IO10,
	IO18,
	IO26,
	IO33,
    IO41,
	IO49,
	IO57,
	IO3, 
    IO11,
	IO19,
    IO27,
	IO34,
	IO42,
	IO50,
    IO58,
	IO4, 
	IO12,
	IO20,
    IO28,
	IO35,
	IO43,
	IO51,
	IO59,
	IO5, 
    IO13,
	IO21,
	IO29,
	IO36,
    IO44,
	IO52,
	IO60,
	IO6, 
	IO14,
	IO22,
    IO30,
	IO37,
	IO45,
	IO53,
    IO61,
	IO7, 
	IO15,
	IO23,
	IO31,
	IO38,
    IO46,
	IO54,
	IO62,
	IO8, 
    IO16,
	IO24,
	IO39,
	IO47,
	IO55,
    SAMPLE_CHANNL_END,
}ADC_SAMPLE_CHANNEL;//采样顺序不按照实际端口顺序，而是将（PIN_A0_S1、PIN_A1_S1，PIN_A2_S1）一致的连着采，避免采样通道频繁切换数据不稳定


typedef struct 
{
    int 				  id_index;
    ADC_SAMPLE_CHANNEL    sample_channel;
    int                   select_status;
    int                   adc_channel;
    char*                 adc_device;
    unsigned int          data_sid;
} ADC_CHANNEL_INFO_STRUCT;


/*********************  函数原型定义  **********************/

void *sample_init_sys(void * param);
void sample_main(void * parameter);
int parse_reg_from_buff(void* dev_inst, void* cmd_buff);
void sample(void);
void analog_sample(void);
unsigned int MedianFilter(unsigned int data[ADCCONVERTEDVALUES_SAMPLING_NUM]);
void hal_board_gpio_init(void);
int adc_get_data_by_channel(ADC_SAMPLE_CHANNEL channel);
void set_selection_sample_gpio(ADC_SAMPLE_CHANNEL channel);



#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  
