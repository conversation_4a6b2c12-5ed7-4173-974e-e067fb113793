/**
 * @file     dev_dc_ac_1363.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR> @date     
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */


#ifndef _DEV_DC_AC_1363_H
#define _DEV_DC_AC_1363_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "device_type.h"
#include "cmd.h"
#include "linklayer.h"
#include "protocol_layer.h"
#include "dev_dc_ac.h"

#define S1363_VERSION          "V1.21"

#define PARA_TYPE              0x02

#define APP_COMMON_NAME          "user"
#define APP_ADMIN_NAME           "admin"
#define RTN_SUCCESSFUL            0
#define RTN_PASSWORD_ERR          0x20
#define RTN_USER_NAME_ERR         0x21
#define RTN_DEVICE_LOCK           0x22
#define RTN_DEF_PASSWD_SUCC       0x23
#define RTN_PASSWD_OVERDUE_SUCC   0x24
#define RTN_ACCOUNT_EXPIRED       0x25


#define RTN_POWER_ON_OFF_FAIL     0x30
#define RTN_POWER_ON_TIME_FAIL    0x31
#define RTN_POWER_ON_ESD_FAIL     0x32


#define CMD_TYPE_INDEX            0
#define USER_NAME_INDEX           1
#define CIPHERTEXT_INDEX         (1 + 16)
#define NEW_PASSWORD_INDEX       (1 + 16 + 45)     
#define USER_NAME_LEN             16
#define PASSWORD_LEN              31 
#define CIPHERTEXT_LEN            45
#define APP_LOCK_PERIOD           (30 * 60 * 1000)    // APP锁定时间周期为30分钟
#define APP_COUNT_PERIOD          (5 * 60 * 1000)     // 5分钟内，登陆失败三次，自锁
#define APP_OVERDUE_PERIOD        (10 * 24 * 3600 * 1000)   // 10天定期修改密码提醒
#define APP_LOAD_FAIL_TIMES       3            // APP锁定的最大错误次数

#define VERSION_LEN                  32

#define CSC_VERSION_INDEX            1
#define CSC_FILE_NAME_INDEX          (CSC_VERSION_INDEX + VERSION_LEN)
#define CSC_FILE_VERSION_INDEX       (CSC_FILE_NAME_INDEX + VERSION_LEN)

#define PSC_VERSION_INDEX            1
#define CPLD_FILE_NAME_INDEX         (PSC_VERSION_INDEX + VERSION_LEN)
#define CPLD_FILE_VERSION_INDEX      (CPLD_FILE_NAME_INDEX + VERSION_LEN)
#define SLAVE_FILE_NAME_INDEX        (CPLD_FILE_VERSION_INDEX + VERSION_LEN)
#define SLAVE_FILE_VERSION_INDEX     (SLAVE_FILE_NAME_INDEX + VERSION_LEN)
#define MASTER_FILE_NAME_INDEX       (SLAVE_FILE_VERSION_INDEX + VERSION_LEN)
#define MASTER_FILE_VERSION_INDEX    (MASTER_FILE_NAME_INDEX + VERSION_LEN)

#define ANA_DATA_FLAG_DEF_VALUE 1       ///模拟量数据flag默认值

#define FAULT_RECORD_NUM           2

#define TEST_HIS_DATA                      0x01
#define TEST_HIS_ALM                       0x02
#define TEST_OPERATE_RECORD                0x03
#define TEST_ENERGY_DATA                   0x04
#define SOUTH_PKT_CNT                      0x05    // print_south_pkt_cnt
#define NORTH_PKT_CNT                      0x06    // print_north_pkt_cnt
#define GET_ONE_DATA                       0x07    // probe_get_one_data
#define SET_ONE_DATA                       0x08    // probe_get_one_data
#define ALARM_PRINT                        0x09    // alarm_print
#define PRINT_DEV_ID                       0x0A    // print_device_id
#define PRINT_SUB_TOPIC                    0x0B    //print_subdcribe_topic
#define CAN_STATS                          0x0C    // can_stats
#define FREE                               0x0D    // free
#define DF                                 0x0E    // df
#define NETSTAT                            0x0F    // netstat
#define IFCONFIG                           0x10    // ifconfig
#define UARTSTAT                           0x11    // uartstat
#define READ_MCU_REG                       0x12    // read_mcu_reg
#define STORAGE_COUNT                      0x13    // print_storage_count
#define POWER_TRANS_ENABLE                 0x14    // 功率转换使能

/* 设备缓冲区长度 */
#define INVAILD_DATA                       0x20      ///< 无效数据
#define S1363_HIGH_SEND_LEN                960      ///<发送缓冲区长度


#define POW_10 10
#define POW_100 100
#define POW_1000 1000

#define U_CHAR_INVALID 0xFF
#define U_SHORT_INVAILD 0xFFFF
#define U_INT_INVAILD 0xFFFFFFFF

#define CHAR_INVALID 0x80
#define SHORT_INVAILD 0x8000
#define INT_INVALID 0x80000000

#define MAX_POINTS_NUMS 10 ///<最大曲线点数
#define MAX_PROT_LEVELS 6 ///<最大保护等级数
#define PV_NUM_MAX                         8         /// PV的数量
#define MPPT_NUM_MAX                       4         /// MPPT的数量
#define IN_RELAY_NUM                       4         /// 输入干接点状态数量

// 运行状态
#define SPOT_CHECK      0x0100          /// 点检
#define SHUTDOWN        0x0080          /// 关机
#define SET_STOP        0x0040          /// 功率设定停机
#define FAULT_STOP      0x0020          /// 故障停机
#define NORMAL_STOP     0x0010          /// 正常停机
#define CONN_DE_IN      0x0008          /// 并网降额
#define CONN_DE_POWER   0x0004          /// 并网降额（功率设定）
#define CONN_NORMAL     0x0002          /// 并网正常
#define GRID_CONN       0x0001          /// 并网
#define STANDBY         0x0000          /// 待机


/*S1363的命令唯一标识定义 */
//#define GET_ANA_DATA            1          //<获取模拟量量化后数据（定点数）
#define GET_ALM_DATA            2           //<获取告警量
#define GET_DIG_DATA            3           //<获取状态量
#define GET_SYSHIS_DATA         4          //获取系统历史数据（定点数）	
#define GET_HIS_ALM_DATA        5         //<获取历史告警
#define GET_CSU_TIME_DATA       6         //<获取监测模块时间
#define SET_CSU_TIME_DATA       7           //<设置监测模块时间
#define GET_VER_DATA            8         //<获取通信协议版本号
#define GET_ADDR_DATA           9         //<获取设备地址
#define GET_CSU_FACT_DATA       10         //<获取设备（监测模块）厂家信息
#define GET_POWER_FACT_DATA     11          //<获取设备（功率模块）厂家信息
#define GET_SYSPARA_DATA     12           //获取系统参数   
#define GET_CURVE_PARA_DATA  13         //<获取特征曲线参数（定点数)
#define GET_PROT_PARA_DATA   14          //<获取保护参数（定点数)
#define GET_POWER_PARA_DATA  15           //<获取功率参数（定点数）
#define GET_ADJUST_PARA_DATA 16          //获取比例校准参数（定点数）	
#define GET_PROD_PARA_DATA  17          //获取生产参数（定点数）

/******************************************************************************************************/
#define SET_PROT_PARA_DATA   18         //<设定保护参数(定点数)
//////////
#define SET_PROT_PARA_DATA1 19 //设置一级保护参数
#define SET_PROT_PARA_DATA2 20 //设置二级保护参数
#define SET_PROT_PARA_DATA3 21 //设置三级保护参数
#define SET_PROT_PARA_DATA4 22 //设置四级保护参数
#define SET_PROT_PARA_DATA5 23 //设置五级保护参数
#define SET_PROT_PARA_DATA6 24 //设置六级保护参数

#define SET_CURVE_PARA_DATA  25          //<设置特征曲线参数（定点数）
/////////
#define SET_CURVE_PARA_DATA1 26    //<设置特征曲线参数-LVRT特征曲线
#define SET_CURVE_PARA_DATA2 27   //<设置特征曲线参数-cosφ-P/Pn特性曲线
#define SET_CURVE_PARA_DATA3 28   //<设置特征曲线参数-Q-U特性曲线设置
#define SET_CURVE_PARA_DATA4 29   //<设置特征曲线参数-PF-U特性曲线设置

#define SET_SYSPARA_DATA     30          //<设定系统参数（定点数)
////////
#define SET_SYSPARA_DATA1    31
#define SET_SYSPARA_DATA2    32
#define SET_SYSPARA_DATA3    33
#define SET_SYSPARA_DATA4    34
#define SET_SYSPARA_DATA5    35
#define SET_SYSPARA_DATA6    36
#define SET_SYSPARA_DATA7    37
#define SET_SYSPARA_DATA8    38


#define SET_POWER_PARA_DATA  39         //<设定功率参数（定点数）
///////
#define SET_POWER_PARA_DATA1 40      
#define SET_POWER_PARA_DATA2 41
#define SET_POWER_PARA_DATA3 42
#define SET_POWER_PARA_DATA4 43

///////
#define SET_ADJUST_PARA_DATA     44          //设置比例校准参数（定点数）
#define SET_ADJUST_PARA_DATA1    45 
#define SET_ADJUST_PARA_DATA2    46 
#define SET_ADJUST_PARA_DATA3    47 
#define SET_ADJUST_PARA_DATA4    48 
#define SET_ADJUST_PARA_DATA5    49 
#define SET_ADJUST_PARA_DATA6    50 
#define SET_ADJUST_PARA_DATA7    51 

#define SET_PRODUCE_PARA_DATA        52         //设置生产参数（定点数）
#define SET_PRODUCE_PARA_DATA1       53  
#define SET_PRODUCE_PARA_DATA2       54  
#define SET_PRODUCE_PARA_DATA3       55  
#define SET_PRODUCE_PARA_DATA4       56  

#define DAC_GET_ANA_FLOAT_DATA           57         //<获取模拟量量化后数据（浮点数)
#define DAC_GET_ANA_INT_DATA             58         //<获取模拟量量化后数据（定点数）
#define DAC_GET_ANA_INT_INVALI_DATA      60         //<获取模拟量量化后数据（定点数） 无效值
#define DAC_GET_ANA_INT_VAILD_DATA       59         //<获取模拟量量化后数据（定点数） 有效值
#define DAC_GET_DIG_DATA                 61         //<获取数字量量化后数据
#define DAC_GET_ALM_DATA                 62         //<获取告警量数据

#define DAC_TEST_ORDER                   63          //生成测试数据

#define SET_SYSPARA_DATA9                64
//新增命令唯一标识

#define SET_POWER_PARA_DATA5             65
#define SET_POWER_PARA_DATA6             66
#define SET_SYSPARA_DATA10               67

#define DAC_CONTROL_ORDER                68        //控制命令

#define DAC_GET_ANA_INT_DATA_2           69         //<获取模拟量量量化后数据（定点数）2
#define DAC_GET_ANA_INT_INVALI_DATA_2    70         //<获取模拟量量化后数据（定点数） 无效值2
#define DAC_GET_ANA_INT_VAILD_DATA_2     71         //<获取模拟量量化后数据（定点数） 有效值2

#define DAC_GET_FAULT_RECORD_PARA        72
#define DAC_SET_FAULT_RECORD_PARA        73
#define DAC_SET_FAULT_RECORD1_PARA       74
#define DAC_SET_FAULT_RECORD2_PARA       75

#define DAC_SET_ALM_LEVEL                76
#define DAC_SET_MPPT_OVER_VOLT_LEVEL                    77
#define DAC_SET_BUS_OVER_VOLT_LEVEL                     78
#define DAC_SET_POS_BUS_OVER_VOLT_LEVEL                 79
#define DAC_SET_NEG_BUS_OVER_VOLT_LEVEL                 80
#define DAC_SET_BUS_VOLT_IMBALANCE_LEVEL                81
#define DAC_SET_STR_REVERSE_CONNECT_LEVEL               82
#define DAC_SET_STR_LOSS_LEVEL                          83
#define DAC_SET_MPPT_OVER_CURR_WARN_LEVEL               84
#define DAC_SET_MPPT_OVER_CURR_ERROR_LEVEL              85
#define DAC_SET_BUS_SOFT_START_ERR_LEVEL                86
#define DAC_SET_DC_DISCONNECTOR_TRIP_LEVEL              87
#define DAC_SET_PHASE_VOLT_OVER_VOLT_LEVEL              88
#define DAC_SET_OVER_VOLT_BETWEEN_PHASE_LEVEL           89
#define DAC_SET_PHASE_VOLT_UNDER_VOLT_LEVEL             90
#define DAC_SET_UNDER_VOLT_BETWEEN_PHASE_LEVEL          91
#define DAC_SET_GRID_OVER_FREQ_LEVEL                    92
#define DAC_SET_GRID_UNDER_FREQ_LEVEL                   93
#define DAC_SET_AC_PHASE_LOSS_LEVEL                     94
#define DAC_SET_OUTPUT_SHORT_CIRCUIT_LEVEL              95
#define DAC_SET_OUT_CURR_IMBALANCE_LEVEL                96
#define DAC_SET_ABNORMAL_DC_COMPONENT_LEVEL             97
#define DAC_SET_ISLAND_PROT_LEVEL                       98
#define DAC_SET_PV_OVER_CURR_WARN_LEVEL                 99
#define DAC_SET_PV_OVER_CURR_ERROR_LEVEL                100
#define DAC_SET_GRID_ANTI_ERROR_LEVEL                   101
#define DAC_SET_SINGLE_PHASE_GND_SHORT_CIRCUIT_LEVEL    102
#define DAC_SET_LOW_POWER_SHUTDOWN_LEVEL                103
#define DAC_SET_LOW_VOLT_RT_LEVEL                       104
#define DAC_SET_HIGH_VOLT_RT_LEVEL                      105
#define DAC_SET_TEN_MINUTE_GRID_OVER_VOLT_PROT_LEVEL    106
#define DAC_SET_ENVIR_OVER_TEMP_LEVEL                   107
#define DAC_SET_ENVIR_LOW_TEMP_LEVEL                    108
#define DAC_SET_ABN_RESIDUAL_CURR_LEVEL                 109
#define DAC_SET_BUS_POS_IMPEDANCE_WARN_LEVEL            110
#define DAC_SET_BUS_NEG_IMPEDANCE_WARN_LEVEL            111
#define DAC_SET_BUS_POS_IMPEDANCE_ERROR_LEVEL           112
#define DAC_SET_BUS_NEG_IMPEDANCE_ERROR_LEVEL           113
#define DAC_SET_RADIATOR_OVER_TEMP_LEVEL                114
#define DAC_SET_INTER_FAN_FAIL_LEVEL                    115
#define DAC_SET_EXTER_FAN_FAIL_LEVEL                    116
#define DAC_SET_OVER_TEMP_INSIDS_MACHINE_LEVEL          117
#define DAC_SET_FIRE_ERROR_LEVEL                        118
#define DAC_SET_DC_ARC_DAULT_LEVEL                      119
#define DAC_SET_AFCI_SELF_CHK_FAIL_LEVEL                120
#define DAC_SET_AC_LIGHTING_PROTECTION_ERR_LEVEL        121
#define DAC_SET_DC_LIGHTING_PROTECTION_ERR_LEVEL        122
#define DAC_SET_BOOST_IGBT_OVER_TEMP_LEVEL              123
#define DAC_SET_PV_IGBT_OVER_TEMP_LEVEL                 124
#define DAC_SET_GRID_RELAY_ERROR_LEVEL                  125
#define DAC_SET_AC_CURR_SENSOR_ERROR_LEVEL              126
#define DAC_SET_PV_LOCK_ERROR_LEVEL                     127
#define DAC_SET_ABNORMAL_OPER_BUILD_IN_PID_LEVEL        128
#define DAC_SET_ABN_AUXI_POWER_SUPPLT_LEVEL             129
#define DAC_SET_MAIN_CTRL_EEPROM_FAULT_LEVEL            130
#define DAC_SET_MAIN_CTRL_EEPROM_ABNORMAL_LEVEL         131
#define DAC_SET_AUXI_CTRL_EEPROM_FAULT_LEVEL            132
#define DAC_SET_AUXI_CTRL_EEPROM_ABNORMAL_LEVEL         133
#define DAC_SET_MONITER_MAIN_CTRL_COMMU_ERROR_LEVEL     134
#define DAC_SET_CARRIER_SYNC_ABNORMAL_LEVEL             135
#define DAC_SET_PROTOCOL_VER_MISMATCH_LEVEL             136
#define DAC_SET_LICENSE_EXPIRED_LEVEL                   137
#define DAC_SET_MONITER_ERROR_ALARM_LEVEL               138
#define DAC_SET_MAIN_AUXI_COMMU_ERROR_LEVEL             139
#define DAC_SET_MAIN_CPLD_COMMU_ERROR_LEVEL             140

#define DAC_SET_ALM_RELAY                               141
#define DAC_SET_MPPT_OVER_VOLT_RELAY                    142
#define DAC_SET_BUS_OVER_VOLT_RELAY                     143
#define DAC_SET_POS_BUS_OVER_VOLT_RELAY                 144
#define DAC_SET_NEG_BUS_OVER_VOLT_RELAY                 145
#define DAC_SET_BUS_VOLT_IMBALANCE_RELAY                146
#define DAC_SET_STR_REVERSE_CONNECT_RELAY               147
#define DAC_SET_STR_LOSS_RELAY                          148
#define DAC_SET_MPPT_OVER_CURR_WARN_RELAY               149
#define DAC_SET_MPPT_OVER_CURR_ERROR_RELAY              150
#define DAC_SET_BUS_SOFT_START_ERR_RELAY                151
#define DAC_SET_DC_DISCONNECTOR_TRIP_RELAY              152
#define DAC_SET_PHASE_VOLT_OVER_VOLT_RELAY              153
#define DAC_SET_OVER_VOLT_BETWEEN_PHASE_RELAY           154
#define DAC_SET_PHASE_VOLT_UNDER_VOLT_RELAY             155
#define DAC_SET_UNDER_VOLT_BETWEEN_PHASE_RELAY          156
#define DAC_SET_GRID_OVER_FREQ_RELAY                    157
#define DAC_SET_GRID_UNDER_FREQ_RELAY                   158
#define DAC_SET_AC_PHASE_LOSS_RELAY                     159
#define DAC_SET_OUTPUT_SHORT_CIRCUIT_RELAY              160
#define DAC_SET_OUT_CURR_IMBALANCE_RELAY                161
#define DAC_SET_ABNORMAL_DC_COMPONENT_RELAY             162
#define DAC_SET_ISLAND_PROT_RELAY                       163
#define DAC_SET_PV_OVER_CURR_WARN_RELAY                 164
#define DAC_SET_PV_OVER_CURR_ERROR_RELAY                165
#define DAC_SET_GRID_ANTI_ERROR_RELAY                   166
#define DAC_SET_SINGLE_PHASE_GND_SHORT_CIRCUIT_RELAY    167
#define DAC_SET_LOW_POWER_SHUTDOWN_RELAY                168
#define DAC_SET_LOW_VOLT_RT_RELAY                       169
#define DAC_SET_HIGH_VOLT_RT_RELAY                      170
#define DAC_SET_TEN_MINUTE_GRID_OVER_VOLT_PROT_RELAY    171
#define DAC_SET_ENVIR_OVER_TEMP_RELAY                   172
#define DAC_SET_ENVIR_LOW_TEMP_RELAY                    173
#define DAC_SET_ABN_RESIDUAL_CURR_RELAY                 174
#define DAC_SET_BUS_POS_IMPEDANCE_WARN_RELAY            175
#define DAC_SET_BUS_NEG_IMPEDANCE_WARN_RELAY            176
#define DAC_SET_BUS_POS_IMPEDANCE_ERROR_RELAY           177
#define DAC_SET_BUS_NEG_IMPEDANCE_ERROR_RELAY           178
#define DAC_SET_RADIATOR_OVER_TEMP_RELAY                179
#define DAC_SET_INTER_FAN_FAIL_RELAY                    180
#define DAC_SET_EXTER_FAN_FAIL_RELAY                    181
#define DAC_SET_OVER_TEMP_INSIDS_MACHINE_RELAY          182
#define DAC_SET_FIRE_ERROR_RELAY                        183
#define DAC_SET_DC_ARC_DAULT_RELAY                      184
#define DAC_SET_AFCI_SELF_CHK_FAIL_RELAY                185
#define DAC_SET_AC_LIGHTING_PROTECTION_ERR_RELAY        186
#define DAC_SET_DC_LIGHTING_PROTECTION_ERR_RELAY        187
#define DAC_SET_BOOST_IGBT_OVER_TEMP_RELAY              188
#define DAC_SET_PV_IGBT_OVER_TEMP_RELAY                 189
#define DAC_SET_GRID_RELAY_ERROR_RELAY                  199
#define DAC_SET_AC_CURR_SENSOR_ERROR_RELAY              200
#define DAC_SET_PV_LOCK_ERROR_RELAY                     201
#define DAC_SET_ABNORMAL_OPER_BUILD_IN_PID_RELAY        202
#define DAC_SET_ABN_AUXI_POWER_SUPPLT_RELAY             203
#define DAC_SET_MAIN_CTRL_EEPROM_FAULT_RELAY            204
#define DAC_SET_MAIN_CTRL_EEPROM_ABNORMAL_RELAY         205
#define DAC_SET_AUXI_CTRL_EEPROM_FAULT_RELAY            206
#define DAC_SET_AUXI_CTRL_EEPROM_ABNORMAL_RELAY         207
#define DAC_SET_MONITER_MAIN_CTRL_COMMU_ERROR_RELAY     208
#define DAC_SET_CARRIER_SYNC_ABNORMAL_RELAY             209
#define DAC_SET_PROTOCOL_VER_MISMATCH_RELAY             210
#define DAC_SET_LICENSE_EXPIRED_RELAY                   211
#define DAC_SET_MONITER_ERROR_ALARM_RELAY               212
#define DAC_SET_MAIN_AUXI_COMMU_ERROR_RELAY             213
#define DAC_SET_MAIN_CPLD_COMMU_ERROR_RELAY             214

#define DAC_GET_ALM_RELAY                               215
#define DAC_GET_ALM_LEVEL                               216

#define SET_SYSPARA_DATA11                              217
#define SET_SYSPARA_DATA12                              218         //<设置电网标准码参数
#define DAC_GET_DIFF_UPDATE_FLAG                        219
#define DAC_GET_MONITER_PARA                            220         //<获取监控参数
#define DAC_SET_MONITER_PARA                            221         //<设置监控参数
#define DAC_SET_MONITER_PARA1                           222         //<设置监控参数(时间信息)
#define DAC_SET_MONITER_PARA2                           223        //<设置监控参数（RS485）
#define DAC_SET_MONITER_PARA3                           224        //<设置监控参数(WLAN参数)
#define DAC_SET_MONITER_PARA4                           225         //<设置监控参数（dongel wifi配置）
#define DAC_SET_MONITER_PARA5                           226         //<设置监控参数（MQTT配置）
#define DAC_SET_MONITER_PARA6                           227         //<设置监控参数（APP请求登录）
#define DAC_SET_MONITER_PARA7                           228         //<设置监控参数（APP修改密码）
#define SET_CURVE_PARA_DATA5                            229         //<设置特征曲线参数HVRT_CC特性曲线设置
#define DAC_SET_CSC_VERSION_PARA                        230         //<设置csc版本信息
#define DAC_SET_PSC_VERSION_PARA                        231         //<设置psc版本信息
#define SET_ADJUST_PARA_DATA8                           232         //<漏电流比例零点校正
#define DAC_SET_MONITER_PARA8                           233         //<第三方网管配置
#define DAC_SET_MONITER_PARA9                           234         //<经纬度信息
#define DAC_GET_PARALLEL_INFO                           235         //<获取并机数据
#define SET_ADJUST_PARA_DATA9                           236         //<直流分量比例零点校正

#define GET_PROT_PARA_DATA1                             237         //<获取所有保护参数
#define GET_PROT_PARA_DATA2                             238         //<获取一级保护参数
#define GET_PROT_PARA_DATA3                             239         //<获取二级保护参数

#define GET_CURVE_PARA_DATA1                            240         //<获取所有特征曲线参数 
#define GET_CURVE_PARA_DATA2                            241         //<获取特征曲线参数
#define GET_CURVE_PARA_DATA3                            242         //<获取特征曲线参数 
#define GET_CURVE_PARA_DATA4                            243         //<获取特征曲线参数 
#define GET_CURVE_PARA_DATA5                            244         //<获取特征曲线参数 
#define GET_CURVE_PARA_DATA6                            245         //<获取特征曲线参数

#define GET_SYSPARA_DATA1                               246         //<获取所有系统参数
#define GET_SYSPARA_DATA2                               247         //<获取系统参数
#define GET_SYSPARA_DATA3                               248         //<获取系统参数
#define GET_SYSPARA_DATA4                               249         //<获取系统参数
#define GET_SYSPARA_DATA5                               250         //<获取系统参数
#define GET_SYSPARA_DATA6                               251         //<获取系统参数
#define GET_SYSPARA_DATA7                               252         //<获取系统参数
#define GET_SYSPARA_DATA8                               253         //<获取系统参数
#define GET_SYSPARA_DATA9                               254         //<获取系统参数
#define GET_SYSPARA_DATA10                              255         //<获取系统参数
#define GET_SYSPARA_DATA11                              256         //<获取系统参数
#define GET_SYSPARA_DATA12                              257         //<获取系统参数

#define GET_POWER_PARA_DATA1                            258         //<获取所有功率参数
#define GET_POWER_PARA_DATA2                            259         //<获取功率参数
#define GET_POWER_PARA_DATA3                            260         //<获取功率参数
#define GET_POWER_PARA_DATA4                            261         //<获取功率参数
#define GET_POWER_PARA_DATA5                            262         //<获取功率参数
#define GET_POWER_PARA_DATA6                            263         //<获取功率参数
#define GET_POWER_PARA_DATA7                            264         //<获取功率参数

#define GET_ADJUST_PARA_DATA1                           265         //<获取所有校正参数
#define GET_ADJUST_PARA_DATA2                           266         //<获取校正参数
#define GET_ADJUST_PARA_DATA3                           267         //<获取校正参数
#define GET_ADJUST_PARA_DATA4                           268         //<获取校正参数
#define GET_ADJUST_PARA_DATA5                           269         //<获取校正参数
#define GET_ADJUST_PARA_DATA6                           270         //<获取校正参数
#define GET_ADJUST_PARA_DATA7                           271         //<获取校正参数
#define GET_ADJUST_PARA_DATA8                           272         //<获取校正参数
#define GET_ADJUST_PARA_DATA9                           273         //<获取校正参数
#define GET_ADJUST_PARA_DATA10                          274         //<获取校正参数

#define GET_PROD_PARA_DATA1                             275         //<获取所有生产参数
#define GET_PROD_PARA_DATA2                             276         //<获取生产参数
#define GET_PROD_PARA_DATA3                             277         //<获取生产参数

#define DAC_GET_MONITER_PARA1                           278         //<获取所有监控参数
#define DAC_GET_MONITER_PARA2                           279         //<获取监控参数
#define DAC_GET_MONITER_PARA3                           280         //<获取监控参数
#define DAC_GET_MONITER_PARA4                           281         //<获取监控参数
#define DAC_GET_MONITER_PARA5                           282         //<获取监控参数
#define DAC_GET_MONITER_PARA6                           283         //<获取监控参数
#define DAC_GET_MONITER_PARA7                           284         //<获取监控参数
#define DAC_GET_MONITER_PARA8                           285         //<获取监控参数
#define DAC_GET_MONITER_PARA9                           286         //<获取监控参数
#define DAC_GET_MONITER_PARA10                          287         //<获取监控参数

#define DAC_GET_FAULT_RECORD_PARA1                      288         //<获取所有故障录波参数
#define DAC_GET_FAULT_RECORD_PARA2                      289         //<获取故障录波参数
#define DAC_GET_FAULT_RECORD_PARA3                      290         //<获取故障录波参数

#define DAC_GET_1363_VERSION_INFO                       291         //<获取1363协议版本号

#define DAC_SET_DELAY_UPDATE                            292         //<设置延迟升级
#define DAC_GET_DELAY_UPDATE                            293         //<获取延迟升级
#define GET_SYSPARA_DATA13                              294         //<获取电网标准码参数

#define SET_SYSPARA_DATA13                              295         //<设置组串接入检测
#define GET_SYSPARA_DATA14                              296         //<获取组串接入检测

#define SET_SYSPARA_DATA14                              297         //<设置过欠频降额参数
#define GET_SYSPARA_DATA15                              298         //<获取过欠频降额参数

#define DAC_SET_POWER_SPECIAL_PARA                      299         //设置功率研发专用
#define DAC_SET_POWER_SPECIAL_PARA1                     300         //设置功率研发专用参数
#define DAC_GET_POWER_SPECIAL_PARA                      301         //获取功率研发专用
#define DAC_GET_POWER_SPECIAL_PARA1                     302         //获取所有块的数据
#define DAC_GET_POWER_SPECIAL_PARA2                     303         //获取功率研发专用参数

#define DAC_SET_POWER_SPECIAL_PARA2                     304         //设置功率研发专用参数-驱动测试相关参数
#define DAC_SET_POWER_SPECIAL_PARA3                     305         //设置功率研发专用参数-ADC采样测试相关参数
#define DAC_SET_POWER_SPECIAL_PARA4                     306         //设置功率研发专用参数-生产测试相关设置参数

#define DAC_GET_POWER_SPECIAL_PARA3                     307         //获取功率研发专用参数-驱动测试相关参数
#define DAC_GET_POWER_SPECIAL_PARA4                     308         //获取功率研发专用参数-ADC采样测试相关参数
#define DAC_GET_POWER_SPECIAL_PARA5                     309         //获取功率研发专用参数-生产测试相关设置参数

#define GET_PROD_PARA_DATA4                             310         //<获取生产参数-产品信息
#define GET_POWER_SPECIAL_ANA                           311         //<获取功率研发专用模拟量

#define SET_HARDWARE_VERSION_PARA                       312         //设置硬件版本号
#define GET_HARDWARE_VERSION_PARA                       313         //获取硬件版本号

#define DAC_SET_USER_AUTH_TIME                          314         //<设置USER账号授权时间
#define DAC_GET_USER_AUTH_TIME                          315         //<获取USER账号授权时间

/*S1363的cid2 功能码定义 */
#define CMD_GET_ANA_FLOAT_DATA      0x41           //<获取模拟量量化后数据（浮点数)
#define CMD_GET_ANA_INT_DATA        0x42           //<获取模拟量量化后数据（定点数）
#define CMD_GET_ALM_DATA            0x43           //<获取告警量
#define CMD_GET_DIG_DATA            0x44           //<获取状态量
#define CMD_CONTROL_ORDER           0x45           //<遥控命令
#define CMD_GET_SYSPARA_FLOAT_DATA  0x46           //<获取系统参数（浮点数）
#define CMD_GET_SYSPARA_INT_DATA    0x47           //<获取系统参数（定点数)
#define CMD_SET_SYSPARA_FLOAT_DATA  0x48           //<设定系统参数（浮点数）
#define CMD_SET_SYSPARA_INT_DATA    0x49           //<设定系统参数（定点数)
#define CMD_GET_SYSHIS_FLOAT_DATA   0x4A           //获取系统历史数据（浮点数）	
#define CMD_GET_SYSHIS_INT_DATA     0x4B           //获取系统历史数据（定点数）	
#define CMD_GET_HIS_ALM_DATA        0x4C           //<获取历史告警
#define CMD_GET_CSU_TIME_DATA       0x4D           //<获取监测模块时间
#define CMD_SET_CSU_TIME_DATA       0x4E           //<设置监测模块时间
#define CMD_GET_VER_DATA            0x4F           //<获取通信协议版本号
#define CMD_GET_ADDR_DATA           0x50           //<获取设备地址
#define CMD_GET_CSU_FACT_DATA       0x51           //<获取设备（监测模块）厂家信息
//#define CMD_GET_ANA_INT_DATA      (80-EFH)           //<用户自定义
#define CMD_GET_POWER_FACT_DATA     0xE1           //<获取设备（功率模块）厂家信息
#define CMD_GET_CURVE_PARA_INT_DATA  0xE2           //<获取特征曲线参数（定点数)
#define CMD_SET_CURVE_PARA_INT_DATA  0xE3           //<设置特征曲线参数（定点数）
#define CMD_GET_PROT_PARA_INT_DATA  0xE4           //<获取保护参数（定点数)
#define CMD_SET_PROT_PARA_INT_DATA  0xE5           //<设定保护参数(定点数)
#define CMD_GET_POWER_PARA_INT_DATA 0xE6           //<获取功率参数（定点数）
#define CMD_SET_POWER_PARA_INT_DATA 0xE7           //<设定功率参数（定点数）
#define CMD_GET_ADJUST_INT_DATA        0xE8           //获取比例校准参数（定点数）	
#define CMD_SET_ADJUST_INT_DATA        0xE9           //设置比例校准参数（定点数）
#define CMD_GET_PRODUCE_INT_DATA        0xEA           //获取生产参数（定点数）	
#define CMD_SET_PRODUCE_INT_DATA        0xEB           //设置生产参数（定点数）	
#define CMD_TEST_RECORD                 0xF0           //调测命令
#define CMD_GET_ANA_INT_DATA_2        0xED            //<获取模拟量量化后数据（定点数）2
#define CMD_GET_FAULT_RECORD_DATA        0xF1         //获取录波数据
#define CMD_SET_FAULT_RECORD_DATA        0xF2         //设置录波参数
#define CMD_GET_MONITER_PARA             0xF3          //获取监控参数
#define CMD_SET_MONITER_PARA             0xF4          //设置监控参数
#define CMD_GET_ALM_LEVEL                0xF5         //获取告警等级
#define CMD_SET_ALM_LEVEL                0xF6         //设置告警等级
#define CMD_GET_ALM_RELAY                0xF7         //获取告警干接点
#define CMD_SET_ALM_RELAY                0xF8         //设置告警干接点
#define CMD_GET_DIFF_UPDATE_FLAG         0xF9         //获取差分升级标识
#define CMD_GET_PARALLEL_INFO            0xFA         //获取并机信息
#define CMD_GET_1363_VERSION             0xFB         //获取1363协议版本号
#define CMD_GET_POWER_SPECIAL_PARA       0xFC         //设置功率专用参数
#define CMD_SET_POWER_SPECIAL_PARA       0xFD         //获取功率专用参数
#define CMD_GET_POWER_SPECIAL_ANA        0xFE         //获取功率研发专用模拟量

/*cmd_type定义*/
#define RESET_CSU_CMD                                   0x00    //复位CSU
#define START_IV_SCAN_CMD                               0x01   // 启动IV扫描
#define STOP_IV_SCAN_CMD                                0x02   // 停止IV扫描
#define RESTORE_FACT_CMD                                0x03   //恢复出厂设置
// #define START_PRODUCT_MODE_CMD                          0x04   //生产测试模式启动
// #define CLOSE_PRODUCT_MODE_CMD                          0x05   //生产测试模式关闭
#define TRIGGER_FAULT_RECORD                            0x06   // 触发故障录波
#define START_PRE_DRIVER_TEST                           0x08   // 启动前级驱动测试
#define CLOSE_PRE_DRIVER_TEST                           0x09   // 关闭前级驱动测试
#define START_POSTERIOR_DRIVER_TEST                     0x0A   // 启动后级驱动测试
#define CLOSE_POSTERIOR_DRIVER_TEST                     0x0B   // 关闭后级驱动测试

#define START_AUXI_SOURCE_TEST                          0x0C   // 启动交流辅助源测试
#define CLOSE_AUXI_SOURCE_TEST                          0x0D   // 关闭交流辅助源测试

// #define FAN_CTRL_TEST                                   0x0E   //风扇控制测试
// #define RELAY_CTRL_TEST                                 0x0F   //继电器控制测试
#define START_SHIELD_CAN_COMM                           0x10   //CAN通信屏蔽开
#define STOP_SHIELD_CAN_COMM                            0x11   //CAN通讯屏蔽关
#define CLEAN_HIS_ENERGY                                0x12   //清除历史发电量
#define CLEAN_ALARM                                     0x13   //清除告警
#define RS485_STAT                                      0x14   //485统计
#define FORMAT_FILE_SYSTEM                              0x15   // 格式化文件系统

#define START_INTERNAL_FAN_TEST                         0x16   //启动内部风扇测试
#define START_RELAY_TEST                                0x17   //启动并网继电器测试
#define STOP_RELAY_TEST                                 0x18   //关闭并网继电器测试

#define RESTORE_FAULT_RECORD_SET                        0x19   //恢复故障录波默认设置
#define CLEAR_AFCI_FAIL_ALM                             0x1A   //清除AFCI自检失败告警
#define CLEAR_DC_ARC_FALUT_ALM                          0x1B   //清除直流电弧故障告警
#define CLEAR_INSULATION_LOW_ALM                        0x1C   //清除绝缘阻抗低故障告警
#define RESTART_SYSTEM_CMD                              0x1D   //系统重启

#define START_POST_DUTY_CYCLE_TEST                      0x1E   //启动后级定占空比驱动测试
#define STOP_INTERNAL_FAN_TEST                          0x1F   //关闭内部风扇测试
#define START_EXTERNAL_FAN_TEST                         0x20   //启动外部风扇测试
#define STOP_EXTERNAL_FAN_TEST                          0x21   //关闭外部风扇测试
#define START_ISOLATION_POWER                           0x22   //启动隔离开关供电
#define STOP_ISOLATION_POWER                            0x23   //断开隔离开关供电
#define START_IMPEDANCE_TEST                            0x24   //启动绝缘阻抗检测
#define STOP_IMPEDANCE_TEST                             0x25   //停止绝缘阻抗检测
#define CLOSE_PID_CTRL                                  0x26   //闭合PID控制继电器
#define DISCONN_PID_CTRL                                0x27   //断开PID控制继电器
#define CLOSE_GROUND_RELAY                              0x28   //闭合接地异常检测继电器
#define DISCONN_GROUND_RELAY                            0x29   //断开接地异常检测继电器
#define CLOSE_THEFT_RELAY                               0x2A   //闭合防盗继电器
#define DISCONN_THEFT_RELAY                             0x2B   //断开防盗继电器
#define NORMAL_MODE                                     0x2C   //正常模式
#define PRODUCT_MODE                                    0x2D   //生产测试模式
#define RD_DEBUG_MODE                                   0x2E   //研发调试模式
#define BOARD_RD_DEBUG_MODE                             0x2F   //单板研发调试模式
#define START_IMPEDENCE_DIA                             0x30   //启动绝缘阻抗诊断
#define START_AFCI                                      0x31   //启动AFCI自检
#define OPEN_FT_OUTPUT_SIGNAL                           0x32   //FT测试输出信号开
#define CLOSE_FT_OUTPUT_SIGNAL                          0x33   //FT测试输出信号关
#define SHIELD_FAULT_RECORD                             0x34   //FT测试屏蔽故障录波
#define OPEN_FT_FAULT_RECORD                            0x35   //FT测试打开故障录波
#define OPEN_LEAKAGE_CUR_SELF_CHECK                     0x36   //FT主板开漏电流自检
#define CLOSE_LEAKAGE_CUR_SELF_CHECK                    0x37   //FT主板关漏电流自检
#define CLEAN_AFCI_LOG                                  0x38   //清除AFCI数据记录
#define GET_AFCI_LOG                                    0x39   //获取AFCI数据记录
#define CLEAN_HIS_DATA                                  0x3A   //清除历史数据
#define CLEAN_HIS_EVENT                                 0x3B   //清除操作记录
#define CLEAN_IV_DATA                                   0x3C   //清除IV数据
#define CLEAN_FAULT_RECORD_DATA                         0x3D   //清除录波数据
#define DELETE_HIS_ENERGY_DATA                          0x3E   //清除历史电量指数统计数据

//控制命令功能码（南向）
#define CMD_IV_START_CTRL                   0x0001 //启动iv扫描
#define CMD_IV_CLOSE_CTRL                   0x0002 //关闭iv扫描
#define CMD_START_IMPEDENCE_DIA             0x0003 //启动绝缘阻抗诊断
#define CMD_START_AFCI                      0x0004 //启动AFCI自检
#define CMD_CLEAN_AFCI_LOG                  0x0005 //清除AFCI数据记录
#define CMD_GET_AFCI_LOG                    0x0006 //获取AFCI数据记录
#define CMD_RESTORE_FAULT_RECORD_SET        0x0020 //恢复故障录波默认设置
#define CMD_RESTART_POWER                   0x0030 //复位功率控制器
// #define CMD_START_PRODUCT_MODE              0x2401 //生产测试模式启动
// #define CMD_CLOSE_PRODUCT_MODE              0x2402 //生产测试模式关闭
// #define CMD_MASTER_CHECK_PARA_SAVE          0x2403 //主控校正参数存储

// #define CMD_AUXI_CHECK_PARA_SAVE         0x2404 //启动后级定占空比驱动测试
#define CMD_OPEN_FT_OUTPUT_SIGNAL           0x2401 //FT测试输出信号开
#define CMD_CLOSE_FT_OUTPUT_SIGNAL          0x2402 //FT测试输出信号关
#define CMD_SHIELD_FAULT_RECORD             0x2403 //FT测试屏蔽故障录波
#define CMD_POST_DUTY_CYCLE_TEST            0x2404 //启动后级定占空比驱动测试
#define CMD_START_PRE_DRIVER_TEST           0x2410 //启动前级驱动测试
#define CMD_CLOSE_PRE_DRIVER_TEST           0x2411 //关闭前级驱动测试
#define CMD_START_POSTERIOR_DRIVER_TEST     0x2412 //启动后级驱动测试
#define CMD_CLOSE_POSTERIOR_DRIVER_TEST     0x2413 //关闭后级驱动测试
#define CMD_START_AUXI_SOURCE_TEST          0x2414 //启动交流辅助源测试
#define CMD_CLOSE_AUXI_SOURCE_TEST          0x2415 //关闭交流辅助源测试

#define CMD_START_INTERNAL_FAN_TEST         0x2416 //启动内部风扇控制测试
#define CMD_STOP_INTERNAL_FAN_TEST          0x2417 //关闭内部风扇控制测试

// #define CMD_TRIGGER_FAULT_RECORD            0x2418 //触发故障录波
// #define CMD_STOP_FAN_CTRL_TEST              0x2419 //关闭风扇控制测试
#define CMD_OPEN_FT_FAULT_RECORD              0x2419 //FT测试打开故障录波
#define CMD_START_RELAY_TEST                0x241A //启动并网继电器自检
#define CMD_STOP_RELAY_TEST                 0x241B //关闭并网继电器自检
#define CMD_START_EXTERNAL_FAN_TEST         0x241C //启动外部风扇测试
#define CMD_STOP_EXTERNAL_FAN_TEST          0x241D //关闭外部风扇测试
#define CMD_START_ISOLATION_POWER           0x2501 //启动隔离开关供电
#define CMD_STOP_ISOLATION_POWER            0x2502 //断开隔离开关供电
#define CMD_START_IMPEDANCE_TEST            0x2601 //启动绝缘阻抗检测
#define CMD_STOP_IMPEDANCE_TEST             0x2602 //停止绝缘阻抗检测
#define CMD_CLOSE_PID_CTRL                  0x2701 //闭合PID控制继电器
#define CMD_DISCONN_PID_CTRL                0x2702 //断开PID控制继电器
#define CMD_CLOSE_GROUND_RELAY              0x2801 //闭合接地异常检测继电器
#define CMD_DISCONN_GROUND_RELAY            0x2802 //断开接地异常检测继电器
#define CMD_CLOSE_THEFT_RELAY               0x2901 //闭合防盗继电器
#define CMD_DISCONN_THEFT_RELAY             0x2902 //断开防盗继电器

#define CMD_OPEN_LEAKAGE_CUR_SELF_CHECK     0x2A01 //FT主板开漏电流自检
#define CMD_CLOSE_LEAKAGE_CUR_SELF_CHECK    0x2A02 //FT主板关漏电流自检
#define CMD_TRIGGER_FAULT_RECORD            0x0001 //触发故障录波

#define CMD_NORMAL_MODE                     0x01   //正常模式
#define CMD_PRODUCT_MODE                    0x02   //生产测试模式
#define CMD_RD_DEBUG_MODE                   0x03   //研发调试模式
#define CMD_BOARD_RD_DEBUG_MODE             0x04   //单板研发调试模式



#define CMD_CLEAR_AFCI_FAIL_ALM             0x0004 //清除AFCI自检失败告警
#define CMD_CLEAR_DC_ARC_FALUT_ALM          0x0005 //清除直流电弧故障告警
#define CMD_CLEAR_INSULATION_LOW_ALM        0x0006 //清除绝缘阻抗低故障告警



#define CMD_STOP_SHIELD_CAN_COMM            0x0000
#define CMD_START_SHIELD_CAN_COMM           0x0001

#define SHELL_CMD_LEN                       200
#define SHELL_PARA_LEN                      32
typedef struct
{
    unsigned int sid;
    unsigned int map_value;
}dac_dig_map_t;

typedef struct {
    unsigned char  cmd_type;
    unsigned int   ctrl_id;
    unsigned short ctrl_status;
    unsigned char  ctrl_cmd_id;
    unsigned char  is_south_ctrl_cmd;
    char  info[20];
    void (*func)();
}ctrl_cmd_1363_t;

typedef struct
{
    float curr_accum_energy;        // 累计发电量
    float curr_year_energy;        // 当年发电量
    float curr_month_energy;       // 当月发电量
    float curr_day_energy;         // 当日发电量
}energy_data_t;

typedef struct{
    unsigned char sm_name[ARRAY_SIZE_10];
    unsigned short sm_soft_ver;
    unsigned char csu_fac_name[ARRAY_SIZE_20];
}csu_fac_data_t;


typedef struct{
    unsigned char cmd_type;
    unsigned short cmd_id;
    unsigned char south_cmd_id;
    int (*func)();
}cmd_id_map_t;

typedef struct{

    unsigned short cmd_id;
    cmd_id_map_t* para_map;

}cmd_func_map_t;

typedef struct
{
    char csu_file_name[VERSION_LEN];               // 监控升级文件名
    char csu_file_version[VERSION_LEN];               // 监控升级版本号
    char cpld_file_name[VERSION_LEN];               // CPLD升级文件名
    char cpld_file_version[VERSION_LEN];               // CPLD升级版本号
    char slave_file_name[VERSION_LEN];               // 辅控升级文件名
    char slave_file_version[VERSION_LEN];               // 辅控升级版本号
    char master_file_name[VERSION_LEN];               // 主控升级文件名
    char master_file_version[VERSION_LEN];               // 主控升级版本号
}csc_psc_info_t;


typedef struct
{
    char line[SHELL_CMD_LEN+1];
    int  line_len;
}shell_cmd_t;

typedef struct {
    unsigned char   cmd_type;    
    char*  cmd;     
}debug_cmd_t;

int comm_prase_set_para(void* dev_inst, void* cmd_buff);
int parse_com_group(void* dev_inst, void* cmd_buff);
int pack_sys_time_data(void* dev_inst, void* cmd_buff);
int parse_sys_time_data(void* dev_inst, void* cmd_buff);
int get_dig_data_parse(void* dev_inst, void *cmd_buf); 
int parse_test_record_cmd(void* dev_inst, void* cmd_buff);
dev_type_t* init_dev_inverter_1363_main(void);
dev_type_t* init_dev_inverter_1363_IP(void);
void s1363_register_cmd_table();
int parse_control_cmd(void* dev_inst, void* cmd_buff);
void get_all_s1363_data();
int parse_get_fault_record_para(void* dev_inst, void *cmd_buf);
int pack_get_fault_record_para(void* dev_inst, void *cmd_buf);
int parse_set_fault_record1_para(void* dev_inst, void *cmd_buf);
int parse_set_fault_record2_para(void* dev_inst, void *cmd_buf);
int parse_app_request_load(void* dev_inst, void *cmd_buf);
int pack_app_request_load(void* dev_inst, void* cmd_buf);
int parse_app_change_password(void* dev_inst, void* cmd_buf);
int pack_app_change_password(void* dev_inst, void* cmd_buf);
int parse_app_user_auth_time(void* dev_inst, void* cmd_buf);
int set_rs485_para();
int set_time_info();
int set_rs485_flag();
unsigned char* get_rs485_flag();
int send_update_alarm_para_msg(void* dev_inst, void *cmd_buf);
void send_alarm_change_msg();
void start_shield_can_comm();
void stop_shield_can_comm();
int pack_set_time(void* dev_inst, void* cmd_buf);
int pack_alm_data(void* dev_inst, void *cmd_buf);
void get_para_and_set_value(unsigned int para_id ,unsigned int data_id);
void judge_password_with_app(unsigned short para_offset_id, unsigned char*old_ciphertext, unsigned char* new_password, char is_set, unsigned char* fact_ciphert);
void app_chg_passwd_timeout();
void create_app_timer();
int write_app_event(unsigned short para_id_offset, char* info);
void send_clean_his_energy_msg();
void send_rs485_stat_msg();
int parse_csc_cmd(void* dev_inst, void* cmd_buff);
int parse_psc_cmd(void* dev_inst, void* cmd_buff);
int comm_prase_set_single_para(void* dev_inst, void* cmd_buff);
int pack_parallel_data(void* dev_inst, void* cmd_buff);
int comm_pack_get_para(void* dev_inst, void* cmd_buff);
// int pack_get_single_fault_record_para(void* dev_inst, void *cmd_buf);
int pack_1363_version_info(void* dev_inst, void* cmd_buff);
int is_password_correct(unsigned char *old_ciphertext, unsigned char *ciphertext, unsigned char *fact_ciphert);
int get_psc_final_update();
void format_file_system();
int pack_control_cmd(void* dev_inst, void* cmd_buf);
int parse_get_power_special_para(void* dev_inst, void *cmd_buff);
int parse_product_para(void* dev_inst, void *cmd_buff);
int parse_adjust_para(void* dev_inst, void *cmd_buff);
int parse_exe_debug_cmd(void* dev_inst, void* cmd_buff);
int remote_ctrl_para_parse(dev_inst_t* dev_inst, cmd_buf_t* cmd_buff, sid_list_info_t* sid_list_info);
void restart_system();
void start_iv_func();
int comm_pack_set_para(void* dev_inst, void* cmd_buff);
void handle_south_cmd_id(unsigned short south_cmd_id, sid_list_info_t* sid_list_info);
int set_app_init_load_flag(unsigned char flag);
int parse_test_para(void* dev_inst, void* cmd_buff);
cmd_id_map_t* find_set_para_map(unsigned short cmd_id);
cmd_t* find_matched_cmd(cmd_id_map_t* set_para_map, unsigned char cmd_type, int* south_cmd_id, unsigned int* set_para_map_index);
void get_afci_file();
void clean_afci_file();
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  // 
