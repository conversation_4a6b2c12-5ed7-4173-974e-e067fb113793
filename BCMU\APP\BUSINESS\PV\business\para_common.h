#ifndef _PARA_COMMON_H
#define _PARA_COMMON_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */
#include "rtdef.h"

#define SOUTH_PROTO_HEAD_LEN    3  // 南向协议的头的长度
#define PARA_SID_LIST_MAX 50    // 单次的参数不超过50

#define RESTART_POWER_VAL    0x30  // 复位功率控制器

#define RTN_FAILURE      0
#define RTN_SUCCEFUL     1

#define CTRL_RTN_SUCCEFUL     1

#pragma pack(1)
// 设置参数发消息的结构体
typedef struct{
    unsigned short cmd_id;
    unsigned char dev_no;
    unsigned int  sid_list_len;
    unsigned int* sid_list;
}single_para_msg_t;

typedef struct{
    unsigned char normal_off;
    unsigned char time_off;
    unsigned char emergy_off;
}power_off_reason_t;
#pragma pack()

int set_rs485_flag();
unsigned char* get_rs485_flag();
int send_set_para_cmd_msg(unsigned int* sid_list, unsigned int sid_num);
void backup_power_ctrl_para(int south_cmd_id);
void start_iv_callback();
void stop_iv_callback() ;
void reset_csu(void);
void deal_restore_factory(void);
unsigned char get_rtn_south_to_north_flag();
unsigned char set_rtn_south_to_north_flag(unsigned char flag);
int get_restart_south_rtn();
power_off_reason_t* get_power_off_reason();
rt_sem_t get_rtn_sem();
void init_rtn_sem();
int get_afci_file_handle();
void send_clean_his_data_msg();
void send_clean_his_event_msg();
void send_clean_iv_data_msg();
void send_clean_fault_record_msg();
void send_clean_alarm_msg();
void send_delete_his_energy_msg();

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  // 
