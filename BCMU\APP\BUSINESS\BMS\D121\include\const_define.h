/**************************************************************************
* Copyright (C) 2010-2011, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：ZXDU18 CTL板软件
* 文件名称：const_define.h
* 文件说明：常用常量定义
* 作    者：龙明星
* 版本信息：V1.0
* 设计日期：2023-08-05
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/
#ifndef SOFTWARE_SRC_APP_CONST_DEFINE_H_  
#define SOFTWARE_SRC_APP_CONST_DEFINE_H_  

#ifdef __cplusplus
extern "C" {
#endif


typedef enum
{
    CONTOL_BATT_INDICATION = 0, //电池指示
    CONTOL_CHAG_ON ,            //遥控充电回路闭合
    CONTOL_CHAG_OFF ,           //遥控充电回路断开
    CONTOL_DSCHAG_ON ,          //遥控放电回路闭合
    CONTOL_DSCHAG_OFF ,         //遥控放电回路断开
    CONTOL_CLEAR_CELL_DAMAGE,   //清除单体损坏
    CONTOL_SET_TIME,            //设定时间信息
    CONTOL_RST_BMS,             //BMS复位
    CONTOL_RST_PARA,            //恢复缺省参数
    CONTOL_ADDR_COMPETE,        //竞争地址
    CONTOL_RELEASE_LOCK,        //解除闭锁
    CONTOL_UPPER_COMPUTER_UNLOCK_ID,  //上位机防盗解锁
    CONTOL_SWITCH_ADDR,         //地址切换
    CONTOL_UPPER_CANCEL_DEVICE_DEFENCE,   //上位机人工撤防
    CONTOL_MANUAL_ENTER_DEFENCE, //人工进入布防
    CONTOL_RST_SYS,             //系统重启
    CONTOL_ADDR_CHANGE,         //地址改变
    CONTOL_BUTTON_SLEEP,        //按键休眠
    CONTOL_MASTER_SLAVE_CHANGE, //主从机切换
    CONTOL_SHUTDOWN,            //关机
    CONTOL_BOOT_UPDATE,         //boot升级
    CONTOL_BAUDRATE_SWITCH,     //波特率切换
    CONTOL_ENTER_QUIET_SLEEP,   //进入静置休眠状态
    CONTOL_QUIT_QUIET_SLEEP,    //退出静置休眠状态
    CONTOL_SET_LOCK,            //控制闭锁
    CONTOL_CONTACTOR_BREAK,     //电压二级保护接触器断开
    CONTOL_CONTACTOR_CLOSE,     //电压二级保护接触器闭合
    CONTOL_TWO_CLASS_LOCK,      //电压二级保护闭锁控制
    CONTOL_ENTER_APPTEST,       //进入apptest
    CONTOL_QUIT_APPTEST,        //退出apptest
    CONTOL_ENTER_QTP,           //进入qtp
    CONTOL_QUIT_QTP,            //退出qtp
    CONTOL_LOW_POWER_CONSUMPTION_SLEEP,  //低功耗休眠
    CONTOL_SAMPLE_IC_RESTART,   //采样IC重启
    CONTOL_NETWORK_UNLOCK_ANTITHEFT,   //网管防盗解锁
    CONTOL_DEFENCE_STATUS_CHANGE, //布防状态改变
    CONTOL_NETWORK_CANCEL_DEVICE_DEFENCE,   //网管人工撤防
    CONTOL_KEY_DEFQUERY,           //按键布防查询
    CONTOL_CLEAR_DCR_PRT,       //清除直流内阻保护
    CONTOL_SLAVE_UPDATE_SUCCESSFUL, //升级从机升级成功
}SpacialHistoryID;

/*******************   人工布防命令返回码   *****************/
#define RTN_SATISFY_DEFENCE         0    // 满足布防条件
#define RTN_SUCCESS_DEFENCE         0x80 // 成功进入布防
#define RTN_FAIL_ALREADY_DEFENCE    0x81 // 布防失败，已经是布防状态
#define RTN_FAIL_DEFENCE_LEVEL      0x82 // 布防失败，告警级别未设置好
#define RTN_FAIL_DEFENCE_PARA       0x83 // 布防失败，软件防盗延时为0
#define RTN_FAIL_DEFENCE_LEVEL_PARA 0x84 // 布防失败，软件防盗延时为0、告警级别未设置好
#define RTN_FAIL_DEFENCE_APPTEST    0x85 // 布防失败，当前为apptest模式
#define RTN_FAIL_DEFENCE_QTP        0x86 // 布防失败，当前为qtp模式

#define SMART_LI_RATE_CURRENT_CHG    (70.0f)  //电池侧最大充电电流
#define SMART_LI_RATE_CURRENT_CHG_BUS    (25.0f)  //Bus侧最大电流
#define SMART_LI_RATE_CURRENT_DISCHG (50.0f)
#define SMART_LI_RATE_TIME_CHG_SLOW (5)
#define SMART_LI_RATE_CURRENT_CHG_SLOW (5)
#define MIN_CURR_DET_BDCU (2.0) //电流检测误差修改为2A
#define DCR_TEST_MIN_CURRENT 7.0f   //DCR测试最小电池电流

#define CELL_TEMP_NUM       4     //单体温度数量
#define ALARM_CLASS         71    //告警数量
#define NUM_OF_MEASUREPOINTS         23    // 可变监测点数量
#define SMART_LI_MAX_DISCHARGE_CURRENT (70.0f)
#define SMART_LI_SELF_DISCHARGE_CURRENT (0.02f)
#define SELF_DISCHARGE_RATE_35C (0.745)
#define BATT_SOC_MAX (2.0f)
#define DISCHARGE_CURRENT_COEFFICIENT  (1.4)
#define SHORT_CUT_ALARM_SAVE_ID        (0x09)
#define BDCU_CHARGE_RATE_MAX ((FLOAT)40.0f)
#define BDCU_CHARGE_RATE_MIN ((FLOAT)2.0f)
#define BDCU_DISCHARGE_RATE_MAX ((FLOAT)66.0f)
#define BDCU_DISCHARGE_RATE_MIN ((FLOAT)2.0f)

#define BASE_ADDR 0x10
#define CYCLE_TIMES_MAX (3500) // 电芯最大循环次数

#define CHARGE_OVER_CURR 	  4500		// 初始化充电过流保护阈值，精度2
#define DISCHARGE_OVER_CURR   10000  	// 初始化放电过流保护阈值，精度2;
#define BATTERY_OVER_VOLT	  2922		// 初始化电池组过压保护阈值，精度2
#define BUS_DROP_VOLT	      4500		// 初始化母排跌落电压阈值，精度2
#define CHARGE_LIMIT_CURR	  1000		// 初始化设定充电限电流，千分比	
#define DISCHARGE_LIMIT_CURR  1320		// 初始化设定放电限电流，千分比	
#define CHARGE_VOLT 		  2800		// 初始化设定充电电压，精度2
#define DISCHARGE_VOLT 		  4500		// 初始化设定放电电压，精度2
#define CURR_SHARE			  0			// 初始化均流电流，精度2
#define CHARGE_BUS_VOLT		  3500		// 初始化BUS充电最低电压阀值，精度2

#define BATT_LOSE_ALM_INDEX   49        //电池丢失告警对应告警序号
#define DCR_FAULT_ALM_INDEX   65        //直流内阻异常告警对应告警序号
#define DCR_FAULT_PRT_INDEX   66        //直流内阻异常保护对应告警序号
#define BATT_UNDER_VOLT_PRT_INDEX   3  //电池组欠压保护告警序号
#define CELL_UNDER_VOLT_PRT_START_INDEX   52  //单体欠压保护告警开始序号
#define CELL_UNDER_VOLT_PRT_END_INDEX     67  //单体欠压保护告警结束序号
#define CELL_OVER_VOLT_PRT_START_INDEX    68  //单体过压保护告警开始序号
#define CELL_OVER_VOLT_PRT_END_INDEX      83  //单体过压保护告警结束序号
#define CELL_DYNAMIC_UNDER_VOLT_PRT_START_INDEX   273  //单体动态欠压保护告警开始序号
#define CELL_DYNAMIC_UNDER_VOLT_PRT_END_INDEX     288  //单体动态欠压保护告警结束序号
#define CHG_CURR_HIGH_PRT_INDEX        6   //充电过流保护告警序号
#define DISCHG_CURR_HIGH_PRT_INDEX     8   //放电过流保护告警序号

#define TOWER_CID1 0X57   // D121 TOWER协议 CID1

#define LED_NUM               2   // D121 指示灯总数(LED_RUN, LED_ALARM)

#define BACKUP_DEV_TYPE              1     // 互备份设备类型
#define BACKUP_HISACT_LEN            38    // 互备份操作记录自定义长度
#define BACKUP_DEVICE_MAX_NUM        13    // 互备份最大设备数量
#define BACKUP_HISDATA_MAX_NUM       1000  // 互备份历史数据最大条数
#define BACKUP_HISALARM_MAX_NUM      2000  // 互备份历史告警最大条数
#define BACKUP_HISACT_MAX_NUM        500   // 互备份历史操作记录最大条数
#define BACKUP_RECORD_MAX_NUM        100   // 互备份录波数据最大条数
#define BACKUP_TIME_VERIFY_INDEX     3     // 互备份记录保存时时间校验偏移


typedef enum {
    NORTH_PROTOCOL_PAD = 0,
    NORTH_PROTOCOL_TOWER,
    NORTH_PROTOCOL_NUM_MAX,
}record_north_protocol_e;

#define MAIN_NORTH_PROTOCOL NORTH_PROTOCOL_PAD //此版本主要使用的北向协议

#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // SOFTWARE_SRC_APP_CONST_DEFINE_H_  ;