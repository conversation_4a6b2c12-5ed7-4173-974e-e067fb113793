/**
 * @file     device_type.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2023-01-04
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */

 
#ifndef _SAMPLE_H
#define _SAMPLE_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include <stdio.h>
#include <rtthread.h>
#include <string.h>
#include "data_type.h"
#include "dev_bmu.h"
#include "utils_server.h"

#define DATA_INVALID        0
#define DATA_VALID          1

#define DEV_INVALID        0
#define DEV_VALID          1   



/* 电池模块模拟量数据 */
#pragma pack(1)

typedef struct {
    BMU_real_data_ana_t mod_comm_ana;       ///<  电池模块通讯采样模拟量数据
    unsigned char mod_soc;                  ///<  电池模块SOC
    unsigned char mod_soh;                  ///<  电池模块SOH
    unsigned short mod_cycle_num;           ///<  电池模块循环次数
    unsigned short mod_cal_life;            ///<  电池模块日历寿命衰减值
    unsigned short mod_cyc_life;            ///<  电池模块循环寿命衰减值
    unsigned short mod_add_life;            ///<  电池模块附加寿命衰减值
}batt_mod_ana_data_t;                 


/* 电池模块数字量数据 */
typedef struct {
    BMU_real_data_dig_t  mod_comm_dig;      ///<  电池模块通讯采样数据数字量数据
    // todo  继续补充计算值 
}batt_mod_dig_data_t;

/* 电池模块告警状态数据 */
typedef BMU_real_data_alm_t batt_mod_alm_sta_t; 
typedef BMU_data_fac_t batt_mod_fact_data_t; 


/* 电池模块所有数据 */
typedef struct {                            
    batt_mod_ana_data_t   ana_data;           ///<  电池模块模拟量数据
    batt_mod_dig_data_t   dig_data;           ///<  电池模块数字量数据
    batt_mod_alm_sta_t    alm_sta;            ///<  电池模块告警状态数据
    batt_mod_fact_data_t  fact_data;          ///<  电池模块厂家信息数据
}batt_mod_data_t;                           

// 绝缘电阻结构体
typedef struct {
    float pos_value;   // 正极绝缘电阻
    float neg_value;   // 负极绝缘电阻
}insulation_resistance_t;              

typedef struct {
    // DC_DC_real_data_ana_t cluster_comm_ana;         ///<  电池簇通讯采样模拟量数据 
    insulation_resistance_t insulation_resistance;  ///< 电池簇绝缘电阻
    float   cell_temp_max;
    float   cell_temp_min;
}batt_cluster_ana_data_t;                 

/* 电池簇数字量数据 */
// typedef struct {
//     DC_DC_real_data_dig_t cluster_comm_dig;  ///<  电池簇通讯采样数字量数据
//     // todo  继续补充计算值  
// }batt_cluster_dig_data_t;

/* 电池簇告警状态数据 */
// typedef DC_DC_real_data_alm_t batt_cluster_alm_sta_t; 

/* 电池簇厂家信息数据 */
// typedef DC_DC_fac_data_t batt_cluster_fact_data_t; 


/* 电池簇所有数据 */
// typedef struct {
//     batt_cluster_ana_data_t  ana_data;         ///<  电池簇模拟量数据
//     batt_cluster_dig_data_t  dig_data;         ///<  电池簇数字量数据
//     batt_cluster_alm_sta_t   alm_sta;          ///<  电池簇告警状态数据
//     batt_cluster_fact_data_t fact_data;        ///<  电池簇厂家信息
// }batt_cluster_data_t;

/* 电池柜数据 */
typedef struct {
    unsigned char busbar_overtemp;
    unsigned char time_sync_alm;               ///<  时间同步告警
}batt_cabinet_dig_t;

typedef struct {
    batt_cabinet_dig_t dig_data;
    // batt_cluster_data_t batt_cluster_data[BATT_CLUSTER_NUM];
}batt_cabinet_data_t;



#pragma pack()

typedef enum {
    DEVICE_BMU,                  ///< BMU
    DEVICE_BCMU,                 ///< BCMU
    DEVICE_ID_MAX                ///< 无效设备
}device_id_e;

/* 采样模块消息通知结构体 */
typedef struct {
    device_id_e dev_id;         ///< 设备ID
    unsigned char mod_no;       ///< BMU设备:1~BATT_MOD_NUM
}sample_change_msg_t;



void *init_sample(void * param);
void sample_main(void * param);
unsigned char get_batt_mod_ana_data(unsigned char mod_no, batt_mod_ana_data_t* ana_data);
// unsigned char get_batt_cluster_dig_data(unsigned char mod_no, batt_cluster_dig_data_t* dig_data); 
unsigned char get_batt_cabinet_dig_data(batt_cabinet_dig_t* dig_data);
unsigned char get_batt_cluster_ana_data(unsigned char mod_no, batt_cluster_ana_data_t* ana_data);

void set_time_sync_alm_state(unsigned char value);
unsigned char get_time_sync_alm_state(void);
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _SAMPLE_H
