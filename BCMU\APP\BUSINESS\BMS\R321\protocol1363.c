/**************************************************************************
* Copyright (C) 2001, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：ZXDU18 CTL板软件
* 文件名称：protocol1363.c
* 文件说明：协议模块
* 作    者：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/
#include "common.h"
#include "rtthread.h"
#include "sample.h"
#include "hisdata.h"
#include "realAlarm.h"
#include "battery.h"
#include "para.h"
#include "comm.h"
#include "protocol.h"
#include "protocol1363.h"
#include "CommCan.h"
#include "CommCan2.h"
#include "Candrive.h"
#include "stdio.h"
#include "flash.h"
#include "commBdu.h"
#include "wireless.h"
#include "usart.h"
#include "SearchHisdata.h"
#include "pdt_version.h"
#include "fileSys.h"
#include "realAlarm.h"
#include "led.h"
#include "const_define.h"
#include "ActivatePort.h"
#include "HeaterControl.h"
#include "utils_rtthread_security_func.h"
#include "netdev_ipaddr.h"
#include "netdev.h"
#include "BalanceTimeStatistics.h"
#include "peakshift.h"
#include "fm.h"
#include "prtclWireless.h"
#include "MultBattData.h"
#include "SysMonitor.h"
#include "snmp_comm_status.h"

void system_reset(void);  // TODO: interface没有给出头文件接口，临时这么处理（DIRTY CODE!）

// 2003-7-12 增加触发重复次数
#define     TRIGGER_COUNTER     3
#define     ALARM_CLASS_PAST    68      //为了兼容FB100B3，旧命令的告警长度为68个
#define     ALARM_NEW_NUM       (ALARM_CLASS - ALARM_CLASS_PAST)       //R321新增获取告警级别、获取告警干接点命令中新增的告警个数、自定义字节数
BYTE   s_ucOutTestTriggerRepeat = 0;   //0907 add mwl
WORD   s_wOutTestTriggerTimeout = 0;

#ifdef QUICK_RESPONSE_ENABLED
static BOOLEAN s_bShouldSetSysParaFlag = False;
#endif /* QUICK_RESPONSE_ENABLED */
static T_SysPara   s_tSysPara;
Static BYTE s_ucBlockNum = 0;          // 记录实际分成了多少块
static T_TimeStruct s_atBlockTime[13]; // 分块数组
static T_HisExtremeData s_tHisExtremeData;  //极值记录
Static rt_timer_t s_waitYD1363LedQuickNormalTimer = RT_NULL;
static rt_timer_t s_ResetTimer = RT_NULL;
Static T_TemplateInfo s_tTemplateStruct;
Static BYTE s_ucSetTemplateNum = 0;
Static BYTE s_ucGetTemplateNum = 0;
Static T_TemplateInfo s_tTemplateInfo;
Static T_PeakShiftPara	s_tPeakShiftPara;
#ifdef SHUTDOWNSLEEP
static BYTE s_ucShutdownTriggerRepeat = 0;
static BYTE s_ucStartSleepTriggerRepeat = 0;
#endif
/*********************  静态函数原型定义  **********************/
static void SendFactoryInfo( BYTE ucPort );

Static void SendBmsFactInfo(BYTE ucPort);
Static void SendBduFactInfo(BYTE ucPort);

static void SendBmsData(BYTE ucPort);
static void SendBmsStatus(BYTE ucPort);
static void SendBmsAlarm(BYTE ucPort);
static void SendBmsPara(BYTE ucPort);
static void RecBmsPara(BYTE ucPort);
static void SendBmsSpecialPara(BYTE ucPort);
static void RecBmsSpecialPara(BYTE ucPort);
static void SendBmsAlmLevel(BYTE ucPort);
static void RecBmsAlmLevel(BYTE ucPort);
static void SendBmsAlmRelay(BYTE ucPort);
static void RecBmsAlmRelay(BYTE ucPort);
static void SendBmsHisData( BYTE ucPort );
static void SendBmsHisData_new( BYTE ucPort );
static void SendBmsHisData_time( BYTE ucPort );
Static void SendBmsHisAlm( BYTE ucPort );
Static void SendBmsHisAlm_new( BYTE ucPort );
Static void SendBmsHisAlm_time( BYTE ucPort );
Static void SendBmsHisOperation( BYTE ucPort );
static void SendBmsHisOperation_new( BYTE ucPort );
Static void SendBmsHisOperation_time( BYTE ucPort );
static void SendBalanceCapacityRecord(BYTE ucPort);
static void SendRealBalanceCapacity(BYTE ucPort);
Static void SendBmsFactSpecInfo(BYTE ucPort);
static void RecBmsFactSpecInfo(BYTE ucPort);
static void SendProtocolVerion( BYTE ucPort );
static void GetCellFactInfo(BYTE ucPort);
static void SetCellFactInfo(BYTE ucPort);
static void GetBmsAnalogNew(BYTE ucPort);
static void GetBmsParaNew(BYTE ucPort);
static void SetBmsParaNew(BYTE ucPort);
static void SetBmsSpeParaNew(BYTE ucPort);
static BOOLEAN SendDcrRecord_time(BYTE ucPort);
static void GetCustomerName(BYTE ucPort);
//内部使用命令
static void RegetBmsHisAlm( BYTE ucPort );
static void RegetBmsHisData( BYTE ucPort );
static void RegetBmsHisOper( BYTE ucPort );
#ifdef SHUTDOWNSLEEP
static void CtrlShutdown(BYTE ucPort);
static void CtrlSleepStart(BYTE ucPort);
static void CtrlSleepStop(BYTE ucPort);
#endif
//static void ClearCalibPara(BYTE ucPort);
static void	RecControl( BYTE ucPort );
Static BOOLEAN  CheckCommandType( void );
static void GetSleepTest(BYTE ucPort);
//static void RecGprsCtrl(BYTE ucPort);
//static void GetGprsTest(BYTE ucPort);
// static  SHORT  FloatChangeToModbus(FLOAT fData);
static void SendBmsDebugInfo(BYTE ucPort);
Static void GetBmsAlarmNew(BYTE ucPort);
Static void GetBmsAlarmLVLNew(BYTE ucPort);
Static void SetBmsAlarmLVLNew(BYTE ucPort);
Static void GetBmsAlmRLYNew(BYTE ucPort);
Static void SetBmsAlmRLYNew(BYTE ucPort);
static void GetBmsFactInfoNew(BYTE ucPort);
static void GetBmsNewAlm(BYTE ucPort);
static void SetBmsNewAlm(BYTE ucPort);
Static void GetBmsMaxSoc(BYTE ucPort);
//static void TriggerOutAssistTest(BYTE ucPort);    //外协触发指令 0907 
//static void ExitOutAssistTest(BYTE ucPort);
static void ReadBootVerDate(BYTE *p);
static void	RecSwapBaudrate(BYTE ucPort);       // 波特率切换
static void RecHandShake( BYTE ucPort );
static void SendBmsStatusNew(BYTE ucPort);
Static BOOLEAN CheckHisCommandType(void);
static BYTE IsProtect(BYTE protectvalue, BYTE alarmvalue);
static VOID DealChargeCtrlCmd(BYTE bRunMode,BYTE bCtrlType,BYTE bCtrlStatus);
static VOID DealDischargeCtrlCmd(BYTE bRunMode,BYTE bCtrlType1,BYTE bCtrlType2,BYTE bCtrlStatus);
static void GetBmsSpecialParaNew(BYTE ucPort);
Static void SetBmsSpecialParaNew(BYTE ucPort);
Static void GetBmsExtremeValue(BYTE ucPort);
static BYTE FillBattExtremeValue(BYTE* p);
Static BYTE FillCellVoltMaxExtremeValue(BYTE* p, BYTE ucCellVoltNum);
static BYTE FillCellVoltMinExtremeValue(BYTE* p, BYTE ucCellVoltNum);
static BYTE FillCellTempMaxMinExtremeValue(BYTE* p, BYTE ucCellTempNum);
Static BOOLEAN RecControlSpecial(void);
Static void	RecCtrlNew( BYTE ucPort );
Static void DealBattManualUnlockCtrlCmd(void);
Static BOOLEAN PutSoftwareVersionIntoBuff(T_SoftwareCustomizedInfo *ptSoftwareCustomizedInfo, BYTE *buff);
Static void GetBmsStatisticsNew(BYTE ucPort);

static void GetNetPara(BYTE ucPort);                // 获取网络参数信息（R321）
Static void SetNetPara(BYTE ucPort);                // 设定网络参数信息（R321）
Static BYTE ManualCtrlEnterDefenceStatus(void);   //人工布防
static BOOLEAN GetDcrInfo(BYTE ucPort);          //获取直流内阻信息
//内部测试
Static void GetBmsParaInternal(BYTE ucPort);
Static void SetBmsParaInternal(BYTE ucPort);
static void GetBduAddressInfo(BYTE ucPort);   // 获取BDU内存地址信息（内部使用）
static void SetBduAddressInfo(BYTE ucPort);   // 设定BDU内存地址信息（内部使用）
Static void SendDateTempData(BYTE ucPort);  // 获取智能错峰日模板数据
Static void RecDateTempData(BYTE ucPort);  // 设置智能错峰日模板数据
Static void SendPeakShiftPara(BYTE ucPort);   //  获取智能错峰参数
Static void RecPeakShiftPara(BYTE ucPort);  //  设置智能错峰参数
Static BOOLEAN SetTempToProtocol(T_DateTemplateStruct* ptDateTemp, BYTE* p, BYTE ucNum);
Static BOOLEAN GetTempFromProtocol(T_DateTemplateStruct* ptDateTemp, BYTE* p, BYTE ucNum);

Static BOOLEAN ManualAntitheftUnlock(BYTE bType);
static BOOLEAN GetBalanceNum (WORD wAlarmNum,BYTE ucPort);  //解决圈复杂度
Static void GetFmPeakStatus(BYTE ucPort); //获取调频错峰实时数据
Static void EnterFMMode(void); //进入调频模式
Static void SetFmPowerFrom1363(FLOAT fFMPower); //设置调频功率
Static void ExitFMMode(void);  //退出调频模式
Static void GetAlarmR321(BYTE ucPort); //获取告警量(R321新增)
Static void GetRealData1363(BYTE ucPort); //获取实时数据(R321新增)
Static void GetAlarmLevelNew(BYTE uPort);
Static void SetAlarmLevelNew(BYTE uPort);
Static void GetAlarmRlyNew(BYTE uPort);
Static void SetAlarmRlyNew(BYTE uPort);
Static void PeakShiftTemplateForward(BYTE ucPort);  

#ifdef INTELLIGENT_PEAK_SHIFTING
static void RecvFmInfoResponse(BYTE ucPort);
#endif

Static void SiteAntiTheftCtrl(BYTE ucPort);
Static BYTE StartSiteAntiTheft(BYTE *pucSiteAntiTheftKey);
Static BYTE HeartSiteAntiTheft(BYTE *pucSiteAntiTheftKey);
Static BYTE EndSiteAntiTheft(BYTE *pucSiteAntiTheftKey);
Static BYTE ChangeKeySiteAntiTheft(BYTE *pucSiteAntiTheftKey, BYTE *pucSiteAntiTheftKeyNew);
Static BOOLEAN DealSwitchCAN2Address(void);
Static void NetAntiTheftCtrl(BYTE ucPort);
Static BYTE StartNetAntiTheft(BYTE *pucNetAntiTheftKey);
Static BYTE HeartNetAntiTheft(BYTE *pucNetAntiTheftKey, BYTE *pucNetAntiTheftSN);
Static BYTE EndNetAntiTheft(BYTE *pucNetAntiTheftKey);
Static void ChangeNetAntiTheftKey(BYTE ucPort);
Static void GetNetAntikey(BYTE ucPort);
Static void GetFactoryInfoEx(BYTE ucPort);
#ifdef ENABLE_SYS_MONITOR
static void GetElaborateMemoryManagement(BYTE ucPort);
#endif

Static void GetGpsInitInfo(BYTE uPort);
Static void SetGpsInitInfo(BYTE uPort);
/********************************命令码处理START**************************************/
const T_CmdFuncStruct s_atCID2All1363Table[] = 
{
    {GET_TIME, SendSysTime, 0},
    {SET_TIME, RecSysTime, 14},
    {GET_FACTORY_INFO, SendFactoryInfo, 0},
    {GET_PROTOCOL_VER, SendProtocolVerion, 0},
    //{GET_ADDR, SendDevAddr, 0},
    //{SET_ADDR_DEFINED, RecDeviceAddr, 0},
    {GET_BMS_FAC_INFO, SendBmsFactInfo, 0},
    {GET_BDU_FAC_INFO, SendBduFactInfo, 0},
    {GET_ANALOG_INT, SendBmsData, 2},
    {GET_SWITCH, SendBmsStatus, 2},
    {GET_ALARM, SendBmsAlarm, 2},
    {GET_PARA_INT, SendBmsPara, 2},
    {SET_PARA_INT, RecBmsPara, 8},
    {GET_SPECIAL_PARA, SendBmsSpecialPara, 2},
    {SET_SPECIAL_PARA, RecBmsSpecialPara, 0},
    {GET_BMS_HISDATA,SendBmsHisData,4},
    {GET_BMS_HISDATA_NEW,SendBmsHisData_new,6},
    {GET_BMS_HISDATA_TIME,SendBmsHisData_time,6},
    {GET_DCR_DATA_TIME,SendDcrRecord_time,6},
    {GET_BMS_HISALM,SendBmsHisAlm,4},
    {GET_BMS_HISALM_NEW,SendBmsHisAlm_new,6},
    {GET_BMS_HISALM_TIME,SendBmsHisAlm_time,6},
    {GET_BMS_ALM_LVL, SendBmsAlmLevel, 2},
    {SET_BMS_ALM_LVL, RecBmsAlmLevel, 8},
    {GET_BMS_ALM_RLY, SendBmsAlmRelay, 2},
    {SET_BMS_ALM_RLY, RecBmsAlmRelay, 8},
    {GET_BMS_HISOPERATION, SendBmsHisOperation, 4},
    {GET_BMS_HISOPERATION_NEW, SendBmsHisOperation_new, 6},
    {GET_BMS_HISOPERA_TIME, SendBmsHisOperation_time, HIS_RECORD_LEN_TYPE_ID},
    {GET_BMS_BALANCE_CAPACITY,SendBalanceCapacityRecord,6},
    {GET_BAL_CAPAYITY_INFO,SendRealBalanceCapacity,2},
    {REMOTE_CTRL, RecControl, 8},//190128兼容设置放电输出电压增加两个字节作为command_info
    {GET_BMS_FAC_SPEC_INFO, SendBmsFactSpecInfo, 2},
    {SET_BMS_FAC_SPEC_INFO, RecBmsFactSpecInfo, 0},
    {GET_BMS_DEBUG_INFO, SendBmsDebugInfo, 2},
    {GET_BMS_CELL_FACT, GetCellFactInfo, 2},//新增
    {SET_BMS_CELL_FACT, SetCellFactInfo, 0},
    {GET_BMS_ANALOG_NEW, GetBmsAnalogNew, 2},
    {GET_BMS_PARA_NEW, GetBmsParaNew, 2},
    {SET_BMS_PARA_NEW, SetBmsParaNew, 0},
    {SET_BMS_SPEPARA_NEW, SetBmsSpeParaNew, 0},
    
    {GET_BMS_ALM_NEW, GetBmsAlarmNew, 2}, //新增20200818
    {GET_BMS_ALM_LVL_NEW, GetBmsAlarmLVLNew, 2},
    {SET_BMS_ALM_LVL_NEW, SetBmsAlarmLVLNew, 8},
    {GET_BMS_ALM_RLY_NEW, GetBmsAlmRLYNew, 2},
    {SET_BMS_ALM_RLY_NEW, SetBmsAlmRLYNew, 8},
    {GET_BMS_FACT_INFO_NEW, GetBmsFactInfoNew, 0},
	{GET_BMS_NEW_ALM_PARA, GetBmsNewAlm, 2},
	{SET_BMS_NEW_ALM_PARA, SetBmsNewAlm, 8},
	{GET_BMS_MAX_SOC, GetBmsMaxSoc, 2},
    {REMOTE_SWAP_BAUD, RecSwapBaudrate, 8},
	{REMOTE_HANDSHAKE, RecHandShake, 8},
    {GET_SWITCH_NEW, SendBmsStatusNew, 2},
    {GET_BMS_SPECIAL_PARA_NEW, GetBmsSpecialParaNew, 2},
    {SET_BMS_SPECIAL_PARA_NEW, SetBmsSpecialParaNew, 6},
    {GET_BMS_EXTREME_VALUE, GetBmsExtremeValue, 4}, 

    {GET_BMS_STATISTICS_NEW, GetBmsStatisticsNew, 2},

    {GET_BMS_NET_NEW, GetNetPara, 2}, 
    {SET_BMS_NET_NEW, SetNetPara, 0},

    {GET_DATE_TEMPLATE, SendDateTempData, 4},
	{SET_DATE_TEMPLATE, RecDateTempData, REC_DATE_TEMP_DATA_MAXLEN}, // 最多243*2
	{GET_PEAK_SHIFT_PARA, SendPeakShiftPara, 2},
	{SET_PEAK_SHIFT_PARA, RecPeakShiftPara, 0},

    {GET_FM_PEAK_STATUS, GetFmPeakStatus,2},
    {GET_ALARM_R321, GetAlarmR321, 2},
    {SITE_ANTITHEFT_CTRL, SiteAntiTheftCtrl, 68},
    {NET_ANTITHEFT_CTRL, NetAntiTheftCtrl, 68},
    {NET_ANTITHEFTKEY_CHANGE, ChangeNetAntiTheftKey, 130},
    {GET_NET_ANTITHEFTKEY, GetNetAntikey, 2},
    {GET_REAL_DATA_1363, GetRealData1363, 2},
    //内部使用命令
    {REGET_BMS_HISALM,  RegetBmsHisAlm, 0},
    {REGET_BMS_HISDATA, RegetBmsHisData, 0},
    {REGET_BMS_HISOPER, RegetBmsHisOper, 0},
    {GET_BMS_INTERNAL_PARA, GetBmsParaInternal, 2},
    {SET_BMS_INTERNAL_PARA, SetBmsParaInternal, 0},
    {GET_ALARM_LEVEL_NEW, GetAlarmLevelNew, 2},
    {SET_ALARM_LEVEL_NEW, SetAlarmLevelNew,6},//发送3个字节,一个字节两个ASCII码
    {GET_ALARM_RLY_NEW, GetAlarmRlyNew, 2},
    {SET_ALARM_RLY_NEW, SetAlarmRlyNew, 6},

    {GET_GPS_INIT_INIO, GetGpsInitInfo, 2},
    {SET_GPS_INIT_INIO, SetGpsInitInfo, 2},

    #ifdef SHUTDOWNSLEEP
    {CTRL_BMS_SHUTDOWN, CtrlShutdown, 0},
    {CTRL_BDU_SLEEP_START, CtrlSleepStart, 0},
    {CTRL_BDU_SLEEP_STOP, CtrlSleepStop, 0},
    #endif
    //{CLEAR_ZERO_POINT_CALIB_PARA, ClearCalibPara, 2}, 

    {GET_SLEEP_TEST, GetSleepTest, 0},     //获取休眠状态
	{GET_ADDRESS_INFO, GetBduAddressInfo, 10},   // 获取BDU内存地址信息（内部使用）
    {SET_ADDRESS_INFO, SetBduAddressInfo, 18},   // 设定BDU内存地址信息（内部使用）
    //{OUTTEST_TRIGGER, TriggerOutAssistTest, 0},
    //{OUTTEST_EXIT, ExitOutAssistTest, 0},
    {GET_DCR_INFO, GetDcrInfo, 2},
    {GET_CUSTOMER_NAME, GetCustomerName,2},  //获取客户名称
    {REMOTE_CTRL_NEW, RecCtrlNew, 52},   //遥控（新指令）
    {GET_FACTORY_INFORMATION_EXTEND, GetFactoryInfoEx, 0},
    {NET_PEAKSHIFTTEMPLATE_FORWARD, PeakShiftTemplateForward,0}, 
#ifdef ENABLE_SYS_MONITOR
    {GET_ELABORATE_MEMORY_MANAGEMENT, GetElaborateMemoryManagement, 2},
#endif
#ifdef INTELLIGENT_PEAK_SHIFTING
    {RECV_FM_INFO_RESPONSE, RecvFmInfoResponse, 4},
#endif
    
    {0x00, 0x0000, 0x00},
};

/* 本文件对外获取参数接口 */
T_SysPara* GetSysParaPointer_1363( void )
{
    return &s_tSysPara;
}


#ifdef QUICK_RESPONSE_ENABLED

BOOLEAN ShouldSaveSysParaBy1363(void)
{
    return s_bShouldSetSysParaFlag;
}

void SetSaveSysParaBy1363Flag(BOOLEAN flag)
{
    s_bShouldSetSysParaFlag = !!flag;
}

BOOLEAN ExecuteSaveSysParaBy1363(void)
{
    return ExecuteSaveSysPara(&s_tSysPara, True, CHANGE_BY_YD1363);
}

static BOOLEAN TrySetSysParaBy1363(T_SysPara *ptSysPara, BOOLEAN bChk, T_ChangeByEnum ChangeBy)
{
    BOOLEAN bCheckResult = False;

    bCheckResult = TrySetSysPara(ptSysPara, bChk, ChangeBy);
    if (bCheckResult)
    {
        SetSaveSysParaBy1363Flag(True);
    }

    return bCheckResult;
}

#define SetSysPara(para, chk, changeby) TrySetSysParaBy1363(para, chk, changeby)

#endif /* QUICK_RESPONSE_ENABLED */


/****************************************************************************
* 函数名称：SendFactoryInfo()
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：发送设备厂家信息
* 作    者  ：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
static void SendFactoryInfo( BYTE ucPort )
{
    WORD wSendLen = 64;//(10+1+1+20)*2 = 64
    BYTE wMajorVer = MAJOR_VER;
    BYTE wMinorVer = MINOR_VER;
    BYTE  *p = NULL;
    T_BmsInfoStruct tBmsInfo = {0};

    readBMSInfofact(&tBmsInfo);

    p   = s_tProtocol.aucSendBuf;

    MemsetBuff(p, tBmsInfo.acNameLite, (BYTE)rt_strnlen_s(tBmsInfo.acNameLite, 10), 10, 0x20); // BMS系统名称 短称
    p += 10;

    *p++ = wMajorVer; //版本信息
    *p++ = wMinorVer;
    MemsetBuff(p, tBmsInfo.acBattCorpName, (BYTE)rt_strnlen_s(tBmsInfo.acBattCorpName, 20), 20, 0x20);   /* 厂家名称 */

    /* LENID */
    //s_tProtocol.wSendLenid  = (WORD) ((p-s_tProtocol.aucSendBuf)*2);
    s_tProtocol.wSendLenid = wSendLen;//为解决KW问题

    return;
}


Static void SendBmsFactInfo(BYTE ucPort)
{
    BYTE *p = NULL;
    WORD wSendLen = 88; // (20+20+2+2)*2 = 88
    T_SoftwareCustomizedInfo tSoftwareCustomizedInfo = {0, };

    p = s_tProtocol.aucSendBuf;

    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));

    GetSysPara(&s_tSysPara);
    readSoftwareCustomizedInfo(&tSoftwareCustomizedInfo);

    MemsetBuff(p, s_tSysPara.acBMSSysName, (BYTE)rt_strnlen_s((CHAR*)s_tSysPara.acBMSSysName, 20), (BYTE)20, 0x20); // 系统名称
    p += 20;

    PutSoftwareVersionIntoBuff(&tSoftwareCustomizedInfo, p);
    p += 24;

    *p = 0x00; // KW4

    /* LENID */
    // s_tProtocol.wSendLenid = (WORD)((p - s_tProtocol.aucSendBuf) * 2);
    s_tProtocol.wSendLenid = wSendLen; // 为解决KW问题

    return;
}



Static void SendBduFactInfo(BYTE ucPort)
{
    WORD wSendLen = 164; // (2+1+1+30+8+6+4+30)*2 = 164
    BYTE *p = NULL;
    BYTE *pTemp = NULL;
    BYTE i = 0;
    T_DCFactory tDcFactory = {0,};
    T_SoftwareCustomizedInfo tSoftwareCustomizedInfo = {0,};

    SHORT sTemp = 0;
    p = s_tProtocol.aucSendBuf;

    GetBduFact(&tDcFactory);
    readSoftwareCustomizedInfo(&tSoftwareCustomizedInfo);

    if(CheckArrAllZero(tSoftwareCustomizedInfo.CustomizedBduRealseDate, sizeof(tSoftwareCustomizedInfo.CustomizedBduRealseDate)))
    {
        sTemp = ((WORD)tDcFactory.acVerDate[0] << 8) + ((WORD)tDcFactory.acVerDate[1]);
        PutInt16ToBuff(p, sTemp); // BDU真实版本日期
        p += 2;
        *p++ = tDcFactory.acVerDate[2];
        *p++ = tDcFactory.acVerDate[3];
    }
    else
    {
        sTemp = ((WORD)tSoftwareCustomizedInfo.CustomizedBduRealseDate[0] << 8) + ((WORD)tSoftwareCustomizedInfo.CustomizedBduRealseDate[1]);
        PutInt16ToBuff(p, sTemp); // BDU客户定制个性化版本日期
        p += 2;
        *p++ = tSoftwareCustomizedInfo.CustomizedBduRealseDate[2];
        *p++ = tSoftwareCustomizedInfo.CustomizedBduRealseDate[3];
    }

    // 填充20还是00？
    // BDU名称,填充码0x00转为0x20 20210107 mwl
    pTemp = p;
    rt_memset(pTemp, 0x20, 30);
    for(i = 0; i < sizeof(tDcFactory.acSysName); i++)
    {
        if(tDcFactory.acSysName[i] == 0x00)
        {
            break;
        }
        pTemp[i] = tDcFactory.acSysName[i];
    }
    p += 30;

    MemsetBuff(p, tDcFactory.acPlatformVer, 8, 8, 0x20); // 数控平台版本
    p += 8;

    if(CheckArrAllZero(tSoftwareCustomizedInfo.CustomizedBduVersion, sizeof(tSoftwareCustomizedInfo.CustomizedBduVersion)))
    {
        MemsetBuff(p, tDcFactory.acSoftVer, 6, 6, 0x20); // BDU真实软件版本
    }
    else
    {
        MemsetBuff(p, tSoftwareCustomizedInfo.CustomizedBduVersion, 6, 6, 0x20); // BDU客户个性化软件版本
    }
    p += 6;

    PutInt32ToBuff(p, tDcFactory.ulSN); // BDU出厂序列号
    p += 4;

    // BDU资产信息,填充码0x00转为0x20 20210107 mwl
    pTemp = p;
    rt_memset(pTemp, 0x20, 30);
    for(i = 0; i < sizeof(tDcFactory.acAsset); i++)
    {
        if(tDcFactory.acAsset[i] == 0x00)
        {
            pTemp[i] = 0x20;
        }
        else
        {
            pTemp[i] = tDcFactory.acAsset[i];
        }
    }
    p += 30;

    *p = 0x00; // KW4
    s_tProtocol.wSendLenid = wSendLen; // 为解决KW问题

    return;
}


/****************************************************************************
* 函数名称：SendProtocolVerion()
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：发送设备厂家信息
* 作    者  ：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
static void SendProtocolVerion( BYTE ucPort )
{
    /* LENID */
    s_tProtocol.wSendLenid  = 0;
    
    return;
}

// static void SendDevAddr( BYTE ucPort )
// {
//     s_tProtocol.wSendLenid  = 0;
//     return;
// }

/****************************************************************************
* 函数名称：CheckCommandType()
* 调    用：无
* 被 调 用：在SendAcHisAlarm()等中调用
* 输入参数：无
* 返 回 值：TRUE或FALSE
* 功能描述：检查CommandType是否正确
* 作    者  ：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
Static BOOLEAN  CheckCommandType( void )
{
    /* COMMAND_TYPE */
    s_tProtocol.ucCommandType   = s_tProtocol.aucRecBuf[8];
    /* 如果COMMAND_TYPE不为0x00,0x01,0x02或0x03，表示命令格式错 */  
    if (s_tProtocol.ucCommandType == 0x00 || s_tProtocol.ucCommandType == 0x01
            || s_tProtocol.ucCommandType == 0x02)
    {
        return True;
    }
    s_tProtocol.ucRTN   = RTN_WRONG_COMMAND;
    return False;
}

static void SendBmsData(BYTE ucPort)
{
    WORD wSendLen = 162;//(1+1+2+2+1+2*15+2+2+2+1+4*2+2+2+2+1+2*11)*2 = 81*2 = 162
    BYTE *p = NULL;
    BYTE i = 0;
    SHORT sInvalid = 0x8000;
    SHORT sTemp;
    T_BCMDataStruct    tBCMAnaData;
    T_BattResult tBattout;
	T_HardwareParaStruct tHWPara;
    BYTE ucCellVoltNum = 0;
    BYTE ucCellTempNum = 0;
    T_DCCtrl tCtrState = {0};
    rt_memset(&tBCMAnaData, 0, sizeof(T_BCMDataStruct));
    rt_memset(&tBattout, 0, sizeof(T_BattResult));
    GetRealData(&tBCMAnaData);
    GetBattResult(&tBattout);
    GetBduCtrl(&tCtrState);
    readBmsHWPara(&tHWPara);
    ucCellVoltNum = tHWPara.ucCellVoltNum<CELL_VOL_NUM_MAX?tHWPara.ucCellVoltNum:CELL_VOL_NUM_MAX;
    ucCellTempNum = tHWPara.ucCellTempNum<CELL_TEMP_NUM_MAX?tHWPara.ucCellTempNum:CELL_TEMP_NUM_MAX;
    p  = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    /* DATA_FLAG */
    *p++   = GetDataFlag( BCM_ALARM, ucPort );
    *p++ = BATT_NUM_BCM;//铁锂电池组数M

    sTemp  = Host2Modbus((SHORT*)&tBCMAnaData.wBattSOC);    //电池SOC
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(tBCMAnaData.fBattVolt*100);    //电池电压
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    *p++  = ucCellVoltNum;//单体数量
    for (i = 0; i < ucCellVoltNum; i++)
    {     
        sTemp  = FloatChangeToModbus(tBCMAnaData.afCellVolt[i]*1000);    //单体电压 
        rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
        p += 2;
    }
    sTemp  = FloatChangeToModbus(tBCMAnaData.fEnvTemp*10);    //环境温度
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = Host2Modbus(&sInvalid);    //电池温度
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    // *(SHORT*)p  = Host2Modbus(&sInvalid);    //单板温度
    sTemp = FloatChangeToModbus(tBCMAnaData.fBoardTemp*10);
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    *p++  = ucCellTempNum;//单体温度数量
    for (i = 0; i < ucCellTempNum; i++)
    {
        sTemp  = FloatChangeToModbus(tBCMAnaData.afCellTemp[i]*10);    //单体温度
        rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
        p += 2;
    }
    sTemp  = FloatChangeToModbus(tBCMAnaData.fBattCurr*100);    //电池电流  
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = Host2Modbus(&sInvalid);    //电池内阻
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = Host2Modbus((SHORT*)&tBCMAnaData.wBattSOH);    //电池SOH
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    *p++ = 11; //用户自定义个数
    sTemp  = FloatChangeToModbus(tBCMAnaData.fBattChgReqVolt*100);    //电池充电请求电压 
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(tBCMAnaData.fBattChgReqCurr*100);    //电池充电请求电流  
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(tBCMAnaData.fExterVolt*100);    //外部电压
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = Host2Modbus((SHORT*)&tBCMAnaData.wBattCycleTimes);    //电池循环次数
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(tBattout.fSetChgVolt*100);    //当前设定充电电压
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(tBattout.fSetDischVolt*100);    //当前设定放电电压
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = Host2Modbus((SHORT*)&tBattout.wSetDischCurr);    //当前设定放电限电流
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = Host2Modbus((SHORT*)&tBattout.wSetChgCurr);    //当前设定充电限电流
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    //190128add
    sTemp  = FloatChangeToModbus((FLOAT)tCtrState.ucChange2Chg*100);    //转充电使能

    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(tBCMAnaData.fDischObjVolt*100);    //当前放电目标电压值
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(tBCMAnaData.fBusCurr*100);    //BUS电流
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
	*p = 0x00;    //KW4
    /* LENID */
    //s_tProtocol.wSendLenid    = (WORD) ((p-s_tProtocol.aucSendBuf)*2);
    s_tProtocol.wSendLenid = wSendLen;//为解决KW问题
    return;
}

static BOOLEAN GetDcrInfo(BYTE ucPort)
{
    BYTE *p = NULL;
    BYTE i = 0;
    SHORT sTemp;
    T_BattResult tBattout;
	T_HardwareParaStruct tHWPara;
    BYTE ucCellVoltNum = 0;
    rt_memset_s(&tBattout, sizeof(T_BattResult), 0, sizeof(T_BattResult));
    rt_memset_s(&tHWPara, sizeof(T_HardwareParaStruct), 0, sizeof(T_HardwareParaStruct));
    GetBattResult(&tBattout);
    readBmsHWPara(&tHWPara);
    if(tHWPara.ucCellVoltNum > CELL_VOL_NUM)
    {
        return FALSE;
    }

    ucCellVoltNum = tHWPara.ucCellVoltNum;

    p  = s_tProtocol.aucSendBuf;
    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));
    /* DATA_FLAG */
    *p++ = GetDataFlag( BCM_ALARM, ucPort );
    *p++ = BATT_NUM_BCM;//铁锂电池组数M
    *p++ = ucCellVoltNum;//单体数量
    for (i = 0; i < ucCellVoltNum; i++)
    {
        sTemp  = FloatChangeToModbus(tBattout.fCellDcr[i]*100);    //单体DCR
        rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
        p += 2;
    }
    /* LENID */
    s_tProtocol.wSendLenid = GetLength((p-s_tProtocol.aucSendBuf)*2);

    return TRUE;
}

static void SendBmsStatus(BYTE ucPort)
{
    BYTE *p = NULL;
    T_BCMDataStruct tBCMData;
    T_BattResult tBattout;

    rt_memset(&tBCMData, 0, sizeof(T_BCMDataStruct));  
    GetRealData( &tBCMData);
    rt_memset(&tBattout, 0, sizeof(T_BattResult));
    GetBattResult(&tBattout);
    
    p   = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    /* DATA_FLAG */
    ClearDataFlag( BCM_ALARM, 0xEF, ucPort );    /* 开关量状态变化标志清0 */ 
    *p++ = GetDataFlag( BCM_ALARM, ucPort );
    *p++ = BATT_NUM_BCM;//铁锂电池组数M

    if(tBCMData.ucBattPackSta == CHARGE)
    {
        *p++ = 0;
    }
    else if(tBCMData.ucBattPackSta == DISCHARGE)
    {
        *p++ = 2;
    }
    else
    {
        *p++ = 1;
    }
    *p++ = 0x0C; //增加布防状态                      /* 监测的开关数量 */
    *p++ = tBCMData.ucBattChargeEn; ////充电禁止状态
    *p++ = tBCMData.ucBattDischEn; ////放电禁止状态
    *p++ = tBCMData.ucChgProtSta;//充电保护状态
    *p++ = tBCMData.ucDischgProtSta;//放电保护状态
    //*p++ = tBCMData.ucDischgLimitSta;//放电限流状态
    //*p++ = tBCMData.ucSupplyChargeSta; //补电状态
    //*p++ = tBCMData.ucFullChargeSta; //充满状态
    *p++ = tBCMData.ucCellEquSta; //均衡状态
    *p++ = tBCMData.ucBattPackSta; //电池组状态
    *p++ = tBCMData.ucBduStatus; //BMS充放电状态
    *p++ = (tBattout.ucMasterStatus + 1)%2; //主从机状态
    *p++ = tBCMData.aucOutputRly[0]; //输出干接点状-1#
    *p++ = tBCMData.aucOutputRly[1]; //输出干接点状-2#
    *p++ = tBCMData.ucLimit;//限流状态
    *p++ = tBCMData.ucBduSleep;//休眠状态
	//*p++ = tBCMData.ucDefenceStatus;    //KW4
    /* LENID */
    //s_tProtocol.wSendLenid  = (WORD) ((p-s_tProtocol.aucSendBuf)*2);
    s_tProtocol.wSendLenid = GetLength((p-s_tProtocol.aucSendBuf)*2);//为解决KW问题

    return;
}
//告警重构：降低函数圈复杂度，暂时使用为了代码入库，后续有更好的方案再优化
//判断是保护还是告警
static BYTE IsProtect(BYTE protectvalue, BYTE alarmvalue)
{
    BYTE res = 0;
    if(alarmvalue)
    {
        res = 1;
    }
    else if(protectvalue)
    {
        res = 2;
    }
    else
    {
        res = 0;
    }  
    return res;
}

static WORD GetAlarm2Buff(BYTE* pcBuff, BYTE ucPort)
{
    BYTE *p = NULL;
    BYTE i;
    T_BCMAlarmStruct    tBCMAlm;
    T_HardwareParaStruct tHWPara;
    BYTE ucCellVoltNum = 0;
    BYTE ucCellTempNum = 0;
    rt_memset(&tBCMAlm, 0, sizeof(T_BCMAlarmStruct));
    GetRealAlarm(BCM_ALARM, (BYTE *)&tBCMAlm);
    rt_memset(&tHWPara, 0, sizeof(T_HardwareParaStruct));
    readBmsHWPara(&tHWPara);
    ucCellVoltNum = tHWPara.ucCellVoltNum<CELL_VOL_NUM_MAX?tHWPara.ucCellVoltNum:CELL_VOL_NUM_MAX;
    ucCellTempNum = tHWPara.ucCellTempNum<CELL_TEMP_NUM_MAX?tHWPara.ucCellTempNum:CELL_TEMP_NUM_MAX;
    
    p = pcBuff;
    *p++ = GetDataFlag( BCM_ALARM, ucPort );
    *p++ = BATT_NUM_BCM;//铁锂电池组数M

    *p++ = tBCMAlm.ucBattOverVoltAlm;          //电池组过压告警
    *p++ = tBCMAlm.ucChgCurrHighAlm;           //充电过流保护告警
    *p++ = tBCMAlm.ucBattUnderVoltAlm;         //电池组欠压告警 
    *p++ = tBCMAlm.ucDischgCurrHighAlm;        //放电过流告警
    *p++ = tBCMAlm.ucBattReverse;              //电池组极性反接告警
    *p++ = tBCMAlm.ucEnvTempHighAlm;           //环境温度高告警
    *p++ = tBCMAlm.ucEnvTempLowAlm;            //环境温度低告警
    *p++ = 0;                                  //电池组高温告警
    *p++ = 0;                                  //PCB高温告警
    *p++ = tBCMAlm.ucBattSOCLowAlm;            //电池组容量低告警
    *p++ = 0;                                  //电池组温度传感器失效告警
    *p++ = 0;                                  //电池组电压传感器失效告警
    *p++ = 0;                                  //电池组电流传感器失效告警
    *p++ = 0;                                  //电池组失效告警
    *p++ = ucCellVoltNum;//CELL_VOL_NUM;  //单体数量
    /*********单体过压告警，数量1~16可变*********/
    for (i = 0; i < ucCellVoltNum; i++)
    {
        *p++ = tBCMAlm.aucCellOverVoltAlm[i];
    }
    /*********单体欠压告警，数量1~16可变*********/
    for (i = 0; i < ucCellVoltNum; i++)
    {
        *p++ = tBCMAlm.aucCellUnderVoltAlm[i];
        //*p++ = IsProtect(tBCMAlm.aucCellUnderVoltPrt[i], tBCMAlm.aucCellUnderVoltAlm[i]);
        // if(tBCMAlm.aucCellUnderVoltPrt[i])
        // {
        //     *p++ = 2;   //02为保护
        // }
        // else
        // {
        //     *p++ = tBCMAlm.aucCellUnderVoltAlm[i];
        // }
    }
    /*********单体失效告警，数量1~16可变*********/
    for (i = 0; i < ucCellVoltNum; i++)
    {
        *p++ = 0;
    }
    *p++ = ucCellTempNum;//4;     //单体温度数量
    /*********单体充电高温告警，数量1~16可变*********/
    for (i = 0; i < ucCellTempNum; i++)
    {
        *p++ = tBCMAlm.aucChgTempHighAlm[i];
    }
    /*********单体温度传感器失效告警，数量1~16可变*********/
    for (i = 0; i < ucCellTempNum; i++)
    {
        *p++ = tBCMAlm.ucCellTempSensorInvalidAlm[i];
    }

    /*自定义字节,这里的统计有问题，单体15，单体温度为4时，统计的字节也是70，不是64
      而且这个自定义字节没有起作用，无论为多少，不影响与上位机之间的通信，后期上位机
      修改需要改成下面屏蔽的代码，实现正常统计*/
    // *p++ = 64;   
    //CID2中44H和94H基本一样，但94H为新指令，比44H多了41个字节，所以两个自定义字节的数量不同需要区分
    if(s_tProtocol.ucCID2 == GET_ALARM)
    {
        *p++ = 2*ucCellVoltNum+4*ucCellTempNum+24;
    }
    else
    {
        *p++ = 2*ucCellVoltNum+4*ucCellTempNum+65;
    }

    *p++ = tBCMAlm.ucBattOverVoltPrt;     //电池组过压保护
    *p++ = tBCMAlm.ucChgCurrHighPrt;      //充电过流保护
    *p++ = tBCMAlm.ucBattUnderVoltPrt;    //电池组欠压保护
    *p++ = tBCMAlm.ucDischgCurrHighPrt;   //放电过流保护

    /********单体一致性差告警，00正常，01告警，02保护*********/
    *p++ = IsProtect(tBCMAlm.ucCellPoorConsisPrt, tBCMAlm.ucCellPoorConsisAlm);
    // if (tBCMAlm.ucCellPoorConsisAlm)
    // {
    //     *p++ = 1;
    // }
    // else if (tBCMAlm.ucCellPoorConsisPrt)
    // {
    //     *p++ = 2;
    // }
    // else
    // {
    //     *p++ = 0; 
    // }
    *p++ = tBCMAlm.ucBoardTempHighPrt;   //单板温度高保护
    *p++ = tBCMAlm.ucBattSOCLowPrt;      //电池SOC低保护
    /********电池SOH告警，00正常，01告警，02保护*********/
    *p++ = IsProtect(tBCMAlm.ucBattSOHPrt, tBCMAlm.ucBattSOHAlm);
    // if (tBCMAlm.ucBattSOHAlm)
    // {
    //     *p++ = 1;
    // }
    // else if (tBCMAlm.ucBattSOHPrt)
    // {
    //     *p++ = 2;
    // }
    // else
    // {
    //     *p++ = 0;
    // }
    *p++ = tBCMAlm.ucBattLoseAlm;            //电池丢失告警
    *p++ = tBCMAlm.ucBattShortCut;           //电池短路
    *p++ = tBCMAlm.ucCellVoltSampleFault;    //单体电压采样异常
    *p++ = tBCMAlm.ucChgLoopInvalid;         //充电回路失效
    *p++ = tBCMAlm.ucDischgLoopInvalid;      //放电回路失效
    *p++ = tBCMAlm.ucCurrLimLoopInvalid;     //限流回路失效
    *p++ = ucCellVoltNum;//CELL_VOL_NUM;     //单体数量
    /****单体电压保护，00正常，01欠压，02过压*****/
    for (i = 0; i < ucCellVoltNum; i++)
    {
        *p++ = IsProtect(tBCMAlm.aucCellOverVoltPrt[i], tBCMAlm.aucCellUnderVoltPrt[i]);
        // if (tBCMAlm.aucCellUnderVoltPrt[i])
        // {
        //     *p++ = 1;
        // }
        // else if (tBCMAlm.aucCellOverVoltPrt[i])
        // {
        //     *p++ = 2;
        // }
        // else
        // {
        //     *p++ = 0;
        // }
    }
    /****单体损坏保护，00正常，01告警*****/
    for (i = 0; i < ucCellVoltNum; i++)
    {
        *p++ = tBCMAlm.aucCellDamagePrt[i];
    }
    *p++ = ucCellTempNum;//4; //单体温度数量
    /****单体充电低温告警，数量1~16可变*****/
    for (i = 0; i < ucCellTempNum; i++)
    {
        *p++ = tBCMAlm.aucChgTempLowAlm[i];
    }
    /****单体充电温度保护，00正常，01低温保护，02高温保护，数量1~16可变*****/
    for (i = 0; i < ucCellTempNum; i++)
    {
        *p++ = IsProtect(tBCMAlm.aucChgTempHighPrt[i], tBCMAlm.aucChgTempLowPrt[i]);
        // if (tBCMAlm.aucChgTempLowPrt[i])
        // {
        //     *p++ = 1;
        // }
        // else if (tBCMAlm.aucChgTempHighPrt[i])
        // {
        //     *p++ = 2;
        // }
        // else
        // {
        //     *p++ = 0;
        // }
    }
    /****单体放电温度告警，00正常，01低温，02高温，数量1~16可变*****/
    for (i = 0; i < ucCellTempNum; i++)
    {
        *p++ = IsProtect(tBCMAlm.aucDischgTempHighAlm[i], tBCMAlm.aucDischgTempLowAlm[i]);
        // if (tBCMAlm.aucDischgTempLowAlm[i])
        // {
        //     *p++ = 1;
        // }
        // else if (tBCMAlm.aucDischgTempHighAlm[i])
        // {
        //     *p++ = 2;
        // }
        // else
        // {
        //     *p++ = 0;
        // }
    }
    /****单体放电温度保护，00正常，01低温，02高温，数量1~16可变*****/
    for (i = 0; i < ucCellTempNum; i++)
    {
        *p++ = IsProtect(tBCMAlm.aucDischgTempHighPrt[i], tBCMAlm.aucDischgTempLowPrt[i]);
        // if (tBCMAlm.aucDischgTempLowPrt[i])
        // {
        //     *p++ = 1;
        // }
        // else if (tBCMAlm.aucDischgTempHighPrt[i])
        // {
        //     *p++ = 2;
        // }
        // else
        // {
        //     *p++ = 0;
        // }
    }
    *p++ = tBCMAlm.ucInsideTempHighPrt;       //机内过温保护
    *p++ = tBCMAlm.ucBDUBattVoltLowPrt;       //BDU电池欠压保护
    *p++ = tBCMAlm.ucCellTempAbnormal;  //单体温度异常
    *p++ = tBCMAlm.ucAddressClash;   //地址冲突
    *p++ = tBCMAlm.ucShakeAlarm;//190128add    //振动告警
    *p++ = tBCMAlm.ucBduEepromAlm;         //BDUU EEPROM故障
    /****BDU母排保护，00正常，01欠压，02过压，数量1~16可变*****/
    *p++ = IsProtect(tBCMAlm.ucBDUBusVoltHighPrt, tBCMAlm.ucBDUBusVoltLowPrt);
    // if (tBCMAlm.ucBDUBusVoltLowPrt)
    // {
    //     *p++ = 1;
    // }
    // else if (tBCMAlm.ucBDUBusVoltHighPrt)
    // {
    //     *p++ = 2;
    // }
    // else
    // {
    //     *p++ = 0;
    // }
    *p++ = tBCMAlm.ucBDUCommFail;   //BDU通讯断
    *p++ = tBCMAlm.ucWaterIngrAlm;  //防水告警

    return GetLength(p-s_tProtocol.aucSendBuf);

}

static void SendBmsAlarm(BYTE ucPort)
{
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    /* DATA_FLAG */
    ClearDataFlag( BCM_ALARM, 0xEF, ucPort );    /* 开关量状态变化标志清0 */ 

    s_tProtocol.wSendLenid = GetAlarm2Buff(s_tProtocol.aucSendBuf, ucPort)*2;
        
    return;
}

static void SendBmsPara(BYTE ucPort)
{
    BYTE *p = NULL;
    SHORT sInvalid = 0x8000;
//    T_SysPara s_tSysPara;
    SHORT sTemp;

    rt_memset_s(&s_tSysPara, sizeof(T_SysPara), 0, sizeof(T_SysPara));    
    GetSysPara(&s_tSysPara);

    p   = s_tProtocol.aucSendBuf;
    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));
    *p++ = GetDataFlag( BCM_ALARM, ucPort );
    *p++ = BATT_NUM_BCM;//铁锂电池组数M

    sTemp  = FloatChangeToModbus(s_tSysPara.fCellOverVoltAlmThre*1000);   
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2; 
    sTemp  = FloatChangeToModbus(s_tSysPara.fCellUnderVoltAlmThre*1000);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));         
    p += 2;
    sInvalid = 65;
    sTemp  = Host2Modbus(&sInvalid); //温度上限
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));          
    p += 2;
    sInvalid = -20;
    sTemp  = Host2Modbus(&sInvalid); //温度下限
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));          
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fChargeMaxCurr*100);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));          
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fBattOverVoltAlmThre*100);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));        
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fBattUnderVoltAlmThre*100);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));          
    p += 2;
    *p++ = 55;//190128新增常规锂电等参数，删除放电输出电压，删除多余的电芯损坏保护阈值
    sTemp  = FloatChangeToModbus(s_tSysPara.fCellOverVoltPrtThre*1000);  
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));          
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fCellUnderVoltPrtThre*1000);  
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));          
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fBattOverVoltPrtThre*100); 
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2; 
    sTemp  = FloatChangeToModbus(s_tSysPara.fBattUnderVoltPrtThre*100); 
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2; 
    sTemp  = FloatChangeToModbus(s_tSysPara.fChgTempHighAlmThre*10);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));         
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fChgTempLowAlmThre*10); 
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fChgTempHighPrtThre*10);  
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fChgTempLowPrtThre*10); 
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fDischgTempHighAlmThre*10); 
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fDischgTempLowAlmThre*10);  
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fDischgTempHighPrtThre*10);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fDischgTempLowPrtThre*10); 
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fChgCurrHighAlmThre*100); 
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fChgCurrHighPrtThre*100);  
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fDischgCurrHighAlmThre*100);  
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fDischgCurrHighPrtThre*100);  
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fBoardTempHighPrtThre*10); 
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fBoardTempHighcThre*10); 
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fCellPoorConsisAlmThre*100); 
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fCellPoorConsisPrtThre*100);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = Host2Modbus((SHORT*)&s_tSysPara.wBattSOCLowAlmThre);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = Host2Modbus((SHORT*)&s_tSysPara.wBattSOCLowPrtThre); 
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = Host2Modbus((SHORT*)&s_tSysPara.wBattSOHAlmThre);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = Host2Modbus((SHORT*)&s_tSysPara.wBattSOHPrtThre); 
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fCellDamagePrtThre*100); 
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fEnvTempHighAlmThre); 
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fEnvTempLowAlmThre); 
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fBattChgFullAverVoltThre*100);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fBattChgFullAverCurrThre*100);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = Host2Modbus((SHORT*)&s_tSysPara.wChgMaxDura);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = Host2Modbus((SHORT*)&s_tSysPara.wChgEndDura);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fBattSupplVolt*100);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = Host2Modbus((SHORT*)&s_tSysPara.wBattSupplPeriod); 
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fCellEquVoltDiffThre*100);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    //*(SHORT*)p  = FloatChangeToModbus(52.5*100);
    //p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fPowerOffVoltThre*100);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fPowerOnVoltThre*100);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = Host2Modbus((SHORT*)&s_tSysPara.wHisDataInter); 
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp = s_tSysPara.bSleepEn;//R321删除软关机使能参数，实际没有使用到
    sTemp  = Host2Modbus((SHORT*)&sTemp);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp = s_tSysPara.bBuzzerEnable;
    sTemp  = Host2Modbus((SHORT*)&sTemp);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = Host2Modbus((SHORT*)&s_tSysPara.wDischgSwitchSOC);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    //常规锂电充电限电流，实际没有使用到
    sTemp  = FloatChangeToModbus(s_tSysPara.fCommonLiLimitCurr*100);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = Host2Modbus((SHORT*)&s_tSysPara.wBatteryCap);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp = s_tSysPara.ucRunMode;
    sTemp  = Host2Modbus((SHORT*)&sTemp);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp = s_tSysPara.ucUsageScen;
    sTemp  = Host2Modbus((SHORT*)&sTemp);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp = s_tSysPara.ucCycleMode;
    sTemp  = Host2Modbus((SHORT*)&sTemp);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fLABattSwitchVolt*100);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fRemoteSupplyOutVolt*100);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp = s_tSysPara.ucLiAcidDischargeRate;
    sTemp  = Host2Modbus((SHORT*)&sTemp);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp = s_tSysPara.ucDODPerDischarge;
    sTemp  = Host2Modbus((SHORT*)&sTemp);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = Host2Modbus((SHORT*)&s_tSysPara.wLABattDurationPerDischarge);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp = s_tSysPara.bBoostCharge;
    sTemp  = Host2Modbus((SHORT*)&sTemp);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fDischgMaxCurr*100);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fPowerDownVoltThre*100);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fOutputOffsetVoltThre*100);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp = s_tSysPara.ucCellUVPDelay;
    sTemp  = Host2Modbus((SHORT*)&sTemp);
    rt_memcpy_s(p, sizeof(sTemp), (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;

    /* LENID */
    s_tProtocol.wSendLenid  = GetLength((p-s_tProtocol.aucSendBuf)*2);
    return;
}

static BYTE RecBmsPara1(FLOAT fPara) {
    switch (s_tProtocol.ucCommandType){
        case 0x80:
            s_tSysPara.fCellOverVoltAlmThre = fPara/1000.0;
            break;
        case 0x81:
            s_tSysPara.fCellUnderVoltAlmThre = fPara/1000.0;
            break;
        case 0x82:
        case 0x83:
            s_tProtocol.ucRTN   = RTN_INVALID_DATA;
            return True;
        case 0x84:
            s_tSysPara.fChargeMaxCurr = fPara/100.0;
            break;
        case 0x85:
            s_tSysPara.fBattOverVoltAlmThre = fPara/100.0;             
            break;
        case 0x86:
            s_tSysPara.fBattUnderVoltAlmThre = fPara/100.0;
            break;
        case 0x87:
            s_tSysPara.fCellOverVoltPrtThre = fPara/1000.0;
            break;
        case 0x88:
            s_tSysPara.fCellUnderVoltPrtThre = fPara/1000.0;
            break;
        case 0x89:
            s_tSysPara.fBattOverVoltPrtThre = fPara/100.0;
            break;
        case 0x8A:
            s_tSysPara.fBattUnderVoltPrtThre = fPara/100.0;
            break;
        case 0x8B:
            s_tSysPara.fChgTempHighAlmThre = fPara/10.0;
            break;
        case 0x8C:
            s_tSysPara.fChgTempLowAlmThre = fPara/10.0;
            break;
        case 0x8D:
            s_tSysPara.fChgTempHighPrtThre = fPara/10.0;
            break;
        case 0x8E:
            s_tSysPara.fChgTempLowPrtThre = fPara/10.0;
            break;
        case 0x8F:
            s_tSysPara.fDischgTempHighAlmThre = fPara/10.0;
            break;
        default:
        return False;
    }
    return True;
}

static BYTE RecBmsPara2(FLOAT fPara) {
    switch (s_tProtocol.ucCommandType){
        case 0x90:
            s_tSysPara.fDischgTempLowAlmThre = fPara/10.0;
            break;
        case 0x91:
            s_tSysPara.fDischgTempHighPrtThre = fPara/10.0;
            break;
        case 0x92:
            s_tSysPara.fDischgTempLowPrtThre = fPara/10.0;
            break;
        case 0x93:
            s_tSysPara.fChgCurrHighAlmThre = fPara/100.0;
            break;
        case 0x94:
            s_tSysPara.fChgCurrHighPrtThre = fPara/100.0;
            break;
        case 0x95:
            s_tSysPara.fDischgCurrHighAlmThre = fPara/100.0;
            break;
        case 0x96:
            s_tSysPara.fDischgCurrHighPrtThre = fPara/100.0;
            break;
        case 0x97:
            s_tSysPara.fBoardTempHighPrtThre = fPara/10.0;
            break;
        case 0x98:
            s_tSysPara.fBoardTempHighcThre = fPara/10.0;
            break;
        case 0x99:
            s_tSysPara.fCellPoorConsisAlmThre = fPara/100.0;
            break;
        case 0x9A:
            s_tSysPara.fCellPoorConsisPrtThre = fPara/100.0;
            break;
        case 0x9B:
            s_tSysPara.wBattSOCLowAlmThre = (WORD)fPara;
            break;
        case 0x9C:
            s_tSysPara.wBattSOCLowPrtThre = (WORD)fPara;
            break;
        case 0x9D:
            s_tSysPara.wBattSOHAlmThre = (WORD)fPara;
            break;
        case 0x9E:
            s_tSysPara.wBattSOHPrtThre = (WORD)fPara;
            break;
        case 0x9F:
            s_tSysPara.fCellDamagePrtThre = fPara/100.0;
            break;
        default:
        return False;
    }
    return True;
}

static BYTE RecBmsPara3(FLOAT fPara) {
    switch (s_tProtocol.ucCommandType){
        case 0xA0:
            s_tSysPara.fEnvTempHighAlmThre = fPara;
            break;
        case 0xA1:
            s_tSysPara.fEnvTempLowAlmThre = fPara;
            break;
        case 0xA2:
            s_tSysPara.fBattChgFullAverVoltThre = fPara/100.0;
            break;
        case 0xA3:
            s_tSysPara.fBattChgFullAverCurrThre = fPara/100.0;
            break;
        case 0xA4:
            s_tSysPara.wChgMaxDura = (WORD)fPara;
            break;
        case 0xA5:
            s_tSysPara.wChgEndDura = (WORD)fPara;
            break;
        case 0xA6:
            s_tSysPara.fBattSupplVolt = fPara/100.0;
            break;
        case 0xA7:
            s_tSysPara.wBattSupplPeriod = (WORD)fPara;
            break;
        case 0xA8:
            s_tSysPara.fCellEquVoltDiffThre = fPara/100.0;
            break;
        case 0xA9:
            s_tSysPara.fPowerOffVoltThre = fPara/100.0;
            break;
        case 0xAA:
            s_tSysPara.fPowerOnVoltThre = fPara/100.0;
            break;
        case 0xAB:
            s_tSysPara.wHisDataInter = (WORD)fPara;
            break;
        case 0xAC: 
            //删除软关机使能参数，可设置实际没有使用
            s_tSysPara.bSleepEn = (BOOLEAN)fPara;
            break;
        case 0xAD:
            s_tSysPara.bBuzzerEnable = (BOOLEAN)fPara;
            break;
        case 0xAE:
            s_tSysPara.wDischgSwitchSOC = (WORD)fPara;
            break;
        case 0xAF:
            s_tSysPara.fCommonLiLimitCurr = fPara/100.0;
            break;
        default:
        return False;
    }
    return True;
}
    
static BYTE RecBmsPara4(FLOAT fPara) {
    switch (s_tProtocol.ucCommandType){
        case 0xB0:
            s_tProtocol.ucRTN   = RTN_INVALID_DATA;
            return True;
        case 0xB1:
            s_tSysPara.ucRunMode = (BYTE)fPara;
            break;
        case 0xB2:
            s_tSysPara.ucUsageScen = (BYTE)fPara;
            break;
        case 0xB3:
            s_tSysPara.ucCycleMode = (BYTE)fPara;
            break;
        case 0xB4:
            s_tSysPara.fLABattSwitchVolt = fPara/100.0;
            break;
        case 0xB5:
            s_tSysPara.fRemoteSupplyOutVolt = fPara/100.0;
            break;
        case 0xB6:
            s_tSysPara.ucLiAcidDischargeRate = (BYTE)fPara;
            break;
        case 0xB7:
            s_tSysPara.ucDODPerDischarge = (BYTE)fPara;
            break;
        case 0xB8:
            s_tSysPara.wLABattDurationPerDischarge = (WORD)fPara;
            break;
        case 0xB9:
            s_tSysPara.bBoostCharge = (BOOLEAN)fPara;
            break;
        case 0xBA:
            s_tSysPara.fDischgMaxCurr = fPara/100.0;
            break;
        case 0xBB:
            s_tSysPara.fPowerDownVoltThre = fPara/100.0;
            break;
        case 0xBC:
            s_tSysPara.fOutputOffsetVoltThre = fPara/100.0;
            break;
        case 0xBD:
            s_tSysPara.ucCellUVPDelay = (BOOLEAN)fPara;
            break;
        default:
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;  /* 命令格式错 */
        return False;
    }
    return True;
}

static void RecBmsParaAll(FLOAT fPara) {
    if (RecBmsPara1(fPara)) {
        return;
    }
    if (RecBmsPara2(fPara)) {
        return;
    }
    if (RecBmsPara3(fPara)) {
        return;
    }
    if (RecBmsPara4(fPara)) {
        return;
    }
    return;
}

static void RecBmsPara(BYTE ucPort)
{
    FLOAT fPara;
	U_16Int tData;
	const BYTE aucJudgeType[] = {0x9B,0x9C,0x9D,0x9E,0xA4,0xA5,0xA7,0xAB,0xAC,0xAD,0xAE,
		                         0xB1,0xB2,0xB3,0xB6,0xB7,0xB8,0xB9,0xBD,0x00,0x00};

    GetSysPara( &s_tSysPara );

    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[8];
    fPara = (FLOAT)(Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+9)));
	tData.ucByte[0] = s_tProtocol.aucRecBuf[10];
	tData.ucByte[1] = s_tProtocol.aucRecBuf[9];

    //屏蔽环境温度高告警和环境温度低告警阈值的设置 mwl
    /*if(s_tProtocol.ucCommandType==0xA0 || s_tProtocol.ucCommandType==0xA1)
    {
        s_tProtocol.ucRTN   = RTN_INVALID_DATA;
        return;
    }*/
	//WORD型 BYTE型 BOOLEAN型参数,不能下发负数 mwl 20201222
	if(tData.sData < 0)
	{
		BYTE i = 0;
		while(aucJudgeType[i] != 0x00)
		{
			if(aucJudgeType[i] == s_tProtocol.ucCommandType)
			{
				s_tProtocol.ucRTN   = RTN_INVALID_DATA;
        		return;
			}
			i++;
		}
	}

    RecBmsParaAll(fPara);

    if (s_tProtocol.ucRTN == RTN_INVALID_DATA || s_tProtocol.ucRTN == RTN_WRONG_COMMAND) {
        return;
    }
    
    if ( !(SetSysPara( &s_tSysPara, True, CHANGE_BY_YD1363 )) )
    {
        s_tProtocol.ucRTN   = RTN_INVALID_DATA; 
    }

    if (s_tProtocol.ucCommandType == 0xB2 && s_tProtocol.ucRTN != RTN_INVALID_DATA)
    {
        SynTimeAndMode(); // 主机修改时间或应用场景同步从机
    }

    return;
}

static void SendBmsSpecialPara(BYTE ucPort)
{
    WORD wSendLen = 456;//(1+1+2++20+2+20+10+20+20*6+32)*2 = 228*2=456
    BYTE *p = NULL;
    SHORT sTemp;
    rt_memset(&s_tSysPara, 0, sizeof(T_SysPara));    
    GetSysPara(&s_tSysPara);
    
    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    *p++ = GetDataFlag( BCM_ALARM, ucPort );
    *p++ = BATT_NUM_BCM;//铁锂电池组数M

    sTemp  = Host2Modbus((SHORT*)&s_tSysPara.wSoftAntiTheftDelay);
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    rt_memcpy(p, (BYTE*)s_tSysPara.acBackstageIpAddr, 20);
    p += 20;
    sTemp  = Host2Modbus((SHORT*)&s_tSysPara.wBackstagePort);  
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    rt_memcpy(p, (BYTE*)s_tSysPara.acGPGSUserName, 20);
    p += 20;
    rt_memcpy(p, (BYTE*)s_tSysPara.acGPGSPazwerd, 10);
    p += 10;
    rt_memcpy(p, (BYTE*)s_tSysPara.acGPGSAPN, 20);
    p += 20;
    rt_memcpy(p, (BYTE*)s_tSysPara.acAlmPhoneNum_1, 20);
    p += 20;
    rt_memcpy(p, (BYTE*)s_tSysPara.acAlmPhoneNum_2, 20);
    p += 20;
    rt_memcpy(p, (BYTE*)s_tSysPara.acAlmPhoneNum_3, 20);
    p += 20;
    rt_memcpy(p, (BYTE*)s_tSysPara.acAlmPhoneNum_4, 20);
    p += 20;
    rt_memcpy(p, (BYTE*)s_tSysPara.acAlmPhoneNum_5, 20);
    p += 20;
    rt_memcpy(p, (BYTE*)s_tSysPara.acSMSCenterNum, 20);
    p += 20;
    //190128增加设备名称
    rt_memcpy(p, (BYTE*)s_tSysPara.acDeviceName, 32);
    p += 32;
    *p = 0x00;    //KW4
    //s_tProtocol.wSendLenid  = (WORD) ((p-s_tProtocol.aucSendBuf)*2);
    s_tProtocol.wSendLenid = wSendLen;//为解决KW问题
    return;
}

static void RecBmsSpecialPara(BYTE ucPort)
{
    FLOAT  fPara = 0.0;
    U_16Int tData;
	BYTE ucLenBuf[13] = {2, 20, 2, 20, 10, 20, 20, 20, 20, 20, 20, 20, 32};
    GetSysPara( &s_tSysPara );

    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[8];
	tData.ucByte[0] = s_tProtocol.aucRecBuf[10];
	tData.ucByte[1] = s_tProtocol.aucRecBuf[9];

    if(s_tProtocol.ucCommandType < 0x80 + sizeof(ucLenBuf) && s_tProtocol.ucCommandType >= 0x80)
    {
        if(ucLenBuf[s_tProtocol.ucCommandType - 0x80] != (s_tProtocol.wRecLenid / 2 - 2))
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
            return;
        }
    }
    else
    {
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
        return;
    }
    switch (s_tProtocol.ucCommandType)
    {
        case 0x80:
			if(tData.sData < 0)   //WORD型 拦截负数 20210106
			{
				s_tProtocol.ucRTN  = RTN_INVALID_DATA;
				return;
			}
			fPara = (FLOAT)(Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+9)));
            s_tSysPara.wSoftAntiTheftDelay = (WORD)fPara;
            break;
        case 0x81:
            ParaStrCpy(s_tSysPara.acBackstageIpAddr, (s_tProtocol.aucRecBuf+9), sizeof(s_tSysPara.acBackstageIpAddr));        
            break;
        case 0x82:
            s_tSysPara.wBackstagePort = (WORD)Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+9));
            break;
        case 0x83:
            ParaStrCpy(s_tSysPara.acGPGSUserName, (s_tProtocol.aucRecBuf+9), sizeof(s_tSysPara.acGPGSUserName));
            break;
        case 0x84:
            ParaStrCpy(s_tSysPara.acGPGSPazwerd, (s_tProtocol.aucRecBuf+9), sizeof(s_tSysPara.acGPGSPazwerd));
            break;
        case 0x85:
            ParaStrCpy(s_tSysPara.acGPGSAPN, (s_tProtocol.aucRecBuf+9), sizeof(s_tSysPara.acGPGSAPN));                                        
            break;
        case 0x86:
            ParaStrCpy(s_tSysPara.acAlmPhoneNum_1, (s_tProtocol.aucRecBuf+9), sizeof(s_tSysPara.acAlmPhoneNum_1));                                
            break;
        case 0x87:
            ParaStrCpy(s_tSysPara.acAlmPhoneNum_2, (s_tProtocol.aucRecBuf+9), sizeof(s_tSysPara.acAlmPhoneNum_2));                        
            break;
        case 0x88:
            ParaStrCpy(s_tSysPara.acAlmPhoneNum_3, (s_tProtocol.aucRecBuf+9), sizeof(s_tSysPara.acAlmPhoneNum_3));                
            break;
        case 0x89:
            ParaStrCpy(s_tSysPara.acAlmPhoneNum_4, (s_tProtocol.aucRecBuf+9), sizeof(s_tSysPara.acAlmPhoneNum_4));        
            break;
        case 0x8A:
            ParaStrCpy(s_tSysPara.acAlmPhoneNum_5, (s_tProtocol.aucRecBuf+9), sizeof(s_tSysPara.acAlmPhoneNum_5));
            break;
        case 0x8B:
            ParaStrCpy(s_tSysPara.acSMSCenterNum, (s_tProtocol.aucRecBuf+9), sizeof(s_tSysPara.acSMSCenterNum));
            break;
        case 0x8C:
            ParaStrCpy(s_tSysPara.acDeviceName, (s_tProtocol.aucRecBuf+9), sizeof(s_tSysPara.acDeviceName));
            break;
        default:
            s_tProtocol.ucRTN  = RTN_WRONG_COMMAND;  
            return;
    }
    
    if ( !(SetSysPara( &s_tSysPara, True, CHANGE_BY_YD1363 )) )
    {
         s_tProtocol.ucRTN  = RTN_INVALID_DATA; 
    }

    return;
}
static void SendBmsAlmLevel(BYTE ucPort)
{
    BYTE *p = NULL;
    BYTE i;

    rt_memset(&s_tSysPara, 0, sizeof(T_SysPara));    
    GetSysPara(&s_tSysPara);
    
    p   = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    *p++ = GetDataFlag( BCM_ALARM, ucPort );
    *p++ = BATT_NUM_BCM;//铁锂电池组数M

    for (i = 0; i < ALARM_CLASS_OLD; i++)
    {
        *p++ = 0;//两个字节，第一个字节不填充
        *p++ = s_tSysPara.aucAlarmLevel[i]; 
    }
    
    s_tProtocol.wSendLenid  = GetLength((p-s_tProtocol.aucSendBuf)*2);
    return;
}

static void RecBmsAlmLevel(BYTE ucPort)
{
    BYTE ucPara;
    BYTE ucIndex = 0;

    GetSysPara( &s_tSysPara );

    s_tProtocol.ucCommandType   = s_tProtocol.aucRecBuf[8];

    ucPara = s_tProtocol.aucRecBuf[10];

    //屏蔽环境温度高和环境温度低告警级别的设置                      mwl
    /*if(s_tProtocol.ucCommandType == 0x87 || s_tProtocol.ucCommandType == 0x88)
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
        return;
    }*/
    if (s_tProtocol.ucCommandType >= 0x80
        &&s_tProtocol.ucCommandType <= 0xAE)//190128增加振动告警??
    {
        ucIndex = s_tProtocol.ucCommandType -0x80;
        s_tSysPara.aucAlarmLevel[ucIndex] = ucPara;
    }
    else
    {
        s_tProtocol.ucRTN  = RTN_WRONG_COMMAND;
        return;
    }
    if ( !(SetSysPara( &s_tSysPara, True, CHANGE_BY_YD1363 )) )
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;   
    }                
       
    return;
}

static void SendBmsAlmRelay(BYTE ucPort)
{
    BYTE *p = NULL;
    BYTE i;

    rt_memset(&s_tSysPara, 0, sizeof(T_SysPara));    
    GetSysPara(&s_tSysPara);
    
    p   = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    *p++ = GetDataFlag( BCM_ALARM, ucPort );
    *p++ = BATT_NUM_BCM;//铁锂电池组数M

    for (i = 0; i < ALARM_CLASS_OLD; i++)
    {
        *p++ = 0;//两个字节，第一个字节不填充
        *p++ = s_tSysPara.aucRelayBit[i]; 
    }
    
    s_tProtocol.wSendLenid  = GetLength((p-s_tProtocol.aucSendBuf)*2);

    return;
}

static void RecBmsAlmRelay(BYTE ucPort)
{
    BYTE ucPara;
    BYTE ucIndex = 0;

    GetSysPara( &s_tSysPara );

    s_tProtocol.ucCommandType   = s_tProtocol.aucRecBuf[8];

    ucPara = s_tProtocol.aucRecBuf[10];

    if (s_tProtocol.ucCommandType >= 0x80
        &&s_tProtocol.ucCommandType <= 0xAE)//190128增加振动告警??
    {
        ucIndex = s_tProtocol.ucCommandType -0x80;
        s_tSysPara.aucRelayBit[ucIndex] = ucPara;
    }
    else
    {
        s_tProtocol.ucRTN  = RTN_WRONG_COMMAND;
        return;
    }
    if ( !(SetSysPara( &s_tSysPara, True, CHANGE_BY_YD1363 )) )
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;   
    }

    return;
}

static void SendBmsHisData( BYTE ucPort )    //R321 1363协议获取历史数据接口
{
    BYTE *p = NULL;
    WORD num = 0;
    T_HisDataStruct tHisDataSave;
    struct tm tTime;
    SHORT sTemp;
    rt_memset(&tTime, 0x00, sizeof(tTime));

    rt_memset(&s_tSysPara, 0, sizeof(T_SysPara));
    GetSysPara(&s_tSysPara);
    // 检查当前历史记录版本是否正确
    /*if(s_tSysPara.ucHisDataType == HIS_DATA_TOWER_VER)
    {
        s_tProtocol.ucRTN   = RTN_NO_DATA;
        return;
    }*/

    if ( !CheckCommandType() )
    {
        return;
    }
    rt_memset(&tHisDataSave, 0, sizeof(T_HisDataStruct));

    p    = s_tProtocol.aucSendBuf;
    if( 0x01 == s_tProtocol.ucCommandType )
    {
        MoveProtoHisDataPoint(1, NORTH_PROTOCOL_1363);
    }

    num = GetProtoHisDataNum(NORTH_PROTOCOL_1363);

    if ( 0 == num)
    {    
        s_tProtocol.ucRTN   = RTN_NO_DATA;
        return;
    }   
    else if ( 1 == num)  /* COMMAND_TYPE */
    {
        *p++    = 0x01;     /* 发送最后一条历史数据 */
    }
    else
    {
        *p++    = 0x00;     /* 正常发送下一条历史数据 */
    }

    /* DATA_FLAG */
    //*p++    =   ucDataFlag;

    *p++ = BATT_NUM_BCM;//铁锂电池组数M;
    //ReadMoreHisData((BYTE *)&tHisDataSave,TYPE_DC);
    ReadHisData(&tHisDataSave, NORTH_PROTOCOL_1363);
    localtime_r(&tHisDataSave.tStartTime, &tTime);
    PutInt16ToBuff(p, tTime.tm_year + 1900);
    p += 2;
    *p++ = tTime.tm_mon + 1;
    *p++ = tTime.tm_mday;
    *p++ = tTime.tm_hour;
    *p++ = tTime.tm_min;
    *p++ = tTime.tm_sec;

    /* DATAI */
    sTemp = Host2Modbus((SHORT*)&tHisDataSave.wCellVoltMax);    //单体电压最大值
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    *p++ = tHisDataSave.ucCellVoltMaxNo;
    sTemp  = Host2Modbus((SHORT*)&tHisDataSave.wCellVoltMin);    //单体电压最小值  
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    *p++ = tHisDataSave.ucCellVoltMinNo;
    sTemp  = Host2Modbus((SHORT*)&tHisDataSave.wCellTempMax);    //单体温度最大值 
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    *p++ = tHisDataSave.ucCellTempMaxNo;
    sTemp  = Host2Modbus((SHORT*)&tHisDataSave.wCellTempMin);    //单体温度最小值
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    *p++ = tHisDataSave.ucCellTempMinNo;
    sTemp  = Host2Modbus((SHORT*)&tHisDataSave.wBattCurr);    //电池电流 
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = Host2Modbus((SHORT*)&tHisDataSave.wBattVolt);     //电池电压 
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = Host2Modbus((SHORT*)&tHisDataSave.wBusVolt);    //母排电压 
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = Host2Modbus((SHORT*)&tHisDataSave.wBattCycleTimes);     //电池循环次数  
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    *p++ = (rt_int8_t)tHisDataSave.sEnvTemp;
    *p++ = (rt_uint8_t)tHisDataSave.wBattSOH;
    *p++ = (rt_uint8_t)tHisDataSave.wBattSOC;
    *p++ = (tHisDataSave.wStatus>>CELL_EQU_STA_BIT)&1; //均衡状态
    *p++ = (tHisDataSave.wStatus>>CHG_PROT_STA_BIT)&1; //充电保护状态
    *p++ = (tHisDataSave.wStatus>>DISCH_PROT_STA_BIT)&1; //放电保护状态
    *p++ = tHisDataSave.ucBattStatus; //电池组状态
    *p++ = (tHisDataSave.wStatus>>BATT_VOLH_PROT_BIT)&1;//电池组过压保护
    *p++ = (tHisDataSave.wStatus>>BATT_VOLL_PROT_BIT)&1;//电池组欠压保护
    *p++ = (tHisDataSave.wStatus>>CHG_CURH_PROT_BIT)&1; //充电过流保护
    *p++ = (tHisDataSave.wStatus>>DISCH_CURH_PROT_BIT)&1;//放电过流保护
    *p++ = (tHisDataSave.wStatus>>LIMIT_STA_BIT)&1; //限流状态
    *p++ = (tHisDataSave.wStatus>>BDU_SLEEP_STA_BIT)&1; //BDU休眠状态
    *p++ = (tHisDataSave.wStatus>>BMS_CHGDISCH_STA_BIT)&15;//bdu充放电状态 //待添加

    /* LENID */
    s_tProtocol.wSendLenid  = GetLength((p-s_tProtocol.aucSendBuf)*2);   
    return;
}

static void SendBmsHisData_new( BYTE ucPort ) {
    BYTE *p = NULL;
    WORD num = 0;
    T_HisDataStruct tHisDataSave;
    struct tm tTime;
    SHORT sTemp;
    rt_memset(&tTime, 0x00, sizeof(tTime));

    rt_memset(&s_tSysPara, 0, sizeof(T_SysPara));
    GetSysPara(&s_tSysPara);

    if ( !CheckCommandType() )
    {
        return;
    }
    rt_memset_s(&tHisDataSave, sizeof(T_HisDataStruct), 0, sizeof(T_HisDataStruct));

    p    = s_tProtocol.aucSendBuf;
    if( 0x01 == s_tProtocol.ucCommandType )
    {
        MoveProtoHisDataPoint(1, NORTH_PROTOCOL_1363);
    }

    num = GetProtoHisDataNum(NORTH_PROTOCOL_1363);
		
    if ( 0 == num)
    {    
        s_tProtocol.ucRTN   = RTN_NO_DATA;
        return;
    }   
    else if ( 1 == num)  /* COMMAND_TYPE */
    {
        *p++    = 0x01;     /* 发送最后一条历史数据 */
    }
    else
    {
        *p++    = 0x00;     /* 正常发送下一条历史数据 */
    }

    /* DATA_FLAG */
    //*p++    =   ucDataFlag;
    
	*p++ = s_tProtocol.aucRecBuf[9];   //帧ID

    *p++ = BATT_NUM_BCM;//铁锂电池组数M;
    //ReadMoreHisData((BYTE *)&tHisDataSave,TYPE_DC);
    ReadHisData(&tHisDataSave, NORTH_PROTOCOL_1363);
    localtime_r(&tHisDataSave.tStartTime, &tTime);
    PutInt16ToBuff(p, tTime.tm_year + 1900);
    p += 2;
    *p++ = tTime.tm_mon + 1;
    *p++ = tTime.tm_mday;
    *p++ = tTime.tm_hour;
    *p++ = tTime.tm_min;
    *p++ = tTime.tm_sec;

   /* DATAI */
    sTemp = Host2Modbus((SHORT*)&tHisDataSave.wCellVoltMax);    //单体电压最大值
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    *p++ = tHisDataSave.ucCellVoltMaxNo;
    sTemp  = Host2Modbus((SHORT*)&tHisDataSave.wCellVoltMin);    //单体电压最小值  
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    *p++ = tHisDataSave.ucCellVoltMinNo;
    sTemp  = Host2Modbus((SHORT*)&tHisDataSave.wCellTempMax);    //单体温度最大值 
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    *p++ = tHisDataSave.ucCellTempMaxNo;
    sTemp  = Host2Modbus((SHORT*)&tHisDataSave.wCellTempMin);    //单体温度最小值
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    *p++ = tHisDataSave.ucCellTempMinNo;
    sTemp  = Host2Modbus((SHORT*)&tHisDataSave.wBattCurr);    //电池电流 
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = Host2Modbus((SHORT*)&tHisDataSave.wBattVolt);     //电池电压 
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = Host2Modbus((SHORT*)&tHisDataSave.wBusVolt);    //母排电压 
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = Host2Modbus((SHORT*)&tHisDataSave.wBattCycleTimes);     //电池循环次数  
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    *p++ = (rt_int8_t)tHisDataSave.sEnvTemp;
    *p++ = (rt_uint8_t)tHisDataSave.wBattSOH;
    *p++ = (rt_uint8_t)tHisDataSave.wBattSOC;
    *p++ = (tHisDataSave.wStatus>>CELL_EQU_STA_BIT)&1; //均衡状态
    *p++ = (tHisDataSave.wStatus>>CHG_PROT_STA_BIT)&1; //充电保护状态
    *p++ = (tHisDataSave.wStatus>>DISCH_PROT_STA_BIT)&1; //放电保护状态
    *p++ = tHisDataSave.ucBattStatus; //电池组状态
    *p++ = (tHisDataSave.wStatus>>BATT_VOLH_PROT_BIT)&1;//电池组过压保护
    *p++ = (tHisDataSave.wStatus>>BATT_VOLL_PROT_BIT)&1;//电池组欠压保护
    *p++ = (tHisDataSave.wStatus>>CHG_CURH_PROT_BIT)&1; //充电过流保护
    *p++ = (tHisDataSave.wStatus>>DISCH_CURH_PROT_BIT)&1;//放电过流保护
    *p++ = (tHisDataSave.wStatus>>LIMIT_STA_BIT)&1; //限流状态
    *p++ = (tHisDataSave.wStatus>>BDU_SLEEP_STA_BIT)&1; //BDU休眠状态
    *p++ = (tHisDataSave.wStatus>>BMS_CHGDISCH_STA_BIT)&15;//bdu充放电状态 //待添加

    /* LENID */
    s_tProtocol.wSendLenid  = GetLength((p-s_tProtocol.aucSendBuf)*2);   
    return;
}

static void SendBmsHisData_time( BYTE ucPort )
{
    BYTE *p;
    static BYTE s_ucGetNum = 0;
    WORD alarmNum;
    static WORD s_wLastTop = 0xFFFF;
    static WORD s_wStartIndex = 0, s_wEndIndex = 0;
    BYTE ucCommandID = 0;
    static BYTE s_ucCommandID = 0;
    BYTE ucStartTimeRtn = 0, ucEndTimeRtn = 0;
    T_TimeStruct tStartTime, tEndTime;
    
    if ( !CheckHisCommandType() )
    {
        return;
    }
    
    /* 获取特定时间段内的历史数据条数 */
    if(s_tProtocol.ucCommandType == GET_RECORD_NUM)
    {
        if(s_tProtocol.wRecLenid != HIS_RECORD_LEN_TYPE_TIME)
        {
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
            return;
        }
        ExtractTimeSlot(&tStartTime, &tEndTime, &s_tProtocol.aucRecBuf[9]);

        if (CompareTime(tStartTime, tEndTime) == TIME_GREATER_THAN_TARGET)
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
            return;
        }

        alarmNum = GetHisSaveNum(HISTORICAL_DATA, &tStartTime, &tEndTime);

        p = s_tProtocol.aucSendBuf;
        rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

        /* 历史数据条数4字节 */
        *p++ = 0;
        *p++ = 0;
        *p++ = (alarmNum >> 8) & 0XFF;
        *p++ = alarmNum & 0XFF;
        s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
        return;
    }
    
    ucCommandID = s_tProtocol.aucRecBuf[COMMAND_ID];
    /* 获取特定时间段内的历史数据，根据起始与终止时间，得到最终的readpoint */
    if(s_tProtocol.ucCommandType == GET_RECORD_TIME_SLOT)
    {
        if(s_tProtocol.wRecLenid != HIS_RECORD_LEN_TYPE_ID_TIME)
        {
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
            return;
        }
        ExtractTimeSlot(&tStartTime, &tEndTime, &s_tProtocol.aucRecBuf[COMMAND_TIME]);

        if (IfTimeAllZero(tStartTime, tEndTime) == True)
        {
            s_wStartIndex = 0;
            s_wEndIndex = GetHisDataTotNum() - 1;
        }
        else
        {
            DivideRecordToBlock(s_atBlockTime, &s_ucBlockNum, HISTORICAL_DATA);
            if (s_ucBlockNum == 0)
            {
                s_tProtocol.ucRTN = RTN_NO_DATA;
                return;
            }

            /* 获得起始时间与终止时间对应的readpoint */
            ucStartTimeRtn = GetStartTimePose(s_atBlockTime, s_ucBlockNum, tStartTime, &s_wStartIndex, HISTORICAL_DATA);
            ucEndTimeRtn = GetEndTimePose(s_atBlockTime, s_ucBlockNum, tEndTime, &s_wEndIndex, HISTORICAL_DATA);
            if ((ucStartTimeRtn == TIME_INVALID) || (ucEndTimeRtn == TIME_INVALID))
            {
                s_tProtocol.ucRTN = RTN_NO_DATA;
                return;
            }
        }

        SetProtoHisDataPoint(s_wStartIndex, NORTH_PROTOCOL_1363);
    }
    
    alarmNum = s_wEndIndex - GetProtoHisDataPoint(NORTH_PROTOCOL_1363) + 1;
    if (alarmNum == 0)
    {
        ClearDataFlag(BCM_ALARM, 0xFE, ucPort);
        s_tProtocol.ucRTN = RTN_NO_DATA;
        return;
    }

    if(s_tProtocol.ucCommandType == GET_RECORD_CORRECT || s_tProtocol.ucCommandType == GET_RECORD_WRONG || s_tProtocol.ucCommandType == GET_RECORD_COMPLETE) {
        if (s_tProtocol.wRecLenid != HIS_RECORD_LEN_TYPE_ID)
        {
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
            return;
        }
    }
    
    /* 发送第一条历史数据时不会进入以下判断，第二次发送开始进入 */
    if (s_tProtocol.ucCommandType == GET_RECORD_CORRECT  || (s_tProtocol.ucCommandType == GET_RECORD_WRONG && ((s_ucCommandID + 1) == ucCommandID)) )
    {
        if (GetProtoHisDataPoint(NORTH_PROTOCOL_1363) == s_wLastTop)
        {
            MoveProtoHisDataPoint(s_ucGetNum, NORTH_PROTOCOL_1363); // 移动历史数据的readpoint
        }
        alarmNum = s_wEndIndex - GetProtoHisDataPoint(NORTH_PROTOCOL_1363) + 1;
        if (alarmNum == 0)
        {
            ClearDataFlag(BCM_ALARM, 0xFE, ucPort);
            s_tProtocol.ucRTN = RTN_NO_DATA;
            return;
        }
    }
    s_ucCommandID = ucCommandID;
    s_wLastTop = GetProtoHisDataPoint(NORTH_PROTOCOL_1363);
    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

    /* DATA TYPE判断，判断是否为最后一次发送 */
    *p++ = alarmNum > 2 ? 0x00 : 0x01;

    *p++ = ucCommandID;
    s_ucGetNum = alarmNum > 2 ? 2 : alarmNum; // 实际待读取的条数
    *p++ = s_ucGetNum;
    ReadMoreHisData_new(s_ucGetNum, p, NORTH_PROTOCOL_1363);
    p += 41 * s_ucGetNum; // 移动指针
    
    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    return ;
}

/***************************************************************************
 * @brief    是否为无效时间
 **************************************************************************/
Static BOOLEAN isInValidTime(BYTE ucStartTimeRtn, BYTE ucEndTimeRtn)
{
    if((ucStartTimeRtn == TIME_INVALID) || (ucEndTimeRtn == TIME_INVALID))
    {
        return True;
    }
    return False;
}

/***************************************************************************
 * @brief    函数名并非字面意思，主要为了降低圈复杂度
 **************************************************************************/
static BOOLEAN isValidCommandType(void)
{
    if(s_tProtocol.ucCommandType == GET_RECORD_CORRECT || s_tProtocol.ucCommandType == GET_RECORD_WRONG || s_tProtocol.ucCommandType == GET_RECORD_COMPLETE)
    {
        return True;
    }
    return False;
}

/***************************************************************************
 * @brief    读取并打包回包数据
 **************************************************************************/
Static BOOLEAN ReadAndPackDcrRecord(BYTE *aucSendBuf, WORD wDcrDataNum, BYTE *ucGetNum, BYTE ucCommandId)
{
    /* DATA TYPE判断，判断是否为最后一次发送 */
    *aucSendBuf++ = wDcrDataNum > 2 ? 0x00 : 0x01;

    *aucSendBuf++ = ucCommandId;
    *ucGetNum = wDcrDataNum > 2 ? 2 : wDcrDataNum; // 实际待读取的条数
    *aucSendBuf++ = *ucGetNum;
    ReadMoreDcrRecord_new(*ucGetNum, aucSendBuf, NORTH_PROTOCOL_1363);
    aucSendBuf += 38 * *ucGetNum; // 移动指针
    /* LENID */
    s_tProtocol.wSendLenid = GetLength((aucSendBuf - s_tProtocol.aucSendBuf) * 2);
    return True;
}

/***************************************************************************
 * @brief    计算获取条数异常时的返回码
 **************************************************************************/
static BOOLEAN isCalcNumFault(T_TimeStruct *tStartTime, T_TimeStruct *tEndTime)
{
    if(s_tProtocol.wRecLenid != HIS_RECORD_LEN_TYPE_TIME)
    {
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
        return True;
    }
    ExtractTimeSlot(tStartTime, tEndTime, &s_tProtocol.aucRecBuf[9]);

    if (CompareTime(*tStartTime, *tEndTime) == TIME_GREATER_THAN_TARGET)
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
        return True;
    }
    return False;
}

/***************************************************************************
 * @brief    是否为第二次发送直流内阻记录
 **************************************************************************/
Static BOOLEAN isSecondSendDcrRecord(BYTE s_ucCommandID, BYTE ucCommandID)
{
    if(s_tProtocol.ucCommandType == GET_RECORD_CORRECT  || (s_tProtocol.ucCommandType == GET_RECORD_WRONG && ((s_ucCommandID + 1) == ucCommandID)))
    {
        return True;
    }
    return False;
}

/// @brief 获取直流内阻记录(支持时间段)
/// @param ucPort 
static BOOLEAN SendDcrRecord_time( BYTE ucPort )
{
    BYTE *p;
    static BYTE s_ucGetNum = 0;
    WORD wDcrDataNum;
    static WORD s_wLastTop = 0xFFFF;
    static WORD s_wStartIndex = 0, s_wEndIndex = 0;
    BYTE ucCommandID = 0;
    static BYTE s_ucCommandID = 0;
    BYTE ucStartTimeRtn = 0, ucEndTimeRtn = 0;
    T_TimeStruct tStartTime, tEndTime;
    
    if ( !CheckHisCommandType() )
    {
        return FALSE;
    }
    
    /* 获取特定时间段内的历史数据条数 */
    if(s_tProtocol.ucCommandType == GET_RECORD_NUM)
    {
        if(isCalcNumFault(&tStartTime, &tEndTime))
        {
           return FALSE;
        }
        wDcrDataNum = GetHisSaveNum(DCR_RECORD, &tStartTime, &tEndTime);

        p = s_tProtocol.aucSendBuf;
        rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));

        /* 直流内阻记录条数4字节 */
        *p++ = 0;
        *p++ = 0;
        *p++ = (wDcrDataNum >> 8) & 0XFF;
        *p++ = wDcrDataNum & 0XFF;
        s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
        return TRUE;
    }
    
    ucCommandID = s_tProtocol.aucRecBuf[COMMAND_ID];
    /* 获取特定时间段内的直流内阻记录，根据起始与终止时间，得到最终的readpoint */
    if(s_tProtocol.ucCommandType == GET_RECORD_TIME_SLOT)
    {
        if(s_tProtocol.wRecLenid != HIS_RECORD_LEN_TYPE_ID_TIME)
        {
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
            return FALSE;
        }
        ExtractTimeSlot(&tStartTime, &tEndTime, &s_tProtocol.aucRecBuf[COMMAND_TIME]);

        if (IfTimeAllZero(tStartTime, tEndTime) == True)
        {
            s_wStartIndex = 0;
            s_wEndIndex = GetDcrRecordTotNum() - 1;
        }
        else
        {
            DivideRecordToBlock(s_atBlockTime, &s_ucBlockNum, DCR_RECORD);
            if (s_ucBlockNum == 0)
            {
                s_tProtocol.ucRTN = RTN_NO_DATA;
                return FALSE;
            }

            /* 获得起始时间与终止时间对应的readpoint */
            ucStartTimeRtn = GetStartTimePose(s_atBlockTime, s_ucBlockNum, tStartTime, &s_wStartIndex, DCR_RECORD);
            ucEndTimeRtn = GetEndTimePose(s_atBlockTime, s_ucBlockNum, tEndTime, &s_wEndIndex, DCR_RECORD);
            if (isInValidTime(ucStartTimeRtn, ucEndTimeRtn))
            {
                s_tProtocol.ucRTN = RTN_NO_DATA;
                return FALSE;
            }
        }

        SetProtoDcrRecordPoint(s_wStartIndex, NORTH_PROTOCOL_1363);
    }
    
    wDcrDataNum = s_wEndIndex - GetProtoDcrRecordPoint(NORTH_PROTOCOL_1363) + 1;
    if (wDcrDataNum == 0)
    {
        ClearDataFlag(BCM_ALARM, 0xFE, ucPort);
        s_tProtocol.ucRTN = RTN_NO_DATA;
        return FALSE;
    }

    if(isValidCommandType()) 
    {
        if (s_tProtocol.wRecLenid != HIS_RECORD_LEN_TYPE_ID)
        {
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
            return FALSE;
        }
    }
    
    /* 发送第一条直流内阻记录时不会进入以下判断，第二次发送开始进入 */
    if ( isSecondSendDcrRecord(s_ucCommandID, ucCommandID) )
    {
        if (GetProtoDcrRecordPoint(NORTH_PROTOCOL_1363) == s_wLastTop)
        {
            MoveProtoDcrRecordPoint(s_ucGetNum, NORTH_PROTOCOL_1363); // 移动直流内阻记录的readpoint
        }
        wDcrDataNum = s_wEndIndex - GetProtoDcrRecordPoint(NORTH_PROTOCOL_1363) + 1;
        if (wDcrDataNum == 0)
        {
            ClearDataFlag(BCM_ALARM, 0xFE, ucPort);
            s_tProtocol.ucRTN = RTN_NO_DATA;
            return FALSE;
        }
    }
    s_ucCommandID = ucCommandID;
    s_wLastTop = GetProtoDcrRecordPoint(NORTH_PROTOCOL_1363);
    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

    ReadAndPackDcrRecord(p, wDcrDataNum, &s_ucGetNum, ucCommandID);
    return TRUE;
}

Static void SendBmsHisAlm( BYTE ucPort )
{
    BYTE *p;
    static BYTE getNum = 0;
    WORD alarmNum;
    BYTE *pRealNum;
    static WORD lastTop = 0xFFFF;
    if ( !CheckCommandType() )
    {
        return;
    }
    alarmNum = GetProtoHisAlarmNum(NORTH_PROTOCOL_1363);  //获取剩余条数
    //没有历史告警
    if (alarmNum == 0)
    {
        ClearDataFlag(BCM_ALARM, 0xFE, ucPort);
        s_tProtocol.ucRTN   = RTN_NO_DATA;
        return;
    }
    if (s_tProtocol.ucCommandType == 0x01)
    {
        if (GetProtoHisAlarmPoint(NORTH_PROTOCOL_1363)== lastTop)
        {
            MoveProtoHisAlarmPoint(getNum, NORTH_PROTOCOL_1363);
        }
        lastTop = GetProtoHisAlarmPoint(NORTH_PROTOCOL_1363);
        alarmNum = GetProtoHisAlarmNum(NORTH_PROTOCOL_1363);
        if (alarmNum == 0)
        {
            ClearDataFlag(BCM_ALARM, 0xFE, ucPort);
            s_tProtocol.ucRTN   = RTN_NO_DATA;
            return;
        }
    }
    lastTop = GetProtoHisAlarmPoint(NORTH_PROTOCOL_1363);
    p   = s_tProtocol.aucSendBuf;
    *p++ = alarmNum > 10 ? 0x00 : 0x01;
    *p++ = BATT_NUM_BCM; //电池组数
    getNum =  alarmNum > 10 ? 10 : alarmNum;
    pRealNum = p;
    p++;
    *pRealNum = ReadMoreHisAlarm(getNum, p, NORTH_PROTOCOL_1363);
    p += 16 * (*pRealNum);

    if(alarmNum <= 10)
    {
        MoveProtoHisAlarmPoint(getNum, NORTH_PROTOCOL_1363);
    }
    s_tProtocol.wSendLenid = GetLength((p-s_tProtocol.aucSendBuf)*2);
    return;
}

Static void SendBmsHisAlm_new( BYTE ucPort ) {
    BYTE *p;
    static BYTE getNum = 0;
    WORD alarmNum;
    BYTE *pRealNum;
    static WORD lastTop = 0xFFFF;
    if ( !CheckCommandType() )
    {
        return;
    }
    alarmNum = GetProtoHisAlarmNum(NORTH_PROTOCOL_1363);  //获取剩余条数
    //没有历史告警
    if (alarmNum == 0)
    {
        ClearDataFlag(BCM_ALARM, 0xFE, ucPort);
        s_tProtocol.ucRTN   = RTN_NO_DATA;
        return;
    }
    if (s_tProtocol.ucCommandType == 0x01)
    {
        if (GetProtoHisAlarmPoint(NORTH_PROTOCOL_1363)== lastTop)
        {
            MoveProtoHisAlarmPoint(getNum, NORTH_PROTOCOL_1363);
        }
        lastTop = GetProtoHisAlarmPoint(NORTH_PROTOCOL_1363);
        alarmNum = GetProtoHisAlarmNum(NORTH_PROTOCOL_1363);
        if (alarmNum == 0)
        {
            ClearDataFlag(BCM_ALARM, 0xFE, ucPort);
            s_tProtocol.ucRTN   = RTN_NO_DATA;
            return;
        }
    }
    lastTop = GetProtoHisAlarmPoint(NORTH_PROTOCOL_1363);
    p   = s_tProtocol.aucSendBuf;
    *p++ = alarmNum > 10 ? 0x00 : 0x01;

    *p++ = s_tProtocol.aucRecBuf[9];   //帧ID
    
    *p++ = BATT_NUM_BCM; //电池组数
    getNum =  alarmNum > 10 ? 10 : alarmNum;
    pRealNum = p;
    p++;
    *pRealNum = ReadMoreHisAlarm(getNum, p, NORTH_PROTOCOL_1363);
    p += 16* (*pRealNum);

    if(alarmNum <= 10)
    {
        MoveProtoHisAlarmPoint(getNum, NORTH_PROTOCOL_1363);
    }
    s_tProtocol.wSendLenid = GetLength((p-s_tProtocol.aucSendBuf)*2);
    return;
}

Static void SendBmsHisAlm_time( BYTE ucPort )
{
    BYTE *p;
    static BYTE s_ucGetNum = 0;
    WORD alarmNum;
    static WORD s_wLastTop = 0xFFFF;
    static WORD s_wStartIndex = 0, s_wEndIndex = 0;
    BYTE ucCommandID = 0;
    static BYTE s_ucCommandID = 0;
    BYTE ucStartTimeRtn = 0, ucEndTimeRtn = 0;
    T_TimeStruct tStartTime, tEndTime;
    
    if ( !CheckHisCommandType() )
    {
        return;
    }
    
    /* 获取特定时间段内的历史告警条数 */
    if(s_tProtocol.ucCommandType == GET_RECORD_NUM)
    {
        if(s_tProtocol.wRecLenid != HIS_RECORD_LEN_TYPE_TIME)
        {
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
            return;
        }
        ExtractTimeSlot(&tStartTime, &tEndTime, &s_tProtocol.aucRecBuf[9]);

        if (CompareTime(tStartTime, tEndTime) == TIME_GREATER_THAN_TARGET)
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
            return;
        }

        alarmNum = GetHisSaveNum(HISTORICAL_ALARM, &tStartTime, &tEndTime);

        p = s_tProtocol.aucSendBuf;
        rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

        /* 历史告警条数4字节 */
        *p++ = 0;
        *p++ = 0;
        *p++ = (alarmNum >> 8) & 0XFF;
        *p++ = alarmNum & 0XFF;
        s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
        return;
    }
    
    ucCommandID = s_tProtocol.aucRecBuf[COMMAND_ID];
    /* 获取特定时间段内的历史告警，根据起始与终止时间，得到最终的readpoint */
    if(s_tProtocol.ucCommandType == GET_RECORD_TIME_SLOT)
    {
        if(s_tProtocol.wRecLenid != HIS_RECORD_LEN_TYPE_ID_TIME)
        {
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
            return;
        }
        ExtractTimeSlot(&tStartTime, &tEndTime, &s_tProtocol.aucRecBuf[COMMAND_TIME]);

        if (IfTimeAllZero(tStartTime, tEndTime) == True)
        {
            s_wStartIndex = 0;
            s_wEndIndex = GetHisAlarmTotNum() - 1;
        }
        else
        {
            DivideRecordToBlock(s_atBlockTime, &s_ucBlockNum, HISTORICAL_ALARM);
            if (s_ucBlockNum == 0)
            {
                s_tProtocol.ucRTN = RTN_NO_DATA;
                return;
            }

            /* 获得起始时间与终止时间对应的readpoint */
            ucStartTimeRtn = GetStartTimePose(s_atBlockTime, s_ucBlockNum, tStartTime, &s_wStartIndex, HISTORICAL_ALARM);
            ucEndTimeRtn = GetEndTimePose(s_atBlockTime, s_ucBlockNum, tEndTime, &s_wEndIndex, HISTORICAL_ALARM);
            if ((ucStartTimeRtn == TIME_INVALID) || (ucEndTimeRtn == TIME_INVALID))
            {
                s_tProtocol.ucRTN = RTN_NO_DATA;
                return;
            }
        }

        SetProtoHisAlarmPoint(s_wStartIndex, NORTH_PROTOCOL_1363);
    }
    
    alarmNum = s_wEndIndex - GetProtoHisAlarmPoint(NORTH_PROTOCOL_1363) + 1;
    if (alarmNum == 0)
    {
        ClearDataFlag(BCM_ALARM, 0xFE, ucPort);
        s_tProtocol.ucRTN = RTN_NO_DATA;
        return;
    }

    if(s_tProtocol.ucCommandType == GET_RECORD_CORRECT || s_tProtocol.ucCommandType == GET_RECORD_WRONG || s_tProtocol.ucCommandType == GET_RECORD_COMPLETE) {
        if (s_tProtocol.wRecLenid != HIS_RECORD_LEN_TYPE_ID)
        {
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
            return;
        }
    }
    
    /* 发送第一条历史告警时不会进入以下判断，第二次发送开始进入 */
    if (s_tProtocol.ucCommandType == GET_RECORD_CORRECT || (s_tProtocol.ucCommandType == GET_RECORD_WRONG && ((s_ucCommandID + 1) == ucCommandID)) )
    {
        if (GetProtoHisAlarmPoint(NORTH_PROTOCOL_1363) == s_wLastTop)
        {
            MoveProtoHisAlarmPoint(s_ucGetNum, NORTH_PROTOCOL_1363); // 移动历史告警数据的readpoint
        }
        alarmNum = s_wEndIndex - GetProtoHisAlarmPoint(NORTH_PROTOCOL_1363) + 1;
        if (alarmNum == 0)
        {
            ClearDataFlag(BCM_ALARM, 0xFE, ucPort);
            s_tProtocol.ucRTN = RTN_NO_DATA;
            return;
        }
    }
    s_ucCommandID = ucCommandID;
    s_wLastTop = GetProtoHisAlarmPoint(NORTH_PROTOCOL_1363);
    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

    /* DATA TYPE判断，判断是否为最后一次发送 */
    *p++ = alarmNum > 10 ? 0x00 : 0x01;
    *p++ = ucCommandID;
    s_ucGetNum = alarmNum > 10 ? 10 : alarmNum; // 实际待读取的条数
    *p++ = s_ucGetNum;
    ReadMoreHisAlarm(s_ucGetNum, p, NORTH_PROTOCOL_1363);
    p += 16 * s_ucGetNum; // 移动指针

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
}

Static void SendBmsHisOperation( BYTE ucPort )
{
    BYTE *p;
    static BYTE s_ucGetNum = 0;
    WORD HisOperNum;
    static WORD s_wLastTop = 0xFFFF;
    if ( !CheckCommandType() )
    {
        return;
    }
    HisOperNum = GetProtoHisOperNum(NORTH_PROTOCOL_1363);  //获取剩余条数
    //没有操作记录
    if (HisOperNum == 0)
    {
        //ClearDataFlag(BCM_ALARM, 0xFE, ucPort);
        s_tProtocol.ucRTN   = RTN_NO_DATA;
        return;
    }
    if (s_tProtocol.ucCommandType == 0x01)
    {
        if (GetProtoHisOperPoint(NORTH_PROTOCOL_1363)== s_wLastTop)
        {
            MoveProtoHisOperPoint(s_ucGetNum, NORTH_PROTOCOL_1363);
        }
        s_wLastTop = GetProtoHisOperPoint(NORTH_PROTOCOL_1363);
        HisOperNum = GetProtoHisOperNum(NORTH_PROTOCOL_1363);
        if (HisOperNum == 0)
        {
            //ClearDataFlag(BCM_ALARM, 0xFE, ucPort);
            s_tProtocol.ucRTN   = RTN_NO_DATA;
            return;
        }
    }
    s_wLastTop = GetProtoHisOperPoint(NORTH_PROTOCOL_1363);
    p   = s_tProtocol.aucSendBuf;

    *p++ = HisOperNum > 3 ? 0x00 : 0x01;
    *p++ = 1;
    s_ucGetNum =  HisOperNum > 3 ? 3 : HisOperNum;
    *p++ = s_ucGetNum;
    ReadMoreHisOperation(s_ucGetNum, p, NORTH_PROTOCOL_1363);
    p += sizeof(T_ActionRecord) * s_ucGetNum;
    if(HisOperNum <= 3)
    {
        MoveProtoHisOperPoint(s_ucGetNum, NORTH_PROTOCOL_1363);
    }
    s_tProtocol.wSendLenid = GetLength((p-s_tProtocol.aucSendBuf)*2);
    return;
}

static void SendBmsHisOperation_new( BYTE ucPort ) {
    BYTE *p;
    static BYTE s_ucGetNum = 0;
    WORD HisOperNum;
    static WORD s_wLastTop = 0xFFFF;
    if ( !CheckCommandType() )
    {
        return;
    }
    HisOperNum = GetProtoHisOperNum(NORTH_PROTOCOL_1363);  //获取剩余条数
    //没有操作记录
    if (HisOperNum == 0)
    {
        //ClearDataFlag(BCM_ALARM, 0xFE, ucPort);
        s_tProtocol.ucRTN   = RTN_NO_DATA;
        return;
    }
    if (s_tProtocol.ucCommandType == 0x01)
    {
        if (GetProtoHisOperPoint(NORTH_PROTOCOL_1363)== s_wLastTop)
        {
            MoveProtoHisOperPoint(s_ucGetNum, NORTH_PROTOCOL_1363);
        }
        s_wLastTop = GetProtoHisOperPoint(NORTH_PROTOCOL_1363);
        HisOperNum = GetProtoHisOperNum(NORTH_PROTOCOL_1363);
        if (HisOperNum == 0)
        {
            //ClearDataFlag(BCM_ALARM, 0xFE, ucPort);
            s_tProtocol.ucRTN   = RTN_NO_DATA;
            return;
        }
    }
    s_wLastTop = GetProtoHisOperPoint(NORTH_PROTOCOL_1363);
    p   = s_tProtocol.aucSendBuf;

    *p++ = HisOperNum > 3 ? 0x00 : 0x01;

    *p++ = s_tProtocol.aucRecBuf[9];//帧ID

    *p++ = 1;
    s_ucGetNum =  HisOperNum > 3 ? 3 : HisOperNum;
    *p++ = s_ucGetNum;
    ReadMoreHisOperation(s_ucGetNum, p, NORTH_PROTOCOL_1363);
    p += sizeof(T_ActionRecord) * s_ucGetNum;
    if(HisOperNum <= 3)
    {
        MoveProtoHisOperPoint(s_ucGetNum, NORTH_PROTOCOL_1363);
    }
    s_tProtocol.wSendLenid = GetLength((p-s_tProtocol.aucSendBuf)*2);
    return;
}

Static void SendBmsHisOperation_time( BYTE ucPort )
{
    BYTE *p;
    static BYTE s_ucGetNum = 0;
    WORD HisOperNum;
    static WORD s_wLastTop = 0xFFFF;
    static WORD s_wStartIndex = 0, s_wEndIndex = 0;
    BYTE ucStartTimeRtn = 0, ucEndTimeRtn = 0;
    T_TimeStruct tStartTime, tEndTime;
    BYTE ucCommandID = 0;
    static BYTE s_ucCommandID = 0;

    if ( !CheckHisCommandType() )
    {
        return;
    }
    ucCommandID = s_tProtocol.aucRecBuf[COMMAND_ID];

    /* 获取特定时间段内的历史操作记录条数 */
    if (s_tProtocol.ucCommandType == GET_RECORD_NUM)
    {
        if(s_tProtocol.wRecLenid != HIS_RECORD_LEN_TYPE_TIME)
        {
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
            return;
        }
        ExtractTimeSlot(&tStartTime, &tEndTime, &s_tProtocol.aucRecBuf[9]);

        if (CompareTime(tStartTime, tEndTime) == TIME_GREATER_THAN_TARGET)
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
            return;
        }

        HisOperNum = GetHisSaveNum(HISTORICAL_ACTION, &tStartTime, &tEndTime);
        p = s_tProtocol.aucSendBuf;
        rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

        /* 历史操作记录条数4字节 */
        *p++ = 0;
        *p++ = 0;
        *p++ = (HisOperNum >> 8) & 0XFF;
        *p++ = HisOperNum & 0XFF;
        s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
        return;
    }

    /* 获取特定时间段内的历史操作，根据起始与终止时间，得到最终的readpoint */
    if (s_tProtocol.ucCommandType == GET_RECORD_TIME_SLOT)
    {
        if (s_tProtocol.wRecLenid != HIS_RECORD_LEN_TYPE_ID_TIME)
        {
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
            return;
        }

        ExtractTimeSlot(&tStartTime, &tEndTime, &s_tProtocol.aucRecBuf[COMMAND_TIME]);

        if (IfTimeAllZero(tStartTime, tEndTime) == True)
        {
            s_wStartIndex = 0;
            s_wEndIndex = GetHisOperationTotNum() - 1;
        }
        else
        {
            DivideRecordToBlock(s_atBlockTime, &s_ucBlockNum, HISTORICAL_ACTION);
            if (s_ucBlockNum == 0)
            {
                s_tProtocol.ucRTN = RTN_NO_DATA;
                return;
            }
            ucStartTimeRtn = GetStartTimePose(s_atBlockTime, s_ucBlockNum, tStartTime, &s_wStartIndex, HISTORICAL_ACTION);
            ucEndTimeRtn = GetEndTimePose(s_atBlockTime, s_ucBlockNum, tEndTime, &s_wEndIndex, HISTORICAL_ACTION);
            if ((ucStartTimeRtn == TIME_INVALID) || (ucEndTimeRtn == TIME_INVALID))
            {
                s_tProtocol.ucRTN = RTN_NO_DATA;
                return;
            }
        }

        SetProtoHisActionPoint(s_wStartIndex, NORTH_PROTOCOL_1363);
    }

    HisOperNum = s_wEndIndex - GetProtoHisOperPoint(NORTH_PROTOCOL_1363) + 1;

    if (HisOperNum == 0)
    {
        s_tProtocol.ucRTN = RTN_NO_DATA;
        return;
    }

    if(s_tProtocol.ucCommandType == GET_RECORD_CORRECT || s_tProtocol.ucCommandType == GET_RECORD_WRONG || s_tProtocol.ucCommandType == GET_RECORD_COMPLETE) {
        if (s_tProtocol.wRecLenid != HIS_RECORD_LEN_TYPE_ID)
        {
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
            return;
        }
    }

    if (s_tProtocol.ucCommandType == GET_RECORD_CORRECT || (s_tProtocol.ucCommandType == GET_RECORD_WRONG && ((s_ucCommandID + 1) == ucCommandID)) )
    {
        if (s_tProtocol.wRecLenid != HIS_RECORD_LEN_TYPE_ID)
        {
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
            return;
        }
        if (GetProtoHisOperPoint(NORTH_PROTOCOL_1363) == s_wLastTop)
        {
            MoveProtoHisOperPoint(s_ucGetNum, NORTH_PROTOCOL_1363);
        }
        HisOperNum = s_wEndIndex - GetProtoHisOperPoint(NORTH_PROTOCOL_1363) + 1;
        if (HisOperNum == 0)
        {
            s_tProtocol.ucRTN = RTN_NO_DATA;
            return;
        }
    }
    s_ucCommandID = ucCommandID;
    s_wLastTop = GetProtoHisOperPoint(NORTH_PROTOCOL_1363);
    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

    /* DATA TYPE判断 */
    *p++ = HisOperNum > 3 ? 0x00 : 0x01;
    *p++ = ucCommandID;
    s_ucGetNum = HisOperNum > 3 ? 3 : HisOperNum; // 实际待读取的条数
    *p++ = s_ucGetNum;
    ReadMoreHisOperation(s_ucGetNum, p, NORTH_PROTOCOL_1363);
    p += 28 * s_ucGetNum;

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    return;
}

/***************************************************************************
 * @brief    遥控重启BMS
 **************************************************************************/
Static void RemoteCtrlReset(void *para)
{
    ResetMCU(NO_RESET_REMOTE_CTRL);
}

Static void DealDischargeCurrCtrlCmd(void) {
    U_16Int tData;

    s_tProtocol.ucRTN = RTN_CORRECT;
    tData.ucByte[0] = s_tProtocol.aucRecBuf[10];
    tData.ucByte[1] = s_tProtocol.aucRecBuf[9];
    if(tData.sData > 30000 || tData.sData < -30000)
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA; 
    }
    else if(RUN_MODE_FREE != s_tSysPara.ucRunMode)
    {
        s_tProtocol.ucRTN = RTN_FAIL_COMMAND;
    }
    else if (SUCCESSFUL != SetValueFromProtocol(NPROTOCOL_TYPE_RS485_1363, INFO_TYPE_SET_MIX_BATT_CURR, tData.sData))
    {
        s_tProtocol.ucRTN = RTN_FAIL_COMMAND;
    }
    return;

}

static void DealChargeCtrlCmdWithCheck(BYTE bRunMode,BYTE bCtrlType,SHORT sValue) {
    CHAR scRet=0;

    if (bRunMode == s_tSysPara.ucRunMode)////only controlled mode, response the command.	Added by fengfj, 2021-07-29 20:50:41
    {
        scRet = SetValueFromProtocol(NPROTOCOL_TYPE_RS485_1363, bCtrlType, sValue);
        if(SUCCESSFUL != scRet)
        {
            s_tProtocol.ucRTN	=  RTN_INVALID_DATA;
        }
    }
    else
    {
        s_tProtocol.ucRTN	= RTN_FAIL_COMMAND;
    }

    return; 
}

Static void DealMaxSocCtrlCmd(void) {
    U_16Int tData;

    tData.ucByte[0] = s_tProtocol.aucRecBuf[10];
    tData.ucByte[1] = s_tProtocol.aucRecBuf[9];
    if(tData.sData > 10000 || tData.sData < 0)
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA; 
    }
    else if(IsMaster())
    {
        SetValueFromProtocol(NPROTOCOL_TYPE_RS485_1363, INFO_TYPE_SET_MAX_SOC, tData.sData);
    }
    else
    {
        s_tProtocol.ucRTN	= RTN_FAIL_COMMAND;
    }
}

Static void DealBattManualUnlockCtrlCmd(void) {
    T_BmsPACKFactoryStruct tBmsPackFacInfo;
    CHAR acLocalSeralNumber[20] = {0};
    WORD wCRCHighLow=0;
    BYTE bCRCHigh, bCRCLow;

    readBmsPackFacInfo(&tBmsPackFacInfo);
    rt_memcpy(&acLocalSeralNumber[0] ,&tBmsPackFacInfo.acBmsFacSn[0], 15);
    wCRCHighLow = UnlockCodeCreate(acLocalSeralNumber, rt_strnlen_s(acLocalSeralNumber, sizeof(acLocalSeralNumber)));
    bCRCHigh = ((0xff00 & wCRCHighLow)>>8);
    bCRCLow = ((0x00ff & wCRCHighLow));
    if(s_tProtocol.aucRecBuf[9]  != bCRCHigh || s_tProtocol.aucRecBuf[10] != bCRCLow)
    {
        s_tProtocol.ucRTN   = RTN_WRONG_COMMAND;
        return;
    }
    if (GPIO_OFF == GetPMOSStatus())
    {
        s_tProtocol.ucRTN = RTN_FAIL_COMMAND;
        return;
    }
    DealDeviceUnlock(DEVICE_UNLOCK_MANUAL);
    SaveAction(GetActionId(CONTOL_UNLOCK_ANTITHEFT), "1363 UnAnti-Theft");
}

/*
static void DealManualCancelDefenceCtrlCmd(void) {
    T_BmsPACKFactoryStruct tBmsPackFacInfo;
    T_BmsCustomerNameStruct tBmsCustomerName;
    CHAR acLocalSeralNumber[33] = {0};
    WORD wCRCHighLow=0;
    BYTE bCRCHigh = 0;
    BYTE bCRCLow = 0;

    rt_memset_s(&tBmsPackFacInfo, sizeof(T_BmsPACKFactoryStruct), 0, sizeof(T_BmsPACKFactoryStruct));
    rt_memset_s(&tBmsCustomerName, sizeof(T_BmsCustomerNameStruct), 0, sizeof(T_BmsCustomerNameStruct));

    readBmsPackFacInfo(&tBmsPackFacInfo);
    readBmsCustomerName(&tBmsCustomerName);
    rt_snprintf_s(&acLocalSeralNumber[0], sizeof(acLocalSeralNumber),"%s%s",&tBmsPackFacInfo.acBmsFacSn[0],&tBmsCustomerName.acBmsCustomerName[0]);
    //将客户名称后的一个字节置为结束符
    rt_memset_s(acLocalSeralNumber + rt_strnlen_s(&tBmsPackFacInfo.acBmsFacSn[0], sizeof(tBmsPackFacInfo.acBmsFacSn)) + 17, 1, 0, 1);
    wCRCHighLow = UnlockCodeCreate(acLocalSeralNumber,rt_strnlen_s(acLocalSeralNumber, 33));
    bCRCHigh = ((0xff00 & wCRCHighLow)>>8);
    bCRCLow = ((0x00ff & wCRCHighLow));
    if(s_tProtocol.aucRecBuf[9]  != bCRCHigh || s_tProtocol.aucRecBuf[10] != bCRCLow)
    {
        s_tProtocol.ucRTN   = RTN_WRONG_COMMAND;
        return;
    }
    if(GetDefenceStatus() == True) {
        WriteDefenceStatus(False);
    }
    SaveAction(GetActionId(CONTOL_CANCEL_DEVICE_DEFENCE), "MaualLeaveDefence");
}
*/

Static void DealBattIndicationCtrlCmd(void) {  
    RETURN_IF_FAIL(s_tProtocol.ucAddr != 0x00);// 不支持广播

    SetBattIndicationStatus(True);
    if (s_waitYD1363LedQuickNormalTimer == RT_NULL) {
        s_waitYD1363LedQuickNormalTimer = rt_timer_create("YD1363LedQuickNormal", CloseBattIndication, False, 15000, RT_TIMER_FLAG_ONE_SHOT);
    }
    
    if(s_waitYD1363LedQuickNormalTimer != RT_NULL)
    {
        rt_timer_start(s_waitYD1363LedQuickNormalTimer);
    }

    SaveAction(GetActionId(CONTOL_BATT_INDICATION),"1363 FindAddrbyLED");

    return;
}

static BOOLEAN DealSwitchBatteryAddress(void)
{
    if((s_tSysPara.ucBattAddressMode == MANUAL_MODE)&&(s_tProtocol.ucAddr == 0))
    {
        SetBattAddr(s_tSysPara.ucBattSwitchAddr);
        SaveAction(GetActionId(CONTOL_SWITCH_ADDR),"1363Switch Address");
        return True;
    }
    else
    {
        s_tProtocol.ucRTN = RTN_FAIL_COMMAND;
        return False;
    }
}

Static BOOLEAN DealSwitchCAN2Address(void)
{
    if((s_tSysPara.ucCAN2AddressMode == MANUAL_MODE)&&(s_tProtocol.ucAddr == 0))
    {
        SetBMSCan2Addr(s_tSysPara.ucCAN2SwitchAddr);
        return True;
    }
    else
    {
        s_tProtocol.ucRTN = RTN_FAIL_COMMAND;
        return False;
    }
}

static BOOLEAN CtrlRemoteReset(void)
{
#if !defined(KW_CHECK)
    if (RT_NULL == s_ResetTimer)
    {
        s_ResetTimer = rt_timer_create("ResetMCU", RemoteCtrlReset, RT_NULL, 2 * ONE_SEC, RT_TIMER_FLAG_ONE_SHOT | RT_TIMER_FLAG_SOFT_TIMER);
    }

    if (s_ResetTimer != NULL)
    {
        rt_timer_start(s_ResetTimer);
        return True;
    }
    else
    {
        s_tProtocol.ucRTN = RTN_FAIL_COMMAND;
        return False;
    }
#endif
}

Static BOOLEAN RecControlBMSOld(void)
{
    switch(s_tProtocol.ucCommandType)
    {
        case REMOTE_RESET:
            CtrlRemoteReset();
            break;
        case RESTORE_DEFAULT_PARA:
            LoadPara();
            SaveAction(GetActionId(CONTOL_RST_PARA), "1363Default Para");
            break;
        case RESET_ADD_COMPETE:
            //地址竞争      
            
            if(s_tSysPara.ucBattAddressMode == AUTO_MODE)
            {
                restartAddrCompete();
                SaveAction(GetActionId(CONTOL_ADDR_COMPETE), "1363 RstAddress"); 
            }
            
            break;
        case SET_CLEAR_LOCKSTAT:
            s_tProtocol.ucRTN = RTN_CORRECT;
            BduCtrl(SCI_CTRL_BDULOCK, 0);
            SaveAction(GetActionId(CONTOL_RELEASE_LOCK),"1363Remote Unlock");
            break;
        default:
            return False;
    }
    return True;
}

static BOOLEAN RecControlChgDischgOld(void)
{
    WORD wTemp = 0;

    switch(s_tProtocol.ucCommandType)
    {
        case REQUEST_CHARGE_CONTROLLED:
            //增加充电控制命令
            DealChargeCtrlCmd(RUN_MODE_CONTROLLED,INFO_TYPE_STATUS,POWER_STATUS_ON); //解决圈复杂度，将执行过程封装为一个函数
            break;
        case REQUEST_DISCH_CONTROLLED:
            //增加放电控制命令
            DealDischargeCtrlCmd(RUN_MODE_CONTROLLED,INFO_TYPE_STATUS,INFO_TYPE_OUT_VOLT,POWER_STATUS_OFF);	//解决圈复杂度，将执行过程封装为一个函数			
            break;
        case SET_CHARGE_CURR_COEF:
            wTemp = (Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+9)));
            DealChargeCtrlCmdWithCheck(RUN_MODE_CONTROLLED, INFO_TYPE_CHG_COEF, wTemp);
            break;
        case SET_CHARGE_PRIOR:
            wTemp = (Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+9)));
            SetValueFromProtocol(NPROTOCOL_TYPE_RS485_1363, INFO_TYPE_CHG_PRIOR, wTemp);
            break;
        case SET_DISCH_OUTVOLT:
            wTemp = (Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+9)));
            DealChargeCtrlCmdWithCheck(RUN_MODE_CONTROLLED, INFO_TYPE_OUT_VOLT, wTemp);
            break;
        case SET_DISCHARGE_CURR:
            DealDischargeCurrCtrlCmd();
            break;
        // Added by fengfj, 2021-07-29 20:55:11
        case REQUEST_CHARGE_FERR:
            //下发来电状态
            if(getChargeBreakStatus())
            {
                s_tProtocol.ucRTN = RTN_FAIL_COMMAND;
                return False;
            }
            DealChargeCtrlCmd(RUN_MODE_FREE,INFO_TYPE_STATUS,POWER_STATUS_ON); //解决圈复杂度，将执行过程封装为一个函数
            break;
        case REQUEST_DISCH_FREE:
            //下发停电状态
            DealDischargeCtrlCmd(RUN_MODE_FREE,INFO_TYPE_STATUS,INFO_TYPE_OUT_VOLT,POWER_STATUS_OFF); //解决圈复杂度，将执行过程封装未一个函数
            break;
        case SET_DISCH_VOLT_FREE:
            wTemp = (Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+9)));
            DealChargeCtrlCmdWithCheck(RUN_MODE_FREE, INFO_TYPE_DISCH_VOLT_FREE, wTemp);
            break;
        case SET_CHARGE_CURR_COEF_FREE:
            wTemp = (Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+9)));
            DealChargeCtrlCmdWithCheck(RUN_MODE_FREE, INFO_TYPE_CHG_COEF_FREE, wTemp);
            break;
        case SET_MAX_SOC:
            DealMaxSocCtrlCmd();
            break;
        default:
            return False;
    }
    return True;
}

static BOOLEAN RecControlNew(void)
{
    WORD wTemp = 0;
    SHORT sFmTemp = 0;
    BYTE ucEMCTest = 0;
    char *apEMCRecord[] = {
        "EMC TEST OFF",
        "EMC TEST ON",
    };

    switch(s_tProtocol.ucCommandType)
    {
        case SET_BATT_MANUAL_UNLOCK:
            DealBattManualUnlockCtrlCmd();
            if(s_tProtocol.ucRTN != RTN_CORRECT)
            {
                return False;
            }
            break;
        case GET_GPS_LOCATION_TO_PHONE:
            GPSSendSMS();
            break;
        case SET_BDU_EMC_TEST_STATUS:
            wTemp = (Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+9)));
            ucEMCTest = wTemp>=1?1:0;
            BduCtrl(SCI_CTRL_EMC_TEST,ucEMCTest);
            SaveAction(GetActionId(CONTOL_EMC_TEST), apEMCRecord[ucEMCTest]);
            break;
        case BATT_IN_PLACE_FLAG: // 电池在位指示
            DealBattIndicationCtrlCmd();
            break;
        case SWITCH_BATT_ADDR: // 地址切换
            DealSwitchBatteryAddress();
            break;
        case SET_HEATING_START: // 启动加热
            SetHeaterTestCtrl(HEATER_TEST_ON);
            SaveAction(GetActionId(CONTOL_START_HEATING), "START HEATING");
            break;
        case SET_HEATING_STOP: // 停止加热
            SetHeaterTestCtrl(HEATER_TEST_OFF);
            SaveAction(GetActionId(CONTOL_STOP_HEATING), "STOP HEATING");
            break; 
        case CLEAR_DCR_FAULT_PRT: //清除直流内阻异常保护
            ClearDcrFaultPrt();
            break;
        case ENTER_FM_MODE:
            EnterFMMode();
            break;
        case CONTROL_FM_POWER:
            sFmTemp = (Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+9)));
            SetFmPowerFrom1363((FLOAT)(sFmTemp/1000.0));
            break;
        case EXIT_FM_MODE:
            ExitFMMode();
            break;
        default:
            return False;
    }
    return True;
}

Static BOOLEAN RecControlSpecial(void)
{
    GetSysPara(&s_tSysPara);
    switch(s_tProtocol.ucCommandType)
    {
        case MANUAL_CTRL_ENTER_DEFENCE_STATE:
            ManualCtrlEnterDefenceStatus();
            break;
        /*
        case MANUAL_CANCEL_DEVICE_DEFENCE: // 人工撤防
            DealManualCancelDefenceCtrlCmd();
            if(s_tProtocol.ucRTN == RTN_WRONG_COMMAND)
            {
                return False;
            }
            break;*/
        case RESET_CAN2_ADD_COMPETE: 
            //地址竞争
            if(!restartCan2AddrCompete(s_tSysPara.ucCAN2AddressMode))
            {
                s_tProtocol.ucRTN = RTN_FAIL_COMMAND;
                return False;
            }
            SaveAction(GetActionId(CONTOL_CAN2_ADDR_COMPLETE), " ");
            break;
        case CLEAR_4G_TRAFFIC:
            Clear4GTraffic();
            SaveAction(GetActionId(CONTOL_CLEAR_4G_TRAFFIC), "Clear 4G Traffic");
            break;
        case SWITCH_CAN2_ADDR:
            DealSwitchCAN2Address();
            break;
#ifdef REDIRECT_VCOM_ENABLED
        case CTRL_ENET_PORT_ENABLE:
            send_message_to_enet_port(ENET_PORT_ENABLE);
            break;
        case CTRL_ENET_PORT_DISABLE:
            send_message_to_enet_port(ENET_PORT_DISABLE);
            break;
#endif
        default:
            return False;
    }
    return True;
}

static void RecControl(BYTE ucPort)
{
    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[8];
    s_tProtocol.ucRTN = RTN_CORRECT;

    if(RecControlBMSOld())
    {
        s_tProtocol.ucRTN = RTN_CORRECT;
    }
    else if(RecControlChgDischgOld())
    {
        s_tProtocol.ucRTN = RTN_CORRECT;
    }
    else if(RecControlNew())
    {
        s_tProtocol.ucRTN = RTN_CORRECT;
    }
    else if(RecControlSpecial())
    {

    }
    else
    {
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
    }

    if((s_tProtocol.ucAddr == 0x00) || (s_tProtocol.ucAddr == 0x40))
    {
        s_tProtocol.ucRTN = NO_RETURN;
    }

    return;
}

static void CheckBMSSnChange(void)
{
    if (s_tProtocol.ucCommandType == 0x80)
    {
        // BMS序列号变更了，密钥会变更，所以加密数据需重新加密存储
        if ( !(SetSysPara( &s_tSysPara, True, CHANGE_BY_YD1363 )) )
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
        }
    }
}

static void RecBmsFactSpecInfo(BYTE ucPort)
{
    T_BmsPACKFactoryStruct  tBmsPACKFactory;
	BYTE ucLenBuf[8] = {15, 15, 20, 20, 20, 20, 1, 2};
//	   T_SysPara  s_tSysPara;
    
    GetSysPara( &s_tSysPara );
    rt_memset((BYTE*)&tBmsPACKFactory,0x00,sizeof(tBmsPACKFactory));
    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[8];

    if(s_tProtocol.ucCommandType < 0x80 + sizeof(ucLenBuf) && s_tProtocol.ucCommandType >= 0x80)
    {
        if(ucLenBuf[s_tProtocol.ucCommandType - 0x80] != (s_tProtocol.wRecLenid / 2 - 2))
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
            return;
        }
    }
    else
    {
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
        return;
    }
    if (GPIO_OFF == GetPMOSStatus())
    {
        s_tProtocol.ucRTN = RTN_FAIL_COMMAND;
        return;
    }

    //190128先读取再设置
    readBmsPackFacInfo(&tBmsPACKFactory);

    switch (s_tProtocol.ucCommandType)
    {
        case 0x80:
			if(False == CheckSn(&s_tProtocol.aucRecBuf[9], sizeof(tBmsPACKFactory.acBmsFacSn)))
			{
				s_tProtocol.ucRTN  = RTN_INVALID_DATA;
				return;
			}
			rt_memcpy((BYTE *)&(tBmsPACKFactory.acBmsFacSn[0]), (BYTE *)(&s_tProtocol.aucRecBuf[9]), sizeof(tBmsPACKFactory.acBmsFacSn));
            break;
        case 0x81:
			if(False == CheckSn(&s_tProtocol.aucRecBuf[9], sizeof(tBmsPACKFactory.acDeviceSn)))
			{
				s_tProtocol.ucRTN  = RTN_INVALID_DATA;
				return;
			}
			rt_memcpy((BYTE *)&(tBmsPACKFactory.acDeviceSn[0]), (BYTE *)(&s_tProtocol.aucRecBuf[9]), sizeof(tBmsPACKFactory.acDeviceSn));
            break;
        case 0x82:
			if(False == CheckSn(&s_tProtocol.aucRecBuf[9], 20))
			{
				s_tProtocol.ucRTN  = RTN_INVALID_DATA;
				return;
			}
			rt_memcpy((BYTE *)tBmsPACKFactory.acPackBarCode[0], (BYTE *)(&s_tProtocol.aucRecBuf[9]), 20);
			break;
        case 0x86:
            if(!(SetBattSOH(s_tProtocol.aucRecBuf[9])))
            {
                s_tProtocol.ucRTN  = RTN_INVALID_DATA;
            }  	 	 
            return;
        case 0x87:
            s_tSysPara.wBatteryCap = (WORD)(Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+9)));
            if ( !(SetSysPara( &s_tSysPara, True, CHANGE_BY_YD1363 )) )
            {
                s_tProtocol.ucRTN = RTN_INVALID_DATA;
            }
            return;		 
        default:
            s_tProtocol.ucRTN  = RTN_WRONG_COMMAND;
            return;
    }
         
    tBmsPACKFactory.wCRC = CRC_Cal((BYTE*)&tBmsPACKFactory, (sizeof(tBmsPACKFactory)-2)); 
    writeBmsPackFacInfo(&tBmsPACKFactory);

    CheckBMSSnChange();

    return;
}


static void SendBmsDebugInfo(BYTE ucPort)
{
    BYTE *p = NULL;
    T_RotateDebugStruct tRotateDebug;
    T_ActivatePortDebugInfo tActivatePortDebugInfo;
    //T_SOCCalDebugStruct tSOCCalDebug;
    SHORT sTemp;

    GetRotateDebugInfo(&tRotateDebug);
    GetActivatePortDebugInfo(&tActivatePortDebugInfo);
    //GetSOCCalDebugInfo(&tSOCCalDebug);

    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

    *p++ = GetDataFlag( BCM_ALARM, ucPort );  //DATA_FLAG
    *p++ = BATT_NUM_BCM;//铁锂电池组数M

    PutInt32ToBuff(p, tRotateDebug.ulDisableFlag);
    p+=4;

    PutInt32ToBuff(p, tRotateDebug.ulDisableStat);
    p+=4;

    PutInt32ToBuff(p, tRotateDebug.ulCurrStat);
    p+=4;

    PutInt32ToBuff(p, tRotateDebug.ulCurrPrt);
    p+=4;

    *p++ = tRotateDebug.ucTransCounter;
    *p++ = tRotateDebug.ucInitStat;

    sTemp = FloatChangeToModbus(tRotateDebug.fBusMaxVol * 100) ;
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p+=2;
    sTemp = FloatChangeToModbus(tRotateDebug.fMaxCurrInAll * 100);
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p+=2;

    *p++ = tRotateDebug.ucMaxSocAddr;
    PutInt16ToBuff(p, tRotateDebug.wMaxSOC);
    p+=2;
    
    *p++ = tRotateDebug.ucMinSocAddr;
    PutInt16ToBuff(p, tRotateDebug.wMinSOC);
    p+=2;

    rt_memcpy(p, tRotateDebug.aucCtrlRotateLimitCurr, 32);
    p+=32;

    rt_memcpy(p, tRotateDebug.aucSlaveRotateCurr, 32);
    p+=32;

    *p++ = tActivatePortDebugInfo.bActivatePortCurrError;                          // 激活口回路电流异常标志（回路有充电电流）

    sTemp = FloatChangeToModbus(tActivatePortDebugInfo.fActivatePortVol * 100) ;   // 激活口电压
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p+=2;

    sTemp = FloatChangeToModbus(tActivatePortDebugInfo.fBusVol * 100) ;            // 母排电压
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p+=2;

    *p++ = tActivatePortDebugInfo.bActivatePortProtect;                            // 激活口相关保护
    *p++ = tActivatePortDebugInfo.ucActivateCounter;                               // 持续5秒激活
    *p++ = tActivatePortDebugInfo.bActivateStart;                                  // 是否开始激活
    *p++ = tActivatePortDebugInfo.ucActivateCurrCounter;                           // 电流持续5秒
    *p++ = tActivatePortDebugInfo.ucActivateVoltCounter;                           // 电压持续5秒

    PutInt16ToBuff(p, tActivatePortDebugInfo.wActivateNoCurrAndVoltCounter);       // 没有电压和电流持续5分钟
    p+=2;

    PutInt32ToBuff(p, tActivatePortDebugInfo.ulActivateTryCounter);                // 24小时重新尝试计时
    p+=4;

    *p++ = tActivatePortDebugInfo.bActivateTry;                                    // 是否24小时重新尝试一次

    /**p++ = tSOCCalDebug.ucSOCCalStatus;                               //SOC校正状态
    *p++ = tSOCCalDebug.ucBatStatus;                                  //电池状态
    *p++ = tSOCCalDebug.ucIfBduDischarge;                             //BDU是否放电
    *(SHORT*)p = FloatChangeToModbus(tSOCCalDebug.fBattCurr * 100);   //电池电流
    p+=2;
    *(SHORT*)p = FloatChangeToModbus(tSOCCalDebug.fCellTempMax * 10); //最高单体温度
    p+=2;
    *(SHORT*)p = FloatChangeToModbus(tSOCCalDebug.fBattVol * 100);    //电池电压
    p+=2;
    *(SHORT*)p = FloatChangeToModbus(tSOCCalDebug.fMinBatVol * 100);  //电池最小电压
    p+=2;
    *(SHORT*)p = FloatChangeToModbus(tSOCCalDebug.fMaxBatVol * 100);  //电池最大电压
    p+=2;
    *(SHORT*)p = FloatChangeToModbus(tSOCCalDebug.fBattCap * 100);    //电池当前实际AH容量
    p+=2;
    PutInt16ToBuff(p, tSOCCalDebug.wBattSOC);                         //电池SOC
    p+=2;
    PutInt32ToBuff(p, tSOCCalDebug.ulDischargeSOCCalTimer);           //计时处于放电SOC校正状态的时间
    p+=4;
    PutInt32ToBuff(p, tSOCCalDebug.ulDischargeBatVolDiffTimer);       //计时处于整组电压变化范围内的时间
    p+=4;*/

    /* LENID */
    s_tProtocol.wSendLenid  = GetLength((p-s_tProtocol.aucSendBuf)*2);
    return;
}


Static void SendBmsFactSpecInfo(BYTE ucPort)
{
    BYTE *p = NULL;
    T_BmsPACKFactoryStruct  tBmsPACKFactory;
    T_BCMDataStruct    tBCMAnaData;
    SHORT sTemp;
//	T_SysPara s_tSysPara;

    
    rt_memset(&tBCMAnaData, 0, sizeof(T_BCMDataStruct));
    rt_memset(&s_tSysPara, 0, sizeof(T_SysPara));       
    GetRealData( &tBCMAnaData );
    GetSysPara(&s_tSysPara);

    readBmsPackFacInfo(&tBmsPACKFactory);

    p   = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    *p++ = GetDataFlag( BCM_ALARM, ucPort );
    *p++ = BATT_NUM_BCM;//铁锂电池组数M

    MemsetBuff(p, tBmsPACKFactory.acBmsFacSn, 15, 15, 0x20);   //BMS序列号
    p   += 15;

    //*p++ = tBmsPACKFactory.ucCellManufactruer;
    MemsetBuff(p, tBmsPACKFactory.acDeviceSn, 15, 15, 0x20);   //整机序列号
    p   += 15;

    MemsetBuff(p, tBmsPACKFactory.acPackBarCode[0], 20, 20, 0x20);   //PACK_BAR_CODE
    p   += 20;

    MemsetBuff(p, tBmsPACKFactory.acPackBarCode[1], 20, 20, 0x20);   //PACK_BAR_CODE
    p   += 20;

    MemsetBuff(p, tBmsPACKFactory.acPackBarCode[2], 20, 20, 0x20);   //PACK_BAR_CODE
    p   += 20;

    MemsetBuff(p, tBmsPACKFactory.acPackBarCode[3], 20, 20, 0x20);   //PACK_BAR_CODE
    p   += 20;
    //add soh 0305
    *p++ = (BYTE)(tBCMAnaData.wBattSOH&0xff);
    sTemp  = Host2Modbus((SHORT*)&s_tSysPara.wBatteryCap);
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;

    /* LENID */
    s_tProtocol.wSendLenid  = GetLength((p-s_tProtocol.aucSendBuf)*2);
    return;
}

static void RegetBmsHisAlm( BYTE ucPort )
{
    ResetProtoHisAlarmPoint(NORTH_PROTOCOL_1363);

    s_tProtocol.ucRTN   = RTN_CORRECT;
    s_tProtocol.wSendLenid  = 0;

    return;
}

static void RegetBmsHisData( BYTE ucPort )
{
    ResetProtoHisDataPoint(NORTH_PROTOCOL_1363);

    s_tProtocol.ucRTN   = RTN_CORRECT;
    s_tProtocol.wSendLenid  = 0;

    return;
}

static void RegetBmsHisOper( BYTE ucPort )
{
    ResetBmsHisOperPoint(NORTH_PROTOCOL_1363);

    s_tProtocol.ucRTN   = RTN_CORRECT;
    s_tProtocol.wSendLenid  = 0;

    return;
}

#ifdef SHUTDOWNSLEEP
static void CtrlShutdown(BYTE ucPort)
{
    s_tProtocol.wSendLenid = 2;
    s_tProtocol.ucRTN = RTN_CORRECT;
    s_tProtocol.aucSendBuf[0] = 1;
    if(!IsSleep())
    {
        SetSleepShutdownFlag(0);
    }
    SetSleepShutdownFlag(2);
	return;
}

static void CtrlSleepStart(BYTE ucPort)
{
    if(!IsSleep())
    {
        SetSleepShutdownFlag(0);
        s_tProtocol.ucRTN = RTN_CORRECT;
        s_tProtocol.aucSendBuf[0] = 1;
    }
    else
    {
        s_tProtocol.ucRTN = RTN_FAIL_COMMAND;
    }
    s_tProtocol.wSendLenid  = 2;
    return;
}

static void CtrlSleepStop(BYTE ucPort)
{
    if(IsSleep())
    {
        SetSleepShutdownFlag(1);
        s_tProtocol.ucRTN = RTN_CORRECT;
        s_tProtocol.aucSendBuf[0] = 1;
    }
    else
    {
        s_tProtocol.ucRTN = RTN_FAIL_COMMAND;
    }
    s_tProtocol.wSendLenid  = 2;
    return;
}
#endif
/*static void ClearCalibPara(BYTE ucPort)
{
	T_CellVoltCalStruct tCellVoltCal;
	rt_memset(&tCellVoltCal, 0x00, sizeof(T_CellVoltCalStruct));
	WriteCellVoltCal(&tCellVoltCal);
	SetCellVoltCalibVal(&tCellVoltCal);
}*/

static void GetSleepTest(BYTE ucPort)
{
    BYTE *p = NULL;

    p  = s_tProtocol.aucSendBuf;
    *p++ = IsSleep();

    /* LENID */
    s_tProtocol.wSendLenid  = GetLength((p-s_tProtocol.aucSendBuf)*2);

    return;
}


static void GetCellFactInfo(BYTE ucPort)
{
    BYTE *p = NULL;
    WORD wSendLen = 100; //(20+4+4+22)*2=100
    T_BmsPACKManufactStruct tPackInfo;
    CHAR acPackAndCell[20] = {0};
    SHORT sSymbolPos = -1; // 电芯厂家与PACK厂家中符号'-'所在位置

    rt_memset_s(&s_tSysPara, sizeof(T_SysPara), 0, sizeof(T_SysPara));
    GetSysPara(&s_tSysPara );
    rt_memset_s(&tPackInfo, sizeof(T_BmsPACKManufactStruct), 0, sizeof(T_BmsPACKManufactStruct));
    readPackManufact(&tPackInfo);

    sSymbolPos = FindSympolPosition(tPackInfo.acCellManufactruer, sizeof(tPackInfo.acCellManufactruer));

    p = s_tProtocol.aucSendBuf;

    rt_memcpy_s((BYTE *)&(acPackAndCell[0]), sizeof(tPackInfo.acPackInfo), (BYTE *)(&tPackInfo.acPackInfo[0]), sizeof(tPackInfo.acPackInfo));   //PACK厂家信息
    rt_memcpy_s((BYTE *)&(acPackAndCell[sSymbolPos + 2]), 1, (BYTE *)(&tPackInfo.acCellManufactruer[sSymbolPos + 1]), 1);  //电芯厂家信息
    rt_memcpy_s((BYTE *)&(p[0]), sizeof(acPackAndCell), (BYTE *)(&acPackAndCell[0]), sizeof(acPackAndCell));
    p += 20;
    PutInt16ToBuff(p, (SHORT)(min(tPackInfo.tBattDate.wYear,INT16S_MAX))); //电芯出厂日期
    p += 2;
    *p++ = tPackInfo.tBattDate.ucMonth;
    *p++ = tPackInfo.tBattDate.ucDay;

    PutInt16ToBuff(p, (SHORT)(min(tPackInfo.tActiveDate.wYear,INT16S_MAX))); //电芯启用日期
    p += 2;
    *p++ = tPackInfo.tActiveDate.ucMonth;
    *p++ = tPackInfo.tActiveDate.ucDay;

	PutInt16ToBuff(p, (SHORT)(min(tPackInfo.wCellCycleTimes,INT16S_MAX))); //电芯循环次数
	p += 2;
    *p++ = s_tSysPara.ucCellType; // 电芯类型

    // 后面预留20字节
    p += 19;
    *p = 0x00; // KW4
    s_tProtocol.wSendLenid = wSendLen; // 为解决KW问题
}



static BOOLEAN ProtocolCommandTypeCheck(BYTE ucCmdType, BYTE ucCmdTypeStart, BYTE ucCmdTypeEnd)
{
    return (ucCmdType >= ucCmdTypeStart && ucCmdType <= ucCmdTypeEnd);
}


static void SetCellFactInfo(BYTE ucPort)
{
    T_BmsPACKManufactStruct	tPackInfo;
    SHORT wTemp;
    BYTE aucLenBuf[5] = {20, 4, 4, 2, 1};
    SHORT sSymbolPos = -1; // 电芯厂家与PACK厂家中符号'-'所在位置

    rt_memset((BYTE*)&tPackInfo,0x00,sizeof(T_BmsPACKManufactStruct));
    GetSysPara(&s_tSysPara);
    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[8];
    //if(s_tProtocol.ucCommandType < 0x80 + sizeof(aucLenBuf) && s_tProtocol.ucCommandType >= 0x80)
    if (ProtocolCommandTypeCheck(s_tProtocol.ucCommandType, 0x80, 0x7F + sizeof(aucLenBuf)))
    {
        if(aucLenBuf[s_tProtocol.ucCommandType - 0x80] != (s_tProtocol.wRecLenid / 2 - 2))
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
            return;
        }
    }
    if (GPIO_OFF == GetPMOSStatus())
    {
        s_tProtocol.ucRTN = RTN_FAIL_COMMAND;
        return;
    }

    //190128先读取再设置
    readPackManufact(&tPackInfo);

    if (s_tProtocol.ucCommandType == 0x80)
    {
        if (False == CheckPackAndCellFormat(&s_tProtocol.aucRecBuf[9], 20))
            {
                s_tProtocol.ucRTN = RTN_INVALID_DATA;
                return;
            }
        sSymbolPos = FindSympolPosition((char*)&s_tProtocol.aucRecBuf[9], LEN_TYPE_STRING_20);
        rt_memcpy((BYTE *)&(tPackInfo.acCellManufactruer[0]), (BYTE *)(&s_tProtocol.aucRecBuf[9]), sizeof(tPackInfo.acCellManufactruer));
        rt_memcpy((BYTE *)&(tPackInfo.acCellManufactruer[sSymbolPos + 1]), (BYTE *)(&s_tProtocol.aucRecBuf[9 + sSymbolPos + 2]), 1); //电芯厂家信息：ZTE-**中第二位字母
        rt_memset((BYTE *)&(tPackInfo.acCellManufactruer[sSymbolPos + 2]), 0, 1);

        rt_memcpy((BYTE *)&(tPackInfo.acPackInfo[0]), (BYTE *)(&s_tProtocol.aucRecBuf[9]), sizeof(tPackInfo.acPackInfo));
        rt_memcpy((BYTE *)&(tPackInfo.acPackInfo[sSymbolPos + 1]), (BYTE *)(&s_tProtocol.aucRecBuf[9 + sSymbolPos + 1]), 1); //PACK厂家信息：ZTE-**中第一位字母
        rt_memset((BYTE *)&(tPackInfo.acPackInfo[sSymbolPos + 2]), 0, 1);
    }
    else if (s_tProtocol.ucCommandType == 0x81)
    {
        wTemp = Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+9));
        tPackInfo.tBattDate.wYear = (WORD)wTemp;
        tPackInfo.tBattDate.ucMonth = s_tProtocol.aucRecBuf[11];
        tPackInfo.tBattDate.ucDay = s_tProtocol.aucRecBuf[12];
        if(!CheckDateValid(&tPackInfo.tBattDate))
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
            return;
        }
    }
    else if(s_tProtocol.ucCommandType == 0x82)
    {
        wTemp = Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+9));
      s_tSysPara.tEnableTime.wYear = (WORD)wTemp;
        s_tSysPara.tEnableTime.ucMonth = s_tProtocol.aucRecBuf[11];
        s_tSysPara.tEnableTime.ucDay = s_tProtocol.aucRecBuf[12];
    }
    else if(s_tProtocol.ucCommandType == 0x83)
    {
        tPackInfo.wCellCycleTimes = (WORD)(Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf + 9)));
        if(!CheckCellCycleTimesValid(tPackInfo.wCellCycleTimes))
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
            return;
        }
        SetCellCycleTimes(tPackInfo.wCellCycleTimes);
    }
    else if(s_tProtocol.ucCommandType == 0x84)
    {
        s_tSysPara.ucCellType = s_tProtocol.aucRecBuf[9];
        if(!(SetSysPara(&s_tSysPara, True, CHANGE_BY_YD1363)))
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
        }
        return;
    }
    else
    {
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
        return;
    }
    if (!(SetSysPara(&s_tSysPara, True, CHANGE_BY_YD1363)))
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
        return;
    }

    //修改启用日期参数后同步到电芯厂家信息中
    if (rt_memcmp(&s_tSysPara.tEnableTime, &tPackInfo.tActiveDate, sizeof(T_DateStruct)) != 0)
    {
        rt_memcpy(&tPackInfo.tActiveDate, &s_tSysPara.tEnableTime, sizeof(T_DateStruct));
    }
    tPackInfo.wCRC = CRC_Cal((BYTE*)&tPackInfo, (sizeof(tPackInfo) - 2));
    writePackManufact(&tPackInfo);
    return;
}

static void GetBmsAnalogNew(BYTE ucPort)
{
    WORD wSendLen = 114;//(1+1+ 1+1+1+ 2+2 +4+4 +40)*2 = 57*2 = 114
    BYTE *p = NULL;
    INT32 slTemp;
    SHORT sTemp;
    T_BattResult tBattResult;
    T_BCMDataStruct tBCMAnaData;
    T_Mg21Status tMg21Data;
    T_DCRealData tDcRealData;

    rt_memset(&tBCMAnaData, 0, sizeof(T_BCMDataStruct));
    rt_memset(&tMg21Data, 0, sizeof(T_Mg21Status));
    rt_memset(&tDcRealData, 0, sizeof(T_DCRealData));

    GetSysPara(&s_tSysPara);
    GetBattResult(&tBattResult);
    GetRealData( &tBCMAnaData );
    getMg21Status(&tMg21Data);
    GetBduReal(&tDcRealData);

    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    /* DATA_FLAG */
    *p++ = GetDataFlag( BCM_ALARM, ucPort );
    *p++ = BATT_NUM_BCM;//铁锂电池组数M

    *p++ = s_tSysPara.ucRunMode;//运行模式
    *p++ = s_tSysPara.ucUsageScen;//使用场景
    *p++ = tBattResult.ucInputBreak;//充电输入断
    sTemp = Host2Modbus((SHORT*)&tBattResult.wChargeLeftMinutes);    //充电剩余时间
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp = Host2Modbus((SHORT*)&tBattResult.wDisChargeLeftMinutes);    //充电剩余时间
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;

    slTemp = FloatChangeToInt32Modbus(tBattResult.fTotalDischargeCap*100);    //累计放电容量
    rt_memcpy(p, (BYTE*)&slTemp, sizeof(slTemp));
    p += 4;
    slTemp = FloatChangeToInt32Modbus(tBattResult.fTotalDischargeQuanty*100);    //累计放电电量
    rt_memcpy(p, (BYTE*)&slTemp, sizeof(slTemp));
    p += 4;

    *p = (BYTE)(tBattResult.wCellBalVoltBits & 0x00FF); // 电芯均衡状态，低八位，单体1-单体8 
    p++;
    *p = (BYTE)((tBattResult.wCellBalVoltBits & 0xFF00) >> 8); // 电芯均衡状态，高八位，单体9-单体16 
    p++;

	sTemp = FloatChangeToModbus(tBCMAnaData.fBattMaxCurrCap*100);  //充放电最大电流
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
	p += 2;

    //TODO:电芯阻抗补偿状态（R321无此值）
    //*p++ = GetCellCompStatus();
	p++;

    sTemp = FloatChangeToModbus(tBattResult.fPowerOnVolt*100);    //系统来电电压阈值
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp = FloatChangeToModbus(tBattResult.fPowerdownVolt*100);    //掉电电压阈值
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    *p++ = tBCMAnaData.ucAlarmStatus;
    *p++ = tBCMAnaData.ucThroughStatus;
		
    //新增GPS相关量，用于后续故障定位
	*p++ = tMg21Data.bSIMCardOk;             //1表示正常，0表示异常
	*p++ = tMg21Data.ucStarNum;              //卫星数量
    *p++ = tMg21Data.ucSignalQuality;        //4G信号强度
    *p++ = tMg21Data.ucMC20Stage;            //0空闲，1初始启动，2表示短信，3表示数据，4表示定位

    sTemp  = Host2Modbus((SHORT*)&tBattResult.wFullChargeLastDura);    //满充计时
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
	p += 2;

    *p++ = tBCMAnaData.ucPriceStatus;       //当前错峰电价状态

    slTemp = FloatChangeToInt32Modbus(tBattResult.fTotalChargeQuanty * 100);    //累计充电电量
    rt_memcpy(p, (BYTE*)&slTemp, sizeof(slTemp));
    p += 4;

    slTemp = FloatChangeToInt32Modbus(tBattResult.fTotalChargeCap * 100);       //累计充电容量
    rt_memcpy(p, (BYTE*)&slTemp, sizeof(slTemp));
    p += 4;

    sTemp  = FloatChangeToModbus(tBCMAnaData.fConnTemp*10);    //连接器温度
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;

    sTemp = FloatChangeToModbus(tBCMAnaData.fBalanceResisTemp*10);    //均衡电阻温度
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;

    sTemp = Host2Modbus((SHORT*)&tBattResult.wSetBusChgCurr);    //当前设定BUS侧充电限电流
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;

    *p++ = (rt_uint8_t)tBCMAnaData.wBattLeftEnergy;

    *p++ = tBCMAnaData.bChargeMachineTest;       //当前是否为充放电机模式
    *p++ = tMg21Data.ucSNR;   //载噪比

    s_tProtocol.wSendLenid = wSendLen;//为解决KW问题
}

static void GetBmsParaNew(BYTE ucPort)
{
    BYTE *p = NULL;
    WORD wSendLen = 94;//(1+1+ 1+4+40)*2 = 47*2 = 94
    SHORT sTemp;
    rt_memset(&s_tSysPara, 0, sizeof(T_SysPara));
    GetSysPara(&s_tSysPara);

    p   = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    *p++ = GetDataFlag( BCM_ALARM, ucPort );
    *p++ = BATT_NUM_BCM;//铁锂电池组数M

    *p++ = s_tSysPara.bMajorBatt;//主用电池
    PutInt16ToBuff(p, (SHORT)s_tSysPara.tEnableTime.wYear);//启用日期
    p +=2;
    *p++ = s_tSysPara.tEnableTime.ucMonth;
    *p++ = s_tSysPara.tEnableTime.ucDay;

    *p++ = s_tSysPara.bVibrationAlarmEn;//振动告警使能
    *p++ = s_tSysPara.bGyroscopeSensitivity;//陀螺仪灵敏度
    sTemp = Host2Modbus((SHORT *)&s_tSysPara.wHeartbeatCycle);//心跳周期
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    *p++ = s_tSysPara.bUVPTempCompensationEn;

    rt_memcpy(p, (BYTE*)s_tSysPara.acBMSSysName, 20);
    p += 20;

	*p++ = s_tSysPara.ucRelayDefaultStatus;  // 干接点默认状态
	*p++ = s_tSysPara.ucDischargeMode;       // 放电方式，R321删除该参数，上送跟随方式放电值。（经讨论兼容B3可设置）
	*p++ = s_tSysPara.ucSleepIndicator;      // 休眠指示灯
	*p++ = s_tSysPara.bThroughDischgEnable;      // 放电直通使能状态
    *p++ = 0;               // 删除节能功能
    sTemp  = FloatChangeToModbus(s_tSysPara.fEnvTempHighPrtThre);
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp  = FloatChangeToModbus(s_tSysPara.fEnvTempLowPrtThre);
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    *p++ = s_tSysPara.bRemoteSupplyEndDischgSwitchMode;                                //  远供末期放电切换方式
    *p++ = s_tSysPara.bRemoteSupplyEndDischgSwitchSOC;                                 //  远供末期放电切换SOC
    sTemp  = FloatChangeToModbus(s_tSysPara.fRemoteSupplyEndDischgSwitchVolt*100);//  远供末期放电切换电压(V)
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp = Host2Modbus((SHORT *)&s_tSysPara.wGPSAntiTheftDistance);//GPS防盗距离
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    *p = 0x00;    //KW4

    s_tProtocol.wSendLenid = wSendLen;//为解决KW问题
}

static BYTE SetBmsParaNew1(void) {
    WORD wTemp;
    T_BCMAlarmStruct    tBCMAlm;

    rt_memset(&tBCMAlm, 0, sizeof(T_BCMAlarmStruct)); 

    GetRealAlarm(BCM_ALARM, (BYTE *)&tBCMAlm);

    switch (s_tProtocol.ucCommandType){
        case 0x80:
            s_tSysPara.bMajorBatt = s_tProtocol.aucRecBuf[9];
            break;
        case 0x81:
            wTemp = Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+9));
            s_tSysPara.tEnableTime.wYear = (WORD)wTemp;
            s_tSysPara.tEnableTime.ucMonth = s_tProtocol.aucRecBuf[11];
            s_tSysPara.tEnableTime.ucDay = s_tProtocol.aucRecBuf[12];
            break;

        case 0x82:
            s_tSysPara.bVibrationAlarmEn = s_tProtocol.aucRecBuf[9];
            break;
        case 0x83:
            s_tSysPara.bGyroscopeSensitivity = s_tProtocol.aucRecBuf[9];
            break;
        case 0x84:
            wTemp = (WORD)Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9));
            s_tSysPara.wHeartbeatCycle = wTemp;
            break;
        case 0x85:
            s_tSysPara.bUVPTempCompensationEn = s_tProtocol.aucRecBuf[9];
            break;
        case 0x86:
            ParaStrCpy(s_tSysPara.acBMSSysName, (s_tProtocol.aucRecBuf + 9), sizeof(s_tSysPara.acBMSSysName));
            break;
        case 0x87:
            s_tSysPara.ucRelayDefaultStatus = s_tProtocol.aucRecBuf[9];
            break;
		case 0x88:     
            s_tSysPara.ucDischargeMode = s_tProtocol.aucRecBuf[9];
            break;
		case 0x89:
            s_tSysPara.ucSleepIndicator = s_tProtocol.aucRecBuf[9];
            break;
        default:
        return False;
    }
    return True;
}

static BYTE SetBmsParaNew2(void) {
    WORD wTemp;

    switch (s_tProtocol.ucCommandType){
        case 0x90:
            s_tSysPara.bThroughDischgEnable = s_tProtocol.aucRecBuf[9];
            break;
        case 0x92:
            s_tSysPara.fEnvTempHighPrtThre = Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9));
            break;
        case 0x93:
            s_tSysPara.fEnvTempLowPrtThre = Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9));
            break;
        case 0x94:
            s_tSysPara.bRemoteSupplyEndDischgSwitchMode = s_tProtocol.aucRecBuf[9];
            break;
        case 0x95:
            s_tSysPara.bRemoteSupplyEndDischgSwitchSOC = s_tProtocol.aucRecBuf[9];
            break;
        case 0x96:
            s_tSysPara.fRemoteSupplyEndDischgSwitchVolt = (FLOAT)Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9))/100.0;
            break;
        case 0x97:
            wTemp = (WORD)Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9));
            s_tSysPara.wGPSAntiTheftDistance = wTemp;
            break;
        default:
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;  /* 命令格式错 */
        return False;
    }
    return True;
}

static void SetBmsParaNewAll(void) {
    if (SetBmsParaNew1()) {
        return;
    }
    if (SetBmsParaNew2()) {
        return;
    }
    return;
}

static void SetBmsParaNew(BYTE ucPort)
{
	U_16Int tData;
	const BYTE aucJudgeType[] = {0x00,0x00};
    BYTE aucLenBuf[18] = {1, 4, 1, 1, 2, 1, 20, 1, 1, 1, 1, 1, 2, 2, 1, 1, 2, 2};
    T_BmsPACKManufactStruct	tPackInfo;
    T_BCMAlarmStruct    tBCMAlm;
    rt_memset(&tBCMAlm, 0, sizeof(T_BCMAlarmStruct)); 
    GetRealAlarm(BCM_ALARM, (BYTE *)&tBCMAlm);
    rt_memset((BYTE*)&tPackInfo,0x00,sizeof(T_BmsPACKManufactStruct));

    //190128先读取再设置
    readPackManufact(&tPackInfo);
    GetSysPara( &s_tSysPara );

    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[8];
	tData.ucByte[0] = s_tProtocol.aucRecBuf[10];
	tData.ucByte[1] = s_tProtocol.aucRecBuf[9];

    if(s_tProtocol.ucCommandType < 0x8A && s_tProtocol.ucCommandType >= 0x80)   
    {
        if(aucLenBuf[s_tProtocol.ucCommandType - 0x80] != (s_tProtocol.wRecLenid / 2 - 2))
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
            return;
        }
    }
    else if(s_tProtocol.ucCommandType < 0x98 && s_tProtocol.ucCommandType >= 0x90)   //COMMAND_TYPE不连续
    {
        if(aucLenBuf[s_tProtocol.ucCommandType - 0x90 + 10] != (s_tProtocol.wRecLenid / 2 - 2))
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
            return;
        }
    }
    else
    {
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
        return;
    }
    if (GPIO_OFF == GetPMOSStatus())
    {
        s_tProtocol.ucRTN = RTN_FAIL_COMMAND;
        return;
    }	

    //WORD型参数,不能下发负数 mwl 20201222
	if(tData.sData < 0)
	{
		BYTE i = 0;
		while(aucJudgeType[i] != 0x00)
		{
			if(aucJudgeType[i] == s_tProtocol.ucCommandType)
			{
				s_tProtocol.ucRTN   = RTN_INVALID_DATA; 
        		return;
			}
			i++;
		}
	}
	
    SetBmsParaNewAll();

    if (s_tProtocol.ucRTN == RTN_INVALID_DATA || s_tProtocol.ucRTN == RTN_WRONG_COMMAND) {
        return;
    }

    if ( !(SetSysPara( &s_tSysPara, True, CHANGE_BY_YD1363 )) )
    {
       s_tProtocol.ucRTN   = RTN_INVALID_DATA;
       return;
    }

    //修改启用日期参数后同步到电芯厂家信息中
    if (rt_memcmp(&s_tSysPara.tEnableTime, &tPackInfo.tActiveDate, sizeof(T_DateStruct)) != 0)
    {
        rt_memcpy(&tPackInfo.tActiveDate, &s_tSysPara.tEnableTime, sizeof(T_DateStruct));
        tPackInfo.wCRC = CRC_Cal((BYTE*)&tPackInfo, (sizeof(tPackInfo)-2));
        writePackManufact(&tPackInfo);
    }

    return;
}

static void SetBmsSpeParaNew(BYTE ucPort)
{

    WORD wTemp = 0;
	U_16Int tData;
    BYTE aucLenBuf[1] = {2};
    T_BmsPACKFactoryStruct	tPackInfo;
    GetSysPara( &s_tSysPara );

    rt_memset((BYTE*)&tPackInfo,0x00,sizeof(T_BmsPACKManufactStruct));
    readBmsPackFacInfo(&tPackInfo);
    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[8];

    if(s_tProtocol.ucCommandType < 0x80 + sizeof(aucLenBuf) && s_tProtocol.ucCommandType >= 0x80)
    {
        if(aucLenBuf[s_tProtocol.ucCommandType - 0x80] != (s_tProtocol.wRecLenid / 2 - 2))
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
            return;
        }
    }

    switch (s_tProtocol.ucCommandType)
    {
        case 0x80:
			tData.ucByte[0] = s_tProtocol.aucRecBuf[10];
			tData.ucByte[1] = s_tProtocol.aucRecBuf[9];
			if(tData.sData < 0)
			{
				s_tProtocol.ucRTN  = RTN_INVALID_DATA;
				return;
			}
            wTemp = Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+9));
            //调用Set循环次数接口，待提供接口后补充
            if (True != SetBattCycleTimes(wTemp))
            {
                s_tProtocol.ucRTN  = RTN_INVALID_DATA;
            }
            break;

        default:
            s_tProtocol.ucRTN  = RTN_WRONG_COMMAND;    /* 命令格式错 */
            return;
    }
    return;
}

Static void GetBmsAlarmNew(BYTE ucPort)
{
    BYTE *p = NULL;
    BYTE i = 0;
    BYTE ucCellVoltNum = 0,ucCellTempNum = 0;
    T_HardwareParaStruct tHWPara;
    T_BCMAlarmStruct    tBCMAlm;
    rt_memset(&tBCMAlm, 0, sizeof(T_BCMAlarmStruct)); 
    GetRealAlarm(BCM_ALARM, (BYTE *)&tBCMAlm);
    rt_memset(&tHWPara, 0, sizeof(T_HardwareParaStruct));
    readBmsHWPara(&tHWPara);
    ucCellVoltNum = tHWPara.ucCellVoltNum<CELL_VOL_NUM_MAX?tHWPara.ucCellVoltNum:CELL_VOL_NUM_MAX;
    ucCellTempNum = tHWPara.ucCellTempNum<CELL_TEMP_NUM_MAX?tHWPara.ucCellTempNum:CELL_TEMP_NUM_MAX;
    
    p   = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    /* DATA_FLAG */
    ClearDataFlag( BCM_ALARM, 0xEF, ucPort );    /* 开关量状态变化标志清0 */ 

    p += GetAlarm2Buff(p, ucPort);

    //new add alarm 20200818
    *p++ = tBCMAlm.ucBattVoltSampleAlm;
    *p++ = tBCMAlm.ucLoopFault;
    *p++ = tBCMAlm.ucBDUBattChgVoltLowPrt;
    *p++ = tBCMAlm.ucBDUBattLockAlm;
    *p++ = tBCMAlm.ucEnvTempHighPrt;
    *p++ = tBCMAlm.ucEnvTempLowPrt;
    *p++ = tBCMAlm.ucBoardTempHighAlm;
#ifdef SHIELD_BALANCE_CIRCUIT_OPENALM
    *p++ = 0;      // 均衡电路故障(删除该参数，默认上送0)
#else
    *p++ = tBCMAlm.ucEqualCircuitFaultAlm;      // 均衡电路故障
#endif
    *p++ = tBCMAlm.ucBalanceResisTempHighPrt;   // 均衡电阻温度高保护
    *p++ = tBCMAlm.ucHeaterFilmFailure;      // 加热膜失效
    *p++ = tBCMAlm.ucBDUConnTempHighPrt;        // 连接器温度高保护
    *p++ = tBCMAlm.ucMainRelayFail;      // 主继电器失效
    *p++ = tBCMAlm.ucDCDCErr;   // DCDC故障
    *p++ = tBCMAlm.ucSampleErr;      // 采集异常
    *p++ = tBCMAlm.ucAuxiSourceErr;        // 辅助源故障
    
    *p++ = ucCellVoltNum;//CELL_VOL_NUM;     //单体数量
    /*********单体动态欠压保护，数量1~16可变*********/
    for (i = 0; i < ucCellVoltNum; i++)
    {
        *p++ = tBCMAlm.aucCellDynamicUnderVoltPrt[i];
    }
    *p++ = tBCMAlm.ucFireControlAlm;    //消防告警
    *p++ = tBCMAlm.ucActivePortCurrError;//激活回路电流异常保护
    *p++ = tBCMAlm.ucCellTempRiseAbnormal;//单体温升速率异常
    *p++ = tBCMAlm.ucDcrFaultAlm;    //直流内阻异常告警
    *p++ = tBCMAlm.ucDcrFaultPrt;    //直流内阻异常保护
    *p++ = tBCMAlm.ucSelfDischFualt;//自放电异常告警
    *p++ = tBCMAlm.ucCapDCPRFaultAlm;//容量衰减一次性告警
    *p++ = tBCMAlm.ucBattFaultTempHighAlm; // 电池异常温度高保护告警
    *p++ = tBCMAlm.ucFireControlFaultAlm; // 消防电路故障告警
    *p++ = tBCMAlm.ucActivatePortReverseAlm; // 激活口反接告警
    if(((ucCellVoltNum - 15) * 6 + (ucCellTempNum - 4) * 6) < 0)
    {
        p += 0 - ((ucCellVoltNum - 15) * 6 + (ucCellTempNum - 4) * 6); //预留字节，为了与100B3兼容
    }
    /* LENID */
    s_tProtocol.wSendLenid  = GetLength((p-s_tProtocol.aucSendBuf)*2);
    return;
}

Static void GetBmsAlarmLVLNew(BYTE ucPort)
{
    BYTE *p = NULL;
    BYTE i;

    rt_memset(&s_tSysPara, 0, sizeof(T_SysPara));    
    GetSysPara(&s_tSysPara);
    
    p   = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    *p++ = GetDataFlag( BCM_ALARM, ucPort );
    *p++ = BATT_NUM_BCM;//铁锂电池组数M

    for (i = 0; i < ALARM_CLASS_PAST; i++)
    {
		*p++ = 0;   //高字节填充0        0828
		*p++ = s_tSysPara.aucAlarmLevel[i]; 
    }
    //p += 26; //预留字节
    s_tProtocol.wSendLenid = GetLength((p-s_tProtocol.aucSendBuf)*2);
    return;
}

Static void SetBmsAlarmLVLNew(BYTE ucPort)
{
    BYTE ucPara;
    BYTE ucIndex = 0;
    T_BCMAlarmStruct    tBCMAlm;
    rt_memset(&tBCMAlm, 0, sizeof(T_BCMAlarmStruct)); 
    GetRealAlarm(BCM_ALARM, (BYTE *)&tBCMAlm);
    GetSysPara( &s_tSysPara );

    s_tProtocol.ucCommandType   = s_tProtocol.aucRecBuf[8];

    //ucPara = s_tProtocol.aucRecBuf[9];    //0828修改,datainfo改为2字节，低字节有效
	ucPara = s_tProtocol.aucRecBuf[10];

    if (s_tProtocol.ucCommandType >= 0x80 && s_tProtocol.ucCommandType <= 0xC3)
    {
        ucIndex = s_tProtocol.ucCommandType - 0x80;
        s_tSysPara.aucAlarmLevel[ucIndex] = ucPara;
    }
    else
    {
        s_tProtocol.ucRTN  = RTN_WRONG_COMMAND;
        return;
    }
    if ( !(SetSysPara( &s_tSysPara, True, CHANGE_BY_YD1363 )) )
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;   
    }   
    return;
}

Static void GetBmsAlmRLYNew(BYTE ucPort)
{
    BYTE *p = NULL;
    BYTE i;

    rt_memset(&s_tSysPara, 0, sizeof(T_SysPara));
    GetSysPara(&s_tSysPara);

    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    *p++ = GetDataFlag(BCM_ALARM, ucPort);
    *p++ = BATT_NUM_BCM; //铁锂电池组数M

    for (i = 0; i < ALARM_CLASS_PAST; i++)
    {
    	*p++ = 0;   //高字节填充0        0828
        *p++ = s_tSysPara.aucRelayBit[i];
    }
    //p += 26; //预留字节
    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);

    return;
}

Static void SetBmsAlmRLYNew(BYTE ucPort)
{
    BYTE ucPara;
    BYTE ucIndex = 0;

    GetSysPara( &s_tSysPara );

    s_tProtocol.ucCommandType   = s_tProtocol.aucRecBuf[8];

    //ucPara = s_tProtocol.aucRecBuf[9];     //0828修改,datainfo改为2字节，低字节有效
	ucPara = s_tProtocol.aucRecBuf[10];

    if (s_tProtocol.ucCommandType >= 0x80 && s_tProtocol.ucCommandType <= 0xC3)
    {
		ucIndex = s_tProtocol.ucCommandType - 0x80;
		s_tSysPara.aucRelayBit[ucIndex] = ucPara;
	}
    else
	{
		s_tProtocol.ucRTN  = RTN_WRONG_COMMAND;
		return;
	}

    if ( !(SetSysPara( &s_tSysPara, True, CHANGE_BY_YD1363 )) )
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;   
    }   
    return;
}


static void GetBmsFactInfoNew(BYTE ucPort)
{
    T_BmsPACKFactoryStruct tBmsPACKFactory;
    BYTE *p = NULL;
    T_IMEI tIMEI;
    T_IMSI tIMSI;
    BYTE  FirstBootTimeLength = 0;
    BYTE  FactoryTimeLength = 0;
    T_NetMacStruct tNetMacPara;
    T_DCFactory tDcFactory;
    BYTE softwareTag[CUSTOMIZED_INFO_LEN] = {0, };
    T_SoftwareCustomizedInfo tSoftwareCustomizedInfo = {0, };

    p = s_tProtocol.aucSendBuf;
    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));
    rt_memset_s(&tIMEI, sizeof(tIMEI), 0, sizeof(tIMEI));
    rt_memset_s(&tIMSI, sizeof(tIMSI), 0, sizeof(tIMSI));
    rt_memset_s(&tBmsPACKFactory, sizeof(T_BmsPACKFactoryStruct), 0, sizeof(T_BmsPACKFactoryStruct));
    rt_memset_s(&tNetMacPara, sizeof(T_NetMacStruct), 0, sizeof(T_NetMacStruct));

    readBmsPackFacInfo(&tBmsPACKFactory);
    readNetMacPara(&tNetMacPara);
    GetBduFact(&tDcFactory);
    readSoftwareCustomizedInfo(&tSoftwareCustomizedInfo);
    GetSysPara(&s_tSysPara);
    GetIMEI(&tIMEI);
    GetIMSI(&tIMSI);
    GetSoftwareTagInfo(softwareTag, CUSTOMIZED_INFO_LEN);
    
    MemsetBuff(p, s_tSysPara.acBMSSysName, (BYTE)rt_strnlen_s((CHAR*)s_tSysPara.acBMSSysName, 20), (BYTE)20, 0x20); //系统名称
    p += 20;

    PutSoftwareVersionIntoBuff(&tSoftwareCustomizedInfo, p);
    p += 24;

    MemsetBuff(p, g_ProConfig.chBMSType, (BYTE)rt_strnlen_s(g_ProConfig.chBMSType, 20), (BYTE)20, 0x20); //BMS型号
    p += 20;
    ReadBootVerDate(p);
    p += 10;
    MemsetBuff(p, tIMEI.acIMEI, rt_strnlen_s(tIMEI.acIMEI, 20), 20, 0x20); // IMEI
    p += 20;

    FactoryTimeLength = PutTimeStruct2Buff(p, tBmsPACKFactory.tFactoryTime);         // 出厂时间
    p += FactoryTimeLength;

    FirstBootTimeLength = PutTimeStruct2Buff(p, tBmsPACKFactory.tFirstBootTime);       // 第一次开机时间
    p += FirstBootTimeLength;

    rt_memcpy(p, tNetMacPara.aucMacAddr, 6);
    p += 6;

    *p++ = tDcFactory.bHeatFilm;            //是否支持加热

    *p++ = tDcFactory.bActivatePort;        //是否支持激活口

    *p++ = CELL_VOL_NUM;                  //电芯串数

    *p++ = SMART_LI;                  //BMS类型

    *p++ = tDcFactory.acHardwareVer;        //硬件版本号

    MemsetBuff(p, tIMSI.acIMSI, rt_strnlen_s(tIMSI.acIMSI, 20), 20, 0x20); // IMSI
    p += 20;

    *p++ = tDcFactory.bParalleUpdate;            //功率是否支持并发升级

    MemsetBuff(p, softwareTag, CUSTOMIZED_INFO_LEN, CUSTOMIZED_INFO_LEN, 0x20); // 软件标识码
    p += 15;

    p += 23 - FactoryTimeLength - FirstBootTimeLength;//预留字节

    /* LENID */
    s_tProtocol.wSendLenid  = GetLength((p-s_tProtocol.aucSendBuf)*2);

    return;
}

static void GetBmsNewAlm(BYTE ucPort)
{
    BYTE *p = NULL;
    WORD wSendLen = 124;//62*2 = 124
    SHORT sTemp;
    rt_memset(&s_tSysPara, 0, sizeof(T_SysPara));
    GetSysPara(&s_tSysPara);

    p   = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    *p++ = GetDataFlag( BCM_ALARM, ucPort );
    *p++ = BATT_NUM_BCM;//铁锂电池组数M

    sTemp = FloatChangeToModbus(s_tSysPara.fBattOverVoltPrtRecoThre * 100);   //电池组过压保护恢复阈值
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p +=2;
    sTemp = FloatChangeToModbus(s_tSysPara.fBattOverVoltAlmRecoThre * 100);   //电池组过压告警恢复阈值
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p +=2;
    sTemp = FloatChangeToModbus(s_tSysPara.fCellOverVoltPrtRecoThre * 1000);   //单体过压保护恢复阈值
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p +=2;
    sTemp = FloatChangeToModbus(s_tSysPara.fCellOverVoltAlmRecoThre * 1000);   //单体过压告警恢复阈值
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p +=2;
    sTemp = FloatChangeToModbus(s_tSysPara.fCellUnderVoltPrtRecoThre * 1000);   //单体欠压保护恢复阈值
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p +=2;
    sTemp = FloatChangeToModbus(s_tSysPara.fCellUnderVoltAlmRecoThre * 1000);   //单体欠压告警恢复阈值
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p +=2;
    sTemp = FloatChangeToModbus(s_tSysPara.fChgTempHighPrtRecoThre * 10);   //充电温度高保护恢复阈值
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p +=2;
    sTemp = FloatChangeToModbus(s_tSysPara.fChgTempHighAlmRecoThre * 10);   //充电温度高告警恢复阈值
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p +=2;
    sTemp = FloatChangeToModbus(s_tSysPara.fDischgTempHighPrtRecoThre * 10);   //放电温度高保护恢复阈值
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p +=2;
    sTemp = FloatChangeToModbus(s_tSysPara.fDischgTempHighAlmRecoThre * 10);   //放电温度高告警恢复阈值
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p +=2;
    sTemp = FloatChangeToModbus(s_tSysPara.fChgTempLowPrtRecoThre * 10);   //充电温度低保护恢复阈值
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p +=2;
    sTemp = FloatChangeToModbus(s_tSysPara.fChgTempLowAlmRecoThre * 10);   //充电温度低告警恢复阈值
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p +=2;
    sTemp = FloatChangeToModbus(s_tSysPara.fDischgTempLowPrtRecoThre * 10);   //放电温度低保护恢复阈值
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p +=2;
    sTemp = FloatChangeToModbus(s_tSysPara.fDischgTempLowAlmRecoThre * 10);   //放电温度低告警恢复阈值
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p +=2;
    sTemp = FloatChangeToModbus(s_tSysPara.fEnvTempHighPrtRecoThre);   //环境温度高保护恢复阈值
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p +=2;
    sTemp = FloatChangeToModbus(s_tSysPara.fEnvTempHighAlmRecoThre);   //环境温度高告警恢复阈值
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p +=2;
    sTemp = FloatChangeToModbus(s_tSysPara.fEnvTempLowPrtRecoThre);   //环境温度低保护恢复阈值
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p +=2;
    sTemp = FloatChangeToModbus(s_tSysPara.fEnvTempLowAlmRecoThre);   //环境温度低告警恢复阈值
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p +=2;
    sTemp = FloatChangeToModbus(s_tSysPara.fBoardTempHighPrtRecoThre * 10);   //单板过温保护恢复阈值
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p +=2;
    sTemp = FloatChangeToModbus(s_tSysPara.fBoardTempHighAlmRecoThre * 10);   //单板过温告警恢复阈值
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p +=2;	
    *p++ = 0x00;	
    *p++ = s_tSysPara.ucGyroAntiTheftMode;
    *p++ = 0x00;
    *p++ = s_tSysPara.ucBattUnlockMode;
    *p++ = s_tSysPara.bChargeRotate;
    *p++ = s_tSysPara.ucHisDataType;
    sTemp = FloatChangeToModbus(s_tSysPara.fCellChargeFullVolt * 1000);
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p +=2;
    sTemp = Host2Modbus((SHORT *)&s_tSysPara.wBattShutdownTime);//静置关机时间
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    *p++ = s_tSysPara.bBusVoltActiveEn;//母排电压激活使能
    *p++ = s_tSysPara.ucModbusAddr; // Modbus基地址
    *p++ = s_tSysPara.ucDcrFaultAlmThre; // 直流内阻异常告警阈值
    *p++ = s_tSysPara.ucDcrFaultPrtThre; // 直流内阻异常保护阈值
    sTemp = FloatChangeToModbus(s_tSysPara.fBattFaultTempHighPrtThre); // 电池异常温度高保护阈值(℃)
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    *p++ = s_tSysPara.bRelaySyncEn;    // 干接点同步使能
    *p++ = s_tSysPara.ucGyroAntiTheftTime;    // 陀螺仪防盗延时
    *p++ = s_tSysPara.ucWaterIngrnessEn;    // 防水检测使能
    p += 1; // 预留字节
	*p = 0x00;    //KW4
    s_tProtocol.wSendLenid = wSendLen;//为解决KW问题
}

static BYTE SetBmsNewAlm1(FLOAT fPara) {
    switch (s_tProtocol.ucCommandType){
        case 0x80:
            s_tSysPara.fBattOverVoltPrtRecoThre = fPara / 100.0;
            break;
        case 0x81:
            s_tSysPara.fBattOverVoltAlmRecoThre = fPara / 100.0;
            break;
        case 0x82:
            s_tSysPara.fCellOverVoltPrtRecoThre = fPara / 1000.0;
            break;
        case 0x83:
            s_tSysPara.fCellOverVoltAlmRecoThre = fPara / 1000.0;
            break;
        case 0x84:
            s_tSysPara.fCellUnderVoltPrtRecoThre = fPara / 1000.0;
            break;
        case 0x85:
            s_tSysPara.fCellUnderVoltAlmRecoThre = fPara / 1000.0;
            break;
        case 0x86:
            s_tSysPara.fChgTempHighPrtRecoThre = fPara / 10.0;
            break;
        case 0x87:
            s_tSysPara.fChgTempHighAlmRecoThre = fPara / 10.0;
			break;
		case 0x88:
            s_tSysPara.fDischgTempHighPrtRecoThre = fPara / 10.0;
		    break;
		case 0x89:
            s_tSysPara.fDischgTempHighAlmRecoThre = fPara / 10.0;
			break;
		case 0x8A:
            s_tSysPara.fChgTempLowPrtRecoThre = fPara / 10.0;
			break;
        case 0x8B:
            s_tSysPara.fChgTempLowAlmRecoThre = fPara / 10.0;
            break;
		case 0x8C:
            s_tSysPara.fDischgTempLowPrtRecoThre = fPara / 10.0;
			break;
		case 0x8D:
            s_tSysPara.fDischgTempLowAlmRecoThre = fPara / 10.0;
			break;
		case 0x8E:
			s_tSysPara.fEnvTempHighPrtRecoThre = fPara;
			break;
		case 0x8F:
			s_tSysPara.fEnvTempHighAlmRecoThre = fPara;
			break;
        default:
        return False;
    }
    return True;
}

static BYTE SetBmsNewAlm2(FLOAT fPara) {
    WORD wTemp;
    T_BCMAlarmStruct    tBCMAlm;

    rt_memset(&tBCMAlm, 0, sizeof(T_BCMAlarmStruct)); 
    GetRealAlarm(BCM_ALARM, (BYTE *)&tBCMAlm);

    switch (s_tProtocol.ucCommandType){
        case 0x90:
			s_tSysPara.fEnvTempLowPrtRecoThre = fPara;
			break;
		case 0x91:
			s_tSysPara.fEnvTempLowAlmRecoThre = fPara;
			break;
		case 0x92:
			s_tSysPara.fBoardTempHighPrtRecoThre = fPara / 10.0;
			break;
		case 0x93:
			s_tSysPara.fBoardTempHighAlmRecoThre = fPara / 10.0;
			break;
        case 0x94:
            s_tSysPara.ucGyroAntiTheftMode = s_tProtocol.aucRecBuf[10];
            break;
        case 0x95:
            s_tSysPara.ucBattUnlockMode = s_tProtocol.aucRecBuf[10];
            break;
        case 0x96:
            s_tSysPara.bChargeRotate = s_tProtocol.aucRecBuf[9];
            break;
        case 0x97:
            s_tSysPara.ucHisDataType = s_tProtocol.aucRecBuf[9];
            break;
        case 0x98:
            s_tSysPara.fCellChargeFullVolt = fPara / 1000.0;
            break;
        case 0x99:
            wTemp = (WORD)Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9));
            s_tSysPara.wBattShutdownTime = wTemp;
            break;
        default:
            return False;
    }
    return True;
}

Static BYTE SetBmsNewAlm3(FLOAT fPara) {
    switch (s_tProtocol.ucCommandType){
        case 0x9A:
            s_tSysPara.bBusVoltActiveEn = s_tProtocol.aucRecBuf[9];
            break;
        case 0x9B:
            if(GetTotalAddr() + s_tProtocol.aucRecBuf[9] > 255)
            {//modbus通信地址不可大于255
                s_tProtocol.ucRTN   = RTN_INVALID_DATA;
                return True;
            }
            s_tSysPara.ucModbusAddr = s_tProtocol.aucRecBuf[9];
            break;
        case 0x9C:
            s_tSysPara.ucDcrFaultAlmThre = s_tProtocol.aucRecBuf[9];
            break;
        case 0x9D:
            s_tSysPara.ucDcrFaultPrtThre = s_tProtocol.aucRecBuf[9];
            break;
        case 0x9E:
            s_tSysPara.fBattFaultTempHighPrtThre = fPara;
            break;
        case 0x9F:
            s_tSysPara.bRelaySyncEn = s_tProtocol.aucRecBuf[9];
            break;
        case 0xA0:
            s_tSysPara.ucGyroAntiTheftTime = s_tProtocol.aucRecBuf[9];
            break;
        case 0xA1:
            s_tSysPara.ucWaterIngrnessEn = s_tProtocol.aucRecBuf[9];
            break; 
        default:
            s_tProtocol.ucRTN  = RTN_WRONG_COMMAND;    /* 命令格式错 */
            return False;
    }
    return True;
}

static void SetBmsNewAlmAll(FLOAT fPara) {
    if(SetBmsNewAlm1(fPara)) {
        return;
    }
    if(SetBmsNewAlm2(fPara)) {
        return;
    }
    if(SetBmsNewAlm3(fPara)) {
        return;
    }
    return;
}

static void SetBmsNewAlm(BYTE ucPort)
{
	U_16Int tData;
	const BYTE aucJudgeType[] = {0x00,0x00};
    
	FLOAT fPara;
    //190128先读取再设置
    GetSysPara( &s_tSysPara );

    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[8];
	fPara = (FLOAT)(Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf + 9)));
	tData.ucByte[0] = s_tProtocol.aucRecBuf[10];
	tData.ucByte[1] = s_tProtocol.aucRecBuf[9];
	//WORD型参数,不能下发负数 mwl 20201222
	if(s_tProtocol.ucCommandType < 0x94 && tData.sData < 0)
	{
		BYTE i = 0;
		while(aucJudgeType[i] != 0x00)
		{
			if(aucJudgeType[i] == s_tProtocol.ucCommandType)
			{
				s_tProtocol.ucRTN   = RTN_INVALID_DATA; 
        		return;
			}
			i++;
		}
	}
	
    SetBmsNewAlmAll(fPara);

    if (s_tProtocol.ucRTN == RTN_INVALID_DATA || s_tProtocol.ucRTN == RTN_WRONG_COMMAND) {
        return;
    }
    
    if ( !SetSysPara( &s_tSysPara, True, CHANGE_BY_YD1363 ) )
    {
       s_tProtocol.ucRTN   = RTN_INVALID_DATA;
    }
    return;
}

Static void GetBmsMaxSoc(BYTE ucPort)
{
    WORD wSendLen = 10;//(1+1+2+1)*2 = 10
    BYTE *p = NULL;
    SHORT sTemp;
    T_BattResult tBattResult;
    rt_memset(&tBattResult, 0, sizeof(T_BattResult));
    GetBattResult(&tBattResult);

    if(!IsMaster())
    {
        s_tProtocol.ucRTN   = RTN_INVALID_DATA; 
        return;
    }
    p  = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    /* DATA_FLAG */
    *p++ = GetDataFlag( BCM_ALARM, ucPort );
    *p++ = BATT_NUM_BCM;//铁锂电池组数M
    
    sTemp  = Host2Modbus((SHORT*)&tBattResult.wGroupMaxSoc);     //组内最大SOC
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    *p++ = 0;   //自定义字节数

    s_tProtocol.wSendLenid = wSendLen;//为解决KW问题
}

static void SendBmsStatusNew(BYTE ucPort)
{
    BYTE *p = NULL;
    BYTE *pSelfDefinedByte = NULL;
    T_BCMDataStruct tBCMAnaData;
    T_DCRealData tDcRealData;
    BYTE ucStatusCode = 0;

    p  = s_tProtocol.aucSendBuf;
    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));
    rt_memset_s(&tBCMAnaData, sizeof(T_BCMDataStruct), 0, sizeof(T_BCMDataStruct));
    rt_memset_s(&tDcRealData, sizeof(T_DCRealData), 0, sizeof(T_DCRealData));
    GetRealData(&tBCMAnaData);
    GetBduReal(&tDcRealData);

    /* DATA_FLAG */
    *p++ = GetDataFlag( BCM_ALARM, ucPort );
    *p++ = BATT_NUM_BCM;//铁锂电池组数M

    *p++ = GetDefenceStatus();  //布防状态

    *p++ = GetFireControlStatus();  //消防信号状态

    pSelfDefinedByte = p++;  //自定义字节数

    *p++ = (checkHeaterRealState())&0X01;//加热膜开启状态

    *p++ = GetFireControlFaultStatus();//消防故障状态

    *p++ = GetSiteAntiTheftStatus(); // 站点防盗布防状态(0:未布防，1:已布防)

    *p++ = (BYTE)get_snmp_comm_status();  // 网管连接状态（0：通信断；1：正常）

    // 新增 下发充电直通状态
    *p++ = tBCMAnaData.ucThroughChgStatus;
    // 新增 下发放电直通状态
    *p++ = tBCMAnaData.ucThroughDischgStatus;

    *p++ = tBCMAnaData.ucExternalPowerStatus;      //外部有电状态
    *p++ = tBCMAnaData.ucTransChagEn;              //转充电允许
    *p++ = tBCMAnaData.bContactorStatus;           //母排接触器状态
    *p++ = tBCMAnaData.bUpgradeEnable;             //升级使能状态

    DealFaultDiagnAlarm(p, &tDcRealData);
    p += 4;

    *p++ = 0;     //故障诊断告警5，预留
    *p++ = 0;     //故障诊断告警6，预留

    *p++ = tDcRealData.tDCStatus.bEMCtest;          //EMC测试状态

    ucStatusCode = tBCMAnaData.bSolarMode;
    *p++ = ucStatusCode;                            //状态码(后期增加状态，可以添加到此位置)

    *p++ = GetNetAntiTheftStatus(); // 网管防盗布防状态(0:未布防，1:已布防)


    *p++ = tBCMAnaData.bNetAntiKeyMatchStatus;  // 网管电子钥匙匹配状态(00H：不匹配 01H：匹配)


    *pSelfDefinedByte = (BYTE)(p - pSelfDefinedByte - 1);

    s_tProtocol.wSendLenid = GetLength((p-s_tProtocol.aucSendBuf) * 2);
}

/***************************************************************************
 * @brief   波特率切换
 **************************************************************************/
static void	RecSwapBaudrate(BYTE ucPort)
{
	BYTE *p = NULL;
    BYTE ucActId;
    CHAR buff[21] = {0};
    WORD wTemp=0;
    UINT32 boudMode = 0;
    BYTE handShakeCount = 0;

    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[8];
    
    s_tProtocol.ucRTN = RTN_CORRECT;
    switch (s_tProtocol.ucCommandType)
    {
        case SET_HIGH_BAUDRATE:
            wTemp = (WORD)(Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+9)));
            if(wTemp != 96 && wTemp != 1152 && wTemp != 576 && wTemp != 384 && wTemp != 192){
                s_tProtocol.ucRTN	=  RTN_WRONG_COMMAND;
                setHandShakeCounter(0);
                break;
            }
            handShakeCounterIncrement();
            handShakeCount = getHandShakeCounter();
            if(handShakeCount >= 3){
                ucActId = CONTOL_BAUDRATE_SWITCH;
                rt_snprintf_s(buff, sizeof(buff), "%d to %d", (UINT32)getBaudMode(), (UINT32)wTemp * 100);
                SaveAction(ucActId, buff);
                setBaudMode((UINT32)wTemp * 100);
            }
			boudMode = getBaudMode();
            wTemp = boudMode/100;

            p = s_tProtocol.aucSendBuf;
            rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));
            PutInt16ToBuff(p, wTemp);
            p += 2;
            s_tProtocol.wSendLenid = GetLength((p-s_tProtocol.aucSendBuf) * 2);
            break;
        default:
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
    }

    if((s_tProtocol.ucAddr==0x00) || (s_tProtocol.ucAddr==0x40))
    {
        s_tProtocol.ucRTN	=  NO_RETURN;
        //return;
    }

    return;
}

static void	RecHandShake( BYTE ucPort )
{
    BYTE *p = NULL;

    WORD wTemp=0;
    UINT32 ulTemp = 0;

    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[8];

    s_tProtocol.ucRTN	=  RTN_CORRECT;
    switch (s_tProtocol.ucCommandType)
    {
        case SET_HIGH_BAUDRATE:
            //handshake 3 times
            wTemp = (WORD)(Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+9)));
            if(wTemp != 96 && wTemp != 1152 && wTemp != 576 && wTemp != 384 && wTemp != 192){
                s_tProtocol.ucRTN	=  RTN_WRONG_COMMAND;
                break;
            }
            ulTemp = getBaudMode();
            wTemp = ulTemp / 100;

            p   = s_tProtocol.aucSendBuf;
            rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));
            PutInt16ToBuff(p, wTemp);
            p += 2;
            s_tProtocol.wSendLenid  = GetLength((p-s_tProtocol.aucSendBuf)*2);
            break;
        default:
            s_tProtocol.ucRTN	=  RTN_WRONG_COMMAND;
    }

    if((s_tProtocol.ucAddr==0x00) || (s_tProtocol.ucAddr==0x40))
    {
        s_tProtocol.ucRTN	=  NO_RETURN;
        //return;
    }

    return;
}

/***************************************************************************
 * @brief    读boot日期
 * @param    {BYTE} *p
 * @return   {*}
 **************************************************************************/
static void ReadBootVerDate(BYTE *p)
{
    rt_memcpy(p, (BYTE *)BOOT_VER_START, 10);
    return;
}

/*static void TriggerOutAssistTest(BYTE ucPort)
{
    s_tProtocol.aucSendBuf[0] = 0;
    s_tProtocol.wSendLenid  = 2;
	s_wOutTestTriggerTimeout = 0;
	s_ucOutTestTriggerRepeat++;

    if (!GetQtptestFlag())
    {
        if ( s_ucOutTestTriggerRepeat >= TRIGGER_COUNTER )
        {
			SetQtptestFlag(TRUE);
			ClearOutAssitSampleStatus();
			ClearChgAlmPrtRecond();
            //for(i = 0; i < 6; i++)
            //{
            //    tCtrlOut.bLed[i] = LEDON;
            //}
            //tCtrlOut.bBuzz = FALSE;
            //SetCtrlOut(&tCtrlOut);
            //SetDummyTimer(TIMER_APPTEST_LED, DELAY, (ONE_MINUTE*3), MSG_APPTEST_LED);
			s_tProtocol.aucSendBuf[0] = 1;    // 触发成功
		}
        return ;
    }
    else 
    {
        s_tProtocol.aucSendBuf[0] = 1;
    }
    return;
}

static void ExitOutAssistTest(BYTE ucPort)
{
	s_tProtocol.wSendLenid  = 0;
	s_wOutTestTriggerTimeout = 0;
	s_ucOutTestTriggerRepeat = 0;
	g_bOutAssistTestFlag = FALSE;
	SetValueFromProtocol(NPROTOCOL_TYPE_RS485_1363, INFO_TYPE_ALL, 0x00);
	SetValueFromProtocol(NPROTOCOL_TYPE_RS485_MODBUS, INFO_TYPE_ALL, 0x00);
	return;
}*/

/***************************************************************************
 * @brief    检查历史记录命令传入的command type
 * @return   True-无问题 False-有问题
 **************************************************************************/
Static BOOLEAN CheckHisCommandType(void)
{
    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[COMMAND_TYPE];

    if (s_tProtocol.ucCommandType == GET_RECORD_TIME_SLOT || s_tProtocol.ucCommandType == GET_RECORD_CORRECT ||
        s_tProtocol.ucCommandType == GET_RECORD_WRONG || s_tProtocol.ucCommandType == GET_RECORD_COMPLETE ||
        s_tProtocol.ucCommandType == GET_RECORD_NUM)
    {
        return True;
    }

    s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
    return False;
}


//遥控充电指令执行过程，解决圈复杂度
static VOID DealChargeCtrlCmd(BYTE bRunMode,BYTE bCtrlType,BYTE bCtrlStatus)
{
    if (bRunMode == s_tSysPara.ucRunMode)////only controlled mode, response the command.	Added by fengfj, 2021-07-29 20:50:41
    {
        SetValueFromProtocol(NPROTOCOL_TYPE_RS485_1363, bCtrlType, bCtrlStatus);
    }
    else
    {
        s_tProtocol.ucRTN	= RTN_FAIL_COMMAND;
    }

    return;
}

//遥控放电指令执行过程，解决圈复杂度
static VOID DealDischargeCtrlCmd(BYTE bRunMode,BYTE bCtrlType1,BYTE bCtrlType2,BYTE bCtrlStatus)
{
    WORD sTemp=0;

    if (bRunMode == s_tSysPara.ucRunMode)
    {
        SetValueFromProtocol(NPROTOCOL_TYPE_RS485_1363, bCtrlType1, bCtrlStatus);
        sTemp = Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+9));
        SetValueFromProtocol(NPROTOCOL_TYPE_RS485_1363, bCtrlType2, sTemp);
    }
    else
    {
        s_tProtocol.ucRTN	= RTN_FAIL_COMMAND;
    }

    return;
}

/***************************************************************************
 * @brief   获取新增参数（新指令）
 **************************************************************************/
static void GetBmsSpecialParaNew(BYTE ucPort)
{
    BYTE *p = NULL;
    SHORT sTemp = 0;
    BYTE i = 0;
    T_BattResult tBattResult;
    BYTE *pSelfDefinedByte = NULL;
    rt_memset(&s_tSysPara, 0, sizeof(T_SysPara));

    GetSysPara(&s_tSysPara);
    GetBattResult(&tBattResult);

    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    /* DATA_FLAG */
    *p++ = GetDataFlag( BCM_ALARM, ucPort );
    *p++ = BATT_NUM_BCM;//铁锂电池组数M

    sTemp = FloatChangeToModbus(s_tSysPara.fDischargeEndVolt1 * 100);   //末期放电电压1
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp = FloatChangeToModbus(s_tSysPara.fDischargeEndVolt2 * 100);   //末期放电电压2
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;

    sTemp  = Host2Modbus((SHORT*)&s_tSysPara.wPowerOnDetermineTime);    //来电判断时间
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;

    *p++ = s_tSysPara.bChargeMapEnable;                                 //充电map使能

    pSelfDefinedByte = p++;  //自定义字节数

    *p++ = s_tSysPara.bSagEqualCurr;                                    //下垂均流使能
    *p++ = s_tSysPara.bHeatingPadEnable;                                //加热垫使能

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fChgHeaterStartupTemp); // 充电加热膜启动温度(℃)
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fChgHeaterShutdownTemp); // 充电加热膜关闭温度(℃)
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fHeaterTempHighThre); // 加热膜过温阈值(℃)
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fHeaterTempHighRel); // 加热膜过温解除(℃)
    p += 2;

    *p++ = s_tSysPara.ucCurrBalanceAmplitude; // 均流SOC补偿幅值
    *p++ = s_tSysPara.ucCurrBalanceSlope; // 均流SOC补偿斜率
    *p++ = s_tSysPara.ucCurrBalanceMethod; // 均流方式

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fCellSupplVolt * 100); // 单体补充电电压(V)
    p += 2;

    *(SHORT *)p = Host2Modbus((SHORT*)&s_tSysPara.wPreRecordInterval);      //故障前录波时间间隔（us）
    p += 2;

    *(SHORT *)p = Host2Modbus((SHORT*)&s_tSysPara.wPostRecordInterval);     //故障后录波时间间隔（us）
    p += 2;
    *p++ = s_tSysPara.ucPreRecordNum;//故障前录波条数
    *p++ = s_tSysPara.ucPostRecordNum;//故障后录波条数
    *p++ = s_tSysPara.ucMeasurePointsNum;//测点数量X
    for(i = 0; i < s_tSysPara.ucMeasurePointsNum; i++)
    {
        *p++ = s_tSysPara.ucMeasurePointsID[i];//可变测点ID
    }
    *p++ = s_tSysPara.ucBattAddressMode;      // 电池地址获取方式
    *p++ = s_tSysPara.ucBattSwitchAddr;       // 电池切换地址

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fCellTempRiseAbnormalThre * 10); // 单体温升速率异常阈值
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fSelfDischgACR * 10); // 自放电容量比率
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fCapDCPRFaultAlmThre * 10); // 容量衰减一致性阈值
    p += 2;

    *(SHORT *)p = Host2Modbus((SHORT*)&s_tSysPara.wChargeMaxPower); // 充电最大功率
    p += 2;

    *(SHORT *)p = Host2Modbus((SHORT*)&s_tSysPara.wDischargeMaxPower); // 放电最大功率
    p += 2;

    *p++ = s_tSysPara.ucLocateMode;       // 定位方式

    *p++ = s_tSysPara.ucCAN2AddressMode;      // CAN2柜间地址获取方式

    *p++ = s_tSysPara.ucCAN2SwitchAddr;       // CAN2柜间切换地址

    *(SHORT *)p = Host2Modbus((SHORT *)&s_tSysPara.wSiteAntiTheftDelayTime); // 站点防盗延时时间
    p += 2;

    *(SHORT *)p = Host2Modbus((SHORT *)&s_tSysPara.wNetAntiTheftDelayTime); // 网管防盗延时时间
    p += 2;

    *p++ = s_tSysPara.ucCriticalAlarmlight;   //严重告警指示灯:0xA1

    *p++ = s_tSysPara.ucSecondaryAlarmlight;   //次要告警指示灯:0xA2

    *p++ = s_tSysPara.ucFireRelayControlEnable;   //消防干接点控制使能:0xA3

    *p++ = s_tSysPara.ucChargeMachineTestMode;   //充放电机模式:0xA4

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fDeyeFullVolt * 10); // 德业逆变器充满电压(V):0xA5
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fDeyeUnderVolt * 10); // 德业逆变器放电下限电压(V):0xA6
    p += 2;

    *(SHORT *)p = Host2Modbus((SHORT *)&s_tSysPara.wDischgSwitchSOC2); // 放电末期切换SOC2:0xA7
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fDischgHeaterStartupTemp); // 放电加热膜启动温度(℃)
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fDischgHeaterShutdownTemp); // 放电加热膜关闭温度(℃)
    p += 2;
    *p++ = s_tSysPara.ucAutoLocationRefreshPeriod;   //自动位置刷新周期(s):0xAA
    *p++ = s_tSysPara.ucNTCInvalidShieldNum;    // NTC无效屏蔽路数:0xAB

    *(SHORT *)p = FloatChangeToModbus(s_tSysPara.fSelfAdaptionInitVolt * 10); // 自适应模式初始电压(V):0xAC
    p += 2;

    *pSelfDefinedByte = (BYTE)(p - pSelfDefinedByte - 1);
    /* LENID */
    s_tProtocol.wSendLenid  = GetLength((p-s_tProtocol.aucSendBuf)*2);
}


Static BYTE SetBmsSpecialParaNewCase1_10()
{
    FLOAT fPara = 0.0;
    switch (s_tProtocol.ucCommandType)
    {
        case 0x80: //末期放电电压1
            fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9)));
            s_tSysPara.fDischargeEndVolt1 = fPara /100.0;
            break;

        case 0x81: //末期放电电压2
            fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9)));
            s_tSysPara.fDischargeEndVolt2 = fPara /100.0;
            break;

        case 0x82: //来电判断时间
            s_tSysPara.wPowerOnDetermineTime = Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9));
            break;

        case 0x83: //充电map使能
            s_tSysPara.bChargeMapEnable = s_tProtocol.aucRecBuf[9];
            break;

        case 0x84: //下垂均流使能
            s_tSysPara.bSagEqualCurr = s_tProtocol.aucRecBuf[9];
            break;

        case 0x85://加热垫使能
            s_tSysPara.bHeatingPadEnable = s_tProtocol.aucRecBuf[9];
            break;

        case 0x86: //充电加热膜启动温度(℃)
            s_tSysPara.fChgHeaterStartupTemp =  Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9));
            break;

        case 0x87: //充电加热膜关闭温度(℃)
            s_tSysPara.fChgHeaterShutdownTemp =  Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9));
            break;

        case 0x88: //加热膜过温阈值(℃)
            s_tSysPara.fHeaterTempHighThre =  Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9));
            break;

        case 0x89: //加热膜过温解除(℃)
            s_tSysPara.fHeaterTempHighRel =  Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9));
            break;

        default:
            return False;
    }
    return True;
}

Static BYTE SetBmsSpecialParaNewCase11_20()
{
    FLOAT fPara = 0.0;
    switch (s_tProtocol.ucCommandType)
    {
        case 0x8A: //均流SOC补偿幅值
            s_tSysPara.ucCurrBalanceAmplitude = s_tProtocol.aucRecBuf[9];
            break;

        case 0x8B: //均流SOC补偿斜率
            s_tSysPara.ucCurrBalanceSlope = s_tProtocol.aucRecBuf[9];
            break;
            
        case 0x8C: //均流方式
            s_tSysPara.ucCurrBalanceMethod = s_tProtocol.aucRecBuf[9];
            break;

        case 0x8D://单体补充电电压(V)
            fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9)));
            s_tSysPara.fCellSupplVolt = fPara /100.0;
            break;

        case 0x8E://故障前录波时间间隔(us)
            s_tSysPara.wPreRecordInterval = Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9));
            break;

        case 0x8F://故障后录波时间间隔(us)
            s_tSysPara.wPostRecordInterval = Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9));
            break;

        case 0x90: //故障前录波条数
            s_tSysPara.ucPreRecordNum = s_tProtocol.aucRecBuf[9];
            break;

        case 0x91: //故障后录波条数
            s_tSysPara.ucPostRecordNum = s_tProtocol.aucRecBuf[9];
            break;

        case 0x9D: //柜间地址获取方式
            s_tSysPara.ucCAN2AddressMode = s_tProtocol.aucRecBuf[9];
            break;

        case 0x9E: //柜间切换地址
            s_tSysPara.ucCAN2SwitchAddr = s_tProtocol.aucRecBuf[9];
            break;

        default:
            return False;
    }
    return True;
}

static BYTE SetBmsSpecialParaNewCase21_30()
{
    BYTE i = 0;
    FLOAT fPara = 0.0;
    switch (s_tProtocol.ucCommandType)
    {
        case 0x92: //测点数量X
            s_tSysPara.ucMeasurePointsNum = s_tProtocol.aucRecBuf[9];
            break;
        case 0x93://可变测点ID
            rt_memset(s_tSysPara.ucMeasurePointsID, 0x00, NUM_OF_MEASUREPOINTS);
            for(i = 0; i < s_tSysPara.ucMeasurePointsNum; i++)
            {
                s_tSysPara.ucMeasurePointsID[i] = s_tProtocol.aucRecBuf[9 + i];  // 可变测点ID
            }
            break;
        case 0x94: // 电池地址获取方式
            s_tSysPara.ucBattAddressMode = s_tProtocol.aucRecBuf[9];
            break;
        case 0x95: // 电池切换地址参数
            s_tSysPara.ucBattSwitchAddr = s_tProtocol.aucRecBuf[9];
            break;
        case 0x96: // 单体温升速率异常阈值
            fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9)));
            s_tSysPara.fCellTempRiseAbnormalThre = fPara /10.0;
            break;
        case 0x97: // 自放电容量比率
            fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9)));
            s_tSysPara.fSelfDischgACR = fPara /10.0;
            break;
		case 0x98: // 容量衰减一致性差告警比率阈值
            fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9)));
            s_tSysPara.fCapDCPRFaultAlmThre = fPara /10.0;
            break;
		case 0x99: // 最大充电功率(w)
            s_tSysPara.wChargeMaxPower = Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9));
            break;
        case 0x9A: // 最大放电功率(w)
            s_tSysPara.wDischargeMaxPower = Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9));
            break;
        case 0x9B: // 定位方式
            s_tSysPara.ucLocateMode = s_tProtocol.aucRecBuf[9];
            break;
        default:
            return False;
    }
    return True;
}



Static BYTE SetBmsSpecialParaNewCase31_40()
{
    FLOAT fPara = 0.0;

    switch (s_tProtocol.ucCommandType)
    {
        case 0x9C: // 站点防盗延时时间
            s_tSysPara.wSiteAntiTheftDelayTime = Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9));
            break;
        case 0x9F: // 网管防盗延时时间
            s_tSysPara.wNetAntiTheftDelayTime = Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9));
            break;
        case 0xA1: // 临界报警灯
            s_tSysPara.ucCriticalAlarmlight = s_tProtocol.aucRecBuf[9];
            break;
        case 0xA2: // 次要报警灯
            s_tSysPara.ucSecondaryAlarmlight = s_tProtocol.aucRecBuf[9];
            break;
        case 0xA3: // 火警继电器控制使能
            s_tSysPara.ucFireRelayControlEnable = s_tProtocol.aucRecBuf[9];
            break;
        case 0xA4: // 充放电机模式
            s_tSysPara.ucChargeMachineTestMode = s_tProtocol.aucRecBuf[9];
            break;
        case 0xA5: // 德业逆变器充满电压(V)
            fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9)));
            s_tSysPara.fDeyeFullVolt = fPara / 10.0;
            break;
        case 0xA6: // 德业逆变器放电下限电压(V)
            fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9)));
            s_tSysPara.fDeyeUnderVolt = fPara / 10.0;
            break;
        case 0xA7: // 放电末期切换SOC2
            s_tSysPara.wDischgSwitchSOC2 = Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9));
            break;
        case 0xA8: //放电加热膜启动温度(℃)
            s_tSysPara.fDischgHeaterStartupTemp =  Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9));
            break;
        case 0xA9: //放电加热膜关闭温度(℃)
            s_tSysPara.fDischgHeaterShutdownTemp =  Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9));
            break;
        case 0xAA: // 自动位置刷新周期
            s_tSysPara.ucAutoLocationRefreshPeriod = s_tProtocol.aucRecBuf[9];
            break;
        case 0xAB: // NTC无效屏蔽路数
            s_tSysPara.ucNTCInvalidShieldNum = s_tProtocol.aucRecBuf[9];
            break;
        case 0xAC: // 自适应模式初始电压
            fPara = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9)));
            s_tSysPara.fSelfAdaptionInitVolt = fPara/10.0;
            break;
        default:
            return False;
    }
    return True;
}



Static BYTE SetBmsSpecialParaNewCase()
{
    if(SetBmsSpecialParaNewCase1_10()) {
        return True;
    }
    if(SetBmsSpecialParaNewCase11_20()) {
        return True;
    }
    if(SetBmsSpecialParaNewCase21_30()) {
        return True;
    }
    if(SetBmsSpecialParaNewCase31_40()){
        return True;
    }
    return False;
}

/***************************************************************************
 * @brief   设置新增参数（新指令）
 **************************************************************************/
Static void SetBmsSpecialParaNew(BYTE ucPort)
{
    BYTE aucLenBuf[] = {2, 2, 2, 1, 1, 1, 2, 2, 2, 2, 1, 1, 1, 2, 2, 2, 1, 1, 1, 23, 1, 1, 2, 2, 2, 2, 2, 1, 2, 1, 1, 2, 0, 1, 1, 1, 1, 2, 2, 2, 2, 2, 1, 1,2}; 

    GetSysPara( &s_tSysPara );

    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[8];

    if(s_tProtocol.ucCommandType <= 0xAC && s_tProtocol.ucCommandType >= 0x80)
    {
        if(s_tProtocol.ucCommandType == 0x93)
        {
            if((s_tProtocol.wRecLenid / 2 - 2) != s_tSysPara.ucMeasurePointsNum)
            {
                s_tProtocol.ucRTN = RTN_INVALID_DATA;
                return;
            }
        }
        else if(aucLenBuf[s_tProtocol.ucCommandType - 0x80] != (s_tProtocol.wRecLenid / 2 - 2))
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
            return;
        }
    }
    else
    {
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
        return;
    }	

    SetBmsSpecialParaNewCase();

    if (!(SetSysPara( &s_tSysPara, True, CHANGE_BY_YD1363 )))
    {
       s_tProtocol.ucRTN   = RTN_INVALID_DATA;
       return;
    }

    return;
}





/***************************************************************************
 * @brief   获取内部测试参数（内部测试）
 **************************************************************************/
Static void GetBmsParaInternal(BYTE ucPort)
{
    BYTE *p = NULL;
    SHORT sTemp;

    p   = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    /* DATA_FLAG */
    *p++ = GetDataFlag( BCM_ALARM, ucPort );
    *p++ = BATT_NUM_BCM;//铁锂电池组数M

    *p++ = GetVoltTwoClassBreakRelayCounter();    //继电器断开次数
    WORD wStatsTime = GetVoltTwoClassStatsTime();
    sTemp  = Host2Modbus((SHORT*)&wStatsTime);     //二级电压保护统计时间
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;

    s_tProtocol.wSendLenid = GetLength((p-s_tProtocol.aucSendBuf)*2);
}

/***************************************************************************
 * @brief   设置内部测试参数（内部测试）
 **************************************************************************/
Static void SetBmsParaInternal(BYTE ucPort)
{
    FLOAT fPara = 0.0;
    BYTE aucLenBuf[2] = {1, 2};  //长度校验

    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[8];
    fPara = (FLOAT)(Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+9)));
    
    if(s_tProtocol.ucCommandType <= 0x81 && s_tProtocol.ucCommandType >= 0x80)   
    {
        if(aucLenBuf[s_tProtocol.ucCommandType - 0x80] != (s_tProtocol.wRecLenid / 2 - 2))
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
            return;
        }
    }
    else
    {
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
        return;
    }	

    switch (s_tProtocol.ucCommandType)
    {
        case 0x80: //继电器断开次数
            SetVoltTwoClassBreakRelayCounter(s_tProtocol.aucRecBuf[9]);
            break;
        case 0x81: //电压二级保护统计时间
            SetVoltTwoClassStatsTime((WORD)fPara);
            break;    

        default:
            s_tProtocol.ucRTN  = RTN_WRONG_COMMAND;    /* 命令格式错 */
            return;
    }

    return;
}

/***************************************************************************
 * @brief    获取功率内存地址信息（内部使用）
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
static void GetBduAddressInfo(BYTE ucPort)
{
    BYTE *p = NULL;
    T_DCMemQuery tDCMemQuery;
    U_32Int tAddress;

    rt_memset(&tDCMemQuery, 0x00, sizeof(T_DCMemQuery));

    tDCMemQuery.ucDataWidth = s_tProtocol.aucRecBuf[7]; 
    tAddress.ucData[0] = s_tProtocol.aucRecBuf[8];
    tAddress.ucData[1] = s_tProtocol.aucRecBuf[9];
    tAddress.ucData[2] = s_tProtocol.aucRecBuf[10];
    tAddress.ucData[3] = s_tProtocol.aucRecBuf[11];
    tDCMemQuery.ulMemAddr = (UINT32)Int32ValuetoModbus((INT32)tAddress.LData);

    NotifyBduMemQuery(&tDCMemQuery);

    rt_thread_delay(500);

    GetBduMem(&tDCMemQuery);
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    p = s_tProtocol.aucSendBuf;
    *(UINT32 *)p = (UINT32)Int32ValuetoModbus((INT32)tDCMemQuery.ulMemAddr);
    p += 4;
    *(UINT32 *)p = (UINT32)Int32ValuetoModbus((INT32)tDCMemQuery.ulMemValue);
    p += 4;
    s_tProtocol.ucRTN = RTN_CORRECT;
    s_tProtocol.wSendLenid = 16;
    return;
}

/***************************************************************************
 * @brief    设定功率内存地址信息内部使用）
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
static void SetBduAddressInfo(BYTE ucPort)
{
    T_DCMemQuery tDCMemQuery;
    U_32Int tAddress;
    U_32Int tValue;

    rt_memset(&tDCMemQuery, 0x00, sizeof(T_DCMemQuery));

    tDCMemQuery.ucDataWidth = s_tProtocol.aucRecBuf[7];
    
    tAddress.ucData[0] = s_tProtocol.aucRecBuf[8];
    tAddress.ucData[1] = s_tProtocol.aucRecBuf[9];
    tAddress.ucData[2] = s_tProtocol.aucRecBuf[10];
    tAddress.ucData[3] = s_tProtocol.aucRecBuf[11];
  
    tValue.ucData[0] = s_tProtocol.aucRecBuf[12];
    tValue.ucData[1] = s_tProtocol.aucRecBuf[13];
    tValue.ucData[2] = s_tProtocol.aucRecBuf[14];
    tValue.ucData[3] = s_tProtocol.aucRecBuf[15];

    tDCMemQuery.ulMemAddr = (UINT32)Int32ValuetoModbus((INT32)tAddress.LData);

    if ((tDCMemQuery.ulMemAddr >= 0x0000940D) && (tDCMemQuery.ulMemAddr <= 0x0000A000))
    {
        tDCMemQuery.ulMemAddr = (UINT32)Int32ValuetoModbus((INT32)tAddress.LData);
        tDCMemQuery.ulMemValue = (UINT32)Int32ValuetoModbus((INT32)tValue.LData);
        s_tProtocol.ucRTN = RTN_CORRECT;
    }
    else
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
    }
    SetBduMem(&tDCMemQuery);
    return;
}
/**************************************************************************
 * @brief   获取极值记录
 **************************************************************************/
Static void GetBmsExtremeValue(BYTE ucPort)
{
    //WORD wSendLen = 856;//(1+1+2*9+1+2*15*2+1+2*4*2+1)*2 = 99*2 = 198+658
    BYTE *p = NULL;
    BYTE ucTemp = 0;    //指针偏移
	T_HardwareParaStruct tHWPara = {0};
    BYTE ucCellVoltNum = 0;
    BYTE ucCellTempNum = 0;
    readBmsHWPara(&tHWPara);
    ReadHisExtremeData(&s_tHisExtremeData);
    ucCellVoltNum = tHWPara.ucCellVoltNum<CELL_VOL_NUM_MAX?tHWPara.ucCellVoltNum:CELL_VOL_NUM_MAX;
    ucCellTempNum = tHWPara.ucCellTempNum<CELL_TEMP_NUM_MAX?tHWPara.ucCellTempNum:CELL_TEMP_NUM_MAX;

    p  = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    /* COMMAND_TYPE */
    s_tProtocol.ucCommandType   = s_tProtocol.aucRecBuf[8];
    /* DATA_TYPE */
    if((0x00==s_tProtocol.ucCommandType) || (0x01==s_tProtocol.ucCommandType) ||(0x02==s_tProtocol.ucCommandType))
    {
        *p++ = 0x00;
    }
    else if(0x03==s_tProtocol.ucCommandType)
    {
        *p++ = 0x01;
    }
    else
    {
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
        return;
    }
  
    *p++ = BATT_NUM_BCM;//铁锂电池组数M

    switch (s_tProtocol.ucCommandType)
    {
        case 0x00:
            ucTemp = FillBattExtremeValue(p);    // 填充电池极值记录
            p += ucTemp;
            break;
        case 0x01:
            ucTemp = FillCellVoltMaxExtremeValue(p, ucCellVoltNum);     //填充单体电压最大值极值记录
            p += ucTemp;
            break;
        case 0x02:
            ucTemp = FillCellVoltMinExtremeValue(p, ucCellVoltNum);     //填充单体电压最小值极值记录
            p += ucTemp;
            break;
        case 0x03:
            ucTemp = FillCellTempMaxMinExtremeValue(p, ucCellTempNum);  //填充单体温度最大值最小值极值记录
            p += ucTemp;
            break;
    }

    /* LENID */
    s_tProtocol.wSendLenid = (WORD) ((p-s_tProtocol.aucSendBuf)*2);
    //s_tProtocol.wSendLenid = wSendLen;
    return;
}

Static BYTE FillBattExtremeValue(BYTE* p)
{
    SHORT sTemp;
    struct tm tTime;
    BYTE *pTemp = p; //保存指针起始地址
    BYTE ucTemp = 0; //指针偏移

    //电池电压最大值
    sTemp = FloatChangeToModbus(s_tHisExtremeData.fBattVoltMax*100);
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    //电池电压最大值记录时间
    localtime_r(&s_tHisExtremeData.BattVoltMaxTime, &tTime);
    ucTemp = PutTime2Buff(p, tTime);
    p += ucTemp;

    //电池电压最小值
    sTemp = FloatChangeToModbus(s_tHisExtremeData.fBattVoltMin*100);
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    //电池电压最小值记录时间
    localtime_r(&s_tHisExtremeData.BattVoltMinTime, &tTime);
    ucTemp = PutTime2Buff(p, tTime);
    p += ucTemp;

    //外部电压最大值
    sTemp = FloatChangeToModbus(s_tHisExtremeData.fExterVoltMax*100);
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    //外部电压最大值记录时间
    localtime_r(&s_tHisExtremeData.ExterVoltMaxTime, &tTime);
    ucTemp = PutTime2Buff(p, tTime);
    p += ucTemp;

    //环境温度最大值
    sTemp = FloatChangeToModbus(s_tHisExtremeData.fEnvTempMax*10);
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    //环境温度最大值记录时间
    localtime_r(&s_tHisExtremeData.EnvTempMaxTime, &tTime);
    ucTemp = PutTime2Buff(p, tTime);
    p += ucTemp;

    //环境温度最小值
    sTemp = FloatChangeToModbus(s_tHisExtremeData.fEnvTempMin*10);
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    //环境温度最小值记录时间
    localtime_r(&s_tHisExtremeData.EnvTempMinTime, &tTime);
    ucTemp = PutTime2Buff(p, tTime);
    p += ucTemp;

    //BUS充电电流最大值
    sTemp = FloatChangeToModbus(s_tHisExtremeData.fChargeBusCurrMax*100);
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    //BUS充电电流最大值记录时间
    localtime_r(&s_tHisExtremeData.ChargeBusCurrMaxTime, &tTime);
    ucTemp = PutTime2Buff(p, tTime);
    p += ucTemp;

    //BUS放电电流最大值
    sTemp = FloatChangeToModbus(s_tHisExtremeData.fDischargeBusCurrMax*100);
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    //BUS放电电流最大值记录时间
    localtime_r(&s_tHisExtremeData.DischargeBusCurrMaxTime, &tTime);
    ucTemp = PutTime2Buff(p, tTime);
    p += ucTemp;

    //电池充电电流最大值
    sTemp = FloatChangeToModbus(s_tHisExtremeData.fChargeBattCurrMax*100);
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    //电池充电电流最大值记录时间
    localtime_r(&s_tHisExtremeData.ChargeBattCurrMaxTime, &tTime);
    ucTemp = PutTime2Buff(p, tTime);
    p += ucTemp;

    //电池放电电流最大值
    sTemp = FloatChangeToModbus(s_tHisExtremeData.fDischargeBattCurrMax*100);
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    //电池放电电流最大值记录时间
    localtime_r(&s_tHisExtremeData.DischargeBattCurrMaxTime, &tTime);
    ucTemp = PutTime2Buff(p, tTime);
    p += ucTemp;

    return p-pTemp;
}

Static BYTE FillCellVoltMaxExtremeValue(BYTE* p, BYTE ucCellVoltNum)
{
    SHORT sTemp;
    struct tm tTime;
    BYTE i = 0;
    BYTE ucTemp = 0;    //指针偏移
    BYTE * pTemp = p;   //指针起始地址

    if(ucCellVoltNum > CELL_VOL_NUM_MAX)
    {
        ucCellVoltNum = CELL_VOL_NUM_MAX;
    }
    //单体电压数量
    *p++ = ucCellVoltNum;

    //单体电压最大值
    for(i=0; i<ucCellVoltNum; i++)
    {
        sTemp  = FloatChangeToModbus(s_tHisExtremeData.fCellVoltMax[i]*100);
        rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
        p += 2;
    }

    //单体电压最大值记录时间
    for(i=0; i<ucCellVoltNum; i++)
    {
        localtime_r(&s_tHisExtremeData.CellVoltMaxTime[i], &tTime);
        ucTemp = PutTime2Buff(p, tTime);
        p += ucTemp;
    }

    return p-pTemp;
}

static BYTE FillCellVoltMinExtremeValue(BYTE* p, BYTE ucCellVoltNum)
{
    SHORT sTemp;
    struct tm tTime;
    BYTE i = 0;
    BYTE ucTemp = 0;    //指针偏移
    BYTE * pTemp = p;   //指针起始地址

    //单体电压数量
    *p++ = ucCellVoltNum;

    //单体电压最小值
    for(i=0; i<ucCellVoltNum; i++)
    {
        sTemp  = FloatChangeToModbus(s_tHisExtremeData.fCellVoltMin[i]*100);
        rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
        p += 2;
    }

    //单体电压最小值记录时间
    for(i=0; i<ucCellVoltNum; i++)
    {
        localtime_r(&s_tHisExtremeData.CellVoltMinTime[i], &tTime);
        ucTemp = PutTime2Buff(p, tTime);
        p += ucTemp;
    }

    return p-pTemp;
}

static BYTE FillCellTempMaxMinExtremeValue(BYTE* p, BYTE ucCellTempNum)
{
    SHORT sTemp;
    struct tm tTime;
    BYTE i = 0;
    BYTE ucTemp = 0;    //指针偏移
    BYTE * pTemp = p;   //指针起始地址

    //单体温度数量
    *p++ = ucCellTempNum;

    //单体温度最大值
    for (i = 0; i<ucCellTempNum; i++)
    {
        sTemp  = FloatChangeToModbus(s_tHisExtremeData.fCellTempMax[i]*10);
        rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
        p += 2;
    }

    //单体温度最大值记录时间
    for (i = 0; i<ucCellTempNum; i++)
    {
        localtime_r(&s_tHisExtremeData.CellTempMaxTime[i], &tTime);
        ucTemp = PutTime2Buff(p, tTime);
        p += ucTemp;
    }

    //单体温度最小值
    for (i = 0; i<ucCellTempNum; i++)
    {
        sTemp  = FloatChangeToModbus(s_tHisExtremeData.fCellTempMin[i]*10);
        rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
        p += 2;
    }

    //单体温度最小值记录时间
    for (i = 0; i<ucCellTempNum; i++)
    {
        localtime_r(&s_tHisExtremeData.CellTempMinTime[i], &tTime);
        ucTemp = PutTime2Buff(p, tTime);
        p += ucTemp;
    }

    //自定义字节
    *p++ = 0;

    return p-pTemp;
}

Static void GetBmsStatisticsNew(BYTE ucPort)
{
    int i, j;
    BYTE *p = NULL;
    SHORT sTemp;
    T_AnalyseInfoStruct tAnalyseInfo;

    rt_memset(&tAnalyseInfo, 0, offsetof(T_AnalyseInfoStruct,wCheckSum)+2);
    GetAnalyticalAlm( &tAnalyseInfo );

    p   = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    *p++ = GetDataFlag( BCM_ALARM, ucPort );
    *p++ = BATT_NUM_BCM;//铁锂电池组数M

    for (i=0; i<CELL_TEMP_RANGE_NUM; i++)
    {
        for (j=0; j<SOC_RANGE_NUM; j++)
        {
            PutInt32ToBuff(p, tAnalyseInfo.aulBattCellTempandSOCRangeTimer[i][j]);
            p += 4;
        }
    }

    PutInt32ToBuff(p, tAnalyseInfo.ulBattUVPTimer);
    p += 4;
    PutInt32ToBuff(p, tAnalyseInfo.ulBattOVPTimer);
    p += 4;
    PutInt32ToBuff(p, tAnalyseInfo.ulBattOCPTimer);
    p += 4;
    PutInt32ToBuff(p, tAnalyseInfo.ulBattUVPShutDownTimer);
    p += 4;

    sTemp = Host2Modbus((SHORT *)&tAnalyseInfo.wBattUVPCounter);
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp = Host2Modbus((SHORT *)&tAnalyseInfo.wBattOVPCounter);
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp = Host2Modbus((SHORT *)&tAnalyseInfo.wBattOCPCounter);
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    sTemp = Host2Modbus((SHORT *)&tAnalyseInfo.wBattUVPShutDownCounter);
    rt_memcpy(p, (BYTE*)&sTemp, sizeof(sTemp));
    p += 2;

    /* LENID */
    s_tProtocol.wSendLenid  = GetLength((p-s_tProtocol.aucSendBuf)*2);
}

/***************************************************************************
 * @brief    获取网络参数信息（R311）
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
static void GetNetPara(BYTE ucPort)
{
    BYTE *p = NULL;
    struct netdev *pDev;
    #if !defined(KW_CHECK) && !defined(UNITEST)
    pDev = netdev_get_by_name("e0");
    #endif
    rt_memset_s(&s_tSysPara, sizeof(s_tSysPara), 0, sizeof(T_SysPara));

    GetSysPara(&s_tSysPara);

    p   = s_tProtocol.aucSendBuf;
    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));
    /* DATA_FLAG */
    *p++ = GetDataFlag( BCM_ALARM, ucPort );
    *p++ = BATT_NUM_BCM;//铁锂电池组数M

    rt_memcpy_s(p, LEN_TYPE_STRING_16, (BYTE*)s_tSysPara.acSNMPTrapIP, LEN_TYPE_STRING_16);        // SNMP告警ip地址
    p += LEN_TYPE_STRING_16;

    rt_memcpy_s(p, LEN_TYPE_STRING_16, (BYTE*)s_tSysPara.acSNMPReadCommunity, LEN_TYPE_STRING_16); // SNMP可读共同体
    p += LEN_TYPE_STRING_16;

    rt_memcpy_s(p, LEN_TYPE_STRING_16, (BYTE*)s_tSysPara.acSNMPSetCommunity, LEN_TYPE_STRING_16);  // SNMP设置共同体
    p += LEN_TYPE_STRING_16;

    *p++ = s_tSysPara.ucSNMPV3UserLevel;                             //SNMP V3用户等级

    rt_memcpy_s(p, LEN_TYPE_STRING_16, (BYTE*)s_tSysPara.acSNMPV3UserName, LEN_TYPE_STRING_16);    // SNMP V3用户名
    p += LEN_TYPE_STRING_16;

    rt_memcpy_s(p, LEN_TYPE_STRING_16, (BYTE*)s_tSysPara.acSNMPV3AuthPass, LEN_TYPE_STRING_16);    // SNMP V3鉴别PW
    p += LEN_TYPE_STRING_16;

    rt_memcpy_s(p, LEN_TYPE_STRING_16, (BYTE*)s_tSysPara.acSNMPV3PrivPass, LEN_TYPE_STRING_16);    // SNMP V3加密PW
    p += LEN_TYPE_STRING_16;

    *(SHORT *)p = Host2Modbus((SHORT*)&s_tSysPara.wSNMPTrapPort); //SNMP Trap端口号
    p += 2;

    *(SHORT *)p = Host2Modbus((SHORT*)&s_tSysPara.wSNMPAgentPort); //SNMP Agent端口号
    p += 2;

    if (s_tSysPara.ucLocalIPGetMode == STATIC_IP_MODE) // 静态ip地址时，从参数上传相关信息
    {
        rt_memcpy_s(p, LEN_TYPE_STRING_16, (BYTE*)&s_tSysPara.acLocalIPAddr[0], LEN_TYPE_STRING_16);   // 本机ip地址
        p += LEN_TYPE_STRING_16;

        rt_memcpy_s(p, LEN_TYPE_STRING_16, (BYTE*)&s_tSysPara.acMask[0], LEN_TYPE_STRING_16);          // 子网掩码
        p += LEN_TYPE_STRING_16;

        rt_memcpy_s(p, LEN_TYPE_STRING_16, (BYTE*)&s_tSysPara.acGateway[0], LEN_TYPE_STRING_16);       // 网关
        p += LEN_TYPE_STRING_16;
    }    
    else
    {
        if (pDev == RT_NULL) // 获取网卡信息前需要对指针判空处理
        {
            p += 16 * 3;
        }
        else
        {
            rt_memcpy_s(p, LEN_TYPE_STRING_16, inet_ntoa(pDev->ip_addr), rt_strnlen(inet_ntoa(pDev->ip_addr), 16)); // 本机IP地址
            p += 16;
            rt_memcpy_s(p, LEN_TYPE_STRING_16, inet_ntoa(pDev->netmask), rt_strnlen(inet_ntoa(pDev->netmask), 16)); // 子网掩码
            p += 16;
            rt_memcpy_s(p, LEN_TYPE_STRING_16, inet_ntoa(pDev->gw), rt_strnlen(inet_ntoa(pDev->gw), 16));           // 网关
            p += 16;
        }
    }

    *p++ = (BYTE)s_tSysPara.ucLocalIPGetMode;                          //本机ip获取方式

    rt_memcpy_s(p, LEN_TYPE_STRING_16, (BYTE*)&s_tSysPara.acRemoteUpdateIpAddr[0], LEN_TYPE_STRING_16);//远程升级后台ip地址
    p += LEN_TYPE_STRING_16;

    *(SHORT *)p = Host2Modbus((SHORT*)&s_tSysPara.wRemoteUpdatePort); //远程升级端口
    p += 2; 

    *p++ = s_tSysPara.ucSNMPVersion;                                  //SNMP V3用户等级

    *p++ = s_tSysPara.ucSNMPAuthenticationAlgo;                       //SNMP V3认证算法

    *p++ = s_tSysPara.ucSNMPPrivacyAlgo;                              //SNMP V3加密算法

    *p++ = 18; //自定义字节数

    rt_memcpy_s(p, LEN_TYPE_STRING_16, (BYTE*)&s_tSysPara.acNtpIPAddr[0], LEN_TYPE_STRING_16);   // NTP服务器ip地址
    p += LEN_TYPE_STRING_16;

    *p++ = s_tSysPara.ucTimeZone;                                     // 时区

    *p++ = s_tSysPara.bAutoTimingEn;                                  // 网络自动对时使能

    /* LENID */
    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
}


static BOOLEAN SetNetParaCase1_10()
{
    switch (s_tProtocol.ucCommandType)
    {
        case 0x80: //SNMP告警ip地址
            ParaStrCpy(s_tSysPara.acSNMPTrapIP, (s_tProtocol.aucRecBuf + 9), sizeof(s_tSysPara.acSNMPTrapIP));
            break;

        case 0x81: //SNMP可读共同体
            //ParaStrCpy(s_tSysPara.acSNMPReadCommunity, (s_tProtocol.aucRecBuf + 9), sizeof(s_tSysPara.acSNMPReadCommunity));//R321不需要支持设置
            break;

        case 0x82: //SNMP设置共同体
            //ParaStrCpy(s_tSysPara.acSNMPSetCommunity, (s_tProtocol.aucRecBuf + 9), sizeof(s_tSysPara.acSNMPSetCommunity));  //R321不需要支持设置
            break;

        case 0x83://SNMP V3用户等级
            s_tSysPara.ucSNMPV3UserLevel = s_tProtocol.aucRecBuf[9];
            break;

        case 0x84://SNMP V3用户名
            ParaStrCpy(s_tSysPara.acSNMPV3UserName, (s_tProtocol.aucRecBuf + 9), sizeof(s_tSysPara.acSNMPV3UserName));
            break;

        case 0x85://SNMP V3鉴别PW
            ParaStrCpy(s_tSysPara.acSNMPV3AuthPass, (s_tProtocol.aucRecBuf + 9), sizeof(s_tSysPara.acSNMPV3AuthPass));
            break;

        case 0x86://SNMP V3加密PW
            ParaStrCpy(s_tSysPara.acSNMPV3PrivPass, (s_tProtocol.aucRecBuf + 9), sizeof(s_tSysPara.acSNMPV3PrivPass));
            break;

        case 0x87: //SNMP Trap端口号
            s_tSysPara.wSNMPTrapPort = Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9));
            break;

        case 0x88: //SNMP Agent端口号
            s_tSysPara.wSNMPAgentPort = Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9));
            break;

        case 0x89: //本机ip地址
            if (s_tSysPara.ucLocalIPGetMode == DYNAMIC_IP_MODE) // 动态ip时不允许设置
            {
                s_tProtocol.ucRTN = RTN_INVALID_DATA;
                return False;
            }
            ParaStrCpy(s_tSysPara.acLocalIPAddr, (s_tProtocol.aucRecBuf + 9), sizeof(s_tSysPara.acLocalIPAddr));
            break;

        default:
            return False;
    }
    return True;
}

static BOOLEAN SetNetParaCase1_20()
{
    switch (s_tProtocol.ucCommandType)
    {
        case 0x8A: //子网掩码
            if (s_tSysPara.ucLocalIPGetMode == DYNAMIC_IP_MODE) // 动态ip时不允许设置
            {
                s_tProtocol.ucRTN = RTN_INVALID_DATA;
                return False;
            }
            ParaStrCpy(s_tSysPara.acMask, (s_tProtocol.aucRecBuf + 9), sizeof(s_tSysPara.acMask));
            break;
            
        case 0x8B: //网关
            if (s_tSysPara.ucLocalIPGetMode == DYNAMIC_IP_MODE) // 动态ip时不允许设置
            {
                s_tProtocol.ucRTN = RTN_INVALID_DATA;
                return False;
            }
            ParaStrCpy(s_tSysPara.acGateway, (s_tProtocol.aucRecBuf + 9), sizeof(s_tSysPara.acGateway));
            break;    
        case 0x8C: //本机ip获取方式
            s_tSysPara.ucLocalIPGetMode = s_tProtocol.aucRecBuf[9];
            break;  

        case 0x8D://远程升级后台ip地址
            ParaStrCpy(s_tSysPara.acRemoteUpdateIpAddr, (s_tProtocol.aucRecBuf + 9), sizeof(s_tSysPara.acRemoteUpdateIpAddr));
            break;

        case 0x8E://远程升级端口
            s_tSysPara.wRemoteUpdatePort = Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 9));
            break;

        case 0x8F://SNMP版本
            s_tSysPara.ucSNMPVersion = s_tProtocol.aucRecBuf[9];
            break;

        case 0x90://SNMP V3加密PW
            s_tSysPara.ucSNMPAuthenticationAlgo = s_tProtocol.aucRecBuf[9];
            break;

        case 0x91: //SNMP V3认证算法
            s_tSysPara.ucSNMPPrivacyAlgo = s_tProtocol.aucRecBuf[9];
            break;

        default:
            return False;
    }
    return True;
}


static BOOLEAN SetNetParaCase1_30()
{
    switch (s_tProtocol.ucCommandType)
    {
        case 0x92: // NTP服务器IP
            ParaStrCpy(s_tSysPara.acNtpIPAddr, (s_tProtocol.aucRecBuf + 9), sizeof(s_tSysPara.acNtpIPAddr));
            break;
        case 0x93: // 时区
            s_tSysPara.ucTimeZone = s_tProtocol.aucRecBuf[9];
            break;    
        case 0x94: // 网络自动对时使能
            s_tSysPara.bAutoTimingEn = s_tProtocol.aucRecBuf[9];
            break;
        default:
            return False;
    }
    return True;
}

Static BOOLEAN SetNetParaCase()
{
    if(SetNetParaCase1_10()) {
        return True;
    }
    if(SetNetParaCase1_20()) {
        return True;
    }
    if(SetNetParaCase1_30()) {
        return True;
    }
    return False;
}

/***************************************************************************
 * @brief   设置新增参数（新指令）
 **************************************************************************/
Static void SetNetPara(BYTE ucPort)
{
    BYTE aucLenBuf[] = {16,16,16,1,16,16,16,2,2,16,
                        16,16,1,16,2,1,1,1,16,1,1};

    GetSysPara( &s_tSysPara );

    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[8];

    if(s_tProtocol.ucCommandType <= 0x94 && s_tProtocol.ucCommandType >= 0x80)   
    {
        if(aucLenBuf[s_tProtocol.ucCommandType - 0x80] != (s_tProtocol.wRecLenid / 2 - 2))
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
            return;
        }
    }
    else
    {
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
        return;
    }	

    SetNetParaCase();

    if (!(SetSysPara( &s_tSysPara, True, CHANGE_BY_YD1363 )))
    {
       s_tProtocol.ucRTN   = RTN_INVALID_DATA;
       return;
    }

    return;
}

Static BYTE  ManualCtrlEnterDefenceStatus(void)
{
    int value = IsDefenseCondition();//布防条件
    switch(value)
    {
        case RTN_SUCCESS_DEFENCE:
            s_tProtocol.ucRTN  = RTN_SUCCESS_DEFENCE;
            break;
        case RTN_FAIL_DEFENCE_WIRE_LEVEL_PARA:
            s_tProtocol.ucRTN  = RTN_FAIL_DEFENCE_WIRE_LEVEL_PARA;
            break;
        case RTN_FAIL_DEFENCE_LEVEL:
            s_tProtocol.ucRTN  = RTN_FAIL_DEFENCE_LEVEL;
            break;
        case RTN_FAIL_DEFENCE_WIRE:
            s_tProtocol.ucRTN  = RTN_FAIL_DEFENCE_WIRE;
            break;
        case RTN_FAIL_DEFENCE_PARA:
            s_tProtocol.ucRTN  = RTN_FAIL_DEFENCE_PARA;
            break;
        case RTN_FAIL_COMMAND:
            s_tProtocol.ucRTN  = RTN_FAIL_COMMAND;
            break;
        case RTN_SATISFY_DEFENCE:
            // 布防成功并保存布防状态
            SaveAction(GetActionId(CONTOL_MANUAL_ENTER_DEFENCE), "1363EnterDefence");
            WriteDefenceStatus(True);
            s_tProtocol.ucRTN = RTN_SUCCESS_DEFENCE;
            break;
        default:
            return FAILURE;
    }
        return SUCCESSFUL;
}

static void GetCustomerName(BYTE ucPort)
{
    BYTE *p = NULL;
    T_BmsCustomerNameStruct tBmsCustomerName;

    rt_memset_s(&tBmsCustomerName, sizeof(T_BmsCustomerNameStruct), 0, sizeof(T_BmsCustomerNameStruct));

    readBmsCustomerName(&tBmsCustomerName);

    p  = s_tProtocol.aucSendBuf;
    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));

    MemsetBuff(p, &tBmsCustomerName.acBmsCustomerName[0], 17, 17, 0x20);
    //MemsetBuff将最后一个字节赋值为0，最后一个字节保持原先的不变
    rt_memcpy_s(p + 16, 1, &tBmsCustomerName.acBmsCustomerName[0] + 16, 1);

    p += 17;

    s_tProtocol.wSendLenid = GetLength((p-s_tProtocol.aucSendBuf)*2);
}

/***************************************************************************
 * @brief    人工防盗解锁和撤防
 **************************************************************************/
Static BOOLEAN ManualAntitheftUnlock(BYTE bType)
{
    T_BmsPACKFactoryStruct tBmsPackFacInfo;
    T_BmsCustomerNameStruct tBmsCustomerName;
    T_UnlockRecordStruct t_UnlockRecord;
    CHAR acLocalSeralNumber[33] = {0};
    WORD wCRCHighLow=0;
    BYTE bCRCHigh = 0;
    BYTE bCRCLow = 0;
    CHAR buff[21]={0};

    rt_memset_s(&tBmsPackFacInfo, sizeof(T_BmsPACKFactoryStruct), 0, sizeof(T_BmsPACKFactoryStruct));
    rt_memset_s(&tBmsCustomerName, sizeof(T_BmsCustomerNameStruct), 0, sizeof(T_BmsCustomerNameStruct));
    rt_memset_s(&t_UnlockRecord, sizeof(T_UnlockRecordStruct), 0, sizeof(T_UnlockRecordStruct));

    readBmsPackFacInfo(&tBmsPackFacInfo);
    readBmsCustomerName(&tBmsCustomerName);
    rt_snprintf_s(&acLocalSeralNumber[0], sizeof(acLocalSeralNumber),"%s%s",&tBmsPackFacInfo.acBmsFacSn[0],&tBmsCustomerName.acBmsCustomerName[0]);
    //将客户名称后的一个字节置为结束符
    rt_memset_s(acLocalSeralNumber + rt_strnlen_s(&tBmsPackFacInfo.acBmsFacSn[0], sizeof(tBmsPackFacInfo.acBmsFacSn)) + 17, 1, 0, 1);
    wCRCHighLow = UnlockCodeCreate(acLocalSeralNumber,rt_strnlen_s(acLocalSeralNumber, 33));
    bCRCHigh = ((0xff00 & wCRCHighLow)>>8);
    bCRCLow = ((0x00ff & wCRCHighLow));
    if(s_tProtocol.aucRecBuf[9]  != bCRCHigh || s_tProtocol.aucRecBuf[10] != bCRCLow)
    {
        s_tProtocol.ucRTN   = RTN_WRONG_COMMAND;
        return False;
    }
    if(GPIO_OFF == GetPMOSStatus())
    {
        s_tProtocol.ucRTN   = RTN_FAIL_COMMAND;
        return False;
    }
    rt_memcpy_s(t_UnlockRecord.ucUnlockAccount, sizeof(t_UnlockRecord.ucUnlockAccount), (BYTE *)(&s_tProtocol.aucRecBuf[13]), sizeof(t_UnlockRecord.ucUnlockAccount));
    rt_snprintf_s(buff, sizeof(buff),"%s", t_UnlockRecord.ucUnlockAccount);
    t_UnlockRecord.ucUnlockType = s_tProtocol.aucRecBuf[11];
    switch (bType) {
        case DEVICE_UNLOCK:
            DealDeviceUnlock( DEVICE_UNLOCK_MANUAL);
            if(t_UnlockRecord.ucUnlockType == 0)
            {
                SaveAction(GetActionId(CONTOL_UPPER_COMPUTER_UNLOCK_ID),buff);
            }
            else
            {
                SaveAction(GetActionId(CONTOL_NETWORK_MANAGE_UNLOCK_ID),buff);
            }
            break;
        case DEVICE_CANCEL_DEFENCE:
            if(GetDefenceStatus() == True) {
                WriteDefenceStatus(False);
            }
            if(t_UnlockRecord.ucUnlockType == 0)
            {
                SaveAction(GetActionId(CONTOL_UPPER_CANCEL_DEVICE_DEFENCE), buff);
            }
            else
            {
                SaveAction(GetActionId(CONTOL_NETWORK_CANCEL_DEVICE_DEFENCE),buff);
            }
            break;
        default:
            break;
    }
    return True;
}

Static void	RecCtrlNew( BYTE ucPort )
{
    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[8];
    s_tProtocol.ucRTN	=  RTN_CORRECT;

    switch (s_tProtocol.ucCommandType)
    {
        case SET_BATT_MANUAL_UNLOCK: // 人工解锁防盗
            ManualAntitheftUnlock(DEVICE_UNLOCK);
            break;
        case MANUAL_CANCEL_DEVICE_DEFENCE: // 人工撤防
            ManualAntitheftUnlock(DEVICE_CANCEL_DEFENCE);
            break;
        default:
            s_tProtocol.ucRTN	=  RTN_WRONG_COMMAND;
    }

    if((s_tProtocol.ucAddr==0x00) || (s_tProtocol.ucAddr==0x40))
    {
        s_tProtocol.ucRTN	=  NO_RETURN;
    }

    return;
}

Static void SendDateTempData( BYTE ucPort )
{
    BYTE *p = NULL;
    rt_memset_s(&s_tTemplateInfo, sizeof(T_TemplateInfo), 0x00, sizeof(T_TemplateInfo));
    rt_memset_s(&s_tPeakShiftPara, sizeof(T_PeakShiftPara), 0x00, sizeof(T_PeakShiftPara));

    GetPeakShiftPara(&s_tPeakShiftPara);
    GetPeakShiftTemplate(&s_tTemplateInfo);

    s_tProtocol.ucCommandType   = s_tProtocol.aucRecBuf[8];
    if (s_tProtocol.ucCommandType != 0 && s_tProtocol.ucCommandType != 1)
    {
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
        return;
    }
    if(s_tProtocol.ucCommandType == 0x00 && s_ucGetTemplateNum != 0)
    {
        s_ucGetTemplateNum = 0;
    }
    if(s_ucGetTemplateNum == 0 && s_tProtocol.ucCommandType != 0x00)
    {
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
        return;
    }
    if( s_tTemplateInfo.ucDayTemplateNum == 0 )
    {
        s_tProtocol.ucRTN   = RTN_NO_DATA;
        return;
    }
    p = s_tProtocol.aucSendBuf;
    if ( s_tTemplateInfo.ucDayTemplateNum - 1 == s_ucGetTemplateNum )  /* COMMAND_TYPE */
    {
        *p++    = 0x01;     /* 发送最后一条数据 */
    }
    else
    {
        *p++    = 0x00;     /* 正常发送下一条数据 */
    }
    *p++ = BATT_NUM_BCM;//铁锂电池组数M;
    if(!SetTempToProtocol(&s_tTemplateInfo.uTemplateInfo.tWeekTemp.atDateTemp[s_ucGetTemplateNum], p, s_tTemplateInfo.uTemplateInfo.tWeekTemp.atDateTemp[s_ucGetTemplateNum].ucDurationNum))
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
        return;
    }
    p += 1 + s_tTemplateInfo.uTemplateInfo.tWeekTemp.atDateTemp[s_ucGetTemplateNum].ucDurationNum * 5;

    s_ucGetTemplateNum++;
    if(s_ucGetTemplateNum >= s_tTemplateInfo.ucDayTemplateNum)
    {
        s_ucGetTemplateNum = 0;
    }
    s_tProtocol.wSendLenid  = GetLength((p-s_tProtocol.aucSendBuf)*2);    
    return;
}

Static void RecDateTempData( BYTE ucPort )
{
    T_PeakShiftPara	tPeakShiftPara;
    BYTE ucTempNum = 0;
    BOOLEAN bLastTemp = False;
    BYTE ucTimeDurNum = 0;
    
    rt_memset_s(&tPeakShiftPara, sizeof(T_PeakShiftPara), 0x00, sizeof(T_PeakShiftPara));
    GetPeakShiftPara(&tPeakShiftPara);

    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[8];
    bLastTemp = s_tProtocol.ucCommandType >> 7;
    ucTempNum = (s_tProtocol.ucCommandType & 0x7F) - 1;   //当前日模板序号
    ucTimeDurNum = s_tProtocol.aucRecBuf[9];  //时间段数量
    if(ucTempNum == 0)
    {
        s_ucSetTemplateNum = 0;
        rt_memset_s(&s_tTemplateStruct, sizeof(T_TemplateInfo), 0x00, sizeof(T_TemplateInfo));
    }
    if(ucTimeDurNum * 5 !=  (s_tProtocol.wRecLenid / 2 - 3) || (s_tProtocol.wRecLenid / 2 - 3) < 5)
    {
        s_tProtocol.ucRTN   = RTN_INVALID_DATA;
        return;
    }
    if(s_ucSetTemplateNum != ucTempNum)
    {
        s_ucSetTemplateNum = 0x00;
        s_tProtocol.ucRTN   = RTN_WRONG_COMMAND;
        return;
    }
    if(ucTempNum == 0x07 && !bLastTemp) //第八个模板也是最后一个模板
    {
        s_tProtocol.ucRTN   = RTN_WRONG_COMMAND;
        return;
    }
    if(ucTempNum <= 7)
    {
        if(!GetTempFromProtocol(&s_tTemplateStruct.uTemplateInfo.tMonthTemp.atDateTemp[ucTempNum], &s_tProtocol.aucRecBuf[9], ucTimeDurNum))
        {
            s_tProtocol.ucRTN  = RTN_INVALID_DATA; 
            return;
        }
    }
    else
    {
        s_tProtocol.ucRTN  = RTN_WRONG_COMMAND;  
        return;
    }
    if (GPIO_OFF == GetPMOSStatus())
    {
        s_tProtocol.ucRTN = RTN_FAIL_COMMAND;
        return;
    }

    s_ucSetTemplateNum++;
    if(s_ucSetTemplateNum == 8)
    {
        s_ucSetTemplateNum = 0;
    }
    if(bLastTemp)
    {
        s_ucSetTemplateNum = 0x00;
        s_tTemplateStruct.ucDayTemplateNum = ucTempNum + 1;
        if(!SetPeakShiftTemplate(&s_tTemplateStruct))
        {
            s_tProtocol.ucRTN  = RTN_INVALID_DATA; 
        }
    }
    return;
}

Static void SendPeakShiftPara(BYTE ucPort)
{
    BYTE *p = NULL;
    T_PeakShiftPara	tPeakShiftPara;
    rt_memset_s(&tPeakShiftPara, sizeof(T_PeakShiftPara), 0x00, sizeof(T_PeakShiftPara));
    GetPeakShiftPara(&tPeakShiftPara);

    p   = s_tProtocol.aucSendBuf;
    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));
    // *p++ = GetDataFlag( BCM_ALARM, ucPort );
    *p++ = BATT_NUM_BCM;//铁锂电池组数M
    *p++ = tPeakShiftPara.bPeakShift;   //智能错峰
    *p++ = tPeakShiftPara.ucPeakShiftDOD;  //错峰放电深度
    *p++ = tPeakShiftPara.ucElectricPricePattern; //电价模式
    *p++ = tPeakShiftPara.ucDayPatterTempNum;      //日模式对应模板序号
    rt_memcpy_s(p, 7, (BYTE*)tPeakShiftPara.aucWeekPatternTempNum, 7); //周模式对应模板序号
    p += 7;
    rt_memcpy_s(p, 31, (BYTE*)tPeakShiftPara.aucMonthPatternTempNum, 31); //月模式对应模板序号
    p += 31;
    *p++ = tPeakShiftPara.ucHolidayPatternTempNum;
    rt_memcpy_s(p, sizeof(tPeakShiftPara.atHolidayPatternDate), (BYTE*)tPeakShiftPara.atHolidayPatternDate, sizeof(tPeakShiftPara.atHolidayPatternDate));
    p += sizeof(tPeakShiftPara.atHolidayPatternDate);
    *p++ = tPeakShiftPara.ucPoweroffPeakDelay;
    p += 19;
    *p = 0x00;
    s_tProtocol.wSendLenid  = GetLength((p-s_tProtocol.aucSendBuf)*2);

}

Static void RecPeakShiftPara(BYTE ucPort)
{
    BYTE aucLenBuf[9] = {1, 1, 1, 1, 7, 31, 1, 60, 1};
    T_PeakShiftPara	tPeakShiftPara;
    rt_memset_s(&tPeakShiftPara, sizeof(T_PeakShiftPara), 0x00, sizeof(T_PeakShiftPara));

    GetPeakShiftPara(&tPeakShiftPara);

    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[8];

    if(s_tProtocol.ucCommandType < 0x80 + sizeof(aucLenBuf) && s_tProtocol.ucCommandType >= 0x80)   
    {
        if(aucLenBuf[s_tProtocol.ucCommandType - 0x80] != (s_tProtocol.wRecLenid / 2 - 2))
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
            return;
        }
    }
    else
    {
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
        return;
    }	
	if (GPIO_OFF == GetPMOSStatus())
    {
        s_tProtocol.ucRTN = RTN_FAIL_COMMAND;
        return;
    }

    switch (s_tProtocol.ucCommandType)
    {
        case 0x80:
            tPeakShiftPara.bPeakShift = s_tProtocol.aucRecBuf[9];
            break;
        case 0x81:
            tPeakShiftPara.ucPeakShiftDOD = s_tProtocol.aucRecBuf[9];
            break;
        case 0x82:
            tPeakShiftPara.ucElectricPricePattern = s_tProtocol.aucRecBuf[9];
            break;
        case 0x83:
            tPeakShiftPara.ucDayPatterTempNum = s_tProtocol.aucRecBuf[9];
            break;
        case 0x84:
            rt_memcpy_s((BYTE*)tPeakShiftPara.aucWeekPatternTempNum, sizeof(tPeakShiftPara.aucWeekPatternTempNum), (BYTE *)(&s_tProtocol.aucRecBuf[9]), sizeof(tPeakShiftPara.aucWeekPatternTempNum));
            break;
        case 0x85:
            rt_memcpy_s((BYTE*)tPeakShiftPara.aucMonthPatternTempNum, sizeof(tPeakShiftPara.aucMonthPatternTempNum), (BYTE *)(&s_tProtocol.aucRecBuf[9]), sizeof(tPeakShiftPara.aucMonthPatternTempNum));
            break;
        case 0x86:
            tPeakShiftPara.ucHolidayPatternTempNum = s_tProtocol.aucRecBuf[9];
            break;
        case 0x87:
            rt_memcpy_s((BYTE*)tPeakShiftPara.atHolidayPatternDate, sizeof(tPeakShiftPara.atHolidayPatternDate), (BYTE *)(&s_tProtocol.aucRecBuf[9]), sizeof(tPeakShiftPara.atHolidayPatternDate));
            break;
        case 0x88:
            tPeakShiftPara.ucPoweroffPeakDelay = s_tProtocol.aucRecBuf[9];
            break;
        default:
            s_tProtocol.ucRTN  = RTN_WRONG_COMMAND;    /* 命令格式错 */
            return;
    }
    if(!SetPeakShiftPara(&tPeakShiftPara, s_tProtocol.ucCommandType))
    {
        s_tProtocol.ucRTN  = RTN_INVALID_DATA;
    }
    return;
}

Static BOOLEAN SetTempToProtocol(T_DateTemplateStruct* ptDateTemp, BYTE* p, BYTE ucNum)
{
    BYTE i;
    if(p == NULL || ptDateTemp == NULL || ucNum > TEMPLATE_DURATION_MAX)
    {
        return False;
    }
    *p++ = ptDateTemp->ucDurationNum;
    for(i = 0; i < ucNum; i++)
    {
        *p++ = ptDateTemp->atDura[i].FromHour;
        *p++ = ptDateTemp->atDura[i].FromMinute;
        *p++ = ptDateTemp->atDura[i].ToHour;
        *p++ = ptDateTemp->atDura[i].ToMinute;
        *p++ = ptDateTemp->atDura[i].PriceClass;
    }
    return True;
}

Static BOOLEAN GetTempFromProtocol(T_DateTemplateStruct* ptDateTemp, BYTE* p, BYTE ucNum)
{
    BYTE i;
    if(p == NULL || ptDateTemp == NULL || ucNum > TEMPLATE_DURATION_MAX)
    {
        return False;
    }
    ptDateTemp->ucDurationNum = p[0];
    for(i = 0; i < ucNum; i++)
    {
        if(p[1 + i * 5] > 23 || p[2 + i * 5] > 59 || p[3 + i * 5] > 23 || p[4 + i * 5] > 59)
        {
            return False;
        }
        ptDateTemp->atDura[i].FromHour = p[1 + i * 5];
        ptDateTemp->atDura[i].FromMinute = p[2 + i * 5];
        ptDateTemp->atDura[i].ToHour = p[3 + i * 5];
        ptDateTemp->atDura[i].ToMinute = p[4 + i * 5];
        ptDateTemp->atDura[i].PriceClass = p[5 + i * 5];
    }
    return True;
}

//解决圈复杂度
static BOOLEAN GetBalanceNum (WORD wAlarmNum,BYTE ucPort)
{
	if (wAlarmNum == 0 || LastBalCapRecordJudge())
    {
        ClearDataFlag(BCM_ALARM, 0xFE, ucPort);
        s_tProtocol.ucRTN = RTN_NO_DATA;
        return True;
    }

     if(s_tProtocol.ucCommandType == GET_RECORD_CORRECT || s_tProtocol.ucCommandType == GET_RECORD_WRONG || s_tProtocol.ucCommandType == GET_RECORD_COMPLETE) {
        if (s_tProtocol.wRecLenid != HIS_RECORD_LEN_TYPE_ID)
        {
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
            return True;
        }
    }

    return False;
}

/***************************************************************************
 * @brief   获取均衡容量历史记录
 **************************************************************************/
static void SendBalanceCapacityRecord(BYTE ucPort)
{
    BYTE *p;
    WORD alarmNum;
    static BYTE s_ucGetNum = 0;
    static BYTE s_ucCommandID = 0;
    static WORD s_wLastTop = 0xFFFF;
    static WORD s_wStartIndex = 0 , s_wEndIndex = 0;
    BYTE ucCommandID = 0;

    if ( !CheckHisCommandType() )
    {
        return;
    }

    /* 获取特定时间段内的历史数据条数 */
    if(s_tProtocol.ucCommandType == GET_RECORD_NUM)
    {
        alarmNum = GetBalanceTimeRecordNum();

        p = s_tProtocol.aucSendBuf;
        rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

        /* 历史数据条数4字节 */
        *p++ = 0;
        *p++ = 0;
        *p++ = (alarmNum >> 8) & 0XFF;
        *p++ = alarmNum & 0XFF;
        s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
        return;
    }

    ucCommandID = s_tProtocol.aucRecBuf[COMMAND_ID];
     /* 获取特定时间段内的历史数据，根据起始与终止时间，得到最终的readpoint */
    if(s_tProtocol.ucCommandType == GET_RECORD_TIME_SLOT)
    {
        s_wStartIndex = 0;
        s_wEndIndex = GetBalanceTimeRecordNum() - 1;
        SetBalTimeHisDataPoint(s_wStartIndex);
    }

    alarmNum = s_wEndIndex - GetProtoBalanceCapacityPoint(NORTH_PROTOCOL_1363) + 1;

    // if (alarmNum == 0 || LastBalCapRecordJudge())
    // {
    //     ClearDataFlag(BCM_ALARM, 0xFE, ucPort);
    //     s_tProtocol.ucRTN = RTN_NO_DATA;
    //     return;
    // }

    //  if(s_tProtocol.ucCommandType == GET_RECORD_CORRECT || s_tProtocol.ucCommandType == GET_RECORD_WRONG || s_tProtocol.ucCommandType == GET_RECORD_COMPLETE) {
    //     if (s_tProtocol.wRecLenid != HIS_RECORD_LEN_TYPE_ID)
    //     {
    //         s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
    //         return;
    //     }
    // }

    if(GetBalanceNum(alarmNum,ucPort) == True)
    {
        return;
    }

     /* 发送第一条历史数据时不会进入以下判断，第二次发送开始进入 */
    if (s_tProtocol.ucCommandType == GET_RECORD_CORRECT  || (s_tProtocol.ucCommandType == GET_RECORD_WRONG && ((s_ucCommandID + 1) == ucCommandID)) )
    {
        if (GetProtoBalanceCapacityPoint(NORTH_PROTOCOL_1363) == s_wLastTop)
        {
            MoveBalTimeHisDataPoint(s_ucGetNum); // 移动历史数据的readpoint
        }
    }

    s_ucCommandID = ucCommandID;
    s_wLastTop = GetProtoBalanceCapacityPoint(NORTH_PROTOCOL_1363);
    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

    /* DATA TYPE判断，判断是否为最后一次发送 */
    *p++ = alarmNum > 1 ? 0x00 : 0x01;

    *p++ = ucCommandID;
    s_ucGetNum = (alarmNum > 1 ? 1 : alarmNum) & 0x00FF; // 实际待读取的条数
    *p++ = s_ucGetNum;
    ReadBalanceCapacityRecordData(s_ucGetNum, p, NORTH_PROTOCOL_1363);
    p += 68 * s_ucGetNum; // 移动指针
    
    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    return ;

}

/***************************************************************************
 * @brief   获取均衡容量实时信息
 **************************************************************************/
static void SendRealBalanceCapacity(BYTE ucPort)
{
    BYTE *p = NULL;
    BYTE i = 0;
    SHORT sTemp;
	T_HardwareParaStruct tHWPara;
    BYTE ucCellVoltNum = 0; 
    T_HisCellBalCAPData tHisCellBalCAPData;
    T_HisCellBalTimeDataStruct tHisCellBalTimeData;

    readBmsHWPara(&tHWPara);
    ucCellVoltNum = tHWPara.ucCellVoltNum;

    if(tHWPara.ucCellVoltNum > CELL_VOL_NUM)
    {
        return;
    }

    p  = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    /* DATA_FLAG */
    *p++ = GetDataFlag( BCM_ALARM, ucPort );
    *p++ = 1;//铁锂电池组数M

    GetCellBalTimeData(&tHisCellBalTimeData);
   
    if(GetRealBalCAP(&tHisCellBalCAPData,&tHisCellBalTimeData,&tHWPara,&sTemp))
    {
        *(SHORT *)p = Host2Modbus(&sTemp);       //时间统计，以24小时为周期
        p += 2;

        *p++ = ucCellVoltNum;

        for(i = 0;i < ucCellVoltNum ; i++)
        {
            *(SHORT *)p = FloatChangeToModbus(tHisCellBalCAPData.fBalCAP[i]); // 各单体军均衡容量信息
            p += 2;
        }
    }
    else
    {
        s_tProtocol.ucRTN = RTN_NO_DATA;
        return;
    }

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2); 
    return;
}


Static void GetFmPeakStatus(BYTE ucPort)
{
    BYTE *p = NULL;
    SHORT sTemp;
    T_FmSubmissionstruct  tFmRealPara;
    rt_memset_s(&tFmRealPara, sizeof(T_FmSubmissionstruct),0, sizeof(T_FmSubmissionstruct));
    GetFmRealData(&tFmRealPara);

    p  = s_tProtocol.aucSendBuf;
    rt_memset_s(s_tProtocol.aucSendBuf,sizeof(s_tProtocol.aucSendBuf) ,0, sizeof(s_tProtocol.aucSendBuf));
    /* DATA_FLAG */
    *p++ = GetDataFlag( BCM_ALARM, ucPort );

    *p++  = tFmRealPara.ucCurrentFMStatus; //当前模式：0备电 、 1调频 、2错峰
    sTemp = FloatChangeToModbus(tFmRealPara.fCurrentMaxChgPower*1000);    //最大可充电功率
    rt_memcpy_s(p,sizeof(sTemp) ,(BYTE*)&sTemp, sizeof(sTemp));
    p += 2;
    *p++  = tFmRealPara.wFMBattSOCLeftCapacity;  //电池剩余容量(整个站点的平均SOC)
    *p++  = tFmRealPara.ucChgAndDischgDisable;   //可充可放电状态

     /* LENID */
    s_tProtocol.wSendLenid = GetLength((p-s_tProtocol.aucSendBuf)*2);
    return;
}


/****************************************************************
函数:EnterFMMode
入口参数：无
出口参数：无
功能：进入调频模式
****************************************************************/

Static void EnterFMMode(void)
{
    SetFmMode(True); 
    ClearFmTimeCounter();
}


/****************************************************************
SetFmPower
入口参数：
出口参数：
功能：设置调频功率(分开写，降低圈复杂度)
****************************************************************/

Static void SetFmPowerFrom1363(FLOAT fFMPower)
{
    SetFmPower(fFMPower);
    ClearFmTimeCounter();
}


/****************************************************************
函数:EnterFMMode
入口参数：无
出口参数：无
功能：进入调频模式
****************************************************************/

Static void ExitFMMode(void)
{
    SetFmMode(False);
    SetFmPower(0);
    ClearFmTimeCounter();
}


/***************************************************************************
 * @brief    获取新增告警量(R321)
 **************************************************************************/
Static void GetAlarmR321(BYTE ucPort)
{
    BYTE *p = NULL;
    T_BCMAlarmStruct tBCMAlm = {0};
    GetRealAlarm(BCM_ALARM, (BYTE *)&tBCMAlm);

    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

    *p++ = GetDataFlag(BCM_ALARM, ucPort);
    *p++ = BATT_NUM_BCM; // 铁锂电池组数

    *p++ = tBCMAlm.ucSiteAntitheftAlm; // 站点防盗告警
    *p++ = tBCMAlm.ucGpsFaultAlm; // GPS故障告警
    *p++ = tBCMAlm.ucGyrFaultAlm; // 陀螺仪故障告警
    *p++ = tBCMAlm.ucNetAntitheftAlm; // 网管防盗告警
    *p++ = tBCMAlm.ucBattCellDamagePrt; //电池单体损坏保护
    *p++ = tBCMAlm.ucBattCellTempInvalidAlm; //电池单体温度无效告警
    p = p + 4; // 预留字节(先用预留字节，预留字节用完再用自定义字节数)

    *p++ = 0; // 自定义字节数

    /* LENID */
    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    return;
}

#ifdef INTELLIGENT_PEAK_SHIFTING

static void RecvFmInfoResponse(BYTE ucPort)
{
    s_tProtocol.ucRTN = NO_RETURN;
    T_FmInfoNotice *ptNotice = GetFmInfoNotice();

    if (!IsMaster())
    {
        return;
    }

    if (s_tProtocol.aucRecBuf[7] == RTN_CORRECT && s_tProtocol.aucRecBuf[8] == ((ptNotice->ucFrameId - 1) & 0xFF))
    {
        ptNotice->ucSendCount = 0;
        ptNotice->ucTimer = 0;
    }
}

#endif /* INTELLIGENT_PEAK_SHIFTING */



/***************************************************************************
 * @brief     站点防盗遥控命令
 **************************************************************************/
Static void SiteAntiTheftCtrl(BYTE ucPort)
{
    BYTE *p = s_tProtocol.aucSendBuf;
    BYTE ucResponseData = 0;
    BYTE ucCommandType = s_tProtocol.aucRecBuf[8];
    BYTE aucSiteAntiTheftKey[SITE_ANTITHEFT_KEY_LEN] = {0,};
    BYTE aucNewKey[SITE_ANTITHEFT_KEY_LEN] = {0,};

    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));
    rt_memcpy_s(aucSiteAntiTheftKey, SITE_ANTITHEFT_KEY_LEN, &s_tProtocol.aucRecBuf[9], SITE_ANTITHEFT_KEY_LEN);

    switch (ucCommandType)
    {
        case (BYTE)SiteAntiTheftStart:
            ucResponseData = StartSiteAntiTheft(aucSiteAntiTheftKey);
            if (ucResponseData == (BYTE)ResponseSuccess)
            {
                SaveAction(GetActionId(CTRL_SITE_ANTI_THEFT_ON), "Site Alm On");
            }
            break;

        case (BYTE)SiteAntiTheftHeartbeat:
            ucResponseData = HeartSiteAntiTheft(aucSiteAntiTheftKey);
            break;

        case (BYTE)SiteAntiTheftClose:
            ucResponseData = EndSiteAntiTheft(aucSiteAntiTheftKey);
            if (ucResponseData == (BYTE)ResponseSuccess)
            {
                SaveAction(GetActionId(CTRL_SITE_ANTI_THEFT_OFF), "Site Alm Off");
            }
            break;

        case (BYTE)SiteAntiTheftChangeKey:
            rt_memcpy_s(aucNewKey, SITE_ANTITHEFT_KEY_LEN, &s_tProtocol.aucRecBuf[9 + SITE_ANTITHEFT_KEY_LEN], SITE_ANTITHEFT_KEY_LEN);
            ucResponseData = ChangeKeySiteAntiTheft(aucSiteAntiTheftKey, aucNewKey);
            if (ucResponseData == (BYTE)ResponseSuccess)
            {
                SaveAction(GetActionId(CTRL_SITE_ANTI_THEFT_ON), "Change Key");
            }
            break;

        default:
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
            return;
    }

    if ((s_tProtocol.ucAddr == 0x00) || (s_tProtocol.ucAddr == 0x40))
    {
        s_tProtocol.ucRTN = NO_RETURN;
        return;
    }

    *p++ = ucResponseData;

    /* LENID */
    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
}

/***************************************************************************
 * @brief    站点防盗-开启
 **************************************************************************/
Static BYTE StartSiteAntiTheft(BYTE *pucSiteAntiTheftKey)
{
    if (GetSiteAntiTheftStatus() == (BOOLEAN)SiteAntiTheftOn)
    {
        return (BYTE)ResponseAlready;
    }

    DealSiteAntiTheftStart(pucSiteAntiTheftKey);

    return (GetSiteAntiTheftStatus() == (BOOLEAN)SiteAntiTheftOn) ? (BYTE)ResponseSuccess : (BYTE)ResponseFailure;
}

/***************************************************************************
 * @brief    站点防盗-心跳
 **************************************************************************/
Static BYTE HeartSiteAntiTheft(BYTE *pucSiteAntiTheftKey)
{
    if (GetSiteAntiTheftStatus() == (BOOLEAN)SiteAntiTheftOff)
    {
        return (BYTE)ResponseNotBind;
    }

    return (DealSiteAntiTheftHeartbeat(pucSiteAntiTheftKey) == True) ? (BYTE)ResponseSuccess : (BYTE)ResponseFailure;
}

/***************************************************************************
 * @brief    站点防盗-关闭
 **************************************************************************/
Static BYTE EndSiteAntiTheft(BYTE *pucSiteAntiTheftKey)
{
    if (GetSiteAntiTheftStatus() == (BOOLEAN)SiteAntiTheftOff)
    {
        return (BYTE)ResponseAlready;
    }

    DealSiteAntiTheftEnd(pucSiteAntiTheftKey);

    return (GetSiteAntiTheftStatus() == (BOOLEAN)SiteAntiTheftOff) ? (BYTE)ResponseSuccess : (BYTE)ResponseFailure;
}


/***************************************************************************
 * @brief    站点防盗-更换电子钥匙
 * @param    {BYTE} *pucSiteAntiTheftKey 旧电子钥匙
 * @param    {BYTE} *pucSiteAntiTheftKeyNew 新电子钥匙
 **************************************************************************/
Static BYTE ChangeKeySiteAntiTheft(BYTE *pucSiteAntiTheftKey, BYTE *pucSiteAntiTheftKeyNew)
{
    if (GetSiteAntiTheftStatus() == (BOOLEAN)SiteAntiTheftOff)
    {
        return (BYTE)ResponseNotBind;
    }

    return (DealSiteAntiTheftChangeKey(pucSiteAntiTheftKey, pucSiteAntiTheftKeyNew) == True) ? 
    (BYTE)ResponseSuccess : (BYTE)ResponseFailure;
}

Static void GetRealData1363(BYTE ucPort)
{
    BYTE *p = NULL;
    T_4GTrafficTotail t4GTrafficTotail = {0};

    Get4GTraffic(&t4GTrafficTotail);

    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    /* DATA_FLAG */
    *p++ = GetDataFlag( BCM_ALARM, ucPort );
    *p++ = BATT_NUM_BCM;//铁锂电池组数M

    *p++ = 8;     // 自定义字节

    *(UINT32 *)p = (UINT32)Int32ValuetoModbus(t4GTrafficTotail.s_tUp4GTraffic.uiByteTotail);      // 上行流量
    p += 4;

    *(UINT32 *)p = (UINT32)Int32ValuetoModbus(t4GTrafficTotail.s_tDown4GTraffic.uiByteTotail);    // 下行流量
    p += 4;

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
}
/***************************************************************************
 * @brief    获取告警级别
 **************************************************************************/
Static void GetAlarmLevelNew(BYTE uPort)
{
    BYTE *p = NULL;
    BYTE i;   
    GetSysPara(&s_tSysPara);
    p = s_tProtocol.aucSendBuf; 
    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));
    *p++ = GetDataFlag( BCM_ALARM, uPort );
    *p++ = BATT_NUM_BCM;
    *p++ = ALARM_NEW_NUM;//自定义字节数，新增数据需要修改该值
    for(i = 0; i < ALARM_NEW_NUM; i++)
    {
        *p++ = s_tSysPara.aucAlarmLevel[ALARM_CLASS_PAST + i];//告警级别一个字节
    }
    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2); 
}

/***************************************************************************
 * @brief    设置告警级别
 **************************************************************************/
Static void SetAlarmLevelNew(BYTE uPort)
{
   
    BYTE ucPara;
    BYTE ucIndex = 0;
    T_BCMAlarmStruct tAlarm;
    rt_memset_s(&tAlarm, sizeof(T_BCMAlarmStruct), 0, sizeof(T_BCMAlarmStruct));
    GetRealAlarm(BCM_ALARM,(BYTE *)&tAlarm);
    GetSysPara(&s_tSysPara);
    ucPara = s_tProtocol.aucRecBuf[9];//datainfo改为1字节
    s_tProtocol.ucCommandType =  s_tProtocol.aucRecBuf[8];
    if (s_tProtocol.ucCommandType >= 0x80 && s_tProtocol.ucCommandType <= ((ALARM_NEW_NUM - 1) + 0x80))
    {
        ucIndex = s_tProtocol.ucCommandType - 0x80;
        s_tSysPara.aucAlarmLevel[ALARM_CLASS_PAST + ucIndex] = ucPara;
    }
    else
    {
        s_tProtocol.ucRTN  = RTN_WRONG_COMMAND;
        return;
    }
    if ( !(SetSysPara( &s_tSysPara, True, CHANGE_BY_YD1363 )) )
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;   
    }   
    return;
  

}

/***************************************************************************
 * @brief    获取告警干接点
 **************************************************************************/
Static void GetAlarmRlyNew(BYTE uPort)
{
    BYTE *p = NULL;
    BYTE i;    
    GetSysPara(&s_tSysPara);
    p = s_tProtocol.aucSendBuf; 
    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));
    *p++ = GetDataFlag( BCM_ALARM, uPort );
    *p++ = BATT_NUM_BCM;
    *p++ = ALARM_NEW_NUM;//自定义字节数，新增数据需要修改该值
    for(i = 0; i < ALARM_NEW_NUM; i++)
    {
        *p++ = s_tSysPara.aucRelayBit[ALARM_CLASS_PAST + i];//告警级别一个字节
    }
    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2); 
}

/***************************************************************************
 * @brief    设置告警干接点
 **************************************************************************/
Static void SetAlarmRlyNew(BYTE ucPort)
{
    BYTE ucPara;
    BYTE ucIndex = 0;

    GetSysPara( &s_tSysPara );

    s_tProtocol.ucCommandType   = s_tProtocol.aucRecBuf[8];

    ucPara = s_tProtocol.aucRecBuf[9];     //datainfo改为1字节

    if (s_tProtocol.ucCommandType >= 0x80 && s_tProtocol.ucCommandType <= ((ALARM_NEW_NUM - 1) + 0x80))
    {
        ucIndex = s_tProtocol.ucCommandType - 0x80;
         s_tSysPara.aucRelayBit[ALARM_CLASS_PAST + ucIndex] = ucPara;
    }
    else
    {
        s_tProtocol.ucRTN  = RTN_WRONG_COMMAND;
        return;
    }

    if ( !(SetSysPara( &s_tSysPara, True, CHANGE_BY_YD1363 )) )
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;   
    }   
    return;
}

/***************************************************************************
 * @brief     网管防盗遥控命令
 **************************************************************************/
Static void NetAntiTheftCtrl(BYTE ucPort)
{
    BYTE *p = s_tProtocol.aucSendBuf;
    BYTE ucResponseData = 0;
    BYTE ucCommandType = s_tProtocol.aucRecBuf[8];
    BYTE aucNetAntiTheftKey[NETANTITHEFT_KEY_LEN] = {0,};
    BYTE aucNetAntiTheftSN[NETANTITHEFT_SN_LEN] = {0,};
    BYTE aucLenBuf[3] = {68, 98, 68};

    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));
    rt_memcpy_s(aucNetAntiTheftKey, sizeof(aucNetAntiTheftKey), &s_tProtocol.aucRecBuf[9], NETANTITHEFT_KEY_LEN);

    if(ucCommandType <= NETANTITHEFT_CLOSE)
    {
        if(s_tProtocol.wRecLenid != aucLenBuf[ucCommandType])
        {
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
            return;
        }
    }

    switch (ucCommandType)
    {
        case NETANTITHEFT_START:
            ucResponseData = StartNetAntiTheft(aucNetAntiTheftKey);
            if (ucResponseData == ResponseSuccess)
            {
                SaveAction(GetActionId(CTRL_NET_ANTI_THEFT), "1363NetDefence On");
            }
            break;

        case NETANTITHEFT_HEARBEAT:
            rt_memcpy_s(aucNetAntiTheftSN, sizeof(aucNetAntiTheftSN), &s_tProtocol.aucRecBuf[9 + NETANTITHEFT_KEY_LEN], NETANTITHEFT_SN_LEN);
            ucResponseData = HeartNetAntiTheft(aucNetAntiTheftKey, aucNetAntiTheftSN);
            break;

        case NETANTITHEFT_CLOSE:
            ucResponseData = EndNetAntiTheft(aucNetAntiTheftKey);
            if (ucResponseData == ResponseSuccess)
            {
                SaveAction(GetActionId(CTRL_NET_ANTI_THEFT), "1363NetDefence Off");
            }
            break;

        default:
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
            return;
    }

    *p++ = ucResponseData;

    /* LENID */
    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
}

/***************************************************************************
 * @brief    网管防盗-开启
 **************************************************************************/
Static BYTE StartNetAntiTheft(BYTE *pucNetAntiTheftKey)
{
    if (GetNetAntiTheftStatus() == NETANTITHEFT_ON)
    {
        return NET_RESPONSE_ALREADY;
    }

    return DealNetAntiTheftStart(pucNetAntiTheftKey);
}

/***************************************************************************
 * @brief    网管防盗-心跳
 **************************************************************************/
Static BYTE HeartNetAntiTheft(BYTE *pucNetAntiTheftKey, BYTE *pucNetAntiTheftSN)
{
    if (GetNetAntiTheftStatus() == NETANTITHEFT_OFF)
    {
        return NET_RESPONSE_NOT_BIND;
    }

    return DealNetAntiTheftHeartbeat(pucNetAntiTheftKey, pucNetAntiTheftSN);
}

/***************************************************************************
 * @brief    网管防盗-关闭
 **************************************************************************/
Static BYTE EndNetAntiTheft(BYTE *pucNetAntiTheftKey)
{
    if (GetNetAntiTheftStatus() == NETANTITHEFT_OFF)
    {
        return NET_RESPONSE_ALREADY;
    }

    return DealNetAntiTheftEnd(pucNetAntiTheftKey);
}

Static void ChangeNetAntiTheftKey(BYTE ucPort)
{
    BYTE *p = s_tProtocol.aucSendBuf;
    BYTE ucResponseData = 0;
    BYTE aucNetAntiTheftKeyOld[NETANTITHEFT_KEY_LEN] = {0,};
    BYTE aucNetAntiTheftKeyNew[NETANTITHEFT_KEY_LEN] = {0,};

    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));
    rt_memcpy_s(aucNetAntiTheftKeyOld, NETANTITHEFT_KEY_LEN, &s_tProtocol.aucRecBuf[8], NETANTITHEFT_KEY_LEN);
    rt_memcpy_s(aucNetAntiTheftKeyNew, NETANTITHEFT_KEY_LEN, &s_tProtocol.aucRecBuf[40], NETANTITHEFT_KEY_LEN);
    
    if(GetNetAntiTheftStatus() != NETANTITHEFT_ON)
    {
        ucResponseData = NET_RESPONSE_NOT_BIND;
    }
    else if(JudgeNetAntiTheftKey(aucNetAntiTheftKeyOld) != 0)
    {
        ucResponseData = NET_RESPONSE_FAILURE_BY_KEY;
    }
    else
    {
        ucResponseData = WriteNetAntiTheftInfo(NETANTITHEFT_ON, aucNetAntiTheftKeyNew);
    }

    if(ucResponseData == SUCCESSFUL)
    {
        SaveAction(GetActionId(CTRL_NET_ANTI_THEFT), "1363ChangeNetDeKey");
    }

    *p++ = ucResponseData;

    /* LENID */
    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
}

// 公共函数，用于将版本信息放置到发送buff当中

Static BOOLEAN PutSoftwareVersionIntoBuff(T_SoftwareCustomizedInfo *ptSoftwareCustomizedInfo, BYTE *buff)
{
    SHORT sTemp = 0;

    // 检查自定义BMS版本是否全为零
    if(CheckArrAllZero(ptSoftwareCustomizedInfo->CustomizedBmsVersion, sizeof(ptSoftwareCustomizedInfo->CustomizedBmsVersion)))
    {
        MemsetBuff(buff, BMS_VER, (BYTE)rt_strnlen_s(BMS_VER, 20), (BYTE)20, 0x20);   // BMS真实软件版本
    }
    else
    {
        MemsetBuff(buff, ptSoftwareCustomizedInfo->CustomizedBmsVersion, (BYTE)20, (BYTE)20, 0x20); // BMS客户个性化信息
    }

    buff += 20; // 移动指针到下一个字段

    // 检查自定义BMS发布日期是否全为零
    if(CheckArrAllZero(ptSoftwareCustomizedInfo->CustomizedBmsReleaseDate, sizeof(ptSoftwareCustomizedInfo->CustomizedBmsReleaseDate)))
    {
        PutInt16ToBuff(buff, SOFTWARE_RELEASE_YEAR);
        buff += 2;
        *buff++ = SOFTWARE_RELEASE_MONTH;
        *buff++ = SOFTWARE_RELEASE_DATE; // BMS真实软件发布日期
    }
    else
    {
        sTemp = ((WORD)ptSoftwareCustomizedInfo->CustomizedBmsReleaseDate[0] << 8) + ((WORD)ptSoftwareCustomizedInfo->CustomizedBmsReleaseDate[1]);
        PutInt16ToBuff(buff, sTemp); // BMS客户个性化版本日期
        buff += 2;
        *buff++ = ptSoftwareCustomizedInfo->CustomizedBmsReleaseDate[2];
        *buff++ = ptSoftwareCustomizedInfo->CustomizedBmsReleaseDate[3];
    }

    return True;
}


/***************************************************************************
 * @brief    获取网管防盗电子钥匙（R321）
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/

Static void GetNetAntikey(BYTE ucPort)
{
    BYTE *p = NULL;
    T_BCMDataStruct tBCMAnaData;

    // 初始化结构体
    rt_memset_s(&tBCMAnaData, sizeof(T_BCMDataStruct), 0, sizeof(T_BCMDataStruct));

    // 获取真实数据
    GetRealData(&tBCMAnaData);

    // 初始化发送缓冲区
    p = s_tProtocol.aucSendBuf;
    rt_memset_s(p, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));

    // 复制网络防盗密钥到发送缓冲区
    rt_memcpy_s(p, NETANTITHEFT_KEY_LEN, tBCMAnaData.ucNetAntitheftKey, NETANTITHEFT_KEY_LEN);
    p += NETANTITHEFT_KEY_LEN;

    // 预留字节
    p += 30; // 预留字节

    // 自定义字节数
    *p = 0; // 自定义字节数
    p ++;

    // 计算发送长度标识
    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
}



Static void GetFactoryInfoEx(BYTE ucPort)
{
    BYTE *p = NULL;
    T_BmsInfoStruct tBmsInfo;
    rt_memset_s(&tBmsInfo, sizeof(T_BmsInfoStruct), 0x00, sizeof(T_BmsInfoStruct));

    readBMSInfofact(&tBmsInfo);
    p = s_tProtocol.aucSendBuf;
    rt_memset_s(p, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));

    MemsetBuff(p, tBmsInfo.acHardwareInfo, (BYTE)rt_strnlen_s(tBmsInfo.acHardwareInfo, LENGTH_HARDWARE_INFORMATION), LENGTH_HARDWARE_INFORMATION, 0x20); //BMS硬件版本信息

    p += 20;

    *p = 0; // 自定义字节数
    p ++;

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    return;
}


#ifdef ENABLE_SYS_MONITOR
/***************************************************************************
 * @brief    获取内存精细化管理信息（内部使用）
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/

static void GetElaborateMemoryManagement(BYTE ucPort)
{
    BYTE *p = NULL;
    BYTE ucCommandType = s_tProtocol.aucRecBuf[7];
    rt_int32_t sWriteFsCnt = getWriteFsCnt();
    const sys_monitor_thread_info_t *tThreadInfo = get_thread_info();
    const sys_monitor_heap_info_t *tHeapInfo = get_heap_info();
    const sys_monitor_log_t *tSysMonitorLog = get_sys_monitor_log();
    const BYTE ucCpuUsage = get_cpu_usage();
    // 初始化发送缓冲区
    p = s_tProtocol.aucSendBuf;
    rt_memset_s(p, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));

    switch (ucCommandType)
    {
    case 0x80: // 获取实时运行信息
        PutInt32ToBuff(p, sWriteFsCnt); // flash擦写次数统计信息
        p += 4;
        *p++ = ucCpuUsage; // CPU利用率
        PutInt32ToBuff(p, (INT32)tHeapInfo->current_available_size); // 动态内存剩余大小
        p += 4;
        *p++ = tThreadInfo->num; // 线程个数
        for (size_t i = 0; i < tThreadInfo->num; ++i)
        {
            MemsetBuff(p, (void *)tThreadInfo->info[i].name, sizeof(tThreadInfo->info[0].name), 12, 0x00); // 线程名
            p += 12;
            *p = tThreadInfo->info[i].current_stack_usage; // 线程栈使用率
            p++;
        }
        break;

    case 0x81: // 获取运行态极值信息
        PutInt32ToBuff(p, sWriteFsCnt); // flash擦写次数统计信息
        p += 4;
        *p++ = tSysMonitorLog->max_cpu_usage; // CPU利用率
        PutInt32ToBuff(p, tSysMonitorLog->min_heap_remained_size); // 动态内存剩余大小
        p += 4;
        *p++ = tThreadInfo->num; // 线程个数
        for (size_t i = 0; i < tThreadInfo->num; ++i)
        {
            MemsetBuff(p, (void *)tThreadInfo->info[i].name, sizeof(tThreadInfo->info[0].name), 12, 0x00); // 线程名
            p += 12;
            *p = tSysMonitorLog->thread_log[i].max_stack_usage; // 线程栈使用率
            p++;
        }
        break;

    case 0x82: // 删除运行态极值记录文件
        delete_sys_monitor_log();
        return;

    default:
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
        return;
    }

    // 计算发送长度标识
    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
}

#endif /* ENABLE_SYS_MONITOR */

/* Started by AICoder, pid:p8eaeyfb4as11bc14b03090130d6cb27aa59cc65 */
Static void GetGpsInitInfo(BYTE uPort) {
    BYTE *p = s_tProtocol.aucSendBuf;
    T_GPSPositionStruct tGPSPos = {0};
    T_GPSDirectionStruct tGpsDirect = {0};

    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));

    readGPSLocation(&tGPSPos);
    readGPSDirection(&tGpsDirect);

    // 填充协议头信息
    *p++ = GetDataFlag(BCM_ALARM, uPort);
    *p++ = BATT_NUM_BCM;
    *p++ = 10;  // 自定义字节数

    // 纬度
    *(UINT32 *)p = (UINT32)Int32ValuetoModbus((INT32)(tGPSPos.fLatitude * 100000.0));
    p += 4;

    // 经度
    *(UINT32 *)p = (UINT32)Int32ValuetoModbus((INT32)(tGPSPos.fLongtitude * 100000.0));
    p += 4;

    // 方向信息
    *p++ = tGpsDirect.ucLatiDirect;
    *p++ = tGpsDirect.ucLongDirect;

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
}
/* Ended by AICoder, pid:p8eaeyfb4as11bc14b03090130d6cb27aa59cc65 */

/* Started by AICoder, pid:n27297a5103a36e14f27086c4095ab52b503a1fb */
Static void SetGpsInitInfo(BYTE uPort) {
    const BYTE ucLenBuf[] = {4, 4, 1, 1};  // 命令长度表
    T_GPSPositionStruct tGPSPos = {0};
    T_GPSDirectionStruct tGpsDirect = {0};
    double fPara = (double)Int32ValuetoModbus(*(INT32*)(s_tProtocol.aucRecBuf + 9));

    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[8];

    // 验证命令类型有效性
    if (s_tProtocol.ucCommandType < 0x80 || s_tProtocol.ucCommandType >= 0x80 + sizeof(ucLenBuf)) {
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
        return;
    }

    // 数据长度校验
    const size_t cmdIndex = s_tProtocol.ucCommandType - 0x80;
    if (ucLenBuf[cmdIndex] != (s_tProtocol.wRecLenid / 2 - 2)) {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
        return;
    }

    s_tProtocol.ucRTN = RTN_CORRECT;

    switch (s_tProtocol.ucCommandType) {
        case 0x80:  // 设置纬度
            readGPSLocation(&tGPSPos);
            tGPSPos.fLatitude = fPara / 100000.0;
            writeGPSLocation(&tGPSPos);
            break;

        case 0x81:  // 设置经度
            readGPSLocation(&tGPSPos);
            tGPSPos.fLongtitude = fPara / 100000.0;
            writeGPSLocation(&tGPSPos);
            break;

        case 0x82:  // 设置纬度方向
            readGPSDirection(&tGpsDirect);
            tGpsDirect.ucLatiDirect = s_tProtocol.aucRecBuf[9];
            writeGPSDirection(&tGpsDirect);
            break;

        case 0x83:  // 设置经度方向
            readGPSDirection(&tGpsDirect);
            tGpsDirect.ucLongDirect = s_tProtocol.aucRecBuf[9];
            writeGPSDirection(&tGpsDirect);
            break;

        default:
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
            break;
    }
}
/* Ended by AICoder, pid:n27297a5103a36e14f27086c4095ab52b503a1fb */

Static void PeakShiftTemplateForward(BYTE ucPort)
{
    BYTE ucCommandNum = 0;
    BOOLEAN bLastTemp = False;

    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];
    bLastTemp = s_tProtocol.ucCommandType >> 7;        //是否为最后一条指令
    ucCommandNum = s_tProtocol.ucCommandType & 0x7F;   //当前指令编号

    if(ucCommandNum == 0)
    {
        s_ucSetTemplateNum = 0;
        rt_memset_s(&s_tPeakShiftPara,sizeof(T_PeakShiftPara) ,0x00, sizeof(T_PeakShiftPara));
        if((s_tProtocol.wRecLenid / 2 - 2) == sizeof(T_PeakShiftPara))
        {
            s_tTemplateInfo.ucDayTemplateNum =  s_tProtocol.aucRecBuf[8];
            rt_memcpy_s(&s_tPeakShiftPara,sizeof(s_tPeakShiftPara),&s_tProtocol.aucRecBuf[9],sizeof(s_tPeakShiftPara));
            //整体设置错峰参数
            if(!SetPeakShiftAllPara(&s_tPeakShiftPara))
            {
                s_tProtocol.ucRTN  = RTN_INVALID_DATA;
                return;
            }
            //s_ucSetTemplateNum++;
            return;
        }
        
        s_tProtocol.ucRTN  = RTN_INVALID_DATA;
        return;
    }

    //一个模板时间段最大为48个，每个是时间段4字节，一共192字节
    if((192 != (s_tProtocol.wRecLenid / 2 - 2)) || ((s_tProtocol.wRecLenid / 2 - 2) < 5)) 
    {
        s_tProtocol.ucRTN   = RTN_INVALID_DATA;
        return;
    }

    if(ucCommandNum == 0x08 && !bLastTemp) //第八个模板也是最后一个模板
    {
        s_tProtocol.ucRTN   = RTN_WRONG_COMMAND;
        return;
    }

    if(ucCommandNum <= 8)
    {
        if(!TempForwardCheck(&s_tTemplateInfo.uTemplateInfo.tMonthTemp.atDateTemp[ucCommandNum - 1], &s_tProtocol.aucRecBuf[8]))
        {
            s_tProtocol.ucRTN  = RTN_INVALID_DATA; 
            return;
        }
    }
    else
    {
        s_tProtocol.ucRTN  = RTN_WRONG_COMMAND;  
        return;
    }

    if(bLastTemp)
    {
        s_ucSetTemplateNum = 0x00;
        if(!TemplateCompared(&s_tTemplateInfo))
        {
            if(!SetPeakShiftTemplate(&s_tTemplateInfo))
            {
                s_tProtocol.ucRTN  = RTN_INVALID_DATA; 
            }
        }
    }
    return;
}