#include <string.h>
#include "sps.h"
#include "cmd.h"
#include "concurrent_update_master.h"
#include "protocol_remote_download.h"
#include "protocol_layer.h"


/* 设备缓冲区长度 */
#define R_COM_BUFF_LEN                    2048     ///<  接收缓冲区长度（串口链路）
#define S_COM_BUFF_LEN                    5120     ///<  发送缓冲区长度（串口链路）

/* 命令请求 */
static download_cmd_head_t cmd_req[] = {
    {CMD_TRIG_FRAME_TYPE_H, CMD_TRIG_FRAME_TYPE_L, CMD_TRIG_CHIP_TYPE_H, CMD_EXTEND_TRIG_VERSION,},                   //0
    {CMD_DATA_CONCUR_UPDATE_DATA     ,          CMD_DATA_NONE, CMD_DATA_NONE, CMD_DATA_NONE,},                     // 1
    {CMD_DATA_CONCUR_TRANSFER_CONFIRM,          CMD_DATA_NONE, CMD_DATA_NONE, CMD_DATA_NONE,},                     // 2
    {CMD_DATA_CONCUR_CONFIRM_UPDATE  ,          CMD_DATA_NONE, CMD_DATA_NONE, CMD_DATA_NONE,},                     // 3
    {0}
};

/* 命令应答   */
static download_cmd_head_t cmd_ack[] = {
    {CMD_TRIG_FRAME_TYPE_H, CMD_TRIG_FRAME_TYPE_L, CMD_TRIG_CHIP_TYPE_H, CMD_EXTEND_TRIG_VERSION},                        //0
    {CMD_DATA_CONCUR_UPDATE_DATA     ,          CMD_DATA_NONE, CMD_DATA_NONE, CMD_DATA_NONE,},                     // 1
    {CMD_DATA_CONCUR_TRANSFER_CONFIRM,          CMD_DATA_NONE, CMD_DATA_NONE, CMD_DATA_NONE,},                     // 2
    {CMD_DATA_CONCUR_CONFIRM_UPDATE  ,          CMD_DATA_NONE, CMD_DATA_NONE, CMD_DATA_NONE,},                     // 3
    {0}
};

/* 通信协议命令表 */
static cmd_t master_no_poll_cmd_tab[] = {
    {DOWNLOAD_CONCURRENT_DATA_TRIG   ,      CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], DOWNLOAD_CMD_HEADLEN, },
    {DOWNLOAD_CONCURRENT_DATA        ,      CMD_POSITIVE, &cmd_req[1], &cmd_ack[1], DOWNLOAD_CMD_HEADLEN, },
    {DOWNLOAD_CONCURRENT_ACK_FRAME   ,      CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], DOWNLOAD_CMD_HEADLEN, },
    {DOWNLOAD_CONCURRENT_UPDATE_FRAME,      CMD_POSITIVE, &cmd_req[3], &cmd_ack[3], DOWNLOAD_CMD_HEADLEN, },
    {0},
};

static dev_type_t master_dev_inverter = {
    DEV_DAC_NORTH_DOWNLOAD_MASTER, 1, PROTOCOL_DOWNLOAD, LINK_INVERTER, R_COM_BUFF_LEN, S_COM_BUFF_LEN, 0, master_no_poll_cmd_tab, NULL
};

dev_type_t* init_master_dev_inverter()
{
    return &master_dev_inverter;
}
