#ifndef _DEV_CSU_COMM_H
#define _DEV_CSU_COMM_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "device_type.h"

/*-----设备类型-----*/
#define  DEV_TOV      1
#define  DEV_TOV_COM  2
#define  DEV_CSU_CAN  3


/* 远程下载协议命令ID:唯一标示一个命令 */
#define UPDATE_TRIG_FRAME             1         ///< 升级触发帧
#define TRAN_DATA_FRAME               2         ///< 传输数据帧
#define UPLOAD_FILE_TRIG              3         ///<  上传触发
#define UPLOAD_FILE_LIST              4         ///<  上传文件列表
#define UPLOAD_FILE_DATA              5         ///<  上传文件数据
#define GET_REAL_DATA                 6         ///<  获取实时量
#define GET_PARA_1                    7         ///<  获取参数1
#define GET_PARA_2                    8         ///<  获取参数2
#define SET_PARA_1                    9         ///<  设置参数1
#define SET_PARA_2                    10        ///<  设置参数2
#define BUILD_LINK                    11        ///<  建链命令
#define SET_SYS_TIME                  12        ///<  设置系统时间
#define GET_SYS_TIME                  13        ///<  获取系统时间
#define SET_MANU_info                 14        ///<  设置厂家信息
#define GET_MANU_info                 15        ///<  获取厂家信息
#define GET_INRLY_STATUS              16        ///<  获取干接点状态
#define CTRL_OUTRLY_HIGH              17        ///<  控制输出干接点高
#define CTRL_OUTRLY_LOW               18        ///<  控制输出干接点低
#define CLEAR_EXTREME_DATA            19        ///<  清除极值数据 
#define RESTORE_FACT                  20        ///<  恢复出厂设置 
#define CLEAR_FAULT_DATA              21        ///<  清除录波数据
#define GET_4G_INFO                   22        ///<  获取4G信息
#define GET_MQTT_PARA                 23        ///<  获取mqtt参数
#define SET_MQTT_PARA                 24        ///<  设置mqtt参数
#define CLEAR_DATA_REBOOT             25        ///<  清除数据并重启

#define PARA1_LEN                     34        ///<  参数1字节长度
#define PARA2_LEN                     78        ///<  参数2字节长度
#define LINK_LEN                      76        ///<  建链命令字节长度


#pragma pack(push, 1)
typedef struct {
    char manu_name[30];
    char hardware_ver[20];
    char serial[12];
}manu_info_t;
#pragma pack(pop)

dev_type_t* init_dev_tov_com(void);
dev_type_t* init_dev_csu_can(void);

int pack_para_2(void* dev_inst, void* cmd_buff);
int parse_para_2(void* dev_inst, void* cmd_buff);
int pack_real_data(void* dev_inst, void* cmd_buff);
int pack_sys_time_data(void* dev_inst, void* cmd_buff);
int parse_sys_time_data(void* dev_inst, void* cmd_buff);
int parse_manu_info(void* dev_inst, void* cmd_buff);
int load_manu_info();
int parse_get_inrly(void* dev_inst, void* cmd_buff);
int parse_ctrl_outrly_high(void* dev_inst, void* cmd_buff);
int parse_ctrl_outrly_low(void* dev_inst, void* cmd_buff);
int parse_clear_extreme_data(void* dev_inst, void* cmd_buff);
int parse_restore_fact(void* dev_inst, void* cmd_buff);
int parse_clear_fault_data(void* dev_inst, void* cmd_buff);
int pack_para_1(void* dev_inst, void* cmd_buff);
int parse_clear_data_reboot(void* dev_inst, void* cmd_buff);
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif 
