#ifndef TIME_SYNC_H_
#define TIME_SYNC_H_

#ifdef __cplusplus
extern "C" {
#endif
#include "msg.h"
#include "device_type.h"

typedef struct {
    unsigned short check_period;   ///< 时间同步周期, unit ms, 同步判断间隔
    unsigned short north_delay;   ///< 北向执行周期, unit ms
    unsigned short max_sync_num;  ///< 最大同步次数
    unsigned short sync_cmd_id;   ///< 时间同步功能码
    unsigned int resync_interval; ///< 重新同步间隔, unit s, 定期时间同步
    module_msg_t   alm_msg;       ///< 同步失败消息
    dev_inst_t*    dev_inst;      ///< 请求发送目标设备
    unsigned char* sync_state;    ///< 时间同步状态
}time_sync_para_t;

typedef void (*set_t_sync_alm_f)(unsigned char value);
typedef unsigned char (*get_t_sync_alm_f)(void);
void positive_time_sync(set_t_sync_alm_f set_state, get_t_sync_alm_f get_state, time_sync_para_t* para);

#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // TIME_SYNC_H_;
