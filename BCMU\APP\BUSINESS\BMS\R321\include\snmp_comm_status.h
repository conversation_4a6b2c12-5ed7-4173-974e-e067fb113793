/* Started by AICoder, pid:53aa3s0aadf442c145960b2c708df3114585ac18 */
#ifndef SNMP_COMM_COUNT_H
#define SNMP_COMM_COUNT_H

#include "common.h"

#include <stdint.h>

uint32_t get_snmp_recv_count(void);
void inc_snmp_recv_count(void);
void clear_snmp_recv_count(void);
BOOLEAN get_snmp_comm_status(void);
void set_snmp_comm_status(BOOLEAN status);
void snmp_comm_timer_count(void);

#endif /* SNMP_COMM_COUNT_H */
/* Ended by AICoder, pid:53aa3s0aadf442c145960b2c708df3114585ac18 */