#include <rtthread.h>
#include "para_check.h"
#include "data_type.h"
#include "type_define_in.h"
#include "para_manage.h"
#include "para_id_in.h"
#include "cmd.h"
#include "sps.h"
#include "msg.h"
#include "msg_id.h"
#include "realdata_id_in.h"
#include "delay_update.h"
#include "realdata_save.h"
#include "update_download_manage.h"
#include "update_manage.h"
#include "concurrent_slave_update_handle.h"
#include "utils_rtthread_security_func.h"

static rt_timer_t g_update_enable_timer = NULL; 
static delay_update_info_t g_delay_update_info = {0};
char g_network_download_msg[18] = {0};
time_t g_delay_update_start_time = 0;

delay_update_info_t* get_delay_update_info(void)
{
    return &g_delay_update_info;
}

int get_pv_normal_time(void)
{
    char status = 0;
    unsigned char delay_update_status = 0;

    g_delay_update_info.curr_time = rt_tick_get_millisecond();

    get_one_data(DAC_DATA_ID_INPUT_UNDERVOLT_STA, &status);

    // 说明第一次
    if(g_delay_update_info.last_time == 0)
    {
        g_delay_update_info.last_time = g_delay_update_info.curr_time;
        return g_delay_update_info.pv_normal_time;
    }

    // 检查时间是否递增
    if(g_delay_update_info.curr_time >= g_delay_update_info.last_time)
    {   
        // 如果欠压，则清零pv_normal_time
        if(status != FALSE)
        {
            g_delay_update_info.pv_normal_time = 0;
            set_one_data(DAC_DATA_ID_DELAY_UPDATE_STATUS, &delay_update_status);
        }
        else
        {   
            g_delay_update_info.pv_normal_time += g_delay_update_info.curr_time - g_delay_update_info.last_time;
        }
        g_delay_update_info.last_time = g_delay_update_info.curr_time;

        //rt_kprintf("g_pv_normal_time:  %d\n",g_delay_update_info.pv_normal_time);
        return g_delay_update_info.pv_normal_time;
    }

    g_delay_update_info.last_time = g_delay_update_info.curr_time;
    return g_delay_update_info.pv_normal_time;
}


int check_update_enable(void)
{   
    unsigned int pv_normal_time = get_pv_normal_time();
    time_t cur_time = time(RT_NULL);
    unsigned char master_slave_status = 0;
    unsigned char update_status = DEVICE_NO_UPDATE;
    get_one_data(DAC_DATA_ID_PV_UPDATE_STATUS, &update_status);
    get_one_data(DAC_DATA_ID_MASTER_SLAVE_STATUS, &master_slave_status);
    //网管升级条件下，从机设备，且延迟升级时间超过1天，则清除升级标志
    if((update_status == DEVICE_MQTT_UPDATING) && (master_slave_status == 0) && (cur_time - g_delay_update_start_time >= ONE_DAY_SECOND))
    {
        update_status = DEVICE_NO_UPDATE;
        set_one_data(DAC_DATA_ID_PV_UPDATE_STATUS, &update_status);
    }
    if(pv_normal_time < DELAY_UPDATE_TIME)
    {
        return SUCCESSFUL;
    }

    //满足延迟升级状态置位
    unsigned char delay_update_status = 1;
    set_one_data(DAC_DATA_ID_DELAY_UPDATE_STATUS, &delay_update_status);
    rt_memset_s(&g_delay_update_info, sizeof(g_delay_update_info), 0x00, sizeof(g_delay_update_info));
    return SUCCESSFUL;
}

void update_enable_timeout(void *parameter)
{   
    rt_msg_t msg = NULL;
    msg = rt_msg_create(DELAY_UPDATE_CHECH_MSG, NULL, 0);
    RETURN_IF_FAIL(msg != NULL);
    rt_msg_send_event(msg);
}

int init_update_enable_timer()
{   
    g_update_enable_timer = rt_timer_create("update_enable_timer", update_enable_timeout, NULL, CHECK_PV_NORMAL_TIMEOUT, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
    RETURN_VAL_IF_FAIL(g_update_enable_timer != NULL, FAILURE);
    rt_timer_start(g_update_enable_timer);
    return SUCCESSFUL;
}


int start_delay_update()
{
    unsigned char delay_update = 0;
    unsigned char update_status = DEVICE_NO_UPDATE;
    get_one_data(DAC_DATA_ID_PV_UPDATE_STATUS, &update_status);
    RETURN_VAL_IF_FAIL(update_status != DEVICE_UPDATING, FAILURE);
    get_one_para(DAC_PARA_ID_DELAY_UPDATE_OFFSET, &delay_update);
    LOG_E("%s:%d|delay_update:%d", __FUNCTION__ , __LINE__, delay_update);

    if(delay_update == 2)
    {
        rt_kprintf("start_delay_update DEVICE_NO_UPDATE\n");
        set_one_data(DAC_DATA_ID_PV_UPDATE_STATUS, &update_status);
        return SUCCESSFUL;
    }

    if(delay_update == 0)
    {
        set_north_trans_end_flag(TRUE);
        start_slave_concurr_update_timer(120000);
    }

    rt_memset_s(&g_delay_update_info, sizeof(g_delay_update_info), 0x00, sizeof(g_delay_update_info));
    g_delay_update_start_time = time(RT_NULL);
    update_status = DEVICE_MQTT_UPDATING;
    set_one_data(DAC_DATA_ID_PV_UPDATE_STATUS, &update_status);
    return SUCCESSFUL;
}




int send_update_msg_to_sys_manage(int opt, int file_type, unsigned int file_size, unsigned int file_crc, short addr_info)
{
    rt_memset_s(g_network_download_msg, sizeof(g_network_download_msg), 0x00, sizeof(g_network_download_msg));
    *(int*)g_network_download_msg = opt;
    *(int*)(g_network_download_msg + 4) = file_type;
    *(unsigned int*)(g_network_download_msg + 8) = file_size;
    *(unsigned int*)(g_network_download_msg + 12) = file_crc;
    *(short*)(g_network_download_msg + 16) = addr_info;
    send_msg_to_thread(NETWORK_DOWNLOAD_FILE_MSG, MOD_SYS_MANAGE, &g_network_download_msg, sizeof(g_network_download_msg));
    rt_kprintf("delay send_update_msg_to_sys_manage\n");
    rt_kprintf("mqtt_update|g_opt:%d, type:%d, size:%d, addr_info:0x%x\n", opt, file_type, file_size, addr_info);
    return SUCCESSFUL;
}