/*
 * @file    : control_manage.c
 * @brief   : 控制管理
 * @details : 
 * <AUTHOR> 邹绍云10326737
 * @Date    : 2023-02-08
 * @LastEditTime: 2023-02-08
 * @version : V0.0.1
 * @para    : Copyright (c) 
 *            ZTE Corporation
 * @par     : History
 *     version: author, date, descn
 */
#include <string.h>
#include <rtthread.h>
#include <stdlib.h>
#include "utils_thread.h"
#include "control_manage.h"
#include "msg.h"
#include "pin_ctrl.h"
#include "cmd.h"
#include "device_type.h"
#include "sps.h"
#include "fire_protect.h"



static int fire_protect_ctrl_process(void* dev_inst, void *cmd_buf) {
    epo_ctrl_process();
    return SUCCESSFUL;
}

void fire_protect_ctrl_init(void) {
    cmd_handle_register_t reg = {0};
    
    reg.dev_id = DEV_BMU;
    reg.cmd_id = BMU_FIRE_ALARM_PROTECT;
    reg.cmd_type = CMD_TYPE_NO_POLL;
    reg.cmd_parse_handle = fire_protect_ctrl_process;
    register_cmd_handle(&reg);
}