﻿/*
 * @file    : 
 * @brief   : 
 * @details : 
 * <AUTHOR> 周彬
 * @Date    : 2021-12-13 14:25:06
 * @LastEditTime: 2022-02-15 16:07:15
 * @version : V0.0.1
 * @para    : Copyright (c) 
 *            ZTE Corporation
 * @par     : History
 *     version: author, date, descn
 */

#include <stdio.h>
#include <rtthread.h>
#include <rtdbg.h>
#include <board.h>

//#include "dataTypedef.h"
//#include "utils.h"
#include "softbus.h"
#include "server_id.h"

#include "led.h"
#include "sample.h"
#include "realAlarm.h"
#include "CommCan.h"
#include "prtclDL.h"
#include "hisdata.h"
#include "flash.h"
#include "wireless.h"
#include "fileSys.h"
#include "para.h"
#include "usart.h"
#include "ism330dhcx.h"
#include "MdbsRtu.h"
#include "bduRecord.h"
#include "lwip/netifapi.h"
#include "netdev.h"
#include "commBdu.h"
#include "circle_buff.h"
#include "netdev.h"
#include "algorithmAES.h"
#include "apptest.h"
#include "utils_server.h"
#include "syswatch.h"
#include "utils_heart_beat.h"
#include "utils_rtthread_security_func.h"

#define THREAD_PRIORITY 15
#define THREAD_TIMESLICE 5

static void init_main(void);

rt_uint32_t Iwd_timeout = 10;

//static void checkNetPara(void)
//{
//#if !defined(UNITEST)
//    T_SysPara tSysPara;
//    ip_addr_t ipaddr, netmask, gw;
//    struct netdev *pDev = netdev_get_by_name("e0");

//    GetSysPara(&tSysPara);
//    if (tSysPara.ucLocalIPGetMode)
//    {
//        return;
//    }

//    netifapi_dhcp_stop(netif_default);
//    netdev_dhcp_enabled(pDev, 0);

//    ipaddr.addr = netdev_ipaddr_addr(tSysPara.acLocalIPAddr);
//    gw.addr = netdev_ipaddr_addr(tSysPara.acGateway);
//    netmask.addr = netdev_ipaddr_addr(tSysPara.acMask);
//    netifapi_netif_set_addr(netif_default, &ipaddr, &netmask, &gw);
//#endif
//    return;
//}

#define THREAD_STACK_SIZE_SAMPLE   6144
#define THREAD_STACK_SIZE_LED      2048
#define THREAD_STACK_SIZE_CAN      6144
#define THREAD_STACK_SIZE_COMM     6144
#define THREAD_STACK_SIZE_HISDATA  2048
#define THREAD_STACK_SIZE_GSENSOR  3072
#define THREAD_STACK_SIZE_ALARM    3072
#define THREAD_STACK_SIZE_BATTERY  3072
#define THREAD_STACK_SIZE_COMMBDU  6144
#define THREAD_STACK_SIZE_BACKUP   3072
#define THREAD_STACK_SIZE_RECORD   3072

static char s_thread_stack_sample[THREAD_STACK_SIZE_SAMPLE] RT_SECTION(".sdram_bss");
static char s_thread_stack_led[THREAD_STACK_SIZE_LED] RT_SECTION(".sdram_bss");
static char s_thread_stack_can[THREAD_STACK_SIZE_CAN] RT_SECTION(".sdram_bss");
static char s_thread_stack_comm[THREAD_STACK_SIZE_COMM] RT_SECTION(".sdram_bss");
static char s_thread_stack_hisdata[THREAD_STACK_SIZE_HISDATA] RT_SECTION(".sdram_bss");
static char s_thread_stack_gsensor[THREAD_STACK_SIZE_GSENSOR] RT_SECTION(".sdram_bss");
static char s_thread_stack_alarm[THREAD_STACK_SIZE_ALARM] RT_SECTION(".sdram_bss");
static char s_thread_stack_battery[THREAD_STACK_SIZE_BATTERY] RT_SECTION(".sdram_bss");
static char s_thread_stack_commbdu[THREAD_STACK_SIZE_COMMBDU] RT_SECTION(".sdram_bss");
static char s_thread_stack_backup[THREAD_STACK_SIZE_BACKUP] RT_SECTION(".sdram_bss");
static char s_thread_stack_bdurecord[THREAD_STACK_SIZE_RECORD] RT_SECTION(".sdram_bss");

static server_info_t g_server_group[] = {
    {{{SAMPLE_SERVER_ID,         "Sample",      sizeof(s_thread_stack_sample),    s_thread_stack_sample,    THREAD_PRIORITY  }}, NULL,  SampleInSys},
    {{{LED_SERVER_ID,            "LED",         sizeof(s_thread_stack_led),       s_thread_stack_led,       THREAD_PRIORITY  }}, NULL,  CtrlOut},
    {{{CANCOMM_SERVER_ID ,       "CanComm",     sizeof(s_thread_stack_can),       s_thread_stack_can,       THREAD_PRIORITY  }}, NULL,  Process_CAN_Comm},
    {{{COMM_SERVER_ID ,          "Comm",        sizeof(s_thread_stack_comm),      s_thread_stack_comm,      THREAD_PRIORITY  }}, NULL,  DealComm},
    {{{HISDATA_SERVER_ID,        "HisData",     sizeof(s_thread_stack_hisdata),   s_thread_stack_hisdata,   THREAD_PRIORITY  }}, NULL,  processHisData},
    {{{GSENSOR_SERVER_ID,        "GSensor",     sizeof(s_thread_stack_gsensor),   s_thread_stack_gsensor,   THREAD_PRIORITY  }}, NULL,  Proc_G_Sensor},
    {{{ALARM_SERVER_ID,          "Alarm",       sizeof(s_thread_stack_alarm),     s_thread_stack_alarm,     THREAD_PRIORITY  }}, NULL,  JudgeAlarm},
    {{{BATTERY_MANAGE_SERVER_ID, "Battery",     sizeof(s_thread_stack_battery),   s_thread_stack_battery,   THREAD_PRIORITY  }}, NULL,  BatteryManagement},
    {{{BDUCOMM_SERVER_ID,        "CommBdu",     sizeof(s_thread_stack_commbdu),   s_thread_stack_commbdu,   THREAD_PRIORITY  }}, NULL,  DealBduComm},
    {{{BACKUP_SERVER_ID  ,       "BackUp",      sizeof(s_thread_stack_backup),    s_thread_stack_backup,    THREAD_PRIORITY  }}, NULL,  processBackupData},
    {{{BDU_RECORD_SERVER_ID,     "BduRecord",   sizeof(s_thread_stack_bdurecord), s_thread_stack_bdurecord, THREAD_PRIORITY  }}, NULL,  BduRecordSaver},
};

static void init_main(void) {
    rt_thread_delay(1000);
    initFileSys(); // 文件系统
    InitProConfig();
    InitCRC();
    initHisData();
    InitAesKey();
    InitSysPara();
    InitCan1();
    init_circle_buff();
    InitBduRecordSaver();
    syswatch_init();
    return;
}

int main(void)
{
    init_main();
    create_server(&g_server_group[0], sizeof(g_server_group)/sizeof(g_server_group[0]));
    rt_thread_delay(10000);
    SaveResetToHisOper();
    BeginDownload(FLAG_BACKUP);
//    checkNetPara();
#if !defined(KW_CHECK)
    while (1)
    {
#endif
        rt_thread_delay(100);
        check_heart_beat();
        CheckUpdateTimeOut();
#if !defined(KW_CHECK)
    }
#endif
    return RT_EOK;
}

void jugde_heartbeat_flag(void)
{
    int abnormal_thread_t;
    char reset_reason[20] = {0};
    if (judge_thread_beat_normal(&abnormal_thread_t) == TRUE) {
        reset_thread_beat_flag();
    } else {
        rt_snprintf_s(reset_reason, sizeof(reset_reason), "thread%d wd rst", abnormal_thread_t);
        SaveAction(GetActionId(CONTOL_RST_SYS), reset_reason);
        rt_hw_cpu_reset();
    }
}
