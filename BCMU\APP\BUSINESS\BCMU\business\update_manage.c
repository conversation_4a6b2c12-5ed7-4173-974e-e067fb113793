/*******************************************************************************
  * @file        update_manage.c
  * @version     v1.0.0
  * @copyright   COPYRIGHT &copy; 2023 CSG
  * <AUTHOR>
  * @date        2023-4-6
  * @brief
  * @attention
  * Modification History
  * DATE         DESCRIPTION
  * ------------------------
  * - 2023-4-6  dongmengling Created
*******************************************************************************/
#include <string.h>
#include <rtthread.h>
#include <rtdevice.h>
#include <stdlib.h>
#include "update_manage.h"
#include "msg.h"
#include "cmd.h"
#include "device_type.h"
#include "dev_bmu.h"
#include "sps.h"
#include "storage.h"
#include "protocol_layer.h"
#include "update_south_dev.h"
#include "pin_define.h"
#include "utils_data_transmission.h"
//弃用spi_flash
//#include "drv_spi_flash.h"
//#include "spi_flash.h"
//#include "sfud.h"
//#include "spi_ctrl.h"
#include "pin_ctrl.h"  
#include "update_download_manage.h"
#include "utils_rtthread_security_func.h"

#define BMU_UPDATE_FILE_NAME     "bmu_app.bin"
#define BMU_UPDATE_FILE_PREFIX_  "bmu_app."
#define BCMU_UPDATE_FILE_NAME    "bcmu_app.bin"
#define BCMU_UPDATE_FILE_PREFIX_ "bcmu_app."


extern file_manage_t file_manage;
Static int parse_update_data_trig(void* dev_inst, void *cmd_buf);
Static int parse_update_data_trig(void* dev_inst, void *cmd_buf);
Static int pack_update_data_trig(void* dev_inst, void *cmd_buf);
Static int parse_update_data(void* dev_inst, void *cmd_buf);
static int pack_update_data(void* dev_inst, void *cmd_buf);
static int parse_update_data_interrupt(void* dev_inst, void *cmd_buf);
static int pack_update_data_interrupt(void* dev_inst, void *cmd_buf);
static int parse_update_trig(void* dev_inst, void *cmd_buf);
static int pack_update_trig(void* dev_inst, void *cmd_buf);
Static int get_app_download_rtn(void *cmd_buf);
static int check_first_frame(unsigned char dev_type);


Static trig_ctr_inf_t s_trig_ctr_inf;
static trsdata_ctr_inf_t s_trsdata_ctr_inf;
Static bcmu_update_manage_t s_bcmu_update_manage = {update_init, 0, 0, PIN_HIGH, PIN_HIGH};
static unsigned char interrupt_rtn = BOTTOM_RTN_APP_CORRECT;


static cmd_handle_register_t update_cmd_handle[] = {
    {DEV_BSMU, BSMU_UPDATE_DATA_TRIG,      CMD_TYPE_NO_POLL, parse_update_data_trig, pack_update_data_trig},
    {DEV_BSMU, BSMU_UPDATE_DATA,           CMD_TYPE_NO_POLL, parse_update_data, pack_update_data},
    {DEV_BSMU, BSMU_UPDATE_DATA_INTERRUPT, CMD_TYPE_NO_POLL, parse_update_data_interrupt, pack_update_data_interrupt},
    {DEV_BSMU, BSMU_UPDATE_TRIG,           CMD_TYPE_NO_POLL, parse_update_trig, pack_update_trig},
    /*{DEV_BCMU, BMU_UPDATE_DATA_TRIG,      CMD_TYPE_NO_POLL, parse_update_data_trig, pack_update_data_trig},
    {DEV_BCMU, BMU_UPDATE_DATA,           CMD_TYPE_NO_POLL, parse_update_data, pack_update_data},
    {DEV_BCMU, BMU_UPDATE_DATA_INTERRUPT, CMD_TYPE_NO_POLL, parse_update_data_interrupt, pack_update_data_interrupt},*/
    //{DEV_BCMU, BMU_UPDATE_TRIG,           CMD_TYPE_NO_POLL, parse_update_trig, pack_update_trig},
    /*{DEV_BMU,  BMU_UPDATE_DATA_TRIG,       CMD_TYPE_NO_POLL, parse_update_data_trig, pack_update_data_trig},
    {DEV_BMU,  BMU_UPDATE_DATA,            CMD_TYPE_NO_POLL, parse_update_data, pack_update_data},
    {DEV_BMU,  BMU_UPDATE_DATA_INTERRUPT,  CMD_TYPE_NO_POLL, parse_update_data_interrupt, pack_update_data_interrupt},*/
};


Static int parse_update_data_trig(void* dev_inst, void *cmd_buf) {

    s_trig_ctr_inf.rtn = get_app_download_rtn(cmd_buf);
    
    if (BOTTOM_RTN_APP_CORRECT == s_trig_ctr_inf.rtn) {
        s_bcmu_update_manage.suc_counts++;
        if (s_bcmu_update_manage.suc_counts >= TRIGGER_COUNTER) {
            rt_memset_s(&s_trsdata_ctr_inf, sizeof(s_trsdata_ctr_inf), 0, sizeof(s_trsdata_ctr_inf));
            s_bcmu_update_manage.update_state = update_trans_data;
            s_bcmu_update_manage.di_value = PIN_HIGH;
            s_bcmu_update_manage.do_value = PIN_HIGH;
            set_pin(ADDR_ASSSIGN_DO_PIN, PIN_HIGH);        //将下一个BCMU di拉高，todo 需要确定ADDR_ASSSIGN_DO_PIN
            begin_download(FLAG_CAN_IAP);//开始升级
        }
    } else {
        s_bcmu_update_manage.suc_counts = 0;
    }

    return SUCCESSFUL;
}

Static int get_app_download_rtn(void *cmd_buf) {
    bottom_comm_cmd_head_t* proto_head = NULL;
    cmd_buf_t* trig_cmd_buf = (cmd_buf_t*)cmd_buf;
    unsigned char dev_type = 0;

    // 入参校验
    if ( trig_cmd_buf == NULL || trig_cmd_buf->buf == NULL) {
        return FAILURE;
    }

    proto_head = (bottom_comm_cmd_head_t*)trig_cmd_buf->cmd->req_head;
    dev_type = proto_head->res1.dst_dev;
    char *p_file_name = s_trig_ctr_inf.file_name;
    unsigned char *p = trig_cmd_buf->buf;

    if (proto_head->append != 0xAA55)
    {
        return DownloadFrameErr;
    }

    if (trig_cmd_buf->data_len == UPDATE_FILE_NAME_LEN)
    {
        switch(dev_type) {
            case BOTTOM_BCMU_TYPE:
                rt_snprintf_s(p_file_name, UPDATE_FILE_NAME_LEN, BCMU_UPDATE_FILE_NAME);
                if (strncmp((char*)p, BCMU_UPDATE_FILE_NAME, UPDATE_FILE_NAME_LEN) == 0) {
                    return BOTTOM_RTN_APP_CORRECT;
                }
                break;
            case BOTTOM_BMU_TYPE:
                rt_snprintf_s(p_file_name, UPDATE_FILE_NAME_LEN, BMU_UPDATE_FILE_NAME);
                if (strncmp((char*)p, BMU_UPDATE_FILE_NAME, UPDATE_FILE_NAME_LEN) == 0){
                    return BOTTOM_RTN_APP_CORRECT;
                }
                break;
        }
    }
    return DownFileNameErr;
}



Static int pack_update_data_trig(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* trig_cmd_buf = (cmd_buf_t*)cmd_buf;
    bottom_comm_cmd_head_t* proto_head = NULL;

    // 入参校验
    if ( trig_cmd_buf == NULL || trig_cmd_buf->buf == NULL) {
        return FAILURE;
    }
    proto_head = (bottom_comm_cmd_head_t*)trig_cmd_buf->cmd->ack_head;
    proto_head->append = 0x55AA;

    if(s_trig_ctr_inf.rtn == DownloadFrameErr) {
        //附加码错误
        trig_cmd_buf->data_len = 0;
        trig_cmd_buf->rtn = BOTTOM_RTN_CMD_ERROR;
        return SUCCESSFUL;
    } else {
        //文件名错误或正确，回复正确文件名
        trig_cmd_buf->data_len = UPDATE_FILE_NAME_LEN;
        rt_memcpy_s(trig_cmd_buf->buf, trig_cmd_buf->buflen, &s_trig_ctr_inf.file_name[0], trig_cmd_buf->data_len);
    }
    return SUCCESSFUL;
}



Static int parse_update_data(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    unsigned short frm_no = 0;
    bottom_comm_cmd_head_t* proto_head = NULL;
    unsigned short copy_len = 0;
    part_data_t part_data = {0};
    trsdata_ctr_inf_t* trsdata_ctr_inf = NULL;
    unsigned char dev_type = 0;

     // 入参校验
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL) {
        return FAILURE;
    }

    proto_head = (bottom_comm_cmd_head_t*)tran_cmd_buf->cmd->req_head;
    frm_no = proto_head->append;
    dev_type = proto_head->res1.dst_dev;
    trsdata_ctr_inf = &s_trsdata_ctr_inf;

    if (s_bcmu_update_manage.update_state == update_interrupt) {
        trsdata_ctr_inf->rtn = BOTTOM_RTN_APP_CORRECT;
        return SUCCESSFUL;
    }

    // 判断是首发针还是更新过程数据帧
    if (frm_no == 0) {
        if(tran_cmd_buf->data_len < UPDATE_FIRST_FRAME_LEN) {
            trsdata_ctr_inf->rtn = DownloadFrameErr;
            return SUCCESSFUL;
        }
        
        copy_len = sizeof(first_frame_inf_t);
        if ( copy_len > tran_cmd_buf->data_len )
            copy_len = tran_cmd_buf->data_len;
        
        trsdata_ctr_inf->first_frame_inf.data_lenth_per_frame = get_int16_data(&tran_cmd_buf->buf[0]);
        trsdata_ctr_inf->first_frame_inf.total_frame_num = get_int16_data(&tran_cmd_buf->buf[2]);
        trsdata_ctr_inf->first_frame_inf.total_file_length = get_ulong_data(&tran_cmd_buf->buf[4]);
        rt_memcpy_s(trsdata_ctr_inf->first_frame_inf.file_name, UPDATE_FILE_NAME_LEN, &tran_cmd_buf->buf[8], UPDATE_FILE_NAME_LEN);
        rt_memcpy_s(trsdata_ctr_inf->first_frame_inf.file_time, UPDATE_FILE_NAME_LEN, &tran_cmd_buf->buf[8+UPDATE_FILE_NAME_LEN], UPDATE_FILE_TIME_LEN);
        trsdata_ctr_inf->first_frame_inf.file_check = get_int16_data(&tran_cmd_buf->buf[8+UPDATE_FILE_NAME_LEN + UPDATE_FILE_TIME_LEN]);
        //校验首发帧
        if (check_first_frame(dev_type) != SUCCESSFUL) {
            trsdata_ctr_inf->rtn = DownloadFrameErr;
            return SUCCESSFUL;
        }
        //初始化文件存储偏移
        trsdata_ctr_inf->data_offset = 0;
        trsdata_ctr_inf->cur_frame_no = frm_no + 1;
        trsdata_ctr_inf->rtn = BOTTOM_RTN_APP_CORRECT;
        
    } else if(frm_no == trsdata_ctr_inf->cur_frame_no && frm_no <= MAX_PACKET_NUM){
        //RETURN_VAL_IF_FAIL(frm_no < trsdata_ctr_inf->first_frame_inf.total_frame_num, FAILURE);
        
        //保存传输信息
        trsdata_ctr_inf->req_frame_no = frm_no;

        //存储数据帧
        part_data.buff = (unsigned char*)tran_cmd_buf->buf;
        part_data.len = tran_cmd_buf->data_len;
        part_data.offset = trsdata_ctr_inf->data_offset;
        rt_snprintf_s(part_data.name, sizeof(part_data.name), DOWNLOAD_PART);
        if (SUCCESSFUL != storage_process(&part_data, write_opr)) {
            //存储失败
            trsdata_ctr_inf->rtn = DownloadFrameErr;
            return SUCCESSFUL;
        }
        trsdata_ctr_inf->cur_frame_no = frm_no + 1;
        trsdata_ctr_inf->data_offset += tran_cmd_buf->data_len;
        trsdata_ctr_inf->rtn = BOTTOM_RTN_APP_CORRECT;
    } else {
        trsdata_ctr_inf->rtn = DownloadFrameErr;
    }
    
    return SUCCESSFUL;
}

/**
 * @FunctionName: check_first_frame
 * @brief: 校验首发帧信息
 * @details:
 * @Author:       dongmengling
 * @DateTime:     2023年4月6日T16:54:11+0800
 * @Purpose:      
 * @param:        dev_type设备类型
 * @return: 校验结果 SUCCESSFUL 或 FAILURE
 */
static int check_first_frame(unsigned char dev_type) {
    unsigned short total_frame_num = 0;
    unsigned short data_lenth_per_frame = 0;

    data_lenth_per_frame = s_trsdata_ctr_inf.first_frame_inf.data_lenth_per_frame;
    //每帧长度校验
    if (data_lenth_per_frame != MSC_MAX_PACKET_1 && data_lenth_per_frame != MSC_MAX_PACKET_2) {
        return FAILURE;
    }
    //文件名校验
    switch(dev_type) {
        case BOTTOM_BMU_TYPE:
            if (strncmp(s_trsdata_ctr_inf.first_frame_inf.file_name, BMU_UPDATE_FILE_NAME, UPDATE_FILE_NAME_LEN) != 0) {
                return FAILURE;
            }
            break;
        case BOTTOM_BCMU_TYPE:
            if (strncmp(s_trsdata_ctr_inf.first_frame_inf.file_name, BCMU_UPDATE_FILE_NAME, UPDATE_FILE_NAME_LEN) != 0) {
                return FAILURE;
            }
            break;
        default:
            break;
    }
    //总帧数校验
    total_frame_num = s_trsdata_ctr_inf.first_frame_inf.total_file_length/data_lenth_per_frame +1;
    if (s_trsdata_ctr_inf.first_frame_inf.total_file_length % data_lenth_per_frame > 0) {
        total_frame_num += 1;
    }
    if (s_trsdata_ctr_inf.first_frame_inf.total_frame_num != total_frame_num) {
        return FAILURE;
    }
    return SUCCESSFUL;
}


int check_file_crc(void) {
    unsigned short crc = 0;
    part_data_t part_data = {0};
    unsigned int offset = 0;
    unsigned int i = 0;
    unsigned int j = 0;
    unsigned char* buff = NULL;
    unsigned short data_lenth_per_frame = 0;
    unsigned short data_lenth_last_frame = 0;
    
    if (s_trsdata_ctr_inf.data_offset == 0 || s_trsdata_ctr_inf.first_frame_inf.total_file_length != s_trsdata_ctr_inf.data_offset) {
        return FAILURE;
    }

    data_lenth_per_frame = s_trsdata_ctr_inf.first_frame_inf.data_lenth_per_frame;

    buff = (unsigned char*)rt_malloc(data_lenth_per_frame);
    RETURN_VAL_IF_FAIL(buff != NULL, FAILURE);

    for(i = 0; i < s_trsdata_ctr_inf.first_frame_inf.total_frame_num - 2; i++) {
        //读取数据帧
        part_data.buff = buff;
        part_data.len = data_lenth_per_frame;
        part_data.offset = offset;
        rt_snprintf_s(part_data.name, sizeof(part_data.name), DOWNLOAD_PART);
        if (SUCCESSFUL != storage_process(&part_data, read_opr)) {
            //读取失败
            rt_free(buff);
            return FAILURE;
        }
        offset += data_lenth_per_frame;
        for (j = 0; j < data_lenth_per_frame; j++) {
            crc += buff[i];
        }
    }

    //最后一帧处理
    data_lenth_last_frame = s_trsdata_ctr_inf.first_frame_inf.total_file_length % data_lenth_per_frame;
    if (data_lenth_last_frame == 0) {
        data_lenth_last_frame = data_lenth_per_frame;
    }
    
    part_data.buff = buff;
    part_data.len = data_lenth_last_frame;
    part_data.offset = offset;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), DOWNLOAD_PART);
    if (SUCCESSFUL != storage_process(&part_data, read_opr)) {
        //读取失败
        rt_free(buff);
        return FAILURE;
    }
    for (i = 0; i < data_lenth_last_frame; i++) {
        crc += buff[i];
    }
    rt_free(buff);

    if (crc != s_trsdata_ctr_inf.first_frame_inf.file_check) {
        return FAILURE;
    }
    return SUCCESSFUL;
}


static int pack_update_data(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    bottom_comm_cmd_head_t* proto_head = NULL;
    
    // 入参校验
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL) {
        return FAILURE;
    }

    if(s_trsdata_ctr_inf.rtn == DownloadFrameErr) {
        //帧错误
        tran_cmd_buf->data_len = 0;
        tran_cmd_buf->rtn = BOTTOM_RTN_CMD_ERROR;
    } else {
        tran_cmd_buf->rtn = BOTTOM_RTN_APP_CORRECT;
    }
    proto_head = (bottom_comm_cmd_head_t*)tran_cmd_buf->cmd->ack_head;
    proto_head->append = s_trsdata_ctr_inf.cur_frame_no;
    return SUCCESSFUL;
}

static int parse_update_data_interrupt(void* dev_inst, void *cmd_buf) {
    bottom_comm_cmd_head_t* proto_head = NULL;
    cmd_buf_t* trig_cmd_buf = (cmd_buf_t*)cmd_buf;

    // 入参校验
    if ( trig_cmd_buf == NULL || trig_cmd_buf->buf == NULL) {
        return FAILURE;
    }

    proto_head = (bottom_comm_cmd_head_t*)trig_cmd_buf->cmd->req_head;
    unsigned char discard_data = trig_cmd_buf->buf[0];

    if (proto_head->append != 0xAA55) {
        interrupt_rtn = DownloadFrameErr;
        return SUCCESSFUL;
    }

    if (trig_cmd_buf->data_len == INTERRUPT_DATA_LEN) {
        if(INTERRUPT_DISCARD == discard_data) {
            //删除首发帧及保存的文件

        }
        s_bcmu_update_manage.update_state = update_interrupt;
        interrupt_rtn = BOTTOM_RTN_APP_CORRECT;
    } else {
        interrupt_rtn = DownloadFrameErr;
    }

    return SUCCESSFUL;
}

static int pack_update_data_interrupt(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    bottom_comm_cmd_head_t* proto_head = NULL;
    
    // 入参校验
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL) {
        return FAILURE;
    }

    if(interrupt_rtn == DownloadFrameErr) {
        //帧错误
        tran_cmd_buf->data_len = 0;
        tran_cmd_buf->rtn = BOTTOM_RTN_CMD_ERROR;
    } else {
        tran_cmd_buf->rtn = BOTTOM_RTN_APP_CORRECT;
    }
    proto_head = (bottom_comm_cmd_head_t*)tran_cmd_buf->cmd->ack_head;
    proto_head->append = 0x55AA;

    return SUCCESSFUL;
}



static int parse_update_trig(void* dev_inst, void *cmd_buf) {

    s_trig_ctr_inf.rtn = get_app_download_rtn(cmd_buf);
    
    return SUCCESSFUL;
}

static int pack_update_trig(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* trig_cmd_buf = (cmd_buf_t*)cmd_buf;
    bottom_comm_cmd_head_t* proto_head = NULL;

    // 入参校验
    if ( trig_cmd_buf == NULL || trig_cmd_buf->buf == NULL) {
        return FAILURE;
    }

    proto_head = (bottom_comm_cmd_head_t*)trig_cmd_buf->cmd->ack_head;
    proto_head->append = 0x55AA;

    if(s_trig_ctr_inf.rtn == DownloadFrameErr) {
        //附加码错误
        trig_cmd_buf->data_len = 0;
        trig_cmd_buf->rtn = BOTTOM_RTN_CMD_ERROR;
        return SUCCESSFUL;
    } else {
        //文件名错误或正确，回复正确文件名
        trig_cmd_buf->data_len = UPDATE_FILE_NAME_LEN;
        rt_memcpy_s(trig_cmd_buf->buf, trig_cmd_buf->data_len, s_trig_ctr_inf.file_name, trig_cmd_buf->data_len);
    }

    return SUCCESSFUL;
}


void update_manage_init(void) {
    short i = 0;
    for(i = 0; i < sizeof(update_cmd_handle)/sizeof(update_cmd_handle[0]); i++) {
        register_cmd_handle(&update_cmd_handle[i]);
    }
    
    init_south_update();
}

trig_ctr_inf_t get_trig_info(void) {
    return s_trig_ctr_inf;
}


trsdata_ctr_inf_t get_trsdata_ctr_info(void) {
    return s_trsdata_ctr_inf;
}

bcmu_update_manage_t* get_bcmu_update_manage(void) {
    return &s_bcmu_update_manage;
}


void fill_south_update_info()
{   
    update_file_manage_t update_file_manage = {0};
    read_download_tmpInfo(&update_file_manage);
    file_manage.file_attr.data_lenth_per_frame = MSC_MAX_PACKET_2;
    file_manage.file_attr.total_file_length = update_file_manage.file_info.total_leng;
    file_manage.file_attr.total_frame_num = ((update_file_manage.file_info.total_leng + MSC_MAX_PACKET_2 - 1) / MSC_MAX_PACKET_2) + 1;
    rt_strncpy_s(file_manage.file_attr.file_name, FILE_NAME_LEN, update_file_manage.file_info.file_name, FILE_NAME_LEN - 1);
    rt_strncpy_s(file_manage.file_attr.file_time, FILE_TIME_LEN, update_file_manage.file_info.file_time, FILE_TIME_LEN - 1);

    file_manage.file_attr.file_check = update_file_manage.file_info.filecrc;
    // rt_kprintf("fill_south_update_info|file_len:%d, total_frame_num:%d, file_name:%d, file_time；%d\n",
    //            g_download_file_size, file_manage.file_attr.total_frame_num, file_manage.file_attr.file_name, file_manage.file_attr.file_time,
    //            file_manage.file_attr.file_check);
}

int south_dev_update(unsigned char dev_id, unsigned char dev_addr, unsigned char dev_num) {
    int ret = 0;
    update_file_manage_t update_file_manage = {0};

    fill_south_update_info();
    ret = start_south_update(&file_manage.file_attr, dev_id, dev_addr, dev_num); 

    //传输结束后清除文件信息数据
    read_download_tmpInfo(&update_file_manage);
    rt_memset_s(&update_file_manage.file_info, sizeof(update_file_attr_t), 0, sizeof(update_file_attr_t));
    write_download_tmpInfo(&update_file_manage);// 完成标记,文件传输完成
    return ret;


}
