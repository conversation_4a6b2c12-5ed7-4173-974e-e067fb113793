
#ifndef _NORTH_UPDATE_UTILS_H_
#define _NORTH_UPDATE_UTILS_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "north_data_utils.h"

#define FLAG_APP_UPDATE_APP    1
#define FILE_NAME_LEN          40
#define FILE_TIME_LEN          20
#define UPDATE_FILE_TIME_LEN                    20
#define UPDATE_FIRST_FRAME_LEN 72
#define MSC_MAX_PACKET_1       128
#define MSC_MAX_PACKET_2       256
#define MAX_PACKET_NUM         3584 //升级包最大帧数，可根据分配空间定
#define UPDATE_FILE_EXTEND_NAME_LEN             256

#define SOUTH_UPDATE_WAITE              120000
#define MONITER_UPDATE_WAITE            180000

#pragma pack(1)
typedef struct
{
    char   file_time[UPDATE_FILE_TIME_LEN];             ///< 下载时间
    char   file_name[UPDATE_FILE_EXTEND_NAME_LEN];      ///< 文件名称
    unsigned char   param_type;                                  ///< 参数类型
    unsigned int    total_leng;                                 ///< 文件总长度
    unsigned short  total_frames;                                ///< 总帧数量int
    unsigned int    filecrc;                                    ///< 文件CRC
    unsigned short  resv;
}update_file_attr_t;

typedef struct
{
    update_file_attr_t     file_info;
    unsigned char   sys_run_flag;
    unsigned char   backup_flag;
    unsigned char   update_flag;
    unsigned char   count;                                        ///控制boot侧升级逻辑的count
    unsigned char   rtn;  
    unsigned char   update_status;   
    unsigned int    data_offset;                                 ///<  文件存储偏移
    unsigned int    backup_frame_leng;                                
    unsigned short  cur_frame_no;
    unsigned short  restore_frame_num;
    unsigned short  crc;
}update_file_manage_t;

//升级或备份文件记录管理
typedef struct
{
    unsigned short    data_lenth_per_frame;
    unsigned short    total_frame_num;
    unsigned int      total_file_length;
    char     file_name[FILE_NAME_LEN];
    char     file_time[FILE_TIME_LEN];
    unsigned short    file_check;
    unsigned short    resv;
}file_attr_t;

typedef struct
{
    file_attr_t     file_attr;
    unsigned char   flag;
    unsigned char   update_addr;
    unsigned short  conunter;
    unsigned short  backup_frame_num;
    unsigned short  crc;
}file_manage_t;
typedef struct {
    unsigned char file_type;
    unsigned char need_update_slave_num;
    unsigned char slave_addr[MAX_PARALELL_NUM - 1];
    unsigned char cur_slave_addr;
    unsigned char is_master_update;
    unsigned char master_addr;
    unsigned char update_status[MAX_PARALELL_NUM];
    unsigned int  file_size;
    unsigned short file_crc;
}parallel_update_info_t;

typedef enum
{
    MONITER_FILE  = 0,
    MASTER_CTRL_FILE,
    AUXI_CTRL_FILE,
    CPLD_FILE,
    MQTT_CERT,
    DIFF_FILE,
} update_file_type;

typedef enum
{
    NO_UPDATEING  = 0,
    UPDATE_SUCCESS,
    UPDATE_FAILURE,
    UPDATEING,
    DIFF_UPDATE_FAILURE,
    TRANSFER_SUCCESS,
    TRANSFER_FAILURE,
    UPDATE_END,
}updata_status;

typedef enum {
    UPDATE_SOUTH_REMOTE = 0, // 远程升级功率
    UPDATE_PV_REMOTE,        // 远程升级监控
    UPDATE_MQTT,             // mqtt升级
    UPDATE_MODE_MAX,
}update_mode_t;

typedef struct 
{
    short (*start_south_update)(file_attr_t* file_info, unsigned char dev_id, unsigned char dev_addr, unsigned char dev_num);
}south_update_func_t;
#pragma pack()

int deal_update_timer();
unsigned char* get_concurrent_addr();
parallel_update_info_t* get_parallel_update_info();
char get_parallel_update_shield_com_flag();
void set_parallel_update_shield_com_flag(char flag);
void write_download_tmpInfo(update_file_manage_t* tFileManage);
void read_download_tmpInfo(update_file_manage_t* tFileManage);
rt_timer_t get_mqtt_update_timer();
int set_mqtt_update_timer(rt_timer_t update_timer);
void is_need_restart();
int deal_update(int file_type, unsigned int file_size, unsigned int file_crc);
int set_south_update_func(short (*start_south_update)(file_attr_t* , unsigned char , unsigned char , unsigned char ));

#ifdef __cplusplus
}
#endif      //< __cplusplus

#endif      //< _NORTH_UPDATE_UTILS_H_