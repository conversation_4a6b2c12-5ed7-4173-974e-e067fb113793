#include <string.h>
#include <stdlib.h>
#include <rtthread.h>
#include "north_main.h"
#include "io_control.h"
#include "Menu.h"
#include "const_define_in.h"
#include "utils_heart_beat.h"
#include "data_type.h"
#include "north_main.h"
#include "linklayer.h"
#include "para_id_in.h"
#include "para_manage.h"
#include "dev_dcmu.h"

static dev_inst_t* dev_dcmu = NULL;
Static int s_com_time_out_count_usart2 = 0;
Static unsigned char s_apptest_usart_test_flag = FALSE; // 是否正在执行apptest协议的串口回环测试
static dev_inst_t* s_north_dev_inst[PROTOCOL_INDEX_MAX] = {NULL};

static unsigned char s_dev_type_usart2[] = {
    DEV_NORTH_1104,
    DEV_NORTH_1363,
    DEV_REMOTE_DOWNLOAD,
};

Static msg_map north_msg_map_usart2[] =
{
    {0,NULL},//临时添加解决编译问题
};

Static int trace_protocol_debug(int loop, cmd_buf_t* cmd_buff);

Static dcmu_mgr_t * init_thread_data_usart2(link_inst_t* link_inst, unsigned char mod_id) {
    dcmu_mgr_t* dcmu_mgr = NULL;

    dcmu_mgr = rt_malloc(sizeof(dcmu_mgr_t));
    if (dcmu_mgr == NULL)
        return NULL;
    
    dcmu_mgr->cmd_buff = rt_malloc(sizeof(cmd_buf_t));
    if (dcmu_mgr->cmd_buff == NULL) {
        goto MGR_FREE;
    }

    dcmu_mgr->cmd_buff->addr_dev_data = 0x01;
    dcmu_mgr->link_inst = link_inst;
    return dcmu_mgr;
     
MGR_FREE:
    free(dcmu_mgr);
    return NULL;
}

Static int trace_protocol_debug(int loop, cmd_buf_t* cmd_buff) {
    s1363_cmd_head_t* s1363_cmd_head = NULL;
    char buff[MAX_LETTERS];

    rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));
    RETURN_VAL_IF_FAIL(loop < ARR_SIZE(s_dev_type_usart2), FAILURE);

    if (s_dev_type_usart2[loop] == DEV_REMOTE_DOWNLOAD) {
        rt_snprintf_s( buff, sizeof(buff), "COM1Pro=remotedownload");
        SetTraceStr( 8, buff );
        return SUCCESSFUL;
    }else if (s_dev_type_usart2[loop] == DEV_NORTH_1104) {
        rt_snprintf_s( buff, sizeof(buff), "COM1Pro=1104");
        SetTraceStr( 8, buff );
    } else if(s_dev_type_usart2[loop] == DEV_NORTH_1363) {
        rt_snprintf_s( buff, sizeof(buff), "COM1Pro=1363");
        SetTraceStr( 8, buff );
    } 

    RETURN_VAL_IF_FAIL(cmd_buff != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(cmd_buff->cmd != NULL, FAILURE);

    s1363_cmd_head = (s1363_cmd_head_t*)cmd_buff->cmd->req_head;

    RETURN_VAL_IF_FAIL(s1363_cmd_head != NULL, FAILURE);
    

    rt_snprintf_s( buff, sizeof(buff), "CID1=%2xH CID2=%2xH", s1363_cmd_head->cid1, s1363_cmd_head->cid2 );
    SetTraceStr( 9, buff );
    return SUCCESSFUL;
}

Static int dcmu_boot_softver(void)
{
    boot_info_t boot_info = {0};
    rt_memcpy_s(&boot_info, sizeof(boot_info_t), BOOT_DATA_ADDRESS, sizeof(boot_info_t));
    set_one_data(DCMU_DATA_ID_BOOT_VER, boot_info.ver);
    set_one_data(DCMU_DATA_ID_BOOT_VER_DATE , boot_info.date);
    return SUCCESSFUL;
}

Static int handle_received_data_usart2(dcmu_mgr_t* north_mgr) {

    for(int loop = 0; loop < ARR_SIZE(s_dev_type_usart2); loop++)
    {
        if(SUCCESSFUL == protocol_parse_recv(s_north_dev_inst[loop], north_mgr->cmd_buff))
        {
            trace_protocol_debug(loop, north_mgr->cmd_buff);
            led_set_com_by_status(1);
            cmd_send(s_north_dev_inst[loop], north_mgr->cmd_buff);
            led_set_com_by_status(0);
            rt_sem_clear_value(&(north_mgr->link_inst->rx_sem));
            break;
        }
    }
    return 0;
}

void* init_usart2_comm(void * param)
{
    server_info_t *server_info = (server_info_t *)param;
    int loop = 0;

    for(; loop < ARR_SIZE(s_dev_type_usart2); loop ++)
    {
        dev_dcmu = init_dev_inst(s_dev_type_usart2[loop]);
        RETURN_VAL_IF_FAIL(dev_dcmu != NULL, NULL);
        s_north_dev_inst[loop] = dev_dcmu;
    }
    dcmu_boot_softver();
    server_info->server.server.map_size = sizeof(north_msg_map_usart2) / sizeof(msg_map);
    register_server_msg_map(north_msg_map_usart2, server_info);
    dcmu_update_baudrate();
    return init_thread_data_usart2(dev_dcmu->dev_type->link_inst, MOD_DCMU_NORTH);
}

/* 收发线程 */
void usart2_comm_thread(void *param) {
    dcmu_mgr_t* north_mgr = (dcmu_mgr_t*)param;
    thread_monitor_register("usart2");

    while (is_running(TRUE)) {
        thread_monitor_update_heartbeat();

        // 如果正在进行apptest协议的串口回环测试，则跳过中断，避免影响测试结果
        if(s_apptest_usart_test_flag == TRUE)
        {
            s_com_time_out_count_usart2 = 0;
            rt_thread_mdelay(10);  // 避免空转
            continue;
        }
        if (RT_EOK == rt_sem_take(&(north_mgr->link_inst->rx_sem), THREAD_TICK * 5)) {
            s_com_time_out_count_usart2 = 0;
            if (FAILURE == linker_recv(s_north_dev_inst[0], north_mgr->cmd_buff)) {
                continue;
            }
            handle_received_data_usart2(north_mgr);
        } 
        if(s_com_time_out_count_usart2 > 10) {
            s_com_time_out_count_usart2 = 0;
            north_mgr->link_inst->r_cache.index = 0;
            north_mgr->link_inst->r_cache.len = 0;
        }
        s_com_time_out_count_usart2++;
    }
}

unsigned char set_apptest_usart_test_flag(unsigned char flag_set)
{
    s_apptest_usart_test_flag = flag_set;
    return SUCCESSFUL;
}

