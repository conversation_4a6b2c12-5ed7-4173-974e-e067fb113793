#ifndef _SAMPLE_H
#define _SAMPLE_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include <stdio.h>
#include <rtthread.h>
#include <rtdevice.h>
#include "device_common.h"
#include "drv_adc_TOV.h"
#include "his_record.h"
#include "softbus.h"
#include "utils_time.h"
#include "utils_math.h"

#define MAX_SAMPLE_DATA_SAVE_NUM        10        //最大保存次数
#define LOW_VOL_CHECK_POINT_TIMES       4         //低电压检测次数

#define CHANNEL_TOTAL_NUM               10


#define LED_BLINK_COUNT                 1000//(5000/(DMA_TRANS_CNT * 10/1000))  (一次采样10us，采样500次为5000us = 5ms)（led需要持续闪烁5s）

#define MQTT_UPFILE_TIME_10MIN_COUNT    (10 * 60 * 1000/20) //开启采样每5ms一个循环，10min的循环次数
#define LOW_VOL_START_SAMPLE_COUNT      (60 * 60 * 1000/20)//关闭采样每20ms一个循环，1小时的循环次数


#define AC_VOLT_COMPRESSION_RATIO       0.00052184         //(10 *8 * 3) / (51.1 * 1000 * 9 + 10)交流电压缩小比例
#define AC_N_VOLT_COMPRESSION_RATIO     0.0009393         //(10 *8 * 3) / (51.1 * 1000 * 5 + 10)交流电压缩小比例
#define DC2500_VOLT_COMPRESSION_RATIO   0.0005218         //(10 *8 * 3) / (100 * 1000 * 3 + 51.1 * 1000 * 3+ 10)直流2500V电压缩小比例
#define DC300_VOLT_COMPRESSION_RATIO    0.00469575          //(10 *8 * 3) / (51.1 * 1000 * 3 + 10)直流300V电压缩小比例
#define CURR_COMPRESSION_RATIO          0.0239165         //(240 * 4.01) / (10 * 1000 * 4 + 240)电流缩小比例
#define VOLT_BASE                       1.5               //电压基准值

#define ADC_SAMPLE_FREQ_MIN             1           //AD采样最小周期 1   * 10us
#define ADC_SAMPLE_FREQ_MAX             100         //AD采样最大周期 100 * 10us

// 定义一个结构体来存储阈值和标志
typedef struct {
    unsigned short *buf;
    unsigned short threshold_up;
    unsigned short threshold_down;
    unsigned char valid_flag;
    unsigned short cause;
}threshold_check_t;

typedef struct {
    unsigned char  phase_a_vol_valid_flag;
    unsigned char  phase_b_vol_valid_flag;
    unsigned char  phase_c_vol_valid_flag;
    unsigned char  phase_n_vol_valid_flag;
    unsigned char  phase_a_curr_valid_flag;
    unsigned char  phase_b_curr_valid_flag;
    unsigned char  phase_c_curr_valid_flag;
    unsigned char  phase_n_curr_valid_flag;
    unsigned char  phase_dc_vol_valid_flag;
    unsigned char  phase_dc_curr_valid_flag;
} sample_data_valid_t;


typedef struct {
    unsigned int msg_id;
    void (*handle)(_rt_msg_t curr_msg);
}sample_msg_handle_process_t;


typedef struct {
    unsigned short  adc_a_ac_high_up_vol;        //录波触发电压上限
    unsigned short  adc_a_ac_high_down_vol;
    unsigned short  adc_b_ac_high_up_vol;
    unsigned short  adc_b_ac_high_down_vol;
    unsigned short  adc_c_ac_high_up_vol;
    unsigned short  adc_c_ac_high_down_vol;
    unsigned short  adc_n_ac_high_up_vol;
    unsigned short  adc_n_ac_high_down_vol;

    unsigned short  adc_a_ac_low_vol;        //录波触发电压下限
    unsigned short  adc_b_ac_low_vol;        //录波触发电压下限
    unsigned short  adc_c_ac_low_vol;        //录波触发电压下限

    unsigned short  adc_a_ac_high_up_curr;        //录波触发电流上限
    unsigned short  adc_a_ac_high_down_curr;
    unsigned short  adc_b_ac_high_up_curr;
    unsigned short  adc_b_ac_high_down_curr;
    unsigned short  adc_c_ac_high_up_curr;
    unsigned short  adc_c_ac_high_down_curr;
    unsigned short  adc_n_ac_high_up_curr;
    unsigned short  adc_n_ac_high_down_curr;

    unsigned short  adc_dc_high_up_vol;           //录波触发电压上限
    unsigned short  adc_dc_high_down_vol;
    unsigned short  adc_dc_low_up_vol;               //录波触发电压下限
    unsigned short  adc_dc_low_down_vol;
    unsigned short  adc_dc_high_up_curr;             //录波触发电流上限
    unsigned short  adc_dc_high_down_curr;

    unsigned short  adc_ac_low_vol_dev;             //交流欠压偏差
    unsigned short  adc_dc_low_vol_dev;             //直流欠压偏差
} sample_data_threshold_t;


void *init_sample(void * param);
void sample_main(void * param);
int find_low_trig_point(unsigned int su32_index, 
                        unsigned short* phase_a_vol, unsigned short* phase_b_vol, 
                        unsigned short* phase_c_vol);
int is_over_threshold(unsigned short *buf, unsigned short up_threshold, unsigned short down_threshold, 
                        unsigned int su32_index, unsigned short cause, unsigned char valid_flag, int sample_channel_no);
int check_dc_low_vol(unsigned short *buf, unsigned int su32_index, int sample_channel_no);
int is_behind_threshold(unsigned short *buf,  unsigned short threshold, unsigned int su32_index, unsigned short cause, unsigned char valid_flag);
int find_high_vol_trig_point(unsigned short *adc1_dma_buf1, unsigned short *adc2_dma_buf1, unsigned short *adc3_dma_buf1, unsigned int su32_index);
int fill_record_data_info(record_data_sample_info_t* sample_data );
int check_after_DC_low_vol_trig(unsigned int* current_index);
int save_record_data_after_trig(unsigned int* current_index, unsigned char* save_num);
int save_val_adc_data(data_base_address_t *data_base_addr, unsigned int* sample_data_index, unsigned char* save_num);
int process_recv_sample_msg(void);
int check_power_down_trig(unsigned int su32_index);
void handle_update_threshold(_rt_msg_t curr_msg);
void handle_update_chan_valid(_rt_msg_t curr_msg);
void handle_ctrl_sample_switch(_rt_msg_t curr_msg);
int deal_channel_valid();
int print_threshold_info();
void handle_mqtt_upfile_time(_rt_msg_t curr_msg);
int is_mqtt_upfile(unsigned int* current_index);
int clear_memory_record_data();
int init_adc_sample_dev();
int is_update_upfile_sta();
rt_err_t adc_sample_freq_set(unsigned char sample_freq);
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _SAMPLE_H
