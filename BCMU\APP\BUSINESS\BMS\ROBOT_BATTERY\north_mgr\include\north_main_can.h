/**
 * @brief dev_inverter main板北向通信头文件
 */

#ifndef _ROBOT_NORTH_MAIN_CAN_H_
#define _ROBOT_NORTH_MAIN_CAN_H_

#ifdef __cplusplus
extern "C" {
#endif

#include <stdio.h>
#include <rtthread.h>
#include <rtdevice.h>
#include "linklayer.h"
#include "protocol_layer.h"
#include "device_type.h"
#include "sps.h"
#include "parse_layer.h"
#include "dev_can_comm.h"

typedef struct {
    void*        data;
    cmd_buf_t*   cmd_buff;
    link_inst_t* link_inst;
} north_mgr_t;

void* init_north_can(void* param);
void north_main_can(void* param);
#ifdef __cplusplus
}
#endif      //< __cplusplus

#endif      //< _TOV_NORTH_MAIN_H_
