#include "led.h"
#include "CommCan.h"
#include "battery.h"
#include "realAlarm.h"
#include "MasterOper.h"
#include "SlaveOper.h"
#include "BattDischarge.h"
#include "BattDischarge_product.h"
#include "ChargeRotate.h"
#include "BattCharge.h"


Static BYTE s_ucDischgCntForControled = 0;

void (*SmartLi_CalcDischargeOutputVoltOper)(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo);
void (*GetPowerdownVoltInDisChargeOper)(T_BattInfo *ptBattInfo, T_BattDealInfoStruct *ptBattDeal);
Static BOOLEAN DischargeControlCnt(T_BattDealInfoStruct *pBattDeal);
Static BOOLEAN IfBduChargeOrDischarge(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);

Static void CalcFirstDischargeBatt(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if (DISCHG_MODE_SELF_ADAPTION != pBattIn->tPara.ucUsageScen && DISCHG_MODE_SOLAR != pBattIn->tPara.ucUsageScen)
    {
        pBattDeal->ucNextDischargeMode = DISCHARGE_LI_PRIOR_AND_NOT_SWITCH;
        return;
    }

    if ( 
        CYCLE_MODE_N_ADD_1 == pBattIn->tPara.ucCycleMode 
        && pBattDeal->ucLiDischargeNTimes >= pBattIn->tPara.ucDischargeRate 
        /*&& pBattDeal->bLABatteryFull*/
        )
    {
        pBattDeal->ucNextDischargeMode = DISCHARGE_LA_PRIOR_AND_SWITCH;
    }
    else
    {
        pBattDeal->ucNextDischargeMode = DISCHARGE_LI_PRIOR_AND_SWITCH;
    }

    pBattIn->tPara.ucCycleModeBak = pBattIn->tPara.ucCycleMode;
    pBattIn->tPara.ucUsageScenBak = pBattIn->tPara.ucUsageScen;

    return;
}

//检测转充电时激活口电压是否存在异常
Static BOOLEAN checkVoltActivatePort(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    T_DCFactory tBduFact;

    if(pBattIn == NULL || pBattDeal == NULL)
    {
        return FALSE;
    }

    rt_memset(&tBduFact, 0, sizeof(T_DCFactory));
    GetBduFact(&tBduFact);

    if(tBduFact.bActivatePort == FALSE || pBattIn->tData.fActivatePortVol < 40.0f)
    {
        return FALSE;
    }

    if((pBattIn->tData.fActivatePortVol - pBattIn->tData.fBusVol) > 0.6 && pBattIn->tData.fActivatePortVol > 40.0f)
    {
        pBattDeal->ucChargeActivatePortVoltErrCnt++;
    }

    return TRUE;
}

Static void CheckAndClearDischargeModeCounter(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if (DISCHARGE_LA_PRIOR_AND_SWITCH == pBattDeal->ucNextDischargeMode
        && BATT_MODE_DISCHARGE == pBattDeal->ucBatStatus
        && pBattDeal->wLABattDischargeTimer >= pBattIn->tPara.wLABattDurPerDischg)
    {
        pBattDeal->wLABattDischargeTimer   = 0;
        pBattDeal->fDischargeCapForNTimes  = 0.0;
    }
    return;
}

Static void CheckDischargeBatt(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if (pBattIn->tPara.ucCycleModeBak == pBattIn->tPara.ucCycleMode &&
        pBattIn->tPara.ucUsageScenBak == pBattIn->tPara.ucUsageScen)
    {
        return;
    }

    if ((pBattIn->tPara.ucCycleModeBak != pBattIn->tPara.ucCycleMode)
        || (pBattIn->tPara.ucUsageScenBak != pBattIn->tPara.ucUsageScen)
        )
    {
        pBattDeal->bChargeCurrMin          = FALSE;
        pBattDeal->bChargeCurrMinBak       = FALSE; 
        
        if (DISCHG_MODE_SELF_ADAPTION != pBattIn->tPara.ucUsageScen && DISCHG_MODE_SOLAR != pBattIn->tPara.ucUsageScen)
        {
            pBattDeal->ucNextDischargeMode = DISCHARGE_LI_PRIOR_AND_NOT_SWITCH;
        }
        else
        {
            pBattDeal->ucNextDischargeMode = DISCHARGE_LI_PRIOR_AND_SWITCH;
            pBattDeal->ucDischgDelayCounter = 0;
            pBattDeal->bChargeCurrEnough   = FALSE;
        }
    }

    pBattIn->tPara.ucCycleModeBak = pBattIn->tPara.ucCycleMode;
    pBattIn->tPara.ucUsageScenBak = pBattIn->tPara.ucUsageScen;

    return;
}

Static void SmartLi_CalcDischargeOutputVolt(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if(SmartLi_CalcDischargeOutputVoltOper != NULL)
    {
        SmartLi_CalcDischargeOutputVoltOper(pBattDeal, pBattIn);
        return;
    }
}

Static void GetPowerdownVoltInDisCharge(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if(GetPowerdownVoltInDisChargeOper != NULL)
    {
        GetPowerdownVoltInDisChargeOper(pBattIn, pBattDeal);
        return;
    }
}


Static void BattDischargeInit(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    // 初始化协议设置信息结构体
    T_ProtocolSetInfoStruct tSetVal = {0};
    if (pBattIn == NULL || pBattDeal == NULL)
    {
        return;
    }
    // 检查电源状态，如果电源开启，则清理受控模式充电请求
    if (GetValueFromProtocol(INFO_TYPE_STATUS, &tSetVal) != FAILURE && tSetVal.sValue == POWER_STATUS_ON)
    {
        SetProtoSetInfoInvalid(INFO_TYPE_STATUS);
    }

    // 初始化电池处理结构体的各个字段
    pBattDeal->ucDischgDelayCounter = 0;
    CalcFirstDischargeBatt(pBattIn, pBattDeal);
    ClearPowerdownVoltOffset();

    pBattDeal->slBattChargeMinutes = 0;
    pBattDeal->bChargeEndHold = FALSE;
    pBattDeal->bChargeCurrMin = FALSE;
    pBattDeal->bNeedLimitCurrCharge = FALSE;
    pBattDeal->bNeedDeclineDischgVolt = FALSE;
    pBattDeal->bDirectStartChgJudge = TRUE;
    pBattDeal->ucBduCtrlMode = BDU_DISCHG_START;
    pBattDeal->bThroughChg = FALSE;
    pBattDeal->bThroughDischg = FALSE;
    pBattDeal->wNoDisChargeCurrTimer = 0;
    pBattDeal->wBusVoltNum = 0;
    pBattDeal->fBusVoltSum = 0.0f;
    pBattDeal->slDefenseBattChargeMinutes = 0;
    pBattDeal->ucMixSwitchCount = 0;
    pBattDeal->wDischargeEndTimer = 0;
    pBattDeal->wEqualChgPowerOffTime = 0;
    pBattDeal->ucChangePowerOnDelay = 0;
    pBattDeal->ucChargeClearActivatePortVoltErrCnt = 0; // 清除计数
    pBattDeal->bFirstEnterDischg = TRUE;
    pBattDeal->ucCAN2SlavePowerOnCount = 0;
    pBattDeal->fCurrAdj = 0.0f;
    pBattDeal->fSocAdj = 0.0f;

    InitRotate(FALSE);

    // 注释：首次进入放电流程的初始化操作
}


Static BOOLEAN JudgePowerOn(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if(pBattIn == NULL || pBattDeal == NULL)
    {
        return FALSE;
    }

    if (!pBattDeal->bPowerOff && !pBattDeal->bPeakDischg && !pBattDeal->bFmDischg)
    {
        /////来电启动充电
        s_ucDischgCntForControled = 0;
        checkVoltActivatePort(pBattIn, pBattDeal);

        CheckAndClearDischargeModeCounter(pBattIn, pBattDeal);
        SetChargeMode(BATT_MODE_CHARGE);
        ClearVoltRecord();
        if(DISCHG_MODE_SELF_ADAPTION == pBattIn->tPara.ucUsageScen)
        {
            pBattDeal->fPowerDownVolt = MIN(pBattIn->tPara.fSysPowerOnThreshold - MIX_POWRE_ON_DOWN_DIFF, pBattIn->tData.fBusVol - MIX_POWRE_DOWN_VOL_DIFF_CHG);
        }
        else if(DISCHG_MODE_SOLAR == pBattIn->tPara.ucUsageScen)
        {
            pBattDeal->fPowerDownVolt = MIN(pBattIn->tPara.fSysPowerOnThreshold - SOLAR_POWRE_ON_DOWN_DIFF, pBattIn->tData.fBusVol - SOLAR_POWER_DOWN_DIFF);
        }
        return TRUE;
    }

    return FALSE;
}

void BattDischarge(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if (pBattDeal->ucBatStatus != BATT_MODE_DISCHARGE)// 充转放
    {
        pBattDeal->ucBatStatus = BATT_MODE_DISCHARGE;
        BattDischargeInit(pBattIn, pBattDeal);
    }
    else if (pBattDeal->bFirstEnterDischg)
    {
        pBattDeal->bFirstEnterDischg = False;
    }

    if (IsMaster())
    {
        SmartLi_CalcDischargeOutputVoltOper = SmartLi_CalcDischargeOutputVolt_Master;
        GetPowerdownVoltInDisChargeOper = GetPowerdownVolt_Master;
    }
    else
    {
        SmartLi_CalcDischargeOutputVoltOper = SmartLi_CalcDischargeOutputVolt_Slave;
        GetPowerdownVoltInDisChargeOper = GetPowerdownVolt_Slave;
    }

    CheckDischargeBatt(pBattIn, pBattDeal);
    pBattDeal->fChargeSetVol           = pBattIn->tPara.fChargeFullVol;
    pBattDeal->fDischargeLimitCurr     = clacDischCurr();

    // TimerPlus(pBattDeal->ucDischgDelayCounter, DISCHARGE_POWER_STAT_DELAY);
    // TimerPlus(s_ucDischgCntForControled, 10);
    // if(TimeOut(s_ucDischgCntForControled,10))
    // {
    //     ClearRunModeControledFlag();
    // }

    DischargeControlCnt(pBattDeal);

    JudgeDisChargeThrough(pBattIn, pBattDeal);

    SmartLi_CalcChargeHopeCurr();
    SmartLi_CalcDischargeOutputVolt(pBattIn, pBattDeal);

    UpdateMixPowerOnVolWhenDischg(pBattIn, pBattDeal);

    GetPowerdownVoltInDisCharge(pBattIn, pBattDeal);

#ifdef INTELLIGENT_PEAK_SHIFTING
    if(JudgeFmDischgSwit(pBattIn,pBattDeal))
    {
        return;
    }

    if(!JudgePeakShiftDischarge(pBattIn,pBattDeal,GetShiftPeakPara()))
    {
        return;
    }
#endif

    JudgePowerOn(pBattIn, pBattDeal);

    IfBduChargeOrDischarge(pBattIn, pBattDeal);

    return;
}

Static BOOLEAN DischargeControlCnt(T_BattDealInfoStruct *pBattDeal)
{
    TimerPlus(pBattDeal->ucDischgDelayCounter, DISCHARGE_POWER_STAT_DELAY);
    TimerPlus(s_ucDischgCntForControled, 10);
    if(TimeOut(s_ucDischgCntForControled,10))
    {
       ClearRunModeControledFlag();
    }

    return True;
}

//解决圈复杂度
Static BOOLEAN IfBduChargeOrDischarge(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if ((BYTE)NORMAL == pBattIn->tData.ucBduDischargePrtStatus && FALSE == pBattIn->tData.bBduDischgDisable)
    {
        /*
        ChargeInputBreakFlag cleared when receive CHG command. The ChargeInputBreakFlag is true during the whole 
        discharge process.
        Added by fengfj, 2021-12-13 15:30:19
        */
        if (!IfBduDischarge() && !IfSmartLiDischgStopDelay())
        {
            pBattDeal->ucBduCtrlMode = BDU_DISCHG_START;
        }
    }
    else if (IfBduCharge())
    {
        pBattIn->tData.bChargeProtect = TRUE;
        pBattDeal->ucBduCtrlMode = BDU_DISCHG_START;
    }

    if(pBattDeal->bCellUnderVolt)
    {
        SetChargeMode(BATT_MODE_CHARGE);
    }

    return True;
}


BOOLEAN BattSleepDischarge(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if(!IsMaster())
    {
        return False;
    }

    if (pBattDeal->ucSleepBattStatus != BATT_MODE_DISCHARGE)// 充转放
    {
        pBattDeal->ucSleepBattStatus = BATT_MODE_DISCHARGE;
        BattDischargeInit(pBattIn, pBattDeal);
    }
    else if (pBattDeal->bFirstEnterDischg)
    {
        pBattDeal->bFirstEnterDischg = False;
    }

    SmartLi_CalcDischargeOutputVoltOper = SmartLi_CalcDischargeOutputVolt_Master;
    GetPowerdownVoltInDisChargeOper = GetPowerdownVolt_Master;

    CheckDischargeBatt(pBattIn, pBattDeal);

    TimerPlus(pBattDeal->ucDischgDelayCounter, DISCHARGE_POWER_STAT_DELAY);
    TimerPlus(s_ucDischgCntForControled, 10);
    if(TimeOut(s_ucDischgCntForControled,10))
    {
        ClearRunModeControledFlag();
    }

    // SmartLi_CalcChargeHopeCurr();
    SmartLi_CalcDischargeOutputVolt(pBattIn, pBattDeal);

    UpdateMixPowerOnVolWhenDischg(pBattIn, pBattDeal);

    GetPowerdownVoltInDisCharge(pBattIn, pBattDeal);

    if (!pBattDeal->bPowerOff)
    {
        /////来电启动充电
        s_ucDischgCntForControled = 0;
        // checkVoltActivatePort(pBattIn, pBattDeal);

        CheckAndClearDischargeModeCounter(pBattIn, pBattDeal);
        // SetChargeMode(BATT_MODE_CHARGE);
        pBattDeal->ucSleepBattMode = BATT_MODE_CHARGE;
        ClearVoltRecord();
        if(DISCHG_MODE_SELF_ADAPTION == pBattIn->tPara.ucUsageScen)
        {
            pBattDeal->fPowerDownVolt = MIN(pBattIn->tPara.fSysPowerOnThreshold - MIX_POWRE_ON_DOWN_DIFF, pBattIn->tData.fBusVol - MIX_POWRE_DOWN_VOL_DIFF_CHG);
        }
        return False;
    }

    return True;
}

