#include "common.h"
#include "led.h"
#include "sample.h"
#include "realAlarm.h"
#include "battery.h"
#include "para.h"
#include "comm.h"
#include "protocol.h"
#include "utils_heart_beat.h"
#include "utils_rtthread_security_func.h"
#include "thread_id.h"
#include "CommCan.h"
#include "commBdu.h"
#include "HeaterControl.h"
#include "stdio.h"
#include <rtdevice.h>
#include "const_define.h"
#include "BalanceTimeStatistics.h"
#include <bq769x2.h>
#ifndef UNITEST
#include "drv_MT9818.h"
#endif
#include "fileSys.h"
#include "qtp.h"
#include "prtclWireless.h"


#ifdef ACTIVATE_PORT_ENABLED
#include "ActivatePort.h"
#endif

#define MAX_CELL_VOLTAGE      32
#define DEF_CELL_VOLTAGE      0
#define MIN_CELL_VOLTAGE      0

#define EQUAL_CIRCUIT_NORMAL  0
#define EQUAL_CIRCUIT_SHORT   1
#define EQUAL_CIRCUIT_OPEN    2

//ADC
#define ADC_DEV_NAME0        "adc0"
#define ADC_DEV_NAME1        "adc1"
#define ADC_DEV_NAME2        "adc2"
#define REFER_VOLTAGE        3          /* VOLTAGE is 3.0V */
#define CONVERT_BITS         4096       /* 12bit */

#define AI_1_8               8
#define AI_2_8               9

#define DO_1_A0              GET_PIN(D, 10)
#define DO_1_A1              GET_PIN(D, 7)
#define DO_1_A2              GET_PIN(D, 3)

#ifdef DEVICE_USING_D121
#define ADC_NUM              11      //1个基准  4个电芯温度  6个加热膜连接器温度
#define ADC_5V               13      //ADC0
#endif
#ifdef DEVICE_USING_R321
#define ADC_NUM              CELL_TEMP_NUM_MAX + 1
#define ADC_CHANNLE_0        8
#endif
#define ADC_CellTemp_1        15     //ADC1
#define ADC_CellTemp_2        8      //ADC2
#define ADC_CellTemp_3        10     //ADC3
#define ADC_CellTemp_4        12     //ADC4
#define ADC_HeatConnTemp_1     8      //ADC_P1
#define ADC_HeatConnTemp_2     9      //ADC_N1
#define ADC_HeatConnTemp_3     0      //ADC_P2
#define ADC_HeatConnTemp_4     3      //ADC_N2
#define ADC_HeatConnTemp_5     9      //ADC_P3
#define ADC_HeatConnTemp_6     14     //ADC_N3
#define ADC_FIRE_CONTROL       16

#define Equal_Circuit_Check_ONE_DAY_COUNT 288000  //0.3s周期下一天所需时间计数

typedef enum{
	AI_0 = 0,
	AI_1,
	AI_2,
	AI_3,
	AI_4,
	AI_5,
	AI_6,
	AI_7,
	AI_8,
	AI_9,
	AI_10,
	AI_11,
	AI_12,
	AI_13,
	AI_14,
	AI_15,
	AI_16,
}adc_sample_channel;

typedef struct
{
    rt_adc_device_t        dev;
    rt_uint32_t            channle;
}adc_dev;

typedef struct
{
	adc_sample_channel     sample_channel;
	rt_base_t              select_channel;
	int                    select_status;
}select_sample_pin_t;

static select_sample_pin_t s_select_sample[] = {
	{AI_0, AI_1_8, 0},
	{AI_1, AI_1_8, 0},
	{AI_2, AI_1_8, 1},
	{AI_3, AI_1_8, 2},
	{AI_4, AI_1_8, 3},
	{AI_5, AI_1_8, 4},
	{AI_6, AI_1_8, 5},
	{AI_7, AI_1_8, 6},
	{AI_8, AI_1_8, 7},
	{AI_9, AI_2_8, 0},
	{AI_10, AI_2_8, 1},
	{AI_11, AI_2_8, 2},
	{AI_12, AI_2_8, 3},
	{AI_13, AI_2_8, 4},
	{AI_14, AI_2_8, 5},
	{AI_15, AI_2_8, 6},
	{AI_16, AI_2_8, 7},
};

//电阻温度对应表
// const INT32  NTC_Table[306]=
// {
//     459591, 432381, 406957, 383189, 360961, 340163, 320695, 302464, 285383, 269374,     //-55~-46
//     254362, 240281, 227066, 214659, 203007, 192059, 181768, 172091, 162988, 154422,     //-45~-36
//     146358, 138763, 131608, 124865, 118507, 112511, 106854, 101515, 96474 , 91713 ,     //-35~-26
//     87215 , 82963 , 78944 , 75143 , 71546 , 68143 , 64921 , 61870 , 58980 , 56241 ,     //-25~-16
//     53645 , 51184 , 48849 , 46634 , 44532 , 42537 , 40642 , 38842 , 37132 , 35506 ,     //-15~-06
//     33961 , 32491 , 31093 , 29763 , 28498 , 27293 , 26145 , 25052 , 24010 , 23018 ,     //-05~4
//     22072 , 21169 , 20309 , 19488 , 18705 , 17957 , 17243 , 16561 , 15910 , 15288 ,     //5~14
//     14693 , 14125 , 13582 , 13062 , 12565 , 12089 , 11634 , 11199 , 10781 , 10382 ,     //15~24
//     10000 , 9633  , 9282  , 8945  , 8622  , 8313  , 8016  , 7731  , 7458  , 7196  ,     //25~34
//     6944  , 6703  , 6471  , 6248  , 6034  , 5828  , 5631  , 5441  , 5258  , 5083  ,     //35~44
//     4914  , 4751  , 4595  , 4445  , 4300  , 4160  , 4026  , 3897  , 3773  , 3653  ,     //45~54
//     3538  , 3426  , 3319  , 3216  , 3116  , 3020  , 2927  , 2838  , 2751  , 2668  ,     //55~64
//     2588  , 2510  , 2435  , 2363  , 2293  , 2226  , 2161  , 2098  , 2037  , 1978  ,     //65~74
//     1921  , 1866  , 1813  , 1762  , 1712  , 1664  , 1618  , 1573  , 1529  , 1487  ,     //75~84
//     1446  , 1407  , 1369  , 1332  , 1296  , 1261  , 1227  , 1195  , 1163  , 1133  ,     //85~94
//     1103  , 1074  , 1046  , 1019  , 993   , 968   , 943   , 919   , 896   , 873   ,     //95~104
//     851   , 830   , 809   , 789   , 770   , 751   , 733   , 715   , 697   , 681   ,     //105~114
//     664   , 648   , 633   , 618   , 603   , 589   , 575   , 562   , 549   , 536   ,     //115~124
//     524   , 512   , 500   , 489   , 478   , 467   , 456   , 446   , 436   , 427   ,     //125~134
//     417   , 408   , 399   , 390   , 382   , 374   , 366   , 358   , 350   , 343   ,     //135~144
//     335   , 328   , 321   , 315   , 308   , 302   , 295   , 289   , 283   , 278   ,     //145~154
//     272   , 266   , 261   , 256   , 251   , 246   , 241   , 236   , 231   , 227   ,     //155~164
//     222   , 218   , 214   , 210   , 205   , 202   , 198   , 194   , 190   , 187   ,     //165~174
//     183   , 180   , 176   , 173   , 170   , 167   , 164   , 161   , 158   , 155   ,     //175~184
//     152   , 149   , 146   , 144   , 141   , 139   , 136   , 134   , 131   , 129   ,     //185~194
//     127   , 125   , 122   , 120   , 118   , 116   , 114   , 112   , 110   , 108   ,     //195~204
//     107   , 105   , 103   , 101   , 100   , 98    , 96    , 95    , 93    , 92    ,     //205~214
//     90    , 89    , 87    , 86    , 84    , 83    , 82    , 80    , 79    , 78    ,     //215~224
//     77    , 76    , 74    , 73    , 72    , 71    , 70    , 69    , 68    , 67    ,     //225~234
//     66    , 65    , 64    , 63    , 62    , 61    , 60    , 59    , 58    , 57    ,     //235~244
//     56    , 56    , 55    , 54    , 53    , 52    ,                                     //255~250
// };

const INT32  NTC_Table[191]=
{
    209256, 197600, 186662, 176395, 166754, 157699, 149190, 141192, 133672, 126599,     //-40~-31
    119943, 113679, 107780, 102224, 96989 , 92055 , 87402 , 83014 , 78874 , 74964 ,     //-30~-21
    71388 , 67906 , 64615 , 61502 , 58558 , 55773 , 53136 , 50640 , 48275 , 46036 ,     //-20~-11
    43913 , 41901 , 39993 , 38183 , 36465 , 34835 , 33287 , 31817 , 30421 , 29095 ,     //-10~-01
    27833 , 26634 , 25494 , 24408 , 23376 , 22393 , 21457 , 20566 , 19717 , 18908 ,     //0~9
    18137 , 17401 , 16700 , 16031 , 15392 , 14783 , 14201 , 13645 , 13115 , 12608 ,     //10~19
    12123 , 11660 , 11217 , 10793 , 10388 , 10000 , 9629  , 9273  , 8933  , 8607  ,     //20~29
    8295  , 7995  , 7709  , 7434  , 7170  , 6917  , 6675  , 6442  , 6218  , 6004  ,     //30~39
    5798  , 5600  , 5410  , 5227  , 5052  , 4884  , 4722  , 4566  , 4416  , 4271  ,     //40~49
    4132  , 3999  , 3871  , 3746  , 3627  , 3512  , 3402  , 3295  , 3192  , 3093  ,     //50~59
    2999  , 2907  , 2818  , 2732  , 2649  , 2570  , 2493  , 2418  , 2347  , 2278  ,     //60~69
    2211  , 2146  , 2084  , 2024  , 1965  , 1909  , 1855  , 1802  , 1752  , 1702  ,     //70~79
    1654  , 1608  , 1564  , 1521  , 1479  , 1438  , 1400  , 1361  , 1325  , 1290  ,     //80~89
    1255  , 1222  , 1190  , 1158  , 1128  , 1099  , 1070  , 1042  , 1016  , 989   ,     //90~99
    964   , 940   , 916   , 893   , 871   , 849   , 828   , 808   , 788   , 769   ,     //100~109
    751   , 733   , 715   , 698   , 682   , 665   , 649   , 634   , 619   , 605   ,     //110~119
    591   , 577   , 564   , 551   , 538   , 526   , 514   , 502   , 490   , 479   ,     //120~129
    468   , 458   , 448   , 437   , 427   , 418   , 409   , 399   , 391   , 382   ,     //130~139
    374   , 365   , 358   , 350   , 342   , 335   , 328   , 321   , 314   , 307   ,     //140~149
    303   ,                                                                             //150
    
};

//加热膜连接器 电阻温度对应表
const INT32  NTC_Table_HeatConnect[]=
{
    8060772, 7803766, 7546760, 7289755, 7032749, 6775743, 6518738, 6261732, 6004726, 5747720,     //-55~-46
    5490715, 5233709, 4976703, 4719698, 4462692, 4205686, 3917990, 3651903, 3405663, 3177663,     //-45~-36
    2966436, 2770640, 2589051, 2420548, 2264108, 2118789, 1983734, 1858153, 1741324, 1632582,     //-35~-26
    1531319, 1436785, 1348687, 1266547, 1189927, 1118422, 1051660, 989298 , 931020 , 876533 ,     //-25~-16
    825570 , 777880 , 733236 , 691423 , 652248 , 615526 , 581042 , 548697 , 518348 , 489858 ,     //-15~-06
    463104 , 438002 , 414406 , 392219 , 371347 , 351706 , 333197 , 315769 , 299354 , 283885 ,     //-05~4
    269305 , 255556 , 242587 , 230350 , 218798 , 207891 , 197588 , 187853 , 178651 , 169951 ,     //5~14
    161722 , 153934 , 146564 , 139587 , 132980 , 126723 , 120794 , 115175 , 109847 , 104794 ,     //15~24
    100000 , 95451  , 91133  , 87033  , 83139  , 79439  , 75924  , 72582  , 69405  , 66384  ,     //25~34
    63509  , 60774  , 58171  , 55692  , 53332  , 51084  , 48943  , 46903  , 44958  , 43104  ,     //35~44
    41336  , 39647  , 38036  , 36498  , 35030  , 33628  , 32291  , 31013  , 29792  , 28626  ,     //45~54
    27510  , 26444  , 25424  , 24448  , 23515  , 22621  , 21766  , 20947  , 20163  , 19412  ,     //55~64
    18692  , 18004  , 17344  , 16712  , 16106  , 15525  , 14966  , 14429  , 13915  , 13421  ,     //65~74
    12947  , 12493  , 12057  , 11638  , 11235  , 10849  , 10477  , 10120  , 9777   , 9447   ,     //75~84
    9129   , 8824   , 8530   , 8247   , 7975   , 7713   , 7462   , 7220   , 6986   , 6762   ,     //85~94
    6546   , 6336   , 6135   , 5940   , 5753   , 5572   , 5399   , 5231   , 5070   , 4914   ,     //95~104
    4764   , 4619   , 4479   , 4343   , 4213   , 4087   , 3965   , 3847   , 3734   , 3624   ,     //105~114
    3518   , 3416   , 3317   , 3222   , 3130   , 3040   , 2954   , 2870   , 2788   , 2710   ,     //115~124
    2634   , 2554,                                                                                //125 126
};

/***********************  变量定义  ************************/
T_DCRealData s_tRealData;
static adc_dev stTemp[ADC_NUM];
static rt_uint32_t ADCvaridata[ADC_NUM];
rt_adc_device_t adc_dev0, adc_dev1, adc_dev2;

Static T_BCMDataStruct s_tBcmData;
Static T_TemDataStruct s_tOriginalData;
Static T_BalCtrlStruct s_tBalanceCtrl;
static BYTE s_ucIICComFailCounter = 0;
Static BYTE s_ucMinvolNo = CELL_VOL_NUM_MAX;
//static struct rt_spi_device *bq769x2_dev;
Static struct rt_device *s_BattChip_device;
Static T_HardwareParaStruct s_tHardwarePara;

Static BYTE s_ucEqualCircuitFault = EQUAL_CIRCUIT_NORMAL;
Static BOOLEAN s_bBalanceStart = False;

#ifndef SHIELD_BALANCE_CIRCUIT_OPENALM
Static BOOLEAN s_bActiveBalanceFlag = True;
#endif /* SHIELD_BALANCE_CIRCUIT_OPENALM */

static BYTE s_ucBalanceChl = 0;
static WORD s_wBalanceCode = 0;
static rt_mutex_t s_ptMutexAdcSamp;
static BOOLEAN s_bNormalBalaCircCheckFlag = False; //用于常规状态下24小时检测均衡电路故障状态

static FLOAT s_afCellVoltBak[CELL_VOL_NUM_MAX];
Static FLOAT s_afCellVoltRaw[CELL_VOL_NUM_MAX] = {0.0,};
static BYTE s_aucCellVolInvalidCounter[CELL_VOL_NUM_MAX];

Static uint8_t s_ucVolReg, s_ucBalanceReg,s_ucChipType = 0;
static BYTE s_ucFlagSample = 0;
Static BYTE s_ucIC_Open = TRUE;
Static T_SysPara s_tSysPara;                 // 参数
static T_HisExtremeData s_atHisExtremeData = {0};
Static BOOLEAN s_bHisExtremeUpadate = True;
static T_TempRiseRateStruct s_tTempRiseRateData;  //判断单体温升速率异常的全局结构体变量
Static BYTE s_ucCellVoltSampleFault = NORMAL;
Static BYTE s_ucHeaterFilmFailure = NORMAL; //是否存在加热膜失效告警
#ifdef FIRE_CONTROL_ENABLED
Static BYTE s_ucADCFireControlStatus = 0;
Static BYTE s_ucFireControlFaultStatus = 0;
Static float s_afFireControlVol = 0.0;
#endif
#ifdef USING_HEAT_CONNECT
Static FLOAT s_afTempSampleVal[ADC_NUM] = {0.0};
#endif
/*********************  静态函数原型定义  **********************/
static int  UpdateVoltageFromMT9818(void);
static void CtrlBalance(void);
#ifndef SHIELD_BALANCE_CIRCUIT_OPENALM
static void JudgeCircuitFaultStatus(void);
static void EqualCircuitFaultAct(void);
Static void EqualCircuitFaultJudge(void);
static void BalanceCircuitCheck(void);
static void DayTimeEqualCircuitCheck(void);
#endif
Static void Temp_Read(void *parameter);
Static FLOAT LinearInsert(INT32 data, const INT32 *TABLE, WORD wNum);
static rt_uint32_t tpw4051_adc_sample(rt_adc_device_t adcdev, adc_sample_channel sample_channel) UNUSED;  // TODO: 仅R321使用，应抽离成接口后去除UNUSED宏和static属性
static void MaxValueJudgment(float *pfCurrentValue, float *pfCurrentExtreme, float fCondition, time_t* SaveTime,BOOLEAN *UpdateFlag);
static void MinValueJudgment(float *pfCurrentValue, float *pfCurrentExtreme, float fCondition, time_t* SaveTime,BOOLEAN *UpdateFlag);
Static void UpdateExtremeFlag();
static void SpecialMaxValueJudgment(float *pfCurrentValue, float *pfCurrentExtreme, float fCondition, time_t* SaveTime,BOOLEAN *UpdateFlag);
static BOOLEAN CheckFloatDefaultValue(float fInputValue);
// static void SleepNotSample();
static void calTempMaxAndMin(FLOAT *pMax, FLOAT *pMin, BYTE *pMaxChan, BYTE *pMinChan);
static void calVolMaxAndMin(FLOAT *pMax, FLOAT *pMin, BYTE *pMaxChan, BYTE *pMinChan);
static void calTempMedium(FLOAT *pMedium);
static void RestartIC(void);
static BOOLEAN CalCellTempRiseRate(FLOAT sampletemp, BYTE CellIndex);
Static void CellVoltAssign(BYTE ucCellVoltNum);
Static BOOLEAN DeviceCellSet(void);
#ifdef FIRE_CONTROL_ENABLED
Static BOOLEAN Adc16FireControlJudge(float fAdcValueSum, int uiSampleCnt, int uiSampleChannel);
#endif
#ifdef FAULT_DIAGNOSTIC_CODE_ENABLED
static void ConvertFaultCodeToAlarm(T_DCStatusBit* pDCStatus, T_BCMAlarmStruct* pBcmAlm);
#endif

Static FLOAT calculateMedianTemperature();
Static BOOLEAN isTemperatureFaulty(FLOAT fTemp, TempConditionType condType);
Static BOOLEAN detectNTCFault(BYTE bIndex);
Static BOOLEAN recoverNTCFault(BYTE bIndex);
Static BYTE JudgeNTCFaultNum(FLOAT *pfTemSum);
Static void CellTemFaultALarmEnter();

#ifdef USING_HEAT_CONNECT
Static BOOLEAN CheckTempCSRVaild(FLOAT afOutput[], BOOLEAN bHypoComp, BYTE ucIndex, BYTE ucDeepNum);
Static void Temp_CSR_D121(void);
Static void Temp_Exchange(void);
#endif

static void getMaxAndMin(FLOAT afData[], BYTE ucNum, FLOAT *pMax, FLOAT *pMin, BYTE *pMaxChan, BYTE *pMinChan)
{
    BYTE i;
    *pMin = afData[0];
    *pMax = afData[0];
    *pMinChan = 1;
    *pMaxChan = 1;
    for(i = 0; i < ucNum; i++)
    {
        if(*pMin > afData[i])
        {
            *pMin = afData[i];
            *pMinChan = i + 1;
        }
        if(*pMax < afData[i])
        {
            *pMax = afData[i];
            *pMaxChan = i + 1; 
        }
    }
    return;
}

static void getMedium(FLOAT afData[], BYTE ucNum, FLOAT *pMedium)
{
    size_t j=0;
    FLOAT afDataTmp[CELL_VOL_NUM_MAX] = {0.0,};

    if ( ucNum < 1 || ucNum > 16)
    {
        return;
    }
    for(j = 0; j < ucNum; j++)
    {
        afDataTmp[j] = afData[j];
    }


    BubbleSort(afDataTmp, ucNum);

    if(ucNum % 2 == 0)
    {
        *pMedium = (afDataTmp[ucNum/2 - 1] + afDataTmp[ucNum/2])/2.0;
    }
    else
    {
        *pMedium = afDataTmp[(ucNum - 1)/2];
    }

    return;
}
/****************************************************************************
* 函数名称：    Lookup_TAB
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：折半查找，data所在的位置，前一坐标点，后一坐标点，记录开始位置
* 作    者：彭雪峰
* 设计日期：2015-07-15
* 修改记录：
* 日    期        版    本        修改人        修改摘要
***************************************************************************/
#ifndef UNITEST
Static void Lookup_TAB(INT32 data, const INT32 *TABLE, INT32 *aptr, WORD wNum)
{
    BYTE i;
    INT32 *endptr = (INT32*)&TABLE[wNum-1];         //高端指针，数组最后一个
    INT32 *startptr = (INT32*)TABLE;                //低端指针，数组第一个
    INT32 *lookupptr = NULL;                                //查数指针

    for ( i = 0; i < 20; i++)                        //搜索全表,8次已经可以保证全部循环完毕
    {
        lookupptr = (startptr+((endptr-startptr)>>1));  //PCLINT检查:经确认，对程序正确性无影响。
        if (*lookupptr > data)
        {
            startptr = lookupptr;
        }
        else if (*lookupptr < data)
        {
            endptr = lookupptr;
        }
        else //查到相等的节点
        {
            aptr[2] = *lookupptr;    //Y1
            aptr[1] = *(lookupptr + 1);//Y2
            aptr[0] = lookupptr - TABLE;//X1

            break;
        }

        if ((endptr-startptr) == 1)    //查到节点的范围
        {
            aptr[2] = *startptr;//Y1
            aptr[1] = *endptr;//Y2
            aptr[0] = startptr-TABLE;//X1
            break;
        }
    }
    return;
}
#endif
/****************************************************************************
* 函数名称：     LinearInsert
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：线性查找计算
* 作    者：彭雪峰
* 设计日期：2015-07-15
* 修改记录：
* 日    期        版    本        修改人        修改摘要
***************************************************************************/
Static FLOAT LinearInsert(INT32 data, const INT32 *TABLE, WORD wNum)
{
    FLOAT Temp;
    INT32 Array[3] = {0};
    INT32 NTC_Data_LowTemp = TABLE[0];
    INT32 NTC_Data_HighTemp = TABLE[wNum-1];
#ifndef UNITEST
    if (data < NTC_Data_LowTemp)                    //Beyond Lowest Temperature
    {
        if (data > NTC_Data_HighTemp)            //Beyond Highest Temperature
        {
            Lookup_TAB(data, TABLE, Array, wNum);       //Lookup data

            if (data == Array[2])
            {
                Temp = (Array[0] * 1.0 + NTC_START_TEMP);   //-40为开始点对应的温度，也就是0度是第几个，此处用的表0度为第56个，向下取整，则-55.
            }
            else
            {
                Temp = (Array[0] * 1.0 + NTC_START_TEMP) + (data * 1.0 - Array[2]) * 1.0 / (Array[1] * 1.0 - Array[2]);  //线性插值计算
            }
        }
        else
        {
            Temp = DEFAULT_TEMP_MAX; // 350; //mark Underflow
        }
    }
    else
    {
        Temp = DEFAULT_TEMP_MIN; // -75.0; //mark Overflow
    }
#endif
    return(Temp);
}

#ifndef UNITEST
static rt_uint32_t tpw4051_adc_sample(rt_adc_device_t adcdev, adc_sample_channel sample_channel)
{
	rt_uint32_t vol = 0;

	if((s_select_sample[sample_channel].select_status>>2 & 0x01) == 1)
	{
		rt_pin_write(DO_1_A2, 1);
	}
	else
	{
		rt_pin_write(DO_1_A2, 0);
	}
	if((s_select_sample[sample_channel].select_status>>1 & 0x01) == 1)
	{
		rt_pin_write(DO_1_A1, 1);
	}
	else
	{
		rt_pin_write(DO_1_A1, 0);
	}
	if((s_select_sample[sample_channel].select_status & 0x01) == 1)
	{
		rt_pin_write(DO_1_A0, 1);
	}
	else
	{
		rt_pin_write(DO_1_A0, 0);
	}

	rt_thread_mdelay(2);

	vol = rt_adc_read(adcdev, s_select_sample[sample_channel].select_channel);

	return vol;
}

static void setADCEn(rt_adc_device_t adcdev, rt_uint32_t channle)
{
    static int count = 0 ;
    if(count < ADC_NUM)
    {
        stTemp[count].dev = adcdev;
        stTemp[count++].channle = channle;
    }

    rt_adc_enable(adcdev, channle);
}
#endif

static void ADC_Init(void)
{
#ifndef UNITEST
#ifdef DEVICE_USING_D121
    adc_dev1 = (rt_adc_device_t)rt_device_find(ADC_DEV_NAME1);
    adc_dev2 = (rt_adc_device_t)rt_device_find(ADC_DEV_NAME2);
    if ((RT_NULL == adc_dev1) || (RT_NULL == adc_dev2))
    {
        return;
    }
    // setADCEn(adc_dev1 ,ADC_CHANNLE_0);
    // setADCEn(adc_dev2 ,ADC_CHANNLE_1);
    // setADCEn(adc_dev2 ,ADC_CHANNLE_2);
    // setADCEn(adc_dev2 ,ADC_CHANNLE_3);
    // // setADCEn(adc_dev1 ,ADC_CHANNLE_4);
    // setADCEn(adc_dev1 ,ADC_CHANNLE_5);
    // // setADCEn(adc_dev1 ,ADC_CHANNLE_6);
    // // setADCEn(adc_dev1 ,ADC_CHANNLE_7);
    // // setADCEn(adc_dev1 ,ADC_CHANNLE_8);

    setADCEn(adc_dev1 ,ADC_5V);             //电压基准
    setADCEn(adc_dev2 ,ADC_CellTemp_1);     //单体温度1
    setADCEn(adc_dev2 ,ADC_CellTemp_2);     //单体温度2
    setADCEn(adc_dev2 ,ADC_CellTemp_3);     //单体温度3
    setADCEn(adc_dev2 ,ADC_CellTemp_4);     //单体温度4

    setADCEn(adc_dev1 ,ADC_HeatConnTemp_1);     //连接器温度P1
    setADCEn(adc_dev1 ,ADC_HeatConnTemp_2);     //连接器温度N1
    setADCEn(adc_dev2 ,ADC_HeatConnTemp_3);     //连接器温度P2
    setADCEn(adc_dev2 ,ADC_HeatConnTemp_4);     //连接器温度N2
    setADCEn(adc_dev2 ,ADC_HeatConnTemp_5);     //连接器温度P3
    setADCEn(adc_dev2 ,ADC_HeatConnTemp_6);     //连接器温度N3

#endif
#ifdef DEVICE_USING_R321
    rt_pin_mode(DO_1_A0, PIN_MODE_OUTPUT);
    rt_pin_mode(DO_1_A1, PIN_MODE_OUTPUT);
    rt_pin_mode(DO_1_A2, PIN_MODE_OUTPUT);

    adc_dev0 = (rt_adc_device_t)rt_device_find(ADC_DEV_NAME0);
    adc_dev2 = (rt_adc_device_t)rt_device_find(ADC_DEV_NAME2);
    if ((RT_NULL == adc_dev0) || (RT_NULL == adc_dev2))
    {
        return;
    }
    setADCEn(adc_dev2 ,ADC_CHANNLE_0);
    setADCEn(adc_dev0 ,AI_1_8);
    setADCEn(adc_dev0 ,AI_1_8);
    setADCEn(adc_dev0 ,AI_1_8);
    setADCEn(adc_dev0 ,AI_1_8);
    setADCEn(adc_dev0 ,AI_1_8);
    setADCEn(adc_dev0 ,AI_1_8);
    setADCEn(adc_dev0 ,AI_1_8);
    setADCEn(adc_dev0 ,AI_1_8);
    setADCEn(adc_dev0 ,AI_2_8);
    setADCEn(adc_dev0 ,AI_2_8);
    setADCEn(adc_dev0 ,AI_2_8);
    setADCEn(adc_dev0 ,AI_2_8);
    setADCEn(adc_dev0 ,AI_2_8);
    setADCEn(adc_dev0 ,AI_2_8);
    setADCEn(adc_dev0 ,AI_2_8);
    setADCEn(adc_dev0 ,AI_2_8);
#endif
    s_ptMutexAdcSamp = rt_mutex_create("adcsamp_lock", RT_IPC_FLAG_PRIO);
    if(s_ptMutexAdcSamp == NULL)
    {
        return;
    }
#endif
}

Static void Temp_Read(void* parameter)
{
    BYTE i = 0;
    static float adc_value[ADC_NUM];
    static int adc_cnt[ADC_NUM]={0,};

#ifndef UNITEST
    while(1)
    {
        for(i = 0; i< ADC_NUM; i++)
        {
#ifndef UNITEST
#ifdef DEVICE_USING_D121
            adc_value[i] += ((float)rt_adc_read(stTemp[i].dev, stTemp[i].channle))*REFER_VOLTAGE/CONVERT_BITS;
#else
            adc_value[i] += ((float)tpw4051_adc_sample(stTemp[i].dev, i))*REFER_VOLTAGE/CONVERT_BITS;
#endif
#endif
            adc_cnt[i]++;
            if(adc_cnt[i]==10)
            {
                rt_mutex_take(s_ptMutexAdcSamp,RT_WAITING_FOREVER);
#ifdef FIRE_CONTROL_ENABLED
                Adc16FireControlJudge(adc_value[i], adc_cnt[i], i);
#endif
                ADCvaridata[i] = (rt_uint32_t)(adc_value[i] / adc_cnt[i]*1000);
                rt_mutex_release(s_ptMutexAdcSamp);
                adc_cnt[i] = 0 ;
                adc_value[i] = 0;
            }
        }
        rt_thread_delay(30);
    }
#endif
}

#ifdef FIRE_CONTROL_ENABLED
Static BOOLEAN Adc16FireControlJudge(float fAdcValueSum, int uiSampleCnt, int uiSampleChannel)
{
    if (False == IsSupportsFireControl())
        return False;

    if (ADC_FIRE_CONTROL != uiSampleChannel || 0 == uiSampleCnt)
        return False;

    s_afFireControlVol = fAdcValueSum / uiSampleCnt;

    if (s_afFireControlVol < 0.5)
    {
        s_ucADCFireControlStatus = 0;
        s_ucFireControlFaultStatus = 0;
    }
    else if (s_afFireControlVol > 2.0)
    {
        s_ucADCFireControlStatus = 1;
        s_ucFireControlFaultStatus = 0;
    }
    else
    {
        s_ucFireControlFaultStatus = 1;
    }
    return True;
}

BYTE GetAdcFireControlStatus(void)
{
    return s_ucADCFireControlStatus;
}

BYTE GetFireControlFaultStatus(void)
{
    return s_ucFireControlFaultStatus;
}

float GetFireControlAdcVol(void)
{
    return s_afFireControlVol;
}
#endif

#ifdef USING_HEAT_CONNECT

// 温度补偿拟合多项式系数
Static const FLOAT NTC_Coe[4][5] = {
    {-0.3702, 1.3057, 0.2403, 0.027, 0.001}, // NTC0低温补偿多项式系数
    {0.8944, 0.5428, -0.0220, 0.0008, 0},    // NTC0高温补偿多项式系数
    {-0.3621, 2.2779, 0.8014, 0.1314, 0.0073}, // NTC1低温补偿多项式系数
    {1.2190, -0.0401, 0.075, -0.0046, 0},      // NTC1高温补偿多项式系数
};

// NTC0、NTC1温度补偿的上下限：依次为NTC0低温补偿下限、NTC0高温补偿上限、NTC1低温补偿下限、NTC1高温补偿上限
Static const FLOAT NTC_Compos[4] = {-7.0, 7.0, -4.5, 3.5};

// 限制NTC0、NTC1高低温温度补偿的上下限
Static BOOLEAN CheckTempCSRVaild(FLOAT afOutput[], BOOLEAN bHypoComp, BYTE ucIndex, BYTE ucDeepNum)
{
    if (ucDeepNum < 4 || ucIndex > 1)
    {
        // 解决kw问题
        return FALSE;
    }

    if (bHypoComp) // 低温补偿（下限为NTC0 = -7.0，NTC1 = -4.5，上限为0）
    {
        afOutput[ucIndex] = (afOutput[ucIndex] < NTC_Compos[2 * ucIndex]) ? NTC_Compos[2 * ucIndex] : afOutput[ucIndex];
        afOutput[ucIndex] = afOutput[ucIndex] > 0 ? 0 : afOutput[ucIndex];
    }
    else // 高温补偿（上限为NTC0 = 7.0，NTC1 = 3.5，下限为0）
    {
        afOutput[ucIndex] = (afOutput[ucIndex] > NTC_Compos[2 * ucIndex + 1]) ? NTC_Compos[2 * ucIndex + 1] : afOutput[ucIndex];
        afOutput[ucIndex] = afOutput[ucIndex] < 0 ? 0 : afOutput[ucIndex];
    }

    return TRUE;
}

Static void Temp_CSR_D121(void)
{
    FLOAT afNTC_Input[2] = {0.0};
    FLOAT afOutput[2] = {0.0};
    FLOAT fNTC_Ave23 = 0;

    fNTC_Ave23 = (s_afTempSampleVal[2] + s_afTempSampleVal[3]) / 2;

    for (BYTE i = 0; i < 2; i++)
    {
        afNTC_Input[i] = s_afTempSampleVal[i] - fNTC_Ave23;
    }

    for (BYTE i = 0; i < 2; i++)
    {
        for (BYTE j = 0; j < 5; j++)
        {
            if (afNTC_Input[i] < 0)
            {
                afOutput[i] += NTC_Coe[2 * i][j] * powf(afNTC_Input[i], j); // 低温补偿的拟合多项式
                CheckTempCSRVaild(afOutput, HYPOTEMP, i, j);
            }
            else
            {
                afOutput[i] += NTC_Coe[2 * i + 1][j] * powf(afNTC_Input[i], j); // 高温补偿的拟合多项式
                CheckTempCSRVaild(afOutput, HYPERTEMP, i, j);
            }
        }
    }

    for (BYTE i = 0; i < 2; i++)
    {
        if (s_afTempSampleVal[i] < 3 && afNTC_Input[i] > 0)
        {
            afOutput[i] = 0;
        }
        // else if (s_afTempSampleVal[i] > 0 && afNTC_Input[i] < 0)
        // {
        //     afOutput[i] = 0;
        // }
    }

     // 电芯温度0/1补偿，电芯温度2/3不补偿
    s_tOriginalData.afCellTemp[0] = s_afTempSampleVal[0] - afOutput[0];
    s_tOriginalData.afCellTemp[1] = s_afTempSampleVal[1] - afOutput[1];
    s_tOriginalData.afCellTemp[2] = s_afTempSampleVal[2];
    s_tOriginalData.afCellTemp[3] = s_afTempSampleVal[3];

    return;
}

Static void Temp_Exchange(void)
{
    BYTE i = 0, ucCellNum;
    LONG ulTemp;
    FLOAT fData;
    ucCellNum = s_tHardwarePara.ucCellTempNum;

    if (ucCellNum < CELL_TEMP_NUM_MIX || ucCellNum > CELL_TEMP_NUM_MAX)
        return;

    // 电芯温度采样处理
    for (i = 1; i < ucCellNum + 1 && i < ADC_NUM; i++)
    {
        rt_mutex_take(s_ptMutexAdcSamp, RT_WAITING_FOREVER);
        ulTemp = (INT32)(5157 * (float)ADCvaridata[0] / (float)ADCvaridata[i] - 4260);
        rt_mutex_release(s_ptMutexAdcSamp);
        fData = LinearInsert(ulTemp, NTC_Table, sizeof(NTC_Table) / sizeof(INT32));

        // 低温下电阻分压采集值接近0，抖动较大，通过加权滤波抑制；s_afTempSampleVal为前一时刻采样值，fData为当前时刻的采样值
        if (((fData - s_afTempSampleVal[i - 1] > 0.3) || (fData - s_afTempSampleVal[i - 1] < -0.3)) && (fData < -30))
        {
            s_afTempSampleVal[i - 1] = (s_afTempSampleVal[i - 1] * 4 + fData) / 5;
        }
        else
        {
            s_afTempSampleVal[i - 1] = (s_afTempSampleVal[i - 1] + fData) / 2;
        }
    }

    Temp_CSR_D121(); // D121电芯温度补偿策略

    // 加热膜连接器温度采样处理
    for (i = ucCellNum + 1; i < ADC_NUM; i++)
    {
        rt_mutex_take(s_ptMutexAdcSamp, RT_WAITING_FOREVER);
        ulTemp = (INT32)(5157 * (float)ADCvaridata[0] / (float)ADCvaridata[i] - 2210);
        rt_mutex_release(s_ptMutexAdcSamp);
        fData = LinearInsert(ulTemp, NTC_Table_HeatConnect, sizeof(NTC_Table_HeatConnect) / sizeof(INT32));

        // 低温下电阻分压采集值接近0，抖动较大，通过加权滤波抑制；afHeatConnTemp为前一时刻的采样值，fData为当前时刻的采样值
        if (((fData - s_tBcmData.afHeatConnTemp[i - ucCellNum - 1] > 0.3) || (fData - s_tBcmData.afHeatConnTemp[i - ucCellNum - 1] < -0.3)) && (fData < -30))
        {
            s_tBcmData.afHeatConnTemp[i - ucCellNum - 1] = (s_tBcmData.afHeatConnTemp[i - ucCellNum - 1] * 4 + fData) / 5;
        }
        else
        {
            s_tBcmData.afHeatConnTemp[i - ucCellNum - 1] = (s_tBcmData.afHeatConnTemp[i - ucCellNum - 1] + fData) / 2;
        }
    }

    return;
}

#else
Static void Temp_Exchange(void)
{
    BYTE i = 0;
    LONG ulTemp;
    FLOAT fData;
    static BYTE s_ucTempValid[ADC_NUM] = {0};

    for(i = 1; i < ADC_NUM; i++)
    {
        rt_mutex_take(s_ptMutexAdcSamp,RT_WAITING_FOREVER);
        ulTemp = (INT32)(4420*(float)ADCvaridata[0]/(float)ADCvaridata[i] - 4260);
        rt_mutex_release(s_ptMutexAdcSamp);
        fData = LinearInsert(ulTemp, NTC_Table, sizeof(NTC_Table)/sizeof(INT32));
        if((fabs(fData-DEFAULT_TEMP_MIN) < 0.0001) || (fabs(fData-DEFAULT_TEMP_MAX) < 0.0001))
        {
            s_ucTempValid[i] = False;
            s_tOriginalData.afCellTemp[i-1] = fData;
        }
        else
        {
            if(s_ucTempValid[i] == False)
            {
                s_tOriginalData.afCellTemp[i-1] = fData;
            }
            else
            {
                // 低温下电阻分压采集值接近0，抖动会较大，通过加权滤波抑制下
                if((fabs(fData - s_tOriginalData.afCellTemp[i-1]) > 0.3) && (fData < -30))
                {
                    s_tOriginalData.afCellTemp[i-1]=(s_tOriginalData.afCellTemp[i-1]*4+fData)/5;
                }
                else
                {
                    s_tOriginalData.afCellTemp[i-1]=(s_tOriginalData.afCellTemp[i-1]+fData)/2;
                }
            }
            s_ucTempValid[i] = True;
        }
    }
}
#endif
 uint8_t Get_SampleChipType(void)
 {
    return s_ucChipType;
 }
static void SampleDeviceFind(void)
{
    s_BattChip_device = rt_device_find("mt9818");
#ifndef UNITEST
    if(RT_NULL == s_BattChip_device)
    {
        s_BattChip_device = rt_device_find("bq76952");
        s_ucVolReg = Cell1Voltage;
        s_ucBalanceReg = CB_ACTIVE_CELLS ;
        s_ucChipType = CHIP_BQ76952;
    }
    else 
    {
        s_ucVolReg = VC1_HI;
        s_ucBalanceReg = CELLBAL1 ;
        s_ucChipType = CHIP_MT9818;
    }
    if(RT_NULL != s_BattChip_device)
    {
         rt_device_open(s_BattChip_device ,RT_DEVICE_OFLAG_RDWR);
         DeviceCellSet();
    }
#endif
}


Static BOOLEAN DeviceCellSet(void)
{
    rt_uint32_t cell_num = 0; 
    rt_uint32_t bal_max_num = 0;  

    readBmsHWPara(&s_tHardwarePara);
    if(s_tHardwarePara.ucCellVoltNum == R121_CELL_VOLT_NUM)
    {
        cell_num = 0x807F;
        bal_max_num = 4;
    }
    else
    {
        cell_num = 0xDFFF;
        bal_max_num = 8;
    }
    if(s_ucChipType == CHIP_BQ76952)
    {
        rt_device_control(s_BattChip_device, VCellMode, &cell_num);
        rt_device_control(s_BattChip_device, CellBalanceMaxCells, &bal_max_num);
        return True;
    }
    return False;
}


Static void DealControl(void) {
    if(IsHeaterFilmConsist() && IsHardwareSupportHeater() && (s_ucHeaterFilmFailure == NORMAL))//加热器在位&&硬件支持加热膜&&无加热膜失效告警，则正常控制加热器
    {
        RefreshHeaterInfo(&s_tBcmData);
        DealHeaterControl();
    }
    else
    {
        if(GetHeaterLogicStatus() == HEATER_ON)
        {
            CtrlHeaterOnOff(HEATER_OFF);
        }
    }
#ifndef SHIELD_BALANCE_CIRCUIT_OPENALM
    DayTimeEqualCircuitCheck(); //一天做一次主动均衡故障检测
    BalanceCircuitCheck();
#endif
    if (s_tBalanceCtrl.bCtrl && s_BattChip_device != RT_NULL)
    {
        if (!IsSleep()||(s_ucIC_Open == 1))
        {
            CtrlBalance();
        }
        else
        {
            return;  // note: 避免进入休眠没有控制均衡动作，却进行后面的回路故障判断，导致误告警
        }
    }
#ifndef SHIELD_BALANCE_CIRCUIT_OPENALM
    if(s_bBalanceStart)
    {
        if(s_bActiveBalanceFlag == True)
        {
            EqualCircuitFaultJudge();
        }
        else
        {
            s_bActiveBalanceFlag = True;
        }
    }
#endif
}

void ReadAndResetIC(void)
{
    if(GetICRestartFlag())
    {
        RestartIC();
        SetICRestartFlag(False);
    }
    if (s_ucIC_Open == 1)  // 休眠模式不做单体采样
    {
        UpdateVoltageFromMT9818();
    }
}

BOOLEAN CellBalStatisticInit(void)
{
    // if(IsCellNumInScope(s_tHardwarePara.ucCellVoltNum, 0, CELL_VOL_NUM_MAX))
    // {
        CellBalInit(&s_tHardwarePara);
        return TRUE;
    // }
    // return FALSE;
}


// 计算中位数温度
Static FLOAT calculateMedianTemperature()
{
    FLOAT aftemperatures[4] = {0};
    BYTE ucvalidCount = 0;

    // 收集有效的温度值
    for (int i = 0; i < s_tHardwarePara.ucCellTempNum; ++i)
    {
        if (s_tOriginalData.abCellTempFault[i] == False)
        {
            aftemperatures[ucvalidCount] = s_tOriginalData.afCellTemp[i];
            ucvalidCount++;
        }
    }

    if (ucvalidCount == 0) return 0.0f; // 没有有效温度值

    return GetCellMediumValue(aftemperatures, ucvalidCount);
}

/* Started by AICoder */
Static BOOLEAN isTemperatureFaulty(FLOAT fTemp, TempConditionType condType)
{
    #ifndef FTTEST
    static WORD s_awTimer[8] = {0};
    #else
    extern WORD s_awTimer[8];
    #endif
    BOOLEAN bConditionMet = False;

    /* 根据条件类型选择计时器和判断逻辑 */
    if (condType <= CONDITION_MEDIAN_DELTA_4)
    {
        FLOAT fmedianTemp = calculateMedianTemperature();
#ifdef DEVICE_USING_R321
        bConditionMet = fabs(fTemp - fmedianTemp) > CELLTEMP_MEDIADELTA_15 ? True : False; // R321判断单体温度无效，阈值为15℃
#else
        bConditionMet = fabs(fTemp - fmedianTemp) > CELLTEMP_MEDIADELTA_20 ? True : False; // D121判断单体温度无效，阈值为20℃
#endif
    }
    else
    {
        bConditionMet = fTemp >= CELLTMP_75 ? True : False;
    }

    /* 统一处理计时逻辑 */
    if (bConditionMet)
    {
        TimerPlus(s_awTimer[condType], SAMPLE_TWO_HOUR);
        if (TimeOut(s_awTimer[condType], SAMPLE_TWO_HOUR))
        {
            s_awTimer[condType] = 0;
            return True;
        }
        return False;
    }
    else
    {
        s_awTimer[condType] = 0;
        return False;
    }
}



// 单体温度无效告警判断
Static BOOLEAN detectNTCFault(BYTE bIndex)
{
    BYTE ucTempDelta = isTemperatureFaulty(s_tOriginalData.afCellTemp[bIndex], CONDITION_MEDIAN_DELTA_1 + bIndex);
    BYTE ucTempFixedThre = isTemperatureFaulty(s_tOriginalData.afCellTemp[bIndex], CONDITION_FIXED_THRESHOLD_1 + bIndex);
    if (ucTempDelta || ucTempFixedThre ||
        (s_tOriginalData.afCellTemp[bIndex] < CELLTEMP_MIN) ||
        (s_tOriginalData.afCellTemp[bIndex] > CELLTEMP_MAX))
    {
        return True;
    }
    return False;
}

// 单体温度无效告警恢复
Static BOOLEAN recoverNTCFault(BYTE bIndex)
{
    static WORD s_awTimer[4] = {0}, s_awTempChangeTimer[4] = {0};
    static FLOAT s_afpretemp[4] = {0}, s_afcurtemp[4] = {0};
    static FLOAT s_afTempChangeRateData[4] = {0};

    #ifdef FTTEST
    updateTimers(&s_awTimer, &s_awTempChangeTimer, &s_afpretemp, &s_afcurtemp, &s_afTempChangeRateData);
    #endif

    if(s_awTempChangeTimer[bIndex] == 0)
    {
        s_afpretemp[bIndex] = s_tOriginalData.afCellTemp[bIndex]; // 10min前的温度
    }

    TimerPlus(s_awTempChangeTimer[bIndex], SAMPLE_TEN_MIN);
    if(TimeOut(s_awTempChangeTimer[bIndex], SAMPLE_TEN_MIN))
    {
        s_afcurtemp[bIndex] = s_tOriginalData.afCellTemp[bIndex]; // 当前温度
        s_afTempChangeRateData[bIndex] = s_afcurtemp[bIndex] - s_afpretemp[bIndex]; // 每10min的温升速率
        s_awTempChangeTimer[bIndex] = 0;
    }

    // NTC故障告警恢复条件条件4：温度采样值在[-70，110]°C范围内
    if (s_tOriginalData.afCellTemp[bIndex] < CELLTEMP_MIN ||
        s_tOriginalData.afCellTemp[bIndex] > CELLTEMP_MAX)
    {
        return False;
    }

    // NTC故障告警恢复条件1，2，3：
    if(s_tOriginalData.afCellTemp[bIndex] < CELLTMP_45 &&  // 温度小于45℃持续2h
       fabs(s_tOriginalData.afCellTemp[bIndex] - calculateMedianTemperature()) < CELLTEMP_MEDIADELTA_5 && // 采样值和其他正常NTC温度中位数的温差小于5℃持续2h（若全部单体温度都故障，无正常中位数，则认为此条件满足）
       fabs(s_afTempChangeRateData[bIndex]) < CELLTEMP_RISERATE_2) // 温度变化率(包含上升和下降)小于2℃/10min，持续2h
    {
        TimerPlus(s_awTimer[bIndex], SAMPLE_TWO_HOUR);
        if(TimeOut(s_awTimer[bIndex], SAMPLE_TWO_HOUR))
        {
            s_awTimer[bIndex] = 0;
            return True;
        }
        return False;
    }
    else
    {
        s_awTimer[bIndex] = 0;
        return False;
    }
}


Static BYTE JudgeNTCFaultNum(FLOAT *pfTemSum)
{
    BYTE ucNTCFaultNum = 0;
    //进入qtp模式，不执行后续操作，提升效率
    if(GetQtptestFlag())
    {
        return ucNTCFaultNum;
    }
    if(NULL == pfTemSum)
    {
        return ERROR_NULL;
    }
    for (int i = 0; i < s_tHardwarePara.ucCellTempNum; i++)
    {
        if(s_tOriginalData.abCellTempFault[i] == False)
        {
            // NTC故障告警产生
            if(detectNTCFault(i) == True)
            {
                ucNTCFaultNum++;
                s_tOriginalData.abCellTempFault[i] = True;
            }
            else // NTC故障告警未产生
            {
                *pfTemSum += s_tOriginalData.afCellTemp[i];
            }
        }

        // NTC故障告警产生后恢复
        else if(s_tOriginalData.abCellTempFault[i] == True)
        {
            if(recoverNTCFault(i) == True)
            {
                s_tOriginalData.abCellTempFault[i] = False;
                *pfTemSum += s_tOriginalData.afCellTemp[i];
            }
            else// NTC故障告警产生后未恢复
            {
                ucNTCFaultNum++;
            }

        }
    }
    return ucNTCFaultNum;
}

Static void CellTemFaultALarmEnter()
{
    FLOAT fTemSum = 0;
    BYTE ucNTCFaultNum = 0;
    ucNTCFaultNum = JudgeNTCFaultNum(&fTemSum);

    if(ERROR_NULL == ucNTCFaultNum)
    {
        return;
    }
    // 如果满足NTCFaultNum <= ucNTCInvalidShieldNum判断条件，则不上送NTC故障告警
    if(!GetApptestFlag() && !GetQtptestFlag() && ucNTCFaultNum <= s_tSysPara.ucNTCInvalidShieldNum)
    {
        for (int i = 0; i < s_tHardwarePara.ucCellTempNum; i++)
        {
            if(s_tOriginalData.abCellTempFault[i] == True)
            {
                // 更新故障NTC温度为其他正常NTC采样的均值
                s_tBcmData.afCellTemp[i] = fTemSum / (s_tHardwarePara.ucCellTempNum - ucNTCFaultNum);
                // 不产生告警
                s_tBcmData.abCellTempFault[i] = False;
            }
            else
            {
                s_tBcmData.afCellTemp[i] = s_tOriginalData.afCellTemp[i];
                s_tBcmData.abCellTempFault[i] = s_tOriginalData.abCellTempFault[i];
            }
        }
    }
    else
    {
        for (int i = 0; i < s_tHardwarePara.ucCellTempNum; i++)
        {
            s_tBcmData.afCellTemp[i] = s_tOriginalData.afCellTemp[i];
            s_tBcmData.abCellTempFault[i] = s_tOriginalData.abCellTempFault[i];
        }
    }
}

Static BOOLEAN SyncRealData(void)
{
    INT32 i = 0;
#ifdef Traffic4G
    T_4GTrafficTotail t4GTrafficTotail = {0};
#endif

    s_tBcmData.fBattCurr   = s_tRealData.tDCAnalag.sBatCur / 100.0f;          //电池电流
    s_tBcmData.fBattVolt   = s_tRealData.tDCAnalag.wBatVol / 100.0f;          //电池电压
    s_tBcmData.fExterVolt  = s_tRealData.tDCAnalag.wBusVol / 100.0f;          //外部电压
    s_tBcmData.fBusCurr    = s_tRealData.tDCAnalag.sBusCur / 100.0f;          //BUS电流
    s_tBcmData.fVoltChange = s_tRealData.tDCAnalag.wBatFusePro / 100.0f;      //输出熔丝前电压(调试)
    s_tBcmData.fEnvTemp    = (FLOAT)((SHORT)(s_tRealData.tDCAnalag.sEnvTemp / 10.0f));    //环境温度，D121环境温度没有小数（PAD协议）功率要求不四舍五入
    s_tBcmData.ucExternalPowerStatus = s_tRealData.tDCStatus.bExternalPowerOn;    //外部有电状态
    s_tBcmData.ucMosOffStatus = s_tRealData.tDCStatus.bMOSOffStatus;              //MOS关闭状态

    s_tBcmData.fBoardTemp = (FLOAT)((SHORT)(s_tRealData.tDCAnalag.sBoardTemp/10.0f));      //单板温度，D121单板温度没有小数（PAD协议）功率要求不四舍五入
    s_tBcmData.fConnTemp  = s_tRealData.tDCAnalag.sConnTemp/10.0f;       //连接器温度

#ifdef DEVICE_USING_R321
    s_tBcmData.fEnvTemp    = s_tRealData.tDCAnalag.sEnvTemp / 10.0f;              // 环境温度，R321环境温度
    s_tBcmData.fBoardTemp = s_tRealData.tDCAnalag.sBoardTemp/10.0f;               //单板温度，R321单板温度
    s_tBcmData.fBalanceResisTemp = s_tRealData.tDCAnalag.sBalanceResisTemp/10.0f; // 均衡电阻温度
    s_tBcmData.fActivatePortVol = s_tRealData.tDCAnalag.sActivatePortVol/100.0f;  // 激活口电压
#endif
     //计算单体温度入口
    CellTemFaultALarmEnter();

    //计算单体最大压差，最大温差
    calVolMaxAndMin(&s_tBcmData.fCellVoltMax, &s_tBcmData.fCellVoltMin, &s_tBcmData.ucCellMaxVoltChan, &s_tBcmData.ucCellMinVoltChan);
    calTempMaxAndMin(&s_tBcmData.fCellTempMax, &s_tBcmData.fCellTempMin, &s_tBcmData.ucCellMaxTempChan, &s_tBcmData.ucCellMinTempChan);
    calTempMedium(&s_tBcmData.fCellTempMedium);
    //单体温升速率异常功能入口函数
    CellTempRiseAbnormalEnter();
    s_tBcmData.fHeatConnTempMax = -75.0f;
    for(i = 0; i < Heat_Conn_TEMP_NUM_MAX; i++)
    {
        if(s_tBcmData.afHeatConnTemp[i]<-45 || s_tBcmData.afHeatConnTemp[i]>120)
            continue;

        if(s_tBcmData.fHeatConnTempMax < s_tBcmData.afHeatConnTemp[i])
            s_tBcmData.fHeatConnTempMax = s_tBcmData.afHeatConnTemp[i];
    }

    s_tBcmData.maxVoltDiff = s_tBcmData.fCellVoltMax - s_tBcmData.fCellVoltMin;
    s_tBcmData.maxTempDiff = s_tBcmData.fCellTempMax - s_tBcmData.fCellTempMin;
#ifndef UNITEST
    s_tBcmData.ucBduStatus = ConvertBduStatus(s_tRealData.tDCStatus.bStatus);   //充放电状态
#endif
    s_tBcmData.ucLimit            = (BYTE)s_tRealData.tDCStatus.bLimit;               // 限流状态
    s_tBcmData.ucBduSleep         = (BYTE)s_tRealData.tDCStatus.bSleepStatus;         // BDU休眠状态
    s_tBcmData.ucInputBreak       = (BYTE)s_tRealData.tDCStatus.bInputBreak;          // 充电输入断状态
    s_tBcmData.ucChgProtSta       = (BYTE)s_tRealData.tDCStatus.bChgPrt;              // 充电保护状态
    s_tBcmData.ucDischgProtSta    = (BYTE)s_tRealData.tDCStatus.bDischgPrt;           // 放电保护状态
    s_tBcmData.ucBattChargeEn     = (BYTE)s_tRealData.tDCStatus.bChgForbid;           // 充电使能状态
    s_tBcmData.ucBattDischEn      = (BYTE)s_tRealData.tDCStatus.bDischgForbid;        // 放电使能状态
    s_tBcmData.bHeatpadEnable     = (BYTE)s_tRealData.tDCStatus.bHeaterStatus;        // 加热垫开关状态
    s_tBcmData.bUpgradeEnable     = (BYTE)s_tRealData.tDCStatus.bUpgradeEn;           // 升级使能状态
    s_tBcmData.ucAlarmStatus      = GetExistAlarmFlag();                        // 获取BDU的告警状态，标志位不是从功率传递过来，监控自己判断，放在集中数据，以免数据零散
    s_tBcmData.bContactorStatus   = (BYTE)s_tRealData.tDCStatus.bContactorBus;        // 母排接触器状态
    s_tBcmData.bChargeMachineTest = (BYTE)s_tRealData.tDCStatus.bChargeMachineTest;   // 充放电机测试状态
    s_tBcmData.bSolarMode         = (BYTE)s_tRealData.tDCStatus.bSolarMode;           // 光伏模式

#ifdef Traffic4G
    Get4GTraffic(&t4GTrafficTotail);
    s_tBcmData.w4GUpTrafficHigh   = (t4GTrafficTotail.s_tUp4GTraffic.uiByteTotail >> 16)&0xFFFF;      //上行流量高字节
    s_tBcmData.w4GUpTrafficLow    = (t4GTrafficTotail.s_tUp4GTraffic.uiByteTotail)&0xFFFF;             //上行流量低字节
    s_tBcmData.w4GDownTrafficHigh = (t4GTrafficTotail.s_tDown4GTraffic.uiByteTotail >> 16)&0xFFFF;    //下行流量高字节
    s_tBcmData.w4GDownTrafficLow  = (t4GTrafficTotail.s_tDown4GTraffic.uiByteTotail)&0xFFFF;           //下行流量低字节
#endif

    s_tBcmData.bSiteAntiTheftStatus = GetSiteAntiTheftStatus(); // 站点防盗布防状态
    return TRUE;
}

Static BOOLEAN SyncRealAlarm(void)
{
    T_BCMAlarmStruct tBcmAlm;
    GetBcmRealAlarm(&tBcmAlm);
    tBcmAlm.ucBDUBusVoltHighPrt       = (BYTE)s_tRealData.tDCAlarm.bBusOverVol;                // BDU母排过压保护
    tBcmAlm.ucBDUBusVoltLowPrt        = (BYTE)s_tRealData.tDCAlarm.bBusUnderVol;               // BDU母排欠压保护
    tBcmAlm.ucBDUBattLockAlm          = (BYTE)s_tRealData.tDCAlarm.bLockErr;                   // 闭锁状态
    tBcmAlm.ucBduEepromAlm            = (BYTE)s_tRealData.tDCAlarm.bEEPROM;                    // BDU EEPROM故障
    tBcmAlm.ucBDUBattChgVoltLowPrt    = (BYTE)s_tRealData.tDCAlarm.bChgBatUnderVol;            // BDU电池充电欠压保护
    tBcmAlm.ucBattShortCut            = (BYTE)s_tRealData.tDCAlarm.bShortCut;                  // 电池短路
    tBcmAlm.ucBattReverse             = (BYTE)s_tRealData.tDCAlarm.bBatReverse;                // 电池反接
    tBcmAlm.ucBoardTempHighPrt        = (BYTE)s_tRealData.tDCAlarm.bBoardOverTemp;             // 单板过温保护
    tBcmAlm.ucBattOverVoltPrt         = (BYTE)s_tRealData.tDCAlarm.bBatOverVol;                // 电池组过压保护
    //tBcmAlm.ucDischgCurrHighPrt = tRealData.tDCAlarm.bDischOverCur;                          // 放电过流保护
    //tBcmAlm.ucChgCurrHighPrt =  tRealData.tDCAlarm.bChgOverCur;                              // 充电过流保护
    tBcmAlm.ucCurrLimLoopInvalid      = (BYTE)s_tRealData.tDCAlarm.bLimLoopFail;               // 限流回路失效
    tBcmAlm.ucDischgLoopInvalid       = (BYTE)s_tRealData.tDCAlarm.bDischLoopFail;             // 放电回路失效
    tBcmAlm.ucChgLoopInvalid          = (BYTE)s_tRealData.tDCAlarm.bChgLoopFail;               // 充电回路失效
    tBcmAlm.ucBDUBattVoltLowPrt       = (BYTE)s_tRealData.tDCAlarm.bBatUnderVol;               // 电池组欠压保护
    tBcmAlm.ucInsideTempHighPrt       = (BYTE)s_tRealData.tDCAlarm.bInsideOverTemp;            // 机内过温保护
    tBcmAlm.ucBDUConnTempHighPrt      = (BYTE)s_tRealData.tDCAlarm.bConnTempHighPrt||s_tBcmData.ucConnTempHighPrtStatus; // 连接器高温保护
    tBcmAlm.ucHeaterFilmFailure       = (BYTE)s_tRealData.tDCAlarm.bHeaterErr;                  // 加热膜失效告警
    tBcmAlm.ucMainRelayFail           = (BYTE)s_tRealData.tDCAlarm.bMainRelayFail;              // 主继电器失效告警
    tBcmAlm.ucDCDCErr                 = (BYTE)s_tRealData.tDCAlarm.bDCDCErr;                    // DCDC故障告警
    tBcmAlm.ucSampleErr               = (BYTE)s_tRealData.tDCAlarm.bSampleErr;                  // 采样异常告警
    tBcmAlm.ucAuxiSourceErr           = (BYTE)s_tRealData.tDCAlarm.bAuxiSourceErr;              // 辅助源故障告警
    #ifdef DEVICE_USING_R321
    tBcmAlm.ucBalanceResisTempHighPrt = (BYTE)s_tRealData.tDCAlarm.bBalanceResisTempHighPrt;    // 均衡电阻温度高保护
    tBcmAlm.ucFireControlAlm = GetFireControlStatus();                                          // 消防告警
    tBcmAlm.ucFireControlFaultAlm = GetFireControlFaultStatus();                                // 消防故障告警
    tBcmAlm.ucActivatePortReverseAlm = (BYTE)s_tRealData.tDCAlarm.bActivatePortReverseAlm;      // 激活口反接告警
    if(GetApptestFlag() || GetQtptestFlag())
    {
        tBcmAlm.ucActivePortCurrError = (BYTE)s_tRealData.tDCAlarm.bActivatePortCurrError;      // 激活回路电流异常
    }
    else
    {
        tBcmAlm.ucActivePortCurrError = GetActivateErrorFlag();                                 // 激活回路电流异常
    }
    tBcmAlm.ucWaterIngrAlm = s_tRealData.tDCStatus.bDiagSts27;                                 // 防水告警
    #endif

    #ifdef FAULT_DIAGNOSTIC_CODE_ENABLED
    ConvertFaultCodeToAlarm(&s_tRealData.tDCStatus, &tBcmAlm);
    #endif

    s_ucHeaterFilmFailure = tBcmAlm.ucHeaterFilmFailure;
    TransBcmAlarm( &tBcmAlm );

    return TRUE;
}

void SampleInSys(void* parameter)
{
    SampleDeviceFind();
    ADC_Init();
    InitHeater();

#ifndef UNITEST
    ClearBlanceCircuitStatus();
    rt_memset_s(&s_tHardwarePara, sizeof(T_HardwareParaStruct), 0, sizeof(T_HardwareParaStruct));
    rt_memset_s(&s_tBcmData, sizeof(T_BCMDataStruct), 0, sizeof(T_BCMDataStruct));
    rt_memset_s(&s_tOriginalData, sizeof(T_BCMDataStruct), 0, sizeof(T_TemDataStruct));
#endif
    readBmsHWPara(&s_tHardwarePara);
    rt_memset(&s_tBalanceCtrl, 0, sizeof(T_BalCtrlStruct));

    CellBalStatisticInit();
#ifndef UNITEST
    rt_thread_t TaskTmpAdc;
    TaskTmpAdc = rt_thread_create("TmpAdc", Temp_Read, RT_NULL, 512, 15, 5);
    if (TaskTmpAdc != RT_NULL)
        rt_thread_startup(TaskTmpAdc);
#endif

#ifndef UNITEST
    s_ucFlagSample = 0;
    pre_thread_beat_f(THREAD_SAMPLE);
    InitTempRiseRateData();
    while (1)
#endif
    {
        rt_thread_delay(300);
        thread_beat_go_on(THREAD_SAMPLE);
        ReadAndResetIC();
        GetBduReal(&s_tRealData);
        Temp_Exchange();

        SyncRealData();
        SyncRealAlarm();

        DealControl();
        CircleCheckCellBalSave();
#ifdef EXTREME_DATA_RECORD_ENABLE
        UpdateExtremeFlag();
#endif

        TimerPlus(s_ucFlagSample, times_4);
    }
}

#ifdef FAULT_DIAGNOSTIC_CODE_ENABLED
/****************************************************************************
* ConvertFaultCodeToAlarm
* 功能描述：将故障诊断码转化为实时告警
* 修改记录：
***************************************************************************/
static void ConvertFaultCodeToAlarm(T_DCStatusBit* pDCStatus, T_BCMAlarmStruct* pBcmAlm) {
    
    if (NULL == pDCStatus || NULL == pBcmAlm)
    {
        return;
    }

    //加热膜失效告警
    if (pBcmAlm->ucHeaterFilmFailure == NORMAL) {
        pBcmAlm->ucHeaterFilmFailure = pDCStatus->bDiagSts28 |pDCStatus->bDiagSts29 | pDCStatus->bDiagSts30;   
    } 

    //主继电器失效告警
    if (pBcmAlm->ucMainRelayFail == NORMAL) {
        pBcmAlm->ucMainRelayFail = pDCStatus->bDiagSts8  | pDCStatus->bDiagSts9;
    }

    //DCDC故障告警
    if (pBcmAlm->ucDCDCErr == NORMAL) {
        pBcmAlm->ucDCDCErr = pDCStatus->bDiagSts4 | pDCStatus->bDiagSts5 | pDCStatus->bDiagSts26;
    }

    //采样异常告警
    if (pBcmAlm->ucSampleErr == NORMAL) {
        pBcmAlm->ucSampleErr = pDCStatus->bDiagSts14 | pDCStatus->bDiagSts16 | pDCStatus->bDiagSts17;
    } 
}
#endif

static void calVolMaxAndMin(FLOAT *pMax, FLOAT *pMin, BYTE *pMaxChan, BYTE *pMinChan)
{
    if(s_tHardwarePara.ucCellVoltNum > 0 && s_tHardwarePara.ucCellVoltNum < CELL_VOL_NUM_MAX)
    {
        getMaxAndMin(s_tBcmData.afCellVolt, s_tHardwarePara.ucCellVoltNum, pMax, pMin, pMaxChan, pMinChan);
    }
}

static void calTempMaxAndMin(FLOAT *pMax, FLOAT *pMin, BYTE *pMaxChan, BYTE *pMinChan)
{

    if(s_tHardwarePara.ucCellTempNum > 0 && s_tHardwarePara.ucCellTempNum < CELL_TEMP_NUM_MAX)
    {
        getMaxAndMin(s_tBcmData.afCellTemp, s_tHardwarePara.ucCellTempNum, pMax, pMin, pMaxChan, pMinChan);
    }
}

static void calTempMedium(FLOAT *pMedium)
{

    if(s_tHardwarePara.ucCellTempNum > 0 && s_tHardwarePara.ucCellTempNum < CELL_TEMP_NUM_MAX)
    {
        getMedium(s_tBcmData.afCellTemp, s_tHardwarePara.ucCellTempNum, pMedium);
    }
}

// static void SleepNotSample()
// {
//     if (s_ucIC_Open == 1)  // 休眠模式不做单体采样
//     {
//         UpdateVoltageFromMT9818();
//     }
// }

BYTE GetFlagSample(void)
{
    return s_ucFlagSample < times_4;
}

void GetRealData(T_BCMDataStruct *pDest)
{
    if (NULL == pDest)
    {
        return;
    }

    rt_memcpy(pDest, (BYTE *)&s_tBcmData, sizeof(T_BCMDataStruct));
}

void JudgeDataFlag(void)
{
    return;
}

void SetRealData(T_BCMDataStruct const* pDest)
{
    rt_memcpy( &s_tBcmData, pDest, sizeof(T_BCMDataStruct));
}
#ifndef UNITEST//wzt
BOOLEAN Get_IC_Flag(void)
{
    return s_ucIC_Open;
}
void Open_IC_Onece(BOOLEAN bFlag)
{
    rt_base_t GPIO_AFE1 = get_pin_by_name(BSP_MT9818_AEF1, 0, 0);
    rt_base_t GPIO_AFE2 = get_pin_by_name(BSP_MT9818_AEF2, 0, 0);
    if(s_ucIC_Open != bFlag)
    {
        switch (bFlag)
        {
            case TRUE/* constant-expression */:
            /* code:todo物理打开IC */
            rt_pin_write(GPIO_AFE2 , 1);
            rt_pin_write(GPIO_AFE1 , 1);
            rt_thread_delay(400);
            if(s_BattChip_device != RT_NULL)
            {
                rt_device_open(s_BattChip_device,1);
                DeviceCellSet();
            }
            rt_thread_delay(100);
            s_ucIC_Open = bFlag;
        break;
        case FALSE:
            s_ucIC_Open = bFlag;
            rt_thread_delay(310);
                /* code:todo物理关闭IC */
            rt_pin_write(GPIO_AFE2 , 0);
            rt_pin_write(GPIO_AFE1 , 0);
        break;
        default:
        break;
        }
    }
}

static BOOLEAN IsDataJump(double fVolDiff, BYTE *ucData, BYTE ucCellNum)
{
    static BYTE s_auDataHopCnt[CELL_VOL_NUM_MAX] = {0,};
    if ( (fVolDiff >= 0.25) && (fVolDiff <= 0.26) && (ucData[2*ucCellNum] > 0xF0 || ucData[2*ucCellNum]  < 0x10) &&  s_auDataHopCnt[ucCellNum] < 3)//过滤掉0.256V左右的跳变
    {
        s_auDataHopCnt[ucCellNum]++;
        return True;
    }
    s_auDataHopCnt[ucCellNum] = 0;
    return False;
}

BYTE GetMinvolNo(void)
{
    return s_ucMinvolNo;
}

static void Update_CellVol(BYTE* ucData, BYTE ucCellVoltNum)
{
    FLOAT fVolTmp;
    double fVolDiff;
    FLOAT fMinvol = 1.2;
    BYTE i = 0;

    for ( i = 0; i < ucCellVoltNum; i++)
    {
        fVolTmp = (((FLOAT)(ucData[2*i + 1] << 8) + ucData[2*i]) + 0.5)/1000.0;
        if (MIN_CELL_VOLTAGE > fVolTmp || MAX_CELL_VOLTAGE < fVolTmp)
        {
            fVolTmp = DEF_CELL_VOLTAGE;  // 解决长排线断开时，电芯采样芯片输出负电压的问题
        }
        fVolDiff = fabs(s_afCellVoltRaw[i] - fVolTmp);
        if(IsDataJump(fVolDiff, ucData, i) != True)
        {
           s_afCellVoltRaw[i] = fVolTmp;
        }
        if (fMinvol > s_afCellVoltRaw[i])
        {
            s_ucMinvolNo = i;                             // 找出16节电芯的最小值
            fMinvol = s_afCellVoltRaw[i];
        }
        //以下代码为暂时规避电芯采样过程中突然会出现一条采样失效的场景，电芯电压为0.01
        if (((s_afCellVoltBak[i] > 0.05 && s_afCellVoltRaw[i] < 0.05) || (fabs(s_afCellVoltBak[i]-s_afCellVoltRaw[i]) > 2)) && s_aucCellVolInvalidCounter[i] < 3) {
            s_aucCellVolInvalidCounter[i]++;
            s_afCellVoltRaw[i] = s_afCellVoltBak[i];
        } else {
            s_aucCellVolInvalidCounter[i] = 0;
            s_afCellVoltBak[i] = s_afCellVoltRaw[i];
        }
    }
    CellVoltAssign(ucCellVoltNum);
//    if (TRUE == s_tCellComp.bValid && 0 != s_tCellComp.ucPos && s_tCellComp.ucPos <= CELL_VOL_NUM)
//    {
//        s_tBcmData.afCellVolt[s_tCellComp.ucPos - 1] -= s_tBcmData.fBattCurr * s_tCellComp.fCompValue / 1000.0; //0.43毫欧补充 Added by fengfj, 2020-11-09 14:11:57
//    }
}

static void RestartIC(void)
{
    Open_IC_Onece(FALSE);
    rt_thread_delay(400);
    Open_IC_Onece(TRUE);
}

static int UpdateVoltageFromMT9818(void)
{
    int Result = 0;
    BYTE ucCellVoltNum = 0;
    BYTE aucTmp[36] = {0x00, };
    T_BCMAlarmStruct tBcmAlm;

    if (IsSleep())  // 休眠模式不做单体采样
    {
        return 0;
    }
    GetBcmRealAlarm(&tBcmAlm);
#ifndef UNITEST
    if(s_BattChip_device!=RT_NULL)
    {
        Result = rt_device_read(s_BattChip_device, s_ucVolReg, aucTmp, 18*2);
        if(0 >= Result)
        {
            s_ucIICComFailCounter++;
            if (s_ucIICComFailCounter >= 5)
            {
                RestartIC();
                SaveAction(GetActionId(CONTOL_SAMPLE_IC_RESTART), "SampleFault ICRst");
                s_ucIICComFailCounter = 0;
            }
            s_ucCellVoltSampleFault = FAULT;
            tBcmAlm.ucCellVoltSampleFault = FAULT;
            TransBcmAlarm(&tBcmAlm);
            return Result;
        }

    }
    else
    {
        s_ucCellVoltSampleFault = FAULT;
        tBcmAlm.ucCellVoltSampleFault = FAULT;
        TransBcmAlarm(&tBcmAlm);
        return Result;
    }

#endif

    if (s_tHardwarePara.ucCellVoltNum <1 || s_tHardwarePara.ucCellVoltNum > 16)
    {
        return 0;
    }
#ifdef DEVICE_USING_D121
    ucCellVoltNum = s_tHardwarePara.ucCellVoltNum;
#else

    ucCellVoltNum = (s_tHardwarePara.ucCellVoltNum == R121_CELL_VOLT_NUM) ? s_tHardwarePara.ucCellVoltNum : CELL_VOL_NUM_MAX;

#endif
    if(s_ucBalanceReg == CB_ACTIVE_CELLS)
    {
        aucTmp[(ucCellVoltNum-1)*2]   = aucTmp[15*2];
        aucTmp[(ucCellVoltNum-1)*2+1] = aucTmp[15*2+1];
    }
    else if(s_ucBalanceReg == CELLBAL1)
    {
        aucTmp[(ucCellVoltNum-1)*2]   = aucTmp[17*2];
        aucTmp[(ucCellVoltNum-1)*2+1] = aucTmp[17*2+1];
    }
    s_ucIICComFailCounter = 0;
    s_ucCellVoltSampleFault =  NORMAL;
    tBcmAlm.ucCellVoltSampleFault = NORMAL;
    TransBcmAlarm(&tBcmAlm);
    Update_CellVol(aucTmp, ucCellVoltNum);
    return 0;
}

static void Chip_Balance(void *arg)
{
      static rt_uint8_t s_ucBalanceCnt = 0;
      while(s_ucBalanceCnt <= 5)
        {
            if(RT_EOK != rt_device_control(s_BattChip_device, s_ucBalanceReg , arg)) 
            {
             rt_device_close(s_BattChip_device);
             SampleDeviceFind();
             s_ucBalanceCnt ++;
            }
            else
            {
                CellBalTime_Staticise(&s_tHardwarePara , s_wBalanceCode);    
                break;
            }
        }
        s_ucBalanceCnt = 0;
    
}
static void CtrlBalance(void)
{
    BYTE ucTmp[3] = {0};

#ifndef UNITEST
    if(GetApptestFlag() || GetQtpBalanceCircCheckFlag() || GetNormalBalanceCircCheckFlag()) //如果是进入了apptest/qtp模式触发的均衡，单独控制
    {
        if(s_tBalanceCtrl.wExpected & 0xAAAA)
        {
            s_wBalanceCode = s_tBalanceCtrl.wExpected & 0xAAAA;
        }
        else
        {
            s_wBalanceCode = s_tBalanceCtrl.wExpected & 0x5555;
        }
    }
    else 
    {
        if (True == s_tBalanceCtrl.bOddBit)
        {
            s_wBalanceCode = s_tBalanceCtrl.wExpected & 0xAAAA;
        }
        else
        {
            s_wBalanceCode = s_tBalanceCtrl.wExpected & 0x5555;
        }
    }
#endif
/*
    if (0 == wTmp)
    {
        wTmp = s_tBalanceCtrl.wExpected;
    }
*/
#if !defined(UNITEST)
    if(s_ucBalanceReg == CB_ACTIVE_CELLS)
    {
        // rt_device_control(s_BattChip_device, s_ucBalanceReg , &s_wBalanceCode);//TI
        Chip_Balance(&s_wBalanceCode);
    }
    if(s_ucBalanceReg == CELLBAL1)
    {
        ucTmp[0] = (BYTE)(s_wBalanceCode & 0x3f);
        ucTmp[1] = (BYTE)((s_wBalanceCode>>6) & 0x3f);
        ucTmp[2] = (BYTE)((s_wBalanceCode>>12) & 0x3f);
        ucTmp[2] = (ucTmp[2]&0x08) > 0 ? ucTmp[2]^0x28 : ucTmp[2];//硬件强相关
        Chip_Balance(&ucTmp);
    }
    if(GetApptestFlag() || GetQtpBalanceCircCheckFlag() || GetNormalBalanceCircCheckFlag()) //如果是进入了apptest/qtp模式触发的均衡，单独控制
    {
#ifndef KW_CHECK
        rt_thread_delay(800);
#endif
    }
#endif

    if(0 != s_tBalanceCtrl.wExpected)
    {
        s_tBalanceCtrl.bOddBit ^= 0x01;   //切换奇偶位
    }
    s_tBalanceCtrl.bCtrl = False;
}
#endif
void SetBattBalance(WORD wBalVal)
{
#ifdef BALANCE_CELL_15
    WORD wTmp1, wTmp2;
#endif
    // D121整机8串按照下面策略执行
    s_tBalanceCtrl.wExpected = (wBalVal & (1<<(s_tHardwarePara.ucCellVoltNum -1))) > 0 ? (wBalVal|0X8000)^(1<<(s_tHardwarePara.ucCellVoltNum -1)): wBalVal;//硬件决定
    s_tBalanceCtrl.bCtrl = True;

#ifdef BALANCE_CELL_15
    // R321整机15串电芯，需要特殊处理
    if(GetApptestFlag()) //如果是进入了apptest模式触发的均衡，单独控制
    {
        s_tBalanceCtrl.wExpected = wBalVal;
        s_tBalanceCtrl.bCtrl = True;
        return;
    }
    wTmp1 = wBalVal & ((1 << s_ucMinvolNo) - 1);
    wTmp2 = (wBalVal - wTmp1) * 2;
    s_tBalanceCtrl.wExpected = wTmp1 | wTmp2;
    s_tBalanceCtrl.bCtrl = True;
#endif
}

BOOLEAN IsCellEqualOn(void)
{
    if (0 == s_wBalanceCode)
    {
        return False;
    }
    else
    {
        return True;
    }
}
#ifndef SHIELD_BALANCE_CIRCUIT_OPENALM
static void BalanceCircuitCheck(void)
{
    if (s_bBalanceStart && (!s_tBalanceCtrl.bCtrl))
    {
        if(s_bActiveBalanceFlag)
        {
            EqualCircuitFaultAct();
        }
        else
        {
            SetBattBalance(0);
        }
    }
}
#endif
void CheckEqualCircuitFault(void)
{
    s_bBalanceStart = True;
}

BYTE GetCircuitCellNum(void) {
    T_HardwareParaStruct tHardwarePara;
    rt_memset(&tHardwarePara, 0, sizeof(T_HardwareParaStruct)); 
    readBmsHWPara(&tHardwarePara);
    return tHardwarePara.ucCellVoltNum;
}

#ifndef SHIELD_BALANCE_CIRCUIT_OPENALM
static void EqualCircuitFaultAct(void)
{
    WORD wBalVal = 0;
    
    if (s_ucEqualCircuitFault)
    {
        s_ucBalanceChl = 0;
        s_bBalanceStart = False;
        SetQtpBalanceCircCheckFlag(False);
        SetNormalBalanceCircCheckFlag(False);
        SetBattBalance(0);
        return;
    }

    if (0 == s_ucBalanceChl)
    {
        s_ucEqualCircuitFault = EQUAL_CIRCUIT_NORMAL;
        wBalVal = 0;
    }
    else
    {
        wBalVal = 1 << (s_ucBalanceChl-1);
    }

    if (s_ucBalanceChl > GetCircuitCellNum())
    {
        s_ucEqualCircuitFault = EQUAL_CIRCUIT_NORMAL;
        s_ucBalanceChl = 0;
        s_bBalanceStart = False;
        SetQtpBalanceCircCheckFlag(False);
        SetNormalBalanceCircCheckFlag(False);
        SetBattBalance(0);
        return;
    }

    SetBattBalance(wBalVal);
}

static void JudgeCircuitFaultStatus(void)
{
    if(0 == s_ucBalanceChl)
    {
        if(IsEqualCircuitFaultDetected())
        {
            s_ucEqualCircuitFault = EQUAL_CIRCUIT_SHORT;  //短路检测
        }
        else
        {
            s_ucEqualCircuitFault = EQUAL_CIRCUIT_NORMAL;
        }
    }
    else
    {
        if(!IsEqualCircuitFaultDetected())
        {
#ifdef SHIELD_BALANCE_CIRCUIT_OPENALM
            s_ucEqualCircuitFault = EQUAL_CIRCUIT_NORMAL; // R321成果鉴定不验收断路故障
#else
            s_ucEqualCircuitFault = EQUAL_CIRCUIT_OPEN;  //断路检测
#endif
        }
        else
        {
            s_ucEqualCircuitFault = EQUAL_CIRCUIT_NORMAL;
        }
    }
    return;
}

Static void EqualCircuitFaultJudge(void)
{
    WORD uRecTime = 0;
    do{
        JudgeCircuitFaultStatus();
        uRecTime ++;
#ifndef KW_CHECK
        rt_thread_delay(1);
#endif
    }while(s_ucEqualCircuitFault && uRecTime < 1000);

    s_bActiveBalanceFlag = False;
    s_ucBalanceChl++;
}
#endif

BYTE GetEqualCircuitFault(void)
{
    if (s_bBalanceStart)
    {
        return 0xff;
    }
    else
    {
        return s_ucEqualCircuitFault;
    }
}

void ClearBlanceCircuitStatus(void)
{
    s_ucEqualCircuitFault = EQUAL_CIRCUIT_NORMAL;
    s_ucBalanceChl = 0;
    s_bBalanceStart = False;
    SetQtpBalanceCircCheckFlag(False);
    SetNormalBalanceCircCheckFlag(False);
    SetBattBalance(0);
    return;
}

void SetConnTempHighPrtStatus(BYTE ucConnTempHighPrtStatus) {
    s_tBcmData.ucConnTempHighPrtStatus = ucConnTempHighPrtStatus;
    return;
}

BOOLEAN GetNormalBalanceCircCheckFlag(void)
{
    return s_bNormalBalaCircCheckFlag;
}

BOOLEAN SetNormalBalanceCircCheckFlag(BOOLEAN Param)
{
    return s_bNormalBalaCircCheckFlag = Param;
}
#ifndef SHIELD_BALANCE_CIRCUIT_OPENALM
static void DayTimeEqualCircuitCheck(void)
{
    static LONG s_lDayTimeEqualCirCheckFlag = 0; //用于均衡故障检测时间计数

    if(s_ucEqualCircuitFault == 1|| s_ucEqualCircuitFault == 2)  //当已经检测出故障后不再进行检测
    {
        return;
    }

    if (s_lDayTimeEqualCirCheckFlag++ >= Equal_Circuit_Check_ONE_DAY_COUNT)
    {
        s_lDayTimeEqualCirCheckFlag = 0;
        ClearBlanceCircuitStatus();  //正常状态下均衡电路故障检测，一天检测一次
        CheckEqualCircuitFault();
        SetNormalBalanceCircCheckFlag(TRUE);
    }
}
#endif

/****************************************************************************
* 函数名称：CheckDataIsExtreme
* 功能描述：检测极值的变化
* 修改记录：
***************************************************************************/
BOOLEAN CheckDataIsExtreme(void)
{
    BOOLEAN UpdateFlag = False;
    BYTE i = 0;
    FLOAT fBattMaxVolt = 0.0;
    FLOAT fBattMinVolt = 0.0;
    FLOAT fBusMaxChgCurr = 0.0;
    FLOAT fBusMaxDischgCurr = 0.0;
    GetSysPara(&s_tSysPara);
    
    if(s_tHardwarePara.ucCellVoltNum == R121_CELL_VOLT_NUM)
    {
        fBattMaxVolt = 28.0;
        fBattMinVolt = 22.4;
        fBusMaxChgCurr = 50.0;
        fBusMaxDischgCurr = 52.0;
    }
    else
    {
        fBattMaxVolt = 52.5;
        fBattMinVolt = 42.0;
        fBusMaxChgCurr = 100.0;
        fBusMaxDischgCurr = 105.0;
    }
    //电池电压最大值,当前值>上次最大值，and 当前值>52.5V
    MaxValueJudgment(&s_tBcmData.fBattVolt, &s_atHisExtremeData.fBattVoltMax, fBattMaxVolt, &s_atHisExtremeData.BattVoltMaxTime, &UpdateFlag);

    //电池电压最小值, 当前值<上次最小值，and 当前值<42V	
    MinValueJudgment(&s_tBcmData.fBattVolt, &s_atHisExtremeData.fBattVoltMin, fBattMinVolt, &s_atHisExtremeData.BattVoltMinTime, &UpdateFlag);

    //外部电压最大值, 当前值>上次最大值，and 当前值>58V
    MaxValueJudgment(&s_tBcmData.fExterVolt, &s_atHisExtremeData.fExterVoltMax, 58, &s_atHisExtremeData.ExterVoltMaxTime, &UpdateFlag);

    //环境温度最大值,当前值>上次最大值，and 当前值>环境温度高保护阈值-5°
    MaxValueJudgment(&s_tBcmData.fEnvTemp, &s_atHisExtremeData.fEnvTempMax, 
            s_tSysPara.fEnvTempHighPrtThre - 5, &s_atHisExtremeData.EnvTempMaxTime, &UpdateFlag);

    //环境温度最小值,当前值<上次最小值，and 当前值<环境温度低保护阈值
    MinValueJudgment(&s_tBcmData.fEnvTemp, &s_atHisExtremeData.fEnvTempMin, 
            s_tSysPara.fEnvTempLowPrtThre, &s_atHisExtremeData.EnvTempMinTime, &UpdateFlag);

    //BUS充电电流最大值, 当前充电电流>上次最大值，and 当前充电电流>100A
    if(s_tBcmData.fBusCurr > 0)
    {
        MaxValueJudgment(&s_tBcmData.fBusCurr, &s_atHisExtremeData.fChargeBusCurrMax, fBusMaxChgCurr, &s_atHisExtremeData.ChargeBusCurrMaxTime, &UpdateFlag);
    }

    //BUS放电电流最大值, 当前放电电流>上次最大值，and 当前放电电流>105A
    if(s_tBcmData.fBusCurr < 0)
    {
        SpecialMaxValueJudgment(&s_tBcmData.fBusCurr, &s_atHisExtremeData.fDischargeBusCurrMax, fBusMaxDischgCurr, &s_atHisExtremeData.DischargeBusCurrMaxTime, &UpdateFlag);
    }

    //电池充电电流最大值, 当前充电电流>上次最大值，and 当前充电电流>100A
    if(s_tBcmData.fBattCurr > 0)
    {
        MaxValueJudgment(&s_tBcmData.fBattCurr, &s_atHisExtremeData.fChargeBattCurrMax, 100, &s_atHisExtremeData.ChargeBattCurrMaxTime, &UpdateFlag);
    }

    //电池放电电流最大值, 当前放电电流>上次最大值，and 当前放电电流>105A
    if(s_tBcmData.fBattCurr < 0)
    {
        SpecialMaxValueJudgment(&s_tBcmData.fBattCurr, &s_atHisExtremeData.fDischargeBattCurrMax, 105, &s_atHisExtremeData.DischargeBattCurrMaxTime, &UpdateFlag);
    }

    for(i = 0; i < CELL_VOL_NUM_MAX; i++)
    {
        //单体电压最大值,当前值>上次最大值，and 当前值>单体充电截止电压阈值
        if(s_ucCellVoltSampleFault == NORMAL) //单体电压采样异常数据过滤掉
        {
            //单体电压最大值,当前值>上次最大值，and 当前值>单体充电截止电压阈值
            MaxValueJudgment(&s_tBcmData.afCellVolt[i], &s_atHisExtremeData.fCellVoltMax[i], 
                    s_tSysPara.fCellChargeFullVolt, &s_atHisExtremeData.CellVoltMaxTime[i], &UpdateFlag);

            //单体电压最小值,当前值<上次最小值，and 当前值<2.9V
            MinValueJudgment(&s_tBcmData.afCellVolt[i], &s_atHisExtremeData.fCellVoltMin[i], 
                    2.9, &s_atHisExtremeData.CellVoltMinTime[i], &UpdateFlag);
        }
    }

    for(i = 0; i < CELL_TEMP_NUM_MAX; i++)
    {
        if(s_tBcmData.afCellTemp[i] <= CELLTEMP_MAX) //单体温度过滤无效值
        {
            //单体温度最小值, 当前值>上次最大值，and 当前值>50°C
            MaxValueJudgment(&s_tBcmData.afCellTemp[i], &s_atHisExtremeData.fCellTempMax[i], 
                    50, &s_atHisExtremeData.CellTempMaxTime[i], &UpdateFlag);
        }
        if(s_tBcmData.afCellTemp[i] >= CELLTEMP_MIN) //单体温度过滤无效值
        {
            //单体温度最小值, 当前值<上次最小值，and 当前值<充电低温保护阈值
            MinValueJudgment(&s_tBcmData.afCellTemp[i], &s_atHisExtremeData.fCellTempMin[i], 
                    s_tSysPara.fChgTempLowPrtThre, &s_atHisExtremeData.CellTempMinTime[i], &UpdateFlag);
        }
    }
    return UpdateFlag;
}


/****************************************************************************
* 函数名称：SpecialMaxValueJudgment
* 功能描述：特殊最大值的变化，即取值为负，但是要存入极大值
* 修改记录：
***************************************************************************/
static void SpecialMaxValueJudgment(float *pfCurrentValue, float *pfCurrentExtreme, float fCondition, time_t* SaveTime,BOOLEAN *UpdateFlag)
{   
    if(CheckFloatDefaultValue(*pfCurrentExtreme) == True)
    {
        if(fabs(*pfCurrentValue)  > fCondition)
        {
            *pfCurrentExtreme = fabs(*pfCurrentValue);
            time(SaveTime);
            *UpdateFlag = True;
        }
    }
    else if(fabs(*pfCurrentValue) > *pfCurrentExtreme && fabs(*pfCurrentValue)  > fCondition)
    {
        *pfCurrentExtreme = fabs(*pfCurrentValue);
        time(SaveTime);
        *UpdateFlag = True;
    }
}


/****************************************************************************
* 函数名称：MaxValueJudgment
* 功能描述：判断最大值的变化
* 修改记录：
***************************************************************************/
static void MaxValueJudgment(float *pfCurrentValue, float *pfCurrentExtreme, float fCondition, time_t* SaveTime,BOOLEAN *UpdateFlag)
{   
    if(CheckFloatDefaultValue(*pfCurrentExtreme) == True)
    {
        if(*pfCurrentValue  > fCondition)
        {
            *pfCurrentExtreme = *pfCurrentValue;
            time(SaveTime);
            *UpdateFlag = True;
        }
    }
    else if(*pfCurrentValue > *pfCurrentExtreme && *pfCurrentValue  > fCondition)
    {
        *pfCurrentExtreme = *pfCurrentValue;
        time(SaveTime);
        *UpdateFlag = True;
    }
}


/****************************************************************************
* 函数名称：MinValueJudgment
* 功能描述：判断最小值的变化
* 修改记录：
***************************************************************************/
static void MinValueJudgment(float *pfCurrentValue, float *pfCurrentExtreme, float fCondition, time_t* SaveTime,BOOLEAN *UpdateFlag)
{
    if(CheckFloatDefaultValue(*pfCurrentExtreme) == True)
    {
        if(*pfCurrentValue  < fCondition)
        {
            *pfCurrentExtreme = *pfCurrentValue;
            time(SaveTime);
            *UpdateFlag = True;
        }
    }
    else if(*pfCurrentValue < *pfCurrentExtreme && *pfCurrentValue  < fCondition)
    {
        *pfCurrentExtreme = *pfCurrentValue;
        time(SaveTime);
        *UpdateFlag = True;
    }
}


/****************************************************************************
* 函数名称：GetExtremeData
* 功能描述：获取暂存的极值数据
* 修改记录：
***************************************************************************/
void GetExtremeData(T_HisExtremeData * pDest )
{
    if (NULL == pDest)
    {
        return;
    }

    rt_memcpy(pDest, (BYTE *)&s_atHisExtremeData, sizeof(T_HisExtremeData));
}


/****************************************************************************
* 函数名称：SetExtremeData
* 功能描述：设定初始的极值数据
* 修改记录：
***************************************************************************/
void SetExtremeData(T_HisExtremeData const* pDest)
{
    rt_memcpy( &s_atHisExtremeData, pDest, sizeof(T_HisExtremeData));
}


/****************************************************************************
* 函数名称：GetExtremeFlag
* 功能描述：获取是否有极值变化的标志
* 修改记录：
***************************************************************************/
BOOLEAN GetExtremeFlag()
{
    return s_bHisExtremeUpadate;
}


/****************************************************************************
* 函数名称：SetExtremeFlag
* 功能描述：设定极值变化的标志
* 修改记录：
***************************************************************************/
void SetExtremeFlag(BOOLEAN InputFlag)
{
    s_bHisExtremeUpadate = InputFlag;
}


/****************************************************************************
* 函数名称：UpdateExtremeFlag
* 功能描述：更新机值变化的标志
* 修改记录：
***************************************************************************/
Static void UpdateExtremeFlag()
{
    if(CheckDataIsExtreme() == True)
    {
        s_bHisExtremeUpadate = True;
    }
}

/****************************************************************************
* 函数名称：检测是否为初始值
* 功能描述：更新机值变化的标志
* 修改记录：
***************************************************************************/
static BOOLEAN CheckFloatDefaultValue(float fInputValue)
{
    float fTemp;
    rt_memset(&fTemp, 0xFF, sizeof(float));
    if(0 == rt_memcmp(&fInputValue, &fTemp, sizeof(float)))
    {   
        //若为默认值，则返回True
        return True;
    }
    return False;
}

WORD GetBalaneceCode(void)
{//获取电芯实际均衡状态
    return s_wBalanceCode;
}


/****************************************************************************
* 函数名称：CalCellTempRiseRate
* 返 回 值：False:计算有误
           True:计算正确
* 功能描述：计算单体温升速率是否达到条件
* 作    者：
* 设计日期：2024-2-5
* 修改记录：
***************************************************************************/
static BOOLEAN CalCellTempRiseRate(FLOAT sampletemp, BYTE CellIndex)
{
    FLOAT pretemp = 0, curtemp = 0;
    static FLOAT fTempRiseTre = 1.0;
    BYTE CurIndex = 0, PreIndex = 0;
    T_SysPara *pSysPara = NULL;

    if(CellIndex >= CELL_TEMP_NUM) //超过四路单体温度时返回错误
    {
        return False;
    }

    pSysPara = GetSysParaPointer();
    if(fabs(pSysPara->fCellTempRiseAbnormalThre - fTempRiseTre) > 0.01 
        && pSysPara->fCellTempRiseAbnormalThre >= 0.1)
    {
        fTempRiseTre = pSysPara->fCellTempRiseAbnormalThre;
    }

    CurIndex = s_tTempRiseRateData.ucMeasureIndex;
    PreIndex = (s_tTempRiseRateData.ucMeasureIndex + 1) % SAMPLE_TIME_SNUM;

    s_tTempRiseRateData.fTempMeasureMents[CellIndex][CurIndex] = sampletemp;//更新当前温度
    pretemp = s_tTempRiseRateData.fTempMeasureMents[CellIndex][PreIndex];//1s前的温度
    curtemp = s_tTempRiseRateData.fTempMeasureMents[CellIndex][CurIndex];//当前温度
    
    s_tTempRiseRateData.fCellTempRiseRate[CellIndex] = curtemp - pretemp;//每10s的温升速率

    if(s_tTempRiseRateData.bCellTempRiseRateFault == False)
    {
        if(s_tTempRiseRateData.fCellTempRiseRate[CellIndex] > fTempRiseTre)//1.0)
        {
            //s_tTempRiseRateData.wCellTempRiseTimeCounter[CellIndex]++;
            TimerPlus(s_tTempRiseRateData.wCellTempRiseTimeCounter[CellIndex],CELL_TEMP_RISE_COUNTER);
            if(TimeOut(s_tTempRiseRateData.wCellTempRiseTimeCounter[CellIndex], CELL_TEMP_RISE_COUNTER))
            {
                s_tTempRiseRateData.bCellTempRiseGenerageJudge[CellIndex] = True;
            }
        }
        else
        {
            s_tTempRiseRateData.wCellTempRiseTimeCounter[CellIndex] = 0;
            s_tTempRiseRateData.bCellTempRiseGenerageJudge[CellIndex] = False;
        }
    }
    else
    {
        s_tTempRiseRateData.wCellTempRiseTimeCounter[CellIndex] = 0;
        s_tTempRiseRateData.bCellTempRiseGenerageJudge[CellIndex] = False;
    }

    return True;
}

/****************************************************************************
* 函数名称：GetTempRiseRateFault
* 返 回 值：当前温升速率异常值，False表示没有产生单体温升速率异常，True表示产生单体温升速率异常
* 功能描述：获取当前温升速率异常的状态，False表示没有产生告警，1表示告警产生
* 作    者：
* 设计日期：2024-2-5
* 修改记录：
***************************************************************************/
BOOLEAN GetTempRiseRateFault(void)
{
    return s_tTempRiseRateData.bCellTempRiseRateFault;
}


/****************************************************************************
* 函数名称：InitTempRiseRateData
* 返 回 值：返回True
* 功能描述：单体温升速率异常告警判断初始化函数
* 作    者：
* 设计日期：2024-2-5
* 修改记录：
***************************************************************************/
BOOLEAN InitTempRiseRateData(void)
{
    BYTE i = 0, j =0;
    rt_memset(&s_tTempRiseRateData, 0, sizeof(T_TempRiseRateStruct));
    for(i = 0;i < CELL_TEMP_NUM;i ++)
    {
        for(j = 0;j < SAMPLE_TIME_SNUM; j ++)
        {
            s_tTempRiseRateData.fTempMeasureMents[i][j] = 100.0f;
        }
    }
    return True;
}

/****************************************************************************
* 函数名称：InitTempRiseRateData
* 返 回 值：返回当前单体温升速率异常的状态
* 功能描述：单体温升速率异常判断功能入口函数
* 作    者：
* 设计日期：2024-2-5
* 修改记录：
***************************************************************************/
BOOLEAN CellTempRiseAbnormalEnter(void)
{
    INT32 i = 0;

    for(i = 0; i < s_tHardwarePara.ucCellTempNum; i++)
    {   
        if(True == CalCellTempRiseRate(s_tBcmData.afCellTemp[i], i))
        {
            if(s_tTempRiseRateData.bCellTempRiseGenerageJudge[i] && s_tBcmData.afCellTemp[i] > 45) //温升速率大于1℃/1s，且单体温度大于45℃，连续满足3次，则产生告警
            {
                s_tTempRiseRateData.bCellTempRiseRateFault = True;
            }
        }
    }

    s_tTempRiseRateData.ucMeasureIndex = (s_tTempRiseRateData.ucMeasureIndex + 1) % SAMPLE_TIME_SNUM;//最后一路单体温度温升速率计算完毕，更新温升速率数组索引
    if(s_tBcmData.fCellTempMax < 40 && s_tTempRiseRateData.bCellTempRiseRateFault) //出现单体温升速率异常，且单体最大温度小于40度，连续判断3次，则告警消失
    {
        TimerPlus(s_tTempRiseRateData.wCellTempRiseRecCounter,CELL_TEMP_RISE_COUNTER);
        if(TimeOut(s_tTempRiseRateData.wCellTempRiseRecCounter,CELL_TEMP_RISE_COUNTER))
        {
            s_tTempRiseRateData.bCellTempRiseRateFault = False;
        }
    }
    else
    {
        s_tTempRiseRateData.wCellTempRiseRecCounter = 0;
    }

    return s_tTempRiseRateData.bCellTempRiseRateFault;

}


Static void CellVoltAssign(BYTE ucCellVoltNum)
{
    BYTE i = 0;

#ifdef DEVICE_USING_D121
    for (i = 0; i < ucCellVoltNum; i++)
    {
        s_tBcmData.afCellVolt[i] = s_afCellVoltRaw[i];
    }
#else
    if(s_tHardwarePara.ucCellVoltNum == R121_CELL_VOLT_NUM)
    {
        for (i = 0; i < ucCellVoltNum; i++)
        {
            s_tBcmData.afCellVolt[i] = s_afCellVoltRaw[i];
        }
    }
    else
    {
        for (i = 0; i < CELL_VOL_NUM; i++)
        {
            s_tBcmData.afCellVolt[i] = (i < s_ucMinvolNo)? s_afCellVoltRaw[i] : s_afCellVoltRaw[i+1];
        }
        s_tBcmData.afCellVolt[CELL_VOL_NUM] = 0;
    }
#endif
    return;
}


