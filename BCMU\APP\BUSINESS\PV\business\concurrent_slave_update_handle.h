#ifndef _CONCURRENCT_SLAVE_UPDATE_HANDLE_H_
#define _CONCURRENCT_SLAVE_UPDATE_HANDLE_H_

#ifdef __cplusplus
extern "C" {
#endif
typedef enum
{
    NO_UPDATE  = 0,
    UPDATE_TRIG,
    UPDATE_TRAN_DATA,
    UPDATE_DATA_CONFIRM,
    UPDATE_CONFIRM,
}update_stat;

#define     CONCURR_UPDATE_TIME             30000
#define     CONCURR_UPDATE_DATA_FRAM_TIME   600000
#define     POWER_UPDATE_WAIT_TIME          150000

int parse_extern_trig_data(void* dev_inst, void* cmd_buf);
int pack_extern_trig_data(void* dev_inst, void *cmd_buf);
int parse_concur_update_data(void* dev_inst, void *cmd_buf);
int pack_concur_update_data(void* dev_inst, void *cmd_buf);
int parse_concur_confirm_update_data(void* dev_inst, void *cmd_buf);
int pack_concur_confirm_update_data(void* dev_inst, void *cmd_buf);
int parse_concur_confirm_update(void* dev_inst, void *cmd_buf);
int pack_concur_confirm_update(void* dev_inst, void *cmd_buf);
int erase_download_flash();
int record_loss_frm_when_confirm();
short record_loss_frm(unsigned short cur_frame_no);
int start_slave_concurr_update_timer(unsigned int tm);
void init_concurr_update_slave();
void concurr_update_timeout();
int set_north_trans_end_flag(unsigned char flag);
int register_concurrent_update_slave_cmd_handle();
#ifdef __cplusplus
}
#endif

#endif  


