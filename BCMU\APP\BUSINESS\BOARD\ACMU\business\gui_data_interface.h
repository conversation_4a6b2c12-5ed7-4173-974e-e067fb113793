/**
 * @file     gui_data_interface.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2025-06-13
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */

#ifndef _GUI_DATA_INTERFACE_H
#define _GUI_DATA_INTERFACE_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "dev_acmu.h"
#include "rtdef.h"
#include "data_type.h"
#include "his_record.h"
#include "MIBTable.h"
#include "io_control.h"

#define LEN_PASER            4     // 菜单口令长度
#define COMM_PORT_NUM       2     // 串行口数量
#define ALARM_CLASS_NUM     26    // 总告警数量
#define MAX_EXCLUDE_CONFIG_PARA_NUM  20  //排除配置参数的最大参数数量
#define MAX_EXCLUDE_RUNNING_PARA_NUM  60  //排除运行参数的最大参数数量

// 参数类型定义
#define PARATYPE_NULL        0
#define    PARATYPE_SYSTEM      1     // 系统参数
#define PARATYPE_ATRRIBUTE   2
#define PARATYPE_INRELAY     3
#define    PARATYPE_AC          4     // 交流参数
#define    PARATYPE_ENV         5     // 环境参数
#define PARATYPE_ADJUST      6     
#define PARATYPE_RUNNING     7
#define PARATYPE_CONFIG      8
#define    PARATYPE_ALARM_RELAY 9    // 告警干节点参数
#define    PARATYPE_ALARM_GRADE 10    // 告警级别参数
#define PARATYPE_ALL         11

// 告警类型定义
#define    ALL_PART        0
#define    AC_PART            1
#define    SMR_PART        2
#define    DC_PART            3
#define    ENV_PART        4
#define    CSU_PART        5

#define KEY_BUFF_LEN        6

#define KEY_ESC              0x37
#define KEY_ENTER           0x2F
#define KEY_UP              0x3D
#define KEY_DOWN            0x3B
#define KEY_NOTHING             0x3F
#define KEY_UP_DOWN         0x39
#define KEY_UP_ENTER        0x2D    
#define KEY_DOWN_ENTER        0x2B 
#define KEY_LEFT_RIGHT_ENT  0x0E
#define KEY_LEFT            0x1F    
#define KEY_RIGHT            0x3E

#define	CTRL_NULL		0x00	
#define	CTRL_ON			0x01	
#define	CTRL_OFF		0x02

/* 按键数据结构 */
struct key_info {
    rt_base_t pin;      // 引脚号
    rt_uint8_t id;           // 按键ID
    rt_bool_t last_state; // 上次状态
    rt_tick_t last_press_time; // 上次按下时间
    rt_tick_t last_release_time; // 上次松开时间
};

struct keys_state {
    rt_uint8_t last_keys_state;    // 组合按键上次按键状态
    rt_uint8_t current_keys_state;
    rt_tick_t last_keys_press_time; // 上次按下时间
};

/************************************************************
** 结构名: T_ACDigitalDataStruct
** 描  述: 交流段实时状态量数据结构定义,用于保存采样结果
** 作  者: 潘奇银
** 日  期: 2008-02-22
** 版  本: V5.0 
** 修改记录            
** 日  期        版    本        修改人        修改摘要
** 
**************************************************************/
typedef    struct
{
    unsigned char    ucPowerSource;                //交流供电方式：0市电1；1市电2；2电池
    unsigned char    ucACMainSwitch;            //交流输入空开：0市电1；1市电2
    unsigned char    aucACOutSwitch[SWITCH_NUM];    //交流输出空开：0 闭合；1 断开
    unsigned char    ucLamp;                        // 应急照明灯状态:0灭；1亮
    unsigned char    aucInputRelay[RELAY_NUM];        // 输入继电器状态：0断开；1闭合 闭合说明要告警
    unsigned char    ucACSPD_C;                    // C级防雷回路：0正常；1故障
    unsigned char     ucACEMState;                //交流电表在位：0不在位，1在位
    
}T_ACDigitalDataStruct;

/************************************************************
** 结构名: T_ENVDigitalDataStruct
** 描  述: 环境实时状态量数据结构定义,用于保存采样结果
** 作  者: 潘奇银
** 日  期: 2008-02-22
** 版  本: V5.0 
** 修改记录            
** 日  期        版    本        修改人        修改摘要
** 
**************************************************************/
typedef    struct
{
    unsigned char        ucSmog;            // 烟雾：0正常；1故障
    unsigned char        ucFlood;        // 水淹：0正常；1故障
    unsigned char        ucDoorMag;        // 门磁：0正常；1故障
    unsigned char        ucDoorInfrared;    // 门禁：0正常；1故障
    unsigned char        ucGlass;            // 玻璃碎：0正常；1故障
}T_ENVDigitalDataStruct;

/************************************************************
** 结构名: T_DigitalDataStruct
** 描  述: 实时状态量数据结构定义,用于保存采样结果
** 作  者: 潘奇银
** 日  期: 2008-01-11
** 版  本: V5.0 
** 修改记录            
** 日  期        版    本        修改人        修改摘要
** 
**************************************************************/
typedef    struct
{
    T_ACDigitalDataStruct    tACData;
    T_ENVDigitalDataStruct    tENVData;

}T_DigitalDataStruct;

/************************************************************
** 结构名: T_AcAnalogDataStruct
** 描  述: AC实时模拟量数据结构定义
** 作  者: 潘奇银
** 日  期: 2008-01-11
** 版  本: V5.0 
** 修改记录            
** 日  期        版    本        修改人        修改摘要
** 
**************************************************************/
typedef struct
{
    float    aiPhaseVolt[3];        // 交流相电压
    float    aiIin[3];                // 交流线电流
    float    aiPhasePhaseVolt[3];        // 交流线电压
    float    aslAcPhaseCur[3];        //交流相电流
    int    slSumActPower;            //总有功功率
    int    slPhaseActPower[3];        //三相有功功率
    int     slSumReactPower;        //总无功功率
    int    slSumApparentPower;        //总视在功率
    signed short slSumPowerFactor;        //总功率因数
    int    slFrequency;            //交流频率
    unsigned int     slAllenergy;            //总电能
    signed short aslPowerFactor[3];    //交流相功率因数
}T_AcAnalogDataStruct;

/************************************************************
** 结构名: T_EnvAnalogDataStruct
** 描  述: ENV实时模拟量数据结构定义
** 作  者: 潘奇银
** 日  期: 2008-01-11
** 版  本: V5.0 
** 修改记录            
** 日  期        版    本        修改人        修改摘要
** 
**************************************************************/
typedef struct
{    
    char    scTemp;        // 环境温度
    char    scHum;            // 环境湿度
}T_EnvAnalogDataStruct;

typedef    struct
{
    T_AcAnalogDataStruct        tAcAnalogData;
    T_EnvAnalogDataStruct        tEnvAnalogData;
}T_AnalogDataStruct;


/************************************************************
** 结构名: T_CommonParaStruct
** 描  述: 通用系统参数结构定义
** 作  者:
** 日  期: 2025-06-28
** 版  本: 
** 修改记录
** 日  期        版    本        修改人        修改摘要
**
**************************************************************/
#pragma pack(push, 1)
typedef struct
{
    unsigned char    ucBeepOn;                        // 蜂鸣器开关
    unsigned char    ucHisDataInterval;                // 历史数据保存间隔时间
    unsigned char    aucCOMRate[COMM_PORT_NUM];        // COM波特率
    unsigned short    awDeviceAddr;                    // 本地地址
    unsigned char    ucLanguage;                        // 语言版本
    unsigned char    aucPaserword[LEN_PASER];           // 菜单口令
}T_CommonParaStruct;

typedef struct
{
    unsigned char    usageScenario;         // 使用场景
    unsigned short    awOutCurrTransType;         // 输出电流互感类型
}T_CommonParaStruct2;

/************************************************************
** 结构名: T_AcParaStruct
** 描  述: 交流参数结构定义
** 作  者:
** 日  期: 2025-06-28
** 版  本: 
** 修改记录
** 日  期        版    本        修改人        修改摘要
**
**************************************************************/
typedef struct
{
    unsigned short    sAcVoltMax;                    // 交流电压高阈值
    unsigned short    sAcVoltMin;                    // 交流电压低阈值
    unsigned short    sAcVolUnbalance;               // 相不平衡电压阀值
    unsigned short    sAcCurrMax;                    // 交流电流高阈值
    signed short    sAcVoltPPMax;                // 交流线电压高阈值
    signed short    sAcVoltPPMin;                // 交流线电压低阈值
    unsigned char   ucCurrCfg[AC_PHASE_NUM];     // 交流线电流IL12配置    TRUE: 配置　FALSE:未配置
    unsigned char   ucSwitchCfg[SWITCH_NUM];    // 交流空开配置    TRUE: 配置　FALSE:未配置
    unsigned short  awCurrTransType[AC_PHASE_NUM]; // 交流互感类型
    unsigned char   ucAcemCfg;                    // 交流电表配置 TRUE: 配置　FALSE:未配置
}T_AcParaStruct;

/************************************************************
** 结构名: T_EnvParaStruct
** 描  述: 环境参数结构定义
** 作  者:
** 日  期: 2025-06-28
** 版  本: 
** 修改记录
** 日  期        版    本        修改人        修改摘要
**
**************************************************************/
typedef struct
{
    short            scEnvTempMax;        // 环境温度高告警值
    short            scEnvTempMin;        // 环境温度低告警值
    unsigned short            scEnvHumMax;        // 环境湿度高告警值
    unsigned short            scEnvHumMin;        // 环境湿度低告警值
}T_EnvParaStruct;

/************************************************************
** 结构名: T_AlarmRelayParaStruct
** 描  述: 告警干节点参数结构定义
** 作  者:
** 日  期: 2025-06-28
** 版  本: 
** 修改记录
** 日  期        版    本        修改人        修改摘要
**
**************************************************************/
typedef struct
{
    unsigned char    aucRlyAlarmTTL[RELAY_NUM];                // 输入干结点告警电平
    char            acRlyName[RELAY_NUM][LEN_RELAYNAME];    // 输入干结点名称
    unsigned char    aucRlyAlarmGrade[RELAY_NUM];        // 输入干结点告警级别
}T_AlarmRelayParaStruct;

/************************************************************
** 结构名: T_AlarmGradeParaStruct
** 描  述: 告警级别参数结构定义
** 作  者:
** 日  期: 2025-06-28
** 版  本: 
** 修改记录
** 日  期        版    本        修改人        修改摘要
**
**************************************************************/
typedef struct
{ 
    unsigned char    aucAlmGrade[ALARM_CLASS_NUM];        // 告警级别
    unsigned char    aucAlmOutRly[ALARM_CLASS_NUM];        // 告警干结点
}T_AlarmGradeParaStruct;

typedef struct
{
    signed char    VoltageZeroPoint[3];    // 交流相电压零点
    signed short    VoltageSlope[3];        // 交流相电压斜率
    signed char    CurrentZeroPoint[3];    // 交流相电流零点
    signed short    CurrentSlope[3];        // 交流相电流斜率
    signed char    EnvTempZeroPoint;        // 环境温度零点
    signed char    EnvHumZeroPoint;        // 环境湿度零点
}T_AdjustParaStruct;

/************************************************************
** 结构名: T_SysPara
** 描  述: 系统参数总结构定义（分类后的系统参数）
** 作  者:
** 日  期: 2025-06-28
** 版  本: 
** 修改记录
** 日  期        版    本        修改人        修改摘要
**
**************************************************************/
typedef struct
{
    T_AcParaStruct            tAcPara;            // 交流参数
    T_AdjustParaStruct        tAdjustPara;        // 校准参数
    T_AlarmRelayParaStruct    tAlarmRelayPara;    // 告警干节点参数
    T_CommonParaStruct        tCommonPara;        // 通用参数
    T_CommonParaStruct2       tCommonPara2;       // 通用参数
    T_EnvParaStruct           tEnvPara;           // 环境参数
    T_AlarmGradeParaStruct    tAlarmGradePara;    // 告警级别参数
}T_SysPara;
#pragma pack(pop)
/************************************************************
** 结构名: T_ParaWnd
** 描  述: 参数窗口属性结构定义（参考Menu.h中的定义）
** 作  者:
** 日  期: 2025-06-28
** 版  本: 
** 修改记录
** 日  期        版    本        修改人        修改摘要
**
**************************************************************/
typedef struct
{
    unsigned char    ucID1;                // 参数ID1
    unsigned char    ucID2;                // 参数ID2
    unsigned char    ucParaIndex;        // 参数设置序号
    unsigned char    ucWndAttrib;        // 参数设置窗属性
}T_ParaWnd;

/************************************************************
** 结构名: T_ACAlarmStruct
** 描  述: AC段告警数据结构定义
** 作  者: 潘奇银
** 日  期: 2008-01-09
** 版  本: V5.0 
** 修改记录            
** 日  期        版    本        修改人        修改摘要
** 
**************************************************************/
typedef struct
{
    unsigned char ucPowerOff;            // 交流停电0
    unsigned char aucPhaseLost[3];        // 交流L1/L2/L3缺相    3
    unsigned char ucPhUnbalance;        // 相电压不平衡4
    unsigned char aucVoltHigh[3];        // 交流UL12/UL23/UL31电压高7
    unsigned char aucVoltLow[3];        // 交流UL12/UL23/UL31电压低10
    
    unsigned char aucCurr[3];            // 交流IL1/IL2/IL3电流高13
    unsigned char ucSPD_C;                // C级避雷器回路异常14
    unsigned char aucOutSwitch[SWITCH_NUM];    // 交流输出空开告警26
    unsigned char aucPhasePhaseVoltHigh[3];        // 交流线电压高29
    unsigned char aucPhasePhaseVoltLow[3];        // 交流线电压低32
    unsigned char ucACEMCommFail;        //    交流电表通讯断33
}T_ACAlarmStruct;

/************************************************************
** 结构名: T_ENVAlarmStruct
** 描  述: ENV段告警数据结构定义
** 作  者: 潘奇银
** 日  期: 2008-01-09
** 版  本: V5.0 
** 修改记录            
** 日  期        版    本        修改人        修改摘要
** 
**************************************************************/
typedef struct
{
    unsigned char ucTempHigh;        // 环境温度高
    unsigned char ucTempLow;        // 环境温度低
    unsigned char ucHumHigh;        // 环境湿度高
    unsigned char ucHumLow;        // 环境湿度低
    unsigned char ucSmog;            // 烟雾告警
    unsigned char ucFlood;            // 水淹告警
    unsigned char ucDoorMag;        // 门磁告警
    unsigned char ucETSensor;        // 环境温度失效
    unsigned char ucEHSensor;        // 环境湿度失效
}T_ENVAlarmStruct;

/************************************************************
** 结构名: T_AlarmStruct
** 描  述: 告警数据结构定义
** 作  者: 潘奇银
** 日  期: 2008-01-09
** 版  本: V5.0 
** 修改记录            
** 日  期        版    本        修改人        修改摘要
** 
**************************************************************/
typedef struct
{
    unsigned char ucCommon;
    T_ACAlarmStruct    tACAlarm;
    T_ENVAlarmStruct    tENVAlarm;    
    unsigned char ucGenStart;
    unsigned char aucInRelay[RELAY_NUM];        // 输入干结点告警

}T_AlarmStruct;



typedef struct
{
    char    aucACEMSysName[20];    //    ACEM 系统名称       
    char    aucACEMSysVersion[10];    //    ACEM 版本号
}T_ACEMFactStruct;

typedef struct
{
    struct
    {
        time_base_t    tTime;
        short        iVolt;
        short        aiVolt[3];
    }tMaxAcVolt;                // 交流电压最大值
    
    struct
    {
        time_base_t    tTime;
        char    scEnvTemp;
    }tMaxEnvTemp;                // 环境温度最大值
    
    struct
    {
        time_base_t    tTime;
        char    scEnvTemp;
    }tMinEnvTemp;                // 环境温度最小值

    struct
    {
        time_base_t    tTime;
        short        iVolt;
        short        aiVolt[3];
    }tMinAcVolt;                // 交流电压最小值
}T_PeakDataStruct;

typedef struct
{
    time_base_t    tTime;
    unsigned char  ucID1;
    unsigned char  ucID2;
    unsigned char  ucIndex;
    char           aucMsg[LEN_EVENTMSG];
}T_EventRecord;

typedef struct
{
    unsigned char        ucAlarmSn;            // 告警序号
    unsigned char        ucIDIndex;            // 告警设备索引(如SMR1/2/3)
    unsigned char        ucAlarmState;           // 告警值 TRUE表示告警 FALSE表示正常
    time_base_t    tStartTime;            // 告警开始时间
    time_base_t    tEndTime;            // 告警开始时间
}T_HisAlarmStruct;

typedef struct
{
    unsigned char        ucAlarmSn;            // 告警序号
    unsigned char        ucIDIndex;            // 告警设备索引(如SMR1/2/3)
    unsigned char        ucAlarmState;       // 告警值 TRUE表示告警 FALSE表示正常
    time_base_t    tStartTime;            // 告警开始时间
}T_RealAlarmStruct;

/******************输出控制结构*******************/	
typedef	struct	
{
	unsigned char		bScreenPower;
	unsigned char		bLcdLight;			/* 液晶背光 TRUE:点亮 FALSE:关闭*/
	unsigned char		bBuzz;				/* 蜂鸣器 TRUE:鸣叫 FALSE:关闭 */	
	unsigned char		ucBuzzCtrl;			/* 蜂鸣器 ALMCTL_MINOR:一般表现/ ALMCTL_CRITICAL:紧急表现/ALMCTL_OFF:关闭 */	
	
	unsigned char		bRunLed;			/* 运行灯 TRUE:点亮 FALSE:关闭 */
	unsigned char		ucAlarmLed;			/* 机架故障灯 监控箱故障灯 ALMCTL_MINOR:一般表现/ ALMCTL_CRITICAL:紧急表现/ALMCTL_OFF:关闭 */	
	unsigned char		aucRelay[OUT_RELAY_MAX_NUM];	// 输出干节点控制字
	unsigned char		bManiCtrlRelay;		// 是否手动控制输出干节点
	unsigned int		wRelayOut;			// 输出多路干结点
	
}T_CtrlOutStruct; 

int get_acem_fact_info(char * pDest);
void GetAlarmNoMsk( unsigned char ucPart, char *pDest );
void GetDigitalData( unsigned char ucPart, char * pDest );
void GetAnalogData( unsigned char ucPart, char * pDest );
unsigned char GetSysPara( unsigned char ucParaType, char * pDest );
unsigned char SaveCurrentPara( void );
void key_buffer_init(void);
unsigned char DelKey( void );
void AddKey( unsigned char ucKeyID );
char GetPara( T_SysPara * ptPara, MIBTable_ParaInstance * ptInstance);
int GetPeakData(char* pDest);
short GetDisHisEvent(unsigned short index , T_EventRecord* dest);
unsigned short GetHisEventNum( void );
short GetDisHisAlarmAndNode( unsigned short index, T_HisAlarmStruct* dest, MIB_AlarmDataNode* alarm_node);
unsigned char CalRealAlarmTotal(void);
T_RealAlarmStruct * GetDisRealAlarm(unsigned char ucItem);
void    GetCtrlOut( T_CtrlOutStruct * tCtrlOut );
void    SetCtrlOut( T_CtrlOutStruct * tCtrlOut );
unsigned char clear_ctrl_out(void);
unsigned char get_precision(unsigned char raw_type, unsigned short para_id_offset_base);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _GUI_DATA_INTERFACE_H

