#ifndef SOFTWARE_SRC_APP_SNMPDATA_H_  
#define SOFTWARE_SRC_APP_SNMPDATA_H_

#include <rtdevice.h>
#include "SNMPData_in.h"

#ifdef __cplusplus
extern "C" {
#endif

/* HardCode */
#define SNMP_ID_REALDATA_BATTEXISTSTATUS 0x31
#if (SNMP_REAL_DATA_NUM >= SNMP_ID_REALDATA_BATTEXISTSTATUS)
#  define HAS_SNMP_REALDATA_BATTEXISTSTATUS 1
#endif


#define BATT_STATUS_NOT_EXIST   0
#define BATT_STATUS_EXIST       1
#define BATT_STATUS_COMM_FAIL   2

#define NET_ANTITHEFT_NOT_BIND 0x82
#define SNMP_NETANTITHEFT_START 38
#define SNMP_NETANTITHEFT_HEARTBEAT 39
#define SNMP_NETANTITHEFT_END 40
#define SNMP_NETANTITHEFT_OLDKEY 41
#define SNMP_NETANTITHEFT_CHANGEKEY 42

typedef struct
{
    INT32 aiRealData[SNMP_REAL_DATA_NUM];
    INT32 aiRealAlarm[SNMP_REAL_ALARM_NUM];
    T_SNMPFactoryInfo tSNMPFactoryInfo;
}T_SnmpDataStruct;

typedef struct
{
    INT32 aiPara[SNMP_PARA_NUM];
    BYTE  aucParaStr[SNMP_PARA_STR_NUM*SNMP_OTHER_STRING_NUM];
}T_SnmpParaStruct;

void Process_SNMP(void* parameter);

BOOLEAN InitSNMPData(BYTE ucAddr);
BOOLEAN IsEthLink(void);
BOOLEAN SnmpGetMacAddr(BYTE macAddr[]);
WORD CollectRealDataForSNMP(BYTE *p, WORD wLenMax);
UINT32 CollectRealAlarmForSNMP(BYTE *p, WORD wLenMax);
UINT32 CollectSyncParaForSNMP(BYTE *p);
UINT32 CollectSyncParaStrForSNMP(BYTE *p);
UINT32 CollectFactInfoForSNMP(BYTE *p);

BOOLEAN SetSNMPRealData(BYTE *p, BYTE ucSrcAddr);
BOOLEAN SetSNMPRealAlarm(BYTE *p, BYTE ucSrcAddr);
BOOLEAN SetSNMPSyncPara(BYTE *p);
BOOLEAN SetSNMPSyncParaStr(BYTE *p);
BOOLEAN SetSNMPFactInfo(BYTE *p, BYTE ucSrcAddr);

BOOLEAN SnmpSetPara(BYTE ucoffset, INT32 uiData, BOOLEAN bSetFlag);
BOOLEAN SnmpSetParaStr(BYTE ucOffset, BYTE* pData, BOOLEAN bSetFlag);

BOOLEAN SnmpSetParaFromBatt(BYTE *p);
BOOLEAN SnmpSetParaStrFromBatt(BYTE *p);


BOOLEAN BattStatusCommFail(BYTE ucAddr);
BOOLEAN SnmpCommFailCheck(BYTE ucAddr);
BOOLEAN BattExistCheckSave(void);
BOOLEAN InitExistBattInfo(void);


void* getData(BYTE ucAddr);
void* getAlarm(BYTE ucAddr);
void* getFact(BYTE ucAddr);
void* getPara(BYTE ucAddr);
void* getParaStr(BYTE ucAddr);
#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif