#include "led.h"
#include "data_type.h"
#include <rtthread.h>
#include <rtdevice.h>
#include <board.h>
#include "signal_led.h"
#include "msg_id.h"
#include "msg.h"
#include <stdlib.h>
#include <string.h>
#include "utils_thread.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "alarm_manage.h"
#include "utils_heart_beat.h"
#include "thread_id.h" 
#include "pin_define.h"


/* 定义信号灯对象句柄 */
static led_t* g_run_led = NULL;
static led_t* g_alm_led = NULL;
static led_t* g_grid_led = NULL;

static char s_alarm_flag = FALSE;
static unsigned short s_dev_status1 = 0;
static unsigned short s_dev_status2 = 0;
static unsigned short s_dev_status3 = 0;

/* 设置信号灯一个周期内的闪烁模式 
 * 格式为 “亮、灭、亮、灭、亮、灭 …………”
 */
static char* s_led_blink_mode = "500,500,";  ///< 1Hz闪烁
static char* s_led_off_mode = "0, 1000,";         // 常灭
static char* s_led_on_mode = "1000, 0,";          // 常亮

_rt_server_t g_led_server = NULL;

static msg_map led_msg_map[] =
{
    {LED_CTRL_MSG, msg_handle_nothing},
    {RECOVER_LED_STATUS_MSG, msg_handle_nothing},
};

/* 定义运行指示灯开函数 */
void run_led_switch_on(void *param) {
    rt_pin_write(LED_RUN_PIN, PIN_LOW);
}

/* 定义运行指示灯关函数 */
void run_led_switch_off(void *param) {
    rt_pin_write(LED_RUN_PIN, PIN_HIGH);
}

/* 定义告警指示灯开函数 */
void alm_led_switch_on(void *param) {
    rt_pin_write(LED_ALM_PIN, PIN_LOW);
}

/* 定义告警指示灯关函数 */
void alm_led_switch_off(void *param) {
    rt_pin_write(LED_ALM_PIN, PIN_HIGH);
}

/* 定义并网指示灯开函数 */
void grid_led_switch_on(void *param) {
    rt_pin_write(LED_GRID_PIN, PIN_LOW);
}

/* 定义并网指示灯关函数 */
void grid_led_switch_off(void *param) {
    rt_pin_write(LED_GRID_PIN, PIN_HIGH);
}

/* 初始化闪烁led */
void* init_led(void *param) {
    //运行灯初始化--灯灭
    rt_pin_mode(LED_RUN_PIN, PIN_MODE_OUTPUT_OD);
    g_run_led = led_create(run_led_switch_on, run_led_switch_off, NULL);
    RETURN_VAL_IF_FAIL(g_run_led != NULL, NULL);
    led_set_mode(g_run_led, LOOP_PERMANENT, s_led_off_mode);
    led_start(g_run_led);

    //告警灯初始化--灯灭
    rt_pin_mode(LED_ALM_PIN, PIN_MODE_OUTPUT_OD);
    g_alm_led = led_create(alm_led_switch_on, alm_led_switch_off, NULL);
    RETURN_VAL_IF_FAIL(g_alm_led != NULL, NULL);
    led_set_mode(g_alm_led, LOOP_PERMANENT, s_led_off_mode);
    led_start(g_alm_led);

    //并网灯初始化--灯灭
    rt_pin_mode(LED_GRID_PIN, PIN_MODE_OUTPUT_OD);
    g_grid_led = led_create(grid_led_switch_on, grid_led_switch_off, NULL);
    RETURN_VAL_IF_FAIL(g_grid_led != NULL, NULL);
    led_set_mode(g_grid_led, LOOP_PERMANENT, s_led_off_mode);
    led_start(g_grid_led);

    server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(led_msg_map) / sizeof(msg_map);
    register_server_msg_map(led_msg_map, server_info);
    return server_info;
}



// 辅助函数：根据设备状态设置LED控制消息
void set_grid_led_ctrl_msg(unsigned short dev_status, led_ctrl_msg_t *ctrl_msg) {
    // Starting || On-Grid  ||  Grid_Connect    ||   Grid_scheduling
    if(dev_status == 0x0100 || (dev_status >= 0x0200 && dev_status <= 0x0207) || (dev_status >= 0x0401 && dev_status <= 0x0405))  //满足并网，常亮
    {
        ctrl_msg->led_no = LED_GRID_STATE;
        ctrl_msg->ctrl_state = LED_ON; 
    }  // shutdown || standby
    else if((dev_status >= 0x0300 && dev_status <= 0x0309) || (dev_status >= 0x0001 && dev_status <= 0x0003))    //不满足并网，闪
    {
        ctrl_msg->led_no = LED_GRID_STATE;
        ctrl_msg->ctrl_state = LED_BLINK;
    }
    else                                                                                                       // 灭
    {
        ctrl_msg->led_no = LED_GRID_STATE;
        ctrl_msg->ctrl_state = LED_OFF;
    }

    ctrl_msg->is_updating = NO_UPDATE_STATUS;
}

void grid_led_deal() {
    unsigned short dev_status = 0;
    led_ctrl_msg_t ctrl_msg = {};

    get_one_data(DAC_DATA_ID_DEV_STATUS, &dev_status);
    if(s_dev_status1 == dev_status) {
        return;
    }
    s_dev_status1 = dev_status;

    set_grid_led_ctrl_msg(dev_status, &ctrl_msg);

    rt_kprintf("grid led |dev_status:0x%x, ctrl_state:%d\n", dev_status, ctrl_msg.ctrl_state);
    send_msg_to_thread(LED_CTRL_MSG, MOD_LED, &ctrl_msg, sizeof(led_ctrl_msg_t));
}




// 辅助函数：根据设备状态设置LED控制消息
void set_run_led_ctrl_msg(unsigned short dev_status, led_ctrl_msg_t *ctrl_msg) {
    // On-Grid  ||  Grid_Connect    ||   Grid_scheduling
    if((dev_status >= 0x0200 && dev_status <= 0x0207) || (dev_status >= 0x0401 && dev_status <= 0x0405))  //运行，常亮
    {
        ctrl_msg->led_no = LED_RUN_STATE;
        ctrl_msg->ctrl_state = LED_ON;        
    } // standby, starting
    else if(dev_status == 0x0100 || (dev_status >= 0x0001 && dev_status <= 0x0003))                   //启动中，闪烁
    {
        ctrl_msg->led_no = LED_RUN_STATE;
        ctrl_msg->ctrl_state = LED_BLINK; 
    }
    else if(dev_status >= 0x0300 && dev_status <= 0x0309)                                          //停机，灭
    {
        ctrl_msg->led_no = LED_RUN_STATE;
        ctrl_msg->ctrl_state = LED_OFF;
    }
    else
    {
        ctrl_msg->led_no = LED_RUN_STATE;
        ctrl_msg->ctrl_state = LED_OFF;
    }

    ctrl_msg->is_updating = NO_UPDATE_STATUS;
}

void run_led_deal() {
    led_ctrl_msg_t ctrl_msg = {};
    unsigned short dev_status = 0;

    get_one_data(DAC_DATA_ID_DEV_STATUS, &dev_status);
    if(s_dev_status2 == dev_status) {
        return;
    }
    s_dev_status2 = dev_status;

    set_run_led_ctrl_msg(dev_status, &ctrl_msg);

    rt_kprintf("run led |dev_status:0x%x, ctrl_state:%d\n", dev_status, ctrl_msg.ctrl_state);
    send_msg_to_thread(LED_CTRL_MSG, MOD_LED, &ctrl_msg, sizeof(led_ctrl_msg_t));
}





// 辅助函数：判断是否需要更新LED状态  
int should_update_led(unsigned short dev_status, char alarm_flag) {  
    return (s_dev_status3 != dev_status || s_alarm_flag != alarm_flag);  
}  
  
// 辅助函数：根据设备状态和告警标志设置LED控制消息  
void set_alarm_led_ctrl_msg(unsigned short dev_status, char alarm_flag, led_ctrl_msg_t *ctrl_msg) {  
    if (alarm_flag) {  
        if (dev_status >= 0x0300 && dev_status <= 0x0309) { // 故障停机，常亮  
            ctrl_msg->ctrl_state = LED_ON;  
        } else { // 非故障停机，闪烁  
            ctrl_msg->ctrl_state = LED_BLINK;  
        }  
    } else { // 无告警，灭  
        ctrl_msg->ctrl_state = LED_OFF;  
    }  
    ctrl_msg->led_no = LED_ALM_STATE;  
    ctrl_msg->is_updating = NO_UPDATE_STATUS;  
}  
  
void alarm_led_deal() {  
    unsigned short dev_status = 0;  
    led_ctrl_msg_t ctrl_msg = {0};
    int count = get_realtime_alarm_count();  
    char alarm_flag = (count > 0); // 使用int代替bool  
  
    get_one_data(DAC_DATA_ID_DEV_STATUS, &dev_status);  
  
    if (!should_update_led(dev_status, alarm_flag)) {  
        return;  
    }  
  
    s_dev_status3 = dev_status;  
    s_alarm_flag = alarm_flag; // 注意这里将s_alarm_flag的类型改为int  
  
    set_alarm_led_ctrl_msg(dev_status, alarm_flag, &ctrl_msg);  
  
    rt_kprintf("alarm led |dev_status:0x%x, alarm_flag:%d, ctrl_state:%d\n", dev_status, alarm_flag, ctrl_msg.ctrl_state);  
    send_msg_to_thread(LED_CTRL_MSG, MOD_LED, &ctrl_msg, sizeof(led_ctrl_msg_t));  
}



void ctrl_led_when_update(unsigned char state)
{
    led_ctrl_msg_t ctrl_msg = {};
    int loop = 0;
    unsigned char led_no[] = {LED_RUN_STATE, LED_ALM_STATE, LED_GRID_STATE};
    int num = sizeof(led_no) / sizeof(unsigned char);
    for(loop = 0; loop < num; loop ++)
    {
        ctrl_msg.led_no = led_no[loop];
        if(state == LED_OFF)
        {
            ctrl_msg.is_updating = UPDATE_FINISH_STATUS;
        }
        else
        {
            ctrl_msg.is_updating = UPDATING_STATUS;
        }
        ctrl_msg.ctrl_state = state;
        send_msg_to_thread(LED_CTRL_MSG, MOD_LED, &ctrl_msg, sizeof(led_ctrl_msg_t));
    }
}

static void run_led_ctrl(unsigned char ctrl_state)
{
    if (LED_ON == ctrl_state) 
    {
        led_set_mode(g_run_led, LOOP_PERMANENT, s_led_on_mode);
        led_start(g_run_led);
    } 
    else if (LED_BLINK == ctrl_state) 
    {
        led_set_mode(g_run_led, LOOP_PERMANENT, s_led_blink_mode);
        led_start(g_run_led);
    }
    else if(LED_OFF == ctrl_state) 
    {
        led_stop(g_run_led);
    }
}

static void alm_led_ctrl(unsigned char ctrl_state)
{
    if (LED_BLINK == ctrl_state) 
    {
        led_set_mode(g_alm_led, LOOP_PERMANENT, s_led_blink_mode);
        led_start(g_alm_led);
    }
    else if(LED_ON == ctrl_state) 
    {
        led_set_mode(g_alm_led, LOOP_PERMANENT, s_led_on_mode);
        led_start(g_alm_led);
    }
    else if(LED_OFF == ctrl_state)
    {
        led_stop(g_alm_led);
    }
}

static void grid_led_ctrl(unsigned char ctrl_state)
{
    if(LED_BLINK == ctrl_state)
    {
        led_set_mode(g_grid_led, LOOP_PERMANENT, s_led_blink_mode);
        led_start(g_grid_led);
    }
    else if(LED_ON == ctrl_state)
    {
        led_set_mode(g_grid_led, LOOP_PERMANENT, s_led_on_mode);
        led_start(g_grid_led);
    }
    else if(LED_OFF == ctrl_state)
    {
        led_stop(g_grid_led);
    }
}

static void ctrl_led_by_msg(led_ctrl_msg_t* ctrl_msg)
{
    switch (ctrl_msg->led_no)
    {
        case LED_RUN_STATE:
        {
            run_led_ctrl(ctrl_msg->ctrl_state);
            break;
        }
        case LED_ALM_STATE:
        {
            alm_led_ctrl(ctrl_msg->ctrl_state);
            break;
        }
        case LED_GRID_STATE:
        {
            grid_led_ctrl(ctrl_msg->ctrl_state);
            break;
        }
        default:
            break;
    }
}

void deal_led_ctrl_msg(led_ctrl_msg_t* ctrl_msg)
{
    static unsigned char s_is_updating[3] = {NO_UPDATE_STATUS, NO_UPDATE_STATUS, NO_UPDATE_STATUS};
    if(s_is_updating[ctrl_msg->led_no] == NO_UPDATE_STATUS && ctrl_msg->is_updating == NO_UPDATE_STATUS)    // 检查非升级状态
    {
        ctrl_led_by_msg(ctrl_msg);
        rt_kprintf("led|no_update_status, led_no:%d, state:%d\n", ctrl_msg->led_no, ctrl_msg->ctrl_state);
    }
    else if(s_is_updating[ctrl_msg->led_no] != UPDATING_STATUS && ctrl_msg->is_updating == UPDATING_STATUS) // 检查升级状态
    {
        s_is_updating[ctrl_msg->led_no] = UPDATING_STATUS;
        ctrl_led_by_msg(ctrl_msg);
        rt_kprintf("led|update_status, led_no:%d, state:%d\n", ctrl_msg->led_no, ctrl_msg->ctrl_state);
    }
    else if(ctrl_msg->is_updating == UPDATE_FINISH_STATUS)                                // 结束升级状态
    {
        s_is_updating[ctrl_msg->led_no] = NO_UPDATE_STATUS;
        ctrl_led_by_msg(ctrl_msg);
        rt_kprintf("led|update_finish_status, led_no:%d, state:%d\n", ctrl_msg->led_no, ctrl_msg->ctrl_state);
    }
}

void deal_led_recover_msg()
{
    s_alarm_flag = FALSE;
    s_dev_status1 = 0;
    s_dev_status2 = 0;
    s_dev_status3 = 0;
}

void recv_msg_deal()
{
    
    if ((g_led_server == NULL) || (rt_sem_take(&g_led_server->msg_sem, 0) != RT_EOK) || (g_led_server->msg_node == RT_NULL))
    {
        return;
    }
    
    _rt_msg_t curr_msg = g_led_server->msg_node;

    switch (curr_msg->msg.msg_id)
    {
        case LED_CTRL_MSG:
            deal_led_ctrl_msg((led_ctrl_msg_t*)curr_msg->msg.data);
            break;
        case RECOVER_LED_STATUS_MSG:
            deal_led_recover_msg();
            break;
        default:
            break;
    }

    rt_mutex_take(&g_led_server->mutex, RT_WAITING_FOREVER);
    g_led_server->msg_node = curr_msg->next;
    g_led_server->msg_count--;
    rt_mutex_release(&g_led_server->mutex);

    softbus_free(curr_msg);
}

void led_thread_entry(void* parameter) {
    PRINT_MSG_AND_RETURN_IF_FAIL(parameter != NULL);
    g_led_server = _curr_server_get();
    pre_thread_beat_f(THREAD_LED);

    while (is_running(TRUE)){
        thread_beat_go_on(THREAD_LED);
        recv_msg_deal();
        led_ticks();
        rt_thread_mdelay(LED_TICK_TIME);    // 50ms
    }
}
