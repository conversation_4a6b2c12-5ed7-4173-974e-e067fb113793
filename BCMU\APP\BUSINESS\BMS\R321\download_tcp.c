#include "lwip/ip_addr.h"
#include "lwip/timeouts.h"
#include "download_tcp.h"
#include "common.h"
#include "update_handle.h"
#include "para.h"
#include "SingleButton.h"
#include "hisdata.h"
#include "utils_rtthread_security_func.h"
#include "NetProtocol1363.h"
#include "CommCan.h"
#include "commBdu.h"
#include "prtclDL.h"

#define TCP_SERVER_PORT 24521

extern T_CommRecCache s_tUpdateCommRecCache;     // 链路层数据缓冲区
extern T_PRTCL_REC_BUFF s_tUpdateCommSendCache;  // 链路层数据发送缓冲区
extern rt_mutex_t s_mSendQueMutex;
extern rt_mutex_t s_mRecvQueMutex;
extern uint8_t eth_link_state;

Static BOOLEAN s_bClientServerFlag = False;
static BOOLEAN s_TimeoutQuitUPG = FALSE;
Static BOOLEAN s_bNetRemoteNorthCommDisconnect = True;     // 网口通信断状态(只针对远程下载协议)
Static UINT32 s_ulNetRemoteNorthDisconnCnt = 0; //远程下载网口北向通讯通讯断计数器
Static WORD s_wTimeOutCounter = 0; // 超时计数线程计数器
Static WORD s_wRemoteTimeCounter = 0;  // 远程启动网口升级超时计数器
Static struct tcp_pcb *s_tServeLinkPcb = NULL;
Static struct tcp_pcb *s_tClientLinkPcb = NULL;

Static BOOLEAN s_bJudgeKeyOrRemoteStart = False;   //判断时按键启动网口模式，还是远程启动网口模式，False:按键启动网口模式，True:远程启动网口模式
Static BOOLEAN s_bDeleteTimeOutCounterThreadFlag = False; // 删除超时计数线程标志位

Static void CloseNetReturnToNormal(void);
Static BOOLEAN JudgeReturnRemoteUpdateMode(void); //判断是否退出远程升级模式
Static BOOLEAN NetStatusChange(void); //检测插拔网线后，网口状态处理函数
Static BOOLEAN FileTransCompleteHandle(void);
Static BOOLEAN PeakFileTransCompleteHandle(void); //错峰文件传输完成后的处理

//static err_t download_connect(void *arg, download_tcp_pcb *tpcb, err_t err);
err_t download_callback(void *arg, download_tcp_pcb *pcb, struct pbuf *tcp_recv_pbuf, err_t err);
Static void download_connected_err(void *arg, err_t err);
static void download_process(void);
//static void ClientInitProcessFunction(void); //R311做客户端时的初始化处理函数 by hxk 2023-01-30
//static err_t download_callback2(void *arg, download_tcp_pcb *pcb, struct pbuf *tcp_recv_pbuf, err_t err);
// static void LinkCallFunc(void); //重复触发定时器回调函数 by hxk 2023-02-06
//static void DelayTrigUpdate(void); //延时触发升级 by hxk 2023-02-07
//static void DelayTrigCallFunc(void *parameter); //延时触发升级回调函数 by hxk 2023-02-07
//static BOOLEAN GetReomteIpAndPort(char *serverIP,WORD *RemotePort); //获取远程ip地址和端口号

Static BOOLEAN SetNetRemoteCommDisconnect(BOOLEAN flag);

// 远程网口下载通信断判断
BOOLEAN JudgeNetRemoteCommDisconnect(void)
{
    return s_bNetRemoteNorthCommDisconnect;
}


Static BOOLEAN SetNetRemoteCommDisconnect(BOOLEAN flag)
{
    return s_bNetRemoteNorthCommDisconnect = flag;
}


BOOLEAN NetRemoteNorthTimeCount(void)
{
    TimerPlus(s_ulNetRemoteNorthDisconnCnt, TIME_NETREMOTENORTH_MAX);
    if(TimeOut(s_ulNetRemoteNorthDisconnCnt, TIME_NETREMOTENORTH_MAX))
    {
        SetNetRemoteCommDisconnect(True); // 超时通信断
        s_ulNetRemoteNorthDisconnCnt = 0;
        return True; // 通信断
    }
    return False; // 通信正常
}

BOOLEAN getTimeoutQuitFlag(void) {
    return s_TimeoutQuitUPG;
}

BOOLEAN setTimeoutQuitFlag(BOOLEAN flag_timeout) {
    s_TimeoutQuitUPG = flag_timeout;
    return 0;
}

void SetTimeOutCounter(void)
{
    TimerPlus(s_wTimeOutCounter, TIMEOUT_WAITTING_MAX_TIME);
}

void ClearTimeOutCounter(void)
{
    s_wTimeOutCounter = 0;
}

WORD GetTimeOutCounter(void)
{
    return s_wTimeOutCounter;
}

/***************************************************************************
 * @brief    远程控制网口升级超时判断
 * @return   True--超时 False--未超时
 **************************************************************************/
Static BOOLEAN JudgeReturnRemoteUpdateMode(void)
{
    TimerPlus(s_wRemoteTimeCounter, TCP_REMOTE_START_NET_UPDATE_MAX_TIME);
    if (TimeOut(s_wRemoteTimeCounter, TCP_REMOTE_START_NET_UPDATE_MAX_TIME))
    {
#ifndef KW_CHECK
        // 超时则主动关闭tcp连接
        if (s_tServeLinkPcb != NULL)
        {
            tcp_close((download_tcp_pcb *)s_tServeLinkPcb);
            s_tServeLinkPcb = NULL;
        }

        if (s_tClientLinkPcb != NULL)
        {
            tcp_close((download_tcp_pcb *)s_tClientLinkPcb);
            s_tClientLinkPcb = NULL;
        }
#endif
        SetNetRemoteCommDisconnect(True);
        return True;
    }

    return False;
}

//检测插拔网线后，网口状态处理函数
Static BOOLEAN NetStatusChange(void)
{
    //NetStatusDownToUp标志位用来检测是否需要重新配置网口1363,当网线拔掉时，此标志位置位，等待网线重新插上，则重新配置网口1363
    static BOOLEAN s_bNetStatusDownToUp = False;

    //eth_link_state为一个全局变量，网线拔掉为0，网线插上为1
    if(eth_link_state == 0)
    {
       s_bNetStatusDownToUp = True;
    }

    if(s_bNetStatusDownToUp && eth_link_state)
    {
        s_bNetStatusDownToUp = False;
        close1363socket(False,True);
        //CloseClientLink();
    }

    return s_bNetStatusDownToUp;
}

//关闭网络连接，回归到正常的状态
Static void CloseNetReturnToNormal(void)
{
    #ifndef KW_CHECK
    //断开tcp连接
    if(s_tServeLinkPcb != NULL)
    {
        tcp_close((download_tcp_pcb *)s_tServeLinkPcb);
        s_tServeLinkPcb = NULL;
    }
    #endif
    SetNetRemoteCommDisconnect(True);
    //退出近端网口模式
    ReturnToNormalMode(0);
    SaveAction(GetActionId(CONTOL_KEY_UPGRADE_TIMEOUT_TO_NORMAL), "Net UpdateTimeout");

    return;
}

/* 接收到数据后的数据处理回调函数 */
err_t download_callback(void *arg, download_tcp_pcb *pcb, struct pbuf *tcp_recv_pbuf, err_t err)
{
  //struct pbuf *tcp_send_pbuf;
  //char echoString[]="This is the server content echo:\r\n";
  int write_offset = 0;// RepeatSendresult = 0;

  if (tcp_recv_pbuf != NULL)
  {
    //只要接收到网口传输过来的数据则重启网口数据传输超时定时器,重新计时。
#ifndef KW_CHECK
    ClearTimeOutCounter();
    s_wRemoteTimeCounter = 0;
#endif
    SetNetRemoteCommDisconnect(False);
	DealDeviceUnlock(AUTO_UNLOCK);
    //tcp_recv_pbuf中为收到的服务端的数据
    #ifndef KW_CHECK
    tcp_recved(pcb, tcp_recv_pbuf->tot_len);
    #endif
    // TODO：处理这部分的数据（解析以及存储对应的信息） and 回包

    // 接收
    #ifndef KW_CHECK
    rt_mutex_take(s_mRecvQueMutex, RT_WAITING_FOREVER);
    write_offset = s_tUpdateCommRecCache.ulDataLength;
    if(write_offset + tcp_recv_pbuf->tot_len < DOWNLOAD_RECBF_SIZE)
    {
        rt_memcpy_s(s_tUpdateCommRecCache.aucDataBuff + write_offset,sizeof(s_tUpdateCommRecCache.aucDataBuff) - write_offset, tcp_recv_pbuf->payload, tcp_recv_pbuf->tot_len); // 收到的一帧数据
        s_tUpdateCommRecCache.ulDataLength += tcp_recv_pbuf->tot_len; // 收到数据长度
    }
    else
    {
		rt_kprintf("full\r\n");
        rt_memcpy_s(s_tUpdateCommRecCache.aucDataBuff, sizeof(s_tUpdateCommRecCache.aucDataBuff), 0x00, RAW_BUFF_LEN); // 收到的一帧数据
        s_tUpdateCommRecCache.ulDataLength = 0; // 收到数据长度
        RepeatSendlastFrame();
    }
    
    rt_mutex_release(s_mRecvQueMutex);

    rt_kprintf("receive: count = %d, write pos = %d\r\n", tcp_recv_pbuf->tot_len, write_offset);
    // for (i = 0; i < tcp_recv_pbuf->tot_len; ++i) {
    //     rt_kprintf("%x ", *((BYTE*)tcp_recv_pbuf->payload + i));
    // }
    // rt_kprintf("\r\n");
    #endif
    if (SUCCESSFUL == parseCmd()) { // 解析
        exeCmd();  // 解析成功则 执行
    }
    // 回包
    getDataFromQueue();
    #ifndef KW_CHECK
    rt_mutex_take(s_mSendQueMutex, RT_WAITING_FOREVER);
    tcp_write(pcb, s_tUpdateCommSendCache.aucDataBuff, s_tUpdateCommSendCache.wDataLength, 0x01);
    rt_kprintf("send: count = %d\r\n", s_tUpdateCommSendCache.wDataLength);
    // for (i = 0; i < s_tUpdateCommSendCache.wDataLength; ++i) {
    //     rt_kprintf("%x ", s_tUpdateCommSendCache.aucDataBuff[i]);
    // }
    // rt_kprintf("\r\n");
    
    rt_memset(&s_tUpdateCommSendCache, 0x00, sizeof(s_tUpdateCommSendCache));
    rt_mutex_release(s_mSendQueMutex);
    pbuf_free(tcp_recv_pbuf);

    FileTransCompleteHandle();
    PeakFileTransCompleteHandle();

    #endif
  }
  else if (err == ERR_OK)
  {
    #ifndef KW_CHECK
    tcp_close(pcb);
    s_tClientLinkPcb = NULL;
    #endif

    return ERR_OK;
  }

  return ERR_OK;
}

// 升级主机接受网管传输文件完毕后的操作 返回TRUE：传输完成且对应操作成功 FALSE：未传输完成

Static BOOLEAN FileTransCompleteHandle(void)
{
    if(GetFileTransFlag())
    {
        SetFileTransFlag(FALSE);
        // 网管升级时会将s_bClientServerFlag置为TRUE，因此网管升级主机默认并发升级从机；上位机上如果选择升从机时会发扩展触发帧，也进行主机并发升级从机的动作。
        if(s_bClientServerFlag || GetExtendTrigFrameFlag() || GetSwUpgradeFlag())
        {
            
            if(GetUpgradeFileFlag() == UPDATE_BMS_FILE || GetUpgradeFileFlag() == UPDATE_BDU_FILE)
            {
                SetParallUpdateFlag(True); // 启动从机检测前,停止升级主机的业务
                BduCtrl(SCI_CTRL_STOP_CAN_COMM, True);  // 停止BDU CAN通信
                StartBMSUpdateScan(); // 启动从机检测
                return TRUE;
            }
            else
            {
                return False;
            }
            
        }
        // else // 否则为近端按键升级
        // {
        //     DelayTrigUpdate();
        //     return TRUE;
        // }
    }
    return FALSE;
}


//错峰文件传输完成后的处理

Static BOOLEAN PeakFileTransCompleteHandle(void)
{
    BOOLEAN ret = False;

    if(GetPeakFileTransFlag())
    {
        SetPeakFileTransFlag(False);
        // 传输完成后的处理
        ret = PeakDataCheckAndSave();
    }
    
    return ret;
}


Static void download_connected_err(void *arg, err_t err)
{
    /* 重新启动连接 */
    download_process();
}

//服务器连接成功后将要调用的函数
err_t download_accept(void *arg, download_tcp_pcb *newpcb, err_t err)
{
    err_t ret_err = ERR_OK;
    // char echoString[]="This is the server content echo:\r\n";
    #ifndef KW_CHECK
    ClearTimeOutCounter();
    s_wRemoteTimeCounter = 0;
    if (s_tClientLinkPcb == NULL)
    {
        s_tClientLinkPcb = newpcb; // s_tClientLinkPcb理解为与远程主机相连接的pcb连接
    }
    /* 与网管联调屏蔽，不需要回复 */
    // ret_err = tcp_write(newpcb,echoString,rt_strnlen_s(echoString,sizeof(echoString)),1);  //回应信息
    tcp_recv(newpcb, download_callback);    //指定连接接收到新的数据之后将要调用的回调函数
    tcp_err(newpcb, download_connected_err);   //指定连接出错将要调用的函数
    #endif
    return ret_err;
}

static void download_process(void)
{
    #ifndef KW_CHECK
    struct tcp_pcb *download_pcb;
    err_t err;

    SaveKeyUpgradeAction();
    download_pcb = tcp_new();

    if(download_pcb!=NULL)
    {
        err = tcp_bind(download_pcb,IP_ADDR_ANY,DOWNLOAD_PORT);//绑定本地所有IP地址和端口号 作为服务器不需要知道客户端的IP
        if(err==ERR_OK)//成功绑定
        {
            ClearTimeOutCounter();
            s_wRemoteTimeCounter = 0;
            download_pcb = tcp_listen(download_pcb);    //开始监听端口
            s_tServeLinkPcb = download_pcb; // s_tServeLinkPcb理解为处于监听状态的pcb连接

            tcp_accept(download_pcb,download_accept); //指定监听状态的连接联通之后将要调用的回调函数
        }
    }
    else
    {
        tcp_abort(download_pcb);
    }
    #endif
}

void processDownload(void* parameter)
{
    if(RT_NULL == s_mSendQueMutex)
    {
        s_mSendQueMutex = rt_mutex_create("download_tcp_send_lock", RT_IPC_FLAG_PRIO);
    }

    if(RT_NULL == s_mRecvQueMutex)
    {
        s_mRecvQueMutex = rt_mutex_create("download_tcp_rcv_lock", RT_IPC_FLAG_PRIO);
    }
    download_process();
}

/***************************************************************************
 * @brief    超时计数线程入口函数
 **************************************************************************/
void TimeOutCounterThread(void *parameter)
{
    BOOLEAN bRet = False;
#ifndef UNITEST
    ClearTimeOutCounter();
    while(1)
#endif
    {
#ifndef KW_CHECK
        rt_thread_delay(1000);
#endif
        NetStatusChange(); //插拔网线后，对网口1104的处理
        if (s_bJudgeKeyOrRemoteStart)
        {
            // 远程控制进入网口模式
            bRet = JudgeReturnRemoteUpdateMode();
            if (bRet)
            {
                // 超时清除标志位
                s_bJudgeKeyOrRemoteStart = False;
                s_bClientServerFlag = False;
                #ifndef UNITEST
                break;
                #endif
            }
        }
        else
        {
            // 近端按键进入网口模式
            SetTimeOutCounter();
            if (GetTimeOutCounter() >= TIMEOUT_WAITTING_MAX_TIME)
            {
                // 退出网口模式
                CloseNetReturnToNormal();
                #ifndef UNITEST
                break;
                #endif
            }
        }
    }

    s_bDeleteTimeOutCounterThreadFlag = True;

    return;
}

//启动客户端链接 by hxk
void StartNetUpdateDL(void)
{
    rt_thread_t tThreadTimeout;
    s_bClientServerFlag = True;

    // 通过snmp启动升级链路
    processDownload(NULL);
    if (s_bJudgeKeyOrRemoteStart == False)
    {
        s_bJudgeKeyOrRemoteStart = True;
#ifndef KW_CHECK
        tThreadTimeout = rt_thread_create("TimeOut", TimeOutCounterThread, RT_NULL, 4096, TCP_THREAD_PRIORITY_UPDATE,
                                          TCP_THREAD_TIMESLICE_UPDATE);

        if (tThreadTimeout != RT_NULL)
        {
            rt_thread_startup(tThreadTimeout);
        }
#endif
    }
}

/***************************************************************************
 * @brief    获取远程升级标志位
 * @return   True-远程升级 False-近端升级
 **************************************************************************/
BOOLEAN GetRemoteUpdateFlag(void)
{
    return s_bJudgeKeyOrRemoteStart;
}

//按键退出升级模式时，需要关闭升级链路，以及相关的定时器
void CloseUpdateLink(void)
{
#ifndef KW_CHECK
    rt_thread_t TimeOutthread;
    if(s_tServeLinkPcb != NULL)
    {
        tcp_close((download_tcp_pcb *)s_tServeLinkPcb);
        s_tServeLinkPcb = NULL;
    }
    TimeOutthread = rt_thread_find("TimeOut");
    if (TimeOutthread != RT_NULL)
    {
        rt_thread_delete(TimeOutthread);
        TimeOutthread = RT_NULL;
    }
    ReturnToNormalMode(0);
#endif
    return;
}

//删除超时计数线程
void DeleteTempThread(void)
{
    if (s_bDeleteTimeOutCounterThreadFlag)
    {
#ifndef KW_CHECK
        rt_thread_t TimeOutthread;
        TimeOutthread = rt_thread_find("TimeOut");
        if (TimeOutthread != RT_NULL)
        {
            rt_thread_delete(TimeOutthread);
            TimeOutthread = RT_NULL;
        }
#endif
        s_bDeleteTimeOutCounterThreadFlag = False;
    }

    return;
}

/*//延时触发升级，保证远程升级工具能够正常收到最后一帧的回包 by hxk 2023-02-07
static void DelayTrigUpdate(void)
{
  #ifndef KW_CHECK
    if (RT_NULL == ClientLinkManage.Client_Manage_timer)
    {
        ClientLinkManage.Client_Manage_timer = rt_timer_create("DelayTrigUpdate",\
												DelayTrigCallFunc,\
												NULL, RT_TICK_PER_SECOND*5, \
												RT_TIMER_FLAG_ONE_SHOT | RT_TIMER_FLAG_SOFT_TIMER);
												
    }
    RETURN_IF_FAIL(ClientLinkManage.Client_Manage_timer != RT_NULL);
    rt_timer_start(ClientLinkManage.Client_Manage_timer);
  #endif
}

//延时触发升级回调函数 by hxk 2023-02-07
static void DelayTrigCallFunc(void *parameter)
{
    (void)parameter;

	ResetMCU( NO_RESET_CSU_UPGRADE );
}*/

//获取单板是客户端升级还是服务器端升级,True是客户端升级，False是服务器端升级 by hxk 2023-02-07
BOOLEAN GetClientUpdateFlag(void)
{
    return s_bClientServerFlag;
}