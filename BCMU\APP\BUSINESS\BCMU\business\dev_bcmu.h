/**
 * @file     device_type.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2017-04-19
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */

#ifndef _DEVICE_BCMU_H
#define _DEVICE_BCMU_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "dev_bmu.h"


dev_type_t* init_dev_bcmu(void);
void set_bcmu_data(unsigned char dev_addr);
void set_bcmu_ana_data(BMU_real_data_ana_t *bmu_ana_data_tmp);
void set_bcmu_dig_data(BMU_real_data_dig_t *bmu_dig_data_tmp);
void set_bcmu_alm_data(BMU_real_data_alm_t *bmu_alm_data_tmp);
void set_bcmu_fac_data(BMU_data_fac_t *bmu_fac_data_tmp);
void set_bcmu_soc_data(BMU_para_data_t *BMU_para_data_tmp);
void set_bmu_serial_number(unsigned char *serial_number);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _DEVICE_BCMU_H
