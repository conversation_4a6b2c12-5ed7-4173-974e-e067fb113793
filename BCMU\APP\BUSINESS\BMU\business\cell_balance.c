#include "cell_balance.h"
#include <stdio.h>
#include <rtthread.h>
#include <rtdevice.h>
#include <board.h>
#include <string.h>
#include "pin_define.h"
#include "device_num.h"
#include "drv_MT9818.h"
#include "bq769x2.h"

#define CELL_BALANCE_TIMER_NAME "cell_balance"
#define CELL_BALANCE_TIMEOUT    240000    ///<  定时器时间，240S
#define CONTOR_CELL_BALANCE_STOP 0   ///<  停止均衡的命令
#define ODD_BIT 1

#define EQUAL_CIRCUIT_NORMAL  0
#define EQUAL_CIRCUIT_SHORT   1
#define EQUAL_CIRCUIT_OPEN    2

#define CHIP_1_VOL_CHANNEL 10
#define CHIP_2_VOL_CHANNEL 10

/***********************  变量定义  ************************/
Static rt_timer_t cell_balance_period_timer;
static rt_device_t BattChip_device1;
static rt_device_t BattChip_device2;
cell_balance_data_t cell_balance_data = {0};

Static unsigned char s_balance_fault_start = FALSE;
Static unsigned char s_balance_fault_result = EQUAL_CIRCUIT_NORMAL;
static unsigned char s_balance_open_fault[BATT_MOD_CELL_NUM] = {0};  //断路时故障情况

/*********************  静态函数原型定义  **********************/
static void init_cell_balance_timer();
static short balance_chip_find(void);
static void cell_balance_gap_start(unsigned int *cell_balance_control, char chip_no);
static void cell_balance_control_excute(rt_device_t BattChip_device, unsigned int cell_balance_control);
Static void cell_balance_period_excute();

static void equal_circuit_fault_judge(unsigned char cell_no);
static void judge_circuit_fault_status(unsigned char cell_no);
static void init_blance_circuit_status(void);
static unsigned char is_equal_circuit_fault_detected(void);
static unsigned int revert_control_status_order(unsigned int ctrl_status);

static void init_cell_balance_timer()
{
    // 启动定时器
    cell_balance_period_timer = rt_timer_create(CELL_BALANCE_TIMER_NAME, cell_balance_period_excute,
                            NULL, CELL_BALANCE_TIMEOUT,
                            RT_TIMER_FLAG_ONE_SHOT);
}

/**
 * @description: 外部接口，启动电芯均衡模块
 * @return {*}
 * @Date: 2023-04-07
 */
void cell_balance_process_start(void)
{
    cell_balance_data.passive_flag = TRUE;
    // 初始化定时器
    init_cell_balance_timer();

    // 识别均衡芯片
    balance_chip_find();

    // 与硬件相关，一块芯片对应10路通道
    cell_balance_data.ucCellVoltNum[0] = CHIP_1_VOL_CHANNEL;
    cell_balance_data.ucCellVoltNum[1] = CHIP_2_VOL_CHANNEL;
}

/**
 * @description: 外部接口，启动定时器的接口
 * @return {*}
 * @Date: 2023-04-07
 */
void cell_balance_timer_start()
{
    rt_timer_start(cell_balance_period_timer);
}

/**
 * @description: 外部接口，关闭定时器的接口
 * @return {*}
 * @Date: 2023-04-07
 */
void cell_balance_timer_stop()
{
    rt_timer_stop(cell_balance_period_timer);
}

/*
* @brief 将北向接收的控制状态反序
*/
static unsigned int revert_control_status_order(unsigned int ctrl_status) {
    short i = 0;
    short count = sizeof(ctrl_status) * 8;
    unsigned int new_ctrl = 0;
    unsigned int bit_val = 0;
    for (i = 0; i < count; i++) {
        // 从左到右取bit位的值
        bit_val = ctrl_status & (1 << (count - i - 1));
        // 该bit位的值为1则放到从右往左的对应位置
        if (bit_val > 0) {
            new_ctrl |= 1 << i;
        }
    }
    return new_ctrl;
}

/**
 * @description: 外部接口，对均衡进行控制
 * @param {int} cell_balance_control_status
 * @return {*}
 * @Date: 2023-04-07
 */
void cell_balance_control(unsigned int cell_balance_control_status)
{
    // 将均衡指令拆分
    unsigned int chip1_control = 0;
    unsigned int chip2_control = 0;
    unsigned int ctrl_status_order = 0;

    ctrl_status_order = revert_control_status_order(cell_balance_control_status);
    chip1_control = ctrl_status_order & 0x000003FF;          // 取低1~10号电芯bit
    chip2_control = (ctrl_status_order & 0x000FFC00) >> 10;  // 取低11~20号电芯bit

    // 均衡策略，相邻电芯不得同时开启均衡
    cell_balance_gap_start(&chip1_control, 1);
    cell_balance_gap_start(&chip2_control, 2);

    // 控制两个均衡芯片进行均衡
    cell_balance_control_excute(BattChip_device1, chip1_control);
    cell_balance_control_excute(BattChip_device2, chip2_control);

    // 切换奇偶位
    if (0 != chip1_control)
    {
        cell_balance_data.bOddBit[0] ^= 0x01;
    }

    if (0 != chip2_control)
    {
        cell_balance_data.bOddBit[1] ^= 0x01;
    }
}

/**
 * @description: 定时器到时间以后执行的函数
 * @return {*}
 * @Date: 2023-04-07
 */
Static void cell_balance_period_excute()
{
    // 执行关闭均衡指令
    cell_balance_control(CONTOR_CELL_BALANCE_STOP);
}

/**
 * @description: 均衡安全策略，确保相邻电芯不能同时启动
 * @param {short} *cell_balance_control
 * @param {char} chip_no
 * @return {*}
 * @Date: 2023-04-07
 */
static void cell_balance_gap_start(unsigned int *cell_balance_control,char chip_no)
{
    if(!cell_balance_data.passive_flag)   //均衡检测时调用
    {
        if( (*cell_balance_control) & 0xAAAAAAAA)
        {
            *cell_balance_control =  (*cell_balance_control) & 0xAAAAAAAA;
        }
        else
        {
            *cell_balance_control =  (*cell_balance_control) & 0x55555555;
        }
    }
    else {
    // 相邻电芯不能同时启动
        if (ODD_BIT == cell_balance_data.bOddBit[chip_no-1])
        {
            *cell_balance_control = (*cell_balance_control) & 0xAAAAAAAA;
        }
        else
        {
            *cell_balance_control = (*cell_balance_control) & 0x55555555;
        }
    }
}

/**
 * @brief  寻找均衡芯片
 * @retval SUCCESSFUL 成功，FAILURE 失败
 * @note   判断使用的是TI还是民芯的芯片,一块芯片对应10路通道,需两块芯片级联
 */
static short balance_chip_find(void) {
    BattChip_device1 = rt_device_find("bq76952_2");
    BattChip_device2 = rt_device_find("bq76952");

    if(RT_NULL == BattChip_device1 && RT_NULL == BattChip_device2) {
        BattChip_device1 = rt_device_find("mt9818_2");
        BattChip_device2 = rt_device_find("mt9818");
        cell_balance_data.ucVolReg = VC1_HI;
        cell_balance_data.ucBalanceReg = CELLBAL1;
    } else {
        cell_balance_data.ucVolReg = Cell1Voltage;
        cell_balance_data.ucBalanceReg = CB_ACTIVE_CELLS;
    }

    if(RT_NULL != BattChip_device1 && RT_NULL != BattChip_device2) {
        rt_device_open(BattChip_device1 , RT_DEVICE_FLAG_RDWR);
        rt_device_open(BattChip_device2 , RT_DEVICE_FLAG_RDWR);
        return SUCCESSFUL;
    }
    return FAILURE;
}

/**
 * @description: 不同芯片按照不同策略执行均衡
 * @param {rt_device_t} *BattChip_device
 * @param {short} cell_balance_control
 * @return {*}
 * @Date: 2023-04-07
 */
static void cell_balance_control_excute(rt_device_t BattChip_device, unsigned int cell_balance_control)
{
    unsigned char special_channel = 0; // 对于不同的芯片，10路对应不同的通道
    char ucTmp[3] = {0};

    if(cell_balance_data.ucBalanceReg == CB_ACTIVE_CELLS)
    {
        if (cell_balance_control & (1 << 9)) // 如果cell_balance_control的第10路为1，则需要根据芯片类型将不同的位置为1
        {
            special_channel = 15; // TI芯片，10路对应16路
            cell_balance_control |= 1 << special_channel;
        }
        rt_device_control(BattChip_device, cell_balance_data.ucBalanceReg , &cell_balance_control);//TI
    }
    if(cell_balance_data.ucBalanceReg == CELLBAL1)
    {
        if (cell_balance_control & (1 << 9)) // 如果cell_balance_control的第10路为1，则需要根据芯片类型将不同的位置为1
        {
            special_channel = 17; // MT芯片，10路对应18路
            cell_balance_control |= 1 << special_channel;
        }
        ucTmp[0] = (unsigned char)(cell_balance_control & 0x3f);
        ucTmp[1] = (unsigned char)((cell_balance_control>>6) & 0x3f);
        ucTmp[2] = (unsigned char)((cell_balance_control>>12) & 0x3f);
        rt_device_control(BattChip_device, cell_balance_data.ucBalanceReg , &ucTmp);//MT
    }
}

/**
 * @brief 开启电池均衡故障检测
 * @details
 * @param[in]
 * @param[out]
 * @retval
 * @note
 */
void start_balance_fault_detected() {
    int i = 0;
    int cell_balance_control_status = 0;

    init_blance_circuit_status();

    s_balance_fault_start = TRUE;       //开始检测标识开启
    cell_balance_data.passive_flag = FALSE;     //被动均衡不可用
    /*关闭均衡，检测短路情况*/
    cell_balance_control(CONTOR_CELL_BALANCE_STOP);
    equal_circuit_fault_judge(0);

    /*没有短路故障，则检测断路故障*/
    if (s_balance_fault_result != EQUAL_CIRCUIT_SHORT) {
    /*循环开启20路电芯均衡电路，检测断路情况*/
        for(i = 0; i < BATT_MOD_CELL_NUM; i++) {
            cell_balance_control_status = 1 << i;
            cell_balance_control(cell_balance_control_status);
            equal_circuit_fault_judge(i+1);
            //均衡后关闭800ms
            cell_balance_control(CONTOR_CELL_BALANCE_STOP);
            rt_thread_delay(800);
        }
    }

    s_balance_fault_start = FALSE;
    cell_balance_data.passive_flag = TRUE;
    return;
}

/**
 * @brief 判断电芯均衡故障结果
 * @details
 * @param[in]
 * @param[out]
 * @retval
 * @note 每1ms判断一次，判断1000次，若判断无均衡故障，则退出
 */
static void equal_circuit_fault_judge(unsigned char cell_no)
{
    unsigned short rec_time = 0;
    do{
        judge_circuit_fault_status(cell_no);
        rec_time ++;
        rt_thread_delay(1);
    }while(s_balance_fault_result && rec_time < 1000);
    return;
}


/**
 * @brief 判断电芯均衡故障状态
 * @details
 * @note
 */
static void judge_circuit_fault_status(unsigned char cell_no)
{
    /* 均衡电芯编号为0，标识未开启均衡*/
    if (0 == cell_no)
    {
        if (is_equal_circuit_fault_detected()) //获取电池均衡检测DI,为高电平为真
        {
            s_balance_fault_result = EQUAL_CIRCUIT_SHORT;  //短路检测
        }
        else
        {
            s_balance_fault_result = EQUAL_CIRCUIT_NORMAL;
        }
    }
    else
    {
        if (!is_equal_circuit_fault_detected())
        {
            s_balance_fault_result = EQUAL_CIRCUIT_OPEN;  //断路检测
            s_balance_open_fault[cell_no -1] = EQUAL_CIRCUIT_OPEN;
        }
        else
        {
            s_balance_fault_result = EQUAL_CIRCUIT_NORMAL;
            s_balance_open_fault[cell_no -1] = EQUAL_CIRCUIT_NORMAL;
        }
    }
        return;
}


static void init_blance_circuit_status(void)
{
    s_balance_fault_result = EQUAL_CIRCUIT_NORMAL;
    rt_memset_s(s_balance_open_fault, BATT_MOD_CELL_NUM, 0, BATT_MOD_CELL_NUM);
    s_balance_fault_start = FALSE;
    return;
}

/**
 * @brief 判断电池均衡故障DI口是否为高电平
 * @details
 * @param[in]
 * @param[out]
 * @retval 为高电平返回TRUE，否则FALSE
 * @note 注解
 */
static unsigned char is_equal_circuit_fault_detected(void)
{
//     di_data_t di_data = {0};
//     di_data.di_pin_no = BMU_BALANCE_FAULT_DI_PIN;
//     get_di_state(&di_data);
//     return di_data.state == PIN_HIGH;
}

/**
 * @brief 获取均衡电路故障状态
 * @details
 * @param[in]
 * @param[out]
 * @retval
 * @note 注解
 */
unsigned char get_equal_circuit_fault(void)
{
    if (s_balance_fault_start)
    {
        return 0xff; //检测中
    }
    else
    {
        return s_balance_fault_result;
    }
}

/**
 * @brief  获取均衡电路断路故障状态
 * @details
 * @param[in]
 * @param[out]
 * @retval
 * @note 注解
 */
unsigned char* get_equal_circuit_open_fault(void)
{
    if (!s_balance_fault_start && s_balance_fault_result == EQUAL_CIRCUIT_OPEN)
    {
        return &s_balance_open_fault[0];
    }
    else
    {
        rt_memset_s(&s_balance_open_fault, BATT_MOD_CELL_NUM, 0, BATT_MOD_CELL_NUM);
        return &s_balance_fault_result;
    }
}

/**
 * @brief  获取被动均衡标识
 * @details
 * @param[in]
 * @param[out]
 * @retval  TURE 可被动均衡  FALSE 不可执行被动均衡
 * @note 注解
 */
unsigned char get_balance_passive_flag(void)
{
    return cell_balance_data.passive_flag;
}