{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    // hex用于烧录,elf用于调试代码
    "version": "0.2.0",
    "configurations": [
        // {
        //     // 智能空开
        //     "cwd": "${workspaceFolder}",
        //     // "executable": "${workspaceFolder}/BCMU/ROM/AIRSWITCH/MB7Z-63-A0_boot.elf",
        //     "executable": "${workspaceFolder}/BCMU/ROM/AIRSWITCH/MB7Z-63-A0_sw.elf",
        //     "name": "Debug with JLink",
        //     "request": "launch",
        //     "type": "cortex-debug",
        //     "device": "GD32C103CB",
        //     "runToEntryPoint": "main",
        //     "showDevDebugOutput": "none",
        //     "servertype": "jlink",
        //     "interface": "swd",
        //     "armToolchainPath": "D:/install/VSCode/gcc/10202110/bin", //305
        //     // "armToolchainPath": "D:/wangxuan/install/vscodecfg/10202110/bin", //深圳
        //     "serverpath": "C:/Program Files/SEGGER/JLink/JLinkGDBServerCL",   //填写jlink的执行文件,linux不需要CL
        //     "svdFile": "${workspaceFolder}/BCMU/BSP/DEBUG/OpenOCD/GD32F4xx.svd",
        //     // "ipAddress": "*************",    //远程调试时使用
        // },
        // {
        //     // BMU
        //     "name": "Debug with JLink",
        //     "cwd": "${workspaceFolder}",
        //     "executable": "${workspaceFolder}/BCMU/ROM/ZXESM_BMU/bmu_boot.elf",
        //     "executable": "${workspaceFolder}/BCMU/ROM/ZXESM_BMU/bmu_app.elf",
        //     "request": "launch",
        //     "type": "cortex-debug",
        //     "device": "GD32F403VG",
        //     "runToEntryPoint": "main",
        //     "showDevDebugOutput": "none",
        //     "servertype": "jlink",
        //     "interface": "swd",
        //     "armToolchainPath": "D:/install/VSCode/gcc/10202110/bin",
        //     //"svdFile": "GD32F4xx.svd",
        //     // "preLaunchTask": "build",
        //     // "postDebugTask": "run"
        // },
        {
            // R321 D121 SEUB ACMU
            "cwd": "${workspaceFolder}",
            // "executable": "${workspaceFolder}/build/BOOT/bms_boot.hex",
            // "executable": "${workspaceFolder}/build/APP/bms_app.hex",
            // "executable": "${workspaceFolder}/BCMU/ROM/SEUB/SEUB_app.elf",
            // "executable": "${workspaceFolder}/BCMU/ROM/SEUB/SEUB_boot.elf",
            "executable": "${workspaceFolder}/BCMU/ROM/DCMU/DCMU_app.elf",
            // "executable": "${workspaceFolder}/BCMU/ROM/DCMU/DCMU_boot.elf", 
            // "executable": "${workspaceFolder}/BCMU/ROM/ACMU/ACMU_app.elf",
            // "executable": "${workspaceFolder}/BCMU/ROM/ACMU/ACMU_boot.elf", 
            "name": "Debug with JLink",
            "request": "launch",
            "type": "cortex-debug",
            "device": "GD32F470ZG",
            "runToEntryPoint": "main",
            "showDevDebugOutput": "none",
            "servertype": "jlink",
            "interface": "swd",
            "armToolchainPath": "D:/install/VSCode/gcc/10202110/bin",
            "serverpath": "C:/Program Files/SEGGER/JLink/JLinkGDBServerCL",
            "svdFile": "${workspaceFolder}/BCMU/BSP/DEBUG/OpenOCD/GD32F4xx.svd",
        },
    ]
}




