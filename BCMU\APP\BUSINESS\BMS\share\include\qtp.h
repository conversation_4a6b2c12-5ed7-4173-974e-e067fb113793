/**************************************************************************
* Copyright (C) 2001, ZTE Corporation.
* 版权信息：（C）2010-2022，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：ZXDU18 CTL板软件
* 文件名称：protocol.h
* 文件说明：协议模块头文件
* 作    者：
* 版本信息：V1.0
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要
* 其他说明：
***************************************************************************/
#ifndef SOFTWARE_SRC_APP_QTP_H_
#define SOFTWARE_SRC_APP_QTP_H_

#ifdef __cplusplus
extern "C" {
#endif

#define   CID1_QTP                  0x4F        //QTP
//支持QTP自动化测试新增
#define GET_ANALOG_AND_SWITCH_INFO          0x42
#define GET_BMS_ALM_INFO                    0x43
#define GET_TIME_INFO                       0x44
#define SET_TIME_INFO                       0x45
#define SET_SERIAL_NUMBER_INFO              0x46
#define GET_SERIAL_NUMBER_INFO              0x47
#define GET_CELL_FACTORY_INFO               0x48
#define SET_CELL_FACTORY_INFO               0x49
#define GET_REMOTE_COMMAND_INFO             0x5A
#define GET_IO_STATE_INFO                   0x5B
#define ClEAR_HISTORY_INFO                  0x5C
#define SET_TEST_SUBPHASE_CTR_INFO          0x5D
#define GET_TEST_SUBPHASE_CTR_INFO          0x5E
#define SET_QUIT_TEST_MODE_INFO             0x5F
#define GET_BMS_FACTORY_INFO                0x60
#define GET_BDU_FACTORY_INFO                0x61
#define SET_BATT_INFO                       0x62
#define START_BALA_CIRC_CHECK_QTP           0x63    //启动均衡电路故障检测
#define GET_BALA_CIRC_FAULT_INFO            0x64    //获取均衡电路故障状态
#define START_AND_STOP_HEATER_HAET_QTP      0x65    //启动和停止加热器加热
#define GET_FAULT_INFO                      0x68    //获取故障诊断状态
#define GET_HEATER_CURRENT                  0x69    //启动加热膜电流检测
#define SEND_HEATER_CURRENT                 0x6A    //读取加热膜电流
#define SET_FAT_ID_AT_QTP                   0x6B    //设置出厂测试记录
#define GET_FAT_ID_AT_QTP                   0x6C    //读取出厂测试记录
#define SET_BMS_PARA                        0x6D    //设置电池参数数据
#define GET_BMS_PARA                        0x6E    //获取电池参数数据
#define SET_SYS_PARA_BY_ID                  0x6F    //设置电池系统参数
#define GET_SYS_PARA_BY_ID                  0x70    //获取电池系统参数
#define RESTORE_DEFAULT_PARA_QTP            0x71    //恢复缺省参数
#define GET_PRODUCTION_VER_INFO             0x72    //获取生产版本软件信息
#define SET_BMS_INFO_QTP                    0x73    //设置BMS厂家信息及短称
#define GET_BMS_INFO_QTP                    0x74    //获取BMS厂家信息及短称
#define STOP_HEATER_CURRENT                 0x75    //停止加热膜电流检测
#define GET_TOTAL_DISCHARG_INFO             0x76    //获取累计放电信息
#define CLEAR_TOTAL_DISCHARG_INFO           0x77    //清除累计放电信息
#define GET_PEAK_SHIFT_PARA_QTP             0x78    //获取智能错峰参数
#define SET_PEAK_SHIFT_PARA_QTP             0x79    //设置智能错峰参数

#define GET_SOFTWARE_CUSTOMED_INFO          0x7A    //获取软件个性化信息
#define SET_SOFTWARE_CUSTOMED_INFO          0x7B    //设置软件个性化信息

#define GET_HARDWARE_INFORMATION            0x7C    //获取硬件版本信息
#define SET_HARDWARE_INFORMATION            0x7D    //设置硬件版本信息

#define GET_SPECIAL_PARA_QTP                    0x7E    //获取特殊参数
#define SET_SPECIAL_PARA_QTP                    0x7F    //设置特殊参数


//通讯协议版本号，请确认和APP中定义的是否一致
#define PROTOCOL_VER_24         0x24    //通讯协议版本号,yang_an,整理协议修改
#define CID2QTP    0

#define   BATT_SHUT_DOWN                                 0x10
#define   BATT_SLEEP                                               0x11

void DealQtptestRtnAbnormal(void);
void DealQtptestCommand(BYTE ucComType);
BOOLEAN GetQtpBalanceCircCheckFlag(void);
BOOLEAN SetQtpBalanceCircCheckFlag(BOOLEAN Param);
void SetQtpSleepStatus(BOOLEAN status);
BOOLEAN GetQtpSleepStatus();
BYTE GetChageProtectFlag(void);
BOOLEAN GetHeatTimeFlag(void);
void SetHeatTimeFlagFalse(UNUSED void *parameter);
#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // SOFTWARE_SRC_APP_QTP_H_
