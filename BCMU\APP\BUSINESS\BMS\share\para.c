#include "common.h"
#include "sample.h"
#include "realAlarm.h"
#include "battery.h"
#include "para.h"
#include "comm.h"
#include "protocol.h"
#include "CommCan.h"
#include "para_in.h"
#include "hisdata.h"
#include "fileSys.h"
#include "led.h"
#include "algorithmAES.h"
#include "utils_rtthread_security_func.h"
#include <rtdevice.h>
#include <rtthread.h>
#include <dfs.h>
#include <sys/types.h>
#if !defined(KW_CHECK)
#include "lwip/netifapi.h"
#include "netdev.h"
#include <netif/ethernetif.h>
#endif
#include "para_var_def.h"
#include "apptest.h"
#include "wireless.h"

#ifdef NTP_TIME_ZONE_ENABLE
#include "Timing.h"
#endif

#define LOG_TAG     "para"
#define LOG_LVL     LOG_LVL_WARNING

#include <ulog.h>

extern struct netif *netif_default;

/***********************  变量定义  ************************/
// 记录系统硬件参数：电芯电压数量、温度检测数量
static  T_HardwareParaStruct   s_tHardwarePara;
// 保存在EEPROM中的双区备份参数
static  T_SavePara      s_tSavePara;
static  T_SavePara      s_tSaveBackupPara;
// 待保存缺省参数
static  T_SavePara      s_tSaveDefaultPara;
// 保存在RAM中的系统参数
Static  T_SysPara       s_tSysPara;

static T_SysPara s_tSysParaFromSave;
// 用于参数ID化获取/设置的缓存
static  T_SysPara       s_tParaCache;
// 恒压放电输出电压
static FLOAT s_fRemoteSupplyOutVolt;
// 是否进行参数保存的标志
static BOOLEAN s_bParaSaveFlag = FALSE;
#ifdef GPS_BEIDOU_SET
// GPS北斗参数设置
static BYTE s_ucDefLocateMode;
#endif
Static BYTE s_ucTimeZoneNow;
static BOOLEAN s_bInitFlag = True;

// 控制类操作记录ID 有CID2和command_type组成；当第一个字节为0x00，为自定义遥控信息
const BYTE g_aucSpacialHistoryID[][2] =
{
    {0x45,0X10},//电池指示
    {0x45,0X11},//遥控充电回路闭合
    {0x45,0X12},//遥控充电回路断开
    {0x45,0X13},//遥控放电回路闭合
    {0x45,0X14},//遥控放电回路断开
    {0x45,0X15},//清除单体损坏
    {0x9D,0X00},//设定时间信息
    {0x97,0X20},//BMS复位
    {0x97,0X21},//恢复缺省参数
    {0x97,0X22},//竞争地址
    {0x97,0X23},//解除闭锁
    {0x97,0X24},//上位机防盗解锁
    {0x97,0x25},//地址切换
    {0x97,0x2A},//人工撤防
    {0x97,0x2B},//人工进入布防
    {0x00,0x00},//系统重启
    {0x00,0x01},//地址改变
    {0x00,0x02},//按键休眠
    {0x00,0x03},//主从机切换
    {0x00,0x04},//关机
    {0x00,0x05},//boot升级
    {0x9E,0x20},//波特率切换
    {0x00,0x06},//进入静置休眠状态
    {0x00,0x07},//退出静置休眠状态
    {0x00,0x08},//控制闭锁
    {0x00,0x09},//电压二级保护断开接触器
    {0x00,0x0A},//电压二级保护闭合接触器
    {0x00,0x0B},//电压二级保护闭锁控制
    {0x00,0x0C},//进入APPTEST
    {0x00,0x0D},//退出APPTEST
    {0x00,0x0E},//进入QTP
    {0x00,0x0F},//退出QTP
    {0x00,0x10},//低功耗休眠
    {0x00,0x11},//采样IC重启
    {0x00,0x12},//网管防盗解锁
    {0x00,0x13},//布防状态改变
    {0x00,0x14},//网管人工撤防
    {0x00,0x15},//按键布防查询
    {0x97,0x28},//清除直流内阻异常保护
    {0x00,0x16},//升级从机升级成功
};

static uint8_t s_aucParaTypeSizeMap[] = {
    sizeof(float),
    sizeof(WORD),
    sizeof(BOOLEAN),
    sizeof(BYTE),
    sizeof(T_DateStruct), // NOTE: 刚好对齐无填充，暂时这么写
    sizeof(ULONG),
};

// 自研协议偏移地址列举
const BYTE s_OffsetAddr[3] = {0, 4, 8};

/*****************  静态函数原型定义  **********************/
static BOOLEAN GetParaScope(T_SysPara *ptPara, BYTE ID_PARA, FLOAT *pfMax, FLOAT *pfMin);
static void LoadDefaultPara(T_SysPara *ptPara);
static void PreWrPara(void);
static FLOAT GetPara(T_SysPara *ptPara, BYTE ucParaID);
static BOOLEAN CheckDigStatus(BYTE *p, BYTE ucCounter, BYTE ucMaxStatus);
static BOOLEAN JudgeParaItemValid(T_SysPara *ptPara, BYTE ucParaID);
static BOOLEAN SaveParaAction(T_SysPara *ptNewPara, T_SysPara *ptOldPara, T_ChangeByEnum ChangeBy);
static BOOLEAN CheckIpAddr(char *p) UNUSED;  // TODO: 仅D121使用，应抽离成接口后去除UNUSED宏和static属性
static BOOLEAN CheckPhoneNumber(BYTE *p, BYTE ucCounter) UNUSED;  // TODO: 仅R321使用，应抽离成接口后去除UNUSED宏和static属性
static void ParseSavedPara(void *ptSavedPara, size_t savedParaFileSize, T_SysPara *ptSysPara);
static WORD CalculateParaSN(WORD wParaID);
static void WriteSavedPara(T_SysPara *ptSysPara, T_SavePara *ptSavedPara, const WORD *awParaIDList, WORD wIDNum);
static BOOLEAN DealParaNotDefault(T_SysPara *ptSysPara);
static BOOLEAN CheckPrivateID(WORD ParaID);
static BOOLEAN CheckIpMask(char *p);

Static BOOLEAN DealTheftAlarmOnParaChange(T_SysPara *ptSysPara);
Static BOOLEAN DealDeviceTheftAlarm(T_SysPara *ptSysPara);
#ifdef SITE_ANTI_THEFT_ENABLED
Static BOOLEAN DealSiteTheftAlarm(T_SysPara *ptSysPara);
#endif
#ifdef NET_ANTI_THEFT_ENABLED
Static BOOLEAN DealNetTheftAlarmPara(T_SysPara *ptSysPara);
#endif
static ssize_t GetParaByProperty(const ParaProperty *property, void *dest, size_t len, size_t (*converter)(ParaType type, void *value));
static ssize_t SetParaByProperty(const ParaProperty *property, const void *src, size_t len, size_t (*converter)(ParaType type, void *value));

#ifdef DEVICE_USING_R321
static int DecryptAndProcess(BYTE* dest);
#endif

/****************************************************************
//  函数名称    :InitAesKey
//  功能描述    ：AES加解密函数在调用后会将key置0，需要在每次加解密前调用此函数
*****************************************************************/
void InitAesKey(void)
{
    CHAR acBMS_SN[15];
    GetBmsSn(acBMS_SN);
    mbedtls_aes_update_key((unsigned char *)acBMS_SN);
    return;
}

/****************************************************************
//  函数名称    : GetHardwarePara
//  输入参数    ：ptHardwarePara
//  返 回 值    : 无
//  功能描述    ：从s_tHardwarePara读取参数，避免重复从flash读取
*****************************************************************/
void GetHardwarePara(T_HardwareParaStruct *ptHardwarePara)
{
    rt_memcpy((BYTE *)ptHardwarePara, (BYTE *)&s_tHardwarePara, sizeof(T_HardwareParaStruct));
    return;
}

/*************************************************************************
函数名称    ：  GetParaMax
输入参数    ：  ID_PARA -- 参数ID号
返 回 值    ：  参数最大值
功能描述    ：  根据参数号,获得该参数能够设置的最大值，单元测试用
*************************************************************************/
void GetParaMax(BYTE ID_PARA, FLOAT *fRet)
{
    *fRet = -100.0;

    // 如果参数号无效，返回-1
    if (ID_PARA >= PARA_NUM)
    {
        return;
    }
    // 根据不同参数，获取其最大值
    switch (ID_PARA)
    {
    default:
        *fRet = g_afMaxPara[ID_PARA];
        break;
    }

    return;
}

/*************************************************************************
函数名称    ：  GetParaMin
输入参数    ：  ID_PARA -- 参数ID号
返 回 值    ：  参数最小值
功能描述    ：  根据参数号,获得该参数能够设置的最小值，单元测试用
*************************************************************************/
void GetParaMin(BYTE ID_PARA, FLOAT *fRet)
{
    *fRet = -100.0;

    // 如果参数号无效，返回-1
    if (ID_PARA >= PARA_NUM)
    {
        return;
    }
    // 根据不同参数，获取其最小值
    switch (ID_PARA)
    {
    default:
        *fRet = g_afMinPara[ID_PARA];
        break;
    }

    return;
}


static double GetDiffDayNum(T_DateStruct tStartDate, T_DateStruct tEndDate)
{
    struct tm tTmTime;
    time_t tStartTime = 0;
    time_t tEndTime = 0;
    double dDiffTime = 0;
    rt_memset_s(&tTmTime, sizeof(tTmTime), 0x00, sizeof(tTmTime)); // 初始化结构体，确保所有字段为零

    // 设置起始日期的年、月、日
    tTmTime.tm_year     = tStartDate.wYear - 1900; // 年份从1900年开始计算
    tTmTime.tm_mon      = tStartDate.ucMonth - 1;  // 月份从0开始（1月是0）
    tTmTime.tm_mday     = tStartDate.ucDay;        // 日
    tStartTime = mktime(&tTmTime); // 将tm结构转换为time_t类型的时间戳

    // 设置结束日期的年、月、日
    tTmTime.tm_year     = tEndDate.wYear - 1900;   // 年份从1900年开始计算
    tTmTime.tm_mon      = tEndDate.ucMonth - 1;    // 月份从0开始（1月是0）
    tTmTime.tm_mday     = tEndDate.ucDay;          // 日
    tEndTime = mktime(&tTmTime); // 将tm结构转换为time_t类型的时间戳

    if(tEndTime <= tStartTime)
    {
        return 0; // 如果结束时间早于或等于起始时间，返回0
    }

    dDiffTime = difftime(tEndTime, tStartTime); // 计算两个时间戳之间的差值

    // 检查差值是否超过INT32S的最大值
    if(dDiffTime > INT32S_MAX)
    {
        return 0; // 如果差值过大，返回0
    }

    // 返回天数差，单位为天
    return dDiffTime/ONE_DAY_SECONDS;
}

static T_DateStruct DateAdd(T_DateStruct tStartDate, WORD wDay)
{
    struct tm tTime;
    struct tm tStartTime;
    time_t tObjectTime = 0;
    T_DateStruct tObjectDate;
    rt_memset_s(&tTime, sizeof(tTime), 0x00, sizeof(tTime));       // 初始化结构体，确保所有字段为零
    rt_memset_s(&tStartTime, sizeof(tStartTime), 0x00, sizeof(tStartTime)); // 初始化结构体，确保所有字段为零
    rt_memset_s(&tObjectDate, sizeof(tObjectDate), 0x00, sizeof(tObjectDate)); // 初始化结构体，确保所有字段为零

    // 设置起始日期的年、月、日
    tStartTime.tm_year     = tStartDate.wYear - 1900; // 年份从1900年开始计算
    tStartTime.tm_mon      = tStartDate.ucMonth - 1;  // 月份从0开始（1月是0）
    tStartTime.tm_mday     = tStartDate.ucDay;        // 日
    tObjectTime = mktime(&tStartTime) +  wDay * ONE_DAY_SECONDS; // 在起始时间上增加指定天数

    localtime_r(&tObjectTime, &tTime); // 将time_t类型的时间戳转换为tm结构

    // 提取年、月、日信息
    tObjectDate.wYear = tTime.tm_year + 1900; // 年份从1900年开始计算
    tObjectDate.ucMonth = tTime.tm_mon + 1;   // 月份从1开始（1月是1）
    tObjectDate.ucDay = tTime.tm_mday;        // 日

    return tObjectDate; // 返回新的日期结构体
}



Static BOOLEAN SetBattEnableDate(time_t tStartTime)
{
    struct tm tTime;
    BOOLEAN bRet = False;
    T_DateStruct tObjectDate;
    T_BmsPACKManufactStruct tPackInfo;
    rt_memset_s(&tTime, sizeof(tTime), 0x00, sizeof(tTime));
    rt_memset_s(&tObjectDate, sizeof(tObjectDate), 0x00, sizeof(tObjectDate));
    rt_memset_s((BYTE*)&tPackInfo, sizeof(T_BmsPACKManufactStruct), 0x00, sizeof(T_BmsPACKManufactStruct));

    localtime_r(&tStartTime, &tTime);
    GetSysPara(&s_tSysPara);
    readPackManufact(&tPackInfo);

    s_tSysPara.tEnableTime.wYear = tTime.tm_year + 1900;
    s_tSysPara.tEnableTime.ucMonth = tTime.tm_mon + 1;
    s_tSysPara.tEnableTime.ucDay = tTime.tm_mday;
    if (CheckDateValid(&(s_tSysPara.tEnableTime)) && CheckDateValid(&(tPackInfo.tBattDate)))
    {
        if (GetDiffDayNum(tPackInfo.tBattDate, s_tSysPara.tEnableTime) < ONE_WEEK_DAYS)
        {
            tObjectDate = DateAdd(tPackInfo.tBattDate, ONE_WEEK_DAYS);
            rt_memcpy_s(&s_tSysPara.tEnableTime, sizeof(T_DateStruct), &tObjectDate, sizeof(T_DateStruct));
        }

        if (SetSysPara(&s_tSysPara, True, CHANGE_BY_AUTO))
        {
            bRet = True;
            rt_memcpy_s(&tPackInfo.tActiveDate, sizeof(T_DateStruct), &s_tSysPara.tEnableTime, sizeof(T_DateStruct));
            tPackInfo.wCRC = CRC_Cal((BYTE*)&tPackInfo, offsetof(T_BmsPACKManufactStruct, wCRC));
            writePackManufact(&tPackInfo);
        }
    }

    return bRet;
}

BOOLEAN CheckBattEnableDate(T_BattInfo *pBattIn)
{
    BOOLEAN bRet = False;
    static WORD s_wCheckMinutes = 0;
    static BOOLEAN s_bStartFlag = False;
    static time_t s_tStartTime = 0;
    static time_t s_tCurrentTime = 0;
    if (rt_memcmp(&s_tSysPara.tEnableTime, &s_tDefaultPara.tEnableTime, sizeof(T_DateStruct)) == 0)
    {
        if (!s_bStartFlag)
        {
            if (pBattIn->tData.fBusVol >= 53.0f)
            {
                s_wCheckMinutes = s_wCheckMinutes + 1;
                if (s_wCheckMinutes >= 5)
                {
                    s_bStartFlag = True;
                    s_tStartTime = time(RT_NULL);
                }
            }
            else
            {
                s_wCheckMinutes = 0;
            }
        }

        if (s_bStartFlag)
        {
#ifdef USE_D121_ENABLE_DATE
            if ((pBattIn->tData.fCellVoltMin >= 1.5f) && (pBattIn->tData.fBatVol > 12.0f))
#else
            if ((pBattIn->tData.fCellVoltMin >= 1.5f) && (pBattIn->tData.fBatVol > 20.0f))
#endif
            {
                s_tCurrentTime = time(RT_NULL);
                if ((s_tCurrentTime - s_tStartTime) > ONE_WEEK_SECONDS)
                {
                    SetBattEnableDate(s_tStartTime);
                }
            }
            else
            {
                s_tStartTime = time(RT_NULL);
            }
        }

        bRet = True;
    }
    return bRet;
}



static BOOLEAN checkckParaScope1(T_SysPara *ptPara, BYTE ID_PARA, FLOAT *pfMax, FLOAT *pfMin)
{
    switch (ID_PARA)
    {
        case PARA_ID_CHG_CURR_HIGH_PRT:
            *pfMin = max(*pfMin, ptPara->fChgCurrHighAlmThre);
            *pfMin = max(*pfMin, ptPara->fChargeMaxCurr + 0.04);
            break;
        #ifdef DEVICE_USING_R321
        case PARA_ID_DISCHG_CURR_HIGH_PRT:
            *pfMin = max(*pfMin, ptPara->fDischgCurrHighAlmThre);
            break;
        #endif
        case PARA_ID_BATT_OVER_VOLT_PRT:
            *pfMin = max(*pfMin, ptPara->fBattOverVoltAlmThre);
            break;
        case PARA_ID_BOARD_TEMP_HIGH_PRT:
            *pfMin = max(*pfMin, ptPara->fBoardTempHighcThre);
            break;
        case PARA_ID_CHG_CURR_HIGH_ALM:
            *pfMax = min(*pfMax, ptPara->fChgCurrHighPrtThre);
            *pfMin = max(*pfMin, ptPara->fChargeMaxCurr);
            break;
        case PARA_ID_DISCHG_CURR_HIGH_ALM:
            //告警处理
            #ifdef DEVICE_USING_R321
            *pfMax = min(*pfMax, ptPara->fDischgCurrHighPrtThre);
            if(s_tHardwarePara.ucCellVoltNum != R121_CELL_VOLT_NUM)
            {
                *pfMin = max(*pfMin, (g_ProConfig.fBdcuDischgMax+2)/ptPara->wBatteryCap);
            }
            *pfMin = GetValidData(*pfMin, g_afMaxPara[PARA_ID_DISCHG_CURR_HIGH_ALM], g_afMinPara[PARA_ID_DISCHG_CURR_HIGH_ALM]);
            #endif
            break;
        default:
            return False;
    }
    return True;
}

static BOOLEAN checkckParaScope2(T_SysPara *ptPara, BYTE ID_PARA, FLOAT *pfMax, FLOAT *pfMin)
{
    switch (ID_PARA)
    {
        case PARA_ID_CELL_OVER_VOLT_PRT:
            *pfMin = max(*pfMin, ptPara->fCellOverVoltAlmThre);
            break;
        case PARA_ID_CELL_UNDER_VOLT_ALM:
            *pfMin = max(*pfMin, ptPara->fCellUnderVoltPrtThre);
            break;
        case PARA_ID_CELL_UNDER_VOLT_PRT:
            *pfMax = min(*pfMax, ptPara->fCellUnderVoltAlmThre);
            *pfMin = max(*pfMin, ptPara->fCellDamagePrtThre);
            break;
        case PARA_ID_CHG_TEMP_HIGH_ALM:
            *pfMin = max(*pfMin, ptPara->fChgTempLowAlmThre);
            *pfMax = min(*pfMax, ptPara->fChgTempHighPrtThre);
            break;
        case PARA_ID_CHG_TEMP_HIGH_PRT:
            *pfMin = max(*pfMin, ptPara->fChgTempHighAlmThre);
            break;
        case PARA_ID_DISCHG_TEMP_HIGH_ALM:
            *pfMin = max(*pfMin, ptPara->fDischgTempLowAlmThre);
            *pfMax = min(*pfMax, ptPara->fDischgTempHighPrtThre);
            break;
        case PARA_ID_DISCHG_TEMP_HIGH_PRT:
            *pfMin = max(*pfMin, ptPara->fDischgTempHighAlmThre);
            break;
        case PARA_ID_CHG_TEMP_LOW_ALM:
            *pfMin = max(*pfMin, ptPara->fChgTempLowPrtThre);
            *pfMax = min(*pfMax, ptPara->fChgTempHighAlmThre);
            break;
        case PARA_ID_CHG_TEMP_LOW_PRT:
            *pfMax = min(*pfMax, ptPara->fChgTempLowAlmThre);
            *pfMax = min(*pfMax, ptPara->fChgHeaterStartupTemp);
            break;
        case PARA_ID_DISCHG_TEMP_LOW_ALM:
            *pfMin = max(*pfMin, ptPara->fDischgTempLowPrtThre);
            *pfMax = min(*pfMax, ptPara->fDischgTempHighAlmThre);
            break;
        default:
            return False;
    }
    return True;
}

static BOOLEAN checkckParaScope3(T_SysPara *ptPara, BYTE ID_PARA, FLOAT *pfMax, FLOAT *pfMin)
{
    switch (ID_PARA)
    {
        case PARA_ID_CELL_POOR_CONSIS_ALM:
            *pfMax = min(*pfMax, ptPara->fCellPoorConsisPrtThre);
            break;
        case PARA_ID_CELL_POOR_CONSIS_PRT:
            *pfMin = max(*pfMin, ptPara->fCellPoorConsisAlmThre);
            break;
        case PARA_ID_BATT_SOC_LOW_ALM:
            *pfMin = max(*pfMin, ptPara->wBattSOCLowPrtThre);
            #ifndef DEVICE_USING_R321
            *pfMax = min(*pfMax, ptPara->wBattSOCLowAlmRecoThre - 2);
            #endif
            break;
        case PARA_ID_BATT_SOC_LOW_PRT:
            *pfMax = min(*pfMax, ptPara->wBattSOCLowAlmThre);
            break;
        case PARA_ID_BATT_SOH_ALM:
            *pfMin = max(*pfMin, ptPara->wBattSOHPrtThre);
            break;
        case PARA_ID_BATT_SOH_PRT:
            *pfMax = min(*pfMax, ptPara->wBattSOHAlmThre);
            break;
        case PARA_ID_CELL_DAMAGE_PRT:
            *pfMax = min(*pfMax, ptPara->fCellUnderVoltPrtThre);
            break;
        case PARA_ID_BATT_SUPPL_VOLT:
            *pfMin = max(*pfMin, ptPara->fBattUnderVoltAlmThre);
            break;
        case PARA_ID_ENV_TEMP_HIGH_PRT:
            *pfMin = max(*pfMin,ptPara->fEnvTempHighAlmThre);
            break;
        case PARA_ID_ENV_TEMP_LOW_PRT:
            *pfMax = min(*pfMax,ptPara->fEnvTempLowAlmThre);
            break;
        case PARA_ID_BATT_OVER_VOLT_ALM_RECO:
            *pfMax = min(*pfMax,ptPara->fBattOverVoltAlmThre - 0.25*s_tHardwarePara.ucCellVoltNum*BATT_SOC_MAX);          //D121显示和实际存在2倍关系
            //*pfMax = min(*pfMax,ptPara->fBattOverVoltPrtRecoThre);
            break;
        // case PARA_ID_BATT_OVER_VOLT_PRT_RECO:
        //     *pfMax = min(*pfMax,ptPara->fBattOverVoltPrtThre - 0.13*s_tHardwarePara.ucCellVoltNum*BATT_SOC_MAX);        //D121显示和实际存在2倍关系
        //     break;
        default:
            return False;
    }
    return True;
}

static BOOLEAN checkckParaScope4(T_SysPara *ptPara, BYTE ID_PARA, FLOAT *pfMax, FLOAT *pfMin)
{
    switch (ID_PARA)
    {
        case PARA_ID_CELL_OVER_VOLT_ALM_RECO:
            *pfMax = min(*pfMax,ptPara->fCellOverVoltAlmThre - 0.15);
            *pfMax = min(*pfMax,ptPara->fCellOverVoltPrtRecoThre);
            break;
        case PARA_ID_CELL_OVER_VOLT_PRT_RECO:
            *pfMax = min(*pfMax,ptPara->fCellOverVoltPrtThre - 0.3);
            break;
        case PARA_ID_CELL_UNDER_VOLT_ALM_RECO:
            *pfMin = max(*pfMin,ptPara->fCellUnderVoltAlmThre + 0.2);
            break;
        case PARA_ID_CELL_UNDER_VOLT_PRT_RECO:
            *pfMin = max(*pfMin,ptPara->fCellUnderVoltPrtThre + 0.2);
            break;
        case PARA_ID_CHG_TEMP_HIGH_ALM_RECO:
            *pfMax = min(*pfMax,ptPara->fChgTempHighAlmThre - 3);
            *pfMax = min(*pfMax,ptPara->fChgTempHighPrtRecoThre);
            break;
        case PARA_ID_CHG_TEMP_HIGH_PRT_RECO:
            *pfMax = min(*pfMax,ptPara->fChgTempHighPrtThre - 3);
            break;
        case PARA_ID_DISCHG_TEMP_HIGH_ALM_RECO:
            *pfMax = min(*pfMax,ptPara->fDischgTempHighAlmThre - 3);
            *pfMax = min(*pfMax,ptPara->fDischgTempHighPrtRecoThre);
            break;
        case PARA_ID_DISCHG_TEMP_HIGH_PRT_RECO:
            *pfMax = min(*pfMax,ptPara->fDischgTempHighPrtThre - 3);
            break;
        case PARA_ID_CHG_TEMP_LOW_ALM_RECO:
            *pfMin = max(*pfMin,ptPara->fChgTempLowAlmThre + 3);
            *pfMin = max(*pfMin,ptPara->fChgTempLowPrtRecoThre);
            break;
        case PARA_ID_CHG_TEMP_LOW_PRT_RECO:
            *pfMin = max(*pfMin,ptPara->fChgTempLowPrtThre + 3);
            break;
        case PARA_ID_DISCHG_TEMP_LOW_ALM_RECO:
            *pfMin = max(*pfMin,ptPara->fDischgTempLowAlmThre + 3);
            *pfMin = max(*pfMin,ptPara->fDischgTempLowPrtRecoThre);
            break;
        case PARA_ID_DISCHG_TEMP_LOW_PRT_RECO:
            *pfMin = max(*pfMin,ptPara->fDischgTempLowPrtThre + 3);
            break;
        default:
            return False;
    }
    return True;
}

static BOOLEAN checkckParaScope5(T_SysPara *ptPara, BYTE ID_PARA, FLOAT *pfMax, FLOAT *pfMin)
{
    switch (ID_PARA)
    {
        case PARA_ID_ENV_TEMP_HIGH_ALM_RECO:
            *pfMax = min(*pfMax,ptPara->fEnvTempHighAlmThre - 5);
            *pfMax = min(*pfMax,ptPara->fEnvTempHighPrtRecoThre);
            break;
        case PARA_ID_ENV_TEMP_HIGH_PRT_RECO:
            *pfMax = min(*pfMax,ptPara->fEnvTempHighPrtThre - 5);
            break;
        case PARA_ID_ENV_TEMP_LOW_ALM_RECO:
            *pfMin = max(*pfMin,ptPara->fEnvTempLowAlmThre + 5);
            *pfMin = max(*pfMin,ptPara->fEnvTempLowPrtRecoThre);
            break;
        case PARA_ID_ENV_TEMP_LOW_PRT_RECO:
            *pfMin = max(*pfMin,ptPara->fEnvTempLowPrtThre + 5);
            break;
        case PARA_ID_BOARD_TEMP_HIGH_ALM_RECO:
            *pfMax = min(*pfMax,ptPara->fBoardTempHighcThre - 15);
            *pfMax = min(*pfMax,ptPara->fBoardTempHighPrtRecoThre);
            break;
        case PARA_ID_BOARD_TEMP_HIGH_PRT_RECO:
            *pfMax = min(*pfMax,ptPara->fBoardTempHighPrtThre - 15);
            break;
#ifdef DEVICE_USING_D121
        case PARA_ID_BATT_SOC_LOW_ALM_RECO: // 电池SOC低告警恢复阈值
            *pfMin = max(*pfMin,ptPara->wBattSOCLowAlmThre + 2);
            *pfMin = max(*pfMin,ptPara->wBattSOCLowPrtThre + 1);
            break;
        case PARA_ID_CELL_TEMP_PRT_SHIELD: // 单体温度保护屏蔽
            *pfMax = min(*pfMax, s_tHardwarePara.ucCellTempNum);
            break;
#endif
        case PARA_ID_CHRG_MAX_CURR:           // 最大充电电流
            *pfMin = max(*pfMin, BDCU_CHARGE_RATE_MIN/ptPara->wBatteryCap);
            *pfMax = min(*pfMax, ptPara->fChgCurrHighAlmThre);
            *pfMax = min(*pfMax, (BDCU_CHARGE_RATE_MAX + 0.6)/ptPara->wBatteryCap);
            *pfMax = min(*pfMax, ptPara->fChgCurrHighPrtThre - 0.04);
            break;
        case PARA_ID_DISCHRG_MAX_CURR:        // 最大放电电流
            *pfMin = max(*pfMin, BDCU_DISCHARGE_RATE_MIN/ptPara->wBatteryCap);
            *pfMax = min(*pfMax, BDCU_DISCHARGE_RATE_MAX/ptPara->wBatteryCap);
            break;
        default:
            return False;
    }
    return True;
}

static BOOLEAN checkckParaScope6(T_SysPara *ptPara, BYTE ID_PARA, FLOAT *pfMax, FLOAT *pfMin)
{
    switch (ID_PARA)
    {
        case PARA_ID_CHG_HEATER_STARTUP_TEMP: // 充电加热膜启动温度
            *pfMax = min(*pfMax, ptPara->fChgHeaterShutdownTemp);
            *pfMin = max(*pfMin, ptPara->fChgTempLowPrtThre);
            break;
        case PARA_ID_CHG_HEATER_SHUTDOWN_TEMP: // 充电加热膜关闭温度
            *pfMin = max(*pfMin, ptPara->fChgHeaterStartupTemp);
            break;
        case PARA_ID_DISCHG_HEATER_STARTUP_TEMP: // 放电加热膜启动温度
            *pfMax = min(*pfMax, ptPara->fDischgHeaterShutdownTemp);
            *pfMin = max(*pfMin, ptPara->fDischgTempLowPrtThre);
            break;
        case PARA_ID_DISCHG_HEATER_SHUTDOWN_TEMP: // 放电加热膜关闭温度
            *pfMin = max(*pfMin, ptPara->fDischgHeaterStartupTemp);
            break;
        case PARA_ID_HEATER_TEMP_HIGH_THRE: // 加热膜过温阈值
            *pfMin = max(*pfMin, ptPara->fHeaterTempHighRel);
            break;
        case PARA_ID_HEATER_TEMP_HIGH_RELEASE: // 加热膜过温解除
            *pfMax = min(*pfMax, ptPara->fHeaterTempHighThre);
            break;
        default:
            return False;
    }
    return True;
}

static BOOLEAN checkckParaScope7(T_SysPara *ptPara, BYTE ID_PARA, FLOAT *pfMax, FLOAT *pfMin)
{
    switch (ID_PARA)
    {
        case PARA_ID_DISCHG_TEMP_LOW_PRT:
            *pfMax = min(*pfMax, ptPara->fDischgTempLowAlmThre);
            *pfMax = min(*pfMax, ptPara->fDischgHeaterStartupTemp);
            break;
        case PARA_ID_DISCHARGE_REMO_END_OUTPUT_VOLT_FIRST: // 末期放电电压1
            *pfMin = max(*pfMin, ptPara->fDischargeEndVolt2);
            break;
        case PARA_ID_DISCHARGE_REMO_END_OUTPUT_VOLT_SECOND: // 末期放电电压2
            *pfMax = min(*pfMax, ptPara->fDischargeEndVolt1);
            break;
        case PARA_ID_PREFAULT_RECORDING_NUMBER: // 故障前录波条数
            *pfMax = min(*pfMax, 10 - ptPara->ucPostRecordNum);
            break;
        case PARA_ID_POSTFAULT_RECORDING_NUMBER: // 故障后录波条数
            *pfMax = min(*pfMax, 10 - ptPara->ucPreRecordNum);
            break;
        case PARA_ID_DCR_FAULT_ALM_THRE: // 直流内阻异常告警阈值
            *pfMax = min(*pfMax, ptPara->ucDcrFaultPrtThre);
            break;
        case PARA_ID_DCR_FAULT_PRT_THRE: // 直流内阻异常保护阈值
            *pfMin = max(*pfMin, ptPara->ucDcrFaultAlmThre);
            break;
        case PARA_ID_BATT_FAULT_TEMP_HIGH_PRT: // 电池异常温度高保护阈值
            *pfMin = max(*pfMin,ptPara->fDischgTempHighPrtThre + 5);
            break;
        case PARA_ID_SWITCH_SOC2: // 放电末期切换SOC2
            *pfMax = min(*pfMax, ptPara->wDischgSwitchSOC);
            break;
        default:
            return False;
    }
    return True;
}


static BOOLEAN checkParaScope8(T_SysPara *ptPara, BYTE ID_PARA, FLOAT *pfMax, FLOAT *pfMin)
{
    if (ptPara == NULL || pfMax == NULL || pfMin == NULL) {
        return False; // 返回错误码，表示无效指针
    }

    switch (ID_PARA)
    {
        case PARA_ID_BATT_OVER_VOLT_ALM:
            *pfMax = min(*pfMax, ptPara->fBattOverVoltPrtThre);
            break;
        case PARA_ID_BOARD_TEMP_HIGH_ALM:
            *pfMax = min(*pfMax, ptPara->fBoardTempHighPrtThre);
            break;
        case PARA_ID_ENV_TEMP_HIGH_ALM:
            *pfMin = max(*pfMin, ptPara->fEnvTempLowAlmThre);
            break;
        case PARA_ID_ENV_TEMP_LOW_ALM:
            *pfMax = min(*pfMax, ptPara->fEnvTempHighAlmThre);
            break;
        case PARA_ID_BATT_UNDER_VOLT_ALM:
            *pfMin = max(*pfMin, ptPara->fBattUnderVoltPrtThre);
            *pfMax = min(*pfMax, ptPara->fBattSupplVolt);
            break;
        case PARA_ID_BATT_UNDER_VOLT_PRT:
            *pfMax = min(*pfMax, ptPara->fBattUnderVoltAlmThre);
            break;
        case PARA_ID_CELL_OVER_VOLT_ALM:
            *pfMax = min(*pfMax, ptPara->fCellOverVoltPrtThre);
            break;
        default:
            return False;
    }
    return True;
}


/***************************************************************************
 * @brief    获取参数范围
 * @param    {T_SysPara} *ptPara
 * @param    {BYTE} ID_PARA
 * @param    {FLOAT} *pfMax
 * @param    {FLOAT} *pfMin
 * @return   {*}
 **************************************************************************/
static BOOLEAN GetParaScope( T_SysPara *ptPara, BYTE ID_PARA, FLOAT *pfMax, FLOAT *pfMin)
{
    if ( ID_PARA >= PARA_NUM )
    {
        return False;
    }
    *pfMax = g_afMaxPara[ID_PARA];
    *pfMin = g_afMinPara[ID_PARA];
    if(checkckParaScope1(ptPara, ID_PARA, pfMax, pfMin) != False)
    {
        return True;
    }
    if(checkckParaScope2(ptPara, ID_PARA, pfMax, pfMin) != False)
    {
        return True;
    }
    if(checkckParaScope3(ptPara, ID_PARA, pfMax, pfMin) != False)
    {
        return True;
    }
    if(checkckParaScope4(ptPara, ID_PARA, pfMax, pfMin) != False)
    {
        return True;
    }
    if(checkckParaScope5(ptPara, ID_PARA, pfMax, pfMin) != False)
    {
        return True;
    }
    if(checkckParaScope6(ptPara, ID_PARA, pfMax, pfMin) != False)
    {
        return True;
    }
    if(checkckParaScope7(ptPara, ID_PARA, pfMax, pfMin) != False)
    {
        return True;
    }
    if(checkParaScope8(ptPara, ID_PARA, pfMax, pfMin) != False)
    {
        return True;
    }
    return False;
}


BOOLEAN IsSaveParaValid(T_SavePara *ptSavePara, ssize_t filesize)
{
    if (filesize < sizeof(T_SaveParaSegment1))
    {
        return False;
    }

    BOOLEAN chk1 = (CRC_Cal((BYTE *)ptSavePara, offsetof(T_SaveParaSegment1, wCrc) + 2) == 0); 

    BOOLEAN chk2 = True;
    if (filesize >= sizeof(T_SavePara))
    {
        chk2 = (CRC_Cal((BYTE *)ptSavePara + offsetof(T_SavePara, segment2), offsetof(T_SaveParaSegment2, wCrc) + 2) == 0); 
    }

    return chk1 && chk2;
}

//************************************************************
//  函数名称    ：LoadDefaultPara
//  输入参数    ：ptPara -- 参数保存区
//  返 回 值    ：无
//  功能描述    ：载入参数缺省值
// 作    者 ：王威
// 版本信息：V1.0
// 设计日期：2010-06-13
// 修改记录：
// 日    期     版  本      修改人      修改摘要      
// 其他说明：
//*************************************************************
static void LoadDefaultPara(T_SysPara *ptPara)
{
    BOOLEAN bCheckResult = 0;
    LONG lDefaultParaReadRtn = 0;
    T_SysPara tSavedSysPara;
    rt_memset(&s_tSaveDefaultPara, 0, sizeof(s_tSaveDefaultPara));
    rt_memset(&tSavedSysPara, 0, sizeof(tSavedSysPara));

    lDefaultParaReadRtn = readFile(FILE_NAME_DEFAULT_PARA, (BYTE *)&s_tSaveDefaultPara, sizeof(T_SavePara));

    rt_memcpy((BYTE *)ptPara, (BYTE *)&s_tDefaultPara, sizeof(T_SysPara));
    rt_memcpy((BYTE *)&tSavedSysPara, (BYTE *)&s_tDefaultPara, sizeof(T_SysPara));
    if (IsSaveParaValid(&s_tSaveDefaultPara, lDefaultParaReadRtn))
    {
        ParseSavedPara(&s_tSaveDefaultPara, lDefaultParaReadRtn, &tSavedSysPara);
    }
#ifdef DEVICE_USING_D121
    if(tSavedSysPara.wBatteryCap == 0)         //解决Coverity问题
        tSavedSysPara.wBatteryCap = 50;
#else
    if(tSavedSysPara.wBatteryCap == 0)         //解决Coverity问题
        tSavedSysPara.wBatteryCap = 100;
#endif
    bCheckResult = ChkSysPara(&tSavedSysPara);
    if (bCheckResult == True)
    {
        rt_memcpy((BYTE *)ptPara, (BYTE *)&tSavedSysPara, sizeof(T_SysPara));
    }

    s_fRemoteSupplyOutVolt = ptPara->fRemoteSupplyOutVolt;

#ifdef GPS_BEIDOU_SET
    s_ucDefLocateMode = ptPara->ucLocateMode;
#endif
    return;
}

void CalcSaveParaCrc(T_SavePara *ptSavePara)
{
    ptSavePara->segment1.wCrc = CRC_Cal((BYTE *)&ptSavePara->segment1, offsetof(T_SaveParaSegment1, wCrc));
    ptSavePara->segment2.wCrc = CRC_Cal((BYTE *)&ptSavePara->segment2, offsetof(T_SaveParaSegment2, wCrc));
}

/****************************************************************
//  函数名称    ：PreWrPara
//  输入参数    ：无
//  返 回 值    ：无
//  功能描述    ：写入参数前的预处理
// 作    者 ：王威
// 版本信息：V1.0
// 设计日期：2010-06-13
// 修改记录：
            2023-09-01 将重要参数备份区用于对所有参数进行双备份
// 日    期     版  本      修改人      修改摘要      
// 其他说明：
*****************************************************************/
static void PreWrPara(void)
{
    // 写入所有参数
    WORD wSaveIDNum;
    wSaveIDNum = sizeof(s_awParaID) / sizeof(s_awParaID[0]);
    WriteSavedPara(&s_tSysPara, &s_tSavePara, s_awParaID, wSaveIDNum);
    CalcSaveParaCrc(&s_tSavePara);

    // 将所有参数双备份到重要参数备份区
    WriteSavedPara(&s_tSysPara, &s_tSaveBackupPara, s_awParaID, wSaveIDNum);
    CalcSaveParaCrc(&s_tSaveBackupPara);

    return;
}

/****************************************************************
//  函数名称    ：GetSysPara
//  输入参数    ：ptSysPara －读取系统参数的保存地址
//  返 回 值    ：无
//  功能描述    ：读取参数，从s_tSysPara读取
// 作    者 ：王威
// 版本信息：V1.0
// 设计日期：2010-06-13
// 修改记录：
// 日    期     版  本      修改人      修改摘要      
// 其他说明：
*****************************************************************/
void GetSysPara(T_SysPara *ptSysPara)
{
    rt_memcpy((BYTE *)ptSysPara, (BYTE *)&s_tSysPara, sizeof(T_SysPara));
    return;
}

/****************************************************************
//  函数名称    ：GetSysParaPointer
//  输入参数    ：无
//  返 回 值    ：返回系统参数
//  功能描述    ：系统参数存放在s_tSysPara变量中，如果每次采用GetSysPara()获取参数，都必须将s_tSysPara中的内容复制一份，当前空间不足，
                 采用指针的方式获取参数，可以节省空间
// 作    者 ：hxk
// 版本信息：V1.0
// 设计日期：2024-04-04
// 修改记录：
// 日    期     版  本      修改人      修改摘要      
// 其他说明：
*****************************************************************/
T_SysPara *GetSysParaPointer(void)
{
    return &s_tSysPara;
}

/***************************************************************************
 * @brief    获取由数据字典生成的固定缺省参数
 **************************************************************************/
void GetSysDefaultPara(T_SysPara *ptSysPara)
{
    rt_memcpy((BYTE *)ptSysPara, (BYTE *)&s_tDefaultPara, sizeof(T_SysPara));
    return;
}


Static void LoadParaScope(void)
{
    BYTE i,ucCellNum;
    double dScale = 1000;

    ucCellNum = s_tHardwarePara.ucCellVoltNum;

    for(i=0;i<sizeof(s_awCellNumParaID)/sizeof(s_awCellNumParaID[0]);i++)
    {
        //最大值
        g_afMaxPara[s_awCellNumParaID[i]] = round(g_afMaxPara[s_awCellNumParaID[i]] * ucCellNum * g_ProConfig.fVoltTrasRate *dScale)/dScale;
        
        //最小值
        g_afMinPara[s_awCellNumParaID[i]] = round(g_afMinPara[s_awCellNumParaID[i]] * ucCellNum * g_ProConfig.fVoltTrasRate *dScale)/dScale;
    }
    //默认值
    s_tDefaultPara.fBattOverVoltAlmThre = s_tDefaultPara.fBattOverVoltAlmThre * ucCellNum * g_ProConfig.fVoltTrasRate;	// 电池组过压告警阈值
    s_tDefaultPara.fBattOverVoltPrtThre = s_tDefaultPara.fBattOverVoltPrtThre * ucCellNum * g_ProConfig.fVoltTrasRate;	// 电池组过压保护阈值

    s_tDefaultPara.fBattUnderVoltAlmThre = s_tDefaultPara.fBattUnderVoltAlmThre * ucCellNum * g_ProConfig.fVoltTrasRate;	 // 电池组欠压告警阈值
    s_tDefaultPara.fBattUnderVoltPrtThre = s_tDefaultPara.fBattUnderVoltPrtThre * ucCellNum * g_ProConfig.fVoltTrasRate;	 // 电池组欠压保护阈值

    s_tDefaultPara.fBattOverVoltAlmRecoThre = s_tDefaultPara.fBattOverVoltAlmRecoThre * ucCellNum * g_ProConfig.fVoltTrasRate;	 // 电池组过压告警恢复值
    s_tDefaultPara.fBattOverVoltPrtRecoThre = s_tDefaultPara.fBattOverVoltPrtRecoThre * ucCellNum * g_ProConfig.fVoltTrasRate;	 // 电池组过压保护恢复值

#ifndef DEVICE_USING_R321
    s_tDefaultPara.fBattUnderVoltAlmRecoThre = s_tDefaultPara.fBattUnderVoltAlmRecoThre * ucCellNum * g_ProConfig.fVoltTrasRate;    // 电池组欠压告警恢复阈值,设置不实际生效，参数范围不具备参考性
    s_tDefaultPara.fBattUnderVoltPrtRecoThre = s_tDefaultPara.fBattUnderVoltPrtRecoThre * ucCellNum * g_ProConfig.fVoltTrasRate;    // 电池组欠压保护恢复阈值,设置不实际生效，参数范围不具备参考性
#endif
    s_tDefaultPara.fBattSupplVolt = s_tDefaultPara.fBattSupplVolt * ucCellNum * g_ProConfig.fVoltTrasRate;    // 电池补充电电压
    s_tDefaultPara.fBattChgFullAverVoltThre = s_tDefaultPara.fBattChgFullAverVoltThre * ucCellNum * g_ProConfig.fVoltTrasRate;  // 电池组充满电压
}


#ifdef DEVICE_USING_R321

// 辅助函数，用于解密并处理填充
static int DecryptAndProcess(BYTE* dest)
{
    int ret = 0;
    BYTE aucPlainText[LEN_TYPE_STRING_16] = {0};
    BYTE ucPadSize = 0;

    ret = Decrypt_plain_aes(dest, LEN_TYPE_STRING_16, aucPlainText);
    ucPadSize = aucPlainText[LEN_TYPE_STRING_16 - 1];
    if (ucPadSize > 0 && ucPadSize <= LEN_TYPE_STRING_16)
    {
        for (int i = LEN_TYPE_STRING_16 - 1; i >= LEN_TYPE_STRING_16 - ucPadSize; i--)
        {
            aucPlainText[i] = 0;
        }
    }
    rt_memcpy_s(dest, LEN_TYPE_STRING_16, aucPlainText, LEN_TYPE_STRING_16);
    return ret;
}

// 主函数，初始化默认参数
int InitDefaultPara(void)
{
    int ret = 0;

    // SNMP V3鉴别
    ret = DecryptAndProcess(s_tDefaultPara.acSNMPV3AuthPass);
    if (ret != 0)
    {
        return ret;
    }

    // SNMP V3加密
    ret = DecryptAndProcess(s_tDefaultPara.acSNMPV3PrivPass);
    if (ret != 0)
    {
        return ret;
    }

    return ret;
}

#endif

/****************************************************************
//  函数名称    ：InitSysPara
//  输入参数    ：无
//  返 回 值    ：如果从EEPROM读出有效，则返回TRUE，否则返回FALSE
//  功能描述    ：初始化系统参数，把静态参数s_tSysPara从EEPROM取出，
                  并且检验，获取有效的参数
// 作    者 ：王威
// 版本信息：V1.0
// 设计日期：2010-06-13
// 修改记录：
// 日    期     版  本      修改人      修改摘要      
// 其他说明：
*****************************************************************/
BOOLEAN InitSysPara(void)
{
    BOOLEAN bCrcChkResult, bChkRamParaResult;
    LONG lParaReadRtn, lBackupParaReadRtn;
    s_bInitFlag = True;

#ifdef DEVICE_USING_R321
    InitDefaultPara();
#endif

    rt_memset(&s_tHardwarePara, 0, sizeof(T_HardwareParaStruct));
    readBmsHWPara(&s_tHardwarePara);

    lParaReadRtn = readFile(FILE_NAME_PARA, (BYTE *)&s_tSavePara, sizeof(T_SavePara));
    lBackupParaReadRtn = readFile(FILE_NAME_BACKUP_PARA, (BYTE *)&s_tSaveBackupPara, sizeof(T_SavePara));

    LoadParaScope();
  
    // 由于参数可能增删，因此先载入缺省值，再用保存的参数进行覆盖
    LoadDefaultPara(&s_tSysPara);
    // 判断基本参数区CRC是否正确
    if (IsSaveParaValid(&s_tSavePara, lParaReadRtn))
    {
        ParseSavedPara(&s_tSavePara, lParaReadRtn, &s_tSysPara);
        bCrcChkResult = True;
    }

    else
    {
        // 重要参数恢复
        if (IsSaveParaValid(&s_tSaveBackupPara, lBackupParaReadRtn)
#ifndef KW_CHECK
            && (lBackupParaReadRtn > 0)
#endif
        )
        {
            ParseSavedPara(&s_tSaveBackupPara, lBackupParaReadRtn, &s_tSysPara);
        }
        bCrcChkResult = False;
    }

    // 参数范围校验
    bChkRamParaResult = ChkRamPara();

    // 参数范围校验错误
    if (False == bChkRamParaResult)
    {
        //载入缺省值
        LoadDefaultPara(&s_tSysPara);
    }

    if ((False == bCrcChkResult) || (False == bChkRamParaResult))
    {
        SetSysPara(&s_tSysPara, False, CHANGE_BY_INIT);
    }
    LoadNetPara();
    readBmsHWPara(&s_tHardwarePara);

#ifdef NTP_TIME_ZONE_ENABLE
    s_ucTimeZoneNow = s_tSysPara.ucTimeZone;       //当前时时区参数
#endif
    s_bInitFlag = False;
    return (bCrcChkResult & bChkRamParaResult);
}

/***************************************************************************
 * @brief    载入MAC并应用设置网络参数
 **************************************************************************/
void LoadNetPara(void)
{
#if !defined(KW_CHECK)
    UNUSED ip_addr_t ipaddr, netmask, gw; // TODO: 仅R321使用，分离后去除UNUSED宏
    T_NetMacStruct tNetMacPara;
    rt_device_t ETH_dev;
    struct netdev *pDev = netdev_get_by_name("e0");
    if(RT_NULL == pDev)
    {
        return;
    }
    // 先从文件系统中加载MAC，默认是UID生成的MAC
    readNetMacPara(&tNetMacPara);
    ETH_dev = rt_device_find("e0");
    if (RT_NULL == ETH_dev)
    {
        return;
    }
    rt_device_control(ETH_dev, NIOCTL_SADDR, &tNetMacPara.aucMacAddr[0]);

#ifdef DEVICE_USING_R321
    if (s_tSysPara.ucLocalIPGetMode)
    {
        return;
    }

    //netifapi_dhcp_stop(netif_default);
//    netdev_dhcp_enabled(pDev, 0);
    ipaddr.addr = netdev_ipaddr_addr((char *)s_tSysPara.acLocalIPAddr);
    gw.addr = netdev_ipaddr_addr((char *)s_tSysPara.acGateway);
    netmask.addr = netdev_ipaddr_addr((char *)s_tSysPara.acMask);
    netifapi_netif_set_addr(netif_default, &ipaddr, &netmask, &gw);
#endif

#endif
    return;
}


void JudgeBuzzEnable( void )
{
	ON_BUZZ(s_tSysPara.bBuzzerEnable);
}

/****************************************************************
//  函数名称    ：CalculateParaSN
//  输入参数    ：
//  返 回 值    ：
//  功能描述    ：根据参数ID计算该参数在所有参数中的序号
*****************************************************************/
static WORD CalculateParaSN(WORD wParaID)
{
    WORD i;
    WORD AllParaNum;
    AllParaNum = sizeof(s_awParaID) / sizeof(s_awParaID[0]);
    for (i = 0; i < AllParaNum; i++)
    {
        if (wParaID == s_awParaID[i])
        {
            break;
        }
    }
    return i;
}


#define PARSE_SAVED_PARA_SEGMENT_TEMPLATE(seg_num) \
{ \
    WORD ParaID; \
    WORD ParaSN; \
    WORD ParaOffset; \
    WORD AllParaNum; \
    WORD NumericalParaNum; \
    WORD StringParaNum; \
    WORD AlarmParaNum; \
    WORD i, j; \
    BYTE ParaSaveAccuracy; \
    BYTE ParaAccuracy; \
    BYTE ParaLenSaved; \
    BYTE ParaLenNow; \
    BOOLEAN bRet; \
    BYTE aucDataBuff[MBEDTLS_PARA_MAX_LEN] = {0}; \
    BYTE aucEncryptedData[MBEDTLS_PARA_MAX_LEN] = {0}; \
    AllParaNum = sizeof(s_awParaID) / sizeof(s_awParaID[0]); \
    NumericalParaNum = ptSavedPara->wNumericalParaNum; \
    StringParaNum = ptSavedPara->wStringParaNum; \
    AlarmParaNum = ptSavedPara->wAlarmParaNum; \
    if ((NumericalParaNum > MAX_NUMERICAL_PARA_SAVE_NUM_##seg_num) || \
        (StringParaNum > MAX_STRING_PARA_SAVE_NUM_##seg_num) || \
        (AlarmParaNum > 2)) \
    { \
        return; \
    } \
    for (i = 0; i < NumericalParaNum; i++) \
    { \
        ParaOffset = 0; \
        ParaID = ptSavedPara->atNumericalParaList[i].wID; \
        ParaSaveAccuracy = ptSavedPara->atNumericalParaList[i].ucAccuracy; \
        ParaLenSaved = ptSavedPara->atNumericalParaList[i].ucLen; \
        ParaSN = CalculateParaSN(ParaID); \
        if (ParaLenSaved > 4) \
        { \
            continue; \
        } \
        if (ParaSN == AllParaNum) \
        { \
            continue; \
        } \
        else \
        { \
            ParaAccuracy = g_aucParaWei[ParaSN]; \
            if (ParaSaveAccuracy != ParaAccuracy) \
            { \
                /* No action needed */ \
            } \
            for (j = 0; j < ParaSN; j++) \
            { \
                ParaOffset += g_aucParaSpace[j]; \
            } \
            rt_memcpy((BYTE *)ptSysPara + ParaOffset, ptSavedPara->atNumericalParaList[i].aucVal, ParaLenSaved); \
            for (j = 0; j < sizeof(s_awCellNumParaID) / sizeof(s_awCellNumParaID[0]); j++) \
            { \
                if (ParaSN == s_awCellNumParaID[j]) \
                { \
                    *(FLOAT *)((BYTE *)ptSysPara + ParaOffset) = \
                        (*(FLOAT *)((BYTE *)ptSysPara + ParaOffset)) * \
                        s_tHardwarePara.ucCellVoltNum * g_ProConfig.fVoltTrasRate; \
                } \
            } \
        } \
    } \
    for (i = 0; i < StringParaNum; i++) \
    { \
        ParaOffset = 0; \
        ParaID = ptSavedPara->atStringParaList[i].wID; \
        ParaLenSaved = ptSavedPara->atStringParaList[i].ucLen; \
        ParaSN = CalculateParaSN(ParaID); \
        if (ParaLenSaved > MAX_STRING_PARA_SAVE_LEN) \
        { \
            continue; \
        } \
        if (ParaSN == AllParaNum) \
        { \
            continue; \
        } \
        else \
        { \
            for (j = 0; j < ParaSN; j++) \
            { \
                ParaOffset += g_aucParaSpace[j]; \
            } \
            ParaLenNow = g_aucParaSpace[ParaSN]; \
            bRet = CheckPrivateID(ParaID); \
            if (bRet == True) \
            { \
                rt_memset(aucDataBuff, 0, sizeof(aucDataBuff)); \
                rt_memset(aucEncryptedData, 0, sizeof(aucEncryptedData)); \
                rt_memcpy(aucEncryptedData, ptSavedPara->atStringParaList[i].aucVal, MBEDTLS_PARA_MAX_LEN); \
                InitAesKey(); \
                Decrypt_aes(aucEncryptedData, MBEDTLS_PARA_MAX_LEN, aucDataBuff); \
            } \
            if (ParaLenSaved > ParaLenNow && bRet == True) \
            { \
                rt_memcpy((BYTE *)ptSysPara + ParaOffset, aucDataBuff, ParaLenNow); \
            } \
            else if (ParaLenSaved <= ParaLenNow && bRet == True) \
            { \
                rt_memcpy((BYTE *)ptSysPara + ParaOffset, aucDataBuff, ParaLenSaved); \
            } \
            else if (ParaLenSaved > ParaLenNow && bRet == False) \
            { \
                rt_memcpy((BYTE *)ptSysPara + ParaOffset, ptSavedPara->atStringParaList[i].aucVal, ParaLenNow); \
            } \
            else \
            { \
                rt_memcpy((BYTE *)ptSysPara + ParaOffset, ptSavedPara->atStringParaList[i].aucVal, ParaLenSaved); \
            } \
        } \
    } \
    for (i = 0; i < AlarmParaNum; i++) \
    { \
        ParaOffset = 0; \
        ParaID = ptSavedPara->atAlarmParaList[i].wID; \
        ParaLenSaved = ptSavedPara->atAlarmParaList[i].ucLen; \
        ParaSN = CalculateParaSN(ParaID); \
        if (ParaLenSaved > MAX_ALARM_PARA_SAVE_LEN) \
        { \
            continue; \
        } \
        if (ParaSN == AllParaNum) \
        { \
            continue; \
        } \
        else \
        { \
            for (j = 0; j < ParaSN; j++) \
            { \
                ParaOffset += g_aucParaSpace[j]; \
            } \
            ParaLenNow = g_aucParaSpace[ParaSN]; \
            if (ParaLenSaved > ParaLenNow) \
            { \
                rt_memcpy((BYTE *)ptSysPara + ParaOffset, ptSavedPara->atAlarmParaList[i].aucVal, ParaLenNow); \
            } \
            else \
            { \
                rt_memcpy((BYTE *)ptSysPara + ParaOffset, ptSavedPara->atAlarmParaList[i].aucVal, ParaLenSaved); \
            } \
        } \
    } \
}

Static void ParseSavedParaSegment1(T_SaveParaSegment1 *ptSavedPara, T_SysPara *ptSysPara)
{
    PARSE_SAVED_PARA_SEGMENT_TEMPLATE(1)
}

Static void ParseSavedParaSegment2(T_SaveParaSegment2 *ptSavedPara, T_SysPara *ptSysPara)
{
    PARSE_SAVED_PARA_SEGMENT_TEMPLATE(2)
}



static void ParseSavedPara(void *ptSavedPara, size_t savedParaFileSize, T_SysPara *ptSysPara)
{
    if (savedParaFileSize >= sizeof(T_SaveParaSegment1))
    {
        ParseSavedParaSegment1(ptSavedPara, ptSysPara);
    }

    if (savedParaFileSize >= sizeof(T_SaveParaSegment2))
    {
        T_SaveParaSegment2 *segment2 = (T_SaveParaSegment2 *)((BYTE *)ptSavedPara + offsetof(T_SavePara, segment2));
        ParseSavedParaSegment2(segment2, ptSysPara);
    }
}


/****************************************************************
//  函数名称    ：CheckRamPara
//  输入参数    ：无
//  返 回 值    ：校验是否成功
//  功能描述    ：检测参数s_tSysPara,如果不正确，返回FALSE
// 作    者 ：王威
// 版本信息：V1.0
// 设计日期：2010-06-13
// 修改记录：
// 日    期     版  本      修改人      修改摘要      
// 其他说明：
*****************************************************************/
BOOLEAN ChkRamPara(void)
{
    BOOLEAN bRet;

    bRet = ChkSysPara(&s_tSysPara);

    return bRet;
}

//***************************************************************
// 函数名称 ：GetPara( T_SysPara * ptPara, BYTE ucParaID )
// 输入参数 ：ptPara    -- 参数区
//          ucParaID  -- 参数号
// 返 回 值 ：参数值
// 功能描述 ：获取参数
// 作    者 ：王威
// 版本信息 ：V1.0
// 设计日期 ：2010-06-13
// 修改记录 ：
// 日   期 : 版  本      修改人      修改摘要
// 其他说明：
//************************************************************
static FLOAT GetPara(T_SysPara *ptPara, BYTE ucParaID)
{
    FLOAT fRet = 0;
    WORD wOffset = 0;
    BYTE i;
    BYTE *p;
    BYTE ucParaLen;

    if (ucParaID >= PARA_NUM)
    {
        return fRet;
    }

    for (i = 0; i < ucParaID; i++)
    {
        wOffset += g_aucParaSpace[i];
    }

    ucParaLen = g_aucParaSpace[ucParaID];
    p = (BYTE *)ptPara;
    p += wOffset;

    if (1 == ucParaLen)
    {
        fRet = p[0];
    }
    else if (2 == ucParaLen)
    {
        fRet = (p[1] << 8) | p[0];
    }
    #ifdef DEVICE_USING_D121
    else if (ucParaID == PARA_ID_POWER_OFF_TIME_PRT_THRE)   //针对D121 PAD协议中设置中移参数中的停电时间保护阈值4位整形数据的特殊处理
    {
        U_32Int uTemp;
        uTemp.ucData[0] = p[0];
        uTemp.ucData[1] = p[1];
        uTemp.ucData[2] = p[2];
        uTemp.ucData[3] = p[3];
        fRet = (FLOAT)uTemp.LData;
    }
    #endif
    else
    {
        FLOAT_u floatData = {0};
        floatData.ucData[0] = p[0];
        floatData.ucData[1] = p[1];
        floatData.ucData[2] = p[2];
        floatData.ucData[3] = p[3];
        fRet = floatData.fData;
    }

    return fRet;
}

//************************************************************
//  函数名称    ：CheckDigStatus
//  输入参数    ：P           -- 字符串  
//                ucCounter   -- 数量  
//                ucMaxStatus -- 状态量
//  返 回 值    ：TRUE -- 正确  False -- 错误
//  功能描述    ：检查字符串p中ucCounter个字符是否在ucMaxStatus范围内
// 作    者 ：王威
// 版本信息：V1.0
// 设计日期：2010-06-13
// 修改记录：
// 日    期     版  本      修改人      修改摘要      
// 其他说明：
//************************************************************
static BOOLEAN CheckDigStatus(BYTE *p, BYTE ucCounter, BYTE ucMaxStatus)
{
    BYTE i;

    for (i = 0; i < ucCounter; i++)
    {
        if (p[i] > ucMaxStatus)
        {
            return False; //错误返回
        }
    }

    return True; //正确返回
}

static BOOLEAN CheckPhoneNumber(BYTE *p, BYTE ucCounter)
{
    BYTE i;
    if (p == NULL || ucCounter == (BYTE)0)
    {
        return False;
    }
    for (i = 0; i < ucCounter; i++)
    {
        if ((p[i] >= (BYTE)0x30 && p[i] <= (BYTE)0x39) || (p[i] == (BYTE)0x2B && i == (BYTE)0) || p[i] == (BYTE)0x00)
        {
        }
        else
        {
            return False;
        }
    }
    return True; //正确返回
}

static BOOLEAN CheckAlmLvlValid(BYTE *pucAlmLvl)
{
    BYTE i;
    for (i = 0; i < ALARM_CLASS; i++)
    {
        if ((pucAlmLvl[i] < aucAlmLevelMin[i]) || (pucAlmLvl[i] > 2))
        {
            return False;
        }
    }
    return True;
}

#ifdef DEVICE_USING_R321
/**
 * @brief 校验电话号码类参数的有效性
 * 
 * @param ptPara 
 * @param ucParaID 
 * @return SHORT   NOT_FOUND -1为没有找到对应参数， True 1为找到参数且校验正确， False 0为找到参数且校验错误。
 */
static SHORT JudgeParaItemPhoneValid(T_SysPara *ptPara, BYTE ucParaID)
{
    switch (ucParaID)
    {
        case PARA_ID_ALM_PHONE_NUM_1:
            return CheckPhoneNumber(ptPara->acAlmPhoneNum_1, LEN_TYPE_STRING_20);

        case PARA_ID_ALM_PHONE_NUM_2:
            return CheckPhoneNumber(ptPara->acAlmPhoneNum_2, LEN_TYPE_STRING_20);

        case PARA_ID_ALM_PHONE_NUM_3:
            return CheckPhoneNumber(ptPara->acAlmPhoneNum_3, LEN_TYPE_STRING_20);

        case PARA_ID_ALM_PHONE_NUM_4:
            return CheckPhoneNumber(ptPara->acAlmPhoneNum_4, LEN_TYPE_STRING_20);

        case PARA_ID_ALM_PHONE_NUM_5:
            return CheckPhoneNumber(ptPara->acAlmPhoneNum_5, LEN_TYPE_STRING_20);

        case PARA_ID_SMS_CENTER_NUM:
            return CheckPhoneNumber( ptPara->acSMSCenterNum, LEN_TYPE_STRING_20);
    }
    return NOT_FOUND;
}

/**
 * @brief 校验GPRS类参数的有效性
 * 
 * @param ptPara 
 * @param ucParaID 
 * @return SHORT   NOT_FOUND -1为没有找到对应参数， True 1为找到参数且校验正确， False 0为找到参数且校验错误。
 */
static SHORT JudgeParaItemGPRSValid(T_SysPara *ptPara, BYTE ucParaID)
{
    switch (ucParaID)
    {
        case PARA_ID_GPRS_USER_NAME:
            return CheckCharRange( ptPara->acGPGSUserName, LEN_TYPE_STRING_20);

        case PARA_ID_GPRS_PAZWERD:
            return CheckCharRange( ptPara->acGPGSPazwerd, LEN_TYPE_STRING_10);

        case PARA_ID_GPRS_APN:
            return CheckCharRange(ptPara->acGPGSAPN, LEN_TYPE_STRING_20);
    }
    return NOT_FOUND;
}

/**
 * @brief 校验地址类参数的有效性
 * 
 * @param ptPara 
 * @param ucParaID 
 * @return SHORT   NOT_FOUND -1为没有找到对应参数， True 1为找到参数且校验正确， False 0为找到参数且校验错误。
 */
static SHORT JudgeParaItemIpAddrValid(T_SysPara *ptPara, BYTE ucParaID)
{
    switch (ucParaID)
    {
        case PARA_ID_BACKSTAGE_IP_ADDR:
            return CheckIpAddr((char *)ptPara->acBackstageIpAddr);

        case PARA_ID_LOCAL_IP:
            return CheckIpAddr((char *)ptPara->acLocalIPAddr);

        case PARA_ID_MASK:
            return CheckIpMask((char *)ptPara->acMask);

        case PARA_ID_GATEWAY:
            return CheckIpAddr((char *)ptPara->acGateway);

        case PARA_ID_NTP_IP:
            return CheckIpAddr((char *)ptPara->acNtpIPAddr);
        
    }
    return NOT_FOUND;
}

/**
 * @brief 校验SNMP类参数的有效性
 * 
 * @param ptPara 
 * @param ucParaID 
 * @return SHORT   NOT_FOUND -1为没有找到对应参数， True 1为找到参数且校验正确， False 0为找到参数且校验错误。
 */
static SHORT JudgeParaItemSnmpValid(T_SysPara *ptPara, BYTE ucParaID)
{
    switch (ucParaID)
    {
        case PARA_ID_SNMP_TRAP_IP:
            return CheckIpAddr((char *)ptPara->acSNMPTrapIP);

        case PARA_ID_SNMP_READ_COMM:
            return CheckCharRange(ptPara->acSNMPReadCommunity, LEN_TYPE_STRING_16);

        case PARA_ID_SNMP_SET_COMM:
            return CheckCharRange(ptPara->acSNMPSetCommunity, LEN_TYPE_STRING_16);

        case PARA_ID_SNMP_NAME:
            return CheckCharRangeAndLength(ptPara->acSNMPV3UserName, LEN_TYPE_STRING_16, 1);//长度不能为0

        case PARA_ID_SNMP_AUTH_PASS:
            return CheckCharRangeAndLength(ptPara->acSNMPV3AuthPass, LEN_TYPE_STRING_16, 8);//长度不能小于8

        case PARA_ID_SNMP_PRIV_PASS:
            return CheckCharRangeAndLength(ptPara->acSNMPV3PrivPass, LEN_TYPE_STRING_16, 8);//长度不能小于8

        case PARA_ID_RETMOTE_UPDATE_IP_ADDR:
            return CheckIpAddr((char *)ptPara->acRemoteUpdateIpAddr);
    }
    return NOT_FOUND;
}
#endif

#ifdef GPS_BEIDOU_SET

Static BOOLEAN IsSetLocateMode(BYTE ucLocateMode)
{
    BYTE ucMax, ucMin;
    BYTE ucLocateModeType;

    if (!GetFlagRun() || GetApptestFlag() || GetQtptestFlag())
    {
        ucMax = (BYTE)g_afMaxPara[PARA_ID_LOCATE_MODE];
        ucMin = (BYTE)g_afMinPara[PARA_ID_LOCATE_MODE];
        return (ucLocateMode >= ucMin && ucLocateMode <= ucMax);
    }
    else
    {
        ucLocateModeType = GetLocateModeType();
        if (ucLocateModeType == GPS_AND_BEIDOU)
        {
            ucMax = MIX_MODE;
            ucMin = GPS_MODE;
        }
        else
        {
            ucMax = ucMin = ucLocateModeType;
        }

        return (ucLocateMode >= ucMin && ucLocateMode <= ucMax) ? SetLocateMode(ucLocateMode, False) : False;
    }
}

#endif

#ifdef NTP_TIME_ZONE_ENABLE

Static BOOLEAN SetTimeZone(BYTE ucTimeZoneSet)
{
    BYTE ucMax = (BYTE)g_afMaxPara[PARA_ID_TIME_ZONE];
    BYTE ucMin = (BYTE)g_afMinPara[PARA_ID_TIME_ZONE];
    time_t t = time(RT_NULL);

    if (ucTimeZoneSet < ucMin || ucTimeZoneSet > ucMax)
    {
        return False;
    }

    if (GetQtptestFlag() || GetApptestFlag() || ucTimeZoneSet == s_ucTimeZoneNow || s_bInitFlag == True)
    {
        return True;
    }

    time_t t_set = t - GetTimeZone(s_ucTimeZoneNow) * ONE_HOUR_SECONDS + GetTimeZone(ucTimeZoneSet) * ONE_HOUR_SECONDS;

    if (SetSysTimeByTimeStamp(t_set))
    {
        s_ucTimeZoneNow = ucTimeZoneSet;
        return True;
    }

    return False;
}

#endif


Static SHORT JudgeParaItemSetBspValid(T_SysPara *ptPara, BYTE ucParaID)
{
    switch (ucParaID)
    {
        #ifdef GPS_BEIDOU_SET
        case PARA_ID_LOCATE_MODE:
            return IsSetLocateMode(ptPara->ucLocateMode);
        #endif

        #ifdef NTP_TIME_ZONE_ENABLE
        case PARA_ID_TIME_ZONE:
            return SetTimeZone(ptPara->ucTimeZone);
        #endif

        default:
            return NOT_FOUND;
    }
}


//************************************************************
//  函数名称    ：JudgeParaItemValid( T_SysPara * ptPara, 
//                                    BYTE  ucParaID)
//  输入参数    ：ptPara   -- 临时参数区
//                ucParaID -- 参数ID号
//  返 回 值    ：是否正确保存，TRUE -- 正确，FALE -- 错误
//  功能描述    ：判断系统参数某一项的有效性
// 作    者 ：王威
// 版本信息：V1.0
// 设计日期：2010-06-13
// 修改记录：
// 日    期     版  本      修改人      修改摘要      
// 其他说明：
//************************************************************
static BOOLEAN JudgeParaItemValid(T_SysPara *ptPara, BYTE ucParaID)
{
    FLOAT fPara, fMax, fMin;
    SHORT sResult = NOT_FOUND;
    if (ucParaID >= PARA_NUM)
    {
        return False;
    }

#ifdef DEVICE_USING_R321
    sResult = JudgeParaItemPhoneValid(ptPara, ucParaID);
    RETURN_VAL_IF_FAIL(sResult == NOT_FOUND, (BOOLEAN)sResult);

    sResult = JudgeParaItemGPRSValid(ptPara, ucParaID);
    RETURN_VAL_IF_FAIL(sResult == NOT_FOUND, (BOOLEAN)sResult);

    sResult = JudgeParaItemIpAddrValid(ptPara, ucParaID);
    RETURN_VAL_IF_FAIL(sResult == NOT_FOUND, (BOOLEAN)sResult);

    sResult = JudgeParaItemSnmpValid(ptPara, ucParaID);
    RETURN_VAL_IF_FAIL(sResult == NOT_FOUND, (BOOLEAN)sResult); 
#endif
    sResult = JudgeParaItemSetBspValid(ptPara, ucParaID);
    RETURN_VAL_IF_FAIL(sResult == NOT_FOUND, (BOOLEAN)sResult);

    switch (ucParaID)
    {
        case PARA_ID_RELAY: // 告警对应的干结点
#ifdef DEVICE_USING_D121
            return CheckDigStatus(ptPara->aucRelayBit, ALARM_CLASS, 1);
#else
            return CheckDigStatus(ptPara->aucRelayBit, ALARM_CLASS, 2);
#endif
        case PARA_ID_ALARM_CLASS: // 告警等级
            return CheckAlmLvlValid(ptPara->aucAlarmLevel);
        case PARA_ID_DEVICE_NAME:
            return CheckCharRange(ptPara->acDeviceName, LEN_TYPE_STRING_32);
        case PARA_ID_ENABLE_DATE:
            return CheckDateValid(&(ptPara->tEnableTime));
        case PARA_ID_BMS_SYSTEM_NAME:
            return CheckCharRange(ptPara->acBMSSysName, LEN_TYPE_STRING_20);
#ifdef PAD_SELF_DEVELOP_PROTO_OFFSET_ADDR
        case PARA_ID_SELFDEVELOP_PROTOCOL_OFFSET_ADDR:
            return CheckDiscreteValueRange(&ptPara->fSelfDevelopProtocolOffsetAddr, (BYTE*)&s_OffsetAddr[0], sizeof(s_OffsetAddr)/sizeof(BYTE), sizeof(BYTE));
#endif
        default:
            fPara = GetPara(ptPara, ucParaID);
            GetParaScope(ptPara, ucParaID, &fMax, &fMin);
            if (fPara - fMin > -0.00005f && fPara - fMax < 0.00005f)
            {
                return True;
            }
    }

    return False;
}

//************************************************************
//  函数名称    ：ChkSysPara( T_SysPara  tSysPara )
//  输入参数    ：tSysPara   带检查的系统参数
//  返 回 值    ：参数是否正确   True -- 正确  False -- 错误
//  功能描述    ：检查系统参数数据的有效性
// 作    者 ：王威
// 版本信息：V1.0
// 设计日期：2010-06-13
// 修改记录：
// 日    期     版  本      修改人      修改摘要      
// 其他说明：
//************************************************************
BOOLEAN ChkSysPara(T_SysPara *ptSysPara)
{
    BYTE i;
    BOOLEAN bRet = True;

    readBmsHWPara(&s_tHardwarePara);

    for (i = 0; i < PARA_NUM; i++)
    {
        bRet = JudgeParaItemValid(ptSysPara, i);
        if (False == bRet)
        {
            return bRet;
        }
    }

    return True;
}


static void SetDefaultParaByBdu(T_SysPara *ptSysPara, WORD wBattCap)
{
    FLOAT fBattCap = (FLOAT)wBattCap;

    if (wBattCap)
    {
        ptSysPara->fChargeMaxCurr = GetValidData((s_tDefaultPara.fChargeMaxCurr * s_tDefaultPara.wBatteryCap) / fBattCap, g_afMaxPara[PARA_ID_CHRG_MAX_CURR], g_afMinPara[PARA_ID_CHRG_MAX_CURR]);//充电最大电流
        ptSysPara->fChgCurrHighAlmThre = GetValidData((s_tDefaultPara.fChgCurrHighAlmThre * s_tDefaultPara.wBatteryCap) / fBattCap, g_afMaxPara[PARA_ID_CHG_CURR_HIGH_ALM], g_afMinPara[PARA_ID_CHG_CURR_HIGH_ALM]);//充电过流告警阈值
        ptSysPara->fDischgCurrHighAlmThre = GetValidData((s_tDefaultPara.fDischgCurrHighAlmThre * s_tDefaultPara.wBatteryCap) / fBattCap, g_afMaxPara[PARA_ID_DISCHG_CURR_HIGH_ALM], g_afMinPara[PARA_ID_DISCHG_CURR_HIGH_ALM]);//放电过流告警阈值
        ptSysPara->fChgCurrHighPrtThre = GetValidData((s_tDefaultPara.fChgCurrHighPrtThre * s_tDefaultPara.wBatteryCap) / fBattCap, g_afMaxPara[PARA_ID_CHG_CURR_HIGH_PRT], g_afMinPara[PARA_ID_CHG_CURR_HIGH_PRT]);//充电过流保护阈值
        ptSysPara->fDischgMaxCurr = GetValidData((s_tDefaultPara.fDischgMaxCurr * s_tDefaultPara.wBatteryCap) / fBattCap, g_afMaxPara[PARA_ID_DISCHRG_MAX_CURR], g_afMinPara[PARA_ID_DISCHRG_MAX_CURR]);//放电最大电流
        ptSysPara->fDischgCurrHighPrtThre = GetValidData((s_tDefaultPara.fDischgCurrHighPrtThre * s_tDefaultPara.wBatteryCap) / fBattCap, g_afMaxPara[PARA_ID_DISCHG_CURR_HIGH_PRT], g_afMinPara[PARA_ID_DISCHG_CURR_HIGH_PRT]);//放电过流保护阈值
        ptSysPara->fBattChgFullAverCurrThre = GetValidData((s_tDefaultPara.fBattChgFullAverCurrThre * s_tDefaultPara.wBatteryCap) / fBattCap, g_afMaxPara[PARA_ID_BATT_CHG_FULL_AVER_CURR], g_afMinPara[PARA_ID_BATT_CHG_FULL_AVER_CURR]);//电池充满电流
    }

    return;
}


static void DealBattCapChange(T_SysPara *ptSysPara)
{
    SetDefaultParaByBdu(ptSysPara, ptSysPara->wBatteryCap);
    return;
}

Static void DealUsageScenChange(T_SysPara* ptSysPara, T_SysPara const* ptParaSave)
{

    if (DISCHG_MODE_CONSTANT_VOLTAGE == ptSysPara->ucUsageScen)
    {
        ptSysPara->fRemoteSupplyOutVolt = s_fRemoteSupplyOutVolt;
        ptSysPara->wDischgSwitchSOC = 0;
        ptSysPara->wDischgSwitchSOC2 = 0;
    }

#ifdef DEVICE_USING_R321
    else if(DISCHG_MODE_BATT_CHARACTERISTIC == ptSysPara->ucUsageScen)
    {
        ptSysPara->bThroughDischgEnable = 0;
    }
#endif

    else
    {
        if (DISCHG_MODE_SELF_ADAPTION == ptSysPara->ucUsageScen || DISCHG_MODE_SOLAR == ptSysPara->ucUsageScen)
        {
            ptSysPara->wDischgSwitchSOC = 10;
            ptSysPara->wDischgSwitchSOC2 = 10;
        }
        ptSysPara->fRemoteSupplyOutVolt = 50.5;
    }

    return;
}

static void PreDealLinkChange(T_SysPara *ptSysPara, T_SysPara *ptParaFromSave)
{
    if (NULL == ptSysPara || NULL == ptParaFromSave)
    {
        return;
    }

    if (ptSysPara->wBatteryCap != ptParaFromSave->wBatteryCap)
    {
        DealBattCapChange(ptSysPara);
    }
	if (ptSysPara->ucUsageScen != ptParaFromSave->ucUsageScen)
    {
        DealUsageScenChange(ptSysPara, ptParaFromSave);
    }

    return;
}

static BOOLEAN CheckPrivateID(WORD ParaID)
{
    BYTE i = 0;
#ifdef DEVICE_USING_R321
    WORD PrivateIDList[] = {PARA_ID_GPRS_APN,        PARA_ID_ALM_PHONE_NUM_1, PARA_ID_ALM_PHONE_NUM_2,
                            PARA_ID_ALM_PHONE_NUM_3, PARA_ID_ALM_PHONE_NUM_4, PARA_ID_ALM_PHONE_NUM_5,
                            PARA_ID_SNMP_NAME,       PARA_ID_SNMP_AUTH_PASS,  PARA_ID_SNMP_PRIV_PASS,
                            0};
#else
    WORD PrivateIDList[] = {0};	
#endif
    while (PrivateIDList[i])
    {
        if (ParaID == PrivateIDList[i])
        {
            return True;
        }
        i++;
    }

    return False;
}


Static BOOLEAN LocateNumericalParaSegment(T_SavePara *ptSavedPara, WORD wParaNum, T_ParaSegmentInfo *ptParaSegmentInfo)
{
    LOG_D("numerical paranum: %d", wParaNum);
    if (wParaNum >= MAX_NUMERICAL_PARA_SAVE_NUM)
    {
        return False;
    }

    if (wParaNum >= MAX_NUMERICAL_PARA_SAVE_NUM_1)
    {
        ptParaSegmentInfo->itemStartAddr = (T_NumericalParaSaveItem *)((BYTE *)ptSavedPara + offsetof(T_SavePara, segment2.atNumericalParaList));
        ptParaSegmentInfo->savedNum = &ptSavedPara->segment2.wNumericalParaNum;
        ptParaSegmentInfo->offset = MAX_NUMERICAL_PARA_SAVE_NUM_1;
        ptParaSegmentInfo->capacity = MAX_NUMERICAL_PARA_SAVE_NUM_2;
        LOG_D("locate in numerical seg2");
    }
    else
    {
        ptParaSegmentInfo->itemStartAddr = (T_NumericalParaSaveItem *)((BYTE *)ptSavedPara + offsetof(T_SavePara, segment1.atNumericalParaList));
        ptParaSegmentInfo->savedNum = &ptSavedPara->segment1.wNumericalParaNum;
        ptParaSegmentInfo->offset = 0;
        ptParaSegmentInfo->capacity = MAX_NUMERICAL_PARA_SAVE_NUM_1;
        LOG_D("locate in numerical seg1");
    }
    return True;
}

Static BOOLEAN LocateStringParaSegment(T_SavePara *ptSavedPara, WORD wParaNum, T_ParaSegmentInfo *ptParaSegmentInfo)
{
    LOG_D("string paranum: %d", wParaNum);
    if (wParaNum >= MAX_STRING_PARA_SAVE_NUM)
    {
        return False;
    }

    if (wParaNum >= MAX_STRING_PARA_SAVE_NUM_1)
    {
        ptParaSegmentInfo->itemStartAddr = (T_StringParaSaveItem *)((BYTE *)ptSavedPara + offsetof(T_SavePara, segment2.atStringParaList));
        ptParaSegmentInfo->savedNum = &ptSavedPara->segment2.wStringParaNum;
        ptParaSegmentInfo->offset = MAX_STRING_PARA_SAVE_NUM_1;
        ptParaSegmentInfo->capacity = MAX_STRING_PARA_SAVE_NUM_2;
        LOG_D("locate in string seg2");
    }
    else
    {
        ptParaSegmentInfo->itemStartAddr = (T_StringParaSaveItem *)((BYTE *)ptSavedPara + offsetof(T_SavePara, segment1.atStringParaList));
        ptParaSegmentInfo->savedNum = &ptSavedPara->segment1.wStringParaNum;
        ptParaSegmentInfo->offset = 0;
        ptParaSegmentInfo->capacity = MAX_STRING_PARA_SAVE_NUM_1;
        LOG_D("locate in string seg1");
    }
    return True;
}

Static BOOLEAN LocateAlarmParaSegment(T_SavePara *ptSavedPara, WORD wParaNum, T_AlarmParaSegmentInfo *ptParaSegmentInfo)
{
    if (wParaNum >= 2)
    {
        return False;
    }
    LOG_D("alarm paranum: %d", wParaNum);

    ptParaSegmentInfo->segment[0].itemStartAddr = ((BYTE *)ptSavedPara + offsetof(T_SavePara, segment1.atAlarmParaList) + sizeof(T_AlarmParaSaveItem1) * wParaNum);
    ptParaSegmentInfo->segment[0].savedNum = &ptSavedPara->segment1.wAlarmParaNum;
    ptParaSegmentInfo->segment[0].offset = 0;
    ptParaSegmentInfo->segment[0].capacity = MAX_ALARM_PARA_SAVE_LEN_1;

    ptParaSegmentInfo->segment[1].itemStartAddr = ((BYTE *)ptSavedPara + offsetof(T_SavePara, segment2.atAlarmParaList) + sizeof(T_AlarmParaSaveItem2) * wParaNum);
    ptParaSegmentInfo->segment[1].savedNum = &ptSavedPara->segment2.wAlarmParaNum;
    ptParaSegmentInfo->segment[1].offset = MAX_ALARM_PARA_SAVE_LEN_1;
    ptParaSegmentInfo->segment[1].capacity = MAX_ALARM_PARA_SAVE_LEN_2;

    LOG_D("SavedPara addr: %u", (void *)ptSavedPara);
    LOG_D("alarm seg1 addr: %u", ptParaSegmentInfo->segment[0].itemStartAddr);
    LOG_D("alarm seg2 addr: %u", ptParaSegmentInfo->segment[1].itemStartAddr);

    return True;
}


Static ssize_t SerializeNumericalPara(T_SavePara *ptSavedPara, T_SysPara *ptSysPara, T_ParaSerializationContext *ctx)
{
    T_ParaSegmentInfo info = {0, };
    T_NumericalParaSaveItem *ptNumericalParaList = NULL;
    float tmp = 0.0f;
    WORD wIndex = 0;

    if (!LocateNumericalParaSegment(ptSavedPara, ctx->NumericalParaNum, &info))
    {
        return -1;
    }
    ptNumericalParaList = info.itemStartAddr;
    wIndex = ctx->NumericalParaNum - info.offset;
    LOG_D("numerical index: %d", wIndex);

    ptNumericalParaList[wIndex].wID = ctx->ParaID;
    ptNumericalParaList[wIndex].ucAccuracy = ctx->ParaAccuracy;
    ptNumericalParaList[wIndex].ucLen = ctx->ParaLen;
    
    tmp = *(FLOAT*)((BYTE *)ptSysPara + ctx->ParaOffset);
    for (size_t i = 0; i < sizeof(s_awCellNumParaID) / sizeof(s_awCellNumParaID[0]); i++)
    {
        if (ctx->ParaSN == s_awCellNumParaID[i])
        {
            if (s_tHardwarePara.ucCellVoltNum != 0)
            {
                *(FLOAT*)((BYTE *)ptSysPara + ctx->ParaOffset) = tmp / s_tHardwarePara.ucCellVoltNum / BATT_SOC_MAX;     //D121 实际和外部呈现存在2倍关系
            }
        }
    }

    rt_memcpy(ptNumericalParaList[wIndex].aucVal, (BYTE *)ptSysPara + ctx->ParaOffset,
                ctx->ParaLen);

    *(FLOAT*)((BYTE *)ptSysPara + ctx->ParaOffset) = tmp;
    *info.savedNum = *info.savedNum + 1;

    ctx->NumericalParaNum++;

    return sizeof(T_NumericalParaSaveItem);
}

Static ssize_t SerializeStringPara(T_SavePara *ptSavedPara, T_SysPara *ptSysPara, T_ParaSerializationContext *ctx)
{
    T_ParaSegmentInfo info = {0, };
    T_StringParaSaveItem *ptStirngParaList = NULL;
    WORD wIndex = 0;
    BYTE aucDataBuff[MBEDTLS_PARA_MAX_LEN] = {0, }; //待加密数据大小必须是16字节的倍数（aes加密算法过程决定了此特性），否则会数组访问越界
    BYTE aucEncryptedData[MBEDTLS_PARA_MAX_LEN] = {0, };
    BYTE *pucSrc = (BYTE *)ptSysPara + ctx->ParaOffset;
    WORD wSrcLen = ctx->ParaLen;

    //隐私数据在存储前进行加密处理
    if (CheckPrivateID(ctx->ParaID))
    {
        rt_memset(aucDataBuff, 0, sizeof(aucDataBuff));
        rt_memset(aucEncryptedData, 0, sizeof(aucEncryptedData));

        rt_memcpy(aucDataBuff, (BYTE *)ptSysPara + ctx->ParaOffset,
                    ctx->ParaLen); //隐私数据先取出，存入40字节大小（兼容最大）的区域
        InitAesKey();
        Encrypt_aes(aucDataBuff, MBEDTLS_PARA_MAX_LEN, aucEncryptedData);

        pucSrc = aucEncryptedData;
        wSrcLen = MBEDTLS_PARA_MAX_LEN;
    }

    if (!LocateStringParaSegment(ptSavedPara, ctx->StringParaNum, &info))
    {
        return -1;
    }
    ptStirngParaList = info.itemStartAddr;
    wIndex = ctx->StringParaNum - info.offset;
    LOG_D("string index: %d", wIndex);


    ptStirngParaList[wIndex].wID = ctx->ParaID;
    ptStirngParaList[wIndex].ucLen = ctx->ParaLen;
    rt_memcpy(ptStirngParaList[wIndex].aucVal, pucSrc, wSrcLen);
    *info.savedNum = *info.savedNum + 1;

    ctx->StringParaNum++;

    return sizeof(T_StringParaSaveItem);
}

Static ssize_t SerializeAlarmPara(T_SavePara *ptSavedPara, T_SysPara *ptSysPara, T_ParaSerializationContext *ctx)
{
    T_AlarmParaSegmentInfo info = {{{0,} }, };
    T_AlarmParaSaveItemHead *ptHead = NULL;
    BYTE *ptList = NULL;
    // WORD wIndex = 0;
    WORD wSavedAlarm = 0;
    WORD wLen = 0;

    if (!LocateAlarmParaSegment(ptSavedPara, ctx->AlarmParaNum, &info))
    {
        return -1;
    }

    for (size_t i = 0; i < ARR_SIZE(info.segment) && wSavedAlarm < ctx->ParaLen; ++i)
    {
        if (info.segment[i].capacity <= ctx->ParaLen - wSavedAlarm)
        {
            wLen = info.segment[i].capacity;
        }
        else
        {
            wLen = ctx->ParaLen - wSavedAlarm;
        }

        ptHead = info.segment[i].itemStartAddr;
        ptList = (BYTE *)ptHead + sizeof(T_AlarmParaSaveItemHead);

        ptHead->wID = ctx->ParaID;
        ptHead->ucLen = ctx->ParaLen;
        rt_memcpy(ptList, (BYTE *)ptSysPara + ctx->ParaOffset + wSavedAlarm, wLen);

        *info.segment[i].savedNum = *info.segment[i].savedNum + 1;

        wSavedAlarm += wLen;
        LOG_D("alarm saved len: %d", wSavedAlarm);
    }

    ctx->AlarmParaNum++;

    return 0;
}

/****************************************************************
//  函数名称    ：WriteSavedPara
//  输入参数    ：
//  返 回 值    ：无
//  功能描述    ：
*****************************************************************/
static void WriteSavedPara(T_SysPara *ptSysPara, T_SavePara *ptSavedPara, const WORD *awParaIDList, WORD wIDNum)
{
    T_ParaSerializationContext ctx = {0,};
    WORD i, j;
    WORD AllParaNum;

    AllParaNum = sizeof(s_awParaID) / sizeof(s_awParaID[0]);

    rt_memset_s(ptSavedPara, sizeof(T_SavePara), 0x00, sizeof(T_SavePara)); // 清空待保存的参数

    for (i = 0; i < wIDNum; i++)
    {
        ctx.ParaID = awParaIDList[i];
        ctx.ParaSN = CalculateParaSN(ctx.ParaID);

        if (ctx.ParaSN == AllParaNum)
        {
            continue;
        }

        ctx.ParaAccuracy = g_aucParaWei[ctx.ParaSN];
        ctx.ParaLen = g_aucParaSpace[ctx.ParaSN];
        ctx.ParaOffset = 0;

        for (j = 0; j < ctx.ParaSN; j++)
        {
            ctx.ParaOffset = ctx.ParaOffset + g_aucParaSpace[j];
        }
        LOG_D("parasn:%d, paraid:%d, paralen: %d, paraoffset: %d", ctx.ParaSN, ctx.ParaID, ctx.ParaLen, ctx.ParaOffset);

        //根据参数的字节长度来区分参数类型，但是若参数字节长度在不同分类切换，则会导致错误
        // 0≤ParaLen≤4：NumericalPara
        // 5≤ParaLen≤MAX_STRING_PARA_SAVE_LEN：StringPara
        // MAX_STRING_PARA_SAVE_LEN+1≤ParaLen：AlarmPara
        if (ctx.ParaLen <= 4)
        {
            if (SerializeNumericalPara(ptSavedPara, ptSysPara, &ctx) < 0)
            {
                return;
            }
        }
        else if (ctx.ParaLen <= MAX_STRING_PARA_SAVE_LEN)
        {
            //隐私数据在存储前进行加密处理
            if (SerializeStringPara(ptSavedPara, ptSysPara, &ctx) < 0)
            {
                return;
            }
        }
        else
        {
            if (SerializeAlarmPara(ptSavedPara, ptSysPara, &ctx) < 0)
            {
                return;
            }
        }
    }
    return;
}

/***************************************************************************
 * @brief    若ip、mask、gateway发生更改，则使其生效（无需重启）
 **************************************************************************/
#ifdef NET_PARA_SET
static BOOLEAN SetNet(struct netdev *pDev,ip_addr_t para , const char * ptPara)
{
    para.addr = netdev_ipaddr_addr(ptPara);
    if( para.addr != IPADDR_NONE)
    {   
        netdev_set_ipaddr(pDev,&para);
        return TRUE;
    }
    return FALSE;
}
static void DealNetParaChange(T_SysPara *ptSysPara)
{
#ifdef DEVICE_USING_R321
    #if !defined(KW_CHECK)
    ip_addr_t ipaddr, netmask, gw;
    struct netdev *pDev = netdev_get_by_name("e0");

    if (RT_NULL == pDev)
    {
        return;
    }

    if (s_tSysPara.ucLocalIPGetMode == DYNAMIC_IP_MODE && ptSysPara->ucLocalIPGetMode == STATIC_IP_MODE)
    {
        /* 动态ip->静态ip */
        netdev_dhcp_enabled(pDev, RT_FALSE);
        SetNet(pDev,ipaddr,(char *)ptSysPara->acLocalIPAddr);
        SetNet(pDev,netmask,(char *)ptSysPara->acMask);
        SetNet(pDev,gw,(char *)ptSysPara->acGateway);
        return;
    }
    else if (s_tSysPara.ucLocalIPGetMode == STATIC_IP_MODE && ptSysPara->ucLocalIPGetMode == DYNAMIC_IP_MODE)
    {
        /* 静态ip->动态ip */
        netdev_dhcp_enabled(pDev, RT_TRUE);
        return;
    }
    else if (s_tSysPara.ucLocalIPGetMode == DYNAMIC_IP_MODE && ptSysPara->ucLocalIPGetMode == DYNAMIC_IP_MODE)
    {
        /* 动态ip不变 */
        return;
    }
    else
    {
        if(0 != rt_memcmp(&s_tSysPara.acLocalIPAddr, &ptSysPara->acLocalIPAddr, sizeof(s_tSysPara.acLocalIPAddr)))
        {
            SetNet(pDev,ipaddr,(char *)ptSysPara->acLocalIPAddr);
        }

        if(0 != rt_memcmp(&s_tSysPara.acMask, &ptSysPara->acMask, sizeof(s_tSysPara.acMask)))
        {
            SetNet(pDev,netmask,(char *)ptSysPara->acMask);
        }

        if(0 != rt_memcmp(&s_tSysPara.acGateway, &ptSysPara->acGateway, sizeof(s_tSysPara.acGateway)))
        {
            SetNet(pDev,gw,(char *)ptSysPara->acGateway);
        }
    }
    #endif
#endif
}
#endif

// 设置参数时，为了保存参数修改记录，需知道设置前后的参数值
// 设置前的参数值，也是通过读文件，解析得到参数
// 特殊情况，可能未读到文件、文件不存在的情况，需要处理
static void PreDealSysParaFromSave(T_SysPara *ptSysPara, T_SysPara *ptSysParaFromSave, T_ChangeByEnum ChangeBy)
{
    int32_t lParaReadRtn;

    if (ChangeBy == CHANGE_BY_INIT) // 初始化时，不考虑参数联动处理
    {
        lParaReadRtn = readFile(FILE_NAME_PARA, (BYTE *)&s_tSavePara, sizeof(T_SavePara));

        if (IsSaveParaValid(&s_tSavePara, lParaReadRtn))
        {
            rt_memcpy_s((BYTE *)ptSysParaFromSave, sizeof(T_SysPara), (BYTE *)&s_tDefaultPara, sizeof(T_SysPara));
            ParseSavedPara(&s_tSavePara, lParaReadRtn, ptSysParaFromSave); // 读取到的内容即为设置前的值
        }
        else
        {
            LoadDefaultPara(ptSysParaFromSave); // 读取失败，设置前的值载入缺省值，此时没有参数修改记录
        }
    }
    else
    {
        ParseSavedPara(&s_tSavePara, sizeof(s_tSavePara), ptSysParaFromSave);
        PreDealLinkChange(ptSysPara, ptSysParaFromSave); // 处理参数联动
    }
    return;
}


BOOLEAN TrySetSysPara(T_SysPara *ptSysPara, BOOLEAN bChk, T_ChangeByEnum ChangeBy)
{
    BOOLEAN bChkResult = False;

    if (GPIO_OFF == GetPMOSStatus())
    {
        return False;
    }

    if (ptSysPara == NULL)
    {
        return False;
    }

    rt_memset_s(&s_tSysParaFromSave, sizeof(T_SysPara), 0, sizeof(T_SysPara));

    PreDealSysParaFromSave(ptSysPara, &s_tSysParaFromSave, ChangeBy);

    if (True == bChk)
    {
        bChkResult = ChkSysPara(ptSysPara);
    }
    else
    {
        bChkResult = True;
    }

    if (DealTheftAlarmOnParaChange(ptSysPara) == True)
    {
        bChkResult = False;
    }

    return bChkResult;
}



BOOLEAN ExecuteSaveSysPara(T_SysPara *ptSysPara, BOOLEAN bChk, T_ChangeByEnum ChangeBy)
{
    BOOLEAN rtn = False;

    if (ptSysPara == NULL)
    {
        return False;
    }

#ifdef NET_PARA_SET
    DealNetParaChange(ptSysPara);
#endif

    // 参数保存到s_tSysPara
    rt_memcpy((BYTE *)&s_tSysPara, (BYTE *)ptSysPara, sizeof(T_SysPara));
    rtn = SaveParaAction(&s_tSysPara, &s_tSysParaFromSave, ChangeBy);

    if ((rtn == True) || (ChangeBy == CHANGE_BY_INIT))
    {
        // 双区备份，写入前参数的预处理
        PreWrPara();

        writeFile(FILE_NAME_PARA, (BYTE *)&s_tSavePara, sizeof(T_SavePara));
        writeFile(FILE_NAME_BACKUP_PARA, (BYTE *)&s_tSaveBackupPara, sizeof(T_SavePara));
    }

    return rtn;
}


/****************************************************************
//  函数名称    ：SetSysPara
//  输入参数    ：ptSysPara -- 指向待保存参数的指针
                  bChk -- 是否需要校验
//  返 回 值    ：TRUE -- 待保存参数范围校验正确
                  False -- 待保存参数范围校验错误
//  功能描述    ：如果需要校验，进行校验，校验不成功，返回FALSE；
                  如果校验成功，把参数保存到s_tSysPara，并且保存
                  到EEPROM。
// 作    者 ：王威
// 版本信息：V1.0
// 设计日期：2010-06-13
// 修改记录：
// 日    期     版  本      修改人      修改摘要
// 其他说明：
*****************************************************************/

BOOLEAN SetSysPara(T_SysPara *ptSysPara, BOOLEAN bChk, T_ChangeByEnum ChangeBy)
{
    // SetSysPara 是一个使用频率高的接口，这里使用静态局部变量存储，避免多线程访问

    BOOLEAN bChkResult = False;

    bChkResult = TrySetSysPara(ptSysPara, bChk, ChangeBy);
    if (True == bChkResult)
    {
        ExecuteSaveSysPara(ptSysPara, bChk, ChangeBy);
    }

    return bChkResult;
}


/***************************************************************************
 * @brief    当电池丢失告警产生时，部分情况下防盗相关参数不允许修改
 * @param    {T_SysPara} *ptSysPara
 * @return   True-不允许修改    False-允许修改
 **************************************************************************/
Static BOOLEAN DealTheftAlarmOnParaChange(T_SysPara *ptSysPara)
{
    T_BCMAlarmStruct tBCMAlm = {0,};
    GetRealAlarm(BCM_ALARM, (BYTE *)&tBCMAlm);

    if (IsExistLostAlarm(&tBCMAlm) == True) // 产生电池丢失告警，需判断以下
    {
        if (DealDeviceTheftAlarm(ptSysPara) == True)
        {
            return True;
        }

#ifdef DEVICE_USING_R321
        // 陀螺仪灵敏度由非屏蔽设置为屏蔽
        if (s_tSysPara.bGyroscopeSensitivity != 0 && ptSysPara->bGyroscopeSensitivity == 0)
        {
            return True;
        }
#else
        // 陀螺仪倾角由非屏蔽设置为屏蔽
        if (s_tSysPara.ucGyroAngle != 0 && ptSysPara->ucGyroAngle == 0)
        {
            return True;
        }
#endif

#ifdef SITE_ANTI_THEFT_ENABLED
        if (DealSiteTheftAlarm(ptSysPara) == True)
        {
            return True;
        }
#endif
    }

#ifdef SITE_ANTI_THEFT_ENABLED
    if (GetSiteAntiTheftStatus() == True) // 站点防盗已开启
    {
        if (DealSiteTheftAlarm(ptSysPara) == True)
        {
            return True;
        }
    }
#endif

#ifdef NET_ANTI_THEFT_ENABLED
    if (DealNetTheftAlarmPara(ptSysPara) == True)
    {
        return True;
    }
#endif

    return False;
}

/***************************************************************************
 * @brief    检查设备防盗参数有无修改
 **************************************************************************/
Static BOOLEAN DealDeviceTheftAlarm(T_SysPara *ptSysPara)
{
    // 电池丢失告警级别由非屏蔽设置为屏蔽
    if (s_tSysPara.aucAlarmLevel[ALM_SN_BATT_LOSE_ALM] != 0 && ptSysPara->aucAlarmLevel[ALM_SN_BATT_LOSE_ALM] == 0)
    {
        return True;
    }

    // 电池解锁方式由人工解锁改为通信解锁
    if (s_tSysPara.ucBattUnlockMode == 1 && ptSysPara->ucBattUnlockMode == 0)
    {
        return True;
    }

    // 软件防盗延时由非屏蔽设置为屏蔽
    if (s_tSysPara.wSoftAntiTheftDelay != 0 && ptSysPara->wSoftAntiTheftDelay == 0)
    {
        return True;
    }
    return False;
}

/***************************************************************************
 * @brief    检查站点防盗参数有无修改
 **************************************************************************/
#ifdef SITE_ANTI_THEFT_ENABLED
Static BOOLEAN DealSiteTheftAlarm(T_SysPara *ptSysPara)
{
    // CSU防盗延时由非屏蔽设置为屏蔽
    if (s_tSysPara.wSiteAntiTheftDelayTime != 0 && ptSysPara->wSiteAntiTheftDelayTime == 0)
    {
        return True;
    }

    // 站点防盗告警级别由非屏蔽设置为屏蔽
    if (s_tSysPara.aucAlarmLevel[ALM_SN_SITE_ANTITHEFT_ALM] != 0 && ptSysPara->aucAlarmLevel[ALM_SN_SITE_ANTITHEFT_ALM] == 0)
    {
        return True;
    }
    return False;
}
#endif

/***************************************************************************
 * @brief    检查网管防盗参数有无修改
 **************************************************************************/
#ifdef NET_ANTI_THEFT_ENABLED
Static BOOLEAN DealNetTheftAlarmPara(T_SysPara *ptSysPara)
{
    if (GetNetAntiTheftStatus() == True)
    {
        // CSU防盗延时由非屏蔽设置为屏蔽
        if (s_tSysPara.wNetAntiTheftDelayTime != 0 && ptSysPara->wNetAntiTheftDelayTime == 0)
        {
            return True;
        }

        // 站点防盗告警级别由非屏蔽设置为屏蔽
        if (s_tSysPara.aucAlarmLevel[ALM_SN_NET_ANTITHEFT_ALM] != 0 && ptSysPara->aucAlarmLevel[ALM_SN_NET_ANTITHEFT_ALM] == 0)
        {
            return True;
        }
    }
    return False;
}
#endif

/****************************************************************
//      函数名          ：LoadPara
//      入口参数        ：参数恢复类型:
恢复运行参数
恢复配置参数
恢复所有参数
//      出口参数        ：无
//      功能            ：恢复运行参数或恢复所有参数话框( WND_RESUME 回调函数调用到)
***************************************************************/
void LoadPara(void)
{
    static T_SysPara tSysPara;

    LoadDefaultPara(&tSysPara);
    #ifdef INTELLIGENT_PEAK_SHIFTING
    RestorePeakShiftPara();
    #endif

    if (s_tSysPara.wBatteryCap != s_tDefaultPara.wBatteryCap)
    {
        SetDefaultParaByBdu(&tSysPara, s_tSysPara.wBatteryCap);
    }

    DealParaNotDefault(&tSysPara);
    
    SetSysPara(&tSysPara, False, CHANGE_BY_LOAD);

    return;
}

static WORD FindDiff(BYTE *p1, BYTE *p2, WORD wLen)
{
    WORD index = 0;

    if (NULL == p1 || NULL == p2)
    {
        return DIFF_INVALID;
    }

    for (index = 0; index < wLen; index++)
    {
        if (p1[index] != p2[index])
        {
            return index;
        }
    }

    return DIFF_INVALID;
}

// TODO: 参数和逻辑冗余，待优化
static void HandleParaParaWei0(T_SysPara* ptNewPara, T_SysPara* ptOldPara, char* buff, BYTE bSize, BYTE i, WORD wOffset, WORD* ptTmp) {
    BYTE    *ptNew =(BYTE*)ptNewPara;
    BYTE    *ptOld = (BYTE*)ptOldPara;
    FLOAT_u floatData;
    FLOAT_u floatData_old;

    for (BYTE j = 0; j < 4; j++)
    {
        floatData.ucData[j] = ptNew[wOffset + j];
        floatData_old.ucData[j] = ptOld[wOffset + j];
    }

    if (1 == g_aucParaSpace[i])
    {
        rt_snprintf(buff, bSize, "%d->%d", *(ptOld + wOffset), *(ptNew + wOffset));
    }
    else if (2 == g_aucParaSpace[i])
    {
        rt_snprintf(buff, bSize, "%d->%d", *(WORD *)(ptOld + wOffset), *(WORD *)(ptNew + wOffset));
    }
    else if (PARA_ID_ENABLE_DATE == i)//启用日期
    {
        WORD wOldYear = *(WORD *)(ptOld + wOffset);
        BYTE oldMonth = *(BYTE*)(ptOld + wOffset + 2);
        BYTE oldDay = *(BYTE*)(ptOld + wOffset + 3);
        WORD wNewYear = *(WORD *)(ptNew + wOffset);
        BYTE newMonth = *(BYTE*)(ptNew + wOffset + 2);
        BYTE newDay = *(BYTE*)(ptNew + wOffset + 3);
        rt_snprintf(buff, bSize, "%d%02d%02d->%d%02d%02d", wOldYear, oldMonth, oldDay, wNewYear, newMonth, newDay);
    }
    else if (4 == g_aucParaSpace[i])
    {
        #ifdef DEVICE_USING_D121
        if (PARA_ID_POWER_OFF_TIME_PRT_THRE == i) {
            rt_snprintf(buff, bSize, "%u->%u", *(ULONG *)(ptOld + wOffset), *(ULONG *)(ptNew + wOffset));
        } 
        else
        #endif 
        {
            rt_snprintf(buff, bSize, "%.0f->%.0f", floatData_old.fData, floatData.fData);
        }          
    }
    else if (PARA_ID_RELAY == i || PARA_ID_ALARM_CLASS == i)
    {
        *ptTmp = FindDiff(ptNew + wOffset, ptOld + wOffset, g_aucParaSpace[i]);
        rt_snprintf(buff, bSize, "%d->%d", ptOld[wOffset + *ptTmp], ptNew[wOffset + *ptTmp]);
    }
    else
    {
        rt_memcpy(buff, (ptNew + wOffset), min(g_aucParaSpace[i], bSize));//新值
        buff[20] = 0;
        #ifdef DEVICE_USING_R321
        if ((i >= PARA_ID_ALM_PHONE_NUM_1 && i <= PARA_ID_ALM_PHONE_NUM_5) || i == PARA_ID_SNMP_AUTH_PASS || i == PARA_ID_SNMP_PRIV_PASS)
        {
            rt_memcpy(buff, "**********", 10);
        }
        #endif
    }
    return;
    
}
/***************************************************************************
 * @brief    ExtractParaInfo 提取告警级别、干接点操作信息
 * @param    wAlarmParaIndex:告警级别、干接点操作记录序号
 *           ptBuff:操作信息
 *           ucBuffLen:ptBuff长度
 *           ucIndex:PARA_NUM
 *           ptActID:操作记录ID
 * @return   TRUE,FALSE
 **************************************************************************/
static BOOLEAN ExtractParaInfo(WORD wAlarmParaIndex, CHAR *ptBuff, BYTE ucBuffLen, BYTE ucIndex, BYTE *ptActID)
{
    CHAR tempBuff[21] = {0};
    CHAR addbuff[21] = {0};
    UINT32 uCopySize = 0;

    if (NULL == ptBuff || NULL == ptActID)
    {
        return FALSE;
    }

    if (ucIndex > PARA_ID_ALARM_CLASS)
    {
        *ptActID = ucIndex + (NEW_PARA_OFFSET - 1);
    }
    else if (PARA_ID_RELAY == ucIndex || PARA_ID_ALARM_CLASS == ucIndex)
    {
        /* 导出操作记录中增加告警级别、干接点序号, ucTmp+1:告警操作序号 */
        if (wAlarmParaIndex < ALARM_CLASS)
        {
            uCopySize = (ucBuffLen <= sizeof(tempBuff)) ? ucBuffLen : sizeof(tempBuff);
            rt_memcpy(tempBuff, ptBuff, uCopySize);

            rt_snprintf(addbuff, sizeof(addbuff), "ID:%02d  %s", (wAlarmParaIndex + 1), tempBuff);

            uCopySize = (sizeof(addbuff) <= ucBuffLen) ? sizeof(addbuff) : ucBuffLen;
            rt_memcpy(ptBuff, addbuff, uCopySize);

            *ptActID = ucIndex;
        }
        else
        {
            return FALSE;
        }
    }
    else
    {
        *ptActID = ucIndex;
    }

    return TRUE;
}


#ifdef PARA_SAVE_DEBUG
static void ParaSaveDebug(char* buff, BYTE bSize, T_ChangeByEnum ChangeBy)
{
    rt_memmove(buff+2, buff, bSize-2);
    buff[1] = '_';
    if(CHANGE_BY_YD1363 == ChangeBy)
    {
        buff[0] = 'Y';
    }
    else if(CHANGE_BY_MDBUS == ChangeBy)
    {
        buff[0] = 'M';
    }
    else if(CHANGE_BY_CAN == ChangeBy)
    {
        buff[0] = 'C';
    }
    else if(CHANGE_BY_INIT == ChangeBy)
    {
        buff[0] = 'I';
    }
    else if(CHANGE_BY_LOAD == ChangeBy)
    {
        buff[0] = 'L';
    }
    else if(CHANGE_BY_APPTEST == ChangeBy)
    {
        buff[0] = 'A';
    }
    else
    {
        buff[0] = 'U';
    }
}
#endif

static BOOLEAN PreDealSaveParaAction(T_SysPara* ptNewPara, T_SysPara* ptOldPara, char* buff, BYTE bSize, BYTE i, WORD wOffset, WORD* ptTmp)
{
    BYTE *ptNew = (BYTE *)ptNewPara;
    BYTE *ptOld = (BYTE *)ptOldPara;
    FLOAT_u floatData;
    FLOAT_u floatData_old;

    floatData.ucData[0] = ptNew[wOffset];
    floatData.ucData[1] = ptNew[wOffset + 1];
    floatData.ucData[2] = ptNew[wOffset + 2];
    floatData.ucData[3] = ptNew[wOffset + 3];
    floatData_old.ucData[0] = ptOld[wOffset];
    floatData_old.ucData[1] = ptOld[wOffset + 1];
    floatData_old.ucData[2] = ptOld[wOffset + 2];
    floatData_old.ucData[3] = ptOld[wOffset + 3];

    if (PARA_ID_BATT_OVER_VOLT_PRT == i || PARA_ID_BATT_UNDER_VOLT_ALM == i)
    {
        if (fabs(floatData_old.fData - floatData.fData) < 0.001f)//电池电压为2位精度
        {
            return False;
        }
    }
    if (0 == g_aucParaWei[i])//精度
    {
        HandleParaParaWei0(ptNewPara, ptOldPara, buff, bSize, i, wOffset, ptTmp);
    }
    else if (1==  g_aucParaWei[i])
    {
        rt_snprintf(buff, bSize, "%.1f->%.1f", floatData_old.fData, floatData.fData);
    }
    else if (2 == g_aucParaWei[i])
    {
        rt_snprintf(buff, bSize, "%.2f->%.2f", floatData_old.fData, floatData.fData);
    }
    else
    {
        rt_snprintf(buff, bSize, "%.3f->%.3f", floatData_old.fData, floatData.fData);
    }

    return True;
}

static BOOLEAN SaveParaAction(T_SysPara* ptNewPara, T_SysPara* ptOldPara, T_ChangeByEnum ChangeBy)
{
    BYTE    i = 0;
    WORD    ucTmp = 0;
    WORD    wOffset = 0;
    BYTE    *ptNew =(BYTE*)ptNewPara;
    BYTE    *ptOld = (BYTE*)ptOldPara;
    char    buff[21] = {0};
    BOOLEAN bRet = FALSE;
    UNUSED BYTE    ucActID = 0; // TODO: 仅R321使用，后期应分离出去
    UNUSED U_Int   wActID;      // TODO: 仅D121使用，后期应分离出去

    if (NULL == ptNewPara || NULL == ptOldPara)
    {
        return FALSE;
    }

    for (i = 0; i < PARA_NUM; i++)
    {
        if (rt_memcmp((BYTE *)(ptNew + wOffset), (BYTE *)(ptOld + wOffset), g_aucParaSpace[i]))
        {
            if (PreDealSaveParaAction(ptNewPara, ptOldPara, buff, sizeof(buff), i, wOffset, &ucTmp) == True)
            {
#ifdef PARA_SAVE_DEBUG
                ParaSaveDebug(buff, sizeof(buff), ChangeBy);
#endif
#ifdef DEVICE_USING_D121
                wActID.ucByte[1] = g_aucParaCID2[i];
                if (PARA_ID_RELAY == i || PARA_ID_ALARM_CLASS == i)
                {
                    if (ucTmp < sizeof(g_aucAlarmHistoryID))
                    {
                        wActID.ucByte[0] = g_aucAlarmHistoryID[ucTmp];
                    }
                }
                else
                {
                    wActID.ucByte[0] = g_aucParaCommandType[i];
                }
                SaveAction(wActID.iData, buff);
#else
                /*//修正参数ID,当ID>74时，为新增参数，需要修正到128后面
                if(i > PARA_ID_ALARM_CLASS)
                {
                    ucActID = i+NEW_PARA_OFFSET-1;
                }*/
                if (PARA_ID_RELAY == i || PARA_ID_ALARM_CLASS == i)
                {
                    for(BYTE j = 0; j < ALARM_CLASS; j++)
                    {
                        if(ptNew[wOffset+j] != ptOld[wOffset+j])
                        {
                            rt_snprintf(buff, sizeof(buff), "%d->%d", ptOld[wOffset + j], ptNew[wOffset + j]);
                            #ifdef PARA_SAVE_DEBUG
                            ParaSaveDebug(buff, sizeof(buff), ChangeBy);
                            #endif
                            ExtractParaInfo(j, buff, sizeof(buff), i, &ucActID);
                            SaveAction(ucActID, buff);
                        }
                    }
                }
                else
                {
                    ExtractParaInfo(ucTmp, buff, sizeof(buff), i, &ucActID);
                    SaveAction(ucActID, buff);
                }
#endif
                bRet = True;
            }
        }

        wOffset += g_aucParaSpace[i];
    }

    return bRet;
}


static BOOLEAN CheckIpAddr(char *p)
{
    int aucIp[4] = {0, 0, 0, 0};
    BYTE i, ucPointNum = 0;
    WORD wCounter = 0;

    if (p == NULL)
    {
        return False;
    }
    
    wCounter = rt_strnlen(p, LEN_TYPE_STRING_16);
    
    if (wCounter == 16)
    {
        p[wCounter - 1] = 0;
    }
    else
    {
        p[wCounter] = 0;
    }

    if ((rt_sscanf_s(p, "%d.%d.%d.%d", &aucIp[0], &aucIp[1], &aucIp[2], &aucIp[3]) != 4) || (aucIp[0] >= 224))
    {
        return False;
    }

    while (*p)
    {
        if (*p == '.')
        {
            ucPointNum++; //判断点的个数
        }
        p++;
    }
    if (ucPointNum != 3)
        return False;

    for (i = 0; i < 4; i++)
    {
        if (aucIp[i] > 255 || aucIp[i] < 0)
        {
            return False;
        }
    }
    return True;
}

static BOOLEAN DealParaNotDefault(T_SysPara *ptSysPara)
{
        // 防盗类参数不恢复
    ptSysPara->aucAlarmLevel[BATT_LOSE_ALM_INDEX] = s_tSysPara.aucAlarmLevel[BATT_LOSE_ALM_INDEX];     // 电池丢失告警级别
    ptSysPara->aucRelayBit[BATT_LOSE_ALM_INDEX] = s_tSysPara.aucRelayBit[BATT_LOSE_ALM_INDEX];         // 电池丢失告警干接点
    ptSysPara->ucGyroAntiTheftMode = s_tSysPara.ucGyroAntiTheftMode;     // 陀螺仪防盗方式
    ptSysPara->ucBattUnlockMode = s_tSysPara.ucBattUnlockMode;           // 电池解锁方式
    ptSysPara->wSoftAntiTheftDelay = s_tSysPara.wSoftAntiTheftDelay;     // 软件防盗延迟
#ifdef DEVICE_USING_R321
    ptSysPara->wGPSAntiTheftDistance = s_tSysPara.wGPSAntiTheftDistance; // GPS防盗距离
    ptSysPara->bVibrationAlarmEn = s_tSysPara.bVibrationAlarmEn;         // 振动告警使能
    ptSysPara->bGyroscopeSensitivity = s_tSysPara.bGyroscopeSensitivity; // 陀螺仪灵敏度
    ptSysPara->ucGyroAntiTheftTime = s_tSysPara.ucGyroAntiTheftTime;     // 陀螺仪防盗延时
#else
    ptSysPara->ucGyroAngle = s_tSysPara.ucGyroAngle;                     // 陀螺仪倾角
#endif

    // 其他重要参数
    ptSysPara->wBatteryCap = s_tSysPara.wBatteryCap;                                                   // 电池容量
    rt_memcpy((BYTE *)&ptSysPara->acDeviceName, (BYTE *)&s_tSysPara.acDeviceName, LEN_TYPE_STRING_32); // 设备名称
    rt_memcpy((BYTE *)&ptSysPara->acBMSSysName, (BYTE *)&s_tSysPara.acBMSSysName, LEN_TYPE_STRING_20); // BMS系统名称
    ptSysPara->tEnableTime = s_tSysPara.tEnableTime;                                                   // 启用日期
    // ptSysPara->fCellChargeFullVolt = s_tSysPara.fCellChargeFullVolt; // 单体充电截止电压
    ptSysPara->ucBattAddressMode = s_tSysPara.ucBattAddressMode;  // 电池地址获取方式
    ptSysPara->ucBattSwitchAddr = s_tSysPara.ucBattSwitchAddr;    // 电池切换地址
    ptSysPara->wDischgSwitchSOC = s_tSysPara.wDischgSwitchSOC;    // 放电末期切换SOC1
    ptSysPara->wDischgSwitchSOC2 = s_tSysPara.wDischgSwitchSOC2;  // 放电末期切换SOC2

#ifdef DEVICE_USING_R321
    ptSysPara->ucCAN2AddressMode = s_tSysPara.ucCAN2AddressMode;  // 柜间地址获取方式
    ptSysPara->ucCAN2SwitchAddr = s_tSysPara.ucCAN2SwitchAddr;    // 柜间切换地址

    // 手机号
    rt_memcpy((BYTE *)&ptSysPara->acAlmPhoneNum_1, (BYTE *)&s_tSysPara.acAlmPhoneNum_1, LEN_TYPE_STRING_20); // 告警手机号码-1
    rt_memcpy((BYTE *)&ptSysPara->acAlmPhoneNum_2, (BYTE *)&s_tSysPara.acAlmPhoneNum_2, LEN_TYPE_STRING_20); // 告警手机号码-2
    rt_memcpy((BYTE *)&ptSysPara->acAlmPhoneNum_3, (BYTE *)&s_tSysPara.acAlmPhoneNum_3, LEN_TYPE_STRING_20); // 告警手机号码-3
    rt_memcpy((BYTE *)&ptSysPara->acAlmPhoneNum_4, (BYTE *)&s_tSysPara.acAlmPhoneNum_4, LEN_TYPE_STRING_20); // 告警手机号码-4
    rt_memcpy((BYTE *)&ptSysPara->acAlmPhoneNum_5, (BYTE *)&s_tSysPara.acAlmPhoneNum_5, LEN_TYPE_STRING_20); // 告警手机号码-5                                                          // 告警手机号码-5
    rt_memcpy((BYTE *)&ptSysPara->acGPGSAPN, (BYTE *)&s_tSysPara.acGPGSAPN, LEN_TYPE_STRING_20); // GPRS APN

    // 网络参数
    rt_memcpy((BYTE *)&ptSysPara->acBackstageIpAddr, (BYTE *)&s_tSysPara.acBackstageIpAddr, LEN_TYPE_STRING_16);     // 后台ip地址
    ptSysPara->wBackstagePort = s_tSysPara.wBackstagePort; // 后台端口
    rt_memcpy((BYTE *)&ptSysPara->acSNMPTrapIP, (BYTE *)&s_tSysPara.acSNMPTrapIP, LEN_TYPE_STRING_16);               // SNMP告警ip地址
    rt_memcpy((BYTE *)&ptSysPara->acSNMPReadCommunity, (BYTE *)&s_tSysPara.acSNMPReadCommunity, LEN_TYPE_STRING_16); // SNMP可读共同体
    rt_memcpy((BYTE *)&ptSysPara->acSNMPSetCommunity, (BYTE *)&s_tSysPara.acSNMPSetCommunity, LEN_TYPE_STRING_16);   // SNMP设置共同体
    ptSysPara->ucSNMPV3UserLevel = s_tSysPara.ucSNMPV3UserLevel; // SNMP V3用户等级
    rt_memcpy((BYTE *)&ptSysPara->acSNMPV3UserName, (BYTE *)&s_tSysPara.acSNMPV3UserName, LEN_TYPE_STRING_16); // SNMP V3用户名
    rt_memcpy((BYTE *)&ptSysPara->acSNMPV3AuthPass, (BYTE *)&s_tSysPara.acSNMPV3AuthPass, LEN_TYPE_STRING_16); // SNMP V3鉴别密码
    rt_memcpy((BYTE *)&ptSysPara->acSNMPV3PrivPass, (BYTE *)&s_tSysPara.acSNMPV3PrivPass, LEN_TYPE_STRING_16); // SNMP V3加密密码
    ptSysPara->wSNMPTrapPort = s_tSysPara.wSNMPTrapPort;   // SNMP Trap端口号
    ptSysPara->wSNMPAgentPort = s_tSysPara.wSNMPAgentPort; // SNMP Agent端口号
    rt_memcpy((BYTE *)&ptSysPara->acLocalIPAddr, (BYTE *)&s_tSysPara.acLocalIPAddr, LEN_TYPE_STRING_16); // 本机ip地址
    rt_memcpy((BYTE *)&ptSysPara->acMask, (BYTE *)&s_tSysPara.acMask, LEN_TYPE_STRING_16);               // 子网掩码
    rt_memcpy((BYTE *)&ptSysPara->acGateway, (BYTE *)&s_tSysPara.acGateway, LEN_TYPE_STRING_16);         // 网关
    ptSysPara->ucLocalIPGetMode = s_tSysPara.ucLocalIPGetMode; // 本机ip获取方式
#endif

    // B3参数
    ptSysPara->fChargeMaxCurr      = s_tSysPara.fChargeMaxCurr;         //充电最大电流与容量联动，不恢复
    ptSysPara->ucBattUnlockMode     = s_tSysPara.ucBattUnlockMode;      //电池解锁方式
    ptSysPara->ucUsageScen         = s_tSysPara.ucUsageScen;            // 放电模式
	ptSysPara->fRemoteSupplyOutVolt = s_tSysPara.fRemoteSupplyOutVolt;  // 远供输出电压
	ptSysPara->fDischgMaxCurr      = s_tSysPara.fDischgMaxCurr;         //放电最大电流与容量联动，不恢复

#ifdef DEVICE_USING_R321
    rt_memcpy((BYTE *)&ptSysPara->acGPGSUserName,(BYTE *)&s_tSysPara.acGPGSUserName,LEN_TYPE_STRING_20); // GPRS用户名
    rt_memcpy((BYTE *)&ptSysPara->acGPGSPazwerd,(BYTE *)&s_tSysPara.acGPGSPazwerd,LEN_TYPE_STRING_10); // GPRS密码
    rt_memcpy((BYTE *)&ptSysPara->acSMSCenterNum,(BYTE *)&s_tSysPara.acSMSCenterNum,LEN_TYPE_STRING_20); // 短信中心号码
    ptSysPara->ucRunMode           = s_tSysPara.ucRunMode;                //运行模式
    ptSysPara->ucCycleMode         = s_tSysPara.ucCycleMode;              //循环模式
    ptSysPara->fLABattSwitchVolt   = s_tSysPara.fLABattSwitchVolt;        // 铅酸切换电压
    ptSysPara->bMajorBatt          = s_tSysPara.bMajorBatt;               // 主用电池
    ptSysPara->fPowerDownVoltThre   = s_tSysPara.fPowerDownVoltThre;   // 掉电电压阈值
    ptSysPara->fPowerOnVoltThre  = s_tSysPara.fPowerOnVoltThre;        // 系统来电电压阈值
    ptSysPara->fPowerOffVoltThre = s_tSysPara.fPowerOffVoltThre;       // 系统停电电压阈值
    ptSysPara->ucDischargeMode = s_tSysPara.ucDischargeMode;           // 放电方式
    ptSysPara->ucHisDataType = s_tSysPara.ucHisDataType;               // 历史数据类型
    ptSysPara->ucModbusAddr = s_tSysPara.ucModbusAddr;                 // Modbus基地址
    ptSysPara->ucLocateMode = s_tSysPara.ucLocateMode;                 // 定位方式
    ptSysPara->ucTimeZone = s_tSysPara.ucTimeZone;                     // 时区
#endif
    return True;
}

void WriteBMSDefaultPara(T_SysPara *ptSysPara)
{
    WORD wSaveIDNum;

    // WriteBMSDefaultPara函数仅在工装测试时使用，多线程同时访问s_tSaveDefaultPara风险较低
    rt_memset(&s_tSaveDefaultPara, 0, sizeof(s_tSaveDefaultPara));

    wSaveIDNum = sizeof(s_awParaID) / sizeof(s_awParaID[0]);
    WriteSavedPara(ptSysPara, &s_tSaveDefaultPara, s_awParaID, wSaveIDNum);
    CalcSaveParaCrc(&s_tSaveDefaultPara);
    writeFile(FILE_NAME_DEFAULT_PARA, (BYTE *)&s_tSaveDefaultPara, sizeof(T_SavePara));

    return;
}

/***************************************************************************
 * @brief    获取BMS系统名称
 * @param    {CHAR} *pacSysName-保存BMS系统名称的数组指针
 * @param    {BYTE} NameSize-保存BMS系统名称的数组长度
 * @return   {*}
 **************************************************************************/
void GetBmsSysName(CHAR *pacSysName, BYTE NameSize)
{
    if (NULL == pacSysName)
        return;

    GetSysPara(&s_tSysPara);
    rt_memcpy_s(pacSysName, NameSize, (CHAR *)s_tSysPara.acBMSSysName, sizeof(s_tSysPara.acBMSSysName));

    return;
}

static BOOLEAN CheckIpMask(char *p)
{
    U_32Int aucIp ;
    BOOLEAN bZero=FALSE;
    int i, ucPointNum = 0;
    WORD wCounter = 0;
    aucIp.LData = 0;
    int aucIpTemp[4] = {0, 0, 0, 0};

    RETURN_VAL_IF_FAIL(p != NULL, False);

    wCounter = rt_strnlen(p, LEN_TYPE_STRING_16);

    if (wCounter == 16)
    {
        p[wCounter - 1] = 0;
    }
    else
    {
        p[wCounter] = 0;
    }


    if ((rt_sscanf_s(p, "%d.%d.%d.%d", &aucIpTemp[0], &aucIpTemp[1], &aucIpTemp[2],&aucIpTemp[3]) != 4))
    {
        return False;
    }
    
    while (*p)
    {
        if (*p == '.')
        {
            ucPointNum++; //判断点的个数
        }
        p++;
    }

    RETURN_VAL_IF_FAIL(ucPointNum == 3, False);

    for (i = 0; i < 4; i++)
    {
        if (aucIpTemp[i] > 255 || aucIpTemp[i] < 0)
        {
            return False;
        }
        aucIp.ucData[3-i] = (BYTE)aucIpTemp[i];
    }

    RETURN_VAL_IF_FAIL(aucIp.LData != 0xFFFFFFFF, False);

    for (i = 31; i >= 0; i--)
    {
        if(aucIp.LData&((uint32_t)1 <<i))
        {
            if( True == bZero )
            {
                return False;
            }
        }
        else
        {
            bZero = True;
        }
    }

    return True;
}

void SaveHisOpID(U_Int *uData, rt_uint16_t ID)
{
    uData->ucByte[0]=g_aucSpacialHistoryID[ID][1];
    uData->ucByte[1]=g_aucSpacialHistoryID[ID][0];
}

/**
 * @brief 通过ID获取参数
 * 
 * @param[in] id 参数ID
 * @param[out] dest 输出缓冲区，存储获取到的参数值
 * @param[in] len dest的长度
 * @param[in] converter 字节序转换处理函数，为NULL时不做字节序处理
 * @return int <=0, 获取失败; >0, 实际写入输出缓冲区的字节数
 */
int GetParaById(ParaId id, void *dest, size_t len, size_t (*converter)(ParaType type, void *value))
{
    ParaProperty property = {0, };

    if (dest == NULL)
    {
        return -1;
    }

    if (!GetParaPropertyById(id, &property))
    {
        return -1;
    }

    return GetParaByProperty(&property, dest, len, converter);
}

/**
 * @brief 通过ID设置参数
 * 
 * @param[in] id 参数ID
 * @param[in] src 输入缓冲区，存储待设置的参数值
 * @param[in] len src的长度
 * @param[in] converter 字节序转换处理函数，为NULL时不做字节序处理
 * @return int <=0, 设置失败; >0, 实际从输入缓冲区读取的字节数
 */
int SetParaById(ParaId id, const void *src, size_t len, size_t (*converter)(ParaType type, void *value))
{
    ParaProperty property = {0, };

    if (src == NULL)
    {
        return -1;
    }

    if (!GetParaPropertyById(id, &property))
    {
        return -1;
    }

    return SetParaByProperty(&property, src, len, converter);
}

/**
 * @brief 通过参数段（section）获取整段参数
 * 
 * @param[in] section 参数段
 * @param[out] dest 输出缓冲区，存储获取到的参数值
 * @param[in] len dest的长度
 * @param[in] converter 字节序转换处理函数，为NULL时不做字节序处理
 * @return int <=0, 获取失败; >0, 实际写入输出缓冲区的字节数
 */
int GetMultiParaBySection(uint8_t section, void *dest, size_t len, size_t (*converter)(ParaType type, void *value))
{
    ParaProperty property = {0, };
    ParaId id = 0;
    size_t copiedSize = 0;
    int ret = 0;

    if (dest == NULL)
    {
        return -1;
    }

    for (size_t i = 0; i < 0xFF; i++)
    {
        id = MakeParaId(section, i);
        if (!GetParaPropertyById(id, &property))
        {
            continue;
        }
        ret = GetParaByProperty(&property, dest + copiedSize, len - copiedSize, converter);
        if (ret < 0)
        {
            return ret;
        }
        copiedSize += ret;
    }
    return copiedSize;
}

/**
 * @brief 通过ID获取对应参数的属性
 * 
 * @param[in] id 参数ID
 * @param[out] property 参数属性
 * @return BOOLEAN 获取结果
 *
 * @note 该接口在不同产品上的实现应不同
 */
BOOLEAN GetParaPropertyById(ParaId id, ParaProperty *property)
{
    size_t offset = 0;

    if (property == NULL)
    {
        return False;
    }

    ssize_t pos = FindParaById(id);
    if (pos < 0)
    {
        return False;
    }

    if (GetParaSectionById(id) == PARA_SECTION_ALM_RELAY || GetParaSectionById(id) == PARA_SECTION_ALM_LEVEL)
    {
        property->elementNum = 1;
        property->elementType = aucVarType[pos];
        property->elementSize = 1;
        for (ssize_t i = 0; i < pos; i++)
        {
            offset += g_aucParaSpace[i];
        }
        property->offset = offset + GetParaIndexById(id) - PARA_ALM_START_INDEX;
    }
    else
    {
        property->elementType = aucVarType[pos];
        property->elementSize = s_aucParaTypeSizeMap[aucVarType[pos]];
        property->elementNum = g_aucParaSpace[pos] / s_aucParaTypeSizeMap[aucVarType[pos]];
        for (ssize_t i = 0; i < pos; i++)
        {
            offset += g_aucParaSpace[i];
        }
        property->offset = offset;
    }

    return True;
}

/**
 * @brief 通过参数ID获取内部索引号
 * 
 * @param id 参数ID
 * @return uint8_t 内部索引号
 */
uint8_t GetParaIndexById(ParaId id)
{
    return (uint8_t)(id & 0xFF);
}

/**
 * @brief 通过参数ID获取段号
 * 
 * @param id 参数ID
 * @return uint8_t 参数段号
 */
uint8_t GetParaSectionById(ParaId id)
{
    return (uint8_t)((id >> 8) & 0xFF);
}

/**
 * @brief 生成参数ID
 * 
 * @param section 内部索引号
 * @param index 参数段号
 * @return ParaId 参数ID
 */
ParaId MakeParaId(uint8_t section, uint8_t index)
{
    return (section << 8) | index;
}

/**
 * @brief 通过参数属性获取参数
 * 
 * @param[in] property 参数属性
 * @param[out] dest 输出缓冲区，存储获取到的参数
 * @param[in] len dest的长度
 * @param[in] converter 字节序转换处理函数，为NULL时不做字节序处理
 * @return ssize_t <=0, 获取失败; >0, 实际写入输出缓冲区的字节数
 */
static ssize_t GetParaByProperty(const ParaProperty *property, void *dest, size_t len, size_t (*converter)(ParaType type, void *value))
{
    ssize_t copiedSize = 0;
    ssize_t i = 0;

    RT_ASSERT(property != NULL && dest != NULL);

    if (property->elementSize * property->elementNum > len)
    {
        return -1;
    }

    GetSysPara(&s_tParaCache);
    for (i = 0; i < property->elementNum; i++)
    {
        rt_memcpy((uint8_t *)dest + copiedSize,
                  (uint8_t *)&s_tParaCache + property->offset + copiedSize,
                  property->elementSize);
        if (converter != NULL)
        {
            converter(property->elementType, dest + copiedSize);
        }
        copiedSize += property->elementSize;
    }

    return copiedSize;
}

/**
 * @brief 通过参数属性设置参数
 * 
 * @param[in] property 参数属性
 * @param[in] src 输入缓冲区，存储待设置的参数
 * @param[in] len src的长度
 * @param[in] converter 字节序转换处理函数，为NULL时不做字节序处理
 * @return ssize_t <=0, 获取失败; >0, 实际写入输出缓冲区的字节数
 */
static ssize_t SetParaByProperty(const ParaProperty *property, const void *src, size_t len, size_t (*converter)(ParaType type, void *value))
{
    ssize_t copiedSize = 0;

    RT_ASSERT(property != NULL && src != NULL);

    if (property->elementSize > len)
    {
        return -1;
    }

    GetSysPara(&s_tParaCache);
    for (size_t i = 0; i < property->elementNum; i++)
    {
        rt_memcpy((uint8_t *)&s_tParaCache + property->offset + copiedSize,
                  (uint8_t *)src + copiedSize,
                  property->elementSize);
        if (converter != NULL)
        {
            converter(property->elementType, (uint8_t *)&s_tParaCache + property->offset + copiedSize);
        }
        copiedSize += property->elementSize;
    }

    if (!SetSysPara(&s_tParaCache, True, CHANGE_BY_QTPTEST))
    {
        return -1;
    }

    return copiedSize;
}

/**
 * @brief 通过参数ID找到其在数组中的位置
 * 
 * @param id 参数ID
 * @return ssize_t <0, 未找到; >=0, 参数在数组中的位置
 */
ssize_t FindParaById(ParaId id)
{
    uint8_t section = GetParaSectionById(id);
    uint8_t index = GetParaIndexById(id);

    if (section == PARA_SECTION_ALM_RELAY || section == PARA_SECTION_ALM_LEVEL)
    {
        if (index < PARA_ALM_START_INDEX || index - PARA_ALM_START_INDEX >= ALARM_CLASS)
        {
            return -1;
        }
        for (ssize_t i = 0; i < ARR_SIZE(g_aucParaIdSection); i++)
        {
            if (section == g_aucParaIdSection[i])
            {
                return i;
            }
        }
        return -1;
    }

    for (ssize_t i = 0; i < ARR_SIZE(s_awParaID); i++)
    {
        if (index == s_awParaID[i] && section == g_aucParaIdSection[i])
        {
            return i;
        }
    }
    return -1;
}

/**
 * @brief 符合1363类协议的字节序转换
 * 
 * @param type 参数类型
 * @param value 参数
 * @return size_t 处理的字节数
 */
size_t EndianConvert(ParaType type, void *value)
{
    uint8_t len = 0;
    uint8_t i = 0;
    uint8_t tmp = 0;

    if (value == NULL)
    {
        return 0;
    }

    len = s_aucParaTypeSizeMap[type];

    if (type == PARA_TYPE_T_DateStruct)
    {
        tmp = *((uint8_t *)value);
        *((uint8_t *)value) = *((uint8_t *)value + 1);
        *((uint8_t *)value + 1) = tmp;
    }
    else if (type == PARA_TYPE_FLOAT)
    {
    }
    else
    {
        for (i = 0; i < len / 2; i++)
        {
            tmp = *((uint8_t *)value + i);
            *((uint8_t *)value + i) = *((uint8_t *)value + len - 1 - i);
            *((uint8_t *)value + len - 1 - i) = tmp;
        }
    }
    return len;
}

/**
 * @brief 判断参数类型是否合法
 * 
 * @param type 参数类型
 * @return BOOLEAN 判断结果
 */
BOOLEAN IsValidParaType(int type)
{
    return (type == PARA_TYPE_FLOAT) || (type == PARA_TYPE_WORD)
           || (type == PARA_TYPE_BOOLEAN || type == PARA_TYPE_BYTE)
           || (type == PARA_TYPE_ULONG) || (type == PARA_TYPE_T_DateStruct);
}

/**
 * @brief 获取默认定位方式
 *
 * @param
 * @return 默认定位方式
 */
#ifdef GPS_BEIDOU_SET
BYTE getDefLocateMode(void)
{
    return s_ucDefLocateMode;
}
#endif

/// @brief 将临时参数保存在s_tParaCache中，以便设置参数的时候使用
/// @param ptTempSyspara 待暂存的临时参数
/// @return
BOOLEAN SaveTempSysPara(T_SysPara *ptTempSyspara)
{
    rt_memcpy((BYTE *)&s_tParaCache, (BYTE *)ptTempSyspara, sizeof(T_SysPara));
    return TRUE;
}

/// @brief 获取暂存的系统参数
/// @param ptSyspara
/// @return
BOOLEAN GetTempSysPara(T_SysPara *ptSyspara)
{
    rt_memcpy((BYTE *)ptSyspara, (BYTE *)&s_tParaCache, sizeof(T_SysPara));
    return TRUE;
}

/// @brief 设置参数是否保存的标志
/// @param bParaSaveFlag ture表示校验通过，需要进行保存
/// @return TRUE
BOOLEAN SetParaSaveFlag(BOOLEAN bParaSaveFlag)
{
    s_bParaSaveFlag = bParaSaveFlag;
    return TRUE;
}

/// @brief 获取参数是否保存的标志
/// @param void
/// @return 参数保存标志
BOOLEAN GetParaSaveFlag(void)
{
    return s_bParaSaveFlag;
}