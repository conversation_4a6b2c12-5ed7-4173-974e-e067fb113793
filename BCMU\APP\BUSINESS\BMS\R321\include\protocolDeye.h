/**************************************************************************
 * 文件名称：protocolDeye.h
 * 文件说明：德业协议模块头文件（协议组包与协议判断等过程隔离）
 ***************************************************************************/

#ifndef SOFTWARE_SRC_APP_PROTOCOLDEYE_H_
#define SOFTWARE_SRC_APP_PROTOCOLDEYE_H_

#ifdef __cplusplus
extern "C"
{
#endif

#define BMS_FACTORY_NAME_DEYE "ZTE Corporation"  // 主机厂商名称
#define BMS_DEVICE_NAME_DEYE  "ZXESM R321"  // 主机厂商名称                          

/************************  返回码  ************************/
#define RTN_NORMAL         0x00      // 接收数据正确
#define RTN_VER_ERROR      0x01      // 版本号错
#define RTN_CHKSUM_ERROR   0x02      // 和校验错
#define RTN_LCHKSUM_ERROR  0x03      // 长度校验错
#define RTN_CID2_INVALID   0x04      // CID2错
#define RTN_COMMAND_ERROR  0x05      // 命令格式错
#define RTN_INVALID_DATA   0x06      // 无效数据
#define RTN_ADR_ERROR      0x90      // 地址错误
#define RTN_COMM_ERROR     0x91      // 内部通信错误

/************************   CID2码   ***********************/
#define GET_SYS_INFO_DEYE    0x60    //获取电池组系统基本信息
#define GET_ANALOG_DEYE      0x61    //获取电池组系统运行模拟量数据
#define GET_ALARM_DEYE       0x62    //获取电池组系统状态告警量数据
#define GET_BATT_MANAGE_DEYE 0x63    //获取电池组系统充放电管理交互信息
#define SET_SHUTDOWN_DEYE    0x64    //控制电池组系统关机指令

#define MAX_CHARGE_VOLT_DEYE (54.0f)
#define MIN_DISCHARGE_VOLT_DEYE (42.0f)
#define MAX_CHARGE_CURRENT_DEYE (100.0f)
#define MAX_DISCHARGE_CURRENT_DEYE (100.0f)

#ifdef __cplusplus
} /* end of the 'extern "C"' block */
#endif

#endif


