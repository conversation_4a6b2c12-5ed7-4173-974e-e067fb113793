#include "south_dev_common.h"
#include "dev_north_dxcb_modbus.h"
#include "utils_data_transmission.h"
#include "realdata_id_in.h"
#include "para_id_in.h"
#include "south_dev_modbus.h"

/* 命令请求头 */
static modbusrtu_cmd_head_t cmd_req[] = {
    {READ_HOLD_REG},     // 读取保持寄存器 (功能码 0x03)
    {READ_INPUT_REG},    // 读取输入寄存器 (功能码 0x04)
    {WRITE_SINGLE_REG},  // 写单个寄存器 (功能码 0x06)
    {WRITE_MULTI_REG},   // 写多个寄存器 (功能码 0x10)
};

/* 命令应答头 */
static modbusrtu_cmd_head_t cmd_ack[] = {
    {READ_HOLD_REG},     // 应答：读取保持寄存器
    {READ_INPUT_REG},    // 应答：读取输入寄存器
    {WRITE_SINGLE_REG},  // 应答：写单个寄存器
    {WRITE_MULTI_REG},   // 应答：写多个寄存器
};

//解析实时量寄存器地址：53264-53287
static data_info_id_verison_t parse_real_data_info[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},//数据的字节长度

    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_FAN_SPEED_PERCENT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_MOTOR_STATUS},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_TOTAL_ALARM},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_MODULE_TEMP},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_MOTOR_TEMP},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_ELECT_TEMP},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_POWER},
};

static data_info_id_verison_t parse_ebm_all_time_info[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},//数据的字节长度
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_FAN_ACCE_TIME},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_FAN_DECE_TIME},
};

static cmd_parse_info_id_verison_t cmd_parse_info[] RAM_SECTION =
{
    {&parse_real_data_info[0], sizeof(parse_real_data_info)/sizeof(data_info_id_verison_t)},
    {&parse_ebm_all_time_info[0], sizeof(parse_ebm_all_time_info)/sizeof(data_info_id_verison_t)},
};

/* 命令总表,必须以空字符串""结束 */
static cmd_t no_poll_cmd_tab[] = {
    {EBM_GET_ALL_TIME, CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[1]},
    {WRITE_EBM_SINGLE_REG, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, NULL, NULL,NULL,NULL},
    {WRITE_EBM_MULTI_REG, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, NULL, NULL,NULL,NULL},
    {EBM_SET_WIND_SPEED_CTRL, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data,NULL,NULL},
    {EBM_SET_ADDR, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data, NULL,NULL},
    {EBM_SET_ACCE_TIME, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data, NULL,NULL},
    {EBM_SET_DECE_TIME, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data, NULL,NULL},
    {EBM_SET_CODE1, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data, NULL,NULL},
    {EBM_SET_CODE2, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data, NULL,NULL},
    {EBM_SET_CODE3, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data, NULL,NULL},
    {EBM_SET_VALUE_SOURCE, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data, NULL,NULL},
    {EBM_SET_EMERGY_MODE, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data, NULL,NULL},
    {EBM_SET_RESET_EFFECT, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data, NULL,NULL},
    {EBM_SET_SAVE_SET_VALUE, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data, NULL,NULL},

    {EBM_SET_ALL_TIME, CMD_POSITIVE, &cmd_req[3], &cmd_ack[3], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data, NULL,NULL},
    {0},
};

static cmd_t poll_cmd_tab[] = {
    // {READ_EBM_HOLD_REG, CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[0]},
    {READ_EBM_INPUT_REG, CMD_POSITIVE, &cmd_req[1], &cmd_ack[1], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[0]},
    {0},
};

int update_fan_ebm_dev_type(dev_inst_t* dev_inst, char dev_num, char is_inter_fan)
{
    return change_dev_type_info_by_diff_brand(dev_inst, SOUTH_FAN_TYPE, is_inter_fan, dev_num, no_poll_cmd_tab, poll_cmd_tab);
}

