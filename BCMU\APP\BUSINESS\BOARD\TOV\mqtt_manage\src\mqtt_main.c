#include "data_type.h"
#include "utils_thread.h"
#include "utils_server.h"
#include "msg_id.h"
#include "utils_rtthread_security_func.h"
#include "partition_def.h"
#include "mqtt_main.h"
#include "sys/stat.h"
#include "mbedtls/platform.h"
#include "mbedtls/ssl.h"
#include "mbedtls/entropy.h"
#include "mbedtls/net_sockets.h"
#include "mbedtls/ctr_drbg.h"
#include "mbedtls/error.h"
#include "mbedtls/debug.h"
#include "mbedtls/x509_crt.h"
#include "mbedtls/pk.h"
#include "random.h"
#include "nettype_tls.h"
#include "webclient.h"
#include "para_manage.h"
#include "platform_memory.h"
#include "4g_mgr.h"
#include "realdata_id_in.h"
#include "parse_layer.h"
#include "his_record.h"
#include "storage.h"
#include "realdata_save.h"


char g_recv_buffer[BUFFER_SIZE] = {0};
char g_send_buffer[WEBCLIENT_RESPONSE_BUFSZ] = {0};
char g_file_url[BUFFER_SIZE] = {0};
char g_json_body[BUFFER_SIZE] = {0};
file_name_map_t g_file_name_map = {{RECORD_FAULT_FILE_0, RECORD_FAULT_FILE_1, RECORD_FAULT_FILE_2, RECORD_FAULT_FILE_3, EXTREME_DATA_FILE}, 5};
file_name_map_t g_rf_file_map = {{RECORD_FAULT_FILE_0, RECORD_FAULT_FILE_1, RECORD_FAULT_FILE_2, RECORD_FAULT_FILE_3}, 4};
static rt_timer_t g_mqtt_timer = NULL;
_rt_server_t g_tov_mqtt_server;
static unsigned int g_count = 0;
static msg_map mqtt_msg_map[] = 
{
    {MQTT_UPFILE_MSG,   msg_handle_nothing},
};

static mqtt_msg_handle_process_t s_msg_handle[] =
{
    {MQTT_UPFILE_MSG,      handle_upfile},
};

int is_file_exit(file_name_map_t* file_name_map , const char* file_name[])
{
    struct stat stat_file = {0};
    int file_nums = 0 ;

    for(int file_loop = 0 ; file_loop < file_name_map->num ; file_loop++ )
    {
        if (storage_stat(file_name_map->file_name[file_loop] , &stat_file) != SUCCESSFUL )
        {
            continue;
        }
        file_name[file_nums] = file_name_map->file_name[file_loop];
        file_nums++;
    }
    return file_nums;
}

void* init_mqtt(void* param)
{
    server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(mqtt_msg_map) / sizeof(msg_map);
    register_server_msg_map(mqtt_msg_map, server_info);
    set_mqtt_memory_mode(POOL_MEM_MODE);

    g_mqtt_timer = rt_timer_create("mqtt_timer", reset_4g_timeout, NULL, MQTT_PERIOD, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
    if(g_mqtt_timer == NULL)
    {
        LOG_E("mqtt_timer creat fail\n");
    }
    return NULL;
}



int log_in(char* host, unsigned short server_port, char* username, char* word)
{
    int ret = 0;
    nettype_tls_params_t nettype_tls_params = {0};
    str16 port = {0};

    if (nettype_tls_conf_no_verify_init(0) != SUCCESSFUL)
    {
        nettype_tls_conf_no_verify_init(0);
        return FAILURE;
    }

    rt_snprintf_s(port, STR_LEN_16, "%d", server_port);
    if(nettype_tls_connect(&nettype_tls_params, host, port, 0) != SUCCESSFUL)
    {
        nettype_tls_disconnect(&nettype_tls_params);
        return FAILURE;
    }

    rt_memset(g_json_body, 0x00, sizeof(g_json_body));
    rt_memset(g_recv_buffer, 0x00, sizeof(g_recv_buffer));
    // 构造 JSON 格式的请求体
    rt_snprintf_s(g_json_body, sizeof(g_json_body), "{\"device_flag\": 1, \"userId\": \"%s\", \"word\": \"%s\"}", username, word);
    
    // 构造 POST 请求
    char* url = "/server/api/v1/user/login/";
    rt_snprintf_s(g_recv_buffer, sizeof(g_recv_buffer), 
             "POST %s HTTP/1.1\r\n"
             "Host: %s:%s\r\n"
             "Content-Length: %lu\r\n"
             "Content-Type: application/json\r\n"
             "\r\n"
             "%s", 
             url,host, port, rt_strnlen_s(g_json_body, sizeof(g_json_body)), g_json_body);

    // 发送请求
    ret = mbedtls_ssl_write(&nettype_tls_params.ssl, (const unsigned char *)g_recv_buffer, rt_strnlen_s(g_recv_buffer, sizeof(g_recv_buffer)));
    if (ret < 0)
    {
        nettype_tls_disconnect(&nettype_tls_params);
        LOG_E("%s:%d|mbedtls_ssl_write returned %d", __FUNCTION__, __LINE__, ret);
        return FAILURE;
    }

    // 接收响应
    rt_memset_s(g_recv_buffer, BUFFER_SIZE, 0, BUFFER_SIZE);
    ret = mbedtls_ssl_read(&nettype_tls_params.ssl, (unsigned char *)g_recv_buffer, BUFFER_SIZE - 1);
    if (ret < 0)
    {
        nettype_tls_disconnect(&nettype_tls_params);
        LOG_E("%s:%d|bedtls_ssl_read returned %d", __FUNCTION__, __LINE__, ret);
        return FAILURE;
    }

    // 断开连接
    nettype_tls_disconnect(&nettype_tls_params);
    return SUCCESSFUL;
}

int get_cookie(char **cookie1, char **cookie2)
{
    // 获取cookie
    char *cookie1_start = NULL, *cookie2_start = NULL, *cookie1_end = NULL, *cookie2_end = NULL;
    cookie1_start = rt_strstr(g_recv_buffer, "Set-Cookie:");
    if (cookie1_start != NULL)
    {
        cookie1_start += 12; // 跳过 "Set-Cookie: "
        cookie1_end = rt_strstr(cookie1_start, ";");
        if (cookie1_end != NULL)
        {
            *cookie1_end = '\0';
            cookie2_start = rt_strstr(cookie1_end+1, "Set-Cookie:");

            if (cookie2_start != NULL)
            {
                cookie2_start += 12; // 跳过 "Set-Cookie: "
                cookie2_end = rt_strstr(cookie2_start, ";");
                if (cookie2_end != NULL)
                {
                    *cookie2_end = '\0';
                    *cookie1 = cookie1_start;
                    *cookie2 = cookie2_start;
                    return SUCCESSFUL;
                }
            }
        }
    }
    return FAILURE;
}

int get_token(char** token)
{
    char *token_start = NULL, *token_end = NULL;
    token_start = rt_strstr(g_recv_buffer, "\"token\": \"");
    if (token_start != NULL)
    {
        token_start += 10; // 跳过 "\"token\": \""
        token_end = rt_strstr(token_start, "\"");
        if (token_end != NULL)
        {
            *token_end = '\0';
            *token = token_start;
            return SUCCESSFUL;
        }
    }
    return FAILURE;
}



int upload_file(char* host, unsigned short server_port, char* username)
{
    int ret = FAILURE;
    static int log_in_fail_count = 0;
    const char* tmp[16] ={0};
    int file_nums = is_file_exit(&g_file_name_map, tmp);
    char* cookie1 = NULL, *cookie2 = NULL, *token = NULL, *msgid = NULL;
    str32 serial = {0};
    
    rt_memset_s(g_file_url, sizeof(g_file_url), 0, sizeof(g_file_url));
    rt_snprintf_s(g_file_url, sizeof(g_file_url), "https://%s:%d/server/api/v1/tov/fault-data/", host, server_port);

    if(SUCCESSFUL != get_token(&token))
    {
        LOG_E("%s:%d|get token fail", __FUNCTION__, __LINE__);
        if(log_in_fail_count++ < 10){
            LOG_E("%s", g_json_body);
            LOG_E("%s", g_recv_buffer);
        }
        return FAILURE;
    }

    if(SUCCESSFUL != get_cookie(&cookie1, &cookie2))
    {
        LOG_E("%s:%d|get cookie fail", __FUNCTION__, __LINE__);
        return FAILURE;
    }
    
    get_one_data(TOV_DATA_ID_SERIAL, serial);
    serial[ARRAY_SIZE_12] = '\0';
    // 附加的请求头信息
    rt_memset(g_send_buffer, 0x00, sizeof(g_send_buffer));
    rt_snprintf_s(g_send_buffer, sizeof(g_send_buffer), 
             "X-Emp-No: %s\r\n"
             "X-Auth-Value: %s\r\n"
             "Cookie: %s; %s\r\n"
             "Serial-Number: %s\r\n",
             username, token, cookie1, cookie2, serial);

    //校验文件是否存在
    if(file_nums > 0)
    {
        ret = webclient_post_file(0, g_file_url , tmp , file_nums, g_send_buffer);
        if(ret < 0)
        {
            LOG_E("%s:%d| ret: %d", __FUNCTION__, __LINE__, ret);
            return FAILURE;
        }
        LOG_E("%s:%d| %s", __FUNCTION__, __LINE__, g_send_buffer);

        if(SUCCESSFUL != get_msgid(&msgid))
        {
            LOG_E("%s:%d| get_msgid fail", __FUNCTION__, __LINE__);
            return FAILURE;
        }

        if(rt_strcmp(msgid, "0000") != 0)
        {
            LOG_E("%s:%d| msgid %s", __FUNCTION__, __LINE__, msgid);
            return FAILURE;
        }
    }

    return SUCCESSFUL;
}


void mqtt_thread_entry(void* param)
{
    g_tov_mqtt_server = _curr_server_get();
    while (is_running(TRUE))
    {
        process_recv_mqtt_msg();
        rt_thread_mdelay(20); 
        init_file_upload();
    }
}





int upfile_fail()
{
    unsigned int timer_status = 0;
    unsigned char upfile_sta = 0;
    set_one_data(TOV_DATA_ID_RECORD_UPFILE_STATUS, &upfile_sta);
    char ctrl[2] = {0};
    // 维持原样
    ctrl[0] = TRUE;   
    ctrl[1] = FALSE;   
    rt_thread_mdelay(20);
    pub_msg_to_thread(CTRL_SAMPLE_SWITCH, &ctrl, sizeof(ctrl));
    rt_timer_control(g_mqtt_timer, RT_TIMER_CTRL_GET_STATE, &timer_status);
    if(timer_status == RT_TIMER_FLAG_ACTIVATED)
    {
        return SUCCESSFUL;
    }
    rt_timer_start(g_mqtt_timer);
    return SUCCESSFUL;
}

void handle_upfile(_rt_msg_t curr_msg)
{
    str32 host = {0};
    str32 username = {0};
    str32 word = {0};
    unsigned short port = 0;
    int ret = 0;
    char ctrl[2] = {0};
    unsigned char upfile_sta = 0;
    
    get_one_para(TOV_PARA_ID_MQTT_NET_IP_OFFSET, host);
    get_one_para(TOV_PARA_ID_MQTT_NET_PORT_OFFSET, &port);
    get_one_para(TOV_PARA_ID_MQTT_USERNAME_OFFSET, username);
    get_one_para(TOV_PARA_ID_MQTT_WORD_OFFSET, word);
    ret = log_in(host, port, username, word);
    if(ret != SUCCESSFUL)
    {
        LOG_E("%s:%d|log_in fail", __FUNCTION__, __LINE__);
        upfile_fail();       
        return;
    }

    ret = upload_file(host, port, username);
    if(ret != SUCCESSFUL)
    {
        LOG_E("%s:%d|upload_file fail", __FUNCTION__, __LINE__);
        upfile_fail();
        return;
    }

    // delete_record_data_new(auth_code,RECORD_DATA_ACC_INDEX);//多条录波数据文件
    rt_thread_mdelay(20);
    // 开启采样
    ctrl[0] = TRUE;    
    ctrl[1] = TRUE;    
    set_one_data(TOV_DATA_ID_RECORD_UPFILE_STATUS, &upfile_sta);
    pub_msg_to_thread(CTRL_SAMPLE_SWITCH, &ctrl, sizeof(ctrl));
    rt_timer_stop(g_mqtt_timer);
    g_count = 0;
    return;
}

int process_recv_mqtt_msg(void)
{
    int i = 0;
    if ((rt_sem_take(&g_tov_mqtt_server->msg_sem, 0) != RT_EOK) || (g_tov_mqtt_server == NULL) || (g_tov_mqtt_server->msg_node == RT_NULL))
    {
        return FAILURE;
    }

    _rt_msg_t curr_msg = g_tov_mqtt_server->msg_node ;
    for (i = 0; i < sizeof(s_msg_handle) / sizeof(s_msg_handle[0]); i++)
    {
        if (s_msg_handle[i].msg_id == curr_msg->msg.msg_id)
        {
            s_msg_handle[i].handle(curr_msg);
        }
    }

    rt_mutex_take(&g_tov_mqtt_server->mutex, RT_WAITING_FOREVER);
    g_tov_mqtt_server->msg_node = curr_msg->next;
    g_tov_mqtt_server->msg_count--;
    rt_mutex_release(&g_tov_mqtt_server->mutex);

    softbus_free(curr_msg);
    return SUCCESSFUL;
}

void reset_4g_timeout()
{
    if(g_count <= MQTT_THRESH)
    {
        pub_msg_to_thread(MQTT_INFORM_4G_RESET, NULL, 0);
    }
    else
    {
        if(g_count % MQTT_THRESH == 0)
        {
            pub_msg_to_thread(MQTT_INFORM_4G_RESET, NULL, 0);
        }
    }

    g_count++;
}


int get_msgid(char** msgid)
{
    char *msgid_start = NULL, *msgid_end = NULL;
    msgid_start = rt_strstr(g_send_buffer, "\"msgId\": \"");
    if (msgid_start != NULL)
    {
        msgid_start += 10; // 跳过 "\"msgId\": \""
        msgid_end = rt_strstr(msgid_start, "\"");
        if (msgid_end != NULL)
        {
            *msgid_end = '\0';
            *msgid = msgid_start;
            return SUCCESSFUL;
        }
    }
    return FAILURE;
}


int wait_upload_file(unsigned int* count)
{
    unsigned char sim_sta = 0, model_sta = 0;
    static unsigned int count_4g = 0;
    get_one_data(TOV_DATA_ID_SIM_EXIST, &sim_sta);
    get_one_data(TOV_DATA_ID_4G_EXIST, &model_sta);
    if(sim_sta && model_sta)
    {
        count_4g++;
        if(count_4g == COUNT_4G)
        {
            LOG_E("%s:%d| start upload", __FUNCTION__, __LINE__);
            handle_upfile(NULL);
            *count = WAIT_COUNT + 1;
        }
    }
    return SUCCESSFUL;
}

int init_file_upload()
{
    static unsigned int count = 0;
    const char* tmp[16] ={0};
    static int file_nums = 0;
    char ctrl[2] = {0};
    unsigned char upfile_sta = TRUE;
    ctrl[0] = TRUE;     // 开启采样
    ctrl[1] = FALSE;     // 上送失败
    if(count <= WAIT_COUNT)
    {   
        // 判断录波文件在不在
        if(file_nums == 0)
        {
            file_nums = is_file_exit(&g_rf_file_map, tmp);
            if(file_nums == 0)
            {
                pub_msg_to_thread(CTRL_SAMPLE_SWITCH, &ctrl, sizeof(ctrl));
                count = WAIT_COUNT + 1;
                LOG_E("%s:%d| file not exist", __FUNCTION__, __LINE__);
                return SUCCESSFUL;
            }
        }
        set_one_data(TOV_DATA_ID_RECORD_UPFILE_STATUS, &upfile_sta);
        wait_upload_file(&count);
        if(count == WAIT_COUNT)
        {
            upfile_sta = FALSE;
            set_one_data(TOV_DATA_ID_RECORD_UPFILE_STATUS, &upfile_sta);
            pub_msg_to_thread(CTRL_SAMPLE_SWITCH, &ctrl, sizeof(ctrl));
            LOG_E("%s:%d| upload fail", __FUNCTION__, __LINE__);
        }
        count++;
    }
    return SUCCESSFUL;
}

