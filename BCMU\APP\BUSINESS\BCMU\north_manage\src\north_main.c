/*
 * @file    : north_main.c
 * @brief   : BCMU北向处理
 * @details : 
 * <AUTHOR> 邹绍云10326737
 * @Date    : 2023-01-30
 * @LastEditTime: 2023-04-24
 * @version : V0.0.1
* @para    : Copyright (c) 
 *            ZTE Corporation
 * @par     : History
 *     version: author, date, descn
 */
#include <rtthread.h>
#include <rtdevice.h>
#include <stdlib.h>
#include "device_type.h"
#include "cmd.h"
#include "sps.h"
#include "north_main.h"
#include "cmd.h"
#include "msg.h"
#include "sample.h"
#include "para_manage.h"
#include "pid.h"
#include "protocol_layer.h"
#include "msg_id.h"
#include "alarm_register.h"
#include "alarm_mgr_api.h"
#include "self_check.h"
#include "bcmu_self_test.h"
#include "update_manage.h"
#include "version.h"
#include "pin_ctrl.h"
#include "cell_balance.h"
#include "pin_define.h"
#include "utils_server.h"
#include "server_id.h"
#include "batt_charge.h"
#include "utils_time.h"
#include "realdata_save.h"
#include "rtc_ctrl.h"
#include "utils_rtthread_security_func.h"

/* 升级超时时间定义 */
#define CONCURRENT_UPGRADE_TIMEOUT   50   ///<  并发升级超时单位时间50ms
#include "time_sync.h"
#include "realdata_id_in.h"


#define time_wait_tick  10
#ifndef BATCH_PARA_SET_PID
#define BATCH_PARA_SET_PID
#define TIME_SYNC_MAX_NUM  20
#define BCMU_NORTH_TIME_DELAY  50    // unit ms
#define BCMU_TIME_SYNC_PERIOD  1000  // unit ms
#define BCMU_RESYNC_INTERVAL   (6 * 3600 * 1000)  // unit s



const unsigned short batch_para_set_pid[] = {
    PARA_ID_CELL_OVER_VOL_ALM_LEVEL_1,
    PARA_ID_CELL_OVER_VOL_ALM_LEVEL_2,
    PARA_ID_CELL_OVER_VOL_ALM_LEVEL_3,
    PARA_ID_CELL_UNDER_VOL_ALM_LEVEL_1,
    PARA_ID_CELL_UNDER_VOL_ALM_LEVEL_2,
    PARA_ID_CELL_UNDER_VOL_ALM_LEVEL_3,
    PARA_ID_CELL_VOL_DIFF_OVER_ALM_LEVEL_1,
    PARA_ID_CELL_VOL_DIFF_OVER_ALM_LEVEL_2,
    PARA_ID_CELL_VOL_DIFF_OVER_ALM_LEVEL_3,
    PARA_ID_CELL_CHG_HIGH_TEMP_ALM_LEVEL_1,
    PARA_ID_CELL_CHG_HIGH_TEMP_ALM_LEVEL_2,
    PARA_ID_CELL_CHG_HIGH_TEMP_ALM_LEVEL_3,
    PARA_ID_CELL_DISCHG_HIGH_TEMP_ALM_LEVEL_1,
    PARA_ID_CELL_DISCHG_HIGH_TEMP_ALM_LEVEL_2,
    PARA_ID_CELL_DISCHG_HIGH_TEMP_ALM_LEVEL_3,
    PARA_ID_CELL_CHG_LOW_TEMP_ALM_LEVEL_1,
    PARA_ID_CELL_CHG_LOW_TEMP_ALM_LEVEL_2,
    PARA_ID_CELL_CHG_LOW_TEMP_ALM_LEVEL_3,
    PARA_ID_CELL_DISCHG_LOW_TEMP_ALM_LEVEL_1,
    PARA_ID_CELL_DISCHG_LOW_TEMP_ALM_LEVEL_2,
    PARA_ID_CELL_DISCHG_LOW_TEMP_ALM_LEVEL_3,
    PARA_ID_CELL_OVER_TEMP_DIFF_ALM_LEVEL_1,
    PARA_ID_CELL_OVER_TEMP_DIFF_ALM_LEVEL_2,
    PARA_ID_CELL_OVER_TEMP_DIFF_ALM_LEVEL_3,
    PARA_ID_CELL_OVER_VOL_PROTECT_THRESH,
    PARA_ID_SET_CHG_LIMIT_CURR_COE,
    PARA_ID_SET_DISCHG_LIMIT_CURR,
    PARA_ID_SET_CHG_VOL,
    PARA_ID_SET_DISCHG_VOL,
    PARA_ID_BUS_OVER_VOL_PROTECT_THRESH,
    PARA_ID_BATT_UNDER_VOL_PROTECT_THRESH,
    PARA_ID_BUS_UNDER_VOL_PROTECT_THRESH,
    PARA_ID_BUS_AND_BATT_DIFF_PROTECT_THRESH,
    PARA_ID_BATT_NUM,
    PARA_ID_BCMU_ADDR,
    PARA_ID_BATT_CLUSTER_LOW_SOC_ALM,
    PARA_ID_BATT_CLUSTER_LOW_SOC_PROTECT,
    PARA_ID_BATT_CLUSTER_LOW_SOH_ALM,
    PARA_ID_BATT_CLUSTER_LOW_SOH_PROTECT,
    PARA_ID_BATT_CLUSTER_ENV_TEMP_HIGH_PROTECT,
    PARA_ID_BATT_CLUSTER_ENV_TEMP_LOW_PROTECT,
    PARA_ID_BMU_CELL_DAMAGE_PROTECT,
    PARA_ID_INSULATION_RESISTANCE_ALM_LEVEL_1,
    PARA_ID_INSULATION_RESISTANCE_ALM_LEVEL_2,
    PARA_ID_INSULATION_RESISTANCE_ALM_LEVEL_3,
    PARA_ID_CO_CONCENTRATION_ALM_LEVEL_1,
    PARA_ID_CO_CONCENTRATION_ALM_LEVEL_2,
    PARA_ID_CO_CONCENTRATION_ALM_LEVEL_3,
    PARA_ID_CELL_BALANCE_DIFF_THRE,
    PARA_ID_CELL_BALANCE_LOW_VOL_VALUE,
    //PARA_ID_BUS_CHG_LOW_VOL,
    PARA_ID_DISCHG_CUR_BALANCE_CUR,
    PARA_ID_CELL_CHARGE_HIGH_TEMP_LIMIT_CURR_COE,
    PARA_ID_POWRE_DOWN_VOL_THRE
};
#endif

#ifndef DC_DC_BATCH_PARA_SET_PID
#define DC_DC_BATCH_PARA_SET_PID
const unsigned short dc_dc_batch_para_set_pid[] = {
    PARA_ID_CELL_OVER_VOL_PROTECT_THRESH,
    PARA_ID_SET_CHG_LIMIT_CURR,
    PARA_ID_SET_DISCHG_LIMIT_CURR,
    PARA_ID_SET_CHG_VOL,
    PARA_ID_SET_DISCHG_VOL,
    PARA_ID_BUS_OVER_VOL_PROTECT_THRESH,
    PARA_ID_BATT_UNDER_VOL_PROTECT_THRESH,
    PARA_ID_BUS_UNDER_VOL_PROTECT_THRESH,
    PARA_ID_BUS_AND_BATT_DIFF_PROTECT_THRESH,
    PARA_ID_BATT_NUM,
    PARA_ID_BCMU_ADDR,
    //PARA_ID_BUS_CHG_LOW_VOL,
    PARA_ID_DISCHG_CUR_BALANCE_CUR,
    PARA_ID_POWRE_DOWN_VOL_THRE
};
#endif

#ifndef BATCH_ALM_LEVEL_SET_PID
#define BATCH_ALM_LEVEL_SET_PID
const unsigned short batch_alm_level_set_pid[] = {
    PARA_ID_ALM_LEVEL,
};
#endif

#ifndef BATCH_ALM_RELAY_SET_PID
#define BATCH_ALM_RELAY_SET_PID
const unsigned short batch_alm_relay_set_pid[] = {
    PARA_ID_ALM_RELAY,
};
#endif

#ifndef FAC_CONFIG_PARA_SET_PID
#define FAC_CONFIG_PARA_SET_PID
const unsigned short fac_config_para_set_pid[] = {
    PARA_ID_BCMU_SYSTEM_NAME,
    PARA_ID_BCMU_SERIES_NUMBER,
    PARA_ID_SERIES_NUMBER,
    PARA_ID_BMS_PRODUCE_DATE,
    PARA_ID_BMS_APPLY_DATE,
    PARA_ID_BMS_TYPE,
};
#endif

/* 协议命令处理结构 */
typedef struct {
    unsigned char cmd_id;
    int (*process)(dev_inst_t* dev_inst, north_mgr_t* north_mgr);
} north_cmd_process_t;

/* 消息接收处理结构 */
typedef struct {
    unsigned int msg_id;
    int (*handle)(module_msg_t* msg, dev_inst_t* dev_inst, north_mgr_t* north_mgr);
} north_msg_handle_t;

/* BSMU设备 */
static dev_inst_t* dev_bsmu = NULL;

/* 主动时间同步 */
static unsigned char time_sync_state = FALSE;
static time_sync_para_t time_sync_para = {
    BCMU_TIME_SYNC_PERIOD,
    BCMU_NORTH_TIME_DELAY,
    TIME_SYNC_MAX_NUM,
    BSMU_TIME_SYNC,
    BCMU_RESYNC_INTERVAL,
    {MOD_BSMU_COMM1, MOD_ALARM_MANAGE, DEVICE_SAMPLE_MSG, NULL},
    NULL,
    &time_sync_state
};

/* 获取厂家信息数据区 */
// static bcmu_fac_config_data_t bcmu_fac_config_data = {0};
// static bsmu_get_fac_data_t bsmu_get_fac_data = {0};
// static batt_mod_fact_data_t bmu_fact_data = {0};

Static int addr_assign_recv_process(dev_inst_t* dev_inst, north_mgr_t* north_mgr);
static int get_real_data_recv_process(dev_inst_t* dev_inst, north_mgr_t* north_mgr);
static int get_fac_info_recv_process(dev_inst_t* dev_inst, north_mgr_t* north_mgr);
// static void get_fac_info(bsmu_data_t* bsmu_data);
static int addr_assign_reply_process(module_msg_t* msg, dev_inst_t* dev_inst, north_mgr_t* north_mgr);
static int get_real_alm_recv_process(dev_inst_t* dev_inst, north_mgr_t* north_mgr);
Static int get_self_check_state_recv_process(dev_inst_t* dev_inst, north_mgr_t* north_mgr);
static north_mgr_t *init_thread_data(link_inst_t* link_inst, unsigned char mod_id);
static void send_batt_cluster_batch_para_msg();
// static void send_batt_cluster_para_msg(para_mapping_t para_mapping, void* dc_dc_para_val);
// static void send_batt_cluster_para_diff_cluster_msg(para_mapping_t para_mapping, void* dc_dc_para_val);
Static int  para_set_recv_process(dev_inst_t* dev_inst, north_mgr_t* north_mgr);
Static void send_msg_to_sample(unsigned int msg_id, unsigned char dest,  unsigned char dev_no, unsigned char cmd_id);
// static para_mapping_t get_para_mapping_by_bsmu_cmdid(unsigned char bsmu_cmd_id);
Static void handle_dc_dc_batch_para(dev_inst_t* dev_dc_dc, unsigned char dcdc_no);
// static short single_para_set_reply(unsigned char cmd_id, north_mgr_t* north_mgr, void* para_val);
// static short single_cluster_para_set_reply(unsigned char cmd_id,  north_mgr_t* north_mgr, void* para_val, void* dc_dc_para_val);
// static void get_batt_cabinet_real_data(bsmu_data_t* bsmu_data);
// static void get_batt_cluster_real_data(unsigned char mod_no, bsmu_data_t* bsmu_data);
// static void get_bmu_real_data(unsigned char mod_no, bsmu_data_t* bsmu_data);
// static void get_all_real_data(bsmu_data_t* bsmu_data);
static int get_alm_level_recv_process(dev_inst_t* dev_inst, north_mgr_t* north_mgr);
// static void get_alm_level_config(bsmu_data_t* bsmu_data);
static int update_recv_process(dev_inst_t* dev_inst, north_mgr_t* north_mgr);
static void handle_update_trig(unsigned char update_dev_type, unsigned char update_dev_addr);
static int handle_update_data(unsigned char update_dev_type, unsigned char update_dev_addr);
Static int set_bcmu_system_time_recv_process(dev_inst_t* dev_inst, north_mgr_t* north_mgr);
Static int get_bcmu_system_time_process(dev_inst_t* dev_inst, north_mgr_t* north_mgr);
static void exe_north_heartbeat(unsigned char inst_id);
static int  ctrl_recv_process(dev_inst_t* dev_inst, north_mgr_t* north_mgr);
Static int process_bsmu_cmd(dev_inst_t* dev_inst, north_mgr_t* north_mgr);
Static int process_bsmu_msg(dev_inst_t* dev_inst, north_mgr_t* north_mgr);

/* 未使用，先注释
static para_mapping_t para_mapping[] = {
    {PARA_ID_CELL_OVER_VOL_PROTECT_THRESH,     BSMU_SET_CLUSTER_OVP_THRE,     DC_DC_SET_PARA_CELL_OVER_VOL_PROTECT_THRESH},
    {PARA_ID_SET_CHG_LIMIT_CURR_COE,           BSMU_SET_CLUSTER_CL_CURR,      DC_DC_SET_PARA_SET_CHG_LIMIT_CURR},
    {PARA_ID_SET_DISCHG_LIMIT_CURR,            BSMU_SET_CLUSTER_DL_CURR,      DC_DC_SET_PARA_SET_DISCHG_LIMIT_CURR},
    {PARA_ID_SET_CHG_VOL,                      BSMU_SET_CLUSTER_CHG_VO,       DC_DC_SET_PARA_SET_CHG_VOL},
    {PARA_ID_SET_DISCHG_VOL,                   BSMU_SET_CLUSTER_DISCHG_VOL,   DC_DC_SET_PARA_SET_DISCHG_VOL},
    {PARA_ID_BUS_OVER_VOL_PROTECT_THRESH,      BSMU_SET_CLUSTER_BUS_OVP_THRE, DC_DC_SET_PARA_BUS_OVER_VOL_PROTECT_THRESH},
    {PARA_ID_BATT_UNDER_VOL_PROTECT_THRESH,    BSMU_SET_CLUSTER_BAT_UVP_THRE, DC_DC_SET_PARA_BATT_UNDER_VOL_PROTECT_THRESH},
    {PARA_ID_BUS_UNDER_VOL_PROTECT_THRESH,     BSMU_SET_CLUSTER_BUS_UVP_THRE, DC_DC_SET_PARA_BUS_UNDER_VOL_PROTECT_THRESH},
    {PARA_ID_BUS_AND_BATT_DIFF_PROTECT_THRESH, BSMU_SET_CLUSTER_BBDP_THRE,    DC_DC_SET_PARA_BUS_AND_BATT_DIFF_PROTECT_THRESH},
    {PARA_ID_BATT_NUM,                         BSMU_SET_CLUSTER_BAT_NUM,      DC_DC_SET_PARA_BATT_NUM},
    {PARA_ID_BCMU_ADDR,                        BSMU_SET_BCMU_ADDR,            DC_DC_SET_PARA_BCMU_ADDR},
    //{PARA_ID_BUS_CHG_LOW_VOL,                  BSMU_BUS_CHG_LOW_VOL,          DC_DC_SET_PARA_BUS_CHG_LOW_VOL},           //BUS充电最低电压阈值
    {PARA_ID_DISCHG_CUR_BALANCE_CUR,           BSMU_DISCHG_CUR_BALANCE_CUR,   DC_DC_SET_PARA_DISCHG_CUR_BALANCE_CUR},           //放电均流电流
    {PARA_ID_CELL_CHARGE_HIGH_TEMP_LIMIT_CURR_COE, BSMU_CELL_CHG_HIGH_TEMP_LIMIT_CURR_COE, DC_DC_SET_PARA_CELL_CHG_HIGH_TEMP_LIMIT_CURR_COE}, 
    {PARA_ID_POWRE_DOWN_VOL_THRE,              BSMU_SET_CLUSTER_POWRE_DOWN_VOL_THRE,    DC_DC_SET_PARA_POWRE_DOWN_VOL_THRE},
    {PARA_ID_CELL_OVER_VOL_ALM_LEVEL_1, BSMU_SET_CELL_OVA_LV1},         //电芯过压告警（一级）
    {PARA_ID_CELL_OVER_VOL_ALM_LEVEL_2, BSMU_SET_CELL_OVA_LV2},         //电芯过压告警（二级）
    {PARA_ID_CELL_OVER_VOL_ALM_LEVEL_3, BSMU_SET_CELL_OVA_LV3},         //电芯过压告警（三级）
    {PARA_ID_CELL_UNDER_VOL_ALM_LEVEL_1, BSMU_SET_CELL_UVA_LV1},        //电芯欠压告警（一级）
    {PARA_ID_CELL_UNDER_VOL_ALM_LEVEL_2, BSMU_SET_CELL_UVA_LV2},        //电芯欠压告警（二级）
    {PARA_ID_CELL_UNDER_VOL_ALM_LEVEL_3, BSMU_SET_CELL_UVA_LV3},        //电芯欠压告警（三级）
    {PARA_ID_CELL_VOL_DIFF_OVER_ALM_LEVEL_1, BSMU_SET_CELL_VDOA_LV1},   //电芯压差过大告警（一级）
    {PARA_ID_CELL_VOL_DIFF_OVER_ALM_LEVEL_2, BSMU_SET_CELL_VDOA_LV2},   //电芯压差过大告警（二级）
    {PARA_ID_CELL_VOL_DIFF_OVER_ALM_LEVEL_3, BSMU_SET_CELL_VDOA_LV3},   //电芯压差过大告警（三级）
    {PARA_ID_CELL_CHG_HIGH_TEMP_ALM_LEVEL_1, BSMU_SET_CELL_CHTA_LV1},   //电芯充电高温告警（一级）
    {PARA_ID_CELL_CHG_HIGH_TEMP_ALM_LEVEL_2, BSMU_SET_CELL_CHTA_LV2},   //电芯充电高温告警（二级）
    {PARA_ID_CELL_CHG_HIGH_TEMP_ALM_LEVEL_3, BSMU_SET_CELL_CHTA_LV3},   //电芯充电高温告警（三级）
    {PARA_ID_CELL_DISCHG_HIGH_TEMP_ALM_LEVEL_1, BSMU_SET_CELL_DHTA_LV1},//电芯放电高温告警（一级）
    {PARA_ID_CELL_DISCHG_HIGH_TEMP_ALM_LEVEL_2, BSMU_SET_CELL_DHTA_LV2},//电芯放电高温告警（二级）
    {PARA_ID_CELL_DISCHG_HIGH_TEMP_ALM_LEVEL_3, BSMU_SET_CELL_DHTA_LV3},//电芯放电高温告警（三级）
    {PARA_ID_CELL_CHG_LOW_TEMP_ALM_LEVEL_1, BSMU_SET_CELL_CLTA_LV1},    //电芯充电低温告警（一级）
    {PARA_ID_CELL_CHG_LOW_TEMP_ALM_LEVEL_2, BSMU_SET_CELL_CLTA_LV2},    //电芯充电低温告警（二级）
    {PARA_ID_CELL_CHG_LOW_TEMP_ALM_LEVEL_3, BSMU_SET_CELL_CLTA_LV3},    //电芯充电低温告警（三级）
    {PARA_ID_CELL_DISCHG_LOW_TEMP_ALM_LEVEL_1, BSMU_SET_CELL_DLTA_LV1}, //电芯放电低温告警（一级）
    {PARA_ID_CELL_DISCHG_LOW_TEMP_ALM_LEVEL_2, BSMU_SET_CELL_DLTA_LV2}, //电芯放电低温告警（二级）
    {PARA_ID_CELL_DISCHG_LOW_TEMP_ALM_LEVEL_3, BSMU_SET_CELL_DLTA_LV3}, //电芯放电低温告警（三级）
    {PARA_ID_CELL_OVER_TEMP_DIFF_ALM_LEVEL_1, BSMU_SET_CELL_OTDA_LV1},  //电芯温差过大告警（一级）
    {PARA_ID_CELL_OVER_TEMP_DIFF_ALM_LEVEL_2, BSMU_SET_CELL_OTDA_LV2},  //电芯温差过大告警（二级）
    {PARA_ID_CELL_OVER_TEMP_DIFF_ALM_LEVEL_3, BSMU_SET_CELL_OTDA_LV3},  //电芯温差过大告警（三级）
    {PARA_ID_BATT_CLUSTER_LOW_SOC_ALM, BSMU_SET_CLUSTER_SOC_LOW_ALM},       //电池簇SOC低告警
    {PARA_ID_BATT_CLUSTER_LOW_SOC_PROTECT, BSMU_SET_CLUSTER_SOC_LOW_PROT},  //电池簇SOC低保护
    {PARA_ID_BATT_CLUSTER_LOW_SOH_ALM, BSMU_SET_CLUSTER_SOH_LOW_ALM},       //电池簇SOH低告警
    {PARA_ID_BATT_CLUSTER_LOW_SOH_PROTECT, BSMU_SET_CLUSTER_SOH_LOW_PROT},  //电池簇SOH低保护
    {PARA_ID_BATT_CLUSTER_ENV_TEMP_HIGH_PROTECT, BSMU_SET_CLUSTER_ETH_PROT},//电池簇环境温度高保护
    {PARA_ID_BATT_CLUSTER_ENV_TEMP_LOW_PROTECT, BSMU_SET_CLUSTER_ETL_PROT}, //电池簇环境温度低保护
    {PARA_ID_BMU_CELL_DAMAGE_PROTECT, BSMU_SET_BMU_CELL_DAM_PROT},          //BMU电芯损坏保护
    {PARA_ID_INSULATION_RESISTANCE_ALM_LEVEL_1, BSMU_SET_IRA_ALM_LV1},      //绝缘电阻告警（一级）
    {PARA_ID_INSULATION_RESISTANCE_ALM_LEVEL_2, BSMU_SET_IRA_ALM_LV2},      //绝缘电阻告警（二级）
    {PARA_ID_INSULATION_RESISTANCE_ALM_LEVEL_3, BSMU_SET_IRA_ALM_LV3},      //绝缘电阻告警（三级）
    {PARA_ID_CO_CONCENTRATION_ALM_LEVEL_1, BSMU_SET_COC_ALM_LV1},           //一氧化碳浓度告警（一级）
    {PARA_ID_CO_CONCENTRATION_ALM_LEVEL_2, BSMU_SET_COC_ALM_LV2},           //一氧化碳浓度告警（二级）
    {PARA_ID_CO_CONCENTRATION_ALM_LEVEL_3, BSMU_SET_COC_ALM_LV3},           //一氧化碳浓度告警（三级）
    {PARA_ID_CELL_BALANCE_DIFF_THRE, BSMU_CELL_BALANCE_VOL_DIFF},      //均衡启动电压差阈值设置
    {PARA_ID_CELL_BALANCE_LOW_VOL_VALUE, BSMU_CELL_BALANCE_LOW_VOL},           //均衡最低启动电压值设置

    {0}
};
*/
static dev_init_t dev_init_tab[] = {
    #ifdef USING_DEVICE_BMU
    {DEV_BMU, init_dev_bmu, get_bmu_data , NULL},
    #endif
    
    #ifdef USING_DEVICE_BSMU
    {DEV_BSMU, init_dev_bsmu, get_bsmu_data, NULL},
    #endif
    
    #ifdef USING_DEVICE_BCMU
    {DEV_BCMU, init_dev_bcmu, get_bcmu_data, NULL},
    #endif
};
/* BCMU 协议命令处理表 */
static north_cmd_process_t bsmu_process_tab[] = {
    {BSMU_ADDR_ASSIGN_TRIG, addr_assign_recv_process},
    {BSMU_GET_ALL_REAL_DATA, get_real_data_recv_process},
    {BSMU_SET_BATCH_PARA, para_set_recv_process},
    {BSMU_GET_REAL_ALM, get_real_alm_recv_process},
    {BSMU_GET_ALM_LEVEL, get_alm_level_recv_process},
    {BSMU_GET_SELF_CHECK_STATE, get_self_check_state_recv_process},
    {BSMU_UPDATE_DATA_TRIG, update_recv_process},
    {BSMU_GET_FAC_DATA, get_fac_info_recv_process},
    {BSMU_SET_CALIBRATED_TIME, set_bcmu_system_time_recv_process},
    {BSMU_GET_SYS_TIME, get_bcmu_system_time_process},
    {BSMU_CTRL_POWER_ON, ctrl_recv_process},
};

/* BCMU  消息处理表 */
static north_msg_handle_t bsmu_msg_handle_tab[] = {
    {FINISH_BMU_ADDR_DISTRIBUTION_MSG, addr_assign_reply_process},
};

static void process_data_change(const rt_msg_t pMsg) {
    
}

static msg_map north_msg_map[] = {
    {DATA_CHANGE_MSG_ID, process_data_change},
};

void* init_north1(void * param)
{
    server_info_t *server_info = (server_info_t *)param;
    north_mgr_t* north_mgr = NULL;
    init_dev_tab(dev_init_tab, sizeof(dev_init_tab)/sizeof(dev_init_tab[0]));
    dev_bsmu = init_dev_inst(DEV_BSMU);
    RETURN_VAL_IF_FAIL(dev_bsmu != NULL, NULL);
    time_sync_para.dev_inst = dev_bsmu;

    //server_info = get_server_info("north1");
    server_info->server.server.map_size = sizeof(north_msg_map) / sizeof(msg_map);
    register_server_msg_map(north_msg_map, server_info);
    north_mgr = init_thread_data(dev_bsmu->dev_type->link_inst, MOD_BSMU_COMM1);

    /*if(north_mgr != NULL) {
        bsmu_thread(north_mgr);
    }*/
    return north_mgr;
    //thread_create(thread_north_group, sizeof(thread_north_group)/sizeof(thread_info_t));
}

void* init_north2(void * param)
{
    server_info_t *server_info = (server_info_t *)param;
    north_mgr_t* north_mgr = NULL;
    dev_bsmu = init_dev_inst(DEV_BSMU);
    RETURN_VAL_IF_FAIL(dev_bsmu != NULL, NULL);
    time_sync_para.dev_inst = dev_bsmu;

    //server_info = get_server_info("north2");
    server_info->server.server.map_size = sizeof(north_msg_map) / sizeof(msg_map);
    register_server_msg_map(north_msg_map, server_info);
    north_mgr = init_thread_data(dev_bsmu->dev_type->link_inst_bak, MOD_BSMU_COMM2);

    /*if(north_mgr != NULL) {
        bsmu_thread(north_mgr);
    }*/
    return north_mgr;
    //thread_create(thread_north_group, sizeof(thread_north_group)/sizeof(thread_info_t));
}

static north_mgr_t *init_thread_data(link_inst_t* link_inst, unsigned char mod_id) {
    north_mgr_t* north_mgr = NULL;
    
    north_mgr = (north_mgr_t*)malloc(sizeof(north_mgr_t));
    if (north_mgr == NULL)
        return north_mgr;
    
    north_mgr->cmd_buff = (cmd_buf_t*)malloc(sizeof(cmd_buf_t));
    if (north_mgr->cmd_buff == NULL) {
        goto NORTH_MGR_FREE;
    }

    north_mgr->north_mq = init_msg_queue(mod_id);
    if (north_mgr->north_mq == NULL) {
        goto CMD_BUFF_FREE;
    }
    dev_bsmu->dev_data.valid_flag = DATA_VALID;
    north_mgr->dev_data = (bsmu_data_t*)dev_bsmu->dev_data.bsmu_data;
    north_mgr->link_inst = link_inst;
    //server_info->server_data = north_mgr;
    return north_mgr;
 
CMD_BUFF_FREE:
    free(north_mgr->cmd_buff);
     
NORTH_MGR_FREE:
    free(north_mgr);
    return NULL;
}

/**
 * @brief 北向BSMU命令处理
*/
Static int process_bsmu_cmd(dev_inst_t* dev_inst, north_mgr_t* north_mgr) {
    short ret = 0;
    unsigned short i = 0;

    RETURN_VAL_IF_FAIL(dev_inst != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(north_mgr != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(north_mgr->cmd_buff != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(north_mgr->cmd_buff->cmd != NULL, FAILURE);

    // 协议栈接收解析至ID数据
    rt_memset_s(north_mgr->cmd_buff, sizeof(cmd_buf_t), 0x00, sizeof(cmd_buf_t));
    ret = cmd_recv_id_version(dev_bsmu, north_mgr->cmd_buff);
    RETURN_VAL_IF_FAIL(SUCCESSFUL == ret, FAILURE);

    // 匹配命令ID进行处理
    for (i = 0; i < sizeof(bsmu_process_tab) / sizeof(north_cmd_process_t); ++i) {
        if (NULL == bsmu_process_tab[i].process || north_mgr->cmd_buff->cmd->cmd_id != bsmu_process_tab[i].cmd_id) {
            continue;
        }

        if (SUCCESSFUL == bsmu_process_tab[i].process(dev_bsmu, north_mgr)) {
            break;
        }
    }
    return SUCCESSFUL;
}

/**
 * @brief 北向消息接收处理
 * @note todo:用最新消息机制替换
*/
Static int process_bsmu_msg(dev_inst_t* dev_inst, north_mgr_t* north_mgr) {
    module_msg_t msg = {0};
    unsigned short i = 0;
    // 收消息模块返回的消息,并发送结果
    if (RT_EOK == rt_mq_recv(north_mgr->north_mq, &msg, sizeof(module_msg_t), 0)) {
        for (i = 0; i < sizeof(bsmu_msg_handle_tab) / sizeof(north_msg_handle_t); ++i) {
            if (NULL == bsmu_msg_handle_tab[i].handle || msg.msg_id != bsmu_msg_handle_tab[i].msg_id) {
                continue;
            }

            if (SUCCESSFUL == bsmu_msg_handle_tab[i].handle(&msg, dev_bsmu, north_mgr)) {
                break;
            }
        }
    }
    return SUCCESSFUL;
}

/* BSMU收发线程 */
void bsmu_thread(void *param) {
    north_mgr_t* north_mgr = (north_mgr_t*)param;

    while (is_running(TRUE)) {
        if(RT_EOK == rt_sem_take(&(north_mgr->link_inst->rx_sem), time_wait_tick)) {
            // 接收命令数据
            process_bsmu_cmd(dev_bsmu, north_mgr);
        }

        // 接收消息并协议恢复
        process_bsmu_msg(dev_bsmu, north_mgr);

        /* 时间同步 */
        positive_time_sync(set_time_sync_alm_state, get_time_sync_alm_state, &time_sync_para);
        rt_thread_mdelay(BCMU_NORTH_TIME_DELAY);

        exe_north_heartbeat(north_mgr->link_inst->id);
    }
}

/**
 * @brief 接收站址分配命令后的处理 
 * @param[in] dev_inst 设备实例
 * @param[in] cmd_buff 协议栈命令包
 * @retval    SUCCESSFUL 成功， FAILURE 失败
 * @note 发送消息到站址分配模块
 */
Static int addr_assign_recv_process(dev_inst_t* dev_inst, north_mgr_t* north_mgr) {
    dev_inst_t* bsmu_inst = NULL;
    bsmu_data_t* bsmu_data = NULL;
    module_msg_t msg_send = {MOD_BSMU_COMM1, MOD_CONTROL, NOTIFY_ADDR_DISTRIBUTION_MSG, NULL};

    bsmu_inst = (dev_inst_t*)dev_inst;
    RETURN_VAL_IF_FAIL(bsmu_inst != NULL, FAILURE);

    bsmu_data = (bsmu_data_t*)bsmu_inst->dev_data.bsmu_data;
    RETURN_VAL_IF_FAIL(bsmu_data != NULL, FAILURE);

    RETURN_VAL_IF_FAIL(north_mgr->cmd_buff->cmd != NULL, FAILURE);

    if (north_mgr->cmd_buff->cmd->cmd_id != BSMU_ADDR_ASSIGN_TRIG)
        return FAILURE;

    msg_send.data = malloc(sizeof(addr_assign_trig_data_t));  // 由站址分配模块释放
    RETURN_VAL_IF_FAIL(msg_send.data != NULL, FAILURE);
    rt_memset_s(msg_send.data, sizeof(addr_assign_trig_data_t), 0x00, sizeof(addr_assign_trig_data_t));
    rt_memcpy_s(msg_send.data, sizeof(addr_assign_trig_data_t), &bsmu_data->addr_assign_trig, sizeof(addr_assign_trig_data_t));
    return send_msg(&msg_send);
}

/**
 * @brief 设置系统时间
 * @param[in] dev_inst 设备实例
 * @param[in] nort_mgr 
 * @retval    SUCCESSFUL 成功， FAILURE 失败
 */
Static int set_bcmu_system_time_recv_process(dev_inst_t* dev_inst, north_mgr_t* north_mgr) {
    time_base_t date_time_recv = {0};

    RETURN_VAL_IF_FAIL(dev_inst != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(north_mgr != NULL, FAILURE);

    // ID化数据读取
    get_one_data(BCMU_DATA_ID_SYS_TIME_SET, &date_time_recv);

    // 写入并回包
    if (SUCCESSFUL == set_rtc_date_time(date_time_recv)) {
        send_dest_cmd(dev_bsmu, BSMU_SET_CALIBRATED_TIME);
        time_sync_state = TRUE;
        return SUCCESSFUL;
    }
    return FAILURE;
}

/**
 * @brief 获取系统时间
 * @param[in] dev_inst 设备实例
 * @param[in] nort_mgr 
 * @retval    SUCCESSFUL 成功， FAILURE 失败
 */
Static int get_bcmu_system_time_process(dev_inst_t* dev_inst, north_mgr_t* north_mgr) {
    time_base_t date_time_now = {0};

    RETURN_VAL_IF_FAIL(dev_inst != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(north_mgr != NULL, FAILURE);

    get_time(&date_time_now);
    set_one_data(BCMU_DATA_ID_SYS_TIME_SET, &date_time_now);
    send_dest_cmd(dev_bsmu, BSMU_GET_SYS_TIME);

    return SUCCESSFUL;
}

static int get_real_data_recv_process(dev_inst_t* dev_inst, north_mgr_t* north_mgr) {
    dev_inst_t* bsmu_inst = NULL;
    bsmu_data_t* bsmu_data = NULL;
    int ret = 0;


    bsmu_inst = (dev_inst_t*)dev_inst;
    RETURN_VAL_IF_FAIL(bsmu_inst != NULL, FAILURE);

    bsmu_data = (bsmu_data_t*)bsmu_inst->dev_data.bsmu_data;
    RETURN_VAL_IF_FAIL(bsmu_data != NULL, FAILURE);

    RETURN_VAL_IF_FAIL(north_mgr->cmd_buff->cmd != NULL, FAILURE);

    switch(north_mgr->cmd_buff->cmd->cmd_id) {
        case BSMU_GET_ALL_REAL_DATA:
        
            ret = send_dest_cmd(dev_bsmu, BSMU_GET_ALL_REAL_DATA);
            break;
        case BSMU_GET_CABINET_REAL_DATA:
   
            ret = send_dest_cmd(dev_bsmu, BSMU_GET_CABINET_REAL_DATA);
            break;
        case BSMU_GET_CLUSTER_REAL_DATA:
         
            ret = send_dest_cmd(dev_bsmu, BSMU_GET_CLUSTER_REAL_DATA);
            break;
        case BSMU_GET_BMU_REAL_DATA:
        
            ret = send_dest_cmd(dev_bsmu, BSMU_GET_BMU_REAL_DATA);
            break;
        default:
            return FAILURE;
    } 
    return ret;
}

/* 未使用，先注释
static void get_batt_cabinet_real_data(bsmu_data_t* bsmu_data) {
    cluster_topology_t cluster_topo = {0};

    memset(&(bsmu_data->pack_batt_cabinet_real_data), 0, sizeof(BATT_CABINET_real_data_t));

    get_cluster_topology(&cluster_topo);
    if(cluster_topo.topo_valid == TOPO_INVALID) {
        return;
    }
    
    bsmu_data->pack_batt_cabinet_real_data.batt_cluster_num = cluster_topo.cluster_count;
    bsmu_data->pack_batt_cabinet_real_data.all_bmu_num = cluster_topo.mod_count;
    memcpy(bsmu_data->pack_batt_cabinet_real_data.batt_clutter_bmu_num, cluster_topo.mod_num, BATT_CLUSTER_NUM);
    bsmu_data->pack_batt_cabinet_real_data.bmu_cell_num = BATT_MOD_CELL_NUM;
    //todo:电池柜工作状态，主从机状态，消防状态
    return;
}
*/

/**
 * @brief 接收获取厂家信息后的处理 
 * @param[in] dev_inst 设备实例
 * @param[in] cmd_buff 协议栈命令包
 * @retval    SUCCESSFUL 成功， FAILURE 失败
 * @note 
 */
static int  get_fac_info_recv_process(dev_inst_t* dev_inst, north_mgr_t* north_mgr) {
    dev_inst_t* bsmu_inst = NULL;
    // bsmu_data_t* bsmu_data = NULL;
    int ret = FAILURE;

    RETURN_VAL_IF_FAIL(north_mgr != NULL, FAILURE);

    bsmu_inst = (dev_inst_t*)dev_inst;
    RETURN_VAL_IF_FAIL(bsmu_inst != NULL, FAILURE);

    RETURN_VAL_IF_FAIL(north_mgr->cmd_buff->cmd != NULL, FAILURE);

    if (north_mgr->cmd_buff->cmd->cmd_id != BSMU_GET_FAC_DATA)
        return FAILURE;

    // bsmu_data = (bsmu_data_t*)bsmu_inst->dev_data.bsmu_data;
   // get_fac_info(bsmu_data);
    ret = send_dest_cmd(dev_bsmu, BSMU_GET_FAC_DATA);
    return ret;
}



/**
 * @brief 接收获取实时告警后的处理 
 * @param[in] dev_inst 设备实例
 * @param[in] cmd_buff 协议栈命令包
 * @retval    SUCCESSFUL 成功， FAILURE 失败
 * @note 
 */
static int get_real_alm_recv_process(dev_inst_t* dev_inst, north_mgr_t* north_mgr) {
    dev_inst_t* bsmu_inst = NULL;
    // bsmu_data_t* bsmu_data = NULL;
    int ret = FAILURE;

    RETURN_VAL_IF_FAIL(north_mgr != NULL, FAILURE);

    bsmu_inst = (dev_inst_t*)dev_inst;
    RETURN_VAL_IF_FAIL(bsmu_inst != NULL, FAILURE);

    RETURN_VAL_IF_FAIL(north_mgr->cmd_buff->cmd != NULL, FAILURE);

    if (north_mgr->cmd_buff->cmd->cmd_id != BSMU_GET_REAL_ALM)
        return FAILURE;

    // bsmu_data = (bsmu_data_t*)bsmu_inst->dev_data.bsmu_data;
   // get_real_alm(bsmu_data); /* ID化后就不需要这种获取方式了，屏蔽20230907*/
    ret = send_dest_cmd(dev_bsmu, BSMU_GET_REAL_ALM);
    return ret;
}


/**
 * @brief 接收获取告警级别命令后的处理 
 * @param[in] dev_inst 设备实例
 * @param[in] north_mgr
 * @retval    SUCCESSFUL 成功， FAILURE 失败
 * @note 
 */
static int get_alm_level_recv_process(dev_inst_t* dev_inst, north_mgr_t* north_mgr){
    dev_inst_t* bsmu_inst = NULL;
    bsmu_data_t* bsmu_data = NULL;
    int ret = 0;
    
    bsmu_inst = (dev_inst_t*)dev_inst;
    RETURN_VAL_IF_FAIL(bsmu_inst != NULL, FAILURE);
    
    bsmu_data = (bsmu_data_t*)bsmu_inst->dev_data.bsmu_data;
    RETURN_VAL_IF_FAIL(bsmu_data != NULL, FAILURE);

    RETURN_VAL_IF_FAIL(north_mgr->cmd_buff->cmd != NULL, FAILURE);
    
    if(north_mgr->cmd_buff->cmd->cmd_id != BSMU_GET_ALM_LEVEL)
        return FAILURE;
    
   // get_alm_level_config(bsmu_data);/* ID化后就不需要这种获取方式了，屏蔽20230907*/
    ret = send_dest_cmd(dev_bsmu, BSMU_GET_ALM_LEVEL);
    return ret;
}

/* 未使用，先注释
static void get_alm_level_config(bsmu_data_t* bsmu_data){
    BSMU_param_alm_level_t bsmu_param_alm_level = {0};
    
    RETURN_IF_FAIL(bsmu_data != NULL);
    get_multi_para(1, &batch_alm_level_set_pid[0], &bsmu_param_alm_level);
    memcpy(&bsmu_data->pack_bsmu_param_alm_level, &bsmu_param_alm_level, sizeof(BSMU_param_alm_level_t));
}
*/




/**
 * @brief 站址分配结果回复处理
 * @param[in] dev_inst 设备实例
 * @param[in] cmd_buff 协议栈命令包
 * @param[in] msg      收到的消息
 * @retval    SUCCESSFUL 成功， FAILURE 失败
 * @note 打包回复结果
 */
static int addr_assign_reply_process(module_msg_t* msg, dev_inst_t* dev_inst, north_mgr_t* north_mgr) {
    dev_inst_t* bsmu_inst = NULL;
    bsmu_data_t* bsmu_data = NULL;

    bsmu_inst = (dev_inst_t*)dev_inst;
    RETURN_VAL_IF_FAIL(bsmu_inst != NULL, FAILURE);

    bsmu_data = (bsmu_data_t*)bsmu_inst->dev_data.bsmu_data;
    RETURN_VAL_IF_FAIL(bsmu_data != NULL, FAILURE);

    RETURN_VAL_IF_FAIL(msg != NULL, FAILURE);

    if (FINISH_BMU_ADDR_DISTRIBUTION_MSG != msg->msg_id) {
            return FAILURE;
    }

    if (msg->data != NULL) {
        bsmu_data->addr_assign_trig_reply = *((unsigned char*)msg->data);
        free(msg->data);
        msg->data = NULL;
    }

    RETURN_VAL_IF_FAIL(north_mgr->cmd_buff != NULL, FAILURE);
    north_mgr->cmd_buff->addr_dev_data = 0x0000;  // 无设备地址偏移
    return cmd_send_id_version(dev_inst, north_mgr->cmd_buff);
}

Static int get_self_check_state_recv_process(dev_inst_t* dev_inst, north_mgr_t* north_mgr){
    int ret = 0;
    int i;
    int j;
    int map_addr;
    int all_bmu_num = 0;
    // batt_mod_dig_data_t batt_mod_dig_data;
    // batt_cluster_dig_data_t batt_cluster_dig_data;
    BSMU_self_check_state_t self_check_state;
    cluster_topology_t cluster_topo;


    RETURN_VAL_IF_FAIL(north_mgr->cmd_buff->cmd != NULL, FAILURE);

    if(north_mgr->cmd_buff->cmd->cmd_id != BSMU_GET_SELF_CHECK_STATE)
        return FAILURE;

    get_cluster_topology(&cluster_topo);

    if(cluster_topo.topo_valid){
        self_check_state.bcmu_self_check_state = bcmu_power_on_check();
        rt_memset_s(self_check_state.dc_dc_self_check_state, sizeof(char) * BATT_CLUSTER_NUM , 0, sizeof(char) * BATT_CLUSTER_NUM);
        rt_memset_s(self_check_state.batt_clutter_bmu_num, sizeof(char) * BATT_CLUSTER_MOD_NUM, 0, sizeof(char) * BATT_CLUSTER_MOD_NUM);
        rt_memset_s(self_check_state.bmu_self_check_state, sizeof(char) * BATT_MOD_NUM, 0, sizeof(char) * BATT_MOD_NUM);
        //power_on_check(bcmu_power_on_check_item, sizeof(bcmu_power_on_check_item)/sizeof(bcmu_power_on_check_item[0]));//todo:调用接口获取
        self_check_state.batt_cluster_num = cluster_topo.cluster_count;
        if(self_check_state.batt_cluster_num > BATT_CLUSTER_NUM){
            return FAILURE;
        }
        for(i = 0; i < self_check_state.batt_cluster_num; i++){
            self_check_state.batt_clutter_bmu_num[i] = cluster_topo.mod_num[i];
            all_bmu_num += self_check_state.batt_clutter_bmu_num[i];
        }

        //获取BMU的自检状态
        for(i = 0; i < self_check_state.batt_cluster_num; i++){
            self_check_state.batt_clutter_bmu_num[i] = cluster_topo.mod_num[i];
            for(j = 0; j < self_check_state.batt_clutter_bmu_num[i]; j++){
                map_addr = cluster_topo.mod_addr[i][j];
                if(map_addr > BATT_MOD_NUM || map_addr < 1){
                    return FAILURE;
                }
                get_one_data(BCMU_DATA_ID_BMU_SELF_CHECK_STATE, &self_check_state.bmu_self_check_state[map_addr-1]);
            }
        }
        self_check_state.all_bmu_num = all_bmu_num;
        rt_memcpy_s(&((bsmu_data_t*)dev_bsmu->dev_data.bsmu_data)->bsmu_self_check_state, sizeof(BSMU_self_check_state_t), &self_check_state, sizeof(BSMU_self_check_state_t));
        ret = send_dest_cmd(dev_bsmu, BSMU_GET_SELF_CHECK_STATE);
    }
    return ret;
}
/**
 * @brief 发送设置电池簇批量参数消息
 * @param[in] para_id 参数ID
 * @retval
 * @note 
 */

static void send_batt_cluster_batch_para_msg( ) {
    unsigned char dcdc_no = 1;
    dev_inst_t* dev_dc_dc = init_dev_inst(DEV_DC_DC);


    if(dev_dc_dc == NULL){
        return;
    }
    
    while(dcdc_no <= BATT_CLUSTER_NUM)
    {
        handle_dc_dc_batch_para(dev_dc_dc, dcdc_no);
        //发消息通知DC_DC模块批量设置参数
        send_msg_to_sample(EXE_DEST_CMD_MSG, MOD_DC_DC, dcdc_no, DC_DC_SET_BATCH_PARA);
        dcdc_no++;
        dev_dc_dc++;
    }
    
}


Static void handle_dc_dc_batch_para(dev_inst_t* dev_dc_dc, unsigned char dcdc_no ) {
    /* TODO: 无簇控制器
    DC_DC_para_t para_val = {0};

    RETURN_IF_FAIL(dev_dc_dc != NULL);

    get_multi_para(&dc_dc_batch_para_set_pid[0], &para_val, sizeof(dc_dc_batch_para_set_pid)/sizeof(dc_dc_batch_para_set_pid[0]));
    memcpy((DC_DC_para_t*)dev_dc_dc->dev_data.para_data, &para_val, sizeof(DC_DC_para_t));

    calc_cluster_chg_curr();
    */
   return;
}


//发送消息给采样模块/设备模块
Static void send_msg_to_sample(unsigned int msg_id, unsigned char dest,  unsigned char dev_no, unsigned char cmd_id){
    module_msg_t msg_send;
    unsigned char msg_data[2] = {0};
    unsigned char (*send_data)[2] = malloc(2*sizeof(unsigned char));
    RETURN_IF_FAIL(send_data != NULL);

    msg_send.src = MOD_BSMU_COMM1;
    msg_send.dest = dest;
    msg_send.msg_id = msg_id;
    
    msg_data[0] = cmd_id;
    msg_data[1] = dev_no;
    rt_memcpy_s(send_data, sizeof(msg_data), &msg_data, sizeof(msg_data));
    msg_send.data = send_data;
    send_msg(&msg_send);
}


/**
 * @brief 设置命令结果回复处理
 * @param[in] dev_inst 设备实例
 * @param[in] cmd_buff 协议栈命令包
 * @retval    SUCCESSFUL 成功， FAILURE 失败
 * @note 打包回复结果
 */
/* 未使用，先注释
static short single_para_set_reply(unsigned char cmd_id, north_mgr_t* north_mgr, void* para_val) {
    unsigned short para_id = 0;
    para_mapping_t para_mapping_data = {0};

    para_mapping_data = get_para_mapping_by_bsmu_cmdid(cmd_id);
    para_id = para_mapping_data.para_id;
    RETURN_VAL_IF_FAIL(para_id != 0, FAILURE);
    
    if(set_one_para(para_id, para_val,0,0) != SUCCESSFUL){
        north_mgr->cmd_buff->rtn = BOTTOM_RTN_DATA_ERROR;
    } else {
        north_mgr->cmd_buff->rtn = BOTTOM_RTN_APP_CORRECT; 
    }

    if(send_dest_cmd(dev_bsmu, cmd_id) != SUCCESSFUL){
        return FAILURE;
    }
    return SUCCESSFUL;
}
*/

/* 未使用，先注释
static short single_cluster_para_set_reply(unsigned char cmd_id,  north_mgr_t* north_mgr, void* para_val, void* dc_dc_para_val) {
    unsigned short para_id = 0;
    para_mapping_t para_mapping_data = {0};

    para_mapping_data = get_para_mapping_by_bsmu_cmdid(cmd_id);
    para_id = para_mapping_data.para_id;
    RETURN_VAL_IF_FAIL(para_id != 0, FAILURE);
    
    if(set_one_para(para_id, para_val,0,0) != SUCCESSFUL){
        north_mgr->cmd_buff->rtn = BOTTOM_RTN_DATA_ERROR;
    } else {
        if (para_mapping_data.dcdc_cmd_id != 0) {
            send_batt_cluster_para_msg(para_mapping_data, dc_dc_para_val);
        }
        north_mgr->cmd_buff->rtn = BOTTOM_RTN_APP_CORRECT; 
    }
    if(send_dest_cmd(dev_bsmu, cmd_id) != SUCCESSFUL){
        return FAILURE;
    }
     return SUCCESSFUL;
}
*/

/* 未使用，先注释
static void send_batt_cluster_para_msg(para_mapping_t para_mapping, void* dc_dc_para_val) {
    unsigned char dcdc_no = 0;
    get_one_para(para_mapping.para_id, dc_dc_para_val);
    while(dcdc_no < BATT_CLUSTER_NUM)
    {   
        //发消息通知DC_DC模块设置参数
        send_msg_to_sample(EXE_DEST_CMD_MSG, MOD_DC_DC, dcdc_no, para_mapping.dcdc_cmd_id);
        dcdc_no++;
    }
}
*/

Static int  para_set_recv_process(dev_inst_t* dev_inst, north_mgr_t* north_mgr) {
    unsigned char cmd_id;
    BSMU_param_t bsmu_param_before = {0};
    BSMU_param_t bsmu_param_after = {0};
    BSMU_param_alm_level_t bsmu_param_alm_level_before = {0};;
    BSMU_param_alm_level_t bsmu_param_alm_level_after = {0};
    BSMU_param_alm_relay_t bsmu_param_alm_relay_before = {0};
    BSMU_param_alm_relay_t bsmu_param_alm_relay_after = {0};
    // dev_inst_t* dev_dc_dc = NULL;
    // DC_DC_para_t* DC_DC_para_data = NULL;
    int index = 0;

    RETURN_VAL_IF_FAIL(dev_inst != NULL && north_mgr != NULL && north_mgr->cmd_buff->cmd != NULL, FAILURE);
    
    cmd_id = north_mgr->cmd_buff->cmd->cmd_id;
    if(get_bsmu_batch_para(&bsmu_param_before) == DATA_INVALID)
    {
       return FAILURE; 
    }
    switch (cmd_id)
    {
        case BSMU_SET_BATCH_PARA:
            set_multi_para(&batch_para_set_pid[0], &bsmu_param_before,(sizeof(batch_para_set_pid)/sizeof(batch_para_set_pid[0])));
            send_batt_cluster_batch_para_msg();
            get_multi_para(&batch_para_set_pid[0], &bsmu_param_after,(sizeof(batch_para_set_pid)/sizeof(batch_para_set_pid[0])));
            north_mgr->cmd_buff->rtn = BOTTOM_RTN_APP_CORRECT; 
            rt_memcpy_s(&((bsmu_data_t*)dev_bsmu->dev_data.bsmu_data)->parse_bsmu_param, sizeof(BSMU_param_t), &bsmu_param_after, sizeof(BSMU_param_t));
            if(send_dest_cmd(dev_bsmu, BSMU_SET_BATCH_PARA) != SUCCESSFUL){
                return FAILURE;
            }
            break;
        case BSMU_SET_ALM_LEVEL:
           if(get_bsmu_alm_level_para(&bsmu_param_alm_level_before) != DATA_INVALID){
                set_multi_para(&batch_alm_level_set_pid[0], &bsmu_param_alm_level_before,(sizeof(batch_alm_level_set_pid)/sizeof(batch_alm_level_set_pid[0])));
                get_multi_para(&batch_alm_level_set_pid[0], &bsmu_param_alm_level_after,(sizeof(batch_alm_level_set_pid)/sizeof(batch_alm_level_set_pid[0])));
                north_mgr->cmd_buff->rtn = BOTTOM_RTN_APP_CORRECT;
                rt_memcpy_s(&((bsmu_data_t*)dev_bsmu->dev_data.bsmu_data)->parse_bsmu_param_alm_level, sizeof(BSMU_param_alm_level_t), &bsmu_param_alm_level_after, sizeof(BSMU_param_alm_level_t));
                if(send_dest_cmd(dev_bsmu, BSMU_SET_ALM_LEVEL) != SUCCESSFUL){
                    return FAILURE;
                }
            }
        
            break;

        case BSMU_SET_ALM_RELAY:
            if(get_bsmu_alm_relay_para(&bsmu_param_alm_relay_before) != DATA_INVALID){
                set_multi_para(&batch_alm_relay_set_pid[0], &bsmu_param_alm_relay_before,(sizeof(batch_alm_relay_set_pid)/sizeof(batch_alm_relay_set_pid[0])));
                get_multi_para(&batch_alm_relay_set_pid[0], &bsmu_param_alm_relay_after,(sizeof(batch_alm_relay_set_pid)/sizeof(batch_alm_relay_set_pid[0])));
                north_mgr->cmd_buff->rtn = BOTTOM_RTN_APP_CORRECT; 
                rt_memcpy_s(&((bsmu_data_t*)dev_bsmu->dev_data.bsmu_data)->parse_bsmu_param_alm_relay, sizeof(BSMU_param_alm_relay_t), &bsmu_param_alm_relay_after, sizeof(BSMU_param_alm_relay_t));
                if(send_dest_cmd(dev_bsmu, BSMU_SET_ALM_RELAY) != SUCCESSFUL){
                    return FAILURE;
                }
            }
            break;
            
        case  BSMU_SET_CLUSTER_CL_CURR://充电限电流的处理方式和其他簇参数不同，是需要计算得到对应的值以后下发给簇而不是直接用bsmu设置过来的值
            calc_cluster_chg_curr();
            for(index = 0; index < BATT_CLUSTER_NUM; index++) {
                send_msg_to_sample(EXE_DEST_CMD_MSG, MOD_DC_DC, index, DC_DC_SET_PARA_SET_CHG_LIMIT_CURR);
            }
            break;
        /*删除多余case原因：直接通过配置表格(bsmu_zte_can.c)的方式可以匹配到对应的参数ID，将数据写入*/
        default:
                    return FAILURE;
            break;
    }
    return SUCCESSFUL;
}


unsigned char get_bsmu_batch_para(BSMU_param_t* batch_para) {  
    if (batch_para == NULL)
        return DATA_INVALID;
    
    if (dev_bsmu->dev_data.valid_flag == DATA_INVALID)
        return DATA_INVALID;
    
    rt_memcpy_s(batch_para, sizeof(BSMU_param_t), &((bsmu_data_t*)dev_bsmu->dev_data.bsmu_data)->parse_bsmu_param, sizeof(BSMU_param_t));
    return DATA_VALID;

}

unsigned char get_bsmu_alm_level_para(BSMU_param_alm_level_t* alm_level_para) {
    if (alm_level_para == NULL)
        return DATA_INVALID;
    
    if (dev_bsmu->dev_data.valid_flag == DATA_INVALID)
        return DATA_INVALID;
    
    rt_memcpy_s(alm_level_para, sizeof(BSMU_param_alm_level_t), &((bsmu_data_t*)dev_bsmu->dev_data.bsmu_data)->parse_bsmu_param_alm_level, sizeof(BSMU_param_alm_level_t));
    return DATA_VALID;

}


unsigned char get_bsmu_alm_relay_para(BSMU_param_alm_relay_t* alm_relay_para) {
    if (alm_relay_para == NULL)
        return DATA_INVALID;
    
    if (dev_bsmu->dev_data.valid_flag == DATA_INVALID)
        return DATA_INVALID;
    
    rt_memcpy_s(alm_relay_para, sizeof(BSMU_param_alm_relay_t), &((bsmu_data_t*)dev_bsmu->dev_data.bsmu_data)->parse_bsmu_param_alm_relay, sizeof(BSMU_param_alm_relay_t));
    return DATA_VALID;

}

/* 未使用，先注释
static para_mapping_t get_para_mapping_by_bsmu_cmdid(unsigned char bsmu_cmd_id) {
    int i = 0;
    int para_mapping_len = 0;
    
    para_mapping_len = sizeof (para_mapping) / sizeof(para_mapping_t);
    for(i = 0; i < para_mapping_len; i++) {
        if (para_mapping[i].bsmu_cmd_id == bsmu_cmd_id) {
            return para_mapping[i];
        }
    }
    return para_mapping[para_mapping_len-1];
}
*/

static int update_recv_process(dev_inst_t* dev_inst, north_mgr_t* north_mgr) {
    unsigned short cmd_id = 0;
    unsigned char update_dev_type = 0;
    unsigned char update_dev_addr = 0;
    bottom_comm_cmd_head_t * req_head = NULL;
    int ret = SUCCESSFUL;

    RETURN_VAL_IF_FAIL(dev_inst != NULL && north_mgr != NULL && north_mgr->cmd_buff->cmd != NULL, FAILURE);

    cmd_id = north_mgr->cmd_buff->cmd->cmd_id;
    req_head = (bottom_comm_cmd_head_t *)north_mgr->cmd_buff->cmd->req_head;
    
    update_dev_type = req_head->res1.dst_dev;
    update_dev_addr = req_head->src_addr;

    switch(cmd_id) {
        case BSMU_UPDATE_DATA_TRIG:
            if(send_dest_cmd(dev_bsmu, BSMU_UPDATE_DATA_TRIG) != SUCCESSFUL){
                return FAILURE;
            }
            break;
        case BSMU_UPDATE_DATA:
            ret = handle_update_data(update_dev_type , update_dev_addr);
            RETURN_VAL_IF_FAIL(ret == SUCCESSFUL, FAILURE);
            break;
        case BSMU_UPDATE_DATA_INTERRUPT:
            if(send_dest_cmd(dev_bsmu, BSMU_UPDATE_DATA_INTERRUPT) != SUCCESSFUL){
                return FAILURE;
            }
            break;
        case BSMU_UPDATE_TRIG:
             //升级触发帧应答
             if(send_dest_cmd(dev_bsmu, BSMU_UPDATE_TRIG) != SUCCESSFUL){
                return FAILURE;
            }
            handle_update_trig(update_dev_type, update_dev_addr);
            break;
        default:
            return FAILURE;
    }

    return SUCCESSFUL;
}

static int handle_update_data(unsigned char update_dev_type, unsigned char update_dev_addr) {
    trsdata_ctr_inf_t trsdata_ctr_inf = {0};
    bcmu_update_manage_t* bcmu_update_manage;
    int ret = SUCCESSFUL;
    const unsigned char time_waite = 10; //ms
    unsigned char localhost_addr = 1;    // TODO:获取BCMU站址分配地址
    int count_delay = (CONCURRENT_UPGRADE_TIMEOUT / time_waite) * localhost_addr;
    int count = 0;

    trsdata_ctr_inf = get_trsdata_ctr_info();
    bcmu_update_manage = get_bcmu_update_manage();
    if (update_dev_type == BOTTOM_BMU_TYPE && update_dev_addr == 0) {
       //广播 若响应正确，不回应
        while((get_pin(BCMU_ADDR_ASSSIGN_DI_PIN) != (bcmu_update_manage->di_value ^ 0x01))) {
            if (count >= count_delay) {
                break;
            } else {
                count++;
            }
            rt_thread_delay(time_waite);
        }

        bcmu_update_manage->di_value ^= 0x01;

        if (trsdata_ctr_inf.rtn == BOTTOM_RTN_APP_CORRECT) {
            set_pin(ADDR_ASSSIGN_DO_PIN, bcmu_update_manage->do_value ^ 0x01);        //翻转下一个BCMU di，todo 需要确定ADDR_ASSSIGN_DO_PIN
            bcmu_update_manage->do_value ^= 0x01;
            return ret;
        }
    }

    if(send_dest_cmd(dev_bsmu, BSMU_UPDATE_DATA) != SUCCESSFUL){
        ret = FAILURE;
    }

    if (update_dev_type == BOTTOM_BMU_TYPE && update_dev_addr == 0) {
        set_pin(ADDR_ASSSIGN_DO_PIN, bcmu_update_manage->do_value ^ 0x01);        //翻转下一个BCMU di，todo 需要确定ADDR_ASSSIGN_DO_PIN
        bcmu_update_manage->do_value ^= 0x01;
    }

    return ret;
}

static void handle_update_trig(unsigned char update_dev_type, unsigned char update_dev_addr) {
    trig_ctr_inf_t trig_ctr_inf = {0};
    cluster_topology_t topo;

    trig_ctr_inf = get_trig_info();

    if (trig_ctr_inf.rtn != BOTTOM_RTN_APP_CORRECT)
        return;

    get_cluster_topology(&topo);
    switch(update_dev_type) {
        //当设备类型为0，表示升级自己
        case 0:
        case BOTTOM_BMU_TYPE:
            //todo:升级BMU的流程
            if (topo.topo_valid == TOPO_VALID)
                south_dev_update(DEV_BMU, update_dev_addr, topo.mod_count);
            break;
   }
    return;
}

static int  ctrl_recv_process(dev_inst_t* dev_inst, north_mgr_t* north_mgr) {
    dev_inst_t* bsmu_inst = NULL;
    unsigned char cmd_id;
    int ret = 0;
    module_msg_t msg_send = {MOD_BSMU_COMM1, MOD_BATT_MANAGE/*MOD_BATT_MANAGE*/, CTRL_POWER_ON_MSG, NULL};

    bsmu_inst = (dev_inst_t*)dev_inst;
    RETURN_VAL_IF_FAIL(bsmu_inst != NULL, FAILURE);

    cmd_id = north_mgr->cmd_buff->cmd->cmd_id;
    switch (cmd_id)
    {
        case BSMU_CTRL_POWER_ON:
            ret = send_msg(&msg_send);
            break;
        
        default:
            break;
    }
    return ret;
}

static void exe_north_heartbeat(unsigned char inst_id)
{
    static rt_uint32_t last_time = 0;
    rt_int32_t  ltdif = 0;
    rt_uint32_t curr_time = get_sys_runtime();

    if (last_time == 0) {
        last_time = curr_time;
        if(inst_id == LINK_BSMU){
            send_heart_beat_msg(MOD_BSMU_COMM1);
        }else{
            send_heart_beat_msg(MOD_BSMU_COMM2);
        }
        return;
    }

    ltdif = curr_time - last_time;
    if (ltdif < 0) {
        ltdif = (~ltdif) + 1;
    }

    if (ltdif >= 5000) {
        if(inst_id == LINK_BSMU){
            send_heart_beat_msg(MOD_BSMU_COMM1);
        }else{
            send_heart_beat_msg(MOD_BSMU_COMM2);
        }
        last_time = curr_time;
    }
}







