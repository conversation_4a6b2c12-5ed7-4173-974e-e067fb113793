/* Started by <PERSON><PERSON>oder, pid:p84c5ff36dq9eca1401d084bd0696c5342e6c70a */
#include "snmp_comm_status.h"
#include "realAlarm.h"
#include "common.h"
#include "para.h"

#define TIME_SNMP_COMM_MAX (60 * 6)

static uint32_t snmp_recv_counter = 0;
static uint32_t previous_snmp_recv_counter = 0;

static BOOLEAN snmp_comm_status = False;

static uint32_t snmp_comm_timer = 0;

uint32_t get_snmp_recv_count(void)
{
    return snmp_recv_counter;
}

void inc_snmp_recv_count(void)
{
    ++snmp_recv_counter;
}

void clear_snmp_recv_count(void)
{
    snmp_recv_counter = 0;
}

BOOLEAN get_snmp_comm_status(void)
{
    return snmp_comm_status;
}

void set_snmp_comm_status(BOOLEAN status)
{
    snmp_comm_status = !!status;
}

void snmp_comm_timer_count(void)
{
    const uint32_t current_snmp_recv_counter = get_snmp_recv_count();
    if (current_snmp_recv_counter != previous_snmp_recv_counter)
    {
        set_snmp_comm_status(True);
        DealDeviceUnlock(AUTO_UNLOCK);
        snmp_comm_timer = 0;
        previous_snmp_recv_counter = current_snmp_recv_counter;
    }
    else
    {
        TimerPlus(snmp_comm_timer, TIME_SNMP_COMM_MAX);
        if (TimeOut(snmp_comm_timer, TIME_SNMP_COMM_MAX))
        {
            set_snmp_comm_status(False);
        }
    }
}
/* Ended by AICoder, pid:p84c5ff36dq9eca1401d084bd0696c5342e6c70a */