#include <stdio.h>
#include <string.h>
#include "protocol_layer.h"
#include "dev_dc_ac_1363.h"
#include <string.h>
#include <stdlib.h>
#include <rtthread.h>
#include <fal.h>
#include "utils_thread.h"
#include "device_type.h"
#include "msg.h"
#include "cmd.h"
#include "data_type.h"
#include "sps.h"
#include "utils_server.h"
#include "msg_id.h"
#include "device_num.h"
#include "utils_data_transmission.h"
#include "utils_data_type_conversion.h"
#include "utils_string.h"
#include "utils_time.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "alarm_manage.h"
#include "para_id_in.h"
#include "his_record.h"
#include "time.h"
#include "linklayer.h"
#include "sha256.h"
#include "base64.h"
#include "utils_rtthread_security_func.h"
#include "ee_public_info.h"
#include "para_common.h"
#include "north_parallel_modbus.h"
#include "msh.h"
#include "app_config.h"
#include "para_to_realdata_id.h"
#include "unified_id_interface.h"
#include "time_manage.h"

unsigned char g_restore_app_paswrd_flag = FALSE;
unsigned char g_app_rtn = 0;
unsigned char g_self_lock = 0;     // 自锁标记
unsigned char is_common_name = TRUE; // TRUE:普通账户登录  ，FALSE:管理员账户登录
unsigned char g_passwd_overdue_flag = FALSE;  //APP密码超期标志位， FALSE:未超期，TRUE:超期
static int s_lock_count = 0;
time_t g_time = 0;
static rt_timer_t g_app_lock_timer = NULL;
static rt_timer_t g_app_count_timer = NULL;
static rt_timer_t g_app_chg_passwd_timer = NULL;
static unsigned int g_sid_list[PARA_SID_LIST_MAX] RAM_SECTION_BSS = {0};
unsigned char g_rtn = 0;
static csc_psc_info_t g_csc_psc_info = {0};
debug_cmd_t debug_cmd_tab[] = 
{
    {SOUTH_PKT_CNT,     "print_south_pkt_cnt %s %s %s"},
    {NORTH_PKT_CNT,     "print_north_pkt_cnt %s %s %s"},
    {GET_ONE_DATA,      "probe_get_one_data %s %s %s"},
    {SET_ONE_DATA,      "probe_set_one_data %s %s %s"},
    {ALARM_PRINT,       "alarm_print %s %s %s"},
    {PRINT_DEV_ID,      "print_device_id %s %s %s"},
    {PRINT_SUB_TOPIC,   "print_subdcribe_topic %s %s %s"},
    {CAN_STATS,         "canstat %s %s %s"},
    {FREE,              "free %s %s %s"},
    {DF,                "df %s %s %s"},
    {NETSTAT,           "netstat %s %s %s"},
    {IFCONFIG,          "ifconfig %s %s %s"},
    {UARTSTAT,          "uartstat %s %s %s"},
    {READ_MCU_REG,      "read_mcu_reg %s %s %s"},
    {STORAGE_COUNT,     "print_storage_count %s %s %s"},
};

static ctrl_cmd_1363_t s_ctrl_cmd[] RAM_SECTION =
{    /*cmd_type                     ctrl_id                               ctrl_status                           ctrl_cmd_id          is_south_ctrl_cmd     info[20]             func */
    {RESET_CSU_CMD,                 DAC_CTRL_ID_RESET_CSU,                0,                                    0,                   FALSE,               "reset csu"        ,   reset_csu},
    {START_IV_SCAN_CMD,             DAC_CTRL_ID_PV_CTRL_ORDER,            CMD_IV_START_CTRL,                    DC_AC_IV_CTRL_ORDER, TRUE,                "start iv scan"    ,   start_iv_func},
    {STOP_IV_SCAN_CMD,              DAC_CTRL_ID_PV_CTRL_ORDER,            CMD_IV_CLOSE_CTRL,                    DC_AC_IV_CTRL_ORDER, TRUE,                "close iv scan"    ,   stop_iv_callback},
    // {START_PRODUCT_MODE_CMD,        DAC_CTRL_ID_PRODUCT ,                 CMD_START_PRODUCT_MODE,               DC_AC_PRODUCT_ORDER, TRUE,                "start product"    ,            NULL},
    // {CLOSE_PRODUCT_MODE_CMD,        DAC_CTRL_ID_PRODUCT ,                 CMD_CLOSE_PRODUCT_MODE,               DC_AC_PRODUCT_ORDER, TRUE,                "close product"    ,            NULL},
    // {TRIGGER_FAULT_RECORD,          DAC_CTRL_ID_PRODUCT,                  CMD_TRIGGER_FAULT_RECORD,             DC_AC_PRODUCT_ORDER, TRUE,                "trigger fault record"  ,       NULL},
    {START_PRE_DRIVER_TEST,         DAC_CTRL_ID_PRODUCT,                  CMD_START_PRE_DRIVER_TEST,            DC_AC_PRODUCT_ORDER, TRUE,                "start pre test"   ,            NULL},
    {CLOSE_PRE_DRIVER_TEST,         DAC_CTRL_ID_PRODUCT,                  CMD_CLOSE_PRE_DRIVER_TEST,            DC_AC_PRODUCT_ORDER, TRUE,                "close pre test"   ,            NULL},
    {START_POSTERIOR_DRIVER_TEST,   DAC_CTRL_ID_PRODUCT,                  CMD_START_POSTERIOR_DRIVER_TEST,      DC_AC_PRODUCT_ORDER, TRUE,                "start poster test",            NULL},
    {CLOSE_POSTERIOR_DRIVER_TEST,   DAC_CTRL_ID_PRODUCT,                  CMD_CLOSE_POSTERIOR_DRIVER_TEST,      DC_AC_PRODUCT_ORDER, TRUE,                "close poster test",            NULL},
    // {START_AUXI_SOURCE_TEST,        DAC_CTRL_ID_PRODUCT,                  CMD_START_AUXI_SOURCE_TEST,           DC_AC_PRODUCT_ORDER, TRUE,                "start auxi test"  ,            NULL},
    // {CLOSE_AUXI_SOURCE_TEST,        DAC_CTRL_ID_PRODUCT,                  CMD_CLOSE_AUXI_SOURCE_TEST,           DC_AC_PRODUCT_ORDER, TRUE,                "close auxi test"  ,            NULL},
    {RESTORE_FACT_CMD,              DAC_CTRL_ID_RESTORE_FACTORY,          0,                                    0,                   FALSE,               ""        ,            deal_restore_factory},
    // {FAN_CTRL_TEST,                 DAC_CTRL_ID_PRODUCT,                  CMD_FAN_CTRL_TEST,                    DC_AC_PRODUCT_ORDER, TRUE,                "fan ctrl test"    ,            NULL},
    // {RELAY_CTRL_TEST,               DAC_CTRL_ID_PRODUCT,                  CMD_RELAY_CTRL_TEST,                  DC_AC_PRODUCT_ORDER, TRUE,                "relay ctrl test"  ,            NULL},
    {START_SHIELD_CAN_COMM,         DAC_CTRL_ID_SHIELD_CAN_COMM,          CMD_START_SHIELD_CAN_COMM,            0,                   FALSE,               "start shield can" ,   start_shield_can_comm},
    {STOP_SHIELD_CAN_COMM,          DAC_CTRL_ID_SHIELD_CAN_COMM,          CMD_STOP_SHIELD_CAN_COMM,             0,                   FALSE,               "stop shield can"  ,   stop_shield_can_comm},
    {CLEAN_HIS_ENERGY,              DAC_CTRL_ID_CLEAN_HIS_ENERGY,         0,                                    0,                   FALSE,               "clean his energy" ,   send_clean_his_energy_msg},
    {CLEAN_ALARM,                   DAC_CTRL_ID_CLEAN_ALARM,              0,                                    0,                   FALSE,               "clean alarm"      ,   send_clean_alarm_msg},
    {RS485_STAT,                    DAC_CTRL_ID_RS485_STAT,               0,                                    0,                   FALSE,               "RS485 statistic"  ,   send_rs485_stat_msg},
    // {STOP_FAN_CTRL_TEST,            DAC_CTRL_ID_PRODUCT,                  CMD_STOP_FAN_CTRL_TEST,               DC_AC_PRODUCT_ORDER, TRUE,                "stop fan ctrl test",         NULL},
    // {START_RELAY_TEST,              DAC_CTRL_ID_PRODUCT,                  CMD_START_RELAY_TEST,                 DC_AC_PRODUCT_ORDER, TRUE,                "start relay test",           NULL},
    // {STOP_RELAY_TEST,               DAC_CTRL_ID_PRODUCT,                  CMD_STOP_RELAY_TEST,                  DC_AC_PRODUCT_ORDER, TRUE,                "stop relay test",            NULL},
    {RESTORE_FAULT_RECORD_SET,      DAC_CTRL_ID_PV_CTRL_ORDER,            CMD_RESTORE_FAULT_RECORD_SET,         DC_AC_IV_CTRL_ORDER, TRUE,                "restore fault record",       NULL},
    {FORMAT_FILE_SYSTEM,            DAC_CTRL_ID_FORMAT_FILE_SYSTEM,       0,                                    0,                   FALSE,               "format file system"  ,format_file_system},
    {CLEAR_AFCI_FAIL_ALM,           DAC_CTRL_ID_PV_CLEAR_ALARM,            CMD_CLEAR_AFCI_FAIL_ALM     ,        DC_AC_PV_ALARM_CLEAR_ORDER, TRUE,         "clear AFCI alm",      NULL},
    {CLEAR_DC_ARC_FALUT_ALM,        DAC_CTRL_ID_PV_CLEAR_ALARM,            CMD_CLEAR_DC_ARC_FALUT_ALM  ,        DC_AC_PV_ALARM_CLEAR_ORDER, TRUE,         "clear DC arc alm",    NULL},
    {CLEAR_INSULATION_LOW_ALM,      DAC_CTRL_ID_PV_CLEAR_ALARM,            CMD_CLEAR_INSULATION_LOW_ALM,        DC_AC_PV_ALARM_CLEAR_ORDER, TRUE,         "clear insulation alm",NULL},
    {RESTART_SYSTEM_CMD,            DAC_CTRL_ID_SYSTEM_RESTART,           0,                                    0,                   FALSE,               "restart system",      restart_system},


    {TRIGGER_FAULT_RECORD,          DAC_CTRL_ID_DUBUG_CONTROL_COMMAND,    CMD_TRIGGER_FAULT_RECORD,             DC_AC_DEBUG_CTRL_ORDER,  TRUE,                "trig fault record"  ,       NULL},
    {START_AUXI_SOURCE_TEST,        DAC_CTRL_ID_PRODUCT,                  CMD_START_AUXI_SOURCE_TEST,           DC_AC_PRODUCT_ORDER, TRUE,                "start auxi test"  ,            NULL},
    {CLOSE_AUXI_SOURCE_TEST,        DAC_CTRL_ID_PRODUCT,                  CMD_CLOSE_AUXI_SOURCE_TEST,           DC_AC_PRODUCT_ORDER, TRUE,                "close auxi test"  ,            NULL},
    {START_POST_DUTY_CYCLE_TEST,    DAC_CTRL_ID_PRODUCT,                  CMD_POST_DUTY_CYCLE_TEST,             DC_AC_PRODUCT_ORDER, TRUE,                "post duty test"  ,            NULL}, 
    {START_INTERNAL_FAN_TEST,       DAC_CTRL_ID_PRODUCT,                  CMD_START_INTERNAL_FAN_TEST,          DC_AC_PRODUCT_ORDER, TRUE,                "start in fan test",      NULL},
    {STOP_INTERNAL_FAN_TEST,        DAC_CTRL_ID_PRODUCT,                  CMD_STOP_INTERNAL_FAN_TEST,           DC_AC_PRODUCT_ORDER, TRUE,                "stop in fan test",       NULL},
    {START_RELAY_TEST,              DAC_CTRL_ID_PRODUCT,                  CMD_START_RELAY_TEST,                 DC_AC_PRODUCT_ORDER, TRUE,                "start relay test",           NULL},
    {STOP_RELAY_TEST,               DAC_CTRL_ID_PRODUCT,                  CMD_STOP_RELAY_TEST,                  DC_AC_PRODUCT_ORDER, TRUE,                "stop relay test",            NULL},
    {START_EXTERNAL_FAN_TEST,       DAC_CTRL_ID_PRODUCT,                  CMD_START_EXTERNAL_FAN_TEST,          DC_AC_PRODUCT_ORDER, TRUE,                "start ex fan test",      NULL},
    {STOP_EXTERNAL_FAN_TEST,        DAC_CTRL_ID_PRODUCT,                  CMD_STOP_EXTERNAL_FAN_TEST,           DC_AC_PRODUCT_ORDER, TRUE,                "stop ex fan test",       NULL},
    {START_ISOLATION_POWER,         DAC_CTRL_ID_PRODUCT,                  CMD_START_ISOLATION_POWER,            DC_AC_PRODUCT_ORDER, TRUE,                "start isol power",       NULL},
    {STOP_ISOLATION_POWER,          DAC_CTRL_ID_PRODUCT,                  CMD_STOP_ISOLATION_POWER,             DC_AC_PRODUCT_ORDER, TRUE,                "stop isol power",        NULL},
    {START_IMPEDANCE_TEST,          DAC_CTRL_ID_PRODUCT,                  CMD_START_IMPEDANCE_TEST,             DC_AC_PRODUCT_ORDER, TRUE,                "start imped test",        NULL},
    {STOP_IMPEDANCE_TEST,           DAC_CTRL_ID_PRODUCT,                  CMD_STOP_IMPEDANCE_TEST,              DC_AC_PRODUCT_ORDER, TRUE,                "stop imped test",         NULL},
    {CLOSE_PID_CTRL,                DAC_CTRL_ID_PRODUCT,                  CMD_CLOSE_PID_CTRL,                   DC_AC_PRODUCT_ORDER, TRUE,                "close PID ctrl",              NULL},
    {DISCONN_PID_CTRL,              DAC_CTRL_ID_PRODUCT,                  CMD_DISCONN_PID_CTRL,                 DC_AC_PRODUCT_ORDER, TRUE,                "disconn PID ctrl",            NULL},
    {CLOSE_GROUND_RELAY,            DAC_CTRL_ID_PRODUCT,                  CMD_CLOSE_GROUND_RELAY,               DC_AC_PRODUCT_ORDER, TRUE,                "close ground relay",          NULL},
    {DISCONN_GROUND_RELAY,          DAC_CTRL_ID_PRODUCT,                  CMD_DISCONN_GROUND_RELAY,             DC_AC_PRODUCT_ORDER, TRUE,                "disconn ground relay",        NULL},
    {CLOSE_THEFT_RELAY,             DAC_CTRL_ID_PRODUCT,                  CMD_CLOSE_THEFT_RELAY,                DC_AC_PRODUCT_ORDER, TRUE,                "close theft relay",           NULL},
    {DISCONN_THEFT_RELAY,           DAC_CTRL_ID_PRODUCT,                  CMD_DISCONN_THEFT_RELAY,              DC_AC_PRODUCT_ORDER, TRUE,                "disconn theft relay",         NULL},
    {NORMAL_MODE,                   DAC_CTRL_ID_WORK_MODE_CTRL_CMD,       CMD_NORMAL_MODE,                      DC_AC_WORK_MODE_CTRL_ORDER, TRUE,                    "normal mode",                 NULL},
    {PRODUCT_MODE,                  DAC_CTRL_ID_WORK_MODE_CTRL_CMD,       CMD_PRODUCT_MODE,                     DC_AC_WORK_MODE_CTRL_ORDER, TRUE,                    "product test mode",           NULL},
    {RD_DEBUG_MODE,                 DAC_CTRL_ID_WORK_MODE_CTRL_CMD,       CMD_RD_DEBUG_MODE,                    DC_AC_WORK_MODE_CTRL_ORDER, TRUE,                    "RD test mode",                NULL},
    {BOARD_RD_DEBUG_MODE,           DAC_CTRL_ID_WORK_MODE_CTRL_CMD,       CMD_BOARD_RD_DEBUG_MODE,              DC_AC_WORK_MODE_CTRL_ORDER, TRUE,                    "board RD test mode",          NULL},
    {START_IMPEDENCE_DIA,           DAC_CTRL_ID_PV_CTRL_ORDER,            CMD_START_IMPEDENCE_DIA,              DC_AC_IV_CTRL_ORDER, TRUE,                "start impedence test",        NULL},
    {START_AFCI,                    DAC_CTRL_ID_PV_CTRL_ORDER,            CMD_START_AFCI,                       DC_AC_IV_CTRL_ORDER, TRUE,                "start afci test",             NULL},
    {OPEN_FT_OUTPUT_SIGNAL,         DAC_CTRL_ID_PRODUCT,                  CMD_OPEN_FT_OUTPUT_SIGNAL ,           DC_AC_PRODUCT_ORDER, TRUE,                "open ft outsignal",          NULL},
    {CLOSE_FT_OUTPUT_SIGNAL,        DAC_CTRL_ID_PRODUCT,                  CMD_CLOSE_FT_OUTPUT_SIGNAL,           DC_AC_PRODUCT_ORDER, TRUE,                "close ft outsignal",        NULL},
    {SHIELD_FAULT_RECORD,           DAC_CTRL_ID_PRODUCT,                  CMD_SHIELD_FAULT_RECORD,              DC_AC_PRODUCT_ORDER, TRUE,                "shield fault record",           NULL},
    {OPEN_FT_FAULT_RECORD,          DAC_CTRL_ID_PRODUCT,                  CMD_OPEN_FT_FAULT_RECORD,             DC_AC_PRODUCT_ORDER, TRUE,                "open fault record",         NULL},
    {OPEN_LEAKAGE_CUR_SELF_CHECK,   DAC_CTRL_ID_PRODUCT,                  CMD_OPEN_LEAKAGE_CUR_SELF_CHECK,      DC_AC_PRODUCT_ORDER, TRUE,                "open leak-cur check",          NULL},
    {CLOSE_LEAKAGE_CUR_SELF_CHECK,  DAC_CTRL_ID_PRODUCT,                  CMD_CLOSE_LEAKAGE_CUR_SELF_CHECK,     DC_AC_PRODUCT_ORDER, TRUE,                "close leak-cur check",        NULL},
    {CLEAN_AFCI_LOG,                DAC_CTRL_ID_PV_CTRL_ORDER,            CMD_CLEAN_AFCI_LOG,                   DC_AC_IV_CTRL_ORDER, TRUE,                "clean afci log"    ,   clean_afci_file},
    {GET_AFCI_LOG,                  DAC_CTRL_ID_PV_CTRL_ORDER,            CMD_GET_AFCI_LOG,                     DC_AC_IV_CTRL_ORDER, TRUE,                "get afci log"    ,   get_afci_file},
    {CLEAN_HIS_DATA,                DAC_CTRL_ID_CLEAN_HIS_DATA,           0,                                    0,                   FALSE,               "clean his_data",   send_clean_his_data_msg},
    {CLEAN_HIS_EVENT,               DAC_CTRL_ID_CLEAN_HIS_EVENT,          0,                                    0,                   FALSE,               "clean his_event",  send_clean_his_event_msg},
    {CLEAN_IV_DATA,                 DAC_CTRL_ID_CLEAN_IV_DATA,            0,                                    0,                   FALSE,               "clean iv data",    send_clean_iv_data_msg},
    {CLEAN_FAULT_RECORD_DATA,       DAC_CTRL_ID_FAULT_RECORD_DATA,        0,                                    0,                   FALSE,               "clean fault record", send_clean_fault_record_msg},
    {DELETE_HIS_ENERGY_DATA,        DAC_CTRL_ID_DELETE_HIS_ENERGY,        0,                                    0,                   FALSE,               "delete his energy", send_delete_his_energy_msg},

};

cmd_id_map_t g_prot_cmd_map[] RAM_SECTION =
{
    { 0x15,SET_PROT_PARA_DATA1 , DC_AC_SET_PROTECT_L1_PARA_DATA },//一级保护等级
    { 0x16,SET_PROT_PARA_DATA2 , DC_AC_SET_PROTECT_L2_PARA_DATA },//二级保护等级
    { 0x17,SET_PROT_PARA_DATA3 , DC_AC_SET_PROTECT_L3_PARA_DATA },
    { 0x18,SET_PROT_PARA_DATA4 , DC_AC_SET_PROTECT_L4_PARA_DATA },
    { 0x19,SET_PROT_PARA_DATA5 , DC_AC_SET_PROTECT_L5_PARA_DATA },
    { 0x1A,SET_PROT_PARA_DATA6 , DC_AC_SET_PROTECT_L6_PARA_DATA },
    {0},
};
   
cmd_id_map_t g_curve_cmd_map[] RAM_SECTION =
{ 
    //
    {0x06,SET_CURVE_PARA_DATA1 , DC_AC_SET_LVRT_CC_PARA_DATA },//特征曲线
    {0x0E,SET_CURVE_PARA_DATA2 , DC_AC_SET_P_PN_CC_PARA_DATA },//P-COS曲线
    {0x0F,SET_CURVE_PARA_DATA3 , DC_AC_SET_Q_U_CC_PARA_DATA  },//Q-U曲线
    {0x10,SET_CURVE_PARA_DATA4 , DC_AC_SET_PF_U_CC_PARA_DATA },//PF曲线
    {0x1C,SET_CURVE_PARA_DATA5 , DC_AC_SET_HVRT_CC_PARA_DATA },//HVRT曲线
    {0},
};

cmd_id_map_t g_sys_cmd_map[] RAM_SECTION =
{
    // {0x80,}
    {0x01, SET_SYSPARA_DATA11,DC_AC_SET_REMOTE_CTRL_PARA_DATA},            //控制参数
    {0x02, SET_SYSPARA_DATA1, DC_AC_SET_POWER_GRID_PARA_DATA },     //电网参数
    {0x03, SET_SYSPARA_DATA2, DC_AC_SET_CHARA_PARA_DATA },          //特性参数
    {0x04, SET_SYSPARA_DATA3, DC_AC_SET_MPPT_PARA_DATA },           //MPPT参数
    {0x05, SET_SYSPARA_DATA4, DC_AC_SET_LVRT_PARA_DATA },           //LVRT参数
    {0x07, SET_SYSPARA_DATA5, DC_AC_SET_HVRT_PARA_DATA },           //HVRT参数
    {0x08, SET_SYSPARA_DATA6, DC_AC_SET_PID_PARA_DATA  },           //PID参数
    {0x09, SET_SYSPARA_DATA7, DC_AC_SET_AFCI_PARA_DATA },           //AFCI参数
    {0x13, SET_SYSPARA_DATA8, DC_AC_SET_CCP_FEED_OVER_PARA_DATA },  //并网点控制
    {0x14, SET_SYSPARA_DATA9, DC_AC_SET_PROTECT_PARA_DATA },        //保护参数
    {0x1B, SET_SYSPARA_DATA10, DC_AC_SET_OTHER_PPARA_DATA },        //海拔高度
    {0x1C, SET_SYSPARA_DATA12, DC_AC_SET_POWER_GRID_PARA_DATA },    //电网标准码参数
    {0x1D, SET_SYSPARA_DATA13, DC_AC_SET_STRING_IN_DETECTION_DATA},  //组串接入检测
    {0x1E, SET_SYSPARA_DATA14, DC_AC_SET_OVER_UNDER_FREQ_DERATE_DATA},  // 过欠频降额参数设置

    {0},
};

cmd_id_map_t g_power_cmd_map[] RAM_SECTION =
{
    {0x0A,SET_POWER_PARA_DATA1 , DC_AC_SET_POWER_REGULATION_PARA_DATA },//功率调节参数
    {0x0B,SET_POWER_PARA_DATA2 , DC_AC_SET_COMM_FAIL_SAFE_PARA_DATA },//通信断链失效保护参数
    {0x0C,SET_POWER_PARA_DATA3 , DC_AC_SET_ACT_REGULATION_PARA_DATA },//有功功率调节参数
    {0x0D,SET_POWER_PARA_DATA4 , DC_AC_SET_REACT_REGULATION_PARA_DATA },//无功功率调节
    {0x11,SET_POWER_PARA_DATA5 , DC_AC_SET_CCP_ACT_PARA_DATA },//并网点控制-有功功率
    {0x12,SET_POWER_PARA_DATA6 , DC_AC_SET_CCP_REACT_PARA_DATA },//并网点控制-无功功率
    {0},
};

//TODO:设置参数 矫正参数和生产参数南向没有分段
cmd_id_map_t g_adjust_cmd_map[] RAM_SECTION =
{
    {0x50,SET_ADJUST_PARA_DATA1 , DC_AC_SET_ADG_PARA_DATA },//输出电压比例校正参数
    {0x51,SET_ADJUST_PARA_DATA2 , DC_AC_SET_ADG_PARA_DATA },//bus电压比例校正
    {0x52,SET_ADJUST_PARA_DATA3 , DC_AC_SET_ADG_PARA_DATA },//MPPT1比例校正
    {0x53,SET_ADJUST_PARA_DATA4 , DC_AC_SET_ADG_PARA_DATA },//MPPT2比例校正
    {0x54,SET_ADJUST_PARA_DATA5 , DC_AC_SET_ADG_PARA_DATA },//MPPT3比例校正
    {0x55,SET_ADJUST_PARA_DATA6 , DC_AC_SET_ADG_PARA_DATA },//MPPT4比例校正
    {0x56,SET_ADJUST_PARA_DATA7 , DC_AC_SET_ADG_PARA_DATA },//辅控比例校正
    {0x57,SET_ADJUST_PARA_DATA8 , DC_AC_SET_ADG_PARA_DATA}, //漏电流比例校正
    {0x58,SET_ADJUST_PARA_DATA9 , DC_AC_SET_ADG_PARA_DATA}, //直流分量比例校正
    {0},
};

cmd_id_map_t g_produce_cmd_map[] RAM_SECTION =
{
   // {0x01,SET_PRODUCE_PARA_DATA1 ,    DC_AC_SET_PRO_PARA_DATA },//设备条码
    {0x90,SET_PRODUCE_PARA_DATA2 ,      DC_AC_SET_PRO_PARA_DATA },//生产信息写入
    {0x91,SET_PRODUCE_PARA_DATA3 ,      DC_AC_NOT_SEND_SOUTH_ID},//资产信息写入
    {0x92,SET_PRODUCE_PARA_DATA4 ,      DC_AC_SET_PRO_PARA_DATA },//产品信息
    {0x93,SET_HARDWARE_VERSION_PARA,    DC_AC_SET_PRO_PARA_DATA},
    {0},
};

cmd_id_map_t g_record_cmd_map[] RAM_SECTION =
{
    {0x60,DAC_SET_FAULT_RECORD1_PARA , DC_AC_SET_FAULT_RECORD1_PARA },//录波1参数
    {0x61,DAC_SET_FAULT_RECORD2_PARA , DC_AC_SET_FAULT_RECORD2_PARA },//录波2参数
    {0},
};
cmd_id_map_t g_moniter_cmd_map[] RAM_SECTION =
{
    {0x01,DAC_SET_MONITER_PARA1, DC_AC_NOT_SEND_SOUTH_ID ,set_time_info},   //时间信息
    {0x02,DAC_SET_MONITER_PARA2, DC_AC_NOT_SEND_SOUTH_ID ,set_rs485_flag},  //rs485配置
    {0x03,DAC_SET_MONITER_PARA3, DC_AC_NOT_SEND_SOUTH_ID},                  //wlan配置
    {0x04,DAC_SET_MONITER_PARA4, DC_AC_NOT_SEND_SOUTH_ID},                  //通讯棒问题
    {0x05,DAC_SET_MONITER_PARA5, DC_AC_NOT_SEND_SOUTH_ID},                  //mqtt配置
    {0x06,DAC_SET_MONITER_PARA8, DC_AC_NOT_SEND_SOUTH_ID},                  //第三方网管配置
    {0x07,DAC_SET_CSC_VERSION_PARA, DC_AC_NOT_SEND_SOUTH_ID},               //csc版本信息
    {0x08,DAC_SET_PSC_VERSION_PARA, DC_AC_NOT_SEND_SOUTH_ID},               //psc版本信息
    {0x09,DAC_SET_MONITER_PARA9, DC_AC_NOT_SEND_SOUTH_ID},                  //设备位置信息
    // {0x0B,DAC_SET_DELAY_UPDATE, DC_AC_NOT_SEND_SOUTH_ID},                   // 延迟升级
    {0x0C,DAC_SET_MONITER_PARA6, DC_AC_NOT_SEND_SOUTH_ID},                  //APP请求登录
    {0x0D,DAC_SET_MONITER_PARA7, DC_AC_NOT_SEND_SOUTH_ID},                  //APP修改密码
    {0x0F,DAC_SET_USER_AUTH_TIME, DC_AC_NOT_SEND_SOUTH_ID},                 //APP user授权时间
};
cmd_id_map_t g_alm_relay_map[] RAM_SECTION =
{
    {0x01, DAC_SET_MPPT_OVER_VOLT_RELAY                  ,0},
    {0x02, DAC_SET_BUS_OVER_VOLT_RELAY                   ,0},
    {0x03, DAC_SET_POS_BUS_OVER_VOLT_RELAY               ,0},
    {0x04, DAC_SET_NEG_BUS_OVER_VOLT_RELAY               ,0},
    {0x05, DAC_SET_BUS_VOLT_IMBALANCE_RELAY              ,0},
    {0x06, DAC_SET_STR_REVERSE_CONNECT_RELAY             ,0},
    {0x07, DAC_SET_STR_LOSS_RELAY                        ,0},
    {0x08, DAC_SET_MPPT_OVER_CURR_WARN_RELAY             ,0},
    {0x09, DAC_SET_MPPT_OVER_CURR_ERROR_RELAY            ,0},
    {0x0A, DAC_SET_BUS_SOFT_START_ERR_RELAY              ,0},
    {0x0B, DAC_SET_DC_DISCONNECTOR_TRIP_RELAY            ,0},
    {0x0C, DAC_SET_PHASE_VOLT_OVER_VOLT_RELAY            ,0},
    {0x0D, DAC_SET_OVER_VOLT_BETWEEN_PHASE_RELAY         ,0},
    {0x0E, DAC_SET_PHASE_VOLT_UNDER_VOLT_RELAY           ,0},
    {0x0F, DAC_SET_UNDER_VOLT_BETWEEN_PHASE_RELAY        ,0},
    {0x10, DAC_SET_GRID_OVER_FREQ_RELAY                  ,0},
    {0x11, DAC_SET_GRID_UNDER_FREQ_RELAY                 ,0},
    {0x12, DAC_SET_AC_PHASE_LOSS_RELAY                   ,0},
    {0x13, DAC_SET_OUTPUT_SHORT_CIRCUIT_RELAY            ,0},
    {0x14, DAC_SET_OUT_CURR_IMBALANCE_RELAY              ,0},
    {0x15, DAC_SET_ABNORMAL_DC_COMPONENT_RELAY           ,0},
    {0x16, DAC_SET_ISLAND_PROT_RELAY                     ,0},
    {0x17, DAC_SET_PV_OVER_CURR_WARN_RELAY               ,0},
    {0x18, DAC_SET_PV_OVER_CURR_ERROR_RELAY              ,0},
    {0x19, DAC_SET_GRID_ANTI_ERROR_RELAY                 ,0},
    {0x1A, DAC_SET_SINGLE_PHASE_GND_SHORT_CIRCUIT_RELAY  ,0},
    {0x1B, DAC_SET_LOW_POWER_SHUTDOWN_RELAY              ,0},
    {0x1C, DAC_SET_LOW_VOLT_RT_RELAY                     ,0},
    {0x1D, DAC_SET_HIGH_VOLT_RT_RELAY                    ,0},
    {0x1E, DAC_SET_TEN_MINUTE_GRID_OVER_VOLT_PROT_RELAY  ,0},
    {0x1F, DAC_SET_ENVIR_OVER_TEMP_RELAY                 ,0},
    {0x20, DAC_SET_ENVIR_LOW_TEMP_RELAY                  ,0},
    {0x21, DAC_SET_ABN_RESIDUAL_CURR_RELAY               ,0},
    {0x22, DAC_SET_BUS_POS_IMPEDANCE_WARN_RELAY          ,0},
    {0x23, DAC_SET_BUS_NEG_IMPEDANCE_WARN_RELAY          ,0},
    {0x24, DAC_SET_BUS_POS_IMPEDANCE_ERROR_RELAY         ,0},
    {0x25, DAC_SET_BUS_NEG_IMPEDANCE_ERROR_RELAY         ,0},
    {0x26, DAC_SET_RADIATOR_OVER_TEMP_RELAY              ,0},
    {0x27, DAC_SET_INTER_FAN_FAIL_RELAY                  ,0},
    {0x28, DAC_SET_EXTER_FAN_FAIL_RELAY                  ,0},
    {0x29, DAC_SET_OVER_TEMP_INSIDS_MACHINE_RELAY        ,0},
    {0x2A, DAC_SET_FIRE_ERROR_RELAY                      ,0},
    {0x2B, DAC_SET_DC_ARC_DAULT_RELAY                    ,0},
    {0x2C, DAC_SET_AFCI_SELF_CHK_FAIL_RELAY              ,0},
    {0x2D, DAC_SET_AC_LIGHTING_PROTECTION_ERR_RELAY      ,0},
    {0x2E, DAC_SET_DC_LIGHTING_PROTECTION_ERR_RELAY      ,0},
    {0x2F, DAC_SET_BOOST_IGBT_OVER_TEMP_RELAY            ,0},
    {0x30, DAC_SET_PV_IGBT_OVER_TEMP_RELAY               ,0},
    {0x31, DAC_SET_GRID_RELAY_ERROR_RELAY                ,0},
    {0x32, DAC_SET_AC_CURR_SENSOR_ERROR_RELAY            ,0},
    {0x33, DAC_SET_PV_LOCK_ERROR_RELAY                   ,0},
    {0x34, DAC_SET_ABNORMAL_OPER_BUILD_IN_PID_RELAY      ,0},
    {0x35, DAC_SET_ABN_AUXI_POWER_SUPPLT_RELAY           ,0},
    {0x36, DAC_SET_MAIN_CTRL_EEPROM_FAULT_RELAY          ,0},
    {0x37, DAC_SET_MAIN_CTRL_EEPROM_ABNORMAL_RELAY       ,0},
    {0x38, DAC_SET_AUXI_CTRL_EEPROM_FAULT_RELAY          ,0},
    {0x39, DAC_SET_AUXI_CTRL_EEPROM_ABNORMAL_RELAY       ,0},
    {0x3A, DAC_SET_MONITER_MAIN_CTRL_COMMU_ERROR_RELAY   ,0},
    {0x3B, DAC_SET_CARRIER_SYNC_ABNORMAL_RELAY           ,0},
    {0x3C, DAC_SET_PROTOCOL_VER_MISMATCH_RELAY           ,0},
    {0x3D, DAC_SET_LICENSE_EXPIRED_RELAY                 ,0},
    {0x3E, DAC_SET_MONITER_ERROR_ALARM_RELAY             ,0},
    {0x3F, DAC_SET_MAIN_AUXI_COMMU_ERROR_RELAY           ,0},
    {0x40, DAC_SET_MAIN_CPLD_COMMU_ERROR_RELAY           ,0},
    {0},
};

cmd_id_map_t g_alm_level_map[] RAM_SECTION =
{
    {0x01, DAC_SET_MPPT_OVER_VOLT_LEVEL                  ,0},
    {0x02, DAC_SET_BUS_OVER_VOLT_LEVEL                   ,0},
    {0x03, DAC_SET_POS_BUS_OVER_VOLT_LEVEL               ,0},
    {0x04, DAC_SET_NEG_BUS_OVER_VOLT_LEVEL               ,0},
    {0x05, DAC_SET_BUS_VOLT_IMBALANCE_LEVEL              ,0},
    {0x06, DAC_SET_STR_REVERSE_CONNECT_LEVEL             ,0},
    {0x07, DAC_SET_STR_LOSS_LEVEL                        ,0},
    {0x08, DAC_SET_MPPT_OVER_CURR_WARN_LEVEL             ,0},
    {0x09, DAC_SET_MPPT_OVER_CURR_ERROR_LEVEL            ,0},
    {0x0A, DAC_SET_BUS_SOFT_START_ERR_LEVEL              ,0},
    {0x0B, DAC_SET_DC_DISCONNECTOR_TRIP_LEVEL            ,0},
    {0x0C, DAC_SET_PHASE_VOLT_OVER_VOLT_LEVEL            ,0},
    {0x0D, DAC_SET_OVER_VOLT_BETWEEN_PHASE_LEVEL         ,0},
    {0x0E, DAC_SET_PHASE_VOLT_UNDER_VOLT_LEVEL           ,0},
    {0x0F, DAC_SET_UNDER_VOLT_BETWEEN_PHASE_LEVEL        ,0},
    {0x10, DAC_SET_GRID_OVER_FREQ_LEVEL                  ,0},
    {0x11, DAC_SET_GRID_UNDER_FREQ_LEVEL                 ,0},
    {0x12, DAC_SET_AC_PHASE_LOSS_LEVEL                   ,0},
    {0x13, DAC_SET_OUTPUT_SHORT_CIRCUIT_LEVEL            ,0},
    {0x14, DAC_SET_OUT_CURR_IMBALANCE_LEVEL              ,0},
    {0x15, DAC_SET_ABNORMAL_DC_COMPONENT_LEVEL           ,0},
    {0x16, DAC_SET_ISLAND_PROT_LEVEL                     ,0},
    {0x17, DAC_SET_PV_OVER_CURR_WARN_LEVEL               ,0},
    {0x18, DAC_SET_PV_OVER_CURR_ERROR_LEVEL              ,0},
    {0x19, DAC_SET_GRID_ANTI_ERROR_LEVEL                 ,0},
    {0x1A, DAC_SET_SINGLE_PHASE_GND_SHORT_CIRCUIT_LEVEL  ,0},
    {0x1B, DAC_SET_LOW_POWER_SHUTDOWN_LEVEL              ,0},
    {0x1C, DAC_SET_LOW_VOLT_RT_LEVEL                     ,0},
    {0x1D, DAC_SET_HIGH_VOLT_RT_LEVEL                    ,0},
    {0x1E, DAC_SET_TEN_MINUTE_GRID_OVER_VOLT_PROT_LEVEL  ,0},
    {0x1F, DAC_SET_ENVIR_OVER_TEMP_LEVEL                 ,0},
    {0x20, DAC_SET_ENVIR_LOW_TEMP_LEVEL                  ,0},
    {0x21, DAC_SET_ABN_RESIDUAL_CURR_LEVEL               ,0},
    {0x22, DAC_SET_BUS_POS_IMPEDANCE_WARN_LEVEL          ,0},
    {0x23, DAC_SET_BUS_NEG_IMPEDANCE_WARN_LEVEL          ,0},
    {0x24, DAC_SET_BUS_POS_IMPEDANCE_ERROR_LEVEL         ,0},
    {0x25, DAC_SET_BUS_NEG_IMPEDANCE_ERROR_LEVEL         ,0},
    {0x26, DAC_SET_RADIATOR_OVER_TEMP_LEVEL              ,0},
    {0x27, DAC_SET_INTER_FAN_FAIL_LEVEL                  ,0},
    {0x28, DAC_SET_EXTER_FAN_FAIL_LEVEL                  ,0},
    {0x29, DAC_SET_OVER_TEMP_INSIDS_MACHINE_LEVEL        ,0},
    {0x2A, DAC_SET_FIRE_ERROR_LEVEL                      ,0},
    {0x2B, DAC_SET_DC_ARC_DAULT_LEVEL                    ,0},
    {0x2C, DAC_SET_AFCI_SELF_CHK_FAIL_LEVEL              ,0},
    {0x2D, DAC_SET_AC_LIGHTING_PROTECTION_ERR_LEVEL      ,0},
    {0x2E, DAC_SET_DC_LIGHTING_PROTECTION_ERR_LEVEL      ,0},
    {0x2F, DAC_SET_BOOST_IGBT_OVER_TEMP_LEVEL            ,0},
    {0x30, DAC_SET_PV_IGBT_OVER_TEMP_LEVEL               ,0},
    {0x31, DAC_SET_GRID_RELAY_ERROR_LEVEL                ,0},
    {0x32, DAC_SET_AC_CURR_SENSOR_ERROR_LEVEL            ,0},
    {0x33, DAC_SET_PV_LOCK_ERROR_LEVEL                   ,0},
    {0x34, DAC_SET_ABNORMAL_OPER_BUILD_IN_PID_LEVEL      ,0},
    {0x35, DAC_SET_ABN_AUXI_POWER_SUPPLT_LEVEL           ,0},
    {0x36, DAC_SET_MAIN_CTRL_EEPROM_FAULT_LEVEL          ,0},
    {0x37, DAC_SET_MAIN_CTRL_EEPROM_ABNORMAL_LEVEL       ,0},
    {0x38, DAC_SET_AUXI_CTRL_EEPROM_FAULT_LEVEL          ,0},
    {0x39, DAC_SET_AUXI_CTRL_EEPROM_ABNORMAL_LEVEL       ,0},
    {0x3A, DAC_SET_MONITER_MAIN_CTRL_COMMU_ERROR_LEVEL   ,0},
    {0x3B, DAC_SET_CARRIER_SYNC_ABNORMAL_LEVEL           ,0},
    {0x3C, DAC_SET_PROTOCOL_VER_MISMATCH_LEVEL           ,0},
    {0x3D, DAC_SET_LICENSE_EXPIRED_LEVEL                 ,0},
    {0x3E, DAC_SET_MONITER_ERROR_ALARM_LEVEL             ,0},
    {0x3F, DAC_SET_MAIN_AUXI_COMMU_ERROR_LEVEL           ,0},
    {0x40, DAC_SET_MAIN_CPLD_COMMU_ERROR_LEVEL           ,0},
    {0},
};

cmd_id_map_t g_power_special_cmd_map[] RAM_SECTION =
{
    {0x01, DAC_SET_POWER_SPECIAL_PARA1, DC_AC_SET_RD_TEST_RELATED_DATA},
    {0x02, DAC_SET_POWER_SPECIAL_PARA2, DC_AC_SET_DRIVE_TEST_RELATED_DATA},
    {0x03, DAC_SET_POWER_SPECIAL_PARA3, DC_AC_SET_ADC_TEST_RELATED_DATA},
    {0x04, DAC_SET_POWER_SPECIAL_PARA4, DC_AC_SET_PRODUCTION_TEST_DATA},
};

cmd_func_map_t g_parse_func_map[] RAM_SECTION =
{
   {SET_PROT_PARA_DATA,        &g_prot_cmd_map[0]},
   {SET_CURVE_PARA_DATA,       &g_curve_cmd_map[0]},
   {SET_SYSPARA_DATA,          &g_sys_cmd_map[0]},
   {SET_POWER_PARA_DATA,       &g_power_cmd_map[0]},
   {SET_ADJUST_PARA_DATA,      &g_adjust_cmd_map[0]},
   {SET_PRODUCE_PARA_DATA,     &g_produce_cmd_map[0]},
   {DAC_SET_FAULT_RECORD_PARA, &g_record_cmd_map[0]},
   {DAC_SET_ALM_RELAY,         &g_alm_relay_map[0]},
   {DAC_SET_ALM_LEVEL,         &g_alm_level_map[0]},
   {DAC_SET_MONITER_PARA,      &g_moniter_cmd_map[0]},
   {DAC_SET_POWER_SPECIAL_PARA,&g_power_special_cmd_map[0]},
};
cmd_id_map_t g_prot_para_map[] RAM_SECTION =
{
    {0xFF, GET_PROT_PARA_DATA1},
    {0x15, GET_PROT_PARA_DATA2},
    {0x16, GET_PROT_PARA_DATA3},
    {0},
};

cmd_id_map_t g_curve_para_map[] RAM_SECTION =
{
    {0xFF, GET_CURVE_PARA_DATA1},
    {0x06, GET_CURVE_PARA_DATA2},
    {0x0E, GET_CURVE_PARA_DATA3},
    {0x0F, GET_CURVE_PARA_DATA4},
    {0x10, GET_CURVE_PARA_DATA5},
    {0x1C, GET_CURVE_PARA_DATA6},
    {0},
};

cmd_id_map_t g_sys_para_map[] RAM_SECTION =
{
    {0xFF, GET_SYSPARA_DATA1 },
    {0X01, GET_SYSPARA_DATA2 },
    {0x02, GET_SYSPARA_DATA3 },
    {0x03, GET_SYSPARA_DATA4 },
    {0x04, GET_SYSPARA_DATA5 },
    {0x05, GET_SYSPARA_DATA6 },
    {0x07, GET_SYSPARA_DATA7 },
    {0x08, GET_SYSPARA_DATA8 },
    {0x09, GET_SYSPARA_DATA9 },
    {0x13, GET_SYSPARA_DATA10},
    {0x14, GET_SYSPARA_DATA11},
    {0x1B, GET_SYSPARA_DATA12},
    {0x1C, GET_SYSPARA_DATA13},
    {0x1D, GET_SYSPARA_DATA14},
    {0x1E, GET_SYSPARA_DATA15},
    {0},
};

cmd_id_map_t g_power_para_map[] RAM_SECTION =
{
    {0xFF, GET_POWER_PARA_DATA1 },
    {0X0A, GET_POWER_PARA_DATA2 },
    {0x0B, GET_POWER_PARA_DATA3 },
    {0x0C, GET_POWER_PARA_DATA4 },
    {0x0D, GET_POWER_PARA_DATA5 },
    {0x11, GET_POWER_PARA_DATA6 },
    {0x12, GET_POWER_PARA_DATA7 },
    {0},
};

cmd_id_map_t g_adjust_para_map[] RAM_SECTION =
{
    {0xFF, GET_ADJUST_PARA_DATA1 },
    {0x50, GET_ADJUST_PARA_DATA2 },
    {0x51, GET_ADJUST_PARA_DATA3 },
    {0x52, GET_ADJUST_PARA_DATA4 },
    {0x53, GET_ADJUST_PARA_DATA5 },
    {0x54, GET_ADJUST_PARA_DATA6 },
    {0x55, GET_ADJUST_PARA_DATA7 },
    {0x56, GET_ADJUST_PARA_DATA8 },
    {0x57, GET_ADJUST_PARA_DATA9 },
    {0x58, GET_ADJUST_PARA_DATA10 },
    {0},
};

cmd_id_map_t g_produce_para_map[] RAM_SECTION =
{
    {0xFF, GET_PROD_PARA_DATA1},
    {0x90, GET_PROD_PARA_DATA2},
    {0x91, GET_PROD_PARA_DATA3},
    {0x92, GET_PROD_PARA_DATA4},
    {0x93, GET_HARDWARE_VERSION_PARA},
    {0},
};

cmd_id_map_t g_moniter_para_map[] RAM_SECTION =
{
    {0xFF, DAC_GET_MONITER_PARA1 },
    {0x00, DAC_GET_MONITER_PARA2 },
    {0x0A, DAC_GET_MONITER_PARA3 },
    {0x01, DAC_GET_MONITER_PARA4 },
    {0x02, DAC_GET_MONITER_PARA5 },
    {0x03, DAC_GET_MONITER_PARA6 },
    {0x04, DAC_GET_MONITER_PARA7 },
    {0x05, DAC_GET_MONITER_PARA8 },
    {0x06, DAC_GET_MONITER_PARA9 },
    {0x09, DAC_GET_MONITER_PARA10},
    {0x0B, DAC_GET_DELAY_UPDATE},
    {0x0F, DAC_GET_USER_AUTH_TIME},
    {0},
};

cmd_id_map_t g_record_para_map[] RAM_SECTION =
{
    {0xFF, DAC_GET_FAULT_RECORD_PARA1  },
    {0x60, DAC_GET_FAULT_RECORD_PARA2 },
    {0x61, DAC_GET_FAULT_RECORD_PARA3 },
    {0},
};

cmd_id_map_t g_power_special_para_map[] RAM_SECTION =
{
    {0xFF, DAC_GET_POWER_SPECIAL_PARA1 },
    {0x01, DAC_GET_POWER_SPECIAL_PARA2 },
    {0x02, DAC_GET_POWER_SPECIAL_PARA3 },
    {0x03, DAC_GET_POWER_SPECIAL_PARA4 },
    {0x04, DAC_GET_POWER_SPECIAL_PARA5 },
    {0},
};

cmd_func_map_t g_pack_func_map[] RAM_SECTION =
{
    {GET_PROT_PARA_DATA,        &g_prot_para_map[0]},
    {GET_CURVE_PARA_DATA,       &g_curve_para_map[0]},
    {GET_SYSPARA_DATA,          &g_sys_para_map[0]},
    {GET_POWER_PARA_DATA,       &g_power_para_map[0]},
    {GET_ADJUST_PARA_DATA,      &g_adjust_para_map[0]},
    {GET_PROD_PARA_DATA,        &g_produce_para_map[0]},
    {DAC_GET_FAULT_RECORD_PARA, &g_record_para_map[0]}, 
    {DAC_GET_MONITER_PARA,      &g_moniter_para_map[0]},
    {DAC_GET_POWER_SPECIAL_PARA,&g_power_special_para_map[0]},
};

/* 命令请求头 */
static s1363_cmd_head_t cmd_req[] RAM_SECTION = {
    {VER_22 , CMD_NONE , CMD_NONE},    ///< 0
    //设置                                   
    {VER_22 , DAC_S1363_CID1 , CMD_SET_PROT_PARA_INT_DATA},  ///<1 设置保护参数
    {VER_22 , DAC_S1363_CID1 , CMD_SET_CURVE_PARA_INT_DATA}, ///<2 设置特征曲线参数
    {VER_22 , DAC_S1363_CID1 , CMD_SET_SYSPARA_INT_DATA},    ///< 3 设置系统参数
    {VER_22 , DAC_S1363_CID1 , CMD_SET_POWER_PARA_INT_DATA},  ///< 4 功率参数
    {VER_22 , DAC_S1363_CID1 , CMD_SET_ADJUST_INT_DATA},      ///< 5  比率调节参数
    {VER_22 , DAC_S1363_CID1 , CMD_SET_PRODUCE_INT_DATA},     ///< 6 生产参数
    //获取
    {VER_22 , DAC_S1363_CID1 , CMD_GET_PROT_PARA_INT_DATA},    ///< 7 获取保护参数
    {VER_22 , DAC_S1363_CID1 , CMD_GET_CURVE_PARA_INT_DATA},   ///< 8 获取特征曲线参数
    {VER_22 , DAC_S1363_CID1 , CMD_GET_SYSPARA_INT_DATA},      ///< 9 获取系统参数
    {VER_22 , DAC_S1363_CID1 , CMD_GET_POWER_PARA_INT_DATA},   ///< 10 获取参数
    {VER_22 , DAC_S1363_CID1 , CMD_GET_ADJUST_INT_DATA},       ///< 11 获取比率调节参数
    {VER_22 , DAC_S1363_CID1 , CMD_GET_PRODUCE_INT_DATA},      ///< 12 获取生产参数
    {VER_22 , DAC_S1363_CID1 , CMD_GET_POWER_FACT_DATA},      ///< 13 获取功率厂家信息
    {VER_22 , DAC_S1363_CID1 , CMD_GET_CSU_FACT_DATA},      ///< 14 获取监控厂家信息
    {VER_22 , DAC_S1363_CID1 , CMD_GET_CSU_TIME_DATA},      ///< 15 获取时间
    {VER_22 , DAC_S1363_CID1 , CMD_SET_CSU_TIME_DATA},      ///< 16 设置时间
    {VER_22 , DAC_S1363_CID1 , CMD_GET_ANA_INT_DATA},       ///< 17 获取模拟量量化后数据（定点数）

    {VER_22 , DAC_S1363_CID1 , CMD_GET_DIG_DATA},           ///< 18 获取状态量
    {VER_22 , DAC_S1363_CID1 , CMD_GET_VER_DATA},           ///< 19 获取通信协议
    {VER_22 , DAC_S1363_CID1 , CMD_GET_ADDR_DATA},           ///< 20 获取设备地址
    {VER_22 , DAC_S1363_CID1 , CMD_GET_ALM_DATA},            ///< 21 获取告警量
    {VER_22 , DAC_S1363_CID1 , CMD_TEST_RECORD},             ///< 22 调测命令
    {VER_22 , DAC_S1363_CID1 , CMD_CONTROL_ORDER},           ///< 23 控制命令
    {VER_22 , DAC_S1363_CID1 , CMD_GET_ANA_INT_DATA_2},       ///< 24 获取模拟量量化后数据（定点数）2
    {VER_22 , DAC_S1363_CID1 , CMD_SET_FAULT_RECORD_DATA},     ///< 25 设置录波参数
    {VER_22 , DAC_S1363_CID1 , CMD_GET_FAULT_RECORD_DATA},     ///< 26 获取录波参数
    {VER_22 , DAC_S1363_CID1 , CMD_SET_ALM_LEVEL},             ///< 27 设置告警等级
    {VER_22 , DAC_S1363_CID1 , CMD_GET_ALM_LEVEL},             ///< 28 获取告警等级
    {VER_22 , DAC_S1363_CID1 , CMD_SET_ALM_RELAY},             ///< 29 设置告警干接点
    {VER_22 , DAC_S1363_CID1 , CMD_GET_ALM_RELAY},             ///< 30 获取告警干接点
    {VER_22 , DAC_S1363_CID1 , CMD_GET_DIFF_UPDATE_FLAG},      ///< 31 获取差分升级标识
    {VER_22 , DAC_S1363_CID1 , CMD_GET_MONITER_PARA},          ///<32 获取监控参数
    {VER_22 , DAC_S1363_CID1 , CMD_SET_MONITER_PARA},          ///<33 设置监控参数
    {VER_22 , DAC_S1363_CID1 , CMD_GET_PARALLEL_INFO},          ///<34 并机数据
    {VER_22 , DAC_S1363_CID1 , CMD_GET_1363_VERSION},          ///<35 1363协议版本号
    {VER_22 , DAC_S1363_CID1 , CMD_SET_POWER_SPECIAL_PARA},    ///<36 设置功率专用参数
    {VER_22 , DAC_S1363_CID1 , CMD_GET_POWER_SPECIAL_PARA},    ///<37 获取功率专用参数
    {VER_22 , DAC_S1363_CID1 , CMD_GET_POWER_SPECIAL_ANA},     ///<38 获取功率研发专用模拟量
};

/* 命令应答头 */
static s1363_cmd_head_t cmd_ack[] RAM_SECTION = {
    {VER_22 , CMD_NONE , CMD_NONE},    ///< 0
    //设置                                   
    {VER_22 , DAC_S1363_CID1 , CMD_SET_PROT_PARA_INT_DATA},  ///<1 设置保护参数
    {VER_22 , DAC_S1363_CID1 , CMD_SET_CURVE_PARA_INT_DATA}, ///<2 设置特征曲线参数
    {VER_22 , DAC_S1363_CID1 , CMD_SET_SYSPARA_INT_DATA},    ///< 3 设置系统参数
    {VER_22 , DAC_S1363_CID1 , CMD_SET_POWER_PARA_INT_DATA},  ///< 4 功率参数
    {VER_22 , DAC_S1363_CID1 , CMD_SET_ADJUST_INT_DATA},      ///< 5  比率调节参数
    {VER_22 , DAC_S1363_CID1 , CMD_SET_PRODUCE_INT_DATA},     ///< 6 生产参数
    //获取
    {VER_22 , DAC_S1363_CID1 , CMD_GET_PROT_PARA_INT_DATA},    ///< 7 获取保护参数
    {VER_22 , DAC_S1363_CID1 , CMD_GET_CURVE_PARA_INT_DATA},   ///< 8 获取特征曲线参数
    {VER_22 , DAC_S1363_CID1 , CMD_GET_SYSPARA_INT_DATA},      ///< 9 获取系统参数
    {VER_22 , DAC_S1363_CID1 , CMD_GET_POWER_PARA_INT_DATA},   ///< 10 获取参数
    {VER_22 , DAC_S1363_CID1 , CMD_GET_ADJUST_INT_DATA},       ///< 11 获取比率调节参数
    {VER_22 , DAC_S1363_CID1 , CMD_GET_PRODUCE_INT_DATA},      ///< 12 获取生产参数
    {VER_22 , DAC_S1363_CID1 , CMD_GET_POWER_FACT_DATA},      ///< 13 获取功率厂家信息
    {VER_22 , DAC_S1363_CID1 , CMD_GET_CSU_FACT_DATA},      ///< 14 获取监控厂家信息
    {VER_22 , DAC_S1363_CID1 , CMD_GET_CSU_TIME_DATA},      ///< 15 获取时间
    {VER_22 , DAC_S1363_CID1 , CMD_SET_CSU_TIME_DATA},      ///< 16 设置时间
    {VER_22 , DAC_S1363_CID1 , CMD_GET_ANA_INT_DATA},       ///< 17 获取模拟量量化后数据（定点数）
    {VER_22 , DAC_S1363_CID1 , CMD_GET_DIG_DATA},           ///< 18 获取状态量
    {VER_22 , DAC_S1363_CID1 , CMD_GET_VER_DATA},           ///< 19 获取通信协议
    {VER_22 , DAC_S1363_CID1 , CMD_GET_ADDR_DATA},           ///< 20 获取设备地址
    {VER_22 , DAC_S1363_CID1 , CMD_GET_ALM_DATA},            ///< 21 获取告警量
    {VER_22 , DAC_S1363_CID1 , CMD_TEST_RECORD},             ///< 22 调测命令
    {VER_22 , DAC_S1363_CID1 , CMD_CONTROL_ORDER},           ///< 23 控制命令
    {VER_22 , DAC_S1363_CID1 , CMD_GET_ANA_INT_DATA_2},       ///< 24 获取模拟量量化后数据（定点数）2
    {VER_22 , DAC_S1363_CID1 , CMD_SET_FAULT_RECORD_DATA},     ///< 25 设置录波参数
    {VER_22 , DAC_S1363_CID1 , CMD_GET_FAULT_RECORD_DATA},     ///< 26 获取录波参数
    {VER_22 , DAC_S1363_CID1 , CMD_SET_ALM_LEVEL},             ///< 27 设置告警等级
    {VER_22 , DAC_S1363_CID1 , CMD_GET_ALM_LEVEL},             ///< 28 获取告警等级
    {VER_22 , DAC_S1363_CID1 , CMD_SET_ALM_RELAY},             ///< 29 设置告警干接点
    {VER_22 , DAC_S1363_CID1 , CMD_GET_ALM_RELAY},             ///< 30 获取告警干接点
    {VER_22 , DAC_S1363_CID1 , CMD_GET_DIFF_UPDATE_FLAG},      ///< 31 获取差分升级标识
    {VER_22 , DAC_S1363_CID1 , CMD_GET_MONITER_PARA},         ///<32 获取监控参数
    {VER_22 , DAC_S1363_CID1 , CMD_SET_MONITER_PARA},         ///<33 设置监控参数
    {VER_22 , DAC_S1363_CID1 , CMD_GET_PARALLEL_INFO},        ///<34 并机数据
    {VER_22 , DAC_S1363_CID1 , CMD_GET_1363_VERSION},         ///<35 1363协议版本号
    {VER_22 , DAC_S1363_CID1 , CMD_SET_POWER_SPECIAL_PARA},    ///<36 设置功率专用参数
    {VER_22 , DAC_S1363_CID1 , CMD_GET_POWER_SPECIAL_PARA},    ///<37 获取功率专用参数
    {VER_22 , DAC_S1363_CID1 , CMD_GET_POWER_SPECIAL_ANA},     ///<38 获取功率研发专用模拟量
};


/*------------------------------------------------获取保护参数，cid2=E4------------------------------------------------------------------------------*/
static data_info_id_verison_t pack_prot_para_dato_info[] RAM_SECTION = {

    {SEG_LOOP_SYS_PARA, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE , NEED_INTERACT, DAC_PARA_ID_MAX_PROT_LEVELS_OFFSET}, //LOOP开始
    {SEG_SYSTEM_PARA, type_unsigned_short,ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,  DAC_PARA_ID_OVER_VOLT_PROT_POINT_GRID_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,    DAC_PARA_ID_OVER_VOLT_PROT_TIME_GRID_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,  DAC_PARA_ID_UNDER_VOLT_PROT_POINT_GRID_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,    DAC_PARA_ID_UNDER_VOLT_PROT_TIME_GRID_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,  DAC_PARA_ID_OVER_FREQ_PROT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,    DAC_PARA_ID_OVER_FREQ_PROT_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQ_PROT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,    DAC_PARA_ID_UNDER_FREQ_PROT_TIME_OFFSET},
    {SEG_LOOP_OVER},  //LOOP结束
};

//一级保护参数
static data_info_id_verison_t pack_prot_para1_data_info[] RAM_SECTION ={
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_OVER_VOLT_PROT_POINT_GRID_OFFSET},
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,   DAC_PARA_ID_OVER_VOLT_PROT_TIME_GRID_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_UNDER_VOLT_PROT_POINT_GRID_OFFSET},
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,   DAC_PARA_ID_UNDER_VOLT_PROT_TIME_GRID_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_OVER_FREQ_PROT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,   DAC_PARA_ID_OVER_FREQ_PROT_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_UNDER_FREQ_PROT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,   DAC_PARA_ID_UNDER_FREQ_PROT_TIME_OFFSET},
};
//二级保护参数
static data_info_id_verison_t pack_prot_para2_data_info[] RAM_SECTION ={
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,DAC_PARA_ID_OVER_VOLT_PROT_POINT_GRID_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_VOLT_PROT_TIME_GRID_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,DAC_PARA_ID_UNDER_VOLT_PROT_POINT_GRID_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_VOLT_PROT_TIME_GRID_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,DAC_PARA_ID_OVER_FREQ_PROT_POINT_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,  DAC_PARA_ID_OVER_FREQ_PROT_TIME_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,DAC_PARA_ID_UNDER_FREQ_PROT_POINT_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,  DAC_PARA_ID_UNDER_FREQ_PROT_TIME_OFFSET + 1},
};

/*------------------------------------------------获取特征曲线参数，cid2=E2------------------------------------------------------------------------------*/
static data_info_id_verison_t pack_curve_para_dato_info[] RAM_SECTION ={

    {SEG_SYSTEM_PARA, type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_CHARACTER_CURVE_POINT_OFFSET},
    {SEG_LOOP_SYS_PARA, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CHARACTER_CURVE_POINT_OFFSET}, //LOOP开始
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,DAC_PARA_ID_VOLT_PERCENT_OFFSET},
    {SEG_LOOP_OVER},  //LOOP结束

    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_QP_MODE_TRIGG_VOLT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_QP_MODE_EXIT_VOLT_OFFSET},

    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,  DAC_PARA_ID_SETTING_POINT_OF_COS_CURVE_OFFSET},
    {SEG_LOOP_SYS_PARA, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,  DAC_PARA_ID_SETTING_POINT_OF_COS_CURVE_OFFSET}, //LOOP开始
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_P_PN_AXIS_OFFSET},
    {SEG_SYSTEM_PARA, type_short,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_COS_AXIS_OFFSET},
    {SEG_LOOP_OVER},  //LOOP结束

    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_QU_SCHEDU_TRIGG_POWER_PERCENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_QU_SCHEDU_EXIT_POWER_PERCENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_QU_CHARACTER_CURVE_MIN_PF_LIMIT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_HYSTERESIS_RATIO_OFFSET},

    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,  DAC_PARA_ID_NUMBER_OF_POINT_FOR_QU_CURVE_OFFSET},
    {SEG_LOOP_SYS_PARA, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,  DAC_PARA_ID_NUMBER_OF_POINT_FOR_QU_CURVE_OFFSET}, //LOOP开始
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_U_UN_AXIS_OFFSET},
    {SEG_SYSTEM_PARA, type_short,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_QS_AXIS_OFFSET},
    {SEG_LOOP_OVER},  //LOOP结束

    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_VOLT_DETECT_FILTER_TIME_OFFSET},

    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_NUMBER_OF_POINT_FOR_PF_CURVE_OFFSET},
    {SEG_LOOP_SYS_PARA, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_NUMBER_OF_POINT_FOR_PF_CURVE_OFFSET}, //LOOP开始
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_U_AXIS_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_AXIS_OFFSET},
    {SEG_LOOP_OVER},  //LOOP结束

    {SEG_SYSTEM_PARA, type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_POINT_OFFSET},
    {SEG_LOOP_SYS_PARA, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_POINT_OFFSET}, //LOOP开始
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET},
    {SEG_LOOP_OVER},  //LOOP结束

};
//LVRT特征曲线
static data_info_id_verison_t pack_para_lvrt_cc_data_info[] RAM_SECTION ={
    {SEG_SYSTEM_PARA, type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_CHARACTER_CURVE_POINT_OFFSET},
    {SEG_LOOP_SYS_PARA, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CHARACTER_CURVE_POINT_OFFSET}, //LOOP开始
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,DAC_PARA_ID_VOLT_PERCENT_OFFSET},
    {SEG_LOOP_OVER},  //LOOP结束
};

//cosφ-P/Pn特性曲线
static data_info_id_verison_t pack_para_P_Pn_CC_data_info[] RAM_SECTION =
{
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_QP_MODE_TRIGG_VOLT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_QP_MODE_EXIT_VOLT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_SETTING_POINT_OF_COS_CURVE_OFFSET},
    {SEG_LOOP_SYS_PARA, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_SETTING_POINT_OF_COS_CURVE_OFFSET}, //LOOP开始
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_P_PN_AXIS_OFFSET},
    {SEG_SYSTEM_PARA, type_short,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_COS_AXIS_OFFSET},
    {SEG_LOOP_OVER},  //LOOP结束
};

//Q-U特性曲线设置
static data_info_id_verison_t pack_para_Q_U_CC_data_info[]RAM_SECTION =
{
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_QU_SCHEDU_TRIGG_POWER_PERCENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_QU_SCHEDU_EXIT_POWER_PERCENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_QU_CHARACTER_CURVE_MIN_PF_LIMIT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_HYSTERESIS_RATIO_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_NUMBER_OF_POINT_FOR_QU_CURVE_OFFSET},
    {SEG_LOOP_SYS_PARA, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NUMBER_OF_POINT_FOR_QU_CURVE_OFFSET}, //LOOP开始
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_U_UN_AXIS_OFFSET},
    {SEG_SYSTEM_PARA, type_short,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_QS_AXIS_OFFSET},
    {SEG_LOOP_OVER},  //LOOP结束
};

//PF-U特性曲线设置
static data_info_id_verison_t pack_para_PF_U_CC_data_info[] RAM_SECTION =
{
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_VOLT_DETECT_FILTER_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NUMBER_OF_POINT_FOR_PF_CURVE_OFFSET},
    {SEG_LOOP_SYS_PARA, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NUMBER_OF_POINT_FOR_PF_CURVE_OFFSET}, //LOOP开始
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_U_AXIS_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_AXIS_OFFSET},
    {SEG_LOOP_OVER},  //LOOP结束
};

//HVRT特征曲线
static data_info_id_verison_t pack_para_hvrt_cc_data_info[]={
    {SEG_SYSTEM_PARA,   type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_POINT_OFFSET},
    {SEG_LOOP_SYS_PARA, type_char,             ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_POINT_OFFSET}, //LOOP开始
    {SEG_SYSTEM_PARA,   type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET},
    {SEG_SYSTEM_PARA,   type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET},
    {SEG_LOOP_OVER},  //LOOP结束
};
/*------------------------------------------------获取系统参数，cid2=47------------------------------------------------------------------------------*/
static data_info_id_verison_t pack_sys_para_dato_info[] RAM_SECTION ={

    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_POWER_ON_OFF_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PHASE_SEQ_ADAPTIVE_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_CONNECT_TYPE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_EN_LEAK_CURR_SELF_CHECK_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_RCD_BOOST_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_WEAK_GRID_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_WATER_INGRESS_ENABLE_OFFSET},

    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GRID_ISOLATION_SETTING_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OUTPUT_MODE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ATUO_START_AFTER_GRID_FAULT_RECOVER_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CONNECT_TIME_AFTER_GRID_FAULT_RECOVER_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_DELAY_START_TIME_AFTER_GRID_FAULT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UPPER_LIMIT_GRID_RECONN_VOLT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LOWER_LIMIT_GRID_RECONN_VOLT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UPPER_LIMIT_GRID_RECONN_FREQ_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LOWER_LIMIT_GRID_RECONN_FREQ_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PQ_MODE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LOWER_LIMIT_GRID_CONN_START_VOLT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UPPER_LIMIT_GRID_CONN_START_FREQ_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LOWER_LIMIT_GRID_CONN_START_FREQ_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_RATED_GRID_VOLT_OFFSET},

    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_STANDBY_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_BOOT_SOFT_START_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ISLAND_DETECT_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ENABLE_GRID_VOLT_PROT_SHEILD_DURING_VRT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_VRT_EXIT_HYSTERESIS_THRESHLOD_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GRID_FAULT_ZERO_CURR_MODE_OFFSET},

    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_SCAN_INTERVAL_OFFSET},

    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_TRIGG_THRESHOLD_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_POS_SEQ_REACTIVE_POWER_COMPEN_FACTOR_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_NEG_SEQ_REACTIVE_POWER_COMPEN_FACTOR_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_REACTIVE_CURR_LIMIT_PERCENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_ZERO_CURR_MODE_THRESHOLD_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_MODE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_ACTIVE_CURR_MAINTEN_COEFFICIENT_OFFSET},

    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_TRIGG_THRESHOLD_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_POS_SEQ_REACTIVE_POWER_COMPEN_FACTOR_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_NEG_SEQ_REACTIVE_POWER_COMPEN_FACTOR_OFFSET},

    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PID_REPAIR_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NIGHT_PID_PROT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NIGHT_SLEEP_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NIGHT_SLEEP_WAIT_TIME_OFFSET},

    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AFCI_DETECT_ON_OFF_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AFCI_CHECK_DETECTION_SENSITIVITY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AFCI_SAVE_ENABLE_OFFSET},

    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_FEED_OVER_LIMIT_PROT_SHUTDOWN_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_FEED_LIMIT_PROT_SHUTDOWN_THRESHOLD_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_FEED_LIMIT_PORT_SHUTDOWN_INTERVER_OFFSET},

    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GRID_VOLT_IMBALANCE_PROT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_INSULATION_IMPEDANCE_PROT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_TEN_MINUTE_GRID_OVER_PROT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_TEN_MINUTE_GRID_OVER_PROT_TIME_OFFSET},

    {SEG_SYSTEM_PARA,type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ALTITUDE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GRID_STANDARD_CODE_OFFSET},

    /* 组串接入检测 */
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_LOSS_DETECTION_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_STARTING_CURRENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_TWO_IN_ONE_STARTING_CURRENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 2},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 3},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 4},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 5},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 6},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 7},

    /* 过欠频降额参数 */
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVERFREQUENCY_DERATING_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVERFREQUENCY_DERATING_TRIG_FREQUENCY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVERFREQUENCY_DERATING_EXIT_FREQUENCY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVERFREQUENCY_DERATING_CUTOFF_FREQUENCY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVERFREQUENCY_DERATING_RATED_POWER_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVERFREQUENCY_DERATING_RECOVERY_GRADIENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_FREQUENCY_CONTROL_FILTERING_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQUENCY_ACTIVE_DERATING_RECOVERY_DELAY_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQUENCY_ACTIVE_DERATING_EFFECTIVE_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_TRIG_FREQUENCY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_EXIT_FREQUENCY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_CUTOFF_FREQUENCY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_CUTOFF_POWER_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_RECOVERY_GRADIENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_ACTIVE_DERATING_RECOVERY_DELAY_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_ACTIVE_DERATING_EFFECTIVE_TIME_OFFSET},

};

//控制参数
static data_info_id_verison_t pack_para_power_ctrl_data_info[]={

    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_POWER_ON_OFF_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PHASE_SEQ_ADAPTIVE_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_CONNECT_TYPE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_EN_LEAK_CURR_SELF_CHECK_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_RCD_BOOST_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_WEAK_GRID_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_WATER_INGRESS_ENABLE_OFFSET},
};



//电网参数
static data_info_id_verison_t pack_para_power_grid_data_info[] RAM_SECTION ={

    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GRID_ISOLATION_SETTING_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OUTPUT_MODE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ATUO_START_AFTER_GRID_FAULT_RECOVER_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CONNECT_TIME_AFTER_GRID_FAULT_RECOVER_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_DELAY_START_TIME_AFTER_GRID_FAULT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UPPER_LIMIT_GRID_RECONN_VOLT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LOWER_LIMIT_GRID_RECONN_VOLT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UPPER_LIMIT_GRID_RECONN_FREQ_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LOWER_LIMIT_GRID_RECONN_FREQ_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PQ_MODE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LOWER_LIMIT_GRID_CONN_START_VOLT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UPPER_LIMIT_GRID_CONN_START_FREQ_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LOWER_LIMIT_GRID_CONN_START_FREQ_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_RATED_GRID_VOLT_OFFSET},
};

//特性参数
static data_info_id_verison_t pack_para_chara_data_info[] RAM_SECTION ={
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_STANDBY_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_BOOT_SOFT_START_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ISLAND_DETECT_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ENABLE_GRID_VOLT_PROT_SHEILD_DURING_VRT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_VRT_EXIT_HYSTERESIS_THRESHLOD_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GRID_FAULT_ZERO_CURR_MODE_OFFSET},
};

//MPPT参数
static data_info_id_verison_t pack_para_mppt_data_info[]RAM_SECTION ={
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_SCAN_INTERVAL_OFFSET},
};

//LVRT参数设置
static data_info_id_verison_t pack_para_lvrt_data_info[]RAM_SECTION ={
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_TRIGG_THRESHOLD_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_POS_SEQ_REACTIVE_POWER_COMPEN_FACTOR_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_NEG_SEQ_REACTIVE_POWER_COMPEN_FACTOR_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_REACTIVE_CURR_LIMIT_PERCENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_ZERO_CURR_MODE_THRESHOLD_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_MODE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_ACTIVE_CURR_MAINTEN_COEFFICIENT_OFFSET},
};

//HVRT使能
static data_info_id_verison_t pack_para_hvrt_data_info[]RAM_SECTION ={
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_TRIGG_THRESHOLD_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_POS_SEQ_REACTIVE_POWER_COMPEN_FACTOR_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_NEG_SEQ_REACTIVE_POWER_COMPEN_FACTOR_OFFSET},
};

//PID参数设置
static data_info_id_verison_t pack_para_pid_data_info[] RAM_SECTION={
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PID_REPAIR_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NIGHT_PID_PROT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NIGHT_SLEEP_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NIGHT_SLEEP_WAIT_TIME_OFFSET},
};

//AFCI参数
static data_info_id_verison_t pack_para_afci_data_info[] RAM_SECTION ={
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AFCI_DETECT_ON_OFF_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AFCI_CHECK_DETECTION_SENSITIVITY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AFCI_SAVE_ENABLE_OFFSET},

};

//并网点控制-馈电越限保护关机
static data_info_id_verison_t pack_para_conn_ctrl_data_info[] RAM_SECTION ={
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_FEED_OVER_LIMIT_PROT_SHUTDOWN_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_FEED_LIMIT_PROT_SHUTDOWN_THRESHOLD_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_FEED_LIMIT_PORT_SHUTDOWN_INTERVER_OFFSET},
};

static data_info_id_verison_t pack_para_prot_data_info[] RAM_SECTION={
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GRID_VOLT_IMBALANCE_PROT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_INSULATION_IMPEDANCE_PROT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_TEN_MINUTE_GRID_OVER_PROT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_TEN_MINUTE_GRID_OVER_PROT_TIME_OFFSET},
};

//海拔高度
static data_info_id_verison_t pack_para_altitude[] RAM_SECTION ={
    {SEG_SYSTEM_PARA,type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ALTITUDE_OFFSET},
};

//电网标准码
static data_info_id_verison_t pack_para_grid_standard_code[] RAM_SECTION = {
     {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GRID_STANDARD_CODE_OFFSET},
};

// 组串接入检测
static data_info_id_verison_t pack_para_str_in_detection[] RAM_SECTION ={
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_LOSS_DETECTION_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_STARTING_CURRENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_TWO_IN_ONE_STARTING_CURRENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 2},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 3},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 4},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 5},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 6},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 7},

};

// 过欠频降额参数
static data_info_id_verison_t pack_para_over_under_freq_derate[] RAM_SECTION ={
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVERFREQUENCY_DERATING_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVERFREQUENCY_DERATING_TRIG_FREQUENCY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVERFREQUENCY_DERATING_EXIT_FREQUENCY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVERFREQUENCY_DERATING_CUTOFF_FREQUENCY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVERFREQUENCY_DERATING_RATED_POWER_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVERFREQUENCY_DERATING_RECOVERY_GRADIENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_FREQUENCY_CONTROL_FILTERING_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQUENCY_ACTIVE_DERATING_RECOVERY_DELAY_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQUENCY_ACTIVE_DERATING_EFFECTIVE_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_TRIG_FREQUENCY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_EXIT_FREQUENCY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_CUTOFF_FREQUENCY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_CUTOFF_POWER_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_RECOVERY_GRADIENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_ACTIVE_DERATING_RECOVERY_DELAY_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_ACTIVE_DERATING_EFFECTIVE_TIME_OFFSET},

};

/*------------------------------------------------获取功率参数，cid2=E6------------------------------------------------------------------------------*/
static data_info_id_verison_t pack_power_para_dato_info[] RAM_SECTION ={
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REMOTE_POWER_DISPATCH_ENABLE_DISABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_SHCED_INSTRUCT_MAINTEN_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ACTIVE_POWER_REFERENCE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_APPARNET_POWER_REFERENCE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MAX_APPARENT_POWER_PARAM_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MAX_ACTIVE_POWER_PARAM_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_POWER_LIMIT_ZERO_PERCENT_SHUTDOWN_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NIGHT_REACTIVE_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NIGTH_REACTIVE_POWER_COMPEN_OFFSET},
    {SEG_SYSTEM_PARA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_SHUTDOWN_GRADIENT_OFFSET},


    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ENABLE_COMMU_CHAIN_FAIL_PROT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_COMMU_BROKEN_CHAIN_DETECT_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_FAIL_PROT_ACTIVE_POWER_MODE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ACTIVE_POWER_LIMIT_FAIL_POWER_PERCENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ACTIVE_POWER_LIMIT_FAIL_PROT_VALUE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_FAIL_PROT_REACTIVE_POWER_MODE_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_FAIL_PROT_REACTIVE_LIMIT_VALUE_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_FAIL_PROT_REACTIVE_POWER_LIMIT_PROT_OFFSET},

    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GRID_ACTIVE_POWER_CHANGE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ACTIVE_POWER_CONTROL_MODE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ACTIVE_POWER_DERATING_SETTING_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ACTIVE_POWER_DERATING_PERCENT_OFFSET},

    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GRID_REACTIVE_POWER_CHANGE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REACTIVE_POWER_COMPEN_MODE_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REACTIVE_POWER_COMPEN_SETTING_FACTOR_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REACTIVE_POWER_COMPEN_SETTING_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REACTIVE_POWER_REGULATIONG_TIME_OFFSET},

    {SEG_SYSTEM_PARA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REACTIVE_POWER_VALUE_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REACTIVE_POWER_PCT_OFFSET},

    {SEG_SYSTEM_PARA,type_unsigned_short,ARRAY_SIZE_1,DOP_0,DATA_DROP, NO_DEF_VALUE, NEED_INTERACT ,DAC_PARA_ID_GRID_CONNECT_ACTIVE_POWER_CONTROL_MODE_OFFSET},
    {SEG_SYSTEM_PARA,type_unsigned_short,ARRAY_SIZE_1,DOP_0,DATA_DROP, NO_DEF_VALUE, NEED_INTERACT ,DAC_PARA_ID_CLOSE_LOOP_CONTROLLER_OFFSET},
    {SEG_SYSTEM_PARA,type_unsigned_short,ARRAY_SIZE_1,DOP_0,DATA_DROP, NO_DEF_VALUE, NEED_INTERACT ,DAC_PARA_ID_RESTRICTION_METHOD_OFFSET},
    {SEG_SYSTEM_PARA,type_unsigned_short,ARRAY_SIZE_1,DOP_1,DATA_DROP, NO_DEF_VALUE, NEED_INTERACT ,DAC_PARA_ID_POWER_REGULAR_CYCLE_OFFSET},
    {SEG_SYSTEM_PARA,type_int,ARRAY_SIZE_1,DOP_3,DATA_DROP, NO_DEF_VALUE, NEED_INTERACT ,DAC_PARA_ID_POWER_UP_THRESHOLD_OFFSET},
    {SEG_SYSTEM_PARA,type_int,ARRAY_SIZE_1,DOP_3,DATA_DROP, NO_DEF_VALUE, NEED_INTERACT ,DAC_PARA_ID_MAX_FEED_GRID_POWER_OFFSET},
    {SEG_SYSTEM_PARA,type_unsigned_short,ARRAY_SIZE_1,DOP_1,DATA_DROP, NO_DEF_VALUE,NEED_INTERACT ,DAC_PARA_ID_MAX_FEED_GRID_POWER_PERCENT_OFFSET},

    {SEG_SYSTEM_PARA,type_unsigned_short,ARRAY_SIZE_1,DOP_0,DATA_DROP, NO_DEF_VALUE,NEED_INTERACT ,DAC_PARA_ID_GRID_CONNECT_REACTIVE_POWER_CONTROL_METHOD_OFFSET},
    {SEG_SYSTEM_PARA,type_short,ARRAY_SIZE_1,DOP_3,DATA_DROP, NO_DEF_VALUE,NEED_INTERACT ,DAC_PARA_ID_TARGET_POWER_METHOD_OFFSET},
    {SEG_SYSTEM_PARA,type_unsigned_short,ARRAY_SIZE_1,DOP_1,DATA_DROP, NO_DEF_VALUE,NEED_INTERACT ,DAC_PARA_ID_REACTIVE_POWER_REGULAR_CYCLE_OFFSET},
    {SEG_SYSTEM_PARA,type_unsigned_short,ARRAY_SIZE_1,DOP_3,DATA_DROP, NO_DEF_VALUE,NEED_INTERACT ,DAC_PARA_ID_REACTIVE_POWER_REGULAR_DEAD_ZONE_OFFSET},

};

//功率调节参数
static data_info_id_verison_t pack_para_power_regu_data_info[] RAM_SECTION ={
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REMOTE_POWER_DISPATCH_ENABLE_DISABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_SHCED_INSTRUCT_MAINTEN_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ACTIVE_POWER_REFERENCE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_APPARNET_POWER_REFERENCE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MAX_APPARENT_POWER_PARAM_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MAX_ACTIVE_POWER_PARAM_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_POWER_LIMIT_ZERO_PERCENT_SHUTDOWN_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NIGHT_REACTIVE_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NIGTH_REACTIVE_POWER_COMPEN_OFFSET},
    {SEG_SYSTEM_PARA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_SHUTDOWN_GRADIENT_OFFSET},

};

//通信锻炼失效保护参数
static data_info_id_verison_t pack_para_comm_fail_data_info[] RAM_SECTION ={
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ENABLE_COMMU_CHAIN_FAIL_PROT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_COMMU_BROKEN_CHAIN_DETECT_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_FAIL_PROT_ACTIVE_POWER_MODE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ACTIVE_POWER_LIMIT_FAIL_POWER_PERCENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ACTIVE_POWER_LIMIT_FAIL_PROT_VALUE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_FAIL_PROT_REACTIVE_POWER_MODE_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_FAIL_PROT_REACTIVE_LIMIT_VALUE_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_FAIL_PROT_REACTIVE_POWER_LIMIT_PROT_OFFSET},
};

// 有功功率调节参数
static data_info_id_verison_t pack_para_active_power_reg_data_info[] RAM_SECTION ={
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GRID_ACTIVE_POWER_CHANGE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ACTIVE_POWER_CONTROL_MODE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ACTIVE_POWER_DERATING_SETTING_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ACTIVE_POWER_DERATING_PERCENT_OFFSET},
};

//无功功率调节参数
static data_info_id_verison_t pack_para_reactive_power_regu_data_info[] RAM_SECTION ={
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GRID_REACTIVE_POWER_CHANGE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REACTIVE_POWER_COMPEN_MODE_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REACTIVE_POWER_COMPEN_SETTING_FACTOR_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REACTIVE_POWER_COMPEN_SETTING_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REACTIVE_POWER_REGULATIONG_TIME_OFFSET},

    {SEG_SYSTEM_PARA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REACTIVE_POWER_VALUE_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REACTIVE_POWER_PCT_OFFSET},

};

//并网点控制-有功功率
static data_info_id_verison_t pack_para_parall_point_ctrl_active[] RAM_SECTION ={
    {SEG_SYSTEM_PARA,type_short,ARRAY_SIZE_1,DOP_0,DATA_DROP, NO_DEF_VALUE, NEED_INTERACT ,DAC_PARA_ID_GRID_CONNECT_ACTIVE_POWER_CONTROL_MODE_OFFSET},
    {SEG_SYSTEM_PARA,type_short,ARRAY_SIZE_1,DOP_0,DATA_DROP, NO_DEF_VALUE, NEED_INTERACT ,DAC_PARA_ID_CLOSE_LOOP_CONTROLLER_OFFSET},
    {SEG_SYSTEM_PARA,type_short,ARRAY_SIZE_1,DOP_0,DATA_DROP, NO_DEF_VALUE, NEED_INTERACT ,DAC_PARA_ID_RESTRICTION_METHOD_OFFSET},
    {SEG_SYSTEM_PARA,type_short,ARRAY_SIZE_1,DOP_1,DATA_DROP, NO_DEF_VALUE, NEED_INTERACT ,DAC_PARA_ID_POWER_REGULAR_CYCLE_OFFSET},
    {SEG_SYSTEM_PARA,type_int,ARRAY_SIZE_1,DOP_3,DATA_DROP, NO_DEF_VALUE , NEED_INTERACT ,DAC_PARA_ID_POWER_UP_THRESHOLD_OFFSET},
    {SEG_SYSTEM_PARA,type_unsigned_int,ARRAY_SIZE_1,DOP_3,DATA_DROP, NO_DEF_VALUE , NEED_INTERACT , DAC_PARA_ID_MAX_FEED_GRID_POWER_OFFSET},
    {SEG_SYSTEM_PARA,type_short,ARRAY_SIZE_1,DOP_1,DATA_DROP, NO_DEF_VALUE, NEED_INTERACT ,DAC_PARA_ID_MAX_FEED_GRID_POWER_PERCENT_OFFSET},
};
//并网点控制-无功功率
static data_info_id_verison_t pack_para_parall_point_ctrl_reactive[] RAM_SECTION ={
    {SEG_SYSTEM_PARA,type_unsigned_short,ARRAY_SIZE_1,DOP_0,DATA_DROP, NO_DEF_VALUE, NEED_INTERACT ,DAC_PARA_ID_GRID_CONNECT_REACTIVE_POWER_CONTROL_METHOD_OFFSET},
    {SEG_SYSTEM_PARA,type_short,ARRAY_SIZE_1,DOP_3,DATA_DROP, NO_DEF_VALUE, NEED_INTERACT ,DAC_PARA_ID_TARGET_POWER_METHOD_OFFSET},
    {SEG_SYSTEM_PARA,type_unsigned_short,ARRAY_SIZE_1,DOP_1,DATA_DROP, NO_DEF_VALUE, NEED_INTERACT ,DAC_PARA_ID_REACTIVE_POWER_REGULAR_CYCLE_OFFSET},
    {SEG_SYSTEM_PARA,type_unsigned_short,ARRAY_SIZE_1,DOP_3,DATA_DROP, NO_DEF_VALUE, NEED_INTERACT ,DAC_PARA_ID_REACTIVE_POWER_REGULAR_DEAD_ZONE_OFFSET},

};
/*------------------------------------------------获取比率校准参数，cid2=E8------------------------------------------------------------------------------*/
static data_info_id_verison_t pack_adjust_para_dato_info[] RAM_SECTION ={
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_CORRECT_RATIO_OF_GRID_VOLT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_NET_CA_VOLT_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_CORRECT_RATIO_OF_GRID_VOLT + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_NET_CB_VOLT_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_CORRECT_RATIO_OF_GRID_VOLT + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_NET_C_VOLT_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_INVERTER_VOLT_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_INVERTER_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_INVERTER_VOLT_CORRECT_RATIO + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_INVERTER_CORRECT_POINT + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_INVERTER_VOLT_CORRECT_RATIO + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_INVERTER_CORRECT_POINT + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_SINGLE_PHASE_GRID_CONNECT_CURR_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_GRID_CURR_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_SINGLE_PHASE_GRID_CONNECT_CURR_CORRECT_RATIO + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_GRID_CURR_CORRECT_POINT + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_SINGLE_PHASE_GRID_CONNECT_CURR_CORRECT_RATIO + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_GRID_CURR_CORRECT_POINT + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_POS_BUS_VOLT_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_POS_BUS_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_NEG__BUS_VOLT_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_NEG_BUS_CORRECT_POINT},

    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_NUM_MAX, NOT_NEED_INTERACT},
    {SEG_LOOP, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_NUM_MAX, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_INPUT_VOLT_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_IN_VOLT_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_TOTAL_INPUT_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ALLIN_CURR_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_BRANCH_INPUT_CURR_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_BRANCH_IN_CURR_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_SAMPLE_CURRENT_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_SAMPLE_CURRENT_CORRECT_POINT},
    {SEG_LOOP_OVER}, 

    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_GRID_VOLT_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_NET_CA_VOLT_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_GRID_VOLT_CORRECT_RATIO + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_NET_CB_VOLT_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_GRID_VOLT_CORRECT_RATIO + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_NET_C_VOLT_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_INVERTER_VOLT_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_INVERTER_PHASE_VOLT_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_INVERTER_VOLT_CORRECT_RATIO + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_INVERTER_PHASE_VOLT_CORRECT_POINT + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_INVERTER_VOLT_CORRECT_RATIO + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_INVERTER_PHASE_VOLT_CORRECT_POINT + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_INVERTER_CURR_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_GRID_CURR_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_INVERTER_CURR_CORRECT_RATIO + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_GRID_CURR_CORRECT_POINT + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_INVERTER_CURR_CORRECT_RATIO + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_GRID_CURR_CORRECT_POINT + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_POS_BUS_VOLT_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_POS_BUS_VOLT_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_NEG_BUS_VOLT_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_NEG_BUS_VOLT_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_DC_ARC_DETECT_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_DC_ARC_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_DC_ARC_DETECT_CORRECT_RATIO + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_DC_ARC_CORRECT_POINT + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_DC_ARC_DETECT_CORRECT_RATIO + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_DC_ARC_CORRECT_POINT + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_DC_ARC_DETECT_CORRECT_RATIO + 3},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_DC_ARC_CORRECT_POINT + 3},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_PE_VOLT_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PE_VOLT_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_LEAK_CURR_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_LEAK_CURR_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_DC_COMPONENT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_DC_COMPONENT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_DC_COMPONENT_RATIO + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_DC_COMPONENT_POINT + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_DC_COMPONENT_RATIO + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_DC_COMPONENT_POINT + 2},

};

//输出电压比例校正参数
static data_info_id_verison_t pack_para_adj_out_volt_data_info[] RAM_SECTION ={
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_CORRECT_RATIO_OF_GRID_VOLT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_NET_CA_VOLT_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_CORRECT_RATIO_OF_GRID_VOLT + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_NET_CB_VOLT_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_CORRECT_RATIO_OF_GRID_VOLT + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_NET_C_VOLT_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_INVERTER_VOLT_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_INVERTER_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_INVERTER_VOLT_CORRECT_RATIO + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_INVERTER_CORRECT_POINT + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_INVERTER_VOLT_CORRECT_RATIO + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_INVERTER_CORRECT_POINT + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_SINGLE_PHASE_GRID_CONNECT_CURR_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_GRID_CURR_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_SINGLE_PHASE_GRID_CONNECT_CURR_CORRECT_RATIO + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_GRID_CURR_CORRECT_POINT + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_SINGLE_PHASE_GRID_CONNECT_CURR_CORRECT_RATIO + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_GRID_CURR_CORRECT_POINT + 2},
};

//BUS电压比例校正
static data_info_id_verison_t pack_para_adj_bus_volt_data_info[] RAM_SECTION ={
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_POS_BUS_VOLT_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_POS_BUS_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_NEG__BUS_VOLT_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MASTER_NEG_BUS_CORRECT_POINT},
};

//MPPT1比例校正
static data_info_id_verison_t pack_para_adj_mppt1_data_info[] RAM_SECTION ={
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_INPUT_VOLT_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_IN_VOLT_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_TOTAL_INPUT_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ALLIN_CURR_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_BRANCH_INPUT_CURR_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_BRANCH_IN_CURR_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_SAMPLE_CURRENT_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_SAMPLE_CURRENT_CORRECT_POINT},
};

//MPPT2比例校正
static data_info_id_verison_t pack_para_adj_mppt2_data_info[] RAM_SECTION ={
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_INPUT_VOLT_CORRECT_RATIO + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_IN_VOLT_CORRECT_POINT + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_TOTAL_INPUT_CORRECT_RATIO + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ALLIN_CURR_CORRECT_POINT + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_BRANCH_INPUT_CURR_CORRECT_RATIO + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_BRANCH_IN_CURR_CORRECT_POINT + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_SAMPLE_CURRENT_CORRECT_RATIO + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_SAMPLE_CURRENT_CORRECT_POINT + 1},
};

//MPPT3比例校正
static data_info_id_verison_t pack_para_adj_mppt3_data_info[] RAM_SECTION ={
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_INPUT_VOLT_CORRECT_RATIO + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_IN_VOLT_CORRECT_POINT + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_TOTAL_INPUT_CORRECT_RATIO + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ALLIN_CURR_CORRECT_POINT + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_BRANCH_INPUT_CURR_CORRECT_RATIO + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_BRANCH_IN_CURR_CORRECT_POINT + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_SAMPLE_CURRENT_CORRECT_RATIO + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_SAMPLE_CURRENT_CORRECT_POINT + 2},
};

//MPPT4比例校正
static data_info_id_verison_t pack_para_adj_mppt4_data_info[] RAM_SECTION = {
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_INPUT_VOLT_CORRECT_RATIO + 3},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_IN_VOLT_CORRECT_POINT + 3},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_TOTAL_INPUT_CORRECT_RATIO + 3},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ALLIN_CURR_CORRECT_POINT + 3},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_BRANCH_INPUT_CURR_CORRECT_RATIO + 3},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_BRANCH_IN_CURR_CORRECT_POINT + 3},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_SAMPLE_CURRENT_CORRECT_RATIO + 3},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVER_SAMPLE_CURRENT_CORRECT_POINT + 3},
};

//辅控比例校正
static data_info_id_verison_t pack_para_adj_adj_aux_data_info[] RAM_SECTION ={
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_GRID_VOLT_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_NET_CA_VOLT_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_GRID_VOLT_CORRECT_RATIO + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_NET_CB_VOLT_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_GRID_VOLT_CORRECT_RATIO + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_NET_C_VOLT_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_INVERTER_VOLT_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_INVERTER_PHASE_VOLT_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_INVERTER_VOLT_CORRECT_RATIO + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_INVERTER_PHASE_VOLT_CORRECT_POINT + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_INVERTER_VOLT_CORRECT_RATIO + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_INVERTER_PHASE_VOLT_CORRECT_POINT + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_INVERTER_CURR_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_GRID_CURR_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_INVERTER_CURR_CORRECT_RATIO + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_GRID_CURR_CORRECT_POINT + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_INVERTER_CURR_CORRECT_RATIO + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_GRID_CURR_CORRECT_POINT + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_POS_BUS_VOLT_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_POS_BUS_VOLT_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_NEG_BUS_VOLT_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUX_NEG_BUS_VOLT_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_DC_ARC_DETECT_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_DC_ARC_CORRECT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_DC_ARC_DETECT_CORRECT_RATIO + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_DC_ARC_CORRECT_POINT + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_DC_ARC_DETECT_CORRECT_RATIO + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_DC_ARC_CORRECT_POINT + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_DC_ARC_DETECT_CORRECT_RATIO + 3},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_DC_ARC_CORRECT_POINT + 3},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXI_CONTROL_PE_VOLT_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PE_VOLT_CORRECT_POINT},
};

//漏电流比例校正
static data_info_id_verison_t pack_para_leak_curr_data_info[] RAM_SECTION = {
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_LEAK_CURR_CORRECT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_LEAK_CURR_CORRECT_POINT},
};

//直流分量比例校正
static data_info_id_verison_t pack_dc_component_data_info[] RAM_SECTION = {
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_DC_COMPONENT_RATIO},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_DC_COMPONENT_POINT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_DC_COMPONENT_RATIO + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_DC_COMPONENT_POINT + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_DC_COMPONENT_RATIO + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_DC_COMPONENT_POINT + 2},
};
/*------------------------------------------------获取生产参数，cid2=EA------------------------------------------------------------------------------*/
static data_info_id_verison_t pack_produce_para_dato_info[] RAM_SECTION ={

    {SEG_DATA , type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_RATED_POWER_FAC_PARAM},
    {SEG_DATA , type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MAX_ACTIVE_POWER_FAC_PARAM},
    {SEG_DATA , type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MAX_APPARENT_POWER_FAC_PARAM},
    {SEG_DATA , type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MAX_REACTIVE_POWER_FAC_PARAM},
    {SEG_DATA , type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MANUFACTURER_ID},
    {SEG_DATA , type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MANUFACTURER_ADDRESS},
    {SEG_SYSTEM_PARA, type_string, DC_AC_MACH_SERIES_LEN, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MACHINE_BARCODE_OFFSET},
    {SEG_DATA , type_string, ARRAY_SIZE_16, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA , type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA , type_date, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MANUFACTURER_DATE},
    {SEG_DATA,  type_string, DC_AC_PRO_MODEL_LEN_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PRODUCT_MODEL},
    {SEG_DATA,  type_string, ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_POWER_HARDWARE_VERSION},
};

//生产信息
static data_info_id_verison_t pack_para_produce_data_info[] RAM_SECTION ={
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_RATED_POWER_FAC_PARAM},
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MAX_ACTIVE_POWER_FAC_PARAM},
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MAX_APPARENT_POWER_FAC_PARAM},
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MAX_REACTIVE_POWER_FAC_PARAM},
};
//资产信息
static data_info_id_verison_t pack_para_prop_data_info[] RAM_SECTION ={
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MANUFACTURER_ID},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MANUFACTURER_ADDRESS},
    {SEG_SYSTEM_PARA, type_string, DC_AC_MACH_SERIES_LEN, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MACHINE_BARCODE_OFFSET},
    {SEG_DATA, type_string, ARRAY_SIZE_16, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_date, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_MANUFACTURER_DATE},
};

//产品信息
static data_info_id_verison_t pack_para_prop2_data_info[] RAM_SECTION ={
    {SEG_DATA, type_string, DC_AC_PRO_MODEL_LEN_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PRODUCT_MODEL},
};

//硬件版本
static data_info_id_verison_t pack_para_prop3_data_info[] RAM_SECTION ={
    {SEG_DATA, type_string, ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_POWER_HARDWARE_VERSION},
};

static data_info_id_verison_t pack_para_user_auth_time[] RAM_SECTION ={
    {SEG_SYSTEM_PARA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_USER_ACCOUNT_PERMANENTLY_VALID_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_USER_ACCOUNT_EXPIRATION_DATE_OFFSET},
};


///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//设置
/*------------------------------------------------设置保护参数，cid2=E5------------------------------------------------------------------------------*/
//一级保护参数
static data_info_id_verison_t parse_prot_para1_data_info[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_OVER_VOLT_PROT_POINT_GRID_OFFSET},
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,   DAC_PARA_ID_OVER_VOLT_PROT_TIME_GRID_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_UNDER_VOLT_PROT_POINT_GRID_OFFSET},
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,   DAC_PARA_ID_UNDER_VOLT_PROT_TIME_GRID_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_OVER_FREQ_PROT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,   DAC_PARA_ID_OVER_FREQ_PROT_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_UNDER_FREQ_PROT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,   DAC_PARA_ID_UNDER_FREQ_PROT_TIME_OFFSET},
};
//二级保护参数
static data_info_id_verison_t parse_prot_para2_data_info[] RAM_SECTION ={

    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,DAC_PARA_ID_OVER_VOLT_PROT_POINT_GRID_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_VOLT_PROT_TIME_GRID_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,DAC_PARA_ID_UNDER_VOLT_PROT_POINT_GRID_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_VOLT_PROT_TIME_GRID_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,DAC_PARA_ID_OVER_FREQ_PROT_POINT_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,  DAC_PARA_ID_OVER_FREQ_PROT_TIME_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,DAC_PARA_ID_UNDER_FREQ_PROT_POINT_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,  DAC_PARA_ID_UNDER_FREQ_PROT_TIME_OFFSET + 1},
};
//三级保护参数
static data_info_id_verison_t parse_prot_para3_data_info[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,DAC_PARA_ID_OVER_VOLT_PROT_POINT_GRID_OFFSET + 2},
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,  DAC_PARA_ID_OVER_VOLT_PROT_TIME_GRID_OFFSET + 2},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,DAC_PARA_ID_UNDER_VOLT_PROT_POINT_GRID_OFFSET + 2},
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,  DAC_PARA_ID_UNDER_VOLT_PROT_TIME_GRID_OFFSET + 2},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,DAC_PARA_ID_OVER_FREQ_PROT_POINT_OFFSET + 2},
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,  DAC_PARA_ID_OVER_FREQ_PROT_TIME_OFFSET + 2},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_2, DATA_DROP,NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_UNDER_FREQ_PROT_POINT_OFFSET + 2},
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,  DAC_PARA_ID_UNDER_FREQ_PROT_TIME_OFFSET + 2},
};

//四级保护参数
static data_info_id_verison_t parse_prot_para4_data_info[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,DAC_PARA_ID_OVER_VOLT_PROT_POINT_GRID_OFFSET + 3},
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,  DAC_PARA_ID_OVER_VOLT_PROT_TIME_GRID_OFFSET + 3},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,DAC_PARA_ID_UNDER_VOLT_PROT_POINT_GRID_OFFSET + 3},
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,  DAC_PARA_ID_UNDER_VOLT_PROT_TIME_GRID_OFFSET + 3},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,DAC_PARA_ID_OVER_FREQ_PROT_POINT_OFFSET + 3},
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,  DAC_PARA_ID_OVER_FREQ_PROT_TIME_OFFSET + 3},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,DAC_PARA_ID_UNDER_FREQ_PROT_POINT_OFFSET + 3},
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,  DAC_PARA_ID_UNDER_FREQ_PROT_TIME_OFFSET + 3},
};

//五级保护参数
static data_info_id_verison_t parse_prot_para5_data_info[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_OVER_VOLT_PROT_POINT_GRID_OFFSET + 4},
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,   DAC_PARA_ID_OVER_VOLT_PROT_TIME_GRID_OFFSET + 4},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_UNDER_VOLT_PROT_POINT_GRID_OFFSET + 4},
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,   DAC_PARA_ID_UNDER_VOLT_PROT_TIME_GRID_OFFSET + 4},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_OVER_FREQ_PROT_POINT_OFFSET + 4},
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,   DAC_PARA_ID_OVER_FREQ_PROT_TIME_OFFSET + 4},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_UNDER_FREQ_PROT_POINT_OFFSET + 4},
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,   DAC_PARA_ID_UNDER_FREQ_PROT_TIME_OFFSET + 4},
};

//六级保护函数
static data_info_id_verison_t parse_prot_para6_data_info[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_OVER_VOLT_PROT_POINT_GRID_OFFSET + 5},
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,   DAC_PARA_ID_OVER_VOLT_PROT_TIME_GRID_OFFSET + 5},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_UNDER_VOLT_PROT_POINT_GRID_OFFSET + 5},
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,   DAC_PARA_ID_UNDER_VOLT_PROT_TIME_GRID_OFFSET + 5},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_OVER_FREQ_PROT_POINT_OFFSET + 5},
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,   DAC_PARA_ID_OVER_FREQ_PROT_TIME_OFFSET + 5},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_UNDER_FREQ_PROT_POINT_OFFSET + 5},
    {SEG_SYSTEM_PARA, type_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,   DAC_PARA_ID_UNDER_FREQ_PROT_TIME_OFFSET + 5},
};
/*------------------------------------------------设置特征曲线参数，cid2=E3------------------------------------------------------------------------------*/
//LVRT特征曲线
static data_info_id_verison_t parse_para_lvrt_cc_data_info[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_CHARACTER_CURVE_POINT_OFFSET},
    {SEG_LOOP_SYS_PARA, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CHARACTER_CURVE_POINT_OFFSET}, //LOOP开始
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,DAC_PARA_ID_VOLT_PERCENT_OFFSET},
    {SEG_LOOP_OVER},  //LOOP结束
};

//cosφ-P/Pn特性曲线
static data_info_id_verison_t parse_para_P_Pn_CC_data_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_QP_MODE_TRIGG_VOLT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_QP_MODE_EXIT_VOLT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_SETTING_POINT_OF_COS_CURVE_OFFSET},
    {SEG_LOOP_SYS_PARA, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_SETTING_POINT_OF_COS_CURVE_OFFSET}, //LOOP开始
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_P_PN_AXIS_OFFSET},
    {SEG_SYSTEM_PARA, type_short,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_COS_AXIS_OFFSET},
    {SEG_LOOP_OVER},  //LOOP结束
};

//Q-U特性曲线设置
static data_info_id_verison_t parse_para_Q_U_CC_data_info[]RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_QU_SCHEDU_TRIGG_POWER_PERCENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_QU_SCHEDU_EXIT_POWER_PERCENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_QU_CHARACTER_CURVE_MIN_PF_LIMIT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_HYSTERESIS_RATIO_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_NUMBER_OF_POINT_FOR_QU_CURVE_OFFSET},
    {SEG_LOOP_SYS_PARA, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NUMBER_OF_POINT_FOR_QU_CURVE_OFFSET}, //LOOP开始
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_U_UN_AXIS_OFFSET},
    {SEG_SYSTEM_PARA, type_short,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_QS_AXIS_OFFSET},
    {SEG_LOOP_OVER},  //LOOP结束
};

//PF-U特性曲线设置
static data_info_id_verison_t parse_para_PF_U_CC_data_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_VOLT_DETECT_FILTER_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NUMBER_OF_POINT_FOR_PF_CURVE_OFFSET},
    {SEG_LOOP_SYS_PARA, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NUMBER_OF_POINT_FOR_PF_CURVE_OFFSET}, //LOOP开始
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_U_AXIS_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PF_AXIS_OFFSET},
    {SEG_LOOP_OVER},  //LOOP结束
};

//HVRT特征曲线
static data_info_id_verison_t parse_para_hvrt_cc_data_info[]={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA,   type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_POINT_OFFSET},
    {SEG_LOOP_SYS_PARA, type_char,             ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_CHARACTER_CURVE_POINT_OFFSET}, //LOOP开始
    {SEG_SYSTEM_PARA,   type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET},
    {SEG_SYSTEM_PARA,   type_unsigned_short,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET},
    {SEG_LOOP_OVER},  //LOOP结束
};

/*------------------------------------------------设置系统参数，cid2=49------------------------------------------------------------------------------*/
//控制参数
static data_info_id_verison_t parse_para_power_ctrl_data_info[]={

    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_POWER_ON_OFF_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PHASE_SEQ_ADAPTIVE_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_CONNECT_TYPE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_EN_LEAK_CURR_SELF_CHECK_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_RCD_BOOST_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_WEAK_GRID_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_WATER_INGRESS_ENABLE_OFFSET},
};



//电网参数
static data_info_id_verison_t parse_para_power_grid_data_info[] RAM_SECTION ={

    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GRID_ISOLATION_SETTING_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OUTPUT_MODE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ATUO_START_AFTER_GRID_FAULT_RECOVER_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CONNECT_TIME_AFTER_GRID_FAULT_RECOVER_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_DELAY_START_TIME_AFTER_GRID_FAULT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UPPER_LIMIT_GRID_RECONN_VOLT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LOWER_LIMIT_GRID_RECONN_VOLT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UPPER_LIMIT_GRID_RECONN_FREQ_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LOWER_LIMIT_GRID_RECONN_FREQ_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PQ_MODE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LOWER_LIMIT_GRID_CONN_START_VOLT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UPPER_LIMIT_GRID_CONN_START_FREQ_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LOWER_LIMIT_GRID_CONN_START_FREQ_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_RATED_GRID_VOLT_OFFSET},
};

//特性参数
static data_info_id_verison_t parse_para_chara_data_info[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_STANDBY_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_BOOT_SOFT_START_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ISLAND_DETECT_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ENABLE_GRID_VOLT_PROT_SHEILD_DURING_VRT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_VRT_EXIT_HYSTERESIS_THRESHLOD_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GRID_FAULT_ZERO_CURR_MODE_OFFSET},
};

//MPPT参数
static data_info_id_verison_t parse_para_mppt_data_info[]RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_SCAN_INTERVAL_OFFSET},
};

//LVRT参数设置
static data_info_id_verison_t parse_para_lvrt_data_info[]RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_TRIGG_THRESHOLD_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_POS_SEQ_REACTIVE_POWER_COMPEN_FACTOR_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_NEG_SEQ_REACTIVE_POWER_COMPEN_FACTOR_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_REACTIVE_CURR_LIMIT_PERCENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_ZERO_CURR_MODE_THRESHOLD_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_MODE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LVRT_ACTIVE_CURR_MAINTEN_COEFFICIENT_OFFSET},
};

//HVRT使能
static data_info_id_verison_t parse_para_hvrt_data_info[]RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_TRIGG_THRESHOLD_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_POS_SEQ_REACTIVE_POWER_COMPEN_FACTOR_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HVRT_NEG_SEQ_REACTIVE_POWER_COMPEN_FACTOR_OFFSET},
};

//PID参数设置
static data_info_id_verison_t parse_para_pid_data_info[] RAM_SECTION={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PID_REPAIR_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NIGHT_PID_PROT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NIGHT_SLEEP_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NIGHT_SLEEP_WAIT_TIME_OFFSET},
};

//AFCI参数
static data_info_id_verison_t parse_para_afci_data_info[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AFCI_DETECT_ON_OFF_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AFCI_CHECK_DETECTION_SENSITIVITY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AFCI_SAVE_ENABLE_OFFSET},
};

//并网点控制-馈电越限保护关机
static data_info_id_verison_t parse_para_conn_ctrl_data_info[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_FEED_OVER_LIMIT_PROT_SHUTDOWN_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_FEED_LIMIT_PROT_SHUTDOWN_THRESHOLD_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_FEED_LIMIT_PORT_SHUTDOWN_INTERVER_OFFSET},
};

static data_info_id_verison_t parse_para_prot_data_info[] RAM_SECTION={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GRID_VOLT_IMBALANCE_PROT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_INSULATION_IMPEDANCE_PROT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_TEN_MINUTE_GRID_OVER_PROT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_TEN_MINUTE_GRID_OVER_PROT_TIME_OFFSET},
};

//海拔高度
static data_info_id_verison_t parse_para_altitude[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA,type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ALTITUDE_OFFSET},
};

//电网标准码
static data_info_id_verison_t parse_para_grid_standard_code[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GRID_STANDARD_CODE_OFFSET},
};
// 组串接入检测
static data_info_id_verison_t parse_para_str_in_detection[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_LOSS_DETECTION_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_STARTING_CURRENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_TWO_IN_ONE_STARTING_CURRENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 2},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 3},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 4},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 5},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 6},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 7},

};

// 过欠频降额参数
static data_info_id_verison_t parse_para_over_under_freq_derate[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVERFREQUENCY_DERATING_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVERFREQUENCY_DERATING_TRIG_FREQUENCY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVERFREQUENCY_DERATING_EXIT_FREQUENCY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVERFREQUENCY_DERATING_CUTOFF_FREQUENCY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVERFREQUENCY_DERATING_RATED_POWER_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVERFREQUENCY_DERATING_RECOVERY_GRADIENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_FREQUENCY_CONTROL_FILTERING_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQUENCY_ACTIVE_DERATING_RECOVERY_DELAY_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_FREQUENCY_ACTIVE_DERATING_EFFECTIVE_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_TRIG_FREQUENCY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_EXIT_FREQUENCY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_CUTOFF_FREQUENCY_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_CUTOFF_POWER_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_RECOVERY_GRADIENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_ACTIVE_DERATING_RECOVERY_DELAY_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_UNDER_FREQUENCY_ACTIVE_DERATING_EFFECTIVE_TIME_OFFSET},

};


/*------------------------------------------------设置功率系统参数，cid2=E7------------------------------------------------------------------------------*/
//功率调节参数
static data_info_id_verison_t parse_para_power_regu_data_info[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REMOTE_POWER_DISPATCH_ENABLE_DISABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_SHCED_INSTRUCT_MAINTEN_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ACTIVE_POWER_REFERENCE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_APPARNET_POWER_REFERENCE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MAX_APPARENT_POWER_PARAM_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MAX_ACTIVE_POWER_PARAM_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_POWER_LIMIT_ZERO_PERCENT_SHUTDOWN_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NIGHT_REACTIVE_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NIGTH_REACTIVE_POWER_COMPEN_OFFSET},
    {SEG_SYSTEM_PARA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_SHUTDOWN_GRADIENT_OFFSET},

};

//通信锻炼失效保护参数
static data_info_id_verison_t parse_para_comm_fail_data_info[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ENABLE_COMMU_CHAIN_FAIL_PROT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_COMMU_BROKEN_CHAIN_DETECT_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_FAIL_PROT_ACTIVE_POWER_MODE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ACTIVE_POWER_LIMIT_FAIL_POWER_PERCENT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ACTIVE_POWER_LIMIT_FAIL_PROT_VALUE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_FAIL_PROT_REACTIVE_POWER_MODE_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_FAIL_PROT_REACTIVE_LIMIT_VALUE_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_FAIL_PROT_REACTIVE_POWER_LIMIT_PROT_OFFSET},
};

// 有功功率调节参数
static data_info_id_verison_t parse_para_active_power_reg_data_info[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GRID_ACTIVE_POWER_CHANGE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ACTIVE_POWER_CONTROL_MODE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ACTIVE_POWER_DERATING_SETTING_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ACTIVE_POWER_DERATING_PERCENT_OFFSET},
};

//无功功率调节参数
static data_info_id_verison_t parse_para_reactive_power_regu_data_info[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_GRID_REACTIVE_POWER_CHANGE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REACTIVE_POWER_COMPEN_MODE_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REACTIVE_POWER_COMPEN_SETTING_FACTOR_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REACTIVE_POWER_COMPEN_SETTING_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REACTIVE_POWER_REGULATIONG_TIME_OFFSET},

    {SEG_SYSTEM_PARA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REACTIVE_POWER_VALUE_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_REACTIVE_POWER_PCT_OFFSET},

};

//并网点控制-有功功率
static data_info_id_verison_t parse_para_parall_point_ctrl_active[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA,type_short,ARRAY_SIZE_1,DOP_0,DATA_DROP, NO_DEF_VALUE, NEED_INTERACT ,DAC_PARA_ID_GRID_CONNECT_ACTIVE_POWER_CONTROL_MODE_OFFSET},
    {SEG_SYSTEM_PARA,type_short,ARRAY_SIZE_1,DOP_0,DATA_DROP, NO_DEF_VALUE, NEED_INTERACT ,DAC_PARA_ID_CLOSE_LOOP_CONTROLLER_OFFSET},
    {SEG_SYSTEM_PARA,type_short,ARRAY_SIZE_1,DOP_0,DATA_DROP, NO_DEF_VALUE, NEED_INTERACT ,DAC_PARA_ID_RESTRICTION_METHOD_OFFSET},
    {SEG_SYSTEM_PARA,type_short,ARRAY_SIZE_1,DOP_1,DATA_DROP, NO_DEF_VALUE, NEED_INTERACT ,DAC_PARA_ID_POWER_REGULAR_CYCLE_OFFSET},
    {SEG_SYSTEM_PARA,type_int,ARRAY_SIZE_1,DOP_3,DATA_DROP, NO_DEF_VALUE , NEED_INTERACT ,DAC_PARA_ID_POWER_UP_THRESHOLD_OFFSET},
    {SEG_SYSTEM_PARA,type_unsigned_int,ARRAY_SIZE_1,DOP_3,DATA_DROP, NO_DEF_VALUE , NEED_INTERACT , DAC_PARA_ID_MAX_FEED_GRID_POWER_OFFSET},
    {SEG_SYSTEM_PARA,type_short,ARRAY_SIZE_1,DOP_1,DATA_DROP, NO_DEF_VALUE, NEED_INTERACT ,DAC_PARA_ID_MAX_FEED_GRID_POWER_PERCENT_OFFSET},
};
//并网点控制-无功功率
static data_info_id_verison_t parse_para_parall_point_ctrl_reactive[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA,type_unsigned_short,ARRAY_SIZE_1,DOP_0,DATA_DROP, NO_DEF_VALUE, NEED_INTERACT ,DAC_PARA_ID_GRID_CONNECT_REACTIVE_POWER_CONTROL_METHOD_OFFSET},
    {SEG_SYSTEM_PARA,type_short,ARRAY_SIZE_1,DOP_3,DATA_DROP, NO_DEF_VALUE, NEED_INTERACT ,DAC_PARA_ID_TARGET_POWER_METHOD_OFFSET},
    {SEG_SYSTEM_PARA,type_unsigned_short,ARRAY_SIZE_1,DOP_1,DATA_DROP, NO_DEF_VALUE, NEED_INTERACT ,DAC_PARA_ID_REACTIVE_POWER_REGULAR_CYCLE_OFFSET},
    {SEG_SYSTEM_PARA,type_unsigned_short,ARRAY_SIZE_1,DOP_3,DATA_DROP, NO_DEF_VALUE, NEED_INTERACT ,DAC_PARA_ID_REACTIVE_POWER_REGULAR_DEAD_ZONE_OFFSET},

};
/*------------------------------------------------零点比例校准参数，cid2=E9------------------------------------------------------------------------------*/
//输出电压比例校正参数
static data_info_id_verison_t parse_para_adj_out_volt_data_info[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CORRECT_RATIO_OF_GRID_VOLT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MASTER_NET_CA_VOLT_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CORRECT_RATIO_OF_GRID_VOLT_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MASTER_NET_CB_VOLT_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CORRECT_RATIO_OF_GRID_VOLT_OFFSET + 2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MASTER_NET_C_VOLT_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_INVERTER_VOLT_CORRECT_RATIO_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MASTER_INVERTER_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_INVERTER_VOLT_CORRECT_RATIO_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MASTER_INVERTER_CORRECT_POINT_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_INVERTER_VOLT_CORRECT_RATIO_OFFSET + 2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MASTER_INVERTER_CORRECT_POINT_OFFSET + 2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_SINGLE_PHASE_GRID_CONNECT_CURR_CORRECT_RATIO_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MASTER_GRID_CURR_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_SINGLE_PHASE_GRID_CONNECT_CURR_CORRECT_RATIO_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MASTER_GRID_CURR_CORRECT_POINT_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_SINGLE_PHASE_GRID_CONNECT_CURR_CORRECT_RATIO_OFFSET + 2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MASTER_GRID_CURR_CORRECT_POINT_OFFSET + 2},
};

//BUS电压比例校正
static data_info_id_verison_t parse_para_adj_bus_volt_data_info[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_POS_BUS_VOLT_CORRECT_RATIO_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MASTER_POS_BUS_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_NEG__BUS_VOLT_CORRECT_RATIO_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MASTER_NEG_BUS_CORRECT_POINT_OFFSET},
};

//MPPT1比例校正
static data_info_id_verison_t parse_para_adj_mppt1_data_info[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_INPUT_VOLT_CORRECT_RATIO_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_IN_VOLT_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_TOTAL_INPUT_CORRECT_RATIO_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ALLIN_CURR_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_BRANCH_INPUT_CURR_CORRECT_RATIO_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_BRANCH_IN_CURR_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_SAMPLE_CURRENT_CORRECT_RATIO_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_SAMPLE_CURRENT_CORRECT_POINT_OFFSET},

};

//MPPT2比例校正
static data_info_id_verison_t parse_para_adj_mppt2_data_info[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_INPUT_VOLT_CORRECT_RATIO_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_IN_VOLT_CORRECT_POINT_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_TOTAL_INPUT_CORRECT_RATIO_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ALLIN_CURR_CORRECT_POINT_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_BRANCH_INPUT_CURR_CORRECT_RATIO_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_BRANCH_IN_CURR_CORRECT_POINT_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_SAMPLE_CURRENT_CORRECT_RATIO_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_SAMPLE_CURRENT_CORRECT_POINT_OFFSET + 1},
};

//MPPT3比例校正
static data_info_id_verison_t parse_para_adj_mppt3_data_info[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_INPUT_VOLT_CORRECT_RATIO_OFFSET + 2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_IN_VOLT_CORRECT_POINT_OFFSET + 2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_TOTAL_INPUT_CORRECT_RATIO_OFFSET + 2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ALLIN_CURR_CORRECT_POINT_OFFSET + 2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_BRANCH_INPUT_CURR_CORRECT_RATIO_OFFSET + 2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_BRANCH_IN_CURR_CORRECT_POINT_OFFSET + 2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_SAMPLE_CURRENT_CORRECT_RATIO_OFFSET + 2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_SAMPLE_CURRENT_CORRECT_POINT_OFFSET + 2},
};

//MPPT4比例校正
static data_info_id_verison_t parse_para_adj_mppt4_data_info[] RAM_SECTION = {
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_INPUT_VOLT_CORRECT_RATIO_OFFSET + 3},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_IN_VOLT_CORRECT_POINT_OFFSET + 3},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_TOTAL_INPUT_CORRECT_RATIO_OFFSET + 3},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ALLIN_CURR_CORRECT_POINT_OFFSET + 3},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_BRANCH_INPUT_CURR_CORRECT_RATIO_OFFSET + 3},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_BRANCH_IN_CURR_CORRECT_POINT_OFFSET + 3},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_SAMPLE_CURRENT_CORRECT_RATIO_OFFSET + 3},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_OVER_SAMPLE_CURRENT_CORRECT_POINT_OFFSET + 3},
};

//辅控比例校正
static data_info_id_verison_t parse_para_adj_adj_aux_data_info[] RAM_SECTION ={

    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUXI_CONTROL_GRID_VOLT_CORRECT_RATIO_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUX_NET_CA_VOLT_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUXI_CONTROL_GRID_VOLT_CORRECT_RATIO_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUX_NET_CB_VOLT_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUXI_CONTROL_GRID_VOLT_CORRECT_RATIO_OFFSET + 2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUX_NET_C_VOLT_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUXI_CONTROL_INVERTER_VOLT_CORRECT_RATIO_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUX_INVERTER_PHASE_VOLT_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUXI_CONTROL_INVERTER_VOLT_CORRECT_RATIO_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUX_INVERTER_PHASE_VOLT_CORRECT_POINT_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUXI_CONTROL_INVERTER_VOLT_CORRECT_RATIO_OFFSET + 2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUX_INVERTER_PHASE_VOLT_CORRECT_POINT_OFFSET + 2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUXI_CONTROL_INVERTER_CURR_CORRECT_RATIO_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUX_GRID_CURR_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUXI_CONTROL_INVERTER_CURR_CORRECT_RATIO_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUX_GRID_CURR_CORRECT_POINT_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUXI_CONTROL_INVERTER_CURR_CORRECT_RATIO_OFFSET + 2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUX_GRID_CURR_CORRECT_POINT_OFFSET + 2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUXI_CONTROL_POS_BUS_VOLT_CORRECT_RATIO_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUX_POS_BUS_VOLT_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUXI_CONTROL_NEG_BUS_VOLT_CORRECT_RATIO_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUX_NEG_BUS_VOLT_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUXI_CONTROL_DC_ARC_DETECT_CORRECT_RATIO_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_DC_ARC_CORRECT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUXI_CONTROL_DC_ARC_DETECT_CORRECT_RATIO_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_DC_ARC_CORRECT_POINT_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUXI_CONTROL_DC_ARC_DETECT_CORRECT_RATIO_OFFSET + 2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_DC_ARC_CORRECT_POINT_OFFSET + 2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUXI_CONTROL_DC_ARC_DETECT_CORRECT_RATIO_OFFSET + 3},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_DC_ARC_CORRECT_POINT_OFFSET + 3},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_AUXI_CONTROL_PE_VOLT_CORRECT_RATIO_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PE_VOLT_CORRECT_POINT_OFFSET},
};

//漏电流比例校正
static data_info_id_verison_t parse_para_leak_curr_data_info[] RAM_SECTION = {
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LEAK_CURR_CORRECT_RATIO_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LEAK_CURR_CORRECT_POINT_OFFSET},
};

//直流分量比例校正
static data_info_id_verison_t parse_dc_component_data_info[] RAM_SECTION = {
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PV_CURR_DC_COMPONENT_RATIO_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PV_CURR_DC_COMPONENT_POINT_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PV_CURR_DC_COMPONENT_RATIO_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PV_CURR_DC_COMPONENT_POINT_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PV_CURR_DC_COMPONENT_RATIO_OFFSET + 2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PV_CURR_DC_COMPONENT_POINT_OFFSET + 2},
};
/*------------------------------------------------生产参数，cid2=EC------------------------------------------------------------------------------*/
//设备条码
static data_info_id_verison_t parse_para_device_sn_data_info[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_string,   ARRAY_SIZE_12,DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT, },
};
//生产信息写入
static data_info_id_verison_t parse_para_produce_data_info[] RAM_SECTION ={
    {SEG_DATA, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_RATED_POWER_FAC_PARAM},
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MAX_ACTIVE_POWER_FAC_PARAM},
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MAX_APPARENT_POWER_FAC_PARAM},
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MAX_REACTIVE_POWER_FAC_PARAM},
};
//资产信息写入
static data_info_id_verison_t parse_para_prop_data_info[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MANUFACTURER_ID},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MANUFACTURER_ADDRESS},
    {SEG_SYSTEM_PARA, type_string, DC_AC_MACH_SERIES_LEN, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MACHINE_BARCODE_OFFSET},
    {SEG_DATA, type_string, ARRAY_SIZE_16, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_date, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_MANUFACTURER_DATE},

};

//产品信息写入
static data_info_id_verison_t parse_para_prop2_data_info[] RAM_SECTION = {
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_DATA, type_string, DC_AC_PRO_MODEL_LEN_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PRODUCT_MODEL},
};

//硬件版本写入
static data_info_id_verison_t parse_para_prop3_data_info[] RAM_SECTION = {
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_DATA, type_string, ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_POWER_HARDWARE_VERSION},
};

//控制命令
static data_info_id_verison_t parse_para_ctrl_cmd_data_info[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short,ARRAY_SIZE_1,DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT},
};

/*------------------------------------------------------------------------------------------------------------*/
//获取功率厂家信息
static data_info_id_verison_t pack_power_fac_data_info[] RAM_SECTION ={

    {SEG_DATA, type_string, ARRAY_SIZE_12, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_MASTER_CTRL_SOFTWARE_VER},
    {SEG_DATA, type_date, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_MASTER_CTRL_SOFTWARE_VER_DATE},
    {SEG_DATA, type_string, ARRAY_SIZE_12, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_AUXI_CTRL_SOFTWARE_VER},
    {SEG_DATA, type_date, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_AUXI_CTRL_SOFTWARE_VER_DATE},
    {SEG_DATA, type_string, ARRAY_SIZE_12, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_CPLD_SOFTWARE_VER},
    {SEG_DATA, type_date, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_CPLD_SOFTWARE_VER_DATE},
    {SEG_SYSTEM_PARA, type_string, ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PSC_VERSION_OFFSET},

    {SEG_DATA, type_string, ARRAY_SIZE_8, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_MASTER_CTRL_PROTOCOL_VERSION},
    {SEG_DATA, type_string, ARRAY_SIZE_8, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_SLAVE_CTRL_PROTOCOL_VERSION},
    {SEG_DATA, type_string, ARRAY_SIZE_8, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_CPLD_PROTOCOL_VERSION},
};

//获取监控厂家信息
static data_info_id_verison_t pack_csu_fac_data_info[] RAM_SECTION ={

    {SEG_SYSTEM_PARA, type_string, ARRAY_SIZE_10, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_string, ARRAY_SIZE_10, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MONITOR_SOFTWARE_VER},
    {SEG_DATA, type_string, ARRAY_SIZE_20, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_FACTORY_NAME},
    {SEG_SYSTEM_PARA, type_string, ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_CSC_VERSION_OFFSET},
    {SEG_DATA, type_time, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_CA_CERT_START_TIME},
    {SEG_DATA, type_time, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_CA_CERT_END_TIME},
    {SEG_DATA, type_time, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_CLIENT_CERT_START_TIME},
    {SEG_DATA, type_time, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_CLIENT_CERT_END_TIME},
    {SEG_DATA, type_time, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_CA_CERT_START_TIME + 1},
    {SEG_DATA, type_time, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_CA_CERT_END_TIME + 1},
    {SEG_DATA, type_time, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_CLIENT_CERT_START_TIME + 1},
    {SEG_DATA, type_time, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_CLIENT_CERT_END_TIME + 1},
    {SEG_DATA, type_date, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_MONITOR_SOFTWARE_VER_DATE},
    {SEG_DATA, type_string, ARRAY_SIZE_16, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MONITOR_BOOT_VER},
    {SEG_DATA, type_date, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MONITOR_BOOT_VER_DATE},

};
//获取监控厂家信息
static data_info_id_verison_t parse_diff_update_flag_info[] RAM_SECTION ={

    {SEG_DATA, type_string, ARRAY_SIZE_40, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MONITOR_DIFF_UPDATE_FLAG},

};

/*------------------------------------------------------------------------------------------------------------*/
static data_info_id_verison_t pack_all_power_special_para_info[] RAM_SECTION ={

    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_HARDWARE_VERSION},  
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_LEAK_CURR_PROTECTION_PROHIBITION},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_LOW_IMPEDANCE_PROTECTION_PROHIBITION},

    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_DUTY_RATIO},  
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_DUTY_RATIO+1}, 
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_DUTY_RATIO+2}, 
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_DUTY_RATIO+3}, 
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_VOLTAGE_PEAK},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_VOLTAGE_FREQUENCY},

    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ADC_SAMPLING_TEST_ENABLE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_TARGET_SAMPLING_CHANNEL},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_SET_SAMPLING_VALUE},    

    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_INTERNAL_FAN_DUTY_CYCLE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_EXTERNAL_FAN_DUTY_CYCLE},

};
//获取功率研发专用
static data_info_id_verison_t pack_power_special_para_info[] RAM_SECTION ={

    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_HARDWARE_VERSION},  
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_LEAK_CURR_PROTECTION_PROHIBITION},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_LOW_IMPEDANCE_PROTECTION_PROHIBITION},

};
//设置功率研发专用
static data_info_id_verison_t parse_power_special_para_info[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_HARDWARE_VERSION_OFFSET},  
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LEAK_CURR_PROTECTION_PROHIBITION_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LOW_IMPEDANCE_PROTECTION_PROHIBITION_OFFSET},

};

//获取功率研发专用-驱动测试
static data_info_id_verison_t pack_drive_test_para_info[] RAM_SECTION ={
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_DUTY_RATIO},  
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_DUTY_RATIO+1}, 
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_DUTY_RATIO+2}, 
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_DUTY_RATIO+3}, 
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_VOLTAGE_PEAK},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_VOLTAGE_FREQUENCY},
};

//设置功率研发专用-驱动测试
static data_info_id_verison_t parse_drive_test_para_info[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_DUTY_RATIO_OFFSET},  
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_DUTY_RATIO_OFFSET+1}, 
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_DUTY_RATIO_OFFSET+2}, 
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_MPPT_DUTY_RATIO_OFFSET+3}, 
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PV_VOLTAGE_PEAK_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_PV_VOLTAGE_FREQUENCY_OFFSET},
};

//获取功率研发专用-adc采样测试参数
static data_info_id_verison_t pack_adc_test_para_info[] RAM_SECTION ={
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ADC_SAMPLING_TEST_ENABLE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_TARGET_SAMPLING_CHANNEL},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_SET_SAMPLING_VALUE},    
};

//设置功率研发专用-adc采样测试参数
static data_info_id_verison_t parse_adc_test_para_info[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_ADC_SAMPLING_TEST_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_TARGET_SAMPLING_CHANNEL_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_SET_SAMPLING_VALUE_OFFSET},    
};

//获取功率研发专用-生产测试相关设置参数
static data_info_id_verison_t pack_product_test_para_info[] RAM_SECTION ={
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_INTERNAL_FAN_DUTY_CYCLE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_EXTERNAL_FAN_DUTY_CYCLE},
};

//设置功率研发专用-生产测试相关设置参数
static data_info_id_verison_t parse_product_test_para_info[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_INTERNAL_FAN_DUTY_CYCLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_EXTERNAL_FAN_DUTY_CYCLE_OFFSET},
};

/*获取模拟量 有效值*/
static data_info_id_verison_t cmd_pack_ana_valid_data_info[] RAM_SECTION = 
{
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NOT_NEED_INTERACT}, 

    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_NUM_MAX, NOT_NEED_INTERACT}, 
    {SEG_LOOP, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_NUM_MAX, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_int,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,   DAC_DATA_ID_MPPT_TOTAL_INPUT_POWER},  
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_VOLT},  
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_CURR},  
    {SEG_LOOP_OVER},  

    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, PV_NUM_MAX, NOT_NEED_INTERACT},
    {SEG_LOOP, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PV_NUM_MAX, NOT_NEED_INTERACT},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_PV_VOLT},  
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR}, 
    {SEG_LOOP_OVER},  

    {SEG_DATA, type_int,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ACTIVE_POWER}, 
    {SEG_DATA, type_int,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_REACTIVE_POWER}, 
    {SEG_DATA, type_int,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_APPARENT_POWER},
    
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_POWER_FACTOR},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_GRID_FREQ},  
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_GRID_AB_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_GRID_BC_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_GRID_CA_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_GRID_PHASE_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_GRID_PHASE_VOLT + 1},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_GRID_PHASE_VOLT + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_GRID_PHASE_CURR},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_GRID_PHASE_CURR + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_GRID_PHASE_CURR + 2},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_AB_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_BC_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CA_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_PHASE_VOLT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_PHASE_VOLT + 1},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_PHASE_VOLT + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_RES_CURR_AC_COMPONENT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_RES_CURR_DC_COMPONENT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_DC_COMPONENT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_DC_COMPONENT + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_DC_COMPONENT + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_AVERAGE_VALUE},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_AVERAGE_VALUE + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CURR_AVERAGE_VALUE + 2},

    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_TOTAL_INPUT_POWER},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_INTERNAL_TEMPER},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_POS_INSULATION_IMPERD},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_EFFICIENCY},

    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_NET_NUMBER},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_NUMBER},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_RATED_NUMBER},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MAX_ACTIVE_POWER},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MAX_APPARENT_POWER},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MAX_REACTIVE_POWER},

    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_DAILY_POWER_GENERATION},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MONTH_POWER_GENERATION},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_YEAR_POWER_GENERATION},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ACCUMLATE_POWER_GENERATION},

    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_CO2_DAY_EMISSION},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_CO2_MONTH_EMISSION},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_CO2_YEAR_EMISSION},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_CO2_ACCMULATE_EMISSION},

    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_TOTAL_GRID_CONNECT_RUNNING_TIME},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_DAILY_GRID_CONNECT_RUNNING_TIME},

    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_BUS_VOLT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_POS_BUS_VOLT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_NEG_BUS_VOLT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_REDIATOR_TEMPER},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_REDIATOR_TEMPER + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_REDIATOR_TEMPER + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PCB_TEMPER},
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_NUM_MAX, NOT_NEED_INTERACT}, 
    {SEG_LOOP, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, MPPT_NUM_MAX, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OVERSAMPLE_CURRENT},  
    {SEG_LOOP_OVER}, 

    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_SHORT_CIRCUIT_POSITION_WITH_LOW_INSULAT_IMPEDANCE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_FAULT_STRING_WITH_LOW_INSULAT_IMPEDANCE},
};

//获取模拟量2 有效值
static data_info_id_verison_t cmd_pack_ana_valid_data_info_2[] RAM_SECTION ={
    {SEG_DATA, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_VERF_SAMPLE},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_VERF_SAMPLE + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_VERF_SAMPLE + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_VCC_5V_CTL},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_VCC_5V_CTL +  1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_VERF_SAMPLE + 3},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_PROTECTION_THRESHOLD},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_INV_PROTECTION_THRESHOLD},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_DC_13V_CTL},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AC_13V_CTL},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_DC_LIGHTNING_PROTECTION_ERR},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AC_LIGHTNING_PROTECTION_ERR},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_SWITCH_RELEASE_FB},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_V_PE_IR},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_V_PE_IR + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_V_PE_IR + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_CHECK_PID},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_RELAY_SUPPLLY_VOLT},

    {SEG_DATA, type_unsigned_short,  ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_NET_VOLT},
    {SEG_DATA, type_unsigned_short,  ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_NET_VOLT+1},
    {SEG_DATA, type_unsigned_short,  ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_NET_VOLT+2},

    {SEG_DATA, type_unsigned_short,  ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_NET_PHASE_VOLT},
    {SEG_DATA, type_unsigned_short,  ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_NET_PHASE_VOLT+1},
    {SEG_DATA, type_unsigned_short,  ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_NET_PHASE_VOLT+2},

    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_NET_PHASE_CURR},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_NET_PHASE_CURR+1},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_NET_PHASE_CURR+2},

    {SEG_DATA, type_unsigned_short,  ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_INVERTER_VOLT},
    {SEG_DATA, type_unsigned_short,  ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_INVERTER_VOLT+1},
    {SEG_DATA, type_unsigned_short,  ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_INVERTER_VOLT+2},

    {SEG_DATA, type_unsigned_short,  ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_INVERTER_PHASE_VOLT},
    {SEG_DATA, type_unsigned_short,  ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_INVERTER_PHASE_VOLT+1},
    {SEG_DATA, type_unsigned_short,  ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_INVERTER_PHASE_VOLT+2},

    {SEG_DATA, type_unsigned_short,  ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_BUSBAR_VOL},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_POS_BUSBAR_VOL},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_NEG_BUSBAR_VOL},

    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},

    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AUXILIARY_PE_VOL},
    {SEG_DATA, type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AFCI_LOG_SAVE_NUM},
    {SEG_DATA, type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AFCI_LOG_SAVED_NUM},
    {SEG_DATA, type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_USER_ACCOUNT_REMAIN_VALID_TIME},

};

// 获取状态量
static data_info_id_verison_t cmd_pack_dig_data_info[] RAM_SECTION =
{
    {SEG_DATA, type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_DEV_STATUS},  
    {SEG_DATA, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, PV_NUM_MAX, NOT_NEED_INTERACT},
    {SEG_LOOP, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, PV_NUM_MAX, NOT_NEED_INTERACT},
    {SEG_DATA, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_CONNECTED_STATUS},
    {SEG_DATA, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PV_SCAN_STATUS},
    {SEG_LOOP_OVER},
    {SEG_DATA, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_INPUT_UNDERVOLT_STA},
    {SEG_DATA, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_IV_COMPLETE_STATUS},
    {SEG_DATA, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_RELAY_STATUS},
    {SEG_DATA, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_RELAY_STATUS + 1},
    {SEG_DATA, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_RELAY_STATUS + 2},
    {SEG_DATA, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_RELAY_STATUS + 3},
    {SEG_DATA, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_RELAY_STATUS + 4},
    {SEG_DATA, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_OUT_RELAY_STATUS},
    {SEG_DATA, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_UPDATE_STATUS},
    {SEG_DATA, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_GRID_CODE_STATUS},
    {SEG_DATA, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_CURRENT_LIMITING_STATUS},
    {SEG_DATA, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_CURRENT_LIMITING_STATUS + 1},
    {SEG_DATA, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_CURRENT_LIMITING_STATUS + 2},
    {SEG_DATA, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MPPT_CURRENT_LIMITING_STATUS + 3},
    {SEG_DATA, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_DC_INPUT_LOAD_SHEDDING},
    {SEG_DATA, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_MODULATION_INDEX_LOAD_SHEDDING},
    {SEG_DATA, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_RADIATOR_TEMPERATURE_LOAD_SHEDDING},
    {SEG_DATA, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_INTERNAL_TEMPERATURE_LOAD_SHEDDING},
    {SEG_DATA, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PCB_TEMPERATURE_LOAD_SHEDDING},
    {SEG_DATA, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_GRID_VOLTAGE_LOAD_SHEDDING},
    {SEG_DATA, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_ALTITUDE_LOAD_SHEDDING},

    {SEG_DATA, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_INSULATION_IMPEDANCE_LOW_POSITION_STATE},
    {SEG_DATA, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_PID_REPAIR_STATUS},
    {SEG_DATA, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_DATA_ID_AFCI_FILE_STATUS},
};

static data_info_id_verison_t pack_alm_relay_info[] RAM_SECTION =
{
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MPPT_OVER_VOLT_RELAY},//MPPT过压
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_BUS_OVER_VOLT_RELAY},//母线过压
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_POS_BUS_OVER_VOLT_RELAY},//正母线过压
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_NEG_BUS_OVER_VOLT_RELAY},//负母线过压
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_BUS_VOLT_IMBALANCE_RELAY},//母线电压不平衡
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_STR_REVERSE_CONNECT_RELAY},//组串反接
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_STR_LOSS_RELAY},//组串丢失
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MPPT_OVER_CURR_WARN_RELAY},//MPPT过流告警
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MPPT_OVER_CURR_ERROR_RELAY},//MPPT过流故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_BUS_SOFT_START_ERR_RELAY},//母线软启动故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_DC_DISCONNECTOR_TRIP_RELAY},//直流隔离开关脱扣
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_PHASE_VOLT_OVER_VOLT_RELAY},//交流相电压过压
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_OVER_VOLT_BETWEEN_PHASE_RELAY},//交流线电压过压
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_PHASE_VOLT_UNDER_VOLT_RELAY},//交流相电压欠压
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_UNDER_VOLT_BETWEEN_PHASE_RELAY},//交流线电压欠压
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_GRID_OVER_FREQ_RELAY},//电网过频
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_GRID_UNDER_FREQ_RELAY},//电网欠频
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_AC_PHASE_LOSS_RELAY},//交流缺相
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_OUTPUT_SHORT_CIRCUIT_RELAY},//输出短路保护
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_OUT_CURR_IMBALANCE_RELAY},//逆变输出电流不平衡
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_ABNORMAL_DC_COMPONENT_RELAY},//直流分量异常
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_ISLAND_PROT_RELAY},//防孤岛保护（孤岛保护）
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_PV_OVER_CURR_WARN_RELAY},//逆变器输出相过流告警
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_PV_OVER_CURR_ERROR_RELAY},//逆变器输出相过流故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_GRID_ANTI_ERROR_RELAY},//电网反序故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_SINGLE_PHASE_GND_SHORT_CIRCUIT_RELAY},//相对地短路
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_LOW_POWER_SHUTDOWN_RELAY},//低功率关机
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_LOW_VOLT_RT_RELAY},//低穿标志位（低电压穿越）
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_HIGH_VOLT_RT_RELAY},//高电压穿越标志（高电压穿越）
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_TEN_MINUTE_GRID_OVER_VOLT_PROT_RELAY},//十分钟电网电压过压保护
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_ENVIR_OVER_TEMP_RELAY},//环境温度过高（状态量）（环境温度过温）
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_ENVIR_LOW_TEMP_RELAY},//环境温度过低（状态量）
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_ABN_RESIDUAL_CURR_RELAY},//残余电流异常
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NOT_NEED_INTERACT,},//母线正对地绝缘阻抗低告警
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NOT_NEED_INTERACT,},//母线负对地绝缘阻抗低告警
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NOT_NEED_INTERACT,},//母线正对地绝缘阻抗低故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NOT_NEED_INTERACT,},//母线负对地绝缘阻抗低故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_RADIATOR_OVER_TEMP_RELAY},//散热器过温
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_INTER_FAN_FAIL_RELAY},//内部风扇故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_EXTER_FAN_FAIL_RELAY},//外部风扇故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_OVER_TEMP_INSIDS_MACHINE_RELAY},//内部腔体温度过高（机内温度过温）
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_FIRE_ERROR_RELAY},//火灾故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_DC_ARC_DAULT_RELAY},//直流电弧故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_AFCI_SELF_CHK_FAIL_RELAY},//AFCI自检失败
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_AC_LIGHTING_PROTECTION_ERR_RELAY},//交流防雷失效
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_DC_LIGHTING_PROTECTION_ERR_RELAY},//直流防雷失效
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NOT_NEED_INTERACT,},//boostIGBT过温（X=1~12）(预留)
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NOT_NEED_INTERACT,},//逆变单相IGBT过温（X=A、B、C）(预留)
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_GRID_RELAY_ERROR_RELAY},//并网逆变器继电器故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_AC_CURR_SENSOR_ERROR_RELAY},//AC电流传感器故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_PV_LOCK_ERROR_RELAY},//逆变器锁相失败故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_ABNORMAL_OPER_BUILD_IN_PID_RELAY},//PID修复故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_ABN_AUXI_POWER_SUPPLT_RELAY},//辅助电源异常
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MAIN_CTRL_EEPROM_FAULT_RELAY},//主控EEPROM故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MAIN_CTRL_EEPROM_ABNORMAL_RELAY},//主控EEPROM异常
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_AUXI_CTRL_EEPROM_FAULT_RELAY},//辅控EEPROM故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_AUXI_CTRL_EEPROM_ABNORMAL_RELAY},//辅控EEPROM异常
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MONITER_MAIN_CTRL_COMMU_ERROR_RELAY},//监控与主控通信故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_CARRIER_SYNC_ABNORMAL_RELAY},//载波同步信号异常（预留）
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_PROTOCOL_VER_MISMATCH_RELAY},//协议版本不匹配
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_LICENSE_EXPIRED_RELAY},//许可证过期
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MONITER_ERROR_ALARM_RELAY},//监控单元故障告警
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MAIN_AUXI_COMMU_ERROR_RELAY},//主控与辅控通讯故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MAIN_CPLD_COMMU_ERROR_RELAY},//主控与CPLD通讯故障

};

static data_info_id_verison_t pack_alm_level_info[] RAM_SECTION =
{
    
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MPPT_OVER_VOLT_LEVEL},//MPPT过压
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_BUS_OVER_VOLT_LEVEL},//母线过压
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_POS_BUS_OVER_VOLT_LEVEL},//正母线过压
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_NEG_BUS_OVER_VOLT_LEVEL},//负母线过压
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_BUS_VOLT_IMBALANCE_LEVEL},//母线电压不平衡
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_STR_REVERSE_CONNECT_LEVEL},//组串反接
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_STR_LOSS_LEVEL},//组串丢失
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MPPT_OVER_CURR_WARN_LEVEL},//MPPT过流告警
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MPPT_OVER_CURR_ERROR_LEVEL},//MPPT过流故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_BUS_SOFT_START_ERR_LEVEL},//母线软启动故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_DC_DISCONNECTOR_TRIP_LEVEL},//直流隔离开关脱扣
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_PHASE_VOLT_OVER_VOLT_LEVEL},//交流相电压过压
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_OVER_VOLT_BETWEEN_PHASE_LEVEL},//交流线电压过压
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_PHASE_VOLT_UNDER_VOLT_LEVEL},//交流相电压欠压
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_UNDER_VOLT_BETWEEN_PHASE_LEVEL},//交流线电压欠压
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_GRID_OVER_FREQ_LEVEL},//电网过频
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_GRID_UNDER_FREQ_LEVEL},//电网欠频
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_AC_PHASE_LOSS_LEVEL},//交流缺相
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_OUTPUT_SHORT_CIRCUIT_LEVEL},//输出短路保护
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_OUT_CURR_IMBALANCE_LEVEL},//逆变输出电流不平衡
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_ABNORMAL_DC_COMPONENT_LEVEL},//直流分量异常
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_ISLAND_PROT_LEVEL},//防孤岛保护（孤岛保护）
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_PV_OVER_CURR_WARN_LEVEL},//逆变器输出相过流告警
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_PV_OVER_CURR_ERROR_LEVEL},//逆变器输出相过流故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_GRID_ANTI_ERROR_LEVEL},//电网反序故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_SINGLE_PHASE_GND_SHORT_CIRCUIT_LEVEL},//相对地短路
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_LOW_POWER_SHUTDOWN_LEVEL},//低功率关机
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_LOW_VOLT_RT_LEVEL},//低穿标志位（低电压穿越）
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_HIGH_VOLT_RT_LEVEL},//高电压穿越标志（高电压穿越）
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_TEN_MINUTE_GRID_OVER_VOLT_PROT_LEVEL},//十分钟电网电压过压保护
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_ENVIR_OVER_TEMP_LEVEL},//环境温度过高（状态量）（环境温度过温）
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_ENVIR_LOW_TEMP_LEVEL},//环境温度过低（状态量）
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_ABN_RESIDUAL_CURR_LEVEL},//残余电流异常
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NOT_NEED_INTERACT,},//母线正对地绝缘阻抗低告警
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NOT_NEED_INTERACT,},//母线负对地绝缘阻抗低告警
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NOT_NEED_INTERACT,},//母线正对地绝缘阻抗低故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NOT_NEED_INTERACT,},//母线负对地绝缘阻抗低故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_RADIATOR_OVER_TEMP_LEVEL},//散热器过温
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_INTER_FAN_FAIL_LEVEL},//内部风扇故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_EXTER_FAN_FAIL_LEVEL},//外部风扇故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_OVER_TEMP_INSIDS_MACHINE_LEVEL},//内部腔体温度过高（机内温度过温）
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_FIRE_ERROR_LEVEL},//火灾故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_DC_ARC_DAULT_LEVEL},//直流电弧故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_AFCI_SELF_CHK_FAIL_LEVEL},//AFCI自检失败
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_AC_LIGHTING_PROTECTION_ERR_LEVEL},//交流防雷失效
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_DC_LIGHTING_PROTECTION_ERR_LEVEL},//直流防雷失效
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NOT_NEED_INTERACT,},//boostIGBT过温（X=1~12）(预留)
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NOT_NEED_INTERACT,},//逆变单相IGBT过温（X=A、B、C）(预留)
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_GRID_RELAY_ERROR_LEVEL},//并网逆变器继电器故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_AC_CURR_SENSOR_ERROR_LEVEL},//AC电流传感器故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_PV_LOCK_ERROR_LEVEL},//逆变器锁相失败故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_ABNORMAL_OPER_BUILD_IN_PID_LEVEL},//PID修复故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_ABN_AUXI_POWER_SUPPLT_LEVEL},//辅助电源异常
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MAIN_CTRL_EEPROM_FAULT_LEVEL},//主控EEPROM故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MAIN_CTRL_EEPROM_ABNORMAL_LEVEL},//主控EEPROM异常
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_AUXI_CTRL_EEPROM_FAULT_LEVEL},//辅控EEPROM故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_AUXI_CTRL_EEPROM_ABNORMAL_LEVEL},//辅控EEPROM异常
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MONITER_MAIN_CTRL_COMMU_ERROR_LEVEL},//监控与主控通信故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_CARRIER_SYNC_ABNORMAL_LEVEL},//载波同步信号异常（预留）
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_PROTOCOL_VER_MISMATCH_LEVEL},//协议版本不匹配
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_LICENSE_EXPIRED_LEVEL},//许可证过期
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MONITER_ERROR_ALARM_LEVEL},//监控单元故障告警
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MAIN_AUXI_COMMU_ERROR_LEVEL},//主控与辅控通讯故障
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MAIN_CPLD_COMMU_ERROR_LEVEL},//主控与CPLD通讯故障
};

static data_info_id_verison_t parse_mppt_over_volt_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MPPT_OVER_VOLT_LEVEL},//MPPT过压
};
static data_info_id_verison_t parse_bus_over_volt_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_BUS_OVER_VOLT_LEVEL},//母线过压
};
static data_info_id_verison_t parse_pos_bus_over_volt_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_POS_BUS_OVER_VOLT_LEVEL},//正母线过压
};
static data_info_id_verison_t parse_neg_bus_over_volt_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_NEG_BUS_OVER_VOLT_LEVEL},//负母线过压
};
static data_info_id_verison_t parse_bus_volt_imbalance_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_BUS_VOLT_IMBALANCE_LEVEL},//母线电压不平衡
};
static data_info_id_verison_t parse_bus_str_reverse_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_STR_REVERSE_CONNECT_LEVEL},//组串反接
};
static data_info_id_verison_t parse_str_loss_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_STR_LOSS_LEVEL},//组串丢失
};
static data_info_id_verison_t parse_mppt_over_curr_warn_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MPPT_OVER_CURR_WARN_LEVEL},//MPPT过流告警
};
static data_info_id_verison_t parse_mppt_over_curr_err_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MPPT_OVER_CURR_ERROR_LEVEL},//MPPT过流故障
};
static data_info_id_verison_t parse_bus_soft_start_err_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_BUS_SOFT_START_ERR_LEVEL},//母线软启动故障
};
static data_info_id_verison_t parse_dc_disconn_trip_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_DC_DISCONNECTOR_TRIP_LEVEL},//直流隔离开关脱扣
};
static data_info_id_verison_t parse_phase_volt_over_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_PHASE_VOLT_OVER_VOLT_LEVEL},//交流相电压过压
};
static data_info_id_verison_t parse_between_phase_volt_over_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_OVER_VOLT_BETWEEN_PHASE_LEVEL},//交流线电压过压
};
static data_info_id_verison_t parse_phase_volt_under_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_PHASE_VOLT_UNDER_VOLT_LEVEL},//交流相电压欠压
};
static data_info_id_verison_t parse_between_phase_volt_under_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_UNDER_VOLT_BETWEEN_PHASE_LEVEL},//交流线电压欠压
};
static data_info_id_verison_t parse_grid_over_freq_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_GRID_OVER_FREQ_LEVEL},//电网过频
};
static data_info_id_verison_t parse_grid_under_freq_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_GRID_UNDER_FREQ_LEVEL},//电网欠频
};
static data_info_id_verison_t parse_ac_phase_loss_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_AC_PHASE_LOSS_LEVEL},//交流缺相
};
static data_info_id_verison_t parse_output_short_circuit_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_OUTPUT_SHORT_CIRCUIT_LEVEL},//输出短路保护
};
static data_info_id_verison_t parse_out_curr_imbalance_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_OUT_CURR_IMBALANCE_LEVEL},//逆变输出电流不平衡
};
static data_info_id_verison_t parse_abnormal_dc_component_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_ABNORMAL_DC_COMPONENT_LEVEL},//直流分量异常
};
static data_info_id_verison_t parse_island_prot_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_ISLAND_PROT_LEVEL},//防孤岛保护（孤岛保护）
};
static data_info_id_verison_t parse_pv_over_curr_warn_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_PV_OVER_CURR_WARN_LEVEL},//逆变器输出相过流告警
};
static data_info_id_verison_t parse_pv_over_curr_err_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_PV_OVER_CURR_ERROR_LEVEL},//逆变器输出相过流故障
};
static data_info_id_verison_t parse_grid_anti_err_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_GRID_ANTI_ERROR_LEVEL},//电网反序故障
};
static data_info_id_verison_t parse_single_phase_gnd_short_circuit_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_SINGLE_PHASE_GND_SHORT_CIRCUIT_LEVEL},//相对地短路
};
static data_info_id_verison_t parse_low_power_shutdown_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_LOW_POWER_SHUTDOWN_LEVEL},//低功率关机
};
static data_info_id_verison_t parse_low_volt_rt_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_LOW_VOLT_RT_LEVEL},//低穿标志位（低电压穿越）
};
static data_info_id_verison_t parse_high_volt_rt_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_HIGH_VOLT_RT_LEVEL},//高电压穿越标志（高电压穿越）
};
static data_info_id_verison_t parse_ten_min_grid_over_volt_pro_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_TEN_MINUTE_GRID_OVER_VOLT_PROT_LEVEL},//十分钟电网电压过压保护
};

static data_info_id_verison_t parse_envir_temp_over_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_ENVIR_OVER_TEMP_LEVEL},//环境温度过高（状态量）（环境温度过温）
};
static data_info_id_verison_t parse_envir_temp_under_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_ENVIR_LOW_TEMP_LEVEL},//环境温度过低（状态量）
};
static data_info_id_verison_t parse_abn_residual_curr_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_ABN_RESIDUAL_CURR_LEVEL},//残余电流异常
};
static data_info_id_verison_t parse_bus_pos_impedance_warn_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NOT_NEED_INTERACT,},
};
static data_info_id_verison_t parse_neg_pos_impedance_warn_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NOT_NEED_INTERACT,},
};
static data_info_id_verison_t parse_bus_pos_impedance_err_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NOT_NEED_INTERACT,},
};
static data_info_id_verison_t parse_neg_pos_impedance_err_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NOT_NEED_INTERACT,},
};
static data_info_id_verison_t parse_radiator_over_temp_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_RADIATOR_OVER_TEMP_LEVEL},//散热器过温
};
static data_info_id_verison_t parse_inter_fan_fail_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_INTER_FAN_FAIL_LEVEL},//内部风扇故障
};
static data_info_id_verison_t parse_exter_fan_fail_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_EXTER_FAN_FAIL_LEVEL},//外部风扇故障
};
static data_info_id_verison_t parse_insids_machine_over_temp_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_OVER_TEMP_INSIDS_MACHINE_LEVEL},//内部腔体温度过高（机内温度过温）
};
static data_info_id_verison_t parse_fire_error_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_FIRE_ERROR_LEVEL},//火灾故障
};
static data_info_id_verison_t parse_dc_arc_err_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_DC_ARC_DAULT_LEVEL},//直流电弧故障
};
static data_info_id_verison_t parse_afci_self_chk_fail_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_AFCI_SELF_CHK_FAIL_LEVEL},//AFCI自检失败
};
static data_info_id_verison_t parse_ac_lighting_prot_err_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_AC_LIGHTING_PROTECTION_ERR_LEVEL},//交流防雷失效
};
static data_info_id_verison_t parse_dc_lighting_prot_err_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_DC_LIGHTING_PROTECTION_ERR_LEVEL},//直流防雷失效
};
static data_info_id_verison_t parse_boost_igbt_over_temp_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NOT_NEED_INTERACT,},//boostIGBT过温（X=1~12）(预留)
};
static data_info_id_verison_t parse_pv_igbt_over_temp_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NOT_NEED_INTERACT,},//逆变单相IGBT过温（X=A、B、C）(预留)
};
static data_info_id_verison_t parse_grid_relay_err_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_GRID_RELAY_ERROR_LEVEL},//并网逆变器继电器故障
};
static data_info_id_verison_t parse_ac_curr_sensor_err_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_AC_CURR_SENSOR_ERROR_LEVEL},//AC电流传感器故障
};
static data_info_id_verison_t parse_pv_lock_err_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_PV_LOCK_ERROR_LEVEL},//逆变器锁相失败故障
};
static data_info_id_verison_t parse_pld_err_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_ABNORMAL_OPER_BUILD_IN_PID_LEVEL},//PID修复故障
};
static data_info_id_verison_t parse_auxi_power_err_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_ABN_AUXI_POWER_SUPPLT_LEVEL},//辅助电源异常
};
static data_info_id_verison_t parse_main_ctrl_eeprom_fault_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MAIN_CTRL_EEPROM_FAULT_LEVEL},//主控EEPROM故障
};
static data_info_id_verison_t parse_main_ctrl_eeprom_abnormal_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MAIN_CTRL_EEPROM_ABNORMAL_LEVEL},//主控EEPROM异常
};
static data_info_id_verison_t parse_auxi_ctrl_eeprom_fault_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_AUXI_CTRL_EEPROM_FAULT_LEVEL},//辅控EEPROM故障
};
static data_info_id_verison_t parse_auxi_ctrl_eeprom_abnormal_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_AUXI_CTRL_EEPROM_ABNORMAL_LEVEL},//辅控EEPROM异常
};
static data_info_id_verison_t parse_moniter_main_ctrl_commu_err_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MONITER_MAIN_CTRL_COMMU_ERROR_LEVEL},//监控与主控通信故障
};
static data_info_id_verison_t parse_carrier_sync_abnormal_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_CARRIER_SYNC_ABNORMAL_LEVEL},//载波同步信号异常（预留）
};
static data_info_id_verison_t parse_protocol_ver_mismatch_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_PROTOCOL_VER_MISMATCH_LEVEL},//协议版本不匹配
};
static data_info_id_verison_t parse_license_expied_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_LICENSE_EXPIRED_LEVEL},//许可证过期
};
static data_info_id_verison_t parse_moniter_err_alm_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MONITER_ERROR_ALARM_LEVEL},//监控单元故障告警
};
static data_info_id_verison_t parse_main_auxi_commu_err_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MAIN_AUXI_COMMU_ERROR_LEVEL},//主控与辅控通讯故障
};
static data_info_id_verison_t parse_main_cpld_commu_err_level_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MAIN_CPLD_COMMU_ERROR_LEVEL},//主控与CPLD通讯故障
};


//监控参数
static data_info_id_verison_t cmd_pack_moniter_para[] RAM_SECTION =
{
    {SEG_DATA, type_date,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_MONITER_DELIERY_TIME},
    {SEG_DATA, type_date,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_FIRST_STARTUP_TIME},
    {SEG_DATA, type_unsigned_int,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_RUN_TIME},
    {SEG_DATA, type_string,  ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_WIRELESS_IP_ADDR},
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_NET_CONNECT_MODE},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,  DAC_DATA_ID_MASTER_SLAVE_STATUS},

    {SEG_DATA, type_string,  ARRAY_SIZE_16, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_4G_COMMUNICATION_ROD_MODEL},
    {SEG_DATA, type_string,  ARRAY_SIZE_16, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_4G_COMMUNICATION_ROD_SERIAL_NUMBER},
    {SEG_DATA, type_string,  ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_4G_COMMUNICATION_ROD_SOFT_VER},
    {SEG_DATA, type_string,  ARRAY_SIZE_24, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_string,  ARRAY_SIZE_16, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_4G_OPERATOR_NAME},
    {SEG_DATA, type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_4G_SIGNAL_STRENGTH},

    {SEG_SYSTEM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_SYNC_PHONE_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_UTC_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_AUTH_USE_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_COMMON_ID_HIS_DATA_SAVE_INTERVER_OFFSET},

    {SEG_SYSTEM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_COM_NAME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_COMMON_ID_RS485_BAUD_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_RS485_DATA_BIT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_RS485_STOP_BIT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_COMMON_ID_RS485_CHK_CODE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_COMMON_ID_RS485_COMMU_ADDR_OFFSET},

    {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_COMMON_ID_WIFI_NAME_OFFSET},
    {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_COMMON_ID_WIFI_PASSWORD_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_WIFI_WORK_MODE_OFFSET},

    {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_DONGEL_WIFI_SSID_OFFSET},
    {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_DONGEL_WIFI_PASSWORD_OFFSET},

    {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_16, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_SITE_ID_OFFSET},
    {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_MQTT_NET_IP_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_MQTT_NET_PORT_OFFSET},
    {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_16, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_SITE_ID_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_MQTT_NET_IP_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_MQTT_NET_PORT_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_int,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LONGITUDE_OFFSET},
    {SEG_SYSTEM_PARA, type_int,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LATITUDE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT, },


};

static data_info_id_verison_t pack_basic_info[] RAM_SECTION ={
    {SEG_DATA, type_date,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_MONITER_DELIERY_TIME},
    {SEG_DATA, type_date,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_FIRST_STARTUP_TIME},
    {SEG_DATA, type_unsigned_int,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_RUN_TIME},
    {SEG_DATA, type_string,  ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_WIRELESS_IP_ADDR},
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_NET_CONNECT_MODE},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NEED_INTERACT,  DAC_DATA_ID_MASTER_SLAVE_STATUS},
};
static data_info_id_verison_t pack_4g_info[] RAM_SECTION ={
    {SEG_DATA, type_string,  ARRAY_SIZE_16, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_4G_COMMUNICATION_ROD_MODEL},
    {SEG_DATA, type_string,  ARRAY_SIZE_16, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_4G_COMMUNICATION_ROD_SERIAL_NUMBER},
    {SEG_DATA, type_string,  ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_4G_COMMUNICATION_ROD_SOFT_VER},
    {SEG_DATA, type_string,  ARRAY_SIZE_24, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_string,  ARRAY_SIZE_16, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_4G_OPERATOR_NAME},
    {SEG_DATA, type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_DATA_ID_4G_SIGNAL_STRENGTH},
};
static data_info_id_verison_t pack_moniter_time_para[] RAM_SECTION ={
    {SEG_SYSTEM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_SYNC_PHONE_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_UTC_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_AUTH_USE_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_COMMON_ID_HIS_DATA_SAVE_INTERVER_OFFSET},

};

static data_info_id_verison_t pack_moniter_rs485_para[] RAM_SECTION ={
    {SEG_SYSTEM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_COM_NAME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_COMMON_ID_RS485_BAUD_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_RS485_DATA_BIT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_RS485_STOP_BIT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_COMMON_ID_RS485_CHK_CODE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_COMMON_ID_RS485_COMMU_ADDR_OFFSET},
};

static data_info_id_verison_t pack_moniter_wifi_para[] RAM_SECTION ={
    {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_COMMON_ID_WIFI_NAME_OFFSET},
    {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_COMMON_ID_WIFI_PASSWORD_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_WIFI_WORK_MODE_OFFSET},
};

static data_info_id_verison_t pack_moniter_dongel_wifi_para[] RAM_SECTION ={
   {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_DONGEL_WIFI_SSID_OFFSET},
   {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_DONGEL_WIFI_PASSWORD_OFFSET},
};

static data_info_id_verison_t pack_moniter_mqtt_para[] RAM_SECTION ={
    {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_16, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_SITE_ID_OFFSET},
    {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_MQTT_NET_IP_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_MQTT_NET_PORT_OFFSET},
};

static data_info_id_verison_t pack_moniter_three_mqtt_para[] RAM_SECTION ={
    {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_16, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_SITE_ID_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_MQTT_NET_IP_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_MQTT_NET_PORT_OFFSET + 1},
};

static data_info_id_verison_t pack_position_para[] RAM_SECTION ={
    {SEG_SYSTEM_PARA, type_int,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LONGITUDE_OFFSET},
    {SEG_SYSTEM_PARA, type_int,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LATITUDE_OFFSET},
};

static data_info_id_verison_t pack_delay_update_para[] RAM_SECTION ={
    {SEG_SYSTEM_PARA, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
};

static data_info_id_verison_t parse_delay_update_para[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
};


static data_info_id_verison_t parse_moniter_time_para[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_SYNC_PHONE_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_UTC_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_AUTH_USE_TIME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_COMMON_ID_HIS_DATA_SAVE_INTERVER_OFFSET},

};

static data_info_id_verison_t parse_moniter_rs485_para[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_COM_NAME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_COMMON_ID_RS485_BAUD_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_RS485_DATA_BIT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_RS485_STOP_BIT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_COMMON_ID_RS485_CHK_CODE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_COMMON_ID_RS485_COMMU_ADDR_OFFSET},
};

static data_info_id_verison_t parse_moniter_wifi_para[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_COMMON_ID_WIFI_NAME_OFFSET},
    {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_COMMON_ID_WIFI_PASSWORD_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_WIFI_WORK_MODE_OFFSET},
};

static data_info_id_verison_t parse_moniter_dongel_wifi_para[] RAM_SECTION ={
   {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
   {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_DONGEL_WIFI_SSID_OFFSET},
   {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_DONGEL_WIFI_PASSWORD_OFFSET},
};

static data_info_id_verison_t parse_moniter_mqtt_para[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_16, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_SITE_ID_OFFSET},
    {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_MQTT_NET_IP_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_MQTT_NET_PORT_OFFSET},
};

static data_info_id_verison_t parse_moniter_three_mqtt_para[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_16, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_SITE_ID_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_MQTT_NET_IP_OFFSET + 1},
    {SEG_SYSTEM_PARA, type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,DAC_PARA_ID_MQTT_NET_PORT_OFFSET + 1},
};

static data_info_id_verison_t parse_position_para[] RAM_SECTION ={
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_int,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LONGITUDE_OFFSET},
    {SEG_SYSTEM_PARA, type_int,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PARA_ID_LATITUDE_OFFSET},
};

static data_info_id_verison_t parse_mppt_over_volt_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MPPT_OVER_VOLT_RELAY},//MPPT过压
};
static data_info_id_verison_t parse_bus_over_volt_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_BUS_OVER_VOLT_RELAY},//母线过压
};
static data_info_id_verison_t parse_pos_bus_over_volt_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_POS_BUS_OVER_VOLT_RELAY},//正母线过压
};
static data_info_id_verison_t parse_neg_bus_over_volt_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_NEG_BUS_OVER_VOLT_RELAY},//负母线过压
};
static data_info_id_verison_t parse_bus_volt_imbalance_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_BUS_VOLT_IMBALANCE_RELAY},//母线电压不平衡
};
static data_info_id_verison_t parse_bus_str_reverse_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_STR_REVERSE_CONNECT_RELAY},//组串反接
};
static data_info_id_verison_t parse_str_loss_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_STR_LOSS_RELAY},//组串丢失
};
static data_info_id_verison_t parse_mppt_over_curr_warn_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MPPT_OVER_CURR_WARN_RELAY},//MPPT过流告警
};
static data_info_id_verison_t parse_mppt_over_curr_err_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MPPT_OVER_CURR_ERROR_RELAY},//MPPT过流故障
};
static data_info_id_verison_t parse_bus_soft_start_err_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_BUS_SOFT_START_ERR_RELAY},//母线软启动故障
};
static data_info_id_verison_t parse_dc_disconn_trip_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_DC_DISCONNECTOR_TRIP_RELAY},//直流隔离开关脱扣
};
static data_info_id_verison_t parse_phase_volt_over_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_PHASE_VOLT_OVER_VOLT_RELAY},//交流相电压过压
};
static data_info_id_verison_t parse_between_phase_volt_over_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_OVER_VOLT_BETWEEN_PHASE_RELAY},//交流线电压过压
};
static data_info_id_verison_t parse_phase_volt_under_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_PHASE_VOLT_UNDER_VOLT_RELAY},//交流相电压欠压
};
static data_info_id_verison_t parse_between_phase_volt_under_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_UNDER_VOLT_BETWEEN_PHASE_RELAY},//交流线电压欠压
};
static data_info_id_verison_t parse_grid_over_freq_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_GRID_OVER_FREQ_RELAY},//电网过频
};
static data_info_id_verison_t parse_grid_under_freq_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_GRID_UNDER_FREQ_RELAY},//电网欠频
};
static data_info_id_verison_t parse_ac_phase_loss_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_AC_PHASE_LOSS_RELAY},//交流缺相
};
static data_info_id_verison_t parse_output_short_circuit_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_OUTPUT_SHORT_CIRCUIT_RELAY},//输出短路保护
};
static data_info_id_verison_t parse_out_curr_imbalance_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_OUT_CURR_IMBALANCE_RELAY},//逆变输出电流不平衡
};
static data_info_id_verison_t parse_abnormal_dc_component_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_ABNORMAL_DC_COMPONENT_RELAY},//直流分量异常
};
static data_info_id_verison_t parse_island_prot_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_ISLAND_PROT_RELAY},//防孤岛保护（孤岛保护）
};
static data_info_id_verison_t parse_pv_over_curr_warn_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_PV_OVER_CURR_WARN_RELAY},//逆变器输出相过流告警
};
static data_info_id_verison_t parse_pv_over_curr_err_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_PV_OVER_CURR_ERROR_RELAY},//逆变器输出相过流故障
};
static data_info_id_verison_t parse_grid_anti_err_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_GRID_ANTI_ERROR_RELAY},//电网反序故障
};
static data_info_id_verison_t parse_single_phase_gnd_short_circuit_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_SINGLE_PHASE_GND_SHORT_CIRCUIT_RELAY},//相对地短路
};
static data_info_id_verison_t parse_low_power_shutdown_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_LOW_POWER_SHUTDOWN_RELAY},//低功率关机
};
static data_info_id_verison_t parse_low_volt_rt_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_LOW_VOLT_RT_RELAY},//低穿标志位（低电压穿越）
};
static data_info_id_verison_t parse_high_volt_rt_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_HIGH_VOLT_RT_RELAY},//高电压穿越标志（高电压穿越）
};
static data_info_id_verison_t parse_ten_min_grid_over_volt_pro_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_TEN_MINUTE_GRID_OVER_VOLT_PROT_RELAY},//十分钟电网电压过压保护
};
static data_info_id_verison_t parse_envir_temp_over_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_ENVIR_OVER_TEMP_RELAY},//环境温度过高（状态量）（环境温度过温）
};
static data_info_id_verison_t parse_envir_temp_under_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_ENVIR_LOW_TEMP_RELAY},//环境温度过低（状态量）
};
static data_info_id_verison_t parse_abn_residual_curr_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_ABN_RESIDUAL_CURR_RELAY},//残余电流异常
};
static data_info_id_verison_t parse_bus_pos_impedance_warn_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NOT_NEED_INTERACT,},//母线正对地绝缘阻抗低告警
};
static data_info_id_verison_t parse_neg_pos_impedance_warn_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NOT_NEED_INTERACT,},//母线负对地绝缘阻抗低告警
};
static data_info_id_verison_t parse_bus_pos_impedance_err_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NOT_NEED_INTERACT,},//母线正对地绝缘阻抗低故障
};
static data_info_id_verison_t parse_neg_pos_impedance_err_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NOT_NEED_INTERACT,},//母线负对地绝缘阻抗低故障
};
static data_info_id_verison_t parse_radiator_over_temp_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_RADIATOR_OVER_TEMP_RELAY},//散热器过温
};
static data_info_id_verison_t parse_inter_fan_fail_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_INTER_FAN_FAIL_RELAY},//内部风扇故障
};
static data_info_id_verison_t parse_exter_fan_fail_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_EXTER_FAN_FAIL_RELAY},//外部风扇故障
};
static data_info_id_verison_t parse_insids_machine_over_temp_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_OVER_TEMP_INSIDS_MACHINE_RELAY},//内部腔体温度过高（机内温度过温）
};
static data_info_id_verison_t parse_fire_error_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_FIRE_ERROR_RELAY},//火灾故障
};
static data_info_id_verison_t parse_dc_arc_err_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_DC_ARC_DAULT_RELAY},//直流电弧故障
};
static data_info_id_verison_t parse_afci_self_chk_fail_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_AFCI_SELF_CHK_FAIL_RELAY},//AFCI自检失败
};
static data_info_id_verison_t parse_ac_lighting_prot_err_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_AC_LIGHTING_PROTECTION_ERR_RELAY},//交流防雷失效
};
static data_info_id_verison_t parse_dc_lighting_prot_err_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_DC_LIGHTING_PROTECTION_ERR_RELAY},//直流防雷失效
};
static data_info_id_verison_t parse_boost_igbt_over_temp_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NOT_NEED_INTERACT,},//boostIGBT过温（X=1~12）(预留)
};
static data_info_id_verison_t parse_pv_igbt_over_temp_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NOT_NEED_INTERACT,},//逆变单相IGBT过温（X=A、B、C）(预留)
};
static data_info_id_verison_t parse_grid_relay_err_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_GRID_RELAY_ERROR_RELAY},//并网逆变器继电器故障
};
static data_info_id_verison_t parse_ac_curr_sensor_err_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_AC_CURR_SENSOR_ERROR_RELAY},//AC电流传感器故障
};
static data_info_id_verison_t parse_pv_lock_err_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_PV_LOCK_ERROR_RELAY},//逆变器锁相失败故障
};
static data_info_id_verison_t parse_pld_err_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_ABNORMAL_OPER_BUILD_IN_PID_RELAY},//PID修复故障
};
static data_info_id_verison_t parse_auxi_power_err_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_ABN_AUXI_POWER_SUPPLT_RELAY},//辅助电源异常
};
static data_info_id_verison_t parse_main_ctrl_eeprom_fault_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MAIN_CTRL_EEPROM_FAULT_RELAY},//主控EEPROM故障
};
static data_info_id_verison_t parse_main_ctrl_eeprom_abnormal_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MAIN_CTRL_EEPROM_ABNORMAL_RELAY},//主控EEPROM异常
};
static data_info_id_verison_t parse_auxi_ctrl_eeprom_fault_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_AUXI_CTRL_EEPROM_FAULT_RELAY},//辅控EEPROM故障
};
static data_info_id_verison_t parse_auxi_ctrl_eeprom_abnormal_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_AUXI_CTRL_EEPROM_ABNORMAL_RELAY},//辅控EEPROM异常
};
static data_info_id_verison_t parse_moniter_main_ctrl_commu_err_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MONITER_MAIN_CTRL_COMMU_ERROR_RELAY},//监控与主控通信故障
};
static data_info_id_verison_t parse_carrier_sync_abnormal_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_CARRIER_SYNC_ABNORMAL_RELAY},//载波同步信号异常（预留）
};
static data_info_id_verison_t parse_protocol_ver_mismatch_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_PROTOCOL_VER_MISMATCH_RELAY},//协议版本不匹配
};
static data_info_id_verison_t parse_license_expied_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_LICENSE_EXPIRED_RELAY},//许可证过期
};
static data_info_id_verison_t parse_moniter_err_alm_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MONITER_ERROR_ALARM_RELAY},//监控单元故障告警
};
static data_info_id_verison_t parse_main_auxi_commu_err_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MAIN_AUXI_COMMU_ERROR_RELAY},//主控与辅控通讯故障
};
static data_info_id_verison_t parse_main_cpld_commu_err_relay_info[] RAM_SECTION =
{
    {SEG_DATA, type_char,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE,NOT_NEED_INTERACT},
    {SEG_ALARM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ANA_DATA_FLAG_DEF_VALUE, NEED_INTERACT,DAC_ALARM_ID_MAIN_CPLD_COMMU_ERROR_RELAY},//主控与CPLD通讯故障
};


/*获取研发专用模拟量*/
static data_info_id_verison_t cmd_pack_ana_power_special_data_info[] RAM_SECTION = 
{
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,   DAC_DATA_ID_ARC_DETECTION_VOLTAGE},  
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,   DAC_DATA_ID_ARC_DETECTION_VOLTAGE+1},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,   DAC_DATA_ID_ARC_DETECTION_VOLTAGE+2},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,   DAC_DATA_ID_ARC_DETECTION_VOLTAGE+3},

    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,   DAC_DATA_ID_INTERNAL_FAN_SPEED},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,   DAC_DATA_ID_EXTERNAL_FAN_SPEED},

    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,   DAC_DATA_ID_FT_N_PE},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,   DAC_DATA_ID_FT_TEST_PRESTAGE_FLAG},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,   DAC_DATA_ID_FT_TEST_POSTSTAGE_FLAG},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,   DAC_DATA_ID_AFCI_SELF_CHECK_STAGE},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,   DAC_DATA_ID_FT_TEST_RESERVED_ANA+4},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,   DAC_DATA_ID_FT_TEST_RESERVED_ANA+5},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,   DAC_DATA_ID_FT_TEST_RESERVED_ANA+6},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,   DAC_DATA_ID_FT_TEST_RESERVED_ANA+7},

    {SEG_DATA, type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,   DAC_DATA_ID_CURRENT_WORK_MODE},
    {SEG_DATA, type_unsigned_int,    ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT,   DAC_DATA_ID_FLASH_ERASE_NUM},

};
/*------------------------------------------------------------------------------------------------------------*/
static cmd_parse_info_id_verison_t cmd_parse_info[] RAM_SECTION =
{
//设置
    //保护参数
    {&parse_prot_para1_data_info[0], sizeof(parse_prot_para1_data_info)/sizeof(data_info_id_verison_t)},//0
    {&parse_prot_para2_data_info[0], sizeof(parse_prot_para2_data_info)/sizeof(data_info_id_verison_t)},
    {&parse_prot_para3_data_info[0], sizeof(parse_prot_para3_data_info)/sizeof(data_info_id_verison_t)},
    {&parse_prot_para4_data_info[0], sizeof(parse_prot_para4_data_info)/sizeof(data_info_id_verison_t)},
    {&parse_prot_para5_data_info[0], sizeof(parse_prot_para5_data_info)/sizeof(data_info_id_verison_t)},
    {&parse_prot_para6_data_info[0], sizeof(parse_prot_para6_data_info)/sizeof(data_info_id_verison_t)},

    //曲线参数
    {&parse_para_lvrt_cc_data_info[0], sizeof(parse_para_lvrt_cc_data_info)/sizeof(data_info_id_verison_t)},//6
    {&parse_para_P_Pn_CC_data_info[0], sizeof(parse_para_P_Pn_CC_data_info)/sizeof(data_info_id_verison_t)},
    {&parse_para_Q_U_CC_data_info[0], sizeof(parse_para_Q_U_CC_data_info)/sizeof(data_info_id_verison_t)},
    {&parse_para_PF_U_CC_data_info[0], sizeof(parse_para_PF_U_CC_data_info)/sizeof(data_info_id_verison_t)},

    //系统参数
    {&parse_para_power_grid_data_info[0], sizeof(parse_para_power_grid_data_info)/sizeof(data_info_id_verison_t)},//10
    {&parse_para_chara_data_info[0], sizeof(parse_para_chara_data_info)/sizeof(data_info_id_verison_t)},//11
    {&parse_para_mppt_data_info[0], sizeof(parse_para_mppt_data_info)/sizeof(data_info_id_verison_t)},//12
    {&parse_para_lvrt_data_info[0], sizeof(parse_para_lvrt_data_info)/sizeof(data_info_id_verison_t)},
    {&parse_para_hvrt_data_info[0], sizeof(parse_para_hvrt_data_info)/sizeof(data_info_id_verison_t)},
    {&parse_para_pid_data_info[0], sizeof(parse_para_pid_data_info)/sizeof(data_info_id_verison_t)},
    {&parse_para_afci_data_info[0], sizeof(parse_para_afci_data_info)/sizeof(data_info_id_verison_t)},
    {&parse_para_conn_ctrl_data_info[0], sizeof(parse_para_conn_ctrl_data_info)/sizeof(data_info_id_verison_t)},//17
    {&parse_para_prot_data_info[0], sizeof(parse_para_prot_data_info)/sizeof(data_info_id_verison_t)},//18
    {&parse_para_altitude[0], sizeof(parse_para_altitude)/sizeof(data_info_id_verison_t)},//19


    //功率参数
    {&parse_para_power_regu_data_info[0], sizeof(parse_para_power_regu_data_info)/sizeof(data_info_id_verison_t)},
    {&parse_para_comm_fail_data_info[0], sizeof(parse_para_comm_fail_data_info)/sizeof(data_info_id_verison_t)},
    {&parse_para_active_power_reg_data_info[0], sizeof(parse_para_active_power_reg_data_info)/sizeof(data_info_id_verison_t)},
    {&parse_para_reactive_power_regu_data_info[0], sizeof(parse_para_reactive_power_regu_data_info)/sizeof(data_info_id_verison_t)},
    {&parse_para_parall_point_ctrl_active[0], sizeof(parse_para_parall_point_ctrl_active)/sizeof(data_info_id_verison_t)},//24
    {&parse_para_parall_point_ctrl_reactive[0], sizeof(parse_para_parall_point_ctrl_reactive)/sizeof(data_info_id_verison_t)},

    //比率矫正参数
    {&parse_para_adj_out_volt_data_info[0], sizeof(parse_para_adj_out_volt_data_info)/sizeof(data_info_id_verison_t)},
    {&parse_para_adj_bus_volt_data_info[0], sizeof(parse_para_adj_bus_volt_data_info)/sizeof(data_info_id_verison_t)},
    {&parse_para_adj_mppt1_data_info[0], sizeof(parse_para_adj_mppt1_data_info)/sizeof(data_info_id_verison_t)},
    {&parse_para_adj_mppt2_data_info[0], sizeof(parse_para_adj_mppt2_data_info)/sizeof(data_info_id_verison_t)},
    {&parse_para_adj_mppt3_data_info[0], sizeof(parse_para_adj_mppt3_data_info)/sizeof(data_info_id_verison_t)},
    {&parse_para_adj_mppt4_data_info[0], sizeof(parse_para_adj_mppt4_data_info)/sizeof(data_info_id_verison_t)},
    {&parse_para_adj_adj_aux_data_info[0], sizeof(parse_para_adj_adj_aux_data_info)/sizeof(data_info_id_verison_t)},//32
    //生产参数
    {&parse_para_device_sn_data_info[0], sizeof(parse_para_device_sn_data_info)/sizeof(data_info_id_verison_t)},
    {&parse_para_produce_data_info[0], sizeof(parse_para_produce_data_info)/sizeof(data_info_id_verison_t)},
    {&parse_para_prop_data_info[0], sizeof(parse_para_prop_data_info)/sizeof(data_info_id_verison_t)},
    {&parse_para_ctrl_cmd_data_info[0], sizeof(parse_para_ctrl_cmd_data_info)/sizeof(data_info_id_verison_t)},//36

//获取
    //保护参数
    {&pack_prot_para_dato_info[0], sizeof(pack_prot_para_dato_info)/sizeof(data_info_id_verison_t)},
    //特征曲线
     {&pack_curve_para_dato_info[0], sizeof(pack_curve_para_dato_info)/sizeof(data_info_id_verison_t)},
    //系统
     {&pack_sys_para_dato_info[0], sizeof(pack_sys_para_dato_info)/sizeof(data_info_id_verison_t)},
    //功率
    {&pack_power_para_dato_info[0], sizeof(pack_power_para_dato_info)/sizeof(data_info_id_verison_t) },
    //比率校准
    {&pack_adjust_para_dato_info[0], sizeof(pack_adjust_para_dato_info)/sizeof(data_info_id_verison_t)},
    //生产
    {&pack_produce_para_dato_info[0], sizeof(pack_produce_para_dato_info)/sizeof(data_info_id_verison_t)},

    //获取功率厂家信息
    {&pack_power_fac_data_info[0], sizeof(pack_power_fac_data_info)/sizeof(data_info_id_verison_t)}, 
    //获取监控厂家信息
    {&pack_csu_fac_data_info[0], sizeof(pack_csu_fac_data_info)/sizeof(data_info_id_verison_t)},
    //获取告警等级信息
    {&pack_alm_level_info[0], sizeof(pack_alm_level_info)/sizeof(data_info_id_verison_t)}, //45
    //获取告警干接点信息
    {&pack_alm_relay_info[0], sizeof(pack_alm_relay_info)/sizeof(data_info_id_verison_t)}, //46

    //设置告警等级
    {&parse_mppt_over_volt_level_info[0], sizeof(parse_mppt_over_volt_level_info)/sizeof(data_info_id_verison_t)}, //47
    {&parse_bus_over_volt_level_info[0], sizeof(parse_bus_over_volt_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_pos_bus_over_volt_level_info[0], sizeof(parse_pos_bus_over_volt_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_neg_bus_over_volt_level_info[0], sizeof(parse_neg_bus_over_volt_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_bus_volt_imbalance_level_info[0], sizeof(parse_bus_volt_imbalance_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_bus_str_reverse_level_info[0], sizeof(parse_bus_str_reverse_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_str_loss_level_info[0], sizeof(parse_str_loss_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_mppt_over_curr_warn_level_info[0], sizeof(parse_mppt_over_curr_warn_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_mppt_over_curr_err_level_info[0], sizeof(parse_mppt_over_curr_err_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_bus_soft_start_err_level_info[0], sizeof(parse_bus_soft_start_err_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_dc_disconn_trip_level_info[0], sizeof(parse_dc_disconn_trip_level_info)/sizeof(data_info_id_verison_t)},
    {&parse_phase_volt_over_level_info[0], sizeof(parse_phase_volt_over_level_info)/sizeof(data_info_id_verison_t)},
    {&parse_between_phase_volt_over_level_info[0], sizeof(parse_between_phase_volt_over_level_info)/sizeof(data_info_id_verison_t)},
    {&parse_phase_volt_under_level_info[0], sizeof(parse_phase_volt_under_level_info)/sizeof(data_info_id_verison_t)},
    {&parse_between_phase_volt_under_level_info[0], sizeof(parse_between_phase_volt_under_level_info)/sizeof(data_info_id_verison_t)},//61
    {&parse_grid_over_freq_level_info[0], sizeof(parse_grid_over_freq_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_grid_under_freq_level_info[0], sizeof(parse_grid_under_freq_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_ac_phase_loss_level_info[0], sizeof(parse_ac_phase_loss_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_output_short_circuit_level_info[0], sizeof(parse_output_short_circuit_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_out_curr_imbalance_level_info[0], sizeof(parse_out_curr_imbalance_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_abnormal_dc_component_level_info[0], sizeof(parse_abnormal_dc_component_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_island_prot_level_info[0], sizeof(parse_island_prot_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_pv_over_curr_warn_level_info[0], sizeof(parse_pv_over_curr_warn_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_pv_over_curr_err_level_info[0], sizeof(parse_pv_over_curr_err_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_grid_anti_err_level_info[0], sizeof(parse_grid_anti_err_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_single_phase_gnd_short_circuit_level_info[0], sizeof(parse_single_phase_gnd_short_circuit_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_low_power_shutdown_level_info[0], sizeof(parse_low_power_shutdown_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_low_volt_rt_level_info[0], sizeof(parse_low_volt_rt_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_high_volt_rt_level_info[0], sizeof(parse_high_volt_rt_level_info)/sizeof(data_info_id_verison_t)}, //75
    {&parse_ten_min_grid_over_volt_pro_level_info[0], sizeof(parse_ten_min_grid_over_volt_pro_level_info)/sizeof(data_info_id_verison_t)},  
    {&parse_envir_temp_over_level_info[0], sizeof(parse_envir_temp_over_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_envir_temp_under_level_info[0], sizeof(parse_envir_temp_under_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_abn_residual_curr_level_info[0], sizeof(parse_abn_residual_curr_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_bus_pos_impedance_warn_level_info[0], sizeof(parse_bus_pos_impedance_warn_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_neg_pos_impedance_warn_level_info[0], sizeof(parse_neg_pos_impedance_warn_level_info)/sizeof(data_info_id_verison_t)}, //81
    {&parse_bus_pos_impedance_err_level_info[0], sizeof(parse_bus_pos_impedance_err_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_neg_pos_impedance_err_level_info[0], sizeof(parse_neg_pos_impedance_err_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_radiator_over_temp_level_info[0], sizeof(parse_radiator_over_temp_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_inter_fan_fail_level_info[0], sizeof(parse_inter_fan_fail_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_exter_fan_fail_level_info[0], sizeof(parse_exter_fan_fail_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_insids_machine_over_temp_level_info[0], sizeof(parse_insids_machine_over_temp_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_fire_error_level_info[0], sizeof(parse_fire_error_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_dc_arc_err_level_info[0], sizeof(parse_dc_arc_err_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_afci_self_chk_fail_level_info[0], sizeof(parse_afci_self_chk_fail_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_ac_lighting_prot_err_level_info[0], sizeof(parse_ac_lighting_prot_err_level_info)/sizeof(data_info_id_verison_t)}, //91
    {&parse_dc_lighting_prot_err_level_info[0], sizeof(parse_dc_lighting_prot_err_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_boost_igbt_over_temp_level_info[0], sizeof(parse_boost_igbt_over_temp_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_pv_igbt_over_temp_level_info[0], sizeof(parse_pv_igbt_over_temp_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_grid_relay_err_level_info[0], sizeof(parse_grid_relay_err_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_ac_curr_sensor_err_level_info[0], sizeof(parse_ac_curr_sensor_err_level_info)/sizeof(data_info_id_verison_t)}, //96
    {&parse_pv_lock_err_level_info[0], sizeof(parse_pv_lock_err_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_pld_err_level_info[0], sizeof(parse_pld_err_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_auxi_power_err_level_info[0], sizeof(parse_auxi_power_err_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_main_ctrl_eeprom_fault_level_info[0], sizeof(parse_main_ctrl_eeprom_fault_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_main_ctrl_eeprom_abnormal_level_info[0], sizeof(parse_main_ctrl_eeprom_abnormal_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_auxi_ctrl_eeprom_fault_level_info[0], sizeof(parse_auxi_ctrl_eeprom_fault_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_auxi_ctrl_eeprom_abnormal_level_info[0], sizeof(parse_auxi_ctrl_eeprom_abnormal_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_moniter_main_ctrl_commu_err_level_info[0], sizeof(parse_moniter_main_ctrl_commu_err_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_carrier_sync_abnormal_level_info[0], sizeof(parse_carrier_sync_abnormal_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_protocol_ver_mismatch_level_info[0], sizeof(parse_protocol_ver_mismatch_level_info)/sizeof(data_info_id_verison_t)}, //106
    {&parse_license_expied_level_info[0], sizeof(parse_license_expied_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_moniter_err_alm_level_info[0], sizeof(parse_moniter_err_alm_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_main_auxi_commu_err_level_info[0], sizeof(parse_main_auxi_commu_err_level_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_main_cpld_commu_err_level_info[0], sizeof(parse_main_cpld_commu_err_level_info)/sizeof(data_info_id_verison_t)}, //110
    
//设置告警干接点
    {&parse_mppt_over_volt_relay_info[0], sizeof(parse_mppt_over_volt_relay_info)/sizeof(data_info_id_verison_t)}, //111
    {&parse_bus_over_volt_relay_info[0], sizeof(parse_bus_over_volt_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_pos_bus_over_volt_relay_info[0], sizeof(parse_pos_bus_over_volt_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_neg_bus_over_volt_relay_info[0], sizeof(parse_neg_bus_over_volt_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_bus_volt_imbalance_relay_info[0], sizeof(parse_bus_volt_imbalance_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_bus_str_reverse_relay_info[0], sizeof(parse_bus_str_reverse_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_str_loss_relay_info[0], sizeof(parse_str_loss_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_mppt_over_curr_warn_relay_info[0], sizeof(parse_mppt_over_curr_warn_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_mppt_over_curr_err_relay_info[0], sizeof(parse_mppt_over_curr_err_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_bus_soft_start_err_relay_info[0], sizeof(parse_bus_soft_start_err_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_dc_disconn_trip_relay_info[0], sizeof(parse_dc_disconn_trip_relay_info)/sizeof(data_info_id_verison_t)}, //121
    {&parse_phase_volt_over_relay_info[0], sizeof(parse_phase_volt_over_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_between_phase_volt_over_relay_info[0], sizeof(parse_between_phase_volt_over_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_phase_volt_under_relay_info[0], sizeof(parse_phase_volt_under_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_between_phase_volt_under_relay_info[0], sizeof(parse_between_phase_volt_under_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_grid_over_freq_relay_info[0], sizeof(parse_grid_over_freq_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_grid_under_freq_relay_info[0], sizeof(parse_grid_under_freq_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_ac_phase_loss_relay_info[0], sizeof(parse_ac_phase_loss_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_output_short_circuit_relay_info[0], sizeof(parse_output_short_circuit_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_out_curr_imbalance_relay_info[0], sizeof(parse_out_curr_imbalance_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_abnormal_dc_component_relay_info[0], sizeof(parse_abnormal_dc_component_relay_info)/sizeof(data_info_id_verison_t)}, //131
    {&parse_island_prot_relay_info[0], sizeof(parse_island_prot_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_pv_over_curr_warn_relay_info[0], sizeof(parse_pv_over_curr_warn_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_pv_over_curr_err_relay_info[0], sizeof(parse_pv_over_curr_err_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_grid_anti_err_relay_info[0], sizeof(parse_grid_anti_err_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_single_phase_gnd_short_circuit_relay_info[0], sizeof(parse_single_phase_gnd_short_circuit_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_low_power_shutdown_relay_info[0], sizeof(parse_low_power_shutdown_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_low_volt_rt_relay_info[0], sizeof(parse_low_volt_rt_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_high_volt_rt_relay_info[0], sizeof(parse_high_volt_rt_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_ten_min_grid_over_volt_pro_relay_info[0], sizeof(parse_ten_min_grid_over_volt_pro_relay_info)/sizeof(data_info_id_verison_t)}, //140
    {&parse_envir_temp_over_relay_info[0], sizeof(parse_envir_temp_over_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_envir_temp_under_relay_info[0], sizeof(parse_envir_temp_under_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_abn_residual_curr_relay_info[0], sizeof(parse_abn_residual_curr_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_bus_pos_impedance_warn_relay_info[0], sizeof(parse_bus_pos_impedance_warn_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_neg_pos_impedance_warn_relay_info[0], sizeof(parse_neg_pos_impedance_warn_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_bus_pos_impedance_err_relay_info[0], sizeof(parse_bus_pos_impedance_err_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_neg_pos_impedance_err_relay_info[0], sizeof(parse_neg_pos_impedance_err_relay_info)/sizeof(data_info_id_verison_t)}, //147
    {&parse_radiator_over_temp_relay_info[0], sizeof(parse_radiator_over_temp_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_inter_fan_fail_relay_info[0], sizeof(parse_inter_fan_fail_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_exter_fan_fail_relay_info[0], sizeof(parse_exter_fan_fail_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_insids_machine_over_temp_relay_info[0], sizeof(parse_insids_machine_over_temp_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_fire_error_relay_info[0], sizeof(parse_fire_error_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_dc_arc_err_relay_info[0], sizeof(parse_dc_arc_err_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_afci_self_chk_fail_relay_info[0], sizeof(parse_afci_self_chk_fail_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_ac_lighting_prot_err_relay_info[0], sizeof(parse_ac_lighting_prot_err_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_dc_lighting_prot_err_relay_info[0], sizeof(parse_dc_lighting_prot_err_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_boost_igbt_over_temp_relay_info[0], sizeof(parse_boost_igbt_over_temp_relay_info)/sizeof(data_info_id_verison_t)}, //157
    {&parse_pv_igbt_over_temp_relay_info[0], sizeof(parse_pv_igbt_over_temp_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_grid_relay_err_relay_info[0], sizeof(parse_grid_relay_err_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_ac_curr_sensor_err_relay_info[0], sizeof(parse_ac_curr_sensor_err_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_pv_lock_err_relay_info[0], sizeof(parse_pv_lock_err_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_pld_err_relay_info[0], sizeof(parse_pld_err_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_auxi_power_err_relay_info[0], sizeof(parse_auxi_power_err_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_main_ctrl_eeprom_fault_relay_info[0], sizeof(parse_main_ctrl_eeprom_fault_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_main_ctrl_eeprom_abnormal_relay_info[0], sizeof(parse_main_ctrl_eeprom_abnormal_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_auxi_ctrl_eeprom_fault_relay_info[0], sizeof(parse_auxi_ctrl_eeprom_fault_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_auxi_ctrl_eeprom_abnormal_relay_info[0], sizeof(parse_auxi_ctrl_eeprom_abnormal_relay_info)/sizeof(data_info_id_verison_t)}, //167
    {&parse_moniter_main_ctrl_commu_err_relay_info[0], sizeof(parse_moniter_main_ctrl_commu_err_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_carrier_sync_abnormal_relay_info[0], sizeof(parse_carrier_sync_abnormal_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_protocol_ver_mismatch_relay_info[0], sizeof(parse_protocol_ver_mismatch_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_license_expied_relay_info[0], sizeof(parse_license_expied_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_moniter_err_alm_relay_info[0], sizeof(parse_moniter_err_alm_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_main_auxi_commu_err_relay_info[0], sizeof(parse_main_auxi_commu_err_relay_info)/sizeof(data_info_id_verison_t)}, 
    {&parse_main_cpld_commu_err_relay_info[0], sizeof(parse_main_cpld_commu_err_relay_info)/sizeof(data_info_id_verison_t)}, //174
    {&parse_diff_update_flag_info[0], sizeof(parse_diff_update_flag_info)/sizeof(data_info_id_verison_t)}, //175
    //设置监控参数
    {&parse_moniter_time_para[0], sizeof(parse_moniter_time_para)/sizeof(data_info_id_verison_t)},//176
    {&parse_moniter_rs485_para[0], sizeof(parse_moniter_rs485_para)/sizeof(data_info_id_verison_t)},
    {&parse_moniter_wifi_para[0], sizeof(parse_moniter_wifi_para)/sizeof(data_info_id_verison_t)},
    {&parse_moniter_dongel_wifi_para[0], sizeof(parse_moniter_dongel_wifi_para)/sizeof(data_info_id_verison_t)},
    {&parse_moniter_mqtt_para[0], sizeof(parse_moniter_mqtt_para)/sizeof(data_info_id_verison_t)},//180
    //系统参数（功率控制命令）
    {&parse_para_power_ctrl_data_info[0], sizeof(parse_para_power_ctrl_data_info)/sizeof(data_info_id_verison_t)},
    //特征曲线参数（HVRT）
    {&parse_para_hvrt_cc_data_info[0], sizeof(parse_para_hvrt_cc_data_info)/sizeof(data_info_id_verison_t)},
    //设置漏电流比例校正
    {&parse_para_leak_curr_data_info[0], sizeof(parse_para_leak_curr_data_info)/sizeof(data_info_id_verison_t)},//183
    //设置第三方网管参数
    {&parse_moniter_three_mqtt_para[0], sizeof(parse_moniter_three_mqtt_para)/sizeof(data_info_id_verison_t)},
    //设置经纬度
    {&parse_position_para[0], sizeof(parse_position_para)/sizeof(data_info_id_verison_t)},
    {&parse_dc_component_data_info[0], sizeof(parse_dc_component_data_info)/sizeof(data_info_id_verison_t)},
    //获取保护参数
    {&pack_prot_para1_data_info[0], sizeof(pack_prot_para1_data_info)/sizeof(data_info_id_verison_t)},//187
    {&pack_prot_para2_data_info[0], sizeof(pack_prot_para2_data_info)/sizeof(data_info_id_verison_t)},
    //获取曲线参数
    {&pack_para_lvrt_cc_data_info[0], sizeof(pack_para_lvrt_cc_data_info)/sizeof(data_info_id_verison_t)},//189
    {&pack_para_P_Pn_CC_data_info[0], sizeof(pack_para_P_Pn_CC_data_info)/sizeof(data_info_id_verison_t)},
    {&pack_para_Q_U_CC_data_info[0], sizeof(pack_para_Q_U_CC_data_info)/sizeof(data_info_id_verison_t)},
    {&pack_para_PF_U_CC_data_info[0], sizeof(pack_para_PF_U_CC_data_info)/sizeof(data_info_id_verison_t)},
    {&pack_para_hvrt_cc_data_info[0], sizeof(pack_para_hvrt_cc_data_info)/sizeof(data_info_id_verison_t)},
    //获取系统参数
    {&pack_para_power_ctrl_data_info[0], sizeof(pack_para_power_ctrl_data_info)/sizeof(data_info_id_verison_t)},//194
    {&pack_para_power_grid_data_info[0], sizeof(pack_para_power_grid_data_info)/sizeof(data_info_id_verison_t)},
    {&pack_para_chara_data_info[0], sizeof(pack_para_chara_data_info)/sizeof(data_info_id_verison_t)},
    {&pack_para_mppt_data_info[0], sizeof(pack_para_mppt_data_info)/sizeof(data_info_id_verison_t)},
    {&pack_para_lvrt_data_info[0], sizeof(pack_para_lvrt_data_info)/sizeof(data_info_id_verison_t)},
    {&pack_para_hvrt_data_info[0], sizeof(pack_para_hvrt_data_info)/sizeof(data_info_id_verison_t)},
    {&pack_para_pid_data_info[0], sizeof(pack_para_pid_data_info)/sizeof(data_info_id_verison_t)},
    {&pack_para_afci_data_info[0], sizeof(pack_para_afci_data_info)/sizeof(data_info_id_verison_t)},
    {&pack_para_conn_ctrl_data_info[0], sizeof(pack_para_conn_ctrl_data_info)/sizeof(data_info_id_verison_t)},
    {&pack_para_prot_data_info[0], sizeof(pack_para_prot_data_info)/sizeof(data_info_id_verison_t)},
    {&pack_para_altitude[0], sizeof(pack_para_altitude)/sizeof(data_info_id_verison_t)},
    //功率参数
    {&pack_para_power_regu_data_info[0], sizeof(pack_para_power_regu_data_info)/sizeof(data_info_id_verison_t)},//205
    {&pack_para_comm_fail_data_info[0], sizeof(pack_para_comm_fail_data_info)/sizeof(data_info_id_verison_t)},
    {&pack_para_active_power_reg_data_info[0], sizeof(pack_para_active_power_reg_data_info)/sizeof(data_info_id_verison_t)},
    {&pack_para_reactive_power_regu_data_info[0], sizeof(pack_para_reactive_power_regu_data_info)/sizeof(data_info_id_verison_t)},
    {&pack_para_parall_point_ctrl_active[0], sizeof(pack_para_parall_point_ctrl_active)/sizeof(data_info_id_verison_t)},
    {&pack_para_parall_point_ctrl_reactive[0], sizeof(pack_para_parall_point_ctrl_reactive)/sizeof(data_info_id_verison_t)},
    //比例校正
    {&pack_para_adj_out_volt_data_info[0], sizeof(pack_para_adj_out_volt_data_info)/sizeof(data_info_id_verison_t)},//211
    {&pack_para_adj_bus_volt_data_info[0], sizeof(pack_para_adj_bus_volt_data_info)/sizeof(data_info_id_verison_t)},
    {&pack_para_adj_mppt1_data_info[0], sizeof(pack_para_adj_mppt1_data_info)/sizeof(data_info_id_verison_t)},
    {&pack_para_adj_mppt2_data_info[0], sizeof(pack_para_adj_mppt2_data_info)/sizeof(data_info_id_verison_t)},
    {&pack_para_adj_mppt3_data_info[0], sizeof(pack_para_adj_mppt3_data_info)/sizeof(data_info_id_verison_t)},
    {&pack_para_adj_mppt4_data_info[0], sizeof(pack_para_adj_mppt4_data_info)/sizeof(data_info_id_verison_t)},
    {&pack_para_adj_adj_aux_data_info[0], sizeof(pack_para_adj_adj_aux_data_info)/sizeof(data_info_id_verison_t)},
    {&pack_para_leak_curr_data_info[0], sizeof(pack_para_leak_curr_data_info)/sizeof(data_info_id_verison_t)},
    {&pack_dc_component_data_info[0], sizeof(pack_dc_component_data_info)/sizeof(data_info_id_verison_t)},
    //生产
    {&pack_para_produce_data_info[0], sizeof(pack_para_produce_data_info)/sizeof(data_info_id_verison_t)},//220
    {&pack_para_prop_data_info[0], sizeof(pack_para_prop_data_info)/sizeof(data_info_id_verison_t)},
    //监控参数
    {&pack_basic_info[0], sizeof(pack_basic_info)/sizeof(data_info_id_verison_t)},//222
    {&pack_4g_info[0], sizeof(pack_4g_info)/sizeof(data_info_id_verison_t)},
    {&pack_moniter_time_para[0], sizeof(pack_moniter_time_para)/sizeof(data_info_id_verison_t)},
    {&pack_moniter_rs485_para[0], sizeof(pack_moniter_rs485_para)/sizeof(data_info_id_verison_t)},
    {&pack_moniter_wifi_para[0], sizeof(pack_moniter_wifi_para)/sizeof(data_info_id_verison_t)},
    {&pack_moniter_dongel_wifi_para[0], sizeof(pack_moniter_dongel_wifi_para)/sizeof(data_info_id_verison_t)},
    {&pack_moniter_mqtt_para[0], sizeof(pack_moniter_mqtt_para)/sizeof(data_info_id_verison_t)},
    {&pack_moniter_three_mqtt_para[0], sizeof(pack_moniter_three_mqtt_para)/sizeof(data_info_id_verison_t)},
    {&pack_position_para[0], sizeof(pack_position_para)/sizeof(data_info_id_verison_t)},

    // 延迟升级
    {&parse_delay_update_para[0], sizeof(parse_delay_update_para)/sizeof(data_info_id_verison_t)}, // 231
    {&pack_delay_update_para[0], sizeof(pack_delay_update_para)/sizeof(data_info_id_verison_t)}, // 232
    //电网标准码参数
    {&parse_para_grid_standard_code[0], sizeof(parse_para_grid_standard_code)/sizeof(data_info_id_verison_t)},//233
    {&pack_para_grid_standard_code[0], sizeof(pack_para_grid_standard_code)/sizeof(data_info_id_verison_t)}, // 234

    // 组串接入检测
    {&parse_para_str_in_detection[0], sizeof(parse_para_str_in_detection)/sizeof(data_info_id_verison_t)}, // 235
    {&pack_para_str_in_detection[0], sizeof(pack_para_str_in_detection)/sizeof(data_info_id_verison_t)}, // 236

    // 过欠频降额参数
    {&parse_para_over_under_freq_derate[0], sizeof(parse_para_over_under_freq_derate)/sizeof(data_info_id_verison_t)}, // 237
    {&pack_para_over_under_freq_derate[0], sizeof(pack_para_over_under_freq_derate)/sizeof(data_info_id_verison_t)}, // 238
    //功率研发专用
    {&pack_all_power_special_para_info[0], sizeof(pack_all_power_special_para_info)/sizeof(data_info_id_verison_t)}, // 239
    {&pack_power_special_para_info[0], sizeof(pack_power_special_para_info)/sizeof(data_info_id_verison_t)}, // 240
    {&parse_power_special_para_info[0], sizeof(parse_power_special_para_info)/sizeof(data_info_id_verison_t)}, // 241

    {&pack_drive_test_para_info[0], sizeof(pack_drive_test_para_info)/sizeof(data_info_id_verison_t)}, // 242
    {&parse_drive_test_para_info[0], sizeof(parse_drive_test_para_info)/sizeof(data_info_id_verison_t)}, // 243
    {&pack_adc_test_para_info[0], sizeof(pack_adc_test_para_info)/sizeof(data_info_id_verison_t)}, // 244
    {&parse_adc_test_para_info[0], sizeof(parse_adc_test_para_info)/sizeof(data_info_id_verison_t)}, // 245
    {&pack_product_test_para_info[0], sizeof(pack_product_test_para_info)/sizeof(data_info_id_verison_t)}, // 246
    {&parse_product_test_para_info[0], sizeof(parse_product_test_para_info)/sizeof(data_info_id_verison_t)}, // 247

    // 产品信息
    {&parse_para_prop2_data_info[0], sizeof(parse_para_prop2_data_info)/sizeof(data_info_id_verison_t)}, // 248
    {&pack_para_prop2_data_info[0], sizeof(pack_para_prop2_data_info)/sizeof(data_info_id_verison_t)}, // 249

    {&cmd_pack_ana_power_special_data_info[0], sizeof(cmd_pack_ana_power_special_data_info)/sizeof(data_info_id_verison_t)}, // 250

    // 功率硬件版本
    {&parse_para_prop3_data_info[0], sizeof(parse_para_prop3_data_info)/sizeof(data_info_id_verison_t)}, // 251
    {&pack_para_prop3_data_info[0], sizeof(pack_para_prop3_data_info)/sizeof(data_info_id_verison_t)}, // 252

    {&pack_para_user_auth_time[0], sizeof(pack_para_user_auth_time)/sizeof(data_info_id_verison_t)}, // 253
};

static cmd_parse_info_id_verison_t cmd_pack_info[] RAM_SECTION = {

    {&cmd_pack_ana_valid_data_info[0],       sizeof(cmd_pack_ana_valid_data_info)/sizeof(data_info_id_verison_t)},
    {&cmd_pack_dig_data_info[0],             sizeof(cmd_pack_dig_data_info)/sizeof(data_info_id_verison_t)},
    {&cmd_pack_ana_valid_data_info_2[0],     sizeof(cmd_pack_ana_valid_data_info_2)/sizeof(data_info_id_verison_t)},
    {&cmd_pack_moniter_para[0],              sizeof(cmd_pack_moniter_para)/sizeof(data_info_id_verison_t)},
};

/* 命令总表,必须以空字符串""结束 */
static cmd_t no_poll_cmd_tab[] RAM_SECTION = {
    //设置参数量
    //保护参数
    {SET_PROT_PARA_DATA, CMD_PASSIVE, &cmd_req[1], &cmd_ack[1], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {SET_PROT_PARA_DATA1, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[0]},
    {SET_PROT_PARA_DATA2, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[1]},
    {SET_PROT_PARA_DATA3, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[2]},
    {SET_PROT_PARA_DATA4, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[3]},
    {SET_PROT_PARA_DATA5, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[4]},
    {SET_PROT_PARA_DATA6, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[5]},
    //曲线参数
    {SET_CURVE_PARA_DATA, CMD_PASSIVE, &cmd_req[2], &cmd_ack[2], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {SET_CURVE_PARA_DATA1, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),NULL, NULL,SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[6]},
    {SET_CURVE_PARA_DATA2, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),NULL, NULL,SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[7]},
    {SET_CURVE_PARA_DATA3, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),NULL, NULL,SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[8]},
    {SET_CURVE_PARA_DATA4, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),NULL, NULL,SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[9]},
    {SET_CURVE_PARA_DATA5, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),NULL, NULL,SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[182]},

    //系统参数
    {SET_SYSPARA_DATA , CMD_PASSIVE, &cmd_req[3], &cmd_ack[3], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {SET_SYSPARA_DATA1, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[10]},
    {SET_SYSPARA_DATA2, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[11]},
    {SET_SYSPARA_DATA3, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[12]},
    {SET_SYSPARA_DATA4, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[13]},
    {SET_SYSPARA_DATA5, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[14]},
    {SET_SYSPARA_DATA6, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[15]},
    {SET_SYSPARA_DATA7, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[16]},
    {SET_SYSPARA_DATA8, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[17]},
    {SET_SYSPARA_DATA9, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[18]},
    {SET_SYSPARA_DATA10, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[19]},
    {SET_SYSPARA_DATA11, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[181]},
    {SET_SYSPARA_DATA12, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[233]},
    {SET_SYSPARA_DATA13, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[235]},
    {SET_SYSPARA_DATA14, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[237]},


    //功率参数
    {SET_POWER_PARA_DATA, CMD_PASSIVE, &cmd_req[4], &cmd_ack[4], sizeof(s1363_cmd_head_t),  NULL, NULL, SELF_PACK, SELF_PARSE},
    {SET_POWER_PARA_DATA1, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[20]},
    {SET_POWER_PARA_DATA2, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[21]},
    {SET_POWER_PARA_DATA3, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[22]},
    {SET_POWER_PARA_DATA4, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[23]},
    {SET_POWER_PARA_DATA5, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[24]},
    {SET_POWER_PARA_DATA6, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[25]},

    //设置比例校准参数（定点数）
    {SET_ADJUST_PARA_DATA, CMD_PASSIVE, &cmd_req[5], &cmd_ack[5], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {SET_ADJUST_PARA_DATA1, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[26]},
    {SET_ADJUST_PARA_DATA2, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[27]},
    {SET_ADJUST_PARA_DATA3, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[28]},
    {SET_ADJUST_PARA_DATA4, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[29]},
    {SET_ADJUST_PARA_DATA5, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[30]},
    {SET_ADJUST_PARA_DATA6, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[31]},
    {SET_ADJUST_PARA_DATA7, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[32]},
    {SET_ADJUST_PARA_DATA8, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[183]},
    {SET_ADJUST_PARA_DATA9, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[186]},

     //设置生产参数（定点数）
    {SET_PRODUCE_PARA_DATA, CMD_PASSIVE, &cmd_req[6], &cmd_ack[6], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    //{SET_PRODUCE_PARA_DATA1, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[30]},
    {SET_PRODUCE_PARA_DATA2, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[34]},
    {SET_PRODUCE_PARA_DATA3, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[35]},
    {SET_PRODUCE_PARA_DATA4, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[248]},
    {SET_HARDWARE_VERSION_PARA, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[251]},
    //获取参数
    //保护参数
    {GET_PROT_PARA_DATA, CMD_PASSIVE, &cmd_req[7], &cmd_ack[7], sizeof(s1363_cmd_head_t),    NULL, NULL , SELF_PACK,SELF_PARSE},
    {GET_PROT_PARA_DATA1,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),    NULL, NULL , SELF_PACK,SELF_PARSE,&cmd_parse_info[37]},
    {GET_PROT_PARA_DATA2,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),    NULL, NULL , SELF_PACK,SELF_PARSE,&cmd_parse_info[187]},
    {GET_PROT_PARA_DATA3,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),    NULL, NULL , SELF_PACK,SELF_PARSE,&cmd_parse_info[188]},
    //特征曲线参数                                                                                                                            
    {GET_CURVE_PARA_DATA, CMD_PASSIVE, &cmd_req[8], &cmd_ack[8], sizeof(s1363_cmd_head_t),   NULL, NULL, SELF_PACK, SELF_PARSE},
    {GET_CURVE_PARA_DATA1, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),   NULL, NULL, SELF_PACK, SELF_PARSE,&cmd_parse_info[38]},
    {GET_CURVE_PARA_DATA2, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),   NULL, NULL, SELF_PACK, SELF_PARSE,&cmd_parse_info[189]},
    {GET_CURVE_PARA_DATA3, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),   NULL, NULL, SELF_PACK, SELF_PARSE,&cmd_parse_info[190]},
    {GET_CURVE_PARA_DATA4, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),   NULL, NULL, SELF_PACK, SELF_PARSE,&cmd_parse_info[191]},
    {GET_CURVE_PARA_DATA5, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),   NULL, NULL, SELF_PACK, SELF_PARSE,&cmd_parse_info[192]},
    {GET_CURVE_PARA_DATA6, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),   NULL, NULL, SELF_PACK, SELF_PARSE,&cmd_parse_info[193]},

    //系统参数                                                                                                                                
    {GET_SYSPARA_DATA,  CMD_PASSIVE, &cmd_req[9], &cmd_ack[9], sizeof(s1363_cmd_head_t),      NULL, NULL, SELF_PACK, SELF_PARSE},
    {GET_SYSPARA_DATA1, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),      NULL, NULL, SELF_PACK, SELF_PARSE,&cmd_parse_info[39]},
    {GET_SYSPARA_DATA2, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),      NULL, NULL, SELF_PACK, SELF_PARSE,&cmd_parse_info[194]},
    {GET_SYSPARA_DATA3, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),      NULL, NULL, SELF_PACK, SELF_PARSE,&cmd_parse_info[195]},
    {GET_SYSPARA_DATA4, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),      NULL, NULL, SELF_PACK, SELF_PARSE,&cmd_parse_info[196]},
    {GET_SYSPARA_DATA5, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),      NULL, NULL, SELF_PACK, SELF_PARSE,&cmd_parse_info[197]},
    {GET_SYSPARA_DATA6, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),      NULL, NULL, SELF_PACK, SELF_PARSE,&cmd_parse_info[198]},
    {GET_SYSPARA_DATA7, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),      NULL, NULL, SELF_PACK, SELF_PARSE,&cmd_parse_info[199]},
    {GET_SYSPARA_DATA8, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),      NULL, NULL, SELF_PACK, SELF_PARSE,&cmd_parse_info[200]},
    {GET_SYSPARA_DATA9, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),      NULL, NULL, SELF_PACK, SELF_PARSE,&cmd_parse_info[201]},
    {GET_SYSPARA_DATA10,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),      NULL, NULL, SELF_PACK, SELF_PARSE,&cmd_parse_info[202]},
    {GET_SYSPARA_DATA11,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),      NULL, NULL, SELF_PACK, SELF_PARSE,&cmd_parse_info[203]},
    {GET_SYSPARA_DATA12,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),      NULL, NULL, SELF_PACK, SELF_PARSE,&cmd_parse_info[204]},
    {GET_SYSPARA_DATA13,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),      NULL, NULL, SELF_PACK, SELF_PARSE,&cmd_parse_info[234]},
    {GET_SYSPARA_DATA14,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),      NULL, NULL, SELF_PACK, SELF_PARSE,&cmd_parse_info[236]},
    {GET_SYSPARA_DATA15,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),      NULL, NULL, SELF_PACK, SELF_PARSE,&cmd_parse_info[238]},


    //功率参数
    {GET_POWER_PARA_DATA,  CMD_PASSIVE, &cmd_req[10], &cmd_ack[10], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {GET_POWER_PARA_DATA1, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0],   sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,&cmd_parse_info[40]},
    {GET_POWER_PARA_DATA2, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0],   sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,&cmd_parse_info[205]},
    {GET_POWER_PARA_DATA3, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0],   sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,&cmd_parse_info[206]},
    {GET_POWER_PARA_DATA4, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0],   sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,&cmd_parse_info[207]},
    {GET_POWER_PARA_DATA5, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0],   sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,&cmd_parse_info[208]},
    {GET_POWER_PARA_DATA6, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0],   sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,&cmd_parse_info[209]},
    {GET_POWER_PARA_DATA7, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0],   sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,&cmd_parse_info[210]},
    //比率校准参数
    {GET_ADJUST_PARA_DATA,   CMD_PASSIVE, &cmd_req[11], &cmd_ack[11], sizeof(s1363_cmd_head_t),NULL, NULL , SELF_PACK,SELF_PARSE},
    {GET_ADJUST_PARA_DATA1,  CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),NULL, NULL , SELF_PACK,SELF_PARSE,&cmd_parse_info[41]},
    {GET_ADJUST_PARA_DATA2,  CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),NULL, NULL , SELF_PACK,SELF_PARSE,&cmd_parse_info[211]},
    {GET_ADJUST_PARA_DATA3,  CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),NULL, NULL , SELF_PACK,SELF_PARSE,&cmd_parse_info[212]},
    {GET_ADJUST_PARA_DATA4,  CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),NULL, NULL , SELF_PACK,SELF_PARSE,&cmd_parse_info[213]},
    {GET_ADJUST_PARA_DATA5,  CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),NULL, NULL , SELF_PACK,SELF_PARSE,&cmd_parse_info[214]},
    {GET_ADJUST_PARA_DATA6,  CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),NULL, NULL , SELF_PACK,SELF_PARSE,&cmd_parse_info[215]},
    {GET_ADJUST_PARA_DATA7,  CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),NULL, NULL , SELF_PACK,SELF_PARSE,&cmd_parse_info[216]},
    {GET_ADJUST_PARA_DATA8,  CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),NULL, NULL , SELF_PACK,SELF_PARSE,&cmd_parse_info[217]},
    {GET_ADJUST_PARA_DATA9,  CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),NULL, NULL , SELF_PACK,SELF_PARSE,&cmd_parse_info[218]},
    {GET_ADJUST_PARA_DATA10, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),NULL, NULL , SELF_PACK,SELF_PARSE,&cmd_parse_info[219]},

    //生产参数                                                                                                                                
    {GET_PROD_PARA_DATA,  CMD_PASSIVE, &cmd_req[12], &cmd_ack[12], sizeof(s1363_cmd_head_t),  NULL, NULL , SELF_PACK,SELF_PARSE},
    {GET_PROD_PARA_DATA1, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),  NULL, NULL , SELF_PACK,SELF_PARSE,&cmd_parse_info[42]},
    {GET_PROD_PARA_DATA2, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),  NULL, NULL , SELF_PACK,SELF_PARSE,&cmd_parse_info[220]},
    {GET_PROD_PARA_DATA3, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),  NULL, NULL , SELF_PACK,SELF_PARSE,&cmd_parse_info[221]},
    {GET_PROD_PARA_DATA4, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),  NULL, NULL , SELF_PACK,SELF_PARSE,&cmd_parse_info[249]},
    {GET_HARDWARE_VERSION_PARA, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),  NULL, NULL , SELF_PACK,SELF_PARSE,&cmd_parse_info[252]},
                                                                                                                                             
    //获取功率厂家信息                                                                                                                        
    {GET_POWER_FACT_DATA, CMD_PASSIVE, &cmd_req[13], &cmd_ack[13], sizeof(s1363_cmd_head_t), NULL, NULL , SELF_PACK,SELF_PARSE,&cmd_parse_info[43]},
    //获取监控厂家信息                                                                                                                        
    {GET_CSU_FACT_DATA, CMD_PASSIVE, &cmd_req[14], &cmd_ack[14], sizeof(s1363_cmd_head_t),   NULL, NULL , SELF_PACK,SELF_PARSE,&cmd_parse_info[44]},
    //获取系统时间
    {GET_CSU_TIME_DATA, CMD_PASSIVE, &cmd_req[15], &cmd_ack[15], sizeof(s1363_cmd_head_t),NULL,NULL , SELF_PACK, SELF_PARSE},
    //设置系统时间
    {SET_CSU_TIME_DATA, CMD_PASSIVE, &cmd_req[16], &cmd_ack[16], sizeof(s1363_cmd_head_t), NULL,NULL , SELF_PACK, SELF_PARSE},

    //模拟量和数字量
    {DAC_GET_ANA_INT_VAILD_DATA,          CMD_PASSIVE, &cmd_req[17], &cmd_ack[17], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, &cmd_pack_info[0]},
    {DAC_GET_DIG_DATA,              CMD_PASSIVE, &cmd_req[18], &cmd_ack[18], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, &cmd_pack_info[1]},

    {GET_VER_DATA,              CMD_PASSIVE, &cmd_req[19], &cmd_ack[19], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE},
    {GET_ADDR_DATA,             CMD_PASSIVE, &cmd_req[20], &cmd_ack[20], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE},

    //告警量
    {DAC_GET_ALM_DATA,           CMD_PASSIVE, &cmd_req[21], &cmd_ack[21], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    //调测命令
    {DAC_TEST_ORDER,             CMD_PASSIVE, &cmd_req[22], &cmd_ack[22], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    //控制命令
    {DAC_CONTROL_ORDER, CMD_PASSIVE, &cmd_req[23], &cmd_ack[23], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},

    //模拟量2
    {DAC_GET_ANA_INT_VAILD_DATA_2,  CMD_PASSIVE, &cmd_req[24], &cmd_ack[24], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, &cmd_pack_info[2]},

    //录波参数
    {DAC_SET_FAULT_RECORD_PARA, CMD_PASSIVE, &cmd_req[25], &cmd_ack[25], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {DAC_SET_FAULT_RECORD1_PARA,CMD_PASSIVE, &cmd_req[0],  &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, SELF_PACK},
    {DAC_SET_FAULT_RECORD2_PARA,CMD_PASSIVE, &cmd_req[0],  &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, SELF_PACK},
    {DAC_GET_FAULT_RECORD_PARA, CMD_PASSIVE, &cmd_req[26], &cmd_ack[26], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, SELF_PACK},
    {DAC_GET_FAULT_RECORD_PARA1,CMD_PASSIVE, &cmd_req[0],  &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, SELF_PACK},
    {DAC_GET_FAULT_RECORD_PARA2,CMD_PASSIVE, &cmd_req[0],  &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, SELF_PACK},
    {DAC_GET_FAULT_RECORD_PARA3,CMD_PASSIVE, &cmd_req[0],  &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, SELF_PACK},
    //告警干接点和告警等级
    {DAC_SET_ALM_LEVEL,CMD_PASSIVE, &cmd_req[27], &cmd_ack[27], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, NULL},
    {DAC_SET_MPPT_OVER_VOLT_LEVEL                 ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE,NULL, &cmd_parse_info[47]},
    {DAC_SET_BUS_OVER_VOLT_LEVEL                  ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE,NULL, &cmd_parse_info[48]},
    {DAC_SET_POS_BUS_OVER_VOLT_LEVEL              ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE,NULL, &cmd_parse_info[49]},
    {DAC_SET_NEG_BUS_OVER_VOLT_LEVEL              ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE,NULL, &cmd_parse_info[50]},
    {DAC_SET_BUS_VOLT_IMBALANCE_LEVEL             ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE,NULL, &cmd_parse_info[51]},
    {DAC_SET_STR_REVERSE_CONNECT_LEVEL            ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE,NULL, &cmd_parse_info[52]},
    {DAC_SET_STR_LOSS_LEVEL                       ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE,NULL, &cmd_parse_info[53]},
    {DAC_SET_MPPT_OVER_CURR_WARN_LEVEL            ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE,NULL, &cmd_parse_info[54]},
    {DAC_SET_MPPT_OVER_CURR_ERROR_LEVEL           ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE,NULL, &cmd_parse_info[55]},
    {DAC_SET_BUS_SOFT_START_ERR_LEVEL             ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[56]},
    {DAC_SET_DC_DISCONNECTOR_TRIP_LEVEL           ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[57]},
    {DAC_SET_PHASE_VOLT_OVER_VOLT_LEVEL           ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[58]},
    {DAC_SET_OVER_VOLT_BETWEEN_PHASE_LEVEL        ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[59]},
    {DAC_SET_PHASE_VOLT_UNDER_VOLT_LEVEL          ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[60]},
    {DAC_SET_UNDER_VOLT_BETWEEN_PHASE_LEVEL       ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[61]},
    {DAC_SET_GRID_OVER_FREQ_LEVEL                 ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[62]},
    {DAC_SET_GRID_UNDER_FREQ_LEVEL                ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[63]},
    {DAC_SET_AC_PHASE_LOSS_LEVEL                  ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[64]},
    {DAC_SET_OUTPUT_SHORT_CIRCUIT_LEVEL           ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[65]},
    {DAC_SET_OUT_CURR_IMBALANCE_LEVEL             ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[66]},
    {DAC_SET_ABNORMAL_DC_COMPONENT_LEVEL          ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[67]},
    {DAC_SET_ISLAND_PROT_LEVEL                    ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[68]},
    {DAC_SET_PV_OVER_CURR_WARN_LEVEL              ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[69]},
    {DAC_SET_PV_OVER_CURR_ERROR_LEVEL             ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[70]},
    {DAC_SET_GRID_ANTI_ERROR_LEVEL                ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[71]},
    {DAC_SET_SINGLE_PHASE_GND_SHORT_CIRCUIT_LEVEL ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[72]},
    {DAC_SET_LOW_POWER_SHUTDOWN_LEVEL             ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[73]},
    {DAC_SET_LOW_VOLT_RT_LEVEL                    ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[74]},
    {DAC_SET_HIGH_VOLT_RT_LEVEL                   ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[75]},
    {DAC_SET_TEN_MINUTE_GRID_OVER_VOLT_PROT_LEVEL ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[76]},
    {DAC_SET_ENVIR_OVER_TEMP_LEVEL                ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[77]},
    {DAC_SET_ENVIR_LOW_TEMP_LEVEL                 ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[78]},
    {DAC_SET_ABN_RESIDUAL_CURR_LEVEL              ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[79]},
    {DAC_SET_BUS_POS_IMPEDANCE_WARN_LEVEL         ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[80]},
    {DAC_SET_BUS_NEG_IMPEDANCE_WARN_LEVEL         ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[81]},
    {DAC_SET_BUS_POS_IMPEDANCE_ERROR_LEVEL        ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[82]},
    {DAC_SET_BUS_NEG_IMPEDANCE_ERROR_LEVEL        ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[83]},
    {DAC_SET_RADIATOR_OVER_TEMP_LEVEL             ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[84]},
    {DAC_SET_INTER_FAN_FAIL_LEVEL                 ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[85]},
    {DAC_SET_EXTER_FAN_FAIL_LEVEL                 ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[86]},
    {DAC_SET_OVER_TEMP_INSIDS_MACHINE_LEVEL       ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[87]},
    {DAC_SET_FIRE_ERROR_LEVEL                     ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[88]},
    {DAC_SET_DC_ARC_DAULT_LEVEL                   ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[89]},
    {DAC_SET_AFCI_SELF_CHK_FAIL_LEVEL             ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[90]},
    {DAC_SET_AC_LIGHTING_PROTECTION_ERR_LEVEL        ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[91]},
    {DAC_SET_DC_LIGHTING_PROTECTION_ERR_LEVEL        ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[92]},
    {DAC_SET_BOOST_IGBT_OVER_TEMP_LEVEL           ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[93]},
    {DAC_SET_PV_IGBT_OVER_TEMP_LEVEL              ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[94]},
    {DAC_SET_GRID_RELAY_ERROR_LEVEL               ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[95]},
    {DAC_SET_AC_CURR_SENSOR_ERROR_LEVEL           ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[96]},
    {DAC_SET_PV_LOCK_ERROR_LEVEL                  ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[97]},
    {DAC_SET_ABNORMAL_OPER_BUILD_IN_PID_LEVEL     ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[98]},
    {DAC_SET_ABN_AUXI_POWER_SUPPLT_LEVEL          ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[99]},
    {DAC_SET_MAIN_CTRL_EEPROM_FAULT_LEVEL         ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[100]},
    {DAC_SET_MAIN_CTRL_EEPROM_ABNORMAL_LEVEL      ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[101]},
    {DAC_SET_AUXI_CTRL_EEPROM_FAULT_LEVEL         ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[102]},
    {DAC_SET_AUXI_CTRL_EEPROM_ABNORMAL_LEVEL      ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[103]},
    {DAC_SET_MONITER_MAIN_CTRL_COMMU_ERROR_LEVEL  ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[104]},
    {DAC_SET_CARRIER_SYNC_ABNORMAL_LEVEL          ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[105]},
    {DAC_SET_PROTOCOL_VER_MISMATCH_LEVEL          ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[106]},
    {DAC_SET_LICENSE_EXPIRED_LEVEL                ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[107]},
    {DAC_SET_MONITER_ERROR_ALARM_LEVEL            ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[108]},
    {DAC_SET_MAIN_AUXI_COMMU_ERROR_LEVEL          ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[109]},
    {DAC_SET_MAIN_CPLD_COMMU_ERROR_LEVEL          ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[110]},
    
    {DAC_GET_ALM_LEVEL,CMD_PASSIVE, &cmd_req[28], &cmd_ack[28], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, &cmd_parse_info[45]},
    
    {DAC_SET_ALM_RELAY ,CMD_PASSIVE, &cmd_req[29], &cmd_ack[29], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, SELF_PACK},
    {DAC_SET_MPPT_OVER_VOLT_RELAY                ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[111]},
    {DAC_SET_BUS_OVER_VOLT_RELAY                 ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[112]},
    {DAC_SET_POS_BUS_OVER_VOLT_RELAY             ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[113]},
    {DAC_SET_NEG_BUS_OVER_VOLT_RELAY             ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[114]},
    {DAC_SET_BUS_VOLT_IMBALANCE_RELAY            ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[115]},
    {DAC_SET_STR_REVERSE_CONNECT_RELAY           ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[116]},
    {DAC_SET_STR_LOSS_RELAY                      ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[117]},
    {DAC_SET_MPPT_OVER_CURR_WARN_RELAY           ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[118]},
    {DAC_SET_MPPT_OVER_CURR_ERROR_RELAY          ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[119]},
    {DAC_SET_BUS_SOFT_START_ERR_RELAY            ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[120]},
    {DAC_SET_DC_DISCONNECTOR_TRIP_RELAY          ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[121]},
    {DAC_SET_PHASE_VOLT_OVER_VOLT_RELAY          ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[122]},
    {DAC_SET_OVER_VOLT_BETWEEN_PHASE_RELAY       ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[123]},
    {DAC_SET_PHASE_VOLT_UNDER_VOLT_RELAY         ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[124]},
    {DAC_SET_UNDER_VOLT_BETWEEN_PHASE_RELAY      ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[125]},
    {DAC_SET_GRID_OVER_FREQ_RELAY                ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[126]},
    {DAC_SET_GRID_UNDER_FREQ_RELAY               ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[127]},
    {DAC_SET_AC_PHASE_LOSS_RELAY                 ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[128]},
    {DAC_SET_OUTPUT_SHORT_CIRCUIT_RELAY          ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[129]},
    {DAC_SET_OUT_CURR_IMBALANCE_RELAY            ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[130]},
    {DAC_SET_ABNORMAL_DC_COMPONENT_RELAY         ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[131]},
    {DAC_SET_ISLAND_PROT_RELAY                   ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[132]},
    {DAC_SET_PV_OVER_CURR_WARN_RELAY             ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[133]},
    {DAC_SET_PV_OVER_CURR_ERROR_RELAY            ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[134]},
    {DAC_SET_GRID_ANTI_ERROR_RELAY               ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[135]},
    {DAC_SET_SINGLE_PHASE_GND_SHORT_CIRCUIT_RELAY,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[136]},
    {DAC_SET_LOW_POWER_SHUTDOWN_RELAY            ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[137]},
    {DAC_SET_LOW_VOLT_RT_RELAY                   ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[138]},
    {DAC_SET_HIGH_VOLT_RT_RELAY                  ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[139]},
    {DAC_SET_TEN_MINUTE_GRID_OVER_VOLT_PROT_RELAY,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[140]},
    {DAC_SET_ENVIR_OVER_TEMP_RELAY               ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[141]},
    {DAC_SET_ENVIR_LOW_TEMP_RELAY                ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[142]},
    {DAC_SET_ABN_RESIDUAL_CURR_RELAY             ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[143]},
    {DAC_SET_BUS_POS_IMPEDANCE_WARN_RELAY        ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[144]},
    {DAC_SET_BUS_NEG_IMPEDANCE_WARN_RELAY        ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[145]},
    {DAC_SET_BUS_POS_IMPEDANCE_ERROR_RELAY       ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[146]},
    {DAC_SET_BUS_NEG_IMPEDANCE_ERROR_RELAY       ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[147]},
    {DAC_SET_RADIATOR_OVER_TEMP_RELAY            ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[148]},
    {DAC_SET_INTER_FAN_FAIL_RELAY                ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[149]},
    {DAC_SET_EXTER_FAN_FAIL_RELAY                ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[150]},
    {DAC_SET_OVER_TEMP_INSIDS_MACHINE_RELAY      ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[151]},
    {DAC_SET_FIRE_ERROR_RELAY                    ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[152]},
    {DAC_SET_DC_ARC_DAULT_RELAY                  ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[153]},
    {DAC_SET_AFCI_SELF_CHK_FAIL_RELAY            ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[154]},
    {DAC_SET_AC_LIGHTING_PROTECTION_ERR_RELAY    ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[155]},
    {DAC_SET_DC_LIGHTING_PROTECTION_ERR_RELAY    ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[156]},
    {DAC_SET_BOOST_IGBT_OVER_TEMP_RELAY          ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[157]},
    {DAC_SET_PV_IGBT_OVER_TEMP_RELAY             ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[158]},
    {DAC_SET_GRID_RELAY_ERROR_RELAY              ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[159]},
    {DAC_SET_AC_CURR_SENSOR_ERROR_RELAY          ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[160]},
    {DAC_SET_PV_LOCK_ERROR_RELAY                 ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[161]},
    {DAC_SET_ABNORMAL_OPER_BUILD_IN_PID_RELAY    ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[162]},
    {DAC_SET_ABN_AUXI_POWER_SUPPLT_RELAY         ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[163]},
    {DAC_SET_MAIN_CTRL_EEPROM_FAULT_RELAY        ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[164]},
    {DAC_SET_MAIN_CTRL_EEPROM_ABNORMAL_RELAY     ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[165]},
    {DAC_SET_AUXI_CTRL_EEPROM_FAULT_RELAY        ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[166]},
    {DAC_SET_AUXI_CTRL_EEPROM_ABNORMAL_RELAY     ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[167]},
    {DAC_SET_MONITER_MAIN_CTRL_COMMU_ERROR_RELAY ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[168]},
    {DAC_SET_CARRIER_SYNC_ABNORMAL_RELAY         ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[169]},
    {DAC_SET_PROTOCOL_VER_MISMATCH_RELAY         ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[170]},
    {DAC_SET_LICENSE_EXPIRED_RELAY               ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[171]},
    {DAC_SET_MONITER_ERROR_ALARM_RELAY           ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[172]},
    {DAC_SET_MAIN_AUXI_COMMU_ERROR_RELAY         ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[173]},
    {DAC_SET_MAIN_CPLD_COMMU_ERROR_RELAY         ,CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, send_update_alarm_para_msg , SELF_PARSE, NULL, &cmd_parse_info[174]},

    {DAC_GET_ALM_RELAY,CMD_PASSIVE, &cmd_req[30], &cmd_ack[30], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, &cmd_parse_info[46]},
    {DAC_GET_DIFF_UPDATE_FLAG,CMD_PASSIVE, &cmd_req[31], &cmd_ack[31], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, &cmd_parse_info[175]},

    {DAC_GET_MONITER_PARA,  CMD_PASSIVE, &cmd_req[32], &cmd_ack[32], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE},
    {DAC_GET_MONITER_PARA1,  CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, &cmd_pack_info[3]},
    {DAC_GET_MONITER_PARA2,  CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, &cmd_parse_info[222]},
    {DAC_GET_MONITER_PARA3,  CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, &cmd_parse_info[223]},
    {DAC_GET_MONITER_PARA4,  CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, &cmd_parse_info[224]},
    {DAC_GET_MONITER_PARA5,  CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, &cmd_parse_info[225]},
    {DAC_GET_MONITER_PARA6,  CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, &cmd_parse_info[226]},
    {DAC_GET_MONITER_PARA7,  CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, &cmd_parse_info[227]},
    {DAC_GET_MONITER_PARA8,  CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, &cmd_parse_info[228]},
    {DAC_GET_MONITER_PARA9,  CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, &cmd_parse_info[229]},
    {DAC_GET_MONITER_PARA10,  CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE,&cmd_parse_info[230]},
    {DAC_GET_DELAY_UPDATE, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, &cmd_parse_info[232]},
    {DAC_GET_USER_AUTH_TIME, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, &cmd_parse_info[253]},
    
    {DAC_SET_MONITER_PARA, CMD_PASSIVE, &cmd_req[33], &cmd_ack[33], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE},
    {DAC_SET_MONITER_PARA1, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, NULL, &cmd_parse_info[176]},
    {DAC_SET_MONITER_PARA2, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, NULL, &cmd_parse_info[177]},
    {DAC_SET_MONITER_PARA3, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, NULL, &cmd_parse_info[178]},
    {DAC_SET_MONITER_PARA4, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, NULL, &cmd_parse_info[179]},
    {DAC_SET_MONITER_PARA5, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, NULL, &cmd_parse_info[180]},
    {DAC_SET_CSC_VERSION_PARA, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, NULL, NULL},
    {DAC_SET_PSC_VERSION_PARA, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, NULL, NULL},
    {DAC_SET_MONITER_PARA6, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, pack_app_request_load, parse_app_request_load, NULL, NULL},
    {DAC_SET_MONITER_PARA7, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, pack_app_change_password, parse_app_change_password, NULL, NULL},
    {DAC_SET_MONITER_PARA8, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, NULL, &cmd_parse_info[184]},
    {DAC_SET_MONITER_PARA9, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, NULL, &cmd_parse_info[185]},
    {DAC_SET_DELAY_UPDATE, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, NULL, &cmd_parse_info[231]},
    {DAC_SET_USER_AUTH_TIME, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, parse_app_user_auth_time, NULL, NULL},

    {DAC_GET_PARALLEL_INFO, CMD_PASSIVE, &cmd_req[34], &cmd_ack[34], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, NULL, NULL},
    {DAC_GET_1363_VERSION_INFO, CMD_PASSIVE, &cmd_req[35], &cmd_ack[35], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, NULL, NULL},
    {DAC_SET_POWER_SPECIAL_PARA, CMD_PASSIVE, &cmd_req[36], &cmd_ack[36], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE},
    {DAC_SET_POWER_SPECIAL_PARA1, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, NULL, &cmd_parse_info[241]},
    {DAC_SET_POWER_SPECIAL_PARA2, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, NULL, &cmd_parse_info[243]},
    {DAC_SET_POWER_SPECIAL_PARA3, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, NULL, &cmd_parse_info[245]},
    {DAC_SET_POWER_SPECIAL_PARA4, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, NULL, &cmd_parse_info[247]},
    
    
    {DAC_GET_POWER_SPECIAL_PARA,  CMD_PASSIVE, &cmd_req[37], &cmd_ack[37], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE},
    {DAC_GET_POWER_SPECIAL_PARA1,  CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, &cmd_parse_info[239]},
    {DAC_GET_POWER_SPECIAL_PARA2,  CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, &cmd_parse_info[240]},
    {DAC_GET_POWER_SPECIAL_PARA3,  CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, &cmd_parse_info[242]},
    {DAC_GET_POWER_SPECIAL_PARA4,  CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, &cmd_parse_info[244]},
    {DAC_GET_POWER_SPECIAL_PARA5,  CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, &cmd_parse_info[246]},
    
    {GET_POWER_SPECIAL_ANA,  CMD_PASSIVE, &cmd_req[38], &cmd_ack[38], sizeof(s1363_cmd_head_t), NULL, NULL, NULL, SELF_PARSE, &cmd_parse_info[250]},
    {0},
};


static dev_type_t dev_inverter_s1363_main = {
    DEV_DAC_NORTH_S1363, 1, PROTOCOL_YD_1363, LINK_INVERTER, R_BUFF_LEN_1024, S_BUFF_LEN_5120, BOTTOM_CSU_TYPE, no_poll_cmd_tab, NULL, 0,
};

//IP链路线程中malloc收发内存，北向初始化不分配收发内存
static dev_type_t dev_inverter_s1363_IP = {  // 收发端填1，仅仅是为了malloc申请成功，实际的收发buff长度不在这里控制
    DEV_DAC_NORTH_S1363, 1, PROTOCOL_YD_1363, LINK_DAC_IP, 1, 1, BOTTOM_CSU_TYPE, no_poll_cmd_tab, NULL, 0,
};

int pack_proto_ver(void* dev_inst, void *cmd_buf)
{
    if(dev_inst == NULL || cmd_buf == NULL)
    {
        return FAILURE;
    }
    unsigned char* buff = ((cmd_buf_t*)cmd_buf)->buf;
    buff[0] = VER_22;
    ((cmd_buf_t*)cmd_buf)->data_len = 1;
    return SUCCESSFUL;
}

int pack_proto_addr(void* dev_inst, void *cmd_buf)
{
    if(dev_inst == NULL || cmd_buf == NULL)
    {
        return FAILURE;
    }
    unsigned char    self_addr = 0;
    get_one_para(DAC_COMMON_ID_RS485_COMMU_ADDR_OFFSET , &self_addr);
    unsigned char* buff = ((cmd_buf_t*)cmd_buf)->buf;
    buff[0] = self_addr;
    ((cmd_buf_t*)cmd_buf)->data_len = 1;
    return SUCCESSFUL;
}

int parse_alarm_data(void* dev_inst, void *cmd_buf)
{
    return SUCCESSFUL;
}

int pack_alm_data(void* dev_inst, void *cmd_buf)
{
    if(dev_inst == NULL || cmd_buf == NULL)
    {
        return FAILURE;
    }
    unsigned char* buff = ((cmd_buf_t*)cmd_buf)->buf;
    unsigned int offset = 0;
    int real_alm_num = 0;
    time_base_t temp_time ;

    buff[offset] = 0;   //data_flag
    offset += 1;
    buff[offset] = real_alm_num;
    offset += 1;

    real_alarm_info_t real_alarm = {};
    real_alm_num = get_realtime_alarm_count();
    for(int loop = 0; loop < real_alm_num; loop ++)
    {     
        //告警产生时间
        rt_memset_s(&real_alarm, sizeof(real_alarm_info_t), 0, sizeof(real_alarm_info_t));
        if(FAILURE == get_nth_realtime_alarm_info(&real_alarm, loop))
        {
            continue;
        }
        time_t_to_timestruct(real_alarm.start_time, &temp_time);
        put_time_to_buff(&buff[offset], temp_time);
        offset += 7;
        //告警ID
        unsigned short alm_code = ALM_ID_GET_ALM_CODE(real_alarm.alm_id) + ALM_ID_GET_DEV(real_alarm.alm_id);
        put_uint16_to_buff(&buff[offset], alm_code);
        offset += 2;
        //告警等级
        buff[offset] = real_alarm.alm_level;
        offset ++;
        //告警原因
        buff[offset] = 1;
        offset ++;
    }

    buff[1] = real_alm_num;
    ((cmd_buf_t*)cmd_buf)->data_len = offset;
    return SUCCESSFUL;
}

static cmd_handle_register_t s_cmd_handle[] = {
    //设置
    {DEV_DAC_NORTH_S1363, SET_PROT_PARA_DATA, CMD_TYPE_NO_POLL,comm_prase_set_para,comm_pack_set_para},    //<设定保护参数(定点数)
    {DEV_DAC_NORTH_S1363, SET_CURVE_PARA_DATA, CMD_TYPE_NO_POLL,comm_prase_set_para,comm_pack_set_para},  //<设置特征曲线参数（定点数）
    {DEV_DAC_NORTH_S1363, SET_SYSPARA_DATA , CMD_TYPE_NO_POLL,comm_prase_set_para,comm_pack_set_para},     ////<设定系统参数（定点数)
    {DEV_DAC_NORTH_S1363, SET_POWER_PARA_DATA, CMD_TYPE_NO_POLL,comm_prase_set_para,comm_pack_set_para},  ///<//<设定功率参数（定点数）
    {DEV_DAC_NORTH_S1363, SET_ADJUST_PARA_DATA, CMD_TYPE_NO_POLL,comm_prase_set_para,comm_pack_set_para},   //设置比例校准参数（定点数）
    {DEV_DAC_NORTH_S1363, SET_PRODUCE_PARA_DATA, CMD_TYPE_NO_POLL,comm_prase_set_para,comm_pack_set_para},   //设置生产参数（定点数）
    //获取  
    {DEV_DAC_NORTH_S1363,  GET_PROT_PARA_DATA,   CMD_TYPE_NO_POLL,  parse_com_group,  comm_pack_get_para},//<获取保护参数（定点数)
    {DEV_DAC_NORTH_S1363,  GET_CURVE_PARA_DATA,  CMD_TYPE_NO_POLL,  parse_com_group,  comm_pack_get_para},//<获取特征曲线参数（定点数)
    {DEV_DAC_NORTH_S1363,  GET_SYSPARA_DATA,     CMD_TYPE_NO_POLL,  parse_com_group,  comm_pack_get_para},//获取系统参数定点
    {DEV_DAC_NORTH_S1363,  GET_POWER_PARA_DATA,  CMD_TYPE_NO_POLL,  parse_com_group,  comm_pack_get_para},//<获取功率参数（定点数）
    {DEV_DAC_NORTH_S1363,  GET_ADJUST_PARA_DATA, CMD_TYPE_NO_POLL,  parse_adjust_para,  comm_pack_get_para},//获取比例校准参数（定点数）
    {DEV_DAC_NORTH_S1363,  GET_PROD_PARA_DATA,   CMD_TYPE_NO_POLL,  parse_product_para,  comm_pack_get_para},//获取生产参数（定点数）
    //获取功率厂家信息
    {DEV_DAC_NORTH_S1363,  GET_POWER_FACT_DATA,   CMD_TYPE_NO_POLL,  parse_com_group,  NULL},//获取功率厂家信息
    {DEV_DAC_NORTH_S1363,  GET_CSU_FACT_DATA,   CMD_TYPE_NO_POLL,  parse_com_group,  NULL},//获取监控厂家信息
    //获取时间
    {DEV_DAC_NORTH_S1363,  GET_CSU_TIME_DATA,   CMD_TYPE_NO_POLL,  parse_com_group,  pack_sys_time_data},//获取功率厂家信息
    //设置时间
    {DEV_DAC_NORTH_S1363,  SET_CSU_TIME_DATA,   CMD_TYPE_NO_POLL,  parse_sys_time_data,  pack_set_time},

    // 获取模拟量
    {DEV_DAC_NORTH_S1363,  DAC_GET_ANA_INT_DATA,      CMD_TYPE_NO_POLL,   NULL, NULL},
    // 获取模拟量2
    {DEV_DAC_NORTH_S1363,  DAC_GET_ANA_INT_DATA_2,      CMD_TYPE_NO_POLL,   NULL, NULL},
    // 获取状态量
    {DEV_DAC_NORTH_S1363,  DAC_GET_DIG_DATA,      CMD_TYPE_NO_POLL,   get_dig_data_parse, NULL},
    //获取协议版本号
    {DEV_DAC_NORTH_S1363,  GET_VER_DATA,      CMD_TYPE_NO_POLL, NULL , pack_proto_ver },
    //获取设备地址
    {DEV_DAC_NORTH_S1363,  GET_ADDR_DATA,      CMD_TYPE_NO_POLL, NULL,  pack_proto_addr },
    //获取告警量
    {DEV_DAC_NORTH_S1363,  DAC_GET_ALM_DATA, CMD_TYPE_NO_POLL, parse_alarm_data, pack_alm_data},
    //调测命令
    {DEV_DAC_NORTH_S1363, DAC_TEST_ORDER, CMD_TYPE_NO_POLL, parse_test_record_cmd, NULL},
    //控制命令
    {DEV_DAC_NORTH_S1363, DAC_CONTROL_ORDER, CMD_TYPE_NO_POLL, parse_control_cmd, pack_control_cmd},
    //录波参数
    {DEV_DAC_NORTH_S1363, DAC_GET_FAULT_RECORD_PARA, CMD_TYPE_NO_POLL, parse_get_fault_record_para, comm_pack_get_para},
    {DEV_DAC_NORTH_S1363, DAC_GET_FAULT_RECORD_PARA1, CMD_TYPE_NO_POLL, NULL, pack_get_fault_record_para},
    {DEV_DAC_NORTH_S1363, DAC_GET_FAULT_RECORD_PARA2, CMD_TYPE_NO_POLL, NULL, pack_get_fault_record_para},
    {DEV_DAC_NORTH_S1363, DAC_GET_FAULT_RECORD_PARA3, CMD_TYPE_NO_POLL, NULL, pack_get_fault_record_para},
    {DEV_DAC_NORTH_S1363, DAC_SET_FAULT_RECORD_PARA, CMD_TYPE_NO_POLL, comm_prase_set_para, NULL},
    {DEV_DAC_NORTH_S1363, DAC_SET_FAULT_RECORD1_PARA, CMD_TYPE_NO_POLL, parse_set_fault_record1_para, NULL},
    {DEV_DAC_NORTH_S1363, DAC_SET_FAULT_RECORD2_PARA, CMD_TYPE_NO_POLL, parse_set_fault_record2_para, NULL},
    //设置告警等级
    {DEV_DAC_NORTH_S1363, DAC_SET_ALM_LEVEL, CMD_TYPE_NO_POLL, comm_prase_set_para, NULL},
    //设置告警干接点
    {DEV_DAC_NORTH_S1363, DAC_SET_ALM_RELAY, CMD_TYPE_NO_POLL, comm_prase_set_para, NULL},
    //获取监控参数
    {DEV_DAC_NORTH_S1363, DAC_GET_MONITER_PARA, CMD_TYPE_NO_POLL, parse_com_group, comm_pack_get_para},
    //设置监控参数
    {DEV_DAC_NORTH_S1363, DAC_SET_MONITER_PARA, CMD_TYPE_NO_POLL, comm_prase_set_para, NULL},
    // 设置监控CSC版本信息
    {DEV_DAC_NORTH_S1363, DAC_SET_CSC_VERSION_PARA, CMD_TYPE_NO_POLL, parse_csc_cmd, NULL},
    // 设置功率PSC版本信息
    {DEV_DAC_NORTH_S1363, DAC_SET_PSC_VERSION_PARA, CMD_TYPE_NO_POLL, parse_psc_cmd, NULL},
    {DEV_DAC_NORTH_S1363, DAC_GET_PARALLEL_INFO, CMD_TYPE_NO_POLL, NULL, pack_parallel_data},
    {DEV_DAC_NORTH_S1363, DAC_GET_1363_VERSION_INFO, CMD_TYPE_NO_POLL, NULL, pack_1363_version_info},
    //功率研发专用
    {DEV_DAC_NORTH_S1363, DAC_GET_POWER_SPECIAL_PARA, CMD_TYPE_NO_POLL, parse_test_para,  comm_pack_get_para},
    {DEV_DAC_NORTH_S1363, DAC_SET_POWER_SPECIAL_PARA, CMD_TYPE_NO_POLL, comm_prase_set_para, NULL},
    
};

int parse_adjust_para(void* dev_inst, void *cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    cmd_buf_t* cmd_buf_tmp = ((cmd_buf_t *)cmd_buff);
    cmd_buf_tmp->cmd->cmd_type = cmd_buf_tmp->buf[0];
    //比例零点校正参数，需要主动获取
    send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, DC_AC_GET_ADG_PARA_DATA );
    rt_thread_mdelay(500);
    return SUCCESSFUL;
}

int parse_product_para(void* dev_inst, void *cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    cmd_buf_t* cmd_buf_tmp = ((cmd_buf_t *)cmd_buff);
    cmd_buf_tmp->cmd->cmd_type = cmd_buf_tmp->buf[0];
    //生产参数，需要主动获取
    send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, DC_AC_GET_PRO_PARA_DATA);
    rt_thread_mdelay(500);
    return SUCCESSFUL;
}

int parse_get_fault_record_para(void* dev_inst, void *cmd_buf)
{   
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf != NULL, FAILURE);
    cmd_buf_t* cmd_buf_tmp = ((cmd_buf_t *)cmd_buf);
    cmd_buf_tmp->cmd->cmd_type = cmd_buf_tmp->buf[0];

    // 发消息给给南向更新实时量存的录波参数
    send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, DC_AC_GET_FAULT_RECORD1_PARA);
    send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, DC_AC_GET_FAULT_RECORD2_PARA);
    rt_thread_mdelay(1000);
    return SUCCESSFUL;
}

int pack_get_fault_record_para(void* dev_inst, void *cmd_buf)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf != NULL, FAILURE);
    cmd_buf_t* cmd_buf_tmp = ((cmd_buf_t *)cmd_buf);
    unsigned short point_num = 0,record_idx = 0, record_num = 2;
    unsigned short data_u = 0;
    unsigned int data_i = 0;
    int i = 0;
    unsigned int array_point_id[2][10] = {{DAC_DATA_ID_FIRST_RECORDED_POINT_ID1,
                                           DAC_DATA_ID_FIRST_RECORDED_POINT_ID2,
                                           DAC_DATA_ID_FIRST_RECORDED_POINT_ID3,
                                           DAC_DATA_ID_FIRST_RECORDED_POINT_ID4,
                                           DAC_DATA_ID_FIRST_RECORDED_POINT_ID5,
                                           DAC_DATA_ID_FIRST_RECORDED_POINT_ID6,
                                           DAC_DATA_ID_FIRST_RECORDED_POINT_ID7,
                                           DAC_DATA_ID_FIRST_RECORDED_POINT_ID8,
                                           DAC_DATA_ID_FIRST_RECORDED_POINT_ID9,
                                           DAC_DATA_ID_FIRST_RECORDED_POINT_ID10,}, 

                                         { DAC_DATA_ID_SECOND_RECORDED_POINT_ID1,
                                           DAC_DATA_ID_SECOND_RECORDED_POINT_ID2,
                                           DAC_DATA_ID_SECOND_RECORDED_POINT_ID3,
                                           DAC_DATA_ID_SECOND_RECORDED_POINT_ID4,
                                           DAC_DATA_ID_SECOND_RECORDED_POINT_ID5,
                                           DAC_DATA_ID_SECOND_RECORDED_POINT_ID6,
                                           DAC_DATA_ID_SECOND_RECORDED_POINT_ID7,
                                           DAC_DATA_ID_SECOND_RECORDED_POINT_ID8,
                                           DAC_DATA_ID_SECOND_RECORDED_POINT_ID9,
                                           DAC_DATA_ID_SECOND_RECORDED_POINT_ID10,}};

    unsigned char* p = cmd_buf_tmp->buf;
    //录波数据
    if (cmd_buf_tmp->cmd->cmd_type == 0x61 || cmd_buf_tmp->cmd->cmd_type == 0x60)
    {
        record_idx = cmd_buf_tmp->cmd->cmd_type == 0x60 ? 0 : 1;
        record_num = cmd_buf_tmp->cmd->cmd_type == 0x60 ? 1 : 2;
    }

    for(; record_idx < record_num; record_idx++)
    {
        get_one_data(DAC_DATA_ID_RECORDED_NUM_BEFORE_ERROR + record_idx, &data_u);
        put_uint16_to_buff(p, data_u);
        p += ARRAY_SIZE_2;
        get_one_data(DAC_DATA_ID_RECORDED_TIME_INTERVAL_BEFORE_ERROR + record_idx, &data_i);
        put_uint32_to_buff(p, data_i);
        p += ARRAY_SIZE_4;
        get_one_data(DAC_DATA_ID_RECORDED_NUM_AFTER_ERROR + record_idx, &data_u);
        put_uint16_to_buff(p, data_u);
        p += ARRAY_SIZE_2;
        get_one_data(DAC_DATA_ID_RECORDED_TIME_INTERVAL_AFTER_ERROR + record_idx, &data_i);
        put_uint32_to_buff(p, data_i);
        p += ARRAY_SIZE_4;
        get_one_data(DAC_DATA_ID_RECORDED_POINT_NUM + record_idx, &point_num);
        put_uint16_to_buff(p, point_num);
        p += ARRAY_SIZE_2;

        int point_id_index = point_num / MAX_ID_INDEX;
        for(i = 0; i < point_id_index; i++)
        {   
            for(int index = 0; index < MAX_ID_INDEX; index++)
            {

                get_one_data(array_point_id[record_idx][i] + index, &data_u);
                put_uint16_to_buff(p, data_u);
                p += ARRAY_SIZE_2;
            }
        }

        for (int k = 0; k < point_num % MAX_ID_INDEX; k++)
        {
            get_one_data(array_point_id[record_idx][point_id_index] + k, &data_u);
            put_uint16_to_buff(p, data_u);
            p += ARRAY_SIZE_2;
        }
    }
    cmd_buf_tmp->data_len = p - cmd_buf_tmp->buf;
    return SUCCESSFUL;
}

int parse_set_fault_record1_para(void* dev_inst, void *cmd_buf)
{   
    int ret = 0;
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf != NULL, FAILURE);
    cmd_buf_t* cmd_buf_tmp = ((cmd_buf_t *)cmd_buf);
    unsigned int data_i = 0;
    unsigned short data_s = 0;
    unsigned short point_num = 0;
    int i = 0;

    unsigned int array_point_id[10] = {DAC_PARA_ID_FIRST_RECORDED_POINT_ID1_OFFSET,
                                       DAC_PARA_ID_FIRST_RECORDED_POINT_ID2_OFFSET,
                                       DAC_PARA_ID_FIRST_RECORDED_POINT_ID3_OFFSET,
                                       DAC_PARA_ID_FIRST_RECORDED_POINT_ID4_OFFSET,
                                       DAC_PARA_ID_FIRST_RECORDED_POINT_ID5_OFFSET,
                                       DAC_PARA_ID_FIRST_RECORDED_POINT_ID6_OFFSET,
                                       DAC_PARA_ID_FIRST_RECORDED_POINT_ID7_OFFSET,
                                       DAC_PARA_ID_FIRST_RECORDED_POINT_ID8_OFFSET,
                                       DAC_PARA_ID_FIRST_RECORDED_POINT_ID9_OFFSET,
                                       DAC_PARA_ID_FIRST_RECORDED_POINT_ID10_OFFSET,};

    unsigned char* p = cmd_buf_tmp->buf;
    p++;//
    data_s = get_uint16_data(p);
    ret = set_one_para(DAC_PARA_ID_RECORDED_NUM_BEFORE_ERROR_OFFSET, &data_s, FALSE, FALSE);
    p += ARRAY_SIZE_2;

    data_i = get_ulong_data(p);
    ret |= set_one_para(DAC_PARA_ID_RECORDED_TIME_INTERVAL_BEFORE_ERROR_OFFSET, &data_i, FALSE, FALSE);
    p += ARRAY_SIZE_4;

    data_s = get_uint16_data(p);
    ret |= set_one_para(DAC_PARA_ID_RECORDED_NUM_AFTER_ERROR_OFFSET, &data_s, FALSE, FALSE);
    p += ARRAY_SIZE_2;

    data_i = get_ulong_data(p);
    ret |= set_one_para(DAC_PARA_ID_RECORDED_TIME_INTERVAL_AFTER_ERROR_OFFSET, &data_i, FALSE, FALSE);
    p += ARRAY_SIZE_4;

    point_num = get_uint16_data(p);
    if(point_num > MAX_OBSER_NUM)
    {   
        cmd_buf_tmp->rtn = RTN_INVALID_DATA;
        return FAILURE;
    }
    ret |= set_one_para(DAC_PARA_ID_RECORDED_POINT_NUM_OFFSET, &point_num, FALSE, TRUE);
    p += ARRAY_SIZE_2;

    int point_id_index = point_num / MAX_ID_INDEX;
    for(i = 0; i < point_id_index; i++)
    {
        for(int index = 0; index < MAX_ID_INDEX; index++)
        {
            data_s = get_uint16_data(p);
            ret |= set_one_para(array_point_id[i] + index, &data_s, FALSE, FALSE);
            p += ARRAY_SIZE_2;
        }
    }
    for (int k = 0; k < point_num % MAX_ID_INDEX; k++)
    {
        data_s = get_uint16_data(p);
        ret |= set_one_para(array_point_id[point_id_index] + k, &data_s, FALSE, FALSE);
        p += ARRAY_SIZE_2;
    }  

    save_all_para();
    if(ret < 0)
    {
        cmd_buf_tmp->rtn = RTN_INVALID_DATA;
        return FAILURE;
    }
    return SUCCESSFUL;
}

int parse_set_fault_record2_para(void* dev_inst, void *cmd_buf)
{   
    int ret = 0;
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf != NULL, FAILURE);
    cmd_buf_t* cmd_buf_tmp = ((cmd_buf_t *)cmd_buf);
    unsigned int data_i = 0;
    unsigned short data_s = 0;
    unsigned short point_num = 0;
    int i = 0;
    unsigned int array_point_id[10] = {DAC_PARA_ID_SECOND_RECORDED_POINT_ID1_OFFSET,
                                       DAC_PARA_ID_SECOND_RECORDED_POINT_ID2_OFFSET,
                                       DAC_PARA_ID_SECOND_RECORDED_POINT_ID3_OFFSET,
                                       DAC_PARA_ID_SECOND_RECORDED_POINT_ID4_OFFSET,
                                       DAC_PARA_ID_SECOND_RECORDED_POINT_ID5_OFFSET,
                                       DAC_PARA_ID_SECOND_RECORDED_POINT_ID6_OFFSET,
                                       DAC_PARA_ID_SECOND_RECORDED_POINT_ID7_OFFSET,
                                       DAC_PARA_ID_SECOND_RECORDED_POINT_ID8_OFFSET,
                                       DAC_PARA_ID_SECOND_RECORDED_POINT_ID9_OFFSET,
                                       DAC_PARA_ID_SECOND_RECORDED_POINT_ID10_OFFSET,};

    unsigned char* p = cmd_buf_tmp->buf;
    p++;//
    data_s = get_uint16_data(p);
    ret |= set_one_para(DAC_PARA_ID_RECORDED_NUM_BEFORE_ERROR_OFFSET+1, &data_s, FALSE, FALSE);
    p += ARRAY_SIZE_2;

    data_i = get_ulong_data(p);
    ret |= set_one_para(DAC_PARA_ID_RECORDED_TIME_INTERVAL_BEFORE_ERROR_OFFSET+1, &data_i, FALSE, FALSE);
    p += ARRAY_SIZE_4;

    data_s = get_uint16_data(p);
    ret |= set_one_para(DAC_PARA_ID_RECORDED_NUM_AFTER_ERROR_OFFSET+1, &data_s, FALSE, FALSE);
    p += ARRAY_SIZE_2;

    data_i = get_ulong_data(p);
    ret |= set_one_para(DAC_PARA_ID_RECORDED_TIME_INTERVAL_AFTER_ERROR_OFFSET+1, &data_i, FALSE, FALSE);
    p += ARRAY_SIZE_4;

    point_num = get_uint16_data(p);
    if(point_num > MAX_OBSER_NUM)
    {   
        cmd_buf_tmp->rtn = RTN_INVALID_DATA;
        return FAILURE;
    }
    ret |= set_one_para(DAC_PARA_ID_RECORDED_POINT_NUM_OFFSET+1, &point_num, FALSE, TRUE);
    p += ARRAY_SIZE_2;

    int point_id_index = point_num / MAX_ID_INDEX;
    for(i = 0; i < point_id_index; i++)
    {
        for(int index = 0; index < MAX_ID_INDEX; index++)
        {
            data_s = get_uint16_data(p);
            ret |= set_one_para(array_point_id[i] + index, &data_s, FALSE, FALSE);
            p += ARRAY_SIZE_2;
        }
    }
    for (int k = 0; k < point_num % MAX_ID_INDEX; k++)
    {
        data_s = get_uint16_data(p);
        ret |= set_one_para(array_point_id[point_id_index] + k, &data_s, FALSE, FALSE);
        p += ARRAY_SIZE_2;
    }  

    save_all_para();

    if(ret < 0)
    {
        cmd_buf_tmp->rtn = RTN_INVALID_DATA;
        return FAILURE;
    }

    return SUCCESSFUL;
}

static cmd_t* get_matched_cmd_by_id(unsigned short cmd_id, cmd_t* cmd_tab)
{
    cmd_t* cmd = NULL;

    cmd = cmd_tab;
    while (cmd != NULL && cmd->cmd_id != 0) {
        
        if(cmd->cmd_id == cmd_id)
        {
            return cmd;
        }
        cmd++;
    }
    return NULL;
}


dev_type_t* init_dev_inverter_1363_main(void)
{
    return &dev_inverter_s1363_main;
}

dev_type_t* init_dev_inverter_1363_IP(void)
{
    return &dev_inverter_s1363_IP;
}

int send_update_alarm_para_msg(void* dev_inst, void *cmd_buf)
{
    send_msg_to_thread(PARA_CHANGE_MSG_ID, MOD_ALARM_MANAGE, NULL, 0);
    return SUCCESSFUL;
}


void s1363_register_cmd_table()
{
    for(int i = 0; i < sizeof(s_cmd_handle)/sizeof(s_cmd_handle[0]); i++) 
    {
        register_cmd_handle(&s_cmd_handle[i]);
    }

}

int init_parse_layer_param(void* dev_inst, void* cmd_buff, parse_layer_param_t* parse_layer_param, sid_list_info_t* sid_list_info)
{
    parse_layer_param->array_offset = 0;
    parse_layer_param->dev_addr = ((dev_inst_t *)dev_inst)->dev_addr;
    parse_layer_param->data = ((cmd_buf_t *)cmd_buff)->buf;
    parse_layer_param->data_len = ((cmd_buf_t *)cmd_buff)->data_len;
    //TODO:parse_layer_param解析层参数在协议中初始化不合理，待处理
    parse_layer_param->set_data = unified_set_data;
    parse_layer_param->get_data = unified_get_data;
    parse_layer_param->linear_search = linear_search_id_index;
    sid_list_info->sid_list = g_sid_list;
    sid_list_info->sid_num = 0;
    rt_memset(g_sid_list, 0x00, sizeof(g_sid_list));
    parse_layer_param->sid_list_info = sid_list_info;
    parse_layer_param->para_and_realdata_tab = para_and_realdata_tab;
    parse_layer_param->para_len = sizeof(para_and_realdata_tab) / sizeof(map_para_to_realdata_t);
    return SUCCESSFUL;

}



cmd_id_map_t* find_set_para_map(unsigned short cmd_id) {
    int parse_map_size = sizeof(g_parse_func_map) / sizeof(cmd_func_map_t);
    for (int j = 0; j < parse_map_size; j++) {
        if (g_parse_func_map[j].cmd_id == cmd_id) {
            return g_parse_func_map[j].para_map;
        }
    }
    return NULL;
}

cmd_t* find_matched_cmd(cmd_id_map_t* set_para_map, unsigned char cmd_type, int* south_cmd_id, unsigned int* set_para_map_index) {
    int i = 0;
    while (set_para_map[i].cmd_id != 0) {
        if (set_para_map[i].cmd_type == cmd_type) {
            *south_cmd_id = set_para_map[i].south_cmd_id;
            *set_para_map_index = i;
            return get_matched_cmd_by_id(set_para_map[i].cmd_id, no_poll_cmd_tab);
        }
        i++;
    }
    return NULL;
}

void update_cmd_buffer(cmd_buf_t* cmd_buf_tmp, cmd_t* cmt_tmp) {
    cmd_buf_tmp->cmd->cmd_data_parse_id_ver = cmt_tmp->cmd_data_parse_id_ver;
    cmd_buf_tmp->cmd->data_parse = cmt_tmp->data_parse;
    cmd_buf_tmp->cmd->data_pack = cmt_tmp->data_pack;
}

void handle_south_cmd_id(unsigned short south_cmd_id, sid_list_info_t* sid_list_info) {
    if ((DC_AC_SET_PRO_PARA_DATA == south_cmd_id) || 
        (DC_AC_SET_FAULT_RECORD1_PARA == south_cmd_id) || 
        (DC_AC_SET_FAULT_RECORD2_PARA == south_cmd_id)) {
        send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, south_cmd_id);
    } else if (0 != south_cmd_id) {
        g_rtn = RTN_SUCCESSFUL;
        if(send_set_para_cmd_msg(sid_list_info->sid_list, sid_list_info->sid_num) != SUCCESSFUL)
        {
            g_rtn = RTN_WRONG_COMMAND;
        }
    }
}


int remote_ctrl_para_parse(dev_inst_t* dev_inst, cmd_buf_t* cmd_buff, sid_list_info_t* sid_list_info)
{   
    // 开机，需要判断关机原因
    int ret = 0;
    unsigned short unshort = 0;
    unsigned short power_on_off = 0;
    unsigned char* buf = NULL;
    power_off_reason_t* power_off_reason = NULL;
    
    power_off_reason = get_power_off_reason();

    // 开关机
    buf = cmd_buff->buf;
    buf++;

    power_on_off = get_uint16_data(buf);
    buf += 2;
    
    LOG_E("%s | %d power_on_off: %d emergy: %d  time:%d  normal:%d\n",__FUNCTION__ , __LINE__, power_on_off, power_off_reason->emergy_off, power_off_reason->time_off, power_off_reason->normal_off);
    if(power_on_off == 1)
    {
        // 开机处理，如果是非正常关机，则开机失败
        if(power_off_reason->emergy_off == TRUE)
        {
            cmd_buff->rtn = RTN_POWER_ON_ESD_FAIL;
            return FAILURE;
        }

        if(power_off_reason->time_off == TRUE)
        {
            cmd_buff->rtn = RTN_POWER_ON_TIME_FAIL;
            return FAILURE;
        }

        power_off_reason->normal_off = FALSE;
    }
    else
    {
        power_off_reason->normal_off = TRUE;
    }

    ret |= set_one_para(DAC_PARA_ID_POWER_ON_OFF_OFFSET, &power_on_off, FALSE, TRUE);

    unshort = get_uint16_data(buf);
    ret |= set_one_para(DAC_PARA_ID_PHASE_SEQ_ADAPTIVE_ENABLE_OFFSET, &unshort, FALSE, TRUE);
    buf += 2;

    unshort = get_uint16_data(buf);
    ret |= set_one_para(DAC_PARA_ID_MPPT_CONNECT_TYPE_OFFSET, &unshort, FALSE, TRUE);
    buf += 2;

    unshort = get_uint16_data(buf);
    ret |= set_one_para(DAC_PARA_ID_EN_LEAK_CURR_SELF_CHECK_OFFSET, &unshort, FALSE, TRUE);
    buf += 2;

    unshort = get_uint16_data(buf);
    ret |= set_one_para(DAC_PARA_ID_RCD_BOOST_ENABLE_OFFSET, &unshort, FALSE, TRUE);
    buf += 2;
    
    unshort = get_uint16_data(buf);
    ret |= set_one_para(DAC_PARA_ID_WEAK_GRID_ENABLE_OFFSET, &unshort, FALSE, TRUE);
    buf += 2;
    
    unshort = get_uint16_data(buf);
    ret |= set_one_para(DAC_PARA_ID_WATER_INGRESS_ENABLE_OFFSET, &unshort, FALSE, TRUE);
    buf += 2;
    
    sid_list_info->sid_list[0] = DAC_PARA_ID_POWER_ON_OFF_OFFSET;
    sid_list_info->sid_list[1] = DAC_PARA_ID_PHASE_SEQ_ADAPTIVE_ENABLE_OFFSET;
    sid_list_info->sid_list[2] = DAC_PARA_ID_MPPT_CONNECT_TYPE_OFFSET;
    sid_list_info->sid_list[3] = DAC_PARA_ID_EN_LEAK_CURR_SELF_CHECK_OFFSET;
    sid_list_info->sid_list[4] = DAC_PARA_ID_RCD_BOOST_ENABLE_OFFSET;
    sid_list_info->sid_list[5] = DAC_PARA_ID_WEAK_GRID_ENABLE_OFFSET;
    sid_list_info->sid_list[6] = DAC_PARA_ID_WATER_INGRESS_ENABLE_OFFSET;
    sid_list_info->sid_num = 7;

    if(ret < 0)
    {
        cmd_buff->rtn = RTN_INVALID_DATA;
        return FAILURE;
    }

    return SUCCESSFUL;
}

int comm_prase_set_para(void* dev_inst, void* cmd_buff) {
    int ret = 0;
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    parse_layer_param_t parse_layer_param = {0};
    sid_list_info_t sid_list_info = {0};
    cmd_buf_t* cmd_buf_tmp = ((cmd_buf_t *)cmd_buff);
    unsigned short cmd_id = cmd_buf_tmp->cmd->cmd_id;
    unsigned char cmd_type = cmd_buf_tmp->buf[0];  
    unsigned int set_para_map_index = 0;

    cmd_t* cmt_tmp = NULL;
    int south_cmd_id = 0;
    cmd_id_map_t* set_para_map = find_set_para_map(cmd_id);

    if (set_para_map == NULL) {
        cmd_buf_tmp->rtn = RTN_INVALID_DATA;
        return SUCCESSFUL;
    }

    cmt_tmp = find_matched_cmd(set_para_map, cmd_type, &south_cmd_id, &set_para_map_index);
    if (cmt_tmp == NULL) {
        cmd_buf_tmp->rtn = RTN_INVALID_DATA;
        return SUCCESSFUL;
    }

    update_cmd_buffer(cmd_buf_tmp, cmt_tmp);

    init_parse_layer_param(dev_inst, cmd_buff, &parse_layer_param, &sid_list_info);

    // 开关机特殊处理
    if(south_cmd_id == DC_AC_SET_REMOTE_CTRL_PARA_DATA)
    {
        ret = remote_ctrl_para_parse((dev_inst_t*)dev_inst, (cmd_buf_t*)cmd_buff, &sid_list_info);
    }
    else
    {
        ret = parse_set_para_fun(dev_inst, cmd_buff, &parse_layer_param);
    }

    cmd_buf_tmp->cmd->cmd_data_parse_id_ver = NULL;
    cmd_buf_tmp->cmd->data_parse = comm_prase_set_para;
    if(cmd_buf_tmp->cmd->data_pack == NULL)
    {
        cmd_buf_tmp->cmd->data_pack = comm_pack_set_para;
    }
    RETURN_VAL_IF_FAIL(SUCCESSFUL == ret, FAILURE);

    handle_south_cmd_id(south_cmd_id, &sid_list_info);

    backup_power_ctrl_para(south_cmd_id);

    if (set_para_map[set_para_map_index].func != NULL) {
        set_para_map[set_para_map_index].func();
    }

    save_all_para();
    if (cmd_id == SET_PRODUCE_PARA_DATA || cmt_tmp->cmd_id == DAC_SET_MONITER_PARA3)
    {
        send_msg_to_thread(SAVE_FAC_DATA, MOD_SYS_MANAGE, NULL, 0);
    }
    if (cmt_tmp->cmd_id == SET_SYSPARA_DATA12)
    {
        send_msg_to_thread(GRID_CODE_CHANGE, MOD_SYS_MANAGE, NULL, 0);
    }
    send_msg_to_thread(GRID_CODE_STATUS_CHECK, MOD_SYS_MANAGE, NULL, 0);
    cmd_buf_tmp->rtn = g_rtn;
    return SUCCESSFUL;
}


int comm_pack_get_para(void* dev_inst, void* cmd_buff)
{   
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    cmd_buf_t* cmd_buf_tmp = ((cmd_buf_t *)cmd_buff);
    unsigned short cmd_id = cmd_buf_tmp->cmd->cmd_id;
    unsigned char cmd_type = cmd_buf_tmp->cmd->cmd_type;
    cmd_id_map_t* get_para_map = NULL;
    cmd_t* cmt_tmp = NULL;
    int i = 0, j = 0, ret = 0;

    int pack_map_size = sizeof(g_pack_func_map)/sizeof(cmd_func_map_t);

    for(j =0; j< pack_map_size ;j++)
    {
        if(g_pack_func_map[j].cmd_id == cmd_id )
        {
            get_para_map = g_pack_func_map[j].para_map;
            break;
        }
    }
    if( get_para_map == NULL )
    {
        return SUCCESSFUL;
    }
    while(get_para_map[i].cmd_id != 0)
    {
        if(get_para_map[i].cmd_type == cmd_type )
        {   
            cmt_tmp = get_matched_cmd_by_id(get_para_map[i].cmd_id, no_poll_cmd_tab );
            break;
        }
        i++;
    }
    if(cmt_tmp == NULL)
    {
        return SUCCESSFUL;
    }

    cmd_buf_tmp->cmd->cmd_data_pack_id_ver = cmt_tmp->cmd_data_pack_id_ver;
    cmd_buf_tmp->cmd->data_pack = cmt_tmp->data_pack;
    ret = parse_layer_send_id_version(dev_inst, cmd_buff);
    
    cmd_buf_tmp->cmd->cmd_data_pack_id_ver = NULL;
    cmd_buf_tmp->cmd->data_pack = comm_pack_get_para;
    return ret;
}

int comm_pack_set_para(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    g_rtn = RTN_SUCCESSFUL;
    return SUCCESSFUL;
}

int parse_test_record_cmd(void* dev_inst, void* cmd_buff)
{

    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    cmd_buf_t *cmd_buf_tmp = ((cmd_buf_t *)cmd_buff);
    unsigned char cmd_type = cmd_buf_tmp->buf[0];
    unsigned short save_num = get_uint16_data(&cmd_buf_tmp->buf[1]);

    switch (cmd_type) {
        case TEST_HIS_ALM :
            send_msg_to_thread(HIS_ALM_TEST, MOD_SYS_MANAGE, &save_num, 2);             //发送生成历史告警消息
            break;
        case TEST_HIS_DATA :
            send_msg_to_thread(HIS_DATA_TEST, MOD_SYS_MANAGE, &save_num, 2);            //发送生成历史数据消息
            break;
        case TEST_OPERATE_RECORD :
            send_msg_to_thread(OPERATE_RECORD_TEST, MOD_SYS_MANAGE, &save_num, 2);      //发送生成操作记录消息
            break;
        case TEST_ENERGY_DATA :
            send_msg_to_thread(ENERGY_DATA_TEST, MOD_SYS_MANAGE, NULL, 0);      //发送生成近25年的电量统计记录消息
            break;   

        case POWER_TRANS_ENABLE:
            set_one_para(DAC_PARA_ID_POWER_TRANS_ENABLE_OFFSET, &cmd_buf_tmp->buf[1], TRUE, TRUE);
            break;

        default:
            parse_exe_debug_cmd(dev_inst, cmd_buff);
            break;
    }
    return SUCCESSFUL;

}

int parse_control_cmd(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    cmd_buf_t *cmd_buf_tmp = ((cmd_buf_t *)cmd_buff);
    unsigned char cmd_type = cmd_buf_tmp->buf[0];
    int loop = 0;
    g_rtn = 0;
    for(loop = 0; loop < sizeof(s_ctrl_cmd) / sizeof(ctrl_cmd_1363_t); loop ++)
    {
        if(cmd_type != s_ctrl_cmd[loop].cmd_type)
        {
            continue;
        }

        if(s_ctrl_cmd[loop].ctrl_id != DAC_CTRL_ID_RESTORE_FACTORY)
        {
            set_one_data(s_ctrl_cmd[loop].ctrl_id, &s_ctrl_cmd[loop].ctrl_status);
            save_real_ctrl_cmd_event(s_ctrl_cmd[loop].ctrl_id, CTRL_ID_TYPE, s_ctrl_cmd[loop].info);//保存操作记录
        }

        if(s_ctrl_cmd[loop].is_south_ctrl_cmd == TRUE)
        {
            send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, s_ctrl_cmd[loop].ctrl_cmd_id);
        }

        if(s_ctrl_cmd[loop].func != NULL)
        {
            s_ctrl_cmd[loop].func();
        }
        break;
    }
    return SUCCESSFUL;
}

void start_iv_func()
{
    start_iv_callback();
    rt_sem_t rtn_sem = get_rtn_sem();
    unsigned char ctrl_rtn = RTN_FAILURE;
    if(RT_EOK == rt_sem_take(rtn_sem, 3000))
    {
        ctrl_rtn = get_rtn_south_to_north_flag();
    }
    if(ctrl_rtn != RTN_SUCCEFUL)
    {
        LOG_E("%s:%d|Start IV Failed!", __FUNCTION__ , __LINE__); 
        g_rtn = RTN_CTRL_FAIL;
    }
    return;
}

void restart_system()
{
    if(get_restart_south_rtn() != SUCCESSFUL)
    {
        g_rtn = RTN_CTRL_FAIL;
    }
    return;
}

void get_afci_file()
{
    if(get_afci_file_handle() != SUCCESSFUL)
    {
        g_rtn = RTN_CTRL_FAIL;
    }

    return;
}

void clean_afci_file()
{
    rt_sem_t rtn_sem = get_rtn_sem();
    unsigned char ctrl_rtn = RTN_FAILURE;
    unsigned char afci_sta = 0;
    if(RT_EOK == rt_sem_take(rtn_sem, 3000))
    {
        ctrl_rtn = get_rtn_south_to_north_flag();
    }
    if(ctrl_rtn != RTN_SUCCEFUL)
    {
        LOG_E("%s:%d|Clean AFCI FILE Failed!", __FUNCTION__ , __LINE__); 
        g_rtn = RTN_CTRL_FAIL;
    }
    else
    {
        send_msg_to_thread(CLEAN_AFCI_FILE_MSG, MOD_SYS_MANAGE, NULL, 0);
        set_one_data(DAC_DATA_ID_AFCI_FILE_STATUS, &afci_sta); // 清除AFCI文件状态
    }
    return;
}

int pack_control_cmd(void* dev_inst, void* cmd_buf)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf != NULL, FAILURE);
    cmd_buf_t* cmd_buf_tmp = ((cmd_buf_t *)cmd_buf);
    if(cmd_buf_tmp->rtn == 0)
    {
        cmd_buf_tmp->rtn = g_rtn; 
    }
    return SUCCESSFUL;
}

void start_shield_can_comm() 
{
    send_msg_to_sps_cmd(STOP_DEV_POLL_MSG, MOD_DC_AC, 1, 0);
    send_msg_to_thread(CTRL_SHIELD_CAN_COMM, MOD_DC_AC, NULL, 0);//发消息到南向
    return ;
}

void stop_shield_can_comm() 
{
    send_msg_to_thread(CTRL_SHIELD_CAN_COMM, MOD_DC_AC, NULL, 0);//发消息到南向将can屏蔽打开后启动轮询命令
    send_msg_to_sps_cmd(START_DEV_POLL_MSG, MOD_DC_AC, 1, 1 );
    return ;
}

int parse_test_para(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    cmd_buf_t* cmd_buf_tmp = ((cmd_buf_t *)cmd_buff);
    cmd_buf_tmp->cmd->cmd_type = cmd_buf_tmp->buf[0];
    send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, DC_AC_GET_DRIVE_TEST_RELATED_DATA );
    rt_thread_mdelay(500);
    send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, DC_AC_GET_ADC_TEST_RELATED_DATA);
    rt_thread_mdelay(500);
    send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, DC_AC_GET_RD_TEST_RELATED_DATA);
    rt_thread_mdelay(500);
    send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, DC_AC_GET_PRODUCTION_TEST_DATA);
    rt_thread_mdelay(500);

    return SUCCESSFUL;
}

int parse_com_group(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    cmd_buf_t* cmd_buf_tmp = ((cmd_buf_t *)cmd_buff);
    cmd_buf_tmp->cmd->cmd_type = cmd_buf_tmp->buf[0];
    //校正，生产相关参数不能等待check时间，需要主动获取，以防南向参数变更了
    // send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, DC_AC_GET_ADG_PARA_DATA );
    // rt_thread_mdelay(500);
    // send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, DC_AC_GET_PRO_PARA_DATA);
    // rt_thread_mdelay(500);

    return SUCCESSFUL;
}

int pack_sys_time_data(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    time_base_t  pt_time={0};
    int offset=0;
    get_time(&pt_time);

    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;

    put_int16_to_buff(&data_buff[offset],pt_time.year);
    offset+=2;

    data_buff[offset++] = pt_time.month;
    data_buff[offset++] = pt_time.day;
    data_buff[offset++] = pt_time.hour;
    data_buff[offset++] = pt_time.minute;
    data_buff[offset++] = pt_time.second;

    ((cmd_buf_t*)cmd_buff)->data_len=offset;

    return SUCCESSFUL;
}

int parse_sys_time_data(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    int offset = 1;
    time_base_t tm = {0};

    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;

    tm.year = get_int16_data(&data_buff[offset]);
    offset+=2;
    tm.month  = data_buff[offset++];
    tm.day = data_buff[offset++];
    tm.hour = data_buff[offset++];
    tm.minute  = data_buff[offset++];
    tm.second  = data_buff[offset++];
    if( check_time_range(tm) == FAILURE )
    {
        LOG_E("%s | %d | set RTC time failed \n", __FUNCTION__ , __LINE__);
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA ;
        return FAILURE;
    }
    time_t tTime = timestruct_to_time_t(&tm);
    if(tTime < 0)
    {
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA ;
        return FAILURE;
    }
    if(stime(&tTime) == FAILURE )
    {
        LOG_E("%s | %d | set RTC time failed \n", __FUNCTION__ , __LINE__);
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA ;
        return FAILURE;
    }
    set_one_para(DAC_PARA_ID_SYS_TIME_YEAR_OFFSET ,  &tm.year   , TRUE, TRUE);
    set_one_para(DAC_PARA_ID_SYS_TIME_MONTH_OFFSET , &tm.month  , TRUE, TRUE);
    set_one_para(DAC_PARA_ID_SYS_TIME_DAY_OFFSET ,   &tm.day    , TRUE, TRUE);
    set_one_para(DAC_PARA_ID_SYS_TIME_HOUR_OFFSET ,  &tm.hour   , TRUE, TRUE);
    set_one_para(DAC_PARA_ID_SYS_TIME_MINUTE_OFFSET, &tm.minute , TRUE, TRUE);
    set_one_para(DAC_PARA_ID_SYS_TIME_SECOND_OFFSET, &tm.second , TRUE, TRUE);
    return SUCCESSFUL;
}

short get_run_status()
{   
    signed short result = 1;
    unsigned char data = 0;
    dac_dig_map_t sid_map[]={

        // { DAC_DATA_ID_SPOT_CHECK, SPOT_CHECK },//点检
        // { DAC_DATA_ID_SHUTDOWN, SHUTDOWN },//关机
        // { DAC_DATA_ID_POWER_SETTING_SHUTDOWN, SET_STOP }, //功率设定停机
        // { DAC_DATA_ID_FAULT_SHUTDOWN, FAULT_STOP }, //故障停机
        // { DAC_DATA_ID_NORMAL_SHUTDOWN, NORMAL_STOP }, //正常停机
        // //{ DAC_DATA_ID_GRID_CONNECT_DERATING_BY_PV, CONN_DE_IN },  //并网降额（逆变器内部原因导致）
        // { DAC_DATA_ID_GRID_CONNECT_DERATING_BY_POWER, CONN_DE_POWER }, //并网降额（功率设定）
        // //{ DAC_DATA_ID_NORMAL_GRID_CONNECT, CONN_NORMAL }, //并网正常
        // { DAC_DATA_ID_GRID_CONNECT, GRID_CONN }, //并网
        // { DAC_DATA_ID_STANDBY, STANDBY }, //待机
    };
    for(int i = 0;i < sizeof(sid_map)/sizeof(dac_dig_map_t) ;i++)
    {
        get_one_data( sid_map[i].sid , &data);
        if( data == 1 )
        {
            result |= sid_map[i].map_value;
        }
        else
        {
            result |= 0;
        }
        data = 0;
    }
    return result;
}

// 获取数字量
int get_dig_data_parse(void* dev_inst, void* cmd_buf) 
{   
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf != NULL, FAILURE);
    return SUCCESSFUL;
}

void app_count_timeout()
{
    s_lock_count = 0;
    rt_timer_stop(g_app_count_timer);
}

void app_lock_timeout()
{
    g_self_lock = 0;
    rt_timer_stop(g_app_lock_timer);
}

void app_chg_passwd_timeout()
{
    static char s_chg_cnt = 0;
    if(s_chg_cnt >= 6)
    {
        g_passwd_overdue_flag = TRUE;
        rt_timer_stop(g_app_chg_passwd_timer);
        s_chg_cnt = 0;
    }
    s_chg_cnt ++;
}

int set_app_init_load_flag(unsigned char flag)
{
    g_restore_app_paswrd_flag = flag;   
    return SUCCESSFUL;
}

void create_app_timer()
{
    static int s_init_flag = TRUE;
    if(s_init_flag)
    {
        g_app_count_timer = rt_timer_create("app_count_timer", app_count_timeout, NULL, APP_COUNT_PERIOD, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
        if(g_app_count_timer == NULL)
        {
            LOG_E("g_app_count_timer creat fail\n");
            return;
        }
        g_app_lock_timer = rt_timer_create("app_lock_timer", app_lock_timeout, NULL, APP_LOCK_PERIOD, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
        if(g_app_lock_timer == NULL)
        {
            LOG_E("g_app_lock_timer creat fail\n");
            return;
        }
        g_app_chg_passwd_timer = rt_timer_create("app_chg_passwd_timer", app_chg_passwd_timeout, NULL, APP_OVERDUE_PERIOD, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
        if(g_app_chg_passwd_timer == NULL)
        {
            LOG_E("app_chg_passwd_timer creat fail\n");
            return;
        }
        rt_timer_start(g_app_chg_passwd_timer);
        s_init_flag = FALSE;
    }

}

void judge_device_lock(unsigned char* rtn)
{
    if(s_lock_count == 0)
    {
        //启动5分钟定时器
        rt_timer_start(g_app_count_timer);
    }
    s_lock_count ++;
    
    if(s_lock_count == APP_LOAD_FAIL_TIMES)
    {
        rt_timer_start(g_app_lock_timer);
        g_time = time(NULL);
        *rtn = RTN_DEVICE_LOCK;
    }
}

int write_app_event(unsigned short para_id_offset, char* info)
{
    event_record_t save_event_info = {};
    unsigned short event_id = 0;
    rt_memset(&save_event_info, 0, sizeof(save_event_info));
    save_event_info.type = 0x02;    // 参数
    if(para_id_offset == DAC_PARA_ID_APP_COMMON_CIPHERTEXT_OFFSET)
    {
        event_id = DAC_PARA_ID_APP_COMMON_CIPHERTEXT;
    }
    else
    {
        event_id = DAC_PARA_ID_APP_ADMIN_CIPHERTEXT;
    }
    rt_memcpy_s(save_event_info.info, MAX_EVENT_INFO_LEN, info, rt_strnlen_s(info, MAX_EVENT_INFO_LEN));  // info 内容
    put_int16_to_buff((unsigned char*)&save_event_info.event_id ,event_id);       // event id 
    send_msg_to_thread(SAVE_EVENT_MSG, MOD_SYS_MANAGE, &save_event_info, sizeof(save_event_info));
    return SUCCESSFUL;
}



void handle_successful_login(unsigned short para_offset_id, char is_set, unsigned char *new_password) {
    if (TRUE == is_set) {
        // 修改密码
        set_one_para(para_offset_id, new_password, TRUE, TRUE);
    } else {
        // 登录
        write_app_event(para_offset_id, "load success");
    }
    g_app_rtn = RTN_SUCCESSFUL;
}

void handle_password_error(unsigned short para_offset_id) {
    write_app_event(para_offset_id, "password error");
    g_app_rtn = RTN_PASSWORD_ERR;  // 密文校验失败
}

int is_password_correct(unsigned char *old_ciphertext, unsigned char *ciphertext, unsigned char *fact_ciphert) {
    return (memcmp(old_ciphertext, ciphertext, 45) == 0) || 
           (g_restore_app_paswrd_flag && fact_ciphert && (memcmp(old_ciphertext, fact_ciphert, 45) == 0));
}

//APP登录 
void judge_password_with_app(unsigned short para_offset_id, unsigned char *old_ciphertext, unsigned char *new_password, char is_set, unsigned char *fact_ciphert) {
    unsigned char ciphertext[64] = {};
    event_record_t save_event_info = {};

    rt_memset(&save_event_info, 0, sizeof(save_event_info));
    rt_memset(ciphertext, 0, 64);
    get_one_para(para_offset_id, ciphertext);

    if (is_password_correct(old_ciphertext, ciphertext, fact_ciphert)) {
        handle_successful_login(para_offset_id, is_set, new_password);
    } else {
        handle_password_error(para_offset_id);
    }
}




void handle_common_name(const unsigned char *temp_ciphertext) {
    unsigned char fact_ciphert[64] = {};
    unsigned short para_id_offset = DAC_PARA_ID_APP_COMMON_CIPHERTEXT_OFFSET;
    is_common_name = TRUE;
    if (process_user_auth_time() != SUCCESSFUL) {
        g_app_rtn = RTN_ACCOUNT_EXPIRED;
        return;
    }
    get_special_para_fact_value(para_id_offset, (char *)fact_ciphert);
    judge_password_with_app(para_id_offset, (unsigned char *)temp_ciphertext, NULL, FALSE, fact_ciphert);
}

void handle_admin_name(const unsigned char *temp_ciphertext) {
    unsigned char fact_ciphert[64] = {};
    unsigned short para_id_offset = DAC_PARA_ID_APP_ADMIN_CIPHERTEXT_OFFSET;
    get_special_para_fact_value(para_id_offset, (char *)fact_ciphert);
    judge_password_with_app(para_id_offset, (unsigned char *)temp_ciphertext, NULL, FALSE, fact_ciphert);
    is_common_name = FALSE;
}

void handle_invalid_name(unsigned short para_id_offset) {
    g_app_rtn = RTN_PASSWORD_ERR;
    write_app_event(para_id_offset, "username error");
}

void post_login_check(unsigned short para_id_offset, const unsigned char *temp_ciphertext) {
    char def_ciphert[64] = {};
    get_string_para_def_value(para_id_offset, def_ciphert);
    
    if (g_app_rtn == RTN_SUCCESSFUL && g_passwd_overdue_flag) {
        g_app_rtn = RTN_PASSWD_OVERDUE_SUCC;
    }

    if (g_app_rtn == RTN_SUCCESSFUL && rt_memcmp(def_ciphert, temp_ciphertext, 45) == 0) {
        g_app_rtn = RTN_DEF_PASSWD_SUCC;
    }

    if (is_common_name) {
        if (g_app_rtn == RTN_ACCOUNT_EXPIRED) {
            return;
        }
        if ((g_app_rtn != RTN_SUCCESSFUL && g_app_rtn != RTN_DEF_PASSWD_SUCC)) {
            judge_device_lock(&g_self_lock);
        }
    }
}

int parse_app_request_load(void* dev_inst, void *cmd_buf) {
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf != NULL, FAILURE);
    g_app_rtn = 0;
    cmd_buf_t* cmd_buf_tmp = (cmd_buf_t *)cmd_buf;
    char name[USER_NAME_LEN] = {};
    unsigned char temp_ciphertext[CIPHERTEXT_LEN] = {};
    unsigned short para_id_offset = 0;

    rt_memcpy_s(name, USER_NAME_LEN, &cmd_buf_tmp->buf[USER_NAME_INDEX], USER_NAME_LEN);
    rt_memcpy_s(temp_ciphertext, CIPHERTEXT_LEN, &cmd_buf_tmp->buf[CIPHERTEXT_INDEX], CIPHERTEXT_LEN);

    create_app_timer();

    if (g_self_lock == RTN_DEVICE_LOCK && strcmp(name, APP_COMMON_NAME) == 0) {
        is_common_name = TRUE;
        return SUCCESSFUL;
    }

    if (strcmp(name, APP_COMMON_NAME) == 0) {
        handle_common_name(temp_ciphertext);
        para_id_offset = DAC_PARA_ID_APP_COMMON_CIPHERTEXT_OFFSET;
    } else if (strcmp(name, APP_ADMIN_NAME) == 0) {
        handle_admin_name(temp_ciphertext);
        para_id_offset = DAC_PARA_ID_APP_ADMIN_CIPHERTEXT_OFFSET;
    } else {
        para_id_offset = DAC_PARA_ID_APP_COMMON_CIPHERTEXT_OFFSET;
        handle_invalid_name(para_id_offset);
    }

    post_login_check(para_id_offset, temp_ciphertext);
    g_rtn = g_app_rtn;
    return SUCCESSFUL;
}


int pack_app_request_load(void* dev_inst, void* cmd_buf)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf != NULL, FAILURE);
    cmd_buf_t* cmd_buf_tmp = ((cmd_buf_t *)cmd_buf);
    unsigned short remain_time = 0;

    if(is_common_name && g_self_lock == RTN_DEVICE_LOCK)   // 普通账户-自锁阶段
    {
        time_t cur_time = time(NULL);
        remain_time = APP_LOCK_PERIOD / 1000 - (cur_time - g_time);
        put_uint16_to_buff(cmd_buf_tmp->buf, remain_time);
        cmd_buf_tmp->data_len = sizeof(unsigned short);
        cmd_buf_tmp->rtn = RTN_DEVICE_LOCK; 
        write_app_event(DAC_PARA_ID_APP_COMMON_CIPHERTEXT_OFFSET, "device self lock");
    }

    return SUCCESSFUL;
}

int pack_set_time(void* dev_inst, void* cmd_buf)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf != NULL, FAILURE);
    return SUCCESSFUL;
}

int parse_app_change_password(void* dev_inst, void* cmd_buf)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf != NULL, FAILURE);
    cmd_buf_t* cmd_buf_tmp = ((cmd_buf_t *)cmd_buf);
    char name[USER_NAME_LEN] = {};
    unsigned char old_ciphertext[CIPHERTEXT_LEN] = {};
    unsigned char new_password[CIPHERTEXT_LEN] = {};
    unsigned char fact_ciphert[64] = {};
    g_app_rtn = 0;
    rt_memset_s(old_ciphertext, CIPHERTEXT_LEN, 0, CIPHERTEXT_LEN);
    rt_memset_s(new_password, CIPHERTEXT_LEN, 0, CIPHERTEXT_LEN);
    rt_memset_s(fact_ciphert, sizeof(fact_ciphert), 0, sizeof(fact_ciphert));

    rt_memcpy_s(name, USER_NAME_LEN, &cmd_buf_tmp->buf[USER_NAME_INDEX], USER_NAME_LEN);                 // 用户名
    rt_memcpy_s(old_ciphertext, CIPHERTEXT_LEN, &cmd_buf_tmp->buf[CIPHERTEXT_INDEX], CIPHERTEXT_LEN);     // 旧密码密文
    rt_memcpy_s(new_password, CIPHERTEXT_LEN, &cmd_buf_tmp->buf[NEW_PASSWORD_INDEX], CIPHERTEXT_LEN);     // 新密码明文

    if(0 == strcmp(name, APP_COMMON_NAME))
    {
        get_special_para_fact_value(DAC_PARA_ID_APP_COMMON_CIPHERTEXT_OFFSET, (char*)fact_ciphert);
        judge_password_with_app(DAC_PARA_ID_APP_COMMON_CIPHERTEXT_OFFSET, old_ciphertext, new_password, TRUE, fact_ciphert);
    }
    else if(0 == strcmp(name, APP_ADMIN_NAME))
    {
        get_special_para_fact_value(DAC_PARA_ID_APP_ADMIN_CIPHERTEXT_OFFSET, (char*)fact_ciphert);
        judge_password_with_app(DAC_PARA_ID_APP_ADMIN_CIPHERTEXT_OFFSET, old_ciphertext, new_password, TRUE, fact_ciphert);
    }
    else
    {
        g_app_rtn = RTN_USER_NAME_ERR;              //用户名错误
    }
    if(g_app_rtn == RTN_SUCCESSFUL)      // 修改密码成功，重启定时器
    {
        g_passwd_overdue_flag = FALSE;
        rt_timer_stop(g_app_chg_passwd_timer);
        rt_timer_start(g_app_chg_passwd_timer);
    }
    g_rtn = g_app_rtn;
    return SUCCESSFUL;
}

int pack_app_change_password(void* dev_inst, void* cmd_buf)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf != NULL, FAILURE);
    return SUCCESSFUL;
}

int parse_app_user_auth_time(void* dev_inst, void* cmd_buf)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf != NULL, FAILURE);
    cmd_buf_t* cmd_buf_tmp = (cmd_buf_t *)cmd_buf;
    unsigned short auth_period = 0;
    time_t auth_time = 0;
    g_rtn = 0;

    if (cmd_buf_tmp->data_len != 7)
    {
        g_rtn = WRONG_DATA_LENGTH;
        return SUCCESSFUL;
    }

    // 只有admin用户才能设置该参数
    if (is_common_name)
    {
        g_rtn = RTN_USER_NAME_ERR;
        return SUCCESSFUL;
    }

    auth_period = cmd_buf_tmp->buf[2];     // 减去group
    auth_time = get_uint32_data(&cmd_buf_tmp->buf[3]);
    if (set_one_para(DAC_PARA_ID_USER_ACCOUNT_PERMANENTLY_VALID_OFFSET, &auth_period, TRUE, TRUE) < 0 ||
        set_one_para(DAC_PARA_ID_USER_ACCOUNT_EXPIRATION_DATE_OFFSET, &auth_time, TRUE, TRUE) < 0)
    {
        g_rtn = RTN_INVALID_DATA;
    }

    // 设置参数后更新一次授权信息
    process_user_auth_time();
    return SUCCESSFUL;
}

int set_time_info()
{
    unsigned int tmp_data = 0;
    get_one_para(DAC_PARA_ID_UTC_OFFSET, &tmp_data);
    signed char utc_map[] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12,
                             -1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -12};
    tz_set(utc_map[tmp_data]);
    send_msg_to_thread(AUTHO_TIME_CHECK_MSG, MOD_SYS_MANAGE, NULL, 0);
    return SUCCESSFUL;
}

int set_rs485_para()
{
    //TODO:改成发消息给北向收发线程，由北向收发线程发送完了包在回复
    struct serial_configure config  = RT_SERIAL_CONFIG_DEFAULT;
    unsigned int baud_rate_map[] = {1200, 2400, 4800, 9600};
    char uart_name_map[3][16]={{"uart8"},{"uart4"},{"uart5"}};
    char uart_name[16] = {0};
    unsigned int tmp_data = 0;
    unsigned int ret = 0;
    get_one_para(DAC_PARA_ID_COM_NAME_OFFSET, &tmp_data);
    rt_memcpy_s(uart_name, sizeof(uart_name),uart_name_map[tmp_data],sizeof(uart_name));
    get_one_para(DAC_COMMON_ID_RS485_BAUD_OFFSET, &tmp_data);
    config.baud_rate = baud_rate_map[tmp_data];
    get_one_para(DAC_PARA_ID_RS485_DATA_BIT_OFFSET, &tmp_data);
    config.data_bits = tmp_data;
    get_one_para(DAC_PARA_ID_RS485_STOP_BIT_OFFSET, &tmp_data);
    config.stop_bits = tmp_data;
    get_one_para(DAC_COMMON_ID_RS485_CHK_CODE_OFFSET, &tmp_data);
    config.parity = tmp_data;
    ret = uart_dev_config(uart_name, &config);
    return ret;
}

void get_para_and_set_value(unsigned int para_id ,unsigned int data_id)
{
    unsigned short data = 0;
    get_one_para(para_id, &data);
    set_one_data(data_id, &data);
}

void send_clean_his_energy_msg() 
{
    send_msg_to_thread(CLEAN_HIS_ENERGY_MSG, MOD_SAMPLE, NULL, 0);//发消息到sample清除历史电量
    return ;
}

void send_rs485_stat_msg()
{
    send_msg_to_thread(RS485_STAT_MSG, MOD_INVERTER, NULL, 0);
}

void format_file_system()
{
    check_fs("/", "filesys");
    send_msg_to_thread(FORMATE_FILE_SYS_MSG, MOD_SYS_MANAGE, NULL, 0);
}

// 处理大版本号
int parse_csc_cmd(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    cmd_buf_t *cmd_buf_tmp = ((cmd_buf_t *)cmd_buff);
    update_app_info_t update_info = {0};
    char temp_str[VERSION_LEN] = {0};
    char curr_csu_version[VERSION_LEN] = {0};
    char info[20] = {0};

    rt_memcpy_s(g_csc_psc_info.csu_file_name, VERSION_LEN, &(cmd_buf_tmp->buf[CSC_FILE_NAME_INDEX]), VERSION_LEN);
    rt_memcpy_s(g_csc_psc_info.csu_file_version, VERSION_LEN, &(cmd_buf_tmp->buf[CSC_FILE_VERSION_INDEX]), VERSION_LEN);

    // 监控不用升级
    rt_memcpy_s(temp_str, VERSION_LEN, &(cmd_buf_tmp->buf[CSC_FILE_VERSION_INDEX]), VERSION_LEN);
    LOG_E("update csu_version: %s", temp_str);
    get_one_data(DAC_DATA_ID_MONITOR_SOFTWARE_VER, curr_csu_version);
    if(rt_strcmp(curr_csu_version, temp_str) == 0)
    {
        rt_memcpy_s(temp_str, VERSION_LEN, &(cmd_buf_tmp->buf[CSC_VERSION_INDEX]), VERSION_LEN);
        set_one_para(DAC_PARA_ID_CSC_VERSION_OFFSET, temp_str, TRUE, FALSE);
        rt_memcpy_s(info, 20 ,&(cmd_buf_tmp->buf[VER_START_INDEX]), VER_END_INDEX);
        save_version_event(DAC_PARA_ID_CSC_VERSION_OFFSET, info, PARA_TYPE);
        return SUCCESSFUL;
    }
    handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char*)&update_info, sizeof(update_info), MQTT_UPDATE_INFO_OFFSET);
    rt_memcpy_s(update_info.version, VERSION_LEN, &(cmd_buf_tmp->buf[CSC_VERSION_INDEX]), VERSION_LEN);
    handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&update_info, sizeof(update_info), MQTT_UPDATE_INFO_OFFSET);
    return SUCCESSFUL;
}

int get_psc_final_update()
{
    char curr_version[VERSION_LEN] = {0};

    get_one_data(DAC_DATA_ID_MASTER_CTRL_SOFTWARE_VER, curr_version);
    LOG_E("master_version: curr: %s | update: %s", curr_version, g_csc_psc_info.master_file_version);
    if(rt_strcmp(curr_version, g_csc_psc_info.master_file_version) != 0 )
    {
        return MAIN_CTRL_TYPE;
    }
    get_one_data(DAC_DATA_ID_AUXI_CTRL_SOFTWARE_VER, curr_version);
    LOG_E("auxi_version: curr: %s | update: %s", curr_version, g_csc_psc_info.slave_file_version);
    if(rt_strcmp(curr_version, g_csc_psc_info.slave_file_version) != 0)
    {
        return AUXI_CTRL_TYPE;
    }
    get_one_data(DAC_DATA_ID_CPLD_SOFTWARE_VER, curr_version);
    LOG_E("cpld_version: curr: %s | update: %s", curr_version, g_csc_psc_info.cpld_file_version);
    if(rt_strcmp(curr_version, g_csc_psc_info.cpld_file_version) != 0)
    {
        return CPLD_CTRL_TYPE;
    }
    return INVALID_DEV_TYPE;
}

int parse_psc_cmd(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    cmd_buf_t *cmd_buf_tmp = ((cmd_buf_t *)cmd_buff);
    update_app_info_t update_info = {0};
    unsigned short psc_final_type = 0;
    char temp_str[VERSION_LEN] = {0};
    char info[20] = {0};

    // CPLD
    rt_memcpy_s(g_csc_psc_info.cpld_file_name, VERSION_LEN, &(cmd_buf_tmp->buf[CPLD_FILE_NAME_INDEX]), VERSION_LEN);
    rt_memcpy_s(g_csc_psc_info.cpld_file_version, VERSION_LEN, &(cmd_buf_tmp->buf[CPLD_FILE_VERSION_INDEX]), VERSION_LEN);

    // 辅控
    rt_memcpy_s(g_csc_psc_info.slave_file_name, VERSION_LEN, &(cmd_buf_tmp->buf[SLAVE_FILE_NAME_INDEX]), VERSION_LEN);
    rt_memcpy_s(g_csc_psc_info.slave_file_version, VERSION_LEN, &(cmd_buf_tmp->buf[SLAVE_FILE_VERSION_INDEX]), VERSION_LEN);

    // 主控
    rt_memcpy_s(g_csc_psc_info.master_file_name, VERSION_LEN, &(cmd_buf_tmp->buf[MASTER_FILE_NAME_INDEX]), VERSION_LEN);
    rt_memcpy_s(g_csc_psc_info.master_file_version, VERSION_LEN, &(cmd_buf_tmp->buf[MASTER_FILE_VERSION_INDEX]), VERSION_LEN);

    psc_final_type = get_psc_final_update();
    set_psc_final_type(psc_final_type);
    if(psc_final_type == INVALID_DEV_TYPE)
    {
        rt_memcpy_s(temp_str, VERSION_LEN, &(cmd_buf_tmp->buf[PSC_VERSION_INDEX]), VERSION_LEN);
        set_one_para(DAC_PARA_ID_PSC_VERSION_OFFSET, temp_str, TRUE, FALSE);
        rt_memcpy_s(info, 20 ,&(cmd_buf_tmp->buf[VER_START_INDEX]), VER_END_INDEX);
        save_version_event(DAC_PARA_ID_PSC_VERSION_OFFSET, info, PARA_TYPE);
        return SUCCESSFUL;
    }

    handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char*)&update_info, sizeof(update_info), MQTT_UPDATE_INFO_OFFSET);
    rt_memcpy_s(update_info.version, VERSION_LEN, &(cmd_buf_tmp->buf[PSC_VERSION_INDEX]), VERSION_LEN);
    handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&update_info, sizeof(update_info), MQTT_UPDATE_INFO_OFFSET);
    return SUCCESSFUL;
}

int pack_1363_version_info(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    cmd_buf_t *cmd_buf_tmp = ((cmd_buf_t *)cmd_buff);
    rt_memset_s(cmd_buf_tmp->buf, 32, 0, 32);
    rt_memcpy_s(cmd_buf_tmp->buf, sizeof(S1363_VERSION), S1363_VERSION, sizeof(S1363_VERSION));
    cmd_buf_tmp->data_len = 32;
    return SUCCESSFUL;
}

int pack_parallel_data(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    cmd_buf_t *cmd_buf_tmp = ((cmd_buf_t *)cmd_buff);
    pv_parallel_status_t* parallel_data = get_parallel_status();
    unsigned char parallel_num = 0, dev_addr_index = 0, master_addr = 0, master_slave_status = 0;

    unsigned char* p = cmd_buf_tmp->buf;
    get_one_data(DAC_DATA_ID_MASTER_SLAVE_STATUS, &master_slave_status);
    if(master_slave_status == 0)
    {
        *p = 0;
        p++;
        cmd_buf_tmp->data_len = p - cmd_buf_tmp->buf;
        return SUCCESSFUL;
    }
    get_one_data(DAC_DATA_ID_PARALLEL_NUM, &parallel_num);
    get_one_para(DAC_COMMON_ID_RS485_COMMU_ADDR_OFFSET, &master_addr);
    *p = parallel_num;
    p++;

    while(dev_addr_index < MAX_PARALELL_NUM)
    {
        if(master_addr == dev_addr_index + 1)
        {
            dev_addr_index++;
            continue;
        }

        if(IS_PO_DEV_ON(parallel_data->valid_dev, dev_addr_index))
        {
            *p = dev_addr_index + 1;
            p++;
            rt_memcpy_s(p, SERIAL_NUMBER_LEN,parallel_data->dev_sn[dev_addr_index],SERIAL_NUMBER_LEN);
            p += SERIAL_NUMBER_LEN;
        }
        dev_addr_index++;
    }
    cmd_buf_tmp->data_len = p - cmd_buf_tmp->buf;
    return SUCCESSFUL;
}

int parse_exe_debug_cmd(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    cmd_buf_t *cmd_buf_tmp = ((cmd_buf_t *)cmd_buff);
    static shell_cmd_t shell_cmd = {0};
    char para[3][SHELL_PARA_LEN+1] = {0};
    int i = 0;
    int cmd_type = cmd_buf_tmp->buf[0];
    int cmd_num = sizeof(debug_cmd_tab) / sizeof(debug_cmd_t);

    rt_memcpy(para[0], &cmd_buf_tmp->buf[1], SHELL_PARA_LEN);
    rt_memcpy(para[1], &cmd_buf_tmp->buf[1 + SHELL_PARA_LEN], SHELL_PARA_LEN);
    rt_memcpy(para[2], &cmd_buf_tmp->buf[1 + 2 * SHELL_PARA_LEN], SHELL_PARA_LEN);
    
    LOG_E("%s | %d | cmd_type: %d",__FUNCTION__ , __LINE__, cmd_type);
    rt_kprintf("para:  %s  %s  %s\n",para[0], para[1], para[2]);
    rt_memset(&shell_cmd, 0x00, sizeof(shell_cmd_t));
    for(i = 0; i < cmd_num; i++)
    {
        if(cmd_type == debug_cmd_tab[i].cmd_type)
        {
            rt_snprintf_s(shell_cmd.line, sizeof(shell_cmd.line), debug_cmd_tab[i].cmd, para[0], para[1], para[2]);
            enable_sync_log(TRUE);
            LOG_E("%s", shell_cmd.line);
            msh_exec(shell_cmd.line, rt_strnlen_s(shell_cmd.line, sizeof(shell_cmd.line)));
            enable_sync_log(FALSE);
            break;
        }
    }
    
    return SUCCESSFUL;
}
