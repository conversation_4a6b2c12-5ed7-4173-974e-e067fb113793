/**************************************************************************
* Copyright (C) 2001, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：ZXDU18 CTL板软件
* 文件名称：prtclWireless.h
* 文件说明：协议模块头文件
* 作    者：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/
#ifndef SOFTWARE_SRC_APP_PRTCLWIRELESS_H_
#define SOFTWARE_SRC_APP_PRTCLWIRELESS_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "common.h"

/***********************  常量定义  ************************/

/************************ 链路层 **************************/
#define  FRM_TYPE_RECV  0x01         //网管发送给网元消息
#define	 FRM_TYPE_SEND  0x02         //网元发送给网管消息

#define  GPRS_PRO_VER   0x10         //无线组网协议版本号

#define	 RTN_PARSE_CORRECT			0x00	//接收数据正确
#define  RTN_PARSE_HEART_CORRECT    0x01    //接收心跳正确
#define	 RTN_PARSE_WRONG_VER		0x02	//版本号错误
#define  RTN_PARSE_WRONG_FRMTYPE	0x03	//帧类型错误
#define	 RTN_PARSE_WRONG_IDLEN		0x04	//设备标识长度错误
#define	 RTN_PARSE_WRONG_HEADLEN	0x05	//帧头长度错误

/************************   返回码   ***********************/
#define   RTN_CORRECT             0x00    //接收数据正确
#define   RTN_WRONG_VER           0x01    //版本号错
#define   RTN_WRONG_CHKSUM        0x02    //和校验错
#define   RTN_WRONG_LCHKSUM       0x03    //长度校验错
#define   RTN_WRONG_CID2          0x04    //CID2错
#define   RTN_WRONG_COMMAND       0x05    //命令格式错
#define   RTN_INVALID_DATA        0x06    //无效数据
#define   RTN_NO_DATA             0x07    //前台无相应数据
#define   RTN_FAIL_COMMAND        0xE2    //命令执行失败
#define   RTN_WRONG_CID1          0xE1    //CID1错
#define   NO_RETURN               0xFF    //不用返回响应包

#define   RTN_CTRL_FAILURE        0xE1    //遥控命令执行失败

/************************   CID1码   ***********************/
#define   CID1_BMS             0x4A        //铁锂电池检测装置
#define   WIRELESS_PRTCL_VER   0x22        //无线协议版本
/***********************   CID2命令码   ********************/

#define   GET_SYS_TIME         0x4D        //获取系统时间
#define   SET_SYS_TIME         0x4E        //设置系统时间
#define   GET_REAL_DATA        0x90        //获取遥测量
#define   GET_REAL_ALM         0x91        //获取遥信量
#define   GET_PARA             0x92        //获取遥调量
#define   SET_PARA             0x93        //设置遥调量
#define   SET_CTRL             0x94        //遥控
#define   AUTO_UP_DATA         0xEA        //主动信息上送
#define   AUTO_UP_ACK          0xEB        //主动信息确认

#define   AUTO_UPDATA_COMMAND_TYPE   0x03     //主动告警回复的COMMAND_TYPE
/*********************   缓冲区长度   **********************/
#define HIGH_SEND_LEN           512      //协议层发送缓冲区长度
#define HIGH_REC_LEN            256     //协议层接收缓冲区长度

//
#define CLEAR_BATT_LOCK       0x80
#define SET_BATT_LOCK         0x81

/*********************  数据结构定义  **********************/
/***************   协议层   ******************/
/************************************************************
** 结构名: T_ProtocolStruct
** 描  述: 协议层数据结构定义
** 作  者: 严宏明
** 日  期: 2004-06-01
** 版  本: V4.0 
** 修改记录         
** 日  期       版  本      修改人      修改摘要
** 
**************************************************************/
typedef struct
{
    /***********   接收数据   ************/
    BYTE    aucRecBuf[HIGH_REC_LEN];            //接收到的16进制数据帧
    WORD    wOriginLength;                     //接收数据包的原长度
    BYTE    ucVer;                              //接收数据包的版本号
    BYTE    ucAddr;
    BYTE    ucAddrBak;                          //将收到的地址备份
    BYTE    ucCID1;
    BYTE    ucCID2;                             //接收数据包的设备地址、CID1和CID2
    BYTE    ucLCHKSUM;                          //接收数据包的长度校验和
    WORD    wRecLenid;                          //接收数据包的LENID
    BYTE    ucCommandGroup;                     //接收数据包的COMMAND_GROUP
    BYTE    ucCommandType;                      //接收数据包的COMMAND_TYPE
    
    /***********   发送数据   ************/
    BYTE    ucRTN;                              //响应数据包的返回码
    WORD    wSendLenid;                         //响应数据包的LENID
    BYTE    aucSendBuf[HIGH_SEND_LEN];          //响应数据包发送前的16进制数据帧
}T_PrtclWirelessStruct;


typedef struct
{
	WORD  wHeaderLen;                 //协议帧包头长度(DATA之前的长度)
	BYTE  ucVer;                      //协议版本号
	BYTE  ucFrameType;                //协议帧类型
	WORD  ucIDLen;                    //设备唯一标识码长度
	BYTE  aucNeIDBuff[100];           //设备唯一标识码

	/*********************** 发送数据域 ***************************/
    WORD    wSndDataCacheLen;                  //发送数据域长度(数据域)
    BYTE	aucSndDataBuffCache[HIGH_REC_LEN*2];	   //数据域发送缓冲区(数据域ASCII)

    /*********************** 接收数据域 ***************************/
    WORD    wRecDataCacheLen;                  //接收数据域长度(数据域)
    BYTE	aucRecDataBuffCache[HIGH_REC_LEN*2];	   //数据域接收缓冲区(数据域ASCII)

}T_GprsLinkStruct;


typedef struct
{
	/*** 发送数据 ***/
	WORD   ucSendOriginLen;                     //发送数据帧长度(原始数据)
	BYTE   aucSendBuff[HIGH_REC_LEN*2];         //发送数据帧(原始数据)

	/** 接收数据 **/
    BYTE    aucRecBuf[HIGH_REC_LEN*2];         //接收到的16进制数据帧(原始数据)
    WORD    wRecHeadLen;                       //接收数据帧头的原长度
    WORD    wRecDataLen;                       //接收数据域的原长度
    BYTE    aucRecDataBuf[HIGH_REC_LEN*2];     //数据域数据
}T_GprsCacheStruct;

typedef struct
{
    WORD   wByte;                         //临时累加 单位为Byte
    UINT32 uiByteTotail;                  //总流量   单位为kb
}T_4GTraffic;

typedef struct
{
    T_4GTraffic s_tUp4GTraffic;
    T_4GTraffic s_tDown4GTraffic;
    WORD        wCRC;
}T_4GTrafficTotail;

extern T_GprsCacheStruct g_tGprsCache; //无线通讯协议缓存数据
/*********************  函数原型定义  **********************/
void DealWirelessData(void);
void InitPrtclWirelessData(void);
void AutoUpGpsData(void);
void AutoSendGprsHeart(void);
BYTE GetDataFromGprsCache(void);
void wireless_Task(void* parameter);
BOOLEAN Judge4GCommDisconnect(void);
BOOLEAN Clear4GTraffic(void);
BOOLEAN Get4GTraffic(T_4GTrafficTotail *pDest);
BOOLEAN Save4GTrafficTotail(void);
#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif
#endif
