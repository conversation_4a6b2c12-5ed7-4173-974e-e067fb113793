#include <stdio.h>
#include <string.h>
#include <math.h>
#include <rtthread.h>
#include "protocol_layer.h"
#include "dev_dc_ac_modbus.h"
#include "device_type.h"
#include "msg.h"
#include "cmd.h"
#include "data_type.h"
#include "sps.h"
#include "msg_id.h"
#include "utils_data_transmission.h"
#include "utils_data_type_conversion.h"
#include "realdata_id_in.h"
#include "alarm_id_in.h"
#include "para_id_in.h"
#include "para_manage.h"
#include "partition_def.h"
#include "storage.h"
#include "realdata_save.h"
#include "alarm_manage.h" 
#include "his_record.h"
#include "utils_rtthread_security_func.h"
#include "para_common.h"
#include "type_define_in.h"

signed short g_reg_addr = 0;
signed short g_reg_nums = 0;
static unsigned int g_sid_list[PARA_SID_LIST_MAX] RAM_SECTION_BSS = {0};
#define MODBUS_DATA_MAP_LEN sizeof(g_modbus_data_map) / sizeof(modbus_addr_map_data_t)
#define PARSE_FUN_MAP_LEN   sizeof(parse_fun_map)/sizeof(parse_fun_map_t)


/*******************************打包映射表*****************/
pack_fun_map_t pack_fun_map[] = {
    {TYPE_FLOAT,  TYPE_INT32U, floattoint32u },
    {TYPE_FLOAT,  TYPE_INT32S, floattoint32s},
    {TYPE_FLOAT,  TYPE_INT16U, floattoint16u},
    {TYPE_FLOAT,  TYPE_INT16S, floattoint16s},
    {TYPE_INT16U, TYPE_INT16U, int16utoint16u},
    {TYPE_INT16S, TYPE_INT16S, int16stoint16s},
    {TYPE_INT32U, TYPE_INT32U, int32utoint32u} , 
    {TYPE_STRING,    TYPE_STRING,    stringtostring},
    {TYPE_DATE_T,   TYPE_INT16U, datetoint16u},
    {TYPE_INT8S,   TYPE_BIT,    chartobit},
    {TYPE_MAX,    TYPE_MAX,    NULL},
};


/*****************************解包映射表*****************/
parse_fun_map_t parse_fun_map[] = {
    {TYPE_INT16U, TYPE_FLOAT,  parse_int16utofloat},
    {TYPE_INT16S, TYPE_FLOAT,  parse_int16stofloat },
    {TYPE_INT32U, TYPE_FLOAT,  parse_int32utofloat },
    {TYPE_INT32S, TYPE_FLOAT,  parse_int32stofloat},
    {TYPE_INT16U, TYPE_INT16U, parse_int16utoint16u},
    {TYPE_INT32U, TYPE_INT32U, parse_int32utoint32u},
    {TYPE_STRING,    TYPE_STRING,    parse_stringtostring },
    {TYPE_MAX,    TYPE_MAX,    NULL},
};

modbus_addr_map_data_t g_modbus_data_map[] RAM_SECTION =
{
    {1000, 0, TYPE_FLOAT, TYPE_INT32U, 3, 0, 4, TYPE_ANA , DAC_DATA_ID_MPPT_TOTAL_INPUT_POWER }, // MPPT1总输入功率
    {1002, 0, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_MPPT_VOLT },               // MPPT1电压
    {1003, 0, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2, TYPE_ANA , DAC_DATA_ID_MPPT_CURR },              // MPPT1电流
    {1004, 0, TYPE_FLOAT, TYPE_INT32U, 3, 0, 4, TYPE_ANA , DAC_DATA_ID_MPPT_TOTAL_INPUT_POWER + 1 }, // MPPT2总输入功率
    {1006, 0, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_MPPT_VOLT + 1 },               // MPPT2电压
    {1007, 0, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2, TYPE_ANA , DAC_DATA_ID_MPPT_CURR + 1 },              // MPPT2电流
    {1008, 0, TYPE_FLOAT, TYPE_INT32U, 3, 0, 4, TYPE_ANA , DAC_DATA_ID_MPPT_TOTAL_INPUT_POWER + 2 }, // MPPT3总输入功率
    {1010, 0, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_MPPT_VOLT + 2 },               // MPPT3电压
    {1011, 0, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2, TYPE_ANA , DAC_DATA_ID_MPPT_CURR + 2 },              // MPPT3电流
    {1012, 0, TYPE_FLOAT, TYPE_INT32U, 3, 0, 4, TYPE_ANA , DAC_DATA_ID_MPPT_TOTAL_INPUT_POWER + 3 }, // MPPT4总输入功率
    {1014, 0, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_MPPT_VOLT + 3 },               // MPPT4电压
    {1015, 0, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2, TYPE_ANA , DAC_DATA_ID_MPPT_CURR + 3 },              // MPPT4电流
    {1016, 1, TYPE_FLOAT, TYPE_INT32U, 3, 0, 4, TYPE_ANA , DAC_DATA_ID_MPPT_TOTAL_INPUT_POWER + 4 },                                          // MPPT5总输入功率
    {1018, 1, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_MPPT_VOLT + 4 },                                       // MPPT5电压(预留)
    {1019, 1, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2, TYPE_ANA , DAC_DATA_ID_MPPT_CURR + 4 },                                        // MPPT5电流(预留)
    {1020, 1, TYPE_FLOAT, TYPE_INT32U, 3, 0, 4, TYPE_ANA , DAC_DATA_ID_MPPT_TOTAL_INPUT_POWER + 5 },                                      // MPPT6总输入功率
    {1022, 1, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_MPPT_VOLT + 5 },                                       // MPPT6电压(预留)
    {1023, 1, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2, TYPE_ANA , DAC_DATA_ID_MPPT_CURR + 5 },                                       // MPPT6电流(预留)
    {1024, 1, TYPE_FLOAT, TYPE_INT32U, 3, 0, 4, TYPE_ANA , DAC_DATA_ID_MPPT_TOTAL_INPUT_POWER + 6 },          // MPPT7总输入功率
    {1026, 1, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_MPPT_VOLT + 6 },                       // MPPT7电压(预留)
    {1027, 1, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2, TYPE_ANA , DAC_DATA_ID_MPPT_CURR + 6 },                       // MPPT7电流(预留)
    {1028, 1, TYPE_FLOAT, TYPE_INT32U, 3, 0, 4, TYPE_ANA , DAC_DATA_ID_MPPT_TOTAL_INPUT_POWER + 7 },          // MPPT8总输入功率
    {1030, 1, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_MPPT_VOLT + 7 },                      // MPPT8电压(预留)
    {1031, 1, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2, TYPE_ANA , DAC_DATA_ID_MPPT_CURR + 7 },                      // MPPT8电流(预留)
    {1032, 1, TYPE_FLOAT, TYPE_INT32U, 3, 0, 4, TYPE_ANA , DAC_DATA_ID_MPPT_TOTAL_INPUT_POWER + 8 },         // MPPT9总输入功率
    {1034, 1, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_MPPT_VOLT + 8 },                      // MPPT9电压(预留)
    {1035, 1, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2, TYPE_ANA , DAC_DATA_ID_MPPT_CURR + 8 },                      // MPPT9电流(预留)-
    {1036, 0, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_VOLT },  // PV1电压
    {1037, 0, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_CURR }, // PV1电流
    {1038, 0, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_VOLT + 1 },  // PV2电压
    {1039, 0, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_CURR + 1 }, // PV2电流
    {1040, 0, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_VOLT + 2 },  // PV3电压
    {1041, 0, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_CURR + 2 }, // PV3电流
    {1042, 0, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_VOLT + 3 },  // PV4电压
    {1043, 0, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_CURR + 3 }, // PV4电流
    {1044, 0, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_VOLT + 4 },  // PV5电压
    {1045, 0, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_CURR + 4 }, // PV5电流
    {1046, 0, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_VOLT + 5 },  // PV6电压
    {1047, 0, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_CURR + 5 }, // PV6电流
    {1048, 0, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_VOLT + 6 },  // PV7电压
    {1049, 0, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_CURR + 6 }, // PV7电流
    {1050, 0, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_VOLT + 7 },  // PV8电压
    {1051, 0, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_CURR + 7 }, // PV8电流
    {1052, 1, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_VOLT + 8 },           // PV9电压(预留)
    {1053, 1, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_CURR + 8 },           // PV9电流(预留)
    {1054, 1, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_VOLT + 9 },           // PV10电压(预留)
    {1055, 1, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_CURR + 9 },           // PV10电流(预留)
    {1056, 1, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_VOLT + 10 },           // PV11电压(预留)
    {1057, 1, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_CURR + 10 },           // PV11电流(预留)
    {1058, 1, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_VOLT + 11 },           // PV12电压(预留)
    {1059, 1, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_CURR + 11 },           // PV12电流(预留)
    {1060, 1, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_VOLT + 12 },           // PV13电压(预留)
    {1061, 1, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_CURR + 12 },           // PV13电流(预留)
    {1062, 1, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_VOLT + 13 },           // PV14电压(预留)
    {1063, 1, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_CURR + 13 },           // PV14电流(预留)
    {1064, 1, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_VOLT + 14 },           // PV15电压(预留)
    {1065, 1, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_CURR + 14 },           // PV15电流(预留)
    {1066, 1, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_VOLT + 15 },           // PV16电压(预留)
    {1067, 1, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_CURR + 15 },           // PV16电流(预留)
    {1068, 1, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_VOLT + 16 },           // PV17电压(预留)
    {1069, 1, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_CURR + 16 },           // PV17电流(预留)
    {1070, 1, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_VOLT + 17 },           // PV18电压(预留)
    {1071, 1, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_CURR + 17 },           // PV18电流(预留)
    {1072, 0, TYPE_FLOAT, TYPE_INT32S, 3, 0, 4, TYPE_ANA , DAC_DATA_ID_ACTIVE_POWER },              // 有功功率
    {1074, 0, TYPE_FLOAT, TYPE_INT32S, 3, 0, 4, TYPE_ANA , DAC_DATA_ID_REACTIVE_POWER },            // 无功功率
    {1076, 0, TYPE_FLOAT, TYPE_INT32U, 3, 0, 4, TYPE_ANA , DAC_DATA_ID_APPARENT_POWER },         // 视在功率
    {1078, 0, TYPE_FLOAT, TYPE_INT16S, 3, 0, 2, TYPE_ANA , DAC_DATA_ID_POWER_FACTOR },           // 功率因数
    {1079, 0, TYPE_FLOAT, TYPE_INT16U, 3, 0, 2, TYPE_ANA , DAC_DATA_ID_GRID_FREQ },    // 电网频率
    {1080, 0, TYPE_FLOAT, TYPE_INT16U, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_GRID_AB_VOLT },          // 电网Uab
    {1081, 0, TYPE_FLOAT, TYPE_INT16U, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_GRID_BC_VOLT },          // 电网Ubc
    {1082, 0, TYPE_FLOAT, TYPE_INT16U, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_GRID_CA_VOLT },          // 电网Uca
    {1083, 0, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2, TYPE_ANA , DAC_DATA_ID_GRID_PHASE_CURR }, // A相电流
    {1084, 0, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2, TYPE_ANA , DAC_DATA_ID_GRID_PHASE_CURR + 1 }, // B相电流
    {1085, 0, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2, TYPE_ANA , DAC_DATA_ID_GRID_PHASE_CURR + 2 }, // C相电流
    {1086, 0, TYPE_FLOAT, TYPE_INT32U, 3, 0, 4, TYPE_ANA , DAC_DATA_ID_TOTAL_INPUT_POWER },    // 总输入功率----74
    {1088, 0, TYPE_FLOAT, TYPE_INT16U,  3, 0, 2, TYPE_ANA , DAC_DATA_ID_POS_INSULATION_IMPERD }, // 母线正对地绝缘阻抗
    {1089, 0, TYPE_FLOAT, TYPE_INT16U,  2, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_EFFICIENCY },         // 逆变器效率
    {1090, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_NET_NUMBER },       // PV组串数量
    {1091, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ANA , DAC_DATA_ID_MPPT_NUMBER },            // MPPT数量
    {1092, 0, TYPE_FLOAT, TYPE_INT32U, 3, 0, 4,  TYPE_ANA , DAC_DATA_ID_RATED_NUMBER },        // 额定功率（Pn）
    {1094, 0, TYPE_FLOAT, TYPE_INT32U, 3, 0, 4,  TYPE_ANA , DAC_DATA_ID_MAX_ACTIVE_POWER },      // 最大有功功率（Pmax）
    {1096, 0, TYPE_FLOAT, TYPE_INT32U, 3, 0, 4,  TYPE_ANA , DAC_DATA_ID_MAX_APPARENT_POWER }, // 最大视在功率（Smax）
    {1098, 0, TYPE_FLOAT, TYPE_INT32S, 3, 0, 4,  TYPE_ANA , DAC_DATA_ID_MAX_REACTIVE_POWER },    // 最大无功功率（ 
    {1100, 0, TYPE_FLOAT, TYPE_INT32U, 2, 0, 4,  TYPE_ANA , DAC_DATA_ID_DAILY_POWER_GENERATION }, // 日发电量
    {1102, 0, TYPE_FLOAT, TYPE_INT32U, 2, 0, 4,  TYPE_ANA , DAC_DATA_ID_MONTH_POWER_GENERATION }, // 月发电量
    {1104, 0, TYPE_FLOAT, TYPE_INT32U, 2, 0, 4,  TYPE_ANA , DAC_DATA_ID_YEAR_POWER_GENERATION }, // 年发电量
    {1106, 0, TYPE_FLOAT, TYPE_INT32U, 2, 0, 4,  TYPE_ANA , DAC_DATA_ID_ACCUMLATE_POWER_GENERATION }, // 累计发电量
    {1108, 0, TYPE_FLOAT, TYPE_INT32U, 0, 0, 4,  TYPE_ANA , DAC_DATA_ID_TOTAL_GRID_CONNECT_RUNNING_TIME }, // 总并网运行时间
    {1110, 0, TYPE_FLOAT, TYPE_INT16U, 0, 0, 2,  TYPE_ANA , DAC_DATA_ID_DAILY_GRID_CONNECT_RUNNING_TIME }, // 日并网运行时间
    {1111, 0, TYPE_FLOAT, TYPE_INT32U, 2, 0, 4, TYPE_ANA , DAC_DATA_ID_CO2_DAY_EMISSION}, // CO2当日减排量
    {1113, 0, TYPE_FLOAT, TYPE_INT32U, 2, 0, 4, TYPE_ANA , DAC_DATA_ID_CO2_MONTH_EMISSION}, // CO2当月减排量
    {1115, 0, TYPE_FLOAT, TYPE_INT32U, 2, 0, 4, TYPE_ANA , DAC_DATA_ID_CO2_YEAR_EMISSION}, // CO2当年减排量
    {1117, 0, TYPE_FLOAT, TYPE_INT32U, 2, 0, 4, TYPE_ANA , DAC_DATA_ID_CO2_ACCMULATE_EMISSION}, // CO2累计减排量
    {1119, 0, TYPE_FLOAT, TYPE_INT16U, 3, 0, 2, TYPE_ANA , DAC_DATA_ID_SHORT_CIRCUIT_POSITION_WITH_LOW_INSULAT_IMPEDANCE}, // 绝缘阻抗低可能短路位置
    {1200, 0, TYPE_INT16U, TYPE_INT16U, 0, 0 , 2, TYPE_ANA , DAC_DATA_ID_DEV_STATUS },                  // 状态量1
    {1201, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2,TYPE_ANA , DAC_DATA_ID_UPDATE_STATUS}, //升级状态
    {1202, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_INPUT_UNDERVOLT_STA}, //输入欠压状态
    {1202, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_RELAY_STATUS}, //输入干接点状态1
    {1202, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_RELAY_STATUS + 1}, //输入干接点状态2
    {1202, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_RELAY_STATUS + 2}, //输入干接点状态3
    {1202, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_RELAY_STATUS + 3 }, //输入干接点状态4
    {1202, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_RELAY_STATUS + 4}, //输入干接点状态5
    {1202, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_OUT_RELAY_STATUS}, //输出干接点状态1
    {1202, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_POWER_ON_OFF_STA}, //开关机状态
    {1202, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_IV_COMPLETE_STATUS}, //IV扫描完成状态
    {1202, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_ACCI_TOTAL_SIGNAL}, // 事故总信号
    {1202, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_ALARM_TOTAL_SIGNAL}, // 告警总信号
    {1202, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_INSULATION_IMPEDANCE_LOW_POSITION_STATE}, //绝缘阻抗低定位状态
    {1202, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_PID_REPAIR_STATUS}, //PID修复状态
    {1202, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 3, TYPE_ANA , }, //预留
    {1203, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_PV_SCAN_STATUS    }, //PV1扫描状态
    {1203, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_PV_SCAN_STATUS + 1}, //PV2扫描状态
    {1203, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_PV_SCAN_STATUS + 2}, //PV3扫描状态
    {1203, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_PV_SCAN_STATUS + 3}, //PV4扫描状态
    {1203, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_PV_SCAN_STATUS + 4}, //PV5扫描状态
    {1203, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_PV_SCAN_STATUS + 5}, //PV6扫描状态
    {1203, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_PV_SCAN_STATUS + 6}, //PV7扫描状态
    {1203, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_PV_SCAN_STATUS + 7}, //PV8扫描状态
    {1203, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 4, TYPE_ANA , }, //预留
    {1203, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_MPPT_CURRENT_LIMITING_STATUS    }, //MPPT1限流状态
    {1203, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_MPPT_CURRENT_LIMITING_STATUS + 1}, //MPPT2限流状态
    {1203, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_MPPT_CURRENT_LIMITING_STATUS + 2}, //MPPT3限流状态
    {1203, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_MPPT_CURRENT_LIMITING_STATUS + 3}, //MPPT4限流状态

    {1204, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_PV_CONNECTED_STATUS    }, //PV1连接状态
    {1204, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_PV_CONNECTED_STATUS + 1}, //PV2连接状态
    {1204, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_PV_CONNECTED_STATUS + 2}, //PV3连接状态
    {1204, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_PV_CONNECTED_STATUS + 3}, //PV4连接状态
    {1204, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_PV_CONNECTED_STATUS + 4}, //PV5连接状态
    {1204, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_PV_CONNECTED_STATUS + 5}, //PV6连接状态
    {1204, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_PV_CONNECTED_STATUS + 6}, //PV7连接状态
    {1204, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_PV_CONNECTED_STATUS + 7}, //PV8连接状态
    {1204, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , }, //预留
    {1204, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_DC_INPUT_LOAD_SHEDDING            }, //直流输入降载
    {1204, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_MODULATION_INDEX_LOAD_SHEDDING    }, //调制比降载
    {1204, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_RADIATOR_TEMPERATURE_LOAD_SHEDDING}, //散热器温度降载
    {1204, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_INTERNAL_TEMPERATURE_LOAD_SHEDDING}, //机内温度降载
    {1204, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_PCB_TEMPERATURE_LOAD_SHEDDING     }, //PCB温度降载
    {1204, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_GRID_VOLTAGE_LOAD_SHEDDING        }, //电网电压降载
    {1204, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ANA , DAC_DATA_ID_ALTITUDE_LOAD_SHEDDING            }, //海拔降载

    {1205, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1,  TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_MPPT_OVER_VOLT,1,1) }, // MPPT1过压
    {1205, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1,  TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_MPPT_OVER_VOLT,2,1) }, // MPPT2过压
    {1205, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1,  TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_MPPT_OVER_VOLT,3,1) }, // MPPT3过压
    {1205, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1,  TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_MPPT_OVER_VOLT,4,1) }, // MPPT4过压

    {1205, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 7, TYPE_ALAM , }, // 预留
    {1205, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1,  TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_IN_HARDWARE_FAULT,1,1) }, // 内部硬件故障

    {1205, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1,  TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_BUS_SOFT_START_ERR,1,1) }, // 母线软启动故障
    {1205, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1,  TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_DC_DISCONNECTOR_TRIP,1,1) },// 直流隔离开关脱扣
    {1205, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_BUS_UNDER_VOLT,1,1)}, // bus欠压
    {1205, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_PV_SLOW_UP_ERR,1,1)}, // 逆变器缓起失败

    {1206, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 8, TYPE_ALAM , },  // 预留                                                                                                    
    {1206, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_STR_REVERSE_CONNECT , 1 , 1 ) },     // 组串1反接
    {1206, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_STR_REVERSE_CONNECT , 2 , 1 ) }, // 组串2反接
    {1206, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_STR_REVERSE_CONNECT , 3 , 1 ) }, // 组串3反接
    {1206, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_STR_REVERSE_CONNECT , 4 , 1 ) }, // 组串4反接
    {1206, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_STR_REVERSE_CONNECT , 5 , 1 ) }, // 组串5反接
    {1206, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_STR_REVERSE_CONNECT , 6 , 1 ) }, // 组串6反接
    {1206, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_STR_REVERSE_CONNECT , 7 , 1 ) }, // 组串7反接
    {1206, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_STR_REVERSE_CONNECT , 8 , 1 ) }, // 组串8反接
    {1207, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 16, TYPE_ALAM , }, // 预留
    {1208, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_MPPT_OVER_CURR_WARN , 1 , 1 ) }, // MPPT1过流告警
    {1208, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_MPPT_OVER_CURR_WARN , 2 , 1 ) }, // MPPT2过流告警
    {1208, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_MPPT_OVER_CURR_WARN , 3 , 1 ) }, // MPPT3过流告警
    {1208, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_MPPT_OVER_CURR_WARN , 4 , 1 ) }, // MPPT4过流告警
    {1208, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 8, TYPE_ALAM , }, // 预留
    {1208, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_MPPT_OVER_CURR_ERROR , 1 , 1 ) }, // MPPT1过流故障
    {1208, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_MPPT_OVER_CURR_ERROR , 2 , 1 ) }, // MPPT2过流故障
    {1208, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_MPPT_OVER_CURR_ERROR , 3 , 1 ) }, // MPPT3过流故障
    {1208, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_MPPT_OVER_CURR_ERROR , 4 , 1 ) }, // MPPT4过流故障
    {1209, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 8, TYPE_ALAM , },// 预留
    {1209, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_STR_CURR_REVERSE_INJECT , 1 , 1 ) },// 组串1电流反灌
    {1209, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_STR_CURR_REVERSE_INJECT , 2 , 1 ) }, // 组串2电流反灌
    {1209, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_STR_CURR_REVERSE_INJECT , 3 , 1 ) }, // 组串3电流反灌
    {1209, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_STR_CURR_REVERSE_INJECT , 4 , 1 ) }, // 组串4电流反灌
    {1209, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_STR_CURR_REVERSE_INJECT , 5 , 1 ) }, // 组串5电流反灌
    {1209, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_STR_CURR_REVERSE_INJECT , 6 , 1 ) }, // 组串6电流反灌
    {1209, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_STR_CURR_REVERSE_INJECT , 7 , 1 ) }, // 组串7电流反灌
    {1209, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_STR_CURR_REVERSE_INJECT , 8 , 1 ) }, // 组串8电流反灌
    {1210, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_SERIES_DC_ARC_FAULT , 1 , 1 )}, // 组串1直流电弧故障
    {1210, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_SERIES_DC_ARC_FAULT , 2 , 1 )}, // 组串2直流电弧故障
    {1210, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_SERIES_DC_ARC_FAULT , 3 , 1 )}, // 组串3直流电弧故障
    {1210, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_SERIES_DC_ARC_FAULT , 4 , 1 )}, // 组串4直流电弧故障
    {1210, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_SERIES_DC_ARC_FAULT , 5 , 1 )}, // 组串5直流电弧故障
    {1210, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_SERIES_DC_ARC_FAULT , 6 , 1 )}, // 组串6直流电弧故障
    {1210, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_SERIES_DC_ARC_FAULT , 7 , 1 )}, // 组串7直流电弧故障
    {1210, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_SERIES_DC_ARC_FAULT , 8 , 1 )}, // 组串8直流电弧故障
    {1210, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 8, TYPE_ALAM , }, //预留
    {1211, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 16, TYPE_ALAM , }, //预留
    {1212, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_DC_ARC_DAULT , 1 , 1 ) },                // 直流电弧故障
    {1212, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_AFCI_SELF_CHK_FAIL , 1 , 1 ) },        // AFCI自检失败
    {1212, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_BUS_OVER_VOLT , 1 , 1 ) },               // 母线过压
    {1212, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_POS_BUS_OVER_VOLT , 1 , 1 )},           // 正母线过压
    {1212, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_NEG_BUS_OVER_VOLT , 1 , 1 ) },           // 负母线过压
    {1212, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_BUS_VOLT_IMBALANCE , 1 , 1 ) },       // 母线电压不平衡
    {1212, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_ABNORMAL_OPER_BUILD_IN_PID , 1 , 1 ) },        // 内置PID工作异常
    {1212, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM ,  },// 预留
    {1212, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_IMPEDANCE_WARN , 1 , 1 ) },    //绝缘阻抗低告警
    {1212, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_IMPEDANCE_ERROR , 1 , 1 ) },    // 绝缘阻抗低故障
    {1212, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_ABN_RESIDUAL_CURR , 1 , 1 ) },    // 残余电流异常
    {1212, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_ABN_RESIDUAL_CURR_LEAP , 1 , 1 )}, // 残余电流突变异常
    {1212, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_OUTPUT_WIRING_MISMATCH , 1 , 1 )}, // 输出接线不匹配
    {1212, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_LEAK_CURR_SELFCHECK_FAILURE_FAULT , 1 , 1 )}, // 漏电流电路自检失败故障
    {1212, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 2, TYPE_ALAM , }, // 预留
    {1213, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_PV_OVER_CURR_WARN , 1 , 1 ) },             // 逆变器A相过流告警
    {1213, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_PV_OVER_CURR_WARN , 2 , 1 ) },            // 逆变器B相过流告警
    {1213, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_PV_OVER_CURR_WARN , 3 , 1 ) },    // 逆变器C相过流告警
    {1213, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_PV_OVER_CURR_ERROR , 1 , 1 ) },                      // 逆变器A相过流故障
    {1213, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_PV_OVER_CURR_ERROR , 2 , 1 ) },        // 逆变器B相过流故障
    {1213, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_PV_OVER_CURR_ERROR , 3 , 1 ) },        // 逆变器C相过流故障
    {1213, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_HIGH_VOLT_RT , 1 , 1 ) },       // 高电压穿越
    {1213, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_LOW_VOLT_RT , 1 , 1 ) },       // 低电压穿越
    {1213, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_LOW_POWER_SHUTDOWN , 1 , 1 ) },             // 低功率关机
    {1213, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_WATER_INGRESS , 1 , 1 ) },             // 逆变器进水告警
    {1213, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 6, TYPE_ALAM , }, // 预留
    {1214, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_OVER_VOLT_BETWEEN_PHASE , 1 , 1 ) },            // AB过压
    {1214, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_OVER_VOLT_BETWEEN_PHASE , 2 , 1 ) },        // BC过压
    {1214, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_OVER_VOLT_BETWEEN_PHASE , 3 , 1 ) },        // CA过压
    {1214, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_UNDER_VOLT_BETWEEN_PHASE , 1 , 1 ) },           // AB欠压
    {1214, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_UNDER_VOLT_BETWEEN_PHASE , 2 , 1 ) },       // BC欠压
    {1214, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_UNDER_VOLT_BETWEEN_PHASE , 3 , 1 ) },       // CA欠压
    {1214, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_PHASE_VOLT_UNDER_VOLT , 1 , 1 ) },              // A相欠压
    {1214, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_PHASE_VOLT_UNDER_VOLT , 2 , 1 ) },         // B相欠压
    {1214, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_PHASE_VOLT_UNDER_VOLT , 3 , 1 ) },         // C相欠压
    {1214, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_PHASE_VOLT_OVER_VOLT , 1 , 1 ) },              // A相过压
    {1214, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_PHASE_VOLT_OVER_VOLT , 2 , 1 ) },          // B相过压
    {1214, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_PHASE_VOLT_OVER_VOLT , 3 , 1 ) },          // C相过压
    {1214, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_PHASE_VOLT_TRANS_OVER_VOLT , 1 , 1 ) },        // A相瞬态过压
    {1214, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_PHASE_VOLT_TRANS_OVER_VOLT , 2 , 1 ) },    // B相瞬态过压
    {1214, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_PHASE_VOLT_TRANS_OVER_VOLT , 3 , 1 ) },    // C相瞬态过压
    {1214, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_OUT_CURR_IMBALANCE , 1 , 1 )}, // 输出电流不平衡告警
    {1215, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_GRID_OVER_FREQ , 1 , 1 ) },        // 电网过频
    {1215, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_GRID_UNDER_FREQ , 1 , 1 ) },       // 电网欠频
    {1215, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_GRID_FREQ_INSTABLE, 1 , 1 )  },      // 电网频率不稳
    {1215, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_AC_PHASE_LOSS, 1 , 1 )  },              // 交流缺相
    {1215, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_ABNORMAL_GROUND, 1 , 1 )  },         // 接地异常
    {1215, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_SINGLE_PHASE_GND_SHORT_CIRCUIT , 1 , 1 ) },       // A相对地短路
    {1215, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_SINGLE_PHASE_GND_SHORT_CIRCUIT , 2 , 1 )  },       // B相对地短路
    {1215, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_SINGLE_PHASE_GND_SHORT_CIRCUIT , 3 , 1 )  },       // C相对地短路
    {1215, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_TEN_MINUTE_GRID_OVER_VOLT_PROT, 1 , 1 )  }, // 十分钟电网电压过压保护
    {1215, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_UNBALANCE_POWER_GRID, 1 , 1 )  },       // 电网不平衡
    {1215, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_OUTPUT_SHORT_CIRCUIT, 1 , 1 )  },       // 输出短路
    {1215, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_OUTPUT_OVER_CIRCUIT, 1 , 1 )  },           // 输出过流
    {1215, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_ABNORMAL_DC_COMPONENT, 1 , 1 )  },           // 直流分量异常
    {1215, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_ISLAND_PROT, 1 , 1 )  },             // 孤岛保护
    {1215, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_POWER_GRID_OUTAGE, 1 , 1 )  },          // 电网掉电
    {1215, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_GRID_ANTI_ERROR, 1 , 1 )  },          // 电网反序故障
    {1216, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_GRID_RELAY_ERROR, 1 , 1 )  },  //并网继电器故障
    {1216, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_AC_CURR_SENSOR_ERROR, 1 , 1 )  }, // AC电流传感器故障
    {1216, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_PV_LOCK_ERROR, 1 , 1 )  },        // 逆变器锁相失败故障
    {1216, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_STR_LOSS, 1, 1)},  // 组串1丢失
    {1216, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_STR_LOSS, 2, 1)},  // 组串2丢失
    {1216, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_STR_LOSS, 3, 1)},  // 组串3丢失
    {1216, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_STR_LOSS, 4, 1)},  // 组串4丢失
    {1216, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_STR_LOSS, 5, 1)},  // 组串5丢失
    {1216, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_STR_LOSS, 6, 1)},  // 组串6丢失
    {1216, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_STR_LOSS, 7, 1)},  // 组串7丢失
    {1216, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_STR_LOSS, 8, 1)},  // 组串8丢失
    {1216, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 5, TYPE_ALAM ,},  // 预留
    {1217, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_RADIATOR_OVER_TEMP , 1 , 1 ) },    // 散热器1过温
    {1217, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_RADIATOR_OVER_TEMP , 2 , 1 ) },    // 散热器2过温
    {1217, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_RADIATOR_OVER_TEMP , 3 , 1 ) },    // 散热器3过温
    {1217, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , },    // 散热器4过温（预留）
    {1217, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , },    // 散热器5过温（预留）
    {1217, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_PCB_LOW_TEMPERATURE_WARNING , 1 , 1 ) },//PCB低温告警
    {1217, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_INSIDE_LOW_TEMPERATURE_WARNING , 1 , 1 ) },//内部低温告警
    {1217, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_NTC_LOW_TEMPERATURE_WARNING , 1, 1) },//NTC1低温告警
    {1217, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_NTC_LOW_TEMPERATURE_WARNING , 2, 1) },//NTC2低温告警
    {1217, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_NTC_LOW_TEMPERATURE_WARNING , 3, 1) },//NTC3低温告警
    {1217, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_LOW_TEMPERATURE_SHUTDOWN_PROTECTION , 1 , 1 ) },//低温关机保护
    {1217, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 5, TYPE_ALAM ,},  // 预留

    {1218, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_PCB_OVER_TEMP, 1 , 1 )  },  //PCB温度过温
    {1218, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM ,}, // 预留
    {1218, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_OVER_TEMP_INSIDS_MACHINE, 1 , 1 )  },        // 机内温度过温
    {1218, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_LOW_TEMP_INSIDS_MACHINE, 1 , 1 )},  // 机内温度过低
    {1218, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_FIRE_ERROR , 1 , 1 ) },    // 火灾故障
    {1218, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_EXTER_FAN_FAIL , 1 , 1 ) },    // 外部风扇故障
    {1218, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_INTER_FAN_FAIL , 1 , 1 ) },    // 内部风扇故障
    {1218, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_ABN_AUXI_POWER_SUPPLT , 1 , 1 ) },    // 辅助电源异常
    {1218, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_AC_LIGHTING_PROTECTION_ERR , 1 , 1 ) },    // 交流防雷失效
    {1218, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_DC_LIGHTING_PROTECTION_ERR , 1 , 1 ) },    // 直流防雷失效
    {1218, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 6, TYPE_ALAM ,},  // 预留
    {1219, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_MAIN_CTRL_EEPROM_FAULT, 1 , 1 ) }, // 主控EEPROM故障
    {1219, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_MAIN_CTRL_EEPROM_ABNORMAL, 1 , 1 ) }, // 主控EEPROM异常
    {1219, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_AUXI_CTRL_EEPROM_FAULT, 1 , 1 ) },  // 辅控EEPROM故障
    {1219, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_AUXI_CTRL_EEPROM_ABNORMAL, 1 , 1 ) },  // 辅控EEPROM异常
    {1219, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1,  TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_MAIN_CPLD_COMMU_ERROR, 1 , 1 ) }, // 主控与CPLD通讯故障
    {1219, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1,  TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_MAIN_AUXI_COMMU_ERROR, 1 , 1 ) },    // 主控与辅控通讯故障
    {1219, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1,  TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_PROTOCOL_VER_MISMATCH, 1 , 1 ) }, // 协议版本不匹配
    {1219, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1,  TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_CARRIER_SYNC_ABNORMAL, 1 , 1 ) },    // 载波同步信号异常
    {1219, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1,  TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_MONITER_MAIN_CTRL_COMMU_ERROR, 1 , 1 ) }, // 监控与主控通讯异常
    {1219, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , GET_ALM_ID(DAC_ALARM_ID_MONITER_ERROR_ALARM, 1 , 1)}, // 监控单元故障告警
    {1219, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1,  TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_LICENSE_EXPIRED, 1 , 1 ) },    // 许可证过期
    {1219, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1,  TYPE_ALAM , GET_ALM_ID( DAC_ALARM_ID_USER_ACCOUNT_EXPIRED, 1 , 1 ) },    // user账号超期
    {1219, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 4, TYPE_ALAM , }, // 预留

    {1295, 0, TYPE_DATE_T, TYPE_INT16U, 0,  0, 2,  TYPE_ANA ,  DAC_DATA_ID_FIRST_STARTUP_TIME },   // 首次启动时间-年
    {1296, 0, TYPE_DATE_T, TYPE_INT16U, 0,  0, 2,  TYPE_ANA ,  DAC_DATA_ID_FIRST_STARTUP_TIME },   // 首次启动时间-月
    {1297, 0, TYPE_DATE_T, TYPE_INT16U, 0,  0, 2,  TYPE_ANA ,  DAC_DATA_ID_FIRST_STARTUP_TIME },   // 首次启动时间-日
    {1298, 1, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ANA ,  },//预留
    {1299, 0, TYPE_INT16U, TYPE_INT16U, 0,  0,  2,  TYPE_ANA ,  DAC_DATA_ID_NET_CONNECT_MODE },   // 网管组网方式
    {1300, 0, TYPE_FLOAT, TYPE_INT16U, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_GRID_PHASE_VOLT }, // 电网A相电压
    {1301, 0, TYPE_FLOAT, TYPE_INT16U, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_GRID_PHASE_VOLT + 1 }, // 电网B相电压
    {1302, 0, TYPE_FLOAT, TYPE_INT16U, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_GRID_PHASE_VOLT + 2 }, // 电网C相电压
    {1303, 0, TYPE_FLOAT, TYPE_INT16U, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_AB_VOLT },          // 逆变器Uab
    {1304, 0, TYPE_FLOAT, TYPE_INT16U, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_BC_VOLT },          // 逆变器Ubc
    {1305, 0, TYPE_FLOAT, TYPE_INT16U, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_CA_VOLT },          // 逆变器Uca
    {1306, 0, TYPE_FLOAT, TYPE_INT16U, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_PHASE_VOLT },  // 逆变器A相电压
    {1307, 0, TYPE_FLOAT, TYPE_INT16U, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_PHASE_VOLT + 1 },  // 逆变器B相电压
    {1308, 0, TYPE_FLOAT, TYPE_INT16U, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_PV_PHASE_VOLT + 2 },  // 逆变器C相电压
    {1309, 1, TYPE_FLOAT, TYPE_INT16S, 0, 0, 2, TYPE_ANA ,  },//预留
    {1310, 0, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_INTERNAL_TEMPER },        // 机内温度1
    {1311, 0, TYPE_FLOAT, TYPE_INT16U, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_BUS_VOLT },             // 母线电压
    {1312, 0, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_POS_BUS_VOLT },         // 正母线电压
    {1313, 0, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_NEG_BUS_VOLT },         // 负母线电压
    {1314, 0, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_REDIATOR_TEMPER },      // 散热器1温度
    {1315, 0, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_REDIATOR_TEMPER + 1 },      // 散热器2温度
    {1316, 0, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_REDIATOR_TEMPER + 2 },      // 散热器3温度
    {1317, 1, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA ,},      // 散热器4温度(预留)
    {1318, 1, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA ,},      // 散热器5温度（预留）
    {1319, 0, TYPE_FLOAT, TYPE_INT16S, 4, 0, 2, TYPE_ANA , DAC_DATA_ID_RES_CURR_AC_COMPONENT},//残余电流交流分量
    {1320, 0, TYPE_FLOAT, TYPE_INT16S, 4, 0, 2, TYPE_ANA , DAC_DATA_ID_RES_CURR_DC_COMPONENT},//残余电流直流分量
    {1321, 0, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_ANA , DAC_DATA_ID_PCB_TEMPER},//PCB温度
    {1322, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2,  TYPE_ANA , DAC_DATA_ID_ACTIVE_POWER_CONTROL_MODE_CURR},//有功控制模式
    {1323, 0, TYPE_FLOAT, TYPE_INT32U, 3, 0, 4,  TYPE_ANA , DAC_DATA_ID_ACTIVE_POWER_DERATING_SETTING_CURR},//有功降额设置值（固定值）
    {1325, 0, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2,  TYPE_ANA , DAC_DATA_ID_ACTIVE_POWER_DERATING_PERCENT_CURR},//有功降额百分比
    {1326, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2,  TYPE_ANA , DAC_DATA_ID_REACTIVE_POWER_COMPEN_MODE_CURR},//无功补偿模式
    {1327, 0, TYPE_FLOAT, TYPE_INT16S, 3, 0, 2,  TYPE_ANA , DAC_DATA_ID_REACTIVE_POWER_COMPEN_SETTING_FACTOR_CURR},//无功补偿设置（功率因素）
    {1328, 0, TYPE_FLOAT, TYPE_INT16S, 3, 0, 2,  TYPE_ANA , DAC_DATA_ID_REACTIVE_POWER_COMPEN_SETTING_CURR},//无功补偿设置（ Q/S）
    {1329, 0, TYPE_STRING, TYPE_STRING, 0, 0, 32,  TYPE_ANA , DAC_DATA_ID_PSC_VERSION_CURR},//psc版本信息
    {1345, 0, TYPE_STRING, TYPE_STRING, 0, 0, 32,  TYPE_ANA , DAC_DATA_ID_CSC_VERSION_CURR},//csc版本信息
    {1361, 0, TYPE_FLOAT, TYPE_INT16S, 4, 0, 2,  TYPE_ANA , DAC_DATA_ID_PV_CURR_DC_COMPONENT},//逆变A相电流直流分量
    {1362, 0, TYPE_FLOAT, TYPE_INT16S, 4, 0, 2,  TYPE_ANA , DAC_DATA_ID_PV_CURR_DC_COMPONENT + 1},//逆变B相电流直流分量
    {1363, 0, TYPE_FLOAT, TYPE_INT16S, 4, 0, 2,  TYPE_ANA , DAC_DATA_ID_PV_CURR_DC_COMPONENT + 2},//逆变C相电流直流分量
    {1364, 0, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2,  TYPE_ANA , DAC_DATA_ID_PV_CURR_AVERAGE_VALUE},     //A相电流平均值
    {1365, 0, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2,  TYPE_ANA , DAC_DATA_ID_PV_CURR_AVERAGE_VALUE + 1}, //B相电流平均值
    {1366, 0, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2,  TYPE_ANA , DAC_DATA_ID_PV_CURR_AVERAGE_VALUE + 2}, //C相电流平均值
    {1367, 0, TYPE_INT16U,TYPE_INT16U, 0, 0, 2,  TYPE_ANA , DAC_DATA_ID_DELAY_UPDATE_STATUS },      //延迟升级状态
    {1368, 0, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2,  TYPE_ANA , DAC_DATA_ID_OVERSAMPLE_CURRENT},     //MPPT1过采样电流
    {1369, 0, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2,  TYPE_ANA , DAC_DATA_ID_OVERSAMPLE_CURRENT + 1}, //MPPT2过采样电流
    {1370, 0, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2,  TYPE_ANA , DAC_DATA_ID_OVERSAMPLE_CURRENT + 2}, //MPPT3过采样电流
    {1371, 0, TYPE_FLOAT, TYPE_INT16S, 2, 0, 2,  TYPE_ANA , DAC_DATA_ID_OVERSAMPLE_CURRENT + 3}, //MPPT4过采样电流
    {1372, 0, TYPE_STRING, TYPE_STRING, 0, 0, 8,  TYPE_ANA , DAC_DATA_ID_MASTER_CTRL_PROTOCOL_VERSION},//主控协议版本
    {1376, 0, TYPE_STRING, TYPE_STRING, 0, 0, 8,  TYPE_ANA , DAC_DATA_ID_SLAVE_CTRL_PROTOCOL_VERSION },//辅控协议版本
    {1380, 0, TYPE_STRING, TYPE_STRING, 0, 0, 8,  TYPE_ANA , DAC_DATA_ID_CPLD_PROTOCOL_VERSION       },//cpld协议版本
    {1384, 0, TYPE_INT16U,TYPE_INT16U,  0, 0, 2,  TYPE_ANA , DAC_DATA_ID_FAULT_STRING_WITH_LOW_INSULAT_IMPEDANCE },//绝缘阻抗低可能故障组串
    {1385, 0, TYPE_STRING, TYPE_STRING, 0, 0, 32,  TYPE_ANA , DAC_DATA_ID_PRODUCT_MODEL},//产品型号
    {1401, 0, TYPE_STRING, TYPE_STRING, 0, 0, 32,  TYPE_ANA , DAC_DATA_ID_POWER_HARDWARE_VERSION},//硬件版本


    {1500, 0, TYPE_STRING, TYPE_STRING, 0, 12, 12,  TYPE_ANA ,  DAC_DATA_ID_MASTER_CTRL_SOFTWARE_VER }, // 主控软件版本
    {1506, 0, TYPE_DATE_T, TYPE_INT16U, 0, 4, 2,    TYPE_ANA ,  DAC_DATA_ID_MASTER_CTRL_SOFTWARE_VER_DATE },  // 主控版本日期-年
    {1507, 0, TYPE_DATE_T, TYPE_INT16U, 0, 2, 2,    TYPE_ANA ,  DAC_DATA_ID_MASTER_CTRL_SOFTWARE_VER_DATE }, // 主控版本日期-月
    {1508, 0, TYPE_DATE_T, TYPE_INT16U, 0, 2, 2,    TYPE_ANA ,  DAC_DATA_ID_MASTER_CTRL_SOFTWARE_VER_DATE },   // 主控版本日期-日
    {1509, 0, TYPE_STRING, TYPE_STRING, 0, 12, 12, TYPE_ANA ,  DAC_DATA_ID_AUXI_CTRL_SOFTWARE_VER }, // 辅控软件版本
    {1515, 0, TYPE_DATE_T, TYPE_INT16U, 0, 4, 2,    TYPE_ANA ,  DAC_DATA_ID_AUXI_CTRL_SOFTWARE_VER_DATE },  // 辅控版本日期-年
    {1516, 0, TYPE_DATE_T, TYPE_INT16U, 0, 2, 2,    TYPE_ANA ,  DAC_DATA_ID_AUXI_CTRL_SOFTWARE_VER_DATE }, // 辅控版本日期-月
    {1517, 0, TYPE_DATE_T, TYPE_INT16U, 0, 2, 2,    TYPE_ANA ,  DAC_DATA_ID_AUXI_CTRL_SOFTWARE_VER_DATE },   // 辅控版本日期-日
    {1518, 0, TYPE_STRING, TYPE_STRING, 0, 12, 12, TYPE_ANA ,  DAC_DATA_ID_CPLD_SOFTWARE_VER }, // CPLD软件版本
    {1524, 0, TYPE_DATE_T, TYPE_INT16U, 0, 4, 2,    TYPE_ANA ,  DAC_DATA_ID_CPLD_SOFTWARE_VER_DATE },  // CPLD控版本日期-年
    {1525, 0, TYPE_DATE_T, TYPE_INT16U, 0, 2, 2,    TYPE_ANA ,  DAC_DATA_ID_CPLD_SOFTWARE_VER_DATE }, // CPLD控版本日期-月
    {1526, 0, TYPE_DATE_T, TYPE_INT16U, 0, 2, 2,    TYPE_ANA ,  DAC_DATA_ID_CPLD_SOFTWARE_VER_DATE },   // CPLD控版本日期-日
    {1527, 0, TYPE_STRING, TYPE_STRING, 0, 20, 20,  TYPE_ANA ,  DAC_DATA_ID_MONITOR_SOFTWARE_VER }, // 监控软件版本
    {1537, 0, TYPE_DATE_T, TYPE_INT16U, 0, 2, 2,    TYPE_ANA ,  DAC_DATA_ID_MONITOR_SOFTWARE_VER_DATE },  // 监控版本日期-年
    {1538, 0, TYPE_DATE_T, TYPE_INT16U, 0, 2, 2,    TYPE_ANA ,  DAC_DATA_ID_MONITOR_SOFTWARE_VER_DATE }, // 监控版本日期-月
    {1539, 0, TYPE_DATE_T, TYPE_INT16U, 0, 2, 2,    TYPE_ANA ,  DAC_DATA_ID_MONITOR_SOFTWARE_VER_DATE },   // 监控版本日期-日
    {1540, 0, TYPE_INT16U, TYPE_INT16U, 0,  0,  2,  TYPE_ANA , DAC_DATA_ID_MANUFACTURER_ID },   // 制造商ID
    {1541, 0, TYPE_INT16U, TYPE_INT16U, 0,  0,  2,  TYPE_ANA , DAC_DATA_ID_MANUFACTURER_ADDRESS }, // 制造商地址
    {1542, 0, TYPE_STRING, TYPE_STRING, 0,  12, 12, TYPE_PARA , DAC_PARA_ID_MACHINE_BARCODE_OFFSET },   // 整机序列号
    {1548, 0, TYPE_INT16U, TYPE_INT16U, 0,  0,  2,  TYPE_ANA , DAC_DATA_ID_AFCI_LOG_SAVE_NUM }, // afcilog暂存条数
    {1549, 0, TYPE_INT16U, TYPE_INT16U, 0,  0,  2,  TYPE_ANA , DAC_DATA_ID_AFCI_LOG_SAVED_NUM }, // afcilog已存条数
    {1550, 1, TYPE_STRING, TYPE_STRING, 0,  14, 14, TYPE_ANA ,  }, // 预留
    
    {1557, 0, TYPE_DATE_T, TYPE_INT16U, 0,  4,  2,  TYPE_ANA ,  DAC_DATA_ID_MANUFACTURER_DATE },  // 生产日期-年
    {1558, 0, TYPE_DATE_T, TYPE_INT16U, 0,  2,  2,  TYPE_ANA ,  DAC_DATA_ID_MANUFACTURER_DATE }, // 生产日期-月
    {1559, 0, TYPE_DATE_T, TYPE_INT16U, 0,  2,  2,  TYPE_ANA ,  DAC_DATA_ID_MANUFACTURER_DATE },   // 生产日期-日
    {1560, 0, TYPE_FLOAT, TYPE_INT32U, 3,  4,  4,  TYPE_ANA , DAC_DATA_ID_RATED_POWER_FAC_PARAM }, // 额定功率（厂家）
    {1562, 0, TYPE_FLOAT, TYPE_INT32U, 3,  4,  4,  TYPE_ANA , DAC_DATA_ID_MAX_ACTIVE_POWER_FAC_PARAM },  //最大有功功率（厂家）
    {1564, 0, TYPE_FLOAT, TYPE_INT32U, 3,  4,  4,  TYPE_ANA , DAC_DATA_ID_MAX_APPARENT_POWER_FAC_PARAM }, // 最大视在功率（厂家）
    {1566, 0, TYPE_FLOAT, TYPE_INT32U, 3,  4,  4,  TYPE_ANA , DAC_DATA_ID_MAX_REACTIVE_POWER_FAC_PARAM },   //最大无功功率（厂家）
    {1568, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CA_CERT_START_YEAR_TIME },  //  网管CA证书开始时间
    {1569, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CA_CERT_START_MONTH_TIME  },//  网管CA证书开始时间
    {1570, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CA_CERT_START_DAY_TIME    }, // 网管CA证书开始时间
    {1571, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CA_CERT_START_HOUR_TIME   }, // 网管CA证书开始时间
    {1572, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CA_CERT_START_MINUTE_TIME }, // 网管CA证书开始时间
    {1573, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CA_CERT_START_SECOND_TIME }, // 网管CA证书开始时间
    {1574, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CA_CERT_END_YEAR_TIME   },   // 网管CA证书结束时间
    {1575, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CA_CERT_END_MONTH_TIME  },   // 网管CA证书结束时间
    {1576, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CA_CERT_END_DAY_TIME    },   // 网管CA证书结束时间
    {1577, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CA_CERT_END_HOUR_TIME   },   // 网管CA证书结束时间
    {1578, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CA_CERT_END_MINUTE_TIME },   // 网管CA证书结束时间
    {1579, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CA_CERT_END_SECOND_TIME },   // 网管CA证书结束时间
    {1580, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CLIENT_CERT_START_YEAR_TIME   }, // 网管证书客户端开始时间
    {1581, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CLIENT_CERT_START_MONTH_TIME  }, // 网管证书客户端开始时间
    {1582, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CLIENT_CERT_START_DAY_TIME    }, // 网管证书客户端开始时间
    {1583, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CLIENT_CERT_START_HOUR_TIME   }, // 网管证书客户端开始时间
    {1584, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CLIENT_CERT_START_MINUTE_TIME }, // 网管证书客户端开始时间
    {1585, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CLIENT_CERT_START_SECOND_TIME }, // 网管证书客户端开始时间
    {1586, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CLIENT_CERT_END_YEAR_TIME   },   // 网管证书客户端结束时间
    {1587, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CLIENT_CERT_END_MONTH_TIME  },   // 网管证书客户端结束时间
    {1588, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CLIENT_CERT_END_DAY_TIME    },   // 网管证书客户端结束时间
    {1589, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CLIENT_CERT_END_HOUR_TIME   },   // 网管证书客户端结束时间
    {1590, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CLIENT_CERT_END_MINUTE_TIME },   // 网管证书客户端结束时间
    {1591, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CLIENT_CERT_END_SECOND_TIME },   // 网管证书客户端结束时间
    {1592, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CA_CERT_START_YEAR_TIME + 1},// 第三方网管CA证书开始时间
    {1593, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CA_CERT_START_MONTH_TIME+ 1},// 第三方网管CA证书开始时间
    {1594, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CA_CERT_START_DAY_TIME  + 1},// 第三方网管CA证书开始时间
    {1595, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CA_CERT_START_HOUR_TIME + 1},// 第三方网管CA证书开始时间
    {1596, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CA_CERT_START_MINUTE_TIME+ 1},//第三方网管CA证书开始时间
    {1597, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CA_CERT_START_SECOND_TIME+ 1},//第三方网管CA证书开始时间
    {1598, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CA_CERT_END_YEAR_TIME + 1},  // 第三方网管CA证书结束时间
    {1599, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CA_CERT_END_MONTH_TIME+ 1},  // 第三方网管CA证书结束时间
    {1600, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CA_CERT_END_DAY_TIME  + 1},  // 第三方网管CA证书结束时间
    {1601, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CA_CERT_END_HOUR_TIME + 1},  // 第三方网管CA证书结束时间
    {1602, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CA_CERT_END_MINUTE_TIME+ 1}, // 第三方网管CA证书结束时间
    {1603, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CA_CERT_END_SECOND_TIME+ 1}, // 第三方网管CA证书结束时间
    {1604, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CLIENT_CERT_START_YEAR_TIME + 1},// 第三方网管证书客户端开始时间
    {1605, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CLIENT_CERT_START_MONTH_TIME+ 1},// 第三方网管证书客户端开始时间
    {1606, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CLIENT_CERT_START_DAY_TIME  + 1},// 第三方网管证书客户端开始时间
    {1607, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CLIENT_CERT_START_HOUR_TIME + 1},// 第三方网管证书客户端开始时间
    {1608, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CLIENT_CERT_START_MINUTE_TIME+ 1},//第三方网管证书客户端开始时间
    {1609, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CLIENT_CERT_START_SECOND_TIME+ 1},//第三方网管证书客户端开始时间
    {1610, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CLIENT_CERT_END_YEAR_TIME + 1},   //第三方网管证书客户端结束时间
    {1611, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CLIENT_CERT_END_MONTH_TIME+ 1},   //第三方网管证书客户端结束时间
    {1612, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CLIENT_CERT_END_DAY_TIME  + 1},   //第三方网管证书客户端结束时间
    {1613, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CLIENT_CERT_END_HOUR_TIME + 1},   //第三方网管证书客户端结束时间
    {1614, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CLIENT_CERT_END_MINUTE_TIME+ 1},  //第三方网管证书客户端结束时间
    {1615, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_CLIENT_CERT_END_SECOND_TIME+ 1},  //第三方网管证书客户端结束时间
    {1616, 0, TYPE_STRING,TYPE_STRING,  0,   0, 16,  TYPE_ANA ,  DAC_DATA_ID_MONITOR_BOOT_VER },        // 监控BOOT版本
    {1624, 0, TYPE_DATE_T,TYPE_INT16U,  0,   4,  2,  TYPE_ANA ,  DAC_DATA_ID_MONITOR_BOOT_VER_DATE },   // 监控BOOT版本日期-年
    {1625, 0, TYPE_DATE_T,TYPE_INT16U,  0,   2,  2,  TYPE_ANA ,  DAC_DATA_ID_MONITOR_BOOT_VER_DATE },   // 监控BOOT版本日期-月
    {1626, 0, TYPE_DATE_T,TYPE_INT16U,  0,   2,  2,  TYPE_ANA ,  DAC_DATA_ID_MONITOR_BOOT_VER_DATE },   // 监控BOOT版本日期-日
    {1627, 0, TYPE_INT16U,TYPE_INT16U,  0,   0,  2,  TYPE_ANA ,  DAC_DATA_ID_USER_ACCOUNT_REMAIN_VALID_TIME},  // user账号剩余有效时间

    // {1700, 0, TYPE_STRING, TYPE_STRING, 0,  32,  32,  TYPE_ANA ,  DAC_DATA_ID_4G_COMMUNICATION_ROD_MODEL }, //4g通讯棒型号 
    // {1716, 0, TYPE_STRING, TYPE_STRING, 0,  32,  32,  TYPE_ANA ,  DAC_DATA_ID_4G_COMMUNICATION_ROD_SERIAL_NUMBER }, // 4g通讯棒序列号
    // {1732, 0, TYPE_STRING, TYPE_STRING, 0,  32,  32,  TYPE_ANA ,  DAC_DATA_ID_4G_COMMUNICATION_ROD_SOFT_VER }, // 4g通讯棒软件版本
    // {1748, 0, TYPE_STRING, TYPE_STRING, 0,  32,  32,  TYPE_ANA ,  DAC_DATA_ID_4G_COMMUNICATION_ROD_ICCID_NUMBER }, // 4g通讯棒ICCID卡号
    // {1764, 0, TYPE_STRING, TYPE_STRING, 0,  16,  16,  TYPE_ANA ,  DAC_DATA_ID_4G_OPERATOR_NAME }, // 4g通讯棒运营商名称
    // {1772, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2,TYPE_ANA ,  DAC_DATA_ID_4G_SIGNAL_STRENGTH }, // 4g通讯棒信号强度

    {2000, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_SYS_TIME_YEAR_OFFSET }, // 系统时间:年
    {2001, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_SYS_TIME_MONTH_OFFSET },    // 系统时间:月
    {2002, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_SYS_TIME_DAY_OFFSET },         // 系统时间:日
    {2003, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_SYS_TIME_HOUR_OFFSET },         // 系统时间:时
    {2004, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_SYS_TIME_MINUTE_OFFSET },       // 系统时间:分
    {2005, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_SYS_TIME_SECOND_OFFSET },         // 系统时间:秒

    {2006, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_GRID_STANDARD_CODE_OFFSET,     DC_AC_SET_POWER_GRID_PARA_DATA},            // 电网标准码
    {2007, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_GRID_ISOLATION_SETTING_OFFSET, DC_AC_SET_POWER_GRID_PARA_DATA},                   // 隔离设置
    {2008, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_OUTPUT_MODE_OFFSET,            DC_AC_SET_POWER_GRID_PARA_DATA},                   // 输出方式
    {2009, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_ATUO_START_AFTER_GRID_FAULT_RECOVER_OFFSET, DC_AC_SET_POWER_GRID_PARA_DATA},                 // 电网故障恢复自动开机
    {2010, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_CONNECT_TIME_AFTER_GRID_FAULT_RECOVER_OFFSET, DC_AC_SET_POWER_GRID_PARA_DATA},    // 电网故障恢复并网时间
    {2011, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_DELAY_START_TIME_AFTER_GRID_FAULT_OFFSET, DC_AC_SET_POWER_GRID_PARA_DATA},    // 电网故障后缓启动时间
    {2012, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 0, 2, TYPE_PARA , DAC_PARA_ID_UPPER_LIMIT_GRID_RECONN_VOLT_OFFSET, DC_AC_SET_POWER_GRID_PARA_DATA}, // 电网重连电压上限
    {2013, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 0, 2, TYPE_PARA , DAC_PARA_ID_LOWER_LIMIT_GRID_RECONN_VOLT_OFFSET, DC_AC_SET_POWER_GRID_PARA_DATA },      // 电网重连电压下限
    {2014, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 0, 2, TYPE_PARA , DAC_PARA_ID_UPPER_LIMIT_GRID_RECONN_FREQ_OFFSET, DC_AC_SET_POWER_GRID_PARA_DATA },      // 电网重连频率上限
    {2015, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 0, 2, TYPE_PARA , DAC_PARA_ID_LOWER_LIMIT_GRID_RECONN_FREQ_OFFSET, DC_AC_SET_POWER_GRID_PARA_DATA },      // 电网重连频率下限
    {2016, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_PQ_MODE_OFFSET, DC_AC_SET_POWER_GRID_PARA_DATA },                         // PQ模式
    {2017, 0, TYPE_FLOAT, TYPE_INT16U, 1, 0, 2, TYPE_PARA , DAC_PARA_ID_LOWER_LIMIT_GRID_CONN_START_VOLT_OFFSET, DC_AC_SET_POWER_GRID_PARA_DATA },  // 并网开机电压下限
    {2018, 0, TYPE_FLOAT, TYPE_INT16U, 1, 0, 2, TYPE_PARA , DAC_PARA_ID_UPPER_LIMIT_GRID_CONN_START_FREQ_OFFSET, DC_AC_SET_POWER_GRID_PARA_DATA }, // 并网开机频率上限
    {2019, 0, TYPE_FLOAT, TYPE_INT16U, 1, 0, 2, TYPE_PARA , DAC_PARA_ID_LOWER_LIMIT_GRID_CONN_START_FREQ_OFFSET, DC_AC_SET_POWER_GRID_PARA_DATA }, // 并网开机频率下限
    {2020, 0, TYPE_FLOAT, TYPE_INT16U, 1, 0, 2, TYPE_PARA , DAC_PARA_ID_RATED_GRID_VOLT_OFFSET, DC_AC_SET_POWER_GRID_PARA_DATA}, // 额定电网电压
    {2021, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_POWER_ON_OFF_OFFSET, DC_AC_SET_REMOTE_CTRL_PARA_DATA}, // 开关机
    {2022, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_PHASE_SEQ_ADAPTIVE_ENABLE_OFFSET, DC_AC_SET_REMOTE_CTRL_PARA_DATA}, // 相序自适应使能
    {2023, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_MPPT_CONNECT_TYPE_OFFSET, DC_AC_SET_REMOTE_CTRL_PARA_DATA}, // mppt连接模式
    {2024, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_EN_LEAK_CURR_SELF_CHECK_OFFSET, DC_AC_SET_REMOTE_CTRL_PARA_DATA}, // 漏电流使能
    {2025, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_RCD_BOOST_ENABLE_OFFSET,DC_AC_SET_REMOTE_CTRL_PARA_DATA}, // RCD增强模式使能
    {2026, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_STANDBY_TIME_OFFSET, DC_AC_SET_CHARA_PARA_DATA},              // 待机时间
    {2027, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_BOOT_SOFT_START_TIME_OFFSET, DC_AC_SET_CHARA_PARA_DATA},      // 开机软启动时间
    {2028, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_ISLAND_DETECT_ENABLE_OFFSET, DC_AC_SET_CHARA_PARA_DATA},            // 孤岛检测使能
    {2029, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_ENABLE_GRID_VOLT_PROT_SHEILD_DURING_VRT_OFFSET, DC_AC_SET_CHARA_PARA_DATA}, // VRT时电网电压保护屏蔽使能
    {2030, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 0, 2, TYPE_PARA , DAC_PARA_ID_VRT_EXIT_HYSTERESIS_THRESHLOD_OFFSET, DC_AC_SET_CHARA_PARA_DATA},      // VRT退出滞环阈值
    {2031, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_GRID_FAULT_ZERO_CURR_MODE_OFFSET, DC_AC_SET_CHARA_PARA_DATA},       // 电网故障零电流模式
    {2032, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_MPPT_ENABLE_OFFSET, DC_AC_SET_MPPT_PARA_DATA},            // MPPT使能
    {2033, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_MPPT_SCAN_INTERVAL_OFFSET, DC_AC_SET_MPPT_PARA_DATA}, // MPPT扫描间隔
    {2034, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA ,  DAC_PARA_ID_LVRT_ENABLE_OFFSET, DC_AC_SET_LVRT_PARA_DATA},                          // LVRT使能
    {2035, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA ,  DAC_PARA_ID_LVRT_TRIGG_THRESHOLD_OFFSET,DC_AC_SET_LVRT_PARA_DATA},                 // LVRT触发阈值
    {2036, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA ,  DAC_PARA_ID_LVRT_POS_SEQ_REACTIVE_POWER_COMPEN_FACTOR_OFFSET, DC_AC_SET_LVRT_PARA_DATA}, // LVRT正序无功补偿因子
    {2037, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA ,  DAC_PARA_ID_LVRT_NEG_SEQ_REACTIVE_POWER_COMPEN_FACTOR_OFFSET, DC_AC_SET_LVRT_PARA_DATA}, // LVRT负序无功补偿因子
    {2038, 0, TYPE_INT16U,TYPE_INT16U,  0, 0, 2, TYPE_PARA ,  DAC_PARA_ID_LVRT_REACTIVE_CURR_LIMIT_PERCENT_OFFSET, DC_AC_SET_LVRT_PARA_DATA},  // LVRT无功电流限幅百分比
    {2039, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA ,  DAC_PARA_ID_LVRT_ZERO_CURR_MODE_THRESHOLD_OFFSET, DC_AC_SET_LVRT_PARA_DATA},       // LVRT零电流模式阈值
    {2040, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA ,  DAC_PARA_ID_LVRT_MODE_OFFSET, DC_AC_SET_LVRT_PARA_DATA},                            // LVRT模式
    {2041, 0, TYPE_FLOAT, TYPE_INT16U,  2, 0, 2, TYPE_PARA ,  DAC_PARA_ID_LVRT_ACTIVE_CURR_MAINTEN_COEFFICIENT_OFFSET, DC_AC_SET_LVRT_PARA_DATA},    // LVRT有功电流维持系数
    {2042, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_WEAK_GRID_ENABLE_OFFSET, DC_AC_SET_REMOTE_CTRL_PARA_DATA}, // 弱电网使能
    {2043, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_WATER_INGRESS_ENABLE_OFFSET, DC_AC_SET_REMOTE_CTRL_PARA_DATA}, // 进水检测使能
    {2044, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_CHARACTER_CURVE_POINT_OFFSET, DC_AC_SET_LVRT_CC_PARA_DATA},                 // 特征曲线点
    {2045, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET, DC_AC_SET_LVRT_CC_PARA_DATA},         // 时间1
    {2046, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_VOLT_PERCENT_OFFSET, DC_AC_SET_LVRT_CC_PARA_DATA},  // 电压百分比1
    {2047, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET + 1, DC_AC_SET_LVRT_CC_PARA_DATA },         // 时间2
    {2048, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_VOLT_PERCENT_OFFSET + 1, DC_AC_SET_LVRT_CC_PARA_DATA},  // 电压百分比2
    {2049, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET + 2, DC_AC_SET_LVRT_CC_PARA_DATA },         // 时间3
    {2050, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_VOLT_PERCENT_OFFSET + 2, DC_AC_SET_LVRT_CC_PARA_DATA},  // 电压百分比3
    {2051, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET + 3, DC_AC_SET_LVRT_CC_PARA_DATA },         // 时间4
    {2052, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_VOLT_PERCENT_OFFSET + 3, DC_AC_SET_LVRT_CC_PARA_DATA},  // 电压百分比4
    {2053, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET + 4, DC_AC_SET_LVRT_CC_PARA_DATA },         // 时间5
    {2054, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_VOLT_PERCENT_OFFSET + 4 , DC_AC_SET_LVRT_CC_PARA_DATA},  // 电压百分比5
    {2055, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET + 5, DC_AC_SET_LVRT_CC_PARA_DATA },         // 时间6
    {2056, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_VOLT_PERCENT_OFFSET + 5, DC_AC_SET_LVRT_CC_PARA_DATA},  // 电压百分比6
    {2057, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET + 6, DC_AC_SET_LVRT_CC_PARA_DATA },         // 时间7
    {2058, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_VOLT_PERCENT_OFFSET + 6, DC_AC_SET_LVRT_CC_PARA_DATA},  // 电压百分比7
    {2059, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET + 7, DC_AC_SET_LVRT_CC_PARA_DATA },         // 时间8
    {2060, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_VOLT_PERCENT_OFFSET + 7, DC_AC_SET_LVRT_CC_PARA_DATA},  // 电压百分比8
    {2061, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET + 8, DC_AC_SET_LVRT_CC_PARA_DATA },         // 时间9
    {2062, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_VOLT_PERCENT_OFFSET + 8, DC_AC_SET_LVRT_CC_PARA_DATA},  // 电压百分比9
    {2063, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET + 9, DC_AC_SET_LVRT_CC_PARA_DATA },        // 时间10
    {2064, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_VOLT_PERCENT_OFFSET + 9, DC_AC_SET_LVRT_CC_PARA_DATA}, // 电压百分比10
    {2065, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_HVRT_ENABLE_OFFSET, DC_AC_SET_HVRT_PARA_DATA}, // HVRT使能
    {2066, 0, TYPE_FLOAT, TYPE_INT16U, 1,  0, 2, TYPE_PARA , DAC_PARA_ID_HVRT_TRIGG_THRESHOLD_OFFSET, DC_AC_SET_HVRT_PARA_DATA}, //HVRT触发阈值
    {2067, 0, TYPE_FLOAT, TYPE_INT16U, 1,  0, 2, TYPE_PARA , DAC_PARA_ID_HVRT_POS_SEQ_REACTIVE_POWER_COMPEN_FACTOR_OFFSET, DC_AC_SET_HVRT_PARA_DATA}, // HVRT正序无功补偿因子
    {2068, 0, TYPE_FLOAT, TYPE_INT16U, 1,  0, 2, TYPE_PARA , DAC_PARA_ID_HVRT_NEG_SEQ_REACTIVE_POWER_COMPEN_FACTOR_OFFSET, DC_AC_SET_HVRT_PARA_DATA},   // HVRT负序无功补偿因子
    {2069, 0, TYPE_FLOAT, TYPE_INT32U, 3, 0, 4, TYPE_PARA , DAC_PARA_ID_SHUTDOWN_GRADIENT_OFFSET,DC_AC_SET_POWER_REGULATION_PARA_DATA}, // 关机梯度
    {2071, 0, TYPE_INT16U, TYPE_INT16U,  0, 0, 2, TYPE_PARA , DAC_PARA_ID_PID_REPAIR_ENABLE_OFFSET, DC_AC_SET_PID_PARA_DATA},                        // PID修复使能
    {2072, 0, TYPE_INT16U, TYPE_INT16U,  0, 0, 2, TYPE_PARA , DAC_PARA_ID_NIGHT_PID_PROT_OFFSET, DC_AC_SET_PID_PARA_DATA },                        // 夜间PID保护
    {2073, 0, TYPE_INT16U, TYPE_INT16U,  0, 0, 2, TYPE_PARA , DAC_PARA_ID_AFCI_DETECT_ON_OFF_OFFSET, DC_AC_SET_AFCI_PARA_DATA},                      // AFCI检测开/关
    {2074, 0, TYPE_INT16U, TYPE_INT16U,  0, 0, 2, TYPE_PARA , DAC_PARA_ID_AFCI_CHECK_DETECTION_SENSITIVITY_OFFSET, DC_AC_SET_AFCI_PARA_DATA},                   // AFCI检测灵敏度
    {2075, 0, TYPE_INT16U, TYPE_INT16U,  0, 0, 2, TYPE_PARA , DAC_PARA_ID_REMOTE_POWER_DISPATCH_ENABLE_DISABLE_OFFSET, DC_AC_SET_POWER_REGULATION_PARA_DATA},       // 远程功率调度使能/禁止
    {2076, 0, TYPE_INT32U, TYPE_INT32U,  0, 0, 4, TYPE_PARA , DAC_PARA_ID_SHCED_INSTRUCT_MAINTEN_TIME_OFFSET, DC_AC_SET_POWER_REGULATION_PARA_DATA},  // 调度指令维持时间
    {2078, 0, TYPE_FLOAT, TYPE_INT32U,   3, 0, 4, TYPE_PARA , DAC_PARA_ID_ACTIVE_POWER_REFERENCE_OFFSET, DC_AC_SET_POWER_REGULATION_PARA_DATA},      // 有功功率基准
    {2080, 0, TYPE_FLOAT, TYPE_INT32U,   3, 0, 4, TYPE_PARA , DAC_PARA_ID_APPARNET_POWER_REFERENCE_OFFSET, DC_AC_SET_POWER_REGULATION_PARA_DATA}, // 视在功率基准
    {2082, 0, TYPE_FLOAT, TYPE_INT32U,   3, 0, 4, TYPE_PARA , DAC_PARA_ID_MAX_APPARENT_POWER_PARAM_OFFSET, DC_AC_SET_POWER_REGULATION_PARA_DATA}, // 视在功率最大值
    {2084, 0, TYPE_FLOAT, TYPE_INT32U,   3, 0, 4, TYPE_PARA , DAC_PARA_ID_MAX_ACTIVE_POWER_PARAM_OFFSET, DC_AC_SET_POWER_REGULATION_PARA_DATA},      // 有功功率最大值
    {2086, 0, TYPE_INT16U, TYPE_INT16U,  0, 0, 2, TYPE_PARA , DAC_PARA_ID_POWER_LIMIT_ZERO_PERCENT_SHUTDOWN_OFFSET, DC_AC_SET_POWER_REGULATION_PARA_DATA},    // 限功率0%关机
    {2087, 0, TYPE_INT16U, TYPE_INT16U,  0, 0, 2, TYPE_PARA , DAC_PARA_ID_NIGHT_REACTIVE_ENABLE_OFFSET, DC_AC_SET_POWER_REGULATION_PARA_DATA},       // 夜间无功使能
    {2088, 0, TYPE_FLOAT, TYPE_INT32S,   3, 0, 4, TYPE_PARA , DAC_PARA_ID_NIGTH_REACTIVE_POWER_COMPEN_OFFSET, DC_AC_SET_POWER_REGULATION_PARA_DATA}, // 夜间无功补偿(kVar)
    {2090, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2,   TYPE_PARA , DAC_PARA_ID_ALTITUDE_OFFSET, DC_AC_SET_OTHER_PPARA_DATA},           // 海拔高度
    {2091, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2,   TYPE_PARA , DAC_PARA_ID_AFCI_SAVE_ENABLE_OFFSET, DC_AC_SET_AFCI_PARA_DATA},           // AFCI数据保存使能
    {2092, 0, TYPE_INT16U,  TYPE_INT16U, 0,  0, 2, TYPE_PARA , DAC_PARA_ID_ENABLE_COMMU_CHAIN_FAIL_PROT_OFFSET },              // 通信断链失效保护使能
    {2093, 0, TYPE_INT16U,  TYPE_INT16U, 0,  0, 2, TYPE_PARA , DAC_PARA_ID_COMMU_BROKEN_CHAIN_DETECT_TIME_OFFSET },         // 通信断链检测时间
    {2094, 0, TYPE_INT16U,  TYPE_INT16U, 0,  0, 2, TYPE_PARA , DAC_PARA_ID_FAIL_PROT_ACTIVE_POWER_MODE_OFFSET },                 // 失效保护有功功率模式
    {2095, 0, TYPE_FLOAT,   TYPE_INT16U, 1,  0, 2, TYPE_PARA , DAC_PARA_ID_ACTIVE_POWER_LIMIT_FAIL_POWER_PERCENT_OFFSET }, // 失效保护有功功率限值（%）
    {2096, 0, TYPE_FLOAT,   TYPE_INT16U, 1,  0, 2, TYPE_PARA , DAC_PARA_ID_ACTIVE_POWER_LIMIT_FAIL_PROT_VALUE_OFFSET },       // 失效保护有功功率限值（kW）
    {2097, 0, TYPE_INT16U,  TYPE_INT16U, 0,  0, 2, TYPE_PARA , DAC_PARA_ID_FAIL_PROT_REACTIVE_POWER_MODE_OFFSET },                // 失效保护无功功率模式
    {2098, 0, TYPE_FLOAT,   TYPE_INT16S, 3,  0, 2, TYPE_PARA , DAC_PARA_ID_FAIL_PROT_REACTIVE_LIMIT_VALUE_OFFSET },      // 失效保护无功功率限值(PF)
    {2099, 0, TYPE_FLOAT,   TYPE_INT16S, 3,  0, 2, TYPE_PARA , DAC_PARA_ID_FAIL_PROT_REACTIVE_POWER_LIMIT_PROT_OFFSET },     // 失效保护无功功率限值(Q/S)
    {2100, 0, TYPE_FLOAT, TYPE_INT32U,  3, 0, 4, TYPE_PARA ,   DAC_PARA_ID_GRID_ACTIVE_POWER_CHANGE_OFFSET,DC_AC_SET_ACT_REGULATION_PARA_DATA},  // 有功功率变化梯度
    {2102, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA ,   DAC_PARA_ID_ACTIVE_POWER_CONTROL_MODE_OFFSET, DC_AC_SET_ACT_REGULATION_PARA_DATA},               // 有功控制模式
    {2103, 0, TYPE_FLOAT, TYPE_INT32U,  3, 0, 4, TYPE_PARA ,   DAC_PARA_ID_ACTIVE_POWER_DERATING_SETTING_OFFSET, DC_AC_SET_ACT_REGULATION_PARA_DATA},        // 有功降额设置（固定值）
    {2105, 0, TYPE_FLOAT, TYPE_INT16S,  1, 0, 2, TYPE_PARA ,   DAC_PARA_ID_ACTIVE_POWER_DERATING_PERCENT_OFFSET, DC_AC_SET_ACT_REGULATION_PARA_DATA}, // 有功降额百分比
    {2106, 0, TYPE_FLOAT, TYPE_INT32U,  3, 0, 4, TYPE_PARA ,  DAC_PARA_ID_GRID_REACTIVE_POWER_CHANGE_OFFSET, DC_AC_SET_REACT_REGULATION_PARA_DATA}, // 无功功率变化梯度
    {2108, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA ,  DAC_PARA_ID_REACTIVE_POWER_COMPEN_MODE_OFFSET, DC_AC_SET_REACT_REGULATION_PARA_DATA },        // 无功补偿模式
    {2109, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA ,  DAC_PARA_ID_REACTIVE_POWER_COMPEN_SETTING_FACTOR_OFFSET, DC_AC_SET_REACT_REGULATION_PARA_DATA},  // 无功补偿设置（功率因数）
    {2110, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA ,  DAC_PARA_ID_REACTIVE_POWER_COMPEN_SETTING_OFFSET, DC_AC_SET_REACT_REGULATION_PARA_DATA },          // 无功补偿设置（Q/S）
    {2111, 0, TYPE_INT16U,TYPE_INT16U,  0, 0, 2, TYPE_PARA ,  DAC_PARA_ID_REACTIVE_POWER_REGULATIONG_TIME_OFFSET, DC_AC_SET_REACT_REGULATION_PARA_DATA },         // 无功调节时间
    {2113, 0, TYPE_INT16U, TYPE_INT16U,  0, 0, 2, TYPE_PARA , DAC_PARA_ID_NIGHT_SLEEP_ENABLE_OFFSET, DC_AC_SET_PID_PARA_DATA },                // 夜间休眠
    {2115, 0, TYPE_INT16U, TYPE_INT16U,  0, 0, 2, TYPE_PARA , DAC_PARA_ID_NIGHT_SLEEP_WAIT_TIME_OFFSET, DC_AC_SET_PID_PARA_DATA },             // 夜间休眠等待时长


    {2200, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_QP_MODE_TRIGG_VOLT_OFFSET, DC_AC_SET_P_PN_CC_PARA_DATA},  // QP模式触发电压
    {2201, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_QP_MODE_EXIT_VOLT_OFFSET, DC_AC_SET_P_PN_CC_PARA_DATA },       // QP模式退出电压
    {2202, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_SETTING_POINT_OF_COS_CURVE_OFFSET, DC_AC_SET_P_PN_CC_PARA_DATA },         // 设置点数
    {2203, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_P_PN_AXIS_OFFSET, DC_AC_SET_P_PN_CC_PARA_DATA },       // P/Pn-1
    {2204, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_COS_AXIS_OFFSET, DC_AC_SET_P_PN_CC_PARA_DATA },        // cosφ -1
    {2205, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_P_PN_AXIS_OFFSET + 1, DC_AC_SET_P_PN_CC_PARA_DATA },       // P/Pn-2
    {2206, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_COS_AXIS_OFFSET + 1, DC_AC_SET_P_PN_CC_PARA_DATA },        // cosφ -2
    {2207, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_P_PN_AXIS_OFFSET + 2, DC_AC_SET_P_PN_CC_PARA_DATA },       // P/Pn-3
    {2208, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_COS_AXIS_OFFSET + 2, DC_AC_SET_P_PN_CC_PARA_DATA },        // cosφ -3
    {2209, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_P_PN_AXIS_OFFSET + 3, DC_AC_SET_P_PN_CC_PARA_DATA },       // P/Pn-4
    {2210, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_COS_AXIS_OFFSET + 3, DC_AC_SET_P_PN_CC_PARA_DATA },        // cosφ -4
    {2211, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_P_PN_AXIS_OFFSET + 4, DC_AC_SET_P_PN_CC_PARA_DATA },       // P/Pn-5
    {2212, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_COS_AXIS_OFFSET + 4, DC_AC_SET_P_PN_CC_PARA_DATA },        // cosφ -5
    {2213, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_P_PN_AXIS_OFFSET + 5, DC_AC_SET_P_PN_CC_PARA_DATA },       // P/Pn-6
    {2214, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_COS_AXIS_OFFSET + 5, DC_AC_SET_P_PN_CC_PARA_DATA },        // cosφ -6
    {2215, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_P_PN_AXIS_OFFSET + 6, DC_AC_SET_P_PN_CC_PARA_DATA },       // P/Pn-7
    {2216, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_COS_AXIS_OFFSET + 6, DC_AC_SET_P_PN_CC_PARA_DATA },        // cosφ -7
    {2217, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_P_PN_AXIS_OFFSET + 7, DC_AC_SET_P_PN_CC_PARA_DATA },       // P/Pn-8
    {2218, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_COS_AXIS_OFFSET + 7, DC_AC_SET_P_PN_CC_PARA_DATA },        // cosφ -8
    {2219, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_P_PN_AXIS_OFFSET + 8, DC_AC_SET_P_PN_CC_PARA_DATA },       // P/Pn-9
    {2220, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_COS_AXIS_OFFSET + 8, DC_AC_SET_P_PN_CC_PARA_DATA },        // cosφ -9
    {2221, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_P_PN_AXIS_OFFSET + 9, DC_AC_SET_P_PN_CC_PARA_DATA },      // P/Pn-10
    {2222, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_COS_AXIS_OFFSET + 9, DC_AC_SET_P_PN_CC_PARA_DATA },       // cosφ -10
    {2223, 0, TYPE_INT16U,TYPE_INT16U,  0, 0, 2, TYPE_PARA , DAC_PARA_ID_QU_SCHEDU_TRIGG_POWER_PERCENT_OFFSET, DC_AC_SET_Q_U_CC_PARA_DATA},  // Q-U调度触发功率百分比
    {2224, 0, TYPE_INT16U,TYPE_INT16U,  0, 0, 2, TYPE_PARA , DAC_PARA_ID_QU_SCHEDU_EXIT_POWER_PERCENT_OFFSET, DC_AC_SET_Q_U_CC_PARA_DATA },   // Q-U调度退出功率百分比
    {2225, 0, TYPE_FLOAT,TYPE_INT16U,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_QU_CHARACTER_CURVE_MIN_PF_LIMIT_OFFSET, DC_AC_SET_Q_U_CC_PARA_DATA },// Q-U特征曲线最小PF限值
    {2226, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_HYSTERESIS_RATIO_OFFSET, DC_AC_SET_Q_U_CC_PARA_DATA },               // 滞环比
    {2227, 0, TYPE_INT16U,TYPE_INT16U,  0, 0, 2, TYPE_PARA , DAC_PARA_ID_NUMBER_OF_POINT_FOR_QU_CURVE_OFFSET, DC_AC_SET_Q_U_CC_PARA_DATA },   // 设置点数
    {2228, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_U_UN_AXIS_OFFSET, DC_AC_SET_Q_U_CC_PARA_DATA },        // U/Un-1
    {2229, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_QS_AXIS_OFFSET, DC_AC_SET_Q_U_CC_PARA_DATA },        // Q/S-1
    {2230, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_U_UN_AXIS_OFFSET + 1, DC_AC_SET_Q_U_CC_PARA_DATA },        // U/Un-2
    {2231, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_QS_AXIS_OFFSET + 1, DC_AC_SET_Q_U_CC_PARA_DATA },        // Q/S-2
    {2232, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_U_UN_AXIS_OFFSET + 2, DC_AC_SET_Q_U_CC_PARA_DATA },        // U/Un-3
    {2233, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_QS_AXIS_OFFSET + 2, DC_AC_SET_Q_U_CC_PARA_DATA },        // Q/S-3
    {2234, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_U_UN_AXIS_OFFSET + 3, DC_AC_SET_Q_U_CC_PARA_DATA },        // U/Un-4
    {2235, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_QS_AXIS_OFFSET + 3, DC_AC_SET_Q_U_CC_PARA_DATA },        // Q/S-4
    {2236, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_U_UN_AXIS_OFFSET + 4, DC_AC_SET_Q_U_CC_PARA_DATA },        // U/Un-5
    {2237, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_QS_AXIS_OFFSET + 4, DC_AC_SET_Q_U_CC_PARA_DATA },        // Q/S-5
    {2238, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_U_UN_AXIS_OFFSET + 5, DC_AC_SET_Q_U_CC_PARA_DATA },        // U/Un-6
    {2239, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_QS_AXIS_OFFSET + 5, DC_AC_SET_Q_U_CC_PARA_DATA },        // Q/S-6
    {2240, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_U_UN_AXIS_OFFSET + 6, DC_AC_SET_Q_U_CC_PARA_DATA },        // U/Un-7
    {2241, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_QS_AXIS_OFFSET + 6, DC_AC_SET_Q_U_CC_PARA_DATA },        // Q/S-7
    {2242, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_U_UN_AXIS_OFFSET + 7, DC_AC_SET_Q_U_CC_PARA_DATA },        // U/Un-8
    {2243, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_QS_AXIS_OFFSET + 7, DC_AC_SET_Q_U_CC_PARA_DATA },        // Q/S-8
    {2244, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_U_UN_AXIS_OFFSET + 8, DC_AC_SET_Q_U_CC_PARA_DATA },        // U/Un-9
    {2245, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_QS_AXIS_OFFSET + 8 , DC_AC_SET_Q_U_CC_PARA_DATA},        // Q/S-9
    {2246, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_U_UN_AXIS_OFFSET + 9, DC_AC_SET_Q_U_CC_PARA_DATA },       // U/Un-10
    {2247, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_QS_AXIS_OFFSET + 9, DC_AC_SET_Q_U_CC_PARA_DATA },       // Q/S-10
    {2248, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_PF_VOLT_DETECT_FILTER_TIME_OFFSET, DC_AC_SET_PF_U_CC_PARA_DATA}, // PF(U)电压检测滤波时间
    {2249, 0, TYPE_INT16U,TYPE_INT16U,  0, 0, 2, TYPE_PARA , DAC_PARA_ID_NUMBER_OF_POINT_FOR_PF_CURVE_OFFSET, DC_AC_SET_PF_U_CC_PARA_DATA },      // 设置点数
    {2250, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_PF_U_AXIS_OFFSET, DC_AC_SET_PF_U_CC_PARA_DATA },      // U/Un-1
    {2251, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_PF_AXIS_OFFSET, DC_AC_SET_PF_U_CC_PARA_DATA },         // PF-1
    {2252, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_PF_U_AXIS_OFFSET + 1, DC_AC_SET_PF_U_CC_PARA_DATA },      // U/Un-2
    {2253, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_PF_AXIS_OFFSET + 1, DC_AC_SET_PF_U_CC_PARA_DATA },         // PF-2
    {2254, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_PF_U_AXIS_OFFSET + 2, DC_AC_SET_PF_U_CC_PARA_DATA },      // U/Un-3
    {2255, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_PF_AXIS_OFFSET + 2, DC_AC_SET_PF_U_CC_PARA_DATA },         // PF-3
    {2256, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_PF_U_AXIS_OFFSET + 3 , DC_AC_SET_PF_U_CC_PARA_DATA},      // U/Un-4
    {2257, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_PF_AXIS_OFFSET + 3, DC_AC_SET_PF_U_CC_PARA_DATA },         // PF-4
    {2258, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_PF_U_AXIS_OFFSET + 4, DC_AC_SET_PF_U_CC_PARA_DATA },      // U/Un-5
    {2259, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_PF_AXIS_OFFSET + 4, DC_AC_SET_PF_U_CC_PARA_DATA },         // PF-5
    {2260, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_PF_U_AXIS_OFFSET + 5, DC_AC_SET_PF_U_CC_PARA_DATA },      // U/Un-6
    {2261, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_PF_AXIS_OFFSET + 5, DC_AC_SET_PF_U_CC_PARA_DATA },         // PF-6
    {2262, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_PF_U_AXIS_OFFSET + 6, DC_AC_SET_PF_U_CC_PARA_DATA },      // U/Un-7
    {2263, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_PF_AXIS_OFFSET + 6, DC_AC_SET_PF_U_CC_PARA_DATA },         // PF-7
    {2264, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_PF_U_AXIS_OFFSET + 7, DC_AC_SET_PF_U_CC_PARA_DATA },      // U/Un-8
    {2265, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_PF_AXIS_OFFSET + 7, DC_AC_SET_PF_U_CC_PARA_DATA },         // PF-8
    {2266, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_PF_U_AXIS_OFFSET + 8, DC_AC_SET_PF_U_CC_PARA_DATA },      // U/Un-9
    {2267, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_PF_AXIS_OFFSET + 8 , DC_AC_SET_PF_U_CC_PARA_DATA},         // PF-9
    {2268, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_PF_U_AXIS_OFFSET + 9 , DC_AC_SET_PF_U_CC_PARA_DATA},     // U/Un-10
    {2269, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_PF_AXIS_OFFSET + 9, DC_AC_SET_PF_U_CC_PARA_DATA },        // PF-10
    {2270, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_GRID_CONNECT_ACTIVE_POWER_CONTROL_MODE_OFFSET },       // 并网有功功率控制模式
    {2271, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_CLOSE_LOOP_CONTROLLER_OFFSET },           // 闭环控制器
    {2272, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_RESTRICTION_METHOD_OFFSET },                   // 限制方式
    {2273, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_POWER_REGULAR_CYCLE_OFFSET }, // 功率调节周期
    {2274, 0, TYPE_FLOAT, TYPE_INT32U,  3, 0, 4, TYPE_PARA , DAC_PARA_ID_POWER_UP_THRESHOLD_OFFSET },            // 升功率阈值
    {2276, 0, TYPE_FLOAT, TYPE_INT32S,  3, 0, 4, TYPE_PARA , DAC_PARA_ID_MAX_FEED_GRID_POWER_OFFSET },                // 最大馈送电网功率(KW)
    {2278, 0, TYPE_FLOAT, TYPE_INT16U, 1, 0, 2, TYPE_PARA , DAC_PARA_ID_MAX_FEED_GRID_POWER_PERCENT_OFFSET },                // 最大馈送电网功率(%)
    {2279, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_GRID_CONNECT_REACTIVE_POWER_CONTROL_METHOD_OFFSET },  // 并网无功功率控制方式
    {2280, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_TARGET_POWER_METHOD_OFFSET },   // 目标功率因数
    {2281, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_REACTIVE_POWER_REGULAR_CYCLE_OFFSET },   // 无功调节周期
    {2282, 0, TYPE_FLOAT, TYPE_INT16U,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_REACTIVE_POWER_REGULAR_DEAD_ZONE_OFFSET },      // 无功调节死区
    {2283, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_FEED_OVER_LIMIT_PROT_SHUTDOWN_OFFSET },             // 馈电越限保护关机
    {2284, 0, TYPE_FLOAT, TYPE_INT32U,  3, 0, 4, TYPE_PARA , DAC_PARA_ID_FEED_LIMIT_PROT_SHUTDOWN_THRESHOLD_OFFSET }, // 馈电越限保护关机阈值
    {2286, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_FEED_LIMIT_PORT_SHUTDOWN_INTERVER_OFFSET }, // 馈电越限保护关机时间
    {2300, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_GRID_VOLT_IMBALANCE_PROT_POINT_OFFSET,DC_AC_SET_PROTECT_PARA_DATA},         // 电网电压不平衡保护点
    {2301, 0, TYPE_FLOAT, TYPE_INT16U,  3, 0, 2, TYPE_PARA , DAC_PARA_ID_INSULATION_IMPEDANCE_PROT_OFFSET, DC_AC_SET_PROTECT_PARA_DATA}, // 绝缘阻抗保护点
    {2302, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_TEN_MINUTE_GRID_OVER_PROT_POINT_OFFSET,DC_AC_SET_PROTECT_PARA_DATA},       // 十分钟电网过压保护点
    {2303, 0, TYPE_INT32U, TYPE_INT32U, 0, 0, 4, TYPE_PARA , DAC_PARA_ID_TEN_MINUTE_GRID_OVER_PROT_TIME_OFFSET,DC_AC_SET_PROTECT_PARA_DATA},    // 十分钟电网过压保护时间
    {2305, 1, TYPE_INT16U, TYPE_INT16U, 1, 0, 2, TYPE_PARA , }, // 预留
    {2306, 1, TYPE_INT16U, TYPE_INT16U, 1, 0, 2, TYPE_PARA , }, // 预留
    {2307, 1, TYPE_INT16U, TYPE_INT16U, 1, 0, 2, TYPE_PARA , }, // 预留
    {2308, 1, TYPE_INT16U, TYPE_INT16U, 1, 0, 2, TYPE_PARA , }, // 预留
    {2309, 1, TYPE_INT16U, TYPE_INT16U, 1, 0, 2, TYPE_PARA , }, // 预留
    {2310, 1, TYPE_INT16U, TYPE_INT16U, 1, 0, 2, TYPE_PARA , }, // 预留
    {2311, 0, TYPE_FLOAT, TYPE_INT16U, 1, 0,  2, TYPE_PARA , DAC_PARA_ID_OVER_VOLT_PROT_POINT_GRID_OFFSET,DC_AC_SET_PROTECT_L1_PARA_DATA}, // 一级电网过压保护点
    {2312, 0, TYPE_INT32U, TYPE_INT32U,0, 0,  4, TYPE_PARA , DAC_PARA_ID_OVER_VOLT_PROT_TIME_GRID_OFFSET, DC_AC_SET_PROTECT_L1_PARA_DATA},  // 一级电网过压保护时间
    {2314, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_UNDER_VOLT_PROT_POINT_GRID_OFFSET, DC_AC_SET_PROTECT_L1_PARA_DATA}, // 一级电网欠压保护点
    {2315, 0, TYPE_INT32U, TYPE_INT32U, 0, 0, 4, TYPE_PARA , DAC_PARA_ID_UNDER_VOLT_PROT_TIME_GRID_OFFSET, DC_AC_SET_PROTECT_L1_PARA_DATA}, // 一级电网欠压保护时间高16位
    {2317, 0, TYPE_FLOAT, TYPE_INT16U,  2, 0, 2, TYPE_PARA , DAC_PARA_ID_OVER_FREQ_PROT_POINT_OFFSET, DC_AC_SET_PROTECT_L1_PARA_DATA}, // 一级过频保护点
    {2318, 0, TYPE_INT32U, TYPE_INT32U, 0, 0, 4, TYPE_PARA , DAC_PARA_ID_OVER_FREQ_PROT_TIME_OFFSET, DC_AC_SET_PROTECT_L1_PARA_DATA},   // 一级过频保护时间
    {2320, 0, TYPE_FLOAT, TYPE_INT16U,  2, 0, 2, TYPE_PARA , DAC_PARA_ID_UNDER_FREQ_PROT_POINT_OFFSET, DC_AC_SET_PROTECT_L1_PARA_DATA}, // 一级欠频保护点
    {2321, 0, TYPE_INT32U, TYPE_INT32U, 0, 0, 4, TYPE_PARA , DAC_PARA_ID_UNDER_FREQ_PROT_TIME_OFFSET, DC_AC_SET_PROTECT_L1_PARA_DATA},   // 一级欠频保护时间高16位
    {2323, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_OVER_VOLT_PROT_POINT_GRID_OFFSET + 1, DC_AC_SET_PROTECT_L2_PARA_DATA}, // 二级电网过压保护点
    {2324, 0, TYPE_INT32U, TYPE_INT32U, 0, 0, 4, TYPE_PARA , DAC_PARA_ID_OVER_VOLT_PROT_TIME_GRID_OFFSET + 1, DC_AC_SET_PROTECT_L2_PARA_DATA },  // 二级电网过压保护时间高16位
    {2326, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_UNDER_VOLT_PROT_POINT_GRID_OFFSET + 1, DC_AC_SET_PROTECT_L2_PARA_DATA }, // 二级电网欠压保护点
    {2327, 0, TYPE_INT32U, TYPE_INT32U, 0, 0, 4, TYPE_PARA , DAC_PARA_ID_UNDER_VOLT_PROT_TIME_GRID_OFFSET + 1, DC_AC_SET_PROTECT_L2_PARA_DATA}, // 二级电网欠压保护时间高16位
    {2329, 0, TYPE_FLOAT, TYPE_INT16U,  2, 0, 2, TYPE_PARA , DAC_PARA_ID_OVER_FREQ_PROT_POINT_OFFSET + 1, DC_AC_SET_PROTECT_L2_PARA_DATA }, // 二级过频保护点
    {2330, 0, TYPE_INT32U, TYPE_INT32U, 0, 0, 4, TYPE_PARA , DAC_PARA_ID_OVER_FREQ_PROT_TIME_OFFSET + 1, DC_AC_SET_PROTECT_L2_PARA_DATA },   // 二级过频保护时间高16位
    {2332, 0, TYPE_FLOAT, TYPE_INT16U,  2, 0, 2, TYPE_PARA , DAC_PARA_ID_UNDER_FREQ_PROT_POINT_OFFSET + 1, DC_AC_SET_PROTECT_L2_PARA_DATA}, // 二级欠频保护点
    {2333, 0, TYPE_INT32U, TYPE_INT32U, 0, 0, 4, TYPE_PARA , DAC_PARA_ID_UNDER_FREQ_PROT_TIME_OFFSET + 1, DC_AC_SET_PROTECT_L2_PARA_DATA },   // 二级欠频保护时间高16位
    {2335, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_OVER_VOLT_PROT_POINT_GRID_OFFSET + 2, DC_AC_SET_PROTECT_L3_PARA_DATA}, // 三级电网过压保护点
    {2336, 0, TYPE_INT32U, TYPE_INT32U, 0, 0, 4, TYPE_PARA , DAC_PARA_ID_OVER_VOLT_PROT_TIME_GRID_OFFSET + 2, DC_AC_SET_PROTECT_L3_PARA_DATA},  // 三级电网过压保护时间高16位
    {2338, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_UNDER_VOLT_PROT_POINT_GRID_OFFSET + 2, DC_AC_SET_PROTECT_L3_PARA_DATA}, // 三级电网欠压保护点
    {2339, 0, TYPE_INT32U, TYPE_INT32U, 0, 0, 4, TYPE_PARA , DAC_PARA_ID_UNDER_VOLT_PROT_TIME_GRID_OFFSET + 2, DC_AC_SET_PROTECT_L3_PARA_DATA}, // 三级电网欠压保护时间高16位
    {2341, 0, TYPE_FLOAT, TYPE_INT16U,  2, 0, 2, TYPE_PARA , DAC_PARA_ID_OVER_FREQ_PROT_POINT_OFFSET + 2, DC_AC_SET_PROTECT_L3_PARA_DATA}, // 三级过频保护点
    {2342, 0, TYPE_INT32U, TYPE_INT32U, 0, 0, 4, TYPE_PARA , DAC_PARA_ID_OVER_FREQ_PROT_TIME_OFFSET + 2, DC_AC_SET_PROTECT_L3_PARA_DATA},   // 三级过频保护时间高16位
    {2344, 0, TYPE_FLOAT, TYPE_INT16U,  2, 0, 2, TYPE_PARA , DAC_PARA_ID_UNDER_FREQ_PROT_POINT_OFFSET + 2 ,DC_AC_SET_PROTECT_L3_PARA_DATA}, // 三级欠频保护点
    {2345, 0, TYPE_INT32U, TYPE_INT32U, 0, 0, 4, TYPE_PARA , DAC_PARA_ID_UNDER_FREQ_PROT_TIME_OFFSET + 2, DC_AC_SET_PROTECT_L3_PARA_DATA},   // 三级欠频保护时间高16位
    {2347, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_OVER_VOLT_PROT_POINT_GRID_OFFSET + 3, DC_AC_SET_PROTECT_L4_PARA_DATA}, // 四级电网过压保护点
    {2348, 0, TYPE_INT32U, TYPE_INT32U, 0, 0, 4, TYPE_PARA , DAC_PARA_ID_OVER_VOLT_PROT_TIME_GRID_OFFSET + 3, DC_AC_SET_PROTECT_L4_PARA_DATA},  // 四级电网过压保护时间
    {2350, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_UNDER_VOLT_PROT_POINT_GRID_OFFSET + 3, DC_AC_SET_PROTECT_L4_PARA_DATA}, // 四级电网欠压保护点
    {2351, 0, TYPE_INT32U, TYPE_INT32U, 0, 0, 4, TYPE_PARA , DAC_PARA_ID_UNDER_VOLT_PROT_TIME_GRID_OFFSET + 3, DC_AC_SET_PROTECT_L4_PARA_DATA}, // 四级电网欠压保护时间
    {2353, 0, TYPE_FLOAT, TYPE_INT16U,  2, 0, 2, TYPE_PARA , DAC_PARA_ID_OVER_FREQ_PROT_POINT_OFFSET + 3, DC_AC_SET_PROTECT_L4_PARA_DATA}, // 四级过频保护点
    {2354, 0, TYPE_INT32U, TYPE_INT32U, 0, 0, 4, TYPE_PARA , DAC_PARA_ID_OVER_FREQ_PROT_TIME_OFFSET + 3, DC_AC_SET_PROTECT_L4_PARA_DATA},   // 四级过频保护时间
    {2356, 0, TYPE_FLOAT, TYPE_INT16U,  2, 0, 2, TYPE_PARA , DAC_PARA_ID_UNDER_FREQ_PROT_POINT_OFFSET + 3, DC_AC_SET_PROTECT_L4_PARA_DATA}, // 四级欠频保护点
    {2357, 0, TYPE_INT32U, TYPE_INT32U, 0, 0, 4, TYPE_PARA , DAC_PARA_ID_UNDER_FREQ_PROT_TIME_OFFSET + 3, DC_AC_SET_PROTECT_L4_PARA_DATA},   // 四级欠频保护时间
    {2359, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_OVER_VOLT_PROT_POINT_GRID_OFFSET + 4, DC_AC_SET_PROTECT_L5_PARA_DATA}, // 五级电网过压保护点
    {2360, 0, TYPE_INT32U, TYPE_INT32U, 0, 0, 4, TYPE_PARA , DAC_PARA_ID_OVER_VOLT_PROT_TIME_GRID_OFFSET + 4, DC_AC_SET_PROTECT_L5_PARA_DATA},  // 五级电网过压保护时间高16位
    {2362, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_PARA , DAC_PARA_ID_UNDER_VOLT_PROT_POINT_GRID_OFFSET + 4, DC_AC_SET_PROTECT_L5_PARA_DATA}, // 五级电网欠压保护点
    {2363, 0, TYPE_INT32U, TYPE_INT32U, 0, 0, 4, TYPE_PARA , DAC_PARA_ID_UNDER_VOLT_PROT_TIME_GRID_OFFSET + 4, DC_AC_SET_PROTECT_L5_PARA_DATA}, // 五级电网欠压保护时间高16位
    {2365, 0, TYPE_FLOAT, TYPE_INT16U,  2, 0, 2, TYPE_PARA , DAC_PARA_ID_OVER_FREQ_PROT_POINT_OFFSET + 4, DC_AC_SET_PROTECT_L5_PARA_DATA}, // 五级过频保护点
    {2366, 0, TYPE_INT32U, TYPE_INT32U, 0, 0, 4, TYPE_PARA , DAC_PARA_ID_OVER_FREQ_PROT_TIME_OFFSET + 4, DC_AC_SET_PROTECT_L5_PARA_DATA},   // 五级过频保护时间高16位
    {2368, 0, TYPE_FLOAT, TYPE_INT16U,  2, 0, 2, TYPE_PARA , DAC_PARA_ID_UNDER_FREQ_PROT_POINT_OFFSET + 4 ,DC_AC_SET_PROTECT_L5_PARA_DATA}, // 五级欠频保护点
    {2369, 0, TYPE_INT32U, TYPE_INT32U, 0, 0, 4, TYPE_PARA , DAC_PARA_ID_UNDER_FREQ_PROT_TIME_OFFSET + 4, DC_AC_SET_PROTECT_L5_PARA_DATA},   // 五级欠频保护时间高16位
    {2371, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 0, 2, TYPE_PARA , DAC_PARA_ID_OVER_VOLT_PROT_POINT_GRID_OFFSET + 5, DC_AC_SET_PROTECT_L6_PARA_DATA}, // 六级电网过压保护点
    {2372, 0, TYPE_INT32U, TYPE_INT32U, 0, 0, 4, TYPE_PARA , DAC_PARA_ID_OVER_VOLT_PROT_TIME_GRID_OFFSET + 5, DC_AC_SET_PROTECT_L6_PARA_DATA},  // 六级电网过压保护时间
    {2374, 0, TYPE_FLOAT,  TYPE_INT16U, 1, 0, 2, TYPE_PARA , DAC_PARA_ID_UNDER_VOLT_PROT_POINT_GRID_OFFSET + 5, DC_AC_SET_PROTECT_L6_PARA_DATA}, // 六级电网欠压保护点
    {2375, 0, TYPE_INT32U, TYPE_INT32U, 0, 0, 4, TYPE_PARA , DAC_PARA_ID_UNDER_VOLT_PROT_TIME_GRID_OFFSET + 5, DC_AC_SET_PROTECT_L6_PARA_DATA}, // 六级电网欠压保护时间
    {2377, 0, TYPE_FLOAT,  TYPE_INT16U, 2, 0, 2, TYPE_PARA , DAC_PARA_ID_OVER_FREQ_PROT_POINT_OFFSET + 5, DC_AC_SET_PROTECT_L6_PARA_DATA}, // 六级过频保护点
    {2378, 0, TYPE_INT32U, TYPE_INT32U, 0, 0, 4, TYPE_PARA , DAC_PARA_ID_OVER_FREQ_PROT_TIME_OFFSET + 5, DC_AC_SET_PROTECT_L6_PARA_DATA},   // 六级过频保护时间
    {2380, 0, TYPE_FLOAT,  TYPE_INT16U, 2, 0, 2, TYPE_PARA , DAC_PARA_ID_UNDER_FREQ_PROT_POINT_OFFSET + 5, DC_AC_SET_PROTECT_L6_PARA_DATA}, // 六级欠频保护点
    {2381, 0, TYPE_INT32U, TYPE_INT32U, 0, 0, 4, TYPE_PARA , DAC_PARA_ID_UNDER_FREQ_PROT_TIME_OFFSET + 5, DC_AC_SET_PROTECT_L6_PARA_DATA},   // 六级欠频保护时
    {2383, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_COMMON_ID_HIS_DATA_SAVE_INTERVER_OFFSET}, // 历史数据保存间隔
    {2384, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_AUTH_USE_TIME_OFFSET},      // 产品授权时间

    {2385, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_HVRT_CHARACTER_CURVE_POINT_OFFSET, DC_AC_SET_HVRT_CC_PARA_DATA},                 // 特征曲线点
    {2386, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET, DC_AC_SET_HVRT_CC_PARA_DATA},         // 时间1
    {2387, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET, DC_AC_SET_HVRT_CC_PARA_DATA},  // 电压百分比1
    {2388, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET + 1, DC_AC_SET_HVRT_CC_PARA_DATA},         // 时间2
    {2389, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET + 1, DC_AC_SET_HVRT_CC_PARA_DATA},  // 电压百分比2
    {2390, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET + 2, DC_AC_SET_HVRT_CC_PARA_DATA},         // 时间3
    {2391, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET + 2, DC_AC_SET_HVRT_CC_PARA_DATA},  // 电压百分比3
    {2392, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET + 3, DC_AC_SET_HVRT_CC_PARA_DATA},         // 时间4
    {2393, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET + 3, DC_AC_SET_HVRT_CC_PARA_DATA},  // 电压百分比4
    {2394, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET + 4, DC_AC_SET_HVRT_CC_PARA_DATA},         // 时间5
    {2395, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET + 4, DC_AC_SET_HVRT_CC_PARA_DATA},  // 电压百分比5
    {2396, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET + 5, DC_AC_SET_HVRT_CC_PARA_DATA},         // 时间6
    {2397, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET + 5, DC_AC_SET_HVRT_CC_PARA_DATA},  // 电压百分比6
    {2398, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET + 6, DC_AC_SET_HVRT_CC_PARA_DATA},         // 时间7
    {2399, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET + 6 ,DC_AC_SET_HVRT_CC_PARA_DATA},  // 电压百分比7
    {2400, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET + 7, DC_AC_SET_HVRT_CC_PARA_DATA},         // 时间8
    {2401, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET + 7, DC_AC_SET_HVRT_CC_PARA_DATA},  // 电压百分比8
    {2402, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET + 8, DC_AC_SET_HVRT_CC_PARA_DATA},         // 时间9
    {2403, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET + 8, DC_AC_SET_HVRT_CC_PARA_DATA},  // 电压百分比9
    {2404, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET + 9, DC_AC_SET_HVRT_CC_PARA_DATA},        // 时间10
    {2405, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET + 9, DC_AC_SET_HVRT_CC_PARA_DATA}, // 电压百分比10
    {2406, 0, TYPE_STRING, TYPE_STRING, 0, 0, 32, TYPE_PARA , DAC_PARA_ID_CSC_VERSION_OFFSET, },// CSC
    {2422, 0, TYPE_STRING, TYPE_STRING, 0, 0, 32, TYPE_PARA , DAC_PARA_ID_PSC_VERSION_OFFSET, },// PSC
    {2438, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA, DAC_PARA_ID_DELAY_UPDATE_OFFSET,}, // 延迟升级
    {2439, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA, DAC_PARA_ID_WIFI_WORK_MODE_OFFSET,}, // wifi工作模式

    {2440, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_COMMON_ID_RS485_BAUD_OFFSET },     // rs485波特率
    {2441, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_COMMON_ID_RS485_COMMU_ADDR_OFFSET }, // rs485通信地址
    {2442, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_COMMON_ID_RS485_CHK_CODE_OFFSET },  // rs485校验位

    {2443, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_GROUP_STRING_LOSS_DETECTION_OFFSET, DC_AC_SET_STRING_IN_DETECTION_DATA}, // 组串丢失检测使能/禁止
    {2444, 0, TYPE_FLOAT , TYPE_INT16U, 1, 0, 2, TYPE_PARA , DAC_PARA_ID_STARTING_CURRENT_OFFSET, DC_AC_SET_STRING_IN_DETECTION_DATA}, // 启动电流
    {2445, 0, TYPE_FLOAT , TYPE_INT16U, 1, 0, 2, TYPE_PARA , DAC_PARA_ID_TWO_IN_ONE_STARTING_CURRENT_OFFSET, DC_AC_SET_STRING_IN_DETECTION_DATA}, // 二汇一启动电流
    {2446, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET, DC_AC_SET_STRING_IN_DETECTION_DATA}, // 组串1接入类型
    {2447, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 1, DC_AC_SET_STRING_IN_DETECTION_DATA}, // 组串2接入类型
    {2448, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 2, DC_AC_SET_STRING_IN_DETECTION_DATA}, // 组串3接入类型
    {2449, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 3, DC_AC_SET_STRING_IN_DETECTION_DATA}, // 组串4接入类型
    {2450, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 4, DC_AC_SET_STRING_IN_DETECTION_DATA}, // 组串5接入类型
    {2451, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 5, DC_AC_SET_STRING_IN_DETECTION_DATA}, // 组串6接入类型
    {2452, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 6, DC_AC_SET_STRING_IN_DETECTION_DATA}, // 组串7接入类型
    {2453, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 7, DC_AC_SET_STRING_IN_DETECTION_DATA}, // 组串8接入类型

    {2454, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_OVERFREQUENCY_DERATING_ENABLE_OFFSET, DC_AC_SET_OVER_UNDER_FREQ_DERATE_DATA},//过频降额使能
    {2455, 0, TYPE_FLOAT , TYPE_INT16U, 2, 0, 2, TYPE_PARA , DAC_PARA_ID_OVERFREQUENCY_DERATING_TRIG_FREQUENCY_OFFSET, DC_AC_SET_OVER_UNDER_FREQ_DERATE_DATA},//过频降额触发频率(Hz)
    {2456, 0, TYPE_FLOAT , TYPE_INT16U, 2, 0, 2, TYPE_PARA , DAC_PARA_ID_OVERFREQUENCY_DERATING_EXIT_FREQUENCY_OFFSET, DC_AC_SET_OVER_UNDER_FREQ_DERATE_DATA},//过频降额退出频率(Hz)
    {2457, 0, TYPE_FLOAT , TYPE_INT16U, 2, 0, 2, TYPE_PARA , DAC_PARA_ID_OVERFREQUENCY_DERATING_CUTOFF_FREQUENCY_OFFSET, DC_AC_SET_OVER_UNDER_FREQ_DERATE_DATA},//过频降额截止频率(Hz)
    {2458, 0, TYPE_FLOAT , TYPE_INT16U, 1, 0, 2, TYPE_PARA , DAC_PARA_ID_OVERFREQUENCY_DERATING_RATED_POWER_OFFSET, DC_AC_SET_OVER_UNDER_FREQ_DERATE_DATA},//过频降额额定功率（%/s）
    {2459, 0, TYPE_FLOAT , TYPE_INT16U, 1, 0, 2, TYPE_PARA , DAC_PARA_ID_OVERFREQUENCY_DERATING_RECOVERY_GRADIENT_OFFSET, DC_AC_SET_OVER_UNDER_FREQ_DERATE_DATA},//过频降额恢复梯度(%/min)
    {2460, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_FREQUENCY_CONTROL_FILTERING_TIME_OFFSET, DC_AC_SET_OVER_UNDER_FREQ_DERATE_DATA},//频率控制滤波时间
    {2461, 0, TYPE_INT32U, TYPE_INT32U, 0, 0, 4, TYPE_PARA , DAC_PARA_ID_OVER_FREQUENCY_ACTIVE_DERATING_RECOVERY_DELAY_TIME_OFFSET, DC_AC_SET_OVER_UNDER_FREQ_DERATE_DATA},//过频有功降额恢复延时时间
    {2463, 0, TYPE_INT32U, TYPE_INT32U, 0, 0, 4, TYPE_PARA , DAC_PARA_ID_OVER_FREQUENCY_ACTIVE_DERATING_EFFECTIVE_TIME_OFFSET, DC_AC_SET_OVER_UNDER_FREQ_DERATE_DATA},//过频有功降额生效时间
    {2465, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_ENABLE_OFFSET, DC_AC_SET_OVER_UNDER_FREQ_DERATE_DATA},//欠频升功率使能
    {2466, 0, TYPE_FLOAT , TYPE_INT16U, 2, 0, 2, TYPE_PARA , DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_TRIG_FREQUENCY_OFFSET, DC_AC_SET_OVER_UNDER_FREQ_DERATE_DATA},//欠频升功率触发频率(Hz)
    {2467, 0, TYPE_FLOAT , TYPE_INT16U, 2, 0, 2, TYPE_PARA , DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_EXIT_FREQUENCY_OFFSET, DC_AC_SET_OVER_UNDER_FREQ_DERATE_DATA},//欠频升功率退出频率(Hz)
    {2468, 0, TYPE_FLOAT , TYPE_INT16U, 2, 0, 2, TYPE_PARA , DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_CUTOFF_FREQUENCY_OFFSET, DC_AC_SET_OVER_UNDER_FREQ_DERATE_DATA},//欠频升功率截止频率(Hz)
    {2469, 0, TYPE_FLOAT , TYPE_INT16U, 1, 0, 2, TYPE_PARA , DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_CUTOFF_POWER_OFFSET, DC_AC_SET_OVER_UNDER_FREQ_DERATE_DATA},//欠频升功率截止功率（%）
    {2470, 0, TYPE_FLOAT , TYPE_INT16U, 1, 0, 2, TYPE_PARA , DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_RECOVERY_GRADIENT_OFFSET, DC_AC_SET_OVER_UNDER_FREQ_DERATE_DATA},//欠频升功率恢复梯度(%/min)
    {2471, 0, TYPE_INT32U, TYPE_INT32U, 0, 0, 4, TYPE_PARA , DAC_PARA_ID_UNDER_FREQUENCY_ACTIVE_DERATING_RECOVERY_DELAY_TIME_OFFSET, DC_AC_SET_OVER_UNDER_FREQ_DERATE_DATA},//欠频有功降额恢复延时时间
    {2473, 0, TYPE_INT32U, TYPE_INT32U, 0, 0, 4, TYPE_PARA , DAC_PARA_ID_UNDER_FREQUENCY_ACTIVE_DERATING_EFFECTIVE_TIME_OFFSET, DC_AC_SET_OVER_UNDER_FREQ_DERATE_DATA},//欠频有功降额生效时间

    {2475, 0, TYPE_FLOAT, TYPE_INT32S, 3, 0, 4, TYPE_PARA , DAC_PARA_ID_REACTIVE_POWER_VALUE_OFFSET, DC_AC_SET_REACT_REGULATION_PARA_DATA},        // 无功功率设置固定值
    {2477, 0, TYPE_FLOAT, TYPE_INT16S, 1, 0, 2, TYPE_PARA , DAC_PARA_ID_REACTIVE_POWER_PCT_OFFSET, DC_AC_SET_REACT_REGULATION_PARA_DATA},        // 无功功率设置百分比
    {2478, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PARA_ID_USER_ACCOUNT_PERMANENTLY_VALID_OFFSET},        // user账号是否永久有效
    {2479, 0, TYPE_INT32U, TYPE_INT32U, 0, 0, 4, TYPE_PARA , DAC_PARA_ID_USER_ACCOUNT_EXPIRATION_DATE_OFFSET},        // user账号授权截止时间

    {2600, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_MPPT_OVER_VOLT_LEVEL }, // MPPT过压
    {2601, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_BUS_SOFT_START_ERR_LEVEL },     // 母线软启动故障
    {2602, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_DC_DISCONNECTOR_TRIP_LEVEL }, // 直流隔离开关脱扣
    {2603, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_STR_REVERSE_CONNECT_LEVEL },  // 组串反接
    {2604, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_MPPT_OVER_CURR_WARN_LEVEL }, // MPPT过流告警
    {2605, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_MPPT_OVER_CURR_ERROR_LEVEL },             // MPPT过流故障
    {2606, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_STR_CURR_REVERSE_INJECT_LEVEL },             // 组串电流反灌
    {2607, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_DC_ARC_DAULT_LEVEL },             // 直流电弧故障
    {2608, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_AFCI_SELF_CHK_FAIL_LEVEL },             // AFCI自检失败
    {2609, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_BUS_OVER_VOLT_LEVEL },            // 母线过压
    {2610, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_POS_BUS_OVER_VOLT_LEVEL },         //正母线过压
    {2611, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_NEG_BUS_OVER_VOLT_LEVEL }, // 负母线过压
    {2612, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_BUS_VOLT_IMBALANCE_LEVEL}, // 母线电压不平衡
    {2613, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_ABNORMAL_OPER_BUILD_IN_PID_LEVEL}, // 内置PID工作异常
    {2614, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_STR_LOSS_LEVEL}, // 组串丢失
    {2615, 1, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , }, 
    {2616, 1, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , }, 
    {2617, 1, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , }, 
    {2618, 1, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , }, 
    {2619, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_ABN_RESIDUAL_CURR_LEVEL}, // 残余电流异常
    {2620, 1, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA ,  }, // 交流过流告警
    {2621, 1, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA ,  },   // 交流过流故障
    {2622, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_HIGH_VOLT_RT_LEVEL}, // 高电压穿越
    {2623, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_LOW_VOLT_RT_LEVEL },        // 低电压穿越
    {2624, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_LOW_POWER_SHUTDOWN_LEVEL },      // 低功率关机
    {2625, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_OVER_VOLT_BETWEEN_PHASE_LEVEL },           // 线电压过压
    {2626, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_UNDER_VOLT_BETWEEN_PHASE_LEVEL }, // 线电压欠压
    {2627, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_PHASE_VOLT_OVER_VOLT_LEVEL },     // 相电压过压
    {2628, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_PHASE_VOLT_UNDER_VOLT_LEVEL }, // 相电压欠压
    {2629, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_PHASE_VOLT_TRANS_OVER_VOLT_LEVEL },  // 相电压瞬态过压
    {2630, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_GRID_OVER_FREQ_LEVEL }, // 电网过频
    {2631, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_GRID_UNDER_FREQ_LEVEL },             // 电网欠频
    {2632, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_GRID_FREQ_INSTABLE_LEVEL },             // 电网频率不稳
    {2633, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_AC_PHASE_LOSS_LEVEL },             // 交流缺相
    {2634, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_ABNORMAL_GROUND_LEVEL },             // 接地异常
    {2635, 1, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA ,  },
    {2636, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_TEN_MINUTE_GRID_OVER_VOLT_PROT_LEVEL },         // 十分钟电网电压过压保护
    {2637, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_UNBALANCE_POWER_GRID_LEVEL }, // 电网不平衡
    {2638, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_OUTPUT_SHORT_CIRCUIT_LEVEL}, // 输出短路
    {2639, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_OUTPUT_OVER_CIRCUIT_LEVEL}, // 输出过流
    {2640, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_ABNORMAL_DC_COMPONENT_LEVEL}, // 直流分量异常
    {2641, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_ISLAND_PROT_LEVEL}, // 孤岛保护
    {2642, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_POWER_GRID_OUTAGE_LEVEL}, // 电网掉电
    {2643, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_GRID_ANTI_ERROR_LEVEL}, // 电网反序故障
    {2644, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_GRID_RELAY_ERROR_LEVEL}, // 并网继电器故障
    {2645, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_AC_CURR_SENSOR_ERROR_LEVEL}, // AC电流传感器故障
    {2646, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_PV_LOCK_ERROR_LEVEL }, // 逆变器锁相失败故障
    {2647, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_RADIATOR_OVER_TEMP_LEVEL},   // 散热器过温
    {2648, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_ENVIR_OVER_TEMP_LEVEL}, // 环境温度过温
    {2649, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_ENVIR_LOW_TEMP_LEVEL },        // 环境温度过低
    {2650, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_OVER_TEMP_INSIDS_MACHINE_LEVEL },      // 机内温度过温
    {2651, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_LOW_TEMP_INSIDS_MACHINE_LEVEL },           // 机内温度过低
    {2652, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_FIRE_ERROR_LEVEL }, // 火灾故障
    {2653, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_EXTER_FAN_FAIL_LEVEL },     // 外部风扇故障
    {2654, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_INTER_FAN_FAIL_LEVEL }, // 内部风扇故障
    {2655, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_ABN_AUXI_POWER_SUPPLT_LEVEL },  // 辅助电源异常
    {2656, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_AC_LIGHTING_PROTECTION_ERR_LEVEL }, // 交流防雷失效
    {2657, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_DC_LIGHTING_PROTECTION_ERR_LEVEL },             // 直流防雷失效
    {2658, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_MAIN_CTRL_EEPROM_FAULT_LEVEL },             // 主控EEPROM故障
    {2659, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_MAIN_CTRL_EEPROM_ABNORMAL_LEVEL },             // 主控EEPROM异常
    {2660, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_AUXI_CTRL_EEPROM_FAULT_LEVEL },             // 辅控EEPROM故障
    {2661, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_AUXI_CTRL_EEPROM_ABNORMAL_LEVEL },            // 辅控EEPROM异常
    {2662, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_MAIN_CPLD_COMMU_ERROR_LEVEL },         // 主控与CPLD通讯故障
    {2663, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_MAIN_AUXI_COMMU_ERROR_LEVEL }, // 主控与辅控通讯故障
    {2664, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_PROTOCOL_VER_MISMATCH_LEVEL}, // 协议版本不匹配
    {2665, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_CARRIER_SYNC_ABNORMAL_LEVEL}, // 载波同步信号异常
    {2666, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_MONITER_MAIN_CTRL_COMMU_ERROR_LEVEL}, // 监控与主控通讯故障
    {2667, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_MONITER_ERROR_ALARM_LEVEL}, // 监控单元故障告警
    {2668, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_LICENSE_EXPIRED_LEVEL}, // 许可证过期
    {2800, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_MPPT_OVER_VOLT_RELAY }, // MPPT过压
    {2801, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_BUS_SOFT_START_ERR_RELAY },     // 母线软启动故障
    {2802, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_DC_DISCONNECTOR_TRIP_RELAY }, // 直流隔离开关脱扣
    {2803, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_STR_REVERSE_CONNECT_RELAY },  // 组串反接
    {2804, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_MPPT_OVER_CURR_WARN_RELAY }, // MPPT过流告警
    {2805, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_MPPT_OVER_CURR_ERROR_RELAY },             // MPPT过流故障
    {2806, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_STR_CURR_REVERSE_INJECT_RELAY },             // 组串电流反灌
    {2807, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_DC_ARC_DAULT_RELAY },             // 直流电弧故障
    {2808, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_AFCI_SELF_CHK_FAIL_RELAY },             // AFCI自检失败
    {2809, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_BUS_OVER_VOLT_RELAY },            // 母线过压
    {2810, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_POS_BUS_OVER_VOLT_RELAY },         //正母线过压
    {2811, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_NEG_BUS_OVER_VOLT_RELAY }, // 负母线过压
    {2812, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_BUS_VOLT_IMBALANCE_RELAY}, // 母线电压不平衡
    {2813, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_ABNORMAL_OPER_BUILD_IN_PID_RELAY}, // 内置PID工作异常
    {2814, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_STR_LOSS_RELAY}, // 组串丢失
    {2815, 1, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , },
    {2816, 1, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , },
    {2817, 1, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , },
    {2818, 1, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , },
    {2819, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_ABN_RESIDUAL_CURR_RELAY}, // 残余电流异常
    {2820, 1, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , },
    {2821, 1, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , },
    {2822, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_HIGH_VOLT_RT_RELAY}, // 高电压穿越
    {2823, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_LOW_VOLT_RT_RELAY },        // 低电压穿越
    {2824, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_LOW_POWER_SHUTDOWN_RELAY },      // 低功率关机
    {2825, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_OVER_VOLT_BETWEEN_PHASE_RELAY },           // 线电压过压
    {2826, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_UNDER_VOLT_BETWEEN_PHASE_RELAY }, // 线电压欠压
    {2827, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_PHASE_VOLT_OVER_VOLT_RELAY },     // 相电压过压
    {2828, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_PHASE_VOLT_UNDER_VOLT_RELAY }, // 相电压欠压
    {2829, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_PHASE_VOLT_TRANS_OVER_VOLT_RELAY },  // 相电压瞬态过压
    {2830, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_GRID_OVER_FREQ_RELAY }, // 电网过频
    {2831, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_GRID_UNDER_FREQ_RELAY },             // 电网欠频
    {2832, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_GRID_FREQ_INSTABLE_RELAY },             // 电网频率不稳
    {2833, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_AC_PHASE_LOSS_RELAY },             // 交流缺相
    {2834, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_ABNORMAL_GROUND_RELAY },             // 接地异常
    {2835, 1, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA ,  },
    {2836, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_TEN_MINUTE_GRID_OVER_VOLT_PROT_RELAY },         // 十分钟电网电压过压保护
    {2837, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_UNBALANCE_POWER_GRID_RELAY }, // 电网不平衡
    {2838, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_OUTPUT_SHORT_CIRCUIT_RELAY}, // 输出短路
    {2839, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_OUTPUT_OVER_CIRCUIT_RELAY}, // 输出过流
    {2840, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_ABNORMAL_DC_COMPONENT_RELAY}, // 直流分量异常
    {2841, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_ISLAND_PROT_RELAY}, // 孤岛保护
    {2842, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_POWER_GRID_OUTAGE_RELAY}, // 电网掉电
    {2843, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_GRID_ANTI_ERROR_RELAY}, // 电网反序故障
    {2844, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_GRID_RELAY_ERROR_RELAY}, // 并网继电器故障
    {2845, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_AC_CURR_SENSOR_ERROR_RELAY}, // AC电流传感器故障
    {2846, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_PV_LOCK_ERROR_RELAY }, // 逆变器锁相失败故障
    {2847, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_RADIATOR_OVER_TEMP_RELAY},   // 散热器过温
    {2848, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_ENVIR_OVER_TEMP_RELAY}, // 环境温度过温
    {2849, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_ENVIR_LOW_TEMP_RELAY },        // 环境温度过低
    {2850, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_OVER_TEMP_INSIDS_MACHINE_RELAY },      // 机内温度过温
    {2851, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_LOW_TEMP_INSIDS_MACHINE_RELAY },           // 机内温度过低
    {2852, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_FIRE_ERROR_RELAY }, // 火灾故障
    {2853, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_EXTER_FAN_FAIL_RELAY },     // 外部风扇故障
    {2854, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_INTER_FAN_FAIL_RELAY }, // 内部风扇故障
    {2855, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_ABN_AUXI_POWER_SUPPLT_RELAY },  // 辅助电源异常
    {2856, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_AC_LIGHTING_PROTECTION_ERR_RELAY }, // 交流防雷失效
    {2857, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_DC_LIGHTING_PROTECTION_ERR_RELAY },             // 直流防雷失效
    {2858, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_MAIN_CTRL_EEPROM_FAULT_RELAY },             // 主控EEPROM故障
    {2859, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_MAIN_CTRL_EEPROM_ABNORMAL_RELAY },             // 主控EEPROM异常
    {2860, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_AUXI_CTRL_EEPROM_FAULT_RELAY },             // 辅控EEPROM故障
    {2861, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_AUXI_CTRL_EEPROM_ABNORMAL_RELAY },            // 辅控EEPROM异常
    {2862, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_MAIN_CPLD_COMMU_ERROR_RELAY },         // 主控与CPLD通讯故障
    {2863, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_MAIN_AUXI_COMMU_ERROR_RELAY }, // 主控与辅控通讯故障
    {2864, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_PROTOCOL_VER_MISMATCH_RELAY}, // 协议版本不匹配
    {2865, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_CARRIER_SYNC_ABNORMAL_RELAY}, // 载波同步信号异常
    {2866, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_MONITER_MAIN_CTRL_COMMU_ERROR_RELAY}, // 监控与主控通讯故障
    {2867, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_MONITER_ERROR_ALARM_RELAY}, // 监控单元故障告警
    {2868, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ALAM_PARA , DAC_ALARM_ID_LICENSE_EXPIRED_RELAY}, // 许可证过期
};

/*******************************打包函数定义*****************/
int floattoint32u(int* index , unsigned char* data_buff , int* reg_nums , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map  )
{
    float data = 0.0 ;  
    int tmp = 0;
    check_ana_reserve_flag( *index ,&data,data_map);    
    tmp =round(data * percision );
    put_uint32_to_buff(&data_buff[total_len], (unsigned int)(int)(tmp));
    (*index)++;
    (*reg_nums) -= 2;
    return DATA_LEN_4;
}
int floattoint32s(int* index , unsigned char* data_buff, int* reg_nums , int data_len ,  int percision, int total_len, modbus_addr_map_data_t* data_map  )
{
    float data = 0.0 ;  
    int tmp = 0;
    check_ana_reserve_flag( *index ,&data, data_map);    
    tmp =round(data * percision );
    put_int32_to_buff(&data_buff[total_len], (signed int)(tmp));
    (*index)++;
    (*reg_nums) -= 2;
    return DATA_LEN_4;
}
int floattoint16u(int* index , unsigned char* data_buff , int* reg_nums , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map )
{
    float data = 0.0 ;  
    int tmp = 0;
    check_ana_reserve_flag( *index ,&data, data_map);    
    tmp =round(data * percision );
    put_uint16_to_buff(&data_buff[total_len], (unsigned short)(short)(tmp));
    (*index)++;
    (*reg_nums) -= 1;
    return DATA_LEN_2;
}
int floattoint16s(int* index , unsigned char* data_buff , int* reg_nums , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map )
{
    float data = 0.0 ;  
    int tmp = 0;
    check_ana_reserve_flag( *index ,&data, data_map);    
    tmp =round(data * percision );
    put_int16_to_buff(&data_buff[total_len], (signed short)(tmp));
    (*index)++;
    (*reg_nums) -= 1;
    return DATA_LEN_2;
}
int int16utoint16u(int* index , unsigned char* data_buff , int* reg_nums , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map )
{
    unsigned short data = 0;
    check_ana_reserve_flag(*index ,&data, data_map);  
    put_uint16_to_buff(&data_buff[total_len], data * percision);
    (*index)++;
    (*reg_nums) -= 1;
    return DATA_LEN_2;
}

int int16stoint16s(int* index , unsigned char* data_buff , int* reg_nums , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map )
{
    signed short data = 0;
    check_ana_reserve_flag( *index ,&data, data_map);  
    put_int16_to_buff(&data_buff[total_len], data * percision);

    (*index)++;
    (*reg_nums) -= 1;
    return DATA_LEN_2;
}

int int32utoint32u(int* index , unsigned char* data_buff , int* reg_nums, int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map )
{
    unsigned int data = 0;
    check_ana_reserve_flag( *index ,&data, data_map);  

    put_uint32_to_buff(&data_buff[total_len], data * percision );
    (*index)++;
    (*reg_nums) -= 2;
    return DATA_LEN_4;
}
int stringtostring(int* index , unsigned char* data_buff , int* reg_nums , int data_len,  int percision  , int total_len, modbus_addr_map_data_t* data_map )
{
    unsigned char data[64]={0};
    check_ana_reserve_flag( *index ,&data, data_map);  
    rt_memcpy_s(&data_buff[total_len], data_len, data, data_len);
    (*reg_nums) -= data_len / 2;
    (*index)++;
    return data_len;
}
int datetoint16u(int* index, unsigned char* data_buff, int* reg_nums, int data_len, int percision, int total_len, modbus_addr_map_data_t* data_map)
{
    date_base_t data = {0};
    get_one_data(data_map[*index].data_addr, &data);
    put_uint16_to_buff(&data_buff[total_len], data.year);
    put_uint16_to_buff(&data_buff[total_len+2], data.month);
    put_uint16_to_buff(&data_buff[total_len+4], data.day);
    (*index) += 3;
    (*reg_nums) -= 3;
    return DATA_LEN_6;
}
int chartobit(int* index , unsigned char* data_buff ,  int* reg_nums , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map )
{
    numchar_to_bit_to_int16(data_buff, total_len, index, percision,data_map);
    (*reg_nums) -= 1;
    return DATA_LEN_2;
}

/*********************************解包函数定义***********************************************/
int parse_int16utofloat( int* index , unsigned char* data_buff , int* data_valude_index ,int percision ,int* reg_nums, modbus_addr_map_data_t* data_map)
{
    unsigned short data = 0;
    float result = 0.0; 
    data = get_uint16_data(&data_buff[*data_valude_index]);
    result = data*1.0 / percision;
    (*reg_nums) -= 1;
    (*data_valude_index) += 2;
    return set_data_by_id_type(*index , &result, data_map);
}

 int parse_int16stofloat( int* index , unsigned char* data_buff , int* data_valude_index , int percision ,int* reg_nums, modbus_addr_map_data_t* data_map)
 {
    signed short data = 0;
    float result = 0.0;
    data = get_int16_data(&data_buff[*data_valude_index]);
    result  = data*1.0 / percision;
    (*reg_nums) -= 1;
    (*data_valude_index) += 2;
    return set_data_by_id_type(*index , &result, data_map);
 }

 int parse_int32utofloat( int* index , unsigned char* data_buff , int* data_valude_index ,  int percision ,int* reg_nums, modbus_addr_map_data_t* data_map)
 {
    unsigned int data = 0;
    float result = 0.0;
    data = get_ulong_data(&data_buff[*data_valude_index]);
    result = data*1.0 / percision;
    (*reg_nums) -= 2;
    (*data_valude_index) += 4;
    return set_data_by_id_type(*index , &result, data_map);
 }
 int parse_int32stofloat( int* index , unsigned char* data_buff , int* data_valude_index , int percision ,int* reg_nums, modbus_addr_map_data_t* data_map)
 {
    signed int data = 0;
    float result = 0.0;
    data = get_ulong_data(&data_buff[*data_valude_index]);
    result  = data *1.0 / percision;
    (*reg_nums) -= 2;
    (*data_valude_index) += 4;
    return set_data_by_id_type(*index , &result, data_map);
 }

 int parse_int16utoint16u( int* index , unsigned char* data_buff , int* data_valude_index , int percision ,int* reg_nums, modbus_addr_map_data_t* data_map)
 {
    unsigned short data = 0;
    unsigned short result = 0.0;
    data = get_uint16_data(&data_buff[*data_valude_index]);
    result  = data *1.0 / percision;
    (*reg_nums) -= 1;
    (*data_valude_index) += 2;
    return set_data_by_id_type(*index , &result, data_map);
 }

 int parse_int32utoint32u( int* index , unsigned char* data_buff , int* data_valude_index ,int percision , int* reg_nums, modbus_addr_map_data_t* data_map)
 {
    unsigned int data = 0;
    unsigned int result = 0;
    data = get_ulong_data(&data_buff[*data_valude_index]);
    result  = data *1.0 / percision;
    (*reg_nums) -= 2;
    (*data_valude_index) += 4;
    return set_data_by_id_type(*index , &result, data_map);
 }

 int parse_stringtostring( int* index , unsigned char* data_buff , int* data_valude_index , int percision , int* reg_nums, modbus_addr_map_data_t* data_map)
 {
    int ret = SUCCESSFUL;
    unsigned char* data = (unsigned char*)rt_malloc(data_map[*index].data_len);
    if(data == NULL)
    {
        return FAILURE;
    }
    rt_memcpy_s(data, data_map[*index].data_len  , &data_buff[*data_valude_index], data_map[*index].data_len);
    (*reg_nums) -= data_map[*index].data_len/2;
    (*data_valude_index) += data_map[*index].data_len;
    ret = set_data_by_id_type(*index , data, data_map);
    rt_free(data);
    return ret;  
 }


//总入口函数
//打包入口函数
int new_pack_data_to_buff(void* cmd_buff , int* index, int offset_value, modbus_addr_map_data_t* data_map)
{
    int type = 0;
    int old_type = 0;
    int percision = 0;
    int data_len = 0;
    int total_len = 0;
    int reg_nums = g_reg_nums;

    //+1应为第一个字节要放总长度（尽管可以直接寄存器数目计算，但是为了验证代码准性，累加长度的）
    unsigned char *data_buff = (((cmd_buf_t *)cmd_buff)->buf) + offset_value; // 准备往data_buff中存放数据

    while (reg_nums > 0 )
    {
        is_contain_time(*index , 0, data_map);
        type = data_map[*index].type;
        old_type = data_map[*index].old_type;
        percision = pow(10, data_map[*index].precision);
        data_len = data_map[*index].data_len;

        for( int i = 0 ;i < sizeof(pack_fun_map)/sizeof(pack_fun_map_t); i++)
        {
            if (pack_fun_map[i].src_type == TYPE_MAX)
            {
                LOG_E("pack | i:%d, not this data type!\n", i);
                ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_VALUE;
                return FAILURE;
            }
            if(pack_fun_map[i].src_type == old_type && pack_fun_map[i].dst_type == type)
            {
                total_len += pack_fun_map[i].pack_fun(index , data_buff , &reg_nums , data_len , percision , total_len,data_map);
                break;
            }
        }
    }
    return total_len;
}
int new_pack_ana_para_data(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    int reg_addr = g_reg_addr;
    int ret = 0;
    /*先找到基地址的下标索引*/
    int index = find_register_start_addr_index(reg_addr);
    if (index == NO_MATCH_REGISTER_ADDR)
    {
        //特殊处理获取iv数据
        ret = deal_get_iv_data((cmd_buf_t *)cmd_buff, reg_addr);
        if(ret != SUCCESSFUL)
        {
            ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_ADDR;
        }
        return SUCCESSFUL;
    }
    int data_len = new_pack_data_to_buff( cmd_buff , &index, 1,g_modbus_data_map);

    ((cmd_buf_t *)cmd_buff)->buf[0] = data_len;
    ((cmd_buf_t *)cmd_buff)->data_len = data_len + 1;

    return SUCCESSFUL;
}


int special_op(int index, int data_valude_indx, unsigned char *data_buff, special_op_flag_t* special_op_flag)
{
    //下一步再优化
    if(is_contain_time(index , 1, g_modbus_data_map) == TRUE)
    {
        if(set_sys_time(data_buff, data_valude_indx, index) == FAILURE)
        {
            return FAILURE;
        }
    }
    special_op_flag->is_delay_update  = is_contain_delay_update(index, g_modbus_data_map);
    special_op_flag->is_grid_code_flag = is_contain_grid_code(index, g_modbus_data_map);
    special_op_flag->need_send_alarm_msg = is_contain_alarm_para_addr(index, g_modbus_data_map);
    special_op_flag->is_uart_flag = is_contain_uart_conf(index,g_modbus_data_map);//后续优化
    return SUCCESSFUL;
}

int collect_para_sid(int index, int sid_num)
{
    // 如果南向cmd_id不等于零，则说明该参数需要下发给南向
    if(g_modbus_data_map[index].south_cmd_id != 0)
    {
        g_sid_list[sid_num] = g_modbus_data_map[index].data_addr;
        sid_num++;
    }
    return sid_num;
}

int send_process_msg(void* cmd_buff, int sid_num, special_op_flag_t* special_op_flag)
{
    set_uart_conf(special_op_flag->is_uart_flag);
    if(special_op_flag->need_send_alarm_msg)//发送告警参数更新消息
    {
        send_msg_to_thread(PARA_CHANGE_MSG_ID, MOD_ALARM_MANAGE, NULL, 0);
    }
    save_all_para();

    if(send_set_para_cmd_msg(g_sid_list, sid_num) != SUCCESSFUL)
    {
         ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_VALUE;
    }

    if(special_op_flag->is_delay_update == TRUE)
    {
        send_msg_to_thread(START_DELAY_UPDATE_MSG, MOD_SYS_MANAGE, NULL, 0);
        //延迟升级检测
    }

    if(special_op_flag->is_grid_code_flag == TRUE)
    {   
        // 要放在最后，否则设置的参数会覆盖掉电网标准码改变后的参数
        send_msg_to_thread(GRID_CODE_CHANGE, MOD_SYS_MANAGE, NULL, 0);
    }

    send_msg_to_thread(GRID_CODE_STATUS_CHECK, MOD_SYS_MANAGE, NULL, 0);
    return SUCCESSFUL;
}

int ctrl_power_on_off(int index, unsigned char* data_buff, int data_valude_indx)
{
    unsigned int sid = g_modbus_data_map[index].data_addr;
    unsigned short power_on_off = 0;
    power_off_reason_t* power_off_reason = NULL;
    if(sid == DAC_PARA_ID_POWER_ON_OFF_OFFSET)
    {
        power_off_reason = get_power_off_reason();
        power_on_off = get_uint16_data(&data_buff[data_valude_indx]);
        LOG_E("%s | %d power_on_off: %d emergy: %d  time:%d  normal:%d\n",__FUNCTION__ , __LINE__, power_on_off, power_off_reason->emergy_off, power_off_reason->time_off, power_off_reason->normal_off);
        if(power_on_off == 1)
        {
            // 开机处理，如果是非正常关机，则开机失败
            if((power_off_reason->emergy_off == TRUE) || (power_off_reason->time_off == TRUE))
            { 
                return FAILURE;
            }
            power_off_reason->normal_off = FALSE;
        }
        else
        {
            power_off_reason->normal_off = TRUE;
        }
    }
    return SUCCESSFUL;
}

int parse_fun_one(int* index , unsigned char* data_buff , int* data_value_index ,int percision , int* reg_nums, int i)
{
    // 开关机特殊处理
    // 开机，需要判断关机原因
    char result_check_para = 0;
    if(ctrl_power_on_off(*index, data_buff, *data_value_index) != SUCCESSFUL)
    {
        return FAILURE;
    }
    
    result_check_para  = parse_fun_map[i].parse_func(index , data_buff , data_value_index , percision , reg_nums, g_modbus_data_map);
    if (SUCCESSFUL != result_check_para)
    {
        return FAILURE;
    }
    return SUCCESSFUL;
}

//解包入口函数
int new_parse_para_data_from_buff(void* dev_inst, void* cmd_buff)
{
    unsigned char master_slave_status = 0;
    get_one_data(DAC_DATA_ID_MASTER_SLAVE_STATUS, &master_slave_status);
    RETURN_VAL_IF_FAIL(master_slave_status != PARALLEL_MASTER, NO_NEED_REPONSE);
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    unsigned char *data_buff = ((cmd_buf_t *)cmd_buff)->buf;

    g_reg_addr = get_int16_data(&data_buff[0]); // 获取数据域的寄存器首地址 2字节
    g_reg_nums = get_int16_data(&data_buff[2]); // 获取数据域的寄存器个数 2字节

    int data_valude_indx = 5; //
    int reg_addr = g_reg_addr;
    int reg_nums = g_reg_nums;
    int type = 0;
    int old_type = 0;
    int percision = 0;
    unsigned int last_south_cmd_id = 0;
    int sid_num = 0;

    special_op_flag_t special_op_flag = {0};

    rt_memset_s(g_sid_list, sizeof(g_sid_list), 0x00, sizeof(g_sid_list));
    /*先找到基地址的下标索引*/
    int index = find_register_start_addr_index(reg_addr);
    if (index == NO_MATCH_REGISTER_ADDR)
    {
        ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_ADDR;
        return SUCCESSFUL;
    }
    last_south_cmd_id = g_modbus_data_map[index].south_cmd_id;
    get_time_set_sid_value();

    while ( (reg_nums > 0 ) && ( index < MODBUS_DATA_MAP_LEN) )
    {
        unsigned char flag_result = g_modbus_data_map[index].reserve_flag;
        if(SUCCESSFUL != special_op(index, data_valude_indx, data_buff, &special_op_flag))
        {
            ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_VALUE;
            return FAILURE;
        }

        if(flag_result == 1)
        {
            index ++ ;
            reg_nums--;
            data_valude_indx += 2;
            continue;
        }
        type = g_modbus_data_map[index].type;
        old_type = g_modbus_data_map[index].old_type;
        percision = pow(10, g_modbus_data_map[index].precision);
        for( int i = 0 ;i < PARSE_FUN_MAP_LEN; i++)
        {
            if (parse_fun_map[i].src_type == TYPE_MAX)
            {
                LOG_E("parse | not this data type!\n");
                ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_VALUE;
                return SUCCESSFUL;
            }
            if(parse_fun_map[i].src_type == type && parse_fun_map[i].dst_type == old_type)
            {
                if(parse_fun_one(&index, data_buff, &data_valude_indx, percision, &reg_nums, i) != SUCCESSFUL)
                {
                    ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_VALUE;
                    return SUCCESSFUL;
                }

                sid_num = collect_para_sid(index, sid_num);

                if ( last_south_cmd_id != 0 && ((last_south_cmd_id != g_modbus_data_map[index].south_cmd_id) || (reg_nums <= 0)))
                {
                    // 保存有功、无功功率备份
                    backup_power_ctrl_para(last_south_cmd_id);
                    last_south_cmd_id = g_modbus_data_map[index].south_cmd_id;
                }
                break;
            }
        }

        index ++;
    }
    send_process_msg(cmd_buff, sid_num, &special_op_flag);
    return SUCCESSFUL;
}


/* 命令请求头 */
static modbusrtu_cmd_head_t cmd_req[] = {
    {GET_ANA_DATA},
    {GET_PARA_DATA},
    {SET_PARA_DATA},
    {SET_CTRL_CMD},
};

/* 命令应答头 */
static modbusrtu_cmd_head_t cmd_ack[] = {
    {GET_ANA_DATA},
    {GET_PARA_DATA},
    {SET_PARA_DATA},
    {SET_CTRL_CMD},
};

/* 命令总表,必须以空字符串""结束 */
static cmd_t no_poll_cmd_tab[] = {

    {CMD_INVERTER_MODBUS_GET_ANA, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {CMD_INVERTER_MODBUS_GET_PARA, CMD_PASSIVE, &cmd_req[1], &cmd_ack[1], sizeof(modbusrtu_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {CMD_INVERTER_MODBUS_SET_PARA, CMD_PASSIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {CMD_INVERTER_MODBUS_CTRL_CMD, CMD_PASSIVE, &cmd_req[3], &cmd_ack[3], sizeof(modbusrtu_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {0},
};

static dev_type_t dev_inverter_modbus_main = {
    DEV_DAC_NORTH_MODBUS,
    1,
    PROTOCOL_MODBUS_RTU,
    LINK_INVERTER,
    S_BUFF_LEN_2048,
    R_BUFF_LEN_2048,
    BOTTOM_CSU_TYPE,
    no_poll_cmd_tab,
    NULL,
    0,
};

static ctrl_cmd_modbus_t s_modbus_ctrl_cmd[] = 
{
    {3500, DAC_CTRL_ID_PV_CTRL_ORDER,   0x0001, DC_AC_IV_CTRL_ORDER,  "start iv scan", start_iv_callback},//启动iv扫描
    {3501, DAC_CTRL_ID_PV_CTRL_ORDER,   0x0002, DC_AC_IV_CTRL_ORDER,  "stop iv scan", stop_iv_callback},//停止IV扫描
    {3502, DAC_CTRL_ID_RESET_CSU,       0x0001, 0,                    "reset csu", reset_csu},//监控重启
    {3503, DAC_CTRL_ID_RESTORE_FACTORY, 0x0001, 0,                    "restore fact value", deal_restore_factory},//恢复出厂默认值
    {3504, DAC_CTRL_ID_SYSTEM_RESTART,  0x0001, 0,                    "restart system",  NULL},  //重启系统
    {3505, DAC_CTRL_ID_PV_CTRL_ORDER,   0x0003, DC_AC_IV_CTRL_ORDER,  "start check isolate", NULL },
    {3506, DAC_CTRL_ID_PV_CTRL_ORDER,   0x0004, DC_AC_IV_CTRL_ORDER,  "start check AFCI",    NULL},
    {3507, DAC_CTRL_ID_CLEAN_HIS_DATA,     0x0001, 0,                    "clean hisdata",          send_clean_his_data_msg},  //清除历史数据
    {3508, DAC_CTRL_ID_CLEAN_ALARM,        0x0001, 0,                    "clean alarm",            send_clean_alarm_msg},  //清除告警
    {3509, DAC_CTRL_ID_CLEAN_HIS_EVENT,    0x0001, 0,                    "clean hisevent",         send_clean_his_event_msg},  //清除操作记录
    {3510, DAC_CTRL_ID_DELETE_HIS_ENERGY,  0x0001, 0,                    "del his_energy",         send_delete_his_energy_msg},  //清除历史发电量
    {3511, DAC_CTRL_ID_CLEAN_IV_DATA,      0x0001, 0,                    "clean iv data",          send_clean_iv_data_msg},  //清除iv数据
    {3512, DAC_CTRL_ID_FAULT_RECORD_DATA,  0x0001, 0,                    "clean fault record",     send_clean_fault_record_msg},  //清除故障录波数据
    {3513, DAC_CTRL_ID_DUBUG_CONTROL_COMMAND,  0x0001, DC_AC_DEBUG_CTRL_ORDER,      "trig fault record", NULL},  //触发故障录波
};

static modbus_reg_map_iv_data_t s_iv_data_reg_map[]=
{
    {4000, 1, 0  , IV_DATA1_READ_OFFSET},//PV1电压
    {4100, 1, 100, IV_DATA2_READ_OFFSET},//PV1电压
    {4200, 1, 200, IV_DATA3_READ_OFFSET},//PV1电压
    {4300, 2, 0  , IV_DATA1_READ_OFFSET},//PV1电流
    {4400, 2, 100, IV_DATA2_READ_OFFSET},//PV1电流
    {4500, 2, 200, IV_DATA3_READ_OFFSET},//PV1电流
    {4600, 3, 0  , IV_DATA1_READ_OFFSET},//PV2电压
    {4700, 3, 100, IV_DATA2_READ_OFFSET},//PV2电压
    {4800, 3, 200, IV_DATA3_READ_OFFSET},//PV2电压
    {4900, 4, 0  , IV_DATA1_READ_OFFSET},//PV2电流
    {5000, 4, 100, IV_DATA2_READ_OFFSET},//PV2电流
    {5100, 4, 200, IV_DATA3_READ_OFFSET},//PV2电流
    {5200, 5, 0  , IV_DATA1_READ_OFFSET},//PV3电压
    {5300, 5, 100, IV_DATA2_READ_OFFSET},//PV3电压
    {5400, 5, 200, IV_DATA3_READ_OFFSET},//PV3电压
    {5500, 6, 0  , IV_DATA1_READ_OFFSET},//PV3电流
    {5600, 6, 100, IV_DATA2_READ_OFFSET},//PV3电流
    {5700, 6, 200, IV_DATA3_READ_OFFSET},//PV3电流
    {5800, 7, 0  , IV_DATA1_READ_OFFSET},//PV4电压
    {5900, 7, 100, IV_DATA2_READ_OFFSET},//PV4电压
    {6000, 7, 200, IV_DATA3_READ_OFFSET},//PV4电压
    {6100, 8, 0  , IV_DATA1_READ_OFFSET},//PV4电流
    {6200, 8, 100, IV_DATA2_READ_OFFSET},//PV4电流
    {6300, 8, 200, IV_DATA3_READ_OFFSET},//PV4电流
    {6400, 9, 0  , IV_DATA1_READ_OFFSET},//PV5电压
    {6500, 9, 100, IV_DATA2_READ_OFFSET},//PV5电压
    {6600, 9, 200, IV_DATA3_READ_OFFSET},//PV5电压
    {6700, 10, 0  , IV_DATA1_READ_OFFSET},//PV5电流
    {6800, 10, 100, IV_DATA2_READ_OFFSET},//PV5电流
    {6900, 10, 200, IV_DATA3_READ_OFFSET},//PV5电流
    {7000, 11, 0  , IV_DATA1_READ_OFFSET},//PV6电压
    {7100, 11, 100, IV_DATA2_READ_OFFSET},//PV6电压
    {7200, 11, 200, IV_DATA3_READ_OFFSET},//PV6电压
    {7300, 12, 0  , IV_DATA1_READ_OFFSET},//PV6电流
    {7400, 12, 100, IV_DATA2_READ_OFFSET},//PV6电流
    {7500, 12, 200, IV_DATA3_READ_OFFSET},//PV6电流
    {7600, 13, 0  , IV_DATA1_READ_OFFSET},//PV7电压
    {7700, 13, 100, IV_DATA2_READ_OFFSET},//PV7电压
    {7800, 13, 200, IV_DATA3_READ_OFFSET},//PV7电压
    {7900, 14, 0  , IV_DATA1_READ_OFFSET},//PV7电流
    {8000, 14, 100, IV_DATA2_READ_OFFSET},//PV7电流
    {8100, 14, 200, IV_DATA3_READ_OFFSET},//PV7电流
    {8200, 15, 0  , IV_DATA1_READ_OFFSET},//PV8电压
    {8300, 15, 100, IV_DATA2_READ_OFFSET},//PV8电压
    {8400, 15, 200, IV_DATA3_READ_OFFSET},//PV8电压
    {8500, 16, 0  , IV_DATA1_READ_OFFSET},//PV8电流
    {8600, 16, 100, IV_DATA2_READ_OFFSET},//PV8电流
    {8700, 16, 200, IV_DATA3_READ_OFFSET},//PV8电流
};

int find_register_start_addr_index_universal(int reg_addr,int table_size,modbus_addr_map_data_t* data_map)
{

    int ret = NO_MATCH_REGISTER_ADDR;
    int star_index = 0;
    int mid_index = 0;
    int end_index =table_size - 1;

    while (star_index <= end_index && star_index >= 0)
    {
        mid_index = (star_index + end_index) / 2;
        if (data_map[mid_index].register_addr == reg_addr)
        {
            ret = mid_index;
            break;
        }
        star_index = (data_map[mid_index].register_addr < reg_addr) ? (mid_index + 1) : star_index;
        end_index = (data_map[mid_index].register_addr < reg_addr) ? end_index : (mid_index - 1);
    }

    return ret;
}

int find_register_start_addr_index(int reg_addr)
{

    int ret = NO_MATCH_REGISTER_ADDR;
    int star_index = 0;
    int mid_index = 0;
    int end_index = sizeof(g_modbus_data_map) / sizeof(modbus_addr_map_data_t) - 1;

    while (star_index <= end_index && star_index >= 0)
    {
        mid_index = (star_index + end_index) / 2;
        if (g_modbus_data_map[mid_index].register_addr == reg_addr)
        {
            ret = mid_index;
            break;
        }
        star_index = (g_modbus_data_map[mid_index].register_addr < reg_addr) ? (mid_index + 1) : star_index;
        end_index = (g_modbus_data_map[mid_index].register_addr < reg_addr) ? end_index : (mid_index - 1);
    }

    return ret;
}

int parse_start_addr_and_reg_nums(void* dev_inst, void* cmd_buff)
{
    unsigned char master_slave_status = 0;
    get_one_data(DAC_DATA_ID_MASTER_SLAVE_STATUS, &master_slave_status);
    RETURN_VAL_IF_FAIL(master_slave_status != PARALLEL_MASTER, NO_NEED_REPONSE);
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    unsigned char *data_buff = ((cmd_buf_t *)cmd_buff)->buf;

    g_reg_addr = get_int16_data(&data_buff[0]); // 获取数据域的寄存器首地址 2字节
    g_reg_nums = get_int16_data(&data_buff[2]); // 获取数据域的寄存器个数 2字节
    //生产参数需要主动发命令
    send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, DC_AC_GET_PRO_PARA_DATA);
    return SUCCESSFUL;
}


int deal_get_iv_data(cmd_buf_t* cmd_buff, signed short reg_addr)//时间为(7字节: 年月日时分秒)+PV序号(1字节) + 精度(1字节，0：不处理，1：除以10，2：除以100，3：除以1000) + 参数个数(2字节) + PV数据（参数个数 * 2字节）
{
    RETURN_VAL_IF_FAIL(cmd_buff != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(g_reg_nums == GET_IV_DATA_PARA_NUM, FAILURE);
    iv_data_head_t iv_head = {0};
    struct stat stat_buff = {0};
    unsigned int  read_offset = 0;
    unsigned short data_len = g_reg_nums * 2 ;
    int loop = 0, ret = 0;
    unsigned short iv_data_reg_map_len = sizeof(s_iv_data_reg_map) / sizeof(modbus_reg_map_iv_data_t);
    unsigned char iv_complete_status = 0;

    get_one_data(DAC_DATA_ID_IV_COMPLETE_STATUS, &iv_complete_status);
    RETURN_VAL_IF_FAIL(iv_complete_status != 0, FAILURE);//扫描未完成，不允许获取iv数据
    ret = storage_stat(IV_DATA_FILE,&stat_buff);
    RETURN_VAL_IF_FAIL(ret != FAILURE, FAILURE);
    unsigned char* read_data = (unsigned char*)rt_malloc(data_len);
    RETURN_VAL_IF_FAIL(read_data != NULL, FAILURE);
    init_read_data(read_data, data_len);

    for(loop = 0; loop < iv_data_reg_map_len; loop++)
    {
        if ( reg_addr != s_iv_data_reg_map[loop].reg_addr )
        {
            continue;
        }
        while (read_offset < stat_buff.st_size)
        {
            if (SUCCESSFUL != handle_storage(read_opr, IV_DATA_FILE, (unsigned char*)&iv_head, IV_DATA_HEAD_LEN, read_offset))
            {
                rt_free(read_data);
                read_data = NULL;
                return FAILURE;
            }
            if (iv_head.block_id == s_iv_data_reg_map[loop].block_id)
            {
                iv_head.para_num = get_uint16_data((unsigned char*)&iv_head.para_num);
                if (SUCCESSFUL != get_iv_scan_data(read_data, read_offset, iv_head.para_num, loop))
                {
                    rt_free(read_data);
                    read_data = NULL;
                    return FAILURE;
                }
                break;
            }
            iv_head.para_num = get_uint16_data((unsigned char*)&iv_head.para_num);
            read_offset += IV_DATA_HEAD_LEN + iv_head.para_num * 2;
        }
        rt_memcpy_s(&cmd_buff->buf[1], data_len, read_data, data_len);
        cmd_buff->buf[0] = data_len;
        cmd_buff->data_len = data_len + 1;  //1为数据段长度
        rt_free(read_data);
        read_data = NULL;
        return SUCCESSFUL;
    }
    rt_free(read_data);
    read_data = NULL;
    return FAILURE;
}

int get_iv_scan_data(unsigned char* read_data,unsigned int read_offset, unsigned short para_num,int loop)
{
    int read_para_num = 0;
    unsigned int read_len = 0, read_data_offset = 0;

    read_para_num = para_num - s_iv_data_reg_map[loop].need_para_num;
    if (read_para_num >= GET_IV_DATA_PARA_NUM)
    {
        read_len = GET_IV_DATA_PARA_NUM * 2;
    }
    else if (read_para_num > 0)
    {
        read_len = read_para_num * 2;
    }
    RETURN_VAL_IF_FAIL(read_len != 0, SUCCESSFUL);
    read_data_offset = read_offset + s_iv_data_reg_map[loop].data_offset;
    if (SUCCESSFUL != handle_storage(read_opr, IV_DATA_FILE, read_data, read_len, read_data_offset))
    {
        return FAILURE;
    }
    return SUCCESSFUL;
}

void init_read_data(unsigned char* read_data, unsigned int read_len)
{
    unsigned int i = 0;
    rt_memset_s(read_data, read_len, 0, read_len);
    for(i = 0; i < read_len; i+=2)
    {
        read_data[i] = 0x80;
    }
    return;
}

int is_contain_alarm_para_addr(int index, modbus_addr_map_data_t* data_map)
{
    if( data_map[index].register_addr >=2600 && data_map[index].register_addr <= 2868 )
    {
        return TRUE;
    }
    return FALSE;
}

int pack_set_para_data(void* dev_inst, void* cmd_buff)
{
    int offset = 0;
    // 正确响应，数据域：回复寄存器地址+寄存器个数
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    unsigned char *data_buff = ((cmd_buf_t *)cmd_buff)->buf; // 准备往data_buff中存放数据

    put_uint16_to_buff(&data_buff[offset], g_reg_addr);
    offset += 2;

    put_uint16_to_buff(&data_buff[offset], g_reg_nums);
    offset += 2;

    ((cmd_buf_t *)cmd_buff)->data_len = offset;
    return SUCCESSFUL;
}

static cmd_handle_register_t s_cmd_handle[] = {
    {DEV_DAC_NORTH_MODBUS, CMD_INVERTER_MODBUS_GET_ANA, CMD_TYPE_NO_POLL, parse_start_addr_and_reg_nums, new_pack_ana_para_data},
    {DEV_DAC_NORTH_MODBUS, CMD_INVERTER_MODBUS_GET_PARA, CMD_TYPE_NO_POLL, parse_start_addr_and_reg_nums, new_pack_ana_para_data},
    {DEV_DAC_NORTH_MODBUS, CMD_INVERTER_MODBUS_SET_PARA, CMD_TYPE_NO_POLL, new_parse_para_data_from_buff, pack_set_para_data},
    {DEV_DAC_NORTH_MODBUS, CMD_INVERTER_MODBUS_CTRL_CMD, CMD_TYPE_NO_POLL, parse_modbus_control_cmd, pack_modbus_control_cmd},
};

void get_data_by_id_type(int index , void* data, modbus_addr_map_data_t* data_map)
{   
    unsigned char id_type = data_map[index].sid_type;

    if(id_type == TYPE_PARA )
    {
        get_one_para( data_map[index].data_addr , data);
    }
    else if(id_type == TYPE_ANA )
    {
        get_one_data( data_map[index].data_addr , data);
    }
    else if(id_type == TYPE_ALAM_PARA)
    {
        get_alm_para( data_map[index].data_addr , data);
    }
    else
    {   

        unsigned char tmp = 0;
        tmp = get_realtime_alarm_value( data_map[index].data_addr );
        // rt_kprintf("get_data_by_id_type | addr:%x | alam_id:%x | value: %d \n",g_modbus_data_map[index].register_addr,g_modbus_data_map[index].data_addr,tmp);
        *(unsigned char *)data = tmp ;
    }
    
}

void modbus_register_cmd_table()
{
    for (int i = 0; i < sizeof(s_cmd_handle) / sizeof(s_cmd_handle[0]); i++)
    {
        register_cmd_handle(&s_cmd_handle[i]);
    }
}

void numchar_to_bit_to_int16(unsigned char* data_buff, int total_len, int* index, int percision,modbus_addr_map_data_t* data_map)
{
    unsigned short data = 0;
    unsigned char bit[16] = {0};
    unsigned int result = 0;
    int i = 0;
    while( i< 16 )
    {
        if (data_map[*index].reserve_flag == 1)
        {
            i = i + data_map[*index].data_len;
            //rt_kprintf(" addr:%x | value: 0 \n",g_modbus_data_map[*index].register_addr);
            
        }
        else
        {
            get_data_by_id_type( *index, &result,data_map);
            //rt_kprintf(" addr:%x | value: %d \n",g_modbus_data_map[*index].register_addr,result);
            bit[i] = result & 0x01;
            i++;
        }
        (*index)++;
    }
    for (int i = 15; i >= 0; i--)
    {
        data += bit[i] << i;
    }
    put_int16_to_buff(&data_buff[total_len], data);
    //rt_kprintf("-------------\n");
}

int deal_special_para(int index , void* data, modbus_addr_map_data_t* data_map)
{
    char info[20]={0};
    if(data_map[index].register_addr == 2406 || data_map[index].register_addr == 2422)
    {
        set_one_para(data_map[index].data_addr, data, FALSE, FALSE);
        rt_memcpy_s(info, 20 ,&((char*)data)[TAR_VER_START_INDEX], TAR_VER_LEN);
        save_version_event(data_map[index].data_addr, info, PARA_TYPE);
        return SUCCESSFUL;
    }
    return  FAILURE;
}

char set_data_by_id_type(int index , void* data, modbus_addr_map_data_t* data_map)
{
    unsigned char id_type = data_map[index].sid_type;
    int ret = 0;

    if(id_type == TYPE_PARA )
    {
        ret = deal_special_para(index,data,data_map);
        RETURN_VAL_IF_FAIL(ret == FAILURE,SUCCESSFUL);
        if(set_one_para( data_map[index].data_addr , data , FALSE, TRUE) < 0)
        {
            return FAILURE;
        }
    }
    else if(id_type == TYPE_ALAM_PARA)
    {
        if(set_alm_para(data_map[index].data_addr, data, TRUE) == FAILURE)
        {
            return FAILURE;
        }
    }
    return SUCCESSFUL;
}

void check_ana_reserve_flag(int index , void* data,modbus_addr_map_data_t* data_map)
{
    if (data_map[index].reserve_flag != 1)
    {
        get_data_by_id_type( index, data, data_map);
        return;
    }
    return;
}


//判断下发的参数是否包含设置
int is_contain_time(int index , int set_flag, modbus_addr_map_data_t* data_map)
{
    int set_time_flag = FALSE;
    // 2000----设置获取系统时间
    if( data_map[index].register_addr <=2005 && data_map[index].register_addr >= 2000 )
    {
        if(set_flag == 0)
        {
            get_time_set_sid_value();
        }     
        if(set_flag == 1)
        {
            set_time_flag = TRUE;
        } 
    }
    return set_time_flag;
}

// 判断是否包含电网标准码的改变
int is_contain_grid_code(int index, modbus_addr_map_data_t* data_map)
{   
    if(data_map[index].register_addr == 2006)
    {
        return TRUE;
    }
    return FALSE;
}

int is_contain_delay_update(int index, modbus_addr_map_data_t* data_map)
{
    if(data_map[index].register_addr == 2438)
    {
        return TRUE;
    }
    return FALSE;
}

int is_contain_uart_conf(int index, modbus_addr_map_data_t* data_map )
{
    int  flag = 0;
    if( data_map[index].register_addr <=2442 && data_map[index].register_addr >= 2440 )
    {
        flag = 1;
    }
    return flag;
}
void set_uart_conf(int flag)
{
    if(flag == 1)
    {
        set_rs485_flag();
    }
    return;
}

void get_time_set_sid_value()
{
    time_base_t  pt_time={0};
    get_time(&pt_time);
    set_one_para(DAC_PARA_ID_SYS_TIME_YEAR_OFFSET  , &pt_time.year  ,FALSE, FALSE);
    set_one_para(DAC_PARA_ID_SYS_TIME_MONTH_OFFSET , &pt_time.month ,FALSE, FALSE);
    set_one_para(DAC_PARA_ID_SYS_TIME_DAY_OFFSET   , &pt_time.day   ,FALSE, FALSE);
    set_one_para(DAC_PARA_ID_SYS_TIME_HOUR_OFFSET  , &pt_time.hour  ,FALSE, FALSE);
    set_one_para(DAC_PARA_ID_SYS_TIME_MINUTE_OFFSET, &pt_time.minute,FALSE, FALSE);
    set_one_para(DAC_PARA_ID_SYS_TIME_SECOND_OFFSET, &pt_time.second,FALSE, FALSE);
}


int check_time_validity(time_base_t* tm)
{   
    get_one_para(DAC_PARA_ID_SYS_TIME_YEAR_OFFSET , &tm->year);
    get_one_para(DAC_PARA_ID_SYS_TIME_MONTH_OFFSET , &tm->month);
    get_one_para(DAC_PARA_ID_SYS_TIME_DAY_OFFSET , &tm->day);
    get_one_para(DAC_PARA_ID_SYS_TIME_HOUR_OFFSET , &tm->hour);
    get_one_para(DAC_PARA_ID_SYS_TIME_MINUTE_OFFSET , &tm->minute);
    get_one_para(DAC_PARA_ID_SYS_TIME_SECOND_OFFSET , &tm->second);
    if( check_time_range(*tm) == FAILURE )
    {
        LOG_E("%s | %d | set RTC time failed \n", __FUNCTION__ , __LINE__);
        return FAILURE;
    }
    return SUCCESSFUL;
}


int set_sys_time(unsigned char* data_buff, int data_valude_indx, int index)
{
    time_base_t tm = {0};
    unsigned short cur_time_data = 0;
    unsigned short time_data = 0;
    time_data = get_uint16_data(&data_buff[data_valude_indx]);
    get_one_para(g_modbus_data_map[index].data_addr, &cur_time_data);
    if(set_one_para(g_modbus_data_map[index].data_addr , &time_data , FALSE, FALSE) < 0)
    {
        return FAILURE;
    }
    if(check_time_validity(&tm) == FAILURE)
    {
        return FAILURE;
    }
    time_t tTime = timestruct_to_time_t(&tm);
    if(tTime < 0)
    {
        return FAILURE;
    }
    if(stime(&tTime) == FAILURE )
    {
        LOG_E("%s | %d | set RTC time failed \n", __FUNCTION__ , __LINE__);
        return FAILURE;
    }
    set_one_para( g_modbus_data_map[index].data_addr , &cur_time_data , FALSE, FALSE);//时间数据校验无误后，参数后面需要写操作记录（由于相同参数不写操作记录，所以还原回去）
    return SUCCESSFUL;
}

void start_power()
{
    int power_status = 1;
    set_one_para(DAC_PARA_ID_POWER_ON_OFF_OFFSET, &power_status,TRUE, TRUE);
    LOG_E("modbus power on");
    send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, DC_AC_SET_REMOTE_CTRL_PARA_DATA);
}

void stop_power()
{
    int power_status = 0;
    set_one_para(DAC_PARA_ID_POWER_ON_OFF_OFFSET, &power_status, TRUE, TRUE);
    LOG_E("modbus power off");
    send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, DC_AC_SET_REMOTE_CTRL_PARA_DATA);
}

int judge_special_ctrl_cmd()
{
    if(g_reg_addr != 3503)
    {
        return FALSE;
    }
    return TRUE;
}

int parse_modbus_control_cmd(void* dev_inst, void* cmd_buff)
{
    unsigned char master_slave_status = 0;
    get_one_data(DAC_DATA_ID_MASTER_SLAVE_STATUS, &master_slave_status);
    RETURN_VAL_IF_FAIL(master_slave_status != PARALLEL_MASTER, NO_NEED_REPONSE);
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    unsigned char *data_buff = ((cmd_buf_t *)cmd_buff)->buf;
    unsigned short ctrl_cmd_len = sizeof(s_modbus_ctrl_cmd) / sizeof(ctrl_cmd_modbus_t);
    int loop = 0;

    g_reg_addr = get_int16_data(&data_buff[0]); // 获取寄存器2字节
    for(loop = 0; loop < ctrl_cmd_len; loop ++)
    {
        if(g_reg_addr == s_modbus_ctrl_cmd[loop].reg_addr)
        {
            if(judge_special_ctrl_cmd() != TRUE)//排除恢复默认值命令
            {
                set_one_data(s_modbus_ctrl_cmd[loop].ctrl_id, &s_modbus_ctrl_cmd[loop].ctrl_status);
                save_real_ctrl_cmd_event(s_modbus_ctrl_cmd[loop].ctrl_id, CTRL_ID_TYPE, s_modbus_ctrl_cmd[loop].info);//写操作记录
            }

            if(s_modbus_ctrl_cmd[loop].ctrl_cmd_id != 0)
            {
                send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, s_modbus_ctrl_cmd[loop].ctrl_cmd_id);
            }

            if(s_modbus_ctrl_cmd[loop].func != NULL)
            {
                s_modbus_ctrl_cmd[loop].func();
            }

            if(g_reg_addr == 3504)
            {
                if(get_restart_south_rtn() != SUCCESSFUL)
                {
                    ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_VALUE;
                }
            }
            break;
        }
    }

    return SUCCESSFUL;
}


int pack_modbus_control_cmd(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    unsigned char *data_buff = ((cmd_buf_t *)cmd_buff)->buf;
    put_int16_to_buff(data_buff, g_reg_addr);//首地址
    data_buff += 2;
    put_int16_to_buff(data_buff, 1);         //寄存器个数
    data_buff += 2;
    ((cmd_buf_t *)cmd_buff)->data_len = data_buff - ((cmd_buf_t *)cmd_buff)->buf;
    return SUCCESSFUL;
}

dev_type_t *init_dev_inverter_modbus_main(void)
{
    return &dev_inverter_modbus_main;
}

int set_reg_addr(short input_reg_addr)
{
    g_reg_addr = input_reg_addr;
    return SUCCESSFUL;
}

int set_reg_nums(short input_reg_nums)
{
    g_reg_nums = input_reg_nums;
    return SUCCESSFUL;
}