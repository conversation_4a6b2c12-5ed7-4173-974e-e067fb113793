#include "fileSys.h"
#include <rtthread.h>
#include <rtdevice.h> 
// #include <dfs_posix.h> //改用<rtthread.h> 加 <rtdevice.h> 
// #include "drv_flash.h" //TODO:BSP暂未实现
#include "fal.h"
// #include "rtdef.h"
// #include "spi_flash.h"

#include "backup_manage.h"
#include "data_type.h"
#include <dfs_fs.h>
#include "reset.h"

#define FS_PARTITION_NAME   "filesys"
static rt_mutex_t s_ptMutexFile;
static int s_lWriteFsCnt = 0;

void initFileSys(void)
{
    //注释掉，是因为底层已经做好了文件系统的初始化
    // fal_init();
    // fal_mtd_nor_device_create(FS_PARTITION_NAME);
    // if (dfs_mount(FS_PARTITION_NAME, "/", "lfs", 0, 0) != 0)
    // {
    //     dfs_mkfs("lfs", FS_PARTITION_NAME);
    //     dfs_mount(FS_PARTITION_NAME, "/", "lfs", 0, 0);
    // }
    s_ptMutexFile = rt_mutex_create("file_lock", RT_IPC_FLAG_PRIO);

    return;
}

unsigned char saveResetData(unsigned char ucType, unsigned char ucData)
{
    int readCount = 0;
    unsigned char aucBuf[4] = {0,};
    char acStr[21];
    // U_Int ucActId ;
    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);
    int fd = open("/resetdata", O_RDWR | O_CREAT);

    if (fd < 0)
    {
        rt_mutex_release(s_ptMutexFile);
        return FALSE;
    }
    readCount = read(fd, &aucBuf, 4);
    if (readCount < 0)
    {
        close(fd);
        rt_mutex_release(s_ptMutexFile);
        return FALSE;
    }

    if (aucBuf[ucType] != ucData)
    {
        // if (ucType == BMS_ADDR )
        // {
        //     rt_snprintf(acStr, sizeof(acStr), "%d,%d", aucBuf[ucType], ucData);
        //     SaveHisOpID(&ucActId, CONTOL_ADDR_CHANGE);
        //     SaveAction(ucActId.iData, acStr);
        // }

        aucBuf[ucType] = ucData;
        lseek(fd, 0, SEEK_SET);
        write(fd, &aucBuf, 4);
        s_lWriteFsCnt++;
    }
    close(fd);
    rt_mutex_release(s_ptMutexFile);

    return TRUE;
}

unsigned char getResetData(unsigned char ucType)
{
    int readCount = 0;
    unsigned char aucBuf[4] = {0,};

    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);
    int fd = open("/resetdata", O_RDWR);
    if (fd < 0)
    {
        rt_mutex_release(s_ptMutexFile);
        return 0;
    }

    readCount = read(fd, &aucBuf, 4);
    if ( readCount == 0 )
    {
        aucBuf[BMS_ADDR] = 1;
        aucBuf[RESET_REASON] = NO_RESET_UNKNOW - 1;
        aucBuf[BATT_THEFT] = 0;
        write(fd, &aucBuf, 4);
        s_lWriteFsCnt++;
    }
    close(fd);
    rt_mutex_release(s_ptMutexFile);

    return aucBuf[ucType];
}

unsigned char SaveUpgradefileInfo(unsigned char ucMode)
{
//    addr_data_def *a,*b;
    int count =0;
    unsigned char aucBuff[32] ={0,};
    unsigned char *p = NULL;
    file_manage_t tFileManage;
    // rt_spi_flash_device_t dev_w25q;
    // unsigned char *pFlag = (unsigned char*)FLAG_PROGRAM;

    // dev_w25q = (rt_spi_flash_device_t)rt_device_find(FAL_USING_NOR_FLASH_DEV_NAME);

    // rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);
    
    // sfud_read( (sfud_flash *)dev_w25q->user_data, 0, sizeof(file_manage_t), (unsigned char *)&tFileManage );   

    // if (ucMode == FLAG_BACKUP )
    // {
    //     if (*pFlag==0xFF)   //烧写后未备份
    //     {
    //         tFileManage.tFileAttr.wTotalFrameNum = MAX_PACKET_NUM;//(count-0x8020000)/0x100+1;
    //         stm32_flash_write(FLAG_PROGRAM, aucBuff, 32);
    //         s_lWriteFsCnt++;
    //     }
    //     else if(tFileManage.ucFlag != FLAG_APP)
    //     {
    //         rt_mutex_release(s_ptMutexFile);
    //         return TRUE;
    //     }
    // }
    // tFileManage.ucUpdateAddr = GetBMSAddr();
    // tFileManage.ucFlag = ucMode;
    // tFileManage.wCrc = CRC_Cal((unsigned char *)&tFileManage, sizeof(file_manage_t)-2);
    // sfud_erase_write( (sfud_flash *)dev_w25q->user_data, 0, sizeof(file_manage_t), (unsigned char *)&tFileManage );
    // s_lWriteFsCnt ++;
    // rt_mutex_release(s_ptMutexFile);

    return FALSE;
}

int readFile(char *pcFilename, unsigned char *pucData, int ulReadsize)
{
    int len = 0;
    int fd = 0;

    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);

    fd = open( pcFilename, O_RDONLY );
    if (fd < 0)
    {
        rt_mutex_release(s_ptMutexFile);
        return 0;
    }
    len = read(fd, pucData, ulReadsize);

    close(fd);
    rt_mutex_release(s_ptMutexFile);

    return len;
}

int writeFile(char *pcFilename, unsigned char *pucData, int ulWritesize)
{
    int len = 0;
    int fd = 0;

    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);

    fd = open( pcFilename, O_WRONLY | O_CREAT );
    if (fd < 0)
    {
        rt_mutex_release(s_ptMutexFile);
        return 0;
    }
    len = write(fd, pucData, ulWritesize);
    s_lWriteFsCnt++;

    close(fd);
    rt_mutex_release(s_ptMutexFile);

    return len;
}

int writeFsCnt(void)
{
    rt_kprintf("write norflash times: %d\n", s_lWriteFsCnt);

    return 0;
}
// FINSH_FUNCTION_EXPORT(writeFsCnt, show write norflash times);  // scons报错
// MSH_CMD_EXPORT(writeFsCnt, show write norflash times);         // scons报错
