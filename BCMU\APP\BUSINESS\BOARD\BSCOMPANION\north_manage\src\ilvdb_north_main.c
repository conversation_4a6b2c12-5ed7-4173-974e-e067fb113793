#include <string.h>
#include <stdlib.h>
#include <rtthread.h>
#include "protocol_bottom_comm.h"
#include "utils_thread.h"
#include "device_type.h"
#include "north_main.h"
#include "data_type.h"
#include "sps.h"
#include "msg.h"
#include "utils_server.h"
#include "dev_ilvdb.h"

#define time_wait_tick  10
#define commun_timeout_cnt  1200
static dev_inst_t* dev_ilvdb;

static dev_init_t dev_init_tab[] = {
    {DEV_CSU, init_dev_ilvdb, NULL, NULL},
};

Static north_mgr_t * init_thread_data(link_inst_t* link_inst, unsigned char mod_id) {
    north_mgr_t* north_mgr = NULL;

    north_mgr = malloc(sizeof(north_mgr_t));
    if (north_mgr == NULL)
        return NULL;
    
    north_mgr->cmd_buff = malloc(sizeof(cmd_buf_t));
    if (north_mgr->cmd_buff == NULL) {
        goto NORTH_MGR_FREE;
    }
    
    north_mgr->north_mq = init_msg_queue(mod_id);
    if (north_mgr->north_mq == NULL) {
        goto CMD_BUFF_FREE;
    }

    north_mgr->link_inst = link_inst;
    return north_mgr;
 
CMD_BUFF_FREE:
    free(north_mgr->cmd_buff);
     
NORTH_MGR_FREE:
    free(north_mgr);
    return NULL;
}

static msg_map north_msg_map[] =
{
    {0,NULL},//临时添加解决编译问题
};

char init_dev_init_tab(void)
{
    char ret = 0;
    ret = init_dev_tab(dev_init_tab, sizeof(dev_init_tab)/sizeof(dev_init_tab[0]));
    return ret;
}

/* 北向初始化*/
void* init_north(void * param) {
    server_info_t *server_info = (server_info_t *)param;
    north_mgr_t* north_mgr = NULL;
    unsigned char src_addr_t;
    src_addr_t = get_src_addr(LOCAL_DEV_TYPE);
    dev_ilvdb = init_dev_inst(DEV_CSU);

    if(dev_ilvdb == NULL)
    {
        return NULL;
    }
    set_host_addr(src_addr_t);
    register_server_msg_map(north_msg_map, server_info);
    north_mgr = init_thread_data(dev_ilvdb->dev_type->link_inst, MOD_ILVDB);

    return north_mgr;
}

/* ILVDB_MAIN收发线程 */
void north_comm_th(void *param) {
    north_mgr_t* north_mgr = (north_mgr_t*)param;
    while (is_running(TRUE)) {
        if(RT_EOK == rt_sem_take(&(north_mgr->link_inst->rx_sem), THREAD_TICK)){
            if (SUCCESSFUL == cmd_recv(dev_ilvdb, north_mgr->cmd_buff)) {    
                cmd_send(dev_ilvdb, north_mgr->cmd_buff);     
            }
            continue;
        }
        rt_thread_mdelay(10);
    }
}











