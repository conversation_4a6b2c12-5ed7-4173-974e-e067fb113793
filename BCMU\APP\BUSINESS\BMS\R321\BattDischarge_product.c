#include "battery.h"
#include "BattDischarge_product.h"
#include "usart.h"
#include "CommCan.h"
#include "fm.h"
#include "MultBattData.h"

static WORD s_wVoltRecCount = 0;
Static BYTE s_ucVoltRecIndex = 0;
static FLOAT s_afDischgRecVolt[VOLT_REC_TIMES] = {0.0f,};
static BOOLEAN IfDisChargeThrough(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);

void JudgeDisChargeThrough(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{

    if(pBattIn->tData.tHardWareStruct.ucCellVoltNum == R121_CELL_VOLT_NUM)
    {
        pBattDeal->bThroughDischg = False;
        return;
    }

    pBattDeal->bThroughDischg = IfDisChargeThrough(pBattIn, pBattDeal);
    return;
}

/**
 * @brief 放电直通进入条件
 * @retval True 放电直通， False 放电非直通
*/
static BOOLEAN IfDisChargeThrough(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    // qtp模式放电直通
    if (GetQtptestFlag()) {
        return True;
    }
    // 受控模式且北向通信正常，则放电非直通，不管是否为电池特性放电模式
    if (RUN_MODE_CONTROLLED == pBattIn->tPara.ucRunMode &&
        !JudgeCommDisconnect() &&
        !IsProtoSetInfoInvalid(RUN_MODE_CONTROLLED)) {
        return False;
    }
    // 如果电池特性模式，且放电直通使能为允许，则放电直通，否则禁止
    if ((BYTE)DISCHG_MODE_BATT_CHARACTERISTIC == pBattIn->tPara.ucUsageScen &&
        pBattIn->tPara.bThroughDischgEnable) {
        return True;
    }

    return False;
}

void UpdateMixPowerOnVolWhenDischg(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    BYTE i = 0;
    FLOAT fMaxVolt = 0.0f;
    FLOAT fMinVolt = 0.0f;
    ////主从机都进入此逻辑避免反复切换
    if (pBattIn->tPara.ucRunMode == RUN_MODE_CONTROLLED && !IsProtoSetInfoInvalid(RUN_MODE_CONTROLLED))
    {
        return;
    }

    ////仅在混用和光伏混用有效
    if (DISCHG_MODE_SELF_ADAPTION != pBattIn->tPara.ucUsageScen && DISCHG_MODE_SOLAR != pBattIn->tPara.ucUsageScen)
    {
        return;
    }

    if (pBattIn->tData.fBusVol < 51.0f)
    {
        ClearVoltRecord();
    }
    TimerPlus(s_wVoltRecCount, 1800); // 30分钟保存一次母排电压
    if (s_wVoltRecCount < 1800)
    {
        return;
    }
    if (s_ucVoltRecIndex < VOLT_REC_TIMES)
    {
        s_wVoltRecCount = 0;
        s_afDischgRecVolt[s_ucVoltRecIndex] = pBattIn->tData.fBusVol;
        if (s_ucVoltRecIndex > 0)
        {
            fMaxVolt = s_afDischgRecVolt[0];
            fMinVolt = s_afDischgRecVolt[0];
            for (i = 1; i <= s_ucVoltRecIndex; i++)
            {
                if (fMaxVolt < s_afDischgRecVolt[i])
                {
                    fMaxVolt = s_afDischgRecVolt[i];
                }
                if (fMinVolt > s_afDischgRecVolt[i])
                {
                    fMinVolt = s_afDischgRecVolt[i];
                }
            }
        }
        s_ucVoltRecIndex += 1;
        if (fMaxVolt - fMinVolt > 0.5f)
        {
            ClearVoltRecord();
        }
    }
    if (s_ucVoltRecIndex == VOLT_REC_TIMES) // 5个小时电压数据未清除
    {
        pBattIn->tPara.fSysPowerOnThreshold = 51.0f;
        pBattDeal->fPowerDownVolt = 51.0f;
        pBattDeal->fDischargeSetVol = 51.0f;
    }

    return;
}

void ClearVoltRecord(void)
{
    s_ucVoltRecIndex = 0;
    s_wVoltRecCount = 0;
    rt_memset(s_afDischgRecVolt, 0, VOLT_REC_TIMES*sizeof(FLOAT));
}

//放电时的错峰放电处理
BOOLEAN JudgePeakShiftDischarge(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal, T_PeakShiftPara *pPeakShift)
{
    if(pBattDeal->bPeakDischg)
    {
        if(JudgePeakShiftCondition(pBattIn,pBattDeal,pPeakShift))
        {
            if(100 - pBattIn->tData.wBattSOC >= pPeakShift->ucPeakShiftDOD || PeakShiftSlaveDisableDischg(pBattIn,pBattDeal,pPeakShift)
               || pBattDeal->ucElectricityPrice == ELECTRICITY_PRICE_VALLEY
               || pBattDeal->ucElectricityPrice == ELECTRICITY_PRICE_LEVEL)
            {
                ////转到充电，平阶段时应禁止放电，此时无法判断母排是否有电，转到充电阶段判断如果没电就放电，有电则充电禁止
                SetChargeMode(BATT_MODE_CHARGE);
                pBattDeal->bPeakDischg = False;
                return False;
            }
            else if(pBattDeal->ucElectricityPrice == ELECTRICITY_PRICE_PEAK)
            {
                return True;
            }
            else
            {
                pBattDeal->bPeakDischg = False;
                return True;
            }
        }
        else
        {
            pBattDeal->bPeakDischg = False;
            return True;
        }
    }
    else
    {
        if(IsMaster())
        {
            pBattDeal->bPeakShiftChgDisable = False;   ////停电时允许充电，避免转来电时出现充电禁止
            pBattDeal->wMixBattFullTimer = 0;
            pBattDeal->bPeakDischg = False;
        }
        return True;
    }
}

//调频放电控制入口函数
BOOLEAN JudgeFmDischgSwit(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    FLOAT fMinVolt = 0.0f;
    FLOAT fAvePower = 0.0f;
    if(!IsMaster())
    {
        return False;
    }

    if(pBattDeal->bFmDischg)
    {
        if(pBattDeal->bPeakShiftOff)
        {
            //停电后来电需要延时一段时间才能正常进行调频
            pBattDeal->bFmChgDisable = False;
            pBattDeal->bFmDischg = False;
            return False;
        }

        if(!pBattIn->tData.tFmData.ucFmStatus)
        {
            SetChargeMode(BATT_MODE_CHARGE);
            pBattDeal->fFmMaxCurr = 0.0f;
            pBattDeal->bFmDischg = False;
            pBattDeal->bFmChgDisable = False;
            setFmCtrl(False, False, 0.0f);
            return True;
        }

        if(pBattIn->tData.tFmData.fFmPower > 0.0f)
        {
            pBattDeal->bFmDischg = False;
            SetChargeMode(BATT_MODE_CHARGE);
            return True;
        }
        else if(pBattIn->tData.tFmData.fFmPower < 0.0f)
        {
            //控制放电功率
            fMinVolt = MIN(pBattIn->tData.fBatVol,  pBattIn->tData.fSysVol);
            if(pBattIn->tData.ucFmNormalSlaveNum == 0)
            {
                return False;
            }
            fAvePower = pBattIn->tData.tFmData.fFmPower * 1000 / (pBattIn->tData.ucFmNormalSlaveNum);
            if(fMinVolt > 0.0f)
            {
                pBattDeal->fFmMaxCurr = fAvePower / fMinVolt;
            }
            pBattDeal->bFmChgDisable = False;
            setFmCtrl(False, True, fAvePower);
        }
        else
        {
            //控制充电停止
            pBattDeal->bFmDischg = False;
            pBattDeal->bFmChgDisable = True;
            setFmCtrl(True, False, 0.0f);
        }
    }

    return False;
}

//主机放电保护，但没有退主机，此时利用从机的平均SOC判断是否达到错峰放电DOD

BOOLEAN PeakShiftSlaveDisableDischg(T_BattInfo *ptBattIn,T_BattDealInfoStruct *pBattDeal,T_PeakShiftPara *ptPeakShift)
{
    if(ptBattIn == NULL || ptPeakShift == NULL || pBattDeal == NULL)
    {
        return False;
    }
    
    if(IsMaster() && GetMasterDischgProtect()
       && (100 - ptBattIn->tData.wSlaveAvgSOC >= ptPeakShift->ucPeakShiftDOD))
    {
        pBattDeal->bPkSftSlaveDisableDischgFlag = True;
        return True;
    }
    else
    {
        pBattDeal->bPkSftSlaveDisableDischgFlag = False;
    }
    
    return False;
}

