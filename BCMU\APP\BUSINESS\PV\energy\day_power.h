/**
 * @file     DAY_POWER.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2023-05-22
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */


#ifndef _DAY_POWER_H
#define _DAY_POWER_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include <stdio.h>
#include <rtthread.h>
#include <rtdevice.h>
#include <string.h>
#include "data_type.h"
#include "dev_dc_ac.h"
#include "utils_server.h"
#include "storage.h"

#define DAY_POWER_PERIOD        5 * 1000   // 5s判断一次时间,为了满足测试的要求
#define SAVE_HOUR               24
#define SAVE_POINT              6          // 每小时保存的点
#define SAVE_MINUTE             10         // 10分钟保存一次
#define ACT_POWER_PRECISION     1000       // 有功功率的精度
#define ENERGY_DATA_VALID       0x55       // 表示数据有效

// 当天功率
#pragma pack(1)
typedef struct
{
    unsigned int power[SAVE_HOUR][SAVE_POINT];
    time_base_t save_time;                      // 保存时间
    unsigned short valid;                       // 表示这个数据有效
    unsigned short crc;
}power_record_t;
#pragma pack()


int init_day_power(void);
void day_power_handle(void);
void send_day_power_msg_to_sample(void *parameter);
int is_same_day(time_base_t* curr_time, time_base_t* last_time);
void save_act_power(void);
int day_power_time_check(time_base_t* curr_time, time_base_t* last_time, unsigned int curr_power);
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _SAMPLE_H