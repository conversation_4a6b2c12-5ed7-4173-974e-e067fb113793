/**
 * @file     device_type.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2023-01-04
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */

 
#ifndef _CLEANING_H
#define _CLEANING_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "addr_distribution.h"


void cleaning_init(cluster_topology_t * param);
void calc_cluster_chg_temp_max_min(void* ana_data);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _CLEANING_H
