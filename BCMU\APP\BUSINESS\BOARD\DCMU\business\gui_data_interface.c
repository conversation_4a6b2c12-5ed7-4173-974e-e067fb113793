
/*
 * @Author: dml00265523
 * @Date: 2025-06-13 10:35:36
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-07-17 17:23:12
 * @Description:
 * @FilePath: \BCMU\APP\BUSINESS\BOARD\DCMU\business\gui_data_interface.c
 */
#include "data_type.h"
#include "realdata_id_in.h"
#include "para_id_in.h"
#include "realdata_save.h"
#include "para_manage.h"
#include "gui_data_interface.h"
#include "utils_rtthread_security_func.h"
#include "alarm_manage.h"
#include "alarm_id_in.h"
#include "his_record.h"
#include "utils_data_type_conversion.h"
#include "MIBTable.h"
#include "io_control.h"
#include "do_ctrl.h"
#include "math.h"

static rt_mutex_t key_mutex = RT_NULL;  // 互斥锁
Static unsigned char s_aucKeyBuf[KEY_BUFF_LEN];    // 键盘缓冲区
Static unsigned char s_ucKeyHead = 0;            // 键盘缓冲区的头指针
Static unsigned char s_ucKeyTail = 0;            // 键盘缓冲区的尾指针
Static T_SysPara s_tSysPara = {0};
Static T_AnalogDataStruct s_tAnalogData = {0};   // 实时模拟量数据
Static T_DigitalDataStruct s_tDigitalData = {0}; // 实时状态量数据

Static char GetAlarmGradePara(void);
Static int GetDcAnalogData(void);
Static int GetEnvAnalogData(void);
Static int GetDcDigitalData(void);
Static int GetEnvDigitalData(void);
Static char get_env_para(void);
Static char GetAlarmRelayPara(void);

/* 初始化互斥锁 */
void key_buffer_init(void)
{
    key_mutex = rt_mutex_create("key_mtx", RT_IPC_FLAG_PRIO);
    return;
}

/****************************************************************************
* 函数名称:    AddKey
* 调    用:    无
* 被 调 用: 外部主函数调用
* 输入参数: ucKeyID -- 写入的键值
* 返 回 值: 无
* 功能描述: 键盘队列的写入
* 作    者:
* 设计日期:
* 修改记录:
* 日    期        版    本        修改人        修改摘要
***************************************************************************/
void AddKey(unsigned char ucKeyID)
{
    rt_mutex_take(key_mutex, RT_WAITING_FOREVER);
    s_aucKeyBuf[s_ucKeyTail] = ucKeyID;

    s_ucKeyTail++;
    s_ucKeyTail %= KEY_BUFF_LEN;

    if (s_ucKeyHead == s_ucKeyTail)
    {
        s_ucKeyHead++;
        s_ucKeyHead %= KEY_BUFF_LEN;
    }
    rt_mutex_release(key_mutex);  // 释放互斥锁
    // SendMessage( MSG_KEY_PRESSED );
    // SetAddkeyTimer(50);//同步按键响应慢

    return;
}

/****************************************************************************
* 函数名称:    DelKey
* 调    用:    无
* 被 调 用: 外部主函数调用
* 输入参数: 无
* 返 回 值: 无
* 功能描述: 读出键盘值
* 作    者:
* 设计日期:
* 修改记录:
* 日    期        版    本        修改人        修改摘要
***************************************************************************/
unsigned char DelKey(void)
{
    unsigned char ucRet;
    rt_mutex_take(key_mutex, RT_WAITING_FOREVER);
    if (s_ucKeyHead == s_ucKeyTail)
    {
        rt_mutex_release(key_mutex);
        return KEY_NOTHING;
    }

    ucRet = s_aucKeyBuf[s_ucKeyHead];

    s_ucKeyHead++;
    s_ucKeyHead %= KEY_BUFF_LEN;
    rt_mutex_release(key_mutex);
    return ucRet;
}



unsigned char CalRealAlarmTotal(void)
{
    return (unsigned char)get_realtime_alarm_count();
}



T_RealAlarmStruct * GetDisRealAlarm(unsigned char ucItem)
{
    static T_RealAlarmStruct real_alarm_data; // 使用静态变量
    alarm_map_t *alarm_map_info;
    real_alarm_info_t real_alarm;

    if (get_nth_realtime_alarm_info(&real_alarm, ucItem) == FAILURE) {
        return NULL;
    }

    real_alarm_data.ucAlarmSn = get_alarm_sn(ALM_ID_GET_ALM_CODE(real_alarm.alm_id), &alarm_map_info);
    real_alarm_data.ucIDIndex = ALM_ID_GET_DEV(real_alarm.alm_id);
    real_alarm_data.ucAlarmState = real_alarm.alm_level;
    time_t_to_timestruct(real_alarm.start_time, &real_alarm_data.tStartTime);

    if (real_alarm_data.ucAlarmState == ALARM_LEVEL_NONE) {
        return NULL;
    }

    return &real_alarm_data;
}

unsigned char get_precision(unsigned char raw_type, unsigned short para_id_offset_base) {
    numeric_para_attr_t* para_attr = NULL;
    unsigned short para_offset = GET_SYS_PARA_OFFSET(para_id_offset_base);
    unsigned char precision = 0;
    switch (raw_type) {
        case TYPE_FLOAT:
            // 获取参数属性表
            RETURN_VAL_IF_FAIL(para_offset <= SYS_NUMERIC_PARA_MAX_OFFSET, 0);
            para_attr = get_numeric_para_attr_tab();
            RETURN_VAL_IF_FAIL(para_attr != NULL, FAILURE);
            precision = para_attr[para_offset].precision;
            break;
        default:
            break;
    }
    return precision;
}


int get_para(unsigned short para_id_offset_base, unsigned char index, void* pSysPara) {
    float temp = 0;
    u_value raw_val = {0};
    MIBTable_ParaNode* ptParaNode = NULL;
    unsigned short para_id_offset = para_id_offset_base + index;
    unsigned char ucRawType = GET_SYS_PARA_DATA_TYPE(para_id_offset);
    unsigned char precision = 0;

    // 参数有效性检查
    RETURN_VAL_IF_FAIL(pSysPara != NULL, FAILURE);

    precision = get_precision(ucRawType, para_id_offset_base);

    // 获取参数节点
    ptParaNode = GetParaNodeByParaId(para_id_offset_base);
    RETURN_VAL_IF_FAIL(ptParaNode != NULL, FAILURE);

    switch (ucRawType) {
        case TYPE_STRING:
        case TYPE_INT16S:
        case TYPE_INT16U:
        case TYPE_INT8S:
        case TYPE_INT8U:
            get_one_para(para_id_offset, pSysPara);
            return SUCCESSFUL;
        case TYPE_FLOAT:
            get_one_para(para_id_offset, &raw_val.f_val);
            temp = raw_val.f_val * pow(10, precision);
            break;
        default:
            return FAILURE;
    }

    switch (ptParaNode->ucDataType) {
        case TYPE_INT8U:
            *(unsigned char*)pSysPara = (unsigned char)roundf(temp);
            break;
        case TYPE_INT8S:
            *(signed char*)pSysPara = (signed char)roundf(temp);
            break;
        case TYPE_INT16S:
            *(signed short*)pSysPara = (signed short)roundf(temp);
            break;
        case TYPE_INT16U:
            *(unsigned short*)pSysPara = (unsigned short)roundf(temp);
            break;
        default:
            return FAILURE;
    }

    return SUCCESSFUL;
}


/**
 * @description: 获取通用参数
 * @return {int} 成功返回SUCCESSFUL
 */
Static char get_common_para1(void)
{
    int i = 0;
    for(i = 0; i < BATT_NUM; i++) {
        get_para(DCMU_PARA_ID_BATTERY_CONFIG_OFFSET, i, &s_tSysPara.tCommonPara1.bBattSetup[i]);
    }
    for(i = 0; i < LOAD_NUM; i++) {
        get_para(DCMU_PARA_ID_LOAD_CONFIG_OFFSET, i, &s_tSysPara.tCommonPara1.bLoadSetup[i]);
    }
    get_para(DCMU_PARA_ID_LOAD_LOOP_CONFIG_OFFSET, 0, &s_tSysPara.tCommonPara1.bLoadStateSetup);
    return SUCCESSFUL;
}

Static char get_common_para2(void)
{
    int i = 0;
    for(i = 0; i < BATT_NUM; i++) {
        get_para(DCMU_PARA_ID_BATTERY_SHUNT_OFFSET, i, &s_tSysPara.tCommonPara2.aiBatShunt[i]);
    }
    for(i = 0; i < LOAD_NUM; i++) {
        get_para(DCMU_PARA_ID_LOAD_SHUNT_OFFSET, i, &s_tSysPara.tCommonPara2.aiLoadShunt[i]);
    }
    return SUCCESSFUL;
}

Static char get_common_para4(void)
{
    int i = 0;

    get_one_para(DCMU_PARA_ID_BUZZER_SWITCH_OFFSET, &s_tSysPara.tCommonPara4.ucBeepOn);
    get_one_para(DCMU_PARA_ID_HISTORY_SAVE_INTERVAL_OFFSET, &s_tSysPara.tCommonPara4.ucHisDataInterval);
    get_one_para(DCMU_PARA_ID_DEVICE_ADDR_OFFSET, &s_tSysPara.tCommonPara4.awDeviceAddr);
    for(i = 0; i < COMM_PORT_NUM; i++) {
        get_para(DCMU_PARA_ID_UART_BAUDRATE_OFFSET, i, &s_tSysPara.tCommonPara4.ucRS232Bps[i]);
    }
    get_one_para(DCMU_PARA_ID_LANGUAGE_SET_OFFSET, &s_tSysPara.tCommonPara4.ucLanguage);
    get_one_para(DCMU_PARA_ID_MENU_PASERWORD_OFFSET, &s_tSysPara.tCommonPara4.aucPaserword[0]);
   
    return SUCCESSFUL;
}

Static char get_env_para(void)
{
    get_para(DCMU_PARA_ID_ENV_TEMPERATURE_SENSOR_UPPER_LIMIT_OFFSET, 0, &s_tSysPara.tEnvPara.scEnvTempMax);
    get_para(DCMU_PARA_ID_ENV_TEMPERATURE_SENSOR_LOWER_LIMIT_OFFSET, 0, &s_tSysPara.tEnvPara.scEnvTempMin);
    get_para(DCMU_PARA_ID_ENV_HUMIDITY_SENSOR_UPPER_LIMIT_OFFSET, 0, &s_tSysPara.tEnvPara.scEnvHumMax);
    get_para(DCMU_PARA_ID_ENV_HUMIDITY_SENSOR_LOWER_LIMIT_OFFSET, 0, &s_tSysPara.tEnvPara.scEnvHumMin);
    return SUCCESSFUL;
}

Static char get_common_para5(void)
{
    get_one_para(DCMU_PARA_ID_USAGE_SCENARIO_OFFSET, &s_tSysPara.tCommonPara5.usageScenario);
    return SUCCESSFUL;
}

/**
 * @description: 获取直流参数
 * @return {int} 成功返回SUCCESSFUL
 */
Static char get_dc_para1(void)
{
    get_para(DCMU_PARA_ID_DC_VOLTAGE_HIGH_THRESHOLD_OFFSET, 0, &s_tSysPara.tDcPara1.iDcVolMax);
    get_para(DCMU_PARA_ID_DC_VOLTAGE_LOW_THRESHOLD_OFFSET, 0, &s_tSysPara.tDcPara1.iDcVolMin);
    get_para(DCMU_PARA_ID_BATTERY_VOLTAGE_LOW_THRESHOLD_OFFSET, 0, &s_tSysPara.tDcPara1.iBattVolLow);
    get_para(DCMU_PARA_ID_BATTERY_VOLTAGE_TOO_LOW_THRESHOLD_OFFSET, 0, &s_tSysPara.tDcPara1.iBattVolTooLow);
    get_para(DCMU_PARA_ID_BATTERY_CURRENT_THRESHOLD_OFFSET, 0, &s_tSysPara.tDcPara1.ucBattCurrFault);
    get_para(DCMU_PARA_ID_BATTERY_TEMPERATURE_HIGH_THRESHOLD_OFFSET, 0, &s_tSysPara.tDcPara1.scBattTempMax);
    get_para(DCMU_PARA_ID_BATTERY_TEMPERATURE_LOW_THRESHOLD_OFFSET, 0, &s_tSysPara.tDcPara1.scBattTempMin);
    get_para(DCMU_PARA_ID_BATTERY_LOOP_BROKEN_THRESHOLD_OFFSET, 0, &s_tSysPara.tDcPara1.iFuseValveVol);
    return SUCCESSFUL;
}

Static char get_dc_para2(void)
{
    get_para(DCMU_PARA_ID_BATTERY_DISCHARGE_THRESHOLD_OFFSET, 0, &s_tSysPara.tDcPara2.aiBattDisCharge);
    return SUCCESSFUL;
}

Static char get_dc_para3(void)
{
    get_para(DCMU_PARA_ID_BATTERY_TEMPERATURE_TOO_HIGH_THRESHOLD_OFFSET, 0, &s_tSysPara.tDcPara3.scBatTempExMax);
    get_para(DCMU_PARA_ID_DC_VOLTAGE_TOO_LOW_THRESHOLD_OFFSET, 0, &s_tSysPara.tDcPara3.aiDcVoltExLow);
    get_para(DCMU_PARA_ID_DC_VOLTAGE_TOO_HIGH_THRESHOLD_OFFSET, 0, &s_tSysPara.tDcPara3.aiDcVoltExHigh);
    return SUCCESSFUL;
}

/**
 * @description: 获取校正参数
 * @return {int} 成功返回SUCCESSFUL
 */
Static char get_adjust_para1(void)
{
    int i = 0;
    get_para(DCMU_PARA_ID_DC_VOLT_ZERO_OFFSET, 0, &s_tSysPara.tAdjustPara1.iDcVoltZero);
    for(i = 0; i < LOAD_NUM; i++) {
        get_para(DCMU_PARA_ID_LOAD_CURRENT_ZERO_OFFSET, i, &s_tSysPara.tAdjustPara1.iDcCurrZero[i]);
        get_para(DCMU_PARA_ID_LOAD_CURRENT_SLOPE_OFFSET, i, &s_tSysPara.tAdjustPara1.iDcCurrSlope[i]);
    }
    for(i = 0; i < BATT_NUM; i++) {
        get_para(DCMU_PARA_ID_BATTERY_VOLT_ZERO_OFFSET, i, &s_tSysPara.tAdjustPara1.aiBattVoltZero[i]);
        get_para(DCMU_PARA_ID_BATTERY_CURRENT_ZERO_OFFSET, i, &s_tSysPara.tAdjustPara1.aiBattCurrZero[i]);
        get_para(DCMU_PARA_ID_BATTERY_CURRENT_SLOPE_OFFSET, i, &s_tSysPara.tAdjustPara1.aiBattCurrSlope[i]);
        get_para(DCMU_PARA_ID_BATTERY_TEMP_ZERO_OFFSET, i, &s_tSysPara.tAdjustPara1.ascBattTempZero[i]);
    }
    return SUCCESSFUL;
}

Static char get_adjust_para2(void)
{
    get_para(DCMU_PARA_ID_ENV_TEMP_ZERO_POINT_OFFSET, 0, &s_tSysPara.tAdjustPara2.scEnvTempZero);
    get_para(DCMU_PARA_ID_ENV_HUMIDITY_ZERO_POINT_OFFSET, 0, &s_tSysPara.tAdjustPara2.scEnvHumZero);
    return SUCCESSFUL;
}

// 定义告警级别参数ID数组，按照指定顺序
const int alarmGradeParaIds[] = {
    DCMU_ALM_ID_TOTAL_ALARM_LEVEL,          //总告警
    DCMU_ALM_ID_DC_VOLT_HIGH_LEVEL,         // 直流电压高
    DCMU_ALM_ID_DC_VOLT_LOW_LEVEL,          // 直流电压低
    DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN_LEVEL, // 负载回路断
    DCMU_ALM_ID_DC_LIGHTNING_PROTECTOR_DAMAGED_LEVEL,// 防雷器异常
    DCMU_ALM_ID_BATTERY_VOLTAGE_LOW_LEVEL,  // 电池电压低
    DCMU_ALM_ID_BATTERY_VOLTAGE_TOO_LOW_LEVEL,         // 电池电压过低
    DCMU_ALM_ID_ABNORMAL_BATTERY_CURRENT_LEVEL,        // 电池电流异常
    DCMU_ALM_ID_BATTERY_TEMPERATURE_HIGH_LEVEL, // 电池温度高
    DCMU_ALM_ID_BATTERY_TEMPERATURE_LOW_LEVEL,    // 电池温度低
    DCMU_ALM_ID_BATTERY_LOOP_BROKEN_LEVEL,     // 电池回路断
    DCMU_ALM_ID_BATTERY_DISCHARGE_LEVEL,// 电池放电
    DCMU_ALM_ID_INVALID_BATTERY_TEMPERATURE_LEVEL,	// 电池温度无效
    DCMU_ALM_ID_BATTERY_TEMPERATURE_TOO_HIGH_LEVEL,	// 电池温度过高
    DCMU_ALM_ID_DC_VOLTAGE_TOO_LOW_LEVEL,	// 直流电压过低
    DCMU_ALM_ID_DC_VOLTAGE_TOO_HIGH_LEVEL,	// 直流电压过高

    DCMU_ALM_ID_ENV_TEMP_HIGH_ALARM_LEVEL,      //环境温度高
    DCMU_ALM_ID_ENV_TEMP_LOW_ALARM_LEVEL,      //环境温度低
    DCMU_ALM_ID_ENV_HUMIDITY_HIGH_ALARM_LEVEL,      //环境湿度高
    DCMU_ALM_ID_ENV_HUMIDITY_LOW_ALARM_LEVEL,      //环境湿度低
    DCMU_ALM_ID_FUMES_SENSOR_ALARM_LEVEL,      //烟雾告警
    DCMU_ALM_ID_FLOOD_SENSOR_ALARM_LEVEL,      //水淹告警
    DCMU_ALM_ID_DOORMAT_SENSOR_ALARM_LEVEL,      //门磁告警
    DCMU_ALM_ID_ENV_TEMP_SENSOR_INVALID_ALARM_LEVEL,      //环境温度失效告警
    DCMU_ALM_ID_ENV_HUMIDITY_SENSOR_INVALID_ALARM_LEVEL,      //环境湿度失效告警
    DCMU_ALM_ID_INPUT_RELAY1_ALARM_LEVEL,      //输入干接点告警
    DCMU_ALM_ID_INPUT_RELAY2_ALARM_LEVEL,      //输入干接点告警
    DCMU_ALM_ID_INPUT_RELAY3_ALARM_LEVEL,      //输入干接点告警
    DCMU_ALM_ID_INPUT_RELAY4_ALARM_LEVEL,      //输入干接点告警
};

// 定义告警干接点参数ID数组，按照指定顺序
const int alarmRelayParaIds[] = {
    DCMU_ALM_ID_TOTAL_ALARM_RELAY,          //总告警
    DCMU_ALM_ID_DC_VOLT_HIGH_RELAY,         // 直流电压高
    DCMU_ALM_ID_DC_VOLT_LOW_RELAY,          // 直流电压低
    DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN_RELAY, // 负载回路断
    DCMU_ALM_ID_DC_LIGHTNING_PROTECTOR_DAMAGED_RELAY,// 防雷器异常
    DCMU_ALM_ID_BATTERY_VOLTAGE_LOW_RELAY,  // 电池电压低
    DCMU_ALM_ID_BATTERY_VOLTAGE_TOO_LOW_RELAY,         // 电池电压过低
    DCMU_ALM_ID_ABNORMAL_BATTERY_CURRENT_RELAY,        // 电池电流异常
    DCMU_ALM_ID_BATTERY_TEMPERATURE_HIGH_RELAY, // 电池温度高
    DCMU_ALM_ID_BATTERY_TEMPERATURE_LOW_RELAY,    // 电池温度低
    DCMU_ALM_ID_BATTERY_LOOP_BROKEN_RELAY,     // 电池回路断
    DCMU_ALM_ID_BATTERY_DISCHARGE_RELAY,// 电池放电
    DCMU_ALM_ID_INVALID_BATTERY_TEMPERATURE_RELAY,	// 电池温度无效
    DCMU_ALM_ID_BATTERY_TEMPERATURE_TOO_HIGH_RELAY,	// 电池温度过高
    DCMU_ALM_ID_DC_VOLTAGE_TOO_LOW_RELAY,	// 直流电压过低
    DCMU_ALM_ID_DC_VOLTAGE_TOO_HIGH_RELAY,	// 直流电压过高

    DCMU_ALM_ID_ENV_TEMP_HIGH_ALARM_RELAY,      //环境温度高
    DCMU_ALM_ID_ENV_TEMP_LOW_ALARM_RELAY,      //环境温度低
    DCMU_ALM_ID_ENV_HUMIDITY_HIGH_ALARM_RELAY,      //环境湿度高
    DCMU_ALM_ID_ENV_HUMIDITY_LOW_ALARM_RELAY,      //环境湿度低
    DCMU_ALM_ID_FUMES_SENSOR_ALARM_RELAY,      //烟雾告警
    DCMU_ALM_ID_FLOOD_SENSOR_ALARM_RELAY,      //水淹告警
    DCMU_ALM_ID_DOORMAT_SENSOR_ALARM_RELAY,      //门磁告警
    DCMU_ALM_ID_ENV_TEMP_SENSOR_INVALID_ALARM_RELAY,      //环境温度失效告警
    DCMU_ALM_ID_ENV_HUMIDITY_SENSOR_INVALID_ALARM_RELAY,      //环境湿度失效告警
    DCMU_ALM_ID_INPUT_RELAY1_ALARM_RELAY,      //输入干接点告警
    DCMU_ALM_ID_INPUT_RELAY2_ALARM_RELAY,
    DCMU_ALM_ID_INPUT_RELAY3_ALARM_RELAY,
    DCMU_ALM_ID_INPUT_RELAY4_ALARM_RELAY,
};

Static char GetAlarmGradePara(void)
{
    // 检查告警ID数组长度是否超过系统参数数组长度
    if (sizeof(alarmGradeParaIds) / sizeof(alarmGradeParaIds[0]) > ALARM_CLASS_NUM) {
        return FAILURE; // 超出数组范围，记录错误日志或返回失败
    }

    // 获取告警级别参数,存到系统参数中
    for (int i = 0; i < sizeof(alarmGradeParaIds) / sizeof(alarmGradeParaIds[0]); i++) 
    {
        get_alm_para(alarmGradeParaIds[i], &s_tSysPara.tAlarmGradePara.aucAlmGrade[i]);
        get_alm_para(alarmRelayParaIds[i], &s_tSysPara.tAlarmGradePara.aucAlmOutRly[i]);
    }

    return SUCCESSFUL;
}

/**
 * @description: 获取告警干节点参数
 * @return {int} 成功返回SUCCESSFUL
 */
Static char GetAlarmRelayPara(void)
{
    int i = 0;
    char relay_name[LEN_RELAYNAME];

    for(i = 0; i < RELAY_NUM; i++) {
        get_one_para(DCMU_PARA_ID_INRELAYTTL_OFFSET + i, &s_tSysPara.tAlarmRelayPara.aucRlyAlarmTTL[i]);
        get_alm_para(DCMU_ALM_ID_INPUT_RELAY1_ALARM_LEVEL + i, &s_tSysPara.tAlarmRelayPara.aucRlyAlarmGrade[i]);
        get_one_para(DCMU_PARA_ID_INRELAYNAME_OFFSET + i, s_tSysPara.tAlarmRelayPara.acRlyName[i]);
    }

    return SUCCESSFUL;
}

/**
 * @description: 获取系统参数
 * @param {unsigned char} ucParaType 参数类型
 * @param {char *} pDest 目标结构体变量首地址
 * @return {*}
 */
short GetSysPara( unsigned char ucParaType, char * pDest )
{
    char * ptSource;
    unsigned short ucSize;
    T_SysPara* pDestSysPara = (T_SysPara* )pDest;

    RETURN_VAL_IF_FAIL(pDest != NULL, FAILURE);

    switch ( ucParaType )
    {
        case PARATYPE_SYSTEM:
            get_common_para1();
            ptSource = ( char* )&s_tSysPara.tCommonPara1;
            ucSize = sizeof( T_CommonParaStruct1 );
            rt_memcpy_s( (char*)&pDestSysPara->tCommonPara1, ucSize, ptSource, ucSize );
            get_common_para2();
            ptSource = ( char* )&s_tSysPara.tCommonPara2;
            ucSize = sizeof( T_CommonParaStruct2 );
            rt_memcpy_s( (char*)&pDestSysPara->tCommonPara2, ucSize, ptSource, ucSize );
            get_common_para4();
            ptSource = ( char* )&s_tSysPara.tCommonPara4;
            ucSize = sizeof( T_CommonParaStruct4 );
            rt_memcpy_s( (char*)&pDestSysPara->tCommonPara4, ucSize, ptSource, ucSize );
            get_common_para5();
            ptSource = ( char* )&s_tSysPara.tCommonPara5;
            ucSize = sizeof( T_CommonParaStruct5 );
            rt_memcpy_s( (char*)&pDestSysPara->tCommonPara5, ucSize, ptSource, ucSize );
            break;
        case PARATYPE_DC:
            get_dc_para1();
            ptSource = ( char* )&s_tSysPara.tDcPara1;
            ucSize = sizeof( T_DcParaStruct1 );
            rt_memcpy_s( (char*)&pDestSysPara->tDcPara1, ucSize, ptSource, ucSize );
            get_dc_para2();
            ptSource = ( char* )&s_tSysPara.tDcPara2;
            ucSize = sizeof( T_DcParaStruct2 );
            rt_memcpy_s( (char*)&pDestSysPara->tDcPara2, ucSize, ptSource, ucSize );
            get_dc_para3();
            ptSource = ( char* )&s_tSysPara.tDcPara3;
            ucSize = sizeof( T_DcParaStruct3 );
            rt_memcpy_s( (char*)&pDestSysPara->tDcPara3, ucSize, ptSource, ucSize );
            break;
        case PARATYPE_ENV:
            get_env_para();
            ptSource = (char*)&s_tSysPara.tEnvPara;
            ucSize = sizeof(T_EnvParaStruct);
            break;
        case PARATYPE_ADJUST:
            get_adjust_para1();
            ptSource = ( char* )&s_tSysPara.tAdjustPara1;
            ucSize = sizeof( T_AdjustParaStruct1 );
            rt_memcpy_s( (char*)&pDestSysPara->tAdjustPara1, ucSize, ptSource, ucSize );
            get_adjust_para2();
            ptSource = ( char* )&s_tSysPara.tAdjustPara2;
            ucSize = sizeof( T_AdjustParaStruct2 );
            rt_memcpy_s( (char*)&pDestSysPara->tAdjustPara2, ucSize, ptSource, ucSize );
            break;
        case PARATYPE_ATRRIBUTE:
			GetAlarmGradePara();
            ptSource = ( char* )&s_tSysPara.tAlarmGradePara;
            ucSize = sizeof( T_AlarmGradeParaStruct );
            rt_memcpy_s( (char*)&pDestSysPara->tAlarmGradePara, ucSize, ptSource, ucSize );
			break;
        case PARATYPE_ALARM_RELAY:
            GetAlarmRelayPara();
            ptSource = ( char* )&s_tSysPara.tAlarmRelayPara;
            ucSize = sizeof( T_AlarmRelayParaStruct );
            rt_memcpy_s( (char*)&pDestSysPara->tAlarmRelayPara, ucSize, ptSource, ucSize );
            break;
        case PARATYPE_ALL:
            get_common_para1();
            get_common_para2();
            get_common_para4();
            get_common_para5();
            get_dc_para1();
            get_dc_para2();
            get_dc_para3();
            get_env_para();
            get_adjust_para1();
            get_adjust_para2();
			GetAlarmGradePara();
            GetAlarmRelayPara();
            ptSource = ( char* )&s_tSysPara;
            ucSize = sizeof(T_SysPara);
            rt_memcpy_s((char*)pDestSysPara, ucSize,  ptSource, ucSize);
			break;
        
        default:
            ptSource = ( char* )&s_tSysPara;
            ucSize = 0;
            rt_memcpy_s(pDest, ucSize, ptSource, ucSize);
            break;
    }
    return SUCCESSFUL;
}

int convert_gui_to_raw(void *pGuiValue, int guiType, int rawType, unsigned char precision, u_value *pRawValue) {   
    float temp = 0.0f;

    RETURN_VAL_IF_FAIL(pGuiValue != NULL && pRawValue!= NULL, FAILURE);

    switch (guiType) {
        case TYPE_INT8U:
            temp = (float)(*(unsigned char*)pGuiValue);
            break;
        case TYPE_INT8S:
            temp = (float)(*(char*)pGuiValue);
            break;
        case TYPE_INT16S:
            temp = (float)(*(short*)pGuiValue);
            break;
        case TYPE_INT16U:
            temp = (float)(*(unsigned short*)pGuiValue);
            break;
        default:
            return FAILURE;
    }

    switch (rawType) {
        case TYPE_FLOAT:
            pRawValue->f_val = temp / pow(10, precision);
            break;
        case TYPE_INT8S:
            pRawValue->sc_val = (signed char)(temp / pow(10, precision));
            break;
        case TYPE_INT8U:
            pRawValue->uc_val = (unsigned char)(temp / pow(10, precision));
            break;
        case TYPE_INT16S:
            pRawValue->ss_val = (signed short)(temp / pow(10, precision));
            break;
        case TYPE_INT16U:
            pRawValue->us_val = (unsigned short)(temp / pow(10, precision));
            break;
        default:
            return FAILURE;
    }

    return SUCCESSFUL;
}

int GetPeakData(char* pDest)
{
    T_PeakDataStruct tPeakData;
    his_extreme_data_t extreme_data;
    int ret = SUCCESSFUL;

    RETURN_VAL_IF_FAIL(pDest != NULL, FAILURE);

    // 初始化结构体
    rt_memcpy_s(&tPeakData, sizeof(T_PeakDataStruct), 0, sizeof(T_PeakDataStruct));
    rt_memcpy_s(&extreme_data, sizeof(his_extreme_data_t), 0, sizeof(his_extreme_data_t));

    // 读取历史极值数据
    ret = pub_hisrecord_read_msg(RECORD_TYPE_EXTREME_DATA, 1, 0, &extreme_data);
    RETURN_VAL_IF_FAIL(ret == SUCCESSFUL, FAILURE);

    // 负载总电流最大值
    tPeakData.tMaxTotalLoadCurr.iCurr = extreme_data.total_load_curr_max;
    time_t_to_timestruct(extreme_data.total_load_curr_max_time, &tPeakData.tMaxTotalLoadCurr.tTime);

    // 电池放电电流最大值
    tPeakData.tMaxDischgBattCurr.iCurr = extreme_data.batt_discharge_curr_max;
    time_t_to_timestruct(extreme_data.batt_discharge_curr_max_time, &tPeakData.tMaxDischgBattCurr.tTime);

    // 电池温度最大值
    tPeakData.tMaxBattTemp.scBattTemp = extreme_data.batt_temp_max;
    time_t_to_timestruct(extreme_data.batt_temp_max_time, &tPeakData.tMaxBattTemp.tTime);

    // 环境温度最大值
    tPeakData.tMaxEnvTemp.scEnvTemp = extreme_data.env_temp_max;
    time_t_to_timestruct(extreme_data.env_temp_max_time, &tPeakData.tMaxEnvTemp.tTime);

    // 环境温度最小值
    tPeakData.tMinEnvTemp.scEnvTemp = extreme_data.env_temp_min;
    time_t_to_timestruct(extreme_data.env_temp_min_time, &tPeakData.tMinEnvTemp.tTime);

    // 直流电压最大值
    tPeakData.tMaxDcVolt.iDcVolt = extreme_data.dc_volt_max;
    time_t_to_timestruct(extreme_data.dc_volt_max_time, &tPeakData.tMaxDcVolt.tTime);

    // 复制到目标缓冲区
    rt_memcpy_s(pDest, sizeof(T_PeakDataStruct), &tPeakData, sizeof(T_PeakDataStruct));
    return SUCCESSFUL;
}

/**
 * @description: 获取历史事件记录数量
 * @return {unsigned short} 保存数目
 */
unsigned short GetHisEventNum( void ){
    unsigned short saved_num = 0;

    pub_get_saved_record_num_msg(RECORD_TYPE_HIS_ACTION, &saved_num);
    return saved_num;
}

/**
 * @description: 获取历史告警数量
 * @return {unsigned short} 保存数目
 */
unsigned short GetHisAlmNum(void)
{
    unsigned short saved_num = 0;

    pub_get_saved_record_num_msg(RECORD_TYPE_HIS_ALARM, &saved_num);
    return saved_num;
}

/**
 * @description:
 * @param {unsigned short} index
 * @param {T_EventRecord*} dest
 * @return {*}
 */
short GetDisHisEvent(unsigned short index, T_EventRecord* dest)
{
    his_action_record_info his_action_record;
    T_EventRecord his_event;
    int ret = SUCCESSFUL;

    RETURN_VAL_IF_FAIL(dest != NULL, FAILURE);

    rt_memset_s(&his_action_record, sizeof(his_action_record_info), 0, sizeof(his_action_record_info));
    rt_memset_s(&his_event, sizeof(T_EventRecord), 0, sizeof(T_EventRecord));

    RETURN_VAL_IF_FAIL(index < MAX_HIS_ACTION_NUM, FAILURE);

    ret = pub_hisrecord_read_msg(RECORD_TYPE_HIS_ACTION, 1, index, &his_action_record);
    RETURN_VAL_IF_FAIL(ret == SUCCESSFUL, FAILURE);

    his_event.ucID1 = his_action_record.id1;
    his_event.ucID2 = his_action_record.id2;
    his_event.ucIndex = his_action_record.index;
    time_t_to_timestruct(his_action_record.save_time, &his_event.tTime);
    rt_memcpy_s(his_event.aucMsg, sizeof(his_event.aucMsg), his_action_record.msg, sizeof(his_action_record.msg));

    rt_memcpy_s(dest, sizeof(T_EventRecord), &his_event, sizeof(his_event));

    return SUCCESSFUL;
}

/**
 * @description:
 * @param {unsigned short} index 告警偏移
 * @param {T_HisAlarmStruct*} dest 出参
 * @param {MIB_AlarmDataNode*} alarm_node 出参
 * @return {short} 成功SUCCESSFUL；失败FAILURE
 */
short GetDisHisAlarmAndNode(unsigned short index, T_HisAlarmStruct* dest, MIB_AlarmDataNode* alarm_node)
{
    his_alarm_info_t his_alarm_record;
    T_HisAlarmStruct his_alarm;
    MIB_AlarmDataNode alarm_data_node;
    int ret = SUCCESSFUL;
    alarm_map_t *alarm_map;

    RETURN_VAL_IF_FAIL(dest != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(alarm_node != NULL, FAILURE);

    rt_memset_s(&his_alarm_record, sizeof(his_alarm_record), 0, sizeof(his_alarm_record));
    rt_memset_s(&his_alarm, sizeof(T_HisAlarmStruct), 0, sizeof(T_HisAlarmStruct));
    rt_memset_s(&alarm_data_node, sizeof(alarm_data_node), 0, sizeof(alarm_data_node));

    RETURN_VAL_IF_FAIL(index < MAX_HIS_ALARM_NUM, FAILURE);

    ret = pub_hisrecord_read_msg(RECORD_TYPE_HIS_ALARM, 1, index, &his_alarm_record);

    RETURN_VAL_IF_FAIL(ret == SUCCESSFUL, FAILURE);

    ret = get_alarm_sn(his_alarm_record.alarm_id, &alarm_map);

    RETURN_VAL_IF_FAIL(ret != FAILURE, FAILURE);

    his_alarm.ucAlarmSn = ret;
    his_alarm.ucIDIndex = his_alarm_record.index;
    his_alarm.ucAlarmState = his_alarm_record.alarm_state;
    time_t_to_timestruct(his_alarm_record.start_time, &his_alarm.tStartTime);
    time_t_to_timestruct(his_alarm_record.end_time, &his_alarm.tEndTime);

    rt_memcpy_s(dest, sizeof(T_HisAlarmStruct), &his_alarm, sizeof(his_alarm));

    alarm_data_node.ucID1 = alarm_map->id1;
    alarm_data_node.ucID2 = alarm_map->id2;
    alarm_data_node.ucIDNumber = alarm_map->number;

    rt_memcpy_s(alarm_node, sizeof(MIB_AlarmDataNode), &alarm_data_node, sizeof(alarm_data_node));
    return SUCCESSFUL;
}

unsigned char GetCtrlOut( T_CtrlOutStruct * tCtrlOut )
{
    unsigned char i = 0;
    unsigned char status = 0;
    for(i = 0; i < OUT_RELAY_MAX_NUM; i ++)
    {
        get_one_data(DCMU_DATA_ID_OUTRELAY_MANUAL_CONTROL_STATUS + i, &status);
        if(status)
        {
            tCtrlOut->wRelayOut |= 0x0001 << i;
        }
        tCtrlOut->aucRelay[i] = status;
    }
    return SUCCESSFUL;
}

unsigned char SetCtrlOut( T_CtrlOutStruct * tCtrlOut )
{
    unsigned char i = 0;
    for(i = 0; i < OUT_RELAY_MAX_NUM; i ++)
    {
        if(tCtrlOut->aucRelay[i] == CTRL_ON)
        {
            control_out_relay(i, DO_ACTIVE);
        }
        else if(tCtrlOut->aucRelay[i] == CTRL_OFF)
        {
            control_out_relay(i, DO_NOT_ACTIVE);
        }
        set_one_data(DCMU_DATA_ID_OUTRELAY_MANUAL_CONTROL_STATUS + i, &tCtrlOut->aucRelay[i]);
    }
    return SUCCESSFUL;
}

unsigned char clear_ctrl_out(void)
{
    unsigned char i = 0;
    unsigned char manual_control_status = CTRL_NULL;
    unsigned char manual_control_mode = 0;
    for(i = 0; i < OUT_RELAY_MAX_NUM; i ++)
    {
        set_one_data(DCMU_DATA_ID_OUTRELAY_MANUAL_CONTROL_STATUS + i, &manual_control_status);
        control_out_relay(i, DO_NOT_ACTIVE);
    }
    set_one_data(DCMU_DATA_ID_OUTRELAY_MANUAL_CONTROL_MODE, &manual_control_mode);
    return SUCCESSFUL;
}

Static int GetDcAnalogData(void)
{
    int i = 0;
    float f_data = 0.0f;

    get_one_data(DCMU_DATA_ID_DC_OUTPUT_VOLTAGE, &f_data); // 直流输出电压（精度1）
    s_tAnalogData.tDcAnalogData.iVout = f_data * 10;

    get_one_data(DCMU_DATA_ID_TOTAL_LOAD_CURRENT, &f_data); // 负载总电流（精度1）
    s_tAnalogData.tDcAnalogData.iLoadTotalIout = f_data * 10;

    for (i = 0; i < LOAD_NUM; i++) {
        get_one_data(DCMU_DATA_ID_LOAD_CURRENT + i, &f_data); // 分路负载电流（精度1）
        s_tAnalogData.tDcAnalogData.iIout[i] = f_data * 10;
    }

    for (i = 0; i < BATT_NUM; i++) {
        get_one_data(DCMU_DATA_ID_BATTERY_VOLTAGE + i, &f_data);  // 电池电压（精度1）
        s_tAnalogData.tDcAnalogData.aiBattVolt[i] = f_data * 10;
    }

    for (i = 0; i < BATT_NUM; i++) {
        get_one_data(DCMU_DATA_ID_BATTERY_CURRENT + i, &f_data); // 电池电流（精度1）
        s_tAnalogData.tDcAnalogData.aiBattCurr[i] = f_data * 10;
    }

    for (i = 0; i < BATT_NUM; i++) {
        get_one_data(DCMU_DATA_ID_BATTERY_TEMPERATURE + i, &f_data); // 电池温度（精度0）
        s_tAnalogData.tDcAnalogData.ascBattTemp[i] = f_data;
    }

    return SUCCESSFUL;
}

Static int GetEnvAnalogData(void)
{
    char s8_data = 0;

    get_one_data(DCMU_DATA_ID_ENV_TEMP, &s8_data); // 环境温度（精度0）
    s_tAnalogData.tEnvAnalogData.scTemp = s8_data;

    get_one_data(DCMU_DATA_ID_ENV_HUMIDITY, &s8_data); // 环境湿度（精度0）
    s_tAnalogData.tEnvAnalogData.scHum = s8_data;

    return SUCCESSFUL;
}

/**
 * @description: 获取实时模拟量数据
 * @param {unsigned char} ucPart 数据段
 * @param {char *} pDest 目标结构体变量首地址
 * @return {*}
 */
int GetAnalogData(unsigned char ucPart, char* pDest)
{
    char* ptSource;
    unsigned char ucSize;

    RETURN_VAL_IF_FAIL(pDest != NULL, FAILURE);

    switch (ucPart)
    {
        case DC_PART:
            GetDcAnalogData();
            ptSource = (char*)&s_tAnalogData.tDcAnalogData;
            ucSize = sizeof(T_DcAnalogDataStruct);
            break;
        case ENV_PART:
            GetEnvAnalogData();
            ptSource = (char*)&s_tAnalogData.tEnvAnalogData;
            ucSize = sizeof(T_EnvAnalogDataStruct);
            break;
        case ALL_PART:
            GetDcAnalogData();
            GetEnvAnalogData();
            ptSource = (char*)&s_tAnalogData;
            ucSize = sizeof(T_AnalogDataStruct);
            break;
        default:
            ptSource = (char*)&s_tAnalogData;
            ucSize = 0;
            break;
    }

    rt_memcpy_s(pDest, ucSize, ptSource, ucSize);

    return SUCCESSFUL;
}

Static int GetDcDigitalData(void)
{
    int i = 0;
    unsigned char uc_data = 0;
    float batt_temp = 0.0f;

    // 电池温度在正常范围（-40℃~100℃）内，则认为温度传感器正常，否则认为不正常，液晶屏显示电池温度无效
    for (i = 0; i < BATT_NUM; i++) {
        get_one_data(DCMU_DATA_ID_BATTERY_TEMPERATURE + i, &batt_temp);
        if(fabsf(batt_temp - TEMPERATURE_INVALID) < 0.01f) {
            s_tDigitalData.tDCData.abBattTempExist[i] = 0; // 温度传感器在位标志（0：不在位；1：在位）
        }
        else {
            s_tDigitalData.tDCData.abBattTempExist[i] = 1;
        }
    }

    for (i = 0; i < FUSE_NUM; i++) {
        get_one_data(DCMU_DATA_ID_LOAD_LOOP_CONFIG + i, &uc_data);
        s_tDigitalData.tDCData.aucLoadLoop[i] = uc_data; // 负载回路（0：正常；1：断开）
    }

    // （暂未实现，后续添加后放开注释）
    // for (i = 0; i < BATT_NUM; i++) {
    //     get_one_para(DCMU_DATA_ID_BATT_LOOP_CONFIG + i, &uc_data);
    //     s_tDigitalData.tDCData.aucBattLoop[i] = uc_data; // 电池回路（0：正常；1：断开）
    // }

    get_one_data(DCMU_DATA_ID_DC_LIGHTNIN_PROTECTION, &uc_data);
    s_tDigitalData.tDCData.ucDCSPD = uc_data; // 直流防雷状态（0：正常；1：故障）

    for (i = 0; i < IN_RELAY_NUM; i++) {
        get_one_data(DCMU_DATA_ID_INPUT_RELAY + i, &uc_data);
        s_tDigitalData.tDCData.aucInputRelay[i] = uc_data; // 输入干接点状态（0：正常；1：断开）
    }

    return SUCCESSFUL;
}

Static int GetEnvDigitalData(void)
{
    unsigned char uc_data = 0;

    get_one_data(DCMU_DATA_ID_DC_FUMES_SENSOR, &uc_data);
    s_tDigitalData.tENVData.ucENVSmog = uc_data; // 烟雾传感器状态（0：正常；1：故障）

    get_one_data(DCMU_DATA_ID_DC_FLOOD_SENSOR, &uc_data);
    s_tDigitalData.tENVData.ucENVFlood = uc_data; // 水淹传感器状态（0：正常；1：故障）

    // （暂未实现，后续添加后放开注释）
    // get_one_data(DCMU_DATA_ID_DC_DOORMAG_SENSOR, &uc_data);
    // s_tDigitalData.tENVData.ucENCIntrusion = uc_data; // 门禁传感器状态（0：正常；1：故障）

    get_one_data(DCMU_DATA_ID_DC_DOORMAG_SENSOR, &uc_data);
    s_tDigitalData.tENVData.ucENVDoorMag = uc_data; // 门磁传感器状态（0：正常；1：故障）

    // （暂未实现，后续添加后放开注释）
    // get_one_data(ACMU_DATA_ID_DC_GLASS_SENSOR, &uc_data);
    // s_tDigitalData.tENVData.ucENVGlassBroken = uc_data; // 玻璃碎传感器状态（0：正常；1：故障）

    return SUCCESSFUL;
}

/**
 * @description: 获取实时数字量数据
 * @param {unsigned char} ucPart 数据段
 * @param {char *} pDest 目标结构体变量首地址
 * @return {*}
 */
int GetDigitalData(unsigned char ucPart, char* pDest)
{
    char* ptSource;
    unsigned char ucSize;

    RETURN_VAL_IF_FAIL(pDest != NULL, FAILURE);

    switch (ucPart)
    {
        case DC_PART:
            GetDcDigitalData();
            ptSource = (char*)&s_tDigitalData.tDCData;
            ucSize = sizeof(T_DCDigitalDataStruct);
            break;
        case ENV_PART:
            GetEnvDigitalData();
            ptSource = (char*)&s_tDigitalData.tENVData;
            ucSize = sizeof(T_ENVDigitalDataStruct);
            break;
        case ALL_PART:
            GetDcDigitalData();
            GetEnvDigitalData();
            ptSource = (char*)&s_tDigitalData;
            ucSize = sizeof(T_DigitalDataStruct);
            break;
        default:
            ptSource = (char*)&s_tDigitalData;
            ucSize = 0;
            break;
    }

    rt_memcpy_s(pDest, ucSize, ptSource, ucSize);

    return SUCCESSFUL;
}