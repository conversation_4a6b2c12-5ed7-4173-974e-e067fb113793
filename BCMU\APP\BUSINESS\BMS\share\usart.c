/**
  ******************************************************************************
  * @file    usart.c
  * @brief   This file provides code for the configuration
  *          of the USART instances.
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; Copyright (c) 2021 STMicroelectronics.
  * All rights reserved.</center></h2>
  *
  * This software component is licensed by ST under BSD 3-Clause license,
  * the "License"; You may not use this file except in compliance with the
  * License. You may obtain a copy of the License at:
  *                        opensource.org/licenses/BSD-3-Clause
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include <rtdebug.h>
#include <rtdevice.h>
#include <board.h>
#include "comm.h"
#include "utils_heart_beat.h"
#include "thread_id.h"
#include "common.h"
#include "usart.h"
#include "battery.h"
#include "protocol.h"
#include "prtclDL.h"
#include "MdbsRtu.h"
#include "rs485.h"
#include "realAlarm.h"
#include "interface.h"
#include "drv_common.h"

Static BOOLEAN s_bCommDisconnect = False;     // 通信断状态(通过通信清掉)
static BOOLEAN s_bIsNthCommEverConnected = False; // 用于判断：完成布防后，北向通信是否正常连接成功过

static struct rt_semaphore uart1_rx_sem;

Static void UartComm(T_CommStruct *ptComm);
Static void checkNorthComm(T_CommStruct *ptComm);
Static void initUart1(T_CommStruct *ptComm, BYTE ucPrtclType);
Static rs485_inst_t * RS485_Device = RT_NULL;
static UINT32 s_wBaudMode = BAUD_RATE_9600;
Static BYTE s_ucHandShakeCounter = 0;

Static rt_err_t uart1_haveData(rt_device_t dev, rt_size_t size)
{
    RETURN_VAL_IF_FAIL(dev != RT_NULL, -RT_ERROR);
    RETURN_VAL_IF_FAIL(dev->user_data != RT_NULL, -RT_ERROR);
    T_CommStruct *ptComm = (T_CommStruct *)dev->user_data;
    if (RT_UINT32_MAX == size)      //总线错误
    {
        ptComm->ucLineErr = 1;
    }

    rt_sem_release(&uart1_rx_sem);

    return RT_EOK;
}

Static rt_err_t uart1_SendOver(rt_device_t dev, void *p)
{
    RETURN_VAL_IF_FAIL(dev != RT_NULL, -RT_ERROR);
    RETURN_VAL_IF_FAIL(dev->user_data != RT_NULL, -RT_ERROR);
    T_CommStruct *ptComm = (T_CommStruct *)dev->user_data;
    ptComm->wSendLength = 0;

    return RT_EOK;
}

Static void initUart1(T_CommStruct *ptComm, BYTE ucPrtclType)
{
    char *serial = "usart1";
    int baudrate = s_wBaudMode;
    #if defined(ZXESM_R321_APTOWER_VERSION) || defined(PAKISTAN_CMPAK_PROTOCOL)
    int parity   = PARITY_NONE;
    #else
    int parity   = (ucPrtclType==PROTOCOL_MODBUS)? PARITY_EVEN : PARITY_NONE;
    #endif
    int pin      = get_pin_by_name(BSP_UART1_DE_PIN, 0, 0);

    rt_memset(ptComm, 0, sizeof(T_CommStruct));
    ptComm->ucPrtclType = ucPrtclType;
//连接485设备
    if(RT_NULL != RS485_Device)
    {
       rs485_destory(RS485_Device);
    }
    RS485_Device=rs485_create(serial,baudrate,parity,pin,1);
    rs485_connect(RS485_Device);
//设置回调函数
    if (RT_NULL == RS485_Device)
    {
       return;
    }
    rt_device_set_rx_indicate(RS485_Device->serial, uart1_haveData);
    rt_device_set_tx_complete(RS485_Device->serial, uart1_SendOver);

    RS485_Device->serial->user_data = ptComm;

    InitMdbsRtu();
    return;
}

Static BOOLEAN isValid1363Byte(T_CommStruct *ptComm, BYTE ucRevByte)
{
    if (SOI == ucRevByte)
    {
        ptComm->bRecReady = True;
        ptComm->wRecIndex = 1;
        ptComm->ucPrtclType = PROTOCOL_1363;
        return True;
    }

    if (EOI == ucRevByte)
    {
        ptComm->bRecOk = True; 
        ptComm->wRecLength = ptComm->wRecIndex;
        return True;
    }

    if ( (ucRevByte >= '0' && ucRevByte <= '9') || (ucRevByte >= 'A' && ucRevByte <= 'F') )
    {
        return True;
    }

    return False;
}



Static void deal_phydata_recive(T_CommStruct* ptComm, BYTE rec_bytes, BYTE * pucRcvTime) {
    if (!ptComm->bRecOk)
    {
        ptComm->ucPortType = COMM_RS485;
        ptComm->aucRecBuf[ptComm->wRecIndex] = rec_bytes;
        ptComm->wRecIndex++;
        #if defined(ZXESM_R321_APTOWER_VERSION) || defined(PAKISTAN_CMPAK_PROTOCOL)
        if(ptComm->wRecIndex == 1)
        {
            ptComm->ucPrtclType = (rec_bytes == 0x7E) ? PROTOCOL_1363 : PROTOCOL_MODBUS;
        }
        #endif
        if (PROTOCOL_MODBUS == ptComm->ucPrtclType)
        {
            *pucRcvTime = 3;
        }
        else if ((!isValid1363Byte(ptComm, rec_bytes)) && (!GetRemoteCommFlag()))
        {
            ptComm->ucPrtclType = PROTOCOL_DL;
        }
        if (ptComm->bRecOk)
        {
            UartComm( ptComm );
        }
        if (ptComm->wRecIndex >= LEN_COMM_REC_BUF)
        {
            ptComm->wRecIndex  = 0;
            ptComm->wRecLength = 0;
            ptComm->bRecReady  = False;
            ptComm->bRecOk = False;
        }
    }
}

Static void takeSemNotOk(T_CommStruct *ptComm)
{
    if (ptComm->wRecIndex > 0)
    {
        if (PROTOCOL_MODBUS == ptComm->ucPrtclType && ptComm->wRecIndex >= MIN_MDBSREC_BYTE_CNT)
        {
            ptComm->wRecLength = ptComm->wRecIndex;
            ptComm->bRecOk = True;
            s_bCommDisconnect = False;  //通信变为连接状态
            SetNthCommEverConnectedTrue(); // 北向通信连接成功(有过正常通信)
            MdbsRtuCommMain(ptComm);
        }
        if (ptComm->wTimeOut > 5)
        {
            ClearRecBuf(ptComm);
        }
    }
    else if (ptComm->wSendLength > 0)
    {
        if (ptComm->wTimeOut > 10)
        {
            initUart1(ptComm, PROTOCOL_MODBUS);
        }
    }
    else
    {
        checkNorthComm(ptComm);
    }
}

void DealComm(void* parameter)
{
    BYTE ucRcvByte;
    BYTE ucRcvTime = 100;
    T_CommStruct tCommUart1;
    
    BYTE ucWaitTimeCount = 0;

    rt_sem_init(&uart1_rx_sem, "rx_sem1", 0, RT_IPC_FLAG_FIFO);

    initUart1( &tCommUart1, PROTOCOL_MODBUS);
    InitProtocolData();

    while(!GetFlagBattery() && (ucWaitTimeCount< 50))//保证电池管理比1363北向通讯早，最多等5秒
    {
        rt_thread_delay(100);
        ucWaitTimeCount++;
    }
    pre_thread_beat_f(THREAD_USART_COMM);
#  ifdef UNITEST
    while (IsRunning())
#  else
    while (1)
#  endif
    {
        thread_beat_go_on(THREAD_USART_COMM);
        SetNthCommEverConnectedFalse();

        if ( RT_EOK == rt_sem_take(&uart1_rx_sem, ucRcvTime) )
        {
            /**************协议切换*******************/
            if (tCommUart1.ucLineErr)
            {
                rt_thread_delay(80);
                tCommUart1.ucPrtclType = (tCommUart1.ucPrtclType != PROTOCOL_MODBUS) ? PROTOCOL_MODBUS : PROTOCOL_1363;
                initUart1(&tCommUart1, tCommUart1.ucPrtclType);
                continue;
            }           
            while ( 1 == rs485_recv(RS485_Device,&ucRcvByte,1) )
            {
                tCommUart1.wTimeOut = 0;
                deal_phydata_recive(&tCommUart1, ucRcvByte, &ucRcvTime);
            }
        }
        else
        {
            takeSemNotOk(&tCommUart1);
            tCommUart1.wTimeOut++;
            ucRcvTime = 100;
        }
    }
}

Static void UartComm(T_CommStruct *ptComm)
{
    if (ptComm->ucPrtclType == PROTOCOL_DL)
    {
        DealCommDLData(ptComm);
    }
    else
    {
        Deal1363CommData(ptComm);
    }
    ptComm->wTimeOut = 0;
    s_bCommDisconnect = False;  //通信变为连接状态
    SetNthCommEverConnectedTrue(); // 北向通信连接成功(有过正常通信)
    return;
}

Static void checkNorthComm(T_CommStruct *ptComm)
{
    BYTE ucTmp = 0;

    if ( ptComm->wTimeOut >= COMM_NORTH_LOST_COUNTER / 2)	//连续30s未收到正确的协议命令，则波特率恢复到9600bps
    {
        s_ucHandShakeCounter = 0;
        if(s_wBaudMode != BAUD_RATE_9600){
            s_wBaudMode = BAUD_RATE_9600;
            initUart1( ptComm, PROTOCOL_MODBUS );
        }
    }

    if (ptComm->wTimeOut >= COMM_NORTH_LOST_COUNTER)
    {
        s_bCommDisconnect = True;
    }

    if (GetApptestFlag())
    {
        ucTmp = 2;
    }

    if ( ptComm->wTimeOut >= COMM_LOSE_COUNTER*(1+ucTmp))
    {
//        SetValueFromProtocol(NPROTOCOL_TYPE_RS485_1363, INFO_TYPE_ALL, 0);
//        SetValueFromProtocol(NPROTOCOL_TYPE_RS485_MODBUS, INFO_TYPE_ALL, 0);
        initUart1( ptComm, PROTOCOL_MODBUS );
    }
		
    return;
}

void ClearRecBuf(T_CommStruct *ptComm)
{
    RT_ASSERT(ptComm != RT_NULL);

    ptComm->wRecIndex  = 0;
    ptComm->wRecLength = 0;
    ptComm->bRecReady  = False;
    ptComm->bRecOk     = False;
    ptComm->wTimeOut = 0;
    rt_memset(ptComm->aucRecBuf, 0, LEN_COMM_REC_BUF);
}

/***************************************************************************
 * @brief    通信断判断
 * @return   True-断连 False-正常
 **************************************************************************/
BOOLEAN JudgeCommDisconnect(void)
{
    return s_bCommDisconnect;
}

/***************************************************************************
 * @brief    完成布防后再有北向通信则置标志
 **************************************************************************/
BOOLEAN SetNthCommEverConnectedTrue(void)
{
    if (GetDefenceStatus() == True)
    {
        s_bIsNthCommEverConnected = True;
    }
    return s_bIsNthCommEverConnected;
}

/***************************************************************************
 * @brief    布防关闭时需要清掉标志
 **************************************************************************/
BOOLEAN SetNthCommEverConnectedFalse(void)
{
    if (GetDefenceStatus() == False)
    {
        s_bIsNthCommEverConnected = False;
    }
    return s_bIsNthCommEverConnected;
}

/***************************************************************************
 * @brief    完成布防后，是否曾经成功进行北向通信
 * @return   True-是 False-否
 **************************************************************************/
BOOLEAN IsNthCommEverConnected(void)
{
    return s_bIsNthCommEverConnected;
}

void sendCommData(T_CommStruct *ptComm)
{
    RT_ASSERT(ptComm != RT_NULL);
    if (RS485_Device == RT_NULL || ptComm->wSendLength == 0)
    {
        return;
    }

    rs485_send(RS485_Device,ptComm->aucSendBuf,ptComm->wSendLength);
	
    if(s_ucHandShakeCounter >= 3){
        initUart1(ptComm, PROTOCOL_MODBUS);
        s_ucHandShakeCounter = 0;
    }
}

UINT32 getBaudMode(){
    return s_wBaudMode;
}

void setBaudMode(UINT32 baudMode){
    s_wBaudMode = baudMode;
    return;
}

BYTE getHandShakeCounter(){
    return s_ucHandShakeCounter;
}

void setHandShakeCounter(BYTE handShakeCounter){
    s_ucHandShakeCounter = handShakeCounter;
    return;
}

void handShakeCounterIncrement(){
    s_ucHandShakeCounter++;
    return;
}
