/**
 * @brief
 */

#ifndef _IO_CONTROL_H_
#define _IO_CONTROL_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "drv_common.h"
#include "softbus.h"


#define PIN_LCD_BUTTON1 GET_PIN(C, 8) //KEY_ESC//vt28//s1
#define PIN_LCD_BUTTON2 GET_PIN(C, 9)  //KEY_LEFT//vt29//s2
#define PIN_LCD_BUTTON3 GET_PIN(A, 11) //KEY_UP//vt30//s3
#define PIN_LCD_BUTTON4 GET_PIN(A, 10) //KEY_DOWN//vt33//s4
#define PIN_LCD_BUTTON5 GET_PIN(A, 12)  //KEY_ENTER//vt31//s5
#define PIN_LCD_BUTTON6 GET_PIN(C, 10) //KEY_RIGHT//vt32//s6

#define PIN_LCD_BUTTON7 GET_PIN(A, 9)  //KEY_RESET
#define BUZZ_PWM_DEVICE    "pwm8"   // 蜂鸣器PWM设备名
#define BUZZ_PWM_CHANNEL   1        // 通道1
#define BUZZ_PWM_FREQ     200000   // 5KHz, 具体频率可根据蜂鸣器特性调整
#define BUZZ_PWM_DUTY     100000     // 50% 占空比
#define BUZZMODE1_ONOFF_MS 100        //实际250ms
#define BUZZMODE2_ONOFF_MS 200       //实际500ms


#define OUTRELAY1_PIN          GET_PIN(C, 15)  //输出干接点1
#define OUTRELAY2_PIN          GET_PIN(E, 3)   //输出干接点2
#define OUTRELAY3_PIN          GET_PIN(D, 11)  //输出干接点3
#define OUTRELAY4_PIN          GET_PIN(E, 5)   //输出干接点4
#define OUTRELAY5_PIN          GET_PIN(C, 14)  //输出干接点5
#define OUTRELAY6_PIN          GET_PIN(G, 10)  //输出干接点6
#define OUTRELAY7_PIN          GET_PIN(G, 11)  //输出干接点7
#define OUTRELAY8_PIN          GET_PIN(C, 7)   //输出干接点8

#define OUT_RELAY_MAX_NUM  8

#define PRESS_DOWN  1
#define PRESS_UP    0

#define KEY_PROCESS_THREAD_PERIOD_MS 3       //3ms
#define IO_CONTROL_THREAD_PERIOD_MS 5       //5ms
#define TRUE_THREAD_PERIOD_MS   (3*IO_CONTROL_THREAD_PERIOD_MS)  //12
#define LCD_LED_TIMEOUT         (30000/TRUE_THREAD_PERIOD_MS)    // 液晶背光超时时间30s
#define SAVE_SCREEN_TIMEOUT     (10*60*1000 / TRUE_THREAD_PERIOD_MS)    // 屏保界面超时时间10min
#define SILENCE_TIME_MS (30*60*1000)   // 30分钟，单位ms
#define SILENCE_TIME_TICKS (SILENCE_TIME_MS / TRUE_THREAD_PERIOD_MS)
#define CHA_LED_TIMEOUT (60*1000)   // 机架告警灯闪烁切常亮计时，1分钟，单位ms
#define CLK_TRACE_INTERVAL  (1000)   //1s

/* LED模式定义 */
#define LEDON       0
#define LEDOFF      1
#define LEDSLOWSH   2   // 1Hz闪烁 (500ms/500ms)
#define LEDQUICKSH  3   // 2Hz闪烁 (250ms/250ms)
#define LEDINIT     100

enum
{
    BUZZ_OFF = 0,
    BUZZ_MODE1,
    BUZZ_MODE2,
};

typedef struct {
    rt_uint32_t msg_id;
    short (*handle)(_rt_msg_t curr_msg);
}control_msg_handle_process_t;

typedef enum {
    RUNNINT_LED = 0,
    ALARM_LED,
    RACK_ALARM_LED,
    COMM_LED,
    LED_TYPE_NUM
} e_led_type;

/* 函数声明 */
void* init_led_blink();
short led_thread_entry(void* parameter);
short led_set_status(rt_uint8_t led_type, rt_uint8_t mode);
short led_set_alarm_by_level(rt_uint8_t alarm_level);
short led_set_cha_alarm_by_level(rt_uint8_t alarm_level);
short led_set_com_by_status(rt_uint8_t comm_status);
void* init_io_control(void* param);
void io_control_main(void* param);
void* init_key_process(void* param);
void key_process_main(void *parameter);
int lcd_init();
signed char control_out_relay(unsigned char index, unsigned char status);
int alarm_trig_relay(_rt_msg_t curr_msg);
int beep_control(unsigned char on);
unsigned char get_button_press_test_status(unsigned char index);
unsigned char clear_button_press_count(void);
#ifdef __cplusplus
}
#endif      //< __cplusplus

#endif      //< _IO_CONTROL_H_
