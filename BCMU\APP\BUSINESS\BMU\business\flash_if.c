#include <rtthread.h>
#include "flash.h"


uint16_t CheckFlashValue(uint32_t FlashAddress, uint32_t* Data, uint32_t i)
{
    return *(uint32_t*)FlashAddress != *(uint32_t*)(Data + i);
}

flash_error_status FLASH_If_Write(uint32_t FlashAddress, uint32_t* Data, uint32_t DataLength)
{
    uint32_t i;

    fmc_unlock();
    for (i = 0; (i < DataLength / 4) && (FlashAddress <= (USER_FLASH_END_ADDRESS - 4)); i++)
    {
        if (0 == (FlashAddress & 0x7FF))
        {
            fmc_flag_clear(FMC_FLAG_BANK0_END | FMC_FLAG_BANK0_PGERR | FMC_FLAG_BANK0_WPERR);
            fmc_page_erase(FlashAddress);
        }
        if (fmc_word_program(FlashAddress, *(uint32_t*)(Data + i)) == FMC_READY)
        {
            if (CheckFlashValue(FlashAddress, Data, i))
            {
                return FLASHIF_WRITE_ERROR;
            }
            FlashAddress += 4;
        }
        else
        {
            return FLASHIF_WRITE_ERROR;
        }
    }
    fmc_lock();

    return FLASHIF_OK;
}

flash_error_status FLASH_If_Read(uint32_t offset, uint8_t *buf, uint32_t size)
{
    size_t i;
    uint32_t addr = offset;
    if (addr + size >= GD32F4_FLASH_END_ADDR)
    {
        // printf("read outrange flash size!, addr is (0x%p)\r\n", (void *)(addr + size));
        return FLASHIF_READ_ERROR;
    }

    for (i = 0; i < size; i++, addr++, buf++)
    {
        *buf = *(__IO uint8_t *)addr;
    }

    return FLASHIF_OK;
}

