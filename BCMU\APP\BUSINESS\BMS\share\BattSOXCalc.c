#include "BattSOXCalc.h"
#include "const_define.h"
/*初始化SOC/SOH*/

/****************************************************************************
* 函数名称：OCVtoSOC(FLOAT fOCV)
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：开路电压与电池SOC的拟合曲线
* 作    者：xzx
* 设计日期：2018-11-14
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
FLOAT OCVtoSOC(FLOAT fOCV)
{ // 25℃时标准OCV-SOC曲线(0-8%校准值)
    FLOAT fSOC = 0.0;
    fOCV = 0.65 * fOCV + 1.12; // 将其他温度下OCV拟合转化为25摄氏度OCV减少温度影响
    //此处，不同电池则使用不同拟合曲线。
    fSOC = 132.98*fOCV*fOCV*fOCV -1147.5*fOCV*fOCV + 3307.1*fOCV - 3182.4; //光宇拟合曲线
//  fSOC = 46.492*fOCV*fOCV*fOCV -377.41*fOCV*fOCV + 1024.1*fOCV - 927.9;  //统一拟合曲线
    return GetValidData(fSOC, 8.0f, 0.0f);
}

//SOC:相关参数 1：容量  2：SOH 3:精度
WORD CalcSOCFromCap(T_BattDealInfoStruct *pBattDeal, WORD wBattRateCap, BYTE ucPerc)
{
    WORD wSoc = 0;
    if(pBattDeal== NULL || wBattRateCap == 0)
    {
        return 0;
    }
    wSoc = (WORD)(pBattDeal->fBattCap / (pBattDeal->fBattSOH * wBattRateCap * g_ProConfig.fCapTransRate / 100 ) *100*Pow10(ucPerc) + 0.5);
    return MIN(wSoc, 100*Pow10(ucPerc));
}
/*********************
      SOH:*
**********************/
void AccumulateDischargeCap(T_BattInfo *pBattIn, FLOAT *pfCap, ULONG ulTimeDiff)//安时积分
{
    BYTE ucBattCycleTimesCal = pBattIn->tData.ucBattCycleTimesCal;

    if (pBattIn->tData.fBattCurr <= -1.0f * pBattIn->tData.fCurrMinDet)
    {////取正数
        *pfCap -= pBattIn->tData.fBattCurr * ulTimeDiff / 3600.0;
    }
    //soc校正同步计算循环次数
    switch (ucBattCycleTimesCal)
    {
        case SMALL_CURR_COMPENSATION:
            *pfCap += SOC_CAL_DISCHG_CURR * ulTimeDiff / 3600.0;
            pBattIn->tData.ucBattCycleTimesCal = DEFUALT_COMPENSATION;
            break;
        case CAL_SELF_DISCHAGE_DECREASE_CAP_WITH_PERIOD:
            *pfCap += SELF_DISCHARGE_RATE_35C * pBattIn->tPara.wBattCap * BATT_SOC_MAX / 100.0;
            pBattIn -> tData.ucBattCycleTimesCal = DEFUALT_COMPENSATION;
            break;
        case CAL_SELF_DISCHAGE_DECREASE_CAP:
            *pfCap += SMART_LI_SELF_DISCHARGE_CURRENT * 1 / 60.0;
            pBattIn -> tData.ucBattCycleTimesCal = DEFUALT_COMPENSATION;
            break;
        default:
            break;
    }
}
//1、循环衰减
/****************************************************************************
* 函数名称：CalCycleLifeDecline(void)
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：计算电池循环寿命衰减百分比
* 作    者：xzx
* 设计日期：2018-11-14
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
Static void PlusCycleLifeDecline(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    WORD wCellCycleTimes = 0;
    FLOAT ftimes  = 0.0;
    FLOAT fTemp = pBattIn->tData.fCellAverageTemp;
    /*
       * *原始数据:25°C:4200        35°C:3500        45°C:1750
       * *二次多项式拟合曲线,更加合适.常温时最佳,高低温循环次数性能降低
       * *y = -5.25x*x + 245*x + 1356.3 零点:x1 = -5, x2 = 51.6 , Xbest=23.333, Ymax=4215
     */
    ftimes = -5.25*fTemp*fTemp + 245*fTemp + 1356.3;
    ftimes = GetValidData(ftimes, 4500.0, 1500.0);

    wCellCycleTimes = GetCellCycleTimes();
    if(wCellCycleTimes != (WORD)0)
    {
        ftimes = ftimes*wCellCycleTimes/((FLOAT)CYCLE_TIMES_MAX);         //等比例扩大
    }

    pBattDeal->fCycleLifeDecline = MAX(0.0, pBattDeal->fCycleLifeDecline);
    if(ftimes >= 1500.0 && ftimes <= 8000.0)
    {
        pBattDeal->fCycleLifeDecline += (FLOAT)(10.0 / ftimes);
    }
    return;
}

//单次循环计算循环次数
static void CalcCycleTimes(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    pBattDeal->fTotalDischargeCap = GetValidData(pBattDeal->fTotalDischargeCap, (FLOAT)pBattIn->tPara.wBattCap * g_ProConfig.fCapTransRate, 0.0);

    AccumulateDischargeCap(pBattIn, &pBattDeal->fTotalDischargeCap, pBattDeal->ulMinuteTimeDiffSec);

    if(pBattDeal->fTotalDischargeCap > pBattIn->tPara.wBattCap*g_ProConfig.fCapTransRate*0.8f*pBattDeal->fBattSOH/100)
    {
        pBattDeal->fTotalDischargeCap -= pBattIn->tPara.wBattCap*g_ProConfig.fCapTransRate*0.8f*pBattDeal->fBattSOH/100;
        pBattDeal->wBattCycleTimes++;
        PlusCycleLifeDecline(pBattIn , pBattDeal);
        pBattDeal->bSave2eeprom = True;
    }
}

//2、日历衰减
void PlusCalenderLifeDecline(T_BattDealInfoStruct *pBattDeal,FLOAT fCellAverageTemp)
{
    fCellAverageTemp = GetValidData(fCellAverageTemp , 45.0, 10.0);
    pBattDeal->fCalenderLifeDecline += 10.0 / (27.5-0.5 * fCellAverageTemp) / 365; //每次日历衰减值
    GetTime(&pBattDeal->tLastCalcCalenderTime);
    pBattDeal->bSave2eeprom    = True;
}

/****************************************************************************
* 函数名称：CalCalenderLifeDecline(void)
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：计算电池日历寿命衰减百分比
* 作    者：xzx
* 设计日期：2018-11-14
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
static void CalCalenderLifeDecline(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    TimerPlus(pBattDeal->slDeclineTimer, ONE_DAY_MINUTES);

    if (pBattDeal->slDeclineTimer >= ONE_DAY_MINUTES)
    {
        PlusCalenderLifeDecline(pBattDeal,pBattIn->tData.fCellAverageTemp);
        pBattDeal->slDeclineTimer  = (int)0;
    }
    return;
}

//3、附加衰减,校准时发生
/****************************************************************************
* 函数名称：CalAdjustSOH(void)
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：计算电池寿命校准值
* 作    者：xzx
* 设计日期：2018-11-14
* 修改记录：
* 日    期      版  本      修改人      修改摘要
*****************************************************************************/
Static void CalAdjustSOH(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    /*
    满足OCV下端修正条件，才开始SOH修正
    */
    FLOAT fBattRateCap = (FLOAT)pBattIn->tPara.wBattCap*g_ProConfig.fCapTransRate;
    FLOAT fBattDischargeAllCap = 0.0;
    FLOAT fAddedLifeDeclineBak = pBattDeal->fAddedLifeDecline;
    FLOAT fSoc = 0.0;
    FLOAT fMin = 0.0;

    if (pBattIn->tData.fCellOCV > AMEND_OCV_MAX || pBattIn->tData.fCellOCV < AMEND_OCV_MIN
        || !pBattDeal->bDischargeFromFull || pBattDeal->fDischargeCapForCelebrate <
        0.8f*fBattRateCap || pBattIn->tData.fCellAverageTemp <= (FLOAT)0 )
    {
        return;
    }

    fSoc = GetValidData(OCVtoSOC(pBattIn->tData.fCellOCV), 5.0, 0.0);
    fBattDischargeAllCap = pBattDeal->fDischargeCapForCelebrate/(100.0 - fSoc)*100;

    if(pBattIn->tData.fCellAverageTemp >= 15 && fBattRateCap >= BATT_CAP_MIN)
    {  // 充满到放电跳变时，SOH校正并且放电时温度不低于15度
        pBattDeal->fAddedLifeDecline   = (fBattDischargeAllCap / fBattRateCap *100) - (100.0 - pBattDeal->fCalenderLifeDecline - pBattDeal->fCycleLifeDecline);
        fMin = MIN(5.0, pBattDeal->fCalenderLifeDecline);
        pBattDeal->fAddedLifeDecline   = GetValidData(pBattDeal->fAddedLifeDecline , 0.0f, -1.0f*fMin);
    }

    if (fabs(fAddedLifeDeclineBak - pBattDeal->fAddedLifeDecline) >= 1)
    {
        pBattDeal->bSave2eeprom        = True;
    }

    return;
}

//4、SOH计算方法 ： 包含1、日历衰减 2、循环衰减 3、附加衰减
 FLOAT CalcSOHFromDecline(T_BattDealInfoStruct *pBattDeal)
{
    FLOAT fBattSOH    = (100.0 - pBattDeal->fCalenderLifeDecline //日历衰减
                               - pBattDeal->fCycleLifeDecline    //循环衰减
                               + pBattDeal->fAddedLifeDecline);  //附加衰减

    return GetValidData(fBattSOH, 100.0, 45.0);//SOH的范围在45到100之间为有效值
}
//5、计算SOH
/****************************************************************************
* 函数名称：CalBattSOH( void )
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：计算电池SOH百分比
* 作    者：xzx
* 设计日期：2018-11-14
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
static void CalBattSOH( T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal )
{
    WORD wBattSOHBak = pBattIn->tData.wBattSOH;

    pBattDeal->fBattSOH = CalcSOHFromDecline(pBattDeal);

    if (wBattSOHBak != Float2Word(pBattDeal->fBattSOH + 0.501))
    {
        pBattDeal->bSave2eeprom = True;
    }

    return;
}
static void LoadSOCAndSOH(T_BattDealInfoStruct *pBattDeal)
{
    const BYTE ucTimeDiffMax = 130;
    const BYTE ucTimeDiffMin = 30;
    time_t tTimeNow = GetTimeStamp();

    pBattDeal->ulMinuteTimeDiffSec = tTimeNow - pBattDeal->tMinuteCalcTime;
    pBattDeal->tMinuteCalcTime = tTimeNow;
    if (pBattDeal->ulMinuteTimeDiffSec > ucTimeDiffMax ||pBattDeal->ulMinuteTimeDiffSec < ucTimeDiffMin)
    {
        pBattDeal->ulMinuteTimeDiffSec = 60;
    }

    return;
}
//SOH：相关参数：1、衰减
void CalcBattSOH(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    RETURN_IF_FAIL(pBattIn != NULL);
    RETURN_IF_FAIL(pBattDeal != NULL);
    //载入循环时间:主要用于安时积分
    LoadSOCAndSOH(pBattDeal);
    //1、循环衰减
    CalcCycleTimes(pBattIn, pBattDeal);
    //2、日历衰减
    CalCalenderLifeDecline(pBattIn, pBattDeal);
    //3、附加衰减,校准时发生
    CalAdjustSOH(pBattIn, pBattDeal);
    //计算SOH
    CalBattSOH(pBattIn , pBattDeal);
}
