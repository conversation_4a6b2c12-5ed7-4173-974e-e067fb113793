#include <rtthread.h>
#include <rtdevice.h>
#include <board.h>
#include <drv_common.h>
#include <math.h>
#include <string.h>
#include "sample.h"
#include "dac.h"
#include "data_type.h"
#include "utils_thread.h"
#include "adc_ctrl.h"
#include "msg_id.h"
#include "utils_server.h"
#include "pin_ctrl.h"

#define INVALID_LEVEL_SIGNAL -1


/***********************  变量定义  ************************/
Static unsigned int   s_adc_data[ANALOG_COUNT][ADCCONVERTEDVALUES_SAMPLING_NUM];
Static unsigned int   s_digital_data[DIGITAL_COUNT];
Static unsigned int   s_analog_data[ANALOG_COUNT];


/*********************  静态函数原型定义  **********************/
Static void sample(void);
Static void analog_sample(void);
Static void digital_sample(void);
Static unsigned int MedianFilter(unsigned int data[ADCCONVERTEDVALUES_SAMPLING_NUM],int size);
Static void hal_board_gpio_init(void);
Static int adc_get_data_by_channel(ADC_SAMPLE_CHANNEL channel);
Static int di_get_data_by_channel(eDI_SAMPLE channel);
Static void set_selection_sample_gpio(ADC_SAMPLE_CHANNEL channel);

Static DI_CHANNEL_INFO_STRUCT s_di_sample_info[DI_END] =
{
	 {DI1_IN,  PIN_DI1_IN },
	 {DI2_IN,  PIN_DI2_IN },
	 {DI3_IN,  PIN_DI3_IN },
	 {DI4_IN,  PIN_DI4_IN },
	 {DI5_IN,  PIN_DI5_IN },
	 {DI6_IN,  PIN_DI6_IN },
	 {DI7_IN,  PIN_DI7_IN },
	 {DI8_IN,  PIN_DI8_IN },
	 {DI9_IN,  PIN_DI9_IN },
	 {DI10_IN, PIN_DI10_IN},
	 {DI11_IN, PIN_DI11_IN},
	 {DI12_IN, PIN_DI12_IN},
};

Static ADC_CHANNEL_INFO_STRUCT s_sample_info[AI_END] =
{
	 {AI_0_0, 0, ADC_CHANNEL_8, "adc0"},
	 {AI_0_1, 1, ADC_CHANNEL_8, "adc0"},
	 {AI_0_2, 2, ADC_CHANNEL_8, "adc0"},
	 {AI_0_3, 3, ADC_CHANNEL_8, "adc0"},
	 {AI_0_4, 4, ADC_CHANNEL_8, "adc0"},
	 {AI_0_5, 5, ADC_CHANNEL_8, "adc0"},
	 {AI_0_6, 6, ADC_CHANNEL_8, "adc0"},
	 {AI_0_7, 7, ADC_CHANNEL_8, "adc0"},
	 {AI_1_0, 0, ADC_CHANNEL_8, "adc2"},
	 {AI_1_1, 1, ADC_CHANNEL_8, "adc2"},
	 {AI_1_2, 2, ADC_CHANNEL_8, "adc2"},
	 {AI_1_3, 3, ADC_CHANNEL_8, "adc2"},
	 {AI_1_4, 4, ADC_CHANNEL_8, "adc2"},
	 {AI_1_5, 5, ADC_CHANNEL_8, "adc2"},
	 {AI_1_6, 6, ADC_CHANNEL_8, "adc2"},
	 {AI_1_7, 7, ADC_CHANNEL_8, "adc2"},
	 {AI_2_0, 0, ADC_CHANNEL_14, "adc2"},
	 {AI_2_1, 1, ADC_CHANNEL_14, "adc2"},
	 {AI_2_2, 2, ADC_CHANNEL_14, "adc2"},
	 {AI_2_3, 3, ADC_CHANNEL_14, "adc2"},
	 {AI_2_4, 4, ADC_CHANNEL_14, "adc2"},
	 {AI_2_5, 5, ADC_CHANNEL_14, "adc2"},
	 {AI_2_6, 6, ADC_CHANNEL_14, "adc2"},
	 {AI_2_7, 7, ADC_CHANNEL_14, "adc2"},
	 {AI_3_0, 0, ADC_CHANNEL_10, "adc0"},
	 {AI_3_1, 1, ADC_CHANNEL_10, "adc0"},
	 {AI_3_2, 2, ADC_CHANNEL_10, "adc0"},
	 {AI_3_3, 3, ADC_CHANNEL_10, "adc0"},
	 {AI_3_4, 4, ADC_CHANNEL_10, "adc0"},
	 {AI_3_5, 5, ADC_CHANNEL_10, "adc0"},
	 {AI_3_6, 6, ADC_CHANNEL_10, "adc0"},
	 {AI_3_7, 7, ADC_CHANNEL_10, "adc0"},
};

Static DO_CHANNEL_INFO_STRUCT s_do_out_info[DI_END] =
{
    {DO1_OUT, PIN_DO1_OUT},
    {DO2_OUT, PIN_DO2_OUT},
    {DO3_OUT, PIN_DO3_OUT},
    {DO4_OUT, PIN_DO4_OUT},
    {DO5_OUT, PIN_DO5_OUT},
    {DO6_OUT, PIN_DO6_OUT},
    {DO7_OUT, PIN_DO7_OUT},
    {DO8_OUT, PIN_DO8_OUT},
};

Static DAC_CHANNEL_INFO_STRUCT s_dac_info[DAC_END]=
{
    {DAC_1, 0, "dac0", 0},
    {DAC_2, 0, "dac1", 0},
};


Static msg_map sample_msg_map[] =
{
    {DEVICE_SAMPLE_MSG, (void*)msg_handle_nothing},//临时添加解决编译问题
};

void* sample_init_sys(void *param)
{
    RETURN_VAL_IF_FAIL(param != NULL, NULL);
    server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(sample_msg_map) / sizeof(msg_map);
    register_server_msg_map(sample_msg_map, server_info);
    hal_board_gpio_init();//初始化IO模式
    return NULL;
}

void sample_main(void* parameter)
{
    while (is_running(TRUE))
    {
        sample();
        rt_thread_mdelay(400);
    }

}

/* 采样数据 */
Static void sample(void)
{
    digital_sample();
    analog_sample();
}

Static void digital_sample(void)
{
    unsigned int i;

    for(i = 0; i < DI_END; i++)
    {
        s_digital_data[i] = di_get_data_by_channel(i);
    }
    return;
}

Static void analog_sample(void)
{
    unsigned int i,j;

    for(i = 0; i < AI_END; i++)
    {
        for(j = 0; j < ADCCONVERTEDVALUES_SAMPLING_NUM; j++)
        {
            s_adc_data[i][j] = adc_get_data_by_channel(i);
        }
        s_analog_data[i] = MedianFilter(s_adc_data[i],ADCCONVERTEDVALUES_SAMPLING_NUM);

    }

    return;
}

//功能引脚初始化
Static void hal_board_gpio_init(void)
{
    unsigned short i;
    rt_pin_mode(PIN_A0_S1, PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_A1_S1, PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_A2_S1, PIN_MODE_OUTPUT);

    for (i = 0; i < DI_END; i++)
    {
        rt_pin_mode(s_di_sample_info[i].di_pin, PIN_MODE_INPUT);
    }

    for (i = 0; i < DO_END; i++)
    {
        rt_pin_mode(s_do_out_info[i].do_pin, PIN_MODE_OUTPUT);
    }
}

// 采样通道选择
Static void set_selection_sample_gpio(ADC_SAMPLE_CHANNEL channel)
{
    if((s_sample_info[channel].select_status & 0x01) == 1)
        rt_pin_write(PIN_A0_S1, 1);
    else
        rt_pin_write(PIN_A0_S1, 0);

    if((s_sample_info[channel].select_status>>1 & 0x01) == 1)
        rt_pin_write(PIN_A1_S1, 1);
    else
        rt_pin_write(PIN_A1_S1, 0);

    if((s_sample_info[channel].select_status>>2 & 0x01) == 1)
        rt_pin_write(PIN_A2_S1, 1);
    else
        rt_pin_write(PIN_A2_S1, 0);

     return ;
}
//从通道获取AI数据
Static int adc_get_data_by_channel(ADC_SAMPLE_CHANNEL channel)
{
    rt_uint32_t value;
    rt_adc_device_t adc_dev;

    adc_dev = (rt_adc_device_t)rt_device_find(s_sample_info[channel].adc_device);
    if (RT_NULL == adc_dev)
    {
        rt_kprintf("adc sample run failed! can't find %s device!\n", s_sample_info[channel].adc_device);
        return RT_ERROR;
    }
    set_selection_sample_gpio(channel);
    rt_thread_mdelay(20);
    rt_adc_enable(adc_dev, s_sample_info[channel].adc_channel);
    value = rt_adc_read(adc_dev, s_sample_info[channel].adc_channel);
    rt_adc_disable(adc_dev, s_sample_info[channel].adc_channel);
    return value;
}
//从通道获取DI数据
Static int di_get_data_by_channel(eDI_SAMPLE channel)
{
    rt_uint32_t value;

    value = rt_pin_read(s_di_sample_info[channel].di_pin);

    return value;
}

// 获取数字量数据（外部接口）
unsigned short get_digital_data(int *data, unsigned short data_size)
{
    unsigned short real_size = 0;

    if(data_size < sizeof(s_digital_data))
    {
        real_size = data_size;
    }
    else
    {
        real_size = sizeof(s_digital_data);
    }
    rt_memcpy_s(data, real_size, s_digital_data, real_size);

    return real_size/sizeof(unsigned int);
}

// 获取模拟量数据（外部接口）
unsigned short get_analog_data(unsigned short *data, unsigned short data_size)
{
    unsigned short i, real_size = 0;
    unsigned short analog_data[ANALOG_COUNT] = {0};

    for(i = 0; i < AI_END; i++)
    {
        analog_data[i] = s_analog_data[i] * REFER_VOLTAGE * 1000 / 4095;
    }

    if(data_size < sizeof(analog_data))
    {
        real_size = data_size;
    }
    else
    {
        real_size = sizeof(analog_data);
    }

    rt_memcpy_s(data, real_size, analog_data, real_size);

    return real_size/sizeof(unsigned short);
}

Static unsigned int MedianFilter(unsigned int data[ADCCONVERTEDVALUES_SAMPLING_NUM], int size)
{
    unsigned int max,min,sum;
    if(size>2)
    {
        max = data[0];
        min = max;
        sum = 0;
        for(int i=0;i<size;i++)
        {
            sum += data[i];
            if(data[i]>max)
            {
                max = data[i];
            }

            if(data[i]<min)
            {
                min = data[i];
            }
        }

        sum = sum-max-min;
        return sum/(size-2);
    }

    return 0;
}


//设置Do状态外部接口

void setDoValue(eDO_OUT do_index, int value)
{
    RETURN_IF_FAIL(do_index >= DO1_OUT && do_index < DO_END);

    if (FALSE == value)
        rt_pin_write(s_do_out_info[do_index].do_pin, PIN_LOW);
    else
        rt_pin_write(s_do_out_info[do_index].do_pin, PIN_HIGH);

    return;
}

//获取Do状态外部接口
int getDoValue(eDO_OUT do_index)
{
    RETURN_VAL_IF_FAIL(do_index >= DO1_OUT && do_index < DO_END, INVALID_LEVEL_SIGNAL);

    return rt_pin_read(s_do_out_info[do_index].do_pin);
}


//DAC---start
Static rt_err_t dac_set_data_by_channel(eDAC_OUT_CHANNEL channel, rt_uint32_t value)
{
    rt_dac_device_t dac_dev;

    dac_dev = (rt_dac_device_t)rt_device_find(s_dac_info[channel].dac_device);
    if (RT_NULL == dac_dev)
    {
        rt_kprintf("dac set run failed! can't find %s device!\n", s_dac_info[channel].dac_device);
        return RT_ERROR;
    }
    rt_dac_enable(dac_dev, s_dac_info[channel].dac_channel);
    return rt_dac_write(dac_dev, s_dac_info[channel].dac_channel, value);
}
//设置DAC外部接口
void setDacValue(eDAC_OUT_CHANNEL dac_index, rt_uint32_t value)
{
    RETURN_IF_FAIL(dac_index >= DAC_1 && dac_index < DAC_END);
    RETURN_IF_FAIL(value < (1<<12));

    if(RT_ERROR != dac_set_data_by_channel(dac_index, value))
    {
        s_dac_info[dac_index].cur_value = value;
    }

    return;
}
//获取DAC外部接口
rt_uint32_t getDacValue(eDAC_OUT_CHANNEL dac_index)
{
    RETURN_VAL_IF_FAIL(dac_index >= DAC_1 && dac_index < DAC_END, 0);

    return s_dac_info[dac_index].cur_value;
}

