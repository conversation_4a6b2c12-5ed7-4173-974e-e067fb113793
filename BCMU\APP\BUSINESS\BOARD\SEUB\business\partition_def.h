#ifndef PARTITION_DEF_H_
#define PARTITION_DEF_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "bspconfig.h"
//修改ee分区时注意恢复出厂默认值功能部分是否会受影响。
// #define UPDATE_DOWNLOAD_PART    NOR_APP_DOWNLOAD
// #define UPDATE_PATCH_PART       NOR_APP_PATCH
// #define UPDATE_INFO_PART        EE_UPDATE_INFO
#define REAL_ALARM_PART         "" // EE_REAL_ALARM
// #define PARALLEL_ALARM_PART     EE_PARALLEL_ALARM
// #define PARALLEL_ALARM_SIZE     EE_PARALLEL_ALARM_SIZE
// #define ENERGY_PART             EE_ENERGY_CYCLE
// #define APP_DOWNLOAD_SIZE       SECOND_NOR_PART1_SIZE  //APP download区大小
// #define ENERGY_PART_SIZE        EEPROM_PART1_SIZE      // 电量分区的大小
// #define FW_AP_PART              NOR_FW_AP              //ap 分区
// #define FW_AP_PART_SIZE         435635                 // wifi固件大小
#define AFCI_FILE_NAME    ""

#define  PARA_FILE         "/para"  //文件系统---参数管理存储文件名称
#define ACT_POWER_DATA     "act_power.bin"     // 日功率曲线
#define ACT_POWER_FILE     "/act_power.bin"   // 日功率曲线文件

#define LOG_FILE1          "sys.log"
#define LOG_FILE1_PART     "/nor/sys.log"
#define LOG_FILE2          "sys_0.log"
#define LOG_FILE2_PART     "/nor/sys_0.log"

//记录存储文件
#define  HIS_DATA_DIR        "/HisData"
#define  HIS_DATA_FILE       "HisData.bin"
#define  HIS_DATA_FILE_0     HIS_DATA_DIR"/HisData.bin_0"
#define  HIS_DATA_FILE_1     HIS_DATA_DIR"/HisData.bin_1"
#define  HIS_DATA_FILE_2     HIS_DATA_DIR"/HisData.bin_2"
#define  HIS_DATA_FILE_3     HIS_DATA_DIR"/HisData.bin_3"
#define  HIS_DATA_FILE_4     HIS_DATA_DIR"/HisData.bin_4"
#define  HIS_DATA_FILE_5     HIS_DATA_DIR"/HisData.bin_5"
#define  HIS_DATA_FILE_6     HIS_DATA_DIR"/HisData.bin_6"

#define  HIS_ALARM_DIR       "/HisAlarm"
#define  HIS_ALARM_FILE      "HisAlarm.bin"
#define  HIS_ALARM_FILE_0    HIS_ALARM_DIR"/HisAlarm.bin_0"
#define  HIS_ALARM_FILE_1    HIS_ALARM_DIR"/HisAlarm.bin_1"
#define  HIS_ALARM_FILE_2    HIS_ALARM_DIR"/HisAlarm.bin_2"
#define  HIS_ALARM_FILE_3    HIS_ALARM_DIR"/HisAlarm.bin_3"
#define  HIS_ALARM_FILE_4    HIS_ALARM_DIR"/HisAlarm.bin_4"
#define  HIS_ALARM_FILE_5    HIS_ALARM_DIR"/HisAlarm.bin_5"
#define  HIS_ALARM_FILE_6    HIS_ALARM_DIR"/HisAlarm.bin_6"

#define  HIS_EVENT_DIR       "/HisEvent"
#define  HIS_EVENT_FILE      "HisEvent.bin"
#define  HIS_EVENT_FILE_0    HIS_EVENT_DIR"/HisEvent.bin_0"
#define  HIS_EVENT_FILE_1    HIS_EVENT_DIR"/HisEvent.bin_1"
#define  HIS_EVENT_FILE_2    HIS_EVENT_DIR"/HisEvent.bin_2"
#define  HIS_EVENT_FILE_3    HIS_EVENT_DIR"/HisEvent.bin_3"
#define  HIS_EVENT_FILE_4    HIS_EVENT_DIR"/HisEvent.bin_4"
#define  HIS_EVENT_FILE_5    HIS_EVENT_DIR"/HisEvent.bin_5"
#define  HIS_EVENT_FILE_6    HIS_EVENT_DIR"/HisEvent.bin_6"

#define  HISDATA_CONFIG_FILE "/HisData_Config.bin"
#define  HISDATA_CONFIG_BIN  "HisData_Config.bin"

#define  HIS_RECORD_INFO_FILE "/His_Record_info"

#define POWER_REC_DIR     "/PowerRec"
// 电量数据
#define YEAR_FILE_NAME    "YearPowerRec.bin"
#define YEAR_FILE         POWER_REC_DIR"/YearPowerRec.bin"
#define YEAR_FILE_TMP     POWER_REC_DIR"/YearPowerRec_tmp.bin"

#define MONTH_FILE_NAME   "MonPowerRec.bin"
#define MONTH_FILE        POWER_REC_DIR"/MonPowerRec.bin"
#define MONTH_FILE_TMP    POWER_REC_DIR"/MonPowerRec_tmp.bin"

#define DAY_FILE_NAME     "DayPowerRec.bin"
#define DAY_FILE          POWER_REC_DIR"/DayPowerRec.bin"
#define DAY_FILE_TMP      POWER_REC_DIR"/DayPowerRec_tmp.bin"

#define HOUR_FILE_NAME    "HourPowerRec.bin"
#define HOUR_FILE         POWER_REC_DIR"/HourPowerRec.bin"
#define HOUR_FILE_TMP     POWER_REC_DIR"/HourPowerRec_tmp.bin"

#define HIS_FILE_NAME     "his_energy.bin"
#define HIS_FILE          POWER_REC_DIR"/his_energy.bin"
#define HIS_BAK_FILE      POWER_REC_DIR"/his_energy_bak.bin"

//参数存储文件
#define  ALMPARA_FILE_NAME "alarm_para.bin"
#define  ALMPARA_PART      "/alarm_para.bin"     //文件系统---参数管理存储文件名称
#define  NUMPARA_FILE_NAME "num_para.bin"
#define  NUMPARA_PART      "/num_para.bin"       //文件系统---参数管理存储文件名称
#define  STRPARA_FILE_NAME "string_para.bin"
#define  STRPARA_PART      "/string_para.bin"    //文件系统---参数管理存储文件名称

//导入参数临时存储文件
#define  ALMPARA_FILE_SAVE_NAME "alarm_para.bin"
#define  ALMPARA_SAVE_PART      "/nor/alarm_para.bin"     //文件系统---参数管理存储文件名称
#define  NUMPARA_FILE_SAVE_NAME "num_para_save.bin"
#define  NUMPARA_SAVE_PART      "/nor/num_para.bin"       //文件系统---参数管理存储文件名称
#define  STRPARA_FILE_SAVE_NAME "string_para.bin"
#define  STRPARA_SAVE_PART      "/nor/string_para.bin"    //文件系统---参数管理存储文件名称

//iv曲线存储文件
#define IV_DATA_FILE_NAME  "iv_data.bin"
#define IV_DATA_FILE       "/iv_data.bin"

//复位-临时存储文件
#define RESET_FILE_NAME "reset_cause_record"
#define RESET_FILE      "/reset_cause_record"

#define MQTT_CA_FILE                 "/nor/ca.pem"
#define MQTT_CLIENT_KEY_FILE         "/nor/client.key"
#define MQTT_CLIENT_FILE             "/nor/client.pem"

#define MQTT_CA_OTHER_FILE           "/nor/ca_other.pem"
#define MQTT_CLIENT_KEY_OTHER_FILE   "/nor/client_other.key"
#define MQTT_CLIENT_OTHER_FILE       "/nor/client_other.pem"

#define FAC_DEFAULT_PARA_FILE        "/nor/FacDefaultPara.bin" //出厂默认参数文件


//故障录波数据文件数据文件
#define RECORD_FAULT_INFO    "/tov_record_info"
#define RECORD_FAULT_DATA    "record_data.bin"
#define RECORD_FAULT_FILE_0  "/record_data.bin_0"
#define RECORD_FAULT_FILE_1  "/record_data.bin_1"
#define RECORD_FAULT_FILE_2  "/record_data.bin_2"

#define RECORD_FAULT_NACC        "record_data_nacc.bin"
#define RECORD_FAULT_NACC_FILE   "/record_data_nacc.bin"


#define FW_AP_FILE           "/fw_ap6212.bin"

#define MANU_INFO            "/manu_info.bin"
//极值文件
#define  EXTREME_DIR                                "/extreme_data"
#define  A_VOL_EXTREME_DATA_FILE_NAME               "A_phase_vol.bin"
#define  A_VOL_EXTREME_DATA_FILE                    EXTREME_DIR"/A_phase_vol.bin"
#define  A_CURR_EXTREME_DATA_FILE_NAME              "A_phase_curr.bin"
#define  A_CURR_EXTREME_DATA_FILE                   EXTREME_DIR"/A_phase_curr.bin"

#define  B_VOL_EXTREME_DATA_FILE_NAME               "B_phase_vol.bin"
#define  B_VOL_EXTREME_DATA_FILE                    EXTREME_DIR"/B_phase_vol.bin"
#define  B_CURR_EXTREME_DATA_FILE_NAME              "B_phase_curr.bin"
#define  B_CURR_EXTREME_DATA_FILE                   EXTREME_DIR"/B_phase_curr.bin"

#define  C_VOL_EXTREME_DATA_FILE_NAME               "C_phase_vol.bin"
#define  C_VOL_EXTREME_DATA_FILE                    EXTREME_DIR"/C_phase_vol.bin"
#define  C_CURR_EXTREME_DATA_FILE_NAME              "C_phase_curr.bin"
#define  C_CURR_EXTREME_DATA_FILE                   EXTREME_DIR"/C_phase_curr.bin"

#define  N_VOL_EXTREME_DATA_FILE_NAME               "N_phase_vol.bin"
#define  N_VOL_EXTREME_DATA_FILE                    EXTREME_DIR"/N_phase_vol.bin"
#define  N_CURR_EXTREME_DATA_FILE_NAME              "N_phase_curr.bin"
#define  N_CURR_EXTREME_DATA_FILE                   EXTREME_DIR"/N_phase_curr.bin"

#define  DC_VOL_EXTREME_DATA_FILE_NAME              "DC_phase_vol.bin"
#define  DC_VOL_EXTREME_DATA_FILE                   EXTREME_DIR"/DC_phase_vol.bin"
#define  DC_CURR_EXTREME_DATA_FILE_NAME             "DC_phase_curr.bin"
#define  DC_CURR_EXTREME_DATA_FILE                  EXTREME_DIR"/DC_phase_curr.bin"

#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif
#endif
