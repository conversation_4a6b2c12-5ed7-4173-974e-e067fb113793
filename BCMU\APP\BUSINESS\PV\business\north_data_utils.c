#include "north_data_utils.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "storage.h"
#include "ee_public_info.h"
#include "partition_def.h"
#include "utils_rtthread_security_func.h"
#include "softbus.h"
#include "msg_id.h"

static char g_device_id[PV_NUM][SN_LEN] = {0};
static unsigned char g_master_addr = 0;
static pv_parallel_status_t st_pv_parallel_status = {0};
static alarm_to_eeprom_t* s_alarm_to_eeprom = NULL;
static int s_alarm_to_eeprom_num = 0;
static alarm_to_eeprom_t* s_init_alarm_to_eeprom = NULL;
static int s_init_alarm_to_eeprom_num = 0;
static modbus_addr_map_data_t* g_modbus_alarm_data_map_master = NULL;

pv_parallel_status_t* get_parallel_status() {
    return &st_pv_parallel_status;
}

int register_alarm_eeprom_info(alarm_to_eeprom_t* alarm_to_eeprom, int alarm_to_eeprom_num, 
                        alarm_to_eeprom_t* init_alarm_to_eeprom, int init_alarm_to_eeprom_num, modbus_addr_map_data_t* modbus_map_data)
{
    s_alarm_to_eeprom               = alarm_to_eeprom;
    s_alarm_to_eeprom_num           = alarm_to_eeprom_num;
    s_init_alarm_to_eeprom          = init_alarm_to_eeprom;
    s_init_alarm_to_eeprom_num      = init_alarm_to_eeprom_num;
    g_modbus_alarm_data_map_master  = modbus_map_data;
    return SUCCESSFUL;
}

void set_parallel_status(pv_parallel_status_t* pv_parallel_status) {
    rt_memcpy_s(&st_pv_parallel_status, sizeof(pv_parallel_status_t), pv_parallel_status, sizeof(pv_parallel_status_t));
}

void clear_parallel_info() {
    unsigned char slave_num = 0;
    set_one_data(DAC_DATA_ID_PARALLEL_NUM, &slave_num);
    rt_memset_s(&st_pv_parallel_status, sizeof(st_pv_parallel_status), 0, sizeof(st_pv_parallel_status));
    handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&st_pv_parallel_status.dev_sn, sizeof(st_pv_parallel_status.dev_sn), DEV_SN_OFFSET);
}

int update_slave_intr(unsigned char mst_addr)
{
    unsigned char comm_intr = FALSE;
    unsigned char slave_num = 0;
    for (int idx = 0; idx < MAX_PARALELL_NUM; idx++)
    {
        if (idx + 1 == mst_addr)
        {
            continue;
        }
        char is_valid = IS_PO_DEV_ON(st_pv_parallel_status.valid_dev, idx);
        if (!is_valid)
        {
            continue;
        }
        slave_num++;
        char is_online = IS_PO_DEV_ON(st_pv_parallel_status.online_dev, idx);
        get_one_data(DAC_TRACE_ID_SLAVE_COMM_INTR + idx, &comm_intr);
        if(is_online && comm_intr)
        {
            comm_intr = FALSE;
            set_one_data(DAC_TRACE_ID_SLAVE_COMM_INTR + idx, &comm_intr);
        }
        else if ((!is_online) && (!comm_intr))
        {
            comm_intr = TRUE;
            set_one_data(DAC_TRACE_ID_SLAVE_COMM_INTR + idx, &comm_intr);
            handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&st_pv_parallel_status.dev_sn, sizeof(st_pv_parallel_status.dev_sn), DEV_SN_OFFSET);
        }
    }
    set_one_data(DAC_DATA_ID_PARALLEL_NUM, &slave_num);
    return comm_intr;
}

int update_slave_dev(unsigned char slave_idx, int status)
{
    int dev_online = IS_PO_DEV_ON(st_pv_parallel_status.online_dev, slave_idx);
    if (status == SUCCESSFUL)
    {
        PO_DEV_ON(st_pv_parallel_status.valid_dev, slave_idx);
        PO_DEV_ON(st_pv_parallel_status.online_dev, slave_idx);
    }
    else if (!IS_PO_DEV_ON(st_pv_parallel_status.valid_dev, slave_idx))
    {
        return FALSE;
    }
    else if (dev_online)
    {
        CANCEL_PO_DEV_ON(st_pv_parallel_status.online_dev, slave_idx);
    }
    return TRUE;
}


void set_ver_data(unsigned int sid_date, unsigned int sid_year, unsigned int sid_month, unsigned int sid_day)
{
    unsigned short year = 0;
    unsigned short month = 0;
    unsigned short day = 0;
    date_base_t date_base = {0};

    get_one_data(sid_year, &year);
    get_one_data(sid_month, &month);
    get_one_data(sid_day, &day);
    date_base.year = year;
    date_base.month = month;
    date_base.day = day;
    set_one_data(sid_date, &date_base);
}

void set_factory_special(unsigned char address)
{
    // 设置从机序列号
    set_serial_number(address, PARALLEL_SLAVE);

    // 设置主控版本日期
    set_ver_data(DAC_PO_DATA_ID_MASTER_CTRL_SOFTWARE_VER_DATE,
                 DAC_PO_DATA_ID_MASTER_CTRL_SOFTWARE_VER_YEAR,
                 DAC_PO_DATA_ID_MASTER_CTRL_SOFTWARE_VER_MONTH,
                 DAC_PO_DATA_ID_MASTER_CTRL_SOFTWARE_VER_DAY);

    // 设置辅控版本日期
    set_ver_data(DAC_PO_DATA_ID_AUXI_CTRL_SOFTWARE_VER_DATE,
                 DAC_PO_DATA_ID_AUXI_CTRL_SOFTWARE_VER_YEAR,
                 DAC_PO_DATA_ID_AUXI_CTRL_SOFTWARE_VER_MONTH,
                 DAC_PO_DATA_ID_AUXI_CTRL_SOFTWARE_VER_DAY);

    // 设置CPLD版本日期
    set_ver_data(DAC_PO_DATA_ID_CPLD_SOFTWARE_VER_DATE,
                 DAC_PO_DATA_ID_CPLD_SOFTWARE_VER_YEAR,
                 DAC_PO_DATA_ID_CPLD_SOFTWARE_VER_MONTH,
                 DAC_PO_DATA_ID_CPLD_SOFTWARE_VER_DAY);

    // 设置监控版本日期
    set_ver_data(DAC_PO_DATA_ID_MONITOR_SOFTWARE_VER_DATE,
                 DAC_PO_DATA_ID_MONITOR_SOFTWARE_VER_YEAR,
                 DAC_PO_DATA_ID_MONITOR_SOFTWARE_VER_MONTH,
                 DAC_PO_DATA_ID_MONITOR_SOFTWARE_VER_DAY);

    // 设置生产日期
    set_ver_data(DAC_PO_DATA_ID_MANUFACTURER_DATE,
                 DAC_PO_DATA_ID_MANUFACTURER_YEAR,
                 DAC_PO_DATA_ID_MANUFACTURER_MONTH,
                 DAC_PO_DATA_ID_MANUFACTURER_DAY);

    // 设置首次启动时间
    set_ver_data(DAC_PO_DATA_ID_FIRST_STARTUP_TIME,
                 DAC_PO_DATA_ID_FIRST_STARTUP_TIME_YEAR,
                 DAC_PO_DATA_ID_FIRST_STARTUP_TIME_MONTH,
                 DAC_PO_DATA_ID_FIRST_STARTUP_TIME_DAY);

    // 设置BOOT版本日期
    set_ver_data(DAC_PO_DATA_ID_MONITOR_BOOT_VER,
        DAC_PO_DATA_ID_MONITOR_BOOT_VER_YEAR,
        DAC_PO_DATA_ID_MONITOR_BOOT_VER_MONTH,
        DAC_PO_DATA_ID_MONITOR_BOOT_VER_DAY);
}

int clear_parallel_alarm(void)
{
    unsigned char val = 0;
    parallel_alarm_msg_t eeprom_msg;
    eeprom_msg.alarm_num = s_alarm_to_eeprom_num - 1;
    // 将分区的告警读一遍，如果有告警就清除
    for (int idx = 0; idx < MAX_PARALELL_NUM; idx++)
    {
        char need_send = FALSE;
        eeprom_msg.dev_addr = idx + 1;
        eeprom_msg.epprom_offset = idx * s_init_alarm_to_eeprom_num * sizeof(alarm_to_eeprom_t);   // 没有有效标志位
        if (handle_storage(read_opr, PARALLEL_ALARM_PART, (unsigned char*)s_alarm_to_eeprom, s_alarm_to_eeprom_num * sizeof(alarm_to_eeprom_t), eeprom_msg.epprom_offset) == FAILURE)
        {
            LOG_E("485 stat read dev %d alarm failed", eeprom_msg.dev_addr);
            continue;
        }
        for (int alarm_idx = 0; alarm_idx < eeprom_msg.alarm_num; alarm_idx++)
        {
            // 清除上一次的上送状态，防止重复上送,同时需要判断ID，防止EE未初始化
            s_alarm_to_eeprom[alarm_idx].update_flag = 0;
            if (s_alarm_to_eeprom[alarm_idx].alarm_value == 1 && 
                s_alarm_to_eeprom[alarm_idx].alarm_ID == g_modbus_alarm_data_map_master[alarm_idx].data_addr)
            {
                set_one_data(g_modbus_alarm_data_map_master[alarm_idx].data_addr, &val);
                s_alarm_to_eeprom[alarm_idx].alarm_value = 0;
                s_alarm_to_eeprom[alarm_idx].update_flag = 1;
                s_alarm_to_eeprom[alarm_idx].alarm_time = 0;
                need_send = TRUE;
            }
        }
        if (need_send)
        {
            if (handle_storage(write_opr, PARALLEL_ALARM_PART, (unsigned char*)s_alarm_to_eeprom, s_alarm_to_eeprom_num * sizeof(alarm_to_eeprom_t), eeprom_msg.epprom_offset) == FAILURE)
            {
                LOG_E("485 stat write dev %d alarm failed", eeprom_msg.dev_addr);
                continue;
            }
            send_msg_to_thread(PARALLEL_ALARM_CHANGE_MSG, MOD_MQTT, (void *)&eeprom_msg, sizeof(parallel_alarm_msg_t));
        }
    }
    return SUCCESSFUL;
}

short rs485_stat()
{
    unsigned char comm_intr = FALSE;
    for (int idx = 0; idx < MAX_PARALELL_NUM; idx++)
    {
        set_one_data(DAC_TRACE_ID_SLAVE_COMM_INTR + idx, &comm_intr);
    }
    clear_slave_sn();
    clear_parallel_info();
    clear_parallel_alarm();
    return SUCCESSFUL;
}

short set_serial_number(unsigned char address, unsigned char master_flag) {
    short ret = 0;
    char if_find = FALSE;
    pv_parallel_status_t* parallel_status = NULL;
    char sn[MAX_SERIAL_NUMBER_SIZE] = {0};

    RETURN_VAL_IF_FAIL(address >= 1 && address <= MAX_PARALELL_NUM, FAILURE);
    parallel_status = get_parallel_status();
    RETURN_VAL_IF_FAIL(parallel_status != NULL, FAILURE);

    if (PARALLEL_MASTER == master_flag) {
        ret = get_one_data(DAC_DATA_ID_MACHINE_BARCODE, sn);
    } else {
        ret = get_one_data(DAC_PO_PARA_ID_MACHINE_BARCODE, sn); 
    }
    RETURN_VAL_IF_FAIL(SUCCESSFUL == ret, FAILURE);

    // 测试阶段条码未设置，不进行条码比对判断,且不用发送任何数据，置为不在位
    if (rt_strnlen(sn, SERIAL_NUMBER_LEN) != SERIAL_NUMBER_LEN) {
        CANCEL_PO_DEV_ON(parallel_status->valid_dev, address - 1);
        CANCEL_PO_DEV_ON(parallel_status->online_dev, address - 1);
        return FAILURE;
    }

    for (int idx = 0; idx < MAX_PARALELL_NUM; idx++) {
        if (rt_strncmp(sn, parallel_status->dev_sn[idx], SERIAL_NUMBER_LEN) == 0) {
            if_find = TRUE;
            if (idx != (address - 1)) {
                // 设备之间互换了地址
                if (IS_PO_DEV_ON(parallel_status->online_dev, idx)) {
                    rt_memcpy(sn, parallel_status->dev_sn[idx], SERIAL_NUMBER_LEN);
                    rt_memcpy(parallel_status->dev_sn[idx], parallel_status->dev_sn[address - 1], SERIAL_NUMBER_LEN);
                    rt_memcpy(parallel_status->dev_sn[address - 1], sn, SERIAL_NUMBER_LEN);
                } else {
                    // 只是改变了设备地址
                    rt_memset(parallel_status->dev_sn[idx], 0, SERIAL_NUMBER_LEN);
                    rt_memcpy(parallel_status->dev_sn[address - 1], sn, SERIAL_NUMBER_LEN);
                    CANCEL_PO_DEV_ON(parallel_status->valid_dev, idx);
                    CANCEL_PO_DEV_ON(parallel_status->online_dev, idx);
                }
            }
            break;
        }
    }
    // 设备第一次上线，或换了一个设备
    if (!if_find) {
        rt_memcpy(parallel_status->dev_sn[address - 1], sn, SERIAL_NUMBER_LEN);
    }
    return SUCCESSFUL;
}


/**
 * @brief 获取整机序列号
 * @param[in] address modbus地址, 1~10
 * @param[out] sn_buff 接收sn的缓冲区
 * @param[in] buff_size 接收区长度
 * @retval SUCCESSFUL 成功 FAILURE 失败
*/
short get_serial_number(unsigned char address, char* sn_buff, unsigned short buff_size) {
    pv_parallel_status_t* parallel_status = NULL;

    RETURN_VAL_IF_FAIL(sn_buff != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(address >= 1 && address <= MAX_PARALELL_NUM, FAILURE);

    parallel_status = get_parallel_status();
    RETURN_VAL_IF_FAIL(parallel_status != NULL, FAILURE);

    rt_memcpy(sn_buff, parallel_status->dev_sn[address - 1], SERIAL_NUMBER_LEN);

    return SUCCESSFUL;
}

void set_device_id(int idx, const char* id, int len)
{
    if (id == NULL)
    {
        return;
    }
    if (len < SN_LEN)
    {
        rt_memset(g_device_id[idx], 0, SN_LEN);
    }
    else
    {
        rt_memcpy(g_device_id[idx], id, SN_LEN);
    }
}

void get_device_id(int idx, char* id)
{
    if (id == NULL)
    {
        return;
    }
    rt_memcpy(id, g_device_id[idx], SN_LEN);
}

void clear_slave_sn()
{
    unsigned char index = 0;
    unsigned char master_addr = get_master_addr();
    for (index = 0; index < PV_NUM; index++)
    {
        if (index == master_addr - 1)
        {
            continue;
        }
        rt_memset_s(g_device_id[index], SN_LEN, 0, SN_LEN);
    }
    return;
}

unsigned char get_addr_by_device_id(char* id)
{
    int loop = 0;
    for(loop = 0; loop < PV_NUM; loop ++)
    {
        if(0 == rt_memcmp(id, g_device_id[loop], SN_LEN))
        {
            return loop + 1;
        }
    }
    return 0xFF;
}

int get_master_addr(void)
{
    return g_master_addr;
}

void set_master_addr(int addr)
{
    g_master_addr = addr;
}
