/*
 * @file    : north_dxcb_modbus.c
 * @brief   : DXCB北向modbus协议实现
 * @details :
 * <AUTHOR> 邵敏10132013
 * @Date    : 2023-08-30
 * @LastEditTime: 2023-08-30
 * @version : V0.0.1
 * @para    : Copyright (c)
 *            ZTE Corporation
 * @par     : History
 *
 *     version: author, date, descn
 */

#include "dev_north_deub_modbus.h"
#include "protocol_layer.h"
#include <string.h>
#include "utils_data_transmission.h"
#include "utils_rtthread_security_func.h"
#include "realdata_save.h"
#include "realdata_id_in.h"
#include "para_id_in.h"
#include "storage.h"
#include "ee_public_info.h"
#include "partition_def.h"
#include "data_type.h"


/* 设备缓冲区长度 */
#define R_NORTH_MODBUS_BUFF_LEN 512 ///<  接收缓冲区长度
#define S_NORTH_MODBUS_BUFF_LEN 512 ///<  发送缓冲区长度

#define DEV_CODE_NO_USE 1

unsigned short g_reg_addr = 0;
int g_reg_nums = 0;

#define MODBUS_DATA_MAP_LEN sizeof(g_modbus_data_map) / sizeof(modbus_addr_map_data_t)
#define PARSE_FUN_MAP_LEN   sizeof(parse_fun_map)/sizeof(parse_fun_map_t)



/*******************************打包映射表*****************/
pack_fun_map_t pack_fun_map[] = {
    {TYPE_FLOAT,  TYPE_INT32U, floattoint32u },
    {TYPE_FLOAT,  TYPE_INT32S, floattoint32s},
    {TYPE_FLOAT,  TYPE_INT16U, floattoint16u},
    {TYPE_FLOAT,  TYPE_INT16S, floattoint16s},
    {TYPE_INT16U, TYPE_INT16U, int16utoint16u},
    {TYPE_INT16S, TYPE_INT16S, int16stoint16s},
    {TYPE_INT32U, TYPE_INT32U, int32utoint32u} ,
    {TYPE_INT32S, TYPE_INT32S, int32stoint32s} ,  
    {TYPE_STRING,    TYPE_STRING,    stringtostring},
    {TYPE_DATE_T,   TYPE_INT16U, datetoint16u},
    {TYPE_INT8S,   TYPE_BIT,    chartobit},
    {TYPE_MAX,    TYPE_MAX,    NULL},
};


/*****************************解包映射表*****************/
parse_fun_map_t parse_fun_map[] = {
    {TYPE_INT16U, TYPE_FLOAT,  parse_int16utofloat},
    {TYPE_INT16S, TYPE_FLOAT,  parse_int16stofloat },
    {TYPE_INT32U, TYPE_FLOAT,  parse_int32utofloat },
    {TYPE_INT32S, TYPE_FLOAT,  parse_int32stofloat},
    {TYPE_INT16U, TYPE_INT16U, parse_int16utoint16u},
    {TYPE_INT32U, TYPE_INT32U, parse_int32utoint32u},
    {TYPE_STRING,    TYPE_STRING,    parse_stringtostring },
    {TYPE_MAX,    TYPE_MAX,    NULL},
};


/* 命令请求头 */
static modbusrtu_cmd_head_t cmd_req[] = {
    {GET_ANA_DATA},
    {GET_PARA_DATA},
    {SET_SINGLE_PARA_DATA},
    {SET_PARA_DATA},
};

/* 命令应答头 */
static modbusrtu_cmd_head_t cmd_ack[] = {
    {GET_ANA_DATA},
    {GET_PARA_DATA},
    {SET_SINGLE_PARA_DATA},
    {SET_PARA_DATA},
};

/* 命令总表,必须以空字符串""结束 */
static cmd_t no_poll_cmd_tab[] = {
    {GET_ANA_DATA, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, new_pack_ana_para_data, parse_start_addr_and_reg_nums},
    {GET_PARA_DATA, CMD_PASSIVE, &cmd_req[1], &cmd_ack[1], sizeof(modbusrtu_cmd_head_t), NULL, NULL, new_pack_ana_para_data, parse_start_addr_and_reg_nums},
    {SET_SINGLE_PARA_DATA, CMD_PASSIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_set_para_data, new_parse_para_data_from_buff},//后续处理
    {SET_PARA_DATA, CMD_PASSIVE, &cmd_req[3], &cmd_ack[3], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_set_para_data, new_parse_para_data_from_buff},
    {0},
};


static dev_type_t dev_val_north_deub = {
    DEV_NORTH_DEUB, 
    1, 
    PROTOCOL_MODBUS_RTU, 
    LINK_NORTH_DEUB, 
    R_NORTH_MODBUS_BUFF_LEN, 
    S_NORTH_MODBUS_BUFF_LEN, 
    DEV_CODE_NO_USE, 
    no_poll_cmd_tab, 
    NULL,
    0
};


modbus_addr_map_data_t g_modbus_data_map[] = {
    {1,   0, TYPE_FLOAT, TYPE_INT16S, 1, 2, TYPE_ANA, DEUB_DATA_ID_PHASE_VOL},                      // 相电压
    {2,   0, TYPE_FLOAT, TYPE_INT16S, 1, 2, TYPE_ANA, DEUB_DATA_ID_PHASE_VOL + 1},                  
    {3 ,  0, TYPE_FLOAT, TYPE_INT16S, 1, 2, TYPE_ANA, DEUB_DATA_ID_PHASE_VOL + 2}, 
    {4 ,  0, TYPE_FLOAT, TYPE_INT16S, 1, 2, TYPE_ANA, DEUB_DATA_ID_AC_FREQUENCY},                   // 交流频率
    {5 ,  0, TYPE_FLOAT, TYPE_INT16S, 1, 2, TYPE_ANA, DEUB_DATA_ID_BUS_VOL},                        // 母排电压
    {6,   0, TYPE_FLOAT, TYPE_INT16S, 3, 2, TYPE_ANA, DEUB_DATA_ID_INSULATION_DETECT_STABLE_VOL},          // 绝缘检测电压
    {7,   0, TYPE_FLOAT, TYPE_INT16S, 1, 2, TYPE_ANA, DEUB_DATA_ID_BATTERY_CUR},                    // 电池电流
    {8,   0, TYPE_FLOAT, TYPE_INT16S, 1, 2, TYPE_ANA, DEUB_DATA_ID_BATTERY_CUR + 1},
    {9 ,  0, TYPE_FLOAT, TYPE_INT16S, 1, 2, TYPE_ANA, DEUB_DATA_ID_BATTERY_CUR + 2},
    {10,  0, TYPE_FLOAT, TYPE_INT16S, 1, 2, TYPE_ANA, DEUB_DATA_ID_BATTERY_CUR + 3},

    {11,  0, TYPE_INT8S, TYPE_BIT,    0, 1, TYPE_ANA, DEUB_DATA_ID_INPUT_DRY_CONTACT},              // 输入干接点
    {11,  0, TYPE_INT8S, TYPE_BIT,    0, 1, TYPE_ANA, DEUB_DATA_ID_INPUT_DRY_CONTACT + 1},
    {11,  0, TYPE_INT8S, TYPE_BIT,    0, 1, TYPE_ANA, DEUB_DATA_ID_INPUT_DRY_CONTACT + 2},
    {11,  0, TYPE_INT8S, TYPE_BIT,    0, 1, TYPE_ANA, DEUB_DATA_ID_INPUT_DRY_CONTACT + 3},
    {11,  0, TYPE_INT8S, TYPE_BIT,    0, 1, TYPE_ANA, DEUB_DATA_ID_INPUT_DRY_CONTACT + 4},
    {11,  0, TYPE_INT8S, TYPE_BIT,    0, 1, TYPE_ANA, DEUB_DATA_ID_INPUT_DRY_CONTACT + 5},
    {11,  1, TYPE_INT8S, TYPE_BIT,    0, 10, TYPE_ANA, },
    {12,  0, TYPE_INT32S, TYPE_INT32S, 0, 4, TYPE_ANA, DEUB_DATA_ID_INSULATION_RES},                // 绝缘阻抗
    {14,  0, TYPE_INT32S, TYPE_INT32S, 0, 4, TYPE_ANA, DEUB_DATA_ID_INSULATION_RES + 1},
    {200,  0, TYPE_STRING, TYPE_STRING, 0, 20, TYPE_ANA, DEUB_DATA_ID_SOFTWARE_NAME},               //软件名称
    {210,  0, TYPE_STRING, TYPE_STRING, 0, 20, TYPE_ANA, DEUB_DATA_ID_SOFTWARE_VERSION},            //软件版本
    {220,  0, TYPE_STRING, TYPE_STRING, 0, 20, TYPE_ANA, DEUB_DATA_ID_SOFTWARE_DATE},               //软件日期
    {230,  0, TYPE_STRING, TYPE_STRING, 0, 20, TYPE_ANA, DEUB_DATA_ID_SERIAL_NUMBER},               //序列号
    {240,  0, TYPE_STRING, TYPE_STRING, 0, 16, TYPE_ANA, DEUB_DATA_ID_HARDWARE_VERSION},     // 硬件版本号
    {250,  0, TYPE_STRING, TYPE_STRING, 0, 20, TYPE_ANA, DEUB_DATA_ID_BOARD_MANU_DATE },     // 单板生产日期
    {300,  0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DEUB_DATA_ID_DEVICE_ADDR},                  //设备地址
    {301,  0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DEUB_DATA_ID_INSULATION_RES_MODE},          //绝缘阻抗计算模式
    /*********************************************校准参数***************************************************************/
    {302,  0, TYPE_FLOAT, TYPE_INT16S, 3, 2, TYPE_ANA, DEUB_DATA_ID_AC_VOL_CALIBRATE_SLOPE},         //交流电压校准斜率
    {303,  0, TYPE_FLOAT, TYPE_INT16S, 3, 2, TYPE_ANA, DEUB_DATA_ID_BATTERY_CURR_ZERO_POINT},        //电池电流1零点
    {304,  0, TYPE_FLOAT, TYPE_INT16S, 3, 2, TYPE_ANA, DEUB_DATA_ID_BATTERY_CURR_RESET_THRESHOLD},   //电池电流1归零阈值
    {305,  0, TYPE_FLOAT, TYPE_INT16S, 3, 2, TYPE_ANA, DEUB_DATA_ID_BATTERY_CURR_ZERO_POINT + 1},        //电池电流2零点
    {306,  0, TYPE_FLOAT, TYPE_INT16S, 3, 2, TYPE_ANA, DEUB_DATA_ID_BATTERY_CURR_RESET_THRESHOLD + 1},   //电池电流2归零阈值
    {307,  0, TYPE_FLOAT, TYPE_INT16S, 3, 2, TYPE_ANA, DEUB_DATA_ID_BATTERY_CURR_ZERO_POINT + 2},        //电池电流3零点
    {308,  0, TYPE_FLOAT, TYPE_INT16S, 3, 2, TYPE_ANA, DEUB_DATA_ID_BATTERY_CURR_RESET_THRESHOLD + 2},   //电池电流3归零阈值
    {309,  0, TYPE_FLOAT, TYPE_INT16S, 3, 2, TYPE_ANA, DEUB_DATA_ID_BATTERY_CURR_ZERO_POINT + 3},        //电池电流4零点
    {310,  0, TYPE_FLOAT, TYPE_INT16S, 3, 2, TYPE_ANA, DEUB_DATA_ID_BATTERY_CURR_RESET_THRESHOLD + 3},   //电池电流4归零阈值
    /*********************************************校准参数***************************************************************/


    /*********************************************打桩数据部分***************************************************************/
    {400,  0, TYPE_INT16U, TYPE_INT16U, 0, 2, TYPE_ANA, DEUB_DATA_ID_MOCK_SWITCH},                  // 打桩开关
    /*********************************************打桩数据部分***************************************************************/

};


dev_type_t* init_dev_north_deub(void) {
    return &dev_val_north_deub;
}

void check_ana_reserve_flag(int index , void* data, modbus_addr_map_data_t* data_map)
{
    if (data_map[index].reserve_flag != 1)
    {
        get_data_by_id_type(index, data, data_map);
        return;
    }
    return;
}


/*******************************打包函数定义*****************/
int floattoint32u(int* index , unsigned char* data_buff , int* reg_nums , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map  )
{
    float data = 0.0 ;  
    int tmp = 0;
    check_ana_reserve_flag( *index ,&data,data_map);    
    tmp =round(data * percision );
    put_uint32_to_buff(&data_buff[total_len], (unsigned int)(int)(tmp));
    (*index)++;
    (*reg_nums) -= 2;
    return DATA_LEN_4;
}
int floattoint32s(int* index , unsigned char* data_buff, int* reg_nums , int data_len ,  int percision, int total_len, modbus_addr_map_data_t* data_map  )
{
    float data = 0.0 ;  
    int tmp = 0;
    check_ana_reserve_flag( *index ,&data, data_map);    
    tmp =round(data * percision );
    put_int32_to_buff(&data_buff[total_len], (signed int)(tmp));
    (*index)++;
    (*reg_nums) -= 2;
    return DATA_LEN_4;
}
int floattoint16u(int* index , unsigned char* data_buff , int* reg_nums , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map )
{
    float data = 0.0 ;  
    int tmp = 0;
    check_ana_reserve_flag( *index ,&data, data_map);    
    tmp =round(data * percision );
    put_uint16_to_buff(&data_buff[total_len], (unsigned short)(short)(tmp));
    (*index)++;
    (*reg_nums) -= 1;
    return DATA_LEN_2;
}
int floattoint16s(int* index , unsigned char* data_buff , int* reg_nums , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map )
{
    float data = 0.0 ;  
    int tmp = 0;
    check_ana_reserve_flag( *index ,&data, data_map);    
    tmp =round(data * percision );
    put_int16_to_buff(&data_buff[total_len], (signed short)(tmp));
    (*index)++;
    (*reg_nums) -= 1;
    return DATA_LEN_2;
}
int int16utoint16u(int* index , unsigned char* data_buff , int* reg_nums , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map )
{
    unsigned short data = 0;
    check_ana_reserve_flag(*index ,&data, data_map);  
    put_uint16_to_buff(&data_buff[total_len], data * percision);
    (*index)++;
    (*reg_nums) -= 1;
    return DATA_LEN_2;
}

int int16stoint16s(int* index , unsigned char* data_buff , int* reg_nums , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map )
{
    signed short data = 0;
    check_ana_reserve_flag( *index ,&data, data_map);  
    put_int16_to_buff(&data_buff[total_len], data * percision);

    (*index)++;
    (*reg_nums) -= 1;
    return DATA_LEN_2;
}

int int32utoint32u(int* index , unsigned char* data_buff , int* reg_nums, int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map )
{
    unsigned int data = 0;
    check_ana_reserve_flag( *index ,&data, data_map);  

    put_uint32_to_buff(&data_buff[total_len], data * percision );
    (*index)++;
    (*reg_nums) -= 2;
    return DATA_LEN_4;
}

int int32stoint32s(int* index , unsigned char* data_buff , int* reg_nums, int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map )
{
    int data = 0;
    check_ana_reserve_flag( *index ,&data, data_map);  

    put_int32_to_buff(&data_buff[total_len], data * percision );
    (*index)++;
    (*reg_nums) -= 2;
    return DATA_LEN_4;
}

int stringtostring(int* index , unsigned char* data_buff , int* reg_nums , int data_len,  int percision  , int total_len, modbus_addr_map_data_t* data_map )
{
    unsigned char data[64]={0};
    check_ana_reserve_flag( *index ,&data, data_map);  
    rt_memcpy_s(&data_buff[total_len], data_len, data, data_len);
    (*reg_nums) -= data_len / 2;
    (*index)++;
    return data_len;
}
int datetoint16u(int* index, unsigned char* data_buff, int* reg_nums, int data_len, int percision, int total_len, modbus_addr_map_data_t* data_map)
{
    date_base_t data = {0};
    get_one_data(data_map[*index].data_addr, &data);
    put_uint16_to_buff(&data_buff[total_len], data.year);
    put_uint16_to_buff(&data_buff[total_len+2], data.month);
    put_uint16_to_buff(&data_buff[total_len+4], data.day);
    (*index) += 3;
    (*reg_nums) -= 3;
    return DATA_LEN_6;
}



void numchar_to_bit_to_int16(unsigned char* data_buff, int total_len, int* index, int percision, modbus_addr_map_data_t* data_map)
{
    unsigned short data = 0;
    unsigned char bit[16] = {0};
    unsigned int result = 0;
    int i = 0;
    while( i < 16 )
    {
        if (data_map[*index].reserve_flag == 1)
        {
            i += data_map[*index].data_len;
            //rt_kprintf(" addr:%x | value: 0 \n",g_modbus_data_map[*index].register_addr);
            
        }
        else
        {
            get_data_by_id_type( *index, &result, data_map);
            //rt_kprintf(" addr:%x | value: %d \n",g_modbus_data_map[*index].register_addr,result);
            bit[i] = result & 0x01;
            i++;
        }
        (*index)++;
    }
    for (int i = 15; i >= 0; i--)
    {
        data += bit[i] << i;
    }
    put_int16_to_buff(&data_buff[total_len], data);
    //rt_kprintf("-------------\n");
}



int chartobit(int* index, unsigned char* data_buff, int* reg_nums, int data_len, int percision, int total_len, modbus_addr_map_data_t* data_map)
{
    numchar_to_bit_to_int16(data_buff, total_len, index, percision, data_map);
    (*reg_nums) -= 1;
    return DATA_LEN_2;
}



/*********************************解包函数定义***********************************************/
int parse_int16utofloat( int* index , unsigned char* data_buff , int* data_valude_index ,int percision ,int* reg_nums, modbus_addr_map_data_t* data_map)
{
    unsigned short data = 0;
    float result = 0.0; 
    data = get_uint16_data(&data_buff[*data_valude_index]);
    result = data*1.0 / percision;
    (*reg_nums) -= 1;
    (*data_valude_index) += 2;
    return set_data_by_id_type(*index , &result, data_map);
}

int parse_int16stofloat( int* index , unsigned char* data_buff , int* data_valude_index , int percision ,int* reg_nums, modbus_addr_map_data_t* data_map)
{
    signed short data = 0;
    float result = 0.0;
    data = get_int16_data(&data_buff[*data_valude_index]);
    result  = data*1.0 / percision;
    (*reg_nums) -= 1;
    (*data_valude_index) += 2;
    return set_data_by_id_type(*index , &result, data_map);
}

int parse_int32utofloat( int* index , unsigned char* data_buff , int* data_valude_index ,  int percision ,int* reg_nums, modbus_addr_map_data_t* data_map)
{
    unsigned int data = 0;
    float result = 0.0;
    data = get_ulong_data(&data_buff[*data_valude_index]);
    result = data*1.0 / percision;
    (*reg_nums) -= 2;
    (*data_valude_index) += 4;
    return set_data_by_id_type(*index , &result, data_map);
}

int parse_int32stofloat( int* index , unsigned char* data_buff , int* data_valude_index , int percision ,int* reg_nums, modbus_addr_map_data_t* data_map)
{
    signed int data = 0;
    float result = 0.0;
    data = get_ulong_data(&data_buff[*data_valude_index]);
    result  = data *1.0 / percision;
    (*reg_nums) -= 2;
    (*data_valude_index) += 4;
    return set_data_by_id_type(*index , &result, data_map);
}

int parse_int16utoint16u( int* index , unsigned char* data_buff , int* data_valude_index , int percision ,int* reg_nums, modbus_addr_map_data_t* data_map)
{
    unsigned short data = 0;
    unsigned short result = 0;
    data = get_uint16_data(&data_buff[*data_valude_index]);
    result  = data *1.0 / percision;
    (*reg_nums) -= 1;
    (*data_valude_index) += 2;
    return set_data_by_id_type(*index , &result, data_map);
}

int parse_int32utoint32u( int* index , unsigned char* data_buff , int* data_valude_index ,int percision , int* reg_nums, modbus_addr_map_data_t* data_map)
{
    unsigned int data = 0;
    unsigned int result = 0;
    data = get_ulong_data(&data_buff[*data_valude_index]);
    result  = data *1.0 / percision;
    (*reg_nums) -= 2;
    (*data_valude_index) += 4;
    return set_data_by_id_type(*index , &result, data_map);
}

int parse_stringtostring( int* index , unsigned char* data_buff , int* data_valude_index , int percision , int* reg_nums, modbus_addr_map_data_t* data_map)
{
    int ret = SUCCESSFUL;
    unsigned char* data = (unsigned char*)rt_malloc(data_map[*index].data_len);
    if(data == NULL)
    {
        return FAILURE;
    }
    rt_memcpy_s(data, data_map[*index].data_len  , &data_buff[*data_valude_index], data_map[*index].data_len);
    (*reg_nums) -= data_map[*index].data_len/2;
    (*data_valude_index) += data_map[*index].data_len;
    ret = set_data_by_id_type(*index , data, data_map);
    rt_free(data);
    return ret;  
}




char set_data_by_id_type(int index, void* data, modbus_addr_map_data_t* data_map)
{
    unsigned char id_type = data_map[index].sid_type;
    if (id_type == TYPE_ANA)
    {
        if (set_one_data(data_map[index].data_addr, data) == FAILURE)
        {
            return FAILURE;
        }
    }
    return SUCCESSFUL;
}


void get_data_by_id_type(int index , void* data, modbus_addr_map_data_t* data_map)
{   
    unsigned char id_type = data_map[index].sid_type;

    if(id_type == TYPE_ANA )
    {
        get_one_data( data_map[index].data_addr , data);
    }
}



//总入口函数
//打包入口函数
int new_pack_data_to_buff(void* cmd_buff , int* index, int offset_value, modbus_addr_map_data_t* data_map)
{
    int type = 0;
    int old_type = 0;
    int percision = 0;
    int data_len = 0;
    int total_len = 0;
    int reg_nums = g_reg_nums;

    //+1应为第一个字节要放总长度（尽管可以直接寄存器数目计算，但是为了验证代码准性，累加长度的）
    unsigned char *data_buff = (((cmd_buf_t *)cmd_buff)->buf) + offset_value; // 准备往data_buff中存放数据

    while (reg_nums > 0 )
    {
        // is_contain_time(*index , 0, data_map);
        type = data_map[*index].type;
        old_type = data_map[*index].old_type;
        percision = pow(10, data_map[*index].precision);
        data_len = data_map[*index].data_len;

        for( int i = 0 ;i < sizeof(pack_fun_map)/sizeof(pack_fun_map_t); i++)
        {
            if (pack_fun_map[i].src_type == TYPE_MAX)
            {
                LOG_E("pack | i:%d, not this data type!\n", i);
                ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_VALUE;
                return FAILURE;
            }
            if(pack_fun_map[i].src_type == old_type && pack_fun_map[i].dst_type == type)
            {
                total_len += pack_fun_map[i].pack_fun(index , data_buff , &reg_nums , data_len , percision , total_len,data_map);
                break;
            }
        }
    }
    return total_len;
}
int new_pack_ana_para_data(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    int reg_addr = g_reg_addr;
    /*先找到基地址的下标索引*/
    int index = find_register_start_addr_index(reg_addr);
    if (index == NO_MATCH_REGISTER_ADDR)
    {
        ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_ADDR;
        return SUCCESSFUL;
    }
    int data_len = new_pack_data_to_buff( cmd_buff , &index, 1,g_modbus_data_map);

    ((cmd_buf_t *)cmd_buff)->buf[0] = data_len;
    ((cmd_buf_t *)cmd_buff)->data_len = data_len + 1;

    return SUCCESSFUL;
}

int parse_start_addr_and_reg_nums(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    unsigned char *data_buff = ((cmd_buf_t *)cmd_buff)->buf;

    g_reg_addr = get_int16_data(&data_buff[0]); // 获取数据域的寄存器首地址 2字节
    g_reg_nums = get_int16_data(&data_buff[2]); // 获取数据域的寄存器个数 2字节
    return SUCCESSFUL;
}

int find_register_start_addr_index(int reg_addr)
{

    int ret = NO_MATCH_REGISTER_ADDR;
    int star_index = 0;
    int mid_index = 0;
    int end_index = sizeof(g_modbus_data_map) / sizeof(modbus_addr_map_data_t) - 1;

    while (star_index <= end_index && star_index >= 0)
    {
        mid_index = (star_index + end_index) / 2;
        if (g_modbus_data_map[mid_index].register_addr == reg_addr)
        {
            ret = mid_index;
        }
        star_index = (g_modbus_data_map[mid_index].register_addr < reg_addr) ? (mid_index + 1) : star_index;
        end_index = (g_modbus_data_map[mid_index].register_addr < reg_addr) ? end_index : (mid_index - 1);
    }

    return ret;
}



int deal_special_data(unsigned short reg_addr, unsigned char *data_buff)
{
    int ret = 0;
    float calibreate_data = 0;
    float origin_calibreate_data = 0;
    unsigned short data = 0;
    unsigned short origin_addr = 0;
    if(reg_addr == 300)
    {
        data = get_uint16_data(data_buff);

        // 设备地址
        handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char*)&origin_addr, sizeof(origin_addr), DEVICE_ADDR_OFFSET);
        if(origin_addr == data)
        {
            ret = SUCCESSFUL;
            return ret;
        }

        ret = handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&data, sizeof(data), DEVICE_ADDR_OFFSET);
    }
    if(reg_addr >= 302 && reg_addr <= 310)
    {
        // 校准参数设置存eeprom
        calibreate_data = get_int16_data(data_buff) * 0.001;
        handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char*)&origin_calibreate_data, sizeof(origin_calibreate_data), AC_VOL_CALI_OFFSET + (sizeof(float) * (reg_addr - 302)));
        if(if_float_equal(origin_calibreate_data, calibreate_data) == TRUE)
        {
            ret = SUCCESSFUL;
            return ret;
        }
        ret = handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&calibreate_data, sizeof(calibreate_data), AC_VOL_CALI_OFFSET + (sizeof(float) * (reg_addr - 302)));
    }

    return ret;
}


//解包入口函数
int new_parse_para_data_from_buff(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    unsigned char *data_buff = ((cmd_buf_t *)cmd_buff)->buf;

    g_reg_addr = get_int16_data(&data_buff[0]); // 获取数据域的寄存器首地址 2字节
    g_reg_nums = get_int16_data(&data_buff[2]); // 获取数据域的寄存器个数 2字节

    int data_valude_indx = 5;
    int reg_addr = g_reg_addr;
    int reg_nums = g_reg_nums;
    int type = 0;
    int old_type = 0;
    int percision = 0;
    /*先找到基地址的下标索引*/
    int index = find_register_start_addr_index(reg_addr);
    if (index == NO_MATCH_REGISTER_ADDR)
    {
        ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_ADDR;
        return SUCCESSFUL;
    }

    while ((reg_nums > 0) && (index < MODBUS_DATA_MAP_LEN))
    {
        unsigned char flag_result = g_modbus_data_map[index].reserve_flag;
        if(flag_result == 1)
        {
            index++;
            reg_nums--;
            data_valude_indx += 2;
            continue;
        }
        type = g_modbus_data_map[index].type;
        old_type = g_modbus_data_map[index].old_type;
        percision = pow(10, g_modbus_data_map[index].precision);
        for(int i = 0; i < PARSE_FUN_MAP_LEN; i++)
        {
            if (parse_fun_map[i].src_type == TYPE_MAX)
            {
                LOG_E("parse | not this data type!\n");
                ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_VALUE;
                return SUCCESSFUL;
            }
            
            if(parse_fun_map[i].src_type == type && parse_fun_map[i].dst_type == old_type)
            {
                
                if(deal_special_data(g_modbus_data_map[index].register_addr, &data_buff[data_valude_indx]) != SUCCESSFUL)
                {
                    LOG_E("parse | deal_special_data error!\n");
                    ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_VALUE;
                    return SUCCESSFUL;
                }
                

                if(parse_fun_map[i].parse_func(&index, data_buff, &data_valude_indx, percision, &reg_nums, g_modbus_data_map) != SUCCESSFUL)
                {
                    ((cmd_buf_t *)cmd_buff)->rtn = MODBUS_RTN_ILLEGAL_VALUE;
                    return SUCCESSFUL;
                }
                break;
            }
        }

        index++;
    }
    return SUCCESSFUL;
}

int pack_set_para_data(void* dev_inst, void* cmd_buff)
{
    int offset = 0;
    // 正确响应，数据域：回复寄存器地址+寄存器个数
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    unsigned char *data_buff = ((cmd_buf_t *)cmd_buff)->buf; // 准备往data_buff中存放数据

    put_uint16_to_buff(&data_buff[offset], g_reg_addr);
    offset += 2;

    put_uint16_to_buff(&data_buff[offset], g_reg_nums);
    offset += 2;

    ((cmd_buf_t *)cmd_buff)->data_len = offset;
    return SUCCESSFUL;
}

