/**************************************************************************
* Copyright (C) 2001, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：ZXDU18 CTL板软件
* 文件名称：sample.h
* 文件说明：数据采集模块头文件
* 作    者：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/
#ifndef SOFTWARE_SRC_APP_SAMPLE_H_
#define SOFTWARE_SRC_APP_SAMPLE_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "common.h"

/***********************  常量定义  ************************/
#define     SAMPLE_TIMES        6           //每路模拟量采样次数
#define     REFRESH_COUNTER     2           //刷新电池管理的采样圈数
#define     TMP_DEAL_COUNTS     6           //电池温度无效判断次数

#define V_5V            0
#define TBATT1          1
#define TBATT2          2
#define TBATT3          3
#define TBATT4          4
#define VCELL1          5

#define START_CHANNEL   V_5V      //定义起始模拟通道
#define END_CHANNEL     VCELL1//V_5V        //定义最终模拟通道
#define TOTAL_CHANNEL   (END_CHANNEL-START_CHANNEL+1)   //定义模拟通道总数

/* 比例系数 */
#define ADC_VREF        2.5
#define ADC_FULLSCALE   0xFFFF
#define COEFF_VOUT      24.0                //负载电压
#define COEFF_TBATT     155.9454             // 1.0      2*1000/(4.75*2.7))* 1.0        //电池温度系数
#define COEFF_VBATT     24.0                //电池电压系数60/2.5

#define NTC_START_TEMP   -40.0f          // 线性差值起始温度
#define CELLTEMP_MIN	 -40.0f          // 单体正常温度下限
#define CELLTEMP_MAX	 150.0f          // 单体正常温度上限
#define ENVTEMP_MAX      120.0f          // 环境温度传感器失效后检测温度
#define BASE_VOLT        1.225           // 基准电压
#define ZERO_TEMP        273.0           // 绝对零点温度
#define DEFAULT_TEMP_MIN -75.0f          // 缺省温度最小值
#define DEFAULT_TEMP_MAX 350.0f          // 缺省温度最大值
#define SAMPLE_TIME_SNUM   4             //用于采样线程中时间计数(一秒钟为4次,10秒钟为34次)
#define CELL_TEMP_RISE_COUNTER 3         //单体温升满足条件连续判断次数

#define CELLTEMP_MEDIADELTA_5  5         // 单体温度与有效单体温度中值的差值5℃
#define CELLTEMP_MEDIADELTA_15 15        // 单体温度与有效单体温度中值的差值15℃（用于R321判断单体温度无效）
#define CELLTEMP_MEDIADELTA_20 20        // 单体温度与有效单体温度中值的差值20℃（用于D121判断单体温度无效）
#define CELLTMP_45             45        // 单体温度为45度
#define CELLTMP_75             75        // 单体温度为75度
#define CELLTEMP_RISERATE_2    2         // 单体温升系数2°C/10min

#define ERROR_NULL             255       // 错误或者空值


#define CURDETVOLTH_75MV 3813.26//(1.225******)*4095/2.5v\\\1.103V=10/0.68*75mv
#define CURDETVOLTL_75MV 199.84 //(1.225-1.103)*4095/2.5
#define CURDETVOLTH_50MV 3210.48//(1.225+0.735)*4095/2.5\\\0.735V=10/0.68*50mv
#define CURDETVOLTL_50MV 802.62 //(1.225-0.735)*4095/2.5

#define TBATTCOF    1.595833333

#define BCM_ALARM            0 //级别和屏蔽关系以后的告警
#define BCM_ALARM_REAL       1 //级别和屏蔽关系前的告警


#define RESTOREBPS_TIMES    4           //一键恢复波特率判断次数
#define RESTOREBPS_MAX      16          //按键超出此时间加判断时间认为是想恢复参数而不是波特率
#define RESTOREPARA_TIMES   100         //一键恢复参数判断次数

#define POWER_SOURCE_BATT       1   //电池

//电池状态，yang_an,整理协议修改
#define BATT_EXIST              0
#define BATT_NOT_EXIST          1

#define LOAD_CURR_COUNTER       4

#define SECTNUM_4K_256  16
#define SECTNUM_32K_256 128

#define ENERGY_NUM 24
#define OUTPUT_RLY_NUM 2

#define CHIP_MT9818 1
#define CHIP_BQ76952 2
#ifndef FTTEST
#define SAMPLE_TWO_HOUR 24000   // 0.3 * 24000 = 7200
#define SAMPLE_TEN_MIN  2000   // 0.3 * 2000 = 600
#else
#define SAMPLE_TWO_HOUR (2*60)   // 2 小时
#define SAMPLE_TEN_MIN  (10)  // 10 分钟
#endif
#define HYPERTEMP 0  // 高温补偿
#define HYPOTEMP 1   // 低温补偿

/*********************  函数声明  **********************/
BYTE GetMinvolNo(void);

/*********************  数据结构定义  **********************/

/************************************************************
** 结构名: T_BCMDataStruct
** 描  述: BCM 实时数据数据结构定义
** 作  者: 夏文娟
** 日  期: 2018-06-05
** 版  本: V4.0 
** 修改记录         
** 日  期       版  本      修改人      修改摘要
** 
**************************************************************/
typedef struct
{
    FLOAT fBattVolt;                            //电池电压
    FLOAT fExterVolt;                           //外部电压
    FLOAT fEnvTemp;                             //环境温度
    FLOAT fBattCurr;                            //电池电流
    FLOAT afCellVolt[CELL_VOL_NUM_MAX];         //单体电压
    FLOAT afCellTemp[CELL_TEMP_NUM_MAX];        //单体温度
    BOOLEAN abCellTempFault[CELL_TEMP_NUM_MAX]; //温度检测错误
    WORD wBattSOC;                              //电池SOC
    WORD wBattSOH;                              //电池SOH
    WORD wBattCycleTimes;                       //电池循环次数
    BYTE ucChgProtSta;                          //充电保护状态
    BYTE ucDischgProtSta;                       //放电保护状态
    BYTE ucCellEquSta;                          //电芯均衡状态
    BYTE ucBattPackSta;                         //电池组状态  0:充电；1：放电；2：在线非浮充；3：离线 来自电池管理
    FLOAT fBattChgReqVolt;                      //电池充电请求电压
    FLOAT fBattChgReqCurr;                      //电池充电请求电流
    FLOAT fBusChgReqCurr;                       //Bus充电请求电流
    BYTE aucOutputRly[OUTPUT_RLY_NUM];          //输出干接点状态
    BYTE ucBattChargeEn;                        // 0:允许；1：禁止
    BYTE ucBattDischEn;                         // 0:允许；1：禁止
    FLOAT fBusCurr;                             // BUS电流
    BYTE ucBduSleep;                            // 0:未休眠；1：休眠
    BYTE ucLimit;                               ////BDU限流状态
    BYTE ucBduStatus;                           // 0:升降压充电;1:升降压放电;2:升压充电;3:升压放电;4:降压充电;5:降压放电;6:其他 来自BDU
    FLOAT fVoltChange;                          ///电压变化值
    FLOAT fDischObjVolt;                        //当前放电目标电压
    FLOAT fBattMaxCurrCap;                      //充放电最大电流
    WORD ucBattBalBits;                         // 电池均衡标志
    BYTE ucInputBreak;                          //充电输入断
    WORD wChargeLeftMinutes;                    //充电剩余时间
    WORD wDisChargeLeftMinutes;                 //放电剩余时间
	WORD wBattPowerOffTime;                     //停电时间

    WORD wTotalDischgCapHigh; //累计放电容量高字节
    WORD wTotalDischgCapLow;  //累计放电容量低字节
    WORD wTotalDischgQHigh;   //累计放电电量高字节
    WORD wTotalDischgQLow;    //累计放电电量低字节
    WORD wTotalChgCapHigh;    //累计充电容量高字节
    WORD wTotalChgCapLow;     //累计充电容量低字节
    WORD wTotalChgQHigh;      //累计充电电量高字节
    WORD wTotalChgQLow;       //累计充电电量低字节
    FLOAT fTotalDischargeQuanty; //累计放电电量
    FLOAT fTotalChargeQuanty;    //累计充电电量

    WORD w4GUpTrafficHigh;      //上行流量高字节
    WORD w4GUpTrafficLow;       //上行流量低字节
    WORD w4GDownTrafficHigh;    //下行流量高字节
    WORD w4GDownTrafficLow;     //下行流量低字节

    WORD wBattHighPrecSOC;    //高精度SOC(扩大100倍)
    BYTE ucMasterSta;         //主从机状态 0:主机;1从机
    BYTE ucCAN1Address;        //CAN1地址   
    BYTE ucCAN2Address;       //CAN2地址
    BYTE ucCabMasterIdent;    //柜级主机标识 1:主机;0从机
	BYTE ucDefenceStatus;	  //布防状态
    FLOAT fSetChgVolt;        //当前设定充电电压
    FLOAT fSetDischVolt;      //当前设定放电电压
    WORD wSetChgCurr;         //设定电池侧充电限电流比例
    WORD wSetBusChgCurr;      //设定Bus侧充电限电流比例
    WORD wSetDischCurr;       //设定放电限电流比例

    FLOAT fBoardTemp;         //单板温度
    BYTE ucAlarmStatus;       //告警状态
    BYTE ucThroughStatus;     //直通状态
    BYTE ucThroughChgStatus;       //充电直通状态
    BYTE ucThroughDischgStatus;    //放电直通状态
    FLOAT fPowerOnVoltThre;   //来电电压阈值
    FLOAT fPowerDownVoltThre; //掉电电压阈值
    FLOAT maxTempDiff;        //单体最大温差
    FLOAT maxVoltDiff;        //单体最大压差
    BOOLEAN bBuzzerEnable;    //蜂鸣器开关状态
    BOOLEAN bHeatpadEnable;   //加热垫开关状态
    BOOLEAN bUpgradeEnable;    //升级使能状态
    BOOLEAN bBattFull;        //电池充满电标志
    BOOLEAN bChgVolReg;       //充电调压标志
    BOOLEAN bSIMStat;         //SIM卡在位状态
    BOOLEAN bSiteAntiTheftStatus;   //站点防盗状态
    WORD wRemainCap;          //剩余容量
    WORD wBatteryCap;         //电池额定容量
    FLOAT fCellVoltMax;       //最高电芯电压
    FLOAT fCellVoltMin;       //最低电芯电压
    FLOAT fCellTempMax;       //最高电芯温度
    FLOAT fCellTempMin;       //最低电芯温度
    BYTE  ucCellMaxVoltChan;   //最高电芯电压通道
    BYTE  ucCellMinVoltChan;   //最低电芯电压通道
    BYTE  ucCellMaxTempChan;   //最高电芯温度通道
    BYTE  ucCellMinTempChan;   //最低电芯温度通道
    FLOAT fCellTempMedium;    //电芯温度中位数   
    
    FLOAT fChgOverCurrPrtRecoThre;      // 充电过流保护恢复阈值
    FLOAT fDischgOverCurrPrtRecoThre;   // 放电过流保护恢复阈值
    FLOAT fBattUnderVoltAlmRecoThre;    // 电池组欠压告警恢复阈值
    FLOAT fBattUnderVoltPrtRecoThre;    // 电池组欠压保护恢复阈值
    FLOAT fCellPoorConsistAlmRecoThre;  // 单体一致性差告警恢复阈值
    FLOAT fCellPoorConsistPrtRecoThre;  // 单体一致性差保护恢复阈值
    
    FLOAT fConnTemp;          //连接器温度
    // FLOAT fBattBalanceCurr;   // 均流电流计算值
    BYTE ucExternalPowerStatus; // 外部有电状态
    BYTE ucTransChagEn; //转充电允许
    BYTE ucMosOffStatus; // MOS关闭状态
    FLOAT fBalanceResisTemp;    // 均衡电阻温度
    FLOAT fActivatePortVol;   // 激活口电压
    BYTE ucConnTempHighPrtStatus;  //连接器温度高保护状态

    FLOAT afHeatConnTemp[Heat_Conn_TEMP_NUM_MAX];        //加热膜连接器温度
    FLOAT fHeatConnTempMax;       // 最高加热膜连接器温度
    BOOLEAN bContactorStatus;     // 母排接触器状态
    BYTE bChargeMachineTest;      // 充放电机测试模式
    BYTE bSolarMode;              // 光伏模式
    WORD wGroupMaxSoc;
    WORD wBattLeftEnergy;         //剩余能效循环
    BYTE ucPriceStatus;           //当前错峰电价状态
#ifdef PAKISTAN_CMPAK_PROTOCOL
    WORD wAccuBattRunTimeHigh; // 电池累计运行时间高字节
    WORD wAccuBattRunTimeLow;  // 电池累计运行时间低字节
    WORD wDischgTimesHigh;     // 电池放电次数高字节
    WORD wDischgTimesLow;      // 电池放电次数低字节
#endif
// R321 为兼容法电 Modbus 新增实时数据
    WORD wBattUsableCap;       // 电池可用容量:额定容量*SOH
    FLOAT fBattBusPower;       // 电池母排功率:母排电压*母排电流
    FLOAT fBattChgCurr;        // 当前充电限电流:充电限电流参数*额定容量
    FLOAT fBattMaxchgCurr;     // 最大充电限电流
    BYTE ucBattStatus;         // 电池状态（法电）: 0--充电; 1--放电; 2--idle(既不充电也不放电但是在线状态); 3--保护
    FLOAT fBattChgVolt;        // 充电电压:充电以及充满时为母排电压，其他情况为0
    WORD wBattRemaingCap;      // 电池剩余容量:SOC*额定容量
    BOOLEAN bNetAntiTheftStatus;   // 网管防盗布防状态
    BOOLEAN bNetAntiKeyMatchStatus;   // 网管电子钥匙匹配状态
    BYTE ucNetAntitheftKey[32];       // 网管防盗电子钥匙
    BYTE ucBattExistStatus;    // 电池在位状态
    BYTE bNetAntiThefChgKeySts;    // 更换网管电子钥匙执行结果
    UINT32 ulNetAntiThefGetKey1;  //网络防盗电子钥匙
    UINT32 ulNetAntiThefGetKey2;
    UINT32 ulNetAntiThefGetKey3;
    UINT32 ulNetAntiThefGetKey4;
    UINT32 ulNetAntiThefGetKey5;
    UINT32 ulNetAntiThefGetKey6;
    UINT32 ulNetAntiThefGetKey7;
    UINT32 ulNetAntiThefGetKey8;
    BYTE ucGPSSNR;                   // GPS信噪比
// R321 为兼容法电 Modbus 新增实时数据 end
}T_BCMDataStruct;

typedef struct
{
    FLOAT afCellTemp[CELL_TEMP_NUM_MAX];        //单体温度
    BOOLEAN abCellTempFault[CELL_TEMP_NUM_MAX]; //温度检测错误
} T_TemDataStruct;

/************************************************************
** 结构名: T_DetStatusStruct
** 描  述: 数据采样检测状态结构定义
** 作  者: 
** 日  期: 
** 版  本: 
** 修改记录         
** 日  期       版  本      修改人      修改摘要
** 
**************************************************************/
typedef struct
{
    BOOLEAN     bBattCurrDetError[BATT_NUM+1];  //电池电流AD检测错误
    BOOLEAN     bBattVoltDetError[BATT_NUM];    //电池电压AD检测错误
    BYTE        ucBattDischargeAbility;         //电池放电能力电池故障检测
    WORD        wBattDetTimer;                  //电池检测时间计数器
    BYTE        ucbattdet;                      //电池检测标志

    BOOLEAN     bInitBattDet;                   //首次电池检测标志
    LONG        lChargeTimer;                   //连续充电计数器
    LONG        lConChargeTimer;                //上次连续充电计数器
//
}T_DetStatusStruct;

typedef struct
{
    BYTE ucPointSaveOffset;
    BYTE ucRealAlmPointSaveOffset;
}T_PointSaveStruct;

typedef struct
{
    WORD wExpected;
    BOOLEAN bOddBit;
    BOOLEAN bCtrl;
}T_BalCtrlStruct;

typedef enum {
    CONDITION_MEDIAN_DELTA_1,  // 单体温度与有效单体温度中值偏移超过15℃（R321）或20℃（D121）
    CONDITION_MEDIAN_DELTA_2,
    CONDITION_MEDIAN_DELTA_3,
    CONDITION_MEDIAN_DELTA_4,
    CONDITION_FIXED_THRESHOLD_1, // 单体温度超过75℃
    CONDITION_FIXED_THRESHOLD_2,
    CONDITION_FIXED_THRESHOLD_3,
    CONDITION_FIXED_THRESHOLD_4,
} TempConditionType;

/***************************************************************************
 * @brief   极值结构体(R321)
 **************************************************************************/
typedef struct
{
    float fBattVoltMax;                      //电池电压最大值
    time_t BattVoltMaxTime;                 // 电池电压最大值记录时间
    float fBattVoltMin;                      //电池电压最小值
    time_t BattVoltMinTime;                 //电池电压最小值记录时间
    float fExterVoltMax;                          //外部电压最大值
    time_t ExterVoltMaxTime;                     //外部电压最大值记录时间
    float fEnvTempMax;                       //环境温度最大值
    time_t EnvTempMaxTime;                  //环境温度最大值记录时间
    float fEnvTempMin;                       //环境温度最小值
    time_t EnvTempMinTime;                  //环境温度最小值记录时间
    float fChargeBusCurrMax;                 //BUS充电电流最大值
    time_t ChargeBusCurrMaxTime;            //BUS充电电流最大值记录时间
    float fDischargeBusCurrMax;              //BUS放电电流最大值
    time_t DischargeBusCurrMaxTime;         //BUS放电电流最大值记录时间
    float fChargeBattCurrMax;                //电池充电电流最大值
    time_t ChargeBattCurrMaxTime;            //电池充电电流最大值记录时间
    float fDischargeBattCurrMax;              //电池放电电流最大值
    time_t DischargeBattCurrMaxTime;         //电池放电电流最大值记录时间
    float fCellVoltMax[CELL_VOL_NUM_MAX];    //电芯电压最大值
    time_t CellVoltMaxTime[CELL_VOL_NUM_MAX]; //电芯电压最大值记录时间
    float fCellVoltMin[CELL_VOL_NUM_MAX];      //电芯电压最小值
    time_t CellVoltMinTime[CELL_VOL_NUM_MAX]; //电芯电压最小值记录时间
    float fCellTempMax[CELL_TEMP_NUM_MAX];     //电芯温度最大值
    time_t CellTempMaxTime[CELL_TEMP_NUM_MAX];//电芯温度最大值记录时间
    float fCellTempMin[CELL_TEMP_NUM_MAX];     //电芯温度最小值
    time_t CellTempMinTime[CELL_TEMP_NUM_MAX];//电芯温度最小值记录时间
    rt_uint16_t wCheckSum;
} T_HisExtremeData;

/***************************************************************************
 * @brief   单体温升速率异常功能判断结构体
 **************************************************************************/
typedef struct
{
    FLOAT       fTempMeasureMents[CELL_TEMP_NUM][SAMPLE_TIME_SNUM];  //用于记录10s内的温度数据，以实时计算温升速率
    FLOAT       fCellTempRiseRate[CELL_TEMP_NUM]; //单体温升速率数组
    WORD        wCellTempRiseTimeCounter[CELL_TEMP_NUM];
    WORD        wCellTempRiseRecCounter;  //单体温升速率异常产生后，恢复计数判断
    BYTE        ucMeasureIndex;   //用于记录温度数组中当前温度索引位置
    BOOLEAN     bCellTempRiseGenerageJudge[CELL_TEMP_NUM]; //单体温升速率是否达到界限的标志位
    BOOLEAN     bCellTempRiseRateFault;    //温升速率异常标志位
}T_TempRiseRateStruct;

/*********************  函数原型定义  **********************/
void    SampleInSys( void* parameter );
void    GetRealData(T_BCMDataStruct * pDest );
void    JudgeDataFlag( void );
void    SetRealData( T_BCMDataStruct const* pDest );
void    SetBattBalance(WORD wBalVal);
BOOLEAN IsCellEqualOn(void);
void    CheckEqualCircuitFault(void);
BYTE    GetEqualCircuitFault(void);
void    ClearBlanceCircuitStatus(void);
void    SetConnTempHighPrtStatus(BYTE ucConnTempHighPrtStatus);
BYTE    GetCircuitCellNum(void);
//void    UpdateCellCompConfig(void);
//BOOLEAN GetCellCompStatus(void);
BOOLEAN CheckDataIsExtreme(void);
void    GetExtremeData(T_HisExtremeData * pDest );
void    SetExtremeData(T_HisExtremeData const* pDest);
BOOLEAN GetExtremeFlag();
void SetExtremeFlag(BOOLEAN InputFlag);
BOOLEAN GetNormalBalanceCircCheckFlag(void);
BOOLEAN SetNormalBalanceCircCheckFlag(BOOLEAN Param);

BYTE GetFlagSample(void);
uint8_t Get_SampleChipType(void);
BYTE GetMinvolNo(void);
BOOLEAN Get_IC_Flag(void);
void Open_IC_Onece(BOOLEAN bFlag);
WORD GetBalaneceCode(void);
BOOLEAN InitTempRiseRateData(void);       //单体温升速率异常功能初始化函数
BOOLEAN GetTempRiseRateFault(void);       //获取单体温升速率异常功能标志位
BOOLEAN CellTempRiseAbnormalEnter(void);  //单体温升速率异常功能判断入口函数

BYTE GetAdcFireControlStatus(void);
BYTE GetFireControlFaultStatus(void);
float GetFireControlAdcVol(void);
#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // SOFTWARE_SRC_APP_SAMPLE_H_;
