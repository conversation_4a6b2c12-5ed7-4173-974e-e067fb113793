#include <stdio.h>
#include <rtthread.h>
#include "utils_data_transmission.h"
#include "north_main.h"
#include "sample.h"
#include "utils_server.h"
#include "server_id.h"
#include "eev_ctrl.h"
#include "storage.h"
#include "partition_table.h"
#include "unified_id_interface.h"
#include "realdata_save.h"
#include "dev_north_dxcb_modbus.h"
#include "bottom_comm_update_cmd.h"
#include "bottom_comm_update_handle.h"
#include "software_version.h"
#include "south_manage.h"
#include "south_dev_common.h"
#include "para_manage.h"
#include "sys_para_in.h"
#include "alarm_id_in.h"
#include "alarm_para_in.h"
#include "product_main.h"
#include "alarm_mgr_api.h"
#include "alarm_register.h"
#include "led.h"
#include "io_ctrl_api.h"
#include "msg_id.h"
#include "pin_define.h"
#include "para_exclude_id.h"

static char s_north_thread_stack[2048];
static char s_sample_thread_stack[4096];
static char s_south_thread_stack[2048];
static char s_south2_thread_stack[2048];
static char s_eev_ctrl_thread_stack[1024];
static char s_product_main_thread_stack[2048];
static char s_alarm_thread_stack[2048];
static rt_device_t wdg_dev;


static server_info_t g_server_group[] = {
    /*   服务ID              服务名字            栈大小                             栈的起始地址                    优先级*/
    {{{NORTH_SERVER_ID,      "north",        sizeof(s_north_thread_stack),        s_north_thread_stack,        9}}, init_north,        north_comm_th},
    {{{SAMPLE_SERVER_ID,     "sample",       sizeof(s_sample_thread_stack),       s_sample_thread_stack,       10}}, sample_init_sys,   sample_main},
    {{{SOUTH_SERVER_ID,      "south_fan",    sizeof(s_south_thread_stack),        s_south_thread_stack,        10}}, init_south_comm,   south_comm_thread},
    {{{SOUTH2_SERVER_ID,     "south_vfd",    sizeof(s_south2_thread_stack),       s_south2_thread_stack,       10}}, init_south2_comm,  south2_comm_thread},
    {{{EEV_CONTROL_ID,       "eev_ctrl",     sizeof(s_eev_ctrl_thread_stack),     s_eev_ctrl_thread_stack,     11}}, init_eev_ctrl,     eev_ctrl_main},
    {{{PRODUCT_MAIN_ID,      "product_main", sizeof(s_product_main_thread_stack), s_product_main_thread_stack, 11}}, init_product_main, product_main},
    {{{ALARM_SERVER_ID,      "alarm",        sizeof(s_alarm_thread_stack),        s_alarm_thread_stack,        10}}, init_alarm_manage, alarm_main},
};



rt_uint8_t mempool_32[BLOCK_COUNT_32 * BLOCK_SIZE_32] ;
rt_uint8_t mempool_64[BLOCK_COUNT_64 * BLOCK_SIZE_64] ;
rt_uint8_t mempool_512[BLOCK_COUNT_512 * BLOCK_SIZE_512] ;
softbus_mempool_all_t softbus_mempool_info;

static void init_softbus_config(void)
{
    softbus_mempool_info.mempool_32.mempool_addr = &mempool_32[0];
    softbus_mempool_info.mempool_32.mempool_size = sizeof(mempool_32);
    softbus_mempool_info.mempool_32.block_size = BLOCK_SIZE_32;

    softbus_mempool_info.mempool_64.mempool_addr = &mempool_64[0];
    softbus_mempool_info.mempool_64.mempool_size = sizeof(mempool_64);
    softbus_mempool_info.mempool_64.block_size = BLOCK_SIZE_64;

    softbus_mempool_info.mempool_512.mempool_addr = &mempool_512[0];
    softbus_mempool_info.mempool_512.mempool_size = sizeof(mempool_512);
    softbus_mempool_info.mempool_512.block_size = BLOCK_SIZE_512;
    return ;
}

// 参数的注册
static para_manage_info_t dxcb_para_manage = {
    SYS_NUMERIC_PARA_MAX_OFFSET,
    SYS_STRING_PARA_MAX_OFFSET,
    ALARM_ID_OFFSET_MAX,
    numeric_para_attr_tab,
    scope_tab,
    constraint_tab,
    numeric_val_tab,
    string_para_attr_tab,
    string_val_tab,
    alarm_attr_tab,
    alarm_attr_val_tab,
};

static const data_access_interface_t id_base_interface = {
    .get_data = unified_get_data,
    .set_data = unified_set_data,    
    .linear_search = linear_search_id_index
};

// 链路层
static link_inst_t link_inst_tab[] = {
    {LINK_NORTH_DXCB,   LINK_COM, NORTH_EDU_UART_NAME},    // 北向485
    {LINK_SOUTH_DXCB1,  LINK_COM, SOUTH_FAN_UART_NAME},    // 南向-风机
    {LINK_SOUTH_DXCB2,  LINK_COM, SOUTH_VFD_UART_NAME},    // 南向-变频器
};

static link_type_t link_type_tab[] = {
    {LINK_COM, s485_dev_init, com_dev_read, com_dev_write, com_dev_set},
};

// 协议层
static protocol_process_t protocol_process_tab[] = {
    {PROTOCOL_MODBUS_RTU, modbus_pack, modbus_parse},
    {PROTOCOL_BOTTOM_COMM, bottom_pack, bottom_parse},
};

// 解析层
static dev_init_t dev_init_tab[] = {
    {DEV_NORTH_DXCB_MODBUS,  init_dev_north_dxcb,               NULL},
    {DEV_BOTTOM_COMM_UPDATE, init_bottom_comm_update_dev_type,  NULL},
    {DEV_SOUTH_MODBUS, init_fan_south, NULL},
    {DEV_SOUTH2_MODBUS, init_vfd_south, NULL},
};

static exclude_para_info_t exclude_para_info = 
{
    .sid = exclude_sids,
    .sid_num = sizeof(exclude_sids) / sizeof(unsigned short),
};

// 灯控制
static io_item_info_t run_led_info = {OUTPUT_LED, 1, PIN_LED_CONTROL, LED_CTRL_MSG};

// 支持下载的文件
transfer_file_name_info_t S_download_file_name[] = {{0, "DXCB_APP.bin", ONCHIP_DOWNLOAD, ONCHIP_DOWNLOAD_SIZE}};


int init_factory_info()
{
    boot_info_t boot_info = {0};
    if(handle_storage(read_opr, ONCHIP_BOOT_VERSION, (unsigned char*)&boot_info, sizeof(boot_info_t), 0) != SUCCESSFUL)
    {
        return FAILURE;
    }
    set_one_data(DXCB_DATA_ID_BOOT_SOFTWARE_VERSION, boot_info.ver);
    set_one_data(DXCB_DATA_ID_BOOT_SOFTWARE_DATE, boot_info.date);
    set_one_data(DXCB_DATA_ID_SOFTWARE_NAME, "DXCB01");
    set_one_data(DXCB_DATA_ID_SOFTWARE_VERSION, DXCB_COMPILE_VERSION);
    set_one_data(DXCB_DATA_ID_SOFTWARE_DATE, DXCB_COMPILE_VERSION_DATE);
    return SUCCESSFUL;
}



static void feed_dog(void)
{
    rt_device_control(wdg_dev, RT_DEVICE_CTRL_WDT_KEEPALIVE, NULL);
    return;
}

int init_watchdog(void)
{
    wdg_dev = rt_device_find("wdt");
    if (RT_NULL == wdg_dev)
    {
        return -RT_ERROR;
    }
    if (RT_EOK != rt_device_control(wdg_dev, RT_DEVICE_CTRL_WDT_START, RT_NULL))
    {
        return -RT_ERROR;
    }
    rt_thread_idle_sethook(feed_dog);

    return RT_EOK;
}




int sys_run_result()
{
    bottom_update_manage_t update_info = {0};
    if(handle_storage(read_opr, ONCHIP_UPDATE_INFO, (unsigned char*)&update_info, sizeof(bottom_update_manage_t), 0) != SUCCESSFUL)
    {
        return FAILURE;
    }

    update_info.count = 0;
    update_info.sys_run_flag = TRUE;
    if(write_update_info(&update_info) != SUCCESSFUL)
    {
        return FAILURE;
    }
    return  SUCCESSFUL;
}

int main(void){
    init_watchdog();
    init_crc();
    init_softbus_config();
    register_send_msg_to_led_f(send_led_ctrl_msg);
    softbus_init(&softbus_mempool_info);
    if (SUCCESSFUL != storage_init(&part_tab[0], sizeof(part_tab)/sizeof(part_info_t))) {
        return FAILURE;
    }
    // 初始化ID化空间
    register_para_manage_info(&dxcb_para_manage);
    register_dxcb_alarm();
    int rtn = init_para_manage();
    if(rtn != SUCCESSFUL)
    {
        LOG_E("%s | %d | init_para_manage fail\n" , __FUNCTION__ , __LINE__);
        return FAILURE;
    }
    register_exclude_para(EXCLUDE_TYPE_NONE, &exclude_para_info);
    init_real_data_memory();
    register_link_inst_tab_info(link_inst_tab, sizeof(link_inst_tab)/sizeof(link_inst_tab[0]));
    register_link_type_info(link_type_tab, sizeof(link_type_tab) / sizeof(link_type_t));
    // 注册 协议所使用的函数接口
    register_protocol_process_info(protocol_process_tab, sizeof(protocol_process_tab) / sizeof(protocol_process_t));
    register_data_access_interface(&id_base_interface);
    // 注册协议
    init_dev_tab(dev_init_tab, sizeof(dev_init_tab)/sizeof(dev_init_tab[0]));
    register_download_file_name(S_download_file_name, sizeof(S_download_file_name) / sizeof(transfer_file_name_info_t), ONCHIP_UPDATE_INFO);
    create_server(&g_server_group[0], sizeof(g_server_group)/sizeof(g_server_group[0]));
    init_factory_info();
    deal_para_crc();
    register_io_item_info(&run_led_info);
    if(sys_run_result() != SUCCESSFUL)
    {
        LOG_E("%s | %d | sys_run_result fail\n" , __FUNCTION__ , __LINE__);
        return FAILURE;
    }
    send_led_ctrl_msg(LED_NORMAL);
    return 0;
}








