/**************************************************************************
* Copyright (C) 2010-2011, ZTE Corporation.
* 版权信息：（C）2013，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：ZXDUx8 CTL板软件
* 文件名称：PrtclDL.c
* 文件说明：底层通信协议模块
* 作    者  ：yl
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/
#include "common.h"
#include "fileSys.h"
#include "CommCan.h"
#include "sample.h"
#include "realAlarm.h"
#include "battery.h"
#include "para.h"
#include "comm.h"
#include "protocol.h"
#include "stdio.h"
#include "prtclDL.h"
#include "flash.h"
#include "hisdata.h"
#include <rtdevice.h>
#include "usart.h"
#include "bspconfig.h"
#include "utils_rtthread_security_func.h"
#include "utils_frame_loss_statistic.h"
#include "circle_buff.h"
#include "fal.h"
#include "spi_flash.h"
#include "sfud.h"
#include "sfud_def.h"
#include "update_handle.h"
#include "pdt_version.h"

#ifdef DEVICE_USING_R321
#include "lwip/netifapi.h"
#include "netdev.h"
#include "download_tcp.h"
#include "SingleButton.h"
#include "led.h"
#include "CommCan2.h"
#endif

#define THREAD_PRIORITY_UPDATE 15
#define THREAD_TIMESLICE_UPDATE 5
#define NORMAL_SW_START 5
#define DIFF_TAG "diff"
#define RETRANS_MAX_COUNT 20

#define PARALLE_UPDATE_DEBUG    0    // 并发升级调试宏
#define PARALLE_TIMEOUT_TICKS   1800000    ///< 并发升级超时时间数，unit tick, 30分钟
#define LOSS_FRAME_COUNT_THRESHOLD 20

Static BYTE s_ucTriggerRepeat = 0;
Static BYTE s_ucTriggerTimeOut = 0;
//Static BYTE s_ucUpdateConfirmRepeat = 0;
Static T_PrtclDLLinkType s_bPrtclDLLinkType = DL_LINK_TYPE_NONE;
Static BOOLEAN s_bDlDataNewCAN = False;
Static BOOLEAN s_bDlDataNew485 = False;
Static BOOLEAN s_bReceivedFinished = False;  // 接收完成

Static T_PrtclDLStruct s_tPrtclDLRcv;
Static T_PrtclDLStruct s_tPrtclDLSnd;
Static T_FileManageStruct s_tFileManage;
UNUSED static BOOLEAN s_bSwUpgrade = False;  // TODO: 仅供R321使用，后续应分离出去
struct fal_partition *s_tDiffPatchPartDev = NULL;
Static WORD s_lastRecivedFrmNO = INVALID_FRM_NO;  ///< 上次接收的帧号
Static WORD s_wRequestFrmNO = 0;  ///< 请求的帧号,点对点
Static T_AppDataSaveStruct s_tDataToCircleBuff = {0};
Static T_AppDataSaveStruct s_tCircleBuffToData = {0}; //从缓存区读数据
Static T_ParalleDLStat s_tParalleDLStat = PARALLE_DL_STAT_PREPARE;
Static WORD s_wLossFrameNo = 0;
static rt_tick_t s_TickStartDownload = 0;
Static WORD s_wComplementaryRepeat = 0;
Static BYTE s_ucRepeatSendFrameCnt = 0;
Static BOOLEAN s_bIsDLCommTrans = False;

Static int s_onSlavecount = 0;
Static WORD s_wLostFrameNum = 0;// 补包时记录的补包帧数，用于补包过程中从机未回包时的重发。
Static BYTE s_ucAgentUpdatePollingAddr = 1;// 代理升级轮询地址
Static BYTE s_ucSupplyFrameRepeatCnt = 0;// （代理升级）升级主机记录补包重试次数
Static T_CAN_BMSUpdateScanResult s_tBMSUpdateScanResult;// 从机设备状态
Static T_AgentParalleDLStat s_tAgentParalleDLStat = AGENT_DL_STAT_PREPARE;//代理升级状态机
Static rt_timer_t s_AgentAckOverTimeTimer = NULL;// (代理升级)回包超时计数器
Static BOOLEAN s_bAgentUpdateFlag = False;       //代理从机升级开始升级的标志位
Static BOOLEAN s_bAgentP2PAckFlag = False;  //代理升级点对点回包标志，为True时表示要开启定时器。
Static WORD s_wFrameLossCountThresh = 0;


Static BYTE s_ucBDUDLStat = AGENT_DL_STAT_PREPARE;
Static BOOLEAN s_bConfimeFlag = False;
Static BOOLEAN s_bTrigerFlag = False;


//下载文件名
//const BYTE s_ucFileName[FILE_NAME_LEN] = "bms_dualcore_app.hex";
#ifdef DEVICE_USING_R321
const CHAR s_ucBootFile[FILE_NAME_LEN] = "bms_boot230321.bin";
const CHAR s_ucFileNameBin[FILE_NAME_LEN] = "bms_mcu_app230321.bin";
const CHAR s_ucFileBDUNameBin[FILE_NAME_LEN] = "ZXDUPAV5.0BDCUR321230512.BIN";
#else
const CHAR s_ucBootFile[FILE_NAME_LEN] = "bms_boot.bin";
const CHAR s_ucFileNameBin[FILE_NAME_LEN] = "bms_mcu_app230215.bin";
const CHAR s_ucFileBDUNameBin[FILE_NAME_LEN] = "ZXDUPAV5.0BDCUR321230512.BIN";
BYTE GetUpgradeFileFlag(void)
{
    return 0;
}
#endif
BYTE s_aucFileName[FILE_NAME_LEN] = "";
static struct rt_timer s_ParalleOvertimeTimer;   ///< 并发升级超时退出升级定时器


static BYTE s_bUpdateDataBuff[300];
Static T_PrtclCommRecStruct s_tDecodeFrame;        // 串口解码后的一帧数据

static void SendLinkRTN(T_CommStruct *ptComm, BYTE ucRTN);
static void CovertDataToSend(T_CommStruct *ptComm);
static void BMUSendTrigger(T_CommStruct *ptComm);
Static BYTE GetAPPDownLoadRTN(T_CommStruct *ptComm);
static void DealBMSTrigger(T_CommStruct *ptComm);
static void RecAppDownloadRTNFault(void);
Static SHORT ReplyParalleUpdateConfirmFrm(T_CommStruct *ptComm);
static void PackAndSend(T_CommStruct *ptComm);
static void DealBMSSwapBaud(T_CommStruct *ptComm);
static void	SwapBaudrate(void);
Static BYTE GetAPPCommonRTN(T_CommStruct *ptComm, WORD wFrameLength);
Static void DealBMSParalleTrigger(T_CommStruct *ptComm);
Static void BMSSendParalleTrigger(T_CommStruct *ptComm);
Static short BMSSendParalleTriggerTransProcess(T_CommStruct *ptComm);
Static short BMSSendParalleUpdateConfirmTransProcess(T_CommStruct *ptComm);
Static short ParalleUpdateConfirmProcess(T_CommStruct *ptComm);
Static short  UpdateConfirmTransHandleSlaveProcess(T_CommStruct *ptComm);
Static short BMSSendParalleUpdateConfirm(T_CommStruct *ptComm);
Static short BMSParalleCtrlBduCanComm(T_CommStruct *ptComm, BYTE ctrl);
Static int CheckFirstFrame(void);
Static short DealBMSParalleTransferFrm(T_CommStruct* ptComm);
Static short DealBMSParalleFirstFrm(T_CommStruct* ptComm);
Static short DealBMSParalleDataFrm(T_CommStruct* ptComm);
Static short DealBMSParalleAppDownloadRtnFault(T_CommStruct* ptComm);
Static short RecordBMSParalleFirstFrmLoss(T_CommStruct* ptComm);
Static short RecordBMSParalleDataFrmLoss(T_CommStruct* ptComm);
Static short RecordBMSParalleFrmLossWhenConfirm(T_CommStruct* ptComm);
Static BYTE DealBMSParalleDataConfimFrm(T_CommStruct *ptComm);
Static BYTE BMSSendParalleDataConfimFrm(T_CommStruct *ptComm);
Static BYTE ComplementaryFrameAck(void);
Static BOOLEAN CompletePackageCrcCheckRes();
Static void DealParalleTimeout(void);
Static T_ParalleTransferMode GetParalleTransferMode(void);
Static short StartParalleTimeoutTimer(rt_timer_t pTimer);
Static WORD TransCodingFunc(BYTE *src, WORD src_lenth, BYTE *dest, WORD dest_lenth, WORD lenth);
Static SHORT DealSlaveTransferFrmAck(T_CommStruct *ptComm);
Static BOOLEAN DealDLData(T_CommStruct *ptComm);
Static void  BMSSendBDUParalleTrigger(T_CommStruct *ptComm);
Static BYTE CheckBMSSendParalleDataConfimFrmLostThresh(WORD wFrameLossCount);

/*--------并发升级主机作为代理升级从机接口----------*/
#define READ_NORFLASH_LEN 256
Static INT32S AgentUpdateMain(T_CommStruct *ptComm);
Static short AgentParalleFirstFrmTransFrm(INT16U *pOutLen, INT8U *pOutData, BYTE ucDestDevAdr);//首发帧
Static short AgentParalleDataTransFrm(INT16U *pOutLen, INT8U *pOutData, WORD wFrameNum, BYTE ucDestDevAdr);//传输帧
Static short AgentParalleDataTransConfirmFrm(INT16U *pOutLen, INT8U *pOutData, BYTE ucDestDevAdr);//传输确认帧
Static BYTE AgentBMSParalleDataConfimRtnFrm(T_CommStruct *ptComm);//从机返回数据传输确认帧
Static short AgentParalleUpdateConfirmFrm(INT16U *pOutLen, INT8U *pOutData, BYTE ucDestDevAdr);//更新确认帧（升级确认帧）
Static short  SendDataFrame(T_CommStruct *ptComm);
Static short SendSupplyProcess(T_CommStruct *ptComm);
Static short SendSupplyDataFrame(T_CommStruct *ptComm, WORD wLostFrameNum, BYTE ucDestDevAddr, E_DATA_SUPPLY_TYPE supplyType);
Static short  SendConfirmRepeatProcess(T_CommStruct *ptComm);
Static short  SendConfirmFrame(T_CommStruct *ptComm);
Static rt_int32_t PackagingFrameData(T_CommStruct *ptComm, INT16U pOutLen,INT8U *pOutData);
Static short AgentDownloadPrepare(T_CommStruct *ptComm);
Static short TrigRepeatProcess(T_CommStruct *ptComm);
Static short SendTrigerFrame(T_CommStruct *ptComm);
Static short SendTrigerFrameAck(T_CommStruct *ptComm);
Static short TriggerNextSlave(T_CommStruct *ptComm);
Static short TriggerNextSlaveAck(T_CommStruct *ptComm);
Static short SendBroadcastDataFrame(T_CommStruct *ptComm);
Static BYTE AgentGetAPPCommonRTN(T_CommStruct *ptComm, WORD wFrameLength);
Static void ProcessTriggerFrame(T_CommStruct *ptComm);
Static void ProcessTriggerFrameAck(T_CommStruct *ptComm);
Static BYTE GetAPPDownLoadRTNAck(T_CommStruct *ptComm);
Static void ProcessUpdateConfirmFrame(T_CommStruct *ptComm);
Static void ProcessUpdateConfirmFrameAck(T_CommStruct *ptComm);
//Static short AgentParalleDataTransConfirmFrmAck(BYTE ucDestAddr);
Static void AgentAckTimeOutCallFunc(void *parameter);
Static BOOLEAN checkCommDLFrame(T_CommStruct *ptComm);
Static BOOLEAN SaveVersionAction(void);


Static void BMSRecvBDUParalleTransfrm(T_CommStruct *ptComm);                            // 其他电池BDU升级数据帧
Static void BMSRecvBDUParalleDataConfimFrm(T_CommStruct *ptComm);                       // 其他电池BDU升级确认帧
Static void DealDLDataSCI(BYTE* pucApplyData, BYTE ucLen);                              // 处理BDU升级过程
Static void BDUParalleTrigger_Self(T_CommStruct *ptComm);                               // 触发自己BDU升级触发帧
Static void ProcessTriggerFrameAck_Self(T_CommStruct *ptComm);                          // 触发自己BDU升级触发帧回包
Static void ProcessUpdateConfirmFrameAck_Self(T_CommStruct *ptComm);                    // 自己BDU升级确认帧回包
Static short BDUDownloadPrepare_Self(void);                                             // 自己BDU升级准备阶段
Static void TrigRepeatProcess_Self(void);                                               // 自己BDU升级触发阶段
Static short SendDataFrame_Self(void);                                                  // 自己BDU升级触发成功发送数据阶段
Static short SendBroadcastDataFrame_Self(void);                                         // 自己BDU升级发送数据帧
Static short SendConfirmRepeatProcess_Self(void);                                       // 自己BDU升级发送确认帧阶段
Static short SendConfirmFrame_Self(void);                                               // 自己BDU升级发送确认帧
Static short SendSupplyProcess_Self(void);                                              // 自己BDU升级发送补帧阶段
Static short SendSupplyDataFrame_Self(WORD wLostFrameNum, BYTE ucDestDevAddr, E_DATA_SUPPLY_TYPE supplyType);   // 自己BDU升级发送补帧
Static void BMSRecvBDUParalleTransfrm_Self(T_CommStruct *ptComm);                       // 自己BDU升级补帧回包处理入口
Static SHORT DealSlaveTransferFrmAck_Self(T_CommStruct* ptComm);                        // 自己BDU升级补帧回包处理
Static void BMSRecvBDUParalleTransfrm_SendBduSelf(INT16U pOutLen, INT8U *pOutData);     // 自己BDU升级数据发送
Static void BMSRecvBDUParalleDataConfimFrm_Self(T_CommStruct *ptComm);                  // 自己BDU升级确认帧回包处理入口


/*
 * @brief 数据解码
 * @param[in] pucSrc 串口数据缓冲区
 * @param[in] pucDest 经过解码的数据
 * @retval  解码后数据长度
 * @note  注解 还原转码数据
 * @par 修改日志
 */
static WORD decodeframe(BYTE *pucSrc, BYTE *pucDest)
{
    WORD wDataLength = 0;

    while ( EOI != *pucSrc && wDataLength < DLFRAM_MAX_LEN)
    {
        if ( CHANGE_CODE == *pucSrc )
        {
            pucSrc++;
            *(pucDest++) = ~(*(pucSrc++));
        }
        else
        {
            *(pucDest++) = *(pucSrc++);
        }
        wDataLength = wDataLength + 1;
    }

    return wDataLength;
}

/*
 * @brief 检查下载帧是否正确
 * @param[in] ptComm 串口数据缓冲区
 * @retval  成功或失败
 */
Static BOOLEAN checkCommDLFrame(T_CommStruct *ptComm) {
    if ( s_tDecodeFrame.ucDestDevType!=BMS_Type || (s_tDecodeFrame.ucDestDevAdr != GetBMSDLAddr() && s_tDecodeFrame.ucDestDevAdr!= 0)||
        s_tDecodeFrame.ucProtoType != 0xE1)
    {
        return FAILURE;
    }

    if ( CRC_Cal(s_tDecodeFrame.aucDataBuf, s_tDecodeFrame.wDataLength) )
    {
        //并发升级时链路层crc错误不回包。
        if (IsParallUpdate() == False) {
            SendLinkRTN(ptComm, LinkRTN_CRC);
        }
        return FAILURE;
    }
    return SUCCESSFUL;
}

static BOOLEAN parseDLFrame(T_CommStruct *ptComm)
{
    BYTE *pucSrc = &ptComm->aucRecBuf[1];

    rt_memset_s(&s_tDecodeFrame, sizeof(T_PrtclCommRecStruct), 0, sizeof(T_PrtclCommRecStruct));
    s_tDecodeFrame.wDataLength = decodeframe(pucSrc, (BYTE *)&s_tDecodeFrame.aucDataBuf);
    s_tDecodeFrame.ucDestDevType = s_tDecodeFrame.aucDataBuf[2];
    s_tDecodeFrame.ucDestDevAdr = s_tDecodeFrame.aucDataBuf[3];
    s_tDecodeFrame.ucProtoType = s_tDecodeFrame.aucDataBuf[9];

    ClearRecBuf(ptComm);
    if (s_tDecodeFrame.wDataLength >= DLFRAM_MAX_LEN)
    {
        return FAILURE;
    }

    if(checkCommDLFrame(ptComm) == FAILURE) {
        return FAILURE;
    }

    rt_memcpy_s( &s_tPrtclDLRcv.aucDataBuf[0], sizeof(s_tPrtclDLRcv.aucDataBuf), &s_tDecodeFrame.aucDataBuf[0], sizeof(s_tDecodeFrame.aucDataBuf));

    s_tPrtclDLRcv.wDataLength = s_tDecodeFrame.wDataLength;
    s_tPrtclDLRcv.ucSrcDevType = s_tPrtclDLRcv.aucDataBuf[0];
    s_tPrtclDLRcv.ucSrcDevAdr = s_tPrtclDLRcv.aucDataBuf[1];
    s_tPrtclDLRcv.ucDestDevType = s_tPrtclDLRcv.aucDataBuf[2];
    s_tPrtclDLRcv.ucDestDevAdr = s_tPrtclDLRcv.aucDataBuf[3];
    s_tPrtclDLRcv.wApplyLength = (s_tPrtclDLRcv.aucDataBuf[7]<<8) + s_tPrtclDLRcv.aucDataBuf[8];
    s_tPrtclDLRcv.ucProtoType = s_tPrtclDLRcv.aucDataBuf[9];
    s_tPrtclDLRcv.ucApplyFun = s_tPrtclDLRcv.aucDataBuf[12];
    s_tPrtclDLRcv.wApplyAppEnd = (s_tPrtclDLRcv.aucDataBuf[13]<<8) + s_tPrtclDLRcv.aucDataBuf[14];
    s_tPrtclDLRcv.ucRsvDestDevType = s_tPrtclDLRcv.aucDataBuf[16];
    s_tPrtclDLRcv.ucRsvDestDevAdr = s_tPrtclDLRcv.aucDataBuf[17];
    s_tPrtclDLRcv.ucRsvTrans = s_tPrtclDLRcv.aucDataBuf[18];

    s_tPrtclDLRcv.wApplyRecvDataLength = (s_tPrtclDLRcv.aucDataBuf[19]<<8) + s_tPrtclDLRcv.aucDataBuf[20];

    if ((s_tPrtclDLRcv.wApplyLength + 11) != s_tPrtclDLRcv.wDataLength)
    {
        return FAILURE;
    }
    rt_memcpy_s( s_tPrtclDLRcv.aucApplyDataBuf, sizeof(s_tPrtclDLRcv.aucApplyDataBuf), &s_tPrtclDLRcv.aucDataBuf[9], s_tPrtclDLRcv.wApplyLength );

    return SUCCESSFUL;
}

static void sendDLFrame(T_CommStruct *ptComm)
{
    BYTE i = 0;
    BYTE *pucDest;
    BYTE *pucSrc = s_tPrtclDLSnd.aucDataBuf;
    WORD wLength = 0;

    pucDest = ptComm->aucSendBuf;

    *(pucDest++) = SOI;
    wLength++;
    while ( i++ < s_tPrtclDLSnd.wDataLength)
    {
        if ( CHANGE_CODE==*pucSrc || SOI==*pucSrc || EOI==*pucSrc )
        {
            *(pucDest++) = CHANGE_CODE;
            *(pucDest++) = ~(*(pucSrc++));
            wLength += 2;
        }
        else
        {
            *(pucDest++) = *(pucSrc++);
            wLength++;
        }
    }
    *(pucDest++) = EOI;
    wLength++;
    ptComm->wSendLength = wLength;
    if(COMM_CAN != ptComm->ucPortType)
    {
        sendCommData( ptComm );
    }

    return;
}

static void fillDLData(void)
{
    WORD wCrc;
    BYTE *p = s_tPrtclDLSnd.aucDataBuf;

    p[0] = BMS_Type;
    p[1] = GetBMSDLAddr();
    p[2] = s_tPrtclDLRcv.ucSrcDevType;
    p[3] = s_tPrtclDLRcv.ucSrcDevAdr;
    p[4] = 0x80;
    p[5] = s_tPrtclDLSnd.ucRTN;
    p[6] = 0;
    p[7] = s_tPrtclDLSnd.wApplyLength / 256;
    p[8] = s_tPrtclDLSnd.wApplyLength % 256;

    if (s_tPrtclDLSnd.wApplyLength>0)
    {
        rt_memcpy(&p[9], s_tPrtclDLSnd.aucApplyDataBuf, s_tPrtclDLSnd.wApplyLength);
    }
    s_tPrtclDLSnd.wDataLength = s_tPrtclDLSnd.wApplyLength + 11;
    wCrc = CRC_Cal( p, s_tPrtclDLSnd.wDataLength - 2 );

    p[s_tPrtclDLSnd.wDataLength - 2] = wCrc%256;
    p[s_tPrtclDLSnd.wDataLength - 1] = wCrc/256;

    return;
}


BOOLEAN sendDlRepData(BYTE* pucApplyData, BYTE ucLen)
{
    T_CommStruct tCommStruct;

    RETURN_VAL_IF_FAIL(pucApplyData != NULL, False);

    rt_memset(&tCommStruct, 0, sizeof(T_CommStruct));
    tCommStruct.ucPortType = COMM_RS485;

    s_tPrtclDLSnd.wApplyLength = ucLen;
    s_tPrtclDLSnd.ucRTN = 0;

    switch(s_bPrtclDLLinkType) {
        case DL_LINK_TYPE_CAN1:
            if (!IsMaster() && pucApplyData[DOWNLOAD_RSV_TRANS_INDEX] == DOWNLOAD_TRANS) {
                RETURN_VAL_IF_FAIL(pucApplyData[DOWNLOAD_RSV_DEST_DEV_TYPE_INDEX] == DEV_TYPE_BMS, False);
                RETURN_VAL_IF_FAIL(pucApplyData[DOWNLOAD_FUNC_INDEX] != FUNC_CODE_PARALLE_FRONT_TRIGGER_FRM, True);
                ChangeIntoCanFrm(pucApplyData, s_tPrtclDLSnd.wApplyLength, DEV_TYPE_BMS, GetMasterAddr());
            } else if (s_tPrtclDLRcv.ucSrcDevType == DEV_TYPE_BMS && pucApplyData[DOWNLOAD_RSV_TRANS_INDEX] == 0x00) {
                if (pucApplyData[DOWNLOAD_FUNC_INDEX] == FUNC_CODE_PARALLE_FRONT_TRIGGER_FRM) {
                    SetBduParallUpdateFlag(True);
                }
                RETURN_VAL_IF_FAIL(pucApplyData[DOWNLOAD_RSV_DEST_DEV_TYPE_INDEX] == DEV_TYPE_BMS, False);
                if (pucApplyData[DOWNLOAD_FUNC_INDEX] == FUNC_CODE_PARALLE_FRONT_TRANSFER_FRM && s_tPrtclDLRcv.ucRsvDestDevAdr == 0x00) {
                    return True;
                }
                ChangeIntoCanFrm(pucApplyData, s_tPrtclDLSnd.wApplyLength, DEV_TYPE_BMS, s_tPrtclDLRcv.ucSrcDevAdr);
            } else {
                ChangeIntoCanFrm(pucApplyData, s_tPrtclDLSnd.wApplyLength, DEV_TYPE_SC, DEV_ADDR_NORTH);
            }
            break;
        #ifdef USE_CAN2
        case DL_LINK_TYPE_CAN2:
            ChangeIntoCan2Frm(pucApplyData, s_tPrtclDLSnd.wApplyLength, DEV_TYPE_SC, DEV_ADDR_NORTH);
            break;
        #endif
        case DL_LINK_TYPE_COMM:
            rt_memcpy( s_tPrtclDLSnd.aucApplyDataBuf, pucApplyData, s_tPrtclDLSnd.wApplyLength );
            fillDLData();
            sendDLFrame(&tCommStruct);
            break;
        case DL_LINK_TYPE_SCI:
            DealDLDataSCI(pucApplyData, ucLen);
            break;
        default:
            break;
    }
    return True;
}


//转码函数封装
Static WORD TransCodingFunc(BYTE *src, WORD src_lenth, BYTE *dest, WORD dest_lenth, WORD lenth)
{
    RETURN_VAL_IF_FAIL(src != NULL, False);
    RETURN_VAL_IF_FAIL(dest != NULL, False);
    RETURN_VAL_IF_FAIL(src_lenth >= lenth, False);
    RETURN_VAL_IF_FAIL(dest_lenth >= lenth, False);
    WORD i = 0;
    WORD j = 0;
    for (i = 0; i < lenth; i++)
    {
        if ((src[i] == EOI) && (i != (lenth - 1)))
        {
            dest[j] = CHANGE_CODE;
            dest[j + 1] = 0xF2;
            j += 2;
        }
        else if (src[i] == CHANGE_CODE)
        {
            dest[j] = CHANGE_CODE;
            dest[j + 1] = 0x82;
            j += 2;
        }
        else if ((src[i] == SOI) && (i != 0))
        {
            dest[j] = CHANGE_CODE;
            dest[j + 1] = 0x81;
            j += 2;
        }
        else
        {
            dest[j] = src[i];
            j += 1;
        }
    }

    return j;
}

// 发送串口透传回包
BOOLEAN SendCommTransFrm(T_CommStruct *ptComm)
{
    RETURN_VAL_IF_FAIL(ptComm != NULL, False);

    WORD    wCRCTemp, wDataLength, wApplyRecvDataLength, wApplyLength;
    BYTE    i = 0;
    BYTE    aucTmp[256];

    wApplyRecvDataLength = (ptComm->aucRecBuf[10]<<8) + ptComm->aucRecBuf[11];
    wApplyLength = wApplyRecvDataLength + 14;
    aucTmp[0]   = SOI;
    aucTmp[1]   = BMS_Type;
    aucTmp[2]   = GetBMSDLAddr();
    aucTmp[3]   = s_tPrtclDLRcv.ucSrcDevType;
    aucTmp[4]   = s_tPrtclDLRcv.ucSrcDevAdr;
    aucTmp[5]   = 0x80;
    aucTmp[6]   = 0x00;
    aucTmp[7]   = 0x00;
    aucTmp[8]   = (wApplyLength >> 0x08) & 0xff;
    aucTmp[9]   = wApplyLength & 0xff;

    rt_memcpy_s(&aucTmp[10], wApplyLength, ptComm->aucRecBuf, wApplyLength);

    i = 10 + wApplyLength;
    wCRCTemp = CRC_Cal(&aucTmp[1], i - 1);
    aucTmp[i++] = wCRCTemp & 0xff;
    aucTmp[i++] = (wCRCTemp >> 8) & 0xff;
    aucTmp[i] = EOI;
    wDataLength = i + 1; //换码前的数据总长度

    ptComm->wSendLength = TransCodingFunc(aucTmp, 256, ptComm->aucSendBuf, 512, wDataLength);

    sendCommData( ptComm );
    return True;
}

static void SendLinkRTN(T_CommStruct *ptComm, BYTE ucRTN)
{
    s_tPrtclDLSnd.wApplyLength = 0;
    s_tPrtclDLSnd.ucRTN = ucRTN;

    fillDLData();
    sendDLFrame(ptComm);

    return;
}

char *GetRcvFileName(void)
{
    return s_tFileManage.tFileAttr.acFileName;
}

static BYTE	DealDownload( void )
{
    T_PrtclDLStruct	*pApp = &s_tPrtclDLRcv;
    uint8_t	*p = &(pApp->aucApplyDataBuf[12]);
    uint32_t offset = 0; 
    
 
    const struct fal_flash_dev *flash_dev = NULL;  
    flash_dev = fal_flash_device_find("gd32_onchip");
    if (RT_NULL == flash_dev)
    {
        return False;
    }

    if ( pApp->wApplyAppEnd == 0 )
    {
        s_tFileManage.wCounter = 0;
        if ( pApp->wApplyRecvDataLength < 72 )
        {
            pApp->wApplyAppEnd = 0;
            pApp->ucRTN = DownloadFrameErr;
            return 0;
        }

        if ( GetInt16Data(p) != MSC_MAX_PACKET )
        {
            pApp->wApplyAppEnd = 0;
            pApp->ucRTN = DownloadFrameErr;
            return 0;
        }
        s_tFileManage.tFileAttr.wTotalFrameNum = GetInt16Data(p+2);
    
        flash_dev->ops.erase(ADDR_FLASH_SECTOR_0_BANK1-GD32F4_FLASH_BASE,APPLICATION_ADDRESS-GD32F4_FLASH_BASE);

        pApp->ucRTN = 0x00;
    }
    else if ( pApp->wApplyAppEnd == (s_tFileManage.wCounter+1) && pApp->wApplyAppEnd <= MAX_PACKET_NUM)
    {
        offset = ADDR_FLASH_SECTOR_0_BANK1 + s_tFileManage.wCounter*256;  
       
        if ( 0 == flash_dev->ops.write((offset -GD32F4_FLASH_BASE) ,p, MSC_MAX_PACKET) )
        {
            pApp->ucRTN = DownloadFrameErr;//写入失败则请求帧号wCounter还保留原来的，重发机制
            return 0;
        }
        pApp->ucRTN = 0x00;
        s_tFileManage.wCounter = pApp->wApplyAppEnd;
        if ( s_tFileManage.wCounter >= s_tFileManage.tFileAttr.wTotalFrameNum-1 )
        {
            s_tFileManage.ucFlag = FLAG_APP;
        }
    }
    else
    {
        pApp->ucRTN = DownloadFrameErr;
    }
    pApp->wApplyAppEnd = s_tFileManage.wCounter+1;
    pApp->wDataLength = 0;

    return 0;
}

Static BYTE CheckBMSSendParalleDataConfimFrmLostThresh(WORD wFrameLossCount){
    WORD wFrameLossCountThresh = 0;
    
    wFrameLossCountThresh = (s_tPrtclDLRcv.aucApplyDataBuf[12] << 8) | s_tPrtclDLRcv.aucApplyDataBuf[13];
    if(wFrameLossCount > wFrameLossCountThresh){
        DealParalleTimeout();
    }
    return SUCCESSFUL;
}

// 并发升级从机对主机发送的数据确认帧的回包
Static BYTE BMSSendParalleDataConfimFrm(T_CommStruct *ptComm) {
    WORD wFrameLossCount = 0;

    RETURN_VAL_IF_FAIL(ptComm != NULL, FAILURE);

    s_tParalleDLStat = PARALLE_DL_STAT_DATA_CONFIRM;
    RecordBMSParalleFrmLossWhenConfirm(ptComm);  // 进入确认阶段，对尾部丢帧进行记录

    s_wRequestFrmNO = 0;    // 进入确认阶段，清除请求帧号
    s_wLossFrameNo = 0;
    wFrameLossCount = get_frame_loss_count();
    if (wFrameLossCount == 0) {
        s_tPrtclDLSnd.aucApplyDataBuf[0] = FRAME_RECEIVE_COMPLETE;
        s_tParalleDLStat = PARALLE_DL_STAT_UPDATE_CONFIRM;
        #if PARALLE_UPDATE_DEBUG
        rt_kprintf("回复确认帧，接收完整\n");
        #endif
    } else {
        s_wLossFrameNo = get_first_frame_loss();
        s_tPrtclDLSnd.aucApplyDataBuf[0] = FRAME_RECEIVE_INCOMPLETE;
        #if PARALLE_UPDATE_DEBUG
        rt_kprintf("回复确认帧，丢失第 %d 帧\n", s_wLossFrameNo);
        #endif
    }
    CheckBMSSendParalleDataConfimFrmLostThresh(wFrameLossCount); //依据升级确认帧的补帧总数和补帧阈值的关系判断是否退出升级
    s_tPrtclDLSnd.aucApplyDataBuf[1] = (s_wLossFrameNo >> 8) & 0xff;
    s_tPrtclDLSnd.aucApplyDataBuf[2] = s_wLossFrameNo & 0xff;
    s_tPrtclDLSnd.aucApplyDataBuf[3] = (wFrameLossCount >> 8) & 0xff;
    s_tPrtclDLSnd.aucApplyDataBuf[4] = wFrameLossCount & 0xff;
    s_tPrtclDLSnd.wApplySendDataLength = 5;
    s_tPrtclDLRcv.wApplyAppEnd = 0x55AA;
    s_tPrtclDLRcv.ucRTN = ApplyRTN_ACK;
    return SUCCESSFUL;
}

// 主机处理从机针对数据传输确认帧的回包
Static BYTE AgentBMSParalleDataConfimRtnFrm(T_CommStruct *ptComm)
{
    INT16U BuffLen = 0;
    WORD wLostFrmNum = 0;
    WORD wFrameLossCount = 0;
    RETURN_VAL_IF_FAIL(ptComm != NULL, FAILURE);

    s_tPrtclDLRcv.ucRTN = AgentGetAPPCommonRTN(ptComm, DOWNLOAD_APP_DATA_CONFIRM_BACK_LEN);
    // 从机回包校验失败进入更新确认帧重发阶段
    if((s_tPrtclDLRcv.ucRTN != ApplyRTN_ACK) || (s_tPrtclDLRcv.aucDataBuf[6] != ApplyRTN_ACK))
    {
        // 令ucRTN == NOACK, 即不需要对此命令进行回包。
        #if PARALLE_UPDATE_DEBUG
        rt_kprintf("收到传输完成确认帧，rtn错误\n");
        #endif
        s_tPrtclDLRcv.ucRTN = ApplyRTN_NOACK;
        s_tAgentParalleDLStat = AGENT_DL_STAT_DATA_CONFIRM;
        s_bAgentUpdateFlag = True;
        return SUCCESSFUL;
    }
    s_ucRepeatSendFrameCnt = 0;
    // 从机i确认已经接收到完整的数据帧时也认为补帧完成，从机收到最后一帧补发帧后的回包也会走这里的逻辑。
    if (s_tPrtclDLRcv.aucApplyDataBuf[12] == FRAME_RECEIVE_COMPLETE)
    {
        // 从机无需补帧，直接发送更新确认帧
        #if PARALLE_UPDATE_DEBUG
        rt_kprintf("收到传输完成确认帧，无需补帧，向地址%d发送更新确认帧\n", s_ucAgentUpdatePollingAddr);
        #endif
        AgentParalleUpdateConfirmFrm(&BuffLen, s_bUpdateDataBuff, s_ucAgentUpdatePollingAddr);
        rt_memcpy(s_tPrtclDLSnd.aucApplyDataBuf, s_bUpdateDataBuff, BuffLen);
        s_tPrtclDLSnd.wApplySendDataLength = BuffLen;
        s_tAgentParalleDLStat = AGENT_DL_STAT_UPDATE_CONFIRM;
    }
    else
    {
        // 接收到从机需要补包的回帧时将s_ucSupplyFrameRepeatCnt清零
        s_ucSupplyFrameRepeatCnt = 0;
        // 记录需要给从机发送的补帧序号
        wLostFrmNum = Host2Modbus((SHORT *)(s_tPrtclDLRcv.aucApplyDataBuf + 13));
        s_wLostFrameNum = wLostFrmNum;
        //获取从机需要补发包的总帧数
        wFrameLossCount = Host2Modbus((SHORT *)(s_tPrtclDLRcv.aucApplyDataBuf + 15));
        //判断从机丢包数是否超过阈值，超过则不对该从机进行补帧动作
        #if PARALLE_UPDATE_DEBUG
        rt_kprintf("收到传输完成确认帧，需要补帧，丢包总数%d,丢包阈值%d\n", wFrameLossCount, s_wFrameLossCountThresh);
        #endif
        if(wFrameLossCount > s_wFrameLossCountThresh){
            s_onSlavecount++;
            if(s_onSlavecount < s_tBMSUpdateScanResult.numInPlace){
                s_ucAgentUpdatePollingAddr = s_tBMSUpdateScanResult.battInPlaceAddr[s_onSlavecount];
                s_tAgentParalleDLStat = AGENT_DL_STAT_DATA_CONFIRM;
                s_bAgentUpdateFlag = True;
            }else{
                s_tAgentParalleDLStat = AGENT_DL_STAT_UPDATE_CONFIRM;
                s_bAgentUpdateFlag = True;
            } 
        }else{
            #if PARALLE_UPDATE_DEBUG
            rt_kprintf("收到传输完成确认帧，需要补帧，向地址%d发送帧号%d\n", s_ucAgentUpdatePollingAddr, s_wLostFrameNum);
            #endif
            // 升级主机接收到从机数据确认帧回包后如果需要进行补帧，则直接发送补帧数据。
            s_tAgentParalleDLStat = AGENT_DL_STAT_SUPPLY_FRAME;
            SendSupplyDataFrame(ptComm, s_wLostFrameNum, s_ucAgentUpdatePollingAddr, DIRECT_SUPPLY);
        }
    }

    return SUCCESSFUL;
}


Static BYTE AgentBMSParalleDataConfimRtnFrm_Self(T_CommStruct *ptComm)
{
    INT16U BuffLen = 0;
    WORD wLostFrmNum = 0;
    WORD wFrameLossCount = 0;

    RETURN_VAL_IF_FAIL(ptComm != NULL, FAILURE);

    s_tPrtclDLRcv.ucRTN = AgentGetAPPCommonRTN(ptComm, DOWNLOAD_APP_DATA_CONFIRM_BACK_LEN);
    
    // 从机回包校验失败进入更新确认帧重发阶段
    if ((s_tPrtclDLRcv.ucRTN != ApplyRTN_ACK) || (s_tPrtclDLRcv.aucDataBuf[6] != ApplyRTN_ACK))
    {
        #if PARALLE_UPDATE_DEBUG
        rt_kprintf("收到传输完成确认帧，rtn错误\n");
        #endif
        s_tPrtclDLRcv.ucRTN = ApplyRTN_NOACK;
        s_ucBDUDLStat = AGENT_DL_STAT_DATA_CONFIRM;
        s_bAgentUpdateFlag = True;
        return SUCCESSFUL;
    }

    s_ucRepeatSendFrameCnt = 0;

    // 从机确认已经接收到完整的数据帧时也认为补帧完成，从机收到最后一帧补发帧后的回包也会走这里的逻辑。
    if (s_tPrtclDLRcv.aucApplyDataBuf[12] == FRAME_RECEIVE_COMPLETE)
    {
        #if PARALLE_UPDATE_DEBUG
        rt_kprintf("收到传输完成确认帧，无需补帧，发送更新确认帧\n");
        #endif
        AgentParalleUpdateConfirmFrm(&BuffLen, s_bUpdateDataBuff, GetTotalAddr());
        s_ucBDUDLStat = AGENT_DL_STAT_UPDATE_CONFIRM;
        BMSRecvBDUParalleTransfrm_SendBduSelf(BuffLen, s_bUpdateDataBuff);
    }
    else
    {
        // 接收到从机需要补包的回帧时将s_ucSupplyFrameRepeatCnt清零
        s_ucSupplyFrameRepeatCnt = 0;

        // 记录需要给从机发送的补帧序号
        wLostFrmNum = Host2Modbus((SHORT *)(s_tPrtclDLRcv.aucApplyDataBuf + 13));
        s_wLostFrameNum = wLostFrmNum;

        // 获取从机需要补发包的总帧数
        wFrameLossCount = Host2Modbus((SHORT *)(s_tPrtclDLRcv.aucApplyDataBuf + 15));

        // 判断从机丢包数是否超过阈值，超过则不对该从机进行补帧动作
        #if PARALLE_UPDATE_DEBUG
        rt_kprintf("收到传输完成确认帧，需要补帧，丢包总数%d,丢包阈值%d\n", wFrameLossCount, s_wFrameLossCountThresh);
        #endif
        if (wFrameLossCount > s_wFrameLossCountThresh)
        {
            s_ucBDUDLStat = AGENT_DL_STAT_UPDATE_CONFIRM;
            s_bAgentUpdateFlag = True;
        }
        else
        {
            #if PARALLE_UPDATE_DEBUG
            rt_kprintf("收到传输完成确认帧，需要补帧，发送帧号%d\n", s_wLostFrameNum);
            #endif
            // 升级主机接收到从机数据确认帧回包后如果需要进行补帧，则直接发送补帧数据。
            s_ucBDUDLStat = AGENT_DL_STAT_SUPPLY_FRAME;
            SendSupplyDataFrame_Self(s_wLostFrameNum, GetTotalAddr(), DIRECT_SUPPLY);
        }
    }

    return SUCCESSFUL;
}


// 数据传输帧处理函数（主要根据wApplyAppEnd来判断是从机在处理数据还是主机在处理数据）
Static BYTE DealBMSParalleDataConfimFrm(T_CommStruct *ptComm) {
    RETURN_VAL_IF_FAIL(ptComm != NULL, FAILURE);
    #if PARALLE_UPDATE_DEBUG
    rt_tick_t tickEndDownload = rt_tick_get();
    rt_kprintf("收到传输确认帧，帧号:%x, 收到帧数:%d, 收到时刻:%d, 总用时:%d ms\n", s_tPrtclDLRcv.wApplyAppEnd, s_lastRecivedFrmNO, tickEndDownload, tickEndDownload - s_TickStartDownload);
    #endif

    if (s_tPrtclDLRcv.wApplyAppEnd != 0xAA55 && s_tPrtclDLRcv.wApplyAppEnd != 0x55AA) {
        s_tPrtclDLRcv.ucRTN = ApplyRTN_NOACK;
        return FAILURE;
    }

    // 附加码为55AA说明是升级主机在处理从机对数据传输确认帧的回帧。
    if(s_tPrtclDLRcv.wApplyAppEnd == 0x55AA){
        s_bAgentP2PAckFlag = True;
        AgentBMSParalleDataConfimRtnFrm(ptComm);
        return SUCCESSFUL;
    }

    if (s_tParalleDLStat == PARALLE_DL_STAT_PREPARE)
    {
        s_tPrtclDLRcv.ucRTN = ApplyRTN_NOACK;
        return FAILURE;
    }

    s_tPrtclDLRcv.ucRTN = GetAPPCommonRTN(ptComm, DOWNLOAD_APP_DATA_CONFIRM_LEN);
    // 从机处理升级主机发来的数据传输确认帧
    if(s_tPrtclDLRcv.ucRTN == ApplyRTN_ACK )
    {
        s_tPrtclDLRcv.wApplyLength = 0;
        BMSSendParalleDataConfimFrm(ptComm);
    }
    else
    {
        s_tPrtclDLRcv.wApplyRecvDataLength = 0;
        RecAppDownloadRTNFault();
    }
    return SUCCESSFUL;

}


Static void DealDLDataSCI(BYTE* pucApplyData, BYTE ucLen)
{
    if (pucApplyData == NULL || ucLen < 12) {
        return;
    }

    T_CommStruct tComm = {0};
    BYTE *pucDest = s_tPrtclDLRcv.aucDataBuf;

    tComm.ucPortType = COMM_CAN;
    rt_memcpy(pucDest, pucApplyData, 12);

    s_tPrtclDLRcv.ucProtoType = pucApplyData[0];
    s_tPrtclDLRcv.ucSrcDevType = pucApplyData[1];
    s_tPrtclDLRcv.ucSrcDevAdr = pucApplyData[2];
    s_tPrtclDLRcv.ucApplyFun = pucApplyData[3];
    s_tPrtclDLRcv.wApplyAppEnd = (pucApplyData[4] << 8) + pucApplyData[5];
    s_tPrtclDLRcv.ucRsvDestDevType = pucApplyData[7];
    s_tPrtclDLRcv.ucRsvDestDevAdr = pucApplyData[8];
    s_tPrtclDLRcv.ucRsvTrans = pucApplyData[9];
    s_tPrtclDLRcv.wApplyRecvDataLength = (pucApplyData[10] << 8) + pucApplyData[11];

    rt_memcpy(pucDest + 12, pucApplyData + 12, s_tPrtclDLRcv.wApplyRecvDataLength + 2);
    s_tPrtclDLRcv.wApplyLength = s_tPrtclDLRcv.wApplyRecvDataLength + 14;
    rt_memcpy(s_tPrtclDLRcv.aucApplyDataBuf, s_tPrtclDLRcv.aucDataBuf, s_tPrtclDLRcv.wApplyLength);

    if (s_tPrtclDLRcv.ucProtoType != 0xE1) {
        return;
    }

    s_bAgentP2PAckFlag = False;

    switch (s_tPrtclDLRcv.ucApplyFun) {
        case FUNC_CODE_PARALLE_FRONT_TRANSFER_FRM:
            BMSRecvBDUParalleTransfrm_Self(&tComm);
            break;
        case FUNC_CODE_PARALLE_FRONT_DATA_CONFIRM_FRM:
            BMSRecvBDUParalleDataConfimFrm_Self(&tComm);
            break;
        case FUNC_CODE_PARALLE_FRONT_TRIGGER_FRM:
        case FUNC_CODE_PARALLE_FRONT_UPDATE_CONFIRM_FRM:
            BDUParalleTrigger_Self(&tComm);
            break;
        default:
            break;
    }
}



Static BOOLEAN SaveVersionAction(void)
{
    Static BYTE ucTrigcnt = 0;
    T_DCFactory tBduFactory;
    char str[21] = {0};

    rt_memset_s(&tBduFactory, sizeof(T_DCFactory), 0, sizeof(T_DCFactory));
    GetBduFact(&tBduFactory);

    if (ucTrigcnt != 0) {
        return FAILURE;
    }

    if (s_tPrtclDLRcv.ucApplyFun == 0x80 || s_tPrtclDLRcv.ucApplyFun == 0x91)
    {
        ucTrigcnt++;
        rt_snprintf(str, sizeof(str), "BFR BMS %s", BMS_VER);
    }
    else if (s_tPrtclDLRcv.ucApplyFun == 0x81 || s_tPrtclDLRcv.ucApplyFun == 0x92)
    {
        ucTrigcnt++;
        rt_snprintf(str, sizeof(str), "BFR BDU %s", tBduFactory.acSoftVer);
    }
    else
    {
        return FAILURE;
    }

    SaveAction(GetActionId(CONTOL_BATT_INDICATION), str);

    return SUCCESSFUL;
}



Static BOOLEAN DealDLData(T_CommStruct *ptComm)
{
    s_bAgentP2PAckFlag = False;
    switch(s_tPrtclDLRcv.ucApplyFun)
    {
        case 0x80:
        case 0x82:
            DealBMSTrigger(ptComm);
            break;
        case 0x81:  // SCI升级触发帧
        case 0x84:  // SCI升级数据帧
            SetBduUpdate(True);
            return SUCCESSFUL;
        case FUNC_CODE_PARALLE_FRONT_TRANSFER_FRM:
            BMSRecvBDUParalleTransfrm(ptComm);
            return SUCCESSFUL;
        case FUNC_CODE_PARALLE_FRONT_DATA_CONFIRM_FRM:
            BMSRecvBDUParalleDataConfimFrm(ptComm);
            return SUCCESSFUL;
        case FUNC_CODE_PARALLE_FRONT_TRIGGER_FRM:
        case FUNC_CODE_PARALLE_FRONT_UPDATE_CONFIRM_FRM:
            BMSSendBDUParalleTrigger(ptComm);
            return SUCCESSFUL;
        case 0x85:
            DealDownload(); 
            break;
        case 0x90:
            DealBMSSwapBaud(ptComm);
            break;
        case FUNC_CODE_PARALLE_TRIGGER_FRM:
        case FUNC_CODE_PARALLE_UPDATE_CONFIRM_FRM:
            DealBMSParalleTrigger(ptComm);
            break;
        case FUNC_CODE_PARALLE_TRANSFER_FRM:
            DealBMSParalleTransferFrm(ptComm);
            break;
        case FUNC_CODE_PARALLE_DATA_CONFIRM_FRM:
            DealBMSParalleDataConfimFrm(ptComm);
            break;
        default:
            return FAILURE;
    }
    PackAndSend(ptComm);

    return SUCCESSFUL;
}



Static void BMSRecvBDUParalleTransfrm_in(void) {
    // 当前帧写入升级帧缓存
    rt_memset_s(&s_tDataToCircleBuff, sizeof(T_AppDataSaveStruct), 0x00, sizeof(T_AppDataSaveStruct));

    s_tDataToCircleBuff.ulFrameNo = s_tPrtclDLRcv.wApplyAppEnd;
    rt_memcpy_s(s_tDataToCircleBuff.auFrameHead, sizeof(s_tDataToCircleBuff.auFrameHead), s_tPrtclDLRcv.aucApplyDataBuf, APP_FRAME_HEAD_LEN - 2);
    rt_memcpy_s(s_tDataToCircleBuff.aucData, sizeof(s_tDataToCircleBuff.aucData), &s_tPrtclDLRcv.aucApplyDataBuf[APP_FRAME_HEAD_LEN - 2], s_tPrtclDLRcv.wApplyRecvDataLength);
    rt_memcpy_s(&s_tDataToCircleBuff.auFrameHead[APP_FRAME_HEAD_LEN - 2], 2, &s_tPrtclDLRcv.aucApplyDataBuf[APP_FRAME_HEAD_LEN - 2 + s_tPrtclDLRcv.wApplyRecvDataLength], 2);

    write_circle_buff(&s_tDataToCircleBuff, 1);
    SetBduParalleUpdate(True);
}



Static void BMSRecvBDUParalleTransfrm_SendBduSelf(INT16U pOutLen, INT8U *pOutData) {
    BYTE ucBuf[300] = {0};
    BYTE *p = ucBuf;
    WORD wCrc;

    // 当前帧写入升级帧缓存
    rt_memset_s(&s_tDataToCircleBuff, sizeof(T_AppDataSaveStruct), 0x00, sizeof(T_AppDataSaveStruct));

    *p++ = DownloadType;
    *p++ = BMS_Type;
    *p++ = GetBMSDLAddr();
    *p++ = s_tPrtclDLRcv.ucApplyFun;
    *p++ = (s_tPrtclDLRcv.wApplyAppEnd >> 8) & 0xff;
    *p++ = s_tPrtclDLRcv.wApplyAppEnd & 0xff;
    *p++ = s_tPrtclDLRcv.ucRTN;
    *p++ = s_tPrtclDLRcv.ucRsvDestDevType;
    *p++ = s_tPrtclDLRcv.ucRsvDestDevAdr;
    *p++ = s_tPrtclDLRcv.ucRsvTrans;
    *p++ = (BYTE)((pOutLen >> 8) & 0xff);
    *p++ = (BYTE)(pOutLen & 0xff);
    rt_memcpy(p, pOutData, pOutLen);
    p += pOutLen;

    wCrc = CRC_Cal(ucBuf, p - ucBuf);
    *(p++) = wCrc & 0xFF;
    *(p++) = wCrc >> 8;

    rt_memcpy(s_tPrtclDLRcv.aucDataBuf, ucBuf, 12);
    s_tPrtclDLRcv.ucProtoType = ucBuf[0];
    s_tPrtclDLRcv.ucSrcDevType = ucBuf[1];
    s_tPrtclDLRcv.ucSrcDevAdr = ucBuf[2];
    s_tPrtclDLRcv.ucApplyFun = ucBuf[3];
    s_tPrtclDLRcv.wApplyAppEnd = (ucBuf[4] << 8) + ucBuf[5];
    s_tPrtclDLRcv.ucRsvDestDevType = ucBuf[7];
    s_tPrtclDLRcv.ucRsvDestDevAdr = ucBuf[8];
    s_tPrtclDLRcv.ucRsvTrans = ucBuf[9];
    s_tPrtclDLRcv.wApplyRecvDataLength = (ucBuf[10] << 8) + ucBuf[11];
    rt_memcpy(&s_tPrtclDLRcv.aucDataBuf[12], &ucBuf[12], s_tPrtclDLRcv.wApplyRecvDataLength + 2);
    s_tPrtclDLRcv.wApplyLength = s_tPrtclDLRcv.wApplyRecvDataLength + 14;
    rt_memcpy(s_tPrtclDLRcv.aucApplyDataBuf, s_tPrtclDLRcv.aucDataBuf, s_tPrtclDLRcv.wApplyLength);

    s_tDataToCircleBuff.ulFrameNo = s_tPrtclDLRcv.wApplyAppEnd;
    rt_memcpy_s(s_tDataToCircleBuff.auFrameHead, sizeof(s_tDataToCircleBuff.auFrameHead), s_tPrtclDLRcv.aucApplyDataBuf, APP_FRAME_HEAD_LEN - 2);
    rt_memcpy_s(s_tDataToCircleBuff.aucData, sizeof(s_tDataToCircleBuff.aucData), &s_tPrtclDLRcv.aucApplyDataBuf[APP_FRAME_HEAD_LEN - 2], s_tPrtclDLRcv.wApplyRecvDataLength);
    rt_memcpy_s(&s_tDataToCircleBuff.auFrameHead[APP_FRAME_HEAD_LEN - 2], 2, &s_tPrtclDLRcv.aucApplyDataBuf[APP_FRAME_HEAD_LEN - 2 + s_tPrtclDLRcv.wApplyRecvDataLength], 2);

    write_circle_buff(&s_tDataToCircleBuff, 1);
    SetBduParalleUpdate(True);
}



Static void BDUParalleTrigger_Self(T_CommStruct *ptComm)           // 自己触发BDU升级
{
#if PARALLE_UPDATE_DEBUG
    rt_kprintf("BDUParalleTrigger_Self:%d   %x   %x   %x   %x   %d \n",GetUpgradeFileFlag(), s_tPrtclDLRcv.ucRsvDestDevType, s_tPrtclDLRcv.ucApplyFun,s_tPrtclDLRcv.wApplyAppEnd, s_tPrtclDLRcv.ucRsvTrans, s_tPrtclDLRcv.ucRsvDestDevAdr);
#endif
    if(GetUpgradeFileFlag() == UPDATE_BDU_FILE)
    {
        if (s_tPrtclDLRcv.wApplyAppEnd != 0xAA55 && s_tPrtclDLRcv.wApplyAppEnd != 0x55AA) {
            s_tPrtclDLRcv.ucRTN = ApplyRTN_NOACK;
            return;
        }
        if(s_tPrtclDLRcv.ucApplyFun == FUNC_CODE_PARALLE_FRONT_TRIGGER_FRM) // 传输触发帧处理逻辑
        {
            #if PARALLE_UPDATE_DEBUG
            rt_kprintf("Trigger_Self:%x   %x\n",s_tPrtclDLRcv.ucApplyFun,s_tPrtclDLRcv.wApplyAppEnd);
            #endif
            if(s_tPrtclDLRcv.wApplyAppEnd == 0x55AA)// 对触发帧回包的处理逻辑
            {
                SetBduParallUpdateFlag(True);
                s_bAgentP2PAckFlag = True;
                ProcessTriggerFrameAck_Self(ptComm);
            }
        }
        else //更新确认帧处理逻辑
        {
            if(s_tPrtclDLRcv.wApplyAppEnd == 0x55AA)// 更新确认帧回包的处理逻辑
            {
                s_bAgentP2PAckFlag = True;
                ProcessUpdateConfirmFrameAck_Self(ptComm);
            }
        }

        return;
    }
}


/**
 * @brief 接收并发升级功率触发帧（区分广播与透传）
 * @param[in] ptComm 通信数据
*/

Static void BMSSendBDUParalleTrigger(T_CommStruct *ptComm)         //// 外界触发BDU升级
{
    WORD wSendCount = 0;

#if PARALLE_UPDATE_DEBUG
    rt_kprintf("BMSSendBDUParalleTrigger:%d   %x   %x   %x   %x   %d \n",GetUpgradeFileFlag(), s_tPrtclDLRcv.ucRsvDestDevType, s_tPrtclDLRcv.ucApplyFun,s_tPrtclDLRcv.wApplyAppEnd, s_tPrtclDLRcv.ucRsvTrans, s_tPrtclDLRcv.ucRsvDestDevAdr);
#endif
    if(GetUpgradeFileFlag() == UPDATE_BDU_FILE)
    {
        if (s_tPrtclDLRcv.wApplyAppEnd != 0xAA55 && s_tPrtclDLRcv.wApplyAppEnd != 0x55AA) {
            s_tPrtclDLRcv.ucRTN = ApplyRTN_NOACK;
            return;
        }
        if(s_tPrtclDLRcv.ucApplyFun == FUNC_CODE_PARALLE_FRONT_TRIGGER_FRM) // 传输触发帧处理逻辑
        {
            if(s_tPrtclDLRcv.wApplyAppEnd == 0x55AA)// 对触发帧回包的处理逻辑
            {
                s_bAgentP2PAckFlag = True;
                ProcessTriggerFrameAck(ptComm);
            }
        }
        else //更新确认帧处理逻辑
        {
            if(s_tPrtclDLRcv.wApplyAppEnd == 0x55AA)// 更新确认帧回包的处理逻辑
            {
                s_bAgentP2PAckFlag = True;
                ProcessUpdateConfirmFrameAck(ptComm);
            }
        }

        return;
    }

    //非透传
    if (s_tPrtclDLRcv.ucRsvDestDevType == DEV_TYPE_BMS && s_tPrtclDLRcv.ucRsvTrans == 0xFF && s_tPrtclDLRcv.ucRsvDestDevAdr == 0) {
        //透传主机需要重发20次
        if (IsMaster()) {
            //主机接收到传输触发帧以后连续发送20次给从机，让从机完成触发
            for(wSendCount = 0; wSendCount < 20; wSendCount++){
                ChangeIntoCanFrm(s_tPrtclDLRcv.aucApplyDataBuf, s_tPrtclDLRcv.wApplyLength, DEV_TYPE_BMS, 0);
                rt_thread_delay(50);
            }
        }
    }
    reset_circle_buff();
    BMSRecvBDUParalleTransfrm_in();
    SetBduParallUpdateFlag(True);
}


/**
 * @brief 接收并发升级功率数据帧，存入缓存
*/

Static void BMSRecvBDUParalleTransfrm(T_CommStruct *ptComm) {
    // 当前帧写入升级帧缓存
    if(GetUpgradeFileFlag() == UPDATE_BDU_FILE)
    {
        // （代理升级）升级主机处理从机数据传输帧回包
        // 数据传输阶段当接收的DATA域数据长度为0时说明是从机的回包
        if (s_tPrtclDLRcv.wApplyRecvDataLength == 0)
        {
            s_bAgentP2PAckFlag = True;
            DealSlaveTransferFrmAck(ptComm);
            return;
        }
        
        // 没有触发成功，不接受传输帧
        if(s_tParalleDLStat == PARALLE_DL_STAT_PREPARE) {
            s_tPrtclDLRcv.ucRTN = ApplyRTN_NOACK;
            return;
        }
    }

    rt_memset_s(&s_tDataToCircleBuff, sizeof(T_AppDataSaveStruct), 0x00, sizeof(T_AppDataSaveStruct));

    s_tDataToCircleBuff.ulFrameNo = s_tPrtclDLRcv.wApplyAppEnd;
    rt_memcpy_s(s_tDataToCircleBuff.auFrameHead, sizeof(s_tDataToCircleBuff.auFrameHead), s_tPrtclDLRcv.aucApplyDataBuf, APP_FRAME_HEAD_LEN - 2);
    rt_memcpy_s(s_tDataToCircleBuff.aucData, sizeof(s_tDataToCircleBuff.aucData), &s_tPrtclDLRcv.aucApplyDataBuf[APP_FRAME_HEAD_LEN - 2], s_tPrtclDLRcv.wApplyRecvDataLength);
    rt_memcpy_s(&s_tDataToCircleBuff.auFrameHead[APP_FRAME_HEAD_LEN - 2], 2, &s_tPrtclDLRcv.aucApplyDataBuf[APP_FRAME_HEAD_LEN - 2 + s_tPrtclDLRcv.wApplyRecvDataLength], 2);

    write_circle_buff(&s_tDataToCircleBuff, 1);

    SetBduParalleUpdate(True);
}



Static void BMSRecvBDUParalleTransfrm_Self(T_CommStruct *ptComm) {
    // 当前帧写入升级帧缓存
    if(GetUpgradeFileFlag() == UPDATE_BDU_FILE)
    {
        // （代理升级）升级主机处理从机数据传输帧回包
        // 数据传输阶段当接收的DATA域数据长度为0时说明是从机的回包
        if (s_tPrtclDLRcv.wApplyRecvDataLength == 0)
        {
            s_bAgentP2PAckFlag = True;
            DealSlaveTransferFrmAck_Self(ptComm);
            return;
        }

        // 没有触发成功，不接受传输帧
        if(s_tParalleDLStat == PARALLE_DL_STAT_PREPARE) {
            s_tPrtclDLRcv.ucRTN = ApplyRTN_NOACK;
            return;
        }
    }

    return;
}



Static void BMSRecvBDUParalleDataConfimFrm(T_CommStruct *ptComm)
{
    if(GetUpgradeFileFlag() == UPDATE_BDU_FILE)
    {
        if (s_tPrtclDLRcv.wApplyAppEnd != 0xAA55 && s_tPrtclDLRcv.wApplyAppEnd != 0x55AA) {
            s_tPrtclDLRcv.ucRTN = ApplyRTN_NOACK;
            return;
        }

        // 附加码为55AA说明是升级主机在处理从机对数据传输确认帧的回帧。
        if(s_tPrtclDLRcv.wApplyAppEnd == 0x55AA){
            s_bAgentP2PAckFlag = True;
            AgentBMSParalleDataConfimRtnFrm(ptComm);
            return;
        }
    }

    rt_memset_s(&s_tDataToCircleBuff, sizeof(T_AppDataSaveStruct), 0x00, sizeof(T_AppDataSaveStruct));

    s_tDataToCircleBuff.ulFrameNo = s_tPrtclDLRcv.wApplyAppEnd;
    rt_memcpy_s(s_tDataToCircleBuff.auFrameHead, sizeof(s_tDataToCircleBuff.auFrameHead), s_tPrtclDLRcv.aucApplyDataBuf, APP_FRAME_HEAD_LEN - 2);
    rt_memcpy_s(s_tDataToCircleBuff.aucData, sizeof(s_tDataToCircleBuff.aucData), &s_tPrtclDLRcv.aucApplyDataBuf[APP_FRAME_HEAD_LEN - 2], s_tPrtclDLRcv.wApplyRecvDataLength);
    rt_memcpy_s(&s_tDataToCircleBuff.auFrameHead[APP_FRAME_HEAD_LEN - 2], 2, &s_tPrtclDLRcv.aucApplyDataBuf[APP_FRAME_HEAD_LEN - 2 + s_tPrtclDLRcv.wApplyRecvDataLength], 2);

    write_circle_buff(&s_tDataToCircleBuff, 1);
    SetBduParalleUpdate(True);
}



Static void BMSRecvBDUParalleDataConfimFrm_Self(T_CommStruct *ptComm)
{
    if(GetUpgradeFileFlag() == UPDATE_BDU_FILE)
    {
        if (s_tPrtclDLRcv.wApplyAppEnd != 0xAA55 && s_tPrtclDLRcv.wApplyAppEnd != 0x55AA) {
            s_tPrtclDLRcv.ucRTN = ApplyRTN_NOACK;
            return;
        }

        // 附加码为55AA说明是升级主机在处理从机对数据传输确认帧的回帧。
        if(s_tPrtclDLRcv.wApplyAppEnd == 0x55AA){
            s_bAgentP2PAckFlag = True;
            AgentBMSParalleDataConfimRtnFrm_Self(ptComm);
            return;
        }
    }

    return;
}


BOOLEAN GetSciData(T_PrtclDLStruct *pDLFrm)
{
    WORD wApplyRecvDataLength = 0;
    if (NULL == pDLFrm)
    {
        return False;
    }

    if (IsBduP2PUpdate()) {
        if (s_bDlDataNewCAN)
        {
            s_bDlDataNewCAN = False;
            rt_memcpy( pDLFrm, &s_tPrtclDLRcv, sizeof(T_PrtclDLStruct) );
            return True;
        }
        if (s_bDlDataNew485)
        {
            s_bDlDataNew485 = False;  
            rt_memcpy( pDLFrm, &s_tPrtclDLRcv, sizeof(T_PrtclDLStruct) );
            return True;
        }
    } else {
        if (read_circle_buff(&s_tCircleBuffToData, 1)) {
            wApplyRecvDataLength = (s_tCircleBuffToData.auFrameHead[10] << 8) + s_tCircleBuffToData.auFrameHead[11];
            rt_memcpy_s( &pDLFrm->aucApplyDataBuf[0], sizeof(pDLFrm->aucApplyDataBuf), &s_tCircleBuffToData.auFrameHead[0], sizeof(s_tCircleBuffToData.auFrameHead) -2);
            rt_memcpy_s( &pDLFrm->aucApplyDataBuf[APP_FRAME_HEAD_LEN - 2], sizeof(pDLFrm->aucApplyDataBuf) - APP_FRAME_HEAD_LEN + 2, s_tCircleBuffToData.aucData, wApplyRecvDataLength);
            rt_memcpy_s( &pDLFrm->aucApplyDataBuf[wApplyRecvDataLength + APP_FRAME_HEAD_LEN - 2], 2, &s_tCircleBuffToData.auFrameHead[APP_FRAME_HEAD_LEN -2], 2);
            pDLFrm->wApplyLength =wApplyRecvDataLength + APP_FRAME_HEAD_LEN;
            return True;
        }
    }

    return False;
}

void DealCanDLData(T_CommStruct *ptComm)
{
	T_BCMAlarmStruct    tBCMAlm;
	T_BattResult    tBattResult;

	rt_memset(&tBCMAlm, 0, sizeof(T_BCMAlarmStruct));
	rt_memset(&tBattResult, 0, sizeof(T_BattResult));
	GetRealAlarm(BCM_ALARM_REAL, (BYTE *)&tBCMAlm);
	GetBattResult(&tBattResult);

	if(tBCMAlm.ucBDUBattLockAlm == FAULT || tBattResult.ucCellProtectStatus == FAULT)
	{
		return;
	}

    BYTE *pucDest = s_tPrtclDLRcv.aucDataBuf;
    BYTE *pucSrc = ptComm->aucRecBuf;
    
    rt_memcpy(pucDest, pucSrc, 12);
    s_tPrtclDLRcv.ucProtoType = s_tPrtclDLRcv.aucDataBuf[0];
    s_tPrtclDLRcv.ucSrcDevType = s_tPrtclDLRcv.aucDataBuf[1];
    s_tPrtclDLRcv.ucSrcDevAdr = s_tPrtclDLRcv.aucDataBuf[2];
    s_tPrtclDLRcv.ucApplyFun = s_tPrtclDLRcv.aucDataBuf[3];
    s_tPrtclDLRcv.wApplyAppEnd = (s_tPrtclDLRcv.aucDataBuf[4]<<8) + s_tPrtclDLRcv.aucDataBuf[5];
    s_tPrtclDLRcv.ucRsvDestDevType = s_tPrtclDLRcv.aucDataBuf[7];
    s_tPrtclDLRcv.ucRsvDestDevAdr = s_tPrtclDLRcv.aucDataBuf[8];
    s_tPrtclDLRcv.ucRsvTrans = s_tPrtclDLRcv.aucDataBuf[9];
    s_tPrtclDLRcv.wApplyRecvDataLength = (s_tPrtclDLRcv.aucDataBuf[10]<<8) + s_tPrtclDLRcv.aucDataBuf[11];
    rt_memcpy( pucDest+12, pucSrc+12, s_tPrtclDLRcv.wApplyRecvDataLength + 2 );
    s_tPrtclDLRcv.wApplyLength = s_tPrtclDLRcv.wApplyRecvDataLength + 14;
    rt_memcpy( s_tPrtclDLRcv.aucApplyDataBuf, &s_tPrtclDLRcv.aucDataBuf, s_tPrtclDLRcv.wApplyLength );

    if (s_tPrtclDLRcv.ucProtoType != 0xE1)
    {
        return;
    }

    s_bDlDataNewCAN = True;

    SaveVersionAction();
    DealDLData(ptComm);
    DealDeviceUnlock(AUTO_UNLOCK); 
    return;
}

Static BOOLEAN DealDLFrameComm(T_CommStruct *ptComm)
{
    RETURN_VAL_IF_FAIL(ptComm != NULL, False);

    if (IsMaster() && s_tPrtclDLRcv.ucRsvTrans == DOWNLOAD_TRANS) {
        RETURN_VAL_IF_FAIL(s_tPrtclDLRcv.ucRsvDestDevType == DEV_TYPE_BMS, False);
        if (IsMasterTrans(s_tPrtclDLRcv.ucRsvDestDevAdr, s_tPrtclDLRcv.ucApplyFun)) {
            s_bIsDLCommTrans = True;
            //不支持即是广播又是透传广播的方式, 是这种情况直接不处理数据
            RETURN_VAL_IF_FAIL(s_tPrtclDLRcv.ucDestDevAdr != 0, False);
            ChangeIntoCanFrm(s_tPrtclDLRcv.aucApplyDataBuf, s_tPrtclDLRcv.wApplyLength, s_tPrtclDLRcv.ucRsvDestDevType, s_tPrtclDLRcv.ucRsvDestDevAdr);
            //点对点透传，主机接收后不处理数据 
            RETURN_VAL_IF_FAIL(s_tPrtclDLRcv.ucRsvDestDevAdr == getGrpAddr(), False);
        }
    }

    return True;
}

void DealCommDLData(T_CommStruct *ptComm)
{
	T_BCMAlarmStruct    tBCMAlm;
	T_BattResult    tBattResult;

	rt_memset(&tBCMAlm, 0, sizeof(T_BCMAlarmStruct));
	rt_memset(&tBattResult, 0, sizeof(T_BattResult));
	GetRealAlarm(BCM_ALARM_REAL, (BYTE *)&tBCMAlm);
	GetBattResult(&tBattResult);

	if(tBCMAlm.ucBDUBattLockAlm == FAULT || tBattResult.ucCellProtectStatus == FAULT)
	{
		return;
	}

    if ( FAILURE == parseDLFrame(ptComm) )
    {
        //SendLinkRTN(ptComm, LinkRTN_DataLen);
        return;
    }

    RETURN_IF_FAIL(DealDLFrameComm(ptComm) == True);

    s_bPrtclDLLinkType = DL_LINK_TYPE_COMM;

    s_bDlDataNew485 = True;

    SaveVersionAction();
    DealDLData( ptComm );
    DealDeviceUnlock(AUTO_UNLOCK);    
    return;
}

static void DealBMSSwapBaud(T_CommStruct *ptComm)
{
    s_tPrtclDLRcv.ucRTN = GetAPPCommonRTN(ptComm, DOWNLOAD_SWAP_BAUD_LEN);

    if(s_tPrtclDLRcv.ucRTN == ApplyRTN_ACK )
    {
        s_tPrtclDLRcv.wApplyLength = 0;
        SwapBaudrate();
    }
    else
    {
        s_tPrtclDLRcv.wApplyRecvDataLength = 0;
        RecAppDownloadRTNFault();
    }
    return;
}

static void	SwapBaudrate()
{
    CHAR buff[21] = {0};
    BYTE handShakeCount = 0;
    T_PrtclDLStruct	*pApp = &s_tPrtclDLRcv;
    uint8_t	*p = &(pApp->aucApplyDataBuf[12]);
    BYTE ucActId;
    WORD wTemp=0;

    wTemp = GetInt16Data(p);
    if (wTemp != 96 && wTemp != 1152 && wTemp != 576 && wTemp != 384 && wTemp != 192)
    {
        pApp->ucRTN = DownloadFrameErr;
        setHandShakeCounter(0);
    }
    handShakeCounterIncrement();
    handShakeCount = getHandShakeCounter();
    if (handShakeCount >= 3)
    {
        ucActId = CONTOL_BAUDRATE_SWITCH;
        rt_snprintf_s(buff, sizeof(buff), "%d to %d", (UINT32)getBaudMode(), (UINT32)(wTemp * 100));
        SaveAction(ucActId, buff);
        setBaudMode((UINT32)(wTemp * 100));
    }
    pApp->ucRTN = 0x00;
    pApp->wApplyAppEnd = 0xAA55;
    pApp->wDataLength = 0;

    return;
}

/* 收到触发帧的处理逻辑 */
Static void ProcessTriggerFrame(T_CommStruct *ptComm)
{
    s_tPrtclDLRcv.ucRTN = GetAPPDownLoadRTN(ptComm);
    if(s_tPrtclDLRcv.ucRTN == ApplyRTN_ACK)
    {
        BMSSendParalleTrigger(ptComm);
    }
    else
    {
        s_tPrtclDLRcv.wApplyRecvDataLength = 0;
        RecAppDownloadRTNFault();
    }
    return;
}

/* 对触发帧回包的处理逻辑 */
Static void ProcessTriggerFrameAck(T_CommStruct *ptComm)
{
    s_tPrtclDLRcv.ucRTN = GetAPPDownLoadRTNAck(ptComm);
    if(s_tPrtclDLRcv.ucRTN == ApplyRTN_ACK)
    {
        TriggerNextSlaveAck(ptComm);
    }
    else
    {
        #if PARALLE_UPDATE_DEBUG
            rt_kprintf("收到传输触发帧，rtn错误：%d\n", s_tPrtclDLRcv.ucRTN);
        #endif
        s_tAgentParalleDLStat = AGENT_DL_STAT_TRIGING;
        s_bAgentUpdateFlag = True;
        s_tPrtclDLRcv.ucRTN = ApplyRTN_NOACK;
    }
    return;
}


Static void ProcessTriggerFrameAck_Self(T_CommStruct *ptComm)
{
    s_tPrtclDLRcv.ucRTN = GetAPPDownLoadRTNAck(ptComm);
    if(s_tPrtclDLRcv.ucRTN == ApplyRTN_ACK)
    {
        s_bTrigerFlag = True;
        s_ucBDUDLStat = AGENT_DL_STAT_DATA_TRANS;
        s_bAgentUpdateFlag = True;
        s_tPrtclDLRcv.ucRTN = ApplyRTN_NOACK;
        s_tPrtclDLRcv.wApplyAppEnd = 0;
    }
    else
    {
        #if PARALLE_UPDATE_DEBUG
            rt_kprintf("收到传输触发帧，rtn错误：%d\n", s_tPrtclDLRcv.ucRTN);
        #endif
        s_ucBDUDLStat = AGENT_DL_STAT_TRIGING;
        s_bAgentUpdateFlag = True;
        s_tPrtclDLRcv.ucRTN = ApplyRTN_NOACK;
    }
}


/* 收到更新确认帧的处理逻辑 */
Static void ProcessUpdateConfirmFrame(T_CommStruct *ptComm)
{
    s_tPrtclDLRcv.ucRTN = GetAPPDownLoadRTN(ptComm);
    if(s_tPrtclDLRcv.ucRTN == ApplyRTN_ACK)
    {
        s_tPrtclDLRcv.wApplyLength = 0;
        BMSSendParalleUpdateConfirm(ptComm);
    }
    else
    {
        s_tPrtclDLRcv.wApplyRecvDataLength = 0;
        RecAppDownloadRTNFault();
    }
    return;
}

/* 更新确认帧回包的处理逻辑 */
Static void ProcessUpdateConfirmFrameAck(T_CommStruct *ptComm)
{
    INT16U BuffLen = 0;
#ifdef REMOTE_DOWNLOAD_AND_UPDATE
    char SlaveUpdateRecord[20] = {0};
#endif
    s_tPrtclDLRcv.ucRTN = GetAPPDownLoadRTNAck(ptComm);
    if(s_tPrtclDLRcv.ucRTN == ApplyRTN_ACK && s_tPrtclDLRcv.aucDataBuf[6] == ApplyRTN_ACK)
    {
        if (s_ucAgentUpdatePollingAddr != s_tPrtclDLRcv.ucSrcDevAdr) {
            s_tPrtclDLRcv.ucRTN = ApplyRTN_NOACK;
            return;
        }
#ifdef REMOTE_DOWNLOAD_AND_UPDATE
        
        if(GetUpgradeFileFlag() == UPDATE_BMS_FILE)
        {
            rt_snprintf_s(SlaveUpdateRecord, sizeof(SlaveUpdateRecord), "%d BMS updated", s_ucAgentUpdatePollingAddr);  // 升级BMS
        }
        else if(GetUpgradeFileFlag() == UPDATE_BDU_FILE)
        {
            rt_snprintf_s(SlaveUpdateRecord, sizeof(SlaveUpdateRecord), "%d BDU updated", s_ucAgentUpdatePollingAddr);  // 升级BDU
        }
        SaveAction(GetActionId(CONTOL_SLAVE_UPDATE_SUCCESSFUL), SlaveUpdateRecord); // 从机升级成功，则升级主机生成操作记录
        
#endif
        s_onSlavecount++;
        if(s_onSlavecount < s_tBMSUpdateScanResult.numInPlace) // 轮询地址切换到在位电池的下一个地址
        {
            s_ucAgentUpdatePollingAddr = s_tBMSUpdateScanResult.battInPlaceAddr[s_onSlavecount];
            #if PARALLE_UPDATE_DEBUG
            rt_kprintf("收到更新确认帧，向设备%d发送数据传输完成确认帧\n", s_ucAgentUpdatePollingAddr);
            #endif
            s_tAgentParalleDLStat = AGENT_DL_STAT_DATA_CONFIRM;
            //AgentParalleDataTransConfirmFrmAck(s_ucAgentUpdatePollingAddr);
            AgentParalleDataTransConfirmFrm(&BuffLen, s_bUpdateDataBuff, s_ucAgentUpdatePollingAddr);
            rt_memcpy(s_tPrtclDLSnd.aucApplyDataBuf, s_bUpdateDataBuff, BuffLen);
            s_tPrtclDLSnd.wApplySendDataLength = BuffLen;
        }
        else //所有的从机全部收到升级确认帧，需要执行让主机升级的动作
        {
            #if PARALLE_UPDATE_DEBUG
            rt_kprintf("收到更新确认帧，所有从机升级完成，切换状态升级主机跳boot\n");
            #endif
            // 跳boot前清除状态
            s_tAgentParalleDLStat = AGENT_DL_STAT_PROBE;
            s_ucAgentUpdatePollingAddr = 0;
            s_bAgentUpdateFlag = True;
            s_tPrtclDLRcv.ucRTN = ApplyRTN_NOACK;
        }
    }
    else
    {
        #if PARALLE_UPDATE_DEBUG
            rt_kprintf("收到更新确认帧，s_tPrtclDLRcv.ucRTN:%d, s_tPrtclDLRcv.aucDataBuf[6]:%d\n", s_tPrtclDLRcv.ucRTN, s_tPrtclDLRcv.aucDataBuf[6]);
        #endif
        s_tAgentParalleDLStat = AGENT_DL_STAT_UPDATE_CONFIRM;
        s_bAgentUpdateFlag = True;
        s_tPrtclDLRcv.ucRTN = ApplyRTN_NOACK;
    }
    return;
}


Static void ProcessUpdateConfirmFrameAck_Self(T_CommStruct *ptComm)
{
    s_tPrtclDLRcv.ucRTN = GetAPPDownLoadRTNAck(ptComm);
    if(s_tPrtclDLRcv.ucRTN == ApplyRTN_ACK && s_tPrtclDLRcv.aucDataBuf[6] == ApplyRTN_ACK)
    {
        s_bConfimeFlag = True;
#ifdef REMOTE_DOWNLOAD_AND_UPDATE
        SaveAction(GetActionId(CONTOL_SLAVE_UPDATE_SUCCESSFUL), "UEM BDU UPGRAD"); // 自己升级BDU完成生成操作记录
#endif
        
        #if PARALLE_UPDATE_DEBUG
        rt_kprintf("收到更新确认帧，切换状态升级跳boot\n");
        #endif
        // 跳boot前清除状态
        s_ucBDUDLStat = AGENT_DL_STAT_PROBE;
        s_ucAgentUpdatePollingAddr = 0;
        s_bAgentUpdateFlag = True;
        s_tPrtclDLRcv.ucRTN = ApplyRTN_NOACK;
    }
    else
    {
        #if PARALLE_UPDATE_DEBUG
            rt_kprintf("收到更新确认帧，s_tPrtclDLRcv.ucRTN:%d, s_tPrtclDLRcv.aucDataBuf[6]:%d\n", s_tPrtclDLRcv.ucRTN, s_tPrtclDLRcv.aucDataBuf[6]);
        #endif
        s_ucBDUDLStat = AGENT_DL_STAT_UPDATE_CONFIRM;
        s_ucAgentUpdatePollingAddr = 0;
        s_bAgentUpdateFlag = True;
        s_tPrtclDLRcv.ucRTN = ApplyRTN_NOACK;
    }
}



//处理传输触发帧和更新确认帧
Static void DealBMSParalleTrigger(T_CommStruct *ptComm)
{
    if (s_tPrtclDLRcv.wApplyAppEnd != 0xAA55 && s_tPrtclDLRcv.wApplyAppEnd != 0x55AA)
    {
        s_tPrtclDLRcv.ucRTN = ApplyRTN_NOACK;
        return;
    }

    // 根据功能类型和结束标志进行分发处理
    if(s_tPrtclDLRcv.ucApplyFun == FUNC_CODE_PARALLE_TRIGGER_FRM)
    {
        if(s_tPrtclDLRcv.wApplyAppEnd == 0xAA55)// 传输触发帧处理
        {
            ProcessTriggerFrame(ptComm);// 收到触发帧
        }
        else 
        {
            s_bAgentP2PAckFlag = True;
            ProcessTriggerFrameAck(ptComm);
        }
    }
    else
    {
        if(s_tPrtclDLRcv.wApplyAppEnd == 0xAA55)// 更新确认帧处理
        {
            ProcessUpdateConfirmFrame(ptComm);// 收到更新确认帧
        }
        else
        {
            s_bAgentP2PAckFlag = True;
            ProcessUpdateConfirmFrameAck(ptComm);
        }
    }
    return;
}


// 处理并发升级数据传输帧（分为从机处理主机的数据和主机处理从机的数据）
Static short DealBMSParalleTransferFrm(T_CommStruct* ptComm) {
    T_ParalleTransferMode tTransferMode = PARALLE_TRANSFER_INVALID_MODE;
    RETURN_VAL_IF_FAIL(ptComm != NULL, FAILURE);

    // （代理升级）升级主机处理从机数据传输帧回包
    // 数据传输阶段当接收的DATA域数据长度为0时说明是从机的回包
    if (s_tPrtclDLRcv.wApplyRecvDataLength == 0)
    {
        s_bAgentP2PAckFlag = True;
        DealSlaveTransferFrmAck(ptComm);
        return SUCCESSFUL;
    }
    // 没有触发成功，不接受传输帧
    if(s_tParalleDLStat == PARALLE_DL_STAT_PREPARE) {
        s_tPrtclDLRcv.ucRTN = ApplyRTN_NOACK;  
        return FAILURE;
    }
    // 返回码处理
    DealBMSParalleAppDownloadRtnFault(ptComm);
    tTransferMode = GetParalleTransferMode();
    if (s_tPrtclDLRcv.ucRTN != ApplyRTN_ACK) {
        if (PARALLE_TRANSFER_BROADCAST == tTransferMode) {
            s_tPrtclDLRcv.ucRTN = ApplyRTN_NOACK;  // 广播不回包
        }
        return FAILURE;
    }
    s_tPrtclDLRcv.wApplyLength = 0;
    // 收到首发帧处理
    DealBMSParalleFirstFrm(ptComm);
    // 收到数据帧处理
    DealBMSParalleDataFrm(ptComm);
    // 是否回包处理
    if (PARALLE_TRANSFER_BROADCAST == tTransferMode)
    {
        s_tPrtclDLRcv.ucRTN = ApplyRTN_NOACK;  // 广播不回包
    }
    else
    {
        // 如果是北向并发升级数据传输确认阶段
        if (s_tParalleDLStat == PARALLE_DL_STAT_DATA_CONFIRM || s_tParalleDLStat == PARALLE_DL_STAT_UPDATE_CONFIRM)
        {
            ComplementaryFrameAck();
        }
        else
        {
            if (s_tPrtclDLRcv.wApplyAppEnd == s_tFileManage.tFileAttr.wTotalFrameNum)
            {
                // 收到最后一帧
                s_bReceivedFinished = True;
                return SUCCESSFUL;
            }
            // 点对点回包: 请求下一帧
            s_tPrtclDLRcv.wApplyAppEnd = s_wRequestFrmNO;
            s_tPrtclDLSnd.wApplySendDataLength = 0;
        }
    }
    return SUCCESSFUL;
}

// （代理升级）升级主机处理从机数据传输帧回包
Static SHORT DealSlaveTransferFrmAck(T_CommStruct* ptComm)
{
    INT16U BuffLen = 0;
    // 主机如果接收到的从机数据传输帧回包CRC校验失败时需要将之前的补帧数据再给从机发送一次。
    if((0 == CRC_Cal(&s_tPrtclDLRcv.aucDataBuf[0], s_tPrtclDLRcv.wApplyLength)) && (s_tPrtclDLRcv.aucDataBuf[6] == ApplyRTN_ACK))
    {
        s_tPrtclDLRcv.ucRTN = ApplyRTN_ACK;
    }
    else
    {
        s_tPrtclDLRcv.ucRTN = DownloadCRCErr;
    }
    if (s_tPrtclDLRcv.ucRTN == ApplyRTN_ACK)
    {
        // 补帧阶段升级主机收到从机回包时将s_ucSupplyFrameRepeatCnt清零
        s_ucSupplyFrameRepeatCnt = 0;
        // 补帧阶段升级主机如果收到从机的请求补帧序号等于总帧数，则认为补帧结束。
        if (s_tPrtclDLRcv.wApplyAppEnd == s_tFileManage.tFileAttr.wTotalFrameNum)
        {
            // 补帧结束，直接发送更新确认帧
            #if PARALLE_UPDATE_DEBUG
            rt_kprintf("设备%d补帧结束,发送更新确认帧\n", s_ucAgentUpdatePollingAddr);
            #endif
            AgentParalleUpdateConfirmFrm(&BuffLen, s_bUpdateDataBuff, s_ucAgentUpdatePollingAddr);
            rt_memcpy(s_tPrtclDLSnd.aucApplyDataBuf, s_bUpdateDataBuff, BuffLen);
            s_tPrtclDLSnd.wApplySendDataLength = BuffLen;
            s_tAgentParalleDLStat = AGENT_DL_STAT_UPDATE_CONFIRM;
        }
        else
        {
            // 从机时，升级主机需要将补帧序号更新为从机发送回来的补帧序号。
            s_wLostFrameNum = s_tPrtclDLRcv.wApplyAppEnd;
            #if PARALLE_UPDATE_DEBUG
            rt_kprintf("发送补帧帧号%d\n", s_wLostFrameNum);
            #endif
            // 主机收到从机发回来的补帧序号后直接将对应的补帧数据发送出去
            SendSupplyDataFrame(ptComm, s_wLostFrameNum, s_ucAgentUpdatePollingAddr, DIRECT_SUPPLY);
        }
    }
    // 从机回包CRC异常时升级主机需要进入补帧重发阶段
    else
    {
        #if PARALLE_UPDATE_DEBUG
            rt_kprintf("收到补帧错误\n");
        #endif
        // 首先从机CRC错误，此处不需要回包了；
        s_tPrtclDLRcv.ucRTN = ApplyRTN_NOACK;
        // 代理升级标志置true，状态机切换到补帧阶段。
        s_bAgentUpdateFlag = true;
        s_tAgentParalleDLStat = AGENT_DL_STAT_SUPPLY_FRAME;
    }
    return SUCCESSFUL;
}


Static SHORT DealSlaveTransferFrmAck_Self(T_CommStruct* ptComm)
{
    INT16U BuffLen = 0;
    // 主机如果接收到的从机数据传输帧回包CRC校验失败时需要将之前的补帧数据再给从机发送一次。
    if ((0 == CRC_Cal(&s_tPrtclDLRcv.aucDataBuf[0], s_tPrtclDLRcv.wApplyLength)) && (s_tPrtclDLRcv.aucDataBuf[6] == ApplyRTN_ACK))
    {
        s_tPrtclDLRcv.ucRTN = ApplyRTN_ACK;
    }
    else
    {
        s_tPrtclDLRcv.ucRTN = DownloadCRCErr;
    }

    if (s_tPrtclDLRcv.ucRTN == ApplyRTN_ACK)
    {
        // 补帧阶段升级主机收到从机回包时将s_ucSupplyFrameRepeatCnt清零
        s_ucSupplyFrameRepeatCnt = 0;

        // 补帧阶段升级主机如果收到从机的请求补帧序号等于总帧数，则认为补帧结束。
        if (s_tPrtclDLRcv.wApplyAppEnd == s_tFileManage.tFileAttr.wTotalFrameNum)
        {
            // 补帧结束，直接发送更新确认帧
            #if PARALLE_UPDATE_DEBUG
            rt_kprintf("自己补帧结束,发送更新确认帧\n");
            #endif
            AgentParalleUpdateConfirmFrm(&BuffLen, s_bUpdateDataBuff, GetTotalAddr());
            s_ucBDUDLStat = AGENT_DL_STAT_UPDATE_CONFIRM;
            #if PARALLE_UPDATE_DEBUG
            rt_kprintf("DealSlaveTransferFrmAck_Self: %x\n", s_tPrtclDLRcv.ucApplyFun);
            #endif
            BMSRecvBDUParalleTransfrm_SendBduSelf(BuffLen, s_bUpdateDataBuff);
            rt_thread_delay(100);
        }
        else
        {
            // 从机时，升级主机需要将补帧序号更新为从机发送回来的补帧序号。
            s_wLostFrameNum = s_tPrtclDLRcv.wApplyAppEnd;
            #if PARALLE_UPDATE_DEBUG
            rt_kprintf("发送补帧帧号%d\n", s_wLostFrameNum);
            #endif
            // 主机收到从机发回来的补帧序号后直接将对应的补帧数据发送出去
            SendSupplyDataFrame_Self(s_wLostFrameNum, GetTotalAddr(), DIRECT_SUPPLY);
        }
    }
    else
    {
        #if PARALLE_UPDATE_DEBUG
        rt_kprintf("收到补帧错误\n");
        #endif
        // CRC错误，此处不需要回包了；
        s_tPrtclDLRcv.ucRTN = ApplyRTN_NOACK;
        // 代理升级标志置true，状态机切换到补帧阶段。
        s_bAgentUpdateFlag = TRUE;
        s_ucBDUDLStat = AGENT_DL_STAT_SUPPLY_FRAME;
    }

    return SUCCESSFUL;
}


//补帧响应回包
Static BYTE ComplementaryFrameAck(void) {
    if (s_tPrtclDLRcv.ucRTN != ApplyRTN_ACK || s_tPrtclDLRcv.wApplyAppEnd != s_wLossFrameNo) {
        s_wComplementaryRepeat++;
        s_tPrtclDLRcv.wApplyAppEnd = s_wLossFrameNo;
        s_tPrtclDLSnd.wApplySendDataLength = 0;
        return SUCCESSFUL;
    }

    //上位机始终回复非请求帧到20次，设备回到初始状态
    if(s_wComplementaryRepeat >= RETRANS_MAX_COUNT){
        s_tParalleDLStat = PARALLE_DL_STAT_PREPARE;
        s_wComplementaryRepeat = 0;
        s_tFileManage.ucFlag = FLAG_APP;
    }
        
    reset_frame_loss(s_wLossFrameNo);
    #if PARALLE_UPDATE_DEBUG
    rt_kprintf("补帧 %d 成功\n", s_wLossFrameNo);
    #endif
    s_wLossFrameNo = get_first_frame_loss();
    if (s_wLossFrameNo == INVALID_FRM_NO) {
        s_tPrtclDLRcv.wApplyAppEnd = s_tFileManage.tFileAttr.wTotalFrameNum;
        s_tParalleDLStat = PARALLE_DL_STAT_UPDATE_CONFIRM;
        #if PARALLE_UPDATE_DEBUG
        rt_kprintf("无丢帧，进入升级确认阶段, 返回总帧数:%d.等待上位机发升级确认帧.\n", s_tPrtclDLRcv.wApplyAppEnd);
        #endif
    } else {
        s_tPrtclDLRcv.wApplyAppEnd = s_wLossFrameNo;
        s_wRequestFrmNO = s_wLossFrameNo;  // 更新当前请求帧号
        #if PARALLE_UPDATE_DEBUG
        rt_kprintf("补帧后，回复丢失第 %d 帧\n", s_wLossFrameNo);
        #endif
    }
    s_tPrtclDLRcv.ucRTN = ApplyRTN_ACK;
    s_tPrtclDLSnd.wApplySendDataLength = 0;
    return SUCCESSFUL;
}

Static T_ParalleTransferMode GetParalleTransferMode(void) {
    // 透传且DEV_TYPE_BMS
    if (0 == s_tPrtclDLRcv.ucRsvDestDevAdr) {
        return PARALLE_TRANSFER_BROADCAST;  // 广播
    } else {
        return PARALLE_TRANSFER_POINT_TO_POINT;  // 点对点
    }
}

/**
 * @brief 程序更新数据传输首发帧信息处理
 * @param[in] ptComm 通信数据
 * @retval SUCCESSFUL 成功, FAILURE 失败
*/
Static short DealBMSParalleFirstFrm(T_CommStruct* ptComm) {
    WORD wDataIndex = 0;

    RETURN_VAL_IF_FAIL(ptComm != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(0 == s_tPrtclDLRcv.wApplyAppEnd, SUCCESSFUL);  // 非首发帧，返回

    // 保存文件信息
    s_tFileManage.tFileAttr.wDataLenPerFrame = GetInt16Data(&s_tPrtclDLRcv.aucDataBuf[PARALLE_PROTO_BOTTOM_DATA_INDEX]);      // 下载帧字节数
    s_tFileManage.tFileAttr.wTotalFrameNum = GetInt16Data(&s_tPrtclDLRcv.aucDataBuf[PARALLE_PROTO_BOTTOM_DATA_INDEX + 2]);    // 发送总帧数
    s_tFileManage.tFileAttr.ulTotalFileLength = GetULongData(&s_tPrtclDLRcv.aucDataBuf[PARALLE_PROTO_BOTTOM_DATA_INDEX + 4]); // 传输文件长度
    // 传输文件名
    wDataIndex = PARALLE_PROTO_BOTTOM_DATA_INDEX + 8;
    rt_memcpy_s(s_tFileManage.tFileAttr.acFileName, sizeof(s_tFileManage.tFileAttr.acFileName), &s_tPrtclDLRcv.aucDataBuf[wDataIndex], FILE_NAME_LEN);
    // 传输文件时间
    wDataIndex = PARALLE_PROTO_BOTTOM_DATA_INDEX + 8 + FILE_NAME_LEN;
    rt_memcpy_s(s_tFileManage.tFileAttr.acFileTime, sizeof(s_tFileManage.tFileAttr.acFileTime), &s_tPrtclDLRcv.aucDataBuf[wDataIndex], FILE_TIME_LEN);
    // 传输文件总校验
    wDataIndex = PARALLE_PROTO_BOTTOM_DATA_INDEX + 8 + FILE_NAME_LEN + FILE_TIME_LEN;
    s_tFileManage.tFileAttr.wFileCheck = GetInt16Data(&s_tPrtclDLRcv.aucDataBuf[wDataIndex]);

    //校验首发帧
    if (CheckFirstFrame() != SUCCESSFUL) {
        s_tPrtclDLRcv.ucRTN = DownloadFrameErr;
        return FAILURE ;
    }
 
    // 首发帧丢帧记录
    RecordBMSParalleFirstFrmLoss(ptComm);

    return SUCCESSFUL;
}

/**
 * @brief 程序更新过程数据帧处理
 * @param[in] ptComm 通信数据
 * @retval SUCCESSFUL 成功, FAILURE 失败
*/
Static short DealBMSParalleDataFrm(T_CommStruct* ptComm) {
    uint32_t ret = 0;
    RETURN_VAL_IF_FAIL(ptComm != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(s_tPrtclDLRcv.wApplyAppEnd > 0 && s_tPrtclDLRcv.wApplyAppEnd < INVALID_FRM_NO, SUCCESSFUL);  // 非数据帧，返回
    RETURN_VAL_IF_FAIL(s_tPrtclDLRcv.wApplyAppEnd >= s_wRequestFrmNO, SUCCESSFUL);  // 跳帧到小于当前帧，则舍弃该帧，返回

    // 当前帧写入升级帧缓存
    rt_memset_s(&s_tDataToCircleBuff, sizeof(T_AppDataSaveStruct), 0x00, sizeof(T_AppDataSaveStruct));
    s_tDataToCircleBuff.ulFrameNo = s_tPrtclDLRcv.wApplyAppEnd;
    rt_memcpy_s(s_tDataToCircleBuff.aucData, sizeof(s_tDataToCircleBuff.aucData), 
                &s_tPrtclDLRcv.aucDataBuf[PARALLE_PROTO_BOTTOM_DATA_INDEX], s_tPrtclDLRcv.wApplyRecvDataLength);
    ret = write_circle_buff(&s_tDataToCircleBuff, 1);

    RETURN_VAL_IF_FAIL(TRUE == ret, FAILURE);  // 无法写入缓存，作为丢帧处理

    // 收帧, 判断丢帧, 记录丢帧
    RecordBMSParalleDataFrmLoss(ptComm);

    return SUCCESSFUL;
}

/**
 * @brief 并发升级APP失败返回信息处理
 * @param[in] ptComm 通信数据
 * @retval SUCCESSFUL 成功, FAILURE 失败
*/
Static short DealBMSParalleAppDownloadRtnFault(T_CommStruct* ptComm) {
    RETURN_VAL_IF_FAIL(ptComm != NULL, FAILURE);

    if (COMM_CAN != ptComm->ucPortType)
    {
        rt_memcpy_s(&s_tPrtclDLRcv.aucDataBuf[0], s_tPrtclDLRcv.wApplyLength, 
                &s_tPrtclDLRcv.aucDataBuf[9], s_tPrtclDLRcv.wApplyLength);
    }

    if (0 == CRC_Cal(&s_tPrtclDLRcv.aucDataBuf[0], s_tPrtclDLRcv.wApplyLength)) {
        s_tPrtclDLRcv.ucRTN = ApplyRTN_ACK;
    } else {
        s_tPrtclDLRcv.ucRTN = DownloadFrameErr;
        s_tPrtclDLRcv.wApplyRecvDataLength = 0;
        RecAppDownloadRTNFault();
    }

    return SUCCESSFUL;
}

/**
 * @brief 并发升级首发帧丢帧记录
 * @param[in] ptComm 通信数据
 * @retval SUCCESSFUL 成功, FAILURE 失败
*/
Static short RecordBMSParalleFirstFrmLoss(T_CommStruct* ptComm) {
    WORD wCurrentFrmNO = s_tPrtclDLRcv.wApplyAppEnd;
    WORD i = 0;
    WORD wFrmStart = 0;
    WORD wFrmEnd = 0;
    RETURN_VAL_IF_FAIL(ptComm != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(0 == wCurrentFrmNO, SUCCESSFUL);

    // 补发帧阶段
    if (PARALLE_DL_STAT_DATA_CONFIRM == s_tParalleDLStat || s_tParalleDLStat == PARALLE_DL_STAT_UPDATE_CONFIRM) {
        if (INVALID_FRM_NO == s_lastRecivedFrmNO) {
            wFrmStart = 1;
        } else {
            wFrmStart = s_lastRecivedFrmNO + 1;
        }
        wFrmEnd = s_tFileManage.tFileAttr.wTotalFrameNum;

        // 清除首帧丢帧记录
        reset_frame_loss(0);

        // 记录数据帧丢帧
        #if PARALLE_UPDATE_DEBUG
        if (wFrmStart < wFrmEnd) {
            rt_kprintf("末尾丢帧区段: %d ~ %d \n", wFrmStart, wFrmEnd - 1);
        }
        #endif

        for (i = wFrmStart; i < wFrmEnd; i++) {
            set_frame_loss(i);
        }
    } else {
        s_lastRecivedFrmNO = 0;
        s_wRequestFrmNO = 1;
    }
    return SUCCESSFUL;
}

/**
 * @brief 并发升级数据帧丢帧记录
 * @param[in] ptComm 通信数据
 * @retval SUCCESSFUL 成功, FAILURE 失败
*/
Static short RecordBMSParalleDataFrmLoss(T_CommStruct* ptComm) {
    WORD wCurrentFrmNO = s_tPrtclDLRcv.wApplyAppEnd;
    WORD i = 0;
    WORD wFrmStart = 0;
    RETURN_VAL_IF_FAIL(ptComm != NULL, FAILURE);

    // 补发帧阶段
    if (PARALLE_DL_STAT_DATA_CONFIRM == s_tParalleDLStat || s_tParalleDLStat == PARALLE_DL_STAT_UPDATE_CONFIRM) {
        // 清除当前帧丢帧记录
        reset_frame_loss(wCurrentFrmNO);
    } else {
        // 记录首发帧丢帧,第 0 帧
        if (INVALID_FRM_NO == s_lastRecivedFrmNO) {
            wFrmStart = 0;
        } else {
            wFrmStart = s_lastRecivedFrmNO + 1;
        }

        // 记录数据帧丢帧
        #if PARALLE_UPDATE_DEBUG
        if (wFrmStart < wCurrentFrmNO) {
            rt_kprintf("丢帧区段: %d ~ %d \n", wFrmStart, wCurrentFrmNO - 1);
        }
        #endif

        for (i = wFrmStart; i < wCurrentFrmNO; i++) {
            set_frame_loss(i);
        }

        s_lastRecivedFrmNO = wCurrentFrmNO;
        if (wCurrentFrmNO == s_wRequestFrmNO) {
            s_wRequestFrmNO = wCurrentFrmNO + 1;
        }
    }

    return SUCCESSFUL;
}

/**
 * @brief 并发升级收到确认帧时对丢帧情况进行记录
 * @param[in] ptComm 通信数据
 * @retval SUCCESSFUL 成功, FAILURE 失败
*/
Static short RecordBMSParalleFrmLossWhenConfirm(T_CommStruct* ptComm) {
    WORD i = 0;
    WORD wFrmStart = 0;
    WORD wFrmEnd = 0;
    RETURN_VAL_IF_FAIL(ptComm != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(PARALLE_DL_STAT_DATA_CONFIRM == s_tParalleDLStat, SUCCESSFUL);
    // 首发帧未丢失，根据总帧数记录尾部丢失；首发帧丢失，则在补帧阶段，收到首发帧后进行统计
    if ((0 == get_frame_loss(0)) && (s_lastRecivedFrmNO != INVALID_FRM_NO)) {
        wFrmStart = s_lastRecivedFrmNO + 1;
        wFrmEnd = s_tFileManage.tFileAttr.wTotalFrameNum;
        for (i = wFrmStart; i < wFrmEnd; i++) {
            set_frame_loss(i);
        }
    }

    return SUCCESSFUL;
}

/**
 * @brief 并发升级触发帧处理（透传），区分主机以及从机的处理
 * @param[in] ptComm 通信数据
 * @retval SUCCESSFUL 成功, FAILURE 失败
*/
Static short BMSSendParalleTriggerTransProcess(T_CommStruct *ptComm){
    WORD wSendCount = 0;

    if (IsMaster()) {
         // 通过主机透传到ADDR电池
        if(s_tPrtclDLRcv.ucRsvDestDevAdr == 0){
            //主机接收到传输触发帧以后连续发送20次给从机，让从机完成触发
            for(wSendCount = 0; wSendCount < 20; wSendCount++){
                ChangeIntoCanFrm(s_tPrtclDLRcv.aucApplyDataBuf, s_tPrtclDLRcv.wApplyLength, DEV_TYPE_BMS, 0);
            }
        }
        rt_memcpy(s_tPrtclDLSnd.aucApplyDataBuf, s_tFileManage.tFileAttr.acFileName, FILE_NAME_LEN);
        s_tPrtclDLSnd.wApplySendDataLength = FILE_NAME_LEN;
        s_tPrtclDLRcv.wApplyAppEnd = 0x55AA;
    }else{
        //从机处理
        if(s_tPrtclDLRcv.ucRsvDestDevAdr == GetBMSAddr()){
            rt_memcpy(s_tPrtclDLSnd.aucApplyDataBuf, s_tFileManage.tFileAttr.acFileName, FILE_NAME_LEN);
            s_tPrtclDLSnd.wApplySendDataLength = FILE_NAME_LEN;
            s_tPrtclDLRcv.wApplyAppEnd = 0x55AA;
        }else{
            s_tPrtclDLRcv.ucRTN = ApplyRTN_NOACK;  
        }
    }
    s_tPrtclDLRcv.wApplyLength = 0;
    return SUCCESSFUL;
}

/**
 * @brief 并发升级超时退出升级模式处理
*/
short DealParalleUpdateTimeout(void)
{
    s_tParalleDLStat = PARALLE_DL_STAT_PREPARE;
    s_bReceivedFinished = False;
    s_lastRecivedFrmNO = INVALID_FRM_NO;
    s_wRequestFrmNO = 0;
    s_wLossFrameNo = 0;
    s_TickStartDownload = 0;
    ResetToInitUpdate();
    clear_frame_loss_info();
    s_tFileManage.ucFlag = FLAG_OK;  // 恢复升级灯状态
    BduCtrl(SCI_CTRL_STOP_CAN_COMM, False);  // 通知BDU恢复CAN通信
    SaveAction(GetActionId(CONTOL_SLAVE_UPDATE_SUCCESSFUL), "ParalleUpdateFail");

    return SUCCESSFUL;
}

/**
 * @brief 并发升级超时处理回调
*/
Static void DealParalleTimeout(void) {
    DealParalleUpdateTimeout();
    #if PARALLE_UPDATE_DEBUG
    rt_kprintf("并发升级 %d 分钟超时\n", PARALLE_TIMEOUT_TICKS / 60000);
    #endif
}

Static short StartParalleTimeoutTimer(rt_timer_t pTimer) {
    static BYTE timer_inited = FALSE;
    RETURN_VAL_IF_FAIL(pTimer != NULL, FAILURE);
    // 触发成功进入传输阶段，防止多触发帧情况下频繁启停定时器
    RETURN_VAL_IF_FAIL(s_tParalleDLStat != PARALLE_DL_STAT_TRANS, SUCCESSFUL);
    if (FALSE == timer_inited) {
        rt_timer_init(pTimer, "ParalleTimeout", (void*)DealParalleTimeout, RT_NULL, PARALLE_TIMEOUT_TICKS, RT_TIMER_FLAG_ONE_SHOT | RT_TIMER_FLAG_SOFT_TIMER);
        timer_inited = TRUE;
    }
    rt_timer_stop(pTimer);
    rt_timer_start(pTimer);
    return SUCCESSFUL;
}

/**
 * @brief 接收并发升级触发帧（区分广播与透传）
 * @param[in] ptComm 通信数据
*/
Static void  BMSSendParalleTrigger(T_CommStruct *ptComm){
    if (s_tPrtclDLRcv.ucRsvTrans == 0) {
        s_tPrtclDLRcv.ucRTN = TriggerEnd;
        s_bReceivedFinished = False;
        s_lastRecivedFrmNO = INVALID_FRM_NO;  // 触发成功，清除上次收到的帧号
        clear_frame_loss_info();              // 触发成功，清除丢帧记录
        s_wRequestFrmNO = 0;                  // 触发成功，下一帧首发帧
        rt_memcpy(s_tPrtclDLSnd.aucApplyDataBuf, s_tFileManage.tFileAttr.acFileName, FILE_NAME_LEN);
        s_tPrtclDLSnd.wApplySendDataLength = FILE_NAME_LEN;
        s_tPrtclDLRcv.wApplyAppEnd = 0x55AA;
        StartParalleTimeoutTimer(&s_ParalleOvertimeTimer);
        #if PARALLE_UPDATE_DEBUG
        s_TickStartDownload = rt_tick_get();
        rt_kprintf("传输触发成功\n");
        #endif

    } else if (s_tPrtclDLRcv.ucRsvDestDevType == DEV_TYPE_BMS && s_tPrtclDLRcv.ucRsvTrans == 0xFF) {
       BMSSendParalleTriggerTransProcess(ptComm);
       StartParalleTimeoutTimer(&s_ParalleOvertimeTimer);
    }
    s_tParalleDLStat = PARALLE_DL_STAT_TRANS;
    ResetToInitUpdate();
    if (s_tFileManage.ucFlag != FLAG_NOR_IAP)
    {
        s_tFileManage.ucFlag = FLAG_NOR_IAP;
    }
    // 通知BDU停止CAN通信
    BMSParalleCtrlBduCanComm(ptComm, True);
    return;
}

BOOLEAN GetTriggerEndFlag(void)
{
    return (s_tParalleDLStat != PARALLE_DL_STAT_PREPARE)?True:False;
}


/**
 * @brief 升级确认帧从机处理
 * @param[in] ptComm 通信数据
 * @retval SUCCESSFUL 成功, FAILURE 失败
*/
Static short  UpdateConfirmTransHandleSlaveProcess(T_CommStruct *ptComm){
    if(s_tPrtclDLRcv.ucRsvDestDevAdr == GetBMSAddr()){
        //回包
        ParalleUpdateConfirmProcess(ptComm);
    }else{
        s_tPrtclDLRcv.ucRTN = ApplyRTN_NOACK;
    }
    return SUCCESSFUL;
}

/**
 * @brief 升级确认帧透传处理（区分主机处理以及从机处理）
 * @param[in] ptComm 通信数据
 * @retval SUCCESSFUL 成功, FAILURE 失败
*/
Static short BMSSendParalleUpdateConfirmTransProcess(T_CommStruct *ptComm){
    if (IsMaster()) {    
        ParalleUpdateConfirmProcess(ptComm);
    }else{
        UpdateConfirmTransHandleSlaveProcess(ptComm);
    }
    return SUCCESSFUL;
}

/**
 * @brief 在升级确认状态进行crc校验，校验通过才可以跳转boot进行程序更新
 * @param[in] NULL  
 * @retval True 校验通过, False 校验不通过
*/
Static BOOLEAN CompletePackageCrcCheckRes(){
    unsigned short ulCrc  = CRC_Cal_NOR(NORFLASH_APP_START, s_tFileManage.tFileAttr.ulTotalFileLength);
    #if PARALLE_UPDATE_DEBUG
    rt_kprintf("ulCrc:%d  s_tFileManage.tFileAttr.wFileCheck: %d\n",ulCrc, s_tFileManage.tFileAttr.wFileCheck);
    #endif
    if(ulCrc == s_tFileManage.tFileAttr.wFileCheck){
        #if PARALLE_UPDATE_DEBUG
        rt_kprintf("Crc check success!! ");
        #endif
        return True;
    }else{
        #if PARALLE_UPDATE_DEBUG
        rt_kprintf("Crc check failed!! ");
        #endif
        return False;
    }
}
/**
 * @brief 升级确认帧回包以及crc校验，后续跳转boot进行程序更新
 * @param[in] ptComm 通信数据
 * @retval SUCCESSFUL 成功, FAILURE 失败
*/
Static short ParalleUpdateConfirmProcess(T_CommStruct *ptComm){
    rt_memcpy(s_tPrtclDLSnd.aucApplyDataBuf, s_tFileManage.tFileAttr.acFileName, FILE_NAME_LEN);
    s_tPrtclDLSnd.wApplySendDataLength = FILE_NAME_LEN;
    s_tPrtclDLRcv.wApplyAppEnd = 0x55AA;
    if(CompletePackageCrcCheckRes() == False){
        s_tPrtclDLRcv.ucRTN = DownloadCRCErr;
        PackAndSend(ptComm);
    }else{
        s_tPrtclDLRcv.ucRTN = ApplyRTN_ACK;
        PackAndSend(ptComm);
        //延时等待回包完成
        rt_thread_delay(1000);
        // 跳boot前清除状态
        s_tParalleDLStat = PARALLE_DL_STAT_PREPARE;
        //跳转boot,执行升级
        writeFileManageInfo(s_tFileManage);
        BeginDownload( FLAG_NOR_IAP );
    }
    return SUCCESSFUL;
}


/**
 * @brief 并发升级控制CAN通信启停
 * @param[in] ptComm 通信数据
 * @param[in] ctrl 控制命令（1停止，0恢复）
 * @retval SUCCESSUFL 成功，FAILURE 失败
*/
Static short BMSParalleCtrlBduCanComm(T_CommStruct *ptComm, BYTE ctrl) {
    RETURN_VAL_IF_FAIL(ptComm != NULL, FAILURE);

    if (COMM_CAN == ptComm->ucPortType) {
        if (BduCtrl(SCI_CTRL_STOP_CAN_COMM, ctrl)) {
            return SUCCESSFUL;
        } else {
            return FAILURE;
        }
    }

    return SUCCESSFUL;
}


/**
 * @brief 接收更新确认帧，区分广播与透传
 * @param[in] ptComm 通信数据
 * @retval SUCCESSFUL 成功, FAILURE 失败
*/
Static short  BMSSendParalleUpdateConfirm(T_CommStruct *ptComm){
    #if PARALLE_UPDATE_DEBUG
    rt_tick_t tickEndDownload = rt_tick_get();
    rt_kprintf("收到程序更新触发帧，帧号:%x, 收到帧数:%d, 收到时刻:%d, 总用时:%d ms\n", s_tPrtclDLRcv.wApplyAppEnd, s_lastRecivedFrmNO, tickEndDownload, tickEndDownload - s_TickStartDownload);
    #endif

    // 通知BDU恢复CAN通信
    BMSParalleCtrlBduCanComm(ptComm, False);

    if(s_tParalleDLStat != PARALLE_DL_STAT_UPDATE_CONFIRM){
        s_tPrtclDLRcv.ucRTN = ApplyRTN_NOACK;  
        return FAILURE;
    }
    //广播处理
    if (s_tPrtclDLRcv.ucRsvTrans == 0) {
        ParalleUpdateConfirmProcess(ptComm);
    } else if (s_tPrtclDLRcv.ucRsvDestDevType == DEV_TYPE_BMS && s_tPrtclDLRcv.ucRsvTrans == 0xFF) {
        // 透传处理
        BMSSendParalleUpdateConfirmTransProcess(ptComm);
    }

    return SUCCESSFUL;
}

static void DealBMSTrigger(T_CommStruct *ptComm)
{
    s_tPrtclDLRcv.ucRTN = GetAPPDownLoadRTN(ptComm);

    if(s_tPrtclDLRcv.ucRTN == ApplyRTN_ACK )
    {
        s_tPrtclDLRcv.wApplyLength = 0;
        BMUSendTrigger(ptComm);
    }
    else
    {
        s_tPrtclDLRcv.wApplyRecvDataLength = 0;
        RecAppDownloadRTNFault();
    }
    return;
}

Static int CheckFirstFrame(void) {
    unsigned short total_frame_num = 0;
    unsigned short frameLenth = 0;

    frameLenth = s_tFileManage.tFileAttr.wDataLenPerFrame;
    //每帧长度校验
    if (frameLenth != 256 && frameLenth != 128) {
        return FAILURE;
    }
    //文件名校验
    if (strncmp(s_tFileManage.tFileAttr.acFileName, s_ucFileNameBin, FILE_NAME_LEN) != 0) {
        return FAILURE;
    }
    //总帧数校验
    total_frame_num = s_tFileManage.tFileAttr.ulTotalFileLength/frameLenth +1;
    if (s_tFileManage.tFileAttr.ulTotalFileLength % frameLenth > 0) {
        total_frame_num += 1;
    }
    if (s_tFileManage.tFileAttr.wTotalFrameNum != total_frame_num) {
        return FAILURE;
    }
    return SUCCESSFUL;
}


static BOOLEAN IsBMSParalle(BYTE ucApplyFun)
{
    return (ucApplyFun == 0x80 || 
            ucApplyFun == FUNC_CODE_PARALLE_TRIGGER_FRM ||
            ucApplyFun == FUNC_CODE_PARALLE_DATA_CONFIRM_FRM ||
            ucApplyFun == FUNC_CODE_PARALLE_UPDATE_CONFIRM_FRM);
}



static BOOLEAN IsBDUParalle(BYTE ucApplyFun)
{
    return (ucApplyFun == FUNC_CODE_PARALLE_FRONT_TRIGGER_FRM || 
            ucApplyFun == FUNC_CODE_PARALLE_FRONT_DATA_CONFIRM_FRM || 
            ucApplyFun == FUNC_CODE_PARALLE_FRONT_UPDATE_CONFIRM_FRM);
}



static BOOLEAN IsBootUpgrade(BYTE ucApplyFun)
{
    return (ucApplyFun == 0x82);
}



Static BYTE GetAPPDownLoadRTN(T_CommStruct *ptComm)
{
    char *pcFileName = s_tFileManage.tFileAttr.acFileName;
    BYTE *p = s_tPrtclDLRcv.aucDataBuf;

    if (COMM_CAN != ptComm->ucPortType)
    {
        p += 9;
    }

    if (CRC_Cal(p, 54))
    {
        return DownloadCRCErr;
    }

    if (s_tPrtclDLRcv.wApplyAppEnd != 0xAA55)
    {
        return DownloadFrameErr;
    }

    if (s_tPrtclDLRcv.wApplyRecvDataLength == FILE_NAME_LEN)
    {
        p += 12;
        if(IsBMSParalle(s_tPrtclDLRcv.ucApplyFun) == True)
        {
            rt_memcpy(pcFileName, s_ucFileNameBin, FILE_NAME_LEN);
            rt_memcpy(s_aucFileName, p, FILE_NAME_LEN);

            if (rt_memcmp(p, s_ucFileNameBin, FILE_NAME_LEN) == 0)
            {
                return ApplyRTN_ACK;
            }
            else if(rt_strstr((char *)s_aucFileName, DIFF_TAG) != NULL)
            {
                rt_memcpy(pcFileName, s_aucFileName, FILE_NAME_LEN);
                return ApplyRTN_ACK;
            }
        }
        else if(IsBDUParalle(s_tPrtclDLRcv.ucApplyFun) == True)
        {
            rt_memcpy(pcFileName, s_ucFileBDUNameBin, FILE_NAME_LEN);
            rt_memcpy(s_aucFileName, p, FILE_NAME_LEN);

            if (rt_memcmp(p, s_ucFileBDUNameBin, FILE_NAME_LEN) == 0)
            {
                return ApplyRTN_ACK;
            }
            else if(rt_strstr((char *)s_aucFileName, DIFF_TAG) != NULL)
            {
                rt_memcpy(pcFileName, s_aucFileName, FILE_NAME_LEN);
                return ApplyRTN_ACK;
            }
        }
        else if(IsBootUpgrade(s_tPrtclDLRcv.ucApplyFun) == True)
        {
            rt_memcpy(pcFileName, s_ucBootFile, FILE_NAME_LEN);
            if (rt_memcmp(p, s_ucBootFile, FILE_NAME_LEN) == 0)
            {
                s_tFileManage.ucFlag = FLAG_IAP;
                return ApplyRTN_ACK;
            }
        }
    }
    return DownFileNameErr;
}



Static BYTE GetAPPDownLoadRTNAck(T_CommStruct *ptComm)
{
    char *pcFileName = s_tFileManage.tFileAttr.acFileName;
    BYTE *p = s_tPrtclDLRcv.aucDataBuf;

    if (COMM_CAN != ptComm->ucPortType)
    {
        p += 9;
    }

    if (CRC_Cal(p, 54))
    {
        return DownloadCRCErr;
    }

    if (s_tPrtclDLRcv.wApplyAppEnd != 0x55AA)
    {
        return DownloadFrameErr;
    }

    if (s_tPrtclDLRcv.wApplyRecvDataLength == FILE_NAME_LEN)
    {
        p += 12;
        switch(s_tPrtclDLRcv.ucApplyFun)
        {
            case FUNC_CODE_PARALLE_TRIGGER_FRM:
            case FUNC_CODE_PARALLE_DATA_CONFIRM_FRM:
            case FUNC_CODE_PARALLE_UPDATE_CONFIRM_FRM:
                rt_memcpy_s(pcFileName, FILE_NAME_LEN, s_ucFileNameBin, FILE_NAME_LEN);
                rt_memcpy_s(s_aucFileName, FILE_NAME_LEN, p, FILE_NAME_LEN);

                if (rt_memcmp(p, s_ucFileNameBin, FILE_NAME_LEN) == 0)
                {
                    return ApplyRTN_ACK;
                }
                else if(rt_strstr((char *)s_aucFileName, DIFF_TAG) != NULL)
                {
                    rt_memcpy_s(pcFileName, FILE_NAME_LEN, s_aucFileName, FILE_NAME_LEN);
                    return ApplyRTN_ACK;
                }
                break;
            case FUNC_CODE_PARALLE_FRONT_TRIGGER_FRM:
            case FUNC_CODE_PARALLE_FRONT_DATA_CONFIRM_FRM:
            case FUNC_CODE_PARALLE_FRONT_UPDATE_CONFIRM_FRM:
                rt_memcpy_s(pcFileName, FILE_NAME_LEN, s_ucFileBDUNameBin, FILE_NAME_LEN);
                rt_memcpy_s(s_aucFileName, FILE_NAME_LEN, p, FILE_NAME_LEN);

                if (rt_memcmp(p, s_ucFileBDUNameBin, FILE_NAME_LEN) == 0)
                {
                    return ApplyRTN_ACK;
                }
                else if(rt_strstr((char *)s_aucFileName, DIFF_TAG) != NULL)
                {
                    rt_memcpy_s(pcFileName, FILE_NAME_LEN, s_aucFileName, FILE_NAME_LEN);
                    return ApplyRTN_ACK;
                }
                break;
        }
    }
    return DownFileNameErr;
}


Static BYTE AgentGetAPPCommonRTN(T_CommStruct *ptComm, WORD wFrameLength)
{
    BYTE *p = s_tPrtclDLRcv.aucDataBuf;

    if (COMM_CAN != ptComm->ucPortType)
    {
        p+=9;
    }

    if (CRC_Cal(p, wFrameLength))
    {
        return DownloadCRCErr;
    }

    if (s_tPrtclDLRcv.wApplyAppEnd != 0x55AA)
    {
        return DownloadFrameErr;
    }

    return ApplyRTN_ACK;
}

Static BYTE GetAPPCommonRTN(T_CommStruct *ptComm, WORD wFrameLength)
{
    BYTE *p = s_tPrtclDLRcv.aucDataBuf;

    if (COMM_CAN != ptComm->ucPortType)
    {
        p+=9;
    }

    if (CRC_Cal(p, wFrameLength))
    {
        return DownloadCRCErr;
    }

    if (s_tPrtclDLRcv.wApplyAppEnd != 0xAA55)
    {
        return DownloadFrameErr;
    }

    return ApplyRTN_ACK;
}

static void BMUSendTrigger(T_CommStruct *ptComm)
{
    s_ucTriggerTimeOut = 0;
    s_ucTriggerRepeat += 1;

    if (s_ucTriggerRepeat >= TRIGGER_COUNTER)
    {
        if (s_ucTriggerRepeat == TRIGGER_COUNTER)
        {
            s_tPrtclDLRcv.ucRTN = TriggerEnd;
        }
        else
        {
            s_tPrtclDLRcv.ucRTN = JumpBootFail;
        }
    }

    if(s_ucTriggerRepeat == TRIGGER_COUNTER && rt_memcmp(s_tFileManage.tFileAttr.acFileName, s_ucBootFile, FILE_NAME_LEN) == 0)
    {
        SaveAction(GetActionId(CONTOL_BOOT_UPDATE), "BootUpdate");
    }

    //rt_memcpy(s_tPrtclDLRcv.aucDataBuf, s_tFileManage.tFileAttr.acFileName, FILE_NAME_LEN); //Change Expected update FileName to bin, by xzx
    rt_memcpy(s_tPrtclDLSnd.aucApplyDataBuf, s_tFileManage.tFileAttr.acFileName, FILE_NAME_LEN);

    s_tPrtclDLSnd.wApplySendDataLength = FILE_NAME_LEN;
    s_tPrtclDLRcv.wApplyAppEnd = 0x55AA;

    if (s_tFileManage.ucFlag != FLAG_IAP)
    {
        BeginDownload( (COMM_CAN == ptComm->ucPortType)? FLAG_CAN_IAP : FLAG_IAP );
    }
    return;
}

Static SHORT ReplyParalleUpdateConfirmFrm(T_CommStruct *ptComm)
{
    WORD wReplyNum = 10;
    WORD i = 0;
    RETURN_VAL_IF_FAIL(ptComm != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(ptComm->wSendLength > 0, FAILURE);
    RETURN_VAL_IF_FAIL(FUNC_CODE_PARALLE_UPDATE_CONFIRM_FRM == s_tPrtclDLRcv.ucApplyFun && 0x55AA == s_tPrtclDLRcv.wApplyAppEnd, FAILURE);
    for (i = 0; i < wReplyNum; i++)
    {
        switch (s_bPrtclDLLinkType)
        {
        case DL_LINK_TYPE_CAN1:
            ChangeIntoCanFrm(ptComm->aucSendBuf, ptComm->wSendLength, ptComm->ucLinkSrcDev, ptComm->ucLinkSrcAdr);
            break;
        #ifdef USE_CAN2
        case DL_LINK_TYPE_CAN2:
            ChangeIntoCan2Frm(ptComm->aucSendBuf, ptComm->wSendLength, ptComm->ucLinkSrcDev, ptComm->ucLinkSrcAdr);
            break;
        #endif
        case DL_LINK_TYPE_COMM:
        case DL_LINK_TYPE_NONE:
            return FAILURE;
        }
    }
    rt_thread_delay(5000);  // CAN总线占用率高，增加等待回包时间
    ptComm->wSendLength = 0;
    return SUCCESSFUL;
}

BOOLEAN IsMasterTrans(BYTE ucRsvDestDevAdr, BYTE ucFunc) {
    //预留设备地址为本机
    if (ucRsvDestDevAdr == GetBMSAddr()) {
        return False;
    }
    //预留设备地址为0，且功能码为数据传输触发帧或更新确认帧
    if (ucRsvDestDevAdr == getGrpAddr() && (ucFunc == FUNC_CODE_PARALLE_TRIGGER_FRM || ucFunc == FUNC_CODE_PARALLE_FRONT_TRIGGER_FRM)) {
        return False;
    }
    return True;
}

static void PackAndSend(T_CommStruct *ptComm)
{
    WORD wCrc;
    BYTE *p;

    if(COMM_CAN == ptComm->ucPortType)
    {
        p = ptComm->aucSendBuf;
    }
    else
    {
        p = s_tPrtclDLSnd.aucDataBuf;
    }

    *(p++) = DownloadType;
    *(p++) = BMS_Type;
    *(p++) = GetBMSDLAddr();
    *(p++) =  s_tPrtclDLRcv.ucApplyFun;
    *(p++) = (s_tPrtclDLRcv.wApplyAppEnd >> 8) & 0xff;
    *(p++) = s_tPrtclDLRcv.wApplyAppEnd & 0xff;
    *(p++) = s_tPrtclDLRcv.ucRTN;
    //*(p++) = DOWNLOADSHEETSN;
    //*(p++) = 0;
    //*(p++) = 0;
    *(p++) = s_tPrtclDLRcv.ucRsvDestDevType;
    *(p++) = s_tPrtclDLRcv.ucRsvDestDevAdr;
    *(p++) = s_tPrtclDLRcv.ucRsvTrans;
    *(p++) = (s_tPrtclDLSnd.wApplySendDataLength >> 8) & 0xff;
    *(p++) =  s_tPrtclDLSnd.wApplySendDataLength & 0xff;

    //rt_memcpy( p, s_tFileManage.tFileAttr.acFileName, s_tPrtclDLSnd.wApplySendDataLength );
    rt_memcpy( p, s_tPrtclDLSnd.aucApplyDataBuf, s_tPrtclDLSnd.wApplySendDataLength );
    p += s_tPrtclDLSnd.wApplySendDataLength;
    if(COMM_CAN != ptComm->ucPortType)
    {
        wCrc = CRC_Cal(s_tPrtclDLSnd.aucDataBuf, p - s_tPrtclDLSnd.aucDataBuf);
    }else{
        wCrc = CRC_Cal(ptComm->aucSendBuf, p - ptComm->aucSendBuf);
    }
    *(p++) = wCrc & 0xFF;
    *(p++) = wCrc>>8;
    if(COMM_CAN != ptComm->ucPortType)
    {
        if (ApplyRTN_NOACK != s_tPrtclDLRcv.ucRTN && s_tPrtclDLRcv.ucDestDevAdr != 0){
            s_tPrtclDLSnd.wApplyLength = p - s_tPrtclDLSnd.aucDataBuf;
            CovertDataToSend(ptComm);
            sendCommData( ptComm );
        }
    }
    else
    {
        if (ApplyRTN_NOACK == s_tPrtclDLRcv.ucRTN)
        {
            ptComm->wSendLength = 0;  // 不回包处理
        }
        else
        {
            ptComm->wSendLength = p - ptComm->aucSendBuf;
             // 如果s_bAgentP2PAckFlag为true需要开启定时器，避免从机没有回包无法进入代理升级状态机中执行重发机制。
            if (s_bAgentP2PAckFlag == True)
            {
                if (s_AgentAckOverTimeTimer == NULL)
                {
                    s_AgentAckOverTimeTimer = rt_timer_create("AgentAckOverTime", (void (*)(void *))AgentAckTimeOutCallFunc, NULL, RT_TICK_PER_SECOND * 2, RT_TIMER_FLAG_ONE_SHOT);
                }
                else
                {
                    rt_timer_stop(s_AgentAckOverTimeTimer);
                }
                rt_timer_start(s_AgentAckOverTimeTimer);

                ChangeIntoCanFrm(ptComm->aucSendBuf, ptComm->wSendLength, DEV_TYPE_BMS, s_ucAgentUpdatePollingAddr);
                ptComm->wSendLength = 0;
                return;
            }
            ReplyParalleUpdateConfirmFrm(ptComm);
        }
    }

    return;
}

static void CovertDataToSend(T_CommStruct *ptComm)
{
    WORD    wCRCTemp;
    BYTE    i;
    BYTE    aucTmp[256];

    aucTmp[0]   = SOI;
    aucTmp[1]   = BMS_Type;
    aucTmp[2]   = GetBMSDLAddr();
    aucTmp[3]   = s_tPrtclDLRcv.ucSrcDevType;
    aucTmp[4]   = s_tPrtclDLRcv.ucSrcDevAdr;
    aucTmp[5]   = 0x80;
    aucTmp[6]   = 0x00;
    aucTmp[7]   = 0x00;
    aucTmp[8]   = (s_tPrtclDLSnd.wApplyLength >> 0x08) & 0xff;
    aucTmp[9]   = s_tPrtclDLSnd.wApplyLength & 0xff;

    if (s_tPrtclDLSnd.wApplyLength != 0)
    {
        rt_memcpy(&aucTmp[10], s_tPrtclDLSnd.aucDataBuf, s_tPrtclDLSnd.wApplyLength);
    }

    i = 10 + s_tPrtclDLSnd.wApplyLength;
    wCRCTemp = CRC_Cal(&aucTmp[1], i - 1);
    aucTmp[i++] = wCRCTemp & 0xff;
    aucTmp[i++] = (wCRCTemp >> 8) & 0xff;
    aucTmp[i] = EOI;
    s_tPrtclDLSnd.wDataLength = i + 1; //换码前的数据总长度

    ptComm->wSendLength = TransCodingFunc(aucTmp, 256, ptComm->aucSendBuf, 512, s_tPrtclDLSnd.wDataLength);

    s_tPrtclDLSnd.wApplyLength = 0;      // 清空协议层
    return;
}

static void RecAppDownloadRTNFault(void)
{
    if(s_tPrtclDLRcv.ucRTN == DownFileNameErr)
    {
        s_tPrtclDLSnd.wApplySendDataLength = FILE_NAME_LEN;
        s_tPrtclDLRcv.wApplyAppEnd = 0x55AA;
        switch(s_tPrtclDLRcv.ucApplyFun)
        {
            case 0x80:  //app
            case 0x91:  //app
            case FUNC_CODE_PARALLE_UPDATE_CONFIRM_FRM:   //app
                rt_memcpy_s(s_tPrtclDLSnd.aucApplyDataBuf, sizeof(s_tPrtclDLSnd.aucApplyDataBuf), s_ucFileNameBin, sizeof(s_ucFileNameBin));
                break;
            case 0x82:  //boot
                rt_memcpy_s(s_tPrtclDLSnd.aucApplyDataBuf, sizeof(s_tPrtclDLSnd.aucApplyDataBuf), s_ucBootFile, sizeof(s_ucBootFile));
                break;
        }

    }
    else
    {
        s_tPrtclDLSnd.wApplySendDataLength = 0;
        s_tPrtclDLRcv.wApplyAppEnd = 0x55AA;
    }

    return ;
}

void BeginDownload( BYTE ucMode )
{
    if (s_tFileManage.ucFlag == FLAG_IAP)   //正在进行在线升级
    {
        return;
    }

    if (SaveUpgradefileInfo(ucMode))
    {
        return;
    }

    if (ucMode == FLAG_BACKUP)
    {
        ResetMCU( NO_RESET_CSU_BACKUP );
    }
    else
    {
        ResetMCU( NO_RESET_CSU_UPGRADE );
    }
}

void CheckUpdateTimeOut(void)
{
    if ( ++s_ucTriggerTimeOut > 150 )
    {
        s_ucTriggerRepeat = 0;
        s_ucTriggerTimeOut = 0;
    }

    if (0 == s_ucTriggerRepeat)
    {
        s_ucTriggerTimeOut = 0;
    }
    
    if (s_tFileManage.ucFlag == FLAG_IAP)
    return;
}

BOOLEAN IsUpdate(void)
{
    return (s_tFileManage.ucFlag == FLAG_IAP|| s_tFileManage.ucFlag == FLAG_NOR_IAP);
}

#ifdef DEVICE_USING_R321
void UpgradeMode(void)
{
#if !defined(KW_CHECK)
    BYTE ucBattChargeMode;
    Button_State u_tButSta;
    T_SysPara   tSysPara;
    ip_addr_t ipaddr, netmask, gw;
    struct netdev *pDev = netdev_get_by_name("e0");
    if (pDev == RT_NULL)
    {
        return;
    }

    //ucLocalIPGetMode = getLocalIPGetMode();
    GetSysPara(&tSysPara);
    ucBattChargeMode = getBattChargeMode();
    // 离线状态不处理近端网口通信请求
    if (BATT_MODE_OFFLINE == ucBattChargeMode)
    {
        return;
    }
    GetButtonState(&u_tButSta);
    // 圆键按键15-25s，切换IP、子网掩码、网关，进入升级模式只调用一次CheckSW，实现退出时不考虑结束时间
    if (!s_bSwUpgrade && u_tButSta.ucCurrentMode == BUT_UPGRADEMODE)
    {
        rt_thread_t TaskItem, tThreadTimeout;
         // 当前为动态ip的话，则需先禁用动态ip获取
        if (tSysPara.ucLocalIPGetMode == DYNAMIC_IP_MODE)
        {
            netdev_dhcp_enabled(pDev, RT_FALSE);
        }

        s_bSwUpgrade = True;
        IP4_ADDR(&ipaddr, 192, 168, 0, 2);
        IP4_ADDR(&netmask, 255, 255, 255, 0);
        IP4_ADDR(&gw, 192, 168, 0, 1);
        netdev_set_ipaddr(pDev, &ipaddr);
        netdev_set_netmask(pDev, &netmask);
        netdev_set_gw(pDev, &gw);

        // ip发生变化，需先断开之前参数ip建立的套接字
        close1363socket(True, True);

        TaskItem = rt_thread_create("Download", processDownload, RT_NULL, 4096 , THREAD_PRIORITY_UPDATE, THREAD_TIMESLICE_UPDATE);
        if (TaskItem != RT_NULL)
        {
            rt_thread_startup(TaskItem);
        }

        // 超时线程，用于处理各种超时情况
        tThreadTimeout = rt_thread_create("TimeOut", TimeOutCounterThread, RT_NULL, 4096, THREAD_PRIORITY_UPDATE, THREAD_TIMESLICE_UPDATE);
        if (tThreadTimeout != RT_NULL)
        {
            rt_thread_startup(tThreadTimeout);
        }
    }
#endif
    return;
}

//退出近端网口升级模式时需要将s_bSwUpgrade置为Flase。
void SetSwUpgradeFlag(BOOLEAN bSwUpgradeFlag)
{
    s_bSwUpgrade = bSwUpgradeFlag;
}


BOOLEAN GetSwUpgradeFlag(void)
{
    return s_bSwUpgrade;
}

#endif

void SetPrtclDLLinkType(T_PrtclDLLinkType bPrtclDLLinkType) {
   s_bPrtclDLLinkType = bPrtclDLLinkType;
}

// 代理升级超时回调函数
Static void AgentAckTimeOutCallFunc(void *parameter)
{
    (void)parameter;

    s_bAgentUpdateFlag = True;
}

void Process_DL_Main(T_CommStruct *ptComm)
{
    if(s_bAgentUpdateFlag) {
        s_bAgentUpdateFlag = False;
        AgentUpdateMain(ptComm);
        if(s_tAgentParalleDLStat == AGENT_DL_STAT_PROBE || s_tAgentParalleDLStat == AGENT_DL_STAT_PREPARE)
        {
            return;
        }
        if(s_tAgentParalleDLStat != AGENT_DL_STAT_DATA_TRANS){
            // 点对点发包时要进行定时器计时，超时后重新执行Process_DL_Main
            if(s_AgentAckOverTimeTimer == NULL)
            {
                s_AgentAckOverTimeTimer = rt_timer_create("AgentAckOverTime", AgentAckTimeOutCallFunc, NULL, RT_TICK_PER_SECOND * 2, RT_TIMER_FLAG_ONE_SHOT);
            }
            else
            {
                rt_timer_stop(s_AgentAckOverTimeTimer);
            }
            rt_timer_start(s_AgentAckOverTimeTimer);
            ChangeIntoCanFrm(ptComm->aucSendBuf, ptComm->wSendLength, DEV_TYPE_BMS, s_ucAgentUpdatePollingAddr);
        }
    }
}


Static INT32S UpgradeSelfBDU()
{
    switch(s_ucBDUDLStat)
    {
        case AGENT_DL_STAT_PREPARE:
            BDUDownloadPrepare_Self();
            break;
        case AGENT_DL_STAT_TRIGING:
            TrigRepeatProcess_Self();
            break;
        case AGENT_DL_STAT_DATA_TRANS:
            SendDataFrame_Self();
            break;
        case AGENT_DL_STAT_DATA_CONFIRM:
        case AGENT_DL_STAT_UPDATE_CONFIRM:
            SendConfirmRepeatProcess_Self();
            break;
        case AGENT_DL_STAT_SUPPLY_FRAME:
            // 正常的补帧是在收到从机回包之后直接发送出去，在状态机中发送补帧数据主要是应对从机超时未回包的情形。
            SendSupplyProcess_Self();
            break;
        case AGENT_DL_STAT_PROBE:
            ResetMCU(NO_RESET_BDU_UPGRADE);
            break;
        default:
            break;
    }
    return 0;
}



Static BOOLEAN ResetBootUpgrade(void)
{
    if(GetUpgradeFileFlag() == UPDATE_BMS_FILE)
    {
        BeginDownload(FLAG_NOR_IAP);
    }
    else if(GetUpgradeFileFlag() == UPDATE_BDU_FILE)
    {
        T_BCMAlarmStruct tBCMAlm = {0};
        T_BattResult tBattResult = {0};

        GetRealAlarm(BCM_ALARM_REAL, (BYTE *)&tBCMAlm);
        GetBattResult(&tBattResult);

#ifndef BMS_DEBUG_ALL_ALARM_ON
        if(tBCMAlm.ucBDUBattLockAlm == FAULT || tBattResult.ucCellProtectStatus == FAULT)
        {
            return False;
        }
#endif

        SetPrtclDLLinkType(DL_LINK_TYPE_SCI);
        UpgradeSelfBDU();
        s_bAgentUpdateFlag = True;
    }
    return True;
}


Static INT32S AgentUpdateMain(T_CommStruct *ptComm)
{
    switch(s_tAgentParalleDLStat)
    {
        // 准备阶段
        case AGENT_DL_STAT_PREPARE:
            AgentDownloadPrepare(ptComm);
            break;

        // 数据传输触发阶段
        case AGENT_DL_STAT_TRIGING:
            TrigRepeatProcess(ptComm);
            break;

        // 数据传输阶段
        case AGENT_DL_STAT_DATA_TRANS:
            SendDataFrame(ptComm);
            break;

        // 发送数据传输确认帧、升级触发帧阶段
        case AGENT_DL_STAT_DATA_CONFIRM:
        case AGENT_DL_STAT_UPDATE_CONFIRM:
            SendConfirmRepeatProcess(ptComm);
            break;

        // 补帧阶段
        case AGENT_DL_STAT_SUPPLY_FRAME:
            // 正常的补帧是在收到从机回包之后直接发送出去，在状态机中发送补帧数据主要是应对从机超时未回包的情形。
            SendSupplyProcess(ptComm);
            break;

        case AGENT_DL_STAT_PROBE:
            //跳转boot,执行升级
            ResetBootUpgrade();
            break;

        default:
            break;
    }

    return 0;
}

//发送数据帧未收到回包时的处理
Static short SendDataFrame(T_CommStruct *ptComm)
{
    #if PARALLE_UPDATE_DEBUG
        rt_kprintf("广播发送数据帧开始\n");
    #endif
    //广播发送首发帧
    SendBroadcastDataFrame(ptComm);
    //广播发送数据传输帧
    while(s_tPrtclDLRcv.wApplyAppEnd < s_tFileManage.tFileAttr.wTotalFrameNum){
        SendBroadcastDataFrame(ptComm); 
    }
    if(s_tPrtclDLRcv.wApplyAppEnd >= s_tFileManage.tFileAttr.wTotalFrameNum){
        #if PARALLE_UPDATE_DEBUG
        rt_kprintf("广播发送数据帧结束\n");
        #endif
        s_tAgentParalleDLStat = AGENT_DL_STAT_DATA_CONFIRM;
        s_tPrtclDLRcv.wApplyAppEnd = 0;
        //发送当前从机数据传输确认帧
        s_onSlavecount = 0;
        if(s_onSlavecount < s_tBMSUpdateScanResult.numInPlace) {
            s_ucAgentUpdatePollingAddr = s_tBMSUpdateScanResult.battInPlaceAddr[s_onSlavecount];
        }
        SendConfirmFrame(ptComm);
        #if PARALLE_UPDATE_DEBUG
        rt_kprintf("向地址%d发送数据传输完成确认帧\n", s_ucAgentUpdatePollingAddr);
        #endif
    }
    return SUCCESSFUL;
}


Static short SendDataFrame_Self(void)
{
    #if PARALLE_UPDATE_DEBUG
        rt_kprintf("自身发送数据帧开始\n");
    #endif
    //广播发送首发帧
    SendBroadcastDataFrame_Self();
    //广播发送数据传输帧
    while(s_tPrtclDLRcv.wApplyAppEnd < s_tFileManage.tFileAttr.wTotalFrameNum){
        SendBroadcastDataFrame_Self(); 
    }
    if(s_tPrtclDLRcv.wApplyAppEnd >= s_tFileManage.tFileAttr.wTotalFrameNum){
        #if PARALLE_UPDATE_DEBUG
        rt_kprintf("自身发送数据帧结束\n");
        #endif
        s_ucBDUDLStat = AGENT_DL_STAT_DATA_CONFIRM;
        s_tPrtclDLRcv.wApplyAppEnd = 0;
        s_bConfimeFlag = False;
        SendConfirmFrame_Self();
        #if PARALLE_UPDATE_DEBUG
        rt_kprintf("自身发送数据传输完成确认帧\n");
        #endif
    }
    return SUCCESSFUL;
}



Static short SendBroadcastDataFrame(T_CommStruct *ptComm){
    INT16U BuffLen = 0;
    if(s_tPrtclDLRcv.wApplyAppEnd == 0)
    {
        rt_thread_delay(3000);
        #if PARALLE_UPDATE_DEBUG
        rt_kprintf("Send %d\n",s_tPrtclDLRcv.wApplyAppEnd);
        #endif
        AgentParalleFirstFrmTransFrm(&BuffLen, s_bUpdateDataBuff, 0x00);
    }
    else
    {
        #if PARALLE_UPDATE_DEBUG
        rt_kprintf("Send %d\n",s_tPrtclDLRcv.wApplyAppEnd);
        #endif
        AgentParalleDataTransFrm(&BuffLen, s_bUpdateDataBuff, s_tPrtclDLRcv.wApplyAppEnd, 0x00);
    }
    PackagingFrameData(ptComm, BuffLen, s_bUpdateDataBuff);
    ChangeIntoCanFrm(ptComm->aucSendBuf, ptComm->wSendLength, DEV_TYPE_BMS, 0);
    s_tPrtclDLRcv.wApplyAppEnd += 1;
    if(GetUpgradeFileFlag() == UPDATE_BMS_FILE)
    {
        rt_thread_delay(20);
    }
    else if(GetUpgradeFileFlag() == UPDATE_BDU_FILE)
    {
        rt_thread_delay(100);
    }
    return SUCCESSFUL;
}



Static short SendBroadcastDataFrame_Self(void)
{
    INT16U BuffLen = 0;
    if (s_tPrtclDLRcv.wApplyAppEnd == 0)
    {
        rt_thread_delay(3000);
        #if PARALLE_UPDATE_DEBUG
        rt_kprintf("Self Send %d\n", s_tPrtclDLRcv.wApplyAppEnd);
        #endif
        AgentParalleFirstFrmTransFrm(&BuffLen, s_bUpdateDataBuff, 0x00);
    }
    else
    {
        #if PARALLE_UPDATE_DEBUG
        rt_kprintf("Self Send %d\n", s_tPrtclDLRcv.wApplyAppEnd);
        #endif
        AgentParalleDataTransFrm(&BuffLen, s_bUpdateDataBuff, s_tPrtclDLRcv.wApplyAppEnd, 0x00);
    }

    BMSRecvBDUParalleTransfrm_SendBduSelf(BuffLen, s_bUpdateDataBuff);
    s_tPrtclDLRcv.wApplyAppEnd += 1;
    rt_thread_delay(100);

    return SUCCESSFUL;
}


// 点对点补帧处理函数
Static short SendSupplyProcess(T_CommStruct *ptComm)
{
    if(s_ucSupplyFrameRepeatCnt < 20)
    {
        SendSupplyDataFrame(ptComm, s_wLostFrameNum, s_ucAgentUpdatePollingAddr, TIME_OUT_SUPPLY);
        s_ucSupplyFrameRepeatCnt++;
    }
    else
    {
        // 给当前轮询从机发送补帧时没有回复，状态机切换到数据传输确认阶段，进行下一从机的补帧。
        s_onSlavecount++;
        s_ucSupplyFrameRepeatCnt = 0;
        if(s_onSlavecount < s_tBMSUpdateScanResult.numInPlace){
            s_ucAgentUpdatePollingAddr = s_tBMSUpdateScanResult.battInPlaceAddr[s_onSlavecount];
            s_tAgentParalleDLStat = AGENT_DL_STAT_DATA_CONFIRM;
            s_bAgentUpdateFlag = True;
        }else{
            s_tAgentParalleDLStat = AGENT_DL_STAT_PROBE;
            s_bAgentUpdateFlag = True;
        }
    }
    return SUCCESSFUL;
}


Static short SendSupplyProcess_Self(void)
{
    if(s_ucSupplyFrameRepeatCnt < 20)
    {
        SendSupplyDataFrame_Self(s_wLostFrameNum, GetTotalAddr(), TIME_OUT_SUPPLY);
        s_ucSupplyFrameRepeatCnt++;
    }
    else
    {
        // 给当前轮询从机发送补帧时没有回复，状态机切换到数据传输确认阶段，进行下一从机的补帧。
        s_ucSupplyFrameRepeatCnt = 0;
        s_ucBDUDLStat = AGENT_DL_STAT_PROBE;
        s_bAgentUpdateFlag = True;
    }

    return SUCCESSFUL;
}


// 点对点发送补发帧
Static short SendSupplyDataFrame(T_CommStruct *ptComm, WORD wLostFrameNum, BYTE ucDestDevAddr, E_DATA_SUPPLY_TYPE supplyType)
{
    INT16U BuffLen = 0;
    // 需要补第0帧即首发帧
    if(wLostFrameNum == 0)
    {
        // 准备要发送的首发帧数据
        AgentParalleFirstFrmTransFrm(&BuffLen, s_bUpdateDataBuff, ucDestDevAddr);
    }
    // 需要补对应的数据帧
    else{
        // 准备要发送的数据传输数据
        AgentParalleDataTransFrm(&BuffLen, s_bUpdateDataBuff, wLostFrameNum, ucDestDevAddr);
    }
    // 如果是超时补帧
    if(supplyType == TIME_OUT_SUPPLY)
    {
        // 如果是从机超时未回包，数据会在Process_DL_Main的ChangeIntoCanFrm中发送出去。此处只需要将发送缓冲区填充完毕即可。
        PackagingFrameData(ptComm, BuffLen, s_bUpdateDataBuff);
    }
    // 如果是从机回包后直接补帧，会通过Proc_CAN_Frame的ChangeIntoCanFrm中发送出去。
    else
    {
        rt_memcpy(s_tPrtclDLSnd.aucApplyDataBuf, s_bUpdateDataBuff, BuffLen);
        s_tPrtclDLSnd.wApplySendDataLength = BuffLen;
    }
    return SUCCESSFUL;
}


Static short SendSupplyDataFrame_Self(WORD wLostFrameNum, BYTE ucDestDevAddr, E_DATA_SUPPLY_TYPE supplyType)
{
    INT16U BuffLen = 0;
    // 需要补第0帧即首发帧
    if(wLostFrameNum == 0)
    {
        // 准备要发送的首发帧数据
        AgentParalleFirstFrmTransFrm(&BuffLen, s_bUpdateDataBuff, ucDestDevAddr);
    }
    // 需要补对应的数据帧
    else
    {
        // 准备要发送的数据传输数据
        AgentParalleDataTransFrm(&BuffLen, s_bUpdateDataBuff, wLostFrameNum, ucDestDevAddr);
    }
    // 如果是超时补帧
    BMSRecvBDUParalleTransfrm_SendBduSelf(BuffLen, s_bUpdateDataBuff);
    rt_thread_delay(100);
    return SUCCESSFUL;
}


//发送数据传输确认帧未收到回包时的处理
Static short SendConfirmRepeatProcess(T_CommStruct *ptComm)
{
    // 先发送数据传输确认帧，如果能正常回包需要判断是否补包完毕。补包完毕的情况下进行下一电池数据传输完整性的确认
    if(s_ucRepeatSendFrameCnt < 20)
    {
        SendConfirmFrame(ptComm);
        s_ucRepeatSendFrameCnt++;
    }
    else
    {
        s_onSlavecount++;
        s_ucRepeatSendFrameCnt = 0;
        if(s_onSlavecount < s_tBMSUpdateScanResult.numInPlace){
            s_ucAgentUpdatePollingAddr = s_tBMSUpdateScanResult.battInPlaceAddr[s_onSlavecount];
            s_tAgentParalleDLStat = AGENT_DL_STAT_DATA_CONFIRM;
            s_bAgentUpdateFlag = True;
        }else{
            s_tAgentParalleDLStat = AGENT_DL_STAT_PREPARE;
            s_bAgentUpdateFlag = True;
        }
    }

    return SUCCESSFUL;
}


Static short SendConfirmRepeatProcess_Self(void)
{
    if(s_bConfimeFlag == True)
    {
        return SUCCESSFUL;
    }

    if(s_ucRepeatSendFrameCnt < 20)
    {
        SendConfirmFrame_Self();
        s_ucRepeatSendFrameCnt++;
    }
    else
    {
        s_ucBDUDLStat = AGENT_DL_STAT_PREPARE;
        s_bAgentUpdateFlag = True;
    }

    return SUCCESSFUL;
}


//发送数据传输确认帧或者升级确认帧
Static short SendConfirmFrame(T_CommStruct *ptComm)
{
    INT16U BuffLen = 0;

    if(s_tAgentParalleDLStat == AGENT_DL_STAT_DATA_CONFIRM)
    {
        // 向地址i发送数据传输确认帧，接收到对应的回复帧或者连续20次没有回复帧之后地址加1
        AgentParalleDataTransConfirmFrm(&BuffLen, s_bUpdateDataBuff, s_ucAgentUpdatePollingAddr);
    }
    else if(s_tAgentParalleDLStat == AGENT_DL_STAT_UPDATE_CONFIRM)
    {
        /*升级确认阶段则发升级确认帧*/
        AgentParalleUpdateConfirmFrm(&BuffLen, s_bUpdateDataBuff, s_ucAgentUpdatePollingAddr);
    }
    PackagingFrameData(ptComm, BuffLen, s_bUpdateDataBuff);

    return SUCCESSFUL;
}


Static short SendConfirmFrame_Self(void)
{
    INT16U BuffLen = 0;

    if(s_ucBDUDLStat == AGENT_DL_STAT_DATA_CONFIRM)
    {
        // 向地址i发送数据传输确认帧，接收到对应的回复帧或者连续20次没有回复帧之后地址加1
        AgentParalleDataTransConfirmFrm(&BuffLen, s_bUpdateDataBuff, GetTotalAddr());
    }
    else if(s_ucBDUDLStat == AGENT_DL_STAT_UPDATE_CONFIRM)
    {
        /*升级确认阶段则发升级确认帧*/
        AgentParalleUpdateConfirmFrm(&BuffLen, s_bUpdateDataBuff, GetTotalAddr());
    }

    BMSRecvBDUParalleTransfrm_SendBduSelf(BuffLen, s_bUpdateDataBuff);
    rt_thread_delay(100);

    return SUCCESSFUL;
}


// 并发升级发送首发帧

Static short AgentParalleFirstFrmTransFrm(INT16U *pOutLen, INT8U *pOutData, BYTE ucDestDevAdr) {
    if (pOutData == NULL || pOutLen == NULL) {
        return FAILURE;
    }

    *pOutLen = SM_DLFL_FIRST_FRM_APPLE_LEN;

    PutInt16ToBuff(pOutData, s_tFileManage.tFileAttr.wDataLenPerFrame);
    pOutData += 2;
    PutInt16ToBuff(pOutData, s_tFileManage.tFileAttr.wTotalFrameNum);
    pOutData += 2;
    PutInt32ToBuff(pOutData, s_tFileManage.tFileAttr.ulTotalFileLength);
    pOutData += 4;
    rt_memcpy_s(pOutData, sizeof(s_bUpdateDataBuff), s_tFileManage.tFileAttr.acFileName, SM_DLFL_NAME_LEN);
    pOutData += SM_DLFL_NAME_LEN;
    rt_memcpy_s(pOutData, sizeof(s_bUpdateDataBuff), s_tFileManage.tFileAttr.acFileTime, SM_DLFL_TIME_LEN);
    pOutData += SM_DLFL_TIME_LEN;
    PutInt16ToBuff(pOutData, s_tFileManage.tFileAttr.wFileCheck);
    pOutData += 2;
    *pOutData++ = 0;
    *pOutData++ = 0;

    if (GetUpgradeFileFlag() == UPDATE_BMS_FILE) {
        s_tPrtclDLRcv.ucApplyFun = FUNC_CODE_PARALLE_TRANSFER_FRM;
        s_tPrtclDLRcv.wApplyAppEnd = 0;
        s_tPrtclDLRcv.ucRTN = ApplyRTN_ACK;
        s_tPrtclDLRcv.ucRsvDestDevType = BMS_Type;
        s_tPrtclDLRcv.ucRsvDestDevAdr = 0X00;
        s_tPrtclDLRcv.ucRsvTrans = 0X00;
    } else if (GetUpgradeFileFlag() == UPDATE_BDU_FILE) {
        s_tPrtclDLRcv.ucApplyFun = FUNC_CODE_PARALLE_FRONT_TRANSFER_FRM;
        s_tPrtclDLRcv.wApplyAppEnd = 0;
        s_tPrtclDLRcv.ucRTN = ApplyRTN_ACK;
        s_tPrtclDLRcv.ucRsvDestDevType = BMS_Type;
        s_tPrtclDLRcv.ucRsvDestDevAdr = 0X00;
        s_tPrtclDLRcv.ucRsvTrans = 0X00;
    }

    return SUCCESSFUL;
}


// 并发升级发送数据传输帧

Static short AgentParalleDataTransFrm(INT16U *pOutLen, INT8U *pOutData, WORD wFrameNum, BYTE ucDestDevAdr) {
    rt_int32_t read_norflash_pos = 0;
    rt_spi_flash_device_t dev_w25q;

    if (pOutData == NULL || pOutLen == NULL) {
        return FAILURE;
    }

    dev_w25q = (rt_spi_flash_device_t)rt_device_find(FAL_USING_NOR_FLASH_DEV_NAME);
    if (dev_w25q == NULL) {
        return FAILURE;
    }

    read_norflash_pos = NORFLASH_APP_START + (wFrameNum - 1) * MSC_MAX_PACKET;
    sfud_read((sfud_flash *)dev_w25q->user_data, read_norflash_pos, MSC_MAX_PACKET, pOutData);
    *pOutLen = MSC_MAX_PACKET;

    if (GetUpgradeFileFlag() == UPDATE_BMS_FILE) {
        s_tPrtclDLRcv.ucApplyFun = FUNC_CODE_PARALLE_TRANSFER_FRM;
        s_tPrtclDLRcv.wApplyAppEnd = wFrameNum;
        s_tPrtclDLRcv.ucRTN = ApplyRTN_ACK;
        s_tPrtclDLRcv.ucRsvDestDevType = BMS_Type;
        s_tPrtclDLRcv.ucRsvDestDevAdr = ucDestDevAdr;
        s_tPrtclDLRcv.ucRsvTrans = 0x00;
    } else if (GetUpgradeFileFlag() == UPDATE_BDU_FILE) {
        s_tPrtclDLRcv.ucApplyFun = FUNC_CODE_PARALLE_FRONT_TRANSFER_FRM;
        s_tPrtclDLRcv.wApplyAppEnd = wFrameNum;
        s_tPrtclDLRcv.ucRTN = ApplyRTN_ACK;
        s_tPrtclDLRcv.ucRsvDestDevType = BMS_Type;
        s_tPrtclDLRcv.ucRsvDestDevAdr = ucDestDevAdr;
        s_tPrtclDLRcv.ucRsvTrans = 0x00;
    }

    return SUCCESSFUL;
}


// 代理升级时升级主机向从机发送数据传输确认帧

Static short AgentParalleDataTransConfirmFrm(INT16U *pOutLen, INT8U *pOutData, BYTE ucDestAddr){
    if(pOutData == NULL || pOutLen == NULL)
    {
        return FAILURE;
    }
    if(GetUpgradeFileFlag() == UPDATE_BMS_FILE)
    {
        s_tPrtclDLRcv.ucApplyFun = FUNC_CODE_PARALLE_DATA_CONFIRM_FRM;
        s_tPrtclDLRcv.wApplyAppEnd = 0XAA55;
        s_tPrtclDLRcv.ucRTN = ApplyRTN_ACK;
        s_tPrtclDLRcv.ucRsvDestDevType = BMS_Type;
        s_tPrtclDLRcv.ucRsvDestDevAdr = ucDestAddr;
        s_tPrtclDLRcv.ucRsvTrans = 0X00;
        *pOutLen = 2;
        s_wFrameLossCountThresh =  ceil(s_tFileManage.tFileAttr.wTotalFrameNum * (LOSS_FRAME_COUNT_THRESHOLD / 100.0));
        pOutData[0] = (s_wFrameLossCountThresh >> 8) & 0xff;
        pOutData[1] = s_wFrameLossCountThresh & 0xff;
    }
    else if(GetUpgradeFileFlag() == UPDATE_BDU_FILE)
    {
        s_tPrtclDLRcv.ucApplyFun = FUNC_CODE_PARALLE_FRONT_DATA_CONFIRM_FRM;
        s_tPrtclDLRcv.wApplyAppEnd = 0XAA55;
        s_tPrtclDLRcv.ucRTN = ApplyRTN_ACK;
        s_tPrtclDLRcv.ucRsvDestDevType = BMS_Type;
        s_tPrtclDLRcv.ucRsvDestDevAdr = ucDestAddr;
        s_tPrtclDLRcv.ucRsvTrans = 0X00;
        *pOutLen = 2;
        s_wFrameLossCountThresh =  ceil(s_tFileManage.tFileAttr.wTotalFrameNum * (LOSS_FRAME_COUNT_THRESHOLD / 100.0));
        pOutData[0] = (s_wFrameLossCountThresh >> 8) & 0xff;
        pOutData[1] = s_wFrameLossCountThresh & 0xff;
    }
    return SUCCESSFUL;
}


// // 代理升级时升级主机向从机发送数据传输确认帧回包
// Static short AgentParalleDataTransConfirmFrmAck(BYTE ucDestAddr)
// {
//     s_tPrtclDLRcv.ucApplyFun = FUNC_CODE_PARALLE_DATA_CONFIRM_FRM;
//     s_tPrtclDLRcv.wApplyAppEnd = 0XAA55;
//     s_tPrtclDLRcv.ucRTN = ApplyRTN_ACK;
//     s_tPrtclDLRcv.ucRsvDestDevType = BMS_Type;
//     s_tPrtclDLRcv.ucRsvDestDevAdr = ucDestAddr;
//     s_tPrtclDLRcv.ucRsvTrans = 0X00;
//     s_tPrtclDLSnd.wApplySendDataLength = 0;
//     return SUCCESSFUL;
// }

// 代理升级时升级主机向从机发送更新确认帧

Static short AgentParalleUpdateConfirmFrm(INT16U *pOutLen, INT8U *pOutData, BYTE ucDestAddr){
    if(pOutData == NULL || pOutLen == NULL)
    {
        return FAILURE;
    }
    if(GetUpgradeFileFlag() == UPDATE_BMS_FILE)
    {
        rt_memcpy_s(pOutData, sizeof(s_bUpdateDataBuff), s_tFileManage.tFileAttr.acFileName, SM_DLFL_NAME_LEN);
        pOutData += SM_DLFL_NAME_LEN;

        s_tPrtclDLRcv.ucApplyFun = FUNC_CODE_PARALLE_UPDATE_CONFIRM_FRM;
        s_tPrtclDLRcv.wApplyAppEnd = 0XAA55;
        s_tPrtclDLRcv.ucRTN = ApplyRTN_ACK;
        s_tPrtclDLRcv.ucRsvDestDevType = BMS_Type;
        s_tPrtclDLRcv.ucRsvDestDevAdr = ucDestAddr;
        s_tPrtclDLRcv.ucRsvTrans = 0X00;
        //s_tPrtclDLSnd.wApplySendDataLength = 0;
        *pOutLen = SM_DLFL_NAME_LEN;
    }
    else if(GetUpgradeFileFlag() == UPDATE_BDU_FILE)
    {
        rt_memcpy_s(pOutData, sizeof(s_bUpdateDataBuff), s_tFileManage.tFileAttr.acFileName, SM_DLFL_NAME_LEN);
        pOutData += SM_DLFL_NAME_LEN;

        s_tPrtclDLRcv.ucApplyFun = FUNC_CODE_PARALLE_FRONT_UPDATE_CONFIRM_FRM;
        s_tPrtclDLRcv.wApplyAppEnd = 0XAA55;
        s_tPrtclDLRcv.ucRTN = ApplyRTN_ACK;
        s_tPrtclDLRcv.ucRsvDestDevType = BMS_Type;
        s_tPrtclDLRcv.ucRsvDestDevAdr = ucDestAddr;
        s_tPrtclDLRcv.ucRsvTrans = 0X00;
        //s_tPrtclDLSnd.wApplySendDataLength = 0;
        *pOutLen = SM_DLFL_NAME_LEN;
    }
    return SUCCESSFUL;
}


/**
 * @brief 打包发送帧
 * 将给定的通信结构体指针作为参数，执行打包触发帧的操作。
 * @param ptComm 通信结构体指针
 */
Static rt_int32_t PackagingFrameData(T_CommStruct *ptComm, INT16U pOutLen,INT8U *pOutData)
{
    BYTE *p;
    WORD wCrc;
    if(pOutData == NULL)
    {
       return FAILURE;
    }
    p = ptComm->aucSendBuf;
    *(p++) = DownloadType;
    *(p++) = BMS_Type;
    *(p++) = GetBMSDLAddr();
    *(p++) =  s_tPrtclDLRcv.ucApplyFun;
    *(p++) = (s_tPrtclDLRcv.wApplyAppEnd >> 8) & 0xff;
    *(p++) = s_tPrtclDLRcv.wApplyAppEnd & 0xff;
    *(p++) = s_tPrtclDLRcv.ucRTN;
    *(p++) = s_tPrtclDLRcv.ucRsvDestDevType;
    *(p++) = s_tPrtclDLRcv.ucRsvDestDevAdr;
    *(p++) = s_tPrtclDLRcv.ucRsvTrans;
    *(p++) = (pOutLen >> 8) & 0xff;
    *(p++) =  pOutLen & 0xff;

    rt_memcpy( p, pOutData, pOutLen );
    p += pOutLen;
    wCrc = CRC_Cal(ptComm->aucSendBuf, p - ptComm->aucSendBuf);
    *(p++) = wCrc & 0xFF;
    *(p++) = wCrc>>8;

    ptComm->wSendLength = p - ptComm->aucSendBuf;
    return SUCCESSFUL;
}

Static short AgentDownloadPrepare(T_CommStruct *ptComm)
{
    #if PARALLE_UPDATE_DEBUG
        rt_kprintf("开始主机升级\n");
    #endif
    s_tBMSUpdateScanResult = GetBMSUpdateScanState();
    #if PARALLE_UPDATE_DEBUG
        rt_kprintf("总共%d台从机在位\n",s_tBMSUpdateScanResult.numInPlace);
    #endif

    readFileManageInfo(&s_tFileManage);
    #if PARALLE_UPDATE_DEBUG
        rt_kprintf("升级文件总帧数%d\n",s_tFileManage.tFileAttr.wTotalFrameNum);
    #endif
    s_tAgentParalleDLStat = AGENT_DL_STAT_TRIGING;

    if (s_tBMSUpdateScanResult.numInPlace > 0)
    {
        s_onSlavecount = 0;
        s_ucAgentUpdatePollingAddr = s_tBMSUpdateScanResult.battInPlaceAddr[s_onSlavecount];
        #if PARALLE_UPDATE_DEBUG
        rt_kprintf("触发第一台地址：%d\n",s_ucAgentUpdatePollingAddr);
        #endif
        SendTrigerFrame(ptComm);
    }
    else
    {
        s_tAgentParalleDLStat = AGENT_DL_STAT_PROBE;
        s_bAgentUpdateFlag = True;
    }

    return SUCCESSFUL;
}


Static short BDUDownloadPrepare_Self(void)
{
    s_ucRepeatSendFrameCnt = 0;
    #if PARALLE_UPDATE_DEBUG
        rt_kprintf("开始升级自己\n");
    #endif

    readFileManageInfo(&s_tFileManage);
    #if PARALLE_UPDATE_DEBUG
        rt_kprintf("升级文件总帧数%d\n",s_tFileManage.tFileAttr.wTotalFrameNum);
    #endif
    s_bTrigerFlag = False;
    s_ucBDUDLStat = AGENT_DL_STAT_TRIGING;

    s_tPrtclDLRcv.ucApplyFun = FUNC_CODE_PARALLE_FRONT_TRIGGER_FRM;
    s_tPrtclDLRcv.wApplyAppEnd = 0xAA55;
    s_tPrtclDLRcv.ucRTN = ApplyRTN_ACK;
    s_tPrtclDLRcv.ucRsvTrans = 0x00;
    s_tPrtclDLRcv.ucRsvDestDevType = BMS_Type;
    s_tPrtclDLRcv.ucRsvDestDevAdr = GetTotalAddr();

    reset_circle_buff();
    BMSRecvBDUParalleTransfrm_SendBduSelf(SM_DLFL_NAME_LEN, (INT8U *)&s_ucFileBDUNameBin[0]);
    rt_thread_delay(100);
    return SUCCESSFUL;
}


/* 触发下一台从机 */
Static short TriggerNextSlave(T_CommStruct *ptComm)
{
    s_onSlavecount++;
    s_ucRepeatSendFrameCnt = 0;
    if(s_onSlavecount < s_tBMSUpdateScanResult.numInPlace){
        s_ucAgentUpdatePollingAddr = s_tBMSUpdateScanResult.battInPlaceAddr[s_onSlavecount];
        s_tAgentParalleDLStat = AGENT_DL_STAT_TRIGING;
        SendTrigerFrame(ptComm);
    }else{
        s_tAgentParalleDLStat = AGENT_DL_STAT_DATA_TRANS;
        s_bAgentUpdateFlag = True;
    }
    return SUCCESSFUL;
}

/* 触发下一台从机回包的处理逻辑 */
Static short TriggerNextSlaveAck(T_CommStruct *ptComm)
{
    if (s_ucAgentUpdatePollingAddr != s_tPrtclDLRcv.ucSrcDevAdr) {
        s_tPrtclDLRcv.ucRTN = ApplyRTN_NOACK;
        return FAILURE;
    }
    s_onSlavecount++;
    s_ucRepeatSendFrameCnt = 0;
    if(s_onSlavecount < s_tBMSUpdateScanResult.numInPlace){
        s_ucAgentUpdatePollingAddr = s_tBMSUpdateScanResult.battInPlaceAddr[s_onSlavecount];
        #if PARALLE_UPDATE_DEBUG
        rt_kprintf("触发下一台地址：%d\n",s_ucAgentUpdatePollingAddr);
        #endif
        s_tAgentParalleDLStat = AGENT_DL_STAT_TRIGING;
        SendTrigerFrameAck(ptComm);
    }else{
        #if PARALLE_UPDATE_DEBUG
        rt_kprintf("所有设备触发完成\n");
        #endif
        s_tAgentParalleDLStat = AGENT_DL_STAT_DATA_TRANS;
        s_bAgentUpdateFlag = True;
        s_tPrtclDLRcv.ucRTN = ApplyRTN_NOACK;
        s_tPrtclDLRcv.wApplyAppEnd = 0;
        if(s_AgentAckOverTimeTimer != NULL)
        {
            rt_timer_stop(s_AgentAckOverTimeTimer);
        }
    }
    return SUCCESSFUL;
}

Static short TrigRepeatProcess(T_CommStruct *ptComm)
{
    if (s_ucRepeatSendFrameCnt < 20)
    {
        #if PARALLE_UPDATE_DEBUG
            rt_kprintf("AGENT_DL_STAT_TRIGING 重发\n");
        #endif
        SendTrigerFrame(ptComm);
        s_ucRepeatSendFrameCnt++;
    }
    else
    {
        #if PARALLE_UPDATE_DEBUG
            rt_kprintf("AGENT_DL_STAT_TRIGING 重发下一台\n");
        #endif
        TriggerNextSlave(ptComm);
    }

    return SUCCESSFUL;
}


Static void TrigRepeatProcess_Self(void)
{
    if(s_bTrigerFlag == True)
    {
        return;
    }
    if (s_ucRepeatSendFrameCnt < 20)
    {
        #if PARALLE_UPDATE_DEBUG
            rt_kprintf("AGENT_DL_SELF_STAT_TRIGING 重发\n");
        #endif
        s_ucBDUDLStat = AGENT_DL_STAT_TRIGING;

        s_tPrtclDLRcv.ucApplyFun = FUNC_CODE_PARALLE_FRONT_TRIGGER_FRM;
        s_tPrtclDLRcv.wApplyAppEnd = 0xAA55;
        s_tPrtclDLRcv.ucRTN = ApplyRTN_ACK;
        s_tPrtclDLRcv.ucRsvTrans = 0x00;
        s_tPrtclDLRcv.ucRsvDestDevType = BMS_Type;
        s_tPrtclDLRcv.ucRsvDestDevAdr = GetTotalAddr();

        reset_circle_buff();
        BMSRecvBDUParalleTransfrm_SendBduSelf(SM_DLFL_NAME_LEN, (INT8U *)&s_ucFileBDUNameBin[0]);
        s_ucRepeatSendFrameCnt++;
        rt_thread_delay(500);
    }
    else
    {
        s_ucBDUDLStat = AGENT_DL_STAT_DATA_TRANS;
        s_bAgentUpdateFlag = True;
        return;
    }

    return;
}


//发送数据传输触发帧

Static short SendTrigerFrame(T_CommStruct *ptComm) {
    if(GetUpgradeFileFlag() == UPDATE_BMS_FILE) {
        ptComm->ucPortType = COMM_CAN;
        s_tPrtclDLRcv.ucApplyFun = FUNC_CODE_PARALLE_TRIGGER_FRM;
        s_tPrtclDLRcv.wApplyAppEnd = 0xAA55;
        s_tPrtclDLRcv.ucRTN = ApplyRTN_ACK;
        s_tPrtclDLRcv.ucRsvTrans = 0x00;

        rt_memcpy_s(s_bUpdateDataBuff, sizeof(s_bUpdateDataBuff), s_ucFileNameBin, sizeof(s_ucFileNameBin));
        PackagingFrameData(ptComm, SM_DLFL_NAME_LEN, s_bUpdateDataBuff);
    } else if(GetUpgradeFileFlag() == UPDATE_BDU_FILE) {
        ptComm->ucPortType = COMM_CAN;
        s_tPrtclDLRcv.ucApplyFun = FUNC_CODE_PARALLE_FRONT_TRIGGER_FRM;
        s_tPrtclDLRcv.wApplyAppEnd = 0xAA55;
        s_tPrtclDLRcv.ucRTN = ApplyRTN_ACK;
        s_tPrtclDLRcv.ucRsvTrans = 0x00;
        s_tPrtclDLRcv.ucRsvDestDevType = BMS_Type;
        s_tPrtclDLRcv.ucRsvDestDevAdr = s_ucAgentUpdatePollingAddr;

        rt_memcpy_s(s_bUpdateDataBuff, sizeof(s_bUpdateDataBuff), s_ucFileBDUNameBin, sizeof(s_ucFileBDUNameBin));
        PackagingFrameData(ptComm, SM_DLFL_NAME_LEN, s_bUpdateDataBuff);

#if PARALLE_UPDATE_DEBUG
        rt_kprintf("SendTrigerFrame   12345\n");
        for(int i = 0; i < 100; i++) {
            rt_kprintf("%x  ", ptComm->aucSendBuf[i]);
        }
        rt_kprintf("\n");
#endif
    }
    return SUCCESSFUL;
}


//对发送数据传输触发帧回包的处理逻辑

Static short SendTrigerFrameAck(T_CommStruct *ptComm) {
    if(GetUpgradeFileFlag() == UPDATE_BMS_FILE) {
        ptComm->ucPortType = COMM_CAN;
        s_tPrtclDLRcv.ucApplyFun = FUNC_CODE_PARALLE_TRIGGER_FRM;
        s_tPrtclDLRcv.wApplyAppEnd = 0xAA55;
        s_tPrtclDLRcv.ucRTN = ApplyRTN_ACK;
        s_tPrtclDLRcv.ucRsvTrans = 0x00;
        rt_memcpy_s(s_tPrtclDLSnd.aucApplyDataBuf, sizeof(s_tPrtclDLSnd.aucApplyDataBuf), s_ucFileNameBin, sizeof(s_ucFileNameBin));
        s_tPrtclDLSnd.wApplySendDataLength = sizeof(s_ucFileNameBin);
    } else if(GetUpgradeFileFlag() == UPDATE_BDU_FILE) {
        ptComm->ucPortType = COMM_CAN;
        s_tPrtclDLRcv.ucApplyFun = FUNC_CODE_PARALLE_FRONT_TRIGGER_FRM;
        s_tPrtclDLRcv.wApplyAppEnd = 0xAA55;
        s_tPrtclDLRcv.ucRTN = ApplyRTN_ACK;
        s_tPrtclDLRcv.ucRsvTrans = 0x00;
        rt_memcpy_s(s_tPrtclDLSnd.aucApplyDataBuf, sizeof(s_tPrtclDLSnd.aucApplyDataBuf), s_ucFileBDUNameBin, sizeof(s_ucFileBDUNameBin));
        s_tPrtclDLSnd.wApplySendDataLength = sizeof(s_ucFileBDUNameBin);
    }
    return SUCCESSFUL;
}


BOOLEAN IsCommTrans(void)
{
    return s_bIsDLCommTrans;
}

BOOLEAN ClearCommTransFlag(void)
{
    s_bIsDLCommTrans = False;
    return True;
}

// 批量升级进度条计算
BYTE CalBatchUpdateProgress(BYTE *ucBuff)
{
    BYTE ucUpdateFlag = SM_UPDATE_NOT_START; // 升级状态（默认为无升级）
    BYTE ucUpdateProgress = 0; // 升级进度（准备阶段和触发阶段升级进度为0%）
    WORD wBroadCastTotalFrame = s_tFileManage.tFileAttr.wTotalFrameNum; // 广播阶段总帧数
    WORD wCurrentTransFrameNum = s_tPrtclDLRcv.wApplyAppEnd; // 广播阶段当前已传输帧数
    BYTE ucSUAddr = GetBMSAddr(); // 升级主机的地址

    RETURN_VAL_IF_FAIL(wBroadCastTotalFrame != 0, 0);
    switch (s_tAgentParalleDLStat)
    {
        case AGENT_DL_STAT_DATA_TRANS: // 数据传输阶段（广播）：从机升级进度为 已传输帧数 / 总帧数 * 85%
            ucUpdateProgress = wCurrentTransFrameNum * BROADCAST_COMPLETE_PROGRESS / wBroadCastTotalFrame;
            ucUpdateFlag = SM_UPDATE_ONGOING;
            break;
        case AGENT_DL_STAT_DATA_CONFIRM: // 数据传输确认阶段：升级进度为85%
            ucUpdateProgress = BROADCAST_COMPLETE_PROGRESS;
            ucUpdateFlag = SM_UPDATE_ONGOING;
            break;
        case AGENT_DL_STAT_SUPPLY_FRAME: // 进入补帧阶段：升级进度为90%
            ucUpdateProgress = BROADCAST_COMPLETE_PROGRESS + 5;
            ucUpdateFlag = SM_UPDATE_ONGOING;
            break;
        case AGENT_DL_STAT_UPDATE_CONFIRM: // 补帧完成，进入更新确认阶段：升级进度为95%
            ucUpdateProgress = SUPPLY_COMPLETE_PROGRESS;
            ucUpdateFlag = SM_UPDATE_ONGOING;
            break;
        case AGENT_DL_STAT_PROBE: // 从机升级完毕，主机开始升级：升级进度为100%
            ucUpdateProgress = 100;
            ucUpdateFlag = SM_UPDATE_SUCCESSFUL;
            break;
        default:
            // 如果没有匹配到任何情况，可以在这里添加默认处理或错误处理
            break;
    }

    ucBuff[0] = 32; // DATA域第一个字节表示支持升级的最大设备数，固定为32
    ucBuff[2 * ucSUAddr - 1] = ucUpdateFlag; // 升级主机的升级状态
    ucBuff[2 * ucSUAddr] = ucUpdateProgress; // 升级主机的升级进度

    for(BYTE i = 0; i < s_tBMSUpdateScanResult.numInPlace; i++) // 在位从机的升级状态和升级进度
    {
        ucBuff[2 * s_tBMSUpdateScanResult.battInPlaceAddr[i] - 1] = ucUpdateFlag;
        ucBuff[2 * s_tBMSUpdateScanResult.battInPlaceAddr[i]] = ucUpdateProgress;
    } // 其余非在位从机，升级状态和升级进度都为0
    return ucUpdateProgress;
}

BOOLEAN SetAgentUpdateFlag(BOOLEAN bAgentUpdateFlag) {
    s_bAgentUpdateFlag = bAgentUpdateFlag;
    return SUCCESSFUL;
}

