#ifndef SOFTWARE_SRC_APP_COMMON_IN_H_
#define SOFTWARE_SRC_APP_COMMON_IN_H_
#include "common.h"

#ifdef __cplusplus
extern "C" {
#endif

/// 参数ID宏定义
#define PARA_ID_CHG_CURR_HIGH_ALM  0 // 充电过流告警阈值
#define PARA_ID_CHG_CURR_HIGH_PRT  1 // 充电过流保护阈值
#define PARA_ID_DISCHG_CURR_HIGH_ALM  2 // 放电过流告警阈值
#define PARA_ID_BATT_OVER_VOLT_ALM  3 // 电池组过压告警阈值
#define PARA_ID_BATT_OVER_VOLT_PRT  4 // 电池组过压保护阈值
#define PARA_ID_BOARD_TEMP_HIGH_PRT  5 // 单板过温保护阈值,  参数范围不具备参考
#define PARA_ID_BOARD_TEMP_HIGH_ALM  6 // 单板过温告警阈值,  参数范围不具备参考
#define PARA_ID_ENV_TEMP_HIGH_ALM  7 // 环境温度高告警阈值
#define PARA_ID_ENV_TEMP_LOW_ALM  8 // 环境温度低告警阈值
#define PARA_ID_BATT_UNDER_VOLT_ALM  9 // 电池组欠压告警阈值
#define PARA_ID_BATT_UNDER_VOLT_PRT  10 // 电池组欠压保护阈值
#define PARA_ID_CELL_OVER_VOLT_ALM  11 // 单体过压告警阈值
#define PARA_ID_CELL_OVER_VOLT_PRT  12 // 单体过压保护阈值
#define PARA_ID_CELL_UNDER_VOLT_ALM  13 // 单体欠压告警阈值
#define PARA_ID_CELL_UNDER_VOLT_PRT  14 // 单体欠压保护阈值
#define PARA_ID_CHG_TEMP_HIGH_ALM  15 // 充电高温告警阈值
#define PARA_ID_CHG_TEMP_HIGH_PRT  16 // 充电高温保护阈值
#define PARA_ID_DISCHG_TEMP_HIGH_ALM  17 // 放电高温告警阈值
#define PARA_ID_DISCHG_TEMP_HIGH_PRT  18 // 放电高温保护阈值
#define PARA_ID_CHG_TEMP_LOW_ALM  19 // 充电低温告警阈值
#define PARA_ID_CHG_TEMP_LOW_PRT  20 // 充电低温保护阈值
#define PARA_ID_DISCHG_TEMP_LOW_ALM  21 // 放电低温告警阈值
#define PARA_ID_DISCHG_TEMP_LOW_PRT  22 // 放电低温保护阈值
#define PARA_ID_CELL_POOR_CONSIS_ALM  23 // 单体一致性差告警阈值
#define PARA_ID_CELL_POOR_CONSIS_PRT  24 // 单体一致性差保护阈值
#define PARA_ID_BATT_SOC_LOW_ALM  25 // 电池SOC低告警阈值
#define PARA_ID_BATT_SOC_LOW_PRT  26 // 电池SOC低保护阈值
#define PARA_ID_BATT_SOH_ALM  27 // 电池SOH低告警阈值
#define PARA_ID_BATT_SOH_PRT  28 // 电池SOH低保护阈值
#define PARA_ID_CELL_DAMAGE_PRT  29 // 单体损坏保护阈值
#define PARA_ID_BATT_CHG_FULL_AVER_CURR  30 // 电池充满电流
#define PARA_ID_CHG_MAX_DURA  31 // 充电最长时间
#define PARA_ID_CHG_END_DURA  32 // 充电末期维持时间
#define PARA_ID_BATT_SUPPL_VOLT  33 // 电池补充电电压
#define PARA_ID_CELL_EQU_VOLT_DIFF  34 // 单体均衡启动压差阈值
#define PARA_ID_HIS_DATA_INTER  35 // 历史数据保存间隔
#define PARA_ID_BUZZER_ENABLE  36 // 蜂鸣器使能
#define PARA_ID_BATT_CAP  37 // 电池容量
#define PARA_ID_SOFT_ANTI_THEFT_DELAY  38 // 软件防盗延时
#define PARA_ID_DEVICE_NAME  39 // 设备名称
#define PARA_ID_DOC_PER_DISCHG  40 // 单次放电DOD
#define PARA_ID_CELL_UVP_DELAY  41 // 单体动态欠压保护
#define PARA_ID_RELAY  42 // 告警干接点
#define PARA_ID_ALARM_CLASS  43 // 告警级别
#define PARA_ID_ENABLE_DATE  44 // 启用日期
#define PARA_ID_UVP_TEMP_COMPENSATION_EN  45 // 整组欠压保护温度补偿
#define PARA_ID_BMS_SYSTEM_NAME  46 // BMS系统名称
#define PARA_ID_RELAY_DEFAULT_STATUS  47 // 干接点默认状态
#define PARA_ID_ENV_TEMP_HIGH_PRT  48 // 环境温度高保护阈值
#define PARA_ID_ENV_TEMP_LOW_PRT  49 // 环境温度低保护阈值
#define PARA_ID_BATT_OVER_VOLT_ALM_RECO  50 // 电池组过压告警恢复阈值
#define PARA_ID_BATT_OVER_VOLT_PRT_RECO  51 // 电池组过压保护恢复阈值
#define PARA_ID_CELL_OVER_VOLT_ALM_RECO  52 // 单体过压告警恢复阈值
#define PARA_ID_CELL_OVER_VOLT_PRT_RECO  53 // 单体过压保护恢复阈值
#define PARA_ID_CELL_UNDER_VOLT_ALM_RECO  54 // 单体欠压告警恢复阈值
#define PARA_ID_CELL_UNDER_VOLT_PRT_RECO  55 // 单体欠压保护恢复阈值
#define PARA_ID_CHG_TEMP_HIGH_ALM_RECO  56 // 充电高温告警恢复阈值
#define PARA_ID_CHG_TEMP_HIGH_PRT_RECO  57 // 充电高温保护恢复阈值
#define PARA_ID_DISCHG_TEMP_HIGH_ALM_RECO  58 // 放电高温告警恢复阈值
#define PARA_ID_DISCHG_TEMP_HIGH_PRT_RECO  59 // 放电高温保护恢复阈值
#define PARA_ID_CHG_TEMP_LOW_ALM_RECO  60 // 充电低温告警恢复阈值
#define PARA_ID_CHG_TEMP_LOW_PRT_RECO  61 // 充电低温保护恢复阈值
#define PARA_ID_DISCHG_TEMP_LOW_ALM_RECO  62 // 放电低温告警恢复阈值
#define PARA_ID_DISCHG_TEMP_LOW_PRT_RECO  63 // 放电低温保护恢复阈值
#define PARA_ID_ENV_TEMP_HIGH_ALM_RECO  64 // 环境温度高告警恢复阈值
#define PARA_ID_ENV_TEMP_HIGH_PRT_RECO  65 // 环境温度高保护恢复阈值
#define PARA_ID_ENV_TEMP_LOW_ALM_RECO  66 // 环境温度低告警恢复阈值
#define PARA_ID_ENV_TEMP_LOW_PRT_RECO  67 // 环境温度低保护恢复阈值
#define PARA_ID_BOARD_TEMP_HIGH_ALM_RECO  68 // 单板过温告警恢复阈值,  参数范围不具备参考
#define PARA_ID_BOARD_TEMP_HIGH_PRT_RECO  69 // 单板过温保护恢复阈值,  参数范围不具备参考
#define PARA_ID_HEATING_PAD_ENABLE  70 // 加热垫使能
#define PARA_ID_GYRO_ANGLE  71 // 陀螺仪倾角
#define PARA_ID_BATT_SOC_LOW_ALM_RECO  72 // 电池SOC低告警恢复阈值,  参数范围不具备参考
#define PARA_ID_GYRO_ANTI_THEFT_MODE  73 // 陀螺仪防盗方式
#define PARA_ID_BATT_UNLOCK_MODE  74 // 电池解锁方式
#define PARA_ID_DISCHG_CURR_HIGH_ALM_RECO  75 // 放电过流告警恢复阈值,  参数范围不具备参考
#define PARA_ID_BATT_UNDER_VOLT_ALM_RECO  76 // 电池组欠压告警恢复阈值,  不允许设置，参数范围不具备参考性
#define PARA_ID_BATT_UNDER_VOLT_PRT_RECO  77 // 电池组欠压保护恢复阈值,  不允许设置，参数范围不具备参考性
#define PARA_ID_CELL_POOR_CONSIS_PRT_RECO  78 // 单体一致性差保护恢复阈值,  不允许设置，参数范围不具备参考性
#define PARA_ID_HIS_DATA_INTER_BK  79 // 数据保存时间,  参数范围不具备参考性
#define PARA_ID_CELL_CHARGE_FULL_VOLT  80 // 单体充电截止电压
#define PARA_ID_BATT_CHG_FULL_AVER_VOLT  81 // 电池充满电压
#define PARA_ID_CHRG_MAX_CURR  82 // 充电最大电流
#define PARA_ID_SWITCH_SOC  83 // 放电切换SOC
#define PARA_ID_USAGE_SCENARIO  84 // 放电模式
#define PARA_ID_REMOTE_SUPPLY_OUT_VOLT  85 // 恒压放电输出电压
#define PARA_ID_DISCHRG_MAX_CURR  86 // 放电最大电流
#define PARA_ID_CHG_HEATER_STARTUP_TEMP  87 // 充电加热膜启动温度
#define PARA_ID_CHG_HEATER_SHUTDOWN_TEMP  88 // 充电加热膜关闭温度
#define PARA_ID_HEATER_TEMP_HIGH_THRE  89 // 加热膜过温阈值
#define PARA_ID_HEATER_TEMP_HIGH_RELEASE  90 // 加热膜过温解除
#define PARA_ID_RECHARGE_SOC  91 // 补充电SOC
#define PARA_ID_INTELLIGENT_INTER_CHARGE_EN  92 // 智能间歇充电使能
#define PARA_ID_BATT_CHARGE_CURR_LIMIT_EN  93 // 电池组充电限流使能
#define PARA_ID_POWER_OFF_TIME_PRT_THRE  94 // 停电时间保护阈值
#define PARA_ID_POWER_OFF_TIME_PRT_EN  95 // 停电时间保护使能
#define PARA_ID_CURR_BALANCE_AMPLITUDE  96 // 均流SOC补偿幅值
#define PARA_ID_CURR_BALANCE_SLOPE  97 // 均流SOC补偿斜率
#define PARA_ID_CURR_BALANCE_SOC_EN  98 // 均流SOC补偿使能
#define PARA_ID_SLEEP_INDICATOR  99 // 休眠指示灯
#define PARA_ID_BATT_ADDRESS_MODE  100 // 电池地址获取方式
#define PARA_ID_BATT_SWITCH_ADDRESS  101 // 电池切换地址
#define PARA_ID_CHARGE_ROTATE_ENABLE  102 // 充电轮换使能
#define PARA_ID_CELL_SUPPL_VOLT  103 // 单体补充电电压
#define PARA_ID_DISCHARGE_REMO_END_OUTPUT_VOLT_FIRST  104 // 末期放电电压1
#define PARA_ID_DISCHARGE_REMO_END_OUTPUT_VOLT_SECOND  105 // 末期放电电压2
#define PARA_ID_CELL_TEMP_PRT_SHIELD  106 // 单体温度保护屏蔽
#define PARA_ID_CHARGE_DISCHARGE_MACHINE_TEST_MODE  107 // 充放电机测试模式
#define PARA_ID_PREFAULT_RECORDING_TIME_INTERVAL  108 // 故障前录波时间间隔
#define PARA_ID_POSTFAULT_RECORDING_TIME_INTERVAL  109 // 故障后录波时间间隔
#define PARA_ID_PREFAULT_RECORDING_NUMBER  110 // 故障前录波条数
#define PARA_ID_POSTFAULT_RECORDING_NUMBER  111 // 故障后录波条数
#define PARA_ID_MEASUREMENT_POINTS_NUMBER  112 // 测点数量X
#define PARA_ID_VARIABLE_MEASUREMENT_POINT_ID  113 // 可变测点ID
#define PARA_ID_DISCHG_CURR_HIGH_PRT  114 // 放电过流保护阈值,  不允许设置，参数范围不具备参考性
#define PARA_ID_DCR_FAULT_ALM_THRE  115 // 直流内阻异常告警阈值
#define PARA_ID_DCR_FAULT_PRT_THRE  116 // 直流内阻异常保护阈值
#define PARA_ID_CELL_TEMP_RISE_ABNORMAL  117 // 单体温升速率异常阈值
#define PARA_ID_SELFDISCHG_ACR  118 // 自放电容量比率
#define PARA_ID_SELFDEVELOP_PROTOCOL_OFFSET_ADDR  119 // 自研协议偏移地址
#define PARA_ID_CAP_DCDR  120 // 容量衰减一致性差告警比率阈值
#define PARA_ID_BATT_FAULT_TEMP_HIGH_PRT  121 // 电池异常温度高保护阈值
#define PARA_ID_CHARGE_MAP_ENABLE  122 // 充电MAP使能
#define PARA_ID_EMERGENCY_FLASH_MODE  123 // 严重告警闪烁方式
#define PARA_ID_SECONDARY_FLASH_MODE  124 // 次级告警闪烁方式
#define PARA_ID_CELL_TYPE  125 // 电芯类型
#define PARA_ID_DISCHG_HEATER_STARTUP_TEMP  126 // 放电加热膜启动温度
#define PARA_ID_DISCHG_HEATER_SHUTDOWN_TEMP  127 // 放电加热膜关闭温度
#define PARA_ID_NTC_INVALID_SHIELD_NUM  128 // NTC无效屏蔽路数
#define PARA_ID_SWITCH_SOC2  129 // 放电末期切换SOC2,  该参数暂不使用，值随放电模式联动，恒压放电模式为0%，混用为10%

/// 参数结构体
typedef	struct
{
	FLOAT  fChgCurrHighAlmThre; // 充电过流告警阈值(C5)
	FLOAT  fChgCurrHighPrtThre; // 充电过流保护阈值(C5)
	FLOAT  fDischgCurrHighAlmThre; // 放电过流告警阈值(C5)
	FLOAT  fBattOverVoltAlmThre; // 电池组过压告警阈值(V)
	FLOAT  fBattOverVoltPrtThre; // 电池组过压保护阈值(V)
	FLOAT  fBoardTempHighPrtThre; // 单板过温保护阈值(℃),  参数范围不具备参考
	FLOAT  fBoardTempHighcThre; // 单板过温告警阈值(℃),  参数范围不具备参考
	FLOAT  fEnvTempHighAlmThre; // 环境温度高告警阈值(℃)
	FLOAT  fEnvTempLowAlmThre; // 环境温度低告警阈值(℃)
	FLOAT  fBattUnderVoltAlmThre; // 电池组欠压告警阈值(V)
	FLOAT  fBattUnderVoltPrtThre; // 电池组欠压保护阈值(V)
	FLOAT  fCellOverVoltAlmThre; // 单体过压告警阈值(V)
	FLOAT  fCellOverVoltPrtThre; // 单体过压保护阈值(V)
	FLOAT  fCellUnderVoltAlmThre; // 单体欠压告警阈值(V)
	FLOAT  fCellUnderVoltPrtThre; // 单体欠压保护阈值(V)
	FLOAT  fChgTempHighAlmThre; // 充电高温告警阈值(℃)
	FLOAT  fChgTempHighPrtThre; // 充电高温保护阈值(℃)
	FLOAT  fDischgTempHighAlmThre; // 放电高温告警阈值(℃)
	FLOAT  fDischgTempHighPrtThre; // 放电高温保护阈值(℃)
	FLOAT  fChgTempLowAlmThre; // 充电低温告警阈值(℃)
	FLOAT  fChgTempLowPrtThre; // 充电低温保护阈值(℃)
	FLOAT  fDischgTempLowAlmThre; // 放电低温告警阈值(℃)
	FLOAT  fDischgTempLowPrtThre; // 放电低温保护阈值(℃)
	FLOAT  fCellPoorConsisAlmThre; // 单体一致性差告警阈值(V)
	FLOAT  fCellPoorConsisPrtThre; // 单体一致性差保护阈值(V)
	WORD  wBattSOCLowAlmThre; // 电池SOC低告警阈值(%)
	WORD  wBattSOCLowPrtThre; // 电池SOC低保护阈值(%)
	WORD  wBattSOHAlmThre; // 电池SOH低告警阈值(%)
	WORD  wBattSOHPrtThre; // 电池SOH低保护阈值(%)
	FLOAT  fCellDamagePrtThre; // 单体损坏保护阈值(V)
	FLOAT  fBattChgFullAverCurrThre; // 电池充满电流(C5)
	WORD  wChgMaxDura; // 充电最长时间(min)
	WORD  wChgEndDura; // 充电末期维持时间(min)
	FLOAT  fBattSupplVolt; // 电池补充电电压(V)
	FLOAT  fCellEquVoltDiffThre; // 单体均衡启动压差阈值(V)
	WORD  wHisDataInter; // 历史数据保存间隔(Min)
	BOOLEAN  bBuzzerEnable; // 蜂鸣器使能
	WORD  wBatteryCap; // 电池容量(AH)
	WORD  wSoftAntiTheftDelay; // 软件防盗延时(Min)
	BYTE  acDeviceName[LEN_TYPE_STRING_32]; // 设备名称
	BYTE  ucDODPerDischarge; // 单次放电DOD(%)
	BYTE  ucCellUVPDelay; // 单体动态欠压保护
	BYTE  aucRelayBit[ALARM_CLASS]; // 告警干接点
	BYTE  aucAlarmLevel[ALARM_CLASS]; // 告警级别
	T_DateStruct  tEnableTime; // 启用日期
	BYTE  bUVPTempCompensationEn; // 整组欠压保护温度补偿
	BYTE  acBMSSysName[LEN_TYPE_STRING_20]; // BMS系统名称
	BYTE  ucRelayDefaultStatus; // 干接点默认状态
	FLOAT  fEnvTempHighPrtThre; // 环境温度高保护阈值(℃)
	FLOAT  fEnvTempLowPrtThre; // 环境温度低保护阈值(℃)
	FLOAT  fBattOverVoltAlmRecoThre; // 电池组过压告警恢复阈值(V)
	FLOAT  fBattOverVoltPrtRecoThre; // 电池组过压保护恢复阈值(V)
	FLOAT  fCellOverVoltAlmRecoThre; // 单体过压告警恢复阈值(V)
	FLOAT  fCellOverVoltPrtRecoThre; // 单体过压保护恢复阈值(V)
	FLOAT  fCellUnderVoltAlmRecoThre; // 单体欠压告警恢复阈值(V)
	FLOAT  fCellUnderVoltPrtRecoThre; // 单体欠压保护恢复阈值(V)
	FLOAT  fChgTempHighAlmRecoThre; // 充电高温告警恢复阈值(℃)
	FLOAT  fChgTempHighPrtRecoThre; // 充电高温保护恢复阈值(℃)
	FLOAT  fDischgTempHighAlmRecoThre; // 放电高温告警恢复阈值(℃)
	FLOAT  fDischgTempHighPrtRecoThre; // 放电高温保护恢复阈值(℃)
	FLOAT  fChgTempLowAlmRecoThre; // 充电低温告警恢复阈值(℃)
	FLOAT  fChgTempLowPrtRecoThre; // 充电低温保护恢复阈值(℃)
	FLOAT  fDischgTempLowAlmRecoThre; // 放电低温告警恢复阈值(℃)
	FLOAT  fDischgTempLowPrtRecoThre; // 放电低温保护恢复阈值(℃)
	FLOAT  fEnvTempHighAlmRecoThre; // 环境温度高告警恢复阈值(℃)
	FLOAT  fEnvTempHighPrtRecoThre; // 环境温度高保护恢复阈值(℃)
	FLOAT  fEnvTempLowAlmRecoThre; // 环境温度低告警恢复阈值(℃)
	FLOAT  fEnvTempLowPrtRecoThre; // 环境温度低保护恢复阈值(℃)
	FLOAT  fBoardTempHighAlmRecoThre; // 单板过温告警恢复阈值(℃),  参数范围不具备参考
	FLOAT  fBoardTempHighPrtRecoThre; // 单板过温保护恢复阈值(℃),  参数范围不具备参考
	BOOLEAN  bHeatingPadEnable; // 加热垫使能
	BYTE  ucGyroAngle; // 陀螺仪倾角(°)
	WORD  wBattSOCLowAlmRecoThre; // 电池SOC低告警恢复阈值(%),  参数范围不具备参考
	BYTE  ucGyroAntiTheftMode; // 陀螺仪防盗方式
	BYTE  ucBattUnlockMode; // 电池解锁方式
	FLOAT  fDischgCurrHighAlmRecThre; // 放电过流告警恢复阈值(C5),  参数范围不具备参考
	FLOAT  fBattUnderVoltAlmRecoThre; // 电池组欠压告警恢复阈值(V),  不允许设置，参数范围不具备参考性
	FLOAT  fBattUnderVoltPrtRecoThre; // 电池组欠压保护恢复阈值(V),  不允许设置，参数范围不具备参考性
	FLOAT  fCellPoorConsisPrtRecoThre; // 单体一致性差保护恢复阈值(V),  不允许设置，参数范围不具备参考性
	WORD  wHisDataInterBk; // 数据保存时间(S),  参数范围不具备参考性
	FLOAT  fCellChargeFullVolt; // 单体充电截止电压(V)
	FLOAT  fBattChgFullAverVoltThre; // 电池充满电压(V)
	FLOAT  fChargeMaxCurr; // 充电最大电流(C3)
	WORD  wDischgSwitchSOC; // 放电切换SOC
	BYTE  ucUsageScen; // 放电模式
	FLOAT  fRemoteSupplyOutVolt; // 恒压放电输出电压(V)
	FLOAT  fDischgMaxCurr; // 放电最大电流(C3)
	FLOAT  fChgHeaterStartupTemp; // 充电加热膜启动温度(℃)
	FLOAT  fChgHeaterShutdownTemp; // 充电加热膜关闭温度(℃)
	FLOAT  fHeaterTempHighThre; // 加热膜过温阈值(℃)
	FLOAT  fHeaterTempHighRel; // 加热膜过温解除(℃)
	WORD  wRechargeSOC; // 补充电SOC
	BOOLEAN  bIntCharge; // 智能间歇充电使能
	BOOLEAN  bChgCurrLiEn; // 电池组充电限流使能
	ULONG  ulPoweroffTimePrtThre; // 停电时间保护阈值(Min)
	BOOLEAN  bPoweroffTimePrtEn; // 停电时间保护使能
	BYTE  ucCurrBalanceAmplitude; // 均流SOC补偿幅值
	BYTE  ucCurrBalanceSlope; // 均流SOC补偿斜率
	BOOLEAN  bCurrBalanceSOCEn; // 均流SOC补偿使能
	BYTE  ucSleepIndicator; // 休眠指示灯
	BYTE  ucBattAddressMode; // 电池地址获取方式
	BYTE  ucBattSwitchAddr; // 电池切换地址
	BOOLEAN  bChargeRotate; // 充电轮换使能
	FLOAT  fCellSupplVolt; // 单体补充电电压(V)
	FLOAT  fDischargeEndVolt1; // 末期放电电压1(V)
	FLOAT  fDischargeEndVolt2; // 末期放电电压2(V)
	BYTE  ucCellTempPrtShield; // 单体温度保护屏蔽
	BYTE  ucChargeMachineTestMode; // 充放电机测试模式
	WORD  wPreRecordInterval; // 故障前录波时间间隔(us)
	WORD  wPostRecordInterval; // 故障后录波时间间隔(us)
	BYTE  ucPreRecordNum; // 故障前录波条数
	BYTE  ucPostRecordNum; // 故障后录波条数
	BYTE  ucMeasurePointsNum; // 测点数量X
	BYTE  ucMeasurePointsID[NUM_OF_MEASUREPOINTS]; // 可变测点ID
	FLOAT  fDischgCurrHighPrtThre; // 放电过流保护阈值(C5),  不允许设置，参数范围不具备参考性
	BYTE  ucDcrFaultAlmThre; // 直流内阻异常告警阈值
	BYTE  ucDcrFaultPrtThre; // 直流内阻异常保护阈值
	FLOAT  fCellTempRiseAbnormalThre; // 单体温升速率异常阈值(℃)
	FLOAT  fSelfDischgACR; // 自放电容量比率(%)
	BYTE  fSelfDevelopProtocolOffsetAddr; // 自研协议偏移地址
	FLOAT  fCapDCPRFaultAlmThre; // 容量衰减一致性差告警比率阈值(%)
	FLOAT  fBattFaultTempHighPrtThre; // 电池异常温度高保护阈值(℃)
	BOOLEAN  bChargeMapEnable; // 充电MAP使能
	BYTE  ucCriticalAlarmlight; // 严重告警闪烁方式
	BYTE  ucSecondaryAlarmlight; // 次级告警闪烁方式
	BYTE  ucCellType; // 电芯类型
	FLOAT  fDischgHeaterStartupTemp; // 放电加热膜启动温度(℃)
	FLOAT  fDischgHeaterShutdownTemp; // 放电加热膜关闭温度(℃)
	BYTE  ucNTCInvalidShieldNum; // NTC无效屏蔽路数
	WORD  wDischgSwitchSOC2; // 放电末期切换SOC2,  该参数暂不使用，值随放电模式联动，恒压放电模式为0%，混用为10%
} T_SysPara;

#ifdef __cplusplus
} /* end of the extern "C" block */
#endif

#endif

