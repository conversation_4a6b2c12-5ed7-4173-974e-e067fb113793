#ifndef _CIRCLE_BUFF_H
#define _CIRCLE_BUFF_H

#ifdef __cplusplus
extern "C" {
#endif    //  __cplusplus

#define	APP_DATA_LEN	          256
#define	APP_FRAME_HEAD_LEN	      14
#define MAX_FAIL_NUM              50
#define MAX_UPDATE_DATA_CACHE_NUM 10
#define MAX_UPDATE_DATA_CACHE_VIRTURE_NUM (MAX_UPDATE_DATA_CACHE_NUM * 2)

typedef struct
{
	unsigned int   ulFrameNo;
	unsigned char	   aucData[APP_DATA_LEN];
	unsigned char    auFrameHead[APP_FRAME_HEAD_LEN];
}T_AppDataSaveStruct;

unsigned int read_circle_buff(T_AppDataSaveStruct* buff, int num);
unsigned int write_circle_buff(T_AppDataSaveStruct* buff, int num);
unsigned int init_circle_buff(void);
unsigned int reset_circle_buff(void);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _CIRCLE_BUFF_H
