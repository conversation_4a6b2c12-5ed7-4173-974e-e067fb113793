#include "commBdu.h"
#include "battery.h"
#include "ActivatePort.h"
#include "realAlarm.h"
#include "sample.h"
#include "commBdu.h"
#include "interface.h"
#include "utils_rtthread_security_func.h"

static T_ActivatePortDebugInfo s_tActivatePortDebugInfo;
static BOOLEAN s_bActivateErrorFlag;

static BOOLEAN SetActivatePortDebugInfo(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if(pBattIn == NULL || pBattDeal == NULL)
    {
        return FALSE;
    }

    s_tActivatePortDebugInfo.fActivatePortVol              = pBattIn->tData.fActivatePortVol;            // 激活口电压
    s_tActivatePortDebugInfo.fBusVol                       = pBattIn->tData.fBusVol;                     // 母排电压
    s_tActivatePortDebugInfo.bActivatePortProtect          = pBattIn->tData.bActivatePortProtect;        // 激活口相关保护
    s_tActivatePortDebugInfo.ucActivateCounter             = pBattDeal->ucActivateCounter;               // 持续5秒开始激活计时
    s_tActivatePortDebugInfo.bActivateStart                = pBattDeal->bActivateStart;                  // 是否开始激活
    s_tActivatePortDebugInfo.ucActivateCurrCounter         = pBattDeal->ucActivateCurrCounter;           // 电流持续5秒激活成功
    s_tActivatePortDebugInfo.ucActivateVoltCounter         = pBattDeal->ucActivateVoltCounter;           // 电压持续5秒激活成功
    s_tActivatePortDebugInfo.wActivateNoCurrAndVoltCounter = pBattDeal->wActivateNoCurrAndVoltCounter;   // 没有电压和电流持续5分钟
    s_tActivatePortDebugInfo.ulActivateTryCounter          = pBattDeal->ulActivateTryCounter;            // 24小时重新尝试计时
    s_tActivatePortDebugInfo.bActivateTry                  = pBattDeal->bActivateTry;                    // 是否24小时重新尝试一次
    s_tActivatePortDebugInfo.bActivatePortCurrError        = pBattDeal->bActivatePortCurrError;          // 激活口回路电流异常标志（回路有充电电流）
    return TRUE;
}

static BOOLEAN ClearCounter(T_BattDealInfoStruct *pBattDeal)
{
    if(pBattDeal == NULL)
    {
        return FALSE;
    }

    pBattDeal->ucActivateCounter = 0;
    pBattDeal->ucActivateCurrCounter = 0;
    pBattDeal->ucActivateVoltCounter = 0;
    pBattDeal->wActivateNoCurrAndVoltCounter = 0;
    pBattDeal->ulActivateTryCounter = 0;

    return TRUE;
}

static BOOLEAN IsStartActivate(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if(pBattIn == NULL || pBattDeal == NULL)
    {
        return FALSE;
    }

    if(pBattDeal->bActivateStart == FALSE)
    {
        TimerPlus(pBattDeal->ucPowerOnCounter, ONE_MINUTES_SECOND);                   //计时60秒
        if(TimeOut(pBattDeal->ucPowerOnCounter, ONE_MINUTES_SECOND))
        {
            pBattDeal->bActivateTry = FALSE;
        }
    }
    else
    {
        pBattDeal->ucPowerOnCounter = 0;
    }

    if((pBattIn->tData.fActivatePortVol - pBattIn->tData.fBusVol) > 0.6 && pBattIn->tData.fActivatePortVol > 40.0f 
        && pBattDeal->bActivateTry)
    {
        TimerPlus(pBattDeal->ucActivateCounter, 5);                   //计时5秒
        if(TimeOut(pBattDeal->ucActivateCounter, 5))
        {
            pBattDeal->bActivateStart = TRUE;
            pBattDeal->bActivatePowerOffFlag = TRUE;
        }
    }
    else
    {
        pBattDeal->ucActivateCounter = 0;
        if(pBattIn->tData.fActivatePortVol < 40.0f && pBattDeal->bActivatePortCurrError == FALSE)
        {
            pBattDeal->bActivateTry = TRUE;
            pBattDeal->bActivateStart = FALSE;
            pBattDeal->ucPowerOnCounter = 0;
            pBattDeal->ucActivatePowerOffFlagCounter = 0;                  //清除调低掉电电压标志计时
            pBattDeal->bActivatePowerOffFlag = FALSE;                      //清除调低掉电电压标志
            ClearCounter(pBattDeal);
        }
    }
    return TRUE;
}

static BOOLEAN StartActivate(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if(pBattIn == NULL || pBattDeal == NULL)
    {
        return FALSE;
    }

    if(pBattDeal->bActivateStart == TRUE)
    {
        if(fabs(pBattIn->tData.fBusCurr) > pBattIn->tData.fCurrMinDet)
        {
            TimerPlus(pBattDeal->ucActivateCurrCounter, 5);            //计时5秒
            if(TimeOut(pBattDeal->ucActivateCurrCounter, 5))
            {
                pBattDeal->bActivateStart = FALSE;
                pBattDeal->bActivateTry = FALSE;                       //激活完成，不尝试激活
                pBattDeal->ucActivatePowerOffFlagCounter = 0;          //清除调低掉电电压标志计时
                pBattDeal->bActivatePowerOffFlag = FALSE;              //清除调低掉电电压标志
                ClearCounter(pBattDeal);
            }
        }
        else if(pBattIn->tData.fBusVol > 45.0 && fabs(pBattIn->tData.fBusVol - pBattIn->tData.fActivatePortVol) < 0.6)
        {
            TimerPlus(pBattDeal->ucActivateVoltCounter, 20);           //计时20秒
            if(TimeOut(pBattDeal->ucActivateVoltCounter, 20))
            {
                pBattDeal->bActivateStart = FALSE;
                pBattDeal->bActivateTry = FALSE;                       //激活完成，不尝试激活
                pBattDeal->ucActivatePowerOffFlagCounter = 0;          //清除调低掉电电压标志计时
                pBattDeal->bActivatePowerOffFlag = FALSE;              //清除调低掉电电压标志
                ClearCounter(pBattDeal);
            }
        }
        else
        {
            pBattDeal->ucActivateCurrCounter = 0;
            pBattDeal->ucActivateVoltCounter = 0;
            TimerPlus(pBattDeal->wActivateNoCurrAndVoltCounter, 10*ONE_MINUTES_SECOND);             //计时10分钟
            if(TimeOut(pBattDeal->wActivateNoCurrAndVoltCounter, 10*ONE_MINUTES_SECOND))
            {
                pBattDeal->bActivateStart = FALSE;
                pBattDeal->bActivateTry = FALSE;                       //激活完成，不尝试激活
                ClearCounter(pBattDeal);
            }
        }
    }
    return TRUE;
}

static BOOLEAN RetryBy24Hour(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if(pBattIn == NULL || pBattDeal == NULL)
    {
        return FALSE;
    }

    if(pBattDeal->bActivatePortCurrError == TRUE)
    {
        TimerPlus(pBattDeal->ulActivateTryCounter, 24*ONE_HOUR_SECONDS);                         //计时24小时
        if(TimeOut(pBattDeal->ulActivateTryCounter, 24*ONE_HOUR_SECONDS))
        {
            pBattDeal->bActivateTry = TRUE;
            pBattDeal->bActivateStart = FALSE;
            pBattDeal->ucPowerOnCounter = 0;
            pBattDeal->bActivatePortCurrError = FALSE;
            pBattDeal->ucActivatePowerOffFlagCounter = 0;          //清除调低掉电电压标志计时
            pBattDeal->bActivatePowerOffFlag = FALSE;              //清除调低掉电电压标志
            ClearCounter(pBattDeal);
        }
    }
    return TRUE;
}

BOOLEAN ActivatePort(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    T_DCFactory tBduFact;
    T_DCRealData tRealData;

    if(pBattIn == NULL || pBattDeal == NULL)
    {
        return FALSE;
    }

    rt_memset_s(&tBduFact, sizeof(T_DCFactory), 0, sizeof(T_DCFactory));
    GetBduFact(&tBduFact);

    //判断是否QTP模式、是否支持激活口和开机延时等待
    if (GetQtptestFlag() || tBduFact.bActivatePort == FALSE || !TimeOut(pBattDeal->ucCtrlChgAndDischgDelay, CTRL_CHG_DISCHG_DELAY))
    {
        return FALSE;
    }

    rt_memset_s(&tRealData, sizeof(T_DCRealData), 0, sizeof(T_DCRealData));
    GetBduReal(&tRealData);

    if ((tRealData.tDCAlarm.bActivatePortCurrError || pBattDeal->ucChargeActivatePortVoltErrCnt >=20) && pBattDeal->bActivatePortCurrError == FALSE)
    {
        pBattDeal->bActivatePortCurrError = TRUE;        //存在激活回路电流异常告警24小时不能充电
        pBattDeal->bPowerOff = TRUE;                     //配合功率转放电
        BduCtrl(SCI_CTRL_CHG2CHG, DISABLE);              //清空 转充电允许 标志位
        pBattDeal->bActivateStart = FALSE;
        pBattDeal->bActivateTry = FALSE;
        pBattDeal->ucChargeActivatePortVoltErrCnt = 0;   //激活口异常反复切换计数
    }

    IsStartActivate(pBattIn, pBattDeal);

    StartActivate(pBattIn, pBattDeal);

    RetryBy24Hour(pBattIn, pBattDeal);

    SetActivatePortDebugInfo(pBattIn, pBattDeal);

    s_bActivateErrorFlag = pBattDeal->bActivatePortCurrError;

    if(fabs(pBattIn->tData.fBusCurr) >= pBattIn->tData.fCurrMinDet)
    {
        pBattDeal->bActivatePowerOffFlag = FALSE;                      //母排有电流则认为K3闭合，则清除调低掉电电压标志
    }
    return TRUE;
}

BOOLEAN GetActivatePortDebugInfo(T_ActivatePortDebugInfo *pActivatePortDebugInfo)
{
    if(pActivatePortDebugInfo == NULL)
    {
        return FALSE;
    }

    rt_memcpy(pActivatePortDebugInfo, &s_tActivatePortDebugInfo, sizeof(T_ActivatePortDebugInfo));
    return TRUE;
}

BOOLEAN GetActivateErrorFlag(void)
{
    return s_bActivateErrorFlag;
}