
#ifndef _APP_UPDATE_MANAGE_H
#define _APP_UPDATE_MANAGE_H
#ifdef __cplusplus
extern "C" {
#endif

#define BMS_R552_UPDATE_FILE_NAME    "bms_mcu_app230321.bin"

#define UPDATE_FILE_NAME_LEN    40
#define TRIGGER_COUNTER         3
#define BAUD_RATE_9600          9600

#define TriggerEnd               0x00
#define JumpBootFail             0x81
#define DownloadFrameErr         0x80
#define DownFileNameErr          0x86
#define DownloadFuncErr          0x84
#define DownloadCRCErr           0x83

typedef struct {
    unsigned char       rtn;                                
    char                file_name[UPDATE_FILE_NAME_LEN];        
} trig_ctr_inf_t;

typedef enum {
    update_init,
    update_trans_data,
    update_interrupt
} bms_update_state_e;

typedef struct {
    bms_update_state_e update_state;   
    unsigned short     succuss_counts;            
} bms_update_manage_t;

int8_t begin_download(uint8_t mode);
void update_manage_init(void); 
int get_bms_trigger_times(void);
#ifdef __cplusplus
}
#endif     
#endif

