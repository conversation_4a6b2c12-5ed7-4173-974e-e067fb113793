#include "backup_manage.h"
#include "reset.h"
// #include "stm32h7xx_hal_cortex.h"  //与芯片相关，后续需要重新确认

//升级或备份文件记录管理
file_manage_t file_manage;

void reset_mcu(unsigned char uc_type)
{    
    //TODO:可能需要补充记录MCU的重启的原因以及保存一些记录

    // HAL_NVIC_SystemReset();  //scons报错，无该函数

}

void begin_download(unsigned char dl_mode){
    if (file_manage.flag == FLAG_IAP)   //正在进行在线升级
    {
        return;
    }

    if (dl_mode == FLAG_BACKUP)
    {
        reset_mcu(NO_RESET_CSU_BACKUP);
    }
    else
    {
        reset_mcu(NO_RESET_CSU_UPGRADE);
    }

    return;
}