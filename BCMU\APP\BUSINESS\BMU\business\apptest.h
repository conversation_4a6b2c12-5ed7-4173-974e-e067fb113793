#ifndef _APPTEST_H
#define _APPTEST_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

#include <rtthread.h>
#include "sample_new.h"
#include "data_type.h"

#define CELL_NUM 65

typedef struct {
    float bmu_cell_voltage[CELL_NUM];      //电芯电压
    float bmu_batt_mod_voltage[SAMPLE_CHIP_NUM];//模组电压
    float bmu_batt_voltage;                     //总模块电压
    float bmu_cell_temperature[CELL_NUM];  //电芯温度
    float bmu_equal_resistance_temp[SAMPLE_CHIP_NUM];    //均衡电阻温度
    float bmu_board_temp;                               //单板温度
    float bmu_batt_pos_temp;                            //电池功率端正极温度
    float bmu_batt_neg_temp;                            //电池功率端负极温度
} bmu_apptest_analog_data;

typedef struct {
    int bmu_fire_status;      //消防模块状态
    int bmu_msd_status;       //MSD连接状态
} bmu_apptest_digital_data;

typedef struct {
    unsigned char bmu_cell_temp_det_fault_status[CELL_NUM];      //温度异常告警状态
    unsigned char bmu_samp_chip_fault_status[SAMPLE_CHIP_NUM];       //电压检测异常告警状态
} bmu_apptest_alarm_data;

#pragma pack(push, 1)
typedef struct {
    char bmu_factory_sn[32];      //bmu序列号
    char bmu_soft_ver[12];       //软件版本
    date_base_t bmu_soft_release_date;       //软件发布日期
    char bmu_hardware_ver[20];       //硬件版本信息
} bmu_apptest_factory_data;
#pragma pack(pop)

int get_address_input_pin_status(void);
int set_address_output_pin_status(int status);
int get_bmu_digital_status(bmu_apptest_digital_data* data);
int get_bmu_analog_status(bmu_apptest_analog_data* data);
int get_bmu_alarm_status(bmu_apptest_alarm_data* data);
int get_bmu_factory_info(bmu_apptest_factory_data* data);

#ifdef __cplusplus
}
#endif  // __cplusplus

#endif  // _APPTEST_H
