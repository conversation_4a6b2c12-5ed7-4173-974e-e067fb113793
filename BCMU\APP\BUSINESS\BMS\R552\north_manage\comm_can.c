#include "softbus.h"
#include "device_type.h"
#include "comm_can.h"
#include "utils_server.h"
#include "utils_heart_beat.h"
#include "thread_id.h"
#include "msg.h"
#include "utils_rtthread_security_func.h"
#include "sps.h"
#include "utils_thread.h"
#include "bms_1363.h"
#include "update_manage.h"
#include "update_download.h"
#include "flash.h"


static dev_inst_t *s_dev_inst_can = NULL;
static dev_inst_t* s_north_dev_inst[BMS_NORTH_DEV_INST_NUM] = {NULL};  //北向设备实例

Static north_mgr_t *init_thread_data(link_inst_t* link_inst, unsigned char mod_id);

static msg_map can_msg_map[] =
{
     {0, NULL},
};

static unsigned char s_dev_type[BMS_NORTH_DEV_INST_NUM] = {
    DEV_BMS_CAN,
    DEV_BMS_CAN_DOWNLOAD,

};

// 设备初始化表，定义了设备的初始化函数和相关信息
static dev_init_t s_dev_init_tab[] = {
    {DEV_BMS_CAN, init_dev_bms_can, NULL},
    {DEV_BMS_CAN_DOWNLOAD, init_dev_update_can, NULL}, 
};



Static north_mgr_t *init_thread_data(link_inst_t* link_inst, unsigned char mod_id)
{
    north_mgr_t* north_mgr = NULL;

    north_mgr = rt_malloc(sizeof(north_mgr_t));
    RETURN_VAL_IF_FAIL(north_mgr != NULL, NULL);
    rt_memset_s(north_mgr, sizeof(north_mgr_t), 0x00, sizeof(north_mgr_t));

    north_mgr->cmd_buff = rt_malloc(sizeof(cmd_buf_t));
    if (north_mgr->cmd_buff == NULL)
    {
        free(north_mgr);
        return NULL;
    }
    rt_memset_s(north_mgr->cmd_buff, sizeof(cmd_buf_t), 0x00, sizeof(cmd_buf_t));

    north_mgr->link_inst = link_inst;
    return north_mgr;
}



void* init_can_comm(void *param)
{
    server_info_t *server_info = (server_info_t *)param;
    north_mgr_t* north_mgr = NULL;

    server_info->server.server.map_size = sizeof(can_msg_map) / sizeof(msg_map);
    register_server_msg_map(can_msg_map, server_info);

    init_dev_tab(s_dev_init_tab, sizeof(s_dev_init_tab) / sizeof(s_dev_init_tab[0]));
    
    //初始化北向设备实例
    int loop = 0;
    for(; loop < BMS_NORTH_DEV_INST_NUM; loop ++)
    {
        s_dev_inst_can = init_dev_inst(s_dev_type[loop]);
        RETURN_VAL_IF_FAIL(s_dev_inst_can != NULL, NULL);
        s_north_dev_inst[loop] = s_dev_inst_can;
    }
    s_dev_inst_can = init_dev_inst(DEV_BMS_CAN);
    RETURN_VAL_IF_FAIL(s_dev_inst_can != NULL, NULL);

    update_manage_init();
    north_mgr = init_thread_data(s_dev_inst_can->dev_type->link_inst, MOD_BMS_CAN);

    return north_mgr;
}



void process_can_comm(void* param)
{
    north_mgr_t* north_mgr = (north_mgr_t*)param;
    pre_thread_beat_f(THREAD_CAN_COMM);
    while (is_running(TRUE))
    {
        thread_beat_go_on(THREAD_CAN_COMM);
        if (RT_EOK == rt_sem_take(&(north_mgr->link_inst->rx_sem), NORTH_CAN1_DELAY)) 
        {
            if (SUCCESSFUL == cmd_recv(s_north_dev_inst[0], north_mgr->cmd_buff))
            {
                cmd_send(s_north_dev_inst[0], north_mgr->cmd_buff);
                if(get_bms_trigger_times() >= TRIGGER_COUNTER)
                {
                    begin_download(FLAG_CAN_IAP);//开始升级
                }
            }

            continue;
        }
    }
}


