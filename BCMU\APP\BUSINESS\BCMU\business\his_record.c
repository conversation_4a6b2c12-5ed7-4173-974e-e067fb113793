#include <string.h>
#include "his_record.h"
#include "data_type.h"
#include "storage.h"
#include "dev_bsmu.h"
#include "his_data.h"
#include "alarm_register.h"
#include "alarm_mgr_api.h"
#include "utils_data_transmission.h"
#include "type_define_in.h"
#include "hisdata_in.h"
#include "partition_def.h"
#include "utils_data_type_conversion.h"

static  short save_bcmu_his_data(unsigned char cluster_addr);

his_data_save_precision_t his_data_save_precision[]=
{
    {
        .cell_vol_precision = 1, //电芯电压精度
        .cell_temp_precision = 2, //电芯温度精度
        .positive_pole_temp_precision = 2,//正极点温度精度
        .negative_pole_temp_precision = 2,//负极点温度精度
        .batt_vol_precision = 1,//电池模块电压
        .batt_cluster_curr_precision =2,//电池簇电流
        .batt_cluster_vol_precision = 2,//电池簇电压
        .bus_ouput_vol_precision = 2,//母排输出电压
        .batt_cycle_index_precision = 0,//电池循环次数
        .env_temp_precision = 0,//环境温度
        .board_temp_precision = 0,//单板温度
        .batt_cluster_soh_precision = 0,//电池簇soh
        .batt_cluster_soc_precision = 0,//电池簇soc
        .batt_vol_precision = 1,//电池模块温度
    }
};

static his_record_t s_his_record[] = {
    {
        .record_size = sizeof(his_alm_save_t),
        //.part_name = HISALM_PART,
        .part_name = "HisAlarm.bin",
        .temp_part_name = TEMP_HISALM_PART,
        .min_save_num = HISALM_MIN_NUM,
        .max_save_num = HISALM_MAX_NUM,
        .one_sect_num = HISALM_RECNUM_ONESECT,
        .save_num_per_file = 200,
    },
    {// 历史数据
        .record_size = HISDATA_LEN, 
        //.part_name = HISDATA_PART,
        .part_name = "HisData.bin",
        .temp_part_name = TEMP_HISDATA_PART,
        .min_save_num = HISDATA_MIN_NUM,
        .max_save_num = HISDATA_MAX_NUM,
        .save_num_per_file = 2000,
        .file_num = 1,
        .file_name = {"/HisData.bin_0", },
    },
    {  // 实时告警
        .mix = FALSE,
        .record_size = sizeof(real_alm_record_t),
        .part_name = REAL_ALARM_PART,
        .temp_part_name = REAL_ALARM_PART,
        .min_save_num = MIN_REAL_ALARM_NUM,
        .max_save_num = MAX_REAL_ALARM_NUM,
        .save_num_per_file = MAX_REAL_ALARM_NUM_PER_FILE,
        .one_sect_num = 0,
        .file_num = 0,
        .file_name = NULL,
    },
};

lv3_alarm_manage_id_to_save_id_t lv3_alarm_manage_id_to_save_id[] =
{
    {GET_ALM_ID(CLUSTER_VOLT_HIGH,1,1),{1,2,3}},
    {GET_ALM_ID(CLUSTER_VOLT_HIGH,1,2),{1,2,3}},
    //TODO: 将告警管理的ID与协议使用的ID一一映射
};

alarm_manage_id_to_save_id_t alarm_manage_id_to_save_id[] =
{
    {GET_ALM_ID(BCMU_ALARM_EPO,1,1), 4},
    {GET_ALM_ID(BMU_BALENCE_CIRCUIT_FAULT,1,1), 5}
    //TODO: 将告警管理的ID与协议使用的ID一一映射

};


void init_his_record(void) {
    register_his_record_tab(s_his_record, sizeof(s_his_record)/sizeof(s_his_record[0]));
}
void alm_change_trigger_save_his_data(module_msg_t* msg_rcv){
    int i;
    for(i = 0; i < BATT_CLUSTER_NUM; i++){
        if(FAILURE == save_bcmu_his_data(i)){
            save_bcmu_his_data(i);
        }
    }
    /*TODO:后续依据簇地址决定存哪一簇
    unsigned char cluster_addr;
    cluster_addr = *msg_rcv->data;
    save_bcmu_his_data(cluster_addr);
    */
}


static  short save_bcmu_his_data(unsigned char cluster_addr){
    his_data_t his_data;

    if(SUCCESSFUL != change_sample_data_to_his_data(&his_data, cluster_addr)){
        return FAILURE;
    }
    //coverity报错屏蔽，待释放
    // if(SUCCESSFUL != save_his_data(&his_data)){
    //     return FAILURE;
    // }
    return SUCCESSFUL;
}

/**
 * @description: 提供簇数据存储成为历史数据记录的结构体的结构体地址与簇的索引
 * @param {his_data_save_t*} his_data
 * @param {unsigned char} cluster_addr
 * @return {*}
 * @Date: 2023-3-8
 */
short change_sample_data_to_his_data(his_data_t* his_data, unsigned char cluster_addr)
{
    dev_sample_data_t dev_data[1] = {0};
    get_bsmu_data(&dev_data[0],0);

    RETURN_VAL_IF_FAIL(his_data != NULL, FAILURE);
    
    his_data->cluster_addr = cluster_addr;
    for(short i = 0; i<4; i++)
    {
        //第i个bmu

        //正极点温度
        his_data->positive_pole_temp[i] =his_data_save_precision[0].positive_pole_temp_precision 
        * 0; //TODO，添加对正极温度的处理
        
        //负极点温度  
        his_data->negative_pole_temp[i] = his_data_save_precision[0].negative_pole_temp_precision 
            * 0; //TODO，添加负极温度的处理

        //电池模块电压
        his_data->batt_vol[i] = his_data_save_precision[0].batt_vol_precision 
             * ((bsmu_data_t*)(dev_data->bsmu_data))->pack_bmu_real_data[(cluster_addr-1)*4 + i].mod_vol_total;


        for(short j = 0; j<20; j++)
        {
            // 第j个电芯

            //电芯电压
            his_data->cell_vol[i*20 + j] = his_data_save_precision[0].cell_vol_precision 
                * ((bsmu_data_t*)(dev_data->bsmu_data))->pack_bmu_real_data[(cluster_addr-1)*4 + i].cell_vol[j];

            //电芯温度
            his_data->cell_temp[i*20 + j] = his_data_save_precision[0].cell_temp_precision 
                * ((bsmu_data_t*)(dev_data->bsmu_data))->pack_bmu_real_data[(cluster_addr-1)*4 + i].cell_temp[j];   
        }
    }


    //电池簇电流
    his_data->batt_cluster_curr = his_data_save_precision[0].batt_cluster_curr_precision 
            * ((bsmu_data_t*)(dev_data->bsmu_data))->pack_batt_cluster_real_data[cluster_addr].batt_current;
    
    //电池簇电压
    his_data->batt_cluster_vol = his_data_save_precision[0].batt_cluster_vol_precision 
            * ((bsmu_data_t*)(dev_data->bsmu_data))->pack_batt_cluster_real_data[cluster_addr].batt_voltage;

    //母排输出电压
    his_data->bus_ouput_vol = his_data_save_precision[0].bus_ouput_vol_precision 
            * ((bsmu_data_t*)(dev_data->bsmu_data))->pack_batt_cluster_real_data[cluster_addr].bus_current;
    
    //电池循环次数
    his_data->batt_cycle_index = his_data_save_precision[0].batt_cycle_index_precision 
            * ((bsmu_data_t*)(dev_data->bsmu_data))->pack_batt_cluster_real_data[cluster_addr].batt_cycle_times;

    //环境温度
    his_data->env_temp = his_data_save_precision[0].env_temp_precision 
            * ((bsmu_data_t*)(dev_data->bsmu_data))->pack_batt_cluster_real_data[cluster_addr].env_temp;
    
    //单板温度
    his_data->board_temp = his_data_save_precision[0].board_temp_precision 
            * ((bsmu_data_t*)(dev_data->bsmu_data))->pack_batt_cluster_real_data[cluster_addr].board_temp;

    //电池簇soh
    his_data->batt_cluster_soh = his_data_save_precision[0].batt_cluster_soh_precision 
            * ((bsmu_data_t*)(dev_data->bsmu_data))->pack_batt_cluster_real_data[cluster_addr].batt_cluster_soh;
    
    //电池簇soc
    his_data->batt_cluster_soc = his_data_save_precision[0].batt_cluster_soc_precision 
            * ((bsmu_data_t*)(dev_data->bsmu_data))->pack_batt_cluster_real_data[cluster_addr].batt_cluster_soc;

    //第一个BMU电芯均衡启动状态
    his_data->cell_balanced_status1 = 0;
            //TODO: 添加对均衡的计算

    //第二个BMU电芯均衡启动状态
    his_data->cell_balanced_status2 = 0;
            //TODO: 添加对均衡的计算

    //第三个BMU电芯均衡启动状态
    his_data->cell_balanced_status3 = 0;
            //TODO: 添加对均衡的计算

    //第四个BMU电芯均衡启动状态
    his_data->cell_balanced_status4 = 0;
            //TODO: 添加对均衡的计算

    //电池簇充电保护
    his_data->batt_cluster_chg_proc = 0;
            //TODO: 添加对电池簇充电保护计算

    //电池簇放电保护
    his_data->batt_cluster_dischg_proc = 0;
            //TODO: 添加对电池簇放电保护计算

    //电池簇充放电状态
    // his_data->batt_cluster_chg_dischg_status = 
        // ((bsmu_data_t*)(dev_data->bsmu_data))->pack_batt_cluster_real_data[cluster_addr].charge_disch_status;


    //电池簇充电使能状态
    his_data->batt_cluster_chg_enable =  
        ((bsmu_data_t*)(dev_data->bsmu_data))->pack_batt_cluster_real_data[cluster_addr].charge_enable_status;
        
    //电池簇放电使能状态
    his_data->batt_cluster_dischg_enable =  
        ((bsmu_data_t*)(dev_data->bsmu_data))->pack_batt_cluster_real_data[cluster_addr].discharge_enable_status;


    //电池簇充电低温告警
    his_data->batt_cluster_chg_low_temp_alm = 0;
        //  TODO:目前暂无，后续增加

    //电池簇过压
    his_data->batt_cluster_over_vol =  0;
        //  TODO:目前暂无，后续增加 
        
    //电池簇充电过温
    his_data->batt_cluster_chg_over_temp = 0;
        //  TODO:目前暂无，后续增加 

    //电池簇放电电低温告警
    his_data->batt_cluster_dischg_under_temp = 0;
        // TODO：目前暂无，后续增加  

    //电池簇欠压
    his_data->batt_cluster_over_vol =  0;
        // TODO：目前暂无，后续增加  
        
    //电池簇放电过温
    his_data->batt_cluster_chg_over_temp =  0;
        // TODO：目前暂无，后续增加
        

    //电池簇充电过流 
    his_data->batt_cluster_over_vol = 0;
        //TODO:依赖实时告警数据的储存位置
        //((bsmu_data_t*)(dev_data->bsmu_data))->parse_bsmu_param_alm_level.chg_over_curr_alm;
        
    //电池簇放电过流 
    his_data->batt_cluster_chg_over_temp = 0;
        //TODO:依赖实时告警数据的储存位置
        //((bsmu_data_t*)(dev_data->bsmu_data))->parse_bsmu_param_alm_level.dischg_over_curr_alm;

    //充电回路损坏
    his_data->batt_cluster_over_vol = 0;
        //TODO:依赖实时告警数据的储存位置
        //((bsmu_data_t*)(dev_data->bsmu_data))->parse_bsmu_param_alm_level.chg_circuit_damage_alm;
        
    //放电回路损坏
    his_data->batt_cluster_chg_over_temp = 0;
        //TODO:依赖实时告警数据的储存位置
        //((bsmu_data_t*)(dev_data->bsmu_data))->parse_bsmu_param_alm_level.dischg_circuit_damage_alm;

    //充电限流状态
    his_data->batt_cluster_over_vol =  0;
        // TODO：目前暂无，后续增加
        
    //放电限流状态
    his_data->batt_cluster_chg_over_temp = 0;  
        // TODO：目前暂无，后续增加
    
    return SUCCESSFUL;
}


void change_alm_and_save(void* real_alm_v)
{   
    real_alm_t* real_alm = (real_alm_t*)real_alm_v;
    his_alm_t his_alm_data = {0};

    if(real_alm == NULL)
        return;

    for(int i = 0;i <sizeof(lv3_alarm_manage_id_to_save_id)/sizeof(lv3_alarm_manage_id_to_save_id[0]);i++)
    {
        if(lv3_alarm_manage_id_to_save_id[i].alarm_manage_id == real_alm->alm_id)
        {
            if(real_alm->alm_level == ALARM_LEVEL_MINOR)
            {
                his_alm_data.alm_code = lv3_alarm_manage_id_to_save_id[i].alarm_save_id[0];
            }
            else if(real_alm->alm_level == ALARM_LEVEL_MAJOR)
            {
                his_alm_data.alm_code = lv3_alarm_manage_id_to_save_id[i].alarm_save_id[1];
            }
            else if(real_alm->alm_level == ALARM_LEVEL_CRITICAL)
            {
                his_alm_data.alm_code = lv3_alarm_manage_id_to_save_id[i].alarm_save_id[2];
            }
        }
    }

    for(int i = 0;i <sizeof(alarm_manage_id_to_save_id)/sizeof(alarm_manage_id_to_save_id[0]);i++)
    {
        if(alarm_manage_id_to_save_id[i].alarm_manage_id == real_alm->alm_id)
        {
            his_alm_data.alm_code = alarm_manage_id_to_save_id[i].alarm_save_id;
        }
    }

    time_t_to_timestruct(real_alm->start_time, &his_alm_data.start_time);
    time_t_to_timestruct(real_alm->end_time, &his_alm_data.end_time);
    save_his_alarm(&his_alm_data);  

    // 释放alarm_manage 分配的内存
    free(real_alm);
    real_alm = NULL;
}



