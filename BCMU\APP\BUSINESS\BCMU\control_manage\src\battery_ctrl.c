#include "battery_ctrl.h"
#include "msg.h"
#include "addr_distribution.h"
#include "sample.h"
#include <string.h>
#include "utils_thread.h"
#include "msg_id.h"
// #include "dev_dc_dc.h"
#include "realdata_id_in.h"
#include "realdata_save.h"

static void init_cluster_manage_info(void);
static void cluster_ctrl(void);
static void state_judge(cluster_manage_info_t *manage);
static void state_in(cluster_manage_info_t *manage);
static void state_exit(cluster_manage_info_t *manage);
static void state_run(cluster_manage_info_t *manage);

static void standby_in(cluster_manage_info_t *manage);
static void standby_act(cluster_manage_info_t *manage);
static void standby_exit(cluster_manage_info_t *manage);
static void standby_judge(cluster_manage_info_t *manage);
static void charge_in(cluster_manage_info_t *manage);
static void charge_act(cluster_manage_info_t *manage);
static void charge_exit(cluster_manage_info_t *manage);
static void charge_judge(cluster_manage_info_t *manage);
static void discharge_in(cluster_manage_info_t *manage);
static void discharge_act(cluster_manage_info_t *manage);
static void discharge_exit(cluster_manage_info_t *manage);
static void discharge_judge(cluster_manage_info_t *manage);
static void load_batt_manage_data(void);
// static unsigned char batt_charge_full(cluster_manage_info_t *manage);

static cluster_manage_info_t cluster_manage_info[BATT_CLUSTER_NUM];
static unsigned char  s_cluster_num;
static state_map_t state_map[] = {
    {BATT_STATE_STANDBY, "standby", standby_in, standby_act, standby_exit, standby_judge},
    {BATT_STATE_CHARGE,  "charge",  charge_in, charge_act, charge_exit, charge_judge},
    {BATT_STATE_DISCHARGE, "discharge", discharge_in, discharge_act, discharge_exit, discharge_judge},
};





void batt_charge_init(void) {
    init_cluster_manage_info();
    init_msg_queue(MOD_BATT_CTRL);
}

//电池状态管理
void batt_charge_manage(void* parameter) {
    while (is_running(TRUE)){
        load_batt_manage_data();
        cluster_ctrl();
    }
}


static batt_state_e get_batt_ctrl_state(void) {
    batt_state_e batt_ctrl_state = BATT_STATE_INVALID;
    module_msg_t msg = {.dest = MOD_BATT_CTRL};
    
    if (recv_msg(&msg) != SUCCESSFUL)
        return BATT_STATE_INVALID;
    
    switch(msg.msg_id) {
        case CTRL_BATT_CHARGE_MSG:
            batt_ctrl_state = BATT_STATE_CHARGE;
            break;
            
        case CTRL_BATT_DISCHARGE_MSG:
            batt_ctrl_state = BATT_STATE_DISCHARGE;
            break;

        case CTRL_BATT_STANDBY_MSG:
            batt_ctrl_state = BATT_STATE_STANDBY;
            break;
        default:
            batt_ctrl_state = BATT_STATE_INVALID;
            break;
    }
    
    return batt_ctrl_state;
}
static void cluster_ctrl(void) {
    unsigned char i;
    batt_state_e batt_ctrl_state = get_batt_ctrl_state();
    
   // if (batt_ctrl_state == BATT_STATE_INVALID)
   //     return;
    
    for (i = 0; i < s_cluster_num; i++) {
        cluster_manage_info[i].real_data.batt_ctrl_state = batt_ctrl_state;
        state_judge(&cluster_manage_info[i]);
        if (cluster_manage_info[i].next_batt_state != cluster_manage_info[i].curr_batt_state) {
            state_exit(&cluster_manage_info[i]);
            cluster_manage_info[i].curr_batt_state = cluster_manage_info[i].next_batt_state;
            state_in(&cluster_manage_info[i]);
        }
        state_run(&cluster_manage_info[i]);
    }
}

static  void init_cluster_manage_info(void) {
    unsigned char i = 0;
    cluster_topology_t cluster_topo = {0};
    
    get_cluster_topology(&cluster_topo);
    if (cluster_topo.topo_valid == TOPO_INVALID)
        return;

    s_cluster_num = cluster_topo.cluster_count;
    for (i = 0; i < s_cluster_num; i++) {
        cluster_manage_info->curr_batt_state = BATT_STATE_CHARGE;
        cluster_manage_info->next_batt_state = BATT_STATE_CHARGE;
        cluster_manage_info->cluster_index = i;
        rt_memset_s(&cluster_manage_info->manage_data, sizeof(cluster_manage_info->manage_data), 0, sizeof(cluster_manage_info->manage_data));
        cluster_manage_info->manage_data.batt_mod_num = cluster_topo.mod_num[i];
        cluster_manage_info->real_data.batt_ctrl_state = BATT_STATE_INVALID;
    }
}

static void state_in(cluster_manage_info_t *manage) {
   if (manage->curr_batt_state == BATT_STATE_INVALID)
        return;
    
    if (state_map[manage->curr_batt_state].state_in != NULL)
        state_map[manage->curr_batt_state].state_in(manage);
    LOG_I("cluster_%d, in %s", state_map[manage->curr_batt_state].state_name);
}

static void state_judge(cluster_manage_info_t *manage) {
    if (manage->curr_batt_state == BATT_STATE_INVALID)
        return;
        
    if (state_map[manage->curr_batt_state].state_judge != NULL)
        state_map[manage->curr_batt_state].state_judge(manage);
    else
        manage->next_batt_state = manage->curr_batt_state;
}

static void state_exit(cluster_manage_info_t *manage) {
   if (manage->curr_batt_state == BATT_STATE_INVALID)
        return;
    
   if (state_map[manage->curr_batt_state].state_exit != NULL)  {
       state_map[manage->curr_batt_state].state_exit(manage);
   }
   LOG_I("cluster_%d, %s--->%s because of %s", manage->cluster_index,
                                               state_map[manage->curr_batt_state].state_name,
                                               state_map[manage->next_batt_state].state_name,
                                               state_map[manage->curr_batt_state].state_change_result);
}

static void state_run(cluster_manage_info_t *manage) {
   if (manage->curr_batt_state == BATT_STATE_INVALID)
        return;
    if (state_map[manage->curr_batt_state].state_act != NULL)  {
        state_map[manage->curr_batt_state].state_act(manage);
    }
}

static void standby_in(cluster_manage_info_t *manage) {

     return;
}

static void standby_act(cluster_manage_info_t *manage) {

     return;
}

static void standby_exit(cluster_manage_info_t *manage) {

     return;
}

void charge_in(cluster_manage_info_t *manage) {
    return;
}

static void charge_act(cluster_manage_info_t *manage) {

     return;
}

static void charge_exit(cluster_manage_info_t *manage) {

     return;
}

void discharge_in(cluster_manage_info_t *manage) {
    return;
}

static void discharge_act(cluster_manage_info_t *manage) {

     return;
}

static void discharge_exit(cluster_manage_info_t *manage) {

     return;
}

static void standby_judge(cluster_manage_info_t *manage) {
    batt_state_e next_state = manage->curr_batt_state;
    batt_real_data_t real_data = manage->real_data;
    batt_state_e ctrl_state = manage->real_data.batt_ctrl_state;

    if (real_data.discharge_status) {
        next_state = BATT_STATE_DISCHARGE;
        rt_snprintf_s(manage->state_result, sizeof(manage->state_result), "blackout");
    } else if (ctrl_state == BATT_STATE_DISCHARGE) {
        next_state =  BATT_STATE_DISCHARGE;
        rt_snprintf_s(manage->state_result, sizeof(manage->state_result), "business control");
    } else if (ctrl_state == BATT_STATE_CHARGE) {
        next_state =  BATT_STATE_CHARGE;
        rt_snprintf_s(manage->state_result, sizeof(manage->state_result), "business control");
    }
    /* 
    if (cluster_data.chg_dischg_status == discharge) {
        next_state = BATT_STATE_DISCHARGE;
        snprintf(manage->state_result, sizeof(manage->state_result), "停电");
    } else if (data->batt_ctrl_state == BATT_STATE_DISCHARGE) {
        next_state = BATT_STATE_DISCHARGE;
        snprintf(manage->state_result, sizeof(manage->state_result), "业务控制");
    } else if (data->batt_ctrl_state == BATT_STATE_CHARGE) {
        next_state = BATT_STATE_CHARGE;
        snprintf(manage->state_result, sizeof(manage->state_result), "业务控制");
    } else if (长充/补电——长充周期到？) {
        next_state = BATT_STATE_CHARGE;
        snprintf(manage->state_result, sizeof(manage->state_result), "补电");
    }  
        
END:
    */
    manage->next_batt_state = next_state;
}

static void charge_judge(cluster_manage_info_t *manage) {
    batt_state_e next_state = manage->curr_batt_state;
    batt_real_data_t real_data = manage->real_data;
    batt_state_e ctrl_state = manage->real_data.batt_ctrl_state;

    if (real_data.discharge_status) {
        next_state = BATT_STATE_DISCHARGE;
        rt_snprintf_s(manage->state_result, sizeof(manage->state_result), "blackout");
    } else if (ctrl_state == BATT_STATE_DISCHARGE) {
        next_state =  BATT_STATE_DISCHARGE;
        rt_snprintf_s(manage->state_result, sizeof(manage->state_result), "business control");
    } else if (ctrl_state == BATT_STATE_STANDBY) {
        next_state =  BATT_STATE_STANDBY;
        rt_snprintf_s(manage->state_result, sizeof(manage->state_result), "business control");
    }
    /* 
    if (cluster_data.chg_dischg_status == discharge) {
        next_state = BATT_STATE_DISCHARGE;
        snprintf(manage->state_result, sizeof(manage->state_result), "停电");
    } else if (data->batt_ctrl_state == BATT_STATE_DISCHARGE) {
        next_state =  BATT_STATE_DISCHARGE;
        snprintf(manage->state_result, sizeof(manage->state_result), "业务控制");
    } else if (data->batt_ctrl_state == BATT_STATE_STANDBY) {
        next_state =  BATT_STATE_STANDBY;
        snprintf(manage->state_result, sizeof(manage->state_result), "业务控制");
    } else if (cluster_lockout(manage)) {
        next_state = BATT_STATE_STANDBY;
        snprintf(manage->state_result, sizeof(manage->state_result), "闭锁");
    } else if (batt_charge_full(manage)) {
        next_state = BATT_STATE_STANDBY;
        snprintf(manage->state_result, sizeof(manage->state_result), "充满");
    }
         
END:
    */
    manage->next_batt_state = next_state;
}

static void discharge_judge(cluster_manage_info_t *manage) {
    batt_state_e next_state = manage->curr_batt_state;
    batt_state_e ctrl_state = manage->real_data.batt_ctrl_state;
    
    if (ctrl_state == BATT_STATE_STANDBY) {
        next_state =  BATT_STATE_STANDBY;
        rt_snprintf_s(manage->state_result, sizeof(manage->state_result), "business control");
    } else if (ctrl_state == BATT_STATE_CHARGE) {
        next_state =  BATT_STATE_CHARGE;
        rt_snprintf_s(manage->state_result, sizeof(manage->state_result), "business control");
    }
    /* 
    if (cell_under_vol_alm ) {
        next_state = BATT_STATE_CHARGE;
        snprintf(manage->state_result, sizeof(manage->state_result), "电池欠压");
    } else if (data->batt_ctrl_state == BATT_STATE_CHARGE) {
        next_state = BATT_STATE_CHARGE;
        snprintf(manage->state_result, sizeof(manage->state_result), "业务控制");
    } else if (data->batt_ctrl_state == BATT_STATE_STANDBY) {
        next_state = BATT_STATE_STANDBY;
        snprintf(manage->state_result, sizeof(manage->state_result), "业务控制");
    } else if (来电) {
        next_state = BATT_STATE_STANDBY;
        snprintf(manage->state_result, sizeof(manage->state_result), "来电");
    }
        
  
END:*/
    manage->next_batt_state = next_state;
}

/* 未使用，先注释
static float charge_end_curr(cluster_manage_info_t *manage) {
    float fEndCurr;
    
    fEndCurr = manage->para_data.fChagEndCurCoeff * manage->para_data.wBattCap;
    // fEndCurr = GetValidData(fEndCurr, manage->manage_data.fChargeCurrHope-0.5, MIN_CURR_DET_BDCU + 0.3);
    return fEndCurr;
}
*/

/* 未使用，先注释
static unsigned char batt_charge_full(cluster_manage_info_t *manage) {
    
    if (manage->real_data.batt_vol >= manage->para_data.fChargeFullVol
        && manage->real_data.batt_curr < charge_end_curr(manage)) {
        manage->manage_data.ChargeEndHoldSeconds++;
    }
    else {
        manage->manage_data.ChargeEndHoldSeconds = 0;
    }
    
    if (manage->manage_data.ChargeEndHoldSeconds >= 3600) {
        manage->manage_data.ChargeEndHoldSeconds = 3600;
        return TRUE;
    }
       
    return FALSE;       
}
*/

/* 未使用，先注释
static unsigned char cluster_lockout(cluster_manage_info_t *manage) {
    if (manage->real_data.lockout_status)
        manage->manage_data.cluster_lockout_seconds++;
    else 
        manage->manage_data.cluster_lockout_seconds = 0;
    
    if (manage->manage_data.cluster_lockout_seconds >= 5000) {
        manage->manage_data.cluster_lockout_seconds = 5000;
        return TRUE;
    }
    return FALSE;       
}
*/


static void load_batt_manage_data(void) {
    /** TODO:重新开发
    unsigned char i, j;
    
    for (i = 0; i < s_cluster_num; i++) {
        get_one_data(BCMU_DATA_ID_BATT_VOLTAGE + i , &cluster_manage_info[i].real_data.batt_vol);
        get_one_data(BCMU_DATA_ID_BATT_CURRENT + i , &cluster_manage_info[i].real_data.batt_curr);
        get_one_data(BCMU_DATA_ID_LOCKOUT_STATUS + i , &cluster_manage_info[i].real_data.lockout_status);
        get_one_data(BCMU_DATA_ID_DISCHARGE_STATUS + i , &cluster_manage_info[i].real_data.discharge_status);

        for (j = 0; j < cluster_manage_info[i].manage_data.batt_mod_num; j++) {
            for(int k = 0; k< BATT_MOD_CELL_NUM; k++)
            {
                get_one_data(BCMU_DATA_ID_CELL_VOL + j*BATT_MOD_CELL_NUM + k , &cluster_manage_info[i].real_data.cell_volt[j][k]);
            }

        }
    }
    */
   return;
}


batt_state_e get_batt_state(unsigned char index) {
    return cluster_manage_info[index].curr_batt_state;
}