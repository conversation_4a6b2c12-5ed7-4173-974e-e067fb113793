#include <rtthread.h>
#include <rtdevice.h>
#include <drivers/adc.h>
#include "pin.h"
#include "pin_define.h"
#include "hal_adc.h"

#define ADC_DEV_NAME        "adc0"
#define REFER_VOLTAGE       250         /* VOLTAGE  is 3.3V */
#define CONVERT_BITS        (1 << 12)   /*  12bit */

static void set_selection_sample_gpio(ADC_SAMPLE_CHANNEL channel);

#ifdef USING_DEVICE_60XA_UIB01
static ADC_CHANNEL_INFO_STRUCT s_sample_info[SAMPLE_CHANNL_END] = {
	 {UIB01_TB_IN_3, 6, 0},
	 {UIB01_TB_IN_4, 7, 0},
	 {SMOKR_IN,      5, 0},
	 {PIN_DOOR_IN,   0, 8},
	 {PIN_WATER_IN,  0, 9},
	 {UIB01_HUM_IN,  0, 0},  // 湿度并不是由adc采样得到，放在这里只是便于处理
	 {UIB01_RLY_IN1, 1, 0},
	 {UIB01_RLY_IN2, 2, 0},
	 {UIB01_RLY_IN3, 3, 0},
	 {UIB01_RLY_IN4, 4, 0},
	 {__2V5REF,      0, 0},
};
#else
static ADC_CHANNEL_INFO_STRUCT s_sample_info[SAMPLE_CHANNL_END] =
{
	 {FUSE_AD_01, 3, 0},
	 {FUSE_AD_02, 3, 1},
	 {FUSE_AD_03, 2, 0},
	 {FUSE_AD_04, 2, 1},
	 {FUSE_AD_05, 1, 0},
	 {FUSE_AD_06, 1, 1},
	 {FUSE_AD_07, 0, 0},
	 {FUSE_AD_08, 0, 1},
	 {FUSE_AD_09, 3, 2},
	 {FUSE_AD_10, 2, 2},
	 {FUSE_AD_11, 1, 2},
	 {FUSE_AD_12, 0, 2},

	 {AIR_AD_01,  0, 3},
	 {DCFL_AD_01, 0, 4},  
	 {CFL_AD_01,  0, 5},

	 {TBIN_AD_01, 1, 6},
	 {TBIN_AD_02, 2, 6},

	 {I_AD_01,  0, 7},
	 {I_AD_02,  1, 7},
	 {I_AD_03,  2, 7},
	 {I_AD_04,  3, 7},
	 {I_AD_05,  4, 7},
	 {I_AD_06,  5, 7},
	 {I_AD_07,  6, 7},
	 {I_AD_08,  7, 7},

	 {VB_AD_01,   0, 8},
	 {VB_AD_02,   1, 8},
	 {VB_AD_03,   2, 8},
	 {VB_AD_04,   3, 8},
	 {VB_AD_05,   5, 8},
	 {VB_AD_06,   6, 8},
	 {VL_AD,      4, 8},


	 {AIN_AD_01,  4, 6},
	 {AIN_AD_02,  5, 6},
	 {AIN_AD_03,  6, 6},
	 {AIN_AD_04,  7, 6},
	 {VBM_AD_01,  3, 9},
	 {VBM_AD_02,  2, 9},
	 {VBM_AD_03,  1, 9},
	 {VBM_AD_04,  0, 9},
	 {_2VREF,     0, 6},
};
#endif

int adc_get_data_by_channel(ADC_SAMPLE_CHANNEL channel)
{
    rt_uint32_t value;
    rt_adc_device_t adc_dev;

    adc_dev = (rt_adc_device_t)rt_device_find(ADC_DEV_NAME);
    if (adc_dev == RT_NULL)
    {
        rt_kprintf("adc sample run failed! can't find %s device!\n", ADC_DEV_NAME);
        return RT_ERROR;
    }
    set_selection_sample_gpio(channel);
    rt_thread_mdelay(20);
	rt_adc_enable(adc_dev, s_sample_info[channel].adc_channel);
    value = rt_adc_read(adc_dev, s_sample_info[channel].adc_channel);
    rt_adc_disable(adc_dev, s_sample_info[channel].adc_channel);
    return value;
}

void hal_board_gpio_init(void)
{
	rt_pin_mode(PIN_A0_S1, PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_A1_S1, PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_A2_S1, PIN_MODE_OUTPUT);

    rt_pin_mode(PIN_ADDR_S1, PIN_MODE_INPUT_PULLDOWN);
    rt_pin_mode(PIN_ADDR_S2, PIN_MODE_INPUT_PULLDOWN);
    rt_pin_mode(PIN_ADDR_S3, PIN_MODE_INPUT_PULLDOWN);

	rt_pin_mode(PIN_LED_CONTROL, PIN_MODE_OUTPUT);
#ifdef USING_DEVICE_60XA_UIB01
    rt_pin_mode(PIN_UIB01_RLY6_OUT, PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_UIB01_RLY7_OUT, PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_UIB01_RLY8_OUT, PIN_MODE_OUTPUT);
#else
    rt_pin_mode(CSU_FAULT_DO_PIN, PIN_MODE_OUTPUT);
#endif
}

void set_selection_sample_gpio(ADC_SAMPLE_CHANNEL channel)
{
    unsigned char status = 0;
    status = (s_sample_info[channel].select_status & 0x01) == 1 ? 1 : 0;
    rt_pin_write(PIN_A0_S1, status);
    status = (s_sample_info[channel].select_status >> 1 & 0x01) == 1 ? 1 : 0;
    rt_pin_write(PIN_A1_S1, status);
    status = (s_sample_info[channel].select_status >> 2 & 0x01) == 1 ? 1 : 0;
    rt_pin_write(PIN_A2_S1, status);
	return ;
}

int get_board_addr(void)
{
    return rt_pin_read(PIN_ADDR_S1)*4+rt_pin_read(PIN_ADDR_S2)*2+rt_pin_read(PIN_ADDR_S3);
}

