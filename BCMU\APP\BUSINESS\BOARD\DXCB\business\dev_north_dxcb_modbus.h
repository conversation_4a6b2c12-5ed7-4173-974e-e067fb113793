#ifndef _DEV_NORTH_DEUB_MODBUS_H
#define _DEV_NORTH_DEUB_MODBUS_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "device_type.h"
#include "cmd.h"
#include "linklayer.h"
#include "protocol_layer.h"
#include "protocol_north_modbus.h"

#define R_DATA_LEN_NO_USE    0
#define S_DATA_LEN_NO_USE    0

#define NO_MATCH_REGISTER_ADDR -1
#define DATA_LEN_2          2//数据长度为2
#define DATA_LEN_4          4 //数据长度为4
#define DATA_LEN_6          6 //数据长度为6


#define PARA_CRC1_START_ADDR           1100
#define PARA_CRC1_END_ADDR             1123

#define PARA_CRC2_START_ADDR           1200
#define PARA_CRC2_END_ADDR             1248

#define PARA_CRC3_START_ADDR           1400
#define PARA_CRC3_END_ADDR             1423


#define NORTH_EDU_UART_NAME            "usart2"    // 北向串口
#define SOUTH_FAN_UART_NAME            "usart0"    // 南向风机串口
#define SOUTH_VFD_UART_NAME            "usart1"    // 南向变频器串口

typedef enum {
    TYPE_ALAM = 0,
    TYPE_PARA,
    TYPE_ANA,
    TYPE_ALAM_PARA
}sid_type_e;

typedef enum
{
    PARA_CRC1 = 0,              // crc1
    PARA_CRC2,                  // crc2
    PARA_CRC3,                  // crc3
    PARA_CRC_MAX,
}crc_type_e;


typedef struct {
    unsigned short      register_addr;                          // 寄存器地址
    unsigned char       reserve_flag;                           // 该寄存器值是否保留，1 是保留
    char                old_type;                               // 原始数据类型
    char                type;                                   // Modbus 传输数据类型
    unsigned char       precision;                              // Modbus 精度
    unsigned char       data_len;                               // Modbus 上送数据长度
    sid_type_e          sid_type;                               // SID 的类型
    unsigned int        data_addr;                              // 数据取值地址
    char                cur_dev_offset;                         // 当前设备偏移，与设备地址有关（暂时不考虑信号的偏移）
    // char                signal_dim;                             // 信号维度
} modbus_addr_map_data_t;

typedef struct{
    unsigned short register_addr;
    unsigned int ctrl_id;
    unsigned char  south_ctrl_cmd_id;
    unsigned char (*func)(unsigned char* buff);
}ctrl_cmd_modbus_t;

typedef struct {
    char src_type;
    char dst_type;
    int (*pack_fun)(int* index, unsigned char* cmd_buff, int* reg_num, int data_len, int percision, int total_len, modbus_addr_map_data_t* data_map);
} pack_fun_map_t;

typedef struct {
    char src_type;
    char dst_type;
    int (*parse_func)(int* index, unsigned char* cmd_buff, int* data_value_index, int percision, int* reg_nums, modbus_addr_map_data_t* data_map);
} parse_fun_map_t;


typedef struct{
    unsigned char is_alm_para_flag;
    unsigned char is_numeric_para_save_flag;
    unsigned char is_string_para_save_flag;
    unsigned char is_fan_commu_para;
    unsigned char is_vfd_commu_para;
    unsigned char is_nor_commu_para;
    unsigned char rtn_flag;
    unsigned char is_do_ctrl;
}special_flag_t;

typedef int (*handle_fun)(unsigned short cur_register_addr, special_flag_t* special_flag, unsigned char is_para_change);
typedef struct 
{
    unsigned short lower_limit;
    unsigned short upper_limit;
    handle_fun handle_north_func;
}special_flag_config_t;

typedef struct
{
    char data_type;
    unsigned short start_reg_addr;
    unsigned short end_reg_addr;
    unsigned short low_limit;
    unsigned short high_limit;
}special_data_scope_t;

dev_type_t* init_dev_north_dxcb(void);

/**************************************打包函数*****************/
int floattoint32u(int* index , unsigned char* data_buff , int* reg_num , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map);
int floattoint32s(int* index , unsigned char* data_buff , int* reg_num , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map);
int floattoint16u(int* index , unsigned char* data_buff , int* reg_num , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map);
int floattoint16s(int* index , unsigned char* data_buff , int* reg_num , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map);
int int16utoint16u(int* index , unsigned char* data_buff, int* reg_num , int data_len , int percision , int total_len, modbus_addr_map_data_t* data_map);
int int16stoint16s(int* index , unsigned char* data_buff, int* reg_num , int data_len , int percision , int total_len, modbus_addr_map_data_t* data_map);
int int32utoint32u(int* index , unsigned char* data_buff, int* reg_num , int data_len , int percision , int total_len, modbus_addr_map_data_t* data_map);
int int32stoint32s(int* index , unsigned char* data_buff , int* reg_nums, int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map );
int stringtostring(int* index , unsigned char* data_buff, int* reg_num , int data_len , int percision , int total_len, modbus_addr_map_data_t* data_map);
int datetoint16u(int* index , unsigned char* data_buff, int* reg_num , int data_len , int percision , int total_len, modbus_addr_map_data_t* data_map);
int chartobit(int* index , unsigned char* data_buff , int* reg_num , int data_len , int percision , int total_len, modbus_addr_map_data_t* data_map);
int int8utoint16u(int* index, unsigned char* data_buff, int* reg_nums, int data_len, int percision, int total_len, modbus_addr_map_data_t* data_map);
/*******************************解包函数*****************/
int parse_int16utofloat( int* index , unsigned char* data_buff , int* data_valude_index ,int percision , int* reg_nums, modbus_addr_map_data_t* data_map);
int parse_int16stofloat( int* index , unsigned char* data_buff , int* data_valude_index ,int percision , int* reg_nums, modbus_addr_map_data_t* data_map );
int parse_int32utofloat( int* index , unsigned char* data_buff , int* data_valude_index , int percision, int* reg_nums, modbus_addr_map_data_t* data_map);
int parse_int32stofloat( int* index , unsigned char* data_buff , int* data_valude_index ,int percision, int* reg_nums, modbus_addr_map_data_t* data_map);
int parse_int16utoint16u( int* index , unsigned char* data_buff , int* data_valude_index ,int percision , int* reg_nums, modbus_addr_map_data_t* data_map);
int parse_int32utoint32u( int* index , unsigned char* data_buff , int* data_valude_index,int percision, int* reg_nums, modbus_addr_map_data_t* data_map);
int parse_stringtostring( int* index , unsigned char* data_buff , int* data_valude_index ,int percision, int* reg_nums, modbus_addr_map_data_t* data_map);
int parse_int16utoint8u( int* index , unsigned char* data_buff , int* data_valude_index , int percision , int* reg_nums, modbus_addr_map_data_t* data_map);

int new_pack_data_to_buff(void* cmd_buff , int* index, int offset_value, modbus_addr_map_data_t* data_map, unsigned char dev_offset);
int new_pack_ana_para_data(void* dev_inst, void* cmd_buff);
int new_parse_para_data_from_buff(void* dev_inst, void* cmd_buff);
int pack_set_para_data(void* dev_inst, void* cmd_buff);
int parse_start_addr_and_reg_nums(void* dev_inst, void* cmd_buf);
void check_ana_reserve_flag(int index , void* data,modbus_addr_map_data_t* data_map);
void get_data_by_id_type(int index , void* data, modbus_addr_map_data_t* data_map);
char set_data_by_id_type(int index , void* data, modbus_addr_map_data_t* data_map);
int get_ctrl_cmd_addr(const void* data);
int get_modbus_addr(const void* data);

int parse_single_para_data_from_buff(void* dev_inst, void* cmd_buff);
int pack_set_single_para_data(void* dev_inst, void* cmd_buff);
int is_contain_alarm_para_addr(int index, modbus_addr_map_data_t* data_map);
int send_msg_process(int need_update_para);
void deal_para_crc();
int update_south_commu_para(char* usart_name, int baud_rate_offset, int parity_bit_offset);
unsigned char restore_default_para(unsigned char* buff);
unsigned char reset_dxcb(unsigned char* buff);
unsigned char clean_ams_hc_vfd_alarm(unsigned char* buff);
int ctrl_compressor_start_stop_freq(unsigned short register_addr);
int ctrl_outer_fan_speed(unsigned short register_addr);
int ctrl_inter_fan_speed(unsigned short register_addr);
int get_set_fan_speed_cmd_id_by_brand(unsigned short brand, int dev_addr_index, unsigned short fan_speed_percent);
int get_set_vfd_freq_cmd_id_by_brand(unsigned short brand, int dev_addr_index, unsigned short vfd_output_freq);
unsigned char ctrl_led(unsigned char* buff);
unsigned char ctrl_eeprom_read_write(unsigned char* buff);
char get_eev_ctrl_flag();
char get_do_ctrl_flag();
int handle_hc_start_para(unsigned short cur_register_addr, special_flag_t* special_flag, unsigned char is_para_change);
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  // 
