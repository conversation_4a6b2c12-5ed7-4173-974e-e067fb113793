#include "addr_distribution.h"
#include "cmd.h"
#include "msg.h"
#include "msg_id.h"
#include "data_type.h"
#include "device_type.h"
#include <string.h>
#include <stdlib.h>
#include "utils_rtthread_security_func.h"

static void send_msg_to_sps(unsigned int msg_id, unsigned char dest, unsigned char cmd_id,  unsigned char dev_no){
    module_msg_t msg_send;
    unsigned char msg_data[2] = {0};
    unsigned char* send_data = (unsigned char*)malloc(sizeof(msg_data));
    RETURN_IF_FAIL(send_data != NULL);

    msg_send.src = MOD_SYS_MANAGE;
    msg_send.dest = (module_id_e)dest;
    msg_send.msg_id = msg_id;

    msg_data[0] = cmd_id;
    msg_data[1] = dev_no;
    rt_memcpy_s(send_data, sizeof(msg_data), &msg_data, sizeof(msg_data));
    msg_send.data = send_data;

    send_msg(&msg_send);
}

short south_self_check(){
    cluster_topology_t cluster_topo;
    get_cluster_topology(&cluster_topo);
    int i;

    if(cluster_topo.topo_valid != TOPO_VALID){
        return FAILURE;
    }
    for(i = 1; i <= BATT_MOD_NUM; i++){
        send_msg_to_sps(EXE_DEST_CMD_MSG, MOD_BMU, BMU_GET_SELF_CHECK_STATE, i);
    }

    for(i = 1; i <= BATT_CLUSTER_NUM; i++){
        send_msg_to_sps(EXE_DEST_CMD_MSG, MOD_DC_DC, DC_DC_GET_SELFCHECK_STATE, i);
    }
    return SUCCESSFUL;
}

