/**
 * @file     north.h
 * @brief    北向处理
 * @details  This is the detail description.
 * <AUTHOR> 
 * @date     2023-01-29
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation 
 * @par History:
 *   version: author, date, descn
 */ 
#ifndef _NORTH_H
#define _NORTH_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

#include "pid.h"
#include "cmd.h"
#include "msg.h"
#include "linklayer.h"
#include "dev_bsmu.h"
#include "addr_distribution.h"

#pragma pack(push, 4)
typedef struct {
    bsmu_data_t* dev_data;
    cmd_buf_t*   cmd_buff;
    link_inst_t* link_inst;
    rt_mq_t      north_mq;
}north_mgr_t;
#pragma pack(pop)

typedef struct {
    unsigned short alm_code;
    unsigned char alm_level;
} alarm_protocol_t;


/* BSMU命令ID、DC_DC命令ID和参数ID映射的结构 */
typedef struct {
    unsigned short para_id;
    unsigned char bsmu_cmd_id;
    unsigned char dcdc_cmd_id;
}para_mapping_t;

//void init_north(void);
void* init_north1(void *param);
void* init_north2(void *param);
void bsmu_thread(void *param);
unsigned char get_bsmu_batch_para(BSMU_param_t* bmu_param);
unsigned char get_bsmu_alm_level_para(BSMU_param_alm_level_t* alm_level_para);
unsigned char get_bsmu_alm_relay_para(BSMU_param_alm_relay_t* alm_relay_para);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _NORTH_H