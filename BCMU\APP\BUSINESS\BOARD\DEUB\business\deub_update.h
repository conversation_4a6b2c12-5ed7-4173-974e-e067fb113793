#ifndef _DEUB_UPDATE_H
#define _DEUB_UPDATE_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "device_type.h"

#define UPDATE_FILE_NAME        "DEUB_APP.bin"

#define UPDATE_TRIG_FRAME   1
#define UPDATE_TRIG_COUNT       2   // 剩余一次触发在boot处理

#pragma pack(1)
typedef struct{
    int trig_success;       ///<  触发成功
    int trig_times;         ///<  触发次数
}trig_ctr_inf_t;
#pragma pack()

int parse_update_trig(void* dev_inst, void *cmd_buf);
int pack_update_trig(void* dev_inst, void *cmd_buf);
dev_type_t* init_deub_dev_north_update(void);
int bottom_update_init(void);
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  // 
