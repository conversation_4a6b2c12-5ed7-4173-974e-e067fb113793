#include <string.h>
#include <rtthread.h>
#include <pthread.h>
#include "aes_interface.h"
#include "para_manage.h"
#include "utils_string.h"
#include "para_id_in.h"
#include "utils_rtthread_security_func.h"

int check_strsid_with_rules(unsigned int sid, void* value, string_para_val_t* string_para_val)
{
    if (sid == DAC_COMMON_ID_WIFI_PASSWORD_OFFSET)
    {
        unsigned short para_offset = GET_SYS_PARA_OFFSET(DAC_COMMON_ID_WIFI_NAME_OFFSET);
        return checkPassword(value, string_para_val[para_offset].cur_val);
    }
    return SUCCESSFUL;
}

void judge_change_aes_key(void* saved_string_para, unsigned char* key)
{
    string_para_save_t* saved_string_para_tmp = NULL;
    // char machine_barcode[ENCRYPT_PARA_MAX_LEN] = {0};
    int i = 0;
    int barcode_index = 0;

    if(saved_string_para == NULL || key == NULL){
        return;
    }
    saved_string_para_tmp = (string_para_save_t*)saved_string_para;

    for(i = 0; i < saved_string_para_tmp->para_num; i++) {
        if (saved_string_para_tmp->para_val[i].id == DAC_PARA_ID_MACHINE_BARCODE)
        {
            barcode_index = i;
            break;
        }
    }
    rt_snprintf_s((char*)key, STR_LEN_16,  "key_%s", saved_string_para_tmp->para_val[barcode_index].cur_val);
}

void init_aes_key(void* saved_string_para, unsigned char* key)
{
    string_para_save_t* saved_string_para_tmp = NULL;
    int i = 0;
    int barcode_index = 0;

    if(saved_string_para == NULL || key == NULL){
        return;
    }
    saved_string_para_tmp = (string_para_save_t*)saved_string_para;

    for(i = 0; i < saved_string_para_tmp->para_num; i++) {
        if (saved_string_para_tmp->para_val[i].id == DAC_PARA_ID_MACHINE_BARCODE)
        {
            barcode_index = i;
            break;
        }
    }

    rt_snprintf_s((char*)key, STR_LEN_16,  "key_%s", saved_string_para_tmp->para_val[barcode_index].cur_val);
}
