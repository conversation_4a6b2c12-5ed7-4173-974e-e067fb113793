#ifndef  FLASH_H
#define  FLASH_H

#include <stdio.h>
#include "gd32c10x.h"
#include "gd32c10x_fmc.h"

#define GD32C103_FLASH_BASE          (0x08000000UL)
#define GD32C103_FLASH_END_ADDR			 (0x08020000UL)
#define APPLICATION_ADDRESS			     (0x08002800UL)//118kb app升级包大小
#define UPDATEINFO_STATRT            (0x00002400UL)//9kb的boot大小
#ifdef UNITEST
static uint8_t mock_flag_program = 0XFF;
#define FLAG_PROGRAM                 (&mock_flag_program) //升级标记
#else
#define FLAG_PROGRAM                 (0x0801FFE0UL) //升级标记
#endif

#define MSC_MAX_PACKET  256
#define MAX_PACKET_NUM  704

#define    FILE_NAME_LEN    40
#define    FILE_TIME_LEN    20
#define    FLAG_IAP        0x55    //待升级
#define    FLAG_CAN_IAP    0x56    //待升级CAN
#define    FLAG_APP        0x66    //升级结束
#define    FLAG_RST        0x77    //升级结束待重启
#define    FLAG_BACKUP     0x88    //运行正常待备份
#define    FLAG_OK         0x99    //已备份

#define FMC_PAGE_SIZE 	400

/* Error code */
typedef enum 
{
  FLASHIF_OK = 0,
  FLASHIF_ERASE_ERROR,
  FLASHIF_WRITE_ERROR,
  FLASHIF_READ_ERROR
} flash_error_status;

typedef union{
  struct{
    uint8_t data1;
    uint8_t data2;
    uint8_t data3;
    uint8_t data4;
  }bf;
  uint32_t data;
}fmc_data_t;
#pragma pack(4)
typedef struct
{
    uint16_t    wDataLenPerFrame;
    uint16_t    wTotalFrameNum;
    uint32_t    ulTotalFileLength;
    uint8_t        acFileName[FILE_NAME_LEN];
    uint8_t        acFileTime[FILE_TIME_LEN];
    uint16_t    wFileCheck;
    uint16_t    wResv;
} T_FileAttrStruct;

typedef struct
{
    T_FileAttrStruct tFileAttr;
    uint8_t  ucFlag;
    uint8_t     ucUpdateAddr;
    uint16_t wCounter;
    uint16_t wBackupFrameNum;
    uint16_t wCrc;
} T_FileManageStruct;
#pragma pack()

void fmc_erase_pages(uint32_t offset);
fmc_state_enum fmc_program(uint32_t offset, const uint8_t *buf, size_t size);
void fmc_read_data(uint32_t offset, uint8_t *p,uint32_t num);

#endif /* FLASH_H */
