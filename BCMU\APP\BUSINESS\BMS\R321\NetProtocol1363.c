#include "common.h"
#include "comm.h"
#include "protocol.h"
#include "NetProtocol1363.h"
#include "utils_heart_beat.h"
#include "thread_id.h"
#include "utils_rtthread_security_func.h"
#include "CommCan.h"
#include "download_tcp.h"
#include <sys/socket.h>
#if !defined(KW_CHECK) && !defined(UNITEST)
#include <rtthread.h>
#endif

// TODO: 引入<sys/socket.h>和<sys/unistd.h>的影响未验证，先手动声明，确认影响后修改删除

extern int shutdown(int s, int how);
extern int accept(int s, struct sockaddr *addr, socklen_t *addrlen);
extern int recv(int s, void *mem, size_t len, int flags);
extern int send(int s, const void *dataptr, size_t size, int flags);
extern int socket(int domain, int type, int protocol);
extern int bind(int s, const struct sockaddr *name, socklen_t namelen);
extern int listen(int s, int backlog);


Static LONG s_lConnected = -1; // TCP服务连接
Static LONG s_lSock = -1;
Static BOOLEAN s_bNetCommMode = NET_COMM_SINGLE;
Static BOOLEAN s_bNet1363NorthCommDisconnect = True;     // 1363网口通信断状态
Static UINT32 s_ulNet1363NorthDisconnCnt = 0; //1363网口北向通讯通讯断计数器


static BOOLEAN NetLinkConfig(void);
static BOOLEAN isValid1363Byte(T_CommStruct *ptComm, T_NetComStruct *NetCom,INT16U ucRevLen);
Static BOOLEAN NetDataAddrCheck(T_CommStruct *pCommNet);
Static BOOLEAN HandleClientConnection(BOOLEAN bReleasesocket);
Static BOOLEAN SetNet1363CommDisconnect(BOOLEAN flag);

// 网口1363协议通信断判断
BOOLEAN JudgeNet1363CommDisconnect(void)
{
    return s_bNet1363NorthCommDisconnect;
}


Static BOOLEAN SetNet1363CommDisconnect(BOOLEAN flag)
{
    return s_bNet1363NorthCommDisconnect = flag;
}


BOOLEAN Net1363NorthTimeCount(void)
{
    TimerPlus(s_ulNet1363NorthDisconnCnt, TIME_NET1363NORTH_MAX);
    if(TimeOut(s_ulNet1363NorthDisconnCnt, TIME_NET1363NORTH_MAX))
    {
        SetNet1363CommDisconnect(True); // 超时通信断
        s_ulNet1363NorthDisconnCnt = 0;
        return True; // 通信断
    }
    return False; // 通信正常
}


BYTE GetNetPackBufSize(BYTE buf[], int size) {
    if (buf == NULL || size <= 0) {
        return 0;
    }
    
    int i = 0;
    BYTE count = 0; 
    for(i = 0; i < size; i++) 
    {
        count++;
        if (count >= 255)
        {
            return 0;
        }
        if(buf[i] == EOI)
        {
            return count;
        }
    }
    
    return 0;
}


/***************************************************************************
 * @brief    对网口1104通信包的地址进行检查，0表示广播，1表示单播
 * @return   地址是否满足要求
 **************************************************************************/
Static BOOLEAN NetDataAddrCheck(T_CommStruct *pCommNet)
{
    if (NULL == pCommNet)
    {
        return False;
    }
    WORD wRecAddr = ASCIIToHex(pCommNet->aucRecBuf[3]) * 16 + ASCIIToHex(pCommNet->aucRecBuf[4]);

    if (wRecAddr == NET_COMM_SINGLE)
    {
        s_bNetCommMode = NET_COMM_SINGLE;
        return True;
    }
    if (wRecAddr == NET_COMM_BROADCAST)
    {
        s_bNetCommMode = NET_COMM_BROADCAST;
        return True;
    }

    return False;
}


void Process1363_NET_Comm(void* parameter)
{
    LONG lBytesReceived;
    T_CommStruct tCommNet;
    T_NetComStruct tbNetCom;
    ULONG ulSinSize = 0;
    BOOLEAN bReleaseSocket = True;
    struct sockaddr_in client_addr;

    rt_memset_s(&tCommNet, sizeof(T_CommStruct), 0, sizeof(T_CommStruct));
    rt_memset_s(&tbNetCom, sizeof(T_NetComStruct), 0, sizeof(T_NetComStruct));
    NetLinkConfig();

#ifndef UNITEST
    while (1)
#endif
    {
        bReleaseSocket = HandleClientConnection(bReleaseSocket);
        if (!bReleaseSocket)
        {
            #ifndef UNITEST
            continue;
            #endif
        }
        else
        {
            ulSinSize = sizeof(struct sockaddr_in);
            // 先检查是否存在已分配的资源，如果有，则释放
            if (s_lConnected != -1) 
            {
                closesocket(s_lConnected);
            }
            s_lConnected = accept(s_lSock, (struct sockaddr *)&client_addr, &ulSinSize);
            rt_kprintf("I got a connection from (%s , %d)\n", inet_ntoa(client_addr.sin_addr),
                        ntohs(client_addr.sin_port));
        }
#ifndef UNITEST
        while (1)
#endif
        {
            lBytesReceived = recv(s_lConnected, tbNetCom.bNetRecBuff, LEN_COMM_REC_BUF, 0);

            if (lBytesReceived < 0)
            {
                if(s_lConnected > 0)
                {
                    shutdown(s_lConnected, 2);
                    closesocket(s_lConnected);
                }

                if(s_lSock < 0)
                {
                    NetLinkConfig();
                }
                rt_memset_s(&tbNetCom, sizeof(T_NetComStruct), 0, sizeof(T_NetComStruct));
            #ifndef UNITEST
                break;
            #endif
            }
            else if (lBytesReceived > 0)
            {
                // 处理接收的数据
                while (isValid1363Byte(&tCommNet, &tbNetCom, lBytesReceived))
                {
                    SetNet1363CommDisconnect(False);
                    s_ulNet1363NorthDisconnCnt = 0;
                    if (NetDataAddrCheck(&tCommNet))
                    {
                        if (NET_COMM_BROADCAST == s_bNetCommMode)
                        {
                            MasterBroadCastToSlave(tCommNet.aucRecBuf, GetNetPackBufSize(tCommNet.aucRecBuf, sizeof(tCommNet.aucRecBuf)));
                        }
                        tCommNet.ucPortType = COMM_NET;
                        tCommNet.wSendLength = 0;
                        Deal1363CommData(&tCommNet);
                        if (NET_COMM_SINGLE == s_bNetCommMode)
                        {
                            send(s_lConnected, tCommNet.aucSendBuf, tCommNet.wSendLength, 0);
                        }
                        ClearTimeOutCounter(); // 接受到数据，清空超时计数器，重新开始计数
                    }
                    rt_memset_s(&tCommNet, sizeof(T_CommStruct), 0, sizeof(T_CommStruct));
                }
                rt_memset_s(&tbNetCom, sizeof(T_NetComStruct), 0, sizeof(T_NetComStruct));
            }
        }
        DeleteTempThread();  //判断是否删除临时线程
    }
}

static BOOLEAN NetLinkConfig(void)
{
    struct sockaddr_in server_addr;

    s_lSock = socket(AF_INET, SOCK_STREAM, 0);
    if (s_lSock == -1)
    {
        rt_kprintf("Socket error\n");
        return False;
    }

    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(NET_PROTCOL_1363_PORT);
    server_addr.sin_addr.s_addr = INADDR_ANY;
    rt_memset_s(&(server_addr.sin_zero), sizeof(server_addr.sin_zero), 0, sizeof(server_addr.sin_zero));
    if (bind(s_lSock, (struct sockaddr *)&server_addr, sizeof(struct sockaddr)) == -1)
    {
        rt_kprintf("Unable to bind\n");
        closesocket(s_lSock);
        s_lSock = -1;
        return False;
    }

    if (listen(s_lSock, 5) == -1)
    {
        rt_kprintf("Listen error\n");
        return False;
    }

    rt_kprintf("\nTCPServer Waiting for client on port 4000...\n");
    
    return True;
}

static BOOLEAN isValid1363Byte(T_CommStruct *ptComm, T_NetComStruct *NetCom,INT16U ucRevLen)
{
    BYTE ucRevByte = 0;
    BOOLEAN ucRecFrameHead = False;

    if (NetCom->wRecIndex >= ucRevLen)
    {
        return False;
    }

    if (SOI == NetCom->bNetRecBuff[NetCom->wRecIndex])
    {
        ptComm->aucRecBuf[0] = SOI;
        ptComm->bRecReady = True;
        ptComm->wRecIndex = 1;
        ptComm->ucPrtclType = PROTOCOL_1363;
        NetCom->wRecIndex++;
        ucRecFrameHead = True;
    }

    while (NetCom->wRecIndex < ucRevLen && ucRecFrameHead)
    {
        if (NetCom->bNetRecBuff[NetCom->wRecIndex] == EOI)
        {
            ptComm->bRecOk = True;
            ptComm->aucRecBuf[ptComm->wRecIndex++] = NetCom->bNetRecBuff[NetCom->wRecIndex];
            ptComm->wRecLength = ptComm->wRecIndex;
            NetCom->wRecIndex++;
            return True;
        }

        ucRevByte = NetCom->bNetRecBuff[NetCom->wRecIndex];
        if ((ucRevByte >= '0' && ucRevByte <= '9') || (ucRevByte >= 'A' && ucRevByte <= 'F'))
        {
            ptComm->aucRecBuf[ptComm->wRecIndex++] = ucRevByte;
        }
        else
        {
            return False;
        }

        NetCom->wRecIndex++;
    }

    return False;
}

/***************************************************************************
 * @brief    关闭网口1363通信套接字
 * 参数：bCloseServer：是否关闭服务器端套接字，ture：关闭服务器端套接字  False:不关闭服务器端套接字
 *       bCloseClient：是否关闭客户端套接字，ture：关闭客户端套接字  False:不关闭客户端套接字
 **************************************************************************/
void close1363socket(BOOLEAN bCloseServer,BOOLEAN bCloseClient)
{
    if(s_lSock > 0 && bCloseServer)
    {
       shutdown(s_lSock, 2);
       closesocket(s_lSock);
       s_lSock = -1;

    }
      
    if (s_lConnected > 0 && bCloseClient)
    {
        shutdown(s_lConnected, 2);
        closesocket(s_lConnected);
        s_lConnected = -1;
    }
}

// 函数名：HandleClientConnection
// 描述：判断是否apptest模式处理客户端连接
Static BOOLEAN HandleClientConnection(BOOLEAN bReleasesocket)
{
    if (GetApptestFlag() == True)
    {
        if (bReleasesocket)
        {
            shutdown(s_lSock, 2);
            closesocket(s_lSock);
            bReleasesocket = False;
        }
        bReleasesocket = False;
        rt_thread_mdelay(100);
        return bReleasesocket;
    }
    else
    {
        bReleasesocket = True;
        return bReleasesocket;
    }
}
