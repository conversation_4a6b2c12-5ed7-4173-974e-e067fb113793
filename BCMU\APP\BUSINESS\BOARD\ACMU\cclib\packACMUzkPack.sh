#!/bin/bash

# 设置错误处理：任何步骤失败立即退出
set -e
# 定义源文件路径（假设当前目录）
LOWER_FILE="cclib.bin"
SOURCE_FILE="CCLIB.BIN"
TARGET_NAME="ACMU_CCLIB.BIN"
DEST_DIR="ZXDACMUCCLIBUpdate"

# 1. 重命名文件
echo "正在重命名文件..."
mv -v "$LOWER_FILE" "$SOURCE_FILE" || { echo "字库文件正确命名，未小写文件名"; }
cp -v "$SOURCE_FILE" "$TARGET_NAME" || { echo "错误：文件重命名失败"; exit 1; }

# 2. 压缩为tar.gz文件
TAR_FILE="$DEST_DIR"".tar.gz"
echo "正在打包并压缩文件..."
tar -czf "$TAR_FILE" "$TARGET_NAME" || { echo "错误：打包/压缩失败"; exit 1; }
chmod 744 $TAR_FILE

# 输出成功信息
echo "操作完成！输出文件：$TAR_FILE"
