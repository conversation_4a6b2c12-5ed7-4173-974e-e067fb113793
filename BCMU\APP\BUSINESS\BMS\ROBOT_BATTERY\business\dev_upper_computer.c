#include "dev_can_comm.h"
#include "alarm_id_in.h"
#include "para_id_in.h"
#include "para_manage.h"
#include "alarm_mgr_api.h"

static void put_his_data_to_buff(unsigned char* buff, unsigned int* data_len);
static void put_his_alm_to_buff(unsigned char* buff, unsigned int* data_len);
static void put_his_ope_to_buff(unsigned char* buff, unsigned int* data_len);
static void put_his_rec_num_to_buff(unsigned char* buff, unsigned int* data_len);

static time_base_t s_time_start;
static time_base_t s_time_end;


//TODO:后续开发，打包函数里面需改为真实数据
static his_rec_t his_rec[] = {
    {HIS_DATA_REC, GET_RECORD_TIME_SLOT, put_his_data_to_buff},
    {HIS_DATA_REC, GET_RECORD_CORRECT,   put_his_data_to_buff},
    {HIS_DATA_REC, GET_RECORD_WRONG,     put_his_data_to_buff},
    {HIS_DATA_REC, GET_RECORD_NUM,       put_his_rec_num_to_buff},
    {HIS_ALM_REC,  GET_RECORD_TIME_SLOT, put_his_alm_to_buff},
    {HIS_ALM_REC,  GET_RECORD_CORRECT,   put_his_alm_to_buff},
    {HIS_ALM_REC,  GET_RECORD_WRONG,     put_his_alm_to_buff},
    {HIS_ALM_REC,  GET_RECORD_NUM,       put_his_rec_num_to_buff},
    {HIS_OPE_REC,  GET_RECORD_TIME_SLOT, put_his_ope_to_buff},
    {HIS_OPE_REC,  GET_RECORD_CORRECT,   put_his_ope_to_buff},
    {HIS_OPE_REC,  GET_RECORD_WRONG,     put_his_ope_to_buff},
    {HIS_OPE_REC,  GET_RECORD_NUM,       put_his_rec_num_to_buff},
};
#define HIS_REC_NUM (sizeof(his_rec)/(sizeof(his_rec_t)))



/* 命令请求 */
static remote_comm_cmd_head_t cmd_req[] RAM_SECTION = {
    {VER_01, CMD_GET_FACTORY},   // 0
    {VER_01, CMD_CTRL_ORDER},    // 1
    {VER_01, CMD_GET_STATUS},    // 2
    {VER_01, CMD_GET_ANA_DATA},  // 3
    {VER_01, CMD_GET_ALARM},     // 4
    {VER_01, CMD_GET_PARA},      // 5
    {VER_01, CMD_SET_PARA},      // 6
    {VER_01, CMD_GET_HIST_DATA}, // 7
    {VER_01, CMD_GET_HIS_ALM},   // 8
    {VER_01, CMD_GET_OPE_REC},   // 9
    {VER_01, CMD_GET_CELL_STA_REC}, // 10
    {VER_01, CMD_GET_CHG_REC},      // 11
    {0}
};

/* 命令应答   */
static remote_comm_cmd_head_t cmd_ack[] RAM_SECTION = {
    {VER_01, CMD_GET_FACTORY},   // 0
    {VER_01, CMD_CTRL_ORDER},    // 1
    {VER_01, CMD_GET_STATUS},    // 2
    {VER_01, CMD_GET_ANA_DATA},  // 3
    {VER_01, CMD_GET_ALARM},     // 4
    {VER_01, CMD_GET_PARA},      // 5
    {VER_01, CMD_SET_PARA},      // 6
    {VER_01, CMD_GET_HIST_DATA}, // 7
    {VER_01, CMD_GET_HIS_ALM},   // 8
    {VER_01, CMD_GET_OPE_REC},   // 9
    {VER_01, CMD_GET_CELL_STA_REC}, // 10
    {VER_01, CMD_GET_CHG_REC},      // 11
    {0}
};

/* 获取厂家信息 */
static data_info_id_verison_t cmd_factory_info[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, FACTORY_CMD_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_string, ARRAY_SIZE_10, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BMS_SYS_NAME},
    {SEG_DATA, type_string, ARRAY_SIZE_2, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BMS_SOFTWARE_VER},
    {SEG_DATA, type_string, ARRAY_SIZE_20, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BATTERY_FACTORY_NAME},
    {SEG_DATA, type_string, ARRAY_SIZE_20, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BATTERY_FACTORY_SERIAL},
    {SEG_DATA, type_string, ARRAY_SIZE_15, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BMS_SERIAL},
    {SEG_DATA, type_date, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BMS_SOFTWARE_DATE},
    {SEG_DATA, type_date, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BDU_SOFTWARE_DATE},
    {SEG_DATA, type_string, ARRAY_SIZE_6, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BDU_SOFTWARE_VER},
    {SEG_DATA, type_string, ARRAY_SIZE_8, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_PLATFORM_VER},
    {SEG_DATA, type_string, ARRAY_SIZE_20, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BMS_MODEL},
    {SEG_DATA, type_string, ARRAY_SIZE_10, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BOOT_DATE},
    {SEG_DATA, type_string, ARRAY_SIZE_20, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_IMEI},
    {SEG_DATA, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CURRENT_LIMITING_MAX},
    {SEG_DATA, type_time, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_FACTORY_TIME},
    {SEG_DATA, type_time, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_FIRST_START_TIME},
    {SEG_DATA, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CELL_NUM},
    {SEG_DATA, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CHARGE_CURR_MAX},
    {SEG_DATA, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BMS_TYPE},
    {SEG_DATA, type_string, ARRAY_SIZE_15, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_SOFTWARE_FLAG},
};



/* 获取状态量 */
static data_info_id_verison_t cmd_status_info[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, STATUS_CMD_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CHARGE_STATUS},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_DISCHARGE_STATUS},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BUZZER_STATUS},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_HEAT_MAT_STATUS},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BATT_FULL_STATUS},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BATT_VOL_REGULA_STATUS},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BDU_CHARGE_PRO_STATUS},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BDU_DISCHARGE_PRO_STATUS},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BATT_EQUAL_STATUS},
    {SEG_DATA, type_multi_bit, ARRAY_SIZE_3, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BATT_MANAGE_MODE},
    {SEG_DATA, type_multi_bit, ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BMS_CHARGE_STATUS},
};

/* 获取模拟量 */
static data_info_id_verison_t cmd_get_realdat[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, REAL_DATA_CMD_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BATT_CURR},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BATT_VOL},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BUS_CURR},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BUS_VOL},

    {SEG_DATA, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, CELL_NUM, NOT_NEED_INTERACT},
    {SEG_LOOP, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, CELL_NUM, NOT_NEED_INTERACT}, //LOOP开始
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CELL_VOL},
    {SEG_LOOP_OVER},  //LOOP结束

    {SEG_DATA, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, CELL_NUM, NOT_NEED_INTERACT},
    {SEG_LOOP, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, CELL_NUM, NOT_NEED_INTERACT}, //LOOP开始
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CELL_TEMP},
    {SEG_LOOP_OVER},  //LOOP结束

    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_ENV_TEMP},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BOARD_TEMP},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BATT_SOC},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BATT_SOH},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BATT_SOC_MAX},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BATT_CYCLE_NUM},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BATT_CHARGE_RES_TIME},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BATT_DISCHARGE_RES_TIME},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_ACCU_DISCHARGE_CAP_AH},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_ACCU_CHARGE_CAP_AH},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_ACCU_DISCHARGE_CAP_KWH},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_ACCU_CHARGE_CAP_KWH},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_REMAIN_EFFI_CYCLE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BATT_CAP},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BATT_CHARGE_REQ_VOL},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BATT_CHARGE_REQ_CURR},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CURRENT_CHARGE_VOL},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CURRENT_CHARGE_LIMIT_CURR},
};



/* 获取参数量 */
static data_info_id_verison_t cmd_get_para[] RAM_SECTION = {
    {SEG_DATA,        type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA_LEN, NOT_NEED_INTERACT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CELL_OVER_VOL_ALM_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CELL_UNDER_VOL_ALM_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CELL_OVER_VOL_PRT_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CELL_UNDER_VOL_PRT_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_BATT_OVER_VOL_ALM_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_BATT_UNDER_VOL_ALM_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_BATT_OVER_VOL_PRT_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_BATT_UNDER_VOL_PRT_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CHG_HIGH_TEMP_ALM_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_short,          ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CHG_LOW_TEMP_ALM_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CHG_HIGH_TEMP_PRT_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_short,          ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CHG_LOW_TEMP_PRT_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_DISCHG_HIGH_TEMP_ALM_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_short,          ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_DISCHG_LOW_TEMP_ALM_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_DISCHG_HIGH_TEMP_PRT_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_short,          ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_DISCHG_LOW_TEMP_PRT_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CHG_CURR_HIGH_ALM_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CHG_CURR_HIGH_PRT_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_DISCHG_CURR_HIGH_PRT_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_DISCHG_CURR_HIGH_ALM_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_BATT_UNDER_VOL_PRT_RECO_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CELL_OVER_VOL_PRT_RECO_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CELL_UNDER_VOL_PRT_RECO_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CHG_HIGH_TEMP_PRT_RECO_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_DISCHG_HIGH_TEMP_PRT_RECO_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CHG_LOW_TEMP_PRT_RECO_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_short,          ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_DISCHG_LOW_TEMP_PRT_RECO_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CELL_POOR_CONSIS_ALM_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_ENV_TEMP_HIGH_ALM_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_short,          ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_ENV_TEMP_LOW_ALM_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_ENABLE_DATE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_BOARD_TEMP_HIGH_ALM_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_BOARD_TEMP_HIGH_PRT_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_BOARD_TEMP_HIGH_ALM_RECO_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_BOARD_TEMP_HIGH_PRT_RECO_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CELL_OVER_VOL_ALM_RECO_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CELL_UNDER_VOL_ALM_RECO_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_BATT_OVER_VOL_ALM_RECO_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_BATT_UNDER_VOL_ALM_RECO_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_BATT_OVER_VOL_PRT_RECO_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CHG_HIGH_TEMP_ALM_RECO_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CHG_LOW_TEMP_ALM_RECO_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_DISCHG_HIGH_TEMP_ALM_RECO_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_short,          ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_DISCHG_LOW_TEMP_ALM_RECO_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CHG_CURR_HIGH_ALM_RECO_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CHG_CURR_HIGH_PRT_RECO_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_DISCHG_CURR_HIGH_ALM_RECO_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_DISCHG_CURR_HIGH_PRT_RECO_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CELL_POOR_CONSIS_ALM_RECO_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_ENV_TEMP_HIGH_ALM_RECO_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_short,          ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_ENV_TEMP_LOW_ALM_RECO_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CELL_POOR_CONSIS_PRT_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CELL_POOR_CONSIS_PRT_RECO_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_BATT_SOC_LOW_ALM_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_BATT_SOC_LOW_ALM_RECO_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_BATT_SOC_LOW_PRT_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_BATT_SOC_LOW_PRT_RECO_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_BATT_SOH_LOW_ALM_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_BATT_SOH_LOW_ALM_RECO_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_BATT_SOH_LOW_PRT_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_BATT_SOH_LOW_PRT_RECO_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_BATT_CHG_FULL_AVER_CURR_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CHG_MAX_DURA_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CHG_END_DURA_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CELL_EQU_START_VOLT_DIFF_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CELL_EQU_STOP_VOLT_DIFF_THRE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_HIS_DATA_INTER_OFFSET},
    {SEG_SYSTEM_PARA, type_string,        ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_DEVICE_NAME_OFFSET},
    {SEG_SYSTEM_PARA, type_string,        ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_BMS_SYSTEM_NAME_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CELL_CHG_END_VOLT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CHG_CURR_LIMIT_METHOD_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CHG_MAP_ENABLE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_BATT_FULL_CHARGE_VOLT_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CELL_CHG_LIMIT_VOL_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_CELL_TYPE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_BATT_SUPPL_VOL_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_PARA_ID_BATT_SUPPL_PERIOD_OFFSET},
};



/* 获取充电记录 */
static data_info_id_verison_t cmd_get_chg_rec[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, CHG_REC_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CURRENT_CHARGE_TIME},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_OUTPUT_ENERGY},
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CHARGER_NUMBER},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CHARGE_TERMINATE_SOC},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CELL_MAX_VOLTAGE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CELL_MIN_VOLTAGE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CELL_MAX_TEMPERATURE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_CELL_MIN_TEMPERATURE},
};



// 告警id
int cell_vol_alarm_id_tab[] = 
{
    ROBOT_ALARM_ID_CELL_UNDER_VOL,
    ROBOT_ALARM_ID_CELL_OVER_VOL,
    ROBOT_ALARM_ID_CELL_UNDER_VOL_PROT,
    ROBOT_ALARM_ID_CELL_OVER_VOL_PROT,
    ROBOT_ALARM_ID_CELLN_BAD_PROT,
    ROBOT_ALARM_ID_CELL_DY_UNDER_VOL_PROT,
};

int alarm_id_tab_1[] = 
{
    ROBOT_ALARM_ID_BATT_UNDER_VOL,
    ROBOT_ALARM_ID_BATT_OVER_VOL,
    ROBOT_ALARM_ID_BATT_UNDER_VOL_PROT,
    ROBOT_ALARM_ID_BATT_OVER_VOL_PROT,
    ROBOT_ALARM_ID_CELL_LAG_VOL,
};

int cell_temp_alarm_id_tab[] = 
{
    ROBOT_ALARM_ID_CELL_CHARGE_UNDER_TEMP,
    ROBOT_ALARM_ID_CELL_CHARGE_OVER_TEMP,
    ROBOT_ALARM_ID_CELL_CHARGE_UNDER_TEMP_PROT,
    ROBOT_ALARM_ID_CELL_CHARGE_OVER_TEMP_PROT,
    ROBOT_ALARM_ID_CELL_DISCHARGE_UNDER_TEMP,
    ROBOT_ALARM_ID_CELL_DISCHARGE_OVER_TEMP,
    ROBOT_ALARM_ID_CELL_DISCHARGE_UNDER_TEMP_PROT,
    ROBOT_ALARM_ID_CELL_DISCHARGE_OVER_TEMP_PROT,
    ROBOT_ALARM_ID_CELL_TEMP_SENSOR_BAD,
};

int alarm_id_tab_2[] = 
{
    ROBOT_ALARM_ID_ENV_OVER_TEMP,
    ROBOT_ALARM_ID_ENV_UNDER_TEMP,
    ROBOT_ALARM_ID_ENV_TEMP_SENSOR_BAD,
    ROBOT_ALARM_ID_BATT_CHARGE_OVER_CURR,
    ROBOT_ALARM_ID_BATT_CHARGE_OVER_CURR_PROT,
    ROBOT_ALARM_ID_BATT_DISCHARGE_OVER_CURR,
    ROBOT_ALARM_ID_BATT_DISCHARGE_OVER_CURR_PROT,
    ROBOT_ALARM_ID_BATT_SHORT,
    ROBOT_ALARM_ID_DISCHARGE_SWITCH_BAD,
    ROBOT_ALARM_ID_CHARGE_SWITCH_BAD,
    ROBOT_ALARM_ID_BATT_REVERSE_PROT,
    ROBOT_ALARM_ID_CHARGE_LIMIT_BAD,
    ROBOT_ALARM_ID_CELL_BAD_PROT,
    ROBOT_ALARM_ID_VOL_SAMPLE_BAD,
    ROBOT_ALARM_ID_BOARD_OVER_TEMP,
    ROBOT_ALARM_ID_BOARD_OVER_PROT,
    ROBOT_ALARM_ID_BOARD_CON_PROT,
    ROBOT_ALARM_ID_BATT_SOC_LOW,
    ROBOT_ALARM_ID_BATT_SOC_LOW_PROT,
    ROBOT_ALARM_ID_BATT_SOH_LOW,
    ROBOT_ALARM_ID_BATT_SOH_LOW_PROT,
    ROBOT_ALARM_ID_CELL_VOL_SAMPLE_BAD,
    ROBOT_ALARM_ID_BDU_UNDER_VOL_PROT,
    ROBOT_ALARM_ID_BDU_EEPROM_BAD,
    ROBOT_ALARM_ID_BDU_BUS_UNDER_VOL_PROT,
    ROBOT_ALARM_ID_BDU_BUS_OVER_VOL_PROT,
    ROBOT_ALARM_ID_BDU_COMM_FAIL,
    ROBOT_ALARM_ID_BDU_CHARGE_UNDER_VOL_PROT,
    ROBOT_ALARM_ID_BDU_LOCK,
    ROBOT_ALARM_ID_ENV_OVER_TEMP_PROT,
    ROBOT_ALARM_ID_ENV_UNDER_TEMP_PROT,
    ROBOT_ALARM_ID_BALANCE_BAD,
    ROBOT_ALARM_ID_DC_IN_RESIS_BAD,
    ROBOT_ALARM_ID_DC_IN_RESIS_BAD_PROT,
    ROBOT_ALARM_ID_CELL_TEMP_RISE_ABNORMAL,
    ROBOT_ALARM_ID_SELF_DISCHARGE_ABNORMAL,
    ROBOT_ALARM_ID_CELL_TEMP_ABNORMAL,
    ROBOT_ALARM_ID_BATT_OVER_TEMP_PROT,
    ROBOT_ALARM_ID_CAP_DECAY_CONSIS_POOR,
};



para_cmd_t para_tab[] = {
    {0x01, type_unsigned_short, DOP_2, ROBOT_PARA_ID_CELL_OVER_VOL_ALM_THRE_OFFSET},
    {0x02, type_unsigned_short, DOP_2, ROBOT_PARA_ID_CELL_UNDER_VOL_ALM_THRE_OFFSET},
    {0x03, type_unsigned_short, DOP_2, ROBOT_PARA_ID_CELL_OVER_VOL_PRT_THRE_OFFSET},
    {0x04, type_unsigned_short, DOP_2, ROBOT_PARA_ID_CELL_UNDER_VOL_PRT_THRE_OFFSET},
    {0x05, type_unsigned_short, DOP_1, ROBOT_PARA_ID_BATT_OVER_VOL_ALM_THRE_OFFSET},
    {0x06, type_unsigned_short, DOP_1, ROBOT_PARA_ID_BATT_UNDER_VOL_ALM_THRE_OFFSET},
    {0x07, type_unsigned_short, DOP_1, ROBOT_PARA_ID_BATT_OVER_VOL_PRT_THRE_OFFSET},
    {0x08, type_unsigned_short, DOP_1, ROBOT_PARA_ID_BATT_UNDER_VOL_PRT_THRE_OFFSET},
    {0x09, type_unsigned_short, DOP_0, ROBOT_PARA_ID_CHG_HIGH_TEMP_ALM_THRE_OFFSET},
    {0x0A, type_short,          DOP_0, ROBOT_PARA_ID_CHG_LOW_TEMP_ALM_THRE_OFFSET},
    {0x0B, type_unsigned_short, DOP_0, ROBOT_PARA_ID_CHG_HIGH_TEMP_PRT_THRE_OFFSET},
    {0x0C, type_short,          DOP_0, ROBOT_PARA_ID_CHG_LOW_TEMP_PRT_THRE_OFFSET},
    {0x0D, type_unsigned_short, DOP_0, ROBOT_PARA_ID_DISCHG_HIGH_TEMP_ALM_THRE_OFFSET},
    {0x0E, type_short,          DOP_0, ROBOT_PARA_ID_DISCHG_LOW_TEMP_ALM_THRE_OFFSET},
    {0x0F, type_unsigned_short, DOP_0, ROBOT_PARA_ID_DISCHG_HIGH_TEMP_PRT_THRE_OFFSET},
    {0x10, type_short,          DOP_0, ROBOT_PARA_ID_DISCHG_LOW_TEMP_PRT_THRE_OFFSET},
    {0x11, type_unsigned_short, DOP_3, ROBOT_PARA_ID_CHG_CURR_HIGH_ALM_THRE_OFFSET},
    {0x12, type_unsigned_short, DOP_3, ROBOT_PARA_ID_CHG_CURR_HIGH_PRT_THRE_OFFSET},
    {0x13, type_unsigned_short, DOP_3, ROBOT_PARA_ID_DISCHG_CURR_HIGH_PRT_THRE_OFFSET},
    {0x14, type_unsigned_short, DOP_3, ROBOT_PARA_ID_DISCHG_CURR_HIGH_ALM_THRE_OFFSET},
    {0x15, type_unsigned_short, DOP_1, ROBOT_PARA_ID_BATT_UNDER_VOL_PRT_RECO_THRE_OFFSET},
    {0x16, type_unsigned_short, DOP_2, ROBOT_PARA_ID_CELL_OVER_VOL_PRT_RECO_THRE_OFFSET},
    {0x17, type_unsigned_short, DOP_2, ROBOT_PARA_ID_CELL_UNDER_VOL_PRT_RECO_THRE_OFFSET},
    {0x18, type_unsigned_short, DOP_2, ROBOT_PARA_ID_CHG_HIGH_TEMP_PRT_RECO_THRE_OFFSET},
    {0x19, type_unsigned_short, DOP_2, ROBOT_PARA_ID_DISCHG_HIGH_TEMP_PRT_RECO_THRE_OFFSET},
    {0x1A, type_unsigned_short, DOP_2, ROBOT_PARA_ID_CHG_LOW_TEMP_PRT_RECO_THRE_OFFSET},
    {0x1B, type_short,          DOP_2, ROBOT_PARA_ID_DISCHG_LOW_TEMP_PRT_RECO_THRE_OFFSET},
    {0x1C, type_unsigned_short, DOP_2, ROBOT_PARA_ID_CELL_POOR_CONSIS_ALM_THRE_OFFSET},
    {0x1D, type_unsigned_short, DOP_0, ROBOT_PARA_ID_ENV_TEMP_HIGH_ALM_THRE_OFFSET},
    {0x1E, type_short,          DOP_0, ROBOT_PARA_ID_ENV_TEMP_LOW_ALM_THRE_OFFSET},
    {0x1F, type_unsigned_int,   DOP_0, ROBOT_PARA_ID_ENABLE_DATE_OFFSET},
    {0x20, type_unsigned_short, DOP_0, ROBOT_PARA_ID_BOARD_TEMP_HIGH_ALM_THRE_OFFSET},
    {0x21, type_unsigned_short, DOP_0, ROBOT_PARA_ID_BOARD_TEMP_HIGH_PRT_THRE_OFFSET},
    {0x22, type_unsigned_short, DOP_0, ROBOT_PARA_ID_BOARD_TEMP_HIGH_ALM_RECO_THRE_OFFSET},
    {0x23, type_unsigned_short, DOP_0, ROBOT_PARA_ID_BOARD_TEMP_HIGH_PRT_RECO_THRE_OFFSET},
    {0x24, type_unsigned_short, DOP_2, ROBOT_PARA_ID_CELL_OVER_VOL_ALM_RECO_THRE_OFFSET},
    {0x25, type_unsigned_short, DOP_2, ROBOT_PARA_ID_CELL_UNDER_VOL_ALM_RECO_THRE_OFFSET},
    {0x26, type_unsigned_short, DOP_1, ROBOT_PARA_ID_BATT_OVER_VOL_ALM_RECO_THRE_OFFSET},
    {0x27, type_unsigned_short, DOP_1, ROBOT_PARA_ID_BATT_UNDER_VOL_ALM_RECO_THRE_OFFSET},
    {0x28, type_unsigned_short, DOP_1, ROBOT_PARA_ID_BATT_OVER_VOL_PRT_RECO_THRE_OFFSET},
    {0x29, type_unsigned_short, DOP_0, ROBOT_PARA_ID_CHG_HIGH_TEMP_ALM_RECO_THRE_OFFSET},
    {0x2A, type_unsigned_short, DOP_0, ROBOT_PARA_ID_CHG_LOW_TEMP_ALM_RECO_THRE_OFFSET},
    {0x2B, type_unsigned_short, DOP_0, ROBOT_PARA_ID_DISCHG_HIGH_TEMP_ALM_RECO_THRE_OFFSET},
    {0x2C, type_short,          DOP_0, ROBOT_PARA_ID_DISCHG_LOW_TEMP_ALM_RECO_THRE_OFFSET},
    {0x2D, type_unsigned_short, DOP_3, ROBOT_PARA_ID_CHG_CURR_HIGH_ALM_RECO_THRE_OFFSET},
    {0x2E, type_unsigned_short, DOP_3, ROBOT_PARA_ID_CHG_CURR_HIGH_PRT_RECO_THRE_OFFSET},
    {0x2F, type_unsigned_short, DOP_3, ROBOT_PARA_ID_DISCHG_CURR_HIGH_ALM_RECO_THRE_OFFSET},
    {0x30, type_unsigned_short, DOP_3, ROBOT_PARA_ID_DISCHG_CURR_HIGH_PRT_RECO_THRE_OFFSET},
    {0x31, type_unsigned_short, DOP_2, ROBOT_PARA_ID_CELL_POOR_CONSIS_ALM_RECO_THRE_OFFSET},
    {0x32, type_unsigned_short, DOP_2, ROBOT_PARA_ID_ENV_TEMP_HIGH_ALM_RECO_THRE_OFFSET},
    {0x33, type_short,          DOP_2, ROBOT_PARA_ID_ENV_TEMP_LOW_ALM_RECO_THRE_OFFSET},
    {0x34, type_unsigned_short, DOP_2, ROBOT_PARA_ID_CELL_POOR_CONSIS_PRT_THRE_OFFSET},
    {0x35, type_unsigned_short, DOP_2, ROBOT_PARA_ID_CELL_POOR_CONSIS_PRT_RECO_THRE_OFFSET},
    {0x36, type_unsigned_short, DOP_0, ROBOT_PARA_ID_BATT_SOC_LOW_ALM_THRE_OFFSET},
    {0x37, type_unsigned_short, DOP_0, ROBOT_PARA_ID_BATT_SOC_LOW_ALM_RECO_THRE_OFFSET},
    {0x38, type_unsigned_short, DOP_0, ROBOT_PARA_ID_BATT_SOC_LOW_PRT_THRE_OFFSET},
    {0x39, type_unsigned_short, DOP_0, ROBOT_PARA_ID_BATT_SOC_LOW_PRT_RECO_THRE_OFFSET},
    {0x3A, type_unsigned_short, DOP_0, ROBOT_PARA_ID_BATT_SOH_LOW_ALM_THRE_OFFSET},
    {0x3B, type_unsigned_short, DOP_0, ROBOT_PARA_ID_BATT_SOH_LOW_ALM_RECO_THRE_OFFSET},
    {0x3C, type_unsigned_short, DOP_0, ROBOT_PARA_ID_BATT_SOH_LOW_PRT_THRE_OFFSET},
    {0x3D, type_unsigned_short, DOP_0, ROBOT_PARA_ID_BATT_SOH_LOW_PRT_RECO_THRE_OFFSET},
    {0x3E, type_unsigned_short, DOP_2, ROBOT_PARA_ID_BATT_CHG_FULL_AVER_CURR_OFFSET},
    {0x3F, type_unsigned_short, DOP_0, ROBOT_PARA_ID_CHG_MAX_DURA_OFFSET},
    {0x40, type_unsigned_short, DOP_0, ROBOT_PARA_ID_CHG_END_DURA_OFFSET},
    {0x41, type_unsigned_short, DOP_2, ROBOT_PARA_ID_CELL_EQU_START_VOLT_DIFF_THRE_OFFSET},
    {0x42, type_unsigned_short, DOP_2, ROBOT_PARA_ID_CELL_EQU_STOP_VOLT_DIFF_THRE_OFFSET},
    {0x43, type_unsigned_short, DOP_0, ROBOT_PARA_ID_HIS_DATA_INTER_OFFSET},
    {0x44, type_string,         DOP_0, ROBOT_PARA_ID_DEVICE_NAME_OFFSET},
    {0x45, type_string,         DOP_0, ROBOT_PARA_ID_BMS_SYSTEM_NAME_OFFSET},
    {0x46, type_unsigned_short, DOP_2, ROBOT_PARA_ID_CELL_CHG_END_VOLT_OFFSET},
    {0x47, type_unsigned_short, DOP_0, ROBOT_PARA_ID_CHG_CURR_LIMIT_METHOD_OFFSET},
    {0x48, type_unsigned_char,  DOP_0, ROBOT_PARA_ID_CHG_MAP_ENABLE_OFFSET},
    {0x49, type_unsigned_short, DOP_1, ROBOT_PARA_ID_BATT_FULL_CHARGE_VOLT_OFFSET},
    {0x4A, type_unsigned_short, DOP_2, ROBOT_PARA_ID_CELL_CHG_LIMIT_VOL_OFFSET},
    {0x4B, type_unsigned_char,  DOP_0, ROBOT_PARA_ID_CELL_TYPE_OFFSET},
    {0x4C, type_unsigned_short, DOP_2, ROBOT_PARA_ID_BATT_SUPPL_VOL_OFFSET},
    {0x4D, type_unsigned_short, DOP_0, ROBOT_PARA_ID_BATT_SUPPL_PERIOD_OFFSET},
};
#define PARA_NUM sizeof(para_tab) / sizeof(para_cmd_t)




/* 打包命令 */
static cmd_parse_info_id_verison_t cmd_pack_info[] = {
    {&cmd_factory_info[0],  sizeof(cmd_factory_info)/sizeof(data_info_id_verison_t)},  // 0
    {&cmd_status_info[0],  sizeof(cmd_status_info)/sizeof(data_info_id_verison_t)},  // 1
    {&cmd_get_realdat[0],  sizeof(cmd_get_realdat)/sizeof(data_info_id_verison_t)},  // 2
    {&cmd_get_para[0],  sizeof(cmd_get_para)/sizeof(data_info_id_verison_t)},  // 3
    {&cmd_get_chg_rec[0],  sizeof(cmd_get_chg_rec)/sizeof(data_info_id_verison_t)},  // 4
};

/* 通信协议命令表 */
static cmd_t no_poll_cmd_tab[] = {
    {GET_FACTORY_INFO, CMD_PASSIVE,  &cmd_req[0], &cmd_ack[0], sizeof(remote_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[0], NULL},
    {CTRL_ORDER, CMD_PASSIVE,  &cmd_req[1], &cmd_ack[1], sizeof(remote_comm_cmd_head_t), NULL, NULL, NULL, parse_control_cmd, NULL, NULL},
    {GET_STATUS, CMD_PASSIVE,  &cmd_req[2], &cmd_ack[2], sizeof(remote_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[1], NULL},
    {GET_REAL_DATA, CMD_PASSIVE,  &cmd_req[3], &cmd_ack[3], sizeof(remote_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[2], NULL},
    {GET_ALARM, CMD_PASSIVE,  &cmd_req[4], &cmd_ack[4], sizeof(remote_comm_cmd_head_t), NULL, NULL, pack_alarm_cmd, NULL, NULL, NULL},
    {GET_PARA, CMD_PASSIVE,  &cmd_req[5], &cmd_ack[5], sizeof(remote_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[3], NULL},
    {SET_PARA, CMD_PASSIVE,  &cmd_req[6], &cmd_ack[6], sizeof(remote_comm_cmd_head_t), NULL, NULL, NULL, parse_para_cmd, NULL, NULL},
    {GET_HIS_DATA, CMD_PASSIVE,  &cmd_req[7], &cmd_ack[7], sizeof(remote_comm_cmd_head_t), NULL, NULL, pack_his_data, parse_his_rec, NULL, NULL},
    {GET_HIS_ALM, CMD_PASSIVE,  &cmd_req[8], &cmd_ack[8], sizeof(remote_comm_cmd_head_t), NULL, NULL, pack_his_alm, parse_his_rec, NULL, NULL},
    {GET_OPE_REC, CMD_PASSIVE,  &cmd_req[9], &cmd_ack[9], sizeof(remote_comm_cmd_head_t), NULL, NULL, pack_his_ope, parse_his_rec, NULL, NULL},
    {GET_CELL_STA_REC, CMD_PASSIVE,  &cmd_req[10], &cmd_ack[10], sizeof(remote_comm_cmd_head_t), NULL, NULL, pack_cell_sta_rec, NULL, NULL, NULL},
    {GET_CHG_REC, CMD_PASSIVE,  &cmd_req[11], &cmd_ack[11], sizeof(remote_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[4], NULL},
    {0},
};

int register_csu_tab()
{
    return register_cmd_tab(no_poll_cmd_tab, sizeof(no_poll_cmd_tab)/ sizeof(cmd_t));
}




/* 自定义函数 */
void bms_reset()
{
    return;
}

void restore_para()
{
    return;
}

void power_lock()
{
    return;
}

ctrl_cmd_t ctrl_cmd[] = 
{
    {0x01, 0, NULL, 0, bms_reset},
    {0x02, 0, NULL, 0, restore_para},
    {0x03, 0, NULL, 0, power_lock},
};
#define CTRL_NUM (sizeof(ctrl_cmd) / sizeof(ctrl_cmd_t))


int parse_control_cmd(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    cmd_buf_t *cmd_buf_tmp = ((cmd_buf_t *)cmd_buff);
    unsigned char cmd_type = get_remote_comm_cmd_type();
    int i = 0;
    for(i = 0; i < CTRL_NUM; i++)
    {
        if(cmd_type == ctrl_cmd[i].cmd_type)
        {
            ctrl_cmd[i].func();
            return SUCCESSFUL;
        }
    }
    cmd_buf_tmp->rtn = REMOTE_COMM_RTN_DATA_ERR;
    return SUCCESSFUL;
}



int pack_alarm_cmd(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    cmd_buf_t *cmd_buf_tmp = ((cmd_buf_t *)cmd_buff);
    unsigned char* buff = ((cmd_buf_t*)cmd_buf_tmp)->buf;
    unsigned int offset = 0;
    int i = 0, index = 0;

    buff[offset] = 1 + 6*CELL_NUM + 5 + 1 + 9*CELL_NUM + 39;
    offset++;

    buff[offset] = CELL_NUM;
    offset++;

    for(index = 0; index < sizeof(cell_vol_alarm_id_tab)/ sizeof(cell_vol_alarm_id_tab[0]); index++)
    {
        for(i = 0; i < CELL_NUM; i++)
        {
            buff[offset] = get_realtime_alarm_value(cell_vol_alarm_id_tab[index] + i);
            offset++;
        }
    }

    for(index = 0; index < sizeof(alarm_id_tab_1)/ sizeof(alarm_id_tab_1[0]); index++)
    {
        buff[offset] = get_realtime_alarm_value(alarm_id_tab_1[index]);
        offset++;
    }

    buff[offset] = CELL_NUM;
    offset++;

    for(index = 0; index < sizeof(cell_temp_alarm_id_tab)/ sizeof(cell_temp_alarm_id_tab[0]); index++)
    {
        for(i = 0; i < CELL_NUM; i++)
        {
            buff[offset] = get_realtime_alarm_value(cell_temp_alarm_id_tab[index] + i);
            offset++;
        }
    }

    for(index = 0; index < sizeof(alarm_id_tab_2)/ sizeof(alarm_id_tab_2[0]); index++)
    {
        buff[offset] = get_realtime_alarm_value(alarm_id_tab_2[index]);
        offset++;
    }
    cmd_buf_tmp->data_len = offset;
    return SUCCESSFUL;
}


Static int handle_data_by_type(unsigned char data_type, unsigned char precision, unsigned int sid, cmd_buf_t *cmd_buf)
{
    unsigned char* buf_data = (cmd_buf->buf + 1);
    unsigned char  string[ARRAY_SIZE_32] = {0};
    u_value data = {0};
    int ret = 0;
    switch (data_type)
    {
        case type_unsigned_char:
            data.uc_val = *buf_data;
            ret = set_one_para(sid, &data.uc_val, TRUE, FALSE);
            break;
        case type_short:
            data.ss_val = get_int16_data(buf_data);
            if(precision == DOP_0)
            {
                ret = set_one_para(sid, &data.ss_val, TRUE, FALSE);
            }
            else
            {
                data.f_val = data.ss_val / pow(10,precision);
                ret = set_one_para(sid, &data.f_val, TRUE, FALSE);
            }
            break;
        case type_unsigned_short:
            data.us_val = get_uint16_data(buf_data);
            if(precision == DOP_0)
            {
                ret = set_one_para(sid, &data.us_val, TRUE, FALSE);
            }
            else
            {
                data.f_val = data.us_val / pow(10,precision);
                ret = set_one_para(sid, &data.f_val, TRUE, FALSE);
            }
            break;
        case type_unsigned_int:
            data.ui_val = get_uint32_data(buf_data);
            ret = set_one_para(sid, &data.ui_val, TRUE, FALSE);
            break;
        case type_string:
            rt_memcpy_s(string, ARRAY_SIZE_32, buf_data, ARRAY_SIZE_32);
            ret = set_one_para(sid, string, TRUE, FALSE);
            break;
        default:
            break;
    }
    if(ret <= 0)
    {
        cmd_buf->rtn = REMOTE_COMM_RTN_DATA_ERR;
    }
    return SUCCESSFUL;
}


int parse_para_cmd(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    cmd_buf_t *cmd_buf_tmp = ((cmd_buf_t *)cmd_buff);
    unsigned char cmd_type = get_remote_comm_cmd_type();
    unsigned char i = 0;
    for(; i < PARA_NUM; i++)
    {
        if(cmd_type == para_tab[i].cmd_type)
        {
            handle_data_by_type(para_tab[i].data_type, para_tab[i].precision, para_tab[i].para_id, cmd_buf_tmp);
            return SUCCESSFUL;
        }
    }
    cmd_buf_tmp->rtn = REMOTE_COMM_RTN_DATA_ERR;
    return SUCCESSFUL;
}


static int compare_time(time_base_t time, time_base_t target_time)
{
    time_t time1 = 0, time2 = 0;
    time1 = timestruct_to_time_t(&time);
    time2 = timestruct_to_time_t(&target_time);

    return (time1 > time2)? TIME_MORE_THAN_TARGET:TIME_NO_MORE_THAN_TARGET;
}



int parse_his_rec(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    cmd_buf_t *cmd_buf_tmp = ((cmd_buf_t *)cmd_buff);
    unsigned char* buff = cmd_buf_tmp->buf;
    unsigned char offset = 1;

    switch (get_remote_comm_cmd_type()) {
        case GET_RECORD_CORRECT:
        case GET_RECORD_WRONG:
        case GET_RECORD_ALL:
            return SUCCESSFUL;
        case GET_RECORD_TIME_SLOT:
        case GET_RECORD_NUM:
            if(cmd_buf_tmp->data_len != REC_TIME_LEN) {
                cmd_buf_tmp->rtn = REMOTE_COMM_RTN_DATA_ERR;
                return SUCCESSFUL;
            }
            get_time_from_buff(&s_time_start, &buff[offset]);
            offset += 7;
            get_time_from_buff(&s_time_end, &buff[offset]);
            if( check_time_valid(&s_time_start) == FALSE || check_time_valid(&s_time_end) == FALSE ||
                compare_time(s_time_start, s_time_end) == TIME_MORE_THAN_TARGET ) {
                cmd_buf_tmp->rtn = REMOTE_COMM_RTN_DATA_ERR;
                return SUCCESSFUL;
            }
            break;
        default:
            cmd_buf_tmp->rtn = REMOTE_COMM_RTN_DATA_ERR;
    }

    return SUCCESSFUL;
}



static void put_his_data_to_buff(unsigned char* buff, unsigned int* data_len)
{
    int offset = 0;
    short data = 0;
    unsigned short data2 = 0;

    buff[offset++] = HIS_DATA_LEN;
    buff[offset++] = FRAME_TYPE_NORMAL;
    buff[offset++] = 1;
    put_time_to_buff(&buff[offset], s_time_start);
    offset += 7;
    data2 = 3500;
    put_uint16_to_buff(&buff[offset], data2);
    offset += 2;
    buff[offset++] = 1;
    put_uint16_to_buff(&buff[offset], data2);
    offset += 2;
    buff[offset++] = 2;
    put_uint16_to_buff(&buff[offset], data2);
    offset += 2;
    buff[offset++] = 3;
    data2 = 500;
    put_uint16_to_buff(&buff[offset], data2);
    offset += 2;
    buff[offset++] = 4;
    data = 4000;
    put_int16_to_buff(&buff[offset], data);
    offset += 2;
    put_uint16_to_buff(&buff[offset], data2);
    offset += 2;
    data2 = 5700;
    put_uint16_to_buff(&buff[offset], data2);
    offset += 2;
    data2 = 88;
    put_uint16_to_buff(&buff[offset], data2);
    offset += 2;
    buff[offset++] = 68;
    buff[offset++] = 98;
    buff[offset++] = 99;
    data2 = 1;
    put_uint16_to_buff(&buff[offset], data2);
    offset += 2;
    buff[offset++] = 55;
    data2 = 0xFFFF;
    put_uint16_to_buff(&buff[offset], data2);
    offset += 2;
    buff[offset++] = 3;
    *data_len = offset;
    return;
}

static void put_his_alm_to_buff(unsigned char* buff, unsigned int* data_len)
{
    int offset = 0;
    unsigned short data = 1;

    buff[offset++] = 18;
    buff[offset++] = FRAME_TYPE_NORMAL;
    buff[offset++] = 1;
    put_uint16_to_buff(&buff[offset], data);
    offset += 2;
    put_time_to_buff(&buff[offset], s_time_start);
    offset += 7;
    put_time_to_buff(&buff[offset], s_time_end);
    offset += 7;
    *data_len = offset;
    return;
}

static void put_his_ope_to_buff(unsigned char* buff, unsigned int* data_len)
{
    int offset = 0;

    buff[offset++] = 32;
    buff[offset++] = FRAME_TYPE_NORMAL;
    buff[offset++] = 1;
    buff[offset++] = 0;
    buff[offset++] = 0;
    buff[offset++] = 1;
    put_time_to_buff(&buff[offset], s_time_start);
    offset += 7;
    rt_memset_s(&buff[offset], 20, 0, 20);
    offset += 20;
    *data_len = offset;
    return;
}

static void put_his_rec_num_to_buff(unsigned char* buff, unsigned int* data_len)
{
    buff[0] = 4;
    put_uint32_to_buff(&buff[1], 10);
    *data_len = 5;
    return;
}



static void handle_his_rec(unsigned char rec_type, unsigned char* buf, unsigned int* data_len)
{
    int i = 0;
    unsigned char cmd_type = get_remote_comm_cmd_type();

    for(; i < HIS_REC_NUM; i++) {
        if(rec_type == his_rec[i].rec_type && cmd_type == his_rec[i].cmd_type) {
            (*his_rec[i].pack_his_rec)(buf, data_len);
            break;
        }
    }
    return;
}



int pack_his_data(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    cmd_buf_t *cmd_buf_tmp = ((cmd_buf_t *)cmd_buff);
    RETURN_VAL_IF_FAIL(cmd_buf_tmp->rtn == 0, FAILURE);
    unsigned char* buff = cmd_buf_tmp->buf;

    handle_his_rec(HIS_DATA_REC, buff, &(cmd_buf_tmp->data_len));
    return SUCCESSFUL;
}

int pack_his_alm(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    cmd_buf_t *cmd_buf_tmp = ((cmd_buf_t *)cmd_buff);
    RETURN_VAL_IF_FAIL(cmd_buf_tmp->rtn == 0, FAILURE);
    unsigned char* buff = cmd_buf_tmp->buf;

    handle_his_rec(HIS_ALM_REC, buff, &(cmd_buf_tmp->data_len));
    return SUCCESSFUL;
}

int pack_his_ope(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    cmd_buf_t *cmd_buf_tmp = ((cmd_buf_t *)cmd_buff);
    RETURN_VAL_IF_FAIL(cmd_buf_tmp->rtn == 0, FAILURE);
    unsigned char* buff = cmd_buf_tmp->buf;

    handle_his_rec(HIS_OPE_REC, buff, &(cmd_buf_tmp->data_len));
    return SUCCESSFUL;
}



int pack_cell_sta_rec(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    cmd_buf_t *cmd_buf_tmp = ((cmd_buf_t *)cmd_buff);
    unsigned char* buff = cmd_buf_tmp->buf;
    unsigned int offset = 0;
    int i = 0, j = 0;

    buff[offset++] = CELL_STA_REC_LEN;

    //TODO:后续开发需改为真实数据
    for (i = 0; i < CELL_TEMP_RANGE_NUM; i++) {
        for (j = 0; j < SOC_RANGE_NUM; j++) {
            put_int32_to_buff(&buff[offset], i + j);
            offset += 4;
        }
    }
    put_int32_to_buff(&buff[offset], 10);
    offset += 4;
    put_int32_to_buff(&buff[offset], 20);
    offset += 4;
    put_int32_to_buff(&buff[offset], 30);
    offset += 4;
    put_int32_to_buff(&buff[offset], 40);
    offset += 4;
    put_int16_to_buff(&buff[offset], 50);
    offset += 2;
    put_int16_to_buff(&buff[offset], 60);
    offset += 2;
    put_int16_to_buff(&buff[offset], 70);
    offset += 2;
    put_int16_to_buff(&buff[offset], 80);
    offset += 2;

    cmd_buf_tmp->data_len = offset;
    return SUCCESSFUL;
}

