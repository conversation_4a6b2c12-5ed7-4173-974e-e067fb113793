/**
 * @brief ACMU板南北向通信头文件
 */

#ifndef _ACMU_1104_H_
#define _ACMU_1104_H_

#ifdef __cplusplus
extern "C" {
#endif

#include <string.h>
#include <stdlib.h>
#include <rtthread.h>
#include "sps.h"
#include "msg.h"
#include "linklayer.h"
#include "protocol_layer.h"
#include "server_id.h"
#include "storage.h"
#include "utils_server.h"
#include "utils_data_transmission.h"
#include "utils_thread.h"
#include "utils_string.h"
#include "utils_time.h"
#include "utils_rtthread_security_func.h"
#include "utils_data_type_conversion.h"
#include "device_type.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "utils_heart_beat.h"
#include "data_type.h"
#include "dev_acmu.h"

#define TOTAL_ENERGY_MAX   (166666666)


typedef struct {
    void*        data;
    cmd_buf_t*   cmd_buff;
    link_inst_t* link_inst;
    rt_mq_t      north_mq;
}acmu_mgr_t;

void* init_north_comm(void * param);
void* init_south_comm(void * param);
void* init_can1_comm(void * param);
void north_comm_thread(void *param);
void south_comm_thread(void *param);
void can1_comm_thread(void *param);
unsigned char set_apptest_usart_test_flag(unsigned char flag_set);
unsigned char set_apptest_can_test_flag(unsigned char flag_set);
int init_isNorthOrSouth(server_info_t *server_group,int server_num);
#ifdef __cplusplus
}
#endif      //< __cplusplus

#endif      //< _ACMU_1104_H_
