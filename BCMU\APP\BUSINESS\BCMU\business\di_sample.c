#include <rtdevice.h>
#include "di_sample.h"
#include "pin_ctrl.h"
/**
 * @FunctionName: get_di_state
 * @brief:
 * @details:
 * @Author:       dongmengling
 * @DateTime:     2023年2月10日T14:34:54+0800
 * @Purpose:      
 * @param:        di_data             
 * @return: 
 */
int get_di_state(di_data_t* di_data) {
    if ( di_data == NULL) {
        return FAILURE;
    }
    di_data->state = get_pin(di_data->di_pin_no);
    return SUCCESSFUL;
}

static void input_relay_config(di_manage_t* di_manage) {

    /* 设置消防干节点引脚为输入模式 */
    rt_pin_mode(di_manage->di_pin_no, PIN_MODE_INPUT_PULLUP);

    /* 绑定中断，下降沿模式，回调函数名为send_fire_alarm_msg */
    rt_pin_attach_irq(di_manage->di_pin_no, di_manage->irq_mode , di_manage->di_irq_callback , RT_NULL);

    /* 使能中断 */
    rt_pin_irq_enable(di_manage->di_pin_no, PIN_IRQ_ENABLE);
}



void di_sample_init(di_manage_t* di_manage, unsigned char di_num) {
    unsigned char i;
    
    if (di_manage == NULL || di_num == 0)
        return;
    
    for (i = 0; i < di_num; i++) {
        if (di_manage[i].di_irq_callback != NULL)
            input_relay_config(&di_manage[i]);  
    }
}
