/**
 *  note: BMU告警模块 alarm_register.h
 *  author: fuzhen
 *  create_time: 2023/03/07
 * 
 *  Copyright (C) 2001, ZTE Corporation.
 */

#ifndef _BMU_APP_SRC_BMU_ALARM_H_
#define _BMU_APP_SRC_BMU_ALARM_H_

#ifdef __cplusplus
extern "C"{
#endif

#include "app_config.h"
#include "msg.h"


typedef struct {
    /* 硬件类告警*/
    int hardware_fault_alarm;                        ///< BMU硬件故障告警
    int volt_sample_line_off_alarm;                  ///< 电压采集线束故障告警
    int temp_sample_line_off_alarm;                  ///< 温度采集线束故障告警
}bmu_hardware_alarm_t;

/* 紧急状态数据,用于记录 */
#pragma pack(1)
typedef struct {
    unsigned char emg_fire_alarm : 1;   ///< 1bit，消防告警状态，1有告警，0无
    unsigned short crc;
}emergency_data_t;
#pragma pack()

int check_bmu_hardware_alarm(void);
bmu_hardware_alarm_t *get_hardware_alarm(void);
int register_bmu_alarm(void);

#ifdef UNITEST
int get_bmu_emergency_data(void);
#endif

#ifdef __cplusplus
}   //  end of the 'extern "C"' block
#endif

#endif  // _BMU_APP_SRC_BMU_ALARM_H_
