/**
 * @brief ACMU板北向通信头文件
 */

#ifndef _DCMU_1104_H_
#define _DCMU_1104_H_

#ifdef __cplusplus
extern "C" {
#endif

#include <string.h>
#include <stdlib.h>
#include <rtthread.h>
#include "sps.h"
#include "msg.h"
#include "linklayer.h"
#include "protocol_layer.h"
#include "server_id.h"
#include "storage.h"
#include "utils_server.h"
#include "utils_data_transmission.h"
#include "utils_thread.h"
#include "utils_string.h"
#include "utils_time.h"
#include "utils_rtthread_security_func.h"
#include "utils_data_type_conversion.h"
#include "device_type.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "data_type.h"
#include "dev_dcmu.h"

typedef struct {
    void*        data;
    cmd_buf_t*   cmd_buff;
    link_inst_t* link_inst;
    rt_mq_t      north_mq;
}dcmu_mgr_t;

void* init_usart1_comm(void * param);
void usart1_comm_thread(void *param);
void* init_usart2_comm(void * param);
void usart2_comm_thread(void *param);
void* init_can0_comm(void * param);
void can0_comm_thread(void *param);
unsigned char set_apptest_usart_test_flag(unsigned char flag_set);
unsigned char set_apptest_can_test_flag(unsigned char flag_set);

#ifdef __cplusplus
}
#endif      //< __cplusplus

#endif      //< _DCMU_1104_H_
