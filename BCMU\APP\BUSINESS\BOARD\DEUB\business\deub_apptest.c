#include "deub_apptest.h"
#include "cmd.h"
#include "protocol_layer.h"
#include "sps.h"
#include "ee_public_info.h"
#include "software_version.h"
#include "storage.h"
#include "bspconfig.h"
#include "sample.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "utils_rtthread_security_func.h"
#include "utils_data_transmission.h"
#include "utils_flag.h"
#include "utils_time.h"
#include "rthw.h"


static s1363_cmd_head_t cmd_req[] = {
    {VER_22 , CMD_NONE , CMD_NONE},    ///< 0
    {VER_22 , CID1_RM ,     CID2_TRIG_APPTEST},                ///<1 触发apptest命令
    {VER_22 , CID1_RM ,     CID2_SET_HARDWARE_VER},            ///<2 设置硬件版本号
    {VER_22 , CID1_RM ,     CID2_GET_HARDWARE_VER},            ///<3 获取硬件版本号
    {VER_22 , CID1_RM ,     CID2_GET_FACT_INFO},               ///<4 获取厂家信息
    {VER_22 , CID1_RM ,     CID2_GET_AI},                      ///<5 获取AI信号
    {VER_22 , CID1_RM ,     CID2_GET_INSULATION_RESISTANCE},   ///<6 获取绝缘阻抗
    {VER_22 , CID1_RM ,     CID2_GET_DI_INFO},                 ///<7 获取DI信息
    {VER_22 , CID1_RM ,     CID2_EXIT_APPTEST},                ///<8 退出apptest命令
    {VER_22 , CID1_COMMON , CID2_GET_PROTO_VER},               ///<9 获取协议版本号命令
    {VER_22 , CID1_RM ,     CID2_CHIP_TEST_INFO},              ///<10 获取芯片测试信息
    {VER_22 , CID1_RM ,     CID2_SET_BOARD_MANU_DATE},         ///<11 设置单板生产日期
    {VER_22 , CID1_RM ,     CID2_GET_BOARD_MANU_DATE},         ///<12 获取单板生产日期
    {VER_22 , CID1_COMMON , CID2_GET_DEVICE_ADDR},             ///<13 获取通讯地址
    {VER_22 , CID1_RM ,     CID2_SET_CALIBRATE_PARA},          ///<14 设置校准参数
    {VER_22 , CID1_RM ,     CID2_GET_CALIBRATE_PARA},          ///<15 获取校准参数
    {VER_22 , CID1_RM ,     CID2_SET_SN},                      ///<16 设置序列号
    {VER_22 , CID1_RM ,     CID2_GET_SN},                      ///<17 获取序列号
};

/* 命令应答头 */
static s1363_cmd_head_t cmd_ack[] = {
    {VER_22 , CMD_NONE , CMD_NONE},    ///< 0
    {VER_22 , CID1_RM ,     CID2_TRIG_APPTEST},               ///<1 触发apptest命令
    {VER_22 , CID1_RM ,     CID2_SET_HARDWARE_VER},           ///<2 设置硬件版本号
    {VER_22 , CID1_RM ,     CID2_GET_HARDWARE_VER},           ///<3 获取硬件版本号
    {VER_22 , CID1_RM ,     CID2_GET_FACT_INFO},              ///<4 获取厂家信息
    {VER_22 , CID1_RM ,     CID2_GET_AI},                     ///<5 获取AI信号
    {VER_22 , CID1_RM ,     CID2_GET_INSULATION_RESISTANCE},  ///<6 获取绝缘阻抗
    {VER_22 , CID1_RM ,     CID2_GET_DI_INFO},                ///<7 获取DI信息
    {VER_22 , CID1_RM ,     CID2_EXIT_APPTEST},               ///<8 退出apptest命令
    {VER_22 , CID1_COMMON , CID2_GET_PROTO_VER},              ///<9 获取协议版本号命令
    {VER_22 , CID1_RM ,     CID2_CHIP_TEST_INFO},             ///<10 获取芯片测试信息
    {VER_22 , CID1_RM ,     CID2_SET_BOARD_MANU_DATE},        ///<11 设置单板生产日期
    {VER_22 , CID1_RM ,     CID2_GET_BOARD_MANU_DATE},        ///<12 获取单板生产日期
    {VER_22 , CID1_COMMON , CID2_GET_DEVICE_ADDR},             ///<13 获取通讯地址
    {VER_22 , CID1_RM ,     CID2_SET_CALIBRATE_PARA},          ///<14 设置校准参数
    {VER_22 , CID1_RM ,     CID2_GET_CALIBRATE_PARA},          ///<15 获取校准参数
    {VER_22 , CID1_RM ,     CID2_SET_SN},                      ///<16 设置序列号
    {VER_22 , CID1_RM ,     CID2_GET_SN},                      ///<17 获取序列号
};


unsigned char s_get_which_channal = 0;


int apptest_comm_parse(void* dev_inst, void* cmd_buff) 
{
    RETURN_VAL_IF_FAIL(get_apptest_flag() == TRUE, NO_NEED_REPONSE);
    return SUCCESSFUL;
}



int apptest_trig_pack(void* dev_inst, void* cmd_buff) 
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    static char apptest_trig_count = 0;
    int offset = 0;
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;
    apptest_trig_count++;
    if(apptest_trig_count < 3)
    {
        data_buff[offset] = 0x00;
    }
    else
    {
        LOG_E("enter AppTest mode!");
        data_buff[offset] = 0x01;
        set_apptest_flag(TRUE);
    }
    offset++;
    ((cmd_buf_t*)cmd_buff)->data_len=offset;
    return SUCCESSFUL;
}



int set_hardware_version_parse(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(get_apptest_flag() == TRUE, NO_NEED_REPONSE);
    int offset = 0;
    unsigned char hardware_version[HARDWARE_VER_LEN] = {0};
    unsigned char origin_hardware_version[HARDWARE_VER_LEN] = {0};
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;

    rt_strncpy_s((char *)hardware_version, HARDWARE_VER_LEN, (const char*)&data_buff[offset], HARDWARE_VER_LEN - 1);
    offset += HARDWARE_VER_LEN;

    handle_storage(read_opr, EE_PUBLIC_INFO, origin_hardware_version, HARDWARE_VER_LEN, HARDWARE_VERSION_OFFSET);
    if(rt_memcmp(&origin_hardware_version[0], &hardware_version[0], HARDWARE_VER_LEN) == 0)
    {
        return SUCCESSFUL;
    }

    if(handle_storage(write_opr, EE_PUBLIC_INFO, hardware_version, HARDWARE_VER_LEN, HARDWARE_VERSION_OFFSET) != SUCCESSFUL)
    {
        LOG_E("set hardware version, write storage error.");
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA;
        return FAILURE;
    }
    set_one_data(DEUB_DATA_ID_HARDWARE_VERSION, hardware_version);
    return SUCCESSFUL;
}



int get_hardware_version_pack(void* dev_inst, void* cmd_buff) 
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    int offset = 0;
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;
    unsigned char hardware_version[HARDWARE_VER_LEN] = {0};
    if(handle_storage(read_opr, EE_PUBLIC_INFO, hardware_version, HARDWARE_VER_LEN, HARDWARE_VERSION_OFFSET) != SUCCESSFUL)
    {
        LOG_E("get hardware version, read storage error.");
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA;
        return FAILURE;
    }
    rt_memset_s(data_buff, HARDWARE_VER_LEN, 0, HARDWARE_VER_LEN);
    rt_strncpy_s((char *)&data_buff[offset], HARDWARE_VER_LEN, (const char*)hardware_version, HARDWARE_VER_LEN - 1);
    offset += HARDWARE_VER_LEN;

    ((cmd_buf_t*)cmd_buff)->data_len=offset;
    return SUCCESSFUL;
}



int get_fact_info_pack(void* dev_inst, void* cmd_buff) 
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    int offset = 0;
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;

    rt_memset_s(data_buff, SOFTWARE_INFO_LEN, 0, SOFTWARE_INFO_LEN);
    rt_memcpy_s(&data_buff[offset], SOFTWARE_NAME_LEN, DEUB_SOFTWARE_NAME, sizeof(DEUB_SOFTWARE_NAME));
    offset += SOFTWARE_NAME_LEN;
    rt_memcpy_s(&data_buff[offset], SOFTWARE_VER_LEN, DEUB_COMPILE_VERSION, sizeof(DEUB_COMPILE_VERSION));
    offset += SOFTWARE_VER_LEN;
    rt_memcpy_s(&data_buff[offset], SOFTWARE_DATE_LEN, DEUB_COMPILE_VERSION_DATE, SOFTWARE_DATE_LEN);
    offset += SOFTWARE_DATE_LEN;

    ((cmd_buf_t*)cmd_buff)->data_len = offset;
    return SUCCESSFUL;
}



int get_res_info_pack(void* dev_inst, void* cmd_buff) 
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    int offset = 0, tmp_data = 0;
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;

    get_one_data(DEUB_DATA_ID_INSULATION_RES, &tmp_data);
    put_int32_to_buff(data_buff, tmp_data);
    offset += 4;
    get_one_data(DEUB_DATA_ID_INSULATION_RES + 1, &tmp_data);
    put_int32_to_buff(&data_buff[offset], tmp_data);
    offset += 4;
    ((cmd_buf_t*)cmd_buff)->data_len = offset;
    return SUCCESSFUL;
}



int get_input_rly_sta_parse(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(get_apptest_flag() == TRUE, NO_NEED_REPONSE);
    int offset = 0;
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;
    s_get_which_channal = data_buff[offset++];  // 对应的干接点通道
    RETURN_VAL_IF_FAIL(s_get_which_channal <= 6 || s_get_which_channal == 0xFF, FAILURE);
    return SUCCESSFUL;
}

int get_input_rly_sta_pack(void* dev_inst, void* cmd_buff) 
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    int offset = 0;
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;
    if(s_get_which_channal == 0xFF)
    {
        // 获取所有干接点的状态
        for(int i = 0; i < 6; i++)
        {
            get_one_data(DEUB_DATA_ID_INPUT_DRY_CONTACT + i, &data_buff[offset]);
            offset++;
        }
    }
    else
    {
        // 获取指定通道干接点状态
        get_one_data(DEUB_DATA_ID_INPUT_DRY_CONTACT + s_get_which_channal - 1, &data_buff[offset]);
        offset++;
    }
    ((cmd_buf_t*)cmd_buff)->data_len = offset;
    return SUCCESSFUL;
}



int apptest_exit_parse(void* dev_inst, void* cmd_buff) 
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(get_apptest_flag() == TRUE, NO_NEED_REPONSE);
    cmd_buf_t send_cmd_buff;
    rt_memset_s(&send_cmd_buff, sizeof(cmd_buf_t), 0, sizeof(cmd_buf_t));
    send_cmd_buff.cmd = ((cmd_buf_t*)cmd_buff)->cmd;
    cmd_send(dev_inst, &send_cmd_buff);
    rt_thread_mdelay(1000);
    rt_hw_cpu_reset();
    return SUCCESSFUL;
}



int get_chip_test_info_pack(void* dev_inst, void* cmd_buff) 
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    int offset = 0;
    unsigned char status = 1;
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;
    unsigned short erase_data = 0xFFFF;
    unsigned short write_data = 0x1010;
    handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&erase_data, sizeof(erase_data), CHIP_TEST_OFFSET);
    erase_data = 0;
    handle_storage(read_opr,  EE_PUBLIC_INFO, (unsigned char*)&erase_data, sizeof(erase_data), CHIP_TEST_OFFSET);
    handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&write_data, sizeof(write_data), CHIP_TEST_OFFSET);
    write_data = 0;
    handle_storage(read_opr,  EE_PUBLIC_INFO, (unsigned char*)&write_data, sizeof(write_data), CHIP_TEST_OFFSET);
    if(erase_data == 0xFFFF && write_data == 0x1010)
    {
        status = 0;
    }
    data_buff[offset] = 0x01;
    offset++;
    data_buff[offset] = status;
    offset++;
    ((cmd_buf_t*)cmd_buff)->data_len=offset;
    return SUCCESSFUL;
}



int set_board_manu_date_parse(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(get_apptest_flag() == TRUE, NO_NEED_REPONSE);
    int offset = 0;
    date_base_t tm = {0};
    date_base_t origin_tm = {0};
    unsigned char board_manu_date[BOARD_MANU_DATE_LEN] = {0};

    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;

    get_date_from_buff(&tm, &data_buff[offset]);
    offset += 4;

    if(check_date_valid(&tm) != TRUE)
    {
        LOG_E("%s | %d | set manu date failed ", __FUNCTION__ , __LINE__);
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA ;
        return FAILURE;
    }


    handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char*)&origin_tm, sizeof(origin_tm), MANU_DATE_OFFSET);
    if(tm.year == origin_tm.year && tm.month == origin_tm.month && tm.day == origin_tm.day)
    {
        return SUCCESSFUL;
    }


    if(handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&tm, sizeof(tm), MANU_DATE_OFFSET) != SUCCESSFUL)
    {
        LOG_E("set manu date, write storage error.");
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA ;
        return FAILURE;
    }
    date_to_string((char *)board_manu_date, (date_base_t*)&tm);
    set_one_data(DEUB_DATA_ID_BOARD_MANU_DATE, board_manu_date);
    return SUCCESSFUL;
}



int get_board_manu_date_pack(void* dev_inst, void* cmd_buff) 
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    int offset = 0;
    date_base_t tm = {0};
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;
    if(handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char*)&tm, sizeof(date_base_t), MANU_DATE_OFFSET) != SUCCESSFUL)
    {
        LOG_E("get manu date, read storage error.");
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA ;
        return FAILURE;
    }

    put_date_to_buff(&data_buff[0], tm);
    offset += 4;

    ((cmd_buf_t*)cmd_buff)->data_len=offset;
    return SUCCESSFUL;
}



int set_calibrate_para_parse(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(get_apptest_flag() == TRUE, NO_NEED_REPONSE);
    int offset = 0;
    float ac_vol_cali_slope = 0.0;
    float batt_curr_zero_point[BATT_CURR_NUM] = {0.0};
    float batt_curr_reset_threshold[BATT_CURR_NUM] = {0.0};
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;

    ac_vol_cali_slope = get_int16_data(&data_buff[offset]) * 0.001;
    offset+=2;
    set_one_data(DEUB_DATA_ID_AC_VOL_CALIBRATE_SLOPE, &ac_vol_cali_slope);
    if(handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&ac_vol_cali_slope, sizeof(float), AC_VOL_CALI_OFFSET) != SUCCESSFUL)
    {
        LOG_E("set ac vol cali slope, write storage error.");
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA;
        return FAILURE;
    }
    for(int i = 0; i < BATT_CURR_NUM; i++)
    {
        batt_curr_zero_point[i] = get_int16_data(&data_buff[offset]) * 0.001;
        set_one_data(DEUB_DATA_ID_BATTERY_CURR_ZERO_POINT + i, &batt_curr_zero_point[i]);
        offset+=2;
        batt_curr_reset_threshold[i] = get_int16_data(&data_buff[offset]) * 0.001;
        set_one_data(DEUB_DATA_ID_BATTERY_CURR_RESET_THRESHOLD + i, &batt_curr_reset_threshold[i]);
        offset+=2;
    }
    if(handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&batt_curr_zero_point, sizeof(float) * BATT_CURR_NUM, BATT_CURR_ZERO_OFFSET) != SUCCESSFUL)
    {
        LOG_E("set batt curr zero point, write storage error.");
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA;
        return FAILURE;
    }
    if(handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&batt_curr_reset_threshold, sizeof(float) * BATT_CURR_NUM, BATT_CURR_RESET_OFFSET) != SUCCESSFUL)
    {
        LOG_E("set batt curr reset threshold, write storage error.");
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA;
        return FAILURE;
    }

    return SUCCESSFUL;
}



int get_calibrate_para_pack(void* dev_inst, void* cmd_buff) 
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    int offset = 0;
    float ac_vol_cali_slope = 0;
    float batt_curr_zero_point[BATT_CURR_NUM] = {0};
    float batt_curr_reset_threshold[BATT_CURR_NUM] = {0};
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;

    get_one_data(DEUB_DATA_ID_AC_VOL_CALIBRATE_SLOPE, &ac_vol_cali_slope);
    put_int16_to_buff(&data_buff[offset], ac_vol_cali_slope * 1000);
    offset += 2;
    for(int i = 0; i < BATT_CURR_NUM; i++)
    {
        get_one_data(DEUB_DATA_ID_BATTERY_CURR_ZERO_POINT + i, &batt_curr_zero_point[i]);
        put_int16_to_buff(&data_buff[offset], batt_curr_zero_point[i] * 1000);
        offset += 2;
        get_one_data(DEUB_DATA_ID_BATTERY_CURR_RESET_THRESHOLD + i, &batt_curr_reset_threshold[i]);
        put_int16_to_buff(&data_buff[offset], batt_curr_reset_threshold[i] * 1000);
        offset += 2;
    }

    ((cmd_buf_t*)cmd_buff)->data_len=offset;
    return SUCCESSFUL;
}



int set_sn_parse(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(get_apptest_flag() == TRUE, NO_NEED_REPONSE);
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    int offset = 0;
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;
    unsigned char sn[SN_LEN] = {0};
    unsigned char origin_sn[SN_LEN] = {0};
    rt_memcpy_s(sn, SN_LEN, &data_buff[offset], SN_LEN);

    handle_storage(read_opr, EE_PUBLIC_INFO, origin_sn, SN_LEN, SN_OFFSET);
    if(rt_memcmp(&origin_sn[0], &sn[0], SN_LEN) == 0)
    {
        return SUCCESSFUL;
    }

    if(handle_storage(write_opr, EE_PUBLIC_INFO, sn, SN_LEN, SN_OFFSET) != SUCCESSFUL)
    {
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA;
        rt_kprintf("set_sn_parse write_opr error!!!\n");
        return FAILURE;
    }
    set_one_data(DEUB_DATA_ID_SERIAL_NUMBER, sn);
    return SUCCESSFUL;
}



int get_sn_pack(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;
    unsigned char sn[SN_LEN] = {0};

    if(handle_storage(read_opr, EE_PUBLIC_INFO, sn, SN_LEN, SN_OFFSET) != SUCCESSFUL)
    {
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA;
        rt_kprintf("get_sn_parse read_opr error!!!\n");
        return FAILURE;
    }
    rt_memset_s(data_buff, SN_LEN, 0, SN_LEN);
    rt_memcpy_s(data_buff, SN_LEN, sn, SN_LEN);

    ((cmd_buf_t*)cmd_buff)->data_len = SN_LEN;
    return SUCCESSFUL;
}


/* 命令总表,必须以空字符串""结束 */

static cmd_t no_poll_cmd_tab[] = 
{
    {DEUB_APPTEST_TRIG,     CMD_PASSIVE, &cmd_req[1], &cmd_ack[1], sizeof(s1363_cmd_head_t), NULL, NULL, apptest_trig_pack, SELF_PARSE},
    {DEUB_SET_HARDWARE_VER, CMD_PASSIVE, &cmd_req[2], &cmd_ack[2], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, set_hardware_version_parse},
    {DEUB_GET_HARDWARE_VER, CMD_PASSIVE, &cmd_req[3], &cmd_ack[2], sizeof(s1363_cmd_head_t), NULL, NULL, get_hardware_version_pack, apptest_comm_parse},
    {DEUB_GET_FACT_INFO,    CMD_PASSIVE, &cmd_req[4], &cmd_ack[4], sizeof(s1363_cmd_head_t), NULL, NULL, get_fact_info_pack, apptest_comm_parse},
    {DEUB_GET_AI,           CMD_PASSIVE, &cmd_req[5], &cmd_ack[5], sizeof(s1363_cmd_head_t), NULL, NULL, apptest_get_ai_pack, apptest_comm_parse},
    {DEUB_GET_RESISTANCE,   CMD_PASSIVE, &cmd_req[6], &cmd_ack[6], sizeof(s1363_cmd_head_t), NULL, NULL, get_res_info_pack, apptest_comm_parse},
    {DEUB_GET_DI_INFO,      CMD_PASSIVE, &cmd_req[7], &cmd_ack[7], sizeof(s1363_cmd_head_t), NULL, NULL, get_input_rly_sta_pack, get_input_rly_sta_parse},
    {DEUB_APPTEST_EXIT,     CMD_PASSIVE, &cmd_req[8], &cmd_ack[8], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, apptest_exit_parse},
    {DEUB_GET_PROTO_VER,    CMD_PASSIVE, &cmd_req[9], &cmd_ack[9], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {DEUB_CHIP_TEST_INFO,   CMD_PASSIVE, &cmd_req[10], &cmd_ack[10], sizeof(s1363_cmd_head_t), NULL, NULL, get_chip_test_info_pack, apptest_comm_parse},
    {DEUB_SET_BOARD_MANU_DATE, CMD_PASSIVE, &cmd_req[11], &cmd_ack[11], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, set_board_manu_date_parse},
    {DEUB_GET_BOARD_MANU_DATE, CMD_PASSIVE, &cmd_req[12], &cmd_ack[12], sizeof(s1363_cmd_head_t), NULL, NULL, get_board_manu_date_pack, apptest_comm_parse},
    {DEUB_GET_COMM_ADDR,    CMD_PASSIVE, &cmd_req[13], &cmd_ack[13], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, apptest_comm_parse},
    {DEUB_SET_CALIBRATE_PARA, CMD_PASSIVE, &cmd_req[14], &cmd_ack[14], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, set_calibrate_para_parse},
    {DEUB_GET_CALIBRATE_PARA, CMD_PASSIVE, &cmd_req[15], &cmd_ack[15], sizeof(s1363_cmd_head_t), NULL, NULL, get_calibrate_para_pack, apptest_comm_parse},
    {DEUB_SET_SN, CMD_PASSIVE, &cmd_req[16], &cmd_ack[16], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, set_sn_parse},
    {DEUB_GET_SN, CMD_PASSIVE, &cmd_req[17], &cmd_ack[17], sizeof(s1363_cmd_head_t), NULL, NULL, get_sn_pack, apptest_comm_parse},

    {0},
}; 



static dev_type_t s_deub_apptest_dev = {
    DEV_NORTH_DEUB_APPTEST, 1, PROTOCOL_YD_1363, LINK_NORTH_DEUB, R_BUFF_LEN_512, S_BUFF_LEN_512, 0, no_poll_cmd_tab, NULL
};

dev_type_t* init_deub_dev_apptest(void)
{
    return &s_deub_apptest_dev;
}



int apptest_get_ai_pack(void* dev_inst, void* cmd_buff) 
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    int offset = 0;
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;
    float f_val = 0.0;
    short s_val = 0;
    int i = 0;
    int sids[] = 
    {
        DEUB_DATA_ID_PHASE_VOL,
        DEUB_DATA_ID_PHASE_VOL + 1,
        DEUB_DATA_ID_PHASE_VOL + 2,
        DEUB_DATA_ID_BUS_VOL,
        DEUB_DATA_ID_BATTERY_CUR,
        DEUB_DATA_ID_BATTERY_CUR + 1,
        DEUB_DATA_ID_BATTERY_CUR + 2,
        DEUB_DATA_ID_BATTERY_CUR + 3,
    };

    for(i = 0; i < sizeof(sids)/ sizeof(sids[0]); i++)
    {
        get_one_data(sids[i], &f_val);
        s_val = f_val*10;   // 这里的数据精度都是10
        put_int16_to_buff(&data_buff[offset], s_val);
        offset += 2;

    }

    ((cmd_buf_t*)cmd_buff)->data_len=offset;
    return SUCCESSFUL;
}

