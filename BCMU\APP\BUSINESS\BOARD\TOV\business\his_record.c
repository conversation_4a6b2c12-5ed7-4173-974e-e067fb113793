#include <unistd.h>
#include "his_record.h"
#include "log_mgr_api.h"
#include "partition_def.h"
#include "utils_rtthread_security_func.h"
#include "msg_id.h"
#include "io_ctrl_api.h"
#include "para_id_in.h"
#include "storage.h"
#include "para_manage.h"
#include "utils_math.h"
#include "utils_data_transmission.h"
#include "realdata_save.h"
#include "realdata_id_in.h"
#include "app_config.h"

static record_data_info_t s_record_data[10]RAM_SECTION_BSS = {0};
//每个通道 ： 2 字节 ID + 9 字节时间 + 2 字节最大值 + 9 字节时间 + 2 字节最小值
static unsigned char s_extreme_data[EXTREME_DATA_LEN] = {0};

static his_record_new_t tov_record[] = {
    {  //录波数据
        .his_record_info = {0},
        .auth_code = 0x10,
        .storage_type = ACC,
        .size_type = VLD,
        .record_size = 0,    // record_size是动态的
        .info_name = RECORD_FAULT_INFO,
        .data = NULL,
        .part_name = RECORD_FAULT_DATA,
        .min_save_num = MIN_RECORD_FAULT_NUM,
        .max_save_num = MAX_RECORD_FAULT_NUM,
        .save_num_per_file = MAX_RECORD_FAULT_NUM_PER_FILE,
        .file_num = RECORD_FAULT_FILE_NUM,
        .file_name = {RECORD_FAULT_FILE_0, RECORD_FAULT_FILE_1, RECORD_FAULT_FILE_2, RECORD_FAULT_FILE_3},
    },
    
    {  //极值数据
        .his_record_info = {0},
        .auth_code = 0x10,
        .storage_type = NACC,
        .size_type = FLD,
        .record_size = EXTREME_DATA_LEN,    // record_size是固定的
        .data = NULL,
        .part_name = EXTREME_DATA_FILE_NAME,
        .min_save_num = 1,
        .max_save_num = 1,
        .save_num_per_file = 1,
        .file_num = 1,
        .file_name = {EXTREME_DATA_FILE},
    },
    
    {
        //LOG文件1
        .his_record_info = {0},
        .auth_code = 0x10,
        .storage_type = NACC,
        .size_type = VLD,
        .record_size = 0,    // record_size是固定的
        .data = NULL,
        .part_name = LOG_FILE1,
        .min_save_num = 1,
        .max_save_num = 1,
        .save_num_per_file = 1,
        .file_num = 1,
        .file_name = {LOG_FILE1_PART},
    },
    {
        //LOG文件2
        .his_record_info = {0},
        .auth_code = 0x10,
        .storage_type = NACC,
        .size_type = VLD,
        .record_size = 0,    // record_size是固定的
        .data = NULL,
        .part_name = LOG_FILE2,
        .min_save_num = 1,
        .max_save_num = 1,
        .save_num_per_file = 1,
        .file_num = 1,
        .file_name = {LOG_FILE2_PART},
    }
    
};


int init_his_record(void)
{
    if(FAILURE == register_his_record_tab_new(tov_record, sizeof(tov_record)/sizeof(tov_record[0])))
    {
        rt_kprintf("init_his_record fail\n");
        return FAILURE;
    }
    return SUCCESSFUL;
}



unsigned int save_after_data(unsigned int trig_index, unsigned char* sample_data, unsigned char* buff, unsigned int* offset)
{
    unsigned int bytes_per_sample = 2;
    unsigned int total_copy_len = 500 * bytes_per_sample; // 录波后的5ms数据，占1000字节
    unsigned int max_point_num = MAX_POINT_NUM_PER_CHANNEL;

    if (trig_index <= max_point_num - 500)
    {
        // 计算源数据的起始地址并进行内存拷贝
        rt_memcpy_s(&buff[*offset], total_copy_len, &sample_data[trig_index * bytes_per_sample], total_copy_len);
        *offset += total_copy_len;
    }
    else
    {
        unsigned int first_copy_len = (max_point_num - trig_index) * bytes_per_sample;
        unsigned int second_copy_len = total_copy_len - first_copy_len;

        // 第一段拷贝
        rt_memcpy_s(&buff[*offset], first_copy_len, &sample_data[trig_index * bytes_per_sample], first_copy_len);
        *offset += first_copy_len;

        // 第二段拷贝
        rt_memcpy_s(&buff[*offset], second_copy_len, sample_data, second_copy_len);
        *offset += second_copy_len;
    }

    return *offset;
}



unsigned int save_before_and_after_data(float before_record_time, float after_record_time, unsigned int trig_index, 
                             unsigned char* sample_data, unsigned char* buff, unsigned int* offset)
{
    const unsigned int max_sample_size = MAX_POINT_NUM_PER_CHANNEL;
    const unsigned int bytes_per_sample = 2;

    // 计算触发点前后的样本点数
    unsigned int before_samples = (unsigned int)(before_record_time * 100);
    unsigned int after_samples = (unsigned int)(after_record_time * 100);

    if (trig_index >= before_samples)  // 触发点在录播前数据之后
    {
        unsigned int start_before_index = trig_index - before_samples;
        unsigned char* start_before_data_addr = sample_data + start_before_index * bytes_per_sample;
        unsigned int start_before_copy_len = before_samples * bytes_per_sample;

        rt_memcpy_s(&buff[*offset], start_before_copy_len, start_before_data_addr, start_before_copy_len);
        // rt_kprintf("index > before|buff:%p, offset:%d, sample_data:%p, ac_buff:%p, ac_len:%d\n", 
        //             &buff[*offset], *offset, sample_data, start_before_data_addr, start_before_copy_len);
        *offset += start_before_copy_len;

        unsigned int end_after_index = trig_index + after_samples;
        if (end_after_index <= max_sample_size)
        {
            unsigned char* start_after_data_addr = sample_data + trig_index * bytes_per_sample;
            unsigned int start_after_copy_len = after_samples * bytes_per_sample;

            rt_memcpy_s(&buff[*offset], start_after_copy_len, start_after_data_addr, start_after_copy_len);
            // rt_kprintf("index > before,1|buff:%p, offset:%d, ac_buff:%p, ac_len:%d\n", &buff[*offset], *offset, start_after_data_addr, start_after_copy_len);
            *offset += start_after_copy_len;
        }
        else
        {
            unsigned int first_part_len = (max_sample_size - trig_index) * bytes_per_sample;
            unsigned char* start_after_data_addr = sample_data + trig_index * bytes_per_sample;

            rt_memcpy_s(&buff[*offset], first_part_len, start_after_data_addr, first_part_len);
            // rt_kprintf("index > before,2|buff:%p, offset:%d, ac_buff:%p, ac_len:%d\n", &buff[*offset], *offset, start_after_data_addr, first_part_len);
            *offset += first_part_len;

            unsigned int second_part_len = (after_samples * bytes_per_sample - first_part_len);
            rt_memcpy_s(&buff[*offset], second_part_len, sample_data, second_part_len);
            // rt_kprintf("index > before,3|buff:%p, offset:%d, ac_buff:%p, ac_len:%d\n", &buff[*offset], *offset, sample_data, second_part_len);
            *offset += second_part_len;
        }
    }
    else  // 触发点在录播前数据之前
    {
        unsigned char* start_before_data_addr = sample_data + (max_sample_size - (before_samples - trig_index)) * bytes_per_sample;
        unsigned int start_before_copy_len = (before_samples - trig_index) * bytes_per_sample;

        rt_memcpy_s(&buff[*offset], start_before_copy_len, start_before_data_addr, start_before_copy_len);
        *offset += start_before_copy_len;
        // rt_kprintf("index < before,1|buff:%p, offset:%d, ac_buff:%p, ac_len:%d\n", &buff[*offset], *offset, start_before_data_addr, start_before_copy_len);

        start_before_copy_len = trig_index * bytes_per_sample;
        rt_memcpy_s(&buff[*offset], start_before_copy_len, sample_data, start_before_copy_len);
        *offset += start_before_copy_len;
        // rt_kprintf("index < before,2|buff:%p, offset:%d, ac_buff:%p, ac_len:%d\n", &buff[*offset], *offset, sample_data, start_before_copy_len);

        unsigned char* start_after_data_addr = sample_data + trig_index * bytes_per_sample;
        unsigned int start_after_copy_len = after_samples * bytes_per_sample;

        rt_memcpy_s(&buff[*offset], start_after_copy_len, start_after_data_addr, start_after_copy_len);
        *offset += start_after_copy_len;
        // rt_kprintf("index < before,3|buff:%p, offset:%d, ac_buff:%p, ac_len:%d\n", &buff[*offset], *offset, start_after_data_addr, start_after_copy_len);
    }

    // rt_kprintf("copy_record_data_to_buff|out_offset:%d\n", *offset);
    return *offset;
}



int save_sample_extreme_value(unsigned short* sample_data, unsigned int sample_len, unsigned short sample_id, ms_time_base_t* time, unsigned char sample_point) {
    if (sample_data == NULL || sample_len == 0 || time == NULL) {
        return FAILURE;
    }
    unsigned char file_extreme_data[EXTREME_DATA_LEN] = {0};
    unsigned short file_min_val = 0, file_max_val = 0;
    unsigned short memory_min_value = 0, memory_max_value = 0;
    unsigned short min_value = 0, max_value = 0;
    struct stat stat_buff = {0};

    min_value = FALUT_MAX_VALUE;
    max_value = 0;
    find_min_max(sample_data, sample_len, &min_value, &max_value);
    memory_max_value = get_uint16_data(file_extreme_data + (sample_point - 1) * SIGLE_EXTREME_DATA_LEN + EXTREME_MAX_INDEX);
    memory_min_value = get_uint16_data(file_extreme_data + (sample_point - 1) * SIGLE_EXTREME_DATA_LEN + EXTREME_MIN_INDEX);
    memory_min_value = memory_min_value == 0? FALUT_MAX_VALUE : memory_min_value;

    if(storage_stat(EXTREME_DATA_FILE, &stat_buff) == SUCCESSFUL)//查看文件是否存在
    {
        if(handle_storage(read_opr, EXTREME_DATA_FILE, file_extreme_data, sizeof(file_extreme_data), 0) != SUCCESSFUL)
        {
            rt_kprintf("save_sample_extreme_value read error!!!!!\n");
            return FAILURE;
        }
        // 读取文件中的现有最大值和最小值
        file_max_val = get_uint16_data(file_extreme_data + (sample_point - 1) * SIGLE_EXTREME_DATA_LEN + EXTREME_MAX_INDEX);
        file_min_val = get_uint16_data(file_extreme_data + (sample_point - 1) * SIGLE_EXTREME_DATA_LEN + EXTREME_MIN_INDEX);
        file_min_val = file_min_val == 0? FALUT_MAX_VALUE : file_min_val;
        //rt_kprintf("min_value:%d max_value:%d file_max_val:%d file_min_val:%d\n",min_value,max_value,file_max_val,file_min_val);

        put_uint16_to_buff(s_extreme_data + (sample_point - 1) * SIGLE_EXTREME_DATA_LEN + EXTREME_SAMPLE_ID_INDEX, sample_id); // 存储ID
        if (max_value >= memory_max_value && max_value >= file_max_val ) {
            put_ms_time_base_to_buff(s_extreme_data + (sample_point - 1) * SIGLE_EXTREME_DATA_LEN + EXTREME_MAX_TIME_INDEX, *time); // 存储最大值时间
            put_uint16_to_buff(s_extreme_data + (sample_point - 1) * SIGLE_EXTREME_DATA_LEN + EXTREME_MAX_INDEX, max_value); // 存储最大值
        }

        if(min_value <= file_min_val && min_value <= memory_min_value)
        {
            put_ms_time_base_to_buff(s_extreme_data + (sample_point - 1) * SIGLE_EXTREME_DATA_LEN + EXTREME_MIN_TIME_INDEX, *time); // 存储最小值时间
            put_uint16_to_buff(s_extreme_data + (sample_point - 1) * SIGLE_EXTREME_DATA_LEN + EXTREME_MIN_INDEX, min_value); // 存储最小值 
        }
    }
    else
    {
        put_uint16_to_buff(s_extreme_data + (sample_point - 1) * SIGLE_EXTREME_DATA_LEN + EXTREME_SAMPLE_ID_INDEX, sample_id); // 存储ID
        if (max_value >= memory_max_value)
        {
            put_ms_time_base_to_buff(s_extreme_data + (sample_point - 1) * SIGLE_EXTREME_DATA_LEN + EXTREME_MAX_TIME_INDEX, *time); // 存储最大值时间
            put_uint16_to_buff(s_extreme_data + (sample_point - 1) * SIGLE_EXTREME_DATA_LEN + EXTREME_MAX_INDEX, max_value); // 存储最大值
        }

        if(min_value <= memory_min_value)
        {
            put_ms_time_base_to_buff(s_extreme_data + (sample_point - 1) * SIGLE_EXTREME_DATA_LEN + EXTREME_MIN_TIME_INDEX, *time); // 存储最小值时间
            put_uint16_to_buff(s_extreme_data + (sample_point - 1) * SIGLE_EXTREME_DATA_LEN + EXTREME_MIN_INDEX, min_value); // 存储最小值 
        }
    }
    return SUCCESSFUL;
}



int pack_record_data(record_data_sample_info_t* record_data_sample_info, unsigned char* record_data, trig_cause_e trig_cause)
{
    unsigned char point_num = 0;
    unsigned int offset = 0;
    unsigned char ac_t0 = 0, ac_t1 = 0;
    float dc_t0 = 0.0, dc_t1 = 0.0;

    get_one_para(TOV_PARA_ID_AC_T0_SIZE_OFFSET, &ac_t0);
    get_one_para(TOV_PARA_ID_AC_T1_SIZE_OFFSET, &ac_t1);
    get_one_para(TOV_PARA_ID_DC_T0_SIZE_OFFSET, &dc_t0);
    get_one_para(TOV_PARA_ID_DC_T1_SIZE_OFFSET, &dc_t1);

    if (ac_t0 + ac_t1 != AC_TOTAL_TIME || !(FLOAT_EQ((dc_t0 + dc_t1) , DC_TOTAL_TIME))) {
        rt_kprintf("%s:%d|pack_record_data time error\n", __FUNCTION__, __LINE__);
        return FAILURE;
    }

    // 触发时间
    put_ms_time_base_to_buff(record_data, record_data_sample_info->trig_time);
    offset += 9;

    // 触发原因
    put_int16_to_buff(&record_data[offset], record_data_sample_info->trig_cause);
    offset += 2;

    // 数据长度占位
    offset += 2;

    // 交流测点数
    unsigned short ac_point_offset = offset;
    offset += 1;

    // 处理交流测点
    offset = pack_ac_points(record_data_sample_info, record_data, trig_cause, offset, &point_num);

    // 赋值实际的交流测点数
    record_data[ac_point_offset] = point_num;

    // 直流测点数
    unsigned short dc_point_offset = offset;
    offset += 1;

    //清空交流测点数
    point_num = 0;
    // 处理直流测点
    offset = pack_dc_points(record_data_sample_info, record_data, trig_cause, offset, &point_num);

    // 赋值实际的直流测点数
    record_data[dc_point_offset] = point_num;

    // 设置数据长度
    put_int16_to_buff(&record_data[11], offset);

    // 计算CRC
    unsigned short crc = crc_cal((unsigned char *)&record_data[0], (unsigned short)offset);
    put_int16_to_buff(&record_data[offset], crc);
    offset += 2;

    return offset;
}



unsigned int pack_ac_points(record_data_sample_info_t* record_data_sample_info, unsigned char* record_data, trig_cause_e trig_cause, unsigned int offset, unsigned char* point_num)
{
    unsigned char  total_point_num = 0;
    unsigned char point = 0, loop = 0;
    unsigned char ac_t0 = 0, ac_t1 = 0;
    get_one_para(TOV_PARA_ID_OBSER_NUM_OFFSET, &total_point_num);
    get_one_para(TOV_PARA_ID_AC_T0_SIZE_OFFSET, &ac_t0);
    get_one_para(TOV_PARA_ID_AC_T1_SIZE_OFFSET, &ac_t1);

    for(loop = 0; loop < total_point_num; loop ++)
    {
        point = 0;
        get_one_para(TOV_PARA_ID_OBSER_OFFSET + loop, &point);
        if(point == INVALID_POINT || point == DC_VOL_POINT || point == DC_CURR_POINT || record_data_sample_info->ac_sample_data[point - 1].data_id == 0 || 
            record_data_sample_info->ac_sample_data[point - 1].sample_data == NULL)
        {
            continue;
        }
        (*point_num)++;
        put_int16_to_buff(&record_data[offset], record_data_sample_info->ac_sample_data[point - 1].data_id);
        offset += 2;
        unsigned char* sample_data_ptr = (unsigned char*)record_data_sample_info->ac_sample_data[point - 1].sample_data;

        if (trig_cause != POWER_DOWN_CAUSE)
        {
            save_before_and_after_data(ac_t0, ac_t1, record_data_sample_info->trig_index, sample_data_ptr, record_data, &offset);
            save_sample_extreme_value((unsigned short *)&record_data[offset - 4000], 2000, record_data_sample_info->ac_sample_data[point - 1].data_id, &(record_data_sample_info->trig_time), point);
        }
        else
        {
            save_before_and_after_data(POWER_CAUSE_AC_T0, POWER_CAUSE_AC_T1, record_data_sample_info->trig_index, sample_data_ptr, record_data, &offset);
        }
    }
    return offset;
}



unsigned int pack_dc_points(record_data_sample_info_t* record_data_sample_info, unsigned char* record_data, trig_cause_e trig_cause, unsigned int offset, unsigned char* point_num)
{
    unsigned char  total_point_num = 0;
    unsigned char point = 0, loop = 0;
    float dc_t0 = 0.0, dc_t1 = 0.0;
    get_one_para(TOV_PARA_ID_DC_T0_SIZE_OFFSET, &dc_t0);
    get_one_para(TOV_PARA_ID_DC_T1_SIZE_OFFSET, &dc_t1);
    get_one_para(TOV_PARA_ID_OBSER_NUM_OFFSET, &total_point_num);

    for(loop = 0; loop < total_point_num; loop ++)
    {
        point = 0;
        get_one_para(TOV_PARA_ID_OBSER_OFFSET + loop, &point);
        if(point == INVALID_POINT || (point != DC_VOL_POINT && point != DC_CURR_POINT) || record_data_sample_info->dc_sample_data[point - DC_VOL_POINT].data_id == 0 || 
            record_data_sample_info->dc_sample_data[point - DC_VOL_POINT].sample_data == NULL)
        {
            continue;
        }
        (*point_num)++;
        put_int16_to_buff(&record_data[offset], record_data_sample_info->dc_sample_data[point - DC_VOL_POINT].data_id);
        offset += 2;
        unsigned char* sample_data = (unsigned char*)record_data_sample_info->dc_sample_data[point - DC_VOL_POINT].sample_data;
        if (trig_cause != POWER_DOWN_CAUSE)
        {
            save_before_and_after_data(dc_t0, dc_t1, record_data_sample_info->trig_index, sample_data, record_data, &offset);
            save_sample_extreme_value((unsigned short *)&record_data[offset - 1000], 500 , record_data_sample_info->dc_sample_data[point - DC_VOL_POINT].data_id, &(record_data_sample_info->trig_time), point);
        }
        else
        {
            save_before_and_after_data(POWER_CAUSE_DC_T0, POWER_CAUSE_DC_T1, record_data_sample_info->trig_index, sample_data, record_data, &offset);
        }
    }
    return offset;
}




int save_record_data(record_data_sample_info_t* record_data_sample_info, unsigned char save_num, trig_cause_e trig_cause)
{
    int record_data_len = 0;
    record_data_len = pack_record_data(record_data_sample_info, s_record_data[save_num].data, trig_cause);
    RETURN_VAL_IF_FAIL(record_data_len != -1,FAILURE);
    s_record_data[save_num].len = record_data_len;
    return SUCCESSFUL;
}




int save_record_data_to_file(unsigned char* save_num)
{
    save_record_msg_t record_msg = {0};
    unsigned int loop = 0;
    record_msg.auth_code = 0x10;        // 0x10~0x1F为LOG权限码
    record_msg.record_type = RECORD_DATA_INDEX;
    for(loop = 0; loop < *save_num; loop++)
    {
        record_msg.hisdata = s_record_data[loop].data;
        record_msg.len = s_record_data[loop].len;  // 待存储记录数据的长度
        pub_msg_to_thread(LOG_MGR_SAVE_ONE_RECORD, &record_msg, sizeof(save_record_msg_t));
    }
    record_msg.record_type = EXTREME_DATA_INDEX;
    record_msg.hisdata = s_extreme_data;
    record_msg.len = EXTREME_DATA_LEN;  // 待存储记录数据的长度
    pub_msg_to_thread(LOG_MGR_SAVE_ONE_RECORD, &record_msg, sizeof(save_record_msg_t));
    *save_num = 0;
    rt_thread_delay(10000);
    return SUCCESSFUL;
}



int clear_memory_exterme_data()
{
    rt_memset_s(s_extreme_data, EXTREME_DATA_LEN, 0, EXTREME_DATA_LEN);
    return SUCCESSFUL;
}



int init_extreme_data()
{
    if(handle_storage(read_opr, EXTREME_DATA_FILE, s_extreme_data, sizeof(s_extreme_data), 0) != SUCCESSFUL)
    {
        rt_memset_s(s_extreme_data, EXTREME_DATA_LEN, 0, EXTREME_DATA_LEN);
        rt_kprintf("init_exterreme_data read error!!!!!\n");
        return FAILURE;
    }
    return SUCCESSFUL;
}


int send_ctrl_led_msg(unsigned short lightoff_time, unsigned short lighton_time, unsigned char pin_no)
{
    led_ctrl_msg ctrl_msg = {0};
    ctrl_msg.lightoff_time = lightoff_time;
    ctrl_msg.lighton_time = lighton_time;
    ctrl_msg.pin_no = pin_no;
    pub_msg_to_thread(LED_CTRL_MSG, &ctrl_msg, sizeof(led_ctrl_msg));
    return SUCCESSFUL;
}
