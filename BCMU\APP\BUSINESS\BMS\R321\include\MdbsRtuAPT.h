/**************************************************************************
* Copyright (C) 2001, ZTE Corporation.
* 版权信息：（C）2009，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：BMS
* 文件名称：MdbsRtuAPT.h
* 文件说明：
* 作    者：
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/

#ifndef CM7_APPLICATIONS_INCLUDE_MDBSRTUAPT_H_
#define CM7_APPLICATIONS_INCLUDE_MDBSRTUAPT_H_

#include "comm.h"

#ifdef __cplusplus
extern "C" {
#endif

#define LOCATION_SEND_READ_ANA_IN 0
#define LOCATION_WPARAMETERS_CHANGED_B 200
#define LOCATION_WEXTER_VOLT 4000
#define LOCATION_AC_PROTOCOLDESCRIPTION_B_END 284 
#define NUM_SPARE_REGISTER_1 184
#define NUM_SPARE_REGISTER_2 129
#define BATTERY_RATE 1
#define MODULE_IMPEDANCE 20
#define CELL_SIZE 32000
#define MODBUS_DOC_VERSION 1
#define MAX_ADDRESS 153
#define SPARE_ADDRESS_VALUE 0X7FFF

typedef struct
{
    WORD   wParametersChanged_b;           // 参数变化标志位，无此参数，APTower默认上送0x7FFF
    WORD   wNoOfBatteryModules_b;          // 每串中电池的序号，无此参数，APTower默认上送1
    WORD   wNoOfBatteryStrings_b;          // 每排中电池位于的串号，无此参数，APTower默认上送1
    WORD   wBatteryStringCapacity_b;       // 电池容量
    WORD   wProdDateYear_b;                // 电芯出厂日期-年
    WORD   wProdDateMonth_b;               // 电芯出厂日期-月
    WORD   wProdDateDay_b;                 // 电芯出厂日期-日
    WORD   acModelDescription_b[8];        // 名称，R321按照ZXESM R321上送
    WORD   wBatteryType_b;                 // 电池类型，这里固定按照0上送
    WORD   acSerialNumber_b[8];            // BMS出厂序列号
    WORD   acPartNumber_b[8];              // 整机序列号
    WORD   acSoftwareVersion_b[4];         // 软件版本，固定按照1.3.4上送
    WORD   acHardwareVersion_b[4];         // 硬件版本
    WORD   wTempCompEnable_b;              // 整组欠压保护温度补偿
    WORD   wFloatChargeRefVoltage_b;       // 充电参考电压，无此参数，默认上送0x7FFF
    WORD   wNomFloatCharegeRefTemp_b;      // 充电参考温度，无此参数，默认上送0x7FFF
    WORD   wTempCompSlope_b;               // 温度补偿斜率，无此参数，默认上送0x7FFF
    WORD   wMaxChargeVoltage_b;            // 充电最大电压，无此参数，默认上送0x7FFF
    WORD   wMinChargeVoltage_b;            // 充电最小电压，无此参数，默认上送0x7FFF
    WORD   wBatteryCurrentLimitEnable_b;   // 限流使能，对应中充放电使能
    WORD   wMaxBatteryChargeCurr_b;        // 最大充电电流
    WORD   wMaxBatteryDisChargeCurr_b;     // 最大放电电流
    WORD   wBoostChargeRefVoltage_b;       // 升压充电参考电压，无此参数，默认上送0x7FFF
    WORD   wEqualizeChargeRefVoltage_b;    // 均衡充电参考电压，无此参数，默认上送0x7FFF
    WORD   wMaxBoostChargeTime_b;          // 最大升压充电时间，无此参数，上送0
    WORD   wMaxEqualizeChargeTime_b;       // 最大均衡充电时间，无此参数，上送0
    WORD   wBoostInterval_b;               // 间隔升压之间的时间，无此参数，上送0
    WORD   wEqualizeInterval_b;            // 间隔均衡之间的时间，无此参数，上送0
    WORD   wBoostTriggerVoltage_b;         // 升压触发器电压，无此参数，上送0
    WORD   wLowVoltBattDisconnect_b;       // 低压蓄电池断开，无此参数，上送0
    WORD   wLowVoltBatteryReconnect_b;     // 低压蓄电池重新连接，无此参数，上送0
    WORD   wHighTempBattDisconnect_b;      // 高温电池断开，无此参数，上送0
    WORD   wHighTempBattDisconnectHyst_b;  // 高温电池断开之后，无此参数，上送0
    WORD   wBatteryRate_b;                 // 电池容量的额定小时数
    WORD   acManufacturerName_b[8];        // 制造商名称，这里固定上送ZTE Corporation
    WORD   acName_b[8];                    // BMS制造商名称，这里固定上送ZTE Corporation
    WORD   acProtocolDescription_b[8];     // modbus协议版本，固定上送ModBus V1.30
    WORD   wExterVolt;        // 母排电压  
    FLOAT  fBattCurr;         // 母排电流
    WORD   wModuleTemperature;// 按照单体的最高温度上送
    WORD   wBattSOH;          // 电池健康度
    WORD   wBattSOC;          // 电池剩余容量
    WORD   wModuleImpedance;  // TBD，无此参数，默认上送20   
    WORD   wTimeLeft;         // 放电剩余时间
    WORD   wAccumulatedCharged;    //累计充电容量
    WORD   wAccumulatedDischarged;    //累计放电容量
    WORD   wBattdischargeCycleTimes;    // 放电循环次数，按循环次数上送
    WORD   wBattchargeCycleTimes;      // 充电循环次数，按循环次数上送
    WORD   wModuleAlarms;     // 16位告警状态位
    WORD   wModuleWarnings;   // 16位warning状态位
    WORD   wModuleStatus;     // 电池组状态
    WORD   wBattVolt;          // 电池组电压
    WORD   wModbusVer;         // modbus软件版本，固定按照0x0001上送
    WORD   aBattSparesamll[184];// 备用寄存器
    WORD   wParametersChanged;  // 参数变化标志位，无此参数，APTower默认上送0x7FFF
    WORD   wBatteryCap;         // 电池额定容量
    WORD   wMaxBattChargeCurr;   // 电池最大充电电流
    WORD   wMaxBattDischargeCurr; // 电池最大放电电流
    WORD   wBatteryProdDateYear;  // 电芯出厂日期-年
    WORD   wBatteryProdDateMonth; // 电芯出厂日期-月
    WORD   wBatteryProdDateDay;   // 电芯出厂日期-日
    WORD   acBatteryModelDescription[8]; // 电池型号说明，ZXESM R321
    WORD   wBatteryType;         // 电池类型，固定按0上送
    WORD   acBatterySerialNumber[8]; // BMS出厂序列号
    WORD   acBatteryPartNumber[8]; // 整机序列号
    WORD   acBatterySoftwareVersion[4];// 软件版本，1.3.4
    WORD   acBatteryHardwareVersion[4];// 硬件版本
    WORD   wBatteryRate;           // 电池容量的额定小时数
    WORD   wNoOfCells;             // 电芯个数
    WORD   wCellSize;              // 电芯尺寸，默认上送32000
    WORD   acManufacturerName[8];  // 制造商名称，默认上送ZTE Corporation
    WORD   acName[8];              // BMS制造商名称，默认上送ZTE Corporation
    WORD   acBMSHardwareVersion[4];//  硬件版本
    WORD   acProtocolDescription[8]; // modbus版本
    WORD   aBattSparelarge[129];   // 备用寄存器
}T_CmpakSMUStruct;

#ifdef __cplusplus
}
#endif

#endif  /* CM7_APPLICATIONS_INCLUDE_MDBSRTU_H_ */
