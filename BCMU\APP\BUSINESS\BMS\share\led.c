#include "common.h"
#include "fileSys.h"
#include "sample.h"
#include "realAlarm.h"
#include "battery.h"
#include "para.h"
#include "comm.h"
#include "utils_heart_beat.h"
#include "thread_id.h"
#include "protocol.h"
#include "CommCan.h"
#include "commBdu.h"
#include "led.h"
#include "prtclDL.h"
#include "wireless.h"
#include "interface.h"
#include "hisdata.h"
#include "battery.h"
#include "SingleButton.h"

#define CONTACTOR_ON   0
#define CONTACTOR_OFF  1
#define LED_ONE_SECOND (100)
#define ALM_FIRE_CONTROL_ID 63

static BOOLEAN s_bInitUpdateLed = False;
Static T_LEDStruct s_tLEDStatus[6];   //0为运行灯，1为告警灯，//2-5为容量灯
static BOOLEAN s_bLedByAddr = FALSE;
static T_CtrlOutStruct s_tCtrl;
static BOOLEAN s_bLedSwitch = False;

static T_SysPara s_tSysPara;
Static WORD s_wSynRelay = 0;    //干接点同步
Static WORD s_wRelayCtrl = 0;   //本机干接点控制
Static WORD s_DefBuzzCounter = 0;        // 布防蜂鸣器计数器
UNUSED Static WORD s_EnterDefBuzzCounter = 0;   // 按键布防蜂鸣器计数器
static BYTE s_ucSPowerCount  = 0;        // 关机键按下次数，//TODO:待删除
static BYTE s_ucSWSleepCount = 0;        // 休眠按键按下次数
static BOOLEAN s_bSWTestFlag = FALSE;    // 按键测试 避免CtrlOut蜂鸣器影??
Static UINT32 s_fireRelayActionCounter = 0;

UNUSED static WORD    s_wDefStsQueryCnt = 0;
UNUSED Static BOOLEAN s_bDefStsQuery = False;
UNUSED Static BOOLEAN s_bEnterDefSts = False;

static BOOLEAN s_bPmosStatus = GPIO_ON;
Static Button_State *s_tButSta ;
Static BYTE s_ucFireControlStatus = 0;
Static BYTE s_wShutDownCount = 0; // 进入关机计时，控制灯和蜂鸣器
Static BYTE s_ucBuzzerStatus = 0; // 蜂鸣器控制状态
UNUSED static WORD s_wCntSecX = 0;  // TODO: 仅R321使用，分离后去除UNUSED宏

BYTE s_aucDigitPortTemp[DIGIT_PORT_NUM];

Static BOOLEAN s_bBattIndication = False; //1363、modbus或pad查找地址标志
Static BYTE s_ucCnt = 0;
/*****************************静态函数声明***************************************/
static void CtrlAlmLedStatus(BYTE type);
static void LEDProcess(void);
static void SetCapLedStatus(BYTE ucChgFlag, BYTE ucSoc);
static BYTE JudgeSysStatus(T_BattResult *pBattOut);
static BOOLEAN UpdateDigitPortStatus(void);
static void CheckApptestSW(void);
static void ProcessDefenceAct(void);
static void BuzzerOnXSec(WORD* _buzz_counter, BYTE ucSecond);
Static BOOLEAN ReadFireControlPin(void);
Static BYTE BUTTONShutDownLedIndicate(void);
Static BYTE BUTTONShutDownBuzzerIndicate(Button_State tButSta);
Static void BUTTONShutDownOffPress(void);
static BOOLEAN UpdateDigitPortStatus(void);
Static BOOLEAN QueryOrEnterDefStsBuzzAct(void);
static BYTE GetPinFireControlStatus(void);
Static BYTE FireActionRelayCtrlOut(BYTE relayPin, WORD* RelayOut);
#ifdef BUTTON_ENTER_DEFENCE_ENABLED
Static void ButtonEnterDefence(void);
Static BOOLEAN IndicateDefenceStatus(void);
#endif
/*
按键注册说明：按键功能包含多个注册信息分别为
1、uiStartTime---按键触发起始时间 (ms)
2、uiEndTime-----按键结束时间 (ms)
3、uiBackTime----返回时间，故在非正常模式下需要按多久可以返回正常状态
4、ucPressMode---按键模式（是否是连续按键还是持续按键，如果是连续按键，多少次连续注册到按键模式中，例如连续N次按键注册n ，持续按注册1
5、ucFuncMode----按键功能模式（按键结束后电池处于何种模式）
6、PressONFunc---按键达到按键触发起始时间时可执行的一次性功能
7、PressOFFFunc--按键到达结束时间可执行的一次性功能
8、ucWorkStage---按键功能模式的有效场景（例如关机模式只能在维护模式下生效，则注册维护模式）正常模式下可不注册此信息
9、ucNeedBack----此模式是否可恢复到正常模式，是：1 否 ：0
10、ucBtModeEn-此模式是否可以按键生效（此功能可外部模块通过Set_Mode_Work接口开启或者关闭，默认开启使能）按键模式生效：1 失效：0

以上功能如不需要可以不注册，例如不可返回，则可不注册uiBackTime，没有按键结束时间，可不注册，则按键松手一定生效
*/
static Button s_tButt_Func[] =
{
    //关机模式
    {
      .uiStartTime  =  3000,
      .ucPressMode  = PRESS_ONCE_MODE,
      .ucFuncMode   = BUT_SHUTDOWN,
      .ucWorkStage  = BUT_SLEEP,
      .PressOFFFunc = BUTTONShutDownOffPress,
    },
    //维护模式
    {
      .uiStartTime  =  3000,
      .uiEndTime    =  15000,
      .ucPressMode  = PRESS_ONCE_MODE,
      .ucFuncMode   = BUT_SLEEP,
      .ucWorkStage  = BUT_NORMAL,
    },
//    //wifi模式
//    {
//      .uiStartTime =  25000,
//      .uiEndTime   =  35000,
//      .ucMode      = PRESS_ONECE_MODE,
//      .ucFuncMode  = BUT_WIFIMODE,
//      .PressONFunc = NULL,
//      .PressOFFFunc= NULL,
//    },
//    //GPS模式
//    {
//      .uiStartTime =  35000,
//      .uiEndTime   =  45000,
//      .ucMode      = PRESS_ONECE_MODE,
//      .ucFuncMode  = BUT_GPSMODE,
//      .PressONFunc = NULL,
//      .PressOFFFunc= NULL,
//    },
#ifdef DEVICE_USING_R321
    //UPGRADE模式
    {
      .uiStartTime =  15000,
      .uiEndTime   =  25000,
      .uiBackTime   =  3000,
      .ucPressMode      = PRESS_ONCE_MODE,
      .ucFuncMode  = BUT_UPGRADEMODE,
      .PressONFunc = NULL,
      .PressOFFFunc= UpgradeMode,
      .ucWorkStage  = BUT_NORMAL,
      .ucNeedBack   = 1,
    },
#endif
    //布防模式
    {
      .ucPressMode  = PRESS_THRICE_MODE ,
      .ucFuncMode   = BUT_DEFENCE,
      .PressOFFFunc = DefenTigger,
      .ucWorkStage  = BUT_NORMAL,
    },
#ifdef BUTTON_ENTER_DEFENCE_ENABLED
    //按键布防
    {
      .ucPressMode  = PRESS_FOURICE_MODE ,
      .ucFuncMode   = BUT_NORMAL,
      .PressOFFFunc = ButtonEnterDefence,
      .ucWorkStage  = BUT_DEFENCE,
    },
#endif
};

#ifdef BUTTON_ENTER_DEFENCE_ENABLED
Static void ButtonEnterDefence(void)
{
    s_bDefStsQuery = FALSE; // 结束查询状态
    s_wCntSecX = 0;
    s_DefBuzzCounter = 0;
    ON_BUZZ(0); // 防止刚进布防查询还在3s响蜂鸣器时间内

    Button_Mode_Set(BUT_NORMAL);
    s_bEnterDefSts = TRUE;
    if (GetDefenceStatus()) // 布防成功,直接返回Ture
    {
        return;
    }
    else
    {  
        if(IsDefenseCondition())//能够布防条件
        {
            return;
        }
        
        WriteDefenceStatus(True);
        SaveAction(GetActionId(CONTOL_MANUAL_ENTER_DEFENCE), "ButtonEnterDefence");
    }
}
#endif
void DefenTigger(void)
{
   s_bDefStsQuery = True;
   SaveKeyDefQueryAction(); 
}
void InitCtrlOut(void)
{
    GPIO_Init();
//    MX_TIM2_Init();
//    MX_TIM4_Init();//底层初始化
    rt_memset((BYTE *)&s_tCtrl, 0, sizeof(s_tCtrl));
    s_bPmosStatus = GPIO_ON;
    return;
}

void GetCtrlOut(T_CtrlOutStruct* tCtrlOut)
{
    rt_memcpy((CHAR *)tCtrlOut, (CHAR *)&s_tCtrl, sizeof(T_CtrlOutStruct ));
    return;
}

void SetCtrlOut(T_CtrlOutStruct* tCtrlOut)
{
    rt_memcpy((CHAR *)&s_tCtrl, (CHAR *)tCtrlOut, sizeof(T_CtrlOutStruct));
    return;
}

void SetRelayCtrl(WORD wRelay)
{
    WORD wtmp;

    s_wRelayCtrl = wRelay;
    wtmp = wRelay;
#ifdef DEVICE_USING_R321
    if (s_tSysPara.bRelaySyncEn == 1)
    {
        wtmp |= s_wSynRelay;
    }
#endif

    if (s_tSysPara.ucRelayDefaultStatus == 0)
    {
        wtmp = ~wtmp;
    }
    
    s_tCtrl.wRelayOut = wtmp;
}

Static BYTE FireActionRelayCtrlOut(BYTE relayPin, WORD* RelayOut)
{
    switch (relayPin)
    {
    case 1:
        (*RelayOut) &= 0x02;
        break;
    case 2:
        (*RelayOut) &= 0x01;
        break;
    default:
        break;
    }
    return 0;
}

void SetRelayCtrl_new(BYTE* ucAlarmTemp, WORD wRelay)
{
    WORD wtmp;

    s_wRelayCtrl = wRelay;
    wtmp = wRelay;
#ifdef DEVICE_USING_R321
    if (s_tSysPara.bRelaySyncEn == 1)
    {
        wtmp |= s_wSynRelay;
    }
#endif

    if (s_tSysPara.ucRelayDefaultStatus == 0)
    {
        wtmp = ~wtmp;
    }
#ifdef FIRE_CONTROL_RELAY_ACTION_TIME_ENABLE
    if (ucAlarmTemp[ALM_FIRE_CONTROL_ID] && s_tSysPara.ucFireRelayControlEnable)
    {
        s_fireRelayActionCounter++;
        if (s_fireRelayActionCounter > 167) // 根据线程执行周期，这里大致为1分钟
        {
            FireActionRelayCtrlOut(s_tSysPara.aucRelayBit[ALM_FIRE_CONTROL_ID], &wtmp);
        }
    }
    else
    {
        s_fireRelayActionCounter = 0;
    }
#endif
    s_tCtrl.wRelayOut = wtmp;
}

BYTE GetRelayCtrl(void)
{
    return s_wRelayCtrl & 0xFF;
}

void SynRelayCtrl(BYTE ucRelayCtrl)
{
    s_wSynRelay = (WORD)ucRelayCtrl;
}

static void Button_Func_Regsist(void)
{
    for (uint8_t i = 0; i < (sizeof(s_tButt_Func)/sizeof(s_tButt_Func[0])) ; i++)
    {
        Button_Regist_Mode_s(&s_tButt_Func[i]);
    }
}

static BOOLEAN BuzzerCtrlButNormal(BYTE ucCounter) {
    GetSysPara( &s_tSysPara );

    if(s_tCtrl.bCtrlFlag == 0x01)
    {
        ON_BUZZ(s_tCtrl.bBuzz);
    }
    else if (s_bSWTestFlag)
    {
        ON_BUZZ(s_tCtrl.bBuzz);
    }
    else if (s_tSysPara.bBuzzerEnable && s_bBattIndication)
    {
        ON_BUZZ(True);
    }
    else if ((s_tCtrl.bBuzz == 2 && ucCounter < 140) || (s_tCtrl.bBuzz == 1 && ucCounter < 100))
    {
        ON_BUZZ(s_tCtrl.bBuzz);
    }
    else
    {
        ON_BUZZ(False);
    }
    return SUCCESSFUL;
}

static void BuzzerCtrl(Button_State s_tButSta) //TODO:待重构
{
    static BYTE s_ucCounter = 0;
    static BYTE s_ucBuzzTime = 0;
    static BYTE s_ucBuzzCounter = 0;
    
    if( s_tButSta.ucState != Button_ONPRESS && s_tButSta.ucCurrentMode == BUT_NORMAL)//如果按键状态没有按下时，正常执行蜂鸣器响应
    {
        BuzzerCtrlButNormal(s_ucCounter);
        
        //从近端网口模式回到正常模式时以下计数要清零，避免后面进入近端网口模式时蜂鸣器提示不正确。
        s_ucBuzzTime = 0;
        s_ucBuzzCounter = 0;
    }
    else if(s_tButSta.ucCurrentMode == BUT_UPGRADEMODE)
    {
        //蜂鸣器1s响1s不响，持续三次
        s_ucBuzzCounter ++;
        if(s_ucBuzzCounter <= 100)
        {
            ON_BUZZ(1);
        }
        else if(s_ucBuzzCounter <= 200)
        {
            ON_BUZZ(0);
        }
        else
        {
            s_ucBuzzTime ++;
            if(s_ucBuzzTime < 3)
            {
                s_ucBuzzCounter = 1;
                ON_BUZZ(1);
            }
            else
            {
                s_ucBuzzTime = 4;
                s_ucBuzzCounter = 200;
                ON_BUZZ(0);
            }
        }
    }
    else if(s_tButSta.ucCurrentMode == BUT_SLEEP)//休眠模式下执行的逻辑
    {
        ON_BUZZ(0);
    }
    BUTTONShutDownBuzzerIndicate(s_tButSta);// 休眠按键关机提示

    QueryOrEnterDefStsBuzzAct();

    if (s_ucCounter++ >= 200)
    {
        s_ucCounter = 0;
    }
}

Static BOOLEAN QueryOrEnterDefStsBuzzAct(void)
{
    if (s_bDefStsQuery)
    {
        BuzzerOnXSec(&s_DefBuzzCounter, 3);
        return True;
    }
    else if (s_bEnterDefSts)
    {
        BuzzerOnXSec(&s_EnterDefBuzzCounter, 1);
        if (s_EnterDefBuzzCounter > 100)
        {
            s_bEnterDefSts = FALSE;
            s_EnterDefBuzzCounter = 0;
        }
        return True;
    }
    return False;
}

/* 蜂鸣器响秒，单位秒 */
static void BuzzerOnXSec(WORD* _buzz_counter, BYTE ucSecond)
{
    if (_buzz_counter == NULL)
    {
        return;
    }
    TimerPlus(*_buzz_counter, HALF_AN_HOUR_PER_SECONDS);
    if((*_buzz_counter) <= 100 * ucSecond)
    {
        ON_BUZZ(1);
    }
    else
    {
        ON_BUZZ(0);
    }
}

void CtrlOut(void* parameter)
{
    BYTE i;
    InitCtrlOut();
    Button_Func_Regsist();
    pre_thread_beat_f(THREAD_LED);
    while(1)
    {
        UpdateDigitPortStatus();
        ReadFireControlPin(); //获取消防信号引脚状态
        CheckApptestSW();
        ProcessDefenceAct();

        thread_beat_go_on(THREAD_LED);
        if (!GetApptestFlag())
        {
            s_tButSta = Button_Funtion(10);
        }
#ifdef BUZZ_DISABLE_DEBUG
        ON_BUZZ(BUZZ_OFF);
#else
        LoadNetParaWhenUpgradeToNormal(*s_tButSta);
        BuzzerCtrl(*s_tButSta);
#endif
        //LED输出
        LEDProcess();
        for (i = 0; i < 6; i++)
        {
            CtrlLedOn(i, s_tCtrl.bLed[i]);
        }

        // 输出干结点控制
        Realy_Control(s_tCtrl.wRelayOut);

        rt_thread_delay(10);
#ifdef UNITEST    
        break;
#endif
    }

}

/***************************************************************************
 * @brief    对面板圆键控制
 * @param    {BYTE} ucStart-时间范围起始
 * @param    {BYTE} ucEnd-时间范围终止
 **************************************************************************/
BOOLEAN CheckSW_new(BYTE ucStart, BYTE ucEnd)  //TODO:待优化
{
    BYTE ucSWDur = getPortInput(Digit_SW_DET);

    if ((ucSWDur >= ucStart) && (ucSWDur < ucEnd || 0 == ucEnd))
    {
        s_ucCnt++;
        if (s_ucCnt == 1)
        {
            return True;
        }
    }
    else if (ucSWDur == (BYTE)0)
    {
        s_ucCnt = 0;
    }

    return False;
}

void SetLedStatus(BYTE LedID, BYTE Status)
{
    switch (Status)
    {
        case LEDOFF : //设置为灭时
            if (s_tLEDStatus[LedID].bOFF != 1)
            {
                s_tLEDStatus[LedID].bOFF = 1;
            }
            s_tLEDStatus[LedID].bON = 0;
            s_tLEDStatus[LedID].lSlowShine  = 0;
            s_tLEDStatus[LedID].lQuickShine = 0;
            s_tLEDStatus[LedID].lBlinkShine = 0;
            break;
        case LEDON : //设置为亮时
            if (s_tLEDStatus[LedID].bON != 1)
            {
                s_tLEDStatus[LedID].bON = 1;
            }
            s_tLEDStatus[LedID].bOFF = 0;
            s_tLEDStatus[LedID].lSlowShine  = 0;
            s_tLEDStatus[LedID].lQuickShine = 0;
            s_tLEDStatus[LedID].lBlinkShine = 0;
            break;
        case LEDSLOWSH : //设置为闪时
            if (s_tLEDStatus[LedID].lSlowShine == 0)
            {
                s_tLEDStatus[LedID].lSlowShine = 1;
            }
            s_tLEDStatus[LedID].bOFF = 0;
            s_tLEDStatus[LedID].bON  = 0;
            s_tLEDStatus[LedID].lQuickShine = 0;
            s_tLEDStatus[LedID].lBlinkShine = 0;
            break;
        case LEDQUICKSH : //设置为闪时
            if (s_tLEDStatus[LedID].lQuickShine == 0)
            {
                s_tLEDStatus[LedID].lQuickShine = 1;
            }
            s_tLEDStatus[LedID].bOFF = 0;
            s_tLEDStatus[LedID].bON  = 0;
            s_tLEDStatus[LedID].lSlowShine  = 0;
            s_tLEDStatus[LedID].lBlinkShine = 0;
            break;
        case LEDBLINK : //设置为眨眼
            if (s_tLEDStatus[LedID].lBlinkShine == 0)
            {
                s_tLEDStatus[LedID].lBlinkShine = 1;
            }
            s_tLEDStatus[LedID].bOFF = 0;
            s_tLEDStatus[LedID].bON  = 0;
            s_tLEDStatus[LedID].lSlowShine  = 0;
            s_tLEDStatus[LedID].lQuickShine = 0;
            break;
        case LEDSWITCH :
            if (s_tLEDStatus[LedID].lSlowShine == 0)
            {
                s_tLEDStatus[LedID].lSlowShine = 1;
            }
            s_tLEDStatus[LedID].bOFF = 0;
            s_tLEDStatus[LedID].bON  = 0;
            s_tLEDStatus[LedID].lQuickShine = 0;
            s_tLEDStatus[LedID].lBlinkShine = 0;
            break;
        default:
            break;
    }
}

static void LEDProcess(void)
{
    BYTE i;
    T_CtrlOutStruct tCtrlOut;
    GetCtrlOut(&tCtrlOut);
    if (!GetApptestFlag() && tCtrlOut.bCtrlFlag == 0x00)
    {
        //如果处于布防查询状态这里的控制逻辑就不走了。
        CtrlLedStatus();
    }

    if ((s_bLedByAddr&&GetApptestFlag()) || !GetApptestFlag())
    {
        
            for (i = 0; i < 6; i ++)
            {
                if (s_tLEDStatus[i].bOFF || s_tLEDStatus[i].lSlowShine >  100 || \
                    s_tLEDStatus[i].lQuickShine > 25 || s_tLEDStatus[i].lBlinkShine > 25)   //闪为亮1s，灭1s
                {
                    s_tCtrl.bLed[i] = False;
                }

                if (s_tLEDStatus[i].bON || (s_tLEDStatus[i].lSlowShine > 0 && s_tLEDStatus[i].lSlowShine <= 100) || \
                   (s_tLEDStatus[i].lQuickShine > 0 && s_tLEDStatus[i].lQuickShine <= 25) || \
                   (s_tLEDStatus[i].lBlinkShine > 0 && s_tLEDStatus[i].lBlinkShine <= 25))
                {
                    s_tCtrl.bLed[i] = True;
                }

                if (s_tLEDStatus[i].lSlowShine > 0)
                {
                    s_tLEDStatus[i].lSlowShine ++;
                    if (s_tLEDStatus[i].lSlowShine >= 200)
                    {
                        s_tLEDStatus[i].lSlowShine = 1;
                    }
                }
                if(s_bLedSwitch == TRUE)
                {
                    s_tCtrl.bLed[LED_ALARM] = (~s_tCtrl.bLed[LED_RUN])&0x01;
                }
                if (s_tLEDStatus[i].lQuickShine > 0)
                {
                    s_tLEDStatus[i].lQuickShine ++;
                    if (s_tLEDStatus[i].lQuickShine >= 50)
                    {
                        s_tLEDStatus[i].lQuickShine = 1;
                    }
                }
                if (s_tLEDStatus[i].lBlinkShine > 0)
                {
                    s_tLEDStatus[i].lBlinkShine ++;
                    if (s_tLEDStatus[i].lBlinkShine >= 400)
                    {
                        s_tLEDStatus[i].lBlinkShine = 1;
                    }
                }
            }
    }
    return;
}



// 定义一个数组，用于存储每个类型对应的LED状态
const BYTE RunledStatus[RUN_STATUS_NUM] = {
    [RUN_SLEEP_ON] = LEDBLINK,
    [RUN_SLEEP_OFF] = LEDOFF,
    [RUN_SHUTDOWN] = LEDOFF,
    [RUN_STANDBY] = LEDSLOWSH,
    [RUN_CHARGE] = LEDON,
    [RUN_DISCHARGE] = LEDQUICKSH,
    [RUN_SYS_ABN] = LEDSLOWSH,
    [RUN_SYS_LOCK] = LEDON,
    [RUN_WIFI] = LEDQUICKSH,
    [RUN_GPS_TEST] = LEDON,
    [RUN_DEFENCE_ON] = LEDON,
    [RUN_DEFENCE_OFF] = LEDQUICKSH,
    [RUN_UPGRADE] = LEDSLOWSH
};

static void CtrlRunLedStatus(BYTE ucType)
{
    UNUSED T_GpsData tGps;  // TODO: 仅R321使用，分离后去除UNUSED宏

    if (ucType >= RUN_STATUS_NUM)
    {
        return;
    }

    SetLedStatus(LED_RUN, RunledStatus[ucType]);

    switch (ucType)
    {
        case RUN_GPS_TEST :  // TODO: 暂无法移走，待进一步优化
#ifdef DEVICE_USING_R321
            if (getCurrentPlace(&tGps)==TRUE)
                SetLedStatus(LED_CAP4,LEDON);
            else
                SetLedStatus(LED_CAP3,LEDON);
#endif
            break;

        default:
            break;
    }

    return;
}



// 定义一个数组，用于存储每个类型对应的LED状态
const BYTE AlmledStatus[ALM_STATUS_NUM] = {
    [ALM_NORMAL] = LEDOFF,
    [ALM_ABNORMAL] = LEDSLOWSH,
    [ALM_PROT] = LEDSLOWSH,
    [ALM_COMM_FAIL] = LEDQUICKSH,
    [ALM_SYS_ABNORMAL] = LEDON,
    [ALM_SLEEP] = LEDOFF,
    [ALM_WIFI] = LEDON,
    [ALM_SYS_LOCK] = LEDQUICKSH,
    [ALM_DEFENCE_ON] = LEDON,
    [ALM_DEFENCE_OFF] = LEDQUICKSH,
    [ALM_CRITICAL] = LEDQUICKSH,
    [ALM_UPGRADE] = LEDSLOWSH
};

static void CtrlAlmLedStatus(BYTE ucType)
{
    if (ucType >= ALM_STATUS_NUM)
    {
        return;
    }

    SetLedStatus(LED_ALARM, AlmledStatus[ucType]);
}



static void SetAllCapLedStatus(BYTE ucType)
{
    if (ucType >= LEDSTATUSNUM)
    {
        return;
    }

    SetLedStatus(LED_CAP1, ucType);
    SetLedStatus(LED_CAP2, ucType);
    SetLedStatus(LED_CAP3, ucType);
    SetLedStatus(LED_CAP4, ucType);
}



static BYTE CalSocClass(BYTE ucSocValue)
{
    BYTE ucSocClass = 0;
    T_BattResult tBattOut = {0};

    GetBattResult(&tBattOut);

    if ((ucSocValue == 0) || (ucSocValue > 100))
    {
        ucSocClass = SOCFAULT;
    }
    else if ((ucSocValue == 100) || ((ucSocValue >= 99)&&(tBattOut.ucBatStatus == BATT_MODE_STANDBY)))
    {
        ucSocClass = 4;
    }
    else
    {
        ucSocClass = (ucSocValue - 1) / 25;  // 0:SOC<25 1:25~50 2:50~75 ，其中减1是为解决边界问题
    }

    return ucSocClass;
}



static void SetCapLedStatus(BYTE ucChgFlag, BYTE ucSoc)
{
    BYTE ucChargeType = 0;
    BYTE ucSocType = 0;

    // 1、优先处理特殊场景
    switch (s_tButSta->ucLed_CurrentMode)
    {
        case BUT_UPGRADEMODE:
            SetAllCapLedStatus(LEDSLOWSH);
            return;
        case BUT_SHUTDOWN:
        case BUT_SLEEP:
        case BUT_GPSMODE:
            SetAllCapLedStatus(LEDOFF);
            return;
        case BUT_DEFENCE:
            ucSocType = GetDefenceStatus() ? LEDON : LEDQUICKSH;
            SetAllCapLedStatus(ucSocType);
            return;
        default:
            break;
    }

    // 2、然后处理一般场景
    ucChargeType = (ucChgFlag == CHARGE) ? LEDQUICKSH : LEDON;  //充电快闪，否则长亮
    ucSocType = CalSocClass(ucSoc);

    switch (ucSocType)
    {
        case 0 : //SOC = [0, 25]
            SetLedStatus(LED_CAP1,ucChargeType);
            SetLedStatus(LED_CAP2,LEDOFF);
            SetLedStatus(LED_CAP3,LEDOFF);
            SetLedStatus(LED_CAP4,LEDOFF);
            break;
        case 1 : //SOC = (25, 50]
            SetLedStatus(LED_CAP1,LEDON);
            SetLedStatus(LED_CAP2,ucChargeType);
            SetLedStatus(LED_CAP3,LEDOFF);
            SetLedStatus(LED_CAP4,LEDOFF);
            break;
        case 2 : //SOC = (50, 75]
            SetLedStatus(LED_CAP1,LEDON);
            SetLedStatus(LED_CAP2,LEDON);
            SetLedStatus(LED_CAP3,ucChargeType);
            SetLedStatus(LED_CAP4,LEDOFF);
            break;
        case 3 : //SOC = (75, 100)
            SetLedStatus(LED_CAP1,LEDON);
            SetLedStatus(LED_CAP2,LEDON);
            SetLedStatus(LED_CAP3,LEDON);
            SetLedStatus(LED_CAP4,ucChargeType);
            break;
        case 4 : //SOC = 100
            SetAllCapLedStatus(LEDON);
            break;
        default : //SOC异常
            SetAllCapLedStatus(LEDOFF);
            break;
    }

    return;
}


static BYTE JudgeSysStatus(T_BattResult *pBattOut)
{
    BYTE ucRet = RUN_SLEEP_ON;

    T_BCMDataStruct tBcmData;
    T_BCMAlarmStruct tBCMAlm;

    if (pBattOut == NULL)
    {
        return RUN_STANDBY;
    }

    GetRealData(&tBcmData);
    rt_memset(&tBCMAlm, 0, sizeof(T_BCMAlarmStruct));
    GetRealAlarm(BCM_ALARM, (BYTE *)&tBCMAlm);
    GetSysPara( &s_tSysPara );

    if (IsSleep())
    {
        return ((s_tSysPara.ucSleepIndicator) ? RUN_SLEEP_ON : RUN_SLEEP_OFF);
    }

    /*************************************/
    if (pBattOut->ucBatStatus == BATT_MODE_STANDBY) //在线非浮充
    {
        ucRet = RUN_CHARGE;
    }
    else if(pBattOut->ucBatStatus == BATT_MODE_CHARGE)
    {
        ucRet = RUN_CHARGE;
    }
    else if (fabs(tBcmData.fBattCurr) < MIN_CURR_DET_BDCU)
    {
        ucRet = RUN_STANDBY;
    }
    else
    {
        ucRet = RUN_DISCHARGE;
    }

    if (IfSysInvalid())
    {
        ucRet = RUN_SYS_ABN;
    }

    if (IsExistLostAlarm(&tBCMAlm) == True)
    {
        ucRet = RUN_SYS_LOCK;
    }

    return ucRet;
}

BOOLEAN UpdateLed(void)
{
    BYTE i = 0;
   if (IsUpdate() || IsBduUpdate())
    {
        if (!s_bInitUpdateLed)
        {
            rt_memset(s_tLEDStatus, 0, sizeof(T_LEDStruct)*6);
            s_bInitUpdateLed = True;
        }

        for (i = 0; i < 6; i++)
        {
            SetLedStatus(i, LEDQUICKSH);
        }
        return True;
    }
    else
    {
        s_bInitUpdateLed = False;
    }
    return False;
}

void CtrlLedStatus(void)
{
    BYTE ucRunType = 0xFF;
    BYTE ucAlmType = 0xFF;
    BYTE ucChargeType = 0;//判断是否在充电
    T_BattResult tBattOut;
    s_bLedSwitch = False;
    BYTE i = 0;

    if(UpdateLed() == True)
    {
        return;
    }

    GetBattResult(&tBattOut);
    GetSysPara(&s_tSysPara);

    switch(s_tButSta->ucLed_CurrentMode)
    {
        case BUT_NORMAL :
        {
            if(s_tButSta->ucReturnToNormal !=0)
            {
                ucAlmType = ALM_NORMAL;
                ucRunType = RUN_CHARGE;
            }
            else
            {
                ucAlmType = JudgeAlarmType(&tBattOut);
                ucRunType = JudgeSysStatus(&tBattOut);
            }
        }
        break;
        case BUT_SLEEP  :
        {
            ucAlmType = ALM_SLEEP;
            ucRunType = (s_tSysPara.ucSleepIndicator) ? RUN_SLEEP_ON : RUN_SLEEP_OFF;
        }
        break;
        case BUT_SHUTDOWN  :
        {
            ucAlmType = ALM_NORMAL;
            ucRunType = RUN_SHUTDOWN;               
        }
        break;
        case BUT_WIFIMODE  :
        {
            ucAlmType = ALM_WIFI;
            ucRunType = RUN_WIFI;
        }
        break;
        case BUT_GPSMODE  :
        {
            ucAlmType = JudgeAlarmType(&tBattOut);
            ucRunType = RUN_GPS_TEST;
        }
        break;     
        case BUT_UPGRADEMODE:
        {
            ucAlmType = ALM_UPGRADE;
            ucRunType = RUN_UPGRADE;
        }
        break;
        case BUT_DEFENCE ://布防状态
        {

            if (GetDefenceStatus())
            {
                ucAlmType = ALM_DEFENCE_ON;
                ucRunType = RUN_DEFENCE_ON;
            }
            else
            {
                ucAlmType = ALM_DEFENCE_OFF;
                ucRunType = RUN_DEFENCE_OFF;
            }

        }
        break;
        default:
        break;
        
    }

    //电池指示，所有指示灯全快闪
    if(s_bBattIndication)
    {
        for(i = 0; i < LED_NUM; i++) {
            SetLedStatus(i, LEDQUICKSH);
        }
        return;
    }

    if (ucRunType == RUN_CHARGE)
    {
        ucChargeType = CHARGE;
    }
    else
    {
        ucChargeType = DISCHARGE;
    }

    CtrlAlmLedStatus(ucAlmType); //ALM灯
    SetCapLedStatus(ucChargeType,tBattOut.wBatSOC);//CAP灯
    CtrlRunLedStatus(ucRunType);//RUN灯,因有休眠状态，必须放最后面
    BUTTONShutDownLedIndicate(); // 如果是进关机则所有灯亮1s,蜂鸣器响1s提示按键生效
    return;
}

void SetLedByAddr(void *parameter)
{
    BYTE i;
    BYTE ucAddr = GetTotalAddr();
    T_CtrlOutStruct tCtrlOut;

    GetCtrlOut(&tCtrlOut);
    SetLedStatus(LED_RUN, LEDON);
    SetLedStatus(LED_ALARM, LEDON);
    for(i = 0; i < 4; i++)
    {
        BYTE ucLedIndex = i+LED_CAP1;                           //Coverity
        if(ucAddr & BIT(i))
        {
            tCtrlOut.bLed[ucLedIndex] = LEDQUICKSH;
            SetLedStatus(ucLedIndex,LEDQUICKSH);
        }
        else
        {
            tCtrlOut.bLed[ucLedIndex] = LEDOFF;
            SetLedStatus(ucLedIndex,LEDOFF);
        }
    }
    SetCtrlOut(&tCtrlOut);
    s_bLedByAddr = TRUE;

    return;
}

void ClearLedByAddr(void)
{
    s_bLedByAddr = FALSE;
}

BYTE UnitestGetLEDStatus(BYTE ucLedType)
{
    if (ucLedType > CAP4_LED_TYPE)
    {
        return 0xFF; //错误
    }

    if (s_tLEDStatus[ucLedType].bON == 1)
    {
        return LEDON;
    }
    else if (s_tLEDStatus[ucLedType].lSlowShine > 0)
    {
        return LEDSLOWSH;
    }
    else if (s_tLEDStatus[ucLedType].lQuickShine > 0)
    {
        return LEDQUICKSH;
    }
    else if (s_tLEDStatus[ucLedType].lBlinkShine > 0)
    {
        return LEDBLINK;
    }
    else
    {
        return LEDOFF;
    }
}

/*蜂鸣器的控制请统一调用该接口函数，不要直接调用BSP_BUZZ_CTRL函数。*/

#define BUZZ_CTRL_MODE_NUM  (3)
const CTRL_State aeBuzzCtrlMode[BUZZ_CTRL_MODE_NUM] = {CTRL_OFF, CTRL_BUZZ_MODE1, CTRL_BUZZ_MODE2};

void ON_BUZZ(BYTE ucAlm)
{
    if (ucAlm >= BUZZ_CTRL_MODE_NUM)
    {
        return;  // 模式错误
    }

    if (s_ucBuzzerStatus == ucAlm)
    {
        return;  // 状态未变
    }

    s_ucBuzzerStatus = ucAlm;

    BSP_BUZZ_CTRL(aeBuzzCtrlMode[ucAlm]);

    return;
}


/*
 * SW_B1 PMOS接口
 */
void CtrlTurnOn(BOOLEAN bOnoff)
{
    BYTE ucWaitCnt = 0;
    time_t start_time = time(RT_NULL);
    T_DCRealData tRealData;

    if(IsBduUpdate())
    {
        return;
    }

    rt_memset(&tRealData, 0, sizeof(T_DCRealData));
    GetBduReal(&tRealData);

    // R321在关PMOS前等待关电池侧接触器
    if(bOnoff == GPIO_OFF)
    {
        while(tRealData.tDCStatus.bContactorBatt != CONTACTOR_OFF && ucWaitCnt < 30)
        {
            rt_thread_delay(1000);
            GetBduReal(&tRealData);
            ucWaitCnt = (BYTE)(time(RT_NULL) - start_time);
        }
    }
    ShutDownDevice(bOnoff);
    s_bPmosStatus = bOnoff;
}

BOOLEAN GetPMOSStatus(void)
{
    return s_bPmosStatus;
}

void SetPMOSStatus(BOOLEAN status)
{
    if(s_bPmosStatus != status)
    {
        if(status != TRUE )
        {
            saveResetData(RESET_REASON, NO_RESET_SHUTDOWN);
            SaveBattInfo();
            SaveHisPoint();
            CtrlTurnOn(GPIO_OFF); 
        }
        else
        {
            CtrlTurnOn(GPIO_ON);
        }
    }
}
void CtrlSleep(BOOLEAN bBuzz)
{

    saveResetData(RESET_REASON, NO_RESET_SHUTDOWN);
    SaveBattInfo();
    SaveHisPoint();
    CtrlTurnOn(GPIO_OFF);
    if (bBuzz)
    {
        ON_BUZZ(1);
    }
    rt_thread_delay(1000);//根据需求关闭辅助源之后响1s
    ON_BUZZ(0);
    rt_thread_delay(20000);
    ResetMCU(NO_RESET_UNKNOW); 
}

/**
 * @brief 根据布防查询状态进行指示灯显示
 *
 * 如果处于布防查询状态，根据实际的布防状态进行指示灯的显示以及按键布防操作。
 * 
 * @param None
 * @return 如果布防成功返回True，否则返回False
 */
#ifdef BUTTON_ENTER_DEFENCE_ENABLED
Static BOOLEAN IndicateDefenceStatus(void)
{
    //处于布防查询状态时根据实际的布防状态进行指示灯显示
    s_wCntSecX++;

    if (GetDefenceStatus()) // 布防成功，亮灯10秒
    {
        if (s_wCntSecX > 1000)
        {
            Button_Mode_Set(BUT_NORMAL);
            s_bDefStsQuery = FALSE; // 结束查询状态
            s_wCntSecX = 0;
            s_DefBuzzCounter = 0;
        }
        return True;
    }
    else
    {
        if (s_wCntSecX > 2000) // 布防失败，闪灯20秒
        {
            Button_Mode_Set(BUT_NORMAL);
            s_bDefStsQuery = FALSE; // 结束查询状态
            s_wCntSecX = 0;
            s_DefBuzzCounter = 0;
        }
        return False;
    }
}
#endif
/**
 * @brief 更新数字端口状态
 *
 * 从相关引脚读取数字端口的状态，更新到全局变量 s_aucDigitPortTemp 中。
 *
 * @param None
 * @return BOOLEAN
 */
Static BOOLEAN UpdateDigitPortStatus(void)
{
    BYTE i, j;
    BYTE uctmp[DIGIT_PORT_NUM][3] = {0, };
    for (j = 0; j < 3; j++)
    {
        // R321只有一个按键，对应的引脚为SW_DET_Pin，S_POWER_Pin没有使用;
        uctmp[Digit_SW_DET][j]     = rt_pin_read(SW_DET_Pin);
        #ifdef BUTTON_ENTER_DEFENCE_ENABLED
        if(IsSupportsAntiTheftLine())
        {
            uctmp[Digit_THEFT_DET][j]  = rt_pin_read(THEFT_DET_Pin);
        }
        #else
        uctmp[Digit_THEFT_DET][j]  = rt_pin_read(THEFT_DET_Pin);
        #endif
        //uctmp[Digit_SPOWER_DET][j] = rt_pin_read(S_POWER_Pin);
        uctmp[Digit_BALANCE_DET][j] = rt_pin_read(BALANCE_FAULT_Pin);
    }

    for (i = 0; i < DIGIT_PORT_NUM; i++)
    {
        if ((uctmp[i][2] == uctmp[i][1]) && (uctmp[i][1] == uctmp[i][0]))
        {
            s_aucDigitPortTemp[i] = uctmp[i][0];
        }
    }
    return True;
}

BYTE getPortInput(BYTE ucPinInex)
{
    if (ucPinInex >= DIGIT_PORT_NUM)
    {
        return GPIO_OFF;
    }

    return s_aucDigitPortTemp[ucPinInex];
}

static void CheckApptestSW(void)
{
    static WORD s_wDurationCnt = 60000;
    static WORD s_wCnt_SWSleep = 0;
    T_CtrlOutStruct tCtrlOut;

    if (!GetApptestFlag() && !GetQtptestFlag())
    {
        return;
    }

    GetCtrlOut(&tCtrlOut);

    if (1 == s_aucDigitPortTemp[Digit_SW_DET])
    {
        if (s_wDurationCnt < 30000)
        {
            s_wDurationCnt++;
        }

        s_wCnt_SWSleep++;
        if(s_wCnt_SWSleep > 5)
        {
            s_bSWTestFlag  = TRUE;
            tCtrlOut.bBuzz = TRUE;
        }
    }
    else
    {
        s_wDurationCnt = 0;

        if(s_wCnt_SWSleep > 5)//按键防抖延时50ms
        {
            s_ucSWSleepCount++;
            tCtrlOut.bBuzz = FALSE;
        }

        s_wCnt_SWSleep = 0;
    }

    if (s_wDurationCnt >= 60000)  // 开机时按键按下未松开，不计时
    {
        s_aucDigitPortTemp[Digit_SW_DET] = 0;
    }
    else
    {
        s_aucDigitPortTemp[Digit_SW_DET] = s_wDurationCnt/100;
    }

    SetCtrlOut(&tCtrlOut);
}

static void ProcessDefenceAct(void)
{

    // WORD wCounter = 0;  //KW 编译告警
    UNUSED static WORD s_wCntSec10 = 0; // TODO: 仅R321使用，分离后去除UNUSED宏

    if (GetApptestFlag() || GetQtptestFlag())
    {
        return;
    }

    if(GetDefenceStatus())//布防成功
    {
        Set_Mode_Work(BUT_SLEEP, False);
    }
    else
    {
        Set_Mode_Work(BUT_SLEEP, True);
    }

	//处于布防查询状态时根据实际的布防状态进行指示灯显示
    if (s_bDefStsQuery == True)
    {        
        IndicateDefenceStatus();
    }
} 


BYTE GetSPowerBtnCount(void)
{
    return s_ucSPowerCount;
}

BYTE GetSWSleepBtnCount(void)
{
    return s_ucSWSleepCount;
}

void ClearBtnCount(void)
{
    s_ucSPowerCount  = 0;
    s_ucSWSleepCount = 0;
    s_bSWTestFlag    = FALSE;
}

BOOLEAN IsEqualCircuitFaultDetected(void)
{
    return getPortInput(Digit_BALANCE_DET) == PIN_HIGH;
}


void LoadNetParaWhenUpgradeToNormal(Button_State s_tButSta)
{
    static BYTE s_lastCurrentMode = BUT_NORMAL;
    if((s_lastCurrentMode == BUT_UPGRADEMODE) && (s_tButSta.ucCurrentMode == BUT_NORMAL))
    {
        LoadNetPara();
    }
    if(s_tButSta.ucCurrentMode == BUT_UPGRADEMODE)
    {
        s_lastCurrentMode = BUT_UPGRADEMODE;
    }
    else
    {
        s_lastCurrentMode = BUT_NORMAL;
    }
}

BOOLEAN SetBattIndicationStatus(BOOLEAN status){
    s_bBattIndication = status;
    return SUCCESSFUL;
}

// 用于定时器，15s后关闭电池指示
void CloseBattIndication(void *parameter)
{
    SetBattIndicationStatus(False);
    return;
}

Static BOOLEAN ReadFireControlPin(void)
{
    static BYTE s_ucFCCnt = 0;
    static BYTE  s_ucFCStat = 0;

    if (False == IsSupportsFireControl())
        return ERROR;

    if (s_ucFCStat != rt_pin_read(FC_PIN))
    {
        TimerPlus(s_ucFCCnt, 10);
    }
    else
    {
        s_ucFCStat ^= 1;
        s_ucFCCnt = 0;
    }
    if (s_ucFCCnt == 10)
    {
        s_ucFireControlStatus = s_ucFCStat;//消防信号状态
    }

    return SUCCESS;
}

static BYTE GetPinFireControlStatus(void)
{
    return s_ucFireControlStatus;
}

BYTE GetFireControlStatus(void)
{
    // 消防判断两种方案兼容
    return GetPinFireControlStatus() || GetAdcFireControlStatus();
}

Static BYTE BUTTONShutDownLedIndicate(void)
{
    BYTE i;
    if (s_wShutDownCount > 100 || s_wShutDownCount == 0)
    {
        // 按键进入关机模式后,将所有的指示灯点亮1s之后全部熄灭。
        return FAILURE;
    }
    else
    {
        for (i = 0; i < 6; i++) // 按键进入关机模式后,将所有的指示灯点亮1s。
        {
            SetLedStatus(i, LEDON);
        }
        return SUCCESSFUL;
    }
}

Static BYTE BUTTONShutDownBuzzerIndicate(Button_State tButSta)
{
    if(tButSta.ucLed_CurrentMode == BUT_SHUTDOWN && tButSta.ucCurrentMode != BUT_SHUTDOWN) // 灯已切换模式
    {
        TimerPlus(s_wShutDownCount, 150);
        if (s_wShutDownCount > 100)
        {
            ON_BUZZ(0);
            return FAILURE;
        }
        else
        {
            ON_BUZZ(1);// 按键进入关机模式后,蜂鸣器响1s提示。
            return SUCCESSFUL;
        }
    }
    else
    {
        s_wShutDownCount = 0;
        return FAILURE;
    }

}

BYTE GetBuzzerStatus(void)
{
    return s_ucBuzzerStatus;
}

Static void BUTTONShutDownOffPress(void)
{
    ON_BUZZ(0);
}
