
#ifndef FILE_LOG_SET_H_
#define FILE_LOG_SET_H_

#ifdef __cplusplus
extern "C" {
#endif



#ifdef ULOG_BACKEND_USING_FILE

#define SAVE_FILE_CLASS_NUM 1
#define True    1
typedef uint8_t         BOOLEAN;
typedef uint8_t         BYTE;
unsigned int get_write_log_cnt();
rt_bool_t sys_log_file_backend_filter(struct ulog_backend *backend, rt_uint32_t level, const char *tag, rt_bool_t is_raw, 
                                    const char *log, rt_size_t len);
#endif

#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif


#endif  // SOFTWARE_SRC_APP_APPTEST_H_
