#include "bcmu_self_test.h"
#include "power_on_self_test.h"
#include "alarm_register.h"
#include "alarm_mgr_api.h"

int bcmu_power_on_check_item[] =
{    
    //后面的1是等级，即表示自检项有 一级xxx告警,若告警ID为开关量，则表示存在该告警
    //第一簇
    GET_ALM_ID(CLUSTER_VOLT_HIGH,1,2),    //电芯过压告警 2级
    GET_ALM_ID(CLUSTER_VOLT_HIGH,1,3),    //电芯过压告警 3级
    GET_ALM_ID(CLUSTER_VOLT_LOW,1,2),     //电芯欠压告警 2级
    GET_ALM_ID(CLUSTER_VOLT_LOW,1,3),     //电芯欠压告警 3级
    GET_ALM_ID(CLUSTER_VOLT_DIFF_OVER,1,2),//电芯压差过大告警 2级
    GET_ALM_ID(CLUSTER_VOLT_DIFF_OVER,1,3),//电芯压差过大告警 3级
    GET_ALM_ID(CLUSTER_CHG_HIGH_TEMP,1,2), //电芯充电高温告警 2级
    GET_ALM_ID(CLUSTER_CHG_HIGH_TEMP,1,3), //电芯充电高温告警 3级
    GET_ALM_ID(CLUSTER_DISCHG_HIGH_TEMP,1,2), //电芯放电高温告警 2级
    GET_ALM_ID(CLUSTER_DISCHG_HIGH_TEMP,1,3), //电芯放电高温告警 3级
    GET_ALM_ID(CLUSTER_CHG_LOW_TEMP,1,2), //电芯充电低温告警 2级
    GET_ALM_ID(CLUSTER_CHG_LOW_TEMP,1,3), //电芯充电低温告警 3级
    GET_ALM_ID(CLUSTER_DISCHG_LOW_TEMP,1,2),//电芯放电低温告警 2级
    GET_ALM_ID(CLUSTER_DISCHG_LOW_TEMP,1,3),//电芯放电低温告警 3级
    GET_ALM_ID(CLUSTER_OVER_TEMP_DIFF,1,2), //电芯温差过大 2级
    GET_ALM_ID(CLUSTER_OVER_TEMP_DIFF,1,3), //电芯温差过大 2级
    GET_ALM_ID(CLUSTER_SOC_LOW_PROTEC,1,1),//电池簇SOC低保护
    GET_ALM_ID(CLUSTER_SOH_LOW_PROTEC,1,1),//电池簇SOH低保护
    GET_ALM_ID(CLUSTER_ENV_TEMP_HIGH_PROTEC,1,1),//电池簇环境温度高保护
    GET_ALM_ID(CLUSTER_ENV_TEMP_LOW_PROTEC,1,1),//电池簇环境温度低保护
    GET_ALM_ID(BMU_CELL_DAMAGE_PROTEC,1,1),//BMU电芯损坏保护
    GET_ALM_ID(CLUSTER_INSULATION_RESIS_ALM,1,2),//绝缘电阻告警 2级
    GET_ALM_ID(CLUSTER_INSULATION_RESIS_ALM,1,3),//绝缘电阻告警 3级
    GET_ALM_ID(CLUSTER_CARBON_MONOXIDE_ALM,1,2),//一氧化碳浓度告警 2级
    GET_ALM_ID(CLUSTER_CARBON_MONOXIDE_ALM,1,3),//一氧化碳浓度告警 3级
    
    //第二簇
    GET_ALM_ID(CLUSTER_VOLT_HIGH,2,2),    //电芯过压告警 2级
    GET_ALM_ID(CLUSTER_VOLT_HIGH,2,3),    //电芯过压告警 3级
    GET_ALM_ID(CLUSTER_VOLT_LOW,2,2),     //电芯欠压告警 2级
    GET_ALM_ID(CLUSTER_VOLT_LOW,2,3),     //电芯欠压告警 3级
    GET_ALM_ID(CLUSTER_VOLT_DIFF_OVER,2,2),//电芯压差过大告警 2级
    GET_ALM_ID(CLUSTER_VOLT_DIFF_OVER,2,3),//电芯压差过大告警 3级
    GET_ALM_ID(CLUSTER_CHG_HIGH_TEMP,2,2), //电芯充电高温告警 2级
    GET_ALM_ID(CLUSTER_CHG_HIGH_TEMP,2,3), //电芯充电高温告警 3级
    GET_ALM_ID(CLUSTER_DISCHG_HIGH_TEMP,2,2), //电芯放电高温告警 2级
    GET_ALM_ID(CLUSTER_DISCHG_HIGH_TEMP,2,3), //电芯放电高温告警 3级
    GET_ALM_ID(CLUSTER_CHG_LOW_TEMP,2,2), //电芯充电低温告警 2级
    GET_ALM_ID(CLUSTER_CHG_LOW_TEMP,2,3), //电芯充电低温告警 3级
    GET_ALM_ID(CLUSTER_DISCHG_LOW_TEMP,2,2),//电芯放电低温告警 2级
    GET_ALM_ID(CLUSTER_DISCHG_LOW_TEMP,2,3),//电芯放电低温告警 3级
    GET_ALM_ID(CLUSTER_OVER_TEMP_DIFF,2,2), //电芯温差过大 2级
    GET_ALM_ID(CLUSTER_OVER_TEMP_DIFF,2,3), //电芯温差过大 2级
    GET_ALM_ID(CLUSTER_SOC_LOW_PROTEC,2,1),//电池簇SOC低保护
    GET_ALM_ID(CLUSTER_SOH_LOW_PROTEC,2,1),//电池簇SOH低保护
    GET_ALM_ID(CLUSTER_ENV_TEMP_HIGH_PROTEC,2,1),//电池簇环境温度高保护
    GET_ALM_ID(CLUSTER_ENV_TEMP_LOW_PROTEC,2,1),//电池簇环境温度低保护
    GET_ALM_ID(BMU_CELL_DAMAGE_PROTEC,2,1),//BMU电芯损坏保护
    GET_ALM_ID(CLUSTER_INSULATION_RESIS_ALM,2,2),//绝缘电阻告警 2级
    GET_ALM_ID(CLUSTER_INSULATION_RESIS_ALM,2,3),//绝缘电阻告警 3级
    GET_ALM_ID(CLUSTER_CARBON_MONOXIDE_ALM,2,2),//一氧化碳浓度告警 2级
    GET_ALM_ID(CLUSTER_CARBON_MONOXIDE_ALM,2,3),//一氧化碳浓度告警 3级
    
    //第三簇
    GET_ALM_ID(CLUSTER_VOLT_HIGH,3,2),    //电芯过压告警 2级
    GET_ALM_ID(CLUSTER_VOLT_HIGH,3,3),    //电芯过压告警 3级
    GET_ALM_ID(CLUSTER_VOLT_LOW,3,2),     //电芯欠压告警 2级
    GET_ALM_ID(CLUSTER_VOLT_LOW,3,3),     //电芯欠压告警 3级
    GET_ALM_ID(CLUSTER_VOLT_DIFF_OVER,3,2),//电芯压差过大告警 2级
    GET_ALM_ID(CLUSTER_VOLT_DIFF_OVER,3,3),//电芯压差过大告警 3级
    GET_ALM_ID(CLUSTER_CHG_HIGH_TEMP,3,2), //电芯充电高温告警 2级
    GET_ALM_ID(CLUSTER_CHG_HIGH_TEMP,3,3), //电芯充电高温告警 3级
    GET_ALM_ID(CLUSTER_DISCHG_HIGH_TEMP,3,2), //电芯放电高温告警 2级
    GET_ALM_ID(CLUSTER_DISCHG_HIGH_TEMP,3,3), //电芯放电高温告警 3级
    GET_ALM_ID(CLUSTER_CHG_LOW_TEMP,3,2), //电芯充电低温告警 2级
    GET_ALM_ID(CLUSTER_CHG_LOW_TEMP,3,3), //电芯充电低温告警 3级
    GET_ALM_ID(CLUSTER_DISCHG_LOW_TEMP,3,2),//电芯放电低温告警 2级
    GET_ALM_ID(CLUSTER_DISCHG_LOW_TEMP,3,3),//电芯放电低温告警 3级
    GET_ALM_ID(CLUSTER_OVER_TEMP_DIFF,3,2), //电芯温差过大 2级
    GET_ALM_ID(CLUSTER_OVER_TEMP_DIFF,3,2), //电芯温差过大 2级
    GET_ALM_ID(CLUSTER_SOC_LOW_PROTEC,3,1),//电池簇SOC低保护
    GET_ALM_ID(CLUSTER_SOH_LOW_PROTEC,3,1),//电池簇SOH低保护
    GET_ALM_ID(CLUSTER_ENV_TEMP_HIGH_PROTEC,3,1),//电池簇环境温度高保护
    GET_ALM_ID(CLUSTER_ENV_TEMP_LOW_PROTEC,3,1),//电池簇环境温度低保护
    GET_ALM_ID(BMU_CELL_DAMAGE_PROTEC,3,1),//BMU电芯损坏保护
    GET_ALM_ID(CLUSTER_INSULATION_RESIS_ALM,3,2),//绝缘电阻告警 2级
    GET_ALM_ID(CLUSTER_INSULATION_RESIS_ALM,3,3),//绝缘电阻告警 3级
    GET_ALM_ID(CLUSTER_CARBON_MONOXIDE_ALM,3,2),//一氧化碳浓度告警 2级
    GET_ALM_ID(CLUSTER_CARBON_MONOXIDE_ALM,3,3),//一氧化碳浓度告警 3级
    
    //第四簇
    GET_ALM_ID(CLUSTER_VOLT_HIGH,4,2),    //电芯过压告警 2级
    GET_ALM_ID(CLUSTER_VOLT_HIGH,4,3),    //电芯过压告警 3级
    GET_ALM_ID(CLUSTER_VOLT_LOW,4,2),     //电芯欠压告警 2级
    GET_ALM_ID(CLUSTER_VOLT_LOW,4,3),     //电芯欠压告警 3级
    GET_ALM_ID(CLUSTER_VOLT_DIFF_OVER,4,2),//电芯压差过大告警 2级
    GET_ALM_ID(CLUSTER_VOLT_DIFF_OVER,4,3),//电芯压差过大告警 3级
    GET_ALM_ID(CLUSTER_CHG_HIGH_TEMP,4,2), //电芯充电高温告警 2级
    GET_ALM_ID(CLUSTER_CHG_HIGH_TEMP,4,3), //电芯充电高温告警 3级
    GET_ALM_ID(CLUSTER_DISCHG_HIGH_TEMP,4,2), //电芯放电高温告警 2级
    GET_ALM_ID(CLUSTER_DISCHG_HIGH_TEMP,4,3), //电芯放电高温告警 3级
    GET_ALM_ID(CLUSTER_CHG_LOW_TEMP,4,2), //电芯充电低温告警 2级
    GET_ALM_ID(CLUSTER_CHG_LOW_TEMP,4,3), //电芯充电低温告警 3级
    GET_ALM_ID(CLUSTER_DISCHG_LOW_TEMP,4,2),//电芯放电低温告警 2级
    GET_ALM_ID(CLUSTER_DISCHG_LOW_TEMP,4,3),//电芯放电低温告警 3级
    GET_ALM_ID(CLUSTER_OVER_TEMP_DIFF,4,2), //电芯温差过大 2级
    GET_ALM_ID(CLUSTER_OVER_TEMP_DIFF,4,3), //电芯温差过大 3级
    GET_ALM_ID(CLUSTER_SOC_LOW_PROTEC,4,1),//电池簇SOC低保护
    GET_ALM_ID(CLUSTER_SOH_LOW_PROTEC,4,1),//电池簇SOH低保护
    GET_ALM_ID(CLUSTER_ENV_TEMP_HIGH_PROTEC,4,1),//电池簇环境温度高保护
    GET_ALM_ID(CLUSTER_ENV_TEMP_LOW_PROTEC,4,1),//电池簇环境温度低保护
    GET_ALM_ID(BMU_CELL_DAMAGE_PROTEC,4,1),//BMU电芯损坏保护
    GET_ALM_ID(CLUSTER_INSULATION_RESIS_ALM,4,2),//绝缘电阻告警 2级
    GET_ALM_ID(CLUSTER_INSULATION_RESIS_ALM,4,3),//绝缘电阻告警 3级
    GET_ALM_ID(CLUSTER_CARBON_MONOXIDE_ALM,4,2),//一氧化碳浓度告警 2级
    GET_ALM_ID(CLUSTER_CARBON_MONOXIDE_ALM,4,3),//一氧化碳浓度告警 3级
    

    //BMU通信断
    GET_ALM_ID(BMU_COMM_FAIL,1,1), //第一簇BMU通信断
    GET_ALM_ID(BMU_COMM_FAIL,2,1), //第二簇BMU通信断
    GET_ALM_ID(BMU_COMM_FAIL,3,1), //第三簇BMU通信断
    GET_ALM_ID(BMU_COMM_FAIL,4,1), //第四簇BMU通信断

    //簇控制器通信断
    GET_ALM_ID(CLUSTER_CAN_COMM_FAULT,1,1), //第一簇通信断
    GET_ALM_ID(CLUSTER_CAN_COMM_FAULT,2,1), //第二簇通信断
    GET_ALM_ID(CLUSTER_CAN_COMM_FAULT,3,1), //第三簇通信断
    GET_ALM_ID(CLUSTER_CAN_COMM_FAULT,4,1), //第四簇通信断 

    //柜绝缘检测状态
    GET_ALM_ID(BCMU_INSULATION_STATUS_FAIL,1,1),  
    
    //柜消防状态
    GET_ALM_ID(BCMU_FIRE_STATUS,1,1),  

    //柜消防状态
    GET_ALM_ID(BCMU_ALARM_EPO,1,1), 
};

char bcmu_power_on_check(void) {
    return power_on_check(bcmu_power_on_check_item,sizeof(bcmu_power_on_check_item)/sizeof(bcmu_power_on_check_item[0]));
}