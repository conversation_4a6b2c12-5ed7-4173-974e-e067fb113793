#ifndef BATTSLEEP_H_
#define BATTSLEEP_H_
#include "battery.h"

#define DISCHG_VOLT_CHANGE_TIME 30 // 输出电压增加1V持续时间

#define SLEEP_MODE_UNUSED           0
#define SLEEP_MODE_OFFLINE          1
#define SLEEP_MODE_BUTTON           2
#define SLEEP_MODE_UPDATE           3
#define SLEEP_MODE_IDLE             4
#define SLEEP_MODE_SHUTDOWN_UVP     5
#define SLEEP_MODE_SHUTDOWN_LOOPOFF 6
#define SLEEP_MODE_IRREVESB_ALARM   7

typedef enum
{
    QUITSAVEMODE = 0,
    ENTERSAVEMODE,
    QUITQUIETSLEEP,
} SleepContrl;

//进入休眠的原因
typedef enum
{
    UV_PRT_SLEEP = 0,
    QUIET_SLEEP,
    ENTER_SLEEP_MAX,
} Enum_Enter_Sleep_Reason;

//退出静置休眠的原因
typedef enum
{
    MASTER_CTRL = 0,//主机遥控
    SYS_POWER_ON,//系统来电
    QUIT_QUIET_SLEEP_MAX,
} Enum_Quit_Quiet_Reason;

void SleepMgr(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
BOOLEAN GetSlaveQuietSleepStatus(void);
void QuitQuietSleep(Enum_Quit_Quiet_Reason quitReason);
void RecovVoltChangeFlag(void);
BOOLEAN BattSleepUnitest(BOOLEAN bFlag);
BOOLEAN NeedPlusOneOutVol(void);
BYTE GetSleepMgrCase(void);
BOOLEAN getbChgTempLowPrtOnly(void);
#endif