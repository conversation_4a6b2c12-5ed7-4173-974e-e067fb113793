#include <stdio.h>
#include <string.h>
#include <math.h>
#include <rtthread.h>
#include "dev_dc_ac_parallel_modbus.h"
#include "msg_id.h"
#include "msg.h"
#include "utils_data_transmission.h"
#include "realdata_id_in.h"
#include "para_id_in.h"
#include "partition_def.h"
#include "storage.h"
#include "realdata_save.h"
#include "north_parallel_modbus.h"
#include "sps.h"
#include "unified_id_interface.h"
static signed short g_reg_addr = 0;
static signed short g_reg_nums = 0;


#define PARALLEL_MODBUS_DEBUG 0

/* 函数声明 */

/* 命令请求头 */
static modbusrtu_cmd_head_t cmd_req[] RAM_SECTION = {
    {GET_ANA_DATA},
    {SET_PARA_DATA},
    {SET_CTRL_CMD},
    {GET_PARA_DATA},
};

/* 命令应答头 */
static modbusrtu_cmd_head_t cmd_ack[] RAM_SECTION = {
    {GET_ANA_DATA},
    {SET_PARA_DATA},
    {SET_CTRL_CMD},
    {GET_PARA_DATA},
};

static cmd_id_to_register_info_t cmd_id_to_register_info[] RAM_SECTION = {
    {CMD_INVERTER_PARALLEL_MODBUS_SET_ONE_PARA, 0x10, 2075, 1},
    {CMD_INVERTER_PARALLEL_MODBUS_SET_PARA_1, 0x10, 2075, 15},
    {CMD_INVERTER_PARALLEL_MODBUS_SET_PARA_2, 0x10, 2100, 12},
    {CMD_INVERTER_PARALLEL_MODBUS_SET_PARA_3, 0x10, 2270, 13},
    {CMD_INVERTER_PARALLEL_MODBUS_CTRL_IV_SCAN_START, 0x06, 3500, 1},
    {CMD_INVERTER_PARALLEL_MODBUS_CTRL_IV_SCAN_STOP, 0x06, 3501, 1},
    {CMD_INVERTER_PARALLEL_MODBUS_CTRL_RESTART_SYSTEM, 0x06, 3504, 1},
    {CMD_INVERTER_PARALLEL_MODBUS_CTRL_START_CHECK_ISOLATE, 0x06, 3505, 1},
    {CMD_INVERTER_PARALLEL_MODBUS_CTRL_START_CHECK_AFCI   , 0x06, 3506, 1},
    {CMD_INVERTER_PARALLEL_MODBUS_GET_FACTORY, 0x04, 1500, 116},
    {CMD_INVERTER_PARALLEL_MODBUS_GET_ANALOG_1, 0x04, 1000, 120},
    {CMD_INVERTER_PARALLEL_MODBUS_GET_ANALOG_2, 0x04, 1295, 106},
    {CMD_INVERTER_PARALLEL_MODBUS_GET_DIGIT, 0x04, 1200, 5},
    {CMD_INVERTER_PARALLEL_MODBUS_GET_PARA_1, 0x3, 2075, 15},
    {CMD_INVERTER_PARALLEL_MODBUS_GET_PARA_2, 0x3, 2100, 12},
    {CMD_INVERTER_PARALLEL_MODBUS_GET_PARA_3, 0x3, 2270, 13},
    {0,}
};

//解析状态量寄存器地址：1200-1204
static data_info_id_verison_t pack_dig_data_info[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},//数据的字节长度

    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_DEV_STATUS},//寄存器地址: 1200, 状态量1
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_UPDATE_STATUS},//1201
    {SEG_DATA, type_bit, ARRAY_SIZE_3, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT, },
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PID_REPAIR_STATUS},//寄存器地址:1202, pid修复状态
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_INSULATION_IMPEDANCE_LOW_POSITION_STATE},//寄存器地址:1202, 绝缘阻抗低定位状态
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_ALARM_TOTAL_SIGNAL},//寄存器地址: 1202, 告警总信号
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_ACCI_TOTAL_SIGNAL},//寄存器地址: 1202, 事故总信号
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_IV_COMPLETE_STATUS},//寄存器地址: 1202, IV扫描完成状态

    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_POWER_ON_OFF_STA},//寄存器地址: 1202, 开关机状态
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_OUT_RELAY_STATUS},//寄存器地址: 1202, 输出干接点状态1
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_RELAY_STATUS + 4},//寄存器地址: 1202, 输入干接点状态5
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_RELAY_STATUS + 3},//寄存器地址: 1202, 输入干接点状态4
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_RELAY_STATUS + 2},//寄存器地址: 1202, 输入干接点状态3
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_RELAY_STATUS + 1},//寄存器地址: 1202, 输入干接点状态2
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_RELAY_STATUS},//寄存器地址: 1202, 输入干接点状态1
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_INPUT_UNDERVOLT_STA},//寄存器地址: 1202, 输入欠压状态

    {SEG_DATA, type_bit, ARRAY_SIZE_4, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT, },//寄存器地址: 1203, 预留
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MPPT_CURRENT_LIMITING_STATUS + 3},//寄存器地址: 1203, mppt4限流状态
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MPPT_CURRENT_LIMITING_STATUS + 2},//寄存器地址: 1203, mppt3限流状态
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MPPT_CURRENT_LIMITING_STATUS + 1},//寄存器地址: 1203, mppt2限流状态
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MPPT_CURRENT_LIMITING_STATUS},    //寄存器地址: 1203, mppt1限流状态

    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_SCAN_STATUS + 7},//寄存器地址: 1203, PV8扫描状态
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_SCAN_STATUS + 6},//寄存器地址: 1203, PV7扫描状态
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_SCAN_STATUS + 5},//寄存器地址: 1203, PV6扫描状态
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_SCAN_STATUS + 4},//寄存器地址: 1203, PV5扫描状态
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_SCAN_STATUS + 3},//寄存器地址: 1203, PV4扫描状态
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_SCAN_STATUS + 2},//寄存器地址: 1203, PV3扫描状态
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_SCAN_STATUS + 1},//寄存器地址: 1203, PV2扫描状态
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_SCAN_STATUS},//寄存器地址: 1203, PV1扫描状态

    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_ALTITUDE_LOAD_SHEDDING},//寄存器地址: 1204,海拔降载
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_GRID_VOLTAGE_LOAD_SHEDDING},//寄存器地址: 1204,电网电压降载
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PCB_TEMPERATURE_LOAD_SHEDDING},//寄存器地址: 1204,pcb温度降载
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_INTERNAL_TEMPERATURE_LOAD_SHEDDING},//寄存器地址: 1204,机内温度降载
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_RADIATOR_TEMPERATURE_LOAD_SHEDDING},//寄存器地址: 1204,散热器温度降载
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MODULATION_INDEX_LOAD_SHEDDING},//寄存器地址: 1204,调制比降载
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_DC_INPUT_LOAD_SHEDDING},//寄存器地址: 1204,直流输入降载
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT, },//寄存器地址: 1204, 预留

    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CONNECTED_STATUS + 7},//寄存器地址: 1204, PV8连接状态
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CONNECTED_STATUS + 6},//寄存器地址: 1204, PV7连接状态
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CONNECTED_STATUS + 5},//寄存器地址: 1204, PV6连接状态
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CONNECTED_STATUS + 4},//寄存器地址: 1204, PV5连接状态
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CONNECTED_STATUS + 3},//寄存器地址: 1204, PV4连接状态
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CONNECTED_STATUS + 2},//寄存器地址: 1204, PV3连接状态
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CONNECTED_STATUS + 1},//寄存器地址: 1204, PV2连接状态
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CONNECTED_STATUS},//寄存器地址: 1204, PV1连接状态
};
#define DIG_DATA_NUM  (sizeof(pack_dig_data_info) / sizeof(data_info_id_verison_t))

//解析模拟量2寄存器地址：1295-1385
static data_info_id_verison_t pack_ana_data_info_2[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},//数据的字节长度
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_FIRST_STARTUP_TIME_YEAR},//寄存器地址: 1401, 首次启动时间-年
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_FIRST_STARTUP_TIME_MONTH},//寄存器地址: 1401, 首次启动时间-月
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_FIRST_STARTUP_TIME_DAY},//寄存器地址: 1401, 首次启动时间-日
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT, },//寄存器地址: 1298, 预留
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_NET_CONNECT_MODE},//寄存器地址: 1299, 网管组网方式
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_GRID_PHASE_VOLT},//寄存器地址: 1300, 电网A相电压
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_GRID_PHASE_VOLT + 1},//寄存器地址: 1301, 电网B相电压
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_GRID_PHASE_VOLT + 2},//寄存器地址: 1302, 电网C相电压
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_AB_VOLT},//寄存器地址: 1303, 逆变器Uab
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_BC_VOLT},//寄存器地址: 1304, 逆变器Ubc
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CA_VOLT},//寄存器地址: 1305, 逆变器Uca
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_PHASE_VOLT},//寄存器地址: 1306, 逆变器A相电压
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_PHASE_VOLT + 1},//寄存器地址: 1307, 逆变器B相电压
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_PHASE_VOLT + 2},//寄存器地址: 1308, 逆变器C相电压
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT, },//寄存器地址: 1309, 预留
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_INTERNAL_TEMPER},//寄存器地址: 1310, 机内温度1
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_BUS_VOLT},//寄存器地址: 1311, 母线电压
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_POS_BUS_VOLT},//寄存器地址: 1312, 正母线电压
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_NEG_BUS_VOLT},//寄存器地址: 1313, 负母线电压
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_REDIATOR_TEMPER},//寄存器地址: 1314, 散热器1温度
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_REDIATOR_TEMPER + 1},//寄存器地址: 1315, 散热器2温度
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_REDIATOR_TEMPER + 2},//寄存器地址: 1316, 散热器3温度
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT, },//寄存器地址: 1317, 散热器4温度（预留）
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT, },//寄存器地址: 1318, 散热器5温度（预留）

    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_RES_CURR_AC_COMPONENT},//寄存器地址: 1319	残余电流交流分量
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_RES_CURR_DC_COMPONENT},//寄存器地址: 1320	残余电流直流分量
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PCB_TEMPER},//寄存器地址: 1321	PCB温度
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_0,DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_ACTIVE_POWER_CONTROL_MODE_CURR},//寄存器地址: 1322	有功控制模式
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_ACTIVE_POWER_DERATING_SETTING_CURR},//寄存器地址: 1323	有功降额设置值（固定值）
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_ACTIVE_POWER_DERATING_PERCENT_CURR},//寄存器地址: 1325	有功降额百分比
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_0,DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_REACTIVE_POWER_COMPEN_MODE_CURR},//寄存器地址: 1326	无功补偿模式
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_REACTIVE_POWER_COMPEN_SETTING_FACTOR_CURR},//寄存器地址: 1327	无功补偿设置（功率因素）
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_REACTIVE_POWER_COMPEN_SETTING_CURR},//寄存器地址: 1328	无功补偿设置（Q/S）
    {SEG_DATA, type_string, ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PSC_VERSION_CURR},//寄存器地址: 1329	PSC版本信息
    {SEG_DATA, type_string, ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CSC_VERSION_CURR},//寄存器地址: 1345	CSC版本信息
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CURR_DC_COMPONENT},//寄存器地址: 1361 逆变A相电流直流分量
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CURR_DC_COMPONENT + 1},//寄存器地址: 1362 逆变A相电流直流分量
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CURR_DC_COMPONENT + 2},//寄存器地址: 1363 逆变A相电流直流分量
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CURR_AVERAGE_VALUE},    //寄存器地址: 1364 A相电流平均值
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CURR_AVERAGE_VALUE + 1},//寄存器地址: 1365 A相电流平均值
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CURR_AVERAGE_VALUE + 2},//寄存器地址: 1366 A相电流平均值
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_DELAY_UPDATE_STATUS},//寄存器地址: 1367 延迟升级状态
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_OVERSAMPLE_CURRENT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_OVERSAMPLE_CURRENT + 1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_OVERSAMPLE_CURRENT + 2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_OVERSAMPLE_CURRENT + 3},
    {SEG_DATA, type_string, ARRAY_SIZE_8, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MASTER_CTRL_PROTOCOL_VERSION},//寄存器地址: 1372 主控协议版本
    {SEG_DATA, type_string, ARRAY_SIZE_8, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_SLAVE_CTRL_PROTOCOL_VERSION },//寄存器地址: 1376 辅控协议版本
    {SEG_DATA, type_string, ARRAY_SIZE_8, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CPLD_PROTOCOL_VERSION       },//寄存器地址: 1380 cpld协议版本
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_FAULT_STRING_WITH_LOW_INSULAT_IMPEDANCE},//寄存器地址: 1384 绝缘阻抗低可能故障组串
    {SEG_DATA, type_string, ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PRODUCT_MODEL},//寄存器地址: 1385 产品型号
    {SEG_DATA, type_string, ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_POWER_HARDWARE_VERSION},//寄存器地址: 1401 硬件版本
};
#define ANA_DATA_INFO2_NUM  (sizeof(pack_ana_data_info_2) / sizeof(data_info_id_verison_t))

//解析厂家信息 寄存器1500--1627
static data_info_id_verison_t pack_fac_data_info[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},//数据的字节长度
    {SEG_DATA, type_string, ARRAY_SIZE_12, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MASTER_CTRL_SOFTWARE_VER},//寄存器地址: 1500, 主控软件版本
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MASTER_CTRL_SOFTWARE_VER_YEAR},//寄存器地址: 1506, 主控版本日期-年
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MASTER_CTRL_SOFTWARE_VER_MONTH},//寄存器地址: 1507, 主控版本日期-月
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MASTER_CTRL_SOFTWARE_VER_DAY},//寄存器地址: 1508, 主控版本日期-日
    {SEG_DATA, type_string, ARRAY_SIZE_12, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_AUXI_CTRL_SOFTWARE_VER},//寄存器地址: 1509, 辅控软件版本
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_AUXI_CTRL_SOFTWARE_VER_YEAR},//寄存器地址: 1515, 辅控软件版本日期-年
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_AUXI_CTRL_SOFTWARE_VER_MONTH},//寄存器地址: 1516, 辅控软件版本日期-月
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_AUXI_CTRL_SOFTWARE_VER_DAY},//寄存器地址: 1517, 辅控软件版本日期-日
    {SEG_DATA, type_string, ARRAY_SIZE_12, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CPLD_SOFTWARE_VER},//寄存器地址: 1518, CPLD软件版本
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CPLD_SOFTWARE_VER_YEAR},//寄存器地址: 1524, CPLD软件版本日期-年
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CPLD_SOFTWARE_VER_MONTH},//寄存器地址: 1525, CPLD软件版本日期-月
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CPLD_SOFTWARE_VER_DAY},//寄存器地址: 1526， CPLD软件版本日期-日
    {SEG_DATA, type_string, ARRAY_SIZE_20, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MONITOR_SOFTWARE_VER},//寄存器地址: 1527, 监控软件版本
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MONITOR_SOFTWARE_VER_YEAR},//寄存器地址: 1537, 监控版本日期-年
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MONITOR_SOFTWARE_VER_MONTH},//寄存器地址: 1538, 监控版本日期-月
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MONITOR_SOFTWARE_VER_DAY},//寄存器地址: 1539, 监控版本日期-日
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MANUFACTURER_ID},//寄存器地址: 1540, 制造商ID
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MANUFACTURER_ADDRESS},//寄存器地址: 1541, 制造商地址
    {SEG_DATA, type_string, ARRAY_SIZE_12, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_PARA_ID_MACHINE_BARCODE},//寄存器地址: 1542, 整机序列号
    {SEG_DATA, type_string, ARRAY_SIZE_16, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT, },//寄存器地址: 1548,预留
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT, },//寄存器地址: 1556, 预留
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MANUFACTURER_YEAR},//寄存器地址: 1557, 生产日期-年
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MANUFACTURER_MONTH},//寄存器地址: 1558, 生产日期-月
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MANUFACTURER_DAY},//寄存器地址: 1559, 生产日期-日
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_RATED_POWER_FAC_PARAM},//寄存器地址: 1560, 额定功率（Pn）（厂家信息）
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MAX_ACTIVE_POWER_FAC_PARAM},//寄存器地址: 1562, 最大有功功率（Pmax）（厂家信息）
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MAX_APPARENT_POWER_FAC_PARAM},//寄存器地址: 1564, 最大视在功率（Smax）（厂家信息）
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MAX_REACTIVE_POWER_FAC_PARAM},//寄存器地址: 1566, 最大无功功率（Qmax）（厂家信息）

    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CA_CERT_START_YEAR_TIME},  // 网管CA证书开始时间年1568
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CA_CERT_START_MONTH_TIME}, // 网管CA证书开始时间月1569
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CA_CERT_START_DAY_TIME },  // 网管CA证书开始时间日1570
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CA_CERT_START_HOUR_TIME},  // 网管CA证书开始时间时1571
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CA_CERT_START_MINUTE_TIME},// 网管CA证书开始时间分1572
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CA_CERT_START_SECOND_TIME},// 网管CA证书开始时间秒1573
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CA_CERT_END_YEAR_TIME},  // 网管CA证书结束时间年1574
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CA_CERT_END_MONTH_TIME}, // 网管CA证书结束时间月1575
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CA_CERT_END_DAY_TIME },  // 网管CA证书结束时间日1576
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CA_CERT_END_HOUR_TIME},  // 网管CA证书结束时间时1577
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CA_CERT_END_MINUTE_TIME},// 网管CA证书结束时间分1578
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CA_CERT_END_SECOND_TIME},// 网管CA证书结束时间秒1579
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CLIENT_CERT_START_YEAR_TIME  },  // 网管证书客户端开始时间年1580
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CLIENT_CERT_START_MONTH_TIME },  // 网管证书客户端开始时间月1581
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CLIENT_CERT_START_DAY_TIME   },  // 网管证书客户端开始时间日1582
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CLIENT_CERT_START_HOUR_TIME  },  // 网管证书客户端开始时间时1583
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CLIENT_CERT_START_MINUTE_TIME},  // 网管证书客户端开始时间分1584
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CLIENT_CERT_START_SECOND_TIME},  // 网管证书客户端开始时间秒1585
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CLIENT_CERT_END_YEAR_TIME  },  // 网管证书客户端结束时间年1586
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CLIENT_CERT_END_MONTH_TIME },  // 网管证书客户端结束时间月1587
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CLIENT_CERT_END_DAY_TIME   },  // 网管证书客户端结束时间日1588
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CLIENT_CERT_END_HOUR_TIME  },  // 网管证书客户端结束时间时1589
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CLIENT_CERT_END_MINUTE_TIME},  // 网管证书客户端结束时间分1590
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CLIENT_CERT_END_SECOND_TIME},  // 网管证书客户端结束时间秒1591
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CA_CERT_START_YEAR_TIME + 1}, //第三方网管CA证书开始时间年1592
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CA_CERT_START_MONTH_TIME+ 1}, //第三方网管CA证书开始时间月1593
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CA_CERT_START_DAY_TIME  + 1}, //第三方网管CA证书开始时间日1594
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CA_CERT_START_HOUR_TIME + 1}, //第三方网管CA证书开始时间时1595
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CA_CERT_START_MINUTE_TIME+ 1},//第三方网管CA证书开始时间分1596
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CA_CERT_START_SECOND_TIME+ 1},//第三方网管CA证书开始时间秒1597
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CA_CERT_END_YEAR_TIME+ 1},  //第三方网管CA证书结束时间年1598
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CA_CERT_END_MONTH_TIME+ 1}, //第三方网管CA证书结束时间月1599
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CA_CERT_END_DAY_TIME  + 1}, //第三方网管CA证书结束时间日1600
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CA_CERT_END_HOUR_TIME+ 1},  //第三方网管CA证书结束时间时1601
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CA_CERT_END_MINUTE_TIME+ 1},//第三方网管CA证书结束时间分1602
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CA_CERT_END_SECOND_TIME+ 1},//第三方网管CA证书结束时间秒1603
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CLIENT_CERT_START_YEAR_TIME  + 1},  //第三方网管证书客户端开始时间年1604
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CLIENT_CERT_START_MONTH_TIME + 1},  //第三方网管证书客户端开始时间月1605
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CLIENT_CERT_START_DAY_TIME   + 1},  //第三方网管证书客户端开始时间日1606
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CLIENT_CERT_START_HOUR_TIME  + 1},  //第三方网管证书客户端开始时间时1607
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CLIENT_CERT_START_MINUTE_TIME+ 1},  //第三方网管证书客户端开始时间分1608
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CLIENT_CERT_START_SECOND_TIME+ 1},  //第三方网管证书客户端开始时间秒1609
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CLIENT_CERT_END_YEAR_TIME  + 1},  //第三方网管证书客户端结束时间年1610
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CLIENT_CERT_END_MONTH_TIME + 1},  //第三方网管证书客户端结束时间月1611
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CLIENT_CERT_END_DAY_TIME   + 1},  //第三方网管证书客户端结束时间日1612
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CLIENT_CERT_END_HOUR_TIME  + 1},  //第三方网管证书客户端结束时间时1613
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CLIENT_CERT_END_MINUTE_TIME+ 1},  //第三方网管证书客户端结束时间分1614
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CLIENT_CERT_END_SECOND_TIME+ 1},  //第三方网管证书客户端结束时间秒1615
    {SEG_DATA, type_string, ARRAY_SIZE_16, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MONITOR_BOOT_VER},             //监控BOOT版本1616
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MONITOR_BOOT_VER_YEAR}, //监控BOOT版本日期-年1624
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MONITOR_BOOT_VER_MONTH},//监控BOOT版本日期-月1625
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MONITOR_BOOT_VER_DAY},  //监控BOOT版本日期-日1626
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_USER_ACCOUNT_REMAIN_VALID_TIME},  //user账号剩余有效时间1627
};
#define FACT_DATA_INFO_NUM  (sizeof(pack_fac_data_info) / sizeof(data_info_id_verison_t))

//解析模拟量1寄存器地址：1000——1119
static data_info_id_verison_t pack_ana_data_info_1[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},//数据的字节长度
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MPPT_TOTAL_INPUT_POWER},//寄存器地址: 1000, MPPT1总输入功率
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MPPT_VOLT},//寄存器地址: 1002, MPPT1电压
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MPPT_CURR},//寄存器地址: 1003, MPPT1电流
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MPPT_TOTAL_INPUT_POWER + 1},//寄存器地址: 1004, MPPT2总输入功率
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MPPT_VOLT + 1},//寄存器地址: 1006, MPPT2电压
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MPPT_CURR + 1},//寄存器地址: 1007, MPPT2电流
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MPPT_TOTAL_INPUT_POWER + 2},//寄存器地址: 1008, MPPT3总输入功率
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MPPT_VOLT + 2},//寄存器地址: 1010, MPPT3电压
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MPPT_CURR + 2},//寄存器地址: 1011, MPPT3电流
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MPPT_TOTAL_INPUT_POWER + 3},//寄存器地址: 1012, MPPT4总输入功率
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MPPT_VOLT + 3},//寄存器地址: 1014, MPPT4电压
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MPPT_CURR + 3},//寄存器地址: 1015, MPPT4电流
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MPPT_TOTAL_INPUT_POWER + 4},//寄存器地址: 1016, MPPT5总输入功率
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MPPT_VOLT + 4},//寄存器地址: 1018, MPPT5电压（预留）
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MPPT_CURR + 4},//寄存器地址: 1019, MPPT5电流（预留）
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MPPT_TOTAL_INPUT_POWER + 5},//寄存器地址: 1020, MPPT6总输入功率
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MPPT_VOLT + 5},//寄存器地址: 1022, MPPT6电压（预留）
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MPPT_CURR + 5},//寄存器地址: 1023, MPPT6电流（预留）
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MPPT_TOTAL_INPUT_POWER + 6},//寄存器地址: 1024, MPPT7总输入功率
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MPPT_VOLT + 6},//寄存器地址: 1026, MPPT7电压（预留）
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MPPT_CURR + 6},//寄存器地址: 1027, MPPT7电流（预留）
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MPPT_TOTAL_INPUT_POWER + 7},//寄存器地址: 1028, MPPT8总输入功率
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MPPT_VOLT + 7},//寄存器地址: 1030, MPPT8电压（预留）
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MPPT_CURR + 7},//寄存器地址: 1031, MPPT8电流（预留）
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MPPT_TOTAL_INPUT_POWER + 8},//寄存器地址: 1032, MPPT9总输入功率
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MPPT_VOLT + 8},//寄存器地址: 1034, MPPT9电压（预留）
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MPPT_CURR + 8},//寄存器地址: 1035, MPPT9电流（预留）
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_VOLT},//寄存器地址: 1036, PV1电压
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CURR},//寄存器地址: 1037, PV1电流
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_VOLT + 1},//寄存器地址: 1038, PV2电压
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CURR + 1},//寄存器地址: 1039, PV2电流
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_VOLT + 2},//寄存器地址: 1040, PV3电压
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CURR + 2},//寄存器地址: 1041, PV3电流
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_VOLT + 3},//寄存器地址: 1042, PV4电压
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CURR + 3},//寄存器地址: 1043, PV4电流
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_VOLT + 4},//寄存器地址: 1044, PV5电压
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CURR + 4},//寄存器地址: 1045, PV5电流
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_VOLT + 5},//寄存器地址: 1046, PV6电压
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CURR + 5},//寄存器地址: 1047, PV6电流
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_VOLT + 6},//寄存器地址: 1048, PV8电压
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CURR + 6},//寄存器地址: 1049, PV8电流
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_VOLT + 7},//寄存器地址: 1050, PV8电压
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CURR + 7},//寄存器地址: 1051, PV8电流
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_VOLT + 8},//寄存器地址: 1052, PV9电压（预留）
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CURR + 8},//寄存器地址: 1053, PV9电流（预留）
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_VOLT + 9},//寄存器地址: 1054, PV10电压（预留）
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CURR + 9},//寄存器地址: 1055, PV10电流（预留）
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_VOLT + 10},//寄存器地址: 1056, PV11电压（预留）
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CURR + 10},//寄存器地址: 1057, PV11电流（预留）
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_VOLT + 11},//寄存器地址: 1058, PV12电压（预留）
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CURR + 11},//寄存器地址: 1059, PV12电流（预留）
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_VOLT + 12},//寄存器地址: 1060, PV13电压（预留）
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CURR + 12},//寄存器地址: 1061, PV13电流（预留）
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_VOLT + 13},//寄存器地址: 1062, PV14电压（预留）
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CURR + 13},//寄存器地址: 1063, PV14电流（预留）
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_VOLT + 14},//寄存器地址: 1064, PV15电压（预留）
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CURR + 14},//寄存器地址: 1065, PV15电流（预留）
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_VOLT + 15},//寄存器地址: 1066, PV16电压（预留）
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CURR + 15},//寄存器地址: 1067, PV16电流（预留）
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_VOLT + 16},//寄存器地址: 1068, PV17电压（预留）
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CURR + 16},//寄存器地址: 1069, PV17电流（预留）
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_VOLT + 17},//寄存器地址: 1070, PV18电压（预留）
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_CURR + 17},//寄存器地址: 1071, PV18电流（预留）
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_ACTIVE_POWER},//寄存器地址: 1072, 有功功率
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_REACTIVE_POWER},//寄存器地址: 1074, 无功功率
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_APPARENT_POWER},//寄存器地址: 1076, 视在功率
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_POWER_FACTOR},//寄存器地址: 1078, 功率因数
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_GRID_FREQ},//寄存器地址: 1079, 电网频率
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_GRID_AB_VOLT},//寄存器地址: 1080, 电网Uab
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_GRID_BC_VOLT},//寄存器地址: 1081, 电网Ubc
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_GRID_CA_VOLT},//寄存器地址: 1082, 电网Uca
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_GRID_PHASE_CURR},//寄存器地址: 1083, A相电流
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_GRID_PHASE_CURR + 1},//寄存器地址: 1084, B相电流
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_GRID_PHASE_CURR + 2},//寄存器地址: 1085, C相电流
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_TOTAL_INPUT_POWER},//寄存器地址: 1086, 总输入功率
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_POS_INSULATION_IMPERD},//寄存器地址: 1088, 绝缘阻抗
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_EFFICIENCY},//寄存器地址: 1089, 逆变器效率
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_PV_NET_NUMBER},//寄存器地址: 1090, PV组串数量
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MPPT_NUMBER},//寄存器地址: 1091, MPPT数量
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_RATED_NUMBER},//寄存器地址: 1092, 额定功率（Pn）
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MAX_ACTIVE_POWER},//寄存器地址: 1094, 最大有功功率（Pmax）
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MAX_APPARENT_POWER},//寄存器地址: 1096, 最大视在功率（Smax）
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MAX_REACTIVE_POWER},//寄存器地址: 1098, 最大无功功率（Qmax）
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_DAILY_POWER_GENERATION},//寄存器地址: 1100, 日发电量
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_MONTH_POWER_GENERATION},//寄存器地址: 1102, 月发电量
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_YEAR_POWER_GENERATION},//寄存器地址: 1104, 年发电量
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_ACCUMLATE_POWER_GENERATION},//寄存器地址: 1106, 累计发电量
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_TOTAL_GRID_CONNECT_RUNNING_TIME},//寄存器地址: 1108, 总并网运行时间
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_DAILY_GRID_CONNECT_RUNNING_TIME},//寄存器地址: 1110, 日并网运行时间
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CO2_DAY_EMISSION},//寄存器地址: 1111, CO2当日减排量
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CO2_MONTH_EMISSION},//寄存器地址: 1113, CO2当月减排量
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CO2_YEAR_EMISSION},//寄存器地址: 1115, CO2当年减排量
    {SEG_DATA, type_unsigned_int, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_CO2_ACCMULATE_EMISSION},//寄存器地址: 1117, CO2累计减排量
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_DATA_ID_SHORT_CIRCUIT_POSITION_WITH_LOW_INSULAT_IMPEDANCE},//寄存器地址: 1119, 绝缘阻抗低可能短路位置
};
#define ANA_DATA_INFO1_NUM     (sizeof(pack_ana_data_info_1) / sizeof(data_info_id_verison_t))

#define PARALLEL_REAL_DATA_NUM (DIG_DATA_NUM + ANA_DATA_INFO2_NUM + FACT_DATA_INFO_NUM + ANA_DATA_INFO1_NUM + SPECIAL_DATA_NUM)  // 7为特殊的sid
// 主机协议栈接收从机的实时量数据：
realdata_to_sdram_t g_realdata_to_sdram[MAX_PARALELL_NUM][PARALLEL_REAL_DATA_NUM] RAM_SECTION_BSS = {};

//解析参数量1寄存器地址：2075——2087
static data_info_id_verison_t pack_para_data_info_1[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},//数据的字节长度
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_PARA_ID_REMOTE_POWER_DISPATCH_ENABLE_DISABLE}, //寄存器地址: 2075, 远程功率调度使能/禁止
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_PARA_ID_SHCED_INSTRUCT_MAINTEN_TIME},          //寄存器地址: 2076, 调度指令维持时间
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_PARA_ID_ACTIVE_POWER_REFERENCE},               //寄存器地址: 2078, 有功功率基准
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_PARA_ID_APPARNET_POWER_REFERENCE},             //寄存器地址: 2080, 视在功率基准
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_PARA_ID_MAX_APPARENT_POWER_PARAM},             //寄存器地址: 2082, 视在功率最大值
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_PARA_ID_MAX_ACTIVE_POWER_PARAM},               //寄存器地址: 2084, 有功功率最大值
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_PARA_ID_POWER_LIMIT_ZERO_PERCENT_SHUTDOWN},    //寄存器地址: 2086, 限功率0%关机  未使用
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_PARA_ID_NIGHT_REACTIVE_ENABLE},                //寄存器地址: 2087, 夜间无功使能   未使用
    {SEG_DATA, type_int,            ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_PARA_ID_NIGTH_REACTIVE_POWER_COMPEN},          //寄存器地址: 2088, 夜间无功补偿(kVar)   未使用
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_PARA_ID_ALTITUDE},                             //寄存器地址: 2090, 海拔高度   未使用

};

//解析参数量2寄存器地址：2100——2111
static data_info_id_verison_t pack_para_data_info_2[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},//数据的字节长度
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_PARA_ID_GRID_ACTIVE_POWER_CHANGE},             //寄存器地址: 2100, 有功功率变化梯度
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_PARA_ID_ACTIVE_POWER_CONTROL_MODE},            //寄存器地址: 2102, 有功控制模式
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_PARA_ID_ACTIVE_POWER_DERATING_SETTING},        //寄存器地址: 2103, 有功降额设置（固定值）
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_PARA_ID_ACTIVE_POWER_DERATING_PERCENT},        //寄存器地址: 2105, 有功降额百分比
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_PARA_ID_GRID_REACTIVE_POWER_CHANGE},           //寄存器地址: 2106, 无功功率变化梯度
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_PARA_ID_REACTIVE_POWER_COMPEN_MODE},           //寄存器地址: 2108, 无功补偿模式
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_PARA_ID_REACTIVE_POWER_COMPEN_SETTING_FACTOR}, //寄存器地址: 2109, 无功补偿设置（功率因数）
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_PARA_ID_REACTIVE_POWER_COMPEN_SETTING},        //寄存器地址: 2110, 无功补偿设置（Q/S）
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_PARA_ID_REACTIVE_POWER_REGULATIONG_TIME},      //寄存器地址: 2111, 无功调节时间
};

//解析参数量3寄存器地址：2270——2282 未使用
static data_info_id_verison_t pack_para_data_info_3[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},//数据的字节长度
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_PARA_ID_GRID_CONNECT_ACTIVE_POWER_CONTROL_MODE},     //寄存器地址: 2270, 并网有功功率控制模式
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_PARA_ID_CLOSE_LOOP_CONTROLLER},                      //寄存器地址: 2271, 闭环控制器
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_PARA_ID_RESTRICTION_METHOD},                         //寄存器地址: 2272, 限制方式
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_PARA_ID_POWER_REGULAR_CYCLE},                        //寄存器地址: 2273, 功率调节周期
    {SEG_DATA, type_unsigned_int,   ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_PARA_ID_POWER_UP_THRESHOLD},                         //寄存器地址: 2274, 升功率阈值
    {SEG_DATA, type_int,            ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_PARA_ID_MAX_FEED_GRID_POWER},                        //寄存器地址: 2276, 最大馈送电网功率(KW)
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_PARA_ID_MAX_FEED_GRID_POWER_PERCENT},                //寄存器地址: 2278, 最大馈送电网功率(%)
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_PARA_ID_GRID_CONNECT_REACTIVE_POWER_CONTROL_METHOD}, //寄存器地址: 2279, 并网无功功率控制方式
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_PARA_ID_TARGET_POWER_METHOD},                        //寄存器地址: 2280, 目标功率因数
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_PARA_ID_REACTIVE_POWER_REGULAR_CYCLE},               //寄存器地址: 2281, 无功调节周期
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DAC_PO_PARA_ID_REACTIVE_POWER_REGULAR_DEAD_ZONE},           //寄存器地址: 2282, 无功调节死区
};

static cmd_parse_info_id_verison_t cmd_parse_info[] RAM_SECTION =
{
    {&pack_fac_data_info[0], sizeof(pack_fac_data_info)/sizeof(data_info_id_verison_t)},
    {&pack_ana_data_info_1[0], sizeof(pack_ana_data_info_1)/sizeof(data_info_id_verison_t)},
    {&pack_ana_data_info_2[0], sizeof(pack_ana_data_info_2)/sizeof(data_info_id_verison_t)},
    {&pack_dig_data_info[0], sizeof(pack_dig_data_info)/sizeof(data_info_id_verison_t)},
    {&pack_para_data_info_1[0], sizeof(pack_para_data_info_1)/sizeof(data_info_id_verison_t)},
    {&pack_para_data_info_2[0], sizeof(pack_para_data_info_2)/sizeof(data_info_id_verison_t)},
    {&pack_para_data_info_3[0], sizeof(pack_para_data_info_3)/sizeof(data_info_id_verison_t)},
};

static cmd_t no_poll_cmd_tab_master[] RAM_SECTION = {
    {CMD_INVERTER_PARALLEL_MODBUS_GET_ALARM, CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {CMD_INVERTER_PARALLEL_MODBUS_SET_PARA_1, CMD_POSITIVE, &cmd_req[1], &cmd_ack[1], sizeof(modbusrtu_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {CMD_INVERTER_PARALLEL_MODBUS_SET_PARA_2, CMD_POSITIVE, &cmd_req[1], &cmd_ack[1], sizeof(modbusrtu_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {CMD_INVERTER_PARALLEL_MODBUS_SET_PARA_3, CMD_POSITIVE, &cmd_req[1], &cmd_ack[1], sizeof(modbusrtu_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {CMD_INVERTER_PARALLEL_MODBUS_CTRL_POWER_ON, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {CMD_INVERTER_PARALLEL_MODBUS_GET_FACTORY, CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[0]},
    {CMD_INVERTER_PARALLEL_MODBUS_GET_ANALOG_1, CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[1]},
    {CMD_INVERTER_PARALLEL_MODBUS_GET_ANALOG_2, CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[2]},
    {CMD_INVERTER_PARALLEL_MODBUS_GET_DIGIT, CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE,NULL,&cmd_parse_info[3]},
    {CMD_INVERTER_PARALLEL_MODBUS_CTRL_POWER_OFF, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {CMD_INVERTER_PARALLEL_MODBUS_CTRL_IV_SCAN_START, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {CMD_INVERTER_PARALLEL_MODBUS_CTRL_IV_SCAN_STOP, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {CMD_INVERTER_PARALLEL_MODBUS_CTRL_RESTART_SYSTEM, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {CMD_INVERTER_PARALLEL_MODBUS_CTRL_START_CHECK_ISOLATE, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {CMD_INVERTER_PARALLEL_MODBUS_CTRL_START_CHECK_AFCI   , CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {CMD_INVERTER_PARALLEL_MODBUS_SET_ONE_PARA, CMD_POSITIVE, &cmd_req[1], &cmd_ack[1], sizeof(modbusrtu_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {CMD_INVERTER_PARALLEL_MODBUS_GET_PARA_1, CMD_POSITIVE, &cmd_req[3], &cmd_ack[3], sizeof(modbusrtu_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE, NULL, &cmd_parse_info[4]},
    {CMD_INVERTER_PARALLEL_MODBUS_GET_PARA_2, CMD_POSITIVE, &cmd_req[3], &cmd_ack[3], sizeof(modbusrtu_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE, NULL, &cmd_parse_info[5]},
    {CMD_INVERTER_PARALLEL_MODBUS_GET_PARA_3, CMD_POSITIVE, &cmd_req[3], &cmd_ack[3], sizeof(modbusrtu_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE, NULL, &cmd_parse_info[6]},
    {0},
};

static cmd_t poll_cmd_tab_master[] RAM_SECTION = {
    {0},
};

/* 主机的注册表，命令的解析走自定义 */
static cmd_handle_register_t s_cmd_handle_master[] RAM_SECTION = {
    {DEV_DAC_PARALLEL_MASTER_MODBUS, CMD_INVERTER_PARALLEL_MODBUS_GET_ALARM, CMD_TYPE_NO_POLL, parse_alarm_data_master, pack_alarm_data_master},//获取告警命令
    {DEV_DAC_PARALLEL_MASTER_MODBUS, CMD_INVERTER_PARALLEL_MODBUS_GET_FACTORY, CMD_TYPE_NO_POLL, NULL, (void*)pack_master_req_real_analog_frm},
    {DEV_DAC_PARALLEL_MASTER_MODBUS, CMD_INVERTER_PARALLEL_MODBUS_GET_ANALOG_1, CMD_TYPE_NO_POLL, NULL, (void*)pack_master_req_real_analog_frm},
    {DEV_DAC_PARALLEL_MASTER_MODBUS, CMD_INVERTER_PARALLEL_MODBUS_GET_ANALOG_2, CMD_TYPE_NO_POLL, NULL, (void*)pack_master_req_real_analog_frm},
    {DEV_DAC_PARALLEL_MASTER_MODBUS, CMD_INVERTER_PARALLEL_MODBUS_GET_DIGIT, CMD_TYPE_NO_POLL, NULL, (void*)pack_master_req_real_analog_frm},
    {DEV_DAC_PARALLEL_MASTER_MODBUS, CMD_INVERTER_PARALLEL_MODBUS_GET_PARA_1, CMD_TYPE_NO_POLL, NULL, (void*)pack_master_req_real_analog_frm}, // 获取参数命令
    {DEV_DAC_PARALLEL_MASTER_MODBUS, CMD_INVERTER_PARALLEL_MODBUS_GET_PARA_2, CMD_TYPE_NO_POLL, NULL, (void*)pack_master_req_real_analog_frm}, // 获取参数命令
    {DEV_DAC_PARALLEL_MASTER_MODBUS, CMD_INVERTER_PARALLEL_MODBUS_GET_PARA_3, CMD_TYPE_NO_POLL, NULL, (void*)pack_master_req_real_analog_frm}, // 获取参数命令
    {DEV_DAC_PARALLEL_MASTER_MODBUS, CMD_INVERTER_PARALLEL_MODBUS_SET_PARA_1, CMD_TYPE_NO_POLL, (void*)parse_set_data_master, (void*)pack_set_data_master}, // 设置参数命令
    {DEV_DAC_PARALLEL_MASTER_MODBUS, CMD_INVERTER_PARALLEL_MODBUS_SET_PARA_2, CMD_TYPE_NO_POLL, (void*)parse_set_data_master, (void*)pack_set_data_master}, // 设置参数命令
    {DEV_DAC_PARALLEL_MASTER_MODBUS, CMD_INVERTER_PARALLEL_MODBUS_SET_PARA_3, CMD_TYPE_NO_POLL, (void*)parse_set_data_master, (void*)pack_set_data_master}, // 设置参数命令
    {DEV_DAC_PARALLEL_MASTER_MODBUS, CMD_INVERTER_PARALLEL_MODBUS_SET_ONE_PARA, CMD_TYPE_NO_POLL, (void*)parse_set_data_master, (void*)pack_set_data_master}, // 设置参数命令
    {DEV_DAC_PARALLEL_MASTER_MODBUS, CMD_INVERTER_PARALLEL_MODBUS_CTRL_POWER_ON, CMD_TYPE_NO_POLL, (void*)parse_crtl_cmd_master, (void*)pack_crtl_cmd_master}, // 遥控功率开
    {DEV_DAC_PARALLEL_MASTER_MODBUS, CMD_INVERTER_PARALLEL_MODBUS_CTRL_POWER_OFF, CMD_TYPE_NO_POLL, (void*)parse_crtl_cmd_master, (void*)pack_crtl_cmd_master}, // 遥控功率关
    {DEV_DAC_PARALLEL_MASTER_MODBUS, CMD_INVERTER_PARALLEL_MODBUS_CTRL_IV_SCAN_START, CMD_TYPE_NO_POLL, (void*)parse_crtl_cmd_master, (void*)pack_crtl_cmd_master}, // 遥控IV扫描开
    {DEV_DAC_PARALLEL_MASTER_MODBUS, CMD_INVERTER_PARALLEL_MODBUS_CTRL_IV_SCAN_STOP, CMD_TYPE_NO_POLL, (void*)parse_crtl_cmd_master, (void*)pack_crtl_cmd_master}, // 遥控IV扫描关
    {DEV_DAC_PARALLEL_MASTER_MODBUS, CMD_INVERTER_PARALLEL_MODBUS_CTRL_RESTART_SYSTEM, CMD_TYPE_NO_POLL, (void*)parse_crtl_cmd_master, (void*)pack_crtl_cmd_master}, // 系统重启
    {DEV_DAC_PARALLEL_MASTER_MODBUS, CMD_INVERTER_PARALLEL_MODBUS_CTRL_START_CHECK_ISOLATE, CMD_TYPE_NO_POLL, (void*)parse_crtl_cmd_master, (void*)pack_crtl_cmd_master}, // 启动绝缘阻抗检测
    {DEV_DAC_PARALLEL_MASTER_MODBUS, CMD_INVERTER_PARALLEL_MODBUS_CTRL_START_CHECK_AFCI   , CMD_TYPE_NO_POLL, (void*)parse_crtl_cmd_master, (void*)pack_crtl_cmd_master}, // 启动AFCI自检

};

static ctrl_set_cmd_modbus_t s_modbus_ctrl_cmd[] RAM_SECTION = {
    {CMD_INVERTER_PARALLEL_MODBUS_CTRL_POWER_ON, 0x0001},
    {CMD_INVERTER_PARALLEL_MODBUS_CTRL_POWER_OFF, 0x0000},
    {CMD_INVERTER_PARALLEL_MODBUS_CTRL_IV_SCAN_START, 0x0001},
    {CMD_INVERTER_PARALLEL_MODBUS_CTRL_IV_SCAN_STOP, 0x0000},
    {CMD_INVERTER_PARALLEL_MODBUS_CTRL_RESTART_SYSTEM, 0x0001},
    {CMD_INVERTER_PARALLEL_MODBUS_CTRL_START_CHECK_ISOLATE, 0x0001},
    {CMD_INVERTER_PARALLEL_MODBUS_CTRL_START_CHECK_AFCI   , 0x0001},
    {0},
};


//设备实例的配置：与从机通信配置,即作为主机
static dev_type_t dev_inverter_modbus_master RAM_SECTION = {
    DEV_DAC_PARALLEL_MASTER_MODBUS,
    MAX_PARALELL_NUM,
    PROTOCOL_MODBUS_RTU,
    LINK_INVERTER,
    S_BUFF_LEN_2048,
    R_BUFF_LEN_2048,
    BOTTOM_CSU_TYPE,
    no_poll_cmd_tab_master,
    poll_cmd_tab_master,
    0,
};

//主机告警表
static modbus_addr_map_data_t g_modbus_alarm_data_map_master[] RAM_SECTION = {
    {1205, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1,  TYPE_ALAM ,DAC_PO_TRACE_ID_MPPT_OVER_VOLT}, // MPPT1过压
    {1205, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1,  TYPE_ALAM ,DAC_PO_TRACE_ID_MPPT_OVER_VOLT+1}, // MPPT2过压
    {1205, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1,  TYPE_ALAM ,DAC_PO_TRACE_ID_MPPT_OVER_VOLT+2}, // MPPT3过压
    {1205, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1,  TYPE_ALAM ,DAC_PO_TRACE_ID_MPPT_OVER_VOLT+3}, // MPPT4过压
    {1205, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 7, TYPE_ALAM , }, // 预留
    {1205, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1,  TYPE_ALAM , DAC_PO_TRACE_ID_IN_HARDWARE_FAULT}, // 内部硬件故障
    {1205, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1,  TYPE_ALAM , DAC_PO_TRACE_ID_BUS_SOFT_START_ERR}, // 母线软启动故障
    {1205, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1,  TYPE_ALAM , DAC_PO_TRACE_ID_DC_DISCONNECTOR_TRIP},// 直流隔离开关脱扣
    {1205, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM ,  DAC_PO_TRACE_ID_BUS_UNDER_VOLT}, // BUS欠压
    {1205, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM ,  DAC_PO_TRACE_ID_PV_SLOW_UP_ERR}, // 逆变缓启动失败

    {1206, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 8, TYPE_ALAM , },  // 预留
    {1206, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM ,DAC_PO_TRACE_ID_STR_REVERSE_CONNECT },     // 组串1反接
    {1206, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM ,DAC_PO_TRACE_ID_STR_REVERSE_CONNECT +1}, // 组串2反接
    {1206, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM ,DAC_PO_TRACE_ID_STR_REVERSE_CONNECT +2}, // 组串3反接
    {1206, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM ,DAC_PO_TRACE_ID_STR_REVERSE_CONNECT +3}, // 组串4反接
    {1206, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM ,DAC_PO_TRACE_ID_STR_REVERSE_CONNECT +4}, // 组串5反接
    {1206, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM ,DAC_PO_TRACE_ID_STR_REVERSE_CONNECT +5}, // 组串6反接
    {1206, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM ,DAC_PO_TRACE_ID_STR_REVERSE_CONNECT +6}, // 组串7反接
    {1206, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM ,DAC_PO_TRACE_ID_STR_REVERSE_CONNECT +7}, // 组串8反接
    {1207, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 16, TYPE_ALAM , }, // 预留
    {1208, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_MPPT_OVER_CURR_WARN}, // MPPT1过流告警
    {1208, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_MPPT_OVER_CURR_WARN+1 }, // MPPT2过流告警
    {1208, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_MPPT_OVER_CURR_WARN+2 }, // MPPT3过流告警
    {1208, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_MPPT_OVER_CURR_WARN+3 }, // MPPT4过流告警
    {1208, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 8, TYPE_ALAM , }, // 预留
    {1208, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_MPPT_OVER_CURR_ERROR }, // MPPT1过流故障
    {1208, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_MPPT_OVER_CURR_ERROR+1 }, // MPPT2过流故障
    {1208, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_MPPT_OVER_CURR_ERROR+2 }, // MPPT3过流故障
    {1208, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_MPPT_OVER_CURR_ERROR+3 }, // MPPT4过流故障
    {1209, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 8, TYPE_ALAM , },// 预留2
    {1209, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_STR_CURR_REVERSE_INJECT },// 组串1电流反灌
    {1209, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_STR_CURR_REVERSE_INJECT+1 }, // 组串2电流反灌
    {1209, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_STR_CURR_REVERSE_INJECT+ 2}, // 组串3电流反灌
    {1209, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_STR_CURR_REVERSE_INJECT+ 3}, // 组串4电流反灌
    {1209, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_STR_CURR_REVERSE_INJECT+ 4}, // 组串5电流反灌
    {1209, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_STR_CURR_REVERSE_INJECT+ 5}, // 组串6电流反灌
    {1209, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_STR_CURR_REVERSE_INJECT+ 6}, // 组串7电流反灌
    {1209, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_STR_CURR_REVERSE_INJECT+ 7}, // 组串8电流反灌
    {1210, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_SERIES_DC_ARC_FAULT},     // 组串1直流电弧故障
    {1210, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_SERIES_DC_ARC_FAULT + 1}, // 组串2直流电弧故障
    {1210, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_SERIES_DC_ARC_FAULT + 2}, // 组串3直流电弧故障
    {1210, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_SERIES_DC_ARC_FAULT + 3}, // 组串4直流电弧故障
    {1210, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_SERIES_DC_ARC_FAULT + 4}, // 组串5直流电弧故障
    {1210, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_SERIES_DC_ARC_FAULT + 5}, // 组串6直流电弧故障
    {1210, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_SERIES_DC_ARC_FAULT + 6}, // 组串7直流电弧故障
    {1210, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_SERIES_DC_ARC_FAULT + 7}, // 组串8直流电弧故障
    {1210, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 8, TYPE_ALAM ,}, // 预留
    {1211, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 16, TYPE_ALAM , }, //预留
    {1212, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_DC_ARC_DAULT },                // 直流电弧故障
    {1212, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_AFCI_SELF_CHK_FAIL },        // AFCI自检失败
    {1212, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_BUS_OVER_VOLT},               // 母线过压
    {1212, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_POS_BUS_OVER_VOLT},           // 正母线过压
    {1212, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_NEG_BUS_OVER_VOLT },           // 负母线过压
    {1212, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_BUS_VOLT_IMBALANCE },       // 母线电压不平衡
    {1212, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_ABNORMAL_OPER_BUILD_IN_PID },        // 内置PID工作异常
    {1212, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM ,},                 // 预留
    {1212, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_IMPEDANCE_WARN },    // 母线缘阻抗低告警
    {1212, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_IMPEDANCE_ERROR},    // 母线绝缘阻抗低故障
    {1212, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_ABN_RESIDUAL_CURR },    // 残余电流异常
    {1212, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_ABN_RESIDUAL_CURR_LEAP}, // 残余电流突变异常
    {1212, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_OUTPUT_WIRING_MISMATCH}, // 输出接线不匹配
    {1212, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_LEAK_CURR_SELFCHECK_FAILURE_FAULT}, // 漏电流电路自检失败故障
    {1212, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 2, TYPE_ALAM , }, // 预留

    {1213, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_PV_OVER_CURR_WARN},             // 逆变器A相过流告警
    {1213, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_PV_OVER_CURR_WARN + 1},            // 逆变器B相过流告警
    {1213, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_PV_OVER_CURR_WARN + 2},    // 逆变器C相过流告警
    {1213, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_PV_OVER_CURR_ERROR},                      // 逆变器A相过流故障
    {1213, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_PV_OVER_CURR_ERROR + 1},        // 逆变器B相过流故障
    {1213, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_PV_OVER_CURR_ERROR + 2},        // 逆变器C相过流故障
    {1213, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_HIGH_VOLT_RT},       // 高电压穿越
    {1213, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_LOW_VOLT_RT},       // 低电压穿越
    {1213, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_LOW_POWER_SHUTDOWN},             // 低功率关机
    {1213, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_WATER_INGRESS},             // 逆变器进水告警
    {1213, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 6, TYPE_ALAM , }, // 预留
    {1214, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_OVER_VOLT_BETWEEN_PHASE },            // AB过压
    {1214, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_OVER_VOLT_BETWEEN_PHASE + 1 },        // BC过压
    {1214, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_OVER_VOLT_BETWEEN_PHASE + 2 },        // CA过压
    {1214, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_UNDER_VOLT_BETWEEN_PHASE },           // AB欠压
    {1214, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_UNDER_VOLT_BETWEEN_PHASE + 1 },       // BC欠压
    {1214, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_UNDER_VOLT_BETWEEN_PHASE + 1},       // CA欠压
    {1214, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_PHASE_VOLT_UNDER_VOLT },              // A相欠压
    {1214, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_PHASE_VOLT_UNDER_VOLT + 1 },         // B相欠压
    {1214, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_PHASE_VOLT_UNDER_VOLT + 2 },         // C相欠压
    {1214, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_PHASE_VOLT_OVER_VOLT},              // A相过压
    {1214, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_PHASE_VOLT_OVER_VOLT + 1},          // B相过压
    {1214, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_PHASE_VOLT_OVER_VOLT + 2 },          // C相过压
    {1214, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_PHASE_VOLT_TRANS_OVER_VOLT },        // A相瞬态过压
    {1214, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_PHASE_VOLT_TRANS_OVER_VOLT + 1 },    // B相瞬态过压
    {1214, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_PHASE_VOLT_TRANS_OVER_VOLT + 2 },    // C相瞬态过压
    {1214, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_OUT_CURR_IMBALANCE}, // 输出电流不平衡
    {1215, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_GRID_OVER_FREQ},        // 电网过频
    {1215, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_GRID_UNDER_FREQ },       // 电网欠频
    {1215, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_GRID_FREQ_INSTABLE  },      // 电网频率不稳
    {1215, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_AC_PHASE_LOSS },              // 交流缺相
    {1215, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_ABNORMAL_GROUND },         // 接地异常
    {1215, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_SINGLE_PHASE_GND_SHORT_CIRCUIT },       // A相对地短路
    {1215, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_SINGLE_PHASE_GND_SHORT_CIRCUIT + 1},       // B相对地短路
    {1215, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_SINGLE_PHASE_GND_SHORT_CIRCUIT + 2},       // C相对地短路
    {1215, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_TEN_MINUTE_GRID_OVER_VOLT_PROT }, // 十分钟电网电压过压保护
    {1215, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_UNBALANCE_POWER_GRID },       // 电网不平衡
    {1215, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_OUTPUT_SHORT_CIRCUIT },       // 输出短路
    {1215, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_OUTPUT_OVER_CIRCUIT },           // 输出过流
    {1215, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_ABNORMAL_DC_COMPONENT },           // 直流分量异常
    {1215, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_ISLAND_PROT },             // 孤岛保护
    {1215, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_POWER_GRID_OUTAGE },          // 电网掉电
    {1215, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_GRID_ANTI_ERROR  },          // 电网反序故障

    {1216, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_GRID_RELAY_ERROR },  //并网继电器故障
    {1216, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_AC_CURR_SENSOR_ERROR }, // AC电流传感器故障
    {1216, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_PV_LOCK_ERROR },        // 逆变器锁相失败故障

    {1216, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_STR_LOSS },    //组串1丢失
    {1216, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_STR_LOSS + 1}, //组串2丢失
    {1216, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_STR_LOSS + 2}, //组串3丢失
    {1216, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_STR_LOSS + 3}, //组串4丢失
    {1216, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_STR_LOSS + 4}, //组串5丢失
    {1216, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_STR_LOSS + 5 },//组串6丢失
    {1216, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_STR_LOSS + 6 },//组串7丢失
    {1216, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_STR_LOSS + 7 },//组串8丢失
    {1216, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 5, TYPE_ALAM ,},  // 预留

    {1217, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_RADIATOR_OVER_TEMP },    // 散热器1过温
    {1217, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_RADIATOR_OVER_TEMP + 1 },    // 散热器2过温
    {1217, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_RADIATOR_OVER_TEMP + 2 },    // 散热器3过温
    {1217, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , },    // 散热器4过温（预留）
    {1217, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , },    // 散热器5过温（预留）
    {1217, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_PCB_LOW_TEMPERATURE_WARNING },       // PCB低温告警
    {1217, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_INSIDE_LOW_TEMPERATURE_WARNING},     // 内部低温告警
    {1217, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_NTC_LOW_TEMPERATURE_WARNING},        // NTC1低温告警
    {1217, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_NTC_LOW_TEMPERATURE_WARNING + 1 },   // NTC2低温告警
    {1217, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_NTC_LOW_TEMPERATURE_WARNING + 2 },   // NTC3低温告警
    {1217, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_LOW_TEMPERATURE_SHUTDOWN_PROTECTION},// 低温关机保护
    {1217, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 5, TYPE_ALAM ,},  // 预留

    {1218, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_PCB_OVER_TEMP },  //PCB温度过温
    {1218, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM ,  },// 预留
    {1218, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_OVER_TEMP_INSIDS_MACHINE },        // 机内温度过温
    {1218, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_LOW_TEMP_INSIDS_MACHINE },  // 机内温度过低
    {1218, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_FIRE_ERROR },    // 火灾故障
    {1218, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_EXTER_FAN_FAIL },    // 外部风扇故障
    {1218, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_INTER_FAN_FAIL },    // 内部风扇故障
    {1218, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_ABN_AUXI_POWER_SUPPLT },    // 辅助电源异常
    {1218, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_AC_LIGHTING_PROTECTION_ERR },    // 交流防雷失效
    {1218, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_DC_LIGHTING_PROTECTION_ERR },    // 直流防雷失效
    {1218, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 6, TYPE_ALAM ,},  // 预留
    {1219, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_MAIN_CTRL_EEPROM_FAULT }, // 主控EEPROM故障
    {1219, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_MAIN_CTRL_EEPROM_ABNORMAL }, // 主控EEPROM异常
    {1219, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_AUXI_CTRL_EEPROM_FAULT },  // 辅控EEPROM故障
    {1219, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_AUXI_CTRL_EEPROM_ABNORMAL },  // 辅控EEPROM异常
    {1219, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1,  TYPE_ALAM , DAC_PO_TRACE_ID_MAIN_CPLD_COMMU_ERROR }, // 主控与CPLD通讯故障
    {1219, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1,  TYPE_ALAM , DAC_PO_TRACE_ID_MAIN_AUXI_COMMU_ERROR },    // 主控与辅控通讯故障
    {1219, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1,  TYPE_ALAM , DAC_PO_TRACE_ID_PROTOCOL_VER_MISMATCH }, // 协议版本不匹配
    {1219, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1,  TYPE_ALAM , DAC_PO_TRACE_ID_CARRIER_SYNC_ABNORMAL},     // 载波同步信号异常
    {1219, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1,  TYPE_ALAM , DAC_PO_TRACE_ID_MONITER_MAIN_CTRL_COMMU_ERROR}, // 监控与主控通讯故障
    {1219, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1, TYPE_ALAM , DAC_PO_TRACE_ID_MONITER_ERROR_ALARM},      // 监控单元故障告警
    {1219, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1,  TYPE_ALAM , DAC_PO_TRACE_ID_LICENSE_EXPIRED },    // 许可证过期
    {1219, 0, TYPE_INT8S, TYPE_BIT, 0, 0, 1,  TYPE_ALAM , DAC_PO_TRACE_ID_USER_ACCOUNT_EXPIRED },    // user账号过期
    {1219, 1, TYPE_INT8S, TYPE_BIT, 0, 0, 4, TYPE_ALAM , }, // 预留
};
#define ALARM_MAP_NUM  (sizeof(g_modbus_alarm_data_map_master) / sizeof(modbus_addr_map_data_t))

/* 并机参数表 */
static modbus_addr_map_data_t g_modbus_data_map[] RAM_SECTION = {
    {2021, 0, TYPE_INT16U, TYPE_INT16U,  0, 0, 2, TYPE_ANA , DAC_PO_PARA_ID_POWER_ON_OFF },      // 开关机
    {2032, 0, TYPE_INT16U, TYPE_INT16U,  0, 0, 2, TYPE_ANA , DAC_PO_PARA_ID_MPPT_ENABLE},           // MPPT多峰扫描使能
    {2033, 0, TYPE_INT16U, TYPE_INT16U,  0, 0, 2, TYPE_ANA , DAC_PO_PARA_ID_MPPT_SCAN_INTERVAL},    // MPPT多峰扫描间隔时间
    {2074, 0, TYPE_INT16U, TYPE_INT16U,  0, 0, 2, TYPE_ANA , DAC_PO_PARA_ID_AFCI_CHECK_DETECTION_SENSITIVITY },      // AFCI检测灵敏度
    {2075, 0, TYPE_INT16U, TYPE_INT16U,  0, 0, 2, TYPE_ANA , DAC_PO_PARA_ID_REMOTE_POWER_DISPATCH_ENABLE_DISABLE },       // 远程功率调度使能/禁止
    {2076, 0, TYPE_INT32U, TYPE_INT32U,  0, 0, 4, TYPE_ANA , DAC_PO_PARA_ID_SHCED_INSTRUCT_MAINTEN_TIME },              // 调度指令维持时间
    {2078, 0, TYPE_FLOAT, TYPE_INT32U,   3, 0, 4, TYPE_ANA , DAC_PO_PARA_ID_ACTIVE_POWER_REFERENCE },      // 有功功率基准
    {2080, 0, TYPE_FLOAT, TYPE_INT32U,   3, 0, 4, TYPE_ANA , DAC_PO_PARA_ID_APPARNET_POWER_REFERENCE }, // 视在功率基准
    {2082, 0, TYPE_FLOAT, TYPE_INT32U,   3, 0, 4, TYPE_ANA , DAC_PO_PARA_ID_MAX_APPARENT_POWER_PARAM }, // 视在功率最大值
    {2084, 0, TYPE_FLOAT, TYPE_INT32U,   3, 0, 4, TYPE_ANA , DAC_PO_PARA_ID_MAX_ACTIVE_POWER_PARAM },      // 有功功率最大值
    {2086, 0, TYPE_INT16U, TYPE_INT16U,  0, 0, 2, TYPE_ANA , DAC_PO_PARA_ID_POWER_LIMIT_ZERO_PERCENT_SHUTDOWN },    // 限功率0%关机
    {2087, 0, TYPE_INT16U, TYPE_INT16U,  0, 0, 2, TYPE_ANA , DAC_PO_PARA_ID_NIGHT_REACTIVE_ENABLE },       // 夜间无功使能
    {2088, 0, TYPE_FLOAT, TYPE_INT32S,   3, 0, 4, TYPE_ANA , DAC_PO_PARA_ID_NIGTH_REACTIVE_POWER_COMPEN }, // 夜间无功补偿(kVar)
    {2090, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2,  TYPE_ANA , DAC_PO_PARA_ID_ALTITUDE },          // 海拔高度

    {2100, 0, TYPE_FLOAT, TYPE_INT32U,  3, 0, 4, TYPE_ANA ,  DAC_PO_PARA_ID_GRID_ACTIVE_POWER_CHANGE },  // 有功功率变化梯度
    {2102, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ANA ,  DAC_PO_PARA_ID_ACTIVE_POWER_CONTROL_MODE },               // 有功控制模式
    {2103, 0, TYPE_FLOAT, TYPE_INT32U,  3, 0, 4, TYPE_ANA ,  DAC_PO_PARA_ID_ACTIVE_POWER_DERATING_SETTING },        // 有功降额设置（固定值）
    {2105, 0, TYPE_FLOAT, TYPE_INT16S,  1, 0, 2, TYPE_ANA ,  DAC_PO_PARA_ID_ACTIVE_POWER_DERATING_PERCENT }, // 有功降额百分比
    {2106, 0, TYPE_FLOAT, TYPE_INT32U,  3, 0, 4, TYPE_ANA ,  DAC_PO_PARA_ID_GRID_REACTIVE_POWER_CHANGE }, // 无功功率变化梯度
    {2108, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ANA ,  DAC_PO_PARA_ID_REACTIVE_POWER_COMPEN_MODE },        // 无功补偿模式
    {2109, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_ANA ,  DAC_PO_PARA_ID_REACTIVE_POWER_COMPEN_SETTING_FACTOR },  // 无功补偿设置（功率因数）
    {2110, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_ANA ,  DAC_PO_PARA_ID_REACTIVE_POWER_COMPEN_SETTING },          // 无功补偿设置（Q/S）
    {2111, 0, TYPE_INT16U,TYPE_INT16U,  0, 0, 2, TYPE_ANA ,  DAC_PO_PARA_ID_REACTIVE_POWER_REGULATIONG_TIME },         // 无功调节时间

    {2270, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ANA , DAC_PO_PARA_ID_GRID_CONNECT_ACTIVE_POWER_CONTROL_MODE },       // 并网有功功率控制模式
    {2271, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ANA , DAC_PO_PARA_ID_CLOSE_LOOP_CONTROLLER },           // 闭环控制器
    {2272, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ANA , DAC_PO_PARA_ID_RESTRICTION_METHOD },                   // 限制方式
    {2273, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_ANA , DAC_PO_PARA_ID_POWER_REGULAR_CYCLE }, // 功率调节周期
    {2274, 0, TYPE_FLOAT, TYPE_INT32U,  3, 0, 4, TYPE_ANA , DAC_PO_PARA_ID_POWER_UP_THRESHOLD },            // 升功率阈值
    {2276, 0, TYPE_FLOAT, TYPE_INT32S,  3, 0, 4, TYPE_ANA , DAC_PO_PARA_ID_MAX_FEED_GRID_POWER },                // 最大馈送电网功率(KW)
    {2278, 0, TYPE_FLOAT, TYPE_INT16U, 1, 0, 2,  TYPE_ANA , DAC_PO_PARA_ID_MAX_FEED_GRID_POWER_PERCENT },                // 最大馈送电网功率(%)
    {2279, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ANA , DAC_PO_PARA_ID_GRID_CONNECT_REACTIVE_POWER_CONTROL_METHOD },  // 并网无功功率控制方式
    {2280, 0, TYPE_FLOAT, TYPE_INT16S,  3, 0, 2, TYPE_ANA , DAC_PO_PARA_ID_TARGET_POWER_METHOD },   // 目标功率因数
    {2281, 0, TYPE_FLOAT, TYPE_INT16U,  1, 0, 2, TYPE_ANA , DAC_PO_PARA_ID_REACTIVE_POWER_REGULAR_CYCLE },   // 无功调节周期
    {2282, 0, TYPE_FLOAT, TYPE_INT16U,  3, 0, 2, TYPE_ANA , DAC_PO_PARA_ID_REACTIVE_POWER_REGULAR_DEAD_ZONE },            // 无功调节死区
    {2406, 0, TYPE_STRING, TYPE_STRING, 0, 0, 32,TYPE_ANA , DAC_PO_PARA_ID_CSC_VERSION},// CSC
    {2422, 0, TYPE_STRING, TYPE_STRING, 0, 0, 32,TYPE_ANA , DAC_PO_PARA_ID_PSC_VERSION},// PSC
    {2438, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ANA , DAC_PO_PARA_ID_DELAY_UPDATE,}, // 延迟升级
    {2439, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_ANA , DAC_PO_PARA_ID_WIFI_WORK_MODE,}, // wifi工作模式

    {2478, 0, TYPE_INT16U, TYPE_INT16U, 0, 0, 2, TYPE_PARA , DAC_PO_PARA_ID_USER_ACCOUNT_PERMANENTLY_VALID},        // user账号是否永久有效
    {2479, 0, TYPE_INT32U, TYPE_INT32U, 0, 0, 4, TYPE_PARA , DAC_PO_PARA_ID_USER_ACCOUNT_EXPIRATION_DATE},        // user账号授权截止时间
};

static alarm_to_eeprom_t alarm_to_eeprom[sizeof(g_modbus_alarm_data_map_master)/sizeof(g_modbus_alarm_data_map_master[0])+1]; //多一个来存crc
#define MAX_ALARM_NUM_SAVE         PARALLEL_ALARM_SIZE / MAX_PARALELL_NUM / sizeof(alarm_to_eeprom_t)   // 单个机子的能够保存的最大并机数量
static alarm_to_eeprom_t init_alarm_to_eeprom[MAX_ALARM_NUM_SAVE] RAM_SECTION_BSS;
/**
 * @brief 获取寄存器地址索引通用接口
 * @param[in] interact_id 交互ID
 * @param[in] table_size  数据条数
 * @param[in] data_map    数据表地址
 * @retval 索引， -1（获取失败)
*/
int find_register_addr_index_by_id_universal(unsigned int interact_id, int table_size, modbus_addr_map_data_t* data_map) {
    int ret = NO_MATCH_REGISTER_ADDR;
    int star_index = 0;

    while (star_index < table_size) {
        if (data_map[star_index].data_addr == interact_id) {
            return star_index;
        }
        star_index ++;
    }

    return ret;
}

/**
 * @brief 并机数据发送前处理
 * @param[in] cmd_id 命令ID
 * @param[in] interact_id 交互数据ID
 * @retval SUCCESSFUL 成功 FAILURE 失败
*/
int set_cmd_deal(unsigned char cmd_id, unsigned int interact_id) {
    int ret = NO_MATCH_REGISTER_ADDR;
    int table_size = sizeof(g_modbus_data_map)/sizeof(g_modbus_data_map[0]);

    ret = find_register_addr_index_by_id_universal(interact_id, table_size, g_modbus_data_map);
    RETURN_VAL_IF_FAIL(ret != NO_MATCH_REGISTER_ADDR, FAILURE);

    ret = set_register_info(cmd_id, g_modbus_data_map[ret].register_addr, g_modbus_data_map[ret].data_len / 2);
    return ret;
}

dev_type_t *init_dev_inverter_modbus_parallel_master(void)
{
    return &dev_inverter_modbus_master;
}

/**
 * @brief 获取short型数据中某个bit位数据，输入bit_count为0，表示获取最低位数据
 * @retval SUCCESSFUL 成功 FAILURE 失败
 * @note 模拟量寄存器地址
*/
unsigned char parse_bit_data(unsigned short data, unsigned char bit_count)
{
    unsigned short mask = 1 << bit_count;
    unsigned char bit = (data & mask) ? 1 : 0;
    return bit;
}

static void init_parallel_alarm(alarm_to_eeprom_t* alarm_data, int num)
{
    for (int idx = 0; idx < num; idx++)
    {
        alarm_data[idx].alarm_ID = g_modbus_alarm_data_map_master[idx].data_addr;
        alarm_data[idx].alarm_value = 0;
        alarm_data[idx].update_flag = 0;
        alarm_data[idx].alarm_time = 0;
    }
}

int process_parallel_alarm(int reg_nums, int index, parallel_alarm_msg_t* eeprom_msg, unsigned char *data_buff)
{
    unsigned char bit_count = 0;
    unsigned char update_flag = 0;
    short current_reg = 0;
    unsigned short data = 0;
    unsigned char flag_result;
    unsigned char bit_value;

    if (eeprom_msg == NULL || data_buff == NULL)
    {
        return FAILURE;
    }

    while ((reg_nums > 0 ) && (index < ALARM_MAP_NUM))
    {
        flag_result = g_modbus_alarm_data_map_master[index].reserve_flag;
        data = get_uint16_data(&data_buff[current_reg*2]); // 获取当前寄存器的数据

        if(flag_result == 1)
        {//预留字段跳过
            bit_count += g_modbus_alarm_data_map_master[index].data_len;
            index++;

            if(bit_count == 16)
            {//取完了一个寄存器的数据
#if PARALLEL_MODBUS_DEBUG
                rt_kprintf("[parallel modbus] parse data: now data is:%d,current_reg = %d \n",data,current_reg);
#endif
                bit_count = 0;
                current_reg += 1;
                reg_nums -= 1;
            }
            continue;
        }

        //解析bit数据到char型中
        bit_value = parse_bit_data(data, bit_count);

        //存入ID对应的地址中
        RETURN_VAL_IF_FAIL(SUCCESSFUL == set_one_data(g_modbus_alarm_data_map_master[index].data_addr, &bit_value), FAILURE); 

        // 解析完以后需要对比EEPROM中数据，看是否产生了告警
        update_flag += judge_alarm_change(bit_value, g_modbus_alarm_data_map_master[index].data_addr,
                            alarm_to_eeprom, sizeof(alarm_to_eeprom) / sizeof(alarm_to_eeprom_t));
        bit_count += 1;
        if(bit_count == 16)
        {//取完了一个寄存器的数据
#if PARALLEL_MODBUS_DEBUG
            rt_kprintf("[parallel modbus] parse data: now data is:%d,current_reg = %d \n",data,current_reg);
#endif
            bit_count = 0;
            current_reg += 1;
            reg_nums -= 1;
        }
        index++;
    }

    if(update_flag > 0)
    {
        // 如果告警有变化，将新的数据存入EEPROM中去
        handle_storage(write_opr, PARALLEL_ALARM_PART, (unsigned char*)alarm_to_eeprom, sizeof(alarm_to_eeprom), eeprom_msg->epprom_offset);
        // 处理完以后发消息通知MQTT线程，让MQTT线程查看告警变化，并上送
        send_msg_to_thread(PARALLEL_ALARM_CHANGE_MSG, MOD_MQTT, (void *)eeprom_msg, sizeof(parallel_alarm_msg_t));
    }

    return SUCCESSFUL;
}

/**********
 * 主机：modbus获取告警命令的解包
*/


int init_alarm_eeprom(parallel_alarm_msg_t* eeprom_msg)
{   
    int index = 0, alarm_index = 0, ee_alarm_index = 0;
    unsigned int ee_offset = 0;
    init_parallel_alarm(alarm_to_eeprom, eeprom_msg->alarm_num);
    register_alarm_eeprom_info(alarm_to_eeprom, sizeof(alarm_to_eeprom) / sizeof(alarm_to_eeprom_t), init_alarm_to_eeprom, MAX_ALARM_NUM_SAVE, g_modbus_alarm_data_map_master);
    for (index = 0; index < MAX_PARALELL_NUM; index++)
    {   
        ee_offset = index * sizeof(init_alarm_to_eeprom);
        // 读取eeprom数据
        if (handle_storage(read_opr, PARALLEL_ALARM_PART, (unsigned char*)init_alarm_to_eeprom, sizeof(init_alarm_to_eeprom), ee_offset) != SUCCESSFUL)
        {
            return FAILURE;
        }

        // 遍历每一个告警，然后赋值
        for(alarm_index = 0; alarm_index < eeprom_msg->alarm_num; alarm_index++)
        {
            for(ee_alarm_index = 0; ee_alarm_index < MAX_ALARM_NUM_SAVE; ee_alarm_index++)
            {
                if(alarm_to_eeprom[alarm_index].alarm_ID == init_alarm_to_eeprom[ee_alarm_index].alarm_ID)
                {   
                    alarm_to_eeprom[alarm_index].alarm_value = init_alarm_to_eeprom[ee_alarm_index].alarm_value;
                    alarm_to_eeprom[alarm_index].update_flag = init_alarm_to_eeprom[ee_alarm_index].update_flag;
                    alarm_to_eeprom[alarm_index].alarm_time = init_alarm_to_eeprom[ee_alarm_index].alarm_time;
                }
            }
        }

        // 全部清除，然后赋值。
        rt_memset(init_alarm_to_eeprom, 0xFF, sizeof(init_alarm_to_eeprom));
        if (handle_storage(write_opr, PARALLEL_ALARM_PART, (unsigned char*)init_alarm_to_eeprom, sizeof(init_alarm_to_eeprom), ee_offset) != SUCCESSFUL)
        {
            return FAILURE;
        }

        if (handle_storage(write_opr, PARALLEL_ALARM_PART, (unsigned char*)alarm_to_eeprom, sizeof(alarm_to_eeprom), ee_offset) != SUCCESSFUL)
        {
            return FAILURE;
        }
    }
    return SUCCESSFUL;
}


int parse_alarm_data_master(void* dev_inst, void* cmd_buff)
{
    //主机parse  解析收到的从机的告警的modbus命令的响应信息,并将告警放入EEPROM中
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    unsigned char *data_buff = ((cmd_buf_t *)cmd_buff)->buf;
    int index = 0;
    g_reg_nums = data_buff[0] / 2;

    int reg_addr = g_reg_addr;
    int reg_nums = g_reg_nums;

    char sn[SERIAL_NUMBER_LEN] = {0};
    parallel_alarm_msg_t eeprom_msg = {0};
    static int init_flag = FALSE;

    // 收到回包，记录地址，
    eeprom_msg.dev_addr = ((dev_inst_t*)dev_inst)->dev_addr;
    if (eeprom_msg.dev_addr > MAX_PARALELL_NUM || eeprom_msg.dev_addr == 0)
    {
        LOG_E("parse_alarm_data failed. addr=%d", eeprom_msg.dev_addr);
        return FAILURE;
    }
    eeprom_msg.alarm_num = sizeof(alarm_to_eeprom) / sizeof(alarm_to_eeprom_t) - 1;     // 减一用于去除crc数据
    eeprom_msg.epprom_offset = (eeprom_msg.dev_addr - 1) * sizeof(init_alarm_to_eeprom); // 没有有效位了，因为每次起来都会初始化一次

    get_serial_number(eeprom_msg.dev_addr, sn, SERIAL_NUMBER_LEN);
    if (rt_strnlen(sn, SERIAL_NUMBER_LEN) != SERIAL_NUMBER_LEN)
    {
        return SUCCESSFUL;
    }

    // 填表的告警数量不能超过eeprom能存的告警数量
    if(eeprom_msg.alarm_num > MAX_ALARM_NUM_SAVE)
    {   
        LOG_E("alarm_num exceed MAX_ALARM_NUM_SAVE");
        return FAILURE;
    }

    if(!init_flag)
    {
        init_alarm_eeprom(&eeprom_msg);
        init_flag = TRUE;
    }

    // 读取eeprom数据到静态数组中
    if(handle_storage(read_opr, PARALLEL_ALARM_PART, (unsigned char*)alarm_to_eeprom, sizeof(alarm_to_eeprom), eeprom_msg.epprom_offset) != SUCCESSFUL)
    {
        return FAILURE;
    }


    /*先找到MODBUS基地址的下标索引*/
    index = find_register_start_addr_index_universal(reg_addr, ALARM_MAP_NUM, g_modbus_alarm_data_map_master);

    if (index == NO_MATCH_REGISTER_ADDR)
    {
        return SUCCESSFUL;
    }
    data_buff = &(((cmd_buf_t *)cmd_buff)->buf[1]);

    return process_parallel_alarm(reg_nums, index, &eeprom_msg, data_buff);
}

/**********
 * 主机：modbus获取告警命令的组包
*/
int pack_alarm_data_master(void* dev_inst, void* cmd_buff)
{
    //主机pack  打包发送获取告警的modbus命令
    cmd_buf_t *send_cmd = (cmd_buf_t *)cmd_buff;
    RETURN_VAL_IF_FAIL(cmd_buff != NULL,FAILURE);
    RETURN_VAL_IF_FAIL(send_cmd->cmd != NULL,FAILURE); 

    unsigned char* data = send_cmd->buf;
    // unsigned int data_len = send_cmd->buflen;
    unsigned short data_offset = 0;
    g_reg_addr = 1205;
    put_int16_to_buff(data + data_offset, 1205);//TODO：后续用宏定义
    data_offset += 2;

    put_int16_to_buff(data + data_offset, 15);
    data_offset += 2;

    send_cmd->data_len = data_offset;

    return SUCCESSFUL;
}

/** 用到
 * @brief 打包主机请求实时数据-模拟量
 * @param[in] dev_inst 设备实例
 * @param[in] cmd_buff 命令数据缓冲区
 * @retval SUCCESSFUL 成功 FAILURE 失败
 * @note 模拟量寄存器地址
*/
int pack_master_req_real_analog_frm(dev_inst_t* dev_inst, cmd_buf_t* cmd_buff) {

    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    // 根据CMD_ID选择寄存器地址
    cmd_id_to_register_info_t* p_cmd_id_to_register_info = NULL;

    find_cmd_id_match_register_info(((cmd_buf_t *)cmd_buff)->cmd->cmd_id, cmd_id_to_register_info, 
        sizeof(cmd_id_to_register_info)/sizeof(cmd_id_to_register_info[0]), &p_cmd_id_to_register_info);

    RETURN_VAL_IF_FAIL(SUCCESSFUL == universal_pack_register_info(cmd_buff, p_cmd_id_to_register_info), FAILURE);

    ((cmd_buf_t *)cmd_buff)->data_len = 4;  // 数据段：寄存器起始地址(2字节) + 寄存器数量(2字节)

    return SUCCESSFUL;
}

/**********
 * 主机：modbus设置参数命令的解包
*/
int parse_set_data_master(void* dev_inst, void* cmd_buff)
{

    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    cmd_id_to_register_info_t* p_cmd_id_to_register_info;
    unsigned char *data_buff = ((cmd_buf_t *)cmd_buff)->buf;

    g_reg_addr = get_uint16_data(&data_buff[0]); // 获取数据域的寄存器首地址 2字节
    g_reg_nums = get_uint16_data(&data_buff[2]); // 获取数据域的寄存器个数 2字节
    
    find_cmd_id_match_register_info(((cmd_buf_t *)cmd_buff)->cmd->cmd_id, cmd_id_to_register_info, 
        sizeof(cmd_id_to_register_info)/sizeof(cmd_id_to_register_info[0]), &p_cmd_id_to_register_info);

    if(g_reg_addr != p_cmd_id_to_register_info->register_addr)
    {
        return FAILURE;
    }

    if(g_reg_nums != p_cmd_id_to_register_info->register_num)
    {
        return FAILURE;
    }

    return SUCCESSFUL;
}

/**********
 * 主机：modbus设置参数命令的组包
*/
int pack_set_data_master(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    // 根据CMD_ID选择寄存器地址
    cmd_id_to_register_info_t* p_cmd_id_to_register_info = NULL;

    find_cmd_id_match_register_info(((cmd_buf_t *)cmd_buff)->cmd->cmd_id, cmd_id_to_register_info, 
        sizeof(cmd_id_to_register_info)/sizeof(cmd_id_to_register_info[0]), &p_cmd_id_to_register_info);

    RETURN_VAL_IF_FAIL(SUCCESSFUL == universal_pack_register_info(cmd_buff, p_cmd_id_to_register_info), FAILURE);

    g_reg_addr = p_cmd_id_to_register_info->register_addr;
    g_reg_nums = p_cmd_id_to_register_info->register_num;

    set_reg_nums(g_reg_nums);
    set_reg_addr(g_reg_addr);
    RETURN_VAL_IF_FAIL(SUCCESSFUL == universal_pack_set_para(cmd_buff,sizeof(g_modbus_data_map)/sizeof(g_modbus_data_map[0]), g_modbus_data_map,5),FAILURE);

    return SUCCESSFUL;
}

/**
 * @brief 通用的组包，组设置命令的包
 * @param[in] cmd_buff 命令数据缓冲区
 * @retval SUCCESSFUL 成功 FAILURE 失败
 * @note 
*/
int universal_pack_set_para(void* cmd_buff, int table_size, modbus_addr_map_data_t* data_map, int offset)
{ 
    int reg_addr = g_reg_addr;
    /*先找到基地址的下标索引*/
    int index = find_register_start_addr_index_universal(reg_addr,table_size, data_map);

    if (index == NO_MATCH_REGISTER_ADDR)
    {
        return FAILURE;
    }

    int data_len = new_pack_data_to_buff(cmd_buff , &index, offset, data_map);
    ((cmd_buf_t *)cmd_buff)->data_len = data_len + offset;
    return SUCCESSFUL;

}

/**
 * @brief 命令自定义解析层，解析函数的注册
 * @note 
*/
void modbus_register_master_cmd_table()
{
    for (int i = 0; i < sizeof(s_cmd_handle_master) / sizeof(s_cmd_handle_master[0]); i++)
    {
        register_cmd_handle(&s_cmd_handle_master[i]);
    }
}

/**
 * @brief 判断某个告警是否变化
 * @param[in] alarm_value 输入告警值
 * @param[in] alaram_id 输入告警id
 * @param[in] input_alarm 输入告警table
 * @param[in] len_alarm 输入告警table的长度
 * @retval TRUE 变化 FALSE 未变化
*/
unsigned char judge_alarm_change(unsigned char alarm_value, unsigned int alaram_id, alarm_to_eeprom_t* input_alarm, unsigned short len_alarm) {
    unsigned short i = 0;
    for(i = 0; i < len_alarm; i++) {
        if(input_alarm[i].alarm_ID == alaram_id) {
            if(alarm_value != input_alarm[i].alarm_value) {
                input_alarm[i].alarm_value = alarm_value;
                input_alarm[i].update_flag = EEPROM_ALARM_UPDATE;
                input_alarm[i].alarm_time = time(NULL);
                return TRUE;
            } else {
                input_alarm[i].update_flag = 0;
                return FALSE;
            }
        }
    }
    return FALSE;
}

/**
 * @brief 打包主机遥控命令
 * @param[in] dev_inst 设备实例
 * @param[in] cmd_buff 命令数据缓冲区
 * @retval SUCCESSFUL 成功 FAILURE 失败
*/
int pack_crtl_cmd_master(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    // 根据CMD_ID选择寄存器地址
    cmd_id_to_register_info_t* p_cmd_id_to_register_info = NULL;

    find_cmd_id_match_register_info(((cmd_buf_t *)cmd_buff)->cmd->cmd_id, cmd_id_to_register_info, 
        sizeof(cmd_id_to_register_info)/sizeof(cmd_id_to_register_info[0]), &p_cmd_id_to_register_info);

    RETURN_VAL_IF_FAIL(SUCCESSFUL == universal_pack_register_info(cmd_buff, p_cmd_id_to_register_info), FAILURE);

    //调用共用的处理函数
    universal_pack_crtl_cmd(cmd_buff);

    return SUCCESSFUL;
}

/**
 * @brief 通用的组织控制命令的获取寄存器的值的函数
 * @retval SUCCESSFUL 成功 FAILURE 失败
*/
int universal_pack_crtl_cmd(void* cmd_buff) {
    unsigned short ctrl_value = 0;
    int ret = 0;

    RETURN_VAL_IF_FAIL(cmd_buff != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(((cmd_buf_t *)cmd_buff)->cmd != NULL, FAILURE);

    ret = find_ctrl_value_in_table(((cmd_buf_t *)cmd_buff)->cmd->cmd_id, s_modbus_ctrl_cmd, sizeof(s_modbus_ctrl_cmd)/sizeof(s_modbus_ctrl_cmd[0]));
    RETURN_VAL_IF_FAIL(ret >= 0, FAILURE);
    ctrl_value = ret;

    put_int16_to_buff( &(((cmd_buf_t *)cmd_buff)->buf[2]), ctrl_value);//存入起始寄存器地址
    ((cmd_buf_t *)cmd_buff)->data_len = 4;  // 数据区：寄存器地址（2字节）+ 设置值（2字节）

    return SUCCESSFUL;
}


/**
 * @brief 从控制命令表格找到需要的命令
 * @retval 控制状态值， -1 失败
*/
int find_ctrl_value_in_table(unsigned char cmd_id, ctrl_set_cmd_modbus_t* input_table, unsigned short input_table_len) {
    int ret = -1;

    RETURN_VAL_IF_FAIL(input_table != NULL, -1);
    unsigned short i = 0;

    for (i = 0; i < input_table_len; i++) {
        if(cmd_id == input_table[i].cmd_id) {
            ret = input_table[i].ctrl_status;
            return ret;
        }
    }
    return ret;
}


/**
 * @brief 通用的打包寄存器相关信息的函数
 * @param[in] cmd_buff 命令数据缓冲区
 * @param[in] input_table 输入的寄存器起止地址，寄存器数量等数据段信息
 * @retval SUCCESSFUL 成功 FAILURE 失败
*/
int universal_pack_register_info(void* cmd_buff,cmd_id_to_register_info_t* input_table)
{
    RETURN_VAL_IF_FAIL(input_table != NULL && cmd_buff != NULL, FAILURE);
    unsigned char* data = ((cmd_buf_t *)cmd_buff)->buf;
    unsigned short data_offset = 0;

    //根据不同的功能码来组包
    switch (input_table->func_code)
    {
    case 0x06:
        put_int16_to_buff(data + data_offset, input_table->register_addr);
        data_offset += 2;
        break;

    case 0x03:
        put_int16_to_buff(data + data_offset, input_table->register_addr);
        data_offset += 2;
        put_int16_to_buff(data + data_offset, input_table->register_num);
        data_offset += 2;
        break;

    case 0x04:
        put_int16_to_buff(data + data_offset, input_table->register_addr);
        data_offset += 2;
        put_int16_to_buff(data + data_offset, input_table->register_num);
        data_offset += 2;
        break;

    case 0x10:
        put_int16_to_buff(data + data_offset, input_table->register_addr);//存入起始寄存器地址
        data_offset += 2;
        put_int16_to_buff(data + data_offset, input_table->register_num); //存入寄存器数目
        data_offset += 2;
        data[data_offset] = input_table->register_num*2; // 存入寄存器数据字节长度
        data_offset += 1;
        break;

    default:
        return FAILURE;
    }

    return SUCCESSFUL;
}

/**
 * @brief 根据cmd_id找到相关的寄存器信息
 * @param[in] cmd_id 输入命令id
 * @param[in] input_table 输入的table
 * @param[in] input_table_len 输入的table的长度
 * @param[in] ouputdata 返回值
 * @retval SUCCESSFUL 成功 FAILURE 失败
*/
int find_cmd_id_match_register_info(unsigned short cmd_id, cmd_id_to_register_info_t* input_table,
                                    unsigned short input_table_len, cmd_id_to_register_info_t** ouputdata) {
    RETURN_VAL_IF_FAIL(input_table != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(ouputdata != NULL, FAILURE);
    unsigned short i = 0;

    for(i = 0; i < input_table_len; i++) {
        if(input_table[i].cmd_id == cmd_id) {
            *ouputdata = &input_table[i];
            return SUCCESSFUL;
        }
    }
    return FAILURE;
}

/**
 * @brief 解析主机发出的控制命令的响应信息
 * @param[in] cmd_buff 命令数据缓冲区
 * @param[in] dev_inst 设备实例
 * @retval SUCCESSFUL 成功 FAILURE 失败
*/
int parse_crtl_cmd_master(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    cmd_id_to_register_info_t* p_cmd_id_to_register_info;
    unsigned char *data_buff = ((cmd_buf_t *)cmd_buff)->buf;
    // unsigned short g_reg_value = 0;
    g_reg_addr = get_uint16_data(&data_buff[0]); // 获取数据域的寄存器首地址 2字节
    // g_reg_value = get_uint16_data(&data_buff[2]); // 获取数据域的寄存器个数 2字节

    find_cmd_id_match_register_info(((cmd_buf_t *)cmd_buff)->cmd->cmd_id, cmd_id_to_register_info, 
        sizeof(cmd_id_to_register_info)/sizeof(cmd_id_to_register_info[0]), &p_cmd_id_to_register_info);
    
    if(g_reg_addr != p_cmd_id_to_register_info->register_addr)
    {
        return FAILURE;
    }

    return SUCCESSFUL;
}

/**
 * @param[in] cmd_id 命令ID
 * @param[in] register_addr 寄存器地址
 * @param[in] register_num 寄存器数量
 * @retval SUCCESSFUL 成功 FAILURE 失败
*/
int set_register_info(unsigned char cmd_id, unsigned short register_addr, unsigned short register_num) {
    int ret = 0;
    int cmd_id_register_info_size = sizeof(cmd_id_to_register_info)/sizeof(cmd_id_to_register_info[0]);
    cmd_id_to_register_info_t* p_cmd_id_to_register_info = NULL;
    // 根据CMD_ID选择寄存器地址
    ret = find_cmd_id_match_register_info(cmd_id, cmd_id_to_register_info, cmd_id_register_info_size, &p_cmd_id_to_register_info);
    RETURN_VAL_IF_FAIL(SUCCESSFUL == ret, FAILURE);

    p_cmd_id_to_register_info->register_addr = register_addr;
    p_cmd_id_to_register_info->register_num = register_num;

    return SUCCESSFUL;
}


int write_data_to_sdram(char addr, int data_type, data_info_id_verison_t  *pack_data_info, int data_num)
{
    int loop = 0;
    char value[32] = {};
    int loop1 = 0;
    unsigned int immediate_submit_sid[] = {DAC_PO_DATA_ID_POWER_ON_OFF_STA, DAC_PO_DATA_ID_ACTIVE_POWER_CONTROL_MODE_CURR, DAC_PO_DATA_ID_ACTIVE_POWER_DERATING_SETTING_CURR,
    DAC_PO_DATA_ID_ACTIVE_POWER_DERATING_PERCENT_CURR, DAC_PO_DATA_ID_REACTIVE_POWER_COMPEN_MODE_CURR, DAC_PO_DATA_ID_REACTIVE_POWER_COMPEN_SETTING_FACTOR_CURR,
    DAC_PO_DATA_ID_REACTIVE_POWER_COMPEN_SETTING_CURR, DAC_PO_DATA_ID_CSC_VERSION_CURR, DAC_PO_DATA_ID_PSC_VERSION_CURR, DAC_PO_DATA_ID_ALARM_TOTAL_SIGNAL,
    DAC_PO_DATA_ID_ACCI_TOTAL_SIGNAL};

    if(addr <= 0 || addr > MAX_PARALELL_NUM)
    {
        return FAILURE;
    }
    
    for(loop = 0; loop < data_num; loop++)
    {
        if(NEED_INTERACT != (pack_data_info + loop)->data_struct_interact)
        {
            continue;
        }
        rt_memset(value, 0, 32);
        g_realdata_to_sdram[addr - 1][data_type + loop + SPECIAL_DATA_NUM].sid = (pack_data_info + loop)->data_inter_addr;

        get_one_data((pack_data_info + loop)->data_inter_addr, value);
        if(rt_memcmp(value, g_realdata_to_sdram[addr - 1][data_type + loop + SPECIAL_DATA_NUM].value, 32) != 0)
        {
            rt_memcpy(g_realdata_to_sdram[addr - 1][data_type + loop + SPECIAL_DATA_NUM].value, value, 32);
            g_realdata_to_sdram[addr - 1][data_type + loop + SPECIAL_DATA_NUM].flag = DATA_CHG_NO_SUBMIT;        
            for(loop1 = 0; loop1 < sizeof(immediate_submit_sid) / sizeof(unsigned int); loop1 ++)
            {
                if(g_realdata_to_sdram[addr - 1][data_type + loop + SPECIAL_DATA_NUM].sid == immediate_submit_sid[loop1])
                {
                    g_realdata_to_sdram[addr - 1][data_type + loop + SPECIAL_DATA_NUM].flag = DATA_CHG_SUBMIT;        
                }
            }
            
        }
    }

    return SUCCESSFUL;
}

int write_special_data_to_sdram(char addr)
{
    int loop = 0;
    char value[32] = {0};
    unsigned int special_sid[] = {DAC_PO_DATA_ID_MASTER_CTRL_SOFTWARE_VER_DATE,     // 主控软件版本日期
                                  DAC_PO_DATA_ID_AUXI_CTRL_SOFTWARE_VER_DATE,       // 辅控软件版本日期
                                  DAC_PO_DATA_ID_MASTER_SN,                         // 主机SN
                                  DAC_PO_DATA_ID_CPLD_SOFTWARE_VER_DATE,            // CPLD软件版本日期
                                  DAC_PO_DATA_ID_MONITOR_SOFTWARE_VER_DATE,         // 监控软件版本日期
                                  DAC_PO_DATA_ID_MANUFACTURER_DATE,                 // 生产日期
                                  DAC_PO_DATA_ID_FIRST_STARTUP_TIME};    // 首次启动时间
    for(loop = 0; loop < sizeof(special_sid) / sizeof(unsigned int); loop ++)
    {
        rt_memset(value, 0, 32);
        g_realdata_to_sdram[addr - 1][loop].sid = special_sid[loop];
        get_one_data(special_sid[loop], value);
        if(rt_memcmp(value, g_realdata_to_sdram[addr - 1][loop].value, 32) != 0)
        {
            rt_memcpy(g_realdata_to_sdram[addr - 1][loop].value, value, 32);
        }
    }
    return SUCCESSFUL;
}

int judge_realdata_immediate_submit(char addr)
{
    int loop = 0;
    char count = 0;
    for(loop = 0; loop < PARALLEL_REAL_DATA_NUM; loop ++)
    {
        if(g_realdata_to_sdram[addr - 1][loop].flag == DATA_CHG_SUBMIT)
        {
            g_realdata_to_sdram[addr - 1][loop].flag = DATA_NO_CHG_NO_SUBMIT;
            count += 1;
        }
    }
    if(count > 0)
    {
        send_msg_to_thread(MQTT_IMMEDIATE_SUBMIT_REAL, MOD_MQTT, &addr, sizeof(char));
    }   
    return SUCCESSFUL;       
}

int write_realdata_to_sdram(char addr)
{
    write_special_data_to_sdram(addr);
    write_data_to_sdram(addr, 0, pack_dig_data_info, DIG_DATA_NUM);
    write_data_to_sdram(addr, DIG_DATA_NUM, pack_ana_data_info_1, ANA_DATA_INFO1_NUM);
    write_data_to_sdram(addr, DIG_DATA_NUM + ANA_DATA_INFO1_NUM, pack_ana_data_info_2, ANA_DATA_INFO2_NUM);
    write_data_to_sdram(addr, DIG_DATA_NUM + ANA_DATA_INFO1_NUM + ANA_DATA_INFO2_NUM, pack_fac_data_info, FACT_DATA_INFO_NUM);
    judge_realdata_immediate_submit(addr);
    return SUCCESSFUL;
}



int get_one_slave_data(char addr, unsigned int sid, void* data)
{

    int loop = 0;
    for(loop = 0; loop < PARALLEL_REAL_DATA_NUM; loop ++)
    {
        if(g_realdata_to_sdram[addr - 1][loop].sid != sid)
        {
            continue;
        }
        char sid_type = (char)(sid >> 28);
        if(sid_type < TYPE_FLOAT)
        {
            *(int*)data = *(int*)(g_realdata_to_sdram[addr - 1][loop].value);
            // rt_kprintf("get_one_slave_data|sid:0x%x, int_value:%d\n", sid, *(int*)data);
            return SUCCESSFUL;
        }
        else if(sid_type == TYPE_FLOAT)
        {
            *(float*)data = *(float*)(g_realdata_to_sdram[addr - 1][loop].value);
            // rt_kprintf("get_one_slave_data|sid:0x%x, float_value:%f\n", sid, *(float*)data);
            return SUCCESSFUL;
        }
        else if(sid_type == TYPE_STRING)
        {
            rt_memcpy((char*)data, g_realdata_to_sdram[addr - 1][loop].value, sid & 0xFF);
            // rt_kprintf("get_one_slave_data|sid:0x%x, str_value:%s\n", sid, *(char*)data);
            return SUCCESSFUL;
        }
        else if(sid_type == TYPE_DATE_T)
        {
            rt_memcpy((char*)data, g_realdata_to_sdram[addr - 1][loop].value, sizeof(date_base_t));
            // rt_kprintf("get_one_slave_data|sid:0x%x, date_value:%d, %d, %d, %d \n", 
            //  sid, *(char*)data, *(char*)(data + 1), *(char*)(data + 2) ,*(char*)(data + 3));
             return SUCCESSFUL;
        }
        else if(sid_type == TYPE_TIME_T)
        {
            rt_memcpy((char*)data, g_realdata_to_sdram[addr - 1][loop].value, sizeof(time_base_t));
            // rt_kprintf("get_one_slave_data|sid:0x%x, time_value:%d, %d, %d, %d, %d, %d \n", 
            //  sid, *(char*)data, *(char*)(data + 1), *(char*)(data + 2) ,*(char*)(data + 3), *(char*)(data + 4), *(char*)(data + 5));
            return SUCCESSFUL;
        }
    }
    rt_kprintf("find sid:0x%x from sdram failure\n", sid);
    return SUCCESSFUL;
}

