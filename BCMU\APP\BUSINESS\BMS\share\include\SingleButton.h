#ifndef SINGLEBUTTON_H
#define SINGLEBUTTON_H
#include "interface.h"
#include "common.h"

#ifdef __cplusplus
extern "C" {
#endif

#define PRESS_ONCE_MODE 1
#define PRESS_TWICE_MODE 2
#define PRESS_THRICE_MODE 3
#define PRESS_FOURICE_MODE 4

#define PRESSS_BUTTON_TIME 5000
#define PRESS_CONSISENT_TIME 1500


typedef enum
{
    eKeyStat0 = 0,
    eKeyStat1,
    eKeyStat2,
}KeyStatus;

typedef enum
{
    Button_OFFPRESS = 0,
    Button_ONPRESS,
}sKeyState;

enum
{
    RUN_LED_TYPE = 0,
    ALM_LED_TYPE,
    CAP1_LED_TYPE,
    CAP2_LED_TYPE,
    CAP3_LED_TYPE,
    CAP4_LED_TYPE,
};//指示灯类型
typedef enum
{
    LIGHT_STILL = 0,//常亮
    SWICH_BLINK,    //交替闪 run 和 warm 1s
    SLOW_BLINK1,    //慢闪1  1  1
    SLOW_BLINK2,    //慢闪2  0.25  3.75
    QUICK_BLINK,    //快闪   0.25  0.25

}Button_LED_type;
//电池按键显示注册模式
typedef struct
{
    rt_tick_t       uiStartTime;  //按键触发时间
    rt_tick_t       uiEndTime;    //按键结束时间
    rt_tick_t       uiBackTime;   //模式返回时间（返回到正常模式所需时间）
    uint8_t         ucPressMode;  //按键模式（按键持续按 或者 连续按）
    uint8_t         ucFuncMode;   //模式名称
    uint8_t         ucWorkStage;  //此模式所需生效的场景（例如此模式为关机，这里注册为休眠，表示关机模式需要在电池已经进入休眠的条件下生效）
    uint8_t         ucNeedBack;   //是否需要返回到正常模式（有些模式是不可返回的，例如休眠模式下不可返回到正常模式，但有些模式可以返回）
    uint8_t         ucBtModeEn; //是否支持按键生效（此模式是否可以按键进入并生效）
    void(*PressONFunc)(void);
    void(*PressOFFFunc)(void);
} Button;

//电池按键当前状态
typedef struct
{
    uint8_t         ucState;          //当前的按键状态（按下或者松开）
    uint8_t         ucCurrentMode;    //当前电池环境所处模式
    uint8_t         ucLed_CurrentMode;//当前的LED模式（用此来触发按键模式生效和结束时触发的不同LED效果，例如注册的按键模式开始时间为3，结束时间为10，当按键达到3s时LED的状态，）
    uint8_t         ucBuzzMode;       //当前的蜂鸣器模式（此蜂鸣器与按键强相关，属于按键按下到松开期间的蜂鸣提示音，不与业务绑定）
    uint8_t         ucReturnToNormal; //
    rt_tick_t       uiBack_Time;      //当前模式返回所需时间
    uint8_t         ucNeed_Back;      //当前所处模式是否能够返回到正常模式
}Button_State;

//电池按键显示状态管理
typedef struct
{
   Button       sButton_Mode[8];
   Button_State sButton_State;
   uint8_t      ucButton_Mode_Num;
}Button_struct;


void BSP_BUZZ_CTRL(CTRL_State state);
void CtrlLedOn(uint8_t  ucLedID, uint8_t  bTrue);
Button_State *Button_Funtion(int Circle_TimeMS);
rt_err_t Button_Regist_Mode_s(Button *Func);
void Realy_Control(uint16_t val);
void system_reset(void);
void Button_LED_Process(int Circle_TimeMS , Button_LED_type ledmode);
void ReturnToNormalMode(uint8_t ucRtnReason);
void Button_Mode_Set(uint8_t ucModeID);
void GetButtonState(Button_State* tButSta);
void SleepReturnToNormalMode(BYTE ucRtnReason);
void Set_Mode_Work(uint8_t ucModeID, uint8_t ucEnabled);
#ifdef __cplusplus
}
#endif
#endif // BUTTON_BEEP_LED_H
