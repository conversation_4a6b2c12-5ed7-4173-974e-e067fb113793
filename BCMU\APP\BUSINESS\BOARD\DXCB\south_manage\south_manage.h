#ifndef SOUTH_MANAGE_H_
#define SOUTH_MANAGE_H_

#ifdef __cplusplus
extern "C" {
#endif
#include "sps.h"
#include "softbus.h"

#define  STAT_COMM_FAIL     0           ///<  通讯断
#define  STAT_NORMAL        1           ///<  通讯正常

typedef struct {
    rt_uint32_t msg_id;
    void (*handle)(dev_comm_t* dev_comm, _rt_msg_t curr_msg);
} dxcb_south_msg_handle_process_t;

void* init_south_comm(void * param);
void* init_south2_comm(void * param);
void south_comm_thread(void *param);
void south2_comm_thread(void *param);
void handle_exe_dest_cmd_msg(dev_comm_t* dev_comm, _rt_msg_t curr_msg);
void south_process_recv_msg(dev_comm_t* dev_comm);
int update_protocol_by_brand(dev_comm_t* dev_comm, char dev_type);
void judge_fan_comm_fail_alarm(dev_comm_t* dev_comm);
void judge_vfd_comm_fail_alarm(dev_comm_t* dev_comm);

#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif
