/**************************************************************************
* Copyright (C) 2010-2011, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有  
* 其他说明：
***************************************************************************/

#include "stdio.h"
#include "4g_data_utils.h"
#include "wireless.h"
#include <rtdevice.h>
#include <drv_at.h>
#include <at_device.h>
#include "utils_rtthread_security_func.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "para_id_in.h"
#include "para_manage.h"

/***********************  变量定义  ************************/

T_GpsData s_tGpsData = {0};
T_GpsData s_tGpsCalDisData = {0};

Static int parseGpsData(void);
Static short checkGps(rt_device_t dev);
Static float calcDistance(T_GpsData* ptGps1, T_GpsData* ptGps2);
Static void sendSmsOver(rt_device_t dev, char *pMsg);


typedef struct
{
    rt_uint32_t uiCmdId;
    rt_err_t bIsSuccess;
}CmdList;


// 模组相关信息获取指令
CmdList model_cmd_list[] = {
    {AT_DEVICE_CTRL_GET_DEV_NUM, RT_ERROR},                 // IMEI码实际长度为15
};

// SIM卡相关信息获取指令
CmdList sim_cmd_list[] = {
    {AT_DEVICE_CTRL_GET_SIM_STATUS, RT_ERROR},              // SIM卡在位状态
    {AT_DEVICE_CTRL_GET_SIGNAL_POWER, RT_ERROR},            // 4G信号强度
    {AT_DEVICE_CTRL_GET_IMSI, RT_ERROR}                     // IMSI码实际长度为15
};

// GPS相关信息获取指令
CmdList gps_cmd_list[] = {
    {AT_DEVICE_CTRL_GET_GPS, RT_ERROR},                     // GPS定位
};



Static float ChangeTitudeByDirect(float fTitude, unsigned char ucDirect)
{
    return (ucDirect == 1) ? -fTitude : fTitude;
}



int is_4g_info_effective()
{
    struct at_device_info_4g* net4g_device_info = get_at_device_info_4g_t();
    if (rt_strnlen_s(net4g_device_info->dev_num, 20) == 0)
    {
        return FAILURE;
    }
    return SUCCESSFUL;
}



short init_delay(void)
{
    rt_thread_delay(30);
    return 0;
}


short model_4g_offline_handle(rt_device_t dev)
{
    //更新参数
    //模块故障告警判断  ？？存疑
    return 0;
}


short check_sim_status_handle(rt_device_t dev)
{
    struct at_device_info_4g *net4g_device_info = get_at_device_info_4g_t();

    if (is_4g_info_effective() == FAILURE)
    {
        for (int i = 0; i < sizeof(model_cmd_list) / sizeof(CmdList); i++)
        {
            model_cmd_list[i].bIsSuccess = get_4g_dev_info(dev, model_cmd_list[i].uiCmdId);
        }
        set_one_data(FACT_INFO_ID_IMEI, net4g_device_info->dev_num);  // IMEI码
    }

    // 获取SIM卡信息
    for (int i = 0; i < sizeof(sim_cmd_list) / sizeof(CmdList); i++)
    {
        sim_cmd_list[i].bIsSuccess = get_4g_dev_info(dev, sim_cmd_list[i].uiCmdId);
    }

    set_one_data(BMS_DATA_ID_SIM_STAT, &(net4g_device_info->sim_status));       // SIM卡在位状态
    set_one_data(BMS_DATA_ID_SIGNAL_QUALITY, &(net4g_device_info->signal_power));  // 4G信号强度
    set_one_data(FACT_INFO_ID_IMSI, net4g_device_info->imsi);                    // IMSI码

    // 获取GPS信息
    for (int i = 0; i < sizeof(gps_cmd_list) / sizeof(CmdList); i++)
    {
        gps_cmd_list[i].bIsSuccess = get_gps_dev_info(dev, gps_cmd_list[i].uiCmdId);
    }

    parseGpsData();
    checkGps(dev);

    return SUCCESSFUL;
}



int register_4g_func(void)
{
    net_4g_func_t* net_4g_func = get_4g_func_t();
    if (net_4g_func == NULL)
    {
        return FAILURE;
    }

    net_4g_func->ptr_init_handle = init_delay;
    net_4g_func->ptr_no4g_model_handle = model_4g_offline_handle;
    net_4g_func->ptr_check_sim_status_handle = check_sim_status_handle;
    net_4g_func->ptr_product_special_handle = NULL;

    return SUCCESSFUL;
}



Static int parseGpsData(void)
{
    struct gps_location *gps_device_info = get_at_device_info_gps_t();

    s_tGpsData.iGpsLocated = (gps_cmd_list[0].bIsSuccess == RT_EOK) ? SUCCESSFUL : FAILURE;  // 是否定位成功

    // UTC日期
    long lTmp = strtol(gps_device_info->date, NULL, 10);  // ddmmyy 示例：080820
    s_tGpsData.ucDay     = lTmp / 10000;
    s_tGpsData.ucMonth   = (lTmp % 10000) / 100;
    s_tGpsData.wYear     = (lTmp % 100) + 2000;

    // UTC时间
    lTmp = strtol(gps_device_info->time, NULL, 10);  // hhmmss.sss
    s_tGpsData.ucHour    = lTmp / 10000;
    s_tGpsData.ucMinute  = (lTmp % 10000) / 100;
    s_tGpsData.ucSecond  = lTmp % 100;

    // 纬度
    lTmp = (long)(1000 * gps_device_info->latitude_num);
    s_tGpsData.fLatitude = (float)(lTmp % 100000) / 60000 + lTmp / 100000;  // ddmm.mmm转换成dd.ddddd

    // 纬度方向
    if (gps_device_info->latitude[0] == 'N')
    {
        s_tGpsData.ucLatiDirect = 0;
    }
    else if (gps_device_info->latitude[0] == 'S')
    {
        s_tGpsData.ucLatiDirect = 1;
    }

    // 经度
    lTmp = (long)(1000 * gps_device_info->longitude_num);
    s_tGpsData.fLongtitude = (float)(lTmp % 100000) / 60000 + lTmp / 100000;

    // 经度方向
    if (gps_device_info->longitude[0] == 'E')
    {
        s_tGpsData.ucLongDirect = 0;
    }
    else if (gps_device_info->longitude[0] == 'W')
    {
        s_tGpsData.ucLongDirect = 1;
    }

    // 方向角
    s_tGpsData.fAzimuth = gps_device_info->cog;

    // 对地速度，节
    s_tGpsData.fSpeed = gps_device_info->spkn;

    // 卫星数量
    s_tGpsData.ucStarNum = gps_device_info->nsat;

    // 设置数据
    set_one_data(BMS_DATA_ID_GPS_UTC_YEAR, &s_tGpsData.wYear);               // UTC时间---年
    set_one_data(BMS_DATA_ID_GPS_UTC_MONTH, &s_tGpsData.ucMonth);            // UTC时间---月
    set_one_data(BMS_DATA_ID_GPS_UTC_DAY, &s_tGpsData.ucDay);                // UTC时间---日
    set_one_data(BMS_DATA_ID_GPS_UTC_HOUR, &s_tGpsData.ucHour);              // UTC时间---时
    set_one_data(BMS_DATA_ID_GPS_UTC_MINUTE, &s_tGpsData.ucMinute);          // UTC时间---分
    set_one_data(BMS_DATA_ID_GPS_UTC_SECOND, &s_tGpsData.ucSecond);          // UTC时间---秒
    set_one_data(BMS_DATA_ID_GPS_LATITUDE, &s_tGpsData.fLatitude);           // 纬度
    set_one_data(BMS_DATA_ID_GPS_LATIDIRECT, &s_tGpsData.ucLatiDirect);      // 纬度方向
    set_one_data(BMS_DATA_ID_GPS_LONGITUDE, &s_tGpsData.fLongtitude);        // 经度
    set_one_data(BMS_DATA_ID_GPS_LONGDIRECT, &s_tGpsData.ucLongDirect);      // 经度方向
    set_one_data(BMS_DATA_ID_GPS_DIRECTANGLE, &s_tGpsData.fAzimuth);         // 方向角
    set_one_data(BMS_DATA_ID_GPS_SPEED, &s_tGpsData.fSpeed);                 // 对地速度
    set_one_data(BMS_DATA_ID_STAR_NUM, &s_tGpsData.ucStarNum);               // 卫星数量

    return SUCCESSFUL;
}



Static short checkGps(rt_device_t dev)
{
    float fTmp = 0.0, fLongtitude = 0.0, fLatitude = 0.0;
    unsigned short wGPSAntiTheftDistance = 0;
    unsigned char aucBMSSn[16];
    char aucSMSData[MAX_TX_HZ * 2];
    char aucBuf[16];

    if (s_tGpsData.iGpsLocated == FAILURE)
    {
        return FAILURE;
    }

    fTmp = calcDistance(&s_tGpsCalDisData, &s_tGpsData);

    // 记录经纬度，如果距离超过0.2，则记录
    if (fTmp > 0.2)
    {
        // 保留原有逻辑，具体实现细节待补充
        // 这里可以添加具体的记录逻辑，例如写入日志文件或更新数据库等
    }

    get_one_para(PARA_ID_GSP_ANTI_THEFT_DISTANCE, &wGPSAntiTheftDistance);
    if (fTmp > (float)wGPSAntiTheftDistance / 1000.0)
    {
        get_one_data(FACT_INFO_ID_BMS_FACT_SN, aucBMSSn);
        rt_memcpy_s(aucBuf, sizeof(aucBuf), aucBMSSn, sizeof(aucBMSSn));
        aucBuf[sizeof(aucBuf) - 1] = '\0';

        fLongtitude = ChangeTitudeByDirect(s_tGpsData.fLongtitude, s_tGpsData.ucLongDirect);
        fLatitude = ChangeTitudeByDirect(s_tGpsData.fLatitude, s_tGpsData.ucLatiDirect);

        if (fabs(s_tGpsCalDisData.fLatitude) < 0.000001 && fabs(s_tGpsCalDisData.fLongtitude) < 0.000001) // 初次定位上，不置振动告警
        {
            rt_snprintf_s(aucSMSData, sizeof(aucSMSData), "Batt SN:%s, StarNum: %d, current place: %f, %f", aucBuf, s_tGpsData.ucStarNum, fLatitude, fLongtitude);
        }
        else
        {
            unsigned char ucShakeAlarm = 1;
            rt_snprintf_s(aucSMSData, sizeof(aucSMSData), "Batt SN:%s, move %fkm, current place: %f, %f", aucBuf, fTmp, fLatitude, fLongtitude);
            set_one_data(ALARM_ID_SHAKE_ALARM, &ucShakeAlarm); // 启动振动告警
        }

        sendSmsOver(dev, aucSMSData);
        rt_memcpy_s(&s_tGpsCalDisData, sizeof(T_GpsData), &s_tGpsData, sizeof(T_GpsData));
    }

    return SUCCESSFUL;
}



Static void sendSmsOver(rt_device_t dev, char *pMsg)
{
    const int phone_nums[] = {
        PARA_ID_ALM_PHONE_NUM_1,
        PARA_ID_ALM_PHONE_NUM_2,
        PARA_ID_ALM_PHONE_NUM_3,
        PARA_ID_ALM_PHONE_NUM_4,
        PARA_ID_ALM_PHONE_NUM_5
    };
    char acphone[20];

    for (int i = 0; i < sizeof(phone_nums) / sizeof(phone_nums[0]); ++i)
    {
        get_one_para(phone_nums[i], acphone);
        sendMessage(dev, acphone, pMsg);
    }
}



Static float calcDistance(T_GpsData* ptGps1, T_GpsData* ptGps2)
{
    const double toRadians = PI / 180.0;

    double fLa1 = ptGps1->fLatitude * toRadians;  // 转换成弧度
    double fLa2 = ptGps2->fLatitude * toRadians;
    double fLo1 = ptGps1->fLongtitude * toRadians;
    double fLo2 = ptGps2->fLongtitude * toRadians;

    double fTmp = cos(fLa1) * cos(fLa2) + sin(fLa1) * sin(fLa2) * cos(fLo1 - fLo2);

    return (float)(EARTH_RADIUS * acos(fTmp));
}

