﻿#include "exchange_data_info.h"
#include <rtthread.h>
#include "data_type.h"
#include "utils_rtthread_security_func.h"
#include "utils_data_transmission.h"
#include "protocol_bottom_comm.h"
#include "sps.h"

Static alarm_data_t s_controller_alarm[CONTROLLER_ALARM_MAX] = {0};
Static alarm_data_t s_battery_alarm[BATTERY_ALARM_MAX] = {0};
Static rt_mutex_t s_mutex_data = NULL;
Static rt_mutex_t s_mutex_can_send = NULL;
Static rt_mutex_t s_mutex_can3_send = NULL;
Static dev_inst_t* s_dev_R321 = NULL;
Static dev_inst_t* s_dev_north = NULL;

Static int generic_linklayer_send(link_inst_t* link_inst, rt_mutex_t mutex, unsigned char src_dev_type, unsigned char src_dev_addr, unsigned char dst_dev_type, unsigned char dst_dev_addr, unsigned char* data, unsigned int data_len);


int init_exchange_data_info(void)
{
    if (s_mutex_data == NULL)
    {
        s_mutex_data = rt_mutex_create("exchange_data", RT_IPC_FLAG_FIFO);
    }

    return SUCCESSFUL;
}

int set_controller_alarm_data(int index, alarm_data_t data)
{
    RETURN_VAL_IF_FAIL(s_mutex_data != NULL, FAILURE);
    if(index >= CONTROLLER_ALARM_MAX || index < 0)
    {
        return FAILURE;
    }

    rt_mutex_take(s_mutex_data, RT_WAITING_FOREVER);
    s_controller_alarm[index].alm_val = data.alm_val;
    s_controller_alarm[index].time = data.time;
    rt_mutex_release(s_mutex_data);

    return SUCCESSFUL;
}

alarm_data_t get_controller_alarm_data(int index)
{
    alarm_data_t invalid = {0};
    if(index >= CONTROLLER_ALARM_MAX || index < 0)
    {
        return invalid;
    }

    return s_controller_alarm[index];
}

int set_battery_alarm_data(int index, alarm_data_t data)
{
    RETURN_VAL_IF_FAIL(s_mutex_data != NULL, FAILURE);
    if(index >= BATTERY_ALARM_MAX || index < 0)
    {
        return FAILURE;
    }

    rt_mutex_take(s_mutex_data, RT_WAITING_FOREVER);
    s_battery_alarm[index].alm_val = data.alm_val;
    s_battery_alarm[index].time = data.time;
    rt_mutex_release(s_mutex_data);

    return SUCCESSFUL;
}

alarm_data_t get_battery_alarm_data(int index)
{
    alarm_data_t invalid = {0};
    if(index >= BATTERY_ALARM_MAX || index < 0)
    {
        return invalid;
    }

    return s_battery_alarm[index];
}


int pack_battery_alarm_can_frm(alarm_data_t alarm_data, unsigned short id, unsigned char* data, unsigned char data_len)
{
    // 根据报警状态设置功能码
    data[FUNC_INDEX_IN_FRAME] = (alarm_data.alm_val == TRUE) ? FUNC_BATTERY_ALARM_HAPPEN : FUNC_BATTERY_ALARM_RECOVER;

    // 将ID和时间写入缓冲区
    put_uint16_to_buff(&data[ID_INDEX_IN_FRAME], id);
    put_uint32_to_buff(&data[TIME_INDEX_IN_FRAME], alarm_data.time);

    return SUCCESSFUL;
}



int pack_controller_alarm_can_frm(alarm_data_t alarm_data, unsigned short id, unsigned char* data, unsigned char data_len)
{
    // 根据报警状态设置功能码
    data[FUNC_INDEX_IN_FRAME] = (alarm_data.alm_val == TRUE) ? FUNC_CONTROLLER_ALARM_HAPPEN : FUNC_CONTROLLER_ALARM_RECOVER;

    // 将ID和时间写入缓冲区
    put_uint16_to_buff(&data[ID_INDEX_IN_FRAME], id);
    put_uint32_to_buff(&data[TIME_INDEX_IN_FRAME], alarm_data.time);

    return SUCCESSFUL;
}


int deal_receviced_can_short_frame(dev_inst_t* dev_inst, exchange_data_handle_t *frm_handle, unsigned int handle_len)
{
    unsigned char i = 0;
    dev_inst_t* device = (dev_inst_t*)dev_inst;
    link_inst_t* link_inst = device->dev_type->link_inst;
    unsigned char* data = link_inst->r_cache.buff;
    unsigned int data_len = link_inst->r_cache.len;

    RETURN_VAL_IF_FAIL(data_len == 7, FAILURE);
    link_inst->s_cache.len = 0;

    for (i = 0; i < handle_len; i++)
    {
        if (data[FUNC_INDEX_IN_FRAME] == frm_handle[i].func_code)
        {
            frm_handle[i].recv_handle(data, data_len, frm_handle[i].para);
            return SUCCESSFUL;
        }
    }

    return FAILURE;
}


dev_inst_t* init_can_common(void){
    s_dev_R321 =  init_dev_inst(DEV_BCCU_SOUTH_CAN);
    s_mutex_can_send = rt_mutex_create("can_link_send", RT_IPC_FLAG_FIFO);
    return s_dev_R321;
}

dev_inst_t* init_can3_common(void){
    s_dev_north =  init_dev_inst(DEV_BCCU_NORTH_CAN);
    s_mutex_can3_send = rt_mutex_create("can3_link_send", RT_IPC_FLAG_FIFO);
    return s_dev_north;
}

// 使用通用函数替换原有函数
int can_linklayer_send(unsigned char addr,unsigned char* data, unsigned int data_len)
{
    link_inst_t* link_inst = s_dev_R321->dev_type->link_inst;
    return generic_linklayer_send(link_inst, s_mutex_can_send, BOTTOM_BCCU_TYPE, 1, BOTTOM_R321_TYPE, addr, data, data_len);
}




int can3_linklayer_send(unsigned char dev_code, unsigned char* data, unsigned int data_len)
{
    link_inst_t* link_inst = s_dev_north->dev_type->link_inst;
    return generic_linklayer_send(link_inst, s_mutex_can3_send, BOTTOM_BCCU_TYPE, get_host_addr(), dev_code, link_inst->r_link_addr, data, data_len);
}




// 抽取成通用函数
Static int generic_linklayer_send(link_inst_t* link_inst, rt_mutex_t mutex, unsigned char src_dev_type, unsigned char src_dev_addr, unsigned char dst_dev_type, unsigned char dst_dev_addr, unsigned char* data, unsigned int data_len)
{
    rt_mutex_take(mutex, RT_WAITING_FOREVER);
    link_inst->s_cache.buff[0] = src_dev_type;             // s_dev
    link_inst->s_cache.buff[1] = src_dev_addr;             // s_addr
    link_inst->s_cache.buff[2] = dst_dev_type;             // d_dev
    link_inst->s_cache.buff[3] = dst_dev_addr;             // d_addr
    rt_memcpy_s(&link_inst->s_cache.buff[4], link_inst->s_cache.bufsize, data, data_len);
    link_inst->s_cache.len = data_len + 4;
    linklayer_send(link_inst);
    rt_mutex_release(mutex);

    return SUCCESSFUL;
}

