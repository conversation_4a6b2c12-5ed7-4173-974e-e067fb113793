/**
 * @file     cmd.h
 * @brief    命令头文件.
 * @details  This is the detail description.
 * <AUTHOR> 
 * @date     2022-12-23
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation 
 * @par History:
 *   version: author, date, descn
 */ 
#ifndef _CMD_H
#define _CMD_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

#include "parse_layer.h"

/*-----设备序号-----*/
#define     DEV_BMU     1
#define     DEV_BCMU    2
#define     DEV_BSMU    3
#define     DEV_DC_DC   4
#define     DEV_BMU_SOUTH   5

/* BMU命令ID:唯一标示一个命令 */
#define BMU_ADDR_ALLOCATION_PREPARE    1        ///<  站址分配准备
#define BMU_GET_ANA_DATA               2        ///<  获取实时数据模拟量
#define BMU_GET_SWITCH_DATA            3        ///<  获取实时数据开关量
#define BMU_GET_ALARM_DATA             4        ///<  获取实时数据告警量
#define BMU_CTRL_CELL_BALANCE          5        ///<  控制电芯均衡
#define BMU_GET_MANUFACTOR_DATA        6        ///<  获取厂家信息
#define BMU_ADDR_ALLOCATION            7        ///<  站址分配
#define BMU_UPDATE_BAK_TRIG            8        ///<  程序备份触发帧
#define BMU_UPDATE_BAK_DATA            9        ///<  程序备份传输帧
#define BMU_UPDATE_PRE_BAK_DATA       10        ///<  前级程序备份传输帧
#define BMU_UPDATE_PST_BAK_DATA       11        ///<  后级程序备份传输帧
#define BMU_UPDATE_DATA_TRIG          12        ///<  数据传输触发帧
#define BMU_UPDATE_DATA               13        ///<  程序更新数据传输帧
#define BMU_UPDATE_DATA_INTERRUPT     14        ///<  数据传输中断帧
#define BMU_UPDATE_TRIG               15        ///<  程序更新触发帧
#define BMU_UPDATE_TRIG_OLD           16        ///<  程序更新触发帧
#define BMU_GET_BALANCE_WORK_STATE    17        ///<  获取均衡电路故障检测状态（0：正常；1：短路；2：断路；FF：检测中）
#define BMU_UPDATE_DATA_COMPLETE_ACK  18        ///<  数据传输完成确认帧
#define BMU_UPDATE_CONFIRM            19        ///<  程序更新确认帧
#define BMU_BROAD_ADDR                20        ///<  广播地址
#define BMU_HEART_BEAT                21        ///<  BMU与优化器心跳建链
#define BMU_CONTROL_OPT_SHUTDOWN      22        ///<  BMU控制优化器关机
#define BMU_ADDRESS_ALLOCATION        23        ///<  BMU响应地址分配(优化器)
#define BMU_QTP_TRIGGER               24        ///<  BMU QTP触发帧
#define BMU_QTP_EXIT                  25        ///<  BMU QTP退出帧
#define BMU_QTP_GET_ANA_DATA          26        ///<  QTP获取实时数据模拟量
#define BMU_QTP_GET_SWITCH_DATA       27        ///<  QTP获取实时数据开关量
#define BMU_QTP_GET_ALARM_DATA        28        ///<  QTP获取实时数据告警量
#define BMU_QTP_GET_SOFTWARE_DATA     29        ///<  QTP获取实时数据软件版本
#define BMU_QTP_GET_MANUFACTOR_DATA   30        ///<  QTP获取实时数据厂家信息
#define BMU_QTP_SET_DEVICE_SN         31        ///<  QTP设置整机序列号
#define BMU_QTP_SET_CELL_MANU_FACTRUER   32        ///<  QTP设置电芯厂家名称
#define BMU_QTP_SET_BATT_DATE_TIME    33        ///<  QTP设置出厂日期
#define BMU_QTP_SET_CELL_CYCLE_TIMES  34        ///<  QTP设置电芯循环次数
#define BMU_QTP_SET_PACK_BAR_CODE     35        ///<  QTP设置PACK BAR CODE
#define BMU_TIME_SYNC                 36        ///<  时间同步
#define BMU_QTP_SET_SYS_NAME          37        ///<  QTP设置系统名称
#define BMU_QTP_SET_HARDWARE_VERSION  38        ///<  QTP设置硬件版本
#define BMU_CTRL_RESET                39        ///<  软件控制复位
#define BMU_SYNC_SOH_DATA             40        ///<  BCMU向BMU同步SOH数据
#define BMU_SYNC_SERIAL_NUM           41        ///<  BCMU向BMU同步序列号
#define BMU_GET_SOH_DATA              42        ///<  BCMU获取BMU保存的备件替换SOH数据
#define BMU_GET_SERIAL_NUM            43        ///<  BCMU获取BMU保存的备件替换序列号
#define BMU_QTP_SET_CELL_SN           44        ///<  QTP设置电芯序列号
#define BMU_CONTROL_OPT_SHUTDOWN      45        ///<  控制优化器电源
#define BMU_CTR_BROAD_OPT_ADDRESS     46        ///<  控制广播优化器地址

/* BCMU-BMU通讯自定义功能码 */
#define CMD_SYNC_SOH_DATA           0X2A     ///<  BCMU向BMU同步SOH数据
#define CMD_GET_SOH_DATA            0X2A     ///<  BCMU获取BMU中保存的SOH数据
#define CMD_SYNC_SERIAL_NUM         0X2B     ///<  BCMU向BMU同步序列号
#define CMD_GET_SERIAL_NUM          0X2B     ///<  BCMU获取BMU中保存的序列号

/* QTP命令ID:功能码 */
#define CMD_TRIGGER_EXIT_QTP        0x50     ///<  QTP触发/退出
#define CMD_GET_INFO_QTP            0x51     ///<  QTP查询命令
#define CMD_SET_INFO_QTP            0x52     ///<  QTP设置命令

/* QTP命令ID:附加码 */
#define CMD_TRIGGER                 0x10     ///<  QTP触发
#define CMD_EXIT                    0x11     ///<  QTP触发
#define CMD_GET_ANA_DATA            0x10     ///<  QTP获取实时数据模拟量
#define CMD_GET_SWITCH_DATA         0x11     ///<  QTP获取实时数据开关量
#define CMD_GET_ALARM_DATA          0x12     ///<  QTP获取实时数据告警量
#define CMD_GET_SOFTWARE_DATA       0x13     ///<  QTP获取实时数据软件版本
#define CMD_GET_MANUFAC_DATA        0x14     ///<  QTP获取实时数据厂家信息
#define CMD_SET_DEVICE_SN           0x10     ///<  QTP设置整机序列号
#define CMD_SET_CELL_MANU_FACTRUER  0x11     ///<  QTP设置电芯厂家名称
#define CMD_SET_BATT_DATE_TIME      0x12     ///<  QTP设置出厂日期
#define CMD_SET_CELL_CYCLE_TIMES    0x13     ///<  QTP设置电芯循环次数
#define CMD_SET_PACK_BAR_CODE       0x14     ///<  QTP设置PACK BAR CODE
#define CMD_SET_SYS_NAME            0x15     ///<  QTP设置系统名称
#define CMD_SET_HARDWARE_VERSION    0x16     ///<  QTP设置硬件版本
#define CMD_SET_CELL_SN             0x17     ///<  QTP设置电芯序列号
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _CMD_H
