#include "led.h"
#include "msg.h"
// #include <board.h>
#include <stdlib.h>
#include <string.h>
#include "msg_id.h"
#include <rtthread.h>
#include <rtdevice.h>
#include <board.h>
#include "data_type.h"
#include "signal_led.h"
#include "utils_thread.h"
#include "utils_server.h"
#include "utils_rtthread_security_func.h"
#include "protocol_north_modbus.h"

/* 定义信号灯对象句柄 */
Static led_t *led_run =  NULL;

/* 设置信号灯一个周期内的闪烁模式
 * 格式为 “亮、灭、亮、灭、亮、灭 …………”
 */
Static char* led_on_mode = "1000,0,";       ///< 常亮
Static char* led_off_mode = "0,1000,";      ///< 常灭
Static char* led_blink_mode = "500,500,";  ///< 1Hz闪烁
Static char* led_blink_mode_quick = "80,80,";  ///< 快闪烁

/* LED控制消息 */
Static module_msg_t msg_rcv = {.dest = MOD_LED};
static _rt_server_t g_sys_server = NULL;

Static void led_ctrl(_rt_msg_t msg_rcv);

/* 定义开灯函数 */
Static void led_run_switch_on(void *param) {
    rt_pin_write(PIN_LED_CONTROL, PIN_LOW);
}

/* 定义关灯函数 */
Static void led_run_switch_off(void *param) {
    rt_pin_write(PIN_LED_CONTROL, PIN_HIGH);
}

Static msg_map sample_msg_map[] =
{
     {LED_BLINK_CTRL_MSG, (void*)msg_handle_nothing},//临时添加解决编译问题
};

typedef struct {
    rt_uint32_t msg_id;
    void (*handle)(_rt_msg_t curr_msg);
} seub_msg_handle_process_t;

static seub_msg_handle_process_t s_msg_handle[] = {
    {LED_BLINK_CTRL_MSG,      led_ctrl},
};


/* 初始化闪烁led */
void* init_led_blink(void *param) {
    
	server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(sample_msg_map) / sizeof(msg_map);
    register_server_msg_map(sample_msg_map, server_info);
    register_send_msg_to_led_cb(send_msg_to_led);

    // main板init
    rt_pin_mode(PIN_LED_CONTROL, PIN_MODE_OUTPUT);
    led_run = led_create(led_run_switch_on, led_run_switch_off, NULL);
    RETURN_VAL_IF_FAIL(led_run != NULL, NULL);
    led_set_mode(led_run, LOOP_PERMANENT, led_blink_mode);
    led_start(led_run);

    init_msg_queue(MOD_LED);
    init_msg_queue(MOD_ALARM_MANAGE);
    return NULL;
}

/* led指示灯模式控制 */
Static int led_type_switch(led_t * led_type, unsigned char status)
{
    switch(status)
    {
        case LED_OFF_MODE:     led_set_mode(led_type, LOOP_PERMANENT, led_off_mode);break;
        case LED_ON_MODE :     led_set_mode(led_type, LOOP_PERMANENT, led_on_mode);break;
        case LED_BLINK_MODE:   led_set_mode(led_type, LOOP_PERMANENT, led_blink_mode);break;
        case LED_BLINK_MODE_QUICK:   led_set_mode(led_type, LOOP_PERMANENT, led_blink_mode_quick);break;
        default: return FALSE;
    }
    return TRUE;
}

/* 不同类型led指示灯控制（运行灯，告警灯） */
Static int led_no_switch(unsigned char led_no, unsigned char status) {
    // 运行灯执行
    if (led_no == LED_RUN_STATE) {
        return led_type_switch(led_run, status);
    }
    return FALSE;
}

/* led指示灯控制 */
Static void led_ctrl(_rt_msg_t msg_rcv) {
    unsigned char recv_data[2] = {0};
    rt_memcpy_s(&recv_data, sizeof(recv_data),  ((unsigned char *)(msg_rcv->msg.data)), sizeof(recv_data));
    led_no_switch(recv_data[0], recv_data[1]);
    return;
}


static void process_recv_msg(void) 
{
    int i = 0;
    if ((g_sys_server == NULL) || (rt_sem_take(&g_sys_server->msg_sem, 10) != RT_EOK) || (g_sys_server->msg_node == RT_NULL))
    {
        return;
    }

    _rt_msg_t curr_msg = g_sys_server->msg_node;

    for (i = 0; i < sizeof(s_msg_handle) / sizeof(s_msg_handle[0]); i++) {
        if (s_msg_handle[i].msg_id == curr_msg->msg.msg_id) {
            s_msg_handle[i].handle(curr_msg);
            break;
        }
    }

    rt_mutex_take(&g_sys_server->mutex, RT_WAITING_FOREVER);
    g_sys_server->msg_node = curr_msg->next;
    g_sys_server->msg_count--;
    rt_mutex_release(&g_sys_server->mutex);

    softbus_free(curr_msg);
}


void led_thread_entry(void* parameter) {
    g_sys_server = _curr_server_get();
    while (is_running(TRUE)){
        process_recv_msg();
        led_ticks();
        rt_thread_mdelay(LED_TICK_TIME);
    }
}
