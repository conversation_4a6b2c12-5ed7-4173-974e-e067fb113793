#ifndef _BCMU_MAIN_H
#define _BCMU_MAIN_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

#include "utils_server.h"
#include "server_id.h"

#define LED_THREAD_STACK_SIZE     512
#define CONTROL_MANAGE_THREAD_STACK_SIZE     1024
#define SYSTEM_MANAGE_THREAD_STACK_SIZE     1024
#define BMU_THREAD_STACK_SIZE     1024
#define SAMPLE_THREAD_STACK_SIZE     1024
#define BATT_MANAGE_THREAD_STACK_SIZE     1024
#define ALARM_MANAGE_THREAD_STACK_SIZE     1024
#define BSMU1_THREAD_STACK_SIZE     1024

int main(int argc, char *argv[]);
server_info_t* get_server_group(int *num);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _BCMU_MAIN_H


