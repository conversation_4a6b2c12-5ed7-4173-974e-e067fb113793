#include <rtthread.h>
#include <rtdevice.h>
#include <math.h>
#include <string.h>
#include "gd32f4xx_gpio.h"
#include "sample.h"
#include "msg.h"
#include "rt_drv_pwm.h"
#include "utils_server.h"
#include "utils_thread.h"
#include "utils_math.h"
#include "alarm_mgr_api.h"
#include "realdata_id_in.h"
#include "alarm_id_in.h"
#include "para_id_in.h"
#include "realdata_save.h"
#include "para_manage.h"
#include "his_record.h"
#include "dev_dcmu.h"
#include "data_type.h"
#include "adc_ctrl.h"
#include "const_define_in.h"
#include "utils_heart_beat.h"
#include "partition_def.h"
#include "his_record.h"
#include "storage.h"
#include "utils_rtthread_security_func.h"

static rt_timer_t power_timer = RT_NULL;
Static int seconds_count = 0;

// 互斥量定义
static struct rt_mutex extreme_data_mutex;
Static struct rt_device_pwm *s_pwm_dev = NULL;
Static float total_power = 0.0f;
Static float load_power[LOAD_NUM] = {0.0f};
Static float total_power_temp = 0.0f;
Static float load_power_temp[LOAD_NUM] = {0.0f};
Static his_extreme_data_t s_extreme_data = {0};

Static int get_dcmu_load_voltage(void);
Static int init_power(void);
Static short save_extreme_data(void);
Static float find_temperature_new(float resistance, int num, float min_temp, float max_temp, float* temp_table);
Static float temp_valid(float temp);
Static int calculate_total_power(void);
Static int calculate_load_power(void);
Static int save_power(void);
Static void power_timer_callback(void *parameter);
Static int power_timer_single_init(void);

static adc_pin_t s_select_adc[] = {
    {LOAD_CUR ,9, 0},
    {LOAD_VOLT,3, 0},
    {LOAD_CURB,8, 0},
    {LOAD_VOLB,0, 0},
    {LOAD_TEMP,12, 0},
};

static HumidityData env_humiditytab[] = {
    {10, 9410}, {20, 9256}, {30, 9102}, 
    {40, 8948}, {50, 8795}, {60, 8641}, 
    {70, 8487}, {80, 8333}, {90, 8179}, 
    {95, 8102}, 
};

//电阻温度对应表
static float ntc_table[306]=
{
    459591, 432381, 406957, 383189, 360961, 340163, 320695, 302464, 285383, 269374,     //-55~-46
    254362, 240281, 227066, 214659, 203007, 192059, 181768, 172091, 162988, 154422,     //-45~-36
    146358, 138763, 131608, 124865, 118507, 112511, 106854, 101515, 96474 , 91713 ,     //-35~-26
    87215 , 82963 , 78944 , 75143 , 71546 , 68143 , 64921 , 61870 , 58980 , 56241 ,     //-25~-16
    53645 , 51184 , 48849 , 46634 , 44532 , 42537 , 40642 , 38842 , 37132 , 35506 ,     //-15~-06
    33961 , 32491 , 31093 , 29763 , 28498 , 27293 , 26145 , 25052 , 24010 , 23018 ,     //-05~4
    22072 , 21169 , 20309 , 19488 , 18705 , 17957 , 17243 , 16561 , 15910 , 15288 ,     //5~14
    14693 , 14125 , 13582 , 13062 , 12565 , 12089 , 11634 , 11199 , 10781 , 10382 ,     //15~24
    10000 , 9633  , 9282  , 8945  , 8622  , 8313  , 8016  , 7731  , 7458  , 7196  ,     //25~34
    6944  , 6703  , 6471  , 6248  , 6034  , 5828  , 5631  , 5441  , 5258  , 5083  ,     //35~44
    4914  , 4751  , 4595  , 4445  , 4300  , 4160  , 4026  , 3897  , 3773  , 3653  ,     //45~54
    3538  , 3426  , 3319  , 3216  , 3116  , 3020  , 2927  , 2838  , 2751  , 2668  ,     //55~64
    2588  , 2510  , 2435  , 2363  , 2293  , 2226  , 2161  , 2098  , 2037  , 1978  ,     //65~74
    1921  , 1866  , 1813  , 1762  , 1712  , 1664  , 1618  , 1573  , 1529  , 1487  ,     //75~84
    1446  , 1407  , 1369  , 1332  , 1296  , 1261  , 1227  , 1195  , 1163  , 1133  ,     //85~94
    1103  , 1074  , 1046  , 1019  , 993   , 968   , 943   , 919   , 896   , 873   ,     //95~104
    851   , 830   , 809   , 789   , 770   , 751   , 733   , 715   , 697   , 681   ,     //105~114
    664   , 648   , 633   , 618   , 603   , 589   , 575   , 562   , 549   , 536   ,     //115~124
    524   , 512   , 500   , 489   , 478   , 467   , 456   , 446   , 436   , 427   ,     //125~134
    417   , 408   , 399   , 390   , 382   , 374   , 366   , 358   , 350   , 343   ,     //135~144
    335   , 328   , 321   , 315   , 308   , 302   , 295   , 289   , 283   , 278   ,     //145~154
    272   , 266   , 261   , 256   , 251   , 246   , 241   , 236   , 231   , 227   ,     //155~164
    222   , 218   , 214   , 210   , 205   , 202   , 198   , 194   , 190   , 187   ,     //165~174
    183   , 180   , 176   , 173   , 170   , 167   , 164   , 161   , 158   , 155   ,     //175~184
    152   , 149   , 146   , 144   , 141   , 139   , 136   , 134   , 131   , 129   ,     //185~194
    127   , 125   , 122   , 120   , 118   , 116   , 114   , 112   , 110   , 108   ,     //195~204
    107   , 105   , 103   , 101   , 100   , 98    , 96    , 95    , 93    , 92    ,     //205~214
    90    , 89    , 87    , 86    , 84    , 83    , 82    , 80    , 79    , 78    ,     //215~224
    77    , 76    , 74    , 73    , 72    , 71    , 70    , 69    , 68    , 67    ,     //225~234
    66    , 65    , 64    , 63    , 62    , 61    , 60    , 59    , 58    , 57    ,     //235~244
    56    , 56    , 55    , 54    , 53    , 52    ,                                     //245~250
};

Static msg_map sample_msg_map[] = {
    {0, NULL}, // 临时添加解决编译问题
};


/* 静态初始化DI通道信息数组（防抖时间统一设置为50ms） */
static DI_CHANNEL_INFO_STRUCT s_di_sample_info[DI_END] = {
    {DI1, WATER_SENSOR_PIN,  {.last_state=0, .curr_state=0, .last_time=0, .debounce_ms=50}},
    {DI2, DOOR_SENSOR_PIN,   {.last_state=0, .curr_state=0, .last_time=0, .debounce_ms=50}},
    {DI3, SMOKE_SENSOR_PIN,  {.last_state=0, .curr_state=0, .last_time=0, .debounce_ms=50}},
    {DI4, DC_PRO_LIGHT_SENSOR_PIN, {.last_state=0, .curr_state=0, .last_time=0, .debounce_ms=50}},
    {DI5, RLY_IN1_PIN, {.last_state=0, .curr_state=0, .last_time=0, .debounce_ms=50}},
    {DI6, RLY_IN2_PIN, {.last_state=0, .curr_state=0, .last_time=0, .debounce_ms=50}},
    {DI7, RLY_IN3_PIN, {.last_state=0, .curr_state=0, .last_time=0, .debounce_ms=50}},
    {DI8, RLY_IN4_PIN, {.last_state=0, .curr_state=0, .last_time=0, .debounce_ms=50}},
};

Static int pwm_device_find(void)
{
    s_pwm_dev = (struct rt_device_pwm *)rt_device_find(PWM_DEV_NAME);
    if(s_pwm_dev == NULL)
    {
        return FAILURE;
    }
    rt_device_open((rt_device_t)s_pwm_dev,  RT_DEVICE_OFLAG_RDONLY);
   
    return SUCCESSFUL;
}

/*
 * @brief 初始化防抖状态结构体
 * @param db 防抖状态指针
 * @param pin 关联的GPIO引脚号
 */
static void debounce_init(sensor_debounce_t *db, rt_base_t pin) {
    /* 初始化时读取当前引脚状态作为基准 */
    db->last_state = rt_pin_read(pin);
    db->curr_state = db->last_state;
    db->last_time = rt_tick_get();
}

/**
 * @brief 初始化数字输入GPIO及防抖状态
 */
static void di_gpio_init() {
    /* 配置传感器引脚为输入模式 */
    rt_pin_mode(WATER_SENSOR_PIN, PIN_MODE_INPUT);
    rt_pin_mode(DOOR_SENSOR_PIN, PIN_MODE_INPUT);
    rt_pin_mode(SMOKE_SENSOR_PIN, PIN_MODE_INPUT);
    rt_pin_mode(DC_PRO_LIGHT_SENSOR_PIN, PIN_MODE_INPUT);
    rt_pin_mode(RLY_IN1_PIN, PIN_MODE_INPUT);
    rt_pin_mode(RLY_IN2_PIN, PIN_MODE_INPUT);
    rt_pin_mode(RLY_IN3_PIN, PIN_MODE_INPUT);
    rt_pin_mode(RLY_IN4_PIN, PIN_MODE_INPUT);

    /* 动态初始化各通道防抖状态 */
    for (int i = DI1; i < DI_END; i++) {
        debounce_init(&s_di_sample_info[i].debounce, s_di_sample_info[i].di_pin);
    }
}

/**
 * @brief 初始化熔丝检测相关GPIO
 */
static void fuse_gpio_init() {
    /* 配置多路复用器控制引脚 */
    rt_pin_mode(MUX_S0, PIN_MODE_OUTPUT);  // MUX通道选择Bit0
    rt_pin_mode(MUX_S1, PIN_MODE_OUTPUT);  // MUX通道选择Bit1
    rt_pin_mode(MUX_S2, PIN_MODE_OUTPUT);  // MUX通道选择Bit2

    /* 配置SGM4581芯片片选引脚 */
    rt_pin_mode(FUSE_CS1, PIN_MODE_INPUT);  // 芯片1片选
    rt_pin_mode(FUSE_CS2, PIN_MODE_INPUT);  // 芯片2片选
    rt_pin_mode(FUSE_CS3, PIN_MODE_INPUT);  // 芯片3片选
}


void pin_init(void) {
    rt_pin_mode(SWITCH_ADDR_A0, PIN_MODE_OUTPUT);
    rt_pin_mode(SWITCH_ADDR_A1, PIN_MODE_OUTPUT);
    rt_pin_mode(SWITCH_ADDR_A2, PIN_MODE_OUTPUT);
    rt_pin_mode(SWITCH_ADDR_A3, PIN_MODE_OUTPUT);
    rt_pin_mode(SWITCH_ADDR_A4, PIN_MODE_OUTPUT);
}

void* sample_init(void *param) {
    server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(sample_msg_map) / sizeof(msg_map);
    register_server_msg_map(sample_msg_map, server_info);
    gpio_mode_set(GPIOB, GPIO_MODE_INPUT, GPIO_PUPD_NONE, GPIO_PIN_15);
    pin_init();
    di_gpio_init();
    fuse_gpio_init();
    init_power();
    // pwm_device_find();

    return NULL;
}

/**
 * @brief   重置极值数据为默认值
 * @param   time_now 当前时间戳
 * @return  void
 */
Static void reset_extreme_data_to_default(time_t time_now)
{
    s_extreme_data.total_load_curr_max          = 0;
    s_extreme_data.total_load_curr_max_time     = time_now;

    s_extreme_data.batt_discharge_curr_max      = 0;
    s_extreme_data.batt_discharge_curr_max_time = time_now;

    s_extreme_data.dc_volt_max                  = 0;
    s_extreme_data.dc_volt_max_time             = time_now;

    s_extreme_data.batt_temp_max                = -50;
    s_extreme_data.batt_temp_max_time           = time_now;

    s_extreme_data.env_temp_max                 = -50;
    s_extreme_data.env_temp_max_time            = time_now;

    s_extreme_data.env_temp_min                 = 120;
    s_extreme_data.env_temp_min_time            = time_now;
}

short init_extreme_data(void)
{
    time_t time_now;
    time(&time_now); // 获取当前时间

    // 初始化互斥量（只执行一次）
    rt_mutex_init(&extreme_data_mutex, "extreme_mtx", RT_IPC_FLAG_PRIO);

    if(pub_hisrecord_read_msg(RECORD_TYPE_EXTREME_DATA, 1, 0, &s_extreme_data) == FAILURE) // 极值记录读取失败
    {
        reset_extreme_data_to_default(time_now);

        return FAILURE;
    }

    return SUCCESSFUL;
}

/**
 * @description: 删除极值记录
 */
int delete_extreme_data(void)
{     
    time_t time_now;

    rt_mutex_take(&extreme_data_mutex, RT_WAITING_FOREVER);

    time(&time_now); // 获取当前时间 
    reset_extreme_data_to_default(time_now);
    pub_extremedata_save_msg(&s_extreme_data);

    rt_mutex_release(&extreme_data_mutex);
    return SUCCESSFUL;
}

/* 防抖处理函数 */
Static rt_bool_t debounce_read(DI_CHANNEL_INFO_STRUCT *di) {
    if (!di) return RT_FALSE;

    /* 读取当前引脚状态 */
    rt_bool_t new_state = rt_pin_read(di->di_pin);
    rt_tick_t current_time = rt_tick_get();
    rt_int32_t elapsed = (rt_int32_t)(current_time - di->debounce.last_time);

    /* 状态变化时更新记录 */
    if (new_state != di->debounce.curr_state) {
        di->debounce.curr_state = new_state;
        di->debounce.last_time = current_time;
    } 
    /* 满足去抖时间条件时确认状态 */
    else if (elapsed >= 0 && elapsed >= di->debounce.debounce_ms) {
        if (new_state != di->debounce.last_state) {
            di->debounce.last_state = new_state;
            return new_state;
        }
    }
    return di->debounce.last_state;
}

/* 通过通道号获取DI数据 */
Static rt_bool_t di_get_data_by_channel(eDI_SAMPLE channel) {
    if (channel >= DI_END) return RT_FALSE;
    return debounce_read(&s_di_sample_info[channel]);
}

/* 获取所有DI采样数据 */
int GetDiSampleData(void) {
    int i;
    rt_uint8_t data[DI_END] = {0};

    for(i = 0; i < DI_END; i++ ) {
        data[i] = (rt_uint8_t)di_get_data_by_channel((eDI_SAMPLE)i);
    }
    data[DI3] = !data[DI3];
    /* 设置各传感器数据到DCMU */
    set_one_data(DCMU_DATA_ID_DC_FLOOD_SENSOR,       &data[DI1]);
    set_one_data(DCMU_DATA_ID_DC_DOORMAG_SENSOR,     &data[DI2]);
    set_one_data(DCMU_DATA_ID_DC_FUMES_SENSOR,       &data[DI3]);
    set_one_data(DCMU_DATA_ID_DC_LIGHTNIN_PROTECTION,&data[DI4]);
    set_one_data(DCMU_DATA_ID_INPUT_RELAY, &data[DI5]);
    set_one_data(DCMU_DATA_ID_INPUT_RELAY + 1, &data[DI6]);
    set_one_data(DCMU_DATA_ID_INPUT_RELAY + 2, &data[DI7]);
    set_one_data(DCMU_DATA_ID_INPUT_RELAY + 3, &data[DI8]);
    return 0;
}

/* 设置多路复用器通道 */
void set_mux_channel(uint8_t channel) {
    rt_pin_write(MUX_S0, channel & 0x01);
    rt_pin_write(MUX_S1, (channel >> 1) & 0x01);
    rt_pin_write(MUX_S2, (channel >> 2) & 0x01);
}

/* 读取指定芯片和通道的状态 */
rt_uint8_t read_state(uint8_t chip, uint8_t channel) {
    /* 1. 配置多路复用器通道 */
    set_mux_channel(channel);

    /* 2. 延时确保信号稳定 */
    rt_thread_mdelay(2);

    rt_uint8_t state;
    switch (chip) {
        case 1: state = rt_pin_read(FUSE_CS1); break;
        case 2: state = rt_pin_read(FUSE_CS2); break;
        case 3: state = rt_pin_read(FUSE_CS3); break;
        default: return 0xFF;  // 错误值
    }

    return state;
}

/* 读取所有熔丝状态 */
int fuse_read_all_states() {
    rt_uint8_t states[3][8]; // 二维数组存储3个芯片×8个通道的状态

    /* 遍历所有芯片和通道 */
    for (rt_uint8_t chip = 1; chip <= 3; chip++) {
        for (rt_uint8_t ch = 0; ch < 8; ch++) {
            states[chip-1][ch] = read_state(chip, ch);
            states[chip-1][ch] = !states[chip-1][ch];
            /* 将状态数据存入DCMU */
            set_one_data(DCMU_DATA_ID_LOAD_LOOP_CONFIG + ((chip - 1) * 8) + ch, &states[chip-1][ch]);
        }
    }
    return 0;
}

Static float temp_valid(float temp)
{
    float result = temp;
    if(temp > 110.1f)
    {
        result = 110.1;
    }
    else if (temp < -50.1f)
    {
        result = -50.1;
    }
    return result;
}

Static int get_dcmu_adc_sample(void)
{
    float adc_volt = 0.0f, current = 0.0f, env_temp = 0.0f;
    char s8_env_temp = 0;
    safe_sid_data_t env_temp_zero = {0};

    // IB参数（0-3对应IB1-IB4）
    float ib_zero[BATT_NUM] = {0.0f};
    float ib_slope[BATT_NUM] = {0.0f};
    short ib_shunt[BATT_NUM] = {0};
    unsigned char ib_config[BATT_NUM] = {0};
    // VB参数（0-3对应VB1-VB4）
    float vb_zero[BATT_NUM] = {0.0f};
    // IL参数
    float il_zero[LOAD_NUM] = {0.0f};
    float il_slope[LOAD_NUM] = {0.0f};
    short il_shunt[LOAD_NUM] = {0};
    unsigned char il_config[LOAD_NUM] = {0};
    // NTC的电压（0-3对应TEMP1-TEMP4）
    float v_ntc[BATT_NUM] = {0.0f};
    //NTC的阻值
    float r_ntc[BATT_NUM] = {0.0f};
    //NTC的温度
    float temp_ntc[BATT_NUM] = {0.0f};
    char temp_ntc_zero[BATT_NUM] = {0};
    char buff[MAX_LETTERS];

    rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));

    // 查找ADC设备
    rt_adc_device_t adc_dev = (rt_adc_device_t)rt_device_find(ADC_DEV_NAME);
    RETURN_VAL_IF_FAIL(adc_dev != NULL, FAILURE);

    // 获取校准参数
    for (uint8_t i = 0; i < LOAD_NUM; i++) {
        get_one_para(DCMU_PARA_ID_LOAD_CURRENT_ZERO_OFFSET + i, &il_zero[i]);
        get_one_para(DCMU_PARA_ID_LOAD_CURRENT_SLOPE_OFFSET + i, &il_slope[i]);
        get_one_para(DCMU_PARA_ID_LOAD_SHUNT_OFFSET + i, &il_shunt[i]);
        get_one_para(DCMU_PARA_ID_LOAD_CONFIG_OFFSET + i, &il_config[i]);
    }
    
    for (uint8_t i = 0; i < BATT_NUM; i++) {
        get_one_para(DCMU_PARA_ID_BATTERY_CURRENT_ZERO_OFFSET + i, &ib_zero[i]);
        get_one_para(DCMU_PARA_ID_BATTERY_CURRENT_SLOPE_OFFSET + i, &ib_slope[i]);
        get_one_para(DCMU_PARA_ID_BATTERY_VOLT_ZERO_OFFSET + i, &vb_zero[i]);
        get_one_para(DCMU_PARA_ID_BATTERY_SHUNT_OFFSET + i, &ib_shunt[i]);
        get_one_para(DCMU_PARA_ID_BATTERY_CONFIG_OFFSET + i, &ib_config[i]);
    }

    rt_adc_enable(adc_dev, s_select_adc[LOAD_CUR].select_channel);
    rt_adc_enable(adc_dev, s_select_adc[LOAD_CURB].select_channel);
    rt_adc_enable(adc_dev, s_select_adc[LOAD_VOLB].select_channel);
    rt_adc_enable(adc_dev, s_select_adc[LOAD_TEMP].select_channel);

    // 25路循环采集（0-24）
    for (uint8_t i = 0; i < LOAD_NUM + 1; i++) {
        // 设置5个片选信号（A4~A0）
        rt_pin_write(SWITCH_ADDR_A4, ((i >> 4) & 0x01));
        rt_pin_write(SWITCH_ADDR_A3, ((i >> 3) & 0x01));
        rt_pin_write(SWITCH_ADDR_A2, ((i >> 2) & 0x01));
        rt_pin_write(SWITCH_ADDR_A1, ((i >> 1) & 0x01));
        rt_pin_write(SWITCH_ADDR_A0, (i & 0x01));

        rt_thread_mdelay(10);  // 等待多路开关稳定

        // 1. 采集负载电流（第24路不采集）
        if (i != 24) {
            adc_volt = get_adc_volt_new(adc_dev, s_select_adc[LOAD_CUR].select_channel);
            current = (adc_volt * 1000 / 111.0f * (il_shunt[i] / 25.0f)) * il_slope[i] + il_zero[i];
            current = il_config[i] ? current : 0;
            set_one_data(DCMU_DATA_ID_LOAD_CURRENT + i, &current);
            rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));
            rt_snprintf_s( buff, sizeof(buff), "LI%d:%5.3f %5.1f",i+1, adc_volt, current );
            SetTraceStr( 32 + i, buff );
        }

        // 2. 仅在特定循环点采集IB系列
        switch (i) {
            case 0:  // IB1：仅第0次循环
                adc_volt = get_adc_volt_new(adc_dev, s_select_adc[LOAD_CURB].select_channel);
                current = ((adc_volt - 1.5f) * 1000.0f / 57.0f * (ib_shunt[0] / 25.0f)) * ib_slope[0] + ib_zero[0];
                current = ib_config[0] ? current : 0;
                set_one_data(DCMU_DATA_ID_BATTERY_CURRENT + 0, &current);
                rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));
                rt_snprintf_s( buff, sizeof(buff), "BI%d:%5.3f %5.1f",i/8 + 1, adc_volt, current );
                SetTraceStr( 16, buff );
                break;
            case 8:  // IB2：仅第8次循环
                adc_volt = get_adc_volt_new(adc_dev, s_select_adc[LOAD_CURB].select_channel);
                current = ((adc_volt - 1.5f) * 1000.0f / 57.0f * (ib_shunt[1] / 25.0f)) * ib_slope[1] + ib_zero[1];
                current = ib_config[1] ? current : 0;
                set_one_data(DCMU_DATA_ID_BATTERY_CURRENT + 1, &current);
                rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));
                rt_snprintf_s( buff, sizeof(buff), "BI%d:%5.3f %5.1f",i/8 + 1, adc_volt, current );
                SetTraceStr( 17, buff );
                break;
            case 16: // IB3：仅第16次循环
                adc_volt = get_adc_volt_new(adc_dev, s_select_adc[LOAD_CURB].select_channel);
                current = ((adc_volt - 1.5f) * 1000.0f / 57.0f * (ib_shunt[2] / 25.0f)) * ib_slope[2] + ib_zero[2];
                current = ib_config[2] ? current : 0;
                set_one_data(DCMU_DATA_ID_BATTERY_CURRENT + 2, &current);
                rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));
                rt_snprintf_s( buff, sizeof(buff), "BI%d:%5.3f %5.1f",i/8 + 1, adc_volt, current );
                SetTraceStr( 18, buff );
                break;
            case 24: // IB4：仅第24次循环
                adc_volt = get_adc_volt_new(adc_dev, s_select_adc[LOAD_CURB].select_channel);
                current = ((adc_volt - 1.5f) * 1000.0f / 57.0f * (ib_shunt[3] / 25.0f)) * ib_slope[3] + ib_zero[3];
                current = ib_config[3] ? current : 0;
                set_one_data(DCMU_DATA_ID_BATTERY_CURRENT + 3, &current);
                rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));
                rt_snprintf_s( buff, sizeof(buff), "BI%d:%5.3f %5.1f",i/8 + 1, adc_volt, current );
                SetTraceStr( 19, buff );
                break;
            default:
                break;
        }

        // 3. 仅在0~3次循环采集VB系列 ntc的电压
        if (i <= 3) {
            adc_volt = get_adc_volt_new(adc_dev, s_select_adc[LOAD_VOLB].select_channel);
            float vb_volt = (adc_volt * 20.0f) + vb_zero[i];
            set_one_data(DCMU_DATA_ID_BATTERY_VOLTAGE + i, &vb_volt);
            v_ntc[i] = get_adc_volt_new(adc_dev, s_select_adc[LOAD_TEMP].select_channel);
            rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));
            rt_snprintf_s( buff, sizeof(buff), "BV%d:%5.3f %5.1f",i+1, adc_volt, vb_volt );
            SetTraceStr( 20+i, buff );
        }

        if (i == 4) {
            adc_volt = get_adc_volt_new(adc_dev, s_select_adc[LOAD_TEMP].select_channel);
            env_temp = -1481.96 + sqrt((2.1962 * 3.88 + (1.8639 - adc_volt)) / 3.88) * 1000;
            get_one_para(DCMU_PARA_ID_ENV_TEMP_ZERO_POINT_OFFSET, &env_temp_zero.c_val);
            env_temp = temp_valid(env_temp);
            env_temp = env_temp + env_temp_zero.c_val;
            s8_env_temp = round(env_temp);
            set_one_data(DCMU_DATA_ID_ENV_TEMP , &s8_env_temp);
            rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));
            rt_snprintf_s( buff, sizeof(buff), "ET:%4.3f %4.1f", adc_volt, env_temp);
            SetTraceStr( 29, buff );
        }

        // 4. 仅在6次循环采集ntc的供电电压，计算ntc的阻值
         if (i ==6)
        {
            adc_volt = get_adc_volt_new(adc_dev, s_select_adc[LOAD_TEMP].select_channel);
            for (int m = 0; m < BATT_NUM; m++) {
                // 输入有效性检查
                if (adc_volt > 0.0f && v_ntc[m] > 0.0f) {
                    r_ntc[m] = (adc_volt * 2.0f * 5.11f) / v_ntc[m] - 8.57f;
                    temp_ntc[m] = find_temperature_new(1000*r_ntc[m], sizeof(ntc_table) / sizeof(int32_t), DEFAULT_TEMP_MIN, DEFAULT_TEMP_MAX, ntc_table);
                    get_one_para(DCMU_PARA_ID_BATTERY_TEMP_ZERO_OFFSET + m, &temp_ntc_zero[m]);
                    temp_ntc[m] = temp_ntc[m] + temp_ntc_zero[m];
                    set_one_data(DCMU_DATA_ID_BATTERY_TEMPERATURE + m, &temp_ntc[m]);
                }
                else
                {
                    temp_ntc[m] = TEMPERATURE_INVALID;
                    set_one_data(DCMU_DATA_ID_BATTERY_TEMPERATURE + m, &temp_ntc[m]);
                }
                rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));
                rt_snprintf_s(buff, sizeof(buff), "BT%d:%5.3f %5.1f", m + 1, adc_volt, temp_ntc[m]);
                SetTraceStr(24 + m, buff);
            }
        }
    }

    rt_adc_disable(adc_dev, s_select_adc[LOAD_CUR].select_channel);
    rt_adc_disable(adc_dev, s_select_adc[LOAD_CURB].select_channel);
    rt_adc_disable(adc_dev, s_select_adc[LOAD_VOLB].select_channel);
    rt_adc_disable(adc_dev, s_select_adc[LOAD_TEMP].select_channel);

    return SUCCESSFUL;
}

Static float find_temperature_new(float resistance, int num, float min_temp, float max_temp, float* temp_table){
    // 检查是否超出物理测量范围
    if (resistance > temp_table[0]) {
        return TEMPERATURE_INVALID;  // 阻值过高（对应超低温）
    }
    
    if (resistance < temp_table[num - 1]) {
        return TEMPERATURE_INVALID;  // 阻值过低（对应超高温）
    }

    // 二分查找定位阻值区间 [left, right]
    int left = 0;
    int right = num - 1;
    
    while (left < right - 1) {
        int mid = left + (right - left) / 2;  // 防止整数溢出
        
        if (temp_table[mid] > resistance) {
            left = mid;  // 目标在右半区
        } else {
            right = mid; // 目标在左半区
        }
    }

    // 线性插值计算温度
    float r_high = temp_table[left];   // 高阻值对应低温
    float r_low  = temp_table[right];  // 低阻值对应高温
    
    // 温度差值等于索引差值（等间距假设）
    float temp_high = min_temp + left;
    float temp_low  = min_temp + right;

    // 计算电阻-温度线性关系
    float temperature = temp_high + (resistance - r_high) * (temp_low - temp_high) / (r_low - r_high);

    // 特殊温度区间映射
    if (temperature <= -40.0f) {
        return -40.0f;  // [-55, -40] 映射到 -40
    }
    
    if (temperature >= 100.0f) {
        return 100.0f;  // [100, 250] 映射到特殊码值
    }

    return temperature;  // 正常温度范围返回插值结果
}

// Static int get_dcmu_env_hum(void)
// {
//     static int count = 0;
//     static int frequency_buffer[10] = {0};
//     int frequency = 0, avg_frequency = 0 ;
//     float humidity = 0, humidityDiff = 0, frequencyDiff = 0, ratio = 0;
//     char humidity_zero = 0;
//     struct rt_pwm_configuration pwm_config = {0};
//     char buff[MAX_LETTERS];

//     if(s_pwm_dev == RT_NULL)
//     {
//         return FAILURE;
//     }

//     rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));

//     pwm_config.channel = 1;
//     rt_device_control((rt_device_t)s_pwm_dev, PWM_CMD_GET, &pwm_config);
//     frequency = pwm_config.pulse;

//     if (count < 10)
//     {
//         frequency_buffer[count++] = frequency;
//     }
//     else
//     {
//         for (int i = 0; i < 9; i++)
//         {
//             frequency_buffer[i] = frequency_buffer[i + 1];
//         }
//         frequency_buffer[9] = frequency;
//     }

//     for (int i = 0; i < count; i++)
//     {
//         avg_frequency += frequency_buffer[i];
//     }

//     frequency = avg_frequency / count;
//     for (int i = 0; i < (ARR_SIZE(env_humiditytab) -1); i++) {
//         if (frequency <= env_humiditytab[i].frequency && frequency >= env_humiditytab[i + 1].frequency) {
//             // 使用线性插值计算精准湿度
//             humidityDiff = env_humiditytab[i + 1].humidity - env_humiditytab[i].humidity;
//             frequencyDiff = env_humiditytab[i + 1].frequency - env_humiditytab[i].frequency;
//             ratio = (float)(frequency - env_humiditytab[i].frequency) / frequencyDiff;
//             humidity = env_humiditytab[i].humidity + ratio * humidityDiff;
//             break;
//         }
//     }

//     get_one_para(DCMU_PARA_ID_ENV_HUMIDITY_ZERO_POINT_OFFSET, &humidity_zero);
//     humidity = humidity + humidity_zero;

//     set_one_data(DCMU_DATA_ID_ENV_HUMIDITY, &humidity);
//     rt_snprintf_s( buff, sizeof(buff), "EH:%u %4.3f %4.1f", frequency, humidity);
//     SetTraceStr( 30, buff );

//     return SUCCESSFUL;
// }

Static int get_dcmu_load_voltage(void)
{
    float adc_volt = 0,voltage = 0,zero_point = 0;
    char buff[MAX_LETTERS];

    rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));

    rt_adc_device_t adc_dev = (rt_adc_device_t)rt_device_find(ADC2_DEV_NAME);
    RETURN_VAL_IF_FAIL(adc_dev != NULL,FAILURE);

    get_one_para(DCMU_PARA_ID_DC_VOLT_ZERO_OFFSET,&zero_point);
    //读取电压
    rt_adc_enable(adc_dev, s_select_adc[LOAD_VOLT].select_channel);
    adc_volt = get_adc_volt_new(adc_dev,s_select_adc[LOAD_VOLT].select_channel);
    rt_adc_disable(adc_dev, s_select_adc[LOAD_VOLT].select_channel);

    //转换为负载电压
    voltage = (adc_volt * 23.75f) + zero_point;
    set_one_data(DCMU_DATA_ID_DC_OUTPUT_VOLTAGE, &voltage);

    rt_snprintf_s(buff, sizeof(buff), "DV:%5.3f %5.1f", adc_volt, voltage);
    SetTraceStr(28, buff);

    return 0;
}

Static int calculate_total_power(void)
{
    float output_voltage, total_load_current=0.0f;
    float temp = 0.0f;

    get_one_data(DCMU_DATA_ID_DC_OUTPUT_VOLTAGE, &output_voltage);
    get_one_data(DCMU_DATA_ID_TOTAL_LOAD_CURRENT, &total_load_current);

    if ((output_voltage >= 0) && (total_load_current >= 0)) {
        total_power_temp = total_power_temp + (output_voltage * total_load_current);
        temp = total_power_temp / 3600000.0f;   // kWh

        if (total_power >= 90000) {
            if (temp >= 1) {
                total_power = total_power + temp;
                temp = 0;
                total_power_temp = 0;
            }
        } else if (total_power >= 900) {
            if (temp >= 0.01) {
                total_power = total_power + temp;
                temp = 0;
                total_power_temp = 0;
            }
        } else if (total_power >= 0) {
            if (temp >= 0.0001) {
                total_power = total_power + temp;
                temp = 0;
                total_power_temp = 0;
            }
        }

        if ((total_power >= 9000000) || (total_power < 0)) {       // 溢出处理
            total_power = 0;
            for (int i = 0; i < LOAD_NUM; i++) {
                load_power[i] = 0;
            }
            save_power(); // 电量计满后保持电量，防止掉电电量丢失，造成CSU电量计算错误
        }
        set_one_data(DCMU_DATA_ID_TOTAL_ELECTRIC_QUANTITY, &total_power);
    }
    return SUCCESSFUL;
}

Static int calculate_load_power(void)
{
    float output_voltage, total_load_current = 0.0f;
    float load_current[LOAD_NUM] = {0.0f};
    float temp = 0.0f;
    unsigned char load_loop_config = 0;
    int i = 0;

    get_one_data(DCMU_DATA_ID_DC_OUTPUT_VOLTAGE, &output_voltage);
    get_one_data(DCMU_DATA_ID_TOTAL_LOAD_CURRENT, &total_load_current);

    if ((output_voltage >= 0) && (total_load_current >= 0)) {
        for (i = 0; i < LOAD_NUM; i++) {
            get_one_data(DCMU_DATA_ID_LOAD_CURRENT + i, &load_current[i]);
            get_one_para(DCMU_PARA_ID_LOAD_CONFIG_OFFSET + i, &load_loop_config);

            if (load_current[i] >= 0 && load_loop_config) {
                load_power_temp[i] = load_power_temp[i] + (output_voltage * load_current[i]);
                temp = load_power_temp[i] / 3600000.0f;   // kWh

                if (load_power[i] >= 90000) {
                    if (temp >= 1) {
                        load_power[i] = load_power[i] + temp;
                        temp = 0;
                        load_power_temp[i] = 0;
                    }
                } else if (load_power[i] >= 900) {
                    if (temp >= 0.01) {
                        load_power[i] = load_power[i] + temp;
                        temp = 0;
                        load_power_temp[i] = 0;
                    }
                } else if (load_power[i] >= 0) {
                    if (temp >= 0.0001) {
                        load_power[i] = load_power[i] + temp;
                        temp = 0;
                        load_power_temp[i] = 0;
                    }
                }

                if ((load_power[i] >= 9000000) || (load_power[i] < 0)) {       // 溢出处理
                    load_power[i] = 0;
                    save_power(); // 电量计满后保持电量，防止掉电电量丢失，造成CSU电量计算错误
                }
                set_one_data(DCMU_DATA_ID_LOAD_BATTERY + i, &load_power[i]);
            }
        }
    }
    return SUCCESSFUL;
}

Static int calculate_total_load_current(void)
{
    float total_load_current = 0.0f;
    float load_current = 0.0f;
    unsigned char load_loop_config = 0;
    for (int i = 0; i < LOAD_NUM; i++) {
        get_one_para(DCMU_PARA_ID_LOAD_CONFIG_OFFSET + i, &load_loop_config);
        if (load_loop_config) {
            get_one_data(DCMU_DATA_ID_LOAD_CURRENT + i, &load_current);
            total_load_current += load_current;
        }
    }
    if (total_load_current > LOAD_TOTAL_CURRENT_MAX)
    {
        total_load_current = LOAD_TOTAL_CURRENT_MAX;
    }
    if (total_load_current < LOAD_TOTAL_CURRENT_MIN)
    {
        total_load_current = LOAD_TOTAL_CURRENT_MIN;
    }
    set_one_data(DCMU_DATA_ID_TOTAL_LOAD_CURRENT, &total_load_current);

    return SUCCESSFUL;
}

// 保存电量数据到文件系统中
Static int save_power(void)
{
    unsigned char temp[100]={0};

    part_data_t part_data = {0};
    part_data.buff = &temp[0];
    part_data.len = sizeof(temp);
    part_data.offset = 0;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), "%s", POWER_DATA);
    rt_memcpy_s(&temp[0], sizeof(total_power), &total_power, sizeof(total_power));
    rt_memcpy_s(&temp[4], sizeof(load_power), &load_power[0], sizeof(load_power));
    if (SUCCESSFUL != storage_process(&part_data, write_opr))
    {
        return FAILURE;
    }
    return SUCCESSFUL;
}

// 初始化时从文件系统中读出保存的电量数据
Static int init_power(void)
{
    unsigned char temp[100]={0};

    part_data_t part_data = {0};
    part_data.buff = &temp[0];
    part_data.len = sizeof(temp);
    part_data.offset = 0;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), "%s", POWER_DATA);
    if (SUCCESSFUL != storage_process(&part_data, read_opr))
    {
        return FAILURE;
    }

    rt_memcpy_s(&total_power, sizeof(total_power), &temp[0], sizeof(total_power));
    rt_memcpy_s(&load_power[0], sizeof(load_power), &temp[4], sizeof(load_power));

    set_one_data(DCMU_DATA_ID_TOTAL_ELECTRIC_QUANTITY, &total_power);
    for (int i = 0; i < LOAD_NUM; i++) {
        set_one_data(DCMU_DATA_ID_LOAD_BATTERY + i, &load_power[i]);

    }
    return SUCCESSFUL;
}

// 删除总电量和分路电量
int clear_power_data(void)
{
    float f_data = 0.0f;
    unsigned char temp[100]={0};
    part_data_t part_data = {0};

    part_data.buff = &temp[0];
    part_data.len = sizeof(temp);
    part_data.offset = 0;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), "%s", POWER_DATA);

    if (SUCCESSFUL != storage_process(&part_data, write_opr))
    {
        return FAILURE;
    }

    total_power = f_data;
    total_power_temp = f_data;
    set_one_data(DCMU_DATA_ID_TOTAL_ELECTRIC_QUANTITY, &total_power);
    for(int i=0; i<LOAD_NUM; i++)
    {
        load_power[i] = f_data;
        load_power_temp[i] = f_data;
        set_one_data(DCMU_DATA_ID_LOAD_BATTERY + i, &load_power[i]);
    }
    return SUCCESSFUL;
}

// 删除单路分路电量
int clear_load_power_data(unsigned char number)
{
    float f_data = 0.0f;
    unsigned char temp[4]={0};
    part_data_t part_data = {0};

    if(number >= LOAD_NUM)
    {
        return FAILURE;
    }

    part_data.buff = &temp[0];
    part_data.len = sizeof(temp);
    part_data.offset = 4+number*sizeof(temp);   // 偏移地址：总电量占前四个字节，根据number定位到分路电量
    rt_snprintf_s(part_data.name, sizeof(part_data.name), "%s", POWER_DATA);

    if (SUCCESSFUL != storage_process(&part_data, write_opr))
    {
        return FAILURE;
    }

    load_power[number] = f_data;
    load_power_temp[number] = f_data;
    set_one_data(DCMU_DATA_ID_LOAD_BATTERY + number, &load_power[number]);

    return SUCCESSFUL;
}

/**
 * @description: 保存极值记录
 * @param {void} 无输入参数
 * @return {short} 成功返回SUCCESSFUL
 */
Static short save_extreme_data(void)
{
    unsigned char no;
    unsigned char extreme_flag = FALSE;
    unsigned char batt_pack_config[BATT_NUM] = {0}; // 电池组配置（4路）
    time_t save_time;
    float f_total_load_curr = 0.0;  // 负载总电流
    float f_dc_volt = 0.0;         // 直流电压
    float f_env_temp = 0.0;        // 环境温度
    float f_batt_curr[BATT_NUM] = {0.0};  // 电池电流（4路）
    float f_batt_temp[BATT_NUM] = {0.0};  // 电池温度（4路）

    short total_load_curr = 0;
    short dc_volt = 0;
    char env_temp = 0;
    short batt_curr[BATT_NUM] = {0};
    char batt_temp[BATT_NUM] = {0};

    time(&save_time); // 获取当前时间

    get_one_data(DCMU_DATA_ID_TOTAL_LOAD_CURRENT, &f_total_load_curr);
    get_one_data(DCMU_DATA_ID_DC_OUTPUT_VOLTAGE, &f_dc_volt);
    get_one_data(DCMU_DATA_ID_ENV_TEMP, &env_temp);

    total_load_curr = (short)round(f_total_load_curr * 10); // 扩大10倍保存，与旧代码保持一致
    dc_volt = (short)round(f_dc_volt * 10); // 扩大10倍保存，与旧代码保持一致

    rt_mutex_take(&extreme_data_mutex, RT_WAITING_FOREVER);

    // 电池组数据采集
    for(no = 0; no < BATT_NUM; no++)
    {
        get_one_data(DCMU_DATA_ID_BATTERY_CURRENT + no, &f_batt_curr[no]);
        get_one_data(DCMU_DATA_ID_BATTERY_TEMPERATURE + no, &f_batt_temp[no]);

        batt_curr[no] = (short)round(f_batt_curr[no]  * 10); // 扩大10倍保存，与旧代码保持一致
        batt_temp[no] = (char)round(f_batt_temp[no]);
    }

    // 负载总电流最大值
    if (total_load_curr > s_extreme_data.total_load_curr_max) {
        s_extreme_data.total_load_curr_max = total_load_curr;
        s_extreme_data.total_load_curr_max_time = save_time;
        extreme_flag = TRUE;
    }

    // 电池参数极值
    for(no = 0; no < BATT_NUM; no++)
    {
        get_one_para(DCMU_PARA_ID_BATTERY_CONFIG_OFFSET + no, &batt_pack_config[no]);

        // 电池组已配置才记录极值
        if(batt_pack_config[no] == TRUE)
        {
            // 电池放电电流最大值（放电电流为负）
            if (batt_curr[no] < s_extreme_data.batt_discharge_curr_max) {
                s_extreme_data.batt_discharge_curr_max = batt_curr[no];
                s_extreme_data.batt_discharge_curr_max_time = save_time;
                extreme_flag = TRUE;
            }

            // 电池温度最大值
            if (batt_temp[no] > s_extreme_data.batt_temp_max) {
                s_extreme_data.batt_temp_max = batt_temp[no];
                s_extreme_data.batt_temp_max_time = save_time;
                extreme_flag = TRUE;
            }
        }
    }

    // 环境温度极值
    if (env_temp > s_extreme_data.env_temp_max) {
        s_extreme_data.env_temp_max = env_temp;
        s_extreme_data.env_temp_max_time = save_time;
        extreme_flag = TRUE;
    }
    if (env_temp < s_extreme_data.env_temp_min) {
        s_extreme_data.env_temp_min = env_temp;
        s_extreme_data.env_temp_min_time = save_time;
        extreme_flag = TRUE;
    }

    // 直流电压最大值
    if (dc_volt > s_extreme_data.dc_volt_max) {
        s_extreme_data.dc_volt_max = dc_volt;
        s_extreme_data.dc_volt_max_time = save_time;
        extreme_flag = TRUE;
    }

    if (extreme_flag == TRUE) {
        pub_extremedata_save_msg(&s_extreme_data);
    }

    rt_mutex_release(&extreme_data_mutex);

    return SUCCESSFUL;
}

/* Started by AICoder, pid:e27c5pd861pe677143e80b9b908745185357afe1 */
Static int get_dcmu_busbar_volt_before_power_off(void) {
    float record_limit = 48.0;
    float change = 0.5f;
    float volt = record_limit;
    static float last_volt = 48.0;

    get_one_data(DCMU_DATA_ID_DC_OUTPUT_VOLTAGE, &volt);

    if (volt > last_volt) {
        last_volt = volt;
    } else if (volt < record_limit && (last_volt - volt) > change) {
        set_one_para(DCMU_PARA_ID_BUSBAR_VOLT_BEFORE_POWER_OFF_OFFSET, &volt, TRUE, FALSE);
        save_power();
        last_volt = volt;
    }
    return SUCCESSFUL;
}
/* Ended by AICoder, pid:e27c5pd861pe677143e80b9b908745185357afe1 */

/* Started by AICoder, pid:6d0c8k60108f20c141b90bc6609d60305180f668 */
Static void power_timer_callback(void *parameter)
{
    // 每秒执行的任务
    calculate_total_power();
    calculate_load_power();
    
    seconds_count++;
    
    // 每小时执行的任务
    if (seconds_count >= ENERGY_SAVE_INTERVAL) {
        save_power();
        seconds_count = 0;
    }
}

Static int power_timer_single_init(void)
{
    power_timer = rt_timer_create("power_timer",
                                 power_timer_callback,
                                 RT_NULL,
                                 1000,  // 1秒
                                 RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
    
    if (power_timer == RT_NULL) {
        return -RT_ERROR;
    }
    
    rt_timer_start(power_timer);
    return RT_EOK;
}
/* Ended by AICoder, pid:6d0c8k60108f20c141b90bc6609d60305180f668 */

/* Started by AICoder, pid:54c1ex4036b8df614f5d0b871042901b57868db0 */
void sample_main(void* parameter) {
    init_extreme_data();
    thread_monitor_register("sample");
    power_timer_single_init();
    while (is_running(TRUE)) {
        thread_monitor_update_heartbeat();
        GetDiSampleData();
        fuse_read_all_states();
        get_dcmu_adc_sample();
        get_dcmu_load_voltage();
        get_dcmu_busbar_volt_before_power_off();
        // get_dcmu_env_hum();

        calculate_total_load_current();
        save_extreme_data();
        rt_thread_mdelay(SAMPLE_THREAD_DELAY_TIME);
    }
}
/* Ended by AICoder, pid:54c1ex4036b8df614f5d0b871042901b57868db0 */


// static int s_sample_test_flag = FALSE;

// void sample_main(void* parameter) {
//     init_extreme_data();
//     thread_monitor_register("sample");
//     power_timer_single_init();
//     while (is_running(TRUE)) {
//         thread_monitor_update_heartbeat();
//         GetDiSampleData();
//         fuse_read_all_states();
//         if(s_sample_test_flag == FALSE)
//         {
//             get_dcmu_adc_sample();
//             get_dcmu_load_voltage();
//             get_dcmu_env_hum();

//             calculate_total_load_current();
//         }
//         save_extreme_data();
//         rt_thread_mdelay(SAMPLE_THREAD_DELAY_TIME);

//     }
// }

// int set_sample_data(int argc, char *argv[])
// {
//     int ret=1;
//     float f_data;
//     char s8_data = 0;
//     int index;

//     if (strcmp(argv[1], "open") == 0)
//     {
//         //停止实际采用，启动模拟采样
//         s_sample_test_flag = TRUE;
//         rt_kprintf("set_sample_data open success\n");
//         ret = 0;
//     }
//     if (strcmp(argv[1], "close") == 0)
//     {
//         //启动实际采用，关闭模拟采样
//         s_sample_test_flag = FALSE;
//         rt_kprintf("set_sample_data close success\n");
//         ret = 0;
//     }

//     else if(strcmp(argv[1], "batt_curr") == 0 && argc >2 && s_sample_test_flag == TRUE)
//     {
//         f_data  = atof(argv[2]);
//         index = atoi(argv[3]);
//         if(index <= 3)
//         {
//             ret = set_one_data(DCMU_DATA_ID_BATTERY_CURRENT + index, &f_data);
//         }
//     }
//     else if(strcmp(argv[1], "batt_volt") == 0 && argc >2 && s_sample_test_flag == TRUE)
//     {
//         f_data  = atof(argv[2]);
//         index = atoi(argv[3]);
//         if(index <= 3)
//         {
//             ret = set_one_data(DCMU_DATA_ID_BATTERY_VOLTAGE + index, &f_data);
//         }
//     }
//     else if(strcmp(argv[1], "batt_temp") == 0 && argc >2 && s_sample_test_flag == TRUE)
//     {
//         f_data  = atof(argv[2]);
//         index = atoi(argv[3]);
//         if(index <= 3)
//         {
//             ret = set_one_data(DCMU_DATA_ID_BATTERY_TEMPERATURE + index, &f_data);
//         }
//     }
//     else if(strcmp(argv[1], "env_temp") == 0 && s_sample_test_flag == TRUE)
//     {
//         s8_data  = atof(argv[2]);
//         ret = set_one_data(DCMU_DATA_ID_ENV_TEMP, &s8_data);
//     }
//     else if(strcmp(argv[1], "env_humid") == 0 && s_sample_test_flag == TRUE)
//     {
//         s8_data  = atof(argv[2]);
//         ret = set_one_data(DCMU_DATA_ID_ENV_HUMIDITY, &s8_data);
//     }

//     else if(strcmp(argv[1], "branch_curr") == 0 && argc >2 && s_sample_test_flag == TRUE)
//     {
//         f_data  = atof(argv[2]);
//         index = atoi(argv[3]);
//         if(index < 24)
//         {
//             ret = set_one_data(DCMU_DATA_ID_LOAD_CURRENT + index, &f_data);
//         }
//     }

//     else if(strcmp(argv[1], "load_curr") == 0 && s_sample_test_flag == TRUE)
//     {
//         f_data  = atof(argv[2]);
//         ret = set_one_data(DCMU_DATA_ID_TOTAL_LOAD_CURRENT, &f_data);
//     }

//     else if(strcmp(argv[1], "output_volt") == 0 && s_sample_test_flag == TRUE)
//     {
//         f_data  = atof(argv[2]);
//         ret = set_one_data(DCMU_DATA_ID_DC_OUTPUT_VOLTAGE, &f_data);
//     }

//     get_one_data(DCMU_DATA_ID_BATTERY_CURRENT, &f_data);
//     rt_kprintf("batt_curr0:%f\n", f_data);
//     get_one_data(DCMU_DATA_ID_BATTERY_CURRENT+1, &f_data);
//     rt_kprintf("batt_curr1:%f\n", f_data);
//     get_one_data(DCMU_DATA_ID_BATTERY_CURRENT+2, &f_data);
//     rt_kprintf("batt_curr2:%f\n", f_data);
//     get_one_data(DCMU_DATA_ID_BATTERY_CURRENT+3, &f_data);
//     rt_kprintf("batt_curr3:%f\n", f_data);

//     get_one_data(DCMU_DATA_ID_BATTERY_VOLTAGE, &f_data);
//     rt_kprintf("batt_vol0:%f\n", f_data);
//     get_one_data(DCMU_DATA_ID_BATTERY_VOLTAGE+1, &f_data);
//     rt_kprintf("batt_vol1:%f\n", f_data);
//     get_one_data(DCMU_DATA_ID_BATTERY_VOLTAGE+2, &f_data);
//     rt_kprintf("batt_vol2:%f\n", f_data);
//     get_one_data(DCMU_DATA_ID_BATTERY_VOLTAGE+3, &f_data);
//     rt_kprintf("batt_vol3:%f\n", f_data);
    
//     get_one_data(DCMU_DATA_ID_BATTERY_TEMPERATURE, &f_data);
//     rt_kprintf("batt_temp0:%f\n", f_data);
//     get_one_data(DCMU_DATA_ID_BATTERY_TEMPERATURE+1, &f_data);
//     rt_kprintf("batt_temp1:%f\n", f_data);
//     get_one_data(DCMU_DATA_ID_BATTERY_TEMPERATURE+2, &f_data);
//     rt_kprintf("batt_temp2:%f\n", f_data);
//     get_one_data(DCMU_DATA_ID_BATTERY_TEMPERATURE+3, &f_data);
//     rt_kprintf("batt_temp3:%f\n", f_data);

//     get_one_data(DCMU_DATA_ID_ENV_TEMP, &s8_data);
//     rt_kprintf("env_temp:%d\n", s8_data);

//     get_one_data(DCMU_DATA_ID_ENV_HUMIDITY, &s8_data);
//     rt_kprintf("env_humid:%d\n", s8_data);

//     for(int i=0;i<24;i++)
//     {
//         get_one_data(DCMU_DATA_ID_LOAD_CURRENT + i, &f_data);
//         rt_kprintf("branch_curr:%f\n", f_data);
//     }

//     get_one_data(DCMU_DATA_ID_TOTAL_LOAD_CURRENT, &f_data);
//     rt_kprintf("load_curr:%f\n", f_data);

//     get_one_data(DCMU_DATA_ID_DC_OUTPUT_VOLTAGE, &f_data);
//     rt_kprintf("output_volt:%f\n", f_data);

//     if(ret)
//     {
//         rt_kprintf("set_sample_data input error\n");
//     }
//     return 0;
// }

// MSH_CMD_EXPORT(set_sample_data, set sample);//注册打桩测试函数