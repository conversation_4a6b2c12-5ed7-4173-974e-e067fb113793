#include "bduRecord.h"
#include "commBdu.h"
#include "fileSys.h"
#include "hisdata.h"

#include "thread_id.h"
#include "utils_heart_beat.h"
#include <rtdef.h>
#include <rtthread.h>

static struct rt_semaphore s_bduRecordNotFull;  /**< 缓冲区未满（可写）信号量 */
static struct rt_semaphore s_bduRecordNotEmpty; /**< 缓冲区非空（可读）信号量 */

/** 缓冲区设计为循环队列  */
static uint8_t s_aucBduRecordBuff[BDU_RECORD_BUFF_NUM][BDU_RECORD_BUFF_SIZE] = {0, };
static size_t s_pushIndex = 0;
static size_t s_popIndex = 0;

/** 存储序列化后的录波记录 */
static uint8_t s_aucPackedBduRecordBuff[BDU_RECORD_SIZE] = {0, };

static int BduRecordToBytes(void *pDest, const T_BduRecordData *ptData);

/**
 * @brief 初始化录波存储相关功能
 *
 * @note 需在BDU通讯线程和录波存储线程启动之前调用
 * 
 */
void InitBduRecordSaver(void)
{
    s_pushIndex = 0;
    s_popIndex = 0;
    rt_memset(&s_aucBduRecordBuff, 0, sizeof(s_aucBduRecordBuff));

    rt_sem_init(&s_bduRecordNotFull, "push", BDU_RECORD_BUFF_NUM, RT_IPC_FLAG_FIFO);
    rt_sem_init(&s_bduRecordNotEmpty, "pop", 0, RT_IPC_FLAG_FIFO);
}

/**
 * @brief 将一条录波记录放入缓冲区
 * 
 * @param[in] ptData 录波记录
 * @return int 操作结果（0：成功；-1：失败）
 */
int SaveOneBduRecord(const T_BduRecordData *ptData)
{
    if (ptData == NULL)
    {
        return -1;
    }

    if (rt_sem_take(&s_bduRecordNotFull, 10) != RT_EOK)
    {
        return -1;
    }

    rt_memcpy(s_aucBduRecordBuff[s_pushIndex], ptData, sizeof(*ptData));
    s_pushIndex = (s_pushIndex + 1) % BDU_RECORD_BUFF_NUM;

    rt_sem_release(&s_bduRecordNotEmpty);
    return 0;
}

/**
 * @brief 录波存储线程入口
 * 
 * @param parameter 未使用
 */
void BduRecordSaver(void *parameter)
{
    int len = 0;

    pre_thread_beat_f(THREAD_BDU_RECORD);

#ifdef UNITEST
    while (IsRunning())
#else
    while (1)
#endif
    {
        thread_beat_go_on(THREAD_BDU_RECORD);

        if (rt_sem_take(&s_bduRecordNotEmpty, 10) != RT_EOK)
        {
            // todo:
        }
        else
        {
            len = BduRecordToBytes(&s_aucPackedBduRecordBuff, (T_BduRecordData *)(s_aucBduRecordBuff[s_popIndex]));
            s_popIndex = (s_popIndex + 1) % BDU_RECORD_BUFF_NUM;
            rt_sem_release(&s_bduRecordNotFull);

            StoreBduRecord(&s_aucPackedBduRecordBuff, len);
        }

        rt_thread_mdelay(50);
    }
}

/**
 * @brief 将录波记录结构体编码至字节数组
 * 
 * @param[out] pDest 指向目的数组
 * @param[in] ptData 指向待转换的录波记录
 * @return int 编码后所占的字节数
 */
static int BduRecordToBytes(void *pDest, const T_BduRecordData *ptData)
{
    int count = 0;
    BYTE *pucBuff = (BYTE *)pDest;

    RT_ASSERT(pDest != RT_NULL && ptData != RT_NULL);

#define COPY_STRUCT_MEMBER(dest, member) ({rt_memcpy((dest), &(member), sizeof(member)); sizeof(member);})

    count += COPY_STRUCT_MEMBER(pucBuff, ptData->tTimeNow);
    count += COPY_STRUCT_MEMBER(pucBuff + count, ptData->ucTrigger);
    count += COPY_STRUCT_MEMBER(pucBuff + count, ptData->tRecordCfg.wRecordIntrvalFront);
    count += COPY_STRUCT_MEMBER(pucBuff + count, ptData->tRecordCfg.wRecordIntrvalAfter);
    count += COPY_STRUCT_MEMBER(pucBuff + count, ptData->tRecordCfg.ucRecordNumFront);
    count += COPY_STRUCT_MEMBER(pucBuff + count, ptData->tRecordCfg.ucRecordNumAfter);
    count += COPY_STRUCT_MEMBER(pucBuff + count, ptData->tRecordCfg.ucRecordPointNum);
    count += COPY_STRUCT_MEMBER(pucBuff + count, ptData->tRecordCfg.aucRecordPointID);
    count += COPY_STRUCT_MEMBER(pucBuff + count, ptData->awData);

#undef COPY_STRUCT_MEMBER

    return count;
}