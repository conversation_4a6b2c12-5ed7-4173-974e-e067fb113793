/**
 * @file     device_type.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2017-04-19
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */

#ifndef _DEVICE_BSMU_H
#define _DEVICE_BSMU_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "dev_bmu.h"

#define BCMU_SYS_NAME_LEN       32      ///< BCMU 系统名称数组长度
#define BCMU_SOFT_VER_LEN       20      ///< BCMU 软件版本数组长度
#define BCMU_SERIES_NUM_LEN     20      ///< BCMU 出厂序列号数组长度
#define BMS_SERIES_NUM_LEN      20      ///< 整机出厂系列号数组长度
#define BMS_TYPE_LEN            20      ///< BMS 型号数组长度

#pragma pack(push, 1)
/* 接收BSMU站址分配触发命令的结构 */
typedef struct {
    unsigned char assign_type;      ///< 站址分配类型
    unsigned char result;           ///< 返回结果
}addr_assign_trig_data_t;

/* 接收BSMU查询BMU实时数据命令的结构 */
typedef struct {
    unsigned char cluster_addr;
    unsigned char bmu_addr;
}get_bmu_real_data_t;


/* 返回BSMU查询BMU实时数据命令的结构 */
typedef struct {
    float cell_temp[BATT_MOD_CELL_NUM]; //电芯温度
    float cell_vol[BATT_MOD_CELL_NUM]; //电芯电压
    unsigned char cell_num;
    float cell_temp_max;    //电芯温度最高
    unsigned char cell_temp_max_channel;
    float cell_temp_min;    //电芯温度最高
    unsigned char cell_temp_min_channel;
    float cell_average_temp;
    float cell_vol_max;
    unsigned char cell_vol_max_channel;
    float cell_vol_min;
    unsigned char cell_vol_min_channel;
    float cell_average_vol;
    float mod_vol_total;
    float mod_pos_temp;
    float mod_neg_temp;
    unsigned char fan1_speed;
    unsigned char fan2_speed;
    unsigned char cell_balanced_status[BATT_MOD_CELL_NUM];
    unsigned char reserved_bit1[64-BATT_MOD_CELL_NUM];
    unsigned char mod_optimizer_status;
    unsigned char mod_sleep_status;
    unsigned char fan1_status;
    unsigned char fan2_status;
    unsigned char reserved_bit2[4];
}BMU_real_data_t;

/* 返回BSMU查询电池簇实时数据命令的结构 */
typedef struct {
    unsigned short batt_cluster_soc;                 //电池簇SOC
    unsigned short batt_cluster_soh;                 //电池簇SOH
    unsigned short batt_cluster_charge_power;        //电池簇充电功率
    unsigned short batt_cluster_discharge_power;     //电池簇放电功率
    float current_remain_capacity;                   //当前剩余容量
    unsigned short discharge_remain_time;            //放电剩余时间
    unsigned short charge_remain_time;               //充电剩余时间
    unsigned int discharge_toatal_capacity;          //累计放电容量
    unsigned int charge_toatal_capacity;             //累计充电容量
    unsigned short batt_cycle_times;                 //电池循环次数
    float batt_charge_request_voltage;               //电池充电请求电压
    float batt_charge_request_current;               //电池充电请求电流
    float current_discharge_target_voltage;          //当前放电目标电压
    float current_set_discharge_voltage;             //当前设定放电电压
    float current_set_charge_voltage;                //当前设定充电电压
    unsigned short current_charging_limit_ratio;     //设定充电限电流比例
    unsigned short current_discharging_limit_ratio;  //设定放电限电流比例
    unsigned short carbon_monoxide_concentration;    //一氧化碳浓度
    float batt_current;                              //电池电流（有符号）
    float batt_voltage;                              //电池电压（无符号）
    float batt_board_voltage;                        //电池单板电压（无符号）
    float bus_current;                               //BUS电流（有符号）
    float bus_board_voltage;                         //BUS单板电压(无符号)
    float compensation_capacitance_voltage;          //补偿电容电压(有符号)
    float cllc_voltage;                              //CLLC电压(无符号)
    float env_temp;                                  //环境温度
    float board_temp;                                //单板温度
    float positive_insulation_resistance;            //正极绝缘电阻
    float negative_insulation_resistance;            //负极绝缘电阻

    unsigned char charge_stop_status;                //充电停止状态
    unsigned char discharge_stop_status;              //放电停止状态
    unsigned char charge_enable_status;              //充电使能状态
    unsigned char discharge_enable_status;           //放电使能状态
    unsigned char closedown_status;                  //闭锁状态
    unsigned char sleep_status;                      //休眠状态
    unsigned char temp_power_limit_status;           //温度限功率状态
    unsigned char fan_control_status;                //风扇控制状态
    unsigned char smr_work_status;                   //整流器工作状态
    unsigned char charge_input_power_off_status;     //充电输入断状态
    unsigned char open_loop_status;                  //开环状态
    unsigned char batt_num;                          //电池个数
    unsigned char download_allowed;                  //下载允许
    unsigned char charge_status;                     //充电状态
    unsigned char discharge_status;                  //放电状态
    unsigned char online_nonfloating_status;          //在线非浮充状态
    unsigned char floating_status;                   //浮充状态
}BATT_CLUSTER_real_data_t;

/* 返回BSMU查询电池簇实时数据命令的结构 */
typedef struct {
    unsigned char batt_cluster_num;
    unsigned char batt_clutter_bmu_num[BATT_CLUSTER_NUM];
    unsigned char bmu_cell_num;
    unsigned char batt_cabinet_work_status;
    unsigned char master_slave_status;
    unsigned char fire_fighting_status;
    unsigned char all_bmu_num;
} BATT_CABINET_real_data_t;

/* BCMU 厂家信息结构 */
typedef struct {
    date_base_t bcmu_release_date;    ///< BCMU 版本日期
    unsigned char bcmu_system_name[BCMU_SYS_NAME_LEN];  ///< BCMU 系统名称
    unsigned char bcmu_soft_version[BCMU_SOFT_VER_LEN]; ///< BCMU 软件版本
    unsigned char bcmu_series_number[BCMU_SERIES_NUM_LEN];   ///< BCMU 出厂序列号
    unsigned char bms_series_number[BMS_SERIES_NUM_LEN];     ///< 整机出厂系列号
    date_base_t bcmu_boot_date;   ///<  BCMU BOOT 日期
}bcmu_fac_data_t;

/* BMU厂家信息 */
typedef struct {
    unsigned char bmu_soft_version[BMU_SOFT_VER_LEN];           // BMU软件版本
    date_base_t bmu_soft_date;                                  // BMU软件日期
    unsigned char bmu_asset_manage[BMU_ASSET_MANAGE_LEN];       //BMU 资产管理信息
}bmu_fac_data_t;

/* BMS 厂家信息 */
typedef struct {
    date_base_t bms_produce_date;                           // 整机生产日期
    date_base_t bms_apply_date;                           // 启用日期
    unsigned char bms_type[BMS_TYPE_LEN];         // BMS型号
}bms_fac_data_t;

/* 接收BMS获取厂家信息命令的结构 */
typedef struct {
    bcmu_fac_data_t bcmu_fac_data;      // BCMU 厂家信息
    bmu_fac_data_t bmu_fac_data[BATT_MOD_NUM];        // BMU 厂家信息
    bms_fac_data_t bms_fac_data;        // BMS 厂家信息
}bsmu_get_fac_data_t;

/*BSMU参数设置 阈值*/
typedef struct {
    float cell_over_vol_alm_level_1;
    float cell_over_vol_alm_level_2;
    float cell_over_vol_alm_level_3;
    float cell_under_vol_alm_level_1;
    float cell_under_vol_alm_level_2;
    float cell_under_vol_alm_level_3;
    float cell_vol_diff_over_alm_level_1;
    float cell_vol_diff_over_alm_level_2;
    float cell_vol_diff_over_alm_level_3;
    float cell_chg_high_temp_alm_level_1;
    float cell_chg_high_temp_alm_level_2;
    float cell_chg_high_temp_alm_level_3;
    float cell_dischg_high_temp_alm_level_1;
    float cell_dischg_high_temp_alm_level_2;
    float cell_dischg_high_temp_alm_level_3;
    float cell_chg_low_temp_alm_level_1;
    float cell_chg_low_temp_alm_level_2;
    float cell_chg_low_temp_alm_level_3;
    float cell_dischg_low_temp_alm_level_1;
    float cell_dischg_low_temp_alm_level_2;
    float cell_dischg_low_temp_alm_level_3;
    float cell_over_temp_diff_alm_level_1;
    float cell_over_temp_diff_alm_level_2;
    float cell_over_temp_diff_alm_level_3;
    float cell_over_vol_protect_thresh;
    float set_chg_limit_curr_coe;
    unsigned short set_dischg_limit_curr;
    float set_chg_vol;
    float set_dischg_vol;
    float bus_over_vol_protect_thresh;
    float batt_under_vol_protect_thresh;
    float bus_under_vol_protect_thresh;
    float bus_and_batt_diff_protect_thresh;
    unsigned char batt_num;
    unsigned char BCMU_addr;
    int batt_cluster_low_soc_alm;
    int batt_cluster_low_soc_protect;
    int batt_cluster_low_soh_alm;
    int batt_cluster_low_soh_protect;
    float batt_cluster_env_temp_high_protect;
    float batt_cluster_env_temp_low_protect;
    float bmu_cell_damage_protect;
    int insulation_resistance_alm_level_1;
    int insulation_resistance_alm_level_2;
    int insulation_resistance_alm_level_3;
    unsigned int co_concentration_alm_level_1;
    unsigned int co_concentration_alm_level_2;
    unsigned int co_concentration_alm_level_3;
    float cell_balance_vol_diff;
    float cell_balance_low_vol;
    //float bus_chg_low_vol;
    float dischg_cur_balance_cur;
    float cell_chg_high_temp_limit_curr_ceo;
    float power_down_vol_thresh;
} BSMU_param_t;


typedef struct
{
    unsigned char cell_over_vol_alm_level_1;
    unsigned char cell_over_vol_alm_level_2;
    unsigned char cell_over_vol_alm_level_3;
    unsigned char cell_under_vol_alm_level_1;
    unsigned char cell_under_vol_alm_level_2;
    unsigned char cell_under_vol_alm_level_3;
    unsigned char cell_vol_diff_over_alm_level_1;
    unsigned char cell_vol_diff_over_alm_level_2;
    unsigned char cell_vol_diff_over_alm_level_3;
    unsigned char cell_chg_high_temp_alm_level_1;
    unsigned char cell_chg_high_temp_alm_level_2;
    unsigned char cell_chg_high_temp_alm_level_3;
    unsigned char cell_dischg_high_temp_alm_level_1;
    unsigned char cell_dischg_high_temp_alm_level_2;
    unsigned char cell_dischg_high_temp_alm_level_3;
    unsigned char cell_chg_low_temp_alm_level_1;
    unsigned char cell_chg_low_temp_alm_level_2;
    unsigned char cell_chg_low_temp_alm_level_3;
    unsigned char cell_dischg_low_temp_alm_level_1;
    unsigned char cell_dischg_low_temp_alm_level_2;
    unsigned char cell_dischg_low_temp_alm_level_3;
    unsigned char cell_over_temp_diff_alm_level_1;
    unsigned char cell_over_temp_diff_alm_level_2;
    unsigned char cell_over_temp_diff_alm_level_3;
    unsigned char batt_capa_soft_over_vol_alm;
    unsigned char batt_capa_hard_over_vol_alm;
    unsigned char batt_capa_under_vol_alm;
    unsigned char bus_soft_over_vol_alm;
    unsigned char bus_hard_over_vol_alm;
    unsigned char bus_under_vol_alm;
    unsigned char bus_vol_out_of_range_alm;
    unsigned char compen_capa_soft_over_vol_alm;
    unsigned char compen_capa_hard_over_vol_alm;
    unsigned char CLLC_over_vol_alm;
    unsigned char CLLC_under_vol_alm;
    unsigned char mod_conn_failure_alm;
    unsigned char chg_over_curr_alm;
    unsigned char dischg_over_curr_alm;
    unsigned char bad_curr_share_alm;
    unsigned char chg_circuit_damage_alm;
    unsigned char dischg_circuit_damage_alm;
    unsigned char curr_limit_circuit_damage_alm;
    unsigned char output_over_load;
    unsigned char bus_short_circuit_alm;
    unsigned char fan_fault;
    unsigned char eeprom_fault;
    unsigned char abnormal_can_comm_between_mod_alm;
    unsigned char high_env_temp_alm;
    unsigned char board_inter_over_temp_protect_alm;
    unsigned char slow_start_exception;
    unsigned char batt_chg_under_vol_protect_alm;
    unsigned char power_connector_self_inspection_resistance_abnormal_alm;
    unsigned char abnormal_relay_closing_protection;
    unsigned char exter_batt_over_vol_alm;
    unsigned char exter_batt_under_vol_alm;
    unsigned char large_vol_difference_between_bus_and_batt_alm;
    unsigned char batt_cluster_low_soc_alm;
    unsigned char batt_cluster_low_soc_protect;
    unsigned char batt_cluster_low_soh_alm;
    unsigned char batt_cluster_low_soh_protect;
    unsigned char batt_cluster_env_temp_high_protect;
    unsigned char batt_cluster_env_temp_low_protect;
    unsigned char bmu_cell_damage_protect;
    unsigned char bmu_cell_temp_abnormal;
    unsigned char bmu_cell_temp_invalid;
    unsigned char bmu_cell_vol_sample_failure;
    unsigned char bmu_equalize_circuit_fault_alm;
    unsigned char bmu_comm_failure;
    unsigned char insulation_resistance_alm_level_1;
    unsigned char insulation_resistance_alm_level_2;
    unsigned char insulation_resistance_alm_level_3;
    unsigned char co_concentration_alm_level_1;
    unsigned char co_concentration_alm_level_2;
    unsigned char co_concentration_alm_level_3;
}BSMU_param_alm_level_t;



typedef struct
{
    unsigned char cell_over_vol_alm_level_1;
    unsigned char cell_over_vol_alm_level_2;
    unsigned char cell_over_vol_alm_level_3;
    unsigned char cell_under_vol_alm_level_1;
    unsigned char cell_under_vol_alm_level_2;
    unsigned char cell_under_vol_alm_level_3;
    unsigned char cell_vol_diff_over_alm_level_1;
    unsigned char cell_vol_diff_over_alm_level_2;
    unsigned char cell_vol_diff_over_alm_level_3;
    unsigned char cell_chg_high_temp_alm_level_1;
    unsigned char cell_chg_high_temp_alm_level_2;
    unsigned char cell_chg_high_temp_alm_level_3;
    unsigned char cell_dischg_high_temp_alm_level_1;
    unsigned char cell_dischg_high_temp_alm_level_2;
    unsigned char cell_dischg_high_temp_alm_level_3;
    unsigned char cell_chg_low_temp_alm_level_1;
    unsigned char cell_chg_low_temp_alm_level_2;
    unsigned char cell_chg_low_temp_alm_level_3;
    unsigned char cell_dischg_low_temp_alm_level_1;
    unsigned char cell_dischg_low_temp_alm_level_2;
    unsigned char cell_dischg_low_temp_alm_level_3;
    unsigned char cell_over_temp_diff_alm_level_1;
    unsigned char cell_over_temp_diff_alm_level_2;
    unsigned char cell_over_temp_diff_alm_level_3;
    unsigned char batt_capa_soft_over_vol_alm;
    unsigned char batt_capa_hard_over_vol_alm;
    unsigned char batt_capa_under_vol_alm;
    unsigned char bus_soft_over_vol_alm;
    unsigned char bus_hard_over_vol_alm;
    unsigned char bus_under_vol_alm;
    unsigned char bus_vol_out_of_range_alm;
    unsigned char compen_capa_soft_over_vol_alm;
    unsigned char compen_capa_hard_over_vol_alm;
    unsigned char CLLC_over_vol_alm;
    unsigned char CLLC_under_vol_alm;
    unsigned char mod_conn_failure_alm;
    unsigned char chg_over_curr_alm;
    unsigned char dischg_over_curr_alm;
    unsigned char bad_curr_share_alm;
    unsigned char chg_circuit_damage_alm;
    unsigned char dischg_circuit_damage_alm;
    unsigned char curr_limit_circuit_damage_alm;
    unsigned char output_over_load;
    unsigned char bus_short_circuit_alm;
    unsigned char fan_fault;
    unsigned char eeprom_fault;
    unsigned char abnormal_can_comm_between_mod_alm;
    unsigned char high_env_temp_alm;
    unsigned char board_inter_over_temp_protect_alm;
    unsigned char slow_start_exception;
    unsigned char batt_chg_under_vol_protect_alm;
    unsigned char power_connector_self_inspection_resistance_abnormal_alm;
    unsigned char abnormal_relay_closing_protection;
    unsigned char exter_batt_over_vol_alm;
    unsigned char exter_batt_under_vol_alm;
    unsigned char large_vol_difference_between_bus_and_batt_alm;
    unsigned char batt_cluster_low_soc_alm;
    unsigned char batt_cluster_low_soc_protect;
    unsigned char batt_cluster_low_soh_alm;
    unsigned char batt_cluster_low_soh_protect;
    unsigned char batt_cluster_env_temp_high_protect;
    unsigned char batt_cluster_env_temp_low_protect;
    unsigned char bmu_cell_damage_protect;
    unsigned char bmu_cell_temp_abnormal;
    unsigned char bmu_cell_temp_invalid;
    unsigned char bmu_cell_vol_sample_failure;
    unsigned char bmu_equalize_circuit_fault_alm;
    unsigned char bmu_comm_failure;
    unsigned char insulation_resistance_alm_level_1;
    unsigned char insulation_resistance_alm_level_2;
    unsigned char insulation_resistance_alm_level_3;
    unsigned char co_concentration_alm_level_1;
    unsigned char co_concentration_alm_level_2;
    unsigned char co_concentration_alm_level_3;
}BSMU_param_alm_relay_t;


typedef struct {
    unsigned char batt_cluster_num;
    unsigned char batt_clutter_bmu_num[BATT_CLUSTER_NUM];
    unsigned char bcmu_self_check_state;
    unsigned char dc_dc_self_check_state[BATT_CLUSTER_NUM];;
    unsigned char bmu_self_check_state[BATT_MOD_NUM];
    unsigned char all_bmu_num;
}BSMU_self_check_state_t;


/* BCMU 工装配置结构 */
typedef struct {
    unsigned char bcmu_system_name[BCMU_SYS_NAME_LEN];  ///< BCMU 系统名称
    unsigned char bcmu_series_number[BCMU_SERIES_NUM_LEN];   ///< BCMU 出厂序列号
    unsigned char series_number[BMS_SERIES_NUM_LEN];     ///< 整机出厂系列号
    date_base_t bms_produce_date;                           // 整机生产日期
    date_base_t bms_apply_date;                           // 启用日期
    unsigned char bms_type[BMS_TYPE_LEN];                 // BMS型号
}bcmu_fac_config_data_t;

/* 北向指令数据汇总 */
typedef struct {
    addr_assign_trig_data_t addr_assign_trig;
    unsigned char addr_assign_trig_reply;  ///< 站址分配触发返回的结果
    get_bmu_real_data_t parse_bmu_real_data;
    BMU_real_data_t pack_bmu_real_data[BATT_MOD_NUM];
    unsigned char parse_batt_cluster_real_data;
    BATT_CLUSTER_real_data_t pack_batt_cluster_real_data[BATT_CLUSTER_NUM];
    BATT_CABINET_real_data_t pack_batt_cabinet_real_data;
    bsmu_get_fac_data_t bsmu_get_fac_data;              ///< bsmu 获取厂家信息命令响应数据
    BSMU_param_t parse_bsmu_param;
    BSMU_param_alm_level_t parse_bsmu_param_alm_level;
    BSMU_param_alm_relay_t parse_bsmu_param_alm_relay;
    BSMU_param_alm_level_t pack_real_alm[BATT_CLUSTER_NUM];
    BSMU_self_check_state_t bsmu_self_check_state;
    BSMU_param_alm_level_t pack_bsmu_param_alm_level;  // 获取告警级别配置 
    time_base_t parse_system_time; // 校准系统时间
}bsmu_data_t;
#pragma pack(pop)


dev_type_t* init_dev_bsmu(void);
void get_bsmu_data(dev_sample_data_t* dev_data, unsigned char dev_index);



#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _DEVICE_BSMU_H
