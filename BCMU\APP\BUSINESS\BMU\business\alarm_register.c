#include "alarm_register.h"
#include "alarm_mgr_api.h"
#include "device_type.h"
#include "utils_math.h"
#include "storage.h"
#include "utils_data_transmission.h"
#include "utils_string.h"
#include "data_type.h"
#include "alarm_id_in.h"
#include "realdata_id_in.h"
#include "alarm_config_in.h"

static unsigned short alarm_clean_by_code_tab[] = {
    0xffff
};

Static alarm_manage_info_t bmu_manage = {
    ALARM_ID_OFFSET_MAX - 1,
    ana_alm_config_tab,
    dig_alm_config_tab,
    self_alm_config_tab,
    alm_shielded,
    alarm_clean_by_code_tab,        // 以告警码清除相关告警列表
};

int register_bmu_alarm(void) {
    register_alarm_manage_info(&bmu_manage);
}
