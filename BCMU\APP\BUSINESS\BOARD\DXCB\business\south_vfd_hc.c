#include "south_dev_common.h"
#include "realdata_id_in.h"
#include "south_dev_modbus.h"


/* 命令请求头 */
static modbusrtu_cmd_head_t cmd_req[] = {
    {READ_HOLD_REG},
    {READ_INPUT_REG},
    {WRITE_SINGLE_REG},
};

/* 命令应答头 */
static modbusrtu_cmd_head_t cmd_ack[] = {
    {READ_HOLD_REG},
    {READ_INPUT_REG},
    {WRITE_SINGLE_REG},
};

static data_info_id_verison_t parse_run_speed[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_COMPRESSOR_RUN_SPEED},
};

static data_info_id_verison_t parse_hc_curr[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_FREQUENCY_CONVERTER_BOARD_CURR},
};

static data_info_id_verison_t parse_hc_temp[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_FREQUENCY_CONVERTER_BOARD_TEMPERATURE},
};


static data_info_id_verison_t parse_real_data[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},//数据的字节长度
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_FREQUENCY_CONVERTER_ERR_CODE, 4},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_FREQUENCY_CONVERTER_ERR_CODE + 1, 4},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_FREQUENCY_CONVERTER_ERR_CODE + 2, 4},
};

static cmd_parse_info_id_verison_t cmd_parse_info[] RAM_SECTION =
{
    {parse_run_speed, sizeof(parse_run_speed)/sizeof(data_info_id_verison_t)},
    {parse_hc_curr, sizeof(parse_hc_curr)/sizeof(data_info_id_verison_t)},
    {parse_hc_temp, sizeof(parse_hc_temp)/sizeof(data_info_id_verison_t)},
    {parse_real_data, sizeof(parse_real_data)/sizeof(data_info_id_verison_t)},
};

/* 命令总表,必须以空字符串""结束 */
static cmd_t no_poll_cmd_tab[] = {
    {AMS_HC_SET_SWITCH,       CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data,NULL,NULL},
    {AMS_HC_SET_TARGET_SPEED, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data,NULL,NULL},
    {AMS_HC_SET_FAULT_CLEAN , CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data,NULL,NULL},
    {HC_SET_FIRST_ACCE_TIME, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data, NULL,NULL},
    {0},
};

static cmd_t poll_cmd_tab[] = {
    {READ_HC_RUN_SPEED_REG, CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[0]},
    {READ_HC_VFD_CURR_REG, CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[1]},
    {READ_HC_VFD_TEMP_REG, CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[2]},
    {READ_HC_HOLD_REG, CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[3]},
    {0},
};


int update_vfd_hc_dev_type(dev_inst_t* dev_inst, char dev_num, char is_inter_fan)
{
    return change_dev_type_info_by_diff_brand(dev_inst, SOUTH_VFD_TYPE, is_inter_fan, dev_num, no_poll_cmd_tab, poll_cmd_tab);
}


