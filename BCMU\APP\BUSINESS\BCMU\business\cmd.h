/**
 * @file     cmd.h
 * @brief    命令头文件.
 * @details  This is the detail description.
 * <AUTHOR> 
 * @date     2022-12-23
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation 
 * @par History:
 *   version: author, date, descn
 */ 
#ifndef _CMD_H
#define _CMD_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

#include "parse_layer.h"

#define     DEV_BMU     1
#define     DEV_BCMU    2
#define     DEV_BSMU    3
#define     DEV_DC_DC   4
#define     DEV_BMU_SOUTH   5

/* 命令ID:命令唯一标识 */
#define INVALID_CMD_ID                0xff
#define BSMU_ADDR_ASSIGN_TRIG         1   ///< 站址分配触发
#define BSMU_GET_ALL_REAL_DATA        2   ///< 查询所有实时数据
#define BSMU_GET_CABINET_REAL_DATA    3   ///< 查询电池柜实时数据
#define BSMU_GET_CLUSTER_REAL_DATA    4   ///< 查询电池簇实时数据
#define BSMU_GET_BMU_REAL_DATA        5   ///< 查询BMU实时数据
#define BSMU_GET_FAC_DATA             6   ///< BMS 获取厂家信息
#define BSMU_SET_BATCH_PARA           7   ///< BSMU批量设置参数
#define BSMU_SET_CELL_OVA_LV1         8   ///< 设置电芯过压告警（一级）
#define BSMU_SET_CELL_OVA_LV2         9   ///< 设置电芯过压告警（二级）
#define BSMU_SET_CELL_OVA_LV3         10  ///< 设置电芯过压告警（三级）
#define BSMU_SET_CELL_UVA_LV1         11  ///< 设置电芯欠压告警（一级）
#define BSMU_SET_CELL_UVA_LV2         12  ///< 设置电芯欠压告警（二级）
#define BSMU_SET_CELL_UVA_LV3         13  ///< 设置电芯欠压告警（三级）
#define BSMU_SET_CELL_VDOA_LV1        14  ///< 电芯压差过大告警（一级）
#define BSMU_SET_CELL_VDOA_LV2        15  ///< 电芯压差过大告警（二级）
#define BSMU_SET_CELL_VDOA_LV3        16  ///< 电芯压差过大告警（三级）
#define BSMU_SET_CELL_CHTA_LV1        17  ///< 电芯充电高温告警（一级）
#define BSMU_SET_CELL_CHTA_LV2        18  ///< 电芯充电高温告警（二级）
#define BSMU_SET_CELL_CHTA_LV3        19  ///< 电芯充电高温告警（三级）
#define BSMU_SET_CELL_DHTA_LV1        20  ///< 电芯放电高温告警（一级）
#define BSMU_SET_CELL_DHTA_LV2        21  ///< 电芯放电高温告警（二级）
#define BSMU_SET_CELL_DHTA_LV3        22  ///< 电芯放电高温告警（三级）
#define BSMU_SET_CELL_CLTA_LV1        23  ///< 电芯充电低温告警（一级）
#define BSMU_SET_CELL_CLTA_LV2        24  ///< 电芯充电低温告警（二级）
#define BSMU_SET_CELL_CLTA_LV3        25  ///< 电芯充电低温告警（三级）
#define BSMU_SET_CELL_DLTA_LV1        26  ///< 电芯放电低温告警（一级）
#define BSMU_SET_CELL_DLTA_LV2        27  ///< 电芯放电低温告警（二级）
#define BSMU_SET_CELL_DLTA_LV3        28  ///< 电芯放电低温告警（三级）
#define BSMU_SET_CELL_OTDA_LV1        29  ///< 电芯温差过大告警（一级）
#define BSMU_SET_CELL_OTDA_LV2        30  ///< 电芯温差过大告警（二级）
#define BSMU_SET_CELL_OTDA_LV3        31  ///< 电芯温差过大告警（三级）
#define BSMU_SET_CLUSTER_OVP_THRE     32  ///< 电池过压保护阈值
#define BSMU_SET_CLUSTER_CL_CURR      33  ///< 设定充电限电流
#define BSMU_SET_CLUSTER_DL_CURR      34  ///< 设定放电限电流
#define BSMU_SET_CLUSTER_CHG_VO       35  ///< 设定充电电压
#define BSMU_SET_CLUSTER_DISCHG_VOL   36  ///< 设定放电电压
#define BSMU_SET_CLUSTER_BUS_OVP_THRE 37  ///< 母排过压保护阈值
#define BSMU_SET_CLUSTER_BAT_UVP_THRE 38  ///< 电池欠压保护阈值
#define BSMU_SET_CLUSTER_BUS_UVP_THRE 39  ///< 母排欠压保护阈值
#define BSMU_SET_CLUSTER_BBDP_THRE    40  ///< 母排与电池差值保护阈值
#define BSMU_SET_CLUSTER_BAT_NUM      41  ///< 电池个数
#define BSMU_SET_BCMU_ADDR            42  ///< BCMU地址
#define BSMU_SET_CLUSTER_SOC_LOW_ALM  43  ///< 电池簇SOC低告警
#define BSMU_SET_CLUSTER_SOC_LOW_PROT 44  ///< 电池簇SOC低保护
#define BSMU_SET_CLUSTER_SOH_LOW_ALM  45  ///< 电池簇SOH低告警
#define BSMU_SET_CLUSTER_SOH_LOW_PROT 46  ///< 电池簇SOC低保护
#define BSMU_SET_CLUSTER_ETH_PROT     47  ///< 电池簇环境温度高保护
#define BSMU_SET_CLUSTER_ETL_PROT     48  ///< 电池簇环境温度低保护
#define BSMU_SET_BMU_CELL_DAM_PROT    49  ///< BMU电芯损坏保护
#define BSMU_SET_IRA_ALM_LV1          50  ///< 绝缘电阻告警（一级）
#define BSMU_SET_IRA_ALM_LV2          51  ///< 绝缘电阻告警（二级）
#define BSMU_SET_IRA_ALM_LV3          52  ///< 绝缘电阻告警（三级）
#define BSMU_SET_COC_ALM_LV1          53  ///< 一氧化碳浓度告警（一级）
#define BSMU_SET_COC_ALM_LV2          54  ///< 一氧化碳浓度告警（二级）
#define BSMU_SET_COC_ALM_LV3          55  ///< 一氧化碳浓度告警（三级）
#define BSMU_SET_ALM_LEVEL            56  ///< BSMU告警级别参数设置
#define BSMU_SET_ALM_RELAY            57  ///< BSMU告警干接点参数设置
#define BSMU_GET_REAL_ALM             58  ///< 查询实时告警
#define BSMU_GET_SELF_CHECK_STATE     59  ///<  获取电池柜自检状态
#define BSMU_GET_ALM_LEVEL            60  ///< BSMU 获取告警级别配置
#define BSMU_UPDATE_BAK_TRIG          61  ///<  程序备份触发帧
#define BSMU_UPDATE_BAK_DATA          62  ///<  程序备份传输帧
#define BSMU_UPDATE_PRE_BAK_DATA      63  ///<  前级程序备份传输帧
#define BSMU_UPDATE_PST_BAK_DATA      64  ///<  后级程序备份传输帧
#define BSMU_UPDATE_DATA_TRIG         65  ///<  数据传输触发帧
#define BSMU_UPDATE_DATA              66  ///<  程序更新数据传输帧
#define BSMU_UPDATE_DATA_INTERRUPT    67  ///<  数据传输中断帧
#define BSMU_UPDATE_TRIG              68  ///<  程序更新触发帧
#define BSMU_UPDATE_STATE_FEEDBACK    69  ///<  状态反馈帧
#define BSMU_CELL_BALANCE_VOL_DIFF    70  ///<  均衡启动电压差阈值设置
#define BSMU_CELL_BALANCE_LOW_VOL     71  ///<  均衡最低启动电压值设置
//#define BSMU_BUS_CHG_LOW_VOL          72  ///<  BUS充电最低电压阈值
#define BSMU_DISCHG_CUR_BALANCE_CUR   72  ///<  放电均流电流
#define BSMU_CELL_CHG_HIGH_TEMP_LIMIT_CURR_COE 73  ///<  电芯充电高温限电流
#define BSMU_SET_CLUSTER_POWRE_DOWN_VOL_THRE   74  ///<  停电电压阈值
#define BSMU_TIME_SYNC                75  ///<  时间同步
#define BSMU_SET_CALIBRATED_TIME      76  ///<  系统时间校准
#define BSMU_CTRL_POWER_ON            77  ///<  下发来电
#define BSMU_GET_SYS_TIME             78  ///<  获取系统时间

/* BMU命令ID:唯一标示一个命令 */         
#define BMU_ADDR_ASSIGN                1        ///<  站址分配
#define BMU_GET_ANA_DATA               2        ///<  获取实时数据模拟量
#define BMU_GET_SWITCH_DATA            3        ///<  获取实时数据开关量
#define BMU_GET_ALARM_DATA             4        ///<  获取实时数据告警量
#define BMU_CTRL_SET_BALANCE           5        ///<  设置均衡使能 
#define BMU_GET_MANUFACTOR_DATA        6        ///<  获取厂家信息 
#define BMU_ADDR_OCCUPY                7        ///<  站址占用
#define BMU_GET_SAMPLE_VOL             8        ///<  获取拓扑计算电压
#define BMU_FIRE_ALARM_PROTECT         9        ///<  消防告警关联保护
#define BMU_GET_FIRST_HIS_DATA        10        ///<  获取第一条历史数据
#define BMU_GET_NEXT_HIS_DATA         11        ///<  获取下一条历史数据
#define BMU_GET_PREV_HIS_DATA         12        ///<  重发上一条历史数据
#define BMU_GET_HIS_DATA_FINISH       13        ///<  历史数据获取完成
#define BMU_GET_FIRST_HIS_ALARM       14        ///<  获取第一条历史告警
#define BMU_GET_NEXT_HIS_ALARM        15        ///<  获取下一条历史告警
#define BMU_GET_PREV_HIS_ALARM        16        ///<  重发上一条历史告警
#define BMU_GET_HIS_ALARM_FINISH      17        ///<  历史告警获取完成
#define BMU_GET_SELF_CHECK_STATE      18        ///<  获取自检状态
#define BMU_HIS_RECORD_SAVE           19        ///<  历史记录保存
#define BMU_GET_SOC_SOH_DATA          20        ///<  获取bmu SOC SOH
#define BMU_SET_SOC_SOH_DATA          21        ///<  设置bmu SOC SOH
#define BMU_UPDATE_BAK_TRIG           22        ///<  程序备份触发帧
#define BMU_UPDATE_BAK_DATA           23        ///<  程序备份传输帧
#define BMU_UPDATE_PRE_BAK_DATA       24        ///<  前级程序备份传输帧
#define BMU_UPDATE_PST_BAK_DATA       25        ///<  后级程序备份传输帧
#define BMU_UPDATE_DATA_TRIG          26        ///<  数据传输触发帧
#define BMU_UPDATE_DATA               27        ///<  程序更新数据传输帧
#define BMU_UPDATE_DATA_INTERRUPT     28        ///<  数据传输中断帧
#define BMU_UPDATE_TRIG               29        ///<  程序更新触发帧
#define BMU_TIME_SYNC                 30        ///<  时间同步
#define BMU_SET_CALIBRATED_TIME       31        ///<  系统时间校准
#define BMU_SET_SERIAL_NUMBER         32        ///<  设置BMU序列号
#define BMU_GET_SERIAL_NUMBER         33        ///<  获取BMU序列号
#define BMU_UPDATE_TRIG_OLD           34        ///<  程序更新触发帧
#define BMU_GET_ANA_TEST_DATA         35        ///<  外部公司测试用获取实时数据模拟量，提供不跨帧的模拟量数据
#define BMU_GET_BALANCE_WORK_STATE    36        ///<  获取均衡电路故障检测状态（0：正常；1：短路；2：断路；FF：检测中）
#define BMU_UPDATE_DATA_COMPLETE_ACK  37        ///<  数据传输完成确认帧
#define BMU_UPDATE_CONFIRM            38        ///<  程序更新确认帧

#define DC_DC_CTRL_EPO                                   19       ///<  控制紧急关机

#define DC_AC_UPDATE_DATA_TRIG                           26        ///<  数据传输触发帧
#define DC_AC_UPDATE_DATA                                27        ///<  程序更新数据传输帧
#define DC_AC_UPDATE_DATA_INTERRUPT                      28        ///<  数据传输中断帧
#define DC_AC_UPDATE_TRIG                                29        ///<  程序更新触发帧

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _CMD_H
