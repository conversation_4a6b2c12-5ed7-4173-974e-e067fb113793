/**
 * @file     insulation_detect.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2023-02-20
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */

 
#ifndef _INSULATION_DETECT_H
#define _INSULATION_DETECT_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */


#include <stdio.h>
#include <rtdevice.h>


#define SAMPLE_NUM 3
#define STABLE_THERSHOLD (3.0)
#define COUNTER_NUM 5
#define TRUE  1
#define FALSE 0

#define D1 1    // 开关
#define D2 2
#define D3 3

// 电压采样点结构体
typedef struct {
    float v_iso1[SAMPLE_NUM]; 
    float v_out[SAMPLE_NUM];
}sample_volt_t;

void read_voltage_value(float* volt_pos, float* volt_neg);
void calc_cluster_insulation_resistance(float *resis_pos, float *resis_neg, unsigned char cluster_num);


#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _INSULATION_DETECT_H
