#include <rtthread.h>
#include <rtdevice.h>
#include "board.h"
#include <string.h>
#include <stdlib.h>
#include "data_type.h"
#include "utils_server.h"
#include "io_control.h"
#include "gui_data_interface.h"
#include "pin.h"
#include "Menu.h"
#include "drv_lcd.h"
#include "msg_id.h"
#include "do_ctrl.h"
#include "para_id_in.h"
#include "realdata_id_in.h"
#include "thread_id.h"
#include "utils_heart_beat.h"
#include "alarm_manage.h"
#include "rt_drv_pwm.h"
#include "para_manage.h"
#include "system_manage.h"
#include "io_ctrl_api.h"
#include "drv_common.h"
#include "pin_define.h"
#include "acmu_watchdog.h"
#include "utils_time.h"
#include "utils_thread.h"

/* 全局按键数组 */
Static struct key_info s_keys[] = {
    {PIN_LCD_BUTTON1, KEY_ESC,   PRESS_UP, 0 ,0},
    {PIN_LCD_BUTTON2, KEY_LEFT,  PRESS_UP, 0 ,0},
    {PIN_LCD_BUTTON3, KEY_UP,    PRESS_UP, 0 ,0},
    {PIN_LCD_BUTTON4, KEY_DOWN,  PRESS_UP, 0 ,0},
    {PIN_LCD_BUTTON5, KEY_ENTER, PRESS_UP, 0 ,0},
    {PIN_LCD_BUTTON6, KEY_RIGHT, PRESS_UP, 0 ,0}
};
#define KEY_COUNT (sizeof(s_keys) / sizeof(s_keys[0]))

static _rt_server_t g_control_server;
Static struct rt_device_pwm *s_buzzer_dev = NULL;
Static unsigned char s_lcd_led_timer_used = FALSE;
Static unsigned int s_lcd_led_timer_time = 0;
Static unsigned int s_buzz_silence_timer_time = 0;
Static unsigned char  s_buzz_silence_timer_used = FALSE;
Static unsigned char  s_is_buzz_silence = FALSE;
Static unsigned int s_save_screen_timer_time = 0;
Static unsigned char  s_save_screen_timer_used = FALSE;
Static int s_buzz_state = 0;      // 当前蜂鸣器状态（0:关, 1:开）
Static unsigned int s_buzz_timer = 0;      // 计数器
Static rt_tick_t s_cha_led_start_time = 0;  // 机架告警灯开始时间
Static rt_bool_t s_cha_led_blinking = FALSE;  // 机架告警灯是否在闪烁状态
Static rt_uint8_t s_cha_led_last_alarm_level = 0;  // 上次告警级别
Static rt_tick_t s_one_sencond_start_time = 0;  // 时钟刷新开始计时时间
static rt_device_t lcd_dev;
static rt_mutex_t s_mutex_com;
Static rt_bool_t s_combo_pending = FALSE;  // 是否有未确认的组合键
Static struct keys_state s_keys_state = { 0 };
Static unsigned int s_led_button_press_count[6] = {0,};     //apptest模式下按键次数计数,顺序与全局按键数组顺序对应

Static void ScanKeyPort( void );
Static int handle_buzz_silence_timer_event (void);
Static short handle_acmu_main_alm_change_msg(_rt_msg_t curr_msg);
Static int handle_acmu_alm_change_msg (_rt_msg_t curr_msg);
Static unsigned char get_buzz_mode_by_alm(void);
Static int buzz_control(void);
Static void key_scan_init(void);
Static int handle_led_timer_event (void);
Static int handle_cha_led_timer_event(void);
Static int handle_save_screen_timer_event(void);
Static short check_acmu_alm(unsigned char alarm_level);
Static unsigned char get_key_id(unsigned char keys_state);
Static int clock_trace(void);
Static unsigned char init_out_relay(void);

rt_base_t s_alLedPin[4] = {PIN_LED_RUN_CONTROL, PIN_LED_ALM_CONTROL, PIN_LED_RACK_CONTROL, PIN_LED_COM_CONTROL};  // 使用数字引脚，实际使用时需要根据硬件定义修改
    
/* 当前LED状态 */
Static rt_uint8_t s_currentLedMode[4] = {LEDINIT, LEDINIT, LEDINIT, LEDINIT};


Static msg_map s_io_control_msg_map[] =
{
    {CHECK_ALL_ALARM_TRIG_RELAY,    alarm_trig_relay},
    {MAIN_ALARM_CHANGE_MSG,    handle_acmu_main_alm_change_msg},
    {CHECK_ALL_ALARM_CHG_MSG,    handle_acmu_alm_change_msg},
};

Static msg_map s_key_process_msg_map[] =
{
    {0,    NULL},
};

Static void key_scan_init(void) {
    int i = 0;
    /* 初始化所有按键引脚为输入模式 */
    for (i = 0; i < KEY_COUNT; i++) {
        rt_pin_mode(s_keys[i].pin, PIN_MODE_INPUT);
    }
}

static void buzzer_init(void)
{
    s_buzzer_dev = (struct rt_device_pwm *)rt_device_find(BUZZ_PWM_DEVICE);
    return;
}

/**
 * @description:处理背光定时器
 * @return {int} 成功返回SUCCESSFUL（0）
 */
Static int handle_led_timer_event (void) {
    if (s_lcd_led_timer_used == FALSE && s_lcd_led_timer_time > LCD_LED_TIMEOUT ) {
        //无按键按下超时，关闭液晶背光
        int key = PIN_LOW;
        rt_device_control(lcd_dev, RT_LCD_CONTROL_LED, &key);
        s_lcd_led_timer_used = TRUE;
    }
    return SUCCESSFUL;
}

/**
 * @description:处理蜂鸣器静音定时器
 * @return {int} 成功返回SUCCESSFUL（0）
 */
Static int handle_buzz_silence_timer_event (void) {
    if(s_buzz_silence_timer_used == FALSE && s_buzz_silence_timer_time > SILENCE_TIME_TICKS) {
        //三十分钟超时，将蜂鸣器静音定时器关掉，静音标志置为FALSE。重新进去蜂鸣器发声逻辑
        s_is_buzz_silence = FALSE;
        s_buzz_silence_timer_used = TRUE;
    }
    return SUCCESSFUL;
}

/**
 * @description:处理背光定时器
 * @return {int} 成功返回SUCCESSFUL（0）
 */
Static int handle_acmu_alm_change_msg (_rt_msg_t curr_msg) 
{
    flag_check_all_alm_t* alm_change_data = (flag_check_all_alm_t*)curr_msg->msg.data;
    if(alm_change_data == NULL)
    {
        return FAILURE;
    }
    if(alm_change_data->save_his_data_flag == TRUE && GetFocusWndID() == WND_SAVE_SCREEN)
    {
        SetFocusWnd(WND_ALARM);
    }
    return SUCCESSFUL;
}

Static int handle_save_screen_timer_event (void) {
    if (s_save_screen_timer_used == FALSE && s_save_screen_timer_time > SAVE_SCREEN_TIMEOUT ) {
        //无按键按下超时，进入屏保界面
        SetFocusWnd(WND_SAVE_SCREEN);
        s_save_screen_timer_used = TRUE;
    }
    return SUCCESSFUL;
}

 Static short handle_acmu_main_alm_change_msg(_rt_msg_t curr_msg) {
    //收到重要告警变化通知，将蜂鸣器静音定时器关掉，静音标志置为FALSE。
    s_is_buzz_silence = FALSE;
    s_buzz_silence_timer_used = TRUE;
    return SUCCESSFUL;
 }

Static unsigned char get_key_id(unsigned char keys_state)
{
    unsigned char key_id = 0;
    switch (keys_state)
    {
    case 0b00000001:
        key_id = KEY_ESC;
        break;
    case 0b00000010:
        key_id = KEY_LEFT;
        break;
    case 0b00000100:
        key_id = KEY_UP;
        break;
    case 0b00001000:
        key_id = KEY_DOWN;
        break;
    case 0b00010000:
        key_id = KEY_ENTER;
        break;
    case 0b00100000:
        key_id = KEY_RIGHT;
        break;
    case 0b00010100:
        key_id = KEY_UP_ENTER;
        break;
    case 0b00001100:
        key_id = KEY_UP_DOWN;
        break;
    case 0b00011000:
        key_id = KEY_DOWN_ENTER;
        break;
    case 0b00110010:
        key_id = KEY_LEFT_RIGHT_ENT;
        break;
    default:
        key_id = KEY_NOTHING;
        break;
    }
    return key_id;
}

Static void ScanKeyPort( void ){
    int i = 0;
    rt_tick_t now;
    rt_bool_t current_state;

    now = rt_tick_get();
    for (i = 0; i < KEY_COUNT; i++) {
        /* 读取当前按键状态 (按下=1, 释放=0) */
        current_state = ((rt_pin_read(s_keys[i].pin)) == PRESS_UP);
        /* 检测下降沿 (按下事件) */
        if (s_keys[i].last_state && !current_state) {
            /* 去抖处理：检查时间间隔 */
            if ((now - s_keys[i].last_press_time) > RT_TICK_PER_SECOND / 20) { // 50ms去抖
                s_keys_state.current_keys_state |= (0x01 << i);// 标记组合按键状态

                //有键按下，打开液晶背光
                int key = PIN_HIGH;
                rt_device_control(lcd_dev, RT_LCD_CONTROL_LED, &key);
                rt_device_control(lcd_dev, RT_LCD_CONTROL_DISPLAY, &key);
                s_lcd_led_timer_time = 0;
                s_lcd_led_timer_used = FALSE;
                //按键按下，停止蜂鸣器发声逻辑，且调整系统管理开启检测重要告警变化
                s_buzz_silence_timer_time = 0;
                s_buzz_silence_timer_used = FALSE;
                s_is_buzz_silence = TRUE;
                //按键按下，不会进行屏保界面
                s_save_screen_timer_time = 0;
                s_save_screen_timer_used = FALSE;
            }
            s_keys[i].last_press_time = now;
            if(get_apptest_flag())  //apptest模式下按键被按下，次数 + 1，正常模式下不会累加
            {
                s_led_button_press_count[i] ++;
            }
        }
        
        /* 检测上升沿 */
        else if (!(s_keys[i].last_state) && current_state)
        {
            /* 去抖处理：检查时间间隔 */
            if ((now - s_keys[i].last_release_time) > RT_TICK_PER_SECOND / 20) // 50ms去抖
            {
                // 有按键松开，并清零组合按键状态
                s_keys_state.current_keys_state = 0;
                s_keys_state.last_keys_state = s_keys_state.current_keys_state;
            }
            s_keys[i].last_release_time = now;
        }
        
        /* 更新按键状态 */
        s_keys[i].last_state = current_state;
    }

    // 扫描完按键后：
    // 情况1：当前组合键有效，且与上一次不同 → 标记为“待确认”
    if (s_keys_state.current_keys_state != s_keys_state.last_keys_state &&
        s_keys_state.current_keys_state != 0)
    {
        if (!s_combo_pending)
        {
            // 只在第一次变化时记录时间
            s_keys_state.last_keys_press_time = now;
            s_combo_pending = TRUE;
        }
    }
    else
    {
        // 状态相同或为空 → 清除待确认状态
        s_combo_pending = FALSE;
    }

    // 情况2：如果正在等待确认，且已稳定超过 100ms
    if (s_combo_pending &&
        (now - s_keys_state.last_keys_press_time) > RT_TICK_PER_SECOND / 10) 
    {
        unsigned char key_id = get_key_id(s_keys_state.current_keys_state);
        rt_kprintf("AddKey executed! key_id=0x%x\n", key_id);
        AddKey(key_id);

        // 确认响应：更新 last_keys_state
        s_keys_state.last_keys_state = s_keys_state.current_keys_state;
        s_combo_pending = FALSE;  // 重置
    }
    
    s_lcd_led_timer_time++;
    s_buzz_silence_timer_time++;
    s_save_screen_timer_time++;
    handle_led_timer_event();
    handle_buzz_silence_timer_event();
    handle_save_screen_timer_event();
    return;
}


/**
 * @description:通过告警中最大告警级别获取蜂鸣器鸣叫方式
 * @param {unsigned char} on - 蜂鸣器开启状态 0 关闭 1 开启
 * @return {int} 成功返回SUCCESSFUL（0）
 */
Static unsigned char get_buzz_mode_by_alm(void)
{
    unsigned char highest_level = get_highest_alarm_level();
    
    // 检查是否有紧急告警
    if (highest_level == ALARMCLASS_CRITICAL)
    {
        return BUZZ_MODE1;
    }
    // 检查是否有重要告警
    if (highest_level == ALARMCLASS_MAJOR)
    {
        return BUZZ_MODE2;
    }

    // 如果没有报警，关闭蜂鸣器
    return BUZZ_OFF;
}

/**
 * @description:蜂鸣器控制
 * @return {int} 成功返回SUCCESSFUL（0）
 */
Static int buzz_control(void)
{
    unsigned short buzz_period = 0;
    unsigned char buzz_mode = BUZZ_OFF;
    unsigned char buzzer_switch = FALSE;

    if(RT_NULL == s_buzzer_dev)
    {
        buzzer_init();
    }
    if(RT_NULL == s_buzzer_dev)
    {
        rt_kprintf("can't find %s device!\n", BUZZ_PWM_DEVICE);
        return FAILURE;
    }
    
    get_one_para(ACMU_PARA_ID_SYS_BUZZER_SWITCH_OFFSET, &buzzer_switch);
    
    if (s_is_buzz_silence == FALSE && buzzer_switch == TRUE) {
        buzz_mode = get_buzz_mode_by_alm();
    }

    if (buzz_mode == BUZZ_MODE1) {
        buzz_period = BUZZMODE1_ONOFF_MS / IO_CONTROL_THREAD_PERIOD_MS;
    } else if (buzz_mode == BUZZ_MODE2) {
        buzz_period = BUZZMODE2_ONOFF_MS / IO_CONTROL_THREAD_PERIOD_MS;
    } else {
        beep_control(BUZZ_OFF);
        s_buzz_state = BUZZ_OFF;
        s_buzz_timer = 0;
        return SUCCESSFUL; 
    }

    s_buzz_timer++;
    if (s_buzz_timer >= buzz_period) {
        s_buzz_timer = 0;
        s_buzz_state = !s_buzz_state;
        beep_control(s_buzz_state);
    }
    return SUCCESSFUL;
}

/**
 * @description:鸣叫控制
 * @param {unsigned char} on - 蜂鸣器开启状态 0 关闭 1 开启
 * @return {int} 成功返回SUCCESSFUL（0）
 */
int beep_control(unsigned char on)
{
    if (on) {
        rt_pwm_set(s_buzzer_dev, BUZZ_PWM_CHANNEL, BUZZ_PWM_FREQ, BUZZ_PWM_DUTY);
        rt_pwm_enable(s_buzzer_dev, BUZZ_PWM_CHANNEL);
    } else {
        rt_pwm_disable(s_buzzer_dev, BUZZ_PWM_CHANNEL);
    }
    return SUCCESSFUL;
}

void* init_io_control(void* param)
{
    server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(s_io_control_msg_map) / sizeof(msg_map);
    register_server_msg_map(s_io_control_msg_map, server_info);
    init_led_blink();
    lcd_init();
    init_out_relay();
    return param;
}


void* init_key_process(void* param)
{
    server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(s_key_process_msg_map) / sizeof(msg_map);
    register_server_msg_map(s_key_process_msg_map, server_info);
    key_buffer_init();
    s_one_sencond_start_time = rt_tick_get();
    return param;
}

void io_control_main(void *param)
{
    PRINT_MSG_AND_RETURN_IF_FAIL(param != NULL);
    g_control_server = _curr_server_get();
    thread_monitor_register("io_control");
    while (is_running(TRUE)) 
    {
        thread_monitor_update_heartbeat();
        ScanKeyPort();
        if (get_apptest_flag() == TRUE) {
            rt_thread_delay(IO_CONTROL_THREAD_PERIOD_MS);   //apptest模式下不再进行led和蜂鸣器控制
            continue;
        }
        /* 每5ms扫描一次按键 */
        buzz_control();
        /* 灯控 */
        check_acmu_alm(get_highest_alarm_level());
        // 处理机架告警灯定时器
        handle_cha_led_timer_event();
        recv_msg_handle(s_io_control_msg_map, (sizeof(s_io_control_msg_map) / sizeof(s_io_control_msg_map[0])));
        rt_thread_delay(IO_CONTROL_THREAD_PERIOD_MS);
    }
}

/**
 * @description:时间调测
 * @param {void} 
 * @return {int} 成功返回SUCCESSFUL（0）
 */
Static int clock_trace(void) {
    rt_tick_t current_time = rt_tick_get();
    char	buff[33] = {0};
    time_base_t tTime;
    unsigned char wnd_id = WND_MAIN;

    if (current_time - s_one_sencond_start_time >= CLK_TRACE_INTERVAL) {
        s_one_sencond_start_time = current_time;
        wnd_id = GetFocusWndID();
        if(wnd_id != WND_REC_ALARM && wnd_id != WND_REC_ACTION && wnd_id != WND_REC_PEAK) {
            AddKey( KEY_PAINT );  	//刷新显示
        }
        get_time(&tTime);
        rt_snprintf_s( buff, sizeof(buff), "Now%04d%02d%02d %02d:%02d:%02d", 
            tTime.year,tTime.month, tTime.day,tTime.hour,tTime.minute,tTime.second);
        SetTraceStr(1, buff);
    }
    return SUCCESSFUL;

}

void key_process_main(void *parameter)
{
    thread_monitor_register("key_process");
    while (is_running(TRUE)) {
        thread_monitor_update_heartbeat();
        clock_trace();
        ProcessKey();
        rt_thread_mdelay(KEY_PROCESS_THREAD_PERIOD_MS);
    }
}

int lcd_init()
{
    lcd_dev = rt_device_find("lcd");
    if(lcd_dev == RT_NULL)
        return RT_ERROR;
    rt_device_open(lcd_dev, RT_DEVICE_FLAG_WRONLY);
    key_scan_init();
    DisplayWelcome();
    return RT_EOK;
}

int alarm_trig_relay(_rt_msg_t curr_msg)
{
    unsigned char manual_control_mode = 0;
    do_msg_t* do_msg = (do_msg_t*)curr_msg->msg.data;
    if(do_msg == NULL)
    {
        return FAILURE;
    }
    get_one_data(ACMU_DATA_ID_OUTRELAY_MANUAL_CONTROL_MODE, &manual_control_mode);
    if(manual_control_mode == TRUE)   // 如果在手动控制，则告警控制不会生效
    {
        return FAILURE;
    }
    for(int i = 0; i < OUT_RELAY_MAX_NUM; i ++)
    {
        control_out_relay(do_msg[i].do_no - 1, do_msg[i].active);
    }
    return SUCCESSFUL;
}

// 定义引脚数组
const rt_base_t out_relay_pins[] = {
    OUTRELAY1_PIN,
    OUTRELAY2_PIN,
    OUTRELAY3_PIN,
    OUTRELAY4_PIN,
    OUTRELAY5_PIN,
    OUTRELAY6_PIN,
    OUTRELAY7_PIN,
    OUTRELAY8_PIN
};

Static unsigned char init_out_relay(void)
{
    for(int i = 0; i < OUT_RELAY_MAX_NUM; i ++)
    {
        rt_pin_mode(out_relay_pins[i], PIN_MODE_OUTPUT); // DO输出
        rt_pin_write(out_relay_pins[i], PIN_LOW);        // DO输出低电平
    }
    
    return SUCCESSFUL;
}

signed char control_out_relay(unsigned char index, unsigned char status)
{
    if(index >= sizeof(out_relay_pins) / sizeof(out_relay_pins[0]))
    {
        return FAILURE;
    }

    rt_pin_write(out_relay_pins[index], (rt_base_t)(status));
    set_one_data(ACMU_DATA_ID_OUTPUT_RELAY + index, &status);
    return SUCCESSFUL;
}

 
Static short check_acmu_alm(unsigned char alarm_level) {
    led_set_alarm_by_level(alarm_level);
    // 同时控制机架告警灯
    led_set_cha_alarm_by_level(alarm_level);
    return SUCCESSFUL;
 }

/* 发送LED控制消息 */
Static short sendLedMsg(rt_uint8_t ucIndex, rt_uint8_t ucLedMode)
{
    int led_num = (sizeof(s_alLedPin) / sizeof(s_alLedPin[0]));
    // 参数检查
    if (ucIndex >= led_num) {
        return FAILURE;
    }
    
    // 如果LED模式没有变化，直接返回成功
    if (s_currentLedMode[ucIndex] == ucLedMode)
    {
        return SUCCESSFUL;
    }

    // 更新当前LED模式
    s_currentLedMode[ucIndex] = ucLedMode;

    // 初始化LED控制消息
    led_ctrl_msg msg = {
        .pin_no = s_alLedPin[ucIndex],
        .lighton_time = 100,
        .lightoff_time = 0  // 默认关闭状态
    };

    // 根据LED模式设置具体的时间参数
    switch (ucLedMode)
    {
        case LEDON:
            msg.lighton_time = 0;
            msg.lightoff_time = 100;
            break;
        case LEDOFF:
            // 已经是默认值，不需要修改
            break;
        case LEDSLOWSH:
            msg.lighton_time = 50;
            msg.lightoff_time = 50;
            break;
        case LEDQUICKSH:
            msg.lighton_time = 12;
            msg.lightoff_time = 13;
            break;
        default:
            // 默认值已经设置，不需要额外处理
            break;
    }

    // 发送消息到线程
    if (SUCCESSFUL != pub_msg_to_thread(LED_CTRL_MSG_ID, &msg, sizeof(msg))) {
        return FAILURE;
    }
    
    return SUCCESSFUL;
}

/* 初始化闪烁led */
void* init_led_blink() {
    int led_num = (sizeof(s_alLedPin) / sizeof(s_alLedPin[0]));
    int i;
    short ret;
    s_mutex_com = rt_mutex_create("com_lock", RT_IPC_FLAG_PRIO);
    
    // 注册所有LED到IO控制组件
    io_item_info_t ledInfo = {
        .eIoType = OUTPUT_LED,
        .initialVal = PIN_LOW,
        .msg_id = LED_CTRL_MSG_ID
    };

    for (i = 0; i < led_num; i++)
    {
        ledInfo.pin_no = s_alLedPin[i];
        ret = register_io_item_info(&ledInfo);
        if (FAILURE == ret) {
            // rt_kprintf("Failed to register LED item %d\n", i);
            return NULL;
        }
    }
    
    // 初始化LED为默认状态
    ret = led_set_status(0, LEDSLOWSH);  // 运行灯1Hz闪烁
    if (SUCCESSFUL != ret) {
        // rt_kprintf("Failed to set run LED status\n");
    }
    
    ret = led_set_status(1, LEDOFF);    // 告警灯熄灭
    if (SUCCESSFUL != ret) {
        // rt_kprintf("Failed to set alarm LED status\n");
    }
    
    ret = led_set_status(2, LEDOFF);      // 机架灯熄灭
    if (SUCCESSFUL != ret) {
        // rt_kprintf("Failed to set cha LED status\n");
    }
    
    ret = led_set_status(3, LEDOFF);      // 通信灯熄灭
    if (SUCCESSFUL != ret) {
        // rt_kprintf("Failed to set com LED status\n");
    }

    return NULL;
}

/* LED状态设置函数 */
short led_set_status(rt_uint8_t led_type, rt_uint8_t mode)
{
    return sendLedMsg(led_type, mode);
}

/* 根据告警级别设置告警灯 */
short led_set_alarm_by_level(rt_uint8_t alarm_level)
{
    rt_uint8_t led_mode = LEDOFF;
    
    switch (alarm_level) {
        case 1:  // 紧急告警
        case 2:  // 重要告警
        case 3:  // 一般告警
            led_mode = LEDQUICKSH; // 2Hz闪烁
            break;
        case 4:  // 轻微告警
            led_mode = LEDSLOWSH;  // 1Hz闪烁
            break;
        default:
            led_mode = LEDOFF;     // 无告警或屏蔽
            break;
    }
    
    return led_set_status(1, led_mode);// 更新告警灯状态
}

/* 根据通信状态设置通信灯 */
short led_set_com_by_status(rt_uint8_t comm_status)
{
    rt_uint8_t led_mode = LEDOFF;
    
    rt_mutex_take(s_mutex_com, RT_WAITING_FOREVER);
    if(get_apptest_flag() == TRUE)  //apptest模式下不进行控制，仅通过apptest协议控制
    {
        return FALSE;
    }
    if (comm_status == 1) {  // 正在发送
        led_mode = LEDON;     // 常亮
    }
    led_set_status(3, led_mode);// 更新通信灯状态
    rt_mutex_release(s_mutex_com);

    return TRUE;
}

/* 根据告警级别设置机架告警灯 */
short led_set_cha_alarm_by_level(rt_uint8_t alarm_level)
{
    rt_tick_t current_time = rt_tick_get();
    rt_uint8_t led_mode = LEDOFF;
    
    // 告警屏蔽或无告警时，关闭机架告警灯
    if (alarm_level == ALARMCLASS_MASK || alarm_level == 0xff) {
        s_cha_led_blinking = FALSE;
        s_cha_led_last_alarm_level = alarm_level;
        return led_set_status(2, LEDON);  // 机架灯索引为2
    }
    
    // 如果告警级别发生变化，重新开始计时
    if (s_cha_led_last_alarm_level != alarm_level) {
        s_cha_led_start_time = current_time;
        s_cha_led_blinking = TRUE;
        s_cha_led_last_alarm_level = alarm_level;
    }
    
    // 计算告警持续时间（毫秒）
    rt_tick_t elapsed_ms = (current_time - s_cha_led_start_time);
    
    // 如果闪烁时间超过1分钟（60000毫秒），则保持常亮
    if (elapsed_ms >= CHA_LED_TIMEOUT) {
        s_cha_led_blinking = FALSE;
        led_mode = LEDOFF;  // 常亮
    } else {
        // 在1分钟内，按1Hz频率闪烁（500ms开/500ms关）
        led_mode = LEDSLOWSH;  // 1Hz闪烁
    }
    
    return led_set_status(2, led_mode);  // 更新机架灯状态
}

/* 机架告警灯定时器处理函数 */
Static int handle_cha_led_timer_event(void)
{
    if (s_cha_led_blinking) {
        // 重新调用设置函数以更新LED状态
        led_set_cha_alarm_by_level(s_cha_led_last_alarm_level);
    }
    return SUCCESSFUL;
}

unsigned char get_button_press_test_status(unsigned char index)
{
    return (s_led_button_press_count[index] > 0) ? 0 : 1;   //按键次数大于0，返回00正常，否则返回01异常
}

unsigned char clear_button_press_count(void)
{
    for(int i = 0; i < 6; i++)
    {
        s_led_button_press_count[i] = 0;
    }
    return SUCCESSFUL;
}