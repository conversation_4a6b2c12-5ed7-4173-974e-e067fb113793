/**
 * @file     device_type.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2017-04-19
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */

#ifndef _DCMU_PROTOCOL_1363_H
#define _DCMU_PROTOCOL_1363_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "device_type.h"

#define PARSE_PARA              1   // 解析参数信息
#define PARSE_ALM               2   // 解析告警信息

#define BATTERY_FUSE_NUM        4   // 电池熔丝数量
#define LOAD_FUSE_SWITCH_NUM    24  // 监测负载分录熔丝开关数量

#define BOOT_VER_LEN            16  // boot版本最大长度

#define MENUKEY_LEN             4   // 菜单口令长度

/*1363 protocol的命令唯一标识定义 */
#define DCMU_GET_INRELAY_INFO               1   ///<  获取输入干节点信息
#define DCMU_SET_INRELAY_INFO               2   ///<  设置输入干节点信息
#define DCMU_REMOTE_CONTROL                 3   ///<  遥控
#define DCMU_GET_SYSPARA                    4   ///<  获取参数（定点数）
#define DCMU_SET_SYSPARA                    5   ///<  设置参数（定点数）
#define DCMU_GET_ALMRELAY                   6   ///<  读取直流告警干接点
#define DCMU_SET_ALMRELAY                   7   ///<  设置直流告警干接点
#define DCMU_GET_ALMLEVEL                   8   ///<  读取直流告警等级
#define DCMU_SET_ALMLEVEL                   9   ///<  设置直流告警等级
#define DCMU_DC_ALARM_DATA                  10  ///<  获取直流配电告警信息
#define DCMU_ENV_ALARM_DATA                 11  ///<  获取环境监控告警信息
#define DCMU_GET_CUSTOMPARA                 12  ///<  获取自定义参数（定点数）
#define DCMU_SET_CUSTOMPARA                 13  ///<  获取自定义参数（定点数）
#define DCMU_GET_TIME                       14   ///<  获取时间
#define DCMU_SET_TIME                       15   ///<  设置时间
#define DCMU_GET_ANALOG                     16   ///<  获取模拟量量化数据（定点数）
#define DCMU_GET_SW_STATUS                  17   ///<  获取开关状态
#define DCMU_GET_HIS_ALARM                  18   ///<  获取历史告警
#define DCMU_GET_ENV_ANALOG                 19   ///<  获取环境模拟量量化数据（定点数）
#define DCMU_GET_FACTORY_INFO_CUSTOM        20   ///<  获取设备厂家信息自定义
#define DCMU_GET_ENV_PARA                   21   ///<  获取环境参数（定点数）
#define DCMU_SET_ENV_PARA                   22   ///<  设置环境参数（定点数）
#define DCMU_GET_ENV_ALMLEVEL               23   ///<  获取环境告警等级
#define DCMU_SET_ENV_ALMLEVEL               24   ///<  设置环境告警等级
#define DCMU_GET_ENV_ALMRELAY               25   ///<  获取环境告警干接点
#define DCMU_SET_ENV_ALMRELAY               26   ///<  设置环境告警干接点
#define DCMU_GET_BOOT_VERSION               27   ///<  获取boot版本号及版本日期

/* cid2 功能码定义 */
#define CMD_GET_TIME                            0x4D    ///<  获取时间
#define CMD_SET_TIME                            0x4E    ///<  设置时间
#define CMD_GET_INRELAY_INFO                    0x87    ///<  获取输入干节点信息
#define CMD_SET_INRELAY_INFO                    0x88    ///<  设置输入干节点信息
#define CMD_GET_ALARM                           0x44    ///<  获取告警
#define CMD_REMOTE_CONTROL                      0x45    ///<  遥控
#define CMD_GET_SYSPARA                         0x47    ///<  获取参数（定点数）
#define CMD_SET_SYSPARA                         0x49    ///<  设置参数（定点数）
#define CMD_GET_ALMLEVEL                        0x81    ///<  读取告警干接点
#define CMD_SET_ALMLEVEL                        0x82    ///<  设置告警干接点
#define CMD_GET_ALMRELAY                        0x83    ///<  读取告警干接点
#define CMD_SET_ALMRELAY                        0x84    ///<  设置告警干接点
#define CMD_GET_CUSTOMPARA                      0x85    ///<  获取自定义参数（定点数）
#define CMD_SET_CUSTOMPARA                      0x86    ///<  获取自定义参数（定点数）
#define CMD_GET_ANALOG                          0x42    ///<  获取模拟量量化数据（定点数）
#define CMD_GET_SW_STATUS                       0x43    ///<  获取开关状态
#define CMD_GET_HIS_ALARM                       0x4C    ///<  获取历史告警
#define CMD_GET_FACTORY_INFO_CUSTOM             0xE1    ///<  获取设备厂家信息自定义
#define CMD_GET_BOOT_VERSION                    0x52    ///<  获取boot版本号及版本日期

#define GROUP_TYPE_SEND_NORMAL                  0x00     //正常发送
#define GROUP_TYPE_SEND_LAST                    0x01     //最后一包
#define GROUP_TYPE_RCV_START                    0x00     //接收第一包
#define GROUP_TYPE_RCV_NEXT                     0x01     //接收下一包
#define GROUP_TYPE_RCV_ERROR                    0x02     //接收错误，重发
#define GROUP_TYPE_RCV_END                      0x03     //接收结束

/* 厂家信息相关宏定义 */
#define SM_NAME_LEN                 30      ///<  SM名称长度
#define SM_FACTORY_NAME_LEN         20      ///<  厂家名称长度
#define SM_SOFT_VER_LEN             12      ///<  SM软件版本长度
#define SM_SOFT_RELEASE_DATE_LEN    4       ///<  软件发布日期长度
#define SM_HARDWARE_VERSION_LEN     21      ///<  硬件版本号长度
#define SM_SERIAL_NUMBER_LEN        21      ///<  序列号长度
#define SM_RESERVED_LEN             15      ///<  预留字节长度

typedef struct {
    unsigned char  command_type;
    unsigned short sid; 
    unsigned char  data_type;
    unsigned short array_len;
    unsigned char  precision;
}dcmu_para_info;

typedef union { 
    short t_data;
    unsigned char uc_data;  
    char c_data;
} dcmu_para_value;

dev_type_t* init_dev_dcmu_1363(void);
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _DCMU_PROTOCOL_1363_H

