#include "north_south_main.h"
#include "remote_download_update_handle.h"
#include "io_control.h"
#include "acmu_watchdog.h"
#include "Menu.h"
#include "const_define_in.h"

static dev_inst_t* dev_acmu = NULL;
Static int s_com_time_out_count = 0;
Static unsigned char s_apptest_usart_test_flag = FALSE; // 是否正在执行apptest协议的串口回环测试
static dev_inst_t* s_north_dev_inst[PROTOCOL_INDEX_MAX] = {NULL};

static unsigned char s_dev_type[] = {
    DEV_NORTH_1104,
    DEV_NORTH_1363,
    DEV_REMOTE_DOWNLOAD,
};

Static msg_map north_msg_map[] =
{
    {0,NULL},//临时添加解决编译问题
};

Static int trace_protocol_debug(int loop, cmd_buf_t* cmd_buff);

Static acmu_mgr_t * init_thread_data(link_inst_t* link_inst, unsigned char mod_id) {
    acmu_mgr_t* acmu_mgr = NULL;

    acmu_mgr = rt_malloc(sizeof(acmu_mgr_t));
    if (acmu_mgr == NULL)
        return NULL;
    
    acmu_mgr->cmd_buff = rt_malloc(sizeof(cmd_buf_t));
    if (acmu_mgr->cmd_buff == NULL) {
        goto MGR_FREE;
    }

    acmu_mgr->cmd_buff->addr_dev_data = 0x01;
    acmu_mgr->link_inst = link_inst;
    return acmu_mgr;
     
MGR_FREE:
    free(acmu_mgr);
    return NULL;
}

Static int trace_protocol_debug(int loop, cmd_buf_t* cmd_buff) {
    s1363_cmd_head_t* s1363_cmd_head = NULL;
    char buff[MAX_LETTERS];

    rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));
    RETURN_VAL_IF_FAIL(loop < ARR_SIZE(s_dev_type), FAILURE);

    if (s_dev_type[loop] != DEV_NORTH_1104 && s_dev_type[loop] != DEV_NORTH_1363) {
        return FAILURE;
    }

    RETURN_VAL_IF_FAIL(cmd_buff != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(cmd_buff->cmd != NULL, FAILURE);

    s1363_cmd_head = (s1363_cmd_head_t*)cmd_buff->cmd->req_head;

    RETURN_VAL_IF_FAIL(s1363_cmd_head != NULL, FAILURE);

    rt_snprintf_s( buff, sizeof(buff), "CID1=%2xH CID2=%2xH", s1363_cmd_head->cid1, s1363_cmd_head->cid2 );
    SetTraceStr( 19, buff );
    return SUCCESSFUL;
}

Static int handle_received_data(acmu_mgr_t* north_mgr) {

    for(int loop = 0; loop < ARR_SIZE(s_dev_type) ; loop ++)
    {
        if(SUCCESSFUL == protocol_parse_recv(s_north_dev_inst[loop], north_mgr->cmd_buff))
        {
            trace_protocol_debug(loop, north_mgr->cmd_buff);
            led_set_com_by_status(1);
            cmd_send(s_north_dev_inst[loop], north_mgr->cmd_buff);
            led_set_com_by_status(0);
            rt_sem_clear_value(&(north_mgr->link_inst->rx_sem));
            break;
        }
    }
    return 0;
}

Static int acmu_boot_softver(void)
{
    boot_info_t boot_info = {0};
    rt_memcpy_s(&boot_info, sizeof(boot_info_t), BOOT_DATA_ADDRESS, sizeof(boot_info_t));
    set_one_data(ACMU_DATA_ID_BOOT_VER, boot_info.ver);
    set_one_data(ACMU_DATA_ID_BOOT_VER_DATE , boot_info.date);
    return SUCCESSFUL;
}

void* init_north_comm(void * param)
{
    server_info_t *server_info = (server_info_t *)param;
    int loop = 0;

    for(; loop < ARR_SIZE(s_dev_type); loop ++)
    {
        dev_acmu = init_dev_inst(s_dev_type[loop]);
        RETURN_VAL_IF_FAIL(dev_acmu != NULL, NULL);
        s_north_dev_inst[loop] = dev_acmu;
    }
    acmu_boot_softver();
    server_info->server.server.map_size = sizeof(north_msg_map) / sizeof(msg_map);
    register_server_msg_map(north_msg_map, server_info);
    return init_thread_data(dev_acmu->dev_type->link_inst, MOD_ACMU_NORTH);
}

/* 收发线程 */
void north_comm_thread(void *param) {
    acmu_mgr_t* north_mgr = (acmu_mgr_t*)param;
    thread_monitor_register("north_comm");
    while (is_running(TRUE)) {
        thread_monitor_update_heartbeat();

        // 如果正在进行apptest协议的串口回环测试，则跳过中断，避免影响测试结果
        if(s_apptest_usart_test_flag == TRUE)
        {
            rt_thread_mdelay(10);  // 避免空转
            continue;
        }

        if (RT_EOK == rt_sem_take(&(north_mgr->link_inst->rx_sem), THREAD_TICK * 5)) {
            s_com_time_out_count = 0;
            if (FAILURE == linker_recv(s_north_dev_inst[0], north_mgr->cmd_buff)) {
                continue;
            }
            handle_received_data(north_mgr);
        }
        if(s_com_time_out_count > 10) {
            s_com_time_out_count = 0;
            north_mgr->link_inst->r_cache.index = 0;
            north_mgr->link_inst->r_cache.len = 0;
        }   
        s_com_time_out_count++;
    }
}

unsigned char set_apptest_usart_test_flag(unsigned char flag_set)
{
    s_apptest_usart_test_flag = flag_set;
    return SUCCESSFUL;
}