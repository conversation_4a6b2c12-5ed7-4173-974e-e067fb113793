#include "common.h"
#include "CommCan.h"
#include "led.h"
#include "sample.h"
#include "realAlarm.h"
#include "utils_heart_beat.h"
#include "thread_id.h"
#include "battery.h"
#include "para.h"
#include "comm.h"
#include "protocol.h"
#include "MdbsRtu.h"
#include "commBdu.h"
#include "prtclDL.h"
#include "fileSys.h"
#include "hisdata.h"
#include "flash.h"
#include <rtdevice.h>
#include "BattSleep.h"
#include "rtc_PFC8563.h"
#include "sys/time.h"
#include "pdt_version.h"
#include "const_define.h"
#include "const_define.h"
#include "prtclDL.h"
#include "update_handle.h"
#include "MultBattData.h"
#include "fm.h"
#include "utils_rtthread_security_func.h"

#ifdef USE_CAN2
#include "CommCan2.h"
#endif
#ifdef ACTIVATE_PORT_ENABLED
#include "ActivatePort.h"
#endif
#ifdef USING_SNMP
#include "SNMPData.h"
#endif

rt_err_t PFC8563set_date(Datebuff *Rcvdate);  //TODO: 待BSP添加对应声明后删除

#define CAN1_DEV_NAME       "can0"

#define R321_MASTER_SEND 1

#define SITE_ANTITHEFT_INFO_SEND_DELAY (200)  // 站点防盗发送延时

typedef struct {
    BYTE ucLinkSn;    //建链序号
    BYTE ucPollAddr;    //轮询地址
    BYTE ucPollIndex;
    BYTE ucUpdateAddr;
    WORD wRegAddr;      //寄存器地址
    BYTE ucWaitAddr;    //等待应答地址
    BYTE ucWaitTime;    //应答超时计数
    BYTE aucLink[SLAVE_NUM];    //建链状态
    BYTE aucCnt[SLAVE_NUM];     //通信异常计数
}T_SlaveTCB;

/***********************  变量定义  ************************/

Static T_CommStruct s_tCommCan;
Static T_BmsTCB s_tMyBms;
Static T_SlaveTCB s_tSlaveTCB;
static T_PACKETBUFF s_tRecBuff; // 接收缓冲区
static INT32S s_slCanDealCounter = 0;
Static T_CAN_DataCache s_tCanDataCache;
Static T_CAN_DataCache s_tCanDataCache_BMS[SLAVE_NUM];
static T_SysPara s_tSysPara;
Static T_BCMDataStruct s_tBCMData;
static T_FmSubmissionstruct s_tFmRealData;
static int ForcemasterCnt = 0;
// static T_BCMAlarmStruct s_tBCMAlarm;
static BYTE s_ucRepeteAdr = 0;
static T_CanBackupStruct s_tCanBuffer[CAN_BACKUP_NUM_MAX];  //接收互备份缓存，5条
static rt_device_t can1_dev;
static struct rt_semaphore can1_rx_sem;
Static BOOLEAN s_bCanNorthCommDisconnect = True;     // can通信断状态(通过can通信清掉)
static UINT32 s_ulCanNorthDisconnCnt = 0; //can北向通讯通讯断计数器
static Datebuff tTime; //用于主从机时间同步时设置从机时间
static BOOLEAN s_bMasterExist = False;
static ULONG s_uMasterNotExisTime = 0;
static rt_mutex_t s_ptMutexCan1 = RT_NULL;
Static BYTE s_ucAddrCompFLAG = 0;
static BYTE s_aucBackupBuffSend[CAN_BACKUP_DATA_BUFF] = {0}; //CAN链路互备份数据发送缓存
static BYTE s_aucBackupBuffRecv[CAN_BACKUP_DATA_BUFF] = {0}; //CAN链路互备份数据接收缓存
Static T_CAN_BMSUpdateScanState s_BMSUpdateState = {0};
Static BOOLEAN s_bParallUpdateFlag = False;    //并发升级标记，置位True后其他Can业务均停止
static char s_bIsBigBattStage = False;
Static BOOLEAN s_bBduParallUpdateFlag = False;    //功率透传并发升级标记，置位True后其他Can业务均停止

#ifdef SITE_ANTI_THEFT_ENABLED
/* 站点防盗相关 */
Static T_CAN_DataCache s_tCanDataCache_AntiTheft;
Static T_SaveBindingInfoStruct s_tSaveBindingInfo;
Static BOOLEAN s_bIsBindingInfoChange;
Static WORD s_wSendBindingInfoCnt;
Static BYTE s_ucRecBindingInfoCnt;
#endif

static UINT32 s_wCANCommLostCnt_5min = 0;  // 并机通信计时器(固定5min)

#ifdef USE_CAN2
static BYTE s_can_1_InterconnectTestData[5] = {0x01, 0x02, 0x03, 0x04, 0x05};
static UINT32 s_can_1_InterconnetTestCounter = 0;  // CAN1对接测试成功计数
#endif

#ifdef USING_SNMP
static BYTE ucBattAddrNet = 0;
static BYTE ucPollAddr = 0;
static BOOLEAN bOneBatt = FALSE;

static BOOLEAN sendSNMPRealData(BYTE ucAddr);
static BOOLEAN sendSNMPRealAlarm(BYTE ucAddr);
static BOOLEAN sendSNMPSyncPara(void);
static BOOLEAN sendSNMPSyncParaStr(void);
static BOOLEAN sendSNMPFactInfo(BYTE ucAddr);

static BYTE s_aucSNMPRealDataBuffSend[1024] = {0};
#endif

#ifdef INTELLIGENT_PEAK_SHIFTING
#define FM_INFO_NOTICE_FRAME_ID_DEFAULT 255
#define FM_INFO_NOTICE_TIMER_MAX_DEFAULT 50
#define FM_INFO_NOTICE_SEND_COUNT_MAX_DEFAULT 3
static T_FmInfoNotice s_tFmInfoNotice = {0, };
#endif

/*****************  静态函数原型定义  **********************/
static SHORT Proc_CAN_Frame( T_Can_Frame * ptCan_Frame );
static void InitSlaveData(void);
static SHORT ChangeCanFrameToPkt( T_CommStruct* ptCommCan, T_Can_Frame * ptCan_Frame);
static SHORT ChangeCanFrameToPkt_BMS( T_CommStruct* ptCommCan, T_Can_Frame * ptCan_Frame);
Static void SendCompeteFrm(ULONG ulSn);
Static void sendCanFrm(BYTE ucDstAdr, BYTE* pucBuff);
static BOOLEAN modifyBmsAddr(BYTE ucAddr);
static void BMSAddrCompete( void );
static BOOLEAN CanDrive_Write(BYTE *pIn, SHORT len, BYTE *ptHeader);
static rt_err_t can1_rx_call(rt_device_t dev, rt_size_t size);
Static void masterComm(void);
static void CheckApptestCan(T_Can_Frame* ptCan_Frame);
static void checkMasterExist(void);
static void ParseSynTimeModeFrm(T_Can_Frame *ptCan_Frame);
Static void SetWorkMode( BYTE* ucCnt );
Static BOOLEAN WaitAddrTimeoutJudege(void);
#ifdef ALARM_UPLOAD_ACTIVE
Static BOOLEAN ParseAlarmFrmFromSlave(T_Can_Frame const* ptFrame);
#endif

#ifdef USING_SNMP
Static BOOLEAN JudgeCommFailToSnmpAgent(void);
#endif

Static SHORT BMSUpdatePollSlaveDev(void);
Static BOOLEAN JudgeAndHandleUpdateTimeOut(void);
Static SHORT SendSetUpdateFlagFrm(BYTE addr, BYTE func, BOOLEAN flag);
Static SHORT SendScanDevFrm(BYTE addr, BYTE func);
Static BOOLEAN JudgeBMSUpdatePollIntervalArrive(void);
Static BOOLEAN JudgeBMSUpdatePollTimeOut(void);
Static SHORT Proc_Prtcl_Type_BMS_Update(T_CAN_Header tHeader);
Static BOOLEAN DealBMSFrameMatchUpdate(T_Can_Frame* ptCan_Frame);
Static SHORT ParseSlaveUpdateFlagFrm(T_Can_Frame* ptCan_Frame);
Static SHORT ParseSlaveScanDevFrm(T_Can_Frame* ptCan_Frame);
Static SHORT ParseMasterUpdateFlagFrm(T_Can_Frame* ptCan_Frame);
Static SHORT ParseMasterScanDevFrm(T_Can_Frame* ptCan_Frame);
static void ParseFmCtrl(T_Can_Frame * ptCan_Frame);
static void ParseMAXChgPowerFrm(T_Can_Frame * ptCan_Frame);
static void MainLoopDealSlaveTCB(void);
static BOOLEAN IsRecLengthError(void);
static void MainLoopCanNorthCommDisconnectJudge(void);

static BOOLEAN CanDrive_Write_test(BYTE *pIn, SHORT len, BYTE ucType, BYTE ucCanType);
static void SaveSysTimeAciton(struct tm TimeNow);

Static BOOLEAN SwitchToSlave(void);
Static void masterSlaveComm(void);
Static void synBMSPara(void);
Static void sendRealData(void);
static void sendHighSOCData(void);
Static void sendBattCapacity(void);
static void sendRealData3(void);
Static BOOLEAN DealBMSDLFrame(T_CommStruct* ptCommCan, T_Can_Frame* ptCan_Frame);
static void SendBattChgPower(void);
static void GetBattChgPower(void);
Static void GetBattChgPowerCount(void);

#ifdef SITE_ANTI_THEFT_ENABLED
Static void InitBindingInfo(void);
Static void DealBindingInfo(void);
Static void SendBindingInfoCanFrame(void);
Static void ParseBindingInfoFrame(BYTE *p, rt_uint32_t SrcAdr);
Static void ClearBindingBmsReceivedStat(void);
Static BOOLEAN CheckIfSiteAntitheftFrame(T_Can_Frame * ptCan_Frame);
#endif
Static BOOLEAN CheckSrcAdrErr(T_Can_Frame * ptCan_Frame);

static BOOLEAN judgeConnTempHighStatus(BYTE ucSn);
static BOOLEAN judgeConnTempHighStatusRestore(BYTE ucSn);
static void setConnTempHighPrt(BYTE bConnTempHighPrtStatus);
Static void sendCanFrmSepc(BYTE ucDstAdr, BYTE* pucBuff, BYTE LEN );
#ifdef BOARDCAST_CONN_TEMP_HIGH_PRT_MONITORING
static void masterPreDealConnTempHighPrt(void);
static BOOLEAN judgeConnTempHighStatusRestoreMaster(FLOAT fConnTemp);
static BYTE s_ucSlaveNum;
#endif
Static T_SlavesRealData s_atSlavesRealData[SLAVE_NUM];
Static T_ConnTempHighPrt s_tConnTempHighPrt;
Static T_ConnTempHighPrt s_tConnTempHighPrtRestore;
static BOOLEAN bIsCanClose = FALSE;

BOOLEAN GetParallUpdateFlag(void)
{
    return s_bParallUpdateFlag;
}

BOOLEAN SetParallUpdateFlag(BOOLEAN Flag)
{
    s_bParallUpdateFlag = Flag;
    return True;
}

BOOLEAN GetBduParallUpdateFlag(void)
{
    return s_bBduParallUpdateFlag;
}

BOOLEAN SetBduParallUpdateFlag(BOOLEAN Flag)
{
    s_bBduParallUpdateFlag = Flag;
    return True;
}

BOOLEAN IsParallUpdate(void)
{
    return (s_bBduParallUpdateFlag || GetTriggerEndFlag() || s_bParallUpdateFlag) ;
}

void getSlaveData(T_SlavesRealData * ptSlavesRealData)
{
    rt_memcpy_s(ptSlavesRealData, sizeof(s_atSlavesRealData), (BYTE *)&s_atSlavesRealData, sizeof(s_atSlavesRealData));
}

static void InitSlaveData(void)
{
    rt_memset_s(s_atSlavesRealData, sizeof(s_atSlavesRealData), 0x00, sizeof(s_atSlavesRealData));
    rt_memset_s(&s_tConnTempHighPrt, sizeof(s_tConnTempHighPrt), 0x00, sizeof(s_tConnTempHighPrt));
    rt_memset_s(&s_tConnTempHighPrtRestore, sizeof(s_tConnTempHighPrtRestore), 0x00, sizeof(s_tConnTempHighPrtRestore));
}

Static BOOLEAN SwitchToSlave(void)
{
    #ifdef USE_CAN2
     if(GetCan2ConnectedFlag()){
        return False;
    }
    #endif
    if(IsSleep() || IsBduP2PUpdate() || IsBduCommFail() || GetLoopOffStat() || GetCellVoltStat() || IsBattLock())
    {
        return True;
    }
#ifdef ACTIVATE_PORT_ENABLED
    if(GetActivateErrorFlag())
    {
        return True;
    }
#endif

    return False;
}

Static void masterSlaveComm(void)
{
    if (s_tMyBms.ucMasterAddr == s_tMyBms.ucAddress)
    {
       if(SwitchToSlave())
       {
           s_tMyBms.wCnt = 0;
           s_tMyBms.ucMasterAddr++;
           InitSlaveData();
           return;
       }
        masterComm();
    }
    else if (s_tMyBms.wCnt > (s_tMyBms.ucAddress + 6) * 50)
    {
        s_tMyBms.wCnt = 0;
        if (!(SwitchToSlave())) //从机休眠时，不切换为主机
        {
            s_tMyBms.ucMasterAddr = s_tMyBms.ucAddress; //切换为主机
        }
    }

    return; 
}

Static void synBMSPara(void)
{
    BYTE aucBuff[7];
    WORD wBattFullVol, wBattVolLow;
    rt_memset_s(&aucBuff, sizeof(aucBuff), 0, 7);

    getGrpAddr();
    GetSysPara(&s_tSysPara);
    wBattFullVol = (WORD)(s_tSysPara.fBattChgFullAverVoltThre*100);
    wBattVolLow = (WORD)(s_tSysPara.fBattUnderVoltPrtThre*100);

    aucBuff[0] = ID_SET_PARA;
    aucBuff[1] = wBattFullVol%256;
    aucBuff[2] = wBattFullVol/256;
    aucBuff[3] = wBattVolLow%256;
    aucBuff[4] = wBattVolLow/256;
#ifdef DEVICE_USING_R321
    aucBuff[5] = s_tSysPara.bRelaySyncEn;
#endif
    sendCanFrm(s_tMyBms.ucGroup, aucBuff);

    return;
}

void setVol(BYTE ucMode, SHORT iVol)
{
    if (IsParallUpdate())
    {
        return;
    }

    if(!IsMaster())
    {
        return;
    }
    BYTE aucBuff[7];
    rt_memset_s(&aucBuff, sizeof(aucBuff), 0, 7);

    getGrpAddr();

    aucBuff[0] = ID_SET_DISCHRGVOL;
	aucBuff[1] = ucMode;
    aucBuff[2] = (iVol>>8) & 0xFF;
    aucBuff[3] = iVol & 0xFF;

    sendCanFrm(s_tMyBms.ucGroup, aucBuff);

    return;
}

Static void getBMSData(void)
{
    BYTE aucBuff[7];
    rt_memset_s(&aucBuff, 7, 0, 7);
    aucBuff[0] = ID_GET_REALDATA;
    aucBuff[1] = R321_MASTER_SEND;   //R321获取实时数据时该标志置为1，R321从机可根据该标志判断主机是R321或B3
    sendCanFrm(s_tMyBms.ucGroup, aucBuff);

    return;
}

#ifdef BOARDCAST_CONN_TEMP_HIGH_PRT_MONITORING
static void getRealData3(void)
{
    BYTE aucBuff[7];

    rt_memset_s(&aucBuff, sizeof(aucBuff), 0, 7);

    aucBuff[0] = ID_GET_REALDATA3;

    sendCanFrm(s_tMyBms.ucGroup, aucBuff);

    return;
}
#endif

Static void getHighSOCData(void)
{
    BYTE aucBuff[7];
    T_DCRealData tDCRealData;
    T_BCMDataStruct tBcmData;
    WORD wCurr = 0;
    WORD wSOC =0;
    rt_memset_s(&tDCRealData, sizeof(T_DCRealData), 0, sizeof(T_DCRealData));
    GetRealData(&tBcmData);
    GetBduReal(&tDCRealData);
    wCurr = tDCRealData.tDCAnalag.sBusCur;
    wSOC = tBcmData.wBattHighPrecSOC;

    rt_memset_s(&aucBuff, 7, 0, 7);

    aucBuff[0] = ID_GET_HIGHSOC;
    aucBuff[1] =  wCurr & 0xFF;
    aucBuff[2] = (wCurr>>8) & 0xFF;
    aucBuff[3] = wSOC & 0xFF;
    aucBuff[4] = (wSOC>>8) & 0xFF;

    sendCanFrm(s_tMyBms.ucGroup, aucBuff);

    return;
}

Static void getBattCapacity(void)
{
    BYTE ucSn;
    BYTE aucBuff[7];

    rt_memset_s(&aucBuff[0], sizeof(aucBuff), 0, sizeof(aucBuff));

    aucBuff[0] = ID_GET_BATT_CAPACITY;
    /***同步干接点控制***/
    aucBuff[1] = GetRelayCtrl();
    for (ucSn=0; ucSn<SLAVE_NUM; ucSn++)
    {
        if (s_atSlavesRealData[ucSn].bExist)
        {
            aucBuff[1] |= s_atSlavesRealData[ucSn].ucRelayCtrl;
        }
    }
    SynRelayCtrl(aucBuff[1]);

    sendCanFrm(s_tMyBms.ucGroup, aucBuff);
}

#ifdef ALARM_UPLOAD_ACTIVE
Static BOOLEAN ParseAlarmFrmFromSlave(T_Can_Frame const* ptFrame)
{
    BOOLEAN ucFlag = 0;
    int uiFuncCode = ptFrame->tDataFrame.aucData[0];
    if (IsMaster() && GetCan2ConnectedFlag())
    {
        ucFlag = (ID_CAN1_ALARM_OCCUR_UPLOAD == uiFuncCode) ? True : False;
        SendAlarmToSCUByCan2(ptFrame, ucFlag);
        return True;
    }
    
    return False;
}
#endif

#ifdef ALARM_UPLOAD_ACTIVE
BOOLEAN SendAlarmToMasterByCan1(BYTE usAlarmId, BOOLEAN ucAlarmOccurFlag) 
{
    BYTE aucBuff[7];
    time_t   tTime;
    rt_time_t rtTime;

    rt_memset(&aucBuff[0], 0, sizeof(aucBuff));
    time(&tTime);
    rtTime = (rt_time_t)(tTime & 0xFFFFFFFF);
    aucBuff[0] = (True == ucAlarmOccurFlag) ? ID_CAN1_ALARM_OCCUR_UPLOAD : ID_CAN1_ALARM_MISS_UPLOAD;
    aucBuff[1] = GetBMSAddr();
    rt_memcpy(&aucBuff[2], (BYTE*)&rtTime, sizeof(rt_time_t));
    aucBuff[6] = usAlarmId;
    sendCanFrm(s_tMyBms.ucMasterAddr, aucBuff);
    return True;
}
#endif

#ifdef USING_SNMP
/*****************************************SNMP 周期轮询同步数据 **********************************************/
static BOOLEAN getSNMPRealData(void)
{
    BYTE aucBuff[7] = {0};
    BYTE i = 0;

    if(ucBattAddrNet == 0 || !IsMaster())      //1、主从机系统中，没有电池接入网络则不收集数据 2、只有主机发轮询获取指令 
    {
        return FALSE;
    }

    bOneBatt = FALSE;
    for (i=1; i<=SLAVE_NUM; i++)
    {
        ucPollAddr = (ucPollAddr >= SLAVE_NUM) ? 1 : (ucPollAddr + 1);
        if(IsBattInSys(ucPollAddr) == TRUE && (ucPollAddr) != ucBattAddrNet)
        {
            break;
        }
        // ucPollAddr = (ucPollAddr >= SLAVE_NUM) ? 0 : (ucPollAddr + 1);
    }

    if(i > SLAVE_NUM)      //只有一台，没有轮循到需要发送的地址
    {
        bOneBatt = TRUE;
        return FALSE;
    }

    if(ucPollAddr == s_tMyBms.ucAddress)
    {
        sendSNMPRealData(ucBattAddrNet);      //发送数据
        return TRUE;
    }

    aucBuff[0] = ID_SNMP_REALDATA;
    aucBuff[1] = ucBattAddrNet;

    sendCanFrm(ucPollAddr, aucBuff);

    return TRUE;
}

/*****************************************SNMP 同步数据解析 **********************************************/

static BOOLEAN ClearCommToSnmpAgentTimer(BYTE ucCanSrcAddr)
{
    if (ucCanSrcAddr >= 1 && ucCanSrcAddr <= SLAVE_NUM)
    {
        s_atSlavesRealData[ucCanSrcAddr - 1].ucCommFailToSnmpAgentCnt = 0;
        s_atSlavesRealData[ucCanSrcAddr - 1].bConnectedToSnmpAgent = True;
        return True;
    }
    return False;
}


static BOOLEAN ParseSNMPRealDataFrm(T_Can_Frame * ptCan_Frame)
{
    BYTE ucDstSn = 0;
    BYTE *p = NULL;

    if(ptCan_Frame == NULL)
    {
        return FALSE;
    }
    p = (BYTE *)&s_tCanDataCache_BMS[ptCan_Frame->tHeader.SrcAdr-1].aucDataBuf[0];
    ucDstSn = p[1];

    if(ucDstSn == 0 || ucDstSn > SLAVE_NUM)
    {
        return FALSE;
    }

    if(ucDstSn == s_tMyBms.ucAddress)
    {
        // 在位状态置位，清定时器
        ClearCommToSnmpAgentTimer(ptCan_Frame->tHeader.SrcAdr);

        SetSNMPRealData(&p[2], ptCan_Frame->tHeader.SrcAdr);             //收数据
    }
    else
    {
        sendSNMPRealData(ucDstSn);          //发数据
    }
    return TRUE;
}


/*****************************************SNMP 同步数据发送 **********************************************/
static BOOLEAN sendSNMPRealData(BYTE ucAddr)
{
    WORD wLen;
    if (ucAddr > SLAVE_NUM || ucAddr == 0)
    {
        return FALSE;
    }

    rt_memset(s_aucSNMPRealDataBuffSend, 0, sizeof(s_aucSNMPRealDataBuffSend));

    s_aucSNMPRealDataBuffSend[0] = ID_SNMP_REALDATA;
    s_aucSNMPRealDataBuffSend[1] = ucAddr;
    wLen = CollectRealDataForSNMP(&s_aucSNMPRealDataBuffSend[4], sizeof(s_aucSNMPRealDataBuffSend)-4);
    if(wLen == 0)
    {
        return FALSE;
    }

    s_aucSNMPRealDataBuffSend[2] = (wLen>>8)&0xFF;
    s_aucSNMPRealDataBuffSend[3] = wLen&0xFF;

    ChangeIntoCanFrm(s_aucSNMPRealDataBuffSend, (4 + wLen), DEV_TYPE_BMS, ucAddr);

    return TRUE;
}

/*****************************************SNMP 周期轮询同步告警 **********************************************/
static BOOLEAN getSNMPRealAlarm(void)
{
    BYTE aucBuff[7] = {0};

    if(ucBattAddrNet == 0 || !IsMaster())      //1、主从机系统中，没有电池接入网络则不收集数据 2、只有主机发轮询获取指令 
    {
        return FALSE;
    }

    if(bOneBatt == TRUE)
    {
        return FALSE;
    }

    if(ucPollAddr == s_tMyBms.ucAddress)
    {
        sendSNMPRealAlarm(ucBattAddrNet);      //发送数据
        return TRUE;
    }

    aucBuff[0] = ID_SNMP_REALALARM;
    aucBuff[1] = ucBattAddrNet;

    sendCanFrm(ucPollAddr, aucBuff);

    return TRUE;
}

/*****************************************SNMP 同步告警解析 **********************************************/
static BOOLEAN ParseSNMPRealAlarmFrm(T_Can_Frame * ptCan_Frame)
{
    BYTE ucDstSn = 0;
    BYTE *p = NULL;
    
    if(ptCan_Frame == NULL)
    {
        return FALSE;
    }
    p = (BYTE *)&s_tCanDataCache_BMS[ptCan_Frame->tHeader.SrcAdr-1].aucDataBuf[0];
    ucDstSn = p[1];

    if(ucDstSn == 0 || ucDstSn > SLAVE_NUM)
    {
        return FALSE;
    }

    if(ucDstSn == s_tMyBms.ucAddress)
    {
        SetSNMPRealAlarm(&p[2], ptCan_Frame->tHeader.SrcAdr);             //收数据
    }
    else
    {
        sendSNMPRealAlarm(ucDstSn);          //发数据
    }
    return TRUE;
}

/*****************************************SNMP 同步数据发送 **********************************************/
static BOOLEAN sendSNMPRealAlarm(BYTE ucAddr)
{
    WORD wLen;
    if (ucAddr > SLAVE_NUM || ucAddr == 0)
    {
        return FALSE;
    }

    rt_memset(s_aucSNMPRealDataBuffSend, 0, sizeof(s_aucSNMPRealDataBuffSend));

    s_aucSNMPRealDataBuffSend[0] = ID_SNMP_REALALARM;
    s_aucSNMPRealDataBuffSend[1] = ucAddr;
    wLen = (WORD)CollectRealAlarmForSNMP(&s_aucSNMPRealDataBuffSend[4], sizeof(s_aucSNMPRealDataBuffSend)-4);
    if(wLen == 0)
    {
        return FALSE;
    }

    s_aucSNMPRealDataBuffSend[2] = (wLen>>8)&0xFF;
    s_aucSNMPRealDataBuffSend[3] = wLen&0xFF;

    ChangeIntoCanFrm(s_aucSNMPRealDataBuffSend, (4 + wLen), DEV_TYPE_BMS, ucAddr);

    return TRUE;
}

/*****************************************SNMP 周期同步参数 **********************************************/
static BOOLEAN SNMPSyncPara(void)
{
    BYTE aucBuff[7] = {0};

    if(ucBattAddrNet == 0 || !IsMaster())      //1、主从机系统中，没有电池接入网络则不收集数据 2、只有主机发轮询获取指令 
    {
        return FALSE;
    }

    if(ucBattAddrNet == s_tMyBms.ucAddress)
    {
        sendSNMPSyncPara();      //发送数据
        return TRUE;
    }

    aucBuff[0] = ID_SNMP_SYNCPARA;
    aucBuff[1] = 0x00;

    sendCanFrm(ucBattAddrNet, aucBuff);

    return TRUE;
}

/*****************************************SNMP 周期同步参数 **********************************************/
static BOOLEAN ParseSNMPSyncPara(T_Can_Frame * ptCan_Frame)
{
    BYTE *p = NULL;
    
    if(ptCan_Frame == NULL)
    {
        return FALSE;
    }
    p = (BYTE *)&s_tCanDataCache_BMS[ptCan_Frame->tHeader.SrcAdr-1].aucDataBuf[0];

    if(ucBattAddrNet == s_tMyBms.ucAddress)
    {
        sendSNMPSyncPara();          //发数据
    }
    else
    {
        SetSNMPSyncPara(&p[2]);             //收数据
    }
    return TRUE;
}

/*****************************************SNMP 周期同步参数 **********************************************/
static BOOLEAN sendSNMPSyncPara(void)
{
    WORD wLen;

    rt_memset(s_aucSNMPRealDataBuffSend, 0, sizeof(s_aucSNMPRealDataBuffSend));

    s_aucSNMPRealDataBuffSend[0] = ID_SNMP_SYNCPARA;
    s_aucSNMPRealDataBuffSend[1] = 0x00;
    wLen = (WORD)CollectSyncParaForSNMP(&s_aucSNMPRealDataBuffSend[4]);

    s_aucSNMPRealDataBuffSend[2] = wLen/256;
    s_aucSNMPRealDataBuffSend[3] = wLen%256;

    ChangeIntoCanFrm(s_aucSNMPRealDataBuffSend, (4 + wLen), DEV_TYPE_BMS, 0x00);

    return TRUE;
}

/*****************************************SNMP 周期同步字符串参数 **********************************************/
static BOOLEAN SNMPSyncParaStr(void)
{
    BYTE aucBuff[7] = {0};

    if(ucBattAddrNet == 0 || !IsMaster())      //1、主从机系统中，没有电池接入网络则不收集数据 2、只有主机发轮询获取指令 
    {
        return FALSE;
    }

    if(ucBattAddrNet == s_tMyBms.ucAddress)
    {
        sendSNMPSyncParaStr();      //发送数据
        return TRUE;
    }

    aucBuff[0] = ID_SNMP_SYNCPARA_STR;
    aucBuff[1] = 0x00;

    sendCanFrm(ucBattAddrNet, aucBuff);

    return TRUE;
}

/*****************************************SNMP 周期同步参数 **********************************************/
static BOOLEAN ParseSNMPSyncParaStr(T_Can_Frame * ptCan_Frame)
{
    BYTE *p = NULL;
    
    if(ptCan_Frame == NULL)
    {
        return FALSE;
    }
    p = (BYTE *)&s_tCanDataCache_BMS[ptCan_Frame->tHeader.SrcAdr-1].aucDataBuf[0];

    if(ucBattAddrNet == s_tMyBms.ucAddress)
    {
        sendSNMPSyncParaStr();          //发数据
    }
    else
    {
        SetSNMPSyncParaStr(&p[2]);             //收数据
    }
    return TRUE;
}

/*****************************************SNMP 周期同步参数 **********************************************/
static BOOLEAN sendSNMPSyncParaStr(void)
{
    WORD wLen;

    rt_memset(s_aucSNMPRealDataBuffSend, 0, sizeof(s_aucSNMPRealDataBuffSend));

    s_aucSNMPRealDataBuffSend[0] = ID_SNMP_SYNCPARA_STR;
    s_aucSNMPRealDataBuffSend[1] = 0x00;
    wLen = (WORD)CollectSyncParaStrForSNMP(&s_aucSNMPRealDataBuffSend[4]);

    s_aucSNMPRealDataBuffSend[2] = wLen/256;
    s_aucSNMPRealDataBuffSend[3] = wLen%256;

    ChangeIntoCanFrm(s_aucSNMPRealDataBuffSend, (4 + wLen), DEV_TYPE_BMS, 0x00);

    return TRUE;
}

/*****************************************SNMP 周期轮询厂家信息 **********************************************/
static BOOLEAN getSNMPFactInfo(void)
{
    BYTE aucBuff[7] = {0};

    if(ucBattAddrNet == 0 || !IsMaster())      //1、主从机系统中，没有电池接入网络则不收集数据 2、只有主机发轮询获取指令 
    {
        return FALSE;
    }

    if(bOneBatt == TRUE)
    {
        return FALSE;
    }

    if(ucPollAddr == s_tMyBms.ucAddress)
    {
        sendSNMPFactInfo(ucBattAddrNet);      //发送数据
        return TRUE;
    }

    aucBuff[0] = ID_SNMP_FACTINFO;
    aucBuff[1] = ucBattAddrNet;

    sendCanFrm(ucPollAddr, aucBuff);

    return TRUE;
}

/*****************************************SNMP 厂家信息解析 **********************************************/
static BOOLEAN ParseSNMPFactInfoFrm(T_Can_Frame * ptCan_Frame)
{
    BYTE ucDstSn = 0;
    BYTE *p = NULL;
    
    if(ptCan_Frame == NULL)
    {
        return FALSE;
    }
    p = (BYTE *)&s_tCanDataCache_BMS[ptCan_Frame->tHeader.SrcAdr-1].aucDataBuf[0];
    ucDstSn = p[1];

    if(ucDstSn == 0 || ucDstSn > SLAVE_NUM)
    {
        return FALSE;
    }

    if(ucDstSn == s_tMyBms.ucAddress)
    {
        SetSNMPFactInfo(&p[2], ptCan_Frame->tHeader.SrcAdr);             //收数据
    }
    else
    {
        sendSNMPFactInfo(ucDstSn);          //发数据
    }
    return TRUE;
}

/*****************************************SNMP 厂家信息发送 **********************************************/
static BOOLEAN sendSNMPFactInfo(BYTE ucAddr)
{
    WORD wLen;
    if (ucAddr > SLAVE_NUM || ucAddr == 0)
    {
        return FALSE;
    }

    rt_memset(s_aucSNMPRealDataBuffSend, 0, sizeof(s_aucSNMPRealDataBuffSend));

    s_aucSNMPRealDataBuffSend[0] = ID_SNMP_FACTINFO;
    s_aucSNMPRealDataBuffSend[1] = ucAddr;
    wLen = (WORD)CollectFactInfoForSNMP(&s_aucSNMPRealDataBuffSend[4]);

    s_aucSNMPRealDataBuffSend[2] = wLen/256;
    s_aucSNMPRealDataBuffSend[3] = wLen%256;

    ChangeIntoCanFrm(s_aucSNMPRealDataBuffSend, (4 + wLen), DEV_TYPE_BMS, ucAddr);

    return TRUE;
}

/*****************************************SNMP 控制指令 **********************************************/
static BOOLEAN ParseSNMPCtrlCmdFrm(T_Can_Frame * ptCan_Frame)
{
    BYTE *p = NULL;
    T_SnmpCtrlPara tCtrl;
    
    if(ptCan_Frame == NULL)
    {
        return FALSE;
    }

    p = (BYTE *)&s_tCanDataCache_BMS[ptCan_Frame->tHeader.SrcAdr-1].aucDataBuf[0];

    if(p[1] == 0xF0)
    {
        SnmpSetParaFromBatt(p+2);
    }
    else if(p[1] == 0xF1)
    {
        SnmpSetParaStrFromBatt(p+2);
    }
    else
    {
        tCtrl.ucAddr = p[2];
        tCtrl.ucCmd = p[1];
        tCtrl.iData = *(INT32 *)(p+3);
        tCtrl.bSetFlag = FALSE;
        SnmpCtrl(&tCtrl);
    }

    return TRUE;
}

BOOLEAN SnmpSetParaToOtherBatt(BYTE ucCmd, BYTE ucOffset, BYTE *p, BYTE ucLen)
{
    BYTE aucBuff[35] = {0};         //SNMP控制指令可能会广播，所以使用长帧格式

    aucBuff[0] = ID_SNMP_CTRL;
    aucBuff[1] = ucCmd;
    aucBuff[2] = ucOffset;
    rt_memcpy(&aucBuff[3], p, ucLen);

    ChangeIntoCanFrm(aucBuff, sizeof(aucBuff), DEV_TYPE_BMS, 0x00);

    return TRUE;
}
#endif

Static void ParseWorkmodeFrm(BYTE *p)
{
    SHORT iVol;
    WORD wTmp;

    if(GetCellVoltStat())
    {
        return;
    }

    GetRealData(&s_tBCMData);
    if(p[1] == BATT_MODE_CHARGE || p[1] == BATT_MODE_DISCHARGE)
    {
        SetACStatus(p[1]);
        iVol = ( p[3]<<8 ) + p[2];
        wTmp = (p[5]<<8) + p[4];
        if (p[1])
        {
            if ( ID_SET_WORKMODE1 == p[0] )
            {
                iVol -= (SHORT)(s_tBCMData.fBattVolt * g_ProConfig.fVoltTrasRate * 100);
            }
            SetDischgVol(iVol, wTmp);
        }
        else
        {
            SetChgHopeVol( (FLOAT)iVol / 100.0 );
        }
#ifdef INTELLIGENT_PEAK_SHIFTING
        SetPeakShiftDisable(0);
#endif
    }
#ifdef INTELLIGENT_PEAK_SHIFTING
    else if(p[1] == BATT_MODE_DISABLE)
    {
        SetACStatus(BATT_MODE_CHARGE);
        SetPeakShiftDisable(1);
    }
#endif

    return;
}

Static void ParseChargeCurrFrm(BYTE* p)
{
    WORD wCurr = 0, wMaxBusVol=0;
    SHORT sMaxCurr=0;

    wCurr = p[2]<<8;
    wCurr += p[1];

    wMaxBusVol =  p[4]<<8;
    wMaxBusVol += p[3];

    sMaxCurr = p[6]<<8;
    sMaxCurr += p[5];
    
    SetBattChargeCurrAndMaxBusVol((FLOAT)wCurr/100.0, (FLOAT)wMaxBusVol/100.0, (FLOAT)sMaxCurr/100.0);
    return;
}

Static void ParseParaFrm(BYTE *p)
{
    WORD wFullVol, wPrtVol;

    wFullVol = p[1] + p[2]*256;
    wPrtVol = p[3] + p[4]*256;
    getGrpAddr();
    GetSysPara(&s_tSysPara);
    if ( wFullVol != FLOAT_TO_WORD(s_tSysPara.fBattChgFullAverVoltThre*100)
        || wPrtVol != FLOAT_TO_WORD(s_tSysPara.fBattUnderVoltPrtThre*100)
#ifdef DEVICE_USING_R321
        || s_tSysPara.bRelaySyncEn != p[5]
#endif
        )
    {
        s_tSysPara.fBattChgFullAverVoltThre = ((FLOAT)wFullVol)/100.0;
        s_tSysPara.fBattUnderVoltPrtThre = ((FLOAT)wPrtVol)/100.0;
#ifdef DEVICE_USING_R321
        s_tSysPara.bRelaySyncEn = p[5];
#endif
        SetSysPara( &s_tSysPara, True, CHANGE_BY_CAN);
    }

    return;
}

Static void ParseRealdataFrm(T_Can_Frame * ptCan_Frame)
{
    BYTE ucSn, *p;

    if ( IsMaster() )
    {
        ucSn = ptCan_Frame->tHeader.SrcAdr - 1;
        p = (BYTE *)&ptCan_Frame->tDataFrame.aucData[0];
        s_atSlavesRealData[ucSn].bExist = True;
        s_atSlavesRealData[ucSn].ucCommFailCnt = 0;
        s_atSlavesRealData[ucSn].fBatVol = ((FLOAT)(p[1] + p[2]*256)) / 100.0;
        s_atSlavesRealData[ucSn].fBatCur = (FLOAT)(SHORT)(p[3] | (p[4] << 8)) / 100.0;
        s_atSlavesRealData[ucSn].bChgPrt = p[5] & 0x01;
        s_atSlavesRealData[ucSn].bDischgPrt = (p[5]>>1) & 0x01;
        s_atSlavesRealData[ucSn].bChgLimit = (p[5]>>2) & 0x01;
        s_atSlavesRealData[ucSn].bDischgLimit = (p[5]>>3) & 0x01;
        s_atSlavesRealData[ucSn].bChargeInBreak = (p[5]>>4) & 0x01;
        s_atSlavesRealData[ucSn].bChargeStat = (p[5]>>5) & 0x01;   
        s_atSlavesRealData[ucSn].ucBattType = (p[5]>>6) & 0x01;
        s_atSlavesRealData[ucSn].bChgCurrSta = ((p[6] & 0x01) == 0);
        s_atSlavesRealData[ucSn].bRotateDisable = (p[6]>>1) & 0x01;
        s_atSlavesRealData[ucSn].bStatFault = (p[6]>>2) & 0x01;
        s_atSlavesRealData[ucSn].bExternalPowerOn = (p[6]>>3) & 0x01;
    }
    else
    {
        if(ptCan_Frame->tDataFrame.aucData[1] == R321_MASTER_SEND)
        {
            //主机为R321
            SetCurrBalanceMethod(False);
        }
        else
        {
            //主机为B3
            SetCurrBalanceMethod(True);
        }
        sendRealData();
    }

    return;
}

Static void sendRealData(void)
{
    BYTE aucBuff[7];
    T_DCRealData tDCRealData;
    T_BCMDataStruct tBcmData;
    T_BCMAlarmStruct tAlm;
    WORD wVol;
    FLOAT fCurr = 0.0;
    BYTE i, ucTmp;
    
    if (s_tMyBms.ucMasterAddr == 0)
    {
        return;
    }
    rt_memset_s(&aucBuff, sizeof(aucBuff), 0, 7);
    rt_memset_s(&tBcmData, sizeof(T_BCMDataStruct), 0, sizeof(T_BCMDataStruct));

    GetRealData(&tBcmData);
    if (BATT_MODE_OFFLINE == tBcmData.ucBattPackSta)
    {
        return;
    }
	GetBduReal(&tDCRealData);

    wVol = (WORD)(tBcmData.fBattVolt * g_ProConfig.fVoltTrasRate * 100);
    fCurr = (tBcmData.fBattCurr>0? tBcmData.fBattCurr : tBcmData.fBusCurr)* 100;

    aucBuff[0] = ID_GET_REALDATA;
    aucBuff[1] = wVol%256;
    aucBuff[2] = wVol/256;
    aucBuff[3]= ((SHORT)(fCurr)) & 0xff;
	aucBuff[4]= ((SHORT)(fCurr) >> 8) & 0xff;
    //aucBuff[6] = tBcmData.ucBattPackSta;
    aucBuff[5] = (tBcmData.ucChgProtSta | tBcmData.ucBattChargeEn | tBcmData.ucBduSleep) 
                + ( (tBcmData.ucDischgProtSta | tBcmData.ucBattDischEn | tBcmData.ucBduSleep)<<1 );
    aucBuff[5] |= (0x01 << 6);
    if (tBcmData.ucLimit != 0)
    {
        if (BATT_MODE_CHARGE == tBcmData.ucBattPackSta)
        {
            aucBuff[5] |= 0x04;
        }
        else if(BATT_MODE_DISCHARGE == tBcmData.ucBattPackSta)
        {
            aucBuff[5] |= 0x08;
        }
    }

    if (tDCRealData.tDCStatus.bInputBreak && !GetCellVoltStat())
    {
        aucBuff[5] |= 0x10;
    }

    if (BATT_MODE_CHARGE == tBcmData.ucBattPackSta)
    {
        aucBuff[5] |= 0x20;
    }
    else if (BATT_MODE_CONSRV == tBcmData.ucBattPackSta)
    {
        aucBuff[5] |= 0x80;
    }

    ucTmp = (BYTE)(!GetChgCurrStatus());
    aucBuff[6] |= ucTmp;

    ucTmp = (BYTE)GetSlaveRotateDisable();
    aucBuff[6] |= ucTmp<<1;

    ucTmp = (BYTE)GetStatusConsistFault();
    aucBuff[6] |= ucTmp<<2;
    
    ucTmp = (BYTE)tDCRealData.tDCStatus.bExternalPowerOn;
    aucBuff[6] |= ucTmp<<3;

    GetRealAlarm(BCM_ALARM_REAL, (BYTE *)&tAlm); // 获取未屏蔽的告警
    ucTmp = tBcmData.ucDischgProtSta | tBcmData.ucBattDischEn | tAlm.ucBattUnderVoltAlm;
    for (i=0; i<CELL_VOL_NUM_MAX; i++)
    {
        ucTmp |= tAlm.aucCellUnderVoltAlm[i];
    }
    if (ucTmp)
    {
        aucBuff[6] |= 0x10;
    }

    sendCanFrm(s_tMyBms.ucMasterAddr, aucBuff);

    return;
}

Static void ParseRealdataFrm3(T_Can_Frame * ptCan_Frame)
{
    BYTE ucSn, *p;

    if ( IsMaster() )
    {
        ucSn = ptCan_Frame->tHeader.SrcAdr - 1;
        p = (BYTE *)&ptCan_Frame->tDataFrame.aucData[0];
        s_atSlavesRealData[ucSn].fConnTemp = (FLOAT)(p[1] + p[2]*256)/10.0;
        s_atSlavesRealData[ucSn].bConnTempHighPrtStatus = p[3] & 0x01;
        if (!s_tConnTempHighPrt.ucSendedFlag && judgeConnTempHighStatus(ucSn)) {
            rt_memset_s(&s_tConnTempHighPrt, sizeof(s_tConnTempHighPrt),0x00, sizeof(s_tConnTempHighPrt));
            rt_memset_s(&s_tConnTempHighPrtRestore, sizeof(s_tConnTempHighPrtRestore), 0x00, sizeof(s_tConnTempHighPrtRestore));
            s_tConnTempHighPrt.ucSendedFlag = True;
            s_tConnTempHighPrtRestore.ucSendedFlag = False;
             //所有从机及自身连接器高温保护状态为1
            SetConnTempHighPrtStatus(1);
            setConnTempHighPrt(1);
        } else if (!s_tConnTempHighPrtRestore.ucSendedFlag && judgeConnTempHighStatusRestore(ucSn)) {
            rt_memset_s(&s_tConnTempHighPrt, sizeof(s_tConnTempHighPrt), 0x00, sizeof(s_tConnTempHighPrt));
            rt_memset_s(&s_tConnTempHighPrtRestore, sizeof(s_tConnTempHighPrtRestore), 0x00, sizeof(s_tConnTempHighPrtRestore));
            s_tConnTempHighPrtRestore.ucSendedFlag = True;
            s_tConnTempHighPrt.ucSendedFlag = False;
            //所有从机及自身连接器高温保护状态为0
            SetConnTempHighPrtStatus(0);
            setConnTempHighPrt(0);
        }
    }
    else
    {
        sendRealData3( );
    }

    return;
}

static BOOLEAN judgeConnTempHighStatus(BYTE ucSn) {
    T_BCMDataStruct tBcmData;
    BYTE ucSlaveNum = 0;

    rt_memset_s(&tBcmData, sizeof(T_BCMDataStruct), 0, sizeof(T_BCMDataStruct));

    //判断从机的连接器高温保护状态
    if (s_atSlavesRealData[ucSn].fConnTemp > CONN_TEMP_HIGH){
        if (++s_tConnTempHighPrt.ucSlaveReceivedNum[ucSn] >= CONN_TEMP_REFRESH) {
            return True;
        }
    }else {
        s_tConnTempHighPrt.ucSlaveReceivedNum[ucSn] = 0;
    }

     //判断主机的连接器高温保护状态
    GetRealData(&tBcmData);
    if(tBcmData.fConnTemp > CONN_TEMP_HIGH) {
        ucSlaveNum = GetBattNum();
        if(++s_tConnTempHighPrt.ucMasterReceivedNum >= CONN_TEMP_REFRESH*ucSlaveNum) {
            return True;
        }
    }else {
        s_tConnTempHighPrt.ucMasterReceivedNum = 0;
    }
    
    return False;      
}

static BOOLEAN judgeConnTempHighStatusRestore(BYTE ucSn) {
    T_BCMDataStruct tBcmData;
    BYTE ucSlaveNum = 0;
    BYTE i = 0;

    rt_memset_s(&tBcmData, sizeof(T_BCMDataStruct), 0, sizeof(T_BCMDataStruct));

    //判断主机的连接器高温保护状态
    GetRealData(&tBcmData);
    if(tBcmData.fConnTemp > CONN_TEMP_HIGH_RESTORE) {
        s_tConnTempHighPrtRestore.ucMasterReceivedNum = 0;
        rt_memset_s(&s_tConnTempHighPrtRestore, sizeof(s_tConnTempHighPrtRestore), 0x00, sizeof(s_tConnTempHighPrtRestore));
        return False;  
    } else {
        s_tConnTempHighPrtRestore.ucMasterReceivedNum++;
    }

    //判断从机的连接器高温保护状态
    if (s_atSlavesRealData[ucSn].fConnTemp > CONN_TEMP_HIGH_RESTORE) {
        s_tConnTempHighPrtRestore.ucMasterReceivedNum = 0;
        rt_memset_s(&s_tConnTempHighPrtRestore, sizeof(s_tConnTempHighPrtRestore), 0x00, sizeof(s_tConnTempHighPrtRestore));
        return False;
    } else {
        s_tConnTempHighPrtRestore.ucSlaveReceivedNum[ucSn]++;
    }

    ucSlaveNum = GetBattNum();

    if (s_tConnTempHighPrtRestore.ucMasterReceivedNum >= CONN_TEMP_REFRESH*ucSlaveNum && s_tConnTempHighPrtRestore.ucSlaveReceivedNum[ucSn] >= CONN_TEMP_REFRESH) {
        for(i = 0; i < SLAVE_NUM; i++) {
            if(s_atSlavesRealData[i].bExist == True && s_tConnTempHighPrtRestore.ucSlaveReceivedNum[i] < CONN_TEMP_REFRESH) {
                return False;
            }
        }
    } else {
        return False;
    }  
    
    return True;      
}

static void sendRealData3(void)
{
    BYTE aucBuff[7];
    T_BCMDataStruct tBcmData;
    WORD wConnTemp = 0;
    
    if (s_tMyBms.ucMasterAddr == 0)
    {
        return;
    }

    rt_memset_s(&aucBuff, sizeof(aucBuff), 0, 7);
    rt_memset_s(&tBcmData, sizeof(T_BCMDataStruct), 0, sizeof(T_BCMDataStruct));
  
	GetRealData(&tBcmData);

    wConnTemp = (WORD)(tBcmData.fConnTemp * 10);

    aucBuff[0] = ID_GET_REALDATA3;
    aucBuff[1] = wConnTemp%256;
    aucBuff[2] = wConnTemp/256;
    if(tBcmData.ucConnTempHighPrtStatus)
    {
        aucBuff[3] |= 0x01;
    }
    
    sendCanFrm(s_tMyBms.ucMasterAddr, aucBuff);

    return;
}

static void setConnTempHighPrt(BYTE bConnTempHighPrtStatus)
{
    BYTE aucBuff[7];

    rt_memset_s(&aucBuff, sizeof(aucBuff), 0, 7);

    aucBuff[0] = ID_SET_CONN_TEMP_HIGH;
    aucBuff[1] = bConnTempHighPrtStatus;

    sendCanFrm(s_tMyBms.ucGroup, aucBuff);

    return;
}


Static void ParseHighSOCFrm(T_Can_Frame * ptCan_Frame)
{
    BYTE ucSn, *p;
    
    if ( IsMaster() )
    {
        ucSn = ptCan_Frame->tHeader.SrcAdr - 1;
        p = (BYTE *)&ptCan_Frame->tDataFrame.aucData[0];
        
        s_atSlavesRealData[ucSn].wBatHighSOC = (p[1] + p[2]*256);
        s_atSlavesRealData[ucSn].fBusVol = (FLOAT)(p[3] + p[4]*256)/100.00;
        s_atSlavesRealData[ucSn].bLoopOff = p[5]&0x01;
        s_atSlavesRealData[ucSn].bUpgrade = (p[5]>>1)&0x01;
        s_atSlavesRealData[ucSn].bQuietSleepSta = (p[5]>>2) & 0x01;
        s_atSlavesRealData[ucSn].bBattLock = (p[5]>>3) & 0x01;
        s_atSlavesRealData[ucSn].bActivatePortCurrErr = (p[5]>>4) & 0x01;
        s_atSlavesRealData[ucSn].bNetAccess = (p[5]>>5) & 0x01;             //是否接入网络
        s_atSlavesRealData[ucSn].bRotateChgHeat = (p[5]>>6) & 0x01;
        s_atSlavesRealData[ucSn].ucRotateLimitCurr = p[6];
        s_atSlavesRealData[ucSn].ucCommFailCntAll = 0;
        s_atSlavesRealData[ucSn].bBattInSys = TRUE;
    }
    else
    {
        sendHighSOCData();
    }
    return;
}



static void sendHighSOCData( void )
{
    BYTE aucBuff[7] = {0};
    WORD wBusVol = 0;
    T_BattResult tBattOut = {0};

    if (s_tMyBms.ucMasterAddr == 0)
    {
        return;
    }

    GetRealData(&s_tBCMData);
    GetBattResult(&tBattOut);

    aucBuff[0] = ID_GET_HIGHSOC;
    aucBuff[1] = s_tBCMData.wBattHighPrecSOC;
    aucBuff[2] = s_tBCMData.wBattHighPrecSOC >> 8;
    
    // Added by fengfj, 2020-12-24 23:39:14
    wBusVol = (WORD)(s_tBCMData.fExterVolt*100+0.5);
    aucBuff[3] = wBusVol;
    aucBuff[4] = wBusVol >> 8;
    aucBuff[5] |= (tBattOut.bLoopOff & 0x01);
    aucBuff[5] |= (IsBduUpdate() ? 0x02 : 0);
    aucBuff[5] |= (GetSlaveQuietSleepStatus() ? 0x04 : 0);   //获取从机静置休眠状态
    aucBuff[5] |= (IsBattLock() ? 0x08 : 0);
#ifdef ACTIVATE_PORT_ENABLED
    aucBuff[5] |= (GetActivateErrorFlag() ? 0x10 : 0);      //获取激活口回路电流异常告警
#endif
#ifdef USING_SNMP
    aucBuff[5] |= (IsEthLink() ? 0x20 : 0);      //是否接入网络
#endif
    aucBuff[5] |= (getbChgTempLowPrtOnly() ? 0x40 : 0);      //是否在有加热膜的情况下只有充电低温保护

    aucBuff[6] = tBattOut.ucRotateMaxCurr;
    // End Added

    sendCanFrm(s_tMyBms.ucMasterAddr, aucBuff);

    return;
}


Static void ParseBattCapacityFrm(T_Can_Frame * ptCan_Frame)
{
    BYTE *p = RT_NULL;
    BYTE ucSn = 0;

    p = (BYTE *)&ptCan_Frame->tDataFrame.aucData[0];

    if (IsMaster())
    {
        ucSn = ptCan_Frame->tHeader.SrcAdr - 1;
        s_atSlavesRealData[ucSn].wBattCapacity = (WORD)((p[1] << 8) | p[2]);
        s_atSlavesRealData[ucSn].ucRelayCtrl = p[3];    //同步干接点控制
        s_atSlavesRealData[ucSn].fBusCur = (FLOAT)(SHORT)(p[4] | (p[5] << 8)) / 100.0;          //BUS电流
    }
    else
    {
        SynRelayCtrl(p[1]);     //同步干接点控制
        sendBattCapacity();
    }
}

Static void ParesePowerdownVoltFrame(BYTE *p)
{
    WORD wDisChargeVol;
    wDisChargeVol = (p[2] | (p[1]<<8));
    SetPowerdownVol(wDisChargeVol);
    return;
}

Static void ParseConnTempHighFrame(BYTE *p)
{
    BYTE ucConnTempHighPrtStatus = 0;
    ucConnTempHighPrtStatus =p[1];
    SetConnTempHighPrtStatus(ucConnTempHighPrtStatus);
    return;
}

Static void sendBattCapacity(void)
{
    BYTE aucBuff[7];
    FLOAT fCurr;

    rt_memset(&aucBuff[0], 0, sizeof(aucBuff));
    GetRealData(&s_tBCMData);
    fCurr = s_tBCMData.fBusCurr * 100;

    aucBuff[0] = ID_GET_BATT_CAPACITY;
    aucBuff[1] = (s_tBCMData.wBatteryCap >> 8) & 0xFF;
    aucBuff[2] = s_tBCMData.wBatteryCap & 0xFF;
    aucBuff[3] = GetRelayCtrl();        //同步干接点控制
    aucBuff[4]= ((SHORT)(fCurr)) & 0xff;
	aucBuff[5]= ((SHORT)(fCurr) >> 8) & 0xff;

    sendCanFrm(s_tMyBms.ucMasterAddr, aucBuff);
}
static void ParseConsrvFrm(T_Can_Frame const* ptFrame)
{
    switch (ptFrame->tDataFrame.aucData[1])
    {
        case QUITQUIETSLEEP:
            QuitQuietSleep(MASTER_CTRL);
            break;
        default:
            break;
    }
    return;
}

static void ParseEqualizeCurrFrm(T_Can_Frame * ptCan_Frame)
{
    BYTE *p;
    
    p = (BYTE *)&ptCan_Frame->tDataFrame.aucData[0];

    if ( IsMaster() )
    {
        return;
    }
    
    SetSlaveEqualizeCurr(p[1]+p[2]*256, p[3]+p[4]*256, p[5]);


    return;
}

// 从机同步主机时间及放电模式
static void ParseSynTimeModeFrm(T_Can_Frame *ptCan_Frame)
{
    BYTE *p, UsageScen;
    time_t tSynSecond, tTimeNow;
    struct tm TimeNow, tSetTime, tBaseTime;
    T_DateStruct tDate;
    S_32Int timeSync = {0,}; 

    rt_memset(&tDate, 0x00, sizeof(tDate));
    rt_memset(&TimeNow, 0x00, sizeof(TimeNow));
    rt_memset(&tSetTime, 0x00, sizeof(tSetTime));
    rt_memset(&tBaseTime, 0x00, sizeof(tBaseTime));

    tTimeNow = time(RT_NULL);
    localtime_r(&tTimeNow, &TimeNow);
    // 主机不需要同步时间和放电场景参数
    if (IsMaster())
    {
        return;
    }

    GetBaseTime(&tBaseTime);
    // CAN帧处理
    p = (BYTE *)&ptCan_Frame->tDataFrame.aucData[1];
    timeSync.ucData[0] = *p;
    timeSync.ucData[1] = *(++p);
    timeSync.ucData[2] = *(++p);
    timeSync.ucData[3] = *(++p);

    tSynSecond = timeSync.LData + mktime(&tBaseTime);

    localtime_r(&tSynSecond, &tSetTime);
    // 时间数据写入预处理
    tTime.PFC8563_Date.year   = tSetTime.tm_year;
    tTime.PFC8563_Date.month  = tSetTime.tm_mon + 1;
    tTime.PFC8563_Date.date   = tSetTime.tm_mday;
    tTime.PFC8563_Date.hour   = tSetTime.tm_hour;
    tTime.PFC8563_Date.min    = tSetTime.tm_min;
    tTime.PFC8563_Date.second = tSetTime.tm_sec;

    tDate.wYear = tTime.PFC8563_Date.year + 1900;
    tDate.ucMonth = tTime.PFC8563_Date.month;
    tDate.ucDay = tTime.PFC8563_Date.date;

    UsageScen = *(++p);

    s_tSysPara.ucUsageScen = UsageScen;

    if (!(SetSysPara(&s_tSysPara, True, CHANGE_BY_CAN)))
    {
        /*系统参数设置失败*/
    }
    //日期校验通过，进入保存操作记录逻辑
    if (CheckDateValid(&tDate) && PFC8563set_date(&tTime) == RT_EOK)
    {
       SaveSysTimeAciton(TimeNow);
    }

    return;
}

void GetBaseTime(struct tm* tBaseTime)
{
    // 定义基准时间，需要将传过来的时间秒数加上该时间，以保证4字节足够传输
    // 基准时间为2023-01-01，00：00：00
    tBaseTime->tm_year = 123;
    tBaseTime->tm_mon  = 0;
    tBaseTime->tm_mday = 1;
    tBaseTime->tm_hour = 0;
    tBaseTime->tm_min  = 0;
    tBaseTime->tm_sec  = 0;

    return;
}

//同步时间间隔大于60s时保存操作记录
static void SaveSysTimeAciton(struct tm TimeNow)
{
    struct tm TimeSet;
    time_t t = time(RT_NULL);
    INT32S tTimeDiff = 0;
    BYTE buff[21] = {0};

    localtime_r(&t, &TimeSet);  //设置后的系统时间

    time_t timestamp1 = mktime(&TimeNow);
    time_t timestamp2 = mktime(&TimeSet);

    tTimeDiff = timestamp1 - timestamp2;
    if(abs(tTimeDiff) > 60)
    {
        rt_snprintf_s((char *)buff, sizeof(buff), "%d-%02d-%02d %02d:%02d:%02d",
                 (WORD)(TimeNow.tm_year + 1900),
                 TimeNow.tm_mon + 1,
                 TimeNow.tm_mday,
                 TimeNow.tm_hour,
                 TimeNow.tm_min,
                 TimeNow.tm_sec);
        SaveAction(GetActionId(CONTOL_SET_TIME),(char *)buff);
    }
    return;
}

static void ParseRotateFrm(BYTE* p)
{
    BYTE ucCurr = 0;
    BOOLEAN bChargeDisable = False;
    T_BCMDataStruct tBcmData;
    rt_memset_s(&tBcmData, sizeof(T_BCMDataStruct), 0, sizeof(T_BCMDataStruct));
    GetRealData(&tBcmData);
    if(tBcmData.ucBattPackSta != BATT_MODE_CHARGE || IsMaster())
    {
        return;
    }

    ucCurr = p[1];
    bChargeDisable = p[2] & 0x01;

    SetRotate(ucCurr, bChargeDisable);
    return;
}

static void ParseDisChgVolFrm(BYTE* p)
{
    WORD wDisChargeVol;
	SetACStatus(p[1]);
    wDisChargeVol = (p[3] | (p[2]<<8));
    SetDisChargeVol(wDisChargeVol);
    return;
}

#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
static void ParseExteralPowerFram(BYTE *p)
{
    BOOLEAN bExternalPoweron = p[1] & 0x01;
    BOOLEAN bB3Exist = (p[1] >> 1) & 0x01; 

    SetExternalPowerOnFlag(bExternalPoweron);
    SetCurrBalanceMethod(bB3Exist);
}
#else
static void ParseExteralPowerFram(BYTE *p)
{
    SetExternalPowerOnFlag(p[1]);
}
#endif


#ifdef BOARDCAST_CONN_TEMP_HIGH_PRT_MONITORING
static BOOLEAN judgeConnTempHighStatusRestoreMaster(FLOAT fConnTemp) {

    //判断主机的连接器高温保护状态
    if(fConnTemp > CONN_TEMP_HIGH_RESTORE) {
        s_tConnTempHighPrtRestore.ucMasterReceivedNum = 0;
        rt_memset_s(&s_tConnTempHighPrtRestore, sizeof(s_tConnTempHighPrtRestore), 0x00, sizeof(s_tConnTempHighPrtRestore));
        return False;  
    }

    if (++s_tConnTempHighPrtRestore.ucMasterReceivedNum < CONN_TEMP_REFRESH) {
        return False;
    }

    return True;
}
#endif

static SHORT Proc_North_Frame(T_CommStruct* ptCommCan)
{
    WORD wCrc;
    switch (ptCommCan->ucPrtclType)
    {
        case PROTOCOL_1363:
            ptCommCan->wRecLength = Get1363DataFromCache(s_tCanDataCache.aucDataBuf, ptCommCan->aucRecBuf, ptCommCan->wRecLength);
            if (ptCommCan->wRecLength > 0)
            {
                Deal1363CommData(ptCommCan);
            }
            break;
        case PROTOCOL_MODBUS:
            if (s_tCanDataCache.aucDataBuf[0] != s_tMyBms.ucAddress
                && s_tCanDataCache.aucDataBuf[0] != s_tMyBms.ucGroup)
            {
                return -1;
            }
            if (ptCommCan->wRecLength < 8)  //单帧报文补充CRC码
            {
                wCrc = GetCRC16(s_tCanDataCache.aucDataBuf, 6);
                s_tCanDataCache.aucDataBuf[6] = wCrc%256;
                s_tCanDataCache.aucDataBuf[7] = wCrc/256;
                ptCommCan->wRecLength = 8;
            }
            DealMdbsRtuCommData(s_tCanDataCache.aucDataBuf, ptCommCan->wRecLength, NORTH_CAN_MODBUS, ptCommCan);
            break;
        case PROTOCOL_DL:
            //RETURN_VAL_IF_FAIL(ptCommCan->wRecLength >= DOWNLOAD_APP_MIN_LEN, False);
            rt_memcpy(ptCommCan->aucRecBuf, s_tCanDataCache.aucDataBuf, ptCommCan->wRecLength);
            //can1中接收SCU下发的下载帧中包含透传标志，不响应
            if (DOWNLOAD_TRANS == ptCommCan->aucRecBuf[DOWNLOAD_RSV_TRANS_INDEX]) { 
                return 0;
            }
            SetPrtclDLLinkType(DL_LINK_TYPE_CAN1);
            DealCanDLData(ptCommCan);
            break;
        default:
            return 0;
    }

    return 1;
}

struct rt_can_filter_item can_filter_items[1] = {
    RT_CAN_FILTER_ITEM_INIT(0x10000000, 1, 0, 1, 0x10000000), /* std,match ID:0x100~0x1ff，hdr 为 - 1，设置默认过滤表 */
};

void InitCan1(void)
{
    struct rt_can_filter_config cfg = {sizeof(can_filter_items)/sizeof(can_filter_items[0]), 1, can_filter_items};

#ifdef UNITEST
    can1_dev = rt_device_find(CAN1_DEV_NAME);
    s_ptMutexCan1 = rt_mutex_create("can1_lock", RT_IPC_FLAG_PRIO);
#else
    InitSuData();
    do
    {
        can1_dev = rt_device_find(CAN1_DEV_NAME);
    }while(RT_NULL == can1_dev);

    do
    {
        s_ptMutexCan1 = rt_mutex_create("can1_lock", RT_IPC_FLAG_PRIO);
    }while(RT_NULL == s_ptMutexCan1);
#endif
    rt_sem_init(&can1_rx_sem, "can_sem", 0, RT_IPC_FLAG_FIFO);
    rt_mutex_take(s_ptMutexCan1, RT_WAITING_FOREVER);
    rt_device_open(can1_dev, RT_DEVICE_FLAG_INT_TX | RT_DEVICE_FLAG_INT_RX);
    rt_device_set_rx_indicate(can1_dev, can1_rx_call);
    rt_device_control(can1_dev, RT_CAN_CMD_SET_FILTER, &cfg);
    rt_mutex_release(s_ptMutexCan1);
}
BOOLEAN DealCan2Connected(int Cnt){
    if (GetCan2ConnectedFlag()&& Cnt > 300){
        ForcemasterCnt = 0;
        s_tMyBms.ucMasterAddr = s_tMyBms.ucAddress;
        SendForceMasterFrm();
        s_bIsBigBattStage = True;
        return True;
    }
    return False;
}


static BOOLEAN DoIfNotParallUpdate(void)
{
    if (!IsParallUpdate())
    {
        masterSlaveComm();
        BMSAddrCompete();
        return True;
    }
    return False;
}


/****************************************************************
函数名：Process_CAN_Comm
入口参数：无
出口参数：无
功能：CAN通讯主函数
****************************************************************/
void Process_CAN_Comm(void* parameter)
{
    rt_uint32_t ulRetCnt = 0;
    rt_uint32_t ulCommFailCnt = 0;
    struct rt_can_msg canmsg;

    pre_thread_beat_f(THREAD_CAN_COMM);
    BduCtrl(SCI_CTRL_STOP_CAN_COMM, False);  // 兜底，启动恢复一次BDU CAN通信
#ifdef INTELLIGENT_PEAK_SHIFTING
    InitFmInfoNotice(&s_tFmInfoNotice);
#endif

#ifdef SITE_ANTI_THEFT_ENABLED
    InitBindingInfo();
#endif

#  ifdef UNITEST
    while (IsRunning())
#  else
    while(1)
#endif
    {
        thread_beat_go_on(THREAD_CAN_COMM);
#ifdef SITE_ANTI_THEFT_ENABLED
        s_wSendBindingInfoCnt++;
        DealBindingInfo();
#endif
#ifdef USING_SNMP
        JudgeCommFailToSnmpAgent();
#endif
#ifdef INTELLIGENT_PEAK_SHIFTING
        //模板转发入口,放在接受下面，存在电池一直接受数据而不往下走的情况，必须放在接受上面
        TemplateForwardMain(&s_tCommCan);
#endif
        if ( RT_EOK == rt_sem_take(&can1_rx_sem, 10) )
        {
            canmsg.hdr = -1;
            rt_mutex_take(s_ptMutexCan1, RT_WAITING_FOREVER);
            ulRetCnt = rt_device_read(can1_dev, 0, &canmsg, sizeof(canmsg));
            rt_mutex_release(s_ptMutexCan1);
            if(ulRetCnt > 0)
            {
                CheckApptestCan((T_Can_Frame *)&canmsg);
                Proc_CAN_Frame( (T_Can_Frame *)&canmsg );
                ulCommFailCnt = 0;
            }
            continue;
        }
#ifdef DEVICE_USING_R321
        Process_DL_Main(&s_tCommCan);
        //解决圈复杂度
        if(WaitAddrTimeoutJudege())
        {
            continue;
        }
        DealCan2Connected(ForcemasterCnt++);
#ifdef INTELLIGENT_PEAK_SHIFTING
        DealFmInfoNotice(&s_tFmInfoNotice);
#endif
        
#endif
        checkMasterExist();
        if (ulCommFailCnt++ > 3000)
        {
            bIsCanClose = TRUE;
            rt_mutex_take(s_ptMutexCan1, RT_WAITING_FOREVER);
            rt_device_close(can1_dev);
            rt_thread_delay(10);
            rt_device_open(can1_dev, RT_DEVICE_FLAG_INT_TX | RT_DEVICE_FLAG_INT_RX);
            rt_mutex_release(s_ptMutexCan1);
            bIsCanClose = FALSE;
            ulCommFailCnt = 0;
            continue;
        }
        MainLoopCanNorthCommDisconnectJudge();
        s_tMyBms.wCnt++;
        DoIfNotParallUpdate();
        BMSUpdatePollSlaveDev();
        JudgeAndHandleUpdateTimeOut();
    }
}

static void MainLoopCanNorthCommDisconnectJudge(void)
{
    if (s_ulCanNorthDisconnCnt++ > 3000)
    {
        s_bCanNorthCommDisconnect = True;
        s_ulCanNorthDisconnCnt = 0;
    }
    return;
}

static void MainLoopDealSlaveTCB(void)
{
    if (s_tSlaveTCB.aucCnt[s_tSlaveTCB.ucWaitAddr - 1] < 10)
    {
        s_tSlaveTCB.aucCnt[s_tSlaveTCB.ucWaitAddr - 1]++;
    }
    else
    {
        s_tSlaveTCB.aucLink[s_tSlaveTCB.ucWaitAddr - 1] = 0;
        s_tSlaveTCB.aucCnt[s_tSlaveTCB.ucWaitAddr - 1] = 0;
    }
    s_tSlaveTCB.ucWaitAddr = 0;
    s_tSlaveTCB.ucWaitTime = 0;
    return;
}

static rt_err_t can1_rx_call(rt_device_t dev, rt_size_t size)
{
    rt_sem_release(&can1_rx_sem);
    return RT_EOK;
}

#ifdef BOARDCAST_CONN_TEMP_HIGH_PRT_MONITORING
static void CheckConnTempHighPrtStatusConsistency(BYTE ucConnTempHighPrtStatusMaster) {
    BYTE i = 0;
    if (ucConnTempHighPrtStatusMaster) {
        s_tConnTempHighPrtRestore.ucSendedFlag = False;
        for(i = 0; i < SLAVE_NUM; i++) {
            if(s_atSlavesRealData[i].bExist == True && !s_atSlavesRealData[i].bConnTempHighPrtStatus) {
                s_tConnTempHighPrt.ucSendedFlag = False;
                return;
            }
        }
    } else {
        s_tConnTempHighPrt.ucSendedFlag = False;
        for(i = 0; i < SLAVE_NUM; i++) {
            if(s_atSlavesRealData[i].bExist == True && s_atSlavesRealData[i].bConnTempHighPrtStatus) {
                s_tConnTempHighPrtRestore.ucSendedFlag = False;
                return;
            }
        }
    }
    return;
}

static void masterPreDealConnTempHighPrt(void) {
    BYTE ucSlaveNum;
    T_BCMDataStruct tBcmData;

    rt_memset_s(&tBcmData, sizeof(T_BCMDataStruct), 0, sizeof(T_BCMDataStruct));

    //判断主机的连接器高温保护状态
    GetRealData(&tBcmData);

    ucSlaveNum = GetBattNum() - 1;
    if (ucSlaveNum == 0) {
        //处理只有主机没有从机的场景
        if(tBcmData.ucConnTempHighPrtStatus&&judgeConnTempHighStatusRestoreMaster(tBcmData.fConnTemp)) {
            s_tConnTempHighPrt.ucSendedFlag = False;
            SetConnTempHighPrtStatus(0);
        }
    } else {
        //处理有从机未收到下发高温保护状态命令的场景（主机连接器高温保护状态和从机保持一致）
        CheckConnTempHighPrtStatusConsistency(tBcmData.ucConnTempHighPrtStatus);
    }  
    //从机有变化初始化标志（防止从机增加不判断的情况）
    if (s_ucSlaveNum != ucSlaveNum) {
        s_tConnTempHighPrt.ucSendedFlag = False;
        s_tConnTempHighPrtRestore.ucSendedFlag = False;
    }
    s_ucSlaveNum = ucSlaveNum;
    return;
}
#endif

#ifdef USING_SNMP
BOOLEAN sendForSnmp(void)
{
    if (s_tMyBms.wCnt % SNMPDATA_CYCLE == 20)
    {
        getSNMPRealData();
    }
    if (s_tMyBms.wCnt % SNMPDATA_CYCLE == 30)
    {
        getSNMPRealAlarm();
    }
    if (s_tMyBms.wCnt % SNMPDATA_CYCLE == 40)
    {
        SNMPSyncPara();
    }
    if (s_tMyBms.wCnt % SNMPDATA_CYCLE == 50)
    {
        getSNMPFactInfo();
    }
    if (s_tMyBms.wCnt % SNMPDATA_CYCLE == 70)
    {
        SNMPSyncParaStr();
    }
    return TRUE;
}
#endif

Static void masterComm(void)
{
    BYTE i;
    
    if ( s_tMyBms.wCnt >= 6000 )
    {
        synBMSPara();
        s_tMyBms.wCnt = 0;
        return;
    }
    if(s_tMyBms.wCnt%REALDATA_CYCLE == 10)
    {
        getBMSData();
        for ( i=0; i<SLAVE_NUM; i++ )
        {
            if(s_atSlavesRealData[i].bExist == True)
            {
                TimerPlus(s_atSlavesRealData[i].ucCommFailCnt, MASTER_SLAVE_COMM_FAIL_CNT);
                if (s_atSlavesRealData[i].ucCommFailCnt >= MASTER_SLAVE_COMM_FAIL_CNT)
                {
                    s_atSlavesRealData[i].bExist = False;
                }
            }
        }
        return;
    }
    #ifdef BOARDCAST_CONN_TEMP_HIGH_PRT_MONITORING
    if (s_tMyBms.wCnt%REALDATA_CYCLE == 40)
    {
        getRealData3();
        masterPreDealConnTempHighPrt();
    }
    #endif
    GetBattChgPowerCount();

    if (s_tMyBms.wCnt%REALDATA_CYCLE == 60)
    {
        getHighSOCData();

        for ( i=0; i<SLAVE_NUM; i++ )
        {
            if(s_atSlavesRealData[i].bBattInSys == True)
            {
                TimerPlus(s_atSlavesRealData[i].ucCommFailCntAll, MASTER_SLAVE_COMM_FAIL_CNT);
                if (s_atSlavesRealData[i].ucCommFailCntAll >= MASTER_SLAVE_COMM_FAIL_CNT)
                {
                    s_atSlavesRealData[i].bBattInSys = False;
                #ifdef USING_SNMP
                    InitSNMPData(i);
                #endif
                }
            }
        }
    }
    if (s_tMyBms.wCnt % REALDATA_CYCLE == 80)
    {
        getBattCapacity();
    }

#ifdef USING_SNMP
    sendForSnmp();
#endif

    return;
}

static void BMSAddrCompete(void)
{
    if (s_tMyBms.ucMasterAddr == 0)    //地址竞争阶段
    {
        if ( s_tMyBms.wCnt > 3 * SLAVE_NUM )
        {
            s_tMyBms.ucMasterAddr = 1;
            s_tMyBms.wCnt = 0;
            InitSlaveData();
            saveResetData(BMS_ADDR, s_tMyBms.ucAddress);
            s_ucAddrCompFLAG = 0;
        }
        else if (s_tMyBms.wCnt%(SLAVE_NUM + 1) == s_tMyBms.ucAddress)
        {
            SendCompeteFrm(getMaskedSNPart());           //发送竞争帧
        }
    }

    return;
}

INT32S CanRecCon(void)
{
    if(s_slCanDealCounter != 0)
    {
        s_slCanDealCounter = 0;
        return 1;
    }

    return 0;
}

void SetMasterExist(BOOLEAN bFlag)
{
    s_bMasterExist = !!bFlag;
}

BOOLEAN IsMasterExist(void)
{
    return s_bMasterExist;
}
static void checkMasterExist(void)
{
    if(s_tMyBms.ucMasterAddr == s_tMyBms.ucAddress)
    {
        s_bMasterExist = True;
        return; 
    }
    if(s_bMasterExist == False)
    {
        return;
    }
    TimerPlus(s_uMasterNotExisTime, 1000);
    if(TimeOut(s_uMasterNotExisTime, 1000))
    {
       s_bMasterExist = False;       
    }
}

Static BOOLEAN IsSlaveBroadCast(T_Can_Frame * ptCan_Frame){
    if(ptCan_Frame->tDataFrame.aucData[0] == ID_CAN1_FIRE_LOCK){
        return True;
    }
    #ifdef USE_CAN2
    if(ptCan_Frame->tDataFrame.aucData[0] == FN_FORCE_MASTER){
        return True;
    }
    #endif
    return False;
}

Static BOOLEAN MasterCompete(T_Can_Frame * ptCan_Frame)
{
    if(ptCan_Frame->tHeader.SrcAdr != s_tMyBms.ucMasterAddr && ptCan_Frame->tHeader.DstAdr == 0 && IsSlaveBroadCast(ptCan_Frame))
    {
        return True;
    }
    
    if(ptCan_Frame->tDataFrame.SegFlg != 1 || ptCan_Frame->tDataFrame.SegNum != 0)         //首帧不为0x80  为长帧分包 互备份数据则接收 不进行主机竞争
    {
        return True;
    }

    if ( (s_tMyBms.ucMasterAddr == s_tMyBms.ucAddress)
        && (ptCan_Frame->tHeader.SrcAdr > s_tMyBms.ucAddress) )
    {
        return False;
    }

    if (s_tMyBms.ucMasterAddr != 0)
    {
        s_tMyBms.wCnt = 0;
        s_tMyBms.ucMasterAddr = ptCan_Frame->tHeader.SrcAdr;
    }
    return True;
}

#ifdef SITE_ANTI_THEFT_ENABLED
Static BOOLEAN CheckIfSiteAntitheftFrame(T_Can_Frame * ptCan_Frame)
{
    return (ptCan_Frame->tHeader.DstAdr == 0 && ptCan_Frame->tDataFrame.aucData[0] == FN_SITE_ANTITHEFT);
}
#endif

Static BOOLEAN CheckSrcAdrErr(T_Can_Frame * ptCan_Frame)
{
    return (ptCan_Frame->tHeader.SrcAdr < 1 || ptCan_Frame->tHeader.SrcAdr > SLAVE_NUM);
}

static BOOLEAN CheckAddress(T_Can_Frame * ptCan_Frame)
{
    if (CheckSrcAdrErr(ptCan_Frame))
    {
        return False;
    }

    GetSysPara(&s_tSysPara);

    if (ptCan_Frame->tHeader.DstDev != DEV_TYPE_BMS)
    {
        return False;
    }

    if (ptCan_Frame->tHeader.DstAdr == s_tMyBms.ucAddress)
    {
        return True;
    }

#ifdef SITE_ANTI_THEFT_ENABLED
    if (CheckIfSiteAntitheftFrame(ptCan_Frame))
    {
        return True;
    }
#endif

#if !defined(UNITEST) && defined(DEVICE_USING_D121)
    if (ptCan_Frame->tHeader.SrcDev == DEV_TYPE_BMS && \
        ptCan_Frame->tHeader.SrcAdr == s_tMyBms.ucAddress && \
        s_tSysPara.ucBattAddressMode == AUTO_MODE)     //地址冲突且地址获取方式为自动  
#else
    if (ptCan_Frame->tHeader.SrcDev == DEV_TYPE_BMS && ptCan_Frame->tHeader.SrcAdr == s_tMyBms.ucAddress)
#endif
    
    {
        s_tMyBms.ucMasterAddr = 0;         //回到地址竞争阶段
        s_tMyBms.wCnt = 0;
    }

    if ( ptCan_Frame->tHeader.DstAdr != s_tMyBms.ucGroup )
    {
        return False;
    }
    else if(ptCan_Frame->tHeader.SrcAdr == s_tMyBms.ucMasterAddr)
    {
       s_bMasterExist = True; 
       s_uMasterNotExisTime = 0;
    } 
    if(!MasterCompete(ptCan_Frame))
    {
        return False;
    }

    // #ifdef DEVICE_USING_R321 
    // if ( (s_tMyBms.ucMasterAddr == s_tMyBms.ucAddress)
    //     && (ptCan_Frame->tHeader.SrcAdr != s_tMyBms.ucAddress) && s_tMyBms_CAN2.ucMasterAddr == 1)
    // {
    //     SendForceMasterFrm();
    //     return False;
    // }
    // #endif

    return True;
}

ULONG getSNPart(void)
{
    BYTE i, ucTmp = 0;
    ULONG ulRet=0;
    T_BmsPACKFactoryStruct tBmsFactInfo;

    rt_memset(&tBmsFactInfo, 0, sizeof(T_BmsPACKFactoryStruct));

    readBmsPackFacInfo(&tBmsFactInfo);
    for (i=0;i<15;i++)
    {
        //ascii转16进制
        if (tBmsFactInfo.acBmsFacSn[i]<0x30 || tBmsFactInfo.acBmsFacSn[i] > 0x46)
        {
            break;
        }
        ucTmp = tBmsFactInfo.acBmsFacSn[i] - 0x30;
        if ( ucTmp > 0x10)
        {
            ucTmp = ucTmp - 7;
        }

        ulRet = ((ulRet & 0x0FFFFFFF) << 4) | ucTmp;
    }
    if ( i<4 )
    {
        return 0;
    }

    return ulRet;
}

ULONG getMaskedSNPart(void)
{
    return (getSNPart() | 0xF0000000);
}

static rt_uint64_t getSNAll(void)
{
    BYTE i, ucTmp = 0;
    rt_uint64_t ulRet=0;

    T_BmsPACKFactoryStruct tBmsFactInfo;
    rt_memset(&tBmsFactInfo, 0, sizeof(T_BmsPACKFactoryStruct));
    readBmsPackFacInfo(&tBmsFactInfo);

    for (i=0;i<15;i++)
    {
        //ascii转16进制
        if (tBmsFactInfo.acBmsFacSn[i]<0x30 || tBmsFactInfo.acBmsFacSn[i] > 0x46)
        {
            break;
        }
        ucTmp = tBmsFactInfo.acBmsFacSn[i] - 0x30;
        if ( ucTmp > 0x10)
        {
            ucTmp = ucTmp - 7;
        }

        ulRet = (ulRet << 4) | ucTmp;
    }
    if ( i<4 )
    {
        return 0;
    }

    return ulRet;
}

static void ParseCompeteFrm(BYTE *p)
{
#ifdef BATT_ADDR_MODE_SET_ENABLE
    BYTE ucAddrMode;
    ucAddrMode = p[2] & 0x01;  //地址竞争帧中BYTE2->BIT0表示地址设置模式 手动or自动
#endif
    ULONG ulBattSn = 0UL;
    if (s_tMyBms.ucAddress != p[1])
    {
        return;
    }
    ulBattSn = getMaskedSNPart();
#ifdef BATT_ADDR_MODE_SET_ENABLE
    /* 自身如果是手动模式需要将自己的序列号发出去，让自动模式的电池改变自己的地址。*/
    if (s_tSysPara.ucBattAddressMode == MANUAL_MODE)
    {
        SendCompeteFrm(ulBattSn);
    }
    else if (s_tSysPara.ucBattAddressMode == AUTO_MODE && ucAddrMode == MANUAL_MODE)
    {
        if (s_ucAddrCompFLAG == 1)
        {
            modifyBmsAddr(s_tMyBms.ucAddress + 1);
        }
        else
        {
            modifyBmsAddr(1);
            s_ucAddrCompFLAG = 1;
        }
    }
    else if (s_tSysPara.ucBattAddressMode == AUTO_MODE && ucAddrMode == AUTO_MODE)
#endif
    {
        ULONG ulRecvSn = *(ULONG *)(p + 3);
        if (ulBattSn < ulRecvSn)
        {
            if ( s_tMyBms.ucAddress == s_ucRepeteAdr )      //同一地址冲突只响应1次
            {
                return;
            }
            SendCompeteFrm(ulBattSn);
            s_ucRepeteAdr = s_tMyBms.ucAddress;
        }
        else
        {
            modifyBmsAddr(s_tMyBms.ucAddress + 1);
        }
    }
    return;
}


/* 生成互备份文件名 */
BOOLEAN BuildBackupFileName(BYTE *pucSN_fileName, BYTE* suffix, BYTE *pucFileName)
{
    // 计算新文件名的长度
    size_t newLength = rt_strnlen_s((char*)pucSN_fileName, BACKUP_FILE_NAME_MAX_LEN) + rt_strnlen_s((char*)suffix, BACKUP_FILE_NAME_MAX_LEN);
    if (newLength >= BACKUP_FILE_NAME_MAX_LEN)
    {
        return False;
    }

    // 拼接文件名前缀和后缀
    if (rt_strncat_s((char*)pucSN_fileName, BACKUP_FILE_NAME_MAX_LEN, (char*)suffix, rt_strnlen_s((char*)suffix, BACKUP_FILE_NAME_MAX_LEN)) < 0)
    {
        return False;
    }

    // 生成完整的文件路径
    if (rt_snprintf((char*)pucFileName, BACKUP_FILE_NAME_MAX_LEN, "/BACKUP/%s", pucSN_fileName) < 0)
    {
        return False;
    }
    
    return True;
}

/* 保存互备份文件处理 */
BOOLEAN HandleBackupData(BYTE *pInBuff, WORD wLen, BYTE *suffix, BYTE *pucFileName)
{
    U_64Int tData = {0};
    BYTE ucSN_fileName[BACKUP_FILE_NAME_MAX_LEN] = {0};

    RETURN_VAL_IF_FAIL(pInBuff != NULL, False);
    rt_memset_s(ucSN_fileName, BACKUP_FILE_NAME_MAX_LEN, 0, BACKUP_FILE_NAME_MAX_LEN);

    GetUint64Data(&tData, pInBuff);
    uint64Valuetostr(tData, ucSN_fileName, sizeof(ucSN_fileName));

    if (False == BuildBackupFileName(ucSN_fileName, suffix, pucFileName))
    {
        return False;
    }
    return True;
}

/****************************************************************************
* 函数名称：DealBMSBackUpData
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：存储互备份数据
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
BOOLEAN DealBMSBackUpData(BYTE* pInBuff)
{
    BYTE ucPackNum = 0;
    WORD wLen = 0;
    BYTE ucFileName[BACKUP_FILE_NAME_MAX_LEN] = {0};

    RETURN_VAL_IF_FAIL(pInBuff != NULL, False);
    rt_memset_s(ucFileName, sizeof(ucFileName), 0, BACKUP_FILE_NAME_MAX_LEN);

    if(*pInBuff == ID_BACKEUP_HISDATA)
    {
        pInBuff++;
        wLen = *pInBuff;
        RETURN_VAL_IF_FAIL(wLen > 8, False);
        pInBuff++;
        HandleBackupData(pInBuff, wLen, (BYTE*)"_HisData", ucFileName);
        WriteHisBackupData(ucFileName, pInBuff + 8, wLen - 8);
    }
    else if(*pInBuff == ID_BACKEUP_HISALARM)
    {
        pInBuff++;
        wLen = *pInBuff;
        RETURN_VAL_IF_FAIL(wLen > 8, False);
        pInBuff++;
        HandleBackupData(pInBuff, wLen, (BYTE*)"_HisAlarm", ucFileName);
        WriteHisBackupAlarm(ucFileName, pInBuff + 8, wLen - 8);
    }
    else if(*pInBuff == ID_BACKEUP_HISACT)
    {
        pInBuff++;
        wLen = *pInBuff;
        RETURN_VAL_IF_FAIL(wLen > 8, False);
        pInBuff++;
        HandleBackupData(pInBuff, wLen, (BYTE*)"_HisAct", ucFileName);
        WriteHisBackupAct(ucFileName, pInBuff + 8, wLen - 8);
    }
    else if(*pInBuff == ID_BACKEUP_BDURECORD)
    {
        pInBuff++;
        wLen = get_int16_data(pInBuff);
        RETURN_VAL_IF_FAIL(wLen > 8, False);
        pInBuff += 2;
        HandleBackupData(pInBuff, wLen, (BYTE*)"_BduRecord", ucFileName);
        WriteBduRecordBackup(ucFileName, pInBuff + 8, wLen - 8);
    }
    else if((*pInBuff == ID_BACKEUP_HISEXTREME_PACK1) || (*pInBuff == ID_BACKEUP_HISEXTREME_PACK2))
    {
        ucPackNum = *pInBuff;  //记录分包序号
        pInBuff++;
        wLen = get_int16_data(pInBuff);
        RETURN_VAL_IF_FAIL(wLen > 8, False);
        pInBuff += 2;
        HandleBackupData(pInBuff, wLen, (BYTE*)"_Hisextreme", ucFileName);
        if (ucPackNum == ID_BACKEUP_HISEXTREME_PACK1)
        {
            WriteExtremeBackup(ucFileName, pInBuff + 8, wLen - 8, ucPackNum); //保存极值互备份第一包数据
        }
        else
        {
            WriteExtremeBackup(ucFileName, pInBuff + 9, wLen - 9, ucPackNum); //保存极值互备份第二包数据，9:设备类型相同不需重复保存
        }
    }
    else if(*pInBuff == ID_BACKEUP_ANALYSE)
    {
        pInBuff++;
        wLen = *pInBuff;
        RETURN_VAL_IF_FAIL(wLen > 8, False);
        pInBuff++;
        HandleBackupData(pInBuff, wLen, (BYTE*)"_AnalyseInfo", ucFileName);
        WriteAnalyseBackup(ucFileName, pInBuff + 8, wLen - 8);
    }

    return True;
}


static SHORT DealBMSFrame3(T_Can_Frame * ptCan_Frame)
{

   switch (s_tCanDataCache_BMS[ptCan_Frame->tHeader.SrcAdr-1].aucDataBuf[0])
   {
    #ifdef USING_SNMP       
        case ID_SNMP_REALDATA:
            ParseSNMPRealDataFrm(ptCan_Frame);
            break;
        case ID_SNMP_REALALARM:
            ParseSNMPRealAlarmFrm(ptCan_Frame);
            break;
        case ID_SNMP_SYNCPARA:
            ParseSNMPSyncPara(ptCan_Frame);
            break;
        case ID_SNMP_SYNCPARA_STR:
            ParseSNMPSyncParaStr(ptCan_Frame);
            break;
        case ID_SNMP_FACTINFO:
            ParseSNMPFactInfoFrm(ptCan_Frame);
            break;
        case ID_SNMP_CTRL:
            ParseSNMPCtrlCmdFrm(ptCan_Frame);
            break;
    #endif
        case ID_SET_FM_CTRL:
            ParseFmCtrl(ptCan_Frame);
            break;
        case ID_GET_SOC_POWER:
            ParseMAXChgPowerFrm(ptCan_Frame);
            break; 

        default:
            return -1;
   }

   return 1;

}

static SHORT DealBMSFrame2(T_Can_Frame * ptCan_Frame)
{

    if(DealBMSFrame3(ptCan_Frame) == 1)
    {
        return 1;
    }

   switch (s_tCanDataCache_BMS[ptCan_Frame->tHeader.SrcAdr-1].aucDataBuf[0])
   {
      case ID_SET_EQUAL_CURR:
            ParseEqualizeCurrFrm(ptCan_Frame);
            break;
        case ID_SET_ROTATE_CTRL:
            ParseRotateFrm(ptCan_Frame->tDataFrame.aucData);
            break;
        case ID_SET_DISCHRGVOL:
            ParseDisChgVolFrm(ptCan_Frame->tDataFrame.aucData);
            break;
        case ID_GET_BATT_CAPACITY:
            ParseBattCapacityFrm(ptCan_Frame);
            break;
        case ID_SET_POWERDOWN_VOLT:
            ParesePowerdownVoltFrame(ptCan_Frame->tDataFrame.aucData);
            break;
        case ID_SET_CONN_TEMP_HIGH:
            ParseConnTempHighFrame(ptCan_Frame->tDataFrame.aucData);
            break;
        case ID_SET_SYN_TIME_MODE:
            ParseSynTimeModeFrm(ptCan_Frame);
            break;
        case ID_SET_EXTERAL_POWER_STATUS:
            ParseExteralPowerFram(ptCan_Frame->tDataFrame.aucData);
            break;
        #ifdef USE_CAN2 
        case FN_FORCE_MASTER:
            if(s_tMyBms.ucMasterAddr != 0){
                s_tMyBms.ucMasterAddr = ptCan_Frame->tDataFrame.aucData[1];
                SetBMSCan2Addr(ptCan_Frame->tDataFrame.aucData[2]);
                s_bIsBigBattStage = True;
            }
            break;
        #endif
        default:
            return -1;
   }

   return 1;

}

Static void SetWorkMode( BYTE* ucCnt )
{
    *ucCnt = (*ucCnt > 0) ? 5 : 1;
}

/**
 * @brief 解析从机回复的设备统计指令帧
 * @note BMS升级状态要在主机收完整包，升级前清零
*/
Static SHORT ParseSlaveScanDevFrm(T_Can_Frame* ptCan_Frame)
{
    BYTE updateSlaveAddr = 0;
    RETURN_VAL_IF_FAIL(ptCan_Frame != NULL, False);
    updateSlaveAddr = ptCan_Frame->tHeader.SrcAdr;
    RETURN_VAL_IF_FAIL((updateSlaveAddr <= SLAVE_NUM) && (updateSlaveAddr > 0), FAILURE);
    s_BMSUpdateState.battInPlaceFlag[updateSlaveAddr - 1] = True;
    return SUCCESSFUL;
}

/**
 * @brief 解析从机回复的升级标志指令帧
*/
Static SHORT ParseSlaveUpdateFlagFrm(T_Can_Frame* ptCan_Frame)
{
    BYTE updateSlaveAddr = 0;
    RETURN_VAL_IF_FAIL(ptCan_Frame != NULL, False);
    updateSlaveAddr = ptCan_Frame->tHeader.SrcAdr;
    RETURN_VAL_IF_FAIL((updateSlaveAddr <= SLAVE_NUM) && (updateSlaveAddr > 0), FAILURE);
    s_BMSUpdateState.stateSetUpdateFlag[updateSlaveAddr - 1] = True;
    return SUCCESSFUL;
}

/**
 * @brief 解析主机发送的升级标志指令帧
 * @note 
*/
Static SHORT ParseMasterUpdateFlagFrm(T_Can_Frame* ptCan_Frame)
{
    BYTE updateMasterAddr = 0;
    RETURN_VAL_IF_FAIL(ptCan_Frame != NULL, False);
    updateMasterAddr = ptCan_Frame->tHeader.SrcAdr;
    RETURN_VAL_IF_FAIL((updateMasterAddr <= SLAVE_NUM) && (updateMasterAddr > 0), FAILURE);
    SendSetUpdateFlagFrm(updateMasterAddr, FUNC_BMS_UPDATE_POLL_SLAVE_FRM, True);
    SetParallUpdateFlag(True);
    // 通知BDU停止CAN通信
    BduCtrl(SCI_CTRL_STOP_CAN_COMM, True);
    return SUCCESSFUL;
}

/**
 * @brief 解析主机发送的设备统计指令帧
*/
Static SHORT ParseMasterScanDevFrm(T_Can_Frame* ptCan_Frame)
{
    BYTE updateMasterAddr = 0;
    RETURN_VAL_IF_FAIL(ptCan_Frame != NULL, False);
    updateMasterAddr = ptCan_Frame->tHeader.SrcAdr;
    RETURN_VAL_IF_FAIL((updateMasterAddr <= SLAVE_NUM) && (updateMasterAddr > 0), FAILURE);
    SendScanDevFrm(updateMasterAddr, FUNC_BMS_UPDATE_POLL_SLAVE_FRM);
    return SUCCESSFUL;
}

/**
 * @brief 处理收到的BMS间升级指令帧
 * @retval True 匹配， False 不匹配
*/
Static BOOLEAN DealBMSFrameMatchUpdate(T_Can_Frame* ptCan_Frame)
{
    T_CAN_BMSFrameHandle deal[] = {
        {ID_SCAN_SLAVE_DEV, FUNC_BMS_UPDATE_POLL_SLAVE_FRM, ParseSlaveScanDevFrm},
        {ID_SET_UPDATE_FLAG, FUNC_BMS_UPDATE_POLL_SLAVE_FRM, ParseSlaveUpdateFlagFrm},
        {ID_SCAN_SLAVE_DEV, FUNC_BMS_UPDATE_POLL_MASTER_FRM, ParseMasterScanDevFrm},
        {ID_SET_UPDATE_FLAG, FUNC_BMS_UPDATE_POLL_MASTER_FRM, ParseMasterUpdateFlagFrm},
    };
    BYTE i = 0;
    BYTE updateSlaveAddr = 0;
    RETURN_VAL_IF_FAIL(ptCan_Frame != NULL, False);
    updateSlaveAddr = ptCan_Frame->tHeader.SrcAdr;
    RETURN_VAL_IF_FAIL((updateSlaveAddr <= SLAVE_NUM) && (updateSlaveAddr > 0), FAILURE);

    for (i = 0; i < sizeof(deal) / sizeof(T_CAN_BMSFrameHandle); i++)
    {
        if (deal[i].cmdID == s_tCanDataCache_BMS[updateSlaveAddr - 1].aucDataBuf[0] &&
            deal[i].func == s_tCanDataCache_BMS[updateSlaveAddr - 1].aucDataBuf[4])
        {
            deal[i].handle(ptCan_Frame);
            return True;
        }
    }
    return False;
}

/**
 * @brief 发送设备统计帧
 * @param[in] addr 访问的从机地址
 * @retval SUCCESSFUL 成功, FAILURE 失败
*/
Static SHORT SendScanDevFrm(BYTE addr, BYTE func)
{
    BYTE aucBuff[7] = {0};

    aucBuff[0] = ID_SCAN_SLAVE_DEV;
    aucBuff[1] = 0xE1;                  // 升级协议标志
    aucBuff[2] = DEV_TYPE_BMS;
    aucBuff[3] = s_tMyBms.ucAddress;
    aucBuff[4] = func;                  // 功能码
    aucBuff[5] = 0x00;                  // 返回码
    aucBuff[6] = 0x00;                  // 保留字节

    sendCanFrm(addr, aucBuff);
    return SUCCESSFUL;
}

/**
 * @brief 发送升级标志指令帧
 * @param[in] addr 访问的从机地址
 * @param[in] func 功能码
 * @param[in] flag 升级标志（1:置位,0:清除）
 * @retval SUCCESSFUL 成功, FAILURE 失败
*/
Static SHORT SendSetUpdateFlagFrm(BYTE addr, BYTE func, BOOLEAN flag)
{
    BYTE aucBuff[7] = {0};

    aucBuff[0] = ID_SET_UPDATE_FLAG;
    aucBuff[1] = 0xE1;                  // 升级协议标志
    aucBuff[2] = DEV_TYPE_BMS;
    aucBuff[3] = s_tMyBms.ucAddress;
    aucBuff[4] = func;                  // 功能码
    aucBuff[5] = 0x00;                  // 返回码
    aucBuff[6] = flag;                  // 升级标志

    sendCanFrm(addr, aucBuff);
    return SUCCESSFUL;
}

/**
 * @brief 升级超时判断及处理
*/
Static BOOLEAN JudgeAndHandleUpdateTimeOut(void)
{
    if(GetParallUpdateFlag())
    {
        TimerPlus(s_BMSUpdateState.updateTimeCount, BMS_UPDATE_TIME_OUT_CNT);
        if (s_BMSUpdateState.updateTimeCount >= BMS_UPDATE_TIME_OUT_CNT)
        {
            SetParallUpdateFlag(False);
            DealParalleUpdateTimeout();
            return True;
        }
    }
    return False;
}

Static BOOLEAN JudgeBMSUpdatePollTimeOut(void)
{
    TimerPlus(s_BMSUpdateState.pollTimeCount, BMS_UPDATE_POLL_TIME_OUT_CNT);
    if (s_BMSUpdateState.pollTimeCount >= BMS_UPDATE_POLL_TIME_OUT_CNT)
    {
        s_BMSUpdateState.bPollTimeOut = True;
        return True;
    }
    return False;
}

Static BOOLEAN JudgeBMSUpdatePollIntervalArrive(void)
{
    TimerPlus(s_BMSUpdateState.intervalCount, BMS_UPDATE_POLL_INTERVAL_CNT);
    if (s_BMSUpdateState.intervalCount >= BMS_UPDATE_POLL_INTERVAL_CNT)
    {
        s_BMSUpdateState.intervalCount = 0;
        return True;
    }
    return False;
}

/**
 * @brief 升级前BMS主机轮询从机
*/
Static SHORT BMSUpdatePollSlaveDev(void)
{
    BOOLEAN isPollTimeOut = False;
    BOOLEAN isIntervalArrive = False;

    RETURN_VAL_IF_FAIL(True == s_BMSUpdateState.bPolling, FAILURE);  // 未进入升级轮询不处理

    // 轮询结束时间到判断
    isPollTimeOut = JudgeBMSUpdatePollTimeOut();
    if (True == isPollTimeOut)
    {
        s_BMSUpdateState.bPolling = False;   // 超时停止轮询
        SetAgentUpdateFlag(True); 
        return SUCCESSFUL;
    }

    // 本机地址跳过扫描
    if ((s_BMSUpdateState.currentPollIndex + 1) == s_tMyBms.ucAddress)
    {
        s_BMSUpdateState.currentPollIndex++;
    }

    // 开始下一轮扫描判断
    if (s_BMSUpdateState.currentPollIndex >= SLAVE_NUM)
    {
        s_BMSUpdateState.currentPollIndex = 0;
    }

    // 发送间隔判断
    isIntervalArrive = JudgeBMSUpdatePollIntervalArrive();
    RETURN_VAL_IF_FAIL((True == isIntervalArrive) || (s_BMSUpdateState.pollTimeCount <= 1), FAILURE);

    // 轮询发包
    if (False == s_BMSUpdateState.battInPlaceFlag[s_BMSUpdateState.currentPollIndex])
    {   // 设备统计
        if (s_BMSUpdateState.slavePollDevCount >= BMS_UPDATE_POLL_SLAVE_CNT)
        {
            // 单台轮询多次无回复则跳过该地址
            s_BMSUpdateState.currentPollIndex++;
            s_BMSUpdateState.slavePollDevCount = 0;
            return FAILURE;
        }
        SendScanDevFrm(s_BMSUpdateState.currentPollIndex + 1, FUNC_BMS_UPDATE_POLL_MASTER_FRM);
        s_BMSUpdateState.slavePollDevCount++;
    }
    else if (False == s_BMSUpdateState.stateSetUpdateFlag[s_BMSUpdateState.currentPollIndex])
    {    // 统计到设备，设置升级标志
        if (s_BMSUpdateState.slavePollFlagCount >= BMS_UPDATE_POLL_SLAVE_CNT)
        {
            s_BMSUpdateState.currentPollIndex++;
            s_BMSUpdateState.slavePollDevCount = 0;
            s_BMSUpdateState.slavePollFlagCount = 0;
            return FAILURE;
        }
        SendSetUpdateFlagFrm(s_BMSUpdateState.currentPollIndex + 1, FUNC_BMS_UPDATE_POLL_MASTER_FRM, True);
        s_BMSUpdateState.slavePollFlagCount++;
    }
    else
    {   // 统计到设备且设置升级标志成功，发下一台
        s_BMSUpdateState.currentPollIndex++;
        s_BMSUpdateState.slavePollDevCount = 0;
        s_BMSUpdateState.slavePollFlagCount = 0;
    }

    return SUCCESSFUL;
}

/**
 * @brief BMS并机通信点对点传递升级帧设置协议类型
*/
Static SHORT Proc_Prtcl_Type_BMS_Update(T_CAN_Header tHeader)
{
    BYTE idMatchBMS[] = {ID_SCAN_SLAVE_DEV, ID_SET_UPDATE_FLAG};
    BYTE i = 0;
    RETURN_VAL_IF_FAIL((tHeader.SrcAdr <= SLAVE_NUM) && (tHeader.SrcAdr > 0), FAILURE);
    for (i = 0; i < sizeof(idMatchBMS); i++)
    {
        if (idMatchBMS[i] == s_tCanDataCache_BMS[tHeader.SrcAdr - 1].aucDataBuf[0])
        {
            s_tCommCan.ucPrtclType = PROTOCOL_BMS;
            break;
        }
    }
    return SUCCESSFUL;
}

/**
 * @brief 重置BMS升级从机检测状态
*/
SHORT ResetBMSUpdateScanState(void)
{
    rt_memset_s(&s_BMSUpdateState, sizeof(T_CAN_BMSUpdateScanState), 0x00, sizeof(T_CAN_BMSUpdateScanState));
    return SUCCESSFUL;
}

/**
 * @brief 开启BMS升级从机检测
*/
SHORT StartBMSUpdateScan(void)
{
    s_BMSUpdateState.bPolling = True;
    return SUCCESSFUL;
}

/**
 * @brief 获取BMS升级从机检测状态
*/
T_CAN_BMSUpdateScanResult GetBMSUpdateScanState(void)
{
    T_CAN_BMSUpdateScanResult result = {0};
    BYTE count = 0;
    BYTE i = 0;

    for (i = 0; i < SLAVE_NUM; i++)
    {
        if (True == s_BMSUpdateState.battInPlaceFlag[i])
        {
            result.battInPlaceAddr[count++] = i + 1;
        }
    }
    result.numInPlace = count;

    return result;
}

Static SHORT DealBMSFrame( T_Can_Frame * ptCan_Frame)
{
    static BYTE s_ucCnt = 5;
    BOOLEAN retMatch = 0;
//    if ( False == CheckMaster(ptCan_Frame) )
//    {
//        return -1;
//    }
    retMatch = DealBMSFrameMatchUpdate(ptCan_Frame);
    RETURN_VAL_IF_FAIL(False == retMatch, 1);  // 匹配上指令了直接返回1，没匹配上继续往下匹配


    switch (s_tCanDataCache_BMS[ptCan_Frame->tHeader.SrcAdr-1].aucDataBuf[0])//ptCan_Frame->tDataFrame.aucData[0])
    {
        case ID_SET_PARA:
            ParseParaFrm(ptCan_Frame->tDataFrame.aucData);
            break;
        case ID_GET_REALDATA:
            ParseRealdataFrm( ptCan_Frame );
            break;
        case ID_GET_REALDATA3:
            ParseRealdataFrm3(ptCan_Frame);
            break;
        case ID_SET_WORKMODE1:
            if ( s_ucCnt > 0 )
            {
                if ( --s_ucCnt > 0 ) break;
            }
        case ID_SET_WORKMODE:
            SetWorkMode(&s_ucCnt);
            ParseWorkmodeFrm(ptCan_Frame->tDataFrame.aucData);
            break;
        case ID_SET_CHRGCURR:
            ParseChargeCurrFrm(ptCan_Frame->tDataFrame.aucData);
            break;
//        case FN_ADDR_COMPETE:
//            ParseCompeteFrm(ptCan_Frame->tDataFrame.aucData);
//            break;
        case ID_GET_HIGHSOC:
            ParseHighSOCFrm(ptCan_Frame);
			break;
        case ID_SET_CONSRV:
            ParseConsrvFrm(ptCan_Frame);
            break;
        // case ID_SET_EQUAL_CURR:
        //     ParseEqualizeCurrFrm(ptCan_Frame);
        //     break;
        // case ID_SET_ROTATE_CTRL:
        //     ParseRotateFrm(ptCan_Frame->tDataFrame.aucData);
        //     break;
        // case ID_SET_DISCHRGVOL:
        //     ParseDisChgVolFrm(ptCan_Frame->tDataFrame.aucData);
        //     break;
        // case ID_GET_BATT_CAPACITY:
        //     ParseBattCapacityFrm(ptCan_Frame);
        //     break;
        // case ID_SET_POWERDOWN_VOLT:
        //     ParesePowerdownVoltFrame(ptCan_Frame->tDataFrame.aucData);
        //     break;
        // case ID_SET_CONN_TEMP_HIGH:
        //     ParseConnTempHighFrame(ptCan_Frame->tDataFrame.aucData);
        //     break;
        // case ID_SET_SYN_TIME_MODE:
        //     ParseSynTimeModeFrm(ptCan_Frame);
        //     break;
        // case ID_SET_EXTERAL_POWER_STATUS:
        //     ParseExteralPowerFram(ptCan_Frame->tDataFrame.aucData);
        //     break;
        #ifdef ALARM_UPLOAD_ACTIVE
        case ID_CAN1_ALARM_OCCUR_UPLOAD:
        case ID_CAN1_ALARM_MISS_UPLOAD:
            ParseAlarmFrmFromSlave(ptCan_Frame);
            break;
        #endif
        default:
            if(DealBMSFrame2(ptCan_Frame) == -1)
            {
                  return -1;
            }
            break;
    }

    return 1;
}

/* 备份数据接收主函数 */
void processBackupData(void* parameter)
{
    pre_thread_beat_f(THREAD_BACKUP);
#ifdef UNITEST
    while (IsRunning())
#else
    while(1)
#endif
    {
        rt_thread_delay(10);
        thread_beat_go_on(THREAD_BACKUP);

        if (!IsParallUpdate()){
            DealBackupProcess();
        }
        DealUpdateProcess();
    }
}

/****************************************************************************
* 函数名称：DealBackupProcess
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：获取CAN缓存的互备份数据并处理
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
void DealBackupProcess()
{
    static BYTE ucIndex = 0; //从缓存中读取的索引

    if (ucIndex >= CAN_BACKUP_NUM_MAX)
    {
        ucIndex = 0;
    }

    rt_memset(s_aucBackupBuffRecv, 0, sizeof(s_aucBackupBuffRecv));
    if (Get_CanBuff_Backup(s_aucBackupBuffRecv, ucIndex))
    {
        DealBMSBackUpData(s_aucBackupBuffRecv);
        ucIndex++;
        return;
    }

    return;
}

/****************************************************************************
* 函数名称：Get_CanBuff_Backup
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：获取CAN缓存的互备份数据
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
BOOLEAN Get_CanBuff_Backup(void *pCanRecvBuff, BYTE ucIndex)
{
    UINT32 uBuffLen = 0;

    uBuffLen = rt_strnlen_s((char*)s_tCanBuffer[ucIndex].ucData, sizeof(s_tCanBuffer[ucIndex].ucData));
    if ((s_tCanBuffer[ucIndex].wDataFlag == 1) && (uBuffLen > 0))
    {
        rt_mutex_take(s_ptMutexCan1, RT_WAITING_FOREVER);
        rt_memcpy(pCanRecvBuff, s_tCanBuffer[ucIndex].ucData, s_tCanBuffer[ucIndex].wDataLen);
        rt_memset(s_tCanBuffer[ucIndex].ucData, 0, s_tCanBuffer[ucIndex].wDataLen);
        s_tCanBuffer[ucIndex].wDataFlag = 0;
        rt_mutex_release(s_ptMutexCan1);
        return True;
    }
    else
    {
        return False;
    }
}

/****************************************************************************
* 函数名称：Set_CanBuff_Backup
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：CAN保存互备份数据到缓存中
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
void Set_CanBuff_Backup(void *pCanSendBuff, SHORT wBuffLen)
{
    static BYTE ucIndex = 0;

    if (s_tCanBuffer[ucIndex].wDataFlag == 0 && (wBuffLen <= CAN_BACKUP_DATA_BUFF))
    {
        rt_mutex_take(s_ptMutexCan1, RT_WAITING_FOREVER);
        rt_memcpy(s_tCanBuffer[ucIndex].ucData, pCanSendBuff, wBuffLen);
        s_tCanBuffer[ucIndex].wDataLen = wBuffLen;
        s_tCanBuffer[ucIndex].wDataFlag = 1;
        ucIndex++;
        rt_mutex_release(s_ptMutexCan1);
    }

    if (ucIndex >= CAN_BACKUP_NUM_MAX)
    {
        ucIndex = 0;
    }
}

Static BOOLEAN DealBMSDLFrame(T_CommStruct* ptCommCan, T_Can_Frame* ptCan_Frame) {
    BYTE ucRsvTrans = 0;
#ifdef USE_CAN2
    BYTE ucCan2SCAdr = GetCan2ScAdr();
#endif

    RETURN_VAL_IF_FAIL(ptCommCan != NULL, False);
    RETURN_VAL_IF_FAIL(ptCommCan->wRecLength >= DOWNLOAD_APP_MIN_LEN, False);

    rt_memset_s(ptCommCan->aucRecBuf, sizeof(ptCommCan->aucRecBuf), 0, sizeof(ptCommCan->aucRecBuf));

    rt_memcpy(ptCommCan->aucRecBuf, s_tCanDataCache_BMS[ptCan_Frame->tHeader.SrcAdr-1].aucDataBuf, ptCommCan->wRecLength);
    ucRsvTrans = ptCommCan->aucRecBuf[DOWNLOAD_RSV_TRANS_INDEX];

    if (IsMaster() && ucRsvTrans == DOWNLOAD_TRANS) {
            if(IsCommTrans()){
                SendCommTransFrm(ptCommCan);
            }
#ifdef USE_CAN2
            else{
                ChangeIntoCan2Frm(ptCommCan->aucRecBuf, ptCommCan->wRecLength, DEV_TYPE_SC, ucCan2SCAdr);
            }
#endif
       ptCommCan->wSendLength = 0;
    }
    else {
        SetPrtclDLLinkType(DL_LINK_TYPE_CAN1);
        DealCanDLData(ptCommCan);
    }
    return True;
}

Static SHORT Proc_Frame_BMS(T_CommStruct* ptCommCan, T_Can_Frame* ptCan_Frame)
{
    DealDeviceUnlock(AUTO_UNLOCK);
    switch (ptCommCan->ucPrtclType)
    {
        case PROTOCOL_BMS_BACKUP:
            if (!IsParallUpdate()){
                Set_CanBuff_Backup(s_tCanDataCache_BMS[ptCan_Frame->tHeader.SrcAdr-1].aucDataBuf, ptCommCan->wRecLength);
            }
            break;
        case PROTOCOL_BMS:
            DealBMSFrame(ptCan_Frame);
            break;
        case PROTOCOL_DL:
            DealBMSDLFrame(ptCommCan, ptCan_Frame);
            break;
        default:
            return 0;
    }

    return 1;
}

Static SHORT Proc_North_Prtcl_Type(void) {
    switch (s_tCanDataCache.aucDataBuf[0])
    {
        case FN_ADDR_COMPETE:
            ParseCompeteFrm(s_tCanDataCache.aucDataBuf);
            return 0;
        case SOI:
            s_tCommCan.ucPrtclType = PROTOCOL_1363;
            break;
        case 0xE1:
            s_tCommCan.ucPrtclType = PROTOCOL_DL;
            break;
        default:
            s_tCommCan.ucPrtclType = PROTOCOL_MODBUS;
    }
    return 1;
}

Static void Proc_Prtcl_Type_BMS_Backup(T_CAN_Header tHeader)
{
    switch (s_tCanDataCache_BMS[tHeader.SrcAdr-1].aucDataBuf[0])
    {
        case ID_BACKEUP_HISDATA:
        case ID_BACKEUP_HISALARM:
        case ID_BACKEUP_HISACT:
        case ID_BACKEUP_BDURECORD:
        case ID_BACKEUP_HISEXTREME_PACK1:
        case ID_BACKEUP_HISEXTREME_PACK2:
        case ID_BACKEUP_ANALYSE:
            s_tCommCan.ucPrtclType = PROTOCOL_BMS_BACKUP;
            break;
        default:
            return;
    }
    return;
}

Static void Proc_Prtcl_Type_BMS_SNMP(T_CAN_Header tHeader)
{
    switch (s_tCanDataCache_BMS[tHeader.SrcAdr-1].aucDataBuf[0])
    {
        case ID_SNMP_REALDATA:
        case ID_SNMP_REALALARM:
        case ID_SNMP_SYNCPARA:
        case ID_SNMP_SYNCPARA_STR:
        case ID_SNMP_FACTINFO:
        case ID_SNMP_CTRL:
            s_tCommCan.ucPrtclType = PROTOCOL_BMS;
            break;
        default:
            return;
    }
    return;
}

BOOLEAN boardCaseSwitch(BYTE byte){
    switch (byte){
        case ID_SET_WORKMODE1:
            s_tCommCan.ucPrtclType = PROTOCOL_BMS;
            break;
        case ID_SET_WORKMODE:
            s_tCommCan.ucPrtclType = PROTOCOL_BMS;
            break;
        case ID_SET_CHRGCURR:
            s_tCommCan.ucPrtclType = PROTOCOL_BMS;
            break;
        case ID_SET_PARA:
            s_tCommCan.ucPrtclType = PROTOCOL_BMS;
            break;
        case ID_GET_REALDATA:
            s_tCommCan.ucPrtclType = PROTOCOL_BMS;
            break;
        case ID_GET_REALDATA3:
            s_tCommCan.ucPrtclType = PROTOCOL_BMS;
            break;
        case ID_GET_HIGHSOC:
            s_tCommCan.ucPrtclType = PROTOCOL_BMS;
            break;
        case ID_SET_EQUAL_CURR:
            s_tCommCan.ucPrtclType = PROTOCOL_BMS;
            break;
        case ID_SET_ROTATE_CTRL:
            s_tCommCan.ucPrtclType = PROTOCOL_BMS;
            break;
        case ID_SET_DISCHRGVOL:
            s_tCommCan.ucPrtclType = PROTOCOL_BMS;
            break;
        default:
            return False;
    }
    return True;
}

Static void Proc_Prtcl_Type_BMS_boardcast(T_CAN_Header tHeader) {
    switch (s_tCanDataCache_BMS[tHeader.SrcAdr-1].aucDataBuf[0])
    {
        case ID_GET_BATT_CAPACITY:
            s_tCommCan.ucPrtclType = PROTOCOL_BMS;
            break;
        case ID_SET_POWERDOWN_VOLT:
            s_tCommCan.ucPrtclType = PROTOCOL_BMS;
            break;
        case ID_SET_SYN_TIME_MODE:
            s_tCommCan.ucPrtclType = PROTOCOL_BMS;
            break;
        case ID_SET_CONN_TEMP_HIGH:
            s_tCommCan.ucPrtclType = PROTOCOL_BMS;
            break;
        case ID_SET_EXTERAL_POWER_STATUS:
            s_tCommCan.ucPrtclType = PROTOCOL_BMS;
            break;
#ifdef DEVICE_USING_R321
        case ID_SET_FM_CTRL:
            s_tCommCan.ucPrtclType = PROTOCOL_BMS;
            break;
        case ID_GET_SOC_POWER:
            s_tCommCan.ucPrtclType = PROTOCOL_BMS;
            break;
        case FN_FORCE_MASTER:
            s_tCommCan.ucPrtclType = PROTOCOL_BMS;
            break;
        case ID_CAN1_FIRE_LOCK:
            // 闭锁控制
            BduCtrl(SCI_CTRL_BDULOCK, 1);
            SaveAction(GetActionId(CONTOL_CAN1_FIRE_LOCK), "Can1FireAlm Lock");
            break;
#endif
        case DownloadType:
            s_tCommCan.ucPrtclType = PROTOCOL_DL;
            break;
        default:
            if(boardCaseSwitch(s_tCanDataCache_BMS[tHeader.SrcAdr-1].aucDataBuf[0])){
                return;
            }
            s_tCommCan.ucPrtclType = PROTOCOL_MODBUS;
    }
    return;
}
Static BOOLEAN Proc_Prtcl_Type_BMS_0x00(T_CAN_Header tHeader)
{
    switch (s_tCanDataCache_BMS[tHeader.SrcAdr-1].aucDataBuf[0])
    {
        case ID_SNMP_REALDATA:
        case ID_SNMP_REALALARM:
        case ID_SNMP_SYNCPARA:
        case ID_SNMP_SYNCPARA_STR:
        case ID_SNMP_FACTINFO:
        case ID_SNMP_CTRL:
            s_tCommCan.ucPrtclType = PROTOCOL_BMS;
            break;
    }
    return TRUE;
}

Static SHORT Proc_Prtcl_Type_BMS(T_CAN_Header tHeader)
{
    if(tHeader.DstAdr != 0x00)
    {
        switch (s_tCanDataCache_BMS[tHeader.SrcAdr-1].aucDataBuf[0])
        {
            case FN_ADDR_COMPETE:
                ParseCompeteFrm(s_tCanDataCache_BMS[tHeader.SrcAdr-1].aucDataBuf);
                return 0;

            case ID_GET_REALDATA:
            case ID_GET_HIGHSOC:
            case ID_GET_BATT_CAPACITY:
            case ID_SET_CONSRV:
            case ID_GET_REALDATA3:
            case ID_SET_POWERDOWN_VOLT:
            case ID_SET_ROTATE_CTRL:
            case ID_GET_SOC_POWER:
        #ifdef ALARM_UPLOAD_ACTIVE
            case ID_CAN1_ALARM_OCCUR_UPLOAD:
            case ID_CAN1_ALARM_MISS_UPLOAD:
        #endif
                s_tCommCan.ucPrtclType = PROTOCOL_BMS;
                break;
            case DownloadType:
                s_tCommCan.ucPrtclType = PROTOCOL_DL;
                break;
        }
        Proc_Prtcl_Type_BMS_0x00(tHeader);
        Proc_Prtcl_Type_BMS_Update(tHeader);
    }
    else
    {
        Proc_Prtcl_Type_BMS_boardcast(tHeader);
        Proc_Prtcl_Type_BMS_Backup(tHeader);
        Proc_Prtcl_Type_BMS_SNMP(tHeader);
    }
    return 1;
}

static void Proc_Frame(T_Can_Frame* ptCan_Frame) 
{
    s_tCommCan.ucLinkSrcDev = ptCan_Frame->tHeader.SrcDev;
    s_tCommCan.ucLinkSrcAdr = ptCan_Frame->tHeader.SrcAdr;
    if (ptCan_Frame->tHeader.SrcDev == DEV_TYPE_BMS)
        Proc_Frame_BMS(&s_tCommCan,ptCan_Frame);
    else if (ptCan_Frame->tHeader.SrcDev == DEV_TYPE_SC)
        Proc_North_Frame(&s_tCommCan);
    return;
}

static BOOLEAN IsRecLengthError(void)
{
    if (0 == s_tCommCan.wRecLength || CAN_BACKUP_DATA_BUFF < s_tCommCan.wRecLength)
    {
        return True;
    }
    return False;
}

/****************************************************************
函数名：Proc_CAN_Frame
入口参数：ptCan_Frame, DataBuff
出口参数：-1: 异常0: 没有处理完1: 结束
功能：接收处理通讯帧
****************************************************************/
static SHORT Proc_CAN_Frame( T_Can_Frame * ptCan_Frame )
{
    rt_memset( &s_tCommCan, 0, sizeof( T_CommStruct ) );    
    if (False == CheckAddress(ptCan_Frame))
    {
        return -1;
    }
    s_tCommCan.ucPortType = COMM_CAN;
    s_tCommCan.ucPrtclType = 0;
    if(ptCan_Frame->tHeader.SrcDev == DEV_TYPE_SC)
    {
        s_bCanNorthCommDisconnect = False;
        s_ulCanNorthDisconnCnt = 0;
        s_tCommCan.wRecLength = ChangeCanFrameToPkt(&s_tCommCan, ptCan_Frame);
    }
    else if(ptCan_Frame->tHeader.SrcDev == DEV_TYPE_BMS)
    {
        s_tCommCan.wRecLength = ChangeCanFrameToPkt_BMS(&s_tCommCan, ptCan_Frame);
    }

    if(IsRecLengthError() == True)
    {
        return 0;
    }

    if(ptCan_Frame->tHeader.SrcDev == DEV_TYPE_SC)
    {
        if(0 == Proc_North_Prtcl_Type()) {
            return 0;
        }
    }
    else if(ptCan_Frame->tHeader.SrcDev == DEV_TYPE_BMS)
    {
        ClearCANCommLostCnt();
#ifdef SITE_ANTI_THEFT_ENABLED
        if (ptCan_Frame->tHeader.DstAdr == 0 && ptCan_Frame->tDataFrame.aucData[0] == FN_SITE_ANTITHEFT)
        {
            ParseBindingInfoFrame(s_tCanDataCache_AntiTheft.aucDataBuf, ptCan_Frame->tHeader.SrcAdr);
            return 0;
        }
#endif
        if (0 == Proc_Prtcl_Type_BMS(ptCan_Frame->tHeader)) {
            return 0;
        }
	}

    if (s_tMyBms.ucMasterAddr == 0)        //地址竞争阶段不回复
    {
        return 0;
    }

    Proc_Frame(ptCan_Frame);

    if (s_tCommCan.wSendLength > 0 && ptCan_Frame->tHeader.DstAdr == s_tMyBms.ucAddress )
    {
        ChangeIntoCanFrm(s_tCommCan.aucSendBuf, s_tCommCan.wSendLength, ptCan_Frame->tHeader.SrcDev, ptCan_Frame->tHeader.SrcAdr);
    }
    s_tCommCan.wSendLength = 0;

    return 0;
}


static BOOLEAN modifyBmsAddr(BYTE ucAddr)
{

    s_tMyBms.wCnt = 0;
    s_tMyBms.ucMasterAddr = 0;

    if (s_tMyBms.ucAddress == ucAddr)
    {
        return FALSE;
    }
    if ((ucAddr<1) || (s_bIsBigBattStage == True && ucAddr>10) || ucAddr>SLAVE_NUM)
    {
        s_tMyBms.ucAddress = 1;
    }
    else
    {
        s_tMyBms.ucAddress = ucAddr;
    }

    return True;
}

BOOLEAN InitSuData( void )
{
    rt_memset( &s_tRecBuff,      0, sizeof( s_tRecBuff ) );
    rt_memset( &s_tSlaveTCB,     0, sizeof( s_tSlaveTCB ) );

    s_tMyBms.wCnt = 0;
    s_tMyBms.ucMasterAddr = 0;
    getGrpAddr();
    s_tMyBms.ucAddress = getResetData(BMS_ADDR);
    if ( (s_tMyBms.ucAddress<1) || (s_tMyBms.ucAddress>SLAVE_NUM) )
    {
        s_tMyBms.ucAddress = 1;
        s_ucAddrCompFLAG = 0;
    }

    return True;
}

/****************************************************************
函数名：InitCanProto
入口参数：无
出口参数：成功: true     失败: false
功能：初始化协议
****************************************************************/
BOOLEAN InitCanProto( void )
{
    rt_memset( &s_tRecBuff, 0, sizeof( s_tRecBuff ) );
    return True;
}

Static void sendCanFrm(BYTE ucDstAdr, BYTE* pucBuff)
{
    T_CAN_Header tHeader;

    tHeader.DstAdr = ucDstAdr;
    tHeader.DstDev = DEV_TYPE_BMS;
    tHeader.SrcAdr = s_tMyBms.ucAddress;
    tHeader.SrcDev = DEV_TYPE_BMS;
    tHeader.IDE = 1;
    tHeader.RTR = 0;
    tHeader.len = 8;
    tHeader.Level = 1;

    CanDrive_Write(pucBuff, 7, (BYTE *)&tHeader);
}

BYTE getGrpAddr(void)
{
#ifdef DEVICE_USING_D121
    s_tMyBms.ucGroup = 0;
#else
    static T_SysPara s_tGrpSysPara;
    rt_memset(&s_tGrpSysPara, 0, sizeof(T_SysPara));
    GetSysPara(&s_tGrpSysPara);
    
    if ( True == s_tGrpSysPara.bMajorBatt || 0 == s_tGrpSysPara.ucRunMode )   //非受控时不分主备
    {
        s_tMyBms.ucGroup = 0;
    }
    else
    {
        s_tMyBms.ucGroup = 0x40;
    }
#endif
    return s_tMyBms.ucGroup;
}

void SetEqualizeCurr(FLOAT fCurr, WORD wBattCap, BYTE bMasterFull)
{
    BYTE aucBuff[7];
    WORD wCurr = Float2Word(fCurr)*100;

    if (IsParallUpdate()) {
        return;
    }

    if (!IsMaster())
    {
        return;
    }
    rt_memset_s(&aucBuff, sizeof(aucBuff), 0, 7);

    aucBuff[0] = ID_SET_EQUAL_CURR;
    aucBuff[1] = wCurr & 0xFF;
    aucBuff[2] = (wCurr>>8) & 0xFF;
    aucBuff[3] = wBattCap & 0xFF;
    aucBuff[4] = (wBattCap>>8) & 0xFF;
    aucBuff[5] = bMasterFull;

    sendCanFrm(s_tMyBms.ucGroup, aucBuff);

    return;
}

void setBMSMode(BYTE ucMode, SHORT iVol, WORD wRef)
{
    BYTE aucBuff[7];

    if (!IsMaster())
    {
        return;
    }
    rt_memset_s(&aucBuff, sizeof(aucBuff), 0, 7);

    aucBuff[0] = ID_SET_WORKMODE;
    aucBuff[1] = ucMode;
    aucBuff[2] = iVol & 0xFF;
    aucBuff[3] = (iVol>>8) & 0xFF;
    aucBuff[4] = wRef & 0xFF;
    aucBuff[5] = (wRef>>8) & 0xFF;

    sendCanFrm(s_tMyBms.ucGroup, aucBuff);

    if (ucMode)
    {
        iVol += (SHORT)(s_tBCMData.fBattVolt * g_ProConfig.fVoltTrasRate *100);
    }
    aucBuff[0] = ID_SET_WORKMODE1;
    aucBuff[1] = ucMode;
    aucBuff[2] = iVol & 0xFF;
    aucBuff[3] = (iVol>>8) & 0xFF;
    aucBuff[4] = wRef & 0xFF;
    aucBuff[5] = (wRef>>8) & 0xFF;

    sendCanFrm(s_tMyBms.ucGroup, aucBuff);
    return;
}

//调频充放电，主机下发给从机
void setFmCtrl(BOOLEAN bChgDisable, BOOLEAN bChgDischg, FLOAT fAvePower)
{
    BOOLEAN bFmModeTemp = False;
    BYTE aucBuff[7] = {0};
    SHORT sPower = 0;

    if (!IsMaster())
    {
        return;
    }

    bFmModeTemp = IsFmMode();
    sPower = (SHORT)(fAvePower);
    aucBuff[0] = ID_SET_FM_CTRL;
    aucBuff[1] = 0x01 & bChgDisable;
    if(bChgDischg)
    {
        aucBuff[1] |= 0x02;
    }
    aucBuff[2] =  sPower & 0xFF;
    aucBuff[3] = (sPower >> 8) & 0xFF;
    aucBuff[4] = bFmModeTemp & 0x01;

    sendCanFrm(s_tMyBms.ucGroup, aucBuff);
    return;
}

//解析调频指令
static void ParseFmCtrl(T_Can_Frame * ptCan_Frame)
{
    BYTE *p;
    FLOAT fPower = 0.0f;
    BOOLEAN bStatus = False;

    p = (BYTE *)&ptCan_Frame->tDataFrame.aucData[0];

    if (IsMaster())
    {
        return;
    }
    bStatus = p[1] & 0x01;
    if(bStatus)
    {
        SetFmChgDisable(True);
    }
    else
    {
        SetFmChgDisable(False);
    }
    bStatus = (p[1] >> 1) & 0x01;
    if(bStatus)
    {
        SetACStatus(bStatus); //错峰放电时才根据该命令来同步状态
    }

    fPower = (FLOAT)((SHORT)((p[3] << 8) | p[2]));
    SetFmMode((BOOLEAN)(p[4] & 0x01));
    SetFmMaxCurr(fPower);

    return;
}

//获取从机的SOC和最大充电功率

static void GetBattChgPower(void)
{
    BYTE aucBuff[7] = {0};

    aucBuff[0] = ID_GET_SOC_POWER;
    sendCanFrm(s_tMyBms.ucGroup, aucBuff);
}


//解析获取从机SOC和最大功率

static void ParseMAXChgPowerFrm(T_Can_Frame * ptCan_Frame)
{
    BYTE ucSn, *p;
    p = (BYTE *)&ptCan_Frame->tDataFrame.aucData[0];

    if(IsMaster())
    {
        ucSn = ptCan_Frame->tHeader.SrcAdr - 1;
        s_atSlavesRealData[ucSn].fFMcurrentSingleMaxChgPower = (FLOAT)(p[1] + p[2]*256)/1000.00;
    }
    else
    {
        SendBattChgPower();
    }
}


//发送从机的SOC和最大充电功率

static void SendBattChgPower(void)
{
    BYTE aucBuff[7] = {0};
    WORD wChgPower = 0;
    
    GetFmRealData(&s_tFmRealData);
    wChgPower = (WORD)(s_tFmRealData.fFMcurrentSingleMaxChgPower*1000 + 0.5);

    aucBuff[0] = ID_GET_SOC_POWER;
    aucBuff[1] = wChgPower & 0xFF;
    aucBuff[2] = (wChgPower>>8) & 0xFF;

    sendCanFrm(s_tMyBms.ucMasterAddr, aucBuff);
}


// 从机同步主机系统时间以及放电模式参数
void SynTimeAndMode(void)
{
    BYTE aucBuff[7];
    struct tm tBaseTime;
    time_t tSynSecond, tTimeNow;
    //只有主机自身能发送同步帧
    if (!IsMaster())
    {
       return;
    }

    tTimeNow = time(RT_NULL);
    rt_memset(&aucBuff, 0, 7);
    rt_memset(&tBaseTime, 0x00, sizeof(tBaseTime));

    GetBaseTime(&tBaseTime);
    GetSysPara(&s_tSysPara);

    tSynSecond = tTimeNow - mktime(&tBaseTime);
    //日期参数,传输相对时间
    aucBuff[0] = ID_SET_SYN_TIME_MODE;
    aucBuff[1] = (tSynSecond & 0xFFFFFFFF) & 0xFF;
    aucBuff[2] = ((tSynSecond & 0xFFFFFFFF) >> 8) & 0xFF;
    aucBuff[3] = ((tSynSecond & 0xFFFFFFFF) >> 16) & 0xFF;
    aucBuff[4] = ((tSynSecond & 0xFFFFFFFF) >> 24) & 0xFF;
    //放电场景同步
    aucBuff[5] = s_tSysPara.ucUsageScen;

    sendCanFrm(s_tMyBms.ucGroup, aucBuff);

    return;
}

void setChargeCurr(WORD wCurr, WORD wMaxVol, SHORT sMaxCurr)
{
    BYTE aucBuff[7];//, i=0;

    if (IsParallUpdate())
    {
        return;
    }

    if (!IsMaster())
    {
        return;
    }

    rt_memset_s(&aucBuff, sizeof(aucBuff), 0, 7);

    aucBuff[0] = ID_SET_CHRGCURR;
    aucBuff[1] = wCurr & 0xFF;
    aucBuff[2] = (wCurr>>8) & 0xFF;

    aucBuff[3] = wMaxVol & 0xFF;
    aucBuff[4] = (wMaxVol>>8) & 0xFF;

    aucBuff[5] = sMaxCurr & 0xFF;
    aucBuff[6] = (sMaxCurr>>8) & 0xFF;

    sendCanFrm(s_tMyBms.ucGroup, aucBuff);
    return;
}

void SetRotateCtrlSlave(BYTE ucCurr, BOOLEAN bChargeDisable, BYTE ucAddr)
{
    BYTE aucBuff[7];

    if (!IsMaster())
    {
        return;
    }

    rt_memset_s(&aucBuff, sizeof(aucBuff),0, 7);

    aucBuff[0] = ID_SET_ROTATE_CTRL;
    aucBuff[1] = ucCurr;
    aucBuff[2] |= 0x01 & bChargeDisable;

    if(ucAddr == 0)
    {
        sendCanFrm(s_tMyBms.ucGroup, aucBuff);
    }
    else
    {
        sendCanFrm(ucAddr, aucBuff);
    }
    return;
}

#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
void SetExternalPowerOnExistFlag(BOOLEAN bExternalPoweronFlag, BOOLEAN bB3CurrBalance)
{
    BYTE aucBuff[7];

    if (IsParallUpdate())
    {
        return;
    }

    if (!IsMaster())
    {
        return;
    }

    rt_memset_s(&aucBuff, sizeof(aucBuff),0, 7);

    aucBuff[0] = ID_SET_EXTERAL_POWER_STATUS;
    if(bExternalPoweronFlag)
    {
        aucBuff[1] |= 0x01;
    }

    if(bB3CurrBalance)
    {
        aucBuff[1] |= 0x02;
    }

    sendCanFrm(s_tMyBms.ucGroup, aucBuff);

    return;
}
#else
void SetExternalPowerOnExistFlag(BOOLEAN ExternalPoweronFlag)
{
    BYTE aucBuff[7];

    if (IsParallUpdate())
    {
        return;
    }

    if (!IsMaster())
    {
        return;
    }

    rt_memset_s(&aucBuff, sizeof(aucBuff), 0x00, sizeof(aucBuff));

    aucBuff[0] = ID_SET_EXTERAL_POWER_STATUS;
    aucBuff[1] = 0x01 & ExternalPoweronFlag;

    sendCanFrm(s_tMyBms.ucGroup, aucBuff);

    return;
}
#endif

BOOLEAN broadSendFireAlm(BOOLEAN flag)
{
    BYTE aucBuff[7] = {0};
    int i = 0;
    if(flag != True && flag != False){
        return False;
    }
    if(flag == True){
        aucBuff[0] = ID_CAN1_FIRE_LOCK;
    }
    for(;i<10;i++){
        sendCanFrm(0, aucBuff);
        rt_thread_delay(30);
    }
    return True;
}

void ctrlBMSConsrv(BYTE ucAddr, BOOLEAN bConsrv)
{
    BYTE aucBuff[7];

    if (!IsMaster())
    {
        return;
    }

    rt_memset_s(&aucBuff, sizeof(aucBuff), 0, 7);

    aucBuff[0] = ID_SET_CONSRV;
    aucBuff[1] = bConsrv;
    sendCanFrm(ucAddr, aucBuff);

    return;
}

void SetPowerdownVoltSlave(SHORT iVol)
{
    if (IsParallUpdate())
    {
        return;
    }

    if(!IsMaster())
    {
        return;
    }
    BYTE aucBuff[7];
    rt_memset_s(&aucBuff, sizeof(aucBuff), 0, 7);

    getGrpAddr();

    aucBuff[0] = ID_SET_POWERDOWN_VOLT;
    aucBuff[1] = (iVol>>8) & 0xFF;
    aucBuff[2] = iVol & 0xFF;

    sendCanFrm(s_tMyBms.ucGroup, aucBuff);
    return;
}

Static void SendCompeteFrm(ULONG ulSn)
{
    BYTE aucBuff[7];
    rt_memset_s(&aucBuff, sizeof(aucBuff),0, 7);
    GetSysPara(&s_tSysPara);
    aucBuff[0] = FN_ADDR_COMPETE;
    aucBuff[1] = s_tMyBms.ucAddress;
#ifdef BATT_ADDR_MODE_SET_ENABLE
    aucBuff[2] = s_tSysPara.ucBattAddressMode & 0x01;
#else
    aucBuff[2] = 0;
#endif
    rt_memcpy_s(&aucBuff[3], sizeof(aucBuff), (void *)&ulSn, sizeof(ULONG));

    sendCanFrm(s_tMyBms.ucAddress, aucBuff);

    return;
}

#ifdef USE_CAN2
/**
 * @brief 检验是否CAN对接测试帧
 * @param[in] ptCan_Frame 收到的帧
 * @retval    True 是，False 否
 */
static BOOLEAN CheckIsCanInterconnectTestFrm(T_Can_Frame* ptCan_Frame, BYTE *dataCmp, UINT32 dataLen)
{
    if (NULL == ptCan_Frame || NULL == dataCmp)
    {
        return False;
    }

    if (0 == rt_memcmp(dataCmp, ptCan_Frame->tDataFrame.aucData, dataLen))
    {
        return True;
    }

    return False;
}

void CheckIsCanInterconnectTestCAN1Frm(T_Can_Frame * ptCan_Frame)
{
    if(CheckIsCanInterconnectTestFrm(ptCan_Frame, s_can_1_InterconnectTestData, sizeof(s_can_1_InterconnectTestData)))
    {
        s_can_1_InterconnetTestCounter++;
    }
    return;
}

UINT32 GetCan1InterconnetTestCounter(void)  // 获取CAN1对接测试计数
{
    return s_can_1_InterconnetTestCounter;
}

void ClearCan1InterconnetTestCounter(void)
{
    s_can_1_InterconnetTestCounter = 0;
}

/**
 * @brief 将数据打包成CAN对接测试帧发送
 * @param dataComm 
 * @param wInLen 
 * @param ucInDstDev 
 * @param ucInDstAdr
*/
static void ChangeIntoCan1InterconnectFrm(BYTE *dataComm, WORD wInLen, BYTE ucInDstDev, BYTE ucInDstAdr, BYTE canDevWrite)
{
    if (NULL == dataComm)
    {
        return;
    }
    T_CAN_Header tHead = {0};
    tHead.Level   = LNK_LEVEL;
    tHead.SrcDev  = DEV_TYPE_BMS;
    tHead.SrcAdr  = 0;                // 不写本机地址，防止触发CAN2的地址竞争
    tHead.DstDev  = ucInDstDev;
    tHead.DstAdr  = ucInDstAdr;
    tHead.IDE = 1;
    tHead.RTR = 0;
    tHead.len = CAN_FRAME_MAX_LEN;
    if (CAN_DEV == canDevWrite)
    {
        CanDrive_Write(dataComm, wInLen, (BYTE *)&tHead);
    }
}

/**
 * @brief 发送CAN对插测试帧
*/
void SendCan1InterconnectTestFrm(void)
{
    // CAN1向CAN2发送对接测试帧
    InitCanProto();  // 清空CAN接收缓冲区
    ChangeIntoCan1InterconnectFrm(s_can_1_InterconnectTestData, sizeof(s_can_1_InterconnectTestData), DEV_TYPE_BMS, 0, CAN_DEV);
}

void SendForceMasterFrm(void)
{
    BYTE aucBuff[7];

    aucBuff[0] = FN_FORCE_MASTER;
    aucBuff[1] = s_tMyBms.ucAddress;
    aucBuff[2] = GetBMSCan2Addr();

    sendCanFrm(s_tMyBms.ucGroup, aucBuff);

    return;
}
#endif

BOOLEAN IsMaster(void)
{
    return (s_tMyBms.ucMasterAddr == s_tMyBms.ucAddress)? True : False;
}

BYTE GetMasterAddr(void)
{
    return s_tMyBms.ucMasterAddr;
}

static BOOLEAN CanDrive_Write(BYTE *pIn, SHORT len, BYTE *ptHeader)
{
    BYTE *p = pIn , i = 0, j = 0;
    struct rt_can_msg canmsg = {0, };
    T_Can_Frame *ptCanfrm = (T_Can_Frame *)&canmsg;

    if (pIn == NULL || ptHeader == NULL || bIsCanClose == TRUE || s_ptMutexCan1 == RT_NULL)
    {
        return False;
    }
#ifndef UNITEST
    if (can1_dev == RT_NULL)
    {
        return False;
    }
#endif

    i = 0;
    rt_mutex_take(s_ptMutexCan1, RT_WAITING_FOREVER);
    while (len > 0)
    {
        rt_memcpy( &(ptCanfrm->tHeader), ptHeader, sizeof(T_CAN_Header) );
        ptCanfrm->tDataFrame.SegNum = i++;
        rt_memcpy(ptCanfrm->tDataFrame.aucData, p, 7);
        len -= 7;
        p += 7;
        ptCanfrm->tDataFrame.SegFlg = (len <= 0) ? 1 : 0;
        canmsg.len = 8;
        for(j = 0; j < CAN_MAX_RESEND; j++)
        {
            if(sizeof(canmsg) == rt_device_write(can1_dev, 0, &canmsg, sizeof(canmsg)))
            {
                break;
            }
        }  
    }
    rt_mutex_release(s_ptMutexCan1);
    return True;
}

/****************************************************************
函数名：ChangeIntoCanFrm
入口参数：dataComm, sInLen, addr
出口参数：无
功能：转成CAN帧
****************************************************************/
void ChangeIntoCanFrm(BYTE *dataComm, WORD wInLen, BYTE ucInScrDev, BYTE ucInSrcAdr)
{
    T_CAN_Header tHead;	

    rt_memset(&tHead, 0x00, sizeof(tHead));
    tHead.Level   = LNK_LEVEL;
    tHead.SrcDev  = DEV_TYPE_BMS;
    tHead.SrcAdr  = s_tMyBms.ucAddress;
    tHead.DstDev  = ucInScrDev;
    tHead.DstAdr  = ucInSrcAdr;
    tHead.IDE = 1;
    tHead.RTR = 0;
    tHead.len = CAN_FRAME_MAX_LEN;
    CanDrive_Write(dataComm, wInLen, (BYTE *)&tHead);
    return;
}

/****************************************************************
函数名：ChangeCanFrameToPkt
入口参数：ptCan_Frame
出口参数：无
功能：将CAN帧(单帧或者多帧)组成北向协议的数据包
****************************************************************/
static SHORT ChangeCanFrameToPkt( T_CommStruct* ptCommCan, T_Can_Frame * ptCan_Frame)
{
    BYTE *p = NULL;
    SHORT sRet = 0;
    BYTE i;

    if( ptCan_Frame->tDataFrame.SegNum == 0 )	
    {
        rt_memset(&s_tCanDataCache, 0x00, sizeof(s_tCanDataCache));
    }
    else if(ptCan_Frame->tDataFrame.SegNum != s_tCanDataCache.ucExpectFragNum)
    {
        return -1;
    }
    //只存储7个字节的数据信息
    p = (BYTE *) ( s_tCanDataCache.aucDataBuf +
        (UINT32)(s_tCanDataCache.ucExpectFragNum * sizeof( ptCan_Frame->tDataFrame.aucData )) );
    for (i=0; i<sizeof( ptCan_Frame->tDataFrame.aucData ); i++)
    {
        *p = ptCan_Frame->tDataFrame.aucData[i];
        if(SOI == s_tCanDataCache.aucDataBuf[0])   //待优化
        {
            if ( EOI == *p ) break;
        }
        p++;
    }
    if(0xE1 == s_tCanDataCache.aucDataBuf[0])
    {
        ptCommCan->ucPrtclType = PROTOCOL_DL;
    }
    else if(GetBMSAddr() == s_tCanDataCache.aucDataBuf[0])
    {
        ptCommCan->ucPrtclType = PROTOCOL_MODBUS;
    }
    s_tCanDataCache.ucExpectFragNum++;
    if( s_tCanDataCache.ucExpectFragNum >= NORTH_FRAM_MAX_NUM )
    {
        s_tCanDataCache.ucExpectFragNum = 0;
    }

    if(ptCan_Frame->tDataFrame.SegFlg == 0)
    {
        return 0;
    }
    sRet = s_tCanDataCache.ucExpectFragNum * sizeof( ptCan_Frame->tDataFrame.aucData );
    return sRet;
}

/****************************************************************
函数名：ChangeCanFrameToPkt
入口参数：ptCan_Frame
出口参数：无
功能：将CAN帧(单帧或者多帧)组成组间协议的数据包
****************************************************************/
static SHORT ChangeCanFrameToPkt_BMS(T_CommStruct *ptCommCan, T_Can_Frame *ptCan_Frame)
{
    BYTE *p = NULL;
    T_CAN_DataCache *tCachePointer = NULL;
    SHORT sRet = 0;
    BYTE i;

#ifdef SITE_ANTI_THEFT_ENABLED
    if (ptCan_Frame->tHeader.DstAdr == 0 && ptCan_Frame->tDataFrame.aucData[0] == FN_SITE_ANTITHEFT)
    {
        tCachePointer = &s_tCanDataCache_AntiTheft;
    }
    else
#endif
    {
        tCachePointer = &s_tCanDataCache_BMS[ptCan_Frame->tHeader.SrcAdr - 1];
    }

    if (ptCan_Frame->tDataFrame.SegNum == 0)
    {
        rt_memset(tCachePointer, 0x00, sizeof(T_CAN_DataCache));
    }
    else if (ptCan_Frame->tDataFrame.SegNum != tCachePointer->ucExpectFragNum)
    {
        return -1;
    }
    // 只存储7个字节的数据信息
    p = (BYTE *)(tCachePointer->aucDataBuf + (UINT32)(tCachePointer->ucExpectFragNum * sizeof(ptCan_Frame->tDataFrame.aucData)));
    for (i = 0; i < sizeof(ptCan_Frame->tDataFrame.aucData); i++)
    {
        *p = ptCan_Frame->tDataFrame.aucData[i];
        p++;
    }

    tCachePointer->ucExpectFragNum++;
    if (tCachePointer->ucExpectFragNum >= NORTH_FRAM_MAX_NUM)
    {
        tCachePointer->ucExpectFragNum = 0;
    }

    if (ptCan_Frame->tDataFrame.SegFlg == 0)
    {
        return 0;
    }

    sRet = tCachePointer->ucExpectFragNum * sizeof(ptCan_Frame->tDataFrame.aucData);
    return sRet;
}

//统计从机的数量和地址
BYTE StatisticUpdateSlaveNum(BYTE *SaveSlaveAddr)
{
    BYTE i = 0;
    BYTE ucUpdateSlaveNum = 0;

    if (SaveSlaveAddr == NULL)
    {
        return FAILURE;
    }

    for (i = 0; i< SLAVE_NUM; i++)
    {
        if (s_tSlaveTCB.aucLink[i])
        {
            SaveSlaveAddr[ucUpdateSlaveNum] = i + 1;
            ucUpdateSlaveNum++;
        }
    }

    return ucUpdateSlaveNum;
}

/*************************************************
* 德业版本：地址和普通版本映射关系：1->02H、2->12H、3->22H...10->92H 
* 新增德业测试模式参数后放开
*************************************************/
// BYTE GetBMSAddr(void)
// {
//     if (True == IsDeyeTestMode() && (0 < s_tMyBms.ucAddress))
//     {
//         return (s_tMyBms.ucAddress * 16 - 14); //德业测试版本
//     }
//     else
//     {
//         return s_tMyBms.ucAddress;
//     }
// }

BYTE GetBMSAddr(void)
{
    return s_tMyBms.ucAddress;
}

BYTE GetTotalAddr(void)
{
#ifdef USE_CAN2
    return MACHINE_NUM_PER_CABINET*(GetBMSCan2Addr()-CAN2_ADDR_START)+s_tMyBms.ucAddress;
#else
    return s_tMyBms.ucAddress;
#endif
}

// BYTE GetBMSDLAddr(void)
// {
//    return (BASE_ADDR + GetTotalAddr() -1);
// }

void SetBattAddr(BYTE ucBattAddr)
{
    RETURN_IF_FAIL(ucBattAddr != 0x00);//手动设置地址不允许为0，此时不允许切换
    s_tMyBms.ucAddress = ucBattAddr;
    //设置地址时需要发送一次地址竞争帧
    SendCompeteFrm(getMaskedSNPart());
    saveResetData(BMS_ADDR, s_tMyBms.ucAddress);
    return;
}

void restartAddrCompete(void)
{
    modifyBmsAddr(1);

    return;
}

static BOOLEAN CanDrive_Write_test(BYTE *pIn, SHORT len, BYTE ucType, BYTE ucCanType)
{/////type 1:整流器新协议，0：整流器老协议
    BYTE *p = pIn;
    struct rt_can_msg canmsg = {0, };
    canmsg.ide = 1;
    canmsg.rtr = 0;
    canmsg.len = 8;

    if (pIn == NULL)
    {
        return False;
    }
    if(ucType > 1)
    {
        return False;
    }
    while (len > (SHORT)0)
    {
        if(ucType)
        {
            canmsg.id = 0x1FA04080;
            canmsg.data[0] = 0;
            rt_memcpy(&canmsg.data[1], p, 7);
            len -= 7;
            p += 7;
            canmsg.len = 8;
            if(CAN_DEV == ucCanType)
            {
                rt_mutex_take(s_ptMutexCan1, RT_WAITING_FOREVER);
                rt_device_write(can1_dev, 0, &canmsg, sizeof(canmsg));
                rt_mutex_release(s_ptMutexCan1);
            }
        }
        else
        {
            canmsg.id = 0x1A780110;
            rt_memcpy(canmsg.data, p, 8);
            p += 8;
            len -= 8;
            canmsg.len = 8;
            if(CAN_DEV == ucCanType)
            {
                rt_mutex_take(s_ptMutexCan1, RT_WAITING_FOREVER);
                rt_device_write(can1_dev, 0, &canmsg, sizeof(canmsg));
                rt_mutex_release(s_ptMutexCan1);
            }
        }
    }
    return True;
}

void GetSmrRealData(BYTE ucDevType)
{
    BYTE aucBuff[14] = {0xE0, 0x7D, 0x01, 0x28, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x16, 0x64};
    BYTE aucBuff1[14] = {0xE0, 0x7D, 0x01, 0x20, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x3F, 0x98};
    BYTE aucBuff2[8] = {0x78, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};

    CanDrive_Write_test(aucBuff,14,1, ucDevType);
    CanDrive_Write_test(aucBuff1,14,1, ucDevType);
    CanDrive_Write_test(aucBuff2,8,0, ucDevType);
    //CanDrive_Write(aucBuff1, 14, (BYTE *)&tHeader);
    return;
}
/****************************************************************************
* 函数名称：setBackUpHisData
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：保存一条历史互备份数据
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
void setBackUpHisData(T_HisDataStruct *ptHisData)
{
    rt_uint64_t ulSN = 0;
    BYTE *p = NULL;

    RETURN_IF_FAIL(ptHisData != NULL);
    getGrpAddr();
    rt_memset(&s_aucBackupBuffSend, 0, sizeof(s_aucBackupBuffSend));
    p = s_aucBackupBuffSend;
    *p = ID_BACKEUP_HISDATA;
    p++;
    *p = sizeof(T_HisDataStruct) + 10;
    p++;
    ulSN = getSNAll();
    ulSN = uint64ValuetoModbus(ulSN);
    rt_memcpy(p, (BYTE*)&ulSN, sizeof(ulSN));     //电池序列号
    p = p+8;
    *p = BACKUP_DEV_TYPE;    //电池型号
    p++;
    *p = HISDATA_VER;    //历史数据版本号
    p++;

    rt_memcpy(p, ptHisData, sizeof(*ptHisData));

    ChangeIntoCanFrm(s_aucBackupBuffSend, s_aucBackupBuffSend[1]+2, DEV_TYPE_BMS, CAN_FRM_FIX_DSTADDR);
}
/****************************************************************************
* 函数名称：setBackUpHisAlm
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：保存一条历史互备份告警
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
void setBackUpHisAlm(T_HisAlarmSaveStruct *ptDisHisAlarm)
{
    rt_uint64_t ulSN;
    BYTE *p = NULL;
    struct tm tTime;

    RETURN_IF_FAIL(ptDisHisAlarm != NULL);

    getGrpAddr();
    rt_memset_s(&s_aucBackupBuffSend, sizeof(s_aucBackupBuffSend), 0, sizeof(s_aucBackupBuffSend));
    p = s_aucBackupBuffSend;
    *p = ID_BACKEUP_HISALARM;
    p++;
    *p = 25; //自定义字节数
    p++;
    ulSN = getSNAll();
    ulSN  = uint64ValuetoModbus(ulSN);
    rt_memcpy_s(p, sizeof(ulSN), (BYTE*)&ulSN, sizeof(ulSN));  //电池序列号
    p = p+8;
    *p = BACKUP_DEV_TYPE;    //互备份设备类型
    p++;
    *(WORD*)p = WordHost2Modbus(&(ptDisHisAlarm->wAlarmID));   //告警ID
    p = p+2;
    localtime_r(&(ptDisHisAlarm->tStartTime), &tTime);    //开始时间
    PutInt16ToBuff(p, tTime.tm_year + 1900);
    p += 2;
    *p++ = tTime.tm_mon + 1;
    *p++ = tTime.tm_mday;
    *p++ = tTime.tm_hour;
    *p++ = tTime.tm_min;
    *p++ = tTime.tm_sec;
    localtime_r(&(ptDisHisAlarm->tEndTime), &tTime);    //结束时间
    PutInt16ToBuff(p, tTime.tm_year + 1900);
    p += 2;
    *p++ = tTime.tm_mon + 1;
    *p++ = tTime.tm_mday;
    *p++ = tTime.tm_hour;
    *p++ = tTime.tm_min;
    *p++ = tTime.tm_sec;
    *p = 0;

    ChangeIntoCanFrm(s_aucBackupBuffSend, s_aucBackupBuffSend[1]+2, DEV_TYPE_BMS, s_tMyBms.ucGroup);
    return;
}

/****************************************************************************
* 函数名称：setBackUpHisAct
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：保存一条历史互备份操作记录
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
void setBackUpHisAct(T_ActionRecord *ptHisAct)
{
    rt_uint64_t ulSN;
    BYTE *p = NULL;
    struct tm tTime;

    RETURN_IF_FAIL(ptHisAct != NULL);

    getGrpAddr();
    rt_memset_s(&s_aucBackupBuffSend, sizeof(s_aucBackupBuffSend), 0, sizeof(s_aucBackupBuffSend));
    p = s_aucBackupBuffSend;
    *p = ID_BACKEUP_HISACT;
    p++;
    *p = BACKUP_HISACT_LEN;  //自定义长度（D121操作ID占两字节）
    p++;
    ulSN = getSNAll();
    ulSN  = uint64ValuetoModbus(ulSN);
    rt_memcpy_s(p, sizeof(ulSN), (BYTE*)&ulSN, sizeof(ulSN));     //电池序列号
    p = p+8;
    *p = BACKUP_DEV_TYPE;    //互备份设备类型
    p++;
#ifdef DEVICE_USING_D121
    *(WORD*)p = WordHost2Modbus(&(ptHisAct->ucActId));   //操作ID
    p = p+2;
    localtime_r(&(ptHisAct->tTime), &tTime);    //开始时间
    PutInt16ToBuff(p, tTime.tm_year + 1900);
    p += 2;
    *p++ = tTime.tm_mon + 1;
    *p++ = tTime.tm_mday;
    *p++ = tTime.tm_hour;
    *p++ = tTime.tm_min;
    *p++ = tTime.tm_sec;
#else
    *p = ptHisAct->ucActId;                              //操作ID
    p++;
    localtime_r(&(ptHisAct->tTime), &tTime);    //开始时间
    PutInt16ToBuff(p, tTime.tm_year + 1900);
    p += 2;
    *p++ = tTime.tm_mon + 1;
    *p++ = tTime.tm_mday;
    *p++ = tTime.tm_hour;
    *p++ = tTime.tm_min;
    *p++ = tTime.tm_sec;
#endif
    rt_memcpy_s(p, 20, ptHisAct->aucMsg, 20);        //操作信息
    p = p+20;
    *p = 0;

    ChangeIntoCanFrm(s_aucBackupBuffSend, s_aucBackupBuffSend[1]+2, DEV_TYPE_BMS, s_tMyBms.ucGroup);
    return;
}

/* 录波互备份保存 */
void setBduRecordBackUp(BYTE *pucData, UINT32 uSize)
{
    rt_uint64_t ulSN;
    BYTE *p = NULL;

    RETURN_IF_FAIL((pucData != NULL) && (0 < uSize) && (uSize <= (sizeof(s_aucBackupBuffSend) - 13)));  //data以外的字节数
    rt_memset_s(s_aucBackupBuffSend, sizeof(s_aucBackupBuffSend), 0, sizeof(s_aucBackupBuffSend));
    p = s_aucBackupBuffSend;
    *p = ID_BACKEUP_BDURECORD;
    p++;
    PutInt16ToBuff(p, (short)(9 + uSize));
    p+=2;
    ulSN = getSNAll();
    ulSN  = uint64ValuetoModbus(ulSN);
    rt_memcpy_s(p, sizeof(ulSN), (BYTE*)&ulSN, sizeof(ulSN));
    p += 8;
    *p = BACKUP_DEV_TYPE;
    p++;
    rt_memcpy_s(p, uSize, pucData, uSize);
    p += uSize;
    *p = 0;

    ChangeIntoCanFrm(s_aucBackupBuffSend, (3 + 9 + uSize), DEV_TYPE_BMS, s_tMyBms.ucGroup); //发送总长度1+2+8+1+uSize
    return;
}

/* 极值互备份保存 */
BOOLEAN setBackupHisExtreme(BYTE *pucData, WORD wSize, BYTE ucPackNum)
{
    rt_uint64_t ulSN;
    BYTE *p = NULL;

    RETURN_VAL_IF_FAIL((pucData != NULL) && (0 < wSize) && (wSize <= (sizeof(s_aucBackupBuffSend) - 13)), False);  //data以外的字节数
    rt_memset_s(s_aucBackupBuffSend, sizeof(s_aucBackupBuffSend), 0, sizeof(s_aucBackupBuffSend));
    p = s_aucBackupBuffSend; 
    if (ucPackNum == 1)
    {
        *p = ID_BACKEUP_HISEXTREME_PACK1;
    }
    else if (ucPackNum == 2)
    {
        *p = ID_BACKEUP_HISEXTREME_PACK2;
    }
    p++;
    PutInt16ToBuff(p, (short)(9 + wSize));
    p+=2;
    ulSN = getSNAll();
    ulSN  = uint64ValuetoModbus(ulSN);
    rt_memcpy_s(p, sizeof(ulSN), (BYTE*)&ulSN, sizeof(ulSN));
    p += 8;
    *p = BACKUP_DEV_TYPE;
    p++;
    rt_memcpy_s(p, wSize, pucData, wSize);
    p += wSize;
    *p = 0;

    ChangeIntoCanFrm(s_aucBackupBuffSend,  (3 + 9 + wSize), DEV_TYPE_BMS, s_tMyBms.ucGroup); //发送总长度1+2+8+1+wSize
    return True;
}

/* 电芯统计记录互备份保存 */
void setBackupAnalyseInfo(BYTE *pucData, UINT32 uSize)
{
    rt_uint64_t ulSN;
    BYTE *p = NULL;

    RETURN_IF_FAIL((pucData != NULL) && (0 < uSize) && (uSize <= (sizeof(s_aucBackupBuffSend) - 12)));  //12:data以外的字节数
    rt_memset_s(s_aucBackupBuffSend, sizeof(s_aucBackupBuffSend), 0, sizeof(s_aucBackupBuffSend));
    p = s_aucBackupBuffSend;
    *p = ID_BACKEUP_ANALYSE;
    p++;
    *p = (9 + uSize); // 自定义字节数
    p++;
    ulSN = getSNAll();
    ulSN  = uint64ValuetoModbus(ulSN);
    rt_memcpy_s(p, sizeof(ulSN), (BYTE*)&ulSN, sizeof(ulSN));
    p += 8;
    *p = BACKUP_DEV_TYPE;
    p++;
    rt_memcpy_s(p, uSize, pucData, uSize);
    p += uSize;
    *p = 0;

    ChangeIntoCanFrm(s_aucBackupBuffSend, (2 + 9 + uSize), DEV_TYPE_BMS, s_tMyBms.ucGroup); //发送总长度1+1+8+1+uSize
    return;
}

BYTE GetBattNum(void)
{
    BYTE ucBattNum = 1, i;
    for(i = 0; i < SLAVE_NUM; i++)
    {
        if(s_atSlavesRealData[i].bExist == True)
        {
            ucBattNum++; 
        }
    }
    return ucBattNum;
}

/***************************************************************************
 * @brief    CAN通信断判断
 * @return   True-断连 False-正常
 **************************************************************************/
BOOLEAN JudgeCanCommDisconnect(void)
{
    return s_bCanNorthCommDisconnect;
}

/***************************************************************************
 * @brief    apptest中can通讯测试
 * @return   
 **************************************************************************/
static void CheckApptestCan(T_Can_Frame* ptCan_Frame)
{
    ////apptest中测试can是否有接收
    if(GetApptestFlag())
    {
        s_slCanDealCounter++;
#ifdef USE_CAN2
        CheckIsCan2InterconnectTestFrm(ptCan_Frame);
#endif
    }

   return;
}

#ifdef USING_SNMP
BOOLEAN SetBattAddrNet(BYTE ucAddr)
{
    ucBattAddrNet = ucAddr;
    return TRUE;
}

BOOLEAN SendSnmpCtrl(BYTE DstAdr, BYTE ucCmd, INT32 iData)
{
    BYTE aucBuff[14] = {0};         //SNMP控制指令可能会广播，所以使用长帧格式

    aucBuff[0] = ID_SNMP_CTRL;
    aucBuff[1] = DstAdr;
    aucBuff[2] = ucCmd;
    rt_memcpy(&aucBuff[3], (BYTE*)&iData, sizeof(INT32));

    ChangeIntoCanFrm(aucBuff, sizeof(aucBuff), DEV_TYPE_BMS, DstAdr);
    return TRUE;
}
#endif

/***************************************************************************
 * @brief    主机将消息通过CAN广播下发给从机
 * @return   True--下发成功 False--下发失败
 **************************************************************************/
BOOLEAN MasterBroadCastToSlave(BYTE *aucRecBuf, BYTE len)
{
    if (IsMaster() && aucRecBuf != NULL)
    {
        sendCanFrmSepc(BROAD_CAST_ADDR, aucRecBuf, len);
        return True;
    }
    return False;
}

Static void sendCanFrmSepc(BYTE ucDstAdr, BYTE* pucBuff, BYTE LEN )
{
    T_CAN_Header tHeader;

    tHeader.DstAdr = ucDstAdr;
    tHeader.DstDev = DEV_TYPE_BMS;
    tHeader.SrcAdr = s_tMyBms.ucAddress;
    tHeader.SrcDev = DEV_TYPE_BMS;
    tHeader.IDE = 1;
    tHeader.RTR = 0;
    tHeader.len = 8;
    tHeader.Level = 1;

    CanDrive_Write(pucBuff, LEN, (BYTE *)&tHeader);
}

Static void GetBattChgPowerCount(void)
{
    if(s_tMyBms.wCnt%REALDATA_CYCLE == 30)
    {
        GetBattChgPower();
    }
}

#ifdef INTELLIGENT_PEAK_SHIFTING

T_FmInfoNotice *GetFmInfoNotice(void)
{
    return &s_tFmInfoNotice;
}

void InitFmInfoNotice(T_FmInfoNotice *ptNotice)
{
    if (ptNotice == NULL)
    {
        return;
    }
    rt_memset_s(ptNotice, sizeof(*ptNotice), 0x00, sizeof(*ptNotice));
    ptNotice->ucFrameId = FM_INFO_NOTICE_FRAME_ID_DEFAULT;
    ptNotice->ucTimerMax = FM_INFO_NOTICE_TIMER_MAX_DEFAULT;
    ptNotice->ucSendCountMax = FM_INFO_NOTICE_SEND_COUNT_MAX_DEFAULT;
}



BOOLEAN DealFmInfoNotice(T_FmInfoNotice *ptNotice)
{
    if (ptNotice == NULL)
    {
        return False;
    }

    RefreshFmInfo(ptNotice);

    if (IsMaster())
    {
        if (IsFmInfoChanged(ptNotice))
        {
            ptNotice->ucFrameId++;
            SendFmInfo(ptNotice);
            ptNotice->ucTimer = 0;
            ptNotice->ucSendCount = 1;
        }
        else if (ptNotice->ucSendCount > 0 && ptNotice->ucSendCount < ptNotice->ucSendCountMax)
        {
            TimerPlus(ptNotice->ucTimer, ptNotice->ucTimerMax);
            if (TimeOut(ptNotice->ucTimer, ptNotice->ucTimerMax))
            {
                SendFmInfo(ptNotice);
                ptNotice->ucSendCount++;
                ptNotice->ucTimer = 0;
            }
        }
    }
    else
    {
        ptNotice->ucTimer = 0;
        ptNotice->ucSendCount = 0;
    }

    return True;
}



BOOLEAN RefreshFmInfo(T_FmInfoNotice *ptNotice)
{
    if (ptNotice == NULL)
    {
        return False;
    }

    ptNotice->tPreviousFmInfo = ptNotice->tCurrentFmInfo;

    GetFmRealData(&s_tFmRealData);
    ptNotice->tCurrentFmInfo.ucChgAndDischgStatus = s_tFmRealData.ucChgAndDischgDisable;
    ptNotice->tCurrentFmInfo.ucFmStatus = s_tFmRealData.ucCurrentFMStatus;

    return True;
}



BOOLEAN IsFmInfoChanged(T_FmInfoNotice *ptNotice)
{
    if (ptNotice == NULL)
    {
        return False;
    }

    return ptNotice->tPreviousFmInfo.ucChgAndDischgStatus != ptNotice->tCurrentFmInfo.ucChgAndDischgStatus
           || ptNotice->tPreviousFmInfo.ucFmStatus != ptNotice->tCurrentFmInfo.ucFmStatus;
}



BOOLEAN SendFmInfo(T_FmInfoNotice *ptNotice)
{
    BYTE aucCmdInfo[4] = {0, };
    BYTE aucSendBuff[28] = {0, };  // 7字节对齐，避免CanDrive_Write()越界
    const T_Yd1363FrameHead tHead = {PROTOCOL_VER_22, GetBMSAddr(), CID1_BMS, {0xFD}};

    if (ptNotice == NULL)
    {
        return False;
    }

    aucCmdInfo[0] = 0x01;
    aucCmdInfo[1] = ptNotice->ucFrameId;
    aucCmdInfo[2] = ptNotice->tCurrentFmInfo.ucFmStatus; // 当前模式
    aucCmdInfo[3] = ptNotice->tCurrentFmInfo.ucChgAndDischgStatus; // 充放电状态

    INT16S sFrameLen = EncodeToYd1363Frame(aucSendBuff, sizeof(aucSendBuff), tHead, aucCmdInfo, sizeof(aucCmdInfo));
    if (sFrameLen < 0)
    {
        return False;
    }
    ChangeIntoCanFrm(aucSendBuff, sFrameLen, DEV_TYPE_SC, 0x01);

    return True;
}

#endif /* INTELLIGENT_PEAK_SHIFTING */


#ifdef USING_SNMP
Static BOOLEAN JudgeCommFailToSnmpAgent(void)
{
    BOOLEAN bHasCommFail = False;
    static WORD s_wCnt = 0;
    if(s_wCnt >=SNMPDATA_CYCLE)
    {
        s_wCnt = 0;
    }
    else
    {
        s_wCnt++;
    }

    if (!IsEthLink() || (s_tMyBms.wCnt % SNMPDATA_CYCLE) != 0)
    {
        return False;
    }

    for (BYTE i = 0; i < SLAVE_NUM; i++)
    {
        if (i == s_tMyBms.ucAddress - 1)
        {
            continue;
        }
        if (s_atSlavesRealData[i].bConnectedToSnmpAgent)
        {
            TimerPlus(s_atSlavesRealData[i].ucCommFailToSnmpAgentCnt, COMM_FAIL_TO_SNMP_AGENT_CNT);
            if (s_atSlavesRealData[i].ucCommFailToSnmpAgentCnt >= COMM_FAIL_TO_SNMP_AGENT_CNT)
            {
                s_atSlavesRealData[i].bConnectedToSnmpAgent = False;
                BattStatusCommFail(i);
                bHasCommFail = True;
            }
        }
        else
        {
            //开机启动后的检测
            SnmpCommFailCheck(i);
        }
    }
    //在位检测
    BattExistCheckSave();
    return bHasCommFail;
}
#endif /* USING_SNMP */


#ifdef SITE_ANTI_THEFT_ENABLED
/***************************************************************************
 * @brief    绑定防盗并机电池信息初始化
 **************************************************************************/
Static void InitBindingInfo(void)
{
    rt_int32_t ReadRtn = 0;
    rt_memset_s(&s_tSaveBindingInfo, sizeof(T_SaveBindingInfoStruct), 0, sizeof(T_SaveBindingInfoStruct));

    ReadRtn = readFile("/bindinginfo", (BYTE *)&s_tSaveBindingInfo, sizeof(T_SaveBindingInfoStruct));

    if (ReadRtn != sizeof(T_SaveBindingInfoStruct) || s_tSaveBindingInfo.wCrc != CRC_Cal((BYTE *)&s_tSaveBindingInfo, offsetof(T_SaveBindingInfoStruct, wCrc)))
    {
        rt_memset_s(&s_tSaveBindingInfo, sizeof(T_SaveBindingInfoStruct), 0, sizeof(T_SaveBindingInfoStruct));
    }

    s_bIsBindingInfoChange = False;
    s_wSendBindingInfoCnt = 0;
    s_ucRecBindingInfoCnt = 0;
    ClearBindingBmsReceivedStat();
    return;
}

/***************************************************************************
 * @brief    绑定防盗时，记录并机电池信息，用于判断电池是否被盗
 **************************************************************************/
Static void DealBindingInfo(void)
{
    static WORD wCnt = 0;
    WORD wOneMinute = 6000;         // 计数100 ≈ 1sec
    if (GetSiteAntiTheftStatus() == False && GetNetAntiTheftStatus() == False)
    {
        s_wSendBindingInfoCnt = 0;
        wCnt = 0;
        return;
    }

    if (s_tSaveBindingInfo.bSaveInfoFinish == True)
    {
        wCnt++;
        if (wCnt % SITE_ANTITHEFT_INFO_SEND_DELAY == s_tMyBms.ucAddress)
        {
            SendBindingInfoCanFrame();
        }

        // 记录并机信息完毕后，每三分钟检查一次此时间段收到的并机电池数量
        if (wCnt > 3 * wOneMinute)
        {
            wCnt = 0;
            s_bIsBindingInfoChange = (s_ucRecBindingInfoCnt < 1) ? True : False;
            s_ucRecBindingInfoCnt = 0;
            ClearBindingBmsReceivedStat();
        }
    }
    else
    {
        // 绑定防盗开启后，初始三分钟内用于发送自身信息和记录其他电池的信息，三分钟达到后认为记录并机信息完毕
        // 由于可能存在并机通讯数据量较大情况，导致自身信息发送间隔较长，因此这里设置一个较长的时间窗口
        if (s_wSendBindingInfoCnt > 3 * wOneMinute)
        {
            s_tSaveBindingInfo.bSaveInfoFinish = True;
            s_wSendBindingInfoCnt = 0;
            s_tSaveBindingInfo.wCrc = CRC_Cal((BYTE *)&s_tSaveBindingInfo, offsetof(T_SaveBindingInfoStruct, wCrc));
            writeFile("/bindinginfo", (BYTE *)&s_tSaveBindingInfo, sizeof(T_SaveBindingInfoStruct));
        }
        else if (s_wSendBindingInfoCnt % SITE_ANTITHEFT_INFO_SEND_DELAY == s_tMyBms.ucAddress)
        {
            SendBindingInfoCanFrame();
        }
    }

    return;
}

/***************************************************************************
 * @brief    返回绑定电池信息是否改变
 **************************************************************************/
BOOLEAN IsBindingInfoChange(void)
{
    return s_bIsBindingInfoChange;
}

/***************************************************************************
 * @brief    电池发送绑定需要的SN码
 **************************************************************************/
Static void SendBindingInfoCanFrame(void)
{
    T_CAN_Header tHeader;
    BYTE aucBuff[7] = {0,};
    WORD wCRC;
    T_BmsPACKFactoryStruct tBmsFactInfo;
    rt_memset_s(&tBmsFactInfo, sizeof(T_BmsPACKFactoryStruct), 0, sizeof(T_BmsPACKFactoryStruct));
    readBmsPackFacInfo(&tBmsFactInfo);

    aucBuff[0] = FN_SITE_ANTITHEFT;
    wCRC = CRC_Cal((BYTE *)&tBmsFactInfo.acBmsFacSn[0], rt_strnlen_s(tBmsFactInfo.acBmsFacSn, 15));
    rt_memcpy_s(&aucBuff[1], 2, (BYTE *)&wCRC, 2);
    rt_memcpy_s(&aucBuff[3], 4, &tBmsFactInfo.acBmsFacSn[0], rt_strnlen_s(tBmsFactInfo.acBmsFacSn, 4));

    tHeader.DstAdr = 0;
    tHeader.DstDev = DEV_TYPE_BMS;
    tHeader.SrcAdr = s_tMyBms.ucAddress;
    tHeader.SrcDev = DEV_TYPE_BMS;
    tHeader.IDE = 1;
    tHeader.RTR = 0;
    tHeader.len = 8;
    tHeader.Level = 1;
    CanDrive_Write(aucBuff, 7, (BYTE *)&tHeader);
    return;
}

/***************************************************************************
 * @brief    处理接收到的绑定信息
 **************************************************************************/
Static void ParseBindingInfoFrame(BYTE *p, rt_uint32_t SrcAdr)
{
    BYTE ucLen;
    if (s_tSaveBindingInfo.bSaveInfoFinish == False)
    {
        ucLen = rt_strnlen_s((const char *)&p[1], 6);
        s_tSaveBindingInfo.atBindingInfo[SrcAdr].bFilled = True;
        rt_memcpy_s(s_tSaveBindingInfo.atBindingInfo[SrcAdr].acBmsSn, 6, &p[1], ucLen);
        return;
    }
    else
    {
        ucLen = rt_strnlen_s(s_tSaveBindingInfo.atBindingInfo[SrcAdr].acBmsSn, 6);
        if (rt_memcmp(s_tSaveBindingInfo.atBindingInfo[SrcAdr].acBmsSn, &p[1], ucLen) == 0 &&
            s_tSaveBindingInfo.atBindingInfo[SrcAdr].bReceived == 0 &&
            s_tSaveBindingInfo.atBindingInfo[SrcAdr].bFilled == True)
        {
            s_tSaveBindingInfo.atBindingInfo[SrcAdr].bReceived = 1;
            s_ucRecBindingInfoCnt++;
        }
    }
    return;
}

/***************************************************************************
 * @brief    关闭防盗时，同时清除并联BMS信息
 **************************************************************************/
void ClearBindingBmsInfo(void)
{
    s_ucRecBindingInfoCnt = 0;
    s_bIsBindingInfoChange = 0;
    rt_memset(&s_tSaveBindingInfo, 0, sizeof(T_SaveBindingInfoStruct));
    writeFile("/bindinginfo", (BYTE *)&s_tSaveBindingInfo, sizeof(T_SaveBindingInfoStruct));
    return;
}

/***************************************************************************
 * @brief    清除绑定防盗中的电池信息接收标志位
 **************************************************************************/
Static void ClearBindingBmsReceivedStat(void)
{
    for (BYTE i = 0; i < SLAVE_NUM; i++)
    {
        s_tSaveBindingInfo.atBindingInfo[i].bReceived = 0;
    }
    return;
}
#endif


WORD GetCANCommLostCnt(void)
{
    return s_wCANCommLostCnt_5min;
}

void ClearCANCommLostCnt(void)
{
    s_wCANCommLostCnt_5min = 0;
    return;
}

void IncCANCommLostCnt(void)
{
    ++s_wCANCommLostCnt_5min;
}

void SetCANCommLostCnt(WORD value)
{
    s_wCANCommLostCnt_5min = value;
}

Static BOOLEAN WaitAddrTimeoutJudege(void)
{
    if (s_tSlaveTCB.ucWaitAddr > 0 )
    {
        if (s_tSlaveTCB.ucWaitTime < 100)
        {
            s_tSlaveTCB.ucWaitTime++;
            return True;
        }
        else
        {
            MainLoopDealSlaveTCB();
        }
    }

    return False;
}

void TemplateForCanFrm(BYTE *dataComm, WORD wInLen, BYTE ucInScrDev, BYTE ucInSrcAdr)
{
    T_CAN_Header tHead;	

    rt_memset(&tHead, 0x00, sizeof(tHead));
    tHead.Level   = LNK_LEVEL;
    tHead.SrcDev  = DEV_TYPE_SC;
    tHead.SrcAdr  = s_tMyBms.ucAddress;
    tHead.DstDev  = ucInScrDev;
    tHead.DstAdr  = ucInSrcAdr;
    tHead.IDE = 1;
    tHead.RTR = 0;
    tHead.len = CAN_FRAME_MAX_LEN;
    CanDrive_Write(dataComm, wInLen, (BYTE *)&tHead);
    return;
}
