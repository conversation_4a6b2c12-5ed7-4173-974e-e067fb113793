/**
 * @brief main板网络通信头文件
 */

#ifndef _MAIN_NET_COMM_H_
#define _MAIN_NET_COMM_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "linklayer.h"
#include "device_type.h"
#include "north_main.h"
#include "net_ip.h"
#include "app_config.h"

#define IP_THREAD_RUN_PERIOD                   50
#define S1363_COMMUNICATION_TIMEOUT_CNT        (8 * 60 * 1000 / IP_THREAD_RUN_PERIOD)    // 8分钟无数据交换，服务端主动断开客户端连接
#define UPDATE_COMMUNICATION_TIMEOUT_CNT       (50 * 1000 / IP_THREAD_RUN_PERIOD)       // 50s无数据交换，服务端主动断开客户端连接

void wireless_protocol(void *parameter);
unsigned char delete_wireless_pro_thread(dev_inst_t* dev_inverter_ip, 
                                    link_inst_t* link_inst_ip, north_mgr_t* north_mgr);

dev_inst_t** get_ip_dev_inst();
void wireless_protocol_main(dev_inst_t* dev_inverter_ip, link_inst_t* link_inst_ip, north_mgr_t* north_mgr, net_pro_para_t* net_pro_para);
int init_ip_link_info(net_pro_para_t* parameter, north_mgr_t** north_mgr, dev_inst_t** dev_inverter_ip);
void wired_network_listen_thread(void* thread_data);
int* init_wired_server_listen(void * param);

#ifdef __cplusplus
}
#endif      //< __cplusplus

#endif      //< _MAIN_NET_COMM_H_
