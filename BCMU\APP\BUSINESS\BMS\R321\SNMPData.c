#include "common.h"
#include "rtthread.h"
#include "CommCan.h"
#include "CommCan2.h"
#include "sample.h"
#include "realAlarm.h"
#include "utils_heart_beat.h"
#include "thread_id.h"
#include "battery.h"
#include "para.h"
#include "comm.h"
#include "protocol.h"
#include "MdbsRtu.h"
#include "commBdu.h"
#include "prtclDL.h"
#include "fileSys.h"
#include "hisdata.h"
#include "flash.h"
#include <rtdevice.h>
#include "sys/time.h"
#include "const_define.h"
#include "SNMPData.h"
#include "SNMPData_in.h"
#include "pdt_version.h"
#include "rtthread.h"
#include "download_tcp.h"
#include "utils_rtthread_security_func.h"
#include <netif/ethernetif.h>
#include "lwip/netifapi.h"
#include "netdev.h"
#include "mbedtls/sha256.h"
#include <stddef.h>

Static T_SysPara s_tSysPara;
static T_PeakShiftPara s_tPeakShiftPara;

Static T_SnmpNetAntiTheftStruct s_tSnmpNetAntiTheftInfo = {0,};
Static BYTE s_ucNetChangeKeyFlag;

T_SnmpDataStruct s_atSnmpData[SLAVE_NUM];
T_SnmpParaStruct s_tSnmpPara;
Static BYTE s_ucExistSlave[SLAVE_NUM] = {0,};
Static BYTE s_ucExistSlaveBak[SLAVE_NUM] = {0,};

static rt_device_t ETH_dev;

const T_SnmpRealDataStruct *s_ptSnmpRealData;
const T_SnmpRealAlarmStruct *s_ptSnmpRealAlarm;
const T_SnmpSyncParaStruct *s_ptSnmpSyncPara;
const T_SnmpSyncParaStrStruct *s_ptSnmpSyncParaStr;
const T_SnmpFactInfoStruct *s_ptSnmpFactInfo;
const T_SnmpCtrlCmd  *s_ptSnmpCtrlCmd;
Static rt_tick_t sample_tick = 0;
Static rt_tick_t sample_tick_pass = 0;

static BOOLEAN ClearBattExistInfo(void);
static BOOLEAN  BattExistInfoToRealData(void);

Static BYTE StartNetAntiTheftSNMP(BYTE *pucNetAntiTheftKey);
Static BYTE HeartNetAntiTheftSNMP(BYTE *pucNetAntiTheftKey);
Static BYTE EndNetAntiTheftSNMP(BYTE *pucNetAntiTheftKey);
Static BYTE NetAntiThefOldtKeySNMP(BYTE* pucNetAntiTheftKey);
Static BYTE ChangeNetAntiTheftKeySNMP(BYTE* pucNetAntiTheftKeyOld);
Static BOOLEAN CtrlSnmpNetAntiTheft(BYTE ucIndex);

BOOLEAN IsEthLink(void)
{
    struct netdev *pDev = NULL;

    pDev = netdev_get_by_name("e0");
    if (pDev != RT_NULL && (pDev->flags & NETDEV_FLAG_LINK_UP))
    {
        return TRUE;
    }
    return FALSE;
}

BOOLEAN SnmpGetMacAddr(BYTE macAddr[])
{
    BYTE aucMacAddr[6] = {0};
    BYTE i = 0;
    ETH_dev = rt_device_find("e0");
    if (RT_NULL == ETH_dev)
    {
        return FALSE;
    }
    rt_device_control( ETH_dev , NIOCTL_GADDR ,aucMacAddr);
    for( i = 0 ; i < 6 ; i ++ )
    {
        macAddr[i] = aucMacAddr[i];
    }
    return TRUE;
}

WORD CollectRealDataForSNMP(BYTE *p, WORD wLenMax)
{
    BYTE i = 0, j = 0, ucSrcAddr = 0;
    WORD wLen = 0;

    if(p == NULL)
        return 0;

    ucSrcAddr = GetBMSAddr();

    if(ucSrcAddr == 0 || ucSrcAddr > SLAVE_NUM)
    {
        return 0;
    }

    if(wLenMax < (SNMP_REAL_DATA_NUM*4))     //判断数据量是否超过发送缓冲区上限
    {
        return 0;
    }

    for(i=0; i<SNMP_REAL_DATA_NUM; i++)
    {
        for(j=0;j<4;j++)
        {
            p[wLen++]= (s_atSnmpData[ucSrcAddr-1].aiRealData[i] >> (j*8)) & 0xff;
        }
    }

    return wLen;
}

UINT32 CollectRealAlarmForSNMP(BYTE *p, WORD wLenMax)
{
    BYTE i = 0, j = 0, ucSrcAddr = 0;
    UINT32 uiLen = 0;
   
    if(p == NULL)
        return 0;

    ucSrcAddr = GetBMSAddr();

    if(ucSrcAddr == 0 || ucSrcAddr > SLAVE_NUM)
    {
        return 0;
    }

    ///////////////////////获取数据发送总长度////////////////////////////////////
    for(i=0; i<SNMP_REAL_ALARM_NUM; i++)
    {
        if(s_ptSnmpRealAlarm[i].ucNum > 8)
        {
            uiLen += 4;
        }
        else
        {
            uiLen++;
        }
    }

    if(uiLen > wLenMax)      //判断数据量是否超过发送缓冲区上限
    {
        return 0;
    }

    uiLen = 0;
    for(i=0; i<SNMP_REAL_ALARM_NUM; i++)
    {
        if(s_ptSnmpRealAlarm[i].ucNum > 8)
        {
            for(j=0;j<4;j++)
            {
                p[uiLen++]= (s_atSnmpData[ucSrcAddr-1].aiRealAlarm[i] >> (j*8)) & 0xff;
            }
        }
        else
        {
            p[uiLen++]= s_atSnmpData[ucSrcAddr-1].aiRealAlarm[i] & 0xff;
        }
    }
    return uiLen;
}

UINT32 CollectSyncParaForSNMP(BYTE *p)
{
    BYTE i = 0;
    UINT32 uiLen = 0;
    BYTE *pStart = NULL;
    SHORT sTmp = 0;
    
    if(p == NULL)
        return 0;

    GetSysPara(&s_tSysPara);
    pStart = (BYTE*)(&s_tSysPara);

    for(i=0; i<SNMP_PARA_NUM; i++)
    {
        if(s_ptSnmpSyncPara[i].ucDataType == DATA_TYPE_INT8U)
        {
            sTmp = (SHORT)((*(BYTE*)(pStart + s_ptSnmpSyncPara[i].wLocalParaDataOffset))*Pow10(s_ptSnmpSyncPara[i].ucPrecision));
        }
        else if(s_ptSnmpSyncPara[i].ucDataType == DATA_TYPE_INT16U)
        {
            sTmp = (SHORT)((*(WORD*)(pStart + s_ptSnmpSyncPara[i].wLocalParaDataOffset))*Pow10(s_ptSnmpSyncPara[i].ucPrecision));
        }
        else if(s_ptSnmpSyncPara[i].ucDataType == DATA_TYPE_FP32)
        {
            sTmp = FLOAT_TO_SHORT((*(FLOAT*)(pStart + s_ptSnmpSyncPara[i].wLocalParaDataOffset))*Pow10(s_ptSnmpSyncPara[i].ucPrecision));
        }
        p[uiLen++]= (sTmp) & 0xff;
        p[uiLen++]= (sTmp >> 8) & 0xff;
    }

    return uiLen;
}

UINT32 CollectSyncParaStrForSNMP(BYTE *p)
{
    BYTE i = 0;
    UINT32 uiLen = 0;
    BYTE *pStart = NULL;
    WORD wTmp;
    BYTE *pNetAntiTheftInfo = NULL;
    BYTE str_tmp_can[SNMP_OTHER_STRING_NUM];
    
    if(p == NULL)
        return 0;

    GetSysPara(&s_tSysPara);
    pStart = (BYTE*)(&s_tSysPara);
    pNetAntiTheftInfo = (BYTE*)(&s_tSnmpNetAntiTheftInfo);
 
    for(i=0; i<SNMP_PARA_STR_NUM; i++)
    {
        if(s_ptSnmpSyncParaStr[i].ucDataType == DATA_TYPE_CHAR)
        {
            if(s_ptSnmpSyncParaStr[i].ucParaType == 1) //网管防盗参数
            {
                rt_memcpy_s(p, s_ptSnmpSyncParaStr[i].ucLen, (pNetAntiTheftInfo + s_ptSnmpSyncParaStr[i].wOffset), s_ptSnmpSyncParaStr[i].ucLen);
            }
            else
            {
                rt_memcpy_s(p, s_ptSnmpSyncParaStr[i].ucLen, pStart + s_ptSnmpSyncParaStr[i].wOffset, s_ptSnmpSyncParaStr[i].ucLen);
            }
        }
        else if(s_ptSnmpSyncParaStr[i].ucDataType == DATA_TYPE_INT16U)
        {
            wTmp = *(WORD*)(pStart + s_ptSnmpSyncParaStr[i].wOffset);

            rt_memset(str_tmp_can, 0x00, sizeof(str_tmp_can));
            rt_snprintf((char*)str_tmp_can,sizeof(str_tmp_can),"%d",wTmp);
            rt_memcpy(p, str_tmp_can, s_ptSnmpSyncParaStr[i].ucLen);
        }
        p = p + s_ptSnmpSyncParaStr[i].ucLen;
        uiLen = uiLen + s_ptSnmpSyncParaStr[i].ucLen;
    }
    return uiLen;
}

UINT32 CollectFactInfoForSNMP(BYTE *p)
{
    BYTE i = 0;
    UINT32 uiLen = 0;
    BYTE *pStart = NULL;
    BYTE ucSrcAddr;

    if(p == NULL)
        return 0;

    ucSrcAddr = GetBMSAddr();
    if(ucSrcAddr == 0 || ucSrcAddr > SLAVE_NUM)
    {
        return 0;
    }

    pStart = (BYTE*)(&(s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo));

    for(i=0; i<SNMP_FACT_INFO_NUM; i++)
    {
        rt_memcpy(p,pStart+s_ptSnmpFactInfo[i].wOffset,s_ptSnmpFactInfo[i].ucLen);
        p = p + s_ptSnmpFactInfo[i].ucLen;
        uiLen = uiLen + s_ptSnmpFactInfo[i].ucLen;
    }

    return uiLen;
}

BOOLEAN SetSNMPRealData(BYTE *p, BYTE ucSrcAddr)
{
    BYTE i = 0, j = 0;
    WORD wLen = 0, wIndex = 0;

    if(p == NULL)
        return FALSE;
    
    if(ucSrcAddr == 0 || ucSrcAddr > SLAVE_NUM)
        return FALSE;

    wLen = (p[0]<<8) | p[1];

    for(i=0; i<SNMP_REAL_DATA_NUM; i++)
    {
        s_atSnmpData[ucSrcAddr-1].aiRealData[i] = 0;
        for(j=0; j<4; j++)
        {
            s_atSnmpData[ucSrcAddr-1].aiRealData[i] = s_atSnmpData[ucSrcAddr-1].aiRealData[i] | (p[wIndex+2]<<(j*8));
            wIndex++;
        }

        if(wIndex >= wLen)
        {
            break;
        }
    }

#ifdef HAS_SNMP_REALDATA_BATTEXISTSTATUS
    s_atSnmpData[ucSrcAddr - 1].aiRealData[SNMP_ID_REALDATA_BATTEXISTSTATUS - 1] = BATT_STATUS_EXIST;
    s_ucExistSlave[ucSrcAddr - 1] = BATT_STATUS_EXIST;  //在位从机
#endif

    return TRUE;
}

BOOLEAN SetSNMPRealAlarm(BYTE *p, BYTE ucSrcAddr)
{
    BYTE i = 0,j=0;
    WORD wLen = 0, wIndex = 0;

    if(p == NULL)
        return FALSE;
    
    if(ucSrcAddr == 0 || ucSrcAddr > SLAVE_NUM)
        return FALSE;
    
    wLen = (p[0]<<8) | p[1];

    for (i=0; i<SNMP_REAL_ALARM_NUM; i++)
    {
        if(s_ptSnmpRealAlarm[i].ucNum > 8)
        {
            for(j=0; j<4; j++)
            {
                s_atSnmpData[ucSrcAddr-1].aiRealAlarm[i] = s_atSnmpData[ucSrcAddr-1].aiRealAlarm[i] | (p[wIndex+2]<<(j*8));
                wIndex++;
                if(wIndex >= wLen)
                {
                    break;
                }
            }
        }
        else
        {
            s_atSnmpData[ucSrcAddr-1].aiRealAlarm[i] = p[wIndex+2];
            wIndex++;
        }
        if(wIndex >= wLen)
        {
            break;
        }
    }

    return TRUE;
}

BOOLEAN SetSNMPSyncPara(BYTE *p)
{
    BYTE i = 0;
    BYTE *pStart = NULL;
    WORD wLen = 0, wIndex = 0;
    SHORT sTmp;

    if(p == NULL)
        return FALSE;

    wLen = (p[0]<<8) | p[1];

    pStart = (BYTE*)(&s_tSysPara);

    wIndex = 2;
    for (i=0; i<SNMP_PARA_NUM; i++)
    {
        if(s_ptSnmpSyncPara[i].ucDataType == DATA_TYPE_INT8U)
        {
            sTmp = (SHORT)(p[wIndex] | (p[wIndex+1] << 8));
            *(BYTE*)(pStart + s_ptSnmpSyncPara[i].wLocalParaDataOffset) =sTmp / Pow10(s_ptSnmpSyncPara[i].ucPrecision);
        }
        else if(s_ptSnmpSyncPara[i].ucDataType == DATA_TYPE_INT16U)
        {
            sTmp = (SHORT)(p[wIndex] | (p[wIndex+1] << 8));
            *(SHORT*)(pStart + s_ptSnmpSyncPara[i].wLocalParaDataOffset) =sTmp / Pow10(s_ptSnmpSyncPara[i].ucPrecision);
        }
        else if(s_ptSnmpSyncPara[i].ucDataType == DATA_TYPE_FP32)
        {
            sTmp = (SHORT)(p[wIndex] | (p[wIndex+1] << 8));
            *(FLOAT*)(pStart + s_ptSnmpSyncPara[i].wLocalParaDataOffset) =sTmp / Pow10(s_ptSnmpSyncPara[i].ucPrecision);
        }
        wIndex = wIndex + 2;
        if(wIndex >= wLen)
        {
            break;
        }
    }

    if(SetSysPara(&s_tSysPara, True, CHANGE_BY_SNMP) == False)
    {
        return FALSE;
    }
    return TRUE;
}

BOOLEAN SetSNMPSyncParaStr(BYTE *p)
{
    BYTE i = 0,uctmp[SNMP_OTHER_STRING_NUM] = {0};
    BYTE *pStart = NULL;
    WORD wLen = 0;
    UINT32 uiIndex = 0;
    BYTE *pNetAntiTheftInfo = NULL;

    if(p == NULL)
        return FALSE;

    wLen = (p[0]<<8) | p[1];

    pStart = (BYTE*)(&s_tSysPara);
    pNetAntiTheftInfo = (BYTE*)(&s_tSnmpNetAntiTheftInfo);

    uiIndex = 2;
    for (i=0; i<SNMP_PARA_STR_NUM; i++)
    {
        if((uiIndex + s_ptSnmpSyncParaStr[i].ucLen) > wLen)
        {
            break;
        }
        if(s_ptSnmpSyncParaStr[i].ucDataType == DATA_TYPE_CHAR)
        {
            if(s_ptSnmpSyncParaStr[i].ucParaType == 1) //网管防盗参数
            {
                rt_memcpy_s((pNetAntiTheftInfo + s_ptSnmpSyncParaStr[i].wOffset), s_ptSnmpSyncParaStr[i].ucLen, &p[uiIndex], s_ptSnmpSyncParaStr[i].ucLen);
            }
            else
            {
                rt_memcpy_s(pStart + s_ptSnmpSyncParaStr[i].wOffset, s_ptSnmpSyncParaStr[i].ucLen, &p[uiIndex], s_ptSnmpSyncParaStr[i].ucLen); 
            }
        }
        else if(s_ptSnmpSyncParaStr[i].ucDataType == DATA_TYPE_INT16U)
        {
            rt_memcpy(uctmp, &p[uiIndex], s_ptSnmpSyncParaStr[i].ucLen);
            *(WORD*)(pStart + s_ptSnmpSyncParaStr[i].wOffset) = (WORD)strtol((char *)uctmp, NULL, 10);
        }
        uiIndex = uiIndex + s_ptSnmpSyncParaStr[i].ucLen;
    }

    if(SetSysPara(&s_tSysPara, True, CHANGE_BY_SNMP) == False)
    {
        return FALSE;
    }
    return TRUE;
}

BOOLEAN SetSNMPFactInfo(BYTE *p, BYTE ucSrcAddr)
{
    BYTE i = 0;
    BYTE *pStart = NULL;
    WORD wLen = 0;
    UINT32 uiIndex = 0;

    if(p == NULL)
        return FALSE;
    
    if(ucSrcAddr == 0 || ucSrcAddr > SLAVE_NUM)
        return FALSE;
    
    wLen = (p[0]<<8) | p[1];
 
    pStart = (BYTE*)(&(s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo));

    for(i=0; i<SNMP_FACT_INFO_NUM; i++)
    {
        if((uiIndex + s_ptSnmpFactInfo[i].ucLen) > wLen)
        {
            break;
        }
        rt_memcpy((pStart + s_ptSnmpFactInfo[i].wOffset),p+2+uiIndex,s_ptSnmpFactInfo[i].ucLen);
        uiIndex = uiIndex + s_ptSnmpFactInfo[i].ucLen;
    }

    return TRUE;
}

static BOOLEAN SyncSelfData(void);

BOOLEAN InitSNMPData(BYTE ucAddr)
{
    if(ucAddr >= SLAVE_NUM)
    {
        return FALSE;
    }

    rt_memset(&s_atSnmpData[ucAddr],0x00,sizeof(T_SnmpDataStruct));

    return TRUE;
}

void Process_SNMP(void* parameter)
{
    rt_memset(s_atSnmpData, 0x00, sizeof(s_atSnmpData));
    rt_memset(&s_tSnmpPara, 0x00, sizeof(s_tSnmpPara));

    BattExistInfoToRealData();
    s_ptSnmpRealData = GetSnmpRealDataPoint();
    s_ptSnmpRealAlarm = GetSnmpRealAlarmPoint();
    s_ptSnmpSyncPara = GetSnmpParaPoint();
    s_ptSnmpSyncParaStr = GetSnmpParaStrPoint();
    s_ptSnmpFactInfo = GetSnmpFactInfoPoint();
    s_ptSnmpCtrlCmd = GetSnmpCtrlCmdPoint();

    pre_thread_beat_f(THREAD_SNMP_COMM);

#ifndef UNITEST
    while(1)
#endif
    {
        thread_beat_go_on(THREAD_SNMP_COMM);
        rt_thread_delay(1000);
        SyncSelfData();
    }
}

static BOOLEAN SyncSelfData_RealData(void)
{
    BYTE *pStart = NULL;
    T_BCMDataStruct tBCMAnaData = {0};
    BYTE ucSrcAddr,i;

    ucSrcAddr = GetBMSAddr();
    if(ucSrcAddr == 0 || ucSrcAddr > SLAVE_NUM)
    {
        return FALSE;
    }

    GetRealData( &tBCMAnaData );
    pStart = (BYTE*)(&tBCMAnaData);
 
    for(i=0; i<SNMP_REAL_DATA_NUM; i++)
    {
        if(s_ptSnmpRealData[i].ucDataType == DATA_TYPE_INT8U)
        {
            s_atSnmpData[ucSrcAddr-1].aiRealData[i] = (INT32)((*(BYTE*)(pStart + s_ptSnmpRealData[i].wOffset))*Pow10(s_ptSnmpRealData[i].ucPrecision));
        }
        else if(s_ptSnmpRealData[i].ucDataType == DATA_TYPE_INT16U)
        {
            s_atSnmpData[ucSrcAddr-1].aiRealData[i] = (INT32)((*(WORD*)(pStart + s_ptSnmpRealData[i].wOffset))*Pow10(s_ptSnmpRealData[i].ucPrecision));
        }
        else if(s_ptSnmpRealData[i].ucDataType == DATA_TYPE_INT16S)
        {
            s_atSnmpData[ucSrcAddr-1].aiRealData[i] = (INT32)((*(SHORT*)(pStart + s_ptSnmpRealData[i].wOffset))*Pow10(s_ptSnmpRealData[i].ucPrecision));
        }
        else if(s_ptSnmpRealData[i].ucDataType == DATA_TYPE_INT32U)
        {
            s_atSnmpData[ucSrcAddr-1].aiRealData[i] = (INT32)((*(UINT32*)(pStart + s_ptSnmpRealData[i].wOffset))*Pow10(s_ptSnmpRealData[i].ucPrecision));
        }
        else if(s_ptSnmpRealData[i].ucDataType == DATA_TYPE_INT32S)
        {
            s_atSnmpData[ucSrcAddr-1].aiRealData[i] = (*(INT32*)(pStart + s_ptSnmpRealData[i].wOffset))*Pow10(s_ptSnmpRealData[i].ucPrecision);
        }
        else if(s_ptSnmpRealData[i].ucDataType == DATA_TYPE_FP32)
        {
            s_atSnmpData[ucSrcAddr-1].aiRealData[i] = FLOAT_TO_INT32((*(FLOAT*)(pStart + s_ptSnmpRealData[i].wOffset))*Pow10(s_ptSnmpRealData[i].ucPrecision));
        }
    }

#ifdef HAS_SNMP_REALDATA_BATTEXISTSTATUS
    s_atSnmpData[ucSrcAddr - 1].aiRealData[SNMP_ID_REALDATA_BATTEXISTSTATUS - 1] = BATT_STATUS_EXIST;
#endif
    return TRUE;
}

static BOOLEAN SyncSelfData_RealAlarm(void)
{
    BYTE *pStart = NULL;
    BYTE ucSrcAddr,i,j;
    T_BCMAlarmStruct tBCMAlarm;

    ucSrcAddr = GetBMSAddr();
    if(ucSrcAddr == 0 || ucSrcAddr > SLAVE_NUM)
    {
        return FALSE;
    }
    
    GetRealAlarm(BCM_ALARM, (BYTE*)&tBCMAlarm);
    pStart = (BYTE*)(&tBCMAlarm);

    for(i=0; i<SNMP_REAL_ALARM_NUM; i++)
    {
        BYTE ucAlarmTmp = 0;
        UINT32 uiTmp = 0;

        for(j=0; j<s_ptSnmpRealAlarm[i].ucNum; j++)
        {
            ucAlarmTmp = *(BYTE*)(pStart + s_ptSnmpRealAlarm[i].wOffset + j);
            uiTmp = (ucAlarmTmp != NORMAL) ? (1<<j |uiTmp) : uiTmp;
        }

        s_atSnmpData[ucSrcAddr-1].aiRealAlarm[i]= uiTmp;
    }

    return TRUE;
}


static BOOLEAN SyncSelfData_FactInfo(void)
{
    BYTE ucSrcAddr;
    BYTE str_tmp[SNMP_STRING_NUM] = {0};
    BYTE softwareTag[CUSTOMIZED_INFO_LEN];
    T_DCFactory tDcFactory;
    T_BmsPACKFactoryStruct tBmsFactInfo;
    T_BmsPACKManufactStruct tPackInfo;
    T_SoftwareCustomizedInfo tSoftwareCustomizedInfo = {0, };
    T_BmsInfoStruct tBmsInfo;
    BYTE macAddr[6] = {0};

    T_IMEI tIMEI;

    ucSrcAddr = GetBMSAddr();
    if(ucSrcAddr == 0 || ucSrcAddr > SLAVE_NUM)
    {
        return FALSE;
    }

    readBmsPackFacInfo(&tBmsFactInfo);
    readPackManufact(&tPackInfo);
    readBMSInfofact(&tBmsInfo);
    readSoftwareCustomizedInfo(&tSoftwareCustomizedInfo);
    GetIMEI(&tIMEI);
    GetBduFact(&tDcFactory);
    SnmpGetMacAddr(&macAddr[0]);
    GetSoftwareTagInfo(softwareTag, CUSTOMIZED_INFO_LEN); //计算软件标识码

    rt_memset(&(s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo), 0x00, sizeof(T_SNMPFactoryInfo));
    rt_strncpy_s((char*)s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo.acCorpName, SNMP_STRING_NUM, (char*)tBmsInfo.acBattCorpName, SNMP_STRING_NUM - 1);  //电池厂家名称
    rt_strncpy_s((char*)s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo.acDeviceSn, SNMP_STRING_NUM, (char*)tBmsFactInfo.acDeviceSn, SNMP_STRING_NUM - 1); //整机出厂序列号
    rt_strncpy_s((char*)s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo.acBmsTypeName, SNMP_STRING_NUM, (char*)g_ProConfig.chBMSType, SNMP_STRING_NUM - 1); //BMS型号
    rt_strncpy_s((char*)s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo.acBmsFacSn, SNMP_STRING_NUM, (char*)tBmsFactInfo.acBmsFacSn, SNMP_STRING_NUM - 1); //BMS序列号
    rt_strncpy_s((char*)s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo.acBMSSysName, SNMP_STRING_NUM, (char*)s_tSysPara.acBMSSysName, SNMP_STRING_NUM - 1); //BMS系统名称
    rt_strncpy_s((char*)s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo.acBootDate, SNMP_STRING_NUM, (char *)BOOT_VER_START, SNMP_STRING_NUM - 1); //BOOT日期

    if(CheckArrAllZero(tSoftwareCustomizedInfo.CustomizedBmsVersion, sizeof(tSoftwareCustomizedInfo.CustomizedBmsVersion)))
    {
        rt_strncpy_s((char*)s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo.acBMSVersion, SNMP_STRING_NUM, (char*)BMS_VER, SNMP_STRING_NUM - 1); //BMS软件版本
    }
    else
    {
        rt_strncpy_s((char*)s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo.acBMSVersion, SNMP_STRING_NUM, (char*)tSoftwareCustomizedInfo.CustomizedBmsVersion, SNMP_STRING_NUM - 1); //BMS软件版本
    }

    rt_strncpy_s((char*)s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo.acSoftwareTag, SNMP_STRING_NUM, (char*)softwareTag, SNMP_STRING_NUM - 1); //软件标识码   

    rt_memset(str_tmp, 0, 20);

    if(CheckArrAllZero(tSoftwareCustomizedInfo.CustomizedBmsReleaseDate, sizeof(tSoftwareCustomizedInfo.CustomizedBmsReleaseDate)))
    {
        rt_snprintf((char*)str_tmp,sizeof(str_tmp),"%d-%d-%d",SOFTWARE_RELEASE_YEAR,SOFTWARE_RELEASE_MONTH,SOFTWARE_RELEASE_DATE);
    }
    else
    {
        rt_snprintf((char*)str_tmp,sizeof(str_tmp),"%d-%d-%d",tSoftwareCustomizedInfo.CustomizedBmsReleaseDate[0]<<8+tSoftwareCustomizedInfo.CustomizedBmsReleaseDate[1],tSoftwareCustomizedInfo.CustomizedBmsReleaseDate[2],tSoftwareCustomizedInfo.CustomizedBmsReleaseDate[3]);
    }

    rt_strncpy_s((char*)s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo.acBMSSoftDate, SNMP_STRING_NUM, (char*)str_tmp, SNMP_STRING_NUM - 1); //BMS软件发布日期

    if(CheckArrAllZero(tSoftwareCustomizedInfo.CustomizedBduVersion, sizeof(tSoftwareCustomizedInfo.CustomizedBduVersion)))
    {
        rt_strncpy_s((char*)s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo.acBDUSoftVer, SNMP_STRING_NUM, (char*)tDcFactory.acSoftVer, SNMP_STRING_NUM - 1); //BDU软件版本
    }
    else
    {
        rt_strncpy_s((char*)s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo.acBDUSoftVer, SNMP_STRING_NUM, (char*)tSoftwareCustomizedInfo.CustomizedBduVersion, SNMP_STRING_NUM - 1); //BDU软件版本
    }

    rt_memset(str_tmp, 0, 20);

    if(CheckArrAllZero(tSoftwareCustomizedInfo.CustomizedBduRealseDate, sizeof(tSoftwareCustomizedInfo.CustomizedBduRealseDate)))
    {
        rt_snprintf((char*)str_tmp,sizeof(str_tmp),"%d-%d-%d",(tDcFactory.acVerDate[0]<<8)+(tDcFactory.acVerDate[1]),tDcFactory.acVerDate[2],tDcFactory.acVerDate[3]);
    }
    else
    {
        rt_snprintf((char*)str_tmp,sizeof(str_tmp),"%d-%d-%d",(tSoftwareCustomizedInfo.CustomizedBduRealseDate[0]<<8)+(tSoftwareCustomizedInfo.CustomizedBduRealseDate[1]),tSoftwareCustomizedInfo.CustomizedBduRealseDate[2],tSoftwareCustomizedInfo.CustomizedBduRealseDate[3]);
    }

    rt_strncpy_s((char*)s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo.acBDUSoftDate, SNMP_STRING_NUM, (char*)str_tmp, SNMP_STRING_NUM - 1); //BDU版本日期
    rt_strncpy_s((char*)s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo.acCellManufactruer, SNMP_STRING_NUM, (char*)tPackInfo.acCellManufactruer, SNMP_STRING_NUM - 1); //电芯厂家名称
    rt_memset(str_tmp, 0, 20);
    rt_snprintf((char*)str_tmp,sizeof(str_tmp),"%d-%d-%d",tPackInfo.tBattDate.wYear,tPackInfo.tBattDate.ucMonth,tPackInfo.tBattDate.ucDay);
    rt_strncpy_s((char*)s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo.acPackDataEnable, SNMP_STRING_NUM, (char*)str_tmp, SNMP_STRING_NUM - 1); //电芯出厂日期
    rt_strncpy_s((char*)s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo.acIMEISn, SNMP_STRING_NUM, (char*)tIMEI.acIMEI, SNMP_STRING_NUM - 1); //IMEI
    rt_memset(str_tmp, 0, 20);
    rt_snprintf((char*)str_tmp,sizeof(str_tmp),"%02x:%02x:%02x:%02x:%02x:%02x", macAddr[0], macAddr[1], macAddr[2], macAddr[3], macAddr[4], macAddr[5]);
    rt_strncpy_s((char*)s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo.acMacAddress, SNMP_STRING_NUM, (char*)str_tmp, SNMP_STRING_NUM - 1); //MAC

    // 以下代码暂时保留，SNMP系统测试通过验收后删除
    // MemsetBuff(s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo.acCorpName, BATT_CORP_NAME, (BYTE)rt_strlen(BATT_CORP_NAME), (BYTE)20, 0x20);  //电池厂家名称
    // MemsetBuff(s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo.acDeviceSn, tBmsFactInfo.acDeviceSn, (BYTE)rt_strlen(tBmsFactInfo.acDeviceSn), (BYTE)20, 0x20); //整机出厂序列号
    // MemsetBuff(s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo.acBmsTypeName, BMS_TYPE_NEW, (BYTE)rt_strlen(BMS_TYPE_NEW), (BYTE)20, 0x20); //BMS型号
    // MemsetBuff(s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo.acBmsFacSn, tBmsFactInfo.acBmsFacSn, (BYTE)20, (BYTE)20, 0x20); //BMS序列号
    // MemsetBuff(s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo.acBMSSysName, s_tSysPara.acBMSSysName, (BYTE)20, (BYTE)20, 0x20); //BMS系统名称
    // MemsetBuff(s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo.acBootDate, (BYTE *)BOOT_VER_START, (BYTE)20, (BYTE)20, 0x20); //BOOT日期
    // MemsetBuff(s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo.acBMSVersion, BMS_VER, (BYTE)20, (BYTE)20, 0x20); //BMS软件版本

    // rt_memset(str_tmp, 0, 20);
    // rt_sprintf((char*)str_tmp,"%d-%d-%d",SOFTWARE_RELEASE_YEAR,SOFTWARE_RELEASE_MONTH,SOFTWARE_RELEASE_DATE);
    // MemsetBuff(s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo.acBMSSoftDate, str_tmp, (BYTE)20, (BYTE)20, 0x20); //BMS软件发布日期

    // MemsetBuff(s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo.acBDUSoftVer, tDcFactory.acSoftVer, (BYTE)6, (BYTE)6, 0x20); //BDU软件版本
    // rt_memset(str_tmp, 0, 20);
    // rt_snprintf((char*)str_tmp,sizeof(str_tmp),"%d-%d-%d",(tDcFactory.acVerDate[0]<<8)+(tDcFactory.acVerDate[1]),tDcFactory.acVerDate[2],tDcFactory.acVerDate[3]);
    // MemsetBuff(s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo.acBDUSoftDate, str_tmp, (BYTE)20, (BYTE)20, 0x20); //BDU版本日期

    // MemsetBuff(s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo.acCellManufactruer, tPackInfo.acCellManufactruer, (BYTE)20, (BYTE)20, 0x20); //电芯厂家名称

    // rt_memset(str_tmp, 0, 20);
    // rt_sprintf((char*)str_tmp,"%d-%d-%d",tPackInfo.tBattDate.wYear,tPackInfo.tBattDate.ucMonth,tPackInfo.tBattDate.ucDay);
    // MemsetBuff(s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo.acPackDataEnable, str_tmp, (BYTE)20, (BYTE)20, 0x20); //电芯出厂日期

    // MemsetBuff(s_atSnmpData[ucSrcAddr-1].tSNMPFactoryInfo.acIMEISn, tIMEI.acIMEI, (BYTE)20, (BYTE)20, 0x20); //IMEI

    //////测试代码///////
    // BYTE aaa[500];
    // WORD ttt = CollectFactInfoForSNMP(&aaa[4]);
    // aaa[2] = (ttt>>8)&0xFF;
    // aaa[3] = ttt&0xFF;
    // SetSNMPFactInfo(&aaa[2], 30);

    return TRUE;
}


static BOOLEAN SyncSelfData_Para(void)
{
    BYTE *pStart = NULL;
    BYTE *pPeakShiftStart = NULL;
    BYTE i;
    BYTE str_tmp[SNMP_STRING_NUM];

    GetSysPara(&s_tSysPara);
    GetPeakShiftPara(&s_tPeakShiftPara);
    pStart = (BYTE*)(&s_tSysPara);
    pPeakShiftStart = (BYTE*)(&s_tPeakShiftPara);

    for(i=0; i<SNMP_PARA_NUM; i++)
    {
        if(s_ptSnmpSyncPara[i].ucDataType == DATA_TYPE_INT8U)
        {
              if(s_ptSnmpSyncPara[i].ucParaType == 1)
            {
                s_tSnmpPara.aiPara[i] = (INT32)((*(BYTE*)(pPeakShiftStart + s_ptSnmpSyncPara[i].wLocalParaDataOffset))*Pow10(s_ptSnmpSyncPara[i].ucPrecision));
            }
            else
            {
                s_tSnmpPara.aiPara[i] = (INT32)((*(BYTE*)(pStart + s_ptSnmpSyncPara[i].wLocalParaDataOffset))*Pow10(s_ptSnmpSyncPara[i].ucPrecision));
            }
        }
        else if(s_ptSnmpSyncPara[i].ucDataType == DATA_TYPE_INT16U)
        {
            s_tSnmpPara.aiPara[i] = (INT32)((*(WORD*)(pStart + s_ptSnmpSyncPara[i].wLocalParaDataOffset))*Pow10(s_ptSnmpSyncPara[i].ucPrecision));
        }
        else if(s_ptSnmpSyncPara[i].ucDataType == DATA_TYPE_FP32)
        {
            s_tSnmpPara.aiPara[i] = FLOAT_TO_INT32((*(FLOAT*)(pStart + s_ptSnmpSyncPara[i].wLocalParaDataOffset))*Pow10(s_ptSnmpSyncPara[i].ucPrecision));
        }
    }

    for(i=0; i<SNMP_PARA_STR_NUM; i++)
    {
        if(s_ptSnmpSyncParaStr[i].ucDataType == DATA_TYPE_CHAR)
        {
            if(s_ptSnmpSyncParaStr[i].ucParaType == 0)
            {
                rt_memcpy_s(&s_tSnmpPara.aucParaStr[i*SNMP_OTHER_STRING_NUM], s_ptSnmpSyncParaStr[i].ucLen, pStart + s_ptSnmpSyncParaStr[i].wOffset, s_ptSnmpSyncParaStr[i].ucLen);
            }       
        }
        else if(s_ptSnmpSyncParaStr[i].ucDataType == DATA_TYPE_INT16U)
        {
            WORD wTmp = *(WORD*)(pStart + s_ptSnmpSyncParaStr[i].wOffset);
            rt_snprintf((char*)str_tmp,sizeof(str_tmp),"%d",wTmp);
            rt_memcpy_s(&s_tSnmpPara.aucParaStr[i*SNMP_OTHER_STRING_NUM], s_ptSnmpSyncParaStr[i].ucLen, str_tmp, s_ptSnmpSyncParaStr[i].ucLen);
        }
    }

    return TRUE;
}

static BOOLEAN SyncSelfData(void)
{
    //获取实时数据
    SyncSelfData_RealData();

    //获取实时告警
    SyncSelfData_RealAlarm();

    //获取厂家信息
    SyncSelfData_FactInfo();

    //获取参数信息
    SyncSelfData_Para();

    return TRUE;
}


BOOLEAN SnmpSetPara(BYTE ucOffset, INT32 iData, BOOLEAN bSetFlag)
{
    BYTE *pStart = NULL;
    BYTE  *pPeakShiftStart = NULL;
    BYTE i = 0;
    BYTE ucPeakShiftParaID = 0;
    BYTE ucPeakShiftParaOffset[] ={OFFSET_PEAK_SHIFT_ENABLE,OFFSET_PEAK_SHIFT_DOD,OFFSET_POWER_OFF_DELAY};  //错峰参数在结构体中的偏移
    BYTE ucPeakShiftParaCommandID[] = {0x80,0x81,0x88};  //下标和错峰参数偏移的下标一一对应 
    T_PeakShiftPara tPeakShiftPara;

    pStart = (BYTE*)(&s_tSysPara);
    GetPeakShiftPara(&tPeakShiftPara);
    pPeakShiftStart = (BYTE*)(&tPeakShiftPara);

    if(s_ptSnmpSyncPara[ucOffset].ucDataType == DATA_TYPE_INT8U)
    {
        if(s_ptSnmpSyncPara[ucOffset].ucParaType == 1)
        {
            *(BYTE*)(pPeakShiftStart + s_ptSnmpSyncPara[ucOffset].wLocalParaDataOffset) = iData / Pow10(s_ptSnmpSyncPara[ucOffset].ucPrecision);
        }
        else{
            *(BYTE*)(pStart + s_ptSnmpSyncPara[ucOffset].wLocalParaDataOffset) = iData / Pow10(s_ptSnmpSyncPara[ucOffset].ucPrecision);
        }
    }
    else if(s_ptSnmpSyncPara[ucOffset].ucDataType == DATA_TYPE_INT16U)
    {
        *(SHORT*)(pStart + s_ptSnmpSyncPara[ucOffset].wLocalParaDataOffset) = iData / Pow10(s_ptSnmpSyncPara[ucOffset].ucPrecision);
    }
    else if(s_ptSnmpSyncPara[ucOffset].ucDataType == DATA_TYPE_FP32)
    {
        *(FLOAT*)(pStart + s_ptSnmpSyncPara[ucOffset].wLocalParaDataOffset) = (FLOAT)iData / Pow10(s_ptSnmpSyncPara[ucOffset].ucPrecision);
    }

    if(s_ptSnmpSyncPara[ucOffset].ucParaType == 1)
    {
        for(i = 0;i< sizeof(ucPeakShiftParaOffset)/sizeof(ucPeakShiftParaOffset[0]);i++)
        {
            if(s_ptSnmpSyncPara[ucOffset].wLocalParaDataOffset == ucPeakShiftParaOffset[i])
            {
                ucPeakShiftParaID = ucPeakShiftParaCommandID[i];
            }
        }
        if(SetPeakShiftPara(&tPeakShiftPara, ucPeakShiftParaID) == False)
        {
            return FALSE;
        }
    }
    else if(SetSysPara(&s_tSysPara, True, CHANGE_BY_SNMP) == False)
    {
        return FALSE;
    }

    //同步参数
    if(bSetFlag == TRUE)
    {
        SnmpSetParaToOtherBatt(0xF0, ucOffset, (BYTE *)&iData, sizeof(INT32));
    } 
    return TRUE;
}



BOOLEAN SnmpSetParaStr(BYTE ucOffset, BYTE* pData, BOOLEAN bSetFlag)
{
    BYTE *pStart = NULL;
    BYTE *pNetAntiTheftInfo = NULL;

    pStart = (BYTE*)(&s_tSysPara);
    pNetAntiTheftInfo = (BYTE*)(&s_tSnmpNetAntiTheftInfo);

    if(s_ptSnmpSyncParaStr[ucOffset].ucDataType == DATA_TYPE_CHAR)
    {
        if(s_ptSnmpSyncParaStr[ucOffset].ucParaType == 1) //网管防盗参数
        {
            rt_memcpy_s((pNetAntiTheftInfo + s_ptSnmpSyncParaStr[ucOffset].wOffset), s_ptSnmpSyncParaStr[ucOffset].ucLen, pData, s_ptSnmpSyncParaStr[ucOffset].ucLen);
            CtrlSnmpNetAntiTheft(ucOffset);
        }
        else
        {
            rt_memcpy_s((pStart + s_ptSnmpSyncParaStr[ucOffset].wOffset), s_ptSnmpSyncParaStr[ucOffset].ucLen, pData, s_ptSnmpSyncParaStr[ucOffset].ucLen); 
        }
    }
    else if (s_ptSnmpSyncParaStr[ucOffset].ucDataType == DATA_TYPE_INT8U)
    {
        UINT32 value = 0;
        if (!ParseUint32(&value, (char *)pData) || value > UINT8_MAX)
        {
            return FALSE;
        }
        *(BYTE *)(pStart + s_ptSnmpSyncParaStr[ucOffset].wOffset) = value;
    }
    else if(s_ptSnmpSyncParaStr[ucOffset].ucDataType == DATA_TYPE_INT16U)
    {
        UINT32 value = 0;
        if (!ParseUint32(&value, (char *)pData) || value > UINT16_MAX)
        {
            return FALSE;
        }
        *(WORD *)(pStart + s_ptSnmpSyncParaStr[ucOffset].wOffset) = value;
    }

    if(SetSysPara(&s_tSysPara, True, CHANGE_BY_SNMP) == False)
    {
        return FALSE;
    }

    //同步参数
    if(s_ptSnmpSyncParaStr[ucOffset].bIsSync && (bSetFlag == TRUE))
    {
        SnmpSetParaToOtherBatt(0xF1, ucOffset, pData, SNMP_OTHER_STRING_NUM);
    }

    return TRUE;
}


/*****************************************SNMP 设置指令 **********************************************/
BOOLEAN SnmpSetParaFromBatt(BYTE *p)
{
    BYTE ucOffset = 0;
    INT32 iData = 0;
    
    if(p == NULL)
    {
        return FALSE;
    }

    ucOffset = p[0];
    iData = *(INT32 *)(p+1);

    SnmpSetPara(ucOffset, iData, FALSE);

    return TRUE;
}

BOOLEAN SnmpSetParaStrFromBatt(BYTE *p)
{
    BYTE ucOffset = 0;
    BYTE aucTemp[SNMP_OTHER_STRING_NUM] = {0};
    
    if(p == NULL)
    {
        return FALSE;
    }
    
    ucOffset = p[0];

    rt_memcpy_s(aucTemp, SNMP_OTHER_STRING_NUM, p+1, SNMP_OTHER_STRING_NUM);

    SnmpSetParaStr(ucOffset, aucTemp, FALSE);

    return TRUE;
}


/**
 * @brief SNMP控制函数，处理SNMP控制命令。
 *
 * 该函数接收一个SNMP控制参数结构体指针，并根据其中的地址和命令ID执行相应的控制函数。
 * 
 * @param ptCtrl 指向SNMP控制参数结构体的指针。
 * @return BOOLEAN 如果处理成功返回TRUE，否则返回FALSE。
 */
BOOLEAN SnmpCtrl(T_SnmpCtrlPara *ptCtrl)
{
    // 检查输入参数是否为NULL
    if (ptCtrl == NULL)
    {
        return FALSE;
    }

    // 检查地址是否超出有效范围
    if (ptCtrl->ucAddr > SLAVE_NUM)
    {
        return FALSE;
    }

    // 遍历所有注册的SNMP控制命令
    for (size_t i = 0; i < SNMP_CTRL_CMD_NUM; i++)
    {
        // 检查命令ID是否匹配且对应的控制函数是否有效
        if (s_ptSnmpCtrlCmd[i].ucId == ptCtrl->ucCmd + 1 && s_ptSnmpCtrlCmd[i].pSnmpCtrlFunc != NULL)
        {
            // 调用对应的控制函数并返回成功
            s_ptSnmpCtrlCmd[i].pSnmpCtrlFunc(ptCtrl);
            return TRUE;
        }
    }

    // 如果没有找到匹配的命令或控制函数无效，返回失败
    return FALSE;
}


BOOLEAN SnmpCtrlDefPara(void* para)
{
    INT32* p = para;

    if(p == NULL)
    {
        return FALSE;
    }
    if(p[2] == 0)           //指令是0不执行，非0执行
    {
        return FALSE;
    }

    LoadPara();
    SaveAction(GetActionId(CONTOL_RST_PARA), "SNMP Default Para");

    if(p[3] == TRUE)        //是否发送给其他电池
    {
        SendSnmpCtrl(p[0], p[1], p[2]);
    }

    return TRUE;
}


BOOLEAN SnmpCtrlStartNetUpdateDL(void* para)
{
    INT32* p = para;

    if(p == NULL)
    {
        return FALSE;
    }
    if(p[2] == 0)           //指令是0不执行，非0执行
    {
        return FALSE;
    }

    StartNetUpdateDL();

    return TRUE;
}


static BOOLEAN DeviceStatisticClearData(void)
{
    BYTE i = 0;
    BYTE ucAddr = GetBMSAddr();
    
    for (i = 0; i < SLAVE_NUM; i++)
    {
        if (i == ucAddr - 1)
        {
            continue; // 跳过当前设备地址，而不是直接跳出循环
        }
      
        InitSNMPData(i);       
    }
    // 删除电池在位信息记录文件
    ClearBattExistInfo();

    return TRUE;	
}



BOOLEAN SnmpCtrlDeviceStatistic(void* para)
{
    INT32* p = para;

    if(p == NULL)
    {
        return FALSE;
    }
    if(p[2] == 0)           //指令是0不执行，非0执行
    {
        return FALSE;
    }

    //清除实时数据
    DeviceStatisticClearData();
    return TRUE;
}


BOOLEAN SnmpCtrlReset(void* para)
{
    INT32* p = para;
    BYTE ucSrcAddr = 0;

    ucSrcAddr = GetBMSAddr();

    if(ucSrcAddr == 0 || ucSrcAddr > SLAVE_NUM)
    {
        return 0;
    }

    if(p == NULL)
    {
        return FALSE;
    }
    if(p[2] == 0)               //指令是0不执行，非0执行
    {
        return FALSE;
    }

    if(p[0] == ucSrcAddr)
    {
        ResetMCU(NO_RESET_REMOTE_CTRL);
        return TRUE;
    }

    if(p[3] == TRUE)            //是否发送给其他电池
    {
        SendSnmpCtrl(p[0], p[1], p[2]);
    }
    
    return TRUE;
}

void* getData(BYTE ucAddr)
{
    if(ucAddr >= SLAVE_NUM)
    {
        return NULL;
    }
    return (void *)&s_atSnmpData[ucAddr].aiRealData[0];
}

void* getAlarm(BYTE ucAddr)
{
    if(ucAddr >= SLAVE_NUM)
    {
        return NULL;
    }
    return (void *)&s_atSnmpData[ucAddr].aiRealAlarm[0];
}

void* getFact(BYTE ucAddr)
{
    if(ucAddr >= SLAVE_NUM)
    {
        return NULL;
    }
    return (void *)&s_atSnmpData[ucAddr].tSNMPFactoryInfo;
}

void* getPara(BYTE ucAddr)
{
    if(ucAddr >= SLAVE_NUM)
    {
        return NULL;
    }
    return (void *)&s_tSnmpPara.aiPara[0];
}

void* getParaStr(BYTE ucAddr)
{
    if(ucAddr >= SLAVE_NUM)
    {
        return NULL;
    }
    return (void *)&s_tSnmpPara.aucParaStr[0];
}


BOOLEAN BattStatusCommFail(BYTE ucAddr)
{
    if(ucAddr >= SLAVE_NUM)
    {
        return FALSE;
    }

    s_atSnmpData[ucAddr].aiRealData[SNMP_ID_REALDATA_BATTEXISTSTATUS - 1] = BATT_STATUS_COMM_FAIL;

    return TRUE;
}


// 主要用于开机启动检测

BOOLEAN SnmpCommFailCheck(BYTE ucAddr)
{
    if(s_ucExistSlave[ucAddr] == BATT_STATUS_EXIST && s_atSnmpData[ucAddr].aiRealData[SNMP_ID_REALDATA_BATTEXISTSTATUS - 1] == BATT_STATUS_EXIST)
    {
        s_atSnmpData[ucAddr].aiRealData[SNMP_ID_REALDATA_BATTEXISTSTATUS - 1] = BATT_STATUS_COMM_FAIL;
    }
    
    return True;
}


// 检测在位电池是否有新增，有新增则更新在位信息

BOOLEAN BattExistCheckSave(void)
{
    if(rt_memcmp(s_ucExistSlave, s_ucExistSlaveBak, sizeof(s_ucExistSlaveBak)))
    {
        rt_memcpy_s(s_ucExistSlaveBak,sizeof(s_ucExistSlaveBak),s_ucExistSlave,sizeof(s_ucExistSlaveBak));
        // 保存在位电池信息到文件
        writeFile(FILE_NAME_BATT_EXIST,s_ucExistSlaveBak,sizeof(s_ucExistSlaveBak));
        return True;
    }

    return False;
}



BOOLEAN InitExistBattInfo(void)
{
    if(!readFile(FILE_NAME_BATT_EXIST, s_ucExistSlaveBak, sizeof(s_ucExistSlaveBak)))
    {
        rt_memset_s(s_ucExistSlaveBak,sizeof(s_ucExistSlaveBak),0x00,sizeof(s_ucExistSlaveBak));
        rt_memset_s(s_ucExistSlave,sizeof(s_ucExistSlave),0x00,sizeof(s_ucExistSlave));
        return False;
    }
    else
    {
        rt_memcpy_s(s_ucExistSlave,sizeof(s_ucExistSlave),s_ucExistSlaveBak,sizeof(s_ucExistSlave));
    }

    return True;
}



static BOOLEAN  BattExistInfoToRealData(void)
{
    BYTE i = 0;

    for(i = 0;i<SLAVE_NUM;i++)
    {
        s_atSnmpData[i].aiRealData[SNMP_ID_REALDATA_BATTEXISTSTATUS - 1] = s_ucExistSlave[i];
    }

    return True;
}



static BOOLEAN ClearBattExistInfo(void)
{
    deleteFile(FILE_NAME_BATT_EXIST);
    rt_memset_s(s_ucExistSlaveBak, sizeof(s_ucExistSlaveBak), 0x00, sizeof(s_ucExistSlaveBak));
    rt_memset_s(s_ucExistSlave, sizeof(s_ucExistSlave), 0x00, sizeof(s_ucExistSlave));
    return True;
}





/***************************************************************************
 * @brief    网管防盗-开启
 **************************************************************************/
Static BYTE StartNetAntiTheftSNMP(BYTE *pucNetAntiTheftKey)
{
    if (GetNetAntiTheftStatus() == NETANTITHEFT_ON)
    {
        return NET_RESPONSE_ALREADY;
    }

    return DealNetAntiTheftStart(pucNetAntiTheftKey);
}



/***************************************************************************
 * @brief    网管防盗-心跳
 **************************************************************************/
Static BYTE HeartNetAntiTheftSNMP(BYTE *pucNetAntiTheftKey)
{
    BYTE aucNetAntiTheftSN[NETANTITHEFT_SN_LEN] = {0};
    if (GetNetAntiTheftStatus() == NETANTITHEFT_OFF)
    {
        return NET_RESPONSE_NOT_BIND;
    }

    return DealNetAntiTheftHeartbeat(pucNetAntiTheftKey, aucNetAntiTheftSN);
}



/***************************************************************************
 * @brief    网管防盗-关闭
 **************************************************************************/
Static BYTE EndNetAntiTheftSNMP(BYTE *pucNetAntiTheftKey)
{
    if (GetNetAntiTheftStatus() == NETANTITHEFT_OFF)
    {
        return NET_RESPONSE_ALREADY;
    }

    return DealNetAntiTheftEnd(pucNetAntiTheftKey);
}



Static BOOLEAN CtrlSnmpNetAntiTheft(BYTE uOffset)
{
    BYTE ret = FAILURE;
    BYTE ucSnmpVer;
    T_BCMDataStruct tBcmSNMPData = {0,};
    GetRealData(&tBcmSNMPData);
    ucSnmpVer = snmp_get_version();
    if (ucSnmpVer != 2) 
    {
        return FALSE;
    }

    switch (uOffset) 
    {
        case SNMP_NETANTITHEFT_START:
                ret = StartNetAntiTheftSNMP(s_tSnmpNetAntiTheftInfo.aucAntiThefDefenceOn);
                if(ret == SUCCESSFUL)
                {
                    SaveAction(GetActionId(CTRL_NET_ANTI_THEFT), "SNMPNetDefence On");
                }
            break;
        case SNMP_NETANTITHEFT_HEARTBEAT:
                HeartNetAntiTheftSNMP(s_tSnmpNetAntiTheftInfo.aucAntiThefHeartbeat);
            break;
        case SNMP_NETANTITHEFT_END:
                ret = EndNetAntiTheftSNMP(s_tSnmpNetAntiTheftInfo.aucAntiThefDefenceOff);
                if(ret == SUCCESSFUL)
                {
                    SaveAction(GetActionId(CTRL_NET_ANTI_THEFT), "SNMPNetDefence Off");
                }
            break;
        case SNMP_NETANTITHEFT_OLDKEY:
            // 更换电子钥匙
            tBcmSNMPData.bNetAntiThefChgKeySts = NetAntiThefOldtKeySNMP(s_tSnmpNetAntiTheftInfo.aucAntiThefOldKey);
            break;
        case SNMP_NETANTITHEFT_CHANGEKEY:
            // 更换电子钥匙
            tBcmSNMPData.bNetAntiThefChgKeySts = ChangeNetAntiTheftKeySNMP(s_tSnmpNetAntiTheftInfo.aucAntiThefChangeKey);
            break;
        default:
            return FALSE;
            break;
        }
        SetRealData(&tBcmSNMPData);
        return TRUE;
}


/***************************************************************************
 * @brief    网管防盗-旧的电子钥匙匹配
 ***************************************************************************/
Static BYTE NetAntiThefOldtKeySNMP(BYTE* pucNetAntiTheftKey)
{
    BYTE ucRet;
    sample_tick = rt_tick_get();    
    if(JudgeNetAntiTheftKey(pucNetAntiTheftKey) != 0)
    {
        ucRet = NET_RESPONSE_FAILURE_BY_KEY; 
        s_ucNetChangeKeyFlag = 0;
    }
    else
    {
        ucRet = NET_RESPONSE_SAME_KEY_OLD;
        s_ucNetChangeKeyFlag = 1;
        
    }
    return ucRet;
}




/***************************************************************************
 * @brief    网管防盗-更换电子钥匙
 **************************************************************************/
Static BYTE ChangeNetAntiTheftKeySNMP(BYTE* pucNetAntiTheftKey)
{
    BYTE ucResponseData = 0;
    sample_tick_pass = rt_tick_get() - sample_tick;
    
    if(GetNetAntiTheftStatus() != NETANTITHEFT_ON)
    {
        return NET_ANTITHEFT_NOT_BIND; //0x82
    }
    else if(JudgeNetAntiTheftKey(pucNetAntiTheftKey) == 0)
    {
        return NET_RESPONSE_SAME_KEY_NEW;
    }
    
    /*当旧的电子钥匙匹配上之后置s_ucNetChangeKeyFlag为1，等代1min，1min内收到若新的电子钥匙则更新；
    没有收到需要重新发送旧的电子钥匙*/
    if (s_ucNetChangeKeyFlag == 1) 
    {
        if (sample_tick_pass >  ONE_MINUTES_SECOND * RT_TICK_PER_SECOND)
        { 
            s_ucNetChangeKeyFlag = 0;
            ucResponseData = NET_RESPONSE_TIMEOUT;
        }
        else
        {
            ucResponseData = WriteNetAntiTheftInfo(NETANTITHEFT_ON, pucNetAntiTheftKey);
            s_ucNetChangeKeyFlag = 0;
        }
    }
    
    if(ucResponseData == SUCCESSFUL)
    {
        SaveAction(GetActionId(CTRL_NET_ANTI_THEFT), "SNMPChangeNetDeKey");
    }
    return ucResponseData;
}



/***************************************************************************
 * @brief    网管防盗-对电子钥匙进行sha256加密
 **************************************************************************/
BOOLEAN NetAntiTheftKeySha256(T_BCMDataStruct *tBcmSNMPData)
{
    mbedtls_sha256_context sha256;
    unsigned char output[32];
    mbedtls_sha256_init(&sha256);
    mbedtls_sha256_starts(&sha256, 0);
    mbedtls_sha256_update(&sha256, (unsigned char *)tBcmSNMPData->ucNetAntitheftKey, 32);
    mbedtls_sha256_finish(&sha256, output);
    mbedtls_sha256_free(&sha256);
    rt_memcpy_s(&tBcmSNMPData->ulNetAntiThefGetKey1, 4, output, 4);
    rt_memcpy_s(&tBcmSNMPData->ulNetAntiThefGetKey2, 4, &output[4], 4);
    rt_memcpy_s(&tBcmSNMPData->ulNetAntiThefGetKey3, 4, &output[8], 4);
    rt_memcpy_s(&tBcmSNMPData->ulNetAntiThefGetKey4, 4, &output[12], 4);
    rt_memcpy_s(&tBcmSNMPData->ulNetAntiThefGetKey5, 4, &output[16], 4);
    rt_memcpy_s(&tBcmSNMPData->ulNetAntiThefGetKey6, 4, &output[20], 4);
    rt_memcpy_s(&tBcmSNMPData->ulNetAntiThefGetKey7, 4, &output[24], 4);
    rt_memcpy_s(&tBcmSNMPData->ulNetAntiThefGetKey8, 4, &output[28], 4);
    
    return TRUE;
}

