/**
 * @brief 备份管理头文件
 * 
 */

#ifndef _BACKUP_MANAGE_H_
#define _BACKUP_MANAGE_H_

//升级或备份文件记录数据
#define    FILE_NAME_LEN    40
#define    UPDATE_FILE_EXTEND_NAME_LEN 256
#define    FILE_TIME_LEN    20
#define    FLAG_IAP         0x55    //待升级
#define    FLAG_CAN_IAP     0x56    //待升级CAN
#define    FLAG_APP         0x66    //0xAA 已烧写    //升级结束
#define    FLAG_BACKUP      0x88    //运行正常待备份
#define    FLAG_OK          0x99    //已备份


//升级或备份文件记录管理
typedef struct
{
    unsigned short    data_lenth_per_frame;
    unsigned short    total_frame_num;
    unsigned int      total_file_length;
    char     file_name[FILE_NAME_LEN];
    char     file_time[FILE_TIME_LEN];
    unsigned short    file_check;
    unsigned short    resv;
}file_attr_t;

typedef struct
{
    file_attr_t     file_attr;
    unsigned char   flag;
    unsigned char   update_addr;
    unsigned short  conunter;
    unsigned short  backup_frame_num;
    unsigned short  crc;
}file_manage_t;

void begin_download(unsigned char dl_mode);

void reset_mcu(unsigned char uc_type);

#endif      // _BACKUP_MANAGE_H_
