#ifndef SOUTH_EBM_MODBUS_H_
#define SOUTH_EBM_MODBUS_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "device_type.h"
#include "cmd.h"
#include "linklayer.h"
#include "protocol_layer.h"

#define MAX_FAN_NUM 16
#define MAX_VFD_NUM 4
#define SIG_FAN_MAX_NUM     8 //单风扇最多数量



typedef enum {
    SOUTH_FAN_TYPE = 0,   // 风机类型
    SOUTH_VFD_TYPE,       // 变频器类型
    MAX_SOUTH_TYPE        // 类型总数（自动计算为2）
} south_dev_type_e;

typedef enum {
    // 风机品牌（从0开始）
    FAN_SLB_BRAND = 0,
    FAN_EBM_BRAND,
    FAN_FSD_BRAND,
    FAN_AVC_BRAND,
    // 变频器品牌（从0开始）
    VFD_AMS_BRAND = 0,
    VFD_DFS_BRAND,
    VFD_HC_BRAND,
    MAX_SOUTH_BRAND
} south_dev_brand_e;




#define R_BUFF_LEN  2048
#define S_BUFF_LEN  512

// 功能码ID
#define READ_HOLD_REG        0x03     // 读保持寄存器
#define READ_INPUT_REG       0x04     // 读输入寄存器
#define WRITE_SINGLE_REG     0x06     // 写单个寄存器
#define WRITE_MULTI_REG      0x10     // 写多个寄存器

// EBM（1-100）
#define READ_EBM_HOLD_REG        1    // 读取ebm 保持寄存器
#define READ_EBM_INPUT_REG       2    // 读取ebm 输入寄存器
#define WRITE_EBM_SINGLE_REG     3
#define WRITE_EBM_MULTI_REG      4
#define EBM_SET_WIND_SPEED_CTRL  5    // ebm 风速控制
#define EBM_SET_ADDR             6    // ebm 本机地址
#define EBM_SET_ACCE_TIME        7    // ebm 加速时间
#define EBM_SET_DECE_TIME        8    // ebm 减速时间
#define EBM_SET_CODE1            9    // ebm 关键字1
#define EBM_SET_CODE2            10   // ebm 关键字2
#define EBM_SET_CODE3            11   // ebm 关键字3
#define EBM_SET_VALUE_SOURCE     12   // ebm 设置值来源
#define EBM_SET_EMERGY_MODE      13   // ebm 紧急模式
#define EBM_SET_RESET_EFFECT     14   // ebm 重置生效
#define EBM_SET_SAVE_SET_VALUE   15   // ebm 设置值保存
#define EBM_SET_ALL_TIME         16   // ebm 设置加速时间和减速时间
#define EBM_GET_ALL_TIME         17   // ebm 获取加速时间和减速时间

// AVC（101-200）
#define READ_AVC_INPUT_REG       101   // 读取AVC 输入寄存器
#define AVC_SET_FAN_SWITCH       102   // 控制风扇的启停
#define AVC_SET_SPEED_CTRL       103   // 风速控制
#define AVC_GET_FAN_SWITCH       104   // 控制风扇的启停
#define AVC_GET_SPEED_CTRL       105   // 风速控制

// 施乐百（201-300）
#define READ_SLB_INPUT_REG       201   // 读取slb 输入寄存器
#define SLB_SET_FAN_SWITCH       202   // 控制风扇的启停
#define SLB_SET_SPEED_CTRL       203   // 风速控制
#define SLB_SET_SPEED_CTRL_MODE  204   // 风速控制模式
#define SLB_SET_COMM_FAIL_SPEED  205   // 通讯断后固定转速
#define SLB_SET_FAN_SPEED_MIN    206   // 设置风机的最小转速
#define SLB_SET_FAN_SPEED_MAX    207   // 设置风机的最大转速
#define SLB_SET_WATCH_DOG_CTRL   208   // 看门狗信号控制
#define SLB_SET_CHANGE_TIME      209   // 变动时间
#define SLB_GET_FAN_SWITCH       210   // 控制风扇的启停
#define SLB_GET_SPEED_CTRL       211   // 风速控制
#define SLB_GET_SPEED_CTRL_MODE  212   // 风速控制模式
#define SLB_GET_COMM_FAIL_SPEED  213   // 通讯断后固定转速
#define SLB_GET_FAN_SPEED_MIN    214   // 设置风机的最小转速
#define SLB_GET_FAN_SPEED_MAX    215   // 设置风机的最大转速
#define SLB_GET_WATCH_DOG_CTRL   216   // 看门狗信号控制
#define SLB_GET_CHANGE_TIME      217   // 变动时间

// 泛仕达（301-400）
#define READ_FSD_HOLD_REG        301   // 读取fsd保持寄存器
#define READ_FSD_INPUT_REG       302   // 读取fsd输入寄存器
#define FSD_SET_WIND_SPEED_CTRL  303   // fsd 风速控制
#define FSD_SET_ADDR             304   // fsd 本机地址
#define FSD_SET_VALUE_SOURCE     305   // fsd 设定值来源
#define FSD_GET_VALUE_SOURCE     306   // fsd 获取值来源
#define READ_FSD_INPUT_REG2      307   // 读取fsd输入寄存器2
#define READ_FSD_INPUT_REG3      308   // 读取fsd输入寄存器3

// 丹弗斯（501-600）
#define READ_DFS_HOLD_REG        501   // 读取dfs保持寄存器
#define READ_DFS_INPUT_REG       502   // 读取dfs输入寄存器
#define DFS_RUN_TIME_03          503   // 获取累计运行时间 03
#define DFS_RUN_TIME_04          504   // 获取累计运行时间 04
#define DFS_SET_RUN_STATUS_03    505   // 获取运行状态（设定值）03
#define DFS_SET_RUN_STATUS_04    506   // 获取运行状态（设定值）04
#define DFS_REAL_RUN_STATUS_03   507   // 获取运行状态（实时值）03
#define DFS_REAL_RUN_STATUS_04   508   // 获取运行状态（实时值）04
#define DFS_START_FRE_HOLD_TIME  509   // 获取启动频率维持时间 03
#define DFS_START_FRE            510   // 获取启动频率 03
#define DFS_FIRST_ACCE_TIME      511   // 获取第一加速时间 03
#define DFS_SET_FRE              512   // 频率设置
#define DFS_SET_SWITCH           513   // 开关机控制
#define DFS_SET_STARTFRE_HOLDTIME 514   // 设置启动频率维持时间
#define DFS_SET_START_FRE        515   // 设置启动频率
#define DFS_SET_FIRST_ACCE_TIME  516   // 设置第一加速时间

// 艾默生（601-700）设置命令汇川复用
#define READ_AMS_HOLD_REG           601   // 读取ams保持寄存器
#define READ_AMS_HOLD_REG2          602   // 读取ams保持寄存器
#define AMS_HC_SET_SWITCH           603   // 开关机控制
#define AMS_HC_SET_TARGET_SPEED     604   // 设置目标转速
#define AMS_HC_SET_FAULT_CLEAN      605   // 故障清除

// 汇川（701-800）
#define READ_HC_RUN_SPEED_REG       701   // 读取汇川压缩机运行转速
#define READ_HC_VFD_CURR_REG        702   // 读取汇川变频板电流
#define READ_HC_VFD_TEMP_REG        703   // 读取汇川变频板温度
#define READ_HC_HOLD_REG            704   // 读取hc保持寄存器
#define HC_SET_FIRST_ACCE_TIME      705   // 设置第一加速时间

// SOUTH_MOCK
#define READ_MOCK_HOLD_REG        801   // 读取保持寄存器

#define NO_MATCH_REGISTER_ADDR -1

#define CMD_DXCB_FAIL_MAX     2     // DXCB命令超时次数
#define CMD_DXCB_TIMEOUT      600   // DXCB命令超时时间

typedef struct{
    unsigned short cmd_id;
    unsigned char func_code;
    unsigned short register_addr;
    unsigned short register_num;
}cmd_id_to_register_info_t;

typedef struct{
    unsigned short cmd_id;
    unsigned short ctrl_status;
}ctrl_set_cmd_modbus_t;

dev_type_t* init_vfd_south();
dev_type_t* init_fan_south();
int pack_read_modbus_data(void* dev_inst, void* cmd_buff);
int pack_write_modbus_data(void* dev_inst, void* cmd_buff);
int change_dev_type_info_by_diff_brand(dev_inst_t* dev_inst, char south_dev_type, char is_inter_fan, char dev_num, cmd_t* no_poll_cmd_tab, cmd_t* poll_cmd_tab);
int parse_comm_data(void* dev_inst, void* cmd_buff);
void init_rtn_sem();
rt_sem_t get_rtn_sem();
int handle_mock_south(dev_inst_t* dev_inst, cmd_t* poll_cmd_tab);


#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif
