#include <stdio.h>
#include <rtthread.h>
#include "utils_heart_beat.h"
#include "utils_thread.h"
#include "server_id.h"
#include "utils_server.h"
#include "syswatch.h"
#include "button.h"
#include "linklayer.h"
#include "app_config.h"
#include "para_manage.h"
#include "alarm_register.h"
#include "realdata_save.h"
#include "realdata_id_in.h"
#include "balance.h"
#include "wireless.h"
#include "4g_mgr.h"
#include "comm_can.h"
#include "linklayer.h"
#include "update_manage.h"
#include "flash.h"
#include "his_record.h"
#include "storage.h"
#include "partition_table.h"
#include "log_mgr_api.h"
#include "softbus.h"
#include "utils_data_transmission.h"
#include "his_record.h"
#include "log_mgr_api.h"
#include "partition_table.h"

#define THREAD_PRIORITY 15
#define THREAD_TIMESLICE 5


#define THREAD_STACK_SIZE_NONAME   6144
#define THREAD_STACK_SIZE_ALARM    6144
#define THREAD_STACK_SIZE_BUTTON   1024
#define THREAD_STACK_SIZE_WIRELSEE 4096
#define THREAD_STACK_SIZE_CAN1     6144
#define THREAD_STACK_SIZE_LOG      4096
#define THREAD_STACK_SIZE_HISRECORD  6144

static char s_thread_stack_button[THREAD_STACK_SIZE_BUTTON] RT_SECTION(".bss");
static char s_alarm_thread_stack[THREAD_STACK_SIZE_ALARM] RT_SECTION(".bss");
static char s_balance_thread_stack[THREAD_STACK_SIZE_BUTTON] RT_SECTION(".bss");
static char s_wireless_thread_stack[THREAD_STACK_SIZE_WIRELSEE] RT_SECTION(".bss");
static char s_thread_stack_can1[THREAD_STACK_SIZE_CAN1] RT_SECTION(".bss");
static char s_thread_stack_log[THREAD_STACK_SIZE_LOG] RT_SECTION(".bss");
static char s_his_record_thread_stack[THREAD_STACK_SIZE_HISRECORD] RT_SECTION(".bss");

rt_uint8_t mempool_32[BLOCK_COUNT_32 * BLOCK_SIZE_32] RT_SECTION(".bss");
rt_uint8_t mempool_64[BLOCK_COUNT_64 * BLOCK_SIZE_64] RT_SECTION(".bss");
rt_uint8_t mempool_512[BLOCK_COUNT_512 * BLOCK_SIZE_512] RT_SECTION(".bss");

rt_uint32_t Iwd_timeout = 10;
softbus_mempool_all_t softbus_mempool_info;

// 定义链路类型表
static link_type_t link_type_tab[LINK_TYPE_MAX] = {
    { LINK_CAN, can_dev_init, can_dev_read, can_dev_write, can_dev_set },
};

// 定义链路实例表
static link_inst_t link_inst_tab[] = {
    { LINK_BMS_CAN, LINK_CAN, "can0", CAN_FRAME_DATA_LEN_8 },
};

// 定义协议处理表
static protocol_process_t protocol_process_tab[] = {
    { PROTOCOL_YD_1363, s1363_pack, s1363_parse },
    {PROTOCOL_BOTTOM_COMM, bottom_pack, bottom_parse},
};


static void init_softbus_config(void)
{
    softbus_mempool_info.mempool_32.mempool_addr = &mempool_32[0];
    softbus_mempool_info.mempool_32.mempool_size = sizeof(mempool_32);
    softbus_mempool_info.mempool_32.block_size = BLOCK_SIZE_32;

    softbus_mempool_info.mempool_64.mempool_addr = &mempool_64[0];
    softbus_mempool_info.mempool_64.mempool_size = sizeof(mempool_64);
    softbus_mempool_info.mempool_64.block_size = BLOCK_SIZE_64;

    softbus_mempool_info.mempool_512.mempool_addr = &mempool_512[0];
    softbus_mempool_info.mempool_512.mempool_size = sizeof(mempool_512);
    softbus_mempool_info.mempool_512.block_size = BLOCK_SIZE_512;
    return ;
}

static void deamon_thread(void)
{
    while (is_running(1))
    {
        check_heart_beat();
        rt_thread_delay(10);
    }
}


static server_info_t g_server_group[] = {
    {{{BUTTON_SERVER_ID,         "Button",      sizeof(s_thread_stack_button),    s_thread_stack_button,    THREAD_PRIORITY  }}, NULL,  ButtonMgr},
    {{{ALARM_MANAGE_SERVER_ID,  "alarm_manage", sizeof(s_alarm_thread_stack),     s_alarm_thread_stack,   THREAD_PRIORITY  }}, init_alarm_manage, alarm_main},
    {{{BALANCE_SERVER_ID,  "balance_manage", sizeof(s_balance_thread_stack),     s_balance_thread_stack,   THREAD_PRIORITY  }}, NULL, bal_main},
    {{{WIRELSESS_SERVER_ID,  "wireless_manage", sizeof(s_wireless_thread_stack),     s_wireless_thread_stack,   THREAD_PRIORITY  }}, net_4g_init, net_4g_thread_entry},
    {{{CANCOMM_SERVER_ID ,       "can_comm",     sizeof(s_thread_stack_can1),      s_thread_stack_can1,      THREAD_PRIORITY  }}, init_can_comm,  process_can_comm},
    {{{THREAD_LOG ,       "log_manage",     sizeof(s_thread_stack_log),      s_thread_stack_log,      THREAD_PRIORITY  }}, init_log_mgr,  log_mgr_thread_entry},
    {{{HIS_RECORD_ID ,       "his_record",     sizeof(s_his_record_thread_stack),      s_his_record_thread_stack,      THREAD_PRIORITY  }}, his_record_Init,  his_record_main},
};

int init_main(void)
{
    rt_thread_delay(1000);
    init_softbus_config();
    softbus_init(&softbus_mempool_info);
    init_crc();
    register_r552_alarm();
    init_flash_page_size(FLASH_PAGE_SIZE);
    initFileSys();
    init_dir();
    if (SUCCESSFUL != storage_init(&part_tab[0], sizeof(part_tab)/sizeof(part_info_t))) 
    {
        return FAILURE;
    }
    init_real_data_memory();
    register_4g_func();          // 注册4G相关函数
    int rtn = init_para_manage();
    if(rtn != SUCCESSFUL)
    {
        // LOG_E("%s | %d | init_para_manage fail\n" , __FUNCTION__ , __LINE__);
        return FAILURE;
    }
    register_link_type_info(link_type_tab, sizeof(link_type_tab) / sizeof(link_type_t));
    register_link_inst_tab_info(link_inst_tab, sizeof(link_inst_tab)/sizeof(link_inst_tab[0]));
    register_protocol_process_info(protocol_process_tab, sizeof(protocol_process_tab) / sizeof(protocol_process_t));
    init_his_record();
    syswatch_init();
    return SUCCESSFUL;
}


int main(void)
{
    init_main();
    create_server(&g_server_group[0], sizeof(g_server_group)/sizeof(g_server_group[0]));
    rt_thread_delay(10000);
    begin_download(FLAG_BACKUP);
    deamon_thread();
    return 0;
}
