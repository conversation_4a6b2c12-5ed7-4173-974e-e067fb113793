#include "alarm_mgr_api.h"
#include "partition_def.h"
#include "hisdata_in.h"
#include "math.h"
#include "utils_server.h"
#include "utils_heart_beat.h"
#include "utils_thread.h"
#include "utils_data_transmission.h"
#include "utils_time.h"
#include "realdata_save.h"
#include "msg_id.h"
#include "msg.h"
#include "server_id.h"
#include <rtthread.h>
#include <rtdef.h>
#include <rtdevice.h>
#include "sys/stat.h"
#include "softbus.h"
#include "data_type.h"
#include "his_record.h"
#include "hisact_id.h"
#include "log_mgr_api.h"
#include "utils_rtthread_security_func.h"
#include "thread_id.h"
#include "storage.h"


static save_record_msg_t s_record_msg;
static _rt_server_t g_his_server;
static his_data_record_info s_hisdata_record = {0, };
static int s_bit_num = 0;
static sig_alm_chg_msg_t buff[MAX_REAL_ALARM_NUM * sizeof(sig_alm_chg_msg_t)] = {0,};

void handle_r552_alm_msg(_rt_msg_t curr_msg);
Static int process_recv_his_msg(void);
void his_record_main(void* thread_data);
short pub_hisrecord_read_msg(unsigned char rec_type, unsigned short rec_num, unsigned short offset, void *buff);
Static short delete_one_record_real_alm(unsigned int alm_id);


// 定义实时数据信息表，包含各种数据ID及其属性
static realdata_info_t realdata_info_tab[] RAM_SECTION = { 
//// 历史数据版本号
   {BMS_DATA_ID_CELL_VOLT_MAX, 0x4401, 3, TYPE_INT16S, 1, },
   {BMS_DATA_ID_CELL_MAX_VOLT_CHAN, 0x4801, 0, TYPE_INT8U, 1, },
   {BMS_DATA_ID_CELL_VOLT_MIX, 0x4501, 3, TYPE_INT16S, 1, },
   {BMS_DATA_ID_CELL_MIN_VOLT_CHAN, 0x4901, 0, TYPE_INT8U, 1, },
   {BMS_DATA_ID_CELL_TEMP_MAX, 0x4601, 2, TYPE_INT16S, 1, },
   {BMS_DATA_ID_CELL_MAX_TEMP_CHAN, 0x4a01, 0, TYPE_INT8U, 1, },
   {BMS_DATA_ID_CELL_TEMP_MIN, 0x4701, 2, TYPE_INT16S, 1, },
   {BMS_DATA_ID_CELL_MIN_TEMP_CHAN, 0x4b01, 1, TYPE_INT8U, 1, },
   {BMS_DATA_ID_BATT_CURR, 0x401, 2, TYPE_INT16S, 1, },
   {BMS_DATA_ID_BATT_VOLT, 0x101, 2, TYPE_INT16S, 1, },
   {BMS_DATA_ID_EXTER_VOLT, 0x201, 2, TYPE_INT16S, 1, },
   {BMS_DATA_ID_BATT_CYCLE_TIMES, 0xa01, 0, TYPE_INT16U, 1, },
   {BMS_DATA_ID_ENV_TEMP, 0x301, 1, TYPE_INT8U, 1, },
   {BMS_DATA_ID_BATT_SOH, 0x901, 0, TYPE_INT8U, 1, },
   {BMS_DATA_ID_BATT_SOC, 0x801, 0, TYPE_INT8U, 1, },
   {BMS_DATA_ID_CELL_EQU_STA, 0xd01, 0, TYPE_INT16U, 1, },
   {BMS_DATA_ID_BOARD_TEMP, 0x3201, 1, TYPE_INT8U, 1, },
////  充电限流状态
   {BMS_DATA_ID_CHG_PROT_STA, 0xb01, 0, TYPE_BIT, 1, },
   {BMS_DATA_ID_DISCHG_PROT_STA, 0xc01, 0, TYPE_BIT, 1, },
   {BMS_DATA_ID_BATT_STATUS, 0x5f01, 0, TYPE_BIT, 4, },
   {BMS_DATA_ID_SIM_STAT, 0x4001, 0, TYPE_BIT, 1, },
   {BMS_DATA_ID_SIGNAL_QUALITY, 0x6401, 0, TYPE_INT8U, 1, },
////  GPS搜星数量
   {BMS_DATA_ID_TOTAL_CHARGE_QUANTY, 0x2401, 2, TYPE_INT32U, 1, },
   {BMS_DATA_ID_TOTAL_DISCHARGE_QUANTY, 0x2201, 2, TYPE_INT32U, 1, },
   {BMS_DATA_ID_BATTERY_CAP, 0x4301, 0, TYPE_INT16U, 1, },
////   预留4字节
};



Static his_record_new_t s_his_record[] = {
     {
        .his_record_info    = {0},
        .auth_code          = AUTH_CODE_HIS_ACTION,
        .min_save_num       = MIN_HIS_ACTION_NUM,
        .max_save_num       = MAX_HIS_ACTION_NUM,
        .data               = NULL,
        .storage_type       = ACC,
        .size_type          = FLD,
        .record_size        = sizeof(his_action_record_info),
        .info_name          = FILE_NAME_HIS_RECORD_INFO,
        .save_num_per_file  = SAVE_NUM_PER_FILE_HIS_ACTION,
        .file_num           = FILE_NUM_HIS_ACTION,
        .file_name          = {FILE_NAME_HIS_ACTION_0, FILE_NAME_HIS_ACTION_1, FILE_NAME_HIS_ACTION_2, FILE_NAME_HIS_ACTION_3, FILE_NAME_HIS_ACTION_4, FILE_NAME_HIS_ACTION_5, }
    },
    {  // 历史告警
        .his_record_info = {0},
        .auth_code = AUTH_CODE_HIS_ALARM,
        .size_type = FLD,
        .storage_type = ACC, 
        .record_size = sizeof(sig_alm_chg_msg_t),
        .data = NULL,
        .min_save_num = MIN_HIS_ALARM_NUM,
        .max_save_num = MAX_HIS_ALARM_NUM,
        .save_num_per_file = MAX_HIS_ALARM_NUM_PER_FILE,
        // .part_name =  DIR_NAME_HIS_RECORD,
        .info_name = HIS_ALARM_FILE,
        .file_num = HIS_ALARM_FILE_NUM,
        .file_name = {HIS_ALARM_FILE_0, HIS_ALARM_FILE_1, HIS_ALARM_FILE_2, HIS_ALARM_FILE_3, HIS_ALARM_FILE_4, HIS_ALARM_FILE_5},
    }, 
    { //历史数据

        .his_record_info = {0},
        .auth_code = AUTH_CODE_HIS_DATA,
        .size_type = FLD,
        .storage_type = ACC,
        .record_size = sizeof(his_data_record_info),
        .data = NULL,
        .min_save_num = MIN_HIS_DATA_NUM,
        .max_save_num = MAX_HIS_DATA_NUM,
        .save_num_per_file = MAX_HIS_DATA_NUM_PER_FILE,
        .info_name = HIS_DATA_FILE,
        .file_num = HIS_DATA_FILE_NUM,
        .file_name = {HIS_DATA_FILE_0, HIS_DATA_FILE_1, HIS_DATA_FILE_2, HIS_DATA_FILE_3, HIS_DATA_FILE_4, HIS_DATA_FILE_5},

    },
    {  // 实时告警
        .his_record_info = {0},
        .auth_code = AUTH_CODE_REAL_ALARM,
        .size_type = FLD,
        .storage_type = ACC,
        .record_size = sizeof(sig_alm_chg_msg_t),
        .data = NULL,
        .min_save_num = MIN_REAL_ALARM_NUM,
        .max_save_num = MAX_REAL_ALARM_NUM,
        .save_num_per_file = MAX_REAL_ALARM_NUM_PER_FILE,
        // .part_name =  DIR_NAME_HIS_RECORD,
        .info_name = REAL_ALARM_FILE,
        .file_num = REAL_ALARM_FILE_NUM,
        .file_name = {REAL_ALARM_FILE_0},
    },
};




static msg_map his_alm_msg_map[] =
{
    {CHECK_SIGNAL_ALARM_CHG_MSG,    msg_handle_nothing},
};
Static log_msg_handle_process_t s_alm_msg_handle[] =
{
    {CHECK_SIGNAL_ALARM_CHG_MSG,     handle_r552_alm_msg},
};



void init_his_record(void)
{
    register_his_record_tab_new(s_his_record, sizeof(s_his_record)/sizeof(his_record_new_t));
}


//目录结构初始化

void init_dir(void)
{
    int res = SUCCESSFUL;
    res = mkdir(DIR_NAME_ROOT, 0);
    if(FAILURE == res){return;}
    res = mkdir(DIR_NAME_HIS_RECORD, 0);
    if(FAILURE == res){return;}
    return;
}



void* his_record_Init(void* param)
{
    server_info_t *server_info = (server_info_t *)param;

    server_info->server.server.map_size = sizeof(his_alm_msg_map) / sizeof(msg_map);
    register_server_msg_map(his_alm_msg_map, server_info);

    return server_info;
}



void his_record_main(void* thread_data)
{
    PRINT_MSG_AND_RETURN_IF_FAIL(thread_data != NULL);
    g_his_server = _curr_server_get();
    pre_thread_beat_f(HIS_RECORD);
    while (is_running(TRUE))
    {
        thread_beat_go_on(HIS_RECORD);
        process_recv_his_msg();
        rt_thread_mdelay(1000);
    }
}




Static int process_recv_his_msg(void)
{
    int i = 0; 
    if ((g_his_server == NULL) || (rt_sem_take(&g_his_server->msg_sem, 10) != RT_EOK) || (g_his_server->msg_node == RT_NULL))
    {
        return FAILURE;
    }

    _rt_msg_t curr_msg = g_his_server->msg_node ;
    for (i = 0; i < sizeof(s_alm_msg_handle) / sizeof(s_alm_msg_handle[0]); i++)
    {
        if (s_alm_msg_handle[i].msg_id == curr_msg->msg.msg_id)
        {
            s_alm_msg_handle[i].handle(curr_msg);
        }
    }

    rt_mutex_take(&g_his_server->mutex, RT_WAITING_FOREVER);
    g_his_server->msg_node = curr_msg->next;
    g_his_server->msg_count--;
    rt_mutex_release(&g_his_server->mutex);

    softbus_free(curr_msg);
    return SUCCESSFUL;
}




void handle_r552_alm_msg(_rt_msg_t curr_msg)
{
    sig_alm_chg_msg_t *msg_info = (sig_alm_chg_msg_t *)(curr_msg->msg.data);
    s_record_msg.hisdata = msg_info;
    s_record_msg.len = sizeof(sig_alm_chg_msg_t);

    if(TRUE == msg_info->alarm_value)//实时告警
    {
        s_record_msg.auth_code = AUTH_CODE_REAL_ALARM;
        s_record_msg.record_type = RECORD_TYPE_REAL_ALARM;
    }
    else //历史告警
    {
        s_record_msg.auth_code = AUTH_CODE_HIS_ALARM;
        s_record_msg.record_type = RECORD_TYPE_HIS_ALARM;
        delete_one_record_real_alm(msg_info->alarm_id);
    }
    pub_msg_to_thread(LOG_MGR_SAVE_ONE_RECORD, &s_record_msg, sizeof(sig_alm_chg_msg_t));
    return;
}


//发布操作记录保存消息

static his_action_record_info hisact_msg = {0, };
short pub_hisaction_save_msg(unsigned short act_id, char *str)
{
    save_record_msg_t  rec_msg = {0,};

    hisact_msg.action_id = act_id;
    time(&hisact_msg.save_time);
    rt_memcpy_s(hisact_msg.msg, sizeof(hisact_msg.msg), str, sizeof(hisact_msg.msg));

    rec_msg.auth_code = AUTH_CODE_HIS_ACTION;
    rec_msg.record_type = RECORD_TYPE_HIS_ACTION;
    rec_msg.len = sizeof(his_action_record_info);
    rec_msg.hisdata = &hisact_msg;

    pub_msg_to_thread(LOG_MGR_SAVE_ONE_RECORD, &rec_msg, sizeof(save_record_msg_t));

    return SUCCESSFUL;
}


//发布操作记录读取消息，阻塞方式

short pub_hisrecord_read_msg(unsigned char rec_type, unsigned short rec_num, unsigned short offset, void *buff)
{
    rt_msg_t req_msg = NULL;
    rt_msg_t res_msg = NULL;
    read_record_msg_t rec_msg = {0, };

    rec_msg.record_type = rec_type;
    rec_msg.record_num = rec_num;
    rec_msg.offset = offset;

    switch(rec_type) {
        case RECORD_TYPE_HIS_ACTION:
            rec_msg.len = rec_num * sizeof(his_action_record_info);
            break;
        case RECORD_TYPE_HIS_ALARM:
        case RECORD_TYPE_REAL_ALARM:
            rec_msg.len = rec_num * sizeof(sig_alm_chg_msg_t);
            break;
        case RECORD_TYPE_HIS_DATA:
            rec_msg.len = rec_num * sizeof(his_data_record_info);
            break;
        default:
            return FAILURE;
    }


    req_msg = rt_msg_create(LOG_MGR_READ_RECORD, &rec_msg, rec_msg.len);
    if (RT_NULL == req_msg)
    {
        return FAILURE;
    }

    res_msg = rt_msg_create(LOG_MGR_READ_RECORD, &rec_msg, rec_msg.len);
    if (RT_NULL == res_msg)
    {
        return FAILURE;
    }

    rt_msg_send_req_block(THREAD_LOG, req_msg, res_msg, READ_HIS_ACTION_TIMEOUT);

    rt_memcpy_s(buff, rec_msg.len, res_msg->data, rec_msg.len);

    rt_msg_delete(req_msg);
    rt_msg_delete(res_msg);

    return SUCCESSFUL;
}



void pack_int32u_data(unsigned char* buff, unsigned int* offset, char precision, void* data)
{
    unsigned int ui_data = 0;
    if(0 != precision)
    {
        ui_data = (unsigned int)(*(float*)data * pow(10, precision));
    }
    else
    {
        ui_data = *(unsigned int*)data;
    }
    
    put_uint32_to_buff(buff + *offset, ui_data);
    *offset += sizeof(unsigned int);
}

void pack_int32s_data(unsigned char* buff, unsigned int* offset, char precision, void* data)
{
    int ui_data = 0;
    if(0 != precision)
    {
        ui_data = (int)(*(float*)data * pow(10, precision));
    }
    else
    {
        ui_data = *(int*)data;
    }
    
    put_int32_to_buff(buff + *offset, ui_data);
    *offset += sizeof(int);
}

void pack_int16u_data(unsigned char* buff, unsigned int* offset, char precision, void* data)
{
    unsigned short si_data = 0;
    if(0 != precision)
    {
        si_data = (unsigned short)(*(float*)data * pow(10, precision));
    }
    else
    {
        si_data = (unsigned short)(*(unsigned int*)data);
    }
    
    put_uint16_to_buff(buff + *offset, si_data);
    *offset += sizeof(unsigned short);
}

void pack_int16s_data(unsigned char* buff, unsigned int* offset, char precision, void* data)
{
    short si_data = 0;
    if(0 != precision)
    {
        si_data = (short)(*(float*)data * pow(10, precision));
    }
    else
    {
        si_data = (short)(*(unsigned int*)data);
    }
    
    put_int16_to_buff(buff + *offset, si_data);
    *offset += sizeof(short);
}

void pack_int8u_data(unsigned char* buff, unsigned int* offset, char precision, void* data)
{
    if(0 != precision)
    {
        *(buff + *offset) = (unsigned char)(*(float*)data * pow(10, precision));
    }
    else
    {
        *(buff + *offset) = (unsigned char)(*(unsigned int*)data);
    }

    *offset += sizeof(unsigned char);
}

void pack_bit_data(unsigned char* buff, unsigned int* offset, char precision, void* data)
{
    s_bit_num ++;
    *(buff + *offset) += (*(unsigned int*)data & 0x01) << (8 - s_bit_num);
    if(s_bit_num == 8)
    {
        s_bit_num = s_bit_num % 8;
        *offset += 1;
    }
}

static his_data_pack_t s_his_data_pack[] = 
{
    {1, TYPE_INT32U, pack_int32u_data},
    {1, TYPE_INT32S, pack_int32s_data},
    {1, TYPE_INT16U, pack_int16u_data},
    {1, TYPE_INT16S, pack_int16s_data},
    {1, TYPE_INT8U,  pack_int8u_data},
    {0, TYPE_INT32U, pack_int32u_data},
    {0, TYPE_INT16U, pack_int16u_data},
    {0, TYPE_INT8U,  pack_int8u_data},
    {0, TYPE_BIT,    pack_bit_data},
};

unsigned char find_pack_tab_index(unsigned char precision, unsigned char storage_type)
{
    int tab_num = sizeof(s_his_data_pack) / sizeof(his_data_pack_t);
    char precision_flag = 0;
    int loop = 0;
    if(precision != 0)
    {
        precision_flag = 1;
    }
    for(loop = 0; loop < tab_num; loop ++)
    {
        if(s_his_data_pack[loop].storage_type == storage_type && s_his_data_pack[loop].precision_flag == precision_flag)
        {
            return loop;
        }
        
    }
    return 0xFF;
}

int save_sample_data_to_his_data(his_data_record_info * his_data_record)
{
    unsigned int offset = 0;
    int loop = 0;
    int id_loop = 0;
    unsigned char index = 0;
    s_bit_num = 0;
    unsigned char data[4] = {};
    time_base_t time = {};
    int time_head_len = sizeof(time_base_t);
    unsigned char* buff = (unsigned char*)his_data_record;
    get_time(&time);
    put_time_to_buff(buff, time);

    for(loop = 0; loop < sizeof(realdata_info_tab) / sizeof(realdata_info_t); loop ++)
    {
        for(id_loop = 0; id_loop < realdata_info_tab[loop].id_num; id_loop ++)
        {
            rt_memset_s(data, sizeof(data), 0, sizeof(data));
            get_one_data(realdata_info_tab[loop].realdata_id_offset + id_loop, (void*)data);
            index = find_pack_tab_index(realdata_info_tab[loop].precision, realdata_info_tab[loop].storage_type);
            if(index == 0xFF)
            {
                return FAILURE;
            }
            s_his_data_pack[index].pack_data(buff + time_head_len, &offset, realdata_info_tab[loop].precision, data);
        } 
    }
    if(s_bit_num != 0)    //当bit型数据不满足8个，也按1字节计算
    {
        offset += 1;
    }
    if(offset != HISDATA_LEN)
    {
        return FAILURE;
    }
    return SUCCESSFUL;
}

int pub_his_data_save_msg(void)
{
    save_record_msg_t  rec_msg = {0,};
    rt_memset_s(&s_hisdata_record, sizeof(s_hisdata_record), 0, sizeof(s_hisdata_record));
    if(SUCCESSFUL != save_sample_data_to_his_data(&s_hisdata_record))
    {
        return FAILURE;
    }
    
    rec_msg.auth_code = AUTH_CODE_HIS_DATA;
    rec_msg.record_type = RECORD_TYPE_HIS_DATA;
    rec_msg.len = sizeof(his_data_record_info);
    rec_msg.hisdata = &s_hisdata_record;
    pub_msg_to_thread(LOG_MGR_SAVE_ONE_RECORD, &rec_msg, sizeof(save_record_msg_t));
    return SUCCESSFUL;
}



//清除一条实时告警信息
Static short delete_one_record_real_alm(unsigned int alm_id)
{
    short ret = FAILURE;
    int index = 0;
    int saved_num = 0;
    
    saved_num = get_saved_record_num_new(RECORD_TYPE_REAL_ALARM);
    read_record_data_new(RECORD_TYPE_REAL_ALARM,saved_num,0,buff); 
    // pub_hisrecord_read_msg(RECORD_TYPE_REAL_ALARM,saved_num,0,buff); 
    for (index = 0; index < saved_num;)
    {
        if (buff[index].alarm_id == alm_id)
        {
            // 删除这条记录
            if (index != saved_num - 1)
            {
                rt_memcpy_s(&buff[index], sizeof(sig_alm_chg_msg_t), &buff[saved_num - 1], sizeof(sig_alm_chg_msg_t));
            }
                saved_num--;
        }
        else
        {
            index++;
        }
    }
    
    delete_record_data_new(AUTH_CODE_REAL_ALARM, RECORD_TYPE_REAL_ALARM);
    //保存数据
    s_his_record[RECORD_TYPE_REAL_ALARM].his_record_info.saved_num = saved_num;
    s_his_record[RECORD_TYPE_REAL_ALARM].his_record_info.crc = crc_cal((unsigned char *)&s_his_record[RECORD_TYPE_REAL_ALARM].his_record_info, offsetof(record_info_t, crc));
    
    if(saved_num != 0)
    {
        save_one_record_data_new(AUTH_CODE_REAL_ALARM, RECORD_TYPE_REAL_ALARM, &buff, saved_num*sizeof(sig_alm_chg_msg_t));
    }
    
    ret = save_record_info(s_his_record[RECORD_TYPE_REAL_ALARM].info_name, &s_his_record[RECORD_TYPE_REAL_ALARM].his_record_info);
    
    return ret;
}

