/**
 * @file     sample_handle.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2023-05-22
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */

 
#ifndef _DELAY_UPDATE_H
#define _DELAY_UPDATE_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include <stdio.h>
#include <rtthread.h>
#include <rtdevice.h>
#include <string.h>
#include "data_type.h"

#define DELAY_UPDATE_TIME           (30*1000)            // 允许远程升级的时间
#define CHECK_PV_NORMAL_TIMEOUT     2000
#define ONE_DAY_SECOND              (24 * 60 * 60)
// 实时量数据
typedef struct 
{
    unsigned int last_time;
    unsigned int curr_time;
    unsigned int pv_normal_time;
}delay_update_info_t; 


int check_update_enable(void);
int init_update_enable_timer();
int start_delay_update();
delay_update_info_t* get_delay_update_info(void);
int get_pv_normal_time(void);
int send_update_msg_to_sys_manage(int opt, int file_type, unsigned int file_size, unsigned int file_crc, short addr_info);
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _SAMPLE_H