#include "softbus.h"
#include "utils_server.h"
#include "log_mgr_api.h"
#include "alarm_manage.h"
#include "system_manage.h"
#include "utils_heart_beat.h"
#include "app_config.h"
#include "thread_id.h"
#include "his_record.h"
#include "msg_id.h"
#include "dev_acmu.h"
#include "utils_thread.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "utils_rtthread_security_func.h"
#include "const_define_in.h"
#include "para_manage.h"

static _rt_server_t g_sys_server;

Static void handle_acmu_all_alm_chg_msg(_rt_msg_t curr_msg);

static msg_map his_alm_msg_map[] =
{
    {CHECK_SIGNAL_ALARM_CHG_MSG,    msg_handle_nothing},
    {CHECK_ALL_ALARM_CHG_MSG, msg_handle_nothing},
};

static log_msg_handle_process_t s_alm_msg_handle[] =
{
    {CHECK_SIGNAL_ALARM_CHG_MSG,     handle_acmu_alm_msg},
    {CHECK_ALL_ALARM_CHG_MSG, handle_acmu_all_alm_chg_msg},
};

static unsigned short s_trigger_save_his_data_alarm_code[] RAM_SECTION = {
    ACMU_ALM_ID_AC_POWER_OFF ,                                    //交流停电
    ACMU_ALM_ID_AC_PHASE_LOST ,                                   //交流缺相A、B、C
    ACMU_ALM_ID_PHASE_VOLT_UNBALANCE ,                            //相电压不平衡
    ACMU_ALM_ID_PHASE_VOLT_HIGH_ALARM ,                           //交流A、B、C相电压高
    ACMU_ALM_ID_PHASE_VOLT_LOW_ALARM ,                            //交流A、B、C相电压低
    ACMU_ALM_ID_PHASE_CURR_HIGH_ALARM ,                                    //交流相电流A、B、C高
    ACMU_ALM_ID_SPD_C ,                                           //C级防雷器损坏
    ACMU_ALM_ID_AC_OUTPUT_SWITCH_DISCONNECTION ,                  //交流输出空开断
    ACMU_ALM_ID_AC_LINE_VOLT_HIGH ,                               //交流线AB、BC、CA电压高
    ACMU_ALM_ID_AC_LINE_VOLT_LOW ,                                //交流线AB、BC、CA电压低
    ACMU_ALM_ID_AC_METER_DISCONNECTED,                            //交流电表通信断
    ACMU_ALM_ID_ENV_TEMP_HIGH_ALARM,                              //环境温度高 
    ACMU_ALM_ID_ENV_TEMP_LOW_ALARM,                               //环境温度低
    ACMU_ALM_ID_ENV_HUMIDITY_HIGH_ALARM,                          //环境湿度高
    ACMU_ALM_ID_ENV_HUMIDITY_LOW_ALARM,                           //环境湿度低
    ACMU_ALM_ID_FLOOD_SENSOR_ALARM,                               //水淹传感器告警
    ACMU_ALM_ID_FUMES_SENSOR_ALARM,                               //烟雾传感器告警
    ACMU_ALM_ID_DOORMAT_SENSOR_ALARM,                             //门磁传感器告警
    ACMU_ALM_ID_ENV_TEMP_SENSOR_INVALID_ALARM,                    //环境温度传感器失效
    ACMU_ALM_ID_ENV_HUMIDITY_SENSOR_INVALID_ALARM,                //环境湿度传感器失效
    ACMU_ALM_ID_INPUT_RELAY1_ALARM,                               //输入干节点1告警
    ACMU_ALM_ID_INPUT_RELAY2_ALARM,                               //输入干节点2告警
    ACMU_ALM_ID_INPUT_RELAY3_ALARM,                               //输入干节点3告警
    ACMU_ALM_ID_INPUT_RELAY4_ALARM,                               //输入干节点4告警
};

Static int check_main_alarm_changed(unsigned short alarm_code);
Static int process_recv_msg(void);
Static int check_almid_and_save_his_data(unsigned int alarm_id);
Static short save_history_data(void);

Static int process_recv_msg(void)
{
    int i = 0; 
    if ((g_sys_server == NULL) || (rt_sem_take(&g_sys_server->msg_sem, 10) != RT_EOK) || (g_sys_server->msg_node == RT_NULL))
    {
        return FAILURE;
    }

    _rt_msg_t curr_msg = g_sys_server->msg_node ;
    for (i = 0; i < sizeof(s_alm_msg_handle) / sizeof(s_alm_msg_handle[0]); i++)
    {
        if (s_alm_msg_handle[i].msg_id == curr_msg->msg.msg_id)
        {
            s_alm_msg_handle[i].handle(curr_msg);
        }
    }

    rt_mutex_take(&g_sys_server->mutex, RT_WAITING_FOREVER);
    g_sys_server->msg_node = curr_msg->next;
    g_sys_server->msg_count--;
    rt_mutex_release(&g_sys_server->mutex);

    softbus_free(curr_msg);
    return SUCCESSFUL;
}


Static int check_almid_and_save_his_data(unsigned int alarm_id)
{
    int i = 0;
    unsigned short alarm_code = 0x0000;
    for(i = 0; i < sizeof(s_trigger_save_his_data_alarm_code) / sizeof(s_trigger_save_his_data_alarm_code[0]); i ++)
    {
        alarm_code = ALM_ID_GET_ALM_CODE(alarm_id); // 通过alarm_id反向获取告警码
        if(alarm_code == s_trigger_save_his_data_alarm_code[i])
        {
            pub_his_data_save_msg();
        }
    }

    return SUCCESSFUL;
}

Static void handle_acmu_all_alm_chg_msg(_rt_msg_t curr_msg)
{
    unsigned char data_flag1 = 0;
    unsigned char data_flag2 = 0;
    char buff[MAX_LETTERS];

    rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));

    get_one_data(ACMU_DATA_ID_DATA_FLAG2, &data_flag2);
    get_one_data(ACMU_DATA_ID_DATA_FLAG1, &data_flag1);
    data_flag1 |= FLAG1_ALMCH;

    set_one_data(ACMU_DATA_ID_DATA_FLAG1, &data_flag1);
    rt_snprintf_s( buff, sizeof(buff), "FLAG1:%x  FLAG2:%x", data_flag1, data_flag2);
    SetTraceStr( 7, buff );
    return;
}

void handle_acmu_alm_msg(_rt_msg_t curr_msg)
{
    sig_alm_chg_msg_t* real_alm = (sig_alm_chg_msg_t*)curr_msg->msg.data;
    his_alarm_info_t his_alarm = {0};
    // real_alm->alarm_value == 1 新告警
    // real_alm->alarm_value == 0 告警消失
    his_alarm.alarm_id = ALM_ID_GET_ALM_CODE(real_alm->alarm_id);
    his_alarm.index = ALM_ID_GET_DEV(real_alm->alarm_id);
    his_alarm.alarm_state = real_alm->alarm_value;
    his_alarm.start_time = (time_t)real_alm->start_time;
    his_alarm.end_time =  (time_t)real_alm->end_time;
    check_almid_and_save_his_data(real_alm->alarm_id);  // 检测是否是触发记录历史数据的告警ID，是则保存历史数据
    check_main_alarm_changed(his_alarm.alarm_id);
    if(real_alm->alarm_value == 0)
    {
        pub_hisalarm_save_msg(&his_alarm);
    }
    return;
}

/**
 * @description:检测是否有重要告警变化
 * @param {unsigned int} alarm_id - 告警ID
 * @return {int} 成功返回SUCCESSFUL（0）
 */
 Static int check_main_alarm_changed(unsigned short alarm_code)
 {
    unsigned char level = 0;
    unsigned short alm_level_offset = GET_ALM_PARA_BY_ALM_CODE(alarm_code, 0);
    
    get_alm_para(alm_level_offset, &level);
    if (level == ALARMCLASS_MAJOR || level == ALARMCLASS_CRITICAL) {
        pub_msg_to_thread(MAIN_ALARM_CHANGE_MSG, NULL, 0);
    }
    return SUCCESSFUL;
 }


Static short save_history_data(void)
{
    static unsigned int s_his_time_count = 0;
    unsigned int time_interval = 0;
    unsigned int his_time = 0;

    s_his_time_count += 1;
    his_time = (s_his_time_count * SYSTEM_MANAGE_THREAD_DELAY_TIME) / 1000;      // 将毫秒转为秒
    get_one_para(ACMU_PARA_ID_HISDATA_SAVE_INTERVAL_OFFSET, &time_interval); // 历史数据存储时间间隔, 单位小时
    time_interval *= 3600; // 转换为秒

    if (his_time >= time_interval ) {
        pub_his_data_save_msg();
	    s_his_time_count = 0;   // 重新计数
    }
    return SUCCESSFUL;
}


void *init_sys_manage(void *param) 
{
    server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(his_alm_msg_map) / sizeof(msg_map);
    register_server_msg_map(his_alm_msg_map, server_info);
    return server_info;
}

void system_manage_main(void *param)
{
    PRINT_MSG_AND_RETURN_IF_FAIL(param != NULL);
    g_sys_server = _curr_server_get();
    thread_monitor_register("system_manage");
    while (is_running(TRUE)) 
    {   
        thread_monitor_update_heartbeat();
        process_recv_msg();
        save_history_data();
        rt_thread_mdelay(SYSTEM_MANAGE_THREAD_DELAY_TIME);
    }
}
