#include <rtthread.h>
#include <rtdevice.h>
#include <math.h>
#include <string.h>
#include "sample.h"
#include "msg.h"
#include "msg_id.h"
#include "utils_server.h"
#include "utils_thread.h"
#include "utils_rtthread_security_func.h"
#include "utils_math.h"
#include "pin.h"
#include "realdata_save.h"
#include "realdata_id_in.h"
#include "para_id_in.h"
#include "adc_ctrl.h"
#include "dev_airswitch.h"
#include "pin_define.h"
#include "meter.h"
#include "para_manage.h"
#include "device_common.h"
#include "eeprom_storage.h"

/* 静态函数声明 */
Static float linear_insert(int data, const int *TABLE, unsigned short num);
Static char get_adc_channel_temp(void);
Static float get_airswitch_adc_volt(rt_adc_device_t adc_dev, unsigned char adc_no);
Static char get_adc_channel_volt(float *data);
Static unsigned char get_over_current_protect_auto_recovery_count(void);
Static float get_airswitch_power(void);
Static float get_adc_output_voltage(void);
Static float get_airswitch_input_voltage(void);
Static char check_power_status(void);
Static char save_energy_time(void);
Static char check_switch_power_status(void);
Static char sync_state_update(void);
Static char deal_north_command(void);
Static char deal_protct_status_judge(void);
Static char status_synchronization_when_power_on(void);
Static char judge_airswitch_measurechip_date(void);
Static char judge_authorization_status(void);
Static unsigned char calc_overload_time_thre(unsigned char s_frame_current);
Static unsigned char clean_overload_relevant_data(void);
Static unsigned char judge_large_current_cause_protect(float realtime_current, float rated_current);
Static unsigned char calc_accumulate_energy(float realtime_current, float rated_current);
Static unsigned char judge_accumulate_energy_cause_protect(float realtime_current, float rated_current);
Static char check_current_valid(float *sample_current, unsigned char shell_frame);
Static char clear_over_current_protect_recovery_count(void);
Static unsigned char normal_disconnect_status_appear(void);
Static float get_airswitch_current(void);
Static char low_volt_down_power_appear(protect_para_t* para);
Static char low_volt_down_power_disappear(protect_para_t* para);
Static protect_status_judge_t* get_exist_protect_status(void);
Static int deal_current_smooth(int sample_current, unsigned char shell_frame);
Static char set_airswitch_control_status(airswitch_control_status_e status);
Static unsigned char is_forbid_act_by_apptest(void);
Static unsigned char is_forbid_close_by_unauthorize(void);
Static unsigned char update_fault_opening_times(void);
Static char get_airswitch_electric(void);

/*全局静态变量声明*/
Static int s_over_current_protect_rec_interval_time = 0; //过流保护自恢复间隔时间
Static int s_over_current_protect_rec_clear_time = 0; //过流保护自恢复执行次数清零时间
Static unsigned char s_energy_calc_start_flag = FALSE;// 累计能量开始计算标志
Static float s_accumulated_energy = 0.0;// 累计能量
Static float s_input_power_energy_old = 0.0;// 输入累计电量
Static float s_output_power_energy_old = 0.0;// 输出累计电量
Static unsigned char s_large_current_start_flag = FALSE;// 开始检测到大电流的标志
Static rt_tick_t s_large_cuurent_duration_tick = 0;// 大电流持续时间
Static unsigned short s_overload_time_thre = 0xffff;// 计算累计能量导致过载跳闸保护时的时间阈值；
Static unsigned char s_frame_current = FRAME_CURRENT_63A;// 壳架电流默认63A
Static char s_SetAddrFlag = FALSE;
static unsigned char s_power_off_disappear_counter = 0;
static int s_power_off_delay_counter = 0;
Static unsigned char s_manual_disconnection_flag = 0;
Static rt_device_t s_meter_dev = NULL;
Static MeterRealDataTypeDef s_airswitch_data = {0};  //计量芯片数据结构体
Static unsigned char s_loop_old_status = LOOP_OPEN;
Static unsigned char s_control_status_close = 1;
Static unsigned char s_authorization_status_change = 0;
rt_tick_t s_input_tick_old = 0;
rt_tick_t s_output_tick_old = 0;


/* 禁止合闸的判断函数 */
FunctionPointerJudge s_func_forbid_close[] = {
    is_forbid_close_by_unauthorize,
    is_forbid_act_by_apptest,
};

/* 空开下电告警产生判断 */
Static char power_off_appear_judge(protect_para_t* para);
/* 空开下电告警恢复判断 */
Static char power_off_disappear_judge(protect_para_t* para);
/* 过温保护产生判断 */
Static char over_temp_appear_judge(protect_para_t* para);
/* 过温保护恢复判断 */
Static char over_temp_disappear_judge(protect_para_t* para);
/* 过压保护产生判断 */
Static char over_voltage_appear_judge(protect_para_t* para);
/* 过压保护恢复判断 */
Static char over_voltage_disappear_judge(protect_para_t* para);
/* 过载保护产生判断 */
Static char overload_appear_judge(protect_para_t* para);
/* 过载保护恢复判断 */
Static char overload_disappear_judge(protect_para_t* para);
/* 过流保护产生判断 */
Static char over_current_protect_appear_judge(protect_para_t* para);
/* 过流保护恢复判断 */
Static char over_current_protect_disappear_judge(protect_para_t* para);

/* 保护处理总表 */
Static protect_status_judge_t s_protect_status_sample_map[] =
{
    {{SWITCH_DATA_ID_POWER_DOWN_ALARM_STATUS}, power_off_appear_judge, power_off_disappear_judge},
    {{SWITCH_PARA_ID_LOW_VOLTAGE_POWER_DOWN_STATUS_OFFSET, TEN_SECOND_COUNT}, low_volt_down_power_appear, low_volt_down_power_disappear},
    {{SWITCH_PARA_ID_OVERLOAD_TRIP_STATUS_OFFSET, FIVE_SECOND_COUNT}, overload_appear_judge, overload_disappear_judge},
    {{SWITCH_PARA_ID_OVER_VOLTAGE_PROTECT_STATUS_OFFSET, THREE_SECOND_COUNT}, over_voltage_appear_judge, over_voltage_disappear_judge},
    {{SWITCH_PARA_ID_OVER_CURRENT_PROTECT_STATUS_OFFSET}, over_current_protect_appear_judge, over_current_protect_disappear_judge},
    {{SWITCH_PARA_ID_OVER_TEMPERATURE_PROTECT_STATUS_OFFSET, FIVE_SECOND_COUNT}, over_temp_appear_judge, over_temp_disappear_judge},
};

static msg_map sample_msg_map[] =
{
     {0,NULL},//临时添加解决编译问题
};

typedef struct
{
    adc_sample_channel     sample_channel;
    rt_base_t              select_channel;
    unsigned char          select_status;
}select_sample_pin_t;

static select_sample_pin_t s_select_sample[] = {
    {VTA, 0, 0},
    {VTB, 1, 0},
    {VTC, 4, 0},
    {ADDX,5, 0},
    {ADDY,6, 0},
    {VOUT,7, 0},
};

//电阻-温度对应表
const int SW_Table[181]=
{
    20447, 19326, 18276, 17291, 16365,15494, 14674, 13902, 13173, 12487,     //-40~-31
    11839, 11228, 10651, 10107, 9593, 9108, 8650, 8218, 7810, 7424,     //-30~-21
    7060, 6716, 6391, 6083, 5793, 5518, 5258, 5012, 4779 , 4558 ,     //-20~-11
    4349 , 4151 , 3964 , 3786 , 3617 , 3456 , 3304 , 3160 , 3022 , 2892 ,     //-10~-1
    2768 , 2649 , 2537 , 2430 , 2328 , 2231 , 2139 , 2051 , 1967 , 1886 ,     //0~9
    1810 , 1737 , 1667 , 1601 , 1537 , 1477 , 1419 , 1364 , 1311 , 1260 ,     //10~19
    1212 , 1165 , 1121 , 1079 , 1038 , 1000 , 962 , 927 , 893 , 860 ,     //20~29
    829 , 799 , 770 , 743, 716 , 691 , 667 , 643 , 621 , 600 ,     //30~39
    579 , 559  , 540  , 522 , 505  , 488 , 472  , 456  , 441  , 427  ,     //40~49
    413  , 400  , 387  , 374  , 363  , 351  , 340  , 329  , 319  , 309  ,     //50~59
    300  , 291  , 282  , 273  , 265  , 257  , 250  , 242  , 235  , 228  ,     //60~69
    222  , 215  , 209  , 203  , 197  , 192  , 186  , 181  , 176  , 171  ,     //70~79
    166  , 162  , 157  , 153  , 149  , 145  , 141  , 137  , 133  , 130  ,     //80~89
    126  , 123  , 120  , 117  , 114  , 111  , 108  , 105  , 102  , 100  ,     //90~99
    97  , 95  , 92  , 90  , 88  , 86  , 83  , 81  , 79  , 77  ,     //100~109
    76  , 74  , 72  , 70  , 69  , 67  , 65  , 64  , 62  , 61  ,     //110~119
    60  , 58  , 57  , 56  , 55  , 53  , 51  , 50  , 48  , 47  ,    //120~129
    45  , 44  , 42  , 41  , 39  , 38  , 36  , 35  , 33  , 32  ,    //130~139
    30  , //140
};

/**
 * @brief  ADC采样电压
 * @param[in]    adc_dev ADC设备指针
 * @param[in]    adc_no ADC编号
 * @retval       ADC采样的电压值
 */

Static float get_airswitch_adc_volt(rt_adc_device_t adc_dev, unsigned char adc_no)
{
    float volt = 0.0f;
    float adc_calibration_slope = 1.0f;
    float adc_calibration_zero = 0.0f; // 接线端电压校准零点（单位是V）

    if (adc_no == s_select_sample[VOUT].select_channel) //仅接线端电压VOUT需要校准
    {
        // 获取校准参数
        get_one_data(SWITCH_DATA_ID_TERMINAL_CALIBRATION_SLOPE, &adc_calibration_slope);
        get_one_data(SWITCH_DATA_ID_TERMINAL_CALIBRATION_ZERO, &adc_calibration_zero);

        // 校准后的接线端电压
        volt = get_adc_volt(adc_dev, adc_no) * adc_calibration_slope + adc_calibration_zero / OUTPUT_VOLTAGE_SCALING_FACTOR;
    }
    else
    {
        volt = get_adc_volt(adc_dev, adc_no);
    }

    return volt;
}


/**
 * @brief  线性插值
 * @param[in]    data  电阻值
 * @param[in]    TABLE 电阻-温度对应表
 * @param[in]    num   电阻-温度对应表元素数量
 * @retval       真实温度(float)
 */
Static float linear_insert(int data, const int *TABLE, unsigned short num)
{
    float Temp;
    int Array[3];
    // 温度有效值 -40~125
    if (data < TABLE[LOW_TEMP+40])                 //Beyond Lowest Temperature
    {
        if (data > TABLE[HIGH_TEMP+40])            //Beyond Highest Temperature
        {
            lookup_TAB(data, TABLE, num, Array);      //Lookup data

            if (data == Array[2])
                Temp = (Array[0] * 1.0 - 40);
            else
                Temp = (Array[0] * 1.0 - 40) + (data * 1.0 - Array[2]) * 1.0 / (Array[1] * 1.0 - Array[2]);  //线性插值计算
        }
        else
            Temp = TEMP_INVALID_HIGH;
    }
    else
        Temp = TEMP_INVALID_LOW;

    return(Temp);
}

Static char get_adc_channel_temp(void) {
    float data[3][3] ={0};    // 采样数据
    short ret = 0;

    unsigned short num = sizeof(SW_Table) / sizeof(SW_Table[0]);
    rt_adc_device_t adc_dev = (rt_adc_device_t)rt_device_find(TEMP_ADC_DEV_NAME);

    data[0][0] = get_airswitch_adc_volt(adc_dev, s_select_sample[VTA].select_channel);
    data[1][0] = get_airswitch_adc_volt(adc_dev, s_select_sample[VTB].select_channel);
    data[2][0] = get_airswitch_adc_volt(adc_dev, s_select_sample[VTC].select_channel);

    if(0 >= data[0][0] || 0 >= data[1][0] || 0 >= data[2][0])
    {
        return FAILURE;
    }

    data[0][1] = (3.3f * 7.5f / data[0][0] - 7.5f) * 100.0f ;          // 计算热敏电阻值
    data[0][2] = linear_insert((int)data[0][1], SW_Table, num);       // 由电阻值得到对应的温度值

    data[1][1] = (3.3f * 7.5f / data[1][0] - 7.5f) * 100.0f ;       // 计算热敏电阻值
    data[1][2] = linear_insert((int)data[1][1], SW_Table, num);       // 由电阻值得到对应的温度值

    data[2][1] = (3.3f * 7.5f / data[2][0] - 7.5f) * 100.0f ;       // 计算热敏电阻值
    data[2][2] = linear_insert((int)data[2][1], SW_Table, num);       // 由电阻值得到对应的温度值

    ret = set_one_data(SWITCH_DATA_ID_INTERNAL_TEMPERATURE,&data[0][2]); //内部温度
    ret |= set_one_data(SWITCH_DATA_ID_CONTACT_TEMPERATURE,&data[1][2]); //触点温度
    ret |= set_one_data(SWITCH_DATA_ID_BUS_TEMPERATURE,&data[2][2]); //母排端温度

    return ret;
}

Static char auto_power_on_judge(void)
{
    float value = 0;
    float threshold = 0;
    unsigned char sw_loop_close_status = 1;
    unsigned char power_on_down_command = 1;
    unsigned char communication_fail_status = 0;
    unsigned char download_status = 1;
    unsigned char power_down_enabled = 0;

    // 获取实时数据
    get_one_para(SWITCH_PARA_ID_POWER_DOWN_ENABLED_OFFSET, &power_down_enabled);
    get_one_data(SWITCH_DATA_ID_COMMUNICATION_FAIL_STATUS, &communication_fail_status);
    get_one_data(SWITCH_DATA_ID_BUS_VOLTAGE, &value);  //母排端(输入)电压
    get_one_para(SWITCH_PARA_ID_POWER_ON_VOLTAGE_THRESHOLD_OFFSET, &threshold);
    get_one_data(SWITCH_DATA_ID_LOOP_STATUS, &sw_loop_close_status);
    get_one_para(SWITCH_PARA_ID_POWER_ON_POWER_DOWN_COMMAND_OFFSET, &power_on_down_command);
    get_one_data(SWITCH_DATA_ID_DOWNLOAD_STATUS,&download_status);

    s_power_off_delay_counter++;
    s_power_off_delay_counter = (s_power_off_delay_counter >= AIRSWITCH_FIVE_MIN_TIME) ? AIRSWITCH_FIVE_MIN_TIME : s_power_off_delay_counter;
    
    if(!communication_fail_status)
    {
        s_power_off_disappear_counter = 0;
        if(sw_loop_close_status && AIRSWITCH_POWER_ON == power_on_down_command && download_status == AIRSWITCH_POWER_ON)
        {
            return TRUE;
        }
        return FALSE;
    }

    if(s_power_off_delay_counter < AIRSWITCH_FIVE_MIN_TIME)        //延时5分钟
    {
        return FALSE;
    }
 
    if((value >= threshold) && power_down_enabled)
    {
        if(s_power_off_disappear_counter < TEN_SECOND_COUNT)       //消失延时计算
        {
            s_power_off_disappear_counter++;
        }
        else
        {
            s_power_off_disappear_counter = TEN_SECOND_COUNT;
            if(s_control_status_close)
            {
                // 控制合闸
                set_airswitch_control_status(AIRSWITCH_CONTROL_STATUS_CLOSE);
                s_control_status_close = 0;
            }
            if(sw_loop_close_status && download_status == AIRSWITCH_POWER_ON)
            {
                s_power_off_disappear_counter = 0;
                return TRUE;
            }
        }
    }
    else
    {
        s_power_off_disappear_counter = 0;
    }
    return FALSE;
}

Static char power_off_appear_judge(protect_para_t* para)
{
    unsigned char power_on_down_command = 1;
    static unsigned char sw_last_download_status = AIRSWITCH_POWER_ON;
    unsigned char sw_download_status = AIRSWITCH_POWER_ON;

    // 获取实时数据
    get_one_para(SWITCH_PARA_ID_POWER_ON_POWER_DOWN_COMMAND_OFFSET, &power_on_down_command);
    get_one_data(SWITCH_DATA_ID_DOWNLOAD_STATUS, &sw_download_status);

    /* 下电指令、且当前分闸时重启，产生下电告警状态；
       下电指令、且下电状态为下电时、且上一次上电，产生下电告警状态 */
    if (power_on_down_command == AIRSWITCH_POWER_DOWN  && sw_last_download_status == AIRSWITCH_POWER_ON && sw_download_status == AIRSWITCH_POWER_DOWN)
    {
        s_power_off_delay_counter = 0;
        s_control_status_close = 1;
        para->protect_state = 1;
        set_one_data(SWITCH_DATA_ID_POWER_DOWN_ALARM_STATUS, &para->protect_state);
        sw_last_download_status = sw_download_status;
        return SUCCESSFUL;
    }
    sw_last_download_status = sw_download_status;
    return FAILURE;
}

Static char power_off_disappear_judge(protect_para_t* para)
{
    if(TRUE == auto_power_on_judge())
    {
        para->protect_state = FALSE;
        set_one_data(SWITCH_DATA_ID_POWER_DOWN_ALARM_STATUS, &para->protect_state);
    }
    return TRUE;
}

char check_micro_movement_status(void)
{
    short ret = 0;
    unsigned char close_pos_micro_status = 0;
    unsigned char free_pos_micro_status = 0;
    unsigned char close_fin_pos_micro_status = 0;
    if(PIN_LOW == rt_pin_read(PIN_MOTOR_VSC)) {
        close_pos_micro_status = LOOP_CLOSE;
    }
    else
    {
        close_pos_micro_status = LOOP_OPEN;
    }
    free_pos_micro_status = rt_pin_read(PIN_MOTOR_VSB);
    close_fin_pos_micro_status = rt_pin_read(PIN_MOTOR_VSA);
    ret = set_one_data(SWITCH_DATA_ID_LOOP_STATUS,&close_pos_micro_status);
    ret |= set_one_data(SWITCH_DATA_ID_CLOSE_FIN_POS_MICRO_STATUS,&close_pos_micro_status);
    ret |= set_one_data(SWITCH_DATA_ID_FREE_POS_MICRO_STATUS,&free_pos_micro_status);
    ret |= set_one_data(SWITCH_DATA_ID_CLOSE_POS_MICRO_STATUS,&close_fin_pos_micro_status);
    return ret;
}

/**
 * @brief 正接反接状态
*/
Static char check_switch_power_status(void)
{
    char ret = 0;
    unsigned char switch_power_status = 0;
    if (FRAME_CURRENT_63A == judge_frame_current()) //63A壳架硬件不支持反接
    {
        return FAILURE;
    }
    switch_power_status = rt_pin_read(PIN_REVERSE_STATUS);
    ret = set_one_data(SWITCH_DATA_ID_SWITCH_POWER_REVERSE_STATUS,&switch_power_status);
    return ret;
}

/**
 * @brief 电量保存计时及变化值判断
*/
Static char save_energy_time(void)
{
    int input_power_energy = 0;
    int output_power_energy = 0;
    unsigned char energy_save_flag = 0;
    unsigned char loop_status = LOOP_OPEN;
    static rt_tick_t input_tick = 0, output_tick = 0;
    get_one_data(SWITCH_DATA_ID_LOOP_STATUS, &loop_status);
    get_one_data(SWITCH_DATA_ID_INPUT_POWER_ENERGY,&input_power_energy);
    get_one_data(SWITCH_DATA_ID_OUTPUT_POWER_ENERGY,&output_power_energy);
    input_tick = rt_tick_get_millisecond();
    output_tick = rt_tick_get_millisecond();

    if(fabs(input_power_energy - s_input_power_energy_old) >= POWER_SAVE_DIFFERENCE || fabs(input_tick - s_input_tick_old) > ONE_WH_POWER)
    {
        if(input_power_energy != s_input_power_energy_old)
        {
            energy_save_flag = 1;
            s_input_power_energy_old = input_power_energy;
            s_input_tick_old = input_tick;
        }
    }
    if(fabs(output_power_energy - s_output_power_energy_old) >= POWER_SAVE_DIFFERENCE || fabs(output_tick - s_output_tick_old) > ONE_WH_POWER)
    {
        if(output_power_energy != s_output_power_energy_old)
        {
            energy_save_flag = 1;
            s_output_power_energy_old = output_power_energy;
            s_output_tick_old = output_tick;
        }
    }
    if((s_loop_old_status != loop_status && LOOP_OPEN == loop_status) || 1 == energy_save_flag)
    {
        save_power_energy();    // 保存电量信息到eeprom中
    }
    s_loop_old_status = loop_status;
    return SUCCESSFUL;
}

Static char get_adc_channel_volt(float *data)
{
    unsigned char offset = 0;
    rt_adc_device_t adc_dev = (rt_adc_device_t)rt_device_find(TEMP_ADC_DEV_NAME);

    data[offset++] = (get_airswitch_adc_volt(adc_dev, s_select_sample[ADDX].select_channel) * ADDR_VOLTAGE_RATIO);//组地址
    data[offset++] = (get_airswitch_adc_volt(adc_dev, s_select_sample[ADDY].select_channel) * ADDR_VOLTAGE_RATIO);//组内地址
    data[offset] = get_airswitch_input_voltage();  //母排端(输入)电压;

    return 0;
}

//获取空开接线端电压（输出电压、负载端电压）
Static float get_adc_output_voltage(void)
{
    float airswitch_output_voltage = 0.0;
    float adc_voltage = 0.0;

    unsigned char reversely_status = 0;
    get_one_data(SWITCH_DATA_ID_SWITCH_POWER_REVERSE_STATUS, &reversely_status);
    if(TRUE == reversely_status) // 如果反接，则接线端电压显示为0V
    {
        return 0.0;
    }

    rt_adc_device_t adc_dev = (rt_adc_device_t)rt_device_find(TEMP_ADC_DEV_NAME);
    if (adc_dev == RT_NULL)
    {
        return FAILURE;
    }
    adc_voltage = get_airswitch_adc_volt(adc_dev, s_select_sample[VOUT].select_channel);   //获取VOUT采样电压
    airswitch_output_voltage = adc_voltage * OUTPUT_VOLTAGE_SCALING_FACTOR;

    if(airswitch_output_voltage < 0.0) // 低于0V按照0V显示
    {
        airswitch_output_voltage = 0.0;
    }
    if(airswitch_output_voltage > EIGHTY_VOLT) // 高于80V按照80V显示
    {
        airswitch_output_voltage = EIGHTY_VOLT;
    }

    return airswitch_output_voltage;
}

// 判断空开有电状态：接线端电压是否>20V，63A壳架则根据母排电压是否>20V判断
Static char check_power_status(void)
{
    float data = 0.0;
    short ret = 0;
    unsigned char power_status = 0;

    if (FRAME_CURRENT_63A == judge_frame_current())
    {
        data = get_airswitch_input_voltage();
    }
    else
    {
        data = get_adc_output_voltage();
    }

    if (data >= POWER_STATUS_VOL_CHECK)
    {
        power_status = POWER_STATUS_ON;
        ret = set_one_data(SWITCH_DATA_ID_POWER_STATUS, &power_status);
    }
    else
    {
        power_status = POWER_STATUS_OFF;
        ret = set_one_data(SWITCH_DATA_ID_POWER_STATUS, &power_status);
    }

    return ret;
}

// 计量芯片数据采样
Static char judge_airswitch_measurechip_date(void)
{
    static unsigned char i = 0,cnt = DEBOUNCE_COUNT*2;
    rt_memset_s(&s_airswitch_data,sizeof(MeterRealDataTypeDef),0,sizeof(MeterRealDataTypeDef));
    RETURN_VAL_IF_FAIL(s_meter_dev != RT_NULL, FAILURE);
    rt_device_control(s_meter_dev, RT_DEVICE_CTRL_GET_VALUE, (void*)&s_airswitch_data); // 获取空开电流、母排电压，写到airswitch_data中

    if((s_airswitch_data.voltage == 0)&& (i <= DEBOUNCE_COUNT))
    {
        cnt--;
        if(cnt == 0)
        {
            i++;
            cnt = DEBOUNCE_COUNT;
            s_meter_dev->flag &= (~RT_DEVICE_FLAG_ACTIVATED);
            rt_device_close(s_meter_dev);
            rt_device_open(s_meter_dev, RT_DEVICE_OFLAG_RDONLY);
        }
    }

    return SUCCESSFUL;
}

/**
 * @brief 获取空开母排电压（输入电压）
 * @return 母排电压值，单位为伏特
 */

Static float get_airswitch_input_voltage(void)
{
    float airswitch_input_voltage = -1.0f;
    float bus_voltage_calibration_slope = 1.0f; // 母排电压校准斜率
    float bus_voltage_calibration_zero = 0.0f; // 母排电压校准零点（单位是V）

    get_one_data(SWITCH_DATA_ID_BUS_VOLTAGE_CALIBRATION_SLOPE, &bus_voltage_calibration_slope);
    get_one_data(SWITCH_DATA_ID_BUS_VOLTAGE_CALIBRATION_ZERO, &bus_voltage_calibration_zero);

    // 将 s_airswitch_data.voltage 转换为 float 类型
    float voltage_float = (float)s_airswitch_data.voltage;

    // 校准后的母排电压
    voltage_float = voltage_float * bus_voltage_calibration_slope + bus_voltage_calibration_zero * 1000.0f;

    if(voltage_float >= 0 && voltage_float <= (EIGHTY_VOLT * 1000.0f)) // 母排电压的显示范围是0-80V
    {
        airswitch_input_voltage = voltage_float / 1000.0f;
    }

    if(voltage_float < 0) // 若母排电压小于0V则按照0V显示
    {
        airswitch_input_voltage = 0.0f;
    }

    if(voltage_float > (EIGHTY_VOLT * 1000.0f)) // 若母排电压大于80V则按照80V显示
    {
        airswitch_input_voltage = EIGHTY_VOLT;
    }

    return airswitch_input_voltage;
}


// 获取空开电压，写入实时数据
Static float get_airswitch_voltage(void)
{
    unsigned char loop_status = 0;
    float airswitch_voltage = 0;
    float airswitch_bus_voltage = 0;
    float airswitch_terminal_voltage = 0;
    get_one_data(SWITCH_DATA_ID_LOOP_STATUS, &loop_status);

    airswitch_bus_voltage = get_airswitch_input_voltage();  //母排端（输入）电压
    set_one_data(SWITCH_DATA_ID_BUS_VOLTAGE, &airswitch_bus_voltage);

    if (FRAME_CURRENT_63A == judge_frame_current()) //63A壳架空开输出电压始终是母排端（输入）电压，接线端赋0
    {
        set_one_data(SWITCH_DATA_ID_TERMINAL_VOLTAGE, &airswitch_terminal_voltage);
        airswitch_voltage = airswitch_bus_voltage;
    }
    else
    {
        airswitch_terminal_voltage = get_adc_output_voltage();  //接线端（输出）电压
        set_one_data(SWITCH_DATA_ID_TERMINAL_VOLTAGE, &airswitch_terminal_voltage);

        if (loop_status == LOOP_OPEN) // 分闸时，电压为接线端（输出）电压
        {
            airswitch_voltage = airswitch_terminal_voltage;
        }
        else // 合闸时，电压为母排端（输入）电压
        {
            airswitch_voltage = airswitch_bus_voltage;
        }
    }

    if(airswitch_voltage < 20) // 电压小于20V（包括为负数的情况），则显示为0
    {
        airswitch_voltage = 0;
    }
    set_one_data(SWITCH_DATA_ID_OUTPUT_VOLTAGE, &airswitch_voltage);    //输出电压，上送CSU使用
    return airswitch_voltage;
}

Static char check_current_valid(float *sample_current, unsigned char shell_frame)
{// 电流检测范围和显示范围为-3*Iu~3*Iu
    float airswitch_current = 0;
    airswitch_current = *sample_current;

    if (airswitch_current < -1 * CURRENT_INVALID_INDEX * shell_frame)
    {
        airswitch_current = -1 * CURRENT_INVALID_INDEX * shell_frame;
    }
    else if (airswitch_current > CURRENT_INVALID_INDEX * shell_frame)
    {
        airswitch_current = CURRENT_INVALID_INDEX * shell_frame;
    }

    *sample_current = airswitch_current;

    return SUCCESSFUL;
}


Static int deal_current_smooth(int sample_current, unsigned char shell_frame)
{
    unsigned char disconnect_judge_mode = 0;
    int airswitch_current = 0;
    int current_fabs = 0.0;
    current_fabs = fabs(sample_current);
    get_one_para(SWITCH_PARA_ID_DISCONNECT_JUDGMENT_MODE_OFFSET, &disconnect_judge_mode);

    airswitch_current = sample_current;
    if(disconnect_judge_mode == INDIRECT_JUDGEMENT) // 判断负载条件下
    {
        if (sample_current > 0 || (s_frame_current != FRAME_CURRENT_63A))
        {
            if (s_frame_current == FRAME_CURRENT_250A)
            {
                shell_frame = SHELL_FRAME_CURR_MAX;
            }

            if (current_fabs < shell_frame * CURRENT_SMOOTH_MIN_INDEX * SAMPLE_AMPLIFY_INDEX)
            {// 清零
                airswitch_current = 0;
            }
            else if (current_fabs >= shell_frame * CURRENT_SMOOTH_MIN_INDEX * SAMPLE_AMPLIFY_INDEX &&
                     current_fabs <= shell_frame * CURRENT_SMOOTH_MAX_INDEX * SAMPLE_AMPLIFY_INDEX)
            {// 平滑处理
                airswitch_current = 2 * (sample_current / current_fabs) *
                                    (current_fabs - shell_frame * CURRENT_SMOOTH_MIN_INDEX * SAMPLE_AMPLIFY_INDEX);
            }
        }
        else if (sample_current < 0 && s_frame_current == FRAME_CURRENT_63A)
        {// 63A壳架负小电流清零
            if (current_fabs <= shell_frame * 0.02 * SAMPLE_AMPLIFY_INDEX)
            {
                airswitch_current = 0;
            }
        }
    }

    return airswitch_current;
}


// 获取空开电流，写入实时数据

Static float get_airswitch_current(void)
{
    unsigned char airswitch_shell_frame_current = judge_frame_current(); // 智能空开壳架电流
    unsigned char loop_status = 0;
    float airswitch_current = 0.0f;
    int amplify_current = 0;
    float current_calibration_slope = 1.0f; // 电流校准斜率
    float current_calibration_zero = 0.0f; // 电流校准零点（单位是A）

    get_one_data(SWITCH_DATA_ID_LOOP_STATUS, &loop_status);
    get_one_data(SWITCH_DATA_ID_CURRENT_CALIBRATION_SLOPE, &current_calibration_slope);
    get_one_data(SWITCH_DATA_ID_CURRENT_CALIBRATION_ZERO, &current_calibration_zero);

    // 将 s_airswitch_data.current 转换为 float 类型
    float current_float = (float)s_airswitch_data.current;

    // 校准后的电流
    current_float = current_float * current_calibration_slope + current_calibration_zero * 1000.0f;

    if (loop_status == LOOP_OPEN)
    {
        // 分闸时电流在0-1.2A（绝对值）的范围内（闭区间）做清零处理
        if (fabsf(current_float) <= CURRENT_DETECT_MIN * SAMPLE_AMPLIFY_INDEX)
        {
            current_float = 0.0f;
        }
    }
    else
    {
        current_float = deal_current_smooth(current_float, airswitch_shell_frame_current);
    }

    airswitch_current = current_float / SAMPLE_AMPLIFY_INDEX;

    check_current_valid(&airswitch_current, airswitch_shell_frame_current);

    if (airswitch_current > 0)
    {
        amplify_current = (int)(airswitch_current * ROUND_TWO_DECIMAL + 0.5);
    }
    else
    {
        amplify_current = (int)(airswitch_current * ROUND_TWO_DECIMAL - 0.5);
    }

    airswitch_current = (float)amplify_current / ROUND_TWO_DECIMAL;

    set_one_data(SWITCH_DATA_ID_CURRENT, &airswitch_current);

    return airswitch_current;
}



Static unsigned char disconnect_status_appear_judge(void)
{
    unsigned char loop_status = 0, real_over_voltage_alarm_status = 0;
    unsigned char disconnect_alarm_status = 0;
    get_one_data(SWITCH_DATA_ID_LOOP_STATUS, &loop_status);
    get_one_para(SWITCH_PARA_ID_OVER_VOLTAGE_PROTECT_STATUS_OFFSET, &real_over_voltage_alarm_status);

    if (loop_status == LOOP_OPEN && real_over_voltage_alarm_status == TRUE)
    {
        disconnect_alarm_status = TRUE;
        set_one_data(SWITCH_DATA_ID_DISCONNECT_STATUS, &disconnect_alarm_status);
    }
    else
    {
        normal_disconnect_status_appear();
    }
    return TRUE;
}

Static unsigned char normal_disconnect_status_appear(void)
{
    unsigned char airswitch_disconnect_judgement_mode = 0;
    unsigned char loop_status = 0, disconnect_alarm_status = 0,current_status = 0;
    unsigned char previous_loop_status = 0, previous_current_status = 0;
    static unsigned char cnt = 0;
    float terminal_voltage = 0, current = 0;

    // 获取参数数据
    get_one_para(SWITCH_PARA_ID_DISCONNECT_JUDGMENT_MODE_OFFSET, &airswitch_disconnect_judgement_mode);
    get_one_para(SWITCH_PARA_ID_PREVIOUS_LOOP_STATUS_OFFSET, &previous_loop_status);
    get_one_para(SWITCH_PARA_ID_PREVIOUS_CURRENT_STATUS_OFFSET, &previous_current_status);

    // 获取实时数据
    get_one_data(SWITCH_DATA_ID_CURRENT, &current);
    get_one_data(SWITCH_DATA_ID_LOOP_STATUS, &loop_status);
    get_one_data(SWITCH_DATA_ID_TERMINAL_VOLTAGE, &terminal_voltage);

    if (airswitch_disconnect_judgement_mode == DIRECT_JUDGEMENT)
    {
        if (previous_loop_status == LOOP_CLOSE && loop_status == LOOP_OPEN)
        {
            disconnect_alarm_status = TRUE;
            set_one_data(SWITCH_DATA_ID_DISCONNECT_STATUS, &disconnect_alarm_status);
        }
    }
    else 
    {
        if (previous_loop_status == LOOP_CLOSE && loop_status == LOOP_OPEN)
        {
            cnt++;
            if ((previous_current_status && (if_float_equal(current, 0))) ||
                ((terminal_voltage >= 20.0f) && (FRAME_CURRENT_63A != judge_frame_current())))
            {
                cnt = DISCONNECT_ALM_DEBOUNCE;
                disconnect_alarm_status = TRUE;
                set_one_data(SWITCH_DATA_ID_DISCONNECT_STATUS, &disconnect_alarm_status);
            }
        }
    }

    current_status = current > 0 ? TRUE:FALSE;

    if((cnt == 0)||(cnt == DISCONNECT_ALM_DEBOUNCE))
    {
        cnt = 0;
        if((previous_current_status != current_status)||(previous_loop_status != loop_status))
        {
            set_one_para(SWITCH_PARA_ID_PREVIOUS_LOOP_STATUS_OFFSET, &loop_status,TRUE,FALSE);
            set_one_para(SWITCH_PARA_ID_PREVIOUS_CURRENT_STATUS_OFFSET, &current_status,TRUE,FALSE);  
        }
    }

    return disconnect_alarm_status;
}

Static unsigned char disconnect_status_disappear_judge(void)
{
    unsigned char loop_status = 0, disconnect_alarm_status = 0;
    get_one_data(SWITCH_DATA_ID_DISCONNECT_STATUS, &disconnect_alarm_status);
    get_one_data(SWITCH_DATA_ID_LOOP_STATUS, &loop_status);

    if (loop_status == LOOP_CLOSE && disconnect_alarm_status == TRUE)
    {
        disconnect_alarm_status = FALSE;
        set_one_data(SWITCH_DATA_ID_DISCONNECT_STATUS, &disconnect_alarm_status);
    }

    return disconnect_alarm_status;
}


/// @brief 根据SN码信息更新壳架电流
/// @param void
/// @return 壳架电流
unsigned char judge_frame_current(void)
{
    unsigned char airswitch_specification = 0;
    get_one_data(SWITCH_DATA_ID_SPECIFICATION, &airswitch_specification);

    switch(airswitch_specification)
    {
        // 63A壳架的额定电流有16A、32A、63A
        case 0x10:
        case 0x20:
        case 0x3F:
            return FRAME_CURRENT_63A;

        // 125A壳架的额定电流有80A、100A、125A
        case 0x50:
        case 0x64:
        case 0x7D:
            return FRAME_CURRENT_125A;

        // 250A壳架的额定电流有160A、200A、250A
        case 0xA0:
        case 0xC8:
        case 0xFA:
            return FRAME_CURRENT_250A;

        default:
            return 0;
    }
}


// 获取空开功率

Static float get_airswitch_power(void)
{
    float airswitch_power = -1;
    float airswitch_bus_voltage = 0;
    float airswitch_current = 0;

    get_one_data(SWITCH_DATA_ID_CURRENT, &airswitch_current); // 获取小电流清零和平滑处理后的电流(A)
    get_one_data(SWITCH_DATA_ID_BUS_VOLTAGE, &airswitch_bus_voltage); // 获取母排电压(V)
    airswitch_power = fabs(airswitch_bus_voltage * airswitch_current); // 功率(W)

    if(FRAME_CURRENT_63A == judge_frame_current())
    {
        if(airswitch_current < 0) //63A壳架且电流小于0时，功率显示为零
        {
            airswitch_power = 0;
            set_one_data(SWITCH_DATA_ID_POWER, &airswitch_power);
            return airswitch_power;
        }
    }

    if(airswitch_power >= 0 && airswitch_power <= SIXTY_KILOWATT) //功率的显示范围是0-60kW
    {
        airswitch_power = airswitch_power / 1000;
        set_one_data(SWITCH_DATA_ID_POWER, &airswitch_power);
        return airswitch_power;
    }

    if(airswitch_power > SIXTY_KILOWATT)
    {
        airswitch_power = SIXTY_KILOWATT / 1000;  // 若功率大于60kW则按照60kW显示
        set_one_data(SWITCH_DATA_ID_POWER, &airswitch_power);
        return airswitch_power;
    }

    return airswitch_power;
}


unsigned char getGroupAddress(float pin3Voltage, float mainVoltage) {
    char groupAddressReturn = 0;
    unsigned char groupAddress = AIRSWITCH_GROUP_NUM;
    unsigned char baseNumber = AIRSWITCH_GROUP_NUM + 1;
    float errorRange = mainVoltage / 24;

    do{
        if (pin3Voltage > mainVoltage * groupAddress / AIRSWITCH_GROUP_NUM - errorRange && pin3Voltage < mainVoltage * groupAddress / AIRSWITCH_GROUP_NUM + errorRange)
        {
            break;
        }
    }while(--groupAddress);

    groupAddressReturn =  (baseNumber - groupAddress) % baseNumber;

    set_one_data(SWITCH_DATA_ID_GROUP_ADDRESS, &groupAddressReturn);

    return groupAddressReturn;
}

unsigned char getSubGroupAddress(float pin4Voltage, float mainVoltage) {
    char subGroupAddress = AIRSWITCH_SUBGROUP_NUM;
    float errorRange = mainVoltage / 20;

    do{
        if (pin4Voltage > mainVoltage * subGroupAddress / AIRSWITCH_SUBGROUP_NUM - errorRange && pin4Voltage < mainVoltage * subGroupAddress / AIRSWITCH_SUBGROUP_NUM + errorRange)
        {
            break;
        }
    }while(--subGroupAddress);

    set_one_data(SWITCH_DATA_ID_GROUP_INTERNAL_ADDRESS, &subGroupAddress);

    return subGroupAddress;
}

unsigned char getAirSwitchCommAddr(void)
{
    float Volt[3] = {0};
    unsigned char addr = 0,groupAddr = 0 ,subGroupAddr = 0;

    get_adc_channel_volt(Volt);
    groupAddr = getGroupAddress(Volt[0],Volt[2]);
    subGroupAddr = getSubGroupAddress(Volt[1],Volt[2]);

    if (groupAddr == 0 || subGroupAddr == 0)
    {
        addr = AIRSWITCH_DEFAULT_COMM_ADDR;
    }
    else
    {
        if(((groupAddr - 1) * 10 + subGroupAddr) > 4)
        {
            addr = ((groupAddr - 1) * 10 + subGroupAddr) - 3;
        }
        else
        {
            addr = 1;
        }
    }
    
    set_one_data(SWITCH_DATA_ID_SW_SLOT_ADDRESS, &addr);
    return addr;
}

char airswitch_set_addr(void)
{
    static int delay_time = 0;
    static unsigned char cnt = 0, s_src_addr = 0, last_addr = 0;
    static unsigned char  i = DEBOUNCE_COUNT;
    
    RETURN_VAL_IF_FAIL(s_SetAddrFlag == FALSE, FAILURE);

    delay_time++;
    RETURN_VAL_IF_FAIL(delay_time >= EIGHT_SECOND_COUNT, FAILURE);

    if(i--)
    {
        s_src_addr = getAirSwitchCommAddr();

        if(last_addr == s_src_addr){
            cnt++;
        }

        last_addr = s_src_addr;
    }
    else
    {
        if(DEBOUNCE_COUNT - cnt == 1)
        {
            set_host_addr(s_src_addr);
        }
        else
        {
            set_host_addr(AIRSWITCH_DEFAULT_COMM_ADDR);
        }

        s_SetAddrFlag = TRUE;
    }

    return TRUE;
}

char get_setaddrflag(void)
{
    return s_SetAddrFlag;
}

/* 获取空开过流保护自恢复次数 */
Static unsigned char get_over_current_protect_auto_recovery_count(void)
{
    unsigned char rec_count_max_point = 0,rec_count_max = 0,count= 0;
    get_one_para(SWITCH_PARA_ID_OVER_CURRENT_PROTECT_AUTO_RECOVERY_TIMES_OFFSET, &rec_count_max);//自动恢复次数最大值
    get_one_para(SWITCH_PARA_ID_AUTO_RECOVERY_TIMES_SOME_POINT_OFFSET, &rec_count_max_point);//某一时刻自动恢复次数最大值

    if(rec_count_max == 0)
    {
        count= 0;
        set_one_para(SWITCH_PARA_ID_AUTO_RECOVERY_TIMES_SOME_POINT_OFFSET, &count,TRUE,FALSE);//自动恢复次数最大值为0时保存
    }
    else
    {
        if(rec_count_max_point == 0)
        {
            count = 0;
        }
        else
        {
            count = rec_count_max;
        }
    }
    return count;
}

/* 判断空开过流保护状态产生 */
Static char over_current_protect_appear_judge(protect_para_t* para)
{
    float current = 0,alarm_threshold = 0;
    unsigned char alm_status = 0,protect_enabled = 0,rec_count_max = 0;
    unsigned short protect_threshold = 0;
    unsigned char protect_condition = 0;
    get_one_data(SWITCH_DATA_ID_OVER_CURRENT_PROTECT_ALARM,&alm_status);//获取告警
    get_one_data(SWITCH_DATA_ID_CURRENT, &current);//获取电流
    get_one_para(SWITCH_PARA_ID_OVER_CURRENT_ALARM_THRESHOLD_OFFSET, &alarm_threshold);//告警阈值
    get_one_para(SWITCH_PARA_ID_OVER_CURRENT_PROTECT_THRESHOLD_OFFSET, &protect_threshold);//保护阈值
    get_one_para(SWITCH_PARA_ID_OVER_CURRENT_PROTECT_DELAY_OFFSET, &para->appear_delay_thre);//保护延时
    get_one_para(SWITCH_PARA_ID_OVER_CURRENT_PROTECT_ENABLED_OFFSET, &protect_enabled);//过流保护使能
    get_one_para(SWITCH_PARA_ID_OVER_CURRENT_PROTECT_AUTO_RECOVERY_TIMES_OFFSET, &rec_count_max);//自动恢复次数最大值

    protect_condition = (protect_enabled == TRUE && alm_status == FALSE)? TRUE:FALSE;

    //满足过流保护状态更新
    if( protect_condition && (fabsf(current) > alarm_threshold * (protect_threshold * 0.01f)))
    {
        if(para->appear_delay++ >= para->appear_delay_thre * ONE_SECOND_COUNT)//保护延时
        {
            para->protect_state = TRUE;
            set_one_para(SWITCH_PARA_ID_AUTO_RECOVERY_TIMES_SOME_POINT_OFFSET, &rec_count_max,TRUE,FALSE);//保存当前的自动恢复次数最大值
            set_one_para(SWITCH_PARA_ID_OVER_CURRENT_PROTECT_STATUS_OFFSET,&para->protect_state,TRUE,FALSE);//过流保护状态更新为TRUE
            update_fault_opening_times();
            para->appear_delay = 0;//重置保护延时
        }
    }
    else
    {
        para->appear_delay = 0;
        return FALSE;
    }

    return TRUE;
}

/* 判断空开过流保护状态恢复 */
Static char over_current_protect_disappear_judge(protect_para_t* para)
{
    unsigned char loop_status = 0,val_ctrl_status = AIRSWITCH_CONTROL_STATUS_NO_ACT;
    int rec_count_max = 0,rec_delay = 0,rec_count = 0,protect_enabled = 0;
    get_one_para(SWITCH_PARA_ID_CURRENT_PROTECT_AUTO_RECOVERY_SPACE_OFFSET, &rec_delay);//自动恢复间隔
    get_one_para(SWITCH_PARA_ID_OVER_CURRENT_PROTECT_ENABLED_OFFSET, &protect_enabled);//过流保护使能

    get_one_data(SWITCH_DATA_ID_LOOP_STATUS,&loop_status);//获取回路状态
    get_one_data(SWITCH_DATA_ID_CONTROL_STATUS, &val_ctrl_status);

    if(loop_status && val_ctrl_status == AIRSWITCH_CONTROL_STATUS_NO_ACT)
    {
        s_over_current_protect_rec_interval_time = 0;//自动恢复间隔清零
        para->protect_state = FALSE;
        set_one_para(SWITCH_PARA_ID_OVER_CURRENT_PROTECT_STATUS_OFFSET,&para->protect_state,TRUE,FALSE);//过流保护状态更新为FALSE
        return TRUE;
    }
    
    if(protect_enabled == 0 && val_ctrl_status == AIRSWITCH_CONTROL_STATUS_NO_ACT)
    {
        set_airswitch_control_status(AIRSWITCH_CONTROL_STATUS_CLOSE);//控制合闸
        return TRUE;
    }

    if(s_over_current_protect_rec_interval_time++ >= rec_delay * SAMPLE_MIN_TICK)//自动恢复间隔
    {
        s_over_current_protect_rec_interval_time = 0;//自动恢复间隔清零
        rec_count_max = get_over_current_protect_auto_recovery_count(); //获取自恢复上限
        RETURN_VAL_IF_FAIL(rec_count_max != 0,FALSE);//自恢复上限为零直接返回

        get_one_para(SWITCH_PARA_ID_BEFORE_AUTO_RECOVERY_COUNT_OFFSET, &rec_count);//获取自动恢复执行次数
        if(rec_count < rec_count_max)//自恢复执行次数
        {
            set_airswitch_control_status(AIRSWITCH_CONTROL_STATUS_CLOSE);//控制合闸
            rec_count++;
            set_one_para(SWITCH_PARA_ID_BEFORE_AUTO_RECOVERY_COUNT_OFFSET, &rec_count,TRUE,FALSE);//保存自动恢复执行次数
        }
    }

    return TRUE;
}

/* 空开过流保护自动恢复执行次数清零 */
Static char clear_over_current_protect_recovery_count(void)
{
    unsigned char zero_count = 0,rec_count = 0,rec_count_max = 0,loop_status = 0;
    unsigned short protect_threshold = 0;
    float current = 0,alarm_threshold = 0;
    get_one_data(SWITCH_DATA_ID_CURRENT, &current);//获取电流
    get_one_para(SWITCH_PARA_ID_OVER_CURRENT_ALARM_THRESHOLD_OFFSET, &alarm_threshold);//告警阈值
    get_one_para(SWITCH_PARA_ID_OVER_CURRENT_PROTECT_THRESHOLD_OFFSET, &protect_threshold);//保护阈值
    get_one_para(SWITCH_PARA_ID_OVER_CURRENT_PROTECT_AUTO_RECOVERY_TIMES_OFFSET, &rec_count_max);//自动恢复次数最大值
    get_one_para(SWITCH_PARA_ID_BEFORE_AUTO_RECOVERY_COUNT_OFFSET, &rec_count);//自动恢复执行次数
    get_one_data(SWITCH_DATA_ID_LOOP_STATUS,&loop_status);//获取回路状态  

    RETURN_VAL_IF_FAIL(rec_count != 0,FALSE);//自动恢复执行次数为零返回

    if(rec_count_max == 0)
    {
        set_one_para(SWITCH_PARA_ID_BEFORE_AUTO_RECOVERY_COUNT_OFFSET, &zero_count,TRUE,FALSE);//自动恢复执行次数清零
    }
    else
    {
        if(loop_status == TRUE && (fabsf(current) < alarm_threshold * (protect_threshold * 0.01f)))
        {  
            if(s_over_current_protect_rec_clear_time++ >= 30 * SAMPLE_MIN_TICK)//30MIN
            {
                set_one_para(SWITCH_PARA_ID_BEFORE_AUTO_RECOVERY_COUNT_OFFSET, &zero_count,TRUE,FALSE);//执行次数清零
                s_over_current_protect_rec_clear_time = 0;
            }
        }
        else
        {
            s_over_current_protect_rec_clear_time = 0;
            return FALSE;
        }
    }
    return TRUE;
}

/**
 * @brief 空开低压下电告警产生
 * @param[in]
 * @retval
 * @note
 */
Static char low_volt_down_power_appear(protect_para_t* para)
{
    unsigned char communication_fail_status = 0;
    float value = 0;
    float threshold = 0;
    unsigned char power_down_enabled = 0;
    unsigned char loop_status = LOOP_OPEN;
    unsigned char protect_condition = 0;
    get_one_para(SWITCH_PARA_ID_POWER_DOWN_ENABLED_OFFSET, &power_down_enabled);
    get_one_data(SWITCH_DATA_ID_COMMUNICATION_FAIL_STATUS, &communication_fail_status);
    get_one_para(SWITCH_PARA_ID_POWER_DOWN_VOLTAGE_THRESHOLD_OFFSET, &threshold);
    get_one_data(SWITCH_DATA_ID_BUS_VOLTAGE, &value);  //母排端(输入)电压
    get_one_data(SWITCH_DATA_ID_LOOP_STATUS,&loop_status);//获取回路状态
    
    protect_condition = (power_down_enabled && communication_fail_status && loop_status)? TRUE:FALSE;

    if(protect_condition && (value < threshold))
    {
        if(para->appear_delay++ > para->appear_delay_thre)       //产生延时计算
        {
            s_power_off_delay_counter = 0;
            s_control_status_close = 1;
            para->protect_state = TRUE;
            set_one_para(SWITCH_PARA_ID_LOW_VOLTAGE_POWER_DOWN_STATUS_OFFSET, &para->protect_state, TRUE, FALSE);
            para->appear_delay = 0;
        }
    }
    else
    {
        para->appear_delay = 0;
        return FALSE;
    }
    return TRUE;
}


/**
 * @brief 空开低压下电告警恢复
 * @param[in]
 * @retval
 * @note
 */
Static char low_volt_down_power_disappear(protect_para_t* para)
{
    if(TRUE == auto_power_on_judge())
    {
        para->protect_state = FALSE;
        set_one_para(SWITCH_PARA_ID_LOW_VOLTAGE_POWER_DOWN_STATUS_OFFSET, &para->protect_state, TRUE, FALSE);
    }
    return TRUE;
}

char hal_board_gpio_init(void)
{
    rt_pin_mode(PIN_LED_RED_CONTROL, PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_LED_GREEN_CONTROL, PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_LED_BLUE_CONTROL, PIN_MODE_OUTPUT);

    return TRUE;
}

Static char meter_device_find(void)
{
    s_meter_dev = rt_device_find(METER_NAME);
    if(s_meter_dev == RT_NULL)
    {
        return FALSE;
    }
    rt_device_open(s_meter_dev, RT_DEVICE_OFLAG_RDONLY);
    return TRUE;

}

/* 上电时状态同步 */
Static char status_synchronization_when_power_on(void)
{
    unsigned char power_on_power_down_command=0;
    unsigned char loop_status=0;
    unsigned char power_down_status=0;
    unsigned char authorization = 0;
    unsigned char down_power_status = 0;

    get_one_para(SWITCH_PARA_ID_POWER_ON_POWER_DOWN_COMMAND_OFFSET, &power_on_power_down_command);
    get_one_para(SWITCH_PARA_ID_POWER_ON_AUTHORIZATION_OFFSET, &authorization);
    get_one_data(SWITCH_DATA_ID_DOWN_POWER_ALARM, &down_power_status);
    loop_status = rt_pin_read(PIN_MOTOR_VSC);    // 获取空开回路状态
    loop_status = (loop_status == PIN_HIGH) ? LOOP_OPEN : LOOP_CLOSE;

    // 下电状态先置位为回路状态
    if(loop_status == LOOP_OPEN)
    {
        power_down_status = AIRSWITCH_POWER_DOWN;
    }
    else
    {
        power_down_status = AIRSWITCH_POWER_ON;
    }
    // 如果上电时，空开授权状态为未授权，置位下电状态为下电，若空开闭合，控制分闸
    if(authorization == UNAUTHORIZATION_STATUS)
    {
        power_down_status = AIRSWITCH_POWER_DOWN;
        if(loop_status == LOOP_CLOSE)
        {
            set_airswitch_control_status(AIRSWITCH_COMMAND_STATUS_UNAUTHORIZE);
        }
    }
    // 如果上电时，上电下电控制命令为上电，但是空开断开，置位手动断开标志，下电状态置为上电
    else if(power_on_power_down_command == AIRSWITCH_POWER_ON && loop_status == LOOP_OPEN)
    {
        s_manual_disconnection_flag = TRUE;
        power_down_status = AIRSWITCH_POWER_ON;
        set_one_para(SWITCH_PARA_ID_MANUAL_DISCONNECTION_FLAG_OFFSET, &s_manual_disconnection_flag, TRUE, FALSE);
        if(down_power_status == TRUE)
        {
            power_down_status = AIRSWITCH_POWER_DOWN;
        }
    }
    set_one_data(SWITCH_DATA_ID_DOWNLOAD_STATUS, &power_down_status);
    set_one_para(SWITCH_PARA_ID_POWER_ON_POWER_DOWN_COMMAND_OFFSET, &power_down_status, TRUE, FALSE);
    return 0;
}

/* 判断授权状态变化 */
Static char judge_authorization_status(void)
{
    static unsigned char last_authorization_status = UNAUTHORIZATION_STATUS;
    static unsigned char last_down_authorization_status = AUTHORIZATION_STATUS;
    unsigned char authorization_status = UNAUTHORIZATION_STATUS;
    unsigned char power_down_status = 0;
    unsigned char loop_status = 0;
    unsigned char power_on_power_down_command = 0;
    int input_power_energy = 0;
    int output_power_energy = 0;

    get_one_para(SWITCH_PARA_ID_POWER_ON_AUTHORIZATION_OFFSET, &authorization_status);
    get_one_para(SWITCH_PARA_ID_POWER_ON_POWER_DOWN_COMMAND_OFFSET, &power_on_power_down_command);
    get_one_data(SWITCH_DATA_ID_OUTPUT_POWER_ENERGY,&output_power_energy);
    get_one_data(SWITCH_DATA_ID_INPUT_POWER_ENERGY,&input_power_energy);
    get_one_data(SWITCH_DATA_ID_LOOP_STATUS, &loop_status);

    if(last_authorization_status != authorization_status)
    {
        // 授权->未授权
        if(authorization_status == UNAUTHORIZATION_STATUS)
        {
            set_one_para(SWITCH_PARA_ID_POWER_ON_AUTHORIZATION_OFFSET, &authorization_status, TRUE, FALSE);
            save_power_energy();
            s_power_off_delay_counter = 0;
            s_control_status_close = 1;
            // 合闸或分闸状态，未授权控制，将空开机械锁死
            set_airswitch_control_status(AIRSWITCH_COMMAND_STATUS_UNAUTHORIZE);
        }
        // 未授权->授权
        else
        {
            set_one_para(SWITCH_PARA_ID_POWER_ON_AUTHORIZATION_OFFSET, &authorization_status, TRUE, FALSE);
            //last_down_authorization_status主要用于避免上电执行此步骤时将s_authorization_status_change错误置位的bug
            if(last_down_authorization_status != authorization_status)
            {
                s_authorization_status_change = 1;
            }
            // 控制电机到自由位置状态
            set_airswitch_control_status(AIRSWITCH_COMMAND_STATUS_AUTHORIZE);
        }
    }
    last_authorization_status = authorization_status;
    last_down_authorization_status = authorization_status;
    if(loop_status == LOOP_CLOSE && s_authorization_status_change == 1)
    {
        power_down_status = AIRSWITCH_POWER_ON;
        set_one_data(SWITCH_DATA_ID_DOWNLOAD_STATUS, &power_down_status);
        s_authorization_status_change = 0;
        set_one_para(SWITCH_PARA_ID_POWER_ON_POWER_DOWN_COMMAND_OFFSET, &power_down_status, TRUE, FALSE);
    }
    if(s_authorization_status_change == 1)
    {
        auto_power_on_judge();
    }
    if(authorization_status == UNAUTHORIZATION_STATUS)
    {
        // 空开下电状态置为下电
        power_down_status = AIRSWITCH_POWER_DOWN;
        set_one_data(SWITCH_DATA_ID_DOWNLOAD_STATUS, &power_down_status);

        if(loop_status == LOOP_OPEN)
        {
            set_one_para(SWITCH_PARA_ID_POWER_ON_POWER_DOWN_COMMAND_OFFSET, &power_down_status, TRUE, FALSE);
        }
        unauthorization_clear_alarm();
    }
    return SUCCESSFUL;
}


Static char over_temp_appear_judge(protect_para_t* para)
{
    unsigned char over_temp_status = 0;
    unsigned char real_over_temperature_alarm_status = 0;
    float contact_temperature, internal_temperature, bus_temperature;

    //更新实时数据
    get_one_data(SWITCH_DATA_ID_CONTACT_TEMPERATURE, &contact_temperature);
    get_one_data(SWITCH_DATA_ID_INTERNAL_TEMPERATURE, &internal_temperature);
    get_one_data(SWITCH_DATA_ID_BUS_TEMPERATURE, &bus_temperature);
    //更新实时告警
    get_one_data(SWITCH_DATA_ID_OVER_TEMPERATURE_PROTECT_ALARM, &real_over_temperature_alarm_status);
    RETURN_VAL_IF_FAIL(real_over_temperature_alarm_status != 1,FALSE);//告警存在直接返回

    over_temp_status = (contact_temperature > CONTACT_OVER_TEMPERATURE || internal_temperature > INTERNAL_OVER_TEMPERATURE || bus_temperature  > INTERNAL_OVER_TEMPERATURE );  //触点温度大于100或内部温度或母排温度大于110
    if(over_temp_status == 1)
    {
        para->appear_delay ++;
        if(para->appear_delay >= FIVE_SECOND_COUNT)
        {
            para->appear_delay = 0;
            para->protect_state = over_temp_status;
            set_one_para(SWITCH_PARA_ID_OVER_TEMPERATURE_PROTECT_STATUS_OFFSET, &para->protect_state,TRUE,FALSE);
            update_fault_opening_times();
            return TRUE;
        }
    }
    else
    {
        para->appear_delay = 0;
    }
    return FALSE;
}


Static char over_temp_disappear_judge(protect_para_t* para)
{
    unsigned char loop_status;
    unsigned char real_over_temperature_alarm_status = 0;
    //获取回路状态
    get_one_data(SWITCH_DATA_ID_LOOP_STATUS, &loop_status);
    //更新实时告警
    get_one_data(SWITCH_DATA_ID_OVER_TEMPERATURE_PROTECT_ALARM, &real_over_temperature_alarm_status);
    if(real_over_temperature_alarm_status == 1 && loop_status == LOOP_CLOSE)
    {
        para->protect_state = 0;
        set_one_para(SWITCH_PARA_ID_OVER_TEMPERATURE_PROTECT_STATUS_OFFSET, &para->protect_state,TRUE,FALSE);
        return TRUE;
    }
    return FALSE;
}

Static char over_voltage_appear_judge(protect_para_t* para)
{
    unsigned char over_voltage_status = 0;
    unsigned char real_over_voltage_alarm_status = 0;
    float terminal_voltage, bus_voltage;

    //更新实时数据
    get_one_data(SWITCH_DATA_ID_TERMINAL_VOLTAGE, &terminal_voltage);
    get_one_data(SWITCH_DATA_ID_BUS_VOLTAGE, &bus_voltage);
    //更新实时告警
    get_one_data(SWITCH_DATA_ID_OVER_VOLTAGE_PROTECT_ALARM, &real_over_voltage_alarm_status);
    RETURN_VAL_IF_FAIL(real_over_voltage_alarm_status != 1,FALSE);//告警存在直接返回

    over_voltage_status = (terminal_voltage > OVER_VOLTAGE_THRESHOLD || bus_voltage > OVER_VOLTAGE_THRESHOLD);  //母排电压或接线端电压>60V持续3s
    if(over_voltage_status == 1)
    {
        para->appear_delay ++;
        if(para->appear_delay >= THREE_SECOND_COUNT)
        {
            para->appear_delay = 0;
            para->protect_state = over_voltage_status;
            set_one_para(SWITCH_PARA_ID_OVER_VOLTAGE_PROTECT_STATUS_OFFSET, &para->protect_state,TRUE,FALSE);
            update_fault_opening_times();
            return TRUE;
        }
    }
    else
    {
        para->appear_delay = 0;
    }
    return FALSE;
}

Static char over_voltage_disappear_judge(protect_para_t* para)
{
    unsigned char loop_status;
    unsigned char real_over_voltage_alarm_status = 0;
    //获取回路状态
    get_one_data(SWITCH_DATA_ID_LOOP_STATUS, &loop_status);
    //更新实时告警
    get_one_data(SWITCH_DATA_ID_OVER_VOLTAGE_PROTECT_ALARM, &real_over_voltage_alarm_status);
    if(real_over_voltage_alarm_status == 1 && loop_status == LOOP_CLOSE)
    {
        para->protect_state = 0;
        set_one_para(SWITCH_PARA_ID_OVER_VOLTAGE_PROTECT_STATUS_OFFSET, &para->protect_state,TRUE,FALSE);
        return TRUE;
    }
    return FALSE;
}

Static char clear_protect_status_judge_dealy(void)
{
    unsigned char i = 0;
    for (i = 0; i < sizeof(s_protect_status_sample_map) / sizeof(s_protect_status_sample_map[0]); i++)
    {
        s_protect_status_sample_map[i].protect_para.appear_delay = 0;
    }
    return SUCCESSFUL;
}

//因为过压保护、过流保护、过温保护、过载保护动作分闸时，更新故障分闸次数
Static unsigned char update_fault_opening_times(void)
{
    unsigned short fault_opening_times = 0;
    // 获取当前故障分闸次数
    get_one_para(SWITCH_PARA_ID_FAULT_OPENING_TIMES_OFFSET, &fault_opening_times);
    //更新故障分闸次数
    fault_opening_times ++;
    if(fault_opening_times <= MAX_FAULT_OPENING_TIMES)
    {
        set_one_para(SWITCH_PARA_ID_FAULT_OPENING_TIMES_OFFSET, &fault_opening_times, TRUE, FALSE);
        return TRUE;
    }
    else
    {
        fault_opening_times = 0;    //故障分闸次数达到上限后自动清0
        set_one_para(SWITCH_PARA_ID_FAULT_OPENING_TIMES_OFFSET, &fault_opening_times, TRUE, FALSE);
        return FALSE;
    }
}

/* 未授权清除相关告警 */
unsigned char unauthorization_clear_alarm(void)
{
    unsigned char status = 0;
    unsigned char i = 0;

    for (i = 0; i < sizeof(s_protect_status_sample_map) / sizeof(s_protect_status_sample_map[0]); i++)
    {
        s_protect_status_sample_map[i].protect_para.protect_state = FALSE;
       
    }

    // 清除告警
    set_one_data(SWITCH_DATA_ID_LOW_VOLTAGE_DOWN_POWER_ALARM, &status);
    set_one_data(SWITCH_DATA_ID_DOWN_POWER_ALARM, &status);
    set_one_data(SWITCH_DATA_ID_OVER_CURRENT_PROTECT_ALARM, &status);
    set_one_data(SWITCH_DATA_ID_OVER_TEMPERATURE_PROTECT_ALARM, &status);
    set_one_data(SWITCH_DATA_ID_OVERLOAD_TRIP_ALARM, &status);
    set_one_data(SWITCH_DATA_ID_OVER_VOLTAGE_PROTECT_ALARM, &status);
    set_one_data(SWITCH_DATA_ID_DISCONNECTION_ALARM, &status);
    set_one_data(SWITCH_DATA_ID_OVER_CURRENT_ALARM, &status);
    set_one_data(SWITCH_DATA_ID_CONTACT_OVER_TEMPERATURE_ALARM, &status);
    set_one_data(SWITCH_DATA_ID_INTERNAL_OVER_TEMPERATURE_ALARM, &status);
    set_one_data(SWITCH_DATA_ID_CONNECTED_REVERSELY_ALARM, &status);
    set_one_data(SWITCH_DATA_ID_CLOSE_FAILURE_ALARM, &status); 

    // 清除告警状态
    set_one_data(SWITCH_DATA_ID_POWER_DOWN_ALARM_STATUS, &status);
    set_one_data(SWITCH_DATA_ID_DISCONNECT_STATUS, &status);
    set_one_data(SWITCH_DATA_ID_SWITCH_POWER_REVERSE_STATUS, &status);
    set_one_para(SWITCH_PARA_ID_LOW_VOLTAGE_POWER_DOWN_STATUS_OFFSET, &status,TRUE,FALSE);
    set_one_para(SWITCH_PARA_ID_OVER_CURRENT_PROTECT_STATUS_OFFSET, &status,TRUE,FALSE);
    set_one_para(SWITCH_PARA_ID_OVER_TEMPERATURE_PROTECT_STATUS_OFFSET, &status,TRUE,FALSE);
    set_one_para(SWITCH_PARA_ID_OVERLOAD_TRIP_STATUS_OFFSET, &status,TRUE,FALSE);
    set_one_para(SWITCH_PARA_ID_OVER_VOLTAGE_PROTECT_STATUS_OFFSET, &status,TRUE,FALSE);
    
    return TRUE;
}

/**
 * @brief 快速数据采样
*/
Static char realgather_data_sample(void)
{
    check_micro_movement_status();
    check_switch_power_status();
    sync_state_update();
    get_adc_channel_temp();
    judge_authorization_status();

    return SUCCESSFUL;
}

/**
 * @brief 慢速数据采样
*/
Static char lowgather_data_sample(void)
{
    judge_airswitch_measurechip_date();
    get_airswitch_voltage();
    airswitch_set_addr();
    get_airswitch_current();
    get_airswitch_electric();
    get_airswitch_power();
    check_power_status();
    save_energy_time();
    deal_north_command();

    return SUCCESSFUL;
}

/**
 * @brief 处理北向控制命令
*/
Static char deal_north_command(void)
{
    unsigned char north_cmd_state = AIRSWITCH_CONTROL_STATUS_NO_ACT;
    get_one_data(SWITCH_DATA_ID_NORTH_CMD_STATUS, &north_cmd_state);
    set_airswitch_control_status(north_cmd_state);

    //  复位北向命令
    north_cmd_state = AIRSWITCH_CONTROL_STATUS_NO_ACT;
    set_one_data(SWITCH_DATA_ID_NORTH_CMD_STATUS, &north_cmd_state);
    return SUCCESSFUL;
}


/**
 * @brief 是否禁止合闸
*/
Static unsigned char judge_forbid_close(void)
{
    unsigned char is_forbid = 0;
    unsigned char i = 0;

    for (i = 0; i < sizeof(s_func_forbid_close) / sizeof(s_func_forbid_close[0]); i++)
    {
        is_forbid = s_func_forbid_close[i]();
        if (TRUE == is_forbid)
        {
            return TRUE;
        }
    }
    return FALSE;
}

/**
 * @brief 是否空开禁止合闸:未授权
*/
Static unsigned char is_forbid_close_by_unauthorize(void)
{
    unsigned char authorize_status = 0;
    get_one_para(SWITCH_PARA_ID_POWER_ON_AUTHORIZATION_OFFSET, &authorize_status);
    if(UNAUTHORIZATION_STATUS == authorize_status)
    {
        return TRUE;
    }
    return FALSE;
}

/**
 * @brief 是否空开禁止分闸或合闸：生产模式
*/
Static unsigned char is_forbid_act_by_apptest(void)
{
    unsigned char apptest_flag = 0;
    get_one_data(SWITCH_DATA_ID_APPTEST_FLAG, &apptest_flag);
    if (TRUE == apptest_flag)
    {
        return TRUE;
    }
    return FALSE;
}

/**
 * @brief 设置空开控制状态
 * @param[in] status 空开控制状态
 */
Static char set_airswitch_control_status(airswitch_control_status_e status)
{
    unsigned char switch_control_status = 0;
    unsigned char is_forbid_close = 0;
    unsigned char is_forbid_open = 0;
    unsigned char loop_status = LOOP_OPEN;

    get_one_data(SWITCH_DATA_ID_LOOP_STATUS, &loop_status);

    if ((status == AIRSWITCH_CONTROL_STATUS_CLOSE) ||
        (status == AIRSWITCH_COMMAND_STATUS_CLOSE))
    {
        is_forbid_close = judge_forbid_close();
        if (TRUE == is_forbid_close)
        {
            return FAILURE;
        }
        switch_control_status = status;

        if (LOOP_OPEN == loop_status)
        {
            set_one_data(SWITCH_DATA_ID_CONTROL_STATUS, &switch_control_status);
            return SUCCESSFUL;
        }
    }
    else if ((status == AIRSWITCH_CONTROL_STATUS_OPEN) ||
             (status == AIRSWITCH_COMMAND_STATUS_OPEN))
    {
        is_forbid_open = is_forbid_act_by_apptest();
        if (TRUE == is_forbid_open)
        {
            return FAILURE;
        }

        switch_control_status = status;
        if (LOOP_CLOSE == loop_status)
        {
            set_one_data(SWITCH_DATA_ID_CONTROL_STATUS, &switch_control_status);
            return SUCCESSFUL;
        }
    }
    else if ((status == AIRSWITCH_COMMAND_STATUS_UNAUTHORIZE) ||
             (status == AIRSWITCH_COMMAND_STATUS_AUTHORIZE))
    {
        switch_control_status = status;
        set_one_data(SWITCH_DATA_ID_CONTROL_STATUS, &switch_control_status);
        return SUCCESSFUL;
    }

    return FAILURE;
}

/// @brief 过载跳闸判断相关标志和累计量恢复初始值
/// @param  无
/// @return
Static unsigned char clean_overload_relevant_data(void)
{
    // 累计能量计算用到的全局变量
    s_energy_calc_start_flag = FALSE;
    s_accumulated_energy = 0.0;

    // 大电流计算用到的全局变量
    s_large_current_start_flag = FALSE;
    s_large_cuurent_duration_tick = 0;
    return TRUE;
}

/// @brief 过载保护恢复判断
/// @param para
/// @return TRUE:过载保护成功恢复，FALSE:继续保持过载保护状态
Static char overload_disappear_judge(protect_para_t* para)
{
    unsigned char loop_status = 0;
    unsigned char overload_protect_status = FALSE;
    unsigned char overload_protect_alarm = FALSE;

    // 获取回路状态
    get_one_data(SWITCH_DATA_ID_LOOP_STATUS, &loop_status);

    // 获取过载跳闸保护告警
    get_one_data(SWITCH_DATA_ID_OVERLOAD_TRIP_ALARM, &overload_protect_alarm);

    // 如果已经产生过载跳闸保护告警并且处于合闸状态，那么需要将对应的过载保护状态置为正常，同时清除过载保护判断时用到的相关计数或者标志位。
    if ((overload_protect_alarm == TRUE) && (loop_status == LOOP_CLOSE))
    {
        para->protect_state = FALSE;
        set_one_para(SWITCH_PARA_ID_OVERLOAD_TRIP_STATUS_OFFSET, &overload_protect_status,TRUE,FALSE);
        return TRUE;
    }
    return FALSE;
}

/// @brief 过载保护产生判断
/// @param  protect_para_t
/// @return
Static char overload_appear_judge(protect_para_t* para)
{
    // 过载跳闸保护状态
    unsigned char overload_protect_status = TRUE;

    // 短时大电流引发的过载跳闸保护
    unsigned char large_current_protect = FALSE;

    // 长时间工作在1.13倍额定电流之上导致的过载跳闸保护
    unsigned char accumulate_energgy_protect = FALSE;

    // 获取当前的电流值
    float realtime_current = 0.0;
    get_one_data(SWITCH_DATA_ID_CURRENT, &realtime_current);

    // 获取额定电流值
    float rated_current = 0.0;
    get_one_data(SWITCH_DATA_ID_RATED_CURRENT, &rated_current);

    // 如果额定电流还没有正确获取到，就不必进行过载跳闸保护判断了。
    if (rated_current < 16)
    {
        return FALSE;
    }

    // 判断是否为大电流引发的过载跳闸保护
    large_current_protect = judge_large_current_cause_protect(realtime_current, rated_current);

    // 判断是否累计能量大于额定能量引发的过载跳闸保护
    accumulate_energgy_protect = judge_accumulate_energy_cause_protect(realtime_current, rated_current);

    // 以上两个条件任意一个引发过载跳闸保护即将过载跳闸状态置位
    if(large_current_protect || accumulate_energgy_protect)
    {
        para->protect_state = TRUE;
        // 过载保护状态判断出来之后清除相关计数和标志
        clean_overload_relevant_data();
        set_one_para(SWITCH_PARA_ID_OVERLOAD_TRIP_STATUS_OFFSET, &overload_protect_status,TRUE,FALSE);
        update_fault_opening_times();
    }
    return TRUE;
}

/// @brief 根据壳架类型计算累计能量过载跳闸保护时间阈值
/// @param s_frame_current ：壳架电流
/// @return 过载跳闸保护时间阈值

Static unsigned char calc_overload_time_thre(unsigned char s_frame_current)
{
    switch(s_frame_current)
    {
        case FRAME_CURRENT_63A:
            s_overload_time_thre = FRAME_TYPE_63A_OVERLOAD_TIME;
            break;
        case FRAME_CURRENT_125A:
            s_overload_time_thre = FRAME_TYPE_125A_OVERLOAD_TIME;
            break;
        case FRAME_CURRENT_250A:
            s_overload_time_thre = FRAME_TYPE_250A_OVERLOAD_TIME;
            break;
        case 0:
            break;
        default :
            break;
    }
    return TRUE;
}


/// @brief 计算累计能量（用于过载跳闸保护状态判断）
/// @param realtime_current 实时电流
/// @param rated_current 额定电流
/// @return 无实际用途，单元测试校验使用
Static unsigned char calc_accumulate_energy(float realtime_current, float rated_current)
{
    // 电流小于比较阈值时的计数
    static unsigned short s_curr_below_thre_cnt = 0;
    // 比较当前电流值和额定电流的关系,满足|实时电流| >= 1.13 * 额定电流时开始进行累计能量计算；
    if(fabs(realtime_current) >= ENERGY_START_CALC_CURR_COFF * rated_current)
    {
        s_curr_below_thre_cnt = 0;
        if(s_energy_calc_start_flag == TRUE)
        {
            // i^2 * t计算累计能量
            s_accumulated_energy += (pow(realtime_current, 2) * SAMPLE_THREAD_DELAY_TIME / 1000);
        }
        else
        {
            s_accumulated_energy = 0.0;
            s_energy_calc_start_flag = TRUE;
        }
    }
    // |实时电流| 小于 1.13 * 额定电流
    else
    {
        /* 开始累计能量后如果发现|实时电流|小于1.13倍额定电流，那么开始进行计数，连续1秒内检测到|实时电流|小于1.13*额定电流时才进行能量清零。 */
        if(s_energy_calc_start_flag == TRUE)
        {
            s_accumulated_energy += (pow(realtime_current, 2) * SAMPLE_THREAD_DELAY_TIME / 1000);
            s_curr_below_thre_cnt++;
        }
        if(s_curr_below_thre_cnt == 5)
        {
            // 累计能量清零
            s_accumulated_energy = 0.0;
            // 停止累计能量计算
            s_energy_calc_start_flag = FALSE;
            s_curr_below_thre_cnt = 0;
        }
    }
    return TRUE;
}

/// @brief 判断是否由于累计能量导致的过载跳闸保护
/// @param realtime_current ：实时电流char
/// @param rated_current ：额定电流
/// @return overload_protect_status：过载跳闸保护状态
Static unsigned char judge_accumulate_energy_cause_protect(float realtime_current, float rated_current)
{
    // 过载跳闸保护状态
    unsigned char overload_protect_status = FALSE;

    // 计算累计能量
    calc_accumulate_energy(realtime_current, rated_current);

    // 累计能量与额定累计能量比较
    if(s_accumulated_energy >= (s_overload_time_thre * pow(2 * rated_current, 2)))
    {
        // 过载跳闸保护状态置位
        overload_protect_status = TRUE;
    }

    return overload_protect_status;
}

/// @brief 判断是否由于短时大电流导致的过载跳闸保护
/// @param realtime_current ：实时电流
/// @param rated_current ：额定电流
/// @return overload_protect_status：过载跳闸保护状态
Static unsigned char judge_large_current_cause_protect(float realtime_current, float rated_current)
{
    // 过载跳闸保护状态
    unsigned char overload_protect_status = FALSE;

    // 如果|实时电流| > 2.6 * 额定电流，并且持续时间超过1s，直接过载跳闸保护；
    if (fabs(realtime_current) > OVER_LOAD_LARGE_CURR_COFF * rated_current)
    {
        if(s_large_current_start_flag == TRUE)
        {
            s_large_cuurent_duration_tick += SAMPLE_THREAD_DELAY_TIME;
        }
        else
        {
            s_large_current_start_flag = TRUE;
            s_large_cuurent_duration_tick = 0;
        }
    }
    else
    {
        s_large_current_start_flag = FALSE;
        s_large_cuurent_duration_tick = 0;
    }
    if(s_large_cuurent_duration_tick >= 1000)
    {
        overload_protect_status = TRUE;
    }
    return overload_protect_status;
}

// 保存输入输出电量到eeprom中
char save_power_energy(void)
{
    power_energy_t power_energy = {0};
    unsigned char buff[8] = {0};

    get_one_data(SWITCH_DATA_ID_INPUT_POWER_ENERGY,&power_energy.input_power_energy);
    get_one_data(SWITCH_DATA_ID_OUTPUT_POWER_ENERGY,&power_energy.output_power_energy);
    rt_memcpy_s(&buff[0], sizeof(buff), &power_energy, sizeof(power_energy_t));
    eeprom_storage_write(&buff[0], sizeof(buff),PART_ELECPARA, TRUE);

    return SUCCESSFUL;
}

/*******************************
 * 获取空开输入/输出电量
 * 不支持计量芯片读取，手动计算
 * *****************************/
Static char get_airswitch_electric(void)
{
    float airswitch_bus_voltage = 0;
    float airswitch_current = 0;
    float airswitch_power = 0;
    static unsigned int input_power_temp = 0;
    static unsigned int output_power_temp = 0;
    int airswitch_electric_input = 0;
    int airswitch_electric_output = 0;
    static rt_tick_t last_sample_time = 0;
    rt_tick_t current_time = 0;
    float time_diff = 0;
    
    // 获取当前时间戳
    current_time = rt_tick_get_millisecond();
    
    // 计算实际的采样时间间隔(ms)
    if (last_sample_time == 0)
    {
        time_diff = SAMPLE_THREAD_DELAY_TIME;
    }
    else
    {
        if (current_time >= last_sample_time)
        {
            time_diff = current_time - last_sample_time;
        }
        else
        {
            // 时间戳溢出或系统时间被修改，使用默认采样间隔
            time_diff = SAMPLE_THREAD_DELAY_TIME;
        }
    }
    last_sample_time = current_time;
    
    get_one_data(SWITCH_DATA_ID_BUS_VOLTAGE, &airswitch_bus_voltage);
    get_one_data(SWITCH_DATA_ID_CURRENT, &airswitch_current);
    get_one_data(SWITCH_DATA_ID_POWER, &airswitch_power);
    get_one_data(SWITCH_DATA_ID_INPUT_POWER_ENERGY, &airswitch_electric_input);
    get_one_data(SWITCH_DATA_ID_OUTPUT_POWER_ENERGY, &airswitch_electric_output);

    if(airswitch_power > 0.001f || if_float_equal(airswitch_power, 0.001f))
    {
        //输入电量，负电流时对应母排电压的功率积分
        if (airswitch_current < 0)
        {
            // 使用实际时间间隔计算
            input_power_temp += airswitch_power * time_diff * 100;
            if( input_power_temp > ONE_WH_POWER)
            {
                airswitch_electric_input += input_power_temp/ONE_WH_POWER;
                input_power_temp = input_power_temp%ONE_WH_POWER;
                set_one_data(SWITCH_DATA_ID_INPUT_POWER_ENERGY, &airswitch_electric_input);
            }
        }
        //输出电量，正电流时对应母排电压的功率积分
        else if (airswitch_current > 0)
        {
            // 使用实际时间间隔计算
            output_power_temp += airswitch_power * time_diff * 100;
            if (output_power_temp > ONE_WH_POWER)
            {
                airswitch_electric_output += output_power_temp/ONE_WH_POWER;
                output_power_temp = output_power_temp%ONE_WH_POWER;
                set_one_data(SWITCH_DATA_ID_OUTPUT_POWER_ENERGY, &airswitch_electric_output);
            }
        }
    }
    
    //显示范围0~700000KWh、累计满时清0
    if (airswitch_electric_input > SEVEN_HUNDRED_THOUSAND_KILOWATT)
    {
        airswitch_electric_input = 0;
        set_one_data(SWITCH_DATA_ID_INPUT_POWER_ENERGY, &airswitch_electric_input);
    }
    
    if (airswitch_electric_output > SEVEN_HUNDRED_THOUSAND_KILOWATT)
    {
        airswitch_electric_output = 0;
        set_one_data(SWITCH_DATA_ID_OUTPUT_POWER_ENERGY, &airswitch_electric_output);
    }

    return SUCCESSFUL;
}

//清除故障分闸次数，北向协议使用
unsigned char clear_fault_opening_times()
{
    unsigned short fault_opening_times = 0;
    set_one_para(SWITCH_PARA_ID_FAULT_OPENING_TIMES_OFFSET, &fault_opening_times, TRUE, FALSE);
    return SUCCESSFUL;
}

Static char sync_state_update(void)
{
    unsigned char authorization = 0;
    unsigned char loop_status = LOOP_OPEN;
    unsigned char download_status = AIRSWITCH_POWER_ON;
    unsigned char val_ctrl_status = AIRSWITCH_CONTROL_STATUS_NO_ACT;
    get_one_data(SWITCH_DATA_ID_DOWNLOAD_STATUS, &download_status);
    get_one_data(SWITCH_DATA_ID_LOOP_STATUS, &loop_status);
    get_one_data(SWITCH_DATA_ID_CONTROL_STATUS, &val_ctrl_status);

    if (AIRSWITCH_POWER_DOWN == download_status && s_authorization_status_change == 0)
    {
        if (loop_status == LOOP_CLOSE && val_ctrl_status == AIRSWITCH_CONTROL_STATUS_NO_ACT)
        {
            get_one_para(SWITCH_PARA_ID_POWER_ON_AUTHORIZATION_OFFSET, &authorization);
            if (authorization != UNAUTHORIZATION_STATUS)
            {
                val_ctrl_status = AIRSWITCH_CONTROL_STATUS_OPEN;
            }
            set_one_data(SWITCH_DATA_ID_CONTROL_STATUS, &val_ctrl_status);
        }
    }

    return SUCCESSFUL;
}

/* 所有保护状态的产生判断 */
Static char protect_status_appear_judge(void)
{
    unsigned char i = 0;

    for (i = 0; i < sizeof(s_protect_status_sample_map) / sizeof(s_protect_status_sample_map[0]); i++)
    {
        s_protect_status_sample_map[i].appear_judge(&s_protect_status_sample_map[i].protect_para);
        if (TRUE == s_protect_status_sample_map[i].protect_para.protect_state)
        {
            // 控制分闸
            set_airswitch_control_status(AIRSWITCH_CONTROL_STATUS_OPEN);
            clear_protect_status_judge_dealy();
            break;
        }
    }
    return SUCCESSFUL;
}

/**
 * @brief 同步保护状态
*/
Static char sync_protect_state(void)
{
    unsigned short i = 0;
    unsigned int id_check = 0;
    get_one_data(SWITCH_DATA_ID_POWER_DOWN_ALARM_STATUS, &s_protect_status_sample_map[0].protect_para.protect_state);
    for (i = 1; i < sizeof(s_protect_status_sample_map) / sizeof(s_protect_status_sample_map[0]); i++)
    {
        id_check = s_protect_status_sample_map[i].protect_para.protect_status_id;
        get_one_para(id_check, &s_protect_status_sample_map[i].protect_para.protect_state);
    }
    return SUCCESSFUL;
}

/* 
 * @brief 获取已存在告警
 * @retVal NULL 无告警，其他：存在的告警
 */
Static protect_status_judge_t* get_exist_protect_status(void)
{
    unsigned short i = 0;

    for (i = 0; i < sizeof(s_protect_status_sample_map) / sizeof(s_protect_status_sample_map[0]); i++)
    {
        if (TRUE == s_protect_status_sample_map[i].protect_para.protect_state)
        {
            return &s_protect_status_sample_map[i];
        }
    }
    return NULL;
}

/**
 * @brief 处理保护状态判断
*/
Static char deal_protct_status_judge(void)
{
    protect_status_judge_t* current_protect_status = NULL;

    // 检查是否已有保护状态置位
    current_protect_status = get_exist_protect_status();

    /* 无保护状态置位时，执行保护状态判断；已有保护状态置位时，执行保护状态恢复判断 */
    if (NULL == current_protect_status)
    {
        // 保护状态产生判断(含比较判断、控制)
        protect_status_appear_judge();
    }
    else
    {
        current_protect_status->disappear_judge(&current_protect_status->protect_para);
    }

    return SUCCESSFUL;
}


void* sample_init_sys(void *param){
    server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(sample_msg_map) / sizeof(msg_map);
    register_server_msg_map(sample_msg_map, server_info);
    meter_device_find();
    sync_protect_state();
    status_synchronization_when_power_on();
    // 更新壳架电流
    s_frame_current = judge_frame_current();
    // 根据壳架电流更新过载跳闸保护的阈值时间
    calc_overload_time_thre(s_frame_current);
    return NULL;
}


void sample_main(void* parameter)
{
    unsigned char delay_time = SAMPLE_THREAD_DELAY_TIME / 2;
    while (is_running(TRUE))
    {
        realgather_data_sample();
        lowgather_data_sample();
        deal_protct_status_judge();
        disconnect_status_appear_judge();
        disconnect_status_disappear_judge();
        clear_over_current_protect_recovery_count();

        rt_thread_mdelay(delay_time);
    }
}
