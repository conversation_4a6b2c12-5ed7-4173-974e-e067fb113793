#include "SingleButton.h"
#include "led.h"
#include "common.h"
#include "prtclDL.h"
#include "para.h"
#include "hisdata.h"
#include "utils_rtthread_security_func.h"
#ifdef LOCAL_NET_UPDATE_ENABLE
#include "download_tcp.h"
#endif
#ifdef __cplusplus
extern "C" {
#endif
static KeyStatus     s_keyState = eKeyStat0;
Static rt_tick_t     s_tCountTick  = 0;
static rt_uint16_t   s_keyCount = 0;
Static Button_struct s_tButton_Single ;
Static  BOOLEAN s_bInitflag = False;
Static rt_uint16_t Button_Scan(void);
Static BYTE Get_ButtonState(void);
Static void BackNormalKeepHis(uint8_t mode);



///**************************************************************************
//* Copyright (C) 2010-2011, ZTE Corporation.
//* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
//* 系统名称：BMS板软件
//* 文件名称：alarm.c
//* 文件说明：按键Button驱动
//* 作    者  ：王梓藤
//* 版本信息：V1.0
//* 设计日期：2022-10-21
//* 修改记录：
//* 日    期      版  本      修改人      修改摘要
//* 其他说明：
//***************************************************************************/
//按键蜂鸣器提示
Static void Button_Buzz_cricle(uint8_t Buzz_Mode)
{
    static int count  , Modechange=0 ;
    static int count_100ms = 0 ;
    if(Modechange != Buzz_Mode)
    {
        Modechange = Buzz_Mode;
        count = 0;
        count_100ms = 0 ;
    }
    else
    {
        count++;
    }
    if(Buzz_Mode != 0 && count_100ms != Buzz_Mode)
    {
        if(count == 20)
        {
            ON_BUZZ(1);
        }
        else if (count > 40)
        {
            ON_BUZZ(0);
            count = 0;
            count_100ms++;
        }
    }
    else
    {
        ON_BUZZ(0);
        if (count > 200)
        {
            count_100ms = 0;
            count = 0;
        }
    }
}

// 单按键情况下由非正常模式回退至正常模式时保存记录
Static void BackNormalKeepHis(uint8_t mode) {
#ifdef SINGLE_BUTTON_ACTION_RECORD_ENABLED
    if (mode == BUT_UPGRADEMODE) {
        SaveKeyUpgradeMannualQuitAction();
    }
#endif
#ifdef LOCAL_NET_UPDATE_ENABLE
    CloseUpdateLink();
#endif
    return ;
}

Static rt_uint16_t Button_Scan(void)
{
#if 0
    if(Bt_test == 1)
    {
        return s_keyState;
    }
#endif
   if (rt_pin_read(SW_DET_Pin) == PIN_LOW)//按键松开
    {
        s_bInitflag  = True;
    }
     switch (s_keyState)
    {
        case eKeyStat0 :
        {
            s_keyCount = 0;
            if (rt_pin_read(SW_DET_Pin) == PIN_HIGH) {
                s_keyState = eKeyStat1;  // 按键可能被按下
            }
            break;
        }
        case eKeyStat1 :
        {
            if (rt_pin_read(SW_DET_Pin) == PIN_HIGH) {
                if(++s_keyCount>5){
                s_keyState = eKeyStat2;  // 确定按键已经被按下防抖50ms
                }
            }
            else {
                s_keyState = eKeyStat0;  // 抖动引起的, 重新进行按键检测
            }
            break;
        }
        case eKeyStat2 :
        {
            if (rt_pin_read(SW_DET_Pin) == PIN_LOW)//按键松开
            {
                s_keyState = eKeyStat0;
            }
        }
        default :
        {
            break;
        }
    }

    return s_keyState;
   
}
//按键持续模式
BOOLEAN PressSustain(rt_tick_t uiStart_Time, rt_tick_t uiEnd_Time, rt_tick_t tick, Button_struct *SingleButton, rt_uint8_t Num)
{
    //计时不满足模式条件
    if (tick < uiStart_Time || (tick > uiEnd_Time && uiEnd_Time != 0))
    {
        return FALSE;
    }

    if (SingleButton->sButton_State.ucState == Button_ONPRESS)//按键按住时
    {
        if (SingleButton->sButton_Mode[Num].ucFuncMode == BUT_UPGRADEMODE) 
        {
            SingleButton->sButton_State.ucBuzzMode = 2; // 特殊处理，升级模式1s响两次
        }
        else
        {
            SingleButton->sButton_State.ucBuzzMode = Num+1;
        }
        SingleButton->sButton_State.ucLed_CurrentMode = SingleButton->sButton_Mode[Num].ucFuncMode;
        if (NULL != SingleButton->sButton_Mode[Num].PressONFunc)
        {
            SingleButton->sButton_Mode[Num].PressONFunc(); //执行开始程序
        }            
    }
    else //按键松开时
    {
        SingleButton->sButton_State.ucCurrentMode = SingleButton->sButton_Mode[Num].ucFuncMode;//当前所处模式
        SingleButton->sButton_State.uiBack_Time   = SingleButton->sButton_Mode[Num].uiBackTime;
        SingleButton->sButton_State.ucNeed_Back   = SingleButton->sButton_Mode[Num].ucNeedBack;
        if (NULL != SingleButton->sButton_Mode[Num].PressOFFFunc)
        {
            SingleButton->sButton_Mode[Num].PressOFFFunc();//执行结束程序
        }
    }
    return TRUE;

}
//按键多次模式
 #if !defined(UNITEST)  
BOOLEAN PressInterval(rt_tick_t tick, uint8_t ucTimes , int Circle_TimeMS , Button_struct *SingleButton, rt_uint8_t Num)
{   
    static uint8_t ucPressTimes = 0;
    static int ucPressDelayCnt = 0 ;
    static BOOLEAN bPressDelayStart = False ;
    if( bPressDelayStart == True)
    {
        ucPressDelayCnt ++;
    }
    //当两次按键间隔超过300次循环或者按键持续时间超过5s，则清空所有计数并返回
    if(ucPressDelayCnt > (2000/Circle_TimeMS) || tick > PRESSS_BUTTON_TIME)
    {
        ucPressTimes = 0 ;
        ucPressDelayCnt = 0;
        bPressDelayStart = False;
        return False;
    }
    if (SingleButton->sButton_State.ucState == Button_ONPRESS)//按键按住时
    {
        bPressDelayStart = False;
        ucPressDelayCnt = 0;
    }
    else if(tick > 0 && tick < PRESS_CONSISENT_TIME)//当按键持续时间在1.5s以内有效
    {
        ucPressTimes ++;
        bPressDelayStart = True;
        if(ucPressTimes >= ucTimes )//连续按大于模式次数
        {
            ucPressTimes = 0;
            bPressDelayStart = False;
            SingleButton->sButton_State.ucCurrentMode = SingleButton->sButton_Mode[Num].ucFuncMode;//当前所处模式
            SingleButton->sButton_State.uiBack_Time   = SingleButton->sButton_Mode[Num].uiBackTime;
            SingleButton->sButton_State.ucNeed_Back   = SingleButton->sButton_Mode[Num].ucNeedBack;
            if (NULL != SingleButton->sButton_Mode[Num].PressOFFFunc)
            {
                SingleButton->sButton_Mode[Num].PressOFFFunc();//执行结束程序
            }
        }
    }
    else{
        return False;
    }
    return True;
}    
#endif

static void Button_BzzpLed(BOOLEAN bflag,Button_struct *SingleButton)
{
   //非按键过程中防止LED与蜂鸣器与当前按键所处模式不一致
    if(bflag == FALSE)
    {
        if(s_tButton_Single.sButton_State.ucLed_CurrentMode != s_tButton_Single.sButton_State.ucCurrentMode)
        {
           s_tButton_Single.sButton_State.ucLed_CurrentMode = s_tButton_Single.sButton_State.ucCurrentMode;
        }
        if(s_tButton_Single.sButton_State.ucBuzzMode != 0)//
        {
           s_tButton_Single.sButton_State.ucBuzzMode = 0;
        }
    }
         //按键按下时的蜂鸣提示
    if(s_tButton_Single.sButton_State.ucState == Button_ONPRESS)
    {
       Button_Buzz_cricle(s_tButton_Single.sButton_State.ucBuzzMode);
    }
}
Button_State *Button_Funtion(int Circle_TimeMS)
{
    rt_tick_t timetick = 0;
    BOOLEAN bAreaFlag = FALSE;
    s_tButton_Single.sButton_State.ucState = Button_OFFPRESS;    
    timetick = Circle_TimeMS * s_tCountTick;
    if(Circle_TimeMS == 0)
    {
        return &(s_tButton_Single.sButton_State);
    }
//执行按键检测，获取按键状态
    s_tButton_Single.sButton_State.ucState = Get_ButtonState();
    //当按键处于非正常模式且按键状态为松开
    if(s_tButton_Single.sButton_State.ucCurrentMode != BUT_NORMAL && s_tButton_Single.sButton_State.ucState == Button_OFFPRESS)
    {
       s_tButton_Single.sButton_State.ucReturnToNormal = 1;
    }
//当按键从正常模式切换
    for(int i = 1; i < s_tButton_Single.ucButton_Mode_Num; i++)
    { 
        //1、如果电池当前的模式与其设定的生效模式不一致的话（例如关机只能在休眠模式下生效，不可通过按键直接从正常模式到关机模式）2、当前的按键模式不可通过按键来生效，例如布防成功，即使按键也不能切换电池模式
        if(s_tButton_Single.sButton_Mode[i].ucWorkStage != s_tButton_Single.sButton_State.ucCurrentMode || s_tButton_Single.sButton_Mode[i].ucBtModeEn== False ) 
        {           
           continue;
        }
        //当满足上面运行环境后，按键才可生效进行模式切换                     
        if(s_tButton_Single.sButton_Mode[i].ucPressMode == PRESS_ONCE_MODE)//单次持续按键逻辑
        {
           bAreaFlag|=PressSustain(s_tButton_Single.sButton_Mode[i].uiStartTime , s_tButton_Single.sButton_Mode[i].uiEndTime,timetick, &s_tButton_Single , i);
        }
#if !defined(UNITEST)           
        else if(s_tButton_Single.sButton_Mode[i].ucPressMode >= PRESS_THRICE_MODE)//连续多次次按键逻辑
        {
           bAreaFlag|=PressInterval(timetick , s_tButton_Single.sButton_Mode[i].ucPressMode, Circle_TimeMS,&s_tButton_Single,i);
            if(s_tButton_Single.sButton_Mode[i].ucFuncMode == s_tButton_Single.sButton_State.ucCurrentMode)
            {
                timetick = 0;
            }
        }
#endif          
    }
    Button_BzzpLed(bAreaFlag, &s_tButton_Single);
    //此逻辑表示电池显示已处于非正常模式时：
    //1、当按键按下，所有恢复到正常模式在按键松手前的提示音为间隔响一声直到松开按键 
    //2、如果按键未按下，如果当前模式可以且需要返回直正常模式时计时时间到达设定的返回时间则切换至正常模式
     if( s_tButton_Single.sButton_State.ucReturnToNormal != 0)
     {
       if(Button_Scan() == eKeyStat2)
       { 
          s_tButton_Single.sButton_State.ucBuzzMode = 1;
       }
       else if(s_tButton_Single.sButton_State.ucNeed_Back !=0 && timetick > s_tButton_Single.sButton_State.uiBack_Time)
       {
          BackNormalKeepHis(s_tButton_Single.sButton_State.ucCurrentMode);
          rt_memset_s(&s_tButton_Single.sButton_State ,sizeof(s_tButton_Single.sButton_State), 0 ,sizeof(s_tButton_Single.sButton_State));
       }
     }

    return &(s_tButton_Single.sButton_State);
}

Static BYTE Get_ButtonState(void)
{
    if(Button_Scan() == eKeyStat2 && s_bInitflag == True )
    {
       s_tCountTick++;
       return Button_ONPRESS;
    }
    else
    {
        s_tCountTick = 0;
        s_tButton_Single.sButton_State.ucBuzzMode = 0;
        return Button_OFFPRESS;    
    }
}

//注册按键功能 参数包括：开始时间， 结束时间， 按键模式(快速或者连续按） ，按键功能模式，按键触发时的功能实现， 按键结束时的功能实现
rt_err_t Button_Regist_Mode_s(Button *Func)
{
    if(s_tButton_Single.ucButton_Mode_Num > 7)
    {
        return RT_ENOMEM;
    }
    if((Func->uiStartTime > Func->uiEndTime && Func->uiEndTime != False) || Func->ucPressMode==0)
    {
        return RT_ERROR ;
    }
    Func->ucBtModeEn = True;//按键方式进入的模式初始化使能
    if(s_tButton_Single.ucButton_Mode_Num==0)
    {
        rt_memset_s(&s_tButton_Single, sizeof(s_tButton_Single), 0, sizeof(s_tButton_Single));//初始化单按键
        s_tButton_Single.ucButton_Mode_Num = 1;
    }
    s_tButton_Single.sButton_Mode[s_tButton_Single.ucButton_Mode_Num++] = *Func;

    return RT_EOK;
}
//对外接口可失效某种按键模式
void Set_Mode_Work(uint8_t ucModeID, uint8_t ucEnabled)
{
    for(int i = 0 ; i < s_tButton_Single.ucButton_Mode_Num; i++)
    {
        if(ucModeID == s_tButton_Single.sButton_Mode[i].ucFuncMode)
        {
            s_tButton_Single.sButton_Mode[i].ucBtModeEn = ucEnabled;
        }
    }
}
//当前接口用于业务应用层强制转换电池状态（原因为bms电池管理状态需要与电池显示状态保持一致）
void Button_Mode_Set(uint8_t ucModeID)
{
    if(ucModeID == BUT_NORMAL )
    {
        s_tButton_Single.sButton_State.ucReturnToNormal = 0;
    }
    for(int i = 0 ; i < s_tButton_Single.ucButton_Mode_Num; i++)
    {
        if(ucModeID == s_tButton_Single.sButton_Mode[i].ucFuncMode)
        {
            s_tButton_Single.sButton_State.ucCurrentMode = s_tButton_Single.sButton_Mode[i].ucFuncMode;//当前所处模式
            s_tButton_Single.sButton_State.uiBack_Time = s_tButton_Single.sButton_Mode[i].uiBackTime;
            s_tButton_Single.sButton_State.ucNeed_Back = s_tButton_Single.sButton_Mode[i].ucNeedBack;
            s_tButton_Single.sButton_State.ucLed_CurrentMode = s_tButton_Single.sButton_Mode[i].ucFuncMode;
        }
    }
}
void GetButtonState(Button_State* tButSta)
{
    rt_memcpy((CHAR *)tButSta, (CHAR *)&s_tButton_Single.sButton_State ,sizeof(Button_State));
}
//LED灯
 void	CtrlLedOn(uint8_t  ucLedID, uint8_t  bTrue)
{
    if (ucLedID > CAP4_LED_TYPE)
    {
        return;
    }
#ifndef KW_CHECK
    switch(ucLedID)
    {
        case RUN_LED_TYPE:
            rt_pin_write(LED_RUN_PIN, !bTrue);
            break;
        case ALM_LED_TYPE:
            rt_pin_write(LED_ALM_PIN, !bTrue);
            break;
        //原理图SOC1-SOC4标反了，暂时这样处理
        case CAP1_LED_TYPE:
            rt_pin_write(LED_SOC4_PIN, !bTrue);
            break;
        case CAP2_LED_TYPE:
            rt_pin_write(LED_SOC3_PIN, !bTrue);
            break;
        case CAP3_LED_TYPE:
            rt_pin_write(LED_SOC2_PIN, !bTrue);
            break;
        case CAP4_LED_TYPE:
            rt_pin_write(LED_SOC1_PIN, !bTrue);
            break;
        default:
            break;
    }
#endif
    return;
}
 //干结点控制
 void Realy_Control(uint16_t val)
 {
     rt_base_t pin_state1 = 0 , pin_state2 = 0;
     pin_state1 = (rt_base_t)(val & 0x01);
     pin_state2 = (rt_base_t)(val & 0x02)>>1;
     rt_pin_write(RELAY1_PIN, pin_state1);
     rt_pin_write(RELAY2_PIN, pin_state2);
 }
 
void ReturnToNormalMode(BYTE ucRtnReason)
{
    if(s_tButton_Single.sButton_State.ucCurrentMode == BUT_UPGRADEMODE)
    {
        rt_memset_s(&s_tButton_Single.sButton_State, sizeof(s_tButton_Single.sButton_State), 0, sizeof(s_tButton_Single.sButton_State));
        s_tCountTick  = 0;
        //避免无法进行再次升级，需要将s_bSwUpgrade标志置为Flase。
        #ifdef DEVICE_USING_R321
        SetSwUpgradeFlag(False);
        #endif
    }
}

//布防为开启，通过按键无法休眠，回到正常模式
void SleepReturnToNormalMode(BYTE ucRtnReason)
{
    if(s_tButton_Single.sButton_State.ucCurrentMode == BUT_SLEEP)
    {
        rt_memset_s(&s_tButton_Single.sButton_State ,sizeof(s_tButton_Single.sButton_State) ,0 ,sizeof(s_tButton_Single.sButton_State));
        s_tCountTick  = 0;
    }
}

#ifdef __cplusplus
 }
#endif

