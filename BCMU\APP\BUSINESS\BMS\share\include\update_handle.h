#ifndef _UPDATE_HANDLE_H
#define _UPDATE_HANDLE_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

//#include "data_type.h"
#include "rtthread.h"
#include "common.h"

//文件类型

#define UPDATE_BMS_FILE  0      // BMS升级文件
#define UPDATE_BDU_FILE  1      // BDU升级文件
#define PEAK_SHIFT_FILE  2      // 错峰模板文件
#define ILLEGAL_FLIE     3      // 非法文件


//远程下载端口号
#define  DOWNLOAD_PORT  1234
#define  UPDATE_PROTOCOL_NAME           "Proto_UPDATE"
#define  DOWNLOAD_RECBF_SIZE            1500//(8 * 1024)
#define  DOWNLOAD_TRIG_SIZE             18
#define  DOWNLOAD_EXT_TRIG_SIZE         22 // 扩展触发帧大小（去除帧头帧尾）

#define  DOWNLOAD_SOI                   0x7E
#define  DOWNLOAD_FLAG                  0x41
#define  DOWNLOAD_EOI                   0x0D
#define  DOWNLOAD_SHIFT                 0x7D
#define  DOWNLOAD_SHIFT_CODE1           0x7E
#define  DOWNLOAD_SHIFT_CODE2           0x0D
#define  DOWNLOAD_SHIFT_CODE3           0x7D
#define  DOWNLOAD_SHIFT_CODE4           0x10
#define  DOWNLOAD_SHIFT_CODE5           0x12
#define  DOWNLOAD_SHIFT_CODE6           0x7F

#define  DOWNLOAD_FLAG_INDEX            0
#define  DOWNLOAD_ADDR_INDEX            1
#define  DOWNLOAD_LEN_INDEX             5
#define  DOWNLOAD_FN1_INDEX             7
#define  DOWNLOAD_FN2_INDEX             9
#define  DOWNLOAD_DEV_INDEX             11
#define  DOWNLOAD_CID_INDEX             12
#define  DOWNLOAD_RTN_INDEX             13
#define  DOWNLOAD_DAT_INDEX             14

#define  DOWNLOAD_FRM_INDEX             0
#define  DOWNLOAD_VER_INDEX             2
#define  DOWNLOAD_TRIG_INDEX            4
#define  DOWNLOAD_EXT_ADDR_INDEX        0
#define  DOWNLOAD_EXT_RESV_INDEX        2
#define  DOWNLOAD_EXT_FRM_INDEX         4
#define  DOWNLOAD_EXT_VER_INDEX         6

#define  EXT_DOWNLOAD_FILE_NAME_LENG    256
#define  DOWNLOAD_FILE_NAME_LENG        40
#define  DOWNLOAD_FILE_TIME_LENG        20

#define   DOWNLOAD_FLUSH_PER_FRAME      10             ///<  刷新帧 >
#define   DOWNLOAD_BYTES_10M            0xA00000       ///<  剩余最小容量

// 首发帧下载信息
#define  DOWNLOAD_TOTAL_FRAME_INDEX     0  // 发送总帧数
#define  DOWNLOAD_FILE_LENG_INDEX       2  // 下载文件长度
#define  DOWNLOAD_FILE_NAME_INDEX       6  // 文件名
#define  DOWNLOAD_FILE_TIME_INDEX       (DOWNLOAD_FILE_NAME_INDEX + DOWNLOAD_FILE_NAME_LENG)  // 文件时间
#define  DOWNLOAD_FILE_PARA_INDEX       (DOWNLOAD_FILE_TIME_INDEX + DOWNLOAD_FILE_TIME_LENG)  // 参数

// 首发扩展帧下载信息
#define  EXT_DOWNLOAD_TOTAL_FRAME_INDEX     0  // 发送总帧数
#define  EXT_DOWNLOAD_FILE_LENG_INDEX       2  // 下载文件长度
#define  EXT_DOWNLOAD_FILE_NAME_INDEX       6  // 文件名
#define  EXT_DOWNLOAD_FILE_TIME_INDEX       (EXT_DOWNLOAD_FILE_NAME_INDEX + EXT_DOWNLOAD_FILE_NAME_LENG)  // 文件时间
#define  EXT_DOWNLOAD_FILE_CRC_INDEX       (EXT_DOWNLOAD_FILE_TIME_INDEX + DOWNLOAD_FILE_TIME_LENG)  // 文件CRC校验
#define  EXT_DOWNLOAD_FILE_PARA_INDEX       (EXT_DOWNLOAD_FILE_CRC_INDEX + 4)  // 参数

#define  DOWNLOAD_TRIG_FRAME            0x4141
#define  DOWNLOAD_TRIG_CHIP             0x3232
#define  DOWNLOAD_EXT_RESV_FRAME        0x3030
#define  DOWNLOAD_EXT_TRIG_FRAME        0x4141
#define  DOWNLOAD_EXT_TRIG_CHIP         0x3232

#define  DOWNLOAD_TRIG_TIMES            3

#define  DOWNLOAD_RTN_OK                0        // 返回 OK
#define  DOWNLOAD_RTN_CRC_ERR           1        // 返回 CRC 错误
#define  DOWNLOAD_RTN_CMD_ERR           2        // 返回命令错误
#define  DOWNLOAD_RTN_TRG_ERR           3        // 返回触发帧错误

#define  DOWNLOAD_TRIG_TIMEOUT          (30)  // 触发帧超时时间
#define  DOWNLOAD_DATA_TIMEOUT          (30 * 6)  // 数据帧超时时间

#define RAW_BUFF_LEN            1500//(1024 * 8)  // 原始数据缓冲区大小

#define MIN_BUFF_LEN            16          // 数据帧最小长度是 16Bytes
#define LEN_STRING              32          // 字符串长度

#define READ_BYTES              256         // 每段大小256Bytes

#define EARSE_FAN_BYTES       0x1000       //需要擦除的扇区的大小

#define EXE_DOWNLOAD_FILE_NAME_LENG 256

/****错峰文件宏***/
//文件长度
#define FILE_TEMPLATE_LENGTH     1640
#define ONE_FRAME_LENGTH         1024
#define FILE_TOTOAL_FRAME        ((FILE_TEMPLATE_LENGTH + ONE_FRAME_LENGTH)/ONE_FRAME_LENGTH)
#define FILE_NAME_LENGTH         40
#define FILE_TIME_LENGTH         20
#define ONE_TEMPLATE_LENGTH      193

//数据偏移
#define TEMPLATE_NUM             0
#define ELECTRIC_PRICE_MODE      1
#define HOLIDAY_TEMPLATE         2
#define HOLIDAY_CONFIG_DATE      3
#define ELECTRIC_MODE_TEMPLATE   63
#define TEMPLATE_DATA_1          94
#define TEMPLATE_DATA_2          287  
#define TEMPLATE_DATA_3          480 
#define TEMPLATE_DATA_4          673
#define TEMPLATE_DATA_5          866
#define TEMPLATE_DATA_6          1059
#define TEMPLATE_DATA_7          1252
#define TEMPLATE_DATA_8          1445  
/*********************/

/****文件传输状态****/
#define FILE_TRANSLATING        0
#define FILE_TRANSLAT_SUCCESS   1
#define FILE_TARANSLA_FAILURE   2

#define TRANS_PROGRESS_0        0
#define TRANS_PROGRESS_100      100
/*******************/

#define NET_PEAKSHIFT_FILE_NAME  "PEAKSHIFTTEMPLATE.bin"  //网络错峰文件名称

typedef struct {
    BYTE  aucDataBuff[RAW_BUFF_LEN];  ///< 协议层数据接收缓冲区
    WORD wDataLength;                ///< 缓冲区数据长度
}T_PRTCL_REC_BUFF;

typedef struct {
    BYTE  aucDataBuff[RAW_BUFF_LEN]; //  缓存区
    WORD wCheckIndex;               //  缓存区检查指针
    WORD wPktStartIndex;            //  起始位置
    INT32S slDevType;
    UINT32 ulDataLength;              // 缓冲区数据长度
    INT32S bRecHeader;                // 找到帧头标记
    INT32S bRecTail;                  // 找到帧尾标记
}T_CommRecCache;         // 链路层数据缓冲区

typedef struct
{
    INT32S slErrCode;   //错误码
    CHAR   acStr[128];  //错误字符串
}T_UpdateErrorStr;

/*
 * 升级进度状态机
 */
enum {
    UPDATE_STATE_NOEXIT     = 0x00,   ///< 未启动升级
    UPDATE_STATE_SUCCESS    = 0x01,   ///< 升级成功
    UPDATE_STATE_FAILED     = 0x02,   ///< 升级失败
    UPDATE_STATE_PROCESSING = 0x03,   ///< 升级中
};

/*
 * 命令定义
 */
enum
{
    DLCMD_TRIG_FRAME      = 0x01,    ///< 触发帧
    DLCMD_EXT_TRIG_FRAME  = 0x02,    ///< 扩展触发帧
    DLCMD_DOWNLOAD        = 0x10,    ///< FLASH下载
    DLCMD_DOWNLOAD_EXT    = 0x11,    ///< FLASH下载首发信息扩展
    DLCMD_VER_COMPARE = 0x12,        ///< 版本比较
    DLCMD_PAUSE_UPDATE = 0x13,       ///< 暂停升级
    DLCMD_GET_MAC_ADDR_PROC = 0x14,  ///< 获取前台唯一标识
    DLCMD_GET_UPDATE_PROC = 0x15,    ///< 获取升级进度
    DLCMD_GET_BATCH_UPDATE_PROC = 0x1A,    ///< 获取批量升级进度
    ULCMD_GET_FILE_LIST = 0x18,      ///< 获取上传文件列表
    ULCMD_GET_FILE = 0x19,           ///< 获取文件
};

typedef enum
{
    eFrameNone = 0x00,
    eTrigFrame = 0x01,
    eDataFrame = 0x02,
    eGetMacFrame = 0x03,
    eExtTrigFrame = 0x04, //扩展触发帧
    eGetBatchUpdatePro = 0x06, //获取批量升级进度帧（0x05留给获取升级进度帧）
}E_FrameType;

/*
 * 协议层数据帧数据结构
 */
typedef struct
{
    BYTE  ucFlag; // 
    WORD usAddr; // 地址
    WORD usLen;  // DATA 段长度
    WORD usFN1;  // 前台收到的帧号
    WORD usFN2;  // 后台发送的帧号, 前台请求的帧号
    BYTE  ucDev;  // 设备类型
    BYTE  ucCMD;  // 命令类型
    BYTE  ucRtn;  // 返回码
    BYTE  aucBuff[DOWNLOAD_RECBF_SIZE];  // DATA 段数据
}T_UpdateDataStruct;

/*
 * 协议层触发帧数据结构
 */
typedef struct
{
    BYTE aucData[DOWNLOAD_EXT_TRIG_SIZE * 2];
    WORD usLeng;
}T_UpdateTrigStruct;

typedef struct
{
    BYTE              ucCmd;
    E_FrameType        frameType;
    T_UpdateTrigStruct trigFrame;
    T_UpdateDataStruct dataFrame;
}T_UpdateFrameStruct;

/*
 * 命令执行
 */
typedef struct
{
    BYTE ucCmd;
    void (*pFunc)(T_UpdateFrameStruct *pData);
}T_UpdateHandleStruct;

typedef struct
{
    INT32S (*parse)(void);  // 协议解析函数
    INT32S (*doCmd)(void);  // 命令执行函数 - 生成应答
}T_UpdateParseStruct;

/*
 * 升级处理
 */
typedef enum {
    DOWNLOAD_STATE_INIT,      ///< 初始化
    DOWNLOAD_STATE_SAVE,      ///< 保存数据
    DOWNLOAD_STATE_ERR,       ///< 出错
}download_state_e;

typedef struct {
    char aucFileName[64];      ///< 文件名
    char aucVersion[64];       ///< 版本
    char aucReleaseDate[64];   ///< 发布时间
    int  reboot;               ///< 是否重启
}csu_info_t;

typedef struct {
    char aucFileName[64];      ///< 文件名
    int  valid;                ///< 是否有效
}update_file_t;

typedef struct {
    char aucFileName[256];      ///< 文件名
    int  valid;                ///< 是否有效
}extern_update_file_t;

typedef struct {
    char fileTime[32];            ///< 下载时间
    char fileName[256];            ///< 文件名称
    unsigned char  param_type;    ///< 参数类型
    unsigned short frame_cnt;     ///< 已经接收的帧号
    unsigned short total_frames;  ///< 总帧数量
    int write_pos;                ///< 文件位置
    unsigned int total_leng;      ///< 文件总长度
    unsigned int crc;             ///< CRC
}file_info_t;

typedef struct {
    int link_used;          ///<  是否被占用
    int trig_success;       ///<  触发成功
    int fd;                 ///<  文件描述符
    int trig_times;         ///<  触发次数
    file_info_t file_info;  ///<  文件信息>
}download_manager_t;

typedef struct {
    update_file_t      update_file;     // 升级文件
    download_manager_t download_mgr;    // 下载管理器
    download_state_e   download_sta;    // 下载状态机
    rt_timer_t         trigTimer;       // 触发帧超时
    rt_timer_t         dataTimer;       // 下载数据帧超时
    int                taskStart;       // 下载开始标识

    void (*handle_data)(T_UpdateDataStruct *data_info);                ///<  数据处理>
    void (*state_check)(void);                                         ///<  升级状态检测>
}download_handle_t;

typedef struct{
    char file_name[64];         ///<  文件名称
    char dev_type[32];          ///<  设备类型
    int  file_size;             ///<  文件大小
    UINT32 check_sum;           ///<  校验和
}file_head_t;

typedef struct{
    char file_name[256];        ///<  文件名称
    char dev_type[32];          ///<  设备类型
    int  file_size;             ///<  文件大小
    UINT32 check_sum;           ///<  校验和
}extern_file_head_t;

typedef struct{
    BYTE ucFileType;                                                     //文件类型
    char file_name[40];                                                  //文件名称    
    //int (*read_file_info)(file_info_t *pfile_info);                      //读文件
    //void (*write_file_info)(file_info_t *pfile_info, BYTE ucMode);       //写文件
    void (*file_download_process)(T_UpdateDataStruct *pData_info);       //文件下载处理函数
    BOOLEAN (*file_received_process)(T_UpdateDataStruct *pData_info);       //文件接受完毕处理函数
}differnet_file_management;

void InitUpdateProtocol(void);
/* 协议处理函数 */
INT32S parseCmd(void);
INT32S exeCmd(void);
INT32S getDataFromQueue(void);
BOOLEAN GetFileTransFlag(void); //获取文件传输标志 by hxk 2023-02-07
void SetFileTransFlag(BOOLEAN Flag); //设置文件传输标志 by hxk 2023-02-07
int RepeatSendlastFrame(void);
BOOLEAN PeakDataCheckAndSave(void);  //错峰数据校验和保存
BOOLEAN GetExtendTrigFrameFlag(void); //获取扩展触发帧标志

BYTE GetUpgradeFileFlag(void);

BOOLEAN GetPeakFileTransFlag(void);  //获取错峰文件传输标志
BOOLEAN SetPeakFileTransFlag(BOOLEAN Flag); //设置错峰参数标志位
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif

