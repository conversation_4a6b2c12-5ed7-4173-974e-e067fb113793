#include "led.h"
#include "msg.h"
// #include <board.h>
#include <stdlib.h>
#include <string.h>
#include "msg_id.h"
#include <rtthread.h>
#include <rtdevice.h>
#include "data_type.h"
#include "pin_define.h"
#include "signal_led.h"
#include "utils_thread.h"
#include "utils_rtthread_security_func.h"

/* 定义信号灯对象句柄 */
static led_t *led_run =  NULL;

/* 设置信号灯一个周期内的闪烁模式 
 * 格式为 “亮、灭、亮、灭、亮、灭 …………”
 */
static char* led_on_mode = "1000,0,";       ///< 常亮
static char* led_off_mode = "0,1000,";      ///< 常灭
static char* led_blink_mode = "500,500,";  ///< 1Hz闪烁

/* LED控制消息 */
static module_msg_t msg_rcv = {.dest = MOD_LED};

/* 定义开灯函数 */
void led_run_switch_on(void *param) {
    rt_pin_write(PIN_LED_CONTROL, PIN_LOW);
}

/* 定义关灯函数 */
void led_run_switch_off(void *param) {
    rt_pin_write(PIN_LED_CONTROL, PIN_HIGH);
}

/* 初始化闪烁led */
void* init_led_blink(void *param) {
    // main板init
    rt_pin_mode(PIN_LED_CONTROL, PIN_MODE_OUTPUT);
    led_run = led_create(led_run_switch_on, led_run_switch_off, NULL);
    led_set_mode(led_run, LOOP_PERMANENT, led_blink_mode);
    led_start(led_run);
    return NULL;
}

/* led指示灯模式控制 */
Static int led_type_switch(led_t * led_type, unsigned char status)
{
    switch(status)
    {
        case LED_OFF_MODE:     led_set_mode(led_type, LOOP_PERMANENT, led_off_mode);break;
        case LED_ON_MODE :     led_set_mode(led_type, LOOP_PERMANENT, led_on_mode);break;
        case LED_BLINK_MODE:   led_set_mode(led_type, LOOP_PERMANENT, led_blink_mode);break;
        default: return FALSE;
    }
    return TRUE;
}

/* 不同类型led指示灯控制（运行灯，告警灯） */
Static int led_no_switch(unsigned char led_no, unsigned char status) {
    // 运行灯执行
    if (led_no == LED_RUN_STATE) {
        return led_type_switch(led_run, status);
    }
    return FALSE;
}

/* led指示灯控制 */
Static int led_ctrl(module_msg_t* msg) {
    led_ctrl_msg_t data = {0};
    RETURN_VAL_IF_FAIL(msg != NULL, FALSE);
    RETURN_VAL_IF_FAIL(LED_BLINK_CTRL_MSG == msg->msg_id, FALSE);
    RETURN_VAL_IF_FAIL(msg->data != NULL, FALSE);
    rt_memcpy_s(&data,sizeof(led_ctrl_msg_t), msg->data, sizeof(led_ctrl_msg_t));
    free(msg->data);
    msg->data = NULL;
    return led_no_switch(data.led_no, data.ctrl_state);
}

void led_thread_entry(void* parameter) {
    while (is_running(TRUE)){
        rt_memset_s(&msg_rcv, sizeof(msg_rcv), 0x00, sizeof(msg_rcv));
        if (SUCCESSFUL == recv_msg(&msg_rcv)) {
            led_ctrl(&msg_rcv);
        }
        led_ticks();
        rt_thread_mdelay(LED_TICK_TIME);
    }
}
