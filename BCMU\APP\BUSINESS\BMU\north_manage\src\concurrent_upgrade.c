#include "sps.h"
#include "cmd.h"
#include "flash.h"
#include "dev_bcmu.h"
#include "data_type.h"
#include "storage.h"
#include "circle_buff.h"
#include "device_type.h"
#include "update_manage.h"
#include "partition_def.h"
#include "realdata_id_in.h"
#include "protocol_layer.h"
#include "concurrent_upgrade.h"
#include "protocol_bottom_comm.h"
#include "update_download_manage.h"
#include "utils_data_transmission.h"
#include "utils_frame_loss_statistic.h"
#include "utils_rtthread_security_func.h"
#include "file_save.h"


#define UPDATE_FILE_NAME     "bmu_app.bin"
#define RETRANS_MAX_COUNT 20
#define PARALLE_UPDATE_DEBUG    0    // 并发升级调试宏
#define PARALLE_TIMEOUT_TICKS   1500000    ///< 并发升级超时时间数，unit tick, 15分钟
#define DOWN_RESTART_TIMEOUT_TICKS  6000   ///< 并发升级重启等待时间数，unit tick, 6秒
Static const char s_file_name_bin[UPDATE_FILE_NAME_LEN] = UPDATE_FILE_NAME;


static link_type_t link_type_tab[LINK_TYPE_MAX] = {
    {LINK_CAN, can_dev_init, can_dev_read, can_dev_write, can_dev_set},
};


static link_inst_t link_inst_tab[] = {
    {BMU_LINK_BCMU, LINK_CAN, "can1",   CAN_FRAME_DATA_LEN_8},
};

Static unsigned char timer_inited = FALSE;
Static trig_ctr_inf_t s_trig_ctr_inf;
Static unsigned char s_received_finished = FALSE;  // 接收完成
Static T_ParalleState s_paralle_sta = PARALLE_STAT_PREPARE;
Static unsigned short s_last_recived_frame_no = INVALID_FRM_NO;  ///< 上次接收的帧号
Static unsigned short s_request_frame_no = 0;  ///< 请求的帧号,点对点
Static unsigned short s_loss_frame_no = 0;
static rt_tick_t s_tick_start_download = 0;
Static T_FileManageStruct s_file_manage;
Static trig_ctr_inf_t s_trig_ctr_inf;
Static bottom_comm_cmd_head_t* s_proto_head_req = NULL;
Static T_AppDataSaveStruct s_data_to_circle_buff = {0};
//补充超时机制
Static struct rt_timer g_concurrent_update_timer;   ///< 并发升级超时退出升级定时器
Static unsigned char s_restart_delay_active = FALSE;
Static rt_tick_t s_restart_delay_start_tick = 0;

Static int parse_trig_data(void* dev_inst, void *cmd_buf);
Static int pack_trig_data(void* dev_inst, void *cmd_buf);
Static int parse_concur_trans_data(void* dev_inst, void *cmd_buf);
Static int pack_concur_trans_data(void* dev_inst, void *cmd_buf);
Static int parse_concur_data_confirm(void* dev_inst, void *cmd_buf);
Static int pack_concur_data_confirm(void* dev_inst, void *cmd_buf);
Static int parse_concur_update_confirm(void* dev_inst, void *cmd_buf);
Static int pack_concur_update_confirm(void* dev_inst, void *cmd_buf);
Static void deal_parallel_restart_timeout(void);
Static short deal_bmu_paralle_first_frm(void* cmd_buf);
Static short deal_bmu_paralle_data_frm(void* cmd_buf);
Static short record_bmu_paralle_first_frm_loss(void* cmd_buf);
Static short record_bmu_paralle_data_frm_loss(void* cmd_buf);

static cmd_handle_register_t s_concurrent_update_cmd_handle[] = {
    {DEV_BCMU, BMU_UPDATE_DATA_TRIG, CMD_TYPE_NO_POLL, parse_trig_data, pack_trig_data},
    {DEV_BCMU, BMU_UPDATE_DATA,  CMD_TYPE_NO_POLL, parse_concur_trans_data, pack_concur_trans_data},
    {DEV_BCMU, BMU_UPDATE_DATA_COMPLETE_ACK, CMD_TYPE_NO_POLL, parse_concur_data_confirm, pack_concur_data_confirm},
    {DEV_BCMU, BMU_UPDATE_CONFIRM, CMD_TYPE_NO_POLL, parse_concur_update_confirm, pack_concur_update_confirm},
};

int concur_update_init(void) 
{
    short i = 0;
    for(i = 0; i < sizeof(s_concurrent_update_cmd_handle)/sizeof(s_concurrent_update_cmd_handle[0]); i++) 
    {
        register_cmd_handle(&s_concurrent_update_cmd_handle[i]);
    }
    return SUCCESSFUL;
}

/**
 * @brief 并发升级完成重启等待定时处理回调
*/
Static void deal_parallel_restart_timeout(void)
{
    // 跳boot前清除状态
    s_paralle_sta = PARALLE_STAT_PREPARE;
    //跳转boot,执行升级
    FLASH_If_Write(UPDATEINFO_START, (uint32_t *)&s_file_manage, sizeof(T_FileManageStruct));
    BeginDownload(FLAG_NOR_IAP);
}

/**
 * @brief 检查是否达到重启延迟时间并执行重启操作
 */
void check_restart_delay(void)
{
    if (s_restart_delay_active) {
        rt_tick_t current_tick = rt_tick_get();
        if (current_tick - s_restart_delay_start_tick >= DOWN_RESTART_TIMEOUT_TICKS) {
            deal_parallel_restart_timeout();
            s_restart_delay_active = FALSE;
        }
    }
}

Static int parse_trig_data(void* dev_inst, void *cmd_buf)
{
    // 标志位初始化
    s_received_finished = FALSE;
    s_last_recived_frame_no = INVALID_FRM_NO;  // 触发成功，清除上次收到的帧号
    reset_to_init_update();
    clear_frame_loss_info();              // 触发成功，清除丢帧记录
    s_request_frame_no = 0;                  // 触发成功，下一帧首发帧

    char *pcFileName = s_file_manage.tFileAttr.acFileName;
    cmd_buf_t* trig_cmd_buf = (cmd_buf_t*)cmd_buf;
    if ( trig_cmd_buf == NULL || trig_cmd_buf->buf == NULL) {
        return FAILURE;
    }
    unsigned char *p = trig_cmd_buf->buf;
    if (trig_cmd_buf->data_len == UPDATE_FILE_NAME_LEN)
    {
        rt_memcpy_s(pcFileName, UPDATE_FILE_NAME_LEN, s_file_name_bin, UPDATE_FILE_NAME_LEN);
        if (rt_strncmp((char*)p, s_file_name_bin, UPDATE_FILE_NAME_LEN) == 0) {
            s_trig_ctr_inf.rtn = BOTTOM_RTN_APP_CORRECT;
        }
        else
        {
            s_trig_ctr_inf.rtn = DownFileNameErr;
        }
    }
    else
    {
        s_trig_ctr_inf.rtn = DownFileNameErr;
    }
    return SUCCESSFUL;
}

/**
 * @brief 并发升级超时退出升级模式处理
*/
short deal_paralle_update_timeout(void)
{
    s_paralle_sta = PARALLE_STAT_PREPARE;
    s_received_finished = FALSE;
    s_last_recived_frame_no = INVALID_FRM_NO;
    s_request_frame_no = 0;
    s_loss_frame_no = 0;
    s_tick_start_download = 0;
    reset_to_init_update();
    clear_frame_loss_info();
    s_file_manage.ucFlag = FLAG_OK;  // 恢复升级灯状态
    return SUCCESSFUL;
}

/**
 * @brief 并发升级超时处理回调
*/
Static void deal_paralle_timeout(void) {
    deal_paralle_update_timeout();
}

Static short start_paralle_timeout_timer(rt_timer_t pTimer) {
    RETURN_VAL_IF_FAIL(pTimer != NULL, FAILURE);
    // 触发成功进入传输阶段，防止多触发帧情况下频繁启停定时器
    RETURN_VAL_IF_FAIL(s_paralle_sta != PARALLE_STAT_TRANS, SUCCESSFUL);
    if (FALSE == timer_inited) {
        rt_timer_init(pTimer, "ParalleTimeout", (void*)deal_paralle_timeout, RT_NULL, PARALLE_TIMEOUT_TICKS, RT_TIMER_FLAG_ONE_SHOT | RT_TIMER_FLAG_SOFT_TIMER);
        timer_inited = TRUE;
    }
    rt_timer_stop(pTimer);
    rt_timer_start(pTimer);
    return SUCCESSFUL;
}

Static void  paralle_trigger_bmu_pack(void *cmd_buf)
{
    cmd_buf_t* trig_cmd_buf = (cmd_buf_t*)cmd_buf;
    trig_cmd_buf->data_len = UPDATE_FILE_NAME_LEN;
    rt_memcpy_s(trig_cmd_buf->buf, UPDATE_FILE_NAME_LEN, s_file_name_bin, UPDATE_FILE_NAME_LEN);
    start_paralle_timeout_timer(&g_concurrent_update_timer);
    s_paralle_sta = PARALLE_STAT_TRANS;
    if (s_file_manage.ucFlag != FLAG_NOR_IAP)
    {
        s_file_manage.ucFlag = FLAG_NOR_IAP;
    }
}

Static void rec_app_download_rtn_fault(void *cmd_buf)
{
    cmd_buf_t* trig_cmd_buf = (cmd_buf_t*)cmd_buf;
    trig_cmd_buf->data_len = UPDATE_FILE_NAME_LEN;
    trig_cmd_buf->rtn = DownFileNameErr;
    rt_memcpy_s(trig_cmd_buf->buf, UPDATE_FILE_NAME_LEN, s_file_name_bin, UPDATE_FILE_NAME_LEN);
    return ;
}

Static int pack_trig_data(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* trig_cmd_buf = (cmd_buf_t*)cmd_buf;
    if ( trig_cmd_buf == NULL || trig_cmd_buf->buf == NULL) {
        return FAILURE;
    }
    if(s_trig_ctr_inf.rtn == BOTTOM_RTN_APP_CORRECT)
    {
        paralle_trigger_bmu_pack(cmd_buf);
    }
    else
    {
        rec_app_download_rtn_fault(cmd_buf);
    }
    return SUCCESSFUL;
}

Static int parse_concur_trans_data(void* dev_inst, void *cmd_buf)
{
    RETURN_VAL_IF_FAIL(cmd_buf != NULL, FAILURE);
    cmd_buf_t* trig_cmd_buf = (cmd_buf_t*)cmd_buf;
    // 没有触发成功，不接受传输帧
    RETURN_VAL_IF_FAIL(s_paralle_sta != PARALLE_STAT_PREPARE, FAILURE);
    s_proto_head_req = trig_cmd_buf->cmd->req_head;
    if(s_proto_head_req->append == 0)
    {
        // 收到首发帧处理
        deal_bmu_paralle_first_frm(cmd_buf);
    }
    else{
        // 收到数据帧处理
        deal_bmu_paralle_data_frm(cmd_buf);
    }
    RETURN_VAL_IF_FAIL( s_paralle_sta == PARALLE_STAT_DATA_CONFIRM, NO_NEED_REPONSE);//补帧阶段才回包，广播阶段不回包
    return SUCCESSFUL;
}


Static int pack_concur_trans_data(void* dev_inst, void *cmd_buf)
{
    RETURN_VAL_IF_FAIL(s_proto_head_req->res2.addr != 0, NO_NEED_REPONSE);
    cmd_buf_t* trig_cmd_buf = (cmd_buf_t*)cmd_buf;
    bottom_comm_cmd_head_t* proto_head_ack = NULL;
    proto_head_ack = trig_cmd_buf->cmd->ack_head;
    Static unsigned short s_complementary_repeat = 0;

    if (s_proto_head_req->append != s_loss_frame_no) {
        s_complementary_repeat++;
        proto_head_ack->append = s_loss_frame_no;
        return SUCCESSFUL;
    }

    //上位机始终回复非请求帧到20次，设备回到初始状态
    if(s_complementary_repeat >= RETRANS_MAX_COUNT){
        s_paralle_sta = PARALLE_STAT_PREPARE;
        s_complementary_repeat = 0;
        s_file_manage.ucFlag = FLAG_APP;
    }
        
    reset_frame_loss(s_loss_frame_no);
    s_loss_frame_no = get_first_frame_loss();
    if (s_loss_frame_no == INVALID_FRM_NO) {
        s_loss_frame_no = 0;
        proto_head_ack->append = s_file_manage.tFileAttr.wTotalFrameNum;
    }
    else {
        proto_head_ack->append = s_loss_frame_no;
    }
    return SUCCESSFUL;
}

Static int parse_concur_data_confirm(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    unsigned short frame_loss_count_threshold = 0;

    RETURN_VAL_IF_FAIL(s_paralle_sta != PARALLE_STAT_PREPARE, FAILURE);

    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL || dev_inst == NULL)
    {
        return FAILURE;
    }
    
    frame_loss_count_threshold = (tran_cmd_buf->buf[0] << 8) | tran_cmd_buf->buf[1];
    s_paralle_sta = PARALLE_STAT_DATA_CONFIRM;
    if(get_frame_loss_count() > frame_loss_count_threshold)
    {
        //TODO:控制退出升级
        tran_cmd_buf->rtn = NO_NEED_REPONSE;
        return SUCCESSFUL;
    }

    return SUCCESSFUL;
}


Static int pack_concur_data_confirm(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;

    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL || dev_inst == NULL)
    {
        return FAILURE;
    }

    s_loss_frame_no = get_first_frame_loss();
    unsigned short frame_loss_count = get_frame_loss_count();

    if(frame_loss_count == 0)
    {
        tran_cmd_buf->buf[0] = 0;   //数据传输帧接收完整
    }
    else
    {
        tran_cmd_buf->buf[0] = 1;   //数据传输帧不完整
    }
    tran_cmd_buf->buf[1] = (s_loss_frame_no >> 8) & 0xff;
    tran_cmd_buf->buf[2] = s_loss_frame_no & 0xff;
    tran_cmd_buf->buf[3] = (frame_loss_count >> 8) & 0xff;
    tran_cmd_buf->buf[4] = frame_loss_count & 0xff;
    tran_cmd_buf->data_len = 5;
    return SUCCESSFUL;
}

/**
 * @brief 在升级确认状态进行crc整包校验
 * @param[in] NULL  
 * @retval TRUE 校验通过, FALSE 校验不通过
 * @note 校验通过才可以跳转boot进行程序更新
*/

Static char complete_package_crc_check_res(void)
{
    RETURN_VAL_IF_FAIL(s_file_manage.tFileAttr.ulTotalFileLength != 0, FALSE);
    unsigned short crc_cal = crc_cal_nor(NORFLASH_APP_START, s_file_manage.tFileAttr.ulTotalFileLength, NORFLASH_DEV_NAME, sizeof(NORFLASH_DEV_NAME));

    #if PARALLE_UPDATE_DEBUG
    rt_kprintf("crc_cal: %d  s_file_manage.tFileAttr.wFileCheck: %d\n",
               crc_cal, s_file_manage.tFileAttr.wFileCheck);
    #endif

    if (crc_cal == s_file_manage.tFileAttr.wFileCheck)
    {
        #if PARALLE_UPDATE_DEBUG
        rt_kprintf("CRC check success!!\n");
        #endif
        return TRUE;
    }

    #if PARALLE_UPDATE_DEBUG
    rt_kprintf("CRC check failed!!\n");
    #endif

    return FALSE;
}


/**
 * @brief 解析升级确认帧
 * @param[in] dev_inst 设备实例
 * @param[in] cmd_buf 数据缓存
*/

Static int parse_concur_update_confirm(void* dev_inst, void* cmd_buf)
{
    // 验证输入参数的有效性
    if ((NULL == dev_inst) || (NULL == cmd_buf))
    {
        return FAILURE;
    }
    RETURN_VAL_IF_FAIL(s_paralle_sta != PARALLE_STAT_PREPARE, FAILURE);

    dev_inst_t* inst = dev_inst;
    cmd_buf_t* buff = cmd_buf;

    // 文件名校验
    const unsigned char* p = buff->buf;
    if (rt_memcmp(p, s_file_name_bin, FILE_NAME_LEN) != 0)
    {
        buff->rtn = BOTTOM_RTN_DOWN_FILE_NAME_ERR;
        return SUCCESSFUL;
    }

    // 整包校验
    if (!complete_package_crc_check_res()) {
        buff->rtn = BOTTOM_RTN_DOWN_CRC_ERR;
        return SUCCESSFUL;
    }

    // 校验成功，设置返回值
    buff->rtn = BOTTOM_RTN_APP_CORRECT;

    return SUCCESSFUL;
}



/**
 * @brief 打包并发更新确认命令。
 * @param dev_inst 设备实例指针。
 * @param cmd_buf 命令缓冲区指针。
 * @return 返回操作结果。
 */
Static int pack_concur_update_confirm(void* dev_inst, void *cmd_buf)
{
    // 验证输入参数的有效性
    if (!dev_inst || !cmd_buf || !((cmd_buf_t*)cmd_buf)->cmd) {
        return FAILURE;
    }

    dev_inst_t* inst = dev_inst;  // 设备实例
    cmd_buf_t* buff = cmd_buf;    // 命令缓冲区
    bottom_comm_cmd_head_t* head = (bottom_comm_cmd_head_t*)buff->cmd->req_head;

    // 设置数据长度和缓冲区内容
    buff->data_len = UPDATE_FILE_NAME_LEN;
    rt_memcpy_s(buff->buf, buff->buflen, s_file_name_bin, sizeof(s_file_name_bin));

    if(buff->rtn == BOTTOM_RTN_APP_CORRECT){
        // 只有校验成功，才启动定时器，等待回包完成，然后跳转boot执行升级
        s_restart_delay_active = TRUE;
        s_restart_delay_start_tick = rt_tick_get();
    }

    return SUCCESSFUL;
}


/**
 * @brief 程序更新数据传输首发帧信息处理
 * @param[in] ptComm 通信数据
 * @retval SUCCESSFUL 成功, FAILURE 失败
*/
Static short deal_bmu_paralle_first_frm(void* cmd_buf) {
    RETURN_VAL_IF_FAIL(0 == s_proto_head_req->append, SUCCESSFUL);  // 非首发帧，返回
    unsigned short wDataIndex = 0;
    RETURN_VAL_IF_FAIL(cmd_buf != NULL, FAILURE);
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;

    // 保存文件信息
    s_file_manage.tFileAttr.wDataLenPerFrame = get_int16_data(&tran_cmd_buf->buf[0]);      // 下载帧字节数
    s_file_manage.tFileAttr.wTotalFrameNum = get_int16_data(&tran_cmd_buf->buf[2]);    // 发送总帧数
    s_file_manage.tFileAttr.ulTotalFileLength = get_ulong_data(&tran_cmd_buf->buf[4]); // 传输文件长度
    // 传输文件名
    rt_memcpy_s(s_file_manage.tFileAttr.acFileName, sizeof(s_file_manage.tFileAttr.acFileName), &tran_cmd_buf->buf[8], FILE_NAME_LEN);
    // 传输文件时间
    rt_memcpy_s(s_file_manage.tFileAttr.acFileTime, sizeof(s_file_manage.tFileAttr.acFileTime), &tran_cmd_buf->buf[8 + FILE_NAME_LEN], FILE_TIME_LEN);
    // 传输文件总校验
    s_file_manage.tFileAttr.wFileCheck = get_int16_data(&tran_cmd_buf->buf[8 + FILE_NAME_LEN + FILE_TIME_LEN]);

    // 首发帧丢帧记录
    record_bmu_paralle_first_frm_loss(cmd_buf);

    return SUCCESSFUL;
}

/**
 * @brief 程序更新过程数据帧处理
 * @param[in] tran_cmd_buf 通信数据
 * @retval SUCCESSFUL 成功, FAILURE 失败
*/
Static short deal_bmu_paralle_data_frm(void* cmd_buf) {
    RETURN_VAL_IF_FAIL(s_proto_head_req->append > 0 && s_proto_head_req->append < INVALID_FRM_NO, SUCCESSFUL);  // 非数据帧，返回
    RETURN_VAL_IF_FAIL(s_proto_head_req->append < s_file_manage.tFileAttr.wTotalFrameNum, SUCCESSFUL);
    RETURN_VAL_IF_FAIL(cmd_buf != NULL, FAILURE);
    unsigned int ret = 0;
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    unsigned int write_offset = 0;
    // 当前帧写入升级帧缓存
    rt_memset_s(&s_data_to_circle_buff, sizeof(T_AppDataSaveStruct), 0x00, sizeof(T_AppDataSaveStruct));
    s_data_to_circle_buff.ulFrameNo = s_proto_head_req->append;
    rt_memcpy_s(s_data_to_circle_buff.aucData, sizeof(s_data_to_circle_buff.aucData), 
                &tran_cmd_buf->buf[0] , APP_DATA_LEN);
    ret = write_circle_buff(&s_data_to_circle_buff, 1);
    RETURN_VAL_IF_FAIL(TRUE == ret, FAILURE);  // 无法写入缓存，作为丢帧处理

    // 收帧, 判断丢帧, 记录丢帧
    record_bmu_paralle_data_frm_loss(cmd_buf);

    return SUCCESSFUL;
}

/**
 * @brief 并发升级首发帧丢帧记录
 * @param[in] tran_cmd_buf 通信数据
 * @retval SUCCESSFUL 成功, FAILURE 失败
*/
Static short record_bmu_paralle_first_frm_loss(void* cmd_buf) {
    unsigned short i = 0;
    unsigned short wFrmStart = 0;
    unsigned short wFrmEnd = 0;
    RETURN_VAL_IF_FAIL(cmd_buf != NULL, FAILURE);
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    RETURN_VAL_IF_FAIL(0 == s_proto_head_req->append, SUCCESSFUL);  // 非首发帧，返回

    // 补发帧阶段
    if (PARALLE_STAT_DATA_CONFIRM == s_paralle_sta) {
        if (INVALID_FRM_NO == s_last_recived_frame_no) {
            wFrmStart = 1;
        }
        else {
            wFrmStart = s_last_recived_frame_no + 1;
        }
        wFrmEnd = s_file_manage.tFileAttr.wTotalFrameNum;

        // 清除首帧丢帧记录
        reset_frame_loss(0);

        // 记录数据帧丢帧
        for (i = wFrmStart; i < wFrmEnd; i++) {
            set_frame_loss(i);
        }
    }
    else {
        s_last_recived_frame_no = 0;
    }
    return SUCCESSFUL;
}

/**
 * @brief 并发升级数据帧丢帧记录
 * @param[in] tran_cmd_buf 通信数据
 * @retval SUCCESSFUL 成功, FAILURE 失败
*/
Static short record_bmu_paralle_data_frm_loss(void* cmd_buf) {
    unsigned short wCurrentFrmNO = s_proto_head_req->append;
    unsigned short i = 0;
    unsigned short wFrmStart = 0;
    RETURN_VAL_IF_FAIL(cmd_buf != NULL, FAILURE);
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;

    // 补发帧阶段
    if (PARALLE_STAT_DATA_CONFIRM == s_paralle_sta) {
        // 清除当前帧丢帧记录
        reset_frame_loss(wCurrentFrmNO);
    }
    else {
        // 记录首发帧丢帧,第 0 帧
        if (INVALID_FRM_NO == s_last_recived_frame_no) {
            wFrmStart = 0;
        }
        else {
            wFrmStart = s_last_recived_frame_no + 1;
        }

        for (i = wFrmStart; i < wCurrentFrmNO; i++) {
            set_frame_loss(i);
        }

        s_last_recived_frame_no = wCurrentFrmNO;
    }

    return SUCCESSFUL;
}

boolean get_trigger_flag(void)
{
    return (s_paralle_sta == PARALLE_STAT_TRANS) ? TRUE : FALSE;
}

#ifdef UNITEST
flash_error_status FLASH_If_Write(uint32_t FlashAddress, uint32_t* Data, uint32_t DataLength)
{
    return FLASHIF_OK;
}
#endif