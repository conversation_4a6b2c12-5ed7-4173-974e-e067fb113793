#include <rtthread.h>
#include <sys/time.h>
#include <string.h>
#include "temp.h"

static	T_SysPara	s_tSysPara = {0};
static T_AnalogDataStruct	s_tAnalogData = {0};		// 实时模拟量数据
static T_DigitalDataStruct	s_tDigitalData = {0};		// 实时状态量数据
static T_AlarmStruct 	s_tAlarmNoMsk;                  //屏蔽关系前的告警值
acem_analog_data_t ACEMShowData;

T_ACEMFactStruct s_tACEMFactToGui;
T_AdjustDataStruct g_atAdjData[8];
unsigned char	g_ucDownloadMode = 0;
unsigned int gslAllenergy;
unsigned int g_wMaxHisEventLen;			//可保存事件记录条数
unsigned int g_wMaxHisAlmLen;			//可保存历史告警条数
T_FileManageStruct	s_tFileManage;

T_EEpromISPSruct		tPrevISP;

const unsigned int  cg_awCRC_Table[256] = {
    0x0000, 0x1021, 0x2042, 0x3063, 0x4084, 0x50a5, 0x60c6, 0x70e7, 
    0x8108, 0x9129, 0xa14a, 0xb16b, 0xc18c, 0xd1ad, 0xe1ce, 0xf1ef, 
    0x1231, 0x0210, 0x3273, 0x2252, 0x52b5, 0x4294, 0x72f7, 0x62d6, 
    0x9339, 0x8318, 0xb37b, 0xa35a, 0xd3bd, 0xc39c, 0xf3ff, 0xe3de, 
    0x2462, 0x3443, 0x0420, 0x1401, 0x64e6, 0x74c7, 0x44a4, 0x5485, 
    0xa56a, 0xb54b, 0x8528, 0x9509, 0xe5ee, 0xf5cf, 0xc5ac, 0xd58d, 
    0x3653, 0x2672, 0x1611, 0x0630, 0x76d7, 0x66f6, 0x5695, 0x46b4, 
    0xb75b, 0xa77a, 0x9719, 0x8738, 0xf7df, 0xe7fe, 0xd79d, 0xc7bc, 
    0x48c4, 0x58e5, 0x6886, 0x78a7, 0x0840, 0x1861, 0x2802, 0x3823, 
    0xc9cc, 0xd9ed, 0xe98e, 0xf9af, 0x8948, 0x9969, 0xa90a, 0xb92b, 
    0x5af5, 0x4ad4, 0x7ab7, 0x6a96, 0x1a71, 0x0a50, 0x3a33, 0x2a12, 
    0xdbfd, 0xcbdc, 0xfbbf, 0xeb9e, 0x9b79, 0x8b58, 0xbb3b, 0xab1a, 
    0x6ca6, 0x7c87, 0x4ce4, 0x5cc5, 0x2c22, 0x3c03, 0x0c60, 0x1c41, 
    0xedae, 0xfd8f, 0xcdec, 0xddcd, 0xad2a, 0xbd0b, 0x8d68, 0x9d49, 
    0x7e97, 0x6eb6, 0x5ed5, 0x4ef4, 0x3e13, 0x2e32, 0x1e51, 0x0e70, 
    0xff9f, 0xefbe, 0xdfdd, 0xcffc, 0xbf1b, 0xaf3a, 0x9f59, 0x8f78, 
    0x9188, 0x81a9, 0xb1ca, 0xa1eb, 0xd10c, 0xc12d, 0xf14e, 0xe16f, 
    0x1080, 0x00a1, 0x30c2, 0x20e3, 0x5004, 0x4025, 0x7046, 0x6067, 
    0x83b9, 0x9398, 0xa3fb, 0xb3da, 0xc33d, 0xd31c, 0xe37f, 0xf35e, 
    0x02b1, 0x1290, 0x22f3, 0x32d2, 0x4235, 0x5214, 0x6277, 0x7256, 
    0xb5ea, 0xa5cb, 0x95a8, 0x8589, 0xf56e, 0xe54f, 0xd52c, 0xc50d, 
    0x34e2, 0x24c3, 0x14a0, 0x0481, 0x7466, 0x6447, 0x5424, 0x4405, 
    0xa7db, 0xb7fa, 0x8799, 0x97b8, 0xe75f, 0xf77e, 0xc71d, 0xd73c, 
    0x26d3, 0x36f2, 0x0691, 0x16b0, 0x6657, 0x7676, 0x4615, 0x5634, 
    0xd94c, 0xc96d, 0xf90e, 0xe92f, 0x99c8, 0x89e9, 0xb98a, 0xa9ab, 
    0x5844, 0x4865, 0x7806, 0x6827, 0x18c0, 0x08e1, 0x3882, 0x28a3, 
    0xcb7d, 0xdb5c, 0xeb3f, 0xfb1e, 0x8bf9, 0x9bd8, 0xabbb, 0xbb9a, 
    0x4a75, 0x5a54, 0x6a37, 0x7a16, 0x0af1, 0x1ad0, 0x2ab3, 0x3a92, 
    0xfd2e, 0xed0f, 0xdd6c, 0xcd4d, 0xbdaa, 0xad8b, 0x9de8, 0x8dc9, 
    0x7c26, 0x6c07, 0x5c64, 0x4c45, 0x3ca2, 0x2c83, 0x1ce0, 0x0cc1, 
    0xef1f, 0xff3e, 0xcf5d, 0xdf7c, 0xaf9b, 0xbfba, 0x8fd9, 0x9ff8, 
    0x6e17, 0x7e36, 0x4e55, 0x5e74, 0x2e93, 0x3eb2, 0x0ed1, 0x1ef0 
};

/* 暂时放置的其余源文件中的实现函数 */
#define KEY_BUFF_LEN		6

static rt_mutex_t key_mutex = RT_NULL;  // 互斥锁
static unsigned char	s_aucKeyBuf[KEY_BUFF_LEN];	//键盘缓冲区 
static unsigned char	s_ucKeyHead	= 0;			//键盘缓冲区的头指针 
static unsigned char	s_ucKeyTail	= 0;			//键盘缓冲区的尾指针 

void  InitLcd( void )
{
    return ;
}

/*****************************************************************************
//	函数名  	：CRC_Cal(char *p,unsigned int wCount)
//	入口参数	：p：数据指针   wCount：数据长度
//	出口参数	：CRC结果
//	功能	  	：查表计算CRC
*****************************************************************************/
unsigned int    CRC_Cal(unsigned char *p, unsigned int wCount)
{	
	return (0);
}

/*************************************************************************
*函数名  :  WriteNVRAM
*调  用  :	无
*被调用  :	
*输入参数:	wAddress--偏移量
            ucData--数据
*返回值	 :	TRUE--成功
			FALSE--失败
*功能描述:	写入NVRAM某地址数据
*作  者  :	
*设计日期:	
*修改记录:	
* 日    期		版	本		修改人		修改摘要      
*************************************************************************/
unsigned char	WriteNVRAM( unsigned long ulAddress, unsigned char	ucData )
{
	return 0;
}

/****************************************************************************
* 函数名称：MemCopyFromNVRam()
* 调    用：
* 被 调 用：无
* 输入参数：p--待写入的地址; wOffset--偏移量; wLen--待读出的长度
* 返 回 值：无
* 功能描述：从NVRAM中拷贝数据至RAM
* 作    者：严宏明
* 设计日期：2004-06-07
* 修改记录：
* 日    期		版	本		修改人		修改摘要      
***************************************************************************/
void	MemCopyFromNVRAM( unsigned char *p, unsigned long ulOffset, unsigned char ucLen )
{   
    return;
}

/*****************************************************************************
//	函数名  	：GetTime
//	入口参数	：指向获取时间的指针
//	出口参数	：无
//	功能	  	：获取时间
//  调用关系	：
//  全局变量	：
*****************************************************************************/
void    GetTime( time_base_t  * ptTime )
{
    time_t t = time(RT_NULL);
    struct tm t_time;

    if (ptTime == NULL)
    {
        return;
    }

    memset(&t_time, 0x00, sizeof(t_time));
    localtime_r( &t, &t_time );  // IAR 报错，未定义
    ptTime->year = t_time.tm_year+1900;
    ptTime->month = t_time.tm_mon + 1;
    ptTime->day = t_time.tm_mday;
    ptTime->hour = t_time.tm_hour;
    ptTime->minute = t_time.tm_min;
    ptTime->second = t_time.tm_sec;
	return;
}


/*****************************************************************************
//	函数名  	：SetTime
//	入口参数	：需要设置的时间
//	出口参数	：无
//	功能	  	：设置时间
//  调用关系	：
//  全局变量	：
*****************************************************************************/
void    SetTime( T_TimeStruct  * ptTime )
{
    return;
}

/****************************************************************************
* 函数名称：SetDataFlag_HDLC()
* 调    用：无
* 被 调 用：
* 输入参数：ucPart -- 0:DATAFLAG1 1:DATAFLAG2
* 返 回 值：无
* 功能描述：发送历史操作记录信息
* 作    者：潘奇银
* 设计日期：2008-01-09
* 修改记录：
* 日    期		版	本		修改人		修改摘要      
***************************************************************************/
void SetDataFlag_HDLC( unsigned char ucPort, unsigned char ucPart , unsigned char ucValue )
{
    return;
}

/****************************************************************
//	函数名  	：SetSysPara
//	入口参数	：ptSysPara -- 指向待保存参数的指针
				  bChk -- 是否需要校验
				  ucWhoSet--设置参数方,SETTER_LOCAL:本地设置参数,SETTER_REMOTE:远程设置参数
//	出口参数	：TRUE -- 待保存参数范围校验正确
                  FALSE -- 待保存参数范围校验错误
//	功能	  	：如果需要校验，进行校验，校验不成功，返回FALSE；
                  如果校验成功，把参数保存到s_tSysPara，并且保存
                  到EEPROM。
//  调用关系	：调用底层I/O函数WriteEEPROM()
//  全局变量	：
*****************************************************************/
unsigned char SetSysPara( T_SysPara * ptSysPara, unsigned char bChk, unsigned char ucWhoSet ) 
{
	return 0;
}

/****************************************************************************
* 函数名称：CheckDailCode
* 调    用：
* 被 调 用：
* 输入参数：unCode--TYPE_RUNAPP运行app程序，TYPE_RUNBOOT运行boot程序,TYPE_PRO_HDLC--网元层协议
* 返 回 值：返回值大于0表明在效
* 功能描述：判断拨码设置
* 作    者：
* 设计日期：
* 修改记录：
* 日    期		版	本		修改人		修改摘要      
***************************************************************************/
unsigned char CheckDailCode( unsigned char ucDail, unsigned char unCode)
{
	return 1;
}

/****************************************************************************
* 函数名称：GetDisHisAlarm()
* 调    用：无
* 被 调 用：
* 输入参数：wIndex--页码
* 返 回 值：
* 功能描述：获取指定的历史告警
* 作    者：潘奇银
* 设计日期：2008-01-17
* 修改记录：
* 日    期		版	本		修改人		修改摘要      
***************************************************************************/
T_HisAlarmStruct * GetDisHisAlarm(  unsigned int wIndex )
{
	return ( T_HisAlarmStruct * )NULL;

}

/***************************************************************************
* 函数名称：GetAlarmGradeNode
* 被 调 用：
* 输入参数：ucAlarmSn页码
* 返 回 值：根据页码查找告警级别参数节点
* 功能描述：告警级别参数节点
* 作    者：潘奇银
* 设计日期：2008-09-03
* 修改记录：
* 日    期		版	本		修改人		修改摘要      
***************************************************************************/
MIBTable_ParaNode * GetAlarmGradeNode( unsigned char ucAlarmSn )
{
	return ( MIBTable_ParaNode *)NULL;
}

/***************************************************************************
* 函数名称：GetAlmOutRlyNode
* 被 调 用：
* 输入参数：ucAlarmSn页码
* 返 回 值：根据页码查找告警对应输出干结点参数节点
* 功能描述：告警对应输出干结点参数节点
* 作    者：潘奇银
* 设计日期：2008-09-03
* 修改记录：
* 日    期		版	本		修改人		修改摘要      
***************************************************************************/
MIBTable_ParaNode * GetAlmOutRlyNode( unsigned char ucAlarmSn )
{
	return ( MIBTable_ParaNode *)NULL;
}

/*************************************************************************
函数名	：	CheckTimeValid
功能	：	检查设置时间的有效性
输入	： 	无
输出	：	TRUE-有效，FALSE-无效
*************************************************************************/
unsigned char 	CheckTimeValid( T_TimeStruct  * ptTime )
{		
    return  0;
}

/***************************************************************************
* 函数名称：DealRS485Comm
* 调    用：
* 被 调 用：
* 输入参数：ucWhichFlag--DATAFLAG编号,ucVal--设置值
* 返 回 值：
* 功能描述：设置RS485通讯协议的DATAFLAG
* 作    者：潘奇银
* 设计日期：2008-09-08
* 修改记录：
* 日    期		版	本		修改人		修改摘要      
***************************************************************************/
void SetRS485DataFlag( unsigned char ucWhichFlag, unsigned char ucVal )
{
	return;
}

/*****************************************************************************
//	函数名  	：WrClk
//	入口参数	：tTime -- 写入的时间
//	出口参数	：无
//	功能	  	：写入时钟
//  调用关系	：	
//  全局变量	：
*****************************************************************************/
void	WrClk( time_base_t tTime )
{
	return;
}	

/****************************************************************************
* 函数名称：WriteEEPROM
* 调    用：
* 被 调 用：
* 输入参数：wAddress--写入地址  
            ucData  --要写入的字节
* 返 回 值：TRUE--成功 
            FALSE--失败
* 功能描述：写入EEPROM某地址数据
* 作    者：
* 设计日期：
* 修改记录：
* 日    期			版    本		修改人 		修改摘要      
***************************************************************************/
unsigned char	WriteEEPROM( unsigned int wAddress, unsigned char ucData )
{
	return 0;
}

/*****************************************************************************
//	函数名  	：ResetMCU
//	入口参数	：无
//	出口参数	：无
//	功能	  	：系统复位
*****************************************************************************/
void	ResetMCU(void)
{
	return;
}

/*************************************************************************
函数名	：	Delayms
功能	：	延时?ms,
输入	：	无
输出	：	无
*************************************************************************/
void	Delayms( unsigned int   wMillisecond )
{
	return;
}

/****************************************************************************
* 函数名称：MemCpDallasToRAM()
* 调    用：
* 被 调 用：无
* 输入参数：p--待写入的地址; wOffset--偏移量; wLen--待读出的长度
* 返 回 值：无
* 功能描述：从NVRAM中拷贝数据至RAM
* 作    者：严宏明
* 设计日期：2004-06-07
* 修改记录：
* 日    期		版	本		修改人		修改摘要      
***************************************************************************/
void FeedWatchDog( void )
{   
    return;
}

/*************************************************************************
函数名		:	SetDummyTimer
功能		:	增加虚拟定时器
输入		:	ucEvent -- 虚拟定时器
				ucType  -- 类型
				lTime  -- 间隔的时间
				ucMessage -- 需要产生的消息
输出		:	无
全局变量	:	s_atDummyTimer[]
*************************************************************************/
unsigned char	SetDummyTimer( unsigned char ucEvent, unsigned char ucType, unsigned long lTime, unsigned char ucMessage )
{
	return 0;
}