#include "alarm_register.h"
#include "alarm_manage.h"
#include "alarm_id_in.h"
#include "realdata_id_in.h"
#include "alarm_config_in.h"
#include "para_id_in.h"
#include "realdata_save.h"
#include "para_manage.h"
#include "dev_dcmu.h"
#include "math.h"
#include "alarm_mgr_api.h"

/* 通过告警码清除实时告警列表 */
static unsigned short alarm_clean_by_code_tab[] = {
    0xffff
};

static alarm_manage_info_t s_dcmu_manage = {
    ALARM_ID_OFFSET_MAX - 1,        //告警码的个数
    ana_alm_config_tab,             //模拟量告警表
    dig_alm_config_tab,             //状态量告警表
    self_alm_config_tab,            //自定义告警表
    alm_shielded,                    //屏蔽关系表
    alarm_clean_by_code_tab,        // 以告警码清除相关告警列表
};

short register_dcmu_alarm(void)
{
    register_alarm_manage_info(&s_dcmu_manage);
    return 0;
}

char abnormal_battery_current_judge(int alm_id){
    unsigned char dev_addr = 0;
    float battery_current = 0;
    unsigned char battery_pack_config = 0;
    float battery_current_thresh = 0.0f;
    float battery_current_thresh_tmp = 0.0f;

    dev_addr = ALM_ID_GET_DEV(alm_id);

    get_one_para(DCMU_PARA_ID_BATTERY_CONFIG_OFFSET + (dev_addr - 1), &battery_pack_config);
    if(battery_pack_config == 0){
        return FALSE;
    }
    
    get_one_data(DCMU_DATA_ID_BATTERY_CURRENT + (dev_addr - 1), &battery_current);
    get_one_para(DCMU_PARA_ID_BATTERY_CURRENT_THRESHOLD_OFFSET, &battery_current_thresh);

    battery_current_thresh_tmp = (battery_current_thresh * FULL_SCALE_CURRENT);
    if(battery_current > battery_current_thresh_tmp || battery_current < battery_current_thresh_tmp*(-1)){
        return TRUE;
    }else{
        return FALSE;
    }
}


char battery_group_config_judge(int alm_id)
{
    unsigned char dev_addr = 0;
    unsigned char battery_pack_config = 0;
    dev_addr = ALM_ID_GET_DEV(alm_id);
    get_one_para(DCMU_PARA_ID_BATTERY_CONFIG_OFFSET + (dev_addr - 1), &battery_pack_config);

    return battery_pack_config == 0 ? TRUE : FALSE;
}


char battery_voltage_low_judge(int alm_id)
{
    unsigned char dev_addr = ALM_ID_GET_DEV(alm_id);
    unsigned char alm_status = get_realtime_alarm_value(alm_id);
    unsigned char battery_pack_config = 0;
    float battery_voltage = 0.0f;
    float battery_voltage_low_thre = 0.0f;

    // 获取所有需要的数据
    get_one_para(DCMU_PARA_ID_BATTERY_CONFIG_OFFSET + (dev_addr - 1), &battery_pack_config);
    if (battery_pack_config == 0) {
        return FALSE;
    }

    get_one_data(DCMU_DATA_ID_BATTERY_VOLTAGE + (dev_addr - 1), &battery_voltage);
    get_one_para(DCMU_PARA_ID_BATTERY_VOLTAGE_LOW_THRESHOLD_OFFSET, &battery_voltage_low_thre);

    if (alm_status == ALARM_NOT_EXSIT) { // 告警产生判断
        if (battery_voltage < battery_voltage_low_thre)  {
            return TRUE;
        } else {
            return FALSE;
        }
    } else { // 告警恢复判断
        if (battery_voltage >= (battery_voltage_low_thre + 0.5) ) {
            return FALSE;
        } else {
            return TRUE;
        }
    }
}

char battery_voltage_too_low_judge(int alm_id)
{
    unsigned char dev_addr = ALM_ID_GET_DEV(alm_id);
    unsigned char alm_status = get_realtime_alarm_value(alm_id);
    unsigned char battery_pack_config = 0;
    float battery_voltage = 0.0f;
    float battery_voltage_too_low_thre = 0.0f;

    // 获取所有需要的数据
    get_one_para(DCMU_PARA_ID_BATTERY_CONFIG_OFFSET + (dev_addr - 1), &battery_pack_config);
    if (battery_pack_config == 0) {
        return FALSE;
    }

    get_one_data(DCMU_DATA_ID_BATTERY_VOLTAGE + (dev_addr - 1), &battery_voltage);
    get_one_para(DCMU_PARA_ID_BATTERY_VOLTAGE_TOO_LOW_THRESHOLD_OFFSET, &battery_voltage_too_low_thre);

    if (alm_status == ALARM_NOT_EXSIT) { // 告警产生判断
        if (battery_voltage < battery_voltage_too_low_thre)  {
            return TRUE;
        } else {
            return FALSE;
        }
    } else { // 告警恢复判断
        if (battery_voltage >= (battery_voltage_too_low_thre + 0.5) ) {
            return FALSE;
        } else {
            return TRUE;
        }
    }
}

char battery_loop_broken_judge(int alm_id)
{
    unsigned char dev_addr = ALM_ID_GET_DEV(alm_id);
    unsigned char alm_status = get_realtime_alarm_value(alm_id);
    int battery_pack_config = 0;
    float dc_voltage = 0;
    float battery_voltage = 0;
    float battery_current = 0;
    float battery_loop_broken_thre = 0.0f;
    float hysteresis = 0.0f;

    // 获取所有需要的数据
    get_one_para(DCMU_PARA_ID_BATTERY_CONFIG_OFFSET + (dev_addr - 1), &battery_pack_config);
    if (battery_pack_config == 0) {
        return FALSE;
    }

    get_one_data(DCMU_DATA_ID_DC_OUTPUT_VOLTAGE, &dc_voltage);
    get_one_data(DCMU_DATA_ID_BATTERY_CURRENT + (dev_addr - 1), &battery_current);
    get_one_data(DCMU_DATA_ID_BATTERY_VOLTAGE + (dev_addr - 1), &battery_voltage);
    get_one_para(DCMU_PARA_ID_BATTERY_LOOP_BROKEN_THRESHOLD_OFFSET, &battery_loop_broken_thre);

    if(battery_loop_broken_thre < 0.3f)
    {
        hysteresis = 0;
    }
    else{
        hysteresis = 0.2;
    }
    if (alm_status == ALARM_NOT_EXSIT) { // 告警产生判断
        if ((dc_voltage - battery_voltage > battery_loop_broken_thre) && (fabs(battery_current) < 10.0f) && (battery_voltage > 30.0f)) {
            return TRUE;
        } else {
            return FALSE;
        }
    } else { // 告警恢复判断
        if ((dc_voltage - battery_voltage < (battery_loop_broken_thre - hysteresis)) || (battery_current > 20.0f)) {
            return FALSE;
        } else {
            return TRUE;
        }
    }
}

/* Started by AICoder, pid:kedb6h40daldefa145930beef0d55339483187a3 */
char battery_discharge_judge(int alm_id){
    unsigned char dev_addr = 0;
    float battery_discharge_threshold = 0;
    float battery_current = 0;
    unsigned char battery_pack_config = 0;
    unsigned char alm_status = get_realtime_alarm_value(alm_id);
    dev_addr = ALM_ID_GET_DEV(alm_id);

    // 获取所有需要的数据
    get_one_para(DCMU_PARA_ID_BATTERY_CONFIG_OFFSET + (dev_addr - 1), &battery_pack_config);
    if (battery_pack_config == 0) {
        return FALSE;
    }

    get_one_data(DCMU_DATA_ID_BATTERY_CURRENT + (dev_addr - 1), &battery_current);
    get_one_para(DCMU_PARA_ID_BATTERY_DISCHARGE_THRESHOLD_OFFSET, &battery_discharge_threshold);

    if (alm_status == ALARM_NOT_EXSIT) { // 告警产生判断
        if (battery_current < battery_discharge_threshold)  {
            return TRUE;
        } else {
            return FALSE;
        }
    } else { // 告警恢复判断
        if (battery_current > (battery_discharge_threshold + 5.0f) || if_float_equal(battery_current,(battery_discharge_threshold + 5.0f))) {
            return FALSE;
        } else {
            return TRUE;
        }
    }
}
/* Ended by AICoder, pid:kedb6h40daldefa145930beef0d55339483187a3 */

char abnormal_load_config_judge(int alm_id){
    unsigned char dev_addr = 0;
    unsigned char battery_pack_config = 0;
    char load_loop_config = 0;
    char load_config = 0;

    dev_addr = ALM_ID_GET_DEV(alm_id);
    get_one_para(DCMU_PARA_ID_LOAD_CONFIG_OFFSET + (dev_addr - 1), &load_config);
    get_one_para(DCMU_PARA_ID_LOAD_LOOP_CONFIG_OFFSET, &load_loop_config);
    get_one_data(DCMU_DATA_ID_LOAD_LOOP_CONFIG + (dev_addr - 1), &battery_pack_config);
    if(load_loop_config){
        if(!load_config)
        {return FALSE;}
    }
    if(!battery_pack_config){
        return TRUE;
    }
    return FALSE;
}



char invalid_battery_temperature_judge(int alm_id) {
    unsigned char dev_addr = 0;
    int battery_pack_config = 0;
    float battery_temperature = 0;

    dev_addr = ALM_ID_GET_DEV(alm_id);
    get_one_para(DCMU_PARA_ID_BATTERY_CONFIG_OFFSET + (dev_addr - 1), &battery_pack_config);
    if (battery_pack_config == 0) {
        return FALSE;
    }

    get_one_data(DCMU_DATA_ID_BATTERY_TEMPERATURE + (dev_addr - 1), &battery_temperature);

    if (fabsf(battery_temperature - TEMPERATURE_INVALID) < 0.01f) {
        return TRUE;
    } else {
        return FALSE;
    }
}



char env_humidity_invalid_judge(int alm_id) {
    char humidity = 0;
    unsigned char usage_scenario = 0;
    get_one_data(DCMU_DATA_ID_ENV_HUMIDITY, &humidity);

    get_one_para(DCMU_PARA_ID_USAGE_SCENARIO_OFFSET, &usage_scenario);
    if(usage_scenario == 0)
    {
        return FALSE;
    }
    if (humidity < HUMIDITY_MIN || humidity > HUMIDITY_MAX) {
        return TRUE;
    } else {
        return FALSE;
    }
}



char env_temp_invalid_judge(int alm_id)
{
    char temperature = 0;
    unsigned char usage_scenario = 0;
    get_one_data(DCMU_DATA_ID_ENV_TEMP, &temperature);

    get_one_para(DCMU_PARA_ID_USAGE_SCENARIO_OFFSET, &usage_scenario);
    if(usage_scenario == 0)
    {
        return FALSE;
    }
    if(temperature < TEMPERATURE_MIN || temperature > TEMPERATURE_MAX){
        return TRUE;
    }
    else{
        return FALSE;
    }
}


char temperature_sensor_alarm_judge(void)
{
    char ret = 0;
    char alarm_status = 0;
    int alm_id = 0;

    alm_id = GET_ALM_ID(DCMU_ALM_ID_ENV_TEMP_HIGH_ALARM,1,1);
    alarm_status = get_realtime_alarm_value(alm_id);
    if(TRUE == alarm_status){
        ret = 2;    // 环境温度高告警
    }

    alm_id = GET_ALM_ID(DCMU_ALM_ID_ENV_TEMP_LOW_ALARM,1,1);
    alarm_status = get_realtime_alarm_value(alm_id);
    if(TRUE == alarm_status){
        ret = 1;    // 环境温度低告警
    }

    alm_id = GET_ALM_ID(DCMU_ALM_ID_ENV_TEMP_SENSOR_INVALID_ALARM,1,1);
    alarm_status = get_realtime_alarm_value(alm_id);
    if(TRUE == alarm_status){
        ret = 4;    // 环境温度无效告警
    }

    return ret;
}




char humidity_sensor_alarm_judge(void)
{
    char ret = 0;
    char alarm_status = 0;
    int alm_id = 0;

    alm_id = GET_ALM_ID(DCMU_ALM_ID_ENV_HUMIDITY_HIGH_ALARM,1,1);
    alarm_status = get_realtime_alarm_value(alm_id);
    if(TRUE == alarm_status){
        ret = 2;    // 环境湿度高告警
    }

    alm_id = GET_ALM_ID(DCMU_ALM_ID_ENV_HUMIDITY_LOW_ALARM,1,1);
    alarm_status = get_realtime_alarm_value(alm_id);
    if(TRUE == alarm_status){
        ret = 1;    // 环境湿度低告警
    }

    alm_id = GET_ALM_ID(DCMU_ALM_ID_ENV_HUMIDITY_SENSOR_INVALID_ALARM,1,1);
    alarm_status = get_realtime_alarm_value(alm_id);
    if(TRUE == alarm_status){
        ret = 4;    // 环境湿度无效告警
    }

    return ret;
}


char dcmu_input_relay_status_judge(int alm_id) {
    char relay_status = 0;
    char relay_para_status = 0;
    int i;
    int alm_code[] = {DCMU_ALM_ID_INPUT_RELAY1_ALARM,
                      DCMU_ALM_ID_INPUT_RELAY2_ALARM,
                      DCMU_ALM_ID_INPUT_RELAY3_ALARM,
                      DCMU_ALM_ID_INPUT_RELAY4_ALARM};
    unsigned char usage_scenario = 0;
    get_one_para(DCMU_PARA_ID_USAGE_SCENARIO_OFFSET, &usage_scenario);
    if(usage_scenario == 0)
    {
        return FALSE;
    }
    // 查找alm_id对应的输入继电器序号
    for(i = 0; i < INPUT_RELAY_NUM; i++) {
        if (alm_id == GET_ALM_ID(alm_code[i], 1, 1)) {
            get_one_data(DCMU_DATA_ID_INPUT_RELAY + i, &relay_status);
            get_one_para(DCMU_PARA_ID_INRELAYTTL_OFFSET + i, &relay_para_status);
            return (relay_status == relay_para_status) ? TRUE : FALSE;
        }
    }

    return FALSE;

}
/* Started by AICoder, pid:c3734m46cem8be014aa20b7bf1309a0550b8396f */
char battery_temp_high_judge(int alm_id)
{
    unsigned char dev_addr = ALM_ID_GET_DEV(alm_id);
    unsigned char alm_status = get_realtime_alarm_value(alm_id);
    unsigned int temp_low_alm_status = 0;
    unsigned int temp_too_high_alm_status = 0;
    unsigned char battery_pack_config = 0;
    float battery_temp = 0.0f;
    float battery_temp_high_thre = 0.0f;

    temp_low_alm_status = GET_ALM_ID(DCMU_ALM_ID_BATTERY_TEMPERATURE_LOW, dev_addr, 1);
    temp_too_high_alm_status = GET_ALM_ID(DCMU_ALM_ID_BATTERY_TEMPERATURE_TOO_HIGH, dev_addr, 1);
    
    // 获取所有需要的数据
    get_one_para(DCMU_PARA_ID_BATTERY_CONFIG_OFFSET + (dev_addr - 1), &battery_pack_config);
    if (battery_pack_config == 0 || get_realtime_alarm_value(temp_low_alm_status) == ALARM_EXSIT || get_realtime_alarm_value(temp_too_high_alm_status) == ALARM_EXSIT) {
        return FALSE;
    }

    get_one_data(DCMU_DATA_ID_BATTERY_TEMPERATURE + (dev_addr - 1), &battery_temp);
    get_one_para(DCMU_PARA_ID_BATTERY_TEMPERATURE_HIGH_THRESHOLD_OFFSET, &battery_temp_high_thre);

    if (alm_status == ALARM_NOT_EXSIT) { // 告警产生判断
        if (battery_temp > battery_temp_high_thre)  {
            return TRUE;
        } else {
            return FALSE;
        }
    } else { // 告警恢复判断
        if (battery_temp < (battery_temp_high_thre - 3.0f) || if_float_equal(battery_temp,(battery_temp_high_thre - 3.0f))) {
            return FALSE;
        } else {
            return TRUE;
        }
    }
}

char battery_temp_low_judge(int alm_id)
{
    unsigned char dev_addr = ALM_ID_GET_DEV(alm_id);
    unsigned char alm_status = get_realtime_alarm_value(alm_id);
    unsigned char battery_pack_config = 0;
    float battery_temp = 0.0f;
    float battery_temp_low_thre = 0.0f;
    unsigned int temp_high_alm_status = 0;

    temp_high_alm_status = GET_ALM_ID(DCMU_ALM_ID_BATTERY_TEMPERATURE_HIGH, dev_addr, 1);

    // 获取所有需要的数据
    get_one_para(DCMU_PARA_ID_BATTERY_CONFIG_OFFSET + (dev_addr - 1), &battery_pack_config);
    if (battery_pack_config == 0 || get_realtime_alarm_value(temp_high_alm_status) == ALARM_EXSIT) {
        return FALSE;
    }

    get_one_data(DCMU_DATA_ID_BATTERY_TEMPERATURE + (dev_addr - 1), &battery_temp);
    get_one_para(DCMU_PARA_ID_BATTERY_TEMPERATURE_LOW_THRESHOLD_OFFSET, &battery_temp_low_thre);

    if (alm_status == ALARM_NOT_EXSIT) { // 告警产生判断
        if (battery_temp < battery_temp_low_thre)  {
            return TRUE;
        } else {
            return FALSE;
        }
    } else { // 告警恢复判断
        if (battery_temp > (battery_temp_low_thre + 3.0f) || if_float_equal(battery_temp,(battery_temp_low_thre + 3.0f))) {
            return FALSE;
        } else {
            return TRUE;
        }
    }
}

char battery_temp_too_high_judge(int alm_id)
{
    unsigned char dev_addr = ALM_ID_GET_DEV(alm_id);
    unsigned char alm_status = get_realtime_alarm_value(alm_id);
    unsigned char battery_pack_config = 0;
    float battery_temp = 0.0f;
    float battery_temp_too_high_thre = 0.0f;
    
    // 获取所有需要的数据
    get_one_para(DCMU_PARA_ID_BATTERY_CONFIG_OFFSET + (dev_addr - 1), &battery_pack_config);
    if (battery_pack_config == 0) {
        return FALSE;
    }

    get_one_data(DCMU_DATA_ID_BATTERY_TEMPERATURE + (dev_addr - 1), &battery_temp);
    get_one_para(DCMU_PARA_ID_BATTERY_TEMPERATURE_TOO_HIGH_THRESHOLD_OFFSET, &battery_temp_too_high_thre);

    if (alm_status == ALARM_NOT_EXSIT) { // 告警产生判断
        if (battery_temp > battery_temp_too_high_thre)  {
            return TRUE;
        } else {
            return FALSE;
        }
    } else { // 告警恢复判断
        if (battery_temp < (battery_temp_too_high_thre - 3.0f) || if_float_equal(battery_temp,(battery_temp_too_high_thre - 3.0f))) {
            return FALSE;
        } else {
            return TRUE;
        }
    }
}
/* Ended by AICoder, pid:c3734m46cem8be014aa20b7bf1309a0550b8396f */

char total_alarm_judge(int alm_id)
{
    int real_alarm_count = 0;
    unsigned char total_alarm_status = 0;
    real_alarm_count = get_realtime_alarm_count();
    if(get_realtime_alarm_value(alm_id) == TRUE && real_alarm_count == 1)    //有且仅有总告警存在时，总告警恢复
    {
        return FALSE;
    }

    if (real_alarm_count > 0) {
        return TRUE;
    } else {
        return FALSE;
    }
}

/**
 * @brief 告警判断函数
 * @param current_value 实时数据值
 * @param threshold 阈值
 * @param hysteresis 回差值
 * @param mode 告警判断模式
 * @param current_state 当前告警状态
 * @return 新的告警状态
 */
char check_alarm(float current_value, float threshold, 
                      float hysteresis, char mode, 
                      char current_state) {
    unsigned char usage_scenario = 0;

    get_one_para(DCMU_PARA_ID_USAGE_SCENARIO_OFFSET, &usage_scenario);
    if(usage_scenario == 0)
    {
        return FALSE;
    }
    
    switch (mode) {
        case ALARM_HIGH_LIMIT:  // 高限判断模式
            if (current_state == ALARM_NOT_EXSIT) {
                // 正常状态下，实时数据高于阈值时产生告警
                if (current_value > threshold) {
                    return TRUE;
                }
            } else {
                // 告警状态下，实时数据小于阈值+回差值时恢复告警
                if (current_value <= threshold + hysteresis) {
                    return FALSE;
                }
            }
            break;
            
        case ALARM_LOW_LIMIT:   // 低限判断模式
            if (current_state == ALARM_NOT_EXSIT) {
                // 正常状态下，实时数据小于阈值时产生告警
                if (current_value < threshold) {
                    return TRUE;
                }
            } else {
                // 告警状态下，实时数据高于阈值+回差值时恢复告警
                if (current_value >= threshold + hysteresis) {
                    return FALSE;
                }
            }
            break;
    }
    
    // 状态未发生变化，保持原状态
    return current_state;
}

char env_humidity_high_alarm_judge(int alm_id)
{
    char env_humidity = 0;  //环境湿度
    float humidity_sensor_upper = 0;  //环境湿度高阈值
    float hysteresis = -5;
    char current_state = 0;
    char mode = ALARM_HIGH_LIMIT;

    get_one_data(DCMU_DATA_ID_ENV_HUMIDITY, &env_humidity);
    get_one_para(DCMU_PARA_ID_ENV_HUMIDITY_SENSOR_UPPER_LIMIT_OFFSET, &humidity_sensor_upper);
    current_state = get_realtime_alarm_value(alm_id);
    return check_alarm(env_humidity, humidity_sensor_upper, hysteresis, mode, current_state);
}

char env_humidity_low_alarm_judge(int alm_id)
{
    char env_humidity = 0;  //环境湿度
    float humidity_sensor_upper = 0; 
    float hysteresis = 5;
    char current_state = 0;
    unsigned char usage_scenario = 0;

    get_one_para(DCMU_PARA_ID_USAGE_SCENARIO_OFFSET, &usage_scenario);
    if(usage_scenario == 0)
    {
        return FALSE;
    }
    get_one_data(DCMU_DATA_ID_ENV_HUMIDITY, &env_humidity);
    get_one_para(DCMU_PARA_ID_ENV_HUMIDITY_SENSOR_LOWER_LIMIT_OFFSET, &humidity_sensor_upper);
    current_state = get_realtime_alarm_value(alm_id);
    if (current_state == ALARM_NOT_EXSIT) {
        if (env_humidity < humidity_sensor_upper && env_humidity >= 10) {
            return TRUE;
        }
    } else {
        // 告警状态下，实时数据高于阈值+回差值时恢复告警
        if (env_humidity >= humidity_sensor_upper + hysteresis || env_humidity < 10) {
            return FALSE;
        }
    }
    return current_state;
}

char env_temp_alarm_judge(int alm_id)
{
    char env_temp = 0;  //环境温度
    float threshold = 0;  
    float hysteresis = -3;
    char current_state = 0;
    char mode = ALARM_HIGH_LIMIT;

    get_one_data(DCMU_DATA_ID_ENV_TEMP, &env_temp);
    switch(ALM_ID_GET_ALM_CODE(alm_id))
    {
        case DCMU_ALM_ID_ENV_TEMP_HIGH_ALARM:  
            get_one_para(DCMU_PARA_ID_ENV_TEMPERATURE_SENSOR_UPPER_LIMIT_OFFSET, &threshold);
            mode = ALARM_HIGH_LIMIT;
            hysteresis = -3;
            break;
        case DCMU_ALM_ID_ENV_TEMP_LOW_ALARM:  
            get_one_para(DCMU_PARA_ID_ENV_TEMPERATURE_SENSOR_LOWER_LIMIT_OFFSET, &threshold);
            mode = ALARM_LOW_LIMIT;
            hysteresis = 3;
            break;
    }
    current_state = get_realtime_alarm_value(alm_id);
    return check_alarm(env_temp, threshold, hysteresis, mode, current_state);
}

char sensor_alarm_judge(int alm_id)
{
    char sensor = 0; 
    unsigned char usage_scenario = 0;

    get_one_para(DCMU_PARA_ID_USAGE_SCENARIO_OFFSET, &usage_scenario);
    if(usage_scenario == 0)
    {
        return FALSE;
    }

    switch(ALM_ID_GET_ALM_CODE(alm_id))
    {
        case DCMU_ALM_ID_FUMES_SENSOR_ALARM:  // 烟雾传感器告警数据
            get_one_data(DCMU_DATA_ID_DC_FUMES_SENSOR, &sensor);
            break;
        case DCMU_ALM_ID_FLOOD_SENSOR_ALARM:  // 水淹传感器告警数据
            get_one_data(DCMU_DATA_ID_DC_FLOOD_SENSOR, &sensor);
            break;
        case DCMU_ALM_ID_DOORMAT_SENSOR_ALARM:  // 门磁传感器告警数据
            get_one_data(DCMU_DATA_ID_DC_DOORMAG_SENSOR, &sensor);
            break;
    }

    return sensor;
}