/**************************************************************************
* Copyright (C) 2001, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：E260 DXCB板软件
* 文件名称：do_ctrl.h
* 文件说明：输出控制模块头文件
* 作    者：hlb
* 版本信息：V1.0
* 设计日期：2023-09-18
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/
#ifndef _DO_CTRL_H
#define _DO_CTRL_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

#include <rtdbg.h>
#include <board.h>
#include <rtdevice.h>

/***********************  常量定义  ************************/
/* DO动作定义 */
#define DO_ACTIVE      1    ///< DO动作
#define DO_NOT_ACTIVE  0    ///< DO恢复
#define EV_FORWARD     0    ///< EV正转
#define EV_REVERSE     1    ///< DO反转
#define EV_CLOSE       0

#define EV_STOP_DELAY_TIMER    30 // 和EV1_4CtrolMain线程执行时间相关
#define EV_CONTROL_STOP        1
#define EV_CONTROL_RUN         0

/*********************  数据结构定义  **********************/
typedef enum{
    INIT_MAX_OPEN_STATUS = 0,
    INIT_MAX_CLOSE_STATUS,
    INIT_COMPLETE_STATUS
}init_eev_status_e;

typedef enum {
    DO_1 = 0,
    DO_2,
    DO_3,
    DO_4,
    DO_5,
    DO_6,
    DO_7,
    DO_8,
    DO_9,
    DO_10,
    DO_11,
    DO_12,
    DO_13,
    DO_14,
    DO_15,
    DO_MAX,
} eDO_t;

typedef enum {
    EV1 = 0,
    EV2,
    EV3,
    EV4,
    EV5,
    EV6,
    EV7,
    EV8_MAX,
    EV_END,
} eEV1_8_t;

typedef struct 
{
    unsigned char   curCtrValue;
    unsigned char   cnt;         //停止励磁延迟计数
    unsigned char   rotateDirection;
    unsigned char   rotateDirectionBak;

    int    targetStep;  //目标步数按照半步存储
    int    curStep;
    int    curCtrValueIndex;
    int    runStopFlag;
}eev_ctrl_info_t;


void* init_eev_ctrl(void* param);
void eev_ctrl_main(void *parameter);
void init_when_eev_close();
int init_eev_output();
void update_ctrl_eev_info();
int set_do_value(eDO_t do_index, unsigned char value);
int get_do_value(eDO_t do_index, unsigned char* do_value);
int set_all_do_value(unsigned short value);
int set_eev_target_step(unsigned short step, eEV1_8_t index);
int get_eev_target_step_para();
int set_eev_value(unsigned char dev_index ,unsigned short value);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  