#ifndef _APP_BCMU_FILESYS_H_ 
#define _APP_BCMU_FILESYS_H_ 


#ifdef __cplusplus
extern "C" {
#endif

#include <rtthread.h>

enum
{
    BMS_ADDR = 0,
    RESET_REASON,
    BATT_THEFT,
};

void initFileSys(void);
unsigned char saveResetData(unsigned char ucType, unsigned char ucData);
unsigned char getResetData(unsigned char ucType);
unsigned char  SaveUpgradefileInfo( unsigned char ucMode );
int readFile(char *pcFilename, unsigned char *pucData, int ulReadsize);
int writeFile(char *pcFilename, unsigned char *pucData, int ulWritesize);
int writeFsCnt(void);

#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // _APP_BCMU_FILESYS_H_


