#ifndef __NETPROTOCOL_1363_H_
#define __NETPROTOCOL_1363_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "common.h"

#define NET_PROTCOL_1363_PORT    4000    //网口1363固定端口号
#define TIMEOUT_WAITTING_MAX_TIME 600   //进入近端网口模式后的超时等待时间
#define TIME_NET1363NORTH_MAX    60      // 60S超时无通信判为通信断

typedef struct
{
    BYTE bNetRecBuff[LEN_COMM_REC_BUF];
    WORD wRecIndex;
} T_NetComStruct;


void Process1363_NET_Comm(void* parameter);
void close1363socket(BOOLEAN bCloseServer,BOOLEAN bCloseClient);
BYTE GetNetPackBufSize(BYTE buf[],int size);
BOOLEAN Net1363NorthTimeCount(void);
BOOLEAN JudgeNet1363CommDisconnect(void);


#ifdef __cplusplus
}  
#endif

#endif  
