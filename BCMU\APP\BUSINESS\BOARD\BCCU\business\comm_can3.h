﻿#ifndef _COMM_CAN3_H_
#define _COMM_CAN3_H_

#ifdef __cplusplus
extern "C" {
#endif

#define NORTH_CAN3_DELAY    10  // unit:ms
#define CAN3_R_BUFF_LEN                    64          ///<  接收缓冲区长度
#define CAN3_S_BUFF_LEN                    64          ///<  发送缓冲区长度
#define EMERGENCY_PERIODIC                 10          // 100ms
#define COMMON_PERIODIC                    30000       // 5min
#define ADDR_COMPETE_END_TIME              500         ///<  地址竞争结束计时5s
#define CAN3_HEAD_FILTER_NUM               2           ///<  can3过滤表数量

typedef struct {
    unsigned char dido_compete_flag;                  ///<   地址竞争标志
    unsigned char dido_comfirm_address;               ///<   DIDO已分配地址
    unsigned char dido_address;                       ///<   本机DIDO地址
    unsigned char di_mode;                            ///<   DI口电平
    unsigned char do_mode;                            ///<   DO口电平
    unsigned char do_active_finish;                   ///<   DO动作结束
}bccu_dido_t;

void* CAN3_comm_init(void* param);
void process_CAN3_comm(void* parameter);

#ifdef __cplusplus
}
#endif      //< __cplusplus

#endif      //< _COMM_CAN3_H_
