#include "dev_can_comm.h"


/* 命令请求 */
static remote_comm_cmd_head_t cmd_req[] RAM_SECTION = {
    {VER_01, CMD_GET_BATT_INFO},   // 1
    {0}
};

/* 命令应答   */
static remote_comm_cmd_head_t cmd_ack[] RAM_SECTION = {
    {VER_01, CMD_GET_BATT_INFO},   // 1
    {0}
};

/* 获取电池信息 */
static data_info_id_verison_t batt_info[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, BATT_INFO_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_MAIN_CTRL_RELAY_STATUS},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_AUTO_CHARGE_PORT_ALIGNMENT_STATUS},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_ALLOW_BMS_OFF_STATUS}
};

static data_info_id_verison_t batt_info_ack[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, BATT_INFO_ACK_LEN, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BATT_SOC},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BATT_VOL},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BATT_CURR},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_REQ_MAIN_CTRL_CLOSE_RELAY},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_REQ_BMS_OFF_STATUS},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BATT_SOH},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BOARD_TEMP},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ROBOT_DATA_BATT_CHARGE_RES_TIME}
};

/* 解包命令 */
static cmd_parse_info_id_verison_t cmd_parse_info[] = {
    {&batt_info[0], sizeof(batt_info)/sizeof(data_info_id_verison_t)},  // 1
};

/* 打包命令 */
static cmd_parse_info_id_verison_t cmd_pack_info[] = {
    {&batt_info_ack[0], sizeof(batt_info_ack)/sizeof(data_info_id_verison_t)},  // 1
};

/* 通信协议命令表 */
static cmd_t no_poll_cmd_tab[] = {
    {GET_BATT_INFO, CMD_PASSIVE,  &cmd_req[0], &cmd_ack[0], sizeof(remote_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[0], &cmd_parse_info[0]},
    {0},
};

int register_main_ctrl_tab()
{
    return register_cmd_tab(no_poll_cmd_tab, sizeof(no_poll_cmd_tab)/ sizeof(cmd_t));
}

