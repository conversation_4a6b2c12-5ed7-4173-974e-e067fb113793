#include <rtthread.h>
#include <rtdef.h>
#include <stdio.h>
#include <rtdevice.h>
#include <fal.h>
#include <dfs_fs.h>
#include "utils_time.h"
#include "utils_data_type_conversion.h"
#include "utils_data_transmission.h"
#include "data_type.h"
#include "energy_accu.h"
#include "device_type.h"
#include "dev_dc_ac.h"
#include "time.h"
#include "msg.h"
#include "msg_id.h"
#include "storage.h"
#include "realdata_save.h"
#include "realdata_id_in.h"
#include "partition_def.h"
#include "softbus.h"
#include "utils_rtthread_security_func.h"
#include "utils_math.h"

static rt_timer_t g_accum_energy_timer = NULL;
static energy_info_cal_t g_his_energy = { 0 };
static int g_count = 0;
static time_base_t g_last_time = { 0 };
static rt_tick_t g_last_clock_time = { 0 };
static int g_his_energy_pos = 0;// 实时数据的位置
static energy_record_t tmp_records[RCD_NUM] = {0};

static void his_energy(energy_info_cal_t* his_energy, time_base_t* curr_time);
static float get_act_power(dev_inst_t* dev_dc_ac);
static void time_cmp_period(time_base_t* curr_time, time_base_t* last_time);
static int get_next_his_energy_write_pos(int his_energy_pos);
static void accu_his_energy(energy_info_cal_t* his_energy, dev_inst_t* dev_dc_ac);
static void send_energy_accum_msg_to_sample(void *parameter);
static void save_minute_energy();
static void save_one_record(int type, energy_record_t* one_record);
static void save_all_record(time_base_t* last_time);
static void set_energy_data(void);
static void create_one_record(energy_record_t* one_record, time_base_t* last_time, float energy);
static int get_time_cmp_status(time_base_t* start_time, time_base_t* end_time);
static int time_base_cmp(time_base_t* start, time_base_t* end);

static his_energy_record_t g_his_energy_record[HIS_ENERGY_TYPE_NUM] = 
{
    {
        .file_name = YEAR_FILE,
        .file_name_tmp = YEAR_FILE_TMP,
        .record_size = YEAR_RECORD_SIZE,
        .time_cmp_status = YEAR_SAME,
    },
    {
        .file_name = MONTH_FILE,
        .file_name_tmp = MONTH_FILE_TMP,
        .record_size = MONTH_RECORD_SIZE,
        .time_cmp_status = MONTH_SAME,
    },
    {
        .file_name = DAY_FILE,
        .file_name_tmp = DAY_FILE_TMP,
        .record_size = DAY_RECORD_SIZE,
        .time_cmp_status = DAY_SAME,
    },
    {
        .file_name = HOUR_FILE,
        .file_name_tmp = HOUR_FILE_TMP,
        .record_size = HOUR_RECORD_SIZE,
        .time_cmp_status = HOUR_SAME,
    }
};

void energy_accum_init(void)
{   
    time_base_t curr_time;
    get_time(&curr_time);
    g_last_clock_time = rt_tick_get_millisecond();

    // 处理停电时候的数据
    data_init(&curr_time);
    set_energy_data();
    g_count = 0;
    rt_memcpy(&g_last_time, &curr_time, sizeof(time_base_t));

    char* timer_name = "accum_energy";
    g_accum_energy_timer = rt_timer_create(timer_name, send_energy_accum_msg_to_sample, NULL, ACCUM_ENERGY_PERIOD, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
    if (g_accum_energy_timer == NULL)
    {
        LOG_E("%s:%d| accum_energy_timer fail", __FUNCTION__ , __LINE__);
        return;
    }
    // 1s之后才会启动
    rt_timer_start(g_accum_energy_timer);
}

static void send_energy_accum_msg_to_sample(void *parameter)
{   
    rt_msg_t msg = NULL;
    msg = rt_msg_create(ENERGY_ACCUM_MSG, NULL, 0);
    RETURN_IF_FAIL(msg != NULL);
    rt_msg_send_event(msg);
}

energy_info_cal_t* get_his_energy(void)
{
    return &g_his_energy;
}

// 处理历史的当前数据
static void his_energy(energy_info_cal_t* his_energy, time_base_t* curr_time)
{
    time_base_t* his_curr_time = &his_energy->curr_time;
    if (curr_time->year != his_curr_time->year)
    {   
        save_all_record(his_curr_time);
        his_energy->curr_hour_energy = 0;
        his_energy->curr_day_energy = 0;
        his_energy->curr_month_energy = 0;
        his_energy->curr_year_energy = 0;
        return;
    }

    if (curr_time->month != his_curr_time->month)
    {
        save_all_record(his_curr_time);
        his_energy->curr_hour_energy = 0;
        his_energy->curr_day_energy = 0;
        his_energy->curr_month_energy = 0;
        return;
    }

    // 比较日
    if (curr_time->day != his_curr_time->day)
    {   
        save_all_record(his_curr_time);
        his_energy->curr_hour_energy = 0;
        his_energy->curr_day_energy = 0;
        return;
    }

    if (curr_time->hour != his_curr_time->hour)
    {   
        save_all_record(his_curr_time);
        his_energy->curr_hour_energy = 0;
        return;
    }
    return;
}

void data_init(time_base_t* curr_time)
{
    part_data_t part_data;
    int his_energy_pos = 0;

    part_data.buff = (unsigned char*)&g_his_energy;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), ENERGY_PART);
    part_data.len = sizeof(energy_info_cal_t);

    for (his_energy_pos = 0; his_energy_pos < SAVE_TURN; his_energy_pos++)
    {
        part_data.offset = CURR_START_ADDR + his_energy_pos * sizeof(energy_info_cal_t);
        if (SUCCESSFUL != storage_process(&part_data, read_opr))
        {
            LOG_E("%s:%d| read energy fail", __FUNCTION__ , __LINE__);
            continue;
        }


        if (g_his_energy.valid != ENERGY_DATA_VALID)
        {
            continue;
        }

        // 验证crc，读取不区分原数据和备份
        if (crc_cal((unsigned char*)&g_his_energy, sizeof(energy_info_cal_t) - 2) != g_his_energy.crc)
        {
            LOG_E("%s:%d| %d crc fail", __FUNCTION__ , __LINE__, his_energy_pos);
            continue;
        }

        if(check_time_range(g_his_energy.curr_time) != SUCCESSFUL)   // 检查时间是否有效
        {
            LOG_E("%s:%d| %d time invalid %d-%d-%d %d-%d-%d", __FUNCTION__ , __LINE__, his_energy_pos, g_his_energy.curr_time.year, 
                  g_his_energy.curr_time.month, g_his_energy.curr_time.day, g_his_energy.curr_time.hour, g_his_energy.curr_time.minute, g_his_energy.curr_time.second);
            continue;
        }

        // 存过数据 
        LOG_E("%s:%d| his_time: %d %d-%d-%d %d-%d-%d  curr_time: %d-%d-%d %d-%d-%d", __FUNCTION__ , __LINE__, his_energy_pos,g_his_energy.curr_time.year, 
              g_his_energy.curr_time.month, g_his_energy.curr_time.day, g_his_energy.curr_time.hour, g_his_energy.curr_time.minute, g_his_energy.curr_time.second,
              curr_time->year, curr_time->month, curr_time->day, curr_time->hour, curr_time->minute, curr_time->second);
        LOG_E("%s:%d| %d total_energy: %f  year: %f  month: %f  day: %f  hour: %f", __FUNCTION__ , __LINE__, 
              his_energy_pos, g_his_energy.total_accum_energy, g_his_energy.curr_year_energy, g_his_energy.curr_month_energy, g_his_energy.curr_day_energy, g_his_energy.curr_hour_energy);
        // 存过数据 
        his_energy(&g_his_energy, curr_time);

        // 移动his_energy数据到0，并且添加备份数据，当前的数据不用清理，因为重启时会从零开始往后搜索，搜索到后就会停止
        his_energy_move_zero(&g_his_energy);
        return;
    }

    // 没有存过数据
    rt_memset(&g_his_energy, 0x00, sizeof(g_his_energy));
    his_energy_move_zero(&g_his_energy);
    return;
}

void his_energy_move_zero(energy_info_cal_t* his_energy)
{
    part_data_t part_data;

    part_data.buff = (unsigned char*)his_energy;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), ENERGY_PART);
    part_data.len = sizeof(energy_info_cal_t);
    part_data.offset = CURR_START_ADDR;
    // 将his_energy从零位置开始写
    if (SUCCESSFUL != storage_process(&part_data, write_opr))
    {   
        LOG_E("%s:%d| write energy fail", __FUNCTION__ , __LINE__);
        return;
    }

    // 备份数据
    part_data.offset = CURR_START_ADDR + sizeof(energy_info_cal_t);
    if (SUCCESSFUL != storage_process(&part_data, write_opr))
    {   
        LOG_E("%s:%d| write energy fail", __FUNCTION__ , __LINE__);
        return;
    }

    g_his_energy_pos = 0;
    return;
}

static float get_act_power(dev_inst_t* dev_dc_ac)
{   
    float act_power = 0.0;
    RETURN_VAL_IF_FAIL(dev_dc_ac != NULL, 0);
    if (dev_dc_ac[0].state == STAT_COMM_FAIL)
    {
        return act_power;
    }
    else
    {  
        get_one_data(DAC_DATA_ID_ACTIVE_POWER, &act_power);
        // 判断有功功率是否为正值
        return max(act_power, 0.0);
    }
}

static void save_all_record(time_base_t* last_time)
{
    energy_record_t one_record = {0};
    time_base_t time_base = {0};
    float energy = 0.0;

    rt_memset(&one_record, 0x00, sizeof(energy_record_t));
    rt_memset(&time_base, 0x00, sizeof(time_base_t));
    energy = g_his_energy.curr_hour_energy;
    time_base.year = last_time->year;
    time_base.month = last_time->month;
    time_base.day = last_time->day;
    time_base.hour = last_time->hour;
    create_one_record(&one_record, &time_base, energy);
    save_one_record(HIS_HOUR, &one_record);

    rt_memset(&one_record, 0x00, sizeof(energy_record_t));
    energy = get_energy_by_cal(HIS_DAY, last_time);
    time_base.hour = 0;
    create_one_record(&one_record, &time_base, energy);
    save_one_record(HIS_DAY, &one_record);

    rt_memset(&one_record, 0x00, sizeof(energy_record_t));
    energy = get_energy_by_cal(HIS_MONTH, last_time);
    time_base.day = 0;
    create_one_record(&one_record, &time_base, energy);
    save_one_record(HIS_MONTH, &one_record);

    rt_memset(&one_record, 0x00, sizeof(energy_record_t));
    energy = get_energy_by_cal(HIS_YEAR, last_time);
    time_base.month = 0;
    create_one_record(&one_record, &time_base, energy);
    save_one_record(HIS_YEAR, &one_record);
    return;
}

// 重启的时候保存数据
void save_record_restart(void)
{
    time_base_t curr_time;
    get_time(&curr_time);
    save_minute_energy();
    save_all_record(&curr_time);
    return;
}

static void hour_no_change(void)
{
    g_count++;
    if (g_count >= MINUTE_10)
    {
        g_count = 0;
        // 只存储g_his_energy
        save_minute_energy();
    }
    return;
}

static void hour_change(void)
{
    g_his_energy.curr_hour_energy = 0;
    save_minute_energy();
    g_count = 0;

    return;
}

static void day_change(void)
{
    g_his_energy.curr_hour_energy = 0;
    g_his_energy.curr_day_energy = 0;
    save_minute_energy();
    g_count = 0;
    return;
}

static void month_change(void)
{
    g_his_energy.curr_hour_energy = 0;
    g_his_energy.curr_day_energy = 0;
    g_his_energy.curr_month_energy = 0;
    save_minute_energy();
    g_count = 0;
    return;
}

static void year_change(void)
{
    g_his_energy.curr_hour_energy = 0;
    g_his_energy.curr_day_energy = 0;
    g_his_energy.curr_month_energy = 0;
    g_his_energy.curr_year_energy = 0;
    save_minute_energy();
    g_count = 0;
    return;
}

static int get_time_cmp_status(time_base_t* start_time, time_base_t* end_time)
{   
    if(start_time->year != end_time->year)
    {
        return YEAR_DIFF;
    }

    if(start_time->month != end_time->month)
    {
        return YEAR_SAME;
    }

    if(start_time->day != end_time->day)
    {
        return MONTH_SAME;
    }

    if(start_time->hour != end_time->hour)
    {
        return DAY_SAME;
    }

    return HOUR_SAME;
}

static void time_cmp_period(time_base_t* curr_time, time_base_t* last_time)
{   
    int time_cmp = 0;
    if(get_time_cmp_status(curr_time, last_time) == HOUR_SAME)
    {
        hour_no_change();
        return;
    }
    
    time_cmp = time_base_cmp(last_time, curr_time);
    save_all_record(last_time);
    if (TIME_DOWN == time_cmp)
    {   
        // 时间下降
        save_all_record(curr_time);
    }

    // 这里需要判断时间是否往前设置
    if(get_time_cmp_status(curr_time, last_time) == DAY_SAME)
    {
        hour_change();
        return;
    }

    if(get_time_cmp_status(curr_time, last_time) == MONTH_SAME)
    {
        day_change();
        return;
    }

    if(get_time_cmp_status(curr_time, last_time) == YEAR_SAME)
    {
        month_change();
        return;
    }

    year_change();
    return;
}


void accum_energy_period(dev_inst_t* dev_dc_ac)
{   
    RETURN_IF_FAIL(dev_dc_ac != NULL);

    time_base_t curr_time;
    get_time(&curr_time);

    rt_memcpy(&g_his_energy.curr_time, &curr_time, sizeof(time_base_t));

    // 累计电量
    accu_his_energy(&g_his_energy, dev_dc_ac);
    time_cmp_period(&curr_time, &g_last_time);
    rt_memcpy(&g_last_time, &curr_time, sizeof(time_base_t));
    return;
}

static int get_next_his_energy_write_pos(int his_energy_pos)
{
    // 当读取的是最后的数据时，下次写入位置为0
    int next_pos = (his_energy_pos + 2) % SAVE_TURN;
    // 算上备份的位置
    return next_pos;
}

// 保存10分钟的电量
static void save_minute_energy()
{
    // 写数据
    int his_energy_next_pos = get_next_his_energy_write_pos(g_his_energy_pos);
    part_data_t part_data;
    part_data.buff = (unsigned char*)&g_his_energy;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), ENERGY_PART);
    part_data.len = sizeof(energy_info_cal_t);
    part_data.offset = CURR_START_ADDR + his_energy_next_pos * sizeof(energy_info_cal_t);
    g_his_energy.valid = ENERGY_DATA_VALID;
    write_his_energy(&part_data, &g_his_energy);

    // 擦除上次的数据
    energy_info_cal_t his_energy_erase;
    rt_memset(&his_energy_erase, 0x00, sizeof(his_energy_erase));
    part_data.buff = (unsigned char*)&his_energy_erase;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), ENERGY_PART);
    part_data.len = sizeof(energy_info_cal_t);
    part_data.offset = CURR_START_ADDR + g_his_energy_pos * sizeof(energy_info_cal_t);
    write_his_energy(&part_data, &g_his_energy);

    // 更新当前的数据的位置
    g_his_energy_pos = his_energy_next_pos;

    return;
};


void write_his_energy(part_data_t* part_data, energy_info_cal_t* his_energy)
{
    his_energy->crc = crc_cal((unsigned char*)his_energy, sizeof(energy_info_cal_t) - 2);
    if (SUCCESSFUL != storage_process(part_data, write_opr))
    {   
        LOG_E("%s:%d| write energy fail", __FUNCTION__ , __LINE__);
        return;
    }

    // 写备份数据
    part_data->offset = part_data->offset + sizeof(energy_info_cal_t);
    if (SUCCESSFUL != storage_process(part_data, write_opr))
    {   
        LOG_E("%s:%d| write energy fail", __FUNCTION__ , __LINE__);
        return;
    }

    return;
}

// 累加电量，当前小时，年，月，日，累计   都累加
static void accu_his_energy(energy_info_cal_t* his_energy, dev_inst_t* dev_dc_ac)
{
    rt_tick_t cur_clock_time = 0;
    cur_clock_time = rt_tick_get_millisecond();
    int cost_time_ms = cur_clock_time - g_last_clock_time;
    float act_power = get_act_power(dev_dc_ac);
    // 每秒钟累计一次,后面可能需要20ms累计一次
    float sec_energy = (act_power * cost_time_ms / RT_TICK_PER_SECOND) / HOUR;
    
    his_energy->curr_hour_energy += sec_energy;
    his_energy->curr_day_energy += sec_energy;
    his_energy->curr_month_energy += sec_energy;
    his_energy->curr_year_energy += sec_energy;
    his_energy->total_accum_energy += sec_energy;

    // 数据写入数据字典
    set_energy_data();
    g_last_clock_time = cur_clock_time;
}

static void set_energy_data(void)
{   
    float day_co2 = 0.0, month_co2 = 0.0, year_co2 = 0.0, accmu_co2 = 0.0;
    float curr_day_energy = 0.0, curr_month_energy = 0.0, curr_year_energy = 0.0, total_accum_energy = 0.0;

    curr_day_energy = g_his_energy.curr_day_energy;
    curr_month_energy = g_his_energy.curr_month_energy;
    curr_year_energy = g_his_energy.curr_year_energy;
    total_accum_energy = g_his_energy.total_accum_energy;
    // 数据写入数据字典
    set_one_data(DAC_DATA_ID_DAILY_POWER_GENERATION, &curr_day_energy);
    set_one_data(DAC_DATA_ID_MONTH_POWER_GENERATION, &curr_month_energy);
    set_one_data(DAC_DATA_ID_YEAR_POWER_GENERATION, &curr_year_energy);
    set_one_data(DAC_DATA_ID_ACCUMLATE_POWER_GENERATION, &total_accum_energy);

    day_co2 = g_his_energy.curr_day_energy * CO2_FACTOR;
    month_co2 = g_his_energy.curr_month_energy * CO2_FACTOR;
    year_co2 = g_his_energy.curr_year_energy * CO2_FACTOR;
    accmu_co2 = g_his_energy.total_accum_energy * CO2_FACTOR;
    set_one_data(DAC_DATA_ID_CO2_DAY_EMISSION, &day_co2);
    set_one_data(DAC_DATA_ID_CO2_MONTH_EMISSION, &month_co2);
    set_one_data(DAC_DATA_ID_CO2_YEAR_EMISSION, &year_co2);
    set_one_data(DAC_DATA_ID_CO2_ACCMULATE_EMISSION, &accmu_co2);
}

static void create_one_record(energy_record_t* one_record, time_base_t* last_time, float energy)
{
    one_record->energy = energy;
    rt_memcpy(&one_record->save_time, last_time, sizeof(time_base_t));
    one_record->crc = crc_cal((unsigned char*)one_record, sizeof(energy_record_t) - 2);
}

float get_energy_by_cal(int type, time_base_t* save_time)
{   
    double energy = 0.0;   
    int read_num = 0, read_index = 0;
    int record_index = 0;
    struct stat file_stat = {0};
    part_data_t part_data = { 0 };

    storage_stat(g_his_energy_record[type + 1].file_name, &file_stat);
    rt_memset(tmp_records, 0x00, sizeof(tmp_records));
    part_data.buff = (unsigned char*)tmp_records;
    part_data.len = sizeof(tmp_records);
    part_data.offset = 0;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), "%s", g_his_energy_record[type + 1].file_name);

    read_num = (file_stat.st_size + sizeof(tmp_records) - 1) / sizeof(tmp_records);
    for(read_index = 0;read_index < read_num; read_index++)
    {
        rt_memset(tmp_records, 0x00, sizeof(tmp_records));
        if (SUCCESSFUL != storage_process(&part_data, read_opr))
        {   
            LOG_E("%s:%d| read energy fail", __FUNCTION__ , __LINE__);
            return energy;
        }

        for(record_index = 0; record_index < RCD_NUM; record_index++)
        {   
            if((get_time_cmp_status(&tmp_records[record_index].save_time, save_time) <= g_his_energy_record[type].time_cmp_status) 
                && (crc_cal((unsigned char*)&tmp_records[record_index], sizeof(energy_record_t) - 2) == tmp_records[record_index].crc))
            {   
                energy += tmp_records[record_index].energy;
            }
        }
        part_data.offset += part_data.len;
    }
    return energy;
}


int file_exists(char* filename)
{   
    struct stat sb;
    if (storage_stat(filename, &sb) < 0)
        return FALSE; 

    return TRUE;
}

static int time_base_cmp(time_base_t* start, time_base_t* end)
{   
    time_t start_time = timestruct_to_time_t(start);
    time_t end_time = timestruct_to_time_t(end);
    if((end_time - start_time) > 0)
    {
        return TIME_UP;
    }
    else if((end_time - start_time) < 0)
    {
        return TIME_DOWN;
    }
    else
    {
        return TIME_SAME;
    }
}

int read_last_record(int type, energy_record_t* one_record)
{   
    struct stat file_stat = {0};
    part_data_t part_data = { 0 };
    storage_stat(g_his_energy_record[type].file_name, &file_stat);

    part_data.buff = (unsigned char*)one_record;
    part_data.len = sizeof(energy_record_t);
    part_data.offset = file_stat.st_size - sizeof(energy_record_t);
    rt_snprintf_s(part_data.name, sizeof(part_data.name), "%s", g_his_energy_record[type].file_name);
    if (SUCCESSFUL != storage_process(&part_data, read_opr))
    {   
        LOG_E("%s:%d| read last record fail", __FUNCTION__ , __LINE__);
        return FAILURE;
    }

    if(crc_cal((unsigned char*)one_record, sizeof(energy_record_t) - 2) != one_record->crc)
    {
        LOG_E("%s:%d| crc error", __FUNCTION__ , __LINE__);
        return FAILURE;
    }

    return SUCCESSFUL;
}


int record_max_check(int type)
{   
    int copy_num = 0;
    int copy_index = 0;
    struct stat file_stat = {0};
    part_data_t part_data = { 0 };
    part_data_t part_data_tmp = { 0 };
    storage_stat(g_his_energy_record[type].file_name, &file_stat);
    if(file_stat.st_size >= (2 * g_his_energy_record[type].record_size))
    {   
        copy_num = (file_stat.st_size - g_his_energy_record[type].record_size + sizeof(tmp_records) -1) / sizeof(tmp_records);
        part_data.buff = (unsigned char*)tmp_records;
        part_data.len = sizeof(tmp_records);
        part_data.offset = file_stat.st_size - g_his_energy_record[type].record_size;
        rt_snprintf_s(part_data.name, sizeof(part_data.name), "%s", g_his_energy_record[type].file_name);
        part_data_tmp.buff = (unsigned char*)tmp_records;
        part_data_tmp.len = sizeof(tmp_records);
        part_data_tmp.offset = 0;
        rt_snprintf_s(part_data_tmp.name, sizeof(part_data_tmp.name), "%s", g_his_energy_record[type].file_name_tmp);
        for(copy_index = 0; copy_index < copy_num; copy_index++)
        {
            // 拷贝到另一个文件中
            rt_memset(tmp_records, 0x00, sizeof(tmp_records));
            if (SUCCESSFUL != storage_process(&part_data, read_opr))
            {   
                return FAILURE;
            }

            part_data_tmp.len = part_data.len;


            if (SUCCESSFUL != storage_process(&part_data_tmp, write_opr))
            {   
                return FAILURE;
            }

            part_data_tmp.offset += part_data_tmp.len;
            part_data.offset += part_data.len;
        }

        storage_unlink(g_his_energy_record[type].file_name);
        storage_rename(g_his_energy_record[type].file_name_tmp, g_his_energy_record[type].file_name);
    }
    return SUCCESSFUL;
}


int save_time_increase(int type, energy_record_t* one_record)
{   
    struct stat file_stat = {0};
    part_data_t part_data = { 0 };

    storage_stat(g_his_energy_record[type].file_name, &file_stat);
    part_data.buff = (unsigned char*)one_record;
    part_data.len = sizeof(energy_record_t);
    part_data.offset = file_stat.st_size;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), "%s", g_his_energy_record[type].file_name);
    if (SUCCESSFUL != storage_process(&part_data, write_opr))
    {   
        LOG_E("%s:%d| write_opr fail", __FUNCTION__ , __LINE__);
        return FAILURE;
    }

    if(SUCCESSFUL != record_max_check(type))
    {
        return FAILURE;
    }

    return SUCCESSFUL;
}


static int get_tmp_records_index(energy_record_t* one_record)
{   
    int index = 0;
    int time_cmp = 0;
    for(index = 0; index < sizeof(tmp_records) / sizeof(energy_record_t); index++)
    {   
        time_cmp = time_base_cmp(&one_record->save_time, &tmp_records[index].save_time);
        if((time_cmp == TIME_SAME) || (time_cmp == TIME_UP))
        {
            return index;
        }
    }

    return index;
}

// 时间减少 只删除，不保存
static int save_time_decrease(int type, energy_record_t* one_record)
{   
    int copy_num = 0;
    int copy_index = 0;
    int index = 0; 

    struct stat file_stat = {0};
    part_data_t part_data = { 0 };
    part_data_t part_data_tmp = { 0 };
    storage_stat(g_his_energy_record[type].file_name, &file_stat);

    copy_num = (file_stat.st_size + sizeof(tmp_records) - 1) / sizeof(tmp_records);
    part_data.buff = (unsigned char*)tmp_records;
    part_data.len = sizeof(tmp_records);
    part_data.offset = 0;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), "%s", g_his_energy_record[type].file_name);

    part_data_tmp.buff = (unsigned char*)tmp_records;
    part_data_tmp.len = sizeof(tmp_records);
    part_data_tmp.offset = 0;
    rt_snprintf_s(part_data_tmp.name, sizeof(part_data_tmp.name), "%s", g_his_energy_record[type].file_name_tmp);

    for(copy_index = 0; copy_index < copy_num; copy_index++)
    {   
        rt_memset(tmp_records, 0x00, sizeof(tmp_records));
        if (SUCCESSFUL != storage_process(&part_data, read_opr))
        {
            return FAILURE;
        }

        index = get_tmp_records_index(one_record);
        if(index  < RCD_NUM)
        {
            break;
        }

        part_data_tmp.len = part_data.len;
        if (SUCCESSFUL != storage_process(&part_data_tmp, write_opr))
        {
            return FAILURE;
        }

        part_data_tmp.offset += part_data_tmp.len;
        part_data.offset += sizeof(tmp_records);
    }

    // 处理不是RCD_NUM整数倍的
    if(index != 0)
    {
        part_data_tmp.len = index * sizeof(energy_record_t);
        if (SUCCESSFUL != storage_process(&part_data_tmp, write_opr))
        {
            return FAILURE;
        }
    }

    storage_unlink(g_his_energy_record[type].file_name);
    storage_rename(g_his_energy_record[type].file_name_tmp, g_his_energy_record[type].file_name);

    return SUCCESSFUL;
}

int save_time_same(int type, energy_record_t* one_record)
{   
    struct stat file_stat = {0};
    part_data_t part_data = { 0 };
    storage_stat(g_his_energy_record[type].file_name, &file_stat);

    part_data.buff = (unsigned char*)one_record;
    part_data.len = sizeof(energy_record_t);
    part_data.offset = file_stat.st_size - sizeof(energy_record_t);
    rt_snprintf_s(part_data.name, sizeof(part_data.name), "%s", g_his_energy_record[type].file_name);
    if (SUCCESSFUL != storage_process(&part_data, write_opr))
    {   
        LOG_E("%s:%d| write energy fail", __FUNCTION__ , __LINE__);
        return FAILURE;
    }
    return SUCCESSFUL;
}

static void save_one_record(int type, energy_record_t* one_record)
{
    int time_cmp = 0;
    energy_record_t energy_record = {0};

    if(read_last_record(type, &energy_record) == FAILURE)
    {
        save_time_increase(type, one_record);
        return;
    }

    time_cmp = time_base_cmp(&energy_record.save_time, &one_record->save_time);
    if(time_cmp == TIME_UP)
    {
        save_time_increase(type, one_record);
    }
    else if(time_cmp == TIME_DOWN)
    {
        save_time_decrease(type, one_record);
    }
    else
    {
        save_time_same(type, one_record);
    }
}

int clean_his_energy()
{
    float f_zero = 0.0;
    char* erase_data = NULL;
    part_data_t part_data = {0};
    // 清除实时计算的结构体
    rt_memset(&g_his_energy, 0x00, sizeof(energy_info_cal_t));

    // 清除存储在eeprom的电量数据
    erase_data = (char*)RAM_MALLOC(ENERGY_PART_SIZE);
    RETURN_VAL_IF_FAIL(erase_data != NULL, FAILURE);
    rt_memset(erase_data, 0x00, ENERGY_PART_SIZE);
    part_data.buff = (unsigned char*)erase_data;
    rt_snprintf(part_data.name, sizeof(part_data.name),"%s", ENERGY_PART);
    part_data.len = ENERGY_PART_SIZE;

    if (SUCCESSFUL != storage_process(&part_data, write_opr))
    {   
        RAM_FREE(erase_data);
        LOG_E("%s:%d| erase energy part fail", __FUNCTION__ , __LINE__);
        return FAILURE;
    }
    RAM_FREE(erase_data);

    // 清除实时显示的数据
    set_one_data(DAC_DATA_ID_DAILY_POWER_GENERATION, &f_zero);
    set_one_data(DAC_DATA_ID_MONTH_POWER_GENERATION, &f_zero);
    set_one_data(DAC_DATA_ID_YEAR_POWER_GENERATION, &f_zero);
    set_one_data(DAC_DATA_ID_ACCUMLATE_POWER_GENERATION, &f_zero);
    set_one_data(DAC_DATA_ID_CO2_DAY_EMISSION, &f_zero);
    set_one_data(DAC_DATA_ID_CO2_MONTH_EMISSION, &f_zero);
    set_one_data(DAC_DATA_ID_CO2_YEAR_EMISSION, &f_zero);
    set_one_data(DAC_DATA_ID_CO2_ACCMULATE_EMISSION, &f_zero);

    LOG_E("%s:%d| clean already", __FUNCTION__ , __LINE__);

    return SUCCESSFUL;
}

