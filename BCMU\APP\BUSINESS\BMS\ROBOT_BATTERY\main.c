#include <stdio.h>
#include <rtthread.h>
#include "utils_server.h"
#include "server_id.h"
#include "storage.h"
#include "para_manage.h"
#include "alarm_manage.h"
#include "alarm_register.h"
#include "partition_table.h"
#include "north_main_can.h"
#include "utils_data_transmission.h"
#include "realdata_save.h"
#include "unified_id_interface.h"
#include "protocol_remote_comm.h"

static char s_north_thread_stack[4096];
static rt_device_t wdg_dev;
static char s_alarm_thread_stack[1024];

static server_info_t g_server_group[] = {
    /*   服务ID                         服务名字             栈大小                           栈的起始地址               优先级*/
    {{{ROBOT_BAT_NORTH_CAN_SERVER_ID,      "north_can",        sizeof(s_north_thread_stack),    s_north_thread_stack,       13}}, init_north_can, north_main_can},
    {{{ALARM_MANAGE_SERVER_ID,             "alarm_manage",     sizeof(s_alarm_thread_stack),    s_alarm_thread_stack,       13}}, init_alarm_manage, alarm_main},
};

static protocol_process_t protocol_process_tab[] = {
    {PROTOCOL_REMOTE_COMM, remote_comm_pack, remote_comm_parse},
};

static dev_init_t dev_init_tab[] = {
    {DEV_ROBOT_BAT_CAN, init_dev_robot_bat_can, NULL},
};

static link_type_t link_type_tab[] = {
     {LINK_CAN, can_dev_init, can_dev_read, can_dev_write, can_dev_set},
};

static link_inst_t link_inst_tab[] = {
    {LINK_ROBOT_BAT_CAN, LINK_CAN, "can0", CAN_FRAME_DATA_LEN_8},
};

int init_file_sys(void)
{
    if(SUCCESSFUL != init_file_sys_mutex())
    {
        return RT_ERROR;
    }

    return RT_EOK;
}
INIT_ENV_EXPORT(init_file_sys);

// 定义接口表
static const data_access_interface_t id_base_interface = {
    .get_data = unified_get_data,
    .set_data = unified_set_data,
    .linear_search = linear_search_id_index
};


static void feed_dog(void)
{
    rt_device_control(wdg_dev, RT_DEVICE_CTRL_WDT_KEEPALIVE, NULL);
    return;
}

int init_watchdog(void)
{
    wdg_dev = rt_device_find("wdt");
    if (RT_NULL == wdg_dev)
    {
        return -RT_ERROR;
    }
    if (RT_EOK != rt_device_control(wdg_dev, RT_DEVICE_CTRL_WDT_START, RT_NULL))
    {
        return -RT_ERROR;
    }
    rt_thread_idle_sethook(feed_dog);

    return RT_EOK;
}


int main(void){
    init_watchdog();
    init_crc();
    initFileSys();
    if (SUCCESSFUL != storage_init(&part_tab[0], sizeof(part_tab)/sizeof(part_info_t))) {
        return FAILURE;
    }
    init_flash_page_size(FLASH_PAGE_SIZE);
    register_product_para();
    if(init_para_manage() != SUCCESSFUL)
    {
        return FAILURE;
    }
    // 初始化ID化空间
    init_real_data_memory();
    register_link_inst_tab_info(link_inst_tab, sizeof(link_inst_tab)/sizeof(link_inst_tab[0]));
    register_link_type_info(link_type_tab, sizeof(link_type_tab) / sizeof(link_type_t));
    // 注册 协议所使用的函数接口
    register_protocol_process_info(protocol_process_tab, sizeof(protocol_process_tab) / sizeof(protocol_process_t));
    register_data_access_interface(&id_base_interface);
    // 注册协议
    init_dev_tab(dev_init_tab, sizeof(dev_init_tab)/sizeof(dev_init_tab[0]));
    //注册告警表
    register_robot_alarm();
    create_server(&g_server_group[0], sizeof(g_server_group)/sizeof(g_server_group[0]));
    return 0;
}








