#include "common.h"
#include "CommCan.h"
#include "CommCan2.h"
#include "sample.h"
#include "realAlarm.h"
#include "utils_heart_beat.h"
#include "thread_id.h"
#include "battery.h"
#include "para.h"
#include "comm.h"
#include "protocol.h"
#include "MdbsRtu.h"
#include "commBdu.h"
#include "prtclDL.h"
#include "fileSys.h"
#include "hisdata.h"
#include "flash.h"
#include <rtdevice.h>
#include "BattSleep.h"
#include "rtc_PFC8563.h"
#include "sys/time.h"
#include "pdt_version.h"
#include "const_define.h"
#include "fileSys.h"
#include "led.h"
#include "circle_buff.h"
#include "MultBattData.h"
#define CAN2_GROUP_ADDR      0x00


#define SCU_ADDR 0x1
//组间并机通信ID
#define FN_EQUAL_CURR_CAN2       0x81  //CAN2总线上的命令码和CAN1总线上的命令码要定义为不同的，避免CAN1和CAN2插错导致命令处理错误
#define FN_SYNC_PARA_CAN2        0x82  //同步簇间参数

#define FN_ADDR_MODE_CAN2       0x02
#define FN_SET_ADDR_CAN2        0x03
#define FN_SWITCH_ADDR_CAN2        0x05

#define FN_ALARM_OCCUR_UPLOAD_CAN2      0x07
#define FN_ALARM_MISS_UPLOAD_CAN2       0x08
#define FN_RESET_CAN2_ADD_COMPETE       0x09

#define FN_ADDR_COMPETE_CAN2     0x80

#define FN_ADDR_FIND_CAN2        0x04
#define FN_HEART_BEAT_CAN2       0x06

#define REALDATA_CYCLE_CAN2_1S  (100)

/***********************  变量定义  ************************/
Static T_CommStruct s_tCommCan_CAN2;
Static T_SysPara s_tSysPara;
static INT32S s_slCanDealCounter_CAN2 = 0;
Static T_CAN_DataCache s_tCanDataCache_CAN2;
static T_CAN_DataCache s_tCanDataCache_BMS_CAN2;
static rt_device_t can2_dev;
struct rt_semaphore can2_rx_sem;
Static T_BmsTCB s_tMyBms_CAN2;
static BYTE s_ucCAN2RepeteAdr = 0;
static rt_mutex_t s_ptMutexCan2 = RT_NULL;
static UINT32 s_can_2_InterconnetTestCounter = 0;  // CAN2对接测试成功计数
static BYTE s_can_2_InterconnectTestData[5] = {0x06, 0x07, 0x08, 0x09, 0x0A};
Static UINT32 s_u32cnt = 0;//用于判断簇间均流指令超时
//static BYTE s_ucMastertimes = 0;
static BYTE s_can2_connected_flag = False;
static BYTE s_ucCAN2Rtn = RTN_CORRECT_CAN2;
static rt_timer_t s_waitCan2AddrFindTimer = RT_NULL;
Static BYTE s_ucCAN2AddrCompFLAG = 0;
static BYTE s_ucSCAdr = 0;
Static T_Can2Data s_atCAN2SlavesData[CAN2_SLAVE_NUM];
Static T_Can2ObjData s_tEqualObjData;
Static BOOLEAN s_bCan2NorthCommDisconnect = True;     // can通信断状态(只针对北向，不针对簇间)
Static UINT32 s_ulCan2NorthDisconnCnt = 0; //can2北向通讯通讯断计数器
static UINT32 s_ulCan2DisconnCnt = 0; //can2通讯通讯断计数器

/*****************  静态函数原型定义  **********************/
static SHORT ChangeCanFrameToPkt_BMS_CAN2( T_CommStruct* ptCommCan, T_Can_Frame * ptCan_Frame);
Static SHORT ChangeCanFrameToPkt_CAN2(T_Can_Frame * ptCan_Frame);
Static SHORT Proc_Prtcl_Type_North_CAN2(void);
Static SHORT Proc_Frame_North_CAN2(T_CommStruct* ptCommCan, T_Can_Frame* ptCan_Frame);
static SHORT Proc_Prtcl_Type_BMS_CAN2(T_CAN_Header tHeader);
static void CheckApptestCan2(T_Can_Frame * ptCan_Frame);
static BOOLEAN Can2Drive_Write(BYTE *pIn, SHORT len, BYTE *ptHeader);
Static void sendCan2Frm(BYTE ucDstAdr, BYTE ucDstDev,  BYTE* pucBuff);

static void ParseCAN2CompeteFrm(BYTE *p);
Static SHORT Proc_CAN2_Frame(T_Can_Frame * ptCan_Frame);
Static void Proc_CAN2_Frame_Type(T_Can_Frame* ptCan_Frame);
static void Proc_Frame_BMS_CAN2(T_CommStruct* ptCommCan, T_Can_Frame* ptCan_Frame);
Static SHORT DealBMSFrameCAN2( T_Can_Frame * ptCan_Frame);
Static BOOLEAN DealDLFrameCAN2(T_CommStruct* ptCommCan);

Static void SendCAN2CompeteFrm(void);
Static BOOLEAN CheckCAN2Address(T_Can_Frame * ptCan_Frame);
static rt_err_t can2_rx_call(rt_device_t dev, rt_size_t size);
Static BOOLEAN SendCAN2EqualizeCurrFrm(void);
static BOOLEAN ParseCAN2EqualizeCurrFrm(T_Can_Frame * ptCan_Frame);
static SHORT ParseCAN2HeartBeatFrm(T_Can_Frame * ptCan_Frame);
static BOOLEAN ParseCAN2AddrFindFrm(T_Can_Frame * ptCan_Frame);
static BOOLEAN DealCan2AddrFind(T_Can_Frame * ptCan_Frame);
static BOOLEAN ParseCAN2AddrModeFrm(T_Can_Frame * ptCan_Frame);
static BOOLEAN ParseSetCAN2AddrFrm(T_Can_Frame * ptCan_Frame);
static BOOLEAN ParseSwitchCAN2AddrFrm(T_Can_Frame * ptCan_Frame);
Static BOOLEAN modifyCAN2Addr(BYTE ucAddr);
static BOOLEAN InitCan2SlaveData(void);
static BOOLEAN SendCAN2EqualizeCurrRsp(void);
Static BOOLEAN SynCAN2Para(void);
static BOOLEAN ParseCAN2Para(T_Can_Frame * ptCan_Frame);
Static BOOLEAN SetCan2CommDisconnect(BOOLEAN flag);
Static void masterCommCAN2(void);
Static void BMSAddrCompeteCAN2(void);
Static BOOLEAN IsSleepPrtDisconComm(void);
Static BOOLEAN ParseRestartCan2AddrCompeteFrm(T_Can_Frame * ptCan_Frame);

// 只针对CAN2北向通信，不判断CAN2之间通信
BOOLEAN JudgeCan2CommDisconnect(void)
{
    return s_bCan2NorthCommDisconnect;
}


Static BOOLEAN SetCan2CommDisconnect(BOOLEAN flag)
{
    return s_bCan2NorthCommDisconnect = flag;
}


BOOLEAN GetCan2ConnectedFlag(void){
    return s_can2_connected_flag;
}

void SetCan2ConnectedFlag(BOOLEAN flag){
    s_can2_connected_flag = flag;
}

BYTE GetBMSCan2Addr(void)
{
    return s_tMyBms_CAN2.ucAddress;
}

void SetBMSCan2Addr(BYTE addr){
    RETURN_IF_FAIL(addr != 0x00);//手动设置柜间地址不允许为0，此时不允许切换
    s_tMyBms_CAN2.ucAddress = addr;
    //设置地址时需要发送一次地址竞争帧
    SendCAN2CompeteFrm();
    saveResetData(BMS_CAN2_ADDR, s_tMyBms_CAN2.ucAddress );
    return;
}

BOOLEAN IsMasterCan2(void)
{
    if(s_tMyBms_CAN2.ucMasterAddr ==  s_tMyBms_CAN2.ucAddress  && IsMaster())
    {
        return True;
    }
    InitCan2SlaveData();
    return False;
}


Static BOOLEAN masterSlaveComm_CAN2(void)
{
    if (s_tMyBms_CAN2.ucMasterAddr == s_tMyBms_CAN2.ucAddress)
    {
       if(IsSleepPrtDisconComm())//CAN2主机簇内所有休眠或保护，切换为从机（CAN2均流标识自己的数据无效）
       {
           s_tMyBms_CAN2.wCnt = 0;
           s_tMyBms_CAN2.ucMasterAddr++;
        //    s_tEqualObjData.bValid = False;//CAN2主机簇内所有休眠或保护均流标识为False，表示均流数据无效
        //    SynCAN2ObjData(&s_tEqualObjData);
           InitCan2SlaveData();
           return True;
       }
        masterCommCAN2();
    }
    else if (s_u32cnt > (s_tMyBms_CAN2.ucAddress + 60) * 200)//2min超时，每200ms依据地址达到超时做对应的处理
    {
        s_u32cnt = 0;
        s_ucCAN2AddrCompFLAG = 0;
        if (!(IsSleepPrtDisconComm())) //CAN2从机簇内所有休眠或保护，不切换为主机
        {
            s_tMyBms_CAN2.ucMasterAddr = s_tMyBms_CAN2.ucAddress; //否则切换为主机
            s_tEqualObjData.bValid = False;//切换为主机以后，之前旧主机发送过来的均流数据无效
            SynCAN2ObjData(&s_tEqualObjData);
        }
    }

    return True;
}

Static void masterCommCAN2(void){
    BYTE i;

    if (s_tMyBms_CAN2.wCnt % (60 * REALDATA_CYCLE_CAN2_1S) == 0)
    {
        s_tMyBms_CAN2.wCnt = 0;
        if(IsMasterCan2())
        {
            SynCAN2Para();
        }
    }

    if(s_tMyBms_CAN2.wCnt%(3*REALDATA_CYCLE_CAN2_1S) == 0)
    {
        if(IsMasterCan2())
        {
            SendCAN2EqualizeCurrFrm();
            for( i=0; i<CAN2_SLAVE_NUM; i++ )
            {
                if(s_atCAN2SlavesData[i].bExist)
                {
                    TimerPlus(s_atCAN2SlavesData[i].ucCommFailCnt, MASTER_SLAVE_COMM_FAIL_CNT);
                    if (s_atCAN2SlavesData[i].ucCommFailCnt >= MASTER_SLAVE_COMM_FAIL_CNT)
                    {
                        s_atCAN2SlavesData[i].bExist = False;
                    }
                }
            }
        }
    }
    return;
}

Static void BMSAddrCompeteCAN2(void)
{
    if (s_tMyBms_CAN2.ucMasterAddr == 0)    //地址竞争阶段
    {
        if ( s_tMyBms_CAN2.wCnt > 3 * CAN2_SLAVE_NUM )
        {
            s_tMyBms_CAN2.ucMasterAddr = 1;
            s_tMyBms_CAN2.wCnt = 0;
            InitCan2SlaveData();
            saveResetData(BMS_CAN2_ADDR, GetBMSCan2Addr());
            s_ucCAN2AddrCompFLAG = 0;
        }
        else if (s_tMyBms_CAN2.wCnt%(CAN2_SLAVE_NUM + 1) == s_tMyBms_CAN2.ucAddress)
        {
            SendCAN2CompeteFrm();           //发送竞争帧
        }
    }

    return;
}

void InitCan2(void)
{
    s_tMyBms_CAN2.wCnt = 0;
    s_tMyBms_CAN2.ucMasterAddr = 0x00;
    s_tMyBms_CAN2.ucAddress = CAN2_ADDR_START;//初始化can2地址为1
    s_tMyBms_CAN2.ucAddress = getResetData(BMS_CAN2_ADDR);
    if (s_tMyBms_CAN2.ucAddress - 1 < 1 || s_tMyBms_CAN2.ucAddress > CAN2_SLAVE_NUM)
    {
        s_tMyBms_CAN2.ucAddress = CAN2_ADDR_START;
        s_ucCAN2AddrCompFLAG = 0;
    }
    s_tMyBms_CAN2.ucGroup = CAN2_GROUP_ADDR;
    InitCan2SlaveData();
    rt_memset(&s_tEqualObjData, 0, sizeof(T_Can2ObjData));

    do
    {
        can2_dev = rt_device_find(CAN2_DEV_NAME);
    }while(RT_NULL == can2_dev);

    do
    {
        s_ptMutexCan2 = rt_mutex_create("can2_lock", RT_IPC_FLAG_PRIO);
    }while(RT_NULL == s_ptMutexCan2);

    // init_circle_buff();
    rt_sem_init(&can2_rx_sem, "can2_sem", 0, RT_IPC_FLAG_FIFO);
    rt_mutex_take(s_ptMutexCan2, RT_WAITING_FOREVER);
    rt_device_open(can2_dev, RT_DEVICE_FLAG_INT_TX | RT_DEVICE_FLAG_INT_RX);
    rt_device_set_rx_indicate(can2_dev, can2_rx_call);
    rt_mutex_release(s_ptMutexCan2);

}

static BOOLEAN InitCan2SlaveData(void)
{
    rt_memset(s_atCAN2SlavesData, 0x00, sizeof(s_atCAN2SlavesData));
    return True;
}

BOOLEAN getCAN2SlaveData(T_Can2Data * ptCAN2SlavesData)
{
    rt_memcpy(ptCAN2SlavesData, (BYTE *)&s_atCAN2SlavesData, sizeof(s_atCAN2SlavesData));
    return True;
}

/****************************************************************
函数名：Process_CAN2_Comm
入口参数：无
出口参数：无
功能：CAN2通讯主函数
****************************************************************/
void Process_CAN2_Comm(void* parameter)
{
    rt_uint32_t ulRetCnt = 0;
    struct rt_can_msg canmsg;

    pre_thread_beat_f(THREAD_CAN2_COMM);
#ifdef UNITEST
    while (IsRunning())
#else
    while(1)
#endif
    {
        thread_beat_go_on(THREAD_CAN2_COMM);
        if ( RT_EOK == rt_sem_take(&can2_rx_sem, 10) )
        {
            canmsg.hdr = -1;
            rt_mutex_take(s_ptMutexCan2, RT_WAITING_FOREVER);
            ulRetCnt = rt_device_read(can2_dev, 0, &canmsg, sizeof(canmsg));
            rt_mutex_release(s_ptMutexCan2);
            if(ulRetCnt > 0)
            {
                CheckApptestCan2((T_Can_Frame *)&canmsg);
                Proc_CAN2_Frame( (T_Can_Frame *)&canmsg );
            }
            continue;
        }

        TimerPlus(s_ulCan2NorthDisconnCnt, TIME_CAN2NORTH_MAX);
        if(TimeOut(s_ulCan2NorthDisconnCnt, TIME_CAN2NORTH_MAX))
        {
            SetCan2CommDisconnect(True); // 超时通信断
            s_ulCan2NorthDisconnCnt = 0;
        }

        s_u32cnt++;
        if (s_ulCan2DisconnCnt++ >3000)
        {
            SetCan2ConnectedFlag(False);//退CAN1主机
            s_ulCan2DisconnCnt = 0;
        }
        s_tMyBms_CAN2.wCnt++;
        masterSlaveComm_CAN2();
        BMSAddrCompeteCAN2();
    }
}

rt_err_t can2_rx_call(rt_device_t dev, rt_size_t size)
{
    rt_sem_release(&can2_rx_sem);
    return RT_EOK;
}

/***************************************************************************
 * @brief    发送簇间均流帧
 **************************************************************************/
Static BOOLEAN SendCAN2EqualizeCurrFrm(void)
{
    BYTE aucBuff[7];
    T_BCMDataStruct tBcmData;
    T_BattResult tBattOut;
    WORD wCurrData = 0;
    SHORT sVoltAdj = 0;
    rt_memset(&aucBuff, 0, 7);
    rt_memset(&tBcmData, 0, sizeof(T_BCMDataStruct));
    rt_memset(&tBattOut, 0, sizeof(T_BattResult));

    if(!IsMaster())
    {
        return False;
    }
    GetRealData(&tBcmData);
    GetBattResult(&tBattOut);
    wCurrData = (WORD)(fabs(tBattOut.fAveCurrInCluster)*100);
    sVoltAdj = (SHORT)(tBattOut.fVoltAdjVal * 100);

    aucBuff[0] = FN_EQUAL_CURR_CAN2;

    if(IsSleep())
    {
        if(GetCan2ConnectedFlag() && tBattOut.ucSleepBattStatus == BATT_MODE_DISCHARGE)
        {
            aucBuff[1] |= 0x80;
        }
    }
    else if(tBcmData.ucBattPackSta == BATT_MODE_DISCHARGE)
    {
        aucBuff[1] |= 0x80;
    }
    aucBuff[1] |= (wCurrData >> 8) & 0x7F;
    aucBuff[2] = wCurrData & 0xFF;
    aucBuff[3] = tBattOut.wAveSocInAllCluster / 256;
    aucBuff[4] = tBattOut.wAveSocInAllCluster % 256;
    aucBuff[5] = (sVoltAdj >> 8) & 0xFF;
    aucBuff[6] = sVoltAdj & 0xFF;

    sendCan2Frm(s_tMyBms_CAN2.ucGroup, DEV_TYPE_BMS, aucBuff);

    return True;
}

Static void SendCAN2CompeteFrm(void)
{
    BYTE aucBuff[7];
    ULONG ulSN;

    ulSN = getSNPart();
    aucBuff[0] = FN_ADDR_COMPETE_CAN2;
    aucBuff[1] = s_tMyBms_CAN2.ucAddress;
    aucBuff[2] = s_tSysPara.ucCAN2AddressMode & 0x01;
    rt_memcpy(&aucBuff[3], (void *)&ulSN, sizeof(ULONG));

    sendCan2Frm(s_tMyBms_CAN2.ucAddress, DEV_TYPE_BMS, aucBuff);
    return;
}

Static SHORT Proc_CAN2_Frame(T_Can_Frame * ptCan_Frame)
{
    if (False == CheckCAN2Address(ptCan_Frame))
    {
        return -1;
    }
    s_ulCan2DisconnCnt = 0;
    s_tCommCan_CAN2.ucPortType = COMM_CAN;
    s_tCommCan_CAN2.ucLinkSrcDev = ptCan_Frame->tHeader.SrcDev;
    s_tCommCan_CAN2.ucLinkSrcAdr = ptCan_Frame->tHeader.SrcAdr;
    if(ptCan_Frame->tHeader.SrcDev == DEV_TYPE_SC)
    {
        SetCan2CommDisconnect(False); // 有上位机通信，清除通信断标志
        s_ulCan2NorthDisconnCnt = 0;
        s_tCommCan_CAN2.wRecLength = ChangeCanFrameToPkt_CAN2(ptCan_Frame);
        if(0 == s_tCommCan_CAN2.wRecLength || 400 < s_tCommCan_CAN2.wRecLength)   
        {
            return -1;
        }
        if (0 == Proc_Prtcl_Type_North_CAN2()) {
            return -1;
        }
        s_ucSCAdr = ptCan_Frame->tHeader.SrcAdr;
    }

    if(ptCan_Frame->tHeader.SrcDev == DEV_TYPE_BMS)
    {
        s_tCommCan_CAN2.wRecLength = ChangeCanFrameToPkt_BMS_CAN2(&s_tCommCan_CAN2, ptCan_Frame);
        if(0 == s_tCommCan_CAN2.wRecLength || 400 < s_tCommCan_CAN2.wRecLength)     
        {
            return -1;
        }
        if (0 == Proc_Prtcl_Type_BMS_CAN2(ptCan_Frame->tHeader)) {
            return -1;
        }
    }

    Proc_CAN2_Frame_Type(ptCan_Frame);
    if(!GetApptestFlag()){
        SetCan2ConnectedFlag(True);
    }

    // 增加CAN2的点对点回包
    if (s_tCommCan_CAN2.wSendLength > 0 && ptCan_Frame->tHeader.DstAdr == s_tMyBms_CAN2.ucAddress )
    {
        ChangeIntoCan2Frm(s_tCommCan_CAN2.aucSendBuf, s_tCommCan_CAN2.wSendLength, ptCan_Frame->tHeader.SrcDev, ptCan_Frame->tHeader.SrcAdr);
    }
    s_tCommCan_CAN2.wSendLength = 0;
    return 0;
}

Static SHORT ChangeCanFrameToPkt_CAN2(T_Can_Frame * ptCan_Frame)
{
    BYTE *p = NULL;
    SHORT sRet = 0;
    BYTE i;

    if( ptCan_Frame->tDataFrame.SegNum == 0 )	
    {
        rt_memset(&s_tCanDataCache_CAN2, 0x00, sizeof(s_tCanDataCache_CAN2));
    }
    else if(ptCan_Frame->tDataFrame.SegNum != s_tCanDataCache_CAN2.ucExpectFragNum)
    {
        return -1;
    }
    //只存储7个字节的数据信息
    p = (BYTE *) ( s_tCanDataCache_CAN2.aucDataBuf +
        (UINT32)(s_tCanDataCache_CAN2.ucExpectFragNum * sizeof( ptCan_Frame->tDataFrame.aucData )) );
    for (i=0; i<sizeof( ptCan_Frame->tDataFrame.aucData ); i++)
    {
        *p = ptCan_Frame->tDataFrame.aucData[i];
        p++;
    }
    s_tCanDataCache_CAN2.ucExpectFragNum++;
    if( s_tCanDataCache_CAN2.ucExpectFragNum >= NORTH_FRAM_MAX_NUM )
    {
        s_tCanDataCache_CAN2.ucExpectFragNum = 0;
    }
    if(ptCan_Frame->tDataFrame.SegFlg == 0)
    {
        return 0;
    }
    sRet = s_tCanDataCache_CAN2.ucExpectFragNum * sizeof( ptCan_Frame->tDataFrame.aucData );
    return sRet;
}

static SHORT ChangeCanFrameToPkt_BMS_CAN2(T_CommStruct* ptCommCan, T_Can_Frame * ptCan_Frame)
{
    BYTE *p = NULL;
    SHORT sRet = 0;
    BYTE i;

    if( ptCan_Frame->tDataFrame.SegNum == 0 )	
    {
        rt_memset(&s_tCanDataCache_BMS_CAN2, 0x00, sizeof(T_CAN_DataCache));
    }
    else if(ptCan_Frame->tDataFrame.SegNum != s_tCanDataCache_BMS_CAN2.ucExpectFragNum)
    {
        return -1;
    }
    //只存储7个字节的数据信息
    p = (BYTE *) ( s_tCanDataCache_BMS_CAN2.aucDataBuf +
        (UINT32)(s_tCanDataCache_BMS_CAN2.ucExpectFragNum * sizeof( ptCan_Frame->tDataFrame.aucData)));
    for (i=0; i<sizeof( ptCan_Frame->tDataFrame.aucData ); i++)
    {
        *p = ptCan_Frame->tDataFrame.aucData[i];
        p++;
    }

    s_tCanDataCache_BMS_CAN2.ucExpectFragNum++;
    if( s_tCanDataCache_BMS_CAN2.ucExpectFragNum >= NORTH_FRAM_MAX_NUM )
    {
        s_tCanDataCache_BMS_CAN2.ucExpectFragNum = 0;
    }

    if(ptCan_Frame->tDataFrame.SegFlg == 0)
    {
        return 0;
    }

    sRet = s_tCanDataCache_BMS_CAN2.ucExpectFragNum * sizeof( ptCan_Frame->tDataFrame.aucData );
    return sRet;
}

Static SHORT Proc_Prtcl_Type_North_CAN2(void) {
    switch (s_tCanDataCache_CAN2.aucDataBuf[0])
    {
        case DownloadType:
            s_tCommCan_CAN2.ucPrtclType = PROTOCOL_DL;
            break;
        case FN_ADDR_MODE_CAN2:
        case FN_SET_ADDR_CAN2:
        case FN_SWITCH_ADDR_CAN2:
        case FN_HEART_BEAT_CAN2:
        case FN_ADDR_FIND_CAN2:
        case FN_RESET_CAN2_ADD_COMPETE:
            s_tCommCan_CAN2.ucPrtclType = PROTOCOL_BMS;
            break;
        default:
            return 0;
    }
    return 1;
}


static SHORT Proc_Prtcl_Type_BMS_CAN2(T_CAN_Header tHeader)
{
    if(tHeader.DstAdr != CAN2_GROUP_ADDR)
    {
        switch (s_tCanDataCache_BMS_CAN2.aucDataBuf[0])
        {
            case FN_ADDR_COMPETE_CAN2:
                ParseCAN2CompeteFrm(s_tCanDataCache_BMS_CAN2.aucDataBuf);   
                return 0;
            case FN_EQUAL_CURR_CAN2:
                s_tCommCan_CAN2.ucPrtclType = PROTOCOL_BMS;
                break;
            default:
                break;
        }
    }
    else
    {
        switch (s_tCanDataCache_BMS_CAN2.aucDataBuf[0])
        {
            case FN_EQUAL_CURR_CAN2:
            case FN_SYNC_PARA_CAN2:
            case FN_SWITCH_ADDR_CAN2:
                s_tCommCan_CAN2.ucPrtclType = PROTOCOL_BMS;
                break;
            default:
                break;
        }
    }
    return 1;
}

Static void Proc_CAN2_Frame_Type(T_Can_Frame* ptCan_Frame)
{
    if(ptCan_Frame == NULL)
    {
        return;
    }

    if (ptCan_Frame->tHeader.SrcDev == DEV_TYPE_BMS )
        Proc_Frame_BMS_CAN2(&s_tCommCan_CAN2,ptCan_Frame);
    else if (ptCan_Frame->tHeader.SrcDev == DEV_TYPE_SC)
        Proc_Frame_North_CAN2(&s_tCommCan_CAN2,ptCan_Frame);
    return;
}

static void Proc_Frame_BMS_CAN2(T_CommStruct* ptCommCan, T_Can_Frame* ptCan_Frame)
{
    if(ptCommCan == NULL || ptCan_Frame == NULL)
    {
        return;
    }

    switch (ptCommCan->ucPrtclType)
    {
        case PROTOCOL_BMS:
            DealBMSFrameCAN2(ptCan_Frame);
            break;
        default:
            break;
    }

    return;
}

Static BOOLEAN DealDLFrameCAN2(T_CommStruct* ptCommCan) {
    BYTE ucRsvDestDevType = 0;
    BYTE ucRsvDestDevAdr = 0;
    BYTE ucRsvTrans = 0;
    BYTE ucFunc = 0;


    RETURN_VAL_IF_FAIL(ptCommCan != NULL, False);
    RETURN_VAL_IF_FAIL(ptCommCan->wRecLength >= DOWNLOAD_APP_MIN_LEN, False);

    rt_memcpy(ptCommCan->aucRecBuf, s_tCanDataCache_CAN2.aucDataBuf, ptCommCan->wRecLength);
    ucFunc = ptCommCan->aucRecBuf[DOWNLOAD_FUNC_INDEX];
    ucRsvDestDevType = ptCommCan->aucRecBuf[DOWNLOAD_RSV_DEST_DEV_TYPE_INDEX];
    ucRsvDestDevAdr = ptCommCan->aucRecBuf[DOWNLOAD_RSV_DEST_DEV_ADR_INDEX];
    ucRsvTrans = ptCommCan->aucRecBuf[DOWNLOAD_RSV_TRANS_INDEX];

    if (IsMaster() && ucRsvTrans == DOWNLOAD_TRANS) {
        RETURN_VAL_IF_FAIL(ucRsvDestDevType == DEV_TYPE_BMS, False);
        if (IsMasterTrans(ucRsvDestDevAdr, ucFunc)) {
            ClearCommTransFlag();
            ChangeIntoCanFrm(ptCommCan->aucRecBuf, ptCommCan->wRecLength, ucRsvDestDevType, ucRsvDestDevAdr);
            //点对点透传，主机接收后不处理数据 
            RETURN_VAL_IF_FAIL(ucRsvDestDevAdr == getGrpAddr(), True);
        }
    }

    SetPrtclDLLinkType(DL_LINK_TYPE_CAN2);
    DealCanDLData(ptCommCan);
    return True;
}

Static SHORT Proc_Frame_North_CAN2(T_CommStruct* ptCommCan, T_Can_Frame* ptCan_Frame)
{
    switch (ptCommCan->ucPrtclType)
    {
        case PROTOCOL_DL:
            DealDLFrameCAN2(ptCommCan);
            break;
        case PROTOCOL_BMS:
            // 触发升级成功后，不处理其他CAN帧
            if (False == GetTriggerEndFlag()) {
                DealBMSFrameCAN2(ptCan_Frame);
            }
            break;
        default:
            return 0;
    }

    return 1;
}

BOOLEAN SendAlarmToSCUByCan2(T_Can_Frame const* ptFrame, BOOLEAN ucAlarmOccurFlag) 
{
    BYTE aucBuff[7];
    rt_time_t uiTime;

    rt_memset(&aucBuff[0], 0, sizeof(aucBuff));

    aucBuff[0] = (True == ucAlarmOccurFlag) ? FN_ALARM_OCCUR_UPLOAD_CAN2 : FN_ALARM_MISS_UPLOAD_CAN2;
    aucBuff[1] = MACHINE_NUM_PER_CABINET*(GetBMSCan2Addr()-CAN2_ADDR_START)+ptFrame->tDataFrame.aucData[1];
    uiTime = GetULongData((BYTE *)&(ptFrame->tDataFrame.aucData[2]));
    rt_memcpy(&aucBuff[2], &uiTime, sizeof(rt_time_t));
    aucBuff[6] = ptFrame->tDataFrame.aucData[6];

    sendCan2Frm(SCU_ADDR, DEV_TYPE_SC, aucBuff);
    return True;
}

BOOLEAN DirectSendAlarmToSCUByCan2(BYTE usAlarmId, BOOLEAN ucAlarmOccurFlag) 
{
    BYTE aucBuff[7];
    time_t   tTime;
    rt_time_t rtTime;

    rt_memset(&aucBuff[0], 0, sizeof(aucBuff));
    time(&tTime);
    rtTime = (rt_time_t)(tTime & 0xFFFFFFFF);

    aucBuff[0] = (True == ucAlarmOccurFlag) ? FN_ALARM_OCCUR_UPLOAD_CAN2 : FN_ALARM_MISS_UPLOAD_CAN2;
    aucBuff[1] = GetTotalAddr();
    rt_memcpy(&aucBuff[2], (BYTE*)&rtTime, sizeof(rt_time_t));
    aucBuff[6] = usAlarmId;

    sendCan2Frm(SCU_ADDR, DEV_TYPE_SC, aucBuff);
    return True;
}

Static SHORT DealBMSFrameCAN2( T_Can_Frame * ptCan_Frame)
{
    if(ptCan_Frame == NULL)
    {
        return -1;
    }

    switch (ptCan_Frame->tDataFrame.aucData[0])
    {
        case FN_EQUAL_CURR_CAN2:
            ParseCAN2EqualizeCurrFrm(ptCan_Frame);
            break;
        case FN_SYNC_PARA_CAN2:
            ParseCAN2Para(ptCan_Frame);
            break;
        case FN_ADDR_FIND_CAN2:
            ParseCAN2AddrFindFrm(ptCan_Frame);
            break;
        case FN_HEART_BEAT_CAN2:
            ParseCAN2HeartBeatFrm(ptCan_Frame);
            break;
        case FN_ADDR_MODE_CAN2:
            ParseCAN2AddrModeFrm(ptCan_Frame);
            break;
        case FN_SET_ADDR_CAN2:
            ParseSetCAN2AddrFrm(ptCan_Frame);
            break;
        case FN_SWITCH_ADDR_CAN2:
            ParseSwitchCAN2AddrFrm(ptCan_Frame);
            break;
        case FN_RESET_CAN2_ADD_COMPETE:
            ParseRestartCan2AddrCompeteFrm(ptCan_Frame);
            break;
        default:
            break;
    }
    return 0;
}

Static BOOLEAN modifyCAN2Addr(BYTE ucAddr)
{

    s_tMyBms_CAN2.wCnt = 0;
    s_tMyBms_CAN2.ucMasterAddr = 0;

    if (s_tMyBms_CAN2.ucAddress == ucAddr)
    {
        return FALSE;
    }
    if ( (ucAddr<1) || (ucAddr>CAN2_SLAVE_NUM) )
    {
        s_tMyBms_CAN2.ucAddress = 1;
    }
    else
    {
        s_tMyBms_CAN2.ucAddress = ucAddr;
    }
    saveResetData(BMS_CAN2_ADDR, s_tMyBms_CAN2.ucAddress);
    return True;
}

/***************************************************************************
 * @brief   判断簇内都休眠或者保护则不进行can2通信
 **************************************************************************/

Static BOOLEAN IsSleepPrtDisconComm(void)
{
    BYTE ucNum = getDischgNum();
    if(IsMaster() && ucNum == 0 && GetCan2ConnectedFlag())
    {
        return True;
    }
    return False;
}


static void ParseCAN2CompeteFrm(BYTE *p)
{
    BYTE ucAddrMode;
    ucAddrMode = p[2] & 0x01;  //地址竞争帧中BYTE2->BIT0表示地址设置模式 手动or自动
    if (s_tMyBms_CAN2.ucAddress == p[1])
    {
        
        if (s_tSysPara.ucCAN2AddressMode == MANUAL_MODE)
        {
            SendCAN2CompeteFrm();
        }
        else if (s_tSysPara.ucCAN2AddressMode == AUTO_MODE && ucAddrMode == MANUAL_MODE)
        {
            if (s_ucCAN2AddrCompFLAG == 1)
            {
                modifyCAN2Addr(s_tMyBms_CAN2.ucAddress + 1);
            }
            else
            {
                modifyCAN2Addr(1);
                s_ucCAN2AddrCompFLAG = 1;
            }
        }
        else if (s_tSysPara.ucCAN2AddressMode == AUTO_MODE && ucAddrMode == AUTO_MODE)
        {
            if ( getSNPart() < *(ULONG*)(p+3) )
            {
                s_tMyBms_CAN2.ucMasterAddr = 0;
                //s_ucMastertimes = 0;
                if ( s_tMyBms_CAN2.ucAddress == s_ucCAN2RepeteAdr )      //同一地址冲突只响应1次
                {
                    return;
                }
                SendCAN2CompeteFrm();
                s_ucCAN2RepeteAdr = s_tMyBms_CAN2.ucAddress;
            }
            else
            {
                modifyCAN2Addr(s_tMyBms_CAN2.ucAddress + 1);
            }
        }
    }
    return;
}



/***************************************************************************
 * @brief    簇间均流解析
 **************************************************************************/
static BOOLEAN ParseCAN2EqualizeCurrFrm(T_Can_Frame * ptCan_Frame)
{
    BYTE ucSn, *p;

    if(ptCan_Frame == NULL)
    {
        return False;
    }
    s_u32cnt = 0;
    if (IsMasterCan2())
    {
        ucSn = ptCan_Frame->tHeader.SrcAdr - CAN2_ADDR_START;
        if(ucSn >= CAN2_SLAVE_NUM)
        {
            return False;
        }
        p = (BYTE *)&ptCan_Frame->tDataFrame.aucData[0];
        s_atCAN2SlavesData[ucSn].bExist = True;
        s_atCAN2SlavesData[ucSn].ucCommFailCnt = 0;
        s_atCAN2SlavesData[ucSn].fAveCurr = (FLOAT)((SHORT)((p[1] << 8) | p[2])) / 100.00;
        s_atCAN2SlavesData[ucSn].wAveSoc = p[3] * 256 + p[4] ;
        s_atCAN2SlavesData[ucSn].fAveBattVolt = (FLOAT)(p[5]*256 + p[6])/100.00;
    }
    else //if(s_tMyBms_CAN2.ucAddress > CAN2_ADDR_START)
    {
        s_ucCAN2RepeteAdr = 0;
        //s_tMyBms_CAN2.ucMasterAddr = CAN2_ADDR_START; //簇地址大于0x31的电池，收到簇主机发送的均流命令后，将自身设为簇从机，并停止簇地址竞争
        p = (BYTE *)&ptCan_Frame->tDataFrame.aucData[0];
        s_tEqualObjData.bValid = True;
        s_tEqualObjData.ucObjSta = p[1] >> 7 & 0x01;
        s_tEqualObjData.fObjCurr = ((FLOAT)((p[1] & 0x7F)*256 + p[2])) / 100.0;
        s_tEqualObjData.wObjSoc = p[3] * 256 + p[4] ;
        s_tEqualObjData.fVolAdj = (FLOAT)((SHORT)((p[5] << 8) | p[6])) / 100.00;
        SynCAN2ObjData(&s_tEqualObjData);
        SendCAN2EqualizeCurrRsp();
    }

    return True;
}

/***************************************************************************
 * @brief   簇从机CAN2回包
 **************************************************************************/
static BOOLEAN SendCAN2EqualizeCurrRsp(void)
{
    BYTE aucBuff[7];
    T_BattResult tBattOut;
    SHORT sCurrData = 0;
    WORD wVoltData = 0;

    if (s_tMyBms_CAN2.ucMasterAddr == 0)
    {
        return False;
    }
    if(!IsMaster())
    {
        return False;
    }
    rt_memset(&aucBuff, 0, 7);
    rt_memset(&tBattOut, 0, sizeof(T_BattResult));
    GetBattResult(&tBattOut);

    sCurrData = (SHORT)(tBattOut.fAveCurrInCluster*100);
    wVoltData = (WORD)(tBattOut.fAveBattVoltInCluster * 100);  //簇内平均电池电压
    aucBuff[0] = FN_EQUAL_CURR_CAN2;
    aucBuff[1] = (sCurrData >> 8) & 0xFF;;  //电流有正负，簇间主机判断充放电时需要考虑放电电流
    aucBuff[2] = sCurrData & 0xFF;
    aucBuff[3] = tBattOut.wAveSocInCluster / 256; //平均soc，初始化是组内的，后续是各簇的平均值
    aucBuff[4] = tBattOut.wAveSocInCluster % 256;
    aucBuff[5] = wVoltData / 256;  //电压调节值
    aucBuff[6] = wVoltData % 256;

    sendCan2Frm(s_tMyBms_CAN2.ucMasterAddr, DEV_TYPE_BMS, aucBuff);

    return True;
}


/***************************************************************************
 * @brief    SCU与柜内主机心跳解析
 **************************************************************************/

static SHORT ParseCAN2HeartBeatFrm(T_Can_Frame * ptCan_Frame)
{
    BYTE aucBuff[7] = {s_ucCAN2Rtn};

    if(ptCan_Frame == NULL)
    {
        return 0;
    }
    
    aucBuff[0] = FN_HEART_BEAT_CAN2;

    sendCan2Frm(ptCan_Frame->tHeader.SrcAdr, DEV_TYPE_SC, aucBuff);
    
    return 1;
}

static BOOLEAN ParseCAN2AddrFindFrm(T_Can_Frame * ptCan_Frame)
{
    BYTE aucBuff[7] = {s_ucCAN2Rtn};

    if(ptCan_Frame == NULL)
    {
        return False;
    }

    DealCan2AddrFind(ptCan_Frame);
    
    aucBuff[0] = FN_ADDR_FIND_CAN2;
    sendCan2Frm(ptCan_Frame->tHeader.SrcAdr, DEV_TYPE_SC, aucBuff);
    
    return True;
}

static BOOLEAN DealCan2AddrFind(T_Can_Frame * ptCan_Frame)
{  
    RETURN_VAL_IF_FAIL(ptCan_Frame->tHeader.DstAdr != s_tMyBms_CAN2.ucGroup, False);// 不支持广播

    SetBattIndicationStatus(True);
    if (s_waitCan2AddrFindTimer == RT_NULL)
    {
        s_waitCan2AddrFindTimer = rt_timer_create("Can2AddrFind", CloseBattIndication, False, 15000, RT_TIMER_FLAG_ONE_SHOT);
    }
    
    if(s_waitCan2AddrFindTimer != RT_NULL)
    {
        rt_timer_start(s_waitCan2AddrFindTimer);
    }

    SaveAction(GetActionId(CONTOL_BATT_INDICATION),"CAN2 FindAddress");

    return True;
}

/***************************************************************************
 * @brief    柜间地址获取方式解析
 **************************************************************************/
static BOOLEAN ParseCAN2AddrModeFrm(T_Can_Frame * ptCan_Frame)
{
    BYTE aucBuff[7] = {0};

    if(ptCan_Frame == NULL)
    {
        return False;
    }

    s_tSysPara.ucCAN2AddressMode = ptCan_Frame->tDataFrame.aucData[1];
    aucBuff[0] = FN_ADDR_MODE_CAN2;
    aucBuff[6] = RTN_CORRECT_CAN2;
    if ( !(SetSysPara( &s_tSysPara, True, CHANGE_BY_CAN )) )
    {
        aucBuff[6] = RTN_INVALID_DATA_CAN2; 
    }

    SaveAction(GetActionId(CONTOL_SET_CAN2_ADDR_MODE),"CAN2 AddrMode");
    sendCan2Frm(ptCan_Frame->tHeader.SrcAdr, DEV_TYPE_SC, aucBuff);

    return True;
}

/***************************************************************************
 * @brief    设置柜间地址解析
 **************************************************************************/
static BOOLEAN ParseSetCAN2AddrFrm(T_Can_Frame * ptCan_Frame)
{
    BYTE aucBuff[7] = {0};

    if(ptCan_Frame == NULL)
    {
        return False;
    }
    
    s_tSysPara.ucCAN2SwitchAddr = ptCan_Frame->tDataFrame.aucData[1];    
    aucBuff[0] = FN_SET_ADDR_CAN2;
    aucBuff[6] = RTN_CORRECT_CAN2;
    if ( !(SetSysPara( &s_tSysPara, True, CHANGE_BY_CAN )) )
    {
        aucBuff[6]  = RTN_INVALID_DATA_CAN2; 
    }
    
    SaveAction(GetActionId(CONTOL_SET_CAN2_ADDR),"CAN2 Set Address");
    sendCan2Frm(ptCan_Frame->tHeader.SrcAdr, DEV_TYPE_SC, aucBuff);

    return True;
}

/***************************************************************************
 * @brief    柜间地址切换解析
 **************************************************************************/
static BOOLEAN ParseSwitchCAN2AddrFrm(T_Can_Frame * ptCan_Frame)
{
    if(ptCan_Frame == NULL)
    {
        return False;
    }

    if((s_tSysPara.ucCAN2AddressMode == MANUAL_MODE)&&(ptCan_Frame->tHeader.DstAdr == CAN2_GROUP_ADDR))    //广播才切换
    {
        SetBMSCan2Addr(s_tSysPara.ucCAN2SwitchAddr);
        return True;
    }

    return False;
}

BOOLEAN restartCan2AddrCompete(BYTE ucAddrMode)
{
    if(ucAddrMode == AUTO_MODE)
    {
        modifyCAN2Addr(1);
        return True;
    }
    return False;
}

/***************************************************************************
 * @brief   重新柜间地址地址
 **************************************************************************/
Static BOOLEAN ParseRestartCan2AddrCompeteFrm(T_Can_Frame * ptCan_Frame)
{
    if(ptCan_Frame == NULL)
    {
        return False;
    }

    if(!restartCan2AddrCompete(s_tSysPara.ucCAN2AddressMode))
    {
         return False;
    }
    SaveAction(GetActionId(CONTOL_CAN2_ADDR_COMPLETE), " ");
    return True;
}

/****************************************************************
函数名：ChangeIntoCanFrm
入口参数：dataComm, sInLen, addr
出口参数：无
功能：转成CAN帧
****************************************************************/
void ChangeIntoCan2Frm(BYTE *dataComm, WORD wInLen, BYTE ucInScrDev, BYTE ucInSrcAdr)
{
    T_CAN_Header tHead;	

    rt_memset(&tHead, 0x00, sizeof(tHead));
    tHead.Level   = LNK_LEVEL;
    tHead.SrcDev  = DEV_TYPE_BMS;
    tHead.SrcAdr  = s_tMyBms_CAN2.ucAddress;
    tHead.DstDev  = ucInScrDev;
    tHead.DstAdr  = ucInSrcAdr;
    tHead.IDE = 1;
    tHead.RTR = 0;
    tHead.len = CAN_FRAME_MAX_LEN;
    Can2Drive_Write(dataComm, wInLen, (BYTE *)&tHead);
    return;
}

/***************************************************************************
 * @brief    CAN2 发送地址竞争帧
 **************************************************************************/
Static void sendCan2Frm(BYTE ucDstAdr, BYTE ucDstDev, BYTE* pucBuff)
{
    T_CAN_Header tHeader;

    if(IsSleepPrtDisconComm())
    {
        return;
    }
    tHeader.DstAdr = ucDstAdr;
    tHeader.DstDev = ucDstDev;
    tHeader.SrcAdr = s_tMyBms_CAN2.ucAddress;
    tHeader.SrcDev = DEV_TYPE_BMS;
    tHeader.IDE = 1;
    tHeader.RTR = 0;
    tHeader.len = 8;
    tHeader.Level = 1;

    Can2Drive_Write(pucBuff, 7, (BYTE *)&tHeader);
}

/***************************************************************************
 * @brief    CAN2 发送接口
 **************************************************************************/
static BOOLEAN Can2Drive_Write(BYTE *pIn, SHORT len, BYTE *ptHeader)
{
    BYTE *p = pIn , i = 0, j = 0;
    struct rt_can_msg canmsg = {0, };
    T_Can_Frame *ptCanfrm = (T_Can_Frame *)&canmsg;

    if (pIn == NULL || ptHeader == NULL || s_ptMutexCan2 == RT_NULL)
    {
        return False;
    }

    if(can2_dev == RT_NULL)
    {
        return False;
    }

    i = 0;
    rt_mutex_take(s_ptMutexCan2, RT_WAITING_FOREVER);
    while (len > 0)
    {   
        rt_memcpy( &(ptCanfrm->tHeader), ptHeader, sizeof(T_CAN_Header) );
        ptCanfrm->tDataFrame.SegNum = i++;
        rt_memcpy(ptCanfrm->tDataFrame.aucData, p, 7);
        len -= 7;
        p += 7;
        ptCanfrm->tDataFrame.SegFlg = (len <= 0) ? 1 : 0;
        canmsg.len = 8;
        for(j = 0; j < CAN_MAX_RESEND; j++)
        {
            if(rt_device_write(can2_dev, 0, &canmsg, sizeof(canmsg)) != 0)
            {
                break;
            }
        }
    }
    rt_mutex_release(s_ptMutexCan2);

    return True;
}

/***************************************************************************
 * @brief    判断CAN2 主机
 **************************************************************************/
// static void JudgeCan2Master(T_Can_Frame * ptCan_Frame)
// {
//     if(ptCan_Frame == NULL)
//     {
//         return;
//     }

//     if(s_tMyBms_CAN2.ucAddress==CAN2_ADDR_START && ptCan_Frame->tHeader.SrcAdr > CAN2_ADDR_START)
//     {
//         s_ucMastertimes++;
//         if(s_ucMastertimes >= 30)       //连续判断30次则认为是主机
//         {
//             s_ucCAN2RepeteAdr = 0;
//             s_ucMastertimes = 30;
//             s_tMyBms_CAN2.ucMasterAddr = CAN2_ADDR_START;
//         }
//     }
//     else
//     {
//         s_ucMastertimes = 0;
//     }
//     return;
// }

/***************************************************************************
 * @brief    柜级主机判断
 **************************************************************************/
BOOLEAN cabinetMasterIdentifying()
{
    if(s_tMyBms_CAN2.ucMasterAddr == s_tMyBms_CAN2.ucAddress && IsMaster())
    {
        return RT_TRUE;
    }
    else if(s_tMyBms_CAN2.ucMasterAddr == 0 && IsMaster())
    {
        return RT_TRUE;
    }
    return RT_FALSE;
}

/***************************************************************************
 * @brief    CAN2 收包检测
 **************************************************************************/
Static BOOLEAN CheckCAN2Address(T_Can_Frame * ptCan_Frame)
{
    GetSysPara(&s_tSysPara);
    if (ptCan_Frame->tHeader.DstDev != DEV_TYPE_BMS)
    {
        return False;
    }

    if ((ptCan_Frame->tHeader.SrcDev == DEV_TYPE_BMS &&  ptCan_Frame->tHeader.SrcAdr < CAN2_ADDR_START )|| ptCan_Frame->tHeader.SrcAdr >= (CAN2_ADDR_START+CAN2_SLAVE_NUM))   //地址检测
    {
        return False;
    }

    // if(ptCan_Frame->tDataFrame.SegFlg != 1 || ptCan_Frame->tDataFrame.SegNum != 0)         //首帧不为0x80  为长帧分包
    // {
    //     return False;
    // }



    //JudgeCan2Master(ptCan_Frame);

    if (ptCan_Frame->tHeader.DstAdr == s_tMyBms_CAN2.ucAddress)
    {
        return True;
    }

    if (ptCan_Frame->tHeader.SrcDev == DEV_TYPE_BMS && ptCan_Frame->tHeader.SrcAdr == s_tMyBms_CAN2.ucAddress)     //地址冲突
    {
        s_tMyBms_CAN2.ucMasterAddr = 0;
        s_tMyBms_CAN2.wCnt = 0;
        //s_ucMastertimes = 0;
    } 

    if (ptCan_Frame->tHeader.DstAdr != s_tMyBms_CAN2.ucGroup)
    {
        return False;
    }

    if ( (s_tMyBms_CAN2.ucMasterAddr == s_tMyBms_CAN2.ucAddress)
        && (ptCan_Frame->tHeader.SrcAdr > s_tMyBms_CAN2.ucAddress) )
    {
        return False;
    }

    if (s_tMyBms_CAN2.ucMasterAddr != 0)
    {
        s_tMyBms_CAN2.wCnt = 0;
        s_tMyBms_CAN2.ucMasterAddr = ptCan_Frame->tHeader.SrcAdr;
    }


    return True;
}

/***************************************************************************
 * @brief    apptest中can通讯测试
 * @return   
 **************************************************************************/
INT32S Can2RecCon(void)
{
    if(s_slCanDealCounter_CAN2 != 0)
    {
        s_slCanDealCounter_CAN2 = 0;
        return 1;
    }

    return 0;
}

static void CheckApptestCan2(T_Can_Frame * ptCan_Frame)
{
    ////apptest中测试can是否有接收
    if(GetApptestFlag())
    {
        s_slCanDealCounter_CAN2++;
        CheckIsCanInterconnectTestCAN1Frm(ptCan_Frame);
    }

    return;
}

static BOOLEAN Can2Drive_Write_test(BYTE *pIn, SHORT len, BYTE ucType, BYTE ucCanType)
{/////type 1:整流器新协议，0：整流器老协议
    BYTE *p = pIn;
    struct rt_can_msg canmsg = {0, };
    canmsg.ide = 1;
    canmsg.rtr = 0;
    canmsg.len = 8;

    if (pIn == NULL)
    {
        return False;
    }
    if(ucType > 1)
    {
        return False;
    }
    while (len > (SHORT)0)
    {
        if(ucType)
        {
            canmsg.id = 0x1FA04080;
            canmsg.data[0] = 0;
            rt_memcpy(&canmsg.data[1], p, 7);
            len -= 7;
            p += 7;
            canmsg.len = 8;
            if(CAN2_DEV == ucCanType)
            {
                rt_mutex_take(s_ptMutexCan2, RT_WAITING_FOREVER);
                rt_device_write(can2_dev, 0, &canmsg, sizeof(canmsg));
                rt_mutex_release(s_ptMutexCan2);
            }
        }
        else
        {
            canmsg.id = 0x1A780110;
            rt_memcpy(canmsg.data, p, 8);
            p += 8;
            len -= 8;
            canmsg.len = 8;
            if(CAN_DEV == ucCanType)
            {
                rt_mutex_take(s_ptMutexCan2, RT_WAITING_FOREVER);
                rt_device_write(can2_dev, 0, &canmsg, sizeof(canmsg));
                rt_mutex_release(s_ptMutexCan2);
            }
        }
    }
    return True;
}

void GetSmrRealDataCan2(BYTE ucDevType)
{
    BYTE aucBuff[14] = {0xE0, 0x7D, 0x01, 0x28, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x16, 0x64};
    BYTE aucBuff1[14] = {0xE0, 0x7D, 0x01, 0x20, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x3F, 0x98};
    BYTE aucBuff2[8] = {0x78, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};

    Can2Drive_Write_test(aucBuff,14,1, ucDevType);
    Can2Drive_Write_test(aucBuff1,14,1, ucDevType);
    Can2Drive_Write_test(aucBuff2,8,0, ucDevType);
    return;
}

UINT32 GetCan2InterconnetTestCounter(void)  // 获取CAN2对接测试计数
{
    return s_can_2_InterconnetTestCounter;
}

void ClearCan2InterconnetTestCounter(void)
{
    s_can_2_InterconnetTestCounter = 0;
}

static void ChangeIntoCan2InterconnectFrm(BYTE *dataComm, WORD wInLen, BYTE ucInDstDev, BYTE ucInDstAdr, BYTE canDevWrite)
{
    if (NULL == dataComm)
    {
        return;
    }
    T_CAN_Header tHead = {0};
    tHead.Level   = LNK_LEVEL;
    tHead.SrcDev  = DEV_TYPE_BMS;
    tHead.SrcAdr  = 0;                // 不写本机地址，防止触发CAN2的地址竞争
    tHead.DstDev  = ucInDstDev;
    tHead.DstAdr  = ucInDstAdr;
    tHead.IDE = 1;
    tHead.RTR = 0;
    tHead.len = CAN_FRAME_MAX_LEN;
    if (CAN2_DEV == canDevWrite)
    {
        Can2Drive_Write(dataComm, wInLen, (BYTE *)&tHead);
    }
}

void SendCan2InterconnectTestFrm(void)
{
    // CAN2向CAN1发送对插测试帧
    InitCanProto();
    ChangeIntoCan2InterconnectFrm(s_can_2_InterconnectTestData, sizeof(s_can_2_InterconnectTestData), DEV_TYPE_BMS, 0, CAN2_DEV);
}

static BOOLEAN CheckIsCan2InterconnectTest(T_Can_Frame* ptCan_Frame, BYTE *dataCmp, UINT32 dataLen)
{
    if (NULL == ptCan_Frame || NULL == dataCmp)
    {
        return False;
    }

    if (0 == rt_memcmp(dataCmp, ptCan_Frame->tDataFrame.aucData, dataLen))
    {
        return True;
    }

    return False;
}

void CheckIsCan2InterconnectTestFrm(T_Can_Frame * ptCan_Frame)
{
    if(CheckIsCan2InterconnectTest(ptCan_Frame, s_can_2_InterconnectTestData, sizeof(s_can_2_InterconnectTestData)))
    {
        s_can_2_InterconnetTestCounter++;
    }
    return;
}

BYTE GetCAN2Addr(void)
{
    return s_tMyBms_CAN2.ucAddress;
}

BYTE GetCan2ScAdr() {
    return s_ucSCAdr;
}

/***************************************************************************
 * @brief   发送同步参数命令
 **************************************************************************/

Static BOOLEAN SynCAN2Para(void)
{
    BYTE aucBuff[7] = {0};
    T_SysPara tSysPara = {0};

    if(!IsMaster())
    {
        return False;
    }

    GetSysPara(&tSysPara);

    aucBuff[0] = FN_SYNC_PARA_CAN2;
    aucBuff[1] = tSysPara.ucCurrBalanceMethod;

    sendCan2Frm(s_tMyBms_CAN2.ucGroup, DEV_TYPE_BMS, aucBuff);

    return True;
}


/***************************************************************************
 * @brief   同步参数命令解析
 **************************************************************************/

static BOOLEAN ParseCAN2Para(T_Can_Frame *ptCan_Frame)
{
    BYTE ucBalanceMethod;
    BYTE *p;
    T_SysPara tSysPara = {0};

    if(ptCan_Frame == NULL)
    {
        return False;
    }

    if (IsMasterCan2())
    {
        return False;    
    }

    if(!IsMaster())
    {
        return False;
    }

    GetSysPara(&tSysPara);

    p = (BYTE *)&ptCan_Frame->tDataFrame.aucData[0];
    ucBalanceMethod = p[1];

    if (ucBalanceMethod != tSysPara.ucCurrBalanceMethod)
    {
        tSysPara.ucCurrBalanceMethod = ucBalanceMethod;
        SetSysPara(&tSysPara, True, CHANGE_BY_CAN);
    }

    return True;
}


