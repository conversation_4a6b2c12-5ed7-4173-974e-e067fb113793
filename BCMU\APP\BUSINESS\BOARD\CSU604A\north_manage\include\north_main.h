/**
 * @brief 604a main板北向通信头文件
 */

#ifndef _604A_MAIN_NORTH_COMM_H_
#define _604A_MAIN_NORTH_COMM_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "cmd.h"
#include "linklayer.h"
#include "protocol_layer.h"
#include "pb_encode.h"
#include "pb.h"

typedef struct {
    void*        data;
    cmd_buf_t*   cmd_buff;
    link_inst_t* link_inst;
    rt_mq_t      north_mq;
}north_mgr_t;

void*  init_north(void * param);
void north_comm_th(void *param);
char init_dev_init_tab(void);
#ifdef __cplusplus
}
#endif      //< __cplusplus

#endif      //< _604A_MAIN_NORTH_COMM_H_
