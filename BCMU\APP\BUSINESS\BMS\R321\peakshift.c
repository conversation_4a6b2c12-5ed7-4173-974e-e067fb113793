/**************************************************************************
* Copyright (C) 2010-2020, ZTE Corporation.
* 版权信息：（C）2011-2020，中兴通讯股份有限公司动力开发部版权所有
* 系统名称：ZXDUPA-PMSA V2.1平台软件
* 文件名称：peakshift.c
* 文件说明：错峰模块源文件
* 作    者：chenkai
* 版本信息：V1.0
* 设计日期：2024-3-1
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/
#include "common.h"
#include "peakshift.h"
#include "hisdata.h"
#include "fileSys.h"
#include <rtdevice.h>
#include <rtthread.h>
#include <dfs.h>
#include <battery.h>
#if !defined(KW_CHECK) && !defined(UNITEST)
#include "lwip/netifapi.h"
#include "netdev.h"
#include <netif/ethernetif.h>
#endif
#include "apptest.h"
#include "utils_rtthread_security_func.h"
#include "CommCan.h"
#include "protocol.h"
#include "protocol1363.h"

/***********************  变量定义  ************************/
Static T_PeakShiftPara s_tPeakShiftPara, s_tSavePeakShiftPara;
Static T_TemplateInfo s_tTemplateInfo, s_tSaveTemplateInfo;
Static WORD s_wValleyMin = 0;

/**********************模板转发相关变量-start****************/
Static BOOLEAN TemplForwardProcFlag = False;
Static BYTE s_ucCommandNum = 0; 
/**********************模板转发相关变量-end******************/

/*****************  静态函数原型定义  **********************/
Static BOOLEAN ReadPeakShiftPara(T_PeakShiftPara* ptPeakShiftPara);
Static BOOLEAN ReadPeakShiftTemplate(T_TemplateInfo* ptPeakShiftTemp);
Static BOOLEAN CheckDateTempValid(T_DateTemplateStruct* ptDateTemp);
Static BOOLEAN CheckPeakShiftPara(T_PeakShiftPara* ptPeakShiftPara, BYTE ucParaID);
Static BOOLEAN CheckHolidayPatternDates(T_PeakShiftPara* ptPeakShiftPara);
Static BYTE SearchPriceClassNow(T_DateTemplateStruct* ptDateTemp, T_TimeStruct* ptTime);
Static int8_t DateWeekCovert(WORD wYear, BYTE ucMonth, BYTE ucDay);
Static BOOLEAN SavePeakShiftParaAction(BYTE ucPeakShiftId, T_PeakShiftPara* ptPeakShiftNew, T_PeakShiftPara* ptPeakShiftOld);
Static BOOLEAN CheckDurationNum(T_DateTemplateStruct* ptDateTemp);
Static BOOLEAN CheckDurationValidity(T_DateTemplateStruct* ptDateTemp);
Static BOOLEAN CheckOverlap(T_DateTemplateStruct* ptDateTemp);
Static BOOLEAN CheckDurationInterval(T_DateTemplateStruct* ptDateTemp);
Static BOOLEAN CheckPeakShiftPara2(T_PeakShiftPara* ptPeakShiftPara, BYTE ucParaID);
Static INT8S GetPatternElectricityClass(void);
Static BOOLEAN InitDayTemplate(void);
Static BOOLEAN SetPeakShiftDefaultPara(void);
/*********************  数据结构定义  **********************/

/**********************模板转发相关函数原型定义-start****************/
static void PackagingPeakShitPara(T_CommStruct *ptComm,WORD wLength,BYTE *ucBuff,BOOLEAN Flag);
static BOOLEAN SendTemplateData(T_CommStruct *ptComm);
/**********************模板转发相关函数原型定义-end******************/

BOOLEAN InitPeakShiftPara( void ) 
{
    if (ReadPeakShiftPara(&s_tPeakShiftPara)) //如果从文件中读取失败或获取读取校验值不对则赋初值并保存
    {
        SetPeakShiftDefaultPara();
        // rt_kprintf("sizeof(T_TemplateInfo) = %d", sizeof(T_TemplateInfo));
    }

    if (ReadPeakShiftTemplate(&s_tTemplateInfo))
    {
        InitDayTemplate();
        // s_tTemplateInfo.wCRC = CRC_Cal((BYTE*)&s_tTemplateInfo, sizeof(T_TemplateInfo) - 2);
        // writeFile(FILE_NAME_PEAKTEMPLATE, (BYTE *)&s_tTemplateInfo, sizeof(T_TemplateInfo));
        // rt_kprintf("sizeof(T_TemplateInfo) = %d", sizeof(T_TemplateInfo));
    }

    return SUCCESSFUL;
}



BOOLEAN RestorePeakShiftPara( void )
{
    rt_memset_s((BYTE*)&s_tPeakShiftPara, sizeof(T_PeakShiftPara), 0, sizeof(T_PeakShiftPara));
    
    SetPeakShiftDefaultPara();
    InitDayTemplate(); 

    return SUCCESSFUL;
}



Static BOOLEAN SetPeakShiftDefaultPara(void)
{
    s_tPeakShiftPara.ucPeakShiftDOD = DEFAULT_PEAKSHIFT_DOD;  //默认值
    s_tPeakShiftPara.ucPoweroffPeakDelay = DEFAULT_POWER_OFF_SHIFT_DELAY; //停电错峰延时默认值：24小时

    //初始化错峰模式和周模式模板序号
    s_tPeakShiftPara.ucElectricPricePattern = 1; //错峰模式默认为周模式
    s_tPeakShiftPara.aucWeekPatternTempNum[0] = 1; // 周模式-周一模板
    s_tPeakShiftPara.aucWeekPatternTempNum[1] = 1; // 周模式-周二模板
    s_tPeakShiftPara.aucWeekPatternTempNum[2] = 1; // 周模式-周三模板
    s_tPeakShiftPara.aucWeekPatternTempNum[3] = 1; // 周模式-周四模板
    s_tPeakShiftPara.aucWeekPatternTempNum[4] = 1; // 周模式-周五模板
    s_tPeakShiftPara.aucWeekPatternTempNum[5] = 2; // 周模式-周六模板
    s_tPeakShiftPara.aucWeekPatternTempNum[6] = 2; // 周模式-周日模板
    s_tPeakShiftPara.wCRC = CRC_Cal((BYTE*)&s_tPeakShiftPara, offsetof(T_PeakShiftPara, wCRC));
    writeFile(FILE_NAME_PEAKSHIFTPARA, (BYTE *)&s_tPeakShiftPara, sizeof(T_PeakShiftPara));
    return SUCCESSFUL;
}



//日模板1-初始值
const T_DurationStruct t_DayTemplateWorkDay[4] = 
{
    {0, 0, 8, 59, ELECTRICITY_PRICE_VALLEY}, {9, 0, 17, 59, ELECTRICITY_PRICE_LEVEL}, {18, 0, 21, 59, ELECTRICITY_PRICE_PEAK}, {22, 0, 23, 59, ELECTRICITY_PRICE_VALLEY}
};
//日模板2-初始值
const T_DurationStruct t_DayTemplateWeeKend = {0, 0, 23, 59, ELECTRICITY_PRICE_VALLEY};
//初始化日模板
Static BOOLEAN InitDayTemplate(void)
{
    T_TemplateInfo tTemplateInfo = {0};

    tTemplateInfo.ucDayTemplateNum = 2; //日模板数量
    //模板1-初始化
    tTemplateInfo.uTemplateInfo.tMonthTemp.atDateTemp[0].ucDurationNum = 4; //日模板区间数
    for(BYTE i = 0; i < tTemplateInfo.uTemplateInfo.tMonthTemp.atDateTemp[0].ucDurationNum; i++)
    {
        tTemplateInfo.uTemplateInfo.tMonthTemp.atDateTemp[0].atDura[i].FromHour = t_DayTemplateWorkDay[i].FromHour;
        tTemplateInfo.uTemplateInfo.tMonthTemp.atDateTemp[0].atDura[i].FromMinute = t_DayTemplateWorkDay[i].FromMinute;
        tTemplateInfo.uTemplateInfo.tMonthTemp.atDateTemp[0].atDura[i].ToHour = t_DayTemplateWorkDay[i].ToHour;
        tTemplateInfo.uTemplateInfo.tMonthTemp.atDateTemp[0].atDura[i].ToMinute = t_DayTemplateWorkDay[i].ToMinute;
        tTemplateInfo.uTemplateInfo.tMonthTemp.atDateTemp[0].atDura[i].PriceClass = t_DayTemplateWorkDay[i].PriceClass;
    }
    //模板2-初始化
    tTemplateInfo.uTemplateInfo.tMonthTemp.atDateTemp[1].ucDurationNum = 1; //日模板区间数
    tTemplateInfo.uTemplateInfo.tMonthTemp.atDateTemp[1].atDura[0].FromHour = t_DayTemplateWeeKend.FromHour;
    tTemplateInfo.uTemplateInfo.tMonthTemp.atDateTemp[1].atDura[0].FromMinute = t_DayTemplateWeeKend.FromMinute;
    tTemplateInfo.uTemplateInfo.tMonthTemp.atDateTemp[1].atDura[0].ToHour = t_DayTemplateWeeKend.ToHour;
    tTemplateInfo.uTemplateInfo.tMonthTemp.atDateTemp[1].atDura[0].ToMinute = t_DayTemplateWeeKend.ToMinute;
    tTemplateInfo.uTemplateInfo.tMonthTemp.atDateTemp[1].atDura[0].PriceClass = t_DayTemplateWeeKend.PriceClass;

    for(BYTE i = 0; i < tTemplateInfo.ucDayTemplateNum; i++)
    {
        if(!CheckDateTempValid(&tTemplateInfo.uTemplateInfo.tMonthTemp.atDateTemp[i]))
        {
            return False;
        }
    }
    
    tTemplateInfo.wCRC = CRC_Cal((BYTE*)&tTemplateInfo, offsetof(T_PeakShiftPara, wCRC));
    writeFile(FILE_NAME_PEAKTEMPLATE, (BYTE *)&tTemplateInfo, sizeof(T_TemplateInfo));
    rt_memcpy_s((BYTE*)&s_tTemplateInfo, sizeof(T_TemplateInfo), (BYTE*)&tTemplateInfo, sizeof(T_TemplateInfo));
    return SUCCESSFUL;
}


BOOLEAN GetPeakShiftPara(T_PeakShiftPara* ptPeakShiftPara)
{
    rt_memcpy_s((BYTE*)ptPeakShiftPara, sizeof(T_PeakShiftPara), (BYTE*)&s_tPeakShiftPara, sizeof(T_PeakShiftPara));
    return SUCCESSFUL;
}

BOOLEAN IsPeakShiftEnabled(void)
{
    return s_tPeakShiftPara.bPeakShift;
}

BOOLEAN GetPeakShiftTemplate(T_TemplateInfo* ptPeakShiftTemp)
{
    rt_memcpy_s((BYTE*)ptPeakShiftTemp, sizeof(T_TemplateInfo), (BYTE*)&s_tTemplateInfo, sizeof(T_TemplateInfo));
    return SUCCESSFUL;
}

Static BOOLEAN ReadPeakShiftPara(T_PeakShiftPara* ptPeakShiftPara)
{
    LONG lParaReadRtn;

    lParaReadRtn = readFile(FILE_NAME_PEAKSHIFTPARA, (BYTE *)&s_tSavePeakShiftPara, sizeof(T_PeakShiftPara));
    if ((CRC_Cal((BYTE *)&s_tSavePeakShiftPara, offsetof(T_PeakShiftPara, wCRC) + 2) == 0)
#ifndef KW_CHECK
        && (lParaReadRtn > 0)
#endif
    )
    {
        rt_memcpy_s((BYTE*)ptPeakShiftPara, sizeof(T_PeakShiftPara), (BYTE*)&s_tSavePeakShiftPara, sizeof(T_PeakShiftPara));
    }
    if(CRC_Cal((BYTE*)ptPeakShiftPara, offsetof(T_PeakShiftPara, wCRC)) != ptPeakShiftPara->wCRC || 
        CRC_Cal((BYTE*)ptPeakShiftPara, offsetof(T_PeakShiftPara, wCRC)) == 0)
    {
        rt_memset_s(ptPeakShiftPara, sizeof(T_PeakShiftPara), 0, sizeof(T_PeakShiftPara));
        return FAILURE;
    }
    return SUCCESSFUL;
}

Static BOOLEAN ReadPeakShiftTemplate(T_TemplateInfo* ptPeakShiftTemp)
{
    LONG lParaReadRtn;

    lParaReadRtn = readFile(FILE_NAME_PEAKTEMPLATE, (BYTE *)&s_tSaveTemplateInfo, sizeof(T_TemplateInfo));
    if ((CRC_Cal((BYTE *)&s_tSaveTemplateInfo, offsetof(T_TemplateInfo, wCRC) + 2) == 0)
#ifndef KW_CHECK
        && (lParaReadRtn > 0)
#endif
    )
    {
        rt_memcpy_s((BYTE*)ptPeakShiftTemp, sizeof(T_TemplateInfo), (BYTE*)&s_tSaveTemplateInfo, sizeof(T_TemplateInfo));
    }

    if(CRC_Cal((BYTE*)ptPeakShiftTemp, sizeof(T_TemplateInfo)-2) != ptPeakShiftTemp->wCRC ||
        CRC_Cal((BYTE*)ptPeakShiftTemp, sizeof(T_TemplateInfo)-2) == 0)
    {
        rt_memset_s(ptPeakShiftTemp, sizeof(T_TemplateInfo), 0, sizeof(T_TemplateInfo));
        return FAILURE;
    }
    return SUCCESSFUL;
}

BOOLEAN SetPeakShiftPara(T_PeakShiftPara* ptPeakShiftPara, BYTE ucParaID)
{
    BYTE ucActionID = 0;

    if (ptPeakShiftPara == NULL)
    {
       return False;
    }
    if(CheckPeakShiftPara(ptPeakShiftPara, ucParaID))
    {
        if (ucParaID < 0x80 || ucParaID > 0x89)
        {
            return False;
        }
        ucActionID = ucParaID - 0x80 + PARA_PEAK_SHIFT_ENABLE;
        SavePeakShiftParaAction(ucActionID, ptPeakShiftPara, &s_tPeakShiftPara);
        ptPeakShiftPara->wCRC = CRC_Cal((BYTE*)ptPeakShiftPara, sizeof(T_PeakShiftPara)-2);
        writeFile(FILE_NAME_PEAKSHIFTPARA, (BYTE *)ptPeakShiftPara, sizeof(T_PeakShiftPara));
        rt_memcpy_s((BYTE*)&s_tPeakShiftPara, sizeof(T_PeakShiftPara), (BYTE*)ptPeakShiftPara, sizeof(T_PeakShiftPara));
        return True;
    }
    return False;
}

BOOLEAN SetPeakShiftTemplate(T_TemplateInfo* ptPeakShiftTemp)
{
    BYTE i;
    if (ptPeakShiftTemp == NULL)
    {
       return False;
    }

    for(i = 0; i < ptPeakShiftTemp->ucDayTemplateNum; i++)
    {
        if(!CheckDateTempValid(&ptPeakShiftTemp->uTemplateInfo.tMonthTemp.atDateTemp[i]))
        {
            return False;
        }
    }
    
    SaveAction(PARA_PEAK_SHIFT_TEMPLATE, "Peakshift Template");
    ptPeakShiftTemp->wCRC = CRC_Cal((BYTE*)ptPeakShiftTemp, sizeof(T_TemplateInfo)-2);
    writeFile(FILE_NAME_PEAKTEMPLATE, (BYTE *)ptPeakShiftTemp, sizeof(T_TemplateInfo));
    rt_memcpy_s((BYTE*)&s_tTemplateInfo, sizeof(T_TemplateInfo), (BYTE*)ptPeakShiftTemp, sizeof(T_TemplateInfo));
    return True;
}

Static BOOLEAN CheckPeakShiftPara(T_PeakShiftPara* ptPeakShiftPara, BYTE ucParaID)
{
    switch (ucParaID)
    {
        case 0x80:
            return (ptPeakShiftPara->bPeakShift == False || ptPeakShiftPara->bPeakShift == True);
        case 0x81:
            return (ptPeakShiftPara->ucPeakShiftDOD >= 10 && ptPeakShiftPara->ucPeakShiftDOD <= 90);
        case 0x82:
            return (ptPeakShiftPara->ucElectricPricePattern <= 2);
        case 0x83:
            return (ptPeakShiftPara->ucDayPatterTempNum >= 1 && ptPeakShiftPara->ucDayPatterTempNum <= 8);
        default:
            return CheckPeakShiftPara2(ptPeakShiftPara, ucParaID);
    }
    return True;
}

Static BOOLEAN CheckPeakShiftPara2(T_PeakShiftPara* ptPeakShiftPara, BYTE ucParaID)
{
    BYTE i;
    switch (ucParaID)
    {
        case 0x84:
            for(i = 0; i < 7; i++)
            {
                if(ptPeakShiftPara->aucWeekPatternTempNum[i] < 1 || ptPeakShiftPara->aucWeekPatternTempNum[i] > 8)
                {
                    return False;
                }
            }
            break;
        case 0x85:
            for(i = 0; i < 31; i++)
            {
                if(ptPeakShiftPara->aucMonthPatternTempNum[i] < 1 || ptPeakShiftPara->aucMonthPatternTempNum[i] > 8)
                {
                    return False;
                }
            }
            break;
        case 0x86:
            return (ptPeakShiftPara->ucHolidayPatternTempNum >= 1 && ptPeakShiftPara->ucHolidayPatternTempNum <= 8);
        case 0x87:
            return CheckHolidayPatternDates(ptPeakShiftPara);
        case 0x88:
            return (ptPeakShiftPara->ucPoweroffPeakDelay <= 24);
        default:
            return False;
    }
    return True;
}

Static BOOLEAN CheckHolidayPatternDates(T_PeakShiftPara* ptPeakShiftPara)
{
    BYTE i, j;
    T_TimeStruct tTime;

    GetTime(&tTime);
    for(i = 0; i < 30; i++)
    {
        tTime.ucMonth = ptPeakShiftPara->atHolidayPatternDate[i].ucMonth;
        tTime.ucDay = ptPeakShiftPara->atHolidayPatternDate[i].ucDay;
        if(tTime.ucMonth != 0 || tTime.ucDay != 0)
        {
            if(!CheckTimeValid(&tTime))
            {
                return False;
            }
            for(j = i + 1; j < 30; j++)
            {
                if(tTime.ucMonth == ptPeakShiftPara->atHolidayPatternDate[j].ucMonth && tTime.ucDay == ptPeakShiftPara->atHolidayPatternDate[j].ucDay)
                {
                    return False;
                }
            }
        }
    }

    return True;
}

Static BOOLEAN CheckDateTempValid(T_DateTemplateStruct* ptDateTemp)
{
    if (!CheckDurationNum(ptDateTemp)) {
        return False;
    }

    if (!CheckDurationValidity(ptDateTemp)) {
        return False;
    }

    if (!CheckOverlap(ptDateTemp)) {
        return False;
    }

    if (!CheckDurationInterval(ptDateTemp)) {
        return False;
    }

    return True;
}

Static BOOLEAN CheckDurationNum(T_DateTemplateStruct* ptDateTemp)
{
    BYTE i;
    BYTE j = 0;
    T_DurationStruct tDurationTemp;
    rt_memset_s(&tDurationTemp, sizeof(T_DurationStruct), 0, sizeof(T_DurationStruct));

    for(i = 0; i < 48; i++) {
        if(0 != rt_memcmp(&ptDateTemp->atDura[i], &tDurationTemp, sizeof(T_DurationStruct))) {
            j++;
        }
    }

    return j == ptDateTemp->ucDurationNum;
}

Static BOOLEAN CheckDurationValidity(T_DateTemplateStruct* ptDateTemp)
{
    BYTE i;
    for(i = 0; i < ptDateTemp->ucDurationNum; i++) {
        if(ptDateTemp->atDura[i].PriceClass > 2 
            || ptDateTemp->atDura[i].FromHour > ptDateTemp->atDura[i].ToHour 
            || (ptDateTemp->atDura[i].FromHour == ptDateTemp->atDura[i].ToHour && ptDateTemp->atDura[i].FromMinute >= ptDateTemp->atDura[i].ToMinute)) {
            return False;
        }
    }

    return True;
}

Static BOOLEAN CheckOverlap(T_DateTemplateStruct* ptDateTemp)
{
    BYTE i, j;
    for(i = 0; i < ptDateTemp->ucDurationNum; i++) {
        for(j = i + 1; j < ptDateTemp->ucDurationNum; j++) {
            if (!(ptDateTemp->atDura[i].ToHour < ptDateTemp->atDura[j].FromHour 
                || (ptDateTemp->atDura[i].ToHour == ptDateTemp->atDura[j].FromHour && ptDateTemp->atDura[i].ToMinute < ptDateTemp->atDura[j].FromMinute)
                || (ptDateTemp->atDura[j].ToHour < ptDateTemp->atDura[i].FromHour 
                || (ptDateTemp->atDura[j].ToHour == ptDateTemp->atDura[i].FromHour && ptDateTemp->atDura[j].ToMinute < ptDateTemp->atDura[i].FromMinute)))) {
                return False;
            }
        }
    }

    return True;
}

Static BOOLEAN CheckDurationInterval(T_DateTemplateStruct* ptDateTemp)
{
    BYTE i;
    for(i = 0; i < ptDateTemp->ucDurationNum; i++) {
        if(ptDateTemp->atDura[i].ToHour * 60 + ptDateTemp->atDura[i].ToMinute - ptDateTemp->atDura[i].FromHour * 60 - ptDateTemp->atDura[i].FromMinute >= 1440
          || ptDateTemp->atDura[i].ToHour * 60 + ptDateTemp->atDura[i].ToMinute - ptDateTemp->atDura[i].FromHour * 60 - ptDateTemp->atDura[i].FromMinute < 9) {
            return False;
        }
    }

    return True;
}

Static BYTE SearchPriceClassNow(T_DateTemplateStruct* ptDateTemp, T_TimeStruct* ptTime)
{
    BYTE i;
    BYTE ucTempPrice = ELECTRICITY_PRICE_NULL;
    WORD wValleyMin = 0;
    s_wValleyMin = 0;

	/* 峰谷充放电统计延迟1分钟，统计有误差，改为小于 */
    for(i = 0; i < ptDateTemp->ucDurationNum; i++)
    {
        if((ptTime->ucHour > ptDateTemp->atDura[i].FromHour || (ptTime->ucHour == ptDateTemp->atDura[i].FromHour && ptTime->ucMinute >= ptDateTemp->atDura[i].FromMinute)) 
        && (ptTime->ucHour < ptDateTemp->atDura[i].ToHour || (ptTime->ucHour == ptDateTemp->atDura[i].ToHour && ptTime->ucMinute < ptDateTemp->atDura[i].ToMinute)))
        {
            ucTempPrice = ptDateTemp->atDura[i].PriceClass;
            if(ucTempPrice == ELECTRICITY_PRICE_VALLEY)
            {
                if ((ptDateTemp->atDura[i].ToHour + ptDateTemp->atDura[i].ToMinute) >= (ptTime->ucHour + ptTime->ucMinute))
                {
                    if (ptDateTemp->atDura[i].ToHour * 60 +  ptDateTemp->atDura[i].ToMinute - ptTime->ucHour * 60 - ptTime->ucMinute <= WORDTYPE_MAX_VALUE &&
                        ptDateTemp->atDura[i].ToHour * 60 +  ptDateTemp->atDura[i].ToMinute - ptTime->ucHour * 60 - ptTime->ucMinute > 0)
                    {
                        wValleyMin = ptDateTemp->atDura[i].ToHour * 60 +  ptDateTemp->atDura[i].ToMinute - ptTime->ucHour * 60 - ptTime->ucMinute;
                    }
                    if(wValleyMin > 0)
                    {
                        s_wValleyMin = wValleyMin; // 谷阶段剩余时间
                    }
                }
            }
            return ucTempPrice;
        }
    }
    return ucTempPrice;
}

WORD GetValleyTime(void)
{
    return s_wValleyMin;
}

Static int8_t DateWeekCovert(WORD wYear, BYTE ucMonth, BYTE ucDay)
{
    if(ucMonth < 1 || ucMonth > 12 || ucDay < 1 || ucDay > 31 || wYear < 1) 
    {
        return FAILED;  // 返回异常值表示输入参数不合法
    }
    if(1 == ucMonth || 2 == ucMonth)
    {
        ucMonth += 12;
        wYear--;
    }
    return ((ucDay + 2 * ucMonth + 3 * (ucMonth + 1) / 5 + wYear + wYear / 4 - wYear / 100 + wYear / 400) % 7); //返回0-6代表周一到周日
}

INT8S GetElectricityClass(void)
{
    T_TimeStruct tTime;
    BYTE i;
    BYTE ucPriceClass = ELECTRICITY_PRICE_NULL;
    GetTime( &tTime ); 
    for(i = 0; i < 30; i++) //节假日
    {
        if(tTime.ucMonth == s_tPeakShiftPara.atHolidayPatternDate[i].ucMonth && tTime.ucDay == s_tPeakShiftPara.atHolidayPatternDate[i].ucDay)
        {
            if(s_tPeakShiftPara.ucHolidayPatternTempNum >= 1 && s_tPeakShiftPara.ucHolidayPatternTempNum <= 8)
            {
                ucPriceClass = SearchPriceClassNow(&s_tTemplateInfo.uTemplateInfo.tMonthTemp.atDateTemp[s_tPeakShiftPara.ucHolidayPatternTempNum - 1], &tTime);
                return ucPriceClass;
            }
        }
    }
    return GetPatternElectricityClass();
}

Static INT8S GetPatternElectricityClass(void)
{
    T_TimeStruct tTime;
    BYTE ucPriceClass = ELECTRICITY_PRICE_NULL;
    BYTE ucWeekDay;
    BYTE ucWeekPatternNum = 0;
    GetTime( &tTime ); 
    if(0 == s_tPeakShiftPara.ucElectricPricePattern) //日模式
    {
        if(s_tPeakShiftPara.ucDayPatterTempNum >= 1 && s_tPeakShiftPara.ucDayPatterTempNum <= 8)
        {
            ucPriceClass = SearchPriceClassNow(&s_tTemplateInfo.uTemplateInfo.tMonthTemp.atDateTemp[s_tPeakShiftPara.ucDayPatterTempNum - 1], &tTime);
        }
    }
    else if(1 == s_tPeakShiftPara.ucElectricPricePattern) //周模式
    {
        ucWeekDay = DateWeekCovert(tTime.wYear, tTime.ucMonth, tTime.ucDay);
        if (ucWeekDay > 6)
        {
            return FAILED;
        }
        ucWeekPatternNum = s_tPeakShiftPara.aucWeekPatternTempNum[ucWeekDay];
        if(ucWeekPatternNum >= 1 && ucWeekPatternNum <= 8)
        {
            ucPriceClass = SearchPriceClassNow(&s_tTemplateInfo.uTemplateInfo.tWeekTemp.atDateTemp[ucWeekPatternNum - 1], &tTime);
        }
    }
    else  //月模式
    {      
        if(s_tPeakShiftPara.aucMonthPatternTempNum[tTime.ucDay - 1] >= 1 && s_tPeakShiftPara.aucMonthPatternTempNum[tTime.ucDay - 1] <= 8)
        {
            ucPriceClass = SearchPriceClassNow(&s_tTemplateInfo.uTemplateInfo.tMonthTemp.atDateTemp[s_tPeakShiftPara.aucMonthPatternTempNum[tTime.ucDay - 1] - 1], &tTime);
        }
    }
    return ucPriceClass;
}
 
Static BOOLEAN SavePeakShiftParaAction(BYTE ucPeakShiftId, T_PeakShiftPara* ptPeakShiftNew, T_PeakShiftPara* ptPeakShiftOld)
{
    CHAR buff[21] = {0};
    BYTE i, j;
    BYTE *ptNew =(BYTE*)ptPeakShiftNew; 
    BYTE *ptOld = (BYTE*)ptPeakShiftOld;
    ULONG  ulOffset = 0;
    BYTE aucParaSpace[PEAK_SHIFT_PARA_NUM] = {1, 1, 1, 1, 7, 31, 1, 60, 1};

    if (NULL == ptPeakShiftNew || NULL == ptPeakShiftOld)
    {
        return FALSE;
    }

    for ( i = 0; i < PEAK_SHIFT_PARA_NUM; i++)
    {       
        if(rt_memcmp( (BYTE * )(ptNew + ulOffset), (BYTE * )( ptOld + ulOffset), aucParaSpace[i]))
        {
            rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));
            if (1==aucParaSpace[i])
            {
                rt_snprintf_s(buff, sizeof(buff), "%d", *(ptNew + ulOffset));
                SaveAction(ucPeakShiftId, buff);

            }
            else if(7 == aucParaSpace[i] || 31 == aucParaSpace[i])
            {
                for(j = 0; j < aucParaSpace[i]; j++)
                {
                    if(ptNew[ulOffset+j] != ptOld[ulOffset+j])
                    {
                        rt_snprintf_s(buff, sizeof(buff), "-%d# %d", j + 1, ptNew[ulOffset+j]);
                        SaveAction(ucPeakShiftId, buff);
                        rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));
                    }
                }
            }
            else if (ONE_HOUR_MINUTES == aucParaSpace[i])
            {
                for(j = 0; j < ONE_HOUR_MINUTES; j += 2)
                {
                    if(ptNew[ulOffset+j] != ptOld[ulOffset+j] || ptNew[ulOffset+j+1] != ptOld[ulOffset+j+1])
                    {
                        rt_snprintf_s(buff, sizeof(buff), "-%d# %d/%d", j / 2 + 1, ptNew[ulOffset+j], ptNew[ulOffset+j+1]);
                        SaveAction(ucPeakShiftId, buff);
                        rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));
                    }
                }
            }
        }
        ulOffset += aucParaSpace[i];
    }
    return TRUE;
}


BOOLEAN SetPeakShiftParaFromNet(T_PeakShiftPara* ptPeakShiftPara, T_TemplateInfo* ptPeakShiftTemplate)
{
    BYTE i = 0;
    BYTE ucConut = 0;
    //BYTE ucActionID = 0;
    BYTE ucParaIDBfuff[] = {0x00, 0x86, 0x87};

    // 比较模板和参数是否发生变化，未发生变化不做处理
    if (!rt_memcmp(ptPeakShiftPara, &s_tPeakShiftPara, sizeof(T_PeakShiftPara)) &&
        !rt_memcmp(ptPeakShiftTemplate, &s_tTemplateInfo, sizeof(T_TemplateInfo)))
    {
        return True;
    }

    // 电价模式校验
    if (ptPeakShiftPara->ucElectricPricePattern <= 2)
    {
        ucParaIDBfuff[0] = 0x83 + ptPeakShiftPara->ucElectricPricePattern;
    }
    else
    {
        return False;
    }

    // 电价模式模板序号、节假日模板序号、节假日日期对应的模板序号校验
    ucConut = sizeof(ucParaIDBfuff) / sizeof(ucParaIDBfuff[0]);
    for (i = 0; i < ucConut; i++)
    {
        if (!CheckPeakShiftPara(ptPeakShiftPara, ucParaIDBfuff[i]))
        {
            // 参数校验错误
            rt_kprintf("check is error\n");
            return False;
        }
    }

    // 对实际的时间段模板进行校验和保存
    if (!SetPeakShiftTemplate(ptPeakShiftTemplate))
    {
        // 校验失败，未保存模板
        return False;
    }

    // 校验成功，将参数写入错峰参数文件
    rt_kprintf("check is correct\n");
    // ucActionID = ucParaID - 0x80 + PARA_PEAK_SHIFT_ENABLE;
    // SavePeakShiftParaAction(ucActionID, ptPeakShiftPara, &s_tPeakShiftPara);
    ptPeakShiftPara->wCRC = CRC_Cal((BYTE*)ptPeakShiftPara, sizeof(T_PeakShiftPara) - 2);
    writeFile(FILE_NAME_PEAKSHIFTPARA, (BYTE *)ptPeakShiftPara, sizeof(T_PeakShiftPara));
    rt_memcpy_s((BYTE*)&s_tPeakShiftPara, sizeof(T_PeakShiftPara), (BYTE*)ptPeakShiftPara, sizeof(T_PeakShiftPara));
    TemplForwardProcFlag = True;

    return True;
}

/**********************模板转发相关函数-start****************/
//获取模板转发标志位
/* Started by AICoder, pid:wfb1bw1999afce614bda0bb5622c1300e96539a3 */
//模板转发入口函数
void TemplateForwardMain(T_CommStruct *ptComm)
{
    if(TemplForwardProcFlag)
    {   
        SendTemplateData(ptComm);
        TemplateForCanFrm(ptComm->aucSendBuf, ptComm->wSendLength, DEV_TYPE_BMS, 0x00); //广播发送,发送地址为0
        return;
    }
}

static void PackagingPeakShitPara(T_CommStruct *ptComm,WORD wLength,BYTE *ucBuff,BOOLEAN Flag)
{
    CHAR cCh1;
    WORD i;
    WORD wSum;
    BYTE *p;
    T_PeakShiftPara *pPeakShiftPrara = NULL;
    BYTE aucTmp[HIGH_SEND_LEN];
    WORD ucCount = 0;

    p = ptComm->aucSendBuf;

    p[0] = SOI; /* SOI */
    p++;
    ucCount++;

    aucTmp[0] = PROTOCOL_VER_22;                         // 采用1363协议转发
    aucTmp[1] = 0x00;                                    // 转发的方式为广播
    aucTmp[2] = 0x4A;                                    // CID1:0x4A
    aucTmp[3] = NET_PEAKSHIFTTEMPLATE_FORWARD;           // CID2:0xCB

    /* LENGTH=LCHKSUM+LENID */
    *(WORD *)&aucTmp[4] = wLength*2;
    ExchangeIntHighLowByte(&aucTmp[4]);
    cCh1 = aucTmp[5] & 0x0F;
    cCh1 += (aucTmp[5] >> 4);
    cCh1 += (aucTmp[4] & 0x0F);
    cCh1 &= 0x0F;
    cCh1 = 16 - cCh1;
    aucTmp[4] += cCh1 * 16;

    // Flag为False表示发送错峰参数，为True表示发送错峰模板
    if(Flag == False)
    {
        aucTmp[6] = 0x00;
        aucTmp[7] = s_tTemplateInfo.ucDayTemplateNum;
        rt_memcpy_s(&aucTmp[8], wLength - 2,ucBuff, wLength - 2);
    }
    else
    {
        if(s_ucCommandNum == 0x08)
        {
            aucTmp[6] = 0x80 + s_ucCommandNum;
        }
        else
        {
            aucTmp[6] = s_ucCommandNum;
        }
        aucTmp[7] = s_tTemplateInfo.uTemplateInfo.tMonthTemp.atDateTemp[s_ucCommandNum - 1].ucDurationNum;
        rt_memcpy_s(&aucTmp[8], wLength - 2,ucBuff, wLength - 2);
    }

    /* 16进制转ASCII码 */
    for (i = 0; i < 6 + wLength; i++)
    {
        *p++ = HexToASCII(aucTmp[i] >> 4);
        *p++ = HexToASCII(aucTmp[i] & 0x0F);
        ucCount += 2;
    }

    /* CHKSUM */
    wSum = 0;
    for (i = 1; i <= wLength*2 + 12; i++)
    {
        wSum += ptComm->aucSendBuf[i];
    }
    wSum = (~wSum) + 1;
    for (i = 0; i < 4; i++)
    {
        *p++ = HexToASCII((wSum >> (12 - i * 4)) & 0x0F);
        ucCount++;
    }

    *p = EOI; /* EOI */
    ucCount++;

    ptComm->wSendLength = 18 + wLength*2;

    rt_kprintf("s_ucCommandNum:%d,ptComm->wSendLength:%d\n",s_ucCommandNum,ptComm->wSendLength);

    return;
}

static BOOLEAN SendTemplateData(T_CommStruct *ptComm)
{
    WORD wLength = 0;
    static BYTE ucCount = 0;

    if(s_ucCommandNum == 0)
    {
        //发送错峰参数
        wLength = sizeof(T_PeakShiftPara) + 2;  //加的两个字节,一个为CommadTye,一个为模板数量
        PackagingPeakShitPara(ptComm,wLength,(BYTE *)&s_tPeakShiftPara,False);
    }
    else
    {
        //发送错峰模板
        wLength = 48*sizeof(T_DurationStruct) + 2;  //加的两个字节,一个为CommadTye,一个为时间段数量
        PackagingPeakShitPara(ptComm,wLength,(BYTE *)s_tTemplateInfo.uTemplateInfo.tMonthTemp.atDateTemp[s_ucCommandNum - 1].atDura,True);
    }

    ucCount++;
    if(ucCount >= 3)
    {
        ucCount = 0;
        s_ucCommandNum++;
    }

    if(s_ucCommandNum == 9)
    {
        //发送结束
        TemplForwardProcFlag = False;
        s_ucCommandNum = 0;
    }

    return True;
}

//一次性整体设置所有参数
BOOLEAN SetPeakShiftAllPara(T_PeakShiftPara* ptPeakShiftPara)
{
    // BYTE ucActionID = 0;
    WORD wCRC = 0;

    if (ptPeakShiftPara == NULL)
    {
       return False;
    }

    //如果参数没有变化，则不更新
    if(!rt_memcmp(ptPeakShiftPara, &s_tPeakShiftPara, sizeof(T_PeakShiftPara)))
    {
        rt_kprintf("all para same\n");
        return True;
    }

    // ucActionID = ucParaID - 0x80 + PARA_PEAK_SHIFT_ENABLE;
    // SavePeakShiftParaAction(ucActionID, ptPeakShiftPara, &s_tPeakShiftPara);
    wCRC = CRC_Cal((BYTE*)ptPeakShiftPara, sizeof(T_PeakShiftPara)-2);
    if(ptPeakShiftPara->wCRC == wCRC)
    {
        rt_kprintf("para CRC cheeck SUCCESS\n");
        writeFile(FILE_NAME_PEAKSHIFTPARA, (BYTE *)ptPeakShiftPara, sizeof(T_PeakShiftPara));
        rt_memcpy((BYTE*)&s_tPeakShiftPara, (BYTE*)ptPeakShiftPara, sizeof(T_PeakShiftPara));
        return True;
    }

    rt_kprintf("para CRC cheeck Failured\n");
    return False;
}

BOOLEAN TempForwardCheck(T_DateTemplateStruct* ptDateTemp, BYTE* p)
{
    BYTE i;
    if(p == NULL || ptDateTemp == NULL)
    {
        return False;
    }
    ptDateTemp->ucDurationNum = p[0];
    rt_memcpy_s(ptDateTemp->atDura,192,&p[1],192);
    return True;
}

//模板比较，如果模板相同则不需要执行写入操作
BOOLEAN TemplateCompared(T_TemplateInfo* ptPeakShiftTemp)
{
    if(ptPeakShiftTemp == NULL)
    {
        return False;
    }
    
    ptPeakShiftTemp->wCRC = CRC_Cal((BYTE*)ptPeakShiftTemp, sizeof(T_TemplateInfo)-2);

    if(!rt_memcmp(ptPeakShiftTemp, &s_tTemplateInfo, sizeof(T_TemplateInfo)))
    {
        rt_kprintf("Template same\n");
        return True;
    }

    return False;
}
/* Ended by AICoder, pid:wfb1bw1999afce614bda0bb5622c1300e96539a3 */
/**********************模板转发相关函数-end******************/
