
#ifndef BALANCE_H
#define BALANCE_H

#include <stdbool.h>
#include <stdint.h>

// 均衡参数结构体
typedef struct {
    float startBalanceVoltageDifference; // 均衡压差启动阈值
} BalanceConfigStruct;

// 状态结构体
typedef struct {
    bool isBalancing; // 均衡状态
    uint8_t hasFault; // 故障状态 (0: 正常, 1: 断路故障, 2: 短路故障)
} BalanceStateStruct;

// 实时数据结构体
typedef struct {
    float cellVoltages[16]; // 单体电压数组，最多16个单体
    int cellCount; // 单体数量
} RealTimeDataStruct;

// 均衡算法计算结果结构体
typedef struct {
    unsigned char balanceStatus; // 0: 未开启, 1: 开启瞬间, 2: 开启后保持
    unsigned short balanceCode; // 每位表示对应单体是否需要均衡，0不需要，1需要
} BalanceAlgorithmResultStruct;

// 单体均衡组件
typedef struct {
    BalanceConfigStruct config;
    BalanceStateStruct state;
    RealTimeDataStruct realTimeData;
    BalanceAlgorithmResultStruct algorithmResult;
    uint16_t balanceCounter; // 计数器
    uint16_t shortCircuitCounter; // 短路故障计数器
    uint16_t openCircuitCounter; // 断路故障计数器
} SingleCellBalanceComponent;

// 均衡控制单元
int IdentifyCellSamplingChip(SingleCellBalanceComponent* component);
int ExecuteBalanceControl(SingleCellBalanceComponent* component);

// 均衡算法单元
int RunBalanceControlAlgorithm(SingleCellBalanceComponent* component, const RealTimeDataStruct* data);
int UpdateBalanceCircuitState(SingleCellBalanceComponent* component, unsigned char newState);

// 故障检测单元
int DetectBalanceCircuitFault(SingleCellBalanceComponent* component, const RealTimeDataStruct* data);
int UpdateBalanceCircuitFaultState(SingleCellBalanceComponent* component, uint8_t newFaultState);

// 配置管理单元
int ManageConfigParams(SingleCellBalanceComponent* component, const BalanceConfigStruct* config);

// 参数管理组件
int SetConfigParam(BalanceConfigStruct* config, float startBalanceVoltageDifference);
BalanceConfigStruct GetConfigParam(const SingleCellBalanceComponent* component);

// 数据管理组件
int SetRealTimeData(SingleCellBalanceComponent* component, const RealTimeDataStruct* data);
RealTimeDataStruct GetRealTimeData(const SingleCellBalanceComponent* component);

// 电芯均衡初始化接口
void InitializeSingleCellBalance(int cellCount, SingleCellBalanceComponent* component);

// 获取均衡故障信号接口
bool GetBalanceFaultSignal();
void bal_main(void * param);
#endif // BALANCE_H


