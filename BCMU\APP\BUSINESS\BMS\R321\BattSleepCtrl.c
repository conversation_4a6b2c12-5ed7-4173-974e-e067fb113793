#include "BattSleepCtrl.h"
#include "led.h"
#include "hisdata.h"
#include "protocol_V20.h"
#include "qtp.h"

BOOLEAN ButtonSleepModeManagement(Button_State *pButtonSta, T_BattDealInfoStruct *pBattDeal, T_BCMAlarmStruct *pAlarm)
{
    /* 若布防开启，则无法按键休眠 */
    if (pButtonSta->ucCurrentMode == BUT_SLEEP || GetQtpSleepStatus())
    {
            SetQtpSleepStatus(False);
            SetChargeMode(BATT_MODE_OFFLINE);
            pBattDeal->bModeChange = TRUE;
            return True;
    }
    return False;
}
