/**
 * @file     cmd.h
 * @brief    命令头文件.
 * @details  This is the detail description.
 * <AUTHOR> 
 * @date     2022-12-23
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation 
 * @par History:
 *   version: author, date, descn
 */ 
#ifndef _CMD_H
#define _CMD_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

#include "parse_layer.h"

/* 设备类型定义 */
#define     DEV_BMU                         1
#define     DEV_DC_DC                       2
#define     DEV_BSMU                        3
#define     DEV_BCMU                        4
#define     DEV_DAC_NORTH_DOWNLOAD          5
#define     DEV_DAC_NORTH_S1363             6
#define     DEV_DAC_NORTH_MODBUS            7
#define     DEV_DAC_PARALLEL_MASTER_MODBUS  8
#define     DEV_DC_AC                       9
#define     DEV_DAC_NORTH_S1363_IP          10
#define     DEV_DAC_NORTH_DOWNLOAD_IP       11
#define     DEV_DAC_NORTH_APPTEST           12
#define     DEV_DAC_NORTH_DOWNLOAD_MASTER   13

/* DC-AC命令ID:唯一标示一个命令 */    
#define DC_AC_NOT_SEND_SOUTH_ID                          0        ///<  参数不需要发给南向     
#define DC_AC_GET_REAL_DATA                              1        ///<  获取实时数据
#define DC_AC_GET_VER_DATA                               2        ///<  获取版本信息
#define DC_AC_GET_PARA_DATA                              3        ///<  获取所有参数
#define DC_AC_SET_PARA_DATA                              4        ///<  设置所有参数

#define DC_AC_GET_REMOTE_CTRL_PARA_DATA                  5        ///<  遥控开关机
#define DC_AC_GET_POWER_GRID_PARA_DATA                   6        ///<  电网参数
#define DC_AC_GET_CHARA_PARA_DATA                        7        ///<  特性参数
#define DC_AC_GET_MPPT_PARA_DATA                         8        ///<  MPPT参数设置
#define DC_AC_GET_LVRT_PARA_DATA                         9        ///<  LVRT参数设置
#define DC_AC_GET_LVRT_CC_PARA_DATA                      10       ///<  LVRT特征曲线
#define DC_AC_GET_HVRT_PARA_DATA                         11       ///<  HVRT参数设置
#define DC_AC_GET_PID_PARA_DATA                          12       ///<  PID参数设置
#define DC_AC_GET_AFCI_PARA_DATA                         13       ///<  AFCI参数
#define DC_AC_GET_POWER_REGULATION_PARA_DATA             14       ///<  功率调节参数
#define DC_AC_GET_COMM_FAIL_SAFE_PARA_DATA               15       ///<  通信断链失效保护参数
#define DC_AC_GET_ACT_REGULATION_PARA_DATA               16       ///<  有功功率调节参数
#define DC_AC_GET_REACT_REGULATION_PARA_DATA             17       ///<  无功功率调节
#define DC_AC_GET_P_PN_CC_PARA_DATA                      18       ///<  cosφ-P/Pn特性曲线
#define DC_AC_GET_Q_U_CC_PARA_DATA                       19       ///<  Q-U特性曲线设置
#define DC_AC_GET_PF_U_CC_PARA_DATA                      20       ///<  PF-U特性曲线设置
#define DC_AC_GET_CCP_ACT_PARA_DATA                      21       ///<  并网点控制-有功功率
#define DC_AC_GET_CCP_REACT_PARA_DATA                    22       ///<  并网点控制-无功功率
#define DC_AC_GET_CCP_FEED_OVER_PARA_DATA                23       ///<  并网点控制-馈电越限保护关机
#define DC_AC_GET_PROTECT_PARA_DATA                      24       ///<  保护参数
#define DC_AC_GET_PROTECT_L1_PARA_DATA                   25       ///<  一级保护参数
#define DC_AC_GET_PROTECT_L2_PARA_DATA                   26       ///<  二级保护参数
#define DC_AC_GET_PROTECT_L3_PARA_DATA                   27       ///<  三级保护参数
#define DC_AC_GET_PROTECT_L4_PARA_DATA                   28       ///<  四级保护参数
#define DC_AC_GET_PROTECT_L5_PARA_DATA                   29       ///<  五级保护参数
#define DC_AC_GET_PROTECT_L6_PARA_DATA                   30       ///<  六级保护参数

#define DC_AC_READ_DATA                                  31        ///<  读数据
#define DC_AC_WRITE_DATA                                 32        ///<  写数据

#define DC_AC_SET_REMOTE_CTRL_PARA_DATA                  33       ///<  遥控开关机
#define DC_AC_SET_POWER_GRID_PARA_DATA                   34       ///<  电网参数
#define DC_AC_SET_CHARA_PARA_DATA                        35       ///<  特性参数
#define DC_AC_SET_MPPT_PARA_DATA                         36       ///<  MPPT参数设置
#define DC_AC_SET_LVRT_PARA_DATA                         37       ///<  LVRT参数设置
#define DC_AC_SET_LVRT_CC_PARA_DATA                      38       ///<  LVRT特征曲线
#define DC_AC_SET_HVRT_PARA_DATA                         39       ///<  HVRT参数设置
#define DC_AC_SET_PID_PARA_DATA                          40       ///<  PID参数设置
#define DC_AC_SET_AFCI_PARA_DATA                         41       ///<  AFCI参数
#define DC_AC_SET_POWER_REGULATION_PARA_DATA             42       ///<  功率调节参数
#define DC_AC_SET_COMM_FAIL_SAFE_PARA_DATA               43       ///<  通信断链失效保护参数
#define DC_AC_SET_ACT_REGULATION_PARA_DATA               44       ///<  有功功率调节参数
#define DC_AC_SET_REACT_REGULATION_PARA_DATA             45       ///<  无功功率调节
#define DC_AC_SET_P_PN_CC_PARA_DATA                      46       ///<  cosφ-P/Pn特性曲线
#define DC_AC_SET_Q_U_CC_PARA_DATA                       47       ///<  Q-U特性曲线设置
#define DC_AC_SET_PF_U_CC_PARA_DATA                      48       ///<  PF-U特性曲线设置
#define DC_AC_SET_CCP_ACT_PARA_DATA                      49       ///<  并网点控制-有功功率
#define DC_AC_SET_CCP_REACT_PARA_DATA                    50       ///<  并网点控制-无功功率
#define DC_AC_SET_CCP_FEED_OVER_PARA_DATA                51       ///<  并网点控制-馈电越限保护关机
#define DC_AC_SET_PROTECT_PARA_DATA                      52       ///<  保护参数
#define DC_AC_SET_PROTECT_L1_PARA_DATA                   53       ///<  一级保护参数
#define DC_AC_SET_PROTECT_L2_PARA_DATA                   54       ///<  二级保护参数
#define DC_AC_SET_PROTECT_L3_PARA_DATA                   55       ///<  三级保护参数
#define DC_AC_SET_PROTECT_L4_PARA_DATA                   56       ///<  四级保护参数
#define DC_AC_SET_PROTECT_L5_PARA_DATA                   57       ///<  五级保护参数
#define DC_AC_SET_PROTECT_L6_PARA_DATA                   58       ///<  六级保护参数

#define DC_AC_GET_FAST_REAL_DATA                         59        ///<  获取快速轮询实时数据
#define DC_AC_GET_SLOW_REAL_DATA                         60        ///<  获取普通轮询实时数据
#define DC_AC_GET_FAST_SLOW_REAL_DATA                    61        ///<  获取实时数据

#define DC_AC_GET_ADG_PARA_DATA                          66        ///<  获取所有比例校正参数
#define DC_AC_SET_ADG_PARA_DATA                          67        ///<  设置所有比例校正参数

#define DC_AC_GET_PRO_PARA_DATA                          68        ///<  获取生产相关参数
#define DC_AC_SET_PRO_PARA_DATA                          69        ///<  设置生产相关参数

#define DC_AC_GET_OTHER_PPARA_DATA                       70        ///<  获取其他参数
#define DC_AC_SET_OTHER_PPARA_DATA                       71        ///<  获取其他参数

#define DC_AC_CONTROL_ORDER                              72        ///<  控制命令
#define DC_AC_IV_CTRL_ORDER                              73        ///<  逆变器控制命令
#define DC_AC_PRODUCT_ORDER                              74        ///<  生产控制命令

#define DC_AC_GET_IV_DATA                                75        ///<  获取iv数据

#define DC_AC_FAULT_RECORD_FIRST                         76        ///<  上传故障录波数据首发帧
#define DC_AC_FAULT_RECORD_DATA                          77        ///<  上传故障录波数据帧

#define DC_AC_SET_FAULT_RECORD1_PARA                     78        ///<设置故障录波1参数
#define DC_AC_SET_FAULT_RECORD2_PARA                     79        ///<设置故障录波2参数

#define DC_AC_GET_FAULT_RECORD1_PARA                     80        ///<  获取故障录播1数据
#define DC_AC_GET_FAULT_RECORD2_PARA                     81        ///<  获取故障录播2数据

#define DC_AC_POWER_UPDATE_STATUS_INFO                   82
#define DC_AC_GET_UPDATE_STATUS_DATA                     83        ///<  升级状态主动上送
#define DC_AC_GET_AUXI_STRL_FRAME_DATA                   84        ///<  辅控升级帧数
#define DC_AC_SET_HVRT_CC_PARA_DATA                      85        ///<  设置HVRT特征曲线参数
#define DC_AC_GET_HVRT_CC_PARA_DATA                      86        ///<  获取HVRT特征曲线
#define DC_AC_SET_STRING_IN_DETECTION_DATA               87        ///<  设置组串接入检测参数
#define DC_AC_GET_STRING_IN_DETECTION_DATA               88        ///<  获取组串接入检测参数
#define DC_AC_SET_OVER_UNDER_FREQ_DERATE_DATA            89        ///<  设置过欠频降额参数
#define DC_AC_GET_OVER_UNDER_FREQ_DERATE_DATA            90        ///<  获取过欠频降额参数
#define DC_AC_GET_ALARM_DATA                             91        ///<  故障录波的时候功率主动上送告警
#define DC_AC_SET_RD_TEST_RELATED_DATA                   92        ///<  设置研发测试相关参数
#define DC_AC_GET_RD_TEST_RELATED_DATA                   93        ///<  获取研发测试相关参数
#define DC_AC_PV_ALARM_CLEAR_ORDER                       94        ///<  逆变器告警清除命令
#define DC_AC_RESTART_SYSTEM_ORDER                       95        ///<  系统重启控制命令
#define DC_AC_WORK_MODE_CTRL_ORDER                       96        ///<  工作模式控制命令
#define DC_AC_DEBUG_CTRL_ORDER                           97        ///<  研发调试控制命令
#define DC_AC_SET_DRIVE_TEST_RELATED_DATA                98        ///<  设置驱动测试相关参数
#define DC_AC_GET_DRIVE_TEST_RELATED_DATA                99        ///<  获取驱动测试相关参数
#define DC_AC_SET_ADC_TEST_RELATED_DATA                  100       ///<  设置ADC采样测试相关参数
#define DC_AC_GET_ADC_TEST_RELATED_DATA                  101       ///<  获取ADC采样测试相关参数
#define DC_AC_SET_PRODUCTION_TEST_DATA                   102       ///<  设置生产测试相关参数
#define DC_AC_GET_PRODUCTION_TEST_DATA                   103       ///<  获取生产测试相关参数

#define BMU_UPDATE_DATA_TRIG          104        ///<  数据传输触发帧
#define BMU_UPDATE_DATA               105        ///<  程序更新数据传输帧
#define BMU_UPDATE_DATA_INTERRUPT     106        ///<  数据传输中断帧
#define BMU_UPDATE_TRIG               107        ///<  程序更新触发帧

#define DC_AC_UPDATE_DATA_TRIG                           BMU_UPDATE_DATA_TRIG        ///<  数据传输触发帧
#define DC_AC_UPDATE_DATA                                BMU_UPDATE_DATA        ///<  程序更新数据传输帧
#define DC_AC_UPDATE_DATA_INTERRUPT                      BMU_UPDATE_DATA_INTERRUPT        ///<  数据传输中断帧
#define DC_AC_UPDATE_TRIG                                BMU_UPDATE_TRIG        ///<  程序更新触发帧

#define DC_AC_UPLOAD_FILE_TRIG                           108        ///<  文件上传触发帧
#define DC_AC_UPLOAD_FILE_LIST                           109        ///<  文件上传列表帧
#define DC_AC_UPLOAD_FILE_DATA                           110        ///<  文件上传数据帧

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _CMD_H
