/**************************************************************************
* Copyright (C) 2001, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：ZXDU18 CTL板软件
* 文件名称：apptest.c
* 文件说明：协议模块
* 作    者：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/

#include "common.h"
#include "algorithmAES.h"
#include "sample.h"
#include "hisdata.h"
#include "realAlarm.h"
#include "battery.h"
#include "para.h"
#include "protocol.h"
#include "CommCan.h"
#include "Candrive.h"
#include "stdio.h"
#include "commBdu.h"
#include "wireless.h"
#include "apptest.h"
#include "battery.h"
#include "ism330dhcx.h"
#include "fileSys.h"
#include "led.h"
#include "HeaterControl.h"
#if !defined(KW_CHECK)
#include <netif/ethernetif.h>
#include <rtthread.h>
#include <lwip/sockets.h>
#endif
#include "pdt_version.h"

#include <rtdef.h>
#include <rtdevice.h>
#include "utils_rtthread_security_func.h"

extern int shutdown(int s, int how);
extern int accept(int s, struct sockaddr *addr, socklen_t *addrlen);
extern int recv(int s, void *mem, size_t len, int flags);
extern int send(int s, const void *dataptr, size_t size, int flags);
extern int socket(int domain, int type, int protocol);
extern int bind(int s, const struct sockaddr *name, socklen_t namelen);
extern int listen(int s, int backlog);

/*工装测试*/
extern BYTE	 s_ucTestTriggerRepeat;
extern BYTE	 s_ucP2PTestTriggerRepeat;
extern WORD  s_wApptestTriggerTimeOut;
Static rt_device_t ETH_dev;
static LONG s_lConnected = 0; // TCP服务连接
static LONG s_lSock = 0;
Static BYTE s_ucRunFlag = False;
#if !defined(KW_CHECK)
    rt_thread_t NetTaskItem;
#endif

static T_SysPara   s_tSysPara;
static rt_timer_t s_waitReacAndResetTimer = RT_NULL;
static rt_timer_t s_shutDownTimer = RT_NULL;

/*********************  静态函数原型定义  **********************/
Static void ResetApptest(void *para);
Static void ApptestShutDown(void *para);
Static BOOLEAN CheckFatRecordValid( T_FatRecordStruct * fatRecord );
Static void WriteFatRecordToEEPROM( T_FatRecordStruct * fatRecord );
Static void ReadFatRecordFromEEPROM( T_FatRecordStruct * fatRecord );
Static void  GetCsuSn( CHAR * p );
Static void  SetCsuSn(BYTE *buff);
Static void  SetBmsSn(BYTE *buff);
Static void Process_NET_Comm(void* parameter);
Static BOOLEAN NetTestOnOff(BYTE ucState);
Static BOOLEAN isValidApptestByte(T_CommStruct *ptComm, BYTE ucRevLen);
Static BOOLEAN BduCtrlByType(BYTE ucCommandType, BYTE ucCmd, BYTE ucData);

union {
	FLOAT result;
	char buff[4];

}float_data;

//校验序列号0-9 A-Z 填充字节0x00
Bool CheckSn(BYTE *p, BYTE ucSnLength )
{
	BYTE i = 0;
	if(p == NULL || ucSnLength == 0)
	{
		return FALSE;
	}
	for(i = 0; i < ucSnLength; i++)
	{
		if( (p[i] >= 0x30 && p[i] <= 0x39) || (p[i] >= 0x41 && p[i] <= 0x5A) || p[i] == 0x00)
		{
		
		}
		else    //如果不是0-9 或A-Z 或0x00 验不过;
		{
			return FALSE;
		}
	}

	return TRUE;
}

/****************************************************************************
* 函数名称：ResetApptest()
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：apptest模式下重启复位
* 作    者：
* 设计日期：2022-07-14
* 修改记录：
* 日    期              版      本              修改人          修改摘要
***************************************************************************/
Static void ResetApptest(void *para)
{
    rt_kprintf("system soft ReseType:%d\n",NO_RESET_APPTEST);
    system_reset();
}

/****************************************************************************
* 函数名称：RecResetCmd()
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：处理接收到的复位命令
* 作    者：潘奇银
* 设计日期：2009-06-12
* 修改记录：
* 日    期              版      本              修改人          修改摘要
***************************************************************************/
void RecResetCmd(BYTE ucPort)
{
    if (RT_NULL == s_waitReacAndResetTimer)
    {
        s_waitReacAndResetTimer = rt_timer_create("reset_wait_protocol_react",ResetApptest,RT_NULL,1000,RT_TIMER_FLAG_ONE_SHOT);
    }

    RETURN_IF_FAIL(s_waitReacAndResetTimer != RT_NULL);
    rt_timer_start(s_waitReacAndResetTimer);
    saveResetData(RESET_REASON, NO_RESET_APPTEST);
}

/****************************************************************************
* 函数名称：CheckFatRecordValid()
* 调    用：无
* 被 调 用：
* 输入参数：fatRecord -出厂测试记录结果
* 返 回 值：
* 功能描述：对出厂测试记录信息进行容错校验
* 作    者：周雪刚
* 设计日期：2013-03-28
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
Static BOOLEAN CheckFatRecordValid( T_FatRecordStruct * fatRecord )
{ 
     BOOLEAN ucFlag = True;
//     BYTE i = 0;
      
    if ((fatRecord->fatRecordType != BMS_FT_TEST))
    {
        ucFlag = False;
    }
    else if (CheckTimeValid(&fatRecord->tTime) == False)
    {
        ucFlag = False;
    }
    else if ((fatRecord->fatRecordResult > RESULT_PASS))
    {
        ucFlag = False;
    }
    
    return ucFlag;

}

/****************************************************************************
* 函数名称：WriteFatRecordToEEPROM
* 输入参数：无
* 返 回 值：无
* 功能描述：保存出厂测试记录到EEPROM
***************************************************************************/
Static void WriteFatRecordToEEPROM( T_FatRecordStruct * fatRecord )
{
    BYTE    *p;

    p = (BYTE *)fatRecord;
    fatRecord->wCheckSum = CRC_Cal( p, sizeof(T_FatRecordStruct)-2 );

	switch(fatRecord->fatRecordType)
	{
		case 0:
		    writeFile("/fatRecord00", p, sizeof(T_FatRecordStruct));
			break;
		case 1:
		    writeFile("/fatRecord01", p, sizeof(T_FatRecordStruct));
			break;
		default:
		    break;
	}

    return;
}


/****************************************************************************
* 函数名称：ReadFatRecordFromEEPROM
* 输入参数：无
* 返 回 值：无
* 功能描述：从EEPROM中读取出厂测试记录
***************************************************************************/
Static void ReadFatRecordFromEEPROM( T_FatRecordStruct * fatRecord )
{
	BYTE    *p;
    WORD    wCrc;
    BOOLEAN bFlag = True;
	LONG lFatBmsRecordReadRtn = 0;
	UNUSED LONG lFatModRecordReadRtn = 0;  // NOTE: 在QTP中校验fatRecord01文件里数据

    p = (BYTE *)fatRecord;
	lFatBmsRecordReadRtn = readFile("/fatRecord00", p, sizeof(T_FatRecordStruct));
	p+=sizeof(T_FatRecordStruct);
	lFatModRecordReadRtn = readFile("/fatRecord01", p, sizeof(T_FatRecordStruct));

	bFlag = True;
	wCrc = CRC_Cal( (BYTE *)(fatRecord), sizeof(T_FatRecordStruct)-2 );

	if (wCrc == fatRecord[0].wCheckSum && lFatBmsRecordReadRtn > 0)
	{
		if (CheckFatRecordValid((T_FatRecordStruct *)(fatRecord)) == False)
		{
			bFlag = False;  
		}
	}
	else
	{
		bFlag = False;
	}

	if (bFlag == False )
	{
		rt_memset((T_FatRecordStruct *)(fatRecord), 0x00, sizeof(T_FatRecordStruct));
	}

    return;
}

/****************************************************************************
* 函数名称：SetFactoryAcceptanceTest()
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：获取出厂测试记录
* 作    者  ：
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
void SetFactoryAcceptanceTest( BYTE ucPort )
{
    T_FatRecordStruct t_FatRecord;

    t_FatRecord.fatRecordType = s_tProtocol.aucRecBuf[7];
    ExchangeIntHighLowByte( (BYTE *)(s_tProtocol.aucRecBuf+8) );
    rt_memcpy((BYTE*)(&t_FatRecord), (BYTE*)(&s_tProtocol.aucRecBuf[7]),
            SIGAL_FAT_RECORD_LENGTH);//PCLINT检查:经确认，对程序正确性无影响。
    //校验记录是否符合条件
    if (CheckFatRecordValid((T_FatRecordStruct*) & t_FatRecord) == True)
    {
        t_FatRecord.bRecValid = True;
        WriteFatRecordToEEPROM((T_FatRecordStruct*)&t_FatRecord);

    }
    else
    {
        s_tProtocol.ucRTN   = RTN_INVALID_DATA;
    }

    return;
}


/****************************************************************************
* 函数名称：GetFactoryAcceptanceTest()
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：发送出厂测试记录
* 作    者  ：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
void GetFactoryAcceptanceTest( BYTE ucPort )
{
    WORD wSendLen = 36;//(9+9) * 2 = 36
    BYTE *p = RT_NULL;
    T_FatRecordStruct t_FatRecord[3];
    rt_memset(t_FatRecord, 0x00, sizeof(t_FatRecord));

    ReadFatRecordFromEEPROM((T_FatRecordStruct *)(&t_FatRecord[0]));
    ExchangeIntHighLowByte((BYTE*)&t_FatRecord[0].tTime);
    ExchangeIntHighLowByte((BYTE*)&t_FatRecord[1].tTime);

    p = s_tProtocol.aucSendBuf;

    rt_memcpy( p, (BYTE *)&t_FatRecord[0], SIGAL_FAT_RECORD_LENGTH);//后面3个字节不发送，共9字节
    p+=SIGAL_FAT_RECORD_LENGTH;
    rt_memcpy( p, (BYTE *)&t_FatRecord[1], SIGAL_FAT_RECORD_LENGTH);

    /* LENID */
    s_tProtocol.wSendLenid = wSendLen;//为解决KW问题

    return;
}

/****************************************************************************
* 函数名称：RecCanBusCtrl(BYTE ucPort)
* 调    用：无
* 被 调 用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：启动CAN总线测试，判断CAN是否初始化
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
void RecCanBusCtrl(BYTE ucPort)
{
	BYTE *p  = s_tProtocol.aucSendBuf;
	WORD wSendLen = 2;

    //InitCan();
   	InitCanProto();
	 GetSmrRealData(CAN_DEV);
	*p++ = 0x01;
	 *p = 0; //解决KW
    //s_tProtocol.wSendLenid  = (WORD) ((p-s_tProtocol.aucSendBuf)*2);
    s_tProtocol.wSendLenid = wSendLen;//为解决KW问题
    return;
}
/****************************************************************************
* 函数名称：SendCanReport(BYTE ucPort)
* 调    用：无
* 被 调 用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：获取CAN总线测试信息
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
void SendCanReport(BYTE ucPort)
{
	WORD wSendLen = 2;
    BYTE *p  = s_tProtocol.aucSendBuf;

    *p++  = CanRecCon();
    //s_tProtocol.wSendLenid  = (WORD) ((p-s_tProtocol.aucSendBuf)*2);
    s_tProtocol.wSendLenid = wSendLen;//为解决KW问题
    *p = 0;  // 解决kw
    return;
}


/****************************************************************************
* 函数名称：SetQuitAPPTest(BYTE ucPort)
* 调    用：无
* 被 调 用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：设置退出测试模式
* 作    者  ：
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
void SetQuitAPPTest(BYTE ucPort)
{
	s_ucTestTriggerRepeat = 0;
	SetApptestFlag(False);
    s_ucP2PTestTriggerRepeat = 0;
	SaveAction(GetActionId(CONTOL_QUIT_APPTEST),"Quit Apptest");
	ClearLedByAddr();
	ClearBtnCount();     //清除按键次数，重新计数 0908 mwl
    ClearBlanceCircuitStatus();
	SetHeaterWorkingMode(HEATER_NORMAL_MODE);  //退出apptest时将加热器设置为正常工作状态，关闭测试模式标志位
	SetHeaterTestCtrl(HEATER_TEST_NONE);
	DeleteTrigTimer();
#ifdef POWER_COMM_FAIL_ALARM
	SetPowerCommShieldFlag(True);
#endif
	return;
}

/****************************************************************************
* 函数名称：SendBMSCtrl(BYTE ucPort)
* 调    用：无
* 被 调 用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：BMS控制，充放电回路、蜂鸣器、LED、均衡
* 作    者  ：
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
void SendBMSCtrl(BYTE ucPort)
{
	BYTE ledStatus = 0;         // LED灯控制
	WORD wBalVal = 0;           // 均衡控制
	//BYTE bCycle = 0;            // 充放电回路控制
	BYTE bStatus = 0;
	BYTE i;

	T_BMSControlStruct tBmsCtrl;
	T_BattResult tBattResult;
	T_CtrlOutStruct tCtrlOut;
	rt_memset(&tBattResult, 0, sizeof(T_BattResult));
	GetCtrlOut(&tCtrlOut);
	GetBattResult(&tBattResult);

	rt_memcpy((BYTE*)&tBmsCtrl, (BYTE*)(&s_tProtocol.aucRecBuf[7]), sizeof(T_BMSControlStruct));

    //充电回路开关和放电回路开关不能同时闭合,控制无效
    if(tBmsCtrl.ucCharge == 0x00 && tBmsCtrl.ucDisCharge == 0x00)
    {
        s_tProtocol.ucRTN  = RTN_INVALID_DATA;    //无效数据
        return;
    }


	if(tBmsCtrl.ucCharge == 0x00 && tBmsCtrl.ucDisCharge == 0x01)	//充电，放电保护开，放电回路断
	{
		BduCtrl(SCI_CTRL_CHG_STG, DISABLE);
		BduCtrl(SCI_CTRL_CHG2CHG, ENABLE);
		// BduCtrl(SCI_CTRL_DISCHG_PRT, ENABLE);
		BduCtrl(SCI_CTRL_CHG_PRT, DISABLE);
	}
	else if(tBmsCtrl.ucCharge == 0x01 && tBmsCtrl.ucDisCharge == 0x00)	//放电，充电保护开，充电回路断
	{
		BduCtrl(SCI_CTRL_DISCHG_STG, DISABLE);
		BduCtrl(SCI_CTRL_CHG2CHG, ENABLE);
		// BduCtrl(SCI_CTRL_CHG_PRT, ENABLE);
		BduCtrl(SCI_CTRL_DISCHG_PRT, DISABLE);
	}
	// else if(tBmsCtrl.ucCharge == 0x01 && tBmsCtrl.ucDisCharge == 0x01)
	else
	{
		//SetBduCtrl(0);
		BduCtrl(SCI_CTRL_CHG_STG, DISABLE);
		BduCtrl(SCI_CTRL_DISCHG_STG, DISABLE);
		BduCtrl(SCI_CTRL_CHG_PRT, ENABLE);
		BduCtrl(SCI_CTRL_DISCHG_PRT, ENABLE);
	}
	ClearBtnCount();
	tCtrlOut.bBuzz = tBmsCtrl.ucBuzzer;
	ledStatus = tBmsCtrl.ucLEDStatus;

	for(i = 0; i < 6; i++)
	{
		bStatus = ledStatus % 2;
		//SetLedStatus(i, bStatus);
		tCtrlOut.bLed[i] = bStatus;
		//CtrlLedOn(i, bStatus);
		ledStatus = ledStatus / 2;
	}
	SetCtrlOut(&tCtrlOut);
	ClearLedByAddr();
	// SetDummyTimer(TIMER_APPTEST_LED, DELAY, (ONE_SECOND*30), MSG_APPTEST_LED);
	
	wBalVal = tBmsCtrl.ucBanlanceLow + (tBmsCtrl.ucBanlanceHigh << 8);
	SetBattBalance(wBalVal);

	return;
}
/****************************************************************************
* 函数名称：GetFactoryInfo(BYTE ucPort)
* 调    用：无
* 被 调 用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：获取BMS厂家信息
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
void GetFactoryInfo( BYTE ucPort )
{
    WORD wSendLen = 192;//(32+32+32)*2 = 96*2 = 192
    CHAR acSysName[32] = {0,};
    BYTE *p = NULL;
    T_BmsInfoStruct tBmsInfo = {0};

    readBMSInfofact(&tBmsInfo);

    p  = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0x00, sizeof(s_tProtocol.aucSendBuf));

    GetBmsSysName(acSysName, sizeof(acSysName));
    MemsetBuff(p, acSysName, (BYTE)rt_strnlen_s(acSysName, sizeof(s_tSysPara.acBMSSysName)), 32, 0x20);//SM软件名称
    p += 32;

#ifdef SOFTBANK_VERSION_BUILD
    MemsetBuff(p, BMS_VER_SOFTBANK, (BYTE)rt_strnlen_s(BMS_VER_SOFTBANK, 32), 32, 0x20);//SM软件版本
#else
    MemsetBuff(p, BMS_VER, (BYTE)rt_strnlen_s(BMS_VER, 32), 32, 0x20);//SM软件版本
#endif
    p += 32;

    MemsetBuff(p, tBmsInfo.acBattCorpName, (BYTE)rt_strnlen_s(tBmsInfo.acBattCorpName, 20), 32, 0x20);//厂家名称

    p += 32;
    /* LENID */
    //s_tProtocol.wSendLenid  = (WORD) ((p-s_tProtocol.aucSendBuf)*2);
    s_tProtocol.wSendLenid = wSendLen;//为解决KW问题
    *p = 0;   // 解决kw
}
/****************************************************************************
* 函数名称：GetBMSPACKFactoryInfo(BYTE ucPort)
* 调    用：无
* 被 调 用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：获取BMS电池PACK厂家信息
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
void GetBMSPACKFactoryInfo(BYTE ucPort)
{
	WORD wSendLen = 0;//(1+20+20+20+20+2+1+1)*2 = 170
	BYTE *p = NULL;
	T_BmsPACKFactoryStruct  tBmsPACKFactory;

	readBmsPackFacInfo(&tBmsPACKFactory);
	p  = s_tProtocol.aucSendBuf;

	//*p++ = tBmsPACKFactory.ucCellManufactruer;
    //wSendLen++;

	 MemsetBuff(p, tBmsPACKFactory.acPackBarCode[0], 20, 20, 0x20);   //PACK_BAR_CODE
	 p   += 20;
     wSendLen += 20;

	 MemsetBuff(p, tBmsPACKFactory.acPackBarCode[1], 20, 20, 0x20);   //PACK_BAR_CODE
	 p   += 20;
     wSendLen += 20;

	 MemsetBuff(p, tBmsPACKFactory.acPackBarCode[2], 20, 20, 0x20);   //PACK_BAR_CODE
	 p   += 20;
     wSendLen += 20;

	 MemsetBuff(p, tBmsPACKFactory.acPackBarCode[3], 20, 20, 0x20);   //PACK_BAR_CODE
	 p   += 20;
     wSendLen += 20;

	 //PutInt16ToBuff(p, (SHORT)tBmsPACKFactory.tBattDate.wYear);
	 //p +=2;                                                      //电池年
	 //*p++ = tBmsPACKFactory.tBattDate.ucMonth;                   //电池月
	 //*p++ = tBmsPACKFactory.tBattDate.ucDay;                     //电池日                  //电池出厂日期
	 //wSendLen += 4;

    /* LENID */
    //s_tProtocol.wSendLenid  = (WORD) ((p-s_tProtocol.aucSendBuf)*2);
	 s_tProtocol.wSendLenid = wSendLen * 2;//为解决KW问题
	 *p = 0;    // 解决kw
	 
}

/****************************************************************************
* 函数名称：SetBMSPACKFactoryInfo(BYTE ucPort)
* 调    用：无
* 被 调 用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：设置BMS电池PACK厂家信息
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
void SetBMSPACKFactoryInfo(BYTE ucPort)
{
	T_BmsPACKFactoryStruct  tBmsPACKFactory;
	BYTE *p = NULL;
	p  = (BYTE *)(&s_tProtocol.aucRecBuf[7]);

	//190128 READ FIRST, THEN SET
	readBmsPackFacInfo(&tBmsPACKFactory);

//	memcpy(tBmsPACKFactory.acPackBarCode, (BYTE *)(&s_tProtocol.aucRecBuf[7]), sizeof(tBmsPACKFactory.acPackBarCode));
	MemsetBuff(tBmsPACKFactory.acPackBarCode[0], p, 20, 20, 0x20);   //PACK_BAR_CODE
	p += 20;
	MemsetBuff(tBmsPACKFactory.acPackBarCode[1], p, 20, 20, 0x20);   //PACK_BAR_CODE
	p += 20;	
	MemsetBuff(tBmsPACKFactory.acPackBarCode[2], p, 20, 20, 0x20);   //PACK_BAR_CODE
	p += 20;
	MemsetBuff(tBmsPACKFactory.acPackBarCode[3], p, 20, 20, 0x20);   //PACK_BAR_CODE
	p += 20;
	
  tBmsPACKFactory.wCRC = CRC_Cal((BYTE*)&tBmsPACKFactory, (sizeof(tBmsPACKFactory)-2));
	
	writeBmsPackFacInfo(&tBmsPACKFactory);

  return;
}

/****************************************************************************
* 函数名称：GetCsuId(BYTE ucPort)
* 调    用：无
* 被 调 用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：获取CSUID
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
void GetCsuId(BYTE ucPort)
{
	WORD wSendLen = 30;//15*2 = 30
	CHAR chCsuId[15];
	BYTE *p  = s_tProtocol.aucSendBuf;

	GetCsuSn((CHAR *)chCsuId);
	rt_memcpy(p, (BYTE *)chCsuId, sizeof(chCsuId));
	p += sizeof(chCsuId);

	//s_tProtocol.wSendLenid  = (WORD) ((p-s_tProtocol.aucSendBuf)*2);
	s_tProtocol.wSendLenid = wSendLen;//为解决KW问题
	*p = 0;  // 解决kw
	return;
}

/****************************************************************************
* 函数名称：SetCsuId(BYTE ucPort)
* 调    用：无
* 被 调 用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：设置CsuId
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
void SetCsuId(BYTE ucPort)
{
	//对设置的序列号进行校验，以免设置后出现乱码 1105 mwl
	if(CheckSn(&s_tProtocol.aucRecBuf[7], 15) == FALSE)
	{
		s_tProtocol.ucRTN  = RTN_INVALID_DATA;    //无效数据
        return;
	}
    SetCsuSn(&s_tProtocol.aucRecBuf[7]);
    return;
}

void SetBmsId(BYTE ucPort)
{
	//对设置的序列号进行校验，以免设置后出现乱码 1105 mwl
	if(CheckSn(&s_tProtocol.aucRecBuf[7], 15) == FALSE)
	{
		s_tProtocol.ucRTN  = RTN_INVALID_DATA;    //无效数据
        return;
	}
    SetBmsSn(&s_tProtocol.aucRecBuf[7]);
    return;
}

void GetBmsId(BYTE ucPort)
{
	WORD wSendLen = 30;//15*2 = 30
	CHAR chBmsId[15];
	BYTE *p  = s_tProtocol.aucSendBuf;

	GetBmsSn((CHAR *)chBmsId);
	rt_memcpy(p, (BYTE *)chBmsId, sizeof(chBmsId));
	p += sizeof(chBmsId);

	//s_tProtocol.wSendLenid  = (WORD) ((p-s_tProtocol.aucSendBuf)*2);
	s_tProtocol.wSendLenid = wSendLen;//为解决KW问题
	*p = 0; // 解决kw
	return;
}


/****************************************************************************
* 函数名称：SetDcControl(BYTE ucPort)
* 调    用：无
* 被 调 用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：设置输出干接点控制
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
void SetDcControl(BYTE ucPort)
{
	BYTE i = 0;
	T_CtrlOutStruct tCtrlOut;
	WORD wtmp	= 0x0000;

	//输出干接点
	for(i=0; i< 8; i++ )
	{
		if(s_tProtocol.aucRecBuf[7+i] == 0)
		{
			wtmp |= BIT(i);
		}
	}

	GetCtrlOut(&tCtrlOut);
	tCtrlOut.wRelayOut	= wtmp;
	SetCtrlOut(&tCtrlOut);

	return;

}

/****************************************************************************
* 函数名称  ：GetCsuSn
* 调    用  ：无
* 被 调 用  ：
* 输入参数  ：
* 返 回 值  ：
* 功能描述  ：获取存在flash的监控序列号
* 作    者  ：
* 设计日期  ：
* 修改记录  ：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
Static void GetCsuSn( CHAR * p )
{
    T_BmsPACKFactoryStruct tBmsPackFacInfo;
    rt_memset(&tBmsPackFacInfo, 0, sizeof(tBmsPackFacInfo));

    readBmsPackFacInfo(&tBmsPackFacInfo);
    rt_memcpy( p, (BYTE*)(&tBmsPackFacInfo.acDeviceSn[0]), sizeof(tBmsPackFacInfo.acDeviceSn));
    return;
}
/****************************************************************************
* 函数名称  ：SetCsuSn
* 调    用  ：无
* 被 调 用  ：
* 输入参数  ：
* 返 回 值  ：
* 功能描述  ：设置监控序列号至flash
* 作    者  ：
* 设计日期  ：
* 修改记录  ：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
Static void SetCsuSn(BYTE *buff)
{
   BYTE i;
    T_BmsPACKFactoryStruct tBmsPackFacInfo;
    rt_memset(&tBmsPackFacInfo, 0, sizeof(tBmsPackFacInfo));

    readBmsPackFacInfo(&tBmsPackFacInfo);
   for (i = 0; i < sizeof(tBmsPackFacInfo.acDeviceSn); i++)
   {
   	tBmsPackFacInfo.acDeviceSn[i] = *(buff+i);
   }
	  rt_memcpy((BYTE *)(&tBmsPackFacInfo.acDeviceSn[0]), buff, sizeof(tBmsPackFacInfo.acDeviceSn));
    tBmsPackFacInfo.wCRC = CRC_Cal((BYTE*)&tBmsPackFacInfo, (sizeof(tBmsPackFacInfo)-2));
    //存储
    writeBmsPackFacInfo(&tBmsPackFacInfo);
}

void GetBmsSn(CHAR *p)
{
    T_BmsPACKFactoryStruct tBmsPackFacInfo;
    rt_memset(&tBmsPackFacInfo, 0, sizeof(tBmsPackFacInfo));

    readBmsPackFacInfo(&tBmsPackFacInfo);
    rt_memcpy( p, (BYTE*)&tBmsPackFacInfo, sizeof(tBmsPackFacInfo.acBmsFacSn));
}

Static void SetBmsSn(BYTE *buff)
{
    T_BmsPACKFactoryStruct tBmsPackFacInfo;
    rt_memset(&tBmsPackFacInfo, 0, sizeof(tBmsPackFacInfo));

    GetSysPara(&s_tSysPara);

    readBmsPackFacInfo(&tBmsPackFacInfo);
    rt_memcpy((BYTE *)(&tBmsPackFacInfo.acBmsFacSn[0]), buff, sizeof(tBmsPackFacInfo.acBmsFacSn));
    tBmsPackFacInfo.wCRC = CRC_Cal((BYTE *)&tBmsPackFacInfo, (sizeof(tBmsPackFacInfo) - 2));
    writeBmsPackFacInfo(&tBmsPackFacInfo);
    mbedtls_aes_update_key((unsigned char *)tBmsPackFacInfo.acBmsFacSn);

    // 密钥变更了，加密数据重新加密存储
    if (False == SetSysPara(&s_tSysPara, True, CHANGE_BY_APPTEST)) {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
    }
    return;
}

void SetBMSBattCap( BYTE ucPort )
{
	GetSysPara( &s_tSysPara );

	float_data.buff[0] = (char)s_tProtocol.aucRecBuf[7];
    float_data.buff[1] = (char)s_tProtocol.aucRecBuf[8];
    float_data.buff[2] = (char)s_tProtocol.aucRecBuf[9];
    float_data.buff[3] = (char)s_tProtocol.aucRecBuf[10];

    s_tSysPara.wBatteryCap = Float2Word(float_data.result);

    if ( False == SetSysPara( &s_tSysPara, True, CHANGE_BY_APPTEST ) )
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
    }

    //设置保存参数缺省值
    GetSysPara( &s_tSysPara );
    WriteBMSDefaultPara( &s_tSysPara );

	return;
}

void SetDelHisData( BYTE ucPort )
{
    BOOLEAN bAlarmEn = 0;
    BOOLEAN	bEventEn = 0;
    BOOLEAN	bDataEn  = 0;

    bAlarmEn = s_tProtocol.aucRecBuf[7];
    bEventEn = s_tProtocol.aucRecBuf[8];
    bDataEn  = s_tProtocol.aucRecBuf[9];

    if(bAlarmEn == True)
    {//删除历史告警
        DeleteHisAlarm();
    }

    if(bEventEn == True)
    {//删除历史事件
        DelAllHisAction();
    }

    if(bDataEn == True)
    {
        //删除历史数据
        DelAllHisData();
        //删除直流内阻记录
        DelAllDcrRecord();
        //删除直流内阻相关计数
        DelDcrCnt();
    }
	return;
}


void GetBDUFact(BYTE ucPort)
{
	T_DCFactory tBduFact;
	BYTE *p = NULL;
	BYTE  *pTemp = NULL;
    BYTE i = 0;
	WORD wYearOfVerDate = 0;

	GetBduFact(&tBduFact);
	
	p  = s_tProtocol.aucSendBuf;
	MemsetBuff(p, tBduFact.acSysName, 30, 32, 0x20);//SM软件名称
	pTemp = p;
    rt_memset_s(pTemp, HIGH_SEND_LEN, 0x20, 32);
    for(i = 0; i < sizeof(tBduFact.acSysName); i++)
    {
        if(tBduFact.acSysName[i] == 0x00)
        {
            break;
        }
        pTemp[i] = tBduFact.acSysName[i];
    }
	p += 32;

	MemsetBuff(p, tBduFact.acSoftVer, 6, 32, 0x20);//SM软件版本
	p += 32;

    wYearOfVerDate = (tBduFact.acVerDate[0] << 8) | tBduFact.acVerDate[1];
    PutInt16ToBuff(p, wYearOfVerDate); // BDU版本日期-年
    p += 2;
    *p++ = tBduFact.acVerDate[2]; // BDU版本日期-月
    *p++ = tBduFact.acVerDate[3]; // BDU版本日期-日

    *p++ = tBduFact.bHeatFilm; // 加热膜配置

    *p++ = tBduFact.bActivatePort; // 激活口配置

    *p++ = tBduFact.acHardwareVer; // 硬件版本号

    p += 25;


	/* LENID */
	s_tProtocol.wSendLenid  = (WORD) ((p-s_tProtocol.aucSendBuf)*2);

	return;
}

void GetTheftTest(BYTE ucPort)
{
	BYTE *p = NULL;

	p  = s_tProtocol.aucSendBuf;
	*p++ = getPortInput(Digit_THEFT_DET);

	/* LENID */
	s_tProtocol.wSendLenid  = (WORD) ((p-s_tProtocol.aucSendBuf)*2);

	return;
}


void SetEfficiencyTest(BYTE ucPort)
{
#if 0
	T_BduRealDataStruct tBduData;

	s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];
	GetBduRealData(&tBduData);

	if(tBduData.ucBduType != BDU_TYPE_COMMON_LI)
	{
		s_tProtocol.ucRTN  = RTN_INVALID_DATA;    //无效数据
        return;
	}
	if(s_tProtocol.ucCommandType == 0x80)
	{
		SetBduCtrl(BDU_DROP_CHG);
	}
	else if(s_tProtocol.ucCommandType == 0x81)
	{
		SetBduCtrl(BDU_COMM_CHG);
	}
	else if(s_tProtocol.ucCommandType == 0x82)
	{
		SetBduCtrl(BDU_DISCHG_START);
	}
	else
	{
		s_tProtocol.ucRTN  = RTN_WRONG_COMMAND;    // 命令格式错 
        return;
	}
	return;
#endif
}

//1363和apptest共用
void SendCellFactInfo(BYTE ucPort)
{
	BYTE *p = NULL;
	WORD wSendLen = 100;
	T_BmsPACKManufactStruct tPackInfo;

	rt_memset(&tPackInfo, 0, sizeof(T_BmsPACKManufactStruct));
	readPackManufact(&tPackInfo);
	p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

	MemsetBuff(p, (BYTE *)(&tPackInfo.acCellManufactruer[0]), 20, 20, 0x20);//电芯厂家名称
	p += 20;
	PutInt16ToBuff(p, (SHORT)(MIN(tPackInfo.tBattDate.wYear, INT16S_MAX))); //电芯出厂日期
	p +=2;
	*p++ = tPackInfo.tBattDate.ucMonth;
	*p++ = tPackInfo.tBattDate.ucDay;

    PutInt16ToBuff(p, (SHORT)(MIN(tPackInfo.wCellCycleTimes, INT16S_MAX))); // 电芯循环次数
	p += 2; 
	*p = 0x00;    //KW4
	s_tProtocol.wSendLenid = wSendLen;//为解决KW问题
	
}
//apptest使用
void RecCellFactInfo(BYTE ucPort)
{
	T_BmsPACKManufactStruct	tPackInfo;
	SHORT wTemp;

	rt_memset((BYTE*)&tPackInfo,0x00,sizeof(T_BmsPACKManufactStruct));
	s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];
	
	//190128先读取再设置
	readPackManufact(&tPackInfo);
	
	if (s_tProtocol.ucCommandType == 0x80)
	{
		rt_memcpy((BYTE *)&(tPackInfo.acCellManufactruer[0]), (BYTE *)(&s_tProtocol.aucRecBuf[8]), sizeof(tPackInfo.acCellManufactruer));
        if(!CheckCharRange((BYTE *)&tPackInfo.acCellManufactruer[0], sizeof(tPackInfo.acCellManufactruer)))
        {
            s_tProtocol.ucRTN   = RTN_INVALID_DATA;
            return;
        }
    }
	else if (s_tProtocol.ucCommandType == 0x81)
	{
		wTemp = Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+8));
		tPackInfo.tBattDate.wYear = (WORD)wTemp;
		tPackInfo.tBattDate.ucMonth = s_tProtocol.aucRecBuf[10];
		tPackInfo.tBattDate.ucDay = s_tProtocol.aucRecBuf[11];
        if(!CheckDateValid(&tPackInfo.tBattDate))
        {
            s_tProtocol.ucRTN   = RTN_INVALID_DATA;
            return;
        }
	}
	else if(s_tProtocol.ucCommandType == 0x82)
	{
	    tPackInfo.wCellCycleTimes = (WORD)(Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf + 8)));
		if(!CheckCellCycleTimesValid(tPackInfo.wCellCycleTimes))
		{
			s_tProtocol.ucRTN   = RTN_INVALID_DATA;
			return;
		}
		SetCellCycleTimes(tPackInfo.wCellCycleTimes);
	}
	else
	{
		s_tProtocol.ucRTN  = RTN_WRONG_COMMAND;
		return;
	}
			 	 
	tPackInfo.wCRC = CRC_Cal((BYTE*)&tPackInfo, (sizeof(tPackInfo)-2)); 		 
	writePackManufact(&tPackInfo);
	  
	return;
}

void GetBmsFactInfo(BYTE ucPort)
{
	BYTE *p = NULL;

	rt_memset(&s_tSysPara, 0, sizeof(T_SysPara));
	GetSysPara(&s_tSysPara);

	p = s_tProtocol.aucSendBuf;
	rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

	MemsetBuff(p, s_tSysPara.acBMSSysName, 20, 20, 0x20); //BMS系统名称
	p += 20;

	/* LENID */
	s_tProtocol.wSendLenid = (WORD)((p - s_tProtocol.aucSendBuf) * 2);
	return;
}

void SetBmsFactInfo(BYTE ucPort)
{
	BYTE aucBmsSysNameBuf[20] = {0};

	rt_memset(&s_tSysPara, 0, sizeof(T_SysPara));
	GetSysPara(&s_tSysPara);

	s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];

	if (s_tProtocol.ucCommandType == 0x80)
	{
		rt_memcpy(aucBmsSysNameBuf, (s_tProtocol.aucRecBuf + 8), 19);// 20字节BMS长度，19字节有效，最后1字节不允许写入。
        rt_memcpy(s_tSysPara.acBMSSysName, aucBmsSysNameBuf, 20);    // 20~32字节全部填充为0
	}

	else
	{
		s_tProtocol.ucRTN = RTN_WRONG_COMMAND; /* 命令格式错 */
		return;
	}

	if (False == SetSysPara(&s_tSysPara, True, CHANGE_BY_APPTEST))
	{
		s_tProtocol.ucRTN = RTN_INVALID_DATA;
	}

	return;
}

void SetBmsFactInfoNew(BYTE ucPort)
{
    // rt_memset(&s_tSysPara, 0, sizeof(T_SysPara));
    // GetSysPara(&s_tSysPara);

    // s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];

    // if (s_tProtocol.ucCommandType == 0x80)
    // {
    //     rt_memcpy(s_tSysPara.acBMSSysName, (s_tProtocol.aucRecBuf + 8), 31); // 32字节BMS长度，31字节有效，最后1字节不允许写入
    // }
    // else
    // {
    //     s_tProtocol.ucRTN = RTN_WRONG_COMMAND; /* 命令格式错 */
    //     return;
    // }

    // if (False == SetSysPara(&s_tSysPara, True, CHANGE_BY_APPTEST))
    // {
    //     s_tProtocol.ucRTN = RTN_INVALID_DATA;
    // }

    // return;
}

void GetBmsFactInfoNew(BYTE ucPort)
{
    // BYTE *p = NULL;

    // rt_memset(&s_tSysPara, 0, sizeof(T_SysPara));
    // GetSysPara(&s_tSysPara);

    // p = s_tProtocol.aucSendBuf;
    // rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

    // MemsetBuff(p, s_tSysPara.acBMSSysName, 32, 32, 0x20); //BMS系统名称
    // p += 32;

    // /* LENID */
    // s_tProtocol.wSendLenid = (WORD)((p - s_tProtocol.aucSendBuf) * 2);
    // return;
}

#if 0
void GetSleepStatus(BYTE ucPort)
{
  BYTE  *p = NULL;
	rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    p  = s_tProtocol.aucSendBuf;
	*p++ = IsSleep();
	s_tProtocol.wSendLenid = 2;
}
#endif


void GetBtnCount(BYTE ucPort)
{
    BYTE  *p = NULL;

	rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    p  = s_tProtocol.aucSendBuf;
	*p++ = GetSPowerBtnCount();
	*p++ = GetSWSleepBtnCount();
	*p = 0; //to solve KW4 problems
	s_tProtocol.wSendLenid = 4;
	return;
}

/****************************************************************************
* 函数名称：ApptestShutDown()
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：apptest模式下关机
* 作    者：
* 设计日期：2022-07-14
* 修改记录：
* 日    期              版      本              修改人          修改摘要
***************************************************************************/
Static void ApptestShutDown(void *para)
{
	uint8_t bOnOff = GPIO_OFF;
    ShutDownDevice(bOnOff);
}

void SetBMSShutdown(BYTE ucPort)
{
    GetSysPara(&s_tSysPara);
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    s_tProtocol.wSendLenid = 0;
#ifndef KW_CHECK
    if (RT_NULL == s_shutDownTimer)
    {
        s_shutDownTimer = rt_timer_create("shutdown",ApptestShutDown,RT_NULL,ONE_SECOND,RT_TIMER_FLAG_ONE_SHOT);
    }

    RETURN_IF_FAIL(s_shutDownTimer != RT_NULL);
    rt_timer_start(s_shutDownTimer);
#endif
}

#ifndef SHIELD_BALANCE_CIRCUIT_OPENALM
void StartBalanceCircCheck(BYTE ucPort)
{
    ClearBlanceCircuitStatus();
    CheckEqualCircuitFault();

    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    s_tProtocol.wSendLenid = 0;

    return;
}

void GetBalanceCircFault(BYTE ucPort)
{
    BYTE  *p = NULL;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    p  = s_tProtocol.aucSendBuf;
    *p++ = GetEqualCircuitFault(); //to realize
    *p = 0; //to solve KW4 problems
    s_tProtocol.wSendLenid = 2;

    return;
}
#endif

void PackInfoSync(void)
{
    T_BmsPACKManufactStruct	tPackInfo;
    //190128先读取再设置
    readPackManufact(&tPackInfo);
    //修改启用日期参数后同步到电芯厂家信息中
    if (rt_memcmp(&s_tSysPara.tEnableTime, &tPackInfo.tActiveDate, sizeof(T_DateStruct)) != 0)
    {
        rt_memcpy(&tPackInfo.tActiveDate, &s_tSysPara.tEnableTime, sizeof(T_DateStruct));
    }
    tPackInfo.wCRC = CRC_Cal((BYTE*)&tPackInfo, (sizeof(tPackInfo)-2));
    writePackManufact(&tPackInfo);
}

void RefreshDefaultPara(BYTE ucPort)
{
	GetSysDefaultPara(&s_tSysPara);
	PackInfoSync();
    if (False == SetSysPara(&s_tSysPara, True, CHANGE_BY_APPTEST))
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
        return;
    }
    deleteFile("/defaultpara");
    SaveAction(GetActionId(CONTOL_RST_PARA), "ApptestDefault Para");
	rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
	s_tProtocol.wSendLenid = 0;
	return;

}

void SetOpenCycleCtrl(BYTE ucPort)
{
	BYTE ucCtrlCode = 0;

	s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];
	ucCtrlCode = s_tProtocol.ucCommandType;

    if(ucCtrlCode == 0)
    {
		BduCtrl(SCI_CTRL_TEST, ENABLE);
		BduCtrl(SCI_CTRL_OPEN_LOOP, DISABLE);
	}
    else if(ucCtrlCode == 1)
    {
		BduCtrl(SCI_CTRL_TEST, ENABLE);
        BduCtrl(SCI_CTRL_OPEN_LOOP, ENABLE);
    }
    else
    {
         s_tProtocol.ucRTN  = RTN_WRONG_COMMAND;    // 命令格式错
         return;
    }

	return;
}

void GetBDUFactHeat(BYTE ucPort)
{
    BYTE *p = NULL;
    
    T_DCFactory tDCFact;
    rt_memset(&tDCFact, 0, sizeof(T_DCFactory));
    GetBduFact(&tDCFact);

    p = s_tProtocol.aucSendBuf;
	rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

    *p++ = tDCFact.bHeatFilm;
	s_tProtocol.wSendLenid = 2;

    *p = 0;

    return;
}

void SetHeatCtrl(BYTE ucPort)
{
    BYTE ucCtrlCode = 0;
    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];
    ucCtrlCode = s_tProtocol.ucCommandType;

    if(ucCtrlCode == 0)
    {
		SetHeaterTestCtrl(HEATER_TEST_OFF);
    }
    else if(ucCtrlCode == 1)
    {
		SetHeaterTestCtrl(HEATER_TEST_ON);
    }
    else
    {
        s_tProtocol.ucRTN  = RTN_WRONG_COMMAND;    // 命令格式错
        return;
    }

    return;
}

 void GetBduIdNo(BYTE ucPort)
{
    BYTE  *p = NULL;
    T_DCFactory tBduFactInfo;
	GetBduFact(&tBduFactInfo);

	rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    p  = s_tProtocol.aucSendBuf;

    PutInt32ToBuff(p, tBduFactInfo.ulSN);
    p += 4;
	s_tProtocol.wSendLenid = 8;
	*p = 0;  //解决KW4级问题添加 对业务无影响 1106
	return;
}

void SetBduIdNo(BYTE ucPort)
{
//	UNS_32 uBduSn;
//	uBduSn = GetULongData(&s_tProtocol.aucRecBuf[7]);
//	SetBduSerialNo(uBduSn);
	return;
}


void GetBDPBSn(BYTE ucPort)
{
	BYTE  *p = NULL;
	T_BDPBSnStruct tBDPBSnInfo;
	LONG lBDPBSnReadRtn;

	rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
	rt_memset(&tBDPBSnInfo, 0, sizeof(T_BDPBSnStruct));
	lBDPBSnReadRtn = readFile("/BDPBSn", (BYTE*)&tBDPBSnInfo, sizeof(T_BDPBSnStruct));
#ifndef KW_CHECK
    if ( CRC_Cal((BYTE*)&tBDPBSnInfo, sizeof(T_BDPBSnStruct)) && (lBDPBSnReadRtn == 0) )
    {
        rt_memset(&tBDPBSnInfo, 0, sizeof(T_BDPBSnStruct));
    }
#endif
    p  = s_tProtocol.aucSendBuf;
	rt_memcpy(p, &tBDPBSnInfo.acBDPBSn[0], 15);
	p += 15;
	s_tProtocol.wSendLenid = 30;
	*p = 0;  //解决KW4级问题添加 对业务无影响 1106
	return;
}

void SetBDPBSn(BYTE ucPort)
{	
	T_BDPBSnStruct tBDPBSnInfo;
	BYTE i;
	rt_memset(&tBDPBSnInfo, 0, sizeof(T_BDPBSnStruct));

	//对设置的序列号进行校验，以免设置后出现乱码 1105 mwl
	if(CheckSn(&s_tProtocol.aucRecBuf[7], 15) == FALSE)
	{
		s_tProtocol.ucRTN  = RTN_INVALID_DATA;    //无效数据
        return;
	}
	for(i = 0; i < sizeof(tBDPBSnInfo.acBDPBSn); i++)
	{
		tBDPBSnInfo.acBDPBSn[i] = s_tProtocol.aucRecBuf[7 + i];
	}
	tBDPBSnInfo.wCheckSum = CRC_Cal((BYTE*)&tBDPBSnInfo, (sizeof(T_BDPBSnStruct)-2));
	writeFile("/BDPBSn", (BYTE*)&tBDPBSnInfo, sizeof(T_BDPBSnStruct));

	return;
}

void GetMainSn(BYTE ucPort)
{
	BYTE  *p = NULL;
    T_DCFactory tBduAsset;
	GetBduFact(&tBduAsset); //后面SCI协议会把获取资产信息接口合入到获取厂家信息中
	rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));
	p  = s_tProtocol.aucSendBuf;
    rt_memcpy_s(p, HIGH_SEND_LEN, tBduAsset.acAsset, 15);
	p += 15;
	s_tProtocol.wSendLenid = 30;
	*p = 0;  //解决KW4级问题添加 对业务无影响 1106
	return;
}

void SetMainSn(BYTE ucPort)
{
	BYTE i;
	T_DCAsset tBduAsset;
	T_DCFactory tBduFact;

	GetBduFact(&tBduFact);

	if(CheckSn(&s_tProtocol.aucRecBuf[7], 15) == FALSE)
	{
		s_tProtocol.ucRTN  = RTN_INVALID_DATA;    //无效数据
        return;
	}
	rt_memcpy(&tBduAsset.acAssetInfo[0], &tBduFact.acAsset[0], sizeof(tBduAsset.acAssetInfo));
	for(i = 0; i < 15; i++)
	{
		tBduAsset.acAssetInfo[i] = s_tProtocol.aucRecBuf[7 + i];
	}
	SetBduAsset(&tBduAsset);
	return;
	
}

void GetBMSBattCap(BYTE ucPort)
{
	BYTE *p = NULL;
	rt_memset(&s_tSysPara, 0, sizeof(T_SysPara));    
    GetSysPara(&s_tSysPara);
	p = s_tProtocol.aucSendBuf;
	rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
	*(FLOAT*)p = s_tSysPara.wBatteryCap;
	p += 4;
	*p =0;
	 /* LENID */
    s_tProtocol.wSendLenid  = (WORD) ((p-s_tProtocol.aucSendBuf)*2);
	return;
}


void SetBMSReleaseLock(BYTE ucPort)
{
//    SetBduReleaseLock();
    s_tProtocol.ucRTN = RTN_CORRECT;
    BduCtrl(SCI_CTRL_BDULOCK, DISABLE);
    /* LENID */
    s_tProtocol.wSendLenid  = 0;
    return;
}

void GetBMSChgVolt(BYTE ucPort)
{
    BYTE *p = NULL;
    WORD wSendLen = 0;
    T_DCPara tDCPara;
    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }
    rt_memset(&tDCPara, 0, sizeof(T_DCPara));    
    GetBduPara(&tDCPara);
    p = s_tProtocol.aucSendBuf;
    *p++ = GetDataFlag( BCM_ALARM, ucPort );
    wSendLen++;
    *(SHORT*)p = Host2Modbus((SHORT*)&tDCPara.wChgVolVal);
    p += 2;
    wSendLen += 2;

    /* LENID */
    s_tProtocol.wSendLenid = wSendLen*2;
    return;
}

// 获取硬件板卡参数
void GetBMSHardwarePara(BYTE ucPort)
{
    BYTE *p = NULL;
    WORD wSendLen = 0;
    T_HardwareParaStruct tHardwarePara;

    rt_memset(&tHardwarePara, 0, sizeof(T_HardwareParaStruct)); 
    readBmsHWPara(&tHardwarePara);
    p  = s_tProtocol.aucSendBuf;

    *p++ = tHardwarePara.ucCellVoltNum;    // 单节电压检测数量
    *p++ = tHardwarePara.ucCellTempNum;    // 单体温度检测数量
    wSendLen += 2;
    s_tProtocol.wSendLenid = wSendLen * 2;	
}

// 设置硬件板卡参数
void SetBMSHardwarePara(BYTE ucPort)
{
    BYTE ucData;
    T_HardwareParaStruct tHardwarePara;

    readBmsHWPara(&tHardwarePara);
    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];
    ucData = s_tProtocol.aucRecBuf[8];

    if( (s_tProtocol.ucCommandType >= 0x80)
     && (s_tProtocol.ucCommandType <= 0x81) )
    {
        switch(s_tProtocol.ucCommandType)
        {
            case 0X80: // 设置单节电压检测数量
                if(ucData > 24 || ucData < 4) // 限制设置范围
                {
                    s_tProtocol.ucRTN  = RTN_INVALID_DATA;
                }
                else
                {
                    tHardwarePara.ucCellVoltNum = ucData;
                }
                break;
            case 0X81: // 设置单体温度检测数量
                if(ucData > 20 || ucData < 4) // 限制设置范围
                {
                    s_tProtocol.ucRTN  = RTN_INVALID_DATA;
                }
                else
                {
                    tHardwarePara.ucCellTempNum = ucData;
                }
                break;
            default:
                s_tProtocol.ucRTN  = RTN_INVALID_DATA;
                break;
        }
    }
    else
    {
        s_tProtocol.ucRTN  = RTN_WRONG_COMMAND;    // 命令格式错
    }

    if((s_tProtocol.ucRTN != RTN_INVALID_DATA)&&(s_tProtocol.ucRTN != RTN_WRONG_COMMAND))
    {
        writeBmsHWPara(&tHardwarePara);
    }
}

void GetHWMacAddr(BYTE ucPort)
{
    BYTE *p = NULL;
    WORD wSendLen = 0;
    BYTE aucMacAddr[6] = {0};
    BYTE i = 0;

    p  = s_tProtocol.aucSendBuf;
#if !defined(KW_CHECK)
    ETH_dev = rt_device_find("e0");
    if (RT_NULL == ETH_dev)
    {
        return;
    }
    rt_device_control( ETH_dev , NIOCTL_GADDR ,aucMacAddr);
#endif
    for( i = 0 ; i < 6 ; i ++ )
    {
        *p++ = aucMacAddr[i];
    }
    wSendLen += 6;
    s_tProtocol.wSendLenid = wSendLen * 2;
}

void SetHWMacAddr(BYTE ucPort)
{
    BYTE i = 0;
    T_NetMacStruct tNetMacPara;

    for( i = 0 ; i < 6 ; i ++ )
    {
        tNetMacPara.aucMacAddr[i] = s_tProtocol.aucRecBuf[7+i];
    }
    tNetMacPara.ucMacFlag = 0X55; // MAC已更新标记
    writeNetMacPara(&tNetMacPara);

    LoadNetPara();
}

void SetNetTest(BYTE ucPort)
{
    BYTE ucNetFlag = 0;

    ucNetFlag = s_tProtocol.aucRecBuf[7];
    if(ucNetFlag == 1)
    {
        s_ucRunFlag = True;
#if !defined(KW_CHECK)
        if (NetTaskItem == RT_NULL)
        {
            NetTestOnOff(1); //开启TCP服务
            NetTaskItem = rt_thread_create("NetApptest", Process_NET_Comm, RT_NULL, 4096, 15, 5);
            if (NetTaskItem != RT_NULL)
            {
                rt_thread_startup(NetTaskItem);
            }
        }
#endif
    }
    else if(ucNetFlag == 0)
    {
        s_ucRunFlag = False;
    }
}

// 网口任务：在RS485中通过apptest启动网口功能，网口生效对apptest功能的支持。
// 通过apptest协议可以开启或者关闭TCP服务或者在任何情况下退出apptest模式则TCP服务关闭。
// 单板模式：TCP_Server,端口号：4000
Static void Process_NET_Comm(void* parameter)
{
#if !defined(KW_CHECK)
    ULONG ulSinSize;
    LONG lBytesReceived;
    UNUSED size_t len;
    struct sockaddr_in client_addr;
    T_CommStruct tCommNet;
    while (1)
    {
        ulSinSize = sizeof(struct sockaddr_in);
        s_lConnected = accept(s_lSock, (struct sockaddr *) &client_addr, &ulSinSize);
		if(s_lConnected < 0)
		{
			continue;
		}
        rt_kprintf("I got a connection from (%s , %d)\n", inet_ntoa(
        client_addr.sin_addr), ntohs(client_addr.sin_port));
        while (1)
        {
            if(s_ucRunFlag != True)
            {
                //rt_thread_delay(1000);
                continue;
            }
            //rt_thread_delay(100);
            lBytesReceived = recv(s_lConnected, &tCommNet.aucRecBuf[0], LEN_COMM_REC_BUF, 0);

            if (lBytesReceived < 0)
            {
                lwip_close(s_lConnected);
                break;
            }
            else if (lBytesReceived == 0)
            {
                rt_kprintf("\nReceived warning,recv function return 0.\r\n");
            }
            else
            {
                // 处理接收的数据
//                if((isValidApptestByte(&tCommNet,lBytesReceived))&&(s_ucRunFlag))
                if(isValidApptestByte(&tCommNet,lBytesReceived))
                {
                    tCommNet.ucPortType = COMM_NET;
                    tCommNet.wSendLength = 0;
                    Deal1363CommData(&tCommNet);
                    len = send(s_lConnected, tCommNet.aucSendBuf, tCommNet.wSendLength, 0);
                }
            }
        }
    }
#endif
}

Static BOOLEAN NetTestOnOff(BYTE ucState)
{
#if !defined(KW_CHECK)
    struct sockaddr_in server_addr;

    if(ucState > 1)
    {
        return 0;
    }

    if(ucState == 1) // 开启网口服务
    {
        if ((s_lSock = socket(AF_INET, SOCK_STREAM, 0)) == -1)
        {
            rt_kprintf("Socket error\n");
            return 0;
        }

        server_addr.sin_family = AF_INET;
        server_addr.sin_port = htons(4000);
        server_addr.sin_addr.s_addr = INADDR_ANY;
        rt_memset(&(server_addr.sin_zero), 8, sizeof(server_addr.sin_zero));
        if (bind(s_lSock, (struct sockaddr *) &server_addr, sizeof(struct sockaddr)) == -1)
        {
            rt_kprintf("Unable to bind\n");
            return 0;
        }

        if (listen(s_lSock, 5) == -1)
        {
            rt_kprintf("Listen error\n");
            return 0;
        }

        rt_kprintf("\nTCPServer Waiting for client on port 4000...\n");
    }
    else // 关闭网口服务
    {
        lwip_close(s_lSock);
        rt_kprintf("\nTCPServer Close!\n");
    }
#endif
    return 1;
}

Static BOOLEAN isValidApptestByte(T_CommStruct *ptComm, BYTE ucRevLen)
{
	#if !defined(KW_CHECK)
	BYTE i = 0;
    BYTE ucRevByte = 0;

   if (SOI == ptComm->aucRecBuf[0])
   {
       ptComm->bRecReady = True;
       ptComm->wRecIndex = 1;
       ptComm->ucPrtclType = PROTOCOL_1363;
   }

   for( i = 1; i < ucRevLen - 1; i++ )
   {
        ucRevByte = ptComm->aucRecBuf[i];
        if ( (ucRevByte >= '0' && ucRevByte <= '9') || (ucRevByte >= 'A' && ucRevByte <= 'F') )
        {
        }
        else
        {
            return False;
        }
   }

   if (EOI == ptComm->aucRecBuf[ucRevLen - 1])
   {
       ptComm->bRecOk = True;
       ptComm->wRecIndex = ucRevLen;
       ptComm->wRecLength = ptComm->wRecIndex;
       return True;
   }
   return False;
   #endif
}

void SetFacTime(BYTE ucPort)
{
    T_BmsPACKFactoryStruct  tBmsPACKFactory;
	BYTE *p = s_tProtocol.aucRecBuf + 7;
	
    readBmsPackFacInfo(&tBmsPACKFactory);
    //出厂时间
    tBmsPACKFactory.tFactoryTime.wYear = p[0] * 256 + p[1];
    p += 2;
    tBmsPACKFactory.tFactoryTime.ucMonth = *(p++);
    tBmsPACKFactory.tFactoryTime.ucDay = *(p++);
    tBmsPACKFactory.tFactoryTime.ucHour = *(p++);
    tBmsPACKFactory.tFactoryTime.ucMinute = *(p++);
    tBmsPACKFactory.tFactoryTime.ucSecond = *(p++);
    if (CheckTimeValid(&tBmsPACKFactory.tFactoryTime) == False)
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
        return;
    }
    tBmsPACKFactory.wCRC = CRC_Cal((BYTE*)&tBmsPACKFactory, (sizeof(tBmsPACKFactory)-2));
	
	writeBmsPackFacInfo(&tBmsPACKFactory);

}

void SetFirstBootTime(BYTE ucPort)
{
    T_BmsPACKFactoryStruct  tBmsPACKFactory;
	BYTE *p = s_tProtocol.aucRecBuf + 7;
	
    readBmsPackFacInfo(&tBmsPACKFactory);
    //第一次开机时间
    tBmsPACKFactory.tFirstBootTime.wYear = p[0] * 256 + p[1];
    p += 2;
    tBmsPACKFactory.tFirstBootTime.ucMonth = *(p++);
    tBmsPACKFactory.tFirstBootTime.ucDay = *(p++);
    tBmsPACKFactory.tFirstBootTime.ucHour = *(p++);
    tBmsPACKFactory.tFirstBootTime.ucMinute = *(p++);
    tBmsPACKFactory.tFirstBootTime.ucSecond = *(p++);
    if (CheckTimeValid(&tBmsPACKFactory.tFirstBootTime) == False)
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
        return;
    }
    tBmsPACKFactory.wCRC = CRC_Cal((BYTE*)&tBmsPACKFactory, (sizeof(tBmsPACKFactory)-2));
	
	writeBmsPackFacInfo(&tBmsPACKFactory);

}

void GetFacTime(BYTE ucPort)
{
    WORD wSendLen = 0;
    BYTE *p = NULL;
	T_BmsPACKFactoryStruct  tBmsPACKFactory;

	readBmsPackFacInfo(&tBmsPACKFactory);
	p  = s_tProtocol.aucSendBuf;
    p += PutTimeStruct2Buff(p , tBmsPACKFactory.tFactoryTime);      //出厂时间
    wSendLen += 7;
    s_tProtocol.wSendLenid = wSendLen * 2;//为解决KW问题
    *p = 0;   // 解决kw
}

void GetFirstBootTime(BYTE ucPort)
{
    WORD wSendLen = 0;
    BYTE *p = NULL;
	T_BmsPACKFactoryStruct  tBmsPACKFactory;

	readBmsPackFacInfo(&tBmsPACKFactory);
	p  = s_tProtocol.aucSendBuf;
    p += PutTimeStruct2Buff(p , tBmsPACKFactory.tFirstBootTime);    //第一次开机时间
    wSendLen += 7;
    s_tProtocol.wSendLenid = wSendLen * 2;//为解决KW问题
    *p = 0;   // 解决kw
}

void SetBduAppTestPara(BYTE ucPort)
{
    BYTE ucData;
    T_DCTestPara tDCTestPara;

    GetBduTestPara(&tDCTestPara);
    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];
    ucData = s_tProtocol.aucRecBuf[8];

    switch (s_tProtocol.ucCommandType)
    {
        case 0X80: // 母排继电器驱动检测控制
            BduCtrlByType(s_tProtocol.ucCommandType, SCI_CTRL_TEST_RELAY, ucData);
            break;
        case 0X81: // 检测母排继电器驱动选择
            BduCtrlByType(s_tProtocol.ucCommandType, SCI_CTRL_TEST_RELAY, ucData);
            break;
        case 0X82: // 逐波保护检测控制
            BduCtrlByType(s_tProtocol.ucCommandType, SCI_CTRL_TEST_WAVE_PRT, ucData);
            break;
        case 0X83: // 设置充电逐波保护点
            tDCTestPara.sWavePointChg = Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8));
            if(fabs(tDCTestPara.sWavePointChg) > WAVE_POINT_CHG_LIMIT)
            {
                s_tProtocol.ucRTN = RTN_INVALID_DATA;
                return;
            }
            SetBduTestPara(&tDCTestPara);
            break;
        case 0X84: // 设置放电逐波保护点
            tDCTestPara.sWavePointDischg = Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8));
            if(fabs(tDCTestPara.sWavePointDischg) > WAVE_POINT_DISCHG_LIMIT)
            {
                s_tProtocol.ucRTN = RTN_INVALID_DATA;
                return;
            }
            SetBduTestPara(&tDCTestPara);
            break;
        case 0x85: // 休眠控制
            BduCtrlByType(s_tProtocol.ucCommandType, SCI_CTRL_SLEEP, ucData);
            break;
        #ifdef BATT_CONTACTOR_TEST
        case 0x86:
            BduCtrlByType(s_tProtocol.ucCommandType, SCI_CTRL_TEST_RELAY, ucData);
            break;
        #endif
        default:
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND; // 命令格式错
            break;
    }
    return;
}

Static BOOLEAN BduCtrlByType(BYTE ucCommandType, BYTE ucCmd, BYTE ucData)
{
    BYTE ucCtrlCode = ucData;
    if (ucData != 0x00 && ucData != 0x01)
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
        return FAILURE;
    }

    if (ucCommandType == 0X81)
    {
        ucCtrlCode = (ucData == 0x00) ? CMD_BUS_DRIVE1_START : CMD_BUS_DRIVE2_START;
    }
    else if (ucCommandType == 0x86)
    {
        ucCtrlCode = (ucData == 0x00) ? CMD_CONNECT_BATT_CONTACTOR : CMD_DISCONNECT_BATT_CONTACTOR;
    }

    BduCtrl(ucCmd, ucCtrlCode);
    return SUCCESSFUL;

}

void SetBmsInfo(BYTE ucPort)
{
    T_BmsInfoStruct  tBmsInfo = {0};

    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];

    readBMSInfofact(&tBmsInfo);

    if (s_tProtocol.ucCommandType == 0x80)
    {
        rt_memcpy_s((BYTE *)&(tBmsInfo.acBattCorpName[0]), sizeof(tBmsInfo.acBattCorpName), (BYTE *)(&s_tProtocol.aucRecBuf[8]), sizeof(tBmsInfo.acBattCorpName));
        if(!CheckCharRange((BYTE *)&tBmsInfo.acBattCorpName[0], sizeof(tBmsInfo.acBattCorpName)))
        {
            s_tProtocol.ucRTN   = RTN_INVALID_DATA;
            return;
        }
    }
    else if (s_tProtocol.ucCommandType == 0x81)
    {
        rt_memcpy_s((BYTE *)&(tBmsInfo.acNameLite[0]), sizeof(tBmsInfo.acNameLite), (BYTE *)(&s_tProtocol.aucRecBuf[8]), sizeof(tBmsInfo.acNameLite));
        if(!CheckCharRange((BYTE *)&tBmsInfo.acNameLite[0], sizeof(tBmsInfo.acNameLite)))
        {
            s_tProtocol.ucRTN   = RTN_INVALID_DATA;
            return;
        }
    }
    else
    {
        s_tProtocol.ucRTN  = RTN_WRONG_COMMAND;
        return;
    }

    writeBMSInfofact(&tBmsInfo);

    return;
}

void GetBmsInfo(BYTE ucPort)
{
    T_BmsInfoStruct  tBmsInfo = {0};
    BYTE *p = NULL;

    readBMSInfofact(&tBmsInfo);

    p = s_tProtocol.aucSendBuf;
    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0x00, sizeof(s_tProtocol.aucSendBuf));

    MemsetBuff(p, tBmsInfo.acBattCorpName, 20, 20, 0x20);         // BMS厂家信息
    p += 20;

    MemsetBuff(p, tBmsInfo.acNameLite, 10, 10, 0x20);             // 短称
    p += 10;

    /* LENID */
    s_tProtocol.wSendLenid = 220;                  //定长110字节*2
    return;
}


void GetHardwareInfo(BYTE ucPort)
{
    BYTE *p = NULL;
    T_BmsInfoStruct tBmsInfo;
    rt_memset_s(&tBmsInfo, sizeof(T_BmsInfoStruct), 0x00, sizeof(T_BmsInfoStruct));

    readBMSInfofact(&tBmsInfo);
    p = s_tProtocol.aucSendBuf;
    rt_memset_s(p, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));

    rt_memcpy_s(p, LENGTH_HARDWARE_INFORMATION, tBmsInfo.acHardwareInfo, LENGTH_HARDWARE_INFORMATION);

    p += 20;

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    return;
}



void SetHardwareInfo(BYTE ucPort)
{
    T_BmsInfoStruct tBmsInfo;
    rt_memset_s(&tBmsInfo, sizeof(T_BmsInfoStruct), 0x00, sizeof(T_BmsInfoStruct));

    readBMSInfofact(&tBmsInfo);

    rt_memcpy_s(tBmsInfo.acHardwareInfo, LENGTH_HARDWARE_INFORMATION, s_tProtocol.aucRecBuf + 7, LENGTH_HARDWARE_INFORMATION);

    if(!CheckCharRange((BYTE *)&tBmsInfo.acHardwareInfo[0], sizeof(tBmsInfo.acHardwareInfo)))
    {
        s_tProtocol.ucRTN  = RTN_INVALID_DATA;
        return;
    }

    writeBMSInfofact(&tBmsInfo);

    return;
}

void Get4GModeRealData(BYTE ucPort)
{
    BYTE *p = NULL;
    WORD wLen = 0;
    T_Mg21Status tMg21Data;

    rt_memset_s(&tMg21Data, sizeof(T_Mg21Status),0, sizeof(T_Mg21Status));

    getMg21Status(&tMg21Data);
    p = s_tProtocol.aucSendBuf;
    rt_memset_s(p, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));

    wLen = 24; //使用4个字节，预留20个字节

    *p++ = tMg21Data.bSIMCardOk;  //SIM卡状态 0：插入  1：未插入
    *p++ = tMg21Data.ucSignalQuality; // 4G信号强度
    *p++ = tMg21Data.ucStarNum;   //卫星数量
    *p++ = tMg21Data.ucSNR;   //信噪比

    s_tProtocol.wSendLenid = wLen*2;
 
    return;
}