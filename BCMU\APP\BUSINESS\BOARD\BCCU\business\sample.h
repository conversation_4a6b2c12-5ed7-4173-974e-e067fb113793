/**
 * @brief
 */

#ifndef _AIRSWITCH_SAMPLE_H_
#define _AIRSWITCH_SAMPLE_H_

#ifdef __cplusplus
extern "C" {
#endif

#define TEMP_ADC_DEV_NAME  "adc0"

/* 温度采样片选引脚 */
typedef enum{
	VTA =  0,
	VT0,
}adc_sample_channel;

#define SAMPLE_THREAD_DELAY_TIME 400 // sample线程延时时间400ms

#define LOW_TEMP  (-40)         ///< 电芯温度下界
#define HIGH_TEMP 120           ///< 电芯温度上界
#define TEMP_INVALID_LOW (-75)  ///< 下界温度无效值
#define TEMP_INVALID_HIGH 125   ///< 上界温度无效值

void* sample_init_sys(void *param);
void sample_main(void* parameter);
#ifdef __cplusplus
}
#endif      //< __cplusplus

#endif      //< __AIRSWITCH_SAMPLE_H_
