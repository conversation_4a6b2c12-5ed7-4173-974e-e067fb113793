#ifndef _DEV_DC_AC_MODBUS_H
#define _DEV_DC_AC_MODBUS_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */


#include "device_type.h"
#include "cmd.h"
#include "linklayer.h"
#include "protocol_layer.h"
#include "north_data_utils.h"

#define PV_MODBUS_ADDR_NUM  10 ///< 逆变MODBUS支持个数, 地址范围1~10

/* 缓冲区长度 */
#define R_BUFF_LEN_2048                    2048       ///<  接收缓冲区长度
#define S_BUFF_LEN_2048                    2048      ///<  发送缓冲区长度
//iv数据
#define IV_DATA_HEAD_LEN                   11
#define IV_DATA1_READ_OFFSET               11
#define IV_DATA2_READ_OFFSET               211
#define IV_DATA3_READ_OFFSET               411
#define GET_IV_DATA_PARA_NUM               100

// inverter_modbus命令一标识ID 定义
#define  CMD_INVERTER_MODBUS_GET_ANA              0x01 ///<  获取模拟量
#define  CMD_INVERTER_MODBUS_SET_PARA             0x03 ///<  获取参数量
#define  CMD_INVERTER_MODBUS_GET_PARA             0x04 ///<  设置参数量
#define  CMD_INVERTER_MODBUS_CTRL_CMD             0x06 ///<  控制命令


#define GET_ANA_DATA        0x04            //<获取模拟量、状态量、版本信息
#define GET_PARA_DATA       0x03            //<获取参数量
#define SET_PARA_DATA       0x10            //<设置参数量


#define PV_INVERTER_MODBUS_SERVER_ID          NORTH_SERVER + 4

#define NO_MATCH_REGISTER_ADDR -1

#define DATA_LEN_2          2//数据长度为2
#define DATA_LEN_4          4 //数据长度为4
#define DATA_LEN_6          6 //数据长度为6

#define TAR_VER_START_INDEX  12//csc,psc大版本号保存操作记录的index
#define TAR_VER_LEN         (32-TAR_VER_START_INDEX)

#pragma pack(1)
typedef struct{
    unsigned short reg_addr;
    unsigned int ctrl_id;
    unsigned short ctrl_status;
    unsigned char  ctrl_cmd_id;
    char  info[20];
    void (*func)();
}ctrl_cmd_modbus_t;

typedef struct 
{
    char src_type;
    char dst_type;
    int (*pack_fun)(int* index , unsigned char* cmd_buff ,  int* reg_num , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map);
}pack_fun_map_t;

typedef struct 
{
    char src_type;
    char dst_type;
    int (*parse_func)(int* index , unsigned char* cmd_buff , int* data_value_index ,int percision , int* reg_nums, modbus_addr_map_data_t* data_map);
}parse_fun_map_t;

typedef struct {
    time_base_t time;
    unsigned char block_id;
    unsigned char precision ;
    unsigned short para_num;
}iv_data_head_t;

typedef struct{

    unsigned short      reg_addr;//寄存器地址
    unsigned char       block_id;//块id
    unsigned short      need_para_num;//文件中至少需要的参数个数
    unsigned short      data_offset;//文件读取位置
}modbus_reg_map_iv_data_t;


typedef struct{
    int is_grid_code_flag;
    int need_send_alarm_msg;
    int is_uart_flag;
    int is_delay_update;
}special_op_flag_t;
#pragma pack()
dev_type_t* init_dev_inverter_modbus_main(void);
int parse_inverter_para_data(void* dev_inst, void *cmd_buf);
int parse_start_addr_and_reg_nums(void* dev_inst, void* cmd_buf);
int pack_inverter_ana_or_para_data(void* dev_inst, void* cmd_buf);
char set_para_signal_data(int dest_type,void *data,int index);
void modbus_register_cmd_table();
void numchar_to_bit_to_int16(unsigned char* data_buff, int total_len, int* index, int percision,modbus_addr_map_data_t* data_map);
int find_register_start_addr_index(int reg_addr);
char check_para_reserve_flag(int type , int index , unsigned char *data_buff , int data_valude_indx );
int pack_modbus_control_cmd(void* dev_inst, void* cmd_buff);
int parse_modbus_control_cmd(void* dev_inst, void* cmd_buff);
void get_time_set_sid_value();
int set_sys_time(unsigned char* data_buff, int data_valude_indx, int index);
int is_contain_time(int index , int set_flag, modbus_addr_map_data_t* data_map);
int is_contain_grid_code(int index, modbus_addr_map_data_t* data_map);
void save_ctrl_cmd_event(unsigned int ctrl_id);
int deal_get_iv_data(cmd_buf_t* cmd_buff, signed short reg_addr);
int is_contain_uart_conf(int index, modbus_addr_map_data_t* data_map );
void set_uart_conf(int flag);
void check_ana_reserve_flag(int index , void* data,modbus_addr_map_data_t* data_map);
char set_data_by_id_type(int index , void* data, modbus_addr_map_data_t* data_map);
void get_data_by_id_type(int index , void* data, modbus_addr_map_data_t* data_map);
int is_contain_alarm_para_addr(int index, modbus_addr_map_data_t* data_map);
int is_contain_delay_update(int index, modbus_addr_map_data_t* data_map);

/**************************************打包函数*****************/
int floattoint32u(int* index , unsigned char* data_buff , int* reg_num , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map);
int floattoint32s(int* index , unsigned char* data_buff , int* reg_num , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map);
int floattoint16u(int* index , unsigned char* data_buff , int* reg_num , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map);
int floattoint16s(int* index , unsigned char* data_buff , int* reg_num , int data_len ,  int percision , int total_len, modbus_addr_map_data_t* data_map);
int int16utoint16u(int* index , unsigned char* data_buff, int* reg_num , int data_len , int percision , int total_len, modbus_addr_map_data_t* data_map);
int int16stoint16s(int* index , unsigned char* data_buff, int* reg_num , int data_len , int percision , int total_len, modbus_addr_map_data_t* data_map);
int int32utoint32u(int* index , unsigned char* data_buff, int* reg_num , int data_len , int percision , int total_len, modbus_addr_map_data_t* data_map);
int stringtostring(int* index , unsigned char* data_buff, int* reg_num , int data_len , int percision , int total_len, modbus_addr_map_data_t* data_map);
int datetoint16u(int* index , unsigned char* data_buff, int* reg_num , int data_len , int percision , int total_len, modbus_addr_map_data_t* data_map);
int chartobit(int* index , unsigned char* data_buff , int* reg_num , int data_len , int percision , int total_len, modbus_addr_map_data_t* data_map);

/*******************************解包函数*****************/
int parse_int16utofloat( int* index , unsigned char* data_buff , int* data_valude_index ,int percision , int* reg_nums, modbus_addr_map_data_t* data_map);
int parse_int16stofloat( int* index , unsigned char* data_buff , int* data_valude_index ,int percision , int* reg_nums, modbus_addr_map_data_t* data_map );
int parse_int32utofloat( int* index , unsigned char* data_buff , int* data_valude_index , int percision, int* reg_nums, modbus_addr_map_data_t* data_map);
int parse_int32stofloat( int* index , unsigned char* data_buff , int* data_valude_index ,int percision, int* reg_nums, modbus_addr_map_data_t* data_map);
int parse_int16utoint16u( int* index , unsigned char* data_buff , int* data_valude_index ,int percision , int* reg_nums, modbus_addr_map_data_t* data_map);
int parse_int32utoint32u( int* index , unsigned char* data_buff , int* data_valude_index,int percision, int* reg_nums, modbus_addr_map_data_t* data_map);
int parse_stringtostring( int* index , unsigned char* data_buff , int* data_valude_index ,int percision, int* reg_nums, modbus_addr_map_data_t* data_map);


int new_pack_data_to_buff(void* cmd_buff , int* index, int offset_value, modbus_addr_map_data_t* data_map);
int new_pack_ana_para_data(void* dev_inst, void* cmd_buff);
int new_parse_para_data_from_buff(void* dev_inst, void* cmd_buff);

void stop_power();
void start_power();
int judge_special_ctrl_cmd();
int set_reg_nums(short input_reg_nums);
int set_reg_addr(short input_reg_addr);
int find_register_start_addr_index_universal(int reg_addr,int table_size,modbus_addr_map_data_t* data_map);
int check_time_validity(time_base_t* tm);
int get_iv_scan_data(unsigned char* read_data,unsigned int read_offset, unsigned short para_num,int loop);
void init_read_data(unsigned char* read_data,unsigned int read_len);
int deal_special_para(int index , void* data, modbus_addr_map_data_t* data_map);
int send_process_msg(void* cmd_buff, int sid_num, special_op_flag_t* special_op_flag);
int special_op(int index, int data_valude_indx, unsigned char *data_buff, special_op_flag_t* special_op_flag);
int ctrl_power_on_off(int index, unsigned char* data_buff, int data_valude_indx);
int pack_set_para_data(void* dev_inst, void* cmd_buff);
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  // 
