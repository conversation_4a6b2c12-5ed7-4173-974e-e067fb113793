/**
 * @brief
 */

#ifndef _AIRSWITCH_SAMPLE_H_
#define _AIRSWITCH_SAMPLE_H_

#ifdef __cplusplus
extern "C" {
#endif

typedef unsigned char (*FunctionPointerJudge)(void);
typedef enum{
    VTA =  0,
    VTB,
    VTC,
    ADDX,
    ADDY,
    VOUT,
}adc_sample_channel;

typedef struct
{
    unsigned short protect_status_id;   ///< 保护状态ID
    unsigned short appear_delay_thre;   ///< 保护状态产生延时阈值
    unsigned char protect_state;        ///< 当前保护状态(中间变量)
    unsigned short appear_delay;        ///< 保护状态产生计时(中间变量)
}protect_para_t;

typedef struct
{
    protect_para_t protect_para;
    char (*appear_judge)(protect_para_t* para);
    char (*disappear_judge)(protect_para_t* para);
}protect_status_judge_t;


#define FRAME_CURRENT_63A  63  //空开壳架电流63A
#define FRAME_CURRENT_125A 125 //空开壳架电流125A
#define FRAME_CURRENT_250A 250 //空开壳架电流250A


#define AIRSWITCH_GROUP_NUM    12
#define AIRSWITCH_SUBGROUP_NUM 10
#define AIRSWITCH_DEFAULT_COMM_ADDR 255 //默认通讯地址

#define KILO_MILLI_CONVERT 1000000 // 用于kilo和milli之间的转换，如kW(千瓦)和mW(毫瓦)之间的转换
#define SIXTY_KILOWATT 60000 // 60kW=60000W，空开功率显示的上限
#define SEVEN_HUNDRED_THOUSAND_KILOWATT 70000000 //70WKWh，空开电量显示上限

#define EIGHTY_VOLT 80         // 80V，空开（母排端和接线端）电压显示的上限
#define ADC_EIGHTY_VOLT 3      // 3V，空开（母排端和接线端）ADC采样值的上限
#define CURRENT_DETECT_MIN 1.2 // 1.2A以下电流置0
#define DEBOUNCE_COUNT 5       // 防抖计数
#define CURRENT_INVALID_INDEX 3
#define SAMPLE_AMPLIFY_INDEX 1000 // 计量芯片采样放大倍数
#define SHELL_FRAME_CURR_MAX 125
#define CURRENT_SMOOTH_MIN_INDEX 0.005
#define CURRENT_SMOOTH_MAX_INDEX 0.01
#define ROUND_TWO_DECIMAL 100

#define DIRECT_JUDGEMENT   0    //直接判断条件
#define INDIRECT_JUDGEMENT 1    //判断负载条件

#define DISCONNECT_ALM_DEBOUNCE     10

#define TEMP_ADC_DEV_NAME  "adc0"

#define SAMPLE_THREAD_DELAY_TIME       200 // sample线程延时时间200ms

#define LOW_TEMP  (-40)         ///< 电芯温度下界
#define HIGH_TEMP 140           ///< 电芯温度上界
#define TEMP_INVALID_LOW (-40)  ///< 下界温度无效值
#define TEMP_INVALID_HIGH 140   ///< 上界温度无效值
#define POWER_STATUS_VOL_CHECK 20
#define POWER_STATUS_ON 1
#define POWER_STATUS_OFF 0

#define INTERNAL_OVER_TEMPERATURE 110    //内部过温保护阈值
#define CONTACT_OVER_TEMPERATURE 100     //触点过温保护阈值
#define OVER_VOLTAGE_THRESHOLD 60.0f	 //过压保护阈值
#define ONE_SECOND_COUNT (1000 / SAMPLE_THREAD_DELAY_TIME)  //SAMPLE线程1s时间计算次数
#define SAMPLE_MIN_TICK  (60000/SAMPLE_THREAD_DELAY_TIME)   //SAMPLE线程1min时间计算次数

#define THREE_SECOND_COUNT (ONE_SECOND_COUNT * 3)         //SAMPLE线程3s时间计算次数
#define FIVE_SECOND_COUNT (ONE_SECOND_COUNT * 5)          //SAMPLE线程5s时间计算次数
#define EIGHT_SECOND_COUNT (ONE_SECOND_COUNT * 8)         //SAMPLE线程8s时间计算次数
#define TEN_SECOND_COUNT (ONE_SECOND_COUNT * 10)          //SAMPLE线程10s时间计算次数
#define AIRSWITCH_FIVE_MIN_TIME (SAMPLE_MIN_TICK *5)      //SAMPLE线程5min时间计算次数
#define AIRSWITCH_ONE_HOUR_TIME (SAMPLE_MIN_TICK * 60)    //SAMPLE线程1h时间计算次数

#define OVER_LOAD_LARGE_CURR_COFF (2.6f)       ///< 过载保护大电流系数，额定电流的2.6倍
#define ENERGY_START_CALC_CURR_COFF (1.13f)    ///< 开始能量累计的电流系数，额定电流的1.13倍
#define FRAME_TYPE_63A_OVERLOAD_TIME 24
#define FRAME_TYPE_125A_OVERLOAD_TIME 34
#define FRAME_TYPE_250A_OVERLOAD_TIME 45
#define ADDR_VOLTAGE_RATIO (7021.0f / 221)    //通讯地址采样电压倍率
#define MAX_FAULT_OPENING_TIMES  20000        //空开故障分闸次数最大值
#define ONE_WH_POWER (3600000)                // 3600000W*ms 1Wh电量
#define OUTPUT_VOLTAGE_SCALING_FACTOR (1686.0f / 66.5f)  //缩放系数
#define POWER_SAVE_DIFFERENCE (50)                // 电量保存差值

void* sample_init_sys(void *param);
void sample_main(void* parameter);
unsigned char getGroupAddress(float pin3Voltage, float mainVoltage);
unsigned char getSubGroupAddress(float pin4Voltage, float mainVoltage);
unsigned char getAirSwitchCommAddr(void);
char check_micro_movement_status(void);
char hal_board_gpio_init(void);
char get_setaddrflag(void);
unsigned char clear_fault_opening_times();
char save_power_energy(void);
unsigned char judge_frame_current(void);
unsigned char unauthorization_clear_alarm(void);
#ifdef __cplusplus
}
#endif      //< __cplusplus

#endif      //< __AIRSWITCH_SAMPLE_H_
