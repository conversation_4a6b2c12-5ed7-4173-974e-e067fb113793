#include "led.h"
#include "CommCan.h"
#include "battery.h"
#include "realAlarm.h"
#include "MasterOper.h"
#include "SlaveOper.h"
#include "BattCharge.h"
#include "BattSOXCalc.h"
#include "ChargeRotate.h"
#include "usart.h"
#include "para.h"
#include "BattCharge_product.h"
#include "BattSleepCtrl.h"

/* define */

#define NO_DISCHG_COUNTER (5*60*60)
#define CHANGE_POWER_ON_COUNTER (120)
#define SMOOTH_LENGTH THREE_MINUTES_SECOND
#define CONTROL_DISCHG_VOLT (47.4f)
#define CHARGE_VOL_DIFF (0.45f)   //功率侧要求外部电压和掉电电压阈值压差达到0.45才会有充电电流
/* var */

static BYTE s_ucRunModeControledFlag = 0;
Static T_SysPara s_tSysPara;
#ifdef UNITEST
static FLOAT s_afBusVolCheck[SMOOTH_LENGTH] = {0.0f,};
static BYTE s_ucBusVolIndexCheck = 0;
#endif

/* func */

Static void SmartLiCharge(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
Static BOOLEAN Standby(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
Static void SmartLi_DealOutVoltWhenControlled(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
Static void CalcBusVolt(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
Static void SmartLi_CalcSysMaxChargeCurr(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
Static void SetDischargeVoltWhenPowerOn(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
Static void SetChgPara(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
Static BOOLEAN DealEnterStandbyInit(T_BattDealInfoStruct *pBattDeal);
Static void DealBattSelfRechargeTimer(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
Static void CheckChargeNoCurr(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
Static BOOLEAN DischgUpdatePowerOnDown(FLOAT fDischgVolt, T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
Static FLOAT UpdateMixVolt(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
Static BOOLEAN PeakShiftChargeJudge(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);

/***************************************************************************
 * @brief    清空受控模式下充电标志位
 **************************************************************************/
void ClearRunModeControledFlag(void)
{
    s_ucRunModeControledFlag = 0;
    return;
}

/***************************************************************************
 * @brief    电池充电处理函数主入口
 **************************************************************************/
void BattCharge(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    GetSysPara(&s_tSysPara);

    if (IsMaster())
    {
        SetMasterOperFunc();
    }
    else
    {
        SetSlaveOperFunc();
    }

    if (BATT_MODE_STANDBY == pBattDeal->ucChargeMode)
    {
        Standby(pHardwarePara, pBattIn, pBattDeal);
    }
    else if (BATT_MODE_CHARGE == pBattDeal->ucChargeMode)
    {
        SmartLiCharge(pHardwarePara, pBattIn, pBattDeal);
    }
    return;
}

/***************************************************************************
 * @brief   在线非浮充模式处理
            在线非浮充时需要处理的事情
            1、状态切换，一些变量、参数的处理
            2、充电轮转处理（在线非浮充、充电相关，放电无关）
            3、为后面转放电状态做好相关准备
            4、自补电做好准备
            5、为充电做好准备
 **************************************************************************/
Static BOOLEAN Standby(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    DealEnterStandbyInit(pBattDeal);
    DealChargeRotate(pBattDeal, pBattIn);

    pBattDeal->fChargeHopeVol = pBattIn->tPara.fChargeFullVol;
    pBattDeal->fDischargeLimitCurr = clacDischCurr();

    // if (pBattDeal->bPowerOff)
    // {
    //     SetChargeMode(BATT_MODE_DISCHARGE);
    //     return False;
    // }

    if(PeakShiftChargeJudge(pBattIn,pBattDeal) == False)
    {
        return False;
    }

    /* 充满时计算母排电压，为放电计算作准备 */
    CalcBusVolt(pBattIn, pBattDeal);

    DealBattSelfRechargeTimer(pBattIn, pBattDeal);

    if (IfShouldRecharge(pBattIn, pBattDeal))
    {
        SetChargeMode(BATT_MODE_CHARGE);
        return False;
    }

    SmartLi_CalcChargeHopeCurr();
    SmartLi_CalcSysMaxChargeCurr(pBattIn, pBattDeal);

    GetMixOnOffVolt(pHardwarePara, pBattIn, pBattDeal, &s_tSysPara); // 混用时先计算好停电/来电电压
    CheckChargeNoCurr(pBattIn, pBattDeal);
    GetPowerdownVoltOper(pBattIn, pBattDeal);

    /* 计算最终期望放电目标电压
    keep output lower than normal discharge voltage to prevent that
    one switch to discharge firstly and charge others. */
    SetDischargeVoltWhenPowerOn(pBattIn, pBattDeal);
    pBattDeal->fDischargeSetVol = pBattDeal->fDischargeHopeVol;

    SmartLi_DealOutVoltWhenControlled(pHardwarePara, pBattIn, pBattDeal);

    pBattDeal->fChargeCurrHope = MIN(pBattDeal->fChargeCurrHope, g_ProConfig.fRateCurrChg);

    SetChgPara(pBattIn, pBattDeal);

    return True;
}

/***************************************************************************
 * @brief    自补电计时器使能判断
 **************************************************************************/
Static void DealBattSelfRechargeTimer(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if (BattSelfRechargeJudgement(pBattIn) == True)
    {
        pBattDeal->bSelfRechargeTimerEnable = True;
    }
    else
    {
        pBattDeal->bSelfRechargeTimerEnable = False;
        pBattDeal->slSelfRechargeTimer = 0;
    }
    return;
}

/***************************************************************************
 * @brief    处理状态切换刚进入在线非浮充模式的所需动作
 **************************************************************************/
Static BOOLEAN DealEnterStandbyInit(T_BattDealInfoStruct *pBattDeal)
{
    if (pBattDeal->ucBatStatus != BATT_MODE_STANDBY)
    {
        EnterStandbyInit(pBattDeal);
        return False;
    }
    return True;
}

#ifdef UNITEST
void ParseBusVolResult(FLOAT *pafBusVol, BYTE ucBusVolIndex)
{
    rt_memcpy(s_afBusVolCheck, pafBusVol, sizeof(FLOAT)*SMOOTH_LENGTH);
    s_ucBusVolIndexCheck = ucBusVolIndex;
    return;
}
void GetBusVolResult(FLOAT *pafBusVol, BYTE ucBusVolIndex)
{
    rt_memcpy(pafBusVol, s_afBusVolCheck, sizeof(FLOAT)*SMOOTH_LENGTH);
    ucBusVolIndex = s_ucBusVolIndexCheck;
    return;
}
#endif
/***************************************************************************
 * @brief    计算母排电压
 **************************************************************************/
Static void CalcBusVolt(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    static FLOAT s_afBusVol[SMOOTH_LENGTH] = {0.0f,};
    static BYTE s_ucBusVolIndex = 0;
    BYTE i = 0;
    FLOAT fBusVoltSum = 0.0f;
    FLOAT fBusAveVolt = 0.0f;

    if (pBattDeal->wBusVoltNum == 0)
    {
        rt_memset(s_afBusVol, 0, SMOOTH_LENGTH * sizeof(FLOAT));
        s_ucBusVolIndex = 0;
    }
    fBusAveVolt = GetAveBusVolt();
    s_afBusVol[s_ucBusVolIndex] = fBusAveVolt;
    s_ucBusVolIndex = (s_ucBusVolIndex + 1) % SMOOTH_LENGTH;
    #ifdef UNITEST
    ParseBusVolResult(s_afBusVol ,s_ucBusVolIndex);
    #endif

    if (pBattDeal->wBusVoltNum < THREE_MINUTES_SECOND && fBusAveVolt > OUT_VOLT_MIN)
    {
        pBattDeal->wBusVoltNum++;
        pBattDeal->fBusVoltSum += fBusAveVolt;
        pBattDeal->fBusVoltAvgForMix = pBattDeal->fBusVoltSum / (FLOAT)pBattDeal->wBusVoltNum;
        pBattDeal->fBusAvgVoltReal = pBattDeal->fBusVoltAvgForMix;
    }
    else if (IfBduCharge() || BATT_MODE_STANDBY == pBattDeal->ucChargeMode) // BDU切放电会比BCM快,掉电后调整电压会导致电流大波动
    {
        /// 平滑处理，避免只有充电前期更新用于混用的母排电压;
        if (pBattDeal->fBusVoltAvgForMix < fBusAveVolt && (DISCHG_MODE_SELF_ADAPTION == pBattIn->tPara.ucUsageScen || DISCHG_MODE_SOLAR == pBattIn->tPara.ucUsageScen))
        { /// 混用避免充电时母排电压被拉低后无限制调低掉电电压阈值，电压只增不减
            for (i = 0; i < SMOOTH_LENGTH; i++)
            {
                fBusVoltSum += s_afBusVol[i];
            }
            pBattDeal->fBusVoltAvgForMix = fBusVoltSum / SMOOTH_LENGTH;
        }
        pBattDeal->fBusAvgVoltReal = 0.95f * pBattDeal->fBusAvgVoltReal + 0.05f * fBusAveVolt;
    }
    return;
}

Static BOOLEAN IsBattCurrAndVolFull(WORD wHoldSeconds, T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    return (pBattIn->tData.fBatVol >= (pBattIn->tPara.fChargeFullVol - DC_VOLT_BIAS)
        && pBattDeal->wBattChargeEndHoldSeconds >= wHoldSeconds
        && pBattDeal->bChargeEndHold);
}

Static void AdjustSOCWhenChargeEnd(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    FLOAT fBattRealCap = (FLOAT)pBattIn->tPara.wBattCap * g_ProConfig.fCapTransRate* pBattDeal->fBattSOH / 100.0;

    if (IsBattCurrAndVolFull(60, pHardwarePara, pBattIn, pBattDeal))
    {
        pBattDeal->fBattCap = MAX(fBattRealCap * 99.81/100.0, pBattDeal->fBattCap);

        if (pBattDeal->wBattCompleteFullCounter >= (WORD)BATT_FULL_CNT_MAX)
        {
            pBattDeal->fBattCap = fBattRealCap;
            pBattDeal->bBattChargeFull = True;
        }
    }
}

Static BOOLEAN IsCompleteFull(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn)
{
    BYTE i;

    for(i = 0; i < pHardwarePara->ucCellVoltNum; i++)
    {
        if(pBattIn->tData.afCellVolt[i] < BATT_CELLFULL_VOLT_MAX)
        {
            return False;
        }
    }
    // 按照充电末期电压判断置满
    return True;
}

Static void CheckAndAdjustSOC(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if(IsCompleteFull(pHardwarePara, pBattIn))
    {
        TimerPlus(pBattDeal->wBattCompleteFullCounter, (WORD)BATT_FULL_CNT_MAX);
    }
    else
    {
        pBattDeal->wBattCompleteFullCounter = 0;
    }

    if (pBattDeal->bChargeEndHold)
    {
        TimerPlus(pBattDeal->wBattChargeEndHoldSeconds, pBattIn->tPara.wChagEndHoldMinute * ONE_MINUTE_PER_SECONDS);
        AdjustSOCWhenChargeEnd(pHardwarePara, pBattIn, pBattDeal);//必须这里判断，因为到了分钟定时器可能计数已经被清 Added by fengfj, 2020-07-30 22:51:19
    }
    else if (fabs(pBattIn->tData.fBattCurr) >= pBattIn->tData.fCurrMinDet)
    {
        /*define as 99.48%, make it close to full. charge little to get full.*/
        pBattDeal->fBattCap = MIN((FLOAT)pBattIn->tPara.wBattCap * g_ProConfig.fCapTransRate* pBattDeal->fBattSOH / 100.0 * 99.48/100.0, pBattDeal->fBattCap);

        if((pBattDeal->fBattCap - pBattDeal->fBattCapBak) < -0.001 && pBattIn->tData.fBattCurr >= pBattIn->tData.fCurrMinDet)
        {
            pBattDeal->fBattCap = pBattDeal->fBattCapBak;
        }
    }

    return;
}

// static void SetPowerdownVoltOnSolarChargeStart(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
// {
//     if((RUN_MODE_FREE == pBattIn->tPara.ucRunMode) && (DISCHG_MODE_SELF_ADAPTION == pBattIn->tPara.ucUsageScen) && pBattIn->tPara.bBoostChg)
//     {
//         pBattDeal->fPowerDownVolt = MIN(pBattIn->tData.fBusVol - 2.0f, 51.0f);////掉电电压阈值初始化
//         pBattDeal->fPowerDownVolt = GetValidData(pBattDeal->fPowerDownVolt, POWER_DOWN_VOL_MAX, POWER_DOWN_VOL_MIN);
//     }

//     pBattDeal->wRecalculatePowerdownVoltDelay = 0;

//     return;
// }

Static BOOLEAN IsBatteryChargeFull(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if (IsBattCurrAndVolFull(pBattIn->tPara.wChagEndHoldMinute * ONE_MINUTE_PER_SECONDS, pHardwarePara, pBattIn, pBattDeal))
    {
        return True;
    }
        
    if (pBattDeal->bCellFull || pBattDeal->bCellOverVolPrt || pBattDeal->bBattOverVolPrt)
    {
        return True;
    }

    return False;
}

Static void SetChgPara(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if (SetChgParaOper != NULL)
    {
        SetChgParaOper(pBattDeal, pBattIn->tPara.fChargeFullVol);
    }
    return;
}

Static void SetDischargeVoltWhenPowerOn(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if ((BYTE)DISCHG_MODE_BATT_CHARACTERISTIC == pBattIn->tPara.ucUsageScen || (BYTE)DISCHG_MODE_CONSTANT_VOLTAGE == pBattIn->tPara.ucUsageScen)
    {
        SmartLi_CalDischargeOutputVoltOper(pBattDeal, pBattIn);
    }
    else
    {
        pBattDeal->fDischargeHopeVol = GetMixOutputVolt(pBattIn, pBattDeal);
    }
    return;
}

/***************************************************************************
 * @brief   混用转放电后放电电压调整：
 *          1.掉电前母排电压大于55，放电电压从54-0.003*I按0.1V/18斜率下降到53-0.003*I
 *          2.掉电前母排电压大于54，小于等于55，放电电压从掉电前母排电压-1-0.003*I按0.1V/18斜率下降到53-0.003*I
 *          3.掉电前母排电压小于等于54，放电电压为掉电前母排电压-1-0.003*I
 *          只有当下垂均流使能为允许时放电电压才会-0.003*I
 **************************************************************************/
Static FLOAT UpdateMixVolt(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    FLOAT fObjVolt = 0.0f;
    if(pBattDeal->bFirstEnterDischg)
    {
        if(pBattDeal->fBusVoltAvgForMix > 55.0f)
        {
            pBattDeal->fMixTargetVolt = 54.0f;
        }
        else if(pBattDeal->fBusVoltAvgForMix > 54.0f)
        {
            pBattDeal->fMixTargetVolt = pBattDeal->fBusVoltAvgForMix - MIX_POWRE_DIFF_DISCHG;
        }
    }
    else if(pBattDeal->fMixTargetVolt - 0.1f / 18 > 53.0f)
    {
        pBattDeal->fMixTargetVolt -= 0.1f / 18;
    }
    else
    {
        pBattDeal->fMixTargetVolt = 53.0f;
    }

    if(pBattIn->tPara.bSagEqualCurr)
    {
        fObjVolt = pBattDeal->fMixTargetVolt - 0.003 * fabs(pBattIn->tData.fBusCurr);
    }
    else
    {
        fObjVolt = pBattDeal->fMixTargetVolt;
    }
    return fObjVolt;
}

/***************************************************************************
 * @brief   放电电压调整时同步更新来电电压、掉电电压
 **************************************************************************/
Static BOOLEAN DischgUpdatePowerOnDown(FLOAT fDischgVolt, T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    pBattDeal->fPowerDownVolt = fDischgVolt;
    pBattDeal->fPowerDownVolt = GetValidData(pBattDeal->fPowerDownVolt, MIX_POWER_DOWN_MAX, MIX_VOLT_MIN);

    pBattIn->tPara.fSysPowerOnThreshold = fDischgVolt + 0.6f;
    pBattIn->tPara.fSysPowerOnThreshold = GetValidData(pBattIn->tPara.fSysPowerOnThreshold, MIX_POWER_ON_MAX, MIX_VOLT_MIN);
    return True;
}

/*混用输出电压：充电时排电压*/
FLOAT GetMixOutputVolt(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    FLOAT fVol = 0.0;
    if(pBattDeal->bInitMixDischg)
    {
        fVol = GetSelfAdaptionInitVolt();
        return fVol;
    }
    fVol = pBattDeal->fBusVoltAvgForMix - MIX_POWRE_DIFF_DISCHG;
    if (pBattDeal->wBusVoltNum > (WORD)0)   //charge
    {
        fVol = pBattDeal->fBusVoltAvgForMix - MIX_POWRE_DIFF_DISCHG;
    }
    else if(pBattDeal->fBusVoltAvgForMix > 54.0f)    //discharge
    {
        fVol = UpdateMixVolt(pBattIn, pBattDeal);
        DischgUpdatePowerOnDown(fVol, pBattIn, pBattDeal);
        if(pBattDeal->fBusVoltAvgForMix > 55.0f)
        {
            if(pBattIn->tPara.fSysPowerOnThreshold > 53.3f && fabs(pBattIn->tData.fBusCurr) < pBattIn->tData.fCurrMinDet && pBattIn->tData.fBusVol > 52.0f)
            {
                TimerPlus(pBattDeal->wEqualChgPowerOffTime, NO_DISCHG_COUNTER);
                if(TimeOut(pBattDeal->wEqualChgPowerOffTime, NO_DISCHG_COUNTER))
                {
                    fVol = 50.5f;
                    TimerPlus(pBattDeal->ucChangePowerOnDelay, CHANGE_POWER_ON_COUNTER);
                    if(TimeOut(pBattDeal->ucChangePowerOnDelay, CHANGE_POWER_ON_COUNTER))
                    {
                        pBattIn->tPara.fSysPowerOnThreshold = 51.0f;
                    }
                }
            }
            else
            {
                pBattDeal->wEqualChgPowerOffTime = 0;
                pBattDeal->ucChangePowerOnDelay = 0;
            }
        }
    }
    else 
    {
        if(pBattIn->tPara.bSagEqualCurr)
        {
            fVol = fVol - 0.003*fabs(pBattIn->tData.fBusCurr);
        }
        DischgUpdatePowerOnDown(fVol, pBattIn, pBattDeal);
    }

    //混用输出电压不高于53.5V,也不得低于51.5V
    fVol = MIN(fVol, MIX_DISCHG_VOLT_MAX);   ///add by xzx 2021-02-25
    if(DISCHG_MODE_SELF_ADAPTION == pBattIn->tPara.ucUsageScen  || DISCHG_MODE_SOLAR == pBattIn->tPara.ucUsageScen)
    {///调整放电输出电压下限仅适用15串电池
        fVol = GetValidData(fVol, pBattIn->tPara.fSysPowerOnThreshold, MIX_VOLT_MIN);
    }

    return fVol;
}

Static void SmartLi_DealOutVoltWhenControlled(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    /*如果是受控模式，没有收到下发电压，则以电池电压放电；如果收到放电电压，则以放电电压放电*/
    if (pBattIn->tData.bRunModeControled)
    {
        if (pBattDeal->tOutputVoltByCtrl.bValid)
        {
            pBattDeal->fDischargeHopeVol   = GetValidData(pBattDeal->tOutputVoltByCtrl.sValue/100.0, OUT_VOLT_MAX, SMART_LI_OUTPUT_VOL_DEFAULT);
            // if(pBattDeal->fDischargeHopeVol >= 48.0f)
            // {
            //     pBattDeal->fDischargeHopeVol = CONTROL_DISCHG_VOLT;
            // }
            if((BATT_MODE_CHARGE == pBattDeal->ucChargeMode && IfBduCharge()) || BATT_MODE_STANDBY == pBattDeal->ucChargeMode)
            {
                s_ucRunModeControledFlag = 4;
                pBattDeal->fDischargeHopeVol   = GetValidData(pBattDeal->tOutputVoltByCtrl.sValue/100.0, OUT_VOLT_MAX, SMART_LI_OUTPUT_VOL_DEFAULT);
                pBattDeal->fPowerDownVolt = GetValidData(pBattDeal->fDischargeHopeVol, MIN(pBattDeal->fDischargeHopeVol, pBattIn->tData.fBusVol - 1.5), CONTROL_DISCHG_VOLT);
            }
            else
            {
                if(s_ucRunModeControledFlag == 0)
                {
                    // s_ucRunModeControledFlag = 3;
                    pBattDeal->fDischargeHopeVol = CONTROL_DISCHG_VOLT;
                    pBattDeal->fPowerDownVolt = 48.0f - CHARGE_VOL_DIFF;   //48V以上有充电电流
                }
            }
        }
        else
        {
            pBattDeal->fDischargeHopeVol = pBattIn->tData.fBatVol * g_ProConfig.fVoltTrasRate;
            if((BATT_MODE_CHARGE == pBattDeal->ucChargeMode && IfBduCharge()) || BATT_MODE_STANDBY == pBattDeal->ucChargeMode)
            {
                s_ucRunModeControledFlag = 2;
                pBattDeal->fDischargeHopeVol = pBattIn->tData.fBusVol - 1.5;
                pBattDeal->fDischargeHopeVol = GetValidData(pBattDeal->fDischargeHopeVol, OUT_VOLT_MAX, CONTROL_DISCHG_VOLT);
                pBattDeal->fPowerDownVolt = pBattDeal->fDischargeHopeVol;
            }
            else
            {
                if(s_ucRunModeControledFlag == 0)
                {
                    // s_ucRunModeControledFlag = 1;
                    pBattDeal->fDischargeHopeVol = CONTROL_DISCHG_VOLT;
                    pBattDeal->fPowerDownVolt = 48.0f - CHARGE_VOL_DIFF;   //48V以上有充电电流
                }
            }
        }
        pBattDeal->fDischargeSetVol    = pBattDeal->fDischargeHopeVol;
    }
    else
    {
        if (pBattDeal->tOutputVoltNonCtrl.bValid)
        {
            pBattDeal->fDischargeSetVol   = GetValidData(pBattDeal->tOutputVoltNonCtrl.sValue/100.0, OUT_VOLT_MAX, SMART_LI_OUTPUT_VOL_DEFAULT);
        }
    }
    return;
}

Static void SmartLi_CalcSysMaxChargeCurr(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if (SmartLi_CalcSysMaxChargeCurrOper != NULL)
    {
        SmartLi_CalcSysMaxChargeCurrOper(pBattDeal, pBattIn);
        return;
    }
}

//充电的计时操作
Static BOOLEAN CountInCharge(T_BattDealInfoStruct *pBattDeal)
{
    if(pBattDeal == NULL)
    {
        return FALSE;
    }

    TimerPlus(pBattDeal->ucActivatePowerOffFlagCounter, 5);                   //充电5s清除降低掉电电压标志
    if(TimeOut(pBattDeal->ucActivatePowerOffFlagCounter, 5))
    {
        pBattDeal->bActivatePowerOffFlag = FALSE;
    }

    TimerPlus(pBattDeal->ucChargeClearActivatePortVoltErrCnt, 40);            //激活口情况下重放电反复计时操作
    if(TimeOut(pBattDeal->ucChargeClearActivatePortVoltErrCnt, 40))
    {
        pBattDeal->ucChargeActivatePortVoltErrCnt = 0;
    }

    return TRUE;
}

Static void SmartLiCharge(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    FLOAT fEndCurr = 0.0;

    if ( pBattDeal->ucBatStatus != BATT_MODE_CHARGE )
    {
        pBattDeal->ucBatStatus                 = BATT_MODE_CHARGE;
        pBattDeal->bChargeEndHold              = False;
        pBattDeal->slBattChargeMinutes         = 0;
        pBattDeal->wBattChargeEndHoldSeconds   = 0;
        pBattDeal->bChargeCurrMin      = FALSE;
        pBattDeal->bChargeCurrMinBak   = FALSE;
        pBattDeal->bChargeCurrEnough   = FALSE;
        pBattDeal->ucBduCtrlMode               = BDU_CHG_START;
        pBattDeal->ucControlledDelayCnt = 0;
        pBattDeal->wBattCompleteFullCounter = 0;
        pBattDeal->slDefenseBattChargeMinutes = 0;
        pBattDeal->wNeedCancelChgLimitTimer = 0;
        pBattIn->tData.ucSlaveDischgCounter = 0;
        pBattDeal->bThroughChg = False;
        pBattDeal->bThroughDischg = False;
        pBattDeal->bChargeThroughBak = False;
        pBattDeal->bChargeThroughEnable   = True;
        pBattDeal->bVoltSwitchFlag        = False;
        pBattDeal->bInitMixDischg        = False;
        pBattDeal->fChargeCurrHope = 0;
        pBattDeal->wChgNoCurrCount = 0;
        pBattDeal->ucUpdateThreCount = 0;
        InitRotate(True);
    //    SetPowerdownVoltOnSolarChargeStart(pBattIn, pBattDeal);
        /*if(GetMcuWriteDisableStat())
        {//非受控下进充电，自动恢复PMOS
            ResetMCU(NO_RESET_PMOS_ONOFF);
        }编译不过，临时屏蔽*/
    }

    CountInCharge(pBattDeal);

    DealChargeRotate(pBattDeal, pBattIn);

    //判断充电是否进直通 20210121 mwl
    JudgeChargeThrough(pBattIn, pBattDeal);

    CalcBusVolt(pBattIn, pBattDeal);
    GetMixOnOffVolt(pHardwarePara, pBattIn, pBattDeal, &s_tSysPara);      ///混用时先计算好停电/来电电压
    SetDischargeVoltWhenPowerOn(pBattIn, pBattDeal);   ///充电时按照母排电压计算放电输出电压
    CheckChargeNoCurr(pBattIn, pBattDeal);
    GetPowerdownVoltOper(pBattIn, pBattDeal);

    TimerPlus(pBattDeal->ucChargeCounter, (BYTE)CHARGE_SOURCE_DELAY);

    pBattDeal->fChargeSetVol           = pBattIn->tPara.fChargeFullVol;
    pBattDeal->fDischargeLimitCurr     = clacDischCurr();
    // Added by fengfj, 2019-01-08 19:55:12
    pBattDeal->fDischargeSetVol        = pBattDeal->fDischargeHopeVol;   
    SmartLi_CalcChargeHopeCurr();
    SmartLi_DealOutVoltWhenControlled(pHardwarePara, pBattIn, pBattDeal);
    SmartLi_CalcSysMaxChargeCurr(pBattIn, pBattDeal);

    CheckAndAdjustSOC(pHardwarePara, pBattIn, pBattDeal);
    // if (pBattDeal->bPowerOff)
    // {
    //     SetChargeMode(BATT_MODE_DISCHARGE);
    //     return;
    // }

    if(PeakShiftChargeJudge(pBattIn,pBattDeal) == False)
    {
        return;
    }

    //判断电池是否进入在线非浮充
    if (IsBatteryChargeFull(pHardwarePara, pBattIn, pBattDeal))
    {
        SetChargeMode(BATT_MODE_STANDBY);
        return ;
    }

    pBattDeal->bChargeTimerEnable      = True;
    if ( (BYTE)NORMAL != pBattIn->tData.ucBduChargeProtectStatus || pBattIn->tData.bBduChgDisable)   //充电保护
    {
        pBattDeal->bChargeTimerEnable          = False;
        pBattDeal->bChargeEndHold              = False;
        pBattDeal->wBattChargeEndHoldSeconds   = 0;
        return ;
    }
    else if ((BDU_STATUS_STOP == pBattIn->tData.ucBDUStatus || pBattIn->tData.bCtrChargeFail)&& !IsTransPeriod())
    {
    /*
    1. If Bdu became discharge, DO NOT control to charge,because AC power 
    off.when AC power off, control charge may cause system and load power off.
    Added by fengfj, 2019-01-08 20:19:57
    */
        pBattDeal->ucBduCtrlMode           = BDU_CHG_START;
    }
    else if (IfBduDischarge())
    {
        if (BMSChgButBDUDischgOper != NULL)
        {
           BMSChgButBDUDischgOper(pBattDeal, pBattIn);
        }
    }

    //增加充电末期电流判断
    fEndCurr = pBattIn->tPara.fChagEndCurCoeff * pBattIn->tPara.wBattCap;

    fEndCurr = GetValidData(fEndCurr, pBattDeal->fChargeCurrHope-0.5, pBattIn->tData.fCurrMinDet + 0.3);

    //判断均充维持阶段是否到达（依据：电压到达均充点-0.5V，所有电池电流小于0.02C）
    if (pBattIn->tData.fBatVol >= (pBattIn->tPara.fChargeFullVol - DC_VOLT_BIAS) && fabs(pBattIn->tData.fBattCurr) <= fEndCurr)
    {
        pBattDeal->bChargeEndHold = True;
    }
    else
    {
        pBattDeal->bChargeEndHold              = False;
        pBattDeal->wBattChargeEndHoldSeconds   = 0;
    }
     if (pBattDeal->bCellUnderVolt)
    {
        pBattDeal->fDischargeHopeVol = SUPPLE_DISCHG_VOL;
        pBattDeal->fDischargeSetVol = pBattDeal->fDischargeHopeVol;
        pBattDeal->fPowerDownVolt = SUPPLE_DISCHG_VOL;
    }
    setBMSMode( BATT_MODE_CHARGE, (SHORT)(pBattIn->tPara.fChargeFullVol*100), 0);
	BduCtrl(SCI_CTRL_CHG2CHG, ENABLE);
    return;
}


BOOLEAN BattSleepCharge(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if(!IsMaster())
    {
        return False;
    }

    if ( pBattDeal->ucSleepBattStatus != BATT_MODE_CHARGE )
    {
        pBattDeal->ucSleepBattStatus                 = BATT_MODE_CHARGE;
        pBattDeal->bChargeEndHold              = False;
        pBattDeal->slBattChargeMinutes         = 0;
        pBattDeal->wBattChargeEndHoldSeconds   = 0;
        pBattDeal->bChargeCurrMin      = FALSE;
        pBattDeal->bChargeCurrMinBak   = FALSE;
        pBattDeal->bChargeCurrEnough   = FALSE;
        pBattDeal->ucBduCtrlMode               = BDU_CHG_START;
        pBattDeal->ucControlledDelayCnt = 0;
        pBattDeal->wBattCompleteFullCounter = 0;
        pBattDeal->slDefenseBattChargeMinutes = 0;
        pBattDeal->wNeedCancelChgLimitTimer = 0;
        pBattIn->tData.ucSlaveDischgCounter = 0;
        pBattDeal->bThroughChg = False;
        pBattDeal->bThroughDischg = False;
        pBattDeal->bChargeThroughBak = False;
        pBattDeal->bChargeThroughEnable   = True;
        pBattDeal->bVoltSwitchFlag        = False;
        pBattDeal->bInitMixDischg        = False;
        pBattDeal->fChargeCurrHope = 0;
        pBattDeal->wChgNoCurrCount = 0;
        pBattDeal->ucUpdateThreCount = 0;
        pBattDeal->ucTransCounter = 0;
        InitRotate(True);
    }
    SetMasterOperFunc(); //指针初始化
    DealChargeRotate(pBattDeal, pBattIn);
    CalcBusVolt(pBattIn, pBattDeal);
    GetMixOnOffVolt(pHardwarePara, pBattIn, pBattDeal, &s_tSysPara);      ///混用时先计算好停电/来电电压
    SetDischargeVoltWhenPowerOn(pBattIn, pBattDeal);   ///充电时按照母排电压计算放电输出电压
    CheckChargeNoCurr(pBattIn, pBattDeal);
    GetPowerdownVoltOper(pBattIn, pBattDeal);
    TimerPlus(pBattDeal->ucChargeCounter, (BYTE)CHARGE_SOURCE_DELAY);
    SmartLi_DealOutVoltWhenControlled(pHardwarePara, pBattIn, pBattDeal);
    SmartLi_CalcSysMaxChargeCurr(pBattIn, pBattDeal);

    if (pBattDeal->bPowerOff)
    {
        // SetChargeMode(BATT_MODE_DISCHARGE);
        pBattDeal->ucSleepBattMode = BATT_MODE_DISCHARGE;
        return False;
    }
    
    setBMSMode( BATT_MODE_CHARGE, (SHORT)(pBattIn->tPara.fChargeFullVol*100), 0);
    return True;
}


/***************************************************************************
 * @brief    充电阶段组内无充电电流，更新来电电压、掉电电压、放电电压
 **************************************************************************/
Static void CheckChargeNoCurr(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if (DISCHG_MODE_SELF_ADAPTION != pBattIn->tPara.ucUsageScen && DISCHG_MODE_SOLAR != pBattIn->tPara.ucUsageScen)
    {
        return;
    }

    if(!IsMaster())
    {
        return;
    }

    if(pBattIn->tData.ucChgStaNum > 0 && fabs(pBattIn->tData.fBattTotalCurr) < pBattIn->tData.fCurrMinDet
        && pBattIn->tData.fBusVol > 52.0f)
    {
        TimerPlus(pBattDeal->wChgNoCurrCount, 600);
        if(TimeOut(pBattDeal->wChgNoCurrCount, 600))   //计时满10分钟
        {
            TimerPlus(pBattDeal->ucUpdateThreCount, 10);
            pBattDeal->wChgNoCurrCount = 0;
        }
    }
    else
    {
        pBattDeal->wChgNoCurrCount = 0;
    }

    if(pBattDeal->ucUpdateThreCount > 0)    //每10分钟阈值在之前的基础上减少0.4
    {
        pBattIn->tPara.fSysPowerOnThreshold -= 0.4f * pBattDeal->ucUpdateThreCount;
        pBattIn->tPara.fSysPowerOnThreshold = GetValidData(pBattIn->tPara.fSysPowerOnThreshold, MIX_POWER_ON_MAX, MIX_VOLT_MIN);
        pBattDeal->fPowerDownVolt -= 0.4f * pBattDeal->ucUpdateThreCount;
        pBattDeal->fPowerDownVolt = GetValidData(pBattDeal->fPowerDownVolt, pBattIn->tPara.fSysPowerOnThreshold - MIX_POWRE_ON_DOWN_DIFF, MIX_VOLT_MIN);
        pBattDeal->fDischargeHopeVol = pBattDeal->fPowerDownVolt;
    }

    return;
}

//错峰充电判断
Static BOOLEAN PeakShiftChargeJudge(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if (pBattDeal->bPowerOff)
    {
        SetChargeMode(BATT_MODE_DISCHARGE);
        return False;
    }

#ifdef INTELLIGENT_PEAK_SHIFTING
    if(JudgeFmChargeSwit(pBattIn,pBattDeal))
    {
        return False;
    }

    if(!JudgePeakShiftCharge(pBattIn,pBattDeal,GetShiftPeakPara()))
    {
        return False;
    }
#endif

    return True;
}
