/**************************************************************************
* Copyright (C) 2010-2011, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：ZXDU18 CTL板软件
* 文件名称：comm.h
* 文件说明：通讯模块
* 作    者  ：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/

/***********************  常量定义  ************************/
#ifndef SOFTWARE_SRC_APP_COMM_H_ 
#define SOFTWARE_SRC_APP_COMM_H_ 

#include "common.h"
// #include "fileSys.h"

#ifdef __cplusplus
extern "C" {
#endif

#define COMM_RS485          0           // 后台RS485通讯口
#define COMM_PORT1          1           // 无线通讯口
#define COMM_PORT2          2           // AT命令通讯口
#define COMM_PORT3          3           // BDU通讯口
#define COMM_CAN            4           // CAN通信口，虚拟CAN通信口，这样改动最小
#define COMM_NET            5			//网口TCP_Server


#define SCI_PORT_NUM       6

#define BDU_SOI              0xFF
#define BDU_PFC_FLAG         0xFC
#define BDU_PACKET_LEN       13//包含帧头0xFF
#define BDU_PACKET_DATA_LEN  12

#define COMM_LOSE_COUNTER         3000        /* 不进通讯中断计时5分钟 5*60*10 */
#define COMM_NORTH_LOST_COUNTER   600         // 与后台无通讯计数
#define QUEUE_DEPTH_MAX		5
#define NORTH_BUFF_LEN		304
/*********************  外部变量说明 **********************/
extern  T_CommStruct        g_atComm[SCI_PORT_NUM];            // 通讯底层

extern  BOOLEAN     g_bCommLost;            // 与后台通讯失败60s通讯不上
extern  BOOLEAN     g_bNeedSendAlarm;       // 重要告警变化，需要主动告警标志，在告警处理时产生，在告警上报后消失
extern  BOOLEAN     g_bAutoSendAlarmFlag;  // 重要告警变化标志
extern  BOOLEAN     g_bGprsAutoUpDataFlag;      // 无线组网重要告警变化标志

typedef enum
{
	NORTH_NULL = 0,
	NORTH_RS485_MODBUS,
	NORTH_CAN_MODBUS,
	NORTH_RS485_1363,
	NORTH_CAN_1363,
}Enum_NorthType;
/*********************  结构体变量 **********************/
typedef struct
{
	BYTE ucNorthType;//
	WORD wLen;
	BYTE aucDataBuf[NORTH_BUFF_LEN];//数据缓冲区
}T_NorthPktElementStruct;

/********************* 函数原型定义 ************************/
void DealComm(void* parameter);
void sendCommData(T_CommStruct *ptComm);

#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // SOFTWARE_SRC_APP_COMM_H_ ;
