
#ifndef _FILE_UPLOAD_H_
#define _FILE_UPLOAD_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "data_type.h"
#include "protocol_bottom_comm.h"
#include "update_download_manage.h"

#define UPLOAD_FIRST_FRM_DATA_LEN    0x48

typedef enum
{
    UPLOAD_TRIG = 0,
    UPLOAD_DATA,
    UPLOAD_LIST,
    UPLOAD_EXIT,
}upload_status_t;

typedef struct
{
    char file_name[UPLOAD_FILE_NAME_MAX_LEN];
    unsigned short upload_trig_times;
    char is_trig_succ;
    char is_get_file_list;
    upload_status_t upload_next_sta;
}upload_sta_info_t;

typedef struct file_list_node
{
    char filename[UPLOAD_FILE_NAME_MAX_LEN];
    struct file_list_node* next;
}file_list_node;

int upload_cmd_pack(void* dev_inst, void *cmd_buf);
int upload_cmd_parse(void* dev_inst, void *cmd_buf);
void send_upload_msg(upload_status_t upload_sta);
int upload_file_trig_parse(dev_inst_t* dev_inst, cmd_buf_t* cmd_buf);
int upload_common_frm_pack(cmd_buf_t* cmd_buf);
int upload_file_list_frm_parse(cmd_buf_t* cmd_buf, unsigned short frm_no);
int upload_file_first_frm_pack(cmd_buf_t* cmd_buf);
int upload_file_first_frm_parse(cmd_buf_t* cmd_buf, unsigned short* frm_no);
int upload_file_data_frm_parse(cmd_buf_t* cmd_buf, unsigned short frm_no);
int upload_file_last_frm_deal();
void finish_south_upload();
int init_south_upload();
file_list_node* creat_file_list_node(const char* filename);
void add_filename_to_list(file_list_node** head, const char* filename);
void free_list(file_list_node* head);
short clean_afci_data();
short start_file_upload(const char* file_name);
char is_file_name_in_list(const char* filename, file_list_node* file_name_list);
char is_resume_trans();
int write_upload_tempinfo();
int clear_req_append(dev_inst_t* dev_inst);
int is_input_valid(dev_inst_t* dev_inst, cmd_buf_t* cmd_buf);
#ifdef __cplusplus
}
#endif

#endif 