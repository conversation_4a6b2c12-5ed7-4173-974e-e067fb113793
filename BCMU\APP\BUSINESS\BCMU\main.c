#include <stdio.h>
#include <rtthread.h>
#include <rtdevice.h>

#include "sample.h"
#include "data_type.h"
#include "control_manage.h"
#include "addr_distribution.h"
#include "north_main.h"
#include "self_check.h"
#include "utils_thread.h"
#include "utils_time.h"
#include "utils_data_transmission.h"
#include "utils_server.h"
#include "alarm_mgr_api.h"
#include "msg_id.h"
#include "battery_management.h"
#include "system_manage.h"
#include "south_main.h"
#include "main.h"
#include "server_id.h"
#include "alarm_register.h"
#include "his_record.h"
#include "realdata_save.h"
#include "led.h"
#include "app_config.h"
#include "storage.h"
#include "protocol_layer.h"
#include "unified_id_interface.h"
#if 1

#include "stdio.h"

#pragma import(__use_no_semihosting_swi)
#pragma import(__use_no_semihosting)

static char s_led_thread_stack[LED_THREAD_STACK_SIZE] RAM_SECTION_BSS;
static char s_control_manage_thread_stack[CONTROL_MANAGE_THREAD_STACK_SIZE] RAM_SECTION_BSS;
static char s_system_manage_thread_stack[SYSTEM_MANAGE_THREAD_STACK_SIZE] RAM_SECTION_BSS;
static char s_bmu_thread_stack[BMU_THREAD_STACK_SIZE] RAM_SECTION_BSS;
static char s_sample_thread_stack[SAMPLE_THREAD_STACK_SIZE] RAM_SECTION_BSS;
static char s_batt_manage_thread_stack[BATT_MANAGE_THREAD_STACK_SIZE] RAM_SECTION_BSS;
static char s_alarm_namage_thread_stack[ALARM_MANAGE_THREAD_STACK_SIZE] RAM_SECTION_BSS;
static char s_north1_thread_stack[BSMU1_THREAD_STACK_SIZE] RAM_SECTION_BSS;

static protocol_process_t protocol_process_tab[] = {
    {PROTOCOL_BOTTOM_COMM, bottom_pack, bottom_parse},
};

struct __FILE {

 int handle;

 /* Whatever you require here. If the only file you are using is */

 /* standard output using printf() for debugging, no file handling */

 /* is required. */

};

/* FILE is typedef d in stdio.h. */

#endif

// static void process_recv_msg(module_msg_t *msg_rcv);



// thread_info_t g_thread_group[] = {
//     {"control_manage", RT_NULL, init_ctrl_manage, RT_NULL, 4096, THREAD_PRIO_HIG, THREAD_TICK},
//     {"addr_distr",  RT_NULL,  init_addr_distr,  RT_NULL,  4096,  THREAD_PRIO_HIG, THREAD_TICK},
//     {"sample",  RT_NULL,  init_sample,  RT_NULL,  4096,  THREAD_PRIO_HIG, THREAD_TICK},
//     {"north",   RT_NULL,  init_north,   RT_NULL,  4096,  THREAD_PRIO_LOW, THREAD_TICK},
//     {"alarm_namage",  RT_NULL,  init_alarm_manage,  RT_NULL,  4096,  THREAD_PRIO_MID, THREAD_TICK},
// };   

/*********
 调试的时候用该段代码
static server_info_t g_server_group[] = {
    // 南向服务的启动需要在数据管理之前启动
    {{BMU_SERVER_ID ,             "bmu",             1024,  SERVER_PRIO_HIG}, init_bmu, comm_thread_entry},
    // {{DC_DC_SERVER_ID ,           "dc_dc",           1024,  SERVER_PRIO_HIG}, init_dc_dc, comm_thread_entry},
    
    {{SYSTEM_MANAGE_SERVER_ID,    "system_manage",   1024,  SERVER_PRIO_HIG}, init_sys_manage,  system_manage_main},

    {{SAMPLE_SERVER_ID,           "data_manage",     1024,  SERVER_PRIO_HIG}, init_sample, sample_main},
    // {{BUSINESS_MANAGE_SERVER_ID,  "business_manage", 1024,  SERVER_PRIO_LOW}, init_business_manage, business_manage_main},
    // {{ALARM_MANAGE_SERVER_ID ,    "alarm_namage",    1024,  SERVER_PRIO_LOW}, init_alarm_manage, alarm_main},
    // {{BSMU1_SERVER_ID ,           "north1",          1024,  SERVER_PRIO_LOW}, init_north1, bsmu_thread},
    // {{BSMU2_SERVER_ID ,           "north2",          1024,  SERVER_PRIO_LOW}, init_north2, bsmu_thread},
    // {{CONTROL_MANAGE_SERVER_ID,   "control_manage",  1024,  SERVER_PRIO_HIG}, init_ctrl_manage, ctrl_manage_main},

};******/


static link_type_t link_type_tab[LINK_TYPE_MAX] = {
    {LINK_CAN, can_dev_init, can_dev_read, can_dev_write, can_dev_set},  // 通过宏控制
    {LINK_COM, usart_dev_init, com_dev_read, com_dev_write, com_dev_set},
};

static server_info_t g_server_group[] = {
    {{{LED_SERVER_ID,   "signal_led",  sizeof(s_led_thread_stack),  s_led_thread_stack,  SERVER_PRIO_LOW}}, init_led_blink, led_thread_entry},
    {{{CONTROL_MANAGE_SERVER_ID,   "control_manage",  sizeof(s_control_manage_thread_stack),  s_control_manage_thread_stack,  SERVER_PRIO_HIG}}, init_ctrl_manage, ctrl_manage_main},
    {{{SYSTEM_MANAGE_SERVER_ID,   "system_manage",  sizeof(s_system_manage_thread_stack),  s_system_manage_thread_stack,  SERVER_PRIO_HIG}}, init_sys_manage, system_manage_main},
    //南向服务的启动需要在数据管理之前启动
    {{{BMU_SERVER_ID,   "bmu",  sizeof(s_bmu_thread_stack),  s_bmu_thread_stack,  THREAD_PRIO_HIG}}, init_bmu, comm_thread_entry},
    // {{DC_DC_SERVER_ID ,           "dc_dc",           1024,  THREAD_PRIO_HIG}, init_dc_dc, comm_thread_entry},
    {{{SAMPLE_SERVER_ID,   "data_manage",  sizeof(s_sample_thread_stack),  s_sample_thread_stack,  SERVER_PRIO_HIG}}, init_sample, sample_main},
    {{{BATT_MANAGE_SERVER_ID,   "batt_manage",  sizeof(s_batt_manage_thread_stack),  s_batt_manage_thread_stack,  SERVER_PRIO_HIG}}, init_batt_manage, batt_manage_main},
    {{{ALARM_MANAGE_SERVER_ID,   "alarm_namage",  sizeof(s_alarm_namage_thread_stack),  s_alarm_namage_thread_stack,  SERVER_PRIO_LOW}}, init_alarm_manage, alarm_main},
    //临时注掉// {{{BSMU1_SERVER_ID,   "north1",  sizeof(s_north1_thread_stack),  s_north1_thread_stack,  SERVER_PRIO_LOW}}, init_north1, bsmu_thread},
    // {{BSMU2_SERVER_ID ,           "north2",          1024,  SERVER_PRIO_LOW}, init_north2, bsmu_thread},
};


static link_inst_t link_inst_tab[] = {
    {LINK_BMU,      LINK_CAN, "can1",   CAN_FRAME_DATA_LEN_8},
    {LINK_BCMU,     LINK_CAN, "can4",   CAN_FRAME_DATA_LEN_8},
    {LINK_FIRE,     LINK_COM, "usart1"},
    {LINK_BSMU_BAK, LINK_CAN, "can5",   CAN_FRAME_DATA_LEN_16},
    {LINK_DC_DC,    LINK_CAN, "can2",   CAN_FRAME_DATA_LEN_8},
    {BMU_LINK_BCMU, LINK_CAN, "can0",   CAN_FRAME_DATA_LEN_8},
    {LINK_BSMU,     LINK_CAN, "can2",   CAN_FRAME_DATA_LEN_8},
};


server_info_t* get_server_group(int *num){
    *num = sizeof(g_server_group)/sizeof(g_server_group[0]);
    return &g_server_group[0];
}

// 定义接口表
static const data_access_interface_t id_base_interface = {
    .get_data = unified_get_data,
    .set_data = unified_set_data,    
    .linear_search = linear_search_id_index
};


int main(int argc, char *argv[]) {
    //初始化
    init_flash_page_size(FLASH_PAGE_SIZE);
    // 注册 协议所使用的函数接口
    register_protocol_process_info(protocol_process_tab, sizeof(protocol_process_tab) / sizeof(protocol_process_t));
    register_data_access_interface(&id_base_interface);
    init_crc();
    init_time_power_on();
    register_bcmu_alarm();
    // 注册 CAN,COM,IP 链路所使用的函数接口
    register_link_type_info(link_type_tab, sizeof(link_type_tab) / sizeof(link_type_t));
    register_link_inst_tab_info(link_inst_tab, sizeof(link_inst_tab)/sizeof(link_inst_tab[0]));
    register_product_para();
    init_his_record();
    init_para_manage();
    //初始化实时数据内存区
    init_real_data_memory();
    
    create_server(&g_server_group[0], sizeof(g_server_group)/sizeof(g_server_group[0]));

    return SUCCESSFUL;
}