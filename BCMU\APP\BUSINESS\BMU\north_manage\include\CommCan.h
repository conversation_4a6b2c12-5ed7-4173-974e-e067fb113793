#ifndef COMMCAN_H_
#define COMMCAN_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "cmd.h"
#include "linklayer.h"
#include "device_type.h"
#include "dev_bmu.h"

#define MIN_ADDR 1
#define MAX_ADDR 32

#define CAN1_HEAD_FILTER_NUM 2
#define TRIGGER_TIMES 3

#pragma pack(push, 4)
typedef struct {
    BMU_para_data_t* dev_data;
    cmd_buf_t*   cmd_buff;
    link_inst_t* link_inst;
    rt_mq_t      north_mq;
}bmu_north_mgr_t;

#pragma pack(pop)

void* init_can_comm(void *param);
unsigned char read_localhost_addr(void);
dev_inst_t* read_dev_inst(void);
void north_thread(void *param);
int set_addr(unsigned char addr);

#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // COMMCAN_H_;
