#include "north_main.h"
#include "data_type.h"
#include "utils_thread.h"
#include "utils_server.h"
#include "msg_id.h"
#include "tov_dev_update_manage_handle.h"
#include "utils_rtthread_security_func.h"
#include "dev_csu_comm.h"
#include "linklayer.h"

static server_info_t *s_server_info = NULL;

static msg_map s_north_msg_map[] = { 0 };


static dev_inst_t* s_dev_tov = NULL;

Static north_mgr_t *init_thread_data(link_inst_t* link_inst, unsigned char mod_id);

void* init_north(void* param)
{
    north_mgr_t* north_mgr = NULL;

    RETURN_VAL_IF_FAIL(param != NULL, NULL);

    s_server_info = (server_info_t *)param;
    s_server_info->server.server.map_size = sizeof(s_north_msg_map) / sizeof(msg_map);
    register_server_msg_map(s_north_msg_map, s_server_info);

    s_dev_tov = init_dev_inst(DEV_TOV);
    RETURN_VAL_IF_FAIL(s_dev_tov != NULL, NULL);
    north_mgr = init_thread_data(s_dev_tov->dev_type->link_inst, MOD_TOV_NORTH);
    struct serial_configure config  = RT_SERIAL_CONFIG_DEFAULT;
    char com_name[] = "uart2";
    uart_dev_config(com_name, &config);
    bottom_update_init();

    return north_mgr;
}

Static north_mgr_t *init_thread_data(link_inst_t* link_inst, unsigned char mod_id)
{
    north_mgr_t* north_mgr = NULL;

    north_mgr = rt_malloc(sizeof(north_mgr_t));
    RETURN_VAL_IF_FAIL(north_mgr != NULL, NULL);
    rt_memset_s(north_mgr, sizeof(north_mgr_t), 0x00, sizeof(north_mgr_t));

    north_mgr->cmd_buff = rt_malloc(sizeof(cmd_buf_t));
    if (north_mgr->cmd_buff == NULL)
    {
        free(north_mgr);
        return NULL;
    }
    rt_memset_s(north_mgr->cmd_buff, sizeof(cmd_buf_t), 0x00, sizeof(cmd_buf_t));

    north_mgr->link_inst = link_inst;
    return north_mgr;
}

void north_main(void* param)
{
    north_mgr_t* north_mgr = (north_mgr_t*)param;
    unsigned char time_delay = 20;

    while (is_running(TRUE))
    {
        if (RT_EOK == rt_sem_take(&(north_mgr->link_inst->rx_sem), THREAD_TICK))
        {
            if (FAILURE == linker_recv(s_dev_tov, north_mgr->cmd_buff)) {
                continue;
            }
            if (SUCCESSFUL == protocol_parse_recv(s_dev_tov, north_mgr->cmd_buff))
            {
                cmd_send(s_dev_tov, north_mgr->cmd_buff);
            }
            continue;
        }
        rt_thread_mdelay(time_delay);
    }
}
