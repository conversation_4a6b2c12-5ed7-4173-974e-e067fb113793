/*******************************************************************************
  * @file        bmu_control_manage.h
  * @version     v1.0.0
  * @copyright   COPYRIGHT &copy; 2023 CSG
  * <AUTHOR>
  * @date        2023-4-11
  * @brief
  * @attention
  * Modification History
  * DATE         DESCRIPTION
  * ------------------------
  * - 2023-4-11  dongmengling Created
*******************************************************************************/

#ifndef _BMU_CONTROL_MANAGE_H
#define _BMU_CONTROL_MANAGE_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

#include "utils_server.h"
/* 告警消息处理 */
typedef struct {
    rt_uint32_t msg_id;
    short (*handle)(_rt_msg_t curr_msg);
}control_msg_handle_process_t;

void* init_ctrl_manage(void* param);
void ctrl_manage_main(void* param);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _BMU_CONTROL_MANAGE_H