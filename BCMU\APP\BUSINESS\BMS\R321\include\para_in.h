#ifndef SOFTWARE_SRC_APP_PARA_IN_H_
#define SOFTWARE_SRC_APP_PARA_IN_H_
#include "common.h"

#ifdef __cplusplus
extern "C" {
#endif

/// 参数最大值数组
FLOAT	g_afMaxPara[PARA_NUM]	=
{
	3, // 充电过流告警阈值
	3, // 充电过流保护阈值
	3, // 放电过流告警阈值
	3, // 放电过流保护阈值
	4, // 电池组过压告警阈值,  使用单体值表示，其真实值应乘以电芯节数
	4, // 电池组过压保护阈值,  使用单体值表示，其真实值应乘以电芯节数
	127, // 单板过温保护阈值
	125, // 单板过温告警阈值
	80, // 环境温度高告警阈值
	10, // 环境温度低告警阈值
	3.3333, // 电池组欠压告警阈值,  使用单体值表示，其真实值应乘以电芯节数
	3.3333, // 电池组欠压保护阈值,  使用单体值表示，其真实值应乘以电芯节数
	4.5, // 单体过压告警阈值
	4.5, // 单体过压保护阈值
	3.2, // 单体欠压告警阈值
	3.2, // 单体欠压保护阈值
	70, // 充电高温告警阈值
	70, // 充电高温保护阈值
	70, // 放电高温告警阈值
	70, // 放电高温保护阈值
	10, // 充电低温告警阈值
	10, // 充电低温保护阈值
	10, // 放电低温告警阈值
	10, // 放电低温保护阈值
	1.5, // 单体一致性差告警阈值
	1.5, // 单体一致性差保护阈值
	60, // 电池SOC低告警阈值
	60, // 电池SOC低保护阈值
	85, // 电池SOH低告警阈值
	85, // 电池SOH低保护阈值
	2.5, // 单体损坏保护阈值
	3.73333, // 电池充满电压,  使用单体值表示，其真实值应乘以电芯节数
	0.1, // 电池充满电流
	2280, // 充电最长时间
	60, // 充电末期维持时间
	3.53333, // 电池补充电电压,  使用单体值表示，其真实值应乘以电芯节数
	180, // 电池补充电周期
	0.1, // 单体均衡启动压差阈值
	2, // 充电最大电流
	1, // 软关机使能
	1440, // 历史数据保存间隔
	60, // 系统停电电压阈值
	60, // 系统来电电压阈值
	1, // 蜂鸣器使能
	200, // 电池容量
	10, // 常规锂电充电限电流
	4320, // 软件防盗延时
	255, // 后台ip地址
	65535, // 后台端口
	'z', // GPRS用户名
	'z', // GPRS PW
	'z', // GPRS APN
	'z', // 告警手机号码-1#
	'z', // 告警手机号码-2#
	'z', // 告警手机号码-3#
	'z', // 告警手机号码-4#
	'z', // 告警手机号码-5#
	'z', // 短信中心号码
	'z', // 设备名称
	100, // 放电末期切换SOC1,  值随放电模式联动，恒压放电模式为0%，混用为10%
	1, // 运行模式
	3, // 放电模式
	1, // 循环模式
	53, // 铅酸切换电压
	58, // 恒压放电输出电压
	10, // 锂电铅酸放电比率
	100, // 单次放电DOD
	600, // 铅酸单次放电时间
	1, // 升压充电
	3, // 放电最大电流
	58, // 掉电电压阈值
	9, // 输出电压偏差阈值
	1, // 单体动态欠压保护
	2, // 设置告警属性,  告警干接点
	2, // 告警级别,  告警级别
	133499934, // 启用日期
	1, // 主用电池
	1, // 振动告警使能
	2, // 陀螺仪灵敏度
	65535, // 心跳周期
	1, // 整组欠压保护温度补偿
	'z', // BMS系统名称
	1, // 干接点默认状态
	1, // 放电方式
	1, // 休眠指示灯
	1, // 放电直通使能
	1, // 节能功能
	85, // 环境温度高保护阈值
	5, // 环境温度低保护阈值
	3.8, // 电池组过压告警恢复阈值,  使用单体值表示，其真实值应乘以电芯节数
	3.8, // 电池组过压保护恢复阈值,  参数实际未使用，范围不具有参考性。使用单体值表示，其真实值应乘以电芯节数
	3.9, // 单体过压告警恢复阈值
	3.9, // 单体过压保护恢复阈值
	3.6, // 单体欠压告警恢复阈值
	3.6, // 单体欠压保护恢复阈值
	60, // 充电高温告警恢复阈值
	60, // 充电高温保护恢复阈值
	65, // 放电高温告警恢复阈值
	65, // 放电高温保护恢复阈值
	13, // 充电低温告警恢复阈值
	13, // 充电低温保护恢复阈值
	13, // 放电低温告警恢复阈值
	13, // 放电低温保护恢复阈值
	75, // 环境温度高告警恢复阈值
	80, // 环境温度高保护恢复阈值
	15, // 环境温度低告警恢复阈值
	10, // 环境温度低保护恢复阈值
	110, // 单板过温告警恢复阈值
	110, // 单板过温保护恢复阈值
	1, // 恒压末期放电切换方式
	100, // 恒压末期放电切换SOC,  与放电末期切换SOC1统一，值随放电模式联动
	52.5, // 恒压末期放电切换电压
	50000, // GPS防盗距离
	1, // 陀螺仪防盗方式
	1, // 电池解锁方式
	1, // 充电轮换使能
	1, // 历史数据类型
	3.9, // 单体充电截止电压
	65535, // 静置关机时间
	1, // 电压激活使能
	254, // Modbus基地址
	1, // 加热垫使能
	255, // SNMP告警ip地址
	'z', // SNMP可读共同体
	'z', // SNMP设置共同体
	2, // SNMP V3用户等级
	'z', // SNMP V3用户名
	9, // SNMP V3鉴别PW
	9, // SNMP V3加密PW
	65535, // SNMP Trap端口号
	65535, // SNMP Agent端口号
	255, // 本机ip地址
	255, // 子网掩码
	255, // 网关
	1, // 本机ip获取方式
	255, // 远程升级后台ip地址
	65535, // 远程升级端口
	10, // 充电加热膜启动温度
	20, // 充电加热膜关闭温度
	80, // 加热膜过温阈值
	80, // 加热膜过温解除
	127, // 均流SOC补偿幅值
	127, // 均流SOC补偿斜率
	2, // 均流方式
	48, // 末期放电电压1
	48, // 末期放电电压2
	3.5, // 单体补充电电压
	300, // 来电判断时间
	1, // 充电MAP使能
	1, // 下垂均流使能
	2, // SNMP版本
	3, // SNMP V3认证算法
	2, // SNMP V3加密算法
	65535, // 故障前录波时间间隔
	65535, // 故障后录波时间间隔
	10, // 故障前录波条数
	10, // 故障后录波条数
	23, // 测点数量X
	255, // 可变测点ID,  备注：业务未使用参数用红色字体标出，蓝色字体为同步R311参数暂时未使用参数，黄色字体表示相较于FB100B3新增约束关系
	1, // 电池地址获取方式
	32, // 电池切换地址
	20, // 直流内阻异常告警阈值
	20, // 直流内阻异常保护阈值
	10, // 单体温升速率异常阈值
	20, // 自放电容量比率
	20, // 容量衰减一致性差告警比率阈值
	5500, // 最大充电功率
	5500, // 最大放电功率
	3, // 定位方式
	1, // 柜间地址获取方式
	20, // 柜间切换地址
	100, // 电池异常温度高保护阈值
	1, // 并机干接点同步使能
	10080, // 站点防盗延时时间
	255, // NTP服务器IP
	24, // 时区
	1, // 网络自动对时使能
	10080, // 网管防盗延时时间
	3, // 严重告警指示灯
	3, // 次要告警指示灯
	1, // 消防干接点控制使能
	1, // 充放电机模式
	58, // 德业逆变器充满电压
	50, // 德业逆变器放电下限电压
	4, // 电芯类型
	100, // 放电末期切换SOC2,  值随放电模式联动，恒压放电模式为0%，混用为10%
	10, // 放电加热膜启动温度
	20, // 放电加热膜关闭温度
	120, // 自动位置刷新周期
	240, // 陀螺仪防盗延时
	2, // NTC无效屏蔽路数
	51, // 自适应模式初始电压
	1, // 防水检测使能
}; 

/// 参数最小值数组
FLOAT	g_afMinPara[PARA_NUM]	=
{
	0.2, // 充电过流告警阈值
	0.3, // 充电过流保护阈值
	0.2, // 放电过流告警阈值
	0.3, // 放电过流保护阈值
	3.3, // 电池组过压告警阈值,  使用单体值表示，其真实值应乘以电芯节数
	3.3, // 电池组过压保护阈值,  使用单体值表示，其真实值应乘以电芯节数
	45, // 单板过温保护阈值
	45, // 单板过温告警阈值
	30, // 环境温度高告警阈值
	-40, // 环境温度低告警阈值
	2.6667, // 电池组欠压告警阈值,  使用单体值表示，其真实值应乘以电芯节数
	2.6667, // 电池组欠压保护阈值,  使用单体值表示，其真实值应乘以电芯节数
	3.2, // 单体过压告警阈值
	3.2, // 单体过压保护阈值
	2, // 单体欠压告警阈值
	2, // 单体欠压保护阈值
	35, // 充电高温告警阈值
	35, // 充电高温保护阈值
	35, // 放电高温告警阈值
	35, // 放电高温保护阈值
	-40, // 充电低温告警阈值
	-40, // 充电低温保护阈值
	-40, // 放电低温告警阈值
	-40, // 放电低温保护阈值
	0.05, // 单体一致性差告警阈值
	0.05, // 单体一致性差保护阈值
	0, // 电池SOC低告警阈值
	0, // 电池SOC低保护阈值
	0, // 电池SOH低告警阈值
	0, // 电池SOH低保护阈值
	1, // 单体损坏保护阈值
	3.46667, // 电池充满电压,  使用单体值表示，其真实值应乘以电芯节数
	0.01, // 电池充满电流
	0, // 充电最长时间
	1, // 充电末期维持时间
	3.26667, // 电池补充电电压,  使用单体值表示，其真实值应乘以电芯节数
	1, // 电池补充电周期
	0.01, // 单体均衡启动压差阈值
	0.03, // 充电最大电流
	0, // 软关机使能
	1, // 历史数据保存间隔
	42, // 系统停电电压阈值
	48, // 系统来电电压阈值
	0, // 蜂鸣器使能
	20, // 电池容量
	2, // 常规锂电充电限电流
	0, // 软件防盗延时
	0, // 后台ip地址
	0, // 后台端口
	' ', // GPRS用户名
	' ', // GPRS PW
	' ', // GPRS APN
	' ', // 告警手机号码-1#
	' ', // 告警手机号码-2#
	' ', // 告警手机号码-3#
	' ', // 告警手机号码-4#
	' ', // 告警手机号码-5#
	' ', // 短信中心号码
	' ', // 设备名称
	0, // 放电末期切换SOC1,  值随放电模式联动，恒压放电模式为0%，混用为10%
	0, // 运行模式
	0, // 放电模式
	0, // 循环模式
	42, // 铅酸切换电压
	47, // 恒压放电输出电压
	0, // 锂电铅酸放电比率
	10, // 单次放电DOD
	1, // 铅酸单次放电时间
	0, // 升压充电
	0.02, // 放电最大电流
	42, // 掉电电压阈值
	0.5, // 输出电压偏差阈值
	0, // 单体动态欠压保护
	0, // 设置告警属性,  告警干接点
	0, // 告警级别,  告警级别
	131727617, // 启用日期
	0, // 主用电池
	0, // 振动告警使能
	0, // 陀螺仪灵敏度
	1, // 心跳周期
	0, // 整组欠压保护温度补偿
	' ', // BMS系统名称
	0, // 干接点默认状态
	0, // 放电方式
	0, // 休眠指示灯
	0, // 放电直通使能
	0, // 节能功能
	35, // 环境温度高保护阈值
	-40, // 环境温度低保护阈值
	3.3333, // 电池组过压告警恢复阈值,  使用单体值表示，其真实值应乘以电芯节数
	3.3333, // 电池组过压保护恢复阈值,  参数实际未使用，范围不具有参考性。使用单体值表示，其真实值应乘以电芯节数
	3, // 单体过压告警恢复阈值
	3, // 单体过压保护恢复阈值
	2, // 单体欠压告警恢复阈值
	2, // 单体欠压保护恢复阈值
	32, // 充电高温告警恢复阈值
	32, // 充电高温保护恢复阈值
	32, // 放电高温告警恢复阈值
	32, // 放电高温保护恢复阈值
	0, // 充电低温告警恢复阈值
	0, // 充电低温保护恢复阈值
	-20, // 放电低温告警恢复阈值
	-20, // 放电低温保护恢复阈值
	25, // 环境温度高告警恢复阈值
	30, // 环境温度高保护恢复阈值
	-20, // 环境温度低告警恢复阈值
	-20, // 环境温度低保护恢复阈值
	30, // 单板过温告警恢复阈值
	30, // 单板过温保护恢复阈值
	0, // 恒压末期放电切换方式
	0, // 恒压末期放电切换SOC,  与放电末期切换SOC1统一，值随放电模式联动
	40, // 恒压末期放电切换电压
	10, // GPS防盗距离
	0, // 陀螺仪防盗方式
	0, // 电池解锁方式
	0, // 充电轮换使能
	0, // 历史数据类型
	3.4, // 单体充电截止电压
	1, // 静置关机时间
	0, // 电压激活使能
	0, // Modbus基地址
	0, // 加热垫使能
	0, // SNMP告警ip地址
	' ', // SNMP可读共同体
	' ', // SNMP设置共同体
	0, // SNMP V3用户等级
	' ', // SNMP V3用户名
	0, // SNMP V3鉴别PW
	0, // SNMP V3加密PW
	160, // SNMP Trap端口号
	160, // SNMP Agent端口号
	0, // 本机ip地址
	0, // 子网掩码
	0, // 网关
	0, // 本机ip获取方式
	0, // 远程升级后台ip地址
	0, // 远程升级端口
	-40, // 充电加热膜启动温度
	0, // 充电加热膜关闭温度
	30, // 加热膜过温阈值
	30, // 加热膜过温解除
	0, // 均流SOC补偿幅值
	0, // 均流SOC补偿斜率
	0, // 均流方式
	42, // 末期放电电压1
	42, // 末期放电电压2
	3.3, // 单体补充电电压
	10, // 来电判断时间
	0, // 充电MAP使能
	0, // 下垂均流使能
	0, // SNMP版本
	0, // SNMP V3认证算法
	0, // SNMP V3加密算法
	0, // 故障前录波时间间隔
	0, // 故障后录波时间间隔
	1, // 故障前录波条数
	1, // 故障后录波条数
	5, // 测点数量X
	0, // 可变测点ID,  备注：业务未使用参数用红色字体标出，蓝色字体为同步R311参数暂时未使用参数，黄色字体表示相较于FB100B3新增约束关系
	0, // 电池地址获取方式
	0, // 电池切换地址
	2, // 直流内阻异常告警阈值
	2, // 直流内阻异常保护阈值
	0.1, // 单体温升速率异常阈值
	0.1, // 自放电容量比率
	0.1, // 容量衰减一致性差告警比率阈值
	100, // 最大充电功率
	100, // 最大放电功率
	0, // 定位方式
	0, // 柜间地址获取方式
	0, // 柜间切换地址
	60, // 电池异常温度高保护阈值
	0, // 并机干接点同步使能
	120, // 站点防盗延时时间
	0, // NTP服务器IP
	0, // 时区
	0, // 网络自动对时使能
	120, // 网管防盗延时时间
	0, // 严重告警指示灯
	0, // 次要告警指示灯
	0, // 消防干接点控制使能
	0, // 充放电机模式
	50, // 德业逆变器充满电压
	40, // 德业逆变器放电下限电压
	0, // 电芯类型
	0, // 放电末期切换SOC2,  值随放电模式联动，恒压放电模式为0%，混用为10%
	-40, // 放电加热膜启动温度
	-40, // 放电加热膜关闭温度
	5, // 自动位置刷新周期
	0, // 陀螺仪防盗延时
	0, // NTC无效屏蔽路数
	42, // 自适应模式初始电压
	0, // 防水检测使能
}; 

/// 参数默认值
T_SysPara	s_tDefaultPara	= 
{
	1.1, // 充电过流告警阈值
	1.1, // 充电过流保护阈值
	1.2, // 放电过流告警阈值
	1.25, // 放电过流保护阈值
	3.6, // 电池组过压告警阈值,  使用单体值表示，其真实值应乘以电芯节数
	3.6533, // 电池组过压保护阈值,  使用单体值表示，其真实值应乘以电芯节数
	120, // 单板过温保护阈值
	85, // 单板过温告警阈值
	80, // 环境温度高告警阈值
	-20, // 环境温度低告警阈值
	3.0667, // 电池组欠压告警阈值,  使用单体值表示，其真实值应乘以电芯节数
	3, // 电池组欠压保护阈值,  使用单体值表示，其真实值应乘以电芯节数
	3.75, // 单体过压告警阈值
	3.8, // 单体过压保护阈值
	3, // 单体欠压告警阈值
	2.5, // 单体欠压保护阈值
	55, // 充电高温告警阈值
	60, // 充电高温保护阈值
	60, // 放电高温告警阈值
	65, // 放电高温保护阈值
	5, // 充电低温告警阈值
	0, // 充电低温保护阈值
	-15, // 放电低温告警阈值
	-20, // 放电低温保护阈值
	0.3, // 单体一致性差告警阈值
	0.6, // 单体一致性差保护阈值
	20, // 电池SOC低告警阈值
	15, // 电池SOC低保护阈值
	50, // 电池SOH低告警阈值
	30, // 电池SOH低保护阈值
	1.5, // 单体损坏保护阈值
	3.5, // 电池充满电压,  使用单体值表示，其真实值应乘以电芯节数
	0.05, // 电池充满电流
	900, // 充电最长时间
	1, // 充电末期维持时间
	3.35, // 电池补充电电压,  使用单体值表示，其真实值应乘以电芯节数
	90, // 电池补充电周期
	0.02, // 单体均衡启动压差阈值
	0.1, // 充电最大电流
	1, // 软关机使能
	480, // 历史数据保存间隔
	48, // 系统停电电压阈值
	52, // 系统来电电压阈值
	1, // 蜂鸣器使能
	100, // 电池容量
	10, // 常规锂电充电限电流
	10, // 软件防盗延时
	{'0', '.', '0', '.', '0', '.', '0', } , // 后台ip地址
	0, // 后台端口
	{'s', 'm', 's', 'o', 'n', 'g', } , // GPRS用户名
	{'1', '2', '3', '4', '5', '6', } , // GPRS PW
	{'C', 'M', 'N', 'E', 'T', } , // GPRS APN
	{'+', '8', '6', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', } , // 告警手机号码-1#
	{'+', '8', '6', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', } , // 告警手机号码-2#
	{'+', '8', '6', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', } , // 告警手机号码-3#
	{'+', '8', '6', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', } , // 告警手机号码-4#
	{'+', '8', '6', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', } , // 告警手机号码-5#
	{'+', '8', '6', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', } , // 短信中心号码
	{'Z', 'T', 'E', ' ', 'B', 'M', 'S', } , // 设备名称
	10, // 放电末期切换SOC1,  值随放电模式联动，恒压放电模式为0%，混用为10%
	0, // 运行模式
	1, // 放电模式
	0, // 循环模式
	47, // 铅酸切换电压
	56, // 恒压放电输出电压
	4, // 锂电铅酸放电比率
	70, // 单次放电DOD
	5, // 铅酸单次放电时间
	0, // 升压充电
	1.05, // 放电最大电流
	48, // 掉电电压阈值
	0.9, // 输出电压偏差阈值
	1, // 单体动态欠压保护
	/// 告警干接点默认值
	{
		0, // 无,  充电过流保护
		0, // 无,  放电过流保护
		0, // 无,  电池组过压保护
		1, // A1,  单板过温保护
		0, // 无,  充电过流告警
		0, // 无,  放电过流告警
		0, // 无,  电池组过压告警
		0, // 无,  环境温度高告警
		0, // 无,  环境温度低告警
		0, // 无,  电池组欠压告警
		0, // 无,  电池组欠压保护
		0, // 无,  单体过压告警
		0, // 无,  单体过压保护
		0, // 无,  单体欠压告警
		0, // 无,  单体欠压保护
		0, // 无,  单体充电高温告警
		0, // 无,  单体充电高温保护
		0, // 无,  单体放电高温告警
		0, // 无,  单体放电高温保护
		0, // 无,  单体充电低温告警
		0, // 无,  单体充电低温保护
		0, // 无,  单体放电低温告警
		0, // 无,  单体放电低温保护
		0, // 无,  单体一致性差告警
		0, // 无,  单体一致性差保护
		0, // 无,  电池SOC低告警
		0, // 无,  电池SOC低保护
		0, // 无,  电池SOH低告警
		1, // A1,  电池SOH低保护
		1, // A1,  单体损坏保护
		0, // 无,  电池丢失告警
		1, // A1,  BDU EEPROM故障
		1, // A1,  单体电压采样异常
		1, // A1,  充电回路失效
		1, // A1,  放电回路失效
		1, // A1,  限流回路失效
		0, // 无,  电池短路
		0, // 无,  电池反接
		1, // A1,  单体温度无效
		0, // 无,  机内过温保护
		0, // 无,  BDU电池欠压保护
		0, // 无,  单体温度异常
		0, // 无,  地址冲突
		0, // 无,  振动告警
		0, // 无,  BDU母排欠压保护
		0, // 无,  BDU母排过压保护
		1, // A1,  BDU通信断
		0, // 无,  电压采样故障
		0, // 无,  回路异常
		1, // A1,  BDU电池充电欠压保护
		1, // A1,  BDU电池闭锁告警
		0, // 无,  环境温度高保护
		0, // 无,  环境温度低保护
		0, // 无,  单板过温告警
		1, // A1,  均衡电路故障告警
		1, // A1,  均衡电阻温度高保护
		0, // 无,  加热膜失效
		1, // A1,  连接器温度高保护
		1, // A1,  主继电器失效
		1, // A1,  DCDC故障
		1, // A1,  采集异常
		1, // A1,  辅助源故障
		0, // 无,  单体动态欠压保护
		1, // A1,  消防告警
		1, // A1,  激活回路电流异常保护
		0, // 无,  单体温升速率异常
		1, // A1,  直流内阻异常告警
		1, // A1,  直流内阻异常保护
		0, // 无,  自放电异常
		0, // 无,  容量衰减一致性差告警
		0, // 无,  电池异常高温保护告警
		0, // 无,  消防故障告警
		0, // 无,  激活口反接告警
		0, // 无,  站点防盗告警
		0, // 无,  GPS故障告警
		0, // 无,  陀螺仪故障告警
		0, // 无,  网管防盗告警
		0, // 无,  电池单体损坏保护
		0, // 无,  电池单体温度无效
		0, // 无,  防水告警
	},

	/// 告警级别默认值
	{
		0, // 屏蔽,  充电过流保护
		1, // 严重,  放电过流保护
		1, // 严重,  电池组过压保护
		1, // 严重,  单板过温保护
		0, // 屏蔽,  充电过流告警
		1, // 严重,  放电过流告警
		0, // 屏蔽,  电池组过压告警
		0, // 屏蔽,  环境温度高告警
		0, // 屏蔽,  环境温度低告警
		0, // 屏蔽,  电池组欠压告警
		1, // 严重,  电池组欠压保护
		0, // 屏蔽,  单体过压告警
		1, // 严重,  单体过压保护
		0, // 屏蔽,  单体欠压告警
		1, // 严重,  单体欠压保护
		0, // 屏蔽,  单体充电高温告警
		1, // 严重,  单体充电高温保护
		1, // 严重,  单体放电高温告警
		1, // 严重,  单体放电高温保护
		0, // 屏蔽,  单体充电低温告警
		1, // 严重,  单体充电低温保护
		1, // 严重,  单体放电低温告警
		1, // 严重,  单体放电低温保护
		0, // 屏蔽,  单体一致性差告警
		1, // 严重,  单体一致性差保护
		0, // 屏蔽,  电池SOC低告警
		0, // 屏蔽,  电池SOC低保护
		1, // 严重,  电池SOH低告警
		1, // 严重,  电池SOH低保护
		1, // 严重,  单体损坏保护
		1, // 严重,  电池丢失告警
		1, // 严重,  BDU EEPROM故障
		1, // 严重,  单体电压采样异常
		1, // 严重,  充电回路失效
		1, // 严重,  放电回路失效
		1, // 严重,  限流回路失效
		1, // 严重,  电池短路
		1, // 严重,  电池反接
		1, // 严重,  单体温度无效
		1, // 严重,  机内过温保护
		1, // 严重,  BDU电池欠压保护
		1, // 严重,  单体温度异常
		0, // 屏蔽,  地址冲突
		0, // 屏蔽,  振动告警
		1, // 严重,  BDU母排欠压保护
		1, // 严重,  BDU母排过压保护
		1, // 严重,  BDU通信断
		1, // 严重,  电压采样故障
		1, // 严重,  回路异常
		1, // 严重,  BDU电池充电欠压保护
		1, // 严重,  BDU电池闭锁告警
		1, // 严重,  环境温度高保护
		1, // 严重,  环境温度低保护
		0, // 屏蔽,  单板过温告警
		1, // 严重,  均衡电路故障告警
		1, // 严重,  均衡电阻温度高保护
		1, // 严重,  加热膜失效
		1, // 严重,  连接器温度高保护
		1, // 严重,  主继电器失效
		1, // 严重,  DCDC故障
		1, // 严重,  采集异常
		1, // 严重,  辅助源故障
		1, // 严重,  单体动态欠压保护
		1, // 严重,  消防告警
		1, // 严重,  激活回路电流异常保护
		1, // 严重,  单体温升速率异常
		1, // 严重,  直流内阻异常告警
		1, // 严重,  直流内阻异常保护
		1, // 严重,  自放电异常
		1, // 严重,  容量衰减一致性差告警
		1, // 严重,  电池异常高温保护告警
		1, // 严重,  消防故障告警
		1, // 严重,  激活口反接告警
		1, // 严重,  站点防盗告警
		1, // 严重,  GPS故障告警
		1, // 严重,  陀螺仪故障告警
		1, // 严重,  网管防盗告警
		1, // 严重,  电池单体损坏保护
		1, // 严重,  电池单体温度无效
		1, // 严重,  防水告警
	}, 

	{2010,1,1}, // 启用日期
	1, // 主用电池
	0, // 振动告警使能
	2, // 陀螺仪灵敏度
	1, // 心跳周期
	1, // 整组欠压保护温度补偿
	{'Z', 'X', 'E', 'S', 'M', ' ', 'R', '3', '2', '1', } , // BMS系统名称
	1, // 干接点默认状态
	0, // 放电方式
	1, // 休眠指示灯
	0, // 放电直通使能
	0, // 节能功能
	85, // 环境温度高保护阈值
	-40, // 环境温度低保护阈值
	3.35, // 电池组过压告警恢复阈值,  使用单体值表示，其真实值应乘以电芯节数
	3.52, // 电池组过压保护恢复阈值,  参数实际未使用，范围不具有参考性。使用单体值表示，其真实值应乘以电芯节数
	3.5, // 单体过压告警恢复阈值
	3.5, // 单体过压保护恢复阈值
	3.2, // 单体欠压告警恢复阈值
	3.4, // 单体欠压保护恢复阈值
	52, // 充电高温告警恢复阈值
	57, // 充电高温保护恢复阈值
	57, // 放电高温告警恢复阈值
	62, // 放电高温保护恢复阈值
	8, // 充电低温告警恢复阈值
	3, // 充电低温保护恢复阈值
	-12, // 放电低温告警恢复阈值
	-17, // 放电低温保护恢复阈值
	75, // 环境温度高告警恢复阈值
	80, // 环境温度高保护恢复阈值
	-15, // 环境温度低告警恢复阈值
	-20, // 环境温度低保护恢复阈值
	68, // 单板过温告警恢复阈值
	100, // 单板过温保护恢复阈值
	1, // 恒压末期放电切换方式
	0, // 恒压末期放电切换SOC,  与放电末期切换SOC1统一，值随放电模式联动
	40, // 恒压末期放电切换电压
	200, // GPS防盗距离
	0, // 陀螺仪防盗方式
	0, // 电池解锁方式
	0, // 充电轮换使能
	0, // 历史数据类型
	3.65, // 单体充电截止电压
	1440, // 静置关机时间
	0, // 电压激活使能
	0, // Modbus基地址
	1, // 加热垫使能
	{'0', '.', '0', '.', '0', '.', '0', } , // SNMP告警ip地址
	{'p', 'u', 'b', 'l', 'i', 'c', } , // SNMP可读共同体
	{'p', 'u', 'b', 'l', 'i', 'c', } , // SNMP设置共同体
	1, // SNMP V3用户等级
	{'u', 's', 'e', 'r', } , // SNMP V3用户名
	{0x3A, 0x52, 0xC1, 0x01, 0x4D, 0x97, 0x44, 0x39, 0x74, 0xEE, 0xE8, 0x4D, 0x0F, 0x8E, 0x4A, 0x96, } , // SNMP V3鉴别PW
	{0x3A, 0x52, 0xC1, 0x01, 0x4D, 0x97, 0x44, 0x39, 0x74, 0xEE, 0xE8, 0x4D, 0x0F, 0x8E, 0x4A, 0x96, } , // SNMP V3加密PW
	162, // SNMP Trap端口号
	161, // SNMP Agent端口号
	{'1', '9', '2', '.', '1', '6', '8', '.', '1', '.', '2', } , // 本机ip地址
	{'2', '5', '5', '.', '2', '5', '5', '.', '2', '5', '5', '.', '0', } , // 子网掩码
	{'1', '9', '2', '.', '1', '6', '8', '.', '1', '.', '1', } , // 网关
	0, // 本机ip获取方式
	{'0', '.', '0', '.', '0', '.', '0', } , // 远程升级后台ip地址
	0, // 远程升级端口
	0, // 充电加热膜启动温度
	16, // 充电加热膜关闭温度
	45, // 加热膜过温阈值
	30, // 加热膜过温解除
	5, // 均流SOC补偿幅值
	2, // 均流SOC补偿斜率
	0, // 均流方式
	45.8, // 末期放电电压1
	43.8, // 末期放电电压2
	3.34, // 单体补充电电压
	60, // 来电判断时间
	1, // 充电MAP使能
	0, // 下垂均流使能
	2, // SNMP版本
	3, // SNMP V3认证算法
	2, // SNMP V3加密算法
	120, // 故障前录波时间间隔
	200, // 故障后录波时间间隔
	7, // 故障前录波条数
	3, // 故障后录波条数
	10, // 测点数量X
	/// 可变测点ID默认值
	{
		1,
		2,
		3,
		4,
		5,
		6,
		7,
		8,
		9,
		10,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
	},

	0, // 电池地址获取方式
	0, // 电池切换地址
	5, // 直流内阻异常告警阈值
	10, // 直流内阻异常保护阈值
	1, // 单体温升速率异常阈值
	2, // 自放电容量比率
	2, // 容量衰减一致性差告警比率阈值
	5500, // 最大充电功率
	5500, // 最大放电功率
	1, // 定位方式
	0, // 柜间地址获取方式
	0, // 柜间切换地址
	70, // 电池异常温度高保护阈值
	1, // 并机干接点同步使能
	10080, // 站点防盗延时时间
	{'1', '9', '2', '.', '1', '6', '8', '.', '1', '.', '1', '0', } , // NTP服务器IP
	8, // 时区
	0, // 网络自动对时使能
	10080, // 网管防盗延时时间
	1, // 严重告警指示灯
	1, // 次要告警指示灯
	0, // 消防干接点控制使能
	0, // 充放电机模式
	52.5, // 德业逆变器充满电压
	42, // 德业逆变器放电下限电压
	0, // 电芯类型
	10, // 放电末期切换SOC2,  值随放电模式联动，恒压放电模式为0%，混用为10%
	0, // 放电加热膜启动温度
	16, // 放电加热膜关闭温度
	30, // 自动位置刷新周期
	0, // 陀螺仪防盗延时
	2, // NTC无效屏蔽路数
	43.8, // 自适应模式初始电压
	0, // 防水检测使能
}; 

/// 参数占用字节数数组
const   BYTE	g_aucParaSpace[] =
{
	4, // 充电过流告警阈值
	4, // 充电过流保护阈值
	4, // 放电过流告警阈值
	4, // 放电过流保护阈值
	4, // 电池组过压告警阈值,  使用单体值表示，其真实值应乘以电芯节数
	4, // 电池组过压保护阈值,  使用单体值表示，其真实值应乘以电芯节数
	4, // 单板过温保护阈值
	4, // 单板过温告警阈值
	4, // 环境温度高告警阈值
	4, // 环境温度低告警阈值
	4, // 电池组欠压告警阈值,  使用单体值表示，其真实值应乘以电芯节数
	4, // 电池组欠压保护阈值,  使用单体值表示，其真实值应乘以电芯节数
	4, // 单体过压告警阈值
	4, // 单体过压保护阈值
	4, // 单体欠压告警阈值
	4, // 单体欠压保护阈值
	4, // 充电高温告警阈值
	4, // 充电高温保护阈值
	4, // 放电高温告警阈值
	4, // 放电高温保护阈值
	4, // 充电低温告警阈值
	4, // 充电低温保护阈值
	4, // 放电低温告警阈值
	4, // 放电低温保护阈值
	4, // 单体一致性差告警阈值
	4, // 单体一致性差保护阈值
	2, // 电池SOC低告警阈值
	2, // 电池SOC低保护阈值
	2, // 电池SOH低告警阈值
	2, // 电池SOH低保护阈值
	4, // 单体损坏保护阈值
	4, // 电池充满电压,  使用单体值表示，其真实值应乘以电芯节数
	4, // 电池充满电流
	2, // 充电最长时间
	2, // 充电末期维持时间
	4, // 电池补充电电压,  使用单体值表示，其真实值应乘以电芯节数
	2, // 电池补充电周期
	4, // 单体均衡启动压差阈值
	4, // 充电最大电流
	1, // 软关机使能
	2, // 历史数据保存间隔
	4, // 系统停电电压阈值
	4, // 系统来电电压阈值
	1, // 蜂鸣器使能
	2, // 电池容量
	4, // 常规锂电充电限电流
	2, // 软件防盗延时
	LEN_TYPE_STRING_20, // 后台ip地址
	2, // 后台端口
	LEN_TYPE_STRING_20, // GPRS用户名
	LEN_TYPE_STRING_10, // GPRS PW
	LEN_TYPE_STRING_20, // GPRS APN
	LEN_TYPE_STRING_20, // 告警手机号码-1#
	LEN_TYPE_STRING_20, // 告警手机号码-2#
	LEN_TYPE_STRING_20, // 告警手机号码-3#
	LEN_TYPE_STRING_20, // 告警手机号码-4#
	LEN_TYPE_STRING_20, // 告警手机号码-5#
	LEN_TYPE_STRING_20, // 短信中心号码
	LEN_TYPE_STRING_32, // 设备名称
	2, // 放电末期切换SOC1,  值随放电模式联动，恒压放电模式为0%，混用为10%
	1, // 运行模式
	1, // 放电模式
	1, // 循环模式
	4, // 铅酸切换电压
	4, // 恒压放电输出电压
	1, // 锂电铅酸放电比率
	1, // 单次放电DOD
	2, // 铅酸单次放电时间
	1, // 升压充电
	4, // 放电最大电流
	4, // 掉电电压阈值
	4, // 输出电压偏差阈值
	1, // 单体动态欠压保护
	ALARM_CLASS, // 设置告警属性,  告警干接点
	ALARM_CLASS, // 告警级别,  告警级别
	4, // 启用日期
	1, // 主用电池
	1, // 振动告警使能
	1, // 陀螺仪灵敏度
	2, // 心跳周期
	1, // 整组欠压保护温度补偿
	LEN_TYPE_STRING_20, // BMS系统名称
	1, // 干接点默认状态
	1, // 放电方式
	1, // 休眠指示灯
	1, // 放电直通使能
	1, // 节能功能
	4, // 环境温度高保护阈值
	4, // 环境温度低保护阈值
	4, // 电池组过压告警恢复阈值,  使用单体值表示，其真实值应乘以电芯节数
	4, // 电池组过压保护恢复阈值,  参数实际未使用，范围不具有参考性。使用单体值表示，其真实值应乘以电芯节数
	4, // 单体过压告警恢复阈值
	4, // 单体过压保护恢复阈值
	4, // 单体欠压告警恢复阈值
	4, // 单体欠压保护恢复阈值
	4, // 充电高温告警恢复阈值
	4, // 充电高温保护恢复阈值
	4, // 放电高温告警恢复阈值
	4, // 放电高温保护恢复阈值
	4, // 充电低温告警恢复阈值
	4, // 充电低温保护恢复阈值
	4, // 放电低温告警恢复阈值
	4, // 放电低温保护恢复阈值
	4, // 环境温度高告警恢复阈值
	4, // 环境温度高保护恢复阈值
	4, // 环境温度低告警恢复阈值
	4, // 环境温度低保护恢复阈值
	4, // 单板过温告警恢复阈值
	4, // 单板过温保护恢复阈值
	1, // 恒压末期放电切换方式
	1, // 恒压末期放电切换SOC,  与放电末期切换SOC1统一，值随放电模式联动
	4, // 恒压末期放电切换电压
	2, // GPS防盗距离
	1, // 陀螺仪防盗方式
	1, // 电池解锁方式
	1, // 充电轮换使能
	1, // 历史数据类型
	4, // 单体充电截止电压
	2, // 静置关机时间
	1, // 电压激活使能
	1, // Modbus基地址
	1, // 加热垫使能
	LEN_TYPE_STRING_16, // SNMP告警ip地址
	LEN_TYPE_STRING_16, // SNMP可读共同体
	LEN_TYPE_STRING_16, // SNMP设置共同体
	1, // SNMP V3用户等级
	LEN_TYPE_STRING_16, // SNMP V3用户名
	LEN_TYPE_STRING_16, // SNMP V3鉴别PW
	LEN_TYPE_STRING_16, // SNMP V3加密PW
	2, // SNMP Trap端口号
	2, // SNMP Agent端口号
	LEN_TYPE_STRING_16, // 本机ip地址
	LEN_TYPE_STRING_16, // 子网掩码
	LEN_TYPE_STRING_16, // 网关
	1, // 本机ip获取方式
	LEN_TYPE_STRING_16, // 远程升级后台ip地址
	2, // 远程升级端口
	4, // 充电加热膜启动温度
	4, // 充电加热膜关闭温度
	4, // 加热膜过温阈值
	4, // 加热膜过温解除
	1, // 均流SOC补偿幅值
	1, // 均流SOC补偿斜率
	1, // 均流方式
	4, // 末期放电电压1
	4, // 末期放电电压2
	4, // 单体补充电电压
	2, // 来电判断时间
	1, // 充电MAP使能
	1, // 下垂均流使能
	1, // SNMP版本
	1, // SNMP V3认证算法
	1, // SNMP V3加密算法
	2, // 故障前录波时间间隔
	2, // 故障后录波时间间隔
	1, // 故障前录波条数
	1, // 故障后录波条数
	1, // 测点数量X
	NUM_OF_MEASUREPOINTS, // 可变测点ID,  备注：业务未使用参数用红色字体标出，蓝色字体为同步R311参数暂时未使用参数，黄色字体表示相较于FB100B3新增约束关系
	1, // 电池地址获取方式
	1, // 电池切换地址
	1, // 直流内阻异常告警阈值
	1, // 直流内阻异常保护阈值
	4, // 单体温升速率异常阈值
	4, // 自放电容量比率
	4, // 容量衰减一致性差告警比率阈值
	2, // 最大充电功率
	2, // 最大放电功率
	1, // 定位方式
	1, // 柜间地址获取方式
	1, // 柜间切换地址
	4, // 电池异常温度高保护阈值
	1, // 并机干接点同步使能
	2, // 站点防盗延时时间
	LEN_TYPE_STRING_16, // NTP服务器IP
	1, // 时区
	1, // 网络自动对时使能
	2, // 网管防盗延时时间
	1, // 严重告警指示灯
	1, // 次要告警指示灯
	1, // 消防干接点控制使能
	1, // 充放电机模式
	4, // 德业逆变器充满电压
	4, // 德业逆变器放电下限电压
	1, // 电芯类型
	2, // 放电末期切换SOC2,  值随放电模式联动，恒压放电模式为0%，混用为10%
	4, // 放电加热膜启动温度
	4, // 放电加热膜关闭温度
	1, // 自动位置刷新周期
	1, // 陀螺仪防盗延时
	1, // NTC无效屏蔽路数
	4, // 自适应模式初始电压
	1, // 防水检测使能
}; 

/// 参数单位数组
const CHAR	g_acParaUints[PARA_NUM][10] =
{
	"C3", // 充电过流告警阈值
	"C3", // 充电过流保护阈值
	"C3", // 放电过流告警阈值
	"C3", // 放电过流保护阈值
	"V", // 电池组过压告警阈值,  使用单体值表示，其真实值应乘以电芯节数
	"V", // 电池组过压保护阈值,  使用单体值表示，其真实值应乘以电芯节数
	"℃", // 单板过温保护阈值
	"℃", // 单板过温告警阈值
	"℃", // 环境温度高告警阈值
	"℃", // 环境温度低告警阈值
	"V", // 电池组欠压告警阈值,  使用单体值表示，其真实值应乘以电芯节数
	"V", // 电池组欠压保护阈值,  使用单体值表示，其真实值应乘以电芯节数
	"V", // 单体过压告警阈值
	"V", // 单体过压保护阈值
	"V", // 单体欠压告警阈值
	"V", // 单体欠压保护阈值
	"℃", // 充电高温告警阈值
	"℃", // 充电高温保护阈值
	"℃", // 放电高温告警阈值
	"℃", // 放电高温保护阈值
	"℃", // 充电低温告警阈值
	"℃", // 充电低温保护阈值
	"℃", // 放电低温告警阈值
	"℃", // 放电低温保护阈值
	"V", // 单体一致性差告警阈值
	"V", // 单体一致性差保护阈值
	"%", // 电池SOC低告警阈值
	"%", // 电池SOC低保护阈值
	"%", // 电池SOH低告警阈值
	"%", // 电池SOH低保护阈值
	"V", // 单体损坏保护阈值
	"V", // 电池充满电压,  使用单体值表示，其真实值应乘以电芯节数
	"C3", // 电池充满电流
	"min", // 充电最长时间
	"min", // 充电末期维持时间
	"V", // 电池补充电电压,  使用单体值表示，其真实值应乘以电芯节数
	"day", // 电池补充电周期
	"V", // 单体均衡启动压差阈值
	"C3", // 充电最大电流
	{0}, // 软关机使能
	"Min", // 历史数据保存间隔
	"V", // 系统停电电压阈值
	"V", // 系统来电电压阈值
	{0}, // 蜂鸣器使能
	"AH", // 电池容量
	"A", // 常规锂电充电限电流
	"Min", // 软件防盗延时
	{0}, // 后台ip地址
	{0}, // 后台端口
	{0}, // GPRS用户名
	{0}, // GPRS PW
	{0}, // GPRS APN
	{0}, // 告警手机号码-1#
	{0}, // 告警手机号码-2#
	{0}, // 告警手机号码-3#
	{0}, // 告警手机号码-4#
	{0}, // 告警手机号码-5#
	{0}, // 短信中心号码
	{0}, // 设备名称
	{0}, // 放电末期切换SOC1,  值随放电模式联动，恒压放电模式为0%，混用为10%
	{0}, // 运行模式
	{0}, // 放电模式
	{0}, // 循环模式
	"V", // 铅酸切换电压
	"V", // 恒压放电输出电压
	":1", // 锂电铅酸放电比率
	"%", // 单次放电DOD
	"Min", // 铅酸单次放电时间
	{0}, // 升压充电
	"C3", // 放电最大电流
	"V", // 掉电电压阈值
	"V", // 输出电压偏差阈值
	{0}, // 单体动态欠压保护
	{0}, // 设置告警属性,  告警干接点
	{0}, // 告警级别,  告警级别
	{0}, // 启用日期
	{0}, // 主用电池
	{0}, // 振动告警使能
	{0}, // 陀螺仪灵敏度
	"Min", // 心跳周期
	{0}, // 整组欠压保护温度补偿
	{0}, // BMS系统名称
	{0}, // 干接点默认状态
	{0}, // 放电方式
	{0}, // 休眠指示灯
	{0}, // 放电直通使能
	{0}, // 节能功能
	"℃", // 环境温度高保护阈值
	"℃", // 环境温度低保护阈值
	"V", // 电池组过压告警恢复阈值,  使用单体值表示，其真实值应乘以电芯节数
	"V", // 电池组过压保护恢复阈值,  参数实际未使用，范围不具有参考性。使用单体值表示，其真实值应乘以电芯节数
	"V", // 单体过压告警恢复阈值
	"V", // 单体过压保护恢复阈值
	"V", // 单体欠压告警恢复阈值
	"V", // 单体欠压保护恢复阈值
	"℃", // 充电高温告警恢复阈值
	"℃", // 充电高温保护恢复阈值
	"℃", // 放电高温告警恢复阈值
	"℃", // 放电高温保护恢复阈值
	"℃", // 充电低温告警恢复阈值
	"℃", // 充电低温保护恢复阈值
	"℃", // 放电低温告警恢复阈值
	"℃", // 放电低温保护恢复阈值
	"℃", // 环境温度高告警恢复阈值
	"℃", // 环境温度高保护恢复阈值
	"℃", // 环境温度低告警恢复阈值
	"℃", // 环境温度低保护恢复阈值
	"℃", // 单板过温告警恢复阈值
	"℃", // 单板过温保护恢复阈值
	{0}, // 恒压末期放电切换方式
	{0}, // 恒压末期放电切换SOC,  与放电末期切换SOC1统一，值随放电模式联动
	"V", // 恒压末期放电切换电压
	"m", // GPS防盗距离
	{0}, // 陀螺仪防盗方式
	{0}, // 电池解锁方式
	{0}, // 充电轮换使能
	{0}, // 历史数据类型
	"V", // 单体充电截止电压
	"min", // 静置关机时间
	{0}, // 电压激活使能
	{0}, // Modbus基地址
	{0}, // 加热垫使能
	{0}, // SNMP告警ip地址
	{0}, // SNMP可读共同体
	{0}, // SNMP设置共同体
	{0}, // SNMP V3用户等级
	{0}, // SNMP V3用户名
	{0}, // SNMP V3鉴别PW
	{0}, // SNMP V3加密PW
	{0}, // SNMP Trap端口号
	{0}, // SNMP Agent端口号
	{0}, // 本机ip地址
	{0}, // 子网掩码
	{0}, // 网关
	{0}, // 本机ip获取方式
	{0}, // 远程升级后台ip地址
	{0}, // 远程升级端口
	"℃", // 充电加热膜启动温度
	"℃", // 充电加热膜关闭温度
	"℃", // 加热膜过温阈值
	"℃", // 加热膜过温解除
	{0}, // 均流SOC补偿幅值
	{0}, // 均流SOC补偿斜率
	{0}, // 均流方式
	"V", // 末期放电电压1
	"V", // 末期放电电压2
	"V", // 单体补充电电压
	"s", // 来电判断时间
	{0}, // 充电MAP使能
	{0}, // 下垂均流使能
	{0}, // SNMP版本
	{0}, // SNMP V3认证算法
	{0}, // SNMP V3加密算法
	"us", // 故障前录波时间间隔
	"us", // 故障后录波时间间隔
	{0}, // 故障前录波条数
	{0}, // 故障后录波条数
	{0}, // 测点数量X
	{0}, // 可变测点ID,  备注：业务未使用参数用红色字体标出，蓝色字体为同步R311参数暂时未使用参数，黄色字体表示相较于FB100B3新增约束关系
	{0}, // 电池地址获取方式
	{0}, // 电池切换地址
	{0}, // 直流内阻异常告警阈值
	{0}, // 直流内阻异常保护阈值
	"℃", // 单体温升速率异常阈值
	"%", // 自放电容量比率
	"%", // 容量衰减一致性差告警比率阈值
	{0}, // 最大充电功率
	{0}, // 最大放电功率
	{0}, // 定位方式
	{0}, // 柜间地址获取方式
	{0}, // 柜间切换地址
	"℃", // 电池异常温度高保护阈值
	{0}, // 并机干接点同步使能
	"min", // 站点防盗延时时间
	{0}, // NTP服务器IP
	{0}, // 时区
	{0}, // 网络自动对时使能
	"min", // 网管防盗延时时间
	{0}, // 严重告警指示灯
	{0}, // 次要告警指示灯
	{0}, // 消防干接点控制使能
	{0}, // 充放电机模式
	"V", // 德业逆变器充满电压
	"V", // 德业逆变器放电下限电压
	{0}, // 电芯类型
	{0}, // 放电末期切换SOC2,  值随放电模式联动，恒压放电模式为0%，混用为10%
	"℃", // 放电加热膜启动温度
	"℃", // 放电加热膜关闭温度
	"s", // 自动位置刷新周期
	"min", // 陀螺仪防盗延时
	{0}, // NTC无效屏蔽路数
	"V", // 自适应模式初始电压
	{0}, // 防水检测使能
}; 

/// 参数精度数组
const   BYTE	g_aucParaWei[] =
{
	2, // 充电过流告警阈值
	2, // 充电过流保护阈值
	2, // 放电过流告警阈值
	2, // 放电过流保护阈值
	2, // 电池组过压告警阈值,  使用单体值表示，其真实值应乘以电芯节数
	2, // 电池组过压保护阈值,  使用单体值表示，其真实值应乘以电芯节数
	1, // 单板过温保护阈值
	1, // 单板过温告警阈值
	1, // 环境温度高告警阈值
	1, // 环境温度低告警阈值
	2, // 电池组欠压告警阈值,  使用单体值表示，其真实值应乘以电芯节数
	2, // 电池组欠压保护阈值,  使用单体值表示，其真实值应乘以电芯节数
	3, // 单体过压告警阈值
	3, // 单体过压保护阈值
	3, // 单体欠压告警阈值
	3, // 单体欠压保护阈值
	1, // 充电高温告警阈值
	1, // 充电高温保护阈值
	1, // 放电高温告警阈值
	1, // 放电高温保护阈值
	1, // 充电低温告警阈值
	1, // 充电低温保护阈值
	1, // 放电低温告警阈值
	1, // 放电低温保护阈值
	2, // 单体一致性差告警阈值
	2, // 单体一致性差保护阈值
	0, // 电池SOC低告警阈值
	0, // 电池SOC低保护阈值
	0, // 电池SOH低告警阈值
	0, // 电池SOH低保护阈值
	2, // 单体损坏保护阈值
	3, // 电池充满电压,  使用单体值表示，其真实值应乘以电芯节数
	2, // 电池充满电流
	0, // 充电最长时间
	0, // 充电末期维持时间
	2, // 电池补充电电压,  使用单体值表示，其真实值应乘以电芯节数
	0, // 电池补充电周期
	2, // 单体均衡启动压差阈值
	2, // 充电最大电流
	0, // 软关机使能
	0, // 历史数据保存间隔
	2, // 系统停电电压阈值
	2, // 系统来电电压阈值
	0, // 蜂鸣器使能
	0, // 电池容量
	2, // 常规锂电充电限电流
	0, // 软件防盗延时
	0, // 后台ip地址
	0, // 后台端口
	0, // GPRS用户名
	0, // GPRS PW
	0, // GPRS APN
	0, // 告警手机号码-1#
	0, // 告警手机号码-2#
	0, // 告警手机号码-3#
	0, // 告警手机号码-4#
	0, // 告警手机号码-5#
	0, // 短信中心号码
	0, // 设备名称
	0, // 放电末期切换SOC1,  值随放电模式联动，恒压放电模式为0%，混用为10%
	0, // 运行模式
	0, // 放电模式
	0, // 循环模式
	2, // 铅酸切换电压
	2, // 恒压放电输出电压
	0, // 锂电铅酸放电比率
	0, // 单次放电DOD
	0, // 铅酸单次放电时间
	0, // 升压充电
	2, // 放电最大电流
	2, // 掉电电压阈值
	2, // 输出电压偏差阈值
	0, // 单体动态欠压保护
	0, // 设置告警属性,  告警干接点
	0, // 告警级别,  告警级别
	0, // 启用日期
	0, // 主用电池
	0, // 振动告警使能
	0, // 陀螺仪灵敏度
	0, // 心跳周期
	0, // 整组欠压保护温度补偿
	0, // BMS系统名称
	0, // 干接点默认状态
	0, // 放电方式
	0, // 休眠指示灯
	0, // 放电直通使能
	0, // 节能功能
	1, // 环境温度高保护阈值
	1, // 环境温度低保护阈值
	2, // 电池组过压告警恢复阈值,  使用单体值表示，其真实值应乘以电芯节数
	2, // 电池组过压保护恢复阈值,  参数实际未使用，范围不具有参考性。使用单体值表示，其真实值应乘以电芯节数
	3, // 单体过压告警恢复阈值
	3, // 单体过压保护恢复阈值
	3, // 单体欠压告警恢复阈值
	3, // 单体欠压保护恢复阈值
	1, // 充电高温告警恢复阈值
	1, // 充电高温保护恢复阈值
	1, // 放电高温告警恢复阈值
	1, // 放电高温保护恢复阈值
	1, // 充电低温告警恢复阈值
	1, // 充电低温保护恢复阈值
	1, // 放电低温告警恢复阈值
	1, // 放电低温保护恢复阈值
	1, // 环境温度高告警恢复阈值
	1, // 环境温度高保护恢复阈值
	1, // 环境温度低告警恢复阈值
	1, // 环境温度低保护恢复阈值
	1, // 单板过温告警恢复阈值
	1, // 单板过温保护恢复阈值
	0, // 恒压末期放电切换方式
	0, // 恒压末期放电切换SOC,  与放电末期切换SOC1统一，值随放电模式联动
	2, // 恒压末期放电切换电压
	0, // GPS防盗距离
	0, // 陀螺仪防盗方式
	0, // 电池解锁方式
	0, // 充电轮换使能
	0, // 历史数据类型
	3, // 单体充电截止电压
	0, // 静置关机时间
	0, // 电压激活使能
	0, // Modbus基地址
	0, // 加热垫使能
	0, // SNMP告警ip地址
	0, // SNMP可读共同体
	0, // SNMP设置共同体
	0, // SNMP V3用户等级
	0, // SNMP V3用户名
	0, // SNMP V3鉴别PW
	0, // SNMP V3加密PW
	0, // SNMP Trap端口号
	0, // SNMP Agent端口号
	0, // 本机ip地址
	0, // 子网掩码
	0, // 网关
	0, // 本机ip获取方式
	0, // 远程升级后台ip地址
	0, // 远程升级端口
	0, // 充电加热膜启动温度
	0, // 充电加热膜关闭温度
	0, // 加热膜过温阈值
	0, // 加热膜过温解除
	0, // 均流SOC补偿幅值
	0, // 均流SOC补偿斜率
	0, // 均流方式
	2, // 末期放电电压1
	2, // 末期放电电压2
	2, // 单体补充电电压
	0, // 来电判断时间
	0, // 充电MAP使能
	0, // 下垂均流使能
	0, // SNMP版本
	0, // SNMP V3认证算法
	0, // SNMP V3加密算法
	0, // 故障前录波时间间隔
	0, // 故障后录波时间间隔
	0, // 故障前录波条数
	0, // 故障后录波条数
	0, // 测点数量X
	0, // 可变测点ID,  备注：业务未使用参数用红色字体标出，蓝色字体为同步R311参数暂时未使用参数，黄色字体表示相较于FB100B3新增约束关系
	0, // 电池地址获取方式
	0, // 电池切换地址
	0, // 直流内阻异常告警阈值
	0, // 直流内阻异常保护阈值
	1, // 单体温升速率异常阈值
	1, // 自放电容量比率
	1, // 容量衰减一致性差告警比率阈值
	0, // 最大充电功率
	0, // 最大放电功率
	0, // 定位方式
	0, // 柜间地址获取方式
	0, // 柜间切换地址
	0, // 电池异常温度高保护阈值
	0, // 并机干接点同步使能
	0, // 站点防盗延时时间
	0, // NTP服务器IP
	0, // 时区
	0, // 网络自动对时使能
	0, // 网管防盗延时时间
	0, // 严重告警指示灯
	0, // 次要告警指示灯
	0, // 消防干接点控制使能
	0, // 充放电机模式
	1, // 德业逆变器充满电压
	1, // 德业逆变器放电下限电压
	0, // 电芯类型
	0, // 放电末期切换SOC2,  值随放电模式联动，恒压放电模式为0%，混用为10%
	0, // 放电加热膜启动温度
	0, // 放电加热膜关闭温度
	0, // 自动位置刷新周期
	0, // 陀螺仪防盗延时
	0, // NTC无效屏蔽路数
	1, // 自适应模式初始电压
	0, // 防水检测使能
}; 

/// 参数类型数组(运行参数，配置参数，校准参数)
const   BYTE	s_aucParaType[PARA_NUM] =
{
	PARATYPE_RUNNING, // 充电过流告警阈值
	PARATYPE_RUNNING, // 充电过流保护阈值
	PARATYPE_RUNNING, // 放电过流告警阈值
	PARATYPE_RUNNING, // 放电过流保护阈值
	PARATYPE_RUNNING, // 电池组过压告警阈值,  使用单体值表示，其真实值应乘以电芯节数
	PARATYPE_RUNNING, // 电池组过压保护阈值,  使用单体值表示，其真实值应乘以电芯节数
	PARATYPE_RUNNING, // 单板过温保护阈值
	PARATYPE_RUNNING, // 单板过温告警阈值
	PARATYPE_RUNNING, // 环境温度高告警阈值
	PARATYPE_RUNNING, // 环境温度低告警阈值
	PARATYPE_RUNNING, // 电池组欠压告警阈值,  使用单体值表示，其真实值应乘以电芯节数
	PARATYPE_RUNNING, // 电池组欠压保护阈值,  使用单体值表示，其真实值应乘以电芯节数
	PARATYPE_RUNNING, // 单体过压告警阈值
	PARATYPE_RUNNING, // 单体过压保护阈值
	PARATYPE_RUNNING, // 单体欠压告警阈值
	PARATYPE_RUNNING, // 单体欠压保护阈值
	PARATYPE_RUNNING, // 充电高温告警阈值
	PARATYPE_RUNNING, // 充电高温保护阈值
	PARATYPE_RUNNING, // 放电高温告警阈值
	PARATYPE_RUNNING, // 放电高温保护阈值
	PARATYPE_RUNNING, // 充电低温告警阈值
	PARATYPE_RUNNING, // 充电低温保护阈值
	PARATYPE_RUNNING, // 放电低温告警阈值
	PARATYPE_RUNNING, // 放电低温保护阈值
	PARATYPE_RUNNING, // 单体一致性差告警阈值
	PARATYPE_RUNNING, // 单体一致性差保护阈值
	PARATYPE_RUNNING, // 电池SOC低告警阈值
	PARATYPE_RUNNING, // 电池SOC低保护阈值
	PARATYPE_RUNNING, // 电池SOH低告警阈值
	PARATYPE_RUNNING, // 电池SOH低保护阈值
	PARATYPE_RUNNING, // 单体损坏保护阈值
	PARATYPE_RUNNING, // 电池充满电压,  使用单体值表示，其真实值应乘以电芯节数
	PARATYPE_RUNNING, // 电池充满电流
	PARATYPE_RUNNING, // 充电最长时间
	PARATYPE_RUNNING, // 充电末期维持时间
	PARATYPE_RUNNING, // 电池补充电电压,  使用单体值表示，其真实值应乘以电芯节数
	PARATYPE_RUNNING, // 电池补充电周期
	PARATYPE_RUNNING, // 单体均衡启动压差阈值
	PARATYPE_RUNNING, // 充电最大电流
	PARATYPE_RUNNING, // 软关机使能
	PARATYPE_RUNNING, // 历史数据保存间隔
	PARATYPE_RUNNING, // 系统停电电压阈值
	PARATYPE_RUNNING, // 系统来电电压阈值
	PARATYPE_CONFIG, // 蜂鸣器使能
	PARATYPE_CONFIG, // 电池容量
	PARATYPE_CONFIG, // 常规锂电充电限电流
	PARATYPE_CONFIG, // 软件防盗延时
	PARATYPE_CONFIG, // 后台ip地址
	PARATYPE_CONFIG, // 后台端口
	PARATYPE_CONFIG, // GPRS用户名
	PARATYPE_CONFIG, // GPRS PW
	PARATYPE_CONFIG, // GPRS APN
	PARATYPE_CONFIG, // 告警手机号码-1#
	PARATYPE_CONFIG, // 告警手机号码-2#
	PARATYPE_CONFIG, // 告警手机号码-3#
	PARATYPE_CONFIG, // 告警手机号码-4#
	PARATYPE_CONFIG, // 告警手机号码-5#
	PARATYPE_CONFIG, // 短信中心号码
	PARATYPE_CONFIG, // 设备名称
	PARATYPE_CONFIG, // 放电末期切换SOC1,  值随放电模式联动，恒压放电模式为0%，混用为10%
	PARATYPE_CONFIG, // 运行模式
	PARATYPE_CONFIG, // 放电模式
	PARATYPE_CONFIG, // 循环模式
	PARATYPE_CONFIG, // 铅酸切换电压
	PARATYPE_CONFIG, // 恒压放电输出电压
	PARATYPE_CONFIG, // 锂电铅酸放电比率
	PARATYPE_CONFIG, // 单次放电DOD
	PARATYPE_CONFIG, // 铅酸单次放电时间
	PARATYPE_CONFIG, // 升压充电
	PARATYPE_CONFIG, // 放电最大电流
	PARATYPE_CONFIG, // 掉电电压阈值
	PARATYPE_CONFIG, // 输出电压偏差阈值
	PARATYPE_CONFIG, // 单体动态欠压保护
	PARATYPE_CONFIG, // 设置告警属性,  告警干接点
	PARATYPE_CONFIG, // 告警级别,  告警级别
	PARATYPE_CONFIG, // 启用日期
	PARATYPE_CONFIG, // 主用电池
	PARATYPE_CONFIG, // 振动告警使能
	PARATYPE_CONFIG, // 陀螺仪灵敏度
	PARATYPE_CONFIG, // 心跳周期
	PARATYPE_CONFIG, // 整组欠压保护温度补偿
	PARATYPE_CONFIG, // BMS系统名称
	PARATYPE_CONFIG, // 干接点默认状态
	PARATYPE_CONFIG, // 放电方式
	PARATYPE_CONFIG, // 休眠指示灯
	PARATYPE_CONFIG, // 放电直通使能
	PARATYPE_CONFIG, // 节能功能
	PARATYPE_RUNNING, // 环境温度高保护阈值
	PARATYPE_RUNNING, // 环境温度低保护阈值
	PARATYPE_RUNNING, // 电池组过压告警恢复阈值,  使用单体值表示，其真实值应乘以电芯节数
	PARATYPE_RUNNING, // 电池组过压保护恢复阈值,  参数实际未使用，范围不具有参考性。使用单体值表示，其真实值应乘以电芯节数
	PARATYPE_RUNNING, // 单体过压告警恢复阈值
	PARATYPE_RUNNING, // 单体过压保护恢复阈值
	PARATYPE_RUNNING, // 单体欠压告警恢复阈值
	PARATYPE_RUNNING, // 单体欠压保护恢复阈值
	PARATYPE_RUNNING, // 充电高温告警恢复阈值
	PARATYPE_RUNNING, // 充电高温保护恢复阈值
	PARATYPE_RUNNING, // 放电高温告警恢复阈值
	PARATYPE_RUNNING, // 放电高温保护恢复阈值
	PARATYPE_RUNNING, // 充电低温告警恢复阈值
	PARATYPE_RUNNING, // 充电低温保护恢复阈值
	PARATYPE_RUNNING, // 放电低温告警恢复阈值
	PARATYPE_RUNNING, // 放电低温保护恢复阈值
	PARATYPE_RUNNING, // 环境温度高告警恢复阈值
	PARATYPE_RUNNING, // 环境温度高保护恢复阈值
	PARATYPE_RUNNING, // 环境温度低告警恢复阈值
	PARATYPE_RUNNING, // 环境温度低保护恢复阈值
	PARATYPE_RUNNING, // 单板过温告警恢复阈值
	PARATYPE_RUNNING, // 单板过温保护恢复阈值
	PARATYPE_RUNNING, // 恒压末期放电切换方式
	PARATYPE_RUNNING, // 恒压末期放电切换SOC,  与放电末期切换SOC1统一，值随放电模式联动
	PARATYPE_RUNNING, // 恒压末期放电切换电压
	PARATYPE_CONFIG, // GPS防盗距离
	PARATYPE_CONFIG, // 陀螺仪防盗方式
	PARATYPE_CONFIG, // 电池解锁方式
	PARATYPE_CONFIG, // 充电轮换使能
	PARATYPE_CONFIG, // 历史数据类型
	PARATYPE_CONFIG, // 单体充电截止电压
	PARATYPE_CONFIG, // 静置关机时间
	PARATYPE_CONFIG, // 电压激活使能
	PARATYPE_CONFIG, // Modbus基地址
	PARATYPE_CONFIG, // 加热垫使能
	PARATYPE_CONFIG, // SNMP告警ip地址
	PARATYPE_CONFIG, // SNMP可读共同体
	PARATYPE_CONFIG, // SNMP设置共同体
	PARATYPE_CONFIG, // SNMP V3用户等级
	PARATYPE_CONFIG, // SNMP V3用户名
	PARATYPE_CONFIG, // SNMP V3鉴别PW
	PARATYPE_CONFIG, // SNMP V3加密PW
	PARATYPE_CONFIG, // SNMP Trap端口号
	PARATYPE_CONFIG, // SNMP Agent端口号
	PARATYPE_CONFIG, // 本机ip地址
	PARATYPE_CONFIG, // 子网掩码
	PARATYPE_CONFIG, // 网关
	PARATYPE_CONFIG, // 本机ip获取方式
	PARATYPE_CONFIG, // 远程升级后台ip地址
	PARATYPE_CONFIG, // 远程升级端口
	PARATYPE_CONFIG, // 充电加热膜启动温度
	PARATYPE_CONFIG, // 充电加热膜关闭温度
	PARATYPE_CONFIG, // 加热膜过温阈值
	PARATYPE_CONFIG, // 加热膜过温解除
	PARATYPE_CONFIG, // 均流SOC补偿幅值
	PARATYPE_CONFIG, // 均流SOC补偿斜率
	PARATYPE_CONFIG, // 均流方式
	PARATYPE_RUNNING, // 末期放电电压1
	PARATYPE_RUNNING, // 末期放电电压2
	PARATYPE_RUNNING, // 单体补充电电压
	PARATYPE_CONFIG, // 来电判断时间
	PARATYPE_CONFIG, // 充电MAP使能
	PARATYPE_CONFIG, // 下垂均流使能
	PARATYPE_CONFIG, // SNMP版本
	PARATYPE_CONFIG, // SNMP V3认证算法
	PARATYPE_CONFIG, // SNMP V3加密算法
	PARATYPE_CONFIG, // 故障前录波时间间隔
	PARATYPE_CONFIG, // 故障后录波时间间隔
	PARATYPE_CONFIG, // 故障前录波条数
	PARATYPE_CONFIG, // 故障后录波条数
	PARATYPE_CONFIG, // 测点数量X
	PARATYPE_CONFIG, // 可变测点ID,  备注：业务未使用参数用红色字体标出，蓝色字体为同步R311参数暂时未使用参数，黄色字体表示相较于FB100B3新增约束关系
	PARATYPE_CONFIG, // 电池地址获取方式
	PARATYPE_CONFIG, // 电池切换地址
	PARATYPE_CONFIG, // 直流内阻异常告警阈值
	PARATYPE_CONFIG, // 直流内阻异常保护阈值
	PARATYPE_RUNNING, // 单体温升速率异常阈值
	PARATYPE_RUNNING, // 自放电容量比率
	PARATYPE_RUNNING, // 容量衰减一致性差告警比率阈值
	PARATYPE_CONFIG, // 最大充电功率
	PARATYPE_CONFIG, // 最大放电功率
	PARATYPE_CONFIG, // 定位方式
	PARATYPE_CONFIG, // 柜间地址获取方式
	PARATYPE_CONFIG, // 柜间切换地址
	PARATYPE_RUNNING, // 电池异常温度高保护阈值
	PARATYPE_CONFIG, // 并机干接点同步使能
	PARATYPE_CONFIG, // 站点防盗延时时间
	PARATYPE_CONFIG, // NTP服务器IP
	PARATYPE_CONFIG, // 时区
	PARATYPE_CONFIG, // 网络自动对时使能
	PARATYPE_CONFIG, // 网管防盗延时时间
	PARATYPE_CONFIG, // 严重告警指示灯
	PARATYPE_CONFIG, // 次要告警指示灯
	PARATYPE_CONFIG, // 消防干接点控制使能
	PARATYPE_CONFIG, // 充放电机模式
	PARATYPE_CONFIG, // 德业逆变器充满电压
	PARATYPE_CONFIG, // 德业逆变器放电下限电压
	PARATYPE_CONFIG, // 电芯类型
	PARATYPE_CONFIG, // 放电末期切换SOC2,  值随放电模式联动，恒压放电模式为0%，混用为10%
	PARATYPE_CONFIG, // 放电加热膜启动温度
	PARATYPE_CONFIG, // 放电加热膜关闭温度
	PARATYPE_CONFIG, // 自动位置刷新周期
	PARATYPE_CONFIG, // 陀螺仪防盗延时
	PARATYPE_CONFIG, // NTC无效屏蔽路数
	PARATYPE_CONFIG, // 自适应模式初始电压
	PARATYPE_CONFIG, // 防水检测使能
}; 

/// 参数数据类型数组
const   BYTE aucVarType[PARA_NUM] =
{
	PARA_TYPE_FLOAT, // 充电过流告警阈值
	PARA_TYPE_FLOAT, // 充电过流保护阈值
	PARA_TYPE_FLOAT, // 放电过流告警阈值
	PARA_TYPE_FLOAT, // 放电过流保护阈值
	PARA_TYPE_FLOAT, // 电池组过压告警阈值,  使用单体值表示，其真实值应乘以电芯节数
	PARA_TYPE_FLOAT, // 电池组过压保护阈值,  使用单体值表示，其真实值应乘以电芯节数
	PARA_TYPE_FLOAT, // 单板过温保护阈值
	PARA_TYPE_FLOAT, // 单板过温告警阈值
	PARA_TYPE_FLOAT, // 环境温度高告警阈值
	PARA_TYPE_FLOAT, // 环境温度低告警阈值
	PARA_TYPE_FLOAT, // 电池组欠压告警阈值,  使用单体值表示，其真实值应乘以电芯节数
	PARA_TYPE_FLOAT, // 电池组欠压保护阈值,  使用单体值表示，其真实值应乘以电芯节数
	PARA_TYPE_FLOAT, // 单体过压告警阈值
	PARA_TYPE_FLOAT, // 单体过压保护阈值
	PARA_TYPE_FLOAT, // 单体欠压告警阈值
	PARA_TYPE_FLOAT, // 单体欠压保护阈值
	PARA_TYPE_FLOAT, // 充电高温告警阈值
	PARA_TYPE_FLOAT, // 充电高温保护阈值
	PARA_TYPE_FLOAT, // 放电高温告警阈值
	PARA_TYPE_FLOAT, // 放电高温保护阈值
	PARA_TYPE_FLOAT, // 充电低温告警阈值
	PARA_TYPE_FLOAT, // 充电低温保护阈值
	PARA_TYPE_FLOAT, // 放电低温告警阈值
	PARA_TYPE_FLOAT, // 放电低温保护阈值
	PARA_TYPE_FLOAT, // 单体一致性差告警阈值
	PARA_TYPE_FLOAT, // 单体一致性差保护阈值
	PARA_TYPE_WORD, // 电池SOC低告警阈值
	PARA_TYPE_WORD, // 电池SOC低保护阈值
	PARA_TYPE_WORD, // 电池SOH低告警阈值
	PARA_TYPE_WORD, // 电池SOH低保护阈值
	PARA_TYPE_FLOAT, // 单体损坏保护阈值
	PARA_TYPE_FLOAT, // 电池充满电压,  使用单体值表示，其真实值应乘以电芯节数
	PARA_TYPE_FLOAT, // 电池充满电流
	PARA_TYPE_WORD, // 充电最长时间
	PARA_TYPE_WORD, // 充电末期维持时间
	PARA_TYPE_FLOAT, // 电池补充电电压,  使用单体值表示，其真实值应乘以电芯节数
	PARA_TYPE_WORD, // 电池补充电周期
	PARA_TYPE_FLOAT, // 单体均衡启动压差阈值
	PARA_TYPE_FLOAT, // 充电最大电流
	PARA_TYPE_BOOLEAN, // 软关机使能
	PARA_TYPE_WORD, // 历史数据保存间隔
	PARA_TYPE_FLOAT, // 系统停电电压阈值
	PARA_TYPE_FLOAT, // 系统来电电压阈值
	PARA_TYPE_BOOLEAN, // 蜂鸣器使能
	PARA_TYPE_WORD, // 电池容量
	PARA_TYPE_FLOAT, // 常规锂电充电限电流
	PARA_TYPE_WORD, // 软件防盗延时
	PARA_TYPE_BYTE, // 后台ip地址
	PARA_TYPE_WORD, // 后台端口
	PARA_TYPE_BYTE, // GPRS用户名
	PARA_TYPE_BYTE, // GPRS PW
	PARA_TYPE_BYTE, // GPRS APN
	PARA_TYPE_BYTE, // 告警手机号码-1#
	PARA_TYPE_BYTE, // 告警手机号码-2#
	PARA_TYPE_BYTE, // 告警手机号码-3#
	PARA_TYPE_BYTE, // 告警手机号码-4#
	PARA_TYPE_BYTE, // 告警手机号码-5#
	PARA_TYPE_BYTE, // 短信中心号码
	PARA_TYPE_BYTE, // 设备名称
	PARA_TYPE_WORD, // 放电末期切换SOC1,  值随放电模式联动，恒压放电模式为0%，混用为10%
	PARA_TYPE_BYTE, // 运行模式
	PARA_TYPE_BYTE, // 放电模式
	PARA_TYPE_BYTE, // 循环模式
	PARA_TYPE_FLOAT, // 铅酸切换电压
	PARA_TYPE_FLOAT, // 恒压放电输出电压
	PARA_TYPE_BYTE, // 锂电铅酸放电比率
	PARA_TYPE_BYTE, // 单次放电DOD
	PARA_TYPE_WORD, // 铅酸单次放电时间
	PARA_TYPE_BOOLEAN, // 升压充电
	PARA_TYPE_FLOAT, // 放电最大电流
	PARA_TYPE_FLOAT, // 掉电电压阈值
	PARA_TYPE_FLOAT, // 输出电压偏差阈值
	PARA_TYPE_BYTE, // 单体动态欠压保护
	PARA_TYPE_BYTE, // 设置告警属性,  告警干接点
	PARA_TYPE_BYTE, // 告警级别,  告警级别
	PARA_TYPE_T_DateStruct, // 启用日期
	PARA_TYPE_BOOLEAN, // 主用电池
	PARA_TYPE_BYTE, // 振动告警使能
	PARA_TYPE_BYTE, // 陀螺仪灵敏度
	PARA_TYPE_WORD, // 心跳周期
	PARA_TYPE_BYTE, // 整组欠压保护温度补偿
	PARA_TYPE_BYTE, // BMS系统名称
	PARA_TYPE_BYTE, // 干接点默认状态
	PARA_TYPE_BYTE, // 放电方式
	PARA_TYPE_BYTE, // 休眠指示灯
	PARA_TYPE_BOOLEAN, // 放电直通使能
	PARA_TYPE_BOOLEAN, // 节能功能
	PARA_TYPE_FLOAT, // 环境温度高保护阈值
	PARA_TYPE_FLOAT, // 环境温度低保护阈值
	PARA_TYPE_FLOAT, // 电池组过压告警恢复阈值,  使用单体值表示，其真实值应乘以电芯节数
	PARA_TYPE_FLOAT, // 电池组过压保护恢复阈值,  参数实际未使用，范围不具有参考性。使用单体值表示，其真实值应乘以电芯节数
	PARA_TYPE_FLOAT, // 单体过压告警恢复阈值
	PARA_TYPE_FLOAT, // 单体过压保护恢复阈值
	PARA_TYPE_FLOAT, // 单体欠压告警恢复阈值
	PARA_TYPE_FLOAT, // 单体欠压保护恢复阈值
	PARA_TYPE_FLOAT, // 充电高温告警恢复阈值
	PARA_TYPE_FLOAT, // 充电高温保护恢复阈值
	PARA_TYPE_FLOAT, // 放电高温告警恢复阈值
	PARA_TYPE_FLOAT, // 放电高温保护恢复阈值
	PARA_TYPE_FLOAT, // 充电低温告警恢复阈值
	PARA_TYPE_FLOAT, // 充电低温保护恢复阈值
	PARA_TYPE_FLOAT, // 放电低温告警恢复阈值
	PARA_TYPE_FLOAT, // 放电低温保护恢复阈值
	PARA_TYPE_FLOAT, // 环境温度高告警恢复阈值
	PARA_TYPE_FLOAT, // 环境温度高保护恢复阈值
	PARA_TYPE_FLOAT, // 环境温度低告警恢复阈值
	PARA_TYPE_FLOAT, // 环境温度低保护恢复阈值
	PARA_TYPE_FLOAT, // 单板过温告警恢复阈值
	PARA_TYPE_FLOAT, // 单板过温保护恢复阈值
	PARA_TYPE_BYTE, // 恒压末期放电切换方式
	PARA_TYPE_BYTE, // 恒压末期放电切换SOC,  与放电末期切换SOC1统一，值随放电模式联动
	PARA_TYPE_FLOAT, // 恒压末期放电切换电压
	PARA_TYPE_WORD, // GPS防盗距离
	PARA_TYPE_BYTE, // 陀螺仪防盗方式
	PARA_TYPE_BYTE, // 电池解锁方式
	PARA_TYPE_BOOLEAN, // 充电轮换使能
	PARA_TYPE_BYTE, // 历史数据类型
	PARA_TYPE_FLOAT, // 单体充电截止电压
	PARA_TYPE_WORD, // 静置关机时间
	PARA_TYPE_BOOLEAN, // 电压激活使能
	PARA_TYPE_BYTE, // Modbus基地址
	PARA_TYPE_BOOLEAN, // 加热垫使能
	PARA_TYPE_BYTE, // SNMP告警ip地址
	PARA_TYPE_BYTE, // SNMP可读共同体
	PARA_TYPE_BYTE, // SNMP设置共同体
	PARA_TYPE_BYTE, // SNMP V3用户等级
	PARA_TYPE_BYTE, // SNMP V3用户名
	PARA_TYPE_BYTE, // SNMP V3鉴别PW
	PARA_TYPE_BYTE, // SNMP V3加密PW
	PARA_TYPE_WORD, // SNMP Trap端口号
	PARA_TYPE_WORD, // SNMP Agent端口号
	PARA_TYPE_BYTE, // 本机ip地址
	PARA_TYPE_BYTE, // 子网掩码
	PARA_TYPE_BYTE, // 网关
	PARA_TYPE_BYTE, // 本机ip获取方式
	PARA_TYPE_BYTE, // 远程升级后台ip地址
	PARA_TYPE_WORD, // 远程升级端口
	PARA_TYPE_FLOAT, // 充电加热膜启动温度
	PARA_TYPE_FLOAT, // 充电加热膜关闭温度
	PARA_TYPE_FLOAT, // 加热膜过温阈值
	PARA_TYPE_FLOAT, // 加热膜过温解除
	PARA_TYPE_BYTE, // 均流SOC补偿幅值
	PARA_TYPE_BYTE, // 均流SOC补偿斜率
	PARA_TYPE_BYTE, // 均流方式
	PARA_TYPE_FLOAT, // 末期放电电压1
	PARA_TYPE_FLOAT, // 末期放电电压2
	PARA_TYPE_FLOAT, // 单体补充电电压
	PARA_TYPE_WORD, // 来电判断时间
	PARA_TYPE_BOOLEAN, // 充电MAP使能
	PARA_TYPE_BOOLEAN, // 下垂均流使能
	PARA_TYPE_BYTE, // SNMP版本
	PARA_TYPE_BYTE, // SNMP V3认证算法
	PARA_TYPE_BYTE, // SNMP V3加密算法
	PARA_TYPE_WORD, // 故障前录波时间间隔
	PARA_TYPE_WORD, // 故障后录波时间间隔
	PARA_TYPE_BYTE, // 故障前录波条数
	PARA_TYPE_BYTE, // 故障后录波条数
	PARA_TYPE_BYTE, // 测点数量X
	PARA_TYPE_BYTE, // 可变测点ID,  备注：业务未使用参数用红色字体标出，蓝色字体为同步R311参数暂时未使用参数，黄色字体表示相较于FB100B3新增约束关系
	PARA_TYPE_BYTE, // 电池地址获取方式
	PARA_TYPE_BYTE, // 电池切换地址
	PARA_TYPE_BYTE, // 直流内阻异常告警阈值
	PARA_TYPE_BYTE, // 直流内阻异常保护阈值
	PARA_TYPE_FLOAT, // 单体温升速率异常阈值
	PARA_TYPE_FLOAT, // 自放电容量比率
	PARA_TYPE_FLOAT, // 容量衰减一致性差告警比率阈值
	PARA_TYPE_WORD, // 最大充电功率
	PARA_TYPE_WORD, // 最大放电功率
	PARA_TYPE_BYTE, // 定位方式
	PARA_TYPE_BYTE, // 柜间地址获取方式
	PARA_TYPE_BYTE, // 柜间切换地址
	PARA_TYPE_FLOAT, // 电池异常温度高保护阈值
	PARA_TYPE_BOOLEAN, // 并机干接点同步使能
	PARA_TYPE_WORD, // 站点防盗延时时间
	PARA_TYPE_BYTE, // NTP服务器IP
	PARA_TYPE_BYTE, // 时区
	PARA_TYPE_BOOLEAN, // 网络自动对时使能
	PARA_TYPE_WORD, // 网管防盗延时时间
	PARA_TYPE_BYTE, // 严重告警指示灯
	PARA_TYPE_BYTE, // 次要告警指示灯
	PARA_TYPE_BYTE, // 消防干接点控制使能
	PARA_TYPE_BYTE, // 充放电机模式
	PARA_TYPE_FLOAT, // 德业逆变器充满电压
	PARA_TYPE_FLOAT, // 德业逆变器放电下限电压
	PARA_TYPE_BYTE, // 电芯类型
	PARA_TYPE_WORD, // 放电末期切换SOC2,  值随放电模式联动，恒压放电模式为0%，混用为10%
	PARA_TYPE_FLOAT, // 放电加热膜启动温度
	PARA_TYPE_FLOAT, // 放电加热膜关闭温度
	PARA_TYPE_BYTE, // 自动位置刷新周期
	PARA_TYPE_BYTE, // 陀螺仪防盗延时
	PARA_TYPE_BYTE, // NTC无效屏蔽路数
	PARA_TYPE_FLOAT, // 自适应模式初始电压
	PARA_TYPE_BYTE, // 防水检测使能
}; 

/// 参数ID数组
const WORD s_awParaID[] =
{
	PARA_ID_CHG_CURR_HIGH_ALM, // 充电过流告警阈值
	PARA_ID_CHG_CURR_HIGH_PRT, // 充电过流保护阈值
	PARA_ID_DISCHG_CURR_HIGH_ALM, // 放电过流告警阈值
	PARA_ID_DISCHG_CURR_HIGH_PRT, // 放电过流保护阈值
	PARA_ID_BATT_OVER_VOLT_ALM, // 电池组过压告警阈值,  使用单体值表示，其真实值应乘以电芯节数
	PARA_ID_BATT_OVER_VOLT_PRT, // 电池组过压保护阈值,  使用单体值表示，其真实值应乘以电芯节数
	PARA_ID_BOARD_TEMP_HIGH_PRT, // 单板过温保护阈值
	PARA_ID_BOARD_TEMP_HIGH_ALM, // 单板过温告警阈值
	PARA_ID_ENV_TEMP_HIGH_ALM, // 环境温度高告警阈值
	PARA_ID_ENV_TEMP_LOW_ALM, // 环境温度低告警阈值
	PARA_ID_BATT_UNDER_VOLT_ALM, // 电池组欠压告警阈值,  使用单体值表示，其真实值应乘以电芯节数
	PARA_ID_BATT_UNDER_VOLT_PRT, // 电池组欠压保护阈值,  使用单体值表示，其真实值应乘以电芯节数
	PARA_ID_CELL_OVER_VOLT_ALM, // 单体过压告警阈值
	PARA_ID_CELL_OVER_VOLT_PRT, // 单体过压保护阈值
	PARA_ID_CELL_UNDER_VOLT_ALM, // 单体欠压告警阈值
	PARA_ID_CELL_UNDER_VOLT_PRT, // 单体欠压保护阈值
	PARA_ID_CHG_TEMP_HIGH_ALM, // 充电高温告警阈值
	PARA_ID_CHG_TEMP_HIGH_PRT, // 充电高温保护阈值
	PARA_ID_DISCHG_TEMP_HIGH_ALM, // 放电高温告警阈值
	PARA_ID_DISCHG_TEMP_HIGH_PRT, // 放电高温保护阈值
	PARA_ID_CHG_TEMP_LOW_ALM, // 充电低温告警阈值
	PARA_ID_CHG_TEMP_LOW_PRT, // 充电低温保护阈值
	PARA_ID_DISCHG_TEMP_LOW_ALM, // 放电低温告警阈值
	PARA_ID_DISCHG_TEMP_LOW_PRT, // 放电低温保护阈值
	PARA_ID_CELL_POOR_CONSIS_ALM, // 单体一致性差告警阈值
	PARA_ID_CELL_POOR_CONSIS_PRT, // 单体一致性差保护阈值
	PARA_ID_BATT_SOC_LOW_ALM, // 电池SOC低告警阈值
	PARA_ID_BATT_SOC_LOW_PRT, // 电池SOC低保护阈值
	PARA_ID_BATT_SOH_ALM, // 电池SOH低告警阈值
	PARA_ID_BATT_SOH_PRT, // 电池SOH低保护阈值
	PARA_ID_CELL_DAMAGE_PRT, // 单体损坏保护阈值
	PARA_ID_BATT_CHG_FULL_AVER_VOLT, // 电池充满电压,  使用单体值表示，其真实值应乘以电芯节数
	PARA_ID_BATT_CHG_FULL_AVER_CURR, // 电池充满电流
	PARA_ID_CHG_MAX_DURA, // 充电最长时间
	PARA_ID_CHG_END_DURA, // 充电末期维持时间
	PARA_ID_BATT_SUPPL_VOLT, // 电池补充电电压,  使用单体值表示，其真实值应乘以电芯节数
	PARA_ID_BATT_SUPPL_PERIOD, // 电池补充电周期
	PARA_ID_CELL_EQU_VOLT_DIFF, // 单体均衡启动压差阈值
	PARA_ID_CHRG_MAX_CURR, // 充电最大电流
	PARA_ID_SLEEP_EN, // 软关机使能
	PARA_ID_HIS_DATA_INTER, // 历史数据保存间隔
	PARA_ID_POWER_OFF_VOLT, // 系统停电电压阈值
	PARA_ID_POWER_ON_VOLT, // 系统来电电压阈值
	PARA_ID_BUZZER_ENABLE, // 蜂鸣器使能
	PARA_ID_BATT_CAP, // 电池容量
	PARA_ID_COMMON_LI_CHG_LIMIT_CURR, // 常规锂电充电限电流
	PARA_ID_SOFT_ANTI_THEFT_DELAY, // 软件防盗延时
	PARA_ID_BACKSTAGE_IP_ADDR, // 后台ip地址
	PARA_ID_BACKSTAGE_PORT, // 后台端口
	PARA_ID_GPRS_USER_NAME, // GPRS用户名
	PARA_ID_GPRS_PAZWERD, // GPRS PW
	PARA_ID_GPRS_APN, // GPRS APN
	PARA_ID_ALM_PHONE_NUM_1, // 告警手机号码-1#
	PARA_ID_ALM_PHONE_NUM_2, // 告警手机号码-2#
	PARA_ID_ALM_PHONE_NUM_3, // 告警手机号码-3#
	PARA_ID_ALM_PHONE_NUM_4, // 告警手机号码-4#
	PARA_ID_ALM_PHONE_NUM_5, // 告警手机号码-5#
	PARA_ID_SMS_CENTER_NUM, // 短信中心号码
	PARA_ID_DEVICE_NAME, // 设备名称
	PARA_ID_SWITCH_SOC, // 放电末期切换SOC1,  值随放电模式联动，恒压放电模式为0%，混用为10%
	PARA_ID_RUN_MODE, // 运行模式
	PARA_ID_USAGE_SCENARIO, // 放电模式
	PARA_ID_CYCLE_MODE, // 循环模式
	PARA_ID_LEAD_ACID_BATT_SWITCH_VOLT, // 铅酸切换电压
	PARA_ID_REMOTE_SUPPLY_OUT_VOLT, // 恒压放电输出电压
	PARA_ID_LI_ACID_DISCHG_RATE, // 锂电铅酸放电比率
	PARA_ID_DOC_PER_DISCHG, // 单次放电DOD
	PARA_ID_LEAD_ACID_BATT_DURA_DISCHG, // 铅酸单次放电时间
	PARA_ID_BOOST_CHARGE, // 升压充电
	PARA_ID_DISCHRG_MAX_CURR, // 放电最大电流
	PARA_ID_POWER_DOWN_VOLT, // 掉电电压阈值
	PARA_ID_OUTPUT_OFFSET_VOLT, // 输出电压偏差阈值
	PARA_ID_CELL_UVP_DELAY, // 单体动态欠压保护
	PARA_ID_RELAY, // 设置告警属性,  告警干接点
	PARA_ID_ALARM_CLASS, // 告警级别,  告警级别
	PARA_ID_ENABLE_DATE, // 启用日期
	PARA_ID_MAJOR_BATT, // 主用电池
	PARA_ID_VIBRATION_ALARM_ENABLE, // 振动告警使能
	PARA_ID_GYROSCOPE_SENSITIVITY, // 陀螺仪灵敏度
	PARA_ID_HEARTBEAT_CYCLE, // 心跳周期
	PARA_ID_UVP_TEMP_COMPENSATION_EN, // 整组欠压保护温度补偿
	PARA_ID_BMS_SYSTEM_NAME, // BMS系统名称
	PARA_ID_RELAY_DEFAULT_STATUS, // 干接点默认状态
	PARA_ID_DISCHARGE_MODE, // 放电方式
	PARA_ID_SLEEP_INDICATOR, // 休眠指示灯
	PARA_ID_THROUGH_ENABLE, // 放电直通使能
	PARA_ID_SAVE_ENERGY_FUNCTION, // 节能功能
	PARA_ID_ENV_TEMP_HIGH_PRT, // 环境温度高保护阈值
	PARA_ID_ENV_TEMP_LOW_PRT, // 环境温度低保护阈值
	PARA_ID_BATT_OVER_VOLT_ALM_RECO, // 电池组过压告警恢复阈值,  使用单体值表示，其真实值应乘以电芯节数
	PARA_ID_BATT_OVER_VOLT_PRT_RECO, // 电池组过压保护恢复阈值,  参数实际未使用，范围不具有参考性。使用单体值表示，其真实值应乘以电芯节数
	PARA_ID_CELL_OVER_VOLT_ALM_RECO, // 单体过压告警恢复阈值
	PARA_ID_CELL_OVER_VOLT_PRT_RECO, // 单体过压保护恢复阈值
	PARA_ID_CELL_UNDER_VOLT_ALM_RECO, // 单体欠压告警恢复阈值
	PARA_ID_CELL_UNDER_VOLT_PRT_RECO, // 单体欠压保护恢复阈值
	PARA_ID_CHG_TEMP_HIGH_ALM_RECO, // 充电高温告警恢复阈值
	PARA_ID_CHG_TEMP_HIGH_PRT_RECO, // 充电高温保护恢复阈值
	PARA_ID_DISCHG_TEMP_HIGH_ALM_RECO, // 放电高温告警恢复阈值
	PARA_ID_DISCHG_TEMP_HIGH_PRT_RECO, // 放电高温保护恢复阈值
	PARA_ID_CHG_TEMP_LOW_ALM_RECO, // 充电低温告警恢复阈值
	PARA_ID_CHG_TEMP_LOW_PRT_RECO, // 充电低温保护恢复阈值
	PARA_ID_DISCHG_TEMP_LOW_ALM_RECO, // 放电低温告警恢复阈值
	PARA_ID_DISCHG_TEMP_LOW_PRT_RECO, // 放电低温保护恢复阈值
	PARA_ID_ENV_TEMP_HIGH_ALM_RECO, // 环境温度高告警恢复阈值
	PARA_ID_ENV_TEMP_HIGH_PRT_RECO, // 环境温度高保护恢复阈值
	PARA_ID_ENV_TEMP_LOW_ALM_RECO, // 环境温度低告警恢复阈值
	PARA_ID_ENV_TEMP_LOW_PRT_RECO, // 环境温度低保护恢复阈值
	PARA_ID_BOARD_TEMP_HIGH_ALM_RECO, // 单板过温告警恢复阈值
	PARA_ID_BOARD_TEMP_HIGH_PRT_RECO, // 单板过温保护恢复阈值
	PARA_ID_REMOTE_SUPPLY_END_SWITCH_MODE, // 恒压末期放电切换方式
	PARA_ID_REMOTE_SUPPLY_END_SWITCH_SOC, // 恒压末期放电切换SOC,  与放电末期切换SOC1统一，值随放电模式联动
	PARA_ID_REMOTE_SUPPLY_END_SWITCH_VOLT, // 恒压末期放电切换电压
	PARA_ID_GSP_ANTI_THEFT_DISTANCE, // GPS防盗距离
	PARA_ID_GYROSCOPE_ANTI_THEFT_MODE, // 陀螺仪防盗方式
	PARA_ID_BATT_UNLOCK_MODE, // 电池解锁方式
	PARA_ID_CHARGE_ROTATE_ENABLE, // 充电轮换使能
	PARA_ID_HIS_DATA_TYPE, // 历史数据类型
	PARA_ID_CELL_CHG_CUTOFF_VOL, // 单体充电截止电压
	PARA_ID_SHUTDOWN_TIME, // 静置关机时间
	PARA_ID_BUSVOLT_CTIVE_EN, // 电压激活使能
	PARA_ID_MODBUS_ADDR, // Modbus基地址
	PARA_ID_HEATING_PAD_ENABLE, // 加热垫使能
	PARA_ID_SNMP_TRAP_IP, // SNMP告警ip地址
	PARA_ID_SNMP_READ_COMM, // SNMP可读共同体
	PARA_ID_SNMP_SET_COMM, // SNMP设置共同体
	PARA_ID_SNMP_LEVEL, // SNMP V3用户等级
	PARA_ID_SNMP_NAME, // SNMP V3用户名
	PARA_ID_SNMP_AUTH_PASS, // SNMP V3鉴别PW
	PARA_ID_SNMP_PRIV_PASS, // SNMP V3加密PW
	PARA_ID_SNMP_TRAP_PORT, // SNMP Trap端口号
	PARA_ID_SNMP_AGENT_PORT, // SNMP Agent端口号
	PARA_ID_LOCAL_IP, // 本机ip地址
	PARA_ID_MASK, // 子网掩码
	PARA_ID_GATEWAY, // 网关
	PARA_ID_LOCAL_IP_GET, // 本机ip获取方式
	PARA_ID_RETMOTE_UPDATE_IP_ADDR, // 远程升级后台ip地址
	PARA_ID_REMOTE_UPDATE_PORT, // 远程升级端口
	PARA_ID_CHG_HEATER_STARTUP_TEMP, // 充电加热膜启动温度
	PARA_ID_CHG_HEATER_SHUTDOWN_TEMP, // 充电加热膜关闭温度
	PARA_ID_HEATER_TEMP_HIGH_THRE, // 加热膜过温阈值
	PARA_ID_HEATER_TEMP_HIGH_RELEASE, // 加热膜过温解除
	PARA_ID_CURR_BALANCE_AMPLITUDE, // 均流SOC补偿幅值
	PARA_ID_CURR_BALANCE_SLOPE, // 均流SOC补偿斜率
	PARA_ID_CURR_BALANCE_METHOD, // 均流方式
	PARA_ID_DISCHARGE_REMO_END_OUTPUT_VOLT_FIRST, // 末期放电电压1
	PARA_ID_DISCHARGE_REMO_END_OUTPUT_VOLT_SECOND, // 末期放电电压2
	PARA_ID_CELL_SUPPL_VOLT, // 单体补充电电压
	PARA_ID_POWER_ON_DETERMINE_TIME, // 来电判断时间
	PARA_ID_CHARGE_MAP_ENABLE, // 充电MAP使能
	PARA_ID_SAG_EQUAL_CURR_EN, // 下垂均流使能
	PARA_ID_SNMP_VERSION, // SNMP版本
	PARA_ID_SNMP_AUTHENTICATION_ALGO, // SNMP V3认证算法
	PARA_ID_SNMP_PRIVACY_ALGO, // SNMP V3加密算法
	PARA_ID_PREFAULT_RECORDING_TIME_INTERVAL, // 故障前录波时间间隔
	PARA_ID_POSTFAULT_RECORDING_TIME_INTERVAL, // 故障后录波时间间隔
	PARA_ID_PREFAULT_RECORDING_NUMBER, // 故障前录波条数
	PARA_ID_POSTFAULT_RECORDING_NUMBER, // 故障后录波条数
	PARA_ID_MEASUREMENT_POINTS_NUMBER, // 测点数量X
	PARA_ID_VARIABLE_MEASUREMENT_POINT_ID, // 可变测点ID,  备注：业务未使用参数用红色字体标出，蓝色字体为同步R311参数暂时未使用参数，黄色字体表示相较于FB100B3新增约束关系
	PARA_ID_BATT_ADDRESS_MODE, // 电池地址获取方式
	PARA_ID_BATT_SWITCH_ADDRESS, // 电池切换地址
	PARA_ID_DCR_FAULT_ALM_THRE, // 直流内阻异常告警阈值
	PARA_ID_DCR_FAULT_PRT_THRE, // 直流内阻异常保护阈值
	PARA_ID_CELL_TEMP_RISE_ABNORMAL, // 单体温升速率异常阈值
	PARA_ID_SELFDISCHG_ACR, // 自放电容量比率
	PARA_ID_CAP_DCDR, // 容量衰减一致性差告警比率阈值
	PARA_ID_MAX_CHARGE_POWER, // 最大充电功率
	PARA_ID_MAX_DISCHARGE_POWER, // 最大放电功率
	PARA_ID_LOCATE_MODE, // 定位方式
	PARA_ID_CAN2_ADDRESS_MODE, // 柜间地址获取方式
	PARA_ID_CAN2_SWITCH_ADDRESS, // 柜间切换地址
	PARA_ID_BATT_FAULT_TEMP_HIGH_PRT, // 电池异常温度高保护阈值
	PARA_ID_RELAY_SYNCHRONIZE, // 并机干接点同步使能
	PARA_ID_SITE_ANTITHEFT_DELAY_TIME, // 站点防盗延时时间
	PARA_ID_NTP_IP, // NTP服务器IP
	PARA_ID_TIME_ZONE, // 时区
	PARA_ID_AUTO_TIMEING_ENABLE, // 网络自动对时使能
	PARA_ID_NET_ANTITHEFT_DELAY_TIME, // 网管防盗延时时间
	PARA_ID_CRITICAL_ALARM_INDICATOR, // 严重告警指示灯
	PARA_ID_SECONDARY_ALARM_INDICATOR, // 次要告警指示灯
	PARA_ID_FIRE_RELAY_CONTROL_ENABLE, // 消防干接点控制使能
	PARA_ID_CHARGE_DISCHARGE_MACHINE_TEST_MODEE, // 充放电机模式
	PARA_ID_DEYE_FULL_VOLT, // 德业逆变器充满电压
	PARA_ID_DEYE_UNDER_VOLT, // 德业逆变器放电下限电压
	PARA_ID_CELL_TYPE, // 电芯类型
	PARA_ID_SWITCH_SOC2, // 放电末期切换SOC2,  值随放电模式联动，恒压放电模式为0%，混用为10%
	PARA_ID_DISCHG_HEATER_STARTUP_TEMP, // 放电加热膜启动温度
	PARA_ID_DISCHG_HEATER_SHUTDOWN_TEMP, // 放电加热膜关闭温度
	PARA_ID_AUTO_LOCAL_REFRESH_PERIOD, // 自动位置刷新周期
	PARA_ID_GYRO_ANTI_THEFT_TIME, // 陀螺仪防盗延时
	PARA_ID_NTC_INVALID_SHIELD_NUM, // NTC无效屏蔽路数
	PARA_ID_SELFADAPTION_INIT_VOLT, // 自适应模式初始电压
	PARA_ID_WATER_INGRNESS_ENABLE, // 防水检测使能
};

const BYTE g_aucParaIdSection[PARA_NUM] =
{
    0x02, // 充电过流告警阈值
    0x02, // 充电过流保护阈值
    0x02, // 放电过流告警阈值
    0x02, // 放电过流保护阈值
    0x02, // 电池组过压告警阈值,  使用单体值表示，其真实值应乘以电芯节数
    0x02, // 电池组过压保护阈值,  使用单体值表示，其真实值应乘以电芯节数
    0x02, // 单板过温保护阈值
    0x02, // 单板过温告警阈值
    0x02, // 环境温度高告警阈值
    0x02, // 环境温度低告警阈值
    0x02, // 电池组欠压告警阈值,  使用单体值表示，其真实值应乘以电芯节数
    0x02, // 电池组欠压保护阈值,  使用单体值表示，其真实值应乘以电芯节数
    0x02, // 单体过压告警阈值
    0x02, // 单体过压保护阈值
    0x02, // 单体欠压告警阈值
    0x02, // 单体欠压保护阈值
    0x02, // 充电高温告警阈值
    0x02, // 充电高温保护阈值
    0x02, // 放电高温告警阈值
    0x02, // 放电高温保护阈值
    0x02, // 充电低温告警阈值
    0x02, // 充电低温保护阈值
    0x02, // 放电低温告警阈值
    0x02, // 放电低温保护阈值
    0x02, // 单体一致性差告警阈值
    0x02, // 单体一致性差保护阈值
    0x02, // 电池SOC低告警阈值
    0x02, // 电池SOC低保护阈值
    0x02, // 电池SOH低告警阈值
    0x02, // 电池SOH低保护阈值
    0x02, // 单体损坏保护阈值
    0x03, // 电池充满电压,  使用单体值表示，其真实值应乘以电芯节数
    0x03, // 电池充满电流
    0x03, // 充电最长时间
    0x03, // 充电末期维持时间
    0x03, // 电池补充电电压,  使用单体值表示，其真实值应乘以电芯节数
    0x03, // 电池补充电周期
    0x03, // 单体均衡启动压差阈值
    0x03, // 充电最大电流
    0x03, // 软关机使能
    0x03, // 历史数据保存间隔
    0x03, // 系统停电电压阈值
    0x03, // 系统来电电压阈值
    0x03, // 蜂鸣器使能
    0x03, // 电池容量
    0x03, // 常规锂电充电限电流
    0x06, // 软件防盗延时
    0x06, // 后台ip地址
    0x06, // 后台端口
    0x06, // GPRS用户名
    0x06, // GPRS PW
    0x06, // GPRS APN
    0x06, // 告警手机号码-1#
    0x06, // 告警手机号码-2#
    0x06, // 告警手机号码-3#
    0x06, // 告警手机号码-4#
    0x06, // 告警手机号码-5#
    0x06, // 短信中心号码
    0x01, // 设备名称
    0x01, // 放电末期切换SOC1,  值随放电模式联动，恒压放电模式为0%，混用为10%
    0x01, // 运行模式
    0x01, // 放电模式
    0x01, // 循环模式
    0x01, // 铅酸切换电压
    0x01, // 恒压放电输出电压
    0x01, // 锂电铅酸放电比率
    0x01, // 单次放电DOD
    0x01, // 铅酸单次放电时间
    0x01, // 升压充电
    0x03, // 放电最大电流
    0x03, // 掉电电压阈值
    0x03, // 输出电压偏差阈值
    0x03, // 单体动态欠压保护
    0x04, // 设置告警属性,  告警干接点
    0x05, // 告警级别,  告警级别
    0x03, // 启用日期
    0x03, // 主用电池
    0x03, // 振动告警使能
    0x03, // 陀螺仪灵敏度
    0x03, // 心跳周期
    0x03, // 整组欠压保护温度补偿
    0x03, // BMS系统名称
    0x01, // 干接点默认状态
    0x01, // 放电方式
    0x01, // 休眠指示灯
    0x01, // 放电直通使能
    0x01, // 节能功能
    0x02, // 环境温度高保护阈值
    0x02, // 环境温度低保护阈值
    0x02, // 电池组过压告警恢复阈值,  使用单体值表示，其真实值应乘以电芯节数
    0x02, // 电池组过压保护恢复阈值,  参数实际未使用，范围不具有参考性。使用单体值表示，其真实值应乘以电芯节数
    0x02, // 单体过压告警恢复阈值
    0x02, // 单体过压保护恢复阈值
    0x02, // 单体欠压告警恢复阈值
    0x02, // 单体欠压保护恢复阈值
    0x02, // 充电高温告警恢复阈值
    0x02, // 充电高温保护恢复阈值
    0x02, // 放电高温告警恢复阈值
    0x02, // 放电高温保护恢复阈值
    0x02, // 充电低温告警恢复阈值
    0x02, // 充电低温保护恢复阈值
    0x02, // 放电低温告警恢复阈值
    0x02, // 放电低温保护恢复阈值
    0x02, // 环境温度高告警恢复阈值
    0x02, // 环境温度高保护恢复阈值
    0x02, // 环境温度低告警恢复阈值
    0x02, // 环境温度低保护恢复阈值
    0x02, // 单板过温告警恢复阈值
    0x02, // 单板过温保护恢复阈值
    0x01, // 恒压末期放电切换方式
    0x01, // 恒压末期放电切换SOC,  与放电切换SOC统一，值随放电模式联动
    0x01, // 恒压末期放电切换电压
    0x06, // GPS防盗距离
    0x06, // 陀螺仪防盗方式
    0x06, // 电池解锁方式
    0x01, // 充电轮换使能
    0x01, // 历史数据类型
    0x01, // 单体充电截止电压
    0x01, // 静置关机时间
    0x01, // 电压激活使能
    0x01, // Modbus基地址
    0x01, // 加热垫使能
    0x07, // SNMP告警ip地址
    0x07, // SNMP可读共同体
    0x07, // SNMP设置共同体
    0x07, // SNMP V3用户等级
    0x07, // SNMP V3用户名
    0x07, // SNMP V3鉴别PW
    0x07, // SNMP V3加密PW
    0x07, // SNMP Trap端口号
    0x07, // SNMP Agent端口号
    0x07, // 本机ip地址
    0x07, // 子网掩码
    0x07, // 网关
    0x07, // 本机ip获取方式
    0x07, // 远程升级后台ip地址
    0x07, // 远程升级端口
    0x03, // 充电加热膜启动温度
    0x03, // 充电加热膜关闭温度
    0x03, // 加热膜过温阈值
    0x03, // 加热膜过温解除
    0x03, // 均流SOC补偿幅值
    0x03, // 均流SOC补偿斜率
    0x03, // 均流方式
    0x01, // 末期放电电压1
    0x01, // 末期放电电压2
    0x03, // 单体补充电电压
    0x01, // 来电判断时间
    0x03, // 充电MAP使能
    0x03, // 下垂均流使能
    0x07, // SNMP版本
    0x07, // SNMP V3认证算法
    0x07, // SNMP V3加密算法
    0x01, // 故障前录波时间间隔
    0x01, // 故障后录波时间间隔
    0x01, // 故障前录波条数
    0x01, // 故障后录波条数
    0x01, // 测点数量X
    0x01, // 可变测点ID,  备注：业务未使用参数用红色字体标出，蓝色字体为同步R311参数暂时未使用参数，黄色字体表示相较于FB100B3新增约束关系
    0x01, // 电池地址获取方式
    0x01, // 电池切换地址
    0x02, // 直流内阻异常告警阈值
    0x02, // 直流内阻异常保护阈值
    0x02, // 单体温升速率异常阈值
    0x01, // 自放电容量比率
    0x02, // 容量衰减一致性差告警比率阈值
    0x02, // 最大充电功率
    0x02, // 最大放电功率
    0x01, // 定位方式
    0x01, // 柜间地址获取方式
    0x01, // 柜间切换地址
    0x02, // 电池异常温度高保护阈值
    0x01, // 干接点同步使能
    0x06, // 站点防盗延时时间
    0x07, // NTP服务器IP
    0x07, // 时区
    0x07, // 网络自动对时使能
    0x06, // 网管防盗延时时间
    0x01, // 严重指示灯
    0x01, // 次要指示灯
    0x01, // 消防干接点控制使能
    0x01, // 充放电机模式
    0x01, // 德业逆变器充满电压
    0x01, // 德业逆变器放电下限电压
    0x01, // 电芯类型
    0x01, // 放电末期切换SOC2,  值随放电模式联动，恒压放电模式为0%，混用为10%
    0x03, // 放电加热膜启动温度
    0x03, // 放电加热膜关闭温度
    0x06, // 自动位置刷新周期
	0x06, // 陀螺仪防盗延时
	0x01, // NTC无效屏蔽路数
	0x01, // 自适应模式初始电压
};
#ifdef __cplusplus
} /* end of the extern "C" block */
#endif

#endif

