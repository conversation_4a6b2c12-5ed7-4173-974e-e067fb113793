#ifndef __INTERFACE_H
#define __INTERFACE_H

#include "board.h"
#include <rtdevice.h>
#include <rtthread.h>
#include "gd32f4xx.h"
#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */
#define LED_SOC1_PIN        GET_PIN(E, 2)
#define LED_SOC2_PIN        GET_PIN(E, 3)
#define LED_SOC3_PIN        GET_PIN(E, 4)
#define LED_SOC4_PIN        GET_PIN(E, 5)
#define LED_DISC_PIN        GET_PIN(E, 6)
#define LED_CHG_PIN         GET_PIN(D, 13)
#define LED_RUN_PIN         GET_PIN(D, 14)
#define LED_ALM_PIN         GET_PIN(D, 15)

#define BUZZ_PIN            GET_PIN(E, 10)
#define SW_B1_Pin           GET_PIN(F, 2) //GET_PIN(E, 7)
#define WARM_CTL_Pin        GET_PIN(G, 1) //����Ĥ
#define RS485_CTL_Pin       GET_PIN(F, 15)

#define SW_DET_Pin          GET_PIN(E, 15)
#define THEFT_DET_Pin       GET_PIN(F, 13)
#define BALANCE_FAULT_Pin   GET_PIN(D, 11)

//#define SWITCH_4G           GET_PIN(G, 2) 
//#define SWITCH_WIFI         GET_PIN(G, 3) 
#define FC_PIN              GET_PIN(A, 5)  //消防信号 


#define BUZZER_DEV_NAME "buzzer"
#define PWM_DEV_NAME0 "pwm0"
#define PWM_DEV_NAME1 "pwm1"
#define PWM_DEV_CHANNEL1  1
#define PWM_DEV_CHANNEL2  2

typedef enum
{
    CTRL_OFF,
    CTRL_ON,
    CTRL_TOGGLE,
    CTRL_BUZZ_MODE1,
    CTRL_BUZZ_MODE2,
}CTRL_State;

void TIM2_IRQHandler(void);
void GPIO_Init(void);
void BSP_BUZZ_CTRL(CTRL_State state);
#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif
