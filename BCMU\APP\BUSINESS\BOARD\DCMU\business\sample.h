
#ifndef _ACMU_SAMPLE_H_
#define _ACMU_SAMPLE_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "pin_define.h"
#include "adc_ctrl.h"

/* 常量定义 */
#define SAMPLE_THREAD_DELAY_TIME 200  // sample线程延时时间200ms
#define LOAD_TOTAL_CURRENT_MAX   3000.0f
#define LOAD_TOTAL_CURRENT_MIN   -3000.0f

#define ALARM_EXIST        1
#define ALARM_NOT_EXIST    0

#define ADC_DEV_NAME   "adc0"
#define ADC2_DEV_NAME  "adc2"
#define PWM_DEV_NAME        "pwm11"

#define ENERGY_SAVE_INTERVAL 300  // 电量保存间隔300s

typedef enum{
    DI1 = 0,
    DI2,
    DI3,
    DI4,
    DI5,
    DI6,
    DI7,
    DI8,
    DI_END,
} eDI_SAMPLE;

typedef enum{
    LOAD_CUR =  0,
    LOAD_VOLT,
    LOAD_CURB,
    LOAD_VOLB,
    LOAD_TEMP,
}adc_channel;

typedef struct
{
    adc_channel            sample_channel;
    rt_base_t              select_channel;
    unsigned char          select_status;
}adc_pin_t;

typedef struct {
    int humidity;   // 湿度值（%RH）
    int frequency;  // 对应的输出频率（Hz）
} HumidityData;

// 防抖结构体
typedef struct {
    rt_bool_t last_state;
    rt_bool_t curr_state;
    rt_tick_t last_time;
    rt_uint32_t debounce_ms;
} sensor_debounce_t;

// DI 通道信息结构体
typedef struct {
    eDI_SAMPLE sample_channel;
    rt_base_t di_pin;
    sensor_debounce_t debounce;
} DI_CHANNEL_INFO_STRUCT;

/* 函数声明 */
void* sample_init(void *param);      // 样本初始化函数
void sample_main(void* parameter);   // 样本主循环函数
int GetDiSampleData(void);
int fuse_read_all_states();
short init_extreme_data(void);
int delete_extreme_data(void);
int clear_power_data(void);
int clear_load_power_data(unsigned char number);

#ifdef __cplusplus
}
#endif // __cplusplus

#endif // _ACMU_SAMPLE_H_

