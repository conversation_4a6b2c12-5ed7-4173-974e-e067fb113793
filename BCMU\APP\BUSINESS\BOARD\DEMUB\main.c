#include <stdio.h>
#include <rtthread.h>
#include "sample.h"
#include "utils_server.h"
#include "server_id.h"
#include "storage.h"
#include "partition_table.h"
#include "north_main.h"
#include "dev_north_demub_modbus.h"
#include "protocol_bottom_comm.h"
#include "protocol_north_modbus.h"
#include "watchdog.h"
#include "dev_north_update_tab.h"
#include "dev_update_manage_handle.h"
#include "utils_data_transmission.h"
#include "dev_north_demub_apptest.h"
#include "protocol_1363_comm.h"
#include "realdata_id_in.h"
#include "software_version.h"
#include "unified_id_interface.h"
#include "ee_public_info.h"
#include "utils_time.h"
#include "realdata_save.h"

static char s_north_thread_stack[4096];
static char s_sample_thread_stack[2048];
static rt_device_t wdg_dev;

static server_info_t g_server_group[] = {
    /*   服务ID              服务名字            栈大小                             栈的起始地址                    优先级*/
    {{{NORTH_SERVER_ID,      "north",        sizeof(s_north_thread_stack),    s_north_thread_stack,       SERVER_PRIO_LOW}}, init_north,      north_comm_th},
    {{{SAMPLE_SERVER_ID,     "sample",       sizeof(s_sample_thread_stack),   s_sample_thread_stack,      SERVER_PRIO_LOW}}, sample_init_sys,  sample_main},
};

static protocol_process_t protocol_process_tab[] = {
    {PROTOCOL_YD_1363, s1363_pack, s1363_parse},
    {PROTOCOL_MODBUS_RTU, modbus_pack, modbus_parse},
    {PROTOCOL_BOTTOM_COMM, bottom_pack, bottom_parse},
};

static dev_init_t dev_init_tab[] = {
    {DEV_NORTH_DEMUB_APPTEST, init_demub_dev_apptest, NULL},
    {DEV_NORTH_DEMUB, init_dev_north_demub, NULL},
    {DEV_NORTH_DEMUB_UPDATE, init_demub_dev_north_update, NULL},

};

static link_type_t link_type_tab[] = {
     {LINK_COM, s485_dev_init, com_dev_read, com_dev_write, com_dev_set},
};


static link_inst_t link_inst_tab[] = {
    {LINK_NORTH_DEMUB,   LINK_COM, "usart2"},     //串口模式
};


int sys_run_result()
{
    bottom_update_manage_t update_info = {0};
    if(handle_storage(read_opr, ONCHIP_UPDATE_INFO, (unsigned char*)&update_info, sizeof(bottom_update_manage_t), 0) != SUCCESSFUL)
    {
        return FAILURE;
    }

    update_info.count = 0;
    update_info.sys_run_flag = TRUE;
    write_update_info(&update_info);
    return  SUCCESSFUL;
}


static void feed_dog(void)
{
    rt_device_control(wdg_dev, RT_DEVICE_CTRL_WDT_KEEPALIVE, NULL);
    return;
}

int init_watchdog(void)
{
    wdg_dev = rt_device_find("wdt");
    if (RT_NULL == wdg_dev)
    {
        return -RT_ERROR;
    }
    if (RT_EOK != rt_device_control(wdg_dev, RT_DEVICE_CTRL_WDT_START, RT_NULL))
    {
        return -RT_ERROR;
    }
    rt_thread_idle_sethook(feed_dog);

    return RT_EOK;
}



rt_uint8_t mempool_32[BLOCK_COUNT_32 * BLOCK_SIZE_32] ;
rt_uint8_t mempool_64[BLOCK_COUNT_64 * BLOCK_SIZE_64] ;
rt_uint8_t mempool_512[BLOCK_COUNT_512 * BLOCK_SIZE_512] ;
softbus_mempool_all_t softbus_mempool_info;

static void init_softbus_config(void)
{
    softbus_mempool_info.mempool_32.mempool_addr = &mempool_32[0];
    softbus_mempool_info.mempool_32.mempool_size = sizeof(mempool_32);
    softbus_mempool_info.mempool_32.block_size = BLOCK_SIZE_32;

    softbus_mempool_info.mempool_64.mempool_addr = &mempool_64[0];
    softbus_mempool_info.mempool_64.mempool_size = sizeof(mempool_64);
    softbus_mempool_info.mempool_64.block_size = BLOCK_SIZE_64;

    softbus_mempool_info.mempool_512.mempool_addr = &mempool_512[0];
    softbus_mempool_info.mempool_512.mempool_size = sizeof(mempool_512);
    softbus_mempool_info.mempool_512.block_size = BLOCK_SIZE_512;
    return ;
}



int init_factory_info()
{
    unsigned char sn[SN_LEN] = {0};
    unsigned char hardware_version[HARDWARE_VER_LEN] = {0};
    unsigned char board_manu_date[BOARD_MANU_DATE_LEN] = {0};
    date_base_t tm = {0};
    set_one_data(DEMUB_DATA_ID_SOFTWARE_NAME, "DEMUB");
    set_one_data(DEMUB_DATA_ID_SOFTWARE_VERSION, DEMUB_COMPILE_VERSION);
    set_one_data(DEMUB_DATA_ID_SOFTWARE_DATE, DEMUB_COMPILE_VERSION_DATE);
    handle_storage(read_opr, EE_PUBLIC_INFO, sn, SN_LEN, SN_OFFSET);
    set_one_data(DEMUB_DATA_ID_SERIAL_NUMBER, sn);
    handle_storage(read_opr, EE_PUBLIC_INFO, hardware_version, HARDWARE_VER_LEN, HARDWARE_VERSION_OFFSET);
    set_one_data(DEMUB_DATA_ID_HARDWARE_VERSION, hardware_version);
    handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char*)&tm, sizeof(date_base_t), MANU_DATE_OFFSET);
    date_to_string((char *)board_manu_date, (date_base_t*)&tm);
    set_one_data(DEMUB_DATA_ID_BOARD_MANU_DATE, board_manu_date);
    return SUCCESSFUL;
}


// 定义接口表
static const data_access_interface_t id_base_interface = {
    .get_data = unified_get_data,
    .set_data = unified_set_data,    
    .linear_search = linear_search_id_index
};


int main(void){
    init_watchdog();
    init_crc();
    init_softbus_config();
    softbus_init(&softbus_mempool_info);
    if (SUCCESSFUL != storage_init(&part_tab[0], sizeof(part_tab)/sizeof(part_info_t))) {
        return FAILURE;
    }
    // 初始化ID化空间
    init_real_data_memory();
    register_link_inst_tab_info(link_inst_tab, sizeof(link_inst_tab)/sizeof(link_inst_tab[0]));
    register_link_type_info(link_type_tab, sizeof(link_type_tab) / sizeof(link_type_t));
    // 注册 协议所使用的函数接口
    register_protocol_process_info(protocol_process_tab, sizeof(protocol_process_tab) / sizeof(protocol_process_t));
    register_data_access_interface(&id_base_interface);
    // 注册协议
    init_dev_tab(dev_init_tab, sizeof(dev_init_tab)/sizeof(dev_init_tab[0]));
    create_server(&g_server_group[0], sizeof(g_server_group)/sizeof(g_server_group[0]));
    sys_run_result();
    init_factory_info();
    return 0;
}








