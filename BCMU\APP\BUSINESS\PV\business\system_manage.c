#include <fal.h>
#include "data_type.h"
#include "msg.h"
#include "msg_id.h"
#include "storage.h"
#include "utils_server.h"
#include "his_record_test.h"
#include "alarm_manage.h"
#include "update_download_manage.h"
#include "para_check.h"
#include "time_manage.h"
#include "para_manage.h"
#include "utils_time.h"
#include "utils_thread.h"
#include "do_ctrl.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "software_version.h"
#include "version_flag.h"
#include "system_manage.h"
#include "alm_his_data.h"
#include "utils_data_type_conversion.h"
#include "sps.h"
#include "grid_code.h"
#include "utils_heart_beat.h"
#include "thread_id.h"
#include "backup_manage.h"
#include "dc_ac_handle.h"
#include "register_common.h"
#include "led.h"
#include "webclient.h"
#include "partition_def.h"
#include "mqtt_update.h"
#include "network_crt_parse.h"
#include "utils_rtthread_security_func.h"
#include "delay_update.h"
#include "softbus.h"
#include "apptest_pv.h"
#include "utils_flag.h"
#include "dc_ac_apptest.h"
#include "server_id.h"
#include "reset.h"
#include "wifi_interface.h"
#include "south_file_upload.h"

static _rt_server_t g_sys_server = NULL;
static rt_timer_t g_his_data_timer = {0};
static rt_timer_t g_run_time_timer = {0};
static rt_timer_t g_alm_his_data_timer = {0};
static dev_inst_t* dev_dc_ac = NULL;
static unsigned short s_hisdata_save_inverter = 0;

void alarm_his_data_timer_timeout(void *parameter);
static void handle_his_data_test_msg(_rt_msg_t curr_msg);
static void handle_process_alm_his_data_msg(_rt_msg_t curr_msg);
static void handle_stop_para_check_msg(_rt_msg_t curr_msg);
static void handle_start_para_check_msg(_rt_msg_t curr_msg);
static void handle_restore_factory_msg(_rt_msg_t curr_msg);
static void handle_grid_code_change_msg(_rt_msg_t curr_msg);
static void process_recv_msg(void);
static void handle_update_status_write_msg(_rt_msg_t curr_msg);
static void handle_reset_csu_msg(_rt_msg_t curr_msg);
static void handle_save_event_msg(_rt_msg_t curr_msg);
static void handle_stop_iv_msg(_rt_msg_t curr_msg);
static void handle_network_download_file(_rt_msg_t curr_msg);
static void handle_para_file_import(_rt_msg_t curr_msg);
static void handle_client_key_msg(_rt_msg_t curr_msg);

static pv_msg_handle_process_t s_msg_handle[] = {
    {HIS_DATA_TEST,           handle_his_data_test_msg},
    {CHECK_SIGNAL_ALARM_CHG_MSG, handle_save_his_alm_msg},
    {OPERATE_RECORD_TEST,     handle_operate_record_test_msg},
    {HIS_ALM_TEST,            handle_his_alm_test_msg},
    {SAVE_IV_DATA_MSG,        handle_save_iv_data_msg},
    {ENERGY_DATA_TEST,        handle_energy_data_test_msg},
    {SAVE_HIS_DATA_MSG,       handle_save_his_data_msg},
    {CHECK_DC_AC_PARA,           handle_check_dc_ac_para_msg},
    {AUTHO_TIME_CHECK_MSG,       handle_autho_time_check_msg},
    {STOP_PARA_CHECK,            handle_stop_para_check_msg},
    {START_PARA_CHECK,           handle_start_para_check_msg},
    {PROCESS_ALM_HIS_DATA_MSG,   handle_process_alm_his_data_msg},
    {RESTORE_FACTORY,            handle_restore_factory_msg},
    {GRID_CODE_CHANGE,           handle_grid_code_change_msg},
    {UPDATE_STATUS,              handle_update_status_write_msg},
    {CSU_RESET,                  handle_reset_csu_msg},
    {SAVE_EVENT_MSG,             handle_save_event_msg},
    {DEL_HIS_DATA_AND_REBOOT,    handle_del_his_data_msg},
    {STOP_IV_SCAN_MSG,           handle_stop_iv_msg},
    {NETWORK_DOWNLOAD_FILE_MSG,  handle_network_download_file},
    {SAVE_RECORD_FAULT_MSG, handle_save_record_fault_msg},
    {CLEAN_HIS_ALARM_MSG,        handle_clean_his_alm_msg},
    {CERT_ONE_CHG_MSG,      handle_crt_one_change_msg},
    {SAVE_FAC_DATA,              handle_save_fac_data_msg},
    {RS485_STAT_MSG,             handle_rs485_stat},
    {DELAY_UPDATE_CHECH_MSG,     handle_delay_update},
    {PARA_FILE_EXPORT_MSG,       handle_para_file_export},
    {PARA_FILE_IMPORT_MSG,       handle_para_file_import},
    {GRID_CODE_STATUS_CHECK,     handle_grid_code_check_msg},
    {ENTER_APPTEST_MSG,          handle_enter_apptest},
    {FORMATE_FILE_SYS_MSG,       handle_format_file_sys},
    {START_DELAY_UPDATE_MSG,     handle_start_delay_update_msg},
    {CLIENT_KEY_MSG,             handle_client_key_msg},
    {CHECK_ALL_ALARM_CHG_MSG,    handle_chk_all_alm_chg_msg},
    {TRIG_SOUTH_FILE_UPLOAD,     handle_south_upload_msg},
    {CLEAN_AFCI_FILE_MSG,        handle_clean_afci_msg},
    {AFCI_FILE_EXPORT_MSG,       handle_afci_file_export},
    {CLEAN_HIS_DATA_MSG,         handle_clean_his_data},
    {CLEAN_HIS_EVENT_MSG,        handle_clean_his_event},
    {CLEAN_IV_DATA_MSG,          handle_clean_iv_data},
    {CLEAN_FAULT_RECORD_MSG,     handle_clean_fault_record},
};

static msg_map system_manage_msg_map[] =
{
    {HIS_DATA_TEST,           msg_handle_nothing},   
    {CHECK_SIGNAL_ALARM_CHG_MSG, msg_handle_nothing},   
    {OPERATE_RECORD_TEST,     msg_handle_nothing},  
    {HIS_ALM_TEST,            msg_handle_nothing}, 
    {SAVE_IV_DATA_MSG,        msg_handle_nothing},  
    {ENERGY_DATA_TEST,        msg_handle_nothing},
    {SAVE_HIS_DATA_MSG,       msg_handle_nothing},
    {CHECK_DC_AC_PARA,        msg_handle_nothing},
    {AUTHO_TIME_CHECK_MSG,    msg_handle_nothing},
    {STOP_PARA_CHECK,          msg_handle_nothing},
    {START_PARA_CHECK,         msg_handle_nothing},
    {PROCESS_ALM_HIS_DATA_MSG, msg_handle_nothing},
    {RESTORE_FACTORY,          msg_handle_nothing},
    {GRID_CODE_CHANGE,         msg_handle_nothing},
    {UPDATE_STATUS,            msg_handle_nothing},
    {CSU_RESET,                msg_handle_nothing},
    {SAVE_EVENT_MSG,           msg_handle_nothing},
    {DEL_HIS_DATA_AND_REBOOT,  msg_handle_nothing},
    {STOP_IV_SCAN_MSG,         msg_handle_nothing},
    {NETWORK_DOWNLOAD_FILE_MSG, msg_handle_nothing},
    {SAVE_RECORD_FAULT_MSG,     msg_handle_nothing},
    {CLEAN_HIS_ALARM_MSG,       msg_handle_nothing},
    {CERT_ONE_CHG_MSG,          msg_handle_nothing},
    {SAVE_FAC_DATA,             msg_handle_nothing},
    {RS485_STAT_MSG,            msg_handle_nothing},
    {DELAY_UPDATE_CHECH_MSG,    msg_handle_nothing},
    {PARA_FILE_EXPORT_MSG,      msg_handle_nothing},
    {PARA_FILE_IMPORT_MSG,      msg_handle_nothing},
    {GRID_CODE_STATUS_CHECK,    msg_handle_nothing},
    {ENTER_APPTEST_MSG,         msg_handle_nothing},
    {FORMATE_FILE_SYS_MSG,      msg_handle_nothing},
    {START_DELAY_UPDATE_MSG,    msg_handle_nothing},
    {CLIENT_KEY_MSG,            msg_handle_nothing},
    {CHECK_ALL_ALARM_CHG_MSG,    msg_handle_nothing},
    {TRIG_SOUTH_FILE_UPLOAD,     msg_handle_nothing},
    {CLEAN_AFCI_FILE_MSG,        msg_handle_nothing},
    {AFCI_FILE_EXPORT_MSG,       msg_handle_nothing},
    {CLEAN_HIS_DATA_MSG,        msg_handle_nothing},
    {CLEAN_HIS_EVENT_MSG,       msg_handle_nothing},
    {CLEAN_IV_DATA_MSG,         msg_handle_nothing},
    {CLEAN_FAULT_RECORD_MSG,    msg_handle_nothing},

};

void send_his_data_save_msg()
{
    send_msg_to_thread(SAVE_HIS_DATA_MSG,MOD_SYS_MANAGE, NULL, 0);
    return;
}
void run_time_timer_timeout()
{
    static unsigned int run_time = 0;
    run_time++;
    set_one_data(DAC_DATA_ID_RUN_TIME, &run_time);
    return;
}

/**
 * @brief 告警历史数据定时器定时时间到
*/
void alarm_his_data_timer_timeout(void *parameter) {
    rt_msg_t msg = NULL;
    msg = rt_msg_create(PROCESS_ALM_HIS_DATA_MSG, NULL, 0);
    RETURN_IF_FAIL(msg != NULL);
    rt_msg_send_event(msg);
}

void init_hisdata_timer()
{
    const char* his_data_inverter = "his_data_time_inverter";
    unsigned long tm = 0;
    get_one_para(DAC_COMMON_ID_HIS_DATA_SAVE_INTERVER_OFFSET, &s_hisdata_save_inverter);     
    tm = s_hisdata_save_inverter*MIN_CONVER_MS_UNIT;

    g_his_data_timer = rt_timer_create(his_data_inverter, send_his_data_save_msg, NULL, tm, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
    if(g_his_data_timer == NULL)
    {
        return;
    }
    rt_timer_start(g_his_data_timer);
    return;
}

void init_run_time_timer()
{
    g_run_time_timer = rt_timer_create("run_time", run_time_timer_timeout, NULL, 1000, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
    if(g_run_time_timer == NULL)
    {
        return;
    }
    rt_timer_start(g_run_time_timer);
    return;
}

/**
 * @brief 初始化告警历史数据保存处理的定时器
*/
void init_alarm_his_data_process_timer(void) {
    g_alm_his_data_timer = rt_timer_create("alm_hisdata", alarm_his_data_timer_timeout, NULL, 1000, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
    RETURN_IF_FAIL(g_alm_his_data_timer != NULL);
    rt_timer_start(g_alm_his_data_timer);
}

void *init_sys_manage(void * param)
{
    server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(system_manage_msg_map) / sizeof(msg_map);
    register_server_msg_map(system_manage_msg_map, server_info);
    // RETURN_VAL_IF_FAIL(init_msg_queue(MOD_SYS_MANAGE) != NULL, NULL);
    init_first_start_time();
    dev_dc_ac = init_dev_inst(DEV_DC_AC);
    init_test_his_record();  
    init_autho_manage();
    init_hisdata_timer();
    init_alarm_his_data_process_timer();
    init_para_check();
    init_soft_info();
    init_run_time_timer();
    init_update_enable_timer();

    RETURN_VAL_IF_FAIL(dev_dc_ac != NULL, NULL);
    autho_time_check(dev_dc_ac); // 刚起来的时候，检测一下授权时间

    crt_date_parse();
    grid_code_status_check(); // 刚起来检测电网标准码状态
    decrypt_all_client_key_file();
    return dev_dc_ac;
}

void check_save_hisdata_time()
{
    unsigned short time_inverter = 0;
    unsigned long tm = 0;
    get_one_para(DAC_COMMON_ID_HIS_DATA_SAVE_INTERVER_OFFSET, &time_inverter);
    if(time_inverter != s_hisdata_save_inverter )
    {
        s_hisdata_save_inverter = time_inverter;
        rt_timer_stop(g_his_data_timer);
        tm = time_inverter*MIN_CONVER_MS_UNIT;
        // rt_kprintf("time:%d\n",time_inverter);
        rt_timer_control(g_his_data_timer , RT_TIMER_CTRL_SET_TIME ,&tm);
        rt_timer_start(g_his_data_timer); 
    }
}

void check_master_alarm()
{
    unsigned char alarm_on = 1;
    if(get_read_flash_fail_nums() >= FLASH_FAIL_MAX_NUMS || get_write_flash_fail_nums() >= FLASH_FAIL_MAX_NUMS )
    {      
        set_one_data(DAC_TRACE_ID_MONITER_ERROR_ALARM , &alarm_on);
    }
}

void system_manage_main(void *param) {
    PRINT_MSG_AND_RETURN_IF_FAIL(param != NULL);
    g_sys_server = _curr_server_get();
    sys_run_result();
    init_fac_data_handle();
    pre_thread_beat_f(THREAD_SYSTEM_MANAGE);
    
    while (is_running(TRUE)) 
    {   
        thread_beat_go_on(THREAD_SYSTEM_MANAGE);
        check_save_hisdata_time();
        check_master_alarm();//检查是否产生主告警
        process_recv_msg();
        set_start_time();
        rt_thread_mdelay(10);
    }
}

static void process_recv_msg(void) 
{
    int i = 0;
    if ((g_sys_server == NULL) || (rt_sem_take(&g_sys_server->msg_sem, 10) != RT_EOK) || (g_sys_server->msg_node == RT_NULL))
    {
        return;
    }

    _rt_msg_t curr_msg = g_sys_server->msg_node;

    for (i = 0; i < sizeof(s_msg_handle) / sizeof(s_msg_handle[0]); i++) {
        if (s_msg_handle[i].msg_id == curr_msg->msg.msg_id) {
            s_msg_handle[i].handle(curr_msg);
            break;
        }
    }

    rt_mutex_take(&g_sys_server->mutex, RT_WAITING_FOREVER);
    g_sys_server->msg_node = curr_msg->next;
    g_sys_server->msg_count--;
    rt_mutex_release(&g_sys_server->mutex);

    softbus_free(curr_msg);
}

int set_heart_beat_time_by_num(unsigned short num)
{
    if(num == 0)
    {
        set_heart_beat_time_limit(0);
    }
    else
    {
        set_heart_beat_time_limit(65535);
    }
    return SUCCESSFUL;
}


static void handle_his_data_test_msg(_rt_msg_t curr_msg) {
    set_heart_beat_time_by_num(*((unsigned short*)curr_msg->msg.data));
    gen_test_sample_data(*((unsigned short*)curr_msg->msg.data));
}

void handle_save_his_alm_msg(_rt_msg_t curr_msg) {
    sig_alm_chg_msg_t* real_alm = (sig_alm_chg_msg_t*)curr_msg->msg.data;
    RETURN_IF_FAIL(real_alm->alarm_value != 1);   // 拦截新告警

    his_alm_t his_alm_data = {0};
    his_alm_data.alm_code = ALM_ID_GET_ALM_CODE(real_alm->alarm_id) + ALM_ID_GET_DEV(real_alm->alarm_id); 
    time_t_to_timestruct(real_alm->start_time, &his_alm_data.start_time);
    time_t_to_timestruct(real_alm->end_time, &his_alm_data.end_time);
    save_his_alarm(&his_alm_data);
}

/**
 * @brief 清除历史告警
*/
void handle_clean_his_alm_msg(_rt_msg_t curr_msg) {
    clean_his_alarm();
}

void handle_operate_record_test_msg(_rt_msg_t curr_msg) {
    set_heart_beat_time_by_num(*((unsigned short*)curr_msg->msg.data));
    gen_test_event_record(*((unsigned short*)curr_msg->msg.data));
}

void handle_his_alm_test_msg(_rt_msg_t curr_msg) {
    set_heart_beat_time_by_num(*((unsigned short*)curr_msg->msg.data));
    save_his_alarm_for_test(*((unsigned short*)curr_msg->msg.data));
}


void handle_save_iv_data_msg(_rt_msg_t curr_msg) {
    unsigned char* data = (unsigned char*)(*((unsigned int*)(curr_msg->msg.data)));
    save_iv_data(data);
}

void handle_energy_data_test_msg(_rt_msg_t curr_msg) {
    set_heart_beat_time_by_num(*((unsigned short*)curr_msg->msg.data));
    gen_energy_record_test_data();
}

void handle_save_his_data_msg(_rt_msg_t curr_msg) {
    unsigned char flag = get_stop_his_data_flag();
    if(flag == FALSE)
    {
        save_pv_his_data();
    }
}

void handle_check_dc_ac_para_msg(_rt_msg_t curr_msg) {
    para_check_period();
}

void handle_autho_time_check_msg(_rt_msg_t curr_msg) {
    autho_time_check(dev_dc_ac);
}

void handle_save_record_fault_msg(_rt_msg_t curr_msg) {
    save_record_fault_file((record_fault_msg_t*)curr_msg->msg.data);
}

void handle_crt_one_change_msg(_rt_msg_t curr_msg) {
    crt_date_parse();
}

/**
 * @brief 处理执行告警历史数据处理功能的消息
 * @param[in] curr_msg 消息
*/
static void handle_process_alm_his_data_msg(_rt_msg_t curr_msg) {
    alm_his_data_queue_process();
}

static void handle_stop_para_check_msg(_rt_msg_t curr_msg) {
    stop_para_check();
}

static void handle_start_para_check_msg(_rt_msg_t curr_msg) {
    start_para_check();
}

static void handle_restore_factory_msg(_rt_msg_t curr_msg) 
{
    restore_factory();
}

static void handle_grid_code_change_msg(_rt_msg_t curr_msg) 
{
    grid_code_change(TRUE);
}

static void handle_update_status_write_msg(_rt_msg_t curr_msg)
{
    // 写升级状态统一接口
    update_sta_msg_t* sta_msg = ((update_sta_msg_t *)curr_msg->msg.data);
    write_event_handle(sta_msg);

}

static void  handle_reset_csu_msg(_rt_msg_t curr_msg)
{
     system_reset(NO_RESET_REMOTE_CTRL);
}

static void handle_save_event_msg(_rt_msg_t curr_msg)
{
    event_record_t data = {};
    rt_memcpy(&data, (event_record_t*)curr_msg->msg.data, sizeof(event_record_t));
    rt_kprintf("handle_save_event_msg|id:0x%x, info:%s\n", data.event_id, data.info);
    save_event_record(&data);
    
}

void handle_del_his_data_msg(_rt_msg_t curr_msg)
{
    del_his_data_when_update();
}

static void handle_stop_iv_msg(_rt_msg_t curr_msg)
{
    del_save_iv_index();
}

void handle_save_fac_data_msg(_rt_msg_t curr_msg)
{
    save_fac_data();
}
static void handle_network_download_file(_rt_msg_t curr_msg)
{
    int opt = *(int*)curr_msg->msg.data;
    int file_type = *(int*)((char*)curr_msg->msg.data + 4);
    unsigned int file_size = *(unsigned int*)((char*)curr_msg->msg.data + 8);
    unsigned int file_crc = *(unsigned int*)((char*)curr_msg->msg.data + 12);
    short addr_info = *(unsigned int*)((char*)curr_msg->msg.data + 16);
    rt_kprintf("system_manage|opt:%d, type:%d, size:%d, crc:%d, addr_info:0x%x\n", opt, file_type, file_size, file_crc, addr_info);
    network_update_deal(opt, file_type, file_size, file_crc, addr_info);

}
void handle_rs485_stat(_rt_msg_t curr_msg)
{
    rs485_stat();
}

static void handle_client_key_msg(_rt_msg_t curr_msg)
{
    deal_client_key_msg((char*)curr_msg->msg.data);
}

void handle_chk_all_alm_chg_msg(_rt_msg_t curr_msg)
{
    flag_check_all_alm_t* alm_flag = (flag_check_all_alm_t*)curr_msg->msg.data;
    if(alm_flag->save_his_data_flag)
    {
        // 有新告警产生，保存当前及告警前后的历史数据
        unsigned char flag = get_stop_his_data_flag();
        if(flag == FALSE)
        {
            alm_happen_save_his_data();
        } 
    }
    if(alm_flag->save_real_alm_flag)
    {
        // 保存告警到EEPROM
        save_real_alarms();
        // 处理告警总、事故总信号
        handle_total_alarm_event();
    }

}

void handle_south_upload_msg(_rt_msg_t curr_msg)
{
    char* file_name = (char*)curr_msg->msg.data;       // 上传文件名
    rt_kprintf("system_manage|file_name:%s\n", file_name);
    start_file_upload(file_name);
}

void handle_clean_afci_msg(_rt_msg_t curr_msg)
{
    clean_afci_data();
}

void handle_afci_file_export(_rt_msg_t curr_msg)
{
    unsigned char afci_sta = 0;
    set_one_data(DAC_DATA_ID_AFCI_FILE_STATUS, &afci_sta); // 清除AFCI文件状态
}


void handle_clean_his_data(_rt_msg_t curr_msg)
{
    delete_his_data_file();
}

void handle_clean_his_event(_rt_msg_t curr_msg)
{
    clean_his_event();
}

void handle_clean_iv_data(_rt_msg_t curr_msg)
{
    delete_iv_data();
}

void handle_clean_fault_record(_rt_msg_t curr_msg)
{
    delete_fault_record_file();
}



void init_soft_info()
{
    date_base_t data = {0};
    boot_info_t boot_info = {0};

    set_one_data(DAC_DATA_ID_MONITOR_DIFF_UPDATE_FLAG , BMS_VER_FLAG);
    set_one_data( DAC_DATA_ID_MONITOR_SOFTWARE_VER , PV_COMPILE_VERSION);
    rt_sscanf_s( PV_COMPILE_VERSION_DATE ,"%hu-%hhu-%hhu", &data.year, &data.month, &data.day);
    set_one_data(DAC_DATA_ID_MONITOR_SOFTWARE_VER_DATE , &data);
    LOG_E("SOFT VERSION:V%s_%s",PV_COMPILE_VERSION,PV_COMPILE_VERSION_DATE);

    handle_storage(read_opr, ONCHIP_BOOT_DATA, (unsigned char *)&boot_info, sizeof(boot_info_t), 0);

    set_one_data(DAC_DATA_ID_MONITOR_BOOT_VER, boot_info.ver);
    rt_sscanf_s( boot_info.data ,"%hu-%hhu-%hhu", &data.year, &data.month, &data.day);
    set_one_data(DAC_DATA_ID_MONITOR_BOOT_VER_DATE , &data);

    return;
}

void system_reset(unsigned char reason)
{
    save_reset_data(reason);
    rt_kprintf("system soft reset");
    rt_thread_mdelay(15000);
    rt_hw_cpu_reset();
}

void sys_run_result()
{
    static update_file_manage_t update_info = {0};
    read_download_tmpInfo(&update_info);
    update_info.sys_run_flag = TRUE;
    update_info.count = 0;
    update_sta_msg_t sta_msg = {0};
    sta_msg.south_dev = INVALID_DEV_TYPE;

    if(update_info.update_status == DIFF_UPDATE_FAILURE || update_info.update_status == UPDATE_FAILURE|| update_info.update_status == UPDATE_SUCCESS)
    {
        LOG_E("UPDATE RESULT: %d ( 1: UPDATE_SUCCESS 2: UPDATE_FAILURE,4:DIFF_UPDATE_FAILURE)",update_info.update_status);
        // 升级成功 or 失败写操作记录
        sta_msg.data_s = update_info.update_status == UPDATE_FAILURE || update_info.update_status == DIFF_UPDATE_FAILURE ? MONITER_UPDATE_FAILED : MONITER_UPDATE_SUCCESS;
        rt_memcpy_s(sta_msg.info, MAX_EVENT_INFO_LEN, update_info.update_status == UPDATE_FAILURE || update_info.update_status == DIFF_UPDATE_FAILURE ? "csu update failed" : "csu update success", MAX_EVENT_INFO_LEN);
        write_event_handle(&sta_msg);
    }
    update_info.update_status = NO_UPDATEING;
    write_download_tmpInfo(&update_info);
}

void handle_delay_update(_rt_msg_t curr_msg)
{
    check_update_enable();
}

void handle_start_delay_update_msg(_rt_msg_t curr_msg)
{
    start_delay_update();
}

int register_common_fun(void)
{
    common_fun_t* common_fun = NULL;
    common_fun = get_register_common_fun();
    common_fun->ptr_ctrl_led_when_update = ctrl_led_when_update;
    common_fun->ptr_set_pv_update_sta = set_pv_update_status;
    common_fun->ptr_get_pv_update_sta = get_pv_update_status;
    common_fun->north_thread_beat_go_on = north_thread_beat_go_on;
    common_fun->ptr_is_wifi_should_recover = is_wifi_should_recover;
    return SUCCESSFUL;
}

static void handle_para_file_import(_rt_msg_t curr_msg)
{
    char* para_msg = ((char *)curr_msg->msg.data);
    para_file_import_parse(para_msg);
}

void handle_para_file_export(_rt_msg_t curr_msg)
{
    para_file_export_handle();
}

void handle_grid_code_check_msg(_rt_msg_t curr_msg)
{
    grid_code_status_check();
}



void handle_enter_apptest(_rt_msg_t curr_msg)
{
    // 使用静态变量确保只进入一次，使用更简洁的逻辑判断
    static unsigned char apptest_triggered = FALSE;
    if (apptest_triggered == FALSE)
    {
        Creat_North_South_Thread();
        apptest_triggered = TRUE;
    }
}




void handle_format_file_sys(_rt_msg_t curr_msg)
{
    save_reset_data(NO_RESET_FORMAT_FILE_SYS);
    rt_kprintf("format filesys reset csu\n");
    rt_thread_mdelay(10000);
    rt_hw_cpu_reset();
}
