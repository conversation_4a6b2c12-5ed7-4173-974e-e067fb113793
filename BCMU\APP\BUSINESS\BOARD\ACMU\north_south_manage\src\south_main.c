#include "north_south_main.h"
#include "dev_acmu.h"

Static int check_energy_empty(dev_comm_t* dev_comm);
static dev_comm_t* south_comm_thread_init(unsigned char dev_type_id);

Static msg_map south_msg_map[] =
{
    {0,NULL},//临时添加解决编译问题
};

static dev_comm_t* south_comm_thread_init(unsigned char dev_type_id) 
{
    dev_comm_t* dev_comm = NULL;
    dev_comm = init_dev_comm_info(dev_type_id);
    if (dev_comm == NULL)
        return NULL;

    return dev_comm;
}

Static int init_acmu_poll(dev_comm_t* dev_comm)
{
    dev_comm->dev_mgr->cmd_timer = RT_NULL;
    dev_comm->dev_mgr->comm_dev->dev_type->cmd_timeout = TIMEOUT_COMMFAIL_MAXTIME;
    dev_comm->dev_mgr->comm_dev->dev_type->cmd_fail_thresh  = 6;
    start_dev_poll(dev_comm,NULL);
    return 0 ;
}

void* init_south_comm(void * param)
{   
    server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(south_msg_map) / sizeof(msg_map);
    register_server_msg_map(south_msg_map, server_info);
    dev_comm_t* dev_comm = south_comm_thread_init(DEV_SOUTH);
    RETURN_VAL_IF_FAIL((dev_comm != NULL), NULL);
    init_acmu_poll(dev_comm);
    return dev_comm;
}

/* Started by AICoder, pid:7511crf88ea93a914d1b0a52a04d2a125923296d */
Static int check_energy_empty(dev_comm_t* dev_comm)
{
    acem_analog_data_t acem_show_data;

    rt_memset_s(&acem_show_data, sizeof(acem_show_data), 0, sizeof(acem_show_data));
    get_acem_show_data(&acem_show_data);

    if (acem_show_data.total_energy < TOTAL_ENERGY_MAX) {
        dev_comm->dev_mgr->poll_opt = POLL_ENABLE;
    } else {
        dev_comm->dev_mgr->poll_opt = POLL_DISABLE;
        dev_comm->dev_mgr->not_poll_dev = 0;
        dev_comm->dev_mgr->not_poll_cmd = 0;
    }
    return 0;
}
/* Ended by AICoder, pid:7511crf88ea93a914d1b0a52a04d2a125923296d */

/* 通讯断判断函数 */
short judge_comm_status(dev_comm_t* dev_comm)
{
    if(get_acem_cfg() == 0 || dev_comm->dev_mgr->cmd_timer == RT_NULL)
    {
        set_acem_timeout_flag(FALSE);
        return 0;
    }

    if(dev_comm->dev_mgr->comm_dev->state == STAT_COMM_FAIL)
    {
        set_acem_timeout_flag(TRUE);
    }
    else
    {
        set_acem_timeout_flag(FALSE);
    }
    return 0;
} 

void south_comm_thread(void *param) {
    dev_comm_t* dev_comm = (dev_comm_t*)param;
    link_inst_t* link_inst = dev_comm->dev_inst[0].dev_type->link_inst;
    thread_monitor_register("south_comm");
    while (is_running(TRUE)) {

        rt_thread_mdelay(50);
        thread_monitor_update_heartbeat();
        judge_comm_status(dev_comm);
        updata_acem_fact_info();
        
        // 先发送数据
        if(dev_comm->dev_mgr->cmd_sta == CMD_FINISH){
            check_energy_empty(dev_comm);
            exe_next_cmd_id_version(dev_comm);
        }
        // 接收命令应答数据
        if(rt_sem_take(&(link_inst->rx_sem), 5) == RT_EOK) {
            if (cmd_recv(dev_comm->dev_mgr->comm_dev, dev_comm->cmd_buf) == SUCCESSFUL)
            {
                update_dev_mgr_info_north_id_version(dev_comm);
            }
        }
    }
}