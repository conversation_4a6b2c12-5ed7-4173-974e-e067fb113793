#include "dev_north_idub_apptest.h"
#include "cmd.h"
#include "protocol_layer.h"
#include "protocol_1363_comm.h"
#include "sps.h"
#include "utils_rtthread_security_func.h"
#include "data_type.h"
#include "utils_time.h"
#include "utils_data_transmission.h"
#include "storage.h"
#include "bspconfig.h"
#include "realdata_id_in.h"
#include "ee_public_info.h"
#include "software_version.h"
#include "utils_flag.h"
#include "rthw.h"
#include "ee_public_info.h"
#include "realdata_save.h"


static s1363_cmd_head_t cmd_req[] = {
    {VER_22 , CMD_NONE ,     CMD_NONE},    ///< 0
    {VER_22 , CID1_DC ,      CID2_TRIG_APPTEST},            ///<1 触发apptest命令
    {VER_22 , CID1_DC ,      CID2_EXIT_APPTEST},            ///<2 触发apptest命令
    {VER_22 , CID1_COMMON ,  CID2_GET_PROTO_VER},           ///<3 获取协议版本号命令
    {VER_22 , CID1_DC ,      CID2_GET_FACT_INFO},           ///<4 获取厂家信息命令
    {VER_22 , CID1_DC ,      CID2_CHIP_TEST_INFO},          ///<5 获取芯片测试信息命令
    {VER_22 , CID1_DC ,      CID2_SET_HARDWARE_VER},        ///<6 设置硬件版本号
    {VER_22 , CID1_DC ,      CID2_GET_HARDWARE_VER},        ///<7 获取硬件版本号
    {VER_22 , CID1_DC ,      CID2_SET_BOARD_MANU_DATE},     ///<8 设置单板生产日期
    {VER_22 , CID1_DC ,      CID2_GET_BOARD_MANU_DATE},     ///<9 获取单板生产日期
    {VER_22 , CID1_DC ,      CID2_GET_AI_SIGNAL},           ///<10 读取AI信号
    {VER_22 , CID1_COMMON ,  CID2_GET_DEVICE_ADDR},         ///<11 获取通讯地址
    {VER_22 , CID1_DC ,      CID2_SET_SN},                  ///<12 设置序列号
    {VER_22 , CID1_DC ,      CID2_GET_SN},                  ///<13 获取序列号
};

/* 命令应答头 */
static s1363_cmd_head_t cmd_ack[] = {
    {VER_22 , CMD_NONE ,     CMD_NONE},    ///< 0
    {VER_22 , CID1_DC ,      CID2_TRIG_APPTEST},            ///<1 触发apptest命令
    {VER_22 , CID1_DC ,      CID2_EXIT_APPTEST},            ///<2 触发apptest命令
    {VER_22 , CID1_COMMON ,  CID2_GET_PROTO_VER},           ///<3 获取协议版本号命令
    {VER_22 , CID1_DC ,      CID2_GET_FACT_INFO},           ///<4 获取厂家信息命令
    {VER_22 , CID1_DC ,      CID2_CHIP_TEST_INFO},          ///<5 获取芯片测试信息命令
    {VER_22 , CID1_DC ,      CID2_SET_HARDWARE_VER},        ///<6 设置硬件版本号
    {VER_22 , CID1_DC ,      CID2_GET_HARDWARE_VER},        ///<7 获取硬件版本号
    {VER_22 , CID1_DC ,      CID2_SET_BOARD_MANU_DATE},     ///<8 设置单板生产日期
    {VER_22 , CID1_DC ,      CID2_GET_BOARD_MANU_DATE},     ///<9 获取单板生产日期
    {VER_22 , CID1_DC ,      CID2_GET_AI_SIGNAL},           ///<10 读取AI信号
    {VER_22 , CID1_COMMON ,  CID2_GET_DEVICE_ADDR},         ///<11 获取通讯地址
    {VER_22 , CID1_DC ,      CID2_SET_SN},                  ///<12 设置序列号
    {VER_22 , CID1_DC ,      CID2_GET_SN},                  ///<13 获取序列号
};



int apptest_comm_parse(void* dev_inst, void* cmd_buff) 
{
    RETURN_VAL_IF_FAIL(get_apptest_flag() == TRUE, NO_NEED_REPONSE);
    return SUCCESSFUL;
}



int apptest_exit_parse(void* dev_inst, void* cmd_buff) 
{
    RETURN_VAL_IF_FAIL(get_apptest_flag() == TRUE, NO_NEED_REPONSE);
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    cmd_buf_t send_cmd_buff;
    rt_memset_s(&send_cmd_buff, sizeof(cmd_buf_t), 0, sizeof(cmd_buf_t));
    send_cmd_buff.cmd = ((cmd_buf_t*)cmd_buff)->cmd;
    cmd_send(dev_inst, &send_cmd_buff);
    rt_thread_mdelay(1000);
    rt_hw_cpu_reset();
    return SUCCESSFUL;
}



int apptest_trig_pack(void* dev_inst, void* cmd_buff) 
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    static int apptest_trig_count = 0;
    int offset = 0;
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;
    apptest_trig_count++;
    if(apptest_trig_count < 3)
    {
        data_buff[offset] = 0x00;
    }
    else
    {
        data_buff[offset] = 0x01;
        set_apptest_flag(TRUE);
    }
    offset++;
    ((cmd_buf_t*)cmd_buff)->data_len=offset;
    return SUCCESSFUL;
}



int get_fact_info_pack(void* dev_inst, void* cmd_buff) 
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    int offset = 0;
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;

    rt_memset_s(data_buff, SOFTWARE_INFO_LEN, 0, SOFTWARE_INFO_LEN);
    rt_memcpy_s(&data_buff[offset], SOFTWARE_NAME_LEN, IDUB_SOFTWARE_NAME, sizeof(IDUB_SOFTWARE_NAME));
    offset += SOFTWARE_NAME_LEN;
    rt_memcpy_s(&data_buff[offset], SOFTWARE_VER_LEN, IDUB_COMPILE_VERSION, sizeof(IDUB_COMPILE_VERSION));
    offset += SOFTWARE_VER_LEN;
    rt_memcpy_s(&data_buff[offset], SOFTWARE_DATE_LEN, IDUB_COMPILE_VERSION_DATE, SOFTWARE_DATE_LEN);
    offset += SOFTWARE_DATE_LEN;

    ((cmd_buf_t*)cmd_buff)->data_len = offset;
    return SUCCESSFUL;
}



int get_chip_test_info_pack(void* dev_inst, void* cmd_buff) 
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    int offset = 0;
    unsigned char status = 1;
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;
    unsigned short erase_data = 0xFFFF, write_data = 0x1010;
    handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&erase_data, sizeof(erase_data), 0);
    erase_data = 0;
    handle_storage(read_opr,  EE_PUBLIC_INFO, (unsigned char*)&erase_data, sizeof(erase_data), 0);

    handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&write_data, sizeof(write_data), 0);
    write_data = 0;
    handle_storage(read_opr,  EE_PUBLIC_INFO, (unsigned char*)&write_data, sizeof(write_data), 0);
    if(erase_data == 0xFFFF && write_data == 0x1010)
    {
        status = 0;
    }

    data_buff[offset] = 0x01;
    offset++;
    data_buff[offset] = status;
    offset++;

    ((cmd_buf_t*)cmd_buff)->data_len=offset;
    return SUCCESSFUL;
}



int set_hardware_version_parse(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(get_apptest_flag() == TRUE, NO_NEED_REPONSE);
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    int offset = 0;
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;
    unsigned char hardware_version[HARDWARE_VER_LEN] = {0};
    unsigned char origin_hardware_version[HARDWARE_VER_LEN] = {0};
    rt_memcpy_s(hardware_version, HARDWARE_VER_LEN, &data_buff[offset], HARDWARE_VER_LEN - 1);

    handle_storage(read_opr, EE_PUBLIC_INFO, origin_hardware_version, HARDWARE_VER_LEN, HARDWARE_VERSION_OFFSET);
    if(memcmp(&origin_hardware_version[0], &hardware_version[0], HARDWARE_VER_LEN) == 0)
    {
        return SUCCESSFUL;
    }

    if(handle_storage(write_opr, EE_PUBLIC_INFO, hardware_version, HARDWARE_VER_LEN, HARDWARE_VERSION_OFFSET) != SUCCESSFUL)
    {
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA;
        rt_kprintf("set_hardware_version_parse write_opr error!!!\n");
        return FAILURE;
    }
    set_one_data(IDUB_DATA_ID_HARDWARE_VERSION, hardware_version);
    return SUCCESSFUL;
}

int get_hardware_version_pack(void* dev_inst, void* cmd_buff) 
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;
    unsigned char hardware_version[HARDWARE_VER_LEN] = {0};
    if(handle_storage(read_opr, EE_PUBLIC_INFO, hardware_version, HARDWARE_VER_LEN, HARDWARE_VERSION_OFFSET)!= SUCCESSFUL)
    {
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA;
        rt_kprintf("get_hardware_version_pack read_opr error!!!\n");
        return FAILURE;
    }
    rt_memset_s(data_buff, HARDWARE_VER_LEN, 0, HARDWARE_VER_LEN);
    rt_memcpy_s(data_buff, HARDWARE_VER_LEN, hardware_version, HARDWARE_VER_LEN - 1);

    ((cmd_buf_t*)cmd_buff)->data_len= HARDWARE_VER_LEN;
    return SUCCESSFUL;
}



int set_board_manu_date_parse(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(get_apptest_flag() == TRUE, NO_NEED_REPONSE);
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    int offset = 0;
    date_base_t tm = {0};
    date_base_t origin_tm = {0};
    unsigned char board_manu_date[BOARD_MANU_DATE_LEN] = {0};
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;

    tm.year = get_int16_data(&data_buff[offset]);
    offset+=2;
    tm.month  = data_buff[offset++];
    tm.day = data_buff[offset++];

    if(check_date_valid(&tm) != TRUE)
    {
        LOG_E("%s | %d | set manu date failed \n", __FUNCTION__ , __LINE__);
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA ;
        return FAILURE;
    }

    handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char*)&origin_tm, sizeof(origin_tm), MANU_DATE_OFFSET);
    if(tm.year == origin_tm.year && tm.month == origin_tm.month && tm.day == origin_tm.day)
    {
        return SUCCESSFUL;
    }

    if(handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&tm, sizeof(tm), MANU_DATE_OFFSET) != SUCCESSFUL)
    {
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA;
        rt_kprintf("set_board_manu_date_parse write_opr error!!!\n");
        return FAILURE;
    }
    date_to_string((char *)board_manu_date, (date_base_t *)&tm);
    set_one_data(IDUB_DATA_ID_BOARD_MANU_DATE, board_manu_date);
    return SUCCESSFUL;
}

int get_board_manu_date_pack(void* dev_inst, void* cmd_buff) 
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    int offset = 0;
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;
    date_base_t manu_date = {0};
    if(handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char*)&manu_date, sizeof(manu_date), MANU_DATE_OFFSET)!= SUCCESSFUL)
    {
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA;
        rt_kprintf("get_board_manu_date_pack read_opr error!!!\n");
        return FAILURE;
    }
    put_date_to_buff(data_buff, manu_date);
    offset += 4;
    ((cmd_buf_t*)cmd_buff)->data_len=offset;
    return SUCCESSFUL;
}



static data_info_id_verison_t cmd_pack_ai_signal_info[] =
{
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_CURRENT     },
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_CURRENT + 1 },
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_CURRENT + 2 },
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_CURRENT + 3 },
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_CURRENT + 4 },
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_CURRENT + 5 },
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_CURRENT + 6 },
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_CURRENT + 7 },
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_CURRENT + 8 },
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_CURRENT + 9 },
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_CURRENT + 10},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_CURRENT + 11},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_CURRENT + 12},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_CURRENT + 13},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_CURRENT + 14},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_CURRENT + 15},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_CURRENT + 16},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_CURRENT + 17},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_CURRENT + 18},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_CURRENT + 19},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_CURRENT + 20},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_CURRENT + 21},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_CURRENT + 22},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_CURRENT + 23},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_CURRENT + 24},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_CURRENT + 25},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_CURRENT + 26},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_CURRENT + 27},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_CURRENT + 28},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_CURRENT + 29},

    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT     },
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 1 },
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 2 },
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 3 },
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 4 },
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 5 },
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 6 },
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 7 },
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 8 },
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 9 },
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 10},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 11},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 12},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 13},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 14},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 15},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 16},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 17},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 18},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 19},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 20},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 21},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 22},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 23},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 24},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 25},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 26},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 27},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 28},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_BRANCH_LEAKAGE_CURRENT + 29},
    
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_RSV_BRANCH_CURRENT},
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, IDUB_DATA_ID_RSV_BRANCH_CURRENT + 1},
};



static cmd_parse_info_id_verison_t cmd_pack_info[] RAM_SECTION = {

    {&cmd_pack_ai_signal_info[0],       sizeof(cmd_pack_ai_signal_info)/sizeof(data_info_id_verison_t)},
};



int get_addr_pack(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(get_apptest_flag() == TRUE, NO_NEED_REPONSE);
    unsigned short device_addr = 0;
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;

    if(handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char*)&device_addr, sizeof(unsigned char), DEVICE_ADDR_OFFSET) != SUCCESSFUL)
    {
        LOG_E("get addr | read storage error.");
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA;
        return FAILURE;
    }
    put_uint16_to_buff(data_buff, device_addr);
    ((cmd_buf_t*)cmd_buff)->data_len = sizeof(unsigned short);
    return SUCCESSFUL;
}



int set_sn_parse(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(get_apptest_flag() == TRUE, NO_NEED_REPONSE);
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    int offset = 0;
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;
    unsigned char sn[SN_LEN] = {0};
    unsigned char origin_sn[SN_LEN] = {0};
    rt_memcpy_s(sn, SN_LEN, &data_buff[offset], SN_LEN);

    handle_storage(read_opr, EE_PUBLIC_INFO, origin_sn, SN_LEN, SN_OFFSET);
    if(memcmp(&origin_sn[0], &sn[0], SN_LEN) == 0)
    {
        return SUCCESSFUL;
    }

    if(handle_storage(write_opr, EE_PUBLIC_INFO, sn, SN_LEN, SN_OFFSET) != SUCCESSFUL)
    {
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA;
        rt_kprintf("set_sn_parse write_opr error!!!\n");
        return FAILURE;
    }
    set_one_data(IDUB_DATA_ID_SERIAL_NUMBER, sn);
    return SUCCESSFUL;
}



int get_sn_pack(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;
    unsigned char sn[SN_LEN] = {0};

    if(handle_storage(read_opr, EE_PUBLIC_INFO, sn, SN_LEN, SN_OFFSET) != SUCCESSFUL)
    {
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA;
        rt_kprintf("get_sn_parse read_opr error!!!\n");
        return FAILURE;
    }
    rt_memset_s(data_buff, SN_LEN, 0, SN_LEN);
    rt_memcpy_s(data_buff, SN_LEN, sn, SN_LEN);

    ((cmd_buf_t*)cmd_buff)->data_len = SN_LEN;
    return SUCCESSFUL;
}



static cmd_handle_register_t s_cmd_handle[] = 
{
    {DEV_NORTH_IDUB_APPTEST, IDUB_APPTEST_TRIG, CMD_TYPE_NO_POLL, NULL, apptest_trig_pack},    //<apptest触发命令
    {DEV_NORTH_IDUB_APPTEST, IDUB_APPTEST_EXIT, CMD_TYPE_NO_POLL, apptest_exit_parse, NULL},    //<apptest退出命令
    {DEV_NORTH_IDUB_APPTEST, IDUB_GET_PROTO_VER, CMD_TYPE_NO_POLL, NULL, NULL},    //<获取协议版本号
    {DEV_NORTH_IDUB_APPTEST, IDUB_GET_FACT_INFO, CMD_TYPE_NO_POLL, apptest_comm_parse, get_fact_info_pack},    //<获取厂家信息
    {DEV_NORTH_IDUB_APPTEST, IDUB_CHIP_TEST_INFO, CMD_TYPE_NO_POLL, apptest_comm_parse, get_chip_test_info_pack},    //<获取芯片测试信息
    {DEV_NORTH_IDUB_APPTEST, IDUB_SET_HARDWARE_VER, CMD_TYPE_NO_POLL, set_hardware_version_parse, NULL},    //<设置硬件版本号
    {DEV_NORTH_IDUB_APPTEST, IDUB_GET_HARDWARE_VER, CMD_TYPE_NO_POLL, apptest_comm_parse, get_hardware_version_pack},    //<获取硬件版本号
    {DEV_NORTH_IDUB_APPTEST, IDUB_SET_BOARD_MANU_DATE, CMD_TYPE_NO_POLL, set_board_manu_date_parse, NULL},    //<设置单板生产日期
    {DEV_NORTH_IDUB_APPTEST, IDUB_GET_BOARD_MANU_DATE, CMD_TYPE_NO_POLL, apptest_comm_parse, get_board_manu_date_pack},    //<获取单板生产日期
    {DEV_NORTH_IDUB_APPTEST, IDUB_GET_DEVICE_ADDR, CMD_TYPE_NO_POLL, apptest_comm_parse, get_addr_pack},  //获取通讯地址
    {DEV_NORTH_IDUB_APPTEST, IDUB_SET_SN, CMD_TYPE_NO_POLL, set_sn_parse, NULL},  //设置序列号
    {DEV_NORTH_IDUB_APPTEST, IDUB_GET_SN, CMD_TYPE_NO_POLL, apptest_comm_parse, get_sn_pack},  //获取序列号
};



/* 命令总表,必须以空字符串""结束 */
static cmd_t no_poll_cmd_tab[] = 
{
    {IDUB_APPTEST_TRIG, CMD_PASSIVE, &cmd_req[1], &cmd_ack[1], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {IDUB_APPTEST_EXIT, CMD_PASSIVE, &cmd_req[2], &cmd_ack[2], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {IDUB_GET_PROTO_VER, CMD_PASSIVE, &cmd_req[3], &cmd_ack[3], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {IDUB_GET_FACT_INFO, CMD_PASSIVE, &cmd_req[4], &cmd_ack[4], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {IDUB_CHIP_TEST_INFO, CMD_PASSIVE, &cmd_req[5], &cmd_ack[5], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {IDUB_SET_HARDWARE_VER, CMD_PASSIVE, &cmd_req[6], &cmd_ack[6], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {IDUB_GET_HARDWARE_VER, CMD_PASSIVE, &cmd_req[7], &cmd_ack[7], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {IDUB_SET_BOARD_MANU_DATE, CMD_PASSIVE, &cmd_req[8], &cmd_ack[8], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {IDUB_GET_BOARD_MANU_DATE, CMD_PASSIVE, &cmd_req[9], &cmd_ack[9], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {IDUB_GET_AI_SIGNAL, CMD_PASSIVE, &cmd_req[10], &cmd_ack[10], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, apptest_comm_parse, &cmd_pack_info[0]},
    {IDUB_GET_DEVICE_ADDR, CMD_PASSIVE, &cmd_req[11], &cmd_ack[11], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {IDUB_SET_SN, CMD_PASSIVE, &cmd_req[12], &cmd_ack[12], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {IDUB_GET_SN, CMD_PASSIVE, &cmd_req[13], &cmd_ack[13], sizeof(s1363_cmd_head_t), NULL, NULL, SELF_PACK, SELF_PARSE},
    {0},
};



static dev_type_t s_idub_apptest_dev = {
    DEV_NORTH_IDUB_APPTEST, 1, PROTOCOL_YD_1363, LINK_NORTH_IDUB, R_BUFF_LEN_1024, S_BUFF_LEN_1024, 0, no_poll_cmd_tab, NULL
};

dev_type_t* init_idub_dev_apptest(void)
{
    return &s_idub_apptest_dev;
}



void apptest_register_cmd_table()
{
    for(int i = 0; i < sizeof(s_cmd_handle)/sizeof(s_cmd_handle[0]); i++) 
    {
        register_cmd_handle(&s_cmd_handle[i]);
    }
}

