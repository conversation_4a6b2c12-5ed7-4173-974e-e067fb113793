#ifndef _HIS_RECORD_H
#define _HIS_RECORD_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus
#include "hisdata_in.h"
#include "data_type.h"

//权限码定义

#define AUTH_CODE_HIS_ACTION    (0x10)
#define AUTH_CODE_HIS_ALARM     (0x11)
#define AUTH_CODE_HIS_DATA      (0x12)
#define AUTH_CODE_REAL_ALARM    (0x40)


//记录类型

#define RECORD_TYPE_HIS_ACTION  (0)
#define RECORD_TYPE_HIS_ALARM   (1)
#define RECORD_TYPE_HIS_DATA    (2)
#define RECORD_TYPE_REAL_ALARM  (3)   


//目录名称

#define DIR_NAME_ROOT              "/root"
#define DIR_NAME_HIS_RECORD        DIR_NAME_ROOT"/hisdata"
#define FILE_NAME_HIS_RECORD_INFO  DIR_NAME_HIS_RECORD"/hisaction.bin"
#define FILE_NAME_HIS_ACTION_0     DIR_NAME_HIS_RECORD"/hisaction.bin_0"
#define FILE_NAME_HIS_ACTION_1     DIR_NAME_HIS_RECORD"/hisaction.bin_1"
#define FILE_NAME_HIS_ACTION_2     DIR_NAME_HIS_RECORD"/hisaction.bin_2"
#define FILE_NAME_HIS_ACTION_3     DIR_NAME_HIS_RECORD"/hisaction.bin_3"
#define FILE_NAME_HIS_ACTION_4     DIR_NAME_HIS_RECORD"/hisaction.bin_4"
#define FILE_NAME_HIS_ACTION_5     DIR_NAME_HIS_RECORD"/hisaction.bin_5"



#define  HIS_ALARM_FILE             DIR_NAME_HIS_RECORD"/HisAlarm.bin"
#define  HIS_ALARM_FILE_0           DIR_NAME_HIS_RECORD"/HisAlarm.bin_0"
#define  HIS_ALARM_FILE_1           DIR_NAME_HIS_RECORD"/HisAlarm.bin_1"
#define  HIS_ALARM_FILE_2           DIR_NAME_HIS_RECORD"/HisAlarm.bin_2"
#define  HIS_ALARM_FILE_3           DIR_NAME_HIS_RECORD"/HisAlarm.bin_3"
#define  HIS_ALARM_FILE_4           DIR_NAME_HIS_RECORD"/HisAlarm.bin_4"
#define  HIS_ALARM_FILE_5           DIR_NAME_HIS_RECORD"/HisAlarm.bin_5"

#define REAL_ALARM_FILE             DIR_NAME_HIS_RECORD"/Real_alarm.bin"
#define REAL_ALARM_FILE_0           DIR_NAME_HIS_RECORD"/RealAlarm.bin_0"


//常规定义

#define MIN_HIS_ACTION_NUM              (500)
#define MAX_HIS_ACTION_NUM              (500*1.2)
#define SAVE_NUM_PER_FILE_HIS_ACTION    (100)
#define FILE_NUM_HIS_ACTION             ((MAX_HIS_ACTION_NUM + SAVE_NUM_PER_FILE_HIS_ACTION - 1)/SAVE_NUM_PER_FILE_HIS_ACTION)



//实时告警
#define MAX_REAL_ALARM_NUM            50
#define MIN_REAL_ALARM_NUM            0
#define MAX_REAL_ALARM_NUM_PER_FILE   50
#define REAL_ALARM_FILE_NUM           ((MAX_REAL_ALARM_NUM + MAX_REAL_ALARM_NUM_PER_FILE - 1) / MAX_REAL_ALARM_NUM_PER_FILE)
//历史告警
#define MAX_HIS_ALARM_NUM             10000
#define MIN_HIS_ALARM_NUM             8000
#define MAX_HIS_ALARM_NUM_PER_FILE    2000
#define HIS_ALARM_FILE_NUM            ((MAX_HIS_ALARM_NUM + MAX_HIS_ALARM_NUM_PER_FILE - 1) / MAX_HIS_ALARM_NUM_PER_FILE)
#define READ_HIS_ACTION_TIMEOUT       100



// 历史数据
#define MAX_HIS_DATA_NUM              10000
#define MIN_HIS_DATA_NUM              8000
#define MAX_HIS_DATA_NUM_PER_FILE     2000
#define HIS_DATA_FILE_NUM             ((MAX_HIS_DATA_NUM + MAX_HIS_DATA_NUM_PER_FILE -1) / MAX_HIS_DATA_NUM_PER_FILE)


//单条操作记录信息结构体

typedef struct
{
    time_t save_time;
    unsigned short action_id;
    char msg[20];
} his_action_record_info;



/** 历史数据记录信息结构体。 */
typedef struct
{
    time_base_t save_time;
    unsigned char his_data[HISDATA_LEN];
} his_data_record_info;

/** 历史数据打包信息结构体。 */
typedef struct {
    unsigned char precision_flag;
    unsigned char storage_type;
    void (*pack_data)(unsigned char* buff, unsigned int* offset, char precision, void* data);
} his_data_pack_t;



void init_dir(void);
void init_his_record(void);
short pub_hisaction_save_msg(unsigned short act_id, char *str);
short pub_hisrecord_read_msg(unsigned char rec_type, unsigned short rec_num, unsigned short offset, void *buff);

void* his_record_Init(void* param);
void his_record_main(void* thread_data);

int pub_his_data_save_msg(void);
void pack_int32u_data(unsigned char* buff, unsigned int* offset, char precision, void* data);
void pack_int32s_data(unsigned char* buff, unsigned int* offset, char precision, void* data);
void pack_int16u_data(unsigned char* buff, unsigned int* offset, char precision, void* data);
void pack_int16s_data(unsigned char* buff, unsigned int* offset, char precision, void* data);
void pack_int8u_data(unsigned char* buff, unsigned int* offset, char precision, void* data);
void pack_bit_data(unsigned char* buff, unsigned int* offset, char precision, void* data);
unsigned char find_pack_tab_index(unsigned char precision, unsigned char storage_type);
int save_sample_data_to_his_data(his_data_record_info * his_data_record);


#ifdef __cplusplus
}
#endif  // __cplusplus

#endif  // _HIS_RECORD_H
