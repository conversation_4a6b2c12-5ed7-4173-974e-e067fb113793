#ifndef SOFTWARE_SRC_APP_ACTIVATEPORT_H_ 
#define SOFTWARE_SRC_APP_ACTIVATEPORT_H_ 

#ifdef __cplusplus
extern "C" {
#endif

#include "battery.h"

typedef struct
{
    FLOAT      fActivatePortVol;                     // 激活口电压
    FLOAT      fBusVol;                              // 母排电压
    BOOLEAN    bActivatePortProtect;                 // 激活口相关保护
    BYTE       ucActivateCounter;                    // 持续5秒激活
    BOOLEAN    bActivateStart;                       // 是否开始激活
    BYTE       ucActivateCurrCounter;                // 电流持续5秒
    BYTE       ucActivateVoltCounter;                // 电压持续5秒
    WORD       wActivateNoCurrAndVoltCounter;        // 没有电压和电流持续5分钟
    UINT32     ulActivateTryCounter;                 // 24小时重新尝试计时
    BOOLEAN    bActivateTry;                         // 是否24小时重新尝试一次
    BOOLEAN    bActivatePortCurrError;               // 激活口回路电流异常标志（回路有充电电流）
} T_ActivatePortDebugInfo;

BOOLEAN ActivatePort(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
BOOLEAN GetActivatePortDebugInfo(T_ActivatePortDebugInfo *pActivatePortDebugInfo);
BOOLEAN GetActivateErrorFlag(void);

#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif
#endif  //SOFTWARE_SRC_APP_BDUTEST_H_;
