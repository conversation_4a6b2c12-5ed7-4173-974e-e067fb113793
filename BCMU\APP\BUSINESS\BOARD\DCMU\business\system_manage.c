
#include "softbus.h"
#include "utils_server.h"
#include "log_mgr_api.h"
#include "alarm_manage.h"
#include "system_manage.h"
#include "utils_heart_beat.h"
#include "app_config.h"
#include "thread_id.h"
#include "msg_id.h"
#include "sample.h"
#include "storage.h"
#include "gui_data_interface.h"
#include "dev_dcmu.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "utils_rtthread_security_func.h"
#include "const_define_in.h"

Static int process_recv_msg(void);
Static int check_almid_and_save_his_data(unsigned int alarm_id);
Static void handle_dcmu_alm_msg(_rt_msg_t curr_msg);
Static void handle_dcmu_all_alm_chg_msg(_rt_msg_t curr_msg);

/* 系统管理服务全局变量 */
static _rt_server_t g_sys_server;

/* 历史告警消息映射表 */
static msg_map his_alm_msg_map[] = {
    {CHECK_SIGNAL_ALARM_CHG_MSG, msg_handle_nothing},
    {CHECK_ALL_ALARM_CHG_MSG, msg_handle_nothing},
};

static log_msg_handle_process_t s_alm_msg_handle[] =
{
    {CHECK_SIGNAL_ALARM_CHG_MSG,     handle_dcmu_alm_msg},
    {CHECK_ALL_ALARM_CHG_MSG, handle_dcmu_all_alm_chg_msg},
};

static unsigned short s_trigger_save_his_data_alarm_code[] RAM_SECTION = {
    DCMU_ALM_ID_DC_VOLT_HIGH ,                                  //直流输出电压高
    DCMU_ALM_ID_DC_VOLT_LOW ,                                   //直流输出电压低
    DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN ,                        //负载回路断
    DCMU_ALM_ID_DC_LIGHTNING_PROTECTOR_DAMAGED ,                //直流防雷器状态异常
    DCMU_ALM_ID_BATTERY_VOLTAGE_LOW ,                           //电池电压低
    DCMU_ALM_ID_BATTERY_VOLTAGE_TOO_LOW ,                       //电池电压过低
    DCMU_ALM_ID_ABNORMAL_BATTERY_CURRENT ,                      //电池电流异常
    DCMU_ALM_ID_BATTERY_TEMPERATURE_HIGH ,                      //电池温度高
    DCMU_ALM_ID_BATTERY_TEMPERATURE_LOW ,                       //电池温度低
    DCMU_ALM_ID_BATTERY_LOOP_BROKEN ,                           //电池回路断
    DCMU_ALM_ID_BATTERY_DISCHARGE ,                             //电池放电
    DCMU_ALM_ID_INVALID_BATTERY_TEMPERATURE ,                   //电池温度失效
    DCMU_ALM_ID_BATTERY_TEMPERATURE_TOO_HIGH ,                  //电池温度过高
    DCMU_ALM_ID_DC_VOLTAGE_TOO_LOW ,                            //直流电压过低
    DCMU_ALM_ID_DC_VOLTAGE_TOO_HIGH ,                           //直流电压过高
    DCMU_ALM_ID_ENV_TEMP_HIGH_ALARM ,                           //环境温度高告警
    DCMU_ALM_ID_ENV_TEMP_LOW_ALARM ,                            //环境温度低告警
    DCMU_ALM_ID_ENV_HUMIDITY_HIGH_ALARM ,                       //环境湿度高告警
    DCMU_ALM_ID_ENV_HUMIDITY_LOW_ALARM ,                        //环境湿度低告警
    DCMU_ALM_ID_FLOOD_SENSOR_ALARM ,                            //水淹告警
    DCMU_ALM_ID_FUMES_SENSOR_ALARM ,                            //烟雾告警
    DCMU_ALM_ID_DOORMAT_SENSOR_ALARM ,                          //门磁告警
    DCMU_ALM_ID_ENV_TEMP_SENSOR_INVALID_ALARM ,                 //环境温度失效
    DCMU_ALM_ID_ENV_HUMIDITY_SENSOR_INVALID_ALARM ,             //环境湿度失效
    DCMU_ALM_ID_INPUT_RELAY1_ALARM,                             //输入干节点1告警
    DCMU_ALM_ID_INPUT_RELAY2_ALARM,                             //输入干节点2告警
    DCMU_ALM_ID_INPUT_RELAY3_ALARM,                             //输入干节点3告警
    DCMU_ALM_ID_INPUT_RELAY4_ALARM,                             //输入干节点4告警
};

Static int check_main_alarm_changed(unsigned short alarm_id);
Static short save_history_data(void);


Static int process_recv_msg(void)
{
    int i = 0; 
    if ((g_sys_server == NULL) || (rt_sem_take(&g_sys_server->msg_sem, 10) != RT_EOK) || (g_sys_server->msg_node == RT_NULL))
    {
        return FAILURE;
    }

    _rt_msg_t curr_msg = g_sys_server->msg_node ;
    for (i = 0; i < sizeof(s_alm_msg_handle) / sizeof(s_alm_msg_handle[0]); i++)
    {
        if (s_alm_msg_handle[i].msg_id == curr_msg->msg.msg_id)
        {
            s_alm_msg_handle[i].handle(curr_msg);
        }
    }

    rt_mutex_take(&g_sys_server->mutex, RT_WAITING_FOREVER);
    g_sys_server->msg_node = curr_msg->next;
    g_sys_server->msg_count--;
    rt_mutex_release(&g_sys_server->mutex);

    softbus_free(curr_msg);
    return SUCCESSFUL;
}



Static int check_almid_and_save_his_data(unsigned int alarm_id)
{
    int i = 0;
    unsigned short alarm_code = 0x0000;
    for(i = 0; i < sizeof(s_trigger_save_his_data_alarm_code) / sizeof(s_trigger_save_his_data_alarm_code[0]); i ++)
    {
        alarm_code = ALM_ID_GET_ALM_CODE(alarm_id); // 通过alarm_id反向获取告警码
        if(alarm_code == s_trigger_save_his_data_alarm_code[i])
        {
            pub_his_data_save_msg();
        }
    }

    return SUCCESSFUL;
}

Static void change_real_alm_change_falg()
{
    unsigned char data_flag1 = 0;
    unsigned char data_flag2 = 0;
    char buff[MAX_LETTERS];

    rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));

    get_one_data(DCMU_DATA_ID_DATA_FLAG2, &data_flag2);
    get_one_data(DCMU_DATA_ID_DATA_FLAG1, &data_flag1);
    data_flag1 |= FLAG1_ALMCH;
    set_one_data(DCMU_DATA_ID_DATA_FLAG1, &data_flag1);
    rt_snprintf_s( buff, sizeof(buff), "FLAG1:%x  FLAG2:%x", data_flag1, data_flag2);
    SetTraceStr( 10, buff );
    return;
}

Static void handle_dcmu_all_alm_chg_msg(_rt_msg_t curr_msg)
{
    change_real_alm_change_falg();
    return;
}

Static void handle_dcmu_alm_msg(_rt_msg_t curr_msg)
{
    his_alarm_info_t his_alarm = {0};
    sig_alm_chg_msg_t* real_alm = (sig_alm_chg_msg_t*)curr_msg->msg.data;

    // real_alm->alarm_value == 1 新告警
    // real_alm->alarm_value == 0 告警消失
    his_alarm.alarm_id = ALM_ID_GET_ALM_CODE(real_alm->alarm_id);
    his_alarm.index = ALM_ID_GET_DEV(real_alm->alarm_id);
    his_alarm.alarm_state = real_alm->alarm_value;
    his_alarm.start_time = (time_t)real_alm->start_time;
    his_alarm.end_time =  (time_t)real_alm->end_time;

    change_real_alm_change_falg();
    check_almid_and_save_his_data(real_alm->alarm_id);  // 检测是否是触发记录历史数据的告警ID，是则保存历史数据
    check_main_alarm_changed(his_alarm.alarm_id);

    if(real_alm->alarm_value == 0)
    {
        pub_hisalarm_save_msg(&his_alarm);
    }

    return;
}


/**
 * @description:检测是否有重要告警变化
 * @param {unsigned int} alarm_id - 告警ID
 * @return {int} 成功返回SUCCESSFUL（0）
 */
Static int check_main_alarm_changed(unsigned short alarm_code)
{
    unsigned char level = 0;
    unsigned short alm_level_offset = GET_ALM_PARA_BY_ALM_CODE(alarm_code,0);
    
    get_alm_para(alm_level_offset, &level);
    if (level == ALARMCLASS_MAJOR || level == ALARMCLASS_CRITICAL) {
         pub_msg_to_thread(MAIN_ALARM_CHANGE_MSG, NULL, 0);
    }
    return SUCCESSFUL;
}

void *init_sys_manage(void *param) {
    server_info_t *server_info = (server_info_t *)param;

    /* 注册消息映射表 */
    server_info->server.server.map_size = sizeof(his_alm_msg_map) / sizeof(msg_map);
    register_server_msg_map(his_alm_msg_map, server_info);
    return server_info;
}

void system_manage_main(void *param) {
    PRINT_MSG_AND_RETURN_IF_FAIL(param != NULL);
    thread_monitor_register("system_manage");
    /* 初始化系统服务 */
    g_sys_server = _curr_server_get();
    /* 主循环处理 */
    while (is_running(TRUE)) {
        thread_monitor_update_heartbeat();
        process_recv_msg();
        save_history_data();
        rt_thread_mdelay(SYSTEM_MANAGE_THREAD_DELAY_TIME);                   // 1秒延时
        // test_his_action_read_func(1);
    }
}


// 操作记录读取临时测试代码，之后通过协议获取或者液晶进行测试时删除
// void test_his_action_read_func(unsigned short index)
// {
//     his_action_record_info his_action_record = {0};
//     T_EventRecord his_event = {0};
//     int ret = SUCCESSFUL;

//     rt_memset_s(&his_action_record, sizeof(his_action_record_info), 0, sizeof(his_action_record_info));
//     rt_memset_s(&his_event, sizeof(T_EventRecord), 0, sizeof(T_EventRecord));

//     RETURN_VAL_IF_FAIL(index < MAX_HIS_ACTION_NUM, FAILURE);

//     ret = pub_hisrecord_read_msg(RECORD_TYPE_HIS_ACTION, 1, index, &his_action_record);

//     RETURN_VAL_IF_FAIL(ret == SUCCESSFUL, FAILURE);

//     his_event.ucID1 = his_action_record.id1;
//     his_event.ucID2 = his_action_record.id2;
//     his_event.ucIndex = his_action_record.index;
//     time_t_to_timestruct(his_action_record.save_time, &his_event.tTime);
//     rt_size_t copy_size = (sizeof(his_event.aucMsg) < sizeof(his_action_record.msg)) ?
//                           sizeof(his_event.aucMsg) : sizeof(his_action_record.msg);
//     rt_memcpy_s(his_event.aucMsg, sizeof(his_event.aucMsg), his_action_record.msg, copy_size);

//     return SUCCESSFUL;
// }

Static short save_history_data(void) {
    static unsigned int s_his_time_count = 0;
    unsigned int time_interval = 0;
    unsigned int his_time = 0;

    s_his_time_count += 1;
    his_time = (s_his_time_count * SYSTEM_MANAGE_THREAD_DELAY_TIME) / 1000; // 将毫秒转为秒
    get_one_para(DCMU_PARA_ID_HISTORY_SAVE_INTERVAL_OFFSET, &time_interval); // 历史数据存储时间间隔, 单位小时
    time_interval *= 3600; // 转换为秒

    if (his_time >= time_interval) {
        pub_his_data_save_msg();
        s_his_time_count = 0; // 重新计数
    }
    return SUCCESSFUL;
}