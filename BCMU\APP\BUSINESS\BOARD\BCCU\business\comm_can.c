﻿#include <rtthread.h>
#include <rtdevice.h>
#include <math.h>
#include <string.h>
#include "comm_can.h"
#include "msg.h"
#include "utils_server.h"
#include "utils_thread.h"
#include "utils_math.h"
#include "exchange_data_info.h"
#include "data_type.h"
#include "device_type.h"
#include "utils_rtthread_security_func.h"
#include "sps.h"
#include "cmd.h"
#include "protocol_bottom_comm.h"

Static dev_type_t s_dev_south_can = {0};
Static cmd_buf_t s_cmd_buf = {0};
Static dev_inst_t* s_dev_R321 = NULL;

Static msg_map sample_msg_map[] =
{
     {0,NULL},
};

Static dev_type_t* init_dev_south_can(void) {
    s_dev_south_can.id = DEV_BCCU_SOUTH_CAN;
    s_dev_south_can.dev_num = 1;
    s_dev_south_can.link_inst_id = LINK_BCCU_CAN1;
    s_dev_south_can.recv_buff_len = CAN1_R_BUFF_LEN;
    s_dev_south_can.send_buff_len = CAN1_S_BUFF_LEN;
    s_dev_south_can.dev_code = BOTTOM_R321_TYPE;
    return &s_dev_south_can;
}

Static dev_init_t s_dev_init_tab[] = {
    {DEV_BCCU_SOUTH_CAN, init_dev_south_can, NULL, NULL},
};

Static int can_reply_common(unsigned char func_code, unsigned char rtn);
Static int parse_alarm_data_info(unsigned char* data, unsigned int data_len, char alm_val);

Static exchange_data_handle_t s_short_frm_handle[] =
{
    {FUNC_BATTERY_ALARM_HAPPEN, parse_alarm_data_info, TRUE},
    {FUNC_BATTERY_ALARM_RECOVER, parse_alarm_data_info, FALSE},
};

void* CAN_comm_init(void *param)
{
    server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(sample_msg_map) / sizeof(msg_map);
    register_server_msg_map(sample_msg_map, server_info);

    init_dev_tab(s_dev_init_tab, sizeof(s_dev_init_tab) / sizeof(s_dev_init_tab[0]));
    s_dev_R321 = init_can_common();

    return &s_dev_R321;
}

void process_CAN_comm(void* parameter)
{
    while (is_running(TRUE))
    {
        if (RT_EOK == rt_sem_take(&(s_dev_R321->dev_type->link_inst->rx_sem), NORTH_CAN1_DELAY)) {
            if (FAILURE == linker_recv(s_dev_R321, &s_cmd_buf)) {
                continue;
            }

            deal_receviced_can_short_frame(s_dev_R321, s_short_frm_handle, sizeof(s_short_frm_handle)/sizeof(&s_short_frm_handle[0]));
        }
    }
}

/**
 * @brief 解析柜内主机上传的告警数据
 * @param[in] data 帧数据段
 * @param[in] data_len 帧数据段长度
 * @param[in] alm_val 告警值，TRUE: 告警产生，FALSE: 告警消失
*/
Static int parse_alarm_data_info(unsigned char* data, unsigned int data_len, char alm_val)
{
    unsigned short id = 0;
    alarm_data_t last_data = {0};
    alarm_data_t current_data = {0};

    if((data == NULL) || (data_len != DATA_LEN_OF_FRAME))
    {
        return FAILURE;
    }

    id = data[ID_INDEX_IN_FRAME];
    last_data = get_battery_alarm_data(id - 1);

    if (last_data.alm_val == alm_val)
    {
        return SUCCESSFUL;
    }

    current_data.alm_val = alm_val;
    current_data.time = get_ulong_data(&data[TIME_INDEX_IN_FRAME]);
    set_battery_alarm_data(id - 1, current_data);

    can_reply_common(data[0], RTN_NORMAL);
    can3_linklayer_send(BOTTOM_SCU_TYPE, data, data_len);

    return SUCCESSFUL;
}

Static int can_reply_common(unsigned char func_code, unsigned char rtn)
{
    link_inst_t* link_inst = s_dev_R321->dev_type->link_inst;
    unsigned char frm_data[7] = {0};
    unsigned char  addr =link_inst->r_link_addr;
    frm_data[0] = func_code;
    frm_data[6] = rtn;

    can_linklayer_send(addr,frm_data, sizeof(frm_data));
    return SUCCESSFUL;
}


