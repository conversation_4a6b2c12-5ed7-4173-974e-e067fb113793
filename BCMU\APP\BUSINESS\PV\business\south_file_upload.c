#include "south_file_upload.h"
#include "utils_rtthread_security_func.h"
#include "utils_data_transmission.h"
#include "storage.h"
#include "sps.h"
#include "msg_id.h"
#include "msg.h"
#include "cmd.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "ee_public_info.h"
#include "rtthread.h"
#include "partition_def.h"
#include "para_common.h"

south_upload_file_info_t g_south_file_info;
upload_sta_info_t g_upload_sta_info = {0};
file_list_node* g_file_name_list = NULL;
static rt_timer_t g_south_upload_timer = {0};

short start_file_upload(const char* file_name)
{
    if(g_upload_sta_info.is_trig_succ != FALSE) return FAILURE;
    unsigned char afci_sta = 0;
    set_one_data(DAC_DATA_ID_AFCI_FILE_STATUS, &afci_sta);
    rt_memset_s(&g_south_file_info, sizeof(south_upload_file_info_t), 0, sizeof(south_upload_file_info_t));
    rt_memset_s(g_upload_sta_info.file_name, UPLOAD_FILE_NAME_MAX_LEN, 0, UPLOAD_FILE_NAME_MAX_LEN);

    rt_memcpy_s(g_upload_sta_info.file_name, UPLOAD_FILE_NAME_MAX_LEN, file_name, UPLOAD_FILE_NAME_MAX_LEN);
    g_upload_sta_info.upload_next_sta = UPLOAD_TRIG;
    g_upload_sta_info.is_get_file_list = FALSE;
    send_upload_msg(g_upload_sta_info.upload_next_sta);
    return SUCCESSFUL;
}

short clean_afci_data()
{
    return storage_unlink(AFCI_FILE_PART);
}

void send_upload_msg(upload_status_t upload_sta)
{
    unsigned char msg_data;
    rt_timer_start(g_south_upload_timer);
    switch (upload_sta)
    {
        case UPLOAD_TRIG:
            msg_data = DC_AC_UPLOAD_FILE_TRIG;
            break;
        case UPLOAD_DATA:
            msg_data = DC_AC_UPLOAD_FILE_DATA;
            break;
        case UPLOAD_LIST:
            msg_data = DC_AC_UPLOAD_FILE_LIST;
            break;
        case UPLOAD_EXIT:
            finish_south_upload();
            rt_timer_stop(g_south_upload_timer);
            return;
        default:
            return;
    }
    send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, msg_data);
}

int upload_cmd_pack(void* dev_inst, void *cmd_buf) {
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*) cmd_buf;
    if(cmd_buf_temp->cmd == NULL || cmd_buf_temp->cmd->req_head == NULL)
    {
        return FAILURE;
    }

    bottom_comm_cmd_head_t* req_head = NULL;
    req_head = (bottom_comm_cmd_head_t*)(cmd_buf_temp->cmd->req_head);

    if(g_upload_sta_info.is_trig_succ == FALSE || g_upload_sta_info.is_get_file_list == FALSE)
    {
        return upload_common_frm_pack(cmd_buf_temp);
    }

    unsigned short frm_no = req_head->append;
    if(frm_no == 0)
    {
        upload_file_first_frm_pack(cmd_buf_temp);
    }
    else
    {
        upload_common_frm_pack(cmd_buf_temp);
    }
    return SUCCESSFUL;
}


int is_input_valid(dev_inst_t* dev_inst, cmd_buf_t* cmd_buf)
{
    if (dev_inst == NULL || cmd_buf == NULL) {
        return FAILURE;
    }
    // 检查命令缓冲区的有效性
    if (cmd_buf->cmd == NULL || cmd_buf->cmd->ack_head == NULL || cmd_buf->cmd->req_head == NULL) {
        return FAILURE;
    }
    bottom_comm_cmd_head_t* ack_head = (bottom_comm_cmd_head_t*)(cmd_buf->cmd->ack_head);
    // 检查确认头的有效性
    if (ack_head->src_addr < 1) {
        return FAILURE;
    }
    return SUCCESSFUL;
}



int upload_cmd_parse(void* dev_inst, void *cmd_buf) {
    // 确保输入参数有效
    if (is_input_valid((dev_inst_t*)dev_inst, (cmd_buf_t*)cmd_buf) != SUCCESSFUL) {
        return FAILURE;
    }

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    bottom_comm_cmd_head_t* ack_head = (bottom_comm_cmd_head_t*)(cmd_buf_temp->cmd->ack_head);
    bottom_comm_cmd_head_t* req_head = (bottom_comm_cmd_head_t*)(cmd_buf_temp->cmd->req_head);

    // 处理触发状态
    if (g_upload_sta_info.is_trig_succ == FALSE) {
        upload_file_trig_parse((dev_inst_t*)dev_inst, cmd_buf_temp);
        send_upload_msg(g_upload_sta_info.upload_next_sta);
        return SUCCESSFUL;
    }

    unsigned short frm_no = ack_head->append;
    // 处理文件列表获取状态
    if (g_upload_sta_info.is_get_file_list == FALSE) {
        int ret = upload_file_list_frm_parse(cmd_buf_temp, frm_no);
        if (ret == SUCCESSFUL) {
            req_head->append = frm_no + 1;  // 收到当前帧，请求下一帧
        }
        send_upload_msg(g_upload_sta_info.upload_next_sta);
        return SUCCESSFUL;
    }

    // 处理文件帧
    int ret;
    if (frm_no == 0)
    {
        ret = upload_file_first_frm_parse(cmd_buf_temp, &frm_no);
    }
    else
    {
        ret = upload_file_data_frm_parse(cmd_buf_temp, frm_no);
    }

    if (ret == SUCCESSFUL)
    {
        req_head->append = frm_no + 1;  // 收到当前帧，请求下一帧
    }
    else
    {
        finish_south_upload();
        return SUCCESSFUL;
    }

    send_upload_msg(g_upload_sta_info.upload_next_sta);
    return SUCCESSFUL;
}


int upload_common_frm_pack(cmd_buf_t* cmd_buf)
{
    RETURN_VAL_IF_FAIL(cmd_buf != NULL, FAILURE);
    cmd_buf->data_len = 0;
    return SUCCESSFUL;
}


int clear_req_append(dev_inst_t* dev_inst)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL, FAILURE);
    cmd_t* cmd_file_data = NULL;
    cmd_t* cmd_file_list = NULL;
    unsigned short cmd_index = 0;
    cmd_file_data = get_dest_cmd(dev_inst->dev_type->no_poll_tab, DC_AC_UPLOAD_FILE_DATA, &cmd_index);
    cmd_file_list = get_dest_cmd(dev_inst->dev_type->no_poll_tab, DC_AC_UPLOAD_FILE_LIST, &cmd_index);
    RETURN_VAL_IF_FAIL(cmd_file_data != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(cmd_file_list != NULL, FAILURE);
    bottom_comm_cmd_head_t* file_data_req = (bottom_comm_cmd_head_t*)(cmd_file_data->req_head);
    bottom_comm_cmd_head_t* file_list_req = (bottom_comm_cmd_head_t*)(cmd_file_list->req_head);
    file_data_req->append = 0;
    file_list_req->append = 0;
    return SUCCESSFUL;
}

int upload_file_trig_parse(dev_inst_t* dev_inst, cmd_buf_t* cmd_buf)
{
    if (cmd_buf == NULL)
    {
        return FAILURE;
    }

    if(cmd_buf->rtn != 0)
    {
        g_upload_sta_info.upload_trig_times = 0;
        g_upload_sta_info.upload_next_sta = UPLOAD_EXIT;
        return FAILURE;
    }
    g_upload_sta_info.upload_trig_times++;

    if(g_upload_sta_info.upload_trig_times >= 3)
    {
        clear_req_append(dev_inst);
        g_upload_sta_info.upload_trig_times = 0;
        g_upload_sta_info.is_trig_succ = TRUE;
        g_upload_sta_info.upload_next_sta = g_upload_sta_info.is_get_file_list == TRUE ? UPLOAD_DATA : UPLOAD_LIST;
    }

    return SUCCESSFUL;
}

int upload_file_first_frm_pack(cmd_buf_t* cmd_buf)
{
    RETURN_VAL_IF_FAIL(cmd_buf != NULL, FAILURE);
    unsigned short offset = 0;
    rt_memset_s(cmd_buf->buf, cmd_buf->buflen, 0, cmd_buf->buflen);
    rt_snprintf_s((char*)&cmd_buf->buf[offset], sizeof(g_upload_sta_info.file_name), "%s", g_upload_sta_info.file_name);
    offset += sizeof(g_upload_sta_info.file_name);
    cmd_buf->data_len = offset;
    return SUCCESSFUL;
}

int upload_file_first_frm_parse(cmd_buf_t* cmd_buf, unsigned short* frm_no)
{
    // 解析首发帧
    if (cmd_buf == NULL|| cmd_buf->buf == NULL)
    {
        return FAILURE;
    }
    if(cmd_buf->rtn != 0)
    {
        return FAILURE;
    }
    if(cmd_buf->data_len != UPLOAD_FIRST_FRM_DATA_LEN)
    {
        g_upload_sta_info.upload_next_sta = UPLOAD_EXIT;
        LOG_E("%s:%d|upload_first_frame data_len:%d", __FUNCTION__ , __LINE__, cmd_buf->data_len);
        return FAILURE;
    }
    char file_name[UPLOAD_FILE_NAME_MAX_LEN + 5] = {0};
    g_south_file_info.upload_frame_size = get_int16_data(&cmd_buf->buf[0]);
    g_south_file_info.file_total_frame = get_int16_data(&cmd_buf->buf[2]);
    g_south_file_info.file_total_len = get_ulong_data(&cmd_buf->buf[4]);
    rt_memcpy_s(g_south_file_info.file_name, UPLOAD_FILE_NAME_MAX_LEN, &cmd_buf->buf[8], UPLOAD_FILE_NAME_MAX_LEN);
    rt_memcpy_s(g_south_file_info.file_time, UPLOAD_FILE_TIME_LEN, &cmd_buf->buf[48], UPLOAD_FILE_TIME_LEN);
    g_south_file_info.file_crc = get_int16_data(&cmd_buf->buf[68]);
    // 判断是否断点续传
    if(is_resume_trans() == TRUE)
    {
        *frm_no = g_south_file_info.cur_frame_no;
        LOG_E("%s:%d|is_resume_trans is TRUE", __FUNCTION__, __LINE__);
        return SUCCESSFUL;
    }
    rt_snprintf_s(file_name, UPLOAD_FILE_NAME_MAX_LEN, "/nor/%s", g_upload_sta_info.file_name);
    storage_unlink(file_name);
    return SUCCESSFUL;
}
char is_resume_trans()
{
    unsigned short crc = 0;
    struct stat file_stat = {0};
    char file_name_tmp[UPLOAD_FILE_NAME_MAX_LEN] = {0};
    int ret = 0;
    rt_snprintf_s(file_name_tmp, UPLOAD_FILE_NAME_MAX_LEN, "/nor/%s", g_south_file_info.file_name);    // 读取上传文件信息
    ret = storage_stat(file_name_tmp, &file_stat);
    RETURN_VAL_IF_FAIL(ret >= 0, FALSE);
    RETURN_VAL_IF_FAIL(file_stat.st_size > 0, FALSE);
    south_upload_file_info_t upload_info_temp = {0};
    handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char*)&upload_info_temp, sizeof(south_upload_file_info_t), UPLOAD_INFO_OFFSET);
    crc = crc_cal((unsigned char *)&upload_info_temp, sizeof(south_upload_file_info_t)-2);
    if(crc != upload_info_temp.crc)
    {
        return FALSE;
    }
    if ((upload_info_temp.file_crc == g_south_file_info.file_crc) && \
        (strcmp(upload_info_temp.file_name, g_south_file_info.file_name) == 0) && \
        (strcmp(upload_info_temp.file_time, g_south_file_info.file_time) == 0) && \
        (upload_info_temp.file_total_frame == g_south_file_info.file_total_frame) && \
        (upload_info_temp.file_total_len   == g_south_file_info.file_total_len))
    {
        rt_memcpy_s(&g_south_file_info, sizeof(south_upload_file_info_t), &upload_info_temp, sizeof(south_upload_file_info_t));
        return TRUE;
    }

    return FALSE;
}

int write_upload_tempinfo()
{
    south_upload_file_info_t temp_south_file_info = {0};
    g_south_file_info.crc = crc_cal((unsigned char *)&g_south_file_info, sizeof(south_upload_file_info_t)-2);
    rt_memcpy_s(&temp_south_file_info, sizeof(south_upload_file_info_t), &g_south_file_info, sizeof(south_upload_file_info_t));
    return handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&temp_south_file_info, sizeof(south_upload_file_info_t), UPLOAD_INFO_OFFSET);
}

int upload_file_data_frm_parse(cmd_buf_t* cmd_buf, unsigned short frm_no)
{
    unsigned short crc = g_south_file_info.u_crc;
    part_data_t part_data = {0};
    if(cmd_buf->rtn != 0)
    {
        return FAILURE;
    }

    rt_snprintf_s(part_data.name, sizeof(part_data.name), "/nor/%s", g_south_file_info.file_name);
    part_data.buff = cmd_buf->buf;
    part_data.len = cmd_buf->data_len;
    part_data.offset = g_south_file_info.data_offset;
    if (SUCCESSFUL != storage_process(&part_data, write_opr))
    {
        g_upload_sta_info.upload_next_sta = UPLOAD_EXIT;
        return FAILURE;
    }

    // 计算crc
    crc = crc16_calc_with_init_crc(cmd_buf->buf, cmd_buf->data_len, crc);

    // 更新数据
    g_south_file_info.cur_frame_no = frm_no;
    g_south_file_info.data_offset += cmd_buf->data_len;
    g_south_file_info.u_crc = crc;

    if(frm_no == g_south_file_info.file_total_frame - 1)
    {
        // 处理最后一帧
        return upload_file_last_frm_deal();
    }
    else if(frm_no %  SAVE_UPDATE_DATA_INDEX == 0)
    {
        // 断点续传，每10帧存储一次
        return write_upload_tempinfo();
    }

    return SUCCESSFUL;
}



int upload_file_list_frm_parse(cmd_buf_t* cmd_buf, unsigned short frm_no)
{
    static unsigned short file_list_total_frm = 0;
    rt_sem_t rtn_sem = get_rtn_sem();
    // 确保输入参数有效
    if(cmd_buf == NULL || cmd_buf->buf == NULL)
    {
        return FAILURE;
    }

    // 处理帧号为0的情况
    if (frm_no == 0)
    {
        file_list_total_frm = get_uint16_data(cmd_buf->buf);
    }
    else
    {
        // 处理其他帧号的情况
        unsigned int offset = 0;
        char filename[UPLOAD_FILE_NAME_MAX_LEN] = {0};
        unsigned short south_file_num = cmd_buf->buf[0];

        if (south_file_num > 0) {
            for (unsigned short i = 0; i < south_file_num; ++i) {
                rt_memcpy_s(filename, UPLOAD_FILE_NAME_MAX_LEN, &cmd_buf->buf[1 + offset], UPLOAD_FILE_NAME_MAX_LEN);
                add_filename_to_list(&g_file_name_list, filename);
                offset += UPLOAD_FILE_NAME_MAX_LEN;
            }
        }
    }

    // 检查是否是最后一帧
    if (frm_no == file_list_total_frm - 1) {
        g_upload_sta_info.upload_trig_times = 0;
        g_upload_sta_info.is_trig_succ = FALSE;
        g_upload_sta_info.upload_next_sta = UPLOAD_TRIG;
        g_upload_sta_info.is_get_file_list = TRUE;

        if (is_file_name_in_list(g_upload_sta_info.file_name, g_file_name_list) != TRUE)
        {
            LOG_E("%s:%d|file_name not in list", __FUNCTION__, __LINE__);
            set_rtn_south_to_north_flag(FALSE);
            rt_sem_release(rtn_sem);
            finish_south_upload();
        }
        else
        {
            LOG_E("%s:%d|file_name: %s in list", __FUNCTION__, __LINE__, g_upload_sta_info.file_name);
            set_rtn_south_to_north_flag(TRUE);
            rt_sem_release(rtn_sem);
        }
    }
    return SUCCESSFUL;
}


int upload_file_last_frm_deal()
{
    int ret = 0;
    finish_south_upload();
    unsigned char afci_sta = 1;
    set_one_data(DAC_DATA_ID_AFCI_FILE_STATUS, &afci_sta);
    south_upload_file_info_t temp_south_file_info = {0};
    rt_memset_s(&temp_south_file_info, sizeof(south_upload_file_info_t), 0, sizeof(south_upload_file_info_t));
    ret =  handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&temp_south_file_info, sizeof(south_upload_file_info_t), UPLOAD_INFO_OFFSET);
    if(ret != SUCCESSFUL)
    {
        LOG_E("%s:%d|write upload info failed", __FUNCTION__ , __LINE__);
        return FAILURE;
    }
    else
    {
        LOG_E("%s:%d|upload successful", __FUNCTION__ , __LINE__);
        return SUCCESSFUL;
    }
}

void finish_south_upload()
{
    g_upload_sta_info.upload_trig_times = 0;
    g_upload_sta_info.is_trig_succ = FALSE;
    g_upload_sta_info.is_get_file_list = FALSE;
    g_upload_sta_info.upload_next_sta = UPLOAD_EXIT;
    rt_memset_s(g_upload_sta_info.file_name, UPLOAD_FILE_NAME_MAX_LEN, 0, UPLOAD_FILE_NAME_MAX_LEN);
    if(g_file_name_list != NULL)
    {
        free_list(g_file_name_list);
        g_file_name_list = NULL;
    }
    rt_timer_stop(g_south_upload_timer);
}

void upload_timeout()
{
    finish_south_upload();
    LOG_E("%s:%d|upload failed", __FUNCTION__ , __LINE__);
}


// 创建新节点
file_list_node* creat_file_list_node(const char* filename)
{
    file_list_node* new_node = (file_list_node*)rt_malloc(sizeof(file_list_node));
    if (!new_node)
    {
        LOG_E("%s:%d|Memory allocation failed", __FUNCTION__ , __LINE__);
        return NULL;
    }
    rt_strncpy_s(new_node->filename, UPLOAD_FILE_NAME_MAX_LEN, filename, UPLOAD_FILE_NAME_MAX_LEN - 1);
    new_node->next = NULL;
    return new_node;
}



// 向链表中添加文件名
void add_filename_to_list(file_list_node** head, const char* filename)
{
    file_list_node* new_node = creat_file_list_node(filename);
    if (!new_node) {
        return;
    }

    if (*head == NULL) {
        *head = new_node;
    } else {
        file_list_node* current = *head;
        while (current->next != NULL) {
            current = current->next;
        }
        current->next = new_node;
    }
}



// 清理链表，释放内存
void free_list(file_list_node* head) {
    file_list_node* current = head;
    file_list_node* next;
    while (current != NULL) {
        next = current->next;
        rt_free(current);
        current = next;
    }
}



char is_file_name_in_list(const char* filename, file_list_node* file_name_list)
{
    if (filename == NULL || file_name_list == NULL) {
        return FALSE;
    }

    file_list_node* current = file_name_list;
    while (current != NULL) {
        if (strcmp(current->filename, filename) == 0) {
            return TRUE;
        }
        current = current->next;
    }
    return FALSE;
}


int init_south_upload()
{
    g_south_upload_timer = rt_timer_create("south_upload", upload_timeout, NULL, 30*1000, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
    if(g_south_upload_timer == NULL)
    {
        return FAILURE;
    }
    return SUCCESSFUL;
}