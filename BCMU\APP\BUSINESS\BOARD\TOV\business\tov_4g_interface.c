#include <rtdef.h>
#include "drv_at.h"
#include <rtthread.h>
#include "at_device.h"
#include "tov_4g_interface.h"
#include "utils_math.h"
#include "utils_rtthread_security_func.h"
#include "para_id_in.h"
#include "para_manage.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "partition_def.h"
#include "data_type.h"


int is_4g_info_effective()
{
    struct at_device_info_4g* net4g_device_info = get_at_device_info_4g_t();

    // 检查每个字段是否为空
    if (rt_strnlen_s(net4g_device_info->dev_model, 20) == 0) return FALSE;
    if (rt_strnlen_s(net4g_device_info->ip_addr, 20) == 0) return FALSE;
    if (rt_strnlen_s(net4g_device_info->dev_num, 20) == 0) return FALSE;
    if (rt_strnlen_s(net4g_device_info->dev_soft_ver, 20) == 0) return FALSE;

    return TRUE;
}





int set_4g_info(short isClear)
{
    if (isClear)
    {
        set_4g_info_invalid();
    }

    struct at_device_info_4g* net4g_device_info = get_at_device_info_4g_t();
    if (net4g_device_info == RT_NULL)
    {
        // 处理无效的设备信息
        return FAILURE;
    }

    // 批量设置数据
    set_one_data(TOV_DATA_ID_4G_COMMUNICATION_ROD_MODEL, net4g_device_info->dev_model);
    set_one_data(TOV_DATA_ID_4G_COMMUNICATION_ROD_SERIAL_NUMBER, net4g_device_info->dev_num);
    set_one_data(TOV_DATA_ID_4G_COMMUNICATION_ROD_SOFT_VER, net4g_device_info->dev_soft_ver);
    set_one_data(TOV_DATA_ID_4G_COMMUNICATION_ROD_ICCID_NUMBER, net4g_device_info->iccid);
    set_one_data(TOV_DATA_ID_4G_OPERATOR_NAME, net4g_device_info->operator_name);
    set_one_data(TOV_DATA_ID_4G_SIGNAL_STRENGTH, &(net4g_device_info->signal_power));
    set_one_data(TOV_DATA_ID_WIRELESS_IP_ADDR, net4g_device_info->ip_addr);
    set_one_data(TOV_DATA_ID_SIM_EXIST, &(net4g_device_info->sim_status));

    return SUCCESSFUL;
}



int judge_4G_ip(rt_device_t dev)
{
    static int last_ip_flag = FALSE;
    static int ip_err_count = 0;
    int curr_ip_flag;

    // 获取设备信息一次
    get_4g_dev_info(dev, AT_DEVICE_CTRL_GET_IP_ADDR);
    struct at_device_info_4g* net4g_device_info = get_at_device_info_4g_t();

    // 检查IP地址的有效性
    curr_ip_flag = is_valid_ip(net4g_device_info->ip_addr);

    if (curr_ip_flag == last_ip_flag)
    {
        return TRUE;
    }

    if (curr_ip_flag == FALSE)
    {
        if (ip_err_count >= MAX_IP_ERR_COUNT)
        {
            last_ip_flag = curr_ip_flag;
            LOG_E("%s:%d|IP change abnormal lost and reset 4G", __FUNCTION__, __LINE__);
            set_dev_fault_handle(TRUE);
            return FALSE;
        }
        ip_err_count++;
    }
    else
    {
        last_ip_flag = curr_ip_flag;
        ip_err_count = 0;
    }

    return TRUE;
}



short model_4g_offline_handle(rt_device_t dev)
{
    // 1.模组离线清除模组缓存信息
    set_4g_info(TRUE);
    char model_sta = MODEL_REMOVE;
    set_one_data(TOV_DATA_ID_4G_EXIST, &model_sta);
    return SUCCESSFUL;
}



short judge_simcard_status(rt_device_t dev, unsigned short curr_sim_status)
{
    static unsigned short last_sim_status = SIM_CARD_NOT_INSERT;
    static int no_sim_cnt = 0;
    if(curr_sim_status == SIM_CARD_INSERT)
    {
        // 异常ip检测
        judge_4G_ip(dev);
    }
    if(curr_sim_status == last_sim_status) 
    {
        if(curr_sim_status == SIM_CARD_NOT_INSERT)
        {
            return FALSE;
        }
        return TRUE;
    }

    if(curr_sim_status == SIM_CARD_NOT_INSERT)
    {
        if(no_sim_cnt > MAX_SIM_NO_COUNT)
        {
            last_sim_status = curr_sim_status; 
            no_sim_cnt = 0;
            // sim卡从有到无且连续三次未检测到sim卡
            set_dev_fault_handle(TRUE);
            LOG_E("%s:%d|sim card lost and reset 4G", __FUNCTION__ , __LINE__);
        }
        no_sim_cnt++;
        return FALSE;
    }
    else
    {
        LOG_E("%s:%d|sim card inserted", __FUNCTION__ , __LINE__);
        last_sim_status = curr_sim_status; 
    }
    return TRUE;
}



short check_sim_status_handle(rt_device_t dev)
{
    static int s_get_4g_info_cnt = GET_4G_INFO_INIT_TIME;
    unsigned short sim_status = 0;
    short ret = FALSE;

    // 模组相关信息获取指令
    static const int model_cmd_list[] = {
        AT_DEVICE_CTRL_GET_IP_ADDR,
        AT_DEVICE_CTRL_GET_DEV_MODEL,
        AT_DEVICE_CTRL_GET_DEV_NUM,
        AT_DEVICE_CTRL_GET_DEV_SOFT_VER
    };

    // sim卡相关信息获取指令
    static const int sim_cmd_list[] = {
        AT_DEVICE_CTRL_GET_ICCID,
        AT_DEVICE_CTRL_GET_OPERATOR_NAME,
        AT_DEVICE_CTRL_GET_SIGNAL_POWER
    };

    if (is_4g_info_effective() == FALSE)
    {
        for (int i = 0; i < sizeof(model_cmd_list)/sizeof(int); i++)
        {
            get_4g_dev_info(dev, model_cmd_list[i]);
        }
        
        set_4g_info(FALSE);
        
    }

    if ((s_get_4g_info_cnt % NET_4G_INTERVAL) == 0)
    {
        sim_status = get_4g_sim_status(dev);
        ret = judge_simcard_status(dev, sim_status);
        if (sim_status)
        {
            for (int i = 0; i < sizeof(sim_cmd_list)/sizeof(int); i++)
            {
                get_4g_dev_info(dev, sim_cmd_list[i]);
            }
            
            set_4g_info(FALSE);
            
        }
        else
        {
            // sim卡丢失，将sim卡相关信息置为无效
            struct at_device_info_4g net4g_device_info = {0};
            
            rt_memset_s(&net4g_device_info, sizeof(struct at_device_info_4g), 0, sizeof(struct at_device_info_4g));
            
            net4g_device_info.signal_power = INT16U_INVALID;
            set_one_data(TOV_DATA_ID_4G_COMMUNICATION_ROD_ICCID_NUMBER, net4g_device_info.iccid);
            set_one_data(TOV_DATA_ID_4G_OPERATOR_NAME, net4g_device_info.operator_name);
            set_one_data(TOV_DATA_ID_4G_SIGNAL_STRENGTH, &(net4g_device_info.signal_power));
            set_one_data(TOV_DATA_ID_WIRELESS_IP_ADDR, net4g_device_info.ip_addr);
            set_one_data(TOV_DATA_ID_SIM_EXIST, &(net4g_device_info.sim_status));
        }
    }

    s_get_4g_info_cnt = (s_get_4g_info_cnt + 1) % (NET_4G_INTERVAL + 1);
    return ret;
}



short product_special_handle()
{
    char model_sta = MODEL_INSERT;
    set_one_data(TOV_DATA_ID_4G_EXIST, &model_sta);
    return SUCCESSFUL;
}


int register_4g_func()
{
    net_4g_func_t* net_4g_func = get_4g_func_t();
    if (net_4g_func == RT_NULL)
    {
        // 处理无效的设备信息
        return FAILURE; // 或者其他错误码
    }

    net_4g_func->ptr_no4g_model_handle = model_4g_offline_handle;
    net_4g_func->ptr_check_sim_status_handle = check_sim_status_handle;
    net_4g_func->ptr_product_special_handle = product_special_handle;

    return SUCCESSFUL;
}

