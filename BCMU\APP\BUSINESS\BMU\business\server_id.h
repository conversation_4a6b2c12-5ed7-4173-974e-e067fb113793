
#ifndef _SERVER_ID_H_
#define _SERVER_ID_H_
#ifdef __cplusplus
extern "C" {
#endif

#include "utils_server.h"

#define CSU_SERVER_NUM 4

#define SAMPLE_SERVER_ID            DATA_SERVER + 1
#define NORTH_SERVER_ID             NORTH_SERVER + 1
#define NORTH_SAVE_SERVER_ID        NORTH_SERVER + 2
#define LED_SERVER_ID               CONTROL_SERVER + 1
#define FILE_SAVE_ID                SAVE_SERVER + 1
#define ALARM_SERVER_ID             ALARM_SERVER + 1
#define SOUTH_SERVER_ID             SOUTH_SERVER + 1


#ifdef __cplusplus
}
#endif

#endif  // _SERVER_ID_H_
