/**************************************************************************
* Copyright (C) 2010-2011, ZTE Corporation.
* 版权信息：（C）2004-2005，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：ZXDU68 PSU板软件
***************************************************************************/
#include "common.h"
#include "interface.h"

void system_reset(void)
{
    rt_kprintf("system soft reset");
	 NVIC_SystemReset();
}
void ShutDownDevice(uint8_t  bOnoff)
{
    rt_pin_write(SW_B1_Pin, bOnoff);
}

// static void Timer_Config(void)
// {
//         rcu_periph_clock_enable(RCU_GPIOE);
    
//     /*Configure PB10(TIMER1_CH2) as alternate function*/
//     gpio_mode_set(GPIOE, GPIO_MODE_AF, GPIO_PUPD_NONE, GPIO_PIN_10);
//     gpio_output_options_set(GPIOE, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,GPIO_PIN_10);

//     gpio_af_set(GPIOE, GPIO_AF_1, GPIO_PIN_10);
    
//  /* TIMER1 configuration: generate PWM signals with different duty cycles:
//        TIMER1CLK = SystemCoreClock / 120  */
//     timer_oc_parameter_struct timer_ocintpara;
//     timer_parameter_struct timer_initpara;

//     rcu_periph_clock_enable(RCU_TIMER0);
//     rcu_timer_clock_prescaler_config(RCU_TIMER_PSC_MUL2);
//     timer_struct_para_init(&timer_initpara);
//     timer_deinit(TIMER0);

//     /* TIMER1 configuration */
//     timer_initpara.prescaler         = 119;
//     timer_initpara.alignedmode       = TIMER_COUNTER_EDGE;
//     timer_initpara.counterdirection  = TIMER_COUNTER_UP;
//     timer_initpara.period            = 0;
//     timer_initpara.clockdivision     = TIMER_CKDIV_DIV1;
//     timer_initpara.repetitioncounter = 0;
//     timer_init(TIMER0,&timer_initpara);

//     /* CH2 configuration in PWM mode 0 */
//     timer_channel_output_struct_para_init(&timer_ocintpara);
//     timer_ocintpara.ocpolarity   = TIMER_OC_POLARITY_HIGH;
//     timer_ocintpara.outputstate  = TIMER_CCX_DISABLE;
//     timer_ocintpara.ocnpolarity  = TIMER_OCN_POLARITY_HIGH;
//     timer_ocintpara.outputnstate = TIMER_CCXN_ENABLE;
//     timer_ocintpara.ocidlestate  = TIMER_OC_IDLE_STATE_LOW;
//     timer_ocintpara.ocnidlestate = TIMER_OCN_IDLE_STATE_HIGH;

//     timer_channel_output_config(TIMER0,TIMER_CH_1,&timer_ocintpara);

//     /* CH2 configuration in PWM mode 0,duty cycle 25% */


//     timer_channel_output_pulse_value_config(TIMER0,TIMER_CH_1,250);
//     timer_channel_output_mode_config(TIMER0,TIMER_CH_1,TIMER_OC_MODE_PWM0);
//     timer_channel_output_shadow_config(TIMER0,TIMER_CH_1,TIMER_OC_SHADOW_DISABLE);

//     /* auto-reload preload enable */
//     timer_auto_reload_shadow_enable(TIMER0);
//     /* TIMER1 enable */
//     timer_primary_output_config(TIMER0, ENABLE);
//     timer_enable(TIMER0);
// }

uint32_t adc_value[9];
#define ADC_CHANNLE0 9
#define ADC_CHANNLE1 9
#define ADC_CHANNLE2 14
#define ADC_CHANNLE3 15
#define ADC_CHANNLE4 12
#define ADC_CHANNLE5 13
#define ADC_CHANNLE6 0
#define ADC_CHANNLE7 3
#define ADC_CHANNLE8 8
void rcu_config(void)
{
    /* enable DMA clock */
    rcu_periph_clock_enable(RCU_DMA1);

}
void dma_config(void)
{
    /* ADC_DMA_channel configuration */
    dma_single_data_parameter_struct dma_single_data_parameter;
    
    /* ADC DMA_channel deinit */
    dma_deinit(DMA1,DMA_CH0);
    
    /* initialize DMA single data mode */
    dma_single_data_parameter.periph_addr = (uint32_t)(&ADC_SYNCDATA);
    dma_single_data_parameter.periph_inc = DMA_PERIPH_INCREASE_DISABLE;
    dma_single_data_parameter.memory0_addr = (uint32_t)(adc_value);
    dma_single_data_parameter.memory_inc = DMA_MEMORY_INCREASE_ENABLE;
    dma_single_data_parameter.periph_memory_width = DMA_PERIPH_WIDTH_32BIT;
    dma_single_data_parameter.circular_mode = DMA_CIRCULAR_MODE_ENABLE;
    dma_single_data_parameter.direction = DMA_PERIPH_TO_MEMORY;
    dma_single_data_parameter.number = 9;
    dma_single_data_parameter.priority = DMA_PRIORITY_HIGH;
    dma_single_data_mode_init(DMA1,DMA_CH0, &dma_single_data_parameter);
    /* DMA channel 0 peripheral select */
    dma_channel_subperipheral_select(DMA1,DMA_CH0,DMA_SUBPERI0);

    /* enable DMA channel */
    dma_channel_enable(DMA1,DMA_CH0);
}
void adc_config(void)
{
    /* ADC channel length config */
    adc_channel_length_config(ADC1,ADC_REGULAR_CHANNEL,2);
    adc_channel_length_config(ADC2,ADC_REGULAR_CHANNEL,2);
    /* ADC regular channel config */
    adc_regular_channel_config(ADC1,0,ADC_CHANNLE0,ADC_SAMPLETIME_144);
    adc_regular_channel_config(ADC2,1,ADC_CHANNLE1,ADC_SAMPLETIME_144);
    adc_regular_channel_config(ADC2,2,ADC_CHANNLE2,ADC_SAMPLETIME_144);
    adc_regular_channel_config(ADC2,3,ADC_CHANNLE3,ADC_SAMPLETIME_144);
    adc_regular_channel_config(ADC1,4,ADC_CHANNLE4,ADC_SAMPLETIME_144);
    adc_regular_channel_config(ADC1,5,ADC_CHANNLE5,ADC_SAMPLETIME_144);
    adc_regular_channel_config(ADC1,6,ADC_CHANNLE6,ADC_SAMPLETIME_144);
    adc_regular_channel_config(ADC1,7,ADC_CHANNLE7,ADC_SAMPLETIME_144);
    adc_regular_channel_config(ADC1,8,ADC_CHANNLE8,ADC_SAMPLETIME_144);
    /* ADC external trigger enable */
    adc_external_trigger_config(ADC1,ADC_REGULAR_CHANNEL,EXTERNAL_TRIGGER_DISABLE);
    adc_external_trigger_config(ADC2,ADC_REGULAR_CHANNEL,EXTERNAL_TRIGGER_DISABLE);
//    adc_external_trigger_source_config(ADC0,ADC_REGULAR_CHANNEL,ADC_EXTTRIG_REGULAR_T1_CH1);
    
    /* ADC data alignment config */
    adc_data_alignment_config(ADC1,ADC_DATAALIGN_RIGHT);
    adc_data_alignment_config(ADC2,ADC_DATAALIGN_RIGHT);
    /* configure the ADC sync mode */
    adc_sync_mode_config(ADC_DAUL_REGULAL_PARALLEL);
    adc_sync_dma_config(ADC_SYNC_DMA_MODE1);
    adc_sync_dma_request_after_last_enable();
    /* ADC scan mode function enable */
    adc_special_function_config(ADC1,ADC_SCAN_MODE,ENABLE);
    adc_special_function_config(ADC2,ADC_SCAN_MODE,ENABLE);
    
    /* enable ADC interface */
    adc_enable(ADC1);
    /* ADC calibration and reset calibration */
    adc_calibration_enable(ADC1);
    /* enable ADC interface */
    adc_enable(ADC2);
    /* ADC calibration and reset calibration */
    adc_calibration_enable(ADC2);
}
static int Timer_Init(void)
{
    GPIO_Init();
    //Timer_Config();
    //adc_config();
    //dma_config();
    //PWM_Config();

    return 0;
}
INIT_BOARD_EXPORT(Timer_Init);

static int adc_test(int argc, char *argv[])
{
    return 0;
    
}
MSH_CMD_EXPORT(adc_test, adc voltage convert sample);