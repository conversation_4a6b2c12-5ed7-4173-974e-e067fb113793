#ifndef DATALOAD_PRODUCT_H_
#define DATALOAD_PRODUCT_H_

#include "common.h"
#include "sample.h"

BOOLEAN GetChgPrtAlmExist(void);
BOOLEAN GetDischgPrtAlmExist(void);
BOOLEAN GetChgPrtAlmExistOnlyChgTempLow(void);
Bool IsDischargeForbidden(T_BCMAlarmStruct const* ptBcmAlarm, T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn);
BOOLEAN IsActivatePortUVP(T_BCMAlarmStruct const *ptBcmAlarm, T_HardwareParaStruct *pHardwarePara);
BOOLEAN IsExistForbiddenDischargeTemperature(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn);
void QtpBattDataOverWrite(T_BattInfo *pBattIn, T_BCMDataStruct *pBcmData, T_SysPara *pSysPara);
void LoadBattSyspara(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal, T_SysPara *pSyspara, T_BCMDataStruct *pBcmData);
BOOLEAN IsExistForbiddenAlarm(T_AlarmConfigStruct *ptAlarmConfig, T_BCMAlarmStruct const *ptBcmAlarm, BYTE num, WORD *pwAlmIndex);
BOOLEAN IsExistSpecialChgPrt(T_AlarmConfigStruct *ptAlarmConfigCanSupply, T_AlarmConfigStruct *ptAlarmConfig, BYTE num, WORD wAlmIndex);
#endif