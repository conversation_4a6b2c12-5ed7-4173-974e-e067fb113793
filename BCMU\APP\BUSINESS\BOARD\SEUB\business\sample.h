/**************************************************************************
* Copyright (C) 2001, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：E260 SEUB板软件
* 文件名称：sample.h
* 文件说明：数据采集模块头文件
* 作    者：hlb
* 版本信息：V1.0
* 设计日期：2024-07-22
* 修改记录：
* 日    期      版  本      修改人      修改摘要
* 其他说明：
***************************************************************************/
#ifndef E260_SEUB_MAIN_SAMPLE_H_
#define E260_SEUB_MAIN_SAMPLE_H_

#ifdef __cplusplus
extern "C" {
#endif

/***********************  常量定义  ************************/
#define ADCCONVERTEDVALUES_SAMPLING_NUM    7   //ADC采样次数

#define DIGITAL_COUNT         12
#define ANALOG_COUNT          32
#define REFER_VOLTAGE         3     /* VOLTAGE  is 3V */
#define DO_COUNT              8
#define DAC_COUNT             2

//引脚
#define PIN_A0_S1    GET_PIN(G, 0)
#define PIN_A1_S1    GET_PIN(G, 1)
#define PIN_A2_S1    GET_PIN(G, 2)

#define PIN_DI1_IN      GET_PIN(E,9)
#define PIN_DI2_IN      GET_PIN(E,10)
#define PIN_DI3_IN      GET_PIN(E,15)
#define PIN_DI4_IN      GET_PIN(F,2)
#define PIN_DI5_IN      GET_PIN(F,11)
#define PIN_DI6_IN      GET_PIN(F,12)
#define PIN_DI7_IN      GET_PIN(F,13)
#define PIN_DI8_IN      GET_PIN(F,14)
#define PIN_DI9_IN      GET_PIN(G,4)
#define PIN_DI10_IN     GET_PIN(G,5)
#define PIN_DI11_IN     GET_PIN(G,6)
#define PIN_DI12_IN     GET_PIN(G,7)

#define PIN_DO1_OUT      GET_PIN(D,10)
#define PIN_DO2_OUT      GET_PIN(D,14)
#define PIN_DO3_OUT      GET_PIN(D,15)
#define PIN_DO4_OUT      GET_PIN(E,2)
#define PIN_DO5_OUT      GET_PIN(E,3)
#define PIN_DO6_OUT      GET_PIN(E,4)
#define PIN_DO7_OUT      GET_PIN(E,5)
#define PIN_DO8_OUT      GET_PIN(E,6)

/*********************  数据结构定义  **********************/

typedef enum
{
    AI_0_0 = 0,
    AI_0_1,
    AI_0_2,
    AI_0_3,
    AI_0_4,
    AI_0_5,
    AI_0_6,
    AI_0_7,
    AI_1_0,
    AI_1_1,
    AI_1_2,
    AI_1_3,
    AI_1_4,
    AI_1_5,
    AI_1_6,
    AI_1_7,
    AI_2_0,
    AI_2_1,
    AI_2_2,
    AI_2_3,
    AI_2_4,
    AI_2_5,
    AI_2_6,
    AI_2_7,
    AI_3_0,
    AI_3_1,
    AI_3_2,
    AI_3_3,
    AI_3_4,
    AI_3_5,
    AI_3_6,
    AI_3_7,
    AI_END,
}ADC_SAMPLE_CHANNEL;

typedef enum
{
    DI1_IN = 0,
    DI2_IN,
    DI3_IN,
    DI4_IN,
    DI5_IN,
    DI6_IN,
    DI7_IN,
    DI8_IN,
    DI9_IN,
    DI10_IN,
    DI11_IN,
    DI12_IN,
    DI_END,
} eDI_SAMPLE;

typedef enum
{
    DO1_OUT = 0,
    DO2_OUT,
    DO3_OUT,
    DO4_OUT,
    DO5_OUT,
    DO6_OUT,
    DO7_OUT,
    DO8_OUT,
    DO_END,
}eDO_OUT;

typedef enum
{
    DAC_1 = 0,
    DAC_2,
    DAC_END,
}eDAC_OUT_CHANNEL;

typedef struct
{
    ADC_SAMPLE_CHANNEL    sample_channel;
    int                   select_status;
    int                   adc_channel;
    char*                 adc_device;
}ADC_CHANNEL_INFO_STRUCT;

typedef struct
{
    eDI_SAMPLE    sample_channel;
    int           di_pin;
}DI_CHANNEL_INFO_STRUCT;

typedef struct
{
    eDO_OUT       do_channel;
    int           do_pin;
}DO_CHANNEL_INFO_STRUCT;

typedef struct
{
    eDAC_OUT_CHANNEL      set_channel;
    int                   dac_channel;
    char*                 dac_device;
    rt_uint32_t           cur_value;
}DAC_CHANNEL_INFO_STRUCT;

/*********************  函数原型定义  **********************/
void *sample_init_sys(void * param);
void sample_main(void * parameter);
unsigned short get_analog_data(unsigned short *data, unsigned short data_size);
unsigned short get_digital_data(int *data, unsigned short data_size);
void setDoValue(eDO_OUT do_index, int value);
int getDoValue(eDO_OUT do_index);
void setDacValue(eDAC_OUT_CHANNEL dac_index, rt_uint32_t value);
rt_uint32_t getDacValue(eDAC_OUT_CHANNEL dac_index);

#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // E260_SEUB_MAIN_SAMPLE_H_;
