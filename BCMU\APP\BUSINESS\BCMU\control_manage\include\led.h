#ifndef _BCMU_LED_H
#define _BCMU_LED_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

#include "alarm_mgr_api.h"
#include "battery_management.h"
#include "msg.h"
#include "signal_led.h"


// 定义灯的枚举类型
typedef enum {
    GREEN1, // 绿灯1
    GREEN2, // 绿灯2
    YELLOW, // 黄灯
    RED // 红灯
} bcmu_light_t;

// 定义闪烁状态的枚举类型
typedef enum {
    PERIODIC_FLASH, // 周期闪
    CONSTANTLY_ON, // 常亮
    FAST_FLASH, // 快闪
    SLOW_FLASH, // 慢闪
    INTERRUPTED_FLASH, // 间断闪
    SUPER_FAST_FLASH, // 超快闪
    CONSTANTLY_OFF // 常灭
} bcmu_blinking_t;



typedef struct {
    module_id_e module_id;
    int status;
    bcmu_light_t light;
    bcmu_blinking_t blinking;
} bcmu_led_status_t;


typedef struct {
    module_id_e module_id;
    int status;
} led_ctrl_msg_t;

void* init_led_blink(void* param);
void led_thread_entry(void* param);


int set_light_and_blinking(module_id_e module_id, int status);


#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _BCMU_LED_H
