/**************************************************************************
* Copyright (C) 2001, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：E260 DXCB板软件
* 文件名称：do_ctrl.c
* 文件说明：输出控制模块
* 作    者：hlb
* 版本信息：V1.0
* 设计日期：2023-09-18
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/
#include "eev_ctrl.h"
#include <rtthread.h>
#include "utils_thread.h"
#include "data_type.h"
#include "drv_pca9535.h"
#include "realdata_save.h"
#include "realdata_id_in.h"
#include "dev_north_dxcb_modbus.h"

#define INDEXCHECK_F(num) (num == 9 ? num = 1 : num)
#define INDEXCHECK_R(num) (num == 0 ? num = 8 : num)

static eev_ctrl_info_t s_evStat[8];
static unsigned char ctrolValue[9] = {0x0,0x1,0x3,0x2,0x6,0x4,0xc,0x8,0x9};
unsigned char s_is_init_status[EV_END] = {INIT_MAX_OPEN_STATUS};     // 0:开阀到最大，1：关阀到最大，2：初始化完成

rt_device_t s_dev[3] = {NULL, NULL, NULL};

void* init_eev_ctrl(void* param)
{
    int result = RT_EOK;
    unsigned short output = 0;
    // io_ex_1:DO,使用hw_i2c0,   io_ex_2 和 io_ex_3都是eev,共用hw_i2c1
    char* dev_name[] = {"io_ex_2", "io_ex_3", "io_ex_1"};
    unsigned short do_status = 0;
    for(int loop = 0; loop < 3; loop ++)
    {
        s_dev[loop] = rt_device_find(dev_name[loop]);
        if (s_dev[loop] == RT_NULL) 
        {
            rt_kprintf("can't find %s dev\n", dev_name[loop]);
            return NULL;
        }

        result = rt_device_open(s_dev[loop], RT_NULL);
        if (result != RT_EOK) 
        {
            rt_kprintf("can't open %s dev\n", dev_name[loop]);
            return NULL;
        }
        rt_device_control(s_dev[loop], FULL_PIN_CONFIG, &output);
    }

    // 输出干接点默认输出高电平
    output = 0xFFFF;
    rt_device_control(s_dev[2], FULL_PIN_WRITE_OUTPUT_REG, &output);
    for(int loop = 0; loop < DO_MAX; loop ++)
    {
        set_one_data(DXCB_DATA_ID_DO_CTRL + loop, &do_status);
    }
    
    init_when_eev_close();
    for(int loop = 0; loop < EV_END; loop ++)
    {
        set_eev_target_step(480, loop);
        s_is_init_status[loop] = INIT_MAX_OPEN_STATUS;
        rt_kprintf("start_open_eev\n");
    }

    return s_dev;
}

//重写电磁阀IO驱动（一次写入4个电磁阀4*4=16状态，不读）
int set_eev_i2c_value(rt_device_t i2c_dev, unsigned short write_value)
{
    return rt_device_control(i2c_dev, FULL_PIN_WRITE_OUTPUT_REG, &write_value);
}

int set_eev_value(unsigned char dev_index ,unsigned short value)
{
    RETURN_VAL_IF_FAIL(dev_index < 2, FAILURE);
    return set_eev_i2c_value(s_dev[dev_index], value);
}

int get_do_value(eDO_t do_index, unsigned char* do_value)
{
    unsigned short read_value = 0;
    rt_device_control(s_dev[2], FULL_PIN_READ_OUTPUT_REG, &read_value);
    *do_value = (read_value & (1 << do_index)) >> do_index;
    return SUCCESSFUL;
}

int set_do_value(eDO_t do_index, unsigned char value)
{
    return rt_device_write(s_dev[2], do_index, &value, 1);
}

int set_all_do_value(unsigned short value)
{
    return rt_device_control(s_dev[2], FULL_PIN_WRITE_OUTPUT_REG, &value);
}

// 电子膨胀阀目标步数外部设置接口
int set_eev_target_step(unsigned short step, eEV1_8_t index)
{
    if(s_evStat[index].targetStep != step || s_evStat[index].runStopFlag == EV_CONTROL_STOP)
    {
        s_evStat[index].targetStep = step;
        s_evStat[index].runStopFlag = EV_CONTROL_RUN;
    }
    return SUCCESSFUL;
}

//用于特殊模式矫正当前步数，特殊模式下目标步数需为8的整数倍(减少跃步)
// void SetEvCurrStep(unsigned short step, eEV1_8_t index)
// {
//     if(s_evStat[index].curStep != step)
//     {
//         s_evStat[index].curStep = step;
//         s_evStat[index].runStopFlag = EV_CONTROL_STOP;
//     }
//     return;
// }

static int NeedAdjustPosition(int index)
{
    if((EV_FORWARD == s_evStat[index].rotateDirectionBak) && (EV_REVERSE == s_evStat[index].rotateDirection))
    {
        s_evStat[index].curCtrValueIndex --;
        s_evStat[index].rotateDirectionBak = s_evStat[index].rotateDirection;
        INDEXCHECK_R(s_evStat[index].curCtrValueIndex);
        s_evStat[index].curCtrValue = ctrolValue[s_evStat[index].curCtrValueIndex];
        // rt_kprintf("forward_reverse|index:%d, curCtrValue:%d\n", s_evStat[index].curCtrValueIndex, s_evStat[index].curCtrValue);
    }
    else if ((EV_REVERSE == s_evStat[index].rotateDirectionBak) && (EV_FORWARD == s_evStat[index].rotateDirection))
    {
        s_evStat[index].curCtrValue = ctrolValue[s_evStat[index].curCtrValueIndex];
        s_evStat[index].curCtrValueIndex ++;
        s_evStat[index].rotateDirectionBak = s_evStat[index].rotateDirection;
        INDEXCHECK_F(s_evStat[index].curCtrValueIndex);
        // rt_kprintf("reverse_forward|index:%d, curCtrValue:%d\n", s_evStat[index].curCtrValueIndex, s_evStat[index].curCtrValue);
    }
    else
    {
        // rt_kprintf("direct:%d, directbak:%d\n",  s_evStat[index].rotateDirection, s_evStat[index].rotateDirectionBak);
        return 0;
    }
    return 1;
}

void update_ctrl_eev_info()
{
    int i;
    for(i = EV1; i < EV_END; i++)
    {
        if(EV_CONTROL_STOP == s_evStat[i].runStopFlag)
        {
            // rt_kprintf("update_ctrl_eev_info|i:%d, flag:%d\n", i, s_evStat[i].runStopFlag);
            continue;
        }
        set_one_data(DXCB_DATA_ID_EEV_FEEDBACK_STEP + i, &s_evStat[i].curStep);
        if(s_evStat[i].targetStep > s_evStat[i].curStep)
        {
            s_evStat[i].curStep ++ ;
            s_evStat[i].cnt = EV_STOP_DELAY_TIMER;
            s_evStat[i].curCtrValue = ctrolValue[s_evStat[i].curCtrValueIndex];
            s_evStat[i].curCtrValueIndex ++;
            s_evStat[i].rotateDirection = EV_FORWARD;
            INDEXCHECK_F(s_evStat[i].curCtrValueIndex);
            // rt_kprintf("1111111|target_step:%d, cur_step:%d, cnt:%d, index:%d, curCtrlValue:%d\n", 
            //     s_evStat[i].targetStep, s_evStat[i].curStep, s_evStat[i].cnt, s_evStat[i].curCtrValueIndex, s_evStat[i].curCtrValue);
        }
        else if (s_evStat[i].targetStep < s_evStat[i].curStep)
        {
            s_evStat[i].curStep --;
            s_evStat[i].cnt = EV_STOP_DELAY_TIMER;
            s_evStat[i].curCtrValueIndex --;
            s_evStat[i].rotateDirection = EV_REVERSE;
            INDEXCHECK_R(s_evStat[i].curCtrValueIndex);
            s_evStat[i].curCtrValue = ctrolValue[s_evStat[i].curCtrValueIndex];
            // rt_kprintf("2222222|target_step:%d, cur_step:%d, cnt:%d, index:%d, curCtrlValue:%d\n", 
            //     s_evStat[i].targetStep, s_evStat[i].curStep, s_evStat[i].cnt, s_evStat[i].curCtrValueIndex, s_evStat[i].curCtrValue);
        }
        else
        {
            //运行方向调整后的步数调整(两相位)
            if(NeedAdjustPosition(i))
            {
                break;
            }
            if(s_evStat[i].cnt)
            {   
                s_evStat[i].cnt --;
                // rt_kprintf("update_info|s_evStat[i].cnt:%d\n", s_evStat[i].cnt);
            }
            else
            {
                if(s_is_init_status[i] == INIT_MAX_OPEN_STATUS)
                {
                    s_evStat[i].curStep = 520;
                    set_eev_target_step(0, i);
                    s_is_init_status[i] = 1;
                    rt_kprintf("init_close_eev\n");
                    break;
                }
                if(s_is_init_status[i] == INIT_MAX_CLOSE_STATUS)
                {
                    s_is_init_status[i] = INIT_COMPLETE_STATUS;
                    s_evStat[i].curCtrValueIndex = 1;
                    s_evStat[i].rotateDirection = EV_FORWARD;
                    s_evStat[i].rotateDirectionBak = EV_FORWARD;
                    rt_kprintf("init_eev_complete\n");
                    break;
                }
                s_evStat[i].curCtrValue = 0x00;
                s_evStat[i].runStopFlag = EV_CONTROL_STOP;

                //rt_kprintf("update_info|curCtrlValue is zero\n");
            }
        }
    }
}

int set_eev_ctrl_info(unsigned short* value1_4, unsigned short* value5_8)
{
    *value1_4 = (s_evStat[0].curCtrValue) | (s_evStat[1].curCtrValue << 4) | (s_evStat[2].curCtrValue << 8) | (s_evStat[3].curCtrValue << 12);
    *value5_8 = (s_evStat[4].curCtrValue) | (s_evStat[5].curCtrValue << 4) | (s_evStat[6].curCtrValue << 8) | (s_evStat[7].curCtrValue << 12);
    set_eev_i2c_value(s_dev[0], *value1_4);
    set_eev_i2c_value(s_dev[1], *value5_8);
    return SUCCESSFUL;
}

//阀完全关闭时初始化，index，运行方向
void init_when_eev_close()
{
    int index ;
    for(index = EV1; index < EV_END; index++)
    {
        if( EV_CLOSE == s_evStat[index].curStep)
        {
            s_evStat[index].curCtrValueIndex = 1;
            s_evStat[index].rotateDirection = EV_FORWARD;
            s_evStat[index].rotateDirectionBak = EV_FORWARD;
        }
    }
}

int get_eev_target_step_para()
{
    unsigned short cur_target_step = 0;
    static unsigned short s_target_step[EV_END] = { 0, 0, 0, 0, 0, 0, 0, 0};

    for(int loop = 0; loop < EV_END; loop ++)
    {
        if(s_is_init_status[loop] != INIT_COMPLETE_STATUS)
        {
            continue;
        }
        get_one_data(DXCB_DATA_ID_ELECTRONIC_EXPANSION_OUT_STEP + loop, &cur_target_step);
        if(cur_target_step != s_target_step[loop])
        {
            set_eev_target_step(cur_target_step, loop);
            s_target_step[loop] = cur_target_step;
            rt_kprintf("set_eev_target_step|eev_no:%d, target_step:%d\n", loop + 1, cur_target_step);
        }
    }
    return SUCCESSFUL;
}

//Ev1_4处理主函数
void eev_ctrl_main(void *parameter)
{
    unsigned short value1_4 = 0, value5_8 = 0;
    while (is_running(TRUE))
    { 
        get_eev_target_step_para();
        update_ctrl_eev_info();
        if(!get_eev_ctrl_flag())
        {
            set_eev_ctrl_info(&value1_4, &value5_8);
        }

        rt_thread_mdelay(6);
        init_when_eev_close();
        if((0 == value1_4) && (0 == value5_8))
        {
            rt_thread_mdelay(500);//挂起自身
        }
    }
}
