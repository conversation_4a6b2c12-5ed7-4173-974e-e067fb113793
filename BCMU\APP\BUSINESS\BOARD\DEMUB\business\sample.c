#include <rtthread.h>
#include <rtdevice.h>
#include <board.h>
#include <drv_common.h>
#include <math.h>
#include <string.h>
#include "sample.h"
#include "data_type.h"
#include "utils_thread.h"
#include "adc_ctrl.h"
#include "utils_server.h"
#include "pin_ctrl.h"
#include "utils_rtthread_security_func.h"
#include "drv_ht7136.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "msg_id.h"
#include "partition_def.h"
#include "storage.h"
#include "ee_public_info.h"
#include "utils_data_transmission.h"

static energy_data_t g_energy_data_init = {0};
static int g_pos = 0;
/***********************  变量定义  ************************/
static unsigned int s_digital_data[DIGITAL_COUNT];
static _rt_server_t g_sys_server = NULL;
static rt_uint32_t measure_data1[MRASURE_ANALOG_NUMBER_AC][ADCCONVERTEDVALUES_SAMPLING_NUM] = {0};
static rt_uint32_t measure_data2[MRASURE_ANALOG_NUMBER_AC][ADCCONVERTEDVALUES_SAMPLING_NUM] = {0};
// static rt_uint32_t check_data1[CHECK_ANALOG_NUMBER_AC][ADCCONVERTEDVALUES_SAMPLING_NUM] = {0};
// static rt_uint32_t check_data2[CHECK_ANALOG_NUMBER_AC][ADCCONVERTEDVALUES_SAMPLING_NUM] = {0};

static int measure_data_analog_addr[MRASURE_ANALOG_NUMBER_AC] = {
    r_UaRms,
    r_UbRms,
    r_UcRms,
    r_IaRms,
    r_IbRms,
    r_IcRms,
    r_Pfa,
    r_Pfb,
    r_Pfc,
    r_Pft,
    r_Pga,
    r_Pgb,
    r_Pgc,
    r_Freq,
    r_YUaUb,
    r_YUbUc,
    r_YUaUc,
    r_PFlag,
    // r_UabRms,
    // r_UbcRms,
    // r_UacRms,
    r_UnRms};
static rt_uint32_t measure_analog_data1[MRASURE_ANALOG_NUMBER_AC] = {0};
static rt_uint32_t measure_analog_data2[MRASURE_ANALOG_NUMBER_AC] = {0};
static int check_data_analog_addr[CHECK_ANALOG_NUMBER_AC] = {
    w_UgainA,
    w_UgainB,
    w_UgainC,
    w_IgainA,
    w_IgainB,
    w_IgainC,
    w_UaRmsoffset,
    w_UbRmsoffset,
    w_UcRmsoffset,
    w_IaRmsoffset,
    w_IbRmsoffset,
    w_IcRmsoffset};
// static rt_uint32_t check_analog_data1[CHECK_ANALOG_NUMBER_AC] = {0};
// static rt_uint32_t check_analog_data2[CHECK_ANALOG_NUMBER_AC] = {0};
static T_FilterAna filterAC1V_V_Angle_ab;
static T_FilterAna filterAC1V_V_Angle_bc;
static T_FilterAna filterAC1V_V_Angle_ac;
static T_FilterAna filterAC1V_I_Angle_a;
static T_FilterAna filterAC1V_I_Angle_b;
static T_FilterAna filterAC1V_I_Angle_c;
static T_FilterAna filterAC2V_V_Angle_ab;
static T_FilterAna filterAC2V_V_Angle_bc;
static T_FilterAna filterAC2V_V_Angle_ac;
static T_FilterAna filterAC2V_I_Angle_a;
static T_FilterAna filterAC2V_I_Angle_b;
static T_FilterAna filterAC2V_I_Angle_c;
// static T_SheetAnalogDataStruct analog_data;
// static T_SheetParaDataStruct para_data = {
//     .usI_Change = 20,
// };
static unsigned char calc_main_type = CALC_BY_A;
/*********************  静态函数原型定义  **********************/
static void sample(void);
static void analog_sample(void);
static void digital_sample(void);
static rt_uint32_t MedianFilter(rt_uint32_t data[ADCCONVERTEDVALUES_SAMPLING_NUM]);
static void hal_board_gpio_init(void);
static void set_selection_di_sample_gpio(eDI_SAMPLE channel);

void calc_maintype(void);
void sample_handle(void);
void calc_energy(void *parameter);
void calc_demand(void *parameter);
float CalcUnblance(float a, float b, float c, float angle1, float angle2, float angle3);
float CalcZeroSeqCur(float I1, float I2, float I3, float angle1, float angle2, float angle3);
float CalLineVoltage(float V1, float V2, float angle);
void harmonic(void);

static DI_CHANNEL_INFO_STRUCT s_di_sample_info[DI_END] =
    {
        {1, DI1_IN_0, 0, PIN_DI1_IN},
        {9, DI2_IN_0, 0, PIN_DI2_IN},
        {17, DI3_IN_0, 0, PIN_DI3_IN},
        {2, DI1_IN_1, 1, PIN_DI1_IN},
        {10, DI2_IN_1, 1, PIN_DI2_IN},
        {18, DI3_IN_1, 1, PIN_DI3_IN},
        {3, DI1_IN_2, 2, PIN_DI1_IN},
        {11, DI2_IN_2, 2, PIN_DI2_IN},
        {19, DI3_IN_2, 2, PIN_DI3_IN},
        {4, DI1_IN_3, 3, PIN_DI1_IN},
        {12, DI2_IN_3, 3, PIN_DI2_IN},
        {20, DI3_IN_3, 3, PIN_DI3_IN},
        {5, DI1_IN_4, 4, PIN_DI1_IN},
        {13, DI2_IN_4, 4, PIN_DI2_IN},
        {21, DI3_IN_4, 4, PIN_DI3_IN},
        {6, DI1_IN_5, 5, PIN_DI1_IN},
        {14, DI2_IN_5, 5, PIN_DI2_IN},
        {22, DI3_IN_5, 5, PIN_DI3_IN},
        {7, DI1_IN_6, 6, PIN_DI1_IN},
        {15, DI2_IN_6, 6, PIN_DI2_IN},
        {23, DI3_IN_6, 6, PIN_DI3_IN},
        {8, DI1_IN_7, 7, PIN_DI1_IN},
        {16, DI2_IN_7, 7, PIN_DI2_IN},
        {24, DI3_IN_7, 7, PIN_DI3_IN},
};


static DO_CHANNEL_INFO_STRUCT s_do_out_info[DO_END] =
{
    {DO1_OUT, PIN_DO1_OUT},
    {DO2_OUT, PIN_DO2_OUT},
};



static msg_handle_process_t s_msg_handle[] = 
{
    {NORTH_SET_PARA_MSG, handle_para_set_msg},
    {SAMPLE_PERIOD_CAL_DATA_MSG, handle_period_data_cal_msg},
    {CLEAN_HIS_ENERGY_MSG, handle_clean_energy},
};

static msg_map sample_msg_map[] =
{
    {NORTH_SET_PARA_MSG, msg_handle_nothing},      // 北向设置参数消息处理
    {SAMPLE_PERIOD_CAL_DATA_MSG, msg_handle_nothing},   // 计算电能和需量消息处理
    {CLEAN_HIS_ENERGY_MSG, msg_handle_nothing},   // 电量清零
};



void *sample_init_sys(void *param)
{
    // rt_kprintf("sample_init_sys\n");
    server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(sample_msg_map) / sizeof(msg_map);
    register_server_msg_map(sample_msg_map, server_info);
    hal_board_gpio_init(); // 初始化IO模式
    rt_timer_t period_data_calc_timer = rt_timer_create("CalcTimer", send_period_data_calc_msg, RT_NULL, 1000, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER); // 定时触发电能计算函数,周期为1000ms
    if (period_data_calc_timer != RT_NULL)
    {
        rt_timer_start(period_data_calc_timer);
    }

    init_energy_data();
    return NULL;
}




static void process_recv_msg(void) 
{
    int i = 0;
    if ((g_sys_server == NULL) || (rt_sem_take(&g_sys_server->msg_sem, 10) != RT_EOK) || (g_sys_server->msg_node == RT_NULL))
    {
        return;
    }

    _rt_msg_t curr_msg = g_sys_server->msg_node;

    for (i = 0; i < sizeof(s_msg_handle) / sizeof(s_msg_handle[0]); i++) {
        if (s_msg_handle[i].msg_id == curr_msg->msg.msg_id) {
            s_msg_handle[i].handle(curr_msg);
            break;
        }
    }

    rt_mutex_take(&g_sys_server->mutex, RT_WAITING_FOREVER);
    g_sys_server->msg_node = curr_msg->next;
    g_sys_server->msg_count--;
    rt_mutex_release(&g_sys_server->mutex);

    softbus_free(curr_msg);
}



int digital_handle()
{
    int i = 0;
    unsigned int temp_data = 0;
    unsigned int sample_digital_data[DIGITAL_COUNT] = {0};
    get_sample_digital_data(sample_digital_data);

    for (i = 0; i < 24; i++)
    {
        if (sample_digital_data[i] & 0x01)
        {
            temp_data |= (1 << i);
        }
    }
    set_one_data(DEMUB_DATA_ID_DI_24_STATUS, &temp_data);
    return SUCCESSFUL;
}



int para1_crc_cal(para_info_t *para_info)
{
    unsigned short para1_crc = 0;
    unsigned char temp_para[260] = {0};
    unsigned short offset = 0;

    unsigned int value = 0;
    get_one_data(DEMUB_DATA_ID_AC_CUR_IN_TRANS_RATIO, &value);
    put_uint16_to_buff(temp_para + offset, value);
    offset += 2;

    put_uint16_to_buff(temp_para + offset, (unsigned short)(para_info->ac_mts_status_detect));
    offset += 2;

    get_one_data(DEMUB_DATA_ID_AC_INPUT_MCCB_CONFIG, &value);
    put_uint16_to_buff(temp_para + offset, value);
    offset += 2;

    for(int phase_loop = 0; phase_loop < PHASE_NUM; phase_loop ++)
    {
        put_int16_to_buff(temp_para + offset, (short)(para_info->first_cur_zero[phase_loop]));
        offset += 2;
    }

    for(int phase_loop = 0; phase_loop < PHASE_NUM; phase_loop ++)
    {
        put_int16_to_buff(temp_para + offset, (short)(para_info->first_cur_slope[phase_loop]));
        offset += 2;
    }

    for(int phase_loop = 0; phase_loop < PHASE_NUM; phase_loop ++)
    {
        put_int16_to_buff(temp_para + offset, (short)(para_info->first_vol_zero[phase_loop]));
        offset += 2;
    }

    for(int phase_loop = 0; phase_loop < PHASE_NUM; phase_loop ++)
    {
        put_int16_to_buff(temp_para + offset, (short)(para_info->first_vol_slope[phase_loop]));
        offset += 2;
    }

    for(int phase_loop = 0; phase_loop < PHASE_NUM; phase_loop ++)
    {
        put_int16_to_buff(temp_para + offset, (short)(para_info->second_cur_zero[phase_loop]));
        offset += 2;
    }

    for(int phase_loop = 0; phase_loop < PHASE_NUM; phase_loop ++)
    {
        put_int16_to_buff(temp_para + offset, (short)(para_info->second_cur_slope[phase_loop]));
        offset += 2;
    }

    for(int phase_loop = 0; phase_loop < PHASE_NUM; phase_loop ++)
    {
        put_int16_to_buff(temp_para + offset, (short)(para_info->second_vol_zero[phase_loop]));
        offset += 2;
    }

    for(int phase_loop = 0; phase_loop < PHASE_NUM; phase_loop ++)
    {
        put_int16_to_buff(temp_para + offset, (short)(para_info->second_vol_slope[phase_loop]));
        offset += 2;
    }

    para1_crc = modbusrtu_get_crc(temp_para, offset);
    para1_crc = ((para1_crc & 0xFF) <<8) | (para1_crc >> 8);
    set_one_data(DEMUB_DATA_ID_PARA_1_CRC, &para1_crc);   

    // rt_kprintf("para1_crc|len:%d, crc:0x%x\n", offset, para1_crc);
    // for(int loop = 0; loop < offset; loop ++)
    // {
    //     rt_kprintf("%02x ", temp_para[loop]);
    // }
    // rt_kprintf("\n");
    return SUCCESSFUL;
}


void sample_main(void *parameter)
{
    g_sys_server = _curr_server_get();
    while (is_running(TRUE))
    {
        sample();
        sample_handle();
        digital_handle();
        harmonic();
        process_recv_msg();
        rt_thread_mdelay(10);
    }
}



/* 采样数据 */
static void sample(void)
{
    digital_sample();
    analog_sample();
    // static int print_cnt_ctrl = 0;
    // if(print_cnt_ctrl == 150)
    // {
    //     rt_kprintf("digital_data\n");
    //     for(int i = 0; i < 24; i ++)
    //     {
    //         rt_kprintf("0x%02x ", s_digital_data[i]);
    //     }
    //     rt_kprintf("\n analog_data_1\n");
    //     for(int i = 0; i < 19; i ++)
    //     {
    //         rt_kprintf("0x%02x ", measure_analog_data1[i]);
    //     }

    //     rt_kprintf("\n analog_data_2\n");
    //     for(int i = 0; i < 19; i ++)
    //     {
    //         rt_kprintf("0x%02x ", measure_analog_data2[i]);
    //     }

    //     unsigned short para1_crc = 0;
    //     get_one_data(DEMUB_DATA_ID_PARA_1_CRC, &para1_crc);
    //     rt_kprintf("\n para1_crc = 0x%x\n", para1_crc);
    //     print_cnt_ctrl = 0;
    // }
    // print_cnt_ctrl ++;
}

static void digital_sample(void)
{
    // rt_kprintf("adc Di sample start\n");
    unsigned int i;

    for (i = 0; i < DI_END; i++) // 24路DI
    {
        s_digital_data[i] = di_get_data_by_channel(i);
    }

    return;
}


unsigned int* get_mock_para_data(unsigned short mock_para_switch)
{
    static unsigned int s_measure_data[MRASURE_ANALOG_NUMBER_AC] = {0};
    unsigned int sid[MRASURE_ANALOG_NUMBER_AC] = {
        DEMUB_DATA_ID_MOCK_PHASE_VOL, DEMUB_DATA_ID_MOCK_PHASE_VOL + 1, DEMUB_DATA_ID_MOCK_PHASE_VOL + 2,     // 相电压
        DEMUB_DATA_ID_MOCK_PHASE_CUR, DEMUB_DATA_ID_MOCK_PHASE_CUR + 1, DEMUB_DATA_ID_MOCK_PHASE_CUR + 2,   // 相电流
        DEMUB_DATA_ID_MOCK_PHASE_POWER_FACTOR, DEMUB_DATA_ID_MOCK_PHASE_POWER_FACTOR + 1, DEMUB_DATA_ID_MOCK_PHASE_POWER_FACTOR + 2, DEMUB_DATA_ID_MOCK_TOTAL_POWER_FACTOR,  // 相功率因数，合功率因数
        DEMUB_DATA_ID_MOCK_PHASE_VOL_CUR_ANGLE, DEMUB_DATA_ID_MOCK_PHASE_VOL_CUR_ANGLE + 1, DEMUB_DATA_ID_MOCK_PHASE_VOL_CUR_ANGLE + 2,   // 电压电流角度
        DEMUB_DATA_ID_MOCK_FREQUENCY,     // 频率
        DEMUB_DATA_ID_MOCK_PHASE_VOL_ANGLE, DEMUB_DATA_ID_MOCK_PHASE_VOL_ANGLE + 2, DEMUB_DATA_ID_MOCK_PHASE_VOL_ANGLE + 1,   // 电压角度
        DEMUB_DATA_ID_MOCK_POWER_DIRECTOR,   // 功率方向
        DEMUB_DATA_ID_MOCK_ZERO_GROUND       // 零地电压
    }; 
    if(!mock_para_switch)
    {
        return s_measure_data;
    }
    for(int loop = 0; loop < MRASURE_ANALOG_NUMBER_AC; loop ++)
    {
        get_one_data(sid[loop], &s_measure_data[loop]);
    }
    
    // for(int loop = 0; loop < MRASURE_ANALOG_NUMBER_AC; loop ++)
    // {
    //     rt_kprintf("0x%x ", s_measure_data[loop]);
    // }
    // rt_kprintf("\n");
    return s_measure_data;
}



static void analog_sample(void)
{
    // rt_kprintf("adc Ai sample start\n");
    int i, j = 0;
    int addr = 0;
    rt_uint32_t data = 0;
    rt_device_t dev1;

    unsigned short mock_para_switch = 0;    // 0:关闭，1：打开
    unsigned int* mock_para_data = NULL;
    get_one_data(DEMUB_DATA_ID_MOCK_SWITCH, &mock_para_switch);
    mock_para_data = get_mock_para_data(mock_para_switch);

    dev1 = rt_device_find("ht7136_1");
    if (!dev1)
    {
        rt_kprintf("find ht7136_1 failed!\n");
        return;
    }
    if (rt_device_open(dev1, RT_NULL))
    {
        rt_kprintf("open ht7136_1 failed!\n");
        return;
    }

    for (i = 0; i < MRASURE_ANALOG_NUMBER_AC; i++)
    {
        addr = measure_data_analog_addr[i];
        for (j = 0; j < ADCCONVERTEDVALUES_SAMPLING_NUM; j++)
        {
            if(!mock_para_switch)
            {
                RETURN_IF_FAIL(rt_device_read(dev1, addr, &data, 3) == 3);
                measure_data1[i][j] = data;
            }
            else
            {
                measure_data1[i][j] = mock_para_data[i];
            }
        }
        measure_analog_data1[i] = MedianFilter(measure_data1[i]);
    }
    rt_device_close(dev1);

    rt_device_t dev2;
    dev2 = rt_device_find("ht7136_2");
    if (!dev2)
    {
        rt_kprintf("find ht7136_2 failed!\n");
        return;
    }
    if (rt_device_open(dev2, RT_NULL))
    {
        rt_kprintf("open ht7136_2 failed!\n");
        return;
    }

    for (i = 0; i < MRASURE_ANALOG_NUMBER_AC; i++)
    {
        addr = measure_data_analog_addr[i];
        for (j = 0; j < ADCCONVERTEDVALUES_SAMPLING_NUM; j++)
        {
            if(!mock_para_switch)
            {
                RETURN_IF_FAIL(rt_device_read(dev2, addr, &data, 3) == 3);
                measure_data2[i][j] = data;
            }
            else
            {
                measure_data2[i][j] = mock_para_data[i];
            }
        }
        measure_analog_data2[i] = MedianFilter(measure_data2[i]);
    }
    rt_device_close(dev2);


    return;
}

// 功能引脚初始化
static void hal_board_gpio_init(void)
{
    rt_pin_mode(PIN_A0_S1, PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_A1_S1, PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_A2_S1, PIN_MODE_OUTPUT);

    rt_pin_mode(PIN_DI1_IN, PIN_MODE_INPUT);
    rt_pin_mode(PIN_DI2_IN, PIN_MODE_INPUT);
    rt_pin_mode(PIN_DI3_IN, PIN_MODE_INPUT);

    rt_pin_mode(PIN_DO1_OUT, PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_DO2_OUT, PIN_MODE_OUTPUT);
}

// 采样通道选择
static void set_selection_di_sample_gpio(eDI_SAMPLE channel)
{
    if ((s_di_sample_info[channel].select_status & 0x01) == 1)
    {
        rt_pin_write(PIN_A0_S1, 1);
    }
    else
    {
        rt_pin_write(PIN_A0_S1, 0);
    }
    if ((s_di_sample_info[channel].select_status >> 1 & 0x01) == 1)
    {
        rt_pin_write(PIN_A1_S1, 1);
    }
    else
    {
        rt_pin_write(PIN_A1_S1, 0);
    }
    if ((s_di_sample_info[channel].select_status >> 2 & 0x01) == 1)
    {
        rt_pin_write(PIN_A2_S1, 1);
    }
    else
    {
        rt_pin_write(PIN_A2_S1, 0);
    }
    return;
}

// 从通道获取DI数据
int di_get_data_by_channel(eDI_SAMPLE channel)
{
    rt_uint32_t value;

    set_selection_di_sample_gpio(channel);
    rt_thread_mdelay(20);

    value = rt_pin_read(s_di_sample_info[channel].di_pin);

    return value;
}

// 计算主路类型
void calc_maintype(void)
{
    int ATS_A_status = s_digital_data[ATS_A_status_index];
    int ATS_B_status = s_digital_data[ATS_B_status_index];
    int Mccb_A_status = s_digital_data[Mccb_A_status_index];
    int Mccb_B_status = s_digital_data[Mccb_B_status_index];
    int input_mccb_cfg = 0;
    int mts_detect_status = 0;
    get_one_data(DEMUB_DATA_ID_AC_INPUT_MCCB_CONFIG, &input_mccb_cfg);
    get_one_data(DEMUB_DATA_ID_AC_MTS_STATUS_DETECT, &mts_detect_status);

    if (input_mccb_cfg == INPUT_MCCB_NUM_ONE)
    {
        calc_main_type = CALC_BY_A;
    }
    else if (input_mccb_cfg == INPUT_MCCB_NUM_TWO)
    {
        if (Mccb_A_status == MCCB_OFF && Mccb_B_status == MCCB_ON)
        {
            calc_main_type = CALC_BY_B;
        }
        else
        {
            calc_main_type = CALC_BY_A;
        }
    }
    else
    {
        if (mts_detect_status == MTS_DET_HAS)
        {
            if (ATS_A_status == ATS_OFF && ATS_B_status == ATS_ON)
            {
                calc_main_type = CALC_BY_B;
            }
            else
            {
                calc_main_type = CALC_BY_A;
            }
        }
        else
        {
            calc_main_type = CALC_BY_A;
        }
    }
}

// 数据处理
void sample_handle(void)
{
    unsigned char symbolAC1_V[3] = {1, 1, 1};
    unsigned char symbolAC2_V[3] = {1, 1, 1};
    unsigned char symbolAC1_I[3] = {1, 1, 1};
    unsigned char symbolAC2_I[3] = {1, 1, 1};
    
    unsigned short i;
    float tempAC1PhaseVolt[PHASE_NUM];    // 相电压
    float tempAC1PhaseCurr[PHASE_NUM];    // 相电流
    float tempAC1LineVolt[PHASE_NUM];    // 线电压
    float tempAC1V_V_Angle[PHASE_NUM];    // 电压角度差
    float tempAC1V_I_Angle[PHASE_NUM];    // 电压电流角度差
    float tempAC1Frequen;                // 频率
    float tempAC1VZero;                    // 零地电压
    float tempAC1Yv;                    // 三相电压不平衡度
    float tempAC2PhaseVolt[PHASE_NUM];    // 相电压
    float tempAC2PhaseCurr[PHASE_NUM];    // 相电流
    float tempAC2LineVolt[PHASE_NUM];    // 线电压
    float tempAC2V_V_Angle[PHASE_NUM];    // 电压角度差
    float tempAC2V_I_Angle[PHASE_NUM];    // 电压电流角度差
    float tempAC2Frequen;                // 频率
    float tempAC2VZero;                    // 零地电压
    float tempAC2Yv;                    // 三相电压不平衡度
    float tempIn;                        // 零序电流
    float tempYi;                        // 电流不平衡度
    float tempActivePower[PHASE_NUM];    // 有功功率
    float tempSysActivePower;            // 系统有功功率
    float tempReactivePower[PHASE_NUM]; // 无功功率
    float tempSysReactivePower;            // 系统无功功率
    float tempApparentPower[PHASE_NUM]; // 视在功率
    float tempSysApparentPower;            // 系统视在功率
    float tempPowerFactor[PHASE_NUM];    // 功率因数
    float tempSysPowerFactor;            // 系统功率因数

    float tempFilterAC1V_V_Angle[PHASE_NUM]; // 电压角度差
    float tempFilterAC1I_I_Angle[PHASE_NUM]; // 电流角度差
    float tempFilterAC1V_I_Angle[PHASE_NUM]; // 电压电流角度差
    float tempFilterAC2V_V_Angle[PHASE_NUM]; // 电压角度差
    float tempFilterAC2I_I_Angle[PHASE_NUM]; // 电流角度差
    float tempFilterAC2V_I_Angle[PHASE_NUM]; // 电压电流角度差
    int ac_change = 0;
    calc_maintype();
    get_one_data(DEMUB_DATA_ID_AC_CUR_IN_TRANS_RATIO, &ac_change);
    for (i = 0; i < PHASE_NUM; i++)
    {
        // 相电压
        tempAC1PhaseVolt[i] = (float)measure_analog_data1[r_URms_start_index + i] / pow(2, 23) * 0.84 * 600348 / 348;
        // 相电流
        tempAC1PhaseCurr[i] = (float)measure_analog_data1[r_IRms_start_index + i] / pow(2, 23) * 0.84 * ac_change * (5 / 0.005) / 39.2;
        // 电压角度差
        // if (measure_analog_data1[r_YUU_start_index + i] >= pow(2, 20))
        // {
        //     float temp=(float)(measure_analog_data1[r_YUU_start_index + i] - pow(2, 24)) / pow(2, 20) * 180;
        //     analog_data.usAC1V_V_Angle[i] = -temp;
        // }
        // else
        // {
        tempAC1V_V_Angle[i] = (float)measure_analog_data1[r_YUU_start_index + i] / pow(2, 20) * 180;
        // }

        // 电压电流角度差
        if (measure_analog_data1[r_Pg_start_index + i] >= pow(2, 20))
        {
            tempAC1V_I_Angle[i] = 360 + (float)(measure_analog_data1[r_Pg_start_index + i] - pow(2, 24)) / pow(2, 20) * 180;
        }
        else
        {
            tempAC1V_I_Angle[i] = (float)measure_analog_data1[r_Pg_start_index + i] / pow(2, 20) * 180;
        }

        // 相电压
        tempAC2PhaseVolt[i] = (float)measure_analog_data2[r_URms_start_index + i] / pow(2, 23) * 0.84 * 600348 / 348;
        // 相电流
        tempAC2PhaseCurr[i] = (float)measure_analog_data2[r_IRms_start_index + i] / pow(2, 23) * 0.84 * ac_change * (5 / 0.005) / 39.2;
        // 电压角度差
        // if (measure_analog_data2[r_YUU_start_index + i] >= pow(2, 20))
        // {
        //     analog_data.usAC2V_V_Angle[i] = 360 + (measure_analog_data2[r_YUU_start_index + i] - pow(2, 24)) / pow(2, 20) * 180;
        // }
        // else
        // {
        tempAC2V_V_Angle[i] = (float)measure_analog_data2[r_YUU_start_index + i] / pow(2, 20) * 180;
        // }
        // 电压电流角度差
        if (measure_analog_data2[r_Pg_start_index + i] >= pow(2, 20))
        {
            tempAC2V_I_Angle[i] = 360 + (float)(measure_analog_data2[r_Pg_start_index + i] - pow(2, 24)) / pow(2, 20) * 180;
        }
        else
        {
            tempAC2V_I_Angle[i] = (float)measure_analog_data2[r_Pg_start_index + i] / pow(2, 20) * 180;
        }
    }

    tempFilterAC1V_V_Angle[0] = FilterSingleAna(&filterAC1V_V_Angle_ab, tempAC1V_V_Angle[0]);
    tempFilterAC1V_V_Angle[1] = FilterSingleAna(&filterAC1V_V_Angle_bc, tempAC1V_V_Angle[1]);
    tempFilterAC1V_V_Angle[2] = FilterSingleAna(&filterAC1V_V_Angle_ac, tempAC1V_V_Angle[2]);
    tempFilterAC1V_I_Angle[0] = FilterSingleAna(&filterAC1V_I_Angle_a, tempAC1V_I_Angle[0]);
    tempFilterAC1V_I_Angle[1] = FilterSingleAna(&filterAC1V_I_Angle_b, tempAC1V_I_Angle[1]);
    tempFilterAC1V_I_Angle[2] = FilterSingleAna(&filterAC1V_I_Angle_c, tempAC1V_I_Angle[2]);

    tempFilterAC2V_V_Angle[0] = FilterSingleAna(&filterAC2V_V_Angle_ab, tempAC2V_V_Angle[0]);
    tempFilterAC2V_V_Angle[1] = FilterSingleAna(&filterAC2V_V_Angle_bc, tempAC2V_V_Angle[1]);
    tempFilterAC2V_V_Angle[2] = FilterSingleAna(&filterAC2V_V_Angle_ac, tempAC2V_V_Angle[2]);
    tempFilterAC2V_I_Angle[0] = FilterSingleAna(&filterAC2V_I_Angle_a, tempAC2V_I_Angle[0]);
    tempFilterAC2V_I_Angle[1] = FilterSingleAna(&filterAC2V_I_Angle_b, tempAC2V_I_Angle[1]);
    tempFilterAC2V_I_Angle[2] = FilterSingleAna(&filterAC2V_I_Angle_c, tempAC2V_I_Angle[2]);

    // 频率
    tempAC1Frequen = (float)measure_analog_data1[r_Freq_index] / pow(2, 13);
    tempAC2Frequen = (float)measure_analog_data2[r_Freq_index] / pow(2, 13);
    // 零地电压
    tempAC1VZero = (float)measure_analog_data1[r_UnRms_index] / pow(2, 23) * 0.84 * 600348 / 348;
    tempAC2VZero = (float)measure_analog_data2[r_UnRms_index] / pow(2, 23) * 0.84 * 600348 / 348;

    rt_uint32_t(*measure_analog_data)[MRASURE_ANALOG_NUMBER_AC];
    if (calc_main_type == CALC_BY_A)
    {
        measure_analog_data = &measure_analog_data1;
    }
    else
    {
        measure_analog_data = &measure_analog_data2;
    }
    for (i = 0; i < PHASE_NUM; i++)
    {
        // 功率因数
        if ((*measure_analog_data)[r_Pf_start_index + i] > pow(2, 23))
        {
            tempPowerFactor[i] = (float)((*measure_analog_data)[r_Pf_start_index + i] - pow(2, 24)) / pow(2, 23);
        }
        else
        {
            tempPowerFactor[i] = (float)(*measure_analog_data)[r_Pf_start_index + i] / pow(2, 23);
        }
        tempPowerFactor[i] = fabs(tempPowerFactor[i]);
    }

    // 系统功率因数
    if ((*measure_analog_data)[r_Pft_index] > pow(2, 23))
    {
        tempSysPowerFactor = (float)((*measure_analog_data)[r_Pft_index] - pow(2, 24)) / pow(2, 23);
    }
    else
    {
        tempSysPowerFactor = (float)(*measure_analog_data)[r_Pft_index] / pow(2, 23);
    }
    tempSysPowerFactor = fabs(tempSysPowerFactor);

    // 相电压、相电流校准写死
    tempAC1PhaseVolt[0] *= 1.112;
    tempAC1PhaseVolt[1] *= 1.114;
    tempAC1PhaseVolt[2] *= 1.112;
    tempAC1PhaseCurr[0] *= 1.133;
    tempAC1PhaseCurr[1] *= 1.136;
    tempAC1PhaseCurr[2] *= 1.136;
    tempAC2PhaseVolt[0] *= 1.112;
    tempAC2PhaseVolt[1] *= 1.114;
    tempAC2PhaseVolt[2] *= 1.112;
    tempAC2PhaseCurr[0] *= 1.133;
    tempAC2PhaseCurr[1] *= 1.136;
    tempAC2PhaseCurr[2] *= 1.136;
    // 零地电压(1.15倍校准写死)
    tempAC1VZero *= 1.15;
    tempAC2VZero *= 1.15;

    for (i = 0; i < PHASE_NUM; i++)
    {
        // 相电压小于20V归零
        if (tempAC1PhaseVolt[i] < 20)
        {
            tempAC1PhaseVolt[i] = 0;
            symbolAC1_V[i] = 0;
        }
        if (tempAC2PhaseVolt[i] < 20)
        {
            tempAC2PhaseVolt[i] = 0;
            symbolAC2_V[i] = 0;
        }
        // 相电流小于0.5A归零
        if (tempAC1PhaseCurr[i] < 0.5)
        {
            tempAC1PhaseCurr[i] = 0;
            symbolAC1_I[i] = 0;
        }
        if (tempAC2PhaseCurr[i] < 0.5)
        {
            tempAC2PhaseCurr[i] = 0;
            symbolAC2_I[i] = 0;
        }
    }
    // 零地电压小于0.5V归零
    if (tempAC1VZero < 0.5)
    {
        tempAC1VZero = 0;
    }
    if (tempAC2VZero < 0.5)
    {
        tempAC2VZero = 0;
    }

    // 三相电压均小于20V或者三相电流均小于0.5A时，电压电流角度差、电流角度差赋值为0
    if (symbolAC1_V[0] + symbolAC1_V[1] + symbolAC1_V[2] == 0 || symbolAC1_I[0] + symbolAC1_I[1] + symbolAC1_I[2] == 0)
    {
        for (i = 0; i < PHASE_NUM; i++)
        {
            tempFilterAC1V_I_Angle[i] = 0;
            tempFilterAC1I_I_Angle[i] = 0;
        }
        // 三相电压均小于20V时，频率、电压角度差赋值为0
        if (symbolAC1_V[0] + symbolAC1_V[1] + symbolAC1_V[2] == 0)
        {
            tempAC1Frequen = 0;
            for (i = 0; i < PHASE_NUM; i++)
            {
                tempFilterAC1V_V_Angle[i] = 0;
            }
        }
    }
    else
    {
        tempFilterAC1I_I_Angle[0] = tempFilterAC1V_I_Angle[1] - tempFilterAC1V_I_Angle[0] + tempFilterAC1V_V_Angle[0];
        tempFilterAC1I_I_Angle[1] = tempFilterAC1V_I_Angle[2] - tempFilterAC1V_I_Angle[1] + tempFilterAC1V_V_Angle[1];
        tempFilterAC1I_I_Angle[2] = tempFilterAC1V_I_Angle[2] - tempFilterAC1V_I_Angle[0] + tempFilterAC1V_V_Angle[2];
        for (i = 0; i < PHASE_NUM; i++)
        {
            if (tempFilterAC1I_I_Angle[i] < 0)
            {
                tempFilterAC1I_I_Angle[i] += 360;
            }
        }
    }
    // 三相电压均小于20V或者三相电流均小于0.5A时，电压电流角度差、电流角度差赋值为0
    if (symbolAC2_V[0] + symbolAC2_V[1] + symbolAC2_V[2] == 0 || symbolAC2_I[0] + symbolAC2_I[1] + symbolAC2_I[2] == 0)
    {
        for (i = 0; i < PHASE_NUM; i++)
        {
            tempFilterAC2V_I_Angle[i] = 0;
            tempFilterAC2I_I_Angle[i] = 0;
        }
        // 三相电压均小于20V时，频率、电压角度差赋值为0
        if (symbolAC2_V[0] + symbolAC2_V[1] + symbolAC2_V[2] == 0)
        {
            tempAC2Frequen = 0;
            for (i = 0; i < PHASE_NUM; i++)
            {
                tempFilterAC2V_V_Angle[i] = 0;
            }
        }
    }
    else
    {
        tempFilterAC2I_I_Angle[0] = tempFilterAC2V_I_Angle[1] - tempFilterAC2V_I_Angle[0] + tempFilterAC2V_V_Angle[0];
        tempFilterAC2I_I_Angle[1] = tempFilterAC2V_I_Angle[2] - tempFilterAC2V_I_Angle[1] + tempFilterAC2V_V_Angle[1];
        tempFilterAC2I_I_Angle[2] = tempFilterAC2V_I_Angle[2] - tempFilterAC2V_I_Angle[0] + tempFilterAC2V_V_Angle[2];
        for (i = 0; i < PHASE_NUM; i++)
        {
            if (tempFilterAC2I_I_Angle[i] < 0)
            {
                tempFilterAC2I_I_Angle[i] += 360;
            }
        }
    }

    // 电压均小于20V或者电流均小于0.5A时,功率因数赋值为1
    if (calc_main_type == CALC_BY_A)
    {
        for (i = 0; i < PHASE_NUM; i++)
        {
            if (symbolAC1_V[i] * symbolAC1_I[i] == 0)
            {
                tempPowerFactor[i] = 1;
            }
        }
        if (symbolAC1_V[0] * symbolAC1_I[0] + symbolAC1_V[1] * symbolAC1_I[1] * +symbolAC1_V[2] * symbolAC1_I[2] == 0)
        {
            tempSysPowerFactor = 1;
        }
    }
    else
    {
        for (i = 0; i < PHASE_NUM; i++)
        {
            if (symbolAC2_V[i] * symbolAC2_I[i] == 0)
            {
                tempPowerFactor[i] = 1;
            }
        }
        if (symbolAC2_V[0] * symbolAC2_I[0] + symbolAC2_V[1] * symbolAC2_I[1] * +symbolAC2_V[2] * symbolAC2_I[2] == 0)
        {
            tempSysPowerFactor = 1;
        }
    }

    // 三相电压不平衡度
    tempAC1Yv = CalcUnblance(tempAC1PhaseVolt[0], tempAC1PhaseVolt[1], tempAC1PhaseVolt[2], tempFilterAC1V_V_Angle[0], tempFilterAC1V_V_Angle[1], tempFilterAC1V_V_Angle[2]);
    // 线电压
    tempAC1LineVolt[0] = CalLineVoltage(tempAC1PhaseVolt[0], tempAC1PhaseVolt[1], tempFilterAC1V_V_Angle[0]);
    tempAC1LineVolt[1] = CalLineVoltage(tempAC1PhaseVolt[1], tempAC1PhaseVolt[2], tempFilterAC1V_V_Angle[1]);
    tempAC1LineVolt[2] = CalLineVoltage(tempAC1PhaseVolt[0], tempAC1PhaseVolt[2], tempFilterAC1V_V_Angle[2]);

    // 三相电压不平衡度
    tempAC2Yv = CalcUnblance(tempAC2PhaseVolt[0], tempAC2PhaseVolt[1], tempAC2PhaseVolt[2], tempFilterAC2V_V_Angle[0], tempFilterAC2V_V_Angle[1], tempFilterAC2V_V_Angle[2]);
    // 线电压
    tempAC2LineVolt[0] = CalLineVoltage(tempAC2PhaseVolt[0], tempAC2PhaseVolt[1], tempFilterAC2V_V_Angle[0]);
    tempAC2LineVolt[1] = CalLineVoltage(tempAC2PhaseVolt[1], tempAC2PhaseVolt[2], tempFilterAC2V_V_Angle[1]);
    tempAC2LineVolt[2] = CalLineVoltage(tempAC2PhaseVolt[0], tempAC2PhaseVolt[2], tempFilterAC2V_V_Angle[2]);

    if (calc_main_type == CALC_BY_A)
    {
        // 零序电流
        tempIn = CalcZeroSeqCur(tempAC1PhaseCurr[0], tempAC1PhaseCurr[1], tempAC1PhaseCurr[2], tempFilterAC1I_I_Angle[0], tempFilterAC1I_I_Angle[1], tempFilterAC1I_I_Angle[2]);
        // 电流不平衡度，三相电压均小于0.5V时赋值为1
        if (symbolAC1_V[0] + symbolAC1_V[0] + symbolAC1_V[0] == 0)
        {
            tempYi = 1;
        }
        else
        {
            tempYi = CalcUnblance(tempAC1PhaseCurr[0], tempAC1PhaseCurr[1], tempAC1PhaseCurr[2], tempFilterAC1I_I_Angle[0], tempFilterAC1I_I_Angle[1], tempFilterAC1I_I_Angle[2]);
        }
        for (i = 0; i < PHASE_NUM; i++)
        {
            //tempActivePower[i] = tempAC1PhaseVolt[i] * tempAC1PhaseCurr[i] * tempPowerFactor[i];

            tempActivePower[i] = tempAC1PhaseVolt[i] * tempAC1PhaseCurr[i] * fabs(cos(tempFilterAC1V_I_Angle[i] / 360 * 2 * 3.1415926)) / 1000;  // 转换为KW
            //tempReactivePower[i] = tempAC1PhaseVolt[i] * tempAC1PhaseCurr[i] * fabs(sin(tempFilterAC1V_I_Angle[i] / 360 * 2 * 3.1415926));
            tempApparentPower[i] = tempAC1PhaseVolt[i] * tempAC1PhaseCurr[i] / 1000;  // 转换为KW
            tempReactivePower[i] = sqrt(pow(tempApparentPower[i], 2) - pow(tempActivePower[i], 2)) / 1000;  // 转换为KW
            tempReactivePower[i] *= ((((*measure_analog_data)[r_PFlag_index] >> (i + 12)) & 0x01) == 1 ? -1 : 1);

        }
    }
    else
    {
        // 零序电流
        tempIn = CalcZeroSeqCur(tempAC2PhaseCurr[0], tempAC2PhaseCurr[1], tempAC2PhaseCurr[2], tempFilterAC2I_I_Angle[0], tempFilterAC2I_I_Angle[1], tempFilterAC2I_I_Angle[2]);
        // 电流不平衡度，三相电压均小于0.5V时赋值为1
        if (symbolAC2_V[0] + symbolAC2_V[0] + symbolAC2_V[0] == 0)
        {
            tempYi = 1;
        }
        else
        {
            tempYi = CalcUnblance(tempAC2PhaseCurr[0], tempAC2PhaseCurr[1], tempAC2PhaseCurr[2], tempFilterAC2I_I_Angle[0], tempFilterAC2I_I_Angle[1], tempFilterAC2I_I_Angle[2]);
        }
        for (i = 0; i < PHASE_NUM; i++)
        {
            //tempActivePower[i] = tempAC2PhaseVolt[i] * tempAC2PhaseCurr[i] * tempPowerFactor[i];

            tempActivePower[i] = tempAC2PhaseVolt[i] * tempAC2PhaseCurr[i] * fabs(cos(tempFilterAC2V_I_Angle[i] / 360 * 2 * 3.1415926)) / 1000;  // 转换为KW
            //tempReactivePower[i] = tempAC2PhaseVolt[i] * tempAC2PhaseCurr[i] * sin(tempFilterAC2V_I_Angle[i] / 360 * 2 * 3.1415926);
            tempApparentPower[i] = tempAC2PhaseVolt[i] * tempAC2PhaseCurr[i] / 1000;  // 转换为KW
            tempReactivePower[i] = sqrt(pow(tempApparentPower[i], 2) - pow(tempActivePower[i], 2)) / 1000;  // 转换为KW
            tempReactivePower[i] *= ((((*measure_analog_data)[r_PFlag_index] >> (i + 12)) & 0x01) == 1 ? -1 : 1);

        }
    }
    // // 零序电流小于0.5A归零
    // if (tempIn < 0.5)
    // {
    //     tempIn = 0;
    // }
    tempSysActivePower = tempActivePower[0] + tempActivePower[1] + tempActivePower[2];
    tempSysReactivePower = tempReactivePower[0] + tempReactivePower[1] + tempReactivePower[2];
    //tempSysReactivePower *= ((((*measure_analog_data)[r_PFlag_index] >> 15) & 0x01) == 1 ? -1 : 1);
    tempSysApparentPower = sqrt(pow(tempSysActivePower, 2) + pow(tempSysReactivePower, 2));

    
    for (i = 0; i < PHASE_NUM; i++)
    {
        
        // 第一路
        unsigned short value = 0;
        set_one_data(DEMUB_DATA_ID_FIRST_INPUT_PHASE_VOL + i, &tempAC1PhaseVolt[i]);    // 相电压
        set_one_data(DEMUB_DATA_ID_FIRST_INPUT_PHASE_CUR + i, &tempAC1PhaseCurr[i]);    // 相电流
        set_one_data(DEMUB_DATA_ID_FIRST_INPUT_LINE_VOL + i, &tempAC1LineVolt[i]);      // 线电压

        value = (unsigned short)tempFilterAC1V_V_Angle[i];
        set_one_data(DEMUB_DATA_ID_FIRST_INPUT_PHASE_VOL_ANGLE_DIFF + i, &value);       // 电压角度差

        value = (unsigned short)tempFilterAC1I_I_Angle[i];
        set_one_data(DEMUB_DATA_ID_FIRST_INPUT_PHASE_CUR_ANGLE_DIFF + i, &value);       // 电流角度差

        value = (unsigned short)tempFilterAC1V_I_Angle[i];
        set_one_data(DEMUB_DATA_ID_FIRST_INPUT_PHASE_VOL_CUR_ANGLE_DIFF + i, &value);   // 电压电流角度差

        // 第二路
        set_one_data(DEMUB_DATA_ID_SECOND_INPUT_PHASE_VOL + i, &tempAC2PhaseVolt[i]);   // 相电压
        set_one_data(DEMUB_DATA_ID_SECOND_INPUT_PHASE_CUR + i, &tempAC2PhaseCurr[i]);   // 相电流
        set_one_data(DEMUB_DATA_ID_SECOND_INPUT_LINE_VOL + i, &tempAC2LineVolt[i]);     // 线电压

        value = (unsigned short)tempFilterAC2V_V_Angle[i];
        set_one_data(DEMUB_DATA_ID_SECOND_INPUT_PHASE_VOL_ANGLE_DIFF + i, &value);      // 电压角度差

        value = (unsigned short)tempFilterAC2I_I_Angle[i];
        set_one_data(DEMUB_DATA_ID_SECOND_INPUT_PHASE_CUR_ANGLE_DIFF + i, &value);      // 电流角度差

        value = (unsigned short)tempFilterAC2V_I_Angle[i];
        set_one_data(DEMUB_DATA_ID_SECOND_INPUT_PHASE_VOL_CUR_ANGLE_DIFF + i, &value);  // 电压电流角度差
        

        // 相功率信息
        set_one_data(DEMUB_DATA_ID_PHASE_ACTIVE_POWER + i, &tempActivePower[i]);   // 三项有功功率
        set_one_data(DEMUB_DATA_ID_PHASE_REACTIVE_POWER + i, &tempReactivePower[i]); // 三项无功功率
        set_one_data(DEMUB_DATA_ID_PHASE_APPARENT_POWER + i, &tempApparentPower[i]); // 三项视在功率
        set_one_data(DEMUB_DATA_ID_PHASE_POWER_FACTOR + i, &tempPowerFactor[i]); // 三项功率因数
    }
    // 第一路
    set_one_data(DEMUB_DATA_ID_FIRST_AC_INPUT_FREQ, &tempAC1Frequen); // 第一路输入频率
    set_one_data(DEMUB_DATA_ID_FIRST_ZERO_GROUND_VOL, &tempAC1VZero); // 第一路零地电压
    set_one_data(DEMUB_DATA_ID_FIRST_AC_PHASE_VOL_IMBALANCE, &tempAC1Yv); // 三相电压不平衡度
    // 第二路
    set_one_data(DEMUB_DATA_ID_SECOND_AC_INPUT_FREQ, &tempAC2Frequen); // 第二路输入频率
    set_one_data(DEMUB_DATA_ID_SECOND_ZERO_GROUND_VOL, &tempAC2VZero); // 第二路零地电压
    set_one_data(DEMUB_DATA_ID_SECOND_AC_PHASE_VOL_IMBALANCE, &tempAC2Yv); // 三相电压不平衡度
    // 系统功率信息
    set_one_data(DEMUB_DATA_ID_ZERO_CUR, &tempIn); // 零序电流
    set_one_data(DEMUB_DATA_ID_CUR_IMBALANCE, &tempYi); // 电流不平衡度
    set_one_data(DEMUB_DATA_ID_SYSTEM_ACTIVE_POWER, &tempSysActivePower); // 系统有功功率
    set_one_data(DEMUB_DATA_ID_SYSTEM_REACTIVE_POWER, &tempSysReactivePower); // 系统无功功率
    set_one_data(DEMUB_DATA_ID_SYSTEM_APPARENT_POWER, &tempSysApparentPower); // 系统视在功率
    set_one_data(DEMUB_DATA_ID_SYSTEM_POWER_FACTOR, &tempSysPowerFactor); // 系统功率因数
    
}

int get_sample_digital_data(unsigned int* digital_data)
{
    unsigned short i = 0;
    for (i = 0; i < DIGITAL_COUNT; i++)
    {
        if (s_di_sample_info[i].id_index < 1 || s_di_sample_info[i].id_index > DI_END)
        {
            return FAILURE;
        }
        digital_data[(s_di_sample_info[i].id_index - 1)] = s_digital_data[i];
    }
    return SUCCESSFUL;
}


int get_all_para_from_memory(para_info_t* para_info)
{
    int loop = 0;
    for(loop = 0; loop < PHASE_NUM; loop ++)
    {
        get_one_data(DEMUB_DATA_ID_INPUT_A_CURR_ZERO_POINT + loop, &para_info->first_cur_zero[loop]);
        get_one_data(DEMUB_DATA_ID_INPUT_A_CURR_SLOP + loop, &para_info->first_cur_slope[loop]);
        get_one_data(DEMUB_DATA_ID_INPUT_A_VOL_ZERO_POINT + loop, &para_info->first_vol_zero[loop]);
        get_one_data(DEMUB_DATA_ID_INPUT_A_VOL_SLOP + loop, &para_info->first_vol_slope[loop]);
        get_one_data(DEMUB_DATA_ID_INPUT_B_CURR_ZERO_POINT + loop, &para_info->second_cur_zero[loop]);
        get_one_data(DEMUB_DATA_ID_INPUT_B_CURR_SLOP + loop, &para_info->second_cur_slope[loop]);
        get_one_data(DEMUB_DATA_ID_INPUT_B_VOL_ZERO_POINT + loop, &para_info->second_vol_zero[loop]);
        get_one_data(DEMUB_DATA_ID_INPUT_B_VOL_SLOP + loop, &para_info->second_vol_slope[loop]);
    }
    get_one_data(DEMUB_DATA_ID_AC_CUR_IN_TRANS_RATIO, &para_info->ac_cur_tran_ratio);
    get_one_data(DEMUB_DATA_ID_AC_MTS_STATUS_DETECT, &para_info->ac_mts_status_detect);
    get_one_data(DEMUB_DATA_ID_AC_INPUT_MCCB_CONFIG, &para_info->ac_input_mccb_config);
    return SUCCESSFUL;
}



void change_float_para_to_int(para_info_t* para_info)
{
    para_info->ac_mts_status_detect = (unsigned short)((float)para_info->ac_mts_status_detect * 1000);
    for(int loop = 0; loop < PHASE_NUM; loop++)
    {
        para_info->first_cur_zero[loop] = (short)((float)para_info->first_cur_zero[loop] * 10);
        para_info->first_cur_slope[loop] = (short)((float)para_info->first_cur_slope[loop] * 1000);
        para_info->first_vol_zero[loop] = (short)((float)para_info->first_vol_zero[loop] * 10);
        para_info->first_vol_slope[loop] = (short)((float)para_info->first_vol_slope[loop] * 1000);
        para_info->second_cur_zero[loop] = (short)((float)para_info->second_cur_zero[loop] * 10);
        para_info->second_cur_slope[loop] = (short)((float)para_info->second_cur_slope[loop] * 1000);
        para_info->second_vol_zero[loop] = (short)((float)para_info->second_vol_zero[loop] * 10);
        para_info->second_vol_slope[loop] = (short)((float)para_info->second_vol_slope[loop] * 1000);
    }
}


// 设置参数量数据（外部接口）

signed int set_para_data()
{
    unsigned short i;
    static rt_uint32_t check_analog_data1_set[CHECK_ANALOG_NUMBER_AC] = {0};
    static rt_uint32_t check_analog_data2_set[CHECK_ANALOG_NUMBER_AC] = {0};

    para_info_t para_info = {0};

    // 1.取出协议数据
    get_all_para_from_memory(&para_info);
    // 2.float数据转换为整型
    change_float_para_to_int(&para_info);
    // 3.计算参数1CRC
    para1_crc_cal(&para_info);


    for (i = 0; i < PHASE_NUM; i++)
    {
        // 相电流零点
        check_analog_data1_set[w_IRmsoffset_start_index + i] = para_info.first_cur_zero[i];
        // 相电流斜率
        if (para_info.first_cur_slope[i] < 1000)
        {
            check_analog_data1_set[w_Igain_start_index + i] = (para_info.first_cur_slope[i] + 1000) * pow(2, 15) / 1000;
        }
        else
        {
            check_analog_data1_set[w_Igain_start_index + i] = (para_info.first_cur_slope[i] - 1000) * pow(2, 15) / 1000;
        }
        // 相电压零点
        check_analog_data1_set[w_URmsoffset_start_index + i] = para_info.first_vol_zero[i];
        // 相电压斜率
        if (para_info.first_vol_slope[i] < 1000)
        {
            check_analog_data1_set[w_Ugain_start_index + i] = (para_info.first_vol_slope[i] + 1000) * pow(2, 15) / 1000;
        }
        else
        {
            check_analog_data1_set[w_Ugain_start_index + i] = (para_info.first_vol_slope[i] - 1000) * pow(2, 15) / 1000;
        }

        // 相电流零点
        check_analog_data2_set[w_IRmsoffset_start_index + i] = para_info.second_cur_zero[i];
        // 相电流斜率
        if (para_info.second_cur_slope[i] < 1000)
        {
            check_analog_data2_set[w_Igain_start_index + i] = (para_info.second_cur_slope[i] + 1000) * pow(2, 15) / 1000;
        }
        else
        {
            check_analog_data2_set[w_Igain_start_index + i] = (para_info.second_cur_slope[i] - 1000) * pow(2, 15) / 1000;
        }
        // 相电压零点
        check_analog_data2_set[w_URmsoffset_start_index + i] = para_info.second_vol_zero[i];
        // 相电压斜率
        if (para_info.second_vol_slope[i] < 1000)
        {
            check_analog_data2_set[w_Ugain_start_index + i] = (para_info.second_vol_slope[i] + 1000) * pow(2, 15) / 1000;
        }
        else
        {
            check_analog_data2_set[w_Ugain_start_index + i] = (para_info.second_vol_slope[i] - 1000) * pow(2, 15) / 1000;
        }
    }



    int addr = 0;
    rt_uint32_t check = 0;

    rt_device_t dev1;
    dev1 = rt_device_find("ht7136_1");
    if (!dev1)
    {
        rt_kprintf("find ht7136_1 failed!\n");
        return FAILURE;
    }
    if (rt_device_open(dev1, RT_NULL))
    {
        rt_kprintf("open ht7136_1 failed!\n");
        return FAILURE;
    }
    RETURN_VAL_IF_FAIL(rt_device_control(dev1, CHANGE_TO_CHECKLIST_CMD, RT_NULL) == RT_EOK, FAILURE);
    RETURN_VAL_IF_FAIL(rt_device_control(dev1, WRITE_CHECKLIST_ENABLE_CMD, RT_NULL) == RT_EOK, FAILURE);
    for (i = 0; i < CHECK_ANALOG_NUMBER_AC; i++)
    {
        check = check_analog_data1_set[i];
        addr = check_data_analog_addr[i];
        RETURN_VAL_IF_FAIL(rt_device_write(dev1, addr, &check, 3) == 3, FAILURE);
    }
    RETURN_VAL_IF_FAIL(rt_device_control(dev1, WRITE_CHECKLIST_DISABLE_CMD, RT_NULL) == RT_EOK, FAILURE);
    RETURN_VAL_IF_FAIL(rt_device_control(dev1, CHANGE_TO_MEASURELIST_CMD, RT_NULL) == RT_EOK, FAILURE);
    rt_device_close(dev1);

    rt_device_t dev2;
    dev2 = rt_device_find("ht7136_2");
    if (!dev2)
    {
        rt_kprintf("find ht7136_2 failed!\n");
        return FAILURE;
    }
    if (rt_device_open(dev2, RT_NULL))
    {
        rt_kprintf("open ht7136_2 failed!\n");
        return FAILURE;
    }
    RETURN_VAL_IF_FAIL(rt_device_control(dev2, CHANGE_TO_CHECKLIST_CMD, RT_NULL) == RT_EOK, FAILURE);
    RETURN_VAL_IF_FAIL(rt_device_control(dev2, WRITE_CHECKLIST_ENABLE_CMD, RT_NULL) == RT_EOK, FAILURE);
    for (i = 0; i < CHECK_ANALOG_NUMBER_AC; i++)
    {
        check = check_analog_data2_set[i];
        addr = check_data_analog_addr[i];
        RETURN_VAL_IF_FAIL(rt_device_write(dev2, addr, &check, 3) == 3, FAILURE);
    }
    RETURN_VAL_IF_FAIL(rt_device_control(dev2, WRITE_CHECKLIST_DISABLE_CMD, RT_NULL) == RT_EOK, FAILURE);
    RETURN_VAL_IF_FAIL(rt_device_control(dev2, CHANGE_TO_MEASURELIST_CMD, RT_NULL) == RT_EOK, FAILURE);
    rt_device_close(dev2);

    return SUCCESSFUL;
}

static rt_uint32_t MedianFilter(rt_uint32_t data[ADCCONVERTEDVALUES_SAMPLING_NUM])
{
    rt_uint32_t temp, ave;
    for (int i = 0; i < ADCCONVERTEDVALUES_SAMPLING_NUM - 1; i++)
    {
        for (int j = i + 1; j < ADCCONVERTEDVALUES_SAMPLING_NUM; j++)
        {
            if (data[j] < data[i])
            {
                temp = data[i];
                data[i] = data[j];
                data[j] = temp;
            }
        }
    }
    if (ADCCONVERTEDVALUES_SAMPLING_NUM < 4)
    {
        ave = data[ADCCONVERTEDVALUES_SAMPLING_NUM / 2];
    }
    else
    {
        ave = (data[ADCCONVERTEDVALUES_SAMPLING_NUM / 2 - 2] + data[ADCCONVERTEDVALUES_SAMPLING_NUM / 2 - 1] + data[ADCCONVERTEDVALUES_SAMPLING_NUM / 2] + data[ADCCONVERTEDVALUES_SAMPLING_NUM / 2 + 1]) / 4;
    }
    return ave;
}


int get_power_from_memory(float* phase_active_power, float* phase_active_energy, float* phase_reactive_power, 
                        float* phase_pos_reactive_energy, float* phase_neg_reactive_energy)
{
    int loop = 0;
    for(loop = 0; loop < PHASE_NUM; loop ++)
    {
        get_one_data(DEMUB_DATA_ID_PHASE_ACTIVE_POWER + loop, &phase_active_power[loop]);     // 相有功功率
        get_one_data(DEMUB_DATA_ID_PHASE_ACTIVE_ENERGY + loop, &phase_active_energy[loop]);   // 相有功电能
        get_one_data(DEMUB_DATA_ID_PHASE_REACTIVE_POWER + loop, &phase_reactive_power[loop]); // 相无功功率
        get_one_data(DEMUB_DATA_ID_PHASE_POSITIVE_REACTIVE_ENERGY + loop, &phase_pos_reactive_energy[loop]);  // 相正向无功电能
        get_one_data(DEMUB_DATA_ID_PHASE_NEGTIVE_REACTIVE_ENERGY + loop, &phase_neg_reactive_energy[loop]);   // 相负向无功电能
    }
    return SUCCESSFUL;
}


int cal_system_energy(float* phase_active_energy, float* phase_pos_reactive_energy, float* phase_neg_reactive_energy)
{
    int loop = 0;
    float system_active_energy = 0.0;
    float system_pos_reactive_energy = 0.0;
    float system_neg_reactive_energy = 0.0;
    float system_total_reactive_energy = 0.0;    
    for(loop = 0; loop < PHASE_NUM; loop ++)
    {
        system_active_energy += phase_active_energy[loop];
        system_pos_reactive_energy += phase_pos_reactive_energy[loop];
        system_neg_reactive_energy += phase_neg_reactive_energy[loop];
    }
    system_total_reactive_energy = abs(system_pos_reactive_energy) + abs(system_neg_reactive_energy);

    set_one_data(DEMUB_DATA_ID_TOTAL_ACTIVE_ENERGY, &system_active_energy);
    set_one_data(DEMUB_DATA_ID_PHASE_TOATL_POSITIVE_REACTIVE_ENERGY, &system_pos_reactive_energy);
    set_one_data(DEMUB_DATA_ID_PHASE_TOATL_NEGTIVE_REACTIVE_ENERGY, &system_neg_reactive_energy);
    set_one_data(DEMUB_DATA_ID_TOTAL_REACTIVE_ENERGY, &system_total_reactive_energy);

    return SUCCESSFUL;
}




void calc_energy(void *parameter)
{
    unsigned short i;
    static unsigned int count = 1;
    static unsigned int old_tick = 0;
    unsigned int new_tick = rt_tick_get_millisecond();
    int time_diff_ms = 0;

    float phase_active_power[PHASE_NUM] = {0};

    float phase_active_energy[PHASE_NUM] = {0};
    float phase_reactive_power[PHASE_NUM] = {0};
    float phase_pos_reactive_energy[PHASE_NUM] = {0};
    float phase_neg_reactive_energy[PHASE_NUM] = {0};   // 后续单独设计计量类结构体
    float phase_total_reactive_energy[PHASE_NUM] = {0}; // 相总无功电能


    float fActivePower[3] = {0.0f,0.0f,0.0f};
    float fReactivePosPower[3] = {0.0f,0.0f,0.0f};
    float fReactiveNegPower[3] = {0.0f,0.0f,0.0f};


    time_diff_ms = new_tick - old_tick;
    if(time_diff_ms < 0)
    {
        time_diff_ms = (~time_diff_ms) + 1;
    }
    old_tick = new_tick;

    get_power_from_memory(phase_active_power, phase_active_energy, phase_reactive_power, phase_pos_reactive_energy, phase_neg_reactive_energy);

    for (i = 0; i < PHASE_NUM; i++)
    {
        fActivePower[i] = phase_active_power[i] * time_diff_ms / 3600000;
        phase_active_energy[i] += fActivePower[i];

        if (phase_reactive_power[i] >= 0)
        {
        
            fReactivePosPower[i] = phase_reactive_power[i] * time_diff_ms / 3600000;
            phase_pos_reactive_energy[i] += fReactivePosPower[i];
        }
        else
        {
            fReactiveNegPower[i] = phase_reactive_power[i] * time_diff_ms / 3600000;
            phase_neg_reactive_energy[i] += fReactiveNegPower[i];
        }
        phase_total_reactive_energy[i] = abs(phase_pos_reactive_energy[i]) + abs(phase_neg_reactive_energy[i]);

        
        set_one_data(DEMUB_DATA_ID_PHASE_ACTIVE_ENERGY + i, &phase_active_energy[i]);   // 相有功电能
        set_one_data(DEMUB_DATA_ID_PHASE_POSITIVE_REACTIVE_ENERGY + i, &phase_pos_reactive_energy[i]);  // 相正向无功电能
        set_one_data(DEMUB_DATA_ID_PHASE_NEGTIVE_REACTIVE_ENERGY + i, &phase_neg_reactive_energy[i]);   // 相负向无功电能
        set_one_data(DEMUB_DATA_ID_PHASE_TOTAL_REACTIVE_ENERGY + i, &phase_total_reactive_energy[i]);   // 相总无功电能
        
    }
    cal_system_energy(phase_active_energy, phase_pos_reactive_energy, phase_neg_reactive_energy);

    // 保存
    if(SAVE_DATA_COUNT == count)
    {
        save_energy_data();
        count = 0;
    }
    count++;
}
    


static float s_afPhaseCurrMaxDemand[PHASE_NUM];
static float s_afActivePowerMaxDemand[PHASE_NUM];
static float s_fSysActivePowerMaxDemand;


int get_phase_info_from_memory(float* first_phase_curr, float* second_phase_curr, float* phase_active_power, float* sys_active_power)
{
    int loop = 0;
    for(loop = 0; loop < PHASE_NUM; loop ++)
    {
        get_one_data(DEMUB_DATA_ID_FIRST_INPUT_PHASE_CUR + loop, &first_phase_curr[loop]);    // 第一路相电流
        get_one_data(DEMUB_DATA_ID_SECOND_INPUT_PHASE_CUR + loop, &second_phase_curr[loop]);   // 第二路相电流
        get_one_data(DEMUB_DATA_ID_PHASE_ACTIVE_POWER + loop, &phase_active_power[loop]);     // 相有功功率
    }
    get_one_data(DEMUB_DATA_ID_SYSTEM_ACTIVE_POWER, sys_active_power); // 系统有功功率
    return SUCCESSFUL;
}

typedef struct {
    float phase_curr_demand[PHASE_NUM];
    float phase_curr_max_demand[PHASE_NUM];
    float phase_active_power_demand[PHASE_NUM];
    float phase_active_power_max_demand[PHASE_NUM];
    float sys_active_power_demand;
    float sys_active_power_max_demand ;    // 后续单独设计有功电能类结构体
} cal_demand_info_t; 

int save_demand_info_to_memory(cal_demand_info_t* cal_demand_info)
{
    int loop = 0; 
    for(loop = 0; loop < PHASE_NUM; loop ++)
    {
        set_one_data(DEMUB_DATA_ID_PHASE_NEED_CUR + loop, &cal_demand_info->phase_curr_demand[loop]);                          // 相电流需量
        set_one_data(DEMUB_DATA_ID_PHASE_MAX_NEED_CUR + loop, &cal_demand_info->phase_curr_max_demand[loop]);                  // 相电流最大需量
        set_one_data(DEMUB_DATA_ID_PHASE_NEED_ACTIVE_ENERGY + loop, &cal_demand_info->phase_active_power_demand[loop]);        // 相有功功率需量
        set_one_data(DEMUB_DATA_ID_PHASE_MAX_NEED_ACTIVE_ENERGY + loop, &cal_demand_info->phase_active_power_max_demand[loop]);// 相有功功率最大需量
    }
    set_one_data(DEMUB_DATA_ID_SYSTEM_NEED_ACTIVE_ENERGY, &cal_demand_info->sys_active_power_demand);                       // 系统有功功率需量
    set_one_data(DEMUB_DATA_ID_SYSTEM_MAX_NEED_ACTIVE_ENERGY, &cal_demand_info->sys_active_power_max_demand);               // 系统有功功率最大需量
    return SUCCESSFUL;
}


void calc_demand(void *parameter)
{
    static int count = 0;
    static int cnt = 0;
    static int aResultCount = 0;
    static float fPhaCurr[PHASE_NUM];
    static float fActePower[PHASE_NUM];
    static float fSysActPower;
    static float alPhaCurrAver[PHASE_NUM][AVER_NUM];
    static float alPhaPowerAver[PHASE_NUM][AVER_NUM];
    static float alSysPowerAver[AVER_NUM];
    static float alPhaCurrSum[PHASE_NUM];
    static float alPhaPowSum[PHASE_NUM];
    static float alSysPowSum;
    int i, j;
    static int bFlag = 0;
    count++;
    // 功率结构体
    float first_phase_curr[PHASE_NUM] = {0};
    float second_phase_curr[PHASE_NUM] = {0};
    float phase_active_power[PHASE_NUM] = {0};
    float sys_active_power = 0;

    cal_demand_info_t cal_demand_info = {0};    // 需量结构体
    get_phase_info_from_memory(first_phase_curr, second_phase_curr, phase_active_power, &sys_active_power);

    if (SAMPLE_TEN_TIMES == count)
    {
        count = 0;
        aResultCount++;
        for (i = 0; i < PHASE_NUM; i++)
        {
            if (calc_main_type == CALC_BY_A)
            {
                fPhaCurr[i] += first_phase_curr[i];
            }
            else
            {
                fPhaCurr[i] += second_phase_curr[i];
            }

            fActePower[i] += phase_active_power[i];
        }
        fSysActPower += sys_active_power;
    }

    if (aResultCount == COUNT_TIMES)
    {
        aResultCount = 0;
        for (i = 0; i < PHASE_NUM; i++)
        {
            alPhaCurrAver[i][cnt] = fPhaCurr[i] / COUNT_TIMES;
            alPhaPowerAver[i][cnt] = fActePower[i] / COUNT_TIMES;
        }
        alSysPowerAver[cnt] = fSysActPower / COUNT_TIMES;

        for (i = 0; i < PHASE_NUM; i++)
        {
            fPhaCurr[i] = 0;
            fActePower[i] = 0;
        }
        fSysActPower = 0;

        cnt = (cnt + 1) % AVER_NUM;
        if (cnt == 0)
            bFlag = 1;
    }

    if (bFlag)
    {
        for (i = 0; i < PHASE_NUM; i++)
        {
            alPhaCurrSum[i] = 0;
            alPhaPowSum[i] = 0;
        }
        alSysPowSum = 0;

        for (i = 0; i < PHASE_NUM; i++)
        {
            for (j = 0; j < AVER_NUM; j++)
            {
                alPhaCurrSum[i] += alPhaCurrAver[i][j];
                alPhaPowSum[i] += alPhaPowerAver[i][j];
            }
        }

        for (j = 0; j < AVER_NUM; j++)
        {
            alSysPowSum += alSysPowerAver[j];
        }

        for (i = 0; i < PHASE_NUM; i++)
        {
            cal_demand_info.phase_curr_demand[i] = alPhaCurrSum[i] / AVER_NUM;
            cal_demand_info.phase_active_power_demand[i] = alPhaPowSum[i] / AVER_NUM;
        }
        cal_demand_info.sys_active_power_demand = alSysPowSum / AVER_NUM;

        for (i = 0; i < PHASE_NUM; i++)
        {
            if (cal_demand_info.phase_curr_demand[i] > s_afPhaseCurrMaxDemand[i])
            {
                s_afPhaseCurrMaxDemand[i] = cal_demand_info.phase_curr_demand[i];
                cal_demand_info.phase_curr_max_demand[i] = cal_demand_info.phase_curr_demand[i];
            }
            if (cal_demand_info.phase_active_power_demand[i] > s_afActivePowerMaxDemand[i])
            {
                s_afActivePowerMaxDemand[i] = cal_demand_info.phase_active_power_demand[i];
                cal_demand_info.phase_active_power_max_demand[i] = cal_demand_info.phase_active_power_demand[i];
            }
        }
        if (cal_demand_info.sys_active_power_demand > s_fSysActivePowerMaxDemand)
        {
            s_fSysActivePowerMaxDemand = cal_demand_info.sys_active_power_demand;
            cal_demand_info.sys_active_power_max_demand = cal_demand_info.sys_active_power_demand;
        }
        save_demand_info_to_memory(&cal_demand_info);
    }
}

float CalcUnblance(float a, float b, float c, float angle1, float angle2, float angle3)
{
    float result, tmp1, tmp2;
    float calc1, calc2, calc3, calc4, calc5, calc6, tmp_sum_neg, tmp_sum_poz;
    if (a < 0.001 && b < 0.001 && c < 0.001)
    {
        return 0;
    }
    calc1 = pow(a, 2);
    calc2 = pow(b, 2);
    calc3 = pow(c, 2);
    calc4 = 2 * a * b * cos((angle1 - 240) / 360 * 2 * 3.1415926);
    calc5 = 2 * b * c * cos((angle2 - 240) / 360 * 2 * 3.1415926);
    calc6 = 2 * a * c * cos((angle3 - 120) / 360 * 2 * 3.1415926);
    tmp_sum_neg = calc1 + calc2 + calc3 + calc4 + calc5 + calc6;
    tmp_sum_poz = pow(a, 2) + pow(b, 2) + pow(c, 2) + 2 * a * b * cos((angle1 - 120) / 360 * 2 * 3.1415926) + 2 * b * c * cos((angle2 - 120) / 360 * 2 * 3.1415926) + 2 * a * c * cos((angle3 - 240) / 360 * 2 * 3.1415926);
    tmp_sum_neg = fabs(tmp_sum_neg);
    tmp_sum_poz = fabs(tmp_sum_poz);
    if (tmp_sum_neg < 0 || tmp_sum_poz <= 0)
    {
        return 1;
    }
    tmp2 = sqrt(tmp_sum_neg);
    tmp1 = sqrt(tmp_sum_poz);

    result = tmp2 / tmp1;

    if (result > 1)
    {
        result = 1;
    }

    return result;
}

float CalcZeroSeqCur(float I1, float I2, float I3, float angle1, float angle2, float angle3)
{
    float fZeroSeqCur = 0;

    fZeroSeqCur = sqrt(pow(I1, 2) + pow(I2, 2) + pow(I3, 2) + 2 * I1 * I2 * cos(angle1 / 360 * 2 * 3.1415926) + 2 * I2 * I3 * cos(angle2 / 360 * 2 * 3.1415926) + 2 * I1 * I3 * cos(angle3 / 360 * 2 * 3.1415926));
    return fZeroSeqCur;
}

float CalLineVoltage(float V1, float V2, float angle)
{
    float fLineVolt = 0;

    fLineVolt = sqrt(pow(V1, 2) + pow(V2, 2) - 2 * V1 * V2 * cos(angle / 360 * 2 * 3.1415926));

    return fLineVolt;
}

float FilterSingleAna(T_FilterAna *ptFilter, float finVal)
{
    float fValBak = 0.0;
    float ftmp = 0.0;
    static float afTempValLocal[FILTER_SAMPLE_TIMES] = {0.0};
    unsigned char i,j;
    if (ptFilter->ucCounter >= FILTER_SAMPLE_TIMES )
    {
        ptFilter->ucCounter = 0;
        ptFilter->afSampleArray[ptFilter->ucCounter] = finVal;
    }
    else
    {
        ptFilter->afSampleArray[ptFilter->ucCounter] = finVal;
        ptFilter->ucCounter += 1;
    }
    for( i=0; i<FILTER_SAMPLE_TIMES; i++ )
    {
        afTempValLocal[i] = ptFilter->afSampleArray[i];
    }
    for( i=0; i<FILTER_SAMPLE_TIMES; i++ )
    {
        for( j=i+1; j<FILTER_SAMPLE_TIMES; j++ )
        {
            if( afTempValLocal[j] < afTempValLocal[i] )
            {
                ftmp = afTempValLocal[i];
                afTempValLocal[i] = afTempValLocal[j];
                afTempValLocal[j] = ftmp;
            }
        }
    }

    ftmp = 0.0;
    for( i=REMOVE_HALF_SAMPLE_TIMES; i<FILTER_SAMPLE_TIMES-REMOVE_HALF_SAMPLE_TIMES; i++ )
    {
        ftmp += afTempValLocal[i];
    }

    fValBak = ftmp/(float)(FILTER_SAMPLE_TIMES-REMOVE_HALF_SAMPLE_TIMES*2);

    return fValBak;
}

#define Num 128 // FFT data number
static unsigned char ChanelNum = 0;
static rt_uint32_t SampleData[Num * 7];
signed short ActualData[Num]; // one channel 128 points data for calculate FFT
compx s[Num] = {{0, 0}};      // length is Num
float sendfftdata[Num];
/************ const data of sin/cos ************/
const float cosf_tab[256] = {
    1.00000, 0.99970, 0.99880, 0.99729, 0.99518, 0.99248, 0.98918, 0.98528, 0.98079, 0.97570,
    0.97003, 0.96378, 0.95694, 0.94953, 0.94154, 0.93299, 0.92388, 0.91421, 0.90399, 0.89322,
    0.88192, 0.87009, 0.85773, 0.84485, 0.83147, 0.81758, 0.80321, 0.78835, 0.77301, 0.75721,
    0.74095, 0.72425, 0.70711, 0.68954, 0.67156, 0.65317, 0.63439, 0.61523, 0.59570, 0.57581,
    0.55557, 0.53500, 0.51410, 0.49290, 0.47140, 0.44961, 0.42756, 0.40524, 0.38268, 0.35990,
    0.33689, 0.31368, 0.29028, 0.26671, 0.24298, 0.21910, 0.19509, 0.17096, 0.14673, 0.12241,
    0.09802, 0.07356, 0.04907, 0.02454, 0.00000, -0.02454, -0.04907, -0.07356, -0.09802, -0.12241,
    -0.14673, -0.17096, -0.19509, -0.21910, -0.24298, -0.26671, -0.29028, -0.31368, -0.33689, -0.35990,
    -0.38268, -0.40524, -0.42756, -0.44961, -0.47140, -0.49290, -0.51410, -0.53500, -0.55557, -0.57581,
    -0.59570, -0.61523, -0.63439, -0.65317, -0.67156, -0.68954, -0.70711, -0.72425, -0.74095, -0.75721,
    -0.77301, -0.78835, -0.80321, -0.81758, -0.83147, -0.84485, -0.85773, -0.87009, -0.88192, -0.89322,
    -0.90399, -0.91421, -0.92388, -0.93299, -0.94154, -0.94953, -0.95694, -0.96378, -0.97003, -0.97570,
    -0.98079, -0.98528, -0.98918, -0.99248, -0.99518, -0.99729, -0.99880, -0.99970, -1.00000, -0.99970,
    -0.99880, -0.99729, -0.99518, -0.99248, -0.98918, -0.98528, -0.98079, -0.97570, -0.97003, -0.96378,
    -0.95694, -0.94953, -0.94154, -0.93299, -0.92388, -0.91421, -0.90399, -0.89322, -0.88192, -0.87009,
    -0.85773, -0.84485, -0.83147, -0.81758, -0.80321, -0.78835, -0.77301, -0.75721, -0.74095, -0.72425,
    -0.70711, -0.68954, -0.67156, -0.65317, -0.63439, -0.61523, -0.59570, -0.57581, -0.55557, -0.53500,
    -0.51410, -0.49290, -0.47140, -0.44961, -0.42756, -0.40524, -0.38268, -0.35990, -0.33689, -0.31368,
    -0.29028, -0.26671, -0.24298, -0.21910, -0.19509, -0.17096, -0.14673, -0.12241, -0.09802, -0.07356,
    -0.04907, -0.02454, 0.00000, 0.02454, 0.04907, 0.07356, 0.09802, 0.12241, 0.14673, 0.17096,
    0.19509, 0.21910, 0.24298, 0.26671, 0.29028, 0.31368, 0.33689, 0.35990, 0.38268, 0.40524,
    0.42756, 0.44961, 0.47140, 0.49290, 0.51410, 0.53500, 0.55557, 0.57581, 0.59570, 0.61523,
    0.63439, 0.65317, 0.67156, 0.68954, 0.70711, 0.72425, 0.74095, 0.75721, 0.77301, 0.78835,
    0.80321, 0.81758, 0.83147, 0.84485, 0.85773, 0.87009, 0.88192, 0.89322, 0.90399, 0.91421,
    0.92388, 0.93299, 0.94154, 0.94953, 0.95694, 0.96378, 0.97003, 0.97570, 0.98079, 0.98528,
    0.98918, 0.99248, 0.99518, 0.99729, 0.99880, 0.99970};

const float sinf_tab[256] = {
    0.00000, 0.02454, 0.04907, 0.07356, 0.09802, 0.12241, 0.14673, 0.17096, 0.19509, 0.21910,
    0.24298, 0.26671, 0.29028, 0.31368, 0.33689, 0.35990, 0.38268, 0.40524, 0.42756, 0.44961,
    0.47140, 0.49290, 0.51410, 0.53500, 0.55557, 0.57581, 0.59570, 0.61523, 0.63439, 0.65317,
    0.67156, 0.68954, 0.70711, 0.72425, 0.74095, 0.75721, 0.77301, 0.78835, 0.80321, 0.81758,
    0.83147, 0.84485, 0.85773, 0.87009, 0.88192, 0.89322, 0.90399, 0.91421, 0.92388, 0.93299,
    0.94154, 0.94953, 0.95694, 0.96378, 0.97003, 0.97570, 0.98079, 0.98528, 0.98918, 0.99248,
    0.99518, 0.99729, 0.99880, 0.99970, 1.00000, 0.99970, 0.99880, 0.99729, 0.99518, 0.99248,
    0.98918, 0.98528, 0.98079, 0.97570, 0.97003, 0.96378, 0.95694, 0.94953, 0.94154, 0.93299,
    0.92388, 0.91421, 0.90399, 0.89322, 0.88192, 0.87009, 0.85773, 0.84485, 0.83147, 0.81758,
    0.80321, 0.78835, 0.77301, 0.75721, 0.74095, 0.72425, 0.70711, 0.68954, 0.67156, 0.65317,
    0.63439, 0.61523, 0.59570, 0.57581, 0.55557, 0.53500, 0.51410, 0.49290, 0.47140, 0.44961,
    0.42756, 0.40524, 0.38268, 0.35990, 0.33689, 0.31368, 0.29028, 0.26671, 0.24298, 0.21910,
    0.19509, 0.17096, 0.14673, 0.12241, 0.09802, 0.07356, 0.04907, 0.02454, 0.00000, -0.02454,
    -0.04907, -0.07356, -0.09802, -0.12241, -0.14673, -0.17096, -0.19509, -0.21910, -0.24298, -0.26671,
    -0.29028, -0.31368, -0.33689, -0.35990, -0.38268, -0.40524, -0.42756, -0.44961, -0.47140, -0.49290,
    -0.51410, -0.53500, -0.55557, -0.57581, -0.59570, -0.61523, -0.63439, -0.65317, -0.67156, -0.68954,
    -0.70711, -0.72425, -0.74095, -0.75721, -0.77301, -0.78835, -0.80321, -0.81758, -0.83147, -0.84485,
    -0.85773, -0.87009, -0.88192, -0.89322, -0.90399, -0.91421, -0.92388, -0.93299, -0.94154, -0.94953,
    -0.95694, -0.96378, -0.97003, -0.97570, -0.98079, -0.98528, -0.98918, -0.99248, -0.99518, -0.99729,
    -0.99880, -0.99970, -1.00000, -0.99970, -0.99880, -0.99729, -0.99518, -0.99248, -0.98918, -0.98528,
    -0.98079, -0.97570, -0.97003, -0.96378, -0.95694, -0.94953, -0.94154, -0.93299, -0.92388, -0.91421,
    -0.90399, -0.89322, -0.88192, -0.87009, -0.85773, -0.84485, -0.83147, -0.81758, -0.80321, -0.78835,
    -0.77301, -0.75721, -0.74095, -0.72425, -0.70711, -0.68954, -0.67156, -0.65317, -0.63439, -0.61523,
    -0.59570, -0.57581, -0.55557, -0.53500, -0.51410, -0.49290, -0.47140, -0.44961, -0.42756, -0.40524,
    -0.38268, -0.35990, -0.33689, -0.31368, -0.29028, -0.26671, -0.24298, -0.21910, -0.19509, -0.17096,
    -0.14673, -0.12241, -0.09802, -0.07356, -0.04907, -0.02454};

const float FftCoefficient[31] = { ////1~31 harmonic coefficient
    1.000000000000000,
    1.000925409832445,
    1.002469903639547,
    1.004636706352368,
    1.007430350532950,
    1.010856694162959,
    1.014922943701906,
    1.019637682557525,
    1.025010905145191,
    1.031054056748977,
    1.037780079434771,
    1.045203464305803,
    1.053340310433575,
    1.062208390842917,
    1.071827225979052,
    1.082218165137800,
    1.093404476397839,
    1.105411445656847,
    1.118266485442214,
    1.131999254242324,
    1.146641787187304,
    1.162228638999322,
    1.178797040233217,
    1.196387067939490,
    1.215041832005096,
    1.234807678564162,
    1.255734412022930,
    1.277875537412385,
    1.301288524970737,
    1.326035099068472,
    1.352181553824114};

short max_find(short *x, unsigned char num) // found the max data
{
    short iax = 0;
    short maxret = 0;
    short tmp = 0;

    tmp = abs(x[0]);
    for (iax = 0; iax < num; iax++) // one wave 128 points
    {
        if ((x[iax] < 0) && ((tmp + x[iax]) < 0))
        {
            maxret = -x[iax];
            tmp = maxret;
        }
        if ((x[iax] > 0) && ((tmp - x[iax]) < 0))
        {
            maxret = x[iax];
            tmp = maxret;
        }
    }
    return (maxret);
}

compx EE(compx b1, compx b2)
{
    compx b3;
    b3.real = b1.real * b2.real - b1.imag * b2.imag;
    b3.imag = b1.real * b2.imag + b1.imag * b2.real;
    return (b3);
}

void FFT(compx *xin, short N)
{
    unsigned char f, j, i, k, m, L;
    unsigned short le, B, ip;
    compx w, t;

    f = N;
    for (m = 1; (f = f / 2) != 1; m++)
        ; // m ranks for FFT

    j = N / 2;
    for (i = 1; i <= (N - 2); i++)
    {
        if (i < j)
        {
            t = xin[j];
            xin[j] = xin[i];
            xin[i] = t;
        }
        k = N / 2;
        while (j >= k)
        {
            j = j - k;
            k = k / 2;
        }
        j = j + k;
    }

    for (L = 1; L <= m; L++) // rank: 1--m
    {
        le = (unsigned short)pow(2, L);
        B = le / 2;

        for (j = 0; j <= B - 1; j++)
        {
            w.real = cosf_tab[(256 * j) >> L];
            w.imag = -sinf_tab[(256 * j) >> L];

            for (i = j; i <= N - 1; i = i + le)
            {
                ip = i + B;
                t = EE(xin[ip], w);

                xin[ip].real = xin[i].real - t.real;
                xin[ip].imag = xin[i].imag - t.imag;
                xin[i].real = xin[i].real + t.real;
                xin[i].imag = xin[i].imag + t.imag;
            }
        }
    } /***** FFT done****/
}

void harmonicwavecalculate(void)
{
    unsigned char i;
    signed short imax;
    float fft_result;

    imax = max_find(ActualData, Num);

    for (i = 0; i < Num; i++)
    {
        s[i].real = ActualData[i];
        CONTINUE_IF_FAIL(imax != 0);
        s[i].real = s[i].real / imax; // 减小幅值范围
        s[i].imag = 0;
    }

    FFT(s, Num);

    for (i = 0; i < (Num >> 1); i++) // fft_result mirror, so only need half
    {
        fft_result = sqrt(s[i].real * s[i].real + s[i].imag * s[i].imag);
        sendfftdata[i] = fft_result * 2 / Num; // 减小幅值范围
    }
}


int get_phase_cur_vol_from_memory(float* first_phase_curr, float* first_phase_vol, float* second_phase_curr, float* second_phase_vol, float* first_input_freq)
{
    int loop = 0;
    for(loop = 0; loop < PHASE_NUM; loop ++)
    {
        get_one_data(DEMUB_DATA_ID_FIRST_INPUT_PHASE_CUR + loop, &first_phase_curr[loop]);
        get_one_data(DEMUB_DATA_ID_FIRST_INPUT_PHASE_VOL + loop, &first_phase_vol[loop]);
        get_one_data(DEMUB_DATA_ID_SECOND_INPUT_PHASE_CUR + loop, &second_phase_curr[loop]);
        get_one_data(DEMUB_DATA_ID_SECOND_INPUT_PHASE_VOL + loop, &second_phase_vol[loop]);
    }
    get_one_data(DEMUB_DATA_ID_FIRST_AC_INPUT_FREQ, first_input_freq);
    return SUCCESSFUL;
}


void harmonic(void)
{

    float first_phase_curr[PHASE_NUM] = {0};
    float first_phase_vol[PHASE_NUM] = {0};
    float second_phase_curr[PHASE_NUM] = {0};
    float second_phase_vol[PHASE_NUM] = {0};
    float first_input_freq = 0.0;

    float tempPhaseCurrTotalTHD[PHASE_NUM]; // 相电流总谐波畸变率
    float averge_total_phase_cur_thd = 0.0; // 相电流平均总谐波畸变率
    float tempPhaseVoltTotalTHD[PHASE_NUM]; // 相电压总谐波畸变率
    float averge_total_phase_vol_thd = 0.0; // 相电压平均总谐波畸变率
    float tempVoltHarmonics[PHASE_NUM][NUM_HARMONIC]; // 相电压谐波含有率
    float tempCurrHarmonics[PHASE_NUM][NUM_HARMONIC]; // 相电流谐波含有率
    unsigned short i, j, k;
    signed long DataAverage;
    float FundamentalWave;
    float HarmonicWave_Percent[6][32]; // Ua Ub Uc Ia Ib Ic的2~31次谐波含有率
    float sum[6] = {0.0};
    float HarmonicWave_Total_THD[6];   // Ua Ub Uc Ia Ib Ic的总谐波畸变率
    rt_uint32_t data = 0;
    rt_uint32_t check = 0;
    rt_device_t dev;
    if (calc_main_type == CALC_BY_A)
    {
        dev = rt_device_find("ht7136_1");
        if (!dev)
        {
            rt_kprintf("find ht7136_1 failed!\n");
            return;
        }
        if (rt_device_open(dev, RT_NULL))
        {
            rt_kprintf("open ht7136_1 failed!\n");
            return;
        }
    }
    else
    {
        dev = rt_device_find("ht7136_2");
        if (!dev)
        {
            rt_kprintf("find ht7136_2 failed!\n");
            return;
        }
        if (rt_device_open(dev, RT_NULL))
        {
            rt_kprintf("open ht7136_2 failed!\n");
            return;
        }
    }
    get_phase_cur_vol_from_memory(first_phase_curr, first_phase_vol, second_phase_curr, second_phase_vol, &first_input_freq);

    if (ChanelNum == 0)
    {
        RETURN_IF_FAIL(rt_device_control(dev, WRITE_CHECKLIST_ENABLE_CMD, RT_NULL) == RT_EOK); // 使能SPI写操作
        check = 0x0D;
        RETURN_IF_FAIL(rt_device_write(dev, 0xC5, &check, 3) == 3); // 停止同步数据功能,设置为手动模式
        rt_thread_mdelay(3);                                        // delay 3ms

        check = 14400 / first_input_freq;
        RETURN_IF_FAIL(rt_device_write(dev, 0xC4, &check, 3) == 3); // 设置同步数据系数
        check = 0x0F;
        RETURN_IF_FAIL(rt_device_write(dev, 0xC5, &check, 3) == 3); // 开启同步数据功能
        check = 0x23;
        RETURN_IF_FAIL(rt_device_write(dev, 0xC1, &check, 3) == 3);                                // 设置缓冲数据读指针，lose the front 35 points for fft
        RETURN_IF_FAIL(rt_device_control(dev, WRITE_CHECKLIST_DISABLE_CMD, RT_NULL) == RT_EOK); // 关闭SPI的写操作
        rt_thread_mdelay(50);                                                                    // delay 50ms

        for (i = 0; i < 896; i++) // wave 128*7
        {
            RETURN_IF_FAIL(rt_device_read(dev, 0x7F, &data, 3) == 3);
            SampleData[i] = data;
        }
    }
    rt_device_close(dev);

    for (k = 0; k < 6; k++) // take one channel from 7 channels(Ua Ub Uc Ia Ib Ic I0)
    {
        DataAverage = 0;
        for (i = 0; i < Num; i++) // one wave 128 point
        {
            ActualData[i] = (signed short)(SampleData[7 * i + ChanelNum] & 0xffff); // 2 bytes of the lower are effective
            DataAverage += ActualData[i];
        }
        DataAverage = DataAverage / Num;

        for (i = 0; i < Num; i++)
        {
            ActualData[i] = ActualData[i] - (signed short)DataAverage; // wipe off the direct current
        }
        ChanelNum++;
        if (ChanelNum == 6)
        {
            ChanelNum = 0;
        }

        harmonicwavecalculate(); // do FFT

        for (j = 0; j < 31; j++) // for fft 1~31 times
        {
            sendfftdata[j + 1] = FftCoefficient[j] * sendfftdata[j + 1]; // multiply FFT results by FftCoefficient
        }

        FundamentalWave = sendfftdata[1];
        for (j = 2; j < 32; j++)
        {
            if (FundamentalWave > 0.4) // if fundamental wave is very small,ignore calculating the harmonics,evaluate them 0
            {
                HarmonicWave_Percent[k][j] = sendfftdata[j] / FundamentalWave;
                sum[k] += sendfftdata[j] * sendfftdata[j];
            }
            else
            {
                HarmonicWave_Percent[k][j] = 0;
                sum[k] += 0;
            }
        }
        HarmonicWave_Total_THD[k] = sqrt(sum[k]) / FundamentalWave;
    }
    for (i = 0; i < PHASE_NUM; i++)
    {
        for (j = 0; j < NUM_HARMONIC; j++)
        {
            tempVoltHarmonics[i][j] = HarmonicWave_Percent[i][j + 2];
            tempCurrHarmonics[i][j] = HarmonicWave_Percent[i + 3][j + 2];
        }
        tempPhaseVoltTotalTHD[i] = HarmonicWave_Total_THD[i];
        tempPhaseCurrTotalTHD[i] = HarmonicWave_Total_THD[i + 3];
    }
    if(calc_main_type == CALC_BY_A)
    {
        for (i = 0; i < PHASE_NUM; i++)
        {
            if(first_phase_vol[i] < 0.000001)
            {
                tempPhaseVoltTotalTHD[i] = 0;
                for (j = 0; j < NUM_HARMONIC; j++)
                {
                    tempVoltHarmonics[i][j] = 0;
                }
            }
            if(first_phase_curr[i] < 0.000001)
            {
                tempPhaseCurrTotalTHD[i] = 0;
                for (j = 0; j < NUM_HARMONIC; j++)
                {
                    tempCurrHarmonics[i][j] = 0;
                }
            }
        }
    }
    else
    {
        for (i = 0; i < PHASE_NUM; i++)
        {
            if(second_phase_vol[i] < 0.000001)
            {
                tempPhaseVoltTotalTHD[i] = 0;
                for (j = 0; j < NUM_HARMONIC; j++)
                {
                    tempVoltHarmonics[i][j] = 0;
                }
            }
            if(second_phase_curr[i] < 0.000001)
            {
                tempPhaseCurrTotalTHD[i] = 0;
                for (j = 0; j < NUM_HARMONIC; j++)
                {
                    tempCurrHarmonics[i][j] = 0;
                }
            }
        }
    }
    
    
    for (j = 0; j < NUM_HARMONIC; j++)
    {
        set_one_data(DEMUB_DATA_ID_A_PHASE_VOL_HARMONIC_RATIO + j, &tempVoltHarmonics[0][j]);
        set_one_data(DEMUB_DATA_ID_B_PHASE_VOL_HARMONIC_RATIO + j, &tempVoltHarmonics[1][j]);
        set_one_data(DEMUB_DATA_ID_C_PHASE_VOL_HARMONIC_RATIO + j, &tempVoltHarmonics[2][j]);
        set_one_data(DEMUB_DATA_ID_A_PHASE_CUR_HARMONIC_RATIO + j, &tempCurrHarmonics[0][j]);
        set_one_data(DEMUB_DATA_ID_B_PHASE_CUR_HARMONIC_RATIO + j, &tempCurrHarmonics[1][j]);
        set_one_data(DEMUB_DATA_ID_C_PHASE_CUR_HARMONIC_RATIO + j, &tempCurrHarmonics[2][j]);
    }
    for (i = 0; i < PHASE_NUM; i++)
    {
        set_one_data(DEMUB_DATA_ID_PHASE_VOL_HARMONIC_DISTORSTION_RATE, &tempPhaseVoltTotalTHD[i]);
        set_one_data(DEMUB_DATA_ID_PHASE_CUR_HARMONIC_DISTORSTION_RATE, &tempPhaseCurrTotalTHD[i]);
    }

    averge_total_phase_cur_thd = (tempPhaseCurrTotalTHD[0] + tempPhaseCurrTotalTHD[1] + tempPhaseCurrTotalTHD[2]) / 3;
    averge_total_phase_vol_thd = (tempPhaseVoltTotalTHD[0] + tempPhaseVoltTotalTHD[1] + tempPhaseVoltTotalTHD[2]) / 3;
    set_one_data(DEMUB_DATA_ID_PHASE_CUR_AVE_HARMONIC_DISTORSTION_RATE, &averge_total_phase_cur_thd);
    set_one_data(DEMUB_DATA_ID_PHASE_VOL_AVE_HARMONIC_DISTORSTION_RATE, &averge_total_phase_vol_thd);
    
}


//设置Do状态外部接口
void setDoValue(eDO_OUT do_index, int value)
{
    RETURN_IF_FAIL(do_index >= DO1_OUT && do_index < DO_END);

    if (FALSE == value)
        rt_pin_write(s_do_out_info[do_index].do_pin, PIN_LOW);
    else
        rt_pin_write(s_do_out_info[do_index].do_pin, PIN_HIGH);

    return;
}





void handle_para_set_msg(_rt_msg_t curr_msg)
{
    set_para_data();
}




void handle_period_data_cal_msg(_rt_msg_t curr_msg)
{
    calc_energy(NULL);
    calc_demand(NULL);
}

void send_period_data_calc_msg()
{
    pub_msg_to_thread(SAMPLE_PERIOD_CAL_DATA_MSG, NULL, 0);
}



int get_energy_pos()
{
    // 原理是电量单调递增的
    int choose_index = 0;
    energy_data_t energy_data[ENERGY_POS_NUM] = {0};
    if(SUCCESSFUL != handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char*)&energy_data, sizeof(energy_data), ENERGY_DATA_OFFSET))
    {
        LOG_E("%s:%d | read energy fail", __FUNCTION__ , __LINE__);
        return FAILURE;
    }

    if (energy_data[0].sys_active_energy > energy_data[1].sys_active_energy ||
        energy_data[0].sys_pos_reactive_energy > energy_data[1].sys_pos_reactive_energy ||
        abs(energy_data[0].sys_neg_reactive_energy) > abs(energy_data[1].sys_neg_reactive_energy) ||
        energy_data[0].sys_total_reactive_energy > energy_data[1].sys_total_reactive_energy) {
        choose_index = 0;
    } 
    else 
    {
        // 相等选择1
        choose_index = 1;
    }
    rt_memcpy_s(&g_energy_data_init, sizeof(g_energy_data_init), &energy_data[choose_index], sizeof(energy_data[choose_index]));

    return choose_index;
}


int init_energy_data()
{
    int i = 0;
    g_pos = get_energy_pos();

    if(g_pos == FAILURE)
    {
        g_pos = 0;
        return FAILURE;
    }

    if(crc_cal((unsigned char*)&g_energy_data_init, sizeof(g_energy_data_init) - 2) != g_energy_data_init.crc)
    {
        LOG_E("%s:%d | init_energy_data", __FUNCTION__ , __LINE__);
        rt_memset_s(&g_energy_data_init, sizeof(g_energy_data_init), 0x00, sizeof(g_energy_data_init));
    }

    for(i = 0; i < PHASE_NUM; i++)
    {
        set_one_data(DEMUB_DATA_ID_PHASE_ACTIVE_ENERGY + i, &g_energy_data_init.active_energy[i]);   // 相有功电能
        set_one_data(DEMUB_DATA_ID_PHASE_POSITIVE_REACTIVE_ENERGY + i, &g_energy_data_init.pos_reactive_energy[i]);  // 相正向无功电能
        set_one_data(DEMUB_DATA_ID_PHASE_NEGTIVE_REACTIVE_ENERGY + i, &g_energy_data_init.neg_reactive_energy[i]);   // 相负向无功电能
        set_one_data(DEMUB_DATA_ID_PHASE_TOTAL_REACTIVE_ENERGY + i, &g_energy_data_init.total_reactive_energy[i]);   // 相总无功电能
    }

    set_one_data(DEMUB_DATA_ID_TOTAL_ACTIVE_ENERGY, &g_energy_data_init.sys_active_energy);
    set_one_data(DEMUB_DATA_ID_PHASE_TOATL_POSITIVE_REACTIVE_ENERGY, &g_energy_data_init.sys_pos_reactive_energy);
    set_one_data(DEMUB_DATA_ID_PHASE_TOATL_NEGTIVE_REACTIVE_ENERGY, &g_energy_data_init.sys_neg_reactive_energy);
    set_one_data(DEMUB_DATA_ID_TOTAL_REACTIVE_ENERGY, &g_energy_data_init.sys_total_reactive_energy);
    return SUCCESSFUL;
}


int save_energy_data()
{
    energy_data_t energy_data = {0};
    int i = 0;

    // 有功电能
    for(i = 0; i < PHASE_NUM; i++)
    {
        get_one_data(DEMUB_DATA_ID_PHASE_ACTIVE_ENERGY + i, &energy_data.active_energy[i]);   // 相有功电能
        get_one_data(DEMUB_DATA_ID_PHASE_POSITIVE_REACTIVE_ENERGY + i, &energy_data.pos_reactive_energy[i]);  // 相正向无功电能
        get_one_data(DEMUB_DATA_ID_PHASE_NEGTIVE_REACTIVE_ENERGY + i, &energy_data.neg_reactive_energy[i]);   // 相负向无功电能
        get_one_data(DEMUB_DATA_ID_PHASE_TOTAL_REACTIVE_ENERGY + i, &energy_data.total_reactive_energy[i]);   // 相总无功电能
    }
    
    get_one_data(DEMUB_DATA_ID_TOTAL_ACTIVE_ENERGY, &energy_data.sys_active_energy);
    get_one_data(DEMUB_DATA_ID_PHASE_TOATL_POSITIVE_REACTIVE_ENERGY, &energy_data.sys_pos_reactive_energy);
    get_one_data(DEMUB_DATA_ID_PHASE_TOATL_NEGTIVE_REACTIVE_ENERGY, &energy_data.sys_neg_reactive_energy);
    get_one_data(DEMUB_DATA_ID_TOTAL_REACTIVE_ENERGY, &energy_data.sys_total_reactive_energy);

    g_pos = (g_pos + 1) % ENERGY_POS_NUM;
    energy_data.crc = crc_cal((unsigned char*)&energy_data, sizeof(energy_data) - 2);
    if(SUCCESSFUL != handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&energy_data, sizeof(energy_data), ENERGY_DATA_OFFSET + g_pos * sizeof(energy_data_t)))
    {
        LOG_E("%s:%d | write energy_data fail", __FUNCTION__ , __LINE__);
        return FAILURE;
    }
    return SUCCESSFUL;
}




void handle_clean_energy(_rt_msg_t curr_msg)
{
    energy_data_t energy_data[ENERGY_POS_NUM] = {0};
    int i = 0;
    int int_zero = 0.0;
    for(i = 0; i < PHASE_NUM; i++)
    {
        set_one_data(DEMUB_DATA_ID_PHASE_ACTIVE_ENERGY + i, &int_zero);   // 相有功电能
        set_one_data(DEMUB_DATA_ID_PHASE_POSITIVE_REACTIVE_ENERGY + i, &int_zero);  // 相正向无功电能
        set_one_data(DEMUB_DATA_ID_PHASE_NEGTIVE_REACTIVE_ENERGY + i, &int_zero);   // 相负向无功电能
        set_one_data(DEMUB_DATA_ID_PHASE_TOTAL_REACTIVE_ENERGY + i, &int_zero);   // 相总无功电能
    }

    set_one_data(DEMUB_DATA_ID_TOTAL_ACTIVE_ENERGY, &int_zero);
    set_one_data(DEMUB_DATA_ID_PHASE_TOATL_POSITIVE_REACTIVE_ENERGY, &int_zero);
    set_one_data(DEMUB_DATA_ID_PHASE_TOATL_NEGTIVE_REACTIVE_ENERGY, &int_zero);
    set_one_data(DEMUB_DATA_ID_TOTAL_REACTIVE_ENERGY, &int_zero);

    if(SUCCESSFUL != handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&energy_data, sizeof(energy_data), ENERGY_DATA_OFFSET))
    {
        LOG_E("%s:%d | write energy_data fail", __FUNCTION__ , __LINE__);
    }
}

