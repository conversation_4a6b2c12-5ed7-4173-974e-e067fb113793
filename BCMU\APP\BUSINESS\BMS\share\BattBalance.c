#include "BattBalance.h"
#include "sample.h"
#include "realAlarm.h"
#include "qtp.h"
#include "BalanceTimeStatistics.h"
#include "utils_rtthread_security_func.h"

static void StopBalance(T_HardwareParaStruct *pHardwarePara, T_BattDealInfoStruct *pBattDeal, T_BattResult *pBattOut);
static void BalanceTimeManagement(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal, T_BattResult *pBattOut);
static void BalanceJudgement(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal, T_BattResult *pBattOut);
static BOOLEAN BalanceCircuitJudgement(BYTE ucEqualCircuitFaultAlm, T_BattResult *pBattOut);
static BOOLEAN BalanceStartCondition(T_BattInfo *pBattIn, BYTE cellNum);
static void CellBalanceJudgement(T_BattDealInfoStruct *pBattDeal, BYTE cellNum, T_BattInfo *pBattIn);
static WORD CellBalVoltBitsCal(T_HardwareParaStruct *pHardwarePara, T_BattDealInfoStruct *pBattDeal);

/***************************************************************************
 * @brief    电池均衡管理主入口函数
 **************************************************************************/
void PassiveBalanceManagement(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal, T_BattResult *pBattOut)
{
    BalTimeStatistics(pHardwarePara, pBattIn);//均衡时间统计
    if (GetQtpBalanceCircCheckFlag() || GetNormalBalanceCircCheckFlag())
    {
        return;
    }

    if (IsSleep() 
#ifdef BALANCE_RESIS_TEMP_HIGH_PRT  // D121无均衡电阻温度高保护告警
    || IsExistBalanceResisTempHighPrt()
#endif
    )
    {
        StopBalance(pHardwarePara, pBattDeal, pBattOut);
        return;
    }

    TimerPlus(pBattDeal->wBalanceTimer, BALANCE_PEROID);

    if (pBattDeal->wBalanceTimer >= BALANCE_PEROID)
    {
        pBattDeal->wBalanceTimer = 0;
    }

    BalanceTimeManagement(pHardwarePara, pBattIn, pBattDeal, pBattOut);

    return;
}

/***************************************************************************
 * @brief    停止均衡
 **************************************************************************/
static void StopBalance(T_HardwareParaStruct *pHardwarePara, T_BattDealInfoStruct *pBattDeal, T_BattResult *pBattOut)
{
    BYTE i = 0;

    for (i = 0; i < pHardwarePara->ucCellVoltNum; i++)
    {
        pBattDeal->abBalanceStart[i] = False;
        pBattDeal->abBalanceCount[i] = 0;
    }

    pBattOut->ucCellEquSta = False;
    pBattOut->wCellBalVoltBits = 0;
    SetBattBalance(0);

    return;
}

/***************************************************************************
 * @brief    均衡开启时长控制管理
 **************************************************************************/
static void BalanceTimeManagement(T_HardwareParaStruct *pHardwarePara , T_BattInfo *pBattIn,  T_BattDealInfoStruct *pBattDeal , T_BattResult *pBattOut)
{
    if (pBattDeal->wBalanceTimer >= BALANCE_STOP_PEROID && pBattDeal->wBalanceTimer <= BALANCE_PEROID &&
        pBattOut->wCellBalVoltBits)
    {
        pBattDeal->ucStopBalanceCounter = 0;
        return;
    }

    if (pBattDeal->ucStopBalanceCounter <= BALANCE_DO_STOP)
    {
        pBattOut->wCellBalVoltBitsBak = 0;
        pBattDeal->ucStopBalanceCounter++;
        StopBalance(pHardwarePara, pBattDeal, pBattOut);
        return;
    }

    BalanceJudgement(pHardwarePara, pBattIn, pBattDeal, pBattOut);

    if (pBattOut->wCellBalVoltBitsBak != pBattOut->wCellBalVoltBits && (WORD)0 == pBattOut->wCellBalVoltBitsBak)
    {
        pBattDeal->wBalanceTimer = BALANCE_STOP_PEROID;
    }
    pBattOut->wCellBalVoltBitsBak = pBattOut->wCellBalVoltBits;

    return;
}

/***************************************************************************
 * @brief    均衡启动判断
 **************************************************************************/
static void BalanceJudgement(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal, T_BattResult *pBattOut)
{
    BYTE i;
    WORD wBalVal = 0; // 电芯均衡按位信号
    T_BCMAlarmStruct tBCMRealAlm;

    rt_memset_s(&tBCMRealAlm, sizeof(T_BCMAlarmStruct), 0, sizeof(T_BCMAlarmStruct));
    GetRealAlarm(BCM_ALARM_REAL, (BYTE *)&tBCMRealAlm); // 获取未屏蔽的告警

    if (BalanceCircuitJudgement(tBCMRealAlm.ucEqualCircuitFaultAlm, pBattOut) == False)
    {
        return;
    }

    for (i = 0; i < pHardwarePara->ucCellVoltNum; i++)
    {
        CellBalanceJudgement(pBattDeal, i, pBattIn);
    }

    wBalVal = CellBalVoltBitsCal(pHardwarePara, pBattDeal);
    pBattOut->ucCellEquSta = (wBalVal != (WORD)0) ? True : False;
    pBattOut->wCellBalVoltBits = wBalVal;
    SetBattBalance(wBalVal);
    pBattOut->wBalanceCounter++;

    return;
}

/***************************************************************************
 * @brief    单节电芯均衡判断
 **************************************************************************/
static void CellBalanceJudgement(T_BattDealInfoStruct *pBattDeal, BYTE cellNum, T_BattInfo *pBattIn)
{
    if (BalanceStartCondition(pBattIn, cellNum) == True)
    {
        TimerPlus(pBattDeal->abBalanceCount[cellNum], BALANCE_COUNTER);
    }
    else
    {
        pBattDeal->abBalanceCount[cellNum] = 0;
        pBattDeal->abBalanceStart[cellNum] = False;
    }
}

/***************************************************************************
 * @brief    电芯均衡按位信号计算
 * @return   计算得到的电芯均衡按位信号
 **************************************************************************/
static WORD CellBalVoltBitsCal(T_HardwareParaStruct *pHardwarePara, T_BattDealInfoStruct *pBattDeal)
{
    WORD wBalVal = 0; // 电芯均衡按位信号
    for (BYTE i = 0; i < pHardwarePara->ucCellVoltNum; i++)
    {
        if (TimeOut(pBattDeal->abBalanceCount[i], BALANCE_COUNTER))
        {
            pBattDeal->abBalanceStart[i] = True;
            wBalVal += (1 << i); // 折算为二进制按位信号
        }
    }
    return wBalVal;
}

/***************************************************************************
 * @brief    均衡电路故障判断
 * @param    {BYTE} ucEqualCircuitFaultAlm---均衡电路故障告警
 * @return   True---均衡电路正常 False---均衡电路故障
 **************************************************************************/
static BOOLEAN BalanceCircuitJudgement(BYTE ucEqualCircuitFaultAlm, T_BattResult *pBattOut)
{
    if (ucEqualCircuitFaultAlm)
    {
        pBattOut->ucCellEquSta = False;
        pBattOut->wCellBalVoltBits = 0;
        SetBattBalance(0);
        return False;
    }
    else
    {
        return True;
    }
}


/***************************************************************************
 * @brief    均衡开启条件
 * @param    {T_BattInfo} *pBattIn---电池状态信息
 * @param    {T_BCMAlarmStruct} *pAlarm---告警信息
 * @return   True---均衡应当开启 False---均衡无需开启
 **************************************************************************/
static BOOLEAN BalanceStartCondition(T_BattInfo *pBattIn, BYTE cellNum)
{
    if (pBattIn == NULL || cellNum >= CELL_VOL_NUM_MAX)
    {
        return False;
    }

    FLOAT fCellVoltDiff = pBattIn->tData.afCellVolt[cellNum] - pBattIn->tData.fCellVoltMin;

    if (pBattIn->tData.fCellVoltMin > 1.5f)
    {
        if (pBattIn->tData.afCellVolt[cellNum] > PLATFORM_CELL_VOLT_UPPER_LIMIT)//充电末期或充满时(任意单节>3.36V)电芯均衡
        {
            return (fCellVoltDiff > pBattIn->tPara.fEqulizationStartThreshold);
        }
        else if (pBattIn->tData.afCellVolt[cellNum] >= PLATFORM_CELL_VOLT_LOWER_LIMIT)//平台区(3.36V≥任意单节≥3.2V)电芯均衡
        {
            return (fCellVoltDiff > PLATFORM_AREA_START_BALANCE_VOLT);
        }
        else if (pBattIn->tData.afCellVolt[cellNum] >= START_BALANCE_CELL_VOLT_LOWER_LIMIT)//平台区之下(3.2>任意单节≥3.1V)电芯均衡
        {
            return (fCellVoltDiff > PLAT_LOWER_AREA_START_BALANCE_VOLT);
        }
    }

    return False;
}

