#include "circle_buff.h"
#include <rtdevice.h>
#include "utils_rtthread_security_func.h"
#include "data_type.h"

Static rt_mutex_t s_ptMutexUpdate = RT_NULL;

// 升级数据缓存数组
static T_AppDataSaveStruct s_atUpdateDataSave[MAX_UPDATE_DATA_CACHE_NUM] = {0};
Static int s_read_index = 0;
Static int s_write_index = 0;
Static int s_written_num = 0;

static unsigned char check_read_in_range(int num);
static unsigned int circle_read_one(T_AppDataSaveStruct* buff);
static unsigned int circle_write_one(T_AppDataSaveStruct* buff);
static unsigned char check_read_index_in_virtual_area_and_reset_index(void);

static unsigned char check_read_in_range(int num) {
    // 超过已写个数
    if (num > s_written_num || (s_read_index + num > s_write_index)) {
        return FALSE;
    }
    return TRUE;
}

static unsigned char check_write_in_range(int num) {
    // 超过写上限
    if (s_written_num + num > MAX_UPDATE_DATA_CACHE_NUM) {
        return FALSE;
    }
    return TRUE;
}

static unsigned char check_read_index_in_virtual_area_and_reset_index(void) {
    if (s_read_index >= MAX_UPDATE_DATA_CACHE_NUM) {
        s_read_index -= MAX_UPDATE_DATA_CACHE_NUM;
        s_write_index -= MAX_UPDATE_DATA_CACHE_NUM;
    }
    return TRUE;
}


static unsigned int circle_read_one(T_AppDataSaveStruct* buff) {
    if (NULL == buff || (s_read_index >= MAX_UPDATE_DATA_CACHE_VIRTURE_NUM)) {
        return FALSE;
    }

    if (s_read_index < MAX_UPDATE_DATA_CACHE_NUM) {
        rt_memcpy(buff, &s_atUpdateDataSave[s_read_index], sizeof(T_AppDataSaveStruct));
    } else {
        rt_memcpy(buff, &s_atUpdateDataSave[s_read_index - MAX_UPDATE_DATA_CACHE_NUM], sizeof(T_AppDataSaveStruct));
    }
		
    ++s_read_index;
    s_written_num = s_write_index - s_read_index;
	// rt_kprintf("circle_read_one s_read_index: %d, num=%d\r\n", s_read_index, s_written_num);
    check_read_index_in_virtual_area_and_reset_index();
    return TRUE;
}

static unsigned int circle_write_one(T_AppDataSaveStruct* buff) {
    if (NULL == buff || (s_write_index >= MAX_UPDATE_DATA_CACHE_VIRTURE_NUM)) {
        return FALSE;
    }

    if (s_write_index < MAX_UPDATE_DATA_CACHE_NUM) {
        rt_memcpy(&s_atUpdateDataSave[s_write_index], buff, sizeof(T_AppDataSaveStruct));
    } else {
        rt_memcpy(&s_atUpdateDataSave[s_write_index - MAX_UPDATE_DATA_CACHE_NUM], buff, sizeof(T_AppDataSaveStruct));
    }

    ++s_write_index;
    s_written_num = s_write_index - s_read_index;
	// rt_kprintf("circle_write_one s_write_index: %d, num=%d\r\n", s_write_index, s_written_num);
    return TRUE;
}

unsigned int read_circle_buff(T_AppDataSaveStruct* buff, int num) {
    int i  = 0;
    char ret = 0;

    if (NULL == buff || num < 0 || num > MAX_UPDATE_DATA_CACHE_NUM  || (NULL == s_ptMutexUpdate)) {
        return FALSE;
    }
    // 检查是否查过存量
    if (FALSE == check_read_in_range(num)) {
        return FALSE;
    }

    rt_mutex_take(s_ptMutexUpdate, RT_WAITING_FOREVER);
    for (i = 0; i < num; ++i) {
        ret = circle_read_one(&buff[i]);
        if (FALSE == ret) {
            rt_mutex_release(s_ptMutexUpdate);
            return FALSE;
        }
    }
    rt_mutex_release(s_ptMutexUpdate);

    return TRUE;
}

unsigned int write_circle_buff(T_AppDataSaveStruct* buff, int num) {
    int i = 0;
    char ret = 0;

    if (NULL == buff || num < 0 || num > MAX_UPDATE_DATA_CACHE_NUM || (NULL == s_ptMutexUpdate)) {
        return FALSE;
    }

    if (FALSE == check_write_in_range(num)) {
        return FALSE;
    }

    rt_mutex_take(s_ptMutexUpdate, RT_WAITING_FOREVER);
    for (i = 0; i < num; ++i) {
        ret = circle_write_one(&buff[i]);
        if (FALSE == ret) {
            rt_mutex_release(s_ptMutexUpdate);
            return FALSE;
        }
    }
    rt_mutex_release(s_ptMutexUpdate);

    return TRUE;
}

unsigned int init_circle_buff(void){
    int i = 0;
    do
    {
        s_ptMutexUpdate = rt_mutex_create("update", RT_IPC_FLAG_PRIO);
        i++;
        if(i > MAX_FAIL_NUM)
        {
            return FALSE;
        }
    }while(RT_NULL == s_ptMutexUpdate);

    return TRUE;
}

//升级流程结束（无论成功与否），调用此接口重置circle_buff
unsigned int reset_circle_buff(void){
    rt_memset_s(&s_atUpdateDataSave, sizeof(s_atUpdateDataSave), 0, sizeof(s_atUpdateDataSave));
    s_read_index = 0;
    s_write_index = 0;
    s_written_num = 0;
    return TRUE;
}  
