#include "common.h"
#include "BalanceTimeStatistics.h"
#include "utils_rtthread_security_func.h"
#ifdef __cplusplus
extern "C" {
#endif
Static CellBalance_Staticis s_tCellBalTime_Staticis = {0,};
Static rt_timer_t   waitCellBalSavetTimer ;
Static T_HisCellBalTimeDataStruct s_tHisCellBalTimeData = {0,};
Static BOOLEAN s_bcellsaveflag = FALSE ;
Static BOOLEAN s_bCellBalChanged = FALSE;
// Static BOOLEAN baltest1 = 0;
Static SHORT sBalPassDays = 0;
Static BYTE s_ucSelfDischFaultFlag = FALSE;
Static BYTE s_ucCapDCDRFaultFlag = FALSE;
extern rt_mutex_t s_ptMutexFile;
Static SHORT PassDays(time_t tTime );
Static T_HisCellBalCAPData       tCellBalTimeTotal_FirstHalfMonth;
Static T_HisCellBalCAPData       tCellBalTimeTotal_SecondHalfMonth;
Static BYTE s_tCellNumStatics = 0;
Static BOOLEAN readHalfMonthBalTime( T_HardwareParaStruct *pHardwarePara);
/***************************************************************************
 * @brief    电池均衡文件读写操作
 **************************************************************************/
Static T_HisRecord s_HisBalTimeRecord = {
    .wRecordSize = sizeof(T_BalTimeRecord),
    .pcFilename  = "/BalTimeRecord",
    .wMinSaveNum = CELLBAL_HISDATE_LENGTH,
    .wReadPoint  = {0},
};
Static rt_err_t ResetStartTime(void)
{
    s_tHisCellBalTimeData.tStartTime = time(RT_NULL);
    if(writeCellBalTimeInfo(&s_tHisCellBalTimeData))
    {
        return RT_EOK;
    }
    return RT_ERROR;
}
 rt_err_t DeleteBalTimeHisDataRecord(BYTE ucType)
{
    if(ucType == DELHISRECORD)
    {
        DelAllRecords( &s_HisBalTimeRecord );
        return RT_EOK; 
    }
    rt_memset_s(&tCellBalTimeTotal_FirstHalfMonth,sizeof(T_HisCellBalCAPData),0,sizeof(T_HisCellBalCAPData));
    rt_memset_s(&tCellBalTimeTotal_SecondHalfMonth,sizeof(T_HisCellBalCAPData),0,sizeof(T_HisCellBalCAPData));
    rt_memset_s(&s_tHisCellBalTimeData,sizeof(T_HisCellBalTimeDataStruct),0,sizeof(T_HisCellBalTimeDataStruct));
    rt_memset_s(&s_tCellBalTime_Staticis,sizeof(CellBalance_Staticis),0,sizeof(CellBalance_Staticis));
    deleteFile("/cellbalInfo");
    if(s_tHisCellBalTimeData.tStartTime == 0)
    {
        if(ResetStartTime() == RT_EOK)
        {
            return RT_EOK;
        }
        return RT_ERROR;
    }
    return RT_EOK; 
}
Static rt_err_t SaveBalTimeHisDataRecord(T_BalTimeRecord *tBalTimeRecord)
{
    T_HisRecord *pRecord = &s_HisBalTimeRecord;
    Static time_t s_tTimer = 0;
    if (tBalTimeRecord == NULL)
    {
        return RT_ERROR;
    }
    if ((tBalTimeRecord->tTime == 0)|| (tBalTimeRecord->tTime == s_tTimer)) // 该条历史数据保存过
    {
        return RT_ERROR;
    }
    s_tTimer = tBalTimeRecord->tTime;
    pRecord->pucData = (rt_uint8_t *)tBalTimeRecord;
    if(saveOneRecord(pRecord) == True)
    {
        return RT_EOK;
    }
    return RT_ERROR;
}
BOOLEAN ReadBalTimeOneDayRecord(T_BalTimeRecord *tBalTimeRecord,rt_uint16_t wReadPoint,rt_uint16_t offsetback , rt_uint16_t offsetforward )
{
     rt_uint32_t len = 0;
     rt_int32_t fd = 0;
     int ReadPoint = 0;
     ReadPoint = wReadPoint - offsetback-1;
    if(ReadPoint <= 0)
    {
        rt_memset_s(tBalTimeRecord, sizeof(T_BalTimeRecord), 0, sizeof(T_BalTimeRecord));
        return False;
    }
    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);

    fd = open("/BalTimeRecord", O_RDONLY );
    if (fd < 0)
    {
        rt_mutex_release(s_ptMutexFile);
        return False;
    }
    len = lseek(fd, ReadPoint* sizeof(T_BalTimeRecord), SEEK_SET);
    len = rt_read_s(fd, tBalTimeRecord, sizeof(T_BalTimeRecord), sizeof(T_BalTimeRecord));
    close(fd);
    rt_mutex_release(s_ptMutexFile);

    if (len == sizeof(T_BalTimeRecord))
    {
        return True;
    }
    return False;
}
BOOLEAN ReadBalTimeHisDataRecord(T_BalTimeRecord *tBalTimeRecord)
{
    s_HisBalTimeRecord.pucData = (rt_uint8_t *)tBalTimeRecord;
    if (readOneRecord(&s_HisBalTimeRecord , 0))
    {
        s_HisBalTimeRecord.wReadPoint[0]--;
        return True;
    }
    return False;
}
void MoveBalTimeHisDataPoint(rt_uint8_t ucIncNum)
{
    s_HisBalTimeRecord.wReadPoint[0] += ucIncNum;
    if (s_HisBalTimeRecord.wReadPoint[0] > s_HisBalTimeRecord.wSavedNum)
    {
        s_HisBalTimeRecord.wReadPoint[0] = s_HisBalTimeRecord.wSavedNum;
    }
    return;
}

BOOLEAN LastBalCapRecordJudge(void)
{
    if (s_HisBalTimeRecord.wReadPoint[0] == s_HisBalTimeRecord.wSavedNum)
    {
        return True;
    }

    return False;
}

void SetBalTimeHisDataPoint(WORD wHisDataReadPoint)
{
    s_HisBalTimeRecord.wReadPoint[0] = wHisDataReadPoint;
    return;
}


rt_err_t  readCellBalTimeInfo(T_HisCellBalTimeDataStruct *tHisCellBalTimeDate)
{
    rt_int32_t readCount = 0;
    readCount = readFile("/cellbalInfo", (BYTE *)tHisCellBalTimeDate, sizeof(T_HisCellBalTimeDataStruct));
    if (readCount != sizeof(T_HisCellBalTimeDataStruct) || CRC_Cal((BYTE*)tHisCellBalTimeDate, sizeof(T_HisCellBalTimeDataStruct)))
    {
        rt_memset_s(tHisCellBalTimeDate, sizeof(*tHisCellBalTimeDate), 0x00, sizeof(*tHisCellBalTimeDate));
    }
    return RT_EOK;
}

rt_err_t  writeCellBalTimeInfo(T_HisCellBalTimeDataStruct *tHisCellBalTimeDate)
{
    BYTE *p = (BYTE *)tHisCellBalTimeDate;	
    s_tHisCellBalTimeData.wReadPoint = s_HisBalTimeRecord.wReadPoint[0];
    tHisCellBalTimeDate->wCRC = CRC_Cal( p, offsetof(T_HisCellBalTimeDataStruct , wCRC));
    if(writeFile("/cellbalInfo", (BYTE *)tHisCellBalTimeDate, sizeof(T_HisCellBalTimeDataStruct)) == sizeof(T_HisCellBalTimeDataStruct))
    {
       return RT_EOK;
    }
    return RT_ERROR;
}

/***************************************************************************
 * @brief    电池均衡时间统计初始化
 **************************************************************************/
 rt_err_t StartTimeInit(void)
 {
    if(s_tHisCellBalTimeData.tStartTime == 0)
    {
        if(ResetStartTime() == RT_EOK)
        {
            return RT_EOK;
        }
        return RT_ERROR;
    }
    return RT_EOK;
 }

rt_err_t readHisBalTimeRecordInfo(void)
{    
    rt_int32_t  fd;
    s_HisBalTimeRecord.wReadPoint[0] = s_tHisCellBalTimeData.wReadPoint ;
    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);
    fd = open(s_HisBalTimeRecord.pcFilename, O_RDONLY | O_CREAT);
    if (fd < 0)
    {
        rt_mutex_release(s_ptMutexFile);
        return RT_ERROR;
    }
    s_HisBalTimeRecord.wSavedNum = lseek(fd, 0, SEEK_END) / s_HisBalTimeRecord.wRecordSize;
    close(fd);
    rt_mutex_release(s_ptMutexFile);
    return RT_EOK;
}
rt_err_t CellBalInit(T_HardwareParaStruct *pHardwarePara)
{

    readCellBalTimeInfo(&s_tHisCellBalTimeData);
    StartTimeInit();
    readHisBalTimeRecordInfo();
    readHalfMonthBalTime(pHardwarePara);
    s_tCellNumStatics = pHardwarePara->ucCellVoltNum;
    return RT_EOK;

}
/***************************************************************************
 * @brief    掉电保存
 **************************************************************************/
//  BOOLEAN SetCellSavePowerOffFlag( BOOLEAN Flag )
// {
//     s_tHisCellBalTimeData.tPowerOffTime = GetTimeStamp();
//     s_tHisCellBalTimeData.wReadPoint[0] = s_HisBalTimeRecord.wReadPoint[0];
//     s_bcellsaveflag = Flag;
//     return s_bcellsaveflag;
// }
 BOOLEAN GetCellSavePowerOffFlag(void)
{
   return s_bcellsaveflag;
}
Static SHORT PassDays(time_t tTime )
{
        time_t tCurrentTime = GetTimeStamp() ;
        SHORT sPassDays =0;
        int TimeDays = tCurrentTime-tTime;
        if(TimeDays >= 0)
        {
            sPassDays = TimeDays/SPD;
        }
        else
        {
            sPassDays = -RT_ERROR;
        }
        // BYTE ucPassDays = (TimeDays > 0) ? (TimeDays/SPD):0;     
        return   sPassDays;
}
/***************************************************************************
 * @brief    电池均衡存储
 **************************************************************************/
Static void CellBalSave(void *parameter)
{
    if(s_bCellBalChanged == TRUE)
    {
        s_bcellsaveflag = TRUE;
        s_bCellBalChanged = FALSE;
    }
}

 rt_err_t CircleCheckCellBalSave(void)
{
    if(GetApptestFlag() || GetQtptestFlag())
    {
        return RT_EBUSY;
    }
    if(waitCellBalSavetTimer == RT_NULL)
	{
        waitCellBalSavetTimer = rt_timer_create("cellbaltimer",CellBalSave, RT_NULL, 5000*60, RT_TIMER_FLAG_PERIODIC);
        if(waitCellBalSavetTimer != RT_NULL)
	    {
            rt_timer_start(waitCellBalSavetTimer);
        }
    }
    if(s_bcellsaveflag == TRUE)
    {
        s_bcellsaveflag = FALSE;
        writeCellBalTimeInfo(&s_tHisCellBalTimeData);
    }
    return RT_EOK;
}
/***************************************************************************
 * @brief    电池均衡容量统计（实时）
 **************************************************************************/
const float fCell_Duty_Cycle_Ti[CELL_VOL_NUM_MAX]={44.7 ,41.9, 91.7, 91.6, 91.6, 91.6, 91.6, 91.6, 91.6, 91.6, 91.6, 91.6, 88.7, 0, 88.7, 94.35 };
const float fCell_Duty_Cycle_MT = 35;
#define CELLBALCURRENT 200
rt_err_t CellBalCAP_CAL(T_HisCellBalCAPData *tHisCellBalCAPData,T_HisCellBalTimeData *tHisCellBalTimeData,T_HardwareParaStruct *pHardwarePara)
{
    float fDuty_Cycle = 0;
    ULONG ulBalTime = 0;
    BYTE ucMinvolNo = GetMinvolNo();
    if(Get_SampleChipType() == CHIP_MT9818)
    {
        for(int i = 0;i < pHardwarePara->ucCellVoltNum;i++)
        {    
            ulBalTime   = i < ucMinvolNo ? tHisCellBalTimeData->ulBalTime[i]:tHisCellBalTimeData->ulBalTime[i+1];
            tHisCellBalCAPData->fBalCAP[i]=tHisCellBalTimeData->ulBalTime[i]*fCell_Duty_Cycle_MT*CELLBALCURRENT/(100*3600);
        }
    }
    else if(Get_SampleChipType() == CHIP_BQ76952)
    {
        for(int i = 0;i < pHardwarePara->ucCellVoltNum;i++)
        {    
            fDuty_Cycle = i < ucMinvolNo ? fCell_Duty_Cycle_Ti[i]:fCell_Duty_Cycle_Ti[i+1];
            ulBalTime   = i < ucMinvolNo ? tHisCellBalTimeData->ulBalTime[i]:tHisCellBalTimeData->ulBalTime[i+1];
            tHisCellBalCAPData->fBalCAP[i] = ulBalTime*fDuty_Cycle*CELLBALCURRENT/(100*3600);
        }
    }
    else
    {
        return RT_ERROR ;
    }

   return RT_EOK;
}

/***************************************************************************
 * @brief    计算均衡容量（实时）
 **************************************************************************/
BOOLEAN GetRealBalCAP(T_HisCellBalCAPData *tHisCellBalCAPData,T_HisCellBalTimeDataStruct *tHisCellBalTimeData,
                      T_HardwareParaStruct *pHardwarePara,SHORT *wStaticTime)
{
    rt_err_t Ret = RT_ERROR;
    
    time_t tCurrentTime = GetTimeStamp() ;
    time_t TimeDays = tCurrentTime - tHisCellBalTimeData->tStartTime;
    if(sBalPassDays != 0)
    {
      return False;  
    }
    if(TimeDays >= 0)
    {
        *wStaticTime = TimeDays/60;   //当前统计时间以分钟计算
        if(*wStaticTime > 1440)
        {
           *wStaticTime = 1440; 
        }
    }
    else
    {
        return False;
    }

    Ret = CellBalCAP_CAL(tHisCellBalCAPData,&tHisCellBalTimeData->tCellBalTimeToday,pHardwarePara);
    if(Ret == RT_ERROR)
    {
        return False;
    }

    return True;
}


/***************************************************************************
 * @brief    获取各单体均衡时间（实时）
 **************************************************************************/
void GetCellBalTimeData(T_HisCellBalTimeDataStruct *tHisCellBalTimeData)
{
    rt_memcpy_s((BYTE *)tHisCellBalTimeData, sizeof(T_HisCellBalTimeDataStruct),(BYTE *)&s_tHisCellBalTimeData, sizeof(T_HisCellBalTimeDataStruct));
    return;
}

/***************************************************************************
 * @brief    电池均衡时间统计（实时）
 **************************************************************************/
Static rt_tick_t GetDeltaTime(rt_tick_t tnow , rt_tick_t tlast)
{
    if(tnow >= tlast)
    {
        return (tnow - tlast);
    }
    else
    {
        return (UINT32_MAX + tnow - tlast + 1);
    }
}

rt_err_t CellBalTime_Staticise(T_HardwareParaStruct *pHardwarePara, WORD wBalanceCode)
{
    if(GetApptestFlag() || GetQtptestFlag())
    {
         return RT_EBUSY;
    }
    for(int i = 0 ; i < pHardwarePara->ucCellVoltNum; i++)
    {
        s_tCellBalTime_Staticis.CellBalState[i].ucCurrentState = ((wBalanceCode>>i))&0X0001;
        if(s_tCellBalTime_Staticis.CellBalState[i].ucCurrentState == 1 && s_tCellBalTime_Staticis.CellBalState[i].ucLastState==0)
        {
            //开始计时
            s_tCellBalTime_Staticis.CellBalTime[i].ulInitTick = rt_tick_get();
        }
        else if(s_tCellBalTime_Staticis.CellBalState[i].ucCurrentState == 0 && s_tCellBalTime_Staticis.CellBalState[i].ucLastState==1)
        {
            //结束计时           
             s_tCellBalTime_Staticis.CellBalTime[i].ulBalTime += GetDeltaTime(rt_tick_get(),s_tCellBalTime_Staticis.CellBalTime[i].ulInitTick)/1000;//按秒计时
             s_tHisCellBalTimeData.tCellBalTimeToday.ulBalTime[i] += s_tCellBalTime_Staticis.CellBalTime[i].ulBalTime;
             s_tCellBalTime_Staticis.CellBalTime[i].ulBalTime = 0;
             s_tCellBalTime_Staticis.CellBalState[i].bStateChange = TRUE;
        }
        if(s_bcellsaveflag == 0 && s_tCellBalTime_Staticis.CellBalState[i].bStateChange == TRUE)//如果没有存储且
        {
           s_bCellBalChanged |= s_tCellBalTime_Staticis.CellBalState[i].bStateChange ; 
        }  
        s_tCellBalTime_Staticis.CellBalState[i].bStateChange = FALSE;        
        s_tCellBalTime_Staticis.CellBalState[i].ucLastState = s_tCellBalTime_Staticis.CellBalState[i].ucCurrentState;
    }
    return RT_EOK;
}

Static rt_err_t BalOneDayPassHisSave(T_HisCellBalTimeDataStruct *tHisCellBalTimeData,T_HardwareParaStruct *pHardwarePara)
{  

    T_BalTimeRecord tBalTimeRecord = {0,} ;
    BYTE uCnt = 0;
    tBalTimeRecord.tTime = tHisCellBalTimeData->tStartTime;//GetTimeStamp();
    CellBalCAP_CAL(&tBalTimeRecord.tCellBalCAPHis, &tHisCellBalTimeData->tCellBalTimeToday, pHardwarePara);
    while(SaveBalTimeHisDataRecord(&tBalTimeRecord) !=RT_EOK)
    {
         if(++uCnt > 3)
         {
            break;
         }
    }
    // s_HisBalTimeRecord.wReadPoint[0]++;
    return RT_EOK;
}
/***************************************************************************
 * @brief    电池均衡按日存储
 **************************************************************************/
Static BOOLEAN readHalfMonthBalTime( T_HardwareParaStruct *pHardwarePara)
{
   rt_uint16_t wReadPoint = s_HisBalTimeRecord.wSavedNum;

   T_BalTimeRecord tBalTimeRecordOneDay_Firsthalf={0,};
   T_BalTimeRecord tBalTimeRecordOneDay_Secondhalf={0,};
   rt_memset_s(&tCellBalTimeTotal_SecondHalfMonth,sizeof(T_HisCellBalTimeData ),0,sizeof(T_HisCellBalTimeData ));
   rt_memset_s(&tCellBalTimeTotal_FirstHalfMonth,sizeof(T_HisCellBalTimeData ),0,sizeof(T_HisCellBalTimeData ));
   for(int i = 0; i < 15; i++)
   {
     ReadBalTimeOneDayRecord(&tBalTimeRecordOneDay_Secondhalf, wReadPoint, i,0);
     ReadBalTimeOneDayRecord(&tBalTimeRecordOneDay_Firsthalf, wReadPoint, 15 + i,0 );
     for(int j = 0 ; j < pHardwarePara->ucCellVoltNum ; j++ )
     {
      tCellBalTimeTotal_SecondHalfMonth.fBalCAP[j] += tBalTimeRecordOneDay_Secondhalf.tCellBalCAPHis.fBalCAP[j];
      tCellBalTimeTotal_FirstHalfMonth.fBalCAP[j]  += tBalTimeRecordOneDay_Firsthalf.tCellBalCAPHis.fBalCAP[j];
     }
   }
   return TRUE;
}


Static T_TimeJump CalJumpTime(time_t tCurrentTime)
{
    T_TimeJump tJumpTime = {0,};
    Static time_t tLastTime = 0 ;
    time_t tJumpMins = 0 ;
    time_t tTimeJump = 0;
    if(tLastTime == 0)//用于区分设置时间或者长期关机引起的时间跳变 TRUE:开关机  False：设置时间
    {
        tLastTime = tCurrentTime ;
        return tJumpTime;
    }
    tTimeJump  = tCurrentTime - tLastTime;
    tJumpMins = tTimeJump/SPM;
    if(tJumpMins >10 || tJumpMins < -10)
    {
        tJumpTime.eTimeType = JumpExist;
        tJumpTime.tJumpTickdata = tTimeJump;
    }
    tLastTime = tCurrentTime ;
    return tJumpTime;
}

Static rt_err_t DayPassHandle(T_HisCellBalTimeDataStruct *tHisCellBalTimeData , T_HardwareParaStruct *pHardwarePara ,time_t tCurrentTime)
{
        if(++tHisCellBalTimeData->ucDateNum >= CELLBAL_ONEMONTH_DAYS)
        {
            if(++tHisCellBalTimeData->ucAreaNum  >= 6)
            {
                tHisCellBalTimeData->ucAreaNum = 0;
            }
            tHisCellBalTimeData->ucDateNum = 0 ;               
        }
        tHisCellBalTimeData->tStartTime += SPD;
        BalOneDayPassHisSave(tHisCellBalTimeData,pHardwarePara);
        rt_memset_s(&tHisCellBalTimeData->tCellBalTimeToday, sizeof(T_HisCellBalTimeData), 0, sizeof(T_HisCellBalTimeData));
        sBalPassDays--;
        if(sBalPassDays == 0 )
        {
           return RT_EOK;
        }
        return RT_EBUSY;
}
BOOLEAN baltest1 = 0;
Static rt_err_t BalOneDayPass(T_HisCellBalTimeDataStruct *tHisCellBalTimeData , T_HardwareParaStruct *pHardwarePara)
{
    T_TimeJump tJump_Time = {0};
    time_t tCurrentTime = GetTimeStamp() ;
    tJump_Time = CalJumpTime(tCurrentTime);//计算跳变时间
    if(tJump_Time.eTimeType == JumpExist)
    {
      tHisCellBalTimeData->tStartTime += tJump_Time.tJumpTickdata;
       writeCellBalTimeInfo( tHisCellBalTimeData);
       return RT_EOK;
    }
    if (sBalPassDays == 0 )
    {
        sBalPassDays = PassDays(tHisCellBalTimeData->tStartTime);
    }
    if(sBalPassDays > 0 )
    {
        if(sBalPassDays > CELLBAL_HISDATE_LENGTH*1.2)
        {
          sBalPassDays = CELLBAL_HISDATE_LENGTH*1.2;
        }
        if(DayPassHandle(tHisCellBalTimeData, pHardwarePara, tCurrentTime) == RT_EOK)
        {
          readHalfMonthBalTime( pHardwarePara);
          writeCellBalTimeInfo( tHisCellBalTimeData);
        }
    }
     return RT_EOK;
}

/***************************************************************************
 * @brief    均衡异常判断
 **************************************************************************/
Static rt_err_t findMaxAndMin(FLOAT afData[], BYTE ucNum, FLOAT *pMax, FLOAT *pMin)
{
    BYTE i;
    *pMin = afData[0];
    *pMax = afData[0];
    for(i = 0; i < ucNum; i++)
    {
            
        if(*pMin > afData[i])
            *pMin = afData[i];
        if(*pMax < afData[i])
            *pMax = afData[i];
    }
    return RT_EOK;
}

Static BOOLEAN JugeSelfDischFualt(T_HisCellBalCAPData *pHisCellBalCAPData,T_HardwareParaStruct *pHardwarePara,T_BattInfo *pBattIn)
{
    float fCellBalMax = 0,fCellBalMin = 0,fcellBalMid = 0 ;
    BYTE ucFaultSta = 0;
    findMaxAndMin(pHisCellBalCAPData->fBalCAP,pHardwarePara->ucCellVoltNum ,&fCellBalMax ,&fCellBalMin);
    fcellBalMid = GetCellMediumValue(pHisCellBalCAPData->fBalCAP , pHardwarePara->ucCellVoltNum);
    if((fcellBalMid  - fCellBalMin) > (pBattIn->tPara.fSelfDischgACR *pBattIn->tPara.wBattCap*0.5*CUR_A_TO_MA))
    {
        ucFaultSta |= 0x01;
    }
    if((fCellBalMax - fcellBalMid) > (pBattIn->tPara.fCapDCDR *pBattIn->tPara.wBattCap*0.5*CUR_A_TO_MA))
    {
        ucFaultSta |= 0x02;
    }
    return ucFaultSta;
}
BYTE GetSelfDischFaultFlag(void)
{
    return s_ucSelfDischFaultFlag;
}

BYTE GeCapDCDRFaultFlag(void)
{
    return s_ucCapDCDRFaultFlag ;
}
 rt_err_t BalTimeStatistics(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn)
{
    BYTE ucFaultType = 0;
    if(GetQtptestFlag())
    {
       return RT_EBUSY;
    }
    BalOneDayPass(&s_tHisCellBalTimeData,pHardwarePara);//日保存
    if(s_HisBalTimeRecord.wSavedNum > CELLBAL_ONEMONTH_DAYS)//存储记录大于30天才判断告警
    {
      ucFaultType = JugeSelfDischFualt(&tCellBalTimeTotal_FirstHalfMonth  , pHardwarePara ,pBattIn)& \
                    JugeSelfDischFualt(&tCellBalTimeTotal_SecondHalfMonth , pHardwarePara ,pBattIn);
    }
    s_ucSelfDischFaultFlag  = ucFaultType&0x01; 
    s_ucCapDCDRFaultFlag   = (ucFaultType>>1)&0x01;    
    return RT_EOK;
}

//获取均衡容量记录的总条数
rt_uint16_t GetBalanceTimeRecordNum(void)
{
    return s_HisBalTimeRecord.wSavedNum;

}

/***************************************************************************
 * @brief    设置均衡容量记录的读取点
 * @param    {WORD} wHisDataReadPoint---读取点
 **************************************************************************/
rt_uint16_t GetProtoBalanceCapacityPoint(record_north_protocol_e wNorthProtocolIndex)
{
    return s_HisBalTimeRecord.wReadPoint[wNorthProtocolIndex];
    
}

//协议读取一条均衡容量记录
void ReadBalanceCapacityRecordData(rt_uint16_t wNum,rt_uint8_t *ptHisData, record_north_protocol_e wNorthProtocolIndex)
{
    rt_uint16_t i = 0;
    BYTE j = 0;
    T_BalTimeRecord tBalTimeRecord;
    struct tm tTime;
    UINT32 sTemp = 0;
    for(i = 0;i < wNum; i++)
    {
        if(ReadBalTimeHisDataRecord(&tBalTimeRecord) == False)
        {
            break;
        }

        localtime_r(&tBalTimeRecord.tTime, &tTime);
        PutInt16ToBuff(ptHisData, tTime.tm_year + 1900);
        ptHisData += 2;

        *ptHisData++ = tTime.tm_mon + 1;
        *ptHisData++ = tTime.tm_mday;
        *ptHisData++ = tTime.tm_hour;
        *ptHisData++ = tTime.tm_min;
        *ptHisData++ = tTime.tm_sec;

        *ptHisData++ = s_tCellNumStatics;  //单体数量

        for(j = 0;j < s_tCellNumStatics;j++)
        {
            sTemp = (UINT32)(tBalTimeRecord.tCellBalCAPHis.fBalCAP[j]*100);
            sTemp = (UINT32)Int32ValuetoModbus(sTemp);
            rt_memcpy(ptHisData, (BYTE*)&sTemp, sizeof(sTemp));
            ptHisData += 4;
        }
    }

    return;
}
// T_HisCellBalTimeDataStruct tHisCellBalTimeData ;

// void Bal_test_show(int argc, char **argv)//shelltest
// {//shelltest
//     T_BalTimeRecord tBalTimeRecordOneDay1 = {0,};
//     baltest1 = 0;
//    if(!strcmp(argv[1], "1"))
//    {
//     rt_kprintf("day : %d\n",s_tHisCellBalTimeData.ucDateNum +s_tHisCellBalTimeData.ucAreaNum*30 +sBalPassDays);
//     rt_kprintf("record saved num : %d\n",    s_HisBalTimeRecord.wSavedNum);

//      for(int i = 0 ; i < 16;i++)
//      {
//         rt_kprintf("Today BAL NUM %d = %d\n",i,s_tHisCellBalTimeData.tCellBalTimeToday.ulBalTime[i]);
//      }
//       for(int a = 0 ; a < 16;a++)
//      {
//         rt_kprintf("Total SecondBAL NUM %d = %f\n",a,tCellBalTimeTotal_SecondHalfMonth.fBalCAP[a]);
//      }
//           for(int b = 0 ; b< 16;b++)
//      {

//         rt_kprintf("Total FirstBAL NUM %d = %f\n",b,tCellBalTimeTotal_FirstHalfMonth.fBalCAP[b]);
//      }
//    }
//     if(!strcmp(argv[1], "his"))
//     {


//     struct tm tTime;     
//     ReadBalTimeOneDayRecord(&tBalTimeRecordOneDay1,atoi(argv[2]),0 ,0);
//     time_t t = tBalTimeRecordOneDay1.tTime;
//     localtime_r(&t, &tTime);
//         rt_kprintf("%d:%d:%d\n%d:%d:%d\n",tTime.tm_year+1900,tTime.tm_mon+ 1,tTime.tm_mday,tTime.tm_hour,tTime.tm_min,tTime.tm_sec);
//     for(int i = 0 ; i < 16;i++)
//         {
//         rt_kprintf("Today BAL NUM %d = %f\n",i,tBalTimeRecordOneDay1.tCellBalCAPHis.fBalCAP[i]);

//      }
//    }
//     if(!strcmp(argv[1], "ch"))
//     {
//         BYTE channel = atoi(argv[2]);
//       s_tHisCellBalTimeData.tCellBalTimeToday.ulBalTime[channel]=atoi(argv[3]);
//       s_bCellBalChanged = 1;
//     }
//     if(!strcmp(argv[1], "day"))
//     {
//     sBalPassDays+=atoi(argv[2]);
//     baltest1 = 1;
//     rt_kprintf("day : %d\n",s_tHisCellBalTimeData.ucDateNum +s_tHisCellBalTimeData.ucAreaNum*30 +sBalPassDays);

//     }
//     if(!strcmp(argv[1], "del"))
//     {
//     DeleteBalTimeHisDataRecord(DELHISRECORD);
//     }

// }//shelltest
// MSH_CMD_EXPORT(Bal_test_show, setmode);//shelltest
#ifdef __cplusplus
 }
#endif
// #endif