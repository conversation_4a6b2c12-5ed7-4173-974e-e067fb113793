#include <rtthread.h>
#include <rtdevice.h>
#include <board.h>
#include <drv_common.h>
#include <math.h>
#include <string.h>
#include "sample.h"
#include "data_type.h"
#include "utils_thread.h"
#include "adc_ctrl.h"
#include "pwm_ctrl.h"
#include "hal_adc.h"
#include "pin_define.h"
#include "utils_server.h"
#include "pin_ctrl.h"

#define GPIO_PIN_SET   1
#define GPIO_PIN_RESET 0

/***********************  变量定义  ************************/
Static unsigned int   s_adc_data[ANALOG_COUNT+DIGITAL_COUNT][ADCCONVERTEDVALUES_SAMPLING_NUM];
Static unsigned int   s_adc_data_filter[ANALOG_COUNT+DIGITAL_COUNT];
Static unsigned int   s_digital_data[DIGITAL_COUNT];
Static unsigned int   s_analog_data[ANALOG_COUNT];

/*********************  静态函数原型定义  **********************/
Static void sample(void);
Static void data_sample(void);
Static unsigned int MedianFilter(unsigned int data[ADCCONVERTEDVALUES_SAMPLING_NUM],int size);

static msg_map sample_msg_map[] =
{
     {0,NULL},//临时添加解决编译问题
};

void* sample_init_sys(void *param){
	server_info_t *server_info = (server_info_t *)param;
    register_server_msg_map(sample_msg_map, server_info);
    humi_sensor_init();
    return NULL;
}

void sample_main(void* parameter)
{
    while (is_running(TRUE)){
        rt_thread_mdelay(400);
        sample();
    }
}

/* 采样数据 */
Static void sample(void) {
    data_sample();
}

Static void data_sample(void) {
    unsigned int i, j;
    for(i = 0; i < SAMPLE_CHANNL_END; i++) {
		for(j = 0; j < ADCCONVERTEDVALUES_SAMPLING_NUM; j++) //ADC采样次数 7
		{
            #ifdef USING_DEVICE_BSC_UIB03
			if (i == UIB03_HUM_IN) {
				s_adc_data[i][j] = hum_sample_func();
				continue;
			}
            if (i == UIB03_RLY_IN1) {
				s_adc_data[i][j] = rt_pin_read(PIN_RLY_IN1);
				continue;
			}
            if (i == UIB03_RLY_IN2) {
				s_adc_data[i][j] = rt_pin_read(PIN_RLY_IN2);
				continue;
			}
            #endif
			s_adc_data[i][j] = adc_get_data_by_channel(i);
		}
		s_adc_data_filter[i] = MedianFilter(s_adc_data[i],ADCCONVERTEDVALUES_SAMPLING_NUM);

    }
    for(j = 0;j < ANALOG_COUNT; j++)
	{
		s_analog_data[j] = s_adc_data_filter[j];
	}
    for(j = 0;j < DIGITAL_COUNT; j++)
	{
		s_digital_data[j] = s_adc_data_filter[j+ANALOG_COUNT];
	}
}

Static unsigned int MedianFilter(unsigned int data[ADCCONVERTEDVALUES_SAMPLING_NUM],int size)
{
    unsigned int max = 0, min = 0, sum = 0;
    if(size > 2)
    {
        max = data[0];
        min = max;
        sum = 0;
        for(int i=0; i<size; i++)
        {
            sum += data[i];
            if(data[i] > max)
            {
                max = data[i];
            }

            if(data[i] < min)
            {
                min = data[i];
            }
        }

        sum = sum - max -min;
        return sum / (size-2);
    }
    return 0;
}

// 获取模拟量数据
unsigned short get_analog_data(int *data, unsigned short data_size) {
    unsigned short real_size = 0;
	if(data_size < sizeof(s_analog_data)) {
		real_size = data_size;
	} else {
		real_size = sizeof(s_analog_data);
	}
	rt_memcpy(data, s_analog_data, real_size);
    return real_size/sizeof(unsigned int);
}

// 获取数字量数据
unsigned short get_digital_data(int *data, unsigned short data_size) {
    unsigned short real_size = 0;

	if(data_size < sizeof(s_digital_data)) {
		real_size = data_size;
	} else {
		real_size = sizeof(s_digital_data);
	}
	rt_memcpy(data, s_digital_data, real_size);

    return real_size/sizeof(unsigned int);
}

