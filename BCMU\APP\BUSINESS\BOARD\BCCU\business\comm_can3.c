﻿#include <rtthread.h>
#include <rtdevice.h>
#include <math.h>
#include <string.h>
#include "comm_can3.h"
#include "msg.h"
#include "utils_server.h"
#include "utils_thread.h"
#include "utils_math.h"
#include "data_type.h"
#include "device_type.h"
#include "exchange_data_info.h"
#include "utils_rtthread_security_func.h"
#include "utils_data_transmission.h"
#include "sps.h"
#include "cmd.h"
#include "partition_def.h"
#include "storage.h"
#include "protocol_bottom_comm.h"
#include "linklayer.h"

Static dev_type_t s_dev_north_can = {0};
Static cmd_buf_t s_cmd_buf = {0};
Static dev_inst_t* s_dev_north = NULL;
Static unsigned int s_emergency_timer = 0;
Static unsigned int s_common_timer = 0;
Static bccu_dido_t s_bccu_dido = {0};
Static int s_address_compete_timer = 0;

Static msg_map sample_msg_map[] =
{
     {0,NULL},
};

Static int check_send_controller_emergency_data(unsigned short index);
Static int process_message(void);
Static int deal_common_data_periodic_send(void);
Static int deal_emergency_data_periodic_send(void);
Static int send_battery_alarm(unsigned short index, alarm_data_t alarm_data);
Static int send_controller_alarm(unsigned short index, alarm_data_t alarm_data);
Static int deal_ctrl_contactor_cmd(unsigned char* data, unsigned int data_len, char flag);
Static int deal_ctrl_battery_cmd(unsigned char* data, unsigned int data_len, char flag);
Static int parse_dido_compete_info(unsigned char* data, unsigned int data_len, char flag);
Static unsigned char get_bccu_di_mode(void);
Static int set_bccu_do_mode(unsigned char do_mode);
Static int send_dido_compete_frame(void);
Static int deal_dido_address(void);
Static unsigned char get_dido_address(void);
Static int save_dido_address(unsigned char data);
Static int deal_address_compete(void);
Static unsigned char get_dst_address(void);

Static dev_type_t* init_dev_north_can(void) {
    s_dev_north_can.id = DEV_BCCU_NORTH_CAN;
    s_dev_north_can.dev_num = 1;
    s_dev_north_can.link_inst_id = LINK_BCCU_CAN3;
    s_dev_north_can.recv_buff_len = CAN3_R_BUFF_LEN;
    s_dev_north_can.send_buff_len = CAN3_S_BUFF_LEN;
    s_dev_north_can.dev_code = BOTTOM_SCU_TYPE;
    return &s_dev_north_can;
}

Static dev_init_t s_dev_init_tab[] = {
    {DEV_BCCU_NORTH_CAN, init_dev_north_can, NULL, NULL},
};

Static exchange_data_handle_t s_short_frm_handle[] =
{
    {FUNC_CONTROLLER_CONTROL, deal_ctrl_contactor_cmd, FALSE},
    {FUNC_BATTERY_CONTROL, deal_ctrl_battery_cmd, FALSE},
    {FUNC_BCCU_DIDO_COMPETE, parse_dido_compete_info, FALSE},
};

Static struct rt_can_filter_item can_filter_item[CAN3_HEAD_FILTER_NUM] = {
    RT_CAN_FILTER_ITEM_INIT(0x10000000, 1, 0, 1, 0x10000000), /* std,match ID:0x100~0x1ff，hdr 为 - 1，设置默认过滤表 */
    RT_CAN_FILTER_ITEM_INIT(0x10000000, 1, 0, 1, 0x10000000),
};

Static int reset_can_filter(link_inst_t* link_inst)
{
    RETURN_VAL_IF_FAIL(link_inst != NULL, FAILURE);

    link_can_filter can_filter_table;
    can_header_t filter_head[CAN3_HEAD_FILTER_NUM];

    rt_memset_s(filter_head, sizeof(filter_head), 0, sizeof(filter_head));

    filter_head[0].d_addr = 0; // 广播
    filter_head[1].d_addr = get_host_addr(); // 本机地址
    for (unsigned char i = 0; i < CAN3_HEAD_FILTER_NUM; i++)
    {
        filter_head[i].d_dev = BOTTOM_BCCU_TYPE;
        can_filter_item[i].id = filter_head[i].d_addr + (filter_head[i].d_dev << 7);
        can_filter_item[i].mask = 0x3FFF; // ID掩码：0 表示对应的位不关心，1 表示对应的位必须匹配
    }

    can_filter_table.can_filter_num = sizeof(can_filter_item)/sizeof(can_filter_item[0]);
    can_filter_table.can_filter_items = can_filter_item;

    can_dev_set(link_inst, &can_filter_table);

    return SUCCESSFUL;
}


void* CAN3_comm_init(void *param)
{
    server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(sample_msg_map) / sizeof(msg_map);
    register_server_msg_map(sample_msg_map, server_info);

    init_dev_tab(s_dev_init_tab, sizeof(s_dev_init_tab) / sizeof(s_dev_init_tab[0]));
    s_dev_north = init_can3_common();
    reset_can_filter(s_dev_north->dev_type->link_inst);

    s_bccu_dido.dido_address = get_dido_address();
    set_host_addr(s_bccu_dido.dido_address);

    set_bccu_do_mode(PIN_HIGH);
    s_bccu_dido.dido_compete_flag = TRUE;
    s_bccu_dido.dido_comfirm_address = 0;

    return s_dev_north;
}

Static int process_message(void)
{
    return deal_receviced_can_short_frame(s_dev_north, s_short_frm_handle, sizeof(s_short_frm_handle)/sizeof(&s_short_frm_handle[0]));
}

void process_CAN3_comm(void* parameter)
{
    while (is_running(TRUE))
    {
        if (RT_EOK == rt_sem_take(&(s_dev_north->dev_type->link_inst->rx_sem), NORTH_CAN3_DELAY)) {
            if (SUCCESSFUL == linker_recv(s_dev_north, &s_cmd_buf)) {
                process_message();
            }
            deal_address_compete();
            continue;
        }
        else
        {
            deal_emergency_data_periodic_send();
            deal_common_data_periodic_send();
        }

        deal_dido_address();
    }
}


Static int deal_emergency_data_periodic_send(void)
{
    TimerPlus(s_emergency_timer, EMERGENCY_PERIODIC);
    if (TimeOut(s_emergency_timer, EMERGENCY_PERIODIC))
    {
        s_emergency_timer = 0;
        check_send_controller_emergency_data(FIRE_LEVEL_1_INDEX);
        check_send_controller_emergency_data(FIRE_LEVEL_2_INDEX);
        check_send_controller_emergency_data(FIRE_LEVEL_3_INDEX);
    }
    return SUCCESSFUL;
}

Static int check_send_controller_emergency_data(unsigned short index)
{
    alarm_data_t alarm_data = {0};

    alarm_data = get_controller_alarm_data(index);
    if (TRUE == alarm_data.alm_val)
    {
        send_controller_alarm(index, alarm_data);
    }
    return SUCCESSFUL;
}

Static int send_controller_alarm(unsigned short index, alarm_data_t alarm_data)
{
    unsigned char frm_data[7] = {0};

    pack_controller_alarm_can_frm(alarm_data, index + 1, frm_data, sizeof(frm_data));
    can3_linklayer_send(BOTTOM_SCU_TYPE, frm_data, sizeof(frm_data));

    return SUCCESSFUL;
}

Static int send_battery_alarm(unsigned short index, alarm_data_t alarm_data)
{
    unsigned char frm_data[7] = {0};

    pack_battery_alarm_can_frm(alarm_data, index + 1, frm_data, sizeof(frm_data));
    can3_linklayer_send(BOTTOM_SCU_TYPE, frm_data, sizeof(frm_data));

    return SUCCESSFUL;
}

Static int deal_common_data_periodic_send(void)
{
    unsigned short i = 0;
    alarm_data_t alarm_data = {0};

    TimerPlus(s_common_timer, COMMON_PERIODIC);
    if (TimeOut(s_common_timer, COMMON_PERIODIC))
    {
        s_common_timer = 0;

        for (i = 0; i < CONTROLLER_ALARM_MAX; i++)
        {
            alarm_data = get_controller_alarm_data(i);
            send_controller_alarm(i, alarm_data);
        }

        for (i = 0; i < BATTERY_ALARM_MAX; i++)
        {
            alarm_data = get_battery_alarm_data(i);
            send_battery_alarm(i, alarm_data);
        }
    }
    return SUCCESSFUL;
}

Static int deal_ctrl_contactor_cmd(unsigned char* data, unsigned int data_len, char flag)
{
    cmd_buf_t *cmd_buf = &s_cmd_buf;

    RETURN_VAL_IF_FAIL(cmd_buf->buf[0] == FUNC_CONTROLLER_CONTROL, FAILURE);

    unsigned char cmd = cmd_buf->buf[2];
    switch(cmd)
    {
        case CMD_CONTACTOR_DISCONNECTION:
            rt_kprintf("deal_ctrl_contactor_cmd: CMD_CONTACTOR_DISCONNECTION\n");
            //todo 断开操作记录
            break;
        case CMD_CONTACTOR_CONNECTION:
            rt_kprintf("deal_ctrl_contactor_cmd: CMD_CONTACTOR_CONNECTION\n");
            //todo连接操作记录
            break;
        case CMD_CTRL_ADDRESS_COMPETE:
            if (get_dst_address() == 0)
            {//仅响应广播命令
                s_bccu_dido.dido_address = 1;
                s_bccu_dido.dido_comfirm_address = 0;
                set_host_addr(s_bccu_dido.dido_address);
                reset_can_filter(s_dev_north->dev_type->link_inst);
            }
            break;
        default:
            break;
    }
    return SUCCESSFUL;
}

Static int deal_ctrl_battery_cmd(unsigned char* data, unsigned int data_len, char flag)
{
    cmd_buf_t *cmd_buf = &s_cmd_buf;

    RETURN_VAL_IF_FAIL(cmd_buf->buf[0] == FUNC_BATTERY_CONTROL, FAILURE);

    can_linklayer_send(cmd_buf->buf[1],&cmd_buf->buf[0],cmd_buf->buflen);

    //todo 电池控制命令记录
    return SUCCESSFUL;
}

Static int parse_dido_compete_info(unsigned char* data, unsigned int data_len, char flag)
{
    if((data == NULL) || (data_len != DATA_LEN_OF_FRAME))
    {
        return FAILURE;
    }

    s_address_compete_timer = 0;

    s_bccu_dido.dido_comfirm_address = data[DIDO_COMFIRM_ADDR_IN_FRAME];

    return SUCCESSFUL;
}

Static int send_dido_compete_frame(void)
{
    unsigned char frm_data[7] = {0};
    link_inst_t* link_inst = s_dev_north->dev_type->link_inst;

    link_inst->r_link_addr = 0;

    frm_data[FUNC_INDEX_IN_FRAME] = FUNC_BCCU_DIDO_COMPETE;
    frm_data[DIDO_COMFIRM_ADDR_IN_FRAME] = s_bccu_dido.dido_comfirm_address;

    can3_linklayer_send(BOTTOM_BCCU_TYPE, frm_data, sizeof(frm_data));

    return SUCCESSFUL;
}

Static int deal_dido_address(void)
{
    TimerPlus(s_address_compete_timer, ADDR_COMPETE_END_TIME);
    if (TimeOut(s_address_compete_timer, ADDR_COMPETE_END_TIME))
    { // 结束竞争
        s_bccu_dido.dido_compete_flag = FALSE;
        set_bccu_do_mode(PIN_HIGH);
        s_bccu_dido.do_active_finish = FALSE;
        save_dido_address(s_bccu_dido.dido_address);
        return SUCCESSFUL;
    }

    if (s_bccu_dido.dido_comfirm_address == 0 && s_bccu_dido.dido_address != 0)
    { // 地址冲突后重置DIDO
        set_bccu_do_mode(PIN_HIGH);
        s_bccu_dido.do_active_finish = FALSE;
    }

    if (get_bccu_di_mode() == PIN_LOW && s_bccu_dido.do_active_finish == FALSE)
    { // 菊花链地址分配
        s_bccu_dido.dido_address = s_bccu_dido.dido_comfirm_address + 1;
        s_bccu_dido.dido_comfirm_address = s_bccu_dido.dido_address;
        set_host_addr(s_bccu_dido.dido_address);
        reset_can_filter(s_dev_north->dev_type->link_inst);

        send_dido_compete_frame();
        // todo ：时序判断
        set_bccu_do_mode(PIN_LOW);
        s_bccu_dido.do_active_finish = TRUE;
    }

    return SUCCESSFUL;
}

Static int deal_address_compete(void)
{
    link_inst_t* link_inst = s_dev_north->dev_type->link_inst;

    if (link_inst->r_link_addr == s_bccu_dido.dido_address)
    { // 存在地址冲突
        s_bccu_dido.dido_compete_flag = TRUE;
        s_bccu_dido.dido_comfirm_address = 0;
        s_address_compete_timer = 0;
        send_dido_compete_frame();
    }

    return SUCCESSFUL;
}

Static int set_bccu_do_mode(unsigned char do_mode)
{
    if (do_mode !=PIN_LOW && do_mode != PIN_HIGH)
    {
        return FAILURE;
    }

    s_bccu_dido.do_mode = do_mode;
    // rt_pin_write(DO_PIN, do_mode);
    return SUCCESSFUL;
}

Static unsigned char get_bccu_di_mode(void)
{
    // s_bccu_dido.di_mode = rt_pin_read(DI_PIN);
    return s_bccu_dido.di_mode;
}

Static int save_dido_address(unsigned char data)
{
    part_data_t part_data = {0};
    part_data.buff = &data;
    part_data.len = sizeof(data);
    part_data.offset = 0;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), "%s", DIDO_FILE);
    if (SUCCESSFUL != storage_process(&part_data, write_opr))
    {
        return FAILURE;
    }
    return SUCCESSFUL;
}

Static unsigned char get_dido_address(void)
{
    unsigned char saved_address = 0;
    part_data_t part_data = { 0 };
    part_data.buff = &saved_address;
    part_data.len = sizeof(saved_address);
    part_data.offset = 0;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), "%s", DIDO_FILE);
    if (SUCCESSFUL != storage_process(&part_data, read_opr))
    {
        saved_address = 0;
    }

    return saved_address;
}

Static unsigned char get_dst_address(void)
{
    link_inst_t* link_inst = s_dev_north->dev_type->link_inst;
    return link_inst->s_link_addr;
}
