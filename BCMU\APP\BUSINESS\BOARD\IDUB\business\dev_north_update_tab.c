#include "dev_north_update_tab.h"
#include "cmd.h"
#include "linklayer.h"
#include "protocol_layer.h"

static bottom_comm_cmd_head_t cmd_req[] = {
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATA_AFT_TRIG_FRAME,   CMD_APPEND_UPDATE_REQ,                       BOTTOM_RTN_APP_NOACK},   //0
    {BOTTOM_PROTO_TYPE_DOWN, CMD_TRAN_AFT_DATA_FRAME,     CMD_APPEND_NONE,                             BOTTOM_RTN_APP_NOACK},   //1
};

static bottom_comm_cmd_head_t cmd_ack[] = {
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATA_AFT_TRIG_FRAME,   CMD_APPEND_UPDATE_ACK,                       BOTTOM_RTN_APP_NOACK},   //0
    {BOTTOM_PROTO_TYPE_DOWN, CMD_TRAN_AFT_DATA_FRAME,     CMD_APPEND_NONE,                             BOTTOM_RTN_APP_NOACK},   //1
};

/* 命令总表,必须以空字符串""结束 */
static cmd_t no_poll_cmd_tab[] = {
    {UPDATE_TRIG_FRAME,             CMD_PASSIVE,  &cmd_req[0], &cmd_ack[0], sizeof(bottom_comm_cmd_head_t), },
    {TRAN_DATA_FRAME,               CMD_PASSIVE,  &cmd_req[1], &cmd_ack[1], sizeof(bottom_comm_cmd_head_t), },
    {0},
};

static dev_type_t s_idub_north_update_dev = {
    DEV_NORTH_IDUB, 1, PROTOCOL_BOTTOM_COMM, LINK_NORTH_IDUB, R_BUFF_LEN_1024, S_BUFF_LEN_1024, 0, no_poll_cmd_tab, NULL
};

dev_type_t* init_idub_dev_north_update(void) {
    return &s_idub_north_update_dev;
}