
#ifndef _FLASH_H_
#define _FLASH_H_
#ifdef __cplusplus
extern "C" {
#endif

#include "board.h"
#include <rtthread.h>
#include <rtdevice.h>
#include "stdint.h"

#define GD32F4_FLASH_BASE              (0x08000000UL)
#define GD32F4_FLASH_SIZE              (0x00100000UL)
#define GD32F4_FLASH_END_ADDR          (0x08100000UL)

#define FLAG_PROGRAM                  0x08007200 //升级标记
#define USER_FLASH_END_ADDRESS        0x080FFFFF
#define APPLICATION_ADDRESS           0x08008000
#define BACKUP_FLASH_START            0x00001000 //程序备份放在NORFLASH
#define UPDATEINFO_STATRT             0x00000000
#define NORFLASH_APP_START            0x00101000 //norflash的起始地址处

#define FILE_NAME_LEN 40
#define FILE_TIME_LEN 20
#define MAX_PACKET_NUM  2944    //升级包最大帧数

#define FLAG_IAP       0x55     //待升级
#define FLAG_CAN_IAP   0x56     //待升级CAN
#define FLAG_APP       0x66     //升级结束
#define FLAG_BACKUP    0x88     //运行正常待备份
#define FLAG_OK        0x99     //已备份

typedef struct
{
    uint16_t    wDataLenPerFrame;
    uint16_t    wTotalFrameNum;
    uint32_t    ulTotalFileLength;
    uint8_t     acFileName[FILE_NAME_LEN];
    uint8_t     acFileTime[FILE_TIME_LEN];
    uint16_t    wFileCheck;
    uint16_t    wResv;
} T_FileAttrStruct;

typedef struct
{
    T_FileAttrStruct tFileAttr;
    uint8_t  ucFlag;
    uint8_t  ucUpdateAddr;
    uint16_t wCounter;
    uint32_t wBaudRate;
    uint16_t wBackupFrameNum;
    uint16_t wCrc;
} T_FileManageStruct;

#ifdef __cplusplus
}
#endif     
#endif

