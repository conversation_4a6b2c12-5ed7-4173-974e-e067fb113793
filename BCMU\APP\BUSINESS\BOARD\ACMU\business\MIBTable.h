/**
 * @file     MIBTable.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2025-06-13
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */

#ifndef _MIBTable_H
#define _MIBTable_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "rtdef.h"
#include "data_type.h"
#include "para_struct_define_in.h"
 #include "para_manage.h"

// MIB库节点结构体定义
/************************************************************
** 结构名: MIBTable_ParaNode
** 描  述: 参数属性数据结构定义(表征一个参数量的属性)
**************************************************************/
typedef struct{
	unsigned short	wParaOffsetId;	// 参数偏移ID，用于set_one_para
	unsigned short	wParaId;	// 参数ID，用于恢复默认参数
	unsigned char	ucID1;		// ID1
	unsigned char	ucID2;		// ID2
	unsigned char	ucIDNumber;	// 用于标志ID1和ID2相同的数量(如有三组电池时，电池容量ID就有3个)(当ID2=0x00时表示ID2数量)
	unsigned char	ucDataType;	// 参量数据类型
	unsigned char	ucDataLen;	// 参量字节长度
	unsigned char	ucParaType;	// 参数类型 
    char scStep;                // 参量步长值
    char* ptrUnit;              // 参数单位
	char	scPrecision;		// 参量数据精度，小数点后几位数,如1表示小数点后1位。
	int	iDefaultVal;		// 参量缺省值
	int	iMinVal;		// 参量最小值
	int	iMaxVal;		// 参量最大值
	unsigned char	bSCAccess;	// 用于标志此参数后台能否访问,TRUE:后台可以访问,FALSE:后台不能访问
}MIBTable_ParaNode;

/************************************************************
** 结构名: MIBTable_ParaInstance
** 描  述: 参数实例数据结构定义(表征一个参数量的属性)
**************************************************************/
typedef struct{
	MIBTable_ParaNode tParaNode;
	unsigned int wZkSn;			// 参数在字库中的偏移量
	unsigned int wOffset;		// 参数在参数结构体中的地址偏移量
}MIBTable_ParaInstance;

typedef struct{
    unsigned short alarm_id;     
    unsigned char id1;
    unsigned char id2;
    unsigned char number;
}alarm_map_t;

typedef struct{
	unsigned char	ucID1;			// ID1
	unsigned char	ucID2;			// ID2
	unsigned char	ucIDNumber;	// ID1和ID2相同的数量(如有三个整流器时，整流器在位信息ID就有3个),另当ID2=0x00时，表示ID2总数
}MIB_AlarmDataNode;

/************************************************************
** 结构名: MIB_CTRLNode
** 描  述: 控制量属性数据结构定义(表征一个控制量的属性)
**************************************************************/
typedef struct{
	unsigned char	ucID1;			// ID1
	unsigned char	ucID2;			// ID2
	unsigned char	ucIDNumber;	// ID1和ID2相同的数量(如有三个整流器时，整流器在位信息ID就有3个),另当ID2=0x00时，表示ID2总数
}MIB_CTRLNode;

/************************************************************
** 结构名: MIB_CTRLInstanceNode
** 描  述: 控制量实例属性数据结构定义(表征一个控制量的属性)
**************************************************************/
typedef struct{
	MIB_CTRLNode tNode;
	unsigned char ucZkSn;		// 在字库中的偏移量
}MIB_CTRLInstanceNode;

MIBTable_ParaInstance* GetParaInstance( unsigned char ucID1, unsigned char ucID2, unsigned char ucIndex );
MIBTable_ParaInstance* GetRelayAlmParaInstance( unsigned char ucID1, unsigned char ucID2, unsigned char ucIndex, unsigned char relayidx);
int GetParaNode( unsigned char ucID1, unsigned char ucID2, MIBTable_ParaInstance* inst);
int GetRelayAlmParaNode( unsigned char ucID1, unsigned char ucID2, MIBTable_ParaInstance* inst, unsigned char relayidx);
MIBTable_ParaNode *GetParaNodeByParaId(unsigned short wOffsetParaId);
MIBTable_ParaNode* GetParaNodeByType(unsigned short wParaId, unsigned char uctype);
unsigned short GetParaOffset( unsigned char ucID1, unsigned char ucID2, unsigned char ucIDIndex );
unsigned short GetInRelayAlmParaOffset(unsigned char ucID1, unsigned char ucID2, unsigned char ucIDIndex, unsigned char ucinrelayIdx);
int convert_gui_to_raw(void *pGuiValue, int guiType, int rawType, unsigned char precision, u_value *pRawValue);
int get_exclude_para_ids_by_type(unsigned char para_type, unsigned short* out_id_list, int max_num);
int get_alarm_sn(unsigned short alarm_id, alarm_map_t** out_map);
const MIB_AlarmDataNode * GetAlarmNode(unsigned char ucAlarmSn);
MIB_CTRLInstanceNode * GetCTRLInstance( unsigned char ucID1, unsigned char ucID2 );
unsigned char GetAlarmGradeZkSn( unsigned char ucID1, unsigned char ucID2 );
unsigned char GetAlmOutRlyZkSn( unsigned char ucID1, unsigned char ucID2 );
unsigned char init_mib_table_para_from_dic(void);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _MIBTable_H

