#include "fileSys.h"
#include "common.h"
#include "hisdata.h"
#include "realAlarm.h"
#include "comm.h"
#include "fal.h"
#include "dfs_fs.h"
#include "rtdef.h"
#include "spi_flash.h"
#include "sfud.h"
#include "sfud_def.h"
#include "CommCan.h"
#include <dfs.h>
#include <dfs_file.h>
#include "usart.h"
#include "wireless.h"
#include "circle_buff.h"
#include "update_handle.h"
#include "prtclDL.h"
#include "pdt_version.h"
#include "utils_rtthread_security_func.h"
#include "led.h"
#include "prtclWireless.h"
#include <stddef.h>

#define READ_CRC_LEN 1024


extern char *GetRcvFileName(void);  // TODO: 确认分层后去除
static char s_aucBackupFileRW[CAN_BACKUP_DATA_BUFF] = {0};  // 读写互备份文件缓存
Static BOOLEAN s_ucEraseFLAG = False;
Static T_AppDataSaveStruct s_aucUpdateBuff = {0};
static BYTE s_wCrcBuf[READ_CRC_LEN];

Static BOOLEAN DealBMSUpdateData(T_AppDataSaveStruct* Buff);
Static BOOLEAN DealNorflashErase(void);
Static sfud_err Erase_norflash_for_update(const sfud_flash *flash, uint32_t earse_addr, size_t size);
Static sfud_err write_circlebuff_norflash(const sfud_flash *flash, uint32_t addr, size_t size, const uint8_t *data);
Static void saveToNewFile(T_HisRecord* pRecord);

#define UID_BASE                  (0x1FFF7A10UL)

#define FS_PARTITION_NAME   "filesys"
rt_mutex_t s_ptMutexFile;
Static rt_int32_t s_lWriteFsCnt = 0;
static T_BmsPACKFactoryStruct s_tBmsPackFacInfo;
static T_BmsPACKManufactStruct s_tBmsPACKManufact;
static T_HardwareParaStruct s_tHWPara;

void initFileSys(void)
{
    rt_int32_t readCount = 0;

    fal_init();
    fal_mtd_nor_device_create(FS_PARTITION_NAME);
    if (dfs_mount(FS_PARTITION_NAME, "/", "lfs", 0, 0) != 0)
    {
        dfs_mkfs("lfs", FS_PARTITION_NAME);
        dfs_mount(FS_PARTITION_NAME, "/", "lfs", 0, 0);
    }
    s_ptMutexFile = rt_mutex_create("file_lock", RT_IPC_FLAG_PRIO);

    readCount = readFile("/packInfo", (BYTE *)&s_tBmsPackFacInfo, sizeof(T_BmsPACKFactoryStruct));
    if (readCount != sizeof(T_BmsPACKFactoryStruct) || CRC_Cal((BYTE*)&s_tBmsPackFacInfo, sizeof(T_BmsPACKFactoryStruct)))
    {
        rt_memset_s(&s_tBmsPackFacInfo,sizeof(T_BmsPACKFactoryStruct) ,0x00, sizeof(T_BmsPACKFactoryStruct));
    }

    readCount = readFile("/packManu", (BYTE *)&s_tBmsPACKManufact, sizeof(T_BmsPACKManufactStruct));
    if (readCount != sizeof(s_tBmsPACKManufact) || CRC_Cal((BYTE*)&s_tBmsPACKManufact, sizeof(T_BmsPACKManufactStruct)))
    {
        rt_memset_s(&s_tBmsPACKManufact,sizeof(s_tBmsPACKManufact) ,0x00, sizeof(s_tBmsPACKManufact));
    }
    
    readCount = readFile("/hwpara", (BYTE *)&s_tHWPara, sizeof(T_HardwareParaStruct));
    if (readCount != sizeof(T_HardwareParaStruct) || CRC_Cal((BYTE*)&s_tHWPara, sizeof(T_HardwareParaStruct)))
    {
        rt_memset_s(&s_tHWPara,sizeof(T_HardwareParaStruct) ,0x00, sizeof(T_HardwareParaStruct));  //此处不应该初始化为0
#ifdef DEVICE_USING_D121
        s_tHWPara.ucCellTempNum = 4;
        s_tHWPara.ucCellVoltNum = 8;//真实电芯数量
#else
        s_tHWPara.ucCellTempNum = CELL_TEMP_NUM;
        s_tHWPara.ucCellVoltNum = CELL_VOL_NUM;//真实电芯数量
#endif
    }

    return;
}


BOOLEAN readGPSLocation(T_GPSPositionStruct *ptGps)
{
    rt_int32_t readCount = 0;

    readCount = readFile("/gpsdata", (BYTE *)ptGps, sizeof(T_GPSPositionStruct));
    if (readCount != sizeof(T_GPSPositionStruct) || ptGps->wCRC != CRC_Cal((BYTE*)ptGps, offsetof(T_GPSPositionStruct,wCRC)))
    {
        rt_memset_s(ptGps,sizeof(T_GPSPositionStruct) ,0x00, sizeof(T_GPSPositionStruct));  //此处不应该初始化为0
        return False;
    }

    return True;
}

void writeGPSLocation(T_GPSPositionStruct *ptGps)
{
    BYTE *p = (BYTE *)ptGps;

    ptGps->wCRC = CRC_Cal(p, offsetof(T_GPSPositionStruct, wCRC));
    writeFile("/gpsdata", (BYTE *)ptGps, sizeof(T_GPSPositionStruct));
    return;
}

/* Started by AICoder, pid:uc8fel6872a2e5114d5008fa5039bf1898f8064a */
BOOLEAN readGPSDirection(T_GPSDirectionStruct *ptGpsDirec) {
    // 参数有效性检查
    if (!ptGpsDirec) return FALSE;

    // 读取文件内容
    rt_int32_t readCount = readFile("/gpsDirec", (BYTE *)ptGpsDirec, sizeof(*ptGpsDirec));
    
    // 验证读取结果和CRC校验
    if (readCount != sizeof(*ptGpsDirec) || 
        ptGpsDirec->wCRC != CRC_Cal((BYTE*)ptGpsDirec, offsetof(T_GPSDirectionStruct, wCRC))) 
    {
        // 清空结构体（使用正确类型和大小）
        rt_memset_s(ptGpsDirec, sizeof(*ptGpsDirec), 0x00, sizeof(*ptGpsDirec));
        return FALSE;
    }

    return TRUE;
}
/* Ended by AICoder, pid:uc8fel6872a2e5114d5008fa5039bf1898f8064a */

/* Started by AICoder, pid:e629fia40ctd921141ee0bf0d0322f170930989f */
void writeGPSDirection(T_GPSDirectionStruct *ptGpsDirec) {
    // 参数有效性检查
    if (!ptGpsDirec) return;

    // 计算CRC校验和（使用结构体指针直接访问）
    ptGpsDirec->wCRC = CRC_Cal((BYTE *)ptGpsDirec, offsetof(T_GPSDirectionStruct, wCRC));

    // 写入文件
    writeFile("/gpsDirec", (BYTE *)ptGpsDirec, sizeof(*ptGpsDirec));
}
/* Ended by AICoder, pid:e629fia40ctd921141ee0bf0d0322f170930989f */

BOOLEAN readBmsCustomerName(T_BmsCustomerNameStruct *ptBmsCustomerName)
{
    rt_int32_t readCount = 0;
    
    if (ptBmsCustomerName == NULL) {
        return False;
    }

    readCount = readFile(FILE_NAME_CUSTOMER_NAME, (BYTE *)ptBmsCustomerName, sizeof(T_BmsCustomerNameStruct));

    if (readCount != sizeof(T_BmsCustomerNameStruct) || CRC_Cal((BYTE*)ptBmsCustomerName, offsetof(T_BmsCustomerNameStruct, wCRC) +2))
    {
        return False;
    }
   
    return True;   
}




BOOLEAN writeBmsCustomerName(T_BmsCustomerNameStruct *ptBmsCustomerName)
{
    rt_int32_t len = 0;
    if (ptBmsCustomerName == NULL) {
        return False;
    }

    BYTE *p = (BYTE *)ptBmsCustomerName;

    ptBmsCustomerName->wCRC = CRC_Cal( p, offsetof(T_BmsCustomerNameStruct, wCRC));
    len = writeFile(FILE_NAME_CUSTOMER_NAME, (BYTE *)ptBmsCustomerName, sizeof(T_BmsCustomerNameStruct));
    if (len == 0) {
        // handle error, for example, log the error
        return False;
    }
    
    return True;
}


BOOLEAN read4GTraffic(T_4GTrafficTotail *pt4GTrafficTotail)
{
    rt_int32_t readCount = 0;

    if (pt4GTrafficTotail == NULL) {
        return False;
    }

    readCount = readFile("4GTraffic", (BYTE *)pt4GTrafficTotail, sizeof(T_4GTrafficTotail));

    if (readCount != sizeof(T_4GTrafficTotail) || CRC_Cal((BYTE*)pt4GTrafficTotail, offsetof(T_4GTrafficTotail, wCRC) +2))
    {
        return False;
    }

    return True;
}

BOOLEAN write4GTraffic(T_4GTrafficTotail *pt4GTrafficTotail)
{
    rt_int32_t len = 0;
    if (pt4GTrafficTotail == NULL) {
        return False;
    }

    BYTE *p = (BYTE *)pt4GTrafficTotail;

    pt4GTrafficTotail->wCRC = CRC_Cal( p, offsetof(T_4GTrafficTotail, wCRC));
    len = writeFile("4GTraffic", (BYTE *)pt4GTrafficTotail, sizeof(T_4GTrafficTotail));
    if (len == 0) {
        return False;
    }

    return True;
}

BOOLEAN readBmsHWPara(T_HardwareParaStruct *ptHWPara)
{
    rt_memcpy(ptHWPara, &s_tHWPara, sizeof(s_tHWPara));

    return True;   
}

void writeBmsHWPara(T_HardwareParaStruct *ptHWPara)
{
    BYTE *p = (BYTE *)ptHWPara; 

    ptHWPara->wCRC = CRC_Cal( p, (sizeof(T_HardwareParaStruct)-2) );
    rt_memcpy(&s_tHWPara, ptHWPara, sizeof(s_tHWPara));
    writeFile("/hwpara", (BYTE *)ptHWPara, sizeof(T_HardwareParaStruct));
    return;
}

BOOLEAN readNetMacPara(T_NetMacStruct *ptNetMacPara)
{
    rt_int32_t readCount = 0;

    readCount = readFile("/macpara", (BYTE *)ptNetMacPara, sizeof(T_NetMacStruct));
#ifndef KW_CHECK
    if ( readCount != sizeof(T_NetMacStruct) || CRC_Cal((BYTE*)ptNetMacPara, sizeof(T_NetMacStruct)) ||
         ptNetMacPara->ucMacFlag != 0X55 )
    {
        rt_memset_s(ptNetMacPara,sizeof(T_NetMacStruct) ,0x00, sizeof(T_NetMacStruct));
        /* OUI 00-80-E1 STMICROELECTRONICS. */
        ptNetMacPara->aucMacAddr[0] = 0x00;
        ptNetMacPara->aucMacAddr[1] = 0x80;
        ptNetMacPara->aucMacAddr[2] = 0xE1;
#ifndef UNITEST
        /* generate MAC addr from 96bit unique ID (only for test). */
        ptNetMacPara->aucMacAddr[3] = *(rt_uint8_t *)(UID_BASE + 4);
        ptNetMacPara->aucMacAddr[4] = *(rt_uint8_t *)(UID_BASE + 2);
        ptNetMacPara->aucMacAddr[5] = *(rt_uint8_t *)(UID_BASE + 0);
#endif
        return False;
    }
#endif
    return True;
}

void writeNetMacPara(T_NetMacStruct *ptNetMacPara)
{
    BYTE *p = (BYTE *)ptNetMacPara;

    ptNetMacPara->wCRC = CRC_Cal( p, (sizeof(T_NetMacStruct)-2) );
    writeFile("/macpara", (BYTE *)ptNetMacPara, sizeof(T_NetMacStruct));
    return;
}

void writeNorFlashTestPara(WORD *wNorFlashTestData)
{
    writeFile("/norflashtestpara", (BYTE *)wNorFlashTestData, 2);
    return;
}

void readNorFlashTestPara(WORD *wNorFlashTestData)
{
    UNUSED rt_int32_t readCount = 0;
    readCount = readFile("/norflashtestpara", (BYTE *)wNorFlashTestData, 2);
    return;
}

void writeBduRecordCounter(WORD *wBduRecordCnt)
{
    writeFile("/bdurecordcnt", (BYTE *)wBduRecordCnt, 2);
    return;
}

void readBduRecordCounter(WORD *wBduRecordCnt)
{
    UNUSED rt_int32_t readCount = 0;
    readCount = readFile("/bdurecordcnt", (BYTE *)wBduRecordCnt, 2);
    return;
}

BOOLEAN SaveDcrCnt(T_DcrCntStruct *ptDcrCnt)
{
    BYTE *p = (BYTE *)ptDcrCnt;

    ptDcrCnt->wCheckSum = CRC_Cal(p, offsetof(T_DcrCntStruct, wCheckSum));
    
    writeFile(FILE_NAME_DCR_CNT, (BYTE *)ptDcrCnt, sizeof(T_DcrCntStruct));

    return TRUE;
}

BOOLEAN ReadDcrCnt( T_DcrCntStruct* ptDcrCnt )
{
    rt_int32_t readCount = 0;

    readCount = readFile(FILE_NAME_DCR_CNT, (BYTE *)ptDcrCnt, sizeof(T_DcrCntStruct));

    if (readCount != sizeof(T_DcrCntStruct) || (ptDcrCnt->wCheckSum != CRC_Cal((BYTE*)ptDcrCnt, offsetof(T_DcrCntStruct, wCheckSum))))
    {
        rt_memset_s(ptDcrCnt,sizeof(T_DcrCntStruct) ,0x00, sizeof(T_DcrCntStruct));
        return False;
    }
    return True;
}

BOOLEAN saveResetData(BYTE ucType, BYTE ucData)
{
    rt_int32_t readCount = 0;
    rt_uint8_t aucBuf[RESET_DATA_NUM] = {0,};
    char acStr[21];
    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);
    rt_int32_t fd = open("/resetdata", O_RDWR | O_CREAT);

    if (fd < 0)
    {
        rt_mutex_release(s_ptMutexFile);
        return False;
    }
    if (GPIO_OFF == GetPMOSStatus())
    {
        close(fd);
        rt_mutex_release(s_ptMutexFile);
        return False;
    }
    readCount = rt_read_s(fd, &aucBuf,sizeof(aucBuf), RESET_DATA_NUM);
    if (readCount < 0)
    {
        close(fd);
        rt_mutex_release(s_ptMutexFile);
        return False;
    }

    if (aucBuf[ucType] != ucData)
    {
        if (ucType == BMS_ADDR )
        {
            rt_snprintf(acStr, sizeof(acStr), "%d->%d", aucBuf[ucType], ucData);
            SaveAction(GetActionId(CONTOL_ADDR_CHANGE), acStr);
        }
#ifdef USE_CAN2
        if (ucType == BMS_CAN2_ADDR )
        {
            rt_snprintf(acStr, sizeof(acStr), "%d->%d", aucBuf[ucType], ucData);
            SaveAction(GetActionId(CONTOL_CAN2_ADDR_CHANGE), acStr);
        }
#endif
        aucBuf[ucType] = ucData;
        lseek(fd, 0, SEEK_SET);
        write(fd, &aucBuf, RESET_DATA_NUM);
        s_lWriteFsCnt++;
    }
    close(fd);
    rt_mutex_release(s_ptMutexFile);

    return True;
}

BYTE getResetData(BYTE ucType)
{
    rt_int32_t readCount = 0;
    rt_uint8_t aucBuf[RESET_DATA_NUM] = {0,};

    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);
    rt_int32_t fd = open("/resetdata", O_RDWR);
    if (fd < 0)
    {
        rt_mutex_release(s_ptMutexFile);
        return 0;
    }

    readCount = rt_read_s(fd, &aucBuf, sizeof(aucBuf), RESET_DATA_NUM);
    if ( readCount == 0 )
    {
        aucBuf[BMS_ADDR] = 1;
        aucBuf[RESET_REASON] = NO_RESET_UNKNOW - 1;
        aucBuf[BATT_THEFT] = 0;
        aucBuf[DCR_FAULT_ALM_PRT] = 0;
        aucBuf[SITE_THEFT] = 0;
        aucBuf[NET_THEFT] = 0;
        if (GPIO_ON == GetPMOSStatus())
        {
            write(fd, &aucBuf, RESET_DATA_NUM);
            s_lWriteFsCnt++;
        }
    }
    close(fd);
    rt_mutex_release(s_ptMutexFile);

    return aucBuf[ucType];
}

void WrEEPCap( T_BattSaveStruct* ptBattSave )
{
    BYTE *p = (BYTE *)ptBattSave;

    ptBattSave->wCRC = CRC_Cal(p, sizeof(T_BattSaveStruct) - 2);
    
    writeFile("/battSave", (BYTE *)ptBattSave, sizeof(T_BattSaveStruct));

    return;
}

BOOLEAN	RdEEPCap( T_BattSaveStruct* ptBattSave )
{
    rt_int32_t readCount = 0;

    readCount = readFile("/battSave", (BYTE *)ptBattSave, sizeof(T_BattSaveStruct));

    if (readCount != sizeof(T_BattSaveStruct) || CRC_Cal((BYTE*)ptBattSave, sizeof(T_BattSaveStruct)))
    {
        rt_memset_s(ptBattSave,sizeof(T_BattSaveStruct) ,0x00, sizeof(T_BattSaveStruct));
        return False;
    }

    return True;
}

void WrEEPCapNew( T_BattSaveNewStruct* ptBattSave )
{
    BYTE *p = (BYTE *)ptBattSave;

    ptBattSave->wCRC = CRC_Cal(p, offsetof(T_BattSaveNewStruct, wCRC));
    writeFile("/battSaveNew", (BYTE *)ptBattSave, sizeof(T_BattSaveNewStruct));

    return;
}

BOOLEAN	RdEEPCapNew( T_BattSaveNewStruct* ptBattSave )
{
    rt_int32_t readCount = 0;

    readCount = readFile("/battSaveNew", (BYTE *)ptBattSave, sizeof(T_BattSaveNewStruct));

    if (readCount != sizeof(T_BattSaveNewStruct) || CRC_Cal((BYTE*)ptBattSave, offsetof(T_BattSaveNewStruct, wCRC) + 2))
    {
        rt_memset_s(ptBattSave,sizeof(T_BattSaveNewStruct) ,0x00, sizeof(T_BattSaveNewStruct));
        return False;
    }

    return True;
}

BOOLEAN readBmsPackFacInfo( T_BmsPACKFactoryStruct *ptBmsPackFacInfo )
{
    rt_memcpy(ptBmsPackFacInfo, &s_tBmsPackFacInfo, sizeof(T_BmsPACKFactoryStruct));

    return True;
}

// 特别说明：调用此接口时请注意，如果修改了BMS序列号，密钥会变更，需对涉密数据重新加密后存储
void  writeBmsPackFacInfo( T_BmsPACKFactoryStruct *ptBmsPackFacInfo )
{
    BYTE *p = (BYTE *)ptBmsPackFacInfo;	
    ptBmsPackFacInfo->wCRC = CRC_Cal( p, (sizeof(T_BmsPACKFactoryStruct)-2) );
    rt_memcpy(&s_tBmsPackFacInfo, ptBmsPackFacInfo, sizeof(T_BmsPACKFactoryStruct));
    writeFile("/packInfo", (BYTE *)ptBmsPackFacInfo, sizeof(T_BmsPACKFactoryStruct));
    return;
}

void readPackManufact(T_BmsPACKManufactStruct* ptBmsPACKManufact)//readPackManufact
{
    rt_memcpy(ptBmsPACKManufact, &s_tBmsPACKManufact, sizeof(*ptBmsPACKManufact));

    return;
}

void  writePackManufact(T_BmsPACKManufactStruct* ptBmsPACKManufact) //WriteBmsPackManufact
{
    BYTE *p = (BYTE *)ptBmsPACKManufact;	
    ptBmsPACKManufact->wCRC = CRC_Cal( p, (sizeof(T_BmsPACKManufactStruct)-2) );
    rt_memcpy(&s_tBmsPACKManufact, ptBmsPACKManufact, sizeof(*ptBmsPACKManufact));
    writeFile("/packManu", (BYTE *)ptBmsPACKManufact, sizeof(T_BmsPACKManufactStruct));
    return;
}

BOOLEAN readBMSInfofact(T_BmsInfoStruct* ptBmsInfo)
{
    rt_int32_t readCount = 0;
    
    if(ptBmsInfo == NULL)
    {
        return FALSE;
    }

    readCount = readFile("/BMSInfo", (BYTE *)ptBmsInfo, sizeof(T_BmsInfoStruct));

    if (readCount != sizeof(T_BmsInfoStruct) ||  ptBmsInfo->wCRC != CRC_Cal((BYTE*)ptBmsInfo, offsetof(T_BmsInfoStruct, wCRC)))
    {
        rt_strncpy_s(ptBmsInfo->acBattCorpName,sizeof(ptBmsInfo->acBattCorpName), BATT_CORP_NAME_DEF, 19);
        rt_strncpy_s(ptBmsInfo->acNameLite,sizeof(ptBmsInfo->acNameLite), BMS_SYS_NAME_LITE_DEF, 9);
    }

    return TRUE;
}

BOOLEAN writeBMSInfofact(T_BmsInfoStruct* ptBmsInfo)
{
    BYTE *p = (BYTE *)ptBmsInfo;	
    ptBmsInfo->wCRC = CRC_Cal( p,  offsetof(T_BmsInfoStruct, wCRC));

    writeFile("/BMSInfo", (BYTE *)ptBmsInfo, sizeof(T_BmsInfoStruct));
    return TRUE;
}

void  readBmsRealAlarm(T_RealAlm256SaveStruct* ptRealAlmSave)
{
    rt_int32_t readCount = 0;
    
    readCount = readFile("/realAlarm", (BYTE *)ptRealAlmSave, sizeof(T_RealAlm256SaveStruct));

    if (readCount != sizeof(T_RealAlm256SaveStruct))
    {
        rt_memset_s(ptRealAlmSave,sizeof(*ptRealAlmSave) ,0x00, sizeof(*ptRealAlmSave));
    }
}

void  SaveRealAlarm(T_RealAlm256SaveStruct* ptRealAlmSave)
{ // 字节对齐问题导致sizeof计算CRC存储位置在数据区，此处应使用offsetof计算偏移
    ptRealAlmSave->wCrc = CRC_Cal((BYTE*)ptRealAlmSave, offsetof(T_RealAlm256SaveStruct, wCrc) );
    writeFile("/realAlarm", (BYTE *)ptRealAlmSave, sizeof(T_RealAlm256SaveStruct));
    return;
}

//extern int Region$$Table$$Base ;
//#define LINKER_VAr1 Region$$Table$$Base
#ifndef UNITEST
BOOLEAN  SaveUpgradefileInfo( BYTE ucMode )
{
    BYTE aucBuff[16] ={0,};
    uint32_t boudMode = BAUD_RATE_9600;
    T_FileManageStruct tFileManage;
    rt_spi_flash_device_t dev_w25q;
    BYTE *pFlag = (BYTE*)FLAG_PROGRAM;
    const struct fal_flash_dev *flash_dev = NULL;
    boudMode = getBaudMode();
    flash_dev = fal_flash_device_find("gd32_onchip");
    if(flash_dev == RT_NULL)
    {
        return False;
    }
    dev_w25q = (rt_spi_flash_device_t)rt_device_find("GD25Q128");
    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);
    if (RT_NULL == dev_w25q)
    {
        return False;
    }
    sfud_read( (sfud_flash *)dev_w25q->user_data, UPDATEINFO_STATRT, sizeof(T_FileManageStruct), (uint8_t *)&tFileManage );

    if (ucMode == FLAG_BACKUP )
    {
        if (*pFlag==0xFF)   //烧写后未备份
        {
            tFileManage.tFileAttr.wTotalFrameNum = MAX_PACKET_NUM;//(count-0x8020000)/0x100+1;
            flash_dev->ops.write((FLAG_PROGRAM -GD32F4_FLASH_BASE) , aucBuff, 16);
            s_lWriteFsCnt++;
        }
        else if(tFileManage.ucFlag != FLAG_APP)
        {
            rt_mutex_release(s_ptMutexFile);
            return True;
        }
    }
    rt_memcpy(tFileManage.tFileAttr.acFileName, GetRcvFileName(), FILE_NAME_LEN);
    tFileManage.ucUpdateAddr = ((ucMode == FLAG_CAN_IAP) ? GetCanBMSDLAddr() : GetBMSDLAddr());
    tFileManage.ucFlag = ucMode;
	tFileManage.wBaudRate = boudMode;
    tFileManage.wCrc = CRC_Cal((BYTE *)&tFileManage, sizeof(T_FileManageStruct)-2);
    sfud_erase_write( (sfud_flash *)dev_w25q->user_data, 0, sizeof(T_FileManageStruct), (uint8_t *)&tFileManage );
    s_lWriteFsCnt ++;
    rt_mutex_release(s_ptMutexFile);

    return False;
}
#endif

Static void saveToNewFile(T_HisRecord* pRecord)
{
    rt_int32_t i, fd, fd1;
    rt_uint8_t aucBuf[MAX_RECORD_SIZE];
    UNUSED size_t len;

    fd = open((char*)pRecord->pcFilename, O_RDONLY);
    if(fd < 0)
    {
        return;
    }
    if (GPIO_OFF == GetPMOSStatus())
    {
        close(fd);
        return;
    }

    fd1 = open("/tmpFile", O_WRONLY|O_APPEND| O_CREAT); /* TODO: 临时文件名不应固定为tmpFile */
    if(fd1 < 0)
    {
        close(fd);
        return;
    }
    
    len = lseek(fd, 0 - pRecord->wRecordSize*pRecord->wMinSaveNum, SEEK_END);

    
    /* Assume that sizeof(aucBuf) >= pRecord->wRecordSize */
    const size_t buffer_size = sizeof(aucBuf);
    const size_t record_size = pRecord->wRecordSize;
    const size_t min_save_num = pRecord->wMinSaveNum;

    const size_t copyBytesPerCycle = buffer_size - (buffer_size % record_size);
    const size_t totalBytesToCopy = record_size * min_save_num;
    const size_t copyCycles = totalBytesToCopy / copyBytesPerCycle;
    const size_t lastCopyBytes = totalBytesToCopy % copyBytesPerCycle;

    for (size_t i = 0; i < copyCycles; ++i)
    {
        rt_read_s(fd, aucBuf, buffer_size, copyBytesPerCycle);
        write(fd1, aucBuf, copyBytesPerCycle);
    }

    if (lastCopyBytes != 0)
    {
        rt_read_s(fd, aucBuf, buffer_size, lastCopyBytes);
        write(fd1, aucBuf, lastCopyBytes);
    }
    
    close(fd);
    close(fd1);

    unlink((char*)pRecord->pcFilename );
    if(rt_rename_s("/tmpFile", (char*)pRecord->pcFilename) < 0)
    {

    }
    s_lWriteFsCnt += 2;

    pRecord->wSavedNum = pRecord->wMinSaveNum;
    for(i = 0; i < NORTH_PROTOCOL_NUM_MAX; i++) {
        if (pRecord->wReadPoint[i] > pRecord->wMinSaveNum/50)
        {
            pRecord->wReadPoint[i] -= pRecord->wMinSaveNum/50;
        }
        else
        {
            pRecord->wReadPoint[i] = 0;
        }
    }

    return;
}

/****************************************************************************
* 函数名称：saveToNewBackupFile
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：历史互备份数据存满时更新旧数据
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
void saveToNewBackupFile(T_HisBackStruct* pRecord, rt_uint8_t index)
{
    rt_int32_t i, fd, fd1;
    rt_int32_t len, offset;
    rt_uint16_t wMaxSavedNum[4] = {BACKUP_HISDATA_MAX_NUM, BACKUP_HISALARM_MAX_NUM, BACKUP_HISACT_MAX_NUM, BACKUP_RECORD_MAX_NUM}; //记录存满时保留最新的条数，将其他更早的记录删除

    RETURN_IF_FAIL(pRecord != NULL && pRecord->wRecordSize <= CAN_BACKUP_DATA_BUFF);

    fd = open((char*)pRecord->ucFileName, O_RDONLY);
    if(fd < 0)
    {
        return;
    }
    if (GPIO_OFF == GetPMOSStatus())
    {
        close(fd);
        return;
    }

    fd1 = open("/BACKUP/tmpFile", O_WRONLY|O_APPEND| O_CREAT);
    if(fd1 < 0)
    {
        close(fd);
        return;
    }

    for (i=0; i<wMaxSavedNum[index]; i++)
    {
        offset = lseek(fd, 0 - pRecord->wRecordSize*(wMaxSavedNum[index]-i), SEEK_END);
        if (offset < 0)
        {
            close(fd);
            close(fd1);
            return;
        }
        rt_memset_s(s_aucBackupFileRW, sizeof(s_aucBackupFileRW),0, sizeof(s_aucBackupFileRW));
        len = rt_read_s(fd, s_aucBackupFileRW,sizeof(s_aucBackupFileRW), pRecord->wRecordSize);
        if (len < 0)
        {
            close(fd);
            close(fd1);
            return;
        }
        write(fd1, s_aucBackupFileRW, pRecord->wRecordSize);
    }
    close(fd);
    close(fd1);

    unlink((char*)pRecord->ucFileName);
    if(rt_rename_s("/BACKUP/tmpFile", (char*)pRecord->ucFileName) < 0)
    {
        return;
    }
    s_lWriteFsCnt += 2;

    return;
}

void DelAllRecords(T_HisRecord* pRecord)
{
    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);
    unlink((char*)pRecord->pcFilename );
    pRecord->wSavedNum = 0;
    rt_memset_s(pRecord->wReadPoint, sizeof(pRecord->wReadPoint),0, sizeof(pRecord->wReadPoint));
    rt_mutex_release(s_ptMutexFile);

    return;
}

/// @brief 清除直流内阻相关计数
/// @param  
BOOLEAN DelDcrCnt(void)
{
    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);
    unlink(FILE_NAME_DCR_CNT);
    rt_mutex_release(s_ptMutexFile);
    return TRUE;
}

BOOLEAN saveOneRecord(T_HisRecord* pRecord)
{
    rt_uint32_t len = 0;
    rt_int32_t fd = 0;

    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);

    fd = open((char*)pRecord->pcFilename, O_WRONLY|O_APPEND| O_CREAT);
    if (fd < 0)
    {
        rt_mutex_release(s_ptMutexFile);
        return False;
    }
    if (GPIO_OFF == GetPMOSStatus())
    {
        close(fd);
        rt_mutex_release(s_ptMutexFile);
        return False;
    }
    write(fd, pRecord->pucData, pRecord->wRecordSize);
    s_lWriteFsCnt++;
    pRecord->wSavedNum++;
    len = lseek(fd, 0, SEEK_END);
    close(fd);

    if (len >= 1.02 * pRecord->wRecordSize * pRecord->wMinSaveNum)
    {
        saveToNewFile( pRecord );
    }
    rt_mutex_release(s_ptMutexFile);

    return True;
}

/****************************************************************************
* 函数名称：saveOneBackupRecord
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：保存互备份数据接口
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
BOOLEAN saveOneBackupRecord(T_HisBackStruct* pRecordBackup, BYTE *pucBuff, rt_uint8_t index)
{
    rt_int32_t fd;
    rt_uint16_t wMaxSavedNum[4] = {BACKUP_HISDATA_MAX_NUM, BACKUP_HISALARM_MAX_NUM, BACKUP_HISACT_MAX_NUM, BACKUP_RECORD_MAX_NUM};  //历史数据、告警、操作记录、录波条数

    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);    
    fd = open((char*)pRecordBackup->ucFileName, O_WRONLY|O_APPEND| O_CREAT);
    if (fd < 0)
    {
        rt_mutex_release(s_ptMutexFile);
        return False;
    }
    if (GPIO_OFF == GetPMOSStatus())
    {
        close(fd);
        rt_mutex_release(s_ptMutexFile);
        return False;
    }
    write(fd, pucBuff, pRecordBackup->wRecordSize);
    s_lWriteFsCnt++;
    close(fd);

    if (pRecordBackup->wSavedNum > wMaxSavedNum[index]) //保存到最大条数后，按时间顺序覆盖旧数据
    {
        saveToNewBackupFile(pRecordBackup, index);
    }

    rt_mutex_release(s_ptMutexFile);
    
    return True;
}

BOOLEAN readOneRecord(T_HisRecord* pRecord, record_north_protocol_e wNorthProtocolIndex)
{
    rt_uint32_t len = 0;
    rt_int32_t fd = 0;

    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);

    fd = open((char*)pRecord->pcFilename, O_RDONLY );
    if (fd < 0)
    {
        rt_mutex_release(s_ptMutexFile);
        return False;
    }
    len = lseek(fd, pRecord->wReadPoint[wNorthProtocolIndex] * pRecord->wRecordSize, SEEK_SET);
    len = rt_read_s(fd, pRecord->pucData,pRecord->wRecordSize, pRecord->wRecordSize);
    close(fd);
    rt_mutex_release(s_ptMutexFile);

    if (len == pRecord->wRecordSize)
    {
        pRecord->wReadPoint[wNorthProtocolIndex]++;
        return True;
    }

    return False;
}

BOOLEAN saveHisPointInfo(T_HisPoint* pHisPoint)
{
    BYTE *p = (BYTE *)pHisPoint;
    
    pHisPoint->wCRC = CRC_Cal(p, (sizeof(T_HisPoint)-2) );

    writeFile("/HisPoint", (BYTE *)pHisPoint, sizeof(T_HisPoint));
    
    return True;
}

BOOLEAN readHisPointInfo(T_HisPoint* pHisPoint)
{
    rt_int32_t readCount = 0;
    
    if (pHisPoint == NULL) {
        return False;
    }

    readCount = readFile("/HisPoint", (BYTE *)pHisPoint, sizeof(T_HisPoint));

    if (readCount != sizeof(T_HisPoint) || CRC_Cal((BYTE*)pHisPoint, sizeof(T_HisPoint)))
    {
        rt_memset_s(pHisPoint,sizeof(T_HisPoint) ,0x00, sizeof(T_HisPoint));
        return False;
    }

    return True;
}

size_t readFile(char *pcFilename, rt_uint8_t *pucData, size_t ulReadsize)
{
    rt_uint32_t len = 0;
    rt_int32_t fd = 0;

    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);

    fd = open((char*)pcFilename, O_RDONLY );
    if (fd < 0)
    {
        rt_mutex_release(s_ptMutexFile);
        return 0;
    }
    len = rt_read_s(fd, pucData, ulReadsize, ulReadsize);

    close(fd);
    rt_mutex_release(s_ptMutexFile);

    return len;
}

size_t writeFile(char *pcFilename, rt_uint8_t *pucData, size_t ulWritesize)
{
    rt_uint32_t len = 0;
    rt_int32_t fd = 0;

    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);

    fd = open((char*)pcFilename, O_WRONLY | O_CREAT );
    if (fd < 0)
    {
        rt_mutex_release(s_ptMutexFile);
        return 0;
    }
    if (GPIO_OFF == GetPMOSStatus())
    {
        close(fd);
        rt_mutex_release(s_ptMutexFile);
        return False;
    }
    len = write(fd, pucData, ulWritesize);
    s_lWriteFsCnt++;

    close(fd);
    rt_mutex_release(s_ptMutexFile);

    return len;
}

rt_int32_t getWriteFsCnt(void)
{
    return s_lWriteFsCnt;
}

rt_int32_t writeFsCnt(void)
{
    rt_kprintf("write norflash times: %d\n", s_lWriteFsCnt);
    return 0;
}
FINSH_FUNCTION_EXPORT(writeFsCnt, show write norflash times);
MSH_CMD_EXPORT(writeFsCnt, show write norflash times);

// 检测当前软件版本号与记录文件记录的版本号是否一致，不一致删除当前的历史数据，否则保存当前的历史数据
void CheckHisdataVersion(void)
{
    rt_int32_t fd = 0;
    BYTE ucBuff[10] = {0};
    char pcFilename[] = "/hisRecordVersion";
    UNUSED rt_int32_t readCount = 0;

#ifndef KW_CHECK
    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);
    fd = open((char*)pcFilename, O_RDONLY);
    if (fd < 0)
    {
        rt_mutex_release(s_ptMutexFile);

        DelAllHisData();
        ucBuff[0] = HISDATA_VER;
        writeFile(pcFilename, ucBuff, 1);
        return;
    }
    readCount = rt_read_s(fd, ucBuff,sizeof(ucBuff) ,1);
    close(fd);
    rt_mutex_release(s_ptMutexFile);
    if (ucBuff[0] != HISDATA_VER)
    {
        rmdir("/BACKUP/");
        DelAllHisData();
        ucBuff[0] = HISDATA_VER;
        writeFile(pcFilename, ucBuff, 1);
    }
    return;
#endif
}

void deleteFile(const char *filename)
{
#ifndef KW_CHECK
    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);
    unlink(filename);
    rt_mutex_release(s_ptMutexFile);
#endif
    return;
}


/****************************************************************************
* 函数名称：saveExtremeDataFile
* 功能描述：以文件的形式保存极值数据到flash中
* 修改记录：
***************************************************************************/
BOOLEAN saveExtremeDataFile(T_HisRecord* pRecord)
{
    rt_int32_t fd = 0;

    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);

    fd = open((char*)pRecord->pcFilename, O_WRONLY|O_TRUNC| O_CREAT);
    if (fd < 0)
    {
        rt_mutex_release(s_ptMutexFile);
        return False;
    }
    if (GPIO_OFF == GetPMOSStatus())
    {
        close(fd);
        rt_mutex_release(s_ptMutexFile);
        return False;
    }
    write(fd, pRecord->pucData, pRecord->wRecordSize);
    s_lWriteFsCnt++;
    close(fd);
    rt_mutex_release(s_ptMutexFile);

    return True;
}

/* *********************************************************
 * 互备份电芯统计记录保存 只保存一条记录
 * *********************************************************/
BOOLEAN saveAnalyseNewFile(T_HisBackStruct* pRecordBackup, BYTE* pstr, WORD wLen)
{
    rt_int32_t fd = 0;

    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);

    fd = open((char*)pRecordBackup->ucFileName, O_WRONLY|O_TRUNC| O_CREAT);
    if (fd < 0)
    {
        rt_mutex_release(s_ptMutexFile);
        return False;
    }
    if (GPIO_OFF == GetPMOSStatus())
    {
        close(fd);
        rt_mutex_release(s_ptMutexFile);
        return False;
    }
    write(fd, pstr, wLen);
    s_lWriteFsCnt++;
    close(fd);
    rt_mutex_release(s_ptMutexFile);

    return True;
}

/* *********************************************************
 * 互备份极值记录保存 只保存一条记录
 * *********************************************************/
BOOLEAN saveExtremeNewFile(T_HisBackStruct* pRecordBackup, BYTE* pstr, WORD wLen, BYTE ucPackNum)
{
    rt_int32_t fd = 0;

    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);

    if (ucPackNum == ID_BACKEUP_HISEXTREME_PACK1)
    {
        fd = open((char*)pRecordBackup->ucFileName, O_WRONLY|O_TRUNC| O_CREAT);  //第一条，清空文件保存
        if (fd < 0)
        {
            rt_mutex_release(s_ptMutexFile);
            return False;
        }
        if (GPIO_OFF == GetPMOSStatus())
        {
            close(fd);
            rt_mutex_release(s_ptMutexFile);
            return False;
        }
    }
    else
    {
        fd = open((char*)pRecordBackup->ucFileName, O_WRONLY|O_APPEND| O_CREAT);  //第二条，追加文件保存
        if (fd < 0)
        {
            rt_mutex_release(s_ptMutexFile);
            return False;
        }
        if (GPIO_OFF == GetPMOSStatus())
        {
            close(fd);
            rt_mutex_release(s_ptMutexFile);
            return False;
        }
    }
    
    write(fd, pstr, wLen);
    s_lWriteFsCnt++;
    close(fd);
    rt_mutex_release(s_ptMutexFile);

    return True;
}

/****************************************************************************
* 函数名称：readExtremeDataFile
* 功能描述：读出flash中的数据
* 修改记录：
***************************************************************************/
BOOLEAN readExtremeDataFile(T_HisRecord* pRecord)
{
    rt_uint32_t len = 0;
    rt_int32_t fd = 0;

    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);

    fd = open((char*)pRecord->pcFilename, O_RDONLY );
    if (fd < 0)
    {
        rt_mutex_release(s_ptMutexFile);
        return False;
    }
    len = rt_read_s(fd, pRecord->pucData, pRecord->wRecordSize, pRecord->wRecordSize);
    close(fd);
    rt_mutex_release(s_ptMutexFile);

    if (len == pRecord->wRecordSize)
    {
        return CRC_Cal((BYTE*)pRecord->pucData, offsetof(T_HisExtremeData, wCheckSum)+2)? False:True;
    }

    return False;
}

// 检测当前软件版本号与极值记录文件记录的版本号是否一致，不一致删除当前文件
void CheckHisExtremeVersion(void)
{
    rt_int32_t fd = 0;
    BYTE ucBuff[10] = {0};
    char pcFilename[] = "/hisExtremeVersion";
    UNUSED rt_int32_t readCount = 0;

    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);
    fd = open(pcFilename, O_RDONLY);
    if (fd < 0)
    {
        rt_mutex_release(s_ptMutexFile);
        DelAllHisExtreme();
        ucBuff[0] = HISEXTREME_VER;
        writeFile(pcFilename, ucBuff, 1);
        return;
    }
    readCount = rt_read_s(fd, ucBuff,sizeof(ucBuff), 1);
    close(fd);
    rt_mutex_release(s_ptMutexFile);
    if (ucBuff[0] != HISEXTREME_VER)
    {
        DelAllHisExtreme();
        ucBuff[0] = HISEXTREME_VER;
        writeFile(pcFilename, ucBuff, 1);
    }
    return;
}
void writeAlarmCheckFile(T_AlarmCheckStruct *ptAlarmCheck)
{
    BYTE *p = (BYTE *)ptAlarmCheck;
#ifndef KW_CHECK
    ptAlarmCheck->wCrc = CRC_Cal(p, (sizeof(T_AlarmCheckStruct) - 2));

    writeFile("/AlmCheck", (BYTE *)ptAlarmCheck, sizeof(T_AlarmCheckStruct));
#endif
    return;
}

void readAlarmCheckFile(T_AlarmCheckStruct *ptAlarmCheck)
{
    rt_int32_t readCount = 0;

    readCount = readFile("/AlmCheck", (BYTE *)ptAlarmCheck, sizeof(T_AlarmCheckStruct));
#ifndef KW_CHECK
    if (readCount != sizeof(*ptAlarmCheck) || CRC_Cal((BYTE *)ptAlarmCheck, sizeof(T_AlarmCheckStruct)))
    {
        rt_memset_s(ptAlarmCheck, sizeof(*ptAlarmCheck),0x00, sizeof(*ptAlarmCheck));
    }
#endif
    return;
}

#if !defined(KW_CHECK) && !defined(UNITEST)
/****************************************************************************
* 函数名称：readHisBackupMagFile
* 调    用：无
* 被 调 用：pcFilename互备份管理文件名、ptMagFile互备份管理文件信息、
*           pcFileNum备份的文件总数(实际并机台数)、ptHisBackup互备份文件信息、ucBackDevNum最大互备份文件个数
* 输入参数：
* 返 回 值：
* 功能描述：读取历史互备份管理文件，当备份文件中记录达到最大条数或者文件数达到设计的最大台数，删除最早的。
            该文件记录了：备份文件名(40字节)+保存的条数(2字节)+最后一条保存的时间(7字节)
                                                   SN1_hisdata+1200+time
                                                   SN2_hisdata+1200+time
                                                   SN3_hisdata+1200+time
                                                   ...
                                                   
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
void readHisBackupMagFile(char *pcFilename, T_HisBackMagStruct *ptMagFile, char *pcFileNum, T_HisBackStruct *ptHisBackup, char ucBackDevNum)
{
    rt_int32_t i, fd, len;
    rt_uint8_t uTemp[BACKUP_FILE_NAME_MAX_LEN] = {0};

    if (pcFilename == NULL)
    {
        return;
    }
    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);
    fd = open(pcFilename, O_RDONLY);
    if (fd < 0)
    {
        fd = open( pcFilename, O_RDWR | O_CREAT | O_APPEND);
        if (fd < 0)
        {
            rt_mutex_release(s_ptMutexFile);
            return;
        }
        if (GPIO_OFF == GetPMOSStatus())
        {
            close(fd);
            rt_mutex_release(s_ptMutexFile);
            return;
        }
        rt_memset_s(ptMagFile,sizeof(T_HisBackMagStruct) * ucBackDevNum,0, sizeof(T_HisBackMagStruct) * ucBackDevNum);
        for(i=0; i<ucBackDevNum; i++)
        {
            len = write(fd, &ptMagFile[i], sizeof(T_HisBackMagStruct));
            if (len < 0)
            {
                close(fd);
                rt_mutex_release(s_ptMutexFile);
                return;
            }
            s_lWriteFsCnt++;
        }
        
        *pcFileNum = 0;
        close(fd);
        rt_mutex_release(s_ptMutexFile);
        return;
    }

    for(i=0; i<ucBackDevNum; i++)
    {  
        len = rt_read_s(fd, &ptMagFile[i], sizeof(T_HisBackMagStruct), sizeof(T_HisBackMagStruct));
        if (len < 0)
        {
            close(fd);
            rt_mutex_release(s_ptMutexFile);
            return;
        }

        ptMagFile[i].ucFileName[BACKUP_FILE_NAME_MAX_LEN - 1] = '\0';
        if (rt_memcmp(ptMagFile[i].ucFileName, uTemp, rt_strnlen_s((char*)ptMagFile[i].ucFileName, sizeof(ptMagFile[i].ucFileName))) != 0)
        {
            rt_memcpy(ptHisBackup[i].ucFileName, ptMagFile[i].ucFileName, rt_strnlen_s((char*)ptMagFile[i].ucFileName, sizeof(ptMagFile[i].ucFileName)));
            (*pcFileNum)++;  //备份管理文件中记录文件总个数
        }
    }
    close(fd);
    rt_mutex_release(s_ptMutexFile);
    return;
}
#endif

Static sfud_err Erase_norflash_for_update(const sfud_flash *flash, uint32_t earse_addr, size_t size) 
{
    if(flash == RT_NULL)
    {
        return False;
    }
    sfud_err result = SFUD_SUCCESS;
    result = sfud_erase(flash, earse_addr, size*EARSE_FAN_BYTES);
    return result;
}

Static sfud_err write_circlebuff_norflash(const sfud_flash *flash, uint32_t addr, size_t size, const uint8_t *data) 
{
    if(flash == RT_NULL)
    {
        return False;
    }
    sfud_err result = SFUD_SUCCESS;
    result = sfud_write(flash, addr, size, data);
    return result;
}

Static BOOLEAN DealBMSUpdateData(T_AppDataSaveStruct* Buff)
{
    sfud_err result = SFUD_ERR_NOT_FOUND;
    rt_spi_flash_device_t dev_w25q;
    dev_w25q = (rt_spi_flash_device_t)rt_device_find("GD25Q128");
    if((dev_w25q == RT_NULL)||(dev_w25q->user_data == NULL))
    {
        return False;
    }
    result = write_circlebuff_norflash((sfud_flash *)dev_w25q->user_data, \
                    NORFLASH_APP_START + (Buff->ulFrameNo - 1)*APP_DATA_LEN, \
                    APP_DATA_LEN, Buff->aucData);
    if(result == SFUD_SUCCESS)
    {
        // rt_kprintf("write frame %d addr offset %d write len %d \r\n", Buff->ulFrameNo, 
        //             NORFLASH_APP_START + (Buff->ulFrameNo - 1)*APP_DATA_LEN, 
        //             APP_DATA_LEN);
        return True;
    } else {
        rt_kprintf("write frame %d to norflash error\n", Buff->ulFrameNo);
    }
    return False;
}

Static BOOLEAN DealNorflashErase(void)
{
    rt_tick_t start = rt_tick_get();
    rt_tick_t end = 0;
    sfud_err result = SFUD_ERR_NOT_FOUND;
    // 获取触发成功标志位后，擦除升级需要使用的norflash
    if((!s_ucEraseFLAG) && GetTriggerEndFlag())
    {
        rt_spi_flash_device_t dev_w25q;
        dev_w25q = (rt_spi_flash_device_t)rt_device_find("GD25Q128");
        if((dev_w25q == RT_NULL)||(dev_w25q->user_data == NULL))
        {
            return False;
        }
        result = Erase_norflash_for_update((sfud_flash *)dev_w25q->user_data, NORFLASH_APP_START, MAX_UPDATE_NORFLASH_NUM);
        end = rt_tick_get();
        rt_kprintf("norflash擦除耗费的时间, %d ms\n", end - start);
        if(result == SFUD_SUCCESS)
        {
            s_ucEraseFLAG = True;
            return True;
        }
    }
    return False;
}

BOOLEAN DealUpdateProcess(void)
{
    DealNorflashErase();
    if(s_ucEraseFLAG)
    {
        for(BYTE i = 0; i< MAX_UPDATE_DATA_CACHE_NUM; i++)
        {
            if (read_circle_buff(&s_aucUpdateBuff, 1))
            {
                DealBMSUpdateData(&s_aucUpdateBuff);
            }
            else
            {
                return False;
            }
        }    
        return True;
    }

    return False;
}

//升级流程结束（无论成功与否）,调用此接口重置到初始化状态
BOOLEAN ResetToInitUpdate(void)
{
    s_ucEraseFLAG = False;
    reset_circle_buff();
    return True;
}

WORD CRC_Cal_NOR(uint32_t addr, ULONG ulTotalFileLength)
{   
    WORD    j, crc = 0; 
    BYTE    ucTmp;
    ULONG   i;
    ULONG   wCount = 0;
    WORD    wLeftLength = 0;
    WORD    wCrcLen = 0;
    WORD*   awCRC_Table;
    rt_spi_flash_device_t dev_w25q;
    dev_w25q = (rt_spi_flash_device_t)rt_device_find("GD25Q128");
    if (RT_NULL == dev_w25q)
    {
        return False;
    }
    wCount = ulTotalFileLength/READ_CRC_LEN;
    wLeftLength = ulTotalFileLength%READ_CRC_LEN;
    awCRC_Table = getCrcTable(&wCrcLen);
    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);
    for (i = 0; i < wCount; i++) 
    {   
        sfud_read( (sfud_flash *)dev_w25q->user_data, addr + i*READ_CRC_LEN , READ_CRC_LEN, s_wCrcBuf ); 
        for(j = 0; j < READ_CRC_LEN; j++){
            ucTmp = (BYTE)((crc >> 8)^s_wCrcBuf[j]);
            crc   = (crc << 8)^awCRC_Table[ucTmp];
        }
    }
    sfud_read( (sfud_flash *)dev_w25q->user_data, addr + wCount*READ_CRC_LEN , wLeftLength, s_wCrcBuf ); 
    for(j = 0; j < wLeftLength; j++){
        ucTmp = (BYTE)((crc >> 8)^s_wCrcBuf[j]);
        crc   = (crc << 8)^awCRC_Table[ucTmp];
    }
    rt_mutex_release(s_ptMutexFile);
    
    return crc;
}


int readFileManageInfo(T_FileManageStruct *tFileManage) {
    rt_spi_flash_device_t dev_w25q;
    dev_w25q = (rt_spi_flash_device_t)rt_device_find("GD25Q128");
    if (dev_w25q == RT_NULL)
    {
        return FAILURE;
    }
    sfud_read((sfud_flash *)dev_w25q->user_data, 0, sizeof(T_FileManageStruct), (BYTE *)tFileManage );
    return SUCCESSFUL;
}

int writeFileManageInfo(T_FileManageStruct tFileManage) {
    rt_spi_flash_device_t dev_w25q;
    dev_w25q = (rt_spi_flash_device_t)rt_device_find("GD25Q128");
    if (dev_w25q == RT_NULL)
    {
        return FAILURE;
    }
    sfud_erase_write( (sfud_flash *)dev_w25q->user_data, 0, sizeof(T_FileManageStruct), (uint8_t *)&tFileManage );
    return SUCCESSFUL;
}


BOOLEAN SaveNetPeakShiftTemplate(const char *Filename, WORD offset, BYTE *ucBuff, WORD wSize)
{
    rt_int32_t len = 0;
    rt_int32_t fd = 0;

    // 获取互斥锁
    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);

    // 检查PMOS状态
    if (GPIO_OFF == GetPMOSStatus())
    {
        close(fd);
        rt_mutex_release(s_ptMutexFile);
        return False;
    }

    // 打开文件
    fd = open((char*)Filename, O_WRONLY | O_APPEND | O_CREAT);
    if (fd < 0)
    {
        rt_mutex_release(s_ptMutexFile);
        return False;
    }

    // 设置文件偏移量
    len = lseek(fd, offset, SEEK_SET);
    if(len < 0)
    {
        close(fd);
        rt_mutex_release(s_ptMutexFile);
        return False;
    }

    // 写入数据
    write(fd, ucBuff, wSize);
    s_lWriteFsCnt++;

    // 关闭文件
    close(fd);

    // 释放互斥锁
    rt_mutex_release(s_ptMutexFile);

    return True;
}


/***************************************************************************
 * @brief   写入客户个性化配置信息
 **************************************************************************/

BOOLEAN writeSoftwareCustomizedInfo(T_SoftwareCustomizedInfo *ptCustomizedInfo)
{
    if(NULL == ptCustomizedInfo){return False;}
    BYTE *p = (BYTE *)ptCustomizedInfo;
    ptCustomizedInfo->wCrc = CRC_Cal(p, offsetof(T_SoftwareCustomizedInfo, wCrc));
    writeFile("/CustomizedInfo", (BYTE *)ptCustomizedInfo, sizeof(T_SoftwareCustomizedInfo));
    return True;
}

/***************************************************************************
 * @brief   读取客户个性化配置信息
 **************************************************************************/

BOOLEAN readSoftwareCustomizedInfo(T_SoftwareCustomizedInfo *ptCustomizedInfo)
{
    if(NULL == ptCustomizedInfo){return False;}
    rt_int32_t readCount = 0;
    readCount = readFile("/CustomizedInfo", (BYTE *)ptCustomizedInfo, offsetof(T_SoftwareCustomizedInfo, wCrc) + 2);
    if (readCount != sizeof(*ptCustomizedInfo) || ptCustomizedInfo->wCrc != CRC_Cal((BYTE *)ptCustomizedInfo, offsetof(T_SoftwareCustomizedInfo, wCrc)))
    {
        rt_memset(ptCustomizedInfo, 0x00, sizeof(*ptCustomizedInfo));
        return False;
    }
    return True;
}


#ifdef PAKISTAN_CMPAK_PROTOCOL
// 写入累计运行时间和放电次数


BOOLEAN writeCmpakInfo(T_CmpakSaveInfo *ptCmpakSaveInfo)
{
    if(NULL == ptCmpakSaveInfo){return False;}

    BYTE *p = (BYTE *)ptCmpakSaveInfo;
    ptCmpakSaveInfo->wCRC = CRC_Cal(p, offsetof(T_CmpakSaveInfo, wCRC));

    writeFile("/cmpakinfo", (BYTE *)ptCmpakSaveInfo, sizeof(T_CmpakSaveInfo));
    return True; 
}


// 读取累计运行时间和放电次数


BOOLEAN readCmpakInfo(T_CmpakSaveInfo *ptCmpakSaveInfo)
{
    if(NULL == ptCmpakSaveInfo){return False;}

    rt_int32_t readCount = 0;

    readCount = readFile("/cmpakinfo", (BYTE *)ptCmpakSaveInfo, sizeof(T_CmpakSaveInfo));

    if((readCount != sizeof(*ptCmpakSaveInfo)) || (ptCmpakSaveInfo->wCRC != CRC_Cal((BYTE *)ptCmpakSaveInfo, offsetof(T_CmpakSaveInfo, wCRC))))
    {
        rt_memset(ptCmpakSaveInfo, 0x00, sizeof(T_CmpakSaveInfo));
        return False;
    }

    return True;
}


#endif


/**
 * @brief 检测文件或目录是否存在
 * 
 * @param path 路径
 * @return BOOLEAN 检测结果
 */
BOOLEAN IsPathExist(const char *path)
{
    int result;
    struct stat sb;

    result = dfs_file_stat(path, &sb);
    return result == 0;
}

