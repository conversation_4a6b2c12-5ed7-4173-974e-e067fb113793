#ifndef _POWER_CTRL_UTILS_H
#define _POWER_CTRL_UTILS_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

#define FAIL_ACT_POWER_PERCENT  0    // 失效保护有功功率模式-百分比
#define FAIL_REACT_POWER_FACTOR  0   // 失效保护无功功率模式-功率因数


#define ACTIVE_POWER_PERCENT    2    // 参数设置百分比-有功功率模式
#define REACTIVE_POWER_FACTOR   2    // 参数设置功率因数-无功功率模式

typedef struct {
    short id;
    float data_f;
    float temp_data_f;
} floatpara;

typedef struct {
    short id;
    short data_s;
    short temp_data_s;
} shortpara;

char res_ori_power_ctrl_data();
char set_act_power_adj();
char set_react_power_adj();
char is_para_change(int sid, float data_f, float temp_data_f);
int is_power_ctrl_para_chg();

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  
