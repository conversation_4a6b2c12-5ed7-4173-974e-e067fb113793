#include "cell_balance.h"
#include "sample.h"
#include "para_manage.h"
#include "pid.h"
#include "device_type.h"
#include "cmd.h"
#include "msg.h"
#include "data_type.h"
#include "msg_id.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "utils_rtthread_security_func.h"

// #define CELL_BALANCE_JUDGMENT_TIMER_NAME                "cell_balance_judgment_period"
// #define CELL_BALANCE_JUDGMENT_TIMEOUT    10000    ///<  定时器时间，10S

#define CELL_BALANCE_TIMER_NAME                "cell_balance"
#define CELL_BALANCE_TIMEOUT    1*1000   ///<  定时器时间，1S
#define TIMER_COUNT_130 130
#define TIMER_COUNT_120 120
#define TIMER_COUNT_START 1
#define CONTOR_CELL_BALANCE_STOP 0   ///<  停止均衡的命令
// static rt_timer_t cell_balance_judgment_period_timer;
static rt_timer_t cell_balance_period_timer;

static cell_balance_data_t cell_balance_data = {0.0,0.0,{0}};

static void init_cell_balance_timer();
static char cluster_cell_balance_judgment(unsigned char* BMU_ID, char BMU_num);
static void cluster_cell_balance_period_check(char cluster_no);
static char get_cluster_all_cell_vol(char bmu_id, float* cell_vol);
static char get_cluster_cell_vol_min(unsigned char* BMU_ID,char cluster_bmu_num,float *cell_min);
// static char get_cluster_cell_balance_status(char bmu_id, char* cell_balance_status);
static char cal_bmu_cell_balance_status(float* cell_vol, float cell_min, char* cell_balance_status,int *control_cell_balance);

static void cell_balance_period_excute();
static char get_cell_balance_init_para();

static void send_msg_to_sps(unsigned int msg_id, unsigned char dest, unsigned char cmd_id,  unsigned char dev_no);


static void init_cell_balance_timer()
{
    // 配置定时器   
   	// cell_balance_judgment_period_timer = rt_timer_create(CELL_BALANCE_JUDGMENT_TIMER_NAME, cell_balance_judgment,
    //                          NULL, CELL_BALANCE_JUDGMENT_TIMEOUT,
    //                          RT_TIMER_FLAG_ONE_SHOT);

    cell_balance_period_timer = rt_timer_create(CELL_BALANCE_TIMER_NAME, cell_balance_period_excute,
                             NULL, CELL_BALANCE_TIMEOUT,
                             RT_TIMER_FLAG_PERIODIC); 
}



/**
 * @description: 启动自动电芯均衡
 * @return {*}
 * @Date: 2023-03-28
 */
void cell_balance_process_start()
{
    // 初始化定时器
    init_cell_balance_timer();

    // 读入参数
    if(get_cell_balance_init_para() == FAILURE)
    {
        return ; //如果读不到参数，则不开启电芯均衡
    }
    
    get_cluster_topology(&cell_balance_data.cluster_topo);

    // 所有电芯停止均衡
    all_cell_balance_close();

    // 开启1S周期定时器
    rt_timer_start(cell_balance_period_timer);
    
    for(short i = 0; i < BATT_CLUSTER_NUM; i++)
    {
        cell_balance_data.cluster_timer_count[i] = TIMER_COUNT_START;
    }

} 


static void cell_balance_period_excute()
{
    for(short i = 0; i < cell_balance_data.cluster_topo.cluster_count; i++)
    {
        cluster_cell_balance_period_check(i + 1);
    }
}

static void cluster_cell_balance_period_check(char cluster_no)
{
    // 判断定时器的值是多少,做出不同决策
    switch(cell_balance_data.cluster_timer_count[cluster_no-1])
    {
       
        case TIMER_COUNT_START:
            {
                // 对该簇的状态做判断, 如果开启电芯均衡则加1
                if(SUCCESSFUL == cluster_cell_balance_judgment(&cell_balance_data.cluster_topo.mod_addr[cluster_no-1][0],
                    cell_balance_data.cluster_topo.mod_num[cluster_no-1]))
                {
                    cell_balance_data.cluster_timer_count[cluster_no-1] += 1; 
                }       
                break;
            }
        case TIMER_COUNT_120:
            {
                // 均衡时间到120s，则停止该簇的均衡10s
                cluster_cell_balance_close(cluster_no);
                cell_balance_data.cluster_timer_count[cluster_no-1] += 1;
                break;
            }
        case TIMER_COUNT_130:
            {
                // 更新拓扑状态
                get_cluster_topology(&cell_balance_data.cluster_topo);
                // 电芯均衡停止10s时间已到，开启新一轮均衡策略
                cell_balance_data.cluster_timer_count[cluster_no-1] = TIMER_COUNT_START;
                break;
            }
        default:
            {              
                cell_balance_data.cluster_timer_count[cluster_no-1] += 1;
            }
    }

}

void all_cell_balance_close()
{
    // 获取当前拓扑结构
    get_cluster_topology(&cell_balance_data.cluster_topo);

    for(int i = 0; i < cell_balance_data.cluster_topo.cluster_count; i++)
    {
        for(int j = 0; j < cell_balance_data.cluster_topo.mod_num[i];j++)
        {    // 发送停止均衡命令
            bmu_cell_balance_send(cell_balance_data.cluster_topo.mod_addr[i][j], CONTOR_CELL_BALANCE_STOP);
        }
    }
}

void cluster_cell_balance_close(char cluster_no)
{
    for(int i = 0; i < cell_balance_data.cluster_topo.mod_num[cluster_no - 1];i++)
    {    // 发送停止均衡命令
        bmu_cell_balance_send(cell_balance_data.cluster_topo.mod_addr[cluster_no - 1][i], CONTOR_CELL_BALANCE_STOP);
    }
    
}

/** 
 * @description: 关闭电芯自动均衡
 * @return {*}
 * @Date: 2023-03-28
 */
void cell_balance_process_stop()
{
    // 关闭定时器
    rt_timer_stop(cell_balance_period_timer);

    // 所有电芯停止均衡  
    all_cell_balance_close();
}

static char get_cell_balance_init_para()
{    
    //  从参数管理读出数据
    RETURN_VAL_IF_FAIL(SUCCESSFUL == get_one_para(PARA_ID_CELL_BALANCE_DIFF_THRE, 
        &cell_balance_data.cell_balance_difference_threshold),FAILURE); 
    
    RETURN_VAL_IF_FAIL(SUCCESSFUL ==  get_one_para(PARA_ID_CELL_BALANCE_LOW_VOL_VALUE,
        &cell_balance_data.cell_balance_open_min_threshold),FAILURE); 

   return SUCCESSFUL;
}


/**
 * @description: 簇电芯均衡判断
 * @return {*}
 * @Date: 2023-03-28
 */
static char cluster_cell_balance_judgment(unsigned char* BMU_ID, char BMU_num)
{   
    int control_cell_balance_status[BATT_CLUSTER_MOD_NUM] = {0};
    float cell_vol[BATT_MOD_CELL_NUM] = {0.0};
    char current_cell_balance_status[BATT_MOD_CELL_NUM] = {0};
    float cell_vol_min = 0.0;
    char start_cell_balance_flag = FAILURE;

    if(TOPO_INVALID == cell_balance_data.cluster_topo.topo_valid)
    {
        get_cluster_topology(&cell_balance_data.cluster_topo);
        return FAILURE;
    }

    // TODO: 先算80个电压数据，还是单个BMU计算，空间和时间的权衡，需实测权衡

    // 获取一簇中最低电压
    RETURN_VAL_IF_FAIL(SUCCESSFUL == get_cluster_cell_vol_min(&BMU_ID[0],BMU_num,&cell_vol_min),FAILURE); 

    for(int i = 0; i < BMU_num; i++)
    {
        // 获取所有电压
        RETURN_VAL_IF_FAIL(SUCCESSFUL == get_cluster_all_cell_vol(BMU_ID[i], &cell_vol[0]), FAILURE); 

        // 获取当前电芯均衡状态
        RETURN_VAL_IF_FAIL(SUCCESSFUL == get_one_data(BCMU_DATA_ID_CELL_BALANCED_STATUS + BMU_ID[i] -1, 
            &current_cell_balance_status[0]), FAILURE); 

        // 判断均衡启动或停止
        RETURN_VAL_IF_FAIL(SUCCESSFUL == cal_bmu_cell_balance_status(&cell_vol[0], cell_vol_min, 
            &current_cell_balance_status[0], &control_cell_balance_status[i]), FAILURE); 
        
        if(control_cell_balance_status[i] != CONTOR_CELL_BALANCE_STOP) 
        {
            start_cell_balance_flag = SUCCESSFUL;
        }
    }

    RETURN_VAL_IF_FAIL(SUCCESSFUL == start_cell_balance_flag, FAILURE); 

    for(int i = 0; i < BMU_num; i++)
    {     // 计算完一个BMU的所有电芯以后，下发相应的启动均衡或停止均衡命令
        RETURN_VAL_IF_FAIL(SUCCESSFUL == bmu_cell_balance_send(BMU_ID[i], control_cell_balance_status[i]), FAILURE); 
    }

    return SUCCESSFUL;
}



static char get_cluster_all_cell_vol(char bmu_id, float* cell_vol)
{
    #if 0 /* ID化屏蔽*/
    batt_mod_ana_data_t ana_data = {0};
    RETURN_VAL_IF_FAIL(DATA_VALID == get_batt_mod_ana_data(bmu_id, &ana_data),FAILURE); 
    memcpy(&cell_vol[0], &ana_data.mod_comm_ana.cell_vol[0], sizeof(float) * BATT_MOD_CELL_NUM); 
    #endif

    for(int i = 0; i< BATT_MOD_CELL_NUM; i++)
    {
        get_one_data(BCMU_DATA_ID_CELL_VOL + (bmu_id - 1)*BATT_MOD_CELL_NUM + i , &cell_vol[i]);
    }
    return SUCCESSFUL;
}

static char get_cluster_cell_vol_min(unsigned char* BMU_ID,char cluster_bmu_num,float *cell_min)
{
    //batt_mod_ana_data_t ana_data = {0}; 
    float cell_vol_min = 0.0;
    for(int i = 0;i<cluster_bmu_num;i++)
    {
        #if 0 /* ID化屏蔽*/
        RETURN_VAL_IF_FAIL(DATA_VALID == get_batt_mod_ana_data(BMU_ID[i], &ana_data),FAILURE); 
        if(0 == i)
        {
            *cell_min = ana_data.mod_comm_ana.cell_vol_min;
        }
        if(ana_data.mod_comm_ana.cell_vol_min < *cell_min)
        {
            *cell_min = ana_data.mod_comm_ana.cell_vol_min;
        }
        #endif
        get_one_data(BCMU_DATA_ID_CELL_VOL_MIN + (BMU_ID[i] - 1) , &cell_vol_min);
        if(0 == i)
        {
            *cell_min = cell_vol_min;
        }
        if(cell_vol_min < *cell_min)
        {
            *cell_min = cell_vol_min;
        }
    }
    return SUCCESSFUL;
}

#if 0 /* ID化屏蔽*/
static char get_cluster_cell_balance_status(char bmu_id, char* cell_balance_status)
{
    batt_mod_dig_data_t dig_data = {0};
    
    RETURN_VAL_IF_FAIL(DATA_VALID == get_batt_mod_dig_data(bmu_id, &dig_data),FAILURE); 
    memcpy(&cell_balance_status[0], &dig_data.mod_comm_dig.cell_balanced_status[0], sizeof(char) * BATT_MOD_CELL_NUM); 
    return SUCCESSFUL;
}
#endif


static char cal_bmu_cell_balance_status(float* cell_vol, float cell_min, char* cell_balance_status,int *control_cell_balance)
{
    float fDeltaComp = 0.02;    //均衡启动电压阈值回差
    float fDeltaCell = 0.001;   //最低设定值(3.0V)回差
    for(int i = 0; i<BATT_MOD_CELL_NUM; i++)
    {
        if (cell_balance_status[i])
        {
            fDeltaComp = 0.02f;
            fDeltaCell = 0.001f;   
        }
        else
        {
            fDeltaComp = 0.0f;
            fDeltaCell = 0.0f;
        }
        // 开启均衡的两个条件: 1、Vcell - Vmin >= 均衡启动电压阈值 2、Vcell大于设定值(3.0V)
        if(cell_vol[i] - cell_min >= cell_balance_data.cell_balance_difference_threshold - fDeltaComp 
            && cell_vol[i] >= cell_balance_data.cell_balance_open_min_threshold - fDeltaCell)
        {
            *control_cell_balance += (1 << i); // 折算为二进制按位信号
        }
    }

    return SUCCESSFUL;
}


char bmu_cell_balance_send(char bmu_ID, int cell_status)
{
    // 将解析层用到的cell_status 值修改
    set_bmu_cell_balance_status(bmu_ID,cell_status);

    // 发消息到协议栈，完成组包发送
    send_msg_to_sps(EXE_DEST_CMD_MSG, MOD_BMU, BMU_CTRL_SET_BALANCE, bmu_ID);
    return SUCCESSFUL;
}

//TODO: 北向管理需要添加逻辑，每当设置参数的时候，调用这两个函数
void set_cell_balance_difference_threshold(float cell_balance_difference_threshold)
{
    cell_balance_data.cell_balance_difference_threshold = cell_balance_difference_threshold;
}


void set_cell_balance_open_min_threshold(float cell_balance_open_min_threshold)
{
    cell_balance_data.cell_balance_open_min_threshold = cell_balance_open_min_threshold;
}


static void send_msg_to_sps(unsigned int msg_id, unsigned char dest, unsigned char cmd_id,  unsigned char dev_no){
    module_msg_t msg_send;
    unsigned char msg_data[2] = {0};
    unsigned char* send_data = (unsigned char*)malloc(msg_data);
    RETURN_IF_FAIL(send_data != NULL);

    msg_send.src = MOD_CONTROL;     // 控制命令
    msg_send.dest = (module_id_e)dest;
    msg_send.msg_id = msg_id;

    msg_data[0] = cmd_id;
    msg_data[1] = dev_no;
    rt_memcpy_s(send_data, sizeof(msg_data),&msg_data, sizeof(msg_data));
    msg_send.data = send_data;

    send_msg(&msg_send);
}