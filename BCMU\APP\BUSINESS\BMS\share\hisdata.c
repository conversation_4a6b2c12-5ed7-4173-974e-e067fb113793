#include "common.h"
#include "commBdu.h"
#include "fileSys.h"
#include "sample.h"
#include "hisdata.h"
#include "realAlarm.h"
#include "para.h"
#include "flash.h"
#include "fileSys.h"
#include "CommCan.h"
#include "SearchHisdata.h"
#include "protocol_V20.h"
#include "const_define.h"
#include "battery.h"
#include "protocol.h"
#include "utils_heart_beat.h"
#include "thread_id.h"
#include "utils_rtthread_security_func.h"
#include "led.h"
#include "pdt_version.h"

#ifdef ENABLE_SYS_MONITOR
#include "SysMonitor.h"
#endif

#include <rtthread.h>
#include <dfs.h>
#include <stddef.h>
#include <fcntl.h>

#ifdef SINGLE_BUTTON_FUNC_INCLUDE_ENABLE
#include "download_tcp.h"
#endif

#include <drv_utils.h>


/// R321旧实时告警结构体
typedef struct
{
	BYTE	ucChgCurrHighPrt;                                  //  ID=1                 //  充电过流保护
	BYTE	ucDischgCurrHighPrt;                               //  ID=2                 //  放电过流保护
	BYTE	ucBattOverVoltPrt;                                 //  ID=3                 //  电池组过压保护
	BYTE	ucBoardTempHighPrt;                                //  ID=4                 //  单板过温保护
	BYTE	ucChgCurrHighAlm;                                  //  ID=5                 //  充电过流告警
	BYTE	ucDischgCurrHighAlm;                               //  ID=6                 //  放电过流告警
	BYTE	ucBattOverVoltAlm;                                 //  ID=7                 //  电池组过压告警
	BYTE	ucEnvTempHighAlm;                                  //  ID=8                 //  环境温度高告警
	BYTE	ucEnvTempLowAlm;                                   //  ID=9                 //  环境温度低告警
	BYTE	ucBattUnderVoltAlm;                                //  ID=10                //  电池组欠压告警
	BYTE	ucBattUnderVoltPrt;                                //  ID=11                //  电池组欠压保护
	BYTE	aucCellOverVoltAlm[CELL_VOL_NUM_MAX];              //  ID=12-27             //  单体过压告警
	BYTE	aucCellOverVoltPrt[CELL_VOL_NUM_MAX];              //  ID=28-43             //  单体过压保护
	BYTE	aucCellUnderVoltAlm[CELL_VOL_NUM_MAX];             //  ID=44-59             //  单体欠压告警
	BYTE	aucCellUnderVoltPrt[CELL_VOL_NUM_MAX];             //  ID=60-75             //  单体欠压保护
	BYTE	aucChgTempHighAlm[CELL_TEMP_NUM_MIX];              //  ID=76-79             //  单体充电高温告警
	BYTE	aucChgTempHighPrt[CELL_TEMP_NUM_MIX];              //  ID=80-83             //  单体充电高温保护
	BYTE	aucDischgTempHighAlm[CELL_TEMP_NUM_MIX];           //  ID=84-87             //  单体放电高温告警
	BYTE	aucDischgTempHighPrt[CELL_TEMP_NUM_MIX];           //  ID=88-91             //  单体放电高温保护
	BYTE	aucChgTempLowAlm[CELL_TEMP_NUM_MIX];               //  ID=92-95             //  单体充电低温告警
	BYTE	aucChgTempLowPrt[CELL_TEMP_NUM_MIX];               //  ID=96-99             //  单体充电低温保护
	BYTE	aucDischgTempLowAlm[CELL_TEMP_NUM_MIX];            //  ID=100-103           //  单体放电低温告警
	BYTE	aucDischgTempLowPrt[CELL_TEMP_NUM_MIX];            //  ID=104-107           //  单体放电低温保护
	BYTE	ucCellPoorConsisAlm;                               //  ID=108               //  单体一致性差告警
	BYTE	ucCellPoorConsisPrt;                               //  ID=109               //  单体一致性差保护
	BYTE	ucBattSOCLowAlm;                                   //  ID=110               //  电池SOC低告警
	BYTE	ucBattSOCLowPrt;                                   //  ID=111               //  电池SOC低保护
	BYTE	ucBattSOHAlm;                                      //  ID=112               //  电池SOH告警
	BYTE	ucBattSOHPrt;                                      //  ID=113               //  电池SOH保护
	BYTE	aucCellDamagePrt[CELL_VOL_NUM_MAX];                //  ID=114-129           //  单体损坏保护
	BYTE	ucBattLoseAlm;                                     //  ID=130               //  电池丢失告警
	BYTE	ucBduEepromAlm;                                    //  ID=131               //  BDU EEPROM故障
	BYTE	ucCellVoltSampleFault;                             //  ID=132               //  单体电压采样异常
	BYTE	ucChgLoopInvalid;                                  //  ID=133               //  充电回路失效
	BYTE	ucDischgLoopInvalid;                               //  ID=134               //  放电回路失效
	BYTE	ucCurrLimLoopInvalid;                              //  ID=135               //  限流回路失效
	BYTE	ucBattShortCut;                                    //  ID=136               //  电池短路
	BYTE	ucBattReverse;                                     //  ID=137               //  电池反接
	BYTE	ucCellTempSensorInvalidAlm[CELL_TEMP_NUM_MIX];     //  ID=138-141           //  单体温度无效
	BYTE	ucInsideTempHighPrt;                               //  ID=142               //  机内过温保护
	BYTE	ucBDUBattVoltLowPrt;                               //  ID=143               //  BDU电池欠压保护
	BYTE	ucCellTempAbnormal;                                //  ID=144               //  单体温度异常
	BYTE	ucAddressClash;                                    //  ID=145               //  地址冲突
	BYTE	ucShakeAlarm;                                      //  ID=146               //  振动告警
	BYTE	ucBDUBusVoltLowPrt;                                //  ID=147               //  BDU母排欠压保护
	BYTE	ucBDUBusVoltHighPrt;                               //  ID=148               //  BDU母排过压保护
	BYTE	ucBDUCommFail;                                     //  ID=149               //  BDU通信断
	BYTE	ucBattVoltSampleAlm;                               //  ID=150               //  电压采样故障
	BYTE	ucLoopFault;                                       //  ID=151               //  回路异常
	BYTE	ucBDUBattChgVoltLowPrt;                            //  ID=152               //  BDU电池充电欠压保护
	BYTE	ucBDUBattLockAlm;                                  //  ID=153               //  BDU电池闭锁告警
	BYTE	ucEnvTempHighPrt;                                  //  ID=154               //  环境温度高保护
	BYTE	ucEnvTempLowPrt;                                   //  ID=155               //  环境温度低保护
	BYTE	ucBoardTempHighAlm;                                //  ID=156               //  单板过温告警
	BYTE	ucEqualCircuitFaultAlm;                            //  ID=157               //  均衡电路故障告警
	BYTE	ucBalanceResisTempHighPrt;                         //  ID=158               //  均衡电阻温度高保护
	BYTE	ucHeaterFilmFailure;                               //  ID=159               //  加热膜失效
	BYTE	ucBDUConnTempHighPrt;                              //  ID=160               //  连接器温度高保护
	BYTE	ucMainRelayFail;                                   //  ID=161               //  主继电器失效
	BYTE	ucDCDCErr;                                         //  ID=162               //  DCDC故障
	BYTE	ucSampleErr;                                       //  ID=163               //  采集异常
	BYTE	ucAuxiSourceErr;                                   //  ID=164               //  辅助源故障
	BYTE	aucCellDynamicUnderVoltPrt[CELL_VOL_NUM_MAX];      //  ID=165-180           //  单体动态欠压保护
    BYTE    ucFireControlAlm;                                  //  ID=181               //  消防告警
    BYTE    ucActivePortCurrError;                             //  ID=182               //  激活回路电流异常保护
    BYTE    ucCellTempRiseAbnormal;                            //  ID=183               //  单体温升速率异常
	BYTE    ucDcrFaultAlm;                                     //  ID=184               //  直流内阻异常告警
	BYTE    ucDcrFaultPrt;                                     //  ID=185               //  直流内阻异常保护
    BYTE    ucSelfDischFualt;                                  //  ID=186               //  自放电异常
    BYTE	ucCapDCPRFaultAlm;                                 //  ID=187               //  容量衰减一致性差告警
	BYTE	ucBattFaultTempHighAlm;                            //  ID=188               //  电池异常高温保护告警
	BYTE	ucFireControlFaultAlm;                             //  ID=189               //  消防故障告警
	BYTE	ucActivatePortReverseAlm;                          //  ID=190               //  激活口反接告警
	BYTE	ucSiteAntitheftAlm;                                //  ID=191               //  站点防盗告警
    BYTE    ucGPSFaultAlm;                                     //  ID=192               //  GPS故障告警
	BYTE	ucGyrFaultAlm;                                     //  ID=193               //  陀螺仪故障告警
    BYTE	ucNetAntitheftAlm;                                 //  ID=194               //  网管防盗告警
    BYTE	ucBattCellDamagePrt;                               //  ID=195               //  电池单体损坏保护
	BYTE	ucBattCellTempInvalidAlm;                          //  ID=196               //  电池单体温度无效
} T_BCMAlarmStructOld;

typedef struct McuResetReasonMap
{
    int id;
    char msg[20];
} T_McuResetReasonMap;

extern rt_mutex_t s_ptMutexFile;

Static T_HisDataStruct s_atTempHisData[2];   // 30s,60s保存两条历史数据的缓冲区
static T_HisDataStruct s_tHisData;           // 达到历史数据保存间隔后，存放历史数据的缓冲区
//static T_HisDataTowerStruct s_tHisDataTower; // 铁塔(待统一)

static T_SysPara s_tSysPara;                 // 参数
static T_BCMDataStruct s_tBcmData;           // 实时数据
static T_BCMAlarmStruct s_tBcmAlarm;         // 告警
static rt_uint8_t s_ucTempHisDataIndex = 0;  // s_atTempHisData下标控制
static T_HardwareParaStruct s_tHardwarePara; // 电压数量及温度数量
Static T_HisExtremeData s_tHisExtremeData = {0};

Static T_HisRecord s_atHisExtreme[] = {
    {
        .wRecordSize = sizeof(T_HisExtremeData),
        .pcFilename = "/hisextreme",
        .wMinSaveNum = 1,
        .wReadPoint = {0},
    },
};

//历史互备份文件信息
Static T_HisBackStruct s_atHisBackupData[BACKUP_DEVICE_MAX_NUM];
Static T_HisBackStruct s_atHisBackupAlarm[BACKUP_DEVICE_MAX_NUM];
Static T_HisBackStruct s_atHisBackupAct[BACKUP_DEVICE_MAX_NUM];
Static T_HisBackStruct s_atBduRecordBackup[BACKUP_DEVICE_MAX_NUM];
Static T_HisBackStruct s_atExtremeBackup[BACKUP_DEVICE_MAX_NUM];
Static T_HisBackStruct s_atAnalyseBackup[BACKUP_DEVICE_MAX_NUM];

//历史互备份管理文件信息
Static T_HisBackMagStruct s_atHisDataMag[BACKUP_DEVICE_MAX_NUM];
Static T_HisBackMagStruct s_atHisAlarmMag[BACKUP_DEVICE_MAX_NUM];
Static T_HisBackMagStruct s_atHisActMag[BACKUP_DEVICE_MAX_NUM];
Static T_HisBackMagStruct s_atBduRecordMag[BACKUP_DEVICE_MAX_NUM];
Static T_HisBackMagStruct s_atExtremeMag[BACKUP_DEVICE_MAX_NUM];
Static T_HisBackMagStruct s_atAnalyseMag[BACKUP_DEVICE_MAX_NUM];

//历史互备份文件总个数
Static char s_ucHisDataBackupFileNum;
Static char s_ucHisAlarmBackupFileNum;
Static char s_ucHisActBackupFileNum;
Static char s_ucBduRecordBackupFileNum;
Static char s_ucExtremeBackupFileNum;
Static char s_ucAnalyseBackupFileNum;

Static T_HisRecord s_atHisRecord[] = {
    {
        .wRecordSize = sizeof(T_ActionRecord),
        .pcFilename = "/hisaction",
        .wMinSaveNum = HISACT_MIN_NUM,
        .wReadPoint = {0},
    },
    {
        .wRecordSize = sizeof(T_HisAlarmSaveStruct),
        .pcFilename = "/hisalarm",
        .wMinSaveNum = HISALARM_MIN_NUM,
        .wReadPoint = {0},
    },
    {
        .wRecordSize = sizeof(T_HisDataStruct),
        .pcFilename = "/hisdata",
        .wMinSaveNum = HISDATA_MIN_NUM,
        .wReadPoint = {0},
    },
    // 直接加到这里要分析波及影响
    {
        .wRecordSize = sizeof(T_DcrRecordStruct),
        .pcFilename = "/dcrRecord",
        .wMinSaveNum = DCRDATA_MIN_NUM,
        .wReadPoint = {0},
    },
};

Static T_HisRecord s_bduRecordMgr = {
    .wRecordSize = BDU_RECORD_SIZE,
    .pcFilename = "/BduRecordData",
    .wMinSaveNum = BDU_RECORD_MIN_NUM,
    .wReadPoint = {0},
};

#ifdef HISALARM_ID_MAPPING_ENABLED
Static SHORT s_aucHisAlarmMapping[] = {
    offsetof(T_BCMAlarmStructOld, ucChgCurrHighPrt),
    offsetof(T_BCMAlarmStructOld, ucDischgCurrHighPrt),
    offsetof(T_BCMAlarmStructOld, ucBattOverVoltPrt),
    offsetof(T_BCMAlarmStructOld, ucBoardTempHighPrt),
    offsetof(T_BCMAlarmStructOld, ucChgCurrHighAlm),
    offsetof(T_BCMAlarmStructOld, ucDischgCurrHighAlm),
    offsetof(T_BCMAlarmStructOld, ucBattOverVoltAlm),
    offsetof(T_BCMAlarmStructOld, ucEnvTempHighAlm),
    offsetof(T_BCMAlarmStructOld, ucEnvTempLowAlm),
    offsetof(T_BCMAlarmStructOld, ucBattUnderVoltAlm),
    offsetof(T_BCMAlarmStructOld, ucBattUnderVoltPrt),
    offsetof(T_BCMAlarmStructOld, aucCellOverVoltAlm[0]),
    offsetof(T_BCMAlarmStructOld, aucCellOverVoltAlm[1]),
    offsetof(T_BCMAlarmStructOld, aucCellOverVoltAlm[2]),
    offsetof(T_BCMAlarmStructOld, aucCellOverVoltAlm[3]),
    offsetof(T_BCMAlarmStructOld, aucCellOverVoltAlm[4]),
    offsetof(T_BCMAlarmStructOld, aucCellOverVoltAlm[5]),
    offsetof(T_BCMAlarmStructOld, aucCellOverVoltAlm[6]),
    offsetof(T_BCMAlarmStructOld, aucCellOverVoltAlm[7]),
    offsetof(T_BCMAlarmStructOld, aucCellOverVoltAlm[8]),
    offsetof(T_BCMAlarmStructOld, aucCellOverVoltAlm[9]),
    offsetof(T_BCMAlarmStructOld, aucCellOverVoltAlm[10]),
    offsetof(T_BCMAlarmStructOld, aucCellOverVoltAlm[11]),
    offsetof(T_BCMAlarmStructOld, aucCellOverVoltAlm[12]),
    offsetof(T_BCMAlarmStructOld, aucCellOverVoltAlm[13]),
    offsetof(T_BCMAlarmStructOld, aucCellOverVoltAlm[14]),
    offsetof(T_BCMAlarmStructOld, aucCellOverVoltAlm[15]),
    offsetof(T_BCMAlarmStructOld, aucCellOverVoltPrt[0]),
    offsetof(T_BCMAlarmStructOld, aucCellOverVoltPrt[1]),
    offsetof(T_BCMAlarmStructOld, aucCellOverVoltPrt[2]),
    offsetof(T_BCMAlarmStructOld, aucCellOverVoltPrt[3]),
    offsetof(T_BCMAlarmStructOld, aucCellOverVoltPrt[4]),
    offsetof(T_BCMAlarmStructOld, aucCellOverVoltPrt[5]),
    offsetof(T_BCMAlarmStructOld, aucCellOverVoltPrt[6]),
    offsetof(T_BCMAlarmStructOld, aucCellOverVoltPrt[7]),
    offsetof(T_BCMAlarmStructOld, aucCellOverVoltPrt[8]),
    offsetof(T_BCMAlarmStructOld, aucCellOverVoltPrt[9]),
    offsetof(T_BCMAlarmStructOld, aucCellOverVoltPrt[10]),
    offsetof(T_BCMAlarmStructOld, aucCellOverVoltPrt[11]),
    offsetof(T_BCMAlarmStructOld, aucCellOverVoltPrt[12]),
    offsetof(T_BCMAlarmStructOld, aucCellOverVoltPrt[13]),
    offsetof(T_BCMAlarmStructOld, aucCellOverVoltPrt[14]),
    offsetof(T_BCMAlarmStructOld, aucCellOverVoltPrt[15]),
    offsetof(T_BCMAlarmStructOld, aucCellUnderVoltAlm[0]),
    offsetof(T_BCMAlarmStructOld, aucCellUnderVoltAlm[1]),
    offsetof(T_BCMAlarmStructOld, aucCellUnderVoltAlm[2]),
    offsetof(T_BCMAlarmStructOld, aucCellUnderVoltAlm[3]),
    offsetof(T_BCMAlarmStructOld, aucCellUnderVoltAlm[4]),
    offsetof(T_BCMAlarmStructOld, aucCellUnderVoltAlm[5]),
    offsetof(T_BCMAlarmStructOld, aucCellUnderVoltAlm[6]),
    offsetof(T_BCMAlarmStructOld, aucCellUnderVoltAlm[7]),
    offsetof(T_BCMAlarmStructOld, aucCellUnderVoltAlm[8]),
    offsetof(T_BCMAlarmStructOld, aucCellUnderVoltAlm[9]),
    offsetof(T_BCMAlarmStructOld, aucCellUnderVoltAlm[10]),
    offsetof(T_BCMAlarmStructOld, aucCellUnderVoltAlm[11]),
    offsetof(T_BCMAlarmStructOld, aucCellUnderVoltAlm[12]),
    offsetof(T_BCMAlarmStructOld, aucCellUnderVoltAlm[13]),
    offsetof(T_BCMAlarmStructOld, aucCellUnderVoltAlm[14]),
    offsetof(T_BCMAlarmStructOld, aucCellUnderVoltAlm[15]),
    offsetof(T_BCMAlarmStructOld, aucCellUnderVoltPrt[0]),
    offsetof(T_BCMAlarmStructOld, aucCellUnderVoltPrt[1]),
    offsetof(T_BCMAlarmStructOld, aucCellUnderVoltPrt[2]),
    offsetof(T_BCMAlarmStructOld, aucCellUnderVoltPrt[3]),
    offsetof(T_BCMAlarmStructOld, aucCellUnderVoltPrt[4]),
    offsetof(T_BCMAlarmStructOld, aucCellUnderVoltPrt[5]),
    offsetof(T_BCMAlarmStructOld, aucCellUnderVoltPrt[6]),
    offsetof(T_BCMAlarmStructOld, aucCellUnderVoltPrt[7]),
    offsetof(T_BCMAlarmStructOld, aucCellUnderVoltPrt[8]),
    offsetof(T_BCMAlarmStructOld, aucCellUnderVoltPrt[9]),
    offsetof(T_BCMAlarmStructOld, aucCellUnderVoltPrt[10]),
    offsetof(T_BCMAlarmStructOld, aucCellUnderVoltPrt[11]),
    offsetof(T_BCMAlarmStructOld, aucCellUnderVoltPrt[12]),
    offsetof(T_BCMAlarmStructOld, aucCellUnderVoltPrt[13]),
    offsetof(T_BCMAlarmStructOld, aucCellUnderVoltPrt[14]),
    offsetof(T_BCMAlarmStructOld, aucCellUnderVoltPrt[15]),
    offsetof(T_BCMAlarmStructOld, aucChgTempHighAlm[0]),
    offsetof(T_BCMAlarmStructOld, aucChgTempHighAlm[1]),
    offsetof(T_BCMAlarmStructOld, aucChgTempHighAlm[2]),
    offsetof(T_BCMAlarmStructOld, aucChgTempHighAlm[3]), 
    -1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,
    offsetof(T_BCMAlarmStructOld, aucChgTempHighPrt[0]), 
    offsetof(T_BCMAlarmStructOld, aucChgTempHighPrt[1]), 
    offsetof(T_BCMAlarmStructOld, aucChgTempHighPrt[2]),
    offsetof(T_BCMAlarmStructOld, aucChgTempHighPrt[3]),
    -1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,
    offsetof(T_BCMAlarmStructOld, aucDischgTempHighAlm[0]),
    offsetof(T_BCMAlarmStructOld, aucDischgTempHighAlm[1]),
    offsetof(T_BCMAlarmStructOld, aucDischgTempHighAlm[2]),
    offsetof(T_BCMAlarmStructOld, aucDischgTempHighAlm[3]),
    -1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,
    offsetof(T_BCMAlarmStructOld, aucDischgTempHighPrt[0]),
    offsetof(T_BCMAlarmStructOld, aucDischgTempHighPrt[1]),
    offsetof(T_BCMAlarmStructOld, aucDischgTempHighPrt[2]),
    offsetof(T_BCMAlarmStructOld, aucDischgTempHighPrt[3]),
    -1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,
    offsetof(T_BCMAlarmStructOld, aucChgTempLowAlm[0]),
    offsetof(T_BCMAlarmStructOld, aucChgTempLowAlm[1]),
    offsetof(T_BCMAlarmStructOld, aucChgTempLowAlm[2]),
    offsetof(T_BCMAlarmStructOld, aucChgTempLowAlm[3]),
    -1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,
    offsetof(T_BCMAlarmStructOld, aucChgTempLowPrt[0]),
    offsetof(T_BCMAlarmStructOld, aucChgTempLowPrt[1]),
    offsetof(T_BCMAlarmStructOld, aucChgTempLowPrt[2]),
    offsetof(T_BCMAlarmStructOld, aucChgTempLowPrt[3]),
    -1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,
    offsetof(T_BCMAlarmStructOld, aucDischgTempLowAlm[0]),
    offsetof(T_BCMAlarmStructOld, aucDischgTempLowAlm[1]),
    offsetof(T_BCMAlarmStructOld, aucDischgTempLowAlm[2]),
    offsetof(T_BCMAlarmStructOld, aucDischgTempLowAlm[3]),
    -1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,
    offsetof(T_BCMAlarmStructOld, aucDischgTempLowPrt[0]),
    offsetof(T_BCMAlarmStructOld, aucDischgTempLowPrt[1]),
    offsetof(T_BCMAlarmStructOld, aucDischgTempLowPrt[2]),
    offsetof(T_BCMAlarmStructOld, aucDischgTempLowPrt[3]),
    -1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,
    offsetof(T_BCMAlarmStructOld, ucCellPoorConsisAlm),
    offsetof(T_BCMAlarmStructOld, ucCellPoorConsisPrt),
    offsetof(T_BCMAlarmStructOld, ucBattSOCLowAlm),
    offsetof(T_BCMAlarmStructOld, ucBattSOCLowPrt),
    offsetof(T_BCMAlarmStructOld, ucBattSOHAlm),
    offsetof(T_BCMAlarmStructOld, ucBattSOHPrt),
    offsetof(T_BCMAlarmStructOld, aucCellDamagePrt[0]),
    offsetof(T_BCMAlarmStructOld, aucCellDamagePrt[1]),
    offsetof(T_BCMAlarmStructOld, aucCellDamagePrt[2]),
    offsetof(T_BCMAlarmStructOld, aucCellDamagePrt[3]),
    offsetof(T_BCMAlarmStructOld, aucCellDamagePrt[4]),
    offsetof(T_BCMAlarmStructOld, aucCellDamagePrt[5]),
    offsetof(T_BCMAlarmStructOld, aucCellDamagePrt[6]),
    offsetof(T_BCMAlarmStructOld, aucCellDamagePrt[7]),
    offsetof(T_BCMAlarmStructOld, aucCellDamagePrt[8]),
    offsetof(T_BCMAlarmStructOld, aucCellDamagePrt[9]),
    offsetof(T_BCMAlarmStructOld, aucCellDamagePrt[10]),
    offsetof(T_BCMAlarmStructOld, aucCellDamagePrt[11]),
    offsetof(T_BCMAlarmStructOld, aucCellDamagePrt[12]),
    offsetof(T_BCMAlarmStructOld, aucCellDamagePrt[13]),
    offsetof(T_BCMAlarmStructOld, aucCellDamagePrt[14]),
    offsetof(T_BCMAlarmStructOld, aucCellDamagePrt[15]),
    offsetof(T_BCMAlarmStructOld, ucBattLoseAlm),
    offsetof(T_BCMAlarmStructOld, ucBduEepromAlm),
    offsetof(T_BCMAlarmStructOld, ucCellVoltSampleFault),
    offsetof(T_BCMAlarmStructOld, ucChgLoopInvalid),
    offsetof(T_BCMAlarmStructOld, ucDischgLoopInvalid),
    offsetof(T_BCMAlarmStructOld, ucCurrLimLoopInvalid),
    offsetof(T_BCMAlarmStructOld, ucBattShortCut),
    offsetof(T_BCMAlarmStructOld, ucBattReverse),
    offsetof(T_BCMAlarmStructOld, ucCellTempSensorInvalidAlm[0]),
    offsetof(T_BCMAlarmStructOld, ucCellTempSensorInvalidAlm[1]),
    offsetof(T_BCMAlarmStructOld, ucCellTempSensorInvalidAlm[2]),
    offsetof(T_BCMAlarmStructOld, ucCellTempSensorInvalidAlm[3]),
    -1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,
    offsetof(T_BCMAlarmStructOld, ucInsideTempHighPrt),
    offsetof(T_BCMAlarmStructOld, ucBDUBattVoltLowPrt),
    offsetof(T_BCMAlarmStructOld, ucCellTempAbnormal),
    offsetof(T_BCMAlarmStructOld, ucAddressClash),
    offsetof(T_BCMAlarmStructOld, ucShakeAlarm),
    offsetof(T_BCMAlarmStructOld, ucBDUBusVoltLowPrt),
    offsetof(T_BCMAlarmStructOld, ucBDUBusVoltHighPrt),
    offsetof(T_BCMAlarmStructOld, ucBDUCommFail),
    offsetof(T_BCMAlarmStructOld, ucBattVoltSampleAlm),
    offsetof(T_BCMAlarmStructOld, ucLoopFault),
    offsetof(T_BCMAlarmStructOld, ucBDUBattChgVoltLowPrt),
    offsetof(T_BCMAlarmStructOld, ucBDUBattLockAlm),
    offsetof(T_BCMAlarmStructOld, ucEnvTempHighPrt),
    offsetof(T_BCMAlarmStructOld, ucEnvTempLowPrt),
    offsetof(T_BCMAlarmStructOld, ucBoardTempHighAlm),
    offsetof(T_BCMAlarmStructOld, ucEqualCircuitFaultAlm),
    offsetof(T_BCMAlarmStructOld, ucBalanceResisTempHighPrt),
    offsetof(T_BCMAlarmStructOld, ucHeaterFilmFailure),
    offsetof(T_BCMAlarmStructOld, ucBDUConnTempHighPrt),
    offsetof(T_BCMAlarmStructOld, ucMainRelayFail),
    offsetof(T_BCMAlarmStructOld, ucDCDCErr),
    offsetof(T_BCMAlarmStructOld, ucSampleErr),
    offsetof(T_BCMAlarmStructOld, ucAuxiSourceErr),
    offsetof(T_BCMAlarmStructOld, aucCellDynamicUnderVoltPrt[0]),
    offsetof(T_BCMAlarmStructOld, aucCellDynamicUnderVoltPrt[1]),
    offsetof(T_BCMAlarmStructOld, aucCellDynamicUnderVoltPrt[2]),
    offsetof(T_BCMAlarmStructOld, aucCellDynamicUnderVoltPrt[3]),
    offsetof(T_BCMAlarmStructOld, aucCellDynamicUnderVoltPrt[4]),
    offsetof(T_BCMAlarmStructOld, aucCellDynamicUnderVoltPrt[5]),
    offsetof(T_BCMAlarmStructOld, aucCellDynamicUnderVoltPrt[6]),
    offsetof(T_BCMAlarmStructOld, aucCellDynamicUnderVoltPrt[7]),
    offsetof(T_BCMAlarmStructOld, aucCellDynamicUnderVoltPrt[8]),
    offsetof(T_BCMAlarmStructOld, aucCellDynamicUnderVoltPrt[9]),
    offsetof(T_BCMAlarmStructOld, aucCellDynamicUnderVoltPrt[10]),
    offsetof(T_BCMAlarmStructOld, aucCellDynamicUnderVoltPrt[11]),
    offsetof(T_BCMAlarmStructOld, aucCellDynamicUnderVoltPrt[12]),
    offsetof(T_BCMAlarmStructOld, aucCellDynamicUnderVoltPrt[13]),
    offsetof(T_BCMAlarmStructOld, aucCellDynamicUnderVoltPrt[14]),
    offsetof(T_BCMAlarmStructOld, aucCellDynamicUnderVoltPrt[15]),
    offsetof(T_BCMAlarmStructOld, ucFireControlAlm),                 //  消防告警
    offsetof(T_BCMAlarmStructOld, ucActivePortCurrError),            //  激活回路电流异常保护
    offsetof(T_BCMAlarmStructOld, ucCellTempRiseAbnormal),
    offsetof(T_BCMAlarmStructOld, ucDcrFaultAlm),
    offsetof(T_BCMAlarmStructOld, ucDcrFaultPrt),
    offsetof(T_BCMAlarmStructOld, ucSelfDischFualt),
    offsetof(T_BCMAlarmStructOld, ucCapDCPRFaultAlm),
	offsetof(T_BCMAlarmStructOld, ucBattFaultTempHighAlm),
	offsetof(T_BCMAlarmStructOld, ucFireControlFaultAlm),
	offsetof(T_BCMAlarmStructOld, ucActivatePortReverseAlm),
	offsetof(T_BCMAlarmStructOld, ucSiteAntitheftAlm),
    offsetof(T_BCMAlarmStructOld, ucGPSFaultAlm),
    offsetof(T_BCMAlarmStructOld, ucGyrFaultAlm),
};
#endif

Static void UpdateAnalyticalAlmReset(void);

Static void SaveHisDataRecord(T_HisDataStruct *ptHisData);
Static void SaveOneTempHisData(rt_uint8_t ucIndex);
static void ExtractHisData(void);

/* 获取时间段历史记录相关函数 */
Static void GetReadPointTime(WORD wReadPoint, T_TimeStruct *ptBlockTime, BYTE ucHisRecordType);
Static SHORT BinaryFindTimePoint(WORD wLow, WORD wHigh, T_TimeStruct tInputTime, BOOLEAN bUseHigh, BYTE ucHisRecordType);

/* 极值记录相关函数 */
Static BOOLEAN SaveExtremeData(void);
static void SetFloatDefault(float *fpInputValue);
Static void SetExtremeDefault(void);

Static uint16_t MakeHisDataBattStatus(const T_BCMDataStruct *ptBcmData, const T_BCMAlarmStruct *ptBcmAlarm);
Static uint8_t MakeHisDataCellEvent(const T_BCMDataStruct *ptBcmData, const T_BCMAlarmStruct *ptBcmAlarm, uint8_t ucCellVoltNum);
Static uint8_t MakeHisDataVoltEvent(const T_BCMAlarmStruct *ptBcmAlarm, uint8_t ucCellVoltNum);
Static uint16_t MakeHisDataTempEvent(const T_BCMAlarmStruct *ptBcmAlarm, uint8_t ucCellTempNum);
Static uint8_t MakeHisDataCurrEvent(const T_BCMAlarmStruct *ptBcmAlarm);
Static uint8_t MakeHisDataCapacityEvent(const T_BCMAlarmStruct *ptBcmAlarm);
Static uint16_t MakeHisDataCellTempEvent(const T_BCMAlarmStruct *ptBcmAlarm, uint8_t ucCellTempNum);

#ifdef HISALARM_ID_MAPPING_ENABLED
SHORT* CopyBCMAlarmStructOld(void)
{
    return &s_aucHisAlarmMapping[0];
}
#endif

/* 录波存储相关函数 */
//static int BduRecordToBytes(void *pDest, const T_BduRecordData *ptData);

/***************************************************************************
 * @brief    保存一条历史数据
 * @param    {T_HisDataStruct} *ptHisData---待存的历史数据
 **************************************************************************/
Static void SaveHisDataRecord(T_HisDataStruct *ptHisData)
{
    T_HisRecord *pRecord = &s_atHisRecord[2];

    static time_t s_tTimer = 0;

    if (ptHisData == NULL)
    {
        return;
    }

    if ((ptHisData->tStartTime == 0) || (ptHisData->tStartTime == s_tTimer)) // 该条历史数据保存过
    {
        return;
    }

    ptHisData->wCheckSum = CRC_Cal((void *)ptHisData, offsetof(T_HisDataStruct, wCheckSum));

    s_tTimer = ptHisData->tStartTime;
    pRecord->pucData = (rt_uint8_t *)ptHisData;
    saveOneRecord(pRecord);
    rt_thread_delay(200);  //等待互备份保存，防止丢包
    setBackUpHisData(ptHisData);
    return;
}

/***************************************************************************
 * @brief    保存一条直流内阻记录
 * @param    {T_DcrRecordStruct} *ptHisData---待存的直流内阻记录
 **************************************************************************/
BOOLEAN SaveDcrRecord(T_DcrRecordStruct *ptDcrRecord)
{
    static time_t s_tTimer = 0;
    T_HisRecord *pRecord = &s_atHisRecord[3];
    time_t lStartTime;
    time(&lStartTime);

    if (ptDcrRecord == NULL)
    {
        return FALSE;
    }

    ptDcrRecord->tStartTime = lStartTime;

    if ((ptDcrRecord->tStartTime == 0) || (ptDcrRecord->tStartTime == s_tTimer)) // 该条直流内阻记录保存过
    {
        return FALSE;
    }

    s_tTimer = ptDcrRecord->tStartTime;
    pRecord->pucData = (rt_uint8_t *)ptDcrRecord;
    saveOneRecord(pRecord);
    return TRUE;
}

/***************************************************************************
 * @brief    保存缓冲区内的历史数据(TempHisData)
 * @param    {rt_uint8_t} ucIndex---临时历史数据下标
 **************************************************************************/
Static void SaveOneTempHisData(rt_uint8_t ucIndex)
{
    SaveHisDataRecord(&s_atTempHisData[ucIndex]);
    s_atTempHisData[ucIndex].tStartTime = 0;

    return;
}

#ifndef DEVICE_USING_R321
/**
 * @brief 提取历史数据到静态结构体中（D121）
 * 
 */
static void ExtractHisData(void)
{
    rt_uint8_t i;
    rt_uint8_t ucTmp = 0;
    rt_uint8_t ucTmp1 = 0;
    time_t lStartTime;

    T_BattResult tBattResult;
    GetBattResult(&tBattResult);

    time(&lStartTime);
    s_tHisData.tStartTime = lStartTime;

    GetRealData(&s_tBcmData);
    GetRealAlarm(BCM_ALARM, (rt_uint8_t *)&s_tBcmAlarm);
    s_tHisData.ucSysMode = s_tBcmData.ucBattPackSta;    //系统模式        
    s_tHisData.ucCellEvent = 0;
    for (i = 0; i < s_tHardwarePara.ucCellVoltNum; i++) 
    {
        s_tHisData.awCellVolt[i] = (WORD)(s_tBcmData.afCellVolt[i] * 1000);
    }

    s_tHisData.wStatus = MakeHisDataBattStatus(&s_tBcmData, &s_tBcmAlarm);
    s_tHisData.ucCellEvent = MakeHisDataCellEvent(&s_tBcmData, &s_tBcmAlarm, s_tHardwarePara.ucCellVoltNum);

    for (i = 0; i < s_tHardwarePara.ucCellTempNum; i++)
    {
        s_tHisData.awCellTemp[i] =  (WORD)(s_tBcmData.afCellTemp[i]*10 + 2731);
    }

    s_tHisData.wTempEvent = MakeHisDataTempEvent(&s_tBcmAlarm, s_tHardwarePara.ucCellTempNum);

    s_tHisData.ucCurrentEvent = MakeHisDataCurrEvent(&s_tBcmAlarm);

    s_tHisData.ucCapacityEvent = MakeHisDataCapacityEvent(&s_tBcmAlarm);

    s_tHisData.uiFaultStatus = 0x00;            //故障状态

    s_tHisData.wBattCurr = (short)(s_tBcmData.fBattCurr * 100);           //充放电流
    s_tHisData.wBattVolt = (short)(s_tBcmData.fBattVolt * 100);           //电池电压
    s_tHisData.wBattSOC = GetRealBattCap();                               //剩余容量 电池SOC  默认存储为s_tBattDeal.fBattCap*100，后续依据协议差异计算
    s_tHisData.ucCellTempNum = s_tHardwarePara.ucCellTempNum;             //电芯温度数量
    s_tHisData.sEnvTemp = (short)(s_tBcmData.fEnvTemp *10);               //环境温度      默认存储为s_tBcmData.fEnvTemp*10，后续依据协议差异计算
    s_tHisData.sPowerTemp = (short)(s_tBcmData.fBoardTemp * 100);         //功率温度      默认存储为s_tBcmData.fBoardTemp*100，后续依据协议差异计算
    s_tHisData.ucCellVoltNum = s_tHardwarePara.ucCellVoltNum;             //电芯电压数量
    s_tHisData.wBusVolt = (short)(s_tBcmData.fExterVolt * 100);           //母排电压
    s_tHisData.wBusCurr = (short)(s_tBcmData.fBusCurr*100);               //母排电流
    s_tHisData.wCellVoltMin = (short)(s_tBcmData.fCellVoltMin*1000);      //电芯电压最小值
    s_tHisData.wCellVoltMax = (short)(s_tBcmData.fCellVoltMax*1000);      //电芯电压最大值
    s_tHisData.wBattCycleTimes = s_tBcmData.wBattCycleTimes;              //电池循环次数
    s_tHisData.wBattBalanceStatus = tBattResult.wCellBalVoltBits;         //16节单体均衡状态
    ucTmp = 0;
    //限流状态
    ucTmp |= s_tBcmData.ucLimit;
    //bms充放电状态:bit1~bit4
    ucTmp1 = s_tBcmData.ucBduStatus;
    ucTmp |=  ucTmp1 << 1;
    //充电保护状态
    ucTmp |= s_tBcmData.ucChgProtSta << 5;
    //放电保护状态
    ucTmp |= s_tBcmData.ucDischgProtSta << 6;
    s_tHisData.ucBattStatus = ucTmp;                     //电池状态
    s_tHisData.wPowerOnVoltThre = s_tBcmData.fPowerOnVoltThre * 100;      //来电电压阈值
    s_tHisData.wPowerDownVoltThre = s_tBcmData.fPowerDownVoltThre * 100;  //停电电压阈值
    s_tHisData.sConnTemp = (short)(s_tBcmData.fConnTemp * 100);           //连接器温度
    s_tHisData.wDischgSetVol =  (short)(s_tBcmData.fSetDischVolt * 100);  //放电设定电压
    s_tHisData.wSetChgCurr = s_tBcmData.wSetChgCurr;                      //充电限流千分比
    return;
}
#else
/**
 * @brief 提取历史数据到静态结构体中（R321）
 * @details 数据涵盖1363协议和铁塔协议
 */
static void ExtractHisData(void)
{
    rt_uint8_t i;

    FLOAT fCellVolMax, fCellVolMin;
    rt_uint8_t ucCellVolMaxNo, ucCellVolMinNo;
    FLOAT fCellTempMax, fCellTempMin;
    rt_uint8_t ucCellTempMaxNo, ucCellTempMinNo;
    time_t lStartTime;
    T_BattResult tBattResult;

    GetBattResult(&tBattResult);
    GetRealData(&s_tBcmData);
    GetRealAlarm(BCM_ALARM, (BYTE *)&s_tBcmAlarm);

    time(&lStartTime);
    s_tHisData.tStartTime = lStartTime;
    s_tHisData.ucSysMode = s_tBcmData.ucBattPackSta;    //系统模式

    s_tHisData.ucCellVoltNum = s_tHardwarePara.ucCellVoltNum;
    s_tHisData.ucCellTempNum = s_tHardwarePara.ucCellTempNum;
    s_tHisData.ucReservedSize = HISDATA_RESERVED_SIZE;

    fCellVolMax = 0.0;
    fCellVolMin = 20.0;
    ucCellVolMaxNo = 0;
    ucCellVolMinNo = 0;
    for (i = 0; i < CELL_VOL_NUM; i++) 
    {
        if (fCellVolMax < s_tBcmData.afCellVolt[i])
        {
            fCellVolMax = s_tBcmData.afCellVolt[i];
            ucCellVolMaxNo = i;
        }
        if (fCellVolMin > s_tBcmData.afCellVolt[i])
        {
            fCellVolMin = s_tBcmData.afCellVolt[i];
            ucCellVolMinNo = i;
        }
    }
    s_tHisData.wCellVoltMax = (short)(fCellVolMax * 1000);
    s_tHisData.wCellVoltMin = (short)(fCellVolMin * 1000);
    s_tHisData.ucCellVoltMaxNo = ucCellVolMaxNo + 1;
    s_tHisData.ucCellVoltMinNo = ucCellVolMinNo + 1;

    fCellTempMax = -127.0;
    fCellTempMin = 127.0;
    ucCellTempMaxNo = 0;
    ucCellTempMinNo = 0;
    for (i = 0; i < CELL_TEMP_NUM; i++)
    {
        if (fCellTempMax < s_tBcmData.afCellTemp[i])
        {
            fCellTempMax = s_tBcmData.afCellTemp[i];
            ucCellTempMaxNo = i;
        }
        if (fCellTempMin > s_tBcmData.afCellTemp[i])
        {
            fCellTempMin = s_tBcmData.afCellTemp[i];
            ucCellTempMinNo = i;
        }
    }
    s_tHisData.wCellTempMax = (short)(fCellTempMax * 10);
    s_tHisData.wCellTempMin = (short)(fCellTempMin * 10);
    s_tHisData.ucCellTempMaxNo = ucCellTempMaxNo + 1;
    s_tHisData.ucCellTempMinNo = ucCellTempMinNo + 1;

    s_tHisData.wBattCurr = (short)(s_tBcmData.fBattCurr * 100);
    s_tHisData.wBattVolt = (short)(s_tBcmData.fBattVolt * 100);
    s_tHisData.wBusVolt = (short)(s_tBcmData.fExterVolt * 100);
    s_tHisData.wBusCurr = (short)(s_tBcmData.fBusCurr * 100);
    s_tHisData.wBattCycleTimes = s_tBcmData.wBattCycleTimes;
    s_tHisData.wBattSOC = s_tBcmData.wBattSOC;
    s_tHisData.wBattSOH = s_tBcmData.wBattSOH;
    s_tHisData.ucBattStatus = s_tBcmData.ucBattPackSta;    //电池模式
    s_tHisData.sEnvTemp = (short)(s_tBcmData.fEnvTemp); 
    s_tHisData.sPowerTemp = (short)(s_tBcmData.fBoardTemp * 100);

    s_tHisData.wStatus = MakeHisDataBattStatus(&s_tBcmData, &s_tBcmAlarm);
    s_tHisData.ucCellEvent = MakeHisDataCellEvent(&s_tBcmData, &s_tBcmAlarm, s_tHardwarePara.ucCellVoltNum);

    for (i = 0; i < s_tHardwarePara.ucCellVoltNum; i++) 
    {
        s_tHisData.awCellVolt[i] = (WORD)(s_tBcmData.afCellVolt[i] * 1000);    //电芯电压
    }

    s_tHisData.ucCellVolEvent = MakeHisDataVoltEvent(&s_tBcmAlarm, s_tHardwarePara.ucCellVoltNum);

    for (i = 0; i < s_tHardwarePara.ucCellTempNum; i++)
    {
        s_tHisData.awCellTemp[i] =  (WORD)(s_tBcmData.afCellTemp[i]*10 + 2731);    //电芯温度
    }

    s_tHisData.wTempEvent = MakeHisDataTempEvent(&s_tBcmAlarm, s_tHardwarePara.ucCellTempNum);
    s_tHisData.ucCurrentEvent = MakeHisDataCurrEvent(&s_tBcmAlarm);
    s_tHisData.ucCapacityEvent = MakeHisDataCapacityEvent(&s_tBcmAlarm);

    s_tHisData.ulTotalChgQuantity = (uint32_t)(tBattResult.fTotalChargeQuanty * 100);
    s_tHisData.ulTotalDischgQuantity = (uint32_t)(tBattResult.fTotalDischargeQuanty * 100);
}
#endif

/****************************************************************************
* 函数名称：HisBackupDataProcess
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：保存历史互备份数据过程
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
BOOLEAN HisBackupDataProcess(BYTE ucNum, BYTE *pfilename, BYTE* pstr, WORD wLen)
{
    time_t lLastTime;
    rt_int32_t fd;

    time(&lLastTime);
    // 更新管理文件内容
    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);
    fd = open(HISDATA_BACKUP_MANAGE_FILE, O_RDWR | O_CREAT);
    if (fd < 0)
    {
        rt_mutex_release(s_ptMutexFile);
        return False;
    }
    if (GPIO_OFF == GetPMOSStatus())
    {
        close(fd);
        rt_mutex_release(s_ptMutexFile);
        return False;
    }
    rt_memcpy(s_atHisDataMag[ucNum].ucFileName, pfilename, rt_strnlen_s((char*)pfilename, BACKUP_FILE_NAME_MAX_LEN));
    s_atHisDataMag[ucNum].wSavedNum++;
    s_atHisDataMag[ucNum].tLastTime = lLastTime;
    write(fd, s_atHisDataMag, sizeof(s_atHisDataMag[0]) * BACKUP_DEVICE_MAX_NUM); 
    close(fd);
    rt_mutex_release(s_ptMutexFile);

    //保存历史互备份数据
    rt_memset(&s_atHisBackupData[ucNum], 0, sizeof(s_atHisBackupData[0]));
    rt_memcpy(s_atHisBackupData[ucNum].ucFileName, pfilename, rt_strnlen_s((char*)pfilename,sizeof(s_atHisBackupData[ucNum].ucFileName)));
    s_atHisBackupData[ucNum].wRecordSize = wLen;
    s_atHisBackupData[ucNum].wSavedNum = s_atHisDataMag[ucNum].wSavedNum;
    saveOneBackupRecord(&s_atHisBackupData[ucNum], pstr, HISDATA_BACKUP_INDEX);

    return True;
}

/****************************************************************************
* 函数名称：HisBackupAlarmProcess
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：保存历史互备份告警过程
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
BOOLEAN HisBackupAlarmProcess(BYTE ucNum, BYTE *pfilename, BYTE* pstr, WORD wLen)
{
    time_t lLastTime;
    rt_int32_t fd;

    time(&lLastTime);
    //更新管理文件内容
    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);
    fd = open(HISALARM_BACKUP_MANAGE_FILE, O_RDWR | O_CREAT);
    if (fd < 0)
    {
        rt_mutex_release(s_ptMutexFile);
        return False;
    }
    if (GPIO_OFF == GetPMOSStatus())
    {
        close(fd);
        rt_mutex_release(s_ptMutexFile);
        return False;
    }
    rt_memcpy(s_atHisAlarmMag[ucNum].ucFileName, pfilename, rt_strnlen_s((char*)pfilename, BACKUP_FILE_NAME_MAX_LEN));
    s_atHisAlarmMag[ucNum].wSavedNum++;
    s_atHisAlarmMag[ucNum].tLastTime = lLastTime;
    write(fd, s_atHisAlarmMag, sizeof(s_atHisAlarmMag[0]) * BACKUP_DEVICE_MAX_NUM); 
    close(fd);
    rt_mutex_release(s_ptMutexFile);

    //保存历史互备份告警
    rt_memset(&s_atHisBackupAlarm[ucNum], 0, sizeof(s_atHisBackupAlarm[0]));
    rt_memcpy(s_atHisBackupAlarm[ucNum].ucFileName, pfilename, rt_strnlen_s((char*)pfilename,sizeof(s_atHisBackupAlarm[ucNum].ucFileName)));
    s_atHisBackupAlarm[ucNum].wRecordSize = wLen;
    s_atHisBackupAlarm[ucNum].wSavedNum = s_atHisAlarmMag[ucNum].wSavedNum;
    saveOneBackupRecord(&s_atHisBackupAlarm[ucNum], pstr, HISALARM_BACKUP_INDEX);

    return True;
}

/****************************************************************************
* 函数名称：HisBackupActProcess
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：保存历史互备份操作记录过程
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
BOOLEAN HisBackupActProcess(BYTE ucNum, BYTE *pfilename, BYTE* pstr, WORD wLen)
{
    time_t lLastTime;
    rt_int32_t fd;

    time(&lLastTime);
    //更新管理文件内容
    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);
    fd = open(HISACT_BACKUP_MANAGE_FILE, O_RDWR | O_CREAT);
    if (fd < 0)
    {
        rt_mutex_release(s_ptMutexFile);
        return False;
    }
    if (GPIO_OFF == GetPMOSStatus())
    {
        close(fd);
        rt_mutex_release(s_ptMutexFile);
        return False;
    }
    rt_memcpy(s_atHisActMag[ucNum].ucFileName, pfilename, rt_strnlen_s((char*)pfilename, BACKUP_FILE_NAME_MAX_LEN));
    s_atHisActMag[ucNum].wSavedNum++;
    s_atHisActMag[ucNum].tLastTime = lLastTime;
    write(fd, s_atHisActMag, sizeof(s_atHisActMag[0]) * BACKUP_DEVICE_MAX_NUM); 
    close(fd);
    rt_mutex_release(s_ptMutexFile);

    //保存历史互备份操作记录
    rt_memset(&s_atHisBackupAct[ucNum], 0, sizeof(s_atHisBackupAct[0]));
    rt_memcpy(s_atHisBackupAct[ucNum].ucFileName, pfilename, rt_strnlen_s((char*)pfilename,sizeof(s_atHisBackupAlarm[ucNum].ucFileName)));
    s_atHisBackupAct[ucNum].wRecordSize = wLen;
    s_atHisBackupAct[ucNum].wSavedNum = s_atHisActMag[ucNum].wSavedNum;
    saveOneBackupRecord(&s_atHisBackupAct[ucNum], pstr, HISACT_BACKUP_INDEX);

    return True;
}

/****************************************************************************
* 函数名称：BduRecBackupProcess
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：保存录波互备份操作记录过程
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
BOOLEAN BduRecBackupProcess(BYTE ucNum, BYTE *pfilename, BYTE* pstr, WORD wLen)
{
    time_t lLastTime;
    rt_int32_t fd;

    RETURN_VAL_IF_FAIL(pfilename != NULL && pstr != NULL, False);

    time(&lLastTime);
    //更新管理文件内容
    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);
    fd = open(BDURECORD_BACKUP_MANAGE_FILE, O_RDWR | O_CREAT);
    if (fd < 0)
    {
        rt_mutex_release(s_ptMutexFile);
        return False;
    }
    if (GPIO_OFF == GetPMOSStatus())
    {
        close(fd);
        rt_mutex_release(s_ptMutexFile);
        return False;
    }
    rt_memcpy(s_atBduRecordMag[ucNum].ucFileName, pfilename, rt_strnlen_s((char*)pfilename, BACKUP_FILE_NAME_MAX_LEN));
    s_atBduRecordMag[ucNum].wSavedNum++;
    s_atBduRecordMag[ucNum].tLastTime = lLastTime;
    write(fd, s_atBduRecordMag, sizeof(s_atBduRecordMag[0]) * BACKUP_DEVICE_MAX_NUM); 
    close(fd);
    rt_mutex_release(s_ptMutexFile);

    //保存录波互备份操作记录
    rt_memset(&s_atBduRecordBackup[ucNum], 0, sizeof(s_atBduRecordBackup[0]));
    rt_memcpy(s_atBduRecordBackup[ucNum].ucFileName, pfilename, rt_strnlen_s((char*)pfilename, BACKUP_FILE_NAME_MAX_LEN));
    s_atBduRecordBackup[ucNum].wRecordSize = wLen;
    s_atBduRecordBackup[ucNum].wSavedNum = s_atBduRecordMag[ucNum].wSavedNum;
    saveOneBackupRecord(&s_atBduRecordBackup[ucNum], pstr, BDURECORD_BACKUP_INDEX);

    return True;
}

/* 保存电芯统计记录互备份记录过程 */
BOOLEAN AnalyseBackupProcess(BYTE ucNum, BYTE *pfilename, BYTE* pstr, WORD wLen)
{
    time_t lLastTime;
    rt_int32_t fd;

    time(&lLastTime);
    //更新管理文件内容
    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);
    fd = open(ANALYSE_BACKUP_MANAGE_FILE, O_RDWR | O_CREAT);
    if (fd < 0)
    {
        rt_mutex_release(s_ptMutexFile);
        return False;
    }
    if (GPIO_OFF == GetPMOSStatus())
    {
        close(fd);
        rt_mutex_release(s_ptMutexFile);
        return False;
    }
    rt_memcpy_s(s_atAnalyseMag[ucNum].ucFileName, BACKUP_FILE_NAME_MAX_LEN, pfilename, rt_strnlen_s((char*)pfilename, BACKUP_FILE_NAME_MAX_LEN));
    s_atAnalyseMag[ucNum].wSavedNum = 1;
    s_atAnalyseMag[ucNum].tLastTime = lLastTime;
    write(fd, s_atAnalyseMag, sizeof(s_atAnalyseMag[0]) * BACKUP_DEVICE_MAX_NUM); 
    close(fd);
    rt_mutex_release(s_ptMutexFile);

    //更新备份文件
    rt_memset_s(&s_atAnalyseBackup[ucNum], sizeof(s_atAnalyseBackup[0]), 0, sizeof(s_atAnalyseBackup[0]));
    rt_memcpy_s(s_atAnalyseBackup[ucNum].ucFileName, BACKUP_FILE_NAME_MAX_LEN, pfilename, rt_strnlen_s((char*)pfilename, BACKUP_FILE_NAME_MAX_LEN));
    s_atAnalyseBackup[ucNum].wRecordSize = wLen;
    s_atAnalyseBackup[ucNum].wSavedNum = s_atAnalyseMag[ucNum].wSavedNum;
    saveAnalyseNewFile(&s_atAnalyseBackup[ucNum], pstr, wLen);

    return True;
}

/****************************************************************************
* 函数名称：WriteHisBackupData
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：保存历史互备份数据
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
BOOLEAN WriteHisBackupData(BYTE *pfilename, BYTE* pstr, WORD wLen)
{
    rt_int8_t i, j;
    rt_int8_t ucNum = 0;  //要保存历史数据的文件索引

    if (pfilename == NULL || pstr == NULL || s_ucHisDataBackupFileNum > BACKUP_DEVICE_MAX_NUM)
    {
         return False;
    }

    if (s_ucHisDataBackupFileNum < BACKUP_DEVICE_MAX_NUM) //文件小于13个时：文件名存在就保存一条备份数据，文件名不存在，就创建一个文件后再保存一条备份数据
    {
        for (i = 0; i < s_ucHisDataBackupFileNum; i++)
        {
            if(rt_memcmp(pfilename, s_atHisDataMag[i].ucFileName, rt_strnlen_s((char*)pfilename,sizeof(s_atHisDataMag[i].ucFileName))) == 0)
            {   
                ucNum = i;
                HisBackupDataProcess(ucNum, pfilename, pstr + 2, wLen - 2);
                return True;
            }
        }
        HisBackupDataProcess(s_ucHisDataBackupFileNum, pfilename, pstr + 2, wLen - 2);
        s_ucHisDataBackupFileNum++;
        return True;
    }
    else
    {
        for (i = 0; i < s_ucHisDataBackupFileNum; i++)
        {
            if(rt_memcmp(pfilename, s_atHisDataMag[i].ucFileName, rt_strnlen_s((char*)pfilename, BACKUP_FILE_NAME_MAX_LEN)) == 0)
            {
                ucNum = i;
                HisBackupDataProcess(ucNum, pfilename, pstr + 2, wLen - 2);
                return True;
            }
        }

        int oldest_device_index = 0;
        time_t oldest_timestamp = 0;
        oldest_timestamp = s_atHisDataMag[0].tLastTime;
        for (j = 1; j < BACKUP_DEVICE_MAX_NUM; j++)
        {
            if (s_atHisDataMag[j].tLastTime < oldest_timestamp)
            {
                oldest_device_index = j;
                oldest_timestamp = s_atHisDataMag[j].tLastTime;
            }
        }
        /* 删除最早那台的文件 */
        rt_memset(&s_atHisDataMag[oldest_device_index], 0, sizeof(s_atHisDataMag[oldest_device_index]));
        unlink((char*)s_atHisDataMag[oldest_device_index].ucFileName);
        HisBackupDataProcess(oldest_device_index, s_atHisDataMag[oldest_device_index].ucFileName, pstr, wLen);
    }

    return True;
}

/****************************************************************************
* 函数名称：WriteHisBackupAlarm
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：保存历史互备份告警
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
BOOLEAN WriteHisBackupAlarm(BYTE *pfilename, BYTE* pstr, WORD wLen)
{
    rt_int8_t i, j;
    rt_int8_t ucNum = 0; //要保存历史告警的文件索引
    T_TimeStruct tTime;

    if (pfilename == NULL || pstr == NULL || s_ucHisAlarmBackupFileNum > BACKUP_DEVICE_MAX_NUM)
    {
        return False;
    }

    //校验告警产生时间
    tTime.wYear = pstr[3] * 256 + pstr[4];
    tTime.ucMonth = pstr[5];
    tTime.ucDay = pstr[6];
    tTime.ucHour = pstr[7];
    tTime.ucMinute = pstr[8];
    tTime.ucSecond = pstr[9];
    if (CheckTimeValid(&tTime) == False)
    {
        return False;
    }

    //校验告警消除时间
    tTime.wYear = pstr[10] * 256 + pstr[11];
    tTime.ucMonth = pstr[12];
    tTime.ucDay = pstr[13];
    tTime.ucHour = pstr[14];
    tTime.ucMinute = pstr[15];
    tTime.ucSecond = pstr[16];
    if (CheckTimeValid(&tTime) == False)
    {
        return False;
    }
    
    if (s_ucHisAlarmBackupFileNum < BACKUP_DEVICE_MAX_NUM) //文件小于13个时：文件名存在就保存一条备份数据，文件名不存在，就创建一个文件再保存一条备份告警
    {
        for (i = 0; i < s_ucHisAlarmBackupFileNum; i++)
        {
            if(rt_memcmp(pfilename, s_atHisAlarmMag[i].ucFileName, rt_strnlen_s((char*)pfilename,sizeof(s_atHisAlarmMag[i].ucFileName))) == 0)
            {   
                ucNum = i;
                HisBackupAlarmProcess(ucNum, pfilename, pstr, wLen);
                return True;
            }
        }
        HisBackupAlarmProcess(s_ucHisAlarmBackupFileNum, pfilename, pstr, wLen);
        s_ucHisAlarmBackupFileNum++;
        return True;
    }
    else
    {
        for (i = 0; i < s_ucHisAlarmBackupFileNum; i++)
        {
            if(rt_memcmp(pfilename, s_atHisAlarmMag[i].ucFileName, rt_strnlen_s((char*)pfilename, BACKUP_FILE_NAME_MAX_LEN)) == 0)
            {   
                ucNum = i;
                HisBackupAlarmProcess(ucNum, pfilename, pstr, wLen);
                return True;
            }
        }

        int oldest_device_index = 0;
        time_t oldest_timestamp = 0;
        oldest_timestamp = s_atHisAlarmMag[0].tLastTime;
        for (j = 1; j < BACKUP_DEVICE_MAX_NUM; j++)
        {
            if (s_atHisAlarmMag[j].tLastTime < oldest_timestamp)
            {
                oldest_device_index = j;
                oldest_timestamp = s_atHisAlarmMag[j].tLastTime;
            }
        }
        /* 删除最早那台的文件 */
        rt_memset(&s_atHisAlarmMag[oldest_device_index], 0, sizeof(s_atHisAlarmMag[oldest_device_index]));
        unlink((char*)s_atHisAlarmMag[oldest_device_index].ucFileName);
        HisBackupAlarmProcess(oldest_device_index, s_atHisAlarmMag[oldest_device_index].ucFileName, pstr, wLen);
    }
    return True;
}

/****************************************************************************
* 函数名称：WriteHisBackupAct
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：保存历史互备份操作记录
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
BOOLEAN WriteHisBackupAct(BYTE *pfilename, BYTE* pstr, WORD wLen)
{
    rt_int8_t i, j;
    rt_int8_t ucNum = 0;  //要保存历史操作记录的文件索引
    T_TimeStruct tTime;

    if (pfilename == NULL || pstr == NULL || s_ucHisActBackupFileNum > BACKUP_DEVICE_MAX_NUM)
    {
        return False;
    }

    //校验时间
    tTime.wYear = pstr[BACKUP_TIME_VERIFY_INDEX] * 256 + pstr[BACKUP_TIME_VERIFY_INDEX + 1];
    tTime.ucMonth = pstr[BACKUP_TIME_VERIFY_INDEX + 2];
    tTime.ucDay = pstr[BACKUP_TIME_VERIFY_INDEX + 3];
    tTime.ucHour = pstr[BACKUP_TIME_VERIFY_INDEX + 4];
    tTime.ucMinute = pstr[BACKUP_TIME_VERIFY_INDEX + 5];
    tTime.ucSecond = pstr[BACKUP_TIME_VERIFY_INDEX + 6];
    if (CheckTimeValid(&tTime) == False)
    {
        return False;
    }
    
    if (s_ucHisActBackupFileNum < BACKUP_DEVICE_MAX_NUM) //文件小于13个时：文件名存在就保存一条备份数据，文件名不存在，就创建一个文件再保存一条备份操作记录
    {
        for (i = 0; i < s_ucHisActBackupFileNum; i++)
        {
            if(rt_memcmp(pfilename, s_atHisActMag[i].ucFileName, rt_strnlen_s((char*)pfilename,sizeof(s_atHisActMag[i].ucFileName))) == 0)
            {   
                ucNum = i;
                HisBackupActProcess(ucNum, pfilename, pstr, wLen);
                return True;
            }
        }
        HisBackupActProcess(s_ucHisActBackupFileNum, pfilename, pstr, wLen);
        s_ucHisActBackupFileNum++;
        return True;
    }
    else
    {
        for (i = 0; i < s_ucHisActBackupFileNum; i++)
        {
            if(rt_memcmp(pfilename, s_atHisActMag[i].ucFileName, rt_strnlen_s((char*)pfilename, BACKUP_FILE_NAME_MAX_LEN)) == 0)
            {   
                ucNum = i;
                HisBackupActProcess(ucNum, pfilename, pstr, wLen);
                return True;
            }
        }

        int oldest_device_index = 0;
        time_t oldest_timestamp = 0;
        oldest_timestamp = s_atHisActMag[0].tLastTime;
        for (j = 1; j < BACKUP_DEVICE_MAX_NUM; j++)
        {
            if (s_atHisActMag[j].tLastTime < oldest_timestamp)
            {
                oldest_device_index = j;
                oldest_timestamp = s_atHisActMag[j].tLastTime;
            }
        }
        /* 删除最早那台的文件 */
        rt_memset(&s_atHisActMag[oldest_device_index], 0, sizeof(s_atHisActMag[oldest_device_index]));
        unlink((char*)s_atHisActMag[oldest_device_index].ucFileName);
        HisBackupActProcess(oldest_device_index, s_atHisActMag[oldest_device_index].ucFileName, pstr, wLen);
    }
    return True;
}

/****************************************************************************
* 函数名称：WriteBduRecordBackup
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：保存录波互备份数据
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
BOOLEAN WriteBduRecordBackup(BYTE *pfilename, BYTE* pstr, WORD wLen)
{
    rt_int8_t i, j;
    rt_int8_t ucNum = 0;  //要保存录波的文件索引

    if (pfilename == NULL || pstr == NULL || s_ucBduRecordBackupFileNum > BACKUP_DEVICE_MAX_NUM)
    {
        return False;
    }
    
    if (s_ucBduRecordBackupFileNum < BACKUP_DEVICE_MAX_NUM) //文件小于32个时：文件名存在就保存一条备份数据，文件名不存在，就创建一个文件再保存一条备份操作记录
    {
        for (i = 0; i < s_ucBduRecordBackupFileNum; i++)
        {
            if(rt_memcmp(pfilename, s_atBduRecordMag[i].ucFileName, rt_strnlen_s((char*)pfilename, BACKUP_FILE_NAME_MAX_LEN)) == 0)
            {   
                ucNum = i;
                BduRecBackupProcess(ucNum, pfilename, pstr, wLen);
                return True;
            }
        }
        BduRecBackupProcess(s_ucBduRecordBackupFileNum, pfilename, pstr, wLen);
        s_ucBduRecordBackupFileNum++;
        return True;
    }
    else
    {
        for (i = 0; i < s_ucBduRecordBackupFileNum; i++)
        {
            if(rt_memcmp(pfilename, s_atBduRecordMag[i].ucFileName, rt_strnlen_s((char*)pfilename, BACKUP_FILE_NAME_MAX_LEN)) == 0)
            {   
                ucNum = i;
                BduRecBackupProcess(ucNum, pfilename, pstr, wLen);
                return True;
            }
        }

        int oldest_device_index = 0;
        time_t oldest_timestamp = 0;
        oldest_timestamp = s_atBduRecordMag[0].tLastTime;
        for (j = 1; j < BACKUP_DEVICE_MAX_NUM; j++)
        {
            if (s_atBduRecordMag[j].tLastTime < oldest_timestamp)
            {
                oldest_device_index = j;
                oldest_timestamp = s_atBduRecordMag[j].tLastTime;
            }
        }
        /* 删除最早那台的文件 */
        rt_memset(&s_atBduRecordMag[oldest_device_index], 0, sizeof(s_atBduRecordMag[oldest_device_index]));
        unlink((char*)s_atBduRecordMag[oldest_device_index].ucFileName);
        BduRecBackupProcess(oldest_device_index, s_atBduRecordMag[oldest_device_index].ucFileName, pstr, wLen);
    }
    return True;
}

/* 保存极值互备份记录 */
BOOLEAN WriteExtremeBackup(BYTE *pfilename, BYTE* pstr, WORD wLen, BYTE ucPackNum)
{
    rt_int8_t i, j;
    rt_int8_t ucNum = 0;

    if (pfilename == NULL || pstr == NULL || s_ucExtremeBackupFileNum > BACKUP_DEVICE_MAX_NUM)
    {
        return False;
    }
    
    if (s_ucExtremeBackupFileNum < BACKUP_DEVICE_MAX_NUM)
    {
        for (i = 0; i < s_ucExtremeBackupFileNum; i++)
        {
            if(rt_memcmp(pfilename, s_atExtremeMag[i].ucFileName, rt_strnlen_s((char*)pfilename, BACKUP_FILE_NAME_MAX_LEN)) == 0)
            {   
                ucNum = i;
                ExtremeBackupProcess(ucNum, pfilename, pstr, wLen, ucPackNum);
                return True;
            }
        }
        ExtremeBackupProcess(s_ucExtremeBackupFileNum, pfilename, pstr, wLen, ucPackNum);
        s_ucExtremeBackupFileNum++;
        return True;
    }
    else
    {
        for (i = 0; i < s_ucExtremeBackupFileNum; i++)
        {
            if(rt_memcmp(pfilename, s_atExtremeMag[i].ucFileName, rt_strnlen_s((char*)pfilename, BACKUP_FILE_NAME_MAX_LEN)) == 0)
            {   
                ucNum = i;
                ExtremeBackupProcess(ucNum, pfilename, pstr, wLen, ucPackNum);
                return True;
            }
        }

        int oldest_device_index = 0;
        time_t oldest_timestamp = 0;
        oldest_timestamp = s_atExtremeMag[0].tLastTime;
        for (j = 1; j < BACKUP_DEVICE_MAX_NUM; j++)
        {
            if (s_atExtremeMag[j].tLastTime < oldest_timestamp)
            {
                oldest_device_index = j;
                oldest_timestamp = s_atExtremeMag[j].tLastTime;
            }
        }
        /* 删除最早那台的文件 */
        rt_memset_s(&s_atExtremeMag[oldest_device_index], sizeof(s_atExtremeMag[oldest_device_index]), 0, sizeof(s_atExtremeMag[oldest_device_index]));
        unlink((char*)s_atExtremeMag[oldest_device_index].ucFileName);
        ExtremeBackupProcess(oldest_device_index, s_atExtremeMag[oldest_device_index].ucFileName, pstr, wLen, ucPackNum);
    }
    return True;
}

/* 保存电芯统计互备份记录 */
BOOLEAN WriteAnalyseBackup(BYTE *pfilename, BYTE* pstr, WORD wLen)
{
    rt_int8_t i, j;
    rt_int8_t ucNum = 0;

    if (pfilename == NULL || pstr == NULL || s_ucAnalyseBackupFileNum > BACKUP_DEVICE_MAX_NUM)
    {
        return False;
    }
    
    if (s_ucAnalyseBackupFileNum < BACKUP_DEVICE_MAX_NUM) //文件小于32个时：文件名存在就保存一条备份数据，文件名不存在，就创建一个文件再保存一条备份操作记录
    {
        for (i = 0; i < s_ucAnalyseBackupFileNum; i++)
        {
            if(rt_memcmp(pfilename, s_atAnalyseMag[i].ucFileName, rt_strnlen_s((char*)pfilename, BACKUP_FILE_NAME_MAX_LEN)) == 0)
            {   
                ucNum = i;
                AnalyseBackupProcess(ucNum, pfilename, pstr, wLen);
                return True;
            }
        }
        AnalyseBackupProcess(s_ucAnalyseBackupFileNum, pfilename, pstr, wLen);
        s_ucAnalyseBackupFileNum++;
        return True;
    }
    else
    {
        for (i = 0; i < s_ucAnalyseBackupFileNum; i++)
        {
            if(rt_memcmp(pfilename, s_atAnalyseMag[i].ucFileName, rt_strnlen_s((char*)pfilename, BACKUP_FILE_NAME_MAX_LEN)) == 0)
            {   
                ucNum = i;
                AnalyseBackupProcess(ucNum, pfilename, pstr, wLen);
                return True;
            }
        }

        int oldest_device_index = 0;
        time_t oldest_timestamp = 0;
        oldest_timestamp = s_atAnalyseMag[0].tLastTime;
        for (j = 1; j < BACKUP_DEVICE_MAX_NUM; j++)
        {
            if (s_atAnalyseMag[j].tLastTime < oldest_timestamp)
            {
                oldest_device_index = j;
                oldest_timestamp = s_atAnalyseMag[j].tLastTime;
            }
        }
        /* 删除最早那台的文件 */
        rt_memset(&s_atAnalyseMag[oldest_device_index], 0, sizeof(s_atAnalyseMag[oldest_device_index]));
        unlink((char*)s_atAnalyseMag[oldest_device_index].ucFileName);
        AnalyseBackupProcess(oldest_device_index, s_atAnalyseMag[oldest_device_index].ucFileName, pstr, wLen);
    }
    return True;
}

/* 保存极值互备份记录过程 */
BOOLEAN ExtremeBackupProcess(BYTE ucNum, BYTE *pfilename, BYTE* pstr, WORD wLen, BYTE ucPackNum)
{
    time_t lLastTime;
    rt_int32_t fd;

    time(&lLastTime);
    //更新管理文件内容
    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);
    fd = open(EXTREME_BACKUP_MANAGE_FILE, O_RDWR | O_CREAT);
    if (fd < 0)
    {
        rt_mutex_release(s_ptMutexFile);
        return False;
    }
    if (GPIO_OFF == GetPMOSStatus())
    {
        close(fd);
        rt_mutex_release(s_ptMutexFile);
        return False;
    }
    rt_memcpy_s(s_atExtremeMag[ucNum].ucFileName, BACKUP_FILE_NAME_MAX_LEN, pfilename, rt_strnlen_s((char*)pfilename, BACKUP_FILE_NAME_MAX_LEN));
    s_atExtremeMag[ucNum].wSavedNum = 1;
    s_atExtremeMag[ucNum].tLastTime = lLastTime;
    write(fd, s_atExtremeMag, sizeof(s_atExtremeMag[0]) * BACKUP_DEVICE_MAX_NUM); 
    close(fd);
    rt_mutex_release(s_ptMutexFile);

    //更新备份文件
    rt_memset_s(&s_atExtremeBackup[ucNum], sizeof(s_atExtremeBackup[0]), 0, sizeof(s_atExtremeBackup[0]));
    rt_memcpy_s(s_atExtremeBackup[ucNum].ucFileName, BACKUP_FILE_NAME_MAX_LEN, pfilename, rt_strnlen_s((char*)pfilename, BACKUP_FILE_NAME_MAX_LEN));
    s_atExtremeBackup[ucNum].wRecordSize = wLen;
    s_atExtremeBackup[ucNum].wSavedNum = s_atExtremeMag[ucNum].wSavedNum;
    saveExtremeNewFile(&s_atExtremeBackup[ucNum], pstr, wLen, ucPackNum);

    return True;
}

/****************************************************************************
* 函数名称：initHisBackupMagFile
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：历史互备份管理文件初始化,用来记录备份的历史文件的信息
*           将备份管理文件中的信息读到内存，使重启后能够获取到文件列表
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
void initHisBackupMagFile()
{    
    if (!IsPathExist("/BACKUP"))
    {
        if (-1 == mkdir("/BACKUP", 0755))
        {
            return;
        }
    }

    readHisBackupMagFile(HISDATA_BACKUP_MANAGE_FILE, s_atHisDataMag, &s_ucHisDataBackupFileNum, s_atHisBackupData, BACKUP_DEVICE_MAX_NUM);
    readHisBackupMagFile(HISALARM_BACKUP_MANAGE_FILE, s_atHisAlarmMag, &s_ucHisAlarmBackupFileNum, s_atHisBackupAlarm, BACKUP_DEVICE_MAX_NUM);
    readHisBackupMagFile(HISACT_BACKUP_MANAGE_FILE, s_atHisActMag, &s_ucHisActBackupFileNum, s_atHisBackupAct, BACKUP_DEVICE_MAX_NUM);
    readHisBackupMagFile(BDURECORD_BACKUP_MANAGE_FILE, s_atBduRecordMag, &s_ucBduRecordBackupFileNum, s_atBduRecordBackup, BACKUP_DEVICE_MAX_NUM);
    readHisBackupMagFile(EXTREME_BACKUP_MANAGE_FILE, s_atExtremeMag, &s_ucExtremeBackupFileNum, s_atExtremeBackup, BACKUP_DEVICE_MAX_NUM);
    readHisBackupMagFile(ANALYSE_BACKUP_MANAGE_FILE, s_atAnalyseMag, &s_ucAnalyseBackupFileNum, s_atAnalyseBackup, BACKUP_DEVICE_MAX_NUM);
}

/***************************************************************************
 * @brief    历史数据初始化
 **************************************************************************/
void initHisData(void)
{
    T_HisPoint tHisPoint;
    rt_int32_t i, fd;

    CheckHisdataVersion();  //检测历史数据的版本号是否发生变化
    rt_memset(&tHisPoint, 0, sizeof(T_HisPoint));

    rt_memset(&s_tHardwarePara, 0, sizeof(T_HardwareParaStruct));
    readBmsHWPara(&s_tHardwarePara);

#ifndef UNITEST
    for (i = 0; i < HISFILE_NUM; i++)
    {
        rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);
        fd = open((char*)s_atHisRecord[i].pcFilename, O_RDONLY | O_CREAT);
        if (fd < 0)
        {
            rt_mutex_release(s_ptMutexFile);
            return;
        }
        s_atHisRecord[i].wSavedNum = lseek(fd, 0, SEEK_END) / s_atHisRecord[i].wRecordSize;
        close(fd);
        rt_mutex_release(s_ptMutexFile);
    }
#endif
    initHisBackupMagFile();
    readHisPointInfo(&tHisPoint);

    rt_memcpy(&s_atHisRecord[0].wReadPoint[0], &tHisPoint.wReadPoint_action[0], sizeof(tHisPoint.wReadPoint_action));
    rt_memcpy(&s_atHisRecord[1].wReadPoint[0], &tHisPoint.wReadPoint_action[0], sizeof(tHisPoint.wReadPoint_alarm));
    rt_memcpy(&s_atHisRecord[2].wReadPoint[0], &tHisPoint.wReadPoint_action[0], sizeof(tHisPoint.wReadPoint_data));

    return;
}

void processHisData(void* parameter)
{
    rt_uint16_t wCounter = 0;

    rt_thread_delay(3000);
    pre_thread_beat_f(THREAD_HISDATA);
#ifndef UNITEST
    while(1)
#endif
    {
        thread_beat_go_on(THREAD_HISDATA);
        GetSysPara( &s_tSysPara );
        ExtractHisData();
        wCounter++;
        if (wCounter >= 2 * s_tSysPara.wHisDataInter)
        {
            SaveHisDataRecord(&s_tHisData);
            wCounter = 0;
#ifdef ENABLE_SYS_MONITOR
            save_sys_monitor_log();
#endif
        }
        else
        {
            rt_memcpy( &s_atTempHisData[s_ucTempHisDataIndex], &s_tHisData, sizeof(T_HisDataStruct) );
            s_ucTempHisDataIndex = (s_ucTempHisDataIndex + 1) % 2;
        }
#ifdef EXTREME_DATA_RECORD_ENABLE
        UpdateDataExtreme();
#endif

#ifdef SINGLE_BUTTON_ACTION_RECORD_ENABLED
        if (getTimeoutQuitFlag() == TRUE)
        {
            SaveKeyUpgradeTimeoutQuitAction();
            setTimeoutQuitFlag(FALSE);
        }
#endif

        rt_thread_delay(30000);

    }
}

BOOLEAN ReadHisData(T_HisDataStruct *ptHisData, record_north_protocol_e wNorthProtocolIndex)
{
    s_atHisRecord[2].pucData = (rt_uint8_t *)ptHisData;
    if (readOneRecord(&s_atHisRecord[2], wNorthProtocolIndex))
    {
        s_atHisRecord[2].wReadPoint[wNorthProtocolIndex]--;
        return True;
    }
    return False;
}

void MoveProtoHisDataPoint(rt_uint8_t ucIncNum, record_north_protocol_e wNorthProtocolIndex)
{
    s_atHisRecord[2].wReadPoint[wNorthProtocolIndex] += ucIncNum;
    if (s_atHisRecord[2].wReadPoint[wNorthProtocolIndex] > s_atHisRecord[2].wSavedNum)
    {
        s_atHisRecord[2].wReadPoint[wNorthProtocolIndex] = s_atHisRecord[2].wSavedNum;
    }
    return;
}


BOOLEAN MoveProtoDcrRecordPoint(rt_uint8_t ucIncNum, record_north_protocol_e wNorthProtocolIndex)
{
    s_atHisRecord[3].wReadPoint[wNorthProtocolIndex] += ucIncNum;
    if (s_atHisRecord[3].wReadPoint[wNorthProtocolIndex] > s_atHisRecord[3].wSavedNum)
    {
        s_atHisRecord[3].wReadPoint[wNorthProtocolIndex] = s_atHisRecord[3].wSavedNum;
    }
    return TRUE;
}

void   ResetProtoHisDataPoint(record_north_protocol_e wNorthProtocolIndex)
{
    s_atHisRecord[2].wReadPoint[wNorthProtocolIndex] = 0;
    return ;
}

rt_uint16_t GetProtoHisDataNum(record_north_protocol_e wNorthProtocolIndex)
{
    return (s_atHisRecord[2].wSavedNum - s_atHisRecord[2].wReadPoint[wNorthProtocolIndex]);
}

rt_uint16_t GetHisDataTotNum(void)
{
    return s_atHisRecord[2].wSavedNum;
}

rt_uint16_t GetDcrRecordTotNum(void)
{
    return s_atHisRecord[3].wSavedNum;
}

void DelAllHisData(void)
{
    DelAllRecords( &s_atHisRecord[2] );
    return;
}

/****************************************************************************
* 函数名称：DelAllDcrRecord
* 功能描述：删除直流内阻记录
* 修改记录：
***************************************************************************/
BOOLEAN DelAllDcrRecord(void)
{
    DelAllRecords( &s_atHisRecord[3] );
    return TRUE;
}

void SaveTempHisData(void)
{
    SaveOneTempHisData(s_ucTempHisDataIndex);
    SaveOneTempHisData((s_ucTempHisDataIndex + 1) % 2);
    return;
}

void SaveHisData(void)
{
    ExtractHisData();
    SaveHisDataRecord( &s_tHisData );
    return;
}

#ifdef DEVICE_USING_D121
WORD GetCtrlActionId(BYTE ucCtrlCode)
{
    WORD wActionId = 0;

    switch(ucCtrlCode)
    {
        case CTRL_BATTERY_CHARGE_START:
            wActionId = CONTOL_CHAG_ON;
            break;
        case CTRL_BATTERY_CHARGE_STOP:
            wActionId = CONTOL_CHAG_OFF;
            break;
        case CTRL_BATTERY_DISCHARGE_START:
            wActionId = CONTOL_DSCHAG_ON;
            break;
        case CTRL_BATTERY_DISCHARGE_STOP:
            wActionId = CONTOL_DSCHAG_OFF;
            break;
        default:
            break;
    }
    return wActionId;
}
#else
rt_uint16_t GetCtrlActionId(BYTE ucCtrlCode)
{
    rt_uint16_t wActionId = 0;
    wActionId = CONTOL_BATTERY_STATE;
    return wActionId;
}
#endif
void SaveKeyShutDownAction(void)
{
    SaveAction(GetActionId(CONTOL_SHUTDOWN), "ShutDownKey");
    return ;
}

void SaveKeySleepAction(void)
{
    SaveAction(GetActionId(CONTOL_BUTTON_SLEEP), "SleepKey");
    return ;
}

void SaveKeyDefQueryAction(void)
{
    SaveAction(GetActionId(CONTOL_KEY_DEFQUERY), "DefQueryKey");
    return ;
}

#ifdef SINGLE_BUTTON_ACTION_RECORD_ENABLED

void SaveKeyUpgradeAction(void)
{
    SaveAction(GetActionId(CONTOL_KEY_UPGRADE), "UpgradeKey");
    return ;
}

void SaveKeyUpgradeTimeoutQuitAction(void)
{
    SaveAction(GetActionId(CONTOL_KEY_UPGRADE_TIMEOUT_TO_NORMAL), "UPGquitToNormalKey");
    return ;
}

BOOLEAN SaveKeyUpgradeMannualQuitAction(void)
{
    SaveAction(GetActionId(CONTOL_KEY_UPGRADE_MANNUAL_TO_NORMAL), "UPGMannualQuitKey");
    return 0;
}
#endif

void SaveAction(WORD wActId, char *str)
{
    T_ActionRecord tAct;

    time(&tAct.tTime);
#ifdef DEVICE_USING_D121
    tAct.ucActId = wActId;
#else
    tAct.ucActId = (BYTE)(wActId&0xFF);
#endif

    rt_memset((rt_uint8_t *)tAct.aucMsg, 0, sizeof(tAct.aucMsg));
    rt_strncpy_s(tAct.aucMsg, sizeof(tAct.aucMsg), str, sizeof(tAct.aucMsg) - 1);
    
    s_atHisRecord[0].pucData = (rt_uint8_t *)&tAct;
    saveOneRecord(&s_atHisRecord[0]);
    rt_thread_delay(200);
    setBackUpHisAct(&tAct);
    s_atHisRecord[0].pucData = RT_NULL;
    return;
}

WORD GetActionId(rt_uint16_t wActionId)
{
    WORD wActId = 0;
#ifdef DEVICE_USING_D121
    U_Int ucActId;
    SaveHisOpID(&ucActId, wActionId);
    wActId = ucActId.iData;
#else
    wActId = (WORD)wActionId;
#endif
    return wActId;
}

/***************************************************************************
 * @brief    读取历史数据当前的读取点
 **************************************************************************/
rt_uint16_t GetProtoHisDataPoint(record_north_protocol_e wNorthProtocolIndex)
{
    return s_atHisRecord[2].wReadPoint[wNorthProtocolIndex];
}

/***************************************************************************
 * @brief    设置历史数据当前的读取点
 * @param    {WORD} wHisDataReadPoint---读取点
 **************************************************************************/
void SetProtoHisDataPoint(WORD wHisDataReadPoint, record_north_protocol_e wNorthProtocolIndex)
{
    s_atHisRecord[2].wReadPoint[wNorthProtocolIndex] = wHisDataReadPoint;
    return;
}

/***************************************************************************
 * @brief    读取直流内阻记录当前的读取点
 **************************************************************************/
rt_uint16_t GetProtoDcrRecordPoint(record_north_protocol_e wNorthProtocolIndex)
{
    return s_atHisRecord[3].wReadPoint[wNorthProtocolIndex];
}

/***************************************************************************
 * @brief    设置直流内阻记录当前的读取点
 * @param    {WORD} wHisDataReadPoint---读取点
 **************************************************************************/
BOOLEAN SetProtoDcrRecordPoint(WORD wDcrRecordReadPoint, record_north_protocol_e wNorthProtocolIndex)
{
    s_atHisRecord[3].wReadPoint[wNorthProtocolIndex] = wDcrRecordReadPoint;
    return True;
}

/***************************************************************************
 * @brief    保存当前历史记录读取位置
 **************************************************************************/
void SaveHisPoint(void)
{
    T_HisPoint tHisPoint;
    rt_memcpy(tHisPoint.wReadPoint_action, s_atHisRecord[0].wReadPoint, sizeof(s_atHisRecord[0].wReadPoint));
    rt_memcpy(tHisPoint.wReadPoint_alarm, s_atHisRecord[1].wReadPoint, sizeof(s_atHisRecord[1].wReadPoint));
    rt_memcpy(tHisPoint.wReadPoint_data, s_atHisRecord[2].wReadPoint, sizeof(s_atHisRecord[2].wReadPoint));
    saveHisPointInfo(&tHisPoint);
    return;
}

void DelAllHisAction(void)
{
    DelAllRecords( &s_atHisRecord[0] );
    return;
}

void ReadMoreHisOperation( rt_uint16_t wNum, rt_uint8_t *tHisOperation, record_north_protocol_e wNorthProtocolIndex)
{
    rt_uint16_t  i;
    struct tm tStartTime;
    T_ActionRecord tActSave;

    s_atHisRecord[0].pucData = (rt_uint8_t*)&tActSave;
    for (i = 0; i < wNum; i++)
    {
        if ( False == readOneRecord(&s_atHisRecord[0], wNorthProtocolIndex) )
        {
            break;
        }

        localtime_r( &tActSave.tTime, &tStartTime );
#if defined(DEVICE_USING_D121) || defined(UNITEST)
        PutInt16ToBuff(tHisOperation, tStartTime.tm_year + 1900);
        tHisOperation += 2;
        *tHisOperation++ = tStartTime.tm_mon + 1;
        *tHisOperation++ = tStartTime.tm_mday;
        *tHisOperation++ = tStartTime.tm_hour;
        *tHisOperation++ = tStartTime.tm_min;
        *tHisOperation++ = tStartTime.tm_sec;
        *tHisOperation++ = 0x41;
        *tHisOperation++ = (rt_uint8_t)(tActSave.ucActId >> 8);
        *tHisOperation++ = (rt_uint8_t)(tActSave.ucActId & 0xFF);
#else
        *tHisOperation++ = (rt_uint8_t)tActSave.ucActId;
        PutInt16ToBuff(tHisOperation, tStartTime.tm_year + 1900);
        tHisOperation += 2;
        *tHisOperation++ = tStartTime.tm_mon + 1;
        *tHisOperation++ = tStartTime.tm_mday;
        *tHisOperation++ = tStartTime.tm_hour;
        *tHisOperation++ = tStartTime.tm_min;
        *tHisOperation++ = tStartTime.tm_sec;
#endif

        rt_memcpy(tHisOperation, tActSave.aucMsg, 20);
        tHisOperation += 20;
    }
    s_atHisRecord[0].wReadPoint[wNorthProtocolIndex] -= i;
    s_atHisRecord[0].pucData = RT_NULL;
    return;
}

rt_uint16_t GetProtoHisOperNum(record_north_protocol_e wNorthProtocolIndex)
{
    return (s_atHisRecord[0].wSavedNum - s_atHisRecord[0].wReadPoint[wNorthProtocolIndex]);
}

rt_uint16_t GetProtoHisOperPoint(record_north_protocol_e wNorthProtocolIndex)
{
    return s_atHisRecord[0].wReadPoint[wNorthProtocolIndex];
}

void  MoveProtoHisOperPoint( rt_uint8_t ucIncNum, record_north_protocol_e wNorthProtocolIndex)
{
    s_atHisRecord[0].wReadPoint[wNorthProtocolIndex] += ucIncNum;
    if (s_atHisRecord[0].wReadPoint[wNorthProtocolIndex] > s_atHisRecord[0].wSavedNum)
    {
        s_atHisRecord[0].wReadPoint[wNorthProtocolIndex] = s_atHisRecord[0].wSavedNum;
    }
    return;
}

void   ResetBmsHisOperPoint(record_north_protocol_e wNorthProtocolIndex)
{
    s_atHisRecord[0].wReadPoint[wNorthProtocolIndex] = 0;
    return;
}

/***************************************************************************
 * @brief    异常关机后开机保存电芯统计记录
 **************************************************************************/

Static void UpdateAnalyticalAlmReset( void )
{
    T_AnalyseInfoStruct tAnalyseInfo;

    ReadAnalyseInfo();
    rt_memset(&tAnalyseInfo, 0, offsetof(T_AnalyseInfoStruct,wCheckSum)+2);
    GetAnalyticalAlm( &tAnalyseInfo );
    if(tAnalyseInfo.ucBattUVPShutDownFlag == SHUT_DOWN_FLAG)
    {
        time(&tAnalyseInfo.tTime2);
        tAnalyseInfo.ucBattUVPShutDownFlag = RESTART_FLAG;
    }
    TransAnalyticalAlm( &tAnalyseInfo, INFO_WRITTEN );
}

/***************************************************************************
 * @brief    开机保存重启原因
 **************************************************************************/
void SaveResetToHisOper(void)
{
    char buff[21] = {0};
    T_DCFactory tBduFactory;
    rt_uint8_t ucResetReason = 0;
    const char *apcReason[] = {
        "Sys Abnormal",
        "Sys Upgrade",
        "Remote Reset",
        "Apptest",
        "Shutdown",
        "Sys Backup",
        "Bdcu Upgrade",
        "Upgrade Timeout",
        "Unknown"
    };
    GetBduFact(&tBduFactory);
    ucResetReason = getResetData(RESET_REASON);

    if (ucResetReason >= NO_RESET_UNKNOW || ucResetReason == 0)
    {
        ucResetReason = NO_RESET_UNKNOW;
    }


    if(ucResetReason == NO_RESET_CSU_UPGRADE)
    {
        rt_snprintf(buff, sizeof(buff), "AFT BMS %s", BMS_VER);
        SaveAction(GetActionId(CONTOL_BATT_INDICATION), buff);
    }
    else if(ucResetReason == NO_RESET_BDU_UPGRADE)
    {
        rt_snprintf(buff, sizeof(buff), "AFT BDU %s", tBduFactory.acSoftVer);
        SaveAction(GetActionId(CONTOL_BATT_INDICATION), buff);
    }

    rt_snprintf(buff, sizeof(buff), apcReason[ucResetReason - 1]);
    SaveAction(GetActionId(CONTOL_RST_SYS), buff);
    UpdateAnalyticalAlmReset();
    saveResetData(RESET_REASON, NO_RESET_CSU_ABNORMAL);

    return;
}

/***************************************************************************
 * @brief    尽可能读取wNum条历史告警，按字节存入ptHisAlarm中，返回实际读到的条数
 * @param    {rt_uint16_t} wNum---期望读取的条数
 * @param    {rt_uint8_t} *ptHisAlarm---读取历史告警存放的buff
 * @return   返回实际读到的条数
 **************************************************************************/
rt_uint8_t ReadMoreHisAlarm( rt_uint16_t wNum , rt_uint8_t *ptHisAlarm, record_north_protocol_e wNorthProtocolIndex)
{
    rt_uint16_t  i;
    rt_uint8_t ucNum = 0;
    struct tm tStartTime,tEndTime;
    T_HisAlarmSaveStruct tSaveHisAlarm;

    s_atHisRecord[1].pucData = (rt_uint8_t*)&tSaveHisAlarm;
    for (i = 0; i < wNum; i++)
    {

        if ( False == readOneRecord(&s_atHisRecord[1], wNorthProtocolIndex) )
        {
            break;
        }

        localtime_r( &tSaveHisAlarm.tStartTime, &tStartTime );
        localtime_r( &tSaveHisAlarm.tEndTime, &tEndTime );

        #ifdef HISALARM_ID_MAPPING_ENABLED
            //告警ID需映射
            PutInt16ToBuff(ptHisAlarm, s_aucHisAlarmMapping[tSaveHisAlarm.wAlarmID -1] + 1); 
        #else
            PutInt16ToBuff(ptHisAlarm, tSaveHisAlarm.wAlarmID);
        #endif
        
        ptHisAlarm += 2;
        PutInt16ToBuff(ptHisAlarm, tStartTime.tm_year+1900);
        ptHisAlarm += 2;
        *ptHisAlarm++ = tStartTime.tm_mon + 1;
        *ptHisAlarm++ = tStartTime.tm_mday;
        *ptHisAlarm++ = tStartTime.tm_hour;
        *ptHisAlarm++ = tStartTime.tm_min;
        *ptHisAlarm++ = tStartTime.tm_sec;
        PutInt16ToBuff(ptHisAlarm, tEndTime.tm_year+1900);
        ptHisAlarm += 2;
        *ptHisAlarm++ = tEndTime.tm_mon + 1;
        *ptHisAlarm++ = tEndTime.tm_mday;
        *ptHisAlarm++ = tEndTime.tm_hour;
        *ptHisAlarm++ = tEndTime.tm_min;
        *ptHisAlarm++ = tEndTime.tm_sec;
        ucNum++;
    }
    s_atHisRecord[1].wReadPoint[wNorthProtocolIndex] -= ucNum;
    s_atHisRecord[1].pucData = RT_NULL;
    return ucNum;
}



void DeleteHisAlarm(void)
{
    deleteFile("/AlmCheck");
    DelAllRecords( &s_atHisRecord[1] );
    return;
}

rt_uint16_t GetHisAlarmTotNum(void)
{
    return s_atHisRecord[1].wSavedNum;
}

void SetProtoHisAlarmPoint(WORD wHisAlarmReadPoint, record_north_protocol_e wNorthProtocolIndex)
{
    s_atHisRecord[1].wReadPoint[wNorthProtocolIndex] = wHisAlarmReadPoint;
}

rt_uint16_t GetProtoHisAlarmNum(record_north_protocol_e wNorthProtocolIndex )
{
    return (s_atHisRecord[1].wSavedNum - s_atHisRecord[1].wReadPoint[wNorthProtocolIndex]);
}

rt_uint16_t GetProtoHisAlarmPoint(record_north_protocol_e wNorthProtocolIndex)
{
    return s_atHisRecord[1].wReadPoint[wNorthProtocolIndex];
}

void   MoveProtoHisAlarmPoint( rt_uint8_t ucIncNum, record_north_protocol_e wNorthProtocolIndex)
{
    s_atHisRecord[1].wReadPoint[wNorthProtocolIndex] += ucIncNum;
    if (s_atHisRecord[1].wReadPoint[wNorthProtocolIndex] > s_atHisRecord[1].wSavedNum)
    {
        s_atHisRecord[1].wReadPoint[wNorthProtocolIndex] = s_atHisRecord[1].wSavedNum;
    }
    return;
}

void   ResetProtoHisAlarmPoint( record_north_protocol_e wNorthProtocolIndex)
{
    s_atHisRecord[1].wReadPoint[wNorthProtocolIndex] = 0;
    return ;
}

void SaveHisAlarm( T_HisAlarmSaveStruct *ptDisHisAlarm)
{
    if(ptDisHisAlarm == NULL)
    {
        return;
    }

    s_atHisRecord[1].pucData = (rt_uint8_t *)ptDisHisAlarm;
    CheckAlarmSaved(ptDisHisAlarm);
    saveOneRecord(&s_atHisRecord[1]);
    rt_thread_delay(400);
	setBackUpHisAlm(ptDisHisAlarm);
    return;
}

rt_uint16_t GetHisOperationTotNum(void)
{
    return s_atHisRecord[0].wSavedNum;
}

void SetProtoHisActionPoint(WORD wHisActionReadPoint, record_north_protocol_e wNorthProtocolIndex)
{
    s_atHisRecord[0].wReadPoint[wNorthProtocolIndex] = wHisActionReadPoint;
}

/***************************************************************************
 * @brief    获取三种历史数据中的某一条记录的时间
 * @param    {tm} *tTime---当前readpoint的时间
 * @param    {BYTE} ucHisRecordType---历史记录类型
 **************************************************************************/
static void GetOneRecordTime(struct tm *tTime, BYTE ucHisRecordType)
{
    T_ActionRecord tHisOptAction;
    T_HisAlarmSaveStruct tHisAlarmSave;
    T_HisDataStruct tHisDataSave;
    //T_HisDataTowerStruct tHisDataSaveTower;

    switch (ucHisRecordType)
    {
    case HISTORICAL_ACTION:
        s_atHisRecord[HISTORICAL_ACTION].pucData = (rt_uint8_t *)&tHisOptAction;
        readOneRecord(&s_atHisRecord[HISTORICAL_ACTION], (record_north_protocol_e)0);
        localtime_r(&tHisOptAction.tTime, tTime);
        s_atHisRecord[HISTORICAL_ACTION].pucData = RT_NULL;
        break;
    case HISTORICAL_ALARM:
        s_atHisRecord[HISTORICAL_ALARM].pucData = (rt_uint8_t *)&tHisAlarmSave;
        readOneRecord(&s_atHisRecord[HISTORICAL_ALARM], (record_north_protocol_e)0);
        localtime_r(&tHisAlarmSave.tEndTime, tTime);
        s_atHisRecord[HISTORICAL_ALARM].pucData = RT_NULL;
        break;
    case HISTORICAL_DATA:
        ReadHisData(&tHisDataSave, (record_north_protocol_e)0);
        localtime_r(&tHisDataSave.tStartTime, tTime);
        break;
    //case HISTORICAL_TOWERDATA:
    //    ReadHisDataTower(&tHisDataSave);
    //    localtime_r(&tHisDataSave.tStartTime, tTime);
    //    break;
    default:
        break;
    }
    return;
}

/***************************************************************************
 * @brief    将历史记录分组 (实际未操作wReadPoint)
 * @param    {T_TimeStruct} *ptBlockArray---时间分块数组
 * @param    {BYTE} *pucBlockNum---最终历史记录分块数量
 * @param    {BYTE} ucHisRecordType---历史记录类型
 **************************************************************************/
void DivideRecordToBlock(T_TimeStruct *ptBlockArray, BYTE *pucBlockNum, BYTE ucHisRecordType)
{
    WORD wRestNum = 0;
    WORD wTempReadPoint = s_atHisRecord[ucHisRecordType].wReadPoint[0];
    BYTE ucBlockNum = 0;
    BYTE i = 0;
    struct tm tTime;

    /*
     * 根据历史记录条数计算BlockNum
     * 0，BlockNum = 0
     * 1，BlockNum = 1
     * 2-1000，BlockNum = 2
     * 1001-2000，BlockNum = 3
     */
    ucBlockNum = s_atHisRecord[ucHisRecordType].wSavedNum / 1000;
    if (ucBlockNum != 0) // BlockNum >= 1000
    {
        if (s_atHisRecord[ucHisRecordType].wSavedNum % (ucBlockNum * 1000))
        {
            ucBlockNum++; // 有余则加
        }
    }
    else
    {
        wRestNum = s_atHisRecord[ucHisRecordType].wSavedNum;
        if (wRestNum)
        {
            if (wRestNum >= 2)
            {
                ucBlockNum++;
            }
        }
        else
        {
            *pucBlockNum = ucBlockNum;
            return;
        }
    }
    ucBlockNum++;

    /* 往分块数组中存放 */
    for (i = 0; i < ucBlockNum - 1; i++)
    {
        s_atHisRecord[ucHisRecordType].wReadPoint[0] = i * 1000;
        GetOneRecordTime(&tTime, ucHisRecordType);
        TmTime2TimeStruct(&ptBlockArray[i], tTime);
    }

    s_atHisRecord[ucHisRecordType].wReadPoint[0] = s_atHisRecord[ucHisRecordType].wSavedNum - 1;
    GetOneRecordTime(&tTime, ucHisRecordType);
    TmTime2TimeStruct(&ptBlockArray[i], tTime);

    *pucBlockNum = ucBlockNum;
    s_atHisRecord[ucHisRecordType].wReadPoint[0] = wTempReadPoint;

    return;
}

/***************************************************************************
 * @brief    先根据blocktime的组数先模糊确定范围，再用二分查找函数精确确定范围
 * @param    {BYTE} *IndexZone---blocktime组数信息
 * @param    {BYTE} ucBlockNum---总block数
 * @param    {T_TimeStruct} tInputTime---待确认的时间
 * @param    {BOOLEAN} bUseHigh---无法精准匹配时，使用上边界还是下边界
 * @param    {BYTE} ucHisRecordType---记录类型
 * @return   查找到的readpoint
 **************************************************************************/
SHORT GetHisDataPosition(BYTE *IndexZone, BYTE ucBlockNum, T_TimeStruct tInputTime, BOOLEAN bUseHigh, BYTE ucHisRecordType)
{
    WORD wLow = 0, wHigh = 0;
    SHORT sRtn = FAILED;

    /* 计算查找范围 */
    if (IndexZone[1] == ucBlockNum - 1)
    {
        wLow = (ucBlockNum - 2) * 1000;
        wHigh = s_atHisRecord[ucHisRecordType].wSavedNum - 1;
    }
    else
    {
        wLow = IndexZone[0] * 1000;
        wHigh = IndexZone[1] * 1000 - 1;
    }

    sRtn = BinaryFindTimePoint(wLow, wHigh, tInputTime, bUseHigh, ucHisRecordType);

    return sRtn;
}

/***************************************************************************
 * @brief    二分查找指定时间节点在历史数据中的位置
 * @param    {WORD} wLow---范围下限
 * @param    {WORD} wHigh---范围上限
 * @param    {T_TimeStruct} tInputTime---待查时间
 * @param    {BOOLEAN} bUseHigh---无法精准匹配时，使用上边界还是下边界
 * @param    {BYTE} ucHisRecordType---记录类型
 * @return   查找到的readpoint
 **************************************************************************/
Static SHORT BinaryFindTimePoint(WORD wLow, WORD wHigh, T_TimeStruct tInputTime, BOOLEAN bUseHigh, BYTE ucHisRecordType)
{
    TimeCompareResult ucTimeCompareRtn = 0;
    T_TimeStruct tMidTime;
    SHORT sRtn = FAILED;
    WORD wMid;

    while (wLow < wHigh)
    {
        /* 从中间查找，不断缩小查找范围 */
        wMid = (wLow + wHigh) / 2;
        GetReadPointTime(wMid, &tMidTime, ucHisRecordType);

        ucTimeCompareRtn = CompareTime(tMidTime, tInputTime);
        if (ucTimeCompareRtn == TIME_EQUAL_TARGET) // 找到目标值
        {
            sRtn = (SHORT)wMid; // 由于条数限制，此处的数据类型转换不会出问题
            return sRtn;
        }
        else if (ucTimeCompareRtn == TIME_GREATER_THAN_TARGET)
        {
            wHigh = wMid;
        }
        else
        {
            wLow = wMid;
        }

        /* 如果最后缩小范围到相邻的两条记录 */
        if (wHigh - wLow == 1)
        {
            /* 两种边界情况 */
            GetReadPointTime(wLow, &tMidTime, ucHisRecordType);
            if (CompareTime(tMidTime, tInputTime) == TIME_EQUAL_TARGET)
            {
                sRtn = wLow;
                return sRtn;
            }

            GetReadPointTime(wHigh, &tMidTime, ucHisRecordType);
            if (CompareTime(tMidTime, tInputTime) == TIME_EQUAL_TARGET)
            {
                sRtn = wHigh;
                return sRtn;
            }

            /* 若仍无法精准匹配，则选择相邻两条中的某一条记录 */
            if (bUseHigh)
            {
                sRtn = wHigh;
                return sRtn;
            }
            else
            {
                sRtn = wLow;
                return sRtn;
            }
        }
    }

    return sRtn;
}

/***************************************************************************
 * @brief    根据输入的readpoint读出此记录的blocktime结构时间
 * @param    {WORD} wReadPoint---记录点
 * @param    {T_TimeStruct} *ptBlockTime---blocktime结构时间
 * @param    {BYTE} ucHisRecordType---历史记录类型
 **************************************************************************/
Static void GetReadPointTime(WORD wReadPoint, T_TimeStruct *ptBlockTime, BYTE ucHisRecordType)
{
    struct tm tTime;
    WORD wReadPointTmp = s_atHisRecord[ucHisRecordType].wReadPoint[0];
    s_atHisRecord[ucHisRecordType].wReadPoint[0] = wReadPoint;
    GetOneRecordTime(&tTime, ucHisRecordType);
    TmTime2TimeStruct(ptBlockTime, tTime);
    s_atHisRecord[ucHisRecordType].wReadPoint[0] = wReadPointTmp;
    return;
}

/***************************************************************************
 * @brief    判断起始时间是否有效，若有效则获取起始时间的历史记录序号
 * @param    {T_TimeStruct} *atBlock---分块数组
 * @param    {BYTE} ucBlockNum---时间分段数
 * @param    {T_TimeStruct} tInputTime---待查找的起始时间
 * @param    {WORD} *pwIndex---历史记录序号
 * @param    {BYTE} ucHisRecordType---历史记录类型
 * @return   TIME_VALID, TIME_INVALID
 **************************************************************************/
BYTE GetStartTimePose(T_TimeStruct *atBlock, BYTE ucBlockNum, T_TimeStruct tInputTime, WORD *pwIndex, BYTE ucHisRecordType)
{
    BYTE IndexZone[2];
    BYTE ucRtn;
    SHORT sRtn;

    /* 先是最简单的两种边界情况 */
    if (CompareTime(tInputTime, atBlock[0]) <= TIME_EQUAL_TARGET)
    {
        *pwIndex = 0;
        return TIME_VALID;
    }
    if (CompareTime(tInputTime, atBlock[ucBlockNum - 1]) == TIME_GREATER_THAN_TARGET)
    {
        return TIME_INVALID;
    }

    ucRtn = FindTimeInBlock(atBlock, ucBlockNum, tInputTime, IndexZone, pwIndex);

    /* 从确定的区间中获得起始时间在历史数据记录中具体的位置 */
    if (ucRtn == FIND_TIME_PRECISE)
    {
        return TIME_VALID;
    }
    else if (ucRtn == FIND_TIME_FUZZY)
    {
        sRtn = GetHisDataPosition(IndexZone, ucBlockNum, tInputTime, USE_HIGH_BOUNDARY, ucHisRecordType);
        if (sRtn <= FAILED)
        {
            return TIME_INVALID;
        }
        *pwIndex = sRtn;
        return TIME_VALID;
    }
    else
    {
        return TIME_INVALID;
    }
}

/***************************************************************************
 * @brief    判断终止时间是否有效，若有效则获取终止时间的历史记录序号
 * @param    {T_TimeStruct} *atBlock---分块数组
 * @param    {BYTE} ucBlockNum---时间分段数
 * @param    {T_TimeStruct} tInputTime---待查找的起始时间
 * @param    {WORD} *pwIndex---历史记录序号
 * @param    {BYTE} ucHisRecordType---历史记录类型
 * @return   TIME_VALID, TIME_INVALID
 **************************************************************************/
BYTE GetEndTimePose(T_TimeStruct *atBlock, BYTE ucBlockNum, T_TimeStruct tInputTime, WORD *pwIndex, BYTE ucHisRecordType)
{
    BYTE IndexZone[2];
    BYTE ucRtn;
    SHORT sRtn;

    /* 先是最简单的两种边界情况 */
    if (CompareTime(atBlock[ucBlockNum - 1], tInputTime) <= TIME_EQUAL_TARGET)
    {
        *pwIndex = s_atHisRecord[ucHisRecordType].wSavedNum - 1;
        return TIME_VALID;
    }
    if (CompareTime(atBlock[0], tInputTime) == TIME_GREATER_THAN_TARGET)
    {
        return TIME_INVALID;
    }

    ucRtn = FindTimeInBlock(atBlock, ucBlockNum, tInputTime, IndexZone, pwIndex);

    /* 从确定的区间中获得起始时间在历史数据记录中具体的位置 */
    if (ucRtn == FIND_TIME_PRECISE)
    {
        return TIME_VALID;
    }
    else if (ucRtn == FIND_TIME_FUZZY)
    {
        sRtn = GetHisDataPosition(IndexZone, ucBlockNum, tInputTime, USE_LOW_BOUNDARY, ucHisRecordType);
        if (sRtn <= FAILED)
        {
            return TIME_INVALID;
        }
        *pwIndex = sRtn;
        return TIME_VALID;
    }
    else
    {
        return TIME_INVALID;
    }
}


WORD ReadMoreHisData(rt_uint16_t wNum , rt_uint8_t *ptHisData, record_north_protocol_e wNorthProtocolIndex){
    rt_uint16_t  i;
	T_HisDataStruct tHisData;
    struct tm tTime;
    rt_uint8_t *ptHisDataTmp = ptHisData;
    rt_int16_t sPowerTempTmp;
	
	s_atHisRecord[2].pucData = (rt_uint8_t *)&tHisData;
	for (i = 0; i < wNum; i++)
    {
		
        if (False == readOneRecord(&s_atHisRecord[2], wNorthProtocolIndex))
        {
            break;
        }
		*ptHisData++ = PACKLOCATION; // Pack组位置
        /* 日期和时间 */
        localtime_r(&tHisData.tStartTime, &tTime);
        PutInt16ToBuff(ptHisData, tTime.tm_year + 1900);
        ptHisData += 2;
        *ptHisData++ = tTime.tm_mon + 1;
        *ptHisData++ = tTime.tm_mday;
        *ptHisData++ = tTime.tm_hour;
        *ptHisData++ = tTime.tm_min;
        *ptHisData++ = tTime.tm_sec;

        *ptHisData++ = HISDATA_VER; // 历史数据版本号

        *ptHisData++ = tHisData.ucSysMode;                                // 系统模式
        *ptHisData++ = tHisData.ucCellEvent;                              // 电芯状态事件
        *ptHisData++ = tHisData.ucCellVolEvent;                           // 单体电压事件
        *(SHORT *)ptHisData = Host2Modbus((SHORT *)&tHisData.wTempEvent); // 温度事件
        ptHisData += 2;
        *ptHisData++ = tHisData.ucCurrentEvent;                          // 电流事件
        *ptHisData++ = tHisData.ucCapacityEvent;                         // 容量事件
        ptHisData += 4;                                                  // 故障状态(暂未实现，四字节均为0)
        *(SHORT *)ptHisData = Host2Modbus((SHORT *)&tHisData.wBattCurr); // 充放电流
        ptHisData += 2;
        *(SHORT *)ptHisData = Host2Modbus((SHORT *)&tHisData.wBattVolt); // 电池电压
        ptHisData += 2;
        *(SHORT *)ptHisData = Host2Modbus((SHORT *)&tHisData.wBattSOC); // 剩余容量(电池SOC)
        ptHisData += 2;

        *ptHisData++ = tHisData.ucCellTempNum; // 电芯温度数量
        if(s_tHardwarePara.ucCellTempNum > 0 && s_tHardwarePara.ucCellTempNum < CELL_TEMP_NUM_MAX)
        {
            for (BYTE i = 0; i < s_tHardwarePara.ucCellTempNum; i++)
            {
                *(SHORT *)ptHisData = Host2Modbus((SHORT *)&tHisData.awCellTemp[i]); // 电芯温度
                ptHisData += 2;
            }
        }else{
            for (BYTE i = 0; i < CELL_TEMP_NUM_MAX; i++)
            {
                *(SHORT *)ptHisData = Host2Modbus((SHORT *)&tHisData.awCellTemp[i]); // 电芯温度
                ptHisData += 2;
            }
        }
        *(SHORT *)ptHisData = Host2Modbus((SHORT *)&tHisData.sEnvTemp); // 环境温度
        ptHisData += 2;
        sPowerTempTmp = tHisData.sPowerTemp/10;//功率温度精度为1，但是历史数据中存放值为实时数据值*100
        *(SHORT *)ptHisData = Host2Modbus((SHORT *)&sPowerTempTmp); // 功率温度
        ptHisData += 2;
        *ptHisData++ = tHisData.ucCellVoltNum; // 电芯电压数量
        if(s_tHardwarePara.ucCellVoltNum > 0 && s_tHardwarePara.ucCellVoltNum < CELL_VOL_NUM_MAX)
        {
            for (BYTE i = 0; i < s_tHardwarePara.ucCellVoltNum; i++)
            {
                *(SHORT *)ptHisData = Host2Modbus((SHORT *)&tHisData.awCellVolt[i]); // 电芯电压
                ptHisData += 2;
            }
        }else{
            for (BYTE i = 0; i < CELL_VOL_NUM_MAX; i++)
            {
                *(SHORT *)ptHisData = Host2Modbus((SHORT *)&tHisData.awCellVolt[i]); // 电芯电压
                ptHisData += 2;
            }
        }
        *(SHORT *)ptHisData = Host2Modbus((SHORT *)&tHisData.wCellVoltMin); // 电芯电压最小值
        ptHisData += 2;
        *(SHORT *)ptHisData = Host2Modbus((SHORT *)&tHisData.wCellVoltMax); // 电芯电压最大值
        ptHisData += 2;
        *(SHORT *)ptHisData = Host2Modbus((SHORT *)&tHisData.wBusVolt); // 母排电压
        ptHisData += 2;
        *(SHORT *)ptHisData = Host2Modbus((SHORT *)&tHisData.wBusCurr); // 母排电流
        ptHisData += 2;
        *(SHORT *)ptHisData = Host2Modbus((SHORT *)&tHisData.wBattCycleTimes); // 电池循环次数
        ptHisData += 2;
        *(SHORT *)ptHisData = Host2Modbus((SHORT *)&tHisData.wBattBalanceStatus); // 电芯均衡状态
        ptHisData += 2;
        *ptHisData++ = tHisData.ucBattStatus;                                   // 电池状态
        *(SHORT *)ptHisData = Host2Modbus((SHORT *)&tHisData.wPowerOnVoltThre); // 来电电压阈值
        ptHisData += 2;
        *(SHORT *)ptHisData = Host2Modbus((SHORT *)&tHisData.wPowerDownVoltThre); // 停电电压阈值
        ptHisData += 2;
        *(SHORT *)ptHisData = Host2Modbus((SHORT *)&tHisData.sConnTemp); // 连接器温度
        ptHisData += 2;
        *(SHORT *)ptHisData = Host2Modbus((SHORT *)&tHisData.wDischgSetVol);// 放电设定电压
        ptHisData += 2; 
        *(SHORT *)ptHisData = Host2Modbus((SHORT *)&tHisData.wSetChgCurr); //充电限流千分比
        ptHisData += 2; 
	}
	s_atHisRecord[2].wReadPoint[wNorthProtocolIndex] -= i;
    s_atHisRecord[2].pucData = RT_NULL;
    return ptHisData - ptHisDataTmp;
}

void ReadMoreHisData_new(rt_uint16_t wNum , rt_uint8_t *ptHisData, record_north_protocol_e wNorthProtocolIndex){
    rt_uint16_t  i;
	T_HisDataStruct tHisData;
    struct tm tTime;
    SHORT sTemp = 0;
	
	s_atHisRecord[2].pucData = (rt_uint8_t *)&tHisData;
	for (i = 0; i < wNum; i++)
    {
		
        if (False == readOneRecord(&s_atHisRecord[2], wNorthProtocolIndex))
        {
            break;
        }

        /* 日期和时间 */
        localtime_r(&tHisData.tStartTime, &tTime);
        PutInt16ToBuff(ptHisData, tTime.tm_year + 1900);
        ptHisData += 2;
        *ptHisData++ = tTime.tm_mon + 1;
        *ptHisData++ = tTime.tm_mday;
        *ptHisData++ = tTime.tm_hour;
        *ptHisData++ = tTime.tm_min;
        *ptHisData++ = tTime.tm_sec;

        sTemp = Host2Modbus((SHORT*)&tHisData.wCellVoltMax);    //单体电压最大值
        rt_memcpy(ptHisData, (BYTE*)&sTemp, sizeof(sTemp));
        ptHisData += 2;
        *ptHisData++ = tHisData.ucCellVoltMaxNo;                //电压最大值通道
        sTemp  = Host2Modbus((SHORT*)&tHisData.wCellVoltMin);    //单体电压最小值  
        rt_memcpy(ptHisData, (BYTE*)&sTemp, sizeof(sTemp));
        ptHisData += 2;
        *ptHisData++ = tHisData.ucCellVoltMinNo;                //电压最小值通道
        sTemp  = Host2Modbus((SHORT*)&tHisData.wCellTempMax);    //单体温度最大值 
        rt_memcpy(ptHisData, (BYTE*)&sTemp, sizeof(sTemp));
        ptHisData += 2;
        *ptHisData++ = tHisData.ucCellTempMaxNo;                //温度最大值通道
        sTemp  = Host2Modbus((SHORT*)&tHisData.wCellTempMin);    //单体温度最小值
        rt_memcpy(ptHisData, (BYTE*)&sTemp, sizeof(sTemp));
        ptHisData += 2;
        *ptHisData++ = tHisData.ucCellTempMinNo;                //温度最小值通道
        sTemp  = Host2Modbus((SHORT*)&tHisData.wBattCurr);    //电池电流 
        rt_memcpy(ptHisData, (BYTE*)&sTemp, sizeof(sTemp));
        ptHisData += 2;
        sTemp  = Host2Modbus((SHORT*)&tHisData.wBattVolt);     //电池电压 
        rt_memcpy(ptHisData, (BYTE*)&sTemp, sizeof(sTemp));
        ptHisData += 2;
        sTemp  = Host2Modbus((SHORT*)&tHisData.wBusVolt);    //母排电压 
        rt_memcpy(ptHisData, (BYTE*)&sTemp, sizeof(sTemp));
        ptHisData += 2;
        sTemp  = Host2Modbus((SHORT*)&tHisData.wBattCycleTimes);     //电池循环次数  
        rt_memcpy(ptHisData, (BYTE*)&sTemp, sizeof(sTemp));
        ptHisData += 2;
        *ptHisData++ = (rt_int8_t)tHisData.sEnvTemp;
        *ptHisData++ = (rt_uint8_t)tHisData.wBattSOH;
        *ptHisData++ = (rt_uint8_t)tHisData.wBattSOC;
        *ptHisData++ = (tHisData.wStatus>>CELL_EQU_STA_BIT)&1; //均衡状态
        *ptHisData++ = (tHisData.wStatus>>CHG_PROT_STA_BIT)&1; //充电保护状态
        *ptHisData++ = (tHisData.wStatus>>DISCH_PROT_STA_BIT)&1; //放电保护状态
        *ptHisData++ = tHisData.ucBattStatus; //电池组状态
        *ptHisData++ = (tHisData.wStatus>>BATT_VOLH_PROT_BIT)&1;//电池组过压保护
        *ptHisData++ = (tHisData.wStatus>>BATT_VOLL_PROT_BIT)&1;//电池组欠压保护
        *ptHisData++ = (tHisData.wStatus>>CHG_CURH_PROT_BIT)&1; //充电过流保护
        *ptHisData++ = (tHisData.wStatus>>DISCH_CURH_PROT_BIT)&1;//放电过流保护
        *ptHisData++ = (tHisData.wStatus>>LIMIT_STA_BIT)&1; //限流状态
        *ptHisData++ = (tHisData.wStatus>>BDU_SLEEP_STA_BIT)&1; //BDU休眠状态
        *ptHisData++ = (tHisData.wStatus>>BMS_CHGDISCH_STA_BIT)&15;//bdu充放电状态 //待添加
	}
	s_atHisRecord[2].wReadPoint[wNorthProtocolIndex] -= i;
    s_atHisRecord[2].pucData = RT_NULL;
    return;
}

BOOLEAN ReadMoreDcrRecord_new(rt_uint16_t wNum , rt_uint8_t *ptDcrRecord, record_north_protocol_e wNorthProtocolIndex)
{
    rt_uint16_t i, j;
	T_DcrRecordStruct tDcrRecord;
    struct tm tTime;
    SHORT sTemp = 0;

	s_atHisRecord[3].pucData = (rt_uint8_t *)&tDcrRecord;
	for (i = 0; i < wNum; i++)
    {
        // 读取指定索引的直流内阻记录
        if (False == readOneRecord(&s_atHisRecord[3], wNorthProtocolIndex))
        {
            break;
        }

        if((tDcrRecord.ucCellNum < 4) || (tDcrRecord.ucCellNum > CELL_VOL_NUM))
        {
            s_atHisRecord[3].pucData = RT_NULL;
            return False;
        }

        /* 日期和时间 */
        localtime_r(&tDcrRecord.tStartTime, &tTime);
        PutInt16ToBuff(ptDcrRecord, tTime.tm_year + 1900);
        ptDcrRecord += 2;
        *ptDcrRecord++ = tTime.tm_mon + 1;
        *ptDcrRecord++ = tTime.tm_mday;
        *ptDcrRecord++ = tTime.tm_hour;
        *ptDcrRecord++ = tTime.tm_min;
        *ptDcrRecord++ = tTime.tm_sec;
        *ptDcrRecord++ = tDcrRecord.ucCellNum;//单体数量
        for(j = 0; j < tDcrRecord.ucCellNum; j++)
        {
            sTemp = Host2Modbus((SHORT*)&tDcrRecord.wCellDcr[j]);// 单体DCR
            rt_memcpy(ptDcrRecord, (BYTE*)&sTemp, sizeof(sTemp));
            ptDcrRecord += 2;
        }
	}
	s_atHisRecord[3].wReadPoint[wNorthProtocolIndex] -= i;
    s_atHisRecord[3].pucData = RT_NULL;
    return True;
}

/****************************************************************************
* 函数名称：initExtremeData
* 功能描述：初始化极值
* 修改记录：
***************************************************************************/
void initExtremeData(void)
{
    CheckHisExtremeVersion();
    if(ReadHisExtremeData(&s_tHisExtremeData) == False)
    {
        SetExtremeDefault();
    }
    SetExtremeData(&s_tHisExtremeData);
}


/****************************************************************************
* 函数名称：initExtremeData
* 功能描述：初始化极值
* 修改记录：
***************************************************************************/
Static void SetExtremeDefault(void)
{
    BYTE i = 0;
    SetFloatDefault(&s_tHisExtremeData.fBattVoltMax);                      //电池电压最大值
    SetFloatDefault(&s_tHisExtremeData.fBattVoltMin);                    //电池电压最小值
    SetFloatDefault(&s_tHisExtremeData.fExterVoltMax);                         //外部电压最大值
    SetFloatDefault(&s_tHisExtremeData.fEnvTempMax);                       //环境温度最大值
    SetFloatDefault(&s_tHisExtremeData.fEnvTempMin);                    //环境温度最小值
    SetFloatDefault(&s_tHisExtremeData.fChargeBusCurrMax);                        //BUS充电电流最大值
    SetFloatDefault(&s_tHisExtremeData.fDischargeBusCurrMax);               //BUS放电电流最大值
    SetFloatDefault(&s_tHisExtremeData.fChargeBattCurrMax);                 //电池充电电流最大值
    SetFloatDefault(&s_tHisExtremeData.fDischargeBattCurrMax);               //电池放电电流最大值
    for(i = 0; i < CELL_VOL_NUM_MAX; i++)
    {
        SetFloatDefault(&s_tHisExtremeData.fCellVoltMax[i]);  //电芯电压最大值
        SetFloatDefault(&s_tHisExtremeData.fCellVoltMin[i]);    //电芯电压最小值
    }
    for(i = 0; i < CELL_TEMP_NUM_MAX; i++)
    {
        SetFloatDefault(&s_tHisExtremeData.fCellTempMax[i]);    //电芯温度最大值
        SetFloatDefault(&s_tHisExtremeData.fCellTempMin[i]);    //电芯温度最小值
    }    
}


/****************************************************************************
* 函数名称：SetFloatDefault
* 功能描述：初始化float型默认值
* 修改记录：
***************************************************************************/
static void SetFloatDefault(float *fpInputValue)
{
    rt_memset(fpInputValue,0xFF,sizeof(float));
}


/****************************************************************************
* 函数名称：ReadHisExtremeData
* 功能描述：读取极值数据
* 修改记录：
***************************************************************************/
BOOLEAN ReadHisExtremeData(T_HisExtremeData *ptHisExtremeData)
{
    T_HisRecord s_tHisExtremeTemp = {0};
    rt_memcpy(&s_tHisExtremeTemp, s_atHisExtreme, sizeof(T_HisRecord));
    s_tHisExtremeTemp.pucData = (rt_uint8_t *)ptHisExtremeData;
    if (readExtremeDataFile(&s_tHisExtremeTemp))
    {
        return True;
    }
    return False;
}

/****************************************************************************
* 函数名称：UpdateDataExtreme
* 功能描述：检测到极值的变化就保存极值
* 修改记录：
***************************************************************************/
void UpdateDataExtreme(void)
{
    if(True == GetExtremeFlag())
    {
        GetExtremeData(&s_tHisExtremeData);
        SaveExtremeData(); 
        SetExtremeFlag(False);
    }
}


/****************************************************************************
* 函数名称：DelAllHisExtreme
* 功能描述：删除极值数据
* 修改记录：
***************************************************************************/
void DelAllHisExtreme(void)
{
    DelAllRecords( &s_atHisExtreme[0] );
    return;
}

/****************************************************************************
* 函数名称：SaveExtremeData
* 功能描述：以文件的形式保存极值数据
* 修改记录：
***************************************************************************/
Static BOOLEAN SaveExtremeData(void)
{   
    BYTE ucPackNum = 0;  //互备份分包序号
    WORD wOffset = 0;  
    WORD wRemaining = 0;  //每次发送后剩余字节
    WORD wSendLen = 0;

    T_HisRecord *pRecord = &s_atHisExtreme[0];
    s_tHisExtremeData.wCheckSum = CRC_Cal((BYTE*)&s_tHisExtremeData, offsetof(T_HisExtremeData, wCheckSum));
    pRecord->pucData = (rt_uint8_t *)&s_tHisExtremeData;
    if (False == saveExtremeDataFile(pRecord))
    {
        return False;
    }

    //极值互备份数据分包发送，每包最大发送CAN_BACKUP_DATA_MAX
    for (ucPackNum=1; wOffset < pRecord->wRecordSize; ucPackNum++)
    {
        wRemaining = pRecord->wRecordSize - wOffset;
        wSendLen = (wRemaining > CAN_BACKUP_DATA_MAX) ? CAN_BACKUP_DATA_MAX : wRemaining;

        if (wOffset + wSendLen <= pRecord->wRecordSize)
        {
            setBackupHisExtreme(pRecord->pucData + wOffset, wSendLen, ucPackNum);
            wOffset += wSendLen;
        }
    }

    return True;
}

/****************************************************************************
* 函数名称：GetBackupFileList
* 调    用：无
* 被 调 用：
* 输入参数：ucNum要备份文件总个数、备份管理文件信息
* 返 回 值：
* 功能描述：获取备份文件列表名
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
void GetBackupFileList(rt_uint8_t ucNum, T_HisBackMagStruct *ptMagFile)
{
    rt_uint8_t i;
    rt_int32_t fd;
    rt_uint16_t wNameLen = 0;
    rt_uint8_t ucFileInfo[64] = {0};

    #ifndef UNITEST
    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);
    fd = open(FILELIST, O_RDWR | O_CREAT | O_APPEND);
    if (fd < 0)
    {
        rt_mutex_release(s_ptMutexFile);
        return;
    }
    if (GPIO_OFF == GetPMOSStatus())
    {
        close(fd);
        rt_mutex_release(s_ptMutexFile);
        return;
    }

    for (i=0; i<ucNum; i++)
    {
        if (!IsPathExist((char *)ptMagFile[i].ucFileName))
        {
            continue;
        }
        wNameLen = (short)rt_strnlen_s((char*)&ptMagFile[i].ucFileName[8], sizeof(ptMagFile[i].ucFileName) - 8);
        rt_memset(ucFileInfo, 0, sizeof(ucFileInfo));
        PutInt16ToBuff(&ucFileInfo[0], wNameLen); //上传的列表文件名长度
        rt_memcpy(&ucFileInfo[2], &ptMagFile[i].ucFileName[8], wNameLen); //上传的列表文件名称
        write(fd, ucFileInfo, 2+wNameLen);
    }
    close(fd);
    rt_mutex_release(s_ptMutexFile);
    #endif

    return;
}

/****************************************************************************
* 函数名称：GetLocalFileList
* 调    用：无
* 被 调 用：pucName文件名
* 输入参数：
* 返 回 值：
* 功能描述：获取本地文件列表名,将要上传的文件列表保存在文件中，格式为2字节文件名长度+文件实际名称
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
void GetLocalFileList(rt_uint8_t *pucName)
{
    rt_int32_t fd;
    rt_uint16_t wNameLen = 0;
    rt_uint8_t ucFileInfo[64] = {0};

    #ifndef UNITEST
    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);
    if (!IsPathExist((char *)pucName))
    {
        return;
    }

    fd = open(FILELIST, O_RDWR | O_CREAT | O_APPEND);
    if (fd < 0)
    {
        rt_mutex_release(s_ptMutexFile);
        return;
    }
    #endif
    if (GPIO_OFF == GetPMOSStatus())
    {
        close(fd);
        rt_mutex_release(s_ptMutexFile);
        return;
    }
    wNameLen = (short)rt_strnlen_s((char*)pucName, BACKUP_FILE_NAME_MAX_LEN);
    rt_memset(ucFileInfo, 0, sizeof(ucFileInfo));
    PutInt16ToBuff(&ucFileInfo[0], wNameLen); //上传的列表文件名长度
    rt_memcpy(&ucFileInfo[2], pucName, wNameLen); //上传的列表文件名称
    write(fd, ucFileInfo, 2+wNameLen);
    close(fd);
    #ifndef UNITEST
    rt_mutex_release(s_ptMutexFile);
    #endif

    return;
}

/****************************************************************************
* 函数名称：GetFileListInfo
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：获取文件列表名
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
void GetFileListInfo()
{
    unlink(FILELIST);
    /* 获取备份列表名 */
    GetBackupFileList(s_ucHisDataBackupFileNum, s_atHisDataMag);
    GetBackupFileList(s_ucHisAlarmBackupFileNum, s_atHisAlarmMag);
    GetBackupFileList(s_ucHisActBackupFileNum, s_atHisActMag);
    GetBackupFileList(s_ucBduRecordBackupFileNum, s_atBduRecordMag);
    GetBackupFileList(s_ucExtremeBackupFileNum, s_atExtremeMag);
    GetBackupFileList(s_ucAnalyseBackupFileNum, s_atAnalyseMag);

    /* 获取本地列表名 */
    GetLocalFileList((BYTE*)HISDATA_LOCAL_FILE);
    GetLocalFileList((BYTE*)HISALARM_LOCAL_FILE);
    GetLocalFileList((BYTE*)HISACT_LOCAL_FILE);
    GetLocalFileList((BYTE*)BDURECORD_LOCAL_FILE);
    GetLocalFileList((BYTE*)ANALYSE_LOCAL_FILE);
    GetLocalFileList((BYTE*)HISEXTREME_LOCAL_FILE);
    
    return;
}

void GetFileListInfoSpec(void)
{
    unlink(FILELIST);

    GetBackupFileList(s_ucHisDataBackupFileNum, s_atHisDataMag);

    GetLocalFileList((BYTE*)HISDATA_LOCAL_FILE);
}

char is_short_cut_alarm(T_HisAlarmSaveStruct *ptDisHisAlarm){
    return ptDisHisAlarm->wAlarmID == SHORT_CUT_ALARM_SAVE_ID;
}

/***************************************************************************
 * @brief    对存入的告警进行检查
 **************************************************************************/
void CheckAlarmSaved(T_HisAlarmSaveStruct *ptDisHisAlarm)
{
    /* 某些需求场景下，需要检查历史告警中是否存在某种告警
       在存入的时候检查本次存入的告警，并存储相关标志文件实现功能 */
    if (is_short_cut_alarm(ptDisHisAlarm))
    {
        WriteShortCutAlarmCheckFile(True);
    }
    return;
}

/***************************************************************************
 * @brief    将短路告警的存在状态写入文件
 **************************************************************************/
void WriteShortCutAlarmCheckFile(BOOLEAN bExist)
{
    T_AlarmCheckStruct tAlarmCheck;
    rt_memset(&tAlarmCheck, 0, sizeof(T_AlarmCheckStruct));

    if (bExist != True && bExist != False)
    {
        return;
    }

    readAlarmCheckFile(&tAlarmCheck);
    if (tAlarmCheck.bIfShortCutExist != bExist)
    {
        tAlarmCheck.bIfShortCutExist = bExist;
        writeAlarmCheckFile(&tAlarmCheck);
    }

    return;
}

//TODO: 临时使用，待完善
int StoreBduRecord(void *pData, int dataLen)
{
    WORD wCrc = 0;
    BYTE *pucData = (BYTE *)pData;

    if (pData == RT_NULL || dataLen < 0 || dataLen > BDU_RECORD_SIZE - sizeof(wCrc))
    {
        return -1;
    }

    wCrc = CRC_Cal(pucData, (WORD)dataLen);
    rt_memcpy(pucData + dataLen, &wCrc, sizeof(wCrc));

    s_bduRecordMgr.pucData = pucData;
    if (!saveOneRecord(&s_bduRecordMgr))
    {
        return -1;
    }
    setBduRecordBackUp(pucData, (dataLen + sizeof(wCrc)));

    return (dataLen + sizeof(wCrc));
}

int GetMcuResetReason(void)
{
    rt_device_t reset_cause_reporter = rt_device_find("reset_cause");
    int reset_cause = RESET_REASON_NULL;
    if (reset_cause_reporter != RT_NULL)
    {
        if (rt_device_control(reset_cause_reporter, 0, &reset_cause) < 0)
        {
            rt_kprintf("Failed to get reset reason.\n");
        }
    }
    rt_kprintf("reset cause: %d\n", reset_cause);
    return reset_cause;
}

BOOLEAN SaveMcuResetReason(int id)
{
    static const T_McuResetReasonMap tMcuResetReasonMap[] = {
        {RESET_REASON_POWER_POR, "Power-on reset"},
        {RESET_REASON_POWER_BOR, "Brownout reset"},
        {RESET_REASON_WWDT_OUT, "Watchdog reset"},
        {RESET_REASON_IWDT_OUT, "Independent WD"},
        {RESET_REASON_SOFTWARE, "Software1 reset"},
    };
    BOOLEAN ret = False;
    T_ActionRecord tAct = {0, };
    BYTE i = 0;
    WORD wActId = 0;

    time(&tAct.tTime);
    wActId = GetActionId(CONTOL_RST_SYS);
#ifdef DEVICE_USING_D121
    tAct.ucActId = wActId;
#else
    tAct.ucActId = (BYTE)(wActId & 0xFF);
#endif
    rt_strncpy_s(tAct.aucMsg, sizeof(tAct.aucMsg), "Other Reset Reason", sizeof(tAct.aucMsg) - 1);
    for (i = 0; i < ARRAY_SIZE(tMcuResetReasonMap); i++)
    {
        if (id == tMcuResetReasonMap[i].id)
        {
            rt_strncpy_s(tAct.aucMsg, sizeof(tAct.aucMsg), tMcuResetReasonMap[i].msg, sizeof(tAct.aucMsg) - 1);
            break;
        }
    }
    rt_kprintf("reset reason: %s\n", tAct.aucMsg);

    s_atHisRecord[0].pucData = (rt_uint8_t *)&tAct;
    ret = saveOneRecord(&s_atHisRecord[0]);
    s_atHisRecord[0].pucData = RT_NULL;
    return ret;
}


Static uint16_t MakeHisDataBattStatus(const T_BCMDataStruct *ptBcmData, const T_BCMAlarmStruct *ptBcmAlarm)
{
    RT_ASSERT(ptBcmData != NULL && ptBcmAlarm != NULL);

    // 电芯均衡启动状态
    uint16_t ret = (ptBcmData->ucCellEquSta << CELL_EQU_STA_BIT);
    
    // 电池充电保护
    ret |= (ptBcmData->ucChgProtSta << CHG_PROT_STA_BIT);
    
    // 电池放电保护
    ret |= (ptBcmData->ucDischgProtSta << DISCH_PROT_STA_BIT);
    
    // 限流状态
    ret |= (ptBcmData->ucLimit << LIMIT_STA_BIT);
    
    // BDU休眠状态
    ret |= (ptBcmData->ucBduSleep << BDU_SLEEP_STA_BIT);

    // BMS充放电状态
    ret |= (ptBcmData->ucBduStatus << BMS_CHGDISCH_STA_BIT);

    // 电池组过压保护
    ret |= (ptBcmAlarm->ucBattUnderVoltPrt << BATT_VOLL_PROT_BIT);
    
    // 电池组过压保护
    ret |= (ptBcmAlarm->ucBattOverVoltPrt << BATT_VOLH_PROT_BIT);
    
    // 电池组充电过流保护
    ret |= (ptBcmAlarm->ucChgCurrHighPrt << CHG_CURH_PROT_BIT);
    
    // 电池组放电过流保护
    ret |= (ptBcmAlarm->ucDischgCurrHighPrt << DISCH_CURH_PROT_BIT);

    return ret;
}



Static uint8_t MakeHisDataCellEvent(const T_BCMDataStruct *ptBcmData, const T_BCMAlarmStruct *ptBcmAlarm, uint8_t ucCellVoltNum)
{
    RT_ASSERT(ptBcmData != NULL && ptBcmAlarm != NULL && ucCellVoltNum <= CELL_VOL_NUM_MAX);

    uint8_t ret = 0;

    if (ptBcmData->ucMasterSta)
    {
        ret |= 0x01;
    }

    for (size_t i = 0; i < ucCellVoltNum; ++i)
    {
        if (ptBcmAlarm->aucCellDamagePrt[i])
        {
            ret |= 0x10;
        }
    }

    return ret;
}



Static uint8_t MakeHisDataVoltEvent(const T_BCMAlarmStruct *ptBcmAlarm, uint8_t ucCellVoltNum)
{
    RT_ASSERT(ptBcmAlarm != NULL && ucCellVoltNum <= CELL_VOL_NUM_MAX);

    uint8_t ret = 0;

    for (size_t i = 0; i < ucCellVoltNum; ++i)
    {
        if (ptBcmAlarm->aucCellOverVoltAlm[i])
        {
            ret |= 1;
        }
        if (ptBcmAlarm->aucCellOverVoltPrt[i])
        {
            ret |= 2;
        }
        if (ptBcmAlarm->aucCellUnderVoltAlm[i])
        {
            ret |= 4;
        }
        if (ptBcmAlarm->aucCellUnderVoltPrt[i])
        {
            ret |= 8;
        }
    }

    // 电池组过压报警和保护
    ret |= (ptBcmAlarm->ucBattOverVoltAlm << 4);
    ret |= (ptBcmAlarm->ucBattOverVoltPrt << 5);
    // 电池组欠压报警和保护
    ret |= (ptBcmAlarm->ucBattUnderVoltAlm << 6);
    ret |= (ptBcmAlarm->ucBattUnderVoltPrt << 7);

    return ret;
}



Static uint16_t MakeHisDataTempEvent(const T_BCMAlarmStruct *ptBcmAlarm, uint8_t ucCellTempNum)
{
    uint16_t ret = 0;

    RT_ASSERT(ptBcmAlarm != NULL && ucCellTempNum <= CELL_TEMP_NUM_MAX);

    ret = MakeHisDataCellTempEvent(ptBcmAlarm, ucCellTempNum);

    if (ptBcmAlarm->ucEnvTempHighAlm)
    {
        ret |= 0x100;
    }
    if (ptBcmAlarm->ucEnvTempHighPrt)
    {
        ret |= 0x200;
    }
    if (ptBcmAlarm->ucEnvTempLowAlm)
    {
        ret |= 0x400;
    }
    if (ptBcmAlarm->ucEnvTempLowPrt)
    {
        ret |= 0x800;
    }
    if (ptBcmAlarm->ucInsideTempHighPrt)
    {
        ret |= 0x1000;
    }
    if (ptBcmAlarm->ucBDUConnTempHighPrt)
    {
        ret |= 0x2000;
    }

    return ret;
}



Static uint16_t MakeHisDataCellTempEvent(const T_BCMAlarmStruct *ptBcmAlarm, uint8_t ucCellTempNum)
{
    RT_ASSERT(ptBcmAlarm != NULL && ucCellTempNum <= CELL_TEMP_NUM_MAX);

    uint16_t ret = 0;

    for (size_t i = 0; i < ucCellTempNum; ++i)
    {
        // 充电温度高报警和保护
        ret |= (ptBcmAlarm->aucChgTempHighAlm[i] << 0);
        ret |= (ptBcmAlarm->aucChgTempHighPrt[i] << 1);
        // 充电温度低报警和保护
        ret |= (ptBcmAlarm->aucChgTempLowAlm[i] << 2);
        ret |= (ptBcmAlarm->aucChgTempLowPrt[i] << 3);
        // 放电温度高报警和保护
        ret |= (ptBcmAlarm->aucDischgTempHighAlm[i] << 4);
        ret |= (ptBcmAlarm->aucDischgTempHighPrt[i] << 5);
        // 放电温度低报警和保护
        ret |= (ptBcmAlarm->aucDischgTempLowAlm[i] << 6);
        ret |= (ptBcmAlarm->aucDischgTempLowPrt[i] << 7);
    }

    return ret;
}



Static uint8_t MakeHisDataCurrEvent(const T_BCMAlarmStruct *ptBcmAlarm)
{
    RT_ASSERT(ptBcmAlarm != NULL);

    uint8_t ret = 0;

    // 充电电流高报警和保护
    ret |= (ptBcmAlarm->ucChgCurrHighAlm << 0);
    ret |= (ptBcmAlarm->ucChgCurrHighPrt << 1);
    // 放电电流高报警和保护
    ret |= (ptBcmAlarm->ucDischgCurrHighAlm << 2);
    ret |= (ptBcmAlarm->ucDischgCurrHighPrt << 3);
    // 电池短路
    ret |= (ptBcmAlarm->ucBattShortCut << 5);

    return ret;
}



Static uint8_t MakeHisDataCapacityEvent(const T_BCMAlarmStruct *ptBcmAlarm)
{
    RT_ASSERT(ptBcmAlarm != NULL);

    if (ptBcmAlarm->ucBattSOCLowAlm || ptBcmAlarm->ucBattSOCLowPrt)
    {
        return 4;
    }
    return 0;
}

