#ifndef BATTCHARGE_PRODUCT_H_
#define BATTCHARGE_PRODUCT_H_

#include "common.h"
#include "battery.h"

void EnterStandbyInit(T_BattDealInfoStruct *pBattDeal);
BOOLEAN BattSelfRechargeJudgement(T_BattInfo *pBattIn);
void JudgeChargeThrough(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
BOOLEAN IfShouldRecharge(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
void SetMasterOperFunc(void);
void SetSlaveOperFunc(void);
void GetMixOnOffVolt(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal, T_SysPara *pSysPara);

#ifdef INTELLIGENT_PEAK_SHIFTING
BOOLEAN JudgePeakShiftCharge(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal, T_PeakShiftPara *pPeakShift);
BOOLEAN JudgeFmChargeSwit(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal); //调频错峰充电控制
#endif

typedef void (*SetChgParaOper_t)(T_BattDealInfoStruct *ptBattDeal, FLOAT fChargeFullVol);
typedef void (*BMSChgButBDUDischgOper_t)(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo);
typedef void (*SmartLi_CalcSysMaxChargeCurrOper_t)(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo);
typedef void (*MultBattMixPowerOnAndOffVolt_t)(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo, FLOAT *pfPowerDownVol, FLOAT *pfPowerOffVol);
typedef void (*SmartLi_CalDischargeOutputVoltOper_t)(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo);
typedef void (*GetPowerdownVoltOper_t)(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);

extern SetChgParaOper_t SetChgParaOper;
extern BMSChgButBDUDischgOper_t BMSChgButBDUDischgOper;
extern SmartLi_CalcSysMaxChargeCurrOper_t SmartLi_CalcSysMaxChargeCurrOper;
extern MultBattMixPowerOnAndOffVolt_t MultBattMixPowerOnAndOffVolt;
extern SmartLi_CalDischargeOutputVoltOper_t SmartLi_CalDischargeOutputVoltOper;
extern GetPowerdownVoltOper_t GetPowerdownVoltOper;

#endif
