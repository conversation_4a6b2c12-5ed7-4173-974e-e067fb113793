#ifndef SOFTWARE_Frequency_MODULATION_H_
#define SOFTWARE_Frequency_MODULATION_H_

#include "common.h"

#ifdef __cplusplus
extern "C" {
#endif

#define FM_TRUE     0
#define FM_FALSE    1

/********************充放电禁止状态************************/
#define ENABLE_CHG_ENABLE_DISCHG        0
#define ENABLE_CHG_DISENABLE_DISCHG     1
#define DISENABLE_CHG_ENABLE_DISCHG     2
#define DISENABLE_CHG_DISENABLE_DISCHG  3


/********************调频模式************************/
#define BACKUP_POWER     0    //备电
#define FM_MODE          1    //调频
#define PEAK_SHIFT_MODE  2    //错峰


#define EXIT_FM_TIME    15    //退出调频模式时间，单位：分钟/min


//1363协议下发的指令数据
typedef struct 
{
    BOOLEAN ucFmStatus;                 // 当前调频状态:进入调频，退出调频
    FLOAT fFmPower;                  // 当前调频功率

}T_FmDistributestruct;


//1363协议上送调频实时数据的结构体
typedef struct 
{
    BYTE ucCurrentFMStatus;               // 当前调频状态  0:备电、1:调频、2:错峰
    BYTE ucChgAndDischgDisable;           // 充放电禁止状态  0:可充可放；1:可充不可放 2：可放不可充 3：不可充不可放
    BYTE wFMBattSOCLeftCapacity;          //站点剩余容量
    FLOAT fFMcurrentSingleMaxChgPower;    //单台电池最大充电功率  
    FLOAT fCurrentMaxChgPower;            // 当前电池最大充电功率

}T_FmSubmissionstruct;


/********************* 函数原型定义 ************************/
VOID InitFrequencyModulation(void);  //调频初始化
VOID GetFmDistributeData(T_FmDistributestruct *pFmDistributeData);  //获取铱控网关下发的数据
VOID SetFmDistributeData(T_FmDistributestruct *pFmDistributeData);   //设置铱控网关数据
BOOLEAN SetFmMode(BOOLEAN FmMode); //设置调频模式
BOOLEAN SetFmPower(FLOAT fPower);  //设置调频功率
BYTE MakeChgAndDischgStatus(BYTE ChgStatus,BYTE DischgStatus); //获取充放电禁止状态
BYTE MakeFMCurrentMode(BYTE ucFmModue,BYTE ucPeakShiftMode);   //获取调频模式
FLOAT GetSingleBatteryChargePower(FLOAT fChgCurrent,FLOAT fChgVoltage); //获取单体电池的最大充电功率
BYTE GetFmTimeCounter(void);      //获取退出调频时间计数
BOOLEAN SetFmTimeCounter(void);   //设置退出调频时间计数
BOOLEAN ClearFmTimeCounter(void); //清除退出调频时间计数
BOOLEAN IsFmMode(void);//获取调频模式
VOID GetFmRealData(T_FmSubmissionstruct *pFmRealData); //获取调频实时数据
VOID SetFmRealData(T_FmSubmissionstruct *pFmRealData); //设置调频实时数


#ifdef __cplusplus
}
#endif

#endif
