#ifndef _TEMP_H
#define _TEMP_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "gui_data_interface.h"

/* 此段为移植暂时解决编译所以引入*/
//菜单宏定义
#define	WND_MAIN		0
#define	WND_DATA		1
#define	WND_ALARM		2
#define	WND_PASER		3

#define	WND_SWITCH		4 
#define	WND_HELP		5 
#define	WND_TRACE		6 
#define	WND_SAVE_SCREEN	7 

#define	WND_PARA		8
#define	WND_SYSPARA	9
#define	WND_ADJUSTPARA	10
#define	WND_CONSOLE		11 
#define	WND_CTRL		12
#define	WND_DELETE		13
#define	WND_RESUME		14
#define	WND_DOWNLOAD	15

#define	WND_HISTORY		16
#define	WND_REC_ALARM	17
#define	WND_REC_ACTION	18
#define	WND_REC_DISCH	19
#define	WND_REC_PEAK	20

#define	WND_EDIT_LINE	21
#define	WND_EDIT_INT	22
#define	WND_EDIT_FLOAT	23
#define	WND_LISTBOX		24
#define	WND_MSGBOX		25
#define	WND_CONFIRM	26
#define	WND_ALERT		27

#define	WND_EDIT_TIME	28
#define	WND_PRI_ALARM	29
#define	WND_EDIT_STR	30
#define	WND_OUT_RELAY	31
#define	WND_OUT_SWITCH	32
#define	WND_INRELAY_CFG	33
#define	WND_ALMATRIBUTE	34

#define	WND_QUERY		35
#define	WND_TIME		36
#define	WND_VERSION	37
#define	WND_CTRL_RELAY	38

#define	WND_IDLE		250	
#define	WND_FOCUS		253	
#define	WND_PARENT		254	
#define	WND_NULL		255	
#define	MSG_NULL		0

#define	RELAY_NUM			4	//输入继电器个数
#define SWITCH_NUM      	12  //交流备用输出空开个数
#define	COMM_PORT_NUM	    2	// 串行口数量
#define LEN_RELAYNAME	    13
#define	RELAY_MAX			8
#define	LEN_PASER		    4

#define	COMM_PORT0			0			//前台RS485通讯口
#define	COMM_PORT1			1			//后台RS485/RS232通讯口

#define ID2_PR_VOLTHIGH		0x01	// 交流相电压高阈值
#define ID2_PR_VOLTLOW		0x02	// 交流相电压低阈值
#define ID2_PR_PHUNBL		0x03	// 相电压不平衡阈值
#define ID2_PR_CURRHIGH		0x06	// 交流电流高阈值
#define ID2_PR_CURRCFG		0x09	// 交流电流互感配置
#define ID2_PR_OUTSWCFG		0x0A	// 交流输出空开配置
#define ID2_PR_PPVOLTHIGH	0x11	// 交流相电压高阈值
#define ID2_PR_PPVOLTLOW	0x12	// 交流相电压低阈值
#define ID2_PR_CURRTRANSTYPE 0x13 	//交流互感类型
#define	ID2_PR_ACEMCFG  	 0x14	//交流电表配置
#define ID2_PR_VOLTOFFST	0x0D	// 相电压UΦ零点
#define ID2_PR_VOLTSLOPE	0x0E	// 相电压UΦ斜率
#define ID2_PR_CURROFFST	0x0F	// 相电流IΦ零点
#define ID2_PR_CURRSLOPE	0x10	// 相电流IΦ斜率
#define ID2_PR_RESNDINTVL	0xF2	// 主动上报间隔时间
#define ID2_PR_BUZZER		0xF5	// 蜂鸣器开关
#define ID2_PR_AUTOALM		0xF6	// 主动告警功能
#define ID2_PR_HISDATAINV	0xF9	// 历史数据保存时间间隔
#define ID2_PR_COMRATE		0xFB	// 串口波特率设置
#define ID2_PR_LOCALADD		0xFC	// 本机地址
#define ID2_PR_LANGUAGE		0xFD	// 语言设置
#define ID2_PR_PASERWORD	0xFE	// 菜单口令设置
#define ID2_PR_INRLYALMTTL	0xF0	// 输入干结点告警电平
#define ID2_PR_INRLYNAME	0xF1	// 输入干结点名称
#define ID2_PR_TIME		0xFF	// 系统时间设置

#define ID2_ALM_INRELAY		0xFE	// 输入干节点告警


#define ID1_ALARM		0x02	// 告警量ID1
#define ID1_PARA		0x03	// 参数量ID1
#define ID1_CTRL		0x04	// 控制量ID1
#define ID1_OUTRLY		0x06	// 输出干结点量ID1
#define ID1_STATUSOUTRLY	0x07	// 状态量对应输出干结点量
#define ID1_ACMU_PARA		0x13	// ACMU参数量ID1
#define ID1_ACMU_ALARM		0x12	// ACMU告警量ID1
#define ID1_ACMU_CTRL		0x14	// ACMU控制量ID1
#define ID1_ACMU_SETSTATUS		0x18	// ACMU可设状态量
#define ID1_INFOTYPE_MASK	0x0f	// 信息类型掩码(ID1低4位)

//  控制量信息ID2定义
#define ID2_CTL_DOWNZK		0xEF	// 下载字库
#define ID2_CTL_RESET	0xF3	// 系统复位
#define ID2_CTL_RLYACT	0xF4	// 输出干节点打开
#define ID2_CTL_RLYRESET	0xF5	// 输出干节点关闭
#define ID2_CTL_LDRUNPARA	0xFD	// 恢复默认运行参数
#define ID2_CTL_LDCFGPARA	0xFE	// 恢复默认配置参数
#define ID2_CTL_DOWMPROG	0xFF	// 下载程序

#define KEY_ESC      		0x37
#define KEY_ENTER       	0x2F
#define KEY_UP          	0x3D
#define KEY_DOWN        	0x3B
#define KEY_NOTHING    	 	0x3F
#define KEY_UP_DOWN     	0x39
#define KEY_UP_ENTER    	0x2D    
#define KEY_DOWN_ENTER	    0x2B 
#define KEY_LEFT_RIGHT_ENT  0x0E
#define KEY_LEFT        	0x1F	
#define KEY_RIGHT        	0x3E

//NVRAM中保存的实时告警的校验和地址
#define	NV_REAL_ALARM_CHECK	0
//NVRAM中保存的实时告警的起始地址
#define	NV_REAL_ALARM_ADDR  1	    //实时告警空间 size = 50*13 = 650 (最多支持50条告警)

//NVRAM中保存的历史告警头尾指针地址
#define	NV_HIS_ALARM_INDEX	( NV_REAL_ALARM_ADDR + 650 )
//NVRAM中保存的历史告警的起始地址
#define	NV_HIS_ALARM_ADDR	( NV_HIS_ALARM_INDEX + sizeof(T_BuffIndex) )      //历史告警空间每条历史告警22 bytes,每条历史事件19bytes


// DATAFLAG常量定义

#define PART_DATAFLAG1		0x00	// DATAFLAG1
#define DATAFLAG_EVENTCH	0x10	// 事件变化

#define SETTER_LOCAL		0x00		// 本地设置参数
#define SETTER_REMOTE	    0x01		// 远程设置参数

#define	ALL_PART		0
#define	AC_PART		    1
#define	ENV_PART		4

// 拨码开关定义
#define	DAIL1 0
#define	DAIL2 1
#define 	TYPE_INDEPEND 	0x04		// S1-bit2

//告警级别定义
#define	ALARMCLASS_MASK		0			// 屏蔽
#define	ALARMCLASS_CRITICAL	1			// 紧急
#define	ALARMCLASS_MAJOR		2		// 重要
#define	ALARMCLASS_MINOR		3		// 一般
#define	ALARMCLASS_WARNING	4			// 轻微

// #define SOFTWARE_NAME           "ZXDU88 S402 ACMU"  // 软件名称
// #define SOFTWARE_VER1            1    // 软件版本1
// #define SOFTWARE_VER2            20    // 软件版本2
// #define SOFTWARE_VER3            0    // 软件版本3
// #define SOFTWARE_VER4            9    // 软件版本4
// #define SOFTWARE_DATE_YEAR      2024             // 软件版本日期
// #define SOFTWARE_DATE_MONTH     3
// #define SOFTWARE_DATE_DAY       8
// #define CORPERATION_NAME        "ZTE Corporation"
// #define ACEMDEVICES             "DTSD3366D-A"


#define LEN_COMPANYNAME	20
#define LEN_SMNAME		30
#define LEN_RESERVED	13

#ifndef CTRL_ON
#define	CTRL_ON			0x01	
#endif
#ifndef CTRL_OFF
#define	CTRL_OFF		0x02
#endif
#ifndef CTRL_NULL
#define	CTRL_NULL		0x00	
#endif

#ifndef NULL
#define NULL 0
#endif

//定时器事件 
#define	TIMER_KEY			0			// 产生MSG_SCAN_KEY	
#define	TIMER_SAMPLE		1			// 产生MSG_SAMPLE 
#define	TIMER_CTRL			2			// 产生MSG_CTRL
#define	TIMER_COMM			3		// 产生MSG_COMM	
#define	TIMER_COMM_COUNTER	4	// 产生MSG_COMM_COUNTER	
#define	TIMER_RS485_COMM		5		// 产生MSG_SMR_CURR	
#define	TIMER_CHECK_PARA		6		// 产生MSG_CHECK_PARA 
#define	TIMER_CLOCK			7		// 产生MSG_CLOCK	
#define	TIMER_ONEHOUR		8			// 产生MSG_ONEHOUR 
#define	TIMER_HISALMCHK	9		// 产生HISALMCHK
#define	TIMER_LCD			10			// 延时关闭LCD背光 
#define	TIMER_SEND0			11			// 延时10ms打开UART0发送中断	
#define	TIMER_SEND1			12			// 延时10ms打开UART1发送中断	
#define	TIMER_REOPEN_BUZZ	13		// 延时30分钟打开蜂鸣器
#define	TIMER_SCREEN_SAVE	14		// 延时进入屏保菜单	
#define	TIMER_HISEVENTCHK	15			// 产生HISEVENTCHK
#define	TIMER_RESET	16					// 复位延迟计时器
#define	TIMER_HISTORY		17			// 延时产生MSG_HISTORY 
#define TIMER_REFRESH		18 //李霁霖2020-11-12：产生HISEVENTHK

//消息类型
#define	MSG_SCAN_KEY		0			// 扫描键盘
#define	MSG_SAMPLE		1			// 数据采集
#define	MSG_CTRL			2			// 输出控制
#define	MSG_COMM			3			// 通讯处理
#define	MSG_COMM_COUNTER	4		// 通讯模块计数器
#define	MSG_CHECK_PARA		5		// 检查RAM中参数和微调系数
#define	MSG_HISALMCHK		6		// 检查历史告警队列计数器
#define	MSG_CLOCK				7			// 时钟计时加1秒（考虑时钟芯片坏的情况）	
#define	MSG_SAVEHISTORY		8			// 保存历史数据和历史告警
#define	MSG_KEY_PRESSED		9		// 处理键值
#define	MSG_ALARM			10			// 处理键值
#define	MSG_DISPLAY			11			// 刷新显示
#define	MSG_BATTERY			12		// 执行电池管理
#define	MSG_SCREEN_SAVE		13		// 菜单模块计数器
#define	MSG_LCD				14			// 延时关闭LCD背光 
#define	MSG_BUZZ				15				// 延时控制蜂鸣器发声 
#define	MSG_SEND0			16			// 延时打开UART0发送中断	
#define	MSG_SEND1			17			// 延时打开UART1发送中断	
#define MSG_RS485_COMM		18			// RS485通讯
#define MSG_HISEVENTCHK		19			// 检查历史事件队列计数器
#define MSG_HUMCAL			20			// 计算湿度采样值
#define MSG_RESET				21			// 复位消息
#define	MSG_ONEHOUR			22			// 保存历史数据和历史告警
#define	MSG_REFRESH	        23          //李霁霖：2020-11-12：30s无按键刷新

#define	PERIODIC		0		
#define	DELAY			1		

#define DATAFLAG1		0x01
#define DATAFLAG2		0x02

#define	ISP_OFFSET		0xF50
#define	ISP485_OFFSET   0xEA6

// 参数类型
#define PARATYPE_NULL	0
#define PARATYPE_SYSTEM	1
#define PARATYPE_ATRRIBUTE	2
#define PARATYPE_INRELAY	3
#define PARATYPE_AC	4
#define PARATYPE_ENV	5
#define PARATYPE_ADJUST 6
#define PARATYPE_RUNNING	7
#define PARATYPE_CONFIG		8

#define	FILE_NAME_LEN	40
#define	FILE_TIME_LEN	20

#define	MODE_APP		0
#define	MODE_IAP		1



/*********************	时间数据结构  **********************/
typedef	struct
{
	unsigned int	wYear;					/* 年 */
	unsigned char	ucMonth;				/* 月 */
	unsigned char	ucDay;					/* 日 */
	unsigned char	ucHour;					/* 时 */
	unsigned char	ucMinute;				/* 分 */
	unsigned char	ucSecond;				/* 秒 */
}T_TimeStruct;

typedef struct
{
	unsigned char   uFlag;
	unsigned int	nCounter;
	unsigned int	nTotal;
	unsigned int	lFileLength;
	unsigned char	uFileName[20];
	char	uFileTime[20];
	unsigned char	uCrcHi;
	unsigned char	uCrcLo;
}T_EEpromISPSruct;

typedef struct
{
	unsigned int	usDataLenPerFrame;
	unsigned int	usTotalFrameNum;
	unsigned int	ulTotalFileLength;
	char	acFileName[FILE_NAME_LEN];
	char	acFileTime[FILE_TIME_LEN];
	unsigned int	usFileCheck;
	unsigned int	usResv;
}T_FileAttrStruct;

typedef struct
{
	unsigned char	ucFlag;
	unsigned int	usCounter;
	
	T_FileAttrStruct	tFileAttr;
	
	unsigned char	uCrcHi;
	unsigned char	uCrcLo;
}T_FileManageStruct;



// 厂家信息结构体定义
typedef struct
{
    char	acCompanyName[LEN_COMPANYNAME+1];		// 厂家名称
    char	acSMName[LEN_SMNAME+1];		// SM名称
    unsigned char aucVersion[4];		// SM软件版本
    unsigned int wReleaseYear;		// SM软件发布日期:年
    unsigned char ucReleaseMonth;	// SM软件发布日期:月
    unsigned char ucReleaseDay;		// SM软件发布日期:日
    char	acReserved[LEN_RESERVED];		// 预留
}T_FactoryInfo;

// 队列指针结构体
typedef struct
{
	unsigned int	wTop;
	unsigned int	wTail;
	unsigned int	awSendTail[COMM_PORT_NUM];
	unsigned int	awAckTail[COMM_PORT_NUM];
	unsigned int	wCRC;
}T_BuffIndex;

/************************************************************
** 结构名: T_SysPara
** 描  述: 系统参数数据结构定义
** 作  者: 潘奇银
** 日  期: 2008-01-11
** 版  本: V5.0 
** 修改记录			
** 日  期		版	本		修改人		修改摘要
** 
**************************************************************/
// typedef	struct	
// {
	
// 	// ACMU参数
// 	int		iAcVoltMax;					// 交流电压高阈值
// 	int		iAcVoltMin;					// 交流电压低阈值
// 	int		iAcVolUnbalance;				// 相不平衡电压阀值
// 	int		iAcCurrMax;					// 交流电流高阈值

// 	int		iAcVoltPPMax;					// 交流线电压高阈值
// 	int		iAcVoltPPMin;					// 交流线电压低阈值

// 	unsigned char 	abCurrCfg[3];					// 交流线电流IL12配置	TRUE: 配置　FALSE:未配置
// 	unsigned char 	abSwitchCfg[SWITCH_NUM];		// 交流空开配置	TRUE: 配置　FALSE:未配置
// 	int		aiAcVoltZero[3];				// 交流线电压UL12零点
// 	int		aiAcVoltSlope[3];				// 交流线电压UL12斜率
// 	int		aiAcCurrZero[3];				// 交流线电流IL12零点
// 	int		aiAcCurrSlope[3];				// 交流线电流IL12斜率
// 	unsigned int  	abCurrTransType[3];               //李霁霖-2020-11-16,交流互感类型
// 	unsigned char		abAcemCfg;						// 交流电表配置 TRUE: 配置　FALSE:未配置
	
// 	unsigned char		aucRlyAlarmTTL[RELAY_NUM];	// 输入干结点告警电平
// 	char		acRlyName[RELAY_NUM][LEN_RELAYNAME];	// 输入干结点名称
// 	//
// 	unsigned char 	bBeepOn;						// 蜂鸣器开关 TRUE 为开 FALSE为关
// 	unsigned char 	bAutoAlarm;					// 非MODEM组网方式主动告警功能使能 TRUE允许　 FALSE禁止
// 	unsigned char		ucHisDataInterval;				// 历史数据保存间隔时间
// 	unsigned char		aucCOMRate[COMM_PORT_NUM];		// COM波特率 110
// 	unsigned int		awDeviceAddr[COMM_PORT_NUM];				// 设备地址编号	***	
// 	unsigned int		awReportInterval[COMM_PORT_NUM];				// 主动上报间隔时间
// 	unsigned char		ucLanguage;					//语言版本
// 	char		acPaserword[LEN_PASER];		//操作口令
	

// 	unsigned char		aucRlyAlarmGrade[RELAY_NUM];	// 输入干结点告警级别
// 	unsigned char		aucAlmGrade[ALARM_CLASS_NUM];		// 告警级别
// 	unsigned char		aucAlmOutRly[ALARM_CLASS_NUM];		// 告警输出干结点
// 	// ENV参数	
// 	char		scEnvTempMax;		// 环境温度高告警值
// 	char		scEnvTempMin;			// 环境温度低告警值
// 	char		scEnvHumMax;			// 环境湿度高告警值
// 	char		scEnvHumMin;			// 环境湿度低告警值
// 	char		scEnvTempZero;		// 环境温度零点
// 	char		scEnvHumZero;			// 环境湿度零点
// 	// Ext参数
// 	unsigned char		aucHostAdd[COMM_PORT_NUM];				// 主机号
// }T_SysPara;	

typedef struct
{
	unsigned int          wTop;
	unsigned int          wTail;
	unsigned int		awSendTail[COMM_PORT_NUM];
	unsigned int		awAckTail[COMM_PORT_NUM];
	unsigned int           wCRC;
}T_HisEventBUFFIndex;

/************************************************************
** 结构名: T_AdjustDataStruct
** 描  述: 根据校准参数界面的零点和斜率计算的采样结果结构体
** 作  者: 潘奇银
** 日  期: 2008-10-21
** 版  本: V1.0 
** 修改记录			
** 日  期		版	本		修改人		修改摘要
** 
**************************************************************/
typedef struct
{
	char acResult[15];				//根据校准参数界面的零点和斜率计算的采样结果     			//Kw修改

}T_AdjustDataStruct;

extern unsigned char g_ucDownloadMode;
extern T_FileManageStruct	s_tFileManage;
extern T_EEpromISPSruct	tPrevISP;

void	InitLcd(void);
unsigned int    CRC_Cal(unsigned char *p, unsigned int wCount);
unsigned char	WriteNVRAM( unsigned long ulAddress, unsigned char	ucData );
void	MemCopyFromNVRAM( unsigned char *p, unsigned long ulOffset, unsigned char ucLen );
void    GetTime( time_base_t  * ptTime );
void    SetTime( T_TimeStruct  * ptTime );
void SetDataFlag_HDLC( unsigned char ucPort, unsigned char ucPart , unsigned char ucValue );
unsigned char SetSysPara( T_SysPara * ptSysPara, unsigned char bChk, unsigned char ucWhoSet );
const MIB_AlarmDataNode * GetAlarmNode( unsigned char ucAlarmSn );
unsigned char CheckDailCode( unsigned char ucDail, unsigned char unCode);
unsigned char	CalRealAlarmTotal( void );
T_HisAlarmStruct * GetDisHisAlarm(  unsigned int wIndex );
MIBTable_ParaNode * GetAlarmGradeNode( unsigned char ucAlarmSn );
MIBTable_ParaNode * GetAlmOutRlyNode( unsigned char ucAlarmSn );
unsigned char 	CheckTimeValid( T_TimeStruct  * ptTime );
void SetRS485DataFlag( unsigned char ucWhichFlag, unsigned char ucVal );
void	WrClk( time_base_t tTime );
void	DeleteHisAlarm( void );
void	DeleteHisDataRecord( void );
void    DeletPeakData( void );
unsigned char	WriteEEPROM( unsigned int wAddress, unsigned char ucData );
void	ResetMCU(void);
void	Delayms( unsigned int   wMillisecond );
void FeedWatchDog( void );
unsigned char	SetDummyTimer( unsigned char ucEvent, unsigned char ucType, unsigned long lTime, unsigned char ucMessage );

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _TEMP_H