/*
* @file    : bc<PERSON>_zte_can.c
* @brief   : BMU与BCMU北向CAN通信协议实现
* @details :
* <AUTHOR> 付振10303717
* @Date    : 2022-12-26
* @LastEditTime: 2023-04-06
* @version : V0.0.1
* @para    : Copyright (c)
*            ZTE Corporation
* @par     : History
*          2022-12-28, longmingxing, 增加设备实例初始化以及命令字段表
*     version: author, date, descn
*/

#include <string.h>
#include "cmd.h"
#include "sps.h"
#include "dev_bcmu.h"
#include "realdata_id_in.h"
#include "para_id_in.h"
#include "alarm_id_in.h"
#include "protocol_layer.h"
#include "protocol_short_comm.h"

/* 设备缓冲区长度 */
#define R_BUFF_LEN                    512     ///<  接收缓冲区长度
#define S_BUFF_LEN                    512    ///<  发送缓冲区长度
#define ANA_BYTE_LEN                  291
#define ANA_NUM                       13
#define DGI_BYTE_LEN                  10
#define DGI_NUM                       6
#define ALM_BYTE_LEN                  9
#define ALM_NUM                       3
#define FAC_BYTE_LEN                  200
#define FAC_NUM                       8
#define PRE_ASSIGN_BYTE_LEN           0x01
#define ASSIGN_PARA_NUM               0x01
#define ADDR_BYTE_LEN                 0x01
#define ADDR_NUM                      0x01
#define MANUFACTOR_DATA_LEN           162
#define MANUFACTOR_DATA_NUM           8
#define SOFTWARE_DATA_LEN             68
#define SOFTWARE_DATA_NUM             3
#define DEVICE_SN_LEN                 20
#define DEVICE_SN_NUM                 1
#define CELL_FAC_NAME_LEN             32
#define CELL_FACT_LEN                 4
#define PACK_BAR_CODE_LEN             20


/* 缺省值 */
#define BMU_SELF_CHECK_DEF_BYTE_NUM 1          ///< 缺省BMU自检状态量字节长度
#define BUM_SELF_CHECK_DEF_NUM 1               ///< 缺省BMU自检状态量数量
#define CMD_SET_CTRL_LEN              0x04
#define CMD_SET_CTRL_NUM              0x01
#define BMU_PLACE_HOLDER              0xFF     ///< BMU占位符，用于外部公司调试需要不跨帧的数据时进行占位

#define CMD_APPEND_REPLACEMENT  0x0001  ///< 备件替换附件码


/* 命令请求 */
static bottom_comm_cmd_head_t cmd_req[] = {
    /* 通信协议命令请求 */
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_MANUFACTOR_DATA, CMD_APPEND_GET_ALL, BOTTOM_RTN_APP_ACK},  //0

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA, CMD_APPEND_GET_ANA, BOTTOM_RTN_APP_ACK},  //1

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA, CMD_APPEND_GET_SWITCH, BOTTOM_RTN_APP_ACK},  //2

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA, CMD_APPEND_GET_ALARM, BOTTOM_RTN_APP_ACK}, //3

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL, CMD_APPEND_BATT_BALANCE, BOTTOM_RTN_APP_ACK},  //4 控制电芯均衡

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL, CMD_APPEND_RESET, BOTTOM_RTN_APP_ACK}, //5

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL, CMD_APPEND_ADDR_ASSIGN_PRE, BOTTOM_RTN_APP_NOACK},  //6

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL, CMD_APPEND_ADDR_BROAD_MOD, BOTTOM_RTN_APP_NOACK},  //7

    {BOTTOM_PROTO_TYPE_DOWN, CMD_DATA_TRIG, CMD_APPEND_UPDATE_REQ, BOTTOM_RTN_APP_ACK}, //8

    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_DATA, CMD_APPEND_NONE, BOTTOM_RTN_APP_ACK}, //9广播阶段不需要回复，补帧阶段需要回复

    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_DATA_COMPLETE_ACK, CMD_APPEND_UPDATE_REQ, BOTTOM_RTN_APP_ACK}, //10

    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_CONFIRM, CMD_APPEND_UPDATE_REQ, BOTTOM_RTN_APP_ACK}, //11

    {BOTTOM_PROTO_TYPE_COMM, CMD_TRIGGER_EXIT_QTP, CMD_TRIGGER, BOTTOM_RTN_APP_ACK}, //12 QTP触发

    {BOTTOM_PROTO_TYPE_COMM, CMD_TRIGGER_EXIT_QTP, CMD_EXIT, BOTTOM_RTN_APP_ACK}, //13 QTP退出

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_INFO_QTP, CMD_GET_ANA_DATA, BOTTOM_RTN_APP_ACK}, //14

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_INFO_QTP, CMD_GET_SWITCH_DATA, BOTTOM_RTN_APP_ACK}, //15

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_INFO_QTP, CMD_GET_ALARM_DATA, BOTTOM_RTN_APP_ACK}, //16

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_INFO_QTP, CMD_GET_SOFTWARE_DATA, BOTTOM_RTN_APP_ACK}, //17

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_INFO_QTP, CMD_GET_MANUFAC_DATA, BOTTOM_RTN_APP_ACK}, //18

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_INFO_QTP, CMD_SET_DEVICE_SN, BOTTOM_RTN_APP_ACK}, //19

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_INFO_QTP, CMD_SET_CELL_MANU_FACTRUER, BOTTOM_RTN_APP_ACK}, //20

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_INFO_QTP, CMD_SET_BATT_DATE_TIME, BOTTOM_RTN_APP_ACK}, //21

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_INFO_QTP, CMD_SET_CELL_CYCLE_TIMES, BOTTOM_RTN_APP_ACK}, //22

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_INFO_QTP, CMD_SET_PACK_BAR_CODE, BOTTOM_RTN_APP_ACK}, //23

    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_TRIG_OLD, CMD_APPEND_UPDATE_REQ, BOTTOM_RTN_APP_ACK}, //24

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_INFO_QTP, CMD_SET_SYS_NAME, BOTTOM_RTN_APP_ACK}, //25

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_INFO_QTP, CMD_SET_HARDWARE_VERSION, BOTTOM_RTN_APP_ACK}, //26

    {BOTTOM_PROTO_TYPE_COMM, CMD_SYNC_SOH_DATA, CMD_APPEND_NONE, BOTTOM_RTN_APP_ACK}, //27 BCMU向BMU同步SOH数据

    {BOTTOM_PROTO_TYPE_COMM, CMD_SYNC_SERIAL_NUM, CMD_APPEND_NONE, BOTTOM_RTN_APP_ACK}, //28 BCMU向BMU同步序列号

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_SOH_DATA, CMD_APPEND_REPLACEMENT, BOTTOM_RTN_APP_ACK}, //29 获取BMU存储的备件替换SOH数据

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_SERIAL_NUM, CMD_APPEND_REPLACEMENT, BOTTOM_RTN_APP_ACK}, //30 获取BMU存储的备件替换序列号

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_INFO_QTP, CMD_SET_CELL_SN, BOTTOM_RTN_APP_ACK}, //31 QTP设置电芯序列号

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL, CMD_APPEND_OPT_SHUTDOWN, BOTTOM_RTN_APP_ACK}, //32 控制优化器关机

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL, CMD_APPEND_OPT_ADDR_BROAD, BOTTOM_RTN_APP_NOACK},  //33 发送优化器地址
};

/* 命令应答   */
static bottom_comm_cmd_head_t cmd_ack[] = {
    /* 通信协议命令应答   */
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_MANUFACTOR_DATA, CMD_APPEND_GET_ALL, BOTTOM_RTN_APP_CORRECT},  //0

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA, CMD_APPEND_GET_ANA, BOTTOM_RTN_APP_CORRECT},  //1

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA, CMD_APPEND_GET_SWITCH, BOTTOM_RTN_APP_CORRECT},  //2

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA, CMD_APPEND_GET_ALARM, BOTTOM_RTN_APP_CORRECT}, //3

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL, CMD_APPEND_BATT_BALANCE, BOTTOM_RTN_APP_CORRECT},  //4 控制电芯均衡

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL, CMD_APPEND_RESET, BOTTOM_RTN_APP_ACK}, //5

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL, CMD_APPEND_ADDR_ASSIGN_PRE, BOTTOM_RTN_APP_CORRECT}, //6

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL, CMD_APPEND_ADDR_BROAD_MOD, BOTTOM_RTN_APP_CORRECT}, //7

    {BOTTOM_PROTO_TYPE_DOWN, CMD_DATA_TRIG, CMD_APPEND_UPDATE_ACK, BOTTOM_RTN_APP_CORRECT}, //8

    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_DATA, CMD_APPEND_NONE, BOTTOM_RTN_APP_CORRECT}, //9

    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_DATA_COMPLETE_ACK, CMD_APPEND_UPDATE_ACK, BOTTOM_RTN_APP_CORRECT}, //10

    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_CONFIRM, CMD_APPEND_UPDATE_ACK, BOTTOM_RTN_APP_CORRECT}, //11
    
    {BOTTOM_PROTO_TYPE_COMM, CMD_TRIGGER_EXIT_QTP, CMD_TRIGGER, BOTTOM_RTN_APP_CORRECT}, //12

    {BOTTOM_PROTO_TYPE_COMM, CMD_TRIGGER_EXIT_QTP, CMD_EXIT, BOTTOM_RTN_APP_CORRECT}, //13

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_INFO_QTP, CMD_GET_ANA_DATA, BOTTOM_RTN_APP_CORRECT}, //14

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_INFO_QTP, CMD_GET_SWITCH_DATA, BOTTOM_RTN_APP_CORRECT}, //15

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_INFO_QTP, CMD_GET_ALARM_DATA, BOTTOM_RTN_APP_CORRECT}, //16

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_INFO_QTP, CMD_GET_SOFTWARE_DATA, BOTTOM_RTN_APP_CORRECT}, //17

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_INFO_QTP, CMD_GET_MANUFAC_DATA, BOTTOM_RTN_APP_CORRECT}, //18

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_INFO_QTP, CMD_SET_DEVICE_SN, BOTTOM_RTN_APP_CORRECT}, //19

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_INFO_QTP, CMD_SET_CELL_MANU_FACTRUER, BOTTOM_RTN_APP_CORRECT}, //20

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_INFO_QTP, CMD_SET_BATT_DATE_TIME, BOTTOM_RTN_APP_CORRECT}, //21

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_INFO_QTP, CMD_SET_CELL_CYCLE_TIMES, BOTTOM_RTN_APP_CORRECT}, //22

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_INFO_QTP, CMD_SET_PACK_BAR_CODE, BOTTOM_RTN_APP_CORRECT}, //23

    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATE_TRIG_OLD, CMD_APPEND_UPDATE_ACK, BOTTOM_RTN_APP_CORRECT},//24

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_INFO_QTP, CMD_SET_SYS_NAME, BOTTOM_RTN_APP_CORRECT}, //25

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_INFO_QTP, CMD_SET_HARDWARE_VERSION, BOTTOM_RTN_APP_CORRECT}, //26

    {BOTTOM_PROTO_TYPE_COMM, CMD_SYNC_SOH_DATA, CMD_APPEND_NONE, BOTTOM_RTN_APP_CORRECT}, //27 BCMU向BMU同步SOH数据

    {BOTTOM_PROTO_TYPE_COMM, CMD_SYNC_SERIAL_NUM, CMD_APPEND_NONE, BOTTOM_RTN_APP_CORRECT}, //28 BCMU向BMU同步序列号

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_SOH_DATA, CMD_APPEND_REPLACEMENT, BOTTOM_RTN_APP_CORRECT}, //29 获取BMU存储的备件替换SOH数据

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_SERIAL_NUM, CMD_APPEND_REPLACEMENT, BOTTOM_RTN_APP_CORRECT}, //30 获取BMU存储的备件替换序列号

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_INFO_QTP, CMD_SET_CELL_SN, BOTTOM_RTN_APP_CORRECT}, //31 QTP设置电芯序列号

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL, CMD_APPEND_OPT_SHUTDOWN, BOTTOM_RTN_APP_CORRECT}, //32 控制优化器关机

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL, CMD_APPEND_OPT_ADDR_BROAD, BOTTOM_RTN_APP_NOACK},  //33 发送优化器地址
};


/* 工商储-模拟量信息 */

static data_info_id_verison_t cmd_ana_data_info[] = {
    {SEG_LEN,  type_unsigned_short,  ARRAY_SIZE_1,      DOP_0, DATA_DROP, ANA_BYTE_LEN, NOT_NEED_INTERACT},               /// 模拟量字节长度
    {SEG_NUM,  type_unsigned_short,  ARRAY_SIZE_1,      DOP_0, DATA_DROP, ANA_NUM,      NOT_NEED_INTERACT},               /// 模拟量数量
    {SEG_DATA, type_unsigned_char,   ARRAY_SIZE_1,      DOP_0, DATA_KEEP, BATT_MOD_CELL_NUM, NOT_NEED_INTERACT},          /// 电芯数量
    {SEG_DATA, type_short,           CELL_TEMP_NUM,     DOP_1, DATA_DROP, 300, NEED_INTERACT, BMU_DATA_ID_CELL_TEMP},     /// 电芯温度
    {SEG_DATA, type_short,           BATT_MOD_CELL_NUM, DOP_3, DATA_DROP, 350, NEED_INTERACT, BMU_DATA_ID_CELL_VOLT},     /// 电芯电压
    {SEG_DATA, type_short,           ARRAY_SIZE_1,      DOP_1, DATA_DROP, 300, NEED_INTERACT, BMU_DATA_ID_BATT_TEMP},     /// 单板温度
    {SEG_DATA, type_short,           ARRAY_SIZE_5,      DOP_1, DATA_DROP, 300, NEED_INTERACT, BMU_DATA_ID_EQUA_RESISTOR_TEMP},   /// 均衡电阻温度
    {SEG_DATA, type_short,           ARRAY_SIZE_1,      DOP_1, DATA_DROP, 300, NEED_INTERACT, BMU_DATA_ID_BATT_POS_TEMP},        /// 电池功率端正极温度
    {SEG_DATA, type_short,           ARRAY_SIZE_1,      DOP_1, DATA_DROP, 300, NEED_INTERACT, BMU_DATA_ID_BATT_NEG_TEMP},        /// 电池功率端负极温度
    {SEG_DATA, type_int,             ARRAY_SIZE_1,      DOP_3, DATA_DROP, 20800, NEED_INTERACT, BMU_DATA_ID_BATT_MOD_VOLT},      /// 电池模块电压
    {SEG_DATA, type_short,           ARRAY_SIZE_1,      DOP_3, DATA_DROP, 4160, NEED_INTERACT, BMU_DATA_ID_CELL_MOD_VOL1},    /// 电芯模组电压1
    {SEG_DATA, type_short,           ARRAY_SIZE_1,      DOP_3, DATA_DROP, 4160, NEED_INTERACT, BMU_DATA_ID_CELL_MOD_VOL2},    /// 电芯模组电压2
    {SEG_DATA, type_short,           ARRAY_SIZE_1,      DOP_3, DATA_DROP, 4160, NEED_INTERACT, BMU_DATA_ID_CELL_MOD_VOL3},    /// 电芯模组电压3
    {SEG_DATA, type_short,           ARRAY_SIZE_1,      DOP_3, DATA_DROP, 4160, NEED_INTERACT, BMU_DATA_ID_CELL_MOD_VOL4},    /// 电芯模组电压4
    {SEG_DATA, type_short,           ARRAY_SIZE_1,      DOP_3, DATA_DROP, 4160, NEED_INTERACT, BMU_DATA_ID_CELL_MOD_VOL5},    /// 电芯模组电压5
};


/* 工商储-开关量命令字段信息 */

static data_info_id_verison_t cmd_dig_data_info[] = {
    {SEG_LEN,  type_unsigned_short,  ARRAY_SIZE_1,      DOP_0, DATA_DROP, DGI_BYTE_LEN, NOT_NEED_INTERACT},  /// 开关量字节长度
    {SEG_NUM,  type_unsigned_short,  ARRAY_SIZE_1,      DOP_0, DATA_DROP, DGI_NUM, NOT_NEED_INTERACT},       /// 开关量数量
    {SEG_DATA, type_unsigned_char,   ARRAY_SIZE_1,      DOP_0, DATA_KEEP, BATT_MOD_CELL_NUM, NOT_NEED_INTERACT},                            /// 电芯数量
    {SEG_DATA, type_bit,             BATT_MOD_CELL_NUM, DOP_0, DATA_DROP,  NO_DEF_VALUE, NEED_INTERACT, BMU_DATA_ID_CELL_BLC_STA},          /// 电芯均衡状态
    {SEG_DATA, type_bit,             ARRAY_SIZE_1,      DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_DATA_ID_FIRE_MODULE_OPER_STAT},  /// 消防模块工作状态
    {SEG_DATA, type_bit,             ARRAY_SIZE_1,      DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_DATA_ID_MSD_CONNECTION},         /// MSD通断状态
    {SEG_DATA, type_bit,             ARRAY_SIZE_1,      DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_DATA_ID_OPT_POWER_STA},          /// 优化器电源状态
};


/* 工商储-告警量命令字段信息 */

static data_info_id_verison_t cmd_alm_data_info[] = {
    {SEG_LEN,  type_unsigned_short,  ARRAY_SIZE_1,      DOP_0, DATA_DROP, ALM_BYTE_LEN, NOT_NEED_INTERACT},       /// 告警量字节长度
    {SEG_NUM,  type_unsigned_short,  ARRAY_SIZE_1,      DOP_0, DATA_DROP, ALM_NUM, NOT_NEED_INTERACT},            /// 告警量数量
    {SEG_DATA, type_bit,             ARRAY_SIZE_1,      DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_DATA_ID_FIRE_TRIG_STA},              /// 消防模块告警
    {SEG_DATA, type_bit,             CELL_TEMP_NUM,     DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_DATA_ID_TEMP_DET_FAULT_STA},         /// 温度检测异常告警
    {SEG_DATA, type_bit,             ARRAY_SIZE_5,      DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_DATA_ID_SAM_CHIP_FAULT_STA},         /// 电压检测异常告警
};


/* 工商储-厂家字段信息 */

static data_info_id_verison_t cmd_fac_data_info[] = {
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1,      DOP_0, DATA_DROP, FAC_BYTE_LEN, NOT_NEED_INTERACT},                                 /// 厂家信息字节长度
    {SEG_NUM,  type_unsigned_short, ARRAY_SIZE_1,      DOP_0, DATA_DROP, FAC_NUM, NOT_NEED_INTERACT},                                      /// 厂家信息字段数量
    {SEG_SYSTEM_PARA, type_string, BMU_CELL_FAC_NAME, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_CELL_FAC_NAME_OFFSET},    /// 电芯厂家名称 
    {SEG_SYSTEM_PARA, type_string, BMU_CELL_VER,      DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_CELL_VER_OFFSET},         /// 电芯版本 
    {SEG_SYSTEM_PARA, type_date,   ARRAY_SIZE_1,      DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_CELL_FACT_DATE_OFFSET},   /// 电芯出厂日期 
    {SEG_SYSTEM_PARA, type_string, BMU_CELL_SERIES_NUM_LEN,  DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_CELL_SN_OFFSET},   /// 电芯序列号 
    {SEG_SYSTEM_PARA, type_string, BMU_SYS_NAME_LEN,  DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_SYS_NAME_OFFSET},         /// BMU系统名称
    {SEG_DATA, type_string, BMU_SOFT_VER, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_DATA_ID_SOFT_VER},                            /// BMU软件版本
    {SEG_DATA, type_date, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_DATA_ID_SOFT_REL_DATE},                         /// BMU软件发布日期
    {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_32,DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_HARDWARE_VERSION_OFFSET},     /// 硬件版本
};


/* 地址字段信息 */
static data_info_id_verison_t cmd_addr_broad_info[] = {
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, ADDR_BYTE_LEN, NOT_NEED_INTERACT},  ///< 参数字节数
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ADDR_NUM, NOT_NEED_INTERACT},       ///< 参数数量
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_DATA_ID_ASSIGN_HOST_ADDR},      ///< 本机地址
};

/* 地址字段信息 */
static data_info_id_verison_t cmd_opt_addr_broad_info[] = {
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, ADDR_BYTE_LEN, NOT_NEED_INTERACT},  ///< 参数字节数
    {SEG_NUM,  type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, ADDR_NUM, NOT_NEED_INTERACT},       ///< 参数数量
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_DATA_ID_OPT_ADDRESS},      ///< 优化器地址
};

/* 同步序列号信息 */
static data_info_id_verison_t cmd_sync_serial_num_info[] = {
    {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_32,DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_BCMU_SN_OFFSET},  /// BCMU序列号
    {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_32,DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_REPLACE_CELL_SN1_OFFSET},    /// 备件替换电芯序列号1
    {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_32,DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_REPLACE_CELL_SN2_OFFSET},    /// 备件替换电芯序列号2
    {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_32,DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_REPLACE_CELL_SN3_OFFSET},    /// 备件替换电芯序列号3
    {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_32,DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_REPLACE_CELL_SN4_OFFSET},    /// 备件替换电芯序列号4
};

/* 同步SOH数据 */
static data_info_id_verison_t cmd_sync_soh_data_info[] = {
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 15, NOT_NEED_INTERACT},  ///< SOH数据字节长度
    {SEG_NUM,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, 5, NOT_NEED_INTERACT},                   ///< SOH数据数量
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_CALENDER_LIFE_DECLINE_OFFSET},  ///< 日历衰减
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_CYCLE_LIFE_DECLINE_OFFSET},     ///< 循环衰减
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_ADDED_LIFE_DECLINE_OFFSET},     ///< 附加衰减
    {SEG_SYSTEM_PARA, type_time, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_LAST_CALC_CALENDER_TIME_OFFSET}, ///< 上次计算日历寿命衰减的时间
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_BATT_RATE_CAP_OFFSET}, ///< 电池额定容量
};

/* 获取软件版本 */
static data_info_id_verison_t cmd_software_data_info_qtp[] = {
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, SOFTWARE_DATA_LEN, NOT_NEED_INTERACT},  ///< 参数字节数
    {SEG_NUM,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, SOFTWARE_DATA_NUM,      NOT_NEED_INTERACT},       /// 参数数量
    {SEG_SYSTEM_PARA, type_string, BMU_SYS_NAME_LEN,  DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_SYS_NAME_OFFSET},  ///< BMU系统名称
    {SEG_DATA, type_string, BMU_SOFT_VER, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_DATA_ID_SOFT_VER},  /// BMU软件版本
    {SEG_DATA, type_date, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_DATA_ID_SOFT_REL_DATE},  /// BMU软件发布日期
};

/* 获取厂家信息 */
static data_info_id_verison_t cmd_manufactor_data_info_qtp[] = {
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, MANUFACTOR_DATA_LEN, NOT_NEED_INTERACT},  ///< 参数字节数
    {SEG_NUM,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, MANUFACTOR_DATA_NUM,      NOT_NEED_INTERACT},       /// 参数数量
    {SEG_SYSTEM_PARA, type_string, BMU_CELL_FAC_NAME, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_CELL_FAC_NAME_OFFSET},    /// 电芯厂家名称 
    {SEG_SYSTEM_PARA, type_string, BMU_CELL_VER,      DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_CELL_VER_OFFSET},         /// 电芯版本 
    {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_20,DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_BMU_WHOLE_DEV_SN_OFFSET},               /// 整机序列号 
    {SEG_SYSTEM_PARA, type_date,   ARRAY_SIZE_1,      DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_CELL_FACT_DATE_OFFSET},                 /// 电芯出厂日期 
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1,      DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_CELL_CYCLE_TIMES_OFFSET}, /// 电芯循环次数 
    {SEG_SYSTEM_PARA, type_string,   BMU_PACK_BAR_CODE_LEN,DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_PACK_BAR_CODE_OFFSET}, /// pack bar code 
    {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_20,DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_BMU_SN_OFFSET},               /// BMU单板序列号
    {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_32,DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_HARDWARE_VERSION_OFFSET},               /// 硬件版本 
};

/* 设置BMU序列号 */
static data_info_id_verison_t cmd_device_sn_qtp[] = {
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, DEVICE_SN_LEN, NOT_NEED_INTERACT},  ///< 参数字节数
    {SEG_NUM,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, DEVICE_SN_NUM,      NOT_NEED_INTERACT},       /// 参数数量
    {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_20,DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_BMU_WHOLE_DEV_SN_OFFSET},        /// 整机序列号 
};

/* 设置电芯序列号 */
static data_info_id_verison_t cmd_cell_sn_qtp[] = {
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, ARRAY_SIZE_32, NOT_NEED_INTERACT},  ///< 参数字节数
    {SEG_NUM,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, DEVICE_SN_NUM,      NOT_NEED_INTERACT},       /// 参数数量
    {SEG_SYSTEM_PARA, type_string,  ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_CELL_SN_OFFSET},        /// 电芯序列号
};

/* 设置电芯厂家名称 */
static data_info_id_verison_t cmd_cell_fac_name_qtp[] = {
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, CELL_FAC_NAME_LEN, NOT_NEED_INTERACT},  ///< 参数字节数
    {SEG_NUM,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, DEVICE_SN_NUM,      NOT_NEED_INTERACT},       /// 参数数量
    {SEG_SYSTEM_PARA, type_string, BMU_CELL_FAC_NAME, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_CELL_FAC_NAME_OFFSET},    /// 电芯厂家名称 
};

/* 设置电芯出厂日期 */
static data_info_id_verison_t cmd_cell_fact_qtp[] = {
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, CELL_FACT_LEN, NOT_NEED_INTERACT},  ///< 参数字节数
    {SEG_NUM,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, DEVICE_SN_NUM,      NOT_NEED_INTERACT},       /// 参数数量
    {SEG_SYSTEM_PARA, type_date,   ARRAY_SIZE_1,      DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_CELL_FACT_DATE_OFFSET},                 /// 电芯出厂日期 
};

/* 设置电芯循环次数 */
static data_info_id_verison_t cmd_cell_cycle_times_qtp[] = {
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, DEVICE_SN_LEN, NOT_NEED_INTERACT},  ///< 参数字节数
    {SEG_NUM,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, DEVICE_SN_NUM,      NOT_NEED_INTERACT},       /// 参数数量
    {SEG_SYSTEM_PARA, type_unsigned_short,   ARRAY_SIZE_1,DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_CELL_CYCLE_TIMES_OFFSET},     /// 电芯循环次数 
};

/* 设置pack bar code */
static data_info_id_verison_t cmd_pack_bar_code_qtp[] = {
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, PACK_BAR_CODE_LEN, NOT_NEED_INTERACT},  ///< 参数字节数
    {SEG_NUM,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, DEVICE_SN_NUM,      NOT_NEED_INTERACT},       /// 参数数量
    {SEG_SYSTEM_PARA, type_string, BMU_PACK_BAR_CODE_LEN,DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_PACK_BAR_CODE_OFFSET}, /// pack bar code 
};

/* 设置BMU系统名称 */
static data_info_id_verison_t cmd_sys_name_qtp[] = {
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, BMU_SYS_NAME_LEN, NOT_NEED_INTERACT},  ///< 参数字节数
    {SEG_NUM,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, DEVICE_SN_NUM,      NOT_NEED_INTERACT},       /// 参数数量
    {SEG_SYSTEM_PARA, type_string, BMU_SYS_NAME_LEN,DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_SYS_NAME_OFFSET}, /// BMU系统名称
};

/* 设置硬件版本 */
static data_info_id_verison_t cmd_hardware_version_qtp[] = {
    {SEG_LEN,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, ARRAY_SIZE_32, NOT_NEED_INTERACT},  ///< 参数字节数
    {SEG_NUM,  type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, DEVICE_SN_NUM,      NOT_NEED_INTERACT},       /// 参数数量
    {SEG_SYSTEM_PARA, type_string, ARRAY_SIZE_32,DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMU_PARA_ID_HARDWARE_VERSION_OFFSET}, /// 硬件版本
};

static cmd_parse_info_id_verison_t cmd_parse_info[] = {
    {&cmd_addr_broad_info[0],      sizeof(cmd_addr_broad_info)/sizeof(data_info_id_verison_t)},       //0
    {&cmd_device_sn_qtp[0],        sizeof(cmd_device_sn_qtp)/sizeof(data_info_id_verison_t)},         //1 QTP设置整机序列号
    {&cmd_cell_fac_name_qtp[0],    sizeof(cmd_cell_fac_name_qtp)/sizeof(data_info_id_verison_t)},     //2 QTP设置电芯厂家名称
    {&cmd_cell_fact_qtp[0],        sizeof(cmd_cell_fact_qtp)/sizeof(data_info_id_verison_t)},         //3 QTP设置电芯出厂日期
    {&cmd_cell_cycle_times_qtp[0], sizeof(cmd_cell_cycle_times_qtp)/sizeof(data_info_id_verison_t)},  //4 QTP设置电芯循环次数
    {&cmd_pack_bar_code_qtp[0],    sizeof(cmd_pack_bar_code_qtp)/sizeof(data_info_id_verison_t)},     //5 QTP设置pack bar code
    {&cmd_sys_name_qtp[0],         sizeof(cmd_sys_name_qtp)/sizeof(data_info_id_verison_t)},          //6 QTP设置BMU系统名称
    {&cmd_hardware_version_qtp[0], sizeof(cmd_hardware_version_qtp)/sizeof(data_info_id_verison_t)},  //7 QTP设置硬件版本
    {&cmd_sync_serial_num_info[0], sizeof(cmd_sync_serial_num_info)/sizeof(data_info_id_verison_t)},  //8 BCMU同步序列号
    {&cmd_sync_soh_data_info[0],   sizeof(cmd_sync_soh_data_info)/sizeof(data_info_id_verison_t)},    //9 BCMU同步SOH数据
    {&cmd_cell_sn_qtp[0],          sizeof(cmd_cell_sn_qtp)/sizeof(data_info_id_verison_t)},           //10 QTP设置电芯序列号
};

static cmd_parse_info_id_verison_t cmd_pack_info[] = {
    {&cmd_addr_broad_info[0], sizeof(cmd_addr_broad_info)/sizeof(data_info_id_verison_t)},  //0

    {&cmd_ana_data_info[0],   sizeof(cmd_ana_data_info)/sizeof(data_info_id_verison_t)}, //1

    {&cmd_dig_data_info[0],   sizeof(cmd_dig_data_info)/sizeof(data_info_id_verison_t)}, //2

    {&cmd_alm_data_info[0],   sizeof(cmd_alm_data_info)/sizeof(data_info_id_verison_t)}, //3

    {&cmd_fac_data_info[0],   sizeof(cmd_fac_data_info)/sizeof(data_info_id_verison_t)},  //4

    {&cmd_software_data_info_qtp[0], sizeof(cmd_software_data_info_qtp)/sizeof(data_info_id_verison_t)}, //5

    {&cmd_manufactor_data_info_qtp[0], sizeof(cmd_manufactor_data_info_qtp)/sizeof(data_info_id_verison_t)}, //6

    {&cmd_sync_serial_num_info[0], sizeof(cmd_sync_serial_num_info)/sizeof(data_info_id_verison_t)},  //7 BMU上送保存的备件替换序列号

    {&cmd_sync_soh_data_info[0],   sizeof(cmd_sync_soh_data_info)/sizeof(data_info_id_verison_t)},    //8 BMU上送保存的备件替换SOH数据

    {&cmd_opt_addr_broad_info[0], sizeof(cmd_opt_addr_broad_info)/sizeof(cmd_opt_addr_broad_info)},  //9 发送优化器地址
};


/* 通信协议命令表 */
static cmd_t no_poll_cmd_tab[] = {
    {BMU_GET_MANUFACTOR_DATA,    CMD_PASSIVE,   &cmd_req[0], &cmd_ack[0], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[4], NULL},
    {BMU_GET_ANA_DATA,           CMD_PASSIVE,   &cmd_req[1], &cmd_ack[1], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[1], NULL},
    {BMU_GET_SWITCH_DATA,        CMD_PASSIVE,   &cmd_req[2], &cmd_ack[2], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[2], NULL},
    {BMU_GET_ALARM_DATA,         CMD_PASSIVE,   &cmd_req[3], &cmd_ack[3], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[3], NULL},
    {BMU_CTRL_CELL_BALANCE,       CMD_PASSIVE,  &cmd_req[4], &cmd_ack[4], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, NULL},
    {BMU_CTRL_RESET,             CMD_PASSIVE,   &cmd_req[5], &cmd_ack[5], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, NULL},
    {BMU_ADDR_ALLOCATION_PREPARE,CMD_BROADCAST, &cmd_req[6], &cmd_ack[6], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, NULL},
    {BMU_ADDR_ALLOCATION,        CMD_BROADCAST, &cmd_req[7], &cmd_ack[7], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[0], &cmd_parse_info[0]},
    {BMU_UPDATE_DATA_TRIG,       CMD_PASSIVE,   &cmd_req[8], &cmd_ack[8], sizeof(bottom_comm_cmd_head_t), },
    {BMU_UPDATE_DATA,            CMD_PASSIVE,   &cmd_req[9], &cmd_ack[9], sizeof(bottom_comm_cmd_head_t), },
    {BMU_UPDATE_DATA_COMPLETE_ACK, CMD_PASSIVE, &cmd_req[10], &cmd_ack[10], sizeof(bottom_comm_cmd_head_t), },
    {BMU_UPDATE_CONFIRM,          CMD_PASSIVE,  &cmd_req[11], &cmd_ack[11], sizeof(bottom_comm_cmd_head_t), },
    {BMU_QTP_TRIGGER,            CMD_PASSIVE,   &cmd_req[12], &cmd_ack[12], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, NULL},
    {BMU_QTP_EXIT,               CMD_PASSIVE,   &cmd_req[13], &cmd_ack[13], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, NULL},
    {BMU_QTP_GET_ANA_DATA,       CMD_PASSIVE,   &cmd_req[14], &cmd_ack[14], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[1], NULL},
    {BMU_QTP_GET_SWITCH_DATA,    CMD_PASSIVE,   &cmd_req[15], &cmd_ack[15], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[2], NULL},
    {BMU_QTP_GET_ALARM_DATA,     CMD_PASSIVE,   &cmd_req[16], &cmd_ack[16], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[3], NULL},
    {BMU_QTP_GET_SOFTWARE_DATA,  CMD_PASSIVE,   &cmd_req[17], &cmd_ack[17], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[5], NULL},
    {BMU_QTP_GET_MANUFACTOR_DATA,CMD_PASSIVE,   &cmd_req[18], &cmd_ack[18], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[6], NULL},
    {BMU_QTP_SET_DEVICE_SN,      CMD_PASSIVE,   &cmd_req[19], &cmd_ack[19], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[1]},
    {BMU_QTP_SET_CELL_MANU_FACTRUER,CMD_PASSIVE,   &cmd_req[20], &cmd_ack[20], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[2]},
    {BMU_QTP_SET_BATT_DATE_TIME, CMD_PASSIVE,   &cmd_req[21], &cmd_ack[21], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[3]},
    {BMU_QTP_SET_CELL_CYCLE_TIMES,  CMD_PASSIVE,   &cmd_req[22], &cmd_ack[22], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[4]},
    {BMU_QTP_SET_PACK_BAR_CODE,  CMD_PASSIVE,   &cmd_req[23], &cmd_ack[23], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[5]},
    {BMU_UPDATE_TRIG_OLD,        CMD_PASSIVE,   &cmd_req[24], &cmd_ack[24], sizeof(bottom_comm_cmd_head_t), NULL, NULL},
    {BMU_QTP_SET_SYS_NAME,       CMD_PASSIVE,   &cmd_req[25], &cmd_ack[25], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[6]},
    {BMU_QTP_SET_HARDWARE_VERSION,  CMD_PASSIVE,   &cmd_req[26], &cmd_ack[26], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[7]},
    {BMU_SYNC_SOH_DATA,          CMD_PASSIVE,   &cmd_req[27], &cmd_ack[27], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[9]},
    {BMU_SYNC_SERIAL_NUM,        CMD_PASSIVE,   &cmd_req[28], &cmd_ack[28], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[8]},
    {BMU_GET_SOH_DATA,           CMD_PASSIVE,   &cmd_req[29], &cmd_ack[29], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[8], NULL},
    {BMU_GET_SERIAL_NUM,         CMD_PASSIVE,   &cmd_req[30], &cmd_ack[30], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[7], NULL},
    {BMU_QTP_SET_CELL_SN,      CMD_PASSIVE,   &cmd_req[31], &cmd_ack[31], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, &cmd_parse_info[10]},
    {BMU_CONTROL_OPT_SHUTDOWN,  CMD_PASSIVE,   &cmd_req[32], &cmd_ack[32], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, NULL, NULL},
    {BMU_CTR_BROAD_OPT_ADDRESS, CMD_BROADCAST, &cmd_req[33], &cmd_ack[33], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[9], NULL},
    {0},
};


static dev_type_t dev_bcmu = {
    DEV_BCMU, 1, PROTOCOL_BOTTOM_COMM, BMU_LINK_BCMU, R_BUFF_LEN, S_BUFF_LEN, BOTTOM_BCMU_TYPE, &no_poll_cmd_tab[0], NULL
};


dev_type_t* init_dev_bcmu(void) {
    return &dev_bcmu;
}

