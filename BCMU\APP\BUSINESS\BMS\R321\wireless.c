/**************************************************************************
* Copyright (C) 2010-2011, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有  
* 其他说明：
***************************************************************************/
#include "common.h"
#include "sample.h"
#include "realAlarm.h"
#include "battery.h"
#include "utils_heart_beat.h"
#include "thread_id.h"
#include "para.h"
#include "protocol.h"
#include "stdio.h"
#include "wireless.h"
#include "ism330dhcx.h"
#include "prtclWireless.h"
#include "fileSys.h"
#include <rtdevice.h>
#include <drv_at.h>
#include <at_device.h>
#include "utils_rtthread_security_func.h"

/***********************  变量定义  ************************/
rt_device_t dev;
struct at_device_info_4g at_device_info_4g_s = {0};
struct gps_location gps_location_s = {0};
static rt_mutex_t s_ptMutexEG21;

Static T_Mc20Manage s_tMc20Manage;
Static T_SmsSendBuff s_tSmsSendBuff;
static T_GpsData s_tGpsCalDisData; //用于计算距离基准GPS
Static T_GpsData s_tGpsData;
static T_SysPara s_tSysPara;
Static T_Mg21Status s_tMg21Status;
Static BOOLEAN s_bMG21State = 0; //MG21模块状态
Static BOOLEAN s_bApptestGprsStat = FAULT; //apptest中GPRS状态信息
Static T_IMEI s_tIMEI;
Static T_IMSI s_tIMSI;
Static BYTE s_ucCntApptest = 0;
Static BYTE s_uctimer = 0;
Static BYTE s_ucState = 0;

Static BYTE s_ucLocateModeType = 0;            // 当前模块支持哪几种类型
Static BYTE s_ucLocateMode_apptest = GPS_MODE; // APPTEST测试时的模式
Static BYTE s_ucLocateMode_Now = GPS_MODE;     // 当前定位方式

static BOOLEAN s_bflagRun = FALSE;
Static T_WirelessCheck s_tWirelessCheck;
static char s_acPhoneBuf[30];
Static T_GPSFaultStatus s_tGPSFaultStatus;
Static T_GPSPositionStruct s_tGPSPos = {0};
Static T_GPSDirectionStruct s_tGpsDirect = {0};

/********************* 静态函数原型定义 ************************/
Static void setStage(T_ModelStage ucStage);
Static void ProcessGps(void);
Static void ProcessSms(void);
Static void InitGprsAndGps(void);
Static char* getPhoneP(BYTE ucIndex);
Static BOOLEAN parseGpsData(void);
Static FLOAT ChangeTitudeByDirect(FLOAT fTitude, BYTE ucDirect);
Static void checkSignalQuality(void);
Static BOOLEAN Reset4GDeviceHardware(void);
Static BOOLEAN Open4GDeviceDriver(void);
Static BOOLEAN CheckGpsModuleStatus(void);
Static BOOLEAN SetGpsFaultStatus(BYTE status);
Static BOOLEAN JudgeFaultGpsStatus(BYTE status, BYTE *faultType);

Static BOOLEAN sendOneSms( char* pcPhone);
Static void sendSmsOver(void);
Static BOOLEAN SaveGpsInfo(void);
/***************************************************************/
BYTE GetLocateModeType(void)
{
    return s_ucLocateModeType;
}

Static BOOLEAN CheckLocateMode(BYTE ucLocateMode)
{
    BYTE ucMax = (ucLocateMode == 3)?3:ucLocateMode;
    BYTE ucMin = (ucLocateMode == 3)?1:ucLocateMode;

    if(s_tSysPara.ucLocateMode < ucMin || s_tSysPara.ucLocateMode > ucMax)
    {
        BYTE ucDefLocateMode = getDefLocateMode();
        if(ucDefLocateMode >= ucMin && ucDefLocateMode <= ucMax)
        {
            s_tSysPara.ucLocateMode = ucDefLocateMode;
        }
        else
        {
            s_tSysPara.ucLocateMode = GetValidData(s_tSysPara.ucLocateMode, ucMax, ucMin);
        }
        SetSysPara(&s_tSysPara, True, CHANGE_BY_LOCATE_INIT);
    }

    return TRUE;
}


BOOLEAN SetLocateMode(BYTE ucLocateMode, BOOLEAN bIsInit)
{
    rt_err_t result = RT_ERROR;

    if (ucLocateMode == 0 || s_ucLocateModeType <= BEIDOU_ONLY) // 没插模块或者模块只支持一种定位方式
    {
        s_ucLocateMode_Now = ucLocateMode;
        s_ucLocateMode_apptest = ucLocateMode;
        return TRUE;
    }

    if (bIsInit == False && s_ucLocateMode_Now == ucLocateMode) // 不是初始化时，如果配置的定位方式与原定位方式相同，则返回
    {
        return TRUE;
    }

    rt_mutex_take(s_ptMutexEG21, RT_WAITING_FOREVER);

    switch (ucLocateMode)
    {
        case GPS_MODE:
            result = rt_device_control(dev, AT_DEVICE_CTRL_CHANGE_TO_GPS, RT_NULL); // 配置定位方式为GPS
            break;
        case BEIDOU_MODE:
            result = rt_device_control(dev, AT_DEVICE_CTRL_CHANGE_TO_BEIDOU, RT_NULL); // 配置定位方式为北斗
            break;
        case MIX_MODE:
            result = rt_device_control(dev, AT_DEVICE_CTRL_CHANGE_TO_GPS_BD_MIX, RT_NULL); // 配置定位方式为混合
            break;
        default:
            break;
    }

    rt_mutex_release(s_ptMutexEG21);

    if (result == RT_EOK)
    {
        s_ucLocateMode_Now = ucLocateMode;
        s_ucLocateMode_apptest = ucLocateMode;
        return TRUE;
    }

    return FALSE;
}



Static void InitGprsAndGps(void)
{
    rt_mutex_take(s_ptMutexEG21, RT_WAITING_FOREVER);
    rt_device_control(dev, AT_DEVICE_CTRL_GET_GPS_SUPPORT_TYPE, &gps_location_s);
    rt_mutex_release(s_ptMutexEG21);
    s_ucLocateModeType = gps_location_s.gps_support_type;
    CheckLocateMode(s_ucLocateModeType);
    SetLocateMode(s_tSysPara.ucLocateMode, TRUE);

    rt_mutex_take(s_ptMutexEG21, RT_WAITING_FOREVER);
    // 获取SIM卡在位状态
    rt_device_control(dev, AT_DEVICE_CTRL_GET_SIM_STATUS, &at_device_info_4g_s);
    rt_mutex_release(s_ptMutexEG21);
    s_tMg21Status.bSIMCardOk = (BOOLEAN)at_device_info_4g_s.sim_status;  // 正常
    s_bMG21State = TRUE;
}


BOOLEAN SendSms(char *pBuf)
{
    char *p;
    LONG wIndex;
    
    p = s_tSmsSendBuff.atSmsData[s_tSmsSendBuff.slSmsSendTail].auSmsData;
    wIndex = (s_tSmsSendBuff.slSmsSendTail + 1) % MAX_SMS_SEND;
    if (wIndex == s_tSmsSendBuff.slSmsSendTop) //发送队列满
    {
        return False;
    }
    rt_memcpy(p, pBuf, MAX_TX_HZ * 2);
    s_tSmsSendBuff.slSmsSendTail = wIndex;

    return True;
}

Static void setStage(T_ModelStage ucStage)
{
    s_tMc20Manage.ucStage = ucStage;
    return;
}

Static void ProcessGps(void)
{
    rt_err_t result = RT_EOK;

    rt_mutex_take(s_ptMutexEG21, RT_WAITING_FOREVER);
    result = rt_device_control(dev,AT_DEVICE_CTRL_GET_SIM_STATUS,&at_device_info_4g_s);   //获取SIM卡在位状态
    rt_mutex_release(s_ptMutexEG21);
    if(result == RT_EOK)
    {
        s_tMg21Status.bSIMCardOk = (BOOLEAN)at_device_info_4g_s.sim_status;  //正常
    }
    else
    {
        s_tMg21Status.bSIMCardOk = False;
    }

    if (s_tIMEI.bIMEIGet == False)
    {
        rt_mutex_take(s_ptMutexEG21, RT_WAITING_FOREVER);
        result = rt_device_control(dev,AT_DEVICE_CTRL_GET_DEV_NUM ,&at_device_info_4g_s);
        rt_mutex_release(s_ptMutexEG21);
        if(result == RT_EOK)
        {
            s_tIMEI.bIMEIGet = True;
            rt_memcpy(s_tIMEI.acIMEI, at_device_info_4g_s.dev_num, IMEI_LEN); // IMEI码实际长度为15
        }
    }

    rt_mutex_take(s_ptMutexEG21, RT_WAITING_FOREVER);
    result = rt_device_control(dev,AT_DEVICE_CTRL_GET_IMSI ,&at_device_info_4g_s);
    rt_mutex_release(s_ptMutexEG21);
    if(result == RT_EOK)
    {
        s_tIMSI.bIMSIGet = True;
        rt_memcpy(s_tIMSI.acIMSI, at_device_info_4g_s.imsi, IMSI_LEN);
    }
    else
    {
        s_tIMSI.bIMSIGet = False;
    }

    rt_mutex_take(s_ptMutexEG21, RT_WAITING_FOREVER);
    result = rt_device_control(dev, AT_DEVICE_CTRL_GET_CONNECT_STATUS, &at_device_info_4g_s);
    rt_mutex_release(s_ptMutexEG21);
    if(result == RT_EOK)
    {
        if(at_device_info_4g_s.connt_status == 1)
        {
            s_bApptestGprsStat = NORMAL;
        }
    }

    JudgeFaultGpsStatus(result, &s_tGPSFaultStatus.ucGetConnectStatusCnt);
    
    if (s_tSmsSendBuff.slSmsSendTail != s_tSmsSendBuff.slSmsSendTop) //有短信需要发送
    {
        setStage(SMS_STAGE);
        return;
    }

    rt_mutex_take(s_ptMutexEG21, RT_WAITING_FOREVER);
    result = rt_device_control(dev, AT_DEVICE_CTRL_GET_GPS, &gps_location_s);
    rt_mutex_release(s_ptMutexEG21);
    if(result == RT_EOK)
    {
        parseGpsData();
    }
    else
    {
        s_tMc20Manage.bGetPlace = False;
    }

    result = rt_device_control(dev, AT_DEVICE_CTRL_GET_SNR, &gps_location_s);
    if(result == RT_EOK)
    {
        s_tMg21Status.ucSNR = gps_location_s.gpssnr;
    }
    else
    {
        s_tMg21Status.ucSNR = 0;
    }

    JudgeFaultGpsStatus(result, &s_tGPSFaultStatus.ucGetGpsStatusCnt);

    rt_mutex_take(s_ptMutexEG21, RT_WAITING_FOREVER);
    result = rt_device_control(dev, AT_DEVICE_CTRL_GET_SIGNAL_POWER, &at_device_info_4g_s);
    rt_mutex_release(s_ptMutexEG21);
    if(result == RT_EOK)
    {
        s_tMg21Status.ucSignalQuality = (BYTE)at_device_info_4g_s.signal_power;
        checkSignalQuality();
    }
    else
    {
        s_tMg21Status.ucSignalQuality = 0;
        
    }

    JudgeFaultGpsStatus(result, &s_tGPSFaultStatus.ucGetSignalPowerCnt);

    return;
}

Static void checkSignalQuality(void)
{
    if (s_tMg21Status.ucSignalQuality == CSQ_SIGNAL_FAULT)
    {
        s_tWirelessCheck.wSignalFaultCnt++;
        if (s_tWirelessCheck.wSignalFaultCnt >= CSQ_SIGNAL_FAULT_RESET_COUNTER_MAX)
        {
            s_tWirelessCheck.bFaultInitFlag = True;
        }
    }
    else
    {
        s_tWirelessCheck.wSignalFaultCnt = 0;
    }
    return;
}

BOOLEAN getWirelessFaultInitFlag(void)
{
    return s_tWirelessCheck.bFaultInitFlag;
}

Static BOOLEAN sendOneSms( char* pcPhone)
{
    rt_err_t result = RT_EOK;
    struct sms_msg sms_msg_t = {0};
    
    if (NULL == pcPhone || rt_strnlen(pcPhone, 20) < 5)
    {
        return FALSE;
    }
    
    sms_msg_t.data = s_tSmsSendBuff.atSmsData[s_tSmsSendBuff.slSmsSendTop].auSmsData;
    sms_msg_t.len = rt_strnlen_s(sms_msg_t.data, sizeof(s_tSmsSendBuff.atSmsData[s_tSmsSendBuff.slSmsSendTop].auSmsData));
    rt_memcpy((&sms_msg_t.phone_num), pcPhone, sizeof(sms_msg_t.phone_num));

    rt_mutex_take(s_ptMutexEG21, RT_WAITING_FOREVER);
    result = rt_device_control(dev,AT_DEVICE_CTRL_SEND_SMS_MSG,&sms_msg_t);
    rt_mutex_release(s_ptMutexEG21);

    return result;
}

Static void sendSmsOver(void)
{
    char *p = NULL;

    if (GetApptestFlag() == True)
    {
        sendOneSms(s_acPhoneBuf);
        return;
    }

    for (BYTE i=0; i<5; i++)
    {
        p = getPhoneP( i );
        sendOneSms( p );
        rt_thread_delay( 500 );
    }

    return;
}

Static void ProcessSms(void)
{
    while (s_tSmsSendBuff.slSmsSendTail != s_tSmsSendBuff.slSmsSendTop) //发送队列空
    {
        sendSmsOver();
        s_tSmsSendBuff.slSmsSendTop  = (s_tSmsSendBuff.slSmsSendTop + 1) % MAX_SMS_SEND;
    }

    setStage(GPS_STAGE);
    return;
}

Static char* getPhoneP(BYTE ucIndex)
{
    GetSysPara(&s_tSysPara);
    switch (ucIndex)
    {
        case 0:
            return (char*)s_tSysPara.acAlmPhoneNum_1;
        case 1:
            return (char*)s_tSysPara.acAlmPhoneNum_2;
        case 2:
            return (char*)s_tSysPara.acAlmPhoneNum_3;
        case 3:
            return (char*)s_tSysPara.acAlmPhoneNum_4;
        case 4:
            return (char*)s_tSysPara.acAlmPhoneNum_5;
    }
    return NULL;
}


BOOLEAN getCurrentPlace(T_GpsData* ptGps)
{
    if (s_tMc20Manage.bGetPlace == True)
    {
        rt_memcpy(ptGps, &s_tGpsData, sizeof(T_GpsData));
        return True;
    }
    return False;
}



BOOLEAN getPlaceForApptest(T_GpsData* ptGps)
{
    if(s_ucLocateModeType == s_ucState)     //测试的定位结果和模块支持的模式匹配
    {
        rt_memcpy(ptGps, &s_tGpsData, sizeof(T_GpsData));
        return True;
    }
    return False;
}


FLOAT calcDistance(T_GpsData* ptGps1, T_GpsData* ptGps2)
{
    double fTmp, fLa1, fLa2, fLo1, fLo2;

    fLa1 = (PI * ptGps1->fLatitude) / 180;        //转换成弧度
    fLa2 = (PI * ptGps2->fLatitude) / 180;
    fLo1 = (PI * ptGps1->fLongtitude) / 180;
    fLo2 = (PI * ptGps2->fLongtitude) / 180;

    fTmp = cos(fLa1) * cos(fLa2) + sin(fLa1) * sin(fLa2) * cos(fLo1 - fLo2);

    return (FLOAT) EARTH_RADIUS * acos(fTmp);
}

Static BOOLEAN parseGpsData(void)
{
    LONG lTmp = 0;
    T_GpsData tGps;

    rt_memset_s(&tGps, sizeof(T_GpsData), 0, sizeof(T_GpsData));

    //UTC时间
    lTmp = (LONG)(1 * strtod(gps_location_s.time, NULL));//hhmmss.sss
    tGps.ucHour   = lTmp / 10000;
    tGps.ucMinute = lTmp % 10000 / 100;
    tGps.ucSecond = (lTmp % 100);

    s_tGpsData.ucHour   = tGps.ucHour;
    s_tGpsData.ucMinute = tGps.ucMinute;
    s_tGpsData.ucSecond = tGps.ucSecond;

    lTmp = (LONG)(1000 * gps_location_s.latitude_num);
    tGps.fLatitude = (FLOAT)(lTmp % 100000) / 60000 + lTmp / 100000;  //ddmm.mmm转换成dd.ddddd

    if (gps_location_s.latitude[0] ==  'N')
    {
        tGps.ucLatiDirect = 0;
    }
    else if (gps_location_s.latitude[0] ==  'S')
    {
        tGps.ucLatiDirect = 1;
    }

    lTmp = (LONG)(1000 * gps_location_s.longitude_num);

    tGps.fLongtitude = (FLOAT)(lTmp % 100000) / 60000 + lTmp / 100000;

    // 经度方向
    if (gps_location_s.longitude[0] == 'E')
    {
        tGps.ucLongDirect = 0;
    }
    else if (gps_location_s.longitude[0] == 'W')
    {
        tGps.ucLongDirect = 1;
    }
 
    //方向角
    tGps.fAzimuth = gps_location_s.cog;

    //对地速度，节
    tGps.fSpeed = gps_location_s.spkn;

    //日期
    lTmp = (LONG)atoi(gps_location_s.date);         //ddmmyy 示例：080820

    tGps.ucDay   = (BYTE)(lTmp / 10000);
    tGps.ucMonth = (BYTE)(lTmp % 10000 / 100);
    tGps.wYear   = (WORD)(lTmp % 100 + 2000);

    //卫星数量
    tGps.ucStarNum = gps_location_s.nsat;
    s_tMg21Status.ucStarNum = gps_location_s.nsat;

    if (!s_tMc20Manage.bGetPlace)
    {
        s_tMc20Manage.bGetPlace = True;
    }

    tGps.bGpsLocated = s_tMc20Manage.bGetPlace;

    rt_memcpy_s(&s_tGpsData, sizeof(T_GpsData), &tGps, sizeof(T_GpsData));   //用于上送的数据

    checkGps();

    return True;
}

void checkGps(void)
{
    FLOAT fTmp = 0.0, fLongtitude = 0.0, fLatitude = 0.0;
    FLOAT fTmpSave = 0.0;
    char aucSMSData[MAX_TX_HZ*2];
    char aucBuf[16];
    T_BmsPACKFactoryStruct tBmsFactInfo;
    T_GpsData tGPSPosTmp = {0};

    if (False == s_tMc20Manage.bGetPlace)
    {
        return;
    }

    fTmp = calcDistance(&s_tGpsCalDisData, &s_tGpsData);

    tGPSPosTmp.fLatitude = s_tGPSPos.fLatitude;
    tGPSPosTmp.ucLatiDirect = s_tGpsDirect.ucLatiDirect;
    tGPSPosTmp.fLongtitude = s_tGPSPos.fLongtitude;
    tGPSPosTmp.ucLongDirect = s_tGpsDirect.ucLongDirect;
    fTmpSave = calcDistance(&tGPSPosTmp, &s_tGpsData);

    if(fTmpSave > 0.2)
    {
        SaveGpsInfo();
    }

    GetSysPara(&s_tSysPara);
    if (fTmp > (FLOAT)s_tSysPara.wGPSAntiTheftDistance / 1000 )
    {
        rt_memset_s(&tBmsFactInfo, sizeof(T_BmsPACKFactoryStruct), 0, sizeof(T_BmsPACKFactoryStruct));
        readBmsPackFacInfo(&tBmsFactInfo);
        rt_memcpy_s(aucBuf, sizeof(aucBuf), tBmsFactInfo.acBmsFacSn, sizeof(tBmsFactInfo.acBmsFacSn));
        aucBuf[sizeof(aucBuf) - 1] = 0;
        fLongtitude = ChangeTitudeByDirect(s_tGpsData.fLongtitude, s_tGpsData.ucLongDirect);
        fLatitude = ChangeTitudeByDirect(s_tGpsData.fLatitude, s_tGpsData.ucLatiDirect);
        if(fabs(s_tGpsCalDisData.fLatitude) < 0.000001 && fabs(s_tGpsCalDisData.fLongtitude) < 0.000001)//初次定位上，不置振动告警
        {
            rt_snprintf_s(aucSMSData, MAX_TX_HZ*2, "Batt SN:%s, StarNum: %d, current place: %f, %f", aucBuf, s_tGpsData.ucStarNum, fLatitude, fLongtitude);
        }
        else
        {
            rt_snprintf_s(aucSMSData, MAX_TX_HZ*2, "Batt SN:%s, move %fkm, current place: %f, %f", aucBuf, fTmp, fLatitude, fLongtitude);
            setShakeAlm(TRUE);
        }
        SendSms(aucSMSData);

        rt_memcpy_s(&s_tGpsCalDisData, sizeof(T_GpsData), &s_tGpsData, sizeof(T_GpsData));
    }
    return;
}

Static FLOAT ChangeTitudeByDirect(FLOAT fTitude, BYTE ucDirect)
{
    fTitude = (ucDirect == 1) ? (-1 * fTitude) : fTitude;
    return fTitude;
}

void TestSms(char *pcPhoneNum)
{
    char acBuf[LEN_TYPE_STRING_20] = {0};
    char aucSMSData[MAX_TX_HZ*2] = {0};
    T_BmsPACKFactoryStruct tBmsFactInfo;

    rt_memcpy_s(s_acPhoneBuf, LEN_TYPE_STRING_20, pcPhoneNum, LEN_TYPE_STRING_20);
    s_acPhoneBuf[LEN_TYPE_STRING_20 - 1] = 0;

    rt_memset_s(&tBmsFactInfo, sizeof(T_BmsPACKFactoryStruct), 0, sizeof(T_BmsPACKFactoryStruct));
    rt_memset_s(&acBuf, sizeof(acBuf), 0, sizeof(acBuf));

    readBmsPackFacInfo(&tBmsFactInfo);
    rt_memcpy_s(acBuf, LEN_TYPE_STRING_20, tBmsFactInfo.acBmsFacSn, sizeof(tBmsFactInfo.acBmsFacSn));
    acBuf[LEN_TYPE_STRING_20 - 1] = 0;
    rt_snprintf_s(aucSMSData, MAX_TX_HZ*2, "BattSn:%s", acBuf);
    SendSms(aucSMSData);
}

void GPSSendSMS()
{
    FLOAT fLongtitude = 0.0, fLatitude = 0.0;
    char aucSMSData[MAX_TX_HZ*2]={0};
    char aucBuf[16]={0};
    char aucBuf2[33]={0};
    T_BmsPACKFactoryStruct tBmsFactInfo;
    rt_memset_s(aucSMSData, sizeof(aucSMSData), 0, sizeof(aucSMSData));

    if (False == s_tMc20Manage.bGetPlace)
    {
        rt_snprintf_s(aucSMSData, MAX_TX_HZ*2,"Not Search Star");
		SendSms(aucSMSData);
        return;
    }
    fLongtitude = ChangeTitudeByDirect(s_tGpsData.fLongtitude, s_tGpsData.ucLongDirect);
    fLatitude = ChangeTitudeByDirect(s_tGpsData.fLatitude, s_tGpsData.ucLatiDirect);
    GetSysPara(&s_tSysPara);

    rt_memset_s(&tBmsFactInfo, sizeof(T_BmsPACKFactoryStruct), 0, sizeof(T_BmsPACKFactoryStruct));
    readBmsPackFacInfo(&tBmsFactInfo);

    rt_memcpy_s(aucBuf, sizeof(tBmsFactInfo.acBmsFacSn), tBmsFactInfo.acBmsFacSn, sizeof(tBmsFactInfo.acBmsFacSn));
    aucBuf[sizeof(aucBuf) - 1] = 0;

    rt_memcpy_s(aucBuf2, sizeof(s_tSysPara.acDeviceName), s_tSysPara.acDeviceName, sizeof(s_tSysPara.acDeviceName));
    aucBuf2[sizeof(aucBuf2) - 1] = 0;

    rt_snprintf_s( aucSMSData, MAX_TX_HZ*2, "Site Name:%s, Batt SN: %s, StarNum: %d, current place: %f, %f", aucBuf2,aucBuf, s_tGpsData.ucStarNum, fLatitude, fLongtitude);
    SendSms(aucSMSData);

    return;
}

BOOLEAN GetFlagRun(void)
{
    return s_bflagRun;
}


Static BOOLEAN SetLocateModeInApptest(BOOLEAN bFirst)
{
    BYTE ucRetryCnt = 0;

    if (s_ucLocateMode_apptest >= MIX_MODE)
    {
        s_ucLocateMode_apptest = GPS_MODE;
    }
    else if (bFirst == TRUE)
    {
        return FALSE;
    }

    while (SetLocateMode(s_ucLocateMode_apptest, FALSE) == FALSE)
    {
        if (++ucRetryCnt > 3)
        {
            s_uctimer = TRY_MAX;
            break;
        }
    }

    s_tMc20Manage.bGetPlace = FALSE;
    return TRUE;
}



Static BOOLEAN SwitchLocateMode(void)
{
    SetLocateModeInApptest(TRUE);
    s_uctimer++;
    if(s_uctimer >= TRY_MAX || s_tMc20Manage.bGetPlace == TRUE)
    {
        s_uctimer = 0;
        s_ucState = s_ucState | (s_tMc20Manage.bGetPlace<<(s_ucLocateMode_apptest-1));
        if(s_ucLocateModeType <= BEIDOU_ONLY)
        {
            s_ucCntApptest = 2;             //APPTEST 模式下，定位模式最多只切换两次
            return TRUE;
        }
        s_ucLocateMode_apptest++;
        SetLocateModeInApptest(FALSE);
        s_ucCntApptest++;
    }
    return TRUE;
}


Static BOOLEAN Reset4GDeviceHardware(void)
{
    int ireset = 1;
    if(dev == NULL)
    {
        return FALSE;
    }
    rt_mutex_take(s_ptMutexEG21, RT_WAITING_FOREVER);
    rt_device_control(dev, AT_DEVICE_CTRL_RESET, &ireset);
    rt_thread_delay(30000);         //重启后延时30s，等待驱动重启
    Open4GDeviceDriver();
    rt_mutex_release(s_ptMutexEG21);
    return TRUE;
}

Static BOOLEAN Open4GDeviceDriver(void)
{
#ifndef UNITEST
    dev = rt_device_find("p0");
    while(dev == NULL)
    {
        rt_thread_delay(1000);
        dev = rt_device_find("p0");
        s_ucLocateModeType = 0;
        CheckLocateMode(s_ucLocateModeType);
    }

    struct at_device_class *class = RT_NULL;
    rt_uint16_t dev_id = at_get_class_id();
    class = at_device_class_get(dev_id);
    while(class == RT_NULL || dev_id == RT_NULL)
    {
        rt_thread_delay(1000);
        TimerPlus(s_tGPSFaultStatus.ucGetDeviceClassCnt, CSQ_GPS_FAULT_COUNTER_MAX);
        if(TimeOut(s_tGPSFaultStatus.ucGetDeviceClassCnt, CSQ_GPS_FAULT_COUNTER_MAX))
        {
            SetGpsFaultStatus(True);
        }
        dev_id = at_get_class_id();
        class = at_device_class_get(dev_id);
        s_ucLocateModeType = 0;
        CheckLocateMode(s_ucLocateModeType);
    }
#endif
    return TRUE;
}

void mg21_CommTask(void* parameter)
{
    rt_thread_delay( 30000 );
    s_bflagRun = TRUE;
    GetSysPara(&s_tSysPara);
    rt_memset_s(&s_tIMEI, sizeof(s_tIMEI), 0, sizeof(s_tIMEI));
    rt_memset_s(&s_tIMSI, sizeof(s_tIMEI), 0, sizeof(s_tIMSI));
    rt_memset_s(&s_tGpsCalDisData, sizeof(T_GpsData), 0, sizeof(T_GpsData));
    rt_memset_s(&s_tMg21Status, sizeof(s_tMg21Status), 0, sizeof(s_tMg21Status));

    rt_memset_s(&s_tMc20Manage, sizeof(s_tMc20Manage), 0, sizeof(s_tMc20Manage));
    rt_memset_s(&s_tSmsSendBuff, sizeof(s_tSmsSendBuff), 0, sizeof(s_tSmsSendBuff));
    rt_memset_s(&s_tGPSFaultStatus, sizeof(T_GPSFaultStatus), 0, sizeof(T_GPSFaultStatus));
    rt_memset_s(&s_tWirelessCheck, sizeof(s_tWirelessCheck), 0, sizeof(s_tWirelessCheck));

    s_tMc20Manage.bGetPlace = False;

#ifndef UNITEST
    Open4GDeviceDriver();

    s_ptMutexEG21 = rt_mutex_create("eg21_lock", RT_IPC_FLAG_PRIO);
#endif

    InitGprsAndGps();
    readGPSLocation(&s_tGPSPos);
    readGPSDirection(&s_tGpsDirect);

#ifndef UNITEST
    rt_thread_t tidWireless = rt_thread_create("protWire", wireless_Task, &dev, 4096, 25, 5);
    if (tidWireless != RT_NULL)
    {
        rt_thread_startup(tidWireless);
    }
    while (1)
#endif
    {
#ifndef UNITEST
        rt_thread_delay( 3000 );
#endif
        if (s_tWirelessCheck.bFaultInitFlag == True)
        {
            Reset4GDeviceHardware();
            InitGprsAndGps();
            rt_memset_s(&s_tWirelessCheck, sizeof(s_tWirelessCheck), 0, sizeof(s_tWirelessCheck));
        }

        CheckGpsModuleStatus();

        if(GetApptestFlag() && s_ucCntApptest < 2)         //APPTEST 模式下，定位模式最多只切换两次
        {
            SwitchLocateMode();
        }

        switch (s_tMc20Manage.ucStage)
        {
            case GPS_STAGE:
                ProcessGps();
                break;
            case SMS_STAGE:
                ProcessSms();
                break;
            default:
                break;
        }
    }
}

BOOLEAN GetMG21State(void)
{
    return s_bMG21State;
}

void getMg21Status(T_Mg21Status *ptStatus)
{
    rt_memcpy_s(ptStatus, sizeof(s_tMg21Status), &s_tMg21Status, sizeof(s_tMg21Status));
    ptStatus->ucMC20Stage = s_tMc20Manage.ucStage;
}

BOOLEAN ApptestGetGprsState(void)
{
    return s_bApptestGprsStat;
}

/***************************************************************************
 * @brief    IMEI码获取接口
 * @param    ptIMEI 用于存储IMEI的结构体指针
 **************************************************************************/
void GetIMEI(T_IMEI *ptIMEI)
{
    T_IMEI tIMEICode;
    rt_memset_s(&tIMEICode, sizeof(T_IMEI), 0, sizeof(T_IMEI));
    rt_memcpy_s(tIMEICode.acIMEI, sizeof(tIMEICode.acIMEI), STR_IMEI_NOT_FOUND, sizeof(STR_IMEI_NOT_FOUND));

    if (s_tIMEI.bIMEIGet == False)
    {
        rt_memcpy_s((BYTE *)ptIMEI, sizeof(T_IMEI), (BYTE *)&tIMEICode, sizeof(T_IMEI));
    }
    else
    {
        rt_memcpy_s((BYTE *)ptIMEI, sizeof(T_IMEI), (BYTE *)&s_tIMEI, sizeof(T_IMEI));
    }

    return;
}

/***************************************************************************
 * @brief    IMSI码获取接口
 * @param    ptIMSI 用于存储IMSI的结构体指针
 **************************************************************************/
void GetIMSI(T_IMSI *ptIMSI)
{
    T_IMSI tIMSICode;
    rt_memset_s(&tIMSICode, sizeof(T_IMSI), 0, sizeof(T_IMSI));
    rt_memcpy_s(tIMSICode.acIMSI, sizeof(tIMSICode.acIMSI), STR_IMSI_NOT_FOUND, sizeof(STR_IMSI_NOT_FOUND));

    if (s_tIMSI.bIMSIGet == False)
    {
        rt_memcpy_s((BYTE *)ptIMSI, sizeof(T_IMSI), (BYTE *)&tIMSICode, sizeof(T_IMSI));
    }
    else
    {
        rt_memcpy_s((BYTE *)ptIMSI, sizeof(T_IMSI), (BYTE *)&s_tIMSI, sizeof(T_IMSI));
    }

    return;
}

/***************************************************************************
 * @brief    GPS模块工作状态检测接口
 * @param    
 **************************************************************************/

Static BOOLEAN CheckGpsModuleStatus(void) // 尝试重启判断
{
    if(s_tGPSFaultStatus.bFaultGpsStatusFlag)
    {
        Reset4GDeviceHardware();
        InitGprsAndGps();
        s_tGPSFaultStatus.ucGetDeviceClassCnt = 0;
        s_tGPSFaultStatus.bFaultGpsStatusFlag = False;
        return True;
    }

    return False;
}



Static BOOLEAN JudgeFaultGpsStatus(BYTE status, BYTE *puFaultType)
{
    if(NULL == puFaultType){return False;}
    if(status == RT_EOK)
    {
        *puFaultType = 0;
        s_tGPSFaultStatus.bFaultGpsStatusFlag = False;
    }
    else
    {
        TimerPlus(*puFaultType, CSQ_GPS_FAULT_COUNTER_MAX);
        if(TimeOut(*puFaultType, CSQ_GPS_FAULT_COUNTER_MAX))
        {   
            s_tGPSFaultStatus.bFaultGpsStatusFlag = True;
        }
    }

    return True;
}



Static BOOLEAN SetGpsFaultStatus(BYTE status)
{
    s_tGPSFaultStatus.bFaultGpsStatusFlag = status;
    return True;
}

BOOLEAN GetGpsModuleStatus(void)
{// GPS故障标志位置位，并且硬件版本号为2或者5，则产生告警
    if(s_tGPSFaultStatus.bFaultGpsStatusFlag && IsSupportsGpsControl())
    {
        return True;
    }
    return False; 
}

/* Started by AICoder, pid:e9da5n78cd51329142a30acc3074081585432d3f */
Static BOOLEAN SaveGpsInfo(void) {
    // 保存经纬度坐标
    s_tGPSPos.fLatitude = s_tGpsData.fLatitude;
    s_tGPSPos.fLongtitude = s_tGpsData.fLongtitude;
    writeGPSLocation(&s_tGPSPos);

    // 保存方向信息
    s_tGpsDirect.ucLatiDirect = s_tGpsData.ucLatiDirect;
    s_tGpsDirect.ucLongDirect = s_tGpsData.ucLongDirect;
    writeGPSDirection(&s_tGpsDirect);

    return TRUE;
}
/* Ended by AICoder, pid:e9da5n78cd51329142a30acc3074081585432d3f */

/* Started by AICoder, pid:qb624l0e04z075114af308a2e074211320b4e043 */
BOOLEAN GetGpsInfo(T_GPSPositionStruct *ptGPSPos, T_GPSDirectionStruct *ptGpsDirec) {
    // 参数有效性检查
    if (!ptGPSPos || !ptGpsDirec) return FALSE;

    // 复制位置数据
    ptGPSPos->fLatitude = s_tGPSPos.fLatitude;
    ptGPSPos->fLongtitude = s_tGPSPos.fLongtitude;

    // 复制方向数据
    ptGpsDirec->ucLatiDirect = s_tGpsDirect.ucLatiDirect;
    ptGpsDirec->ucLongDirect = s_tGpsDirect.ucLongDirect;

    return TRUE;
}
/* Ended by AICoder, pid:qb624l0e04z075114af308a2e074211320b4e043 */

//获取GPS载噪比
INT8S GetGpsSnr(void)
{
    return s_tMg21Status.ucSNR;
}
