#ifndef SOFTWARE_SRC_APP_COMMBDU_H_ 
#define SOFTWARE_SRC_APP_COMMBDU_H_ 
#include "comm.h"
#include "rtthread.h"

#ifdef __cplusplus
extern "C" {
#endif

#include "bdu_test.h"

/***********************  常量定义  ************************/
#define CMD_QUE_NUM        50

#define ACK_WAIT_TIME         10  //5 //延长收包的等待时间
#define GET_BDU_ADJ_VAl_DELAY 241

#define BDU_TASK_PERIOD    50

#define SCI_SEND_MAX       300
#define SCI_ORIGIN_LEN     18

#define SCI_DATA_LEN_CTRL    1
#define SCI_DATA_LEN_SN      4
#define SCI_DATA_LEN_ASSET   30
#define SCI_DATA_LEN_CALI    6
#define SCI_DATA_LEN_MEM_GET 5
#define SCI_DATA_LEN_MEM_SET 9

#define SCI_VER            0x01  // 协议版本
#define SCI_SOI            0x7E  // 帧头
#define SCI_EOI            0x0D  // 帧尾
#define SCI_TRANS_CODE     0x7D  // 变换码

// 各数据段在s_tCommManage中的位置，SendBduCmd函数中，指针p同样适用
//#define SCI_SEG_SOI
#define SCI_SEG_VER        0x00
#define SCI_SEG_REV1       0x01
#define SCI_SEG_LEN        0x05
#define SCI_SEG_FN1        0x07
#define SCI_SEG_FN2        0x09
#define SCI_SEG_REV2       0x0B
#define SCI_SEG_CID        0x0C
#define SCI_SEG_RTN        0x0D
#define SCI_SEG_DATA       0x0E
//#define SCI_SEG_CHK
//#define SCI_SEG_EOI


// SCI 指令集
#define SCI_CID_GET_DATA   0x60  // 获取实时数据
#define SCI_CID_GET_PARA   0x61  // 获取系统参数
#define SCI_CID_SET_PARA   0x62  // 设置系统参数
#define SCI_CID_SET_OPS    0x65  // 设备运行控制
//#define SCI_CID_SET_SN     0x66  // 设置序列号
#define SCI_CID_SET_ASSET  0x67  // 设置资产信息
#define SCI_CID_GET_MENU   0x68  // 获取厂家信息

#define SCI_CID_RECORD_CONFIG  0xA0  // 配置录波参数
#define SCI_CID_RECORD_ORIGIN  0xA1  // 上送录波首发帧
#define SCI_CID_RECORD_DATA    0xA2  // 上送录波数据

#define SCI_CID_SET_OTA    0xF0  // 触发程序更新
#define SCI_CID_GET_CALI   0xF1  // 获取校正信息
#define SCI_CID_SET_CALI   0xF2  // 设置校正信息
#define SCI_CID_GET_MEM    0xF3  // 获取调测信息
#define SCI_CID_SET_MEM    0xF4  // 设置调测信息


// SCI 控制指令集(CID1)
#define SCI_CTRL_CODE       0x24  // 控制命令码
// SCI 控制指令集(CID2)
#define SCI_CTRL_CHG2CHG    0x01  // 转充电使能
#define SCI_CTRL_SLEEP      0x02  // 控制休眠
#define SCI_CTRL_CHG_STG    0x03  // 控制充电直通
#define SCI_CTRL_DISCHG_STG 0x04  // 控制放电直通
#define SCI_CTRL_CHG_PRT    0x05  // 控制充电保护
#define SCI_CTRL_DISCHG_PRT 0x06  // 控制放电保护
#define SCI_CTRL_OPEN_LOOP  0x07  // 开环控制
#define SCI_CTRL_TEST       0x08  // 测试模式
#define SCI_CTRL_HEATER     0x09  // 加热器控制
#define SCI_CTRL_BDULOCK    0x0A  // 闭锁控制
#define SCI_CTRL_CONTACTOR  0x0B  // 接触器控制
#define SCI_CTRL_CHG_BST    0x0C  // 升压充电使能
#define SCI_CTRL_EMC_TEST            0x0D  // EMC测试
#define SCI_CTRL_STOP_CAN_COMM       0x0E  // 停止CAN通信
#define SCI_CTRL_CHG_MANCHIN_TEST    0x0F  // 充放电机测试模式
#define SCI_CTRL_WATER_INGRNESS      0x10  // 进水使能控制


// 测试模式
#define SCI_DATA_LEN_TEST        4

#define SCI_CID_GET_TEST         0x12 // 获取测试数据，仅apptest模式下生效
#define SCI_CID_SET_TEST         0x13 // 设置测试参数，仅apptest模式下生效

#define SCI_CTRL_TEST_RELAY  0xF0     // 继电器、接触器检测
#define CMD_BUS_RELAY_STOP   0        // 停止母排继电器驱动检测命令
#define CMD_BUS_RELAY_START  1        // 启动母排继电器驱动检测命令
#define CMD_BUS_DRIVE1_START 2        // 启动检测母排驱动1命令
#define CMD_BUS_DRIVE2_START 3        // 启动检测母排驱动2命令
#define CMD_CONNECT_BATT_CONTACTOR    0x10       // 闭合电池接触器
#define CMD_DISCONNECT_BATT_CONTACTOR 0x11       // 断开电池接触器

#define SCI_CTRL_TEST_WAVE_PRT   0xF1 // 逐波保护电压测试

#define CMD_BDU_EXIT_SLEEP       0    // 控制BDU侧退出休眠
#define CMD_BDU_ENTER_SLEEP      1    // 控制BDU侧进入休眠


// SCI RTN信息
#define SCI_RTN_SUCCESS    0x00  // 正常
#define SCI_RTN_TLV_ERR    0x01  // TLV标志错
#define SCI_RTN_CID_ERR    0x02  // 命令码错
#define SCI_RTN_CHK_ERR    0x03  // 校验错
#define SCI_RTN_DATA_ERR   0x04  // 数据错
#define SCI_RTN_EXEC_ERR   0x05  // 命令执行失败
#define SCI_RTN_AUTH_ERR   0x06  // 身份识别失败
#define SCI_RTN_NODATA_ERR 0x07  // 无数据（表明无数据响应，与正常响应有区别）
//#define SCI_RTN_XX_ERR    0x08~0x9F  // 其它错误
//#define SCI_RTN_XX_ERR    0xA0~0xFE  // “文件传输协议规范”专用（暂未用）
//#define SCI_RTN_XX_ERR    0xFF       // 未完，待继续分帧传输（暂未用）

#define SCI_COMM_SUCCESS   0x00
#define SCI_COMM_CRC_ERR   0x01
#define SCI_COMM_LEN_ERR   0x02
#define SCI_COMM_RTN_ERR   0x04

// 故障录波
#define SCI_ONE_SECOND       (20)
#define RECORD_SAVE_CYCLE    (SCI_ONE_SECOND * 5)
#define RECORD_DATA_LEN      (RECORD_TOTAL_NUM * RECORD_POINT_MAX)

#define SCI_DATA_LEN_RECORD  (7+RECORD_POINT_MAX)

#define RECORD_CNT_SAVE_CYCLE    (100)
#define RECORD_CNT_SAVE_MAX      (1800)

//硬件版本号对应设备结构体，和s_atHardwareVerAttr对应，中间位置不能删减，只能在预留位新增
typedef struct
{
    uint16_t bPE          : 1; // PE(防盗)
    uint16_t bGyro        : 1; // 陀螺仪
    uint16_t bFE          : 1; // FE(网络)
    uint16_t bDO          : 1; // DO(干接点)
    uint16_t bCAN485      : 1; // 485/CAN
    uint16_t bPWR         : 1; // PWR(激活口)
    uint16_t b4GGPS       : 1; // 4G/GPS
    uint16_t bHeatingFilm : 1; // 加热膜

    uint16_t bCAN2        : 1; // CAN2
    uint16_t bFire        : 1; // 消防
    uint16_t bRsvd        : 6; // 预留位
}T_HardwareVerStruct;

/*********************  数据结构定义 NEW SCI  **********************/
//出厂信息：
typedef struct
{
    uint8_t acVerDate[4];		// 版本日期（yyyy-mm-dd）
    uint8_t acSysName[30];		// 系统名称    (ASCII码，没有使用的填充0x00)
    uint8_t acPlatformVer[8];   // 数控平台版本(ASCII码，没有使用的填充0x00)
    uint8_t acSoftVer[6];		// 软件版本    (ASCII码，没有使用的填充0x00)
    uint8_t acAsset[30];		// 资产管理信息(ASCII码，没有使用的填充0x00)
    uint32_t ulSN;	        	// 序列号
//    uint8_t ucHeatFilm;         // 加热器识别(0:不具备，1:具备)
    uint8_t bRsvd           : 5;        // 预留
    uint8_t bParalleUpdate     : 1;        // 支持并发升级(0:不具备，1:具备)   bit2
    uint8_t bActivatePort   : 1;        // 激活口识别(0:不具备，1:具备)     bit1
    uint8_t bHeatFilm       : 1;        // 加热器识别(0:不具备，1:具备)     bit0
    uint8_t acHardwareVer;      //硬件版本
}T_DCFactory;

//资产管理信息：
typedef struct
{
    uint8_t acAssetInfo[30];	// 资产管理信息(ASCII码，没有使用的填充0x00)
}T_DCAsset;

//SEG1:模拟量数据：
typedef struct
{
    int16_t     sBatCur;	   // 电池电流
    uint16_t    wBatVol;	   // 电池电压
    int16_t     sBusCur;	   // BUS电流
    uint16_t    wBusVol;       // BUS电压
    uint16_t    wBatFusePro;   // 输出熔丝前电压
    int16_t     sEnvTemp;	   // 环境温度
    int16_t     sBoardTemp;    // 单板温度
    int16_t     sConnTemp;     // 连接器温度
    int16_t     sBalanceResisTemp;  // 均衡电阻温度
    int16_t     sActivatePortVol;   // 激活口电压
}T_DCAnalog;

//SEG2:状态量数据：
typedef struct
{
    // BYTE1
    uint16_t bChgPrt		: 1;	    // BDU充电保护状态(功率自判断状态，不体现监控下发状态)
    uint16_t bDischgPrt		: 1;	    // BDU放电保护状态(功率自判断状态，不体现监控下发状态)
    uint16_t bStatus        : 4;	    // 充放电状态，0：常规充电；1：直通充电；2：常规放电；3：直通放电；4：升压充电；6：升压放电；8：降压充电；10：降压放电；12：充放电停止
    uint16_t bLimit 	    : 1;	    // 限流状态
    uint16_t bPowLmtStatus  : 1;        // 输出限功率状态

    // BYTE2
    uint16_t bSleepStatus   : 1;        // 休眠状态
    uint16_t bLock 	        : 1;	    // BDU闭锁状态
    uint16_t bInputBreak 	: 1;	    // 充电输入断状态
    uint16_t bChgStraight         : 1;  // 充电直通状态
    uint16_t bDisChgStraight      : 1;  // 放电直通状态
    uint16_t bExternalPowerSupply : 1;  // 外部初始有电状态
    uint16_t bBoostChgEn    : 1;        // 升压充电使能
    uint16_t bUpgradeEn     : 1;        // 升级使能状态

    // BYTE3
    uint16_t bMOSOffStatus  : 1;        // MOS关闭状态
    uint16_t bExternalPowerOn     : 1;  // 外部有电状态
    uint16_t bTestMode      : 1;        // 测试模式
    uint16_t bOpenloop		: 1;	    // 开环状态
    uint16_t bChange2ChgEn  : 1;        // 转充电允许
    uint16_t bHeaterStatus  : 1;        // 加热器状态
    uint16_t bChgForbid     : 1;        // 充电禁止状态
    uint16_t bDischgForbid  : 1;        // 放电禁止状态

    uint16_t bDiagSts1    : 1;        // 故障诊断状态
    uint16_t bDiagSts2    : 1;        // 故障诊断状态
    uint16_t bDiagSts3    : 1;        // 故障诊断状态
    uint16_t bDiagSts4    : 1;        // 故障诊断状态
    uint16_t bDiagSts5    : 1;        // 故障诊断状态
    uint16_t bDiagSts6    : 1;        // 故障诊断状态
    uint16_t bDiagSts7    : 1;        // 故障诊断状态
    uint16_t bDiagSts8    : 1;        // 故障诊断状态

    uint16_t bDiagSts9    : 1;        // 故障诊断状态
    uint16_t bDiagSts10   : 1;        // 故障诊断状态
    uint16_t bDiagSts11   : 1;        // 故障诊断状态
    uint16_t bDiagSts12   : 1;        // 故障诊断状态
    uint16_t bDiagSts13   : 1;        // 故障诊断状态
    uint16_t bDiagSts14   : 1;        // 故障诊断状态
    uint16_t bDiagSts15   : 1;        // 故障诊断状态
    uint16_t bDiagSts16   : 1;        // 故障诊断状态

    uint16_t bDiagSts17   : 1;        // 故障诊断状态
    uint16_t bDiagSts18   : 1;        // 故障诊断状态
    uint16_t bDiagSts19   : 1;        // 故障诊断状态
    uint16_t bDiagSts20   : 1;        // 故障诊断状态
    uint16_t bDiagSts21   : 1;        // 故障诊断状态
    uint16_t bDiagSts22   : 1;        // 故障诊断状态
    uint16_t bDiagSts23   : 1;        // 故障诊断状态
    uint16_t bDiagSts24   : 1;        // 故障诊断状态

    uint16_t bDiagSts25   : 1;        // 故障诊断状态
    uint16_t bDiagSts26   : 1;        // 故障诊断状态
    uint16_t bDiagSts27   : 1;        // 故障诊断状态
    uint16_t bDiagSts28   : 1;        // 故障诊断状态
    uint16_t bDiagSts29   : 1;        // 故障诊断状态
    uint16_t bDiagSts30   : 1;        // 故障诊断状态
    uint16_t bDiagSts31   : 1;        // 故障诊断状态
    uint16_t bDiagSts32   : 1;        // 故障诊断状态

    // BYTE8
    uint16_t bChargeMachineTest  : 1; // 充放电机测试模式
    uint16_t bContactorBus       : 1; // 母排接触器状态
    uint16_t bEMCtest            : 1; // EMC测试
    uint16_t bContactorBatt      : 1; // 电池接触器状态
    uint16_t bSolarMode          : 1; // 光伏模式
    uint16_t bCanStopped         : 1; // CAN通信停止状态
    uint16_t bWaterIngrnessEn      : 1; // 防水检查使能
    uint16_t bRsvd  : 1;              // 保留位
}T_DCStatusBit;

//SEG3:告警量数据：
typedef struct
{
    // BYTE1
    uint16_t bEEPROM        : 1;        // EEPROM故障
    uint16_t bShortCut 		: 1;	    // 短路保护告警
    uint16_t bBatReverse 	: 1;	    // 反接保护告警
    uint16_t bInsideOverTemp     : 1;   // 机内过温保护
    uint16_t bBoardOverTemp 	 : 1;	// 单板过温保护
    uint16_t bLockErr       : 1;	    // 闭锁状态
    uint16_t bChgLoopFail 	: 1;	    // 充电回路失效
    uint16_t bDischLoopFail : 1;    	// 放电回路失效
    
    // BYTE2    
    uint16_t bLimLoopFail 	: 1;	    // 限流回路失效
    uint16_t bChgOverCur 	: 1;	    // 充电过流保护
    uint16_t bDischOverCur 	: 1;	    // 放电过流保护
    uint16_t bBatOverVol 	: 1;	    // 电池组过压保护
    uint16_t bBatUnderVol 	: 1;	    // 电池组欠压保护
    uint16_t bBusOverVol 	: 1;	    // 母排过压保护
    uint16_t bBusUnderVol 	: 1;	    // 母排欠压保护
    uint16_t bChgBatUnderVol     : 1;   // BDU电池充电欠压保护

    // BYTE3
    uint16_t bWavePrt       : 1;        // 逐波保护
    uint16_t bMainRelayFail : 1;        // 主继电器失效
    uint16_t bDCDCErr       : 1;        // DC/DC故障
    uint16_t bSampleErr     : 1;        // 采集异常
    uint16_t bAuxiSourceErr : 1;        // 辅助源故障
    uint16_t bHeaterErr     : 1;        // 加热故障
    uint16_t bConnTempHighPrt    : 1;   // 连接器高温保护
    uint16_t bBalanceResisTempHighPrt  : 1;  // 均衡电阻高温保护

    // BYTE4
    uint16_t bActivatePortCurrError   : 1;        // 激活回路电流异常
    uint16_t bActivatePortReverseAlm  : 1;        // 激活口反接
    uint16_t bRsvd                    : 6;        // 预留
}T_DCAlarmBit;

typedef struct
{
    T_DCAnalog    tDCAnalag;          // 模拟量数据
    T_DCStatusBit tDCStatus;          // 状态量数据
    T_DCAlarmBit  tDCAlarm;           // 告警量数据
    uint16_t      wSystemParaCrc;     // 系统参数校验值
    uint16_t      wRecordParaCrc;     // 录波参数校验值
}T_DCRealData;

typedef struct
{
    BYTE ucCurrBalanceAmplitude;
    BYTE ucCurrBalanceSlope;
    WORD wBattSOC;
    WORD wBattCap;
    WORD wCurrBalanceMagnif;
    BOOLEAN bCurrBalanceEn;
    BOOLEAN bBroadcastEn;
    BOOLEAN bB3CurrBalance;
    BYTE ucBattSOH;
} T_CurrBalanceInfo;

typedef struct
{
    int16_t sWavePointChg;          // 充电逐波点，精度2
    int16_t sWavePointDischg;       // 放电逐波点，精度2
    uint16_t wCrc;                  // 校验值
}T_DCTestPara;


//控制命令：
typedef struct
{
    uint8_t ucChange2Chg;   // 转充电使能
    uint8_t ucSleep;	    // 控制休眠
    uint8_t ucChgStraight;	// 控制充电直通
    uint8_t ucDisChgStraight;  // 控制放电直通
    uint8_t ucChgPrt;	    // 控制充电保护
    uint8_t ucDischgPrt;	// 控制放电保护
    uint8_t ucOpenloop;	    // 开环控制
    uint8_t ucApptest;	    // 测试模式
    uint8_t ucHeater;       // 加热器控制
    uint8_t ucLockCtrl;	    // 闭锁控制
    uint8_t ucBoostChgEn;   // 升压充电使能
    uint8_t ucRelayTest;    // 继电器、接触器检测
    uint8_t ucWavePrtTest;  // 逐波保护电压测试
    uint8_t ucContactor;    // 接触器控制
    uint8_t ucChargeTestMode; //充放电机测试模式
    uint8_t ucEMCTest;        //EMC测试
    uint8_t ucStopCan;        //停止CAN通信
    uint8_t ucWaterIngrnessEn;      //进水使能控制
}T_DCCtrl;



//设置校正参数：
typedef struct
{
    uint16_t wAdjChannel;  // 校正通道(比例校正通道值)
    uint16_t wAdjParaVal;  // 校正值  (比例校正值)
    uint16_t wZeroAdj;     // uwZeroAdj = 1, 启动零点校正； uwZeroAdj = 0，不启动零点校正； //////// uwZeroAdj = 0x6666 存储比例校正值到EEPROM；  uwZeroAdj = 0xAAAA，校正参数写默认值
}T_DCCaliSet;


// 调测信息
typedef struct
{
    uint8_t  ucDataWidth;   // 数据类型，1:设置的内存数据是32位数，0: 设置的内存数据是16位数
    uint32_t ulMemAddr;     // 要设置的内存地址
    uint32_t ulMemValue;    // 要设置内存地址的设置值
}T_DCMemQuery;


typedef struct
{
    WORD   wRecordIntrvalFront;
    WORD   wRecordIntrvalAfter;
    BYTE   ucRecordNumFront;
    BYTE   ucRecordNumAfter;
    BYTE   ucRecordPointNum;
    BYTE   aucRecordPointID[RECORD_POINT_MAX];
}T_BduRecordCfg;

typedef struct
{
    T_BduRecordCfg tRecordCfg;
    WORD wCrc;
}T_BduRecordPara;

typedef struct
{
    time_t         tTimeNow;
    BYTE           ucTrigger;
    T_BduRecordCfg tRecordCfg;
    WORD           awData[RECORD_DATA_LEN];
}T_BduRecordData;

////////////////////////////////////////////

typedef enum
{
    BDU_CMD_STAGE_IDLE,
    BDU_CMD_STAGE_WAIT,
    BDU_UPGRADE,
}Enum_BDUCMD;

typedef enum
{
    BDU_TYPE_COMMON_LI = 0,
    BDU_TYPE_SMART_LI,
    BDU_TYPE_INVALID,
}Enum_BDUTYPE;

typedef enum
{
    BDU_CTRL_CMD_NULL = 0,          ////无控制
    BDU_CTRL_CMD_RESET = 1,         ////重启
    BDU_CTRL_CMD_SHUTDOWN = 2,      ////关机
    BDU_CTRL_CMD_CELL_PROTECT = 3,  ////单体保护
    BDU_CTRL_CMD_ILLEGAL,           ////非法命令
}BDU_CTRL_CMD;

typedef struct
{
    BYTE ucCmd;
    WORD wTemp;
    WORD wSendLenid;
    BYTE ucCommandType;
}T_BduCmdStruct;

typedef struct
{
    Enum_BDUCMD ucCommStage;
    BYTE ucGetFacInfoCnt;
    WORD wCommFailCnt;
    BOOLEAN bAck;
    BOOLEAN bCommFail;
    BYTE aucBuf[LEN_COMM_REC_BUF];
    T_BduCmdStruct  tCurrCmd;
}T_CommManageStruct;

typedef struct
{
    T_BduCmdStruct atBduCmd[CMD_QUE_NUM];
    BYTE ulHead;
    BYTE ulTail;
}T_BduCmdQueueStruct;


/*********************  函数原型定义  **********************/
void DealBduComm( void* parameter );

BOOLEAN IsBduUpdate(void);
BOOLEAN IsBduCommFail(void);

void SetBduUpdate(BOOLEAN bUpdate);

uint8_t ConvertBduStatus(uint8_t ucBduStatus);

BOOLEAN GetBduReal(T_DCRealData *pRealData);  // 获取实时数据接口
BOOLEAN SetBduPara(T_DCPara *pPara);          // 设置参数接口
BOOLEAN GetBduPara(T_DCPara *pPara);          // 获取参数接口
BOOLEAN BduCtrl(BYTE ucCmd, BYTE ucCtrlCode); // 控制命令接口: ucCmd 指令码; ucCtrlCode 指令内容
BOOLEAN GetBduCtrl(T_DCCtrl *pCtrl);          // 获取控制指令接口
BOOLEAN GetBduFact(T_DCFactory *pFact);  // 获取出厂信息接口
BOOLEAN SetBduAsset(T_DCAsset *pAsset);  // 设置资产信息接口
void BduAdj( BYTE ucCode, WORD wVal );   // 设置校正参数
BOOLEAN GetBduAdj(T_DCCaliGet *pAdj);    // 获取校正参数接口

BOOLEAN SetBduMem(T_DCMemQuery *pMem);          // 设置调测信息
BOOLEAN NotifyBduMemQuery(T_DCMemQuery *pMem);  // 通知调测信息查询
BOOLEAN GetBduMem(T_DCMemQuery *pMem);          // 获取调测信息接口，注意：获取前必须先通知查询
BOOLEAN IsHardwareSupportGyro(void);            // 获取硬件版本是否支持陀螺仪
BOOLEAN IsHardwareSupportHeater(void);          // 获取硬件版本是否支持加热膜
BOOLEAN IsSupportsAntiTheftLine(void);          // 获取硬件版本是否支持防盗线
BOOLEAN IsSupportsFireControl(void);            // 获取硬件版本是否支持消防
BOOLEAN IsSupportsGpsControl(void);             // 获取硬件版本是否支持GPS
FLOAT    GetAveBusVolt(void);
uint32_t GetBduDiagStatus(void);

BOOLEAN GetHeaterStatus(void);

BOOLEAN GetBduTestData(T_DCTestData *pTestData);
BOOLEAN SetBduTestPara(T_DCTestPara *pPara);
BOOLEAN GetBduTestPara(T_DCTestPara *pPara);
BOOLEAN IsBduP2PUpdate(void);
void SetBduParalleUpdate(BOOLEAN bUpdate);

// BOOLEAN SetRecordPara(T_BduRecordPara *pPara);
// BOOLEAN GetRecordPara(T_BduRecordPara *pPara);
#ifdef UNITEST
    BOOLEAN DealSetCmdType(BYTE *p, WORD *pwPayLoadLen, BYTE i);
    BOOLEAN DealGetCmdType(BYTE *p, WORD *pwPayLoadLen, BYTE i);
#endif


#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif
#endif  //SOFTWARE_SRC_APP_COMMBDU_H_;
