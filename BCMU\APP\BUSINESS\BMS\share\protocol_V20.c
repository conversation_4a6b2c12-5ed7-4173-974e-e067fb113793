/**************************************************************************
* Copyright (C) 2001, ZTE Corporation.
* 版权信息：（C）2010-2021，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：ZXDU18 CTL板软件
* 文件名称：protocol_v20.c
* 文件说明：协议模块
* 作    者：hanhui
* 版本信息：V1.0
* 设计日期：2021-10-22
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/

#include "common.h"
#include "sample.h"
#include "hisdata.h"
#include "realAlarm.h"
#include "battery.h"
#include "para.h"
#include "comm.h"
#include "protocol.h"
#include "CommCan.h"
#include "Candrive.h"
#include "led.h"
#include "apptest.h"
#include "flash.h"
#include "fileSys.h"
#include "realAlarm.h"
#include "qtp.h"
#include "protocol_V20.h"
#include "commBdu.h"
#include "HeaterControl.h"
#include "protocolTower_V20.h"
#include "pdt_version.h"
#include <sys/time.h>
#include "utils_rtthread_security_func.h"

static T_SysPara s_tSysPara;
static BOOLEAN s_bTowerSleepStatus;
rt_timer_t s_waitReacAndResetTimer = RT_NULL;
rt_timer_t s_waitReacAndShutTimer = RT_NULL;
//rt_timer_t timer_t; // 单次定时器
static void SendAnalogData_V20( BYTE ucPort );
static void SendDigitalData_V20( BYTE ucPort );
static void RecCtrlData_V20( BYTE ucPort );
static void SendParaData_V20( BYTE ucPort );
static void RecParaData_V20( BYTE ucPort );
static void SendProtocolVer_v20( BYTE ucPort );
static void SendFactoryInfo_v20( BYTE ucPort );
static void SendHistoryData_V20( BYTE ucPort );
static void SendSysTime_v20( BYTE ucPort );
static void RecSysTime_v20( BYTE ucPort );
static BYTE SetAnalogAlarmValue(BOOLEAN highAlarmCondition, BOOLEAN lowAlarmCodition, BOOLEAN noAlarmCoditon);
static BOOLEAN IsCellFault(BYTE arr[], BYTE length);

static WORD WordChange2Modbus(WORD *wTemp);
static void ShutDownSystem(void);
static void RestoryFactory(void);
static void RestartSystem(void);
static void SetBmsVolPara(FLOAT fPara);
static void SetBmsBatTempPara(FLOAT fPara);
static void SetBmsEnvTempPara(FLOAT fPara);
static void SetBmsOtherPara(FLOAT fPara);

// static void MemsetBuff(void *des, void *src, BYTE srcLen, BYTE defalutLen, BYTE defalutValue);
// static void ParaStrCpy(BYTE* pcDst, BYTE* pcSrc, size_t size);

const T_CmdFuncStruct   s_atCID2AllTowerTable[] =
{
    {GET_ANA_DATA_V20,          SendAnalogData_V20,     0},     // 0
    {GET_DIG_DATA_V20,          SendDigitalData_V20,    0},     // 1
    {SET_CTRL_DATA_V20,         RecCtrlData_V20,        4},     // 2
    {GET_PARA_DATA_V20,         SendParaData_V20,       0},     // 3
    {SET_PARA_DATA_V20,         RecParaData_V20,        6},     // 4
    {GET_PROTOCOL_VER_V20,      SendProtocolVer_v20,    0},     // 5
    {GET_FACTORY_INFO_V20,      SendFactoryInfo_v20,    0},     // 6
    {GET_HISTORY_DATA_V20,      SendHistoryData_V20,    2},     // 7
    {GET_TIME_V20, 				SendSysTime_v20,        0},     // 8
    {SET_TIME_V20,              RecSysTime_v20,         14},    // 9

    {0x00, 0x0000, 0x00},
};

/****************************************************************************
* 函数名称：SendAnalogData_V20
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：获取遥测量信息
* 作    者  ：Chang Qing
* 版本信息：V1.0
* 设计日期：2021-10-23
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/

static void SendAnalogData_V20(BYTE ucPort)
{
    WORD wSendLen = 130; // (1+1+1+2*16+1+2*4+2+2+2+2+2+2+2+1+2+2+2)*2 = 65*2 = 130
    WORD wBattSoc;
    BYTE *p = NULL;
    T_BCMDataStruct tBCMAnaData = {0};
    BYTE i = 0;
    BYTE ucRandNum = 0;
    FLOAT fTowerVolt = 0;   // 铁塔总压
    FLOAT fTowerCurr = 0;   // 铁塔电流
#ifdef DEVICE_USING_R321
    FLOAT fAvgBattVolt = 0; // 前15节电池的平均电压
#endif

    p = s_tProtocol.aucSendBuf;
    rt_memset_s(&tBCMAnaData, sizeof(tBCMAnaData), 0, sizeof(tBCMAnaData));
    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));
    rt_memset_s(&s_tSysPara, sizeof(T_SysPara), 0, sizeof(T_SysPara));

    GetRealData(&tBCMAnaData);
    GetSysPara(&s_tSysPara);

    /* DATA_FLAG */
    *p++ = GetDataFlag(BCM_ALARM, ucPort);

    *p++ = PACKLOCATION;      // 上位机需要获取的pack组位置，待填充

    *p++ = CELL_VOL_NUM + 1;  // 单体电池数量

    for (i = 0; i < CELL_VOL_NUM_TOWER; i++)
    {
        *(SHORT*)p = FloatChangeToModbus(tBCMAnaData.afCellVolt[i] * 1000);    // 单体电压
        p += 2;
    }

#ifdef DEVICE_USING_R321
    for (i = CELL_VOL_NUM_TOWER; i < CELL_VOL_NUM; i++)
    {
        *(SHORT*)p = FloatChangeToModbus(tBCMAnaData.afCellVolt[i] * 1000);    // 单体电压
        p += 2;
    }
    for (i = 0; i < CELL_VOL_NUM; i++)
    {
        fAvgBattVolt += tBCMAnaData.afCellVolt[i];
    }
    /////为了解决上海铁塔客户要求上送15串电芯电压以及江西铁塔要求16串电芯电压综合要求（铁塔历史数据类型为0时16串，为1时15串）
    if(s_tSysPara.ucHisDataType == 0)
    {
        *(SHORT*)p = FloatChangeToModbus(fAvgBattVolt * 1000 / CELL_VOL_NUM); // 单体电压16是前15节单体电压的均值
        p += 2;
        fTowerCurr = tBCMAnaData.fBusCurr;     // 江西铁塔电池电流：以母排电流上送
        fTowerVolt = tBCMAnaData.fExterVolt;   // 江西铁塔电池电压：以母排电压上送
    }
    else
    {
        *p++ = 0;
        *p++ = 0;
        fTowerCurr = tBCMAnaData.fBattCurr;    // 上海铁塔电池电流：以电池电流上送
        fTowerVolt = tBCMAnaData.fBattVolt;    // 上海铁塔电池电压：以电池电压上送
    }
#else
    for(i = 0; i < CELL_VOL_NUM_TOWER; i++)
	{
		*(SHORT*)p = FloatChangeToModbus(tBCMAnaData.afCellVolt[i]*1000);    //单体电压
        p += 2; // 9- 16节重复
	}
    fTowerCurr = tBCMAnaData.fBusCurr;         // D121无铁塔要求，默认以母排电流上送
    fTowerVolt = tBCMAnaData.fExterVolt;       // D121无铁塔要求，默认以母排电压上送
#endif

    *p++ = CELL_TEMP_NUM;   // 电芯温度数量

    for (i = 0; i < CELL_TEMP_NUM; i++)
    {
        ucRandNum = rt_rand_s() % 100;
        if(ucRandNum < 80)
        {
            // 80%的几率不变
        }
        else if(ucRandNum < 90)
        {
            tBCMAnaData.afCellTemp[i] += 0.1f;
        }
        else
        {
            tBCMAnaData.afCellTemp[i] -= 0.1f;
        }
        *(SHORT*)p = FloatChangeToModbus(tBCMAnaData.afCellTemp[i] * 10 + 2731);  // 电芯温度数据，待填充
        p += 2;
    }

    *(SHORT*)p = FloatChangeToModbus(tBCMAnaData.fEnvTemp * 10 + 2731);    // 环境温度
    p += 2;

    *(SHORT*)p = FloatChangeToModbus(tBCMAnaData.fBoardTemp * 10 + 2731);    // MOS温度数据，待填充
    p += 2;

    *(SHORT*)p = FloatChangeToModbus(fTowerCurr * 100);
    p += 2;

    *(SHORT*)p = FloatChangeToModbus(fTowerVolt * 100);
    p += 2;

    wBattSoc = GetRealBattCap();
    *(SHORT*)p = Host2Modbus((SHORT*)&wBattSoc);    // 电池剩余容量，待填充
    p += 2;

    *(SHORT*)p = FloatChangeToModbus(s_tSysPara.wBatteryCap * 100);  // 电池总容量，待填充
    p += 2;

    *(SHORT*)p = Host2Modbus((SHORT*)&tBCMAnaData.wBattCycleTimes);    // 电池循环次数
    p += 2;

    *p++ = 3;    // 自定义遥测数量

    *(SHORT*)p = Host2Modbus((SHORT*)&tBCMAnaData.wBattSOC);     // 电池组荷电状态（SOC）
    p += 2;

    *(SHORT*)p = Host2Modbus((SHORT*)&tBCMAnaData.wBattSOH);     // 电池组健康状态（SOH）
    p += 2;

    *p++ = 0;    // 预留今后扩展
    *p++ = 0;    // 预留今后扩展

    s_tProtocol.wSendLenid = wSendLen;
}


/****************************************************************************
* 函数名称：SendDigitalData_V20
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：获取遥信量信息
* 作    者  ：Chang Qing
* 版本信息：V1.0
* 设计日期：2021-10-23
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
static void SendDigitalData_V20( BYTE ucPort )     
{
    WORD wSendLen = 84;//(1+1+1+1*16+1+1*4+1*7+2+1+1+1+1+4+1)*2 = 42*2 = 84
    BYTE *p = NULL;
    BYTE i = 0;
    T_BCMAlarmStruct tBCMAlm = {0,};
    T_BCMDataStruct tBCMData = {0,};
    T_BattResult tBattResult = {0,};
    rt_memset_s(&s_tSysPara, sizeof(T_SysPara), 0, sizeof(T_SysPara));
    GetRealAlarm(BCM_ALARM, (BYTE *)&tBCMAlm);
    GetRealData(&tBCMData);
    GetBattResult(&tBattResult);
    GetSysPara(&s_tSysPara);

    p  = s_tProtocol.aucSendBuf;
    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));


    /* DATA_FLAG */
    *p++ = GetDataFlag( BCM_ALARM, ucPort );	

    *p++ = PACKLOCATION;      //上位机需要获取的pack组位置，待填充

    *p++ =  CELL_VOL_NUM + 1;      //单体电池数量

    for (i = 0; i < CELL_VOL_NUM_TOWER; i++)
    {     
        //单体电压告警状态
        *p++ = SetAnalogAlarmValue(FAULT == tBCMAlm.aucCellOverVoltAlm[i],
                                     FAULT == tBCMAlm.aucCellUnderVoltAlm[i],
                                     True);
    }
#ifdef DEVICE_USING_R321
    for(i = CELL_VOL_NUM_TOWER; i<CELL_VOL_NUM + 1; i++)
	{
	    *p++ = SetAnalogAlarmValue(FAULT == tBCMAlm.aucCellOverVoltAlm[i],
									 FAULT == tBCMAlm.aucCellUnderVoltAlm[i],
									 True);
	}
#else
    for(i = CELL_VOL_NUM_TOWER; i<CELL_VOL_NUM + 1; i++)
	{
		*p++ = 0;
	}
#endif


    *p++ =  CELL_TEMP_NUM;   //温度数量

    for (i = 0; i < CELL_TEMP_NUM; i++)
    {
        //电芯温度告警状态
        *p++ = SetAnalogAlarmValue(FAULT == tBCMAlm.aucChgTempHighAlm[i] || FAULT == tBCMAlm.aucDischgTempHighAlm[i],
                                     FAULT == tBCMAlm.aucChgTempLowAlm[i] || FAULT == tBCMAlm.aucDischgTempLowAlm[i],
                                     True);
    }
    //环境温度告警状态
    *p++ = SetAnalogAlarmValue(FAULT == tBCMAlm.ucEnvTempHighAlm,
                                 FAULT == tBCMAlm.ucEnvTempLowAlm,
                                 True);
    //MOS温度告警状态
    *p++ = SetAnalogAlarmValue(FAULT == tBCMAlm.ucBoardTempHighAlm,
                                 False,
                                 True);
    //充放电流告警状态
    *p++ = SetAnalogAlarmValue(FAULT == tBCMAlm.ucChgCurrHighAlm || FAULT == tBCMAlm.ucDischgCurrHighAlm,
                                 False,
                                 True);
    //电压总压告警状态
    *p++ = SetAnalogAlarmValue(FAULT == tBCMAlm.ucBattOverVoltAlm,
                                 FAULT == tBCMAlm.ucBattUnderVoltAlm,
                                 True);

    *p++ = 0x09;      //自定义告警数量，待填充

    //均衡事件代码
    SetDigitalAlarmBit(0, p, 1 == tBCMData.ucCellEquSta);
    SetDigitalAlarmBit(4, p, FAULT == tBCMAlm.ucCellPoorConsisAlm);
    SetDigitalAlarmBit(5, p, FAULT == tBCMAlm.ucChgLoopInvalid);
    SetDigitalAlarmBit(6, p, FAULT == tBCMAlm.ucDischgLoopInvalid);
    p += 1;

    //电压事件代码
    SetDigitalAlarmBit(0, p, IsCellFault(tBCMAlm.aucCellOverVoltAlm, CELL_VOL_NUM));
    SetDigitalAlarmBit(1, p, IsCellFault(tBCMAlm.aucCellOverVoltPrt, CELL_VOL_NUM));
    SetDigitalAlarmBit(2, p, IsCellFault(tBCMAlm.aucCellUnderVoltAlm, CELL_VOL_NUM));
    SetDigitalAlarmBit(3, p, IsCellFault(tBCMAlm.aucCellUnderVoltPrt, CELL_VOL_NUM));
    SetDigitalAlarmBit(4, p, FAULT == tBCMAlm.ucBattOverVoltAlm);
    SetDigitalAlarmBit(5, p, FAULT == tBCMAlm.ucBattOverVoltPrt);
    SetDigitalAlarmBit(6, p, FAULT == tBCMAlm.ucBattUnderVoltAlm);
    SetDigitalAlarmBit(7, p, FAULT == tBCMAlm.ucBattUnderVoltPrt);
    p += 1;

    //温度事件代码（按照已有协议，高字节在前）
    SetDigitalAlarmBit(0, p, FAULT == tBCMAlm.ucEnvTempHighAlm);
    SetDigitalAlarmBit(1, p, FAULT == tBCMAlm.ucEnvTempHighPrt);
    SetDigitalAlarmBit(2, p, FAULT == tBCMAlm.ucEnvTempLowAlm);
    SetDigitalAlarmBit(3, p, FAULT == tBCMAlm.ucEnvTempLowPrt);
    SetDigitalAlarmBit(4, p, FAULT == tBCMAlm.ucInsideTempHighPrt);
    p += 1;
    SetDigitalAlarmBit(0, p, IsCellFault(tBCMAlm.aucChgTempHighAlm, CELL_TEMP_NUM));
    SetDigitalAlarmBit(1, p, IsCellFault(tBCMAlm.aucChgTempHighPrt, CELL_TEMP_NUM));
    SetDigitalAlarmBit(2, p, IsCellFault(tBCMAlm.aucChgTempLowAlm, CELL_TEMP_NUM));
    SetDigitalAlarmBit(3, p, IsCellFault(tBCMAlm.aucChgTempLowPrt, CELL_TEMP_NUM));
    SetDigitalAlarmBit(4, p, IsCellFault(tBCMAlm.aucDischgTempHighAlm, CELL_TEMP_NUM));
    SetDigitalAlarmBit(5, p, IsCellFault(tBCMAlm.aucDischgTempHighPrt, CELL_TEMP_NUM));
    SetDigitalAlarmBit(6, p, IsCellFault(tBCMAlm.aucDischgTempLowAlm, CELL_TEMP_NUM));
    SetDigitalAlarmBit(7, p, IsCellFault(tBCMAlm.aucDischgTempLowPrt, CELL_TEMP_NUM));
    p += 1;

    //电流事件代码
    SetDigitalAlarmBit(0, p, FAULT == tBCMAlm.ucChgCurrHighAlm);
    SetDigitalAlarmBit(1, p, FAULT == tBCMAlm.ucChgCurrHighPrt);
    SetDigitalAlarmBit(2, p, FAULT == tBCMAlm.ucDischgCurrHighAlm);
    SetDigitalAlarmBit(3, p, FAULT == tBCMAlm.ucDischgCurrHighPrt);
    SetDigitalAlarmBit(5, p, FAULT == tBCMAlm.ucBattShortCut);
    p += 1;

    //荷电状态SOC告警
    SetDigitalAlarmBit(0, p, FAULT == tBCMAlm.ucBattSOCLowPrt);
    p += 1;

    //FET状态代码
    *p = tBattResult.ucBattCtrlStat;
//    SetDigitalAlarmBit(0, p, NORMAL == tBCMAlm.ucDischgCurrHighPrt && 
//                       (BDU_STATUS_COMMON_DISCHARGE == tBCMData.ucBduStatus || 
//                        BDU_STATUS_BOOST_DISCHARGE == tBCMData.ucBduStatus || 
//                        BDU_STATUS_BUCK_DISCHARGE == tBCMData.ucBduStatus ||
//                        BDU_STATUS_STRAIGHT_DISCHARGE == tBCMData.ucBduStatus)
//                );tBattResult.ucBattCtrlStat
//    SetDigitalAlarmBit(1, p, NORMAL == tBCMAlm.ucChgCurrHighPrt && 
//                       (BDU_STATUS_COMMON_CHARGE == tBCMData.ucBduStatus || 
//                        BDU_STATUS_BOOST_CHARGE == tBCMData.ucBduStatus ||
//                        BDU_STATUS_BUCK_CHARGE == tBCMData.ucBduStatus ||
//                        BDU_STATUS_STRAIGHT_CHARGE == tBCMData.ucBduStatus)
//                );
//    SetDigitalAlarmBit(2, p, 1 == tBCMData.ucLimit);
    SetDigitalAlarmBit(3, p, 1 == GetHeaterStatus());
    p += 1;

    //系统状态代码
    SetDigitalAlarmBit(0, p, 1 == tBCMData.ucBattPackSta);
    SetDigitalAlarmBit(1, p, 0 == tBCMData.ucBattPackSta);
    SetDigitalAlarmBit(3, p, 2 == tBCMData.ucBattPackSta);
    p += 1;

    //均衡状态代码（按照已有协议，高字节在前）
    *p++ = (BYTE)(0x00);
    *p++ = (BYTE)(0x00);
    *p++ = (BYTE)((tBattResult.wCellBalVoltBits & 0xFF00) >> 8);
    *p++ = (BYTE)(tBattResult.wCellBalVoltBits & 0x00FF);

    *p++ = 0x00;      //预留今后扩展

    s_tProtocol.wSendLenid = wSendLen;
    return;
}

static void SendParaData_V20( BYTE ucPort )
{
    SendParaDataTower_V20(&s_tSysPara);
}


/****************************************************************************
* 函数名称：RecCtrlData_V20
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：遥控命令
* 作    者  ：Chang Qing
* 版本信息：V1.0
* 设计日期：2021-10-23
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
static void RecCtrlData_V20( BYTE ucPort )           
{
    BYTE ucCommandID = s_tProtocol.aucRecBuf[7];
    BYTE ucCtrlCode = 0;
    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[8];

    rt_memset_s(&s_tSysPara, sizeof(T_SysPara), 0, sizeof(T_SysPara));
    GetSysPara(&s_tSysPara);

    s_tProtocol.ucRTN	=  RTN_CORRECT;

    switch(ucCommandID)
    {
        case DISCHG_CONTROL:
            if (s_tProtocol.ucCommandType == 0x1F)
            {
                ucCtrlCode = CTRL_BATTERY_DISCHARGE_STOP;
            }
            else if (s_tProtocol.ucCommandType == 0x0F)
            {
                ucCtrlCode = CTRL_BATTERY_DISCHARGE_START;                
            }
            s_tProtocol.ucRTN = CtrlBattery(ucCtrlCode);
            break;

        case CHARGE_CONTROL:
            if (s_tProtocol.ucCommandType == 0x1F)
            {
                ucCtrlCode = CTRL_BATTERY_CHARGE_STOP;
            }
            else if (s_tProtocol.ucCommandType == 0x0F)
            {
                ucCtrlCode = CTRL_BATTERY_CHARGE_START;                
            }
            s_tProtocol.ucRTN = CtrlBattery(ucCtrlCode);
            break;

        case CURR_LIMIT_CONTROL:
            if(0x1F == s_tProtocol.ucCommandType)
            {
                s_tProtocol.ucRTN = RTN_FAIL_COMMAND;
            }
            break;

        case HEAT_CONTROL:
            if(0x0F == s_tProtocol.ucCommandType)
            {
                SetHeaterTestCtrl(HEATER_TEST_ON);
            }
            else if(0x1F == s_tProtocol.ucCommandType)
            {
                SetHeaterTestCtrl(HEATER_TEST_OFF);
            }
            else
            {
                s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
            }
            break;

        case SYSTEM_SHUTDOWN:
            ShutDownSystem();
            break;

        case RESTORE_FACTORY:
            RestoryFactory();
            break;

        case SYSTEM_RESTART:
            RestartSystem();
            break;
        
        default:
            s_tProtocol.ucRTN	=  RTN_WRONG_COMMAND;
    }

    return;
}

static void RemoteCtrlReset(void *para)
{
    ResetMCU(NO_RESET_REMOTE_CTRL);
}

static void ShutDownSystem(void)
{
    if(0x0F == s_tProtocol.ucCommandType)
    {
        if(!IsSleep())
        {
            SetTowerSleepStatus(True);
        }
    }
    else if(0x1F == s_tProtocol.ucCommandType)
    {
        if(IsSleep())
        {
            SetTowerSleepStatus(False);
            if (RT_NULL == s_waitReacAndShutTimer)
            {
                s_waitReacAndShutTimer = rt_timer_create("shutdown_wait_protocol_react", RemoteCtrlReset, NULL, 1000, RT_TIMER_FLAG_ONE_SHOT | RT_TIMER_FLAG_SOFT_TIMER);
            }

            if(s_waitReacAndShutTimer != RT_NULL)
            {
                rt_timer_start(s_waitReacAndShutTimer);
            }
        }
    }
    else
    {
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
    }
    return;
}

static void RestoryFactory(void)
{
    if(0x0f == s_tProtocol.ucCommandType)
    {
        LoadPara();
        SaveAction(GetActionId(CONTOL_RST_PARA), "TowerDefault Para");
        if(IsSleep())
        {
            if (RT_NULL == s_waitReacAndResetTimer)
            {
                s_waitReacAndResetTimer = rt_timer_create("reset_wait_protocol_react", RemoteCtrlReset,NULL, 1000, RT_TIMER_FLAG_ONE_SHOT | RT_TIMER_FLAG_SOFT_TIMER);
            }

            if(s_waitReacAndResetTimer != RT_NULL)
            {
                rt_timer_start(s_waitReacAndResetTimer);
            }
        }
        else
        {
            CtrlBattery(CTRL_BATTERY_DISCHARGE_START);
            CtrlBattery(CTRL_BATTERY_CHARGE_START);
        }
    }
    return;
}

static void RestartSystem(void)
{
    if(0x0F == s_tProtocol.ucCommandType)
    {
        if (RT_NULL == s_waitReacAndResetTimer)
        {
            s_waitReacAndResetTimer = rt_timer_create("reset_wait_protocol_react",RemoteCtrlReset,NULL,1000,RT_TIMER_FLAG_ONE_SHOT | RT_TIMER_FLAG_SOFT_TIMER);
        }

        if(s_waitReacAndResetTimer != RT_NULL)
        {
            rt_timer_start(s_waitReacAndResetTimer);
        }
    }
    return;
}

void SetInvalidFlag(ULONG ulOffset, BYTE ucLen)
{
    BYTE i=0;
    ULONG ulIndex = 0;

    for (i=0; i<ucLen; i++)
    {
        ulIndex = ulOffset + i;
        if (ulIndex < HIGH_SEND_LEN)
        {
            s_tProtocol.aucInvalidFlag[ulIndex] = 1;
        }
    }
    return;
}


/****************************************************************************
* 函数名称：RecParaData_V20
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：设定遥调量信息
* 作    者  ：Chang Qing
* 版本信息：V1.0
* 设计日期：2021-10-23
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
static void RecParaData_V20( BYTE ucPort )
{
    BYTE i = 0;
    WORD wPara;
    FLOAT fPara;
    const BYTE aucJudgeType[] = {0x09,0x0C,0x0F,0x13,0x15,0x31,0x32,0x35,
                                0x37,0x38,0x39,0x3A,0x3B,0x3C,0x3D,0x42,0x43,0x44,
                                0x45,0x46,0x47,0x49,0x00,0x00};

    rt_memset_s(&s_tSysPara, sizeof(T_SysPara), 0, sizeof(T_SysPara));
    GetSysPara(&s_tSysPara);

    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];
    fPara = (FLOAT)(Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+8)));
    wPara = (WORD)(WordChange2Modbus((WORD*)(s_tProtocol.aucRecBuf+8)));

    while(aucJudgeType[i] != 0x00)
    {
        if(aucJudgeType[i] == s_tProtocol.ucCommandType)
        {
            s_tProtocol.ucRTN  = RTN_INVALID_DATA;
            return;
        }
        i++;
    }

    if(s_tProtocol.ucCommandType == 0x01 && wPara > 255)
    {
        s_tProtocol.ucRTN  = RTN_INVALID_DATA;
        return;
    }

    if(s_tProtocol.ucCommandType == 0x50 && s_tProtocol.aucRecBuf[6] != ((sizeof(PRTBOARDTYPE)+1)*2))
    {
        s_tProtocol.ucRTN  = RTN_WRONG_COMMAND;
        return;
    }

    if (s_tProtocol.ucCommandType >= 0x01 && s_tProtocol.ucCommandType <= 0x51)
    {
        SetBmsVolPara(fPara);
        SetBmsBatTempPara(fPara);
        SetBmsEnvTempPara(fPara);
        SetBmsOtherPara(fPara);
        SetBmsSwitchPara(wPara, &s_tSysPara);
    }
    else
    {
        s_tProtocol.ucRTN  = RTN_WRONG_COMMAND;
        return;
    }

    if (False == SetSysPara(&s_tSysPara, True, CHANGE_BY_YD1363))
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
    }

    return;
}

static void SetBmsVolPara(FLOAT fPara)
{
    switch (s_tProtocol.ucCommandType)
    {
        case 0x01:
            // PACKLOCATION = (BYTE)fPara;                     //上位机需要获取的pack组位置
            break;
        case 0x02:
            s_tSysPara.fCellOverVoltAlmThre = fPara/1000.0;              //单体高压告警参数(单体过压告警阈值)
            break;
        case 0x03:
            s_tSysPara.fCellOverVoltAlmRecoThre = fPara/1000.0;          //单体高压恢复参数(单体过压告警恢复值)
            break;
        case 0x04:
            s_tSysPara.fCellUnderVoltAlmRecoThre = fPara/1000.0;         //单体低压恢复参数(单体欠压告警恢复值)
            break;
        case 0x05:
            s_tSysPara.fCellUnderVoltAlmThre = fPara/1000.0;             //单体低压告警参数(单体欠压告警阈值)
            break;
        case 0x06:
            s_tSysPara.fCellOverVoltPrtThre = fPara/1000.0;              //单体过压保护参数(单体过压保护阈值)
            break;
        case 0x07:
            s_tSysPara.fCellOverVoltPrtRecoThre = fPara/1000.0;          //单体过压恢复参数(单体过压保护恢复值)
            break;
        case 0x08:
            s_tSysPara.fCellUnderVoltPrtRecoThre = fPara/1000.0;         //单体欠压恢复参数(单体欠压保护恢复值)
            break;
        case 0x0A:
            s_tSysPara.fCellUnderVoltPrtThre = fPara/1000.0;             //单体欠压保护参数(单体欠压保护阈值)
            break;
        case 0x0B:
            s_tSysPara.fCellPoorConsisPrtThre = fPara/1000.0;            //单体失效压差参数(单体一致性差保护阈值)
            break;
        case 0x0D:
            s_tSysPara.fBattOverVoltAlmThre = fPara/100.0;               //总压高压告警参数(电池组过压告警阈值)
            break;
        case 0x0E:
            s_tSysPara.fBattOverVoltAlmRecoThre = fPara/100.0;           //总压高压恢复参数(电池组过压告警恢复值)
            break;
        case 0x10:
            s_tSysPara.fBattUnderVoltAlmThre = fPara/100.0;              //总压低压告警参数(电池组欠压告警阈值)
            break;
        case 0x11:
            s_tSysPara.fBattOverVoltPrtThre = fPara/100.0;               //总压过压保护参数(电池组过压保护阈值)
            break;
        case 0x12:
            s_tSysPara.fBattOverVoltPrtRecoThre = fPara/100.0;           //总压过压恢复参数(电池组过压保护恢复值)
            break;
        case 0x14:
            s_tSysPara.fBattUnderVoltPrtThre = fPara/100.0;              //总压欠压保护参数(电池组欠压保护阈值)
            break;
    }
}

static void SetBmsBatTempPara(FLOAT fPara)
{
    switch (s_tProtocol.ucCommandType)
    {
        case 0x16:
            s_tSysPara.fChgTempHighAlmThre = (fPara-2731)/10.0;          //充电高温告警参数(充电高温告警阈值)
            break;
        case 0x17:
            s_tSysPara.fChgTempHighAlmRecoThre = (fPara-2731)/10.0;      //充电高温恢复参数(充电高温告警恢复阈值)
            break;
        case 0x18:
            s_tSysPara.fChgTempLowAlmRecoThre = (fPara-2731)/10.0;       //充电低温恢复参数(充电低温告警恢复阈值)
            break;
        case 0x19:
            s_tSysPara.fChgTempLowAlmThre = (fPara-2731)/10.0;           //充电低温告警参数(充电低温告警阈值)
            break;
        case 0x1A:
            s_tSysPara.fChgTempHighPrtThre = (fPara-2731)/10.0;          //充电过温保护参数(充电高温保护阈值)
            break;
        case 0x1B:
            s_tSysPara.fChgTempHighPrtRecoThre = (fPara-2731)/10.0;      //充电过温恢复参数(充电高温保护恢复阈值)
            break;
        case 0x1C:
            s_tSysPara.fChgTempLowPrtRecoThre = (fPara-2731)/10.0;       //充电欠温恢复参数(充电低温保护恢复阈值)
            break;
        case 0x1D:
            s_tSysPara.fChgTempLowPrtThre = (fPara-2731)/10.0;           //充电欠温保护参数(充电低温保护阈值)
            break;
        case 0x1E:
            s_tSysPara.fDischgTempHighAlmThre = (fPara-2731)/10.0;       //放电高温告警参数(放电高温告警阈值)
            break;
        case 0x1F:
            s_tSysPara.fDischgTempHighAlmRecoThre = (fPara-2731)/10.0;   //放电高温恢复参数(放电高温告警恢复阈值)
            break;
        case 0x20:
            s_tSysPara.fDischgTempLowAlmRecoThre = (fPara-2731)/10.0;    //放电低温恢复参数(放电低温告警恢复阈值)
            break;
        case 0x21:
            s_tSysPara.fDischgTempLowAlmThre = (fPara-2731)/10.0;        //放电低温告警参数(放电低温告警阈值)
            break;
        case 0x22:
            s_tSysPara.fDischgTempHighPrtThre = (fPara-2731)/10.0;       //放电过温保护参数(放电高温保护阈值)
            break;
        case 0x23:
            s_tSysPara.fDischgTempHighPrtRecoThre = (fPara-2731)/10.0;   //放电过温恢复参数(放电高温保护恢复阈值)
            break;
        case 0x24:
            s_tSysPara.fDischgTempLowPrtRecoThre = (fPara-2731)/10.0;    //放电欠温恢复参数(放电低温保护恢复阈值)
            break;
        case 0x25:
            s_tSysPara.fDischgTempLowPrtThre = (fPara-2731)/10.0;        //放电欠温保护参数(放电低温保护阈值)
            break;
    }
}

static void SetBmsEnvTempPara(FLOAT fPara)
{
    switch (s_tProtocol.ucCommandType)
    {
        case 0x26:
            s_tSysPara.fChgHeaterStartupTemp = (fPara - 2731) / 10.0;    //电芯加热开启参数(充电加热膜启动温度)
            s_tSysPara.fDischgHeaterStartupTemp = (fPara - 2731) / 10.0; //电芯加热开启参数(放电加热膜启动温度)
            break;
        case 0x27:
            s_tSysPara.fChgHeaterShutdownTemp = (fPara - 2731) / 10.0;   //电芯加热停止参数(充电加热膜关闭温度)
            s_tSysPara.fDischgHeaterShutdownTemp = (fPara - 2731) / 10.0;//电芯加热停止参数(放电加热膜关闭温度)
            break;
        case 0x28:
            s_tSysPara.fEnvTempHighAlmThre = (fPara-2731)/10.0;          //环境高温告警参数(环境温度高告警阈值)
            break;
        case 0x29:
            s_tSysPara.fEnvTempLowAlmThre = (fPara-2731)/10.0;           //环境低温告警参数(环境温度低告警阈值)
            break;
        case 0x2A:
            s_tSysPara.fEnvTempHighPrtThre = (fPara-2731)/10.0;          //环境过温保护参数(环境温度高保护阈值)
            break;
        case 0x2B:
            s_tSysPara.fEnvTempHighPrtRecoThre = (fPara-2731)/10.0;      //环境过温恢复参数(环境温度高保护恢复阈值)
            break;
        case 0x2C:
            s_tSysPara.fEnvTempLowPrtRecoThre = (fPara-2731)/10.0;       //环境欠温恢复参数(环境温度低保护恢复阈值)
            break;
        case 0x2D:
            s_tSysPara.fEnvTempLowPrtThre = (fPara-2731)/10.0;           //环境欠温保护参数(环境温度低保护阈值)
            break;
        case 0x2E:
            s_tSysPara.fBoardTempHighPrtThre = (fPara-2731)/10.0;        //功率过温保护参数(单板过温保护阈值)
            break;
        case 0x2F:
            s_tSysPara.fBoardTempHighPrtRecoThre = (fPara-2731)/10.0;    //功率过温恢复参数(单板过温保护恢复阈值)
            break;
    }
}

static void SetBmsOtherPara(FLOAT fPara)
{
    switch (s_tProtocol.ucCommandType)
    {
        case 0x30:
            s_tSysPara.fChgCurrHighAlmThre = fPara/s_tSysPara.wBatteryCap/100.0;       //充电过流告警参数(充电过流告警阈值)
            break;
        case 0x33:
            s_tSysPara.fDischgCurrHighAlmThre = fPara/s_tSysPara.wBatteryCap/100.0;    //放电过流告警参数(放电过流告警阈值)
            break;
        case 0x34:
            #ifdef DEVICE_USING_R321
            s_tSysPara.fChgCurrHighPrtThre = fPara/s_tSysPara.wBatteryCap/100.0;       //充电过流保护参数(充电过流保护阈值)
            #endif
            break;
        case 0x36:
            #ifdef DEVICE_USING_R321
                s_tSysPara.fDischgCurrHighPrtThre = fPara/s_tSysPara.wBatteryCap/100.0;    //放电过流保护参数(放电过流保护阈值)
            #endif
            break;
        case 0x3E:
            s_tSysPara.fChargeMaxCurr = fPara/s_tSysPara.wBatteryCap/100.0;            //充电限流设置参数(充电最大电流)
            break;
        case 0x3F:
            s_tSysPara.wBatteryCap = (WORD)fPara/100.0;                                //电池额定容量参数(电池容量)
            break;
        case 0x40:
            s_tSysPara.wBattSOHPrtThre = (WORD)fPara/100;              //电池剩余容量参数(电池SOH低保护阈值)
            break;
        case 0x41:
            s_tSysPara.wBattSOCLowPrtThre = (WORD)fPara/100;              //剩余容量告警参数(电池SOC低保护阈值)
            break;
        case 0x48:
            s_tSysPara.fCellEquVoltDiffThre = fPara/1000.0;              //均衡开启压差参数(单体均衡启动压差阈值)
            break;
    }
}



/****************************************************************************
* 函数名称：SendProtocolVer_v20()
* 调    用：SendProtocolVer()
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：发送通信协议版本号
* 作    者  ：hanhui
* 版本信息：V1.0
* 设计日期：2021-10-22
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
static void SendProtocolVer_v20( BYTE ucPort )
{
    /* LENID */
    s_tProtocol.wSendLenid  = 0;
    
    return;
}

/****************************************************************************
* 函数名称：SendFactoryInfo_v20()
* 调    用：SendFactoryInfo()
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：发送设备厂家信息
* 作    者  ：hanhui
* 版本信息：V1.0
* 设计日期：2021-10-22
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
static void SendFactoryInfo_v20( BYTE ucPort )
{
    WORD wSendLen = 92;//(20+2+20+2+2)*2 = 92
    BYTE wMajorVer = MAJOR_VER;
    BYTE wMinorVer = MINOR_VER;
    CHAR ucCorpName[20] = "BMS_FB100B3_V2";
    //CHAR acSysVer[9];
    BYTE *p = NULL;
    T_BmsInfoStruct tBmsInfo = {0};

    readBMSInfofact(&tBmsInfo);

    p  = s_tProtocol.aucSendBuf;

    MemsetBuff(p, tBmsInfo.acBattCorpName, (BYTE)rt_strnlen_s(tBmsInfo.acBattCorpName, 20), 20, 0x20);         //电池生产厂商信息
    p += 20;
    *(SHORT*)p = Word2Modbus(100);                                     //电池型号
    p += 2;
    MemsetBuff(p, ucCorpName, (BYTE)rt_strnlen_s(ucCorpName, 20), 20, 0x20);//BMS 生产厂商
    p += 20;
    *(SHORT*)p = Word2Modbus(100);                                     //BMS 型号
    p += 2;
    *p++ = wMajorVer;                                                  //BMS通信软件版本号
    *p++ = wMinorVer;

    //s_tProtocol.wSendLenid  = (WORD) ((p-s_tProtocol.aucSendBuf)*2);
    s_tProtocol.wSendLenid = wSendLen;//为解决KW问题

    return;
}

static void SendHistoryData_V20( BYTE ucPort )
{
    BYTE *p = NULL;
    BYTE i;
    WORD num = 0;
    T_HisDataStruct tHisDataSave;
    // T_TimeStruct tTime;
    struct tm tTime;
    time_t t = time(RT_NULL);

    rt_memset_s(&tTime, sizeof(tTime), 0x00, sizeof(tTime));
    rt_memset_s(&s_tSysPara, sizeof(T_SysPara), 0, sizeof(T_SysPara));
    rt_memset_s(&tHisDataSave, sizeof(T_HisDataStruct), 0, sizeof(T_HisDataStruct));

    GetSysPara(&s_tSysPara);

    s_tProtocol.ucCommandType   = s_tProtocol.aucRecBuf[7];
    switch (s_tProtocol.ucCommandType)
    {
        case 0:
            ResetProtoHisDataPoint(NORTH_PROTOCOL_TOWER);
            break;
        case 1:
            MoveProtoHisDataPoint(1, NORTH_PROTOCOL_TOWER);
            break;
        case 2:
            break;
        default:
            s_tProtocol.ucRTN   = RTN_WRONG_COMMAND;
            return;
    }

    num = GetProtoHisDataNum(NORTH_PROTOCOL_TOWER);

    p    = s_tProtocol.aucSendBuf;
    *p++    = PACKLOCATION;     /* PACK位置 */
    
    if ( 0 == num)
    {
        s_tProtocol.ucRTN   = RTN_NO_DATA;
        return;
    }
    else if ( 1 == num)  /* COMMAND_TYPE */
    {
        *p++    = 0x01;     /* 发送最后一条历史数据 */
    }
    else
    {
        *p++    = 0x00;     /* 正常发送一条历史数据 */
    }

    ReadHisData(&tHisDataSave, NORTH_PROTOCOL_TOWER);
    t = tHisDataSave.tStartTime;
    localtime_r(&t, &tTime);
    /* DATA_TIME */
    PutInt16ToBuff(p, tTime.tm_year + 1900);
    p += 2;
    *p++ = tTime.tm_mon + 1;
    *p++ = tTime.tm_mday;
    *p++ = tTime.tm_hour;
    *p++ = tTime.tm_min;
    *p++ = tTime.tm_sec;
    *p++    = tHisDataSave.ucSysMode;     /* 系统模式 */
    *p++    = 6;     /* 告警字节数 */
    *p++    = tHisDataSave.ucCellEvent;     /* 电芯状态事件 */
    *p++    = tHisDataSave.ucCellVolEvent;      //单节电压事件
    tHisDataSave.wTempEvent &= 0x1fff;
    *(SHORT*)p  = Host2Modbus((SHORT*)&tHisDataSave.wTempEvent);      //温度事件
    p += 2;
    *p++ = tHisDataSave.ucCurrentEvent;                             //电流事件
    *p++ = tHisDataSave.ucCapacityEvent;    //容量事件

    *p++    = 25;     /* 遥测量数量 */
    *(SHORT*)p  = Host2Modbus((SHORT*)&tHisDataSave.wBattCurr);         //电池电流
    p += 2;
    #ifdef DEVICE_USING_R321
    *(SHORT*)p  = Host2Modbus((SHORT*)&tHisDataSave.wBusVolt);      //R321铁塔版本历史数据电池电压按照母排电压上送
    #else
    *(SHORT*)p  = Host2Modbus((SHORT*)&tHisDataSave.wBattVolt);     //电池电压  
    #endif
    p += 2;
    *(SHORT*)p  = Host2Modbus((SHORT*)&tHisDataSave.wBattSOC);          //电池SOC   TODO:补充处理
    p += 2;
    for (i = 0; i < CELL_TEMP_NUM; i++) 
    {
        *(SHORT*)p  = Host2Modbus((SHORT*)&tHisDataSave.awCellTemp[i]);      //单体温度
        p += 2;
    }
    tHisDataSave.sEnvTemp = tHisDataSave.sEnvTemp + 2731;
    *(SHORT*)p  = Host2Modbus((SHORT*)&tHisDataSave.sEnvTemp);         //环境温度
    p += 2;
    tHisDataSave.sPowerTemp = tHisDataSave.sPowerTemp/10 + 2731;
    *(SHORT*)p  = Host2Modbus((SHORT*)&tHisDataSave.sPowerTemp);        //功率温度
    p += 2;
    for (i = 0; i < CELL_VOL_NUM_TOWER; i++)
    {
        *(SHORT*)p  = Host2Modbus((SHORT*)&tHisDataSave.awCellVolt[i]);      //单节1-8电压
        p += 2;
    }
#ifdef DEVICE_USING_R321
    for(i = CELL_VOL_NUM_TOWER; i < CELL_VOL_NUM + 1; i++)
	{
    	*(SHORT*)p  = Host2Modbus((SHORT*)&tHisDataSave.awCellVolt[i]);      //单节电9-16压
		p += 2;
	}
#else
    for(i = CELL_VOL_NUM_TOWER; i < CELL_VOL_NUM + 1; i++)
	{
		*(SHORT*)p  = 0;      //9-16节电压
		p += 2;
	}
#endif


    /* LENID */
    s_tProtocol.wSendLenid  = GetLength((p-s_tProtocol.aucSendBuf)*2);    
    return;
}

// static void MemsetBuff(void *des, void *src, BYTE srcLen, BYTE defalutLen, BYTE defalutValue)
// {
//     BYTE    buff[40];
//     BYTE    *pucDest = (BYTE*)des;
//     BYTE    *pucSrc = (BYTE*)src;
//     memset(buff, defalutValue, defalutLen );
//     memcpy(buff, pucSrc, srcLen);
//     memcpy(pucDest, buff, defalutLen);
// }

/****************************************************************************
* 函数名称：SendSysTime_v20()
* 调    用：SendSysTime()
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：发送系统时间
* 作    者  ：hanhui
* 版本信息：V1.0
* 设计日期：2021-10-22
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
static void SendSysTime_v20( BYTE ucPort )
{
    SendSysTime( ucPort );
    
    return;
}

/****************************************************************************
* 函数名称：RecSysTime_v20()
* 调    用：RecSysTime()
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：同步时间
* 作    者: hanhui
* 版本信息：V1.0
* 设计日期：2021-10-22
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
static void RecSysTime_v20( BYTE ucPort )
{
    RecSysTime(ucPort);

    return;
}

static BYTE SetAnalogAlarmValue(BOOLEAN highAlarmCondition, BOOLEAN lowAlarmCodition, BOOLEAN noAlarmCoditon)
{
    if (highAlarmCondition) 
    {
        return 0x02;
    }
    if (lowAlarmCodition)
    {
        return 0x01;
    }
    if (noAlarmCoditon)
    {
        return 0x00;
    }
    return 0x0F;
}

static BOOLEAN IsCellFault(BYTE arr[], BYTE length)
{
    BYTE i;
    for (i = 0; i < length; ++i)
    {
        if (FAULT == arr[i])
        {
            return True;
        }
    }
    return False;
}

WORD Word2Modbus(WORD wTemp)
{
    WORD wChange;
    wChange = ((wTemp & 0xFF) << 8) | ((wTemp & 0xFF00) >> 8);
    return wChange;
}

static WORD WordChange2Modbus(WORD *wTemp)
{
    WORD wChange;
    wChange = *wTemp;
    wChange = ((wChange & 0xFF) << 8) | ((wChange & 0xFF00) >> 8);
    return wChange;
}

BOOLEAN GetTowerSleepStatus(){
    return s_bTowerSleepStatus;
}

void SetTowerSleepStatus(BOOLEAN status){
    s_bTowerSleepStatus = status;
    return ;
}
