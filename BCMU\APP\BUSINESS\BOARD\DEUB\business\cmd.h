/**
 * @file     cmd.h
 * @brief    命令头文件.
 * @details  This is the detail description.
 * <AUTHOR> 
 * @date     2022-12-23
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation 
 * @par History:
 *   version: author, date, descn
 */ 
#ifndef _CMD_H
#define _CMD_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

#include "parse_layer.h"

#define DEV_NORTH_DEUB_UPDATE     1
#define DEV_NORTH_DEUB            2
#define DEV_NORTH_DEUB_APPTEST    3


/* 命令ID:命令唯一标识 */
#define INVALID_CMD_ID                0xff

#define SELF_PACK       NULL       // 自定义封装
#define SELF_PARSE      NULL       // 自定义解析

//命令类型属性定义
#define CMD_PASSIVE   0
#define CMD_POSITIVE  1
#define CMD_BROADCAST 2
#define CMD_NEED_ACK  4
 


#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _CMD_H
