/**
 * @file     TIME_MANAGE.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2023-08-28
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */


#ifndef _TIME_MANAGE_H
#define _TIME_MANAGE_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include <stdio.h>
#include <rtthread.h>
#include <rtdevice.h>
#include <string.h>
#include "data_type.h"
#include "dev_dc_ac.h"
#include "utils_server.h"
#include "storage.h"

#define AUTHO_CHECK_PERIOD   (30 * 60 * 1000)     // 检测时间
#define MONTH_DAYS            30                  // 一个月30天
#define FIRST_CHECK_COUNT     3000                // 多久检测一次首次启动时间
#define RATE_THRESH           5                   // 有功功率阈值5

int get_first_start_time_stamp(date_base_t *pt_time);
int set_first_start_time_stamp(date_base_t time_stamp);
int erase_first_start_time_stamp(void);
int init_first_start_time(void);
int get_exfactory_time_stamp(date_base_t *pt_time);
int set_exfactory_time_stamp(date_base_t time_stamp);
int erase_exfactory_time_stamp(void);
void autho_time_check(dev_inst_t* dev_dc_ac);
int init_autho_manage(void);
int set_start_time(void);
int first_start_time_check(void);
int is_grid_connect(unsigned short dev_status);
int erase_eeprom(unsigned int offset, size_t len);
int process_user_auth_time(void);
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _TIME_MANAGE_H