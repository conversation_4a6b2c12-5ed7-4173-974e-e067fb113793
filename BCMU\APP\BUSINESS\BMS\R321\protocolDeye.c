/**************************************************************************
* 文件名称：protocolDeye.c
* 文件说明：德业协议模块文件（协议组包与协议判断等过程隔离）
***************************************************************************/
#include "common.h"
#include "protocol.h"
#include "rtthread.h"
#include "fileSys.h"
#include "sample.h"
#include "para.h"
#include "protocolDeye.h"
#include "led.h"
#include "protocol1363.h"
#include "hisdata.h"
#include "flash.h"
#include "CommCan.h"
#include "wireless.h"
#include "realAlarm.h"
#include "sys/time.h"
#include "usart.h"
#include "pdt_version.h"
#include "utils_rtthread_security_func.h"

static T_SysPara s_tSysPara;

// CID2 function
Static void GetSysInfo(BYTE ucPort);
Static void GetAnalog(BYTE ucPort);
Static void GetAlarm(BYTE ucPort);
Static void GetBattManageInfo(BYTE ucPort);
Static void SetShutdown(BYTE ucPort);

// CID2 Function Table
const T_CmdFuncStruct s_atCID2AllDeyeTable[] = {
    {GET_SYS_INFO_DEYE, GetSysInfo, 2},             // 获取电池组系统基本信息
    {GET_ANALOG_DEYE, GetAnalog, 2},                // 获取电池组系统运行模拟量数据
    {GET_ALARM_DEYE, GetAlarm, 2},                  // 获取电池组系统状态告警量数据
    {GET_BATT_MANAGE_DEYE, GetBattManageInfo, 2},   // 获取电池组系统充放电管理交互信息
    {SET_SHUTDOWN_DEYE, SetShutdown, 2},            // 控制电池组系统关机指令

    {0x00, 0x0000, 0x00},
};

/***************************************************************************
 * @brief    获取电池组系统基本信息
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/

Static void GetSysInfo(BYTE ucPort)
{
    BYTE *p = s_tProtocol.aucSendBuf;
    T_BmsPACKFactoryStruct tBmsPACKFactory = {0};
    T_BattResult tBattout = {0};
    T_BmsInfoStruct tBmsInfo = {0};

    // 读取工厂信息和系统信息
    readBmsPackFacInfo(&tBmsPACKFactory);
    readBMSInfofact(&tBmsInfo);

    // 获取系统参数和电池结果
    GetSysPara(&s_tSysPara);
    GetBattResult(&tBattout);

    // 清空发送缓冲区
    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));

    // 填充主机设备名称（BMS系统名称）
    MemsetBuff(p, s_tSysPara.acBMSSysName, (BYTE)rt_strnlen_s((CHAR *)s_tSysPara.acBMSSysName, 10), 10, 0x00);
    p += 10;

    // 填充主机厂商名称（电池厂家名称）
    MemsetBuff(p, tBmsInfo.acBattCorpName, (BYTE)rt_strnlen_s((CHAR *)tBmsInfo.acBattCorpName, 20), 20, 0x00);
    p += 20;

    // 填充主版本号和次版本号
    *p++ = MAJOR_VER;
    *p++ = MINOR_VER;

    // 填充电池数量
    *p++ = 1;  // 电池数量

    // 填充整机出厂序列号
    MemsetBuff(p, tBmsPACKFactory.acBmsFacSn, (BYTE)rt_strnlen_s((CHAR *)tBmsPACKFactory.acBmsFacSn, 16), 16, 0x00);
    p += 16;

    // 设置发送长度标识
    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
}


/***************************************************************************
 * @brief    获取电池组系统运行模拟量数据
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
Static void GetAnalog(BYTE ucPort)
{
    BYTE *p = NULL;
    T_BCMDataStruct tBCMAnaData = {0};

    GetRealData(&tBCMAnaData);

    p = s_tProtocol.aucSendBuf;
    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));

    *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.fBattVolt * 1000);  // 电池组系统总平均电压（电池组电压）
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.fBattCurr * 100);  // 电池组系统总平均电流（电池组电流）
    p += 2;

    *(char *)p  = (BYTE)(tBCMAnaData.wBattSOC);  // 电池组系统SOC（电池SOC，高精度SOC，保证精度为2）
    p += 1;

    *(SHORT *)p = Host2Modbus((SHORT *)&tBCMAnaData.wBattCycleTimes);  //平均循环次数（电池循环次数）
    p += 2;

    *(SHORT *)p = Host2Modbus((SHORT *)&tBCMAnaData.wBattCycleTimes);  //最大循环次数（电池循环次数）
    p += 2;

    *(char *)p  = (BYTE)(tBCMAnaData.wBattSOH);  // 平均SOH（电池组最大可用容量）
    p += 1;

    *(char *)p  = (BYTE)(tBCMAnaData.wBattSOH);  // 最小SOH（电池组最大可用容量）
    p += 1;

    *(SHORT*)p  = FloatChangeToModbus(tBCMAnaData.fCellVoltMax * 1000);  //单芯最高电压（单体电压最大值）
    p += 2;

    *p++ = 0x01;  // 单体最高电压所在模块
    *p++ = 0x02;

    *(SHORT*)p  = FloatChangeToModbus(tBCMAnaData.fCellVoltMin * 1000);  //单芯最低电压（单体电压最小值）
    p += 2;

    *p++ = 0x01;  // 单芯最低电压所在模块
    *p++ = 0x02;

    *(SHORT*)p  = FloatChangeToModbus(tBCMAnaData.fCellTempMax*10 + 2731);  //电芯平均温度（单体温度最大值）
    p += 2;

    *(SHORT*)p  = FloatChangeToModbus(tBCMAnaData.fCellTempMax*10 + 2731);  //电芯最高温度（单体温度最大值）
    p += 2;

    *p++ = 0x01;  // 单芯最高温度所在模块
    *p++ = 0x02;

    *(SHORT*)p  = FloatChangeToModbus(tBCMAnaData.fCellTempMin*10 + 2731);  //单芯最低温度（单体温度最小值）
    p += 2;

    *p++ = 0x01;  // 单芯最低温度所在模块
    *p++ = 0x02;
    
    *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.fBoardTemp * 10 + 2731);  // MOSFET平均温度（单板温度）
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.fBoardTemp * 10 + 2731);  // MOSFET最高温度（单板温度）
    p += 2;

    *p++ = 0x01;  // MOSFET最高温度所在模块
    *p++ = 0x02;

    *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.fBoardTemp * 10 + 2731);  // MOSFET最低温度（单板温度）
    p += 2;

    *p++ = 0x01;  // MOSFET最低温度所在模块
    *p++ = 0x02;

    *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.fEnvTemp * 10 + 2731); // BMS平均温度（环境温度）
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.fEnvTemp * 10 + 2731); // BMS最高温度（环境温度）
    p += 2;

    *p++ = 0x01;  // BMS最高温度所在模块
    *p++ = 0x02;

    *(SHORT *)p = FloatChangeToModbus(tBCMAnaData.fEnvTemp * 10 + 2731); // BMS最低温度（环境温度）
    p += 2;

    *p++ = 0x01;  // BMS最低温度所在模块
    *p++ = 0x02;

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    
    return;
}

Static BYTE GetSysAlarmStatus1()
{
    BYTE i = 0;
    BYTE ucStatus = 0;
    BYTE ucCellOverVoltAlm = 0;
    BYTE ucCellUnderVoltAlm = 0;
    BYTE ucCellTempHighAlm = 0;
    BYTE ucCellTempLowAlm = 0;
    T_BCMAlarmStruct tBCMAlm = {0};

    GetRealAlarm(BCM_ALARM, (BYTE *)&tBCMAlm);

    /* 系统告警状态1 */
    for (i = 0; i < CELL_VOL_NUM_MAX; i++)
    {
        if (tBCMAlm.aucCellOverVoltAlm[i] == FAULT)
        {
            ucCellOverVoltAlm = FAULT;
        }
        if (tBCMAlm.aucCellUnderVoltAlm[i] == FAULT)
        {
            ucCellUnderVoltAlm = FAULT;
        }
        if (tBCMAlm.aucChgTempHighAlm[i] == FAULT)
        {
            ucCellTempHighAlm = FAULT;
        }
        if (tBCMAlm.aucDischgTempHighAlm[i] == FAULT)
        {
            ucCellTempHighAlm = FAULT;
        }
        if (tBCMAlm.aucChgTempLowAlm[i] == FAULT)
        {
            ucCellTempLowAlm = FAULT;
        }
        if (tBCMAlm.aucDischgTempLowAlm[i] == FAULT)
        {
            ucCellTempLowAlm = FAULT;
        }
    }

    ucStatus |= (tBCMAlm.ucBattOverVoltAlm << 7);  // 模块总压高压（电池组过压告警）
    ucStatus |= (tBCMAlm.ucBattUnderVoltAlm << 6);  // 模块总压低压（电池组欠压告警）
    ucStatus |= (ucCellOverVoltAlm << 5);  //单芯电压高压（单体过压告警）
    ucStatus |= (ucCellUnderVoltAlm << 4);  //单芯电压低压（单体欠压告警）
    ucStatus |= (ucCellTempHighAlm << 3);  //单芯温度高温（单体充/放电高温告警）
    ucStatus |= (ucCellTempLowAlm << 2);  //单芯温度低温（单体充/放电低温告警）
    ucStatus |= (tBCMAlm.ucBoardTempHighAlm << 1);  //MOSFET高温（单板过温告警）
    ucStatus |= (tBCMAlm.ucCellPoorConsisAlm << 0);  // 单芯电压一致性告警（电芯一致性差告警）
    return ucStatus;
}

Static BYTE GetSysAlarmStatus2()
{
    /* 系统告警状态2 */
    BYTE ucStatus = 0;
    BYTE ucInterCommFault = 0;
    T_BCMAlarmStruct tBCMAlm = {0};

    GetRealAlarm(BCM_ALARM, (BYTE *)&tBCMAlm);

    if (tBCMAlm.ucBDUCommFail || tBCMAlm.ucBattVoltSampleAlm)
    {
        ucInterCommFault = FAULT;
    }

    ucStatus |= (0 << 7);  // 单芯温度一致性告警
    ucStatus |= (tBCMAlm.ucChgCurrHighAlm << 6);  // 充电过流告警（电池组充电过流告警）
    ucStatus |= (tBCMAlm.ucDischgCurrHighAlm << 5);  // 放电过流告警（电池组放电过流告警）
    ucStatus |= (ucInterCommFault << 4);  // 内部通信错误（BDU通信断/电压采样故障）
    ucStatus &= 0xF0;
    return ucStatus;
}

Static BYTE GetSysProStatus1()
{
    BYTE i = 0;
    BYTE ucStatus = 0;
    BYTE ucCellOverVoltPrt = 0;
    BYTE ucCellUnderVoltPrt = 0;
    BYTE ucCellTempHighPrt = 0;
    BYTE ucCellTempLowPrt = 0;
    T_BCMAlarmStruct tBCMAlm = {0};

    GetRealAlarm(BCM_ALARM, (BYTE *)&tBCMAlm);

    for (i = 0; i < CELL_VOL_NUM_MAX; i++)
    {
        if (FAULT == tBCMAlm.aucCellOverVoltPrt[i])
        {
            ucCellOverVoltPrt = FAULT;
        }
        if (FAULT == tBCMAlm.aucCellUnderVoltPrt[i])
        {
            ucCellUnderVoltPrt = FAULT;
        }
        if (FAULT == tBCMAlm.aucChgTempHighPrt[i])
        {
            ucCellTempHighPrt = FAULT;
        }
        if (FAULT == tBCMAlm.aucDischgTempHighAlm[i])
        {
            ucCellTempHighPrt = FAULT;
        }
        if (FAULT == tBCMAlm.aucChgTempLowPrt[i])
        {
            ucCellTempLowPrt = FAULT;
        }
        if (FAULT == tBCMAlm.aucDischgTempLowPrt[i])
        {
            ucCellTempLowPrt = FAULT;
        }
    }

    ucStatus |= (tBCMAlm.ucBattOverVoltPrt << 7);  // 模块总压过压（电池组过压保护）
    ucStatus |= (tBCMAlm.ucBattUnderVoltPrt << 6);  // 模块总压欠压（电池组欠压保护）
    ucStatus |= (ucCellOverVoltPrt << 5);  //单芯电压过压（单体过压保护）
    ucStatus |= (ucCellUnderVoltPrt << 4);  //单芯电压欠压（单体欠压保护）
    ucStatus |= (ucCellTempHighPrt << 3);  //单芯温度过温（单体充/放电高温保护）
    ucStatus |= (ucCellTempLowPrt << 2);  //单芯温度欠温（单体充/放电低温保护）
    ucStatus |= (tBCMAlm.ucBoardTempHighPrt << 1);  //MOSFET过温（单板过温保护）
    ucStatus &= 0xFE;
    return ucStatus;
}

Static BYTE GetSysSysFaultPrt1()
{
    T_BCMAlarmStruct tBCMAlm = {0};

    GetRealAlarm(BCM_ALARM, (BYTE *)&tBCMAlm);

    if (tBCMAlm.ucDischgLoopInvalid    || tBCMAlm.ucChgLoopInvalid      || tBCMAlm.ucCurrLimLoopInvalid  || \
        tBCMAlm.ucBattSOHPrt           || tBCMAlm.ucBoardTempHighPrt    || tBCMAlm.ucBattVoltSampleAlm   || \
        tBCMAlm.ucCellVoltSampleFault  || tBCMAlm.ucBduEepromAlm        || tBCMAlm.ucBDUCommFail         || \
        tBCMAlm.ucBDUBattChgVoltLowPrt || tBCMAlm.ucBDUBattLockAlm      || \
        tBCMAlm.ucEqualCircuitFaultAlm)
    {
        return FAULT;
    }
    return NORMAL;
}

Static BYTE GetSysProStatus2()
{
    BYTE i = 0;
    BYTE ucStatus = 0;
    BYTE ucSysFaultPrt = 0;
    T_BCMAlarmStruct tBCMAlm = {0};

    GetRealAlarm(BCM_ALARM, (BYTE *)&tBCMAlm);

    ucSysFaultPrt = GetSysSysFaultPrt1();

    for (i = 0; i < CELL_VOL_NUM_MAX; i++)
    {
        if (FAULT == tBCMAlm.aucCellDamagePrt[i])
        {
            ucSysFaultPrt = FAULT;
        }
    }

    for (i = 0; i < CELL_TEMP_NUM_MAX; i++)
    {
        if (FAULT == tBCMAlm.ucCellTempSensorInvalidAlm[i])
        {
            ucSysFaultPrt = FAULT;
        }
    }
    ucStatus |= (tBCMAlm.ucChgCurrHighPrt << 6);  // 充电过流保护（电池组充电过流保护）
    ucStatus |= (tBCMAlm.ucDischgCurrHighPrt << 5);  // 放电过流保护（电池组放电过流保护）
    ucStatus |= (ucSysFaultPrt << 3);  // 系统故障保护（多种条件）
    ucStatus &= 0xA8;

    return ucStatus;
}

/***************************************************************************
 * @brief    获取电池组系统状态告警量数据
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
Static void GetAlarm(BYTE ucPort)
{
    BYTE *p = NULL;

    p = s_tProtocol.aucSendBuf;
    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));

    *p++ = GetSysAlarmStatus1();
    *p++ = GetSysAlarmStatus2();
    *p++ = GetSysProStatus1();
    *p++ = GetSysProStatus2();

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    
    return;
}

Static BYTE GetBattManageInfo1()
{
    BYTE i = 0;
    BYTE ucStatus = 0;
    BYTE ucChargeEn = 0;
    BYTE ucDischargeEn = 0;
    BYTE ucForceChg = 0;
    BYTE ucChgFull = 0;
    T_BCMDataStruct tBCMData = {0};
    T_BCMAlarmStruct tBCMAlm = {0};

    GetRealData(&tBCMData);
    GetRealAlarm(BCM_ALARM, (BYTE *)&tBCMAlm);

    if (BATT_MODE_CHARGE == tBCMData.ucBattPackSta)
    {
        ucChargeEn = FAULT;
    }
    if (BATT_MODE_DISCHARGE == tBCMData.ucBattPackSta)
    {
        ucDischargeEn = FAULT;
    }
    if (tBCMAlm.ucBattUnderVoltPrt)
    {
        ucForceChg = FAULT;
    }
    for (i = 0; i < CELL_VOL_NUM_MAX; i++)
    {
        if (FAULT == tBCMAlm.aucCellUnderVoltPrt[i])
        {
            ucForceChg = FAULT;
        }
    }
    if (BATT_MODE_STANDBY == tBCMData.ucBattPackSta)
    {
        ucChgFull = FAULT;
    }
    ucStatus |= (ucChargeEn << 7);  //充电
    ucStatus |= (ucDischargeEn << 6); //放电
    ucStatus |= (ucForceChg << 5);  //强充
    ucStatus |= (ucChgFull << 4);  //充满
    ucStatus &= 0xF0;
    return ucStatus;
}

/***************************************************************************
 * @brief    获取电池组系统充放电管理交互信息
 * @param    {BYTE} ucPort
 * @return   {*}
 **************************************************************************/
Static void GetBattManageInfo(BYTE ucPort)
{
    BYTE *p = NULL;

    GetSysPara(&s_tSysPara);

    p = s_tProtocol.aucSendBuf;
    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));

    *(SHORT *)p = FloatChangeToModbus(MAX_CHARGE_VOLT_DEYE * 1000); // 充电电压建议上限
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(MIN_DISCHARGE_VOLT_DEYE * 1000); // 放电电压建议下限
    p += 2;   

    *(SHORT *)p = FloatChangeToModbus(MAX_CHARGE_CURRENT_DEYE * 10); // 最大充电电流    
    p += 2;

    *(SHORT *)p = FloatChangeToModbus(MAX_DISCHARGE_CURRENT_DEYE * 10); // 最大放电电流
    p += 2;

    *p++ = GetBattManageInfo1();

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    
    return;
}


// 关闭电源管理开关
Static void DeyeShutDown(void *para)
{
    SetPMOSStatus(GPIO_OFF);
}

// 重置MCU
Static void DeyeReset(void *para)
{
    ResetMCU(NO_RESET_UNKNOW);
}


/***************************************************************************
 * @brief    控制电池组系统关机指令
 * @param    {BYTE} ucPort
 * @return   void
 **************************************************************************/

Static void SetShutdown(BYTE ucPort)
{
    // 创建并启动定时器
    rt_timer_t shutdownTimer = rt_timer_create("DeyeShutdown", DeyeShutDown, RT_NULL, 3 * ONE_SEC, RT_TIMER_FLAG_ONE_SHOT);
    rt_timer_t resetTimer = rt_timer_create("DeyeReset", DeyeReset, RT_NULL, 20 * ONE_SEC, RT_TIMER_FLAG_ONE_SHOT);

    if (shutdownTimer != RT_NULL)
    {
        rt_timer_start(shutdownTimer);
    }

    if (resetTimer != RT_NULL)
    {
        rt_timer_start(resetTimer);
    }

    // 清空发送缓冲区并重置长度标识
    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));
    s_tProtocol.wSendLenid = 0;

    // 记录操作日志
    SaveAction(GetActionId(CONTOL_SHUTDOWN), "DeyeCtrlShutDown");

    // 控制BDU进入休眠模式
    BduCtrl(SCI_CTRL_SLEEP, 1);

    // 设置按钮模式为休眠
    Button_Mode_Set(BUT_SLEEP);
}

