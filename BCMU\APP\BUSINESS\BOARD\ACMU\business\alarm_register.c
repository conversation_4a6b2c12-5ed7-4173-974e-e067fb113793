#include "alarm_register.h"
#include "alarm_manage.h"
#include "alarm_id_in.h"
#include "realdata_id_in.h"
#include "alarm_config_in.h"
#include "realdata_id_in.h"
#include "sample.h"
#include "realdata_save.h"
#include "dev_acmu.h"
#include "Menu.h"
#include "alarm_mgr_api.h"

/* 通过告警码清除实时告警列表 */

static unsigned short alarm_clean_by_code_tab[] = {
    0xffff
};

static alarm_shield_relation_t alarm_shield_relation_tab[] = {
    {ACMU_ALM_ID_AC_PHASE_LOST, ACMU_ALM_ID_AC_POWER_OFF, ALARM_SHIELD_ONE_TO_MORE},
    {ACMU_ALM_ID_AC_LINE_VOLT_HIGH, ACMU_ALM_ID_AC_POWER_OFF, ALARM_SHIELD_ONE_TO_MORE},
    {ACMU_ALM_ID_AC_LINE_VOLT_LOW, ACMU_ALM_ID_AC_POWER_OFF, ALARM_SHIELD_ONE_TO_MORE},
    {ACMU_ALM_ID_PHASE_VOLT_HIGH_ALARM, ACMU_ALM_ID_AC_POWER_OFF, ALARM_SHIELD_ONE_TO_MORE},
    {ACMU_ALM_ID_PHASE_VOLT_LOW_ALARM, ACMU_ALM_ID_AC_POWER_OFF, ALARM_SHIELD_ONE_TO_MORE},
    {ACMU_ALM_ID_PHASE_CURR_HIGH_ALARM, ACMU_ALM_ID_AC_POWER_OFF, ALARM_SHIELD_ONE_TO_MORE},
    {0xffff,}
};

static alarm_manage_info_t s_acmu_manage = {
    ALARM_ID_OFFSET_MAX - 1,        //告警码的个数
    ana_alm_config_tab,             //模拟量告警表
    dig_alm_config_tab,             //状态量告警表
    self_alm_config_tab,            //自定义告警表
    alm_shielded,                    //屏蔽关系表
    alarm_clean_by_code_tab,        // 以告警码清除相关告警列表
    alarm_shield_relation_tab,      // 告警屏蔽关系表
};

short register_acmu_alarm(void)
{
    register_alarm_manage_info(&s_acmu_manage);
    return 0;
}

/**
 * @brief 告警判断函数
 * @param current_value 实时数据值
 * @param threshold 阈值
 * @param hysteresis 回差值
 * @param mode 告警判断模式
 * @param current_state 当前告警状态
 * @return 新的告警状态
 */
char check_alarm(float current_value, float threshold, 
                      float hysteresis, char mode, 
                      char current_state) {
    unsigned char usage_scenario = 0;

    get_one_para(ACMU_PARA_ID_USAGE_SCENARIO_OFFSET, &usage_scenario);
    if(usage_scenario == SCENE_SUPPORTING)
    {
        return FALSE;
    }
    
    switch (mode) {
        case ALARM_HIGH_LIMIT:  // 高限判断模式
            if (current_state == ALARM_NOT_EXSIT) {
                // 正常状态下，实时数据高于阈值时产生告警
                if (current_value > threshold) {
                    return TRUE;
                }
            } else {
                // 告警状态下，实时数据小于阈值+回差值时恢复告警
                if (current_value <= threshold + hysteresis) {
                    return FALSE;
                }
            }
            break;
            
        case ALARM_LOW_LIMIT:   // 低限判断模式
            if (current_state == ALARM_NOT_EXSIT) {
                // 正常状态下，实时数据小于阈值时产生告警
                if (current_value < threshold) {
                    return TRUE;
                }
            } else {
                // 告警状态下，实时数据高于阈值+回差值时恢复告警
                if (current_value >= threshold + hysteresis) {
                    return FALSE;
                }
            }
            break;
    }
    
    // 状态未发生变化，保持原状态
    return current_state;
}

char env_temp_invalid_judge(int alm_id)
{
    float temperature = 0;
    unsigned char usage_scenario = 0;
    get_one_data(ACMU_DATA_ID_ENV_TEMP, &temperature);

    get_one_para(ACMU_PARA_ID_USAGE_SCENARIO_OFFSET, &usage_scenario);
    if(usage_scenario == SCENE_SUPPORTING)
    {
        return FALSE;
    }
    
    if(temperature<TEMPERATURE_MIN || temperature>TEMPERATURE_MAX){
        return TRUE;
    }
    else{
        return FALSE;
    }
}



char temperature_sensor_alarm_judge(void)
{
    char ret = 0;
    char alarm_status = 0;
    int alm_id = 0;

    alm_id = GET_ALM_ID(ACMU_ALM_ID_ENV_TEMP_HIGH_ALARM,1,1);
    alarm_status = get_realtime_alarm_value(alm_id);
    if(TRUE == alarm_status){
        ret = 2;    // 环境温度高告警
    }

    alm_id = GET_ALM_ID(ACMU_ALM_ID_ENV_TEMP_LOW_ALARM,1,1);
    alarm_status = get_realtime_alarm_value(alm_id);
    if(TRUE == alarm_status){
        ret = 1;    // 环境温度低告警
    }

    alm_id = GET_ALM_ID(ACMU_ALM_ID_ENV_TEMP_SENSOR_INVALID_ALARM,1,1);
    alarm_status = get_realtime_alarm_value(alm_id);
    if(TRUE == alarm_status){
        ret = 4;    // 环境温度无效告警
    }

    return ret;
}



char humidity_sensor_alarm_judge(void)
{
    int alm_id;

    alm_id = GET_ALM_ID(ACMU_ALM_ID_ENV_HUMIDITY_SENSOR_INVALID_ALARM, 1, 1);
    if (get_realtime_alarm_value(alm_id) == TRUE) {
        return 4; // 环境湿度无效告警
    }

    alm_id = GET_ALM_ID(ACMU_ALM_ID_ENV_HUMIDITY_LOW_ALARM, 1, 1);
    if (get_realtime_alarm_value(alm_id) == TRUE) {
        return 1; // 环境湿度低告警
    }

    alm_id = GET_ALM_ID(ACMU_ALM_ID_ENV_HUMIDITY_HIGH_ALARM, 1, 1);
    if (get_realtime_alarm_value(alm_id) == TRUE) {
        return 2; // 环境湿度高告警
    }

    return 0;
}

char meter_disconnected_judge(int alm_id)
{
    unsigned char comm_fail_flag = get_acem_timeout_flag();

    if (comm_fail_flag) {
        return TRUE;
    }
    else {
        return FALSE;
    }
}


char env_humidity_invalid_judge(int alm_id) {
    float humidity = 0;
    unsigned char usage_scenario = 0;
    get_one_data(ACMU_DATA_ID_ENV_HUMIDITY, &humidity);

    get_one_para(ACMU_PARA_ID_USAGE_SCENARIO_OFFSET, &usage_scenario);
    if(usage_scenario == SCENE_SUPPORTING)
    {
        return FALSE;
    }
    if (humidity < HUMIDITY_MIN || humidity > HUMIDITY_MAX) {
        return TRUE;
    } else {
        return FALSE;
    }
}

char phase_current_judge(int alm_id) {

    unsigned char dev_addr = 0, config_status = 0;
    float curr = 0, threshold = 0;

    dev_addr = ALM_ID_GET_DEV(alm_id) - 1;

    get_one_data(ACMU_DATA_ID_PHASE_CURR + dev_addr, &curr);
    get_one_para(ACMU_PARA_ID_AC_IN_CURRENT_MAX_OFFSET, &threshold);
    get_one_para(ACMU_PARA_ID_SYS_AC_MUTUAL_INDUCTANCE_CONFIGURATION_OFFSET + dev_addr,&config_status);

    if (get_realtime_alarm_value(GET_ALM_ID(ACMU_ALM_ID_AC_POWER_OFF, 1, 1)) == TRUE) {
        return FALSE;
    }

    if (config_status == FALSE) {
        return FALSE;
    }

    if (get_realtime_alarm_value(alm_id) == ALARM_NOT_EXSIT)
    {
        if (curr > threshold) {
            return TRUE;
        }
        else {
            return FALSE;
        }
    }
    else
    {
        if (curr <= (threshold - 4)) {
            return FALSE;
        }
        else {
            return TRUE;
        }
    }
        
}

char input_relay_status_judge(int alm_id) {
    unsigned char relay_status = 0;
    unsigned char relay_para_status = 0;
    int i;
    int alm_code[] = {ACMU_ALM_ID_INPUT_RELAY1_ALARM,
                      ACMU_ALM_ID_INPUT_RELAY2_ALARM,
                      ACMU_ALM_ID_INPUT_RELAY3_ALARM,
                      ACMU_ALM_ID_INPUT_RELAY4_ALARM};
    unsigned char usage_scenario = 0;

    get_one_para(ACMU_PARA_ID_USAGE_SCENARIO_OFFSET, &usage_scenario);
    if(usage_scenario == SCENE_SUPPORTING)
    {
        return FALSE;
    }

    // 查找alm_id对应的输入继电器序号
    for(i = 0; i < INPUT_RELAY_NUM; i++) {
        if (alm_id == GET_ALM_ID(alm_code[i], 1, 1)) {
            get_one_data(ACMU_DATA_ID_INPUT_RELAY + i, &relay_status);
            get_one_para(ACMU_PARA_ID_INRELAYTTL_OFFSET + i, &relay_para_status);
            return (relay_status == relay_para_status) ? TRUE : FALSE;
        }
    }

    return FALSE;
}



int judge_phase_volt_alarm(unsigned char* buff, unsigned char offset)
{
    char alarm_status_high, alarm_status_low, alarm_status_lost[3];
    int alm_id = 0;
    alm_id = GET_ALM_ID(ACMU_ALM_ID_AC_PHASE_LOST, 1, 1); // 交流缺相
    alarm_status_lost[0] = get_realtime_alarm_value(alm_id);
    alm_id = GET_ALM_ID(ACMU_ALM_ID_AC_PHASE_LOST, 2, 1); // 交流缺相
    alarm_status_lost[1] = get_realtime_alarm_value(alm_id);
    alm_id = GET_ALM_ID(ACMU_ALM_ID_AC_PHASE_LOST, 3, 1); // 交流缺相
    alarm_status_lost[2] = get_realtime_alarm_value(alm_id);

    for (int i = 0; i < 3; i++) // 3相交流电压
    {
        alm_id = GET_ALM_ID(ACMU_ALM_ID_PHASE_VOLT_LOW_ALARM, i + 1, 1); // 交流相电压低
        alarm_status_low = get_realtime_alarm_value(alm_id);
        alm_id = GET_ALM_ID(ACMU_ALM_ID_PHASE_VOLT_HIGH_ALARM, i + 1, 1); // 交流相电压高
        alarm_status_high = get_realtime_alarm_value(alm_id);

        if (alarm_status_low != ALM_NORMAL)
        {
            buff[offset + i] = LOW;
        }
        else if (alarm_status_high != ALM_NORMAL)
        {
            buff[offset + i] = HIGH;
        }
        else if (alarm_status_lost[i] != ALM_NORMAL)
        {
            buff[offset + i] = LACK_PHASE;
        }
        else
        {
            buff[offset + i] = ALM_NORMAL;
        }

    }
    return SUCCESSFUL;
}

unsigned char handle_alarm_status(int alm_id, unsigned char value_abnormal, unsigned char value_normal)
{
    if(get_realtime_alarm_value(alm_id) == ALARM_EXSIT)
    {
        return value_abnormal;
    }
    else
    {
        return value_normal;
    }
}

char total_alarm_judge(int alm_id)
{
    int real_alarm_count = 0;
    unsigned char total_alarm_status = 0;
    real_alarm_count = get_realtime_alarm_count();
    if(get_realtime_alarm_value(alm_id) == TRUE && real_alarm_count == 1)    //有且仅有总告警存在时，总告警恢复
    {
        return FALSE;
    }

    if (real_alarm_count > 0) {
        return TRUE;
    } else {
        return FALSE;
    }
}

char output_switch_disconnection_judge(int alm_id)
{
    unsigned char output_switch_status = 0;
    unsigned char output_switch_config = 0;
    unsigned char dev_sn = 0;
    dev_sn = ALM_ID_GET_DEV(alm_id) - 1;
    get_one_data(ACMU_DATA_ID_AC_OUT_SWITCH + dev_sn, &output_switch_status);
    get_one_para(ACMU_PARA_ID_SYS_AC_OUT_SWITCH_CONFIGURATION_OFFSET + dev_sn, &output_switch_config);

    if(output_switch_config == FALSE) //交流输出空开为配置，屏蔽告警
    {
        return FALSE;
    }

    return output_switch_status;
}

char oil_engine_start_judge(int alm_id)
{
    unsigned char ac_input_status = 0;  //交流输入状态
    unsigned char ac_power_off_alm_status = 0;  //交流停电告警状态
    unsigned char oil_engine_start_alm_status = 0;  //油机启动告警状态
    get_one_data(ACMU_DATA_ID_AC_INPUT_SWITCH, &ac_input_status);
    ac_power_off_alm_status = get_realtime_alarm_value(GET_ALM_ID(ACMU_ALM_ID_AC_POWER_OFF, 1, 1));
    oil_engine_start_alm_status = get_realtime_alarm_value(GET_ALM_ID(ACMU_ALM_ID_OIL_ENGINE_START_UP_ALARM, 1, 1));

    if(oil_engine_start_alm_status == ALARM_NOT_EXSIT)
    {
        if(ac_input_status == INPUT_MAINS_ELECTRICITY2 && ac_power_off_alm_status == ALARM_NOT_EXSIT)   //交流输入状态为市电2且无交流停电告警
        {
            return TRUE;
        }
        else
        {
            return FALSE;
        }
    }
    else
    {
        if(ac_input_status == INPUT_MAINS_ELECTRICITY1)   //交流输入状态为市电1
        {
            return FALSE;
        }
        else if(ac_input_status == INPUT_MAINS_ELECTRICITY2 && ac_power_off_alm_status == ALARM_EXSIT)  //交流输出状态为市电2且产生交流停电告警
        {
            return FALSE;
        }
        else{
            return TRUE;
        }
    }
}

char env_humidity_high_alarm_judge(int alm_id)
{
    float env_humidity = 0;  //环境湿度
    float humidity_sensor_upper = 0;  //环境湿度高阈值
    float hysteresis = -5;
    char current_state = 0;
    char mode = ALARM_HIGH_LIMIT;

    get_one_data(ACMU_DATA_ID_ENV_HUMIDITY, &env_humidity);
    get_one_para(ACMU_PARA_ID_HUMIDITY_SENSOR_UPPER_OFFSET, &humidity_sensor_upper);
    current_state = get_realtime_alarm_value(alm_id);
    return check_alarm(env_humidity, humidity_sensor_upper, hysteresis, mode, current_state);
}

char env_humidity_low_alarm_judge(int alm_id)
{
    float env_humidity = 0;  //环境湿度
    float humidity_sensor_upper = 0; 
    float hysteresis = 5;
    char current_state = 0;
    unsigned char usage_scenario = 0;

    get_one_para(ACMU_PARA_ID_USAGE_SCENARIO_OFFSET, &usage_scenario);
    if(usage_scenario == SCENE_SUPPORTING)
    {
        return FALSE;
    }
    get_one_data(ACMU_DATA_ID_ENV_HUMIDITY, &env_humidity);
    get_one_para(ACMU_PARA_ID_HUMIDITY_SENSOR_LOWER_OFFSET, &humidity_sensor_upper);
    current_state = get_realtime_alarm_value(alm_id);
    if (current_state == ALARM_NOT_EXSIT) {
        if (env_humidity < humidity_sensor_upper && env_humidity >= 10) {
            return TRUE;
        }
    } else {
        // 告警状态下，实时数据高于阈值+回差值时恢复告警
        if (env_humidity >= humidity_sensor_upper + hysteresis || env_humidity < 10) {
            return FALSE;
        }
    }
    return current_state;
}

char env_temp_alarm_judge(int alm_id)
{
    float env_temp = 0;  //环境温度
    float threshold = 0;  
    float hysteresis = -3;
    char current_state = 0;
    char mode = ALARM_HIGH_LIMIT;

    get_one_data(ACMU_DATA_ID_ENV_TEMP, &env_temp);
    switch(ALM_ID_GET_ALM_CODE(alm_id))
    {
        case ACMU_ALM_ID_ENV_TEMP_HIGH_ALARM:  
            get_one_para(ACMU_PARA_ID_TEMPERATURE_SENSOR_UPPER_OFFSET, &threshold);
            mode = ALARM_HIGH_LIMIT;
            hysteresis = -3;
            break;
        case ACMU_ALM_ID_ENV_TEMP_LOW_ALARM:  
            get_one_para(ACMU_PARA_ID_TEMPERATURE_SENSOR_LOWER_OFFSET, &threshold);
            mode = ALARM_LOW_LIMIT;
            hysteresis = 3;
            break;
    }
    current_state = get_realtime_alarm_value(alm_id);
    return check_alarm(env_temp, threshold, hysteresis, mode, current_state);
}

char sensor_alarm_judge(int alm_id)
{
    char sensor = 0; 
    unsigned char usage_scenario = 0;

    get_one_para(ACMU_PARA_ID_USAGE_SCENARIO_OFFSET, &usage_scenario);
    if(usage_scenario == SCENE_SUPPORTING)
    {
        return FALSE;
    }

    switch(ALM_ID_GET_ALM_CODE(alm_id))
    {
        case ACMU_ALM_ID_FUMES_SENSOR_ALARM:  // 烟雾传感器告警数据
            get_one_data(ACMU_DATA_ID_FUMES_SWITCH_STATUS, &sensor);
            break;
        case ACMU_ALM_ID_FLOOD_SENSOR_ALARM:  // 水淹传感器告警数据
            get_one_data(ACMU_DATA_ID_FLOOD_SWITCH_STATUS, &sensor);
            break;
        case ACMU_ALM_ID_DOORMAT_SENSOR_ALARM:  // 门磁传感器告警数据
            get_one_data(ACMU_DATA_ID_DOORMAT_SWITCH_STATUS, &sensor);
            break;
    }

    return sensor;
}