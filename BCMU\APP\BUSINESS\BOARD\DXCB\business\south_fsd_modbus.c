#include "south_dev_common.h"
#include "utils_data_transmission.h"
#include "realdata_id_in.h"
#include "para_id_in.h"
#include "south_dev_modbus.h"


/* 命令请求头 */
static modbusrtu_cmd_head_t cmd_req[] = {
    {READ_HOLD_REG},
    {READ_INPUT_REG},
    {WRITE_SINGLE_REG},
};

/* 命令应答头 */
static modbusrtu_cmd_head_t cmd_ack[] = {
    {READ_HOLD_REG},
    {READ_INPUT_REG},
    {WRITE_SINGLE_REG},
};

//解析实时量寄存器地址：53264-53281
static data_info_id_verison_t parse_real_data_info[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},//数据的字节长度
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_FAN_SPEED_PERCENT},
};

static data_info_id_verison_t parse_real_data_info2[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},//数据的字节长度
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_MOTOR_STATUS},
};

static data_info_id_verison_t parse_real_data_info3[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},//数据的字节长度
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_POWER},
};

// static data_info_id_verison_t parse_val_src_info[] RAM_SECTION = {
//     {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},//数据的字节长度
//     {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_FAN_SET_VALUE_SOURCE},
// };

static cmd_parse_info_id_verison_t cmd_parse_info[] RAM_SECTION =
{
    {&parse_real_data_info[0], sizeof(parse_real_data_info)/sizeof(data_info_id_verison_t)},
    {&parse_real_data_info2[0], sizeof(parse_real_data_info2)/sizeof(data_info_id_verison_t)},
    {&parse_real_data_info3[0], sizeof(parse_real_data_info3)/sizeof(data_info_id_verison_t)},
    // {&parse_val_src_info[0], sizeof(parse_val_src_info)/sizeof(data_info_id_verison_t)},
};

/* 命令总表,必须以空字符串""结束 */
static cmd_t no_poll_cmd_tab[] = {
    {FSD_SET_WIND_SPEED_CTRL, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data, NULL,NULL},
    {FSD_SET_ADDR,            CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data, NULL,NULL},
    {FSD_SET_VALUE_SOURCE,    CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data, NULL,NULL},
    {0},
};

static cmd_t poll_cmd_tab[] = {
    // {READ_FSD_HOLD_REG,    CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[0]},
    {READ_FSD_INPUT_REG,   CMD_POSITIVE, &cmd_req[1], &cmd_ack[1], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[0]},
    {READ_FSD_INPUT_REG2,  CMD_POSITIVE, &cmd_req[1], &cmd_ack[1], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[1]},
    {READ_FSD_INPUT_REG3,  CMD_POSITIVE, &cmd_req[1], &cmd_ack[1], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[2]},
    // {FSD_GET_VALUE_SOURCE, CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[3]},
    {0},
};


int update_fan_fsd_dev_type(dev_inst_t* dev_inst, char dev_num, char is_inter_fan)
{
    return change_dev_type_info_by_diff_brand(dev_inst, SOUTH_FAN_TYPE, is_inter_fan, dev_num, no_poll_cmd_tab, poll_cmd_tab);
}



