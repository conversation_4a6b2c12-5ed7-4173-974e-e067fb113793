/**
 * @file     plat_sps_device.h
 * @brief    监控协议栈设备(含命令)管理接口头文件。
 * @details  监控协议栈设备(含命令)管理接口头文件。
 * <AUTHOR> 
 * @date     2017-03-16
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation 
 * @par History:
 *   version: author, date, descn
 */ 

#ifndef _MSG_ID_H
#define _MSG_ID_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

/* 消息ID定义 */
#define ALARM_PARA_CHANGE_MSG             1    ///< 告警参数变化消息
#define ALARM_CHANGE_MSG                  2    ///< 告警变化消息
#define RELAY_DO_CTR_MSG                  3    ///< 输出干节点控制消息
#define NOTIFY_ADDR_DISTRIBUTION_MSG      4    ///< 通知BCMU启动站址分配
#define START_BMU_ADDR_DISTRIBUTION_MSG   5    ///< BMU站址分配开始消息
#define FINISH_BMU_ADDR_DISTRIBUTION_MSG  6    ///< BMU站址分配结束消息
#define TOPO_CALC_FINISH_MSG              7    ///< 电池簇拓扑信息计算完成
#define NOTIFY_VOLT_SAMPLE_MSG            8    ///< 通知BMU进行电压采样
#define NOTIFY_VOLT_SAMPLE_FINISH_MSG     9    ///< 通知电压采样结束
#define START_DEV_POLL_MSG                10   ///< 启动设备轮询消息
#define STOP_DEV_POLL_MSG                 11   ///< 停止设备轮询消息
#define EXE_DEST_CMD_MSG                  12   ///< 执行目标命令消息
#define SET_DC_DC_BATCH_PARA_MSG          13   ///< 设置dcdc批量参数消息
#define SET_DC_DC_PARA_MSG                14   ///< 设置dcdc单个参数消息
#define CTRL_BATT_CHARGE_MSG              15   ///< 控制电池充电
#define CTRL_BATT_DISCHARGE_MSG           16   ///< 控制电池放电
#define CTRL_BATT_STANDBY_MSG             17   ///< 控制电池待机
#define FIRE_ALARM_PROTECT_MSG            18   ///< 消防告警关联保护消息
#define EPO_ALARM_MSG                     19   ///< 紧急关机消息
#define BMU_SELF_CHECK_MSG                20   ///< BMU进行自检
#define ALM_LED_CTRL_MSG                  21   ///< 告警灯控制
#define ALM_MANAGE_SAVE_HIS_ALM           22   ///< 告警模块保存历史告警
#define SOC_SOH_SET_MSG                   23   ///< SOX业务下发参数
#define UPDATE_TASK_EXE_REQ_MSG           24   ///< 请求执行升级任务
#define BALANCE_FAULT_DETECTED_MSG        25   ///< 请求均衡故障检测
#define HEART_BEAT_MSG                    26   ///< 线程守护
#define CTRL_POWER_ON_MSG                 27   ///< 来电
#define LED_BLINK_CTRL_MSG                28   ///< LED指示灯控制
#define DEVICE_SAMPLE_MSG                 29   ///< 设备采样消息
#define CHECK_DC_AC_PARA                  30   ///< 检查dcac参数是否一致
#define DAY_POWER_MSG                     31   ///< 日功率记录
#define HIS_DATA_TEST                     32   ///< 生成历史数据
#define HIS_ALM_TEST                      33   ///< 生成历史告警
#define OPERATE_RECORD_TEST               34   ///< 生成操作记录
#define ENERGY_ACCUM_MSG                  35   ///< 电量累计
#define SAVE_IV_DATA_MSG                  36   ///< 保存iv数据
#define ENERGY_DATA_TEST                  37   ///< 生成近25年电量统计记录
#define SAVE_HIS_DATA_MSG                 38   ///< 保存历史数据消息
#define GET_IV_DATA_MSG                   39   ///< 获取iv数据
#define ENERGY_SAVE_RESTART               40   ///< 系统重启，保存电量
// #define SAVE_RECORD_FAULT_HEAD_MSG        41   ///< 保存录波的头文件
#define SAVE_RECORD_FAULT_MSG             42   ///< 保存故障录波文件
#define LED_CTRL_MSG                      43   ///< LED灯控制消息
#define AUTHO_TIME_CHECK_MSG              44   ///< 授权时间检查管理
#define ALM_MANAGE_SAVE_REAL_ALM          45   ///< 告警模块保存实时告警
#define STOP_PARA_CHECK                   46   ///< 停止设备参数检查
#define START_PARA_CHECK                  47   ///< 启动设备参数检查
#define MQTT_ALARM_CHANGE_MSG             48   ///< 告警发生变化发送给mqtt模块
#define MQTT_ALARM_UP_MSG                 49   ///< mqtt模块上送告警
#define MQTT_REAL_UP_MSG                  50   ///< mqtt模块上送实时量
#define RS485_STAT_MSG                    51   ///< RS485统计
#define NEW_ALARM_MSG                     52   ///< 新告警产生消息
#define PROCESS_ALM_HIS_DATA_MSG          53   ///< 执行告警历史数据处理功能消息
#define RESTORE_FACTORY                   54   ///< 恢复出厂设置
#define GRID_CODE_CHANGE                  55   ///< 电网标准码改变
#define CTRL_SHIELD_CAN_COMM              56   ///< 控制屏蔽can通讯
#define RECOVER_LED_STATUS_MSG            57   ///< 恢复灯的状态
#define MQTT_REAL_ALL_UP_MSG              58   ///< mqtt模块上送所有实时量
#define UPDATE_STATUS                     59   ///< 升级状态
#define CSU_RESET                         60   ///< csu重启
#define SAVE_EVENT_MSG                    61   ///< 保存历史操作记录
#define DEL_HIS_DATA_AND_REBOOT           62   ///< 删除历史数据并重启
#define GET_SLAVE_REAL_DATA               63   ///< 获取并机工况下从机实时数据
#define SET_SLAVE_PARA                    64   ///< 设置并机工况下从机参数数据
#define PARALLEL_ALARM_CHANGE_MSG         65   ///< 并机告警数据改变
#define MASTER_CTRL_SLAVE                 66   ///< 遥控从机
#define SET_SLAVE_ONE_PARA                67   ///< 对从机进行单个参数的设置
#define SET_SLAVE_ONE_CMD_RET             68   ///< 对从机设置参数或控制命令后的回复
#define MQTT_UPDATE_RESULT_MSG            69   ///< mqtt升级后获取升级结果
#define STOP_IV_SCAN_MSG                  70   ///< 停止iv扫描消息
#define NETWORK_DOWNLOAD_FILE_MSG         71   ///< 网管下载文件通知消息
#define GET_SLAVE_PARA                    72   ///< 获取从机参数
#define PARALLEL_UPDATE_MSG               73   ///< 并机升级消息
#define CLEAN_HIS_ENERGY_MSG              74   ///< 清除历史电量
#define MQTT_DEAL_UPDATE_RESULT           75   ///< 处理升级结果
#define PARA_CHANGE_MSG_ID                76   ///< 参数变化
#define DATA_CHANGE_MSG_ID                77   ///< 数据变化
#define ALARM_CHANGE_MSG_ID               78   ///< 告警变化
#define CLEAN_REAL_ALARM_MSG              79   ///< 清除实时告警
#define CLEAN_HIS_ALARM_MSG               80   ///< 清除历史告警
#define MQTT_IMMEDIATE_SUBMIT_REAL        81   ///< 立刻上报实时数据给网管
#define CERT_CHG_MSG                      82   ///< 所有证书变更
#define CERT_ONE_CHG_MSG                  83   ///< 单个证书文件变更
#define SAVE_FAC_DATA                     84   ///< 保存厂家信息到eeprom
#define ALARM_SIGNAL_MSG                  85   ///< 告警总信号、事故总信号
#define SET_CHG_PARA_MSG                  86   ///< 执行单个参数设置的命令
#define DELAY_UPDATE_CHECH_MSG            87   ///< 延迟升级检测
#define PARA_FILE_EXPORT_MSG              88   ///< 参数文件导出
#define GRID_CODE_STATUS_CHECK            89   ///< 检查电网标准码参数状态
#define PARA_FILE_IMPORT_MSG              90   ///< 参数文件导入
#define ENTER_APPTEST_MSG                 91   ///< 进入apptest
#define FORMATE_FILE_SYS_MSG              92   ///< 文件系统格式化
#define NORTH_TO_SOUTH_APPTEST_MSG        93   ///< 北向给南向透传发消息
#define SOUTH_TO_NORTH_APPTEST_MSG        94   ///< 南向给北向透传发消息
#define STOP_SEND_ALARM_MSG               95   ///< 停止主动上送告警
#define MQTT_UPDATE                       96   ///< 达到升级条件后才启动网管升级
#define START_DELAY_UPDATE_MSG            97   ///< 启动延迟升级
#define JUDGE_DELAY_UPDATE_STATUS         98   ///< 判断延迟升级条件

// 告警组件
#define CHECK_SIGNAL_ALARM_CHG_MSG 101      // 单个告警发生变化
#define CHECK_ALL_ALARM_CHG_MSG    102      // 检测完所有告警，有告警变化
#define CHECK_ALL_ALARM_TRIG_RELAY 103      // 检测完所有告警，触发干接点动作
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _MSG_ID_H
