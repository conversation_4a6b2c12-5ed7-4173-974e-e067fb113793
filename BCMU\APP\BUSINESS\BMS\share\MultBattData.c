#include "common.h"
#include "CommCan.h"
#include "battery.h"
#include "BattSleep.h"
#include "sample.h"
#include "MultBattData.h"
#include "realAlarm.h"
#include "utils_rtthread_security_func.h"

#ifdef USE_CAN2
#include "CommCan2.h"
#endif
#ifdef USING_SNMP
#include "SNMPData.h"
#endif

Static T_SlaveDataResult s_tSlavesDataResult;
Static T_SlavesRealData s_atSlavesRealData[SLAVE_NUM];

static FLOAT s_afBattVol[SLAVE_NUM];
static BOOLEAN DealMultStatusData(BYTE ucIndex);
static BOOLEAN DealMultPeakShiftSlaveNumData(BYTE ucIndex);
static WORD CalSlaveAvgSOC(UINT32 ulTotalSOC,BYTE ucSlaveNum);
static BOOLEAN DealMultFmPowerData(BYTE ucIndex);
static BOOLEAN DealMultFmSlaveNumData(BYTE ucIndex);
static BOOLEAN DealMultCurrData(BYTE ucIndex);
static BOOLEAN DealMultVoltData(BYTE ucIndex);
static BOOLEAN DealMultEqualData(T_MultBattEqualPara *ptBattEqual);
static BOOLEAN DealMultMaxVolt(BYTE ucIndex, FLOAT *pfMaxVol);

static void UpdateMaxBattVal(FLOAT *pfMaxBatVol, WORD *pwMaxBatSOC, T_SlavesRealData *ptSlavesRealData);

#ifdef USING_SNMP
static BOOLEAN DealMultNetData(void)
{
    BYTE i;
    for (i=0; i<SLAVE_NUM; i++)
    {
        if (s_atSlavesRealData[i].bNetAccess)
        {
            SetBattAddrNet(i+1);
            return TRUE;
        }
    }
    SetBattAddrNet(0);
    return FALSE;
}
#endif
#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
static T_Can2Data s_atCAN2SlavesData[CAN2_SLAVE_NUM];
static BOOLEAN SyncCAN2MasterInfo(T_BattInfo *pBattIn);
static BOOLEAN MultCAN2Data(void);
#endif

static void MultBattCalData(BYTE ucCellVoltNum, FLOAT *pfBattTotalVolt, BYTE *pucDischgNum, FLOAT *pfBattMaxVol)
{
    BYTE i;
    s_tSlavesDataResult.ucBusCurrValidNum = 0;
    T_MultBattEqualPara tBattEqual;
    rt_memset_s(&tBattEqual, sizeof(T_MultBattEqualPara), 0, sizeof(T_MultBattEqualPara));

#ifdef USING_SNMP
    DealMultNetData();
#endif

    for (i=0; i<SLAVE_NUM; i++)
    {
        if (s_atSlavesRealData[i].bExist && !s_atSlavesRealData[i].bUpgrade)
        {
            s_tSlavesDataResult.ucBattExistNum++;
            tBattEqual.ucIndex = i;
            if (fabs(s_atSlavesRealData[i].fBatCur) > MIN_CURR_DET_BDCU
                && s_atSlavesRealData[i].fBatVol >= (OUT_VOLT_MIN/15.0*ucCellVoltNum)
                )
            {
                (*pfBattTotalVolt) += s_atSlavesRealData[i].fBatVol;
                (*pucDischgNum)++;
                s_tSlavesDataResult.bSysLimit = (s_tSlavesDataResult.bSysLimit || s_atSlavesRealData[i].bDischgLimit);
            }

            DealMultFmPowerData(i);
            DealMultStatusData(i);
            DealMultPeakShiftSlaveNumData(i);
            DealMultFmSlaveNumData(i);
            DealMultCurrData(i);
            DealMultVoltData(i);
            DealMultEqualData(&tBattEqual);
            DealMultMaxVolt(i, pfBattMaxVol);
        }
    }
    
#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
    if (tBattEqual.ucDischBattNum != 0 && tBattEqual.wCapSum != 0 && !s_tSlavesDataResult.bB3BattExist)
#else
    if (tBattEqual.ucDischBattNum != 0 && tBattEqual.wCapSum != 0)
#endif
    {
        s_tSlavesDataResult.fBattBalanceCurr = tBattEqual.fCurrSum / tBattEqual.wCapSum;
        s_tSlavesDataResult.wAveSoc = tBattEqual.ulHighSOCSum / tBattEqual.ucDischBattNum;
        s_tSlavesDataResult.fAveBattVolt = tBattEqual.fBattVoltSum / tBattEqual.ucDischBattNum;
    }

    if(s_tSlavesDataResult.ucNormalSlaveNum != 0)
    {
        s_tSlavesDataResult.fChgDischgAveCurr = s_tSlavesDataResult.fBattTotalCurr / s_tSlavesDataResult.ucNormalSlaveNum;
        s_tSlavesDataResult.ucFMBatteryleftcapacity = (s_tSlavesDataResult.ulTotalSOC/s_tSlavesDataResult.ucNormalSlaveNum)/100;
    }

    s_tSlavesDataResult.wSlaveAvgSOC = CalSlaveAvgSOC(s_tSlavesDataResult.ulTotalSlaveSOC,s_tSlavesDataResult.ucNormalSalveNumNoMaster);

}

static BOOLEAN DealMultMaxVolt(BYTE ucIndex, FLOAT *pfMaxVol)
{
    if(NULL == pfMaxVol)
    {
        return False;
    }
    if (!s_atSlavesRealData[ucIndex].bLoopOff)
    {
        s_tSlavesDataResult.fBusVolMax = MAX(s_tSlavesDataResult.fBusVolMax, s_atSlavesRealData[ucIndex].fBusVol);
        *pfMaxVol = MAX(*pfMaxVol, s_atSlavesRealData[ucIndex].fBatVol);
    }
    return True;
}

static BOOLEAN DealMultStatusData(BYTE ucIndex) // 圈复杂度
{
    if (s_atSlavesRealData[ucIndex].bChargeInBreak 
    && !s_atSlavesRealData[ucIndex].bLoopOff
    && !s_atSlavesRealData[ucIndex].bBattLock
    && !s_atSlavesRealData[ucIndex].bActivatePortCurrErr
    //&& fabs(s_atSlavesRealData[i].fBatCur) > (MIN_CURR_DET_BDCU)
    )
    {
        s_tSlavesDataResult.bSlaveChargeBreak = TRUE;
    }

    if(s_atSlavesRealData[ucIndex].ucBattType == 0)  //存在B3电池
    {
        s_tSlavesDataResult.bB3BattExist = True;
    }
    return True;
}

//处理从机的最大充电功率

static BOOLEAN DealMultFmPowerData(BYTE ucIndex)
{
    if(!s_atSlavesRealData[ucIndex].bChgPrt)
    {
        //只要存在一台电池可充电，则可充电
        s_tSlavesDataResult.ucFMChgAndDischgDisable &= 0xFD;
        s_tSlavesDataResult.fFMCurrentMaxChgPower += s_atSlavesRealData[ucIndex].fFMcurrentSingleMaxChgPower; 
    }

    if(!s_atSlavesRealData[ucIndex].bDischgPrt)
    {
        //只要存在一台电池可放电，则电池可放电
        s_tSlavesDataResult.ucFMChgAndDischgDisable &= 0xFE;
    }    

    return True;
}


//调频时，正常从机数量统计

static BOOLEAN DealMultFmSlaveNumData(BYTE ucIndex)
{
    if ((s_atSlavesRealData[ucIndex].bChargeStat && !s_atSlavesRealData[ucIndex].bChgPrt)
        || (!s_atSlavesRealData[ucIndex].bChargeStat
            && !s_atSlavesRealData[ucIndex].bDischgPrt
            && !s_atSlavesRealData[ucIndex].bChgPrt))
    {
        s_tSlavesDataResult.ucFmNormalSlaveNum++;
    }

    return True;
}


//统计正常从机的数量合总的SOC

static BOOLEAN DealMultPeakShiftSlaveNumData(BYTE ucIndex)
{
   if(!s_atSlavesRealData[ucIndex].bDischgPrt)
    {
        if(ucIndex != GetBMSAddr() - 1)
        {
            s_tSlavesDataResult.ucNormalSalveNumNoMaster++;
            s_tSlavesDataResult.ulTotalSlaveSOC += s_atSlavesRealData[ucIndex].wBatHighSOC;
        }
    }
    return True;
}



static WORD CalSlaveAvgSOC(UINT32 ulTotalSOC,BYTE ucSlaveNum)
{
    WORD wAvgSOC = 0;
    
    if(ucSlaveNum != 0)
    {
        wAvgSOC = (ulTotalSOC/ucSlaveNum)/100;
    }
    
    return wAvgSOC;
}


static BOOLEAN DealMultCurrData(BYTE ucIndex)
{
    if (s_atSlavesRealData[ucIndex].bChargeStat && !s_atSlavesRealData[ucIndex].bChgPrt)
    {
        s_tSlavesDataResult.ucChgStaNum++;
        s_tSlavesDataResult.ucNormalSlaveNum++;
        s_tSlavesDataResult.fBattTotalCurr += s_atSlavesRealData[ucIndex].fBatCur;
        s_tSlavesDataResult.ulTotalSOC += s_atSlavesRealData[ucIndex].wBatHighSOC;
    }
    else if(!s_atSlavesRealData[ucIndex].bChargeStat
        && !s_atSlavesRealData[ucIndex].bDischgPrt
        )
    {
        s_tSlavesDataResult.ucNormalSlaveNum++;
        s_tSlavesDataResult.fBattTotalCurr += s_atSlavesRealData[ucIndex].fBatCur;
        s_tSlavesDataResult.ulTotalSOC += s_atSlavesRealData[ucIndex].wBatHighSOC;

    }

    if(!s_atSlavesRealData[ucIndex].bDischgPrt)
    {
        s_tSlavesDataResult.ucDischgNormalNum++;
    }

    if (fabs(s_tSlavesDataResult.fMaxCurrInAll) < fabs(s_atSlavesRealData[ucIndex].fBatCur) )
    {
        s_tSlavesDataResult.fMaxCurrInAll = s_atSlavesRealData[ucIndex].fBatCur;
    }

    if (fabs(s_tSlavesDataResult.fMaxBusCurrInAll) < fabs(s_atSlavesRealData[ucIndex].fBusCur) )
    {
        s_tSlavesDataResult.fMaxBusCurrInAll = s_atSlavesRealData[ucIndex].fBusCur;
    }
    return True;
}

static BOOLEAN DealMultVoltData(BYTE ucIndex)
{
    //从机有放电电流时，fSysVol计算调整为从机外部电压
    if(fabs(s_atSlavesRealData[ucIndex].fBatCur) > MIN_CURR_DET_BDCU)
    {
        s_tSlavesDataResult.fSysVol_slave = MAX(s_tSlavesDataResult.fSysVol_slave, s_atSlavesRealData[ucIndex].fBusVol);
        s_tSlavesDataResult.ucBusCurrValidNum++;
    }
    return True;
}

static BOOLEAN DealMultEqualData(T_MultBattEqualPara *ptBattEqual)
{
    BYTE ucIndex = 0;
    if(ptBattEqual == NULL)
    {
        return False;
    }

    ucIndex = ptBattEqual->ucIndex;
    if(!s_atSlavesRealData[ucIndex].bDischgPrt && !s_atSlavesRealData[ucIndex].bLoopOff)
    {
        s_tSlavesDataResult.fMinBattVolt = MIN(s_tSlavesDataResult.fMinBattVolt, s_atSlavesRealData[ucIndex].fBatVol);
        if(!s_atSlavesRealData[ucIndex].bChargeStat)
        {
            ptBattEqual->fCurrSum += s_atSlavesRealData[ucIndex].fBatCur;
            ptBattEqual->wCapSum += s_atSlavesRealData[ucIndex].wBattCapacity;
            ptBattEqual->ucDischBattNum += 1;
            ptBattEqual->ulHighSOCSum += s_atSlavesRealData[ucIndex].wBatHighSOC;
            ptBattEqual->fBattVoltSum +=  s_atSlavesRealData[ucIndex].fBatVol;
        }
    }
    return True;
}

static void getMasterRealData(T_SlavesRealData *ptMasterRealData, T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    T_DCRealData tRealData;
    T_BCMDataStruct tBcmData;
    T_FmSubmissionstruct tFmRealData;
    GetBduReal(&tRealData);
    GetRealData(&tBcmData);
    GetFmRealData(&tFmRealData);

    if (NULL != ptMasterRealData)
    {      
#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
        if(IsMaster() && GetCan2ConnectedFlag() && IsSleep())
        {
            ptMasterRealData->bExist = False;
        }
        else
        {
            ptMasterRealData->bExist = True;
        }
#else
        ptMasterRealData->bExist = True;
#endif
        ptMasterRealData->bChgPrt = tBcmData.ucChgProtSta|tBcmData.ucBattChargeEn|tBcmData.ucBduSleep;
        ptMasterRealData->bDischgPrt = tBcmData.ucDischgProtSta | tBcmData.ucBattDischEn | tBcmData.ucBduSleep;
        ptMasterRealData->bChgLimit = tBcmData.ucLimit;
        ptMasterRealData->bDischgLimit = tBcmData.ucLimit;
        ptMasterRealData->bChargeInBreak = pBattIn->tData.bBduChargeBreak;
        ptMasterRealData->bChargeStat = (BATT_MODE_CHARGE == pBattDeal->ucChargeMode);
        ptMasterRealData->wBatHighSOC = pBattIn->tData.wBattHighPrecSOC;
        ptMasterRealData->fBatCur = tBcmData.fBattCurr>0? tBcmData.fBattCurr : tBcmData.fBusCurr;
        ptMasterRealData->fBusCur = tBcmData.fBusCurr;
        ptMasterRealData->fBatVol = tBcmData.fBattVolt * g_ProConfig.fVoltTrasRate;
        ptMasterRealData->fBusVol = tBcmData.fExterVolt;
        ptMasterRealData->fChargeSetCurr = pBattDeal->fSysChgMaxCurr;
        ptMasterRealData->bLoopOff = pBattDeal->bLoopOff;
        ptMasterRealData->bUpgrade = IsBduUpdate();
        ptMasterRealData->bChgCurrSta = GetChgCurrStatus();
        ptMasterRealData->bRotateDisable = pBattDeal->bBattRotateDisable;
        ptMasterRealData->bStatFault = GetStatusConsistFault();
        ptMasterRealData->bExternalPowerOn = tRealData.tDCStatus.bExternalPowerOn;
        ptMasterRealData->ucRotateLimitCurr = pBattDeal->ucRotateMaxCurr;
        ptMasterRealData->bQuietSleepSta = GetSlaveQuietSleepStatus();
        ptMasterRealData->bBattLock = IsBattLock();
        ptMasterRealData->ucBattType = 1;
        ptMasterRealData->bActivatePortCurrErr = pBattDeal->bActivatePortCurrError;
        ptMasterRealData->bBattInSys = True;
        ptMasterRealData->fFMcurrentSingleMaxChgPower = tFmRealData.fFMcurrentSingleMaxChgPower;
        #ifdef USING_SNMP
        ptMasterRealData->bNetAccess = IsEthLink();
        #endif
        ptMasterRealData->bRotateChgHeat = getbChgTempLowPrtOnly();
    }
}

static void SyncMasterBattInfo(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    BYTE i = 0; 
    T_SlavesRealData tMasterRealData;
    i = GetBMSAddr() - 1;  
    rt_memset_s(&tMasterRealData,sizeof(T_SlavesRealData),0, sizeof(T_SlavesRealData));

    if (i>=SLAVE_NUM)
    {
        return;
    }

    getMasterRealData(&tMasterRealData, pBattIn, pBattDeal);
    rt_memcpy_s(&s_atSlavesRealData[i], sizeof(T_SlavesRealData), &tMasterRealData, sizeof(T_SlavesRealData));
    return;
}

void SlaveData(BYTE ucCellVoltNum, T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    #ifdef UNITEST
    setTestSlaveResult(&s_tSlavesDataResult);
    #endif
    T_BCMDataStruct     tBcmData;
    BYTE ucDischgNum=0, ucSn;
    FLOAT fBattTotalVolt = 0.0;
    FLOAT fBattMaxVol = 0.0;
    GetRealData(&tBcmData);
    ucSn = GetBMSAddr() - 1;
    
    if (ucSn >= SLAVE_NUM)
    {
        return;
    }
    getSlaveData(s_atSlavesRealData);
    SyncMasterBattInfo(pBattIn, pBattDeal);          //同步主机数据

    s_tSlavesDataResult.ucNormalSlaveNum = 0;
    s_tSlavesDataResult.ucNormalSalveNumNoMaster = 0;
    s_tSlavesDataResult.ucFmNormalSlaveNum = 0;
    s_tSlavesDataResult.ulTotalSOC = 0;
    s_tSlavesDataResult.ulTotalSlaveSOC = 0;
    s_tSlavesDataResult.ucFMBatteryleftcapacity = 0;
    s_tSlavesDataResult.wSlaveAvgSOC = 0;
    s_tSlavesDataResult.fFMCurrentMaxChgPower = 0.0;
    s_tSlavesDataResult.ucFMChgAndDischgDisable = 0x03;
    s_tSlavesDataResult.bSysLimit = s_atSlavesRealData[ucSn].bDischgLimit;
    s_tSlavesDataResult.fSysVol_slave = OUT_VOLT_MIN;
    s_tSlavesDataResult.fBusVolMax = tBcmData.fExterVolt;
    s_tSlavesDataResult.fBattTotalCurr = 0.0f;
    s_tSlavesDataResult.fMinBattVolt = s_atSlavesRealData[ucSn].fBatVol;
    s_tSlavesDataResult.ucDischgNormalNum = 0;
    s_tSlavesDataResult.ucBattExistNum = 0;
#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
    s_tSlavesDataResult.fBattBalanceCurr = pBattDeal->fDischgEqualCurr / pBattIn->tPara.wBattCap;
#else
    s_tSlavesDataResult.fBattBalanceCurr = 0;
#endif

    s_tSlavesDataResult.ucChgStaNum = 0;
    if ( IsMaster() )
    {
        s_tSlavesDataResult.bB3BattExist = False;
        s_tSlavesDataResult.fMaxCurrInAll = tBcmData.fBattCurr;
        s_tSlavesDataResult.fMaxBusCurrInAll = tBcmData.fBusCurr;
        s_tSlavesDataResult.bSlaveChargeBreak = FALSE;
        fBattMaxVol = tBcmData.fBattVolt * g_ProConfig.fVoltTrasRate;
        MultBattCalData(ucCellVoltNum, &fBattTotalVolt, &ucDischgNum, &fBattMaxVol);
        pBattIn->tData.fBusVolMax = s_tSlavesDataResult.fBusVolMax;    //主机的最大BUS电压
#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
        if(IsMasterCan2())
        {
            getCAN2SlaveData(s_atCAN2SlavesData);
            SyncCAN2MasterInfo(pBattIn);
            MultCAN2Data();
        }
#endif
    }
    else
    {
        fBattTotalVolt = MAX(s_atSlavesRealData[ucSn].fBatVol, OUT_VOLT_MIN/15.0*ucCellVoltNum);
    }

    if( 0 == s_tSlavesDataResult.ucBusCurrValidNum )
    {
#ifdef DEVICE_USING_D121
        s_tSlavesDataResult.fSysVol_slave = s_tSlavesDataResult.fBusVolMax;
#else
        s_tSlavesDataResult.fSysVol_slave = MAX(s_tSlavesDataResult.fBusVolMax, s_atSlavesRealData[ucSn].fBusVol);
#endif

    }

    if (ucDischgNum >= 1) ///if slave bms exist, DischgNum is more than 1
    {
        s_tSlavesDataResult.fBattAverageVol = fBattTotalVolt/ucDischgNum;
    }
    else
    {
        s_tSlavesDataResult.fBattAverageVol = MAX(s_atSlavesRealData[ucSn].fBatVol, fBattMaxVol);
    }

    if(fabs(s_tSlavesDataResult.fSysVol_slave - OUT_VOLT_MIN) < 0.01 || !IsMaster())
    {
        s_tSlavesDataResult.fSysVol_slave = s_atSlavesRealData[ucSn].fBusVol;
    }
}

/***************************************************************************
 * @brief   多簇数据处理:计算平均soc，判断是否存在放电电流
 **************************************************************************/
#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
static BOOLEAN MultCAN2Data(void)
{
    BYTE i;
    BYTE ucClusterNum = 0;
    ULONG ulSocSum = 0;
    BOOLEAN bDischgExist = False;

    for(i = 0; i < CAN2_SLAVE_NUM; i++)
    {
        if(s_atCAN2SlavesData[i].bExist)
        {
            ucClusterNum++;
            ulSocSum += s_atCAN2SlavesData[i].wAveSoc;
            if(s_atCAN2SlavesData[i].fAveCurr < -MIN_CURR_DET_BDCU)
            {
                bDischgExist = True;
            }
        }
    }
    if(ucClusterNum > 0)
    {
        s_tSlavesDataResult.wAveSocInAllCluster = ulSocSum / ucClusterNum;
    }
    s_tSlavesDataResult.bCAN2DischgExist = bDischgExist;

    return True;

}
#endif

/***************************************************************************
 * @brief   载入簇主机数据
 **************************************************************************/
#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
static BOOLEAN SyncCAN2MasterInfo(T_BattInfo *pBattIn)
{
    BYTE i = GetCAN2Addr() - CAN2_ADDR_START; 
    T_Can2Data tMasterRealData;
    rt_memset(&tMasterRealData, 0, sizeof(T_Can2Data));

    if (i>=CAN2_SLAVE_NUM)
    {
        return False;
    }

    tMasterRealData.bExist = True;
    tMasterRealData.fAveCurr = pBattIn->tData.fAveCurrInCluster;
    tMasterRealData.wAveSoc = pBattIn->tData.wAveSocInCluster;
    tMasterRealData.fAveBattVolt = pBattIn->tData.fAveBattVolt;

    rt_memcpy(&s_atCAN2SlavesData[i], &tMasterRealData, sizeof(T_Can2Data));

    return True;
}
#endif

BOOLEAN getChargeBreakStatus(void)
{
    return s_tSlavesDataResult.bSlaveChargeBreak;
}

BOOLEAN getSysLimit(void)
{
    return s_tSlavesDataResult.bSysLimit;
}

FLOAT getSysVolt(void)
{
    return s_tSlavesDataResult.fSysVol_slave;
}

FLOAT getMaxCurrInAll(void)
{
    return s_tSlavesDataResult.fMaxCurrInAll;
}

FLOAT getMaxBusCurrInAll(void)
{
    return s_tSlavesDataResult.fMaxBusCurrInAll;
}

FLOAT getBattAverageVol(void)
{
    return s_tSlavesDataResult.fBattAverageVol;
}

FLOAT getBattTotalCurr(void)
{
    return s_tSlavesDataResult.fBattTotalCurr;
}

BYTE GetChgStaNum(void)
{
    return s_tSlavesDataResult.ucChgStaNum;
}

FLOAT getSlaveDataMax(FLOAT fMaxBatVol)
{
    BYTE i,ucSn;
    T_SlavesRealData *p = NULL;

    ucSn = GetBMSAddr() - 1;

    if (ucSn >= SLAVE_NUM)
    {
        return 0;
    }

    fMaxBatVol = s_atSlavesRealData[ucSn].fBatVol;
    for ( i = 0; i < SLAVE_NUM; i++ )
    {
        p = &s_atSlavesRealData[i];
        if ( (p->bExist) && (fMaxBatVol < p->fBatVol) )
        {
            fMaxBatVol = p->fBatVol;
        }
    }
    return fMaxBatVol;
}

FLOAT getSlaveDataMin1(void)
{
    return s_tSlavesDataResult.fMinBattVolt;
}

FLOAT getSlaveDataMin(FLOAT fMinBatVol)
{
    BYTE i,ucSn;
    T_SlavesRealData *p = NULL;

    ucSn = GetBMSAddr() - 1;

    if (ucSn >= SLAVE_NUM)
    {
        return 0;
    }

    fMinBatVol = s_atSlavesRealData[ucSn].fBatVol;
    for ( i = 0; i < SLAVE_NUM; i++ )
    {
        p = &s_atSlavesRealData[i];
        if ( (p->bExist) && (fMinBatVol > p->fBatVol) )
        {
            fMinBatVol = p->fBatVol;
        }
    }
    return fMinBatVol;
}

BYTE getSlaveNum(void)
{
    return s_tSlavesDataResult.ucNormalSlaveNum;
}

BYTE getDischgNum(void)
{
    return s_tSlavesDataResult.ucDischgNormalNum;
}

BOOLEAN IfMasterChgFull(BYTE ucChargeMode)
{
    if(IsMaster())
    {
        if(BATT_MODE_STANDBY == ucChargeMode)
        {
            return True;
        }
        else
        {
            return False;
        }
    }
    return False;
}

BYTE getBusCurrValidNum(void)
{
    return s_tSlavesDataResult.ucBusCurrValidNum;
}

BOOLEAN getBattType(void)
{
    return  s_tSlavesDataResult.bB3BattExist;
}

FLOAT getBalanceCurr(void)
{
    return s_tSlavesDataResult.fBattBalanceCurr;
}

/***************************************************************************
 * @brief   获取簇内平均电流
 **************************************************************************/
FLOAT GetAveCurr(void)
{
    return s_tSlavesDataResult.fChgDischgAveCurr;
}

/***************************************************************************
 * @brief   获取簇内平均soc
 **************************************************************************/
WORD GetAveSoc(void)
{
    return s_tSlavesDataResult.wAveSoc;
}

/***************************************************************************
 * @brief   获取簇内平均电池电压
 **************************************************************************/
FLOAT GetAveBattVolt(void)
{
    return s_tSlavesDataResult.fAveBattVolt;
}

/***************************************************************************
 * @brief   获取所有簇的平均soc
 **************************************************************************/
WORD GetAveSocInAllCluster(void)
{
    return s_tSlavesDataResult.wAveSocInAllCluster;
}

/***************************************************************************
 * @brief   获取是否存在放电电流标志
 **************************************************************************/
BOOLEAN GetCAN2BattStatus(void)
{
    return s_tSlavesDataResult.bCAN2DischgExist;
}

/***************************************************************************
 * @brief   获取整个站点剩余容量百分比
 **************************************************************************/

BYTE GetBatteryLeftCapacity(void)
{
    return s_tSlavesDataResult.ucFMBatteryleftcapacity;
}


/***************************************************************************
 * @brief   获取整个站点的最大充电功率
 **************************************************************************/

FLOAT GetBatteryMaxChargePower(void)
{
    return s_tSlavesDataResult.fFMCurrentMaxChgPower;
}


/***************************************************************************
 * @brief   获取整个站点的充放电禁止状态
 **************************************************************************/

BYTE GetBatteryChangeAndDischgStatus(void)
{
    return s_tSlavesDataResult.ucFMChgAndDischgDisable;
}


/***************************************************************************
 * @brief   调频时获取正常从机数量(在线非浮充不在统计范围内)
 **************************************************************************/

BYTE GetFmNormalSlave(void)
{
    return s_tSlavesDataResult.ucFmNormalSlaveNum;
}


/***************************************************************************
 * @brief   获取是否在位的电池数量（从机都休眠，主机连接了can2也休眠时返回0）
 **************************************************************************/
BYTE GetBattExistNum(void)
{
    return s_tSlavesDataResult.ucBattExistNum;
}


WORD GetSlaveAvgSOC(void)
{
    return s_tSlavesDataResult.wSlaveAvgSOC;
}


#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
static FLOAT CalcVolDiffFromSOC(WORD wBattSOCWithMaxVol, WORD wMaxBatSOC)
{
    FLOAT fVol=0.0f;
    
    fVol = (FLOAT)(wMaxBatSOC - wBattSOCWithMaxVol)/1000.0f;
    return GetValidData(2.0*fVol, 0.8, -0.8);
}
#endif /* MONITORING_BASED_EQUALIZE_CURRENT_ENABLED */

BOOLEAN getMaxVolAndMaxSoc(FLOAT *pfMaxBatVol, WORD *pwMaxBatSOC, BYTE *pucSlaveNum)
{
    #ifdef UNITEST
    getSlavesRealData(&s_atSlavesRealData[0]);
    #endif
    BYTE i, ucSn;
    T_SlavesRealData *p = NULL;
    if(pfMaxBatVol==NULL || pwMaxBatSOC==NULL || pucSlaveNum==NULL)
    {
        return FALSE;
    }

    ucSn = GetBMSAddr() - 1; 
    if (ucSn>=SLAVE_NUM)
    {
        return FALSE;
    }

    *pucSlaveNum = 0;
    if(fabs(s_atSlavesRealData[ucSn].fBatCur) > MIN_CURR_DET_BDCU || 0 == s_tSlavesDataResult.ucBusCurrValidNum)
    {
        *pfMaxBatVol = s_atSlavesRealData[ucSn].fBatVol;

        *pwMaxBatSOC = MAX(*pwMaxBatSOC, s_atSlavesRealData[ucSn].wBatHighSOC);
    }  
    for ( i = 0; i < SLAVE_NUM; i++ )
    {
        s_afBattVol[i] = 0.0f;
        p = &s_atSlavesRealData[i];
        if ((p->bExist) && (!p->bDischgPrt) && !p->bLoopOff && !p->bUpgrade)
        {
            if(s_tSlavesDataResult.ucBusCurrValidNum>0 && (fabs(p->fBatCur) < MIN_CURR_DET_BDCU))
            {
                continue;
            }
            s_afBattVol[i] = p->fBatVol;     
            UpdateMaxBattVal(pfMaxBatVol, pwMaxBatSOC, p);
            (*pucSlaveNum)++;
        }
    }
    if (*pfMaxBatVol <= 1.0f)
    {
        *pfMaxBatVol = s_atSlavesRealData[ucSn].fBatVol;
        *pwMaxBatSOC = s_atSlavesRealData[ucSn].wBatHighSOC;
    }
    return TRUE;
}
//降低圈复杂度
static void UpdateMaxBattVal(FLOAT *pfMaxBatVol, WORD *pwMaxBatSOC, T_SlavesRealData *ptSlavesRealData)
{
    if (*pwMaxBatSOC < ptSlavesRealData->wBatHighSOC)
    {
        *pwMaxBatSOC = ptSlavesRealData->wBatHighSOC;
    }
    if (*pfMaxBatVol < ptSlavesRealData->fBatVol)
    {
        *pfMaxBatVol = ptSlavesRealData->fBatVol;
    }
    return;
}
#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
void getBoostVol(FLOAT *pfBoostVol, WORD *pwMaxBatSOC, FLOAT fDischargeHopeVol, FLOAT fCompVol)
{
    BYTE i,ucSn;
    FLOAT fTmpVol;
    T_SlavesRealData *p = NULL;
    BYTE ucVoltBoostCounter = 0;

    ucSn = GetBMSAddr() - 1; 

    if (ucSn>=SLAVE_NUM || pfBoostVol==NULL || pwMaxBatSOC==NULL)
    {
        return;
    }
    if ((0==s_tSlavesDataResult.ucBusCurrValidNum || (fabs(s_atSlavesRealData[ucSn].fBatCur) > MIN_CURR_DET_BDCU)))
    {
        fTmpVol = fDischargeHopeVol + fCompVol - s_atSlavesRealData[ucSn].fBatVol + CalcVolDiffFromSOC(s_atSlavesRealData[ucSn].wBatHighSOC, *pwMaxBatSOC);
        *pfBoostVol = MIN(*pfBoostVol, fTmpVol);  
        ucVoltBoostCounter++;
    }

    for ( i = 0; i < SLAVE_NUM; i++ )
    {
        p = &s_atSlavesRealData[i];
        if ( (p->bExist) && (!p->bDischgPrt) && !p->bLoopOff && !p->bUpgrade && s_afBattVol[i]>42.0f)
        {
            fTmpVol = fDischargeHopeVol + fCompVol - p->fBatVol + CalcVolDiffFromSOC(p->wBatHighSOC, *pwMaxBatSOC);
            *pfBoostVol = MIN(*pfBoostVol, fTmpVol);
            ucVoltBoostCounter++;
        }
    }
    if (ucVoltBoostCounter == 0)
    {
        *pfBoostVol = fDischargeHopeVol + fCompVol - s_atSlavesRealData[ucSn].fBatVol;//兜底，从机使用主机电压抬升值
    }
}
#endif

FLOAT getChgVolt(FLOAT fChargeFullVol)
{
    BYTE i;
    FLOAT fMinBatVol, fChgVol;
    T_SlavesRealData *p = NULL;
    fMinBatVol = fChargeFullVol;
    for ( i = 0; i < SLAVE_NUM; i++ )
    {
        p = &s_atSlavesRealData[i];
        if ( (p->bExist) && (p->fBatCur > 1.0f) && (fMinBatVol > p->fBatVol) )
        {
            fMinBatVol = p->fBatVol;
        }
    }

    fChgVol = MIN(fMinBatVol + 0.5, fChargeFullVol);
    return fChgVol;
}

/*T_SlavesRealData* getSlavesRealData(BYTE ucSn)
{
    if(ucSn > SLAVE_NUM)
    {
        return NULL;
    }

    return &s_atSlavesRealData[ucSn];
}*/

void getSlavesRealData(T_SlavesRealData *ptSlavesRealData)
{
    rt_memcpy((BYTE *)ptSlavesRealData, (BYTE *)&s_atSlavesRealData, sizeof(T_SlavesRealData)*SLAVE_NUM);
    return;
}

void getSlavesDataResult(T_SlaveDataResult *ptSlavesDataResult)
{
    rt_memcpy((BYTE *)ptSlavesDataResult, (BYTE *)&s_tSlavesDataResult, sizeof(T_SlaveDataResult));
    return;
}

BOOLEAN isSingleBatt(void)
{
    return (IsMaster() && getSlaveNum() == 0);
}

BOOLEAN isExternalPowerOn(void)
{
    BYTE i;
    BOOLEAN bPowerOnFlag = FALSE;
    for (i = 0; i < SLAVE_NUM; i++)
    {
        if(s_atSlavesRealData[i].bExist)
        {
            if(s_atSlavesRealData[i].bLoopOff || s_atSlavesRealData[i].bUpgrade || s_atSlavesRealData[i].bBattLock || s_atSlavesRealData[i].ucBattType == 0
                || s_atSlavesRealData[i].bActivatePortCurrErr)
            {
                continue;
            }
            else if(s_atSlavesRealData[i].bExternalPowerOn)
            {
                bPowerOnFlag = TRUE;
            }
            else
            {
                return FALSE;
            }
        }
    }

    return bPowerOnFlag;
}

/***************************************************************************
 * @brief   判断是否存在外部有电状态为 是 的电池，只能主机判断，从机的状态由主机下发 
 * @return   
 **************************************************************************/
BOOLEAN IfExistExternalPowerOn(VOID)
{
    BYTE i = 0;
    for(i =0;i<SLAVE_NUM;i++)
    {
        if(s_atSlavesRealData[i].bExist && s_atSlavesRealData[i].bExternalPowerOn)
        {
            return True;
        }
    }
    
    return False;
}

BOOLEAN MultBattDataUnitest(BOOLEAN bFlag)
{

    if(bFlag == 1)
    {
        return True;
    }
    else
    {
        return False;
    }
}

BOOLEAN IsBattInSys(BYTE ucAddr)
{
    if(ucAddr == 0 || ucAddr > SLAVE_NUM)
    {
        return FALSE;
    }
        
    return s_atSlavesRealData[ucAddr-1].bBattInSys;
}


BOOLEAN IsConnectedToSnmpAgent(BYTE ucAddr)
{
    if (ucAddr == 0 || ucAddr > SLAVE_NUM)
    {
        return FALSE;
    }
    return s_atSlavesRealData[ucAddr - 1].bConnectedToSnmpAgent;
}



BOOLEAN GetMasterDischgProtect(void)
{
    BYTE ucSn = GetBMSAddr() - 1;
    // 确保ucSn在有效范围内，以避免数组越界
    if (ucSn < sizeof(s_atSlavesRealData) / sizeof(s_atSlavesRealData[0]))
    {
        return s_atSlavesRealData[ucSn].bDischgPrt;
    }
    // 如果ucSn无效，返回False
    return False;
}

