#include <rtthread.h>
#include <pin.h>
#include "sample.h"
#include "device_type.h"
#include "cmd.h"
#include "sps.h"
#include "msg.h"
#include "msg_id.h"
#include "utils_thread.h"
#include "utils_time.h"
#include "utils_string.h"
#include "realdata_save.h"
#include "realdata_id_in.h"
#include "alarm_id_in.h"
#include "alarm_manage.h"
#include "day_power.h"
#include "energy_accu.h"
#include "his_data.h"
#include "dev_dc_ac_modbus.h"
#include "led.h"
#include "device_num.h"
#include "partition_def.h"
#include "utils_data_type_conversion.h"
#include "utils_heart_beat.h"
#include "thread_id.h"
#include "his_record.h"
#include "utils_data_transmission.h"
#include "ee_public_info.h"

static void process_recv_msg(module_msg_t* msg_rcv);
static void send_start_dev_poll_msg();
static void handle_day_power_msg(_rt_msg_t curr_msg);
static void handle_accum_energy_period_msg(_rt_msg_t curr_msg);
static void handle_save_record_restart_msg(_rt_msg_t curr_msg);

static short save_on_grid_time_record(grid_time_record_t* data);
static void get_grid_start_time();
static void get_grid_end_time();
static void set_grid_data();

static dev_inst_t* dev_dc_ac;
static module_msg_t msg_rcv = {MOD_SAMPLE, MOD_SAMPLE,};
Static _rt_server_t g_sample_server = NULL;
unsigned short g_dev_status = 0xFFFF;

//并网
Static grid_time_record_t g_grid_time = {0};
static rt_timer_t g_grid_time_accu_timer = NULL;
Static time_t s_start_time = 0;             // 开始计时的时间
Static char s_grid_status_flag = FALSE;     //当前是否处于并网状态
Static int s_grid_time_count = 0;
static time_t s_init_time = 0;


static pv_sample_msg_handle_process_t s_msg_handle[] = {
    {DAY_POWER_MSG,      handle_day_power_msg},
    {ENERGY_ACCUM_MSG,   handle_accum_energy_period_msg},
    {ENERGY_SAVE_RESTART, handle_save_record_restart_msg},
    {CLEAN_HIS_ENERGY_MSG, handle_clean_his_energy_msg},
    {DELETE_HIS_ENERGY_MSG, handle_delete_his_energy_msg},
};

static msg_map sample_msg_map[] =
{
    {DAY_POWER_MSG,      msg_handle_nothing},
    {ENERGY_ACCUM_MSG,   msg_handle_nothing},
    {ENERGY_SAVE_RESTART, msg_handle_nothing},
    {CLEAN_HIS_ENERGY_MSG, msg_handle_nothing},
    {DELETE_HIS_ENERGY_MSG, msg_handle_nothing},
};

static inline void set_grid_data()
{
    unsigned int total_on_grid_time = g_grid_time.total_on_grid_time / (60*60);//总并网时间按照hour上送
    unsigned short day_on_grid_time = g_grid_time.day_on_grid_time / 60;
    set_one_data(DAC_DATA_ID_TOTAL_GRID_CONNECT_RUNNING_TIME, &(total_on_grid_time));
    set_one_data(DAC_DATA_ID_DAILY_GRID_CONNECT_RUNNING_TIME, &(day_on_grid_time));
}

int grid_time_init()
{
    if (SUCCESSFUL != handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char*)&g_grid_time, sizeof(g_grid_time), GRID_TIME_OFFSET))
    {
        LOG_E("read grid_time failed");
        return FALSE;
    }

    unsigned short check_sum = crc_cal((unsigned char*)&g_grid_time, sizeof(grid_time_record_t) - 2);
    if((g_grid_time.crc != check_sum) && (g_grid_time.crc == 0XFFFF))   // eeprom为初始值
    {
        g_grid_time.total_on_grid_time = 0;
        g_grid_time.day_on_grid_time = 0;
        g_grid_time.init_time = time(NULL);
        g_grid_time.crc = crc_cal((unsigned char*)&g_grid_time, sizeof(grid_time_record_t) - 2);
        handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&g_grid_time, sizeof(g_grid_time), GRID_TIME_OFFSET);
    }
    s_init_time = g_grid_time.init_time;
    set_grid_data();
    return TRUE;
}

void* init_sample(void * param)
{
    server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(sample_msg_map) / sizeof(msg_map);
    register_server_msg_map(sample_msg_map, server_info);

    //初始化消息队列
    init_msg_queue(MOD_SAMPLE);

    //启动设备收发线程
    dev_dc_ac = init_dev_inst(DEV_DC_AC);
    send_start_dev_poll_msg();

    // init_para_check(dev_dc_ac);
    init_day_power();
    init_grid_time_accu_timer();

    // 获取所有的实时数据
    send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, DC_AC_NUM, DC_AC_GET_REAL_DATA);
    rt_thread_mdelay(1000);  // 等待1s
    // 设置所有参数
    send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, DC_AC_NUM, DC_AC_SET_PARA_DATA);
    energy_accum_init();
    // 获取生产参数
    send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, DC_AC_NUM, DC_AC_GET_PRO_PARA_DATA);
    rt_thread_mdelay(1000);  // 等待1s

    if(g_dev_status == 0xFFFF)
    {
        get_one_data(DAC_DATA_ID_DEV_STATUS, &g_dev_status);
    }
    LOG_E("The state before the last device shutdown:%d", g_dev_status);
    grid_time_init();
    send_msg_to_thread(CHECK_DC_AC_PARA, MOD_SYS_MANAGE, NULL, 0);
    return dev_dc_ac;
}


int check_dev_status()
{
    unsigned short dev_status = 0;
    unsigned short last_status = 0, status = 0;
    char info[MAX_EVENT_INFO_LEN] = {0};
    get_one_data(DAC_DATA_ID_DEV_STATUS, &dev_status);

    if(g_dev_status == dev_status)
    {
        return SUCCESSFUL;
    }
    last_status = g_dev_status & 0xFF00;
    status = dev_status & 0xFF00;
    if(dev_status == FAULT_DEV_STATUS)
    {
        rt_snprintf(info, MAX_EVENT_INFO_LEN, "shutdown:fault");
        save_real_ctrl_cmd_event(DAC_DATA_ID_DEV_STATUS, REALDATA_ID_TYPE, info);
        g_dev_status = dev_status;
        return SUCCESSFUL;
    }

    if ((g_dev_status == NO_LIGHT && dev_status == LIGHT_DETECT) || 
        (g_dev_status == LIGHT_DETECT && dev_status == NO_LIGHT)) {
        // 更新全局设备状态
        g_dev_status = dev_status;
        
        // 状态切换成功返回标志
        return SUCCESSFUL;
    }

    rt_snprintf(info, MAX_EVENT_INFO_LEN, "devsta:%d->%d", g_dev_status, dev_status);
    g_dev_status = dev_status;
    if(last_status == status)
    {
        return SUCCESSFUL;
    }

    save_real_ctrl_cmd_event(DAC_DATA_ID_DEV_STATUS, REALDATA_ID_TYPE, info);           //保存操作记录
    return SUCCESSFUL;
}


void sample_main(void * param){
    PRINT_MSG_AND_RETURN_IF_FAIL(param != NULL);
    g_sample_server = _curr_server_get();
    pre_thread_beat_f(THREAD_SAMPLE);

    while (is_running(TRUE)){
        thread_beat_go_on(THREAD_SAMPLE);
        grid_led_deal();
        run_led_deal();
        alarm_led_deal();
        check_dev_status();
        grid_time_calcu();
        process_recv_msg(&msg_rcv);
        rt_thread_mdelay(50);
    }
}

static void send_start_dev_poll_msg(){
    send_msg_to_sps(START_DEV_POLL_MSG, MOD_DC_AC);

}

//发送消息给设备模块
void send_msg_to_sps(unsigned int msg_id, unsigned char dest){
    
    rt_msg_t msg = NULL;
    unsigned char mod_num = DC_AC_NUM;

    switch (msg_id)
    {
        case START_DEV_POLL_MSG:
            msg = rt_msg_create(START_DEV_POLL_MSG, &mod_num, sizeof(mod_num));
            break;

        case STOP_DEV_POLL_MSG:
            msg = rt_msg_create(START_DEV_POLL_MSG, NULL, 0);
            break;
    
        default:
            break;
    }
    RETURN_IF_FAIL(msg != NULL);
    rt_msg_send_event(msg);
}

static void handle_day_power_msg(_rt_msg_t curr_msg) 
{
    day_power_handle();
}

static void handle_accum_energy_period_msg(_rt_msg_t curr_msg) 
{
    accum_energy_period(dev_dc_ac);
}

static void handle_save_record_restart_msg(_rt_msg_t curr_msg) 
{
    save_record_restart();
}

void handle_clean_his_energy_msg(_rt_msg_t curr_msg) 
{
    clean_his_energy();    // 电量清除
    clean_grid_time();     // 并网运行时间清除
}


void handle_delete_his_energy_msg(_rt_msg_t curr_msg) 
{
    clean_his_energy();    // 电量清除
    storage_unlink(YEAR_FILE);
    storage_unlink(YEAR_FILE_TMP);
    storage_unlink(MONTH_FILE);
    storage_unlink(MONTH_FILE_TMP);
    storage_unlink(DAY_FILE);
    storage_unlink(DAY_FILE_TMP);
    storage_unlink(HOUR_FILE);
    storage_unlink(HOUR_FILE_TMP);
    storage_unlink(HIS_FILE);
    storage_unlink(HIS_BAK_FILE);
}


static void process_recv_msg(module_msg_t* msg_rcv)
{  
    int i = 0;
    if ((g_sample_server == NULL) || (rt_sem_take(&g_sample_server->msg_sem, 0) != RT_EOK) || (g_sample_server->msg_node == RT_NULL))
    {
        return;
    }
    _rt_msg_t curr_msg = g_sample_server->msg_node;
    for (i = 0; i < sizeof(s_msg_handle) / sizeof(s_msg_handle[0]); i++) {  
        if (s_msg_handle[i].msg_id == curr_msg->msg.msg_id) {
            s_msg_handle[i].handle(curr_msg);
        }
    }

    rt_mutex_take(&g_sample_server->mutex, RT_WAITING_FOREVER);
    g_sample_server->msg_node = curr_msg->next;
    g_sample_server->msg_count--;
    rt_mutex_release(&g_sample_server->mutex);

    softbus_free(curr_msg);
}


int init_grid_time_accu_timer(){
    char*  timer_name = "grid_time_accu";
    g_grid_time_accu_timer = rt_timer_create(timer_name, write_grid_time_period, NULL, SAVE_GRID_TIME_PERIOD, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
    if (g_grid_time_accu_timer == NULL)
    {
        LOG_E("rt_timer_create failed");
        return FAILURE;
    }
    return SUCCESSFUL;
}

static void get_grid_start_time() {
    s_start_time = time(NULL);
}

static void get_grid_end_time() {
    time_t end_time = time(NULL);
    uint64_t elapsed_time = 0;
    if(end_time < s_start_time){
        elapsed_time = 0;
    }else{
        elapsed_time = (unsigned int)(end_time - s_start_time);
    }
    g_grid_time.total_on_grid_time += elapsed_time;
    g_grid_time.day_on_grid_time += elapsed_time;
    s_start_time = time(NULL);
}

void write_grid_time_period(){
    time_t end_time = time(NULL);
    uint64_t elapsed_time = 0;
    if(end_time < s_start_time){
        elapsed_time = 0;
    }else{
        elapsed_time = (unsigned int)(end_time - s_start_time);
    }
    g_grid_time.total_on_grid_time += elapsed_time;//或者直接+SAVE_GRID_TIME_PERIOD
    g_grid_time.day_on_grid_time += elapsed_time;
    set_grid_data();
    s_grid_time_count++;
    if(s_grid_time_count == 5){
        save_on_grid_time_record(&g_grid_time);
        s_grid_time_count = 0;
    }
    s_start_time = time(NULL);
}

static short save_on_grid_time_record(grid_time_record_t* data)
{
    RETURN_VAL_IF_FAIL(data != NULL, FAILURE);
    data->init_time = time(NULL);
    data->crc = crc_cal((unsigned char*)data, sizeof(grid_time_record_t)- 2);
    return handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)data, sizeof(grid_time_record_t), GRID_TIME_OFFSET);
}

void grid_time_calcu()
{
    unsigned short dev_status = 0;

    get_one_data(DAC_DATA_ID_DEV_STATUS, &dev_status);
    if (dev_status == 0x0100 || (dev_status >= 0x0200 && dev_status <= 0x0207) || (dev_status >= 0x0401 && dev_status <= 0x0405)) //满足并网
    {
        if (!s_grid_status_flag)
        {
            get_grid_start_time();
            rt_timer_start(g_grid_time_accu_timer);
            s_grid_status_flag = TRUE;
        }
    }
    else
    {
        if (s_grid_status_flag)
        {
            get_grid_end_time();
            rt_timer_stop(g_grid_time_accu_timer);
            set_grid_data();
            save_on_grid_time_record(&g_grid_time);
            s_grid_status_flag = FALSE;
        }
    }
    //检查是否是新的一天
    if (is_new_day(&s_init_time))
    {
        if(s_grid_status_flag)
        {
            get_grid_end_time();
        }
        g_grid_time.day_on_grid_time = 0; //清除日并网时间
        set_grid_data();
        save_on_grid_time_record(&g_grid_time);
    }
}

int clean_grid_time()
{
    unsigned char buff[sizeof(grid_time_record_t)] = {0};
    unsigned int total_time = 0;
    unsigned short day_time = 0;
    rt_memset(&g_grid_time, 0x00, sizeof(g_grid_time));
    set_one_data(DAC_DATA_ID_TOTAL_GRID_CONNECT_RUNNING_TIME, &total_time);
    set_one_data(DAC_DATA_ID_DAILY_GRID_CONNECT_RUNNING_TIME, &day_time);

    rt_memset(buff, 0xff, sizeof(grid_time_record_t));
    return handle_storage(write_opr, EE_PUBLIC_INFO, buff, sizeof(grid_time_record_t), GRID_TIME_OFFSET);
}
