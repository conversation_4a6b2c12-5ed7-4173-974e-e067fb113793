/**
 * @file     device_type.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2017-04-19
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */

#ifndef _ACMU_PROTOCOL_1363_H
#define _ACMU_PROTOCOL_1363_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "device_type.h"

/*1363 protocol的命令唯一标识定义 */
#define ACMU_GET_INRELAY_INFO               1   ///<  获取输入干节点信息
#define ACMU_SET_INRELAY_INFO               2   ///<  设置输入干节点信息
#define ACMU_GET_FACTORY_INFO_CUSTOM        3   ///<  获取设备厂家信息自定义
#define ACMU_GET_ENV_ANALOG_INFO            4   ///<  获取环境模拟量信息
#define ACMU_REMOTE_CONTROL                 5   ///<  遥控
#define ACMU_GET_AC_ANALOG_INFO             6   ///<  获取交流模拟量信息
#define ACMU_GET_AC_SW_INPUT_STATUS         7   ///<  获取开关输入状态
#define ACMU_GET_SYS_TIME_1363              8   ///<  获取系统时间
#define ACMU_SET_SYS_TIME_1363              9   ///<  设置系统时间
#define ACMU_GET_SYSPARA                    10   ///<  获取参数（定点数）
#define ACMU_SET_SYSPARA                    11   ///<  设置参数（定点数）
#define ACMU_GET_ALMRELAY                   12   ///<  读取交流告警干接点
#define ACMU_SET_ALMRELAY                   13  ///<  设置交流告警干接点
#define ACMU_GET_ALMLEVEL                   14   ///<  读取交流告警干接点
#define ACMU_SET_ALMLEVEL                   15  ///<  设置交流告警干接点
#define ACMU_GET_CUSTOMPARA                 16  ///<  获取自定义参数（定点数）
#define ACMU_SET_CUSTOMPARA                 17  ///<  获取自定义参数（定点数）
#define ACMU_GET_AC_ALARM_INFO              18  ///<  获取交流配电告警信息
#define ACMU_GET_ENV_ALARM_INFO             19  ///<  获取环境监控告警信息
#define ACMU_GET_HIS_ALARM                  20  ///<  获取历史告警
#define ACMU_GET_ENVPARA                    21  ///<  获取环境参数（定点数）
#define ACMU_SET_ENVPARA                    22  ///<  设置环境参数（定点数）
#define ACMU_GET_ENVALMLEVEL                23  ///<  获取环境告警级别
#define ACMU_SET_ENVALMLEVEL                24  ///<  设置环境告警级别
#define ACMU_GET_ENVALMRELAY                25  ///<  获取环境告警干接点
#define ACMU_SET_ENVALMRELAY                26  ///<  设置环境告警干接点
#define ACMU_GET_BOOT_VERSION               27  ///<  获取boot版本号及版本日期
/* cid2 功能码定义 */
#define CMD_GET_INRELAY_INFO                    0x87    ///<  获取输入干节点信息
#define CMD_SET_INRELAY_INFO                    0x88    ///<  设置输入干节点信息
#define CMD_GET_FACTORY_INFO_CUSTOM             0xE1    ///<  获取设备厂家信息自定义
#define CMD_GET_ENV_ANALOG_INFO                 0x42    ///<  获取环境模拟量信息
#define CMD_REMOTE_CONTROL                      0x45    ///<  遥控
#define CMD_GET_AC_ANALOG_INFO                  0x42    ///<  获取交流模拟量信息
#define CMD_GET_AC_SW_INPUT_STATUS              0x43    ///<  获取开关输入状态
#define CMD_GET_AC_ALARM_INFO                   0x44    ///<  获取交流配电告警信息
#define CMD_GET_ENV_ALARM_INFO                  0x44    ///<  获取环境监控告警信息
#define CMD_GET_SYS_TIME                        0x4D    ///<  获取系统时间
#define CMD_SET_SYS_TIME                        0x4E    ///<  设置系统时间
#define CMD_GET_SYSPARA                         0x47    ///<  获取参数（定点数）
#define CMD_SET_SYSPARA                         0x49    ///<  设置参数（定点数）
#define CMD_GET_ALMLEVEL                        0x81    ///<  读取交流告警级别
#define CMD_SET_ALMLEVEL                        0x82    ///<  设置交流告警级别
#define CMD_GET_ALMRELAY                        0x83    ///<  读取交流告警干接点
#define CMD_SET_ALMRELAY                        0x84    ///<  设置交流告警干接点
#define CMD_GET_CUSTOMPARA                      0x85    ///<  获取自定义参数（定点数）
#define CMD_SET_CUSTOMPARA                      0x86    ///<  获取自定义参数（定点数）
#define CMD_GET_HIS_ALARM                       0x4C    ///<  获取历史告警
#define CMD_GET_BOOT_VERSION                    0x52    ///<  获取boot版本号及版本日期


/* 厂家信息相关宏定义 */
#define SM_NAME_LEN                 30      ///<  SM名称长度
#define SM_FACTORY_NAME_LEN         20      ///<  厂家名称长度
#define SM_SOFT_VER_LEN             12      ///<  SM软件版本长度
#define SM_SOFT_RELEASE_DATE_LEN    4       ///<  软件发布日期长度
#define SM_HARDWARE_VERSION_LEN     21      ///<  硬件版本号长度
#define SM_SERIAL_NUMBER_LEN        21      ///<  序列号长度
#define SM_RESERVED_LEN             15      ///<  预留字节长度

#define PARSE_PARA                  1   // 解析参数信息
#define PARSE_ALM                   2   // 解析告警信息

#define GROUP_TYPE_SEND_NORMAL    0x00     //正常发送
#define GROUP_TYPE_SEND_LAST      0x01     //最后一包

#define GROUP_TYPE_RCV_START    0x00     //接收第一包
#define GROUP_TYPE_RCV_NEXT     0x01     //接收下一包
#define GROUP_TYPE_RCV_ERROR    0x02     //接收错误，重发
#define GROUP_TYPE_RCV_END      0x03     //接收结束

#define BOOT_VER_LEN            16  // boot版本最大长度

#define MENUKEY_LEN             4   // 菜单口令长度

typedef union { 
    short t_data;
    unsigned char uc_data; 
    char c_data; 
} acmu_para_value;

typedef struct {
    unsigned char  command_type;
    unsigned short sid; 
    unsigned char  data_type;
    unsigned short array_len;
    unsigned char  precision;
}acmu_para_info;

dev_type_t* init_dev_acmu_1363(void);
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _ACMU_PROTOCOL_1363_H
