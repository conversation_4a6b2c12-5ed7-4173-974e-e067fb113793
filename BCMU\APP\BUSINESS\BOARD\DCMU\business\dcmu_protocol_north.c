#include <string.h>
#include <stdlib.h>
#include <rtthread.h>
#include "protocol_layer.h"
#include "dev_dcmu.h"
#include "his_record.h"
#include "MIBTable.h"
#include "sps.h"
#include "realdata_id_in.h"
#include "alarm_id_in.h"
#include "para_id_in.h"
#include "para_manage.h"
#include "realdata_save.h"
#include "protocol_north_comm.h"
#include "dcmu_protocol_north.h"
#include "alarm_manage.h"
#include "pdt_version.h"
#include "utils_time.h"
#include "const_define_in.h"
#include "utils_data_transmission.h"
#include "utils_rtthread_security_func.h"
#include "utils_data_type_conversion.h"
#include "gui_data_interface.h"

Static int pack_real_data(void* dev_inst, void *cmd_buf);
Static int pack_dcmu_comm_para(void* dev_inst, void *cmd_buf);
Static int parse_dcmu_comm_para(void* dev_inst, void *cmd_buf);
Static int pack_dcmu_extend_para(void* dev_inst, void *cmd_buf);
Static int parse_dcmu_extend_para(void* dev_inst, void *cmd_buf);
Static int pack_comm_func(void* dev_inst, void *cmd_buf);
Static int get_dcem_show_data(dcem_show_data_t*  dcem_show_data);
Static unsigned short cal_common_para_crc(void);
Static unsigned short cal_extra_para_crc(void);
Static int parse_dcmu_real_alarm(void* dev_inst, void *cmd_buf);
Static int pack_dcmu_real_alarm(void* dev_inst, void *cmd_buf);
Static int ctrl_cmd(void* dev_inst, void *cmd_buf);
Static int parse_his_alarm(void* dev_inst, void *cmd_buf);
Static int pack_his_alarm(void* dev_inst, void *cmd_buf);
Static int pack_factory_info(void* dev_inst, void *cmd_buf);
Static int parse_set_time(void* dev_inst, void *cmd_buf);
Static int parse_set_specific_para(void* dev_inst, void *cmd_buf);

Static rt_timer_t s_reset_delay = NULL;
Static unsigned char s_command_type = 0x00;

Static unsigned int s_dcmu_alarm_id[] RAM_SECTION = {
    GET_ALM_ID(DCMU_ALM_ID_DC_VOLT_HIGH , 1 , 1 ),                   //直流电压高
    GET_ALM_ID(DCMU_ALM_ID_DC_VOLT_LOW , 1 , 1 ),                    //直流电压低
    GET_ALM_ID(DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN , 1 , 1 ),         //直流负载回路断
    GET_ALM_ID(DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN , 2 , 1 ),         //直流负载回路断
    GET_ALM_ID(DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN , 3 , 1 ),         //直流负载回路断
    GET_ALM_ID(DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN , 4 , 1 ),         //直流负载回路断
    GET_ALM_ID(DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN , 5 , 1 ),         //直流负载回路断
    GET_ALM_ID(DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN , 6 , 1 ),         //直流负载回路断
    GET_ALM_ID(DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN , 7 , 1 ),         //直流负载回路断
    GET_ALM_ID(DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN , 8 , 1 ),         //直流负载回路断
    GET_ALM_ID(DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN , 9 , 1 ),         //直流负载回路断
    GET_ALM_ID(DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN , 10 , 1 ),         //直流负载回路断
    GET_ALM_ID(DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN , 11 , 1 ),         //直流负载回路断
    GET_ALM_ID(DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN , 12 , 1 ),         //直流负载回路断
    GET_ALM_ID(DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN , 13 , 1 ),         //直流负载回路断
    GET_ALM_ID(DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN , 14 , 1 ),         //直流负载回路断
    GET_ALM_ID(DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN , 15 , 1 ),         //直流负载回路断
    GET_ALM_ID(DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN , 16 , 1 ),         //直流负载回路断
    GET_ALM_ID(DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN , 17 , 1 ),         //直流负载回路断
    GET_ALM_ID(DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN , 18 , 1 ),         //直流负载回路断
    GET_ALM_ID(DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN , 19 , 1 ),         //直流负载回路断
    GET_ALM_ID(DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN , 20 , 1 ),         //直流负载回路断
    GET_ALM_ID(DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN , 21 , 1 ),         //直流负载回路断
    GET_ALM_ID(DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN , 22 , 1 ),         //直流负载回路断
    GET_ALM_ID(DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN , 23 , 1 ),         //直流负载回路断
    GET_ALM_ID(DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN , 24 , 1 ),         //直流负载回路断
    GET_ALM_ID(DCMU_ALM_ID_DC_LIGHTNING_PROTECTOR_DAMAGED , 1 , 1 ), //直流防雷器损坏
    GET_ALM_ID(DCMU_ALM_ID_BATTERY_VOLTAGE_LOW , 1 , 1 ),             //电池电压低
    GET_ALM_ID(DCMU_ALM_ID_BATTERY_VOLTAGE_LOW , 2, 1 ),              //电池电压低
    GET_ALM_ID(DCMU_ALM_ID_BATTERY_VOLTAGE_LOW , 3 , 1 ),             //电池电压低
    GET_ALM_ID(DCMU_ALM_ID_BATTERY_VOLTAGE_LOW , 4 , 1 ),             //电池电压低
    GET_ALM_ID(DCMU_ALM_ID_BATTERY_VOLTAGE_TOO_LOW , 1 , 1 ),         //电池电压过低
    GET_ALM_ID(DCMU_ALM_ID_BATTERY_VOLTAGE_TOO_LOW , 2 , 1 ),         //电池电压过低
    GET_ALM_ID(DCMU_ALM_ID_BATTERY_VOLTAGE_TOO_LOW , 3 , 1 ),         //电池电压过低
    GET_ALM_ID(DCMU_ALM_ID_BATTERY_VOLTAGE_TOO_LOW , 4 , 1 ),         //电池电压过低
    GET_ALM_ID(DCMU_ALM_ID_ABNORMAL_BATTERY_CURRENT , 1 , 1 ),        //电池电流异常
    GET_ALM_ID(DCMU_ALM_ID_ABNORMAL_BATTERY_CURRENT , 2 , 1 ),        //电池电流异常
    GET_ALM_ID(DCMU_ALM_ID_ABNORMAL_BATTERY_CURRENT , 3 , 1 ),        //电池电流异常
    GET_ALM_ID(DCMU_ALM_ID_ABNORMAL_BATTERY_CURRENT , 4 , 1 ),        //电池电流异常
    GET_ALM_ID(DCMU_ALM_ID_BATTERY_TEMPERATURE_HIGH , 1 , 1 ),        //电池温度高
    GET_ALM_ID(DCMU_ALM_ID_BATTERY_TEMPERATURE_HIGH , 2 , 1 ),        //电池温度高
    GET_ALM_ID(DCMU_ALM_ID_BATTERY_TEMPERATURE_HIGH , 3 , 1 ),        //电池温度高
    GET_ALM_ID(DCMU_ALM_ID_BATTERY_TEMPERATURE_HIGH , 4 , 1 ),        //电池温度高
    GET_ALM_ID(DCMU_ALM_ID_BATTERY_TEMPERATURE_LOW , 1 , 1 ),         //电池温度低
    GET_ALM_ID(DCMU_ALM_ID_BATTERY_TEMPERATURE_LOW , 2 , 1 ),         //电池温度低
    GET_ALM_ID(DCMU_ALM_ID_BATTERY_TEMPERATURE_LOW , 3 , 1 ),         //电池温度低
    GET_ALM_ID(DCMU_ALM_ID_BATTERY_TEMPERATURE_LOW , 4 , 1 ),         //电池温度低
    GET_ALM_ID(DCMU_ALM_ID_BATTERY_LOOP_BROKEN , 1 , 1 ),             //电池回路断
    GET_ALM_ID(DCMU_ALM_ID_BATTERY_LOOP_BROKEN , 2 , 1 ),             //电池回路断
    GET_ALM_ID(DCMU_ALM_ID_BATTERY_LOOP_BROKEN , 3 , 1 ),             //电池回路断
    GET_ALM_ID(DCMU_ALM_ID_BATTERY_LOOP_BROKEN , 4 , 1 ),             //电池回路断
    GET_ALM_ID(DCMU_ALM_ID_BATTERY_DISCHARGE , 1 , 1 ),                //蓄电池放电
    GET_ALM_ID(DCMU_ALM_ID_BATTERY_DISCHARGE , 2 , 1 ),                //蓄电池放电
    GET_ALM_ID(DCMU_ALM_ID_BATTERY_DISCHARGE , 3 , 1 ),                //蓄电池放电
    GET_ALM_ID(DCMU_ALM_ID_BATTERY_DISCHARGE , 4 , 1 ),                //蓄电池放电
    GET_ALM_ID(DCMU_ALM_ID_INVALID_BATTERY_TEMPERATURE , 1 , 1 ),      //电池温度无效
    GET_ALM_ID(DCMU_ALM_ID_INVALID_BATTERY_TEMPERATURE , 2 , 1 ),      //电池温度无效
    GET_ALM_ID(DCMU_ALM_ID_INVALID_BATTERY_TEMPERATURE , 3 , 1 ),      //电池温度无效
    GET_ALM_ID(DCMU_ALM_ID_INVALID_BATTERY_TEMPERATURE , 4 , 1 ),      //电池温度无效
    GET_ALM_ID(DCMU_ALM_ID_BATTERY_TEMPERATURE_TOO_HIGH , 1 , 1 ),     //电池温度过高
    GET_ALM_ID(DCMU_ALM_ID_BATTERY_TEMPERATURE_TOO_HIGH , 2 , 1 ),     //电池温度过高
    GET_ALM_ID(DCMU_ALM_ID_BATTERY_TEMPERATURE_TOO_HIGH , 3 , 1 ),     //电池温度过高
    GET_ALM_ID(DCMU_ALM_ID_BATTERY_TEMPERATURE_TOO_HIGH , 4 , 1 ),     //电池温度过高
    GET_ALM_ID(DCMU_ALM_ID_DC_VOLTAGE_TOO_LOW , 1 , 1 ),               //直流电压过低
    GET_ALM_ID(DCMU_ALM_ID_DC_VOLTAGE_TOO_HIGH , 1 , 1 ),              //直流电压过高
};

static unsigned char cmd_req[] RAM_SECTION = 
{
    ORDER_GET,                  //0
    ORDER_GETPARA,              //1 
    ORDER_SETPARA,              //2
    ORDER_GET_DC_ALARM_PARA,    //3
    ORDER_SET_DC_ALARM_PARA,    //4
    ORDER_GETREALALM,           //5
    ORDER_CTRL,                 //6
    ORDER_GETHISALM,            //7
    ORDER_GETFACTINFO,          //8
    ORDER_SETTIME,              //9
    ORDER_SETSPEPARA,          //10
};

static unsigned char cmd_ack[] RAM_SECTION = 
{
    ORDER_GET,                 //0
    ORDER_GETPARA,             //1 
    ORDER_SETPARA,             //2
    ORDER_GET_DC_ALARM_PARA,   //3
    ORDER_SET_DC_ALARM_PARA,   //4
    ORDER_GETREALALM,          //5
    ORDER_CTRL,                //6
    ORDER_GETHISALM,           //7
    ORDER_GETFACTINFO,          //8
    ORDER_SETTIME,              //9
    ORDER_SETSPEPARA,          //10
};


static cmd_t no_poll_cmd_tab[] = 
{
    {DCMU_NORTH_GET_DATA, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(unsigned char),   NULL, NULL, pack_real_data, NULL,},
    {DCMU_NORTH_GET_COMM_PARA, CMD_PASSIVE, &cmd_req[1], &cmd_ack[1], sizeof(unsigned char),   NULL, NULL, pack_dcmu_comm_para, NULL,},
    {DCMU_NORTH_SET_COMM_PARA, CMD_PASSIVE, &cmd_req[2], &cmd_ack[2], sizeof(unsigned char),   NULL, NULL, pack_comm_func, parse_dcmu_comm_para,},
    {DCMU_NORTH_GET_EXT_PARA, CMD_PASSIVE, &cmd_req[3], &cmd_ack[3], sizeof(unsigned char),   NULL, NULL, pack_dcmu_extend_para, NULL,},
    {DCMU_NORTH_SET_EXT_PARA, CMD_PASSIVE, &cmd_req[4], &cmd_ack[4], sizeof(unsigned char),   NULL, NULL, pack_comm_func, parse_dcmu_extend_para,},
    {DCMU_NORTH_GET_ALARM, CMD_PASSIVE, &cmd_req[5], &cmd_ack[5], sizeof(unsigned char),   NULL, NULL, pack_dcmu_real_alarm, parse_dcmu_real_alarm,},
    {DCMU_NORTH_CTRL_CMD, CMD_PASSIVE, &cmd_req[6], &cmd_ack[6], sizeof(unsigned char),   NULL, NULL, pack_comm_func, ctrl_cmd,},
    {DCMU_NORTH_GET_HIS_ALM, CMD_PASSIVE, &cmd_req[7], &cmd_ack[7], sizeof(unsigned char),   NULL, NULL, pack_his_alarm, parse_his_alarm,},
    {DCMU_NORTH_GET_FAC, CMD_PASSIVE, &cmd_req[8], &cmd_ack[8], sizeof(unsigned char),   NULL, NULL, pack_factory_info, NULL,},
    {DCMU_NORTH_SET_TIME, CMD_BROADCAST, &cmd_req[9], &cmd_ack[9], sizeof(unsigned char),   NULL, NULL, NULL, parse_set_time,},
    {DCMU_NORTH_SET_SPEC_PARA, CMD_PASSIVE, &cmd_req[10], &cmd_ack[10], sizeof(unsigned char),   NULL, NULL, pack_comm_func, parse_set_specific_para,},
    {0,},
};

Static dev_type_t dev_dcmu_north = 
{
    DEV_CSU, 1, PROTOCOL_NORTH_COMM, LINK_DCMU_NORTH, R_BUFF_LEN, S_BUFF_LEN, 0, &no_poll_cmd_tab[0],
};

dev_type_t* init_dev_dcmu_north(void){
    return &dev_dcmu_north;
}


Static int parse_set_specific_para(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    unsigned char* buff;
    unsigned char cid1,cid2 = 0;
    unsigned char value = 0;
    unsigned char set_value = 0;
    char rst = 0;
    unsigned short debug_cmd_cnt = 0;
    
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf_temp != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(cmd_buf_temp->buf != NULL, FAILURE);

    get_one_data(DCMU_DATA_ID_SET_SPEC_PARA_CNT, &debug_cmd_cnt);
    debug_cmd_cnt++;
    set_one_data(DCMU_DATA_ID_SET_SPEC_PARA_CNT, &debug_cmd_cnt);

    buff = cmd_buf_temp->buf;

    cid1 = buff[0];
    cid2 = buff[1];
    value = buff[3];

    if(cid1 != ID1_DCMU_PARA || cid2 != ID2_PR_BATSETUP || value > 0x0F)
    {
        cmd_buf_temp->rtn = RTNRS485_INVLDATA;
        return SUCCESSFUL;
    }

    for(int i=0; i<BATT_NUM; i++)
    {
        set_value = (value>>i)&0x01;
        rst |= set_one_para(DCMU_PARA_ID_BATTERY_CONFIG_OFFSET+i, &set_value, TRUE, TRUE);
    }

    if(rst<0)
    {
        cmd_buf_temp->rtn = RTNRS485_INVLDATA;
    }

    return SUCCESSFUL;
}



Static int pack_factory_info(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    unsigned char* buff;
    unsigned int offset = 0;
    char sm_name[LEN_SMNAME] = {0};
    char reserved[LEN_RESERVED] = {0};
    unsigned short debug_cmd_cnt = 0;

    unsigned char data_flag1 = 0;
    unsigned char data_flag2 = 0;

    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf_temp != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(cmd_buf_temp->buf != NULL, FAILURE);

    get_one_data(DCMU_DATA_ID_GET_FACT_INFO_CNT, &debug_cmd_cnt);
    debug_cmd_cnt++;
    set_one_data(DCMU_DATA_ID_GET_FACT_INFO_CNT, &debug_cmd_cnt);
    
    buff = cmd_buf_temp->buf;

    offset++;         //长度占位

    get_one_data(DCMU_DATA_ID_DATA_FLAG1, &data_flag1);
    buff[offset++] = data_flag1 | FLAG1_E | FLAG1_F;         // data_flag1;
    
    get_one_data(DCMU_DATA_ID_DATA_FLAG2, &data_flag2);
    data_flag2 &= ~FLAG2_RST;
    set_one_data(DCMU_DATA_ID_DATA_FLAG2, &data_flag2);
    buff[offset++] = data_flag2;                             // data_flag2;

    rt_memset_s(sm_name, LEN_SMNAME, 0x20, LEN_SMNAME);
    rt_memcpy_s(sm_name, LEN_SMNAME, SOFTWARE_NAME, rt_strnlen_s(SOFTWARE_NAME, LEN_SMNAME));
    rt_memcpy_s(&buff[offset], LEN_SMNAME, &sm_name[0], LEN_SMNAME);
    offset += LEN_SMNAME;

    buff[offset++] = SOFTWARE_VER1;                           // 软件主版本
    buff[offset++] = SOFTWARE_VER2;                           // 软件副版本
    buff[offset++] = SOFTWARE_VER3;                           // 软件子版本

    buff[offset++] = (unsigned char)(SOFTWARE_DATE_YEAR >> 8);   // 年份高字节
    buff[offset++] = (unsigned char)(SOFTWARE_DATE_YEAR & 0xFF); // 年份低字节
    buff[offset++] = SOFTWARE_DATE_MONTH;                        // 月
    buff[offset++] = SOFTWARE_DATE_DAY;                          // 日

    rt_memcpy_s(&buff[offset], LEN_RESERVED, &reserved, LEN_RESERVED);             // 预留
    offset += LEN_RESERVED;

    buff[0] = offset - 3;                     //长度
    ((cmd_buf_t*)cmd_buf)->data_len = offset;
    return SUCCESSFUL;
}

Static int parse_set_time(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    unsigned char* buff;
    unsigned int offset = 0;
    char info[20] = {0};
    time_base_t last_tm = {0},tm = {0};
    unsigned char data_flag1 = 0;
    unsigned short debug_cmd_cnt = 0;
    
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf_temp != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(cmd_buf_temp->buf != NULL, FAILURE);

    get_one_data(DCMU_DATA_ID_SET_TIME_CNT, &debug_cmd_cnt);
    debug_cmd_cnt++;
    set_one_data(DCMU_DATA_ID_SET_TIME_CNT, &debug_cmd_cnt);

    time_t tTime = time(RT_NULL);
    time_t_to_timestruct(tTime,&last_tm);
    buff = cmd_buf_temp->buf;

    tm.year = get_int16_data(&buff[offset]);
    offset+=2;
    tm.month = buff[offset++];
    tm.day = buff[offset++];
    tm.hour = buff[offset++];
    tm.minute = buff[offset++];
    tm.second = buff[offset++];
    if( check_time_range(tm) == FAILURE )
    {
        cmd_buf_temp->rtn = RTNRS485_INVLDATA;
        return SUCCESSFUL;
    }

    get_one_data(DCMU_DATA_ID_DATA_FLAG1, &data_flag1);
    data_flag1 &= ~FLAG1_TIMECH;
    set_one_data(DCMU_DATA_ID_DATA_FLAG1, &data_flag1);

    /* 设置系统时间 */
    if (set_system_time(&tm) != SUCCESSFUL) {
        cmd_buf_temp->rtn = RTNRS485_INVLDATA;
        return SUCCESSFUL;
    }

    rt_snprintf(info, sizeof(info),
                "%d-%02d-%02d %02d:%02d:%02d", 
               last_tm.year, last_tm.month, last_tm.day, last_tm.hour, last_tm.minute, last_tm.second);
    pub_hisaction_save_msg(ID1_DCMU_SETSTATUS, ID2_PR_TIME, 0, info);

    return SUCCESSFUL;
}




Static int get_dcem_show_data(dcem_show_data_t* dcem_show_data) {
    RETURN_VAL_IF_FAIL(dcem_show_data != NULL, FAILURE);
    int i = 0;

    get_one_data(DCMU_DATA_ID_DC_OUTPUT_VOLTAGE, &dcem_show_data->dc_output_voltage);
    get_one_data(DCMU_DATA_ID_TOTAL_LOAD_CURRENT, &dcem_show_data->total_load_current);
    for(i=0; i<BATT_NUM; i++){
        get_one_data(DCMU_DATA_ID_BATTERY_VOLTAGE + i, &dcem_show_data->battery_voltage[i]);
        get_one_data(DCMU_DATA_ID_BATTERY_CURRENT + i, &dcem_show_data->battery_current[i]);
        get_one_data(DCMU_DATA_ID_BATTERY_TEMPERATURE + i, &dcem_show_data->battery_temperature[i]);
    }
    for(i=0; i<SWITCH_NUM; i++)
    {
        get_one_data(DCMU_DATA_ID_LOAD_CURRENT+i, &dcem_show_data->branch_current[i]);
        get_one_data(DCMU_DATA_ID_LOAD_BATTERY+i, &dcem_show_data->branch_battery[i]);
    }

    return SUCCESSFUL;
}

// 计算公共参数的CRC
Static unsigned short cal_common_para_crc(void) {
    unsigned short para_crc = 0;
    char *p = NULL;
    char acBuff[40] = {0};
    T_SysPara tSysPara;

    GetSysPara(PARATYPE_ALL, (char*)&tSysPara);
    p = acBuff;

    put_int16_to_buff((unsigned char *)p, tSysPara.tDcPara1.iDcVolMax);
    p += 2;

    put_int16_to_buff((unsigned char *)p, tSysPara.tDcPara1.iDcVolMin);
    p += 2;

    put_int16_to_buff((unsigned char *)p, tSysPara.tDcPara1.iBattVolLow);
    p += 2;

    put_int16_to_buff((unsigned char *)p, tSysPara.tDcPara1.iBattVolTooLow);
    p += 2;

    *p++ = tSysPara.tDcPara1.ucBattCurrFault;
    *p++ = tSysPara.tDcPara1.scBattTempMax;
    *p++ = tSysPara.tDcPara1.scBattTempMin;

    put_int16_to_buff((unsigned char *)p, tSysPara.tDcPara1.iFuseValveVol);
    p += 2;

    put_int16_to_buff((unsigned char *)p, tSysPara.tDcPara2.aiBattDisCharge);
    p += 2;

    *p++ = tSysPara.tAlarmGradePara.aucAlmGrade[1]; // 直流电压高告警级别
    *p++ = tSysPara.tAlarmGradePara.aucAlmGrade[2]; //直流电压低告警级别
    *p++ = tSysPara.tAlarmGradePara.aucAlmGrade[3]; // 直流负载回路断告警级别
    *p++ = tSysPara.tAlarmGradePara.aucAlmGrade[4]; // 直流防雷器损坏告警级别
    *p++ = tSysPara.tAlarmGradePara.aucAlmGrade[5]; // 电池电压低告警级别
    *p++ = tSysPara.tAlarmGradePara.aucAlmGrade[6]; // 电池电压过低告警级别
    *p++ = tSysPara.tAlarmGradePara.aucAlmGrade[7]; // 电池电流异常告警级别
    *p++ = tSysPara.tAlarmGradePara.aucAlmGrade[8]; // 电池温度高告警级别
    *p++ = tSysPara.tAlarmGradePara.aucAlmGrade[9]; // 电池温度低告警级别
    *p++ = tSysPara.tAlarmGradePara.aucAlmGrade[10]; // 电池回路断告警级别
    *p++ = tSysPara.tAlarmGradePara.aucAlmGrade[11]; // 蓄电池放电告警级别
    *p++ = tSysPara.tAlarmGradePara.aucAlmGrade[12]; // 电池温度失效告警级别

    para_crc = crc_cal((unsigned char *)acBuff, (unsigned short)(p - acBuff));
    set_one_data(DCMU_DATA_ID_OLD_PARA_CRC, &para_crc);

    return para_crc;
}

// 计算扩展参数的CRC
Static unsigned short cal_extra_para_crc(void) {
    unsigned short para_crc = 0;
    char *p = NULL;
    char acBuff[40] = {0};
    T_SysPara tSysPara;

    GetSysPara(PARATYPE_ALL, (char*)&tSysPara);
    p = acBuff;

    *p++ = tSysPara.tDcPara3.scBatTempExMax; // 电池温度过高阈值

    put_int16_to_buff((unsigned char *)p, tSysPara.tDcPara3.aiDcVoltExLow); // 直流电压过低阈值
    p += 2;

    put_int16_to_buff((unsigned char *)p, tSysPara.tDcPara3.aiDcVoltExHigh); // 直流电压过高阈值
    p += 2;

    *p++ = tSysPara.tAlarmGradePara.aucAlmGrade[13]; // 电池温度过高告警级别
    *p++ = tSysPara.tAlarmGradePara.aucAlmGrade[14]; // 直流电压过低告警级别
    *p++ = tSysPara.tAlarmGradePara.aucAlmGrade[15]; // 直流电压过高告警级别

    para_crc = crc_cal((unsigned char *)acBuff, (unsigned short)(p - acBuff));
    set_one_data(DCMU_DATA_ID_PARA_CRC, &para_crc);

    return para_crc;
}

int pack_real_data(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    unsigned char* buff;
    unsigned int offset = 0;
    unsigned int ul_data = 0;
    unsigned char uc_data = 0;
    unsigned char uc_batt_data = 0;
    unsigned short para_crc = 0;
    dcem_show_data_t dcem_show_data;
    int i = 0;
    unsigned char data_flag1 = 0;
    unsigned char data_flag2 = 0;
    float f_data = 0.0f;
    unsigned short debug_cmd_cnt = 0;

    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf_temp != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(cmd_buf_temp->buf != NULL, FAILURE);

    get_one_data(DCMU_DATA_ID_REC_CALL_CMD_CNT, &debug_cmd_cnt);
    debug_cmd_cnt++;
    set_one_data(DCMU_DATA_ID_REC_CALL_CMD_CNT, &debug_cmd_cnt);

    buff = cmd_buf_temp->buf;

    get_dcem_show_data(&dcem_show_data);

    offset++; // 长度占位

    get_one_data(DCMU_DATA_ID_DATA_FLAG1, &data_flag1);
    data_flag1 = data_flag1 | FLAG1_E | FLAG1_F;
    buff[offset++] = data_flag1; // data_flag1;

    get_one_data(DCMU_DATA_ID_DATA_FLAG2, &data_flag2);
    buff[offset++] = data_flag2; // data_flag2

    put_int16_to_buff(&buff[offset], round(dcem_show_data.dc_output_voltage * pow(10,1))); // 直流输出电压
    offset += 2;

    put_int16_to_buff(&buff[offset], round(dcem_show_data.total_load_current * pow(10,1))); // 负载总电流
    offset += 2;

    buff[offset++] = 4; // 监测电池分路数量 M = 4
    for(i = 0; i < BATT_NUM; i++){
        put_int16_to_buff(&buff[offset], round(dcem_show_data.battery_voltage[i] * pow(10,1))); // 电池电压1
        offset += 2;

        put_int16_to_buff(&buff[offset], round(dcem_show_data.battery_current[i]* pow(10,1))); // 电池电流1
        offset += 2;

        buff[offset++] = (char)dcem_show_data.battery_temperature[i]; // 电池温度1
    }

    buff[offset++] = LOAD_NUM; // 监测直流分路数量 N = 24
    for(i=0; i < LOAD_NUM; i++)
    {
        put_int16_to_buff(&buff[offset], round(dcem_show_data.branch_current[i] * pow(10,1))); // 分路(负载)电流
        offset += 2;
    }

    buff[offset++] = 103; // 预留模拟量字节数 S = 103

    para_crc = cal_common_para_crc();
    put_uint16_to_buff(&buff[offset], para_crc); // 公共参数crc（与旧代码完全保持一致）
    offset += 2;

    get_one_data(DCMU_DATA_ID_TOTAL_ELECTRIC_QUANTITY, &f_data);
    put_uint32_to_buff(&buff[offset], f_data * pow(10,2));
    offset += 4;

    buff[offset++] = LOAD_NUM; // 监测直流分路电量数量 N = 24

    for(i = 0; i < LOAD_NUM; i++)
    {
        put_uint32_to_buff(&buff[offset], dcem_show_data.branch_battery[i] * pow(10,2)); // 分路(负载)电量
        offset += 4;
    }

    buff[offset++] = 5; // 预留配置参数字节数 S = 5

    // 获取24路负载配置状态
    for (i = 0; i < LOAD_NUM; i++)
    {
        get_one_para(DCMU_PARA_ID_LOAD_CONFIG_OFFSET + i, &uc_data); // 负载配置
        ul_data |= (uc_data != 0 ? 1 : 0) << i; // 负载配置状态有多种情况，上传只有配置或者未配置
    }
    put_uint32_to_buff(&buff[offset], ul_data);
    offset += 4;

    for(i=0; i < RELAY_NUM; i++)
    {
        get_one_para(DCMU_PARA_ID_BATTERY_CONFIG_OFFSET + i, &uc_data);//电池组配置
        uc_batt_data |= (uc_data & 0x01) << i;
    }
    buff[offset++] = uc_batt_data;

    buff[offset++] = RELAY_NUM; // 输入干接点状态
    for (i = 0; i < RELAY_NUM; i++) {
        get_one_data(DCMU_DATA_ID_INPUT_RELAY + i, &uc_data);
        buff[offset++] = uc_data; // DCMU输入干结点
    }

    buff[offset++] = 2; // 预留状态量字节数 N = 2

    para_crc = cal_extra_para_crc();
    put_uint16_to_buff(&buff[offset], para_crc); // 扩展参数crc（与旧代码完全保持一致）
    offset += 2;

    buff[0] = offset - 3; // 长度填充
    ((cmd_buf_t*)cmd_buf)->data_len = offset;

    return SUCCESSFUL;
}

Static int pack_dcmu_comm_para(void* dev_inst, void *cmd_buf) {//不支持广播
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    unsigned char* buff;
    unsigned int offset = DATA_INFO_OFFSET;
    float f_data = 0.0f;
    int i_data = 0;
    unsigned char data_flag1 = 0;
    unsigned char data_flag2 = 0;
    unsigned short debug_cmd_cnt = 0;

    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf_temp != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(cmd_buf_temp->buf != NULL, FAILURE);
    
    get_one_data(DCMU_DATA_ID_GET_COMM_PARA_CNT, &debug_cmd_cnt);
    debug_cmd_cnt++;
    set_one_data(DCMU_DATA_ID_GET_COMM_PARA_CNT, &debug_cmd_cnt);

    buff = cmd_buf_temp->buf;

    get_one_para(DCMU_PARA_ID_DC_VOLTAGE_HIGH_THRESHOLD_OFFSET , &f_data);
    put_uint16_to_buff(&buff[offset], round(f_data*pow(10, 1)));                         //直流电压高阈值
    offset += 2;

    get_one_para(DCMU_PARA_ID_DC_VOLTAGE_LOW_THRESHOLD_OFFSET , &f_data);
    put_uint16_to_buff(&buff[offset], round(f_data*pow(10, 1)));                         //直流电压低阈值
    offset += 2;

    get_one_para(DCMU_PARA_ID_BATTERY_VOLTAGE_LOW_THRESHOLD_OFFSET , &f_data);
    put_uint16_to_buff(&buff[offset], round(f_data*pow(10, 1)));                         //电池电压低阈值
    offset += 2;

    get_one_para(DCMU_PARA_ID_BATTERY_VOLTAGE_TOO_LOW_THRESHOLD_OFFSET , &f_data);
    put_uint16_to_buff(&buff[offset], round(f_data*pow(10, 1)));                         //电池电压过低阈值
    offset += 2;

    get_one_para(DCMU_PARA_ID_BATTERY_CURRENT_THRESHOLD_OFFSET , &f_data); 
    buff[offset++] = round(f_data * pow(10,2));                                    //电池电流异常阈值

    get_one_para(DCMU_PARA_ID_BATTERY_TEMPERATURE_HIGH_THRESHOLD_OFFSET , &f_data);
    buff[offset++] = round(f_data);                                             //电池温度高阈值

    get_one_para(DCMU_PARA_ID_BATTERY_TEMPERATURE_LOW_THRESHOLD_OFFSET , &f_data);
    buff[offset++] = round(f_data);                                            //电池温度低阈值

    get_one_para(DCMU_PARA_ID_BATTERY_LOOP_BROKEN_THRESHOLD_OFFSET , &f_data);
    put_uint16_to_buff(&buff[offset], round(f_data*pow(10, 1)));       //电池回路断阈值
    offset += 2;

    get_one_para(DCMU_PARA_ID_BATTERY_DISCHARGE_THRESHOLD_OFFSET , &f_data);
    put_int16_to_buff(&buff[offset], round(f_data*pow(10, 1)));                           //电池放电阈值
    offset += 2;

    buff[offset]    = 0x0C;                             // 告警属性设置个数12
    offset += 1;

    get_alm_para(DCMU_ALM_ID_DC_VOLT_HIGH_LEVEL , &i_data);
    buff[offset++] = i_data;                                             //直流电压高告警属性设置

    get_alm_para(DCMU_ALM_ID_DC_VOLT_LOW_LEVEL , &i_data);
    buff[offset++] = i_data;                                             //直流电压低告警属性设置

    get_alm_para(DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN_LEVEL , &i_data);
    buff[offset++] = i_data;                                             //直流负载回路断告警属性设置

    get_alm_para(DCMU_ALM_ID_DC_LIGHTNING_PROTECTOR_DAMAGED_LEVEL , &i_data);
    buff[offset++] = i_data;                                             //直流防雷器损坏告警属性设置

    get_alm_para(DCMU_ALM_ID_BATTERY_VOLTAGE_LOW_LEVEL , &i_data);
    buff[offset++] = i_data;                                             //电池电压低告警属性设置

    get_alm_para(DCMU_ALM_ID_BATTERY_VOLTAGE_TOO_LOW_LEVEL , &i_data);
    buff[offset++] = i_data;                                             //电池电压过低告警属性设置

    get_alm_para(DCMU_ALM_ID_ABNORMAL_BATTERY_CURRENT_LEVEL , &i_data);
    buff[offset++] = i_data;                                             //电池电流异常告警属性设置

    get_alm_para(DCMU_ALM_ID_BATTERY_TEMPERATURE_HIGH_LEVEL , &i_data);
    buff[offset++] = i_data;                                             //电池温度高告警属性设置

    get_alm_para(DCMU_ALM_ID_BATTERY_TEMPERATURE_LOW_LEVEL , &i_data);
    buff[offset++] = i_data;                                             //电池温度低告警属性设置

    get_alm_para(DCMU_ALM_ID_BATTERY_LOOP_BROKEN_LEVEL , &i_data);
    buff[offset++] = i_data;                                             //电池回路断告警属性设置

    get_alm_para(DCMU_ALM_ID_BATTERY_DISCHARGE_LEVEL , &i_data);
    buff[offset++] = i_data;                                             //蓄电池放电告警属性设置

    get_alm_para(DCMU_ALM_ID_INVALID_BATTERY_TEMPERATURE_LEVEL , &i_data);
    buff[offset++] = i_data;                                             //电池温度失效告警属性设置

    buff[0] = offset - DATA_INFO_OFFSET; 
    get_one_data(DCMU_DATA_ID_DATA_FLAG1, &data_flag1);
    data_flag1 = data_flag1 | FLAG1_E | FLAG1_F;
    buff[1] = data_flag1;      // data_flag1;

    get_one_data(DCMU_DATA_ID_DATA_FLAG2, &data_flag2);
    buff[2] = data_flag2;       
    ((cmd_buf_t*)cmd_buf)->data_len = offset;
    return SUCCESSFUL;
}

Static int pack_comm_func(void* dev_inst, void *cmd_buf){
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    unsigned char* buff;
    unsigned char data_flag1 = 0;
    unsigned char data_flag2 = 0;

    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf_temp != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(cmd_buf_temp->buf != NULL, FAILURE);
    
    buff = cmd_buf_temp->buf;
    get_one_data(DCMU_DATA_ID_DATA_FLAG1, &data_flag1);
    data_flag1 = data_flag1 | FLAG1_E | FLAG1_F;
    buff[1] = data_flag1;      // data_flag1;

    get_one_data(DCMU_DATA_ID_DATA_FLAG2, &data_flag2);
    buff[2] = data_flag2;   
    if( ((cmd_buf_t*)cmd_buf)->rtn != RTN_OK){
        buff[0] = 1;
        buff[3] = RTN_INVLDATA;
        ((cmd_buf_t*)cmd_buf)->data_len = 4;
        return SUCCESSFUL;
    }
    buff[0] = 0;
    ((cmd_buf_t*)cmd_buf)->data_len = 3;
    return SUCCESSFUL;
}

Static int parse_dcmu_comm_para(void* dev_inst, void *cmd_buf) {
    unsigned char* buff;
    unsigned int offset = 0;
    unsigned char uc_data = 0;
    short  dc_buff = 0;
    float f_dc_buff_temp = 0;
    int  alm_info_num = 0;
    int ret = 0;
    unsigned short debug_cmd_cnt = 0;

    RETURN_VAL_IF_FAIL(dev_inst != NULL, FAILURE);

    // 检查cmd_buf是否为空
    RETURN_VAL_IF_FAIL(cmd_buf != NULL, FAILURE);
    cmd_buf_t* s_cmd_buf = (cmd_buf_t*)cmd_buf;
    buff = s_cmd_buf->buf;

    // 检查buff是否为空
    RETURN_VAL_IF_FAIL(buff != NULL, FAILURE);

    get_one_data(DCMU_DATA_ID_SET_COMM_PARA_CNT, &debug_cmd_cnt);
    debug_cmd_cnt++;
    set_one_data(DCMU_DATA_ID_SET_COMM_PARA_CNT, &debug_cmd_cnt);

    //s_cmd_buf->data_len = buff[offset++];//不是不解析length在解析函数里面解析吗？

    dc_buff = get_int16_data(&buff[offset]);
    f_dc_buff_temp = (float)dc_buff / pow(10, 1);
    ret = set_one_para(DCMU_PARA_ID_DC_VOLTAGE_HIGH_THRESHOLD_OFFSET , &f_dc_buff_temp, TRUE, TRUE);  // 直流电压高阈值
    offset += 2;

    dc_buff = get_int16_data(&buff[offset]);
    f_dc_buff_temp = (float)dc_buff / pow(10, 1);
    ret |= set_one_para(DCMU_PARA_ID_DC_VOLTAGE_LOW_THRESHOLD_OFFSET , &f_dc_buff_temp, TRUE, TRUE);  // 直流电压低阈值
    offset += 2;

    dc_buff = get_int16_data(&buff[offset]);
    f_dc_buff_temp = (float)dc_buff / pow(10, 1);
    ret |= set_one_para(DCMU_PARA_ID_BATTERY_VOLTAGE_LOW_THRESHOLD_OFFSET , &f_dc_buff_temp, TRUE, TRUE);  // 电池电压低阈值
    offset += 2;

    dc_buff = get_int16_data(&buff[offset]);
    f_dc_buff_temp = (float)dc_buff / pow(10, 1);
    ret |= set_one_para(DCMU_PARA_ID_BATTERY_VOLTAGE_TOO_LOW_THRESHOLD_OFFSET , &f_dc_buff_temp, TRUE, TRUE);  // 电池电压过低阈值
    offset += 2;

    f_dc_buff_temp = buff[offset++];
    f_dc_buff_temp = f_dc_buff_temp/pow(10, 2);
    ret |= set_one_para(DCMU_PARA_ID_BATTERY_CURRENT_THRESHOLD_OFFSET , &f_dc_buff_temp, TRUE, TRUE);  // 电池电流异常阈值

    f_dc_buff_temp = buff[offset++];
    ret |= set_one_para(DCMU_PARA_ID_BATTERY_TEMPERATURE_HIGH_THRESHOLD_OFFSET , &f_dc_buff_temp, TRUE, TRUE);  // 电池温度高阈值

    f_dc_buff_temp = buff[offset++];
    ret |= set_one_para(DCMU_PARA_ID_BATTERY_TEMPERATURE_LOW_THRESHOLD_OFFSET , &f_dc_buff_temp, TRUE, TRUE);  // 电池温度低阈值

    dc_buff = get_int16_data(&buff[offset]);
    f_dc_buff_temp = (float)dc_buff / pow(10, 1);
    ret |= set_one_para(DCMU_PARA_ID_BATTERY_LOOP_BROKEN_THRESHOLD_OFFSET , &f_dc_buff_temp, TRUE, TRUE);  // 电池回路断阈值
    offset += 2;

    dc_buff = get_int16_data(&buff[offset]);
    f_dc_buff_temp = (float)dc_buff / pow(10, 1);
    ret |=  set_one_para(DCMU_PARA_ID_BATTERY_DISCHARGE_THRESHOLD_OFFSET , &f_dc_buff_temp, TRUE, TRUE);  // 电池放电阈值
    offset += 2;

    alm_info_num = buff[offset++];                             // 告警属性设置个数12
    if(alm_info_num != COMM_PARA_INFO_SETTING_NUM){
        ((cmd_buf_t*)cmd_buf)->rtn = RTN_INVLDATA;
    }

    uc_data = buff[offset++];
    ret |= set_alm_para(DCMU_ALM_ID_DC_VOLT_HIGH_LEVEL , &uc_data, TRUE);  //直流电压高告警属性设置

    uc_data = buff[offset++];
    ret |= set_alm_para(DCMU_ALM_ID_DC_VOLT_LOW_LEVEL , &uc_data, TRUE);  //直流电压低告警属性设置

    uc_data = buff[offset++];
    ret |= set_alm_para(DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN_LEVEL , &uc_data, TRUE); //直流负载回路断告警属性设置

    uc_data = buff[offset++];
    ret |= set_alm_para(DCMU_ALM_ID_DC_LIGHTNING_PROTECTOR_DAMAGED_LEVEL , &uc_data, TRUE); //直流防雷器损坏告警属性设置

    uc_data = buff[offset++];
    ret |= set_alm_para(DCMU_ALM_ID_BATTERY_VOLTAGE_LOW_LEVEL , &uc_data, TRUE); //电池电压低告警属性设置

    uc_data = buff[offset++];
    ret |= set_alm_para(DCMU_ALM_ID_BATTERY_VOLTAGE_TOO_LOW_LEVEL , &uc_data, TRUE); //电池电压过低告警属性设置

    uc_data = buff[offset++];
    ret |= set_alm_para(DCMU_ALM_ID_ABNORMAL_BATTERY_CURRENT_LEVEL , &uc_data, TRUE);     //电池电流异常告警属性设置

    uc_data = buff[offset++];
    ret |= set_alm_para(DCMU_ALM_ID_BATTERY_TEMPERATURE_HIGH_LEVEL , &uc_data, TRUE);  //电池温度高告警属性设置

    uc_data = buff[offset++];
    ret |= set_alm_para(DCMU_ALM_ID_BATTERY_TEMPERATURE_LOW_LEVEL , &uc_data, TRUE);  //电池温度低告警属性设置

    uc_data = buff[offset++];
    ret |= set_alm_para(DCMU_ALM_ID_BATTERY_LOOP_BROKEN_LEVEL , &uc_data, TRUE); //电池回路断告警属性设置

    uc_data = buff[offset++];
    ret |= set_alm_para(DCMU_ALM_ID_BATTERY_DISCHARGE_LEVEL , &uc_data, TRUE); //蓄电池放电告警属性设置

    uc_data = buff[offset++];
    ret |= set_alm_para(DCMU_ALM_ID_INVALID_BATTERY_TEMPERATURE_LEVEL , &uc_data, TRUE);   //电池温度失效告警属性设置

    if(ret < 0) {
        ((cmd_buf_t*)cmd_buf)->rtn = RTN_INVLDATA;
    }
    else
    {
        update_para();
        cal_common_para_crc(); // 设置完成后更新公共参数的CRC
    }

    return SUCCESSFUL;
}

Static int pack_dcmu_extend_para(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    unsigned char* buff;
    unsigned int offset = DATA_INFO_OFFSET;
    unsigned char uc_data = 0;
    int i_data = 0;
    float f_data = 0.0f;
    unsigned char data_flag1 = 0;
    unsigned char data_flag2 = 0;

    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf_temp != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(cmd_buf_temp->buf != NULL, FAILURE);

    buff = cmd_buf_temp->buf;

    get_one_para(DCMU_PARA_ID_BATTERY_TEMPERATURE_TOO_HIGH_THRESHOLD_OFFSET , &f_data);
    buff[offset++] = (unsigned char)(f_data + 0.5);                                                               //电池温度过高阈值

    get_one_para(DCMU_PARA_ID_DC_VOLTAGE_TOO_LOW_THRESHOLD_OFFSET , &f_data);
    put_uint16_to_buff(&buff[offset], round(f_data*pow(10, 1)));                         //直流电压过低阈值
    offset += 2;

    get_one_para(DCMU_PARA_ID_DC_VOLTAGE_TOO_HIGH_THRESHOLD_OFFSET , &f_data);
    put_uint16_to_buff(&buff[offset], round(f_data*pow(10, 1)));                         //直流电压过高阈值
    offset += 2;

    buff[offset]    = 0x03;                             // 告警属性设置个数3
    offset += 1;

    get_alm_para(DCMU_ALM_ID_BATTERY_TEMPERATURE_TOO_HIGH_LEVEL , &uc_data); //电池温度高告警属性设置
    buff[offset++] = uc_data;

    get_alm_para(DCMU_ALM_ID_DC_VOLTAGE_TOO_LOW_LEVEL , &uc_data);      // 直流电压过低告警属性设置 
    buff[offset++] = uc_data;
    
    get_alm_para(DCMU_ALM_ID_DC_VOLTAGE_TOO_HIGH_LEVEL , &uc_data);    // 直流电压过高告警属性设置
    buff[offset++] = uc_data;

    buff[0] = offset - DATA_INFO_OFFSET; 
    get_one_data(DCMU_DATA_ID_DATA_FLAG1, &data_flag1);
    data_flag1 = data_flag1 | FLAG1_E | FLAG1_F;
    buff[1] = data_flag1;      // data_flag1;

    get_one_data(DCMU_DATA_ID_DATA_FLAG2, &data_flag2);
    buff[2] = data_flag2;        
    ((cmd_buf_t*)cmd_buf)->data_len = offset;
    return SUCCESSFUL;
}

Static int parse_dcmu_extend_para(void* dev_inst, void *cmd_buf) {
    unsigned char* buff;
    unsigned int offset = 0;
    unsigned char uc_data = 0;
    short dc_buff = 0;
    float f_dc_buff_temp = 0;
    int  alm_info_num = 0;
    int ret = 0;

    RETURN_VAL_IF_FAIL(dev_inst != NULL, FAILURE);

    // 检查cmd_buf是否为空
    RETURN_VAL_IF_FAIL(cmd_buf != NULL, FAILURE);
    cmd_buf_t* s_cmd_buf = (cmd_buf_t*)cmd_buf;
    buff = s_cmd_buf->buf;

    // 检查buff是否为空
    RETURN_VAL_IF_FAIL(buff != NULL, FAILURE);

    //s_cmd_buf->data_len = buff[offset++];

    f_dc_buff_temp = buff[offset++];
    ret = set_one_para(DCMU_PARA_ID_BATTERY_TEMPERATURE_TOO_HIGH_THRESHOLD_OFFSET , &f_dc_buff_temp, TRUE, TRUE);  // 电池温度过高阈值

    dc_buff = get_int16_data(&buff[offset]);
    f_dc_buff_temp = (float)dc_buff / pow(10, 1);
    ret |= set_one_para(DCMU_PARA_ID_DC_VOLTAGE_TOO_LOW_THRESHOLD_OFFSET , &f_dc_buff_temp, TRUE, TRUE);  // 直流电压过低阈值
    offset += 2;

    dc_buff = get_int16_data(&buff[offset]);
    f_dc_buff_temp = (float)dc_buff / pow(10, 1);
    ret |= set_one_para(DCMU_PARA_ID_DC_VOLTAGE_TOO_HIGH_THRESHOLD_OFFSET , &f_dc_buff_temp, TRUE, TRUE);  // 直流电压过高阈值
    offset += 2;

    alm_info_num = buff[offset++];                             // 告警属性设置个数3
    if(alm_info_num != EXTEND_COMM_PARA_INFO_SETTING_NUM){
        ((cmd_buf_t*)cmd_buf)->rtn = RTN_INVLDATA;
    }

    uc_data = buff[offset++];
    ret |= set_alm_para(DCMU_ALM_ID_DC_VOLTAGE_TOO_LOW_LEVEL, &uc_data, TRUE);  //直流电压过低告警等级

    uc_data = buff[offset++];
    ret |= set_alm_para(DCMU_ALM_ID_DC_VOLTAGE_TOO_HIGH_LEVEL, &uc_data, TRUE);  //直流电压过高告警属性设置

    uc_data = buff[offset++];
    ret |= set_alm_para(DCMU_ALM_ID_BATTERY_TEMPERATURE_TOO_HIGH_LEVEL , &uc_data, TRUE);  //电池温度过高告警属性设置

    if(ret < 0) {
        ((cmd_buf_t*)cmd_buf)->rtn = RTN_INVLDATA;
    }
    else
    {
        update_para();
        cal_extra_para_crc();
    }

    return SUCCESSFUL;
}

Static int parse_dcmu_real_alarm(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* cmd_buf_tmp = (cmd_buf_t*)cmd_buf;
    unsigned char* buff;
    
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf_tmp != NULL, FAILURE);

    buff = cmd_buf_tmp->buf;

    RETURN_VAL_IF_FAIL(buff != NULL, FAILURE);

    if(cmd_buf_tmp->data_len > 0) {
        s_command_type = buff[0];
    } else {
        s_command_type = WRONG_DATA_LENGTH;
    }
    return SUCCESSFUL;
}

Static int pack_dcmu_real_alarm(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* cmd_buf_tmp = (cmd_buf_t*)cmd_buf;
    unsigned char* buff;
    unsigned char data_flag1 = 0;
    unsigned char data_flag2 = 0;
    static unsigned short s_send_index = 0;
    static unsigned short s_send_index_bak = 0;
    real_alarm_t* real_alm = NULL;
    alarm_map_t* alarm_map = NULL;
    unsigned int offset = DATA_INFO_OFFSET;
    unsigned short debug_cmd_cnt = 0;
    
    unsigned char uc_ID2 = 0;
    unsigned char uc_index = 0;
    int ret = 0;

    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf_tmp != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(cmd_buf_tmp->buf != NULL, FAILURE);

    get_one_data(DCMU_DATA_ID_GET_REAL_ALM_CNT, &debug_cmd_cnt);
    debug_cmd_cnt++;
    set_one_data(DCMU_DATA_ID_GET_REAL_ALM_CNT, &debug_cmd_cnt);

    buff = cmd_buf_tmp->buf; 

    get_one_data(DCMU_DATA_ID_DATA_FLAG1, &data_flag1);
    buff[OFFSET_FLAG1] = data_flag1;      // data_flag1;
    get_one_data(DCMU_DATA_ID_DATA_FLAG2, &data_flag2);
    buff[OFFSET_FLAG2] = data_flag2;      // data_flag2;

    switch (s_command_type) {
        case GROUP_TYPE_RCV_START:
            s_send_index = 0;
            data_flag1 &= ~FLAG1_ALMCH;   // 清除告警变化标志位
            set_one_data(DCMU_DATA_ID_DATA_FLAG1, &data_flag1); 
            break;
        case GROUP_TYPE_RCV_NEXT:
            break;
        case GROUP_TYPE_RCV_ERROR:
            s_send_index = s_send_index_bak;
            break;
        default:
            buff[offset++] = RTN_INVLDATA;
            buff[OFFSET_DATALEN] = offset - 3;                     // 长度填充
            buff[OFFSET_FLAG1] = data_flag1 | FLAG1_E | FLAG1_F;
            cmd_buf_tmp->data_len = offset;
            cmd_buf_tmp->rtn = RTN_INVLDATA;
            return SUCCESSFUL;
    }

    s_send_index_bak = s_send_index;  // 备份告警id数组的遍历首索引

    if (s_send_index == 0) {          // 如果是发送第一包数据则将commandtype置为0
        buff[offset++] = GROUP_TYPE_SEND_START;       // 把commandtype置为0
    } else {
        buff[offset++] = GROUP_TYPE_SEND_NEXT;        // 把commandtype置为1
    }

    for (; s_send_index < sizeof(s_dcmu_alarm_id) / sizeof(s_dcmu_alarm_id[0]); s_send_index++) {
        if (offset > DCMU_REAL_ALARM_DATALEN_MAX) {   // 如果数据包太长,分成多个包
            break;
        }

        real_alm = get_realtime_alarm(s_dcmu_alarm_id[s_send_index]);

        if (real_alm == NULL) {
            continue;
        }

        ret = get_alarm_sn(ALM_ID_GET_ALM_CODE(s_dcmu_alarm_id[s_send_index]), &alarm_map);
        // 检查get_alarm_sn是否成功
        if (ret == -1) {
            continue;
        }

        uc_ID2 = alarm_map->id2;
        uc_index = ALM_ID_GET_DEV(s_dcmu_alarm_id[s_send_index]);

        buff[offset++] = ID1_ALM;     // 告警ID1
        buff[offset++] = uc_ID2;      // 告警ID2
        if (uc_index > 0) {
            buff[offset++] = uc_index - 1;    // 告警Index
        } else {
            buff[offset++] = uc_index;        // 告警Index
        }
        buff[offset++] = get_real_alm_level(real_alm);  // 告警值  
        put_time_t_to_buff(&buff[offset], real_alm->start_time);  // 告警产生时间
        offset += 7;
    }

    if (s_send_index == sizeof(s_dcmu_alarm_id) / sizeof(s_dcmu_alarm_id[0])) {
        buff[OFFSET_COMMANDTYPE] = GROUP_TYPE_SEND_END;  // 最后一包数据,CommandType=0x02
        s_send_index = 0;
    }

    if (offset == 4) {  // 没有实时告警记录，只有CommandType和长度，data_flag,置位RTN无数据并把后台的ID返回
        buff[OFFSET_ERRID] = RTN_NODATA;
        cmd_buf_tmp->rtn = RTN_NODATA;
    }

    buff[OFFSET_DATALEN] = offset - 3;                     // 长度填充
    buff[OFFSET_FLAG1] = data_flag1 | FLAG1_E | FLAG1_F;
    cmd_buf_tmp->data_len = offset;

    return SUCCESSFUL;

}

Static char soft_sysreset_handle(void)
{
    if (s_reset_delay == NULL)
    {
        s_reset_delay = rt_timer_create("softreset", NVIC_SystemReset, NULL, 1000, RT_TIMER_FLAG_ONE_SHOT);
    }

    if (s_reset_delay != NULL)
    {
        rt_timer_stop(s_reset_delay);
        rt_timer_start(s_reset_delay);
    }

    set_software_reset_reason(RESET_REASON_REMOTE_CTRL);
    
    return SUCCESSFUL;
}

Static int ctrl_cmd(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    unsigned char cid1,cid2 = 0;
    unsigned char* buff;
    unsigned short debug_cmd_cnt = 0;
    
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf_temp != NULL, FAILURE);

    get_one_data(DCMU_DATA_ID_REC_CTRL_CMD_CNT, &debug_cmd_cnt);
    debug_cmd_cnt++;
    set_one_data(DCMU_DATA_ID_REC_CTRL_CMD_CNT, &debug_cmd_cnt);

    buff = cmd_buf_temp->buf;

    RETURN_VAL_IF_FAIL(buff != NULL, FAILURE);

    cid1 = buff[0];
    cid2 = buff[1];

    if(cid1 == 0x34 && cid2 == 0xf3) //遥控复位
    {
        soft_sysreset_handle();
    }
    else
    {
        cmd_buf_temp->rtn = RTN_WRONG_COMMAND;
        cmd_buf_temp->data_len = 0;
    }
    return SUCCESSFUL;
}

Static int check_alm_id(unsigned short alm_code) {

   for (int i = 0; i < sizeof(s_dcmu_alarm_id) / sizeof(s_dcmu_alarm_id[0]); i++) {
        if(alm_code == ALM_ID_GET_ALM_CODE(s_dcmu_alarm_id[i]))
        {
            return SUCCESSFUL;
        }
   }
   return FAILURE;
}

Static int parse_his_alarm(void* dev_inst, void *cmd_buf) {
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf != NULL, FAILURE);
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    RETURN_VAL_IF_FAIL(cmd_buf_temp->buf != NULL, FAILURE);

    unsigned char* buff = cmd_buf_temp->buf;
    s_command_type = (cmd_buf_temp->data_len > 1) ? buff[1] : WRONG_DATA_LENGTH;
    return SUCCESSFUL;
}

Static int HisAlmToBuff(unsigned short index, cmd_buf_t *cmd_buf) {
    unsigned char* buff = cmd_buf->buf;
    unsigned int offset = 0;
    alarm_map_t *alarm_map;
    his_alarm_info_t tHisAlm;
    time_base_t start_time, end_time;
    unsigned char uc_ID2, uc_index, alm_level_offset = 0;
    static unsigned char sendlast_flag = FALSE;

    unsigned char data_flag1 = 0;
    unsigned char data_flag2 = 0;
    get_one_data(DCMU_DATA_ID_DATA_FLAG1, &data_flag1);
    get_one_data(DCMU_DATA_ID_DATA_FLAG2, &data_flag2);

    buff[offset++] = 0;       // 长度占位
    buff[offset++] = data_flag1;      // data_flag1
    buff[offset++] = data_flag2;      // data_flag2
    buff[offset++] = GROUP_TYPE_SEND_NORMAL;    // commandtype

    do{
        // 获取历史告警记录
        rt_memset_s(&tHisAlm,sizeof(his_alarm_info_t),0,sizeof(his_alarm_info_t));
        
        if (pub_hisrecord_read_msg(1, 1, index, (unsigned char *)&tHisAlm) != SUCCESSFUL)
        { 
            if(sendlast_flag)// 没有历史告警记录
            {
                data_flag2 &= ~(FLAG2_HISALMOV | FLAG2_HISALM); // 清除历史告警上送标志位
                set_one_data(DCMU_DATA_ID_DATA_FLAG2, &data_flag2);
                buff[OFFSET_DATALEN] = 1; // 长度填充
                buff[OFFSET_FLAG1] = data_flag1;
                buff[OFFSET_FLAG2] = data_flag2;
                buff[OFFSET_ERRID] = RTN_NODATA;
                cmd_buf->rtn = RTN_NODATA;
                cmd_buf->data_len = OFFSET_ERRID + 1;
                return 0;
            }
            buff[OFFSET_COMMANDTYPE] = GROUP_TYPE_SEND_LAST;
            sendlast_flag = TRUE;
            break;
        }

       if ((get_alarm_sn(tHisAlm.alarm_id, &alarm_map) == -1) || (check_alm_id(tHisAlm.alarm_id) == -1))
        {
            index++;
            continue;
        }

        sendlast_flag = FALSE;
        uc_ID2 = alarm_map->id2;
        alm_level_offset = GET_ALM_PARA_BY_ALM_CODE(tHisAlm.alarm_id,0);
        uc_index = tHisAlm.index;
        time_t_to_timestruct(tHisAlm.start_time, &start_time);
        time_t_to_timestruct(tHisAlm.end_time, &end_time);

        buff[offset++] = ID1_DCMU_ALARM;     // 告警ID1
        buff[offset++] = uc_ID2;      // 告警ID2
        buff[offset++] = (uc_index > 0) ? (uc_index - 1) : uc_index; // 告警Index
        get_alm_para(alm_level_offset,&buff[offset++]);
        put_time_to_buff(&buff[offset], start_time);  // 告警产生时间
        offset += 7;
        put_time_to_buff(&buff[offset], end_time);  // 告警结束时间
        offset += 7;
        index++;

        if (offset > DCMU_HIS_ALARM_PACK_MAX) {   // 如果数据包太长,分成多个包
            break;
        }
    }while(1);

    buff[OFFSET_DATALEN] = offset - 3;                     // 长度填充
    cmd_buf->data_len = offset;

    return index;
}


Static int pack_his_alarm(void* dev_inst, void *cmd_buf) {
    if (dev_inst == NULL || cmd_buf == NULL) {
        return FAILURE;
    }

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    if (cmd_buf_temp->buf == NULL) {
        return FAILURE;
    }

    unsigned short debug_cmd_cnt = 0;
    static unsigned short s_start_index = 0, s_start_index_bak = 0, s_end_index = 0;
    unsigned char data_flag1 = 0;
    unsigned char data_flag2 = 0;

    get_one_data(DCMU_DATA_ID_GET_HIS_ALM_CNT, &debug_cmd_cnt);
    debug_cmd_cnt++;
    set_one_data(DCMU_DATA_ID_GET_HIS_ALM_CNT, &debug_cmd_cnt);

    // 获取数据标志位
    get_one_data(DCMU_DATA_ID_DATA_FLAG1, &data_flag1);
    get_one_data(DCMU_DATA_ID_DATA_FLAG2, &data_flag2);

    // 处理命令类型
    switch (s_command_type) {
        case GROUP_TYPE_RCV_START:
            s_start_index = 0;
            break;
        case GROUP_TYPE_RCV_NEXT:
            s_start_index = s_end_index;
            break;
        case GROUP_TYPE_RCV_ERROR:
            s_start_index = s_start_index_bak;
            break;
        case GROUP_TYPE_RCV_END:
            data_flag2 &= ~(FLAG2_HISALMOV | FLAG2_HISALM); // 清除历史告警上送标志位
            set_one_data(DCMU_DATA_ID_DATA_FLAG2, &data_flag2);
            cmd_buf_temp->buf[OFFSET_DATALEN] = 1; // 长度填充
            cmd_buf_temp->buf[OFFSET_FLAG1] = data_flag1 | FLAG1_E | FLAG1_F;
            cmd_buf_temp->buf[OFFSET_FLAG2] = data_flag2;
            cmd_buf_temp->buf[OFFSET_COMMANDTYPE] = GROUP_TYPE_SEND_END;
            cmd_buf_temp->data_len = OFFSET_COMMANDTYPE + 1;
            return SUCCESSFUL;
        default:
            cmd_buf_temp->buf[OFFSET_DATALEN] = 1; // 长度填充
            cmd_buf_temp->buf[OFFSET_FLAG1] = data_flag1 | FLAG1_E | FLAG1_F;
            cmd_buf_temp->buf[OFFSET_FLAG2] = data_flag2;
            cmd_buf_temp->buf[OFFSET_ERRID] = RTN_INVLDATA;
            cmd_buf_temp->rtn = RTN_INVLDATA;
            cmd_buf_temp->data_len = OFFSET_ERRID + 1;
            return SUCCESSFUL;
    }

    // 备份起始索引
    s_start_index_bak = s_start_index;

    // 转换历史告警记录到缓冲区
    s_end_index = HisAlmToBuff(s_start_index, cmd_buf_temp);

    cmd_buf_temp->buf[OFFSET_FLAG1] = data_flag1 | FLAG1_E | FLAG1_F;

    return SUCCESSFUL;
}

