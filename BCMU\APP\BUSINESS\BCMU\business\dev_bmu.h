/**
 * @file     device_type.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2017-04-19
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */

#ifndef _DEVICE_BMU_H
#define _DEVICE_BMU_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "device_type.h"
#include "device_num.h"


#define BMU_SOFT_VER_LEN        20      ///< BMU 软件版本数组长度
#define BMU_ASSET_MANAGE_LEN    30      ///< BMU 资产管理数组长度
#define BMU_SYS_NAME_LEN        32
#define BMU_SOFT_VER            20
#define BMU_CELL_FAC_NAME       20
#define BMU_CELL_VER            20
#define BMU_CELL_SERIES_NUM_LEN 20      ///< BMU电芯出厂序列号数组长度

#pragma pack(1)

typedef struct
{
    float cell_temp[BATT_MOD_CELL_NUM];
    float cell_vol[BATT_MOD_CELL_NUM];
    float cell_temp_max;
    unsigned char cell_temp_max_channel;
    float cell_temp_min;
    unsigned char cell_temp_min_channel;
    float cell_average_temp;
    float cell_vol_max;
    unsigned char cell_vol_max_channel;
    float cell_vol_min;
    unsigned char cell_vol_min_channel;
    float cell_average_vol;
    float mod_vol_total;
    float mod_pos_temp;
    float mod_neg_temp;
    unsigned char fan1_speed;
    unsigned char fan2_speed;
    float mod_temp;
}BMU_real_data_ana_t;

typedef struct
{
    unsigned char cell_balanced_status[BATT_MOD_CELL_NUM];
    unsigned char mod_optimizer_status;
    unsigned char mod_sleep_status;
    unsigned char fan1_status;
    unsigned char fan2_status;
    unsigned char bmu_self_check_state;
}BMU_real_data_dig_t;

typedef struct
{
    unsigned char cell_over_vol_alm[BATT_MOD_CELL_NUM];
    unsigned char cell_under_vol_alm[BATT_MOD_CELL_NUM];
    unsigned char cell_high_temp_alm[BATT_MOD_CELL_NUM];
    unsigned char cell_low_temp_alm[BATT_MOD_CELL_NUM];
    unsigned char fire_mod_alarm;
    unsigned char abnormal_temp_alm;
    unsigned char abnormal_vol_alm;
}BMU_real_data_alm_t;

typedef struct
{
    unsigned char bmu_cell_fac_name[BMU_CELL_FAC_NAME];
    unsigned char bmu_cell_ver[BMU_CELL_VER];
    date_base_t   bmu_cell_product_date;
    unsigned char bmu_cell_serial_num[BMU_CELL_SERIES_NUM_LEN];
    unsigned char bmu_sys_name[BMU_SYS_NAME_LEN];
    unsigned char bmu_software_ver[BMU_SOFT_VER];
    date_base_t   bmu_software_pub_date;
}BMU_data_fac_t;

typedef struct {
    unsigned short batt_soc;
    unsigned short batt_soh;
    unsigned int cell_balance_control;
    time_base_t parse_system_time;
    unsigned char bmu_serial_number[15];
}BMU_para_data_t;
#pragma pack()


dev_type_t* init_dev_bmu(void);
void set_bmu_soc_data(BMU_para_data_t *bmu_soc_tmp);
void set_bmu_cell_balance_status(char bmu_no, int cell_balance_control);


#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _DEVICE_BMU_H
