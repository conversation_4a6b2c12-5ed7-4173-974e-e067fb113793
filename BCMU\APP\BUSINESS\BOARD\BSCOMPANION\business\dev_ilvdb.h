/**
 * @file     device_type.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2017-04-19
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */

#ifndef _DEVICE_ILVDB_H
#define _DEVICE_ILVDB_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "device_type.h"

/*-----设备类型-----*/
#define  DEV_CSU       1
#define  DEV_ILVDB     2

#define SWITCH_POWEROFF							0X41D //空开下电
#define SWITCH_POWERON							0X421 //空开上电
#define DELETE_TOTAL_SWITCHPOWER				0X463 //删除空开电量

#define SWITCH_CONFIG_MAX_NUMBER                    10    //空开可配置最大数量

typedef enum {
    SW_TYPE_VOID = 0,
    SW_TYPE_BOOLEAN = 1,
    SW_TYPE_CHAR = 2,
    SW_TYPE_INT8U = 3,
    SW_TYPE_INT8S = 4,
    SW_TYPE_INT16U = 5,
    SW_TYPE_INT16S = 6,
    SW_TYPE_INT32U = 7,
    SW_TYPE_INT32S = 8,
    SW_TYPE_FP32 = 9,
    SW_TYPE_INT64S = 10,
    SW_TYPE_TIMESTRUCT = 11,
    SW_TYPE_DATESTRUCT = 12,
    SW_TYPE_BIT = 13,
}airswitch_data_type_e;

#pragma pack(1)

typedef struct {
    unsigned char  LINK_Master_Address;	//建链主机地址
    unsigned short CRC_Code;			//批量公共参数段1的CRC码
    unsigned char  Sync_Flag;           //同步标志
}Ilvdb_sync_data_t;

typedef struct {
    signed short   Board_temp1;         //单板温度1
    unsigned char  Board_over_temp1;    //单板温度1过温
    signed short   Board_temp2;         //单板温度2
    unsigned char  Board_over_temp2;    //单板温度2过温
}Ilvdb_board_data_t;

typedef struct {
    unsigned char  Airswitch_config_num;         //空开可配置数量
}Ilvdb_switch_data_t;

typedef struct {	
    unsigned char   AirSwitch_Address;          //空开地址
	short           Switch_Output_Voltage;      //空开输出电压
	short           Switch_Current;             //空开电流
	short           Switch_Power;               //空开功率
	long            Switch_Input_Power_Energy;  //空开输入电量
	long            Switch_Output_Power_Energy; //空开输出电量
	short           Fault_Opening_Times;        //故障分闸次数
    unsigned char   Switch_Loop_Status;         //空开回路状态
    unsigned char   Switch_DownLoad_Status;     //空开下电状态
    unsigned char   Connector_status;           //接触器状态
    unsigned char   Authorized_status;          //授权状态
    unsigned char   Switch_Power_Status;        //空开有电状态
    unsigned char   Switch_Digital_Reserve[3];  //保留位
    unsigned char   Switch_Over_Current_Alarm;  //空开过流告警
    unsigned char   Switch_Open;                //空开断开
    unsigned char   Switch_Low_Voltage_Download;    //空开低压下电
    unsigned char   Switch_Download;            //空开下电
    unsigned char   Switch_Close_Failure;       //空开合闸失败
    unsigned char   Switch_Opening_Failure;     //空开分闸失败
    unsigned char   Switch_Over_Current_Protect;    //空开过流保护
    unsigned char   Switch_Alarm_Reserve[1];    //保留位
}Ilvdb_real_data_t;

typedef struct {
    unsigned char  AirSwitch_Address;                       //空开地址
	unsigned char  Switch_Download_Enabled;					//空开下电使能
	unsigned short Switch_Download_Voltage_Threshold;		//空开下电电压阈值
	unsigned short Switch_Upload_Voltage_Threshold;			//空开上电电压阈值
	unsigned char  Switch_Over_Current_Protect_Enabled;		//空开过流保护使能
	unsigned short Switch_Over_Current_Alarm_Threshold;		//空开过流告警阈值
	unsigned char  Switch_Upload_Authorization;				//空开上电授权
	unsigned short Switch_Over_Current_Protect_Delay;		//空开过流保护延时
	unsigned char  Switch_Over_Current_Resume_Times;		//空开过流保护自动恢复次数
	unsigned char  Switch_Over_Current_Resume_Space;		//空开过流保护自动恢复间隔
	unsigned short Switch_Over_Current_Protect_Threshold;	//空开过流保护阈值
	unsigned char  Switch_Disconnect_judgment_mode;			//空开断开判断方式
}Ilvdb_para1_data_t;

typedef struct {
    unsigned char   AirSwitch_Address;                          //空开地址	
	long            OutPut_Electricity_Benchmark_Parameters;	//空开输出电量基准参数
	long            Input_Electricity_Benchmark_Parameters;	    //空开输入电量基准参数
    unsigned short  Switch_Current_Slope;                       //空开电流斜率
    short           Switch_Current_Offset;                      //空开电流零点
}Ilvdb_para2_data_t;

typedef struct {
	char  Ilvdb_System_Name[32];					//智能下电板系统名称	
	char  Ilvdb_SoftwareVersion[20];				//智能下电板软件版本
	date_base_t	   Ilvdb_Software_ReleaseDate;		//智能下电板软件发布日期		
    unsigned char  Ilvdb_Device_Type;						//智能下电板空开类型	
	char  Ilvdb_SN[12];							    //智能下电板序列号			
	short Ilvdb_Rated_Current[10];					//空开额定电流
}Ilvdb_fac_data_t;

/* 北向指令数据汇总 */
typedef struct {
    Ilvdb_sync_data_t   Ilvdb_sync_data;
    Ilvdb_board_data_t  Ilvdb_board_data;
    Ilvdb_switch_data_t Ilvdb_switch_data;
	Ilvdb_real_data_t   Ilvdb_real_data[SWITCH_CONFIG_MAX_NUMBER];
	Ilvdb_para1_data_t  Ilvdb_para1_data[SWITCH_CONFIG_MAX_NUMBER];
	Ilvdb_para2_data_t  Ilvdb_para2_data[SWITCH_CONFIG_MAX_NUMBER];
	Ilvdb_fac_data_t    Ilvdb_fac_data;
}Ilvdb_data_t;

#pragma pack()

dev_type_t* init_dev_ilvdb(void);


#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _DEVICE_ILVDB_H
