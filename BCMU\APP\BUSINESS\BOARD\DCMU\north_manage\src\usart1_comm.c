#include <string.h>
#include <stdlib.h>
#include <rtthread.h>
#include "north_main.h"
#include "io_control.h"
#include "const_define_in.h"
#include "realdata_id_in.h"
#include "Menu.h"
#include "update_and_upload_manage.h"
#include "para_id_in.h"
#include "utils_heart_beat.h"

static dev_inst_t* dev_dcmu = NULL;
Static int s_com_time_out_count_usart1 = 0;
static dev_inst_t* s_north_dev_inst[PROTOCOL_INDEX_MAX] = {NULL};

Static unsigned char s_dev_type_usart1[] = {
    DEV_CSU,
    DEV_NORTH_DCMU_APPTEST,
    DEV_BOTTOM_COMM_UPDATE_UPLOAD,
};

Static msg_map north_msg_map_usart1[] =
{
    {0,NULL},//临时添加解决编译问题
};

Static dcmu_mgr_t * init_thread_data_usart1(link_inst_t* link_inst, unsigned char mod_id) {
    dcmu_mgr_t* dcmu_mgr = NULL;

    dcmu_mgr = rt_malloc(sizeof(dcmu_mgr_t));
    if (dcmu_mgr == NULL)
        return NULL;
    
    dcmu_mgr->cmd_buff = rt_malloc(sizeof(cmd_buf_t));
    if (dcmu_mgr->cmd_buff == NULL) {
        goto MGR_FREE;
    }

    dcmu_mgr->cmd_buff->addr_dev_data = 0x01;
    dcmu_mgr->link_inst = link_inst;
    return dcmu_mgr;
     
MGR_FREE:
    free(dcmu_mgr);
    return NULL;
}

/***************************************************************************
* 函数名称：TraceRS485Debug
* 功能描述：显示RS485调试信息      
***************************************************************************/
static int trace_rs485_debug( void )
{
	char buff[MAX_LETTERS];
    unsigned short comm_debug_cnt1 = 0;
    unsigned short comm_debug_cnt2 = 0;
    unsigned char prtcl_mode = 0;
    unsigned char data_flag1 = 0;
    unsigned char data_flag2 = 0;

    rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));
	
    //接收实时数据和实时告警数量
    get_one_data(DCMU_DATA_ID_REC_CALL_CMD_CNT, &comm_debug_cnt1);
    get_one_data(DCMU_DATA_ID_GET_REAL_ALM_CNT, &comm_debug_cnt2);
    rt_snprintf_s( buff, sizeof(buff), "20H=%u 21H=%u", comm_debug_cnt1, comm_debug_cnt2 );
    SetTraceStr( 3, buff );
	
    //接收历史告警和厂家
    rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));
    get_one_data(DCMU_DATA_ID_GET_HIS_ALM_CNT, &comm_debug_cnt1);
    get_one_data(DCMU_DATA_ID_GET_FACT_INFO_CNT, &comm_debug_cnt2);
    rt_snprintf_s( buff, sizeof(buff), "22H=%u 23H=%u", comm_debug_cnt1, comm_debug_cnt2);
    SetTraceStr( 4, buff );
	
    //接收公告参数和设置时间数量
    rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));
    get_one_data(DCMU_DATA_ID_GET_COMM_PARA_CNT, &comm_debug_cnt1);
    get_one_data(DCMU_DATA_ID_SET_TIME_CNT, &comm_debug_cnt2);
    rt_snprintf_s( buff, sizeof(buff), "24H=%u 25H=%u", comm_debug_cnt1, comm_debug_cnt2);
    SetTraceStr( 5, buff );

    //设置公共参数和设置特定参数
    rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));
    get_one_data(DCMU_DATA_ID_SET_COMM_PARA_CNT, &comm_debug_cnt1);
    get_one_data(DCMU_DATA_ID_SET_SPEC_PARA_CNT, &comm_debug_cnt2);
    rt_snprintf_s( buff, sizeof(buff), "26H=%u 27H=%u", comm_debug_cnt1, comm_debug_cnt2);
    SetTraceStr( 6, buff );
	
    //控制命令
    rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));
    get_one_data(DCMU_DATA_ID_REC_CTRL_CMD_CNT, &comm_debug_cnt1);
    rt_snprintf_s( buff, sizeof(buff), "06H=%u ", comm_debug_cnt1);
    SetTraceStr( 7, buff );

     //记录数据标志
     rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));
     get_one_data(DCMU_DATA_ID_DATA_FLAG1, &data_flag1);
     get_one_data(DCMU_DATA_ID_DATA_FLAG2, &data_flag2);
     rt_snprintf_s( buff, sizeof(buff), "FLAG1:%x  FLAG2:%x", data_flag1, data_flag2);
     SetTraceStr( 10, buff );
	
	return SUCCESSFUL;
}

/***************************************************************************
* 函数名称：初始化RS485通讯重要标志位     
***************************************************************************/
static int init_rs485_data_flag( void ) {
    unsigned char data_flag1 = 0;
    unsigned char data_flag2 = 0;
    char buff[MAX_LETTERS];

    rt_memset_s(buff, sizeof(buff), 0, sizeof(buff));

    get_one_data(DCMU_DATA_ID_DATA_FLAG1, &data_flag1);
    get_one_data(DCMU_DATA_ID_DATA_FLAG2, &data_flag2);

    data_flag1 |= FLAG1_ALMCH;      // 置位实时告警变化标志位
    data_flag1 |= FLAG1_TIMECH;     // 置位时间重置标志位
    data_flag2 |= FLAG2_HISALM;	    // 置位历史告警变化标志位
    data_flag2 |= FLAG2_RST;        // 置位系统复位标志位

    set_one_data(DCMU_DATA_ID_DATA_FLAG1, &data_flag1);
    set_one_data(DCMU_DATA_ID_DATA_FLAG2, &data_flag2);

    rt_snprintf_s( buff, sizeof(buff), "FLAG1:%x  FLAG2:%x", data_flag1, data_flag2);
    SetTraceStr( 10, buff );

    return SUCCESSFUL;
}

/* Started by AICoder, pid:738bf5faf0z83e714be209921038391ab9e553ce */
Static int handle_received_data_usart1(dcmu_mgr_t* north_mgr) {

    for(int loop = 0; loop < ARR_SIZE(s_dev_type_usart1) ; loop ++)
    {
        if(SUCCESSFUL == protocol_parse_recv_process(s_north_dev_inst[loop], north_mgr->cmd_buff))
        {
            led_set_com_by_status(1);
            cmd_send(s_north_dev_inst[loop], north_mgr->cmd_buff);
            led_set_com_by_status(0);
            rt_sem_clear_value(&(north_mgr->link_inst->rx_sem));
            break;
        }
    }
    return 0;
}
/* Ended by AICoder, pid:738bf5faf0z83e714be209921038391ab9e553ce */

/* 北向初始化*/
void* init_usart1_comm(void * param) {
    server_info_t *server_info = (server_info_t *)param;
    dcmu_mgr_t* north_mgr = NULL;
    for(int loop = 0; loop < ARR_SIZE(s_dev_type_usart1); loop ++)
    {
        dev_dcmu = init_dev_inst(s_dev_type_usart1[loop]);
        RETURN_VAL_IF_FAIL(dev_dcmu != NULL, NULL);
        s_north_dev_inst[loop] = dev_dcmu;
    }
    server_info->server.server.map_size = sizeof(north_msg_map_usart1) / sizeof(msg_map);
    register_server_msg_map(north_msg_map_usart1, server_info);
    north_mgr = init_thread_data_usart1(dev_dcmu->dev_type->link_inst, MOD_DCMU_NORTH);
    upload_manage_init();
    trace_rs485_debug();
    init_rs485_data_flag();
    return north_mgr;
}

/* 收发线程 */
void usart1_comm_thread(void *param) {
    dcmu_mgr_t* north_mgr = (dcmu_mgr_t*)param;
    thread_monitor_register("usart1");
    while (is_running(TRUE)) {
        thread_monitor_update_heartbeat();
        if (RT_EOK == rt_sem_take(&(north_mgr->link_inst->rx_sem), THREAD_TICK * 5)) {
            s_com_time_out_count_usart1 = 0;
            if (FAILURE == linker_recv(s_north_dev_inst[0], north_mgr->cmd_buff)) {
                continue;
            }   
            handle_received_data_usart1(north_mgr);
            trace_rs485_debug();
        } 
        if(s_com_time_out_count_usart1 > 10) {
            s_com_time_out_count_usart1 = 0;
            north_mgr->link_inst->r_cache.index = 0;
            north_mgr->link_inst->r_cache.len = 0;
        }
        s_com_time_out_count_usart1++;
    }
}