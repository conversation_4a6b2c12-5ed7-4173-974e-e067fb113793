#include "4g_default_netif.h"
#include "utils_rtthread_security_func.h"
#include "lwip/netif.h"
#include "netdev.h"


int switch_default_netif(const char* dev)
{
    char name[NETIF_NAME_LEN_MAX] = {0};

    if (dev == RT_NULL)
    {
        return -RT_EINVAL;
    }

    for (unsigned char i = 0; i < 0xFF; i++)
    {
        struct netif * netif_dev = netif_get_by_index(i);
        if (netif_dev == NULL)
        {
            continue;
        }
        if (rt_strncmp(netif_dev->name, dev, sizeof(netif_dev->name)) == 0)
        {
            rt_snprintf_s(name, NETIF_NAME_LEN_MAX, "%s%d", dev, netif_dev->num);
            struct netif *pp_dev = netif_find(name);
            if (pp_dev == NULL)
            {
                return -RT_ERROR;
            }
            netif_set_default(pp_dev);
            break;
        }
    }

    return RT_EOK;
}

