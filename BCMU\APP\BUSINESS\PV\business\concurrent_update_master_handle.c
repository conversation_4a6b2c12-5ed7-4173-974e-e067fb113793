#include "concurrent_update_master_handle.h"
#include "data_type.h"
#include "update_download_manage.h"
#include "protocol_remote_download.h"
#include "update_manage.h"
#include "utils_data_transmission.h"
#include "utils_rtthread_security_func.h"
#include "download.h"
#include "sps.h"
#include "cmd.h"
#include "partition_def.h"
#include "utils_time.h"
#include "concurrent_update_master.h"
#include "ee_public_info.h"
#include "thread_id.h"
#include "utils_heart_beat.h"
#include "north_update_utils.h"

static int s_frame_no = 0;
unsigned char s_tran_complete_flag = 3;   // 表示初始态
unsigned short s_loss_min_frame_no = 0;
static update_file_attr_t s_file_info;                       // 首发帧文件信息
static download_trig_ctr_inf_t s_parallel_update_trig_info = {0};
static int s_concurrent_process_flag = 0;     // 初始触发阶段(后期使用宏替换), 1: 
rt_timer_t g_concurrent_update_timer = NULL;
static unsigned char s_ack_addr_index = 1;
static cmd_handle_register_t s_master_download_cmd_handle[] = {
    {DEV_DAC_NORTH_DOWNLOAD_MASTER, DOWNLOAD_CONCURRENT_DATA_TRIG,    CMD_TYPE_NO_POLL, parse_concurrent_master_trig, pack_concurrent_master_trig},
    {DEV_DAC_NORTH_DOWNLOAD_MASTER, DOWNLOAD_CONCURRENT_DATA,         CMD_TYPE_NO_POLL, parse_concurrent_master_data, pack_concurrent_master_data},
    {DEV_DAC_NORTH_DOWNLOAD_MASTER, DOWNLOAD_CONCURRENT_ACK_FRAME,    CMD_TYPE_NO_POLL, parse_concurrent_update_ack_frame, pack_concurrent_update_ack_frame},
    {DEV_DAC_NORTH_DOWNLOAD_MASTER, DOWNLOAD_CONCURRENT_UPDATE_FRAME, CMD_TYPE_NO_POLL, parse_concurrent_update_frame, pack_concurrent_update_frame},
};
int get_concurrent_update_process_status()
{
    return s_concurrent_process_flag;
}

int set_concurrent_update_process_status(int status)
{
    s_concurrent_process_flag = status;
    return SUCCESSFUL;
}

int clear_concurrent_update_trig_info()
{
    s_parallel_update_trig_info.trig_success = FALSE;
    s_parallel_update_trig_info.trig_times = 0;
    return SUCCESSFUL;
}

int get_concurrent_update_trig_flag()
{
    return s_parallel_update_trig_info.trig_success;
}

int get_concurrent_update_slave_trig_times()
{
    return s_parallel_update_trig_info.trig_times;
}

int fill_update_file_info(unsigned char* file_name, unsigned int file_size, unsigned int file_crc)
{
    rt_memset_s(&s_file_info, sizeof(update_file_attr_t), 0, sizeof(update_file_attr_t));
    rt_memcpy_s(s_file_info.file_name, FILE_NAME_LEN, file_name, FILE_NAME_LEN);
    time_base_t time = {};
    get_time(&time);
    char file_time[64] = {};
    rt_snprintf_s(file_time, 64, "%d-%d-%d %d:%d:%d", time.year, time.month, time.day, time.hour, time.minute, time.second);
    rt_memcpy_s(s_file_info.file_time, FILE_TIME_LEN, file_time, FILE_TIME_LEN);
    s_file_info.total_leng = file_size;
    s_file_info.total_frames = (s_file_info.total_leng + CONCURRENT_UPDATE_DATA_LEN - 1) / CONCURRENT_UPDATE_DATA_LEN + 1;
    s_file_info.filecrc = file_crc;
    rt_kprintf("!!!!!!!!!!!!!!!!!!!!!!!!!filecrc:%d\n",s_file_info.filecrc);
    s_file_info.param_type = 0;
    return SUCCESSFUL;
}

int get_update_file_frames()
{
    return s_file_info.total_frames;
}

int parse_concurrent_master_trig(void* dev_inst, void *cmd_buf)
{
    update_app_info_t update_info = {0};
    s_parallel_update_trig_info.trig_times++;
    // rt_kprintf("!!!!!!!!!!!!!!!parse_concurrent_master_trig trig time:%d\n",s_parallel_update_trig_info.trig_times);
    if(s_parallel_update_trig_info.trig_times >= 3)
    {
        s_parallel_update_trig_info.trig_success = TRUE;
        s_parallel_update_trig_info.trig_times = 0;
        // rt_kprintf("s_parallel_update_trig_info.trig_success!!!!!!!!!!!!!!!\n");
    }

    handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char*)&update_info, sizeof(update_info), MQTT_UPDATE_INFO_OFFSET);
    update_info.is_enter_update[((dev_inst_t*)dev_inst)->dev_addr - 1] = TRUE;       // 保证从机确实进行升级流程了
    handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&update_info, sizeof(update_info), MQTT_UPDATE_INFO_OFFSET);
    rt_timer_stop(g_concurrent_update_timer);
    return SUCCESSFUL;
}

int pack_concurrent_master_trig(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL || dev_inst == NULL)
    {
        return FAILURE;
    }

    //{0x7e,                        [ADDR_HIGH][ADDR_LOW],                                        0x30,0x30,  0x41,0x41,0x32,0x32,0x30,0x31,0x34,0x30,0x45,0x45,0x35,0x35,0x35,0x41,0x41,0x35,0x41,0x41,0x0d};
    unsigned char trig_info_req[] = {0x30, 0x32, 0x41, 0x41, 0x32, 0x32, 0x30, 0x31, 0x34, 0x30, 0x45, 0x45, 0x35, 0x35, 0x35, 0x41, 0x41, 0x35, 0x41, 0x41};
    rt_memcpy_s(tran_cmd_buf->buf, sizeof(trig_info_req), trig_info_req, sizeof(trig_info_req));
    tran_cmd_buf->data_len = sizeof(trig_info_req);
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;
    rt_timer_start(g_concurrent_update_timer);
    // rt_kprintf ("pack_concurrent_master_trig|addr:%d \n", addr);
    return SUCCESSFUL;
}

int parse_concurrent_master_data(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    // 入参校验
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL || dev_inst == NULL)
    {
        return FAILURE;
    }
    s_loss_min_frame_no = ((download_cmd_head_t*)tran_cmd_buf->cmd->req_head)->fn2;    // 期望帧号  
    // rt_kprintf("parse_master_update_data|g_fn2:%d\n", g_fn2);
    if(s_file_info.total_frames == s_loss_min_frame_no)
    {
        set_concurrent_update_ack_info(0, 0);
        send_concurrent_update_cmd(((dev_inst_t*)dev_inst)->dev_addr, DOWNLOAD_CONCURRENT_UPDATE_FRAME);
        return NO_NEED_REPONSE;
    }
    if(get_concurrent_update_process_status() == 2)
    {
        rt_timer_stop(g_concurrent_update_timer);
    }
    return SUCCESSFUL;
}

int pack_concurrent_data_frame(cmd_buf_t* tran_cmd_buf, int frame_no)
{
    //数据帧 req 段
    download_cmd_head_t* proto_head = (download_cmd_head_t*)tran_cmd_buf->cmd->ack_head;      
    proto_head->fn1 = 0;
    proto_head->fn2 = frame_no;
    proto_head->cid = 0x20;

    //数据帧 data 段
    unsigned int file_offset = 0;
    unsigned int actual_len = 0;
    part_data_t part_data = {0};
    file_offset = (frame_no - 1) * CONCURRENT_UPDATE_DATA_LEN;
    actual_len = (s_file_info.total_leng - file_offset) > CONCURRENT_UPDATE_DATA_LEN ? CONCURRENT_UPDATE_DATA_LEN : (s_file_info.total_leng - file_offset);

    part_data.buff = (unsigned char *)tran_cmd_buf->buf;
    part_data.len = actual_len;                                                  // 默认单帧为 1024 字节
    part_data.offset = file_offset;
    tran_cmd_buf->data_len = actual_len;
    if(strcmp(s_file_info.file_name, "pv_diff.bin") == 0)
    {
        rt_snprintf_s(part_data.name, sizeof(part_data.name), UPDATE_PATCH_PART);
    }
    else
    {
        rt_snprintf_s(part_data.name, sizeof(part_data.name), UPDATE_DOWNLOAD_PART);
    }
    rt_kprintf("pack_concurrent_data_frame|flash_name:%s, file_offset:%ld, fn2:%ld, actual_len:%d\n", part_data.name, file_offset, frame_no, actual_len);
    if (SUCCESSFUL != storage_process(&part_data, read_opr)) {
        return FAILURE;
    }
    // rt_kprintf("!!!!!!start\n");
    // print_com_buff(tran_cmd_buf->buf, actual_len);
    // rt_kprintf("!!!!!!end\n");
    return SUCCESSFUL;
}

int pack_concurrent_first_frame(cmd_buf_t* tran_cmd_buf)
{
    //首帧 req 段
    download_cmd_head_t* proto_head = (download_cmd_head_t*)tran_cmd_buf->cmd->ack_head;
    proto_head->fn1 = 0;
    proto_head->fn2 = 0;
    proto_head->cid = 0x20;
    //首帧 data 段
    put_int16_to_buff(tran_cmd_buf->buf, s_file_info.total_frames);                                            // 总帧数
    put_uint32_to_buff(&tran_cmd_buf->buf[2], s_file_info.total_leng);                                         // 文件长度
    rt_memcpy_s(&tran_cmd_buf->buf[6], UPDATE_FILE_EXTEND_NAME_LEN, s_file_info.file_name, UPDATE_FILE_EXTEND_NAME_LEN);                      // 文件名称
    rt_memcpy_s(&tran_cmd_buf->buf[6 + UPDATE_FILE_EXTEND_NAME_LEN], UPDATE_FILE_TIME_LEN, s_file_info.file_time, UPDATE_FILE_TIME_LEN);   // 文件时间
    put_uint32_to_buff(&tran_cmd_buf->buf[6 + UPDATE_FILE_EXTEND_NAME_LEN + UPDATE_FILE_TIME_LEN], s_file_info.filecrc); 
    // tran_cmd_buf->buf[10 + UPDATE_FILE_EXTEND_NAME_LEN + UPDATE_FILE_TIME_LEN] = 0;                               // 参数类型1 + 预留字节3

    tran_cmd_buf->data_len = DOWNLOAD_UPDATE_FIRST_EXTEND_FRAME_LEN;
    rt_kprintf("pack_concurrent_first_frame|len:%ld, frames:%ld, file_name:%s, file_time:%s,filecrc:%d\n", 
    s_file_info.total_leng , s_file_info.total_frames, s_file_info.file_name, s_file_info.file_time, s_file_info.filecrc);
    return SUCCESSFUL;
}

int pack_concurrent_master_data(void* dev_inst, void *cmd_buf)
{
    if(get_concurrent_update_process_status() == 2)
    {
        s_frame_no = s_loss_min_frame_no;
    }
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;

    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL || dev_inst == NULL)
    {
        return FAILURE;
    }

    if(s_frame_no == 0)
    {
        //首帧处理
        pack_concurrent_first_frame(tran_cmd_buf);
    }
    else
    {
        //数据帧处理
        pack_concurrent_data_frame(tran_cmd_buf, s_frame_no);

    }
    s_frame_no = (s_frame_no + 1) % s_file_info.total_frames;
    if(get_concurrent_update_process_status() == 2)
    {
        rt_timer_start(g_concurrent_update_timer);
    }
    return SUCCESSFUL;
}

int pack_concurrent_update_ack_frame(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;

    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL || dev_inst == NULL)
    {
        return FAILURE;
    }

    //数据帧 req 段
    download_cmd_head_t* proto_head = (download_cmd_head_t*)tran_cmd_buf->cmd->ack_head;      // 确认下这个地方是req_head 还是ack_head
    proto_head->fn1 = 0;
    proto_head->fn2 = 0;
    proto_head->cid = 0x21;

    //数据帧 data 段
    tran_cmd_buf->data_len = 0;
    rt_timer_start(g_concurrent_update_timer);
    return SUCCESSFUL;
}


int get_concurrent_update_ack_info(unsigned char *flag, unsigned short* frame_no)
{
    *flag = s_tran_complete_flag;
    *frame_no = s_loss_min_frame_no;
    rt_kprintf("get_concurrent_update_ack_info|flag:%d, frame_no:%d\n", s_tran_complete_flag, s_loss_min_frame_no);
    return SUCCESSFUL;
}

int set_concurrent_update_ack_info(unsigned char flag, unsigned short frame_no)
{
    s_tran_complete_flag = flag;
    s_loss_min_frame_no = frame_no;
    rt_kprintf("set_concurrent_update_ack_info|flag:%d, frame_no:%d\n", s_tran_complete_flag, s_loss_min_frame_no);
    return SUCCESSFUL;
}

int parse_concurrent_update_ack_frame(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;

    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL || dev_inst == NULL)
    {
        return FAILURE;
    }
    
    unsigned char flag = tran_cmd_buf->buf[0];
    unsigned short frame_no = (tran_cmd_buf->buf[1] << 8) | tran_cmd_buf->buf[2];

    set_concurrent_update_ack_info(flag, frame_no);    // 确认下 高低字节位
    set_concurrent_update_process_status(2);   //确认阶段完成，进入补帧阶段
    rt_timer_stop(g_concurrent_update_timer);
    if(flag == 0)  // 不补帧的时候，直接触发升级
    {
        send_concurrent_update_cmd(((dev_inst_t*)dev_inst)->dev_addr, DOWNLOAD_CONCURRENT_UPDATE_FRAME);
        return NO_NEED_REPONSE;
    }
    return SUCCESSFUL;
}

int parse_concurrent_update_frame(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    s_frame_no = 0;
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL || dev_inst == NULL)
    {
        return FAILURE;
    }
    rt_kprintf("parse_concurrent_update_frame stop timer!!!\n");
    rt_timer_stop(g_concurrent_update_timer);
    return SUCCESSFUL;
}

int pack_concurrent_update_frame(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;

    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL || dev_inst == NULL)
    {
        return FAILURE;
    }
    //数据帧 req 段
    download_cmd_head_t* proto_head = (download_cmd_head_t*)tran_cmd_buf->cmd->ack_head;      // 确认下这个地方是req_head 还是ack_head
    proto_head->fn1 = 0;
    proto_head->fn2 = 0;
    proto_head->cid = 0x22;
    rt_memcpy_s(&tran_cmd_buf->buf[0], UPDATE_FILE_EXTEND_NAME_LEN, s_file_info.file_name, UPDATE_FILE_EXTEND_NAME_LEN);

    //数据帧 data 段
    tran_cmd_buf->data_len = 256;
    rt_kprintf("pack_concurrent_update_frame start timer!!!\n");
    rt_timer_start(g_concurrent_update_timer);
    set_concurrent_update_process_status(3);
    return SUCCESSFUL;
}


int send_concurrent_update_cmd(char addr, unsigned short cmd_id)
{
    dev_inst_t* download_inst = init_dev_inst(DEV_DAC_NORTH_DOWNLOAD_MASTER);
    RETURN_VAL_IF_FAIL(download_inst != NULL, FAILURE);
    download_inst->dev_addr = addr;
    rt_kprintf("send_concurrent_update_cmd %d device update\n", download_inst->dev_addr);
    return send_dest_cmd(download_inst, cmd_id);
}

int master_download_init() 
{
    short i = 0;
    for(i = 0; i < sizeof(s_master_download_cmd_handle)/sizeof(s_master_download_cmd_handle[0]); i++) 
    {
        register_cmd_handle(&s_master_download_cmd_handle[i]);
    }
    g_concurrent_update_timer = rt_timer_create("concurrentTimer", concurrent_timeout, RT_NULL, 10000, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
    if(g_concurrent_update_timer == NULL)
    {
        return FAILURE;
    }
    return SUCCESSFUL;
}

void trig_frame_timeout_process(dev_inst_t* download_inst)
{
    static unsigned char s_trig_count = 0;
    if(get_concurrent_update_process_status() == 0)  // 触发逻辑
    {
        s_trig_count++;
        if(s_trig_count >= 3)
        {
            //不同设备，清除上一个设备的触发信息，切换设备
            rt_timer_stop(g_concurrent_update_timer);
            rt_kprintf("trig timeout!!!!! | start_next_device_update\n");
            if(start_next_device_update() == FAILURE)
            {
                set_concurrent_update_ack_info(3,0);
                deal_update_timer();
            }
            s_trig_count = 0;
            return ;
        }
        //同一个设备，三次触发内
        rt_kprintf("trig timeout!!!!!\n");
        send_concurrent_update_cmd(download_inst->dev_addr, DOWNLOAD_CONCURRENT_DATA_TRIG);
    }
}

void ack_frame_timeout_process(dev_inst_t* download_inst)
{
    static unsigned char s_ack_count = 0;
    if(get_concurrent_update_process_status() == 1)  // 补帧阶段
    {
        //同一个设备，三次确认
        s_ack_count++;
        if(s_ack_count >= 3)
        {
            rt_kprintf("s_ack_count timeout change device!!!!!\n");
            //不同设备，切换设备!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
            rt_timer_stop(g_concurrent_update_timer);
            s_ack_count = 0;
            if(supply_frames_process(download_inst) == FAILURE)
            {
                set_concurrent_update_ack_info(3,0);
                deal_update_timer();
            }
            return ;
        }
        rt_kprintf("ack frame timeout!!!!!\n");
        send_concurrent_update_cmd(download_inst->dev_addr, DOWNLOAD_CONCURRENT_ACK_FRAME);
    }
}

void ack_data_frame_timeout_process(dev_inst_t* download_inst)
{
    static unsigned char s_data_count = 0;
    static unsigned int s_last_fn2 = 0;
    if(get_concurrent_update_process_status() == 2)  // 确认阶段的数据帧阶段
    {
        s_data_count++;
        if(s_frame_no != s_last_fn2)
        {
            s_data_count = 0;
        }
        s_last_fn2 = s_frame_no;
        s_data_count++;
        if(s_data_count >= 3)
        {
            rt_kprintf("s_data_count timeout change device!!!!!\n");
            //不同设备，切换设备!!!!!!!!!!!!!!!!!!!------切完设备后，最后一帧的响应包解析成功之后，设为3
            rt_timer_stop(g_concurrent_update_timer);
            s_data_count = 0;
            s_last_fn2 = 0;
            if(supply_frames_process(download_inst) == FAILURE)
            {
                set_concurrent_update_ack_info(3,0);
                deal_update_timer();
            }
            return ;
        }
        rt_kprintf("s_data_count timeout!!!!!\n");
        send_concurrent_update_cmd(download_inst->dev_addr, DOWNLOAD_CONCURRENT_DATA);//这个地方需要判断是不是同一帧数据帧的补帧
    }
}

void update_frame_timeout_process(dev_inst_t* download_inst)
{
    static unsigned int s_update_count = 0;
    if(get_concurrent_update_process_status() == 3)  // 升级确认阶段
    {
        //同一个设备，三次确认
        s_update_count++;
        if(s_update_count >= 3)
        {
            rt_kprintf("s_update_count timeout change device!!!!\n");
            //不同设备，切换设备!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
            rt_timer_stop(g_concurrent_update_timer);
            s_update_count = 0;
            if(supply_frames_process(download_inst) == FAILURE)
            {
                set_concurrent_update_ack_info(3,0);
                //处理升级的定时器
                deal_update_timer();
            }
            return ;
        }
        rt_kprintf("s_update_count timeout!!!!!\n");
        //同一个设备，三次确认
        send_concurrent_update_cmd(download_inst->dev_addr, DOWNLOAD_CONCURRENT_UPDATE_FRAME);
    }
}

void concurrent_timeout()
{
    dev_inst_t* download_inst = init_dev_inst(DEV_DAC_NORTH_DOWNLOAD_MASTER);
    RETURN_IF_FAIL(download_inst != NULL);
    trig_frame_timeout_process(download_inst);
    ack_frame_timeout_process(download_inst);
    ack_data_frame_timeout_process(download_inst);
    update_frame_timeout_process(download_inst);
}

int deal_trig_frame_after_rcv_pkt(unsigned short dev_addr)
{
    int slave_update_trig_cnt = 0;
    if(TRUE == get_concurrent_update_trig_flag() && 0 ==  get_concurrent_update_process_status())
    {
        rt_kprintf("start next trig\n");
        start_next_device_update();
        return SUCCESSFUL;
    }
    slave_update_trig_cnt = get_concurrent_update_slave_trig_times();
    rt_kprintf("north_main|slave_update_trig_cnt!!!!!!!!!!!!!!!!:%d\n", slave_update_trig_cnt);
    if((slave_update_trig_cnt > 0 && slave_update_trig_cnt < DOWNLOAD_TRIG_TIMES) && 0 == get_concurrent_update_process_status())
    {
        send_concurrent_update_cmd(dev_addr, DOWNLOAD_CONCURRENT_DATA_TRIG);// 主机对从机的三次触发
        return SUCCESSFUL;
    }
    return FAILURE;
}

int deal_ack_frame_data_frame_after_rcv_pkt(dev_inst_t* download_inst)
{
    if(SUCCESSFUL == supply_frames_process(download_inst))
    {
        return SUCCESSFUL;
    }
    set_concurrent_update_ack_info(3,0);
    //处理升级的定时器
    deal_update_timer();
    return SUCCESSFUL;
}

int master_deal_data_after_recv_pkt()
{
    unsigned char flag = 0;
    unsigned short frame_no = 0;
    dev_inst_t* download_inst = init_dev_inst(DEV_DAC_NORTH_DOWNLOAD_MASTER);    // 需要优化
    RETURN_VAL_IF_FAIL(download_inst != NULL, FAILURE); 

    rt_kprintf("north_main|rcv positive cmd rsp\n");
    RETURN_VAL_IF_FAIL(FAILURE == deal_trig_frame_after_rcv_pkt(download_inst->dev_addr), SUCCESSFUL);

    if(0 != get_concurrent_update_process_status())
    {
        get_concurrent_update_ack_info(&flag, &frame_no);
        if(flag == 1)
        {
            send_concurrent_update_cmd(download_inst->dev_addr, DOWNLOAD_CONCURRENT_DATA);   // 第一个设备补帧
        }
        else if(flag == 0)     //切换设备
        {
            deal_ack_frame_data_frame_after_rcv_pkt(download_inst);
        }
    }
    return SUCCESSFUL;
}

int supply_frames_process(dev_inst_t* download_inst)
{
    unsigned char* addr = get_concurrent_addr();
    for(int loop = s_ack_addr_index; loop < 10; loop ++)
    {
        if(addr[loop] == 0)
        {
            break;
        }
        download_inst->dev_addr = addr[loop];
        s_ack_addr_index ++;
        send_concurrent_update_cmd(download_inst->dev_addr, DOWNLOAD_CONCURRENT_ACK_FRAME); 
        set_concurrent_update_process_status(1);   // 进入确认阶段
        return SUCCESSFUL;
    }
    return FAILURE;
}

int clear_concurrent_index()
{
    s_ack_addr_index = 1;
    return SUCCESSFUL;
}



int enter_broadcast_frame_process(dev_inst_t* download_inst, unsigned char concurrent_addr)
{
    int frame_loop = 0;
    download_inst->dev_addr = 0;   // 广播帧的地址为0
    for(frame_loop = 0; frame_loop < get_update_file_frames(); frame_loop ++)
    {
        send_concurrent_update_cmd(download_inst->dev_addr, DOWNLOAD_CONCURRENT_DATA);
        rt_thread_delay(50);
        thread_beat_go_on(THREAD_PV_INVERTER);
    }
    rt_thread_delay(3000);
    if(frame_loop == get_update_file_frames())
    {
        download_inst->dev_addr = concurrent_addr;   // 先给第一个设备进行补帧
        send_concurrent_update_cmd(download_inst->dev_addr, DOWNLOAD_CONCURRENT_ACK_FRAME);   
        set_concurrent_update_process_status(1);   // 进入确认阶段
    }
    return SUCCESSFUL;
}

int start_next_device_update()      
{
    static int loop = 0;
    static int loop1 = 0;
    static int s_trig_succ_flag = FALSE;
    dev_inst_t* download_inst = init_dev_inst(DEV_DAC_NORTH_DOWNLOAD_MASTER);
    unsigned char* concurrent_addr = get_concurrent_addr();
    parallel_update_info_t* parallel_update_info =  get_parallel_update_info();
    RETURN_VAL_IF_FAIL(download_inst != NULL, FAILURE);
    if(TRUE == get_concurrent_update_trig_flag())
    {
        s_trig_succ_flag = TRUE;
    }
    clear_concurrent_update_trig_info();
    for(; loop < parallel_update_info->need_update_slave_num; loop++)
    {
        if(parallel_update_info->cur_slave_addr != parallel_update_info->slave_addr[loop])
        {
            rt_kprintf("cur:%d,slave:%d\n", parallel_update_info->cur_slave_addr, parallel_update_info->slave_addr[loop]);
            concurrent_addr[loop1] = parallel_update_info->slave_addr[loop];
            download_inst->dev_addr = parallel_update_info->slave_addr[loop];
            parallel_update_info->cur_slave_addr = parallel_update_info->slave_addr[loop];
            set_parallel_update_shield_com_flag(TRUE);   // 并机升级 屏蔽 并机四遥
            send_concurrent_update_cmd(download_inst->dev_addr, DOWNLOAD_CONCURRENT_DATA_TRIG);
            loop1++;


            return SUCCESSFUL;
        }
    }

    if(s_trig_succ_flag != TRUE)
    {
        loop = 0;
        loop1 = 0;
        set_parallel_update_shield_com_flag(FALSE);
        return FAILURE;
    }

    // 所有的设备触发完毕，主机进入发广播帧阶段
    enter_broadcast_frame_process(download_inst, concurrent_addr[0]);

    loop = 0;
    loop1 = 0;
    s_trig_succ_flag = FALSE;//此次升级结束
    
    return SUCCESSFUL;
}