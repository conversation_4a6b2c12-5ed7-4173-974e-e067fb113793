#include "south_dev_common.h"
#include "realdata_id_in.h"
#include "south_dev_modbus.h"


/* 命令请求头 */
static modbusrtu_cmd_head_t cmd_req[] = {
    {READ_HOLD_REG},
    {READ_INPUT_REG},
    {WRITE_SINGLE_REG},
};

/* 命令应答头 */
static modbusrtu_cmd_head_t cmd_ack[] = {
    {READ_HOLD_REG},
    {READ_INPUT_REG},
    {WRITE_SINGLE_REG},
};

static data_info_id_verison_t parse_real_data[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},//数据的字节长度
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_COMPRESSOR_RUN_SPEED},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_VFD_COMPRESSOR_VOL},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_VFD_FEEDBACK_FREQUECY},
    {SEG_DATA, type_short,          ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_VFD_POWER_MODULE_TEMP},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_VFD_COMPRESSOR_POWER},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_VFD_COMPRESSOR_PHASE_CURR},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_VFD_ALARM_CODE},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_VFD_WARNING_CODE},
};

static data_info_id_verison_t parse_run_time[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_VFD_ACCUMULATE_RUN_TIME},
};

static data_info_id_verison_t parse_run_status_setval[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_VFD_RUN_STATUS_SETVAL},
};

static data_info_id_verison_t parse_run_status_realval[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
};

static data_info_id_verison_t parse_startfre_holdtime[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_VFD_START_FRE_HOLD_TIME},
};

static data_info_id_verison_t parse_start_fre[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_VFD_START_FREQUENCY},
};

static data_info_id_verison_t parse_first_acce_time[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, DXCB_DATA_ID_VFD_FIRST_ACCE_TIME},
};

static cmd_parse_info_id_verison_t cmd_parse_info[] RAM_SECTION =
{
    {parse_real_data, sizeof(parse_real_data)/sizeof(data_info_id_verison_t)},
    {parse_run_time, sizeof(parse_run_time)/sizeof(data_info_id_verison_t)},
    {parse_run_status_setval, sizeof(parse_run_status_setval)/sizeof(data_info_id_verison_t)},
    {parse_run_status_realval, sizeof(parse_run_status_realval)/sizeof(data_info_id_verison_t)},
    {parse_startfre_holdtime, sizeof(parse_startfre_holdtime)/sizeof(data_info_id_verison_t)},
    {parse_start_fre, sizeof(parse_start_fre)/sizeof(data_info_id_verison_t)},
    {parse_first_acce_time, sizeof(parse_first_acce_time)/sizeof(data_info_id_verison_t)},
};

/* 命令总表,必须以空字符串""结束 */
static cmd_t no_poll_cmd_tab[] = {
    {DFS_SET_SWITCH, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data,NULL,NULL},
    {DFS_SET_FRE, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data,NULL,NULL},
    {DFS_SET_STARTFRE_HOLDTIME, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data, NULL,NULL},
    {DFS_SET_START_FRE, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data, NULL,NULL},
    {DFS_SET_FIRST_ACCE_TIME, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_write_modbus_data, parse_comm_data, NULL,NULL},
    {0},
};

static cmd_t poll_cmd_tab[] = {
    {READ_DFS_HOLD_REG, CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[0]},
    // {READ_DFS_INPUT_REG, CMD_POSITIVE, &cmd_req[1], &cmd_ack[1], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[0]},
    {DFS_RUN_TIME_03, CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[1]},
    // {DFS_RUN_TIME_04, CMD_POSITIVE, &cmd_req[1], &cmd_ack[1], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[1]},
    {DFS_SET_RUN_STATUS_03, CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[2]},
    // {DFS_SET_RUN_STATUS_04, CMD_POSITIVE, &cmd_req[1], &cmd_ack[1], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[2]},
    {DFS_REAL_RUN_STATUS_03, CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[3]},
    // {DFS_REAL_RUN_STATUS_04, CMD_POSITIVE, &cmd_req[1], &cmd_ack[1], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[3]},
    {DFS_START_FRE_HOLD_TIME, CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[4]},
    {DFS_START_FRE, CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[5]},
    {DFS_FIRST_ACCE_TIME, CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_read_modbus_data, NULL,NULL,&cmd_parse_info[6]},
    {0},
};


int update_vfd_dfs_dev_type(dev_inst_t* dev_inst, char dev_num, char is_inter_fan)
{
    return change_dev_type_info_by_diff_brand(dev_inst, SOUTH_VFD_TYPE, is_inter_fan, dev_num, no_poll_cmd_tab, poll_cmd_tab);
}


