/*******************************************************************************
  * @file        battery_management.c
  * @version     v1.0.0
  * @copyright   COPYRIGHT &copy; 2023 CSG
  * <AUTHOR>
  * @date        2023-4-27
  * @brief
  * @attention
  * Modification History
  * DATE         DESCRIPTION
  * ------------------------
  * - 2023-4-27  dongmengling Created
*******************************************************************************/
#include <string.h>
#include <stdlib.h>
#include "battery_management.h"
#include "dev_bmu.h"
// #include "dev_dc_dc.h"
#include "utils_data_valid.h"
#include "data_type.h"
#include "utils_math.h"
#include "batt_sox_calc.h"
#include "msg.h"
#include "msg_id.h"
#include "addr_distribution.h"
#include "softbus.h"
#include "utils_server.h"
#include "cell_balance.h"
#include "utils_time.h"
#include "battery_ctrl.h"
#include "utils_thread.h"
#include "utils_rtthread_security_func.h"

#define CHARGE_MAX_MINUTE  600  //以后增加参数

static cluster_topology_t s_topo;
static dev_inst_t* dev_BMU = NULL;
static dev_inst_t* dev_DC_DC = NULL;

static batt_deal_info_t s_batt_deal[BATT_MOD_NUM];
static batt_result_t s_batt_out[BATT_MOD_NUM];

static module_msg_t msg_rcv = {.dest = MOD_BATT_MANAGE};

//需电池专家修改提供
const static SOC_cal_info_attr_t s_SOC_cal_info_attr[] = {
    {42.45, 0},  {44.265, 1}, {45.45, 2}, {46.26, 3}, {46.98, 4},  {47.505, 5},
    {47.715, 6}, {47.79, 7},  {47.82, 8}, {47.835, 9},{47.85, 10}, {47.895, 12},
    {48.045, 14},{48.24, 16}, {48.39, 18},{48.54, 20},{48.66, 22}, {48.72, 24},
    {48.825, 26},{48.93, 28}, {48.99, 30},{49.8, 68}, {49.815, 70},{49.845, 75},
    {49.875, 98},{49.905, 99},{51, 100}
};

static void process_data_change(const rt_msg_t pMsg){

}

static msg_map batt_manage_msg_map[] =
{
    {DATA_CHANGE_MSG_ID,   process_data_change},          
};

static void init_batt_save(void);
static void load_batt_deal(void);
static void deal_batt_result(void);
static void process_recv_msg(void);
static void exe_business_hearbeat(void);
static void calc_all_batt_cap(void);
static void calc_batt_cap(unsigned char bmu_addr, unsigned char cluster_index);
static unsigned char if_need_discharge_SOC_cal(void);
static unsigned char discharge_from_full(void);
static void accumulate_discharge_cap(float batt_curr, float* cap, unsigned long time_diff);
static unsigned char is_cell_full(unsigned char bmu_addr);
Static float get_cell_ocv(float cell_temp,  float* cell_vol, float batt_curr);
static float get_cell_dcr(float cell_temp);
static unsigned char is_batt_charge_time_full(unsigned char cluster_index);
static unsigned char is_charge_too_long(unsigned char cluster_index);
static unsigned char read_eep_cap( batt_save_t* batt_save);
static unsigned char write_eep_cap( batt_save_t* batt_save, unsigned char index);
static void check_save_eefrom(void);
static void soc_calc_info_patch(float batt_vol, unsigned char bmu_addr);



/**
 * @brief  初始化SOC和SOH
 * @param[in] batt_save_data   保存的电池信息
 * @param[out] soc  
 * @param[out] soh  
 * @retval
 */
void* init_batt_manage(void * param) {
    server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(batt_manage_msg_map) / sizeof(msg_map);
    register_server_msg_map(batt_manage_msg_map, server_info);
    RETURN_VAL_IF_FAIL(init_msg_queue(MOD_BATT_MANAGE) != NULL, NULL);

    get_cluster_topology(&s_topo);
    dev_BMU = get_dev_inst(DEV_BMU);
    dev_DC_DC = get_dev_inst(DEV_DC_DC);

    rt_memset_s(&s_batt_deal, BATT_MOD_NUM*sizeof(batt_deal_info_t), 0, BATT_MOD_NUM*sizeof(batt_deal_info_t));
    rt_memset_s(&s_batt_out, BATT_MOD_NUM*sizeof(batt_result_t), 0, BATT_MOD_NUM*sizeof(batt_result_t));

    init_batt_save();
    
    return NULL;
}


static void init_batt_save(void)
{
    batt_save_t batt_save_data[BATT_MOD_NUM];
    unsigned char i = 0;

    rt_memset(&batt_save_data[0], 0, BATT_MOD_NUM*sizeof(batt_save_t));
    
    //从eefrom中获取保存的电池信息填入batt_save_data
    if (read_eep_cap(&batt_save_data[0]) != SUCCESSFUL)
    {
        rt_memset(&batt_save_data[0], 0, BATT_MOD_NUM*sizeof(batt_save_t));
        for(i = 0; i < BATT_MOD_NUM; i++) {
            batt_save_data[i].batt_cap = 0.5 * BATT_CAP;
            get_time(&batt_save_data[i].last_calc_calender_time);
            s_batt_deal[i].save_eeprom = TRUE;
        }
    }
    ////软件重启以后起来不置满，不做SOH修正
    for(i = 0; i < BATT_MOD_NUM; i++) {
        init_sox(&batt_save_data[i], &s_batt_deal[i]);
    }
    
    check_save_eefrom();
}


void batt_manage_main(void *param) {
    cell_balance_process_start();
    while (is_running(TRUE)) {
        process_recv_msg();
        rt_thread_delay(1000);
        exe_business_hearbeat();

        load_batt_deal();
        calc_all_batt_cap();
        deal_batt_result();
    }
}

static void deal_batt_result(void) {
    unsigned char i = 0;

    for(i = 0; i < BATT_MOD_NUM; i++) {
        s_batt_out[i].batt_SOC = calc_soc_from_cap(&s_batt_deal[i]);
        //s_batt_out[i].batt_SOH = 
        s_batt_out[i].calender_life_decline = s_batt_deal[i].calender_life_decline;
        s_batt_out[i].cycle_life_decline = s_batt_deal[i].cycle_life_decline;
        s_batt_out[i].added_life_decline = s_batt_deal[i].added_life_decline;
    }
}

static void load_batt_deal(void) {
    time_t time_now     = get_timestamp_s();
    unsigned char i = 0;

    for(i = 0; i < BATT_MOD_NUM; i++) {
        s_batt_deal[i].second_time_diff_sec = time_now - s_batt_deal[i].second_calc_time;
        s_batt_deal[i].second_calc_time = time_now;

        if ((s_batt_deal[i].second_time_diff_sec > 3) || (s_batt_deal[i].second_time_diff_sec == 0))
        {
            s_batt_deal[i].second_time_diff_sec = 1;
        }
    }
}


static void process_recv_msg(void) {
    
    if (SUCCESSFUL != recv_msg(&msg_rcv))
        return;
    
    // switch (msg_rcv.msg_id)
    // {
    //     case /* constant-expression */:
    //         /* code */
    //         break;
        
    //     default:
    //         break;
    // }
    
    if (msg_rcv.data != NULL) {
        free(msg_rcv.data);
        msg_rcv.data = NULL;
    }
}

static void check_save_eefrom(void)
{
    unsigned char i = 0;

    for(i = 0; i < BATT_MOD_NUM; i++) {
        if (s_batt_deal[i].save_eeprom)
        {
            s_batt_deal[i].batt_cap_save = s_batt_deal[i].batt_cap;
            s_batt_deal[i].save_eeprom = FALSE;
            save_batt_info(i+1);
        }
    }
    
    return;
}

void save_batt_info(unsigned char mod_no)
{
    batt_save_t batt_save;
    rt_memset_s(&batt_save, sizeof(batt_save_t), 0x00, sizeof(batt_save_t));

    RETURN_IF_FAIL(mod_no>0 && mod_no <= BATT_MOD_NUM);

    batt_save.valid                  = TRUE;
    batt_save.batt_cap                = s_batt_deal[mod_no-1].batt_cap;
    batt_save.added_life_decline       = s_batt_deal[mod_no-1].added_life_decline;
    batt_save.last_calc_calender_time   = s_batt_deal[mod_no-1].last_calc_calender_time;
    batt_save.calender_life_decline    = s_batt_deal[mod_no-1].calender_life_decline;
    batt_save.cycle_life_decline       = s_batt_deal[mod_no-1].cycle_life_decline;

    write_eep_cap(&batt_save, mod_no);
    return;
}

static unsigned char read_eep_cap( batt_save_t* batt_save)
{
    RETURN_VAL_IF_FAIL(batt_save != NULL, FAILURE);

    //为了解决kw，后面接口实现可删除
    if (s_topo.mod_count == 15) {
        return SUCCESSFUL;
    }
    return FAILURE;
}

static unsigned char write_eep_cap( batt_save_t* batt_save, unsigned char index)
{
    RETURN_VAL_IF_FAIL(batt_save != NULL, FAILURE);

    //为了解决kw，后面接口实现可删除
    if (s_batt_deal[0].batt_cap > 70) {
        return FAILURE;
    }
    return SUCCESSFUL;
}

static void exe_business_hearbeat()
{
    static rt_uint32_t last_time = 0;
    rt_int32_t  ltdif = 0;
    rt_uint32_t curr_time = get_sys_runtime();

    if (last_time == 0) {
        last_time = curr_time;
        send_heart_beat_msg(MOD_BATT_MANAGE);
        return;
    }

    ltdif = curr_time - last_time;
    if (ltdif < 0) {
        ltdif = (~ltdif) + 1;
    }

    if (ltdif >= 5000) {
        send_heart_beat_msg(MOD_BATT_MANAGE);
        last_time = curr_time;
    }
}

static void calc_all_batt_cap(void) {
    unsigned char i, j;
    unsigned char mod_addr = 0;

    RETURN_IF_FAIL(s_topo.topo_valid == TOPO_VALID);
    
    for (j = 0; j < s_topo.cluster_count; j++) {
        for (i = 0; i < s_topo.mod_num[j]; i++) {
            mod_addr = s_topo.mod_addr[j][i];
            calc_batt_cap(mod_addr, j+1);
        }
    } 
}

/**
 * @brief  计算电池容量
 * @param[in] batt_deal  
 * @param[in] mod_addr  电池模块地址
 * @param[in] cluster_index  簇地址
 * @param[in] batt_SOC   实时数据中soc
 * @retval
 */
static void calc_batt_cap(unsigned char bmu_addr, unsigned char cluster_index)
{
    /* TODO:重新开发
    float coef = AFFECT_COEF;    //容量校正系数 充电时Kc取0.98；放电时，取1
    unsigned char discharge_SOC_cal_flag = FALSE;
    // unsigned char batt_status = BATT_STATE_CHARGE;
    unsigned char cell_over_vol_prt = 0;
    
    float   batt_real_cap;//电池实际容量
    float batt_cap = BATT_CAP;  //从参数那里获取  
    batt_real_cap = batt_cap*s_batt_deal[bmu_addr-1].batt_SOH/ 100;

    float batt_curr = 0;
    float batt_vol = 0;
    
    if (dev_BMU == NULL || dev_DC_DC == NULL) {
        dev_BMU = get_dev_inst(DEV_BMU);
        dev_DC_DC = get_dev_inst(DEV_DC_DC);
    }
    if(dev_BMU == NULL || dev_DC_DC == NULL)
        return;
    batt_curr = ((DC_DC_real_data_ana_t*)dev_DC_DC[cluster_index -1].dev_data.ana_data)->batt_curr;
    batt_vol =   ((BMU_real_data_ana_t*)dev_BMU[bmu_addr -1].dev_data.ana_data)->mod_vol_total;


    //从满充开始放电
    if (discharge_from_full())
    {
        //累积放电容量
        accumulate_discharge_cap(batt_curr, &s_batt_deal[bmu_addr-1].discharge_cap_for_celebrate, s_batt_deal[bmu_addr-1].second_time_diff_sec);
    }

    if(fabs(batt_curr) >= CURR_MIN_DET)
    {
        if (batt_curr < 0.0f)
        {
            //处于放电 
            coef = 1.00;
        }
        s_batt_deal[bmu_addr-1].batt_cap += batt_curr / 60 * coef * (s_batt_deal[bmu_addr-1].second_time_diff_sec/60.0);
    }
    else
    {
        discharge_SOC_cal_flag = if_need_discharge_SOC_cal();
        if (discharge_SOC_cal_flag)
        {
            soc_calc_info_patch(batt_vol, bmu_addr);
        }
    }

    ////不满足充满条件，则一直充电也不允许置满，防止SOH修正错误
    s_batt_deal[bmu_addr-1].batt_cap = get_valid_data(s_batt_deal[bmu_addr-1].batt_cap, batt_real_cap*BATT_SOC_MAX, batt_real_cap*BATT_SOC_MIN);

    if (batt_curr < -1.0f*CURR_MIN_DET)
    {
       // s_tBattDeal.bBatt99PercentFull  = False;  什么时候使用
    }

    //从告警获取电芯过压保护,只有簇，需补充。解决kw，后面补充后去掉
    if (cluster_index == 1) {    
        cell_over_vol_prt = 0;
    } else {
        cell_over_vol_prt = 1;
    }
   
    //终值处理
    if (cell_over_vol_prt || is_charge_too_long(cluster_index))
    {
        s_batt_deal[bmu_addr-1].batt_cap = MAX(batt_real_cap * 99.0/100.0, s_batt_deal[bmu_addr-1].batt_cap);
    }

    if (cell_over_vol_prt || is_cell_full(bmu_addr))
    {
        s_batt_deal[bmu_addr-1].batt_cap = MAX(batt_real_cap, s_batt_deal[bmu_addr-1].batt_cap);
    }

    if (fabs(s_batt_deal[bmu_addr-1].batt_cap - s_batt_deal[bmu_addr-1].batt_cap_save) >= batt_cap * CAP_CHANGE_SAVE_COEF)
    {
        s_batt_deal[bmu_addr-1].save_eeprom = TRUE;
    }
*/
    return;
}

/**
 * @brief  电压变化缓慢时计算容量补丁
 * @param[in] 
 * @retval  TRUE是    FALSE否
 */
static void soc_calc_info_patch(float batt_vol, unsigned char bmu_addr){
    unsigned char  i = 0;
    unsigned char  data_attr_num = 0;
    unsigned short batt_SOC = 50; //实时数据获取 api

    data_attr_num = ARR_SIZE(s_SOC_cal_info_attr);

    for (i = 0; i < data_attr_num; i++)
    {
        if (batt_vol < s_SOC_cal_info_attr[i].batt_vol)
        {
            if (batt_SOC > s_SOC_cal_info_attr[i].soc)
            {
                s_batt_deal[bmu_addr -1].batt_cap -= SOC_CAL_DISCHG_CURR*s_batt_deal[bmu_addr -1].second_time_diff_sec/3600.0;//默认2A放电电流
            }
            break;
        }
    }
}


/*待补充*/
static unsigned char if_need_discharge_SOC_cal(void){
    //为了解决kw，后面实现接口可删除
    if (s_topo.topo_valid == TOPO_INVALID) {
        return TRUE;
    }
    return FALSE;
}


/**
 * @brief  是否从满容量开始放电
 * @param[in] 
 * @retval  TRUE是    FALSE否
 */
static unsigned char discharge_from_full(void) {
    //为了解决kw，后面实现接口可删除
    if (s_topo.topo_valid == TOPO_INVALID) {
        return FALSE;
    }
    return TRUE;
}

/**
 * @brief  累积放电容量
 * @param[in]    batt_curr  电池电流
 * @param[in]    time_diff  累积时间
 * @param[out]    cap  容量
 * @retval       电芯开路电压
 */
static void accumulate_discharge_cap(float batt_curr, float* cap, unsigned long time_diff)//安时积分
{
    if (batt_curr <= -1.0f*CURR_MIN_DET)
    {
        ////取正数
        *cap -= batt_curr*time_diff/3600.0;
    }
}

/*
*电芯是否充满 
*/
static unsigned char is_cell_full(unsigned char bmu_addr) {
    //为了解决kw，后面补充接口去掉
    if (s_topo.topo_valid == TOPO_INVALID) {
        return TRUE;
    }
    return FALSE;
}

/*
*获取电池充电时间 
*/
int get_batt_charge_minute(unsigned char cluster_index) {
    //为了解决kw，后面补充接口去掉
    if (s_topo.topo_valid == TOPO_INVALID) {
        return 600;
    }
    return 10;
}




static unsigned char is_charge_too_long(unsigned char cluster_index)
{
    unsigned short charge_max_minute = CHARGE_MAX_MINUTE;  //从参数获取
    //解决kw，后面实现后去掉
    if (cluster_index == 1) {
        charge_max_minute = CHARGE_MAX_MINUTE;
    } else {
        charge_max_minute = 500;
    }
    
    if (charge_max_minute >= CHARGE_TIME_FULL_MIN && is_batt_charge_time_full(cluster_index))
    {
        return TRUE;
    }

    return FALSE;
}

static unsigned char is_batt_charge_time_full(unsigned char cluster_index)
{
    unsigned char batt_status = get_batt_state(cluster_index);  //电池管理提供
    //unsigned char batt_status = 0;//dcdc设备 获取电池状态
    int batt_charge_minutes = get_batt_charge_minute(cluster_index);//api 获取电池充电时间 
    unsigned short charge_max_minute = CHARGE_MAX_MINUTE;//参数 获取充电最大分钟
    
    return (batt_status == BATT_STATE_CHARGE)
        &&(batt_charge_minutes >= charge_max_minute);
}

/**
 * @brief  获取电芯开路电压 （计算电池校准值需要）
 * @param[in]    cell_temp  电芯温度
 * @param[in]    cell_vol  电芯电压指针
 * @param[in]    batt_curr  电池电流
 * @retval       电芯开路电压
 */
Static float get_cell_ocv(float cell_temp,  float* cell_vol, float batt_curr) {
    float cell_res = 0.01;
    float temp_cell_volt[BATT_MOD_CELL_NUM];
    float cell_ocv;

    rt_memcpy_s(temp_cell_volt, sizeof(temp_cell_volt), cell_vol, BATT_MOD_CELL_NUM * sizeof(cell_vol[0]));

    //获取电源电阻r
    cell_res = get_cell_dcr(cell_temp);
    //排序，为了取中值，以中值计算OCV
    bubble_sort(temp_cell_volt, BATT_MOD_CELL_NUM);
    //E=U+Ir
    cell_ocv = temp_cell_volt[BATT_MOD_CELL_NUM>>1] - batt_curr/1000*cell_res;
    return cell_ocv;
}


/**
 * @brief  获取电芯内阻
 * @param[in]    cell_temp  电芯温度
 * @retval       电芯内阻
 */
static float get_cell_dcr(float cell_temp)
{
    float x1, y1;
    float dcr = 0.0;
    unsigned short i=0;
    float points[][2] = {
        {-10.0, 20.0},
        {0, 12.0},
        {10, 6.0},
        {15, 4.0},
        {25, 3.0},
        {45, 2.0}, 
    };

    x1 = points[0][0];
    y1 = points[0][1];

    if (cell_temp >= 45)
    {
        return (float)2.0;
    }
    else if(cell_temp <= -10)
    {
        return (float)20.0;
    }
    else
    {
        for(i=0; i<ARR_SIZE(points); i++)
        {
            if (cell_temp <= points[i][0])
            {
                if(SUCCESSFUL == get_linear_point(x1, y1,points[i][0],points[i][1],cell_temp, &dcr))
                {
                    return dcr;
                }
                else
                {
                    break;
                }
            }
            x1 = points[i][0];
            y1 = points[i][1];
        }
    }    
    
    return (float)3.0;
}

void cell_ocv_unitest(void) {
    float cell_vol[BATT_MOD_CELL_NUM] = {3};
    get_cell_ocv(10, cell_vol, 2);
    return;
}


batt_result_t* get_batt_result_info(unsigned char mod_no) {
    return &s_batt_out[mod_no-1];
}
