/**
 * @brief
 */

#ifndef _BCCU_LED_H_
#define _BCCU_LED_H_

#include "board.h"
#include <rtdevice.h>
#include <rtthread.h>
#include "gd32f4xx.h"
#include "signal_led.h"
#ifdef __cplusplus
extern "C" {
#endif

#define LED_RUN_PIN         GET_PIN(D, 14)
#define LED_ALM_PIN         GET_PIN(D, 15)
#define LED_CAN1_PIN        GET_PIN(E, 2)
#define LED_CAN3_PIN        GET_PIN(E, 3)

#define LED_THREAD_DELAY_TIME 50 ///< 50ms

enum
{
    RUN_LED_TYPE = 0,
    ALM_LED_TYPE,
    CAN1_LED_TYPE,
    CAN3_LED_TYPE,
};//指示灯类型

typedef enum {
    CONSTANTLY_ON = 0,   // 常亮
    CONSTANTLY_OFF,      // 常灭
    PERIOD_BLINK,        // 周期闪烁（1s亮0.5s灭）
    DISCONTINUOUS_BLINK, // 间断闪烁（5s亮1s灭）
    FAST_BLINK,          // 超快闪烁（0.2s亮0.1s灭）
} led_blink_stat_e;

typedef struct {
    led_blink_stat_e  led_blink_state_index;
    char *led_blink_state;
} led_mode_t;

typedef struct {
    unsigned char led_type;
    rt_base_t led_pin;
    led_mode_t led_mode;
    led_t *led_handle;
} led_ctrl_t;

void* led_init_sys(void *param);
void led_ctrl(void* parameter);

#ifdef __cplusplus
}
#endif      //< __cplusplus

#endif      //< _BCCU_SAMPLE_H_