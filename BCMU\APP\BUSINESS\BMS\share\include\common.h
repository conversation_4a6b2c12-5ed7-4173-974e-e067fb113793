/**************************************************************************
* Copyright (C) 2010-2011, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：ZXDU18 CTL板软件
* 文件名称：common.h
* 文件说明：公共模块头文件
* 作    者  ：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/
#ifndef SOFTWARE_SRC_APP_COMMON_H_  
#define SOFTWARE_SRC_APP_COMMON_H_  

#ifdef __cplusplus
extern "C" {
#endif
#include <string.h> 
#include <stdlib.h>
#include <stdio.h>
#include <time.h>
#include <math.h>
#include <stdint.h>
#include "rtthread.h"
#include "const_define.h"

#ifdef REDIRECT_VCOM_ENABLED
#include "redirect_vcom.h"
#endif

#ifdef UNITEST
#define Static
#define __packed 
//添加static属性，目的是为了单元测试的spy接口使用，本身不影响存储
#define STATIC_UNITEST static
#else
////#define SOFTBANK_VER    //软银版本条件编译
#define Static static
#define STATIC_UNITEST
#endif

#ifndef UNUSED
#  if defined(__CC_ARM) || defined(__IAR_SYSTEMS_ICC__) || defined(__GNUC__) ||defined(__ADSPBLACKFIN__)
#    define UNUSED __attribute__((unused))
#  elif defined(_MSC_VER) || defined(__TI_COMPILER_VERSION__)
#    define UNUSED
#  else
#    #error not supported tool chain
#  endif
#endif /* UNUSED */

#ifdef KW_CHECK
#include "rtthread.h"
#define __packed
#define CORE_CM7
#endif

#ifdef REDIRECT_VCOM_ENABLED
extern rt_uint8_t enet_port_status;
#define ENET_DEBUG(fmt, ...)                                                                                           \
    do                                                                                                                 \
    {                                                                                                                  \
        if (enet_port_status == ENET_PORT_ENABLE)                                                                      \
        {                                                                                                              \
            rt_kprintf("[%s:%d]" fmt "\n", __FUNCTION__, __LINE__, ##__VA_ARGS__);                                     \
        }                                                                                                              \
    } while (0)
#endif

// 保留软件版本等信息，以便查看
#define BATT_NUMBER    "0000001"
#define PACK_BAR_CODE  "0000000001"
#define INITIAL_DISCHG_OUTPUT_VOLT (48)
#define BMS_HARDWARE_VERSION  "10"
#define MAX_CHARGE_CURRNET (25) // 最大充电电流（硬件的最大规格，与数据字典的参数无关，单位为A）
#define MAX_DISCHARGE_CURRNET (50) // 最大放电电流（硬件的最大规格，与数据字典的参数无关，单位为A）

// 预编译宏定义
#define BIGENDIAN       0 /*大端对齐*/
#define LITTLEENDIAN    1 /*小端对齐*/

#define PAD_TRACE_DATA_NUM 100

#define DISCHG_STEP_NUM 10
#define POWEROFF_MONTH  12
#define POWEROFF_REC_NUM    100 //100
#define POWEROFF_REC_NULL   0xff  //100
#define BATT_TEMP_REC_NULL  0xffffffff  //100

#define RUN_MODE_FREE                          0  //非受控模式
#define RUN_MODE_CONTROLLED                    1  //受控模式
#define RUN_MODE_ALL                           2  //全部模式

#define DISCHG_MODE_BATT_CHARACTERISTIC        0  //电池特性放电模式
#define DISCHG_MODE_SELF_ADAPTION              1  //自适应放电模式
#define DISCHG_MODE_CONSTANT_VOLTAGE           2  //恒压放电模式
#define DISCHG_MODE_SOLAR                      3  //光伏放电模式

#define CYCLE_MODE_LI_PRIOR                    0  //锂电优先
#define CYCLE_MODE_N_ADD_1                     1  //N+1

#define DISCHARGE_LI_PRIOR_AND_SWITCH          ((BYTE)0) //锂电优先并且要切换
#define DISCHARGE_LI_PRIOR_AND_NOT_SWITCH      ((BYTE)1) //锂电优先不切换
#define DISCHARGE_LA_PRIOR_AND_SWITCH          ((BYTE)2) //铅酸优先并且要切换

#define SMART_LI_DEFAULT_CURRENT (10.0)
#define EACH_TABLE_TEMP_NUM   (12)
#define CAN_MAX_RESEND (32)
#define MIN_CELL_CYCLE_TIMES (500)
#define MAX_CELL_CYCLE_TIMES  (10000)
#define LENGTH_HARDWARE_INFORMATION (20)
/***********************  常量定义  ************************/
#define NEW_BATT_SAVE_OFFSET        0x980        //sizeof(T_NEWBattSaveStruct)
#define PACKINFO_SAVE_OFFEST        0xF100     //sizeof(T_BmsPACKFactoryStruct)
#define PACK_MANUFACT_OFFEST        0xF180     //sizeof(T_BmsPACKManufactStruct)
#define CSU_RESET_REASON            0x04
//定义外协模式记录数据的存储空间(0xF200-0xF3E2)
#define  OUT_ASSIT_CHGRED_OFFSET     0xF200    //外协充电记录数据偏移 占(35*6=210)字节
#define  OUT_ASSIT_CHGRED_NUM        4          //2条时间点记录和2条压差记录(另外2条告警记录，不计入)

#define  OUT_ASSIT_STANDYRED_OFFSET  0xF30A    //外协静置记录数据偏移 占35字节
#define  OUT_ASSIT_STANDYRED_NUM     1

#define  OUT_ASSIT_DISCHGRED_OFFSET  0xF356    //外协放电记录数据偏移 占(35*4=140)字节
#define  OUT_ASSIT_DISCHGRED_NUM     3          //第一条时间点记录 第二条压差记录 第三条告警记录

#define  BDPB_ID_INFO_OFFSET         0xF3F0     //BDPB板序列号数据偏移 占(15+2)字节
// #define BDU_LOCK_STAT_OFFSET        0xF410    //BDU锁定状态 16 byte

#define  IMPE_CMPN_INFO_OFFSET      0xF420     // 电芯阻抗补偿信息 占（10*15+1+2=153）字节

#define FAT_OFFSET                  0xFF00   //出厂测试记录3*sizeof( T_FatRecordStruct ）

/***********************  常量定义  ************************/
#define VOL_TORLENRANCE 0.5     //电压误差上限
#define EXTERNAL_ALARM  0 
#define R121_CELL_VOLT_NUM  8
#define WAVE_POINT_CHG_LIMIT 15000
#define WAVE_POINT_DISCHG_LIMIT 15000
// #define FAILURE         1
// #define SUCCESSFUL      0
#ifndef SUCCESSFUL
#define SUCCESSFUL 0  ///<  NULL
#endif

#ifndef FAILURE
#define FAILURE 1  ///<  NULL
#endif
#define FAILED         -1
#define GPIO_OFF        0
#define GPIO_ON         1
#define RET_INVALID_PARAMS -2

#define AUTO_RLYOUT    0
#define BATT_NUM       1// 1        //电池个数--yyh电池组个数最大改为2组
#define RELAY_OUT      2// 6//  3       //输出干接点个数-yyh修改为4路
#define CELL_VOL_NUM     15      //单体电压数量
#define CELL_VOL_NUM_TOWER   8
#define SOC_RANGE_NUM   5
#define CELL_TEMP_RANGE_NUM 7
#define RANGE_BOUNDARY_OFFSET 1

#define CELL_VOL_NUM_MAX   16    //单体电压数量
#define CELL_TEMP_NUM_MAX  16      //单体温度数量最大值
#define CELL_TEMP_NUM_MIX  4       //单体温度数量最小值
#define Heat_Conn_TEMP_NUM_MAX  6      //加热膜连接器温度数量最大值

// #define ALARM_CLASS         52 //告警数量
#define ALARM_CLASS_OLD     47 //旧告警协议使用 20200818

#define NORMAL_LI         0  //常规锂电
#define SMART_LI          1  //智能锂电


#define ALM_PHONE_NUM 5 //告警手机号码数量

#define DEVICE_LEN      26

#define LEN_COMM_REC_BUF    400 //yyh底层接收缓冲区大小
#define LEN_COMM_REC_BUF_2    410 //当前无法容纳48个时间段，需要增大缓冲区，为了减少影响，只用于COM口和CAN口
#define LEN_COMM_SEND_BUF   512 //yyh底层发送缓冲区大小-兼容1.01整流器状态量修改

#define FLOAT_TO_BYTE(f)        ( (BYTE)( ((FLOAT)f) + 0.5 ) ) 
#define FLOAT_TO_WORD(f)        ( (WORD)( ((FLOAT)f) + 0.5 ) )     
#define FLOAT_TO_SCHAR(f)       ( ((f) >= 0 ) ? (CHAR)( (f) + 0.5 ) : (CHAR)( (f) - 0.5 ) )
#define FLOAT_TO_SHORT(f)       ( ((f) >= 0 ) ? (SHORT)( (f) + 0.5 ) : (SHORT)( (f) - 0.5 ) )
#define FLOAT_TO_INT32(f)       ( ((f) >= 0 ) ? (INT32)( (f) + 0.5 ) : (INT32)( (f) - 0.5 ) )
#define FLOAT_TO_SHORT_POW(f, n) ( ((f) >= 0 ) ? (SHORT)( (f)*Pow10(n) + 0.5 ) : (SHORT)( (f)*Pow10(n) - 0.5 ) )
#define FLOAT_TO_LONG(f) ( ((f) >= 0 ) ? (LONG)( (f) + 0.5 ) : (LONG)( (f) - 0.5 ) )

#if !defined(MAX)
#define MAX(a, b) (((a) > (b)) ? (a) : (b))
#endif

//#define BIT(n) (1 << (n))

#define TimerPlus(timer, timerMax)    do{ \
    if ((timer) < (timerMax))   \
    {                       \
        (timer)++;            \
    }                       \
}while(0)
	
#define TimeOut(timer, timerMax)    ((timer) >= (timerMax))

#undef _BIT
/* Set bit macro */
#define _BIT(n) (1 << n)

/* _SBF(f,v) sets the bit field starting at position "f" to value "v".
 * _SBF(f,v) is intended to be used in "OR" and "AND" expressions:
 * e.g., "((_SBF(5,7) | _SBF(12,0xF)) & 0xFFFF)"
 */
#undef _SBF
/* Set bit field macro */
#define _SBF(f, v) (v << f)

#define CLEAR_STRUCT(s) do { rt_memset(&s, 0x00, sizeof(s)); } while (0)

/* 错误返回宏 */
#define RETURN_IF_FAIL(expr)          do { if (!(expr)) { return; }} while (0)      ///<  expr失败直接返回
#define RETURN_VAL_IF_FAIL(expr, val) do { if (!(expr)) { return val; }} while (0)  ///<  expr失败返回某个值
#define BREAK_IF_FAIL(expr)              { if (!(expr)) { break; }}                 ///<  expr失败break
#define CONTINUE_IF_FAIL(expr)           { if (!(expr)) { continue; }}              ///<  expr失败continue
#define PRINT_MSG_AND_RETURN_IF_FAIL(expr) \
            do { \
                if (!(expr)) { \
                    rt_kprintf("%s failed!\n", __FUNCTION__); \
                    return; \
                } else { \
                    rt_kprintf("%s success!\n", __FUNCTION__); \
                } \
            } while (0)      ///<  expr失败,打印信息，然后返回;成功则直接打印成功

//#ifndef UNITEST
#ifdef DEVICE_USING_D121
//#define PARA_NUM            (PARA_ID_SLEEP_SHUTDOWN_TIME + 1)
#define PARA_ID_DATE        (PARA_ID_ALARM_CLASS + 1)//此处现在固定,以此确定操作ID起始值
#define PARA_ID_TIME        (PARA_ID_DATE + 1) //特殊操作记录ID起始值
#define PARA_NUM            (PARA_ID_SWITCH_SOC2 + 1)
#else
#define PARA_ID_DATE        (PARA_ID_ALARM_CLASS + 1)//此处现在固定,以此确定操作ID起始值
#define PARA_ID_TIME        (PARA_ID_DATE + 1) //特殊操作记录ID起始值
#define PARA_NUM            (PARA_ID_WATER_INGRNESS_ENABLE + 1)
#endif
#define NEW_PARA_OFFSET     (128-PARA_ID_ALARM_CLASS)//新增参数ID针对告警级别ID的偏移量

//定时器事件
#define TIMER_SEND0        19      // 延时10ms打开UART0发送中断
#define TIMER_SEND1        20      // 延时10ms打开UART1发送中断
#define TIMER_SEND2        21      // 延时10ms打开UART2发送中断
#define TIMER_SEND3        22      // 延时10ms打开UART3发送中断
#define TIMER_RESET        23      // 延时产生MSG_RESET
#define TIMER_APPTEST_LED  24      // 进入apptest后延时3分钟根据地址闪灯
#define TIMER_BACKUP_FLAG  25      // 延时1分钟置备份标记

//消息类型
#define MSG_SAMPLE              0       // 数据采集
#define MSG_CTRL                1       // 输出控制
#define MSG_COMM                2       // 通讯处理
#define MSG_COMM_COUNTER        3       // 通讯模块计数器
#define MSG_CAN_COMM            4       // 整流器通讯
#define MSG_CHECK_PARA          5       // 检查RAM中参数和微调系数
#define MSG_BATT_COUNTER        6       // 电池管理模块计数器
#define MSG_CLOCK               7       // 时钟计时加1秒（考虑时钟芯片坏的情况）
#define MSG_ONEHOUR             8       // 保存历史数据和历史告警
#define MSG_SU_COM              9          //底层通讯
#define MSG_RESET               10      // 复位消息
#define MSG_BATTERY             11      // 执行电池管理
#define MSG_ALARM               12      // 处理告警
#define MSG_SAVE_REALALM        13      //保存实时告警
#define MSG_BDU_COM             14      //BDU通讯
#define MSG_BACKUP_FLAG         15      //备份标记
#define MSG_APPTEST_LED         16      //apptest闪灯


//定时时长
#define ONE_SECOND          100         // 1秒
#define ONE_MINUTE          6000        // 1分钟  1*60*100
#define FIVE_MINUTES        30000       //5分钟
#define TEN_MINUTES         60000       // 10分钟 10*60*100
#define ONE_HOUR            360000      // 1小时    1*60*60*100
#define SECS_PER_HOUR   (60 * 60)
#define SECS_PER_DAY    (SECS_PER_HOUR * 24)
#define ONE_DAY_MINUTES (24*60)
#define ONE_YEAR_DAYS   (365)
#define ONE_MINUTE_PER_SECONDS (60)

#define MIN_CURR_DET_ODCU (2.0) 	// Added by fengfj, 2019-01-06 18:54:11


#define ONE_SEC    RT_TICK_PER_SECOND  // rt_thread 1 sec
#define ARR_SIZE(name)	(sizeof(name)/sizeof(name[0]))

#ifndef TRUE
#define TRUE 1  ///<  TRUE
#endif

#ifndef FALSE
#define FALSE 0  ///<  FALSE
#endif

typedef unsigned char Bool;

//csu重启原因
enum
{
	NO_RESET_CSU_ABNORMAL = 1,
	NO_RESET_CSU_UPGRADE,
	NO_RESET_REMOTE_CTRL,
	NO_RESET_APPTEST,
	NO_RESET_SHUTDOWN,
	NO_RESET_CSU_BACKUP,
	NO_RESET_BDU_UPGRADE,
	NO_RESET_UPGRADE_TIMEOUT,
	NO_RESET_UNKNOW,
};
//类型定义
typedef uint8_t         BOOLEAN;       // 255
typedef char            CHAR;         // 127
typedef uint8_t         BYTE;
typedef int16_t         SHORT;         // 32767
typedef uint16_t        WORD;          // 65535
typedef int32_t         LONG;          // 2147483647
typedef uint32_t        ULONG;         // 4294967295
typedef float           FLOAT;
typedef int32_t         INT32;         // 2147483647
typedef uint32_t        UINT32;        // 4294967295
typedef int32_t         INT32S;         // 2147483647
typedef void            VOID;         // 2147483647

// 类型定义
    typedef unsigned char INT8U;   // Unsigned  8 bit quantity
    typedef signed char INT8S;     // Signed    8 bit quantity
    typedef unsigned short INT16U; // Unsigned 16 bit quantity
    typedef signed short INT16S;   // Signed   16 bit quantity
    typedef unsigned int INT32U;   // Unsigned 32 bit quantity
    typedef unsigned long INT32UL; // Unsigned 32 bit quantity
    typedef signed long INT32SL;   // Signed   32 bit quantity
    typedef float FP32;            // Single precision floating point
    typedef double FP64;
    typedef long long INT64S; // Unsigned 64 bit quantity   add by panqiyin 2010.4.2

#define False   0
#define True    1

#define INT32S_MAX             2147483647
#define INT16S_MAX             32767

#define LEN_TYPE_STRING_10     10
#define LEN_TYPE_STRING_16     16
#define LEN_TYPE_STRING_20     20
#define LEN_TYPE_STRING_32     32

//参数类型
#define PARATYPE_RUNNING        1
#define PARATYPE_CONFIG         2
#define PARATYPE_ALL            3
#define PARATYPE_ADJUST         4

#define    CELL_COMPATION_NAME_LEN          10
#define    CELL_COMPATION_CONFIG_NUM        10
#if !defined(MIN)
#define MIN(a, b) (((a) < (b)) ? (a) : (b))
#endif

// 设备级防盗解锁方式
#define DEVICE_UNLOCK_COMMUN 0 // 通信解锁
#define DEVICE_UNLOCK_MANUAL 1 // 人工解锁

#define DEVICE_UNLOCK         0 //解锁
#define DEVICE_CANCEL_DEFENCE 1 //撤防

#define SITE_ANTITHEFT_KEY_LEN 32  // 站点防盗电池钥匙长度
#define NETANTITHEFT_KEY_LEN 32  // 网管防盗电池钥匙长度
#define NETANTITHEFT_SN_LEN 15  // 网管防盗SN信息长度
#define CUSTOMIZED_INFO_LEN  15   // 软件标识号信息长度，前五个字节分配给bms版本号，后五个字节空间分配给bdu版本号
#define times_4      4         //4次计数

#define NOT_FOUND   -1  
//网络参数获取方式
#define STATIC_IP_MODE (0)  // 静态ip
#define DYNAMIC_IP_MODE (1) // 动态ip
/*********************  时间数据结构  **********************/

enum
{
    FIRST_REC = 0,
    PREVIOUS_REC,
    NEXT_REC,
};

enum
{
    CHARGE = 0, //充电
    DISCHARGE, //放电
};

/*enum
{
    DISENABLE = 0,     //禁止
    ENABLE,            //允许
};*/

/**
 * @brief 电池工作模式
 * 
 */
typedef enum BattTestMode
{
    BATT_TEST_MODE_NORMAL  = 0, /**< 正常模式 */
    BATT_TEST_MODE_APPTEST = 1, /**< APPTEST模式 */
    BATT_TEST_MODE_QTPTEST = 2, /**< QTP模式 */
    BATT_TEST_MODE_SOLAR   = 3, /**< 光伏模式 */
    R121_BATT_TEST_MODE_NORMAL = 4,  /**< R121正常模式 */
    R121_BATT_TEST_MODE_QTPTEST = 5, /**< R121QTP模式 */
    R121_BATT_TEST_MODE_SOLAR = 6,  /**< R121光伏模式 */
    // BATT_TEST_MODE_ILLEGAL
} BattTestModeEnum;

enum
{
    PROTOCOL_1363 = 0,
    PROTOCOL_MODBUS = 1,
    PROTOCOL_DL,
    PROTOCOL_BMS,
	PROTOCOL_BMS_BACKUP,
};

enum
{
    DL_NONE = 0,
    DL_COMM = 1,
    DL_CAN2 = 2,
};

//充放电状态（0：常规充电；1：直通充电；2：常规放电；3：直通放电；4：升压充电；
//6：升压放电；8：降压充电；10：降压放电；12：充放电停止）
typedef enum
{
    BDU_STATUS_COMMON_CHARGE_SCI = 0,       ////常规充电
    BDU_STATUS_STRAIGHT_CHARGE_SCI = 1,     ////直通充电
    BDU_STATUS_COMMON_DISCHARGE_SCI = 2,    ////常规放电
    BDU_STATUS_STRAIGHT_DISCHARGE_SCI = 3,  ////直通放电
    BDU_STATUS_BOOST_CHARGE_SCI = 4,        ////升压充电
    BDU_STATUS_BOOST_DISCHARGE_SCI = 6,     ////升压放电
    BDU_STATUS_BUCK_CHARGE_SCI = 8,         ////降压充电
    BDU_STATUS_BUCK_DISCHARGE_SCI = 10,      ////降压放电
    BDU_STATUS_STOP_SCI = 12,                ////充放电停止
    BDU_STATUS_FAILURE_SCI = 13,             ////闭锁
    
}BDU_STATUS_SCI;

typedef enum
{
    BDU_STATUS_COMMON_CHARGE = 0,       ////常规充电
    BDU_STATUS_COMMON_DISCHARGE = 1,    ////常规放电
    BDU_STATUS_BOOST_CHARGE = 2,        ////升压充电
    BDU_STATUS_BOOST_DISCHARGE = 3,     ////升压放电
    BDU_STATUS_BUCK_CHARGE = 4,         ////降压充电
    BDU_STATUS_BUCK_DISCHARGE = 5,      ////降压放电
    BDU_STATUS_STOP = 6,                ////充放电停止
    BDU_STATUS_FAILURE = 7,             ////闭锁
    BDU_STATUS_STRAIGHT_CHARGE = 8,     ////直通充电
    BDU_STATUS_STRAIGHT_DISCHARGE = 9,  ////直通放电
}BDU_STATUS;

typedef enum
{
    CELL_MANUFACTRUER_NOT_SET,
    CELL_MANUFACTRUER_GUANGYU,////光宇
    CELL_MANUFACTRUER_SHUANGDENG,////双登
    CELL_MANUFACTRUER_RESERVED,

}CELL_MANUFACTRUER_ENUM;

/*********************   站点防盗相关   *********************/
enum SiteAntiTheft
{
    // command type
    SiteAntiTheftStart = 0,
    SiteAntiTheftHeartbeat = 1,
    SiteAntiTheftClose = 2,
    SiteAntiTheftChangeKey = 3,

    // response
    ResponseSuccess = 0,
    ResponseFailure,
    ResponseAlready,
    ResponseNotBind = 2,

    // status
    SiteAntiTheftOff = 0,
    SiteAntiTheftOn
};

/*********************   网管防盗相关   *********************/
enum NetAntiTheftCommand
{
    // command type
    NETANTITHEFT_START = 0,  //开启网管布防
    NETANTITHEFT_HEARBEAT,   //网管防盗心跳
    NETANTITHEFT_CLOSE,      //关闭网管布防
};

enum NetAntiTheftResponse
{
    // response
    NET_RESPONSE_SUCCESS = 0,  // 控制成功
    NET_RESPONSE_ALREADY = 1,  // 控制前已为该状态
    NET_RESPONSE_NOT_BIND = 1,  // 未开启网管布防
    NET_RESPONSE_FAILURE_BY_KEY = 2,  // 控制命令携带电池钥匙不匹配
    NET_RESPONSE_FAILURE_BY_PARA = 2,  // 控制布防失败，网管防盗延时参数为0
    NET_RESPONSE_FAILURE_BY_FILE = 3,  // 写入文件失败
    NET_RESPONSE_FAILURE_BY_KEY_SNCORRECT = 3,  // 控制命令携带电池钥匙不匹配,但SN信息匹配
    NET_RESPONSE_FAILURE_BY_ALA = 4,  // 控制布防失败，网管防盗告警级别为0
    NET_RESPONSE_TIMEOUT = 5,  // 网管请求超时
    NET_RESPONSE_SAME_KEY_NEW = 6,  // 新旧钥匙相同
    NET_RESPONSE_SAME_KEY_OLD = 7,  // 旧钥匙匹配成功
};

enum NetAntiTheftStatus
{
    // status
    NETANTITHEFT_OFF = 0,  // 未布防
    NETANTITHEFT_ON,       // 已布防
};

#pragma pack(push, 1)
typedef struct
{
    WORD    wYear;                  /* 年 */
    BYTE    ucMonth;                /* 月 */
    BYTE    ucDay;                  /* 日 */
    BYTE    ucHour;                 /* 时 */
    BYTE    ucMinute;               /* 分 */
    BYTE    ucSecond;               /* 秒 */
}T_TimeStruct;
#pragma pack(pop)

typedef struct
{
    WORD    wYear;                  /* 年 */
    WORD    wMonth;             /* 月 */
}T_MonthStruct;

typedef struct
{
    WORD    wYear;                  /* 年 */
    BYTE    ucMonth;                /* 月 */
    BYTE    ucDay;                  /* 日 */
}T_DateStruct;

typedef struct
{
   WORD   wValue;
   BOOLEAN bValid;
}T_ProtoSetVar;

typedef union
{
    char ucData[4];
    float fData;
}FLOAT_u;

/**
 * 大小端序检查联合体
 * 用于检查大小端序
 */
typedef union
{
    int             int_value;      //<  int
    char            char_value;     //<  char
}enddian_check_u;

/********************  参数数据结构  **********************/
#pragma pack(push, 1)
#include "common_in.h"
#pragma pack(pop)
#define DIFF_INVALID (0xffff)



typedef struct
{
    BYTE ucReason;
	WORD wCrc;
}T_ResetReasonStruct;

/********************程序数据类型定义************************/
typedef union
{
    WORD    iData;
    BYTE    ucByte[2];
}U_Int;

typedef union
{
    SHORT    sData;
    BYTE     ucByte[2];
}U_16Int;

typedef union
{
    ULONG    LData;
    BYTE    ucData[4];
}U_32Int;

typedef union
{
    BYTE ucData[8];
    rt_uint32_t ULData[2];
}U_64Int;

typedef union
{
    FLOAT   fData;
    BYTE    ucData[4];
}U_32FLOAT;

typedef union
{
    LONG LData;
    BYTE ucData[4];
}S_32Int;


typedef struct
{
    FLOAT   fRateCurrChg;
    FLOAT   fRateCurrChgBus;
    FLOAT   fRateCurrDischg;
    FLOAT   fMaxDischgCurr;
    FLOAT   fCapTransRate;
    FLOAT   fVoltTrasRate;
    FLOAT   fDischgCurrCoef;
    FLOAT   fBdcuChgMax;
    FLOAT   fBdcuDischgMax;
    CHAR*   chBMSType;
}T_ProjConfig;

typedef struct
{
    BOOLEAN         bValid;                  // 是否有效
    FLOAT           fBattCap;                // 电池容量(AH)
    T_TimeStruct    tLastCalcCalenderTime;   // 上次计算SOH的时间
    WORD            wBattDischgTimes;        // 放电次数
    FLOAT           fAddedLifeDecline;       // 附加衰减，校准时额外的衰减
    FLOAT           fDischargeCapForNTimes;  // 放出AH数；用于N+1计算
    FLOAT           fTotalDischargeCap;      // 放出AH数，用于计算放电次数
    FLOAT           fCalenderLifeDecline;
    FLOAT           fCycleLifeDecline;
    BYTE            ucBduType;               // BDU类型
    FLOAT			fMultiDischargeCap;      // 累计放电容量
    FLOAT           fMultiDischargePower;    // 累计放电电量
    BYTE            reserve;                 // 保留位置
    WORD            wCRC;                    // CRC
}T_BattSaveStruct;

typedef struct
{
    FLOAT fMultiChargeCap;    // 累计充电容量
    FLOAT fMultiChargePower;  // 累计充电电量
    BOOLEAN  bCtrlLock;       // 是否控制闭锁
    BYTE  reserved[7];      // 保留位置
    WORD  wCRC;               // CRC
}T_BattSaveNewStruct;

typedef struct
{
    BOOLEAN abCellDamagePrt[CELL_VOL_NUM_MAX];
    WORD wCrc;
}T_NewBattSaveStruct;

typedef struct
{
    UINT32  ulDcrTestPeriodCnt;
    BYTE    ucDcrFaultAlmCnt;
    BYTE    ucDcrFaultPrtCnt;
    BYTE    ucDcrFaultAlmRecovCnt;
    WORD    wCheckSum;
}T_DcrCntStruct;

/*********************  通讯底层数据的结构定义  ********************/
typedef struct
{
    /***********  直接接收数据  **********/
    BOOLEAN     bRecReady;              /* 数据包正在接收标志（已收到包头） */
    BOOLEAN     bRecOk;                 /* 数据包接收完毕标志 */
    BYTE        ucRecTime;              /* 相邻两接收字符间隔时间，每100ms加1 */    
    BYTE        ucRecCmdTime;              /* 相邻两接收字符间隔时间，每20ms加1 modbus*/
    BYTE        aucRecBuf[LEN_COMM_REC_BUF_2];    /* 直接接收数据缓冲区（ASCII码，环型）*/
    WORD        wRecLength;             /* 直接接收数据缓冲区数据包长度 */
    WORD        wRecIndex;              /* 直接接收数据缓冲区接收索引       */
    WORD        wTimeOut;              

    /***********  直接发送数据  **********/ 
    BYTE        aucSendBuf[LEN_COMM_SEND_BUF];  /* 直接发送数据缓冲区（ASCII码） */ 
    WORD        wSendLength;                /* 直接发送数据缓冲区数据包长度 */
    WORD        wSendIndex;             /* 直接发送数据缓冲区发送索引   */
    BOOLEAN     bSendOk;                /* 数据包发送完毕标志 */   

    /***********  协议类型  **********/ 
    BYTE     ucPortType;
    BYTE     ucPrtclType;
    BYTE     ucLineErr;
    /***********  链路相关信息  **********/
    BYTE     ucLinkSrcDev;            ///< 链路源设备类型
    BYTE     ucLinkSrcAdr;            ///< 链路源设备地址
}T_CommStruct;

/******************输出控制结构*******************/ 
typedef struct  
{
    BOOLEAN     bCtrlFlag;          /* QTP 控制是否有效*/ // TODO:待优化，需移出此结构体
    BOOLEAN     bLed[6];			/* 0:运行灯1:告警灯//2-5:容量灯*/
    BOOLEAN     bBuzz;              /* 蜂鸣器 True:鸣叫 False:关闭 */           
    WORD        wRelayOut;          /* 输出多路干结点 */   
}T_CtrlOutStruct; 

//输出干接点的协议控制
typedef struct
{
    BYTE    aucManuRelayStatus[RELAY_OUT];
    BYTE    ucRlyCtlStat;
    WORD    wCheckSum;
}T_RelayCtrl;

//yyh
typedef struct
{
    T_TimeStruct tStartTime;
    T_TimeStruct tEndTime;
}T_ProcessTimeStruct; // 16 字节

typedef enum {
    Digit_SW_DET = 0,    
    Digit_THEFT_DET, 
    Digit_SPOWER_DET,
    Digit_BALANCE_DET,
} DigitSampleNum;

// 硬件板卡参数，指示单节电压和温度的检测数量
typedef struct
{
    BYTE ucCellVoltNum;                //单节电压检测数量
    BYTE ucCellTempNum;                //单体温度检测数量
    WORD wCRC;
}T_HardwareParaStruct;

typedef struct
{
    BYTE aucMacAddr[16];              //网络Mac地址
    BYTE ucMacFlag;                  //网络Mac配置标志 0X55表示已经进行了MAC的配置
    WORD wCRC;
}T_NetMacStruct;

typedef struct
{
    CHAR    acBmsCustomerName[17];  //客户名称
    WORD    wCRC;
}T_BmsCustomerNameStruct;

typedef struct
{
    T_TimeStruct    tUnlockTime;            //解锁时间
    BYTE            ucUnlockType;           //解锁类型：0上位机、1网管
    CHAR            ucUnlockAccount[20];    //解锁账户：上位机序列号、网管解锁账户名
    WORD            wCheckSum;
}T_UnlockRecordStruct;


typedef struct
{
	CHAR           acBmsFacSn[15];   //BMS出厂序列号
	CHAR           acDeviceSn[15];  //整机序列号
	//BYTE           ucCellManufactruer;              //PACK厂家
    CHAR           acPackBarCode[4][20];      //PACK BAR CODE
    //T_DateStruct   tBattDate;                 //电池出厂日期
    T_TimeStruct   tFactoryTime;           //出厂时间
    T_TimeStruct   tFirstBootTime;         //第一次开机时间
    WORD           wCRC;
}T_BmsPACKFactoryStruct;

typedef struct
{
    CHAR           acCellManufactruer[20];      //电芯厂家
    T_DateStruct   tBattDate;                   //电芯出厂日期
    T_DateStruct   tActiveDate;                 //电芯启用日期
    WORD           wCellCycleTimes;             //电芯循环次数
    CHAR           acPackInfo[20];              //Pack厂家
    //CHAR           acReserve[22];               //保留位
    WORD           wCRC;
}T_BmsPACKManufactStruct;

typedef struct
{
    CHAR  acCellManufactruer[CELL_COMPATION_NAME_LEN];      //PACK厂家
    WORD  wBattCap;                    //容量
    CHAR  ucCmpnPos;                   //补偿电芯位置
    WORD  wImpeVal;                    //补偿阻抗值（毫欧，精度0.01）
}T_ImpeCmpnsatInfoStruct;

typedef struct
{
    CHAR acSysName[20];     // BMS系统名称
    CHAR acVersion[20];     // BMS软件版本
    T_DateStruct tSoftDate; // BMS软件发布日期
    CHAR acCorpName[20];    // BMS厂家名称
    CHAR acBmsTypeName[20]; // BMS型号
    CHAR acBootDate[10];    // Boot日期
    //CHAR acVer[2];          // 软件版本
    CHAR acBmsFacSn[20];    // 序列号
    CHAR acDeviceSn[15];    // BMS软件版本
	CHAR acIMEISn[20];    	// IMEI
    CHAR acCorpNameNew[20];    // BMS厂家名称(新)
    CHAR acBmsTypeNameNew[20]; // BMS型号（新）
    CHAR acSysNameNew[20];     // BMS系统名称（新）
    CHAR acHardwareVersion[20];// BMS硬件版本号
#ifdef GET_NUMOFCELL_AND_TYPEOFBMS_ENABLED
    BYTE ucNumofCell;          //电芯串数
    BYTE ucTypeofBms;          //BMS类型
#endif
    CHAR acSoftwareTag[15]; //软件标识码
    CHAR acHardwareInfo[20];   // 硬件版本信息
} T_BCMFactoryStruct;

typedef struct
{
    CHAR           acBattCorpName[20];        //电池厂家名称
    CHAR           acNameLite[10];            //短称
    CHAR           acHardwareInfo[20];        //硬件版本信息
    CHAR           acReserve[60];             //保留
    WORD           wCRC;
}T_BmsInfoStruct;

//兼容C2对应T_BCMFactoryStruct的结构体
typedef struct
{
    CHAR    acProductNoBack[12];//序列号后12字节对应BMS系统名称前12字节
    CHAR    acReserved[8];
    CHAR    acVersion[20];//BMS软件版本
    T_DateStruct  tSoftDate;  //BMS软件发布日期
    CHAR    acBmsTypeName[10];//电池型号对应BMS厂家名称前10字节
    CHAR    acVer[2];//软件版本对应BMS厂家名称第11、12字节
    CHAR    acCorpName[20]; //制造厂家对应BMS厂家名称后8个字节和BMS型号前12字节
    CHAR    acProductNoFront[8]; //序列号前8个字节对应BMS型号后8个字节
    CHAR    acBootDate[10];  // Boot日期
    //CHAR acVer[2];          // 软件版本
    CHAR acBmsFacSn[20];    // 序列号
    CHAR acDeviceSn[15];    // BMS软件版本
	CHAR acIMEISn[20];    	// IMEI
    CHAR acCorpNameNew[20];    // BMS厂家名称(新)
    CHAR acBmsTypeNameNew[20]; // BMS型号（新）
    CHAR acSysNameNew[20];     // BMS系统名称（新）
    CHAR acHardwareVersion[20];// BMS硬件版本号
#ifdef GET_NUMOFCELL_AND_TYPEOFBMS_ENABLED
    BYTE ucNumofCell;          //电芯串数
    BYTE ucTypeofBms;          //BMS类型
#endif
    CHAR acSoftwareTag[15]; //软件标识码
    CHAR acHardwareInfo[20];   // 硬件版本信息
}T_BCMFactoryC2Struct;

typedef struct
{
    CHAR	       acName[30];		// 系统名称
	T_DateStruct   tSoftDate;       //软件版本日期
	CHAR           acDigControlVer[8]; //数控平台版本
	CHAR           acSoftVer[6];     //软件版本
	ULONG         ulBduFacSn;    //BDU出厂序列号
	CHAR	       acBduAssetMagInfo[30];    //BDU资产管理信息
#ifdef GET_BDU_PARALL_UPDATE_FLAG
    BOOLEAN        bParallUpdateFlag;    //功率并发升级标识
#endif
}T_BduFactoryInfo;

// typedef BOOLEAN (*FCOMPARE_HANDLE)(T_StateListenStruct* ptStat, BYTE index);
struct T_StateListen
{
    BOOLEAN bState;
    BOOLEAN bFlag;
    BOOLEAN (*fhandle)(struct T_StateListen* ptStat, BYTE index);
    BYTE ucCounter;
    BYTE ucCounterMax;
};
//升级地址信息
typedef struct
{
	BYTE	ucFlag;
	BYTE	ucUpdateAddr;
	WORD	wCrc;
}T_UpdateAddrStruct;

typedef struct
{
    FLOAT fTemp; ////temperature
    FLOAT fCoef; ////discharge curr coef
    FLOAT fVolt; ////voltage
}T_DoubleLinearInsertStruct;

typedef struct {
    float fVolt;
    float fTemp;
    float fCurr;
}T_VoltTempCurrStruct;

typedef struct
{
    BOOLEAN bExist;   //从机在位状态added by ht 20150917
    BOOLEAN bChgPrt;
    BOOLEAN bDischgPrt;
    BOOLEAN bChgLimit;
    BOOLEAN bDischgLimit;
    BOOLEAN bChargeInBreak;
    BOOLEAN bChargeStat; ///在充电状态
    BYTE  ucCommFailCnt;        //主从机通信失败计时，包括休眠的电池，用于主从机控制
    BYTE  ucCommFailCntAll;     //无论哪个电池，主从机通信都会被统计，通信失败计时，用于SNMP等全面统计电池数量的场景
    BYTE  ucCommFailToSnmpAgentCnt;
    WORD  wBatHighSOC;   ///高精度SOC
    FLOAT fBatCur;
    FLOAT fBusCur;
    FLOAT fBatVol;
    FLOAT fBusVol;
    FLOAT fChargeSetCurr;
    WORD wBattCapacity; // 电池额定容量

    BOOLEAN bLoopOff; ////回路异常
    BOOLEAN bUpgrade; ////从机升级
    BOOLEAN bChgCurrSta; ////充电电流是否接近限流值
    BOOLEAN bRotateDisable;
    BOOLEAN bStatFault; ////BDPB and BDCU
    BOOLEAN bExternalPowerOn; ////外部有电状态
    BOOLEAN bQuietSleepSta; //静置休眠状态
    BYTE    ucRotateLimitCurr;
    FLOAT fConnTemp; //连接器温度
    BOOLEAN bBattLock; //从机闭锁
    BOOLEAN bConnTempHighPrtStatus; //连接器高温保护状态    
    BYTE    ucBattType;  //Battery type 1-R321 0-FB100B3
    BOOLEAN bActivatePortCurrErr;     //激活口回路电流异常
    BOOLEAN bBattInSys;   //是否有从机接入，休眠也会被标记，用于SNMP及电池统计
    BOOLEAN bNetAccess;   //是否接入网络
    BOOLEAN bConnectedToSnmpAgent;
    BOOLEAN bRotateChgHeat; //是否满足轮换充电加热条件
    BYTE    ucRelayCtrl;    //干接点控制
    FLOAT fFMcurrentSingleMaxChgPower; //单体电池充电最大功率
}T_SlavesRealData;

typedef struct
{
    BOOLEAN bExist;
    BYTE  ucCommFailCnt;
    FLOAT fAveCurr;
    WORD  wAveSoc;
    FLOAT fAveBattVolt;
}T_Can2Data;

typedef struct
{
    BOOLEAN bValid;
    BYTE ucObjSta;
    FLOAT fObjCurr;
    WORD  wObjSoc;
    FLOAT fVolAdj;
}T_Can2ObjData;


typedef struct T_StateListen T_StateListenStruct;

typedef struct
{
    BYTE ucBit0;
    BYTE ucBit1;
    BYTE ucBit2;
    BYTE ucBit3;
    BYTE ucBit4;
    BYTE ucBit5;
    BYTE ucBit6;
    BYTE ucBit7;
} T_BitStruct;

/*********************  变量的声明  **********************/


/*********************  函数原型定义  **********************/
void    InitCRC(void);
VOID InitCRC32Table(VOID);
WORD    CRC_Cal(BYTE *p, WORD wCount);
WORD    GetCRC16(BYTE *buf, WORD n);
float   max( float  i, float j );
float   min( float i, float j );
FLOAT   GetValidData( FLOAT fValue, FLOAT fMax, FLOAT fMin );

void    Delayms( register WORD   wMillisecond );
void    ResetMCU(BYTE ucType);
BYTE    HexToASCII( BYTE ucCh );
BYTE    ASCIIToHex( BYTE ucAscii );
WORD    Float2Word(FLOAT f);
SHORT   Float2Short(FLOAT f);
void    PutInt16ToBuff( BYTE *p , SHORT iVal );
void    PutInt32ToBuff( BYTE *p , INT32 slVal );
ULONG   GetULongData(BYTE *p);
BOOLEAN GetUint64Data(U_64Int *ptData, BYTE *p);
SHORT   GetInt16Data( BYTE * p );
INT32S  GetInt32Data(const BYTE * p );
void    ExchangeIntHighLowByte( BYTE *p );

FLOAT   GetCellDCR(FLOAT fCellTemprature);
FLOAT GetLowTempDischgCurr(FLOAT fCellTemp);
FLOAT   DoubleLinearInsertValue(FLOAT fTemperature, FLOAT fCurrCoef);
SHORT   Pow10( BYTE x);
INT32S  BubbleSort(FLOAT *pfData, BYTE ucNum);
void    InitListener(T_StateListenStruct* ptState, BOOLEAN (*fhandle)(struct T_StateListen* ptStat, BYTE index), BYTE ucCounterMax);
BOOLEAN RunListener(T_StateListenStruct* ptState, BYTE i);
void    ResetListener(T_StateListenStruct* ptState);
size_t  strnlen (const char *str, size_t maxlen);
WORD    GetLength(int l);

time_t GetTimeStamp(void);
INT32S GetDiffDays(T_TimeStruct const* pTime);
time_t TimeStruct2Time_t(T_TimeStruct const* pTime);
void    GetTime(T_TimeStruct  * ptTime);
BOOLEAN GetApptestFlag(void);
BOOLEAN GetQtptestFlag(void);
void SetApptestFlag(BOOLEAN bFlag);
void SetQtptestFlag(BOOLEAN bFlag);
void SetChargeNotFullOneWeek(BOOLEAN bFlag);
BOOLEAN GetChargeNotFullOneWeek(void);
BOOLEAN     CheckDateValid( T_DateStruct  const* ptDate );
BOOLEAN     CheckTimeValid( T_TimeStruct  const* ptTime );
void ParaStrCpy(BYTE* pcDst, BYTE* pcSrc, size_t size);

void ConvertAscii2Hex(BYTE *pucSrc, BYTE *pucDst, WORD wLength);
WORD WordHost2Modbus(WORD *wTemp);
WORD BYTEHost2Modbus(BYTE *ucTemp);
SHORT Host2Modbus(SHORT *wTemp);
void MemsetBuff(void *des, void *src, BYTE srcLen, BYTE defalutLen, BYTE defalutValue);
SHORT FloatChangeToModbus(FLOAT fData);
INT32 FloatChangeToInt32Modbus(FLOAT fData);
INT32 Int32ValuetoModbus(INT32 Temp);
rt_uint64_t uint64ValuetoModbus(rt_uint64_t Temp);
BYTE BitToByte(T_BitStruct *pBitData);
void ParaStrCpySNMP(BYTE* pcDst, BYTE* pcSrc, size_t size);
void SaveResetReason(BYTE ucType);
void LoadNetPara(void);

BOOLEAN uint64Valuetostr(U_64Int tData, BYTE *pstr, BYTE ucSize);

WORD Calculate_CRC16(const BYTE *pInput, UINT32 length);
WORD crc16_calc(WORD *ctx, const uint8_t *data, int len);
UINT32 crc32_calc(uint32_t *ctx, const uint8_t *data, int len);
WORD UnlockCodeCreate(CHAR *SeralNumber, BYTE Len);
BYTE PutTime2Buff(BYTE*p, struct tm tTime);
BYTE PutTimeStruct2Buff(BYTE*p, T_TimeStruct tTime);
void SetDigitalAlarmBit(BYTE index, BYTE *byte, BOOLEAN condition);
unsigned int judge_enddian(void);
short get_int16_data(const unsigned char *p);
void put_time_t_to_buff (unsigned char* p, time_t time);
FLOAT VoltTempLinearInsert(BYTE ucMap, FLOAT fVolt, FLOAT fTemperature);
FLOAT SumArray(FLOAT arr[], uint16_t size);
BOOLEAN CheckDigit(BYTE arr[], BYTE size);
BOOLEAN CheckPackOrCellInfo(CHAR arr[], BYTE size);
BOOLEAN CheckPackAndCellFormat(CHAR arr[], BYTE size);
BOOLEAN CheckCellCycleTimesValid(WORD wTimes);
FLOAT CalAverage(FLOAT *p, BYTE size);
FLOAT GetCellMediumValue(FLOAT afData[], BYTE ucNum);
BOOLEAN CheckCharRange(BYTE *p, BYTE ucCounter);
BOOLEAN CheckCharRangeAndLength(BYTE *p, BYTE ucLength, BYTE ucCounter);
BOOLEAN CheckDiscreteValueRange(void* p, void* ucDiscreteArray, BYTE ucNumber, size_t size);
BOOLEAN IsCellNumInScope(BYTE ucCellNum, BYTE ucMinNum, BYTE ucMaxNum);
rt_uint32_t GetTickDifference(rt_uint32_t currentTime, rt_uint32_t lastTime);
WORD* getCrcTable(WORD* len);
BOOLEAN ParseUint32(uint32_t *value, const char *str);
BYTE IsAllZeros(BYTE arr[], int size);

BOOLEAN IsUpperLetter(BYTE ucElement);
SHORT FindSympolPosition(CHAR arr[], BYTE ucSize);


BOOLEAN CheckArrAllZero(BYTE arr[], BYTE size);
BOOLEAN charTransfer(BYTE *rawArr, BYTE *resultArr, BYTE size);
BYTE removeDot(BYTE *rawArr, BYTE srcLen, BYTE *resultArr, BYTE dstLen, BYTE defaultValue);

BOOLEAN CheckDateAllZero(T_DateStruct *Date);
BOOLEAN ChangeVoltTempCurrTable(FLOAT fCellChargeFullVolt);
#ifdef UNITEST
BOOLEAN IsRunning(void);
#endif
BOOLEAN InitProConfig(void);

#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // SOFTWARE_SRC_APP_COMMON_H_  ;
