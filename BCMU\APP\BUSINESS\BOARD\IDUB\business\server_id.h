
#ifndef _SERVER_ID_H_  
#define _SERVER_ID_H_  
#ifdef __cplusplus
extern "C" {
#endif
#define CSU_SERVER_NUM 1


// typedef enum {
//     MOD_604A_MAIN_COMM,        //< 604A main板北向通信模块

//     //守护线程模块放在最后作为MAX标识
//     MOD_DEAMON,                ///< 守护线程模块
// }module_id_e;    
  
#define SAMPLE_SERVER_ID            DATA_SERVER + 1
#define NORTH_SERVER_ID             NORTH_SERVER + 1
#define LED_SERVER_ID               CONTROL_SERVER + 1
//#define BMU_SERVER_ID               SOUTH_SERVER + 1
#define FRE_CONV_SERVER_ID               SOUTH_SERVER + 1

#ifdef __cplusplus
}  
#endif

#endif  // _SERVER_ID_H_  