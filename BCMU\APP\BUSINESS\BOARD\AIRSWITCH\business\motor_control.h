﻿#ifndef _AIRSWITCH_MOTOR_CONTROL_H_
#define _AIRSWITCH_MOTOR_CONTROL_H_

#ifdef __cplusplus
extern "C" {
#endif

#define PWM_FREQUENCY          1000
#define MOTOR_NORMAL_SPEED     60     // 电机速度
#define MOTOR_DETECT_TIMEOUT   1000   // 电机转动超时时间

typedef enum
{
    MOTOR_STOPPED = 0, ///< 停止
    MOTOR_RUNNING,     ///< 运行
}motor_state_e;

typedef enum
{
    MOVING_FORWARD = 0,  ///< 正向(顺时针)
    MOVING_REVERSE,      ///< 反向(逆时针)
    MOVING_BRAKE,        ///< 刹车停止，speed设置为0也是停止但有惯性
}motor_direction_e;

typedef struct
{
    int speed;                      ///< 电机速度比率 0~100
    int act_interval;               ///< 执行间隔(ms)
    int timeout_count;              ///< 超时次数
    int wait;                       ///< 动作执行等待(ms)
    int unauthorize_act_time;       ///< 未授权动作执行时间(ms)
}motor_para_t;

char init_motor_control(void);
char motor_act_close(void);
char motor_act_open(void);
char motor_act_power_on(void);
char unauthorize_act_open(void);
char authorize_act_reset(void);

#ifdef __cplusplus
}
#endif      //< __cplusplus

#endif      //< _AIRSWITCH_MOTOR_CONTROL_H_
