/**
 * @brief 604a main板北向通信头文件
 */

#ifndef _604A_MAIN_NORTH_COMM_H_
#define _604A_MAIN_NORTH_COMM_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "cmd.h"
#include "linklayer.h"
#include "protocol_layer.h"
#include "pb_encode.h"
#include "pb.h"

#define MAX_NORTH_COMM_BUFF_LEN  1024

#define MAX_DEV_INST_NUM      3
#define MODBUS_PROTO_INDEX    (MAX_DEV_INST_NUM - 1)

#define DEMUB_DEFAULT_DEV_ADDR        61

typedef struct {
    void*        data;
    cmd_buf_t*   cmd_buff;
    link_inst_t* link_inst;
    rt_mq_t      north_mq;
}north_mgr_t;
north_mgr_t * init_thread_data(link_inst_t* link_inst, unsigned char mod_id);
void*  init_north(void * param);
void north_comm_th(void *param);
void handle_modbus_data(north_mgr_t* north_mgr);
#ifdef __cplusplus
}
#endif      //< __cplusplus

#endif      //< _604A_MAIN_NORTH_COMM_H_
