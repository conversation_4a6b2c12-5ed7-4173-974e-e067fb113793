
#include <rtthread.h>
#include <rtdef.h>
#include <rtdevice.h>
#include <stdlib.h>
#include "cmd.h"
#include "device_type.h"
#include "sps.h"
#include "protocol_bottom_comm.h"
#include "update_manage.h"
#include "flash.h"
#include "spi_flash.h"
#include "sfud.h"
#include "sfud_def.h"
#include "fal.h"
#include "hisact_id.h"
#include "his_record.h"
#include "utils_rtthread_security_func.h"
#include "utils_data_transmission.h"
//全局变量
static rt_mutex_t s_mutex_file = {0, };
Static trig_ctr_inf_t s_trig_ctr_inf = {0, };
Static bms_update_manage_t s_bms_update_manage = {update_init, 0};
//静态函数声明
Static int get_app_download_rtn(void *cmd_buf); 
Static int parse_update_data_trig(void* dev_inst, void *cmd_buf); 
Static int pack_update_data_trig(void* dev_inst, void *cmd_buf);

//程序更新指令表
static cmd_handle_register_t update_cmd_handle[] = 
{
    {DEV_BMS_CAN_DOWNLOAD, R552_UPDATE_CAN_TRIG, CMD_TYPE_NO_POLL, parse_update_data_trig, pack_update_data_trig},
};

//注册程序更新命令表
void update_manage_init(void) 
{
    short i = 0;
    for(i = 0; i < sizeof(update_cmd_handle)/sizeof(update_cmd_handle[0]); i++) {
        register_cmd_handle(&update_cmd_handle[i]);
    }

    s_mutex_file = rt_mutex_create("file_lock", RT_IPC_FLAG_PRIO);
}

//触发帧解析
Static int parse_update_data_trig(void* dev_inst, void *cmd_buf) 
{
    s_trig_ctr_inf.rtn = get_app_download_rtn(cmd_buf);

    if (BOTTOM_RTN_APP_CORRECT == s_trig_ctr_inf.rtn) {
        s_bms_update_manage.succuss_counts++;
        //rt_kprintf("trigger time is %d\n", s_bms_update_manage.succuss_counts);
    } 
    else 
    {
        s_bms_update_manage.succuss_counts = 0;
    }

    return SUCCESSFUL;
}

//触发帧回复
Static int pack_update_data_trig(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* trig_cmd_buf = (cmd_buf_t*)cmd_buf;
    bottom_comm_cmd_head_t* proto_head = NULL;

    if ( trig_cmd_buf == NULL || trig_cmd_buf->buf == NULL) 
    {
        return FAILURE;
    }

    proto_head = (bottom_comm_cmd_head_t*)trig_cmd_buf->cmd->ack_head;
    proto_head->append = CMD_APPEND_UPDATE_ACK;

    if(s_trig_ctr_inf.rtn == DownloadFrameErr) 
    {
        trig_cmd_buf->data_len = 0;
        trig_cmd_buf->rtn = BOTTOM_RTN_CMD_ERROR;
        return SUCCESSFUL;
    } 
    else 
    {
        trig_cmd_buf->data_len = UPDATE_FILE_NAME_LEN;
        rt_memset_s(trig_cmd_buf->buf, trig_cmd_buf->data_len, 0x00, trig_cmd_buf->data_len);
        rt_memcpy_s(trig_cmd_buf->buf, trig_cmd_buf->buflen, &s_trig_ctr_inf.file_name[0], trig_cmd_buf->data_len);
    }

    return SUCCESSFUL;
}

Static int get_app_download_rtn(void *cmd_buf) 
{
    bottom_comm_cmd_head_t* proto_head = NULL;
    cmd_buf_t* trig_cmd_buf = (cmd_buf_t*)cmd_buf;
    char *p_file_name = s_trig_ctr_inf.file_name;

    if ( trig_cmd_buf == NULL || trig_cmd_buf->buf == NULL) 
    {
        return FAILURE;
    }

    unsigned char *p = trig_cmd_buf->buf;

    proto_head = (bottom_comm_cmd_head_t*)trig_cmd_buf->cmd->req_head;

    if (proto_head->append != CMD_APPEND_UPDATE_REQ)
    {
        return DownloadFrameErr;
    }

    if (trig_cmd_buf->data_len == UPDATE_FILE_NAME_LEN)
    {
        rt_snprintf_s(p_file_name, UPDATE_FILE_NAME_LEN, BMS_R552_UPDATE_FILE_NAME);
        if (strncmp((char*)p, BMS_R552_UPDATE_FILE_NAME, UPDATE_FILE_NAME_LEN) == 0) 
        {
            return BOTTOM_RTN_APP_CORRECT;
        }
    }
    
    return DownFileNameErr;
}

//保存升级信息
int8_t begin_download(uint8_t ucMode)
{
    uint8_t aucBuff[32] ={0,};
    uint32_t boudMode = BAUD_RATE_9600;
    T_FileManageStruct tFileManage;
    rt_spi_flash_device_t dev_w25q;
    uint8_t *pFlag = (uint8_t*)FLAG_PROGRAM;
    const struct fal_flash_dev *flash_dev = NULL;
    flash_dev = fal_flash_device_find("gd32_onchip");
    if(flash_dev == RT_NULL)
    {
        return FAILURE;
    }

    dev_w25q = (rt_spi_flash_device_t)rt_device_find("GD25Q128");
    
    if (RT_NULL == dev_w25q)
    {
        return FAILURE;
    }

    rt_mutex_take(s_mutex_file, RT_WAITING_FOREVER);
    sfud_read((sfud_flash *)dev_w25q->user_data, UPDATEINFO_STATRT, sizeof(T_FileManageStruct), (uint8_t *)&tFileManage);   

    if (ucMode == FLAG_BACKUP )
    {
        if (*pFlag==0xFF)   //烧写后未备份
        {
            tFileManage.tFileAttr.wTotalFrameNum = MAX_PACKET_NUM;//(count-0x8020000)/0x100+1;
            flash_dev->ops.write((FLAG_PROGRAM -GD32F4_FLASH_BASE) , aucBuff, 32);           
        }
        else if(tFileManage.ucFlag != FLAG_APP)
        {
            rt_mutex_release(s_mutex_file);
            return SUCCESSFUL;
        }
    }
    rt_memcpy(tFileManage.tFileAttr.acFileName, BMS_R552_UPDATE_FILE_NAME, FILE_NAME_LEN);
    tFileManage.ucUpdateAddr = get_host_addr();
    tFileManage.ucFlag = ucMode;
    tFileManage.wBaudRate = boudMode;
    tFileManage.wCrc = crc_cal((uint8_t *)&tFileManage, sizeof(tFileManage)-2);
    tFileManage.wCrc = (tFileManage.wCrc % 256) * 256 + tFileManage.wCrc / 256;
    sfud_erase_write((sfud_flash *)dev_w25q->user_data, 0, sizeof(T_FileManageStruct), (uint8_t *)&tFileManage);
    rt_mutex_release(s_mutex_file);


    if(ucMode == FLAG_IAP || ucMode == FLAG_CAN_IAP)
    {
        pub_hisaction_save_msg(HIS_ACTION_SOFTWARE_UPDATE, "update");
    }

    if(ucMode == FLAG_BACKUP)
    {
        pub_hisaction_save_msg(HIS_ACTION_SOFTWARE_BACKUP, "backup");
    }


    NVIC_SystemReset();

    return SUCCESSFUL;
}

int get_bms_trigger_times(void)
{
    return s_bms_update_manage.succuss_counts;
}

