
#ifndef _ACMU_HIS_RECORD_H
#define _ACMU_HIS_RECORD_H

#ifdef __cplusplus
extern "C" {
#endif

#include <sys/time.h>
#include "data_type.h"

// 权限码定义
#define AUTH_CODE_HIS_ACTION    (0x10)
#define AUTH_CODE_HIS_ALARM     (0x11)
#define AUTH_CODE_HIS_DATA      (0x12)
#define AUTH_CODE_EXTREME_DATA  (0x13)

// 记录类型
#define RECORD_TYPE_HIS_ACTION  (0)
#define RECORD_TYPE_HIS_ALARM   (1)
#define RECORD_TYPE_HIS_DATA    (2)
#define RECORD_TYPE_EXTREME_DATA (3)

#define MIN_HIS_ACTION_NUM              (1000)
#define MAX_HIS_ACTION_NUM              (1000*1.2)
#define SAVE_NUM_PER_FILE_HIS_ACTION    (200)
#define FILE_NUM_HIS_ACTION             ((MAX_HIS_ACTION_NUM + SAVE_NUM_PER_FILE_HIS_ACTION - 1)/SAVE_NUM_PER_FILE_HIS_ACTION)

#define READ_HIS_RECORD_TIMEOUT       100

#define MIN_HIS_DATA_NUM              (10000)
#define MAX_HIS_DATA_NUM              (10000*1.2)
#define SAVE_NUM_PER_FILE_HIS_DATA    (2000)
#define FILE_NUM_HIS_DATA             ((MAX_HIS_DATA_NUM + SAVE_NUM_PER_FILE_HIS_DATA - 1)/SAVE_NUM_PER_FILE_HIS_DATA)

#define LEN_EVENTMSG    20           // 操作记录信息长度

// 历史告警
#define MIN_HIS_ALARM_NUM             5000
#define MAX_HIS_ALARM_NUM             (5000*1.2)
#define SAVE_NUM_PER_FILE_HIS_ALARM   1000
#define FILE_NUM_HIS_ALARM            ((MAX_HIS_ALARM_NUM + SAVE_NUM_PER_FILE_HIS_ALARM - 1) / SAVE_NUM_PER_FILE_HIS_ALARM)

// 取出 DCMU_ALM_ID_TOTAL_ALARM 的前8位（高8位），减1后加上指定的后缀值
#define GET_ALM_PARA_BY_ALM_CODE(alm_id, suffix) ( (((alm_id) >> 8) - 1) + (suffix) )

enum software_reset_reason
{
    RESET_REASON_UNKNOWN = 64,       // 未知原因复位（软件复位原因从64开始，与硬件复位原因区分）
    RESET_REASON_UPDATE_ZK = 65,     // 更新字库复位
    RESET_REASON_UPDATE_PROGRAM,     // 更新程序复位
    RESET_REASON_REMOTE_CTRL,        // 远程控制复位
    RESET_REASON_APPTEST,            // Apptest复位
};

// MCU复位原因结构体
typedef struct
{
    unsigned char id;
    char msg[LEN_EVENTMSG];
} mcu_reset_reason_t;

// 单条操作记录信息结构体
typedef struct
{
    time_t save_time;
    unsigned char id1;
    unsigned char id2;
    unsigned char index;
    char msg[LEN_EVENTMSG];
} his_action_record_info;

typedef struct
{
    time_t start_time;          // 告警开始时间
    time_t end_time;            // 告警开始时间
    unsigned short alarm_id;    // 告警ID 不包括含序号的
    unsigned char index;        // 存储index从1开始
    unsigned char alarm_state;  // 告警值 TRUE表示告警 FALSE表示正常
}his_alarm_info_t;

// 历史数据打包信息结构体
typedef struct
{
    unsigned char precision_flag;
    unsigned char storage_type;
    void (*pack_data)(unsigned char* buff, unsigned int* offset, char precision, void* data);
} his_data_pack_t;

typedef struct
{
    short total_load_curr_max;                // 负载总电流最大值
    time_t total_load_curr_max_time;          // 负载总电流最大值记录时间
    short batt_discharge_curr_max;            // 电池放电电流最大值
    time_t batt_discharge_curr_max_time;      // 电池放电电流最大值记录时间
    char batt_temp_max;                       // 电池温度最大值
    time_t batt_temp_max_time;                // 电池温度最大值记录时间
    char env_temp_max;                        // 环境温度最大值
    time_t env_temp_max_time;                 // 环境温度最大值记录时间
    char env_temp_min;                        // 环境温度最小值
    time_t env_temp_min_time;                 // 环境温度最小值记录时间
    short dc_volt_max;                        // 直流电压最大值
    time_t dc_volt_max_time;                  // 直流电压最大值记录时间
} his_extreme_data_t;

int init_dir(void);
int init_his_record(void);
short pub_hisaction_save_msg(unsigned char id1, unsigned char id2,  unsigned char index, char *str);
short pub_hisrecord_read_msg(unsigned char rec_type, unsigned short rec_num, unsigned short offset, void *buff);
int pub_his_data_save_msg(void);
short pub_extremedata_save_msg(his_extreme_data_t *str);
short pub_hisalarm_save_msg(his_alarm_info_t *str);
short pub_get_saved_record_num_msg(unsigned char record_type, unsigned short *saved_num);
unsigned char judge_reset_reason(unsigned char mcu_reset_reason, unsigned char software_reset_reason);
char set_software_reset_reason(unsigned char reset_reason);
unsigned char get_software_reset_reason(void);
unsigned char save_reset_reason(void);

#ifdef __cplusplus
}
#endif

#endif  // _ACMU_HIS_RECORD_H

