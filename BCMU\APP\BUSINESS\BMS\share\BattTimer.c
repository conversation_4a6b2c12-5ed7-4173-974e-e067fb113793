/****************************************************************************
* 函数名称：BattTimer()
* 调    用：
* 被 调 用：
* 输入参数：无
* 返 回 值：无
* 功能描述：电池管理计数器程序，需要用户启动运行它，1分钟进入一次
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
#include "battery.h"
#include "BattTimer.h"
#include "usart.h"
#include "CommCan.h"
#include "BattSleep.h"
#include "sample.h"
#include "led.h"
#include "fm.h"
#include "stddef.h"

#ifdef JUDGECOMM_4G_NET_CAN2
#include "CommCan2.h"
#include "download_tcp.h"
#include "prtclWireless.h"
#include "NetProtocol1363.h"
#endif

Static WORD s_wBatTimeMinCnt = 0;      // 电池分钟循环计数器

Static BOOLEAN AnalyticalAlmTimer(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
Static BOOLEAN WriteAnalyticalAlmData( void );
Static BYTE s_ucSynMinute = 0; //用于主从机同步时间和放电场景参数的同步

static BOOLEAN PowerOffTimerCnt(T_BattDealInfoStruct *pBattDeal); //停电时间保护计数
#ifdef INTELLIGENT_PEAK_SHIFTING
static BOOLEAN FmModeTimeCounter(T_BattInfo *pBattIn);  //调频模式时间计数
static BOOLEAN GetPeakshiftPricess(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
#endif


Static void DealProtoSetInfoTimeout(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    BYTE i=0;
    T_ProtocolSetInfoStruct* ptInfo = &pBattIn->tData.atProtoSetValue[0];

    for (i=0; i<INFO_MAX_NUM; i++)
    {////外协模式下不处理超时
        if (ptInfo->bValid && !GetQtptestFlag())
        {
            TimerPlus(ptInfo->ucUpdateTimer, PROTO_SET_INFO_TIMEOUT_MINUTES);
            if(TimeOut(ptInfo->ucUpdateTimer, PROTO_SET_INFO_TIMEOUT_MINUTES) && RUN_MODE_FREE == ptInfo->ucRunMode)
            {
                ptInfo->bValid = False;
            }
        }
        ptInfo++;
    }

    return;
}

void CalBattChargeMinutes(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    static unsigned int time_count = 0;
    if (IfBduCharge() && (BATT_MODE_CHARGE == pBattDeal->ucChargeMode) && pBattDeal->bChargeTimerEnable)
    {
        time_count++;
        if(time_count%ONE_MINUTES_SECOND == 0)
        {
            TimerPlus(pBattDeal->slBattChargeMinutes, pBattIn->tPara.wChagMaxMinute);
        }
    }
    else
    {
        time_count = 0;
        pBattDeal->slBattChargeMinutes = 0;
    }
}

// 主从机同步系统时间及放电模式,每小时采样一次时间
void SyncTimeAndMode(void)       
{
    T_TimeStruct tTimeNow;
    TimerPlus(s_ucSynMinute, ONE_HOUR_MINUTES); // 从机同步时间和模式计时

    if(s_ucSynMinute >= 60)
    {
        s_ucSynMinute = 0;
        GetTime(&tTimeNow);
        if (tTimeNow.ucHour == 11 || tTimeNow.ucHour == 23)
        {
            SynTimeAndMode();
        }
    }
}

//停电时间保护计数，解决圈复杂度
static BOOLEAN PowerOffTimerCnt(T_BattDealInfoStruct *pBattDeal)
{
    if (True == pBattDeal->bPowerOff)
    {
         TimerPlus(pBattDeal->wPowerOffTime, TWO_THOUSAND_PER_MINUTES);
    }
    else
    {
        pBattDeal->wPowerOffTime = 0;
    }

    return True;
}

#ifdef INTELLIGENT_PEAK_SHIFTING
static BOOLEAN FmModeTimeCounter(T_BattInfo *pBattIn)
{
    if(pBattIn->tData.tFmData.ucFmStatus)
    {
        if(TimeOut(GetFmTimeCounter(),EXIT_FM_TIME))
        {
            SetFmMode(False);
            SetFmPower(0);
        }
        else
        {
            SetFmTimeCounter();
        }
    }

    return True;
}

static BOOLEAN GetPeakshiftPricess(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if(JudgePeakShiftCondition(pBattIn,pBattDeal,GetShiftPeakPara()))
    {
        pBattDeal->ucElectricityPrice = GetElectricityClass();
        return True;
    }
    else
    {
        pBattDeal->ucElectricityPrice = ELECTRICITY_PRICE_NULL;
        return False;
    }
} 
#endif


Static void BattIdleCount(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if ( BATT_MODE_DISCHARGE != pBattDeal->ucChargeMode 
        || pBattIn->tData.fCurrMinDet <= fabs(pBattIn->tData.fBattCurr)
        || pBattIn->tData.bDischargeProtect
#ifdef JUDGECOMM_4G_NET_CAN2
        || !JudgeCan2CommDisconnect() // 存在CAN2北向通信重新计时
        || !JudgeNet1363CommDisconnect() // 存在1363网口北向通信重新计时
        || !JudgeNetRemoteCommDisconnect() // 存在远程下载固件时重新计时
        || !Judge4GCommDisconnect() // 存在4G通信时重新计时
        || get_snmp_comm_status() // 存在SNMP北向通信重新计时
#endif
        || !JudgeCommDisconnect() // 存在485通讯重新计时
        || !JudgeCanCommDisconnect() ) // 存在CAN1北向通信重新计时
    {
        pBattDeal->wBattIdleTimer = 0;
        /*静置24小时后如果母排电压≥44V会进行将输出电压调高1V，如果此时有输出电流，那么静置计数器会清零。BattIdleMrg逻辑不再
        执行，调压涉及的标志不会被恢复，因此此处需要恢复，避免再次进入BattIdleMrg时不会执行调压操作*/
        RecovVoltChangeFlag();
        #ifdef ZXESM_R321_APTOWER_VERSION
        pBattDeal->ucBattIdleStatus = 0;
        #endif
        return;
    }

    if (pBattDeal->wBattIdleTimer < BATT_IDLE_MUNITE_MAX)
    {
        pBattDeal->wBattIdleTimer++;
        #ifdef ZXESM_R321_APTOWER_VERSION
        pBattDeal->ucBattIdleStatus = 1;
        #endif
    }

    return;
}

Static BYTE getSOCRange(T_BattInfo *pBattIn)
{
    BYTE  j=0;
    FLOAT afSOCRange[SOC_RANGE_NUM+1] = {0.0f, 1.0f, 5.0f, 95.0f, 99.0f, 100.0f + RANGE_BOUNDARY_OFFSET};
    for(j=0; j<ARR_SIZE(afSOCRange)-1; j++)
    {
        if((afSOCRange[j] - (FLOAT)pBattIn->tData.wBattSOC) < 0.00001f  && (afSOCRange[j+1] - (FLOAT)pBattIn->tData.wBattSOC) > 0.00001f ) //包含下界值
        {
            return (SOC_RANGE_NUM - 1 - j);
        }
    }
    return 0xFF;
}

Static BYTE getCellMediumTempRange(T_BattInfo *pBattIn)
{
    BYTE  i=0;
    FLOAT afCellMediumTempRange[CELL_TEMP_RANGE_NUM+1] = {DEFAULT_TEMP_MIN - RANGE_BOUNDARY_OFFSET, 0.0f, 15.0f, 35.0f, 45.0f, 55.0f, 65.0f, DEFAULT_TEMP_MAX};
    for(i=0; i<ARR_SIZE(afCellMediumTempRange)-1; i++)
    {
        if((afCellMediumTempRange[i] - pBattIn->tData.fCellTempMedium) < -0.00001f && (afCellMediumTempRange[i+1] - pBattIn->tData.fCellTempMedium) > -0.00001f ) //包含上界值
        {
            return i;
        }
    }
    return 0xFF;
}

Static BOOLEAN AnalyticalAlmTimer(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    T_AnalyseInfoStruct tAnalyseInfo;
    BYTE ucSOCTag = getSOCRange(pBattIn);
    BYTE ucCellMediumTempTag = getCellMediumTempRange(pBattIn);
    rt_memset(&tAnalyseInfo, 0, offsetof(T_AnalyseInfoStruct,wCheckSum)+2);
    GetAnalyticalAlm( &tAnalyseInfo );
    if(ucSOCTag == 0xFF || ucCellMediumTempTag == 0xFF)
    {
        return False;
    }
    //统计各温度和SOC区间运行状态累计时间
    TimerPlus(tAnalyseInfo.aulBattCellTempandSOCRangeTimer[ucCellMediumTempTag][ucSOCTag], BATT_MAX_LIFETIME_INTERVAL);
    //欠压保护状态累计时间、累计次数统计
    if(pBattDeal->bCellLowVolPrt && GetPMOSStatus()) // PMOS关闭后的时间计入欠压保护关机累计时间
    {
        TimerPlus(tAnalyseInfo.ulBattUVPTimer, BATT_MAX_LIFETIME_INTERVAL);
    }
    if(tAnalyseInfo.bBattLowVolPrtTriggered)
    {
        tAnalyseInfo.bBattLowVolPrtTriggered = 0;
        TimerPlus(tAnalyseInfo.wBattUVPCounter, ALARM_RECORDS_MAX_COUNT);
    }
    //过压保护状态累计时间、累计次数统计
    if(pBattDeal->bRealCellOverVolPrt)
    {
        TimerPlus(tAnalyseInfo.ulBattOVPTimer, BATT_MAX_LIFETIME_INTERVAL);
    }
    if(tAnalyseInfo.bBattOverVolPrtTriggered)
    {
        tAnalyseInfo.bBattOverVolPrtTriggered = 0;
        TimerPlus(tAnalyseInfo.wBattOVPCounter, ALARM_RECORDS_MAX_COUNT);
    }
    //过流保护累计时间和累计次数统计
    if(pBattDeal->bBattOverCurrPrt)
    {
        TimerPlus(tAnalyseInfo.ulBattOCPTimer, BATT_MAX_LIFETIME_INTERVAL);
    }
    if(tAnalyseInfo.bBattOverCurrPrtTriggered)
    {
        tAnalyseInfo.bBattOverCurrPrtTriggered = 0;
        TimerPlus(tAnalyseInfo.wBattOCPCounter, ALARM_RECORDS_MAX_COUNT);
    }
    //欠压保护关机累计时间、累计次数统计

    if(tAnalyseInfo.ucBattUVPShutDownFlag == RESTART_FLAG)
    {
        tAnalyseInfo.ucBattUVPShutDownFlag = NO_FLAG;
        tAnalyseInfo.ulBattUVPShutDownTimer += (ULONG)(difftime(tAnalyseInfo.tTime2, tAnalyseInfo.tTime) / 60.0);
        tAnalyseInfo.tTime = (time_t)(-1);
        tAnalyseInfo.tTime2 = (time_t)(-1);
        TimerPlus(tAnalyseInfo.wBattUVPShutDownCounter, ALARM_RECORDS_MAX_COUNT);
    }
    TransAnalyticalAlm( &tAnalyseInfo, INFO_NOT_WRITTEN );
    return True;
}

Static BOOLEAN WriteAnalyticalAlmData( void )
{
    T_AnalyseInfoStruct tAnalyseInfo;
    s_wBatTimeMinCnt++;
    //每60分钟写入一次电芯记录信息
    if ( s_wBatTimeMinCnt < 60)
    {
        return False;
    }
    rt_memset(&tAnalyseInfo, 0, offsetof(T_AnalyseInfoStruct,wCheckSum)+2);
    GetAnalyticalAlm( &tAnalyseInfo );
    TransAnalyticalAlm( &tAnalyseInfo, INFO_WRITTEN );
    s_wBatTimeMinCnt = 0;
    return True;
}

/***************************************************************************
 * @brief   不可逆告警计时
 **************************************************************************/
Static BOOLEAN IrsAlarmCount(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if(pBattIn->tData.bIrreversibleAlarm)
    {
        TimerPlus(pBattDeal->ucIrreversibleAlarmCount, IRREVSB_ALARM_COUNT);
    }
    else
    {
        pBattDeal->ucIrreversibleAlarmCount = 0;
    }
    return True;
}

void BattTimer(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if (BATT_MODE_CHARGE == pBattDeal->ucChargeMode)
    {
        TimerPlus(pBattDeal->slDefenseBattChargeMinutes, THREE_MINUTES);
    }

    if (pBattDeal->bSelfRechargeTimerEnable)
    {
        TimerPlus(pBattDeal->slSelfRechargeTimer, ONE_HOUR_MINUTES);
    }

    if (BATT_MODE_STANDBY == pBattDeal->ucChargeMode)
    {
        TimerPlus(pBattDeal->wBattFullHoldMinutes, BATT_FULLHOLD_MUNITE_MAX);
        #ifdef DEVICE_USING_R321
        TimerPlus(pBattDeal->slStandByTimer, pBattIn->tPara.wBattRefreshPeriod * MINUTES_PER_DAY);
        #endif
    }

    if (BATT_MODE_DISCHARGE == pBattDeal->ucChargeMode && DISCHARGE_LA_PRIOR_AND_SWITCH == pBattDeal->ucNextDischargeMode)
    {
        TimerPlus(pBattDeal->wLABattDischargeTimer, pBattIn->tPara.wLABattDurPerDischg);
    }
    else
    {
        pBattDeal->wLABattDischargeTimer = 0;
    }

    if (BATT_MODE_DISCHARGE == pBattDeal->ucChargeMode)
    {
        TimerPlus(pBattDeal->wNeedChgTimer, START_CHG_JUDGE_INTERVAL);
    }
    else
    {
        pBattDeal->wNeedChgTimer = 0;
    }

    if(pBattDeal->bCellUnderVolt && (BATT_MODE_CHARGE == pBattDeal->ucChargeMode || IfBduCharge()))
    {
        TimerPlus(pBattDeal->ucCellUnderVoltTimer, CELL_UNDER_VOLT_MIN);
    }
    else
    {
        pBattDeal->ucCellUnderVoltTimer = 0;
    }

    // if (True == pBattDeal->bPowerOff)
    // {
    //      TimerPlus(pBattDeal->wPowerOffTime, TWO_THOUSAND_PER_MINUTES);
    // }
    // else
    // {
    //     pBattDeal->wPowerOffTime = 0;
    // }
    PowerOffTimerCnt(pBattDeal);

    if(pBattIn->tData.bIrreversibleAlarm)
    {
        TimerPlus(pBattDeal->ucIrreversibleAlarmCount, IRREVSB_ALARM_COUNT);
    }
    else
    {
        pBattDeal->ucIrreversibleAlarmCount = 0;
    }

#ifdef INTELLIGENT_PEAK_SHIFTING
    FmModeTimeCounter(pBattIn);  //调频充放电时间计数
    PowerOffPeakTimeCounter();
    GetPeakshiftPricess(pBattIn,pBattDeal);
#endif
    if(GetVoltTwoClassTimeStatus())
    {
        VoltTwoClassTimeCounter();
    }
    IrsAlarmCount(pBattIn, pBattDeal);

    DealProtoSetInfoTimeout(pBattIn, pBattDeal);

    BattIdleCount(pBattIn, pBattDeal);
    AnalyticalAlmTimer(pBattIn, pBattDeal);
    WriteAnalyticalAlmData();

    return;
}
