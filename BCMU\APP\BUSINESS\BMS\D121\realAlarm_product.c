/**************************************************************************
* Copyright (C) 2010-2011, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：ZXDU18 CTL板软件
* 文件名称：alarm.c
* 文件说明：告警模块
* 作    者  ：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/
#include "common.h"
#include "sample.h"
#include "hisdata.h"
#include "utils_heart_beat.h"
#include "thread_id.h"
#include "realAlarm.h"
#include "alarm_in.h"
#include "para.h"
#include "comm.h"
#include "protocol.h"
#include "CommCan.h"
#include "commBdu.h"
#include "battery.h"
#include "ism330dhcx.h"
#include "comm.h"
#include "led.h"
#include "fileSys.h"
#include "usart.h"
#include "const_define.h"
#include "BalanceTimeStatistics.h"
#include "utils_rtthread_security_func.h"
#define ANALOG_ALARM_INIT(tAlmInfo, alarmName) do{	\
    tAlmInfo.pucRealAlarm = &s_tBcmAlarmReal.alarmName; \
    tAlmInfo.pucStateAlarm = &s_tBcmAlarmFlag.alarmName;    \
    tAlmInfo.pucCounter = &s_tBcmAlarmCounter.alarmName;    \
    tAlmInfo.pucCounterThreshold = &s_tBcmAlarmCounterMax.alarmName;    \
    tAlmInfo.pExceptionHandling  = NULL;        \
    tAlmInfo.ucAlarmType = FAULT;   \
    tAlmInfo.pwRetryCounter = NULL;    \
    tAlmInfo.pwRetryCounterMax = NULL;    \
    }while(0)

#define DIGITAL_ALARM_INIT(tAlmInfo, alarmName) do{	\
    tAlmInfo.pucRealAlarm = &s_tBcmAlarmReal.alarmName; \
    tAlmInfo.pucStateAlarm = &s_tBcmAlarmFlag.alarmName;    \
    tAlmInfo.pucCounter = &s_tBcmAlarmCounter.alarmName;    \
    tAlmInfo.pucCounterThreshold = &s_tBcmAlarmCounterMax.alarmName;    \
    tAlmInfo.pExceptionHandling  = NULL;        \
    tAlmInfo.ucAlarmType = FAULT;   \
    tAlmInfo.ucState = s_tBcmAlarmTmp.alarmName;    \
    tAlmInfo.pwRetryCounter = NULL;    \
    tAlmInfo.pwRetryCounterMax = NULL;    \
    }while(0)

#define PROTECT_SHIELD_ALARM(_prtName, _almName) do{ \
    if( s_tBcmAlarm._prtName == 1 ) {	\
    s_tBcmAlarm._almName = NORMAL;	\
        }	\
}while(0)

#define CLEAR_ALAEM(_almName) do{   \
    s_tBcmAlarmReal._almName = NORMAL; \
    s_tBcmAlarm._almName = NORMAL; \
    s_tBcmAlarmCounter._almName = 0; \
    s_tBcmAlarmFlag._almName = 0; \
}while(0)

/***********************  变量定义  ************************/
/*  Bak--代表上次数据或告警
    Flag 告警状态标志
    Counter 告警计数器
    Shield 告警屏蔽开关 */
    
static T_AlmDataStruct s_tAlarmData;
static T_AlmParaStruct s_tAlarmPara;
Static T_BCMAlarmStruct s_tBcmAlarm = {0, };      //屏蔽后的告警
Static T_BCMAlarmStruct s_tBcmAlarmReal = {0, };  //未屏蔽的告警
static T_BCMAlarmStruct s_tBcmAlarmBak = {0, };
static T_BCMAlarmStruct s_tBcmAlarmFlag = {0, };
static T_BCMAlarmStruct s_tBcmAlarmCounter = {0, };
static T_BCMAlarmStruct s_tBcmAlarmCounterMax = {0, };
Static T_BCMAlarmStruct s_tBcmAlarmShield = {0, };
static T_BCMAlarmStruct s_tBcmAlarmTmp = {0, };
static T_BCMAlarmStruct s_tBcmAlarmStartUp = {0, };   //启动最初阶段的告警
Static T_DefenceInfoStruct s_tDefenceInfo = {0, };  //布防信息
static T_SysPara       s_tSysPara;

static WORD s_wBcmAlmStartUpTimer = 0;          //启动最初阶段告警判断计数器
static WORD iterFlag = 0;          //启动最初阶段告警判断计数器
Static BOOLEAN s_bBcmAlmStartUpFlag = True;     //启动最初阶段告警判断标志

static BOOLEAN s_bGprsBattAlmEn = False;       //GPRS电池锁死/解除
//static BOOLEAN s_bMainAlarmChanged = False;        // 重要告警变化标志，保留，20110406，yang.an
//static BOOLEAN s_bMainAlarmExist = False;        // 重要告警存在标志
static BOOLEAN s_bBalanceFault = False;
static BYTE s_aucBcmDataFlag[6] = { 1, 1, 1, 1, 1, 1 };
BYTE   g_ucAlmLevelChge = False;
Static WORD s_wCommLostCnt = 0;      // 通讯防盗计数器
static BOOLEAN s_bGprsAutoUpDataFlag = False;

static BYTE s_ucDisCurrHighFlag = 0;
static BYTE s_ucChgCurrHighFlag = 0;
static BYTE s_ucLastCurrHighFlag = 0;
static FLOAT s_fBattVoltThresh = 12.0f;
static BOOLEAN s_bPowerCommFlag = FAULT;
static BOOLEAN s_bPowerCommShieldFlag = False;
Static BOOLEAN s_bIfRecoverBDUConnTempHighPrt = True; //是否为可自动恢复的BDU连接器温度高保护
static WORD s_wPowerCommTimer = 0;   // 电源通信断计时
/* Bdu连接器高温告警24h产生三次不恢复 */
#define TEMP_ALM_RECREATE_TIMEOUT (24 * 60 * 60 * RT_TICK_PER_SECOND)
#define BDU_CONN_TEMP_ALM_COUNTER 3
Static BYTE s_bBduConnTempAlmStatusBak = 0;
Static BYTE s_bBduConnTempAlmCounter = 0;
Static rt_uint32_t s_tick_array[BDU_CONN_TEMP_ALM_COUNTER + 1] = {0};

/* 实时、历史告警显示结构数组 */
static T_DisRealAlarmStruct s_atDisRealAlarm[ALARM_NUM + 1];
static T_RealAlm256SaveStruct s_tRealAlmSave;
static T_HardwareParaStruct   s_tHardwarePara;
static T_NewBattSaveStruct s_tCellDamageSave;

static T_StateListenStruct s_atCellVoltTooHigh[CELL_VOL_NUM];
static T_StateListenStruct s_atCellVoltPoor[CELL_VOL_NUM];
static BOOLEAN s_bExistAlarmFlag = False; 

Static WORD s_wCircOpenCnt = 0;
Static WORD s_wCircShortCnt = 0;
Static BYTE s_uCircOpenTimes = 0;
Static BOOLEAN s_bRestartIC = False;

/*重要告警变化标识*/
Static BOOLEAN s_bMainAlarmChanged[BCM_ALARM_NUM] = {False};
Static BOOLEAN s_bMainAlarmExist[BCM_ALARM_NUM] = {False};
Static BYTE s_ucAlarmStatusMap[] = {ALM_NORMAL,ALM_ABNORMAL,ALM_CRITICAL,ALM_SYS_ABNORMAL};

/*********************  静态函数原型定义  **********************/
Static BOOLEAN IsAnyAlarmExistNew();
Static BYTE AlarmIndicatorStatus(BYTE IndicatorValue);
static BOOLEAN  JudgeBcmAlarm( void );
static void     JudgeBcmAnalogAlarm( void );
static void     JudgeBcmDigitalAlarm( void );
static void     JudgeBcmAlarmPriority( void );
static void     DealDisAlarm( WORD  wID, BYTE ucState );
static BOOLEAN  ClearSaveRealAlm(WORD wID);
static void     SetAlarmJudgeCounter(void);
static void     GetAlarmInfo(void);
static INT32S   CellLowPrtHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus);
static INT32S   CellDynamicLowPrtHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus);
static INT32S   BattVoltLowPrtHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus);
static INT32S   BattPowerOffPrtHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus);
static BOOLEAN  IsBattTheft(void);
static INT32S   CurrHighPrtDelay(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus, WORD wRetryDelay, BYTE ucLastPrtDelayFlag);
static INT32S   CellPoorConsisHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus);
static void     checkSavedAlarm( void );
static void     SetTheftAlm(BOOLEAN bAlm);
static BOOLEAN  IsCellPoorConsisStart(FLOAT fBattVol, FLOAT fBattCurr);
Static BOOLEAN  IsEqualCircuitFault(void);
static INT32S   CellDamageHandle(struct T_AlmInfoTmp const *pAlmInfo, BOOLEAN *pbTmpStatus);
static BOOLEAN 	JudgeCommAntiTheft(void);
static void 		ReadDefenceStatus(void);
static FLOAT CalCellVoltLowPrtThre(FLOAT fCellTempMin, FLOAT fCellVoltLowPrt);
static BOOLEAN JudgeIfExixtAlarm(BYTE *pAlarm,WORD wLength);
static FLOAT CalCellDynamicLowPrtThre(BYTE Num , FLOAT MinCellTemp);
Static BOOLEAN IsAnyAlarmExist();
static BOOLEAN JudgeAlarmInfoType(BOOLEAN condition);
static void EmergencyAlarmChangeSaveHisData(void);
static BOOLEAN GetMainAlarmChanged(void);
static BOOLEAN GetMainAlarmExist(void);
static BOOLEAN AnalyticalAlmCheck( WORD  wID );
static BOOLEAN DealPowerCommFailCount(void);
static BYTE JudgePowerCommFailAlarmSheild(void);
Static BOOLEAN CheckBduConnTempAlmCounter( T_BCMAlarmStruct* tBcmAlarm );
static void JudgeCellTempAbnormal(T_BCMDataStruct *tBcmData);
#ifdef RESTORE_ALM_LED_OFF_AND_NOT_RESTORE_ALM_LED_ON
static BOOLEAN IsAlarmExistForNotRestoreLedOnOne();
static BOOLEAN IsAlarmExistForNotRestoreLedOnTwo();
#endif
static BOOLEAN IsAlarmExistNormal();
Static BOOLEAN AutoCloseDefence(void);

#ifdef BOARD_Temp_High_Prt_BDPB
static INT32S BoardTempHighPrtHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus);
#endif

Static BOOLEAN CheckBduConnTempAlmCounter( T_BCMAlarmStruct* tBcmAlarm )
{
    rt_uint32_t current_tick, first_tick, thd_tick = 0;
    if (tBcmAlarm->ucBDUConnTempHighPrt != s_bBduConnTempAlmStatusBak) {
        if (tBcmAlarm->ucBDUConnTempHighPrt == FAULT){
            current_tick = rt_tick_get();
            RETURN_VAL_IF_FAIL(s_bBduConnTempAlmCounter < (BDU_CONN_TEMP_ALM_COUNTER + 1), FAILURE);
            s_tick_array[s_bBduConnTempAlmCounter] = current_tick;
            s_bBduConnTempAlmCounter++;
        }
        s_bBduConnTempAlmStatusBak = tBcmAlarm->ucBDUConnTempHighPrt;
    }
    if (s_bBduConnTempAlmCounter == BDU_CONN_TEMP_ALM_COUNTER + 1)
    {
        s_bBduConnTempAlmCounter = BDU_CONN_TEMP_ALM_COUNTER;
        // 数组后三个元素向前挪动，暂时未考虑做成公共组件
        s_tick_array[0] = s_tick_array[BDU_CONN_TEMP_ALM_COUNTER - 2];
        s_tick_array[BDU_CONN_TEMP_ALM_COUNTER - 2] = s_tick_array[BDU_CONN_TEMP_ALM_COUNTER - 1];
        s_tick_array[BDU_CONN_TEMP_ALM_COUNTER - 1] = s_tick_array[BDU_CONN_TEMP_ALM_COUNTER];
    }
    thd_tick = s_tick_array[BDU_CONN_TEMP_ALM_COUNTER - 1];
    first_tick = s_tick_array[0];
    if ((GetTickDifference(thd_tick, first_tick) < TEMP_ALM_RECREATE_TIMEOUT && thd_tick != 0) || !GetBDUConnTempHighRecoverFlag())
    {
        s_tBcmAlarm.ucBDUConnTempHighPrt     = FAULT;
        s_tBcmAlarmReal.ucBDUConnTempHighPrt = FAULT;
        SetBDUConnTempHighRecoverFlag(False);
    }
    return SUCCESSFUL;
}

BOOLEAN SetBDUConnTempHighRecoverFlag(BOOLEAN bConnTempHighRecoverFlag)
{
    s_bIfRecoverBDUConnTempHighPrt = bConnTempHighRecoverFlag;
    return 0;
}

//BDU连接器温度高保护是否可以自动恢复
BOOLEAN GetBDUConnTempHighRecoverFlag(VOID)
{
    return s_bIfRecoverBDUConnTempHighPrt;
}

/******************************************************************************
**函 数 名：ALarmCommon()
**输    入： T_AlmInfoStruct *tAlmInfo 
**输    出：无
**调用模块：
**功能描述：根据输入数据，告警门限判断告警
* 作    者：刘斌
**时    间：2012-6-28
**修改记录：
* 日    期      版  本      修改人      修改摘要      
******************************************************************************/
#if 1
static BOOLEAN ALarmCommon( T_AlmInfoStruct *tAlmInfo , BOOLEAN   bStatus)
{
    T_AlmInfoStruct *pAlmInfo;

    if (tAlmInfo->pucRealAlarm == NULL)
    {
        return  FAILURE;
    }
    pAlmInfo = (T_AlmInfoStruct *)tAlmInfo;
    if (*(pAlmInfo->pucStateAlarm) == bStatus ) //与上次判断结果相同，累计次数加1
    {//ALARM_COUNTER
        if (*(pAlmInfo->pucCounter) < *(pAlmInfo->pucCounterThreshold) )
        {
            (*(pAlmInfo->pucCounter))++;
        }
    }
    else    //与上次判断结果不同，累计次数清0
    {
        *(pAlmInfo->pucStateAlarm) = bStatus;
        *(pAlmInfo->pucCounter)  = 0;
    }
            
    return SUCCESSFUL;
}

/******************************************************************************
**函 数 名：InitAlarm(void)
**输    入：无
**输    出：无
**调用模块：
**功能描述：初始化
* 作    者：余志刚
**时    间：2018.11.9
**修改记录：
* 日    期      版  本      修改人      修改摘要      
******************************************************************************/ 

void InitAlarm(void)
{
    BYTE ucTheftAlm = 0;
    BYTE ucDcrFaultSta = 0;

    rt_memset_s( &s_tBcmAlarm, sizeof(T_BCMAlarmStruct), 0, sizeof(T_BCMAlarmStruct));
    rt_memset_s( &s_tBcmAlarmReal, sizeof(T_BCMAlarmStruct), 0, sizeof(T_BCMAlarmStruct));
    rt_memset_s( &s_tBcmAlarmBak, sizeof(T_BCMAlarmStruct), 0, sizeof(T_BCMAlarmStruct));
    rt_memset_s( &s_tBcmAlarmFlag, sizeof(T_BCMAlarmStruct), 0, sizeof(T_BCMAlarmStruct));
    rt_memset_s( &s_tBcmAlarmCounter, sizeof(T_BCMAlarmStruct), 0, sizeof(T_BCMAlarmStruct));
    rt_memset_s( &s_tBcmAlarmShield, sizeof(T_BCMAlarmStruct), 0, sizeof(T_BCMAlarmStruct));
    rt_memset_s( &s_tBcmAlarmTmp, sizeof(T_BCMAlarmStruct), 0, sizeof(T_BCMAlarmStruct));
    rt_memset_s( &s_tBcmAlarmStartUp, sizeof(T_BCMAlarmStruct), 0, sizeof(T_BCMAlarmStruct));
    rt_memset_s( &s_tHardwarePara, sizeof(T_HardwareParaStruct), 0, sizeof(T_HardwareParaStruct));
    readBmsHWPara(&s_tHardwarePara);

    checkSavedAlarm();
    ucTheftAlm = getResetData(BATT_THEFT);
    ucDcrFaultSta = getResetData(DCR_FAULT_ALM_PRT);
    // 如果掉电保存了直流内阻异常保护，那么继续保持保护状态；
    if(ucDcrFaultSta == DCR_PROTECT)
    {
        s_tBcmAlarm.ucDcrFaultPrt = FAULT;
        s_tBcmAlarmReal.ucDcrFaultPrt = FAULT;
    }   
    if (ucTheftAlm != 1)
    {
        SetTheftAlm(NORMAL);
    }
    else
    {
        s_tBcmAlarm.ucBattLoseAlm     = ucTheftAlm;
        s_tBcmAlarmReal.ucBattLoseAlm = ucTheftAlm;
    }

    rt_memset_s( &s_tCellDamageSave, sizeof(T_NewBattSaveStruct), 0, sizeof(T_NewBattSaveStruct));
    s_tAlarmData.wDischgCurrHighPrtCntMax   = OVERCURR_RERTY_DELAY;
    s_tAlarmData.wBduBusVoltLowPrtCntMax    = OVERCURR_RERTY_DELAY;
    s_tAlarmData.wBattShortCutCntMax        = SHORT_RERTY_DELAY;
    s_tAlarmData.wChgCurrHighPrtCntMax      = OVERCURR_RERTY_DELAY;
    s_tAlarmData.wCurrLimitInvalidCntMax    = OVERCURR_RERTY_DELAY;

    s_bBalanceFault = False;  //均衡电路发生故障后，重启才能恢复   
    ReadDefenceStatus();//重启时读取保存的布防状态
    return;
}

/******************************************************************************
**函 数 名：BOOLEAN JudgeAnalogStatus(  T_AlmInfoStruct *tAlmInfo  )
**输    入：无
**输    出：无
**调用模块：
**功能描述：根据模拟量判断告警状态
* 作    者：刘斌
**时    间：2012-6-28
**修改记录：
* 日    期      版  本      修改人      修改摘要      
******************************************************************************/ 
static BOOLEAN JudgeAnalogStatus( T_AlmInfoStruct *tAlmInfo )
{
    T_AlmInfoStruct *pAlmInfo;
    FLOAT fDelta = 0;
    BOOLEAN   bStatus = NORMAL, bTempStatus=NORMAL;
    INT32S  slRet = 0;

    if ( tAlmInfo == NULL )//模拟量告警给不出修改
    {
        return FAILURE;
    }   

    pAlmInfo = (T_AlmInfoStruct *)tAlmInfo;

    if (*(tAlmInfo->pucRealAlarm) != NORMAL )  
    {
        fDelta = tAlmInfo->fValueAdd;
    }
    else 
    {
        fDelta = 0;
    }

    if (NULL != tAlmInfo->pExceptionHandling)
    {
        slRet = tAlmInfo->pExceptionHandling(tAlmInfo, &bTempStatus);
    }

    if(slRet < 0)
    {
        return FAILURE;
    }
    else if (RTN_ALM_STATUE == slRet)
    {
        bStatus = bTempStatus;
    }
    else
    {
        if (tAlmInfo->fValueAdd < 0)   //判断为高，回差为负
        {
            if (tAlmInfo->fValue > (tAlmInfo->fThread+fDelta))
            {
                bStatus = tAlmInfo->ucAlarmType;
            }
            else 
            {
                bStatus = NORMAL;
            }   
        }
        else
        {
            if ( tAlmInfo->fValue < (tAlmInfo->fThread+fDelta))
            {
                bStatus = tAlmInfo->ucAlarmType;
            }
            else 
            {
                bStatus = NORMAL;
            }   
        }   
    }
    ALarmCommon(pAlmInfo, bStatus);  //yyhpclint 

    return SUCCESSFUL;
}

/******************************************************************************
**函 数 名：BOOLEAN JudgeDigitalAlarm( BYTE ucJudgeTrueInPut,BOOLEAN bTrueFlag )
**输    入：无
**输    出：无
**调用模块：
**功能描述：根据是否开关状态，返回开关表示意义
* 作    者：刘斌
**时    间：2012-6-28
**修改记录：
* 日    期      版  本      修改人      修改摘要      
******************************************************************************/ 
static BOOLEAN JudgeDigitalAlarm( T_AlmInfoStruct *tAlmInfo )
{
    BOOLEAN   bStatus = 0, bTempStatus, btemp;  
    INT32S  slRet = 0;
    
    if (tAlmInfo->ucState == NORMAL)
    {
        bStatus = NORMAL;  
    }
    else
    {
        bStatus = tAlmInfo->ucAlarmType;
    }

    if (NULL != tAlmInfo->pExceptionHandling)
    {
        slRet = tAlmInfo->pExceptionHandling(tAlmInfo, &bTempStatus);
    }

    if(slRet < 0)
    {
        return FAILURE;
    }
    
    if (RTN_ALM_STATUE == slRet)
    {
        bStatus = bTempStatus;
    }

    btemp = ALarmCommon(tAlmInfo, bStatus);  

    return (btemp);
}

/****************************************************************************
* 函数名称：JudgeAlarm
* 调    用：
* 被 调 用：
* 输入参数：无
* 返 回 值：无
* 功能描述：告警管理模块主函数，根据采样结果判断是否有告警产生或消除
            并保存历史数据
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要        
***************************************************************************/
void JudgeAlarm( void* parameter )
{   
    WORD  wRealAlarmTotal  = 0;    //实时告警总条数
    T_CtrlOutStruct tCtrl;
	Button_State u_tButSta = {0};

    InitAlarm();
    pre_thread_beat_f(THREAD_ALARM_MANAGE);
#ifdef UNITEST
    while (IsRunning())
#else
    while(1)
#endif			
    {
        thread_beat_go_on(THREAD_ALARM_MANAGE);
        if(GetApptestFlag())
        {
            rt_thread_delay(1000);
            continue;
        }
        rt_thread_delay(ALM_TASK_PERIOD);

        //s_bMainAlarmChanged = False;        //控制蜂鸣器用，保留，20110406，yang.an
        //s_bMainAlarmExist = False;   
        rt_memset_s(&s_bMainAlarmChanged, BCM_ALARM_NUM, False, BCM_ALARM_NUM); 
        rt_memset_s(&s_bMainAlarmExist, BCM_ALARM_NUM, False, BCM_ALARM_NUM);

        if(s_wBcmAlmStartUpTimer < ALARM_DELAY_COUNTER)    //重启后,先按照以前实时告警判断
        {
            s_wBcmAlmStartUpTimer++;
            s_bBcmAlmStartUpFlag = True;
        }
        else
        {
            s_bBcmAlmStartUpFlag = False;
        }
        JudgeBcmAlarm();
        GetButtonState(&u_tButSta);

        // 告警级别屏蔽或者软件防盗延时设置为0时自动撤防
        AutoCloseDefence();

        CheckBduConnTempAlmCounter(&s_tBcmAlarm);

        GetCtrlOut( &tCtrl );   
        wRealAlarmTotal = CalRealAlarmTotal(EXTERNAL_ALARM, NULL);   //  计算实时告警总条数
        //若没有实时告警，关闭声/光告警，保留
        if ( 0 == wRealAlarmTotal || IsSleep() ) 
        {
            s_tAlarmData.bBuzze = False;
        }
        else
        {
            //重要告警存在且发生变化或告警级别变化才打开蜂鸣器modified by xueb
            if ( (g_ucAlmLevelChge == True) || (GetMainAlarmChanged() == True) )
            {
                g_ucAlmLevelChge = False;           
            }

            s_tAlarmData.bBuzze = IfSysInvalid();	//系统异常告警发生才响蜂鸣器 Added by fengfj, 2019-09-07 21:01:37
        }
        if(tCtrl.bCtrlFlag == 0x01) //QTP蜂鸣器控制
        {
        }
        else if (!s_tAlarmPara.bBuzzEnable)
        {
            tCtrl.bBuzz = 0;
        }
        else if (s_tBcmAlarm.ucBattLoseAlm != NORMAL)
        {
            tCtrl.bBuzz = 2;
        }
        else if (u_tButSta.ucCurrentMode == 0 && u_tButSta.ucLed_CurrentMode == 0) //正常模式
        {
            tCtrl.bBuzz =  s_tAlarmData.bBuzze;
        }
        SetCtrlOut( &tCtrl );       
        SetCtrlOutRelay();
        //若重要告警变化，保存历史数据
        EmergencyAlarmChangeSaveHisData();
    }
}

static BOOLEAN GetMainAlarmChanged(){
    WORD i;
	for( i=0; i<sizeof( s_tBcmAlarm ); i++ )
    {
		if(s_bMainAlarmChanged[i]	== True){
			return True;
		}
	}
	return False;
}

static BOOLEAN GetMainAlarmExist(){
    WORD i;
	for( i=0; i<sizeof( s_tBcmAlarm ); i++ )
    {
		if(s_bMainAlarmExist[i] == True){
			return True;
		}
	}
	return False;
}

static void EmergencyAlarmChangeSaveHisData(void){
    WORD i;
    for( i=0; i<sizeof( s_tBcmAlarm ); i++ )
    {
		if(s_bMainAlarmChanged[i] == True){
			if(s_bMainAlarmExist[i] == True){
				SaveTempHisData();
				SaveHisData();
                return;
			}else{
				SaveHisData();
                return;
			}
		}
	}
}

static BYTE checkTempAlmGroup(BYTE *p, T_BCMAlarmStruct *pBase)
{
    if (((p >= &pBase->aucChgTempHighAlm[0]) && (p <= &pBase->aucChgTempHighAlm[15])) ||
        ((p >= &pBase->aucChgTempLowAlm[0]) && (p <= &pBase->aucChgTempLowAlm[15])) ||
        ((p >= &pBase->aucDischgTempHighAlm[0]) && (p <= &pBase->aucDischgTempHighAlm[15])) ||
        ((p >= &pBase->aucDischgTempLowAlm[0]) && (p <= &pBase->aucDischgTempLowAlm[15])) ||
        ((p >= &pBase->ucCellTempSensorInvalidAlm[0]) && (p <= &pBase->ucCellTempSensorInvalidAlm[15])))
    {
        return 2; // CELL_TEMP_NUM_MAX
    }
    return 0;
}

static BYTE checkTempPrtGroup(BYTE *p, T_BCMAlarmStruct *pBase)
{
    if (((p >= &pBase->aucChgTempHighPrt[0]) && (p <= &pBase->aucChgTempHighPrt[15])) ||
        ((p >= &pBase->aucChgTempLowPrt[0]) && (p <= &pBase->aucChgTempLowPrt[15])) ||
        ((p >= &pBase->aucDischgTempHighPrt[0]) && (p <= &pBase->aucDischgTempHighPrt[15])) ||
        ((p >= &pBase->aucDischgTempLowPrt[0]) && (p <= &pBase->aucDischgTempLowPrt[15])))
    {
        return 2; // CELL_TEMP_NUM_MAX
    }
    return 0;
}

static BYTE checkCellAlmGroup(BYTE *p, T_BCMAlarmStruct *pBase)
{
    if (((p >= &pBase->aucCellUnderVoltAlm[0]) && (p <= &pBase->aucCellUnderVoltAlm[15])) ||
        ((p >= &pBase->aucCellOverVoltAlm[0]) && (p <= &pBase->aucCellOverVoltAlm[15])))
    {
        return 1; // CELL_VOL_NUM_MAX
    }

    return 0;
}

static BYTE checkCellPrtGroup(BYTE *p, T_BCMAlarmStruct *pBase)
{
    if (((p >= &pBase->aucCellUnderVoltPrt[0]) && (p <= &pBase->aucCellUnderVoltPrt[15])) ||
        ((p >= &pBase->aucCellOverVoltPrt[0]) && (p <= &pBase->aucCellOverVoltPrt[15])) ||
        ((p >= &pBase->aucCellDamagePrt[0]) && (p <= &pBase->aucCellDamagePrt[15])) ||
        ((p >= &pBase->aucCellDynamicUnderVoltPrt[0]) && (p <= &pBase->aucCellDynamicUnderVoltPrt[15])))
    {
        return 1; // CELL_VOL_NUM_MAX
    }

    return 0;
}

static BYTE checkAlmGroup(BYTE *p, T_BCMAlarmStruct *pBase)
{
    BYTE ucRet = 0;
    if(checkTempAlmGroup(p, pBase))
    {
        ucRet = checkTempAlmGroup(p, pBase);
    }
    else if(checkTempPrtGroup(p, pBase))
    {
        ucRet = checkTempPrtGroup(p, pBase);
    }
    else if(checkCellAlmGroup(p, pBase))
    {
        ucRet = checkCellAlmGroup(p, pBase);
    }
    else if(checkCellPrtGroup(p, pBase))
    {
        ucRet = checkCellPrtGroup(p, pBase);
    }
    return ucRet;
}

/******************************************************************************
**函 数 SetAlarmJudgeCounter()
**输    入： 
**输    出：无
**调用模块：
**功能描述：设定告警判断次数
* 作    者：
**时    间：
**修改记录：
* 日    期      版  本      修改人      修改摘要      
******************************************************************************/

static void SetAlarmJudgeCounter(void)
{
    BYTE* p = (BYTE*)&s_tBcmAlarmCounterMax;
    size_t i = 0;

    for (i=0; i<sizeof(s_tBcmAlarmCounterMax); i++)
    {
        *p++ = ALARM_COUNTER;
    }

    for (i=0; i<s_tHardwarePara.ucCellVoltNum; i++)
    {
        s_tBcmAlarmCounterMax.aucCellOverVoltPrt[i] = 3;    //单体过压保护判断时间减少为1s
        s_tBcmAlarmCounterMax.aucCellDamagePrt[i] = 3;
    }
	s_tBcmAlarmCounterMax.ucBDUBattVoltLowPrt = 20;
	s_tBcmAlarmCounterMax.ucBattShortCut 		= SHORT_CUT_ALM_COUNTER;
    s_tBcmAlarmCounterMax.ucBDUBusVoltLowPrt 	= SHORT_CUT_ALM_COUNTER;
    s_tBcmAlarmCounterMax.ucDischgCurrHighPrt 	= SHORT_CUT_ALM_COUNTER;
    s_tBcmAlarmCounterMax.ucPowerCommFail       = 1;   // 电源通信断立即消除
    s_tBcmAlarmCounterMax.ucBDUCommFail         = BDUCOMMFAIL_ALM_COUNTER;   //BDU通信断为15s
}

Static void fillAlarmShield( void )
{
    BYTE i, j, ucRet=0;
    BYTE *p;

    //获取系统参数
    GetSysPara( &s_tSysPara );
    p = ( BYTE * )&s_tBcmAlarmShield;
    for( i=0; i<ALARM_CLASS; i++ )
    {
        ucRet = checkAlmGroup(p, &s_tBcmAlarmShield);
        if (1 == ucRet)
        {
            for( j=0; j<CELL_VOL_NUM_MAX; j++ )
            {
                *p = s_tSysPara.aucAlarmLevel[i];
                p = p+1;
            }
        }
        else if (2 == ucRet)
        {
            for( j=0; j<CELL_TEMP_NUM_MAX; j++ )
            {
                *p = s_tSysPara.aucAlarmLevel[i];
                p = p+1;
            }
        }
        else
        {
            *p = s_tSysPara.aucAlarmLevel[i];
            p = p+1;
        }
    }
    
    return;
}

static BOOLEAN IsCellVoltTooHigh(T_StateListenStruct* ptState, BYTE i)
{
#define CELL_VOLT_HIGH_THRESHOLD (4.2f)
    FLOAT fDelta = 0.0;
    
    if(i >= s_tHardwarePara.ucCellVoltNum || s_tAlarmData.fBattVolt < GetBattVoltThresh())
    {
        return False;
    }

    if(ptState->bState)
    {
        fDelta = -0.05f;
    }

    return s_tAlarmData.afCellVolt[i]>(CELL_VOLT_HIGH_THRESHOLD + fDelta);
}

static BOOLEAN IsCellVoltPoor(T_StateListenStruct* ptState, BYTE ucIndex)
{
    if(ucIndex >= s_tHardwarePara.ucCellVoltNum || s_tAlarmData.fBattVolt < GetBattVoltThresh())
    {
        return False;
    }

    return ((s_tAlarmData.fCellVoltMin <= 2.7f) && s_tAlarmData.fCellVoltMax >= 3.3f);
}

void CheckCellDamegeFlag(void)
{
    size_t i=0;
    size_t j=0;
    FLOAT fCellVolt[CELL_VOL_NUM_MAX] = {0.0,};
    FLOAT fMedianCellVolt = 0.0; // 单体电压中位数
    FLOAT fMaxCellVoltDiff = 0.0; // 最大中位数压差

    if (s_tHardwarePara.ucCellVoltNum <1 || s_tHardwarePara.ucCellVoltNum > 16)
    {
        return;
    }

    for(j = 0; j < s_tHardwarePara.ucCellVoltNum; j++)
    {
        fCellVolt[j] = s_tAlarmData.afCellVolt[j];
    }
    BubbleSort(fCellVolt, s_tHardwarePara.ucCellVoltNum);

    if(s_tHardwarePara.ucCellVoltNum % 2 == 0)
    {
        fMedianCellVolt = (fCellVolt[s_tHardwarePara.ucCellVoltNum/2 - 1] + fCellVolt[s_tHardwarePara.ucCellVoltNum/2])/2.0;
    }
    else
    {
        fMedianCellVolt = fCellVolt[(s_tHardwarePara.ucCellVoltNum - 1)/2];
    }
            
    fMaxCellVoltDiff = MAX(fabs(fCellVolt[s_tHardwarePara.ucCellVoltNum - 1] - fMedianCellVolt), fabs(fCellVolt[0] - fMedianCellVolt));

    for(i = 0; i < s_tHardwarePara.ucCellVoltNum; i++)
    {
        InitListener(&s_atCellVoltTooHigh[i], IsCellVoltTooHigh, 2);  //（2 + 3） 持续2s
        InitListener(&s_atCellVoltPoor[i], IsCellVoltPoor, 11);  //（11 + 3）持续5s

        if (RunListener(&s_atCellVoltTooHigh[i], i) && FAULT != s_tCellDamageSave.abCellDamagePrt[i])
        {
            s_tCellDamageSave.abCellDamagePrt[i] = FAULT;
        }

        if (RunListener(&s_atCellVoltPoor[i], i) && FAULT != s_tCellDamageSave.abCellDamagePrt[i])
        {
            if(fabs(fabs(s_tAlarmData.afCellVolt[i] - fMedianCellVolt) - fMaxCellVoltDiff) < 1e-6)
            {
                s_tCellDamageSave.abCellDamagePrt[i] = FAULT;
            }
        }
    }

    return;
}

static void GetAlarmInfo(void)
{
    size_t i=0;
    T_BCMDataStruct tBcmData = {0, };
    T_BattResult tBattOut = {0, };

    //------------para-------------------
    GetRealData(&tBcmData);
    GetSysPara(&s_tSysPara);
    GetBattResult(&tBattOut);

    for(i = 0; i < s_tHardwarePara.ucCellTempNum; i++)
    {
        s_tAlarmData.afCellTemp[i] = tBcmData.afCellTemp[i];
    }

    for(i = 0; i < s_tHardwarePara.ucCellVoltNum; i++)
    {
        s_tAlarmData.afCellVolt[i] = tBcmData.afCellVolt[i];
    }
    s_tAlarmData.fBattVolt = tBcmData.fBattVolt;
    s_tAlarmData.fCellVoltMin = tBcmData.fCellVoltMin;
    s_tAlarmData.fCellVoltMax = tBcmData.fCellVoltMax;
    s_tAlarmData.fBattCurr = tBcmData.fBattCurr;
    if (s_tSysPara.wBatteryCap)
    {
        s_tAlarmData.fCurrCoef = fabs(tBcmData.fBattCurr)/((FLOAT)s_tSysPara.wBatteryCap*g_ProConfig.fCapTransRate);
    }
    else
    {
        s_tAlarmData.fCurrCoef = fabs(tBcmData.fBattCurr)/100.0;
    }
    
    SetAlarmJudgeCounter();

    //------------para-------------------
    GetSysPara(&s_tSysPara);
    s_tAlarmPara.bBuzzEnable = s_tSysPara.bBuzzerEnable;
    s_tAlarmData.fCurrMinDet = MIN_CURR_DET_BDCU;
    s_tAlarmData.bLoopOff = tBattOut.bLoopOff;
    s_tAlarmData.ucBDUBattLockAlm = s_tBcmAlarmTmp.ucBDUBattLockAlm;
    s_tAlarmData.ucBDUConnTempHighPrt = s_tBcmAlarmTmp.ucBDUConnTempHighPrt;
    s_tAlarmData.ucHeaterFilmFailure = s_tBcmAlarmTmp.ucHeaterFilmFailure;
    s_tAlarmData.bDcrFaultAlm = tBattOut.bBattDcrFaultAlmSta;
    s_tAlarmData.bDcrFaultPrt = tBattOut.bBattDcrFaultPrtSta;
    //s_tAlarmData.ucBDUBattLockAlm = (tBcmData.ucBduStatus == BDU_STATUS_FAILURE)? 1:0;
    CheckCellDamegeFlag();
    return;
}

static INT32S CellDamageHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus)
{
    BOOLEAN  bStatus = NORMAL;

    BYTE ucIndex = 0;
    FLOAT fDelta1 = 0.0f;

    if (NULL == pAlmInfo || NULL == pbTmpStatus)
    {
        return FAILURE;
    }
    ucIndex = pAlmInfo->ucReserve;
    if(NORMAL != *(pAlmInfo->pucRealAlarm))
    {
        fDelta1 = pAlmInfo->fValueAdd;

    }
    if(NORMAL != s_tCellDamageSave.abCellDamagePrt[ucIndex])
    {
        bStatus = FAULT;
    }

    if (s_tAlarmData.fBattVolt > GetBattVoltThresh() && s_tAlarmData.fCellVoltMax > 1.5f && pAlmInfo->fValue < (fDelta1 + pAlmInfo->fThread))//电池电压大于12V，单体最高电压大于1.5V，并且任意单节低于单体损坏阈值
    {
        bStatus = FAULT;
    }

    *pbTmpStatus = bStatus;
    return RTN_ALM_STATUE;
}


/******************************************************************************
* 函数名称：JudgeBcmAlarm
* 调    用：
* 被 调 用：
* 输入参数：无
* 返 回 值：有告警变化返回TRUE，否则返回FALSE
* 功能描述：根据采样结果判断整流模块告警
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要           
******************************************************************************/ 
static BOOLEAN  JudgeBcmAlarm( void )
{
    WORD    i;
    BYTE    *pAlarm;
    BYTE    *pAlarmReal;
    BYTE    *pAlarmBak;
    BYTE    *pAlarmFlag;
    BYTE    *pAlarmCounter;
    BYTE    *pAlarmShield;
    BYTE    *pAlarmCounterMax;
    WORD    wOffset;

    BOOLEAN bReturnValue = False;

    GetAlarmInfo();
    JudgeBcmAnalogAlarm();
    JudgeBcmDigitalAlarm();

    pAlarm              = ( BYTE * )&s_tBcmAlarm;
    pAlarmReal          = ( BYTE * )&s_tBcmAlarmReal;
    pAlarmBak           = ( BYTE * )&s_tBcmAlarmBak;
    pAlarmFlag          = ( BYTE * )&s_tBcmAlarmFlag;
    pAlarmCounter       = ( BYTE * )&s_tBcmAlarmCounter;
    pAlarmShield        = ( BYTE * )&s_tBcmAlarmShield;
    pAlarmCounterMax    = ( BYTE * )&s_tBcmAlarmCounterMax;

    fillAlarmShield();
    wOffset = COMMON_ALARM_NUM;

    //连续ALARM_COUNTER次判断结果一样且该项告警未屏蔽，置实时告警
    for( i=0; i<sizeof( s_tBcmAlarm ); i++ )
    {
        if ( pAlarmCounter[i] >= pAlarmCounterMax[i] )
        {
            pAlarmReal[i] = pAlarmFlag[i];
        }
        pAlarm[i] = ( pAlarmShield[i] == IGNORE )? NORMAL : pAlarmReal[i];
        if ( pAlarm[i] && (EMERGENCY==pAlarmShield[i]) )
        {
            s_bMainAlarmExist[i] = True;
        }
    }

    JudgeBcmAlarmPriority();

    for( i=0; i<sizeof( s_tBcmAlarm ); i++ )
    {
    //实时告警有变化
        if( pAlarm[i]!=pAlarmBak[i] )
        {
            // 有重要告警变化
            if (EMERGENCY==pAlarmShield[i])
            {
                s_bMainAlarmChanged[i] = True;// 重要告警变化标志，保留，20110406，yang.an
            }
            //g_bAutoSendAlarmFlag = True;
            //处理告警显示
            DealDisAlarm(i+wOffset, pAlarm[i]);  //处理实时、历史告警显示
            bReturnValue = True; //置函数返回值为TRUE
        }
    }

    if( True==bReturnValue )
    {
        rt_memcpy( pAlarmBak, pAlarm, sizeof(s_tBcmAlarm) );
        SaveRealAlarm(&s_tRealAlmSave);    //保存实时告警
        //置有未读的告警量变化标志
        SetDataFlag( BCM_ALARM, 0x01 );
    }

    s_bExistAlarmFlag = JudgeIfExixtAlarm(pAlarm,sizeof(s_tBcmAlarm));
    return bReturnValue;
}

static void JudgeCellTempAlarm( void )
{
    BYTE i;
    T_BCMDataStruct tBcmData = {0, };
    T_AlmInfoStruct tAlmInfo = {0, };
    T_BattResult tBattOut = {0, };

    GetBattResult(&tBattOut);
    GetRealData(&tBcmData);
    GetSysPara(&s_tSysPara);

    if (s_tHardwarePara.ucCellTempNum <1 || s_tHardwarePara.ucCellTempNum > CELL_TEMP_NUM_MAX)
    {//解决kw故障
        return;
    }

    for (i = 0; i < s_tHardwarePara.ucCellTempNum; i++)   //4路单体温度
    {
        //  充电高温告警
        ANALOG_ALARM_INIT(tAlmInfo, aucChgTempHighAlm[i]);
        tAlmInfo.fValue = tBcmData.afCellTemp[i];
        tAlmInfo.fValueAdd = s_tSysPara.fChgTempHighAlmRecoThre - s_tSysPara.fChgTempHighAlmThre;//-3.0;
        tAlmInfo.fThread = s_tSysPara.fChgTempHighAlmThre;
        tAlmInfo.ucAlarmType = JudgeAlarmInfoType(BATT_MODE_CHARGE == tBattOut.ucBatStatus); //仅在充电时产生告警
        JudgeAnalogStatus(&tAlmInfo);

        //  充电高温保护
        ANALOG_ALARM_INIT(tAlmInfo, aucChgTempHighPrt[i]);
        tAlmInfo.fValue = tBcmData.afCellTemp[i];
        tAlmInfo.fValueAdd = s_tSysPara.fChgTempHighPrtRecoThre - s_tSysPara.fChgTempHighPrtThre;//-3.0;
        tAlmInfo.fThread = s_tSysPara.fChgTempHighPrtThre;
        // Added by fengfj, 2020-09-28 22:40:41
        tAlmInfo.ucAlarmType = JudgeAlarmInfoType(BATT_MODE_CHARGE == tBattOut.ucBatStatus || BATT_MODE_OFFLINE == tBattOut.ucBatStatus);
        JudgeAnalogStatus(&tAlmInfo);

        //  充电低温告警
        ANALOG_ALARM_INIT(tAlmInfo, aucChgTempLowAlm[i]);
        tAlmInfo.fValue = tBcmData.afCellTemp[i];
        tAlmInfo.fValueAdd = s_tSysPara.fChgTempLowAlmRecoThre - s_tSysPara.fChgTempLowAlmThre;//3.0;
        tAlmInfo.fThread = s_tSysPara.fChgTempLowAlmThre;
        tAlmInfo.ucAlarmType = JudgeAlarmInfoType(BATT_MODE_CHARGE == tBattOut.ucBatStatus);  //仅在充电时产生告警
        JudgeAnalogStatus(&tAlmInfo);

        //	充电低温保护
        ANALOG_ALARM_INIT(tAlmInfo, aucChgTempLowPrt[i]);
        tAlmInfo.fValue = tBcmData.afCellTemp[i];
        tAlmInfo.fValueAdd = s_tSysPara.fChgTempLowPrtRecoThre - s_tSysPara.fChgTempLowPrtThre;//3.0;
        tAlmInfo.fThread = s_tSysPara.fChgTempLowPrtThre;
        // Added by fengfj, 2020-09-28 22:41:06
        tAlmInfo.ucAlarmType = JudgeAlarmInfoType(BATT_MODE_CHARGE == tBattOut.ucBatStatus || BATT_MODE_OFFLINE == tBattOut.ucBatStatus);
        JudgeAnalogStatus(&tAlmInfo);

        //  放电高温告警
        ANALOG_ALARM_INIT(tAlmInfo, aucDischgTempHighAlm[i]);
        tAlmInfo.fValue = tBcmData.afCellTemp[i];
        tAlmInfo.fValueAdd = s_tSysPara.fDischgTempHighAlmRecoThre - s_tSysPara.fDischgTempHighAlmThre;//-3.0;
        tAlmInfo.fThread = s_tSysPara.fDischgTempHighAlmThre;
        tAlmInfo.ucAlarmType = JudgeAlarmInfoType(BATT_MODE_DISCHARGE == tBattOut.ucBatStatus);  //仅在放电时产生告警
        JudgeAnalogStatus(&tAlmInfo);

        //  放电高温保护
        ANALOG_ALARM_INIT(tAlmInfo, aucDischgTempHighPrt[i]);
        tAlmInfo.fValue = tBcmData.afCellTemp[i];
        tAlmInfo.fValueAdd = s_tSysPara.fDischgTempHighPrtRecoThre - s_tSysPara.fDischgTempHighPrtThre;//-3.0;
        tAlmInfo.fThread = s_tSysPara.fDischgTempHighPrtThre;
        // Added by fengfj, 2020-09-28 22:41:45
        tAlmInfo.ucAlarmType = JudgeAlarmInfoType(BATT_MODE_DISCHARGE == tBattOut.ucBatStatus);
        JudgeAnalogStatus(&tAlmInfo);

        //  放电低温告警
        ANALOG_ALARM_INIT(tAlmInfo, aucDischgTempLowAlm[i]);
        tAlmInfo.fValue = tBcmData.afCellTemp[i];
        tAlmInfo.fValueAdd = s_tSysPara.fDischgTempLowAlmRecoThre - s_tSysPara.fDischgTempLowAlmThre;//3.0;
        tAlmInfo.fThread = s_tSysPara.fDischgTempLowAlmThre;
        tAlmInfo.ucAlarmType = JudgeAlarmInfoType(BATT_MODE_DISCHARGE == tBattOut.ucBatStatus);  //仅在放电时产生告警
        JudgeAnalogStatus(&tAlmInfo);

        //  放电低温保护
        ANALOG_ALARM_INIT(tAlmInfo, aucDischgTempLowPrt[i]);
        tAlmInfo.fValue = tBcmData.afCellTemp[i];
        tAlmInfo.fValueAdd = s_tSysPara.fDischgTempLowPrtRecoThre - s_tSysPara.fDischgTempLowPrtThre;//3.0;
        tAlmInfo.fThread = s_tSysPara.fDischgTempLowPrtThre;
    	// Added by fengfj, 2020-09-28 22:42:05
        tAlmInfo.ucAlarmType = JudgeAlarmInfoType(BATT_MODE_DISCHARGE == tBattOut.ucBatStatus);
        JudgeAnalogStatus(&tAlmInfo);
    }

    JudgeCellTempAbnormal(&tBcmData); // 单体温度异常和电池异常温度高

    return;
}

// 判断单体温度异常和电池异常温度高（降低圈复杂度）
static void JudgeCellTempAbnormal(T_BCMDataStruct *tBcmData)
{
    BYTE i = 0;
    T_AlmInfoStruct tAlmInfo = {0, };
    BYTE ucInvalidCellTempNum = 0; // 温度传感器失效的个数
    FLOAT fCellTempMinValid = CELLTEMP_MAX; // 温度传感器正常的最小电芯温度
    FLOAT fCellTempMaxValid = CELLTEMP_MIN; // 温度传感器正常的最大电芯温度

    for (i = 0; i < s_tHardwarePara.ucCellTempNum; i++)   //4路单体温度
    {
        // 找出温度正常的单体中最大和最小单体温度
        if (fCellTempMinValid > tBcmData->afCellTemp[i] && NORMAL == s_tBcmAlarm.ucCellTempSensorInvalidAlm[i])
        {
            fCellTempMinValid = tBcmData->afCellTemp[i];
        }

        if (fCellTempMaxValid < tBcmData->afCellTemp[i] && NORMAL == s_tBcmAlarm.ucCellTempSensorInvalidAlm[i])
        {
            fCellTempMaxValid = tBcmData->afCellTemp[i];
        }

        if(s_tBcmAlarm.ucCellTempSensorInvalidAlm[i] == FAULT)
        {
            ucInvalidCellTempNum++;
        }
    }

    // 至少两个单体温度正常才进行单体温度异常判断
    if(ucInvalidCellTempNum < 3)
    {
        // 电芯温度异常告警
        ANALOG_ALARM_INIT(tAlmInfo, ucCellTempAbnormal);
        tAlmInfo.fValue = fCellTempMaxValid - fCellTempMinValid;
        tAlmInfo.fValueAdd = -2.0;
        tAlmInfo.fThread = 10.0;
        JudgeAnalogStatus(&tAlmInfo);
    }
    else
    {
        ////避免无法恢复
        CLEAR_ALAEM(ucCellTempAbnormal);
    }

    //电池异常温度高保护告警
    if (!GetQtptestFlag())
    {
        ANALOG_ALARM_INIT(tAlmInfo, ucBattFaultTempHighAlm);
        tAlmInfo.fValue = fCellTempMaxValid;
        tAlmInfo.fValueAdd = -3.0;
        tAlmInfo.fThread = s_tSysPara.fBattFaultTempHighPrtThre;
        JudgeAnalogStatus(&tAlmInfo);
    }

    return;
}
/******************************************************************************
* 函数名称：JudgeBcmAnalogAlarm()
* 调    用：
* 被 调 用：
* 输入参数：无
* 返 回 值：无
* 功能描述：根据模拟量采样结果判断是否有告警产生或消除     
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要        
******************************************************************************/ 
static void JudgeBcmAnalogAlarm( void )
{
    BYTE i;
    FLOAT fCellVolMax, fCellVolMin, fCellTempMin, fCellAvgTemp = 0.0, fBattSOCLowPrtRecoAdd = 1.0, fCellVoltLowPrt = 0.0;
    T_BCMDataStruct tBcmData;
    T_AlmInfoStruct tAlmInfo;

    GetRealData(&tBcmData );
    GetSysPara( &s_tSysPara );
    if(GetQtptestFlag())
    {
        s_tSysPara.fCellUnderVoltPrtThre = 2.50f; //外协模式按照2.5来保护
        s_tSysPara.ucCellUVPDelay = 0; //外协模式电芯动态欠压保护不产生
        s_tSysPara.fBattUnderVoltPrtThre = OUT_ASSIST_VOLT_MIN/15.0*s_tHardwarePara.ucCellVoltNum*g_ProConfig.fVoltTrasRate; // 外协模式时,欠压保护40v(15节),以便放电截至电压有足够的调节范围 1218 mwl
        s_tSysPara.wBattSOCLowPrtThre = 0;  // 屏蔽SOC低保护，新SOC低保护不会产生
        fBattSOCLowPrtRecoAdd = 0.0; // 消除之前产生的SOC低保护，SOC低保护回差设置为0
    }

    fCellVolMax = tBcmData.fCellVoltMax;
    fCellVolMin = tBcmData.fCellVoltMin;
    fCellTempMin = tBcmData.fCellTempMin;

    //  电池组过压告警
    ANALOG_ALARM_INIT(tAlmInfo, ucBattOverVoltAlm);
    tAlmInfo.fValue = tBcmData.fBattVolt;
    tAlmInfo.fValueAdd = s_tSysPara.fBattOverVoltAlmRecoThre/g_ProConfig.fVoltTrasRate - s_tSysPara.fBattOverVoltAlmThre/g_ProConfig.fVoltTrasRate; //54-3.75;
    tAlmInfo.fThread = s_tSysPara.fBattOverVoltAlmThre/g_ProConfig.fVoltTrasRate;
    JudgeAnalogStatus(&tAlmInfo);
    
    //  放电过流告警
    ANALOG_ALARM_INIT(tAlmInfo, ucDischgCurrHighAlm);
    tAlmInfo.fValue = tBcmData.fBusCurr;
    tAlmInfo.fValueAdd = 2.0;
    tAlmInfo.fThread = 0.0 - s_tSysPara.fDischgCurrHighAlmThre*s_tSysPara.wBatteryCap;
    JudgeAnalogStatus(&tAlmInfo);

    //  充电过流告警
    ANALOG_ALARM_INIT(tAlmInfo, ucChgCurrHighAlm);
    tAlmInfo.fValue = tBcmData.fBusCurr;
    tAlmInfo.fValueAdd = -2.0;
    tAlmInfo.fThread = s_tSysPara.fChgCurrHighAlmThre*s_tSysPara.wBatteryCap;
    JudgeAnalogStatus(&tAlmInfo);

    //  单板过温告警     D121/R321 研制规范无此告警
    // ANALOG_ALARM_INIT(tAlmInfo, ucBoardTempHighAlm);
    // tAlmInfo.fValue = tBcmData.fBoardTemp;
    // tAlmInfo.fValueAdd = s_tSysPara.fBoardTempHighAlmRecoThre - s_tSysPara.fBoardTempHighcThre;//-17.0;
    // tAlmInfo.fThread = s_tSysPara.fBoardTempHighcThre;
    // JudgeAnalogStatus(&tAlmInfo);

    //  单板过温保护
#ifdef BOARD_Temp_High_Prt_BDPB
    ANALOG_ALARM_INIT(tAlmInfo, ucBoardTempHighPrt);
    tAlmInfo.fValue = tBcmData.fBoardTemp;
    tAlmInfo.fValueAdd = s_tSysPara.fBoardTempHighPrtRecoThre - s_tSysPara.fBoardTempHighPrtThre;//-17.0;
    tAlmInfo.fThread = s_tSysPara.fBoardTempHighPrtThre;
    tAlmInfo.pExceptionHandling = BoardTempHighPrtHandle;
    JudgeAnalogStatus(&tAlmInfo);
#endif

    //  环境温度高告警
    ANALOG_ALARM_INIT(tAlmInfo, ucEnvTempHighAlm);
    tAlmInfo.fValue = tBcmData.fEnvTemp;
    tAlmInfo.fValueAdd = s_tSysPara.fEnvTempHighAlmRecoThre - s_tSysPara.fEnvTempHighAlmThre;//-5.0;
    tAlmInfo.fThread = s_tSysPara.fEnvTempHighAlmThre;
    JudgeAnalogStatus(&tAlmInfo);

    //  环境温度低告警
    ANALOG_ALARM_INIT(tAlmInfo, ucEnvTempLowAlm);
    tAlmInfo.fValue = tBcmData.fEnvTemp;
    tAlmInfo.fValueAdd = s_tSysPara.fEnvTempLowAlmRecoThre - s_tSysPara.fEnvTempLowAlmThre;//5.0;
    tAlmInfo.fThread = s_tSysPara.fEnvTempLowAlmThre;
    JudgeAnalogStatus(&tAlmInfo);

    //  环境温度高保护
    ANALOG_ALARM_INIT(tAlmInfo, ucEnvTempHighPrt);
    tAlmInfo.fValue = tBcmData.fEnvTemp;
    tAlmInfo.fValueAdd = s_tSysPara.fEnvTempHighPrtRecoThre - s_tSysPara.fEnvTempHighPrtThre;//-5.0;
    tAlmInfo.fThread = s_tSysPara.fEnvTempHighPrtThre;
    JudgeAnalogStatus(&tAlmInfo);

    //  环境温度低保护
    ANALOG_ALARM_INIT(tAlmInfo, ucEnvTempLowPrt);
    tAlmInfo.fValue = tBcmData.fEnvTemp;
    tAlmInfo.fValueAdd = s_tSysPara.fEnvTempLowPrtRecoThre - s_tSysPara.fEnvTempLowPrtThre;//5.0;
    tAlmInfo.fThread = s_tSysPara.fEnvTempLowPrtThre;
    JudgeAnalogStatus(&tAlmInfo);
    //  电池组欠压保护
    if(!IsSleep())//休眠条件下不判断电池组欠压保护
    {
        ANALOG_ALARM_INIT(tAlmInfo, ucBattUnderVoltPrt);
        tAlmInfo.fValue = tBcmData.fBattVolt;

        if (s_tSysPara.bUVPTempCompensationEn)
        {
            for(i=0; i<s_tHardwarePara.ucCellTempNum; i++)
            {
                fCellAvgTemp += tBcmData.afCellTemp[i];
            }
            fCellAvgTemp = fCellAvgTemp / s_tHardwarePara.ucCellTempNum;

            if(fCellAvgTemp < 0)
            {
                tAlmInfo.fThread = MAX(s_tSysPara.fBattUnderVoltPrtThre/g_ProConfig.fVoltTrasRate - 3/15.0*s_tHardwarePara.ucCellVoltNum, 40.0f/15.0*s_tHardwarePara.ucCellVoltNum); //温度小于0则按照欠压保护阈值-3处理
            }
            else
            {
                tAlmInfo.fThread = s_tSysPara.fBattUnderVoltPrtThre/g_ProConfig.fVoltTrasRate;
            }
        }
        else
        {
            tAlmInfo.fThread = s_tSysPara.fBattUnderVoltPrtThre/g_ProConfig.fVoltTrasRate;
        }
        tAlmInfo.fValueAdd = s_tHardwarePara.ucCellVoltNum * 3.4- tAlmInfo.fThread;  //MIN(51.0 - s_tSysPara.fBattUnderVoltPrtThre, 8.0);
        tAlmInfo.pExceptionHandling = BattVoltLowPrtHandle;
        JudgeAnalogStatus(&tAlmInfo);
    }

    //  电池组欠压告警
    ANALOG_ALARM_INIT(tAlmInfo, ucBattUnderVoltAlm);
    tAlmInfo.fValue = tBcmData.fBattVolt;
    tAlmInfo.fValueAdd = MIN( 4.5/15.0*s_tHardwarePara.ucCellVoltNum, s_tHardwarePara.ucCellVoltNum * 3.4 - s_tSysPara.fBattUnderVoltAlmThre/g_ProConfig.fVoltTrasRate );
    tAlmInfo.fThread = s_tSysPara.fBattUnderVoltAlmThre/g_ProConfig.fVoltTrasRate;
    tAlmInfo.ucAlarmType = JudgeAlarmInfoType(GetQtptestFlag() == False); //仅在非QTP模式下产生告警
    JudgeAnalogStatus(&tAlmInfo);

    //  电池SOC低告警
    ANALOG_ALARM_INIT(tAlmInfo, ucBattSOCLowAlm);
    tAlmInfo.fValue = tBcmData.wBattSOC;
    tAlmInfo.fValueAdd = s_tSysPara.wBattSOCLowAlmRecoThre - s_tSysPara.wBattSOCLowAlmThre;
    tAlmInfo.fThread = s_tSysPara.wBattSOCLowAlmThre;
    tAlmInfo.ucAlarmType = JudgeAlarmInfoType(GetQtptestFlag() == False); //仅在非QTP模式下产生告警
    JudgeAnalogStatus(&tAlmInfo);

    //  电池SOH告警
    ANALOG_ALARM_INIT(tAlmInfo, ucBattSOHAlm);
    tAlmInfo.fValue = tBcmData.wBattSOH;
    tAlmInfo.fValueAdd = 10.0;
    tAlmInfo.fThread = s_tSysPara.wBattSOHAlmThre;
    JudgeAnalogStatus(&tAlmInfo);

    //  电池SOC低保护
    ANALOG_ALARM_INIT(tAlmInfo, ucBattSOCLowPrt);
    tAlmInfo.fValue = tBcmData.wBattSOC;
    tAlmInfo.fValueAdd = fBattSOCLowPrtRecoAdd;
    tAlmInfo.fThread = s_tSysPara.wBattSOCLowPrtThre;
    JudgeAnalogStatus(&tAlmInfo);

    //  电池SOH保护
    ANALOG_ALARM_INIT(tAlmInfo, ucBattSOHPrt);
    tAlmInfo.fValue = tBcmData.wBattSOH;
    tAlmInfo.fValueAdd = 10.0;
    tAlmInfo.fThread = s_tSysPara.wBattSOHPrtThre;
    JudgeAnalogStatus(&tAlmInfo);

    //  停电时间保护告警
    ANALOG_ALARM_INIT(tAlmInfo, ucPowerOffTimePrtAlm);
    tAlmInfo.fValue = tBcmData.wBattPowerOffTime;
    tAlmInfo.fValueAdd = 0;
    tAlmInfo.fThread = s_tSysPara.ulPoweroffTimePrtThre;
    tAlmInfo.pExceptionHandling = BattPowerOffPrtHandle;
    JudgeAnalogStatus(&tAlmInfo);

    for (i = 0; i < s_tHardwarePara.ucCellVoltNum; i++)
    {
        //  电芯过压告警
        ANALOG_ALARM_INIT(tAlmInfo, aucCellOverVoltAlm[i]);
        tAlmInfo.fValue = tBcmData.afCellVolt[i];
        tAlmInfo.fValueAdd = s_tSysPara.fCellOverVoltAlmRecoThre - s_tSysPara.fCellOverVoltAlmThre;//-0.15;
        tAlmInfo.fThread = s_tSysPara.fCellOverVoltAlmThre;
        JudgeAnalogStatus(&tAlmInfo);

        //  电芯过压保护
        ANALOG_ALARM_INIT(tAlmInfo, aucCellOverVoltPrt[i]);
        tAlmInfo.fValue = tBcmData.afCellVolt[i];
        tAlmInfo.fValueAdd = s_tSysPara.fCellOverVoltPrtRecoThre - s_tSysPara.fCellOverVoltPrtThre;//-0.3;
        tAlmInfo.fThread = s_tSysPara.fCellOverVoltPrtThre;
        JudgeAnalogStatus(&tAlmInfo);

        //  电芯欠压告警
        ANALOG_ALARM_INIT(tAlmInfo, aucCellUnderVoltAlm[i]);
        tAlmInfo.fValue = tBcmData.afCellVolt[i];
        tAlmInfo.fValueAdd = s_tSysPara.fCellUnderVoltAlmRecoThre - s_tSysPara.fCellUnderVoltAlmThre;  //0.2;
        tAlmInfo.fThread = s_tSysPara.fCellUnderVoltAlmThre;
        tAlmInfo.ucAlarmType = JudgeAlarmInfoType(GetQtptestFlag() == False); //仅在非QTP模式下产生告警
        JudgeAnalogStatus(&tAlmInfo);

        //  电芯欠压保护
        ANALOG_ALARM_INIT(tAlmInfo, aucCellUnderVoltPrt[i]);
        tAlmInfo.fValue = tBcmData.afCellVolt[i];
        tAlmInfo.fValueAdd = s_tSysPara.fCellUnderVoltPrtRecoThre-s_tSysPara.fCellUnderVoltPrtThre;//3.4 - s_tSysPara.fCellUnderVoltPrtThre;////51V恢复
        tAlmInfo.fThread = s_tSysPara.fCellUnderVoltPrtThre;
        tAlmInfo.pExceptionHandling  = CellLowPrtHandle;
        JudgeAnalogStatus(&tAlmInfo);

        //  电芯动态欠压保护
        fCellVoltLowPrt = CalCellDynamicLowPrtThre(i , fCellTempMin);
        ANALOG_ALARM_INIT(tAlmInfo, aucCellDynamicUnderVoltPrt[i]);
        tAlmInfo.fValue = tBcmData.afCellVolt[i];
        tAlmInfo.fValueAdd = s_tSysPara.fCellUnderVoltPrtRecoThre-fCellVoltLowPrt;
        tAlmInfo.fThread = fCellVoltLowPrt;
        tAlmInfo.ucReserve = i;
        tAlmInfo.pExceptionHandling  = CellDynamicLowPrtHandle;
        JudgeAnalogStatus(&tAlmInfo);

         //  电芯损坏保护
        ANALOG_ALARM_INIT(tAlmInfo, aucCellDamagePrt[i]);
        tAlmInfo.fValue = tBcmData.afCellVolt[i];
        tAlmInfo.fValueAdd = 3.4-s_tSysPara.fCellDamagePrtThre;
        tAlmInfo.fThread = s_tSysPara.fCellDamagePrtThre;
        tAlmInfo.ucReserve = i;
        tAlmInfo.pExceptionHandling  = CellDamageHandle;
        JudgeAnalogStatus(&tAlmInfo);
    } 
    //  电芯一致性差告警
    ANALOG_ALARM_INIT(tAlmInfo, ucCellPoorConsisAlm);
    tAlmInfo.fValue = fCellVolMax - fCellVolMin;
    tAlmInfo.fValueAdd = -0.1;
    tAlmInfo.fThread = s_tSysPara.fCellPoorConsisAlmThre;
    tAlmInfo.ucReserve = (tBcmData.fBattVolt>s_tHardwarePara.ucCellVoltNum*3.1f && tBcmData.fBattVolt<s_tHardwarePara.ucCellVoltNum*3.4f);
    tAlmInfo.pExceptionHandling  = CellPoorConsisHandle;
    JudgeAnalogStatus(&tAlmInfo);
    
    //  电芯一致性差保护
    ANALOG_ALARM_INIT(tAlmInfo, ucCellPoorConsisPrt);
    tAlmInfo.fValue = fCellVolMax - fCellVolMin;
    tAlmInfo.fValueAdd = -0.2;
    tAlmInfo.fThread = s_tSysPara.fCellPoorConsisPrtThre;
    tAlmInfo.ucReserve = IsCellPoorConsisStart(tBcmData.fBattVolt, tBcmData.fBattCurr);
    tAlmInfo.pExceptionHandling  = CellPoorConsisHandle;
    JudgeAnalogStatus(&tAlmInfo);

#ifdef USING_HEAT_CONNECT
    //加热膜连接器高温保护
    ANALOG_ALARM_INIT(tAlmInfo, ucHeatConnTempHighPrt);
    tAlmInfo.fValue = tBcmData.fHeatConnTempMax;
    tAlmInfo.fValueAdd = -20.0;
    tAlmInfo.fThread = 100.0;
    JudgeAnalogStatus(&tAlmInfo);
#endif

    //  电压采样故障 20200820
    /*ANALOG_ALARM_INIT(tAlmInfo, ucBattVoltSampleAlm);    屏蔽电压采样故障告警
    for(i = 0; i < CELL_VOL_NUM_MAX; i++)
    {
        fCellVoltTotal += tBcmData.afCellVolt[i];
    }
    tAlmInfo.fValue = fabs(tBcmData.fBattVolt - fCellVoltTotal); //电池组电压 与 电芯总电压之和 差值
    tAlmInfo.fValueAdd = -0.2;
    tAlmInfo.fThread = BATT_VOLT_SAMPLE_DEFAULT_DTECT;
    JudgeAnalogStatus(&tAlmInfo);*/

    JudgeCellTempAlarm();

    return;
}

static FLOAT CalCellDynamicLowPrtThre(BYTE Num , FLOAT MinCellTemp)
{
        BYTE     ucCellSN = 0, ucTempSN=0;
        FLOAT fCellVoltLowPrt = 0;
        ucCellSN = Num %(s_tHardwarePara.ucCellVoltNum+1);
        ucTempSN = (ucCellSN/s_tHardwarePara.ucCellTempNum)%s_tHardwarePara.ucCellTempNum;	//电芯温度和电芯数量不是一一对应 Added by fengfj, 2019-07-13 16:50:56
        fCellVoltLowPrt = DoubleLinearInsertValue(s_tAlarmData.afCellTemp[ucTempSN], s_tAlarmData.fCurrCoef);
        fCellVoltLowPrt = CalCellVoltLowPrtThre(MinCellTemp, fCellVoltLowPrt);
        return fCellVoltLowPrt;
}
static FLOAT CalCellVoltLowPrtThre(FLOAT fCellTempMin, FLOAT fCellVoltLowPrt)
{
    if(GetChargeNotFullOneWeek())//7天未充满过
    {
        if(fCellTempMin >= -20 && fCellTempMin < -10 && fCellVoltLowPrt <= 2.7)
        {
            fCellVoltLowPrt = 2.7;
        }
        else if(fCellTempMin >= -10 && fCellTempMin < 0 && fCellVoltLowPrt <= 2.8)
        {
            fCellVoltLowPrt = 2.8;
        }
        else if(fCellTempMin >= 0 && fCellTempMin < 10 && fCellVoltLowPrt <= 2.9)
        {
            fCellVoltLowPrt = 2.9;
        }
        else if(fCellTempMin >= 10 && fCellVoltLowPrt <= 3.0)
        {
            fCellVoltLowPrt = 3.0;
        }
    }
    return fCellVoltLowPrt;
}

static INT32S BattVoltLowPrtHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus)
{
    BOOLEAN  bStatus;
    FLOAT    fDelta = 0.0;
    
    if (NULL == pAlmInfo || NULL == pbTmpStatus)
    {
        return FAILURE;
    }

    if(NORMAL != *(pAlmInfo->pucRealAlarm))
    {
        fDelta = pAlmInfo->fValueAdd;
    }

    if (s_tAlarmData.fBattCurr > s_tAlarmData.fCurrMinDet + UNDERVOLL_CHARGE_MIN_CURRENT ) ///充电则告警恢复
    {
        bStatus = NORMAL;
    }
    else if (pAlmInfo->fValue < (fDelta + pAlmInfo->fThread))
    {
        bStatus = FAULT;
    }
    else
    {
        bStatus = NORMAL;
    }
    *pbTmpStatus = bStatus;

    return RTN_ALM_STATUE;
}

static INT32S BattPowerOffPrtHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus)
{
    BOOLEAN  bStatus = 0;
    
    if (NULL == pAlmInfo || NULL == pbTmpStatus)
    {
        return FAILURE;
    }
    
    if (pAlmInfo->fValue >= (pAlmInfo->fThread))
    {
        if (!GetPowerOffStatus())
        {
            bStatus = NORMAL;
        }
        else
        {
            bStatus = FAULT;
        }
    }
 
    *pbTmpStatus = bStatus;

    return RTN_ALM_STATUE;
}


static BOOLEAN checkPointer(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus)
{
    if (NULL == pAlmInfo || NULL == pbTmpStatus)
    {
        return FAILURE;
    }

    return SUCCESSFUL;
}

static INT32S CellLowPrtHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus)
{
    BOOLEAN  bStatus = NORMAL;
    FLOAT    fDelta1 = 0.0;

    if (FAILURE == checkPointer(pAlmInfo, pbTmpStatus))
    {
        return FAILURE;
    }
    
    if(NORMAL != *(pAlmInfo->pucRealAlarm))
    {
        fDelta1 = pAlmInfo->fValueAdd;
    }

    if (s_tAlarmData.fBattCurr < s_tAlarmData.fCurrMinDet + UNDERVOLL_CHARGE_MIN_CURRENT) ///充电则告警恢复
    {
        if (pAlmInfo->fValue < MIN(fDelta1 + pAlmInfo->fThread, 3.4f))
        {
            bStatus = FAULT;
        }

    }
    *pbTmpStatus = bStatus;

    return RTN_ALM_STATUE;
}


static INT32S CellDynamicLowPrtHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus)
{
    BOOLEAN  bStatus;
    FLOAT    fDelta1 = 0.0;
    if (FAILURE == checkPointer(pAlmInfo, pbTmpStatus) || s_tHardwarePara.ucCellTempNum < 4)
    {
        return FAILURE;
    }
    if(NORMAL != *(pAlmInfo->pucRealAlarm))
    {
        fDelta1 = 0.1;
    }
    bStatus = NORMAL;
    if (s_tAlarmData.fBattCurr < s_tAlarmData.fCurrMinDet + UNDERVOLL_CHARGE_MIN_CURRENT) ///充电则告警恢复
    {
        if ( (0 < s_tSysPara.ucCellUVPDelay) &&  (pAlmInfo->fValue < MIN(pAlmInfo->fThread + fDelta1, 3.4f)))
        {
            bStatus = FAULT;
        }
    }
    *pbTmpStatus = bStatus;

    return RTN_ALM_STATUE;
}

static INT32S CurrHighPrtDelay(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus, WORD wRetryDelay, BYTE ucLastPrtDelayFlag)
{
//    static WORD s_wCnt = 0;

    if (NULL == pAlmInfo || NULL == pAlmInfo->pwRetryCounter || NULL == pAlmInfo->pwRetryCounterMax)
    {
        return SUCCESSFUL;
    }

    if ((pAlmInfo->ucState != NORMAL) || (ucLastPrtDelayFlag == CURR_HIGH_BDPB))
    {
        if (*(pAlmInfo->pwRetryCounter) >= wRetryDelay)    //重试失败下次重试延时60s
        {
            (*(pAlmInfo->pwRetryCounterMax)) += wRetryDelay;
        }
        *(pAlmInfo->pwRetryCounter) = 0;
        return SUCCESSFUL;
    }

    if ( *pAlmInfo->pucRealAlarm == NORMAL )
    {
        return SUCCESSFUL;
    }

    if ( *(pAlmInfo->pwRetryCounterMax) >= 4 * wRetryDelay  //重试3次不成功就不再重试
        || (*(pAlmInfo->pwRetryCounter))++ < wRetryDelay )
    {
        *pbTmpStatus = pAlmInfo->ucAlarmType;
        return RTN_ALM_STATUE;
    }

    return SUCCESSFUL;
}

static INT32S DisCurrH_Short_PrtHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus)
{

    if (NULL == pAlmInfo || NULL == pbTmpStatus)
    {
        return FAILURE;
    }

    if (s_tAlarmData.fBattCurr > s_tAlarmData.fCurrMinDet) ///充电时不做特殊处理
    {
        *(pAlmInfo->pwRetryCounterMax) = OVERCURR_RERTY_DELAY;
        return SUCCESSFUL;
    }

    return CurrHighPrtDelay( pAlmInfo, pbTmpStatus, OVERCURR_RERTY_DELAY, s_ucLastCurrHighFlag);
}

static INT32S Short_PrtHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus)
{

    if (NULL == pAlmInfo || NULL == pbTmpStatus)
    {
        return FAILURE;
    }

    if (s_tAlarmData.fBattCurr > s_tAlarmData.fCurrMinDet) ///充电时不做特殊处理
    {
        *(pAlmInfo->pwRetryCounterMax) = SHORT_RERTY_DELAY;
        return SUCCESSFUL;
    }

    return CurrHighPrtDelay( pAlmInfo, pbTmpStatus, SHORT_RERTY_DELAY, s_ucLastCurrHighFlag);
}

static INT32S ChgCurrHighPrtHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus)
{

    if (NULL == pAlmInfo || NULL == pbTmpStatus || NULL == pAlmInfo->pwRetryCounterMax)
    {
        return FAILURE;
    }

    if (s_tAlarmData.fBattCurr < -1*s_tAlarmData.fCurrMinDet) ///放电时不做特殊处理
    {
        *(pAlmInfo->pwRetryCounterMax) = OVERCURR_RERTY_DELAY;
        return SUCCESSFUL;
    }

    return CurrHighPrtDelay( pAlmInfo, pbTmpStatus, OVERCURR_RERTY_DELAY, s_ucLastCurrHighFlag);
}

static INT32S CurrLimLoopInvalidHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus)
{
    static BYTE s_ucCnt = 0;
    INT32S slRet;

    if (NULL == pAlmInfo || NULL == pbTmpStatus||NULL == pAlmInfo->pwRetryCounterMax || NULL == pAlmInfo->pwRetryCounter)
    {
        return SUCCESSFUL;
    }

    slRet = CurrHighPrtDelay(pAlmInfo, pbTmpStatus, OVERCURR_RERTY_DELAY, s_ucLastCurrHighFlag);

    if ( SUCCESSFUL == slRet )
    {
        if (s_ucCnt++ > 200)
        {
            s_ucCnt = 0;
            *(pAlmInfo->pwRetryCounterMax) = OVERCURR_RERTY_DELAY;
        }
    }
    else
    {
        s_ucCnt = 0;
    }

    return slRet;
}

static INT32S CellPoorConsisHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus)
{
    if (NULL == pAlmInfo || NULL == pbTmpStatus)
    {
        return FAILURE;
    }

    if (pAlmInfo->ucReserve) 
    {
        return SUCCESSFUL;
    }

    *pbTmpStatus = NORMAL;
    return RTN_ALM_STATUE;
}

/******************************************************************************
* 函数名称：JudgeBcmDigitalAlarm()
* 调    用：
* 被 调 用：
* 输入参数：无
* 返 回 值：无
* 功能描述：根据开关量采样结果判断是否有告警产生或消除     
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要        
******************************************************************************/ 
static void JudgeBcmDigitalAlarm( void )
{
    BYTE i;
    T_BCMDataStruct tBcmData;
    T_AlmInfoStruct tAlmInfo;
    T_DCRealData tDcRealData;
    rt_memset_s(&tDcRealData, sizeof(T_DCRealData), 0, sizeof(T_DCRealData));
    GetBduReal(&tDcRealData);
    GetRealData(&tBcmData);
    // 电芯温度无效
    for (i = 0; i < s_tHardwarePara.ucCellTempNum; i++)
    {
        DIGITAL_ALARM_INIT(tAlmInfo, ucCellTempSensorInvalidAlm[i]);
        tAlmInfo.ucState = tBcmData.abCellTempFault[i];
        JudgeDigitalAlarm(&tAlmInfo);
    }

    //  充电过流保护
    if(tDcRealData.tDCAlarm.bChgOverCur == FAULT || s_tBcmAlarmReal.ucChgCurrHighPrt)//功率侧上送的充电过流保护告警产生时，使用功率上送的进行数字量逻辑处理
    {
        s_ucLastCurrHighFlag = s_ucChgCurrHighFlag;
        DIGITAL_ALARM_INIT(tAlmInfo, ucChgCurrHighPrt);
        tAlmInfo.ucState = tDcRealData.tDCAlarm.bChgOverCur;
        tAlmInfo.pwRetryCounter     = &s_tAlarmData.wChgCurrHighPrtCnt;
        tAlmInfo.pwRetryCounterMax  = &s_tAlarmData.wChgCurrHighPrtCntMax;
        tAlmInfo.pExceptionHandling = ChgCurrHighPrtHandle;
        JudgeDigitalAlarm(&tAlmInfo);
        s_ucChgCurrHighFlag = CURR_HIGH_BDCU;
    }
    else   //功率侧上送的充电过流保护告警未产生时，监控根据阈值进行模拟量逻辑处理
    {
        ANALOG_ALARM_INIT(tAlmInfo, ucChgCurrHighPrt);
        tAlmInfo.fValue = tBcmData.fBusCurr;
        tAlmInfo.fValueAdd = -2.0; //研制规范中规定固定是2
        tAlmInfo.fThread = s_tSysPara.fChgCurrHighPrtThre*s_tSysPara.wBatteryCap;
        JudgeAnalogStatus(&tAlmInfo);
        s_ucChgCurrHighFlag = CURR_HIGH_BDPB;
    }

    //  放电过流保护
    if(tDcRealData.tDCAlarm.bDischOverCur == FAULT || s_tBcmAlarmReal.ucDischgCurrHighPrt) //功率侧上送的放电过流保护告警产生时，使用功率上送的进行数字量逻辑处理
    {
        s_ucLastCurrHighFlag = s_ucDisCurrHighFlag;
        DIGITAL_ALARM_INIT(tAlmInfo, ucDischgCurrHighPrt);
        tAlmInfo.ucState = tDcRealData.tDCAlarm.bDischOverCur;
        tAlmInfo.pwRetryCounter     = &s_tAlarmData.wDischgCurrHighPrtCnt;
        tAlmInfo.pwRetryCounterMax  = &s_tAlarmData.wDischgCurrHighPrtCntMax;
        tAlmInfo.pExceptionHandling = DisCurrH_Short_PrtHandle;
        JudgeDigitalAlarm(&tAlmInfo);
        s_ucDisCurrHighFlag = CURR_HIGH_BDCU;
    }
    else  //功率侧上送的放电过流保护告警未产生时，监控根据阈值进行模拟量逻辑处理
    {
        ANALOG_ALARM_INIT(tAlmInfo, ucDischgCurrHighPrt);
        tAlmInfo.fValue = tBcmData.fBusCurr;
        tAlmInfo.fValueAdd = 2.0;
        tAlmInfo.fThread = 0.0 - s_tSysPara.fDischgCurrHighPrtThre*s_tSysPara.wBatteryCap;
        JudgeAnalogStatus(&tAlmInfo);
        s_ucDisCurrHighFlag = CURR_HIGH_BDPB;
    }
    s_ucLastCurrHighFlag = CURR_HIGH_FAULT;

    //  电池组过压保护
    DIGITAL_ALARM_INIT(tAlmInfo, ucBattOverVoltPrt);
    JudgeDigitalAlarm(&tAlmInfo);

    //  充电回路失效
    DIGITAL_ALARM_INIT(tAlmInfo, ucChgLoopInvalid);
    JudgeDigitalAlarm(&tAlmInfo);

    //  放电回路失效
    DIGITAL_ALARM_INIT(tAlmInfo, ucDischgLoopInvalid);
    JudgeDigitalAlarm(&tAlmInfo);

    //  限流回路失效
    DIGITAL_ALARM_INIT(tAlmInfo, ucCurrLimLoopInvalid);
    tAlmInfo.pwRetryCounter     = &s_tAlarmData.wCurrLimitInvalidCnt;
    tAlmInfo.pwRetryCounterMax  = &s_tAlarmData.wCurrLimitInvalidCntMax;    
    tAlmInfo.pExceptionHandling = CurrLimLoopInvalidHandle;
    JudgeDigitalAlarm(&tAlmInfo);  
    
    //  电池熔丝损坏
    //DIGITAL_ALARM_INIT(tAlmInfo, ucBatFuseDamage);
    //JudgeDigitalAlarm(&tAlmInfo);
    
    //  母排熔丝损坏
    //DIGITAL_ALARM_INIT(tAlmInfo, ucBusFuseDamage);    
    //JudgeDigitalAlarm(&tAlmInfo);  

    //  电池反接
    DIGITAL_ALARM_INIT(tAlmInfo, ucBattReverse);
    JudgeDigitalAlarm(&tAlmInfo); 

    //  电池短路
    DIGITAL_ALARM_INIT(tAlmInfo, ucBattShortCut);
    tAlmInfo.pwRetryCounter     = &s_tAlarmData.wBattShortCutCnt;
    tAlmInfo.pwRetryCounterMax  = &s_tAlarmData.wBattShortCutCntMax;
    tAlmInfo.pExceptionHandling = Short_PrtHandle;
    JudgeDigitalAlarm(&tAlmInfo);  

    //  机内过温保护
    DIGITAL_ALARM_INIT(tAlmInfo, ucInsideTempHighPrt);
    JudgeDigitalAlarm(&tAlmInfo);
    
    //  BDU EEPROM故障
    DIGITAL_ALARM_INIT(tAlmInfo, ucBduEepromAlm);
    JudgeDigitalAlarm(&tAlmInfo);
    
    //  BDU电池欠压保护
    DIGITAL_ALARM_INIT(tAlmInfo, ucBDUBattVoltLowPrt);
    JudgeDigitalAlarm(&tAlmInfo);
    if(!IsSleep())
    {
        /*主机发现有电池放电电流大于最小检测电流时，会控制从机退出静置休眠。从机退出静置休眠时由于功率上送的
          BDU电池欠压保护没有及时恢复，此处会判断出电池组欠压保护。而退出静置休眠时电池组电压没有达到电池组欠压保护的恢复
          电压，所以电池组欠压保护一直持续，五分钟后会由于欠压保护UVP原因再次进入休眠，只有充电才能唤醒。*/
        if ( s_tBcmAlarm.ucBDUBattVoltLowPrt != NORMAL )
        {
            s_tBcmAlarmFlag.ucBattUnderVoltPrt = s_tBcmAlarm.ucBDUBattVoltLowPrt;
            s_tBcmAlarmReal.ucBattUnderVoltPrt = s_tBcmAlarm.ucBDUBattVoltLowPrt;
        }
    }

    // BDU电池充电欠压保护
	DIGITAL_ALARM_INIT(tAlmInfo,ucBDUBattChgVoltLowPrt);
	tAlmInfo.ucAlarmType = (GPIO_ON == GetPMOSStatus()) ? FAULT : NORMAL;  //仅在PMOS打开时产生告警
	JudgeDigitalAlarm(&tAlmInfo);
    
    //  BDU母排欠压保护
    DIGITAL_ALARM_INIT(tAlmInfo, ucBDUBusVoltLowPrt);
    tAlmInfo.pwRetryCounter     = &s_tAlarmData.wBduBusVoltLowPrtCnt;
    tAlmInfo.pwRetryCounterMax  = &s_tAlarmData.wBduBusVoltLowPrtCntMax;
    tAlmInfo.pExceptionHandling = DisCurrH_Short_PrtHandle;    //母排欠压与放电过流做相同处理
    JudgeDigitalAlarm(&tAlmInfo);
    
    //  BDU母排过压保护
    DIGITAL_ALARM_INIT(tAlmInfo, ucBDUBusVoltHighPrt);
    JudgeDigitalAlarm(&tAlmInfo);

    // //  地址冲突
    // DIGITAL_ALARM_INIT(tAlmInfo, ucAddressClash);
    // JudgeDigitalAlarm(&tAlmInfo);  

    //	电池被盗告警
    DIGITAL_ALARM_INIT(tAlmInfo, ucBattLoseAlm);
    tAlmInfo.ucState = (IsBattTheft() || s_bGprsBattAlmEn);
    JudgeDigitalAlarm(&tAlmInfo);

    //  单体电压采样异常
    DIGITAL_ALARM_INIT(tAlmInfo, ucCellVoltSampleFault);
    JudgeDigitalAlarm(&tAlmInfo);  

    //  BDU通信断
    DIGITAL_ALARM_INIT(tAlmInfo, ucBDUCommFail);
    JudgeDigitalAlarm(&tAlmInfo);

    // 直流内阻异常告警
    DIGITAL_ALARM_INIT(tAlmInfo, ucDcrFaultAlm);
    // 仅仅通过电池管理中的直流内阻异常告警状态进行判断，重启后需要重新进行一次DCR检测才能判断出是否需要产生告警
    tAlmInfo.ucState = s_tAlarmData.bDcrFaultAlm;
    JudgeDigitalAlarm(&tAlmInfo);

    // 直流内阻异常保护
    DIGITAL_ALARM_INIT(tAlmInfo, ucDcrFaultPrt);
    // 已经处于直流内阻异常保护的情况下继续保持，除非使用命令主动清除直流内阻异常保护。
    tAlmInfo.ucState = (s_tBcmAlarmReal.ucDcrFaultPrt == FAULT)?FAULT:s_tAlarmData.bDcrFaultPrt;
    JudgeDigitalAlarm(&tAlmInfo);

    //  回路异常
    DIGITAL_ALARM_INIT(tAlmInfo, ucLoopFault);
    tAlmInfo.ucState = s_tAlarmData.bLoopOff;
    JudgeDigitalAlarm(&tAlmInfo); 

    //	BDU电池闭锁告警
    DIGITAL_ALARM_INIT(tAlmInfo, ucBDUBattLockAlm);
    tAlmInfo.ucState = s_tAlarmData.ucBDUBattLockAlm;
    JudgeDigitalAlarm(&tAlmInfo);

    //	BDU连接器温度高保护
    DIGITAL_ALARM_INIT(tAlmInfo, ucBDUConnTempHighPrt);
    tAlmInfo.ucState = GetBDUConnTempHighRecoverFlag() ? s_tAlarmData.ucBDUConnTempHighPrt : s_tBcmAlarmReal.ucBDUConnTempHighPrt;
    JudgeDigitalAlarm(&tAlmInfo);

    //  均衡电路故障告警
    DIGITAL_ALARM_INIT(tAlmInfo, ucEqualCircuitFaultAlm);
    tAlmInfo.ucState = IsEqualCircuitFault();
    JudgeDigitalAlarm(&tAlmInfo);

    //  环境温度传感器无效告警
    DIGITAL_ALARM_INIT(tAlmInfo, ucEnvTempSensorInvalidAlm);
    tAlmInfo.ucState = (tBcmData.fEnvTemp >= ENVTEMP_MAX) ? FAULT : NORMAL;
    JudgeDigitalAlarm(&tAlmInfo);

    //	加热膜失效故障
    DIGITAL_ALARM_INIT(tAlmInfo, ucHeaterFilmFailure);
    tAlmInfo.ucState = s_tAlarmData.ucHeaterFilmFailure;
    JudgeDigitalAlarm(&tAlmInfo);

    //  主继电器失效
    DIGITAL_ALARM_INIT(tAlmInfo, ucMainRelayFail);
    JudgeDigitalAlarm(&tAlmInfo);

    //  DCDC故障
    DIGITAL_ALARM_INIT(tAlmInfo, ucDCDCErr);
    JudgeDigitalAlarm(&tAlmInfo);

    //  采样异常
    DIGITAL_ALARM_INIT(tAlmInfo, ucSampleErr);
    JudgeDigitalAlarm(&tAlmInfo);

    //  辅助源故障
    DIGITAL_ALARM_INIT(tAlmInfo, ucAuxiSourceErr);
    JudgeDigitalAlarm(&tAlmInfo);

    //  自放电异常
    DIGITAL_ALARM_INIT(tAlmInfo, ucSelfDischFualt);
    tAlmInfo.ucState = GetSelfDischFaultFlag();
    JudgeDigitalAlarm(&tAlmInfo);  
    
    //容量一致性差告警
    DIGITAL_ALARM_INIT(tAlmInfo, ucCapDCPRFaultAlm);
    tAlmInfo.ucState = GeCapDCDRFaultFlag();
    JudgeDigitalAlarm(&tAlmInfo);  

    //单体温升速率异常告警
    DIGITAL_ALARM_INIT(tAlmInfo, ucCellTempRiseAbnormal);
    tAlmInfo.ucState = GetTempRiseRateFault();
    JudgeDigitalAlarm(&tAlmInfo);

#ifdef BOARD_Temp_High_Prt_BDU
    DIGITAL_ALARM_INIT(tAlmInfo, ucBoardTempHighPrt);
    JudgeDigitalAlarm(&tAlmInfo);
#endif

    // 电源通信断告警
    DIGITAL_ALARM_INIT(tAlmInfo, ucPowerCommFail);
    tAlmInfo.ucAlarmType = JudgePowerCommFailAlarmSheild();  //休眠或接收到触发进入/退出apptest/qtp模式的命令时下不产生告警
    tAlmInfo.ucState = DealPowerCommFailCount();
    JudgeDigitalAlarm(&tAlmInfo);  

    return; 
}

/******************************************************************************
* 函数名称：JudgeBcmAlarmPriority()
* 调    用：
* 被 调 用：
* 输入参数：无
* 返 回 值：无
* 功能描述：屏蔽关系判断  
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要        
******************************************************************************/ 
static void JudgeBcmAlarmPriority( void )
{
    BYTE i;

    PROTECT_SHIELD_ALARM(ucBattUnderVoltPrt, ucBattUnderVoltAlm);
    PROTECT_SHIELD_ALARM(ucBattOverVoltPrt, ucBattOverVoltAlm);
    PROTECT_SHIELD_ALARM(ucBattSOCLowPrt, ucBattSOCLowAlm);
    PROTECT_SHIELD_ALARM(ucBattSOHPrt, ucBattSOHAlm);
    PROTECT_SHIELD_ALARM(ucCellPoorConsisPrt, ucCellPoorConsisAlm);
    PROTECT_SHIELD_ALARM(ucEnvTempSensorInvalidAlm, ucEnvTempHighPrt);
    PROTECT_SHIELD_ALARM(ucEnvTempSensorInvalidAlm, ucEnvTempHighAlm);
    PROTECT_SHIELD_ALARM(ucEnvTempHighPrt, ucEnvTempHighAlm);
    PROTECT_SHIELD_ALARM(ucEnvTempLowPrt, ucEnvTempLowAlm);
    PROTECT_SHIELD_ALARM(ucDcrFaultPrt, ucDcrFaultAlm);

    for (i = 0; i < CELL_TEMP_NUM_MAX; i++)
    {
        PROTECT_SHIELD_ALARM(aucChgTempHighPrt[i], aucChgTempHighAlm[i]);
        PROTECT_SHIELD_ALARM(aucChgTempLowPrt[i], aucChgTempLowAlm[i]);
        PROTECT_SHIELD_ALARM(aucDischgTempHighPrt[i], aucDischgTempHighAlm[i]);
        PROTECT_SHIELD_ALARM(aucDischgTempLowPrt[i], aucDischgTempLowAlm[i]);

        /* 出现温度采样异常时，屏蔽单体充/放电低温告警与保护 */
        // PROTECT_SHIELD_ALARM( ucCellTempAbnormal, aucChgTempLowAlm[i]);
        // PROTECT_SHIELD_ALARM( ucCellTempAbnormal, aucChgTempLowPrt[i]);
        // PROTECT_SHIELD_ALARM( ucCellTempAbnormal, aucDischgTempLowAlm[i]);
        // PROTECT_SHIELD_ALARM( ucCellTempAbnormal, aucDischgTempLowPrt[i]);
        /* 出现温度失效时，屏蔽单体充/放电低温告警与保护 */
        PROTECT_SHIELD_ALARM(ucCellTempSensorInvalidAlm[i], aucChgTempLowAlm[i]);
        PROTECT_SHIELD_ALARM(ucCellTempSensorInvalidAlm[i], aucChgTempLowPrt[i]);
        PROTECT_SHIELD_ALARM(ucCellTempSensorInvalidAlm[i], aucDischgTempLowAlm[i]);
        PROTECT_SHIELD_ALARM(ucCellTempSensorInvalidAlm[i], aucDischgTempLowPrt[i]);
    }
    for (i = 0; i < CELL_VOL_NUM_MAX; i++)
    {
        PROTECT_SHIELD_ALARM(ucCellVoltSampleFault, aucCellOverVoltAlm[i]);
        PROTECT_SHIELD_ALARM(ucCellVoltSampleFault, aucCellUnderVoltAlm[i]);
        PROTECT_SHIELD_ALARM(ucCellVoltSampleFault, aucCellOverVoltPrt[i]);
        PROTECT_SHIELD_ALARM(ucCellVoltSampleFault, aucCellUnderVoltPrt[i]);
        PROTECT_SHIELD_ALARM(ucCellVoltSampleFault, aucCellDamagePrt[i]);
        PROTECT_SHIELD_ALARM(aucCellOverVoltPrt[i], aucCellOverVoltAlm[i]);
        PROTECT_SHIELD_ALARM(aucCellUnderVoltPrt[i], aucCellUnderVoltAlm[i]);
    }

    return;
}

/****************************************************************************
* 函数名称：GetMainAlarmExistFlag
* 调    用：
* 被 调 用：
* 输入参数：无
* 返  回    值：重要告警存在标志
* 功能描述：获取重要告警存在标志
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要            
***************************************************************************/
BOOLEAN GetMainAlarmExistFlag( void )
{
    return GetMainAlarmExist();
}

/****************************************************************************
* 函数名称：CalRealAlarmTotal()
* 调    用：
* 被 调 用：
* 输入参数：无
* 返   回   值：实时告警总条数
* 功能描述：计算实时告警总条数
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要               
***************************************************************************/
WORD  CalRealAlarmTotal( BYTE ucAlarmType, BYTE *pucOutCommonAlm)
{
    BYTE ucCommonAlm = NORMAL, *pAlarmShield = NULL;
    WORD    i;  
    WORD   wRealAlarmTotal  = 0;
    
    GetSysPara( &s_tSysPara );
    pAlarmShield = s_tSysPara.aucAlarmLevel;
    for (i = 1; i < ALARM_NUM + 1; i++)
    {
        if ((s_atDisRealAlarm[i].ucState != NORMAL) && (s_atDisRealAlarm[i].ucInnerFlag == ucAlarmType))
        {
            wRealAlarmTotal++;
        }
    }
    ucCommonAlm = 0 < wRealAlarmTotal ? FAULT : NORMAL;
    if (NULL != pucOutCommonAlm)
    {
        *pucOutCommonAlm = IGNORE == pAlarmShield[0] ? NORMAL : ucCommonAlm;
        DealDisAlarm(0, *pucOutCommonAlm);
    }

    return wRealAlarmTotal;
}

Static void alarmToClass(BYTE *pucClass)
{
    BYTE i, j, *p;

    p = ( BYTE * )&s_tBcmAlarm;
    for ( i = 0; i < ALARM_CLASS; i++ )
    {
        pucClass[i] = 0;
        if ( 1 == checkAlmGroup(p, &s_tBcmAlarm) )
        {
            for( j=0; j<CELL_VOL_NUM_MAX; j++ )
            {
                pucClass[i] |= (0 != *(p++));
            }
        }
        else if ( 2 == checkAlmGroup(p, &s_tBcmAlarm) )
        {
            for( j=0; j<CELL_TEMP_NUM_MAX; j++ )
            {
                pucClass[i] |= (0 != *(p++));
            }
        }
        else
        {
            pucClass[i] = *(p++);
        }
    }
    return;
}

/****************************************************************************
* 函数名称：SetCtrlOutRelay
* 调    用：
* 被 调 用：
* 输入参数：无
* 返   回   值：无
* 功能描述：获取告警输出干结点控制字\
* 作    者：
* 设计日期：
* 修改记录：// yang_an,整理协议修改，删掉油机启动部分干接点 
* 日    期      版  本      修改人      修改摘要            
***************************************************************************/
void    SetCtrlOutRelay( void )
{
    WORD    wtmp = 0;
    BYTE    i, k;
    BYTE    aucTemp[ALARM_CLASS];
    T_CtrlOutStruct     tCtrl;

    GetSysPara( &s_tSysPara );
    alarmToClass(aucTemp);
    if (GetApptestFlag())
    {
        return;
    }
    
    for ( i = 0; i < ALARM_CLASS; i++ )
    {
        k = s_tSysPara.aucRelayBit[i];
        if ( (k <= RELAY_OUT) && (k > 0) )
        {
            if ( aucTemp[i] )
            {
                    wtmp |= BIT(k-1);//PCLINT检查:经确认，对程序正确性无影响。
            }
        }
    }
    if(s_tSysPara.ucRelayDefaultStatus == 0)//
    {
         wtmp = ~wtmp;               
    }
    GetCtrlOut( &tCtrl );
    tCtrl.wRelayOut = wtmp;
    SetCtrlOut( &tCtrl );

    return; 
}
#endif


/****************************************************************************
* 函数名称：GetDataFlag
* 调    用：
* 被 调 用：
* 输入参数：ucDataFlagID-交流/整流/直流等ID号；ucCommPort-串口号
* 返 回 值：DATA_FLAG
* 功能描述：获取DATA_FLAG
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要                   
***************************************************************************/
BYTE GetDataFlag( BYTE ucDataFlagID, BYTE ucCommPort )
{
    switch ( ucDataFlagID )
    {
        case    BCM_ALARM:
            return  s_aucBcmDataFlag[ucCommPort];
        default:
            return 0x00;
    }   
}

/****************************************************************************
* 函数名称：SetDataFlag
* 调    用：
* 被 调 用：
* 输入参数：ucDataFlagID-交流/整流/直流等ID号；ucValue-设定值
* 返 回 值：无
* 功能描述：设置DATA_FLAG
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要               
***************************************************************************/
void SetDataFlag( BYTE ucDataFlagID, BYTE ucValue )
{
    switch ( ucDataFlagID )
    {
        case BCM_ALARM:
            s_aucBcmDataFlag[COMM_RS485]  |= ucValue;
            s_aucBcmDataFlag[COMM_PORT1]  |= ucValue;
            s_aucBcmDataFlag[COMM_PORT2]  |= ucValue;
            s_aucBcmDataFlag[COMM_CAN]    |= ucValue;
            break;
        default:
            break;
    }
    
    return; 
}

/****************************************************************************
* 函数名称：ClearDataFlag
* 调    用：
* 被 调 用：
* 输入参数：ucDataFlagID-交流/整流/直流等ID号；
                     ucValue-数值；
                     ucCommPort-串口号
* 返 回 值    ：无
* 功能描述：清掉DATA_FLAG的相应位
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要               
***************************************************************************/
void ClearDataFlag( BYTE ucDataFlagID, BYTE ucValue, BYTE ucCommPort )
{
    switch ( ucDataFlagID )
    {
        case BCM_ALARM:
            s_aucBcmDataFlag[ucCommPort] &= ucValue;             
        default:
            break;
    }
    
    return; 
}

/****************************************************************************
* 函数名称：GetRealAlarm
* 调    用：
* 被 调 用：
* 输入参数：ucUnitID-交流/整流/直流等ID号；pDest-目标地址
* 返 回 值：无
* 功能描述：获取实时告警
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要               
***************************************************************************/
void GetRealAlarm( BYTE ucUnitID, BYTE *pDest )
{
    if(NULL == pDest)
    {
        return;
    }
    
    if (BCM_ALARM == ucUnitID)
    {
    	if(s_bBcmAlmStartUpFlag)
    	{
            rt_memcpy( (BYTE *)pDest, (BYTE *)&s_tBcmAlarmStartUp, sizeof(T_BCMAlarmStruct) );
            iterFlag++;
            if (iterFlag > ALARM_DELAY_COUNTER) {
                s_bBcmAlmStartUpFlag = False;
            }
            return;
    	}
    	else
    	{
            rt_memcpy( (BYTE *)pDest, (BYTE *)&s_tBcmAlarm, sizeof(T_BCMAlarmStruct) );
            return;
    	}

    }

    if (BCM_ALARM_REAL == ucUnitID)
    {
        rt_memcpy( (BYTE *)pDest, (BYTE *)&s_tBcmAlarmReal, sizeof(T_BCMAlarmStruct) );
    }    
    
    return;
}

/****************************************************************************
* 函数名称：GetBcmRealAlarm
* 调    用：
* 被 调 用：
* 输入参数：Dest-目标地址
* 返 回 值：无
* 功能描述：获取Bcm未判断实时告警
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要               
***************************************************************************/
void GetBcmRealAlarm(T_BCMAlarmStruct *pDest)
{
    rt_memcpy((BYTE *)pDest, (BYTE *)&s_tBcmAlarmTmp, sizeof(T_BCMAlarmStruct)); 
}

/***************************************************************************
* 函数名称：TransBcmAlarm
* 调    用：
* 被 调 用：
* 输入参数：tBcmAlarm---整流器告警指针，ucSmrNo---整流器编号
* 返 回 值：无 
* 功能描述：传送CAN通讯BCM告警
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要                    
***************************************************************************/
void TransBcmAlarm( T_BCMAlarmStruct* tBcmAlarm )
{
    rt_memcpy((BYTE *)&s_tBcmAlarmTmp, (BYTE *)tBcmAlarm, sizeof(T_BCMAlarmStruct)); 
}

void setShakeAlm(BOOLEAN bSet)
{
    return;
}

static BOOLEAN AnalyticalAlmCheck(WORD wID)
{
    T_AnalyseInfoStruct tAnalyseInfo = {0,};
    GetAnalyticalAlm( &tAnalyseInfo );
    //特定告警被触发时，保存用于电芯统计记录
    //wID = aucCellUnderVoltPrt[CELL_VOL_NUM_MAX] OR wID = aucCellDynamicUnderVoltPrt[CELL_VOL_NUM_MAX];
    if ((wID == BATT_UNDER_VOLT_PRT_INDEX) \
        || ( wID >= CELL_UNDER_VOLT_PRT_START_INDEX && wID <= CELL_UNDER_VOLT_PRT_END_INDEX ) \
        || ( wID >= CELL_DYNAMIC_UNDER_VOLT_PRT_START_INDEX && wID <= CELL_DYNAMIC_UNDER_VOLT_PRT_END_INDEX ))
    {
        tAnalyseInfo.bBattLowVolPrtTriggered = 1;
    }
    //wID = aucCellOverVoltPrt[CELL_VOL_NUM_MAX];
    if ( wID >= CELL_OVER_VOLT_PRT_START_INDEX && wID <= CELL_OVER_VOLT_PRT_END_INDEX )
    {
        tAnalyseInfo.bBattOverVolPrtTriggered = 1;
    }
    //wID = ucChgCurrHighPrt or ucDischgCurrHighPrt
    if ( wID == CHG_CURR_HIGH_PRT_INDEX || wID == DISCHG_CURR_HIGH_PRT_INDEX )
    {
        tAnalyseInfo.bBattOverCurrPrtTriggered = 1;
    }
    TransAnalyticalAlm( &tAnalyseInfo, INFO_WRITTEN );
    return True;
}

/******************************************************************************
**函 数 名：DealDisAlarm()
**输    入：ucID-告警ID号  ucState-当前状态
**输    出：无
**调用模块：
**功能描述：处理实时、历史告警显示
* 作    者：严宏明
**时    间：2004-06-07
**修改记录：
* 日    期      版  本      修改人      修改摘要      
******************************************************************************/ 
static void DealDisAlarm( WORD  wID, BYTE ucState )
{
//    T_TimeStruct    tTime;
    WORD wRealAlmLen = 0;
    T_HisAlarmSaveStruct tDisHisAlarm = {0};

    //若告警消除，保存历史告警
    if ( s_atDisRealAlarm[wID].ucState && !ucState )
    {   
        tDisHisAlarm.wAlarmID   = wID;
        //tDisHisAlarm.ucState  = s_atDisRealAlarm[wID].ucState;
        tDisHisAlarm.tStartTime = s_atDisRealAlarm[wID].tTime;
        time(&tDisHisAlarm.tEndTime);

        //将历史告警保存至EEPROM
        if (s_atDisRealAlarm[wID].ucInnerFlag == 0)
        {
            SaveHisAlarm( &tDisHisAlarm);
            ClearSaveRealAlm(wID);
            AnalyticalAlmCheck(wID);
        }
    }

    if (ucState)
    {//将实时告警存放在缓存中
        s_atDisRealAlarm[wID].ucState = ucState;
        time(&s_atDisRealAlarm[wID].tTime);

        if (s_tRealAlmSave.ucRealAlmLen < REALALM_MAX_NUM)
        {
            s_tRealAlmSave.ucRealAlmLen++;
        }
        wRealAlmLen = s_tRealAlmSave.ucRealAlmLen - 1;
        s_tRealAlmSave.atRealAlm[wRealAlmLen].wAlarmID = wID;
        s_tRealAlmSave.atRealAlm[wRealAlmLen].ucInnerFlag = s_atDisRealAlarm[wID].ucInnerFlag;
        time(&s_tRealAlmSave.atRealAlm[wRealAlmLen].tTime);
    }
    return;
}

static void checkSavedAlarm( void )
{
	BYTE	i;
	BYTE    *pAlarm;
	WORD    wAlarmID = 0, wOffset;

    T_HisAlarmSaveStruct tDisHisAlarm = {0};

    wOffset = COMMON_ALARM_NUM;
    pAlarm  = ( BYTE * )&s_tBcmAlarmStartUp;

	readBmsRealAlarm(&s_tRealAlmSave);
	
    if ( 0 == CRC_Cal((BYTE*)&s_tRealAlmSave, offsetof(T_RealAlm256SaveStruct, wCrc) + 2) )
    {
        //上电后，保存的实时告警转存历史告警
        for (i = 0; i < MIN(s_tRealAlmSave.ucRealAlmLen, REALALM_MAX_NUM); i++)
        {
            wAlarmID = s_tRealAlmSave.atRealAlm[i].wAlarmID - wOffset; //告警ID,需减偏移
            if (wAlarmID >= sizeof(s_tBcmAlarmStartUp))
            {
                break;
            }
            pAlarm[wAlarmID] = FAULT;

            tDisHisAlarm.wAlarmID   = s_tRealAlmSave.atRealAlm[i].wAlarmID;
            tDisHisAlarm.tStartTime = s_tRealAlmSave.atRealAlm[i].tTime;
            time(&tDisHisAlarm.tEndTime);
            SaveHisAlarm( &tDisHisAlarm);
        }
    }
    rt_memset_s( (BYTE *)&s_tRealAlmSave, sizeof(s_tRealAlmSave), 0, sizeof(s_tRealAlmSave) );
    SaveRealAlarm(&s_tRealAlmSave);

	return;
}

static BOOLEAN ClearSaveRealAlm(WORD wID)
{
    BYTE i;
    BYTE ucLen = s_tRealAlmSave.ucRealAlmLen;
    BOOLEAN bRet = False;
    if (wID > ALARM_NUM)
    {
        return False;
    }
    for (i = 0; i < ucLen; i++)
    {
        if (s_tRealAlmSave.atRealAlm[i].wAlarmID == wID)
        {
            rt_memset_s(&s_tRealAlmSave.atRealAlm[i], sizeof(T_RealAlarmStruct), 0x00, sizeof(T_RealAlarmStruct));
            rt_memcpy(&s_tRealAlmSave.atRealAlm[i], &s_tRealAlmSave.atRealAlm[ucLen-1], sizeof(T_RealAlarmStruct));
            s_tRealAlmSave.ucRealAlmLen -= 1;
            bRet = True;
            break;
        }
    }
    return bRet;
}

BYTE JudgeAlarmType( T_BattResult const* pBattOut )
{
    WORD i;
    BYTE *p = (BYTE *)&s_tBcmAlarm;
    BYTE *pAlarmLevel = (BYTE *)&s_tBcmAlarmShield;
    BYTE ucAlmLevelFlag = 0;
#ifdef RESTORE_ALM_LED_OFF_AND_NOT_RESTORE_ALM_LED_ON
    if(IfSysInvalid())
    {
        return ALM_SYS_ABNORMAL;
    }

    if(s_tBcmAlarm.ucPowerCommFail != NORMAL){
        return ALM_ABNORMAL;
    }
#else
    if(s_tBcmAlarm.ucBattLoseAlm != NORMAL)
    {
        return ALM_SYS_LOCK;
    }

    if(IfSysInvalid())
    {
        return ALM_SYS_ABNORMAL;
    }
    for(i = 0;i < sizeof(s_tBcmAlarm); i++)
    {
        if(p[i] != NORMAL)
        {
            if(pAlarmLevel[i] == EMERGENCY)
            {
                // 重要告警快闪
                ucAlmLevelFlag = EMERGENCY;
                break;
            }
            else if(pAlarmLevel[i] == Minor)
            {
                // 次要告警慢闪
                ucAlmLevelFlag = Minor;
            }
        }
    }

    if (ucAlmLevelFlag == EMERGENCY)
    {
        // 重要告警快闪
        return AlarmIndicatorStatus(s_tSysPara.ucCriticalAlarmlight);
    }
    else if (ucAlmLevelFlag == Minor)
    {
        // 次要告警慢闪
        return AlarmIndicatorStatus(s_tSysPara.ucSecondaryAlarmlight);
    }

    /*if (IsNorthCommFail())
    {
        return ALM_COMM_FAIL;
    }*/ //软银不需要，暂去掉
#endif
    return ALM_NORMAL;
}

Static BOOLEAN IsAnyAlarmExist()
{
#ifdef RESTORE_ALM_LED_OFF_AND_NOT_RESTORE_ALM_LED_ON
    return IsAlarmExistForNotRestoreLedOnTwo();
#else
    return IsAlarmExistNormal();
#endif
}

#ifdef RESTORE_ALM_LED_OFF_AND_NOT_RESTORE_ALM_LED_ON
static BOOLEAN IsAlarmExistForNotRestoreLedOnOne()
{
    if(s_tBcmAlarm.ucBattReverse
        ||s_tBcmAlarm.ucDischgLoopInvalid
        ||s_tBcmAlarm.ucChgLoopInvalid
        ||s_tBcmAlarm.ucCurrLimLoopInvalid
        ||s_tBcmAlarm.ucBattSOHPrt
        ||s_tBcmAlarm.ucCellPoorConsisPrt
        ||s_tBcmAlarm.ucCellVoltSampleFault
        ||s_tBcmAlarm.ucEnvTempSensorInvalidAlm
        ||s_tBcmAlarm.ucBduEepromAlm
        ||s_tBcmAlarm.ucBDUCommFail
        ||s_tBcmAlarm.ucBDUBattLockAlm
        ||s_tBcmAlarm.ucEqualCircuitFaultAlm
        ||s_tBcmAlarm.ucLoopFault
        ||s_tBcmAlarm.ucHeaterFilmFailure){
        return True;
    }
    return False;
}

static BOOLEAN IsAlarmExistForNotRestoreLedOnTwo()
{
    if(IsAlarmExistForNotRestoreLedOnOne() == False){
        if(s_tBcmAlarm.ucFuseError 
            ||s_tBcmAlarm.ucBDUConnTempHighPrt 
            ||s_tBcmAlarm.ucMainRelayFail 
            ||s_tBcmAlarm.ucDCDCErr 
            ||s_tBcmAlarm.ucSampleErr 
            ||s_tBcmAlarm.ucAuxiSourceErr 
            ||s_tBcmAlarm.ucHeatConnTempHighPrt 
            ||s_tBcmAlarm.ucCellTempRiseAbnormal
            ||s_tBcmAlarm.ucDcrFaultPrt){
                return True;
            }
    }else{
        return True;
    }
    return False;
}
#endif

static BOOLEAN IsAlarmExistNormal()
{
    BYTE AlarmExist = 0;
    AlarmExist = s_tBcmAlarm.ucDischgLoopInvalid
       |s_tBcmAlarm.ucChgLoopInvalid
       |s_tBcmAlarm.ucCurrLimLoopInvalid
       |s_tBcmAlarm.ucBduEepromAlm
       |s_tBcmAlarm.ucBDUCommFail
       |s_tBcmAlarm.ucCellVoltSampleFault
       |s_tBcmAlarm.ucBoardTempHighPrt
       |s_tBcmAlarm.ucBattSOHPrt
       |s_tBcmAlarm.ucBDUBattLockAlm
       |s_tBcmAlarm.ucCellTempAbnormal
       |s_tBcmAlarm.ucBDUBattChgVoltLowPrt
       |s_tBcmAlarm.ucEqualCircuitFaultAlm
       |s_tBcmAlarm.ucEnvTempSensorInvalidAlm
       |s_tBcmAlarm.ucBattReverse
       |s_tBcmAlarm.ucHeaterFilmFailure
#ifdef USING_HEAT_CONNECT
       |s_tBcmAlarm.ucHeatConnTempHighPrt
#endif
       |s_tBcmAlarm.ucMainRelayFail
       |s_tBcmAlarm.ucDCDCErr
       |s_tBcmAlarm.ucSampleErr;
    if (AlarmExist)
    {
        return True;
    }
    return False;
}

Static BOOLEAN IsAnyAlarmExistNew()
{
    if (s_tBcmAlarm.ucBDUConnTempHighPrt
       || s_tBcmAlarm.ucHeaterFilmFailure
       || s_tBcmAlarm.ucMainRelayFail
       || s_tBcmAlarm.ucDCDCErr
       || s_tBcmAlarm.ucSampleErr
       || s_tBcmAlarm.ucAuxiSourceErr
     )
    {
        return True;
    }
    return False;
}
BOOLEAN IfSysInvalid(void)
{
    BYTE i;
    if (IsAnyAlarmExist() == True || (IsAnyAlarmExistNew() == True))
    {
        return True;
    }

    for(i = 0; i < s_tHardwarePara.ucCellVoltNum; i++)
    {
        if(s_tBcmAlarm.aucCellDamagePrt[i])
        {
           return True;
        }
    }
    for(i = 0; i < s_tHardwarePara.ucCellTempNum; i++)
    {
    	//单体温度一个无效则长亮
        if(NORMAL != s_tBcmAlarm.ucCellTempSensorInvalidAlm[i])
        {
            return True;
        }
    }
    return False;
}

static void SetTheftAlm(BOOLEAN bAlm)
{
    s_tBcmAlarm.ucBattLoseAlm     = bAlm;
    s_tBcmAlarmReal.ucBattLoseAlm = bAlm;
    saveResetData(BATT_THEFT, bAlm);

    return;
}

static BOOLEAN  IsBattTheft(void)
{
    BOOLEAN bIsBattTheft = False;

    //需要特殊处理，系统掉电电池丢失告警也不能清除
    //移到布防状态判断之前判断，避免撤防造成电池丢失告警被解除。
    if(s_tBcmAlarmReal.ucBattLoseAlm != NORMAL)
    {
        return True;
    }

    //布防失败的情况下不进行电池丢失告警判断
    if (GetDefenceStatus() == False)
    {
        return False;
    }

    if (JudgeCommAntiTheft() == True)
    {
        bIsBattTheft = True;
    }

    if (bIsBattTheft == True)
    {
        SetGprsAutoUpStatus(True);
        SetTheftAlm(FAULT);
        return True;
    }

    return False;
}

/***************************************************************************
 * @brief    通讯防盗判断
 * @return   True-产生告警 False-不产生告警
 **************************************************************************/
static BOOLEAN JudgeCommAntiTheft(void)
{
    if (s_tSysPara.wSoftAntiTheftDelay == 0) // 软件防盗延时屏蔽
    {
        return False;
    }
    //锂离子电池与CSU通讯线延迟一段时间(用户可设置延迟时间)后触发电池丢失告警。
    if(JudgeCommDisconnect() == True)
    {
        s_wCommLostCnt++;
    }
    //在触发电池丢失告警之前，只要北向通讯恢复了就不产生电池丢失告警；
    else
    {
        s_wCommLostCnt = 0;
    }
    if (s_wCommLostCnt > s_tSysPara.wSoftAntiTheftDelay * ONE_MINUTE_CNT_ALARM_THREAD)
    {
        s_wCommLostCnt = s_tSysPara.wSoftAntiTheftDelay * ONE_MINUTE_CNT_ALARM_THREAD;
        return True;
    }

    return False;
}

/***************************************************************************
 * @brief    电池设备级防盗统一解锁入口
 * @param    {BYTE} ucMode  使用的解锁方式
 **************************************************************************/
SHORT DealDeviceUnlock(BYTE ucMode)
{
    if (NORMAL == s_tBcmAlarm.ucBattLoseAlm)
    {
        return FAILURE;  //  无防盗告警则不处理,防止重复执行导致通讯延迟
    }

    GetSysPara(&s_tSysPara);
    if (s_tSysPara.ucBattUnlockMode != ucMode)
    {
        return FAILURE; // 使用的解锁方式与系统设定的不同则不处理
    }
    // 首先进行计数清零，避免告警置为正常后由于计数未清零告警再次置为异常
    ClearBattTheftAlmCnt();

    ClearBattTheftAlm();
    return SUCCESSFUL;
}

/***************************************************************************
 * @brief    清除直流内阻异常保护告警
 * @param
 **************************************************************************/
BOOLEAN ClearDcrFaultPrt(void)
{
    s_tBcmAlarm.ucDcrFaultPrt = NORMAL;
    s_tBcmAlarmReal.ucDcrFaultPrt = NORMAL;
    // 需要将battout里对应的直流内阻保护状态置为正常
    SetDcrFaultPrtNormal();
    saveResetData(DCR_FAULT_ALM_PRT, DCR_NORMAL);
    SaveAction(GetActionId(CONTOL_CLEAR_DCR_PRT), "Clear DCR Prt");
    return TRUE;
}

/***************************************************************************
 * @brief    直流内阻异常告警支持自动恢复，重启后需要重新判断
 * @param
 **************************************************************************/
BOOLEAN ClearDcrFaultAlm(void)
{
    s_tBcmAlarm.ucDcrFaultAlm = NORMAL;
    s_tBcmAlarmReal.ucDcrFaultAlm = NORMAL;
    // 需要将battout里对应的直流内阻告警状态置为正常
    SetDcrFaultAlmNormal();
    return TRUE;
}

/***************************************************************************
 * @brief    清除设备级电池丢失告警
 * @param    {BYTE} ucAddr  本机地址
 **************************************************************************/
void ClearBattTheftAlm(void)
{
    SetTheftAlm(NORMAL);
    return;
}

/***************************************************************************
 * @brief    清除通讯防盗计数器
 **************************************************************************/
WORD ClearBattTheftAlmCnt(void)
{
    s_wCommLostCnt = 0;
    return s_wCommLostCnt;
}

BOOLEAN SetGprsAutoUpStatus(BOOLEAN bStatus)
{
	if(bStatus == True)
	{
		s_bGprsAutoUpDataFlag = bStatus;
	}
	else
	{
		s_bGprsAutoUpDataFlag = False;
	}
	return s_bGprsAutoUpDataFlag;
}

/***************************************************************************
* @brief    设置GPRS主动上送状态
 **************************************************************************/
BOOLEAN GetGprsAutoUpStatus(void)
{
	return s_bGprsAutoUpDataFlag;
}

void GprsSetBattTheftAlm(void)
{
    //避免频繁写EEPROM Added by fengfj, 2019-05-28 19:35:11
    if(NORMAL == s_tBcmAlarmReal.ucBattLoseAlm)
    {
        //g_bGprsAutoUpDataFlag = True;
    	SetTheftAlm(FAULT);
    }
    s_bGprsBattAlmEn = True;
    return;
}

void GprsClearBattTheftAlm(void)
{
    s_bGprsBattAlmEn = False;
    ClearBattTheftAlm();
}

#ifdef BOARD_Temp_High_Prt_BDPB
static INT32S BoardTempHighPrtHandle(struct T_AlmInfoTmp const* pAlmInfo, BOOLEAN* pbTmpStatus)
{
    BOOLEAN  bStatus;
    FLOAT    fDelta = 0.0;

    if (NULL == pAlmInfo || NULL == pbTmpStatus)
    {
        return FAILURE;
    }

    if(NORMAL != *(pAlmInfo->pucRealAlarm))
    {
        fDelta = pAlmInfo->fValueAdd;
    }

    if (pAlmInfo->fValue > (fDelta + pAlmInfo->fThread))
    {
        bStatus = FAULT;
    }
    else
    {
        bStatus = s_tBcmAlarmTmp.ucBoardTempHighPrt;
    }
    *pbTmpStatus = bStatus;

    return RTN_ALM_STATUE;
}
#endif /* BOARD_Temp_High_Prt_BDPB */

static BOOLEAN IsCellPoorConsisStart(FLOAT fBattVol, FLOAT fBattCurr)
{
	if(fBattCurr > s_tAlarmData.fCurrMinDet)
	{////充电并且有充电电流时
		if(fBattVol > fBattCurr * 0.02 + 3.17*s_tHardwarePara.ucCellVoltNum && fBattVol < fBattCurr * 0.02 + 3.4*s_tHardwarePara.ucCellVoltNum)
		{
			return True;
		}
	}
	else if(fBattVol > 3.17*s_tHardwarePara.ucCellVoltNum && fBattVol < 3.4*s_tHardwarePara.ucCellVoltNum)
	{
		return True;
	}

	return False;
}

BOOLEAN GetICRestartFlag(void)
{
    return s_bRestartIC;
}

void SetICRestartFlag(BOOLEAN Flag)
{
    s_bRestartIC = Flag;
}

Static BOOLEAN IsEqualCircuitFault(void)
{
    BYTE ucEqualCircuitFault = 0;
    BOOLEAN bEqualCircuitHigh;
    if ( s_bBalanceFault )
    {
        return True;  // 不能自动恢复
    }

    ucEqualCircuitFault = GetEqualCircuitFault();
    if(ucEqualCircuitFault == 1 || ucEqualCircuitFault == 2)  //当24小时周期检测时检测到短路或者断路时，产生均衡故障告警
    {
        return True;
    }

    bEqualCircuitHigh = IsEqualCircuitFaultDetected();
    if (!IsCellEqualOn())
    {
        if (bEqualCircuitHigh)
        {
            s_wCircShortCnt++;
        }
        else
        {
            s_wCircShortCnt = 0;
        }
    }
    else
    {
        if (bEqualCircuitHigh)
        {
            s_wCircOpenCnt = 0;
            s_uCircOpenTimes = 0;
        }
        else
        {
            s_wCircOpenCnt++;
        }
    }

     if(500 < s_wCircOpenCnt && s_uCircOpenTimes < 1 && s_bRestartIC == False)
    {
        s_wCircOpenCnt = 0;
        s_uCircOpenTimes++;
        s_bRestartIC  = True;
        SaveAction(GetActionId(CONTOL_SAMPLE_IC_RESTART), "CircuitOpen ICRst");
    } 
    
   if ((166 < s_wCircShortCnt)||((500 < s_wCircOpenCnt)&&(s_bRestartIC == True)))
    {
        s_bBalanceFault = True;
    }

    return s_bBalanceFault;
}

void ClearCellDamagePrt(void)
{
    BYTE i = 0;

    for (i = 0; i < s_tHardwarePara.ucCellVoltNum; i++)
    {
        s_tCellDamageSave.abCellDamagePrt[i] = NORMAL;
        s_atCellVoltPoor[i].bState = NORMAL;
        s_atCellVoltPoor[i].bFlag = NORMAL;
        s_atCellVoltPoor[i].ucCounter = 0;
        s_atCellVoltTooHigh[i].bState = NORMAL;
        s_atCellVoltTooHigh[i].bFlag = NORMAL;
        s_atCellVoltTooHigh[i].ucCounter = 0;
        CLEAR_ALAEM(aucCellDamagePrt[i]);
    }
    return;
}

static void ReadDefenceStatus(void)
{
	LONG lDefenceInfoReadRtn;
	rt_memset_s(&s_tDefenceInfo, sizeof(T_DefenceInfoStruct), 0, sizeof(T_DefenceInfoStruct));

	lDefenceInfoReadRtn = readFile(FILE_NAME_DEFENCEINFO, (BYTE*)&s_tDefenceInfo, sizeof(T_DefenceInfoStruct));
    if ( s_tDefenceInfo.wCheckSum != CRC_Cal((BYTE*)&s_tDefenceInfo, offsetof(T_DefenceInfoStruct,wCheckSum)) || (lDefenceInfoReadRtn != sizeof(T_DefenceInfoStruct)) )
    {
		rt_memset_s(&s_tDefenceInfo, sizeof(T_DefenceInfoStruct), 0, sizeof(T_DefenceInfoStruct));//数据异常时直接将布防状态处理为未布防
    }
}

Static BOOLEAN AutoCloseDefence(void)
{
    GetSysPara(&s_tSysPara);
    if (s_tSysPara.aucAlarmLevel[BATT_LOSE_ALM_INDEX] == 0 || (s_tSysPara.wSoftAntiTheftDelay == 0) )
    {
        //当前保存的布防状态为TRUE时更新布防状态为FALSE;
        if(GetDefenceStatus() == True)
        {
            WriteDefenceStatus(False);
            return True;
        }
    }
    return FALSE;
}

/***************************************************************************
 * @brief    清除布防状态
 **************************************************************************/
BOOLEAN ClearDefenceStatus(BYTE ucFlag)
{
    const char* actionName = "Unknown"; // 初始化 actionName 为默认值
    //当前保存的布防状态为TRUE时更新布防状态为FALSE
    if(s_tDefenceInfo.wDefenceStatus == True)
    {
        s_tDefenceInfo.wDefenceStatus = False;
        s_tDefenceInfo.wCheckSum = CRC_Cal((BYTE*)&s_tDefenceInfo, (offsetof(T_DefenceInfoStruct,wCheckSum)));
        if (writeFile(FILE_NAME_DEFENCEINFO, (BYTE*)&s_tDefenceInfo, sizeof(T_DefenceInfoStruct)))
        {
            switch (ucFlag)
            {
                case DEFENCE_CLEAR_BY_ENTERAPPTEST:
                    actionName = "EntAppClearDefence";
                    break;
                case DEFENCE_CLEAR_BY_EXITAPPTEST:
                    actionName = "ExitAppClearDefence";
                    break;
                case DEFENCE_CLEAR_BY_ENTERQTP:
                    actionName = "EntQTPClearDefence";
                    break;
                case DEFENCE_CLEAR_BY_EXITQTP:
                    actionName = "ExitQTPClearDefence";
                    break;
                default:
                    actionName = "TestClearDefence";
                    break;
            }
            SaveAction(GetActionId(CONTOL_DEFENCE_STATUS_CHANGE), actionName);
            return True;
        }
    }
    return False;
}

/***************************************************************************
 * @brief    返回布防状态
 * @return   True-已布防 False-未布防
 **************************************************************************/
WORD GetDefenceStatus(void)
{
    return s_tDefenceInfo.wDefenceStatus;
}

BOOLEAN IsBattLock(void)
{
    return (s_tBcmAlarmReal.ucBDUBattLockAlm != NORMAL);
}

//判断是否有告警产生
static BOOLEAN JudgeIfExixtAlarm(BYTE *pAlarm,WORD wLength)
{
    for(int i = 0;i < wLength ;i++)
    {
        if(pAlarm[i] == True)
        {
            return True;
        }
    }

    return False;
}

//获取存在告警的标志位
BOOLEAN GetExistAlarmFlag(VOID)
{
  return s_bExistAlarmFlag; 
}

/**
 * @brief 判断单体损坏闭锁条件
 * 
 */
FLOAT GetBattVoltThresh(void)
{
  return s_fBattVoltThresh;
}

/***************************************************************************
 * @brief    根据条件判断告警的AlarmType
 * @return   FAULT-异常 NORMAL-正常
 **************************************************************************/
static BOOLEAN JudgeAlarmInfoType(BOOLEAN condition)
{
    if(condition)
    {
        return FAULT;
    }
    else
    {
        return NORMAL;
    }
}

BOOLEAN ResetPowerCommFalg(BOOLEAN bFlag)
{
    return s_bPowerCommFlag = bFlag;
}

BOOLEAN SetPowerCommShieldFlag(BOOLEAN bFlag)
{// 接收到触发进入/退出apptest/qtp模式，屏蔽告警
    return s_bPowerCommShieldFlag = bFlag;
}

static BOOLEAN DealPowerCommFailCount(VOID)
{
    if(s_bPowerCommFlag == NORMAL)
    {// 通信正常则重置计数时间
        s_wPowerCommTimer = 0;
        s_bPowerCommFlag = FAULT;
        return NORMAL;
    }

    TimerPlus(s_wPowerCommTimer, ALARM_FIVE_MIN);  // 5min计时

    if(TimeOut(s_wPowerCommTimer, ALARM_FIVE_MIN))
    {
        return FAULT;
    }

    return NORMAL;
}

static BYTE JudgePowerCommFailAlarmSheild(void)
{ //休眠或接收到触发进入/退出apptest/qtp模式的命令时下不产生告警
    BYTE ucPowerCommFailSheild = 0;

    ucPowerCommFailSheild =  (IsSleep() || s_bPowerCommShieldFlag) ? NORMAL : FAULT;

    return ucPowerCommFailSheild;
}
/***************************************************************************
 * @brief    写入布防状态
 * @return   SUCCESSFUL-写入成功 FAILURE-写入失败
 **************************************************************************/
BOOLEAN WriteDefenceStatus(BOOLEAN bDefenceStatus){
    UINT32 len = 0;
    if(GPIO_OFF == GetPMOSStatus())
    {
        return FAILURE;
    }
    if(bDefenceStatus == True){
        SaveAction(GetActionId(CONTOL_DEFENCE_STATUS_CHANGE), "Normal->Defence");
    }else{
        SaveAction(GetActionId(CONTOL_DEFENCE_STATUS_CHANGE), "Defence->Normal");
    }
    
    s_tDefenceInfo.wDefenceStatus = bDefenceStatus;
    s_tDefenceInfo.wCheckSum = CRC_Cal((BYTE*)&s_tDefenceInfo, (offsetof(T_DefenceInfoStruct,wCheckSum)));
    len = writeFile(FILE_NAME_DEFENCEINFO, (BYTE*)&s_tDefenceInfo, sizeof(T_DefenceInfoStruct));

    //SaveGyroInfo(); // 保存当前电池位置信息作为位置基准(D121暂时不支持陀螺仪)

    if (len == 0) {
        // handle error, for example, log the error
        return FAILURE;
    }
    
    return SUCCESSFUL;
}
/***************************************************************************
 * @brief    判断布防条件
 **************************************************************************/
WORD IsDefenseCondition(void)
{
    // 布防完成情况下，手动布防不操作
    if(GetDefenceStatus())
    {
        return RTN_FAIL_ALREADY_DEFENCE;
    }
    // 布防失败，当前是apptest测试模式
    if(GetApptestFlag())
    {
        return RTN_FAIL_DEFENCE_APPTEST;
    }

    // 布防失败，当前是qtp测试模式
    if(GetQtptestFlag())
    {
        return RTN_FAIL_DEFENCE_QTP;
    }

    GetSysPara(&s_tSysPara);
    //电池丢失告警级别和软件防盗延时同时不满足
    if (s_tSysPara.aucAlarmLevel[BATT_LOSE_ALM_INDEX] == 0 && s_tSysPara.wSoftAntiTheftDelay == 0)
    {
        return RTN_FAIL_DEFENCE_LEVEL_PARA;
    }
    // 软件防盗延时
    if (s_tSysPara.wSoftAntiTheftDelay == 0)
    {
        return RTN_FAIL_DEFENCE_PARA;
    }
    //电池丢失告警级别
    if (s_tSysPara.aucAlarmLevel[BATT_LOSE_ALM_INDEX] == 0)
    {
        return RTN_FAIL_DEFENCE_LEVEL;
    }
    //PMOS关闭
    if (GPIO_OFF == GetPMOSStatus())
    {
        return RTN_FAIL_COMMAND;
    }

    return RTN_SATISFY_DEFENCE;
}

/***************************************************************************
 * @brief    是否存在防盗类告警(设备级、站点级、网管级)
 * @return   True-存在 False-不存在
 **************************************************************************/
BOOLEAN IsExistLostAlarm(T_BCMAlarmStruct *ptBcmAlarm)
{
    if (ptBcmAlarm->ucBattLoseAlm == NORMAL)
    {
        return False;
    }
    return True;
}

/***************************************************************************
 * @brief    获取站点防盗布防状态(0:未布防，1:已布防)
 **************************************************************************/
BOOLEAN GetSiteAntiTheftStatus(void)
{
    return False;
}

Static BYTE AlarmIndicatorStatus(BYTE IndicatorValue)
{
    if (IndicatorValue <= 3) 
    {
        return s_ucAlarmStatusMap[IndicatorValue];
    } 
    else 
    {
        // 其他值不控制告警告警灯
        return ALM_NORMAL;
    }
}