#ifndef PROTOCOL_V25_H_
#define PROTOCOL_V25_H_

#ifdef __cplusplus
extern "C" {
#endif

/************************   CID2码   ***********************/
#define GET_ANALOG_V25          0x42     // 获取模拟量量化后的数据（定点数）
#define GET_ALARM_V25           0x44     // 获取告警量
#define GET_PARA_V25            0x47     // 获取系统参数（定点数）
#define SET_PARA_V25            0x49     // 设置系统参数（定点数）
#define GET_PROTOCOL_VER_V25    0x4F     // 获取通信协议版本号(不支持)
#define GET_FACTORY_INFO_V25    0x51     // 获取设备厂家信息
#define GET_SPECIAL_PARA_V25    0x80     // 获取特定参数(定点数)
#define SET_SPECIAL_PARA_V25    0x81     // 设置特定参数(定点数)
#define GET_PACK_PARA_V25       0x90     // 获取PACK数量(回复RTN = 04H)
#define SET_BAUD_RATE_V25       0x91     // 设定通信速率(不支持，不回复)
#define CHARGE_CURRENT_CTRL_V25 0x98     // 充电电流控制命令（不支持）
#define REMOTE_CTRL_V25         0x99     // 遥控（仅支持防盗功能）

#define GET_DIGITAL_NEW_V25     0x95     // 获取电池状态量（扩展命令）
#define GET_ANALOG_NEW_V25      0x96     // 获取电池模拟量（扩展命令）
#define GET_ALARM_NEW_V25       0x97     // 获取电池告警量（扩展命令）

/*******************   遥控 Command Type   *****************/
#define MANUAL_DEFENCE_V25             0x01     // 人工布防
#define MANUAL_WITHDRAW_DEFENCE_V25    0x02     // 人工撤防
#define MANUAL_UNLOCK_V25              0x03     // 人工解锁

#include "protocol.h"
void DealCommand_v25( BYTE   ucPort );
extern SHORT  FloatChangeToModbus(FLOAT fData);
extern SHORT Host2Modbus(SHORT *wTemp);

#ifdef __cplusplus
}
#endif
#endif