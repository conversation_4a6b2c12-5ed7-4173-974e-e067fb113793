#include "north_main_can.h"
#include "data_type.h"
#include "utils_thread.h"
#include "utils_server.h"
#include "msg_id.h"
#include "utils_rtthread_security_func.h"
#include "dev_csu_comm.h"
#include "upload_manage.h"
#include "pin_define.h"

Static north_mgr_t *init_thread_data(link_inst_t* link_inst, unsigned char mod_id);

static server_info_t *s_server_info = NULL;

static msg_map s_north_msg_map[] = { 0 };


static dev_inst_t* s_dev_csu_can = NULL;


void* init_north_can(void* param)
{
    north_mgr_t* north_mgr_can = NULL;

    RETURN_VAL_IF_FAIL(param != NULL, NULL);

    s_server_info = (server_info_t *)param;
    s_server_info->server.server.map_size = sizeof(s_north_msg_map) / sizeof(msg_map);
    register_server_msg_map(s_north_msg_map, s_server_info);

    s_dev_csu_can = init_dev_inst(DEV_CSU_CAN);
    RETURN_VAL_IF_FAIL(s_dev_csu_can != NULL, NULL);
    north_mgr_can = init_thread_data(s_dev_csu_can->dev_type->link_inst, MOD_TOV_NORTH_CAN);
    upload_manage_init();
    load_manu_info();
    return north_mgr_can;
}



Static north_mgr_t *init_thread_data(link_inst_t* link_inst, unsigned char mod_id)
{
    north_mgr_t* north_mgr_can = NULL;

    north_mgr_can = rt_malloc(sizeof(north_mgr_t));
    RETURN_VAL_IF_FAIL(north_mgr_can != NULL, NULL);
    rt_memset_s(north_mgr_can, sizeof(north_mgr_t), 0x00, sizeof(north_mgr_t));

    north_mgr_can->cmd_buff = rt_malloc(sizeof(cmd_buf_t));
    if (north_mgr_can->cmd_buff == NULL)
    {
        free(north_mgr_can);
        return NULL;
    }
    rt_memset_s(north_mgr_can->cmd_buff, sizeof(cmd_buf_t), 0x00, sizeof(cmd_buf_t));

    north_mgr_can->link_inst = link_inst;
    return north_mgr_can;
}



void north_main_can(void* param)
{
    north_mgr_t* north_mgr = (north_mgr_t*)param;
    unsigned char time_delay = 20;
    while (is_running(TRUE))
    {
        if (RT_EOK == rt_sem_take(&(north_mgr->link_inst->rx_sem), THREAD_TICK))
        {
            if (FAILURE == linker_recv(s_dev_csu_can, north_mgr->cmd_buff)) {
                continue;
            }
            if (SUCCESSFUL == protocol_parse_recv(s_dev_csu_can, north_mgr->cmd_buff))
            {
                cmd_send(s_dev_csu_can, north_mgr->cmd_buff);
            }
            continue;
        }
        rt_thread_mdelay(time_delay);
    }
}

