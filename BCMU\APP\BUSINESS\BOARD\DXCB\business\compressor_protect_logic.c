#include "compressor_protect_logic.h"
#include "para_id_in.h"
#include "realdata_id_in.h"
#include "para_manage.h"
#include "realdata_save.h"
#include "msg_id.h"
#include "softbus.h"
#include "product_main.h"
#include "utils_math.h"
#include "alarm_mgr_api.h"
#include "south_dev_common.h"
#include "sps.h"
#include "eev_ctrl.h"
#include "utils_time.h"

static rt_tick_t s_vfd_recov_alm_time[MAX_VFD_NUM] = {0};

int compressor_protect_logic_valid(unsigned short compressor_num, unsigned short compressor_brand)
{
    unsigned short compressor_protect_code = 0;
    static unsigned short s_compressor_protect_code[MAX_VFD_NUM] = {0};
    unsigned short compressor_value = (compressor_brand == VFD_DFS_BRAND) ? 5 : 0;
    unsigned char run_status = 0;
    for(int loop = 0; loop < compressor_num; loop ++)
    {
        get_one_data(DXCB_DATA_ID_COMPRESSOR_PROTECT_CODE + loop, &compressor_protect_code);
        if(s_compressor_protect_code[loop] == 0 && compressor_protect_code != 0)    // 告警从无到有
        {
            set_do_value(get_do_index_by_vfd_no(loop), 1);
            set_do_value(DO_11, 1);  //散热风扇
            set_one_data(DXCB_DATA_ID_VFD_COMPRESSOR_RUN_STATUS + loop, &run_status);

            pub_msg_to_sps_cmd(NORTH_CTRL_SOUTH_VFD_MSG, loop + 1, get_set_vfd_start_stop_cmd_id_by_brand(compressor_brand, loop, compressor_value));
            if(RT_EOK != rt_sem_take(get_rtn_sem(), 2000))
            {
                rt_kprintf("compressor_protect_logic_valid|south timeout\n");
            }
            else
            {
                rt_kprintf("compressor_protect_logic_valid|dev_addr:%d, brand:%d, stop compressor\n", loop + 1, compressor_brand);
            } 
        }
        s_compressor_protect_code[loop] = compressor_protect_code;
    }
    return SUCCESSFUL;
}    

eDO_t get_do_index_by_vfd_no(int vfd_no)
{
    switch(vfd_no)
    {
        case 0:
            return DO_2;
        case 1:
            return DO_11;
        case 2:
            return DO_13;
        case 3:
            return DO_14;
        default:
            return DO_2;
    }
}

int compressor_protect_logic_delay_close(unsigned short compressor_num, unsigned short compressor_brand, unsigned short comm_status)
{
    unsigned short compressor_value = (compressor_brand == VFD_DFS_BRAND) ? 5 : 0;
    unsigned char run_status = 0;
    if(comm_status == 1)
    {
        return SUCCESSFUL;
    }
    for(int loop = 0; loop < compressor_num; loop ++)
    {
        set_do_value(get_do_index_by_vfd_no(loop), 1);
        set_do_value(DO_11, 1);  //散热风扇
        set_one_data(DXCB_DATA_ID_VFD_COMPRESSOR_RUN_STATUS + loop, &run_status);
        pub_msg_to_sps_cmd(NORTH_CTRL_SOUTH_VFD_MSG, loop + 1, get_set_vfd_start_stop_cmd_id_by_brand(compressor_brand, loop, compressor_value));
        if(RT_EOK != rt_sem_take(get_rtn_sem(), 2000))
        {
            rt_kprintf("compressor_protect_logic_delay_close|south timeout\n");
        }
        else
        {
            rt_kprintf("compressor_protect_logic_delay_close|dev_addr:%d, brand:%d, stop compressor\n", loop + 1, compressor_brand);
        } 
    }
    return SUCCESSFUL;
}



int compressor_protect_logic_keep_running(unsigned short compressor_num, unsigned short compressor_brand, char* flag)
{
    unsigned short compressor_protect_code = 0;
    unsigned short compressor_value = (compressor_brand == VFD_DFS_BRAND) ? 5 : 0;
    unsigned char run_status = 0;
    for(int loop = 0; loop < compressor_num; loop ++)
    {
        get_one_data(DXCB_DATA_ID_COMPRESSOR_PROTECT_CODE + loop, &compressor_protect_code);
        if(compressor_protect_code != 0 && !flag[loop])
        {
            flag[loop] = TRUE;
            set_do_value(get_do_index_by_vfd_no(loop), 1);
            set_do_value(DO_11, 1);  //散热风扇
            set_one_data(DXCB_DATA_ID_VFD_COMPRESSOR_RUN_STATUS + loop, &run_status);
            pub_msg_to_sps_cmd(NORTH_CTRL_SOUTH_VFD_MSG, loop + 1, get_set_vfd_start_stop_cmd_id_by_brand(compressor_brand, loop, compressor_value));
            if(RT_EOK != rt_sem_take(get_rtn_sem(), 2000))
            {
                rt_kprintf("compressor_protect_logic_keep_running|south timeout\n");
            }
            else
            {
                rt_kprintf("compressor_protect_logic_keep_running|dev_addr:%d, brand:%d, stop compressor\n", loop + 1, compressor_brand);
            } 
        }
    }
    return SUCCESSFUL;
}



int set_protect_alm_bits(unsigned short compressor_num)
{
    int alm_sids[] = {
        DXCB_ALARM_ID_EX_TEMP_HIGH,                                // 排气温度高告警
        DCXB_ALARM_ID_EVAPORAT_PRESSURE_LOW_ALARM,                 // 蒸发压力过低告警
        DCXB_ALARM_ID_VFD_CURR_HIGH_ALARM,                         // 变频器电流高告警
        DCXB_ALARM_ID_FREQ_CONVERTER_HIGH_TEMP,                    // 变频器温度高告警
        DCXB_ALARM_ID_EXHAUST_OVERHEAT_LOW_ALARM,                  // 排气过热度低告警
        DCXB_ALARM_ID_INHALE_TEMP_SENSOR_FAULT_ALARM,              // 吸气温度传感器故障
        DCXB_ALARM_ID_EXHAUST_TEMP_SENSOR_FAULT_ALARM,             // 排气温度传感器故障
        DCXB_ALARM_ID_HIGH_VOL_SWITCH_DISCONNECT,                  // 高压开关断开告警
        DCXB_ALARM_ID_CONDEN_PRESS_HIGH_ALARM,                     // 冷凝压力高告警
        DCXB_ALARM_ID_CONDEN_PRESS_SENSOR_FAULT_ALARM,             // 冷凝压力传感器故障
        DCXB_ALARM_ID_LOW_VOL_SWITCH_DISCONNECT,                   // 低压开关断开告警
        DCXB_ALARM_ID_EVA_PRESS_SENSOR_FAULT_ALARM,                // 蒸发压力传感器故障
        DXCB_ALARM_ID_INHALE_OVERHEAT_LOW_ALARM,                   // 吸气过热度低告警
        DCXB_ALARM_ID_FREQ_CONVERTER_ERROR,                        // 变频器故障告警
    };
    unsigned short vfd_protect_code[MAX_VFD_NUM] = {0};
    int bit_state = 0;
    int alm_id = 0;
    for(int i = 0; i < MAX_VFD_NUM; i++)
    {
        if(compressor_num == 0 || (i > compressor_num - 1))
        {
            vfd_protect_code[i] = 0;
        }
        else
        {
            for(int code_bit = EXHAUST_TEMP_HIGH_ALM_BIT; code_bit < MAX_PROTECT_ALM_TYPE; code_bit++)
            {
                alm_id = GET_ALM_ID(alm_sids[code_bit], i + 1, 1);  // 告警维度从1开始累加
                bit_state = get_realtime_alarm_value(alm_id);
                if(bit_state != 0)
                {
                    vfd_protect_code[i] |= (1 << code_bit);
                }
            }
        }
        set_one_data(DXCB_DATA_ID_COMPRESSOR_PROTECT_CODE + i, &vfd_protect_code[i]);
        record_compressor_protect_alarm_recover_time(i, vfd_protect_code[i]);
    }
    return SUCCESSFUL;
}

int record_compressor_protect_alarm_recover_time(int dev_index, unsigned short vfd_protect_code)
{
    static unsigned short s_vfd_protect_code[MAX_VFD_NUM] = {0};
    if(s_vfd_protect_code[dev_index] != 0 && vfd_protect_code == 0)   // 告警保护恢复
    {
        s_vfd_recov_alm_time[dev_index] = rt_tick_get();
        rt_kprintf("record_compressor_protect_alarm_recover_time|tick:%d\n",s_vfd_recov_alm_time[dev_index]);
        
    }
    s_vfd_protect_code[dev_index] = vfd_protect_code;
    return SUCCESSFUL;
}

char is_compressor_complete_recover(int dev_index)
{
    unsigned short recover_delay = 0;
    int diff_time = 0;

    get_one_para(DXCB_PARA_ID_DXCB_PROTECT_ALARM_RECOVER_DELAY_OFFSET, &recover_delay);
    diff_time = get_tick_difference(rt_tick_get(), s_vfd_recov_alm_time[dev_index]) / 1000;

    rt_kprintf("is_compressor_complete_recover|dev_index:%d, recover_delay:%d, diff_time:%d\n", dev_index, recover_delay, diff_time);
    if(diff_time >= recover_delay)
    {
        return TRUE;
    }
    return FALSE;
}


int compressor_protect_logic_deal(unsigned short compressor_num, unsigned short compressor_brand)
{
    unsigned short online_protect_logic = 0;
    unsigned short offline_protect_logic = 0;
    unsigned short north_comm_fail = 0;
    static unsigned short s_north_comm_fail = 0;
    static char s_flag[MAX_VFD_NUM] = {0};
    unsigned short compressor_protect_code = 0;
    set_protect_alm_bits(compressor_num);

    get_one_data(DCXB_TRACE_ID_NORTH_COMM_FAIL_ALARM, &north_comm_fail);
    if(north_comm_fail == 0)                                 // 通信正常         
    {
        get_one_para(DXCB_PARA_ID_NORMAL_COMM_PROTECT_STRATEGY_OFFSET, &online_protect_logic);
        if(online_protect_logic == 1)
        {
            compressor_protect_logic_valid(compressor_num, compressor_brand);
        }
    }
    else                                                    // 通讯断
    {
        get_one_para(DXCB_PARA_ID_OFFLINE_PROCESS_STRATEGY_OFFSET, &offline_protect_logic);
        if(offline_protect_logic == 0)
        {
            compressor_protect_logic_delay_close(compressor_num, compressor_brand, s_north_comm_fail);
        }
        else
        {
            compressor_protect_logic_keep_running(compressor_num, compressor_brand, s_flag);
        }
    }
    for(int loop = 0; loop < compressor_num; loop ++)
    {
        get_one_data(DXCB_DATA_ID_COMPRESSOR_PROTECT_CODE + loop, &compressor_protect_code);
        if(compressor_protect_code == 0)
        {
            s_flag[loop] = FALSE;
        }
    }
    s_north_comm_fail = north_comm_fail;
    return SUCCESSFUL;
}

int is_compressor_protect_valid(int dev_index)
{
    //返回值为FAILURE ,表示 保护拦截，返回值为SUCCESSFUL，表示 保护不拦截
    unsigned short online_protect_logic = 0;
    unsigned short compressor_protect_code = 0;
    char is_recover = FALSE;
    get_one_para(DXCB_PARA_ID_NORMAL_COMM_PROTECT_STRATEGY_OFFSET, &online_protect_logic);
    if(online_protect_logic == 0)      // 通信正常，保护策略不生效
    {
        return SUCCESSFUL;
    }

    get_one_data(DXCB_DATA_ID_COMPRESSOR_PROTECT_CODE + dev_index, &compressor_protect_code);
    is_recover = is_compressor_complete_recover(dev_index);
    if(compressor_protect_code == 0 && is_recover)
    {
        return SUCCESSFUL;
    }
    rt_kprintf("cannot start compressor|dev_addr:%d, protect_code:%d, is_recover:%d\n", dev_index + 1, compressor_protect_code, is_recover);
    return FAILURE;
}

unsigned short get_compressor_start_or_stop_value_by_brand(unsigned short brand, unsigned short vfd_switch)
{
    if(brand == VFD_AMS_BRAND || brand == VFD_HC_BRAND)
    {
        return vfd_switch;
    }
    else if(brand == VFD_DFS_BRAND)
    {
        if(vfd_switch == COMPRESSOR_START_VALUE)
        {
            return 1;
        }
        else
        {
            return 5;
        }
    }
    return COMPRESSOR_STOP_VALUE;
}

int get_set_vfd_start_stop_cmd_id_by_brand(unsigned short brand, int dev_addr_index, unsigned short vfd_switch)
{
    if(brand == VFD_AMS_BRAND || brand == VFD_HC_BRAND)
    {
        vfd_switch = get_compressor_start_or_stop_value_by_brand(brand, vfd_switch);
        set_one_data(DXCB_DATA_ID_VFD_SWITCH + dev_addr_index, &vfd_switch);
        return AMS_HC_SET_SWITCH;
    }
    else if(brand == VFD_DFS_BRAND)
    {
        vfd_switch = get_compressor_start_or_stop_value_by_brand(brand, vfd_switch);
        set_one_data(DXCB_DATA_ID_VFD_SWITCH + dev_addr_index, &vfd_switch);
        return DFS_SET_SWITCH;
    }
    return FAILURE;
}
