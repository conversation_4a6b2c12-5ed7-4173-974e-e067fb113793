#ifndef _PV_INVERTER_HIS_RECORD_H
#define _PV_INVERTER_HIS_RECORD_H

#ifdef __cplusplus
extern "C" {
#endif

#include "his_data.h"
#include "msg.h"
#include "update_download_manage.h"
#include "type_define_in.h"
#include "para_manage.h"
//历史数据
#define MAX_HIS_DATA_NUM              60000
#define MIN_HIS_DATA_NUM              50000
#define MAX_HIS_DATA_NUM_PER_FILE     10000
#define HIS_DATA_FILE_NUM             ((MAX_HIS_DATA_NUM + MAX_HIS_DATA_NUM_PER_FILE -1) / MAX_HIS_DATA_NUM_PER_FILE)
//实时告警
#define MAX_REAL_ALARM_NUM            300
#define MIN_REAL_ALARM_NUM            0
#define MAX_REAL_ALARM_NUM_PER_FILE   300
#define REAL_ALARM_FILE_NUM           0
//历史告警
#define MAX_HIS_ALARM_NUM             1200
#define MIN_HIS_ALARM_NUM             1000
#define MAX_HIS_ALARM_NUM_PER_FILE    200
#define HIS_ALARM_FILE_NUM             ((MAX_HIS_ALARM_NUM + MAX_HIS_ALARM_NUM_PER_FILE - 1) / MAX_HIS_ALARM_NUM_PER_FILE)
//历史事件
#define MAX_HIS_EVENT_NUM             500
#define MIN_HIS_EVENT_NUM             400
#define MAX_HIS_EVENT_NUM_PER_FILE    100
#define HIS_EVENT_FILE_NUM             ((MAX_HIS_EVENT_NUM + MAX_HIS_EVENT_NUM_PER_FILE - 1) / MAX_HIS_EVENT_NUM_PER_FILE)
//故障录波
#define MAX_RECORD_FAULT_NUM           160
#define MIN_RECORD_FAULT_NUM           144
#define MAX_RECORD_FAULT_NUM_PER_FILE  16
#define RECORD_FAULT_FILE_NUM          ((MAX_RECORD_FAULT_NUM + MAX_RECORD_FAULT_NUM_PER_FILE - 1) / MAX_RECORD_FAULT_NUM_PER_FILE)
#define WIDE_SCAN_FLAG                 0x61
#define THIN_SCAN_FLAG                 0x60

#define SAVE_IV_DATA_MAX_LEN       611 //300*2+11(最大300个测点,存储头占11个字节)
#define IV_PARA_NUM_INDEX          2
#define IV_DATA_INDEX              4
#define SAVE_IV_BLOCK_ID_INDEX          7
#define SAVE_IV_PRECISION_INDEX         8
#define SAVE_IV_DATA_NUM_INDEX          9
#define SAVE_IV_DATA_INDEX              11

#define CLIENT_KEY_DATA_LEN        2048     //密钥证书的数据长度
#define KEY_ENCODE_BUFF_LEN        3072     //密钥转码的数据长度
#define KEY_DECODE_BUFF_LEN        2048     //密钥解码的数据长度


#define MAX_OBSER_NUM              160      // 测点最大数量

#define MAX_SLAVE_NUM              10       // 最大从机数量

#define REALDATA_ID_TYPE   0
#define CTRL_ID_TYPE       1

#define DEL_EE_SINGLE_SIZE  512

#define CRITICAL_LEVEL      1     // 告警级别-严重

#define VER_START_INDEX        13
#define VER_END_INDEX          (33 - VER_START_INDEX)

// 故障录波
#define TRIG_ID_OFFSET      7    // 故障前录波条数的偏移
#define BEFORE_NUM_OFFSET   11    // 故障前录波条数的偏移
#define AFTER_NUM_OFFSET    17    // 故障后录波条数偏移
#define OBSER_NUM_OFFSET    23    // 测点数量偏移
#define RF_HEAD_SIZE        18    // 故障录波头中除了测点外的数据大小
#define RF_TIME_SIZE        7     // 故障录波存储时间戳大小

#define TRIG_ID_PAD         1
#define BEFORE_NUM_PAD      5     // 用于补零
#define AFTER_NUM_PAD       5     // 用于补零
#define OBSER_NUM_PAD       10    // 用于补零
// 故障录波的存储数量写在eeprom里面
typedef struct {
    unsigned char precision_flag;
    unsigned char storage_type;
    void (*pack_data)(unsigned char* buff, unsigned int* offset, char precision, void* data);
}his_data_pack_t;

#pragma pack(push, 1)
typedef struct {
    unsigned short  before_num;                 // 故障前录播条数
    unsigned int    before_time;                // 故障前录播时间间隔
    unsigned short  after_num;                  // 故障后录播条数
    unsigned int    after_time;                 // 故障后录播时间间隔
    unsigned short  obser_num;                  // 测点数量
    unsigned short  obsers_id[MAX_OBSER_NUM];   // 测点ID
}record_fault_head_info_t;


typedef struct{
    unsigned short trig_id;              // 故障触发ID---故障类型
    unsigned short record_flag;          // 录波标识ID---存文件0、1
    record_fault_head_info_t head_info;  // 头信息
}record_fault_info_t;

typedef struct{
    char* rf_data;              // 故障录波数据指向的地址
    int   rf_size;              // 故障录波数据的大小
}record_fault_msg_t;
#pragma pack(pop)

#pragma pack(1)
typedef struct
{
    unsigned short manu_id;          // 制造商ID
    unsigned short manu_address;     // 制造商地址
    unsigned int fir_start_time;     // 首次启动时间
    unsigned int rated_power;        // 额定功率
    unsigned int max_act_power;      // 最大有功功率
    unsigned int max_ap_power;       // 最大视在功率
    unsigned int net_4g_reset;       // 4G重启时间
    int max_react_power;             // 最大无功功率
    char machine_code[32];           // 整机条码
    char pro_mod[32];                // 产品型号
    unsigned short pro_num;          // 产品编号
    char fac_name[32];               // 厂家名称
    char dil_time[8];                // 出厂日期
    char manu_date[8];               // 生产日期
    char wifi_name[32];              //wifi名称
    char hardware_version[32];       //硬件版本
    unsigned short crc;
}fac_data_t;

#pragma pack()

typedef struct 
{
    int (*parse_val_handle)(void* buf);
}check_power_update_t;

short clean_his_alarm(void);
short clean_his_event(void);
short clean_his_data(void);
void init_his_record(void);
int save_pv_his_data();
int save_sample_data_to_his_data(unsigned char* buff);
int save_sample_data_to_his_data_with_time_stamp(unsigned char* buff);
void init_test_his_record(void);
int save_iv_data(unsigned char* buff);
void restore_factory();
int save_reset_data(unsigned char data);
unsigned char get_reset_data(void);
int save_reset_his_record(void);
void del_his_data_when_update();
void pack_int16s_data(unsigned char* buff, unsigned int* offset, char precision, void* data);
void pack_int32s_data(unsigned char* buff, unsigned int* offset, char precision, void* data);
int delete_all_ee_data();
int delete_ee_part_data(char* part_name, unsigned char* data, unsigned int offset, size_t size);
int parse_iv_data(unsigned char* buff, unsigned char* iv_data);
// 写操作记录接口
short save_real_ctrl_cmd_event(unsigned int id, unsigned char id_type, char* info);
int init_dir(void);
void del_save_iv_index();
int save_record_fault_file(record_fault_msg_t* record_fault_msg);
int check_all_record_fault_file();
void save_fac_data();
void init_fac_data_handle();
void write_event_handle(update_sta_msg_t* sta_msg);
void restore_pv_update_status(update_sta_msg_t* sta_msg);
void set_pv_update_status(unsigned char update_status);
void get_pv_update_status(unsigned char* update_status);
int handle_total_alarm_event();
unsigned short is_salve_comm_err_alm(int alm_id);
unsigned short is_all_slave_comm_err();
int para_file_export_handle();
int para_file_import_parse(char* para_msg);
int set_psc_final_type(unsigned short psc_final_type_s);
void clear_update_uuid();
int north_thread_beat_go_on();
int check_one_record_fault_file(unsigned file_size, char* file_name);
int binarySearch_offset(map_between_offset_id arr[], unsigned int len, unsigned int x);
int pack_hisdata_config_data(unsigned char** config_data, unsigned int* config_data_len);
int find_hisdata_config_data(unsigned int file_size, unsigned char* config_data, unsigned int new_config_data_len, unsigned char* config_data_number);
int get_his_data_saved_num(char* file_name, unsigned int file_size, unsigned char* read_his_data, unsigned int read_data_len);
int check_his_data_file();
int fault_pad_zero(part_data_t* part_data, int read_offset, int read_loop, unsigned int rf_data_size);
int all_fault_pad_zero(part_data_t* part_data, int read_offset, int read_loop);
char* get_client_key_buf();
char* get_client_other_key_buf();
int deal_client_key_msg(char* file_name);
int key_encrypt(char *in_buff, int data_len, char *file_path);
int key_decrypt(char *data_buff, unsigned int data_len,char *data_out_buff);
int decrypt_client_key_file(char* en_key_file, char* de_key_data);
int decrypt_all_client_key_file();
int init_his_data_info();
unsigned char  is_his_record_exist(void);
unsigned char is_energe_record_exist(void);
void delete_energy_file();
void delete_his_data_file();
void delete_his_alarm_file();
void delete_his_event_file();
void delete_fault_record_file();
void delete_other_file();
void save_version_event(unsigned short event_id_offset, char* info, unsigned char type);
int check_record_file_number(his_record_t* p_record, char* number_format);
short fill_event_record_callback(event_record_cb_param_t *event_record_cb_param);
void delete_iv_data();
#ifdef __cplusplus
}
#endif

#endif  // _PV_INVERTER_HIS_RECORD_H
