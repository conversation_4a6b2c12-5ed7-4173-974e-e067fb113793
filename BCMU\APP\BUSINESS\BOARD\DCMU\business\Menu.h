/**************************************************************************
 * 版权信息：（C）2008-2009，中兴通讯股份有限公司电源开发部版权所有
 * 系统名称：ZXDU88 S402 DCMU CORE板软件
 * 文件名称：menu.h
 * 文件说明：菜单模块头文件
 * 作    者：熊勇
 * 版本信息：V5.0
 * 设计日期：2008-08-01
 * 修改记录：
 * 日    期        版    本        修改人        修改摘要
 * 其他说明：
 ***************************************************************************/

#ifndef MENU_H
#define MENU_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "temp.h"
#include "gui_data_interface.h"

/***********************  常量定义  ************************/

#define CurCTRL 0
#define CurPage 1

#define KEY_PAINT 0x80
#define PAGE_ITEMS 3
#define PARAACTION_NUM 34

#define HISEVENT 0
#define HISBUFF 1

#define MAX_SYSPARA_OFFSET 11    // 系统参数帮助信息起始行数
#define MAX_ATTRIBURE_OFFSET 23 // 告警属性参数帮助信息起始行数
#define MAX_INRELAY_OFFSET 24    // 输入干接点参数帮助信息起始行数
#define MAX_DCPARA_OFFSET 25    // 直流参数帮助信息起始行数
#define MAX_ENVPARA_OFFSET 37    //环境参数帮助信息起始行数

// 窗口类型
#define WND_TYPE_DIALOG 0
#define WND_TYPE_PAGE 1
#define WND_TYPE_EDIT_LINE 2
#define WND_TYPE_EDIT_INT 3
// #define WND_TYPE_EDIT_FLOAT     4
#define WND_TYPE_OTHERS 5

// 消息定义
#define MSG_SRC_KEY 0
#define MSG_SRC_CHILD 1
#define MSG_SRC_PARENT 2

// 菜单宏定义
#define WND_MAIN 0
#define WND_DATA 1
#define WND_ALARM 2
#define WND_PASS 3

#define WND_SWITCH 4 // 切换串口
#define WND_HELP 5
#define WND_TRACE 6          // 调试窗口
#define WND_SAVE_SCREEN 7 // 屏保

#define WND_PARA 8         // 参数
#define WND_SYSTEMPARA 9 // 系统参数
#define WND_INRELAY_CFG 10
#define WND_ALMATRIBUTE 11
#define WND_DCPARA 12      // 直流参数
#define WND_ENVPARA 13      // 环境参数
#define WND_ADJUSTPARA 14 // 校准参数
#define WND_LOAD_ADJ 15      // 负载电流校准
#define WND_BAT_ADJ 16      // 电池电流校准
#define WND_ENV_ADJ 17      // 环境信息校准
#define WND_CONSOLE 18
#define WND_CTRL 19
#define WND_DELETE 20
#define WND_DOWNLOAD 21

#define WND_HISTORY 22
#define WND_REC_ALARM 23
#define WND_REC_ACTION 24
#define WND_REC_PEAK 25

#define WND_EDIT_LINE 26
#define WND_EDIT_INT 27
#define WND_LISTBOX 28
#define WND_MSGBOX 29
#define WND_CONFIRM 30
#define WND_ALERT 31

#define WND_EDIT_TIME 32
#define WND_PRI_ALARM 33
#define WND_EDIT_STR 34

#define WND_BAT_SETUP 35  // 电池配置窗口
#define WND_LOAD_SETUP 36 // 负载配置窗口
#define WND_LOAD_COMMPARA 37
#define WND_LOAD_ADJPARA 38

#define WND_QUERY 39
#define WND_TIME 40
#define WND_VERSION 41
#define WND_CTRL_RELAY 42
#define WND_EDIT_CMPCHAR 43
#define WND_LOADPOWER 44    // tcf
#define WND_DELLOADPOWER 45 // tcf
#define WND_EDIT_CMPSTR 46

#define WND_BAT_SHUNT 47
#define WND_LOAD_SHUNT 48

#define WND_IDLE 250
#define WND_FOCUS 253
#define WND_PARENT 254
#define WND_NULL 255
#define MSG_NULL 0

// 窗口对应字库起始行数

#define STR_WND_COMMON 0
#define STR_WND_MAIN 6
#define STR_WND_DATA 14
#define STR_WND_TIME 28
#define STR_WND_VER 31
#define STR_WND_SWITCH 36
#define STR_WND_CONSOLE 44
#define STR_WND_PARA 58
#define STR_WND_ALLPARA 66
#define STR_WND_LIST 108
#define STR_WND_ADJ_PARA 130
#define STR_ALMGRADE 145
#define STR_WND_HISTORY 149
#define STR_WND_SUPEROPE 158
#define STR_WND_CTRL 166
#define STR_WND_REC_ACT 177
#define STR_WND_PEAK 201
#define STR_CLASS_ALARM 207
#define STR_WND_HELP 241

#define MAX_ADJ_PARA_ENV 2
#define MAX_TRACE_PAGE 7       // by sun 12.7.17增加DCMU与CSU通讯1页收两页发调测信息
#define GENERAL_ADJ_PARA_SET 2 // 常规参数设置
#define EMB_ADJ_PARA_SET 1       // EMB参数设置
#define MAX_ADJ_PARA_SET (GENERAL_ADJ_PARA_SET + EMB_ADJ_PARA_SET)
#define MAX_HELP_MSG 30                  // 参数外帮助信息条数
#define WND_MAX (WND_EDIT_CMPSTR + 1) // 窗口总数
#define LEN_EDITCHAR 68
#define EDITDIR_INC 0     // 递增
#define EDITDIR_DEC 1     // 递减
#define COMM_PARA_SET 7    // 系统普通参数设置
#define SUPER_PARA_SET 5 // 系统超级参数设置 增加负载设置增加负载回路配置
#define SYSTEM_PARA_SET (COMM_PARA_SET + SUPER_PARA_SET)
#define DC_PARA_SET 12       // 9    //直流参数设置
#define ENV_PARA_SET 4       // EMB参数设置
#define MAX_ADJ_PARA_BAT 3 // 电池校准有电压、电流和温度三项
#define MAX_STRING_LEN 21  // 字符6*8 128/6=21，字符串应当增加截止符，所以为21个 add by wu 2009.4.7

/*********************  数据结构定义  **********************/
typedef struct WndStruct
{
    rt_uint8_t ucID;           // 本窗口ID
    rt_uint8_t ucIDParent;  // 父窗口ID
    rt_uint8_t ucIDChild;   // 子窗口ID
    rt_uint8_t bActive;       // 是否激活窗口
    rt_uint8_t bFocus;       // 是否焦点窗口
    rt_uint8_t ucType;       // 窗口类型(属性页、对话框、编辑控件、列表控件等)
    rt_uint8_t ucStyle;       // 窗口风格(正常、反显、闪烁、禁用等)
    rt_uint8_t ucMsgChild;  // 子窗口回传的消息，即反射消息
    rt_uint8_t ucMsgParent; // 父窗口传递的消息
    rt_uint8_t ucMsgKey;       // 键盘消息
    rt_uint8_t ucKeyAccel;  // 加速键定义
    rt_uint8_t bInit;       // 是否初始化
    rt_uint8_t bPartial;       // 是否部分窗口

    rt_uint16_t wTotalPage;   // 显示页总数
    rt_uint16_t wCurPage;       // 当前显示页
    rt_uint8_t ucTotalCtrl; // 对话框控件总数
    rt_uint8_t ucCurCtrl;   // 对话框当前焦点控件

    void (*p)(void); // 执行程序
} T_Wnd;

// 控件类型定义
typedef struct
{
    rt_uint8_t ucStyle;
    char pszText[40];
} T_Label;

// 控件类型定义
typedef struct
{
    char acText1[40];
    char acText2[40];
} T_AlertText;

typedef struct
{
    char *pucParaPtr;
    rt_uint8_t ucIndex;
    char acListStr[6][20];
} T_List;

typedef struct
{
    rt_uint8_t ucRange;
    rt_uint8_t x;
    rt_uint8_t y;
    char acEditStr[20];

    rt_uint8_t ucAttrib;
    int iMax;
    int iMin;
    short iData;
    char scDelt;
    char scPrecision;
    char *pcParaPtr;
    short *piParaPtr;
    char acParaStr[20];
} T_Edit;

/************************************************************
** 结构名: T_ParaWnd
** 描  述: 参数窗口属性结构定义
**************************************************************/
typedef struct
{
    rt_uint8_t ucID1; // 参数ID1
    rt_uint8_t ucID2; // 参数ID2
    rt_uint8_t ucParaIndex; // 参数设置序号
    rt_uint8_t ucWndAttrib; // 参数设置窗属性
} T_ParaWnd;

/**********************函数原型定义**********************************/
rt_uint8_t SetTraceStr(rt_uint8_t ucId, char *pcStr);
rt_uint8_t GetFocusWndID(void);
T_Wnd *GetFocusWnd(void);
T_Wnd *GetWnd(rt_uint8_t ucWndId);
void SetFocusWnd(rt_uint8_t ucWndId);
void PostParentItemMsg(rt_uint8_t ucIDItem);
void ProcessKey(void);
rt_uint8_t GetGUIMsg(rt_uint8_t ucSrcID);
unsigned char SaveCurrentPara(void);
void InitLabel(char *pszText, rt_uint8_t ucStyle);
void MessageBox(void);
void ConfirmDlg(void);
void AlertDlg(void);
void CharEdit(char *p, rt_uint8_t ucRange, rt_uint8_t ucEditDir);
void DisplayArea(float fPara, float fMax, float fMin, rt_uint8_t ucHeight);
void DisplayScroll(float fPara, float fMax, float fMin);
void InitListBox(char *pucParaPtr, rt_uint8_t ucTotal);
void AddListString(char *pcStr);
void GetListText(void);
void ListBoxCtrl(void);
void InitEditLine(rt_uint8_t ucRange, rt_uint8_t x, rt_uint8_t y, char *p, rt_uint8_t ucLength);
int GetEditLineText(char * buff, unsigned int buf_size);
void EditLineCtrl(void);
void InitEditInt(void);
char GetEditIntText(void);
void EditIntCtrl(void);
void DisplayRealData(rt_uint8_t ucMinorItem, rt_uint8_t ucDisDataStatus);
void RealDataDlg(void);
void VersionDlg(void);
void TraceDlg(void);
void DisplayRealAlarm(void);
void RealAlarmDlg(void);
void SwitchMenu(void);
void InRelayEditMenu(void);
void HelpDlg(void);
void SaveScreenDlg(void);
void PassDlg(void);
void ConsoleMenu(void);
rt_uint8_t GenHisRecordDlg(void);
rt_uint8_t GenParaRecordDlg(void);
void HisAlarmDlg(void);
void ClearStr(char *pStr);
void ScrollTextOut16(rt_uint8_t x, rt_uint8_t y, char *pStr);
void HisActionDlg(void);
void HisPeakDlg(void);
void HistoryMenu(void);
void StrEditMenu(void);
void AlmAttributeMenu(void);
void TimeEditMenu(void);
void InitParaItem(void);
void DisplayPara(rt_uint8_t ucPraType);
void SetupPara(rt_uint8_t ucParaType);
void ParaMenu(void);
void ParaMixMenu(void);
void AdjParaMenu(void);
void ControlMenu(void);
void DelLoadPowerMenu(void);
void DeleteDlg(void);
void DeleteLoadPowerDlg(void);
void LoadCommParaDlg(void);
void LoadAdjParaDlg(void);
void LoadParaDlg(void);
void DownloadDlg(void);
void DisplayWelcome(void);
void DeleteHisAction(void);
rt_uint8_t GetHisOperation(char *pDest, rt_uint8_t *bLastRecord);
void SetHisOper_SendFlag(rt_uint8_t bReSend);
void MainMenu(void);
void CtrlRelayDlg(void);
rt_uint16_t GetHisEventNum(void);
void SaveHisEventBuffIndex(void);
void QuaryDlg(void);
void InitPageItem(void);
rt_uint8_t GetOffset(rt_uint8_t ucWnd, rt_uint8_t ucOffset);
void GenCtrlDlg(rt_uint8_t ucMsg, rt_uint8_t ucType);
rt_uint8_t GenAdjParaRecordDlg(void);
void DisplayAdjPara(void);
void GetParaFromGUI(T_SysPara *tSysPara);
void DisplayAdjNum(void);
void AdjParaMixMenu(void);
void CheckHisEvent(void);
void DisplayPage(rt_uint8_t ucLen, rt_uint8_t ucType);
void GenInitPage(void);
rt_int16_t GetParaMax(rt_uint8_t ucID1, rt_uint8_t ucID2);
rt_int16_t GetParaMin(rt_uint8_t ucID1, rt_uint8_t ucID2);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif
