#include"eeprom_storage.h"

int eeprom_storage_write(uint8_t *buf, size_t size, char *PartitionName,unsigned char is_crc)
{
   int ret = RT_EOK,ret_crc = RT_EOK;
   unsigned short  write_crc = 0;
   struct fal_partition *part = (struct fal_partition *)fal_partition_find(PartitionName);
   RETURN_VAL_IF_FAIL(buf != RT_NULL, -RT_ERROR);
   RETURN_VAL_IF_FAIL(part != RT_NULL, -RT_ERROR);
   RETURN_VAL_IF_FAIL(size <= SINGLE_WEAR_ZONE_SIZE_MAX, -RT_ERROR);
   
   ret = wearLeve_eeprom_write(part, 0, buf, size);
   RETURN_VAL_IF_FAIL(ret == size, -RT_ERROR);
   if(is_crc)
   {
      write_crc = crc_cal((uint8_t *)buf, size);
      ret_crc = wearLeve_eeprom_write(part, size, (uint8_t *)&write_crc , CRC_CHECK_BYTE_LEN);
      //这个地方写可能因为越界，地址size后面没空间存储crc值，需要判断ret
      RETURN_VAL_IF_FAIL(ret_crc == CRC_CHECK_BYTE_LEN, -RT_ERROR);
   }
   return ret;
}

int eeprom_storage_read(uint8_t *buf, size_t size, char *PartitionName,unsigned char is_crc)
{
   int ret = RT_EOK,ret_crc = RT_EOK;
   unsigned short check_crc = 0, read_crc = 0;
   struct fal_partition *part = (struct fal_partition *)fal_partition_find(PartitionName);
   RETURN_VAL_IF_FAIL(buf != RT_NULL, -RT_ERROR);
   RETURN_VAL_IF_FAIL(part != RT_NULL, -RT_ERROR);
   RETURN_VAL_IF_FAIL(size <= SINGLE_WEAR_ZONE_SIZE_MAX, -RT_ERROR);

   ret = wearLeve_eeprom_read(part, 0, buf, size);
   RETURN_VAL_IF_FAIL(ret == size, -RT_ERROR);
   if(is_crc)
   {
      check_crc = crc_cal((uint8_t *)buf, size);
      ret_crc = wearLeve_eeprom_read(part, size, (uint8_t *)&read_crc, CRC_CHECK_BYTE_LEN);
      //这个地方读可能因为越界，地址size后面没空间存储crc值,需要判断ret
      RETURN_VAL_IF_FAIL(ret_crc == CRC_CHECK_BYTE_LEN, -RT_ERROR);
      //比较EEPROM里面储存crc16和计算的crc值是否一致
      RETURN_VAL_IF_FAIL(check_crc == read_crc, -RT_ERROR);
   }
   return ret;
}