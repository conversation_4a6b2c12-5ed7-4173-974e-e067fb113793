/**************************************************************************
* Copyright (C) 2001, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：E260 DXCB板软件
* 文件名称：product_main.h
* 文件说明：DXCB数据处理及业务逻辑处理文件
* 作    者：lby
* 版本信息：V1.0
* 设计日期：2025-07-21
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/
#ifndef _PRODUCT_MAIN_H
#define _PRODUCT_MAIN_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

#include "softbus.h"

/*
具体每台压缩机按照从低到高bit0~bit13，分别对应如下告警：
排气温度高告警、蒸发压力过低告警、变频器电流高告警、变频器温度高告警、
排气过热度低告警、吸气温度传感器故障、排气温度传感器故障、高压开关断开告警、冷凝压力高告警、冷凝压力传感器故障、
低压开关断开告警、蒸发压力传感器故障、吸气过热度低告警、变频器故障告警，
如果告警发生，则对应bit置1，否则对应bit置0.
*/ 

typedef enum
{
    EXHAUST_TEMP_HIGH_ALM_BIT = 0,              // 排气温度高告警
    EVAPORATION_PRESSURE_LOW_ALM_BIT,           // 蒸发压力过低告警
    FREQ_CONVER_CURRENT_HIGH_ALM_BIT,           // 变频器电流高告警
    FREQ_CONVER_TEMP_HIGH_ALM_BIT,              // 变频器温度高告警
    EXHAUST_SUPERHEAT_LOW_ALM_BIT,              // 排气过热度低告警
    INHALATION_TEM_SENSOR_FAULT_ALM_BIT,        // 吸气温度传感器故障
    EXHAUST_TEMP_SENSOR_FAULT_ALM_BIT,          // 排气温度传感器故障
    HIGH_VOL_SWITCH_DISCONN_ALM_BIT,            // 高压开关断开告警
    CONDEN_PRESSURE_HIGH_ALM_BIT,               // 冷凝压力高告警
    CONDEN_PRESSURE_SENSOR_FAULT_ALM_BIT,       // 冷凝压力传感器故障
    LOW_VOL_SWITCH_DISCONN_ALM_BIT,             // 低压开关断开告警
    EVAPORATION_PRESSURE_SENSOR_FAULT_ALM_BIT,  // 蒸发压力传感器故障
    SUCTION_SUPERHEAT_LOW_ALM_BIT,              // 吸气过热度低告警
    FREQ_CONVER_FAULT_ALM_BIT,                  // 变频器故障告警
    MAX_PROTECT_ALM_TYPE
}protect_alm_type_e;


void* init_product_main(void* param);
void handle_reset_dxcb(const rt_msg_t pMsg);
void product_main(void *parameter);
int ex_gas_temp_cal();

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  
