#ifndef SOFTWARE_SRC_APP_MASTEROPER_H_
#define SOFTWARE_SRC_APP_MASTEROPER_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "common.h"
#include "battery.h"


BOOLEAN SmartLi_IsPowerDown_Master(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattIn);
void MultBattMixPowerOnAndOffVolt_Master(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo, FLOAT *pfPowerDownVol, FLOAT *pfPowerOffVol);
void BMSChgButBDUDischg_Master(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo);



void CtrlChargeRotate_Master(T_RotateInfoStruct *ptRotate);
void DealSlaveRotateTimeout_Master(T_BattDealInfoStruct *ptBattDeal);
void DealChargeRotate_Master(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo);
void SmartLi_CalcDischargeOutputVolt_Master(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo);
void SmartLi_CalcSysMaxChargeCurr_Master(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo);
void startDischg_Master(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo);
void SetChgPara_Master(T_BattDealInfoStruct *ptBattDeal, FLOAT fChargeFullVol);
void GetPowerdownVolt_Master(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
#ifdef UNITEST
INT32 JudgeCAN2Powerdown(BOOLEAN* pbStatus, T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattIn);
#endif

#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // SOFTWARE_SRC_APP_SAMPLE_H_;
