/**
 * @file     sample_handle.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2023-05-22
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */

 
#ifndef _GRID_CODE_H
#define _GRID_CODE_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include <stdio.h>
#include <rtthread.h>
#include <rtdevice.h>
#include <string.h>
#include "data_type.h"
#include "dev_dc_ac.h"
#include "utils_server.h"
#include "para_struct_define_in.h"
#include "grid_code_para_in.h"

#define DEFAULT_GRID_CODE NB32004
#define GRID_PARA_NUM sizeof(grid_code_para) / sizeof(grid_code_para[0])
typedef struct {
    unsigned char data_type;
    int (*check_data)(void* para_data, u_value value);
}grid_check_data_t;

void grid_code_change(char value_is_set);
unsigned short get_grid_code_from_flash(void);
void update_grid_code_if_needed(char value_is_set, unsigned short* grid_code);
int is_grid_code_changed(unsigned short grid_code);
void update_scope_limits(grid_sys_para_t* grid_sys_para, numeric_para_attr_t* para_attr);
void update_parameter_value_if_needed(char value_is_set, unsigned short para_id_offset, grid_sys_para_t* grid_sys_para);
void process_grid_code_para(char value_is_set, unsigned short grid_code);
void finalize_grid_code_change();
int grid_code_status_check(void);
int check_one_data_eq(unsigned short para_id, u_value value);
int check_int_data_eq(void* para_data, u_value value);
int check_unsigned_int_data_eq(void* para_data, u_value value);
int check_short_data_eq(void* para_data, u_value value);
int check_unsigned_short_data_eq(void* para_data, u_value value);
int check_char_data_eq(void* para_data, u_value value);
int check_unsigned_char_data_eq(void* para_data, u_value value);
int check_float_data_eq(void* para_data, u_value value);
int emergy_auth_off();

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _SAMPLE_H