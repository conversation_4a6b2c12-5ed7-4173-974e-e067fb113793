
#include "cleaning.h"
#include "sample.h"
#include "dev_bmu.h"
// #include "dev_dc_dc.h"
#include "realdata_save.h"
#include "realdata_id_in.h"
static cluster_topology_t* s_topo;
static dev_inst_t* dev_BMU;
static dev_inst_t* dev_DC_DC;

void cleaning_init(cluster_topology_t* topo) {
    s_topo = (cluster_topology_t*)topo;
    dev_BMU = get_dev_inst(DEV_BMU);
    dev_DC_DC = get_dev_inst(DEV_DC_DC);
}

void calc_cluster_chg_temp_max_min(void* ana_data) {
    unsigned char i, j;
	unsigned int bmu_temp_max_addr_id = 0;
	unsigned int bmu_temp_min_addr_id = 0;
    float cell_temp_max = FP32_INVALID;
	float cell_temp_max_tmp = FP32_INVALID;
    float cell_temp_min = FP32_INVALID;
	float cell_temp_min_tmp = FP32_INVALID;

    if (dev_BMU == NULL)
        return;
    
    for (j = 0; j < s_topo->cluster_count; j++)  //4个簇
    {
        for (i = 0; i < s_topo->mod_num[j]; i++)   //表示每簇的电池数目
        {
			bmu_temp_max_addr_id = BCMU_DATA_ID_CELL_TEMP_MAX + s_topo->mod_addr[j][i] - 1;//需要将每簇电池模块地址ID写到临时地址变量bmu_addr_id中
			bmu_temp_min_addr_id = BCMU_DATA_ID_CELL_TEMP_MIN + s_topo->mod_addr[j][i] - 1;//需要将每簇电池模块地址ID写到临时地址变量bmu_addr_id中
			get_one_data(bmu_temp_max_addr_id,&cell_temp_max_tmp); //mod_num[j]表示的是每个簇下的BMU数循环，每次拿到本次循环的某个电芯温度最大值，放到临时变量cell_temp_max_tmp中
			get_one_data(bmu_temp_min_addr_id,&cell_temp_min_tmp); //mod_num[j]表示的是每个簇下的BMU数循环，每次拿到本次循环的某个电芯温度最小值，放到临时变量cell_temp_min_tmp中
			 if (if_float_equal(cell_temp_max, FP32_INVALID))	 
               cell_temp_max = cell_temp_max_tmp;  //比较，找最大值放到临时变量cell_temp_max中		   
            else if (cell_temp_max < cell_temp_max_tmp)   
                cell_temp_max = cell_temp_max_tmp; //比较，找最大值放到临时变量cell_temp_max中
            if (if_float_equal(cell_temp_min, FP32_INVALID))
                cell_temp_min = cell_temp_min_tmp; //比较，找最小值放到临时变量cell_temp_min中
            else if (cell_temp_min > cell_temp_min_tmp)   
               cell_temp_min = cell_temp_min_tmp; //比较，找最小值放到临时变量cell_temp_min中		
        }
        // set_one_data((BCMU_DATA_ID_DC_DC_CELL_TEMP_MAX + j),&cell_temp_max);   //得到每个簇的电芯最高温度   
		// set_one_data((BCMU_DATA_ID_DC_DC_CELL_TEMP_MIN + j),&cell_temp_min);   //得到每个簇的电芯最低温度  
    }  
}

