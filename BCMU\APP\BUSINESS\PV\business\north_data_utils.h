
#ifndef _NORTH_DATA_UTILS_H_
#define _NORTH_DATA_UTILS_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "parse_layer.h"
#include "sps.h"

#define PARALLEL_MASTER            1
#define PARALLEL_SLAVE             0
#define PARALLEL_VALID             1
#define PARALLEL_INVALID           0
#define MAX_PARALELL_NUM           10    ///< 并机个数，含主从机
#define PV_NUM                     10    ///< 逆变器最大并机数量
#define SERIAL_NUMBER_LEN          12    ///< 整机序列号长度
#define MAX_SERIAL_NUMBER_SIZE     64    ///< 整机序列号最大字节数

#define RTN_NULL_DATA_ERR        1     ///< set命令主机发送命令异常，或获取异常
#define RTN_ID_ERR               4     ///< 从机set时ID找不到

#define NORTH_MSG_SEM_TIMEOUT    10    ///< 北向接收消息超时时间, unit OS tick
#define LOCAL_MODBUS_MASTER      0     ///< 本机为MODBUS主机
#define LOCAL_MODBUS_SLAVE       1     ///< 本机为MODBUS从机
#define POLL_INTERVAL_COUNT      500   ///< 轮询间隔计数

#define PO_DEV_ON(dev, idx)          (dev |= 1 << (idx))     ///< 并机设备上线
#define IS_PO_DEV_ON(dev, idx)          ((dev >> (idx)) & 1)    ///< 对应地址-1的设备是否存在或在线
#define CANCEL_PO_DEV_ON(dev, idx)      (dev &= ~(1 << (idx)))  ///< 取消设备的在位状态
#define SN_LEN                          12

#pragma pack(1)
typedef struct {
    void*        data;
    cmd_buf_t*   cmd_buff;
    link_inst_t* link_inst;
    rt_mq_t      north_mq;
}north_mgr_t;

typedef struct {
    short valid_dev;
    short online_dev;
    char dev_sn[MAX_PARALELL_NUM][SERIAL_NUMBER_LEN];
} pv_parallel_status_t;

typedef struct{
    unsigned int alarm_ID;
    unsigned char alarm_value;
    unsigned char update_flag;
    long long alarm_time;
    // unsigned short crc;
} alarm_to_eeprom_t;

typedef enum {

    TYPE_ALAM = 0,
    TYPE_PARA,
    TYPE_ANA,
    TYPE_ALAM_PARA
}sid_type_e;

typedef struct{

    unsigned short      register_addr;                          //寄存器地址
    unsigned char       reserve_flag;                           //该寄存器值是否保留，1是保留
    char                old_type;                               //原始数据类型
    char                type;                                   //modbus传输数据类型
    unsigned char       precision;                              //modbus精度
    unsigned char       src_data_len;                           //南向原始数据长度
    unsigned char       data_len;                               //modbus上送数据长度
    sid_type_e          sid_type;                               //sid的类型
    unsigned int        data_addr;                              //数据取值地址
    unsigned int        south_cmd_id;                           //南向命令id
}modbus_addr_map_data_t;

typedef struct{
    unsigned char dev_addr;
    unsigned short alarm_num;
    unsigned int epprom_offset;
} parallel_alarm_msg_t;
#pragma pack()

void set_factory_special(unsigned char address);
pv_parallel_status_t* get_parallel_status();
short get_serial_number(unsigned char address, char* sn_buff, unsigned short buff_size) ;
void set_factory_special(unsigned char address);
void set_ver_data(unsigned int sid_date, unsigned int sid_year, unsigned int sid_month, unsigned int sid_day);
int update_slave_dev(unsigned char slave_idx, int status);
short set_serial_number(unsigned char address, unsigned char master_flag) ;
int update_slave_intr(unsigned char mst_addr);
void clear_slave_sn();
int get_master_addr(void);
short rs485_stat();
void set_parallel_status(pv_parallel_status_t* pv_parallel_status);
void get_device_id(int idx, char* id);
unsigned char get_addr_by_device_id(char* id);
void set_device_id(int idx, const char* id, int len);
int register_alarm_eeprom_info(alarm_to_eeprom_t* alarm_to_eeprom, int alarm_to_eeprom_num, 
                        alarm_to_eeprom_t* init_alarm_to_eeprom, int init_alarm_to_eeprom_num, modbus_addr_map_data_t* modbus_map_data);
int clear_parallel_alarm();
#ifdef __cplusplus
}
#endif      //< __cplusplus

#endif      //< _NORTH_DATA_UTILS_H_