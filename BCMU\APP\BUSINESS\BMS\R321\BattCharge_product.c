#include "common.h"
#include "battery.h"
#include "BattCharge_product.h"
#include "BattCharge.h"
#include "BattSOXCalc.h"
#include "MasterOper.h"
#include "SlaveOper.h"
#include "usart.h"
#include "CommCan.h"
#include "peakshift.h"
#include "fm.h"

#define INTERNAL_IMPEDANCE (0.01f)

static BOOLEAN IfChargeThrough(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
static BOOLEAN NotEnterChargeThroughVoltageCondition(T_BattInfo *pBattIn);
static void CalR321DischargeHopeVolt(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo);
static BOOLEAN PeakShiftVallyProcess(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal); //错峰谷处理
static BOOLEAN PeakShiftPeakProcess(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);  //错峰峰处理

SetChgParaOper_t SetChgParaOper;
BMSChgButBDUDischgOper_t BMSChgButBDUDischgOper;
SmartLi_CalcSysMaxChargeCurrOper_t SmartLi_CalcSysMaxChargeCurrOper;
MultBattMixPowerOnAndOffVolt_t MultBattMixPowerOnAndOffVolt;
SmartLi_CalDischargeOutputVoltOper_t SmartLi_CalDischargeOutputVoltOper;
GetPowerdownVoltOper_t GetPowerdownVoltOper;

/***************************************************************************
 * @brief    状态切换，进入在线非浮充时的初始化
 **************************************************************************/
void EnterStandbyInit(T_BattDealInfoStruct *pBattDeal)
{
    pBattDeal->ucBatStatus = BATT_MODE_STANDBY;
    pBattDeal->bSelfRechargeTimerEnable = False;
    pBattDeal->slSelfRechargeTimer = 0;
    pBattDeal->slStandByTimer = 0;
    pBattDeal->wBattFullHoldMinutes = 0; // 电池SOC置满计数初始化
    pBattDeal->slSelfDischgCalcTime = 0;
    pBattDeal->bChargeCurrMin = FALSE;
    pBattDeal->bChargeThroughEnable = True;
    pBattDeal->bInitMixDischg = False;
    return;
}

/***************************************************************************
 * @brief    电池自补电判断
 **************************************************************************/
BOOLEAN BattSelfRechargeJudgement(T_BattInfo *pBattIn)
{
    return (pBattIn->tData.fBatVol < pBattIn->tPara.fBattRefreshVoltThreshold && pBattIn->tData.fCellVoltMax < CELL_SUPPL_MAX_VOLT
    && pBattIn->tData.fCellVoltMin < pBattIn->tPara.fCellSupplVolt);
}

/***************************************************************************
 * @brief    判断充电是否进入直通
 **************************************************************************/
void JudgeChargeThrough(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    pBattDeal->bChargeThroughBak = pBattDeal->bThroughChg;

    if(pBattIn->tData.tHardWareStruct.ucCellVoltNum == R121_CELL_VOLT_NUM)
    {
        pBattDeal->bChargeThroughEnable = False;
        pBattDeal->bThroughChg = False;
        return;
    }

    pBattDeal->bThroughChg = (IfChargeThrough(pBattIn, pBattDeal)) ? True : False;
    return;
}

/***************************************************************************
 * @brief   充电直通进入条件
 **************************************************************************/
static BOOLEAN IfChargeThrough(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    // qtp模式充电直通
    if (GetQtptestFlag())
    {
        return True;
    }
    // 受控模式且北向通信正常，则充电为非直通状态，不管是否为电池特性放电模式
    if (RUN_MODE_CONTROLLED == pBattIn->tPara.ucRunMode &&
        !JudgeCommDisconnect() &&
        !IsProtoSetInfoInvalid(RUN_MODE_CONTROLLED))
    {
        return False;
    }
    // 如果不是电池特性模式，则充电非直通
    if ((BYTE)DISCHG_MODE_BATT_CHARACTERISTIC != pBattIn->tPara.ucUsageScen)
    {
        return False;
    }
    // 因上次判断不满足电压条件，本次充电过程不再进入直通
    if (!(pBattDeal->bChargeThroughEnable))
    {
        return False;
    }
    // 如果电池特性模式，并且出现电池欠压保护，则充电退出直通
    if (pBattDeal->bCellUnderVolt)
    {
        return False;
    }
    // 如果电池特性模式下不进入直通的电压条件
    if (NotEnterChargeThroughVoltageCondition(pBattIn))
    {
        // 使本次充电过程不再进入直通
        pBattDeal->bChargeThroughEnable = False;
        return False;
    }

    return True;
}

/***************************************************************************
 * @brief    判断不进入的直通的电压条件（电池电压、母排电压）
 **************************************************************************/
static BOOLEAN NotEnterChargeThroughVoltageCondition(T_BattInfo *pBattIn)
{
    return (((pBattIn->tData.fBatVol - INTERNAL_IMPEDANCE * fabs(pBattIn->tData.fBattCurr)) >=
                 (pBattIn->tPara.fChargeFullVol - 0.8f) ||
             pBattIn->tData.fBatVol > (pBattIn->tPara.fChargeFullVol - 0.2)) &&
            pBattIn->tData.fBusVol > pBattIn->tPara.fChargeFullVol);
}

/***************************************************************************
 * @brief    判断是否转充电
 **************************************************************************/
BOOLEAN IfShouldRecharge(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    WORD wSoc = CalcSOCFromCap(pBattDeal, pBattIn->tPara.wBattCap, 0);

    if (pBattDeal->slSelfRechargeTimer >= ONE_HOUR_MINUTES)
    {
        return True;
    }
    if (pBattDeal->slStandByTimer >= pBattIn->tPara.wBattRefreshPeriod * MINUTES_PER_DAY)
    {
        return True;
    }
    else if (pBattDeal->slStandByTimer >= MINUTES_PER_MONTH && wSoc <= 95)
    {
        return True;
    }

    return False;
}

/***************************************************************************
 * @brief    设置主机操作函数
 **************************************************************************/
void SetMasterOperFunc(void)
{
    SetChgParaOper = SetChgPara_Master;
    BMSChgButBDUDischgOper = BMSChgButBDUDischg_Master;
    MultBattMixPowerOnAndOffVolt = MultBattMixPowerOnAndOffVolt_Master;
    SmartLi_CalDischargeOutputVoltOper = CalR321DischargeHopeVolt;
    SmartLi_CalcSysMaxChargeCurrOper = SmartLi_CalcSysMaxChargeCurr_Master;
    GetPowerdownVoltOper = GetPowerdownVolt_Master;
    return;
}

/***************************************************************************
 * @brief    设置从机操作函数
 **************************************************************************/
void SetSlaveOperFunc(void)
{
    SetChgParaOper = SetChgPara_Slave;
    BMSChgButBDUDischgOper = BMSChgButBDUDischg_Slave;
    MultBattMixPowerOnAndOffVolt = MultBattMixPowerOnAndOffVolt_Slave;
    SmartLi_CalDischargeOutputVoltOper = CalR321DischargeHopeVolt;
    SmartLi_CalcSysMaxChargeCurrOper = SmartLi_CalcSysMaxChargeCurr_Slave;
    GetPowerdownVoltOper = GetPowerdownVolt_Slave;
    return;
}

/***************************************************************************
 * @brief    计算系统来电电压阈值、停电电压阈值、掉电电压阈值
 **************************************************************************/
void GetMixOnOffVolt(T_HardwareParaStruct *pHardwarePara, T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal, T_SysPara *pSysPara)
{
    FLOAT fPowerOnVol = 0.0f;
    FLOAT fPowerOffVol = 0.0f;
    FLOAT fPowerDownVol = 0.0f;
    // FLOAT fBattAvgVol = 0.0f;

    fPowerOnVol = pBattIn->tPara.fSysPowerOnThreshold;
    fPowerOffVol = pBattIn->tPara.fSysPowerOffThreshold;
    fPowerDownVol = pBattDeal->fPowerDownVolt;

    // fBattAvgVol = pBattDeal->fBatteryAverageVol;

    // if (pBattIn->tData.fSysVol > 53.8) // 母排电压大于53.8V，原因:16串的放电输出是53.7V
    // {
    //     TimerPlus(pBattDeal->wNeedCancelChgLimitTimer, PERSIST_MINUTES);
    // }
    // else
    // {
    //     pBattDeal->wNeedCancelChgLimitTimer = 0;
    // }

    // if (pBattDeal->bNeedLimitCurrCharge)
    // {
    //     if (pBattDeal->wNeedCancelChgLimitTimer >= PERSIST_MINUTES) // 持续时间超过5分钟
    //     {
    //         pBattDeal->bNeedLimitCurrCharge = FALSE;
    //     }
    // }

    if (pBattIn->tPara.ucRunMode == RUN_MODE_CONTROLLED && !JudgeCommDisconnect())
    {
        pBattIn->tPara.fSysPowerOnThreshold = 52.0f;
        // pBattDeal->fPowerDownVolt = 48.0f;
        if (pBattDeal->bCellUnderVolt)
        {
            pBattDeal->fPowerDownVolt = 43.0f;
        }
        return;
    }

    if (IfBduCharge() || BATT_MODE_STANDBY == pBattDeal->ucChargeMode || pBattDeal->ucChargeMode == BATT_MODE_CHARGE)
    {
        if (pBattDeal->wBusVoltNum > 0 && DISCHG_MODE_SELF_ADAPTION == pBattIn->tPara.ucUsageScen)
        {
            fPowerOnVol = pBattDeal->fBusVoltAvgForMix - MIX_POWER_ON_VOL_DIFF;
            fPowerOnVol = GetValidData(fPowerOnVol, MIX_POWER_ON_MAX, MIX_VOLT_MIN); // 增加上下限，避免无法转充电

            fPowerOffVol = pBattDeal->fBusVoltAvgForMix - MIX_POWER_OFF_VOL_DIFF;
            fPowerOffVol = GetValidData(fPowerOffVol, pBattDeal->fDischargeHopeVol - 0.1f, 47.0f);

            if(IsMaster())
            {
                fPowerDownVol = pBattDeal->fBusVoltAvgForMix - MIX_POWRE_DOWN_VOL_DIFF_CHG  - 0.003*fabs(pBattIn->tData.fBusCurr);
                fPowerDownVol = MIN(MIX_POWER_DOWN_MAX, fPowerDownVol); /// 掉电电压阈值上限修改为53.2,added by xzx 2021-12-28
                fPowerDownVol = GetValidData(fPowerDownVol, fPowerOnVol - MIX_POWRE_ON_DOWN_DIFF, MIX_VOLT_MIN);
            }
        }
        if (pBattDeal->wBusVoltNum > 0 && DISCHG_MODE_SOLAR == pBattIn->tPara.ucUsageScen)
        {
            fPowerOnVol = pBattDeal->fBusVoltAvgForMix - MIX_POWER_ON_VOL_DIFF;
            fPowerOnVol = GetValidData(fPowerOnVol, MIX_POWER_ON_MAX, MIX_VOLT_MIN); // 增加上下限，避免无法转充电

            fPowerOffVol = pBattDeal->fBusVoltAvgForMix - MIX_POWER_OFF_VOL_DIFF;
            fPowerOffVol = GetValidData(fPowerOffVol, pBattDeal->fDischargeHopeVol - 0.1f, 47.0f);

            if(IsMaster())
            {
                fPowerDownVol = pBattDeal->fBusVoltAvgForMix - MIX_POWRE_DOWN_VOL_DIFF_CHG - 0.5  - 0.003*fabs(pBattIn->tData.fBusCurr);
                fPowerDownVol = MIN(MIX_POWER_DOWN_MAX, fPowerDownVol); /// 掉电电压阈值上限修改为53.2,added by xzx 2021-12-28
                fPowerDownVol = GetValidData(fPowerDownVol, fPowerOnVol - MIX_POWRE_ON_DOWN_DIFF, MIX_VOLT_MIN);
            }
        }
        /*else if (pBattIn->tPara.ucDischargeMode == BATT_DISCHARGE_16FOLLOW &&
                 (BYTE)DISCHG_MODE_BATT_CHARACTERISTIC == pBattIn->tPara.ucUsageScen)
        {
            /// 模拟16串电池电压
            fPowerOnVol = 16.0f / pHardwarePara->ucCellVoltNum * fBattAvgVol + 0.5;
            fPowerOnVol = GetValidData(fPowerOnVol, 53.7f, 48.0f); // 增加上下限，避免无法转充电。3.5*16=56; 3.0*16=48.0

            fPowerOffVol = fPowerOnVol - 1.5f;
            fPowerDownVol = fPowerOffVol;
        }*/
        // else if (pBattIn->tPara.ucDischargeMode == BATT_DISCHARGE_FOLLOW &&
        //          (BYTE)DISCHG_MODE_BATT_CHARACTERISTIC == pBattIn->tPara.ucUsageScen)
        // {
        //     fPowerOnVol = fBattAvgVol + 0.5;
        //     fPowerOnVol = GetValidData(fPowerOnVol, 52.5f, 48.0f); // 增加上下限，避免无法转充电。3.5*15=52.5; 3.0*15=45

        //     fPowerOffVol = fPowerOnVol - 1.5f;
        //     fPowerDownVol = MIN(pBattIn->tData.fSysVol - 1, 53.0f); // 掉电电压 = MIN(母排电压-1V, 53V)
        //     fPowerDownVol = GetValidData(fPowerDownVol, 53.0f, fPowerOffVol);
        // }
        MultBattMixPowerOnAndOffVolt(pBattDeal, pBattIn, &fPowerDownVol, &fPowerOffVol);
    }
    // if (pBattDeal->bCellUnderVolt)
    // {
    //     fPowerDownVol = 43.0f;
    // }
    // else if (pBattDeal->bLoopOff)
    // {
    //     fPowerDownVol = MAX(45.0f, pBattIn->tData.fBatVol - 1.0f);
    // }

    pBattDeal->fPowerDownVolt = fPowerDownVol;

    pBattIn->tPara.fSysPowerOnThreshold = fPowerOnVol;
    pBattIn->tPara.fSysPowerOffThreshold = fPowerOffVol;
    return;
}

/***************************************************************************
 * @brief    计算R321的放电期望电流
 **************************************************************************/
static void CalR321DischargeHopeVolt(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo)
{
    if (DISCHG_MODE_CONSTANT_VOLTAGE == ptBattInfo->tPara.ucUsageScen)
	{
        ///////keep output lower than normal discharge voltage to prevent that one switch to discharge firstly and charge others.
		ptBattDeal->fDischargeHopeVol           = MAX(ptBattInfo->tPara.fRemoteSuplyVolt, OUT_VOLT_MIN);
	}
    else if ((BYTE)DISCHG_MODE_BATT_CHARACTERISTIC == ptBattInfo->tPara.ucUsageScen)
    {
        KeepOutputEqualBatteryVoltage();
    }
	else
	{
		ptBattDeal->fDischargeHopeVol           = GetMixOutputVolt(ptBattInfo, ptBattDeal);
	}
    return;
}

//充电时，错峰判断
BOOLEAN JudgePeakShiftCharge(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal, T_PeakShiftPara *pPeakShift)
{
    TimerPlus(pBattDeal->ucPeakDichgNum, PEAK_DISCHARGE_NUM);   ////来电后等母排稳定后才进入错峰放电
    if(JudgePeakShiftCondition(pBattIn, pBattDeal, pPeakShift))
    {
        if(100 - pBattIn->tData.wBattSOC > pPeakShift->ucPeakShiftDOD || (pBattDeal->bPkSftSlaveDisableDischgFlag && 100 - pBattIn->tData.wSlaveAvgSOC > pPeakShift->ucPeakShiftDOD)
           || pBattDeal->ucElectricityPrice == ELECTRICITY_PRICE_VALLEY)
        {
            pBattDeal->bPkSftSlaveDisableDischgFlag = False;
            PeakShiftVallyProcess(pBattIn,pBattDeal);
            return True;
        }
        else if((((100 - pBattIn->tData.wBattSOC == pPeakShift->ucPeakShiftDOD) || (pBattDeal->bPkSftSlaveDisableDischgFlag && 100 - pBattIn->tData.wSlaveAvgSOC == pPeakShift->ucPeakShiftDOD)) 
                && pBattDeal->ucElectricityPrice == ELECTRICITY_PRICE_PEAK)
                || pBattDeal->ucElectricityPrice == ELECTRICITY_PRICE_LEVEL) ////峰阶段等于放电深度、主机从机处于平阶段均禁止充电
        {
            pBattDeal->bChargeTimerEnable = False;
            pBattDeal->bChargeEndHold = False;
            pBattDeal->wBattChargeEndHoldSeconds = 0;
            pBattDeal->bPeakShiftChgDisable = True;
            setBMSMode(BATT_MODE_DISABLE, 0, 0);
            return False;
        }
        else if(pBattDeal->ucElectricityPrice == ELECTRICITY_PRICE_PEAK && pBattDeal->ucPeakDichgNum >= PEAK_DISCHARGE_NUM)
        {
            PeakShiftPeakProcess(pBattIn,pBattDeal);
            return False;          
        }
    }
    if(pBattDeal->bPeakShiftChgDisable == True && IsMaster()) ////不满足错峰条件或电价为空时退出充电禁止
    {
        pBattDeal->bPeakShiftChgDisable = False;
    }
    return True;
}

//错峰电价谷处理
static BOOLEAN PeakShiftVallyProcess(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    WORD wValleyTime = 0;
    FLOAT fValleyChargeCurr = 0.0f;

    if(pBattDeal->ucElectricityPrice == ELECTRICITY_PRICE_VALLEY)
    {
        wValleyTime = GetValleyTime();
        if(wValleyTime > 0)
        {
            fValleyChargeCurr = (pBattDeal->fBattSOH / 100 * pBattIn->tPara.wBattCap - pBattDeal->fBattCap) / ((FLOAT)wValleyTime / 60);
            fValleyChargeCurr = MIN(fValleyChargeCurr, pBattDeal->fChargeBusSetCurr);
            pBattDeal->fChargeBusSetCurr = GetValidData(fValleyChargeCurr, SMART_LI_RATE_CURRENT_CHG_BUS, SMART_LI_CURRENT_MIN);
            pBattDeal->bValleySetCurr = True;
        }
    }
    pBattDeal->bPeakShiftChgDisable = False;
    return True;
}


//错峰电价峰处理
static BOOLEAN PeakShiftPeakProcess(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if(DISCHG_MODE_BATT_CHARACTERISTIC == pBattIn->tPara.ucUsageScen || DISCHG_MODE_CONSTANT_VOLTAGE == pBattIn->tPara.ucUsageScen)
    {
        if(!pBattDeal->bPeakDischg)
        {
            pBattDeal->bPeakDischg = True;
            pBattDeal->fPeakDischgVolt = (pBattIn->tData.fBattCurr > 2.0f)? (pBattIn->tData.fSysVol + pBattIn->tData.fBattCurr * 0.01 + 1.0f) : (pBattIn->tData.fSysVol + 1.0f);
            pBattDeal->fPeakDischgVolt = GetValidData(pBattDeal->fPeakDischgVolt, SMARTLI_OUTPUT_VOL_MAX, 56.0f);
        }
        SetChargeMode(BATT_MODE_DISCHARGE);
        pBattDeal->bPeakShiftChgDisable = False;
        return False;
    }
    else
    {
        if(!pBattDeal->bPeakDischg)
        {
            pBattDeal->bPeakDischg = True;
            pBattDeal->fPeakDischgVolt = (pBattIn->tData.fBattCurr > 2.0f)? (pBattIn->tData.fSysVol + pBattIn->tData.fBattCurr * 0.01 + 0.5f) : (pBattIn->tData.fSysVol + 0.5f);
            pBattDeal->fPeakDischgVolt = GetValidData(pBattDeal->fPeakDischgVolt, SMARTLI_OUTPUT_VOL_MAX, 53.0f);
        }
        SetChargeMode(BATT_MODE_DISCHARGE);
        pBattDeal->bPeakShiftChgDisable = False;
        return False;
    }          
}

//调频错峰充电控制入口函数
BOOLEAN JudgeFmChargeSwit(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    FLOAT fMinVolt = 0.0f;
    FLOAT fAvePower = 0.0f;

    if(!IsMaster())
    {
        return False;
    }

    //停电后来电需要延时一段时间才能正常进行调频
    if(pBattDeal->bPeakShiftOff)
    {
        pBattDeal->bFmChgDisable = False;
        return False;
    }

    if(!pBattIn->tData.tFmData.ucFmStatus)
    {
        if(pBattDeal->ucLastFmSta)
        {
            pBattDeal->fFmMaxCurr = 0.0f;
            pBattDeal->bFmChgDisable = False;
            pBattDeal->bFmDischg = False;
        }
        setFmCtrl(False, False, 0.0f);
        return False;
    }

    pBattDeal->bFmChgDisable = False;
    if(pBattIn->tData.tFmData.fFmPower > 0.0f)
    {
        //控制充电功率
        fMinVolt = MIN(pBattIn->tData.fBatVol, pBattIn->tData.fSysVol);
        if(pBattIn->tData.ucFmNormalSlaveNum == 0)
        {
            return False;
        }
        fAvePower = pBattIn->tData.tFmData.fFmPower * 1000 / (pBattIn->tData.ucFmNormalSlaveNum);
        if(fMinVolt > 0.0f)
        {
            pBattDeal->fFmMaxCurr = fAvePower / fMinVolt;
        }
        setFmCtrl(False, False, fAvePower);
    }
    else if(pBattIn->tData.tFmData.fFmPower < 0.0f)
    {
        //控制放电功率
        if(!pBattDeal->bFmDischg)
        {
            pBattDeal->bFmDischg = True;
            pBattDeal->fFmDischgVolt = (pBattIn->tData.fBattCurr > 2.0f)? (pBattIn->tData.fSysVol + pBattIn->tData.fBattCurr * 0.01 + 1.0f) : (pBattIn->tData.fSysVol + 1.0f);
            pBattDeal->fFmDischgVolt = GetValidData(pBattDeal->fFmDischgVolt, SMARTLI_OUTPUT_VOL_MAX, MIN(SMART_LI_OUTPUT_VOL_MIN, PURE_LI_OUT_MIN));
        }
        SetChargeMode(BATT_MODE_DISCHARGE);
        return True;
    }
    else
    {
        //控制充电停止
        pBattDeal->bFmChgDisable = True;
        setFmCtrl(True, False, 0.0f);
    }

    return False;
}