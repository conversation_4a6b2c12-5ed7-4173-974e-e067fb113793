#include <string.h>
#include <rtthread.h>
#include <pthread.h>
#include "aes_interface.h"
#include "para_manage.h"
#include "utils_string.h"
#include "para_id_in.h"
#include "utils_rtthread_security_func.h"
#include "realdata_id_in.h"

int check_strsid_with_rules(unsigned int string_id, void* check_val, string_para_val_t* str_para_value)
{
    RETURN_VAL_IF_FAIL(check_val != RT_NULL, SUCCESSFUL);
    RETURN_VAL_IF_FAIL(str_para_value != RT_NULL, SUCCESSFUL);

    if (TOV_PARA_ID_MQTT_WORD_OFFSET == string_id)
    {
        uint16_t str_para_id = GET_SYS_PARA_OFFSET(TOV_PARA_ID_MQTT_USERNAME_OFFSET);
        return checkPassword(check_val, str_para_value[str_para_id].cur_val);
    }
    return SUCCESSFUL;
}

void judge_change_aes_key(void* str_para_save_val, unsigned char* aes_key_val)
{
    RETURN_IF_FAIL(str_para_save_val != RT_NULL);
    RETURN_IF_FAIL(aes_key_val != RT_NULL);

    string_para_save_t* str_para_val_aes = (string_para_save_t*)str_para_save_val;
    RETURN_IF_FAIL(str_para_val_aes != RT_NULL);

    uint16_t aes_key_id = 0;

    for(uint16_t i = 0; i < str_para_val_aes->para_num; ++i) {
        if (TOV_DATA_ID_SERIAL == str_para_val_aes->para_val[i].id) {
            aes_key_id = i;
            break;
        }
    }
    rt_snprintf_s((char*)aes_key_val, STR_LEN_16,  "key_%s", str_para_val_aes->para_val[aes_key_id].cur_val);
}

void init_aes_key(void* str_para_save_val, unsigned char* aes_key_val)
{
    judge_change_aes_key(str_para_save_val, aes_key_val);
}
