#include "common.h"
#include "led.h"
#include "sample.h"
#include "battery.h"
#include "realAlarm.h"
#include "BattSleep.h"
#include "para.h"
#include "hisdata.h"
#include "prtclDL.h"
#include "MultBattData.h"
#include "CommCan.h"
#include "BattSleepCtrl.h"
#include "commBdu.h"
#include <stddef.h>

#define CELL_SHUTDOWN_VOL (2.5f)
#define CELL_ICCLOSE_VOL (2.8f)

/* var */

Static T_SysPara s_tSysPara;   // 参数
Static Button_State s_tButSta; // 按键
Static BOOLEAN s_bSleepStatus = False;
#ifdef ACTIVATE_PORT_ENABLED
Static BOOLEAN s_bLoopSleep = False;                //回路异常进入预关机
#endif
Static BYTE s_ucSleepMgrCase = 0;
Static T_SlavesRealData s_tSlavesRealData[SLAVE_NUM]; // 主从机实时数据
Static BOOLEAN s_bCanSleepOrShutDown = FALSE; // 静置条件满足标志位，是否可以进行静置关机或者静置休眠
Static BYTE s_ucChangeTimeCnt = 0;
static BOOLEAN s_bPlusOneOutVol = False;   //提升母排电压1V30s标志位
static T_BattDealInfoStruct *s_tBattDeal = NULL;
Static BOOLEAN s_bBattUnderVoltPrtJudgeDelay = FALSE;//电池组欠压保护判断是否延时
static BOOLEAN s_bChgTempLowPrtOnly = FALSE;

/* func */
static void EnterSleep(void);
static void QuitSleep(void);
static BOOLEAN IfAnyBattSleep(void);
static BOOLEAN IfAnyBattDischarge(T_BattInfo *pBattIn);
static void MasterCtrlSlaveQuitSleep(T_BattInfo *pBattIn);
static void BattIdleJudge(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
static void OfflineManagement(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
static void MasterSlaveSleepManagement(T_BattInfo *pBattIn);
Static void OverDischargeProtectManagement(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
Static BOOLEAN IsChgTempLowAndHeaterOn(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
Static BOOLEAN IsChgTempLowAndHeaterEnable(T_BattInfo *pBattIn);
static BOOLEAN IsBattUvPrtSleep(T_BattDealInfoStruct *pBattDeal);
static BOOLEAN IsBattUvShouldEnterSleep(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
Static void BattUvEnterSleepAction(T_BattInfo *pBattIn,T_BattDealInfoStruct *pBattDeal);
static BOOLEAN IsBattQuitSleep(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal, Enum_Enter_Sleep_Reason enumSleepReason);
static BOOLEAN IsUvPrtSleepTimeOut(T_BattDealInfoStruct *pBattDeal);
static BOOLEAN IsLoopOffTimeOut(T_BattDealInfoStruct *pBattDeal);
static void ShutdownModeCtrl(void);
Static void SelfSleepOrShutdownCtrl(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
static BOOLEAN SelfSleepOrShutdownCondition(T_BCMAlarmStruct *pAlarm, T_BattDealInfoStruct *pBattDeal);
Static void SleepSwitchToShutdown(T_BattDealInfoStruct *pBattDeal);

static void UpdateAnalyticalAlm( void );
Static BOOLEAN UpdateAnalyticalAlmQuitSleep( void );

static void ShutdownManagement(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
static BOOLEAN IsBoard(T_BattInfo *pBattIn);
static BOOLEAN IsBattNeedShutdownCounter(T_BattInfo *pBattIn);
static BOOLEAN IsBattNeedShutdown(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal, BYTE counter);
static void SetUvPrtTimer(BYTE ucUvPrtTimer);
static void CheckEnterPreShutDown(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);

/* func def */

/***************************************************************************
 * @brief    电池休眠管理入口主函数
 **************************************************************************/
void SleepMgr(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    T_BCMAlarmStruct tBCMAlm;
    s_tBattDeal = pBattDeal;
    GetRealAlarm(BCM_ALARM, (BYTE *)&tBCMAlm);
    GetSysPara(&s_tSysPara);
    MasterSlaveSleepManagement(pBattIn);                // 主机控制从机退出静置休眠管理
    OverDischargeProtectManagement(pBattIn, pBattDeal); // 电池过放保护管理
    SelfSleepOrShutdownCtrl(pBattIn, pBattDeal);//静置管理
    GetButtonState(&s_tButSta);
    CheckEnterPreShutDown(pBattIn, pBattDeal);

    if (BATT_MODE_OFFLINE == pBattDeal->ucChargeMode)
    {
        OfflineManagement(pBattIn, pBattDeal); // 电池离线管理
        s_ucSleepMgrCase = SLEEP_MODE_OFFLINE;
        return;
    }

    if (ButtonSleepModeManagement(&s_tButSta, pBattDeal, &tBCMAlm) == True) // 按键休眠管理
    {
        s_ucSleepMgrCase = SLEEP_MODE_BUTTON;
        return;
    }

    if (IsUpdate())
    {
        s_ucSleepMgrCase = SLEEP_MODE_UPDATE;
        return;
    }

    if (pBattDeal->wBattIdleTimer >= BATT_IDLE_MUNITE_MAX)
    {
        BattIdleJudge(pBattIn, pBattDeal);
        s_ucSleepMgrCase = SLEEP_MODE_IDLE;
        return;
    }

    ShutdownManagement(pBattIn, pBattDeal);
    return;
}

BYTE GetSleepMgrCase(void)
{
    return s_ucSleepMgrCase;
}

/***************************************************************************
 * @brief    主机对从机的休眠状态管理
 **************************************************************************/
static void MasterSlaveSleepManagement(T_BattInfo *pBattIn)
{
    if (IsMaster())
    {
        getSlavesRealData(&s_tSlavesRealData[0]);
        MasterCtrlSlaveQuitSleep(pBattIn);
    }
    return;
}

/***************************************************************************
 * @brief    关机控制
 **************************************************************************/
static void ShutdownModeCtrl(void)
{
    if (s_tButSta.ucCurrentMode == BUT_SHUTDOWN)
    {
        SaveAction(GetActionId(CONTOL_SHUTDOWN), "ShutDownKey");
        CtrlSleep(s_tSysPara.bBuzzerEnable);
    }
    return;
}

/***************************************************************************
 * @brief    静置管理：和电池过放保护管理保持一样的逻辑
 **************************************************************************/
Static void SelfSleepOrShutdownCtrl(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if (s_bCanSleepOrShutDown == True)
    {
        //静置关机
        if (pBattIn->tData.fSysVol < SYS_WAKEUP_VOL )
        {
            SaveAction(GetActionId(CONTOL_SHUTDOWN), "StaticShutDown");
            CtrlSleep(s_tSysPara.bBuzzerEnable);
        }
        //静置休眠
        else if(s_tButSta.ucCurrentMode != BUT_SLEEP)
        {
            s_bSleepStatus = True;
            SaveAction(GetActionId(CONTOL_ENTER_QUIET_SLEEP), "StaticEnterSleep");
            //记录进入休眠之前的电池电压，之后退出静置休眠时需要使用。
            pBattDeal->fQuietSleepBattVolt = pBattIn->tData.fBatVol;
            //静置休眠会执行置休眠、关单体采样、关P-MOS三个操作
            EnterSleep();
        }
        //系统来电时退出静置休眠,退出静置休眠之前需要将欠压保护计数清零。
        if((s_tButSta.ucCurrentMode == BUT_SLEEP) && (IsBattQuitSleep(pBattIn, pBattDeal, QUIET_SLEEP) || pBattDeal->bActivateStart == TRUE) && pBattDeal->bActivatePortCurrError == FALSE)
        {
            pBattDeal->wUvPrtTimer = 0;
            QuitQuietSleep(SYS_POWER_ON);
        }
    }

#ifdef ACTIVATE_PORT_ENABLED
    if(s_bLoopSleep == TRUE)
    {
        if(fabs(pBattIn->tData.fBusVol - pBattIn->tData.fActivatePortVol) < 0.6)
        {
            pBattDeal->wLoopOffShutDownCounter = 0;
            s_bLoopSleep = FALSE;
            LoopOffRestore();
            QuitSleep();
            SaveAction(GetActionId(CONTOL_QUIT_QUIET_SLEEP), "LoopQuitSleep");
            SetChargeMode(BATT_MODE_DISCHARGE);
        }
    }
#endif
    return;
}

/***************************************************************************
 * @brief    休眠模式转关机
 **************************************************************************/
Static void SleepSwitchToShutdown(T_BattDealInfoStruct *pBattDeal)
{
    if ((GetTimeStamp() > pBattDeal->tOfflineTimeStamp + 14 * 3600))
    {
        // ③休眠模式下14小时关机
        if (s_tButSta.ucCurrentMode == BUT_SLEEP)
        {
            SaveAction(GetActionId(CONTOL_SHUTDOWN), "Sleep14ShutDown");
            CtrlSleep(s_tSysPara.bBuzzerEnable);
        }
    }
    return;
}

/***************************************************************************
 * @brief    休眠转关机准入条件
 **************************************************************************/
static BOOLEAN SelfSleepOrShutdownCondition(T_BCMAlarmStruct *pAlarm, T_BattDealInfoStruct *pBattDeal)
{
    return (IsExistLostAlarm(pAlarm) == False && pBattDeal->wUvPrtTimer == 0);
}

/***************************************************************************
 * @brief    离线管理
 **************************************************************************/
static void OfflineManagement(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    T_BCMAlarmStruct tBCMAlm;
    GetRealAlarm(BCM_ALARM, (BYTE *)&tBCMAlm);

    ShutdownModeCtrl();

    if (SelfSleepOrShutdownCondition(&tBCMAlm, pBattDeal))
    {
        /* 按键休眠模式下14小时关机 */
        SleepSwitchToShutdown(pBattDeal);
    }
    return;
}

/***************************************************************************
 * @brief    是否只存在充电低温保护且加热膜能够开启（加热膜在位且使能打开）
 **************************************************************************/
Static BOOLEAN IsChgTempLowAndHeaterEnable(T_BattInfo *pBattIn)
{
    /*
        如果只存在充电低温保护且加热膜能够开启，则应当可以退出休眠
        https://i.zte.com.cn/#/space/c1cc7a8c095a4d85a1cde8aa273b2fb6/wiki/page/1548aafb507647379ee1943da77f374b/view
    */
    T_DCFactory tDCFactory;
    GetBduFact(&tDCFactory);
    if(pBattIn->tData.bChgTempLowPrtOnly && s_tSysPara.bHeatingPadEnable && tDCFactory.bHeatFilm)
    {
        s_bChgTempLowPrtOnly = TRUE;
    }
    else
    {
        s_bChgTempLowPrtOnly = FALSE;
    }

    return s_bChgTempLowPrtOnly;
}

BOOLEAN getbChgTempLowPrtOnly(void)
{
    return s_bChgTempLowPrtOnly;
}

/***************************************************************************
 * @brief    防止电芯过放电管理
 **************************************************************************/
static void UnderVolPrtCount(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if (!pBattIn->tData.bBattLowVoltPrt || pBattDeal->bCellUnderVolt || pBattDeal->bActivateStart == TRUE)
    {
        pBattDeal->wUvPrtTimer = 0;
        return;
    }
    TimerPlus(pBattDeal->wUvPrtTimer,BATT_UV_SECOND_MIN);
    if(!Get_IC_Flag() && IsSleep() && pBattDeal->wUvPrtSleepFlag == TRUE)
    {
        TimerPlus(pBattDeal->sICOpenTimer,BATT_IC_OPEN_SECOND_MAX);
    }
    return;
}
Static void OverDischargeProtectManagement(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if (IsBoard(pBattIn))
    {
        return;
    }
    UnderVolPrtCount(pBattIn, pBattDeal);
    IsChgTempLowAndHeaterEnable(pBattIn);
    if (IsBattUvPrtSleep(pBattDeal) == TRUE && (!pBattIn->tData.bChargeProtect || s_bChgTempLowPrtOnly) && pBattDeal->bActivatePortCurrError == FALSE)
    {
        if (IsBattQuitSleep(pBattIn, pBattDeal, UV_PRT_SLEEP) == True || IsUvPrtSleepTimeOut(pBattDeal) == True || pBattDeal->bActivateStart == TRUE)
        {
            pBattDeal->wUvPrtTimer =  0;
            pBattDeal->sICOpenTimer = 0;
            QuitSleep();
            UpdateAnalyticalAlmQuitSleep();
            SaveAction(GetActionId(CONTOL_QUIT_QUIET_SLEEP), "UVPQuitSleep");
            SetChargeMode(BATT_MODE_DISCHARGE);
            pBattDeal->wUvPrtSleepFlag = FALSE ;          
        }
        return;  
    }
    /*进入休眠条件1、欠压休眠 
    如果已经处于休眠模式，不应该再进行控制进入休眠。
    并机场景下一台电池正常工作，另外一台静置休眠时会满足IsBattUvShouldEnterSleep条件，满足后记录的休眠前的电池组电压就为0了。*/
    if ((IsBattUvShouldEnterSleep(pBattIn, pBattDeal) == True) && (s_tButSta.ucCurrentMode != BUT_SLEEP) && (pBattDeal->bActivateStart == FALSE))
    {
        BattUvEnterSleepAction(pBattIn, pBattDeal);
    }
    return;
}

/***************************************************************************
 * @brief    是否退出休眠（满足欠压维持24小时)
 **************************************************************************/
static BOOLEAN IsUvPrtSleepTimeOut(T_BattDealInfoStruct *pBattDeal)
{
    return (TimeOut(pBattDeal->sICOpenTimer, BATT_IC_OPEN_SECOND_MAX));
}
/***************************************************************************
 * @brief    是否退出休眠(满足来电条件)
 **************************************************************************/
static BOOLEAN IsBattQuitSleep(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal, Enum_Enter_Sleep_Reason enumSleepReason)
{
    FLOAT fSleepBattVolt = 0;
    if(enumSleepReason == UV_PRT_SLEEP)
    {
        fSleepBattVolt = pBattDeal->fUvPrtSleepBattVolt;
    }
    else if(enumSleepReason == QUIET_SLEEP)
    {
        fSleepBattVolt = pBattDeal->fQuietSleepBattVolt;
    }

    if(IsMasterExist())
    {
        /* 1、主机下发来电报文  */
        return (!pBattDeal->bPowerOff);
    }

/*  1、电池特性场景下，来电判断为2倍的电池组电压+0.6 
    2、恒压输出模式下，来电判断为恒压输出+0.6 
    3、自适应放电模式下，来电判断为系统来电电压阈值
    4、光伏放电模式下，来电判断为系统来电电压阈值
*/
#if !defined(UNITEST)
    if ((DISCHG_MODE_BATT_CHARACTERISTIC  == pBattIn->tPara.ucUsageScen) && \
        (pBattIn->tData.fSysVol >= (SYS_WAKEUP_VOL_RATE*fSleepBattVolt + BATT_CHARGE_EXTRA_VOLT)) )
    {
        return TRUE;
    }
    else if ((DISCHG_MODE_CONSTANT_VOLTAGE  == pBattIn->tPara.ucUsageScen) && \
             (pBattIn->tData.fSysVol >= (pBattIn->tPara.fRemoteSuplyVolt + BATT_CHARGE_EXTRA_VOLT)) )
    {
        return TRUE; 
    }
    else if ((DISCHG_MODE_SELF_ADAPTION  == pBattIn->tPara.ucUsageScen || DISCHG_MODE_SOLAR  == pBattIn->tPara.ucUsageScen) && \
            (pBattIn->tData.fSysVol >= pBattIn->tPara.fSysPowerOnThreshold) )
    {
        return TRUE; 
    }
#endif

    return FALSE;
}

/***************************************************************************
 * @brief    电池是否已经由于达到欠压条件进入休眠
 **************************************************************************/
static BOOLEAN IsBattUvPrtSleep(T_BattDealInfoStruct *pBattDeal)
{
    return (BATT_MODE_OFFLINE == pBattDeal->ucChargeMode && pBattDeal->wUvPrtSleepFlag == TRUE);
}

/***************************************************************************
 * @brief    是否只存在充电低温保护且加热膜已开启(此时不进入休眠)
 **************************************************************************/
Static BOOLEAN IsChgTempLowAndHeaterOn(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    /*
        如果只存在充电低温保护且加热膜打开，则不进入休眠
        https://i.zte.com.cn/#/space/c1cc7a8c095a4d85a1cde8aa273b2fb6/wiki/page/1548aafb507647379ee1943da77f374b/view
    */
    T_DCRealData tDcRealData;
    GetBduReal(&tDcRealData);
    if(pBattIn->tData.bChgTempLowPrtOnly && tDcRealData.tDCStatus.bHeaterStatus)
    {
        pBattDeal->wUvPrtTimer = 0;
        return TRUE;
    }
    return FALSE;
}

/***************************************************************************
 * @brief    电池是否应该进入休眠(由于欠压)
 **************************************************************************/
static BOOLEAN IsBattUvShouldEnterSleep(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    /*
        电池组欠压保护或者电芯欠压保护或者电芯动态欠压保护以后，并且无检测电流，并且所有单节大于2.8V，持续1分钟，
        应该控制功率进入休眠模式（FB100B3是休眠+关PMOS），此时功率的MOS关断状态为是，
        监控部分关掉单节采样IC或者控制器进入低功耗模式，BMS主从机状态为从机
        https://i.zte.com.cn/#/space/3586b7cfbe304dc9b6b05c2c1f1c2b3c/wiki/page/a6fac137455f47b388867abf9450b974/view
    */
    IsChgTempLowAndHeaterOn(pBattIn, pBattDeal);
    return (pBattIn->tData.bBattLowVoltPrt && TimeOut(pBattDeal->wUvPrtTimer, BATT_UV_SECOND_MIN) &&
            !pBattDeal->bCellUnderVolt && !IsBduUpdate() && !GetQtptestFlag());
}
/***************************************************************************
 * @brief    进入休眠动作
 **************************************************************************/
static void EnterSleep(void)
{
        BduCtrl(SCI_CTRL_SLEEP , 1);
        Button_Mode_Set(BUT_SLEEP);
        SetChargeMode(BATT_MODE_OFFLINE);
        Open_IC_Onece(FALSE);
        SetPMOSStatus(FALSE);
}

static void QuitSleep(void)
{
        SetPMOSStatus(TRUE);
        Open_IC_Onece(TRUE);
        Button_Mode_Set(BUT_NORMAL);
        BduCtrl(SCI_CTRL_SLEEP , 0);

}
/***************************************************************************
 * @brief    判断进入休眠状态后的动作
 **************************************************************************/
Static void BattUvEnterSleepAction(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
        SaveTempHisData();
        SaveAction(GetActionId(CONTOL_LOW_POWER_CONSUMPTION_SLEEP), "UnderVolt Sleep");
        UpdateAnalyticalAlm();
        pBattDeal->wUvPrtTimer = 0;
        pBattDeal->wUvPrtSleepFlag = TRUE;
        pBattDeal->fUvPrtSleepBattVolt = pBattIn->tData.fBatVol;
        EnterSleep();

    return;
}

/***************************************************************************
 * @brief    返回是否需要提升输出电压1V标志位
 **************************************************************************/
BOOLEAN NeedPlusOneOutVol(void)
{
    return s_bPlusOneOutVol;
}

/***************************************************************************
 * @brief    电池静置判断，判断完成后进行静置管理(是该休眠还是关机)
 **************************************************************************/
static void BattIdleJudge(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if (pBattIn->tData.fSysVol >= SYS_WAKEUP_VOL )
    {
        if (!s_bPlusOneOutVol)
        {
            s_ucChangeTimeCnt = 0;
            s_bPlusOneOutVol = True;
        }
        if (s_ucChangeTimeCnt <= DISCHG_VOLT_CHANGE_TIME)
        {
            s_ucChangeTimeCnt++;
            if (pBattIn->tData.fCurrMinDet <= fabs(pBattIn->tData.fBattCurr)) // 若有电流输出则静置计时器清零
            {
                pBattDeal->wBattIdleTimer = 0;
                s_bPlusOneOutVol = False;
                s_ucChangeTimeCnt = 0;
                return;
            }
        }
    }
    else // 母排电压小于SYS_SHUTDOWN_VOL的情况下先进入到静置模式
    {
        s_bCanSleepOrShutDown = True;
        // 避免主机控制从机退出静置休眠后再次快速进入静置休眠状态
        pBattDeal->wBattIdleTimer = 0;
    }
    // 输出电压提升1V，持续30s没有电流输出则进入到静置模式
    if (s_ucChangeTimeCnt == (DISCHG_VOLT_CHANGE_TIME + 1))
    {
        s_bPlusOneOutVol = False;
        s_ucChangeTimeCnt = 0;
        s_bCanSleepOrShutDown = True;
        // 避免主机控制从机退出静置休眠后再次快速进入静置休眠状态
        pBattDeal->wBattIdleTimer = 0;
    }
    return;
}

/***************************************************************************
 * @brief    电池是否应该关机计时器判断条件
 **************************************************************************/
static BOOLEAN IsBattNeedShutdownCounter(T_BattInfo *pBattIn)
{
    return (pBattIn->tData.fCellVoltMin < CELL_SHUTDOWN_VOL && pBattIn->tData.fCellVoltMin > 1.0f &&
            pBattIn->tData.bBattLowVoltPrt && (!IsBduUpdate()) && (!GetQtptestFlag()));
}

/***************************************************************************
 * @brief    电池是否应该关机
 **************************************************************************/
static BOOLEAN IsBattNeedShutdown(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal, BYTE counter)
{
    /* 欠压保护1小时或回路异常持续1小时，则关机 */
    return (pBattIn->tData.fSysVol < SYS_WAKEUP_VOL && (counter >= 10 )) ;
}

/***************************************************************************
 * @brief    回路异常1小时
 **************************************************************************/
static BOOLEAN IsLoopOffTimeOut(T_BattDealInfoStruct *pBattDeal)
{
    return ((pBattDeal->wLoopOffShutDownCounter >= (WORD)LOOPOFF_SHUTDOWN_COUNTER_MAX));
}
/***************************************************************************
 * @brief    异常关机保存电芯统计记录
 **************************************************************************/
static void UpdateAnalyticalAlm( void )
{
    T_AnalyseInfoStruct tAnalyseInfo;
    rt_memset(&tAnalyseInfo, 0, offsetof(T_AnalyseInfoStruct,wCheckSum)+2);
    GetAnalyticalAlm( &tAnalyseInfo );
    tAnalyseInfo.bBattLowVolPrtTriggered = True;
    tAnalyseInfo.ucBattUVPShutDownFlag = SHUT_DOWN_FLAG;
    time(&tAnalyseInfo.tTime);
    TransAnalyticalAlm( &tAnalyseInfo, INFO_WRITTEN );
}

/***************************************************************************
 * @brief    欠压预关机（没有关机）重新激活后保存电芯统计记录
 **************************************************************************/
Static BOOLEAN UpdateAnalyticalAlmQuitSleep( void )
{
    T_AnalyseInfoStruct tAnalyseInfo;
    rt_memset(&tAnalyseInfo, 0, offsetof(T_AnalyseInfoStruct,wCheckSum)+2);
    GetAnalyticalAlm( &tAnalyseInfo );
    if(tAnalyseInfo.ucBattUVPShutDownFlag == SHUT_DOWN_FLAG)
    {
        time(&tAnalyseInfo.tTime2);
        tAnalyseInfo.ucBattUVPShutDownFlag = NO_FLAG;
        tAnalyseInfo.ulBattUVPShutDownTimer += (ULONG)(difftime(tAnalyseInfo.tTime2, tAnalyseInfo.tTime) / 60.0);
        tAnalyseInfo.tTime = (time_t)(-1);
        tAnalyseInfo.tTime2 = (time_t)(-1);
        TimerPlus(tAnalyseInfo.wBattUVPShutDownCounter, ALARM_RECORDS_MAX_COUNT);
        TransAnalyticalAlm( &tAnalyseInfo, INFO_WRITTEN );
        return True;
    }
    return False;
}

/***************************************************************************
 * @brief    单体电压1~2.5V关机管理以及回路异常关机管理
 **************************************************************************/
static void ShutdownManagement(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    static BYTE s_ucCellShutCnt = 0;
    if (IsBattNeedShutdownCounter(pBattIn))
    {
        if (s_ucCellShutCnt < 10)
        {
            s_ucCellShutCnt++;
        }
    }
    else
    {
        s_ucCellShutCnt = 0;
    }

    if (IsBattNeedShutdown(pBattIn, pBattDeal, s_ucCellShutCnt))
    {
        SaveTempHisData();
        SaveAction(GetActionId(CONTOL_SHUTDOWN), "UnderVolt ShutDown");
        UpdateAnalyticalAlm();
        EnterSleep();
        s_ucSleepMgrCase = SLEEP_MODE_SHUTDOWN_UVP;
    }

    if(IsLoopOffTimeOut(pBattDeal) == True && s_tButSta.ucCurrentMode != BUT_SLEEP)
    {
        #ifdef ACTIVATE_PORT_ENABLED
        T_DCFactory tBduFact;
        rt_memset(&tBduFact, 0, sizeof(T_DCFactory));
        GetBduFact(&tBduFact);

        if (tBduFact.bActivatePort == TRUE && pBattIn->tData.fActivatePortVol > 40.0f)   //不满足关机条件，进入静止休眠（预关机）
        {
            s_bLoopSleep = TRUE;
            SaveAction(GetActionId(CONTOL_ENTER_QUIET_SLEEP), "LoopOff Sleep");
            //静置休眠会执行置休眠、关单体采样、关P-MOS三个操作
            EnterSleep();
        }
        else          //不支持激活口或激活口电压小于40V，则关机
        {
            SaveAction(GetActionId(CONTOL_SHUTDOWN), "LoopOff ShutDown");
            EnterSleep();
        }
        #else
        SaveAction(GetActionId(CONTOL_SHUTDOWN), "LoopOff ShutDown");
        EnterSleep();
        #endif
        s_ucSleepMgrCase = SLEEP_MODE_SHUTDOWN_LOOPOFF;
    }
    return;
}

/***************************************************************************
 * @brief    存在不可逆告警则进入预关机状态
 **************************************************************************/
static void CheckEnterPreShutDown(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if(TimeOut(pBattDeal->ucIrreversibleAlarmCount, IRREVSB_ALARM_COUNT) && s_tButSta.ucCurrentMode != BUT_SLEEP && !IsBoard(pBattIn))
    {
        SaveAction(GetActionId(CONTOL_LOW_POWER_CONSUMPTION_SLEEP), "IRAlm PreDown");
        EnterSleep();
        s_ucSleepMgrCase = SLEEP_MODE_IRREVESB_ALARM;
    }
}

/***************************************************************************
 * @brief    获得从机休眠状态
 **************************************************************************/
BOOLEAN GetSlaveQuietSleepStatus()
{
    return s_bSleepStatus;
}

/***************************************************************************
 * @brief    主机控制从机退出休眠状态
 **************************************************************************/
static void MasterCtrlSlaveQuitSleep(T_BattInfo *pBattIn)
{
    /* 主机发现有从机处于静置休眠状态时，如果检测到有电池放电，那么控制进入到静置休眠状态的从机退出静置休眠 */
    if (IfAnyBattSleep() && IfAnyBattDischarge(pBattIn))
    {
        for (BYTE i = 0; i < SLAVE_NUM; i++)
        {
            if (s_tSlavesRealData[i].bQuietSleepSta)
            {
                ctrlBMSConsrv(i + 1, QUITQUIETSLEEP);
            }
        }
    }

    return;
}

/***************************************************************************
 * @brief    是否有电池处于休眠状态
 * @return   True---是  False---否
 **************************************************************************/
static BOOLEAN IfAnyBattSleep(void)
{
    for (BYTE i = 0; i < SLAVE_NUM; i++)
    {
        if (s_tSlavesRealData[i].bQuietSleepSta)
        {
            return True;
        }
    }
    return False;
}

/***************************************************************************
 * @brief    是否有电池处于放电状态
 **************************************************************************/
static BOOLEAN IfAnyBattDischarge(T_BattInfo *pBattIn)
{
    for (BYTE i = 0; i < SLAVE_NUM; i++)
    {
        /* 如果存在任意一台电池的放电电流小于最小放电电流，则认为存在电池放电的情况 */
        if (s_tSlavesRealData[i].fBatCur < 0 - pBattIn->tData.fCurrMinDet)
        {
            return True;
        }
    }
    return False;
}

/***************************************************************************
 * @brief    控制退出静置休眠
 **************************************************************************/
void QuitQuietSleep(Enum_Quit_Quiet_Reason quitReason)
{
    //静置休眠状态时才执行退出逻辑，避免主从机通讯从机静置休眠状态更新不及时导致操作记录保存多次。
    if(s_bCanSleepOrShutDown)
    {
        s_bSleepStatus = False;
        s_bCanSleepOrShutDown = False;
        //退出静置休眠需要置充电模式为放电、开单体采样、开PMOS。同时需要将欠压保护计数清零。
        SetUvPrtTimer(0);
        QuitSleep();
        if(quitReason == SYS_POWER_ON)
        {
            SaveAction(GetActionId(CONTOL_QUIT_QUIET_SLEEP), "SysPowerOn");
        }
        else if(quitReason == MASTER_CTRL)
        {
            SaveAction(GetActionId(CONTOL_QUIT_QUIET_SLEEP), "MasterCtrl");
        }
        SetChargeMode(BATT_MODE_DISCHARGE);
        s_bBattUnderVoltPrtJudgeDelay = True;
    }
}

/***************************************************************************
 * @brief    恢复调压标志
 **************************************************************************/
void RecovVoltChangeFlag(void)
{
    /* 恢复调压标志，避免再次进入静置状态时无法进行调压 */
    s_bPlusOneOutVol = False;
    s_ucChangeTimeCnt = 0;
}

/***************************************************************************
 * @brief    是否是单板判断
 **************************************************************************/
static BOOLEAN IsBoard(T_BattInfo *pBattIn)
{
    return (pBattIn->tData.fCellVoltMax <= 1.5f);
}

/***************************************************************************
 * @brief    欠压保护计数器清零
 **************************************************************************/
static void SetUvPrtTimer(BYTE ucUvPrtTimer)
{
    s_tBattDeal->wUvPrtTimer = ucUvPrtTimer;
}
