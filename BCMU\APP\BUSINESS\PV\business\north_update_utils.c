#include "north_update_utils.h"
#include "partition_def.h"
#include "storage.h"
#include "para_id_in.h"
#include "ee_public_info.h"
#include "utils_rtthread_security_func.h"
#include "utils_data_transmission.h"
#include "utils_time.h"
#include "para_manage.h"
#include "softbus.h"
#include "msg_id.h"
#include "cmd.h"
#include "his_record.h"
#include "reset.h"

file_manage_t file_manage;

static rt_timer_t g_mqtt_update_timer = NULL; 

unsigned char g_concurrent_addr[MAX_PARALELL_NUM] = {0};    // 合代码时优化

static char s_parallel_update_shield_comm_flag = FALSE;   // FALSE:不屏蔽， TRUE:屏蔽

static parallel_update_info_t s_parallel_update_info = {};

static south_update_func_t s_south_update_func = {};

int set_south_update_func(short (*start_south_update)(file_attr_t* , unsigned char , unsigned char , unsigned char ))
{
    s_south_update_func.start_south_update = start_south_update;
    return SUCCESSFUL;
}

parallel_update_info_t* get_parallel_update_info()
{
    return &s_parallel_update_info;
}

char get_parallel_update_shield_com_flag()
{
    return s_parallel_update_shield_comm_flag;
}

void set_parallel_update_shield_com_flag(char flag)
{
    s_parallel_update_shield_comm_flag = flag;
}

unsigned char* get_concurrent_addr()
{
    return &g_concurrent_addr[0];
}

rt_timer_t get_mqtt_update_timer()
{
    return g_mqtt_update_timer;
}

int set_mqtt_update_timer(rt_timer_t update_timer)
{
    g_mqtt_update_timer = update_timer;
    return SUCCESSFUL;
}

void fill_south_update_info()
{   
    update_file_manage_t update_file_manage = {0};
    read_download_tmpInfo(&update_file_manage);
    file_manage.file_attr.data_lenth_per_frame = MSC_MAX_PACKET_2;
    file_manage.file_attr.total_file_length = update_file_manage.file_info.total_leng;
    file_manage.file_attr.total_frame_num = ((update_file_manage.file_info.total_leng + MSC_MAX_PACKET_2 - 1) / MSC_MAX_PACKET_2) + 1;
    rt_strncpy_s(file_manage.file_attr.file_name, FILE_NAME_LEN, update_file_manage.file_info.file_name, FILE_NAME_LEN - 1);
    rt_strncpy_s(file_manage.file_attr.file_time, FILE_TIME_LEN, update_file_manage.file_info.file_time, FILE_TIME_LEN - 1);

    file_manage.file_attr.file_check = update_file_manage.file_info.filecrc;
    // rt_kprintf("fill_south_update_info|file_len:%d, total_frame_num:%d, file_name:%d, file_time；%d\n",
    //            g_download_file_size, file_manage.file_attr.total_frame_num, file_manage.file_attr.file_name, file_manage.file_attr.file_time,
    //            file_manage.file_attr.file_check);
}

int south_dev_update(unsigned char dev_id, unsigned char dev_addr, unsigned char dev_num) {
    int ret = 0;
    update_file_manage_t update_file_manage = {0};

    fill_south_update_info();
    ret = s_south_update_func.start_south_update(&file_manage.file_attr, dev_id, dev_addr, dev_num); 

    //传输结束后清除文件信息数据
    read_download_tmpInfo(&update_file_manage);
    rt_memset_s(&update_file_manage.file_info, sizeof(update_file_attr_t), 0, sizeof(update_file_attr_t));
    write_download_tmpInfo(&update_file_manage);// 完成标记,文件传输完成
    return ret;


}

void read_download_tmpInfo(update_file_manage_t* tFileManage) {
   part_data_t part_data = {0};

    if (NULL == tFileManage)
        return;
    
    part_data.buff = (unsigned char *)tFileManage;
    part_data.len = sizeof(update_file_manage_t);
    part_data.offset = 0;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), UPDATE_INFO_PART);
    if (SUCCESSFUL != storage_process(&part_data, read_opr)) {
        return ;
    }

    return ;
}

void write_download_tmpInfo(update_file_manage_t* tFileManage) {
    part_data_t part_data = {0};
    update_file_manage_t check_update_info = {0};

    if (NULL == tFileManage)
        return;
    tFileManage->crc = crc_cal((unsigned char *)tFileManage, sizeof(update_file_manage_t)-2);
    part_data.buff = (unsigned char *)tFileManage;
    part_data.len = sizeof(update_file_manage_t);
    part_data.offset = 0;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), UPDATE_INFO_PART);
    if (SUCCESSFUL != storage_process(&part_data, write_opr)) {
        return ;
    }
    //检验升级信息是否写成功
    read_download_tmpInfo(&check_update_info);
    if(check_update_info.crc != tFileManage->crc)
    {
        LOG_E("update Info error | write crc:%d read crc:%d",tFileManage->crc, check_update_info.crc);
    }
}

static void energy_save_restart(void)
{
    send_msg_to_thread(ENERGY_SAVE_RESTART, MOD_SAMPLE, NULL, 0);
    return;
}

void is_need_restart()
{
    update_file_manage_t tFileManage;
    rt_memset_s(&tFileManage, sizeof(update_file_manage_t), 0, sizeof(update_file_manage_t));
    read_download_tmpInfo(&tFileManage);
    LOG_E("is_need_restart | tFileManage.update_flag: %d", tFileManage.update_flag);
    if(tFileManage.update_flag == FLAG_APP_UPDATE_APP)
    {   
        save_reset_data(NO_RESET_CSU_UPGRADE);
        energy_save_restart();
        //删除历史数据并重启
        send_msg_to_thread(DEL_HIS_DATA_AND_REBOOT, MOD_SYS_MANAGE, NULL, 0);
    }
    return ;
}

int start_update_timer(unsigned int tm)
{
    rt_timer_stop(g_mqtt_update_timer);
    rt_timer_control(g_mqtt_update_timer , RT_TIMER_CTRL_SET_TIME ,&tm);
    rt_timer_start(g_mqtt_update_timer); 
    return SUCCESSFUL;
}

int deal_update(int file_type, unsigned int file_size, unsigned int file_crc)
{
    update_file_manage_t update_file_manage = {0};
    update_app_info_t update_info = {0};
    time_base_t ptime = {0};
    unsigned char master_addr = 0;
    int file_index = file_type;
    if(file_type == DIFF_FILE)
    {
        file_index -= 1;
    }
    unsigned char update_file_name[][FILE_NAME_LEN] = {
                                                            {"pv_app.bin"},
                                                            {"PV_INVERTER_MASTER.bin"},
                                                            {"PV_INVERTER_SLAVE.bin"},
                                                            {"PV_INVERTER_CPLD.bin"},
                                                            {"pv_diff.bin"},
                                                        };
    int type_max_num = sizeof(update_file_name)/sizeof(update_file_name[0]);
    RETURN_VAL_IF_FAIL((file_index < type_max_num && file_index >= 0), FAILURE);
    get_time(&ptime);
    read_download_tmpInfo(&update_file_manage);
    rt_memcpy_s(update_file_manage.file_info.file_name, FILE_NAME_LEN, update_file_name[file_index], FILE_NAME_LEN);
    rt_snprintf(update_file_manage.file_info.file_time , UPDATE_FILE_TIME_LEN, "%04d-%02d-%02d %02d:%02d:%02d",
                        ptime.year, ptime.month, ptime.day, ptime.hour, ptime.minute, ptime.second);
    update_file_manage.file_info.total_leng = file_size;
    update_file_manage.file_info.filecrc = file_crc;
    update_file_manage.update_status = TRANSFER_SUCCESS;
    get_one_para(DAC_COMMON_ID_RS485_COMMU_ADDR_OFFSET, &master_addr);
    handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char*)&update_info, sizeof(update_info), MQTT_UPDATE_INFO_OFFSET);
    update_info.is_enter_update[master_addr - 1] = TRUE;
    handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&update_info, sizeof(update_info), MQTT_UPDATE_INFO_OFFSET);

    if (file_type == MONITER_FILE || file_type == DIFF_FILE)//监控
    {
        update_file_manage.update_flag = FLAG_APP_UPDATE_APP;
        update_file_manage.sys_run_flag = FALSE;
        update_file_manage.backup_flag = FALSE;
        update_file_manage.count = 0;
        write_download_tmpInfo(&update_file_manage);
        is_need_restart();
    }
    else
    {
        write_download_tmpInfo(&update_file_manage);
        start_update_timer(SOUTH_UPDATE_WAITE);
        south_dev_update(DEV_DC_AC, 1, 1);
        rt_memset(&update_file_manage.file_info, 0, sizeof(update_file_attr_t));
        write_download_tmpInfo(&update_file_manage);
    }

    return SUCCESSFUL;
}

int deal_update_timer()
{
    rt_kprintf("deal_update_timer!!!!!!!!!!\n");
    set_parallel_update_shield_com_flag(FALSE);
    if(s_parallel_update_info.is_master_update)
    {
        rt_kprintf("start_next_device_update|update master\n");
        deal_update(s_parallel_update_info.file_type, s_parallel_update_info.file_size, s_parallel_update_info.file_crc);//有问题
    }
    if(s_parallel_update_info.file_type == MONITER_FILE || s_parallel_update_info.file_type == DIFF_FILE)
    {
        rt_kprintf("deal_update_timer  MONITER_FILE!!!!!!!!!!\n");
        start_update_timer(MONITER_UPDATE_WAITE);
    }
    else if(s_parallel_update_info.file_type <= CPLD_FILE)
    {
        rt_kprintf("deal_update_timer  CPLD_FILE!!!!!!!!!!\n");
        start_update_timer(SOUTH_UPDATE_WAITE);
    }
    return SUCCESSFUL;
}
