#include <rtthread.h>
#include <rtdevice.h>
#include "led.h"
#include "data_type.h"
#include "pin_define.h"
#include "msg_id.h"
#include "utils_server.h"

#define LED_MSG_SEM_TIMEOUT    10    ///< unit OS tick

static _rt_server_t s_curr_server = NULL;
static msg_map s_led_msg_map[] = {
    {LED_CTRL_MSG, msg_handle_nothing},
};

/* 定义信号灯对象句柄 */
static led_t* s_green_1 = NULL;        // 绿灯1
static led_t* s_green_2 = NULL;        // 绿灯2
static led_t* s_yellow = NULL;         // 黄灯
static led_t* s_red = NULL;            // 红灯

/* 设置信号灯一个周期内的闪烁模式 
 * 格式为 “亮、灭、亮、灭、亮、灭 …………”
 */
static char* s_periodic_flash_mode = "500,500,";         // 周期闪
static char* s_constantly_on_mode = "1000,0,";           // 常亮
static char* s_fast_flash_mode = "500,500,";             // 快闪
static char* s_slow_flash_mode = "1000,1000,";           // 慢闪
static char* s_interrupted_flash_mode = "1000,4000,";    // 间断闪
static char* s_super_fast_flash_mode = "100,100,";       // 超快闪
static char* s_constantly_off_mode = "0,1000,";          // 常灭


Static bcmu_led_status_t bcmu_led_status_table[] = {
    {MOD_BATT_MANAGE, RUNNING, GREEN1, PERIODIC_FLASH},
    {MOD_BATT_MANAGE, DISCHARGING, GREEN2, CONSTANTLY_ON},
    {MOD_BATT_MANAGE, CHARGING, GREEN2, FAST_FLASH},
    {MOD_BATT_MANAGE, STANDBY_BREAKER_CLOSED, GREEN2, SLOW_FLASH},
    {MOD_BATT_MANAGE, STANDBY_BREAKER_CAN_NOT_CLOSE, GREEN2, INTERRUPTED_FLASH},
    {MOD_BATT_MANAGE, STANDBY_BREAKER_CAN_MANUAL_CLOSE, GREEN2, SUPER_FAST_FLASH},
    {MOD_BATT_MANAGE, SHUTDOWN, GREEN2, CONSTANTLY_OFF},
    {MOD_ALARM_MANAGE, ALARM_NO_ACTION, YELLOW, CONSTANTLY_ON},
    {MOD_BATT_MANAGE, SHUTDOWN, YELLOW, CONSTANTLY_OFF},
    {MOD_ALARM_MANAGE, NO_ALARM, YELLOW, CONSTANTLY_OFF},
    {MOD_ALARM_MANAGE, ALARM_DO_ACTION, RED, CONSTANTLY_ON},
    {MOD_BATT_MANAGE, SHUTDOWN, RED, CONSTANTLY_OFF},
    {MOD_ALARM_MANAGE, NO_ALARM, RED, CONSTANTLY_OFF}
};


Static led_t* get_led_handle(bcmu_light_t light);
Static char* get_blink_mode(bcmu_blinking_t blinking);
Static int recv_msg_deal(void);


/*
 *@brief 根据module_id和status获取对应的light和blinking
 *@param[in] module_id 模块ID
 *@param[in] status 状态值，可能是bcmu_battery_status_t或bcmu_alarm_status_t
 *@retval SUCCESSFUL 获取成功
 *@retval FAILURE 获取失败
 */


int set_light_and_blinking(module_id_e module_id, int status) {
    bcmu_light_t light = (bcmu_light_t)0;
    bcmu_blinking_t blinking = (bcmu_blinking_t)0;
    led_t* led_handle = NULL;
    char* blink_mode = NULL;

    for (int i = 0; i < sizeof(bcmu_led_status_table) / sizeof(bcmu_led_status_t); i++) {
        if (bcmu_led_status_table[i].module_id == module_id && bcmu_led_status_table[i].status == status) {
            light = bcmu_led_status_table[i].light;
            blinking = bcmu_led_status_table[i].blinking;

            led_handle = get_led_handle(light);
            CONTINUE_IF_FAIL(led_handle != NULL);

            blink_mode = get_blink_mode(blinking);
            CONTINUE_IF_FAIL(blink_mode != NULL);

            led_set_mode(led_handle, LOOP_PERMANENT, blink_mode);
            led_start(led_handle);
        }
    }
    return SUCCESSFUL;
}


Static led_t* get_led_handle(bcmu_light_t light) {
    switch (light) {
    case GREEN1:
        return s_green_1;
        break;
    case GREEN2:
        return s_green_2;
        break;
    case YELLOW:
        return s_yellow;
        break;
    case RED:
        return s_red;
        break;
    default:
        break;
    }
    return NULL;
}

Static char* get_blink_mode(bcmu_blinking_t blinking) {
    switch (blinking) {
    case PERIODIC_FLASH:
        return s_periodic_flash_mode;
        break;
    case CONSTANTLY_ON:
        return s_constantly_on_mode;
        break;
    case FAST_FLASH:
        return s_fast_flash_mode;
        break;
    case SLOW_FLASH:
        return s_slow_flash_mode;
        break;
    case INTERRUPTED_FLASH:
        return s_interrupted_flash_mode;
        break;
    case SUPER_FAST_FLASH:
        return s_super_fast_flash_mode;
        break;
    case CONSTANTLY_OFF:
        return s_constantly_off_mode;
        break;
    default:
        break;
    }
    return NULL;
}

/* 定义开灯函数 */
Static void led_green_1_switch_on(void *param) {
    rt_pin_write(LED1_RUN_PIN, PIN_LOW);
}

Static void led_green_2_switch_on(void *param) {
    rt_pin_write(LED2_RUN_PIN, PIN_LOW);
}

Static void led_yellow_switch_on(void *param) {
    rt_pin_write(LED_YELLOW_PIN, PIN_LOW);
}

Static void led_red_switch_on(void *param) {
    rt_pin_write(LED_RED_PIN, PIN_LOW);
}

/* 定义关灯函数 */
Static void led_green_1_switch_off(void *param) {
    rt_pin_write(LED1_RUN_PIN, PIN_HIGH);
}

Static void led_green_2_switch_off(void *param) {
    rt_pin_write(LED2_RUN_PIN, PIN_HIGH);
}

Static void led_yellow_switch_off(void *param) {
    rt_pin_write(LED_YELLOW_PIN, PIN_HIGH);
}

Static void led_red_switch_off(void *param) {
    rt_pin_write(LED_RED_PIN, PIN_HIGH);
}

void* init_led_blink(void* param) {
    server_info_t* server_info = NULL;

    rt_pin_mode(LED1_RUN_PIN, PIN_MODE_OUTPUT);
    s_green_1 = led_create(led_green_1_switch_on, led_green_1_switch_off, NULL);
    RETURN_VAL_IF_FAIL(s_green_1 != NULL, NULL);
    // 绿灯1，上电周期闪
    led_set_mode(s_green_1, LOOP_PERMANENT, s_periodic_flash_mode);
    led_start(s_green_1);

    rt_pin_mode(LED2_RUN_PIN, PIN_MODE_OUTPUT);
    s_green_2 = led_create(led_green_2_switch_on, led_green_2_switch_off, NULL);
    RETURN_VAL_IF_FAIL(s_green_2 != NULL, NULL);
    led_set_mode(s_green_2, LOOP_PERMANENT, s_constantly_off_mode);
    led_start(s_green_2);

    rt_pin_mode(LED_YELLOW_PIN, PIN_MODE_OUTPUT);
    s_yellow = led_create(led_yellow_switch_on, led_yellow_switch_off, NULL);
    RETURN_VAL_IF_FAIL(s_yellow != NULL, NULL);
    led_set_mode(s_yellow, LOOP_PERMANENT, s_constantly_off_mode);
    led_start(s_yellow);

    rt_pin_mode(LED_RED_PIN, PIN_MODE_OUTPUT);
    s_red = led_create(led_red_switch_on, led_red_switch_off, NULL);
    RETURN_VAL_IF_FAIL(s_red != NULL, NULL);
    led_set_mode(s_red, LOOP_PERMANENT, s_constantly_off_mode);
    led_start(s_red);

    server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(s_led_msg_map) / sizeof(msg_map);
    register_server_msg_map(s_led_msg_map, server_info);

    return server_info;
}

void led_thread_entry(void* param) {
    PRINT_MSG_AND_RETURN_IF_FAIL(param != NULL);
    s_curr_server = _curr_server_get();

    while (is_running(TRUE)){
        recv_msg_deal();
        led_ticks();
        rt_thread_mdelay(LED_TICK_TIME);
    }
}

Static int recv_msg_deal(void) {
    _rt_msg_t curr_msg = NULL;
    led_ctrl_msg_t* msg_data = NULL;
    RETURN_VAL_IF_FAIL(s_curr_server != NULL && s_curr_server->msg_node != NULL, FAILURE);
    // 消息接收,未获得信号量则不处理
    RETURN_VAL_IF_FAIL(RT_EOK == rt_sem_take(&s_curr_server->msg_sem, LED_MSG_SEM_TIMEOUT), FAILURE);

    curr_msg = s_curr_server->msg_node;

    if (LED_CTRL_MSG == curr_msg->msg.msg_id) {
        RETURN_VAL_IF_FAIL(curr_msg->msg.data_size >= sizeof(led_ctrl_msg_t), FAILURE);
        msg_data = (led_ctrl_msg_t*)curr_msg->msg.data;
        RETURN_VAL_IF_FAIL(msg_data != NULL, FAILURE);
        set_light_and_blinking(msg_data->module_id, msg_data->status);
    }

    rt_mutex_take(&s_curr_server->mutex, RT_WAITING_FOREVER);
    s_curr_server->msg_node = curr_msg->next;
    s_curr_server->msg_count--;
    rt_mutex_release(&s_curr_server->mutex);

    RT_KERNEL_FREE(curr_msg);
    return SUCCESSFUL;
}
