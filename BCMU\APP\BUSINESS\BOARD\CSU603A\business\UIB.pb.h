/* Automatically generated nanopb header */
/* Generated by nanopb-0.3.8 at Wed May 24 09:37:42 2017. */

#ifndef PB_UIB_PB_H_INCLUDED
#define PB_UIB_PB_H_INCLUDED
#include <pb.h>

/* @@protoc_insertion_point(includes) */
#if PB_PROTO_HEADER_VERSION != 30
#error Regenerate this file with the current version of nanopb generator.
#endif

#ifdef __cplusplus
extern "C" {
#endif

/* Struct definitions */
typedef struct _facinfo_response_release_date {
    int32_t year;
    int32_t month;
    int32_t day;
/* @@protoc_insertion_point(struct:facinfo_response_release_date) */
} facinfo_response_release_date;

typedef struct _realdata_AI_response {
    pb_size_t AI_count;
    int32_t AI[100];
/* @@protoc_insertion_point(struct:realdata_AI_response) */
} realdata_AI_response;

typedef struct _realdata_DI_response {
    pb_size_t DI_count;
    int32_t DI[80];
/* @@protoc_insertion_point(struct:realdata_DI_response) */
} realdata_DI_response;

typedef struct _setDO_request {
    pb_size_t DO_count;
    int32_t DO[30];
/* @@protoc_insertion_point(struct:setDO_request) */
} setDO_request;

typedef struct _facinfo_response {
    char soft_name[20];
    char soft_version[20];
    facinfo_response_release_date date;
/* @@protoc_insertion_point(struct:facinfo_response) */
} facinfo_response;

/* Default values for struct fields */

/* Initializer values for message structs */
#define realdata_AI_response_init_default        {0, {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0}}
#define realdata_DI_response_init_default        {0, {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0}}
#define facinfo_response_init_default            {"", "", facinfo_response_release_date_init_default}
#define facinfo_response_release_date_init_default {0, 0, 0}
#define setDO_request_init_default               {0, {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0}}
#define realdata_AI_response_init_zero           {0, {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0}}
#define realdata_DI_response_init_zero           {0, {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0}}
#define facinfo_response_init_zero               {"", "", facinfo_response_release_date_init_zero}
#define facinfo_response_release_date_init_zero  {0, 0, 0}
#define setDO_request_init_zero                  {0, {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0}}

/* Field tags (for use in manual encoding/decoding) */
#define facinfo_response_release_date_year_tag   1
#define facinfo_response_release_date_month_tag  2
#define facinfo_response_release_date_day_tag    3
#define realdata_AI_response_AI_tag              1
#define realdata_DI_response_DI_tag              1
#define setDO_request_DO_tag                     1
#define facinfo_response_soft_name_tag           1
#define facinfo_response_soft_version_tag        2
#define facinfo_response_date_tag                3

/* Struct field encoding specification for nanopb */
extern const pb_field_t realdata_AI_response_fields[2];
extern const pb_field_t realdata_DI_response_fields[2];
extern const pb_field_t facinfo_response_fields[4];
extern const pb_field_t facinfo_response_release_date_fields[4];
extern const pb_field_t setDO_request_fields[2];

/* Maximum encoded size of messages (where known) */
#define realdata_AI_response_size                550
#define realdata_DI_response_size                880
#define facinfo_response_size                    79
#define facinfo_response_release_date_size       33
#define setDO_request_size                       330

/* Message IDs (where set with "msgid" option) */
#ifdef PB_MSGID

#define UIB_MESSAGES \


#endif

#ifdef __cplusplus
} /* extern "C" */
#endif
/* @@protoc_insertion_point(eof) */

#endif
