#ifndef _TOV_4G_INTERFACE_H
#define _TOV_4G_INTERFACE_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

#include "4g_mgr.h"
#include "4g_data_utils.h"

#define NET_4G_INTERVAL             ((30000)/NET_4G_THREAD_RUN_INTERVAL)
#define GET_4G_INFO_INIT_TIME       ((15000)/NET_4G_THREAD_RUN_INTERVAL)
#define MAX_SIM_NO_COUNT            2

#define MAX_IP_ERR_COUNT      1

#define MODEL_REMOVE          0
#define MODEL_INSERT          1


int is_4g_info_effective();

int set_4g_info(short isClear);

int judge_4G_ip(rt_device_t dev);
short model_4g_offline_handle(rt_device_t dev);
short judge_simcard_status(rt_device_t dev, unsigned short curr_sim_status);
short check_sim_status_handle(rt_device_t dev);
short product_special_handle();
int register_4g_func();

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  