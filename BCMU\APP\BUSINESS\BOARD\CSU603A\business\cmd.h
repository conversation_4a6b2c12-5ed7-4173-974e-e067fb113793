/**
 * @file     cmd.h
 * @brief    命令头文件.
 * @details  This is the detail description.
 * <AUTHOR> 
 * @date     2022-12-23
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation 
 * @par History:
 *   version: author, date, descn
 */ 
#ifndef _CMD_H
#define _CMD_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

//  UIB/IDDB命令ID 定义
#define  UIB_LINK               1 ///<  建链(获取厂家信息)
#define  UIB_GET_AI             2 ///<  获取AI采样数据
#define  UIB_GET_DI             3 ///<  获取DI采样数据
#define  UIB_GET_BARCODE        4 ///<  获取单板条码   
#define  UIB_DO_CTRL            5 ///<  DO控制
#define  UIB_SET_BARCODE        6 ///<  设置单板条码信息
#define  UIB_SET_CSU_FAULT_DO   7 ///<  设置CSU异常DO状态
#define  UIB_SET_FAN            8 ///<  设置丰富PWM占空比
#define  UIB_UPDATE_DATA               9        ///<  程序更新数据传输帧
#define  UIB_UPDATE_TRIG               10        ///<  程序更新触发帧

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _CMD_H
