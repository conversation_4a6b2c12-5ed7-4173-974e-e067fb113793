#ifndef USER_BRANCHAPP_INCLUDE_HEATERCONTROL_H
#define USER_BRANCHAPP_INCLUDE_HEATERCONTROL_H

#include "common.h"
#include "sample.h"
#include "interface.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct HeaterSysPara
{
    FLOAT fTurnOnTemp;
    FLOAT fTurnOffTemp;

} T_HeaterSysPara;

typedef enum
{
    HEATER_OFF = 0,
    HEATER_ON = 1,
} T_HeaterStatus;

typedef enum
{
    HEATER_TURNON_NONE = 0,
    HEATER_TURNON_CHARGE = 1,
    HEATER_TURNON_DISCHARGE = 2,
} T_HeaterTurnOnRurpose;

typedef enum
{
    HEATER_TEST_NONE = 0,
    HEATER_TEST_ON = 1,
    HEATER_TEST_OFF = 2,
} T_HeaterTestCtrlStatus;

typedef enum
{
    HEATER_NORMAL_MODE = 0,
    HEATER_TEST_MODE,
    HEATER_INVALID_MODE,
} T_HeaterWorkingMode;

typedef struct HeaterRealData
{
    FLOAT fMinCellTemp;
    BOOLEAN bPowerOnFlag;
    T_HeaterStatus tStatus;
    BYTE tWorkingMode;
    FLOAT fMaxCellTemp;
} T_HeaterRealData;

typedef struct HeaterInfo
{
    T_HeaterSysPara tSysPara;
    T_HeaterRealData tRealData;
} T_HeaterInfo;

typedef struct HeaterManager
{
    T_HeaterInfo tInfo;
    BOOLEAN (*shouldTurnOn)(const T_HeaterInfo *);
    BOOLEAN (*shouldTurnOff)(const T_HeaterInfo *);
} T_HeaterManager;

void InitHeater(void);
void RefreshHeaterInfo(T_BCMDataStruct * tBCMAnaData);
void DealHeaterControl(void);
BOOLEAN SetHeaterWorkingMode(T_HeaterWorkingMode tMode);
void SetHeaterTestCtrl(BYTE status);
BOOLEAN IsHeaterFilmConsist(void);
BYTE checkHeaterRealState(void);
T_HeaterStatus GetHeaterLogicStatus(void);
BOOLEAN CtrlHeaterOnOff(T_HeaterStatus tStatus);
#ifdef __cplusplus
}
#endif

#endif /* USER_BRANCHAPP_INCLUDE_HEATERCONTROL_H */
