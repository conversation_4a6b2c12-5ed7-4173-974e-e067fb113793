/*******************************************************************************
  * @file        update_manage.c
  * @version     v1.0.0
  * @copyright   COPYRIGHT &copy; 2023 CSG
  * <AUTHOR>
  * @date        2023-4-6
  * @brief
  * @attention
  * Modification History
  * DATE         DESCRIPTION
  * ------------------------
  * - 2023-4-6  dongmengling Created
*******************************************************************************/
#include <string.h>
#include <rtthread.h>
#include <rtdevice.h>
#include <stdlib.h>
#include "update_manage.h"
#include "msg.h"
#include "cmd.h"
#include "device_type.h"
#include "dev_bmu.h"
#include "sps.h"
#include "protocol_layer.h"
#include "update_south_dev.h"
#include "pin_define.h"
#include "utils_data_transmission.h"
#include "utils_rtthread_security_func.h"
#include "flash.h"
#include "sfud.h"
#include "fal.h"
#include "sfud_def.h"
#include "spi_flash.h"
#include "para_id_in.h"
#include "para_manage.h"

#define BMU_UPDATE_FILE_NAME     "bmu_app.bin"

Static int parse_update_trig_old(void* dev_inst, void *cmd_buf);
Static int pack_update_trig_old(void* dev_inst, void *cmd_buf);
Static int get_app_download_rtn(void *cmd_buf);


Static trig_ctr_inf_t s_trig_ctr_inf;
Static T_FileManageStruct ptFileManage;

static cmd_handle_register_t update_cmd_handle[] = {
    {DEV_BCMU, BMU_UPDATE_TRIG_OLD,       CMD_TYPE_NO_POLL, parse_update_trig_old, pack_update_trig_old},
};

Static int get_app_download_rtn(void *cmd_buf) {
    bottom_comm_cmd_head_t* proto_head = NULL;
    cmd_buf_t* trig_cmd_buf = (cmd_buf_t*)cmd_buf;

    // 入参校验
    if (trig_cmd_buf == NULL || trig_cmd_buf->buf == NULL) {
        return FAILURE;
    }

    proto_head = (bottom_comm_cmd_head_t*)trig_cmd_buf->cmd->req_head;
    char *p_file_name = s_trig_ctr_inf.file_name;
    unsigned char *p = trig_cmd_buf->buf;

    if (proto_head->append != 0xAA55)
    {
        return DownloadFrameErr;
    }

    if (trig_cmd_buf->data_len == UPDATE_FILE_NAME_LEN)
    {
        rt_snprintf_s(p_file_name, UPDATE_FILE_NAME_LEN, BMU_UPDATE_FILE_NAME);
        if (strncmp((char *)p, BMU_UPDATE_FILE_NAME, UPDATE_FILE_NAME_LEN) == 0) {
            return BOTTOM_RTN_APP_CORRECT;
        }
    }
    return DownFileNameErr;
}

Static int parse_update_trig_old(void* dev_inst, void *cmd_buf) {

    s_trig_ctr_inf.rtn = get_app_download_rtn(cmd_buf);
    return SUCCESSFUL;
}

Static int pack_update_trig_old(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* trig_cmd_buf = (cmd_buf_t*)cmd_buf;

    // 入参校验
    if (trig_cmd_buf == NULL || trig_cmd_buf->buf == NULL) {
        return FAILURE;
    }

    if(s_trig_ctr_inf.rtn == DownloadFrameErr) {
        //附加码错误
        trig_cmd_buf->data_len = 0;
        trig_cmd_buf->rtn = BOTTOM_RTN_CMD_ERROR;
        return SUCCESSFUL;
    }else{
        //回复正确文件名
        trig_cmd_buf->data_len = UPDATE_FILE_NAME_LEN;
        rt_memset_s(trig_cmd_buf->buf, trig_cmd_buf->data_len, 0x00, trig_cmd_buf->data_len);
        rt_memcpy_s(trig_cmd_buf->buf, UPDATE_FILE_NAME_LEN,  BMU_UPDATE_FILE_NAME, sizeof(BMU_UPDATE_FILE_NAME)-1);
    }

    BeginDownload(FLAG_BACKUP);

    return SUCCESSFUL;
}

int update_manage_init(void) {
    short i = 0;
    for(i = 0; i < sizeof(update_cmd_handle)/sizeof(update_cmd_handle[0]); i++) {
        register_cmd_handle(&update_cmd_handle[i]);
    }

   return SUCCESSFUL;
}

int BeginDownload(unsigned char ucMode)
{
    uint8_t *pFlag = (uint8_t*)FLAG_PROGRAM;
    uint8_t aucBuff[MSC_MAX_PACKET] = {0};
    unsigned char localhost_addr = 0;

    rt_memset_s(&ptFileManage,sizeof(T_FileManageStruct),0xFF,sizeof(T_FileManageStruct));
    get_one_para(BMU_PARA_ID_HOST_ADDR_OFFSET, &localhost_addr);
    FLASH_If_Read(UPDATEINFO_START,(uint8_t *)&ptFileManage,sizeof(T_FileManageStruct));
    ptFileManage.ucFlag = ucMode;
    ptFileManage.ucUpdateAddr = localhost_addr;
    ptFileManage.wCrc = crc_cal((unsigned char *)&ptFileManage, offsetof(T_FileManageStruct,wCrc));
    FLASH_If_Write(UPDATEINFO_START, (uint32_t *)&ptFileManage, sizeof(T_FileManageStruct));

    if(ucMode != FLAG_OK){
        NVIC_SystemReset();
    }
    return TRUE;
}