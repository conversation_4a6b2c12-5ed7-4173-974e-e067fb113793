#include "alarm_register.h"
#include "msg.h"
#include "sample.h"
#include "pid.h"
#include "alarm_mgr_api.h"
#include "addr_distribution.h"
#include "msg_id.h"
#include "alarm_id_in.h"
#include "para_id_in.h"
#include "realdata_id_in.h"
#include "alarm_config_in.h"
// static char check_busbar_overtemp(void);
// static void handle_alarm(real_alarm_t* alarm, char value);
// static void send_msg_to_sample(unsigned int msg_id, module_id_e dest,  unsigned char dev_no, unsigned char cmd_id);
// static void send_msg_to_control(unsigned int msg_id, module_id_e dest);
// static void batt_mod_temp_high_handle(real_alarm_t* alarm, char value);
// static void cluster_charge_temp_handle(real_alarm_t* alarm, char value) ;

static unsigned short alarm_clean_by_code_tab[] = {
    0xffff
};

Static alarm_manage_info_t bcmu_manage = {
    ALARM_ID_OFFSET_MAX - 1,
    ana_alm_config_tab,
    dig_alm_config_tab,
    self_alm_config_tab,
    alm_shielded,
    alarm_clean_by_code_tab,        // 以告警码清除相关告警列表
};

/* 未使用，先注释
static void handle_alarm(real_alarm_t* alarm, char value) {
    unsigned short alarm_code = 0;
    unsigned short dev_no = 0;
    unsigned char alm_level = 0;

    RETURN_IF_FAIL(alarm != NULL);
    alarm_code = ALM_ID_GET_ALM_CODE(alarm->alm_id);
    dev_no = ALM_ID_GET_DEV(alarm->alm_id);
    alm_level = ALM_ID_GET_LEVEL(alarm->alm_id);

    switch(alarm_code) {
        case CLUSTER_SOC_LOW:
            send_msg_to_sample(EXE_DEST_CMD_MSG, MOD_BMU, dev_no, BMU_HIS_RECORD_SAVE);
            break;
        case CLUSTER_INSULATION_RESIS_ALM:
            if(ALARM_LEVEL_CRITICAL == alm_level)
                send_msg_to_control(EPO_ALARM_MSG, MOD_CONTROL);
            break;
        default:
            break;
    }
    return;
}
*/

//发送消息到控制模块，关闭断路器
/* 未使用，先注释
static void send_msg_to_control(unsigned int msg_id, module_id_e dest)
{
    module_msg_t msg_send = {MOD_ALARM_MANAGE, dest, msg_id, NULL};
    send_msg(&msg_send);
}
*/

//发送消息给采样模块/设备模块
/* 未使用，先注释
static void send_msg_to_sample(unsigned int msg_id, module_id_e dest,  unsigned char dev_no, unsigned char cmd_id){
    module_msg_t msg_send;
    unsigned char msg_data[2] = {0};
    unsigned char* send_data = NULL;

    send_data = (unsigned char*)malloc(2*sizeof(unsigned char));
    RETURN_IF_FAIL(send_data != NULL);

    msg_send.src = MOD_ALARM_MANAGE;
    msg_send.dest = dest;
    msg_send.msg_id = msg_id;
    
    msg_data[0] = cmd_id;
    msg_data[1] = dev_no;
    memcpy(send_data, &msg_data, 2*sizeof(unsigned char));
    msg_send.data = send_data;
    send_msg(&msg_send);
}
*/

void register_bcmu_alarm(void) {
    register_alarm_manage_info(&bcmu_manage);
}

/* 未使用，先注释
static char check_busbar_overtemp(void) {
    batt_cabinet_dig_t dig_data = {0};
    
    get_batt_cabinet_dig_data(&dig_data);
    if (dig_data.busbar_overtemp) {
        return TRUE;
    } else {
        return FALSE;
    }
}
*/

/* 未使用，先注释
static void batt_mod_temp_high_handle(real_alarm_t* alarm, char value)  {
    int dev_no = ALM_ID_GET_DEV(alarm->alm_id);
    unsigned char level = ALM_ID_GET_LEVEL(alarm->alm_id);
    cluster_topology_t cluster_topo;
    unsigned char i, j;
    
    get_cluster_topology(&cluster_topo);
    if (cluster_topo.topo_valid == TOPO_INVALID || value == FALSE || level != (unsigned char)ALARM_LEVEL_CRITICAL)
        return;
    
    for (i = 0; i < cluster_topo.cluster_count; i++) {
        for (j = 0; j < cluster_topo.mod_num[i]; j++) {
            if (dev_no == cluster_topo.mod_addr[i][j]) {
                send_msg_to_sample(EXE_DEST_CMD_MSG, MOD_DC_DC, dev_no, DC_DC_CTRL_EPO);
                break;
            }
        }
    }
}
*/

/* 未使用，先注释
static void cluster_charge_temp_handle(real_alarm_t* alarm, char value)  {
    int dev_no = ALM_ID_GET_DEV(alarm->alm_id);
    unsigned char level = ALM_ID_GET_LEVEL(alarm->alm_id);
    cluster_topology_t cluster_topo;
    unsigned char i;
    
    get_cluster_topology(&cluster_topo);
    if (cluster_topo.topo_valid == TOPO_INVALID || value == FALSE || level != (unsigned char)ALARM_LEVEL_MAJOR)
        return;
    
    for (i = 0; i < cluster_topo.cluster_count; i++) {
        if (dev_no == i + 1) {
            send_msg_to_sample(EXE_DEST_CMD_MSG, MOD_DC_DC, dev_no, DC_DC_CTRL_POWER_OFF);
            break;
        }
    }
}
*/