#include <string.h>
#include "sps.h"
#include "dev_csu_comm.h"
#include "protocol_layer.h"
#include "para_id_in.h"
#include "para_manage.h"
#include "realdata_id_in.h"
#include "utils_data_transmission.h"
#include "utils_rtthread_security_func.h"
#include "utils_data_type_conversion.h"
#include "realdata_save.h"
#include "pin_define.h"
#include "pin_ctrl.h"
#include "storage.h"
#include "partition_def.h"
#include "msg_id.h"
#include "softbus.h"
#include "pin_define.h"
#include "pdt_version.h"
#include "log_mgr.h"
#include "sample.h"

/* 命令请求 */
static bottom_comm_cmd_head_t cmd_req[] RAM_SECTION = {
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATA_AFT_TRIG_FRAME,   CMD_APPEND_UPDATE_REQ,                       BOTTOM_RTN_APP_NOACK},   //0
    {BOTTOM_PROTO_TYPE_DOWN, CMD_TRAN_AFT_DATA_FRAME,     CMD_APPEND_NONE,                             BOTTOM_RTN_APP_NOACK},   //1
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPLOAD_TRIG_FRAME,   CMD_APPEND_UPDATE_REQ,                       BOTTOM_RTN_APP_NOACK},    //2
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPLOAD_LIST_FRAME,   CMD_APPEND_NONE,                             BOTTOM_RTN_APP_NOACK},    //3
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPLOAD_DATA_FRAME,   CMD_APPEND_NONE,                             BOTTOM_RTN_APP_NOACK},    //4

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA,   CMD_APPEND_NONE,                                BOTTOM_RTN_APP_NOACK},    //5
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_BATCH_PARA,   CMD_APPEND_PARA_1,                             BOTTOM_RTN_APP_NOACK},    //6
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_BATCH_PARA,   CMD_APPEND_PARA_2,                             BOTTOM_RTN_APP_NOACK},    //7
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_BATCH_PARA,   CMD_APPEND_PARA_1,                             BOTTOM_RTN_APP_NOACK},    //8
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_BATCH_PARA,   CMD_APPEND_PARA_2,                             BOTTOM_RTN_APP_NOACK},    //9
    {BOTTOM_PROTO_TYPE_COMM, CMD_LINK,             CMD_APPEND_NONE,                               BOTTOM_RTN_APP_NOACK},    //10
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_BATCH_PARA,   CMD_APPEND_SYS_TIME,                           BOTTOM_RTN_APP_NOACK},    //11
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_BATCH_PARA,   CMD_APPEND_SYS_TIME,                           BOTTOM_RTN_APP_NOACK},    //12
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_BATCH_PARA,   CMD_APPEND_MANU_INFO,                          BOTTOM_RTN_APP_NOACK},    //13
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_BATCH_PARA,   CMD_APPEND_MANU_INFO,                          BOTTOM_RTN_APP_NOACK},    //14
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA,    CMD_APPEND_INRLY_STATUS,                       BOTTOM_RTN_APP_NOACK},    //15
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL,         CMD_APPEND_OUTRLY_HIGH,                        BOTTOM_RTN_APP_NOACK},    //16
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL,         CMD_APPEND_OUTRLY_LOW,                         BOTTOM_RTN_APP_NOACK},    //17
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL,         CMD_CLEAR_EXTREME_DATA,                         BOTTOM_RTN_APP_NOACK},    //18
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL,         CMD_RESTORE_FACT,                               BOTTOM_RTN_APP_NOACK},    //19
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL,         CMD_CLEAR_FAULT_DATA,                           BOTTOM_RTN_APP_NOACK},    //20

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA,    CMD_APPEND_4G_INFO,                            BOTTOM_RTN_APP_NOACK},    //21

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_BATCH_PARA,   CMD_APPEND_MQTT_PARA,                          BOTTOM_RTN_APP_NOACK},    //22
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_BATCH_PARA,   CMD_APPEND_MQTT_PARA,                          BOTTOM_RTN_APP_NOACK},    //23

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL,         CMD_CLEAR_DATA_REBOOT,                         BOTTOM_RTN_APP_NOACK},    //24
    {0}
};

/* 命令应答   */
static bottom_comm_cmd_head_t cmd_ack[] RAM_SECTION = {
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPDATA_AFT_TRIG_FRAME,   CMD_APPEND_UPDATE_ACK,                       BOTTOM_RTN_APP_NOACK},   //0
    {BOTTOM_PROTO_TYPE_DOWN, CMD_TRAN_AFT_DATA_FRAME,     CMD_APPEND_NONE,                             BOTTOM_RTN_APP_NOACK},   //1
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPLOAD_TRIG_FRAME,   CMD_APPEND_UPDATE_ACK,                       BOTTOM_RTN_APP_NOACK},    //2
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPLOAD_LIST_FRAME,   CMD_APPEND_NONE,                             BOTTOM_RTN_APP_NOACK},    //3
    {BOTTOM_PROTO_TYPE_DOWN, CMD_UPLOAD_DATA_FRAME,   CMD_APPEND_NONE,                             BOTTOM_RTN_APP_NOACK},    //4

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA,    CMD_APPEND_NONE,                               BOTTOM_RTN_APP_NOACK},    //5
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_BATCH_PARA,   CMD_APPEND_PARA_1,                             BOTTOM_RTN_APP_NOACK},    //6
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_BATCH_PARA,   CMD_APPEND_PARA_2,                             BOTTOM_RTN_APP_NOACK},    //7
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_BATCH_PARA,   CMD_APPEND_PARA_1,                             BOTTOM_RTN_APP_NOACK},    //8
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_BATCH_PARA,   CMD_APPEND_PARA_2,                             BOTTOM_RTN_APP_NOACK},    //9
    {BOTTOM_PROTO_TYPE_COMM, CMD_LINK,             CMD_APPEND_NONE,                               BOTTOM_RTN_APP_NOACK},    //10
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_BATCH_PARA,   CMD_APPEND_SYS_TIME,                           BOTTOM_RTN_APP_NOACK},    //11
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_BATCH_PARA,   CMD_APPEND_SYS_TIME,                           BOTTOM_RTN_APP_NOACK},    //12
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_BATCH_PARA,   CMD_APPEND_MANU_INFO,                          BOTTOM_RTN_APP_NOACK},    //13
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_BATCH_PARA,   CMD_APPEND_MANU_INFO,                          BOTTOM_RTN_APP_NOACK},    //14
    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA,    CMD_APPEND_INRLY_STATUS,                       BOTTOM_RTN_APP_NOACK},    //15
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL,         CMD_APPEND_OUTRLY_HIGH,                        BOTTOM_RTN_APP_NOACK},    //16
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL,         CMD_APPEND_OUTRLY_LOW,                         BOTTOM_RTN_APP_NOACK},    //17
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL,         CMD_CLEAR_EXTREME_DATA,                         BOTTOM_RTN_APP_NOACK},    //18
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL,         CMD_RESTORE_FACT,                               BOTTOM_RTN_APP_NOACK},    //19
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL,         CMD_CLEAR_FAULT_DATA,                           BOTTOM_RTN_APP_NOACK},    //20

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_REAL_DATA,    CMD_APPEND_4G_INFO,                            BOTTOM_RTN_APP_NOACK},    //21

    {BOTTOM_PROTO_TYPE_COMM, CMD_GET_BATCH_PARA,   CMD_APPEND_MQTT_PARA,                          BOTTOM_RTN_APP_NOACK},    //22
    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_BATCH_PARA,   CMD_APPEND_MQTT_PARA,                          BOTTOM_RTN_APP_NOACK},    //23

    {BOTTOM_PROTO_TYPE_COMM, CMD_SET_CTRL,         CMD_CLEAR_DATA_REBOOT,                         BOTTOM_RTN_APP_NOACK},    //24

    {0}
};

/* 设置参数打包命令 */
static data_info_id_verison_t cmd_para_1[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA1_LEN, NOT_NEED_INTERACT},  
    {SEG_SYSTEM_PARA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_PARA_ID_AC_T0_SIZE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_PARA_ID_AC_T1_SIZE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_char, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_PARA_ID_DC_T0_SIZE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_char, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_PARA_ID_DC_T1_SIZE_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_PARA_ID_AC_PHASE_VOL_HIGH_THRESHOLD_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_PARA_ID_AC_PHASE_VOL_HIGH_THRESHOLD_OFFSET+1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_PARA_ID_AC_PHASE_VOL_HIGH_THRESHOLD_OFFSET+2},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_PARA_ID_AC_PHASE_VOL_HIGH_THRESHOLD_OFFSET+3},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_PARA_ID_AC_PHASE_VOL_LOW_THRESHOLD_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_PARA_ID_AC_PHASE_VOL_LOW_THRESHOLD_OFFSET+1},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_PARA_ID_AC_PHASE_VOL_LOW_THRESHOLD_OFFSET+2},
    {SEG_SYSTEM_PARA, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_PARA_ID_AC_PHASE_CURR_HIGH_THRESHOLD_OFFSET},
    {SEG_SYSTEM_PARA, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_PARA_ID_AC_PHASE_CURR_HIGH_THRESHOLD_OFFSET+1},
    {SEG_SYSTEM_PARA, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_PARA_ID_AC_PHASE_CURR_HIGH_THRESHOLD_OFFSET+2},
    {SEG_SYSTEM_PARA, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_PARA_ID_AC_PHASE_CURR_HIGH_THRESHOLD_OFFSET+3},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_PARA_ID_DC_VOL_HIGH_THRESHOLD_OFFSET},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_PARA_ID_DC_VOL_LOW_THRESHOLD_OFFSET},
    {SEG_SYSTEM_PARA, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_PARA_ID_DC_CURR_HIGH_THRESHOLD_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_PARA_ID_DC_VOL_MODE_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_PARA_ID_SAMPLE_FREQ_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_PARA_ID_VOL_LOW_POINT_NUMBER_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_PARA_ID_VOL_HIGH_POINT_NUMBER_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_PARA_ID_DC_VOL_LOW_DEVIATION_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_PARA_ID_AC_VOL_LOW_DEVIATION_OFFSET},
};

/* 建链命令 */
static data_info_id_verison_t cmd_links[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, LINK_LEN, NOT_NEED_INTERACT},  
    {SEG_DATA, type_string, ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_DATA_ID_COMM_PRO_VER},
    {SEG_DATA, type_string, ARRAY_SIZE_20, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_DATA_ID_SOFTWARE_NAME},
    {SEG_DATA, type_string, ARRAY_SIZE_20, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_DATA_ID_SOFTWARE_VER},
    {SEG_DATA , type_date, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_DATA_ID_SOFTWARE_DATE},
    {SEG_DATA, type_string, ARRAY_SIZE_20, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_DATA_ID_HARDWARE_VER},
};

/* 实时量段2 */
static data_info_id_verison_t real_data_2[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, 1, NOT_NEED_INTERACT},  
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_DATA_ID_RECORD_FINISH_STATUS},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, 
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
    {SEG_DATA, type_bit, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},
};

/* 获取厂家信息 */
static data_info_id_verison_t manu_info[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, 62, NOT_NEED_INTERACT},  
    {SEG_DATA, type_string, ARRAY_SIZE_30, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_DATA_ID_FACTORY_NAME},
    {SEG_DATA, type_string, ARRAY_SIZE_20, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_DATA_ID_HARDWARE_VER},
    {SEG_DATA, type_string, ARRAY_SIZE_12, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_DATA_ID_SERIAL},
};

/* 输入干接点状态 */
static data_info_id_verison_t inrly_status[] RAM_SECTION = {
    {SEG_DATA, type_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_DATA_ID_INRLY_STATUS},
};


static data_info_id_verison_t info_4g[] RAM_SECTION = {
    {SEG_DATA, type_string, ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_DATA_ID_WIRELESS_IP_ADDR},
    {SEG_DATA, type_string, ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_DATA_ID_4G_COMMUNICATION_ROD_MODEL},
    {SEG_DATA, type_string, ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_DATA_ID_4G_COMMUNICATION_ROD_SERIAL_NUMBER},
    {SEG_DATA, type_string, ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_DATA_ID_4G_COMMUNICATION_ROD_SOFT_VER},
    {SEG_DATA, type_string, ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_DATA_ID_4G_COMMUNICATION_ROD_ICCID_NUMBER},
    {SEG_DATA, type_string, ARRAY_SIZE_32, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_DATA_ID_4G_OPERATOR_NAME},
    
    {SEG_DATA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_DATA_ID_4G_SIGNAL_STRENGTH},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_DATA_ID_4G_EXIST},
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_DATA_ID_SIM_EXIST},
    
};


// 设置和获取网络参数
static data_info_id_verison_t cmd_mqtt_para[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, PARA2_LEN, NOT_NEED_INTERACT}, 
    {SEG_SYSTEM_PARA, type_string, ARRAY_SIZE_16, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_PARA_ID_MQTT_NET_IP_OFFSET},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_PARA_ID_MQTT_NET_PORT_OFFSET},
    {SEG_SYSTEM_PARA, type_string, ARRAY_SIZE_30, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_PARA_ID_MQTT_USERNAME_OFFSET},
    {SEG_SYSTEM_PARA, type_string, ARRAY_SIZE_30, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, TOV_PARA_ID_MQTT_WORD_OFFSET},
};

/* 打包命令 */
static cmd_parse_info_id_verison_t cmd_pack_info[] = {
    {&cmd_para_1[0], sizeof(cmd_para_1)/sizeof(data_info_id_verison_t)},  // 0
    {&cmd_links[0],  sizeof(cmd_links)/sizeof(data_info_id_verison_t)},  // 1
    {&real_data_2[0],  sizeof(real_data_2)/sizeof(data_info_id_verison_t)},  // 2
    {&manu_info[0],  sizeof(manu_info)/sizeof(data_info_id_verison_t)},  // 3
    {&inrly_status[0],  sizeof(inrly_status)/sizeof(data_info_id_verison_t)},  // 4

    {&info_4g[0],  sizeof(info_4g)/sizeof(data_info_id_verison_t)},  // 5

    {cmd_mqtt_para,  sizeof(cmd_mqtt_para)/sizeof(data_info_id_verison_t)},  // 6
};

/* 解包命令 */
static cmd_parse_info_id_verison_t cmd_parse_info[] = {
    {&cmd_para_1[0],  sizeof(cmd_para_1)/sizeof(data_info_id_verison_t)},  // 0
    {cmd_mqtt_para,  sizeof(cmd_mqtt_para)/sizeof(data_info_id_verison_t)},  // 1
};

/* 通信协议命令表 */
static cmd_t no_poll_cmd_tab[] = {
    {UPDATE_TRIG_FRAME,             CMD_PASSIVE,  &cmd_req[0], &cmd_ack[0], sizeof(bottom_comm_cmd_head_t), },
    {TRAN_DATA_FRAME,               CMD_PASSIVE,  &cmd_req[1], &cmd_ack[1], sizeof(bottom_comm_cmd_head_t), },
    {UPLOAD_FILE_TRIG,              CMD_PASSIVE,  &cmd_req[2], &cmd_ack[2], sizeof(bottom_comm_cmd_head_t), },
    {UPLOAD_FILE_LIST,              CMD_PASSIVE,  &cmd_req[3], &cmd_ack[3], sizeof(bottom_comm_cmd_head_t), },
    {UPLOAD_FILE_DATA,              CMD_PASSIVE,  &cmd_req[4], &cmd_ack[4], sizeof(bottom_comm_cmd_head_t), },

    {GET_REAL_DATA,           CMD_PASSIVE,  &cmd_req[5], &cmd_ack[5], sizeof(bottom_comm_cmd_head_t), NULL, NULL, pack_real_data, NULL},
    {GET_PARA_1,              CMD_PASSIVE,  &cmd_req[6], &cmd_ack[6], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[0], NULL},
    {GET_PARA_2,              CMD_PASSIVE,  &cmd_req[7], &cmd_ack[7], sizeof(bottom_comm_cmd_head_t), NULL, NULL, pack_para_2, NULL},
    {SET_PARA_1,              CMD_PASSIVE,  &cmd_req[8], &cmd_ack[8], sizeof(bottom_comm_cmd_head_t), NULL, NULL, pack_para_1, NULL, NULL, &cmd_parse_info[0]},
    {SET_PARA_2,              CMD_PASSIVE,  &cmd_req[9], &cmd_ack[9], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, parse_para_2},
    {BUILD_LINK,              CMD_PASSIVE,  &cmd_req[10], &cmd_ack[10], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[1], NULL},
    {SET_SYS_TIME,            CMD_PASSIVE,  &cmd_req[11], &cmd_ack[11], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, parse_sys_time_data},
    {GET_SYS_TIME,            CMD_PASSIVE,  &cmd_req[12], &cmd_ack[12], sizeof(bottom_comm_cmd_head_t), NULL, NULL, pack_sys_time_data, NULL},
    {SET_MANU_info,           CMD_PASSIVE,  &cmd_req[13], &cmd_ack[13], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, parse_manu_info},
    {GET_MANU_info,           CMD_PASSIVE,  &cmd_req[14], &cmd_ack[14], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[3], NULL},

    {GET_INRLY_STATUS,        CMD_PASSIVE,  &cmd_req[15], &cmd_ack[15], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, parse_get_inrly, &cmd_pack_info[4], NULL},
    {CTRL_OUTRLY_HIGH,        CMD_PASSIVE,  &cmd_req[16], &cmd_ack[16], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, parse_ctrl_outrly_high},
    {CTRL_OUTRLY_LOW,         CMD_PASSIVE,  &cmd_req[17], &cmd_ack[17], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, parse_ctrl_outrly_low},
    {CLEAR_EXTREME_DATA,      CMD_PASSIVE,  &cmd_req[18], &cmd_ack[18], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, parse_clear_extreme_data, NULL, NULL},
    {RESTORE_FACT,            CMD_PASSIVE,  &cmd_req[19], &cmd_ack[19], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, parse_restore_fact, NULL, NULL},
    {CLEAR_FAULT_DATA,        CMD_PASSIVE,  &cmd_req[20], &cmd_ack[20], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, parse_clear_fault_data, NULL, NULL},

    {GET_4G_INFO,             CMD_PASSIVE,  &cmd_req[21], &cmd_ack[21], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[5], NULL},

    {GET_MQTT_PARA,           CMD_PASSIVE,  &cmd_req[22], &cmd_ack[22], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, NULL, &cmd_pack_info[6], NULL},
    {SET_MQTT_PARA,           CMD_PASSIVE,  &cmd_req[23], &cmd_ack[23], sizeof(bottom_comm_cmd_head_t), NULL, NULL, pack_para_1, NULL, NULL, &cmd_parse_info[1]},

    {CLEAR_DATA_REBOOT,       CMD_PASSIVE,  &cmd_req[24], &cmd_ack[24], sizeof(bottom_comm_cmd_head_t), NULL, NULL, NULL, parse_clear_data_reboot, NULL, NULL},

    {0},
};


static dev_type_t s_dev_csu_can = {
    DEV_TOV, 1, PROTOCOL_BOTTOM_COMM, LINK_CSU_CAN, R_BUFF_LEN_1024, S_BUFF_LEN_1024, 0, no_poll_cmd_tab, NULL
};

dev_type_t* init_dev_csu_can(void) {
    return &s_dev_csu_can;
}

static dev_type_t s_tov_dev_com = {
    DEV_TOV, 1, PROTOCOL_BOTTOM_COMM, LINK_TOV_COM, R_BUFF_LEN_1024, S_BUFF_LEN_2048, 0, no_poll_cmd_tab, NULL
};

dev_type_t* init_dev_tov_com(void) {
    return &s_tov_dev_com;
}


/* 自定义函数 */
int pack_para_1(void* dev_inst, void* cmd_buff)
{
    rt_kprintf("parse_para_1!!!!\n");
    pub_msg_to_thread(UPDATE_THRESHOLD, NULL, 0);
    save_all_para();
    return SUCCESSFUL;

}


int pack_para_2(void* dev_inst, void* cmd_buff)
{
    // 检查输入参数是否为NULL，如果是则返回FAILURE
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    // 获取命令缓冲区的缓冲区指针
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;

    // 初始化观测数和观测点变量
    unsigned char obser_num = 0;
    unsigned char obser_point = 0;

    // 初始化偏移量
    unsigned int offset = 1;

    // 获取观测数并存储到缓冲区中
    get_one_para(TOV_PARA_ID_OBSER_NUM_OFFSET, &obser_num);
    data_buff[offset] = obser_num;
    offset++;

    // 循环获取每个观测点并存储到缓冲区中
    for(int i = 0; i < obser_num; i++)
    {
        get_one_para(TOV_PARA_ID_OBSER_OFFSET + i, &obser_point);
        data_buff[offset] = obser_point;
        offset++;
    }

    // 更新缓冲区的第一个字节为观测数加1
    data_buff[0] = obser_num + 1;

    // 设置命令缓冲区的数据长度
    ((cmd_buf_t*)cmd_buff)->data_len = offset;
    save_all_para();
    // 返回成功
    return SUCCESSFUL;
}



int parse_para_2(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;
    cmd_buf_t* cmd_buf_tmp = ((cmd_buf_t *)cmd_buff);
    unsigned char obser_num = 0;
    unsigned char obser_point[10] = {0};
    unsigned int offset = 1;
    int i = 0;
    int ret = 0;

    // 获取观测点数量
    obser_num = data_buff[offset];
    offset++;

    // 检查观测点是否有重复
    for(i = 0; i < obser_num; i++)
    {
        obser_point[i] = data_buff[offset];
        offset++;
        for(int j = 0; j < i; j++)
        {
            if(obser_point[j] == obser_point[i])
            {
                cmd_buf_tmp->rtn = RTN_INVALID_DATA;
                return FAILURE;
            }
        }
    }

    // 设置观测点数量参数
    ret |= set_one_para(TOV_PARA_ID_OBSER_NUM_OFFSET, &obser_num, FALSE, TRUE);

    // 设置每个观测点的参数
    for(i = 0; i < obser_num; i++)
    {
        ret |= set_one_para(TOV_PARA_ID_OBSER_OFFSET + i, &obser_point[i], FALSE, TRUE);
    }

    // 检查设置参数是否成功
    if(ret < 0)
    {
        cmd_buf_tmp->rtn = RTN_INVALID_DATA;
        return FAILURE;
    }

    // 保存所有参数
    save_all_para();

    rt_kprintf("parse_para_2!!!!\n");
    // 发布更新消息
    pub_msg_to_thread(UPDATE_CHAN_VALID, NULL, 0);

    return SUCCESSFUL;
}




int pack_real_data(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;
    unsigned int offset = 0;
    cmd_buf_t cmd_buff_tmp = {0};
    parse_layer_param_t parse_layer_param = {0};
    cmd_t cmd_match = {0};
    unsigned char buff[512] = {0};
    
    // 段1数据
    data_buff[offset] = 3;
    offset++;

    // 建链主机地址
    data_buff[offset] = get_host_addr();
    offset++;

    // 段2数据
    rt_memset(buff, 0x00, sizeof(buff));
    rt_memset(&parse_layer_param, 0x00, sizeof(parse_layer_param));
    cmd_match.cmd_data_pack_id_ver = &cmd_pack_info[2];
    cmd_buff_tmp.cmd = &cmd_match;
    parse_layer_param.data = buff;
    parse_layer_param.data_len = sizeof(buff);
    if(SUCCESSFUL != universal_pack_id_version(&cmd_buff_tmp, &parse_layer_param))
    {
        return FAILURE;
    }
    
    rt_memcpy_s(&data_buff[offset], R_BUFF_LEN_1024, buff, parse_layer_param.data_offset);
    offset += parse_layer_param.data_offset;

    ((cmd_buf_t*)cmd_buff)->data_len = offset;
    return SUCCESSFUL;
}



int pack_sys_time_data(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    time_t t = time(RT_NULL);

    time_base_t  pt_time={0};
    int offset=0;
    get_time(&pt_time);

    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;
    data_buff[offset++] = 7;
    put_time_t_to_buff(&data_buff[offset], t);
    offset += 7;

    ((cmd_buf_t*)cmd_buff)->data_len=offset;

    return SUCCESSFUL;
}

int parse_sys_time_data(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    int offset = 1;
    time_base_t tm = {0};

    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;

    get_time_from_buff(&tm, &data_buff[offset]);
    if( check_time_range(tm) == FAILURE )
    {
        LOG_E("%s | %d | set RTC time failed \n", __FUNCTION__ , __LINE__);
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA ;
        return FAILURE;
    }
    time_t tTime = timestruct_to_time_t(&tm);
    if(tTime < 0)
    {
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA ;
        return FAILURE;
    }
    if(stime(&tTime) == FAILURE )
    {
        LOG_E("%s | %d | set RTC time failed \n", __FUNCTION__ , __LINE__);
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA ;
        return FAILURE;
    }
    return SUCCESSFUL;
}



int load_manu_info()
{
    manu_info_t manu_info = {0};
    date_base_t date_base = {0};
    struct stat file_info = {0};
    char buff[40] = {0};

    rt_memcpy_s(buff, sizeof(buff), COMM_PRO_VER, sizeof(COMM_PRO_VER));
    set_one_data(TOV_DATA_ID_COMM_PRO_VER, buff);

    rt_memset_s(buff, sizeof(buff), 0x00, sizeof(buff));
    rt_memcpy_s(buff, sizeof(buff), SOFTWARE_NAME, sizeof(SOFTWARE_NAME));
    set_one_data(TOV_DATA_ID_SOFTWARE_NAME, buff);

    rt_memset_s(buff, sizeof(buff), 0x00, sizeof(buff));
    rt_memcpy_s(buff, sizeof(buff), SOFTWARE_VER, sizeof(SOFTWARE_VER));
    set_one_data(TOV_DATA_ID_SOFTWARE_VER, buff);

    date_base.year = SOFTWARE_DATE_YEAR;
    date_base.month = SOFTWARE_DATE_MONTH;
    date_base.day = SOFTWARE_DATE_DAY;
    set_one_data(TOV_DATA_ID_SOFTWARE_DATE, &date_base);
    if(storage_stat(MANU_INFO, &file_info) != SUCCESSFUL)
    {
        rt_memcpy_s(manu_info.manu_name, sizeof(manu_info.manu_name), FACTORY_NAME, sizeof(FACTORY_NAME));
    }
    else
    {
        handle_storage(read_opr, MANU_INFO, (unsigned char*)&manu_info, sizeof(manu_info_t), 0);
    }

    set_one_data(TOV_DATA_ID_FACTORY_NAME, manu_info.manu_name);
    set_one_data(TOV_DATA_ID_HARDWARE_VER, manu_info.hardware_ver);
    set_one_data(TOV_DATA_ID_SERIAL, manu_info.serial);
    return SUCCESSFUL;
}

int parse_manu_info(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    int offset = 1;
    manu_info_t manu_info = {0};
    unsigned char *data_buff = ((cmd_buf_t*)cmd_buff)->buf;

    if(handle_storage(read_opr, MANU_INFO, (unsigned char*)&manu_info, sizeof(manu_info_t), 0) != SUCCESSFUL)
    {
        LOG_E("manu_info file not exist");
    }

    rt_memset_s(manu_info.manu_name, sizeof(manu_info.manu_name), 0x00, sizeof(manu_info.manu_name));
    rt_memcpy_s(manu_info.manu_name, sizeof(manu_info.manu_name), &data_buff[offset], sizeof(manu_info.manu_name));
    offset += sizeof(manu_info.manu_name);

    rt_memset_s(manu_info.hardware_ver, sizeof(manu_info.hardware_ver), 0x00, sizeof(manu_info.hardware_ver));
    rt_memcpy_s(manu_info.hardware_ver, sizeof(manu_info.hardware_ver), &data_buff[offset], sizeof(manu_info.hardware_ver));
    offset += sizeof(manu_info.hardware_ver);

    rt_memset_s(manu_info.serial, sizeof(manu_info.serial), 0x00, sizeof(manu_info.serial));
    rt_memcpy_s(manu_info.serial, sizeof(manu_info.serial), &data_buff[offset], sizeof(manu_info.serial));

    if(handle_storage(write_opr, MANU_INFO, (unsigned char*)&manu_info, sizeof(manu_info_t), 0) != SUCCESSFUL)
    {
        return FAILURE;
    }
    set_one_data(TOV_DATA_ID_FACTORY_NAME, manu_info.manu_name);
    set_one_data(TOV_DATA_ID_HARDWARE_VER, manu_info.hardware_ver);
    set_one_data(TOV_DATA_ID_SERIAL, manu_info.serial);

    return SUCCESSFUL;
}


int parse_get_inrly(void* dev_inst, void* cmd_buff)
{
    char relay_status = 0;
    relay_status = get_pin(IN_RELAY_PIN);
    set_one_data(TOV_DATA_ID_INRLY_STATUS, &relay_status);
    return SUCCESSFUL;
}

int parse_ctrl_outrly_high(void* dev_inst, void* cmd_buff)
{
    set_pin(OUT_RELAY_PIN, PIN_HIGH);
    return SUCCESSFUL;
}

int parse_ctrl_outrly_low(void* dev_inst, void* cmd_buff)
{
    set_pin(OUT_RELAY_PIN, PIN_LOW);
    return SUCCESSFUL;
}


int parse_clear_extreme_data(void* dev_inst, void* cmd_buff)
{
    rt_kprintf("parse_clear_extreme_data\n");
    unsigned char auth_code = 0x10;
    delete_record_data_new(auth_code,EXTREME_DATA_INDEX);
    //清除内存中的数据
    clear_memory_exterme_data();
    return SUCCESSFUL;
}



int parse_restore_fact(void* dev_inst, void* cmd_buff)
{
    cmd_buf_t send_cmd_buff;
    unsigned char auth_code = 0x10;
    rt_memset_s(&send_cmd_buff, sizeof(cmd_buf_t), 0, sizeof(cmd_buf_t));
    rt_kprintf("parse_restore_fact\n");
    storage_unlink(ALMPARA_PART);
    storage_unlink(NUMPARA_PART);
    storage_unlink(STRPARA_PART);

    delete_record_data_new(auth_code,EXTREME_DATA_INDEX);//极值文件
    delete_record_data_new(auth_code,RECORD_DATA_INDEX);//多条录波数据文件
    send_cmd_buff.cmd = ((cmd_buf_t *)cmd_buff)->cmd;
    cmd_send(dev_inst, &send_cmd_buff);
    rt_thread_mdelay(100);
    rt_hw_cpu_reset();
    return SUCCESSFUL;
}


int parse_clear_fault_data(void* dev_inst, void* cmd_buff)
{
    rt_kprintf("parse_clear_fault_data\n");
    unsigned char upfile_sta = 0;
    get_one_data(TOV_DATA_ID_RECORD_UPFILE_STATUS, &upfile_sta);
    if(upfile_sta == TRUE)
    {
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA;
        return SUCCESSFUL;
    }
    unsigned char ctrl_sample_switch[2] = {0};
    unsigned char auth_code = 0x10;
    delete_record_data_new(auth_code,RECORD_DATA_INDEX);//多条录波数据文件
    //清除内存中的数据
    clear_memory_record_data();
    ctrl_sample_switch[0] = TRUE;
    ctrl_sample_switch[1] = TRUE;
    pub_msg_to_thread(CTRL_SAMPLE_SWITCH, &ctrl_sample_switch, sizeof(ctrl_sample_switch));
    return SUCCESSFUL;
}

int parse_clear_data_reboot(void* dev_inst, void* cmd_buff)
{
    rt_kprintf("parse_clear_data_reboot\n");
    unsigned char auth_code = 0x10;
    cmd_buf_t send_cmd_buff;
    rt_memset_s(&send_cmd_buff, sizeof(cmd_buf_t), 0, sizeof(cmd_buf_t));

    delete_record_data_new(auth_code,EXTREME_DATA_INDEX);//极值文件
    delete_record_data_new(auth_code,RECORD_DATA_INDEX);//多条录波数据文件

    RETURN_VAL_IF_FAIL(cmd_buff != NULL, FAILURE);
    send_cmd_buff.cmd = ((cmd_buf_t *)cmd_buff)->cmd;
    cmd_send(dev_inst, &send_cmd_buff);

    rt_thread_mdelay(100);
    rt_hw_cpu_reset();
    return SUCCESSFUL;
}
