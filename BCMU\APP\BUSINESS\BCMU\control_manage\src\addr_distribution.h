#ifndef _ADDR_DISTRIBUTION_H
#define _ADDR_DISTRIBUTION_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

#include <rtthread.h>
#include "device_num.h"

#define BCMU_ADDR_DISTR    1
#define DC_DC_ADDR_DISTR   2
#define BMU_ADDR_DISTR     3

#define ADDR_DISTR_FAILURE 0
#define ADDR_DISTR_SUCCESS 1
#define ADDR_DISTR_STARTED 2

//站址分配状态定义
typedef enum {
    STATE_WAIT_START = 0,        //等待启动状态
    STATE_BROAD_TO_BMU,          //广播通知BMU状态
    STATE_SET_BMU_IO,            //设置BMU1低电平
    STATE_BROAD_ADDR,            //广播地址0
    STATE_WAIT_DISTR,            //等待地址分配完成
    STATE_BMU_VOLT_SAMPLE,       //采集BMU电压
    STATE_DCDC_VOLT_SAMPLE,      //采集DC/DC电压
    STATE_CALC_TOPO,             //计算拓扑结构
    STATE_MAX
}state_id_e;

#define TOPO_VALID      1
#define TOPO_INVALID    0
#define BATT_MOD_VOLT   32.0

//电池簇拓扑结构
typedef struct
{
    unsigned char topo_valid;                                   //拓扑数据是否有效，1：有效，0：无效
    unsigned char mod_count;                                    //电池模块总数
    unsigned char cluster_count;                                //簇数
    unsigned char mod_addr[BATT_CLUSTER_NUM][BATT_CLUSTER_MOD_NUM];    //每簇电池模块地址
    unsigned char mod_num[BATT_CLUSTER_NUM];                           //每簇电池模块数目
}cluster_topology_t;

//站址分配时采样电压数据定义
typedef struct
{
    unsigned char mod_count;                                    //电池模块总数
    unsigned char cluster_count;                                //簇数
    float mod_voltage[BATT_MOD_NUM];                                 //电池模块采样电压
    float cluster_voltage[BATT_CLUSTER_NUM];                           //簇采样电压
}addr_distribution_volt_t;

void init_addr_distr();//control_manage
void bmu_volt_sample(void *data);//没有调用
void calc_topo(void* data);//没有调用
void get_cluster_topology(cluster_topology_t *cluster_topo);//外部调用
void addr_distribution(void* parameter);
void set_wait_start_flag();

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _ADDR_DISTRIBUTION_H