#include "alarm_register.h"
#include "alarm_manage.h"
#include "alarm_id_in.h"
#include "realdata_id_in.h"
#include "alarm_config_in.h"

/* 通过告警码清除实时告警列表 */

static unsigned short alarm_clean_by_code_tab[] = {0xffff};

static alarm_manage_info_t robot_manage = {
    ALARM_ID_OFFSET_MAX - 1,        //告警码的个数
    ana_alm_config_tab,             //模拟量告警表
    dig_alm_config_tab,             //状态量告警表
    self_alm_config_tab,            //自定义告警表
    alm_shielded,                    //屏蔽关系表
    alarm_clean_by_code_tab,        // 以告警码清除相关告警列表
};

void register_robot_alarm()
{
    register_alarm_manage_info(&robot_manage);
}

