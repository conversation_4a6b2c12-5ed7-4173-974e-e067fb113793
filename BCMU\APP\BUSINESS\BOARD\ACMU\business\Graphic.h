/**************************************************************************
* 版权信息：（C）2004-2005，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：ZXDU68 PSU板软件
* 文件名称：graphic.h
* 文件说明：液晶显示模块头文件
* 作    者：熊勇
* 版本信息：V4.0
* 设计日期：2004-08-01
* 修改记录：
* 日    期		版	本		修改人		修改摘要      
* 其他说明：
***************************************************************************/

/***********************  常量定义  ************************/

#define     XZK_ADDR_HI     (( volatile char * )0xF720)
#define     XZK_ADDR_CS     (( volatile char * )0xF000)

#define  XZK_ROM_ASC8      0       //size=1k
#define  XZK_ROM_ASC16     0x00400 //size=2k
#define  XZK_ROM_ICON      0x00C00 //size=5k
#define  XZK_ROM_BMP       0x02000 //size=56k  --- 1*64KB
#define  XZK_ROM_CHINESE   0x10000 //size=32k
#define  XZK_ROM_ENGLISH   0x18000 //size=32k  --- 2*64KB
 
#define  XZK_ROM_HZK14     0x40000 //size=256k --- 5~8*64kb

#define	TRUE			1	
#define	FALSE			0

#define	HZ_COUNT		255		// 汉字字符数量，当字库汉字修改时，要修改此参数，运行字库生成程序时，可以得到这个参数
#define	WIDTH_ASC		7
#define	WIDTH_HZ		14

#define		PORT_ORDER	0
#define		PORT_DATA	1

#define  MAX_LCD_WAIT         20

#define     IMAGE_SIZE      1024
#define     MIRROR_SIZE   (2*IMAGE_SIZE)

#define     NEWDIS         1      //新的显示系统

#ifdef   NEWDIS

#define	ADDR_DIGITOUTLED   (*(unsigned char *)0xFC78) //指示灯输出控制信号
#define	ADDR_DIGITOUTLCD   (*(unsigned char *)0xFC10) //其他输出控制信号


#define	LCD_BASE_LEFT			((unsigned char *)0xFDC0)	//LCD左半屏地址 
#define	LCD_BASE_RIGHT		((unsigned char*)0xFDC8)	//LCD右半屏地址


#define  DISPLAY_ON           0x3f	    //显示开
#define  DISPLAY_OFF          0x3e		//显示关

#define  DISPLAY_LINE0        0xc0	    //起始行位置第0行


#define  DISPLAY_FIRSTPAGE    0xB8         //第0页
#define  DISPLAY_FIRSTLINES   0x40

#define  PAGES                0x08          //共4页
#define  LINES                0x40          //每片SED1520控制61列
//#define LINES                   0x3d

#define  LCDSTARTPAGES        2
#define  LCDENDPAGES          5
#define  LCDSTARTLINES        3
#define  LCDENDLINES          0x3c
#define		MAX_X			128
#define		MAX_Y			64

#else
#define		LCD_BASE_LEFT		((unsigned char*)0xea00)
#define		LCD_BASE_RIGHT	((unsigned char *)0xec00)

#define  DISPLAY_ON           0xaf	    //显示开
#define  DISPLAY_OFF          0xae		//显示关

#define  DISPLAY_LINE0        0xc0	    //起始行位置第0行
#define  DISPLAY_ADC_SELECT   0xa0          //正向排列控制
#define  DISPLAY_DRIVE_STATIC 0xa4          //关休闲状态
#define  DISPLAY_DUTY_SELECT  0xa9          //1/32占空比，两片SED1520级联
#define  DISPLAY_SOFT_RESET   0xe2	    //软件复位
#define  DISPLAY_FIRSTPAGE    0xB8         //第0页
#define  PAGES                0x04          //共4页
#define  LINES                0x3d          //每片SED1520控制61列
#define		MAX_X			122
#define		MAX_Y			32
#endif


#define		CURSOR_OFF		0
#define		CURSOR_ON		1

#define		EMPTY_FILL		0
#define		SOLID_FILL		1
#define		DOT_FILL		2
#define		LIGHT_FILL		3

#define		SMR_NULL		0
#define		SMR_ON    		1
#define		SMR_OFF 		2
#define		SMR_ALARM 		3

#define		ICON_REAL		0
#define		ICON_ALARM		1
#define		ICON_PARA		2
#define		ICON_CONTROL	3
#define		ICON_RECORD	4

#define     	ICON_QUERY    	5
#define		ICON_CLOCK		6
#define		ICON_VERSION	7
#define		ICON_KEY		8
#define		ICON_SAVE	9
#define		ICON_CRY		10
#define		ICON_SMILE	11

#define		ICON_ARROW	12
#define		ICON_CHANGE	13

#define		ICON_OFF 	    14
#define		ICON_ON         15
#define		ICON_HELP	    16
#define		ICON_TRACE	    17
#define		ICON_MAIN	    18
#define		ICON_SAVESCREEN 19
#define     ICON_WARNING    20
#define		ICON_BATTERY    21
#define     ICON_AC1        22
#define     ICON_AC2        23

#define		ASC_SMR_NO		1  	//整流器不在位
#define		ASC_SMR_OFF		2 	//整流器关机
#define		ASC_SMR_ALARM	3 	//整流器告警
#define		ASC_SMR_OK		4 	//整流器在位


/*********************  函数原型定义  **********************/
void    SaveScreen(void);
void    RestoreScreen(void);
unsigned char *	GetDestStr(unsigned short wId);
void 	MoveMouse(unsigned char x, unsigned char y);
void	InitCursor(unsigned char ucWidth, unsigned char ucHeight, unsigned char ucStyle);
void 	MoveCursor(unsigned char x, unsigned char y);
void    FlashCursor(void);
void 	ClrScreen(void);
void	InitLcdRecover( void );
void    DispCh8(unsigned char x, unsigned char y, unsigned char ch);
void 	DispCh16(unsigned char x, unsigned char y, unsigned char ch);
void	TextOut8(unsigned char x, unsigned char y, unsigned char *pStr);
void	TextOut16(unsigned char x, unsigned char y, unsigned char *pStr);
void	Line(unsigned char x1, unsigned char y1,unsigned char x2, unsigned char y2);
void	DashedScroll(float fPara,float fMax,float fMin);
void	Rectangle(unsigned char x1, unsigned char y1,unsigned char x2, unsigned char y2);
void	Bar(unsigned char x1, unsigned char y1,unsigned char x2, unsigned char y2);
void	FillRec(unsigned char x1, unsigned char y1, unsigned char x2, unsigned char y2,unsigned char ucStyle);
void	PutIcon(unsigned char x, unsigned char y, unsigned char  ucIconId);
//void	TextOut24(unsigned char x, unsigned char y, char * pStr);
void	DisplayBmp(unsigned char ucId);
void	LcdDisplay(void);
unsigned char *  GetImageBuf( unsigned char   ucId);
void	LCD_Power( unsigned char ucCtrl );
void	SpecicalDisplay(unsigned char	ucStyle);
char *	GetHisPeakSpec(void);

