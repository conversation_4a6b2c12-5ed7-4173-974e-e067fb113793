#include "common.h"
#include "algorithmAES.h"
#include "sample.h"
#include "realAlarm.h"
#include "battery.h"
#include "para.h"
#include "comm.h"
#include "protocol.h"
#include "stdio.h"
#include "commBdu.h"
#include "apptest.h"
#include "apptest_product.h"
#include "CommCan.h"
#include "wireless.h"
#include "led.h"
#include "fileSys.h"
#include "ism330dhcx.h"
#include "utils_rtthread_security_func.h"


static T_SysPara   s_tSysPara;
static struct rt_device *s_BattChip_device;

/*********************  静态函数原型定义  **********************/
static void DealDcCommand( BYTE   ucPort );
static void DealCommonCommand( BYTE ucPort );

//APPTEST命令
static void ChipcheckRtcandFlash(BYTE ucPort);
static void GetDcAngData(BYTE ucPort);
static void GetDcStatus(BYTE ucPort);
static void SetBMSAdjustData( BYTE ucPort );
static void GetBmsPara(BYTE ucPort);
static void SetBmsPara(BYTE ucPort);
static void GetBmsParaNew(BYTE ucPort);
static void SetBmsParaNew(BYTE ucPort);
static void GetAlarmLevel(BYTE ucPort);
static void SetAlarmLevel(BYTE ucPort);
static void GetAlarmRelay(BYTE ucPort);
static void SetAlarmRelay(BYTE ucPort);
static void GetSpecificPara(BYTE ucPort);
static void SetSpecificPara(BYTE ucPort);
static const T_CmdFuncStruct* GetCmdFnucStruct(BYTE segment);
static void SetBMSAdjustDataNew(BYTE ucPort);
static void GetBMSAdjustData(BYTE ucPort);
Static void SetBMSChgVolt(BYTE ucPort);
static void GetTestData(BYTE ucPort);
static void RecvSysParaById(BYTE ucPort);
static void SendSysParaById(BYTE ucPort);
Static void GetCustomerName(BYTE ucPort); //获取客户名称
Static void SetCustomerName(BYTE ucPort); //设置客户名称

/********************************命令码处理START**************************************/ 
//CID1处理
const T_CmdFuncStruct s_atCID1AllAppTestFuncTable[] =
{
    {CID1_COMMON, DealCommonCommand, 0},  // 公共部分CID1 = 0X40
    {CID1_DC, DealDcCommand, 0},          // 直流部分CID1 = 0X42
    //end flag
    {0x00, 0x0000, 0x00},
};

    //APPTEST COMMON_PART
const T_CmdFuncStruct   s_atCID2AllAppTestTable[] = 
{
    // 公共测试命令
    {GET_TIME_AT, SendSysTime, 0},
    {SET_TIME_AT, RecSysTime, 14},
    {GET_FACTORY_INFO_AT, GetFactoryInfo, 0},
	{SET_FIRST_BOOT_TIME, SetFirstBootTime, 14},
	{GET_FIRST_BOOT_TIME, GetFirstBootTime, 0},
	{SET_FAC_TIME, SetFacTime, 14},
	{GET_FAC_TIME, GetFacTime, 0},
    //中试增加的命令
    {START_CAN_BUS_TEST_AT, RecCanBusCtrl, 0},
    {GET_CAN_BUS_RESULT_AT, SendCanReport, 0},
    {SET_CSU_ID_AT, SetCsuId, 30},
    {GET_MAC_AT,GetHWMacAddr,0},
    {SET_MAC_AT,SetHWMacAddr,12},
    {START_NET_TEST_AT,SetNetTest,2},

    //new add
    {GET_PROTOCOL_VER_AT, SendProtocolVer, 0},

    {CHIP_TEST_AT, ChipcheckRtcandFlash, 0},
    {SET_FAT_ID_AT, SetFactoryAcceptanceTest, 18},
    {GET_FAT_ID_AT, GetFactoryAcceptanceTest, 0},
    {SET_QUIT_TEST_AT, SetQuitAPPTest, 0},
    {GET_CSU_ID_AT, GetCsuId, 0},
    {START_BMS_CTRL_AT, SendBMSCtrl, 12},
    {GET_BMS_PACK_INFO_AT, GetBMSPACKFactoryInfo, 0},
    {SET_BMS_PACK_INFO_AT, SetBMSPACKFactoryInfo, 160},
    {SET_BMS_BATT_CAP_AT, SetBMSBattCap, 8},

    {SET_APPTEST_RESET_AT, RecResetCmd, 0},
    {BMS_ADJUST_DATA_AT, SetBMSAdjustData, 6},
    {SET_DEL_HISDATA_AT, SetDelHisData, 6},
    {GET_BMS_ID_AT, GetBmsId, 0},
    {SET_BMS_ID_AT, SetBmsId, 30},
    {GET_BDU_FACT_AT, GetBDUFact, 0},
    {COMM_LI_EFFICIENCY_TEST_AT, SetEfficiencyTest, 2},
    {GET_CELL_FACT_INFO_AT, SendCellFactInfo, 0},
    {SET_CELL_FACT_INFO_AT, RecCellFactInfo, 0},
    {GET_BMS_FACT_AT,GetBmsFactInfo, 0},
    {SET_BMS_FACT_AT, SetBmsFactInfo, 42},
    {SET_BMS_FACT_AT_NEW,SetBmsFactInfoNew,66},
    {GET_BMS_FACT_AT_NEW,GetBmsFactInfoNew,0},
	{SET_CUSTOMER_NAME_AT,SetCustomerName,34},
    {GET_CUSTOMER_NAME_AT,GetCustomerName,0},


    {GET_BTN_COUNT, GetBtnCount, 0},
    {SET_BMS_SHUTDOWN, SetBMSShutdown, 0},
    {BMS_ADJUST_DATA_NEW,SetBMSAdjustDataNew, 10},
    {REFRESH_DEFAULT_PARA, RefreshDefaultPara, 0},
    {SET_BDU_OPENCYCLE, SetOpenCycleCtrl, 2},
    {GET_BDU_ID_AT, GetBduIdNo, 0},
    {SET_BDU_ID_AT, SetBduIdNo, 8},
    {GET_BDPB_ID, GetBDPBSn, 0},
    {SET_BDPB_ID, SetBDPBSn, 30},
    {GET_MAIN_ID, GetMainSn, 0},
    {SET_MAIN_ID, SetMainSn, 30},

    {GET_BMS_BATT_CAP,GetBMSBattCap,0},
    {SET_RELEASE_LOCK, SetBMSReleaseLock,0},

    {GET_HARDWARE_PARA, GetBMSHardwarePara,0},
    {SET_HARDWARE_PARA, SetBMSHardwarePara,4},

    {SET_BDU_HEAT, SetHeatCtrl, 2},
    {GET_BDU_FACT_HEAT, GetBDUFactHeat, 0},

    {START_BALA_CIRC_CHECK, StartBalanceCircCheck, 0},
    {GET_BALA_CIRC_FAULT, GetBalanceCircFault, 0},

    {GET_HARDWARE_INFORMATION_EX, GetHardwareInfo, 0},
    {SET_HARDWARE_INFORMATION_EX, SetHardwareInfo, 40},

    //end flag
    {0x00, 0x0000, 0x00},
};

//APPTEST DC_PART
const T_CmdFuncStruct   s_atCID2BMSDCTestTable[] =
{
    // 直流配电部分测试命令
    {GET_ANALOG_INT_AT, GetDcAngData, 0},      //直流部分定点数据
    {GET_SWITCH_AT, GetDcStatus, 0},           //直流部分开关状态
    {REMOTE_CTRL_AT, SetDcControl, 16},        //输出干接点控制
    {GET_THEFT_TEST_AT, GetTheftTest, 0},      //获取防盗端口状态
    {GET_PARA_INT_AT, GetBmsPara, 0},          //获取bms参数
    {SET_PARA_INT_AT, SetBmsPara, 6},          //设置bms参数
    //{GET_SLEEP_TEST_AT, GetSleepStatus, 0},      //休眠功能测试
    {GET_ALARM_LEVEL, GetAlarmLevel, 0},
    {SET_ALARM_LEVEL, SetAlarmLevel, 6},
    {GET_ALARM_RELAY_APPTEST, GetAlarmRelay, 0},
    {SET_ALARM_RELAY_APPTEST, SetAlarmRelay, 6},
    {GET_PARA_NEW_APPTEST, GetBmsParaNew, 0},
    {SET_PARA_NEW_APPTEST, SetBmsParaNew, 6},
    {GET_BDU_ADJ_PARA, GetBMSAdjustData,0},
    {GET_SPECIFIC_PARA, GetSpecificPara, 0},
    {SET_SPECIFIC_PARA, SetSpecificPara, 6},
    {GET_BMS_CHARGE_VOLT, GetBMSChgVolt, 0},
    {SET_BMS_CHARGE_VOLT, SetBMSChgVolt, 6},
    {GET_TEST_DATA ,GetTestData, 0},
    {SET_BDU_TEST_PARA, SetBduAppTestPara, 0},
    {GET_BAT_PARA_BY_ID, SendSysParaById, 4},
    {SET_BAT_PARA_BY_ID, RecvSysParaById, 0},
    {SET_BAT_BMS_INFO, SetBmsInfo, 0},
    {GET_BAT_BMS_INFO, GetBmsInfo, 0},

    // end flag
    {0x00, 0x0000, 0x00},
};

const T_CmdFuncStruct* s_auAppTestProtocl[] =
{s_atCID2AllAppTestTable, s_atCID2BMSDCTestTable};

/********************************命令码处理END**************************************/   
/****************************************************************************
* 函数名称：
* 输入参数： 
* 返 回 值： 
* 功能描述：如果版本号为偶数，版本号加1，否则不变
* 作    者  ：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
yang_an,整理协议修改，20110511
***************************************************************************/
static const T_CmdFuncStruct* GetCmdFnucStruct(BYTE segment)
{ 
    if (segment < sizeof(s_auAppTestProtocl)/sizeof(s_auAppTestProtocl[0]))
    {
        return s_auAppTestProtocl[segment];
    }
    return NULL;
}

/****************************************************************************
* 函数名称：DealApptestCommand()
* 调    用：无
* 被 调 用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：根据CID1和CID2，调用相应的命令处理程序
* 作    者  ：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
void DealApptestCommand(T_CommStruct *ptComm)
{
    WORD i = 0;

    for ( i = 0; i < HIGH_SEND_LEN; i++ )    /* 清空响应缓冲区 */
    {
        s_tProtocol.aucSendBuf[i] = 0;
    }
    /* 将响应包的LENID预置为0 */
    s_tProtocol.wSendLenid  = 0;
    if ( (GET_PROTOCOL_VER_AT == s_tProtocol.ucCID2) && (CID1_COMMON == s_tProtocol.ucCID1) )
    {
        return;
    }

    i = 0;
    while(s_atCID1AllAppTestFuncTable[i].ucCmdCode)
    {
        if ( s_atCID1AllAppTestFuncTable[i].ucCmdCode == s_tProtocol.ucCID1 )
        {
            (*s_atCID1AllAppTestFuncTable[i].func)( ptComm->ucPortType );
            return;
        }
        i++;
    }

    // CID1错，返回RTN_WRONG_CID1
    s_tProtocol.ucRTN   = RTN_WRONG_CID1;

    return;
}

/****************************************************************************
* 函数名称：DealDcCommand()
* 调    用：无
* 被 调 用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：根据CID1和CID2，调用相应的命令处理程序
* 作    者  ：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
static void DealDcCommand( BYTE ucPort )
{
    WORD i = 0;
    const T_CmdFuncStruct *s_atCID2BMSDCTestTable = GetCmdFnucStruct(CID2DC);

    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }
    while (s_atCID2BMSDCTestTable != NULL && s_atCID2BMSDCTestTable[i].ucCmdCode)
    {
        if ( s_atCID2BMSDCTestTable[i].ucCmdCode == s_tProtocol.ucCID2 )
        {
            if (s_atCID2BMSDCTestTable[i].ucDataLen != s_tProtocol.wRecLenid)
            {
                if((s_tProtocol.ucCID2 == SET_PARA_INT_AT)
                    || (s_tProtocol.ucCID2 == SET_PARA_NEW_APPTEST)
                    || (s_tProtocol.ucCID2 == SET_SPECIFIC_PARA)//DC段设置参数
					|| (s_tProtocol.ucCID2 == SET_BDU_TEST_PARA)
					|| (s_tProtocol.ucCID2 == SET_BAT_PARA_BY_ID)
					|| (s_tProtocol.ucCID2 == SET_BAT_BMS_INFO))
                {

                }
                else
                {
                    s_tProtocol.ucRTN   = RTN_WRONG_COMMAND;
                    return;
                }
            }
            (*s_atCID2BMSDCTestTable[i].func)(ucPort);
            return;
        }
        i++;
    }
    s_tProtocol.ucRTN   = RTN_WRONG_CID2;       /* CID2无效 */
}

static void DealCommonCommand( BYTE ucPort )
{
    WORD i = 0;
    const T_CmdFuncStruct *s_atCID2CommonTestTable = GetCmdFnucStruct(CID2ALL);

    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }

    i = 0;
    while (s_atCID2CommonTestTable != NULL && s_atCID2CommonTestTable[i].ucCmdCode)
    {
        if ( s_atCID2CommonTestTable[i].ucCmdCode == s_tProtocol.ucCID2 )
        {
            if (s_atCID2CommonTestTable[i].ucDataLen != s_tProtocol.wRecLenid )
            {
                if (s_tProtocol.ucCID2 == SET_CELL_FACT_INFO_AT)
                { }
                else
                {
                    s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
                    return;
                }
            }
            (*s_atCID2CommonTestTable[i].func)(ucPort);
            return;
        }
        i++;
    }

    s_tProtocol.ucRTN   = RTN_WRONG_CID2;       /* CID2无效 */
}


/****************************************************************************
* 函数名称：ChipcheckRtcandFlash(BYTE ucPort)
* 调    用：无
* 被 调 用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：获取芯片测试信息
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
static void ChipcheckRtcandFlash(BYTE ucPort)
{
    BYTE *p = s_tProtocol.aucSendBuf;
	WORD wNorFlashFlag = 0XA55A;
	CHAR chipNameMt[6] = {'M','T','9','8','1','8'};
	CHAR chipNameBq[7] = {'B','Q','7','6','9','5','2'};

    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }

//    if(WriteEEPROM(APPTEST_EEPROM_OFFSET, FAULT) == True)
//    {
//    	if(ReadEEPROM(APPTEST_EEPROM_OFFSET) == FAULT)
//    	{
//    		ucEEPROMFlag = 1;
//    	}
//    	else
//    	{
//    		ucEEPROMFlag = 0;
//    	}
//    }
    //*p++直接赋值
    *p++ = 4;                   //the number of  pins
    *p++ = 1;                   //rtc
    writeNorFlashTestPara(&wNorFlashFlag);
    wNorFlashFlag = 0;
    readNorFlashTestPara(&wNorFlashFlag);
#ifndef KW_CHECK
    if( 0XA55A != wNorFlashFlag )
    {
        *p++ = 0x00;            //norflash 异常
    }
    else
    {
        *p++ = 0x01;            //norflash 正常
    }
#endif
    *p++ = 1;          //EEPROM

    *p++ = 1;          // 4G

#ifndef UNITEST
	s_BattChip_device = rt_device_find("mt9818");
	if(RT_NULL == s_BattChip_device)
    {
        s_BattChip_device = rt_device_find("bq76952");
		if(s_BattChip_device != RT_NULL)
		{
			MemsetBuff(p, chipNameBq, 7, 16, 0x00);
		}
    }
	else
	{
		MemsetBuff(p, chipNameMt, 6, 16, 0x00);
	}
#endif
	p += 16;

    s_tProtocol.wSendLenid  = (WORD) ((p-s_tProtocol.aucSendBuf)*2);
    // s_tProtocol.wSendLenid = wSendLen;//为解决KW问题
    return;
}


/****************************************************************************
* 函数名称：GetDcAngData(BYTE ucPort)
* 调    用：无
* 被 调 用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：获取直流模拟量定点数据
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
static void GetDcAngData(BYTE ucPort)
{
	BYTE *p = NULL;
	BYTE i = 0;
	BYTE wCellVoltNum = 0;
	BYTE wCellTempNum = 0;
	T_BCMDataStruct    tBCMAnaData;
	T_DCPara tOutPara;
	
	T_DCRealData tDcRealData;
	T_HardwareParaStruct tHWPara;

    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }
	rt_memset(&tBCMAnaData, 0, sizeof(T_BCMDataStruct));
	rt_memset(&tOutPara, 0, sizeof(T_DCPara));
	rt_memset(&tDcRealData, 0, sizeof(T_DCRealData));
    rt_memset(&tHWPara, 0, sizeof(T_HardwareParaStruct));

	GetRealData( &tBCMAnaData );
	GetBduPara(&tOutPara);
	GetBduReal(&tDcRealData);
    readBmsHWPara(&tHWPara);

    if (tHWPara.ucCellVoltNum <= CELL_VOL_NUM_MAX) {
		wCellVoltNum = tHWPara.ucCellVoltNum;
	}

	if (tHWPara.ucCellTempNum <= CELL_TEMP_NUM_MAX) {
		wCellTempNum = tHWPara.ucCellTempNum;
	}

	p  = s_tProtocol.aucSendBuf;
	rt_memset(s_tProtocol.aucSendBuf, 0x00, sizeof(s_tProtocol.aucSendBuf));

	/*DATA_FLAG*/
	*p++ = GetDataFlag( BCM_ALARM, ucPort );                    //DATA_FLAG

	/*DATA_DATAI*/
	rt_memset(p, FLOAT_INVALID, 4);                 //直流输出电压
	p += 4;
	rt_memset(p, FLOAT_INVALID, 4);                 //负载总电流
	p += 4;
	rt_memset(p, FLOAT_INVALID, 4);                 //电池电流1
	p += 4;
	rt_memset(p, FLOAT_INVALID, 4);                 //电池电流2
	p += 4;
	rt_memset(p, FLOAT_INVALID, 4);                 //电池电流3
	p += 4;
	rt_memset(p, FLOAT_INVALID, 4);                 //电池电流4
	p += 4;

	rt_memset(p, FLOAT_INVALID, 4);                 //用户自定义电池电压1
	p += 4;
	rt_memset(p, FLOAT_INVALID, 4);                 //用户自定义电池电压2
	p += 4;
	rt_memset(p, FLOAT_INVALID, 4);                 //用户自定义电池电压3
	p += 4;
	rt_memset(p, FLOAT_INVALID, 4);                 //用户自定义电池电压4
	p += 4;
	rt_memset(p, FLOAT_INVALID, 4);                 //用户自定义电池温度1
	p += 4;
	rt_memset(p, FLOAT_INVALID, 4);                 //用户自定义电池温度2
	p += 4;
	rt_memset(p, FLOAT_INVALID, 4);                 //用户自定义电池温度3
	p += 4;
	rt_memset(p, FLOAT_INVALID, 4);                 //用户自定义电池温度4
	p += 4;

	rt_memset(p, FLOAT_INVALID, 4);                 //用户自定义电池中点电压1
	p += 4;
	rt_memset(p, FLOAT_INVALID, 4);                 //用户自定义电池中点电压2
	p += 4;
	rt_memset(p, FLOAT_INVALID, 4);                 //用户自定义电池中点电压3
	p += 4;
	rt_memset(p, FLOAT_INVALID, 4);                 //用户自定义电池中点电压4
	p += 4;
	/*********************    以上为通用命令非BMS专用，因此赋值无效                    *************************/

	/*************BMS_RealData***************************/
//	*(FLOAT*)p = tBCMAnaData.fBattVolt;                //电池电压
	*(FLOAT*)p = tDcRealData.tDCAnalag.wBatVol / 100.0f;
	p += 4;

//	*(FLOAT*)p = tBCMAnaData.fExterVolt;               //外部电压
	*(FLOAT*)p = tDcRealData.tDCAnalag.wBusVol / 100.0f;
	p += 4;

//	*(FLOAT*)p = tBCMAnaData.fEnvTemp;                  //环境温度
	*(FLOAT*)p = tDcRealData.tDCAnalag.sEnvTemp / 10.0f;
	p += 4;

//	*(FLOAT*)p = tBCMAnaData.fBattCurr;                 //电池电流
	*(FLOAT*)p = tDcRealData.tDCAnalag.sBatCur / 100.0f;
	p += 4;

	*p++ = wCellVoltNum;                                //检测的单体电芯数量M

	for(i = 0; i < wCellVoltNum; i++)
	{
		*(FLOAT*)p = tBCMAnaData.afCellVolt[i];        //单体电压
		p += 4;
	}

	*p++ = wCellTempNum;                              //检测的电芯温度数量N

	for(i = 0; i < wCellTempNum; i++)
	{
		*(FLOAT*)p = tBCMAnaData.afCellTemp[i];        //单体温度
		p += 4;
	}

//	*(FLOAT*)p = tBCMAnaData.fBusCurr;                 //BUS电流
	*(FLOAT*)p = tDcRealData.tDCAnalag.sBusCur / 100.0f;
	p += 4;

	//增加 1当前设定充电电压 2当前设定放电电压 3当前设定充电限电流 0730
	*(FLOAT*)p=tOutPara.wChgVolVal/100.0f;
    
	p += 4;

	*(FLOAT*)p=tOutPara.wDischgVolVal/100.0f;
	p += 4;

	*(FLOAT*)p=(FLOAT)(tOutPara.wChgBusCurVal);    //千分比
	p += 4;

    *(FLOAT*)p = tBCMAnaData.fBoardTemp;            //单板温度
    p += 4;
	
    /* LENID */
    s_tProtocol.wSendLenid  = (WORD) ((p-s_tProtocol.aucSendBuf)*2);

	return;
}

/****************************************************************************
* 函数名称：GetDcAngData(BYTE ucPort)
* 调    用：无
* 被 调 用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：获取直流开关状态
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
static void GetDcStatus(BYTE ucPort)
{

	BYTE *p = NULL;
	BYTE i = 0;
	WORD wLedTemp = 0x00;
    BYTE wFault = 1;
    BYTE ucChgLimit = 0,ucDischgLimit = 0;
	BYTE wCellTempNum = 0;

	T_BCMDataStruct    tBCMAnaData;
	T_BCMAlarmStruct    tBCMAlm;
	T_BattResult tBattResult;
    T_CtrlOutStruct tCtrlOut;
    T_DCRealData tDcRealData;
	T_HardwareParaStruct tHWPara;

    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }
	rt_memset(&tBattResult, 0, sizeof(T_BattResult));
	rt_memset(&tBCMAnaData, 0, sizeof(T_BCMDataStruct));
	rt_memset(&tDcRealData, 0, sizeof(T_DCRealData));
	rt_memset(&tHWPara, 0, sizeof(T_HardwareParaStruct));

	GetBattResult(&tBattResult);
	GetRealData( &tBCMAnaData );
	GetBduReal(&tDcRealData);
	rt_memset(&tBCMAlm, 0, sizeof(T_BCMAlarmStruct));

	GetBcmRealAlarm(&tBCMAlm);
    readBmsHWPara(&tHWPara);
    wCellTempNum = tHWPara.ucCellTempNum;

	p  = s_tProtocol.aucSendBuf;
	rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

	/*DATA_FLAG*/
	*p++ = GetDataFlag( BCM_ALARM, ucPort );                   //DATA_FLAG

	*p++ = 0x00;                    //输入干接点1
	*p++ = 0x00;                    //输入干接点2
	*p++ = 0x00;                    //输入干接点3
	*p++ = 0x00;                    //输入干接点4

	*p++ = 0x00;                    //直流负载回路状态1
	*p++ = 0x00;                    //直流负载回路状态2
	*p++ = 0x00;                    //直流负载回路状态3
	*p++ = 0x00;                    //直流负载回路状态4
	*p++ = 0x00;                    //直流负载回路状态5
	*p++ = 0x00;                    //直流负载回路状态6
	*p++ = 0x00;                    //直流负载回路状态7
	*p++ = 0x00;                    //直流负载回路状态8
	*p++ = 0x00;                    //直流负载回路状态9
	*p++ = 0x00;                    //直流负载回路状态10
	*p++ = 0x00;                    //直流防雷
	*p++ = 0x00;                    //烟雾
	*p++ = 0x00;                    //门磁
	*p++ = 0x00;                    //水淹
	*p++ = 0x00;                    //门禁
	/***********    以上是通用命令，非BMS专用，因此数据赋值为无效值         *************/

	//BMS专用
	*p++ = tDcRealData.tDCStatus.bChgPrt;                       //充电保护状态
	*p++ = tDcRealData.tDCStatus.bDischgPrt;                    //放电保护状态
    if (tBCMAnaData.ucLimit)
    {
        if ( (BDU_STATUS_COMMON_CHARGE == tBCMAnaData.ucBduStatus) || \
             (BDU_STATUS_BOOST_CHARGE == tBCMAnaData.ucBduStatus)  || \
             (BDU_STATUS_BUCK_CHARGE == tBCMAnaData.ucBduStatus) )
        {
            ucChgLimit = wFault;                      //充电限流状态获取:常规充电/升压充电/降压充电
        }
        else if ( (BDU_STATUS_COMMON_DISCHARGE == tBCMAnaData.ucBduStatus) || \
                  (BDU_STATUS_BOOST_DISCHARGE == tBCMAnaData.ucBduStatus)  || \
                  (BDU_STATUS_BUCK_DISCHARGE == tBCMAnaData.ucBduStatus) )
        {
            ucDischgLimit = wFault;                   //放电限流状态获取：常规放电/升压放电/降压放电
        }
    }   

    *p++ = ucChgLimit;                                //充电限流状态
    *p++ = ucDischgLimit;                             //放电限流状态
    *p++ = tBCMAnaData.ucCellEquSta;                  //电芯均衡状态

	GetCtrlOut(&tCtrlOut);

	*p++ = tCtrlOut.bBuzz;                               //蜂鸣器状态

	for(i = 0; i < 6; i++)
	{
		if(tCtrlOut.bLed[i] != 0)
		{
			wLedTemp |= BIT(i);
		}
	}
	*p++ = wLedTemp;                                       //LED灯状态

	*p++ = 1;                                              //下载拨码开关状态
	*p++ = GetTotalAddr();                                   //地址拨码开关状态
	*p++ = tCtrlOut.wRelayOut % 2;            //tBCMAnaData.aucOutputRly[0];   //输出干接点1
	*p++ = (tCtrlOut.wRelayOut / 2) % 2;       //tBCMAnaData.aucOutputRly[1];   //输出干接点2

	/*****************以下根据总表协议修改新增部分告警 add by xzx *****************/
	*p++ = tBCMAlm.ucBoardTempHighPrt;                   //单板过温保护
	*p++ = tBCMAlm.ucChgLoopInvalid;                     //充电回路失效
	*p++ = tBCMAlm.ucDischgLoopInvalid;                  //放电回路失效
	*p++ = tBCMAlm.ucCurrLimLoopInvalid;                 //限流回路失效
	*p++ = tBCMAlm.ucInsideTempHighPrt;                   //机内过温保护

	*p++ = tBCMAlm.ucBDUBattVoltLowPrt;                  //BDU电池欠压保护
	*p++ = tBCMAlm.ucBDUBusVoltLowPrt;                   //BDU母排欠压保护
	*p++ = tBCMAlm.ucBDUBusVoltHighPrt;                  //BDU母排过压保护
	*p++ = tBCMAlm.ucBduEepromAlm;                       //BDU EEPROM故障


	*p++ = 0;                                            //振动告警  D121没有该告警

	*p++ = tBCMAlm.ucCellVoltSampleFault;                //单体电压采样异常
	*p++ = tBCMAlm.ucBDUCommFail;                        //BDU通讯断
	*p++ = 0;                   //单体温度异常

	for(i = 0; i < wCellTempNum; i++)
	{
		*p++ = tBCMAlm.ucCellTempSensorInvalidAlm[i];            //单体温度无效
	}

	//*p++ = tBCMAnaData.ucBduStatus;

	//增加 1充电输入断 2充电使能 3放电使能  4 BMS充放电状态 5反接 6短路 7 16位电芯均衡状态 8电池组过压保护 9充电过流保护 10放电过流保护 11陀螺仪状态
    *p++ = tBCMAnaData.ucInputBreak;
	*p++ = tBCMAnaData.ucBattChargeEn;
	*p++ = tBCMAnaData.ucBattDischEn;   
	*p++ = (tBCMAlm.ucBDUBattLockAlm == FAULT)?BDU_STATUS_FAILURE:tBCMAnaData.ucBduStatus;
	*p++ = tBCMAlm.ucBattReverse;
	*p++ = tBCMAlm.ucBattShortCut;
	*(WORD*)p = tBattResult.wCellBalVoltBits;   //16位电芯均衡状态
	p += 2;
	*p++ = tBCMAlm.ucBattOverVoltPrt;       
	*p++ = tBCMAlm.ucChgCurrHighPrt;
	*p++ = tBCMAlm.ucDischgCurrHighPrt;
#ifndef KW_CHECK
    if(RT_EOK == CheckIsm330Connect())
    {
        *p++ = 0;   //陀螺仪通信正常
    }
    else
#endif
    {
        *p++ = 1;   //陀螺仪通信异常
    }
	*p++ = tBCMAlm.ucEqualCircuitFaultAlm;			//均衡电路故障检测状态
	*p++ = tDcRealData.tDCStatus.bHeaterStatus;		//BDU加热状态
	*p++ = tDcRealData.tDCAlarm.bHeaterErr;			//加热器故障
	*p++ = tDcRealData.tDCAlarm.bWavePrt;			//逐波保护
	*p++ = tDcRealData.tDCAlarm.bMainRelayFail;		//主继电器失效
	*p++ = tDcRealData.tDCAlarm.bDCDCErr;			//DCDC故障
	*p++ = tDcRealData.tDCAlarm.bSampleErr;			//采样异常
	*p++ = tDcRealData.tDCAlarm.bAuxiSourceErr;		//辅助源故障

	SetDigitalAlarmBit(0, p, 1 == tDcRealData.tDCStatus.bDiagSts1);			//Bit0：主继电器K1粘连
	SetDigitalAlarmBit(1, p, 1 == tDcRealData.tDCStatus.bDiagSts2);			//Bit1：主继电器K1无法闭合（驱动正常）
	SetDigitalAlarmBit(2, p, 1 == tDcRealData.tDCStatus.bDiagSts3);			//Bit2：无
	SetDigitalAlarmBit(3, p, 1 == tDcRealData.tDCStatus.bDiagSts4);			//Bit3：无
	SetDigitalAlarmBit(4, p, 1 == tDcRealData.tDCStatus.bDiagSts5);			//Bit4：母排侧上管MOS失效短路
	SetDigitalAlarmBit(5, p, 1 == tDcRealData.tDCStatus.bDiagSts6);			//Bit5：无
	SetDigitalAlarmBit(6, p, 1 == tDcRealData.tDCStatus.bDiagSts7);			//Bit6：无
	SetDigitalAlarmBit(7, p, 1 == tDcRealData.tDCStatus.bDiagSts8);			//Bit7：无
	p++;		//故障诊断告警1  各Bit位取值：0：正常；1：告警 

	SetDigitalAlarmBit(0, p, 1 == tDcRealData.tDCStatus.bDiagSts9);			//Bit0：无 
	SetDigitalAlarmBit(1, p, 1 == tDcRealData.tDCStatus.bDiagSts10);		//Bit1：无
	SetDigitalAlarmBit(2, p, 1 == tDcRealData.tDCStatus.bDiagSts11);		//Bit2：无
	SetDigitalAlarmBit(3, p, 1 == tDcRealData.tDCStatus.bDiagSts12);		//Bit3：无
	SetDigitalAlarmBit(4, p, 1 == tDcRealData.tDCStatus.bDiagSts13);		//Bit4：无
	SetDigitalAlarmBit(5, p, 1 == tDcRealData.tDCStatus.bDiagSts14);		//Bit5：无
	SetDigitalAlarmBit(6, p, 1 == tDcRealData.tDCStatus.bDiagSts15);		//Bit6：无
	SetDigitalAlarmBit(7, p, 1 == tDcRealData.tDCStatus.bDiagSts16);		//Bit7：无
	p++;		//故障诊断告警2  各Bit位取值：0：正常；1：告警 

	SetDigitalAlarmBit(0, p, 1 == tDcRealData.tDCStatus.bDiagSts17);		//Bit0：无
	SetDigitalAlarmBit(1, p, 1 == tDcRealData.tDCStatus.bDiagSts18);		//Bit1：无
	SetDigitalAlarmBit(2, p, 1 == tDcRealData.tDCStatus.bDiagSts19);		//Bit2：无
	SetDigitalAlarmBit(3, p, 1 == tDcRealData.tDCStatus.bDiagSts20);		//Bit3：缓启动异常
	SetDigitalAlarmBit(4, p, 1 == tDcRealData.tDCStatus.bDiagSts21);		//Bit4：无
	SetDigitalAlarmBit(5, p, 1 == tDcRealData.tDCStatus.bDiagSts22);		//Bit5：无
	SetDigitalAlarmBit(6, p, 1 == tDcRealData.tDCStatus.bDiagSts23);		//Bit6：无
	SetDigitalAlarmBit(7, p, 1 == tDcRealData.tDCStatus.bDiagSts24);		//Bit7：无
	p++;		//故障诊断告警3  各Bit位取值：0：正常；1：告警 

	SetDigitalAlarmBit(0, p, 1 == tDcRealData.tDCStatus.bDiagSts25);		//Bit0：无
	SetDigitalAlarmBit(1, p, 1 == tDcRealData.tDCStatus.bDiagSts26);		//Bit1：无
	SetDigitalAlarmBit(2, p, 1 == tDcRealData.tDCStatus.bDiagSts27);		//Bit2：无
	SetDigitalAlarmBit(3, p, 1 == tDcRealData.tDCStatus.bDiagSts28);		//Bit3：无
	SetDigitalAlarmBit(4, p, 1 == tDcRealData.tDCStatus.bDiagSts29);		//Bit4：无
	SetDigitalAlarmBit(5, p, 1 == tDcRealData.tDCStatus.bDiagSts30);		//Bit5：母排电压以及电流检测失效
	SetDigitalAlarmBit(6, p, 1 == tDcRealData.tDCStatus.bDiagSts31);		//Bit6：电池电压检测失效
	SetDigitalAlarmBit(7, p, 1 == tDcRealData.tDCStatus.bDiagSts32);		//Bit7：加热膜失效
	p++;		//故障诊断告警4  各Bit位取值：0：正常；1：告警 
	/* LENID */	
    s_tProtocol.wSendLenid  = (WORD) ((p-s_tProtocol.aucSendBuf)*2);

	return;
}

static void SetBMSAdjustData( BYTE ucPort )
{
	WORD wTemp;
	//T_BduRealDataStruct tBduData;
    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }
    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];

	//GetBduRealData(&tBduData);

    if(((s_tProtocol.ucCommandType >= 0x80)
    	&& (s_tProtocol.ucCommandType <= 0x87))
		|| s_tProtocol.ucCommandType == 0x8A
		/*|| s_tProtocol.ucCommandType == 0xA0
		|| s_tProtocol.ucCommandType == 0xA1*/)
    {
    	wTemp = (WORD)(Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+8)));
		//if((wTemp > 1751 || wTemp < 1167) && (s_tProtocol.ucCommandType == 0x80 || s_tProtocol.ucCommandType == 0x82))
		//{
		//	s_tProtocol.ucRTN  = RTN_INVALID_DATA;
		//	return;
		//}
		//if((wTemp > 5671 || wTemp < 3781) && (s_tProtocol.ucCommandType == 0x81 || s_tProtocol.ucCommandType == 0x83))
		//{
		//	s_tProtocol.ucRTN  = RTN_INVALID_DATA;
		//	return;
		//}
		//if(wTemp != 0 && (s_tProtocol.ucCommandType >= 0x84 && s_tProtocol.ucCommandType <= 0x87))
		//{
		//	s_tProtocol.ucRTN  = RTN_INVALID_DATA;
		//	return;
		//}
		BduAdj(s_tProtocol.ucCommandType, wTemp);
    }
    else
    {
         s_tProtocol.ucRTN  = RTN_WRONG_COMMAND;    // 命令格式错 
         return;
    }
	// if(tBduData.ucBduType == BDU_TYPE_COMMON_LI)
	// {
	// 	if(s_tProtocol.ucCommandType == 0x80
	// 		||s_tProtocol.ucCommandType == 0x82
	// 		||s_tProtocol.ucCommandType == 0x84
	// 		||s_tProtocol.ucCommandType == 0x86)
	// 	{//校准输出电压和电池电压时，需要降压充电
	// 		s_tDCReal.tDCStatus.bStatus = 8;
	// 	}
	// 	else
	// 	{
	// 		s_tDCReal.tDCStatus.bStatus = 0;
	// 	}
	// }
	return;
}

static void GetBmsPara(BYTE ucPort)
{
	BYTE *p = NULL;
	SHORT sTemp = 0;
	T_DCPara tDCPara;

	if ( ucPort >= SCI_PORT_NUM )
	{
		return;
	}

	rt_memset(&s_tSysPara, 0, sizeof(T_SysPara));    
    GetSysPara(&s_tSysPara);
	GetBduPara(&tDCPara);
	p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
	*p++ = GetDataFlag( BCM_ALARM, ucPort );
	sTemp = s_tSysPara.ucUsageScen;	// 使用场景
    *(SHORT*)p  = Host2Modbus((SHORT*)&sTemp);
    p += 2;

	*p++ = s_tSysPara.bUVPTempCompensationEn;
	*p++ = (BOOLEAN)s_tSysPara.ucCellUVPDelay;

	//新增充电最大电流和放电电压 1103 mwl
	*(FLOAT*)p = ((FLOAT)tDCPara.wChgBusCurVal) * g_ProConfig.fRateCurrChgBus / BDU_LIMIT_MAX;
	p += 4;
	*(FLOAT*)p = ((FLOAT)tDCPara.wDischgVolVal) / 100;
	p += 4;

	//新增放电低温告警阈值和放电低温保护阈值 1207 mwl
	*(FLOAT*)p = s_tSysPara.fDischgTempLowAlmThre;
	p += 4;
	*(FLOAT*)p = s_tSysPara.fDischgTempLowPrtThre;
	p += 4;

	*(FLOAT*)p = s_tSysPara.fChargeMaxCurr; // 充电最大电流 s_tSysPara.fChargeMaxCurr
	p += 4;
  	*p++ = s_tSysPara.ucRelayDefaultStatus; // 干接点默认状态

	*p++ = 0;                               // 放电方式 D121无该项

	*p++ = s_tSysPara.ucSleepIndicator;     // 休眠指示灯
	*(FLOAT*)p = s_tSysPara.fDischgMaxCurr;		// 放电最大电流 s_tSysPara.fDischgMaxCurr
	p += 4;
	sTemp = s_tSysPara.bBuzzerEnable;
    *(SHORT*)p  = Host2Modbus((SHORT*)&sTemp);
    p += 2;
	*p++ = s_tSysPara.bHeatingPadEnable; // 加热垫使能，默认禁止

	sTemp = (WORD)(s_tSysPara.fSelfDischgACR * 10);	// 自放电异常
	*(SHORT*)p  = Host2Modbus((SHORT*)&sTemp);
    p += 2;

	sTemp = (WORD)(s_tSysPara.fCellTempRiseAbnormalThre * 10);	// 单体温升速率异常
	*(SHORT*)p  = Host2Modbus((SHORT*)&sTemp);
    p += 2;

	*p++ = s_tSysPara.ucDcrFaultAlmThre;     // 直流内阻异常告警阈值
	*p++ = s_tSysPara.ucDcrFaultPrtThre;     // 直流内阻异常保护阈值
	*p++ = s_tSysPara.fSelfDevelopProtocolOffsetAddr;     // 自研协议偏移地址
	
	sTemp = (WORD)(s_tSysPara.fCapDCPRFaultAlmThre * 10);   // 容量衰减一致性告警阈值
	*(SHORT*)p  = Host2Modbus((SHORT*)&sTemp);
    p += 2;
	 /* LENID */
    s_tProtocol.wSendLenid  = (WORD) ((p-s_tProtocol.aucSendBuf)*2);
	return; 
}

 
BOOLEAN RangeValidationFloat(FLOAT fData, FLOAT fMinValue, FLOAT fMaxValue) {
	if(fData < fMinValue || fData > fMaxValue)
	{
		return FALSE;
	}
	return TRUE;
}

static BOOLEAN SetBmsParaByCmdType1(void) {
	FLOAT fPara = 0.0;
	U_32FLOAT tTemp;
	U_16Int tData;
	T_DCPara tDCPara;

	rt_memset(&tDCPara,0x00,sizeof(T_DCPara));
	tTemp.ucData[0] = s_tProtocol.aucRecBuf[8];
	tTemp.ucData[1] = s_tProtocol.aucRecBuf[9];
	tTemp.ucData[2] = s_tProtocol.aucRecBuf[10];
	tTemp.ucData[3] = s_tProtocol.aucRecBuf[11];

	switch(s_tProtocol.ucCommandType)
	{
	    case 0x80:
	    	tData.ucByte[0] = s_tProtocol.aucRecBuf[9];
			tData.ucByte[1] = s_tProtocol.aucRecBuf[8];
			if(tData.sData < 0)
			{
				s_tProtocol.ucRTN  = RTN_INVALID_DATA;
				return TRUE;
			}
	        fPara = (FLOAT)(Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+8)));
	        s_tSysPara.ucUsageScen = (BYTE)fPara;
			break;
		case 0x81:
			s_tSysPara.bUVPTempCompensationEn = s_tProtocol.aucRecBuf[8];
			break;
		case 0x82:
			s_tSysPara.ucCellUVPDelay = s_tProtocol.aucRecBuf[8];
			break;
		case 0x83:   
			if(!RangeValidationFloat(tTemp.fData, 2.0f, 100.0f)) 	//充电限电流
			{
				s_tProtocol.ucRTN   = RTN_INVALID_DATA;
				return TRUE;
			}                                              
			GetBduPara(&tDCPara);

            tDCPara.wChgCurVal = (WORD)(g_ProConfig.fRateCurrChg / g_ProConfig.fRateCurrChg * BDU_LIMIT_MAX);//采用千分比类型
            tDCPara.wChgBusCurVal = (WORD)(tTemp.fData / g_ProConfig.fRateCurrChgBus * BDU_LIMIT_MAX);//采用千分比类型

			SetBduPara(&tDCPara);
			BduCtrl(SCI_CTRL_CHG_STG, DISABLE);
			BduCtrl(SCI_CTRL_CHG2CHG, ENABLE);
			BduCtrl(SCI_CTRL_CHG_PRT, DISABLE);
//			GetBduIOPara(&tBduIOPara);
//			tBduIOPara.wChgMaxCurr = tTemp.fData * 10;               //千分比类型
//			setBduIoPara(&tBduIOPara);
//			SetBduCtrl(BDU_CHG_START);
			return TRUE;
		case 0x84:                                                  // 对设置的电压数值不做处理，仅允许放电回路闭合
            GetBduPara(&tDCPara);
            tDCPara.wDischgVolVal = (WORD)(tTemp.fData * 100);
			SetBduPara(&tDCPara);
			BduCtrl(SCI_CTRL_DISCHG_STG, DISABLE);
			BduCtrl(SCI_CTRL_CHG2CHG, DISABLE);
			BduCtrl(SCI_CTRL_DISCHG_PRT, DISABLE);
            return TRUE;
		case 0x85:
			s_tSysPara.fDischgTempLowAlmThre = tTemp.fData; 
			break;
		case 0x86:
			s_tSysPara.fDischgTempLowPrtThre = tTemp.fData;  
			break;
		case 0x87:
			s_tSysPara.fChargeMaxCurr = tTemp.fData;
			break;
		case 0x88:
	        s_tSysPara.ucRelayDefaultStatus = s_tProtocol.aucRecBuf[8];
			break;
		// case 0x89:  	
		//	R321删除放电方式参数
		case 0x8A:
	        s_tSysPara.ucSleepIndicator = s_tProtocol.aucRecBuf[8];
			break;
		case 0x8B:
			s_tSysPara.fDischgMaxCurr = tTemp.fData;
			break;

		default:
	        return FALSE;
	}
	return TRUE;
}

static BOOLEAN SetBmsParaByCmdType2(void) {
	FLOAT fPara;
	U_16Int tData;

	switch(s_tProtocol.ucCommandType)
	{
		case 0x8C:
	    	tData.ucByte[0] = s_tProtocol.aucRecBuf[9];
			tData.ucByte[1] = s_tProtocol.aucRecBuf[8];
			if(tData.sData < 0)
			{
				s_tProtocol.ucRTN  = RTN_INVALID_DATA;
				return TRUE;
			}
	        fPara = (FLOAT)(Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+8)));
	        s_tSysPara.bBuzzerEnable = (BYTE)fPara;
			break;
		case 0x8D:
			s_tSysPara.bHeatingPadEnable = s_tProtocol.aucRecBuf[8];; // 加热垫使能
			break;
	    case 0x8E:
	        fPara = (FLOAT)(Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+8)));
	        s_tSysPara.fSelfDischgACR = fPara / 10.0;
			break;
		case 0x8F:
	        fPara = (FLOAT)(Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+8)));
	        s_tSysPara.fCellTempRiseAbnormalThre = fPara / 10.0;
			break;
		case 0x90:
			s_tSysPara.ucDcrFaultAlmThre = s_tProtocol.aucRecBuf[8];
			break;
		case 0x91:
			s_tSysPara.ucDcrFaultPrtThre = s_tProtocol.aucRecBuf[8];
			break;
		case 0x92:
			s_tSysPara.fSelfDevelopProtocolOffsetAddr = s_tProtocol.aucRecBuf[8];
			break;
		case 0x93:
			fPara = (FLOAT)(Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf + 8))); //容量衰减一致性告警阈值
	        s_tSysPara.fCapDCPRFaultAlmThre = fPara / 10.0;
            break;
		default:
	        s_tProtocol.ucRTN  = RTN_WRONG_COMMAND;    /* 命令格式错 */
	        return FALSE;
	}
	return TRUE;
}

static BOOLEAN SetBmsParaAll(void)
{
	if(SetBmsParaByCmdType1())
	{
		return TRUE;
	}
	else if(SetBmsParaByCmdType2())
	{
		return TRUE;
	}
	else
	{
		return FALSE;
	}
}

static void SetBmsPara(BYTE ucPort)
{
	BYTE aucDataLen[20] = {2,1,1,4,4,4,4,4,1,1,1,4,2,1,2,2,1,1,1,2};

//	T_BduIOParaStruct tBduIOPara;
//	memset(&tBduIOPara,0x00,sizeof(T_BduIOParaStruct));
	if ( ucPort >= SCI_PORT_NUM )
	{
		return;
	}

	GetSysPara( &s_tSysPara );

    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];
	if(s_tProtocol.ucCommandType < 0x80 + sizeof(aucDataLen) && s_tProtocol.ucCommandType >= 0x80)
	{
		if(aucDataLen[s_tProtocol.ucCommandType - 0x80] != (s_tProtocol.wRecLenid / 2 - 1))
		{
			s_tProtocol.ucRTN = RTN_INVALID_DATA;
			return;
		}
	}
	else
	{
		s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
		return;
	}

	SetBmsParaAll();

	if (s_tProtocol.ucRTN == RTN_INVALID_DATA || s_tProtocol.ucRTN == RTN_WRONG_COMMAND) {
        return;
    }

	if ( False == SetSysPara( &s_tSysPara, True, CHANGE_BY_APPTEST ) )
    {
        s_tProtocol.ucRTN   = RTN_INVALID_DATA; 
    }

	//设置保存参数缺省值
	GetSysPara( &s_tSysPara );
	WriteBMSDefaultPara( &s_tSysPara );

	return;
}

static void GetAlarmLevel(BYTE ucPort)
{
    BYTE *p = NULL;
    if (ucPort >= SCI_PORT_NUM)
    {
        return;
    }

    rt_memset(&s_tSysPara, 0, sizeof(T_SysPara));
    GetSysPara(&s_tSysPara);

    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    *p++ = GetDataFlag(BCM_ALARM, ucPort);
    *p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[5];//1-充电过流保护:5
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[7];//2-放电过流保护:7
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[3];//3-电池组过压保护:3
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[18];//4-单板过温保护:19->18
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[4];//5-充电过流告警:4
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[6];//6-放电过流告警:6
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[1];//7-电池组过压告警:1
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[36];//8-环境温度高告警:37->36
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[37];//9-环境温度低告警:38->37
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[0];//10-电池组欠压告警:0
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[2];//11-电池组欠压保护:2
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[20];//12-单体过压告警:21->20
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[22];//13-单体过压保护:23->22
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[19];//14-单体欠压告警:20->19
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[21];//15-单体欠压保护:22->21
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[27];//16-充电高温告警:28->27
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[29];//17-充电高温保护:30->29
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[31];//18-放电高温告警:32->31
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[33];//19-放电高温保护:34->33
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[28];//20-充电低温告警:29->28
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[30];//21-充电低温保护:31->30
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[32];//22-放电低温告警:33->32
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[34];//23-放电低温保护:35->34
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[23];//24-单体一致性差告警:24->23
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[24];//25-单体一致性差保护:25->24
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[13];//26-电池SOC低告警:14->13
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[14];//27-电池SOC低保护:15->14
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[15];//28-电池SOH告警:16->15
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[16];//29-电池SOH保护:17->16
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[26];//30-单体损坏保护:27->26
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[49];//31-电池丢失告警:50->49
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[42];//32-BDU EEPROM故障:43->42
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[25];//33-单体电压采样异常:26->25
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[11];//34-充电回路失效:11
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[10];//35-放电回路失效:10
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[12];//36-限流回路失效:12
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[8];//37-电池短路:8
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[9];//38-电池反接:9
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[35];//39-单体温度无效:36->35
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[50]; // 机内过温保护    //40-机内过温保护
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[41];//41-BDU电池欠压保护:42->41
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[60]; // 单体温度异常    //42-单体温度异常
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[61]; // 地址冲突61
	*p++ = 0;
	*p++ = 0;//44-振动告警---不存在该告警
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[43];//45-BDU母排欠压保护:44->43
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[44];//46-BDU母排过压保护:45->44
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[45];//47-BDU通讯断:46->45
	*p++ = 0;
	*p++ = 0;//48-电压采样故障---不存在该告警
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[51];//49-回路异常
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[46];//50-BDU电池充电欠压保护:47->46
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[64];//单体温升速率异常
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[65];//直流内阻异常告警
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[66];//直流内阻异常保护
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[67];//自放电异常     
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[68];//电源通信断
	*p++ = 0;
	*p++ = s_tSysPara.aucAlarmLevel[69];//容量衰减一致性告警

    s_tProtocol.wSendLenid = (WORD)((p - s_tProtocol.aucSendBuf) * 2);
    return;
}

Static BOOLEAN SetBmsParaNew1(BYTE ucCommandType){
	switch(ucCommandType){

	
		case 0x8C:
			s_tSysPara.fChgHeaterStartupTemp = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8)));
			return True;
		case 0x8D:
			s_tSysPara.fChgHeaterShutdownTemp = (FLOAT)(Host2Modbus((SHORT *)(s_tProtocol.aucRecBuf + 8)));
			return True;	
		default:
			return False;
	}
}

static void SetBmsParaNew(BYTE ucPort)
{
	U_Int tData;
	U_32FLOAT tTemp;
	BYTE aucDataLen[15] = {1,2,2,2,2,4,4,4,4,1,2,0,2,2,2};
	if ( ucPort >= SCI_PORT_NUM )
	{
		return;
	}
	GetSysPara( &s_tSysPara );

    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];
	if(s_tProtocol.ucCommandType < 0x80 + sizeof(aucDataLen) && s_tProtocol.ucCommandType >= 0x80)
	{
		if(aucDataLen[s_tProtocol.ucCommandType - 0x80] != (s_tProtocol.wRecLenid/2 - 1))
		{
			s_tProtocol.ucRTN = RTN_INVALID_DATA;
			return;
		}
	}
	else
	{
		s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
		return;
	}
	
	tTemp.ucData[0] = s_tProtocol.aucRecBuf[8];
	tTemp.ucData[1] = s_tProtocol.aucRecBuf[9];
	tTemp.ucData[2] = s_tProtocol.aucRecBuf[10];
	tTemp.ucData[3] = s_tProtocol.aucRecBuf[11];

	tData.ucByte[0] = s_tProtocol.aucRecBuf[9];
	tData.ucByte[1] = s_tProtocol.aucRecBuf[8];

	switch(s_tProtocol.ucCommandType)
	{
		case 0x81:
			s_tSysPara.wBattSOCLowAlmThre = tData.iData;
			break;
		case 0x82:
			s_tSysPara.wBattSOCLowPrtThre = tData.iData;
			break;
		case 0x83:
			s_tSysPara.wBattSOHAlmThre = tData.iData;
			break;
		case 0x84:
			s_tSysPara.wBattSOHPrtThre = tData.iData;
			break;
		case 0x85:
			s_tSysPara.fBattUnderVoltAlmThre = tTemp.fData;
			break;
		case 0x86:
			s_tSysPara.fBattUnderVoltPrtThre = tTemp.fData;
			break;
		case 0x87:
			s_tSysPara.fRemoteSupplyOutVolt = tTemp.fData;
			break;
		case 0x8A:
			s_tSysPara.wDischgSwitchSOC = tData.iData;
			break;
			
		default:
			if( SetBmsParaNew1(s_tProtocol.ucCommandType)==False){
				s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
				return;
			}
			
	}
	if ( !(SetSysPara( &s_tSysPara, True, CHANGE_BY_APPTEST )) )
    {
        s_tProtocol.ucRTN   = RTN_INVALID_DATA; 
    }

	//设置保存参数缺省值
	GetSysPara( &s_tSysPara );
	WriteBMSDefaultPara( &s_tSysPara );

	return;
}

static BOOLEAN  SetSpecificPara_Area1(BYTE ucCommandType, U_Int tData ,U_32FLOAT tTemp)
{
	BOOLEAN bSetState = TRUE;
	switch(ucCommandType)
		{
			case 0x80:
				s_tSysPara.ucCellUVPDelay = s_tProtocol.aucRecBuf[8];
				break;
	//		case 0x81:
	//			s_tSysPara.wDischgSwitchSOC = tData.iData;
	//			break;
			default:
			bSetState = FALSE;
			break;
    }
    return bSetState;
}
static BOOLEAN  SetSpecificPara_Area2(BYTE ucCommandType, U_Int tData ,U_32FLOAT tTemp)
{
	BOOLEAN bSetState = TRUE;
	switch(ucCommandType)
		{
			case 0x83:
				s_tSysPara.aucAlarmLevel[47] = s_tProtocol.aucRecBuf[9];//BDU电池闭锁告警:48->47
				break;
			case 0x84:
				s_tSysPara.aucAlarmLevel[38] = s_tProtocol.aucRecBuf[9];//环境温度高保护:39->38
				break;
			case 0x85:
				s_tSysPara.aucAlarmLevel[39] = s_tProtocol.aucRecBuf[9];//环境温度低保护:40->39
				break;
			case 0x86:
				s_tSysPara.aucAlarmLevel[17] = s_tProtocol.aucRecBuf[9];//单板过温告警:18->17
				break;
			case 0x87:
				s_tSysPara.aucRelayBit[47] = s_tProtocol.aucRecBuf[9];//BDU电池闭锁告警:48->47
				break;
			case 0x88:
				s_tSysPara.aucRelayBit[38] = s_tProtocol.aucRecBuf[9];//环境温度高保护:39->38
				break;
			case 0x89:
				s_tSysPara.aucRelayBit[39] = s_tProtocol.aucRecBuf[9];//环境温度低保护:40->39
				break;
			case 0x8A:
				s_tSysPara.aucRelayBit[17] = s_tProtocol.aucRecBuf[9];//单板过温告警:18->17
				break;
		    case 0x8B: ///单体欠压告警阈值
			s_tSysPara.fCellUnderVoltAlmThre = tData.iData / 100.0f;
			break;
			default:
			bSetState = FALSE;
			break;
    }
    return bSetState;
}
static BOOLEAN  SetSpecificPara_Area3(BYTE ucCommandType, U_Int tData ,U_32FLOAT tTemp)
{
	BOOLEAN bSetState = TRUE;
	switch(ucCommandType)
		{
		case 0x8C: ///单体欠压保护阈值
			s_tSysPara.fCellUnderVoltPrtThre = tData.iData / 100.0f;
			break;
        case 0x8D:
            s_tSysPara.fDischargeEndVolt1 = tData.iData / 100.0f;
            break;
        case 0x8E:
            s_tSysPara.fDischargeEndVolt2 = tData.iData /100.0f;
            break;
        case 0x8F: /// 软件防盗延时
            s_tSysPara.wSoftAntiTheftDelay = tData.iData;
            break;		
		default:
			bSetState = FALSE;
			break;
    }
    return bSetState;
}
static void SetSpecificPara(BYTE ucPort)
{
	U_Int tData;
	U_32FLOAT tTemp;
	BYTE aucDataLen[16] = {1,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2};
	if ( ucPort >= SCI_PORT_NUM )
	{
		return;
	}
	GetSysPara( &s_tSysPara );

    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];
    tTemp.ucData[0] = s_tProtocol.aucRecBuf[8];
    tTemp.ucData[1] = s_tProtocol.aucRecBuf[9];
    tTemp.ucData[2] = s_tProtocol.aucRecBuf[10];
    tTemp.ucData[3] = s_tProtocol.aucRecBuf[11];
	if(s_tProtocol.ucCommandType < 0x80 + sizeof(aucDataLen) && s_tProtocol.ucCommandType >= 0x80)
	{
		if(aucDataLen[s_tProtocol.ucCommandType - 0x80] != s_tProtocol.wRecLenid / 2 - 1)
		{
			s_tProtocol.ucRTN = RTN_INVALID_DATA;
			return;
		}
	// 屏蔽放电切换SOC的设置
        if(s_tProtocol.ucCommandType == 0X81) // 无效参数，禁止设置
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
            return;
        }
	}
	else
	{
		s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
		return;
	}
	
	tData.ucByte[0] = s_tProtocol.aucRecBuf[9];
	tData.ucByte[1] = s_tProtocol.aucRecBuf[8];

    if(TRUE != SetSpecificPara_Area1(s_tProtocol.ucCommandType, tData, tTemp))
    {
        if(TRUE != SetSpecificPara_Area2(s_tProtocol.ucCommandType, tData, tTemp))
        {
		   if(TRUE != SetSpecificPara_Area3(s_tProtocol.ucCommandType, tData, tTemp))
           {
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;  
			return;  
		   }          
        }  
    }
	if ( !(SetSysPara( &s_tSysPara, True, CHANGE_BY_APPTEST )) )
    {
        s_tProtocol.ucRTN   = RTN_INVALID_DATA; 
    }

	//设置保存参数缺省值
	GetSysPara( &s_tSysPara );
	WriteBMSDefaultPara( &s_tSysPara );

	return;
}

static BYTE SetAlarmLevel_CmdType87and88(BYTE ucPara) {
    switch (s_tProtocol.ucCommandType){
		case 0x87:
			s_tSysPara.aucAlarmLevel[36] = ucPara;//环境温度高告警
			break;
		case 0x88:
			s_tSysPara.aucAlarmLevel[37] = ucPara;//环境温度低告警
			break;
        default:
        	return False;
    }
    return True;
}

static BYTE SetAlarmLevel_CmdType8x(BYTE ucPara) {
    switch (s_tProtocol.ucCommandType){
		case 0x80:
			s_tSysPara.aucAlarmLevel[5] = ucPara;//1-充电过流保护:5
			break;
		case 0x81:
			s_tSysPara.aucAlarmLevel[7] = ucPara;//2-放电过流保护:7
			break;
		case 0x82:
			s_tSysPara.aucAlarmLevel[3] = ucPara;//3-电池组过压保护:3
			break;
		case 0x83:
			s_tSysPara.aucAlarmLevel[18] = ucPara;//4-单板过温保护:19->18
			break;
		case 0x84:
			s_tSysPara.aucAlarmLevel[4] = ucPara;//5-充电过流告警:4
			break;
		case 0x85:
			s_tSysPara.aucAlarmLevel[6] = ucPara;//6-放电过流告警:6
			break;
		case 0x86:
			s_tSysPara.aucAlarmLevel[1] = ucPara;//7-电池组过压告警:1
			break;
		case 0x89:
			s_tSysPara.aucAlarmLevel[0] = ucPara;//10-电池组欠压告警:0
			break;
		case 0x8A:
			s_tSysPara.aucAlarmLevel[2] = ucPara;//11-电池组欠压保护:2
			break;
		case 0x8B:
			s_tSysPara.aucAlarmLevel[20] = ucPara;//12-单体过压告警:21->20
			break;
		case 0x8C:
			s_tSysPara.aucAlarmLevel[22] = ucPara;//13-单体过压保护:23->22
			break;
		case 0x8D:
			s_tSysPara.aucAlarmLevel[19] = ucPara;//14-单体欠压告警:20->19
			break;
		case 0x8E:
			s_tSysPara.aucAlarmLevel[21] = ucPara;//15-单体欠压保护:22->21
			break;
		case 0x8F:
			s_tSysPara.aucAlarmLevel[27] = ucPara;//16-充电高温告警:28->27
			break;
        default:
        	return False;
    }
    return True;
}

static BYTE SetAlarmLevel_CmdType9x(BYTE ucPara) {
    switch (s_tProtocol.ucCommandType){
        case 0x90:
			s_tSysPara.aucAlarmLevel[29] = ucPara;//17-充电高温保护:30->29
			break;
		case 0x91:
			s_tSysPara.aucAlarmLevel[31] = ucPara;//18-放电高温告警:32->31
			break;
		case 0x92:
			s_tSysPara.aucAlarmLevel[33] = ucPara;//19-放电高温保护:34->33
			break;
		case 0x93:
			s_tSysPara.aucAlarmLevel[28] = ucPara;//20-充电低温告警:29->28
			break;
		case 0x94:
			s_tSysPara.aucAlarmLevel[30] = ucPara;//21-充电低温保护:31->30
			break;
		case 0x95:
			s_tSysPara.aucAlarmLevel[32] = ucPara;//22-放电低温告警:33->32
			break;
		case 0x96:
			s_tSysPara.aucAlarmLevel[34] = ucPara;//23-放电低温保护:35->34
			break;
		case 0x97:
			s_tSysPara.aucAlarmLevel[23] = ucPara;//24-单体一致性差告警:24->23
			break;
		case 0x98:
			s_tSysPara.aucAlarmLevel[24] = ucPara;//25-单体一致性差保护:25->24
			break;
		case 0x99:
			s_tSysPara.aucAlarmLevel[13] = ucPara;//26-电池SOC低告警:14->13
			break;
		case 0x9A:
			s_tSysPara.aucAlarmLevel[14] = ucPara;//27-电池SOC低保护:15->14
			break;
		case 0x9B:
			s_tSysPara.aucAlarmLevel[15] = ucPara;//28-电池SOH告警:16->15
			break;
		case 0x9C:
			s_tSysPara.aucAlarmLevel[16] = ucPara;//29-电池SOH保护:17->16
			break;
		case 0x9D:
			s_tSysPara.aucAlarmLevel[26] = ucPara;//30-单体损坏保护:27->26
			break;
		case 0x9E:
			s_tSysPara.aucAlarmLevel[49] = ucPara;//31-电池丢失告警:50->49
			break;
		case 0x9F:
			s_tSysPara.aucAlarmLevel[42] = ucPara;//32-BDU EEPROM故障:43->42
			break;
        default:
        	return False;
    }
    return True;
}

static BYTE SetAlarmLevel_CmdTypeA0_6(BYTE ucPara) {
    switch (s_tProtocol.ucCommandType){
        case 0xA0:
			s_tSysPara.aucAlarmLevel[25] = ucPara;//33-单体电压采样异常:26->25
			break;
		case 0xA1:
			s_tSysPara.aucAlarmLevel[11] = ucPara;//34-充电回路失效:11
			break;
		case 0xA2:
			s_tSysPara.aucAlarmLevel[10] = ucPara;//35-放电回路失效:10
			break;
		case 0xA3:
			s_tSysPara.aucAlarmLevel[12] = ucPara;//36-限流回路失效:12
			break;
		case 0xA4:
			s_tSysPara.aucAlarmLevel[8] = ucPara;//37-电池短路:8
			break;
		case 0xA5:
			s_tSysPara.aucAlarmLevel[9] = ucPara;//38-电池反接:9
			break;
		case 0xA6:
			s_tSysPara.aucAlarmLevel[35] = ucPara;//39-单体温度无效:36->35
			break;
        default:
        	return False;
    }
    return True;
}

static BYTE SetAlarmLevel_CmdTypeA7_F(BYTE ucPara) {
    switch (s_tProtocol.ucCommandType){
		case 0xA7:
		    s_tSysPara.aucAlarmLevel[50] = ucPara;// 50-机内过温保护
		    break;
		case 0xA8:
			s_tSysPara.aucAlarmLevel[41] = ucPara;//41-BDU电池欠压保护:42->41
			break;
		case 0xA9:
		    s_tSysPara.aucAlarmLevel[60] = ucPara;//60-单体温度异常
		    break;
		case 0xAA:
		    s_tSysPara.aucAlarmLevel[61] = ucPara;//61-地址冲突
		    break;
		case 0xAB:
			// s_tSysPara.aucAlarmLevel[51] = ucPara;//44-振动告警-参数列表无
			break;
		case 0xAC:
			s_tSysPara.aucAlarmLevel[43] = ucPara;//45-BDU母排欠压保护:44->43
			break;
		case 0xAD:
			s_tSysPara.aucAlarmLevel[44] = ucPara;//46-BDU母排过压保护:45->44
			break;
		case 0xAE:
			s_tSysPara.aucAlarmLevel[45] = ucPara;//47-BDU通讯断:46->45
			break;
		case 0xAF:
			//s_tSysPara.aucAlarmLevel[13] = ucPara;//48-电压采样故障-参数列表无
			break;
        default:
        	return False;
    }
    return True;
}

static BOOLEAN SetAlarmLevel_CmdTypeB0_6(BYTE ucPara) {
    switch (s_tProtocol.ucCommandType){
        case 0xB0:
		    s_tSysPara.aucAlarmLevel[51] = ucPara; //回路异常
	        break;
		case 0xB1:
			s_tSysPara.aucAlarmLevel[46] = ucPara;//50-BDU电池充电欠压保护:47->46
			break;
		case 0xB2:
		    s_tSysPara.aucAlarmLevel[64] = ucPara;//单体温升速率异常
			break;
		case 0xB3:
			s_tSysPara.aucAlarmLevel[65] = ucPara;//直流内阻异常告警
			break;
		case 0xB4:
			s_tSysPara.aucAlarmLevel[66] = ucPara;//直流内阻异常保护
			break;
		case 0xB5:
			s_tSysPara.aucAlarmLevel[67] = ucPara;//自放电异常
			break;
		case 0xB6:
			s_tSysPara.aucAlarmLevel[68] = ucPara; //电源通信断
			break;
		default:
        	return False;
    }
    return True;
}

static BOOLEAN SetAlarmLevel_CmdTypeB7_F(BYTE ucPara) {
    switch (s_tProtocol.ucCommandType){
		case 0xB7:
			s_tSysPara.aucAlarmLevel[17] = ucPara; //单板过温告警
			break;
		case 0xB8:
		    s_tSysPara.aucAlarmLevel[35] = ucPara; //单体温度传感器无效告警
	        break;
		case 0xB9:
			s_tSysPara.aucAlarmLevel[40] = ucPara;//环境温度传感器无效告警
			break;
		case 0xBA:
		    s_tSysPara.aucAlarmLevel[47] = ucPara;//BDU电池闭锁告警
			break;
		case 0xBB:
			s_tSysPara.aucAlarmLevel[48] = ucPara;//均衡电路故障告警
			break;
		case 0xBC:
			s_tSysPara.aucAlarmLevel[52] = ucPara;//停电时间保护告警
			break;
		case 0xBD:
			s_tSysPara.aucAlarmLevel[53] = ucPara;//加热膜失效告警
			break;
		case 0xBE:
			s_tSysPara.aucAlarmLevel[54] = ucPara; //熔丝损坏
			break;
		case 0xBF:
			s_tSysPara.aucAlarmLevel[55] = ucPara; // 连接器高温保护
			break;
		default:
        	return False;
    }
    return True;
}

static BOOLEAN SetAlarmLevel_CmdTypeCx(BYTE ucPara) {
    switch (s_tProtocol.ucCommandType){
        case 0xC0:
		    s_tSysPara.aucAlarmLevel[56] = ucPara; //主继电器失效
	        break;
		case 0xC1:
			s_tSysPara.aucAlarmLevel[57] = ucPara;//DCDC故障
			break;
		case 0xC2:
		    s_tSysPara.aucAlarmLevel[58] = ucPara;//采集异常
			break;
		case 0xC3:
			s_tSysPara.aucAlarmLevel[59] = ucPara;//辅助源故障
			break;
		case 0xC4:
			s_tSysPara.aucAlarmLevel[62] = ucPara;//单体动态欠压保护
			break;
		case 0xC5:
			s_tSysPara.aucAlarmLevel[63] = ucPara;//加热膜连接器高温保护
			break;
		case 0xC6:
			s_tSysPara.aucAlarmLevel[69] = ucPara; // 容量衰减一致性
			break;
		default:
			s_tProtocol.ucRTN = RTN_WRONG_COMMAND;  /* 命令格式错 */
        	return False;
    }
    return True;
}

static void SetAlarmLevelAll(BYTE ucPara) {
    if (SetAlarmLevel_CmdType8x(ucPara)) {
        return;
    }
	if (SetAlarmLevel_CmdType87and88(ucPara)) {
        return;
    }
    if (SetAlarmLevel_CmdType9x(ucPara)) {
        return;
    }
    if (SetAlarmLevel_CmdTypeA0_6(ucPara)) {
        return;
    }
	if (SetAlarmLevel_CmdTypeA7_F(ucPara)) {
        return;
    }
	if (SetAlarmLevel_CmdTypeB0_6(ucPara)) {
        return;
    }
	if (SetAlarmLevel_CmdTypeB7_F(ucPara)) {
        return;
    }
	if (SetAlarmLevel_CmdTypeCx(ucPara)) {
        return;
    }
    return;
}

static void SetAlarmLevel(BYTE ucPort)
{  
	BYTE ucPara;

	GetSysPara( &s_tSysPara );

	s_tProtocol.ucCommandType	= s_tProtocol.aucRecBuf[7];
	ucPara = s_tProtocol.aucRecBuf[9]; 	 
	
	SetAlarmLevelAll(ucPara);

	if (s_tProtocol.ucRTN == RTN_INVALID_DATA || s_tProtocol.ucRTN == RTN_WRONG_COMMAND) {
        return;
    }

	if ( False == SetSysPara( &s_tSysPara, True, CHANGE_BY_APPTEST ) )
	{
		s_tProtocol.ucRTN = RTN_INVALID_DATA;	
	}

	//设置保存参数缺省值
	GetSysPara( &s_tSysPara );
	WriteBMSDefaultPara( &s_tSysPara );

	return;
}

static void GetAlarmRelay(BYTE ucPort)
{
	BYTE *p = NULL;

	rt_memset(&s_tSysPara, 0, sizeof(T_SysPara));
	GetSysPara(&s_tSysPara);

	p = s_tProtocol.aucSendBuf;
	rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
	*p++ = GetDataFlag(BCM_ALARM, ucPort);
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[5];//1-充电过流保护:5
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[7];//2-放电过流保护:7
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[3];//3-电池组过压保护:3
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[18];//4-单板过温保护:19->18
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[4];//5-充电过流告警:4
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[6];//6-放电过流告警:6
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[1];//7-电池组过压告警:1
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[36];//8-环境温度高告警:37->36
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[37];//9-环境温度低告警:38->37
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[0];//10-电池组欠压告警:0
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[2];//11-电池组欠压保护:2
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[20];//12-单体过压告警:21->20
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[22];//13-单体过压保护:23->22
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[19];//14-单体欠压告警:20->19
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[21];//15-单体欠压保护:22->21
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[27];//16-充电高温告警:28->27
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[29];//17-充电高温保护:30->29
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[31];//18-放电高温告警:32->31
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[33];//19-放电高温保护:34->33
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[28];//20-充电低温告警:29->28
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[30];//21-充电低温保护:31->30
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[32];//22-放电低温告警:33->32
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[34];//23-放电低温保护:35->34
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[23];//24-单体一致性差告警:24->23
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[24];//25-单体一致性差保护:25->24
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[13];//26-电池SOC低告警:14->13
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[14];//27-电池SOC低保护:15->14
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[15];//28-电池SOH告警:16->15
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[16];//29-电池SOH保护:17->16
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[26];//30-单体损坏保护:27->26
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[49];//31-电池丢失告警:50->49
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[42];//32-BDU EEPROM故障:43->42
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[25];//33-单体电压采样异常:26->25
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[11];//34-充电回路失效:11
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[10];//35-放电回路失效:10
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[12];//36-限流回路失效:12
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[8];//37-电池短路:8
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[9];//38-电池反接:9
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[35];//39-单体温度无效:36->35
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[50]; // 40-机内过温保护:50
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[41];//41-BDU电池欠压保护:42->41
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[60]; // 42-单体温度异常:60
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[61]; // 43-地址冲突：
	*p++ = 0;
	*p++ = 0;//44-振动告警-参数表中无该字段
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[43];//45-BDU母排欠压保护:44->43
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[44];//46-BDU母排过压保护:45->44
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[45];//47-BDU通讯断:46->45
	*p++ = 0;
	*p++ = 0;//48-电压采样故障-参数表中无该字段
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[51]; //49-回路异常：51
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[46];//50-BDU电池充电欠压保护:47->46
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[64];//单体温升速率异常
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[65];//直流内阻异常告警
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[66];//直流内阻异常保护
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[67];//自放电异常
	*p++ = 0;
	*p++ = s_tSysPara.aucRelayBit[69];//容量衰减一致性异常

	s_tProtocol.wSendLenid = (WORD)((p - s_tProtocol.aucSendBuf) * 2);
	return;
}

static BYTE SetAlarmRelay_CmdType8x(BYTE ucPara) {
    switch (s_tProtocol.ucCommandType){
		case 0x80:
			s_tSysPara.aucRelayBit[5] = ucPara;//充电过流保护:5
			break;
		case 0x81:
			s_tSysPara.aucRelayBit[7] = ucPara;//放电过流保护:7
			break;
		case 0x82:
			s_tSysPara.aucRelayBit[3] = ucPara;//电池组过压保护:3
			break;
		case 0x83:
			s_tSysPara.aucRelayBit[18] = ucPara;//单板过温保护:19->18
			break;
		case 0x84:
			s_tSysPara.aucRelayBit[4] = ucPara;//充电过流告警:4
			break;
		case 0x85:
			s_tSysPara.aucRelayBit[6] = ucPara;//放电过流告警:6
			break;
		case 0x86:
			s_tSysPara.aucRelayBit[1] = ucPara;//电池组过压告警:1
			break;
		case 0x87:
			s_tSysPara.aucRelayBit[36] = ucPara;//环境温度高告警
			break;
		case 0x88:
			s_tSysPara.aucRelayBit[37] = ucPara;//环境温度低告警
			break;
		case 0x89:
			s_tSysPara.aucRelayBit[0] = ucPara;//电池组欠压告警:0
			break;
		case 0x8A:
			s_tSysPara.aucRelayBit[2] = ucPara;//电池组欠压保护:2
			break;
		case 0x8B:
			s_tSysPara.aucRelayBit[20] = ucPara;//单体过压告警:21->20
			break;
		case 0x8C:
			s_tSysPara.aucRelayBit[22] = ucPara;//单体过压保护:23->22
			break;
		case 0x8D:
			s_tSysPara.aucRelayBit[19] = ucPara;//单体欠压告警:20->19
			break;
		case 0x8E:
			s_tSysPara.aucRelayBit[21] = ucPara;//单体欠压保护:22->21
			break;
		case 0x8F:
			s_tSysPara.aucRelayBit[27] = ucPara;//充电高温告警:28->27
			break;
        default:
        	return False;
    }
    return True;
}

static BYTE SetAlarmRelay_CmdType9x(BYTE ucPara) {
    switch (s_tProtocol.ucCommandType){
        case 0x90:
			s_tSysPara.aucRelayBit[29] = ucPara;//充电高温保护:30->29
			break;
		case 0x91:
			s_tSysPara.aucRelayBit[31] = ucPara;//放电高温告警:32->31
			break;
		case 0x92:
			s_tSysPara.aucRelayBit[33] = ucPara;//放电高温保护:34->33
			break;
		case 0x93:
			s_tSysPara.aucRelayBit[28] = ucPara;//充电低温告警:29->28
			break;
		case 0x94:
			s_tSysPara.aucRelayBit[30] = ucPara;//充电低温保护:31->30
			break;
		case 0x95:
			s_tSysPara.aucRelayBit[32] = ucPara;//放电低温告警:33->32
			break;
		case 0x96:
			s_tSysPara.aucRelayBit[34] = ucPara;//放电低温保护:35->34
			break;
		case 0x97:
			s_tSysPara.aucRelayBit[23] = ucPara;//单体一致性差告警:24->23
			break;
		case 0x98:
			s_tSysPara.aucRelayBit[24] = ucPara;//单体一致性差保护:25->24
			break;
		case 0x99:
			s_tSysPara.aucRelayBit[13] = ucPara;//电池SOC低告警:14->13
			break;
		case 0x9A:
			s_tSysPara.aucRelayBit[14] = ucPara;//电池SOC低保护:15->14
			break;
		case 0x9B:
			s_tSysPara.aucRelayBit[15] = ucPara;//电池SOH告警:16->15
			break;
		case 0x9C:
			s_tSysPara.aucRelayBit[16] = ucPara;//电池SOH保护:17->16
			break;
		case 0x9D:
			s_tSysPara.aucRelayBit[26] = ucPara;//单体损坏保护:27->26
			break;
		case 0x9E:
			s_tSysPara.aucRelayBit[49] = ucPara;//电池丢失告警:50->49
			break;
		case 0x9F:
			s_tSysPara.aucRelayBit[42] = ucPara;//BDU EEPROM故障:43->42
			break;
        default:
        return False;
    }
    return True;
}

static BYTE SetAlarmRelay_CmdTypeA0_6(BYTE ucPara) {
    switch (s_tProtocol.ucCommandType){
        case 0xA0:
			s_tSysPara.aucRelayBit[25] = ucPara;//单体电压采样异常:26->25
			break;
		case 0xA1:
			s_tSysPara.aucRelayBit[11] = ucPara;//充电回路失效:11
			break;
		case 0xA2:
			s_tSysPara.aucRelayBit[10] = ucPara;//放电回路失效:10
			break;
		case 0xA3:
			s_tSysPara.aucRelayBit[12] = ucPara;//限流回路失效:12
			break;
		case 0xA4:
			s_tSysPara.aucRelayBit[8] = ucPara;//电池短路:8
			break;
		case 0xA5:
			s_tSysPara.aucRelayBit[9] = ucPara;//电池反接:9
			break;
		case 0xA6:
			s_tSysPara.aucRelayBit[35] = ucPara;//单体温度无效:36->35
			break;
        default:
        	return False;
    }
    return True;
}

static BYTE SetAlarmRelay_CmdTypeA7_F(BYTE ucPara) {
    switch (s_tProtocol.ucCommandType){
		case 0xA7:
			s_tSysPara.aucRelayBit[50] = ucPara;// 机内过温保护：50
			break;
		case 0xA8:
			s_tSysPara.aucRelayBit[41] = ucPara;//BDU电池欠压保护:42->41
			break;
		case 0xA9:
			s_tSysPara.aucRelayBit[60] = ucPara;// 单体温度异常：60
			break;
		case 0xAA:
			s_tSysPara.aucRelayBit[61] = ucPara;// 地址冲突：61
			break;
		case 0xAB:
			// s_tSysPara.aucRelayBit[51] = ucPara;//振动告警:参数表无，应该写为0或新增参数
			break;
		case 0xAC:
			s_tSysPara.aucRelayBit[43] = ucPara;//DU母排欠压保护:44->43
			break;
		case 0xAD:
			s_tSysPara.aucRelayBit[44] = ucPara;//BDU母排过压保护:45->44
			break;
		case 0xAE:
			s_tSysPara.aucRelayBit[45] = ucPara;//BDU通讯断:46->45
			break;
		case 0xAF:
			//s_tSysPara.aucRelayBit[25] = ucPara;//电压采样故障:25
			break;
        default:
        	return False;
    }
    return True;
}

static BYTE SetAlarmRelay_CmdTypeB0_6(BYTE ucPara) {
    switch (s_tProtocol.ucCommandType){
		case 0xB0:
			s_tSysPara.aucRelayBit[51] = ucPara;// 回路异常：51
			break;
		case 0xB1:
			s_tSysPara.aucRelayBit[46] = ucPara;//BDU电池充电欠压保护:47->46
			break;
		case 0xB2:
			s_tSysPara.aucRelayBit[64] = ucPara;//单体温升速率异常
			break;
		case 0xB3:
			s_tSysPara.aucRelayBit[65] = ucPara;//直流内阻异常告警
			break;
		case 0xB4:
			s_tSysPara.aucRelayBit[66] = ucPara;//直流内阻异常保护
			break;
		case 0xB5:
			s_tSysPara.aucRelayBit[67] = ucPara;//自放电异常
			break;
		//case 0xB6:
		//	s_tSysPara.aucRelayBit[69] = ucPara;//电源通信断-干接点不可设
		//	break;
        default:
        	return False;
    }
    return True;
}

static BYTE SetAlarmRelay_CmdTypeB7_F(BYTE ucPara) {
    switch (s_tProtocol.ucCommandType){
		case 0xB7:
			s_tSysPara.aucRelayBit[17] = ucPara;//单板过温告警
			break;
		case 0xB8:
			s_tSysPara.aucRelayBit[35] = ucPara;//单体温度传感器无效告警
			break;
		case 0xB9:
			s_tSysPara.aucRelayBit[40] = ucPara;//环境温度传感器无效告警
			break;
		case 0xBA:
			s_tSysPara.aucRelayBit[47] = ucPara;//BDU电池闭锁告警
			break;
		case 0xBB:
			s_tSysPara.aucRelayBit[48] = ucPara;//均衡电路故障告警
			break;
		case 0xBC:
			s_tSysPara.aucRelayBit[52] = ucPara;//停电时间保护告警
			break;
		case 0xBD:
			s_tSysPara.aucRelayBit[53] = ucPara;//加热膜失效告警
			break;
		case 0xBE:
			s_tSysPara.aucRelayBit[54] = ucPara;//熔丝损坏
			break;
		case 0xBF:
			s_tSysPara.aucRelayBit[55] = ucPara;//连接器温度高保护
			break;
        default:
        	return False;
    }
    return True;
}

static BYTE SetAlarmRelay_CmdTypeCx(BYTE ucPara) {
    switch (s_tProtocol.ucCommandType){
		case 0xC0:
			s_tSysPara.aucRelayBit[56] = ucPara;//主继电器失效
			break;
		case 0xC1:
			s_tSysPara.aucRelayBit[57] = ucPara;//DCDC故障
			break;
		case 0xC2:
			s_tSysPara.aucRelayBit[58] = ucPara;//采集异常
			break;
		case 0xC3:
			s_tSysPara.aucRelayBit[59] = ucPara;//辅助源故障
			break;
		case 0xC4:
			s_tSysPara.aucRelayBit[62] = ucPara;//单体动态欠压保护
			break;
		case 0xC5:
			s_tSysPara.aucRelayBit[63] = ucPara;//加热膜连接器高温保护
			break;
		case 0xC6:
			s_tSysPara.aucRelayBit[69] = ucPara;//容量衰减一致性异常
			break;
        default:
        	s_tProtocol.ucRTN = RTN_WRONG_COMMAND;  /* 命令格式错 */
        	return False;
    }
    return True;
}

static void SetAlarmRelayAll(BYTE ucPara) {
    if (SetAlarmRelay_CmdType8x(ucPara)) {
        return;
    }
    if (SetAlarmRelay_CmdType9x(ucPara)) {
        return;
    }
    if (SetAlarmRelay_CmdTypeA0_6(ucPara)) {
        return;
    }
	if (SetAlarmRelay_CmdTypeA7_F(ucPara)) {
        return;
    }
	if (SetAlarmRelay_CmdTypeB0_6(ucPara)) {
        return;
    }
	if (SetAlarmRelay_CmdTypeB7_F(ucPara)) {
        return;
    }
	if (SetAlarmRelay_CmdTypeCx(ucPara)) {
        return;
    }
    return;
}

static void SetAlarmRelay(BYTE ucPort)
{

	BYTE ucPara;
	GetSysPara( &s_tSysPara );
	s_tProtocol.ucCommandType	= s_tProtocol.aucRecBuf[7];
	ucPara = s_tProtocol.aucRecBuf[9]; 

    SetAlarmRelayAll(ucPara);

	if (s_tProtocol.ucRTN == RTN_INVALID_DATA || s_tProtocol.ucRTN == RTN_WRONG_COMMAND) {
        return;
    }

	if ( False == SetSysPara( &s_tSysPara, True, CHANGE_BY_APPTEST ) )
	{
		s_tProtocol.ucRTN = RTN_INVALID_DATA;
	}

	//设置保存参数缺省值
	GetSysPara( &s_tSysPara );
	WriteBMSDefaultPara( &s_tSysPara );

	return;
}

//新增浮点数校正指令
static void SetBMSAdjustDataNew(BYTE ucPort)
{
	WORD wTemp;
	U_32FLOAT tTemp;
	//T_BduRealDataStruct tBduData;
	if ( ucPort >= SCI_PORT_NUM )
	{
		return;
	}
    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];

	//GetBduRealData(&tBduData);

    if(((s_tProtocol.ucCommandType >= 0x80)
    	&& (s_tProtocol.ucCommandType <= 0x87))
		|| s_tProtocol.ucCommandType == 0x8A
		/*|| s_tProtocol.ucCommandType == 0xA0
		|| s_tProtocol.ucCommandType == 0xA1*/)
    {
    	//wTemp = (WORD)(Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+8)));
		tTemp.ucData[0] = s_tProtocol.aucRecBuf[8];
		tTemp.ucData[1] = s_tProtocol.aucRecBuf[9];
		tTemp.ucData[2] = s_tProtocol.aucRecBuf[10];
		tTemp.ucData[3] = s_tProtocol.aucRecBuf[11];
		wTemp = (WORD)tTemp.fData;

	  //  if((wTemp > 1751 || wTemp < 1167) && (s_tProtocol.ucCommandType == 0x80 || s_tProtocol.ucCommandType == 0x82))
		//{
		//	s_tProtocol.ucRTN  = RTN_INVALID_DATA;
		//	return;
		//}
		//if((wTemp > 5671 || wTemp < 3781) && (s_tProtocol.ucCommandType == 0x81 || s_tProtocol.ucCommandType == 0x83))
		//{
		//	s_tProtocol.ucRTN  = RTN_INVALID_DATA;
		//	return;
		//}
		//if(wTemp != 0 && (s_tProtocol.ucCommandType >= 0x84 && s_tProtocol.ucCommandType <= 0x87))
		//{
		//	s_tProtocol.ucRTN  = RTN_INVALID_DATA;
		//	return;
		//}

		BduAdj(s_tProtocol.ucCommandType, wTemp);
    }
    else
    {
         s_tProtocol.ucRTN  = RTN_WRONG_COMMAND;    // 命令格式错
         return;
    }

	return;

}

Static void SetBMSChgVolt(BYTE ucPort)
{
	U_Int tData;
	T_DCPara tDCPara;
	BYTE ucDataLen = 2;
	if ( ucPort >= SCI_PORT_NUM )
	{
			return;
	}

	rt_memset(&tDCPara, 0, sizeof(T_DCPara));
	GetBduPara(&tDCPara);

	s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];

	tData.ucByte[0] = s_tProtocol.aucRecBuf[9];
	tData.ucByte[1] = s_tProtocol.aucRecBuf[8];

	if (s_tProtocol.ucCommandType == 0x80)
	{
		if(ucDataLen != s_tProtocol.wRecLenid / 2 - 1)
		{
			s_tProtocol.ucRTN = RTN_INVALID_DATA;
			return;
		}
		if(tData.iData / 100.0f < 23.0f || tData.iData / 100.0f > 29.0f)
		{
			s_tProtocol.ucRTN = RTN_INVALID_DATA;
			return;
		}

		if((tDCPara.wChgBusCurVal * g_ProConfig.fRateCurrChgBus / BDU_LIMIT_MAX) >= 2.0f && (tDCPara.wChgBusCurVal * g_ProConfig.fRateCurrChgBus / BDU_LIMIT_MAX) <= 20.0f)
		{
			// tDCPara.wChgVolVal = tTemp.fData * 100.0;
			tDCPara.wChgVolVal = tData.iData;
		}
		else
		{
			s_tProtocol.ucRTN = RTN_FAIL_COMMAND;
			return;
		}
	}

	else
	{
		s_tProtocol.ucRTN = RTN_WRONG_COMMAND; /*wrong command format*/
		return;
	}
	SetBduPara(&tDCPara);
    return;
}

static void GetBmsParaNew(BYTE ucPort)
{
	BYTE *p = NULL;
	WORD wSendLen = 0;
	if ( ucPort >= SCI_PORT_NUM )
	{
		return;
	}
	rt_memset(&s_tSysPara, 0, sizeof(T_SysPara));    
    GetSysPara(&s_tSysPara);
	p = s_tProtocol.aucSendBuf;
	*p++ = GetDataFlag( BCM_ALARM, ucPort );
	wSendLen++;
	*p++ = 0; // s_tSysPara.bThrough
	wSendLen++;
	*(SHORT*)p  = Host2Modbus((SHORT*)&s_tSysPara.wBattSOCLowAlmThre);
	p += 2;
	wSendLen += 2;
	*(SHORT*)p  = Host2Modbus((SHORT*)&s_tSysPara.wBattSOCLowPrtThre);
	p += 2;
	wSendLen += 2;
	*(SHORT*)p  = Host2Modbus((SHORT*)&s_tSysPara.wBattSOHAlmThre);
	p += 2;
	wSendLen += 2;
	*(SHORT*)p  = Host2Modbus((SHORT*)&s_tSysPara.wBattSOHPrtThre);
	p += 2;
	wSendLen += 2;
	*(FLOAT*)p = s_tSysPara.fBattUnderVoltAlmThre;
	p += 4;
	wSendLen += 4;
	*(FLOAT*)p = s_tSysPara.fBattUnderVoltPrtThre;
	p += 4;
	wSendLen += 4;
	*(FLOAT*)p = s_tSysPara.fRemoteSupplyOutVolt;
	p += 4;
	wSendLen += 4;
	*(FLOAT*)p = 0;	// s_tSysPara.fPowerDownVoltThre
	wSendLen += 4;
	p += 4;
	*p++ = 0;  // 节能功能
	wSendLen++;
	*(SHORT*)p  = Host2Modbus((SHORT*)&s_tSysPara.wDischgSwitchSOC);
	p += 2;
	wSendLen += 2;
	*(SHORT*)p  = FloatChangeToModbus(s_tSysPara.fChgHeaterStartupTemp);//充电加热膜启动温度
	p += 2;
	wSendLen += 2;
	*(SHORT*)p  = FloatChangeToModbus(s_tSysPara.fChgHeaterShutdownTemp);//充电加热膜停止温度
	p += 2;
	wSendLen += 2;

	s_tProtocol.wSendLenid = wSendLen*2;
	return;
}

static void GetSpecificPara(BYTE ucPort)
{
	BYTE *p = NULL;
	WORD wSendLen = 0;

	if ( ucPort >= SCI_PORT_NUM )
	{
		return;
	}
	rt_memset(&s_tSysPara, 0, sizeof(T_SysPara));    
    GetSysPara(&s_tSysPara);
	p = s_tProtocol.aucSendBuf;
	*p++ = GetDataFlag( BCM_ALARM, ucPort );
	wSendLen++;
	*p++ = s_tSysPara.ucCellUVPDelay;
	wSendLen++;
	// *(SHORT*)p = Host2Modbus((SHORT*)&s_tSysPara.wDischgSwitchSOC);
	*(SHORT*)p = 0;
	p += 2;
	wSendLen += 2;

    *(SHORT*)p = 0x0000;

	p += 2;
	wSendLen += 2;

	*p++ = 0; //高字节填充0
	*p++ = s_tSysPara.aucAlarmLevel[47];//BDU电池闭锁告警:47
	wSendLen += 2;

	*p++ = 0; //高字节填充0
	*p++ = s_tSysPara.aucAlarmLevel[38];//环境温度高保护:38
	wSendLen += 2;

	*p++ = 0; //高字节填充0
	*p++ = s_tSysPara.aucAlarmLevel[39];//环境温度低保护:39
	wSendLen += 2;

	*p++ = 0; //高字节填充0
	*p++ = s_tSysPara.aucAlarmLevel[17];//单板过温告警:17
	wSendLen += 2;

	*p++ = 0; //高字节填充0
	*p++ = s_tSysPara.aucRelayBit[47];//BDU电池闭锁告警:47
	wSendLen += 2;

	*p++ = 0; //高字节填充0
	*p++ = s_tSysPara.aucRelayBit[38];//环境温度高保护:38
	wSendLen += 2;

	*p++ = 0; //高字节填充0
	*p++ = s_tSysPara.aucRelayBit[39];//环境温度低保护:39
	wSendLen += 2;

	*p++ = 0; //高字节填充0
	*p++ = s_tSysPara.aucRelayBit[17];//单板过温告警:17
	wSendLen += 2;

	*(SHORT*)p = FloatChangeToModbus(s_tSysPara.fCellUnderVoltAlmThre * 100);
	p += 2;
	wSendLen += 2;
	*(SHORT*)p = FloatChangeToModbus(s_tSysPara.fCellUnderVoltPrtThre * 100);
	p += 2;
	wSendLen += 2;
	*(SHORT*)p = FloatChangeToModbus(s_tSysPara.fDischargeEndVolt1 * 100);// 末期放电电压1(V)
	p += 2;
	wSendLen += 2;
	*(SHORT*)p = FloatChangeToModbus(s_tSysPara.fDischargeEndVolt2 * 100);// 末期放电电压2(V)
	p += 2;
	wSendLen += 2;	

    *(SHORT*)p = Host2Modbus((SHORT*)&s_tSysPara.wSoftAntiTheftDelay);// 软件防盗延时
    p += 2;
    wSendLen += 2;

    p += 30;  // 预留字节
    wSendLen += 30;

    s_tProtocol.wSendLenid = wSendLen*2;
    return;
}

static void GetBMSAdjustData(BYTE ucPort)
{
	T_DCCaliGet t_AdjPara;
	BYTE *p = NULL;

	if ( ucPort >= SCI_PORT_NUM )
	{
		return;
	}
	GetBduAdj(&t_AdjPara);
	p = s_tProtocol.aucSendBuf;

	*p++ = 0x80;
	*(SHORT*)p = Host2Modbus((SHORT*)&t_AdjPara.wBusVoltScal);			// 母排侧电压校正
	p+=2;

	*p++ = 0x81;
	*(SHORT*)p = Host2Modbus((SHORT*)&t_AdjPara.wBusPinVoltScal);		// 电池电流比例校正
	p+=2;

	*p++ = 0x82;
	*(SHORT*)p = Host2Modbus((SHORT*)&t_AdjPara.wBatVoltScal);			// 电池侧电压（BAT_DET）校正
	p+=2;

	*p++ = 0x83;
	*(SHORT*)p = Host2Modbus((SHORT*)&t_AdjPara.wBusChaCurrScal);		// 母排侧充电电流校正
	p+=2;

	*p++ = 0x84;
	*(SHORT*)p = Host2Modbus((SHORT*)&t_AdjPara.wBusDischaCurrScal);	// 母排侧放电电流校正
	p+=2;

	*p++ = 0x85;
	*(SHORT*)p = Host2Modbus((SHORT*)&t_AdjPara.wBatChaCurrScal);		// 电池侧充电电流校正
	p+=2;

	*p++ = 0x86;
	*(SHORT*)p = Host2Modbus((SHORT*)&t_AdjPara.wBatDischaCurrLScal);	// 电池侧放电80A电流校正
	p+=2;

	*p++ = 0x87;
	*(SHORT*)p = Host2Modbus((SHORT*)&t_AdjPara.wBatDischaCurrHScal);	// 电池侧放电200A电流校正
	p+=2;

	*p++ = 0x88;
	*(SHORT*)p = Host2Modbus((SHORT*)&t_AdjPara.wBusVoltZero);			// 母排侧电压零点校正
	p+=2;

	*p++ = 0x89;
	*(SHORT*)p = Host2Modbus((SHORT*)&t_AdjPara.wBusPinVoltZero);		// 电池电流零点校正
	p+=2;

	*p++ = 0x8A;
	*(SHORT*)p = Host2Modbus((SHORT*)&t_AdjPara.wBatVoltZero);			// 电池侧电压（BAT_DET）零点校正
	p+=2;

	*p++ = 0x8B;
	*(SHORT*)p = Host2Modbus((SHORT*)&t_AdjPara.wBusChaCurrZero);		// 母排侧充电电流零点校正
	p+=2;

	*p++ = 0x8C;
	*(SHORT*)p = Host2Modbus((SHORT*)&t_AdjPara.wBusDischaCurrZero);	// 母排侧放电电流零点校正
	p+=2;

	*p++ = 0x8D;
	*(SHORT*)p = Host2Modbus((SHORT*)&t_AdjPara.wBatChaCurrZero);		// 电池侧充电电流零点校正
	p+=2;

	*p++ = 0x8E;
	*(SHORT*)p = Host2Modbus((SHORT*)&t_AdjPara.wBatDischaCurrLZero);	// 电池侧放电80A电流零点校正
	p+=2;

	*p++ = 0x8F;
	*(SHORT*)p = Host2Modbus((SHORT*)&t_AdjPara.wBatDischaCurrHZero);	// 电池侧放电200A电流零点校正
	p+=2;

	s_tProtocol.wSendLenid = (WORD)((p - s_tProtocol.aucSendBuf)*2);
}


/****************************************************************************
* 函数名称：GetTestData(BYTE ucPort)
* 调    用：无
* 被 调 用：
* 输入参数：ucPort-串口号
* 返 回 值：
* 功能描述：获取测试数据
* 作    者：hxk
* 设计日期：2023-09-14
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
static void GetTestData(BYTE ucPort)
{
   BYTE *p = NULL;
   BYTE i = 0;
   T_BCMDataStruct tBCMAnaData;
   T_DCTestData   tTestData;
   T_DCTestPara tDCTestPara;

	if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }

    rt_memset(&tBCMAnaData, 0, sizeof(T_BCMDataStruct));
	rt_memset(&tTestData, 0, sizeof(T_DCTestData));
	rt_memset(&tDCTestPara, 0, sizeof(T_DCTestPara));

	GetRealData(&tBCMAnaData);
    GetBduTestData(&tTestData);
	GetBduTestPara(&tDCTestPara);

	rt_memset(s_tProtocol.aucSendBuf, 0x00, sizeof(s_tProtocol.aucSendBuf));
	p  = s_tProtocol.aucSendBuf;

    /*DATA_FLAG*/
	*p++ = GetDataFlag( BCM_ALARM, ucPort );                    //DATA_FLAG

    /*6个连接器温度*/
	for(i = 0;i < Heat_Conn_TEMP_NUM_MAX; i++)
	{
		*(FLOAT*)p = tBCMAnaData.afHeatConnTemp[i];
	    p += 4;
	}

	*(FLOAT*)p = tTestData.sCurrentInfo1 / 100.0f;     //电流信息1
	p += 4;

	*(FLOAT*)p = tTestData.sCurrentInfo2 / 100.0f;     //电流信息2
	p += 4;

	*(FLOAT*)p = tTestData.sCurrentInfo3 / 100.0f;     //电流信息3
	p += 4;

	*(FLOAT*)p = tTestData.sCurrentInfo4 / 100.0f;     //电流信息4
	p += 4;

	*(FLOAT*)p = tTestData.sCurrentInfo5 / 100.0f;     //电流信息5
	p += 4;

	*(FLOAT*)p = tTestData.wVoltageInfo1 / 100.0f;     //电压信息1
	p += 4;

	*(FLOAT*)p = tTestData.wVoltageInfo2 / 100.0f;     //电压信息2
	p += 4;

	*(FLOAT*)p = tTestData.wVoltageInfo3 / 100.0f;     //电压信息3
	p += 4;

	*(FLOAT*)p = tTestData.wVoltageInfo4 / 100.0f;     //电压信息4
	p += 4;

	*(FLOAT*)p = tTestData.sBoardTemp1 / 10.0f;     //单板温度1
	p += 4;

	*(FLOAT*)p = tTestData.sBoardTemp2 / 10.0f;     //单板温度2
	p += 4;

	*(FLOAT*)p = tTestData.sBoardTemp3 / 10.0f;     //单板温度3
	p += 4;

	*(FLOAT*)p = tTestData.sConnTemp1 / 10.0f;     //连接器温度1
	p += 4;

	*(FLOAT*)p = tTestData.sConnTemp2 / 10.0f;     //连接器温度2
	p += 4;

	*(FLOAT*)p = tTestData.sEnvTemp / 10.0f;       //环境温度
	p += 4;

    *(FLOAT*)p = tTestData.wHeaterVol / 100.0f;    //加热膜电压值
	p += 4;

	*(SHORT*)p  = Host2Modbus((SHORT*)&tDCTestPara.sWavePointChg);       // 充电逐波点
	p += 2;

	*(SHORT*)p  = Host2Modbus((SHORT*)&tDCTestPara.sWavePointDischg);    // 放电逐波点
	p += 2;

    /* LENID */
    s_tProtocol.wSendLenid  = (WORD) ((p-s_tProtocol.aucSendBuf)*2);

	return;
}

Static void GetCustomerName(BYTE ucPort)
{
    BYTE *p = NULL;
    T_BmsCustomerNameStruct tBmsCustomerName;

    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }

    rt_memset_s(&tBmsCustomerName, sizeof(T_BmsCustomerNameStruct), 0, sizeof(T_BmsCustomerNameStruct));

    p  = s_tProtocol.aucSendBuf;
    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));

    readBmsCustomerName(&tBmsCustomerName);
    MemsetBuff(p, &tBmsCustomerName.acBmsCustomerName[0], 17, 17, 0x20);//客户名称
    //MemsetBuff将最后一个字节赋值为0，最后一个字节保持原先的不变
    rt_memcpy_s(p + 16, 1, &tBmsCustomerName.acBmsCustomerName[0] + 16, 1);

    p += 17;

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf)*2);
}

Static void SetCustomerName(BYTE ucPort)
{
    T_BmsCustomerNameStruct  tBmsCustomerName;

    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }

    rt_memset_s((BYTE*)&tBmsCustomerName, sizeof(T_BmsCustomerNameStruct), 0x00, sizeof(T_BmsCustomerNameStruct));

    readBmsCustomerName(&tBmsCustomerName);

    rt_memcpy_s((BYTE *)&(tBmsCustomerName.acBmsCustomerName[0]), sizeof(tBmsCustomerName.acBmsCustomerName), (BYTE *)(&s_tProtocol.aucRecBuf[7]), sizeof(tBmsCustomerName.acBmsCustomerName));
	if(!CheckCharRange((BYTE *)&tBmsCustomerName.acBmsCustomerName[0], sizeof(tBmsCustomerName.acBmsCustomerName)))
    {
        s_tProtocol.ucRTN  = RTN_INVALID_DATA;
        return;
    }

    writeBmsCustomerName(&tBmsCustomerName);

    return;
}


static void RecvSysParaById(BYTE ucPort)
{
    WORD wParaId = MakeParaId(s_tProtocol.aucRecBuf[7], s_tProtocol.aucRecBuf[8]);
    WORD wRecvParaLen = s_tProtocol.wRecLenid / 2 - 2;
    BYTE *p = &s_tProtocol.aucRecBuf[9];

    int sz = SetParaById(wParaId, p, wRecvParaLen, EndianConvert);
    if (sz < 0)
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
        return;
    }
    GetSysPara(&s_tSysPara);
    WriteBMSDefaultPara(&s_tSysPara);
    return;
}



static void SendSysParaById(BYTE ucPort)
{
    BYTE *p = s_tProtocol.aucSendBuf;
    WORD wParaId = 0;
    int sz = -1;
    rt_memset_s(p, sizeof(s_tProtocol.aucSendBuf), 0x00, sizeof(s_tProtocol.aucSendBuf));

    if (s_tProtocol.aucRecBuf[8] != 0xFF)
    {
        wParaId = MakeParaId(s_tProtocol.aucRecBuf[7], s_tProtocol.aucRecBuf[8]);
        sz = GetParaById(wParaId, p, sizeof(s_tProtocol.aucSendBuf), EndianConvert);
    }
    else
    {
        sz = GetMultiParaBySection(s_tProtocol.aucRecBuf[7], p, sizeof(s_tProtocol.aucSendBuf), EndianConvert);
    }

    if (sz < 0)
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
        return;
    }
    p += sz;

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
}

