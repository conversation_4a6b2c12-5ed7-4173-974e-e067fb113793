#include "network_crt_parse.h"
#include "x509_crt.h"
#include "partition_def.h"
#include "realdata_id_in.h"
#include "realdata_save.h"


int parse_crt(cert_time_sid_t* start_sid, cert_time_sid_t* end_sid, char* file_name)
{   
    int ret = FAILURE;
    mbedtls_x509_crt cert = {0};
    time_base_t start_date = {0};
    time_base_t end_date = {0};
    // 第一个网管
    if (0 != (ret = mbedtls_x509_crt_parse_file(&cert, file_name))) 
    {   
        LOG_E("parse %s crt failed returned 0x%04x", file_name, (ret < 0 )? -ret : ret);
    }

    start_date.year = cert.valid_from.year;
    start_date.month = cert.valid_from.mon;
    start_date.day = cert.valid_from.day;
    start_date.hour = cert.valid_from.hour;
    start_date.minute = cert.valid_from.min;
    start_date.second = cert.valid_from.sec;
    set_one_data(start_sid->cert_time_year, &start_date.year);
    set_one_data(start_sid->cert_time_month,&start_date.month);
    set_one_data(start_sid->cert_time_day, &start_date.day);
    set_one_data(start_sid->cert_time_hour, &start_date.hour);
    set_one_data(start_sid->cert_time_minute, &start_date.minute);
    set_one_data(start_sid->cert_time_second, &start_date.second);
    set_one_data(start_sid->cert_time, &start_date);

    end_date.year = cert.valid_to.year;
    end_date.month = cert.valid_to.mon;
    end_date.day = cert.valid_to.day;
    end_date.hour = cert.valid_to.hour;
    end_date.minute = cert.valid_to.min;
    end_date.second = cert.valid_to.sec;
    set_one_data(end_sid->cert_time_year, &end_date.year);
    set_one_data(end_sid->cert_time_month, &end_date.month);
    set_one_data(end_sid->cert_time_day, &end_date.day);
    set_one_data(end_sid->cert_time_hour, &end_date.hour);
    set_one_data(end_sid->cert_time_minute, &end_date.minute);
    set_one_data(end_sid->cert_time_second, &end_date.second);
    set_one_data(end_sid->cert_time, &end_date);
    mbedtls_x509_crt_free(&cert);
    return ret;
}

int crt_date_parse(void)
{
    cert_time_sid_t server_cert_start=
    {
        DAC_DATA_ID_CA_CERT_START_YEAR_TIME,
        DAC_DATA_ID_CA_CERT_START_MONTH_TIME,
        DAC_DATA_ID_CA_CERT_START_DAY_TIME,
        DAC_DATA_ID_CA_CERT_START_HOUR_TIME,
        DAC_DATA_ID_CA_CERT_START_MINUTE_TIME,
        DAC_DATA_ID_CA_CERT_START_SECOND_TIME,
        DAC_DATA_ID_CA_CERT_START_TIME,
    };
    cert_time_sid_t server_cert_end=
    {
        DAC_DATA_ID_CA_CERT_END_YEAR_TIME,
        DAC_DATA_ID_CA_CERT_END_MONTH_TIME,
        DAC_DATA_ID_CA_CERT_END_DAY_TIME,
        DAC_DATA_ID_CA_CERT_END_HOUR_TIME,
        DAC_DATA_ID_CA_CERT_END_MINUTE_TIME,
        DAC_DATA_ID_CA_CERT_END_SECOND_TIME,
        DAC_DATA_ID_CA_CERT_END_TIME,
    };
    cert_time_sid_t client_cert_start=
    {
        DAC_DATA_ID_CLIENT_CERT_START_YEAR_TIME,
        DAC_DATA_ID_CLIENT_CERT_START_MONTH_TIME,
        DAC_DATA_ID_CLIENT_CERT_START_DAY_TIME,
        DAC_DATA_ID_CLIENT_CERT_START_HOUR_TIME,
        DAC_DATA_ID_CLIENT_CERT_START_MINUTE_TIME,
        DAC_DATA_ID_CLIENT_CERT_START_SECOND_TIME,
        DAC_DATA_ID_CLIENT_CERT_START_TIME,
    };
    cert_time_sid_t client_cert_end=
    {
        DAC_DATA_ID_CLIENT_CERT_END_YEAR_TIME,
        DAC_DATA_ID_CLIENT_CERT_END_MONTH_TIME,
        DAC_DATA_ID_CLIENT_CERT_END_DAY_TIME,
        DAC_DATA_ID_CLIENT_CERT_END_HOUR_TIME,
        DAC_DATA_ID_CLIENT_CERT_END_MINUTE_TIME,
        DAC_DATA_ID_CLIENT_CERT_END_SECOND_TIME,
        DAC_DATA_ID_CLIENT_CERT_END_TIME,
    };
    parse_crt(&server_cert_start, &server_cert_end, MQTT_CA_FILE);
    parse_crt(&client_cert_start, &client_cert_end, MQTT_CLIENT_FILE);

    cert_time_sid_t server_cert_start1=
    {
        DAC_DATA_ID_CA_CERT_START_YEAR_TIME + 1,
        DAC_DATA_ID_CA_CERT_START_MONTH_TIME+ 1,
        DAC_DATA_ID_CA_CERT_START_DAY_TIME+ 1,
        DAC_DATA_ID_CA_CERT_START_HOUR_TIME+ 1,
        DAC_DATA_ID_CA_CERT_START_MINUTE_TIME+ 1,
        DAC_DATA_ID_CA_CERT_START_SECOND_TIME+ 1,
        DAC_DATA_ID_CA_CERT_START_TIME+ 1,
    };
    cert_time_sid_t server_cert_end1=
    {
        DAC_DATA_ID_CA_CERT_END_YEAR_TIME + 1,
        DAC_DATA_ID_CA_CERT_END_MONTH_TIME+ 1,
        DAC_DATA_ID_CA_CERT_END_DAY_TIME+ 1,
        DAC_DATA_ID_CA_CERT_END_HOUR_TIME+ 1,
        DAC_DATA_ID_CA_CERT_END_MINUTE_TIME+ 1,
        DAC_DATA_ID_CA_CERT_END_SECOND_TIME+ 1,
        DAC_DATA_ID_CA_CERT_END_TIME+ 1,
    };
    cert_time_sid_t client_cert_start1=
    {
        DAC_DATA_ID_CLIENT_CERT_START_YEAR_TIME + 1,
        DAC_DATA_ID_CLIENT_CERT_START_MONTH_TIME+ 1,
        DAC_DATA_ID_CLIENT_CERT_START_DAY_TIME+ 1,
        DAC_DATA_ID_CLIENT_CERT_START_HOUR_TIME+ 1,
        DAC_DATA_ID_CLIENT_CERT_START_MINUTE_TIME+ 1,
        DAC_DATA_ID_CLIENT_CERT_START_SECOND_TIME+ 1,
        DAC_DATA_ID_CLIENT_CERT_START_TIME+ 1,
    };
    cert_time_sid_t client_cert_end1=
    {
        DAC_DATA_ID_CLIENT_CERT_END_YEAR_TIME + 1,
        DAC_DATA_ID_CLIENT_CERT_END_MONTH_TIME+ 1,
        DAC_DATA_ID_CLIENT_CERT_END_DAY_TIME+ 1,
        DAC_DATA_ID_CLIENT_CERT_END_HOUR_TIME+ 1,
        DAC_DATA_ID_CLIENT_CERT_END_MINUTE_TIME+ 1,
        DAC_DATA_ID_CLIENT_CERT_END_SECOND_TIME+ 1,
        DAC_DATA_ID_CLIENT_CERT_END_TIME+ 1,
    };
    parse_crt(&server_cert_start1, &server_cert_end1, MQTT_CA_OTHER_FILE);
    parse_crt(&client_cert_start1, &client_cert_end1, MQTT_CLIENT_OTHER_FILE);
    return SUCCESSFUL;

}
