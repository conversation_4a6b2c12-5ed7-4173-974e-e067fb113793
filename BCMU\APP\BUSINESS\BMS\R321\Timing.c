#include "common.h"
#include "rtthread.h"
#include "sample.h"
#include "utils_heart_beat.h"
#include "thread_id.h"
#include "battery.h"
#include "para.h"
#include "comm.h"
#include "flash.h"
#include <rtdevice.h>
#include "sys/time.h"
#include "const_define.h"
#include "pdt_version.h"
#include "rtthread.h"
#include "rtc_PFC8563.h"
#include "hisdata.h"
#include "utils_rtthread_security_func.h"
#include "ntp.h"
#include "Timing.h"

extern rt_err_t PFC8563set_date(Datebuff *Rcvdate);  //TODO: 待BSP添加对应声明后删除

static T_SysPara s_tSysPara;
static const INT8S s_cTimeZone[25] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -12};
Static UINT32 uiTiming;

void Process_Timing(void* parameter)
{
    time_t new_time;

#ifndef UNITEST
    uiTiming = 0;

    pre_thread_beat_f(THREAD_TIMING_COMM);

    while(1)
#endif
    {
        thread_beat_go_on(THREAD_TIMING_COMM);
        rt_thread_delay(1000);
        TimerPlus(uiTiming, 24*ONE_HOUR_SECONDS);

        if(TimeOut(uiTiming,24*ONE_HOUR_SECONDS))
        {
            uiTiming = 0;
            GetSysPara(&s_tSysPara);
            if(s_tSysPara.bAutoTimingEn == TRUE)
            {
                new_time = ntp_get_time((char *)s_tSysPara.acNtpIPAddr);
                if(new_time > 0)
                {
                    SetSysTimeByTimeStamp(new_time + GetTimeZone(s_tSysPara.ucTimeZone)*ONE_HOUR_SECONDS);
                }
            }
        }
    }
}


INT8S GetTimeZone(BYTE ucTimeZone)
{
    return (ucTimeZone < 25) ? s_cTimeZone[ucTimeZone] : 0;
}


BOOLEAN SetSysTimeByTimeStamp(time_t new_time)
{
    Datebuff tTime = {0};
    BYTE buff[20] = {0};
    T_DateStruct tDate = {0};
    struct tm tTimeBefore = {0};
    struct tm tTimeNow = {0};
    time_t t = time(RT_NULL);

    localtime_r(&t, &tTimeBefore);
    localtime_r(&new_time, &tTimeNow);

    tTime.PFC8563_Date.year   = tTimeNow.tm_year;
    tTime.PFC8563_Date.month  = tTimeNow.tm_mon + 1;
    tTime.PFC8563_Date.date   = tTimeNow.tm_mday;
    tTime.PFC8563_Date.hour   = tTimeNow.tm_hour;
    tTime.PFC8563_Date.min    = tTimeNow.tm_min;
    tTime.PFC8563_Date.second = tTimeNow.tm_sec;

    tDate.wYear   = tTime.PFC8563_Date.year + 1900;
    tDate.ucMonth = tTime.PFC8563_Date.month;
    tDate.ucDay   = tTime.PFC8563_Date.date;
    if (CheckDateValid(&tDate) && (PFC8563set_date(&tTime) == RT_EOK))/* 检查时间无效，则返回码失败 */
    {
        // TODO:时间检查通过，保存操作记录
        rt_snprintf_s((char *)buff, sizeof(buff), "%d-%02d-%02d %02d:%02d:%02d",
                 (WORD)(tTimeBefore.tm_year + 1900),
                 tTimeBefore.tm_mon +1,
                 tTimeBefore.tm_mday,
                 tTimeBefore.tm_hour,
                 tTimeBefore.tm_min,
                 tTimeBefore.tm_sec);
        SaveAction(GetActionId(CONTOL_SET_TIME), (char *)buff);
        return TRUE;
    }
    return FALSE;
}
