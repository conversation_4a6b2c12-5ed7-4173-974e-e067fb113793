#ifndef _BCMU_HIS_RECORD_H
#define _BCMU_HIS_RECORD_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

/*  Include   */
#include "time.h"
#include "device_type.h"
#include "msg.h"

#define MAX_HIS_DATA_NUM              12000
#define MIN_HIS_DATA_NUM              10000
#define MAX_HIS_DATA_NUM_PER_FILE     2000
#define HIS_DATA_FILE_NUM             ((MAX_HIS_DATA_NUM + MAX_HIS_DATA_NUM_PER_FILE -1) / MAX_HIS_DATA_NUM_PER_FILE)

#define MAX_HIS_ALARM_NUM             12000
#define MIN_HIS_ALARM_NUM             10000
#define MAX_HIS_ALARM_NUM_PER_FILE    2000
#define HIS_ALARM_FILE_NUM             ((MAX_HIS_ALARM_NUM + MAX_HIS_ALARM_NUM_PER_FILE - 1) / MAX_HIS_ALARM_NUM_PER_FILE)

#define MAX_HIS_EVENT_NUM             1200
#define MIN_HIS_EVENT_NUM             1000
#define MAX_HIS_EVENT_NUM_PER_FILE    200
#define HIS_EVENT_FILE_NUM             ((MAX_HIS_EVENT_NUM + MAX_HIS_EVENT_NUM_PER_FILE - 1) / MAX_HIS_EVENT_NUM_PER_FILE)

//实时告警
#define MAX_REAL_ALARM_NUM            1000
#define MIN_REAL_ALARM_NUM            0
#define MAX_REAL_ALARM_NUM_PER_FILE   1000
#define REAL_ALARM_FILE_NUM           1

#define MAX_RECORD_NAME_LEN           16

//##编译不通过，依据逆变的宏定义新增
#define REALDATA_ID_TYPE   0
#define CTRL_ID_TYPE       1

#pragma pack(push, 1)
/**
 * 历史数据存储结构体
 */
typedef struct
{
    unsigned char cluster_addr;       // 簇地址
    char cell_vol[80];                      //电芯电压
    short cell_temp[80];                //电芯温度
    short positive_pole_temp[4];       //正极点温度
    short negative_pole_temp[4];       //负极点温度
    short batt_vol[4];//电池模块电压
    short batt_temp[4];//电池模块电压
    short batt_cluster_curr;//电池簇电流
    short batt_cluster_vol; //电池簇电压
    short bus_ouput_vol;//母排输出电压
    unsigned short batt_cycle_index;//电池循环次数
    char env_temp;//环境温度
    char board_temp;//单板温度
    unsigned char batt_cluster_soh;//电池簇soh
    unsigned char batt_cluster_soc;//电池簇soc
    unsigned int cell_balanced_status1:1; //第一个BMU电芯均衡启动状态
    unsigned int cell_balanced_status2:1;//第二个BMU电芯均衡启动状态
    unsigned int cell_balanced_status3:1;//第三个BMU电芯均衡启动状态
    unsigned int cell_balanced_status4:1;//第四个BMU电芯均衡启动状态
    unsigned int batt_cluster_chg_proc:1;//电池簇充电保护
    unsigned int batt_cluster_dischg_proc:1;//电池簇放电保护
    unsigned int batt_cluster_chg_dischg_status:1;//电池簇充放电状态
    unsigned int batt_cluster_chg_enable:1;//电池簇充电使能状态
    unsigned int batt_cluster_dischg_enable:1;//电池簇放电使能状态
    unsigned int batt_cluster_chg_low_temp_alm:2;//电池簇充电低温告警
    unsigned int batt_cluster_over_vol:2;//电池簇过压
    unsigned int batt_cluster_chg_over_temp:2;//电池簇充电过温
    unsigned int batt_cluster_dischg_under_temp:2;//电池簇放电低温
    unsigned int batt_cluster_under_vol:2;//电池簇欠压
    unsigned int batt_cluster_dischg_over_temp:2;//电池簇放电过温
    unsigned int batt_cluster_chg_over_curr:2;//电池簇充电过流
    unsigned int batt_cluster_dischg_over_curr:2;//电池簇放电过流
    unsigned int chg_loop_dam:1;//充电回路损坏
    unsigned int dischg_loop_dam:1;//放电回路损坏
    unsigned int chg_curr_limit:1;//充电限流状态
    unsigned int dischg_curr_limit:1;//放电限流状态
}his_data_t;


typedef struct
{
    unsigned char  cell_vol_precision;                      //电芯电压
    unsigned char  cell_temp_precision;                //电芯温度
    unsigned char  positive_pole_temp_precision;       //正极点温度
    unsigned char  negative_pole_temp_precision;       //负极点温度
    unsigned char  batt_vol_precision;//电池模块电压
    unsigned char  batt_cluster_curr_precision;//电池簇电流
    unsigned char  batt_cluster_vol_precision; //电池簇电压
    unsigned char  bus_ouput_vol_precision;//母排输出电压
    unsigned char  batt_cycle_index_precision;//电池循环次数
    unsigned char  env_temp_precision;//环境温度
    unsigned char  board_temp_precision;//单板温度
    unsigned char  batt_cluster_soh_precision;//电池簇soh
    unsigned char  batt_cluster_soc_precision;//电池簇soc
}his_data_save_precision_t;

typedef struct
{
    int alarm_manage_id;
    short alarm_save_id[3]; // 3级告警映射
}lv3_alarm_manage_id_to_save_id_t;

typedef struct
{
    int alarm_manage_id;
    short alarm_save_id; // 告警映射
}alarm_manage_id_to_save_id_t;







#pragma pack(pop)

void init_his_record(void);
void change_alm_and_save(void* real_alm_v);
short change_sample_data_to_his_data(his_data_t* his_data,  unsigned char cluster_addr); //将采样数据转化成历史数据
void alm_change_trigger_save_his_data(module_msg_t* msg_rcv);

#ifdef __cplusplus
}
#endif  // __cplusplus

#endif  // _BCMU_HIS_RECORD_H
