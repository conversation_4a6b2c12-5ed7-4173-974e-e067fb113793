#ifndef BATTSOXCALC_H_
#define BATTSOXCALC_H_
#include "battery.h"
#include "fileSys.h"

void AccumulateDischargeCap(T_BattInfo *pBattIn, FLOAT *pfCap, ULONG ulTimeDiff);
void CalcBattSOH(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
//2、日历衰减
 void PlusCalenderLifeDecline(T_BattDealInfoStruct *pBattDeal,FLOAT fCellAverageTemp);
//4、SOH计算方法 ： 包含1、日历衰减 2、循环衰减 3、附加衰减
FLOAT CalcSOHFromDecline(T_BattDealInfoStruct *pBattDeal);

WORD CalcSOCFromCap(T_BattDealInfoStruct *pBattDeal, WORD wBattRateCap, BYTE ucPerc);//计算SOC

FLOAT OCVtoSOC(FLOAT fOCV);

#endif
