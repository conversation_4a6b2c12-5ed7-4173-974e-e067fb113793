/*******************************************************************************
  * @file        battery.h
  * @version     v1.0.0
  * @copyright   COPYRIGHT &copy; 2023 CSG
  * <AUTHOR>
  * @date        2023-4-27
  * @brief
  * @attention
  * Modification History
  * DATE         DESCRIPTION
  * ------------------------
  * - 2023-4-27  dongmengling Created
*******************************************************************************/

#ifndef BATTERY_H_  
#define BATTERY_H_  
#ifdef __cplusplus
extern "C" {
#endif

#include <time.h>
#include "data_type.h"

#define BATT_SOC_MIN             (0.0f)
#define BATT_SOC_MAX             (1.0f)

#define BATT_CAP  80     //以后增加参数


//电池状态定义
typedef enum {
    BATT_STATE_STANDBY = 0,            //待机
    BATT_STATE_CHARGE,             //充电
    BATT_STATE_DISCHARGE,          //放电
    BATT_STATE_INVALID,            //无效
}batt_state_e;

typedef struct {
    float batt_SOH;
    float batt_cap;
    float batt_cap_save;         //保存时的AH值
    unsigned char save_eeprom;   //是否保存
    float discharge_cap_for_celebrate; // 单次放出容量   , 用于SOH修正
    time_t       second_calc_time;       ////秒计时时刻点
    unsigned long second_time_diff_sec;  //秒计时两次时间差
    float calender_life_decline; //日历衰减
    float cycle_life_decline; //循环衰减
    float added_life_decline; //附加衰减
    time_base_t last_calc_calender_time;   //长期掉电时计算日历寿命衰减
    int batt_charge_minutes;  //电池充电时间
} batt_deal_info_t;

typedef struct {
    unsigned short batt_SOH;
    float batt_SOC;
    float calender_life_decline; //日历衰减
    float cycle_life_decline; //循环衰减
    float added_life_decline; //附加衰减
    unsigned short cycle_num;
}batt_result_t;

typedef struct {
    unsigned char   valid;                     ////是否有效
    float           batt_cap;                  //AH数
    time_base_t     last_calc_calender_time;   ////上次计算SOH的时间
    unsigned short  batt_dischg_times;         //放电次数
    float           added_life_decline;        ///附加衰减，校准时额外的衰减
    float           discharge_cap_for_Ntimes;  ////放出AH数；用于N+1计算
    float           total_discharge_cap;       // ////放出AH数，用于计算放电次数
    float           calender_life_decline;
    float           cycle_life_decline;
    float           multi_discharge_cap;       //累计放电容量
    float           multi_discharge_power;     //累计放电电量 new add
    unsigned char   reserve;                   //保留位置
    unsigned short  crc;                       ///CRC 
} batt_save_t;


#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // BATTERY_H_  ;