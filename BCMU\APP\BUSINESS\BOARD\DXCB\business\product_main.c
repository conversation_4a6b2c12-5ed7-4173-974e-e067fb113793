#include "product_main.h"
#include "data_type.h"
#include "para_id_in.h"
#include "realdata_id_in.h"
#include "para_manage.h"
#include "realdata_save.h"
#include "alarm_register.h"
#include "south_dev_common.h"
#include "compressor_protect_logic.h"
#include "data_process.h"
#include "utils_server.h"
#include "softbus.h"
#include "msg_id.h"
#include "utils_thread.h"
#include "dev_north_dxcb_modbus.h"

static msg_map s_msg_handle[] = {
    {NORTH_CTRL_RESET_DEVICE_MSG,    handle_reset_dxcb},
};      

static msg_map s_msg_map[] =
{
    {NORTH_CTRL_RESET_DEVICE_MSG,      msg_handle_nothing},
};

void handle_reset_dxcb(const rt_msg_t pMsg)
{
    rt_kprintf("system soft reset");
    rt_thread_mdelay(3000);
    rt_hw_cpu_reset();
}


void* init_product_main(void* param)
{
    server_info_t *server_info = (server_info_t *)param;
    server_info->server.server.map_size = sizeof(s_msg_map) / sizeof(msg_map);
    register_server_msg_map(s_msg_map, server_info);
    init_usart_commu_para();
    return NULL;
}

void product_main(void *parameter)
{
    unsigned short inter_fan_num = 0;
    unsigned short out_fan_num = 0;
    unsigned short compressor_brand = 0;
    unsigned short compressor_num = 0;
    while(is_running(TRUE))
    {
        get_one_para(DXCB_PARA_ID_COMPRESSORS_NUM_OFFSET, &compressor_num);
        get_one_para(DXCB_PARA_ID_FREQUENCY_CONVERTER_BRAND_OFFSET, &compressor_brand);
        get_one_para(DXCB_PARA_ID_INTERNAL_FAN_NUM_OFFSET, &inter_fan_num);
        get_one_para(DXCB_PARA_ID_EXTERNAL_FAN_NUM_OFFSET, &out_fan_num);
        // 排气温度计算
        ex_gas_temp_cal(); 
        // 压缩机保护逻辑处理
        if(!get_do_ctrl_flag())
        {
            compressor_protect_logic_deal(compressor_num, compressor_brand);
        }
        // 南向无效值的处理
        deal_invalid_south_data(inter_fan_num, out_fan_num, compressor_num);
        // 南向压缩机转换到北向
        deal_vfd_data(compressor_num, compressor_brand);
        fan_sync_data(inter_fan_num, out_fan_num);
        recv_msg_handle(s_msg_handle, sizeof(s_msg_handle) / sizeof(msg_map));
        rt_thread_mdelay(20);
    }
}

