
#ifndef _APP_UPDATE_MANAGE_H
#define _APP_UPDATE_MANAGE_H

#ifdef __cplusplus
extern "C" {
#endif

#include "device_type.h"

#define UPDATE_FIRST_FRAME_LEN 72
#define MSC_MAX_PACKET_1       128
#define MSC_MAX_PACKET_2       256

#define UPDATE_FILE_NAME_LEN   32
#define UPDATE_FILE_TIME_LEN   20

//下载部分的宏定义
#define TriggerEnd               0x00
#define JumpBootFail             0x81
#define DownloadFrameErr         0x80
#define DownFileNameErr          0x86
#define DownloadFuncErr          0x84
#define DownloadCRCErr           0x83

#define TRIGGER_COUNTER        3
#define INTERRUPT_DATA_LEN     1
#define INTERRUPT_NOT_DISCARD  0
#define INTERRUPT_DISCARD      1

#pragma pack(4)


//升级或备份文件记录数据
#define    FILE_NAME_LEN    32
#define    FILE_TIME_LEN    20
#define    FLAG_IAP         0x55    //待升级
#define    FLAG_CAN_IAP     0x56    //待升级CAN
#define    FLAG_APP         0x66    //0xAA 已烧写    //升级结束
#define    FLAG_BACKUP      0x88    //运行正常待备份
#define    FLAG_OK          0x99    //已备份

//升级或备份文件记录管理
typedef struct
{
    unsigned short    data_lenth_per_frame;
    unsigned short    total_frame_num;
    unsigned int      total_file_length;
    char     file_name[UPDATE_FILE_NAME_LEN];
    char     file_time[UPDATE_FILE_TIME_LEN];
    unsigned short    file_check;
    unsigned short    resv;
}first_frame_inf_t;


/**
 * 升级触发交互控制信息
 */
typedef struct {
    unsigned char       rtn;                                 ///<  返回码 00 正确      0x80 附加码错误 0x86 文件名错误 
    char                file_name[UPDATE_FILE_NAME_LEN];        ///<  对端下发的升级文件名称
} trig_ctr_inf_t;


#pragma pack()

void update_manage_init(void);

#ifdef __cplusplus
}
#endif

#endif  // __APP_UPDATE_MANAGE_H

