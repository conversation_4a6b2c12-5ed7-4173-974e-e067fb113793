#ifndef _DEV_NORTH_SEUB_APPTEST_H
#define _DEV_NORTH_SEUB_APPTEST_H

#ifdef __cplusplus
extern "C" {
#endif /*__cplusplus */

#include "device_type.h"
#include <board.h>

#define TEST_FAULT  1 //测试结果异常
#define TEST_NORMAL 0 //测试结果正常

// CID2
#define CID2_TRIG_APPTEST           0x00   // 触发apptest命令
#define CID2_EXIT_APPTEST           0x01   // 退出apptest命令
#define CID2_GET_ANA_INPUT_DATA     0x02   // 获取模拟量AI数据
#define CID2_GET_DIG_INPUT_DATA     0x03   // 获取数字量DI数据
#define CID2_SET_ANA_OUTPUT_DATA    0x04   // 设置模拟量AO数据
#define CID2_SET_DIG_OUTPUT_DATA    0x05   // 设置数字量DO数据
#define CID2_GET_FACTORY_INFO       0x06   // 获取厂家信息
#define CID2_SET_FACTORY_INFO       0x07   // 设置厂家信息
#define CID2_GET_MAC_ADDR           0x08   // 获取MAC地址
#define CID2_SET_MAC_ADDR           0x09   // 设置MAC地址
#define CID2_EEPROM_SELF_TEST       0x0A   // EEPROM自测命令
#define CID2_USART_LOOPBACK_TEST    0x0B   // 串口自回环测试

#define SEUB_TRIG_APPTEST           1   // 触发apptest命令
#define SEUB_EXIT_APPTEST           2   // 退出apptest命令
#define SEUB_GET_ANA_INPUT_DATA     3   // 获取模拟量AI数据
#define SEUB_GET_DIG_INPUT_DATA     4   // 获取数字量DI数据
#define SEUB_SET_ANA_OUTPUT_DATA    5   // 设置模拟量AO数据
#define SEUB_SET_DIG_OUTPUT_DATA    6   // 设置数字量DO数据
#define SEUB_GET_FACTORY_INFO       7   // 获取厂家信息
#define SEUB_SET_FACTORY_INFO       8   // 设置厂家信息
#define SEUB_GET_MAC_ADDR           9   // 获取MAC地址
#define SEUB_SET_MAC_ADDR           10  // 设置MAC地址
#define SEUB_EEPROM_SELF_TEST       11  // EEPROM自测命令
#define SEUB_USART_LOOPBACK_TEST    12  // 串口自回环测试

#define SEUB_SOFTWARE_NAME_LEN            20   // 软件名称长度
#define SEUB_SOFTWARE_VER_LEN             20   // 软件版本长度
#define SEUB_SOFTWARE_RELEASE_DATE_LEN    20   // 软件发布日期长度
#define SEUB_SERIAL_NUMBER_LEN            20   // 序列号长度
#define SEUB_SYSTEM_NAME_LEN              20   // 系统名称长度
#define SEUB_HARDWARE_VERSION_LEN         20   // 硬件版本信息长度
#define SEUB_FACTORY_INFO_TOTEL_LEN       (SEUB_SOFTWARE_NAME_LEN + SEUB_SOFTWARE_VER_LEN + SEUB_SOFTWARE_RELEASE_DATE_LEN + \
                                           SEUB_SERIAL_NUMBER_LEN + SEUB_SYSTEM_NAME_LEN + SEUB_HARDWARE_VERSION_LEN) // 厂家信息总长度
#define SEUB_FACTORY_INFO_EEPROM_LEN      (SEUB_SERIAL_NUMBER_LEN + SEUB_SYSTEM_NAME_LEN + SEUB_HARDWARE_VERSION_LEN) // 存储在EEPROM上的厂家信息长度

dev_type_t* init_seub_dev_apptest(void);
void apptest_register_cmd_table();
unsigned int get_seub_apptest_flag();

#ifdef __cplusplus
}
#endif //  __cplusplus

#endif // _DEV_NORTH_SEUB_APPTEST_H