#ifndef SOFTWARE_SRC_APP_FILESYS_H_ 
#define SOFTWARE_SRC_APP_FILESYS_H_ 

#include "common.h"
#include "realAlarm.h"
#include "protocol.h"
#include "wireless.h"
#include "hisdata.h"
#include "realAlarm.h"
#include "flash.h"
#include <rtdef.h>
#include <sys/unistd.h>
#include "prtclWireless.h"

#ifdef __cplusplus
extern "C" {
#endif

#define MAX_RECORD_SIZE     512

//文件名宏定义
#define  FILE_NAME_PARA          "/para"          // 参数文件
#define  FILE_NAME_BACKUP_PARA   "/keypara"       // 备份参数文件
#define  FILE_NAME_DEFAULT_PARA  "/defaultpara"   // 默认参数文件
#define  FILE_NAME_DCR_CNT       "/DcrCnt"        // 直流内阻计数文件
#define  FILE_NAME_DEFENCEINFO        "/DefenceInfo"    // 布防信息
#define  FILE_NAME_CUSTOMER_NAME "/customername"  // 客户名称文件
#define  FILE_NAME_BATT_EXIST    "/BattExist"     // 电池在位信息记录文件

enum
{
    BMS_ADDR = 0,
    RESET_REASON,
    BATT_THEFT,
    DCR_FAULT_ALM_PRT,// 直流内阻异常告警和直流内阻异常保护的掉电保存
    BMS_CAN2_ADDR,
    SITE_THEFT, // 站点级
    NET_THEFT,  // 网管级
    RESET_DATA_NUM, // reset data数量
};

typedef struct
{
    rt_uint16_t wRecordSize; /* 大小不得超过MAX_RECORD_SIZE */
    rt_uint8_t *pucData;
    const char *pcFilename;
    rt_uint16_t wMinSaveNum;
    rt_uint16_t wReadPoint[NORTH_PROTOCOL_NUM_MAX];
    rt_uint16_t wSavedNum;
}T_HisRecord;

typedef struct
{
    WORD wReadPoint_action[NORTH_PROTOCOL_NUM_MAX];
    WORD wReadPoint_alarm[NORTH_PROTOCOL_NUM_MAX];
    WORD wReadPoint_data[NORTH_PROTOCOL_NUM_MAX];
    //WORD wReadPoint_dataTower;
    WORD wCRC;
}T_HisPoint;


typedef struct 
{
    BYTE CustomizedBmsVersion[20];
    BYTE CustomizedBmsReleaseDate[4];
    BYTE CustomizedBduVersion[6];
    BYTE CustomizedBduRealseDate[4];
    WORD wCrc;
} T_SoftwareCustomizedInfo; //客户个性化配置信息


void initFileSys(void);
BOOLEAN readBmsHWPara(T_HardwareParaStruct *ptHWPara);
void writeBmsHWPara(T_HardwareParaStruct *ptHWPara);
BOOLEAN readNetMacPara(T_NetMacStruct *ptNetMacPara);
void writeNetMacPara(T_NetMacStruct *ptNetMacPara);
void readNorFlashTestPara(WORD *wNorFlashTestData);
void writeNorFlashTestPara(WORD *wNorFlashTestData);
BOOLEAN saveResetData(BYTE ucType, BYTE ucData);
BYTE getResetData(BYTE ucType);
void WrEEPCap( T_BattSaveStruct* ptBattSave );
BOOLEAN RdEEPCap( T_BattSaveStruct* ptBattSave);
void WrEEPCapNew(T_BattSaveNewStruct* ptBattSaveNew);
BOOLEAN RdEEPCapNew(T_BattSaveNewStruct* ptBattSaveNew);
BOOLEAN readBmsPackFacInfo( T_BmsPACKFactoryStruct *ptBmsPackFacInfo );
void writeBmsPackFacInfo( T_BmsPACKFactoryStruct *ptBmsPackFacInfo );  // 特别说明：调用此接口时请注意，如果修改了BMS序列号，密钥会变更，需对涉密数据重新加密后存储
BOOLEAN SaveUpgradefileInfo( BYTE ucMode );
BOOLEAN saveOneRecord(T_HisRecord* pRecord);
BOOLEAN readOneRecord(T_HisRecord* pRecord, record_north_protocol_e wNorthProtocolIndex);
BOOLEAN saveHisPointInfo(T_HisPoint* pHisPoint);
BOOLEAN readHisPointInfo(T_HisPoint* pHisPoint);
void readPackManufact(T_BmsPACKManufactStruct* ptBmsPACKManufact);
void DelAllRecords(T_HisRecord* pRecord);
size_t readFile(char *pcFilename, rt_uint8_t *pucData, size_t ulReadsize);
size_t writeFile(char *pcFilename, rt_uint8_t *pucData, size_t ulWritesize);
BOOLEAN readGPSLocation(T_GPSPositionStruct *ptGps);
void writeGPSLocation(T_GPSPositionStruct *ptGps);
BOOLEAN readGPSDirection(T_GPSDirectionStruct *ptGpsDirec);
void writeGPSDirection(T_GPSDirectionStruct *ptGpsDirec);
void deleteFile(const char *filename);
BOOLEAN saveOneBackupRecord(T_HisBackStruct* pRecordBackup, BYTE *pucBuff, rt_uint8_t index);
void saveToNewBackupFile(T_HisBackStruct* pRecord, rt_uint8_t index);
void CheckHisdataVersion(void);
void readHisBackupMagFile(char *pcFilename, T_HisBackMagStruct *ptMagFile, char *pcFileNum, T_HisBackStruct *ptHisBackup, char ucNum);
BOOLEAN readExtremeDataFile(T_HisRecord* pRecord);
BOOLEAN saveExtremeDataFile(T_HisRecord* pRecord);
BOOLEAN saveAnalyseNewFile(T_HisBackStruct* pRecordBackup, BYTE* pstr, WORD wLen);
BOOLEAN saveExtremeNewFile(T_HisBackStruct* pRecordBackup, BYTE* pstr, WORD wLen, BYTE ucPackNum);
void writeAlarmCheckFile(T_AlarmCheckStruct *ptAlarmCheck);
void readAlarmCheckFile(T_AlarmCheckStruct *ptAlarmCheck);

void writeBduRecordCounter(WORD *wBduRecordCnt);
void readBduRecordCounter(WORD *wBduRecordCnt);
BOOLEAN SaveDcrCnt(T_DcrCntStruct *ptDcrCnt);
BOOLEAN	ReadDcrCnt(T_DcrCntStruct* ptDcrCnt);

void initExtremeData(void);
void CheckHisExtremeVersion(void);
BOOLEAN DelDcrCnt(void);

BOOLEAN readBmsCustomerName(T_BmsCustomerNameStruct *ptBmsCustomerName);
BOOLEAN writeBmsCustomerName(T_BmsCustomerNameStruct *ptBmsCustomerName);
BOOLEAN DealUpdateProcess(void);
BOOLEAN ResetToInitUpdate(void);
WORD CRC_Cal_NOR(uint32_t addr, ULONG ulTotalFileLength);
int readFileManageInfo(T_FileManageStruct *tFileManage);
int writeFileManageInfo(T_FileManageStruct tFileManage);
BOOLEAN readBMSInfofact(T_BmsInfoStruct* ptBmsInfo);
BOOLEAN writeBMSInfofact(T_BmsInfoStruct* ptBmsInfo);
BOOLEAN read4GTraffic(T_4GTrafficTotail *pt4GTrafficTotail);
BOOLEAN write4GTraffic(T_4GTrafficTotail *pt4GTrafficTotail);
BOOLEAN SaveNetPeakShiftTemplate(const char *Filename,WORD offset,BYTE *ucBuff,WORD wSize); //保存来自网管侧的错峰模板

BOOLEAN writeSoftwareCustomizedInfo(T_SoftwareCustomizedInfo *ptCustomizedInfo);
BOOLEAN readSoftwareCustomizedInfo(T_SoftwareCustomizedInfo *ptCustomizedInfo);

rt_int32_t getWriteFsCnt(void);

#ifdef PAKISTAN_CMPAK_PROTOCOL
BOOLEAN writeCmpakInfo(T_CmpakSaveInfo *ptCmpakSaveInfo);
BOOLEAN readCmpakInfo(T_CmpakSaveInfo *ptCmpakSaveInfo);
#endif


BOOLEAN IsPathExist(const char *path);

#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // SOFTWARE_SRC_APP_FILESYS_H_
