
#include <stdlib.h>
#include <string.h>
#include "update_south_dev.h"
#include "msg.h"
#include "sps.h"
#include "cmd.h"
#include "protocol_layer.h"
#include "msg_id.h"
#include <rtthread.h>
#include <rtdevice.h>
#include "msg_id.h"
#include "pin_ctrl.h"
#include "pin_define.h"
#include "utils_data_transmission.h"
#include "storage.h"
#include "partition_def.h"
#include "softbus.h"
#include "sample.h"
#include "update_download_manage.h"
#include "utils_rtthread_security_func.h"
#include "south_file_upload.h"

static rt_timer_t g_south_update_timer = {0};
static unsigned short g_south_dev_type = 0;
static update_state_e trans_data_judge_unicast(update_manage_t* info, cmd_buf_t* cmd_buf);
static update_state_e trans_data_judge_broadcast(update_manage_t* info) ;
static void trans_trigger_pack(update_manage_t* info, cmd_buf_t* cmd_buf);
static void trans_trigger_parse(update_manage_t* info, cmd_buf_t* cmd_buf);
Static void trans_first_frm_pack(update_manage_t* info, cmd_buf_t *cmd_buf);
static void trans_data_frm_pack(update_manage_t* info, cmd_buf_t *cmd_buf);
static void update_trig_pack(update_manage_t* info, cmd_buf_t* cmd_buf);
static void update_trig_parse(update_manage_t* info, cmd_buf_t* cmd_buf);
static void trans_interrupt_pack(update_manage_t* info, cmd_buf_t* cmd_buf);
static void trans_interrupt_parse(update_manage_t* info, cmd_buf_t* cmd_buf);
Static void pack_filename(update_manage_t* info, cmd_buf_t* cmd_buf);
static void send_update_act_msg(unsigned char dev_addr, update_manage_t* manage_info);
static void trans_data_parse_broadcast(update_manage_t* info);
static short init_update_sta(update_manage_t* info, unsigned char dev_num);
static void set_addr_do(void);
static unsigned char get_next_addr(update_manage_t* info, update_state_e sta);
static update_state_e trans_interrupt_judge_broadcast(update_manage_t* info);
static update_state_e update_trigger_judge_broadcast(update_manage_t* info);
static unsigned char addr_do_state = PIN_HIGH;
static cmd_handle_register_t s_update_cmd_handle[] = {
    {DEV_BMU,  BMU_UPDATE_DATA_TRIG, CMD_TYPE_NO_POLL, update_cmd_parse, update_cmd_pack},
    {DEV_BMU,  BMU_UPDATE_DATA,      CMD_TYPE_NO_POLL, update_cmd_parse, update_cmd_pack},
    {DEV_BMU,  BMU_UPDATE_TRIG,      CMD_TYPE_NO_POLL, update_cmd_parse, update_cmd_pack},

    {DEV_BMU,  BMU_UPDATE_DATA_INTERRUPT,      CMD_TYPE_NO_POLL, update_cmd_parse, update_cmd_pack},
    {DEV_DC_AC,  DC_AC_UPDATE_DATA_TRIG, CMD_TYPE_NO_POLL, update_cmd_parse, update_cmd_pack},
    {DEV_DC_AC,  DC_AC_UPDATE_DATA,      CMD_TYPE_NO_POLL, update_cmd_parse, update_cmd_pack},
    {DEV_DC_AC,  DC_AC_UPDATE_TRIG,      CMD_TYPE_NO_POLL, update_cmd_parse, update_cmd_pack},
    {DEV_DC_AC,  DC_AC_UPDATE_DATA_INTERRUPT,      CMD_TYPE_NO_POLL, update_cmd_parse, update_cmd_pack},
    // 文件上传
    {DEV_DC_AC,  DC_AC_UPLOAD_FILE_TRIG,    CMD_TYPE_NO_POLL, upload_cmd_parse, upload_cmd_pack},
    {DEV_DC_AC,  DC_AC_UPLOAD_FILE_LIST,   CMD_TYPE_NO_POLL, upload_cmd_parse, upload_cmd_pack},
    {DEV_DC_AC,  DC_AC_UPLOAD_FILE_DATA,   CMD_TYPE_NO_POLL, upload_cmd_parse, upload_cmd_pack},
};

static update_manage_t s_update_info[] = {
    {DEV_BMU, MOD_BMU, },
    {DEV_DC_AC, MOD_DC_AC, },
    {DEV_DC_DC, MOD_DC_DC, },
};


static update_sta_map_t s_update_state_map[] = {
    {trans_trigger,  trans_trigger_judge,  trans_trigger_pack, trans_trigger_parse},
    {trans_data,     trans_data_judge,     trans_data_pack,    trans_data_parse},
    {trans_interrupt,  trans_interrupt_judge,  trans_interrupt_pack, trans_interrupt_parse},
    {update_trigger, update_trigger_judge, update_trig_pack,   update_trig_parse},
};

int update_cmd_pack(void* dev_inst, void *cmd_buf) {
    unsigned char i = 0;
    update_manage_t* info = NULL;
    dev_inst_t* update_dev = (dev_inst_t*) dev_inst;
    
    info = get_update_info(update_dev->dev_type->id);
    if (info == NULL)
        return FAILURE;
    
    for (i = 0; i < sizeof(s_update_state_map)/sizeof(s_update_state_map[0]); i++) {
        if (s_update_state_map[i].update_sta == info->cur_sta[update_dev->dev_addr - 1]) {
            s_update_state_map[i].state_pack_act(info, (cmd_buf_t*) cmd_buf);
            break;
        }   
    }
    return SUCCESSFUL;
}

// 处理update_cmd_parse返回值为FAILURE的第一种情况
static int update_cmd_parse_cmd_buf(dev_inst_t* update_dev, cmd_buf_t* cmd_buf_temp) {
    if(update_dev == NULL || cmd_buf_temp == NULL || cmd_buf_temp->cmd == NULL) {
        return FAILURE;
    }
    return SUCCESSFUL;
}
// 处理update_cmd_parse返回值为FAILURE的第二种情况
static int update_cmd_parse_cmd_head(bottom_comm_cmd_head_t* cmd_head, update_manage_t* info) {
    if(info == NULL || cmd_head == NULL || cmd_head->src_addr < 1 || cmd_head->src_addr > info->dev_num) {
        return FAILURE;
    }
    return SUCCESSFUL;
}

int update_cmd_parse(void* dev_inst, void *cmd_buf) {
    unsigned char i = 0;
    update_state_e next_sta;
    update_state_e last_sta;
    update_manage_t* info = NULL;
    bottom_comm_cmd_head_t* cmd_head = NULL;
    dev_inst_t* update_dev = (dev_inst_t*) dev_inst;
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*) cmd_buf;
    
    RETURN_VAL_IF_FAIL(SUCCESSFUL == update_cmd_parse_cmd_buf(update_dev, cmd_buf_temp), FAILURE);
    
    cmd_head = (bottom_comm_cmd_head_t*)(cmd_buf_temp->cmd->ack_head);
    info = get_update_info(update_dev->dev_type->id);
    
    RETURN_VAL_IF_FAIL(SUCCESSFUL == update_cmd_parse_cmd_head(cmd_head, info), FAILURE);

    for (i = 0; i < sizeof(s_update_state_map)/sizeof(s_update_state_map[0]); i++) {
        if (s_update_state_map[i].update_sta == info->cur_sta[cmd_head->src_addr - 1]) {
            if (s_update_state_map[i].state_parse_act == NULL)
                break;
            s_update_state_map[i].state_parse_act(info, cmd_buf_temp);
            next_sta = s_update_state_map[i].state_judge(info, cmd_buf_temp);
            if (next_sta != info->cur_sta[cmd_head->src_addr - 1]) {
                last_sta = info->cur_sta[cmd_head->src_addr - 1];
                info->fai_counts[cmd_head->src_addr - 1] = 0;
                info->suc_counts[cmd_head->src_addr - 1] = 0;
                info->cur_sta[cmd_head->src_addr - 1] = next_sta;
                if (info->is_broadcast)
                    update_next_addr(info, cmd_head->src_addr, last_sta);
            }
            send_update_act_msg(cmd_head->src_addr, info);
            break;
        }
    }
    return SUCCESSFUL;
}

static void send_update_act_msg(unsigned char dev_addr, update_manage_t* manage_info) {
    // module_msg_t msg_send;
    unsigned char msg_data[2] = {0};
    rt_timer_start(g_south_update_timer);
    if (!manage_info->is_broadcast && manage_info->cur_sta[dev_addr - 1] == update_exit)
        return;
    
    if (manage_info->is_broadcast && manage_info->next_addr == 0)
        return;
    
    // msg_send.msg_id = EXE_DEST_CMD_MSG;
    // msg_send.dest = manage_info->mod_id;
    // msg_send.data = msg_data;
    msg_data[1] = manage_info->is_broadcast ?  manage_info->next_addr:dev_addr;
    switch (manage_info->cur_sta[msg_data[1] - 1]) {
        case trans_trigger:
            msg_data[0] = BMU_UPDATE_DATA_TRIG;
            break;
            
        case trans_data:
            msg_data[0] = BMU_UPDATE_DATA;
            break;
            
        case trans_interrupt:
            msg_data[0] = BMU_UPDATE_DATA_INTERRUPT;
            break;
            
        // case update_trigger:
        //     msg_data[0] = BMU_UPDATE_TRIG;
        //     break;
         
        default:
            rt_timer_stop(g_south_update_timer);
            return;         
    }
    // send_msg(&msg_send); 
    if(msg_data[0] == BMU_UPDATE_DATA_TRIG)
    {
        send_msg_to_thread(STOP_PARA_CHECK, MOD_SYS_MANAGE, NULL, 0);
        send_msg_to_sps_cmd(STOP_DEV_POLL_MSG, MOD_DC_AC, 1, 0);//停止轮询命令
    }
    send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1,  msg_data[0]);
}


static void trans_trigger_pack(update_manage_t* info, cmd_buf_t* cmd_buf) {
    pack_filename(info, cmd_buf);
}

static void trans_trigger_parse(update_manage_t* info, cmd_buf_t* cmd_buf) {
    clear_cmd_count(info, cmd_buf);
}

void trans_data_pack(update_manage_t* info, cmd_buf_t* cmd_buf) {
    bottom_comm_cmd_head_t* cmd_head = (bottom_comm_cmd_head_t*)cmd_buf->cmd->req_head;
    unsigned short frm_no = 0;
    
    // 广播升级，修改数据传输命令为广播命令
    if (info->is_broadcast) {
        cmd_buf->cmd->cmd_type = cmd_buf->cmd->cmd_type | CMD_BROADCAST;
        frm_no = info->send_frm_no;
        cmd_head->append = info->send_frm_no;
    } else {
        frm_no = cmd_head->append;
        cmd_buf->cmd->cmd_type = cmd_buf->cmd->cmd_type & (~CMD_BROADCAST);
    }

    if (info->is_first_data) {
        cmd_head->append = 0;
        frm_no = 0;
        info->is_first_data = FALSE;
        if (info->is_broadcast)
            set_addr_do();
    } 

    if (frm_no == 0) 
        trans_first_frm_pack(info, cmd_buf);
    else 
        trans_data_frm_pack(info, cmd_buf);
}

Static void trans_first_frm_pack(update_manage_t* info, cmd_buf_t *cmd_buf) {
    unsigned short offset = 0;
    rt_memset_s(cmd_buf->buf, cmd_buf->buflen, 0, cmd_buf->buflen);

    put_int16_to_buff(&cmd_buf->buf[offset], info->file_manage.data_lenth_per_frame);
    offset += sizeof(info->file_manage.data_lenth_per_frame);
    put_int16_to_buff(&cmd_buf->buf[offset], info->file_manage.total_frame_num);
    offset += sizeof(info->file_manage.total_frame_num);
    put_int32_to_buff(&cmd_buf->buf[offset], info->file_manage.total_file_length);
    offset += 4;
    rt_snprintf_s((char*)&cmd_buf->buf[offset], sizeof(info->file_manage.file_name), "%s", info->file_manage.file_name);
    offset += sizeof(info->file_manage.file_name);
    rt_snprintf_s((char*)&cmd_buf->buf[offset], sizeof(info->file_manage.file_time), "%s", info->file_manage.file_time);
    offset += sizeof(info->file_manage.file_time);
    put_int16_to_buff(&cmd_buf->buf[offset], info->file_manage.file_check);
    offset += sizeof(info->file_manage.file_check);
    put_int16_to_buff(&cmd_buf->buf[offset], info->file_manage.resv);
    offset += sizeof(info->file_manage.resv);
    cmd_buf->data_len = offset;
}


static void trans_data_frm_pack(update_manage_t* info, cmd_buf_t *cmd_buf) {
    bottom_comm_cmd_head_t* cmd_head = (bottom_comm_cmd_head_t*)cmd_buf->cmd->req_head;
    unsigned short frm_no = cmd_head->append;
    unsigned int offset = 0;
    
    rt_memset_s(cmd_buf->buf, cmd_buf->buflen, 0, cmd_buf->buflen);
    cmd_buf->data_len = get_download_file_data_addr(&info->file_manage, frm_no, &offset);



    part_data_t part_data = {0};
    rt_memset_s(info->file_info, MSC_MAX_PACKET_2, 0, MSC_MAX_PACKET_2);
    part_data.buff = info->file_info;                 //buf已经是data域数据
    part_data.len = cmd_buf->data_len;
    part_data.offset = offset;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), UPDATE_DOWNLOAD_PART);//升级文件存储flash位置
    if (SUCCESSFUL != storage_process(&part_data, read_opr))
    {
        //rt_kprintf("trans_data_frm_pack|read file fail\n");
        return ;
    }
    rt_memcpy_s(cmd_buf->buf, cmd_buf->data_len, info->file_info, cmd_buf->data_len);
    return ;
}


void trans_data_parse(update_manage_t* info, cmd_buf_t* cmd_buf) {
    bottom_comm_cmd_head_t* ack_head = NULL;
    bottom_comm_cmd_head_t* req_head = NULL;
    
    if (info == NULL || cmd_buf == NULL
        || cmd_buf->cmd == NULL || cmd_buf->cmd->ack_head == NULL || cmd_buf->cmd->req_head == NULL)
        return;
    
    ack_head = (bottom_comm_cmd_head_t*)cmd_buf->cmd->ack_head;
    req_head = (bottom_comm_cmd_head_t*)cmd_buf->cmd->req_head;
    if (ack_head->src_addr < 1 || ack_head->src_addr > info->dev_num)
        return;
    
    if (!info->is_broadcast) {
       req_head->append = ack_head->append;
       clear_cmd_count(info, cmd_buf);
    } else {
        info->fai_counts[ack_head->src_addr - 1]++;
        info->suc_counts[ack_head->src_addr - 1] = 0;
    }

    if(req_head->append == info->file_manage.total_frame_num)
    {
        // 南向传输完成后将主动命令打开
        south_dev_update_end();
        return;
    }
}

static void update_trig_pack(update_manage_t* info, cmd_buf_t* cmd_buf) {
    pack_filename(info, cmd_buf);
}

static void update_trig_parse(update_manage_t* info, cmd_buf_t* cmd_buf) {
    clear_cmd_count(info, cmd_buf);
}

unsigned short get_download_file_data_addr(file_attr_t* file_info, unsigned short frm_no, unsigned int* offset) {
    *offset = (frm_no - 1)*file_info->data_lenth_per_frame;
    
    if (frm_no == (file_info->total_frame_num - 1)
       && file_info->data_lenth_per_frame * (frm_no - 1) != file_info->total_file_length) {
       return file_info->total_file_length - file_info->data_lenth_per_frame * (frm_no - 1);
    }
    
    return file_info->data_lenth_per_frame;
}






update_state_e trans_trigger_judge(update_manage_t* info, cmd_buf_t* cmd_buf) {
    update_state_e next_sta = trans_trigger;
    bottom_comm_cmd_head_t* ack_head = NULL;
    
    if (info == NULL || cmd_buf == NULL || cmd_buf->cmd == NULL)
        return update_exit;
    
    
    ack_head = (bottom_comm_cmd_head_t*)cmd_buf->cmd->ack_head;
    if (ack_head == NULL || ack_head->src_addr < 1 || ack_head->src_addr > info->dev_num)
        return update_exit;
    
    if (strcmp((char*)cmd_buf->buf, (char*)info->file_manage.file_name) != 0) {
        next_sta = update_exit;
        info->fail_ret[ack_head->src_addr - 1] = TRANS_TRIGGER_FAIL;
    } else if (info->fai_counts[ack_head->src_addr - 1] >= TRIGGER_FAILURE_TIMES) {
        next_sta = update_exit;
        info->fail_ret[ack_head->src_addr - 1] = TRANS_TRIGGER_FAIL;
    } else if (info->suc_counts[ack_head->src_addr - 1] >= TRIGGER_SUCCESS_TIMES) {
        next_sta = trans_data;
    }

    return  next_sta;
}

update_state_e trans_data_judge(update_manage_t* info, cmd_buf_t* cmd_buf) {
    if (!info->is_broadcast)
        return trans_data_judge_unicast(info, cmd_buf);
    else 
        info->next_addr = 0;
    return trans_data; 
}

static update_state_e trans_data_judge_unicast(update_manage_t* info, cmd_buf_t* cmd_buf) {
    bottom_comm_cmd_head_t* ack_head = NULL;

    if (cmd_buf == NULL || cmd_buf->cmd == NULL || info == NULL)
        return update_exit;
    
    ack_head = (bottom_comm_cmd_head_t*)cmd_buf->cmd->ack_head;
    if (ack_head == NULL || ack_head->src_addr < 1 || ack_head->src_addr > info->dev_num)
        return update_exit;
    
    if (info->fai_counts[ack_head->src_addr - 1] >= TRANS_FAILURE_TIMES) {
        info->fail_ret[ack_head->src_addr - 1] = TRANS_DATA_FAIL;
        return update_exit;
    }
        
    if (info->suc_counts[ack_head->src_addr - 1] != 0 &&
        ack_head->append == info->file_manage.total_frame_num)
        return update_trigger;
        
    return trans_data; 
}


update_state_e update_trigger_judge(update_manage_t* info, cmd_buf_t* cmd_buf) {
    bottom_comm_cmd_head_t* ack_head = NULL;
    
    
    if (info == NULL || cmd_buf == NULL || cmd_buf->cmd == NULL)
        return update_exit;
    
    ack_head = (bottom_comm_cmd_head_t*)cmd_buf->cmd->ack_head;
    if (ack_head == NULL || ack_head->src_addr < 1 || ack_head->src_addr > info->dev_num)
        return update_exit;
    
    if (info->suc_counts[ack_head->src_addr - 1] != 0) {
        return update_exit;
    }
    if (info->fai_counts[ack_head->src_addr - 1] >= TRIGGER_FAILURE_TIMES) {
        info->fail_ret[ack_head->src_addr - 1] = UPDATE_TRIGGER_FAIL;
        return update_exit;
    }

    return update_trigger; 
}


short start_south_update(file_attr_t* file_info, unsigned char dev_id, unsigned char dev_addr, unsigned char dev_num) {
    update_manage_t* info = NULL;
    unsigned char local_dev_num;
    const char (*south_update_file)[UPDATE_FILE_EXTEND_NAME_LEN];
    int max_south_dev_type = 0;

    if (file_info == NULL || dev_num < 1)
        return FAILURE;
    
    info = get_update_info(dev_id);
    south_update_file = get_south_update_file_names(&max_south_dev_type);
    if (info == NULL || south_update_file == NULL)
        return FAILURE;
    for(int i = 0; i < max_south_dev_type; i++)
    {
        if(strcmp(south_update_file[i], file_info->file_name) == 0)
        {
            g_south_dev_type = i;
        }
    }
    info->send_frm_no = 0;
    info->is_first_data = TRUE;
    info->is_broadcast = dev_addr == 0 ? TRUE:FALSE;
    rt_memcpy_s(&info->file_manage,sizeof(file_attr_t), file_info, sizeof(info->file_manage));
    
    if (info->is_broadcast) {
        local_dev_num = dev_num;
        info->next_addr = 1;
        rt_pin_irq_enable(ADDR_ASSSIGN_DO_PIN, PIN_IRQ_ENABLE);
    } else {
        info->next_addr = dev_addr;
        local_dev_num = 1;
    }

    if (init_update_sta(info, local_dev_num) != SUCCESSFUL)
        return FAILURE; 
    
    
    
    send_update_act_msg(info->next_addr, info);
    return SUCCESSFUL;
}

static short init_update_sta(update_manage_t* info, unsigned char dev_num) {
    if (dev_num <= info->dev_num) {
        goto STA_INIT;
    }
        

    info->dev_num = dev_num;
    if (info->suc_counts != NULL) {
        rt_free(info->suc_counts);
    }
    if (info->fai_counts != NULL) {
        rt_free(info->fai_counts);
    }
    if (info->cur_sta != NULL) {
        rt_free(info->cur_sta);
    }
    if (info->fail_ret != NULL) {
        rt_free(info->fail_ret);
    }
    if (info->his_fai_counts != NULL) {
        rt_free(info->his_fai_counts);
    }
    
    info->suc_counts = (unsigned short*)rt_malloc(sizeof(short)*info->dev_num);
    if (info->suc_counts == NULL)
        return FAILURE;
    
    info->fai_counts = (unsigned short*)rt_malloc(sizeof(short)*info->dev_num);
    if (info->fai_counts == NULL) {
        rt_free(info->suc_counts);
        return FAILURE;
    }

    info->cur_sta = (update_state_e *)rt_malloc(sizeof(update_state_e)*info->dev_num);
    if (info->cur_sta == NULL) {
        rt_free(info->suc_counts);
        rt_free(info->fai_counts);
        return FAILURE;
    }
    
    info->fail_ret = (unsigned short*)rt_malloc(sizeof(short)*info->dev_num);
    if (info->fail_ret == NULL) {
        rt_free(info->suc_counts);
        rt_free(info->fai_counts);
        rt_free(info->cur_sta);
        return FAILURE;
    }
    
    info->his_fai_counts = (unsigned short*)rt_malloc(sizeof(short)*info->dev_num);
    if (info->his_fai_counts == NULL) {
        rt_free(info->suc_counts);
        rt_free(info->fai_counts);
        rt_free(info->cur_sta);
        rt_free(info->fail_ret);
        return FAILURE;
    }
STA_INIT:
    rt_memset_s(info->fail_ret, sizeof(short) * (info->dev_num), 0x00, sizeof(short) * (info->dev_num)); 
    rt_memset_s(info->suc_counts, sizeof(short) * (info->dev_num), 0x00, sizeof(short) * (info->dev_num)); 
    rt_memset_s(info->fai_counts, sizeof(short) * (info->dev_num), 0x00, sizeof(short) * (info->dev_num));
    rt_memset_s(info->cur_sta, sizeof(update_state_e) * (info->dev_num), (unsigned short)trans_trigger, sizeof(update_state_e) * (info->dev_num));
    return SUCCESSFUL;
}
Static void pack_filename(update_manage_t* info, cmd_buf_t* cmd_buf) {
    cmd_buf->data_len = sizeof(info->file_manage.file_name);
    rt_memset_s(cmd_buf->buf, cmd_buf->buflen, 0x00, FILE_NAME_LEN);
    rt_memcpy_s(cmd_buf->buf, cmd_buf->buflen, info->file_manage.file_name, cmd_buf->data_len);
}

void clear_cmd_count(update_manage_t* info,  cmd_buf_t* cmd_buf) {
    bottom_comm_cmd_head_t* ack_head = NULL;
    
    if (info == NULL || cmd_buf == NULL
        || cmd_buf->cmd == NULL || cmd_buf->cmd->ack_head == NULL)
        return;
    
    ack_head = (bottom_comm_cmd_head_t*)cmd_buf->cmd->ack_head;
    if (ack_head->src_addr < 1 || ack_head->src_addr > info->dev_num)
        return;
    
    if (cmd_buf->rtn != 0) {
        info->fai_counts[ack_head->src_addr - 1]++;
        info->suc_counts[ack_head->src_addr - 1] = 0;
    } else {
        info->suc_counts[ack_head->src_addr - 1]++;
        info->fai_counts[ack_head->src_addr - 1] = 0;
    }
}

update_manage_t* get_update_info(unsigned char dev_id) {
    unsigned char i = 0;
    
    for(i = 0; i < sizeof(s_update_info)/sizeof(s_update_info[0]); i++) {
        if (dev_id == s_update_info[i].dev_id) 
            return &s_update_info[i];
    }

    return NULL;
}
void finish_south_dev_update()//升级结束后将主动命令打开（北向主动下发命令目前有：1：参数检查，2：轮询命令）
{
    // 南向传输失败写记录
    update_sta_msg_t sta_msg = {0};
    sta_msg.data_s = SOUTH_TRANS_FAILED;
    char info[MAX_INFO_LEN] = "south trans failed";
    rt_memcpy_s(sta_msg.info, MAX_INFO_LEN, info, MAX_INFO_LEN);
    sta_msg.south_dev = g_south_dev_type;

    rt_timer_stop(g_south_update_timer);
    send_msg_to_thread(UPDATE_STATUS, MOD_SYS_MANAGE, &sta_msg, sizeof(sta_msg)); // 写南向传输失败
    send_msg_to_thread(START_PARA_CHECK, MOD_SYS_MANAGE, NULL, 0);    //启动参数检查
    send_msg_to_sps_cmd(START_DEV_POLL_MSG, MOD_DC_AC, 1,  1);//启动轮训命令
}

void south_dev_update_end()
{
    // 南向传输成功写记录
    update_sta_msg_t sta_msg = {0};
    char info[MAX_INFO_LEN] = "south trans success";
    rt_memcpy_s(sta_msg.info, MAX_INFO_LEN, info, MAX_INFO_LEN);
    sta_msg.data_s = SOUTH_TRANS_SUCCESS;
    sta_msg.south_dev = INVALID_DEV_TYPE;

    rt_timer_stop(g_south_update_timer);
    send_msg_to_thread(UPDATE_STATUS, MOD_SYS_MANAGE, &sta_msg, sizeof(sta_msg)); // 写南向传输成功
}


void init_south_update(void) {
    init_south_upload();
    short i = 0;
    
    for(i = 0; i < sizeof(s_update_cmd_handle)/sizeof(s_update_cmd_handle[0]); i++) {
        register_cmd_handle(&s_update_cmd_handle[i]);
    }

    /* 初始化站址分配DO */
    rt_pin_mode(BCMU_ADDR_ASSSIGN_DI_PIN, PIN_MODE_INPUT_PULLUP);
    rt_pin_attach_irq(BCMU_ADDR_ASSSIGN_DI_PIN, PIN_IRQ_MODE_RISING_FALLING, register_addr_di_func , RT_NULL);
    //南向设备升级中设备超时30s后启动设备轮询定时器
    g_south_update_timer = rt_timer_create("south_update", finish_south_dev_update, NULL, 30*1000, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
    // if(g_south_update_timer == NULL)
    // {
    //     return;
    // }
    return;
}

update_state_e get_update_stat(unsigned char dev_id, unsigned short dev_addr) {
    update_manage_t* info = get_update_info(dev_id);

    if (info != NULL && dev_addr > 0 && dev_addr <= info->dev_num)
        return info->cur_sta[dev_addr - 1];
    
    return update_exit;
}

update_state_e trans_interrupt_judge(update_manage_t* info, cmd_buf_t* cmd_buf) {
    bottom_comm_cmd_head_t* ack_head = NULL;
    
    if (info == NULL || cmd_buf == NULL ||
        cmd_buf->cmd == NULL || cmd_buf->cmd->ack_head == NULL)
        return update_exit;
    
    ack_head = (bottom_comm_cmd_head_t*)cmd_buf->cmd->ack_head;
    if (ack_head->src_addr < 1 || ack_head->src_addr > info->dev_num)
        return update_exit;
    
    if (info->suc_counts[ack_head->src_addr - 1] != 0) {
        return update_exit;
    } else if (info->fai_counts[ack_head->src_addr - 1] >= INTERRUPT_FAILURE_TIMES) {
        return update_exit;
    }
        
    return trans_interrupt; 
}

static void trans_interrupt_pack(update_manage_t* info, cmd_buf_t* cmd_buf) {
    cmd_buf->buf[0] = 0;
    cmd_buf->data_len = 1;
}

static void trans_interrupt_parse(update_manage_t* info, cmd_buf_t* cmd_buf) {
    clear_cmd_count(info, cmd_buf);
}

void register_addr_di_func(void* arg) {
    update_manage_t* info = get_update_info(DEV_BMU);
    update_state_e next_sta = trans_data;
        
    if (info == NULL)
        return;

    trans_data_parse_broadcast(info);
    next_sta = trans_interrupt_judge_broadcast(info);  
    if (next_sta == trans_data) {
        trans_data_judge_broadcast(info);
        next_sta = update_trigger_judge_broadcast(info);
    }

    if (next_sta == trans_data)
        set_addr_do();
    
    send_update_act_msg(0, info);  
}

static void trans_data_parse_broadcast(update_manage_t* info) {
    unsigned char i = 0;
    
    // 本轮广播是否收到失败回应
    for (i = 0; i < info->dev_num; i++) {
        if (info->cur_sta[i] != trans_data)
            continue;
        
        if (info->fai_counts[i] != 0) {
            info->fai_counts[i] = 0;
            info->his_fai_counts[i]++;
        } else {
            info->his_fai_counts[i] = 0;
        }
    }
}

static update_state_e trans_interrupt_judge_broadcast(update_manage_t* info) {
    unsigned char i = 0;
    update_state_e next_sta = trans_data;
    
    for (i = 0; i < info->dev_num; i++) {
        if (info->cur_sta[i] != trans_data)
            continue;
        
        if (info->his_fai_counts[i] >= TRANS_FAILURE_TIMES) {
            info->his_fai_counts[i] = 0;
            info->cur_sta[i] = trans_interrupt;
            next_sta = trans_interrupt;
            info->next_addr = i + 1;
        }
    }
    
    return next_sta;
}

static update_state_e update_trigger_judge_broadcast(update_manage_t* info) {
    unsigned char i = info->dev_num;
    update_state_e next_sta = trans_data;
    
    if (info->send_frm_no < info->file_manage.total_frame_num)
        return next_sta;
    
    while (i > 0) {
        if (info->cur_sta[i - 1] == trans_data) {
            info->cur_sta[i - 1] = update_trigger;
            next_sta = update_trigger;
            info->next_addr = i;
        }
        i--;
    }

    return  next_sta;
}

static update_state_e trans_data_judge_broadcast(update_manage_t* info) {
    unsigned char i = 0;
    update_state_e next_sta = trans_data;
    
    for (i = 0; i < info->dev_num; i++) {
        if (info->cur_sta[i] != trans_data)
            continue;
        
        if (info->his_fai_counts[i] > 0) {  // 任意广播帧失败，需重发继续广播
            info->next_addr = i + 1;
            return next_sta;
        }
    }
    
    info->send_frm_no++;                 //  否则广播下一帧
    return  next_sta;
}


static void set_addr_do(void) {
    addr_do_state = !addr_do_state;
    set_pin(ADDR_ASSSIGN_DO_PIN, addr_do_state);    
}

static unsigned char get_next_addr(update_manage_t* info, update_state_e sta) {
    unsigned char i = 0;
    
    for (i = 0; i < info->dev_num; i++) {
        if (info->cur_sta[i] == sta)
            return i + 1;
    }
    return 0;
}

void update_next_addr(update_manage_t* info, int cur_addr, update_state_e last_sta) {
    
    switch (last_sta) {
        case trans_trigger:
            info->next_addr = cur_addr + 1;              // 传输触发下一台
            if (info->next_addr > info->dev_num) {       // 所有触发完成，开始IO接力广播数据传输
                info->next_addr = get_next_addr(info, trans_data);
                if (info->next_addr != 0) {
                    addr_do_state = PIN_HIGH;
                    set_pin(ADDR_ASSSIGN_DO_PIN, addr_do_state);
                }
            }
            break;
            
        case trans_interrupt:
            info->next_addr = get_next_addr(info, trans_interrupt);
            if (info->next_addr == 0)
                info->next_addr = get_next_addr(info, trans_data);
            break;
            
        case update_trigger:
            info->next_addr = get_next_addr(info, update_trigger);
            break;
        
        default:
            break;
    }
}
