#include "south_dev_common.h"
#include "dev_north_dxcb_modbus.h"
#include "utils_data_transmission.h"
#include "realdata_id_in.h"
#include "para_id_in.h"
#include "device_type.h"
#include "utils_rtthread_security_func.h"

static rt_sem_t s_rtn_sem;

__thread unsigned char s_south_dev_addr = 0;

/* 命令ID到寄存器信息映射表 */
static cmd_id_to_register_info_t cmd_id_to_register_info[] = {
    /* EBM设备相关命令 */
    {READ_EBM_HOLD_REG,       0x03, 0xd010, 24},   // 读取保持寄存器
    {READ_EBM_INPUT_REG,      0x04, 0xd010, 24},   // 读取输入寄存器
    {EBM_SET_WIND_SPEED_CTRL, 0x06, 0xd001, 1},    // 设置风速控制
    {EBM_SET_ADDR,            0x06, 0xd100, 1},    // 设置设备地址
    {EBM_SET_ACCE_TIME,       0x06, 0xd11f, 1},    // 设置加速时间
    {EBM_SET_DECE_TIME,       0x06, 0xd120, 1},    // 设置减速时间
    {EBM_SET_CODE1,           0x06, 0xd002, 1},    // 设置代码1（重复定义需确认）
    {EBM_SET_CODE2,           0x06, 0xd003, 1},    // 可能应为不同命令，如EBM_SET_CODE2
    {EBM_SET_CODE3,           0x06, 0xd004, 1},    // 同上，建议检查命令ID是否正确
    {EBM_SET_VALUE_SOURCE,    0x06, 0xd101, 1},    // 设置数值来源
    {EBM_SET_EMERGY_MODE,     0x06, 0xd15c, 1},    // 设置紧急模式
    {EBM_SET_RESET_EFFECT,    0x06, 0xd000, 1},    // 设置复位效果
    {EBM_SET_SAVE_SET_VALUE,  0x06, 0xd15d, 1},    // 保存设定值
    {EBM_SET_ALL_TIME,        0x10, 0xd11f, 2},    // 设置全部时间参数
    {EBM_GET_ALL_TIME,        0x03, 0xd11f, 2},    // 获取全部时间参数

    /* AVC设备相关命令 */
    {READ_AVC_INPUT_REG,      0x04, 0x3300, 12},   // 读取AVC输入寄存器
    {AVC_SET_FAN_SWITCH,      0x06, 0x3200, 1},    // 设置风扇开关
    {AVC_SET_SPEED_CTRL,      0x06, 0x3202, 1},    // 设置速度控制
    {AVC_GET_FAN_SWITCH,      0x03, 0x3200, 1},    // 获取风扇开关状态
    {AVC_GET_SPEED_CTRL,      0x03, 0x3202, 1},    // 获取速度控制状态

    /* 施乐百(SLB)设备相关命令 */
    {READ_SLB_INPUT_REG,      0x04, 0xA, 24},      // 读取输入寄存器（地址格式可能与其他设备不同）
    {SLB_SET_FAN_SWITCH,      0x06, 0x1, 1},       // 设置风扇开关
    {SLB_SET_SPEED_CTRL,      0x06, 0x2, 1},       // 设置速度控制
    {SLB_SET_SPEED_CTRL_MODE, 0x06, 0x4, 1},       // 设置速度控制模式
    {SLB_SET_COMM_FAIL_SPEED, 0x06, 0x5, 1},       // 设置通讯失败时的速度
    {SLB_SET_FAN_SPEED_MIN,   0x06, 0x7, 1},       // 设置最小风扇速度
    {SLB_SET_FAN_SPEED_MAX,   0x06, 0x8, 1},       // 设置最大风扇速度
    {SLB_SET_WATCH_DOG_CTRL,  0x06, 0x11, 1},      // 设置看门狗控制
    {SLB_SET_CHANGE_TIME,     0x06, 0x19, 1},      // 设置变更时间
    {SLB_GET_FAN_SWITCH,      0x03, 0x1, 1},       // 获取风扇开关状态（注意功能码是否正确）
    {SLB_GET_SPEED_CTRL,      0x03, 0x2, 1},       // 获取速度控制状态（注意功能码是否正确）
    {SLB_GET_SPEED_CTRL_MODE, 0x03, 0x4, 1},       // 获取速度控制模式
    {SLB_GET_COMM_FAIL_SPEED, 0x03, 0x5, 1},       // 获取通讯失败速度
    {SLB_GET_FAN_SPEED_MIN,   0x03, 0x7, 1},       // 获取最小风扇速度
    {SLB_GET_FAN_SPEED_MAX,   0x03, 0x8, 1},       // 获取最大风扇速度
    {SLB_GET_WATCH_DOG_CTRL,  0x03, 0x11, 1},      // 获取看门狗控制状态
    {SLB_GET_CHANGE_TIME,     0x03, 0x19, 1},      // 获取变更时间

    /* 泛仕达(FSD)设备相关命令 */
    {READ_FSD_HOLD_REG,       0x03, 0xd010, 18},   // 读取保持寄存器
    {READ_FSD_INPUT_REG,      0x04, 0xd010, 1},    // 读取输入寄存器
    {READ_FSD_INPUT_REG2,     0x04, 0xd011, 1},    // 读取输入寄存器2
    {READ_FSD_INPUT_REG3,     0x04, 0xd021, 1},    // 读取输入寄存器3
    {FSD_SET_WIND_SPEED_CTRL, 0x06, 0xd001, 1},    // 设置风速控制
    {FSD_SET_ADDR,            0x06, 0xd100, 1},    // 设置设备地址
    {FSD_SET_VALUE_SOURCE,    0x06, 0xd101, 1},    // 设定值来源
    {FSD_GET_VALUE_SOURCE,    0x03, 0xd101, 1},    // 获取值来源

    /* 丹弗斯(DFS)设备相关命令 */
    {READ_DFS_HOLD_REG ,     0x03, 0xed82, 8},     // 读取保持寄存器
    {READ_DFS_INPUT_REG,     0x04, 0xed82, 8},     // 读取保持寄存器
    {DFS_RUN_TIME_03,        0x03, 0x5dc, 1},      // 获取累计运行时间
    {DFS_RUN_TIME_04,        0x04, 0x5dc, 1},      // 获取累计运行时间
    {DFS_SET_RUN_STATUS_03,  0x03, 0x3e7f, 1},     // 获取运行状态（设定值）
    {DFS_SET_RUN_STATUS_04,  0x04, 0x3e7f, 1},     // 获取运行状态（设定值）
    {DFS_REAL_RUN_STATUS_03, 0x03, 0x3e9d, 1},     // 获取运行状态（实时值）
    {DFS_REAL_RUN_STATUS_04, 0x04, 0x3e9d, 1},     // 获取运行状态（实时值）
    {DFS_START_FRE_HOLD_TIME,0x03, 0x6ad, 1},      // 获取启动频率维持时间
    {DFS_START_FRE,          0x03, 0x6cb, 1},      // 获取启动频率
    {DFS_FIRST_ACCE_TIME,    0x03, 0xd51, 1},      // 获取第一加速时间
    {DFS_SET_STARTFRE_HOLDTIME,0x06, 0x6ad, 1},    // 设置启动频率维持时间
    {DFS_SET_START_FRE,      0x06, 0x6cb, 1},      // 设置启动频率
    {DFS_SET_FIRST_ACCE_TIME,0x06, 0xd51, 1},      // 设置第一加速时间
    {DFS_SET_FRE,            0x06, 0xc739, 1},     // 设置频率
    {DFS_SET_SWITCH,         0x06, 0xc738, 1},     // 设置开关机

    /* 艾默生(AMS)设备相关命令 */
    {READ_AMS_HOLD_REG,      0x03, 0x3c, 20},      // 读取ams保持寄存器
    {READ_AMS_HOLD_REG2,     0x03, 0x64, 6},       // 读取ams保持寄存器
    {AMS_HC_SET_SWITCH,         0x06, 0x64, 1},       // 开关机控制
    {AMS_HC_SET_TARGET_SPEED,   0x06, 0x65, 1},       // 设置目标转速
    {AMS_HC_SET_FAULT_CLEAN,    0x06, 0x67, 1},       // 故障清除

    /* 汇川(HC)设备相关命令 */
    {READ_HC_RUN_SPEED_REG,  0x03, 0x3C, 1},       // 读取hc运行转速
    {READ_HC_VFD_CURR_REG,   0x03, 0x44, 1},       // 读取hc变频板电流
    {READ_HC_VFD_TEMP_REG,   0x03, 0x46, 1},       // 读取hc变频板温度
    {READ_HC_HOLD_REG,       0x03, 0x4E, 3},       // 读取hc保持寄存器
    {HC_SET_FIRST_ACCE_TIME, 0x06, 0x6E, 1},       // 设置第一加速时间

    /* MOCK虚拟设备相关命令 */
    {READ_MOCK_HOLD_REG,  0x03, 0x00, 49},          // 读取mock数据

    {0}                                            // 结束标记
};
#define CMD_REG_NUM (sizeof(cmd_id_to_register_info)/sizeof(cmd_id_to_register_info_t))

/* 参数表 (寄存器地址必须从小到大排列) */
static modbus_addr_map_data_t g_modbus_data_map[] = {
    // 施乐百
    {1,  0, TYPE_INT16U, TYPE_INT16U,  0, 2, TYPE_ANA , DXCB_DATA_ID_FAN_CTRL_FAN_SWITCH},
    {2,  0, TYPE_INT16U, TYPE_INT16U,  0, 2, TYPE_ANA , DXCB_DATA_ID_FAN_WIND_SPEED_CTRL},
    {4,  0, TYPE_INT16U, TYPE_INT16U,  0, 2, TYPE_ANA , DXCB_DATA_ID_FAN_WIND_SPEED_CTRL_MODE},
    {5,  0, TYPE_INT16U, TYPE_INT16U,  0, 2, TYPE_ANA , DXCB_DATA_ID_FAN_COMM_FAIL_SPEED},
    {7,  0, TYPE_INT16U, TYPE_INT16U,  0, 2, TYPE_ANA , DXCB_DATA_ID_FAN_SPEED_MIN},
    {8,  0, TYPE_INT16U, TYPE_INT16U,  0, 2, TYPE_ANA , DXCB_DATA_ID_FAN_SPEED_MAX},
    {17, 0, TYPE_INT16U, TYPE_INT16U,  0, 2, TYPE_ANA , DXCB_DATA_ID_FAN_WATCH_DOG_CTRL},
    {25, 0, TYPE_INT16U, TYPE_INT16U,  0, 2, TYPE_ANA , DXCB_DATA_ID_FAN_CHANGE_TIME},

    // ams & hc
    {100, 0, TYPE_INT16U, TYPE_INT16U,  0, 2, TYPE_ANA , DXCB_DATA_ID_VFD_SWITCH},
    {101, 0, TYPE_INT16U, TYPE_INT16U,  0, 2, TYPE_ANA , DXCB_DATA_ID_VFD_TARGET_SPEED},
    {103, 0, TYPE_INT16U, TYPE_INT16U,  0, 2, TYPE_ANA , DXCB_DATA_ID_VFD_FAULT_CLEAN},

    // hc
    {110, 0, TYPE_FLOAT, TYPE_INT16U, 1, 2, TYPE_ANA , DXCB_DATA_ID_VFD_FIRST_ACCE_TIME},

    // dfs
    {1709, 0, TYPE_INT16U, TYPE_INT16U,  0, 2, TYPE_ANA , DXCB_DATA_ID_VFD_START_FRE_HOLD_TIME},
    {1739, 0, TYPE_INT16U, TYPE_INT16U,  0, 2, TYPE_ANA , DXCB_DATA_ID_VFD_START_FREQUENCY},
    {3409, 0, TYPE_FLOAT,  TYPE_INT16U,  2, 2, TYPE_ANA , DXCB_DATA_ID_VFD_FIRST_ACCE_TIME},

    // AVC
    {12800, 0, TYPE_INT16U, TYPE_INT16U,  0, 2, TYPE_ANA , DXCB_DATA_ID_FAN_CTRL_FAN_SWITCH},
    {12802, 0, TYPE_FLOAT, TYPE_INT16U,  1, 2, TYPE_ANA , DXCB_DATA_ID_FAN_WIND_SPEED_CTRL_AVC},

    // dfs
    {51000, 0, TYPE_INT16U, TYPE_INT16U,  0, 2, TYPE_ANA , DXCB_DATA_ID_VFD_SWITCH},
    {51001, 0, TYPE_INT16U, TYPE_INT16U,  0, 2, TYPE_ANA , DXCB_DATA_ID_VFD_FREQUENCY},

    // ebm
    {53248, 0, TYPE_INT16U, TYPE_INT16U,  0, 2, TYPE_ANA , DXCB_DATA_ID_FAN_RESET_DFFECT},
    {53249, 0, TYPE_INT16U, TYPE_INT16U,  0, 2, TYPE_ANA , DXCB_DATA_ID_FAN_WIND_SPEED_CTRL},  // 泛仕达
    {53250, 0, TYPE_INT16U, TYPE_INT16U,  0, 2, TYPE_ANA , DXCB_DATA_ID_FAN_CODE},
    {53251, 0, TYPE_INT16U, TYPE_INT16U,  0, 2, TYPE_ANA , DXCB_DATA_ID_FAN_CODE+1},
    {53252, 0, TYPE_INT16U, TYPE_INT16U,  0, 2, TYPE_ANA , DXCB_DATA_ID_FAN_CODE+2},
    {53504, 0, TYPE_INT16U, TYPE_INT16U,  0, 2, TYPE_ANA , DXCB_DATA_ID_FAN_ADDR},             // 泛仕达
    {53505, 0, TYPE_INT16U, TYPE_INT16U,  0, 2, TYPE_ANA , DXCB_DATA_ID_FAN_SET_VALUE_SOURCE},  // 泛仕达
    {53535, 0, TYPE_INT16U, TYPE_INT16U,  0, 2, TYPE_ANA , DXCB_DATA_ID_FAN_ACCE_TIME},
    {53536, 0, TYPE_INT16U, TYPE_INT16U,  0, 2, TYPE_ANA , DXCB_DATA_ID_FAN_DECE_TIME},
    {53596, 0, TYPE_INT16U, TYPE_INT16U,  0, 2, TYPE_ANA , DXCB_DATA_ID_FAN_EMERGY_MODE},
    {53597, 0, TYPE_INT16U, TYPE_INT16U,  0, 2, TYPE_ANA , DXCB_DATA_ID_FAN_SAVE_SET_VALUE},
};
#define DATA_NUM (sizeof(g_modbus_data_map)/sizeof(modbus_addr_map_data_t))

/**
 * @brief 根据cmd_id找到相关的寄存器信息
 * @param[in] cmd_id 输入命令id
 * @param[in] reg_table 输入的table
 * @param[in] table_len 输入的table的长度
 * @retval 成功返回索引，失败返回FAILURE
*/
int find_cmd_id_match_register_info(unsigned short cmd_id, cmd_id_to_register_info_t* reg_table,
                                    unsigned short table_len) {
    RETURN_VAL_IF_FAIL(reg_table != NULL, FAILURE);
    for (unsigned short i = 0; i < table_len; i++) {
        if (reg_table[i].cmd_id == cmd_id) {
            return i;
        }
    }
    return FAILURE;
}

/**
 * @brief 通用的打包寄存器相关信息的函数
 * @param[in] cmd_buff 命令数据缓冲区
 * @param[in] input_table 输入的寄存器起止地址，寄存器数量等数据段信息
 * @retval SUCCESSFUL 成功 FAILURE 失败
*/
int universal_pack_register_info(void* cmd_buff, cmd_id_to_register_info_t* input_table) {
    RETURN_VAL_IF_FAIL(input_table != NULL && cmd_buff != NULL, FAILURE);
    unsigned char* data = ((cmd_buf_t *)cmd_buff)->buf;
    unsigned short data_offset = 0;

    switch (input_table->func_code) {
        case WRITE_SINGLE_REG:
            put_int16_to_buff(data + data_offset, input_table->register_addr);
            data_offset += 2;
            break;
        case READ_HOLD_REG:
        case READ_INPUT_REG:
            put_int16_to_buff(data + data_offset, input_table->register_addr);
            data_offset += 2;
            put_int16_to_buff(data + data_offset, input_table->register_num);
            data_offset += 2;
            break;
        case WRITE_MULTI_REG:
            put_int16_to_buff(data + data_offset, input_table->register_addr);
            data_offset += 2;
            put_int16_to_buff(data + data_offset, input_table->register_num);
            data_offset += 2;
            data[data_offset] = input_table->register_num * 2;
            data_offset++;
            break;
        default:
            return FAILURE;
    }
    return data_offset;
}

int pack_read_modbus_data(void* dev_inst, void* cmd_buff) {
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    int index = find_cmd_id_match_register_info(((cmd_buf_t *)cmd_buff)->cmd->cmd_id, cmd_id_to_register_info, CMD_REG_NUM);
    RETURN_VAL_IF_FAIL(index != FAILURE, FAILURE);
    int data_offset = universal_pack_register_info(cmd_buff, &cmd_id_to_register_info[index]);
    RETURN_VAL_IF_FAIL(data_offset != FAILURE, FAILURE);
    ((cmd_buf_t *)cmd_buff)->data_len = data_offset;
    return SUCCESSFUL;
}

int find_register_start_addr_index_universal(int reg_addr, int table_size, modbus_addr_map_data_t* data_map) {
    RETURN_VAL_IF_FAIL(data_map != NULL, FAILURE);
    int ret = NO_MATCH_REGISTER_ADDR;
    int star_index = 0;
    int mid_index = 0;
    int end_index = table_size - 1;

    while (star_index <= end_index && star_index >= 0) {
        mid_index = (star_index + end_index) / 2;
        if (data_map[mid_index].register_addr == reg_addr) {
            ret = mid_index;
            break;
        }
        star_index = (data_map[mid_index].register_addr < reg_addr) ? (mid_index + 1) : star_index;
        end_index = (data_map[mid_index].register_addr < reg_addr) ? end_index : (mid_index - 1);
    }
    return ret;
}

int universal_pack_set_para(void* cmd_buff, int reg_addr, int offset) {
    int index = find_register_start_addr_index_universal(reg_addr, DATA_NUM, g_modbus_data_map);
    RETURN_VAL_IF_FAIL(index != NO_MATCH_REGISTER_ADDR && cmd_buff != NULL, FAILURE);
    int data_len = new_pack_data_to_buff(cmd_buff, &index, offset, g_modbus_data_map, s_south_dev_addr - 1);
    ((cmd_buf_t *)cmd_buff)->data_len = data_len + offset;
    return SUCCESSFUL;
}

int pack_write_modbus_data(void* dev_inst, void* cmd_buff) {
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    s_south_dev_addr = ((dev_inst_t*)dev_inst)->dev_addr;
    modbusrtn_var_t* modbusrtn_var = get_modbusrtn_var();
    int index = find_cmd_id_match_register_info(((cmd_buf_t *)cmd_buff)->cmd->cmd_id, cmd_id_to_register_info, CMD_REG_NUM);
    RETURN_VAL_IF_FAIL(index != FAILURE, FAILURE);
    int data_offset = universal_pack_register_info(cmd_buff, &cmd_id_to_register_info[index]);
    RETURN_VAL_IF_FAIL(data_offset != FAILURE, FAILURE);

    modbusrtn_var->modbus_reg_addr = cmd_id_to_register_info[index].register_addr;
    modbusrtn_var->modbus_reg_nums = cmd_id_to_register_info[index].register_num;
    RETURN_VAL_IF_FAIL(universal_pack_set_para(cmd_buff, cmd_id_to_register_info[index].register_addr, data_offset) == SUCCESSFUL, FAILURE);
    return SUCCESSFUL;
}

int parse_comm_data(void* dev_inst, void* cmd_buff)
{
    rt_sem_t rtn_sem = get_rtn_sem();
    rt_sem_release(rtn_sem);
    rt_kprintf("parse_comm_data|south recv\n");
    return SUCCESSFUL;
}



dev_type_t* init_fan_south()
{
    static dev_type_t dev_south = {
        DEV_SOUTH_MODBUS, MAX_FAN_NUM, PROTOCOL_MODBUS_RTU, LINK_SOUTH_DXCB1, R_BUFF_LEN, S_BUFF_LEN, 0, NULL, NULL, 0, NULL, NULL, CMD_DXCB_TIMEOUT, CMD_DXCB_FAIL_MAX,
    };
    return &dev_south;
}

dev_type_t* init_vfd_south()
{
    static dev_type_t dev_south = {
        DEV_SOUTH2_MODBUS, MAX_VFD_NUM+1, PROTOCOL_MODBUS_RTU, LINK_SOUTH_DXCB2, R_BUFF_LEN, S_BUFF_LEN, 0, NULL, NULL, 0, NULL, NULL, CMD_DXCB_TIMEOUT, CMD_DXCB_FAIL_MAX,
    };
    return &dev_south;
}

int handle_mock_south(dev_inst_t* dev_inst, cmd_t* poll_cmd_tab) {
    dev_type_t* mock_dev_type = NULL;
    static dev_type_t s_mock_dev_type = {0};
    mock_dev_type = init_vfd_south();
    rt_memcpy_s(&s_mock_dev_type, sizeof(dev_type_t), mock_dev_type, sizeof(dev_type_t));
    s_mock_dev_type.poll_cmd_tab = poll_cmd_tab;
    dev_inst->dev_type = &s_mock_dev_type;
    return SUCCESSFUL;
}

// 根据不同品牌改变注册的设备类型信息
int change_dev_type_info_by_diff_brand(dev_inst_t* dev_inst, char south_dev_type, char is_inter_fan, char dev_num, cmd_t* no_poll_cmd_tab, cmd_t* poll_cmd_tab)
{
    dev_type_t* dev_type = NULL;
    dev_type_t* fan_dev_type = NULL;
    static dev_type_t s_fan_dev_type = {0};
        
    if(south_dev_type == SOUTH_FAN_TYPE)
    {
        if(is_inter_fan == TRUE)
        {
            dev_type = init_fan_south();
        }
        else
        {
            fan_dev_type = init_fan_south();
            rt_memcpy_s(&s_fan_dev_type, sizeof(dev_type_t), fan_dev_type, sizeof(dev_type_t));
            dev_type = &s_fan_dev_type;
        }
    }
    else
    {
        dev_type = init_vfd_south();
    }
    dev_type->dev_num = dev_num;
    dev_type->no_poll_tab = no_poll_cmd_tab;
    dev_type->poll_cmd_tab = poll_cmd_tab;
    dev_inst->dev_type = dev_type;
    rt_kprintf("south_dev_type:%d, is_inter_fan:%d, dev_type:%p, dev_inst:%p, no_poll_cmd_tab:%p, poll_cmd_tab:%p\n",
        south_dev_type, is_inter_fan, dev_type, dev_inst, no_poll_cmd_tab, poll_cmd_tab);
    return SUCCESSFUL;
}

void init_rtn_sem()
{
    s_rtn_sem = rt_sem_create("rtn_sem", 0, RT_IPC_FLAG_FIFO);
}

rt_sem_t get_rtn_sem()
{
    return s_rtn_sem;
}


