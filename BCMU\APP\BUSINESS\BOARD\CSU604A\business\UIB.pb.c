/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.3.8 at Wed May 24 09:37:42 2017. */

#include "UIB.pb.h"

/* @@protoc_insertion_point(includes) */
#if PB_PROTO_HEADER_VERSION != 30
#error Regenerate this file with the current version of nanopb generator.
#endif



const pb_field_t realdata_AI_response_fields[2] = {
    PB_FIELD(  1, INT32   , REPEATED, STATIC  , FIRST, realdata_AI_response, AI, AI, 0),
    PB_LAST_FIELD
};

const pb_field_t realdata_DI_response_fields[2] = {
    PB_FIELD(  1, INT32   , REPEATED, STATIC  , FIRST, realdata_DI_response, DI, DI, 0),
    PB_LAST_FIELD
};

const pb_field_t facinfo_response_fields[4] = {
    PB_FIELD(  1, STRING  , REQUIRED, STATIC  , FIRST, facinfo_response, soft_name, soft_name, 0),
    PB_FIELD(  2, STRING  , <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>  , OTHER, facinfo_response, soft_version, soft_name, 0),
    PB_FIELD(  3, ME<PERSON>AGE , REQUIRED, STATIC  , OTHER, facinfo_response, date, soft_version, &facinfo_response_release_date_fields),
    PB_LAST_FIELD
};

const pb_field_t facinfo_response_release_date_fields[4] = {
    PB_FIELD(  1, INT32   , REQUIRED, STATIC  , FIRST, facinfo_response_release_date, year, year, 0),
    PB_FIELD(  2, INT32   , REQUIRED, STATIC  , OTHER, facinfo_response_release_date, month, year, 0),
    PB_FIELD(  3, INT32   , REQUIRED, STATIC  , OTHER, facinfo_response_release_date, day, month, 0),
    PB_LAST_FIELD
};

const pb_field_t setDO_request_fields[2] = {
    PB_FIELD(  1, INT32   , REPEATED, STATIC  , FIRST, setDO_request, DO, DO, 0),
    PB_LAST_FIELD
};


/* Check that field information fits in pb_field_t */
#if !defined(PB_FIELD_32BIT)
/* If you get an error here, it means that you need to define PB_FIELD_32BIT
 * compile-time option. You can do that in pb.h or on compiler command line.
 * 
 * The reason you need to do this is that some of your messages contain tag
 * numbers or field sizes that are larger than what can fit in 8 or 16 bit
 * field descriptors.
 */
PB_STATIC_ASSERT((pb_membersize(facinfo_response, date) < 65536), YOU_MUST_DEFINE_PB_FIELD_32BIT_FOR_MESSAGES_realdata_AI_response_realdata_DI_response_facinfo_response_facinfo_response_release_date_setDO_request)
#endif

#if !defined(PB_FIELD_16BIT) && !defined(PB_FIELD_32BIT)
/* If you get an error here, it means that you need to define PB_FIELD_16BIT
 * compile-time option. You can do that in pb.h or on compiler command line.
 * 
 * The reason you need to do this is that some of your messages contain tag
 * numbers or field sizes that are larger than what can fit in the default
 * 8 bit descriptors.
 */
PB_STATIC_ASSERT((pb_membersize(facinfo_response, date) < 256), YOU_MUST_DEFINE_PB_FIELD_16BIT_FOR_MESSAGES_realdata_AI_response_realdata_DI_response_facinfo_response_facinfo_response_release_date_setDO_request)
#endif


/* @@protoc_insertion_point(eof) */
