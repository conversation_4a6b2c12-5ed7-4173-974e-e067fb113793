/******************************************************************************
* 版权所�?(C)2017, 中兴通讯股份有限公司
* 当前版本   : 1.0
* 作者: 周彬
* 生成日期   : 2019-01-03
* 功能描述   : 透传升级
* 修改历史   : added by <PERSON><PERSON><PERSON><PERSON> on 2022-12-01
******************************************************************************/

#define LOG_TAG_RDL "rdl"
#define LOG_LVL_RDL LOG_LVL_WARNING

#undef  LOG_TAG
#undef  LOG_LVL
#define LOG_TAG     LOG_TAG_RDL
#define LOG_LVL     LOG_LVL_RDL

#include <ulog.h>
#include "update_handle.h"
#include <rtthread.h>
#include <stdio.h>
#include <string.h>
#include "flash.h"
#include "spi_flash.h"
#include "sfud_def.h"
#include "sfud.h"
#include "CommCan.h"
#include "prtclDL.h"
#include "utils_rtthread_security_func.h"
#include "fileSys.h"
#include "peakshift.h"
#include "hisdata.h"
#include <unistd.h>
#include <sys/stat.h>
#include <sys/fcntl.h>

#ifdef DEVICE_USING_R321
#include "download_tcp.h"
#endif

typedef struct RemoteDownloadProtocolContext
{
    char filename[40];
    uint32_t filesize;
    int32_t lastReadLen;
    uint16_t totalFrameNum;
} T_RdlContext;

extern rt_mutex_t s_ptMutexFile;
Static BOOLEAN s_bIsExtendTrigFrame = FALSE; // 是否为扩展触发帧，TRUE：扩展触发帧；FALSE：普通触发帧
static int flagUpdateInited = FALSE;
Static BOOLEAN s_bFileTransComplete = FALSE;
Static BOOLEAN s_bPeakFileTransComplete;   //错峰文件传输完成标志位
const BYTE ucFileNameBin[FILE_NAME_LEN] = "bms_mcu_app230321.bin"; //自身升级和主机给从机升级实际需要的文件名
const char ucCheckFileNameBin[FILE_NAME_LEN] = "BMS_MCU_CK_APP230321.bin"; //含有校验头升级包文件名
const BYTE ucFileBDUNameBin[FILE_NAME_LEN] = "ZXDUPAV5.0BDCUR321230512.BIN"; //自身升级和主机给从机升级实际需要的文件名

static BOOLEAN s_bFisrtEarseFlash = False;  //发送文件第一帧时，需要擦除一个4k,应对文件重复发送的情况
static T_TemplateInfo s_tPeakshiftTemplate;  //缓存错峰模板
static T_PeakShiftPara	s_tPeakShiftPara;    //缓存错峰参数

/*************文件下载相关全局变量*********/
Static BYTE ucFileType = ILLEGAL_FLIE;
Static WORD peak_crc16 = 0x0000;              // 校验和
static BYTE ucFileStatus[ILLEGAL_FLIE] = {0};                        //文件状态
static BYTE ucCheckResult[ILLEGAL_FLIE] = {0};                      //文件传输完成后的校验结果
static void PeakShiftFileDownload(T_UpdateDataStruct *pData_info);  //错峰文件下载处理
static BOOLEAN PeakShiftFileOver(T_UpdateDataStruct *pData_info);   //错峰文件接受完毕处理
static BOOLEAN ParseTemplateData(T_DateTemplateStruct* ptDateTemp, BYTE* p, BYTE ucNum); //解析错峰模板时间段
/***************************************/

/*************升级相关全局变量************/
Static UINT32 crc_ctx = 0 ^ 0xFFFFFFFF;              // 校验和
static BOOLEAN flagExtern = FALSE;


Static void UpdatefileSave(T_UpdateDataStruct *pData_info);          //升级文件下载处理
Static BOOLEAN UpdateBMSDownlaodOver(T_UpdateDataStruct *pData_info);   //升级文件接受完毕处理
Static BOOLEAN UpdateBDUDownlaodOver(T_UpdateDataStruct *pData_info);   //升级文件接受完毕处理


/************上传文件相关函数************/
Static void ULFileProcess(T_UpdateFrameStruct *pData);      //上传文件处理
Static void ULFileListProcess(T_UpdateDataStruct *pData);   //上传文件列表处理
Static void ULFileDataProcess(T_UpdateDataStruct *pData);   //上传文件数据处理

Static void data_handle(T_UpdateDataStruct *data_info);
Static void check_state(void);

/* 命令处理函数 */
Static void cmd_trigFrme_func(T_UpdateFrameStruct *pData);
static void cmd_download_func(T_UpdateFrameStruct *pData);
Static void cmd_upload_func(T_UpdateFrameStruct *pData);     //文件上传入口函数
Static void cmd_GetFlag_func(T_UpdateFrameStruct *pData);    //获取唯一标示,by hxk 2023-02-03
static void GetMacAddr(BYTE *buff,BYTE Length);  //获得单板的MAC地址 by hxk 2023-02-06
Static void GetBatchUpdateProgress(T_UpdateFrameStruct *pData); //获取批量升级进度
static sfud_err write_to_norflash(const sfud_flash *flash, uint32_t addr, 
                          size_t size, const uint8_t *data,uint32_t earse_addr); //写入到norflash
static BOOLEAN first_frame_handle(T_UpdateDataStruct *pData_info);
Static BOOLEAN first_extern_frame_handle(T_UpdateDataStruct *pData_info);
Static int getOneframe(T_CommRecCache *pCache);
static BOOLEAN FirstFrameProcess(T_UpdateDataStruct *pData_info ,BOOLEAN *flagExtern);//解决圈复杂度
Static void write_download_tmpInfo(file_info_t *pfile_info, BYTE ucMode);
Static int file_change_check(file_info_t *pold_file, file_info_t *pnew_file);
Static BOOLEAN break_file_change_check(void);
Static BOOLEAN accumulated_calc_crc(int calc_len, int data_len, uint32_t *crc_ctx, uint8_t *file_data);

Static void trigTimer_timeout(void* parameter);
Static void dataTimer_timeout(void* parameter);
Static int read_download_tmpInfo(file_info_t *pfile_info);
Static void reset_download_manager(void);


static void InitRdlContext(T_RdlContext *ctx);
static int RdlTransferFile(T_RdlContext *ctx, T_UpdateDataStruct *pData);

T_CommRecCache s_tUpdateCommRecCache;               // 链路层数据接收缓冲区，一整帧数据
T_PRTCL_REC_BUFF s_tUpdateCommSendCache;            // 链路层数据发送缓冲区
//T_PRTCL_REC_BUFF s_tParsedOneFrame;                 // 解析后的一帧数据
T_PRTCL_REC_BUFF s_tSendFrame;                      // 待回包的帧数据
//unsigned char decodeBuf[RAW_BUFF_LEN] = {0};        // 解码后的一帧数据
Static file_info_t file_info;                       // 首发帧文件信息

Static T_UpdateFrameStruct s_tUpdateFramePrase;     // 解码并解析后的完整帧数据
//Static T_UpdateFrameStruct s_tUpdateFramePack;      // 回帧数据
Static T_UpdateFrameStruct s_tUpdateFramePackTmp;   // 链路层数据解码到协议层
Static BOOLEAN bBreakFlag = False;  // 断点续传标志
Static int break_write_pos = 0;     // 断点时写入位置
Static int break_frame_cnt = 0;     // 断点时写入帧数

rt_mutex_t s_mSendQueMutex = RT_NULL;
rt_mutex_t s_mRecvQueMutex = RT_NULL;
file_head_t app_head;
extern_file_head_t extern_app_head;
WORD update_file_crc = 0;

/*
 * 触发帧应答内容 - 不含帧头帧尾
 */
// static const unsigned char trig_frame_ack[] = {
//     0x41, 0x41, 0x32, 0x34, 0x30, 0x31, 0x34, 0x30, 0x45,
//     0x45, 0x35, 0x36, 0x35, 0x42, 0x41, 0x36, 0x41, 0x42
// };
static const unsigned char trig_frame_ack[] = {
    0x41,0x41,0x32,0x34,0x30,0x31,0x34,0x30,0x45,
    0x45,0x35,0x36,0x35,0x42,0x41,0x36,0x41,0x42
};
// 扩展触发帧回包（不含帧头帧尾）
static const unsigned char ext_trig_frame_ack[] = {
    0x30,0x41,0x30,0x30,0x41,0x41,0x32,0x37,0x30,
    0x31,0x34,0x30,0x45,0x45,0x35,0x36,0x35,0x42,
    0x41,0x36,0x41,0x42
};


Static differnet_file_management s_tfileDownload[] =
{
    {
        .ucFileType = UPDATE_BMS_FILE,
        .file_name = "BMS_MCU_CK_APP230321.bin",
        .file_download_process = UpdatefileSave,
        .file_received_process = UpdateBMSDownlaodOver,
    },
    {
        .ucFileType = UPDATE_BDU_FILE,
        .file_name = "BDU_MCU_CK_APP230321.bin",
        .file_download_process = UpdatefileSave,
        .file_received_process = UpdateBDUDownlaodOver,
    },
    {
        .ucFileType = PEAK_SHIFT_FILE,
        .file_name = "PEAKSHIFTTEMPLATE.bin",
        .file_download_process = PeakShiftFileDownload,
        .file_received_process = PeakShiftFileOver,
    },
};


/*
 * 下载管理
 */
Static download_handle_t s_downloadHandler =
{
    .handle_data = data_handle,
    .state_check = check_state,
};

/*
 * 命令执行(回帧)
 */
static T_UpdateHandleStruct s_tDownLoadCMD[] = {
    {
        .ucCmd = DLCMD_TRIG_FRAME,
        .pFunc = cmd_trigFrme_func,
    },
    {
        .ucCmd = DLCMD_EXT_TRIG_FRAME,
        .pFunc = cmd_trigFrme_func,
    },
    {
        .ucCmd = DLCMD_GET_MAC_ADDR_PROC,
        .pFunc = cmd_GetFlag_func,
    },
    {
        .ucCmd = DLCMD_DOWNLOAD,
        .pFunc = cmd_download_func,
    },
    {
        .ucCmd = DLCMD_DOWNLOAD_EXT,
        .pFunc = cmd_download_func,
    },
    {
        .ucCmd = DLCMD_GET_BATCH_UPDATE_PROC,
        .pFunc = GetBatchUpdateProgress,
    },
    {
        .ucCmd = ULCMD_GET_FILE_LIST,
        .pFunc = cmd_upload_func,
    },
    {
        .pFunc = NULL,
    },
};


/*
 * @brief 获取批量升级进度标志
 * @param[in] pData 帧数据
 * @retval
 * @note  注解
 * @par 修改日志
 *      2023-02-03, added by hxk
 */
Static void GetBatchUpdateProgress(T_UpdateFrameStruct *pData)
{
    BYTE ucBuff[65] = {0};

    pData->dataFrame.ucFlag = 0x41;
    pData->dataFrame.usAddr = 0x00;
    pData->dataFrame.usLen = 0x41; // DATA域的字节数为1+32*2=65
    pData->dataFrame.usFN1 = 0x00;
    pData->dataFrame.usFN2 = 0x00;
    pData->dataFrame.ucDev = 0x04;
    pData->dataFrame.ucCMD = 0x1A;
    pData->dataFrame.ucRtn = 0x00;

    CalBatchUpdateProgress(ucBuff);

    rt_memcpy_s(pData->dataFrame.aucBuff, sizeof(pData->dataFrame.aucBuff), ucBuff, sizeof(ucBuff));

    // rt_mutex_take(s_mSendQueMutex, RT_WAITING_FOREVER);
    // rt_memcpy_s(&s_tUpdateFramePack, sizeof(s_tUpdateFramePack), pData, sizeof(s_tUpdateFramePack));
    // rt_mutex_release(s_mSendQueMutex);
}


/*
 * 读取升级文件信息
 */
Static int read_download_tmpInfo(file_info_t *pfile_info) {
    T_FileManageStruct tFileManage = {0};  // flash struct
    rt_spi_flash_device_t dev_w25q;
    #ifndef KW_CHECK
    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);
    rt_memset(&tFileManage, 0x00, sizeof(tFileManage));
    dev_w25q = (rt_spi_flash_device_t)rt_device_find("GD25Q128");
    if (dev_w25q == RT_NULL)
    {
        return FAILURE;
    }
    sfud_read((sfud_flash *)dev_w25q->user_data, 0, sizeof(T_FileManageStruct), (BYTE *)&tFileManage );
    pfile_info->total_frames = tFileManage.tFileAttr.wTotalFrameNum;
    pfile_info->total_leng = tFileManage.tFileAttr.ulTotalFileLength;
    pfile_info->frame_cnt = tFileManage.wCounter;
    rt_memcpy(pfile_info->fileName, tFileManage.tFileAttr.acFileName, FILE_NAME_LEN);
    rt_memcpy(pfile_info->fileTime, tFileManage.tFileAttr.acFileTime, FILE_TIME_LEN);
    rt_mutex_release(s_ptMutexFile);
    #endif
    return SUCCESSFUL;
}

/*
 * 写入升级文件信息
 */
Static void write_download_tmpInfo(file_info_t *pfile_info, BYTE ucMode) {
    T_FileManageStruct tFileManage = {0};  // flash struct
    rt_spi_flash_device_t dev_w25q;

    if (NULL == pfile_info)
        return;
    #ifndef KW_CHECK
    dev_w25q = (rt_spi_flash_device_t)rt_device_find("GD25Q128");
    if (dev_w25q == RT_NULL)
    {
        return;
    }
    #endif
    if((ucMode == FLAG_NOR_IAP) || (ucMode == FLAG_NOR_TOOL_IAP))
    {
        pfile_info->total_frames = (pfile_info->total_leng + MSC_MAX_PACKET - 1) / MSC_MAX_PACKET + 1;
    }
    tFileManage.tFileAttr.wDataLenPerFrame = MSC_MAX_PACKET;
    tFileManage.tFileAttr.wTotalFrameNum = pfile_info->total_frames;
    tFileManage.tFileAttr.ulTotalFileLength = pfile_info->total_leng;
    tFileManage.wCounter = pfile_info->frame_cnt;                       // 请求帧号
    tFileManage.tFileAttr.wFileCheck = update_file_crc;
    #ifndef KW_CHECK
    rt_memcpy(tFileManage.tFileAttr.acFileName, pfile_info->fileName, FILE_NAME_LEN);
    rt_memcpy(tFileManage.tFileAttr.acFileTime, pfile_info->fileTime, FILE_TIME_LEN);
    #endif
    tFileManage.ucUpdateAddr = GetBMSAddr();
    tFileManage.ucFlag = ucMode;  // FLAG_BACKUP 运行正常待备份，0x57 待从NorFlash升级
    tFileManage.wCrc = CRC_Cal((BYTE *)&tFileManage, sizeof(T_FileManageStruct)-2);
    #ifndef KW_CHECK
    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);
    sfud_erase_write((sfud_flash *)dev_w25q->user_data, 0, sizeof(T_FileManageStruct), (uint8_t *)&tFileManage);
    rt_mutex_release(s_ptMutexFile);
    #endif
}

Static void reset_download_manager(void) {
    write_download_tmpInfo(&s_downloadHandler.download_mgr.file_info, FLAG_BACKUP);
    s_downloadHandler.download_mgr.link_used = FALSE;
}

Static int file_change_check(file_info_t *pold_file, file_info_t *pnew_file) {
    if ((rt_strcmp(pold_file->fileName, pnew_file->fileName) == 0) && \
        (rt_strcmp(pold_file->fileTime, pnew_file->fileTime) == 0) && \
        (pold_file->total_frames == pnew_file->total_frames) && \
        (pold_file->total_leng   == pnew_file->total_leng)) {
        return FALSE;
    } else {
        return TRUE;
    }
}

static int set_download_manager(file_info_t *pfile_info) {
    s_downloadHandler.download_mgr.link_used = TRUE;
    return SUCCESSFUL;
}

static void set_update_file(file_info_t *pfile_info)
{
    s_downloadHandler.update_file.valid = TRUE;
    #ifndef KW_CHECK
    rt_memcpy(s_downloadHandler.update_file.aucFileName, pfile_info->fileName, sizeof(pfile_info->fileName));
    #endif
}

Static BOOLEAN break_file_change_check(void)
{
    // 文件有变化，重新保存升级文件
    if (TRUE == file_change_check(&s_downloadHandler.download_mgr.file_info, &file_info)) {
        write_download_tmpInfo(&s_downloadHandler.download_mgr.file_info, FLAG_BACKUP);
        return False;
    }

    // 是同一个升级文件
    else
    {
        // 已存在的升级文件是完整的
        if (file_info.total_frames == file_info.frame_cnt + 1) {
            file_info.frame_cnt = 0;
            file_info.write_pos = 0;
            s_downloadHandler.download_mgr.file_info.frame_cnt = 0;
            s_downloadHandler.download_mgr.file_info.write_pos = 0;
            write_download_tmpInfo(&s_downloadHandler.download_mgr.file_info, FLAG_BACKUP);
        }

        // 已存在的升级文件不完整（断点续传）
        else
        {
            file_info.frame_cnt = break_frame_cnt;
            file_info.write_pos = break_write_pos;
            bBreakFlag = True;
        }
        rt_memcpy_s(&s_downloadHandler.download_mgr.file_info, sizeof(file_info_t), &file_info, sizeof(file_info_t));
        return True;
    }
}

Static BOOLEAN accumulated_calc_crc(int calc_len, int data_len, uint32_t *crc_ctx, uint8_t *file_data)
{
    if(bBreakFlag == False)
    {
        do {
        calc_len = data_len > READ_BYTES ? READ_BYTES : data_len;
        crc32_calc(crc_ctx, file_data, calc_len);
        data_len -= calc_len;
        file_data += calc_len;
        }while(data_len > 0);
        return True;
    }
    else
    {
        bBreakFlag = False;
        return False;
    }
}

 /*
 * @brief 升级数据帧处理
 * @param[in] pData_info 升级数据帧DATA段
 * @retval
 * @note  注解
 * @par 修改日志
 *      2022-12-01, added by zoushaoyun
 */
Static void data_handle(T_UpdateDataStruct *pData_info) {
    
    #ifndef KW_CHECK
    rt_memset_s(&file_info, sizeof(file_info), 0x00, sizeof(file_info));
    #endif
    if (pData_info->usFN2 == 0) {  // 数据首发帧
        #ifndef KW_CHECK
        rt_kprintf("收到首发帧\r\n");
        #endif
        if(!FirstFrameProcess(pData_info,&flagExtern))
        {
             return;
        }

    } else {  // 非首发帧
        rt_kprintf("收到数据帧号 : %04d/%04d", pData_info->usFN2, s_downloadHandler.download_mgr.file_info.total_frames - 1);
        if (s_downloadHandler.download_mgr.link_used) {
            s_downloadHandler.download_sta = DOWNLOAD_STATE_SAVE;
        } else {
            s_downloadHandler.download_sta = DOWNLOAD_STATE_ERR;
        }
    }

    switch (s_downloadHandler.download_sta) {
        case DOWNLOAD_STATE_INIT :
        {
            #ifndef KW_CHECK
            rt_memcpy_s(&s_downloadHandler.download_mgr.file_info, sizeof(file_info_t), &file_info, sizeof(file_info_t));
            #endif

            // 读取升级文件
            read_download_tmpInfo(&file_info);
            break_file_change_check();

            // 请求下一帧数据
            if (s_downloadHandler.download_mgr.link_used == FALSE) {
                set_download_manager(&s_downloadHandler.download_mgr.file_info);
            }

            pData_info->usFN1 = s_downloadHandler.download_mgr.file_info.frame_cnt;
            pData_info->usFN2 = s_downloadHandler.download_mgr.file_info.frame_cnt + 1;
            pData_info->ucRtn = DOWNLOAD_RTN_OK;

            if (pData_info->usFN2 < s_downloadHandler.download_mgr.file_info.total_frames) {
                #ifndef KW_CHECK
                rt_kprintf("请求下一帧号 : %d\r\n", pData_info->usFN2);
                #endif
            }
            break;
        }
        case DOWNLOAD_STATE_SAVE :  // 数据写入
        {
            // 收到的帧数据是需要请求的
            if (pData_info->usFN2 == s_downloadHandler.download_mgr.file_info.frame_cnt + 1) {
                #ifndef KW_CHECK
                 if(pData_info->usFN2 == 1)
                {
                    s_bFisrtEarseFlash = True;
                }
                s_tfileDownload[ucFileType].file_download_process(pData_info);
                #endif
                
                // 下帧数据写入位置
                s_downloadHandler.download_mgr.file_info.write_pos += pData_info->usLen;
                // 更新已接收帧号
                s_downloadHandler.download_mgr.file_info.frame_cnt = pData_info->usFN2;

                // 判断是否为最后一帧数据
                if (s_downloadHandler.download_mgr.file_info.frame_cnt == \
                    s_downloadHandler.download_mgr.file_info.total_frames - 1) {
                    #ifndef KW_CHECK
                    rt_kprintf("rec frame %d , file receive finished\r\n", s_downloadHandler.download_mgr.file_info.frame_cnt);
                    s_downloadHandler.taskStart = FALSE;

                    rt_timer_stop(s_downloadHandler.trigTimer);
                    rt_timer_stop(s_downloadHandler.dataTimer);
                    #endif
                    s_downloadHandler.download_mgr.trig_times = 0;
                    s_downloadHandler.download_mgr.trig_success = FALSE;

                   //文件接受完毕后的处理
                    if(s_tfileDownload[ucFileType].file_received_process(pData_info) == False)
                    {
                        break;
                    }
                }
                pData_info->ucRtn = DOWNLOAD_RTN_OK;
            } else {
                pData_info->ucRtn = DOWNLOAD_RTN_CMD_ERR;
            }

            // 请求下一帧
            pData_info->usFN1 = s_downloadHandler.download_mgr.file_info.frame_cnt;
            pData_info->usFN2 = s_downloadHandler.download_mgr.file_info.frame_cnt + 1;
            if (pData_info->usFN2 < s_downloadHandler.download_mgr.file_info.total_frames) {
                rt_kprintf("请求下一帧号 : %d\r\n", pData_info->usFN2);
            }
            break;
        }
        default :
            pData_info->usFN1 = 0;
            pData_info->usFN2 = 0;
            pData_info->ucRtn = DOWNLOAD_RTN_CMD_ERR;
    }

    pData_info->usLen = 0;  // 应答帧无数据
}

/*
 * 首发下载帧处理
 */
static BOOLEAN first_frame_handle(T_UpdateDataStruct *pData_info) {
    BYTE i = 0;
    ucFileType = ILLEGAL_FLIE;
    // if (s_downloadHandler.download_mgr.link_used) {
    //         write_download_tmpInfo(&s_downloadHandler.download_mgr.file_info, FLAG_BACKUP);
    //     }
        file_info.total_frames = GetInt16Data(&pData_info->aucBuff[DOWNLOAD_TOTAL_FRAME_INDEX]);  // 总帧数
        file_info.total_leng   = GetInt32Data(&pData_info->aucBuff[DOWNLOAD_FILE_LENG_INDEX]);    // 总长度

        // 文件名
        #ifndef KW_CHECK
        rt_memcpy(file_info.fileName, &pData_info->aucBuff[DOWNLOAD_FILE_NAME_INDEX], DOWNLOAD_FILE_NAME_LENG);
        #endif
        file_info.fileName[DOWNLOAD_FILE_NAME_LENG - 1] = 0;
        // if(strcmp(ucCheckFileNameBin,file_info.fileName))
        // {
        //     pData_info->ucRtn = DOWNLOAD_RTN_CMD_ERR;
        //     pData_info->usLen = 0;  // 应答帧无数据
        //     return False;
        // }

        for(i = 0;i < sizeof(s_tfileDownload)/sizeof(differnet_file_management);i++)
         {
            if(!strcmp(s_tfileDownload[i].file_name,file_info.fileName))
            {
                ucFileType = s_tfileDownload[i].ucFileType;
                ucFileStatus[i] = FILE_TRANSLATING;
                break;
            }
         }

        if(ucFileType == ILLEGAL_FLIE)
        {
            rt_kprintf("file type illegal\n");
            pData_info->ucRtn = DOWNLOAD_RTN_CMD_ERR;
            pData_info->usLen = 0;  // 应答帧无数据
            return False;
        }

        // 文件时间
        #ifndef KW_CHECK
        rt_memcpy(file_info.fileTime, &pData_info->aucBuff[DOWNLOAD_FILE_TIME_INDEX], DOWNLOAD_FILE_TIME_LENG);
        #endif
        file_info.fileTime[DOWNLOAD_FILE_TIME_LENG - 1] = 0;

        // 参数类型
        file_info.param_type = pData_info->aucBuff[DOWNLOAD_FILE_PARA_INDEX];
        #ifndef KW_CHECK
        rt_kprintf("升级文件包数 : %d\r\n", file_info.total_frames - 1);
        rt_kprintf("升级文件长度 : %d\r\n", file_info.total_leng);
        rt_kprintf("升级文件名称 : %s\r\n", file_info.fileName);
        rt_kprintf("升级文件时间 : %s\r\n", file_info.fileTime);
        #endif

        file_info.write_pos = 0;
        file_info.frame_cnt = 0;
        file_info.crc       = 0;

        if (TRUE == s_downloadHandler.download_mgr.link_used) {
            if (TRUE == file_change_check(&s_downloadHandler.download_mgr.file_info, &file_info)) {
                #ifndef KW_CHECK
                rt_kprintf("文件发生变化, 释放下载管理器\r\n");
                #endif
                reset_download_manager();
            }
        }

        s_downloadHandler.download_sta = DOWNLOAD_STATE_INIT;
        return True;

}

/*
 * 扩展首发下载帧处理
 */
Static BOOLEAN first_extern_frame_handle(T_UpdateDataStruct *pData_info) {
    BYTE i = 0;
    ucFileType = ILLEGAL_FLIE;
    // if (s_downloadHandler.download_mgr.link_used) {
    //         write_download_tmpInfo(&s_downloadHandler.download_mgr.file_info, FLAG_BACKUP);
    //     }
        file_info.total_frames = GetInt16Data(&pData_info->aucBuff[EXT_DOWNLOAD_TOTAL_FRAME_INDEX]);  // 总帧数
        file_info.total_leng   = GetInt32Data(&pData_info->aucBuff[EXT_DOWNLOAD_FILE_LENG_INDEX]);    // 总长度

        // 文件名
        #ifndef KW_CHECK
        rt_memcpy(file_info.fileName, &pData_info->aucBuff[EXT_DOWNLOAD_FILE_NAME_INDEX], EXT_DOWNLOAD_FILE_NAME_LENG);
        #endif
        file_info.fileName[EXE_DOWNLOAD_FILE_NAME_LENG - 1] = 0;
        // if(strcmp(ucCheckFileNameBin,file_info.fileName))
        // {
        //     pData_info->ucRtn = DOWNLOAD_RTN_CMD_ERR;
        //     pData_info->usLen = 0;  // 应答帧无数据
        //     return False;
        // }

         for(i = 0;i < sizeof(s_tfileDownload)/sizeof(differnet_file_management);i++)
        {
            if(!strcmp(s_tfileDownload[i].file_name,file_info.fileName))
            {
                ucFileType = s_tfileDownload[i].ucFileType;
                break;
            }
        }

        if(ucFileType == ILLEGAL_FLIE)
        {
            rt_kprintf("file type illegal\n");
            pData_info->ucRtn = DOWNLOAD_RTN_CMD_ERR;
            pData_info->usLen = 0;  // 应答帧无数据
            return False;
        }

        // 文件时间
        #ifndef KW_CHECK
        rt_memcpy(file_info.fileTime, &pData_info->aucBuff[EXT_DOWNLOAD_FILE_TIME_INDEX], DOWNLOAD_FILE_TIME_LENG);
        #endif
        file_info.fileTime[DOWNLOAD_FILE_TIME_LENG - 1] = 0;

        //文件crc校验
        file_info.crc = GetInt32Data(&pData_info->aucBuff[EXT_DOWNLOAD_FILE_CRC_INDEX]);

        // 参数类型
        file_info.param_type = pData_info->aucBuff[EXT_DOWNLOAD_FILE_PARA_INDEX];
        #ifndef KW_CHECK
        rt_kprintf("升级文件包数 : %d\r\n", file_info.total_frames - 1);
        rt_kprintf("升级文件长度 : %d\r\n", file_info.total_leng);
        rt_kprintf("升级文件名称 : %s\r\n", file_info.fileName);
        rt_kprintf("升级文件时间 : %s\r\n", file_info.fileTime);
        #endif

        file_info.write_pos = 0;
        file_info.frame_cnt = 0;

        if (TRUE == s_downloadHandler.download_mgr.link_used) {
            if (TRUE == file_change_check(&s_downloadHandler.download_mgr.file_info, &file_info)) {
                #ifndef KW_CHECK
                rt_kprintf("文件发生变化, 释放下载管理器\r\n");
                #endif
                reset_download_manager();
            }
        }

        s_downloadHandler.download_sta = DOWNLOAD_STATE_INIT;
        return True;

}

/*
 * 定时检查
 */
Static void check_state(void) {
    if (s_downloadHandler.taskStart == TRUE) {
        // 收不到触发帧则认为触发帧超时
        if (TRUE == s_downloadHandler.download_mgr.link_used) {
            reset_download_manager();
        }
        #ifndef KW_CHECK
        rt_kprintf("\r\n------升级超时-------\r\n");
        rt_timer_stop (s_downloadHandler.trigTimer);
        rt_timer_stop (s_downloadHandler.dataTimer);
        #endif
        s_downloadHandler.taskStart = FALSE;
        s_downloadHandler.download_mgr.trig_success = FALSE;
        s_downloadHandler.download_mgr.trig_times   = 0;
    }
}


/*
 * @brief 触发帧命令处理（包括普通触发帧和扩展触发帧）
 * @param[in] pData 帧数据
 * @retval
 * @note  注解
 * @par 修改日志
 *      2022-12-01, added by zoushaoyun
 */
Static void cmd_trigFrme_func(T_UpdateFrameStruct *pData) {
    s_downloadHandler.taskStart = TRUE;
    // 第一次收到触发帧
    if (s_downloadHandler.download_mgr.trig_times == 0) {
        #ifndef KW_CHECK
        rt_timer_stop(s_downloadHandler.trigTimer);
        rt_timer_start(s_downloadHandler.trigTimer);
        rt_timer_stop(s_downloadHandler.dataTimer);
        rt_timer_start(s_downloadHandler.dataTimer);
        #endif
    }

    // 重置超时定时器
    #ifndef KW_CHECK
    rt_timer_stop(s_downloadHandler.trigTimer);
    rt_timer_start(s_downloadHandler.trigTimer);
    #endif

    if (++s_downloadHandler.download_mgr.trig_times >= DOWNLOAD_TRIG_TIMES) {
        s_downloadHandler.download_mgr.trig_times   = 0;
        s_downloadHandler.download_mgr.trig_success = TRUE;  // 收到 3 次触发帧，确认下载
        #ifndef KW_CHECK
        rt_timer_stop (s_downloadHandler.trigTimer);         // 关闭触发超时定时器
        #endif
    }

    // 生成应答数据
    if(s_tUpdateFramePrase.ucCmd == DLCMD_TRIG_FRAME) // 普通触发帧回包
    {
        pData->trigFrame.usLeng = sizeof(trig_frame_ack);
        rt_memcpy_s(pData->trigFrame.aucData, sizeof(pData->trigFrame.aucData), trig_frame_ack, pData->trigFrame.usLeng);
    }
    else // 扩展触发帧回包
    {
        pData->trigFrame.usLeng = sizeof(ext_trig_frame_ack);
        rt_memcpy_s(pData->trigFrame.aucData, sizeof(pData->trigFrame.aucData), ext_trig_frame_ack, pData->trigFrame.usLeng);
    }

    // // 应答数据放入缓存
    // rt_mutex_take(s_mSendQueMutex, RT_WAITING_FOREVER);
    // rt_memcpy_s(&s_tUpdateFramePack, sizeof(s_tUpdateFramePack), pData, sizeof(s_tUpdateFramePack));
    // rt_mutex_release(s_mSendQueMutex);
}


/*
 * @brief 获取唯一标识处理函数
 * @param[in] pData 帧数据
 * @retval
 * @note  注解
 * @par 修改日志
 *      2023-02-03, added by hxk
 */
//static BYTE bMacAddr[20] ={0x30,0x30,0x2D,0x38,0x30,0x2D,0x45,0x31,0x2D,0x31,0x37,0x2D,0x33,0x36,0x2D,0x31,0x41};
Static void cmd_GetFlag_func(T_UpdateFrameStruct *pData)
{
    BYTE bMacAddr[20] = {0};
    pData->dataFrame.ucFlag = 0x41;
	pData->dataFrame.usAddr = 0x00;
	pData->dataFrame.usLen = 0x14;
	pData->dataFrame.usFN1 = 0x00;
	pData->dataFrame.usFN2 = 0x00;
	pData->dataFrame.ucDev = 0x04;
	pData->dataFrame.ucCMD = 0x14;
    pData->dataFrame.ucRtn = 0x00;
	
    GetMacAddr(bMacAddr,20);
	rt_memcpy(pData->dataFrame.aucBuff,bMacAddr,sizeof(bMacAddr));
	
    // #ifndef KW_CHECK
	// rt_mutex_take(s_mSendQueMutex, RT_WAITING_FOREVER);
    // #endif
	// rt_memcpy(&s_tUpdateFramePack, pData, sizeof(s_tUpdateFramePack));
    // #ifndef KW_CHECK
	// rt_mutex_release(s_mSendQueMutex);
    // #endif

}

/*
 * @brief 下载命令处理
 * @param[in] pData 帧数据
 * @retval
 * @note  注解
 * @par 修改日志
 *      2022-12-01, added by zoushaoyun
 */
static void cmd_download_func(T_UpdateFrameStruct *pData) {
    if (s_downloadHandler.download_mgr.trig_success != TRUE) {  // 没有收到三次触发帧，未触发下载
        if (0 == s_downloadHandler.download_mgr.trig_times) {   // 一次触发帧都没有收到
            #ifndef KW_CHECK
            rt_timer_start(s_downloadHandler.dataTimer);
            #endif
        }

        pData->dataFrame.usFN1 = 0;
        pData->dataFrame.usFN2 = 0;

        pData->dataFrame.ucRtn = DOWNLOAD_RTN_TRG_ERR;
        pData->dataFrame.usLen = 0;
    } else {
        // 触发成功，则进行烧写
        s_downloadHandler.taskStart = TRUE;
        s_downloadHandler.handle_data(&pData->dataFrame);
    }

    // 重置下载超时定时器
    #ifndef KW_CHECK
    rt_timer_start(s_downloadHandler.dataTimer);

    // // 应答数据放入缓存
    // rt_mutex_take(s_mSendQueMutex, RT_WAITING_FOREVER);
    // rt_memcpy(&s_tUpdateFramePack, pData, sizeof(s_tUpdateFramePack));
    // rt_mutex_release(s_mSendQueMutex);
    #endif
}


Static void cmd_upload_func(T_UpdateFrameStruct *pData)
{
    // 检查是否未触发下载
    if (s_downloadHandler.download_mgr.trig_success != TRUE) {
        // 如果一次触发帧都没有收到
        if (s_downloadHandler.download_mgr.trig_times == 0) {
            rt_timer_start(s_downloadHandler.dataTimer);
        }

        // 初始化数据帧
        pData->dataFrame.usFN1 = 0;
        pData->dataFrame.usFN2 = 0;
        pData->dataFrame.ucRtn = DOWNLOAD_RTN_TRG_ERR;
        pData->dataFrame.usLen = 0;
    } else {
        // 触发成功，则进行烧写
        s_downloadHandler.taskStart = TRUE;
        ULFileProcess(pData);
    }

    // 重置下载超时定时器
    rt_timer_start(s_downloadHandler.dataTimer);

    // 应答数据放入缓存
    rt_mutex_take(s_mSendQueMutex, RT_WAITING_FOREVER);
    rt_memcpy_s(&s_tUpdateFramePrase, sizeof(s_tUpdateFramePrase), pData, sizeof(s_tUpdateFramePrase));
    rt_mutex_release(s_mSendQueMutex);
}


/*
 * @brief 数据解码
 * @param[in] pkt 协议层数据缓冲区
 * @param[in] pdata_buf 经过解码的数据（去帧头帧尾）
 * @retval
 * @note  注解 还原转码数据
 * @par 修改日志
 *      2022-12-01, added by zoushaoyun
 */
static WORD decodeframe(T_CommRecCache *pkt, unsigned char *pdata_buf)
{
    int i;
    WORD wDataLen = 0;

    if(pkt->ulDataLength > RAW_BUFF_LEN)
    {
        return 0;
    }

    for (i = 1; i < pkt->ulDataLength - 1; i++) {
        if (DOWNLOAD_SHIFT == pkt->aucDataBuff[i]) {
            i++;
            *pdata_buf++ = ~pkt->aucDataBuff[i];
        } else {
            *pdata_buf++ = pkt->aucDataBuff[i];
        }

        wDataLen++;
    }

    return wDataLen;
}

/*
 * @brief 从链路层读取下载帧数据到协议层
 * @param[in] pCache 链路层数据缓冲区
 * @param[in] ptDataOut 协议层数据缓冲区
 * @retval
 * @note  注解 获取完整一帧数据
 * @par 修改日志
 *      2022-12-01, added by zoushaoyun
 */
Static int getOneframe(T_CommRecCache *pCache)
{
    unsigned int slLen = 0;
    int ret = FAILURE;
    UINT32 wCnt = 0;
    BYTE *p = NULL;

    RETURN_VAL_IF_FAIL(pCache != NULL, FAILURE);
    
    p = pCache->aucDataBuff;
    while (wCnt < pCache->ulDataLength && (pCache->wPktStartIndex + 1 <= sizeof(pCache->aucDataBuff) - 1)) {
        // 找到帧头
        if (DOWNLOAD_SOI == pCache->aucDataBuff[wCnt]) {
            rt_kprintf("+++找到帧头\r\n");
            pCache->wPktStartIndex = wCnt;  // 记录帧头下标
            pCache->bRecHeader = TRUE;      // 标记找到帧头
            pCache->bRecTail = FALSE;
        }

        // 找到帧尾
        if ((pCache->aucDataBuff[wCnt] == DOWNLOAD_EOI) && (pCache->bRecHeader == TRUE) && \
            (wCnt > pCache->wPktStartIndex) && (pCache->wPktStartIndex + 1 < sizeof(pCache->aucDataBuff))) {
            #ifndef KW_CHECK
            pCache->bRecTail = TRUE;
            rt_kprintf("---找到帧尾\r\n");
            #endif
            // 判断是下载帧，新增的扩展触发帧第二个字节为0x30;
            if ((pCache->aucDataBuff[pCache->wPktStartIndex + 1] == DOWNLOAD_FLAG) || (pCache->aucDataBuff[pCache->wPktStartIndex + 1] == 0x30)) {
                slLen = wCnt - pCache->wPktStartIndex + 1;  // 下载帧的长度
                if (slLen < sizeof(pCache->aucDataBuff) - pCache->wPktStartIndex) {
                    #ifndef KW_CHECK
                    rt_memcpy(pCache->aucDataBuff, &pCache->aucDataBuff[pCache->wPktStartIndex], slLen);  // 取下载帧数据
                    p += slLen;
                    rt_memset(p, 0x00, sizeof(s_tUpdateCommRecCache)-slLen);  // 清空数据接收区
                    pCache->ulDataLength = slLen;  // 下载帧长度
                    rt_kprintf("当前下载帧长度%d\r\n", slLen);
                    #endif
                    return SUCCESSFUL;
                }
            } else {
                #ifndef KW_CHECK
                rt_memset(&s_tUpdateCommRecCache, 0x00, sizeof(s_tUpdateCommRecCache));
                #endif
                return ret;
            }
        }
        wCnt++;
    }

    // 网口远程升级1k和近端升级4k兼容
    if(pCache->bRecHeader != TRUE || pCache->bRecTail != TRUE)
    {
        rt_memset(&s_tUpdateCommRecCache, 0x00, sizeof(s_tUpdateCommRecCache));  // 清空数据接收区
    }

    pCache->bRecHeader = FALSE;
    pCache->bRecTail = FALSE;
    return ret;
}

/*
 * @brief 解析函数(包括数据解码)
 * @param[in]
 * @retval
 * @note  注解
 * @par 修改日志
 *      2022-12-01, added by zoushaoyun
 */
INT32S parseCmd(void)
{
    unsigned short frameType, version;
    unsigned short calcCrc, recvCrc;
    unsigned char *decodeBuf = NULL;
    BYTE cid = 0;
    WORD wLen = 0;
    // unsigned short extAddr = 0; // 扩展触发帧地址
    unsigned short extReserved = 0; // 扩展触发帧保留位
    unsigned short extFrameType = 0; // 扩展触发帧类型
    unsigned short extVersion = 0; // 扩展触发帧协议版本

    InitUpdateProtocol(); // init check
    #ifndef KW_CHECK
    rt_memset(&s_tUpdateFramePrase, 0x00, sizeof(s_tUpdateFramePrase));
    //rt_memset(&s_tParsedOneFrame, 0x00, sizeof(s_tParsedOneFrame));
    //rt_memset(decodeBuf, 0x00, sizeof(decodeBuf));
    #endif

    if (getOneframe(&s_tUpdateCommRecCache) == SUCCESSFUL) { // 从链路层提取一帧下载帧数据
        wLen = decodeframe(&s_tUpdateCommRecCache, s_tUpdateCommRecCache.aucDataBuff);  // 对下载帧解码，去帧头帧尾
        if(wLen == 0)
        {
            rt_memset_s(&s_tUpdateCommRecCache,sizeof(T_CommRecCache) ,0x00, sizeof(T_CommRecCache));
            return FAILURE;
        }
        decodeBuf = s_tUpdateCommRecCache.aucDataBuff;
        decodeBuf += wLen;
        rt_memset_s(decodeBuf,sizeof(s_tUpdateCommRecCache.aucDataBuff) - wLen,0x00, sizeof(s_tUpdateCommRecCache.aucDataBuff) - wLen);
        decodeBuf = s_tUpdateCommRecCache.aucDataBuff;

        // 协议解析
        frameType = GetInt16Data(&decodeBuf[DOWNLOAD_FRM_INDEX]);  // 获取帧类型
        version = GetInt16Data(&decodeBuf[DOWNLOAD_VER_INDEX]);  // 获取协议版本
        cid = decodeBuf[DOWNLOAD_CID_INDEX];  // 命令码
        // extAddr = GetInt16Data(&decodeBuf[DOWNLOAD_EXT_ADDR_INDEX]); // 获取扩展触发帧地址
        extReserved = GetInt16Data(&decodeBuf[DOWNLOAD_EXT_RESV_INDEX]);// 获取扩展触发帧保留位
        extFrameType = GetInt16Data(&decodeBuf[DOWNLOAD_EXT_FRM_INDEX]);// 获取扩展触发帧类型
        extVersion = GetInt16Data(&decodeBuf[DOWNLOAD_EXT_VER_INDEX]);// 获取扩展触发帧协议版本

        if ((frameType == DOWNLOAD_TRIG_FRAME) && (version == DOWNLOAD_TRIG_CHIP)) {  // 是E版本下载触发帧
            s_tUpdateFramePrase.ucCmd     = DLCMD_TRIG_FRAME;
            s_tUpdateFramePrase.frameType = eTrigFrame;
            s_bIsExtendTrigFrame = FALSE;
        } else if ((extReserved == DOWNLOAD_EXT_RESV_FRAME) && (extFrameType == DOWNLOAD_EXT_TRIG_FRAME) && (extVersion == DOWNLOAD_EXT_TRIG_CHIP)) {  // 扩展触发帧
            s_tUpdateFramePrase.ucCmd   = DLCMD_EXT_TRIG_FRAME;
            s_tUpdateFramePrase.frameType = eExtTrigFrame;
            s_bIsExtendTrigFrame = TRUE;
        } else if (cid == DLCMD_GET_MAC_ADDR_PROC) {  // 获取MAC地址
            s_tUpdateFramePrase.ucCmd   = DLCMD_GET_MAC_ADDR_PROC;
            s_tUpdateFramePrase.frameType = eGetMacFrame;
        } else if (cid == DLCMD_GET_BATCH_UPDATE_PROC) {  // 获取批量升级进度
            s_tUpdateFramePrase.ucCmd   = DLCMD_GET_BATCH_UPDATE_PROC;
            s_tUpdateFramePrase.frameType = eGetBatchUpdatePro;
        } else { //数据下载帧
            s_tUpdateFramePrase.dataFrame.ucFlag = decodeBuf[DOWNLOAD_FLAG_INDEX];
            s_tUpdateFramePrase.dataFrame.usAddr = GetInt16Data(&decodeBuf[DOWNLOAD_ADDR_INDEX]);
            s_tUpdateFramePrase.dataFrame.usLen  = GetInt16Data(&decodeBuf[DOWNLOAD_LEN_INDEX]);
            s_tUpdateFramePrase.dataFrame.usFN1  = GetInt16Data(&decodeBuf[DOWNLOAD_FN1_INDEX]);
            s_tUpdateFramePrase.dataFrame.usFN2  = GetInt16Data(&decodeBuf[DOWNLOAD_FN2_INDEX]);
            s_tUpdateFramePrase.dataFrame.ucDev  = decodeBuf[DOWNLOAD_DEV_INDEX];
            s_tUpdateFramePrase.dataFrame.ucCMD  = decodeBuf[DOWNLOAD_CID_INDEX];
            s_tUpdateFramePrase.dataFrame.ucRtn  = decodeBuf[DOWNLOAD_RTN_INDEX];

            //s_tUpdateFramePrase.ucCmd = s_tUpdateFramePrase.dataFrame.ucCMD;
            s_tUpdateFramePrase.ucCmd = DLCMD_DOWNLOAD;
            s_tUpdateFramePrase.frameType = eDataFrame;

            // CRC 校验，如果校验失败,重新请求上一帧
            calcCrc = Calculate_CRC16(decodeBuf, s_tUpdateFramePrase.dataFrame.usLen + DOWNLOAD_DAT_INDEX);  // 计算校验码
            recvCrc = GetInt16Data(&decodeBuf[s_tUpdateFramePrase.dataFrame.usLen + DOWNLOAD_DAT_INDEX]);    // 取校验码
            #ifndef KW_CHECK
            //测试过程中，收到的数据为空，但是帧号和期望帧号一致，导致跳过当前帧，因此做一个校验
            if(s_tUpdateFramePrase.dataFrame.usLen == 0 && s_tUpdateFramePrase.dataFrame.usFN1 == s_tUpdateFramePrase.dataFrame.usFN2 -1)
            {
                rt_kprintf("长度 校验出错, len = 0x%x\r\n", s_tUpdateFramePrase.dataFrame.usLen);
                s_tUpdateFramePrase.dataFrame.ucRtn = DOWNLOAD_RTN_OK;
                s_tUpdateFramePrase.dataFrame.usLen = 0;

                rt_mutex_take(s_mSendQueMutex, RT_WAITING_FOREVER);
                rt_memset(&s_tUpdateCommRecCache, 0x00, sizeof(T_CommRecCache));

                //CRC校验失败，发送上次返回帧
                rt_memcpy(&s_tUpdateFramePrase, &s_tUpdateFramePackTmp, sizeof(s_tUpdateFramePrase));
                rt_mutex_release(s_mSendQueMutex);
            }
            else if (calcCrc != recvCrc) {
                rt_kprintf("CRC 校验出错, calc = 0x%x, recv = 0x%x\r\n", calcCrc, recvCrc);
                s_tUpdateFramePrase.dataFrame.ucRtn = DOWNLOAD_RTN_CRC_ERR;
                s_tUpdateFramePrase.dataFrame.usLen = 0;

                rt_mutex_take(s_mSendQueMutex, RT_WAITING_FOREVER);
                rt_memset(&s_tUpdateCommRecCache, 0x00, sizeof(T_CommRecCache));

                //CRC校验失败，发送上次返回帧
                rt_memcpy(&s_tUpdateFramePrase, &s_tUpdateFramePackTmp, sizeof(s_tUpdateFramePrase));
                rt_mutex_release(s_mSendQueMutex);

                return FAILURE;
            } else {  // 校验通过，拷贝DATA段数据
                rt_memcpy(s_tUpdateFramePrase.dataFrame.aucBuff, &decodeBuf[DOWNLOAD_DAT_INDEX], s_tUpdateFramePrase.dataFrame.usLen);
            }
            #endif
        }

        rt_memset_s(&s_tUpdateCommRecCache,sizeof(T_CommRecCache) ,0x00, sizeof(T_CommRecCache));
        return SUCCESSFUL;
    }else {
        #ifndef KW_CHECK
        rt_kprintf("未获取到一帧完整数据，继续接收...\r\n");
        #endif
        return FAILURE;
    }
}

/*
 * @brief 命令执行函数(生成应答)
 * @param[in]
 * @retval
 * @note  注解
 * @par 修改日志
 *      2022-12-01, added by zoushaoyun
 */
INT32S exeCmd(void) {
    int i = 0;

    while (s_tDownLoadCMD[i].pFunc != NULL) {
        if (s_tUpdateFramePrase.ucCmd == s_tDownLoadCMD[i].ucCmd) {
            s_tDownLoadCMD[i].pFunc(&s_tUpdateFramePrase);
            break;
        }
        i++;
    }

    return SUCCESSFUL;
}

/*
 * @brief 发送前转码
 * @param[in] pDestBuf   转码后的缓冲区
 * @param[in] pSrcBuf    要发送的帧数据
 * @retval
 * @note  注解
 * @par 修改日志
 *      2022-12-01, added by zoushaoyun
 */
static void codeShift(T_PRTCL_REC_BUFF *pDestBuf, T_PRTCL_REC_BUFF *pSrcBuf)
{
    int i = 0;

    for (i = 0; i < pSrcBuf->wDataLength; i++) {
        if (pSrcBuf->aucDataBuff[i] == DOWNLOAD_SHIFT_CODE1 || \
            pSrcBuf->aucDataBuff[i] == DOWNLOAD_SHIFT_CODE2 || \
            pSrcBuf->aucDataBuff[i] == DOWNLOAD_SHIFT_CODE3 || \
            pSrcBuf->aucDataBuff[i] == DOWNLOAD_SHIFT_CODE4 || \
            pSrcBuf->aucDataBuff[i] == DOWNLOAD_SHIFT_CODE5 || \
            pSrcBuf->aucDataBuff[i] == DOWNLOAD_SHIFT_CODE6) {
            pDestBuf->aucDataBuff[pDestBuf->wDataLength++] = DOWNLOAD_SHIFT;
            pDestBuf->aucDataBuff[pDestBuf->wDataLength++] = ~pSrcBuf->aucDataBuff[i];
        } else {
            pDestBuf->aucDataBuff[pDestBuf->wDataLength++] = pSrcBuf->aucDataBuff[i];
        }
    }
}

/*
 * @brief 打包要发送的帧数据
 * @retval    SUCCESSFUL 成功， FAILURE 失败
 * @note  注解
 * @par 修改日志
 *      2022-12-01, added by zoushaoyun
 */
INT32S getDataFromQueue(void) {
    unsigned short crc = 0;
    #ifndef KW_CHECK
    rt_memset(&s_tSendFrame, 0x00, sizeof(s_tSendFrame));

    // 从协议层回帧数据中提取回包数据
    rt_mutex_take(s_mSendQueMutex, RT_WAITING_FOREVER);
    #endif
    if (s_tUpdateFramePrase.frameType == eFrameNone) {
        #ifndef KW_CHECK
        rt_mutex_release(s_mSendQueMutex);
        #endif
        return FAILURE;
    }

    if ((s_tUpdateFramePrase.frameType == eTrigFrame) || (s_tUpdateFramePrase.frameType == eExtTrigFrame))   // 回触发帧或扩展触发帧
    {  // 或回获取前台唯一标识的帧
        rt_memcpy(s_tSendFrame.aucDataBuff, s_tUpdateFramePrase.trigFrame.aucData, s_tUpdateFramePrase.trigFrame.usLeng);
        s_tSendFrame.wDataLength = s_tUpdateFramePrase.trigFrame.usLeng;
    }
    else
    {  // 是回数据下载帧
        s_tSendFrame.wDataLength = 0;
        s_tSendFrame.aucDataBuff[s_tSendFrame.wDataLength++] = s_tUpdateFramePrase.dataFrame.ucFlag;

        PutInt16ToBuff(&s_tSendFrame.aucDataBuff[s_tSendFrame.wDataLength], s_tUpdateFramePrase.dataFrame.usAddr);
        s_tSendFrame.wDataLength += 2;

        PutInt16ToBuff(&s_tSendFrame.aucDataBuff[s_tSendFrame.wDataLength], 0x0000);  // 预留的2字节
        s_tSendFrame.wDataLength += 2;

        PutInt16ToBuff(&s_tSendFrame.aucDataBuff[s_tSendFrame.wDataLength], s_tUpdateFramePrase.dataFrame.usLen);
        s_tSendFrame.wDataLength += 2;

        PutInt16ToBuff(&s_tSendFrame.aucDataBuff[s_tSendFrame.wDataLength], s_tUpdateFramePrase.dataFrame.usFN1);  // 收到的帧号
        s_tSendFrame.wDataLength += 2;

        PutInt16ToBuff(&s_tSendFrame.aucDataBuff[s_tSendFrame.wDataLength], s_tUpdateFramePrase.dataFrame.usFN2);  // 请求的帧号
        s_tSendFrame.wDataLength += 2;

        s_tSendFrame.aucDataBuff[s_tSendFrame.wDataLength++] = s_tUpdateFramePrase.dataFrame.ucDev;
        s_tSendFrame.aucDataBuff[s_tSendFrame.wDataLength++] = s_tUpdateFramePrase.dataFrame.ucCMD;
        s_tSendFrame.aucDataBuff[s_tSendFrame.wDataLength++] = s_tUpdateFramePrase.dataFrame.ucRtn;

        if (s_tUpdateFramePrase.frameType == eGetMacFrame)
        {
            rt_memcpy(&s_tSendFrame.aucDataBuff[s_tSendFrame.wDataLength], s_tUpdateFramePrase.dataFrame.aucBuff, 20);
            s_tSendFrame.wDataLength += 20;
        }
        else if (s_tUpdateFramePrase.frameType == eGetBatchUpdatePro)
        {  // 回获取批量升级进度帧
            rt_memcpy(&s_tSendFrame.aucDataBuff[s_tSendFrame.wDataLength], s_tUpdateFramePrase.dataFrame.aucBuff, 65);
            s_tSendFrame.wDataLength += 65;
        }

        crc = Calculate_CRC16(s_tSendFrame.aucDataBuff, s_tSendFrame.wDataLength);
        PutInt16ToBuff(&s_tSendFrame.aucDataBuff[s_tSendFrame.wDataLength], crc);
        s_tSendFrame.wDataLength += 2;
    }
    #ifndef KW_CHECK
    rt_memset(&s_tUpdateFramePackTmp, 0x00, sizeof(s_tUpdateFramePackTmp));
    rt_memcpy(&s_tUpdateFramePackTmp,&s_tUpdateFramePrase,sizeof(s_tUpdateFramePrase));  // 暂存本次回帧数据，用于重发
    rt_memset(&s_tUpdateFramePrase, 0x00, sizeof(s_tUpdateFramePrase));  // 清除回帧数据

    // 加帧头
    rt_memset(&s_tUpdateCommSendCache, 0x00, sizeof(s_tUpdateCommSendCache));
    #endif
    s_tUpdateCommSendCache.wDataLength = 0;
    s_tUpdateCommSendCache.aucDataBuff[s_tUpdateCommSendCache.wDataLength++] = DOWNLOAD_SOI;
    // 回帧数据转码
    codeShift(&s_tUpdateCommSendCache, &s_tSendFrame);
    // 加帧尾
    s_tUpdateCommSendCache.aucDataBuff[s_tUpdateCommSendCache.wDataLength++] = DOWNLOAD_EOI;
    #ifndef KW_CHECK
    rt_mutex_release(s_mSendQueMutex);
    #endif

    // if(s_bFileTransComplete == TRUE)
    // {
    //     s_bFileTransComplete =  FALSE;
    //     ResetMCU( NO_RESET_CSU_UPGRADE );
    // }

    return SUCCESSFUL;
}

/* 定时器trigTimer超时函数 */
Static void trigTimer_timeout(void* parameter)
{
    s_downloadHandler.state_check();
}

/* 定时器trigTimer超时函数 */
Static void dataTimer_timeout(void* parameter)
{
    s_downloadHandler.state_check();
}

/*
 * @brief 初始化升级协议
 * @param[in]
 * @retval
 * @note  注解
 * @par 修改日志
 *      2022-12-01, added by zoushaoyun
 */
void InitUpdateProtocol(void)
{
    if (TRUE == flagUpdateInited) {
        return;
    }

    rt_tick_t trig_tick_num = DOWNLOAD_TRIG_TIMEOUT * 1000;  // 定时长度,(60 * 5)秒
    rt_tick_t data_tick_num = DOWNLOAD_DATA_TIMEOUT * 1000;  // 定时长度,(60 * 5)秒

    s_downloadHandler.taskStart = FALSE;
    s_downloadHandler.download_mgr.fd           = -1;
    s_downloadHandler.download_mgr.link_used    = FALSE;
    s_downloadHandler.download_mgr.trig_times   = 0;
    s_downloadHandler.download_mgr.trig_success = FALSE;
    s_downloadHandler.update_file.valid         = FALSE;

    // 初始化循环校验码
    InitCRC();
    InitCRC32Table();
    // 添加超时定时器
    #ifndef KW_CHECK
    s_downloadHandler.trigTimer = rt_timer_create("trigTimer", trigTimer_timeout, RT_NULL, trig_tick_num, RT_TIMER_FLAG_PERIODIC);
    s_downloadHandler.dataTimer = rt_timer_create("dataTimer", dataTimer_timeout, RT_NULL, data_tick_num, RT_TIMER_FLAG_PERIODIC);
    #ifndef KW_CHECK
    if (s_downloadHandler.trigTimer == RT_NULL || s_downloadHandler.dataTimer == RT_NULL)
    {
        return;
    }
    rt_timer_stop(s_downloadHandler.trigTimer);
    rt_timer_stop(s_downloadHandler.dataTimer);
    rt_memset(&s_tUpdateCommRecCache, 0x00, sizeof(s_tUpdateCommRecCache));
    #endif
    #endif
    flagUpdateInited = TRUE;
}

/*
 * @brief 获取单板的MAC地址
 * @param[in]
 * @retval
 * @note  注解
 * @par 修改日志
 *      2023-02-06, add by hxk
 */
static void GetMacAddr(BYTE *buff,BYTE Length)
{
    BYTE *p = NULL;
    BYTE BUFF[10];
    BYTE aucMacAddr[6] = {0};
    BYTE bShiftValue = 0;
    BYTE i = 0;
    BYTE j = 0;
    static rt_device_t ETH_dev;

    if(Length > 20)
    {
        return;
    }

    p  = BUFF;
#if !defined(KW_CHECK) && !defined(UNITEST)
    ETH_dev = rt_device_find("e0");
    if (ETH_dev == RT_NULL)
    {
        return;
    }
    rt_device_control(ETH_dev, 0x01, aucMacAddr);
#endif
    for( i = 0 ; i < 6 ; i ++ )
    {
        *p++ = aucMacAddr[i];
    }

    for(i = 0;i < 6;i++ )
    {
        buff[j++] = (bShiftValue = aucMacAddr[i]>>4) < 10 ?(bShiftValue+0x30):(bShiftValue+0x37);
        buff[j++] = (bShiftValue = (aucMacAddr[i] & 0x0F)) < 10 ?(bShiftValue+0x30):(bShiftValue+0x37);
        buff[j++] = 0x2D;
    }
    buff[j-1] = 0x00;
}


//获取文件传输标志 by hxk 2023-02-07
BOOLEAN GetFileTransFlag(void)
{
    return s_bFileTransComplete;
}

//设置文件传输标志 by hxk 2023-02-07
void SetFileTransFlag(BOOLEAN Flag)
{
    s_bFileTransComplete = Flag;
}

//获取扩展触发帧标志
BOOLEAN GetExtendTrigFrameFlag(void)
{
    return s_bIsExtendTrigFrame;
}

static sfud_err write_to_norflash(const sfud_flash *flash, uint32_t addr, 
                          size_t size, const uint8_t *data,uint32_t earse_addr) 
{
    sfud_err result = SFUD_SUCCESS;
    static uint32_t s_u32LastErraseAddr = 0;

    #ifndef KW_CHECK 
    if(s_bFisrtEarseFlash)
    {
       s_u32LastErraseAddr = 0;
       s_bFisrtEarseFlash = False; 
    }

    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);
    if(earse_addr != s_u32LastErraseAddr)
    {
        s_u32LastErraseAddr = earse_addr;
        result = sfud_erase(flash, earse_addr, EARSE_FAN_BYTES);
    }
    else
    {
        result = SFUD_SUCCESS;
    }

    if (result == SFUD_SUCCESS) {
        result = sfud_write(flash, addr, size, data);
    }
    rt_mutex_release(s_ptMutexFile);
    #endif
    return result;
}



int RepeatSendlastFrame(void)
{
    return rt_memcpy_s(&s_tUpdateFramePrase, sizeof(s_tUpdateFramePrase), &s_tUpdateFramePackTmp, sizeof(s_tUpdateFramePackTmp));  // 暂存本次回帧数据，用于重发
}


//解决圈复杂度
static BOOLEAN FirstFrameProcess(T_UpdateDataStruct *pData_info ,BOOLEAN *flagExtern)
{
    BOOLEAN ret = False;

    if (pData_info->ucCMD == DLCMD_DOWNLOAD_EXT) 
    {
        /*下载首发信息扩展帧*/
        *flagExtern = TRUE;
        ret = first_extern_frame_handle(pData_info);
    } 
    else 
    {
        /*下载首发信息帧*/
        *flagExtern = FALSE;
        ret = first_frame_handle(pData_info);
    }

    return ret;
}


BYTE GetUpgradeFileFlag(void)
{
    return ucFileType;
}


//升级文件处理

Static void UpdatefileSave(T_UpdateDataStruct *pData_info)
{
    rt_spi_flash_device_t dev_w25q;
    const int app_write_start_pos = NORFLASH_APP_START;
    int data_len = pData_info->usLen;
    int calc_len;
    INT32U u32ErraseFlashAddr = 0;
    BYTE * file_data = pData_info->aucBuff;     // 每一帧中的数据

    if(pData_info->usFN2 == 1)
    {
        crc_ctx = 0 ^ 0xFFFFFFFF;
        if (!flagExtern) {
            data_len -= READ_BYTES;
            rt_memcpy_s(&app_head, sizeof(file_head_t), pData_info->aucBuff, sizeof(file_head_t));
            file_data += READ_BYTES;
            pData_info->usLen = data_len;
        } else {
            data_len -= 2*READ_BYTES;
            rt_memcpy_s(&extern_app_head, sizeof(extern_file_head_t), pData_info->aucBuff, sizeof(extern_file_head_t));
            file_data += 2*READ_BYTES;
            pData_info->usLen = data_len;
        }
    }

    dev_w25q = (rt_spi_flash_device_t)rt_device_find("GD25Q128");
    RETURN_IF_FAIL(dev_w25q != RT_NULL);
    u32ErraseFlashAddr = (s_downloadHandler.download_mgr.file_info.write_pos + pData_info->usLen)/EARSE_FAN_BYTES;
    write_to_norflash((sfud_flash *)dev_w25q->user_data, \
                    app_write_start_pos + s_downloadHandler.download_mgr.file_info.write_pos, \
                    pData_info->usLen, \
                    file_data,app_write_start_pos+u32ErraseFlashAddr*EARSE_FAN_BYTES);
    rt_kprintf("write frame %d addr offset %d write len %d \r\n", pData_info->usFN2, \
                    app_write_start_pos + s_downloadHandler.download_mgr.file_info.write_pos, \
                    pData_info->usLen);

    // 累加计算升级包校验和
    accumulated_calc_crc(calc_len, data_len, &crc_ctx, file_data);
    break_write_pos = s_downloadHandler.download_mgr.file_info.write_pos;
    break_frame_cnt = s_downloadHandler.download_mgr.file_info.frame_cnt;

    return;
}


//升级BMD文件下载完毕的处理

Static BOOLEAN UpdateBMSDownlaodOver(T_UpdateDataStruct *pData_info)
{
    if (strcmp(app_head.file_name, "ZXESM_R321_BMS_MCU_APP.bin") == 0 && \
        strcmp(app_head.dev_type,  "BMS") == 0 && \
        (app_head.check_sum == crc_ctx)) 
    {
        rt_kprintf("check is correct\n");
        s_downloadHandler.download_mgr.file_info.total_leng -= READ_BYTES;
        update_file_crc = CRC_Cal_NOR(NORFLASH_APP_START, s_downloadHandler.download_mgr.file_info.total_leng);
#ifndef KW_CHECK
        rt_memcpy_s(&s_downloadHandler.download_mgr.file_info.fileName, FILE_NAME_LEN, ucFileNameBin, FILE_NAME_LEN);
#endif
        if(GetClientUpdateFlag())
        {
            write_download_tmpInfo(&s_downloadHandler.download_mgr.file_info, FLAG_NOR_IAP);  // 完成标记,文件传输完成
        }
        else
        {
            write_download_tmpInfo(&s_downloadHandler.download_mgr.file_info, FLAG_NOR_TOOL_IAP);  // 完成标记,文件传输完成
        }
        s_bFileTransComplete = TRUE;
        // reset_download_manager();
        set_update_file(&s_downloadHandler.download_mgr.file_info);
    }
    else
    {
        crc_ctx = 0 ^ 0xFFFFFFFF;
        s_bFileTransComplete = FALSE;
        pData_info->ucRtn = DOWNLOAD_RTN_CMD_ERR;
        return False;
    }

    return True;
}


//升级BDU文件下载完毕的处理

Static BOOLEAN UpdateBDUDownlaodOver(T_UpdateDataStruct *pData_info)
{
    if (strcmp(app_head.file_name, "ZXESM_R321_BDU_MCU_APP.bin") == 0 && \
            strcmp(app_head.dev_type,  "BDU") == 0 && \
            (app_head.check_sum == crc_ctx)) 
    {
        s_downloadHandler.download_mgr.file_info.total_leng -= READ_BYTES;
        update_file_crc = CRC_Cal_NOR(NORFLASH_APP_START, s_downloadHandler.download_mgr.file_info.total_leng);
        #ifndef KW_CHECK
        rt_memcpy(&s_downloadHandler.download_mgr.file_info.fileName, ucFileBDUNameBin, FILE_NAME_LEN);
        #endif
        write_download_tmpInfo(&s_downloadHandler.download_mgr.file_info, FLAG_OK);  // 完成标记,文件传输完成
        s_bFileTransComplete = TRUE;
        // reset_download_manager();
        set_update_file(&s_downloadHandler.download_mgr.file_info);
    }
    else
    {
        crc_ctx = 0 ^ 0xFFFFFFFF;
        s_bFileTransComplete = FALSE;
        pData_info->ucRtn = DOWNLOAD_RTN_CMD_ERR;
        return False;
    }

    return True;
}


//错峰文件下载处理

static void PeakShiftFileDownload(T_UpdateDataStruct *pData_info)
{
    rt_spi_flash_device_t dev_w25q;
    const int app_write_start_pos = NORFLASH_APP_START;
    INT32U u32ErraseFlashAddr = 0;
    BYTE * file_data = pData_info->aucBuff;     // 每一帧中的数据

    rt_kprintf("\nstart download peak shfit file:");
    dev_w25q = (rt_spi_flash_device_t)rt_device_find("GD25Q128");
    RETURN_IF_FAIL(dev_w25q != RT_NULL);
    u32ErraseFlashAddr = (s_downloadHandler.download_mgr.file_info.write_pos + pData_info->usLen)/EARSE_FAN_BYTES;
    write_to_norflash((sfud_flash *)dev_w25q->user_data, \
                    app_write_start_pos + s_downloadHandler.download_mgr.file_info.write_pos, \
                    pData_info->usLen, \
                    file_data,app_write_start_pos+u32ErraseFlashAddr*EARSE_FAN_BYTES);
    rt_kprintf("write frame %d addr offset %d write len %d \r\n", pData_info->usFN2, \
                    app_write_start_pos + s_downloadHandler.download_mgr.file_info.write_pos, \
                    pData_info->usLen);

    // 累加计算升级包校验和
    if (s_downloadHandler.download_mgr.file_info.frame_cnt + 1 == \
                    s_downloadHandler.download_mgr.file_info.total_frames - 1) 
    {
        rt_kprintf("file_data:%x  %x\n",file_data[pData_info->usLen - 2],file_data[pData_info->usLen -1]);
        crc16_calc(&peak_crc16, file_data, pData_info->usLen - 2);
    }
    else
    {
        crc16_calc(&peak_crc16, file_data, pData_info->usLen);
    }

    return;
}



static BOOLEAN PeakShiftFileOver(T_UpdateDataStruct *pData_info)
{
    WORD wCRC = 0;
    rt_kprintf("peak shift file download end\n");
    s_downloadHandler.download_mgr.file_info.total_frames = 0;
    s_downloadHandler.download_mgr.file_info.total_leng = 0;
    wCRC = GetInt16Data(&pData_info->aucBuff[pData_info->usLen-2]);
    if(wCRC != peak_crc16)
    {
        peak_crc16 = 0x0000;
        write_download_tmpInfo(&s_downloadHandler.download_mgr.file_info, FLAG_OK);  // 完成标记,文件传输完成
        rt_kprintf("peak shift file crc check failure:wCRC = %x,peak_crc16 = %x\n",wCRC,peak_crc16);
        pData_info->ucRtn = DOWNLOAD_RTN_CMD_ERR;
        return False;
    }

    peak_crc16 = 0x0000;
    write_download_tmpInfo(&s_downloadHandler.download_mgr.file_info, FLAG_OK);  // 完成标记,文件传输完成
    ucFileStatus[PEAK_SHIFT_FILE] = FILE_TRANSLATING;
    ucCheckResult[PEAK_SHIFT_FILE] = TRANS_PROGRESS_0;
    s_bPeakFileTransComplete = True;

    return True;
}



BOOLEAN PeakDataCheckAndSave(void)
{
    int i = 0;
    int len = 0;
    int fileoffset = 0; 
    rt_spi_flash_device_t dev_w25q;
    const int app_write_start_pos = NORFLASH_APP_START;
    T_PeakShiftPara tPeakShiftPara;
    T_TemplateInfo tPeakshiftTemplate;
    BYTE ucBuff[250] = {0};
    BYTE ucTemplateNum = 0;

    rt_memset_s(&tPeakShiftPara, sizeof(T_PeakShiftPara), 0x00, sizeof(T_PeakShiftPara));
    rt_memset_s(&tPeakshiftTemplate, sizeof(T_TemplateInfo), 0x00, sizeof(T_TemplateInfo));

    GetPeakShiftPara(&tPeakShiftPara);
    
    // 读取文件内容
    dev_w25q = (rt_spi_flash_device_t)rt_device_find("GD25Q128");
    if(dev_w25q == RT_NULL)
    {
        return False;
    }
    else if(sfud_read((sfud_flash *)dev_w25q->user_data, app_write_start_pos, TEMPLATE_DATA_1, ucBuff)) // 读取模板之前的数据
    {
        return False;
    }
    ucTemplateNum = ucBuff[TEMPLATE_NUM];
    tPeakShiftPara.ucElectricPricePattern = ucBuff[ELECTRIC_PRICE_MODE];
    tPeakShiftPara.ucHolidayPatternTempNum = ucBuff[HOLIDAY_TEMPLATE];

    rt_memcpy_s((BYTE*)tPeakShiftPara.atHolidayPatternDate, sizeof(tPeakShiftPara.atHolidayPatternDate), 
               (BYTE *)(&ucBuff[HOLIDAY_CONFIG_DATE]), sizeof(tPeakShiftPara.atHolidayPatternDate));

    if(tPeakShiftPara.ucElectricPricePattern == 0)    // 日模式
    {
        tPeakShiftPara.ucDayPatterTempNum = ucBuff[ELECTRIC_MODE_TEMPLATE];
    }
    else if(tPeakShiftPara.ucElectricPricePattern == 1)  // 周模式
    {
        rt_memcpy_s((BYTE*)tPeakShiftPara.aucWeekPatternTempNum, sizeof(tPeakShiftPara.aucWeekPatternTempNum), 
                   (BYTE *)(&ucBuff[ELECTRIC_MODE_TEMPLATE]), sizeof(tPeakShiftPara.aucWeekPatternTempNum));
    }
    else if(tPeakShiftPara.ucElectricPricePattern == 2)   // 月模式
    {
        rt_memcpy_s((BYTE*)tPeakShiftPara.aucMonthPatternTempNum, sizeof(tPeakShiftPara.aucMonthPatternTempNum), 
                    (BYTE *)(&ucBuff[ELECTRIC_MODE_TEMPLATE]), sizeof(tPeakShiftPara.aucMonthPatternTempNum));
    }
    else
    {
        // 电价模式错误
        ucFileStatus[PEAK_SHIFT_FILE] = FILE_TARANSLA_FAILURE;
        ucCheckResult[PEAK_SHIFT_FILE] = TRANS_PROGRESS_100;
        return False;
    }

    // 错峰模板解析
    tPeakshiftTemplate.ucDayTemplateNum = ucTemplateNum;
    for(i = 0; i < ucTemplateNum; i++)
    {
        sfud_read((sfud_flash *)dev_w25q->user_data, app_write_start_pos + TEMPLATE_DATA_1 + ONE_TEMPLATE_LENGTH * i, ONE_TEMPLATE_LENGTH, ucBuff);
        if(!ParseTemplateData(&tPeakshiftTemplate.uTemplateInfo.tWeekTemp.atDateTemp[i], &ucBuff[1], ucBuff[0]))
        {
            // 模板时间格式错误
            ucFileStatus[PEAK_SHIFT_FILE] = FILE_TARANSLA_FAILURE;
            ucCheckResult[PEAK_SHIFT_FILE] = TRANS_PROGRESS_100;
            return False;
        }
    }

    // 错峰参数处理
    if(SetPeakShiftParaFromNet(&tPeakShiftPara, &tPeakshiftTemplate))
    {
        // 保存错峰参数到文件
        deleteFile(NET_PEAKTEMPLATE);
        while(FILE_TEMPLATE_LENGTH - fileoffset)
        {
            len = (FILE_TEMPLATE_LENGTH - fileoffset > 240)?240:(FILE_TEMPLATE_LENGTH - fileoffset);
            sfud_read((sfud_flash *)dev_w25q->user_data, app_write_start_pos + fileoffset, len, ucBuff);
            SaveNetPeakShiftTemplate(NET_PEAKTEMPLATE, fileoffset, ucBuff, (WORD)len);
            fileoffset += len;
        }
        rt_memcpy_s(&s_tPeakShiftPara, sizeof(T_PeakShiftPara), &tPeakShiftPara, sizeof(T_PeakShiftPara));
        rt_memcpy_s(&s_tPeakshiftTemplate, sizeof(T_TemplateInfo), &tPeakshiftTemplate, sizeof(T_TemplateInfo));
        ucFileStatus[PEAK_SHIFT_FILE] = FILE_TRANSLAT_SUCCESS;
        ucCheckResult[PEAK_SHIFT_FILE] = TRANS_PROGRESS_100;
        return True;
    }

    ucFileStatus[PEAK_SHIFT_FILE] = FILE_TARANSLA_FAILURE;
    ucCheckResult[PEAK_SHIFT_FILE] = TRANS_PROGRESS_100;
    return False;
}



// 错峰文件传输完成
BOOLEAN GetPeakFileTransFlag(void)
{
    return s_bPeakFileTransComplete;
}

// 设置错峰文件传输完成标志
BOOLEAN SetPeakFileTransFlag(BOOLEAN Flag)
{
    s_bPeakFileTransComplete = Flag;
    return True;
}



// 解析模板数据
static BOOLEAN ParseTemplateData(T_DateTemplateStruct* ptDateTemp, BYTE* p, BYTE ucNum)
{
    BYTE i;
    UINT32 ulTempInvert = 0;
    T_DurationStruct tTempDuration;

    if (p == NULL || ptDateTemp == NULL || ucNum > TEMPLATE_DURATION_MAX)
    {
        return False;
    }

    ptDateTemp->ucDurationNum = ucNum;

    for (i = 0; i < ucNum; i++)
    {  
        ulTempInvert = GetInt32Data(&p[i * 4]);
        tTempDuration.FromHour = (ulTempInvert >> 27) & 0x1F;
        tTempDuration.FromMinute = (ulTempInvert >> 21) & 0x3F;
        tTempDuration.ToHour = (ulTempInvert >> 16) & 0x1F;
        tTempDuration.ToMinute = (ulTempInvert >> 10) & 0x3F;
        tTempDuration.PriceClass = ulTempInvert & 0x03FF;

        if (tTempDuration.FromHour > 23 || tTempDuration.FromMinute > 59 ||
            tTempDuration.ToHour > 23 || tTempDuration.ToMinute > 59)
        {
            return False;
        }

        ptDateTemp->atDura[i].FromHour = tTempDuration.FromHour;
        ptDateTemp->atDura[i].FromMinute = tTempDuration.FromMinute;
        ptDateTemp->atDura[i].ToHour = tTempDuration.ToHour;
        ptDateTemp->atDura[i].ToMinute = tTempDuration.ToMinute;
        ptDateTemp->atDura[i].PriceClass = tTempDuration.PriceClass;
    }
    return True;
}



static void InitRdlContext(T_RdlContext *ctx)
{
    RT_ASSERT(ctx != NULL);
    rt_memset_s(ctx, sizeof(T_RdlContext), 0x00, sizeof(T_RdlContext));
}

static int RdlTransferFile(T_RdlContext *ctx, T_UpdateDataStruct *pData)
{
    RT_ASSERT(ctx != NULL && pData != NULL);

    uint16_t offset = 0;

    rt_mutex_take(s_ptMutexFile, RT_WAITING_FOREVER);
    int fd = open(ctx->filename, O_RDONLY);
    if (fd < 0)
    {
        pData->ucRtn = 2;
        rt_mutex_release(s_ptMutexFile);
        LOG_W("failed to open file: %s", ctx->filename);
        return -1;
    }

    off_t readLseek = lseek(fd, ctx->lastReadLen, SEEK_SET); 
    if (readLseek < 0)
    {
        close(fd);
        rt_mutex_release(s_ptMutexFile);
        LOG_W("failed to seek file: %s", ctx->filename);
        return -1;
    }

    uint32_t endLen = ctx->filesize % 1024;
    int readLen = rt_read_s(fd,
                            &pData->aucBuff[offset],
                            sizeof(pData->aucBuff) - offset,
                            (endLen != 0) ? endLen : 1024);

    if (readLen < 0)
    {
        pData->ucRtn = 2;
        close(fd);
        rt_mutex_release(s_ptMutexFile);
        LOG_W("failed to read file: %s", ctx->filename);
        return -1;
    }

    ctx->lastReadLen += readLen;
    offset += readLen;
    close(fd);
    rt_mutex_release(s_ptMutexFile);

    pData->usLen = offset;
    pData->usFN1 = 0x0000;

    LOG_HEX(LOG_TAG_RDL".data", 16, pData->aucBuff, pData->usLen);
    return 0;
}



Static void ULFileProcess(T_UpdateFrameStruct *pData)
{
    BYTE cid = pData->ucCmd;

    switch (cid)
    {
        case ULCMD_GET_FILE_LIST:
            // 文件列表处理
            ULFileListProcess(&pData->dataFrame);
            break;
    
        case ULCMD_GET_FILE:
            // 上传文件处理
            ULFileDataProcess(&pData->dataFrame);
            break;
    }
}


//上传文件列表处理


Static void ULFileListProcess(T_UpdateDataStruct *pData)
{
    static T_RdlContext ctx = {0, };

    LOG_I("ULFileListProcess");

    if (pData->usFN2 == 0)
    {
        InitRdlContext(&ctx);

        GetFileListInfoSpec();
        struct stat st;
        if (stat(FILELIST, &st) == 0)
        {
            ctx.filesize = st.st_size;
        }

        ctx.totalFrameNum = (ctx.filesize + 1024) / 1024;
        rt_strncpy_s(&ctx.filename[0], sizeof(ctx.filename), FILELIST, sizeof(FILELIST));

        pData->usLen = 2;
        pData->aucBuff[0] = (ctx.totalFrameNum >> 8) & 0xFF;
        pData->aucBuff[1] = ctx.totalFrameNum & 0xFF;
        pData->usFN1 = 0x0000;
        pData->usFN2 = 0x0000;
        LOG_I("Frame %d: Len = %d, TotalFrameNum = %d", pData->usFN2, pData->usLen, TotalFrameNum);
        LOG_HEX(LOG_TAG_RDL".data", 16, pData->aucBuff, pData->usLen);
    }
    else
    {
        RdlTransferFile(&ctx, pData);
    }
}


//上传文件数据处理


Static void ULFileDataProcess(T_UpdateDataStruct *pData)
{
    WORD wOffset = 0;
    static char filename[40] = {0};
    static T_RdlContext ctx = {0, };

    if (pData->usFN2 == 0)
    {
        rt_memset_s(filename, sizeof(filename), 0x00, sizeof(filename));
        InitRdlContext(&ctx);

        uint16_t requestFilenameLen = (uint16_t)GetInt16Data(&pData->aucBuff[0]);
        LOG_I("request filename len: %d", requestFilenameLen);
        if (requestFilenameLen > 30)
        {
            pData->ucRtn = 2;
            LOG_W("request filename len too long!");
            return;
        }
        rt_memcpy_s(filename, sizeof(filename), &pData->aucBuff[2], requestFilenameLen);
        filename[39] = '\0';

        if (strchr(filename, '_') != NULL)
        {
            rt_snprintf_s(&ctx.filename[0], sizeof(ctx.filename), "/BACKUP/%s", filename);
        }
        else
        {
            rt_snprintf_s(&ctx.filename[0], sizeof(ctx.filename), "/%s", filename);
        }
        LOG_I("filename: %s", ctx.filename);

        struct stat st;
        if (stat(ctx.filename, &st) == 0)
        {
            ctx.filesize = st.st_size;
        }
        LOG_I("file size: %d", ctx.filesize);

        ctx.totalFrameNum = (ctx.filesize + 1024) / 1024;

        PutInt16ToBuff(&pData->aucBuff[wOffset], ctx.totalFrameNum);
        wOffset += 2;

        rt_memcpy_s(&pData->aucBuff[wOffset], requestFilenameLen, filename, requestFilenameLen);
        wOffset += requestFilenameLen;

        PutInt32ToBuff(&pData->aucBuff[wOffset], ctx.filesize);
        wOffset += 4;

        // 文件时间
        rt_memset_s(&pData->aucBuff[wOffset], FILE_TIME_LENGTH, 0x00, FILE_TIME_LENGTH);
        wOffset += FILE_TIME_LENGTH;
        // 文件校验码
        pData->aucBuff[wOffset++] = 0x00;
        pData->aucBuff[wOffset++] = 0x00;
        pData->aucBuff[wOffset++] = 0x00;
        pData->aucBuff[wOffset++] = 0x00;

        pData->usLen = wOffset;

        pData->usFN1 = 0x0000;
        pData->usFN2 = 0x0000;

        LOG_I("Frame %d: Len = %d", pData->usFN2, pData->usLen);
        LOG_HEX(LOG_TAG_RDL".data", 16, pData->aucBuff, pData->usLen);
    }
    else
    {
        RdlTransferFile(&ctx, pData);
    }
}

