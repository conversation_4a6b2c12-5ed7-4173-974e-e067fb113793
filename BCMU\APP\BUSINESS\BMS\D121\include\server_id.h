
#ifndef _SERVER_ID_H_  
#define _SERVER_ID_H_  

#include "utils_server.h"

#ifdef __cplusplus
extern "C" {
#endif
#ifndef SERVER_PRIO_HIGH 
#define SERVER_PRIO_HIGH     1
#endif

#define SAMPLE_SERVER_ID            (DATA_SERVER + 1)
#define LED_SERVER_ID               (CONTROL_SERVER + 1) 
#define CANCOMM_SERVER_ID           (NORTH_SERVER + 1) 
#define COMM_SERVER_ID              (NORTH_SERVER + 2) 
#define UARTCOMM_SERVER_ID          (NORTH_SERVER + 3) 
#define NET1104_SERVER_ID           (NORTH_SERVER + 4) 
#define BACKUP_SERVER_ID            (NORTH_SERVER + 5)
#define HISDATA_SERVER_ID           (SYSTEM_SERVER + 1)
#define BDU_RECORD_SERVER_ID        (SYSTEM_SERVER + 2) 
#define BATTERY_MANAGE_SERVER_ID    (BUSINESS_SERVER + 1)
#define WIRELESS_SERVER_ID          (BUSINESS_SERVER + 2)  
#define GSENSOR_SERVER_ID           (BUSINESS_SERVER + 3)  
#define ALARM_SERVER_ID             (ALARM_SERVER + 1)
#define ALARM_TRAP_SERVER_ID        (ALARM_SERVER + 2)
#define BDUCOMM_SERVER_ID           (SOUTH_SERVER + 1)


       
#ifdef __cplusplus
}  
#endif

#endif  // _SERVER_ID_H_  
