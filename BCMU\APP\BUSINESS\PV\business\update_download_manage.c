#include <string.h>
#include <rtthread.h>
#include <stdlib.h>
#include <fal.h>
#include <fcntl.h>
#include "update_download_manage.h"
#include "msg.h"
#include "device_type.h"
#include "sps.h"
#include "cmd.h"
#include "protocol_layer.h"
#include "utils_data_transmission.h"
#include "utils_data_type_conversion.h"
#include "download.h"
#include "msg_id.h"
#include "update_manage.h"
#include "utils_string.h"
#include "partition_def.h"
#include "his_data.h"
#include "softbus.h"
#include "signal_led.h"
// #include "backup_manage.h"
#include "his_record.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "utils_rtthread_security_func.h"
// #include "concurrent_update_master_handle.h"
// #include "concurrent_slave_update_handle.h"
#include "utils_heart_beat.h"
#include "thread_id.h"
#include "reset.h"

update_file_manage_t s_download_trsdata_ctr_inf;
T_FileUploadStruct s_upload_trsdata_ctr_inf;
unsigned short g_extend_file_crc = 0;  // 扩展帧传输下来的crc


static download_trig_ctr_inf_t s_download_trig_ctr_inf;
char   file_name_tmp[UPDATE_FILE_EXTEND_NAME_LEN];      ///< 文件名称
rt_timer_t         trigTimer;
rt_timer_t  g_update_timer = NULL;
unsigned char g_other_file_update_flag = OTHER_NO_UPDATE;
unsigned char g_first_frame_flag = FALSE;
int g_south_update_dev = INVALID_DEV_TYPE;
const char g_key_update_file_name[][UPDATE_FILE_EXTEND_NAME_LEN] = {{"client.key"}, {"client_other.key"}};
const char g_cert_update_file_name[][UPDATE_FILE_EXTEND_NAME_LEN] = {{"ca.pem"},{"client.pem"},{"ca_other.pem"},{"client_other.pem"},{"FacDefaultPara.bin"}};//证书更新
const char g_self_update_file_name[][UPDATE_FILE_EXTEND_NAME_LEN] = {{"pv_app.bin"},{"bmu_app.bin"}, {"pv_diff.bin"}};//升级包文件名,确定后再修改
const char g_south_update_file_name[][UPDATE_FILE_EXTEND_NAME_LEN] = {
                                                                                    {"PV_INVERTER_MASTER.bin"},
                                                                                    {"PV_INVERTER_SLAVE.bin"},
                                                                                    {"PV_INVERTER_CPLD.bin"},
                                                                                                
                                                                                };//升级包文件名,确定后再修改
const char g_para_update_file_name[][UPDATE_FILE_EXTEND_NAME_LEN] = {{"alarm_para.bin"}, {"num_para.bin"}, {"string_para.bin"},};
#define SOUTH_UPDATE_FILE_NAME_NUM      sizeof(g_south_update_file_name)/sizeof(g_south_update_file_name[0])
#define SELF_UPDATE_FILE_NAME_NUM       sizeof(g_self_update_file_name)/sizeof(g_self_update_file_name[0])
#define CERT_UPDATE_FILE_NAME_NUM       sizeof(g_cert_update_file_name)/sizeof(g_cert_update_file_name[0])
#define PARA_UPDATE_FILE_NAME_NUM       sizeof(g_para_update_file_name)/sizeof(g_para_update_file_name[0])
#define KEY_UPDATE_FILE_NAME_NUM        sizeof(g_key_update_file_name)/sizeof(g_key_update_file_name[0])
#define DIFF_TAG "diff"
#define WIFI_FILE_NAME  "fw_ap6212.bin"

static unsigned char trig_frame_ack[] = {
    0x30,0x31,0x34,0x30,0x45,0x45,0x35,
    0x36,0x35,0x42,0x41,0x36,0x41,0x42
};

// {"ca.pem"},{"client.key"},{"client.pem"} 按这样排列，如果和为3，说明三个证书传输完成
static char g_cert_flags[MQTT_SERVER_NUM][CERT_FILE_NUM] = {0};
static char* g_cert_file_name[MQTT_SERVER_NUM][CERT_FILE_NUM] = 
{
    {"ca.pem", "client.key", "client.pem"},
    {"ca_other.pem", "client_other.key", "client_other.pem"}
};


static int parse_update_data(void* dev_inst, void *cmd_buf);
static int pack_update_data(void* dev_inst, void *cmd_buf);
static int parse_update_trig(void* dev_inst, void *cmd_buf);
static int pack_update_trig(void* dev_inst, void *cmd_buf);
static int parse_ver_cmp(void* dev_inst, void *cmd_buf);
static int pack_ver_cmp(void* dev_inst, void *cmd_buf);
static int parse_update_progress(void* dev_inst, void *cmd_buf);
static int pack_update_progress(void* dev_inst, void *cmd_buf);
static int parse_stop_update(void* dev_inst, void *cmd_buf);
static int pack_stop_update(void* dev_inst, void *cmd_buf);
static int parse_get_flag(void* dev_inst, void *cmd_buf);
static int pack_get_flag(void* dev_inst, void *cmd_buf);
static int parse_get_single_frame_max_length(void* dev_inst, void *cmd_buf);
static int pack_get_single_frame_max_length(void* dev_inst, void *cmd_buf);
static int parse_set_single_frame_max_length(void* dev_inst, void *cmd_buf);
static int pack_set_single_frame_max_length(void* dev_inst, void *cmd_buf);
static int parse_first_frame(cmd_buf_t* tran_cmd_buf,unsigned short frm_no);
static int parse_data_frame(void* dev_inst, cmd_buf_t *cmd_buf, cmd_buf_t* tran_cmd_buf, unsigned short frm_no);
static int update_file_name_cmp(const char* file_name );
static int judge_wifi_update(update_file_manage_t* file_manage);

static int parse_filedata_first_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no);


static int pack_filelist_first_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no);
static int pack_filelist_other_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no);

static int pack_filedata_first_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no);
int pack_filedata_other_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no);
int update_type_handle(part_data_t* part_data, update_file_manage_t* trsdata_ctr_inf, cmd_buf_t* tran_cmd_buf);

static cmd_handle_register_t s_download_update_cmd_handle[MAX_DOWNLOAD_UPDATE_SLAVE_HANDLE_NUM] = {
    {DEV_DAC_NORTH_DOWNLOAD, DOWNLOAD_DATA_DOWNLOAD,         CMD_TYPE_NO_POLL, parse_update_data, pack_update_data},
    {DEV_DAC_NORTH_DOWNLOAD, DOWNLOAD_DATA_DOWNLOAD_EXTEND,  CMD_TYPE_NO_POLL, parse_update_data, pack_update_data},
    {DEV_DAC_NORTH_DOWNLOAD, DOWNLOAD_VERSION_CMP,           CMD_TYPE_NO_POLL, parse_ver_cmp, pack_ver_cmp },//获取原来的版本信息
    {DEV_DAC_NORTH_DOWNLOAD, DOWNLOAD_STOP_UPDATE,           CMD_TYPE_NO_POLL, parse_stop_update , pack_stop_update},
    {DEV_DAC_NORTH_DOWNLOAD, DOWNLOAD_GET_FLAG,              CMD_TYPE_NO_POLL, parse_get_flag ,pack_get_flag },
    {DEV_DAC_NORTH_DOWNLOAD, DOWNLOAD_GET_UPDATE_PROG,       CMD_TYPE_NO_POLL, parse_update_progress , pack_update_progress},
    {DEV_DAC_NORTH_DOWNLOAD, DOWNLOAD_DATA_TRIG,             CMD_TYPE_NO_POLL, parse_update_trig, pack_update_trig},
    {DEV_DAC_NORTH_DOWNLOAD, DOWNLOAD_GET_FRAME_MAX_LEN,     CMD_TYPE_NO_POLL, parse_get_single_frame_max_length, pack_get_single_frame_max_length},
    {DEV_DAC_NORTH_DOWNLOAD, DOWNLOAD_SET_FRAME_MAX_LEN,     CMD_TYPE_NO_POLL, parse_set_single_frame_max_length, pack_set_single_frame_max_length},
    {DEV_DAC_NORTH_DOWNLOAD, UPLOAD_FILE_LIST,               CMD_TYPE_NO_POLL, parse_upload_file_list, pack_upload_file_list},
    {DEV_DAC_NORTH_DOWNLOAD, UPLOAD_FILE_DATA,               CMD_TYPE_NO_POLL, parse_upload_file_data, pack_upload_file_data},
    {0} // 结束符
    // {DEV_DAC_NORTH_DOWNLOAD, DOWNLOAD_EXTERN_TRIG,           CMD_TYPE_NO_POLL, parse_extern_trig_data, pack_extern_trig_data},
    // {DEV_DAC_NORTH_DOWNLOAD, DOWNLOAD_CONCUR_DATA,           CMD_TYPE_NO_POLL, parse_concur_update_data, pack_concur_update_data},
    // {DEV_DAC_NORTH_DOWNLOAD, DOWNLOAD_CONCUR_CONFIRM_DATA,   CMD_TYPE_NO_POLL, parse_concur_confirm_update_data, pack_concur_confirm_update_data},
    // {DEV_DAC_NORTH_DOWNLOAD, DOWNLOAD_CONCUR_CONFIRM_UPDATE, CMD_TYPE_NO_POLL, parse_concur_confirm_update, pack_concur_confirm_update},
};


int register_download_update_slave_handle(cmd_handle_register_t* download_update_slave_handle, int cmd_num)
{
    if (cmd_num <= 0 || download_update_slave_handle == NULL) {
        LOG_E("Invalid input: cmd_num is %d or download_update_slave_handle is NULL", cmd_num);
        return FAILURE;
    }

    int exist_cmd_num = 0;
    for (; exist_cmd_num < MAX_DOWNLOAD_UPDATE_SLAVE_HANDLE_NUM; exist_cmd_num++)
    {
        if (s_download_update_cmd_handle[exist_cmd_num].dev_id != DEV_DAC_NORTH_DOWNLOAD)
        {
            break;
        }
    }

    if ((exist_cmd_num + cmd_num) > MAX_DOWNLOAD_UPDATE_SLAVE_HANDLE_NUM)
    {
        LOG_E("reg download update cmd fail|exist_num:%d, cmd:%d", exist_cmd_num, cmd_num);
        return FAILURE;
    }

    // 使用标准库函数进行内存拷贝
    rt_memcpy_s(&s_download_update_cmd_handle[exist_cmd_num], 
                (MAX_DOWNLOAD_UPDATE_SLAVE_HANDLE_NUM - exist_cmd_num) * sizeof(cmd_handle_register_t), 
                download_update_slave_handle, 
                cmd_num * sizeof(cmd_handle_register_t));
    
    return SUCCESSFUL;
}


const char (*get_south_update_file_names(int *length))[UPDATE_FILE_EXTEND_NAME_LEN]
{
    *length = sizeof(g_south_update_file_name) / sizeof(g_south_update_file_name[0]);
    return g_south_update_file_name;
}

/* 解析上传文件列表帧 首发帧和其他帧DATA均为空 */
int parse_upload_file_list(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = NULL;
    T_FileUploadStruct* trsdata_ctr_inf = NULL;
    download_cmd_head_t* proto_head = NULL;
    trsdata_ctr_inf = &s_upload_trsdata_ctr_inf;

    //入参校验
    if ( cmd_buf == NULL || ((cmd_buf_t*)cmd_buf)->buf == NULL)
    {
        return FAILURE;
    }

    tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    //判断协议层解析数据是否正确
    if (tran_cmd_buf->rtn != DOWNLOAD_FRAME_CORRECT)
    {
        return FAILURE;
    }
    proto_head = (download_cmd_head_t*)tran_cmd_buf->cmd->req_head;
    trsdata_ctr_inf->cur_frame_no = proto_head->fn2;
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;

    return SUCCESSFUL;
}

/* 打包上传文件列表帧 打包首发帧和其他帧 */
int pack_upload_file_list(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    unsigned short frm_no = 0;
    download_cmd_head_t* proto_head = NULL;

    // 入参校验
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL)
    {
        return FAILURE;
    }

    //判断协议层解析数据是否正确
    if (tran_cmd_buf->rtn != DOWNLOAD_FRAME_CORRECT)
    {
        return FAILURE;
    }
    
    proto_head = (download_cmd_head_t*)tran_cmd_buf->cmd->req_head;
    frm_no = proto_head->fn2;
        
    if (frm_no == 0)
    {
        trig_end();
        pack_filelist_first_frame(tran_cmd_buf, frm_no);
    }
    else
    {
        pack_filelist_other_frame(tran_cmd_buf, frm_no);
    }
    
    if (s_upload_trsdata_ctr_inf.tFileList.wListTotalFrame ==  proto_head->fn2 &&
        s_upload_trsdata_ctr_inf.tFileList.wListTotalFrame != 0)
    {
        rt_memset_s(&s_upload_trsdata_ctr_inf, sizeof(T_FileUploadStruct), 0, sizeof(T_FileUploadStruct));
    }

    return SUCCESSFUL;
}

/* 打包上传文件列表首发帧 */
static int pack_filelist_first_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no)
{
    int i = 0;
    int offset = 0;
    int totalByte = 0;

    //获取文件列表
    get_record_file_list((char**)s_upload_trsdata_ctr_inf.tFileList.pucListName, (char*)s_upload_trsdata_ctr_inf.tFileList.aucListNameLen, 
                    &s_upload_trsdata_ctr_inf.tFileList.ucListTotalNum, UPLOAD_FILE_NUM);

    //计算总帧数
    for (i=0; i<s_upload_trsdata_ctr_inf.tFileList.ucListTotalNum; i++)
    {
        totalByte  += rt_strnlen_s(s_upload_trsdata_ctr_inf.tFileList.pucListName[i], s_upload_trsdata_ctr_inf.tFileList.aucListNameLen[i]);
    }
    s_upload_trsdata_ctr_inf.tFileList.wListTotalFrame = (totalByte + UPLOAD_DATA_FRAME_LEN - 1) / UPLOAD_DATA_FRAME_LEN;

    put_int16_to_buff(&tran_cmd_buf->buf[offset], s_upload_trsdata_ctr_inf.tFileList.wListTotalFrame);

    offset += 2;
    tran_cmd_buf->data_len = offset;
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;

    return SUCCESSFUL;
}

/* 打包上传文件列表其他帧 */
static int pack_filelist_other_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no)
{
    int i = 0;
    int offset = 0;
    //获取文件列表
    for (i=0; i<s_upload_trsdata_ctr_inf.tFileList.ucListTotalNum; i++)
    {
        put_int16_to_buff(&tran_cmd_buf->buf[offset], s_upload_trsdata_ctr_inf.tFileList.aucListNameLen[i]);
        offset += 2;

        memcpy_s(&tran_cmd_buf->buf[offset], s_upload_trsdata_ctr_inf.tFileList.aucListNameLen[i], 
            s_upload_trsdata_ctr_inf.tFileList.pucListName[i], s_upload_trsdata_ctr_inf.tFileList.aucListNameLen[i]);
        offset += s_upload_trsdata_ctr_inf.tFileList.aucListNameLen[i];
    }
    
    tran_cmd_buf->data_len = offset;
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;

    return SUCCESSFUL;
}

/* 解析上传文件数据帧 解析首发帧，其他帧DATA为空 */
int parse_upload_file_data(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = NULL;
    T_FileUploadStruct* trsdata_ctr_inf = NULL;
    download_cmd_head_t* proto_head = NULL;
    trsdata_ctr_inf = &s_upload_trsdata_ctr_inf;

    // 入参校验
    if ( cmd_buf == NULL || ((cmd_buf_t*)cmd_buf)->buf == NULL)
    {
        return FAILURE;
    }

    tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    //判断协议层解析数据是否正确
    if (tran_cmd_buf->rtn != DOWNLOAD_FRAME_CORRECT)
    {
        return FAILURE;
    }
    proto_head = (download_cmd_head_t*)tran_cmd_buf->cmd->req_head;
    trsdata_ctr_inf->cur_frame_no = proto_head->fn2;
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;
    
    if (trsdata_ctr_inf->cur_frame_no == 0)
    {
        trig_end();
        parse_filedata_first_frame(tran_cmd_buf, trsdata_ctr_inf->cur_frame_no);
    }

    return SUCCESSFUL;
}

/* 解析上传文件数据首发帧 */
static int parse_filedata_first_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no)
{
    s_upload_trsdata_ctr_inf.tFileName.wReqFileNameLen = get_int16_data(&tran_cmd_buf->buf[0]); //请求的文件名长度
    rt_memset_s( s_upload_trsdata_ctr_inf.tFileName.ucReqFileName , UPLOAD_FILE_NAME_MAX_LEN , 0 , UPLOAD_FILE_NAME_MAX_LEN );
    memcpy_s(s_upload_trsdata_ctr_inf.tFileName.ucReqFileName, s_upload_trsdata_ctr_inf.tFileName.wReqFileNameLen, 
        &tran_cmd_buf->buf[2], s_upload_trsdata_ctr_inf.tFileName.wReqFileNameLen); //请求的文件名

    return SUCCESSFUL;
}

/* 打包上传文件数据帧 打包首发帧和其他帧 */
int pack_upload_file_data(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    unsigned short frm_no = 0;
    download_cmd_head_t* proto_head = NULL;  

    // 入参校验
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL)
    {
        return FAILURE;
    }

    //判断协议层解析数据是否正确
    if (tran_cmd_buf->rtn != DOWNLOAD_FRAME_CORRECT)
    {
        return FAILURE;
    }

    proto_head = (download_cmd_head_t*)tran_cmd_buf->cmd->req_head;
    frm_no = proto_head->fn2;
    
    if (frm_no == 0)
    {
        pack_filedata_first_frame(tran_cmd_buf, frm_no);
    }
    else
    {
        pack_filedata_other_frame(tran_cmd_buf, frm_no);
    } 

    return SUCCESSFUL;
}

/* 打包上传文件数据首发帧 */
static int pack_filedata_first_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no)
{
    int offset = 0;
    time_t pt_time;
    static unsigned int crc_ctx = 0 ^ 0xFFFFFFFF; // 校验和

    // 获取文件总长度，his_data.c提交后放开
    s_upload_trsdata_ctr_inf.uFileTotalLen = get_record_file_len(s_upload_trsdata_ctr_inf.tFileName.ucReqFileName);

    //总帧数
    s_upload_trsdata_ctr_inf.wFileTotalFrame = (s_upload_trsdata_ctr_inf.uFileTotalLen + UPLOAD_DATA_FRAME_LEN - 1) 
                                                / UPLOAD_DATA_FRAME_LEN; //向上取整
    put_int16_to_buff(&(tran_cmd_buf->buf[offset]), s_upload_trsdata_ctr_inf.wFileTotalFrame);
    offset += 2;

    //上传文件名
    memcpy_s(&tran_cmd_buf->buf[offset], s_upload_trsdata_ctr_inf.tFileName.wReqFileNameLen, 
        s_upload_trsdata_ctr_inf.tFileName.ucReqFileName, s_upload_trsdata_ctr_inf.tFileName.wReqFileNameLen);
    offset += s_upload_trsdata_ctr_inf.tFileName.wReqFileNameLen;

    //上传文件总大小
    put_int32_to_buff(&(tran_cmd_buf->buf[offset]), s_upload_trsdata_ctr_inf.uFileTotalLen);
    offset += 4;

    //上传文件时间
    pt_time = get_timestamp_s();
    rt_memset_s(&s_upload_trsdata_ctr_inf.ucFileTime[0], UPLOAD_FILE_TIME_LEN, 0, UPLOAD_FILE_TIME_LEN);
    put_time_t_to_buff(s_upload_trsdata_ctr_inf.ucFileTime, pt_time);
    memcpy_s(&tran_cmd_buf->buf[offset], sizeof(s_upload_trsdata_ctr_inf.ucFileTime), s_upload_trsdata_ctr_inf.ucFileTime, sizeof(s_upload_trsdata_ctr_inf.ucFileTime));
    offset += UPLOAD_FILE_TIME_LEN;

    //文件校验码
    crc32_calc(&crc_ctx, tran_cmd_buf->buf, offset);
    s_upload_trsdata_ctr_inf.uCrc = crc_ctx;
    put_int16_to_buff(&(tran_cmd_buf->buf[offset]), s_upload_trsdata_ctr_inf.uCrc);
    offset += 4;

    tran_cmd_buf->data_len = offset;
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;

    return SUCCESSFUL;
}

/* 打包上传文件数据其他帧 */
int pack_filedata_other_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no)
{
    int offset = 0;
    int actual_len = 0;
    int tmp = 0;

    //当请求的帧数等于总帧数+1时直接返回
    
    //求当前文件读取偏移
    offset = (frm_no - 1) * UPLOAD_DATA_FRAME_LEN;
    rt_memset_s(tran_cmd_buf->buf, BUFF_LEN_2048, 0, BUFF_LEN_2048);
    tmp = s_upload_trsdata_ctr_inf.uFileTotalLen - offset ;
    actual_len =( tmp >= UPLOAD_DATA_FRAME_LEN ) ? UPLOAD_DATA_FRAME_LEN : tmp ;
    //获取文件数据，his_data.c提交后放开
    actual_len = get_frame_from_record_file(s_upload_trsdata_ctr_inf.tFileName.ucReqFileName, offset, actual_len, tran_cmd_buf->buf);

    tran_cmd_buf->data_len = actual_len;
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;
    if(frm_no == s_upload_trsdata_ctr_inf.wFileTotalFrame)
    {
        special_file_upload_deal();
    }
    return SUCCESSFUL;
}


void time_out_ctrl_update()
{   
    common_fun_t *common_fun_p = get_register_common_fun();
    rt_timer_stop(g_update_timer);
    update_trans_failed(common_fun_p);
    return;
}

void update_trans_failed(common_fun_t *common_fun_p)
{
    if(common_fun_p->ptr_ctrl_led_when_update != NULL)
    {
        common_fun_p->ptr_ctrl_led_when_update(LED_OFF);
    }

    rt_kprintf("LED timer out\n");
    send_msg_to_thread(RECOVER_LED_STATUS_MSG, MOD_LED, NULL, 0);

    // 传输失败写操作记录
    update_sta_msg_t sta_msg = {0};
    rt_snprintf(sta_msg.info, MAX_EVENT_INFO_LEN, "north trans failed");
    sta_msg.data_s = NORTH_TRANS_FAILED;
    sta_msg.south_dev = g_south_update_dev;
    send_msg_to_thread(UPDATE_STATUS, MOD_SYS_MANAGE, &sta_msg, sizeof(sta_msg));
    return;
}

int special_file_upload_deal()
{
    int is_para_update = FAILURE;
    is_para_update = judge_update_by_file_name(g_para_update_file_name, PARA_UPDATE_FILE_NAME_NUM, (const char *)s_upload_trsdata_ctr_inf.tFileName.ucReqFileName);
    if(is_para_update == SUCCESSFUL)
    {
        send_msg_to_thread(PARA_FILE_EXPORT_MSG, MOD_SYS_MANAGE, NULL, 0);
        return SUCCESSFUL;
    }
    if(strcmp(s_upload_trsdata_ctr_inf.tFileName.ucReqFileName, AFCI_FILE_NAME) == 0)
    {
        send_msg_to_thread(AFCI_FILE_EXPORT_MSG, MOD_SYS_MANAGE, NULL, 0);
        return SUCCESSFUL;
    }
    return FAILURE;
}

/* 解析下载帧 */
static int parse_update_data(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    unsigned short frm_no = 0;
    download_cmd_head_t* proto_head = NULL;
    update_file_manage_t* trsdata_ctr_inf = NULL;
    trsdata_ctr_inf = &s_download_trsdata_ctr_inf;
    trsdata_ctr_inf->rtn = 0;
    part_data_t part_data = {0};

    rt_timer_stop(g_update_timer);     
    // 入参校验
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL) {
        return FAILURE;
    }

    //判断协议层解析数据是否正确
    if(tran_cmd_buf->rtn != DOWNLOAD_FRAME_CORRECT){
        return SUCCESSFUL;
    }
    proto_head = (download_cmd_head_t*)tran_cmd_buf->cmd->req_head;
    frm_no = proto_head->fn2;
    // rt_kprintf("parse_update_data|trig:%d, slave_trig:%d, master_trig:%d \n", 
    //     s_download_trig_ctr_inf.trig_success, get_parallel_update_slave_trig_flag(), get_parallel_update_trig_flag());

    // 首发帧处理
    RETURN_VAL_IF_FAIL(is_first_frame_deal(frm_no, tran_cmd_buf) == SUCCESSFUL, SUCCESSFUL);
    // 是否期望帧
    RETURN_VAL_IF_FAIL(is_expect_date_frame_no(frm_no, trsdata_ctr_inf, tran_cmd_buf) == SUCCESSFUL, SUCCESSFUL);
    // 第一帧处理(擦除flash)
    RETURN_VAL_IF_FAIL(erase_flash_in_first_frame(trsdata_ctr_inf, part_data, tran_cmd_buf) == SUCCESSFUL, SUCCESSFUL);
    g_first_frame_flag = FALSE;
    parse_data_frame(dev_inst, cmd_buf, tran_cmd_buf, frm_no);
    tran_cmd_buf->rtn = trsdata_ctr_inf->rtn;
    return SUCCESSFUL;
}

static int parse_first_frame(cmd_buf_t* tran_cmd_buf,unsigned short frm_no){
    int update_first_max_len = 0;
    int update_file_name_len = 0;
    int is_cert_update = FAILURE;
    int is_para_update = FAILURE;
    int is_key_update = FAILURE;
    update_file_manage_t* trsdata_ctr_inf = NULL;
    trsdata_ctr_inf = &s_download_trsdata_ctr_inf;
    update_file_attr_t file_info;                       // 首发帧文件信息
    g_other_file_update_flag = OTHER_NO_UPDATE;
    rt_memset(&file_info, 0x00, sizeof(update_file_attr_t));
    if(tran_cmd_buf->cmd->cmd_id == DOWNLOAD_DATA_DOWNLOAD_EXTEND){//区分扩展帧和非扩展帧
        update_first_max_len = DOWNLOAD_UPDATE_FIRST_EXTEND_FRAME_LEN;
        update_file_name_len = UPDATE_FILE_EXTEND_NAME_LEN;
    }else{
        update_first_max_len = DOWNLOAD_UPDATE_FIRST_FRAME_LEN;
        update_file_name_len = UPDATE_FILE_NAME_LEN;
    }
    //data域解析
    if(tran_cmd_buf->data_len != update_first_max_len) {
        trsdata_ctr_inf->rtn = DOWNLOAD_FIRST_FRAME_ERR;
        LOG_E("%s:%d|first_frame data_len:%d", __FUNCTION__ , __LINE__, tran_cmd_buf->data_len);
        return SUCCESSFUL;
    }

    file_info.total_frames = get_int16_data(&tran_cmd_buf->buf[0]);
    file_info.total_leng = get_ulong_data(&tran_cmd_buf->buf[2]);
    if(file_info.total_leng > APP_DOWNLOAD_SIZE)
    {
        LOG_E("%s:%d|first_frame total_leng:%d", __FUNCTION__ , __LINE__, file_info.total_leng);
        trsdata_ctr_inf->rtn = DOWNLOAD_FIRST_FRAME_ERR;
        return SUCCESSFUL;
    }
    memcpy_s(file_info.file_name, update_file_name_len, &tran_cmd_buf->buf[6], update_file_name_len);
    memcpy_s(file_name_tmp, update_file_name_len, &tran_cmd_buf->buf[6], update_file_name_len);
    if(update_file_name_cmp(file_info.file_name) == FAILURE)
    {
        LOG_E("%s:%d|first_frame file_name:%d", __FUNCTION__ , __LINE__, file_info.file_name);
        trsdata_ctr_inf->rtn = DOWNLOAD_FIRST_FRAME_ERR;
        return SUCCESSFUL;
    }
    g_south_update_dev = south_update_dev_cmp(file_info.file_name);
    memcpy_s(file_info.file_time, UPDATE_FILE_TIME_LEN, &tran_cmd_buf->buf[6+update_file_name_len], UPDATE_FILE_TIME_LEN);

    if(tran_cmd_buf->cmd->cmd_id == DOWNLOAD_DATA_DOWNLOAD_EXTEND){//区分扩展帧和正常帧
        // file_info.filecrc = get_ulong_data(&tran_cmd_buf->buf[6+update_file_name_len + UPDATE_FILE_TIME_LEN]);
        g_extend_file_crc = get_ulong_data(&tran_cmd_buf->buf[6+update_file_name_len + UPDATE_FILE_TIME_LEN]);
        file_info.param_type = tran_cmd_buf->buf[6+update_file_name_len + UPDATE_FILE_TIME_LEN +4];
    }else{
        file_info.param_type = tran_cmd_buf->buf[6+update_file_name_len + UPDATE_FILE_TIME_LEN];
        g_extend_file_crc = 0;
    }
    //支持断点续传
    if (file_change_check(&trsdata_ctr_inf->file_info,&file_info) == TRUE) {
        trsdata_ctr_inf->file_info.filecrc = 0;
        rt_memset_s(&trsdata_ctr_inf->file_info, sizeof(update_file_attr_t), 0x00, sizeof(update_file_attr_t));
        memcpy_s(&trsdata_ctr_inf->file_info, sizeof(update_file_attr_t), &file_info, sizeof(update_file_attr_t));
        trsdata_ctr_inf->data_offset = 0;
        trsdata_ctr_inf->cur_frame_no = frm_no + 1;
    }
    trsdata_ctr_inf->rtn = DOWNLOAD_FRAME_CORRECT;
    trsdata_ctr_inf->update_status = UPDATEING;
    clear_update_uuid();
    is_cert_update = judge_update_by_file_name(g_cert_update_file_name, CERT_UPDATE_FILE_NAME_NUM, (const char *)&trsdata_ctr_inf->file_info.file_name);
    if(SUCCESSFUL == is_cert_update)
    {
        g_other_file_update_flag = CERT_UPDATE;
        rt_snprintf_s(file_info.file_name, sizeof(file_info.file_name), "/nor/%s",trsdata_ctr_inf->file_info.file_name);//证书文件存储位置
        storage_unlink(file_info.file_name);
    }
    is_para_update = judge_update_by_file_name(g_para_update_file_name, PARA_UPDATE_FILE_NAME_NUM, (const char *)&trsdata_ctr_inf->file_info.file_name);
    if(SUCCESSFUL == is_para_update)
    {
        g_other_file_update_flag = PARA_UPDATE;
    }

    is_key_update = judge_update_by_file_name(g_key_update_file_name, KEY_UPDATE_FILE_NAME_NUM, (const char *)&trsdata_ctr_inf->file_info.file_name);
    if(SUCCESSFUL == is_key_update)
    {
        storage_unlink(file_info.file_name);
        g_other_file_update_flag = KEY_UPDATE;
        if(file_info.total_leng >= CLIENT_KEY_FILE_LEN)
        {
            trsdata_ctr_inf->rtn = DOWNLOAD_FIRST_FRAME_ERR;
            return SUCCESSFUL;
        }
    }

    if(SUCCESSFUL == judge_wifi_update(trsdata_ctr_inf))
    {
        g_other_file_update_flag = WIFI_UPDATE;
    }
    //断点续传数据(存放在芯片里面，不用额外增加flash分区)
    write_download_tmpInfo(trsdata_ctr_inf);
    return SUCCESSFUL;

}

static void switch_location_by_type(part_data_t* part_data, update_file_manage_t* trsdata_ctr_inf)
{
    const char* storage_part = NULL;
    switch(g_other_file_update_flag) 
    {
        case CERT_UPDATE:
            rt_snprintf_s(part_data->name, sizeof(part_data->name), "/nor/%s", trsdata_ctr_inf->file_info.file_name); // 证书文件存储位置
            break;
        case PARA_UPDATE:
            rt_snprintf_s(part_data->name, sizeof(part_data->name), "/nor/%s", trsdata_ctr_inf->file_info.file_name); // 参数文件存储位置
            break;
        case WIFI_UPDATE:
            rt_snprintf_s(part_data->name, sizeof(part_data->name), "%s", FW_AP_PART); // wifi固件存储flash位置
            break;
        default:
            storage_part = (strstr(file_name_tmp, DIFF_TAG) != NULL) ? UPDATE_PATCH_PART : UPDATE_DOWNLOAD_PART;
            rt_snprintf_s(part_data->name, sizeof(part_data->name), "%s", storage_part); // 升级文件存储flash位置
            break;
    }
    return;
}

static int parse_data_frame(void* dev_inst, cmd_buf_t *cmd_buf, cmd_buf_t* tran_cmd_buf, unsigned short frm_no){
    part_data_t part_data = {0};
    update_file_manage_t* trsdata_ctr_inf = NULL;
    trsdata_ctr_inf = &s_download_trsdata_ctr_inf;
    unsigned short crc = trsdata_ctr_inf->file_info.filecrc;
    //存储数据帧
    if(g_other_file_update_flag == KEY_UPDATE)
    {
        write_key_client_data(tran_cmd_buf, trsdata_ctr_inf, trsdata_ctr_inf->data_offset);
    }
    else
    {
        part_data.buff = tran_cmd_buf->buf;
        part_data.len = tran_cmd_buf->data_len;
        part_data.offset = trsdata_ctr_inf->data_offset;
        switch_location_by_type(&part_data, trsdata_ctr_inf);
        if (SUCCESSFUL != storage_process(&part_data, write_opr))
        {
            trsdata_ctr_inf->rtn = DOWNLOAD_FRAME_ERR;
            return SUCCESSFUL;
        }
    }

    //计算校验和,判断升级文件是否有问题
    crc = crc16_calc_with_init_crc(tran_cmd_buf->buf, tran_cmd_buf->data_len, crc);

    //更新数据
    trsdata_ctr_inf->cur_frame_no = frm_no + 1;
    trsdata_ctr_inf->data_offset += tran_cmd_buf->data_len;
    trsdata_ctr_inf->rtn = DOWNLOAD_FRAME_CORRECT;
    trsdata_ctr_inf->file_info.filecrc = crc;
    if(frm_no % SAVE_UPDATE_DATA_INDEX == 0)
    {
        write_download_tmpInfo(trsdata_ctr_inf);
    }

    rt_timer_start(g_update_timer);
    //判断是否是最后一帧
    RETURN_VAL_IF_FAIL(frm_no == trsdata_ctr_inf->file_info.total_frames - 1, SUCCESSFUL);

    // 判断是否是扩展帧
    if(g_extend_file_crc != 0)
    {
        trsdata_ctr_inf->file_info.filecrc = g_extend_file_crc;
    }

    LOG_E("update crc:%d\n",trsdata_ctr_inf->file_info.filecrc);
    deal_last_frame(frm_no, trsdata_ctr_inf, cmd_buf, dev_inst);
    return SUCCESSFUL;
}

int judge_one_cert_trans_complete(update_file_manage_t* file_manage,int server_index)
{
    if(strcmp(g_cert_file_name[server_index][0], file_manage->file_info.file_name) == 0)
    {
        g_cert_flags[server_index][0] = 1;
        return SUCCESSFUL;
    }

    if(strcmp(g_cert_file_name[server_index][1], file_manage->file_info.file_name) == 0)
    {
        g_cert_flags[server_index][1] = 1;
        return SUCCESSFUL;
    }

    if(strcmp(g_cert_file_name[server_index][2], file_manage->file_info.file_name) == 0)
    {
        g_cert_flags[server_index][2] = 1;
        return SUCCESSFUL;
    }
    return FAILURE;
}

int judge_cert_trans_complete(update_file_manage_t* file_manage)
{   
    int i = 0, sum = 0, server_index = 0;
    char which_tls = 0;
    judge_one_cert_trans_complete(file_manage, 0);
    judge_one_cert_trans_complete(file_manage, 1);

    for(server_index = 0; server_index < MQTT_SERVER_NUM; server_index++)
    {   
        sum = 0;
        for(i = 0; i < CERT_FILE_NUM; i++)
        {
            sum += g_cert_flags[server_index][i];
        }

        if(sum == CERT_FILE_NUM)
        {   
            which_tls = server_index;
            send_msg_to_thread(CERT_CHG_MSG, MOD_MQTT, NULL, 0);
            send_msg_to_thread(CERT_CHG_MSG, MOD_MQTT, &which_tls, sizeof(which_tls));
            rt_memset(g_cert_flags[server_index], 0x00, sizeof(g_cert_flags[server_index]));
            LOG_E("send CERT_CHG_MSG   %d\n",which_tls);
            return SUCCESSFUL;
        }
    }
    send_msg_to_thread(CERT_CHG_MSG, MOD_MQTT, NULL, 0);
    return SUCCESSFUL;
}

static int pack_update_data(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    download_cmd_head_t* proto_head = NULL;
    
    // 入参校验
    if (tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL) {
        return FAILURE;
    }

    tran_cmd_buf->data_len = 0;
    proto_head = (download_cmd_head_t*)tran_cmd_buf->cmd->ack_head;
    if(g_first_frame_flag == TRUE)
    {
        proto_head->fn1 = 0;
        proto_head->fn2 = s_download_trsdata_ctr_inf.cur_frame_no;
    }
    else
    {
        proto_head->fn1 = s_download_trsdata_ctr_inf.cur_frame_no - 1;
        proto_head->fn2 = s_download_trsdata_ctr_inf.cur_frame_no;
    }
    // rt_kprintf("pack_update_data|fn1:%d, fn2:%d\n", proto_head->fn1, proto_head->fn2);
    return SUCCESSFUL;
}

static int parse_update_trig(void* dev_inst, void *cmd_buf) {
    //开启触发定时器
    rt_timer_start(trigTimer);
    s_download_trig_ctr_inf.trig_success = FALSE;
    s_download_trig_ctr_inf.trig_times++;
    //触发成功
    if(s_download_trig_ctr_inf.trig_times >= DOWNLOAD_TRIG_TIMES){
        s_download_trig_ctr_inf.trig_success = TRUE;
    }
    return SUCCESSFUL;
}

static int pack_update_trig(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    download_cmd_head_t* proto_head = NULL;
    // 入参校验
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL) {
        return FAILURE;
    }
    proto_head = (download_cmd_head_t*)tran_cmd_buf->cmd->ack_head;
    proto_head->cid = CMD_TRIG_FRAME_TYPE_H;
    proto_head->frame_type_l = CMD_TRIG_FRAME_TYPE_L;
    proto_head->chip_type_h = CMD_TRIG_CHIP_TYPE_H;
    proto_head->chip_type_l = CMD_TRIG_VERSION; 
    tran_cmd_buf->data_len = (unsigned int)sizeof(trig_frame_ack);
    memcpy_s(tran_cmd_buf->buf, sizeof(trig_frame_ack), trig_frame_ack, sizeof(trig_frame_ack));
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;
    return SUCCESSFUL;
}

static int parse_ver_cmp(void* dev_inst, void *cmd_buf){
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    char ver_file_name_buff[VER_FILE_LEN] = {0};
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL) {
        return FAILURE;
    }
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;
    memcpy_s(ver_file_name_buff, VER_FILE_NAME_LEN, tran_cmd_buf->buf, VER_FILE_NAME_LEN);
    if(update_file_name_cmp(ver_file_name_buff) != SUCCESSFUL){
        tran_cmd_buf->rtn = DOWNLOAD_FRAME_ERR;
    }
    return SUCCESSFUL;
}
static int pack_ver_cmp(void* dev_inst, void *cmd_buf){
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    tran_cmd_buf->data_len = 0;
    return SUCCESSFUL;
}
static int parse_stop_update(void* dev_inst, void *cmd_buf){
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    char ver_file_name_buff[VER_FILE_LEN] = {0};
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL) {
        return FAILURE;
    }

    memcpy_s(ver_file_name_buff, VER_FILE_NAME_LEN, tran_cmd_buf->buf, VER_FILE_NAME_LEN);
    if(strcmp(s_download_trsdata_ctr_inf.file_info.file_name, ver_file_name_buff)){
       tran_cmd_buf->rtn = DOWNLOAD_FRAME_ERR;
    }else{
        tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;
        s_download_trsdata_ctr_inf.update_status = UPDATE_FAILURE;
    }
    return SUCCESSFUL;
}
static int pack_stop_update(void* dev_inst, void *cmd_buf){
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    tran_cmd_buf->data_len = 0;
    return SUCCESSFUL;
}
static int parse_update_progress(void* dev_inst, void *cmd_buf){
    return SUCCESSFUL;
}
static int pack_update_progress(void* dev_inst, void *cmd_buf){
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    int offset = 0;
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;
    tran_cmd_buf->buf[offset++] = s_download_trsdata_ctr_inf.update_status;
    tran_cmd_buf->buf[offset++] = ((float)(s_download_trsdata_ctr_inf.cur_frame_no )*100)/(float)(s_download_trsdata_ctr_inf.file_info.total_frames);
    tran_cmd_buf->buf[offset++] = 0;
    tran_cmd_buf->data_len = offset;
    return SUCCESSFUL;
}
static int parse_get_flag(void* dev_inst, void *cmd_buf){
    return SUCCESSFUL;
}
static int pack_get_flag(void* dev_inst, void *cmd_buf){
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    rt_memset_s(tran_cmd_buf->buf,MAC_DATA_LEN,0,MAC_DATA_LEN);
    tran_cmd_buf->data_len = MAC_DATA_LEN;
    get_mac_addr(tran_cmd_buf->buf, tran_cmd_buf->data_len);
    return SUCCESSFUL;
}
static int parse_get_single_frame_max_length(void* dev_inst, void *cmd_buf){
    return SUCCESSFUL;
}
static int pack_get_single_frame_max_length(void* dev_inst, void *cmd_buf){
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    unsigned short single_frame_max_length = 0;
    // 入参校验
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL) {
        return FAILURE;
    }

    tran_cmd_buf->data_len = 0x02;
    single_frame_max_length = get_one_frame_max_len();//命令表里面
    put_int16_to_buff(tran_cmd_buf->buf, single_frame_max_length);
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;
    return SUCCESSFUL;
}

static int parse_set_single_frame_max_length(void* dev_inst, void *cmd_buf){
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    unsigned short single_frame_max_length = 0;
    // 入参校验
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL) {
        return FAILURE;
    }
    single_frame_max_length = (unsigned short)get_int16_data(tran_cmd_buf->buf);

    if(single_frame_max_length > SINGLE_FRAME_MAX_LENGTH_MAX){
        tran_cmd_buf->rtn = DOWNLOAD_FRAME_ERR;
    }
    set_one_frame_max_len(single_frame_max_length);
    return SUCCESSFUL;
}
static int pack_set_single_frame_max_length(void* dev_inst, void *cmd_buf){
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    tran_cmd_buf->data_len = 0;
    return SUCCESSFUL;
}

int file_change_check(update_file_attr_t *pold_file, update_file_attr_t *pnew_file) {
    if ((rt_strcmp(pold_file->file_name, pnew_file->file_name) == 0) && \
        (rt_strcmp(pold_file->file_time, pnew_file->file_time) == 0) && \
        (pold_file->total_frames == pnew_file->total_frames) && \
        (pold_file->total_leng   == pnew_file->total_leng)) {
        return FALSE;
    } else {
        return TRUE;
    }
}


static int handle_update_file_name(const char (*file_names)[UPDATE_FILE_EXTEND_NAME_LEN], int file_name_num, const char *file_name)
{
    common_fun_t *common_fun_p = get_register_common_fun();
    for (int i = 0; i < file_name_num; i++)
    {
        if (strcmp(file_names[i], file_name) == 0)
        {
            rt_timer_start(g_update_timer);
            if (common_fun_p->ptr_ctrl_led_when_update != NULL)
            {
                common_fun_p->ptr_ctrl_led_when_update(LED_BLINK);
            }
            return SUCCESSFUL;
        }
    }
    return FAILURE;
}


int judge_update_by_file_name(const char (*file_names)[UPDATE_FILE_EXTEND_NAME_LEN], int file_name_num, const char *file_name)
{
    for (int i = 0; i < file_name_num; i++)
    {
        if (strcmp(file_names[i], file_name) == 0)
        {
            return SUCCESSFUL;
        }
    }
    return FAILURE;
}

static int update_file_name_cmp(const char* file_name)
{   int is_self_update = FAILURE;
    int is_south_update = FAILURE;
    int is_cert_update = FAILURE;
    int is_para_update = FAILURE;
    int is_key_update  = FAILURE;

    // 是否监控自身升级
    is_self_update = handle_update_file_name(g_self_update_file_name, SELF_UPDATE_FILE_NAME_NUM, file_name);
    RETURN_VAL_IF_FAIL(is_self_update != SUCCESSFUL, SUCCESSFUL);
    // 是否南向升级
    is_south_update = handle_update_file_name(g_south_update_file_name, SOUTH_UPDATE_FILE_NAME_NUM, file_name);
    RETURN_VAL_IF_FAIL(is_south_update != SUCCESSFUL, SUCCESSFUL);
    // 是否证书升级
    is_cert_update = judge_update_by_file_name(g_cert_update_file_name, CERT_UPDATE_FILE_NAME_NUM, file_name);
    RETURN_VAL_IF_FAIL(is_cert_update != SUCCESSFUL, SUCCESSFUL);
    // 是否wifi固件升级
    RETURN_VAL_IF_FAIL(strcmp(WIFI_FILE_NAME, file_name) != 0, SUCCESSFUL);
    // 是否参数文件升级
    is_para_update = judge_update_by_file_name(g_para_update_file_name, PARA_UPDATE_FILE_NAME_NUM, file_name);
    RETURN_VAL_IF_FAIL(is_para_update != SUCCESSFUL, SUCCESSFUL);
    //是否是key证书
    is_key_update = judge_update_by_file_name(g_key_update_file_name, KEY_UPDATE_FILE_NAME_NUM, file_name);
    RETURN_VAL_IF_FAIL(is_key_update != SUCCESSFUL, SUCCESSFUL);
    return FAILURE;

}

int south_update_dev_cmp(const char* file_name)
{
    int i = 0;

    for(i = 0; i < SOUTH_UPDATE_FILE_NAME_NUM; i++)
    {
        if(strcmp(g_south_update_file_name[i], file_name) == 0)
        {
            return i;
        }
    }
    return INVALID_DEV_TYPE;  
}

static int judge_wifi_update(update_file_manage_t* file_manage)
{
    if(strcmp(WIFI_FILE_NAME, file_manage->file_info.file_name) == 0)
    {
        return SUCCESSFUL;
    }
    return FAILURE;  
}

void erase_download_tmpInfo() {
    part_data_t part_data = {0};
    rt_snprintf_s(part_data.name, sizeof(part_data.name), UPDATE_INFO_PART);
    if (SUCCESSFUL != storage_process(&part_data, erase_opr)) {
        return ;
    }
    return ;
}




void update_download_init(void) {

    rt_uint32_t trig_tick_num   = TRIG_TIMEOUT * 1000;      //30秒
    short i = 0;

    for(i = 0; i < sizeof(s_download_update_cmd_handle)/sizeof(s_download_update_cmd_handle[0]); i++) 
    {
        register_cmd_handle(&s_download_update_cmd_handle[i]);
    }

    trigTimer = rt_timer_create("trigTimer", trigTimer_timeout, RT_NULL, trig_tick_num, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
    if(trigTimer == NULL)
    {
        return ;
    }
    g_update_timer = rt_timer_create("ctrl_update_timer", time_out_ctrl_update, NULL, 30000, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
    if(g_update_timer == NULL)
    {
        return ;
    }

    return ;
}


void trigTimer_timeout()
{
    s_download_trig_ctr_inf.trig_times = 0;
    update_sta_msg_t sta_msg = {0};
    sta_msg.data_s = TRIG_FAILED;
    rt_snprintf(sta_msg.info, MAX_EVENT_INFO_LEN, "trig failed");
    sta_msg.south_dev = INVALID_DEV_TYPE;
    // 触发失败写操作记录
    send_msg_to_thread(UPDATE_STATUS, MOD_SYS_MANAGE, &sta_msg, sizeof(sta_msg));
    rt_timer_stop(trigTimer);
}

void trig_end()
{
    s_download_trig_ctr_inf.trig_times = 0;
    rt_timer_stop(trigTimer);
}


void send_update_status(const char* info, int data_s) {
    RETURN_IF_FAIL(data_s != PARA_UPDATE_SUCCESS);
    update_sta_msg_t sta_msg = {0};
    rt_memcpy_s(sta_msg.info, MAX_EVENT_INFO_LEN, info, MAX_EVENT_INFO_LEN);
    sta_msg.data_s = data_s;
    sta_msg.south_dev = INVALID_DEV_TYPE;
    send_msg_to_thread(UPDATE_STATUS, MOD_SYS_MANAGE, &sta_msg, sizeof(sta_msg));
}


int update_type_handle(part_data_t* part_data, update_file_manage_t* trsdata_ctr_inf, cmd_buf_t* tran_cmd_buf)
{
    if(g_other_file_update_flag == KEY_UPDATE)
    {
        clear_key_client_data(trsdata_ctr_inf);//清除证书密钥buf数据
        return SUCCESSFUL;
    }

    if(WIFI_UPDATE == g_other_file_update_flag)
    {
        rt_snprintf_s(part_data->name, sizeof(part_data->name), FW_AP_PART);//wifi固件存储flash位置
    }
    else if(strstr(file_name_tmp, DIFF_TAG) != NULL)
    {
        rt_snprintf_s(part_data->name, sizeof(part_data->name), UPDATE_PATCH_PART);//差分升级包存放在patch区
    }
    else
    {
        rt_snprintf_s(part_data->name, sizeof(part_data->name), UPDATE_DOWNLOAD_PART);//升级文件存储flash位置
    }

    if (SUCCESSFUL != storage_process(part_data, erase_opr)) 
    {
        tran_cmd_buf->rtn  = DOWNLOAD_FRAME_ERR;
        return FAILURE;
    }
    return SUCCESSFUL;
}


int clear_key_client_data(update_file_manage_t* trsdata_ctr_inf)
{
    char* key_buf = NULL;
    if(strcmp(g_key_update_file_name[0], trsdata_ctr_inf->file_info.file_name) == 0)
    {
        key_buf = get_client_key_buf();
    }
    else
    {
        key_buf = get_client_other_key_buf();
    }
    rt_memset_s(key_buf, CLIENT_KEY_FILE_LEN, 0, CLIENT_KEY_FILE_LEN);
    return SUCCESSFUL;
}

int write_key_client_data(cmd_buf_t* tran_cmd_buf, update_file_manage_t* trsdata_ctr_inf, unsigned int data_offset)
{
    char* key_buf = NULL;
    if(rt_strcmp(g_key_update_file_name[0], trsdata_ctr_inf->file_info.file_name) == 0)
    {
        key_buf = get_client_key_buf();
    }
    else
    {
        key_buf = get_client_other_key_buf();
    }
    rt_memcpy_s(&key_buf[data_offset], tran_cmd_buf->data_len, tran_cmd_buf->buf, tran_cmd_buf->data_len);
    return SUCCESSFUL;
}

int is_first_frame_deal(unsigned short frm_no, cmd_buf_t* tran_cmd_buf)
{
    if (frm_no == 0)
    {
        unsigned char update_status = 0;
        common_fun_t *common_fun_p = get_register_common_fun();
        trig_end();
        read_download_tmpInfo(&s_download_trsdata_ctr_inf);
        g_first_frame_flag = TRUE;
        if(common_fun_p->ptr_get_pv_update_sta != NULL)
        {
            common_fun_p->ptr_get_pv_update_sta(&update_status);
        }
        if(update_status != DEVICE_NO_UPDATE)
        {
            tran_cmd_buf->rtn = 4;//升级中 rtn
            return FAILURE;
        }
        else
        {
            update_status = DEVICE_UPDATING;
            rt_kprintf("is_first_frame_deal | DEVICE_UPDATING\n");
            if(common_fun_p->ptr_set_pv_update_sta != NULL)
            {
                common_fun_p->ptr_set_pv_update_sta(update_status);
            }
        }
        rt_timer_start(g_update_timer);
        parse_first_frame(tran_cmd_buf, frm_no);
        tran_cmd_buf->rtn = s_download_trsdata_ctr_inf.rtn;
        return FAILURE;
    }
    return SUCCESSFUL;
}

int is_expect_date_frame_no(unsigned short frm_no, update_file_manage_t* trsdata_ctr_inf, cmd_buf_t* tran_cmd_buf)
{
    if (frm_no != trsdata_ctr_inf->cur_frame_no || trsdata_ctr_inf->update_status != UPDATEING)
    {
        tran_cmd_buf->rtn = DOWNLOAD_FRAME_ERR;
        return FAILURE;
    }
    return SUCCESSFUL;
}

int erase_flash_in_first_frame(update_file_manage_t* trsdata_ctr_inf, part_data_t part_data, cmd_buf_t* tran_cmd_buf)
{
    if(trsdata_ctr_inf->cur_frame_no == 1 && CERT_UPDATE != g_other_file_update_flag && PARA_UPDATE != g_other_file_update_flag)
    {
        RETURN_VAL_IF_FAIL(update_type_handle(&part_data, trsdata_ctr_inf, tran_cmd_buf) != FAILURE, FAILURE);
    }
    return SUCCESSFUL;
}

int south_update_deal(update_file_manage_t* trsdata_ctr_inf)
{
    common_fun_t *common_fun_p = get_register_common_fun();
    int is_south_update = judge_update_by_file_name(g_south_update_file_name, SOUTH_UPDATE_FILE_NAME_NUM, (const char *)&trsdata_ctr_inf->file_info.file_name);
    if(is_south_update == SUCCESSFUL)
    {
        write_download_tmpInfo(trsdata_ctr_inf);

        if((g_extend_file_crc != 0) &&(g_extend_file_crc != trsdata_ctr_inf->file_info.filecrc))
        {   
            LOG_E("crc is diff:  g_extend_file_crc:%02x   filecrc:%02x",g_extend_file_crc, trsdata_ctr_inf->file_info.filecrc);
            send_update_status("north trans failed", NORTH_TRANS_FAILED);
            return FAILURE;
        }

        south_dev_update(DEV_DC_AC, 1, 1);

        if(common_fun_p->ptr_ctrl_led_when_update != NULL)
        {
            common_fun_p->ptr_ctrl_led_when_update(LED_OFF);
        }

        send_msg_to_thread(RECOVER_LED_STATUS_MSG, MOD_LED, NULL, 0);
        return SUCCESSFUL;
    }
    return FAILURE;
}

int self_update_deal(update_file_manage_t* trsdata_ctr_inf, cmd_buf_t *cmd_buf, void* dev_inst)
{   
    int is_self_update = judge_update_by_file_name(g_self_update_file_name, SELF_UPDATE_FILE_NAME_NUM, (const char *)&trsdata_ctr_inf->file_info.file_name);
    if(is_self_update == SUCCESSFUL)
    {
        trsdata_ctr_inf->update_flag = FLAG_APP_UPDATE_APP;
        trsdata_ctr_inf->sys_run_flag = FALSE;
        trsdata_ctr_inf->backup_flag = FALSE;
        trsdata_ctr_inf->count = 0;
        write_download_tmpInfo(trsdata_ctr_inf);// 完成标记,文件传输完成

        is_need_restart();
        return SUCCESSFUL;
    }
    return FAILURE;
}

void wifi_file_update_deal(update_file_manage_t* trsdata_ctr_inf, cmd_buf_t *cmd_buf, void* dev_inst)
{
    cmd_buf_t send_cmd_buff;
    rt_memset_s(&send_cmd_buff, sizeof(cmd_buf_t), 0, sizeof(cmd_buf_t));
    LOG_E("wifi file update success and reset csu!");
    rt_memset_s(&trsdata_ctr_inf->file_info, sizeof(update_file_attr_t), 0, sizeof(update_file_attr_t));
    write_download_tmpInfo(trsdata_ctr_inf);// 完成标记,文件传输完成
    send_cmd_buff.cmd = cmd_buf->cmd;
    cmd_send(dev_inst, &send_cmd_buff);
    rt_thread_mdelay(5000); // 延时5s保证wifi固件升级操作记录写完
    send_msg_to_thread(CSU_RESET, MOD_SYS_MANAGE, NULL, 0);
    return;
}

void para_file_update_deal(update_file_manage_t* trsdata_ctr_inf)
{
    common_fun_t *common_fun_p = get_register_common_fun();
    if(common_fun_p->ptr_set_pv_update_sta != NULL)
    {
        common_fun_p->ptr_set_pv_update_sta(0);
    }
    char para_file_name[UPDATE_FILE_EXTEND_NAME_LEN] = {0};
    rt_memcpy_s(para_file_name, UPDATE_FILE_EXTEND_NAME_LEN, &trsdata_ctr_inf->file_info.file_name, UPDATE_FILE_EXTEND_NAME_LEN);
    send_msg_to_thread(PARA_FILE_IMPORT_MSG, MOD_SYS_MANAGE, para_file_name, sizeof(para_file_name));
    return;
}

int other_update_deal(update_file_manage_t* trsdata_ctr_inf, cmd_buf_t *cmd_buf, void* dev_inst)
{
    if(g_other_file_update_flag != OTHER_NO_UPDATE)
    {
        char update_infos[][MAX_EVENT_INFO_LEN] = {"certUpdate success", "wifiUpdate success", "paraTrans success", "keyUpdate success"};
        int update_status[] = {CERT_UPDATE_SUCCESS, WIFI_UPDATE_SUCCESS, PARA_UPDATE_SUCCESS,  CERT_UPDATE_SUCCESS};
        send_update_status(update_infos[g_other_file_update_flag - 1 ], update_status[g_other_file_update_flag - 1]);
        if(g_other_file_update_flag == CERT_UPDATE || g_other_file_update_flag == KEY_UPDATE)
        {
            //判断是否三个证书传输完成，双网管
            //发消息处理证书文件
            if(g_other_file_update_flag == KEY_UPDATE)
            {
                send_msg_to_thread(CLIENT_KEY_MSG, MOD_SYS_MANAGE, trsdata_ctr_inf->file_info.file_name, sizeof(trsdata_ctr_inf->file_info.file_name));
            }
            judge_cert_trans_complete(trsdata_ctr_inf);
        }
        else if(g_other_file_update_flag == WIFI_UPDATE)
        {
            wifi_file_update_deal(trsdata_ctr_inf, cmd_buf, dev_inst);
        }
        else
        {
            para_file_update_deal(trsdata_ctr_inf);
        }
        //传输结束后清除文件信息数据
        rt_memset_s(&trsdata_ctr_inf->file_info, sizeof(update_file_attr_t), 0, sizeof(update_file_attr_t));
        write_download_tmpInfo(trsdata_ctr_inf);// 完成标记,文件传输完成
        return SUCCESSFUL;
    }
    return FAILURE;
}

void deal_last_frame(unsigned short frm_no, update_file_manage_t* trsdata_ctr_inf, cmd_buf_t *cmd_buf, void* dev_inst)
{
    trsdata_ctr_inf->update_status = TRANSFER_SUCCESS;
    // 北向传输成功写记录
    send_update_status("north trans success", NORTH_TRANS_SUCCESS);
    rt_timer_stop(g_update_timer);

    // 南向升级处理
    RETURN_IF_FAIL(south_update_deal(trsdata_ctr_inf) == FAILURE);
    // 监控升级处理
    RETURN_IF_FAIL(self_update_deal(trsdata_ctr_inf, cmd_buf, dev_inst) == FAILURE);
    // 其他文件升级
    RETURN_IF_FAIL(other_update_deal(trsdata_ctr_inf, cmd_buf, dev_inst) == FAILURE);

    return;

}
int save_data_to_diff_part(update_file_attr_t* file_info)
{
    unsigned char* data = NULL;
    unsigned int len = 0;
    int ret = 0;
    common_fun_t *common_fun_p = get_register_common_fun();
    if(common_fun_p->north_thread_beat_go_on == NULL)
    {
        return FAILURE;
    }
    ret = handle_storage(erase_opr, UPDATE_PATCH_PART, data, len, 0);
    RETURN_VAL_IF_FAIL(ret == SUCCESSFUL, FAILURE);
    data = rt_sdram_malloc(512);
    RETURN_VAL_IF_FAIL(data != NULL, FAILURE);
    for(int i = 0; i < file_info->total_frames - 1; i++)
    {
        rt_memset_s(data, 512, 0, 512);
        len = file_info->total_leng - i * 512 > 512? 512:(file_info->total_leng - i * 512);
        ret = handle_storage(read_opr, UPDATE_DOWNLOAD_PART, data, len, i * 512);
        BREAK_IF_FAIL(ret == SUCCESSFUL);
        ret = handle_storage(write_opr, UPDATE_PATCH_PART, data, len, i * 512);
        BREAK_IF_FAIL(ret == SUCCESSFUL);
        rt_thread_mdelay(5);
        common_fun_p->north_thread_beat_go_on();
    }
    rt_sdram_free(data);
    return ret;
}

int deal_concur_update(void* file_info, void* cmd_buf, void* dev_inst)
{
    update_file_attr_t* file_info_tmp = (update_file_attr_t*)file_info;
    read_download_tmpInfo(&s_download_trsdata_ctr_inf);
    rt_memset_s(&s_download_trsdata_ctr_inf, sizeof(s_download_trsdata_ctr_inf),0,sizeof(s_download_trsdata_ctr_inf));
    rt_memcpy_s(&(s_download_trsdata_ctr_inf.file_info), sizeof(update_file_attr_t), file_info_tmp, sizeof(update_file_attr_t));
    if(strcmp(file_info_tmp->file_name, "pv_diff.bin") == 0)
    {
        RETURN_VAL_IF_FAIL(save_data_to_diff_part(file_info_tmp) == SUCCESSFUL, FAILURE);
    }
    g_extend_file_crc = s_download_trsdata_ctr_inf.file_info.filecrc;
    // 南向升级处理
    RETURN_VAL_IF_FAIL(south_update_deal(&s_download_trsdata_ctr_inf) == FAILURE, SUCCESSFUL);
    // 监控升级处理
    RETURN_VAL_IF_FAIL(self_update_deal(&s_download_trsdata_ctr_inf, (cmd_buf_t*)cmd_buf, dev_inst) == FAILURE, SUCCESSFUL);
    return FAILURE;
}
