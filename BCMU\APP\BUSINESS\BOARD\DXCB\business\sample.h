/**************************************************************************
* Copyright (C) 2001, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：E260 DXCB板软件
* 文件名称：sample.h
* 文件说明：数据采集模块头文件
* 作    者：hlb
* 版本信息：V1.0
* 设计日期：2023-09-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/
#ifndef E260_DXCB_MAIN_SAMPLE_H_
#define E260_DXCB_MAIN_SAMPLE_H_

#ifdef __cplusplus
extern "C" {
#endif


/***********************  常量定义  ************************/
#define ADCCONVERTEDVALUES_SAMPLING_NUM  7   //ADC采样次数

#define DIGITAL_COUNT                    17
#define ANALOG_COUNT                     32
#define ADC_DEV_NAME        "adc0"
#define REFER_VOLTAGE        3        /* VOLTAGE  is 3V */

//引脚
#define PIN_A0_S1    GET_PIN(F, 15)
#define PIN_A1_S1    GET_PIN(G, 0)
#define PIN_A2_S1    GET_PIN(G, 1)
#define PIN_A3_S1    GET_PIN(E, 9)
#define PIN_A4_S1    GET_PIN(E, 10)
#define PIN_A5_S1    GET_PIN(E, 15)

#define PIN_DI1_IN     GET_PIN(B,7)
#define PIN_DI2_IN     GET_PIN(B,10)
#define PIN_DI_17      GET_PIN(D,3)

/*********************  数据结构定义  **********************/

typedef enum 
{
    AI_0_0 = 0,
    AI_0_1,
    AI_0_2,
    AI_0_3,
    AI_0_4,
    AI_0_5,
    AI_0_6,
    AI_0_7,
    AI_1_0,
    AI_1_1,
    AI_1_2,
    AI_1_3,
    AI_1_4,
    AI_1_5,
    AI_1_6,
    AI_1_7,
    AI_2_0,
    AI_2_1,
    AI_2_2,
    AI_2_3,
    AI_2_4,
    AI_2_5,
    AI_2_6,
    AI_2_7,
    AI_3_0,
    AI_3_1,
    AI_3_2,
    AI_3_3,
    AI_3_4,
    AI_3_5,
    AI_3_6,
    AI_3_7,
    AI_END,
}ADC_SAMPLE_CHANNEL;

typedef enum{
    DI1_IN_0 = 0,
    DI1_IN_1,
    DI1_IN_2,
    DI1_IN_3,
    DI1_IN_4,
    DI1_IN_5,
    DI1_IN_6,
    DI1_IN_7,
    DI2_IN_0,
    DI2_IN_1,
    DI2_IN_2,
    DI2_IN_3,
    DI2_IN_4,
    DI2_IN_5,
    DI2_IN_6,
    DI2_IN_7,
    DI_17,
    DI_END,
} eDI_SAMPLE;

typedef struct 
{
     ADC_SAMPLE_CHANNEL    sample_channel;
     int                   select_status;
     int                   adc_channel;
     unsigned int          sid;
}ADC_CHANNEL_INFO_STRUCT;

typedef struct 
{
    eDI_SAMPLE    sample_channel;
    int           select_status;
    int           di_pin;
}DI_CHANNEL_INFO_STRUCT;

/*********************  函数原型定义  **********************/
void *sample_init_sys(void * param);
void sample_main(void * parameter);
unsigned short get_analog_data(unsigned short *data, unsigned short data_size);
unsigned short get_digital_data(int *data, unsigned short data_size);



#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // E260_DXCB_MAIN_SAMPLE_H_;
