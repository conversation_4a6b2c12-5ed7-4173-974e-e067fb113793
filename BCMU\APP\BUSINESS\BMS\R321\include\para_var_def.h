// 保存(继承)重要参数ID数组(TODO)
const WORD s_awKeyParaSaveID[] =
{
	PARA_ID_BATT_UNDER_VOLT_PRT,		                    //电池组欠压保护阈值
	PARA_ID_CELL_UNDER_VOLT_PRT,  		                    //单体欠压保护阈值
	PARA_ID_BATT_CHG_FULL_AVER_CURR,   	                    //电池充满电流
	PARA_ID_CHG_END_DURA,				                    //充电末期维持时间
	PARA_ID_HIS_DATA_INTER,  			                    //历史数据保存间隔
	PARA_ID_BUZZER_ENABLE,  			                    //蜂鸣器使能
	PARA_ID_BATT_CAP, 		 			                    //电池容量
	PARA_ID_SOFT_ANTI_THEFT_DELAY,		                    //软件防盗延时
	PARA_ID_CELL_UVP_DELAY,  			                    //单体动态欠压保护
	PARA_ID_ENABLE_DATE,			    	                //启用日期
	PARA_ID_BACKSTAGE_IP_ADDR,			                    //后台ip地址
	PARA_ID_BACKSTAGE_PORT,			                        //后台端口
	PARA_ID_GPRS_APN,					                    //GPRS APN
	PARA_ID_ALM_PHONE_NUM_1,		                        //告警手机号码-1#
	PARA_ID_ALM_PHONE_NUM_2,  			                    //告警手机号码-2#
	PARA_ID_ALM_PHONE_NUM_3,  			                    //告警手机号码-3#
	PARA_ID_RELAY,  					                    //设置告警属性,  告警干接点
	PARA_ID_ALARM_CLASS,  			                    	//告警级别
	PARA_ID_VIBRATION_ALARM_ENABLE,    	                    //振动告警使能
	PARA_ID_HEARTBEAT_CYCLE,  			                    //心跳周期
	PARA_ID_UVP_TEMP_COMPENSATION_EN,	                    //整组欠压保护温度补偿
	PARA_ID_BMS_SYSTEM_NAME,			                    //BMS系统名称
	PARA_ID_RELAY_DEFAULT_STATUS,		                    //干接点默认状态
	PARA_ID_SLEEP_INDICATOR,  		 	                    //休眠指示灯
};

// BDU上传告警禁止屏蔽
const BYTE aucAlmLevelMin[ALARM_CLASS] =
{
    0, // 充电过流保护
    1, // 放电过流保护
    1, // 电池组过压保护
    1, // 单板过温保护
    0, // 充电过流告警
    0, // 放电过流告警
    0, // 电池组过压告警
    0, // 环境温度高告警
    0, // 环境温度低告警
    0, // 电池组欠压告警
    1, // 电池组欠压保护
    0, // 单体过压告警
    1, // 单体过压保护
    0, // 单体欠压告警
    1, // 单体欠压保护
    0, // 单体充电高温告警
    1, // 单体充电高温保护
    0, // 单体放电高温告警
    1, // 单体放电高温保护
    0, // 单体充电低温告警
    1, // 单体充电低温保护
    0, // 单体放电低温告警
    1, // 单体放电低温保护
    0, // 单体一致性差告警
    0, // 单体一致性差保护
    0, // 电池SOC低告警
    0, // 电池SOC低保护
    0, // 电池SOH告警
    0, // 电池SOH保护
    0, // 单体损坏保护
    0, // 电池丢失告警
    1, // BDU EEPROM故障
    1, // 单体电压采样异常
    1, // 充电回路失效
    1, // 放电回路失效
    1, // 限流回路失效
    1, // 电池短路
    1, // 电池反接
    1, //单体温度传感器无效告警
    1, // 机内过温保护
    1, // BDU电池欠压保护
    0, // 单体温度异常
    0, // 地址冲突
    0, // 振动告警
    1, // BDU母排欠压保护
    1, // BDU母排过压保护
    1, // BDU通信断
    0, // 电压采样故障
    0, // 回路异常
    1, // BDU电池充电欠压保护
    1, //BDU电池闭锁告警
    0, //环境温度高保护
    0, //环境温度低保护
    0, //单板过温告警
    1, //均衡电路故障告警
    1, //均衡电阻温度高保护
    1, //加热膜失效
    1, //连接器温度高保护
    1, //主继电器失效
    1, //DCDC故障
    1, //采集异常
    1, //辅助源故障
    0, //单体动态欠压保护
    1, //消防告警
    1, //激活回路电流异常保护
    0, // 单体温升速率异常
    0, // 直流内阻异常告警
    0, // 直流内阻异常保护
    0, // 自放电异常
    0, // 容量衰减一致性差告警
    0, // 电池异常高温保护告警
    1, // 消防故障告警
    1, // 激活口反接告警
    0, // 站点防盗告警
    0, // GPS故障告警
    0, // 陀螺仪故障告警
    0, // 网管防盗告警
    0, // 电池单体损坏保护
    0, // 电池单体温度无效
    1, // 防水告警
};

// 电芯数量动态计算
const WORD s_awCellNumParaID[] =
{
    PARA_ID_BATT_OVER_VOLT_ALM, // 电池组过压告警阈值
    PARA_ID_BATT_OVER_VOLT_PRT, // 电池组过压保护阈值
    PARA_ID_BATT_UNDER_VOLT_ALM, // 电池组欠压告警阈值
    PARA_ID_BATT_UNDER_VOLT_PRT, // 电池组欠压保护阈值
    PARA_ID_BATT_SUPPL_VOLT, // 电池补充电电压
    PARA_ID_BATT_OVER_VOLT_ALM_RECO, // 电池组过压告警恢复值
    PARA_ID_BATT_OVER_VOLT_PRT_RECO, // 电池组过压保护恢复值
    PARA_ID_BATT_CHG_FULL_AVER_VOLT, // 电池组充满电压
};
