/*
 * @file    : acmu_protocol_modbus.c
 * @brief   : ACMU南向modbus协议实现
 * @details :
 * <AUTHOR> penglei
 * @Date    : 2024-07-18
 * @LastEditTime: 2024-07-18
 * @version : V0.0.1
 */

#include <string.h>
#include "sps.h"
#include "dev_acmu.h"
#include "realdata_id_in.h"
#include "protocol_layer.h"
#include "acmu_protocol_modbus.h"
#include "utils_data_transmission.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "utils_rtthread_security_func.h"

Static int pack_acem_universal(void* dev_inst, void *cmd_buf);
Static int parse_energy_empty(void* dev_inst, void* cmd_buff);
Static int parse_version_data_from_buff(void* dev_inst, void* cmd_buff);

Static cmd_id_to_register_info_t cmd_id_to_register_info[] RAM_SECTION = {
    {ACMU_MODBUS_GET_DATA_1,0x03,0x16e,20},
    {ACMU_MODBUS_GET_DATA_2,0x03,0x182,24},
    {ACMU_MODBUS_GET_TOTAL_ENERGY, 0x03, 0x100, 2},
    {ACMU_MODBUS_SET_ENERGY_EMPTY, 0x06, 0x11e0, 1},
    {ACMU_MODBUS_GET_SYSTEM_VERSION, 0x53, 0x00, 1},

    {0,}
};

/* 命令请求头 */
Static modbusrtu_cmd_head_t cmd_req[] = {
    {CMD_GET_DATA},
    {CMD_GET_DATA},
    {CMD_GET_DATA},
    {CMD_SET_DATA},
    {CMD_GET_VER_DATA},
};

/* 命令应答头 */
Static modbusrtu_cmd_head_t cmd_ack[] = {
    {CMD_GET_DATA},
    {CMD_GET_DATA},
    {CMD_GET_DATA},
    {CMD_SET_DATA},
    {CMD_GET_VER_DATA},
};

static data_info_id_verison_t real1_data_info[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},//数据的字节长度
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ACMU_DATA_ID_ACEM_PHASE_VOLT},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ACMU_DATA_ID_ACEM_PHASE_VOLT+1},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ACMU_DATA_ID_ACEM_PHASE_VOLT+2},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ACMU_DATA_ID_ACEM_PHASE_CURR},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ACMU_DATA_ID_ACEM_PHASE_CURR+1},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ACMU_DATA_ID_ACEM_PHASE_CURR+2},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ACMU_DATA_ID_TOTAL_ACT_POWER},
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ACMU_DATA_ID_ACEM_PHASE_ACT_POWER}, 
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ACMU_DATA_ID_ACEM_PHASE_ACT_POWER+1}, 
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ACMU_DATA_ID_ACEM_PHASE_ACT_POWER+2},
};

static data_info_id_verison_t real2_data_info[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},//数据的字节长度
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ACMU_DATA_ID_TOTAL_REACT_POWER}, 
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT, }, 
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT, }, 
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT, }, 
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ACMU_DATA_ID_TOTAL_APPARENT_POWER}, 
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT, }, 
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT, }, 
    {SEG_DATA, type_int, ARRAY_SIZE_1, DOP_4, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT, },   
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ACMU_DATA_ID_TOTAL_POWER_FACTOR},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ACMU_DATA_ID_ACEM_POWER_FACTOR},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ACMU_DATA_ID_ACEM_POWER_FACTOR+1},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ACMU_DATA_ID_ACEM_POWER_FACTOR+2},
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT, }, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT, }, 
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT, },
    {SEG_DATA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ACMU_DATA_ID_FREQUENCY},
    
};

//解析总电能 寄存器100
Static data_info_id_verison_t pack_total_energy_info[] RAM_SECTION = {
    {SEG_DATA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},//数据的字节长度
    {SEG_DATA,type_int, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, ACMU_DATA_ID_ACEM_TOTAL_ENERGY},//寄存器地址: 100, 交流电表总电能
};


static cmd_parse_info_id_verison_t cmd_parse_info[] RAM_SECTION =
{
    {&real1_data_info[0], sizeof(real1_data_info)/sizeof(data_info_id_verison_t)},
    {&real2_data_info[0], sizeof(real2_data_info)/sizeof(data_info_id_verison_t)},
    {&pack_total_energy_info[0], sizeof(pack_total_energy_info)/sizeof(data_info_id_verison_t)},
};

/* 命令总表,必须以空字符串""结束 */
static cmd_t no_poll_cmd_tab[] = {
    {ACMU_MODBUS_SET_ENERGY_EMPTY, CMD_POSITIVE, &cmd_req[3], &cmd_ack[3], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_acem_universal, parse_energy_empty},
    {0},
};

static cmd_t poll_cmd_tab[] = {
    {ACMU_MODBUS_GET_DATA_1, CMD_POSITIVE, &cmd_req[0], &cmd_ack[0], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_acem_universal, SELF_PARSE,NULL,&cmd_parse_info[0]},
    {ACMU_MODBUS_GET_DATA_2, CMD_POSITIVE, &cmd_req[1], &cmd_ack[1], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_acem_universal, SELF_PARSE,NULL,&cmd_parse_info[1]},
    {ACMU_MODBUS_GET_TOTAL_ENERGY, CMD_POSITIVE, &cmd_req[2], &cmd_ack[2], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_acem_universal, SELF_PARSE, NULL, &cmd_parse_info[2]},
    {ACMU_MODBUS_GET_SYSTEM_VERSION, CMD_POSITIVE, &cmd_req[4], &cmd_ack[4], sizeof(modbusrtu_cmd_head_t), NULL, NULL, pack_acem_universal, parse_version_data_from_buff, NULL, NULL},
    {0},
};

Static dev_type_t dev_acmu_south = {
    DEV_SOUTH,1,PROTOCOL_MODBUS_RTU,LINK_ACMU,R_BUFF_LEN,S_BUFF_LEN,0,&no_poll_cmd_tab[0],&poll_cmd_tab[0],
};

dev_type_t* init_dev_acmu_south(void)
{
    return &dev_acmu_south;
}

/**
 * @brief 根据cmd_id找到相关的寄存器信息
 * @param[in] cmd_id 输入命令id
 * @param[in] input_table 输入的table
 * @param[in] input_table_len 输入的table的长度
 * @param[in] ouputdata 返回值
 * @retval SUCCESSFUL 成功 FAILURE 失败
*/
Static int find_cmd_id_match_register_info(unsigned short cmd_id, cmd_id_to_register_info_t* input_table,
                                    unsigned short input_table_len, cmd_id_to_register_info_t** ouputdata) {
    RETURN_VAL_IF_FAIL(input_table != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(ouputdata != NULL, FAILURE);
    unsigned short i = 0;

    for(i = 0; i < input_table_len; i++) {
        if(input_table[i].cmd_id == cmd_id) {
            *ouputdata = &input_table[i];
            return SUCCESSFUL;
        }
    }
    return FAILURE;
}

/**
 * @brief 通用的打包寄存器相关信息的函数
 * @param[in] cmd_buff 命令数据缓冲区
 * @param[in] input_table 输入的寄存器起止地址，寄存器数量等数据段信息
 * @retval SUCCESSFUL 成功 FAILURE 失败
*/
Static int universal_pack_register_info(void* cmd_buff,cmd_id_to_register_info_t* input_table)
{
    RETURN_VAL_IF_FAIL(input_table != NULL && cmd_buff != NULL, FAILURE);
    unsigned char* data = ((cmd_buf_t *)cmd_buff)->buf;
    unsigned short data_offset = 0;

    //根据不同的功能码来组包
    switch (input_table->func_code)
    {
    case 0x06:
        put_int16_to_buff(data + data_offset, input_table->register_addr);
        data_offset += 2;
        data[data_offset++] = 0;
        data[data_offset++] = 0;
        break;

    case 0x53:
    case 0x03:
        put_int16_to_buff(data + data_offset, input_table->register_addr);
        data_offset += 2;
        put_int16_to_buff(data + data_offset, input_table->register_num);
        data_offset += 2;
        break;

    case 0x04:
        put_int16_to_buff(data + data_offset, input_table->register_addr);
        data_offset += 2;
        put_int16_to_buff(data + data_offset, input_table->register_num);
        data_offset += 2;
        break;

    default:
        return FAILURE;
    }

    ((cmd_buf_t *)cmd_buff)->data_len = data_offset;
    return SUCCESSFUL;
}

Static int pack_acem_universal(void* dev_inst, void* cmd_buff)
{
    // 正确响应，数据域：回复寄存器地址+寄存器个数
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    // 根据CMD_ID选择寄存器地址
    cmd_id_to_register_info_t* p_cmd_id_to_register_info = NULL;
    find_cmd_id_match_register_info(((cmd_buf_t *)cmd_buff)->cmd->cmd_id, cmd_id_to_register_info, 
        sizeof(cmd_id_to_register_info)/sizeof(cmd_id_to_register_info[0]), &p_cmd_id_to_register_info);
    RETURN_VAL_IF_FAIL(SUCCESSFUL == universal_pack_register_info(cmd_buff, p_cmd_id_to_register_info), FAILURE);
    return SUCCESSFUL;
}

Static int parse_energy_empty(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    
    float total_energy = 0;
    cmd_buf_t * cmd_buf = (cmd_buf_t *)cmd_buff;

    RETURN_VAL_IF_FAIL(cmd_buf->buf != NULL, FAILURE);
    if((cmd_buf->buf[2] == 0) && (cmd_buf->buf[3] == 0))
    {
        set_one_data(ACMU_DATA_ID_ACEM_TOTAL_ENERGY, &total_energy);
    }
    return SUCCESSFUL;
}

Static int parse_version_data_from_buff(void* dev_inst, void* cmd_buff)
{
    unsigned char data_len = 0;
    unsigned char* data_buff;
    char acem_sys_version[10];                    //交流电表版本
    unsigned char acem_fact_get_status = TRUE;


    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    data_buff = ((cmd_buf_t *)cmd_buff)->buf;

    // 检查buff是否为空
    RETURN_VAL_IF_FAIL(data_buff != NULL, FAILURE);

    data_len = data_buff[0]; // 获取数据域的数据长度

    RETURN_VAL_IF_FAIL(data_len == ARRAY_SIZE_2, FAILURE);

    rt_memset_s(&acem_sys_version[0], 10, 0, 10);

    rt_memcpy_s(&acem_sys_version[0], 10, &data_buff[1], data_len);

    set_one_data(ACMU_DATA_ID_ACEM_SYS_VERSION, &acem_sys_version[0]);

    set_one_data(ACMU_DATA_ID_ACEM_MANUFACTURE_GET_STATUS, &acem_fact_get_status);
    return SUCCESSFUL;
}