#ifndef ACMU_ALARM_REGISTER_H_  
#define ACMU_ALARM_REGISTER_H_  
#ifdef __cplusplus
extern "C" {
#endif
#define INPUT_RELAY_NUM 4

#define INPUT_MAINS_ELECTRICITY1    0   //市电1
#define INPUT_MAINS_ELECTRICITY2    1   //市电2

#define ALARM_HIGH_LIMIT    0   //高限判断模式
#define ALARM_LOW_LIMIT     1   //低限判断模式

short register_acmu_alarm(void);
char env_temp_invalid_judge(int alm_id);
char env_humidity_invalid_judge(int alm_id);
char input_relay_status_judge(int alm_id);
char meter_disconnected_judge(int alm_id);
char phase_current_judge(int alm_id);
char temperature_sensor_alarm_judge(void);
char humidity_sensor_alarm_judge(void);
int judge_phase_volt_alarm(unsigned char* buff, unsigned char offset);
unsigned char handle_alarm_status(int alm_id, unsigned char value_abnormal, unsigned char value_normal);
char total_alarm_judge(int alm_id);
char output_switch_disconnection_judge(int alm_id);
char oil_engine_start_judge(int alm_id);
char env_humidity_high_alarm_judge(int alm_id);
char env_humidity_low_alarm_judge(int alm_id);
char env_temp_alarm_judge(int alm_id);
char sensor_alarm_judge(int alm_id);
char check_alarm(float current_value, float threshold, 
                      float hysteresis, char mode, 
                      char current_state);

#ifdef __cplusplus
}  
#endif

#endif
