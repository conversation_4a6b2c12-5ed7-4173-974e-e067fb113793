
#include <rtthread.h>
#include "stdio.h"
#include "stdbool.h"
#include "para_check.h"
#include "utils_thread.h"
#include "device_type.h"
#include "data_type.h"
#include "type_define_in.h"
#include "utils_data_transmission.h"
#include "para_manage.h"
#include "para_id_in.h"
#include "cmd.h"
#include "sps.h"
#include "msg.h"
#include "msg_id.h"
#include "utils_time.h"
#include "realdata_id_in.h"
#include "grid_code.h"
#include "grid_code_para_in.h"
#include "partition_def.h"
#include "realdata_save.h"
#include "para_common.h"

Static unsigned short g_grid_code = DEFAULT_GRID_CODE;




void update_grid_code_if_needed(char value_is_set, unsigned short* grid_code) {
    if (value_is_set == FALSE) {
        *grid_code = get_grid_code_from_flash();
    } else {
        get_one_para(DAC_PARA_ID_GRID_STANDARD_CODE_OFFSET, grid_code);
    }
}

int is_grid_code_changed(unsigned short grid_code) {
    if (grid_code == g_grid_code) {
        return 0;
    }
    g_grid_code = grid_code;
    return 1;
}

void update_scope_limits(grid_sys_para_t* grid_sys_para, numeric_para_attr_t* para_attr) {
    scope_t* para_scope = get_para_scope_by_offset(para_attr->scope_offset);
    if (para_scope == NULL) {
        return;
    }

    for (int scope_index = 0; scope_index < para_attr->scope_num; scope_index++) {
        rt_memcpy(&para_scope[scope_index], &grid_sys_para->scope_limit[scope_index], sizeof(scope_t));
    }
}

void update_parameter_value_if_needed(char value_is_set, unsigned short para_id_offset, grid_sys_para_t* grid_sys_para) {
    if (value_is_set == TRUE) {
        set_one_para(para_id_offset, &grid_sys_para->value, FALSE, TRUE);
    }
}

void process_grid_code_para(char value_is_set, unsigned short grid_code) {
    int para_num = sizeof(grid_code_para) / sizeof(grid_code_para[0]);

    for (int para_index = 0; para_index < para_num; para_index++) {
        if (grid_code_para[para_index].grid_code != grid_code) 
        {
            continue;
        }
        
        grid_sys_para_t* grid_sys_para = &grid_code_para[para_index].grid_sys_para;
        unsigned short para_id = grid_sys_para->para_id_offset;
        unsigned short para_offset;
        numeric_para_attr_t* para_attr = get_numeric_para_attr(para_id, &para_offset);

        if (para_attr == NULL) {
            continue;
        }

        unsigned short para_id_offset = GET_SYS_PARA_ID_OFFSET(para_attr->data_type, para_offset);

        if (para_attr->scope_num == grid_sys_para->scope_num) {
            update_scope_limits(grid_sys_para, para_attr);
            update_parameter_value_if_needed(value_is_set, para_id_offset, grid_sys_para);
        } 
    }
}

void finalize_grid_code_change() {
    save_numeric_para();
    send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, DC_AC_SET_PARA_DATA);
    rt_thread_mdelay(3000);
    send_msg_to_thread(CHECK_DC_AC_PARA, MOD_SYS_MANAGE, NULL, 0);
}

// 设置电网标准码的时候，紧急关机和授权关机特殊处理
int emergy_auth_off()
{
    unsigned short shutdown = 0;
    power_off_reason_t* power_off_reason = NULL;
    power_off_reason = get_power_off_reason();
    if((power_off_reason->emergy_off == TRUE) || (power_off_reason->time_off == TRUE))
    {
        set_one_para(DAC_PARA_ID_POWER_ON_OFF_OFFSET, &shutdown, TRUE, TRUE);
        LOG_E("%s:%d| grid emergy_off: %d  time_off: %d", __FUNCTION__, __LINE__, power_off_reason->emergy_off, power_off_reason->time_off);
    }
    return SUCCESSFUL;
}

void grid_code_change(char value_is_set) {
    unsigned short grid_code = 0;
    unsigned char grid_code_status = 0;

    update_grid_code_if_needed(value_is_set, &grid_code);

    if (is_grid_code_changed(grid_code)) {
        process_grid_code_para(value_is_set, grid_code);

        if (value_is_set == TRUE) {
            set_one_data(DAC_DATA_ID_GRID_CODE_STATUS, &grid_code_status);
            emergy_auth_off();
            finalize_grid_code_change();
        }
    }
}


// main函数里，对电网标准码特殊处理

unsigned short get_grid_code_from_flash(void)
{   
    unsigned short i = 0;
    unsigned short para_offset = 0;
    unsigned short para_id_offset = 0;
    unsigned short grid_code = DEFAULT_GRID_CODE;
    numeric_para_attr_t*  para_attr = NULL;
    numeric_para_save_t* saved_numeric_para = NULL;

    saved_numeric_para = (numeric_para_save_t*)rt_malloc(sizeof(numeric_para_save_t));
    if (saved_numeric_para == NULL)
    {
        return DEFAULT_GRID_CODE;
    }

    if (read_part_para(NUMPARA_PART, (unsigned char*)saved_numeric_para, sizeof(numeric_para_save_t)) != SUCCESSFUL) {
        rt_free(saved_numeric_para);
        saved_numeric_para = NULL;
        return DEFAULT_GRID_CODE;
    }

    for(i = 0; i < saved_numeric_para->para_num; i++) 
    {
        para_attr = get_numeric_para_attr(saved_numeric_para->para_val[i].id, &para_offset);
        if (para_attr == NULL)
            continue;
        
        para_id_offset = GET_SYS_PARA_ID_OFFSET(para_attr->data_type, para_offset);
        if (para_id_offset == DAC_PARA_ID_GRID_STANDARD_CODE_OFFSET)
        {   
            grid_code = saved_numeric_para->para_val[i].cur_val.us_val;
            break;
        }
    }

    rt_free(saved_numeric_para);
    saved_numeric_para = NULL;
    return grid_code;
}



int check_int_data_eq(void* para_data, u_value value) {
    return (*(int*)para_data == value.si_val) ? TRUE : FALSE;
}

int check_unsigned_int_data_eq(void* para_data, u_value value) {
    return (*(unsigned int*)para_data == value.ui_val) ? TRUE : FALSE;
}

int check_short_data_eq(void* para_data, u_value value) {
    return (*(short*)para_data == value.ss_val) ? TRUE : FALSE;
}

int check_unsigned_short_data_eq(void* para_data, u_value value) {
    return (*(unsigned short*)para_data == value.us_val) ? TRUE : FALSE;
}

int check_char_data_eq(void* para_data, u_value value) {
    return (*(char*)para_data == value.sc_val) ? TRUE : FALSE;
}

int check_unsigned_char_data_eq(void* para_data, u_value value) {
    return (*(unsigned char*)para_data == value.uc_val) ? TRUE : FALSE;
}

int check_float_data_eq(void* para_data, u_value value) {
    return (FLOAT_EQ(*(float*)para_data, value.f_val)) ? TRUE : FALSE;
}

grid_check_data_t grid_check_data[] = {
    {TYPE_INT8U, check_unsigned_char_data_eq},
    {TYPE_INT8S, check_char_data_eq},
    {TYPE_INT16U, check_unsigned_short_data_eq},
    {TYPE_INT16S, check_short_data_eq},
    {TYPE_INT32U, check_unsigned_int_data_eq},
    {TYPE_INT32S, check_int_data_eq},
    {TYPE_FLOAT, check_float_data_eq},
};

int check_one_data_eq(unsigned short para_id, u_value value) {
    str64 para_data = {0};
    unsigned short para_offset;

    numeric_para_attr_t* para_attr = get_numeric_para_attr(para_id, &para_offset);

    if (para_attr == NULL) {
        return FALSE;
    }

    unsigned short para_id_offset = GET_SYS_PARA_ID_OFFSET(para_attr->data_type, para_offset);
    get_one_para(para_id_offset, &para_data);
    unsigned char data_type = GET_SYS_PARA_DATA_TYPE(para_id_offset);
    return grid_check_data[data_type].check_data(&para_data, value);;
}

int grid_code_status_check(void) {
    unsigned char grid_code_status = 0;
    int ret = TRUE;
    unsigned short grid_code = 0;

    // 获取电网标准码
    get_one_para(DAC_PARA_ID_GRID_STANDARD_CODE_OFFSET, &grid_code);

    for (int i = 0; i < GRID_PARA_NUM; i++) {
        if (grid_code_para[i].grid_code != grid_code) {
            continue;
        }

        // 检查参数是否一样,各种参数
        ret = check_one_data_eq(grid_code_para[i].grid_sys_para.para_id_offset, grid_code_para[i].grid_sys_para.value);

        if (ret == FALSE) {
            grid_code_status = 1;
            LOG_E("grid_code_status_check | %02x\n",grid_code_para[i].grid_sys_para.para_id_offset);
            break;
        }
    }

    // 写状态
    set_one_data(DAC_DATA_ID_GRID_CODE_STATUS, &grid_code_status);
    return SUCCESSFUL;
}

