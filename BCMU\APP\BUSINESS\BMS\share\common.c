#include "common.h"
#include "fileSys.h"
#include "para.h"
#include "sample.h"
#include "realAlarm.h"
#include "battery.h"
#include "CommCan.h"
#include "commBdu.h"
#include "stdio.h"
#include "stdarg.h"
#include "hisdata.h"
#include "battery.h"
#include "SingleButton.h"
#include "utils_rtthread_security_func.h"
#include "prtclWireless.h"

#include <stdlib.h>

#define CRC_CCITT   0x1021
#define CRC_CCITT32   0x04C10DB7
//#include "algorithm"
/***********************  变量定义  ************************/
//CRC_CCITT查询表
Static WORD s_awCRC_Table[256];
//CRC32_CCITT查询表
static UINT32 s_auiCRC32_Table[256];//BYTE        g_ucInitFlag = True;                     //是否初始化标志
Static BattTestModeEnum s_battTestMode = BATT_TEST_MODE_NORMAL;
static BOOLEAN     s_bChargeNoFullFlag = False;

Static T_HardwareParaStruct s_tHardwarePara;
T_RelayCtrl g_tRelayCtrl;
T_ProjConfig g_ProConfig;


/*双线性插值表格*/
Static const T_DoubleLinearInsertStruct s_atDoubleLinearTable[] =
{
    {5,0.05,2.95},  {5,0.1,2.5},   {5,0.2,2.5},   {5,0.33,2.5},   {5,0.5,2.5},   {5,1.0,2.5},  ////5
    {15,0.05,2.95}, {15,0.1,2.95}, {15,0.2,2.5},  {15,0.33,2.5},  {15,0.5,2.5},  {15,1.0,2.5}, ////15
    {25,0.05,2.95}, {25,0.1,2.95}, {25,0.2,2.90}, {25,0.33,2.70}, {25,0.5,2.60}, {25,1.0,2.50},////25
    {35,0.05,2.95}, {35,0.1,2.95}, {35,0.2,2.90}, {35,0.33,2.85}, {35,0.5,2.85}, {35,1.0,2.75},////35
    {45,0.05,2.95}, {45,0.1,2.95}, {45,0.2,2.90}, {45,0.33,2.90}, {45,0.5,2.90}, {45,1.0,2.85},////45
};

// 电压-温度-电流 线性插值表
Static T_VoltTempCurrStruct s_atVoltTempCurrTable[3][72] =
{
    {// 默认map
        {0, -5, 0},      {0, 0, 0},       {0, 5, 0},       {0, 10, 0},      {0, 15, 0},      {0, 20, 0},     // <1.5V
        {0, 25, 0},      {0, 30, 0},      {0, 45, 0},      {0, 50, 0},      {0, 55, 0},      {0, 60, 0},

        {1.5, -5, 0.02}, {1.5, 0, 0.02},  {1.5, 5, 0.05},  {1.5, 10, 0.05}, {1.5, 15, 0.05}, {1.5, 20, 0.05},// 1.5~2.5V
        {1.5, 25, 0.05}, {1.5, 30, 0.05}, {1.5, 45, 0.05}, {1.5, 50, 0.05}, {1.5, 55, 0.05}, {1.5, 60, 0.05},

        {2.5, -5, 0.02}, {2.5, 0, 0.02},  {2.5, 5, 0.1},   {2.5, 10, 0.1},  {2.5, 15, 0.1},  {2.5, 20, 0.1}, // 2.5~2.8V
        {2.5, 25, 0.1},  {2.5, 30, 0.1},  {2.5, 45, 0.1},  {2.5, 50, 0.1},  {2.5, 55, 0.1},  {2.5, 60, 0.1},

        {2.8, -5, 0.02}, {2.8, 0, 0.02},  {2.8, 5, 0.1},   {2.8, 10, 0.2},  {2.8, 15, 0.5},  {2.8, 20, 0.7}, // 2.8~3.5V
        {2.8, 25, 1},    {2.8, 30, 1},    {2.8, 45, 1},    {2.8, 50, 1},    {2.8, 55, 0.5},  {2.8, 60, 0.5},

        {3.5, -5, 0},    {3.5, 0, 0.02},  {3.5, 5, 0.1},   {3.5, 10, 0.1},  {3.5, 15, 0.1},  {3.5, 20, 0.1}, // 3.5~3.65V
        {3.5, 25, 0.1},  {3.5, 30, 0.1},  {3.5, 45, 0.1},  {3.5, 50, 0.1},  {3.5, 55, 0.1},  {3.5, 60, 0.1},

        {3.65, -5, 0},   {3.65, 0, 0},    {3.65, 5, 0},    {3.65, 10, 0},   {3.65, 15, 0},   {3.65, 20, 0},  // >3.65V
        {3.65, 25, 0},   {3.65, 30, 0},   {3.65, 45, 0},   {3.65, 50, 0},   {3.65, 55, 0},   {3.65, 60, 0},
    },
    {// 容量为100AH，并且电芯循环次数小于3000，则采用如下map
        {0, -5, 0},      {0, 0, 0},       {0, 5, 0},       {0, 10, 0},      {0, 15, 0},      {0, 20, 0},     // <1.5V
        {0, 25, 0},      {0, 30, 0},      {0, 45, 0},      {0, 50, 0},      {0, 55, 0},      {0, 60, 0},

        {1.5, -5, 0.02}, {1.5, 0, 0.02},  {1.5, 5, 0.02},  {1.5, 10, 0.02}, {1.5, 15, 0.02}, {1.5, 20, 0.02},// 1.5~2.5V
        {1.5, 25, 0.02}, {1.5, 30, 0.02}, {1.5, 45, 0.02}, {1.5, 50, 0.02}, {1.5, 55, 0.02}, {1.5, 60, 0.02},

        {2.5, -5, 0.02}, {2.5, 0, 0.02},  {2.5, 5, 0.1},   {2.5, 10, 0.1},  {2.5, 15, 0.1},  {2.5, 20, 0.1}, // 2.5~2.8V
        {2.5, 25, 0.1},  {2.5, 30, 0.1},  {2.5, 45, 0.1},  {2.5, 50, 0.1},  {2.5, 55, 0.1},  {2.5, 60, 0.02},

        {2.8, -5, 0.02}, {2.8, 0, 0.1},   {2.8, 5, 0.1},   {2.8, 10, 0.2},  {2.8, 15, 0.35}, {2.8, 20, 0.5}, // 2.8~3.5V
        {2.8, 25, 0.7},  {2.8, 30, 1},    {2.8, 45, 1},    {2.8, 50, 0.6},  {2.8, 55, 0.1},  {2.8, 60, 0.02},

        {3.5, -5, 0},    {3.5, 0, 0.1},   {3.5, 5, 0.1},   {3.5, 10, 0.1},  {3.5, 15, 0.1},  {3.5, 20, 0.1}, // 3.5~3.65V
        {3.5, 25, 0.1},  {3.5, 30, 0.1},  {3.5, 45, 0.1},  {3.5, 50, 0.1},  {3.5, 55, 0.1},  {3.5, 60, 0.02},

        {3.65, -5, 0},   {3.65, 0, 0},    {3.65, 5, 0},    {3.65, 10, 0},   {3.65, 15, 0},   {3.65, 20, 0},  // >3.65V
        {3.65, 25, 0},   {3.65, 30, 0},   {3.65, 45, 0},   {3.65, 50, 0},   {3.65, 55, 0},   {3.65, 60, 0},
    },
    {// 容量为150AH，则采用如下充电map
        {0, -5, 0},      {0, 0, 0},       {0, 5, 0},       {0, 10, 0},      {0, 15, 0},      {0, 20, 0},     // <1.5V
        {0, 25, 0},      {0, 30, 0},      {0, 45, 0},      {0, 50, 0},      {0, 55, 0},      {0, 60, 0},

        {1.5, -5, 0.02}, {1.5, 0, 0.02},  {1.5, 5, 0.02},  {1.5, 10, 0.02}, {1.5, 15, 0.02}, {1.5, 20, 0.02},// 1.5~2.5V
        {1.5, 25, 0.02}, {1.5, 30, 0.02}, {1.5, 45, 0.02}, {1.5, 50, 0.02}, {1.5, 55, 0.02}, {1.5, 60, 0.02},

        {2.5, -5, 0.02}, {2.5, 0, 0.02},  {2.5, 5, 0.1},   {2.5, 10, 0.1},  {2.5, 15, 0.1},  {2.5, 20, 0.1}, // 2.5~2.8V
        {2.5, 25, 0.1},  {2.5, 30, 0.1},  {2.5, 45, 0.1},  {2.5, 50, 0.1},  {2.5, 55, 0.1},  {2.5, 60, 0.02},

        {2.8, -5, 0.02}, {2.8, 0, 0.02},  {2.8, 5, 0.1},   {2.8, 10, 0.2},  {2.8, 15, 0.6},  {2.8, 20, 1},   // 2.8~3.5V
        {2.8, 25, 1},    {2.8, 30, 1},    {2.8, 45, 1},    {2.8, 50, 0.6},  {2.8, 55, 0.1},  {2.8, 60, 0.02},

        {3.5, -5, 0},    {3.5, 0, 0.02},  {3.5, 5, 0.1},   {3.5, 10, 0.1},  {3.5, 15, 0.1},  {3.5, 20, 0.1}, // 3.5~3.65V
        {3.5, 25, 0.1},  {3.5, 30, 0.1},  {3.5, 45, 0.1},  {3.5, 50, 0.1},  {3.5, 55, 0.1},  {3.5, 60, 0.02},

        {3.65, -5, 0},   {3.65, 0, 0},    {3.65, 5, 0},    {3.65, 10, 0},   {3.65, 15, 0},   {3.65, 20, 0},  // >3.65V
        {3.65, 25, 0},   {3.65, 30, 0},   {3.65, 45, 0},   {3.65, 50, 0},   {3.65, 55, 0},   {3.65, 60, 0},
    },
};

static BOOLEAN IsDigit(BYTE element);


BOOLEAN InitProConfig(void)
{
    readBmsHWPara(&s_tHardwarePara);

#ifdef DEVICE_USING_D121
    g_ProConfig.fRateCurrChg = 70.0f;
    g_ProConfig.fRateCurrChgBus = 25.0f;
    g_ProConfig.fRateCurrDischg = 50.0f;
    g_ProConfig.fMaxDischgCurr = 70.0f;
    g_ProConfig.fCapTransRate = 2.0f;
    g_ProConfig.fVoltTrasRate = 2.0f;
    g_ProConfig.fDischgCurrCoef = 1.4f;
    g_ProConfig.fBdcuChgMax = 40.0f;
    g_ProConfig.fBdcuDischgMax = 66.0f;
    g_ProConfig.chBMSType = "BR4850S1";
#else
    if(s_tHardwarePara.ucCellVoltNum == R121_CELL_VOLT_NUM)
    {
        g_ProConfig.fRateCurrChg = 125.0f;
        g_ProConfig.fRateCurrChgBus = 50.0f;
        g_ProConfig.fRateCurrDischg = 50.0f;
        g_ProConfig.fMaxDischgCurr = 64.0f;
        g_ProConfig.fCapTransRate = 2.0f;
        g_ProConfig.fVoltTrasRate = (15.0f / 8.0f);
        g_ProConfig.fDischgCurrCoef = 1.28f;
        g_ProConfig.fBdcuChgMax = 50.0f;
        g_ProConfig.fBdcuDischgMax = 64.0f;
        g_ProConfig.chBMSType = "BR48100S4";
    }
    else
    {
        g_ProConfig.fRateCurrChg = 150.0f;
        g_ProConfig.fRateCurrChgBus = 100.0f;
        g_ProConfig.fRateCurrDischg = 100.0f;
        g_ProConfig.fMaxDischgCurr = 105.0f;
        g_ProConfig.fCapTransRate = 1.0f;
        g_ProConfig.fVoltTrasRate = 1.0f;
        g_ProConfig.fDischgCurrCoef = 1.05f;
        g_ProConfig.fBdcuChgMax = 100.0f;
        g_ProConfig.fBdcuDischgMax = 105.0f;
        g_ProConfig.chBMSType = "BR48100S3";
    }
#endif
    return True;
}

/*****************************************************************************
//  函数名      ：InitCRC
//  入口参数    ：无
//  出口参数    ：无
//  功能        ：生成CRC表
* 作    者  ：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
*****************************************************************************/
void InitCRC(void)
{
    BYTE i;
    WORD j;
    WORD crc, nTemp;
    for (j = 0; j < 256; j++)
    {
        crc    = 0;
        nTemp  = j << 8;  
        //硬件模拟的CRC计算     
        for (i = 0; i < 8; i++) 
        {
            if ( (nTemp^crc)&0x8000 )   crc = (crc << 1)^CRC_CCITT;
            else                        crc <<= 1;
            nTemp <<= 1;
        }
        s_awCRC_Table[j] = crc;
    }

    return;     
}

/*
 * @brief 生成CRC32表
 * @param[in]
 * @retval
 * @note  注解
 * @par 修改日志
 *      2022-12-01, added by zoushaoyun
 */
VOID InitCRC32Table(VOID)
{
    UINT32 i, j;
    UINT32 crc;
    UINT32 nData;
    for ( i = 0; i < 256; i++ ) {
        nData = ( UINT32 )( i << 24 );
        crc = 0;
        for ( j = 0; j < 8; j++ ) {
            if ( ( nData ^ crc ) & 0x80000000 ) {
                crc = ( crc << 1 ) ^ CRC_CCITT32;
            } else {
                crc <<= 1;
            }
            nData <<= 1;
        }
        s_auiCRC32_Table[i] = crc;
    }
}

/*****************************************************************************
//  函数名      ：CRC_Cal(BYTE *p,WORD wCount)
//  入口参数    ：p：数据指针   wCount：数据长度
//  出口参数    ：CRC结果
//  功能        ：查表计算CRC
* 作    者  ：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
*****************************************************************************/
WORD CRC_Cal(BYTE *p, WORD wCount)
{   
    WORD    i, crc = 0; 
    BYTE    ucTmp;

    for (i = 0; i < wCount; i++) 
    {   
        ucTmp = (BYTE)((crc >> 8)^p[i]);
        crc   = (crc << 8)^s_awCRC_Table[ucTmp];
    }
    
    return (crc % 256) * 256 + crc / 256;
}

/*************************************************************************
函数名  ：  max 
功能    ：  求两数的最大值
输入    ：  i,j 
输出    ：  最大值
调用关系：  
// 作    者 ：王威
// 版本信息：V1.0
//设计日期：2010-06-13
// 修改记录：
// 日    期     版  本      修改人      修改摘要      
// 其他说明：
*************************************************************************/
float max( float i, float j ) 
{
    if ( i > j )
    {
        return i;
    }
    else
    {
        return j;
    }
}

/*************************************************************************
函数名  ：  min 
功能    ：  求两数的最小值
输入    ：  i,j
输出    ：  最小值
调用关系：  
// 作    者 ：王威
// 版本信息：V1.0
//设计日期：2010-06-13
// 修改记录：
// 日    期     版  本      修改人      修改摘要      
// 其他说明：
*************************************************************************/
float min( float i, float j )
{
    if ( i < j )
    {
        return i;
    }
    else
    {
        return j;
    }
}

ULONG GetULongData(BYTE *p)
{
    U_32Int tData = {0};
    tData.ucData[3] = *p;
    p++;
    tData.ucData[2] = *p;
    p++;
    tData.ucData[1] = *p;
    p++;
    tData.ucData[0] = *p;
    return tData.LData;
}

BOOLEAN GetUint64Data(U_64Int *ptData, BYTE *p)
{
    BYTE i;
    if(ptData == NULL || p == NULL)
    {
        return FALSE;
    }
    for(i=8; i>0; i--)
    {
        ptData->ucData[i-1] = *p;
        p++;
    }
    return TRUE;
}
/****************************************************************************
* 函数名称：ExchangeHighLowByte()
* 输入参数：p-指向整型数第一字节的指针
* 返 回 值：无
* 功能描述：交换整型数的高低字节（根据不同的编译系统决定是否交换）
// 作    者 ：王威
// 版本信息：V1.0
//设计日期：2010-06-13
// 修改记录：
// 日    期     版  本      修改人      修改摘要      
// 其他说明：
***************************************************************************/
void ExchangeIntHighLowByte( BYTE *p )
{
    BYTE uctmp;
    
    uctmp   = *p;
    *p      = *(p + 1);
    *(p + 1)  = uctmp;
    
    return;
}

/*****************************************************************************
//  函数名      ：ResetMCU
//  入口参数    ：无
//  出口参数    ：无
//  功能        ：系统复位
// 作    者 ：王威
// 版本信息：V1.0
//设计日期：2010-06-13
// 修改记录：
// 日    期     版  本      修改人      修改摘要      
// 其他说明：
*****************************************************************************/
void ResetMCU(BYTE ucType)
{    
    if (NO_RESET_UNKNOW != ucType)
    {
    #ifdef Traffic4G
        Save4GTrafficTotail();                  //保存流量统计
    #endif
        saveResetData(RESET_REASON, ucType);    //保存关机原因
		SaveBattInfo();                         //保存BMS信息
		SaveHisPoint();                         //保存历史数据指针位置
    }

    rt_kprintf("system soft ReseType:%d\n",ucType);
    system_reset();
}

/****************************************************************************
* 函数名称：HexToASCII()
* 输入参数：16进制数字
* 返 回 值：对应的ASCII码
* 功能描述：16进制转ASCII码
// 作    者 ：王威
// 版本信息：V1.0
//设计日期：2010-06-13
// 修改记录：
// 日    期     版  本      修改人      修改摘要      
// 其他说明：
***************************************************************************/
BYTE HexToASCII( BYTE ucCh )
{
    if ( ucCh <= 9 )
    {
        return ( ucCh+0x30 );
    }
    if (ucCh <= 0x0F)
    {
        return ( ucCh+0x37 );
    }
        
    return ucCh;
}

/****************************************************************************
* 函数名称：ASCIIToHex()
* 输入参数：ASCII码
* 返 回 值：对应的16进制数值
* 功能描述：将ASCII码转换为对应的16进制数值
// 作    者 ：王威
// 版本信息：V1.0
//设计日期：2010-06-13
// 修改记录：
// 日    期     版  本      修改人      修改摘要      
// 其他说明：
***************************************************************************/
BYTE ASCIIToHex( BYTE ucAscii )
{
    if ( ucAscii >= 0x30 && ucAscii <= 0x39 )
    {
        return ( ucAscii - 0x30 );
    }
    else if ( ucAscii >= 0x41 && ucAscii <= 0x46 )
    {
        return ( ucAscii - 0x37 );
    }
        
    return ucAscii;
}

/****************************************************************************
* 函数名称：PutInt16ToBuff()
* 输入参数：p-指向16bit整型数第一字节的指针
* 返 回 值：无
* 功能描述：将整型数放入缓冲区，高在前低字节在字节在后
            如果编译器对整型数的处理是高字节存储在低地址，则函数直接返回，否则高低字节顺序对换
// 作    者 ：王威
// 版本信息：V1.0
//设计日期：2010-06-13
// 修改记录：
// 日    期     版  本      修改人      修改摘要      
// 其他说明：
***************************************************************************/
void PutInt16ToBuff( BYTE *p , SHORT iVal )
{
    U_Int tData;

    if (NULL != p)
    {
        tData.iData = (WORD)iVal;
        /*if ( LITTLEENDIAN == JudgeEnddian() )//yang.an,系统测试修改
        {*/
            //小蛋转大蛋
            *p++ = tData.ucByte[1];
            *p++ = tData.ucByte[0];
        /*}
        else
        {
            //大蛋转小蛋
            *p++ = tData.ucByte[0];
            *p++ = tData.ucByte[1];
        }*/
    }
    
    return;
}

void PutInt32ToBuff( BYTE *p , INT32 slVal )
{
    U_32Int tData;

    if (p == NULL)
    {
        return;
    }
    tData.LData = (ULONG)slVal;
    /*if ( JudgeEnddian() == LITTLEENDIAN )
    {*/
        *p++ = tData.ucData[3];
        *p++ = tData.ucData[2];
        *p++ = tData.ucData[1];
        *p++ = tData.ucData[0];
    /*}
    else
    {
        *p++ = tData.ucData[0];
        *p++ = tData.ucData[1];
        *p++ = tData.ucData[2];
        *p++ = tData.ucData[3];
    }*/
    return;
}

/****************************************************************************
* 函数名称  ：GetInt16Data
* 调    用  ：无
* 被 调 用  ：
* 输入参数  ：p:数据指针
* 返 回 值  ：16bit整数
* 功能描述  ：获取16bit整型数（根据不同的编译系统改变）
* 作    者  ：
* 设计日期  ：
* 修改记录  ：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
SHORT  GetInt16Data( BYTE * p )
{
    U_16Int tData;
  
    /*if ( JudgeEnddian() == LITTLEENDIAN )
    {*/
        tData.ucByte[1]    = *p;
        p++;
        tData.ucByte[0]    = *p;
    /*}
    else
    {
        tData.ucByte[0]    = *p;
        p++;
        tData.ucByte[1]    = *p;
    }*/

    return tData.sData;    
}

/****************************************************************************
* 函数名称  ：GetInt32Data
* 调    用  ：无
* 被 调 用  ：
* 输入参数  ：p: 数据指针
* 返 回 值  ：32bit整数
* 功能描述  ：获取32bit整型数（根据不同的编译系统改变）
* 作    者  ：
* 设计日期  ：
* 修改记录  ：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
INT32S  GetInt32Data(const BYTE * p )
{
    U_32Int tData;

    //if( JudgeEnddian() == LITTLEENDIAN )
    {
        tData.ucData[3]    = *p;
        p++;
        tData.ucData[2]    = *p;
        p++;
        tData.ucData[1]    = *p;
        p++;
        tData.ucData[0]    = *p;
    }
    //else
    //{
    //    tData.ucData[0]    = *p;
    //    p++;
    //    tData.ucData[1]    = *p;
    //    p++;
     //   tData.ucData[2]    = *p;
   //     p++;
   //     tData.ucData[3]    = *p;
   // }

    return tData.LData;
}

static INT32S GetLinearPoint(FLOAT x1, FLOAT y1, FLOAT x2, FLOAT y2, FLOAT x, FLOAT* py)
{
    FLOAT k, b;
    
    if (fabs(x1-x2) < 0.001)
    {
        return FAILURE;
    }

    if (NULL == py)
    {
        return FAILURE;
    }
    
    k = (y1-y2)/(x1-x2);
    b = y1 - k*x1;
    *py = k*x+b;    

    return SUCCESSFUL;    
}

FLOAT GetCellDCR(FLOAT fCellTemp)
{
    FLOAT x1, y1;
    FLOAT fDCR = 0.0;
    size_t i=0;
    FLOAT afPoints[][2] = {
        {-10.0, 20.0},
        {0, 12.0},
        {10, 6.0},
        {15, 4.0},
        {25, 3.0},
        {45, 2.0}, 
    };

    x1 = afPoints[0][0];
    y1 = afPoints[0][1];

    if (fCellTemp >= 45)
    {
        return (FLOAT)2.0;
    }
    else if(fCellTemp <= -10)
    {
        return (FLOAT)20.0;
    }
    else
    {
        for(i=0; i<ARR_SIZE(afPoints); i++)
        {
            if (fCellTemp <= afPoints[i][0])
            {
                if(SUCCESSFUL == GetLinearPoint(x1, y1,afPoints[i][0],afPoints[i][1],fCellTemp, &fDCR))
                {
                    return fDCR;
                }
                else
                {
                    break;
                }
            }
            x1 = afPoints[i][0];
            y1 = afPoints[i][1];
        }
    }    
    
    return (FLOAT)3.0;
}

FLOAT GetLowTempDischgCurr(FLOAT fCellTemp)
{
    FLOAT fDischgCurr = 0.0f;
    FLOAT fDischgCurrMap[][2] = {
        {-20, 0.4},
        {-10, 0.6}
    };

    if (fCellTemp < fDischgCurrMap[0][0])
    {
        fDischgCurr = fDischgCurrMap[0][1];
    }
    else if (fCellTemp > fDischgCurrMap[1][0])
    {
        fDischgCurr = fDischgCurrMap[1][1];
    }
    else
    {  
            if (SUCCESSFUL == GetLinearPoint(fDischgCurrMap[0][0], fDischgCurrMap[0][1], fDischgCurrMap[1][0], fDischgCurrMap[1][1], fCellTemp, &fDischgCurr))
            {
                return fDischgCurr;
            }
    }
    return fDischgCurr;
}

/****************************************************************************
* 函数名称：GetCRC16()
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：CRC校验码
* 功能描述：计算CRC校验码(modbus)
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
WORD GetCRC16(BYTE *buf, WORD n)
{
    WORD wCRCHi = 0xff;
    WORD wCRCLo = 0xff;
    WORD wCRC = 0xffff;
    WORD wCRCFlag = 0;
    WORD i = 0, j = 0;

    for (j = 0; j < n; j++)
    {
        wCRC = wCRC ^ buf[j];

        for (i = 0; i < 8; i++)
        {
            wCRCFlag = wCRC & 0x0001;
            wCRC = wCRC >> 1;

            if (wCRCFlag == 1)
            {
                wCRC = wCRC ^ 0xA001;
            }
        }
    }

    wCRCHi = wCRC & 0x00ff;
    wCRCLo = (wCRC >> 8) & 0x00ff;
    
    return (wCRCLo << 8 | wCRCHi);
}

WORD UnlockCodeCreate(CHAR *SeralNumber, BYTE Len)
{
    BYTE i = 0;
    CHAR ReverseSeralNumber[35] = {0};
    CHAR NewSeralNumber[80] = {0};
    ULONG LEN = 0;
    for (i = 0; i < Len; i++)
    {
        if ((Len - i - 1 > 0) || (Len - i - 1 == 0))
        {
            ReverseSeralNumber[Len - i - 1] = SeralNumber[i];
        }
        else
        {
            return 0xFFFF;
        }
    }
    ReverseSeralNumber[Len] = '\0';
    rt_snprintf_s(NewSeralNumber, sizeof(NewSeralNumber), "%szte power00000%s", SeralNumber, ReverseSeralNumber);
    LEN = rt_strnlen_s(NewSeralNumber, sizeof(NewSeralNumber));
    return GetCRC16((BYTE *)NewSeralNumber, LEN);
}

WORD Float2Word(FLOAT f)
{
    return (WORD)f;
}

SHORT Float2Short(FLOAT f)
{
    return (SHORT)f;
}

/****************************************************************************
* 函数名称：Pow10()
* 调    用：无
* 被 调 用：
* 输入参数：x--大于等于0的幂指数，x不能大于4
* 返 回 值：10的x次方
* 功能描述：求10的x次幂
* 作    者：潘奇银
* 设计日期：2007-01-15
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
SHORT Pow10( BYTE x)
{
    SHORT slRtn = 1;
    BYTE  i;

    for ( i = 0; i < x; i++ )
    {
        slRtn *= 10;
    }
    
    return slRtn;
}

/****************************************************************
函数名：GetValidData
入口参数：
出口参数：
功能：
****************************************************************/
FLOAT GetValidData( FLOAT fValue, FLOAT fMax, FLOAT fMin )
{
    if ( fValue > fMax )
    {
        fValue = fMax;
    }
    else if ( fValue < fMin )
    {
        fValue = fMin;
    }
    
    return fValue;  
}

INT32S BubbleSort(FLOAT *pfData, BYTE ucNum)
{
    BYTE i, j;
    FLOAT  fTemp;
    INT32S slRet = SUCCESSFUL;

    if (pfData ==  NULL)
    {
        return  FAILURE;
    }

    // 按由大到小排序
    for (i = 0; i < ucNum; i++)
    {
        for (j = i + 1; j < ucNum; j++)
        {
            if (pfData[j] > pfData[i])
            {
                fTemp = pfData[i];
                pfData[i] = pfData[j];
                pfData[j] = fTemp;
            }
        }
    }

    return slRet;
}

//双线性插值 Added by fengfj, 2020-07-27 14:28:19
FLOAT DoubleLinearInsertValue(FLOAT fTemperature, FLOAT fCurrCoef)
{
#define EACH_TABLE_CURR_NUM   (6)
    BYTE ucTempIndex = 0;
    BYTE ucCurrIndex = 0;
    size_t i=0;
    FLOAT fTableValue = 0.0;
    FLOAT x1, x2, y1, y2,Q11,Q12,Q21,Q22,fDenominator, fRet = 0.0;
    
    if (fTemperature <= 5.0)
    {
        fTemperature = 5.0;
    }
    else if (fTemperature >= 45)
    {
        fTemperature = 45;
    }

    if (fCurrCoef < 0.05)
    {
        fCurrCoef = 0.05;
    }
    else if (fCurrCoef > 1.0)
    {
        fCurrCoef = 1.0;
    }

    for (i=0; i<ARR_SIZE(s_atDoubleLinearTable)/EACH_TABLE_CURR_NUM - 1; i++)
    {
        fTableValue = s_atDoubleLinearTable[EACH_TABLE_CURR_NUM*i].fTemp;
        if( fTableValue<= fTemperature && fTemperature<=(fTableValue+10.001))
        {
            ucTempIndex = i;
            break;
        }
    }

    for (i=0; i<EACH_TABLE_CURR_NUM-1; i++)
    {
        if( s_atDoubleLinearTable[i].fCoef<= fCurrCoef && fCurrCoef<=s_atDoubleLinearTable[i+1].fCoef)
        {
            ucCurrIndex = i;
            break;
        }
    }

    x1 = s_atDoubleLinearTable[EACH_TABLE_CURR_NUM*ucTempIndex + ucCurrIndex].fTemp;
    x2 = s_atDoubleLinearTable[EACH_TABLE_CURR_NUM*ucTempIndex + EACH_TABLE_CURR_NUM + ucCurrIndex].fTemp;
    y1 = s_atDoubleLinearTable[EACH_TABLE_CURR_NUM*ucTempIndex + ucCurrIndex].fCoef;
    y2 = s_atDoubleLinearTable[EACH_TABLE_CURR_NUM*ucTempIndex + ucCurrIndex + 1].fCoef;
    Q11 = s_atDoubleLinearTable[EACH_TABLE_CURR_NUM*ucTempIndex + ucCurrIndex].fVolt;
    Q12 = s_atDoubleLinearTable[EACH_TABLE_CURR_NUM*ucTempIndex + ucCurrIndex + 1].fVolt;
    Q21 = s_atDoubleLinearTable[EACH_TABLE_CURR_NUM*ucTempIndex + EACH_TABLE_CURR_NUM + ucCurrIndex].fVolt;
    Q22 = s_atDoubleLinearTable[EACH_TABLE_CURR_NUM*ucTempIndex + EACH_TABLE_CURR_NUM + ucCurrIndex+1].fVolt;

    fDenominator = (x2-x1)*(y2-y1);
    fRet = Q11*(x2-fTemperature)*(y2-fCurrCoef)/fDenominator + Q21*(fTemperature-x1)*(y2-fCurrCoef)/fDenominator
        +Q12*(x2-fTemperature)*(fCurrCoef-y1)/fDenominator + Q22*(fTemperature-x1)*(fCurrCoef-y1)/fDenominator;
    
    return fRet;
}


void InitListener(T_StateListenStruct* ptState, BOOLEAN (*fhandler)(struct T_StateListen* ptStat, BYTE index), BYTE ucCounterMax)
{
    if (NULL == ptState)
    {
        return;
    }
    
    ptState->fhandle = fhandler;
    ptState->ucCounterMax = ucCounterMax;
    return;
}

void ResetListener(T_StateListenStruct* ptState)
{
    if (NULL == ptState)
    {
        return;
    }

    ptState->bState     = False;
    ptState->bFlag      = False;
    ptState->ucCounter  = 0;

}

BOOLEAN RunListener(T_StateListenStruct* ptState, BYTE i)
{
    BOOLEAN bFlag = False;

    if (NULL == ptState)
    {
        return False;
    }
    
    if(ptState->fhandle && (ptState->fhandle)(ptState, i))
    {
        bFlag = True;
    }        

    if (bFlag == ptState->bFlag)
    {
        if (ptState->ucCounter < ptState->ucCounterMax)
        {
            ptState->ucCounter += 1;
        }

        if (ptState->ucCounter >= ptState->ucCounterMax)
        {
            ptState->bState = ptState->bFlag;
        }
    }
    else
    {
        ptState->ucCounter = 0;
        ptState->bFlag = bFlag;
    }

    return ptState->bState;
}

size_t	 strnlen (const char *str, size_t maxlen)
{
    const char *p;

    for(p = str; ((p-str) < maxlen) && (*p != '\0'); ++p)
        ;
    return (p - str);
}

WORD    GetLength(int l)
{
    return (WORD)l;
}

/*************************************************************************
函数名  :   IfLeapYear
功能    :   判断是否是闰年
输入    :   year--年
输出    :   True--为闰年 False--非闰年
*************************************************************************/
BOOLEAN IfLeapYear(LONG year)
{
    return (((year%4 == 0) && (year%100 != 0)) || (year%400 == 0)) ? 1 : 0;
}

/*************************************************************************
函数名  ：  CheckTimeValid
功能    ：  检查设置时间的有效性
输入    ：  无
输出    ：  True-有效，FALSE-无效
*************************************************************************/
#if defined(PAKISTAN_CMPAK_PROTOCOL)
BOOLEAN     CheckTimeValid( T_TimeStruct  const* ptTime )
{
    BYTE  MonthsLeap[12] ={31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    BYTE  MonthsNoLeap[12] ={31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    BYTE    i;
    if (ptTime == NULL)
    {
        return False;
    }

    if ((ptTime->wYear > 2199) || (ptTime->wYear < 2000))
    {
        return False;
    }
    if (( ptTime->ucMonth < 1) || (ptTime->ucMonth > 12))
    {
        return False;
    }
    if ( (ptTime->ucHour > 23) || (ptTime->ucMinute > 59) || (ptTime->ucSecond > 59) )
    {
        return  False;
    }

    i = ptTime->ucMonth-1;
    if (IfLeapYear(ptTime->wYear))
    {
        if ((ptTime->ucDay > MonthsLeap[i]) ||
            (ptTime->ucDay < 1))
        {
            return False;
        }
    }
    else
    {
        if ((ptTime->ucDay > MonthsNoLeap[i]) ||
            (ptTime->ucDay < 1))
        {
            return False;
        }
    }

    return  True;
}
#else
BOOLEAN     CheckTimeValid( T_TimeStruct  const* ptTime )
{
    BYTE  MonthsLeap[12] ={31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    BYTE  MonthsNoLeap[12] ={31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    BYTE    i;
    if (ptTime == NULL)
    {
        return False;
    }

    if ((ptTime->wYear > 2099) || (ptTime->wYear < 2000))
    {
        return False;
    }
    if (( ptTime->ucMonth < 1) || (ptTime->ucMonth > 12))
    {
        return False;
    }
    if ( (ptTime->ucHour > 23) || (ptTime->ucMinute > 59) || (ptTime->ucSecond > 59) )
    {
        return  False;
    }

    i = ptTime->ucMonth-1;
    if (IfLeapYear(ptTime->wYear))
    {
        if ((ptTime->ucDay > MonthsLeap[i]) ||
            (ptTime->ucDay < 1))
        {
            return False;
        }
    }
    else
    {
        if ((ptTime->ucDay > MonthsNoLeap[i]) ||
            (ptTime->ucDay < 1))
        {
            return False;
        }
    }

    return  True;
}
#endif


BOOLEAN     CheckDateValid( T_DateStruct  const* ptDate )
{
    BYTE  MonthsLeap[12] ={31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    BYTE  MonthsNoLeap[12] ={31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    BYTE    i;
    if (ptDate == NULL)
    {
        return False;
    }
    if ((ptDate->wYear > 2099) || (ptDate->wYear < 2000))
    {
        return False;
    }
    if (( ptDate->ucMonth < 1) || (ptDate->ucMonth > 12))
    {
        return False;
    }

    i = ptDate->ucMonth-1;
    if (IfLeapYear(ptDate->wYear))
    {
        if ((ptDate->ucDay > MonthsLeap[i]) ||
            (ptDate->ucDay < 1))
        {
            return False;
        }
    }
    else
    {
        if ((ptDate->ucDay > MonthsNoLeap[i]) ||
            (ptDate->ucDay < 1))
        {
            return False;
        }
    }

    return  True;
}

void GetTime(T_TimeStruct  * ptTime)
{
	time_t t = time(RT_NULL);
	struct tm tTime;

	if (ptTime == NULL)
	{
		return;
	}

	localtime_r( &t, &tTime );
	ptTime->wYear = tTime.tm_year+1900;
	ptTime->ucMonth = tTime.tm_mon + 1;
	ptTime->ucDay = tTime.tm_mday;
	ptTime->ucHour = tTime.tm_hour;
	ptTime->ucMinute = tTime.tm_min;
	ptTime->ucSecond = tTime.tm_sec;

	return;
}

time_t TimeStruct2Time_t(T_TimeStruct const* pTime)
{
    struct tm tTmTime;

    rt_memset(&tTmTime, 0x00, sizeof(tTmTime));
    tTmTime.tm_year     = pTime->wYear - 1900;
    tTmTime.tm_mon      = pTime->ucMonth - 1;
    tTmTime.tm_mday     = pTime->ucDay;
    tTmTime.tm_hour     = pTime->ucHour;
    tTmTime.tm_min      = pTime->ucMinute;
    tTmTime.tm_sec      = pTime->ucSecond;
    return mktime( &tTmTime );
}

INT32S GetDiffDays(T_TimeStruct const* pTime)
{
    T_TimeStruct  tTime = {0};
    time_t nowTime = 0;
    time_t oldTime = 0;
    time_t difftime = 0;

    if(NULL == pTime || !CheckTimeValid(pTime))
    {
        return FAILURE;
    }
    GetTime( &tTime );
    nowTime = TimeStruct2Time_t(&tTime);
    oldTime = TimeStruct2Time_t(pTime);

    if(nowTime <= oldTime)
    {
        return 0;
    }

    difftime = nowTime - oldTime;

    if(difftime > INT32S_MAX)
    {
        return 0;
    }
    return (INT32S)(difftime&0xFFFFFFFF)/(ONE_DAY_MINUTES*ONE_MINUTE_PER_SECONDS);      //Coverity
}

time_t GetTimeStamp(void)
{
    T_TimeStruct  tTime;

    GetTime( &tTime );
    return TimeStruct2Time_t(&tTime);
}

BOOLEAN GetApptestFlag(void)
{
    return s_battTestMode == BATT_TEST_MODE_APPTEST;
}


BOOLEAN GetQtptestFlag(void)
{
    if(s_tHardwarePara.ucCellVoltNum == R121_CELL_VOLT_NUM)
    {
        return s_battTestMode == R121_BATT_TEST_MODE_QTPTEST;
    }
    return s_battTestMode == BATT_TEST_MODE_QTPTEST;
}



void SetApptestFlag(BOOLEAN bFlag)
{
    if(bFlag)
    {
        s_battTestMode = BATT_TEST_MODE_APPTEST;
        ClearDefenceStatus(DEFENCE_CLEAR_BY_ENTERAPPTEST);
        BduCtrl(SCI_CTRL_TEST, BATT_TEST_MODE_APPTEST);
    }
    else
    {
        if(s_tHardwarePara.ucCellVoltNum == R121_CELL_VOLT_NUM)
        {
            s_battTestMode = R121_BATT_TEST_MODE_NORMAL;
            BduCtrl(SCI_CTRL_TEST, R121_BATT_TEST_MODE_NORMAL);
        }
        else
        {
            s_battTestMode = BATT_TEST_MODE_NORMAL;
            BduCtrl(SCI_CTRL_TEST, BATT_TEST_MODE_NORMAL);
        }
        ClearDefenceStatus(DEFENCE_CLEAR_BY_EXITAPPTEST);
    }
}



void SetQtptestFlag(BOOLEAN bFlag)
{
    if(bFlag)
    {
        if(s_tHardwarePara.ucCellVoltNum == R121_CELL_VOLT_NUM)
        {
            s_battTestMode = R121_BATT_TEST_MODE_QTPTEST;
            BduCtrl(SCI_CTRL_TEST, R121_BATT_TEST_MODE_QTPTEST);

        }
        else
        {
            s_battTestMode = BATT_TEST_MODE_QTPTEST;
            BduCtrl(SCI_CTRL_TEST, BATT_TEST_MODE_QTPTEST);
        }
        ClearDefenceStatus(DEFENCE_CLEAR_BY_ENTERQTP);
    }
    else
    {
        if(s_tHardwarePara.ucCellVoltNum == R121_CELL_VOLT_NUM)
        {
            s_battTestMode = R121_BATT_TEST_MODE_NORMAL;
            BduCtrl(SCI_CTRL_TEST, R121_BATT_TEST_MODE_NORMAL);
        }
        else
        {
            s_battTestMode = BATT_TEST_MODE_NORMAL;
            BduCtrl(SCI_CTRL_TEST, BATT_TEST_MODE_NORMAL);
        }
        ClearDefenceStatus(DEFENCE_CLEAR_BY_EXITQTP);
    }
}


void SetChargeNotFullOneWeek(BOOLEAN bFlag)
{
    if(bFlag)
    {
        s_bChargeNoFullFlag = True;
    }
    else
    {
        s_bChargeNoFullFlag = False;
    }
    
}
BOOLEAN GetChargeNotFullOneWeek(void)
{
    return s_bChargeNoFullFlag;
}
void ParaStrCpy(BYTE* pcDst, BYTE* pcSrc, size_t size)
{
    size_t i = 0, j = 0;

    if (pcSrc == NULL || pcDst == NULL)
    {
        return;
    }

    for (i = 0 ; i < size ; i++)
    {
        pcDst[i] = pcSrc[i];
        if (pcSrc[i] == 0)
        {
            break;
        }
    }

    for (j = i ; j < size ; j++)
    {
        pcDst[j] = 0;
    }

    pcDst[size-1] = 0;

    return;
}

rt_uint32_t GetTickDifference(rt_uint32_t currentTime, rt_uint32_t lastTime)
{
    if (currentTime >= lastTime)
    {
        return currentTime - lastTime;
    }
    else
    {
        return RT_TICK_MAX - lastTime + currentTime;
    }
}

/***************************************************************************
 * @brief    Ascii转hex
 * @param    {BYTE*} pucSrc
 * @param    {BYTE*} pucDst
 * @param    {WORD} wLength
 * @return   {*}
 **************************************************************************/
void ConvertAscii2Hex(BYTE *pucSrc, BYTE *pucDst, WORD wLength)
{
    WORD i = 0;
    WORD j = 0;

    if (NULL == pucDst || NULL == pucSrc)
    {
        return;
    }

    while (j < wLength)
    {
        /* 若是包头或包尾，直接放入协议层缓冲区 */
        if ((SOI == pucSrc[j]) || (EOI == pucSrc[j]))
        {
            pucDst[i] = pucSrc[j];
            i++;
            j++;
        }
        /* 其余先转化为16进制，再放入协议层缓冲区 */
        else
        {
            pucDst[i] = ASCIIToHex(pucSrc[j]) * 16 + ASCIIToHex(pucSrc[j + 1]);
            i++;
            j += 2;
        }
    }
}

/***************************************************************************
 * @brief    字节序转换
 * @param    {SHORT} *wTemp
 * @return   转换后的值
 **************************************************************************/
SHORT Host2Modbus(SHORT *wTemp)
{
    SHORT wChange;
    wChange = *wTemp;
    wChange = ((wChange & 0xFF) << 8) | ((wChange & 0xFF00) >> 8);
    return wChange;
}

/***************************************************************************
 * @brief    从src中拷贝srcLen个字符到dst中，剩下的值用defaultValue填充
 * @param    {void} *des 目标位置
 * @param    {void} *src 源位置
 * @param    {BYTE} srcLen 从src中拷贝srcLen个数据
 * @param    {BYTE} defalutLen 默认长度，拷贝defalutLen个数据到des中
 * @param    {BYTE} defalutValue 默认填充数据
 * @return   {void}
 **************************************************************************/
void MemsetBuff(void *des, void *src, BYTE srcLen, BYTE defalutLen, BYTE defalutValue)
{
    BYTE buff[40];
    BYTE *pucDest = (BYTE *)des;
    BYTE *pucSrc = (BYTE *)src;
    rt_memset(buff, defalutValue, defalutLen);
    rt_memcpy(buff, pucSrc, srcLen);
    rt_memcpy(pucDest, buff, defalutLen);
}

/***************************************************************************
 * @brief    float转换为short
 * @param    {FLOAT} fData 待转换float数
 * @return   {SHORT} 转换后的值
 **************************************************************************/
SHORT FloatChangeToModbus(FLOAT fData)
{
    SHORT iTemp = 0;
    iTemp = FLOAT_TO_SHORT(fData);
    return Host2Modbus(&iTemp);
}

/***************************************************************************
 * @brief    字节序转换
 * @param    {WORD} *wTemp
 * @return   转换后的值
 **************************************************************************/
WORD WordHost2Modbus(WORD *wTemp)
{
    WORD wChange;
    wChange = *wTemp;
    wChange = ((wChange & 0xFF) << 8) | ((wChange & 0xFF00) >> 8);
    return wChange;
}

WORD BYTEHost2Modbus(BYTE *ucTemp)
{
    WORD wChange;
    wChange = *ucTemp;
    wChange = ((wChange & 0xFF) << 8) | ((wChange & 0xFF00) >> 8);
    return wChange;
}
/***************************************************************************
 * @brief    Int32ValuetoModbus
 **************************************************************************/
INT32 Int32ValuetoModbus(INT32 Temp)
{
    return ((Temp & 0xFF) << 24) | ((Temp & 0xFF00) << 8) | ((Temp & 0xFF0000) >> 8) | ((Temp & 0xFF000000) >> 24);
}

/***************************************************************************
 * @brief    uint64ValuetoModbus
 **************************************************************************/
rt_uint64_t uint64ValuetoModbus(rt_uint64_t Temp)
{
    return ((Temp & 0xFF) << 56) | ((Temp & 0xFF00) << 40) | ((Temp & 0xFF0000) << 24) | ((Temp & 0xFF000000) << 8) 
         | ((Temp & 0xFF00000000) >> 8) | ((Temp & 0xFF0000000000) >> 24) | ((Temp & 0xFF000000000000) >> 40) | ((Temp & 0xFF00000000000000) >> 56);
}

/***************************************************************************
 * @brief    FloatChangeToInt32Modbus
 * @param    {FLOAT} fData
 * @return   {*}
 **************************************************************************/
INT32 FloatChangeToInt32Modbus(FLOAT fData)
{
    INT32 iTemp = 0;
    iTemp = FLOAT_TO_INT32(fData);
    return Int32ValuetoModbus(iTemp);
}

/***************************************************************************
 * @brief    bit转byte
 * @param    {T_BitStruct} *pBitData
 * @return   {*}
 **************************************************************************/
BYTE BitToByte(T_BitStruct *pBitData)
{
    BYTE ucStatus = 0;
    BYTE i = 0;
    BYTE *pTemp = (BYTE*)pBitData;

    if (NULL != pBitData)
    {
        ucStatus = 0;
        for (i = 0; i < 8; i++)
        {
            if (*pTemp)
            {
                ucStatus |= 0x01 << i;
            }
            pTemp++;
        }
    }

    return ucStatus;
}
void ParaStrCpySNMP(BYTE* pcDst, BYTE* pcSrc, size_t size)
{
    size_t i = 0, j = 0;

    if (pcSrc == NULL || pcDst == NULL)
    {
        return;
    }

    for (i = 0 ; i < size ; i++)
    {
        pcDst[i] = pcSrc[i];
        if (pcSrc[i] == 0)
        {
            break;
        }
    }
    for (j = i ; j < size ; j++)
    {
        pcDst[j] = 0;
    }
    pcDst[size-1] = 0;
    
    for(i =size-1 ;i >0 ;i--)
    {
        if(pcDst[i] == 0x20 || pcDst[i] == 0 )
        {
           pcDst[i] = 0; 
        }
        else
        {
            break;
        }
    }
    return;
}


BOOLEAN uint64Valuetostr(U_64Int tData, BYTE *pstr, BYTE ucSize)
{
    BYTE str[9] = {0};

    if(pstr == NULL || ucSize <= sizeof(str)*2)
    {
        return False;
    }

    rt_snprintf_s((char *)str, sizeof(str), "%x", tData.ULData[1]);
    rt_strncpy_s((char *)pstr, ucSize, (char *)str, sizeof(str)-1);
    rt_snprintf_s((char *)str, sizeof(str), "%x", tData.ULData[0]);
    rt_strncat_s((char *)pstr, ucSize, (char *)str, sizeof(str)-1);

    pstr[ucSize-1] = 0;     //kw问题  非说这个地方不行，必须要加个0

    return True;
}


/*
 * @brief 查表计算CRC16
 * @param[in] pInput 数据指针
 * @param[in] length 数据长度
 * @retval    CRC结果
 * @note  注解
 * @par 修改日志
 *      2022-12-01, added by zoushaoyun
 */
WORD Calculate_CRC16(const BYTE *pInput, UINT32 length)
{
    WORD i, crc;
    BYTE ucTmp;

    crc = 0;

    if (NULL == pInput || length <= 0) {
        return RET_INVALID_PARAMS;
    }

    for (i = 0; i < length; i++)
    {
        ucTmp = (BYTE)((crc >> 8) ^ pInput[i]);
        crc = (crc << 8) ^ s_awCRC_Table[ucTmp];
    }
    return (crc);
}

/*
 * @brief 查表分段计算文件CRC16
 * @param[in] pInput 数据指针
 * @param[in] length 数据长度
 * @retval    CRC结果
 * @note  注解
 * @par 修改日志
 *      2022-12-01, added by zoushaoyun
 */

WORD crc16_calc(WORD *ctx, const uint8_t *data, int len)
{
    BYTE ucTmp = 0;
    for (int i = 0; i < len; i++)
    {
        ucTmp = (BYTE)((*ctx >> 8) ^ data[i]);
        *ctx = (*ctx << 8) ^ s_awCRC_Table[ucTmp];
    }
    return (*ctx);
}


/*
 * @brief 查表分段计算文件CRC32
 * @param[in] ctx 检验和
 * @param[in] data 数据指针
 * @param[in] len 文件长度
 * @retval    CRC结果
 * @note  注解
 * @par 修改日志
 *      2023-02-09
 */
UINT32 crc32_calc(uint32_t *ctx, const uint8_t *data, int len)
{
    for (int i = 0; i < len; i++)
    {
        *ctx = (*ctx >> 8) ^ s_auiCRC32_Table[(*ctx & 0xFF) ^ *data++];
    }

    return *ctx;
}

/***************************************************************************
 * @brief    将时间放入缓冲区中
 * @param    p:缓冲区地址  tTime:时间
 * @return   指针移动长度
 **************************************************************************/
BYTE PutTime2Buff(BYTE*p, struct tm tTime)
{
    BYTE *temp = p;
    PutInt16ToBuff(p, tTime.tm_year + 1900);
    p+= 2;
    *p++ = tTime.tm_mon + 1;
    *p++ = tTime.tm_mday;
    *p++ = tTime.tm_hour;
    *p++ = tTime.tm_min;
    *p++ = tTime.tm_sec;
    return p-temp;
}

/* 将自定义的时间结构体放入缓冲区 */
BYTE PutTimeStruct2Buff(BYTE*p, T_TimeStruct tTime)
{
    BYTE *temp = p;
    PutInt16ToBuff(p, tTime.wYear);
    p+= 2;
    *p++ = tTime.ucMonth;
    *p++ = tTime.ucDay;
    *p++ = tTime.ucHour;
    *p++ = tTime.ucMinute;
    *p++ = tTime.ucSecond;
    return p-temp;
}

void SetDigitalAlarmBit(BYTE index, BYTE *byte, BOOLEAN condition)
{
    if (condition)
    {
        *byte |= 0x01 << index;
    }
    else 
    {
        *byte &= ~(0x01 << index);
    }
}

// void PrintNowStatus(BYTE s_aucFlag ){
//     time_t cur = time(NULL);
//     struct tm TimeNow;
//     localtime_r(&cur, &TimeNow);
//     if(s_aucFlag == CHARGE){
//         rt_kprintf("----BatteryManagement-----充电开启------" );
//     }else if(s_aucFlag == DISCHARGE){
//         rt_kprintf("----BatteryManagement-----放电开启------" );
//     }else{
//         rt_kprintf("----JudgeBcmAlarm-----告警判断结束------" );
//     }
//     printf("\n%d-%d-%d %d:%d:%d", TimeNow.tm_year + 1900, TimeNow.tm_mon + 1, TimeNow.tm_mday, TimeNow.tm_hour,TimeNow.tm_min,TimeNow.tm_sec);
// }

/****************************************************************************
* 函数名称：judge_enddian
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：判断字节序
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
unsigned int judge_enddian(void)
{
    enddian_check_u data;
    data.int_value = 1;
    if (data.char_value == 1)
    {
        return LITTLEENDIAN;
    }
    return BIGENDIAN;
}

/****************************************************************************
* 函数名称：get_int16_data
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：获取16bit整型数
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
short get_int16_data(const unsigned char *p)
{
    U_16Int data;

    if (NULL == p) {
        return (short)RET_INVALID_PARAMS;
    }

    if (judge_enddian() == LITTLEENDIAN)
    {
        data.ucByte[1]    = *p;
        p++;
        data.ucByte[0]    = *p;
    }
    else
    {
        data.ucByte[0]    = *p;
        p++;
        data.ucByte[1]    = *p;
    }
    
    return data.sData;
}

/****************************************************************************
* 函数名称：put_time_t_to_buff
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：时间转buff
* 作    者  ：10331064
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期：2023-06-15      版  本      修改人      修改摘要
***************************************************************************/
void put_time_t_to_buff (unsigned char* p, time_t time) {
    struct tm s_time;

    if (p != NULL) {
        localtime_r(&time, &s_time);
        PutInt16ToBuff(p, s_time.tm_year+1900);
        p += 2;
        *p++ = s_time.tm_mon + 1;
        *p++ = s_time.tm_mday;
        *p++ = s_time.tm_hour;
        *p++ = s_time.tm_min;
        *p++ = s_time.tm_sec;
    }
    return;
}

/***************************************************************************
 * @brief    根据参数“单体充电截止电压”初始化map表
 **************************************************************************/

BOOLEAN ChangeVoltTempCurrTable(FLOAT fCellChargeFullVolt)
{
    /* 单体充电截止电压大于3.5V时，充电map中的3.65V和单体充电截止电压应保持一致。
       如果单体充电截止电压参数修改，则map表中的3.5~3.65，实际应按照3.5~单体充电截止电压 区间限流0.1C */
    for (BYTE i = 0; i < 3; i++)
    {
        for (BYTE j = 60; j < 72; j++)
        {
            s_atVoltTempCurrTable[i][j].fVolt = fCellChargeFullVolt;
        }
    }
    return SUCCESSFUL;
}


/***************************************************************************
 * @brief    电压-温度 线性插值得到充电限流值
 * @param    fVolt:电压  fTemperature:温度
 * @return   充电限流值 单位C
 **************************************************************************/

FLOAT VoltTempLinearInsert(BYTE ucMap, FLOAT fVolt, FLOAT fTemperature)
{
    BYTE ucTempIndex = 0;
    BYTE ucVoltIndex = 0;
    BYTE ucTableIndex = 0;
    BYTE i=0;
    FLOAT fTemp1, fTemp2, fCurr1, fCurr2, fRet = 0.0;

    ucMap = (ucMap > CHARGE_MAP_HIGH) ? CHARGE_MAP_DEFAULT : ucMap;

    if (fTemperature < s_atVoltTempCurrTable[ucMap][0].fTemp || fTemperature > s_atVoltTempCurrTable[ucMap][EACH_TABLE_TEMP_NUM-1].fTemp)
    {
        fRet = 0;
    }
    else
    {
        for (i=0; i<EACH_TABLE_TEMP_NUM-1; i++)
        {
            if( fTemperature >= s_atVoltTempCurrTable[ucMap][i].fTemp && fTemperature <= s_atVoltTempCurrTable[ucMap][i+1].fTemp)
            {
                ucTempIndex = i;
                break;
            }
        }

        for (i=0; i<ARR_SIZE(s_atVoltTempCurrTable[ucMap])/EACH_TABLE_TEMP_NUM-1; i++)
        {
            ucTableIndex = EACH_TABLE_TEMP_NUM*i;
            if(fVolt>=s_atVoltTempCurrTable[ucMap][ucTableIndex].fVolt && fVolt<s_atVoltTempCurrTable[ucMap][ucTableIndex+EACH_TABLE_TEMP_NUM].fVolt)
            {
                break;
            }
        }
        ucVoltIndex = i;

        fTemp1 = s_atVoltTempCurrTable[ucMap][EACH_TABLE_TEMP_NUM*ucVoltIndex + ucTempIndex].fTemp;
        fTemp2 = s_atVoltTempCurrTable[ucMap][EACH_TABLE_TEMP_NUM*ucVoltIndex + ucTempIndex + 1].fTemp;

        fCurr1 = s_atVoltTempCurrTable[ucMap][EACH_TABLE_TEMP_NUM*ucVoltIndex + ucTempIndex].fCurr;
        fCurr2 = s_atVoltTempCurrTable[ucMap][EACH_TABLE_TEMP_NUM*ucVoltIndex + ucTempIndex + 1].fCurr;

        fRet = fCurr1 + (fTemperature-fTemp1)/(fTemp2-fTemp1) * (fCurr2-fCurr1);
    }

    return fRet;
}


/***************************************************************************
 * @brief    计算数组和
 * @param    arr:传入数组  size:元素长度
 * @return   数组和
 **************************************************************************/
FLOAT SumArray(FLOAT arr[], uint16_t size) {
    int i;
    FLOAT sum = 0.0;
    for (i = 0; i < size; i++) {
        sum += arr[i];
    }
    return sum;
}

/***************************************************************************
 * @brief    计算float类型数组的平均值
 * @param    p:传入数组  size:元素长度
 * @return   数组平均值
 **************************************************************************/
FLOAT CalAverage(FLOAT *p, BYTE size)
{
    FLOAT sum = 0;
    if (NULL == p || size == 0)
    {
        return 0;
    }

    for (BYTE i = 0; i < size; i++)
    {
        sum += *p;
        p++;
    }
    return (sum / size);
}

/***************************************************************************
 * @brief    计算float类型数组的中位数
 * @param    p:传入数组  size:元素长度
 * @return   数组中位数
 **************************************************************************/
FLOAT GetCellMediumValue(FLOAT afData[], BYTE ucNum)
{
    BYTE i;
    FLOAT afDataTmp[CELL_VOL_NUM_MAX] = {0.0,};
    FLOAT fMediumVal = 0.0;

    if(ucNum > CELL_VOL_NUM_MAX || ucNum == 0)
    {
        return 0.0;
    }

    for(i = 0; i < ucNum; i++)
    {
        afDataTmp[i] = afData[i];
    }

    BubbleSort(afDataTmp, ucNum);

    if(ucNum % 2 == 0)
    {
        fMediumVal = (afDataTmp[ucNum/2 - 1] + afDataTmp[ucNum/2])/2.0;
    }
    else
    {
        fMediumVal = afDataTmp[(ucNum - 1)/2];
    }

    return fMediumVal;
}
/* 单体数量校验 */
BOOLEAN IsCellNumInScope(BYTE ucCellNum, BYTE ucMinNum, BYTE ucMaxNum)
{
    if(ucCellNum > ucMinNum && ucCellNum < ucMaxNum)
    {
        return TRUE;
    }
    return FALSE;
}
/* 0-9校验 */
static BOOLEAN IsDigit(BYTE element)
{
    return (element>='0' && element<='9');
}

BOOLEAN CheckDigit(BYTE arr[], BYTE size)
{
    for(BYTE i=0; i<size; i++){
        if(arr[i]==0x00){
            break;
        }
        if(!IsDigit(arr[i])){
            return 0;
        }
    }
    return 1;
}

BOOLEAN IsUpperLetter(BYTE ucElement)
{
    return ((ucElement >= 'A') && (ucElement <= 'Z'));
}

/*********************************************************************************************
 * @brief    寻找数组中符号'-'的第一次出现的位置
 * @param    arr-需要进行校验的数组
 * @param    size-需要校验的数组的大小
 * @return   <0：未找到符号'-'，>=0：符号'-'的第一次出现的位置
 ********************************************************************************************/
SHORT FindSympolPosition(CHAR arr[], BYTE ucSize)
{
    if(arr == NULL || ucSize == 0)
    {
        return -1;
    }

    for(BYTE i = 0; i < ucSize; i++)
    {
        if(arr[i] == '-')
        {
            return i;
        }
    }

    return -1;
}

/***************************************************************************
 * @brief    校验PACK厂家或电芯厂家信息格式（格式为XXX-*，XXX为若干位大写字母A-Z，*为一位大写字母A-Z）
 * @param    arr-需要进行校验的数组
 * @param    size-需要校验的数组的大小（一般为20）
 * @return   True-有效，False-无效
 **************************************************************************/
BOOLEAN CheckPackOrCellInfo(CHAR arr[], BYTE ucSize)
{
    SHORT sSymbolPos = -1;

    if(arr == NULL || ucSize != 20)
    {
        return False;
    }

    sSymbolPos = FindSympolPosition(arr, ucSize);

    if(sSymbolPos <= 0 || sSymbolPos >= ucSize - 2) //这里SymbolPos不能等于size - 2，防止1104协议获取PACK和电芯厂家时会出现BUG
    {
        return False;
    }

    for(BYTE i = 0; i < sSymbolPos; i++) // 符号'-'之前全部为大写字母
    {
        if(!IsUpperLetter(arr[i]))
        {
            return False;
        }
    }

    if(!IsUpperLetter(arr[sSymbolPos + 1])) // 符号'-'之后一位为大写字母
    {
        return False;
    }

    for(BYTE i = sSymbolPos + 2; i < ucSize; i++) // 数组末尾全部为空（0x00）
    {
        if(arr[i] != 0x00)
        {
            return False;
        }
    }

    return True;
}

/***************************************************************************
 * @brief    校验PACK厂家和电芯厂家信息格式（格式为XXX-**，XXX为若干位大写字母A-Z，**为一位大写字母A-Z）
 * @param    arr-需要进行校验的数组
 * @param    size-需要校验的数组的大小（一般为20）
 * @return   True-有效，False-无效
 **************************************************************************/
BOOLEAN CheckPackAndCellFormat(CHAR arr[], BYTE ucSize)
{
    SHORT sSymbolPos = -1;

    if((arr == NULL) || (ucSize != 20))
    {
        return False;
    }

    sSymbolPos = FindSympolPosition(arr, ucSize);

    if(sSymbolPos <= 0 || sSymbolPos >= ucSize - 2)
    {
        return False;
    }

    for(BYTE i = 0; i < sSymbolPos; i++) // 符号'-'之前全部为大写字母
    {
        if(!IsUpperLetter(arr[i]))
        {
            return False;
        }
    }

    if((!IsUpperLetter(arr[sSymbolPos + 1])) || (!IsUpperLetter(arr[sSymbolPos + 2]))) // 符号'-'之后两位为大写字母
    {
        return False;
    }

    for(BYTE i = sSymbolPos + 3; i < ucSize; i++) // 数组末尾全部为空（0x00）
    {
        if(arr[i] != 0x00)
        {
            return False;
        }
    }

    return True;
}

//校验电芯循环次数范围(500-10000)
BOOLEAN CheckCellCycleTimesValid(WORD wTimes)
{
    if(wTimes < MIN_CELL_CYCLE_TIMES || wTimes > MAX_CELL_CYCLE_TIMES)
    {
        return False;
    }

    return True;
}


BOOLEAN CheckCharRangeAndLength(BYTE *p, BYTE ucLength, BYTE ucCounter)
{
    BYTE i;
    BYTE ucIndex = ucLength;
    if (p == NULL || ucLength == 0)
    {
        return False;
    }
    for (i = 0; i < ucLength; i++)
    {
        // 检查字节是否为0x00
        if (p[i] == 0x00)
        {
            if (ucIndex == ucLength)
            {
                ucIndex = i;
            }
            continue;
        }
        // 检查字节是否在有效范围内
        else if ((p[i] >= 0x20 && p[i] <= 0x3B && p[i] != 0x22) || (p[i] >= 0x40 && p[i] <= 0x7D && p[i] != 0x60) || (p[i] == 0x3D))
        {
            continue;
        }
        else
        {
            return False;
        }
    }
    return (ucIndex >= ucCounter);
}


BOOLEAN CheckCharRange(BYTE *p, BYTE ucCounter)
{
    BYTE i;
    if (p == NULL || ucCounter == (BYTE)0)
    {
        return False;
    }
    for (i = 0; i < ucCounter; i++)
    {
        if ((p[i] >= 0x20 && p[i] <= 0x3B && p[i] != 0x22) || (p[i] >= 0x40 && p[i] <= 0x7D && p[i] != 0x60) ||
            p[i] == 0x3D || p[i] == (BYTE)0x00)
        {
        }
        else
        {

            return False;
        }
    }
    return True; //正确返回
}


/* DO NOT MODIFY THIS! For bullshit ut only */
#ifndef errno
#define errno *_rt_errno()
#endif

BOOLEAN ParseUint32(uint32_t *value, const char *str)
{
    unsigned long long result = 0ULL;

    if (value == NULL || str == NULL)
    {
        return False;
    }

    errno = 0;
    char *end = NULL;
    result = strtoull(str, &end, 10);

    if (errno != 0)
    {
        errno = 0;
        return False;
    }
    if (end != NULL && *end != '\0')
    {
        return False;
    }
    if (result > UINT32_MAX)
    {
        return False;
    }

    *value = result;
    return True;
}


BOOLEAN CheckDiscreteValueRange(void* p, void* ucDiscreteArray, BYTE ucNumber, size_t size)
{
    BYTE i;
    if (p == NULL || ucDiscreteArray == NULL || ucNumber == (BYTE)0)
    {
        return False;
    }

    for (i = 0; i < ucNumber; i++)
    {
        if (rt_memcmp(p, (char*)ucDiscreteArray + i * size, size) == 0)
        {
            return True;
        }
    }

    return False;
}

WORD* getCrcTable(WORD* len){
    *len = sizeof(s_awCRC_Table);
    return s_awCRC_Table;
}



/**
 * @brief 校验数组是否全部为零
 * @param arr - 需要进行校验的数组
 * @param size - 需要校验的数组的大小
 * @return True - 数组有效（即全部为零）
 *          False - 数组无效（即存在非零元素）
 */
BYTE IsAllZeros(BYTE arr[], int size) {
    for (int i = 0; i < size; ++i) {
        if (arr[i] != 0) {
            return FALSE; // 发现非零元素，立即返回False
        }
    }
    return TRUE; // 所有元素都为零，返回True
}



/***************************************************************************
 * @brief    判断数组元素是否都为0
 * @param    arr-需要进行校验的数组
 * @param    size-需要校验的数组的大小
 **************************************************************************/

BOOLEAN CheckArrAllZero(BYTE arr[], BYTE size)
{
    for(BYTE i = 0; i < size; i++)
    {
        if(arr[i] != 0)
        {
            return False;
        }
    }

    return True;
}


/***************************************************************************
 * @brief    用于对字符数组做变换
 * @param    rawArr-需要进行变换的数组
 * @param    resultArr-结果数组
 * @param    size-需要变换的数组的大小
 **************************************************************************/

BOOLEAN charTransfer(BYTE *rawArr, BYTE *resultArr, BYTE size)
{
    if((NULL == rawArr) || (NULL == resultArr)){return False;};
    BYTE pointer = 0;
    BYTE transferString[] = "bms-r321";
    BYTE transferChar = 0;
    BYTE offset = 0;

    for(BYTE i = 0; i < size; i++)
    {
        pointer = i % (sizeof(transferString) - 1);
        //将字符映射到0~9和a~e之间
        offset = (rawArr[i] + transferString[pointer]) % 15;

        if (offset < 10) 
        {
            transferChar = '0' + offset; 
        } 
        else 
        {
            transferChar = 'a' - 10 + offset; 
        }

        resultArr[i] = transferChar;
    }
    
    return True;
}




/***************************************************************************
 * @brief    用于对剔除字符串当中的‘.’和‘V’字符
 * @param    rawArr-处理之前的字符串
 * @param    srcLen-源串的长度
 * @param    resultArr-结果串
 * @param    dstLen-结果串长度
 * @param    defaultValue-默认值
 **************************************************************************/

BYTE removeDot(BYTE *rawArr, BYTE srcLen, BYTE *resultArr, BYTE dstLen, BYTE defaultValue)
{
    if((NULL == rawArr) || (NULL == resultArr)){return False;}

    BYTE pointer = 0;

    rt_memset_s(resultArr, dstLen, defaultValue, dstLen); //根据传入的默认值对结果数组内容初始化

    for(BYTE i = 0; i < srcLen; i++)
    {
        if(rawArr[i] != '.')
        {
            resultArr[pointer++] = rawArr[i];
        }
    }

    return pointer;
}


/***************************************************************************
 * @brief    用于检查日期是否全为0
 **************************************************************************/

BOOLEAN CheckDateAllZero(T_DateStruct *Date)
{
    if(NULL == Date){return False;}
    
    if(Date->wYear == 0 && Date->ucMonth == 0 && Date->ucDay == 0)
    {
        return True;
    }

    return False;
}


