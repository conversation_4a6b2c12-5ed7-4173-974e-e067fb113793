#include "storage.h"
#include "dev_update_manage_handle.h"
#include "dev_north_update_tab.h"
#include "parse_layer.h"
#include "protocol_bottom_comm.h"
#include "utils_rtthread_security_func.h"
#include "utils_data_transmission.h"
#include "partition_def.h"
#include "cmd.h"
#include "rthw.h"

rt_timer_t  g_update_timer = NULL;
trig_ctr_inf_t g_trig_ctr_info = {0};
bottom_update_manage_t g_update_manage_info = {0}; 
static cmd_handle_register_t s_bottom_update_cmd_handle[] = {
    {DEV_NORTH_IDUB_UPDATE, UPDATE_TRIG_FRAME,         CMD_TYPE_NO_POLL, parse_update_trig, pack_update_trig},
    {DEV_NORTH_IDUB_UPDATE, TRAN_DATA_FRAME,           CMD_TYPE_NO_POLL, parse_update_data, pack_update_data},
    {0} // 结束符
};

int bottom_update_init(void) {
    int i = 0;
    for(i = 0; i < sizeof(s_bottom_update_cmd_handle)/sizeof(s_bottom_update_cmd_handle[0]); i++) 
    {
        register_cmd_handle(&s_bottom_update_cmd_handle[i]);
    }
    g_update_timer = rt_timer_create("ctrl_update_timer", time_out_ctrl_update, NULL, 30000, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
    if(g_update_timer == NULL)
    {
        return FAILURE;
    }
    return SUCCESSFUL;
}

void time_out_ctrl_update()
{   
    rt_timer_stop(g_update_timer);
    g_trig_ctr_info.trig_success = FALSE;
    g_trig_ctr_info.trig_times = 0;
    //触发失败写操作记录
    return;
}


int parse_update_trig(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    rt_timer_start(g_update_timer);
    g_trig_ctr_info.trig_success = FALSE;
    if(rt_strncmp((char*)(tran_cmd_buf->buf), UPDATE_FILE_NAME, UPDATE_FILE_NAME_LEN) != 0)
    {
        tran_cmd_buf->rtn = BOTTOM_RTN_DOWN_FILE_NAME_ERR;
        return SUCCESSFUL;
    }

    g_trig_ctr_info.trig_times++;
    //触发成功
    if(g_trig_ctr_info.trig_times >= UPDATE_TRIG_COUNT){
        g_trig_ctr_info.trig_success = TRUE;
    }
    return SUCCESSFUL;
}

int pack_update_trig(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    rt_memset_s(tran_cmd_buf->buf, UPDATE_FILE_NAME_LEN, 0x00, UPDATE_FILE_NAME_LEN);
    rt_memcpy_s(tran_cmd_buf->buf, UPDATE_FILE_NAME_LEN, UPDATE_FILE_NAME, sizeof(UPDATE_FILE_NAME));
    tran_cmd_buf->data_len = UPDATE_FILE_NAME_LEN;
    return SUCCESSFUL;
}

int parse_update_data(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    bottom_comm_cmd_head_t *head = tran_cmd_buf->cmd->req_head;
    rt_timer_start(g_update_timer);
    //校验是否触发成功
    if(g_trig_ctr_info.trig_success == FALSE)
    {
        tran_cmd_buf->rtn = BOTTOM_RTN_DOWN_EXEC_FAIL;
        return SUCCESSFUL;
    }

    if(head->append == 0)//首发帧
    {
        if(handle_storage(read_opr, ONCHIP_UPDATE_INFO, (unsigned char*)&g_update_manage_info, sizeof(bottom_update_manage_t), 0) != SUCCESSFUL)
        {
            tran_cmd_buf->rtn = BOTTOM_RTN_DOWN_EXEC_FAIL;
            return SUCCESSFUL;
        }
        parse_first_frm(tran_cmd_buf);
    }
    else
    {
        parse_data_frm(dev_inst, tran_cmd_buf, head->append);
    }
    return SUCCESSFUL;
}
int pack_update_data(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    bottom_comm_cmd_head_t* head = NULL;
    
    // 入参校验
    if (tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL) {
        return FAILURE;
    }

    tran_cmd_buf->data_len = 0;
    head = (bottom_comm_cmd_head_t*)tran_cmd_buf->cmd->ack_head;
    head->append = g_update_manage_info.cur_frame_no;
    return SUCCESSFUL;
}
int parse_first_frm(cmd_buf_t* tran_cmd_buf)
{
    bottom_update_info_t file_info = {0};
    //data域解析
    if(tran_cmd_buf->data_len != 72 ) {
        tran_cmd_buf->rtn = BOTTOM_RTN_DOWN_EXEC_FAIL;
        rt_kprintf("data_len:%d\n", tran_cmd_buf->data_len);
        return SUCCESSFUL;
    }

    file_info.per_frame_len = get_int16_data(&tran_cmd_buf->buf[0]);
    file_info.total_frames = get_int16_data(&tran_cmd_buf->buf[2]);
    file_info.total_leng = get_ulong_data(&tran_cmd_buf->buf[4]);
    if(file_info.total_leng > APP_DOWNLOAD_SIZE)
    {
        rt_kprintf("total_leng:%d\n", file_info.total_leng);
        tran_cmd_buf->rtn = BOTTOM_RTN_DOWN_EXEC_FAIL;
        return SUCCESSFUL;
    }
    rt_memcpy_s(file_info.file_name, UPDATE_FILE_NAME_LEN, &tran_cmd_buf->buf[8], UPDATE_FILE_NAME_LEN);
    rt_memcpy_s(file_info.file_time, UPDATE_FILE_TIME_LEN, &tran_cmd_buf->buf[48], UPDATE_FILE_TIME_LEN);
    file_info.file_crc = get_int16_data(&tran_cmd_buf->buf[68]);
    if(rt_strncmp((char *)(file_info.file_name), UPDATE_FILE_NAME, UPDATE_FILE_NAME_LEN) != 0)
    {
        rt_kprintf("file_name:%d\n", file_info.file_name);
        tran_cmd_buf->rtn = BOTTOM_RTN_DOWN_FILE_NAME_ERR;
        return SUCCESSFUL;
    }
    //断点续传
    if (file_change_check(&(g_update_manage_info.file_info),&file_info) == TRUE) {
        rt_memset_s(&(g_update_manage_info.file_info), sizeof(bottom_update_info_t), 0x00, sizeof(bottom_update_info_t));
        rt_memcpy_s(&(g_update_manage_info.file_info), sizeof(bottom_update_info_t), &file_info, sizeof(bottom_update_info_t));
        g_update_manage_info.data_offset = 0;
        g_update_manage_info.cur_frame_no = 1;
    }
    tran_cmd_buf->rtn = BOTTOM_RTN_APP_CORRECT;
    write_update_info(&g_update_manage_info);
    return SUCCESSFUL;
}

int parse_data_frm(void* dev_inst, cmd_buf_t* cmd_buf, unsigned short frm_no)
{
    cmd_buf_t send_cmd_buff;
    rt_memset_s(&send_cmd_buff, sizeof(cmd_buf_t), 0, sizeof(cmd_buf_t));
    //是不是期待的数据帧号
    if (frm_no != g_update_manage_info.cur_frame_no)
    {
        cmd_buf->rtn = BOTTOM_RTN_DOWN_EXEC_FAIL;
        return SUCCESSFUL;
    }
    if(frm_no == 1)//擦除分区
    {
        if(handle_storage(erase_opr, ONCHIP_DOWNLOAD, NULL, 0, 0) != SUCCESSFUL)
        {
            cmd_buf->rtn = BOTTOM_RTN_DOWN_EXEC_FAIL;
            return SUCCESSFUL;
        }
    }
    if(handle_storage(write_opr, ONCHIP_DOWNLOAD, cmd_buf->buf, cmd_buf->data_len, g_update_manage_info.data_offset) != SUCCESSFUL)
    {
        cmd_buf->rtn = BOTTOM_RTN_DOWN_EXEC_FAIL;
        return SUCCESSFUL;
    }
    //更新数据    （文件累加crc暂时不计算）
    g_update_manage_info.data_offset += cmd_buf->data_len;
    g_update_manage_info.cur_frame_no++;
    cmd_buf->rtn = BOTTOM_RTN_APP_CORRECT;
    if(frm_no % 10 == 0)
    {
        write_update_info(&g_update_manage_info);
    }
    if(g_update_manage_info.cur_frame_no == g_update_manage_info.file_info.total_frames)//最后一帧
    {
        rt_kprintf("update success!!!!!!\n");
        g_update_manage_info.count = 0;
        g_update_manage_info.backup_flag = FALSE;
        g_update_manage_info.sys_run_flag = FALSE;
        g_update_manage_info.update_flag = FLAG_APP_UPDATE_APP;
        write_update_info(&g_update_manage_info);
        send_cmd_buff.cmd = cmd_buf->cmd;
        cmd_send(dev_inst, &send_cmd_buff);
        rt_thread_mdelay(5000);
        rt_hw_cpu_reset();
    }
    return SUCCESSFUL;
}

int file_change_check(bottom_update_info_t *pold_file, bottom_update_info_t *pnew_file) {
    if ((rt_strncmp((char*)(pold_file->file_name), (char*)(pnew_file->file_name), UPDATE_FILE_NAME_LEN) == 0) && \
        (rt_strncmp((char*)(pold_file->file_time), (char*)(pnew_file->file_time), UPDATE_FILE_TIME_LEN) == 0) && \
        (pold_file->total_frames == pnew_file->total_frames) && \
        (pold_file->total_leng == pnew_file->total_leng) && \
        (pold_file->per_frame_len == pnew_file->per_frame_len)) {
        return FALSE;
    } else {
        return TRUE;
    }
}

int write_update_info(bottom_update_manage_t* update_manage_info) {
    bottom_update_manage_t check_update_info = {0};

    if (NULL == update_manage_info)
        return FAILURE;
    update_manage_info->crc = crc_cal((unsigned char *)update_manage_info, sizeof(bottom_update_manage_t)-2);
    if(handle_storage(erase_write_opr, ONCHIP_UPDATE_INFO, (unsigned char*)update_manage_info, sizeof(bottom_update_manage_t), 0) != SUCCESSFUL)
    {
        return FAILURE;
    }
    //检验升级信息是否写成功
    if(handle_storage(read_opr, ONCHIP_UPDATE_INFO, (unsigned char*)&check_update_info, sizeof(bottom_update_manage_t), 0) != SUCCESSFUL)
    {
        return FAILURE;
    }
    if(check_update_info.crc != update_manage_info->crc)
    {
        rt_kprintf("update Info error | write crc:%d read crc:%d\n",update_manage_info->crc, check_update_info.crc);
        return  FAILURE;
    }
    return SUCCESSFUL;
}