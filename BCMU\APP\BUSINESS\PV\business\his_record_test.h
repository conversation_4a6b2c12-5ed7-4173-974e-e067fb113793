#ifndef _PV_INVERTER_HIS_RECORD_TEST_H
#define _PV_INVERTER_HIS_RECORD_TEST_H

#ifdef __cplusplus
extern "C" {
#endif
#include "utils_time.h"
#include "his_data.h"
#include "energy_accu.h"

#define TIME_GAP (60 * 5)               // 每次写记录增加的时间

/**
 * 历史告警协议上送数据结构
 *
 */
typedef struct {
    time_base_t     start_time;     //告警开始时间
    time_base_t     end_time;       //告警结束时间
    unsigned short  alm_code;       //告警码
} his_alm_test_t;

// 
typedef struct 
{
    int (*save_sample_data_func)(unsigned char* buff);
}reg_record_test_t_func_t;

int save_his_alarm_for_test(unsigned short save_num);
int gen_test_sample_data(unsigned short save_num);
int gen_test_event_record(unsigned short save_num);
void register_test_his_record_tab(his_record_t* his_record, int (*func)(unsigned char*));
void gen_energy_record_test_data();
short save_test_his_data(void* his_data, time_base_t* time);
int save_one_data(char* name,energy_record_t* data);
void save_one_energy_record(int type ,energy_record_t* data );
void save_exclude_lastyear_all_data();
float save_last_year_day_hour_data();
void save_last_year_month_day_data(float last_month);
#ifdef __cplusplus
}
#endif

#endif  // _PV_INVERTER_HIS_RECORD_TEST_H
