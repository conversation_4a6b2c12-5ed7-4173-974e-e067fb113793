#ifndef DATA_PROCESS_H_  
#define DATA_PROCESS_H_  
#ifdef __cplusplus
extern "C" {
#endif

#define ERR_CODE_NUM                  4 //错误码数量

typedef enum
{
    INTER_FAN_TYPE = 0,              // 内风扇
    OUTER_FAN_TYPE,                  // 外风扇
}fan_type_e;

typedef enum
{
    UINT_TYPE = 0,
    INT_TYPE,
    FLOAT_TYPE,
}data_type_e;

typedef struct
{
    int north_sid;
    data_type_e north_data_type;
    int south_sid;
    data_type_e south_data_type;
}south_north_data_t;

typedef struct
{
    unsigned int sid;
    int dev_offset;
}code_tab_t;

typedef struct
{
    int baud_rate_sid;
    int parity_bit_sid;
    char* usart_name;
}usart_para_t;

int deal_invalid_fan_data(unsigned short fan_no, fan_type_e fan_type);
int deal_invalid_vfd_data(unsigned short vfd_no);
int deal_invalid_south_data(unsigned short inter_fan_num, unsigned short out_fan_num, unsigned short compressor_num);
int trans_south_to_north(south_north_data_t* data_map, unsigned short vfd_no);
int deal_vfd_data(unsigned short compressor_num, unsigned short compressor_brand);
int fan_sync_data(unsigned short internal_fan_count, unsigned short external_fan_count);
int init_usart_commu_para();
#ifdef __cplusplus
}  
#endif

#endif
