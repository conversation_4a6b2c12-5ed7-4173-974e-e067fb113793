/*******************************************************************************
  * @file        batt_sox_calc.c
  * @version     v1.0.0
  * @copyright   COPYRIGHT &copy; 2023 CSG
  * <AUTHOR>
  * @date        2023-4-27
  * @brief
  * @attention
  * Modification History
  * DATE         DESCRIPTION
  * ------------------------
  * - 2023-4-27  dongmengling Created
*******************************************************************************/

#include "utils_data_valid.h"
#include "data_type.h"
#include "utils_math.h"
#include "batt_sox_calc.h"
#include "device_num.h"
#include "utils_time.h"


static float calc_SOH_from_decline(batt_deal_info_t* batt_deal);
static void plus_calender_life_decline(batt_deal_info_t *batt_deal,float cell_average_temp);
static void deal_calender_decline_when_start(batt_deal_info_t* batt_deal);




/**
 * @brief  初始化SOC和SOH
 * @param[in] batt_save_data   保存的电池信息
 * @param[out] soc  
 * @param[out] soh
 * @retval
 */
void init_sox(batt_save_t* batt_save_data, batt_deal_info_t* batt_deal) {
    batt_deal->cycle_life_decline = batt_save_data->cycle_life_decline;
    batt_deal->calender_life_decline = batt_save_data->calender_life_decline;
    batt_deal->added_life_decline = batt_save_data->added_life_decline;

    deal_calender_decline_when_start(batt_deal);
    ////软件重启以后起来不置满，不做SOH修正
    batt_deal->batt_SOH = calc_SOH_from_decline(batt_deal);
    batt_deal->batt_cap = MIN(batt_save_data->batt_cap, BATT_SOC_MAX*(batt_deal->batt_SOH / 100 * BATT_CAP)) ;
}

static void deal_calender_decline_when_start(batt_deal_info_t* batt_deal)
{
    int i = 0;
    int diff_days = get_diff_days(&batt_deal->last_calc_calender_time);

    if (diff_days > ONE_YEAR_DAYS || diff_days < 1)
    {
        return;
    }

    for (i=0; i<diff_days; i++)
    {////起始时不知道电芯温度，以35度计算
        plus_calender_life_decline(batt_deal, 35.0);
    }
}



/************************计算SOC***********************************************/

float ocv_to_soc(float ocv)
{ // 25℃时标准OCV-SOC曲线(0-8%校准值)
    float soc = 0.0;
    ocv = 0.65 * ocv + 1.12; // 将其他温度下OCV拟合转化为25摄氏度OCV减少温度影响
    //此处，不同电池则使用不同拟合曲线。
    soc = 132.98*ocv*ocv*ocv -1147.5*ocv*ocv + 3307.1*ocv - 3182.4; //光宇拟合曲线
//  soc = 46.492*ocv*ocv*ocv -377.41*ocv*ocv + 1024.1*ocv - 927.9;  //统一拟合曲线
    return get_valid_data(soc, 8.0f, 0.0f);
}

//SOC:相关参数 1、SOH      2：标称容量 3:精度
float calc_soc_from_cap(batt_deal_info_t* batt_deal)
{
    unsigned short batt_rate_cap = BATT_CAP ; //从参数获取
    float soc = (batt_deal->batt_cap / (batt_deal->batt_SOH * batt_rate_cap / 100 )) *100;
    return MIN(soc, 100);
}


/************************计算SOH***********************************************/

void calc_batt_SOH(batt_deal_info_t* batt_deal) {
    //todo:增加计算SOH
    batt_deal->batt_SOH = 50.2;
}

//4、SOH计算方法 ： 包含1、日历衰减 2、循环衰减 3、附加衰减
 static float calc_SOH_from_decline(batt_deal_info_t* batt_deal)
{
    float batt_SOH    = (100.0 - batt_deal->calender_life_decline //日历衰减
                               - batt_deal->cycle_life_decline    //循环衰减
                               + batt_deal->added_life_decline);  //附加衰减

    return get_valid_data(batt_SOH, 100.0, 45.0);//SOH的范围在45到100之间为有效值
}

//2、日历衰减
static void plus_calender_life_decline(batt_deal_info_t *batt_deal,float cell_average_temp)
{
    cell_average_temp = get_valid_data(cell_average_temp , 10.0, 45.0);
    batt_deal->calender_life_decline += 10.0 / (27.5-0.5 * cell_average_temp) / 365; //每次日历衰减值
    get_time(&batt_deal->last_calc_calender_time);
    batt_deal->save_eeprom = TRUE;
}




