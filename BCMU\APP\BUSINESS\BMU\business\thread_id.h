#ifndef _THREAD_ID_H_  
#define _THREAD_ID_H_  
#ifdef __cplusplus
extern "C" {
#endif


/*  ###后续根据该产品修改该文件###  */


#define HEART_BEAT_TIME_LIMIT 180000
/* 预心跳，即将进入主循环 */
#define PRE_HEART_BEAT_FLAG   1

/* 线程id定义 */
typedef enum {
    THREAD_SAMPLE = 0,            ///< 采样模块
    THREAD_LED,                   ///< LED模块
    THREAD_CAN_COMM,
    THREAD_CAN2_COMM,
    THREAD_USART_COMM, 
    THREAD_HISDATA,
    // THREAD_WIRELESS,
    // THREAD_NET1104,         /// 因这两个线程中需要采用阻塞等待，线程心跳先屏蔽
    THREAD_GSENSOR,
    THREAD_ALARM_MANAGE,      ///< 告警管理模块
    THREAD_BATT_MANAGE,       ///< 电池管理模块
    THREAD_BDUCOMM,
    THREAD_ALARM_TRAP,
    THREAD_BACKUP,
    THREAD_BDU_RECORD,

    THREAD_MAX,
}thread_mod_t;
       
#ifdef __cplusplus
}  
#endif

#endif  // _THREAD_ID_H_  