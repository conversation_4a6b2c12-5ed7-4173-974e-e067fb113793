#include "msg.h"

Static mod_mq_list_t* s_mq_list; 
Static mod_mq_list_t* cur_mq; 
Static mod_mq_list_t* get_mod_mq(module_id_e  mod_id);


rt_mq_t init_msg_queue(module_id_e mod_id) {
    char mq_name[10];
    mod_mq_list_t* mq_list = NULL;

    mq_list = get_mod_mq(mod_id);
    if (mq_list != NULL)
        return mq_list->mod_mq;
    
    rt_snprintf(mq_name, sizeof(mq_name), "mq_%d", (int)mod_id);
    mq_list = (mod_mq_list_t*)rt_malloc(sizeof(mod_mq_list_t));
    if(mq_list == NULL)
    {
        return NULL;
    }
    mq_list->mod_id = mod_id;
    mq_list->next = NULL;
    mq_list->mod_mq = rt_mq_create(mq_name, sizeof(module_msg_t), MQ_LEN, RT_IPC_FLAG_PRIO);
    if (mq_list->mod_mq == RT_NULL) {
        rt_free(mq_list);
        return NULL;
    }

    if (s_mq_list == NULL) {
        s_mq_list = mq_list;
        cur_mq = s_mq_list;
    } else {
        cur_mq->next = mq_list;
        cur_mq = mq_list;
    }
    return cur_mq->mod_mq;
}


Static mod_mq_list_t* get_mod_mq(module_id_e  mod_id) {
    mod_mq_list_t* mq_list = s_mq_list;
    
    while(mq_list != NULL) {
        if (mod_id == mq_list->mod_id)
            return mq_list;
        
        if (mq_list->next == NULL)
            break;
        mq_list = (mod_mq_list_t*)mq_list->next;
    }
    
    LOG_E("mod id %d was not found\n", mod_id);
    return NULL;
}


short send_msg(module_msg_t* msg) {
    rt_err_t result;
    mod_mq_list_t* mod_mq;

    mod_mq = get_mod_mq(msg->dest);
    if (mod_mq == NULL)
        return FAILURE;
	
    result = rt_mq_send(mod_mq->mod_mq, msg, sizeof(module_msg_t));
    if (result != RT_EOK) {
        LOG_E("send msg to mod_id %d ERR %d\n", mod_mq->mod_id, result);
        return FAILURE;
    }
    
    return SUCCESSFUL;
}


short recv_msg(module_msg_t* msg) {
    mod_mq_list_t* mod_mq;

    mod_mq = get_mod_mq(msg->dest);
    if (mod_mq == NULL)
        return FAILURE;

    if (rt_mq_recv(mod_mq->mod_mq, msg, sizeof(module_msg_t), 0) != RT_EOK) {
        return FAILURE;
    }
    return SUCCESSFUL;
}

