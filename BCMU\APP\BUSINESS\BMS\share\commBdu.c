#include "fileSys.h"
#include "common.h"
#include "sample.h"
#include "utils_heart_beat.h"
#include "thread_id.h"
#include "realAlarm.h"
#include "battery.h"
#include "para.h"
#include "protocol.h"
#include "CommCan.h"
#include "Candrive.h"
#include "stdio.h"
#include "commBdu.h"
#include "bduRecord.h"
#include "usart.h"
#include "prtclDL.h"
#include "rtthread.h"
#include "utils_rtthread_security_func.h"
#include "interface.h"

#define BDU_STATUS_NUM      14
#define BDU_STATUS_ERROR    0xFF

#define ONE_SECOND_SMOOTH   20//1s
#define CURRENT_SHARE_MASK  10


Static T_CommManageStruct   s_tCommManage;
static T_BduCmdQueueStruct  s_tBduCmdQueue;

Static BYTE s_ucBduAdjCnt = GET_BDU_ADJ_VAl_DELAY;
Static BYTE s_ucBduComSts = SCI_COMM_SUCCESS;
Static WORD s_wFrmNum = 0;
Static WORD s_awBusVolt[ONE_SECOND_SMOOTH] = {0};

Static T_DCFactory  s_tDCFactory;
Static T_DCAsset    s_tDCAsset;
static T_CurrBalanceInfo s_tCurrBalanceInfo;
static T_DCRealData s_tDCReal;

static T_DCTestData s_tDCTestData;
Static T_DCTestPara s_tDCTestPara;

Static T_DCPara     s_tDCPara;    // 设置参数用
static T_DCPara     s_tDCParaIn;  // 获取参数用，功率反馈
Static T_DCCtrl     s_tDCCtrl;    // 控制命令下发记录
Static T_DCCtrl     s_tDCCtrlRet; // 控制命令执行结果

Static T_DCCaliGet  s_tDCCaliGet;
Static T_DCCaliSet  s_tDCCaliSet;

Static T_DCMemQuery s_tDCMemoSet;
static T_DCMemQuery s_tDCMemoGet;

static uint32_t s_ulDiagStatus = 0;
static BYTE     s_ucCurrShareCnt = 0;

Static BYTE s_ucRecordWait = False;
Static WORD s_wRecordTimer = 0;
Static WORD s_wRecordSaveCnt = 0;

Static T_BduRecordPara s_tBduRecordPara;
Static T_BduRecordData s_tBduRecordData;

Static BYTE s_ucParalleUpdateFlag = False;
Static WORD s_wCnt = 0;

static void SciDCSetRecordParaRsp(BYTE ucPort);
static void CheckRecordParaChange( WORD wCrc );
Static void SciDCRecordOrigin( BYTE ucPort);
Static void SciDCRecordData( BYTE ucPort);
Static void FaultRecordSave(void);
static void SynRecordPara(void);

// 获取/查询方式
// 获取BDU实时数据：PollBdu中，周期性更新
// 查询BDU出厂信息: PollBdu中，开机获取一次
static void SciDCGetPara(void);             // 查询BDU参数
static void SciDCGetAdj(void);              // 查询校正参数
Static void ReGetBduAdjVal(void);

static void CheckTestParaChange( WORD wCrc );
static void CheckParaChange( WORD wCrc );   // 检查参数是否一致或发生变化

// 获取/查询响应
static void SciDCGetRealRsp(BYTE ucPort);   // 获取BDU实时数据响应函数

static void SciDCGetTestDataRsp(BYTE ucPort);   // 获取BDU测试数据响应函数
Static void SciDCSetTestParaRsp(BYTE ucPort);   // 设置BDU测试数据响应函数

static void SciDCGetParaRsp(BYTE ucPort);   // 查询BDU参数响应函数
static void SciDCGetFactRsp(BYTE ucPort);   // 查询BDU出厂信息响应函数
static void SciDCGetAdjRsp(BYTE ucPort);    // 查询校正参数响应函数
static void SciDCGetMemRsp(BYTE ucPort);    // 查询调测信息响应函数

// 设置/控制响应
static void SciDCSetParaRsp(BYTE ucPort);   // 设置BDU参数响应函数
static void SciDCSetAssetRsp(BYTE ucPort);  // 设置BDU资产信息响应函数
static void SciDCSetAdjRsp(BYTE ucPort);    // 设置校正参数响应函数
static void SciDCSetMemRsp(BYTE ucPort);    // 设置调测信息响应函数

Static void SciDCCtrlRsp(BYTE ucPort);      // 控制命令响应函数

Static void SendBduUpdateData(void);
Static void BduCmdStageManage( void );
Static void DealBduCommData(T_CommStruct *ptComm);
Static void DealBduCommCmd( BYTE ucPort );
static void SendBduCmd(void);

static BOOLEAN QueueFull( T_BduCmdQueueStruct *pQueue );
static BYTE SetCmdToQueue( T_BduCmdQueueStruct *pQueue, T_BduCmdStruct *pCmd );
static BYTE SetCmd( T_BduCmdStruct *pCmd );
static void PollBdu( void );
static BYTE GetCmdFromQueue( T_BduCmdQueueStruct *pQueue, T_BduCmdStruct *pCmd );
static BYTE GetCmd( T_BduCmdStruct *pCmd );

Static WORD GetBduIntData(BYTE *p);
Static void DealBduUpdateRsp(void);

static void RefreshCurrBalanceInfo(void);

Static BOOLEAN IsUpdateComplete(void);
Static BOOLEAN IsParalleUpdateComplete(void);
Static BOOLEAN ClearBduCommFail(void); //清除 BDU 通信断标志

/////////////////// 采样校正通道映射表 ///////////////////
/*
wBusVoltScal;         // 母排侧电压校正，校正通道6
wBusPinVoltScal;      // 母排侧PIN+/-防反接电压比例校正，校正通道7
wBatVoltScal;         // 电池侧电压（BAT_DET）校正，校正通道1
wBusChaCurrScal;      // 母排侧充电电流校正，校正通道8
wBusDischaCurrScal;   // 母排侧放电电流校正，校正通道9
wBatChaCurrScal;      // 电池侧充电电流校正，校正通道3
wBatDischaCurrLScal;  // 电池侧放电80A电流校正，校正通道4
wBatDischaCurrHScal;  // 电池侧放电200A电流校正，校正通道5
*/
const uint8_t aucBduAdjTable[8] = {6, 7, 1, 8, 9, 3, 4, 5};

const uint8_t acBduStsConvert[BDU_STATUS_NUM] =
{
    BDU_STATUS_COMMON_CHARGE,      //0: 常规充电
    BDU_STATUS_STRAIGHT_CHARGE,    //1: 直通充电
    BDU_STATUS_COMMON_DISCHARGE,   //2: 常规放电
    BDU_STATUS_STRAIGHT_DISCHARGE, //3: 直通放电
    BDU_STATUS_BOOST_CHARGE,       //4: 升压充电
    BDU_STATUS_ERROR,              //5: 异常状态
    BDU_STATUS_BOOST_DISCHARGE,    //6: 升压放电
    BDU_STATUS_ERROR,              //7: 异常状态
    BDU_STATUS_BUCK_CHARGE,        //8: 降压充电
    BDU_STATUS_ERROR,              //9: 异常状态
    BDU_STATUS_BUCK_DISCHARGE,     //10:降压放电
    BDU_STATUS_ERROR,              //11:异常状态
    BDU_STATUS_STOP,               //12:充放电停止
    BDU_STATUS_FAILURE             //13:闭锁
};

//硬件版本信息map
const T_HardwareVerStruct s_atHardwareVerAttr[] = {
//  {PE(防盗), 陀螺仪, FE(网络), DO(干接点), 485/CAN, PWR(激活口), 4G/GPS, 加热膜, CAN2, 消防, 预留位,}
    {       1,      1,        1,          1,       1,           1,      0,      0,    0,    0,      0,},//1：PE+陀螺仪+FE+DO+485/CAN+PWR
    {       1,      1,        1,          1,       1,           1,      1,      0,    0,    0,      0,},//2：PE+陀螺仪+FE+DO+4G/GPS+485/CAN+PWR
    {       1,      1,        1,          1,       1,           1,      0,      1,    0,    0,      0,},//3：PE+陀螺仪+FE+DO+485/CAN+PWR+加热膜
    {       0,      0,        0,          0,       1,           0,      0,      0,    0,    0,      0,},//4：485/CAN
    {       0,      0,        0,          0,       1,           0,      1,      0,    0,    0,      0,},//5：485/CAN+4G/GPS
    {       0,      0,        0,          0,       1,           0,      0,      1,    0,    0,      0,},//6：485/CAN+加热膜
    {       0,      0,        1,          1,       1,           1,      0,      0,    1,    1,      0,},//7：PE+FE+DO+CAN2+485/CAN+PWR+消防
    {       1,      1,        1,          1,       1,           1,      1,      1,    1,    1,      0,},//8：全配版本
    {       1,      1,        0,          1,       1,           0,      0,      0,    0,    0,      0,},//9：PE+陀螺仪+DO+485/CAN
};

const T_CmdFuncStruct   s_atBduFuncTable[] =
{
    {SCI_CID_GET_DATA,  SciDCGetRealRsp, 0},

    {SCI_CID_GET_TEST,  SciDCGetTestDataRsp, 0},
    {SCI_CID_SET_TEST,  SciDCSetTestParaRsp, 0},

    {SCI_CID_GET_PARA,  SciDCGetParaRsp, 0},
    {SCI_CID_SET_PARA,  SciDCSetParaRsp, 0},
    {SCI_CID_SET_OPS,   SciDCCtrlRsp, 0},
//    {SCI_CID_SET_SN,    SciDCSetSNRsp, 0},
    {SCI_CID_SET_ASSET, SciDCSetAssetRsp, 0},
    {SCI_CID_GET_MENU,  SciDCGetFactRsp, 0},
    {SCI_CID_GET_CALI,  SciDCGetAdjRsp, 0},
    {SCI_CID_SET_CALI,  SciDCSetAdjRsp, 0},
    {SCI_CID_GET_MEM,   SciDCGetMemRsp, 0},
    {SCI_CID_SET_MEM,   SciDCSetMemRsp, 0},

    {SCI_CID_RECORD_CONFIG,   SciDCSetRecordParaRsp, 0},
    {SCI_CID_RECORD_ORIGIN,   SciDCRecordOrigin, 0},
    {SCI_CID_RECORD_DATA,     SciDCRecordData, 0},

    {0x00, 0x0000, 0x00},
};

static struct rt_semaphore bdu_rx_sem;
Static rt_device_t   bdu_dev;
Static T_CommStruct  bdu_msg;

static rt_err_t bdu_rx_call(rt_device_t dev, rt_size_t size)
{
    rt_sem_release(&bdu_rx_sem);
    return RT_EOK;
}

Static void initBduVar(void)
{
    rt_memset( &s_tCommManage, 0, sizeof(T_CommManageStruct) );
    rt_memset( &s_tBduCmdQueue, 0, sizeof(T_BduCmdQueueStruct) );

    rt_memset( &s_tDCFactory, 0, sizeof(T_DCFactory) );
    rt_memset( &s_tDCAsset, 0, sizeof(T_DCAsset) );
    
    rt_memset( &s_tCurrBalanceInfo, 0, sizeof(T_CurrBalanceInfo) );

    rt_memset( &s_tDCTestData, 0, sizeof(T_DCTestData) );
    rt_memset( &s_tDCTestPara, 0, sizeof(T_DCTestPara) );

    rt_memset( &s_tDCReal, 0, sizeof(T_DCRealData) );
    rt_memset( &s_tDCPara, 0, sizeof(T_DCPara) );
    rt_memset( &s_tDCParaIn, 0, sizeof(T_DCPara) );
    rt_memset( &s_tDCCtrl, 0, sizeof(T_DCCtrl) );
    rt_memset( &s_tDCCtrlRet, 0, sizeof(T_DCCtrl) );
    rt_memset( &s_tDCCaliGet, 0, sizeof(T_DCCaliGet) );
    rt_memset( &s_tDCCaliSet, 0, sizeof(T_DCCaliSet) );
    rt_memset( &s_tDCMemoSet, 0, sizeof(T_DCMemQuery) );
    rt_memset( &s_tDCMemoGet, 0, sizeof(T_DCMemQuery) );

    rt_memset( &s_tBduRecordPara, 0, sizeof(T_BduRecordPara) );
    rt_memset( &s_tBduRecordData, 0, sizeof(T_BduRecordData) );

    readBduRecordCounter(&s_wRecordSaveCnt);

    return;
}


Static void initBduComm(void)
{
    struct serial_configure uart_conf ; 
    struct rt_serial_device *serial;
#ifndef UNITEST
    rt_memset(&bdu_msg, 0, sizeof(T_CommStruct));

    do
    {
        bdu_dev = rt_device_find("usart7");
    }while(RT_NULL == bdu_dev);

    serial = (struct rt_serial_device *)bdu_dev;
    uart_conf = serial->config;
    uart_conf.baud_rate = BAUD_RATE_115200;
    uart_conf.parity    = PARITY_NONE;
    rt_device_control(bdu_dev, RT_DEVICE_CTRL_CONFIG, &uart_conf);

    rt_device_open(bdu_dev, RT_SERIAL_RX_NON_BLOCKING | RT_SERIAL_TX_BLOCKING);

    rt_device_set_rx_indicate(bdu_dev, bdu_rx_call);
#endif

    return;
}


static void CheckSCIByte(T_CommStruct *ptComm, BYTE ucRevByte)
{
    if (SCI_SOI == ucRevByte)
    {
        ptComm->bRecReady = True;
        ptComm->wRecIndex = 1;
        return;
    }

    if (EOI == ucRevByte)
    {
        ptComm->bRecOk = True; 
        ptComm->wRecLength = ptComm->wRecIndex;
        return;
    }

//    if ( (ucRevByte >= '0' && ucRevByte <= '9') || (ucRevByte >= 'A' && ucRevByte <= 'F') )
//    {
//        return;  // TODO:
//    }

    return;
}

void DealBduComm( void* parameter )
{
	BYTE ucRcvByte;

    rt_sem_init(&bdu_rx_sem, "bdu_sem", 0, RT_IPC_FLAG_FIFO);

    initBduVar();
    initBduComm();
    pre_thread_beat_f(THREAD_BDUCOMM);
#ifdef UNITEST
    while (IsRunning())
#else
    while(1)
#endif
    {
        rt_thread_delay(BDU_TASK_PERIOD); 
        thread_beat_go_on(THREAD_BDUCOMM);

        if ( RT_EOK == rt_sem_take(&bdu_rx_sem, 10) )
        {
			while ( 1 == rt_device_read(bdu_dev, 0, &ucRcvByte, 1))
			{
				bdu_msg.wTimeOut = 0;
                if (!bdu_msg.bRecOk)
                {
                    bdu_msg.aucRecBuf[bdu_msg.wRecIndex] = ucRcvByte;
                    bdu_msg.wRecIndex++;

					CheckSCIByte(&bdu_msg, ucRcvByte);

                    if (bdu_msg.bRecOk)
                    {
                        DealBduCommData(&bdu_msg);
                    }

                    if (bdu_msg.wRecIndex >= LEN_COMM_REC_BUF)
                    {
                       bdu_msg.wRecIndex  = 0;
                       bdu_msg.wRecLength = 0;
                       bdu_msg.bRecReady  = False;
                       bdu_msg.bRecOk = False;
                    }
                }
			}
        }
		else
        {
            if (bdu_msg.wRecIndex > 0)
            {
                if (bdu_msg.wTimeOut++ > 5)
                {
                    ClearRecBuf(&bdu_msg);
                }
            }
        }

        ReGetBduAdjVal();

        BduCmdStageManage();

        FaultRecordSave();
    }
}

Static void BduCmdStageManage(void)
{
    static BYTE s_ucDelayTmr = 0;
    T_BCMAlarmStruct tBcmAlm;

    switch (s_tCommManage.ucCommStage)
    {
        case BDU_CMD_STAGE_IDLE:
            s_ucDelayTmr = 0;
            if ( GetCmd(&s_tCommManage.tCurrCmd) == True)
            {
                s_tCommManage.ucCommStage = BDU_CMD_STAGE_WAIT;
                s_tCommManage.bAck = False;
                SendBduCmd();
            }
            else
            {
                PollBdu();
            }
            break;

        case BDU_CMD_STAGE_WAIT:
            s_ucDelayTmr++;
            if (s_ucDelayTmr >= ACK_WAIT_TIME)
            {
                s_tCommManage.ucCommStage = BDU_CMD_STAGE_IDLE;

                s_tCommManage.wCommFailCnt++;
                if ( s_tCommManage.wCommFailCnt >= COMMFAIL_COUNTER )
                {
                    s_tCommManage.wCommFailCnt = COMMFAIL_COUNTER;
                    s_tCommManage.bCommFail = True;
                }

                GetBcmRealAlarm( &tBcmAlm );
                tBcmAlm.ucBDUCommFail = (BYTE)s_tCommManage.bCommFail;
                TransBcmAlarm( &tBcmAlm );
            }
            break;

        case BDU_UPGRADE:
            SendBduUpdateData();
            break;

        default:
            s_tCommManage.ucCommStage = BDU_CMD_STAGE_IDLE;
            break;
    }
}

Static void SendBduUpdateData(void)
{
    int j;
    BYTE *p;
    WORD wTemp;
    BYTE aucSendOrigin[SCI_SEND_MAX];
    WORD wPayLoadLen = 0;
    BYTE *pucDest;
    BYTE *pucSrc;
    WORD wLength = 0;
    T_PrtclDLStruct tDLFrm;
    

    rt_memset(&tDLFrm, 0, sizeof(T_PrtclDLStruct));

    if ( !GetSciData(&tDLFrm) )
    {
        if ( GetBduParallUpdateFlag() ? (s_wCnt++ > BDU_PARALLE_UPDATE_TIMEOUT) : (s_wCnt++ > BDU_UPDATE_TIMEOUT) )
        {
            SetBduUpdate( False );
        }
        return;
    }

    wTemp = tDLFrm.aucApplyDataBuf[4]*256 + tDLFrm.aucApplyDataBuf[5];
    if (0 == wTemp)
    {
        s_wFrmNum = tDLFrm.aucApplyDataBuf[14]*256 + tDLFrm.aucApplyDataBuf[15];
    }

    s_wCnt = 0;

    //增加帧头
    aucSendOrigin[0] = SCI_SOI;

    //应用层data域信息，校验和计算也不包含帧头
    p = &aucSendOrigin[1];
    rt_memset(p, 0x00, SCI_SEND_MAX-1);

    p[SCI_SEG_VER] = SCI_VER;
    p[SCI_SEG_REV2] = GetTotalAddr();
    p[SCI_SEG_CID] = SCI_CID_SET_OTA;  // 12: 命令码

    wPayLoadLen = tDLFrm.wApplyLength;
    p[SCI_SEG_LEN]   = wPayLoadLen/256;   // PAYLOAD长度
    p[SCI_SEG_LEN+1] = wPayLoadLen%256;   // PAYLOAD长度

    rt_memcpy(&p[SCI_SEG_DATA], &tDLFrm.aucApplyDataBuf[0], wPayLoadLen);

    wTemp = CRC_Cal(p, (SCI_ORIGIN_LEN - 4 + wPayLoadLen));
    p[SCI_SEG_DATA+wPayLoadLen]   = wTemp%256;
    p[SCI_SEG_DATA+wPayLoadLen+1] = wTemp/256;

    pucDest = &bdu_msg.aucSendBuf[0];
    pucSrc  = &aucSendOrigin[1];

    *(pucDest++) = SCI_SOI;
    wLength++;

    j = 0;
    while ( j++ < (SCI_ORIGIN_LEN - 2 + wPayLoadLen))
    {
        if ( SCI_TRANS_CODE==*pucSrc || SCI_SOI==*pucSrc || SCI_EOI==*pucSrc )
        {
            *(pucDest++) = SCI_TRANS_CODE;
            *(pucDest++) = ~(*(pucSrc++));
            wLength += 2;
        }
        else
        {
            *(pucDest++) = *(pucSrc++);
            wLength++;
        }
    }

    *(pucDest++) = SCI_EOI;
    wLength++;

    bdu_msg.wSendLength = wLength;
    bdu_msg.wSendIndex = 0;

    rt_device_write(bdu_dev, 0, bdu_msg.aucSendBuf, bdu_msg.wSendLength);

    return;
}


Static void DealBduCommData(T_CommStruct  *ptComm)
{
    BYTE aucRecOrigin[LEN_COMM_REC_BUF] ={0,};
    BYTE *pucSrc;
    BYTE *pucDest = &s_tCommManage.aucBuf[0];
    WORD wLength = 0;

    WORD wPayLoadLen = 0;

    for(int i = 0; i < LEN_COMM_REC_BUF - ptComm->wRecLength; i++)
    {
       if(ptComm->aucRecBuf[i] == SCI_SOI)
       {
           // ptComm->wRecLength-1: means NO SOI
           rt_memcpy(&aucRecOrigin[0], &ptComm->aucRecBuf[i+1], ptComm->wRecLength-1);
           break;
       }
    }

    pucSrc  = &aucRecOrigin[0];

    while ( SCI_EOI != *pucSrc && wLength < ptComm->wRecLength )
    {
        if ( SCI_TRANS_CODE == *pucSrc )
        {
            pucSrc++;
            *(pucDest++) = ~(*(pucSrc++));
        }
        else
        {
            *(pucDest++) = *(pucSrc++);
        }
        wLength++;
    }

    ClearRecBuf(ptComm);  // 清空底层接收缓冲区

    if ((BDU_UPGRADE == s_tCommManage.ucCommStage) &&
        (SCI_CID_SET_OTA == s_tCommManage.aucBuf[SCI_SEG_CID]))
    {
        DealBduUpdateRsp();
        return;
    }

    if ( 0 != CRC_Cal( &s_tCommManage.aucBuf[0], wLength) )
    {
        s_ucBduComSts = SCI_COMM_CRC_ERR;
        return;  // CRC校验不过，数据丢弃
    }

    wPayLoadLen = s_tCommManage.aucBuf[SCI_SEG_LEN]*256 + s_tCommManage.aucBuf[SCI_SEG_LEN+1];
    if ((wLength + 2 - SCI_ORIGIN_LEN) != wPayLoadLen)
    {
        s_ucBduComSts = SCI_COMM_LEN_ERR;
        return;  // LEN校验不过，数据丢弃
    }

    if (SCI_RTN_SUCCESS != s_tCommManage.aucBuf[SCI_SEG_RTN])
    {
        s_ucBduComSts = SCI_COMM_RTN_ERR;
        //return;    // 返回值判断
    }
    else
    {
        s_ucBduComSts = SCI_COMM_SUCCESS;
    }

    DealBduCommCmd(ptComm->ucPortType);

    return;
}

Static BOOLEAN IsUpdateComplete(void) {
    BYTE ucFunc = s_tCommManage.aucBuf[SCI_SEG_DATA+3];
    WORD wRQ_NO = s_tCommManage.aucBuf[SCI_SEG_DATA+4]*256 + s_tCommManage.aucBuf[SCI_SEG_DATA+5];

    if ( (wRQ_NO == s_wFrmNum) && (wRQ_NO != 0) && (FUNC_CODE_FRONT_TRANSFER_FRM == ucFunc)) {
        return True;
    }
    return False;
} 

Static BOOLEAN IsParalleUpdateComplete(void) {
    BYTE ucFunc = s_tCommManage.aucBuf[SCI_SEG_DATA+3];
    BYTE ucRtn = s_tCommManage.aucBuf[SCI_SEG_DATA + 6];

    if ((FUNC_CODE_PARALLE_FRONT_UPDATE_CONFIRM_FRM == ucFunc) && (ApplyRTN_ACK == ucRtn)) {
        return True;
    }
    return False;
}

Static void DealBduUpdateRsp(void)
{
    BYTE aucRspFrame[256] = {0, };
    BYTE ucLen = s_tCommManage.aucBuf[SCI_SEG_LEN]*256 + s_tCommManage.aucBuf[SCI_SEG_LEN+1];

    rt_memcpy( &aucRspFrame[0], &s_tCommManage.aucBuf[SCI_SEG_DATA], ucLen );
    sendDlRepData( &aucRspFrame[0], ucLen );

    if ( IsUpdateComplete() ||  IsParalleUpdateComplete())
    {
        SetBduParallUpdateFlag(False);
        rt_thread_delay(1000);
        ResetMCU(NO_RESET_BDU_UPGRADE);
    }

    return;
}


Static void DealBduCommCmd(BYTE ucPort )
{
    BYTE j = 0;

    s_tCommManage.bAck = True;
    ClearBduCommFail();//清除 BDU 通信断标志
    s_tCommManage.ucCommStage = BDU_CMD_STAGE_IDLE;

    j = 0;
    while (s_atBduFuncTable[j].ucCmdCode)
    {
     if ( s_atBduFuncTable[j].ucCmdCode == s_tCommManage.aucBuf[SCI_SEG_CID])
     {
         (*s_atBduFuncTable[j].func)(ucPort);
         return;
     }
     j++;
    }

    return;
}


/****************************************************************
函数名：QueueFull
入口参数：
出口参数：
功能：判断队列满
****************************************************************/
static BOOLEAN QueueFull( T_BduCmdQueueStruct *pQueue )
{
    if (pQueue == NULL)
    {
        return False;
    }
    if ( ((pQueue->ulHead-pQueue->ulTail+CMD_QUE_NUM )%CMD_QUE_NUM ) >= (CMD_QUE_NUM -1) )
    {
        return True;
    }

    return False;
}

/****************************************************************
函数名：SetCmdToQueue
入口参数：
出口参数：
功能：将命令送入指定队列
****************************************************************/
static BYTE SetCmdToQueue( T_BduCmdQueueStruct *pQueue, T_BduCmdStruct *pCmd )
{
    if ( (pQueue == NULL)
        || (pCmd == NULL) )
    {
        return False;
    }
    if ( QueueFull(pQueue) )
    {
        return False;
    }
    rt_memcpy( &pQueue->atBduCmd[pQueue->ulHead], pCmd, sizeof(T_BduCmdStruct) );
    pQueue->ulHead++;
    pQueue->ulHead %= CMD_QUE_NUM;

    return True;
}

/****************************************************************
函数名：SetCmd
入口参数：
出口参数：
功能：将命令送入队列
****************************************************************/
static BYTE SetCmd( T_BduCmdStruct *pCmd )
{
    if (pCmd == NULL)
    {
        return False;
    }

    return SetCmdToQueue(&s_tBduCmdQueue, pCmd);
}

/****************************************************************
函数名：PollBdu
入口参数：
出口参数：
功能：轮询
****************************************************************/
static void PollBdu( void )
{
    T_BduCmdStruct  tBduCmd;

    BYTE aucBduFacInfoCmd[] = 
    {
        SCI_CID_GET_MENU
    };

    rt_memset(&tBduCmd, 0, sizeof(T_BduCmdStruct));
    if(s_tCommManage.ucGetFacInfoCnt < sizeof(aucBduFacInfoCmd))
    {
        tBduCmd.ucCmd = aucBduFacInfoCmd[ s_tCommManage.ucGetFacInfoCnt ];
        SetCmd(&tBduCmd);
    }
    else
    {
        SciDCGetPara();
        //s_tCommManage.ucGetFacInfoCnt = 0;
    }

    tBduCmd.ucCmd = SCI_CID_GET_DATA;
    SetCmd(&tBduCmd);

    if (GetApptestFlag())
    {
        tBduCmd.ucCmd = SCI_CID_GET_TEST;
        SetCmd(&tBduCmd);
        SciDCGetAdj();
    }

    return;
}

static BYTE GetCmdFromQueue( T_BduCmdQueueStruct *pQueue, T_BduCmdStruct *pCmd )
{
    if ( (pQueue == NULL) || (pCmd == NULL) )
    {
        return False;
    }
    if ( pQueue->ulHead == pQueue->ulTail )
    {
        return False;
    }
    rt_memcpy( pCmd, &pQueue->atBduCmd[pQueue->ulTail], sizeof(T_BduCmdStruct) );
    pQueue->ulTail++;
    pQueue->ulTail %= CMD_QUE_NUM;

    return True;
}

/****************************************************************
函数名：GetCmd
入口参数：
出口参数：
功能：从队列获取命令
****************************************************************/
static BYTE GetCmd( T_BduCmdStruct *pCmd )
{
    if (pCmd == NULL)
    {
        return False;
    }
    
    return GetCmdFromQueue(&s_tBduCmdQueue, pCmd);
}

static BOOLEAN SendSciPara(BYTE i, BYTE *p)
{
    WORD wTemp = 0;
    WORD wPayLoadLen = 0;

    if(p == RT_NULL)
    {
        return False;
    }

    wPayLoadLen = SCI_DATA_LEN_PARA+1;
    p[SCI_SEG_LEN]   = wPayLoadLen/256;   // PAYLOAD长度
    p[SCI_SEG_LEN+1] = wPayLoadLen%256;   // PAYLOAD长度
    p[i] = SCI_DATA_LEN_PARA; // 实际数据长度

#ifdef MONITORING_BASED_CHARGE_AND_DISCHARGE_OVERCURRENT_PROTECTION_ENABLED
    wTemp = s_tDCPara.wChgOverCurVal;
    p[++i] = wTemp/256;
    p[++i] = wTemp%256;

    wTemp = s_tDCPara.wDischOverCurVal;
    p[++i] = wTemp/256;
    p[++i] = wTemp%256;
#endif

    wTemp = s_tDCPara.wBatOverVolVal;
    p[++i] = wTemp/256;
    p[++i] = wTemp%256;

    wTemp = s_tDCPara.wDropVol;
    p[++i] = wTemp/256;
    p[++i] = wTemp%256;

    wTemp = s_tDCPara.wChgCurVal;
    p[++i] = wTemp/256;
    p[++i] = wTemp%256;

    wTemp = s_tDCPara.wDisChgCurVal;
    p[++i] = wTemp/256;
    p[++i] = wTemp%256;

    wTemp = s_tDCPara.wChgVolVal;
    p[++i] = wTemp/256;
    p[++i] = wTemp%256;

    wTemp = s_tDCPara.wDischgVolVal;
    p[++i] = wTemp/256;
    p[++i] = wTemp%256;

    wTemp = s_tDCPara.wChgBusCurVal;
    p[++i] = wTemp/256;
    p[++i] = wTemp%256;                       

#ifdef MAX_CHARGE_AND_DISCHARGE_POWER_ENABLED
    wTemp = s_tDCPara.wMaxChgPowerVal;
    p[++i] = wTemp/256;
    p[++i] = wTemp%256;

    wTemp = s_tDCPara.wMaxDisChgPowerVal;
    p[++i] = wTemp/256;
    p[++i] = wTemp%256; 
#endif

    wTemp = CRC_Cal(p, (SCI_ORIGIN_LEN - 4 + wPayLoadLen));
    p[++i] = wTemp%256;
    p[++i] = wTemp/256;

    return True;
}

/***************************************************************************
 * @brief  处理设置命令
 * @return   
 **************************************************************************/
Static BOOLEAN DealSetCmdType(BYTE *p, WORD *pwPayLoadLen, BYTE i)
{
    WORD wTemp = 0;
    int j;
    switch (s_tCommManage.tCurrCmd.ucCmd)
    {
        case SCI_CID_SET_TEST:
            *pwPayLoadLen = SCI_DATA_LEN_TEST+1;
            p[SCI_SEG_LEN]   = (*pwPayLoadLen)/256;   // PAYLOAD长度
            p[SCI_SEG_LEN+1] = (*pwPayLoadLen)%256;   // PAYLOAD长度
            p[i] = SCI_DATA_LEN_TEST; // 实际数据长度

            wTemp = s_tDCTestPara.sWavePointChg;
            p[++i] = wTemp/256;
            p[++i] = wTemp%256;

            wTemp = s_tDCTestPara.sWavePointDischg;
            p[++i] = wTemp/256;
            p[++i] = wTemp%256;

            wTemp = CRC_Cal(p, (SCI_ORIGIN_LEN - 4 + (*pwPayLoadLen)));
            p[++i] = wTemp%256;
            p[++i] = wTemp/256;
            break;

        case SCI_CID_SET_PARA:
            SendSciPara(i, p);
            break;

        case SCI_CID_SET_OPS:
            p[SCI_SEG_REV1]   = SCI_CTRL_CODE;
            p[SCI_SEG_REV1+1] = s_tCommManage.tCurrCmd.ucCommandType;

            *pwPayLoadLen = SCI_DATA_LEN_CTRL+1;
            p[SCI_SEG_LEN]   = (*pwPayLoadLen)/256;   // PAYLOAD长度
            p[SCI_SEG_LEN+1] = (*pwPayLoadLen)%256;   // PAYLOAD长度
            p[i] = SCI_DATA_LEN_CTRL; // 实际数据长度

            p[++i] = (BYTE)s_tCommManage.tCurrCmd.wTemp;

            wTemp = CRC_Cal(p, (SCI_ORIGIN_LEN - 4 + (*pwPayLoadLen)));
            p[++i] = wTemp%256;
            p[++i] = wTemp/256;
            break;

        case SCI_CID_SET_ASSET:
            *pwPayLoadLen = SCI_DATA_LEN_ASSET+1;
            p[SCI_SEG_LEN]   = (*pwPayLoadLen)/256;   // PAYLOAD长度
            p[SCI_SEG_LEN+1] = (*pwPayLoadLen)%256;   // PAYLOAD长度
            p[i] = SCI_DATA_LEN_ASSET; // 实际数据长度

            rt_memcpy(&p[SCI_SEG_DATA+1], &s_tDCAsset.acAssetInfo[0], ((*pwPayLoadLen)-1));

            //下发完后必须重新获取一遍资产信息，否则新的数据上不来
            s_tCommManage.ucGetFacInfoCnt = 0;

            wTemp = CRC_Cal(p, (SCI_ORIGIN_LEN - 4 + (*pwPayLoadLen)));
            p[SCI_SEG_DATA+31] = wTemp%256;
            p[SCI_SEG_DATA+32] = wTemp/256;
            break;



        case SCI_CID_SET_MEM:
            *pwPayLoadLen = SCI_DATA_LEN_MEM_SET+1;
            p[SCI_SEG_LEN]   = (*pwPayLoadLen)/256;   // PAYLOAD长度
            p[SCI_SEG_LEN+1] = (*pwPayLoadLen)%256;   // PAYLOAD长度
            p[i] = SCI_DATA_LEN_MEM_SET; // 实际数据长度

            p[++i] = s_tDCMemoSet.ucDataWidth;

            wTemp = (s_tDCMemoSet.ulMemAddr)>>16;
            p[++i] = wTemp/256;
            p[++i] = wTemp%256;
            wTemp = (s_tDCMemoSet.ulMemAddr)&0xffff;
            p[++i] = wTemp/256;
            p[++i] = wTemp%256;

            wTemp = (s_tDCMemoSet.ulMemValue)>>16;
            p[++i] = wTemp/256;
            p[++i] = wTemp%256;
            wTemp = (s_tDCMemoSet.ulMemValue)&0xffff;
            p[++i] = wTemp/256;
            p[++i] = wTemp%256;

            wTemp = CRC_Cal(p, (SCI_ORIGIN_LEN - 4 + (*pwPayLoadLen)));
            p[++i] = wTemp%256;
            p[++i] = wTemp/256;
            break;

        case SCI_CID_RECORD_CONFIG:
            *pwPayLoadLen = 8 + s_tBduRecordPara.tRecordCfg.ucRecordPointNum;
            p[SCI_SEG_LEN]   = (*pwPayLoadLen)/256;   // PAYLOAD长度
            p[SCI_SEG_LEN+1] = (*pwPayLoadLen)%256;   // PAYLOAD长度
            p[i] = (*pwPayLoadLen) - 1; // 实际数据长度

            wTemp = s_tBduRecordPara.tRecordCfg.wRecordIntrvalFront;
            p[++i] = wTemp/256;
            p[++i] = wTemp%256;

            wTemp = s_tBduRecordPara.tRecordCfg.wRecordIntrvalAfter;
            p[++i] = wTemp/256;
            p[++i] = wTemp%256;

            p[++i] = s_tBduRecordPara.tRecordCfg.ucRecordNumFront;
            p[++i] = s_tBduRecordPara.tRecordCfg.ucRecordNumAfter;
            p[++i] = s_tBduRecordPara.tRecordCfg.ucRecordPointNum;

            for (j=0; j<s_tBduRecordPara.tRecordCfg.ucRecordPointNum; j++)
            {
                p[++i] = s_tBduRecordPara.tRecordCfg.aucRecordPointID[j];
            }

            wTemp = CRC_Cal(p, (SCI_ORIGIN_LEN - 4 + (*pwPayLoadLen)));
            p[++i] = wTemp%256;
            p[++i] = wTemp/256;
            break;

        default:
            // 无效指令，不发送
            return True;
    }
    return True;
}

/***************************************************************************
 * @brief  处理校正命令
 * @return   
 **************************************************************************/
Static BOOLEAN DealCALICmdType(BYTE *p, WORD *pwPayLoadLen, BYTE i)
{
    WORD wTemp = 0;

    switch (s_tCommManage.tCurrCmd.ucCmd)
    {
#ifdef DEVICE_USING_D121
        case SCI_CID_SET_CALI:
            *pwPayLoadLen = SCI_DATA_LEN_CALI+1;
            p[SCI_SEG_LEN]   = (*pwPayLoadLen)/256;   // PAYLOAD长度
            p[SCI_SEG_LEN+1] = (*pwPayLoadLen)%256;   // PAYLOAD长度
            p[i] = SCI_DATA_LEN_CALI; // 实际数据长度

            s_tDCCaliSet.wAdjParaVal = s_tCommManage.tCurrCmd.wTemp;

            switch (s_tCommManage.tCurrCmd.ucCommandType)
            {
                case 0x80:
                case 0x81:
                case 0x82:
                case 0x83:
                case 0x84:
                case 0x85:
                case 0x86:
                case 0x87:
                    s_tDCCaliSet.wZeroAdj = 0;
                    s_tDCCaliSet.wAdjChannel = aucBduAdjTable[s_tCommManage.tCurrCmd.ucCommandType & 0x0F];
                    break;                    

                case 0x8A:
                    s_tDCCaliSet.wZeroAdj = 1;
                    s_tDCCaliSet.wAdjChannel = 0;
                    break;

//                case 0xA0:
//                    s_tDCCaliSet.wZeroAdj = 0x6666; // 存储比例校正参数到EEPROM
//                    s_tDCCaliSet.wAdjChannel = 0;
//                    break;
//                case 0xA1:
//                    s_tDCCaliSet.wZeroAdj = 0xAAAA; // 校正参数写默认值
//                    s_tDCCaliSet.wAdjChannel = 0;
//                    break;        
                        
                default:    		  
                    break;
            }

            wTemp = s_tDCCaliSet.wAdjChannel;
            p[++i] = wTemp/256;
            p[++i] = wTemp%256;   

            wTemp = s_tDCCaliSet.wAdjParaVal;
            p[++i] = wTemp/256;
            p[++i] = wTemp%256;            

            wTemp = s_tDCCaliSet.wZeroAdj;
            p[++i] = wTemp/256;
            p[++i] = wTemp%256;

            wTemp = CRC_Cal(p, (SCI_ORIGIN_LEN - 4 + (*pwPayLoadLen)));
            p[++i] = wTemp%256;
            p[++i] = wTemp/256;
            break;
#elif defined DEVICE_USING_R321
        case SCI_CID_SET_CALI:
            *pwPayLoadLen = SCI_DATA_LEN_CALI+1;
            p[SCI_SEG_LEN]   = (*pwPayLoadLen)/256;   // PAYLOAD长度
            p[SCI_SEG_LEN+1] = (*pwPayLoadLen)%256;   // PAYLOAD长度
            p[i] = SCI_DATA_LEN_CALI; // 实际数据长度

            wTemp = s_tCommManage.tCurrCmd.ucCommandType;
            p[++i] = wTemp/256;
            p[++i] = wTemp%256;

            wTemp = s_tCommManage.tCurrCmd.wTemp;
            p[++i] = wTemp/256;
            p[++i] = wTemp%256;

            wTemp = 0;
            p[++i] = wTemp/256;
            p[++i] = wTemp%256;

            wTemp = CRC_Cal(p, (SCI_ORIGIN_LEN - 4 + (*pwPayLoadLen)));
            p[++i] = wTemp%256;
            p[++i] = wTemp/256;
            break;
#endif
        default:
            // 无效指令，不发送
            return True;
    }
    return True;
}

/***************************************************************************
 * @brief  处理获取命令
 * @return
 **************************************************************************/
Static BOOLEAN DealGetCmdType(BYTE *p, WORD *pwPayLoadLen, BYTE i)
{
    WORD wTemp = 0;
    #ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
    T_SysPara tSysPara = {0, };
    #endif

    switch (s_tCommManage.tCurrCmd.ucCmd)
    {
        case SCI_CID_GET_DATA:

            s_ucCurrShareCnt++;
            RefreshCurrBalanceInfo();

            if ((s_ucCurrShareCnt / CURRENT_SHARE_MASK) == 0)
            {
                p[SCI_SEG_REV1]   = ((s_tCurrBalanceInfo.wBattSOC >> 8) & 0x3F) | (s_tCurrBalanceInfo.bCurrBalanceEn << 6);
                p[SCI_SEG_REV1+1] =   s_tCurrBalanceInfo.wBattSOC & 0x00FF;
                p[SCI_SEG_REV1+2] =   s_tCurrBalanceInfo.ucCurrBalanceSlope;
                p[SCI_SEG_REV1+3] =   s_tCurrBalanceInfo.ucCurrBalanceAmplitude;
                p[SCI_SEG_REV2]   =  (GetTotalAddr() & 0x7F) | (s_tCurrBalanceInfo.bBroadcastEn << 7);
            }
            else
            {
                s_ucCurrShareCnt = 0;
                p[SCI_SEG_REV1]   = ((s_tCurrBalanceInfo.wBattCap >> 8) & 0x7F) | (1 << 7);
                p[SCI_SEG_REV1+1] =   s_tCurrBalanceInfo.wBattCap & 0x00FF;
                p[SCI_SEG_REV1+2] =   s_tCurrBalanceInfo.wCurrBalanceMagnif >> 8;
                p[SCI_SEG_REV1+3] =   s_tCurrBalanceInfo.wCurrBalanceMagnif & 0x00FF;
                #ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
                p[SCI_SEG_REV2]   =  (s_tCurrBalanceInfo.ucBattSOH & 0x7F) | (s_tCurrBalanceInfo.bB3CurrBalance << 7);
                #endif

            }
            #ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
            GetSysPara(&tSysPara);
            p[SCI_SEG_RTN] = tSysPara.ucCurrBalanceMethod; // 均流方式
            #endif

            // LEN=0,无DATA
            wTemp = CRC_Cal(p, (SCI_ORIGIN_LEN-4));
            p[i]   = wTemp%256;
            p[++i] = wTemp/256;
            break;

        case SCI_CID_GET_TEST:
        case SCI_CID_GET_MENU:
        case SCI_CID_GET_PARA:
        case SCI_CID_GET_CALI:

        case SCI_CID_RECORD_ORIGIN:
        case SCI_CID_RECORD_DATA:

            // LEN=0,无DATA
            wTemp = CRC_Cal(p, (SCI_ORIGIN_LEN-4));
            p[i]   = wTemp%256;
            p[++i] = wTemp/256;
            break;

        case SCI_CID_GET_MEM:
            *pwPayLoadLen = SCI_DATA_LEN_MEM_GET+1;
            p[SCI_SEG_LEN]   = (*pwPayLoadLen)/256;   // PAYLOAD长度
            p[SCI_SEG_LEN+1] = (*pwPayLoadLen)%256;   // PAYLOAD长度
            p[i] = SCI_DATA_LEN_MEM_GET; // 实际数据长度

            p[++i] = s_tDCMemoGet.ucDataWidth;

            wTemp = (s_tDCMemoGet.ulMemAddr)>>16;
            p[++i] = wTemp/256;
            p[++i] = wTemp%256;
            wTemp = (s_tDCMemoGet.ulMemAddr)&0xffff;
            p[++i] = wTemp/256;
            p[++i] = wTemp%256;

            wTemp = CRC_Cal(p, (SCI_ORIGIN_LEN - 4 + (*pwPayLoadLen)));
            p[++i] = wTemp%256;
            p[++i] = wTemp/256;
            break;
        default:
            // 无效指令，不发送
            return True;

    }
    return True;
}

/****************************************************************
函数名：SendBmuCmd
入口参数：
出口参数：
功能：发命令
****************************************************************/
static void SendBduCmd( void )
{
    int i, j;
    BYTE *p;
    //WORD  wTemp;
    BYTE  aucSendOrigin[SCI_SEND_MAX];
    WORD  wPayLoadLen = 0;
    BYTE *pucDest;
    BYTE *pucSrc;
    WORD  wLength = 0;

    //增加帧头
    aucSendOrigin[0] = SCI_SOI;

    //应用层data域信息，校验和计算也不包含帧头
    p = &aucSendOrigin[1];
    rt_memset(p, 0x00, SCI_SEND_MAX-1);

    p[SCI_SEG_VER] = SCI_VER;
    p[SCI_SEG_CID] = s_tCommManage.tCurrCmd.ucCmd;  // 12: 命令码

    i = SCI_SEG_DATA;

    DealSetCmdType(p, &wPayLoadLen, i);
    DealCALICmdType(p, &wPayLoadLen, i);
    DealGetCmdType(p, &wPayLoadLen, i);

    pucDest = &bdu_msg.aucSendBuf[0];
    pucSrc  = &aucSendOrigin[1];

    *(pucDest++) = SCI_SOI;
    wLength++;
    j = 0;
    while ( j++ < (SCI_ORIGIN_LEN - 2 + wPayLoadLen))
    {
        if ( SCI_TRANS_CODE==*pucSrc || SCI_SOI==*pucSrc || SCI_EOI==*pucSrc )
        {
            *(pucDest++) = SCI_TRANS_CODE;
            *(pucDest++) = ~(*(pucSrc++));
            wLength += 2;
        }
        else
        {
            *(pucDest++) = *(pucSrc++);
            wLength++;
        }
    }

    *(pucDest++) = SCI_EOI;
    wLength++;

    bdu_msg.wSendLength = wLength;
    bdu_msg.wSendIndex = 0;

    rt_device_write(bdu_dev, 0, bdu_msg.aucSendBuf, bdu_msg.wSendLength);

    return; 
}

static void SetBduDiagStatus(uint32_t ulStatus)
{
    s_ulDiagStatus = ulStatus;
}


uint32_t GetBduDiagStatus(void)
{
    return s_ulDiagStatus;
}


// 获取BDU实时数据：PollBdu中，周期性更新
// 获取BDU实时数据响应函数
static void SciDCGetRealRsp(BYTE ucPort)
{
    int i, index;
    BYTE  ucLen1, ucLen2, ucLen3, ucLen4;
    BYTE  ucRet = 0;
    static BYTE s_ucIndex = 0;
    uint32_t ulDiagStatus = 0;

    ucRet = s_tCommManage.aucBuf[SCI_SEG_RTN];

    if (SCI_RTN_SUCCESS != ucRet)
    {
        return;  // 异常处理
    }

    // 段1：模拟量
    index = SCI_SEG_DATA;
    ucLen1 = s_tCommManage.aucBuf[index]; // 段长度
    index++;

    if (ucLen1 > SCI_REAL_SEG_LEN_ANA)
    {
        return;  // 防止越界及功率上送数据异常
    }

    s_tDCReal.tDCAnalag.sBatCur     = (SHORT)(GetBduIntData(&s_tCommManage.aucBuf[index]));
    s_tDCReal.tDCAnalag.wBatVol     = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2]));
    s_tDCReal.tDCAnalag.sBusCur     = (SHORT)(GetBduIntData(&s_tCommManage.aucBuf[index+4]));
    s_tDCReal.tDCAnalag.wBusVol     = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+6]));
    s_tDCReal.tDCAnalag.wBatFusePro = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+8]));
    s_tDCReal.tDCAnalag.sEnvTemp    = (SHORT)(GetBduIntData(&s_tCommManage.aucBuf[index+10]));
    s_tDCReal.tDCAnalag.sBoardTemp  = (SHORT)(GetBduIntData(&s_tCommManage.aucBuf[index+12]));
    s_tDCReal.tDCAnalag.sConnTemp   = (SHORT)(GetBduIntData(&s_tCommManage.aucBuf[index+14]));

    #ifdef DEVICE_USING_R321
    s_tDCReal.tDCAnalag.sBalanceResisTemp = (SHORT)(GetBduIntData(&s_tCommManage.aucBuf[index+16]));
    s_tDCReal.tDCAnalag.sActivatePortVol  = (SHORT)(GetBduIntData(&s_tCommManage.aucBuf[index+18]));
    #endif


    // 段2：状态量
    index += ucLen1;
    ucLen2 = s_tCommManage.aucBuf[index]; // 段长度
    index++;

    if (ucLen2 > SCI_REAL_SEG_LEN_STS)
    {
        return;  // 防止越界及功率上送数据异常
    }

    i = 0;    
    s_tDCReal.tDCStatus.bChgPrt       = ((s_tCommManage.aucBuf[index+i]&0x80)>>7);
    s_tDCReal.tDCStatus.bDischgPrt    = ((s_tCommManage.aucBuf[index+i]&0x40)>>6);
    s_tDCReal.tDCStatus.bStatus       = ((s_tCommManage.aucBuf[index+i]&0x3C)>>2);
    s_tDCReal.tDCStatus.bLimit        = ((s_tCommManage.aucBuf[index+i]&0x02)>>1);
    s_tDCReal.tDCStatus.bPowLmtStatus =   s_tCommManage.aucBuf[index+i]&0x01;

    i++;    
    s_tDCReal.tDCStatus.bSleepStatus         = ((s_tCommManage.aucBuf[index+i]&0x80)>>7);
    s_tDCReal.tDCStatus.bLock                = ((s_tCommManage.aucBuf[index+i]&0x40)>>6);
    s_tDCReal.tDCStatus.bInputBreak          = ((s_tCommManage.aucBuf[index+i]&0x20)>>5);
    s_tDCReal.tDCStatus.bChgStraight         = ((s_tCommManage.aucBuf[index+i]&0x10)>>4);
    s_tDCReal.tDCStatus.bDisChgStraight      = ((s_tCommManage.aucBuf[index+i]&0x08)>>3);
    s_tDCReal.tDCStatus.bExternalPowerSupply = ((s_tCommManage.aucBuf[index+i]&0x04)>>2);
    s_tDCReal.tDCStatus.bBoostChgEn          = ((s_tCommManage.aucBuf[index+i]&0x02)>>1);
    s_tDCReal.tDCStatus.bUpgradeEn           =   s_tCommManage.aucBuf[index+i]&0x01;

    i++;
    s_tDCReal.tDCStatus.bMOSOffStatus    = ((s_tCommManage.aucBuf[index+i]&0x80)>>7);
    s_tDCReal.tDCStatus.bExternalPowerOn = ((s_tCommManage.aucBuf[index+i]&0x40)>>6);
    s_tDCReal.tDCStatus.bTestMode        = ((s_tCommManage.aucBuf[index+i]&0x20)>>5);
    s_tDCReal.tDCStatus.bOpenloop        = ((s_tCommManage.aucBuf[index+i]&0x10)>>4);
    s_tDCReal.tDCStatus.bChange2ChgEn    = ((s_tCommManage.aucBuf[index+i]&0x08)>>3);
    s_tDCReal.tDCStatus.bHeaterStatus    = ((s_tCommManage.aucBuf[index+i]&0x04)>>2);
    s_tDCReal.tDCStatus.bChgForbid       = ((s_tCommManage.aucBuf[index+i]&0x02)>>1);
    s_tDCReal.tDCStatus.bDischgForbid    =   s_tCommManage.aucBuf[index+i]&0x01;    

    i++;
    s_tDCReal.tDCStatus.bDiagSts32  = ((s_tCommManage.aucBuf[index+i]&0x80)>>7);
    s_tDCReal.tDCStatus.bDiagSts31  = ((s_tCommManage.aucBuf[index+i]&0x40)>>6);
    s_tDCReal.tDCStatus.bDiagSts30  = ((s_tCommManage.aucBuf[index+i]&0x20)>>5);
    s_tDCReal.tDCStatus.bDiagSts29  = ((s_tCommManage.aucBuf[index+i]&0x10)>>4);
    s_tDCReal.tDCStatus.bDiagSts28  = ((s_tCommManage.aucBuf[index+i]&0x08)>>3);
    s_tDCReal.tDCStatus.bDiagSts27  = ((s_tCommManage.aucBuf[index+i]&0x04)>>2);
    s_tDCReal.tDCStatus.bDiagSts26  = ((s_tCommManage.aucBuf[index+i]&0x02)>>1);
    s_tDCReal.tDCStatus.bDiagSts25  =   s_tCommManage.aucBuf[index+i]&0x01;

    ulDiagStatus |= (s_tCommManage.aucBuf[index+i] << 24);

    i++;
    s_tDCReal.tDCStatus.bDiagSts24 = ((s_tCommManage.aucBuf[index+i]&0x80)>>7);
    s_tDCReal.tDCStatus.bDiagSts23 = ((s_tCommManage.aucBuf[index+i]&0x40)>>6);
    s_tDCReal.tDCStatus.bDiagSts22 = ((s_tCommManage.aucBuf[index+i]&0x20)>>5);
    s_tDCReal.tDCStatus.bDiagSts21 = ((s_tCommManage.aucBuf[index+i]&0x10)>>4);
    s_tDCReal.tDCStatus.bDiagSts20 = ((s_tCommManage.aucBuf[index+i]&0x08)>>3);
    s_tDCReal.tDCStatus.bDiagSts19 = ((s_tCommManage.aucBuf[index+i]&0x04)>>2);
    s_tDCReal.tDCStatus.bDiagSts18 = ((s_tCommManage.aucBuf[index+i]&0x02)>>1);
    s_tDCReal.tDCStatus.bDiagSts17 =   s_tCommManage.aucBuf[index+i]&0x01;

    ulDiagStatus |= (s_tCommManage.aucBuf[index+i] << 16);

    i++;
    s_tDCReal.tDCStatus.bDiagSts16 = ((s_tCommManage.aucBuf[index+i]&0x80)>>7);
    s_tDCReal.tDCStatus.bDiagSts15 = ((s_tCommManage.aucBuf[index+i]&0x40)>>6);
    s_tDCReal.tDCStatus.bDiagSts14 = ((s_tCommManage.aucBuf[index+i]&0x20)>>5);
    s_tDCReal.tDCStatus.bDiagSts13 = ((s_tCommManage.aucBuf[index+i]&0x10)>>4);
    s_tDCReal.tDCStatus.bDiagSts12 = ((s_tCommManage.aucBuf[index+i]&0x08)>>3);
    s_tDCReal.tDCStatus.bDiagSts11 = ((s_tCommManage.aucBuf[index+i]&0x04)>>2);
    s_tDCReal.tDCStatus.bDiagSts10 = ((s_tCommManage.aucBuf[index+i]&0x02)>>1);
    s_tDCReal.tDCStatus.bDiagSts9  =   s_tCommManage.aucBuf[index+i]&0x01;

    ulDiagStatus |= (s_tCommManage.aucBuf[index+i] << 8);

    i++;
    s_tDCReal.tDCStatus.bDiagSts8  = ((s_tCommManage.aucBuf[index+i]&0x80)>>7);
    s_tDCReal.tDCStatus.bDiagSts7  = ((s_tCommManage.aucBuf[index+i]&0x40)>>6);
    s_tDCReal.tDCStatus.bDiagSts6  = ((s_tCommManage.aucBuf[index+i]&0x20)>>5);
    s_tDCReal.tDCStatus.bDiagSts5  = ((s_tCommManage.aucBuf[index+i]&0x10)>>4);
    s_tDCReal.tDCStatus.bDiagSts4  = ((s_tCommManage.aucBuf[index+i]&0x08)>>3);
    s_tDCReal.tDCStatus.bDiagSts3  = ((s_tCommManage.aucBuf[index+i]&0x04)>>2);
    s_tDCReal.tDCStatus.bDiagSts2  = ((s_tCommManage.aucBuf[index+i]&0x02)>>1);
    s_tDCReal.tDCStatus.bDiagSts1  =   s_tCommManage.aucBuf[index+i]&0x01;

    ulDiagStatus |= (s_tCommManage.aucBuf[index+i]);
    SetBduDiagStatus(ulDiagStatus);

    i++;
    s_tDCReal.tDCStatus.bChargeMachineTest = ((s_tCommManage.aucBuf[index+i]&0x80)>>7);
    s_tDCReal.tDCStatus.bContactorBus      = ((s_tCommManage.aucBuf[index+i]&0x40)>>6);
    s_tDCReal.tDCStatus.bEMCtest           = ((s_tCommManage.aucBuf[index+i]&0x20)>>5);
    s_tDCReal.tDCStatus.bContactorBatt     = ((s_tCommManage.aucBuf[index+i]&0x10)>>4);
    s_tDCReal.tDCStatus.bSolarMode         = ((s_tCommManage.aucBuf[index+i]&0x08)>>3);
    
    s_tDCReal.tDCStatus.bCanStopped       = ((s_tCommManage.aucBuf[index+i] & 0x04) >> 2);
    
    
    s_tDCReal.tDCStatus.bWaterIngrnessEn = ((s_tCommManage.aucBuf[index+i] & 0x02) >> 1);
    

    // 段3：告警量
    index += ucLen2;
    ucLen3 = s_tCommManage.aucBuf[index]; // 段长度
    index++;

    if (ucLen3 > SCI_REAL_SEG_LEN_ALM)
    {
        return;  // 防止越界及功率上送数据异常
    }

    i = 0;
    s_tDCReal.tDCAlarm.bEEPROM         = ((s_tCommManage.aucBuf[index+i]&0x80)>>7);
    s_tDCReal.tDCAlarm.bShortCut       = ((s_tCommManage.aucBuf[index+i]&0x40)>>6);
    s_tDCReal.tDCAlarm.bBatReverse     = ((s_tCommManage.aucBuf[index+i]&0x20)>>5);
    s_tDCReal.tDCAlarm.bInsideOverTemp = ((s_tCommManage.aucBuf[index+i]&0x10)>>4);
    s_tDCReal.tDCAlarm.bBoardOverTemp  = ((s_tCommManage.aucBuf[index+i]&0x08)>>3);
    s_tDCReal.tDCAlarm.bLockErr        = ((s_tCommManage.aucBuf[index+i]&0x04)>>2);
    s_tDCReal.tDCAlarm.bChgLoopFail    = ((s_tCommManage.aucBuf[index+i]&0x02)>>1);
    s_tDCReal.tDCAlarm.bDischLoopFail  =   s_tCommManage.aucBuf[index+i]&0x01;

    i++;
    s_tDCReal.tDCAlarm.bLimLoopFail    = ((s_tCommManage.aucBuf[index+i]&0x80)>>7);
    s_tDCReal.tDCAlarm.bChgOverCur     = ((s_tCommManage.aucBuf[index+i]&0x40)>>6);
    s_tDCReal.tDCAlarm.bDischOverCur   = ((s_tCommManage.aucBuf[index+i]&0x20)>>5);
    s_tDCReal.tDCAlarm.bBatOverVol     = ((s_tCommManage.aucBuf[index+i]&0x10)>>4);
    s_tDCReal.tDCAlarm.bBatUnderVol    = ((s_tCommManage.aucBuf[index+i]&0x08)>>3);
    s_tDCReal.tDCAlarm.bBusOverVol     = ((s_tCommManage.aucBuf[index+i]&0x04)>>2);
    s_tDCReal.tDCAlarm.bBusUnderVol    = ((s_tCommManage.aucBuf[index+i]&0x02)>>1);
    s_tDCReal.tDCAlarm.bChgBatUnderVol =   s_tCommManage.aucBuf[index+i]&0x01;
    
    i++;
    s_tDCReal.tDCAlarm.bWavePrt       = ((s_tCommManage.aucBuf[index+i]&0x80)>>7);
    s_tDCReal.tDCAlarm.bMainRelayFail = ((s_tCommManage.aucBuf[index+i]&0x40)>>6);
    s_tDCReal.tDCAlarm.bDCDCErr       = ((s_tCommManage.aucBuf[index+i]&0x20)>>5);
    s_tDCReal.tDCAlarm.bSampleErr     = ((s_tCommManage.aucBuf[index+i]&0x10)>>4);
    s_tDCReal.tDCAlarm.bAuxiSourceErr = ((s_tCommManage.aucBuf[index+i]&0x08)>>3);
    s_tDCReal.tDCAlarm.bHeaterErr     = ((s_tCommManage.aucBuf[index+i]&0x04)>>2);
    s_tDCReal.tDCAlarm.bConnTempHighPrt=((s_tCommManage.aucBuf[index+i]&0x02)>>1);

#ifdef DEVICE_USING_R321
    s_tDCReal.tDCAlarm.bBalanceResisTempHighPrt = s_tCommManage.aucBuf[index+i]&0x01;

    i++;
    s_tDCReal.tDCAlarm.bActivatePortCurrError   = ((s_tCommManage.aucBuf[index+i]&0x80)>>7);
    s_tDCReal.tDCAlarm.bActivatePortReverseAlm  = ((s_tCommManage.aucBuf[index+i]&0x40)>>6);
#endif


    // 段4：参数CRC
    index += ucLen3;
    ucLen4 = s_tCommManage.aucBuf[index]; // 段长度
    index++;

    if (ucLen4 > SCI_REAL_SEG_LEN_CRC)
    {
        return;  // 防止越界及功率上送数据异常
    }

    s_tDCReal.wSystemParaCrc = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index]));
    s_tDCReal.wRecordParaCrc = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2]));

    s_awBusVolt[s_ucIndex] = s_tDCReal.tDCAnalag.wBusVol;
    s_ucIndex = (s_ucIndex + 1) % ONE_SECOND_SMOOTH;

    CheckParaChange(s_tDCReal.wSystemParaCrc);

    SynRecordPara();
    CheckRecordParaChange(s_tDCReal.wRecordParaCrc);

    return;
}

/****************************************************************
函数名：ClearBduCommFail
入口参数：无
出口参数：
功能：清除 BDU 通信断标志
****************************************************************/
Static BOOLEAN ClearBduCommFail(void) 
{
    // 定义告警信息结构体变量
    T_BCMAlarmStruct tBcmAlm; 
    rt_memset(&tBcmAlm, 0, sizeof(T_BCMAlarmStruct));
    // 清零通信失败计数
    s_tCommManage.wCommFailCnt = 0;
    // 将通信失败标志设置为 False
    s_tCommManage.bCommFail = False;
    // 获取实时告警信息
    GetBcmRealAlarm(&tBcmAlm);
    // 更新实时告警信息中的通信失败标志
    tBcmAlm.ucBDUCommFail = (BYTE)s_tCommManage.bCommFail;
    // 转换并更新报警信息
    TransBcmAlarm(&tBcmAlm);
    // 返回操作是否成功，这里简单地返回 true 表示操作成功
    return True;
}

FLOAT GetAveBusVolt(void)
{
    FLOAT fSum = 0.0f;
    BYTE i;
    for(i = 0; i < ONE_SECOND_SMOOTH; i++)
    {
        fSum += s_awBusVolt[i] / 100.0f;
    }
    return (fSum / ONE_SECOND_SMOOTH);
}

// 获取实时数据接口
BOOLEAN GetBduReal(T_DCRealData *pRealData)
{
    if (NULL == pRealData)
    {
        return False;
    }
    rt_memcpy(pRealData, &s_tDCReal, sizeof(T_DCRealData));

    return True;
}

BOOLEAN GetHeaterStatus(void)
{
    return (s_tDCReal.tDCStatus.bHeaterStatus > 0);
}


/////////////////// 生产测试功能 begin ///////////////////

// 获取测试数据接口
BOOLEAN GetBduTestData(T_DCTestData *pTestData)
{
    if (!GetApptestFlag())
    {
        return False;
    }

    if (NULL == pTestData)
    {
        return False;
    }

    rt_memcpy(pTestData, &s_tDCTestData, sizeof(T_DCTestData));

    return True;
}


#ifdef DEVICE_USING_R321
// 获取BDU测试数据响应函数 R321
static void SciDCGetTestDataRsp(BYTE ucPort)
{
    int i, index;
    BYTE  ucLen1, ucLen2, ucLen3;

    BYTE ucRet;
    ucRet = s_tCommManage.aucBuf[SCI_SEG_RTN];

    if (!GetApptestFlag())
    {
        return;
    }

    if (SCI_RTN_SUCCESS != ucRet)
    {
        return;
    }

    // 段1：模拟量
    index = SCI_SEG_DATA;
    ucLen1 = s_tCommManage.aucBuf[index]; // 段长度
    index++;

    if (ucLen1 > SCI_TEST_SEG_LEN_ANA)
    {
        return;  // 防止越界及功率上送数据异常
    }

    s_tDCTestData.sBusPinTestVol   = (SHORT)(GetBduIntData(&s_tCommManage.aucBuf[index]));
    s_tDCTestData.wHeaterTestVol   = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2]));
    s_tDCTestData.wMidMOSTestVol1  = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+4]));
    s_tDCTestData.wMidMOSTestVol2  = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+6]));
    s_tDCTestData.wMidMOSTestVol3  = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+8]));
    s_tDCTestData.wMidMOSTestVol4  = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+10]));
    s_tDCTestData.sActCircTestVol  = (SHORT)(GetBduIntData(&s_tCommManage.aucBuf[index+12]));
    s_tDCTestData.wBusRelayTestVol = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+14]));

    s_tDCTestData.sBoardTemp1 = (SHORT)(GetBduIntData(&s_tCommManage.aucBuf[index+16]));
    s_tDCTestData.sBoardTemp2 = (SHORT)(GetBduIntData(&s_tCommManage.aucBuf[index+18]));
    s_tDCTestData.sBoardTemp3 = (SHORT)(GetBduIntData(&s_tCommManage.aucBuf[index+20]));
    s_tDCTestData.sBoardTemp4 = (SHORT)(GetBduIntData(&s_tCommManage.aucBuf[index+22]));

    s_tDCTestData.sConnTemp1 = (SHORT)(GetBduIntData(&s_tCommManage.aucBuf[index+24]));
    s_tDCTestData.sConnTemp2 = (SHORT)(GetBduIntData(&s_tCommManage.aucBuf[index+26]));


    // 段2：状态量
    index += ucLen1;
    ucLen2 = s_tCommManage.aucBuf[index]; // 段长度
    index++;

    if (ucLen2 > SCI_TEST_SEG_LEN_STS)
    {
        return;  // 防止越界及功率上送数据异常
    }

    i = 0;
    s_tDCTestData.bBusRelayTestSts = ((s_tCommManage.aucBuf[index+i]&0xC0)>>6);

    if (s_tDCTestData.bBusRelayTestSts != s_tDCCtrl.ucRelayTest)
    {
        BduCtrl(SCI_CTRL_TEST_RELAY, s_tDCCtrl.ucRelayTest);
    }

    s_tDCTestData.bWavePrtTestSts = ((s_tCommManage.aucBuf[index+i]&0x20)>>5);

    if (s_tDCTestData.bWavePrtTestSts != s_tDCCtrl.ucWavePrtTest)
    {
        BduCtrl(SCI_CTRL_TEST_WAVE_PRT, s_tDCCtrl.ucWavePrtTest);
    }


    // 段3：参数CRC
    index += ucLen2;
    ucLen3 = s_tCommManage.aucBuf[index]; // 段长度
    index++;

    if (ucLen3 > SCI_TEST_SEG_LEN_CRC)
    {
        return;  // 防止越界及功率上送数据异常
    }

    s_tDCTestData.wTestParaCrc = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index]));

    CheckTestParaChange(s_tDCTestData.wTestParaCrc);

    return;
}

#else

// 获取BDU测试数据响应函数 D121
static void SciDCGetTestDataRsp(BYTE ucPort)
{
    int i, index;
    BYTE  ucLen1, ucLen2, ucLen3;

    BYTE ucRet;
    ucRet = s_tCommManage.aucBuf[SCI_SEG_RTN];

    if (!GetApptestFlag())
    {
        return;
    }

    if (SCI_RTN_SUCCESS != ucRet)
    {
        return;
    }

    // 段1：模拟量
    index = SCI_SEG_DATA;
    ucLen1 = s_tCommManage.aucBuf[index]; // 段长度
    index++;

    if (ucLen1 > SCI_TEST_SEG_LEN_ANA)
    {
        return;  // 防止越界及功率上送数据异常
    }

    s_tDCTestData.sCurrentInfo1 = (SHORT)(GetBduIntData(&s_tCommManage.aucBuf[index]));
    s_tDCTestData.sCurrentInfo2 = (SHORT)(GetBduIntData(&s_tCommManage.aucBuf[index+2]));
    s_tDCTestData.sCurrentInfo3 = (SHORT)(GetBduIntData(&s_tCommManage.aucBuf[index+4]));
    s_tDCTestData.sCurrentInfo4 = (SHORT)(GetBduIntData(&s_tCommManage.aucBuf[index+6]));
    s_tDCTestData.sCurrentInfo5 = (SHORT)(GetBduIntData(&s_tCommManage.aucBuf[index+8]));
    s_tDCTestData.wVoltageInfo1 = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+10]));
    s_tDCTestData.wVoltageInfo2 = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+12]));
    s_tDCTestData.wVoltageInfo3 = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+14]));
    s_tDCTestData.wVoltageInfo4 = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+16]));

    s_tDCTestData.sBoardTemp1 = (SHORT)(GetBduIntData(&s_tCommManage.aucBuf[index+18]));
    s_tDCTestData.sBoardTemp2 = (SHORT)(GetBduIntData(&s_tCommManage.aucBuf[index+20]));
    s_tDCTestData.sBoardTemp3 = (SHORT)(GetBduIntData(&s_tCommManage.aucBuf[index+22]));
    s_tDCTestData.sConnTemp1  = (SHORT)(GetBduIntData(&s_tCommManage.aucBuf[index+24]));
    s_tDCTestData.sConnTemp2  = (SHORT)(GetBduIntData(&s_tCommManage.aucBuf[index+26]));
    s_tDCTestData.sEnvTemp    = (SHORT)(GetBduIntData(&s_tCommManage.aucBuf[index+28]));

    s_tDCTestData.wHeaterVol  = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+30]));


    // 段2：状态量
    index += ucLen1;
    ucLen2 = s_tCommManage.aucBuf[index]; // 段长度
    index++;

    if (ucLen2 > SCI_TEST_SEG_LEN_STS)
    {
        return;  // 防止越界及功率上送数据异常
    }

    i = 0;
    s_tDCTestData.bBusRelayTestSts = ((s_tCommManage.aucBuf[index+i]&0xF8)>>3);

    if (s_tDCTestData.bBusRelayTestSts != s_tDCCtrl.ucRelayTest)
    {
        BduCtrl(SCI_CTRL_TEST_RELAY, s_tDCCtrl.ucRelayTest);
    }

    s_tDCTestData.bWavePrtTestSts = ((s_tCommManage.aucBuf[index+i]&0x04)>>2);

    if (s_tDCTestData.bWavePrtTestSts != s_tDCCtrl.ucWavePrtTest)
    {
        BduCtrl(SCI_CTRL_TEST_WAVE_PRT, s_tDCCtrl.ucWavePrtTest);
    }


    // 段3：参数CRC
    index += ucLen2;
    ucLen3 = s_tCommManage.aucBuf[index]; // 段长度
    index++;

    if (ucLen3 > SCI_TEST_SEG_LEN_CRC)
    {
        return;  // 防止越界及功率上送数据异常
    }

    s_tDCTestData.wTestParaCrc = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index]));

    CheckTestParaChange(s_tDCTestData.wTestParaCrc);

    return;
}
#endif


Static WORD CalcTestParaCRC(T_DCTestPara *pPara)
{
    T_DCTestPara tPara;
    int i = 0;
    BYTE auTemp[SCI_DATA_LEN_TEST] = {0, };
    BYTE *p = auTemp;
    WORD  wTemp;
    SHORT sTemp;

    if (NULL == pPara)
    {
        return False;
    }

    rt_memset( &tPara, 0, sizeof(T_DCTestPara) );
    rt_memcpy( &tPara, pPara, sizeof(T_DCTestPara) );

    sTemp = tPara.sWavePointChg;
    p[i++] = sTemp/256;
    p[i++] = sTemp%256;

    sTemp = tPara.sWavePointDischg;
    p[i++] = sTemp/256;
    p[i++] = sTemp%256;

    wTemp = CRC_Cal(p, sizeof(T_DCTestPara)-2);
    wTemp = (wTemp%256)*256 + wTemp/256;

    return wTemp;
}


// 设置测试参数接口
BOOLEAN SetBduTestPara(T_DCTestPara *pPara)
{
    T_BduCmdStruct  tCmd;
    T_DCTestPara tPara;

    if (!GetApptestFlag())
    {
        return False;
    }

    if (NULL == pPara)
    {
        return False;
    }

    rt_memset( &tPara, 0, sizeof(T_DCTestPara) );
    rt_memcpy( &tPara, pPara, sizeof(T_DCTestPara) );

    tPara.wCrc = CalcTestParaCRC(&tPara);

    rt_memcpy( &s_tDCTestPara, &tPara, sizeof(T_DCTestPara) );

    rt_memset(&tCmd, 0, sizeof(T_BduCmdStruct));
    tCmd.ucCmd = SCI_CID_SET_TEST;
    SetCmd(&tCmd);

    return True;
}

static void CheckTestParaChange( WORD wCrc )
{
    T_BduCmdStruct  tCmd;
    rt_memset(&tCmd, 0, sizeof(T_BduCmdStruct));
    if ( wCrc != s_tDCTestPara.wCrc )
    {
        tCmd.ucCmd = SCI_CID_SET_TEST;
        SetCmd(&tCmd);
    }

    return;
}

// 设置测试参数响应函数
Static void SciDCSetTestParaRsp(BYTE ucPort)
{
    BYTE ucRet;
    ucRet = s_tCommManage.aucBuf[SCI_SEG_RTN];

    if (SCI_RTN_SUCCESS != ucRet)
    {
        return;  // 异常处理
    }
}

// 获取测试参数接口
BOOLEAN GetBduTestPara(T_DCTestPara *pPara)
{
    if (!GetApptestFlag())
    {
        return False;
    }

    if (NULL == pPara)
    {
        return False;
    }

    rt_memcpy(pPara, &s_tDCTestPara, sizeof(T_DCTestPara));

    return True;
}

/////////////////// 生产测试功能 end ///////////////////


Static WORD CalcParaCRC(T_DCPara *pPara)
{
  	T_DCPara tPara;
    int i = 0;
    BYTE auTemp[SCI_DATA_LEN_PARA] = {0, };
    BYTE *p = auTemp;
    WORD wTemp;

    if (NULL == pPara)
    {
        return False;
    }

    rt_memset( &tPara, 0, sizeof(T_DCPara) );
    rt_memcpy( &tPara, pPara, sizeof(T_DCPara) );

#ifdef MONITORING_BASED_CHARGE_AND_DISCHARGE_OVERCURRENT_PROTECTION_ENABLED
    wTemp = tPara.wChgOverCurVal;
    p[i++] = wTemp/256;
    p[i++] = wTemp%256;

    wTemp = tPara.wDischOverCurVal;
    p[i++] = wTemp/256;
    p[i++] = wTemp%256;
#endif

    wTemp = tPara.wBatOverVolVal;
    p[i++] = wTemp/256;
    p[i++] = wTemp%256;

    wTemp = tPara.wDropVol;
    p[i++] = wTemp/256;
    p[i++] = wTemp%256;

    wTemp = tPara.wChgCurVal;
    p[i++] = wTemp/256;
    p[i++] = wTemp%256;

    wTemp = tPara.wDisChgCurVal;
    p[i++] = wTemp/256;
    p[i++] = wTemp%256;

    wTemp = tPara.wChgVolVal;
    p[i++] = wTemp/256;
    p[i++] = wTemp%256;

    wTemp = tPara.wDischgVolVal;
    p[i++] = wTemp/256;
    p[i++] = wTemp%256;

    wTemp = tPara.wChgBusCurVal;
    p[i++] = wTemp/256;
    p[i++] = wTemp%256;

#ifdef MAX_CHARGE_AND_DISCHARGE_POWER_ENABLED
    wTemp = tPara.wMaxChgPowerVal;
    p[i++] = wTemp/256;
    p[i++] = wTemp%256;

    wTemp = tPara.wMaxDisChgPowerVal;
    p[i++] = wTemp/256;
    p[i++] = wTemp%256;    
#endif

    wTemp = CRC_Cal(p, sizeof(T_DCPara)-2);
    wTemp = (wTemp%256)*256 + wTemp/256;

    return wTemp;
}


// 设置参数接口
BOOLEAN SetBduPara(T_DCPara *pPara)
{
    T_DCPara tPara;

    if (NULL == pPara)
    {
        return False;
    }

    rt_memset( &tPara, 0, sizeof(T_DCPara) );
    rt_memcpy( &tPara, pPara, sizeof(T_DCPara) );

    tPara.wCrc = CalcParaCRC(&tPara);

//    if (tPara.wCrc == s_tDCPara.wCrc)
//    {
//        return False;
//    }

    rt_memcpy( &s_tDCPara, &tPara, sizeof(T_DCPara) );

    return True;
}

static void CheckParaChange( WORD wCrc )
{
    T_BduCmdStruct  tCmd;
    rt_memset(&tCmd, 0, sizeof(T_BduCmdStruct));
    if ( wCrc != s_tDCPara.wCrc )
    {
        tCmd.ucCmd = SCI_CID_SET_PARA;
        SetCmd(&tCmd);
    }

    return;
}

// 设置BDU参数响应函数
static void SciDCSetParaRsp(BYTE ucPort)
{
    BYTE ucRet;
    ucRet = s_tCommManage.aucBuf[SCI_SEG_RTN];

    if (SCI_RTN_SUCCESS != ucRet)
    {
        return;  // 异常处理
    }
}

// 查询BDU参数
static void SciDCGetPara(void)
{
    T_BduCmdStruct  tCmd;
    rt_memset_s(&tCmd, sizeof(T_BduCmdStruct), 0, sizeof(T_BduCmdStruct));
    tCmd.ucCmd = SCI_CID_GET_PARA;
    SetCmd(&tCmd);

    return;
}

// 查询BDU参数响应函数
static void SciDCGetParaRsp(BYTE ucPort)
{
    int i, index;
    //BYTE  ucLen ;
    WORD wTemp = 0;
    
    BYTE ucRet;
    ucRet = s_tCommManage.aucBuf[SCI_SEG_RTN];

    if (SCI_RTN_SUCCESS != ucRet)
    {
        return;  // 异常处理
    }

    index = SCI_SEG_DATA;
    //ucLen = s_tCommManage.aucBuf[index]; // 段长度
    index++;

    i = 0;
#ifdef MONITORING_BASED_CHARGE_AND_DISCHARGE_OVERCURRENT_PROTECTION_ENABLED
    wTemp = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2*i]));
    s_tDCParaIn.wChgOverCurVal = wTemp;
    i++;
    wTemp = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2*i]));
    s_tDCParaIn.wDischOverCurVal = wTemp;
    i++;
#endif
    wTemp = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2*i]));
    s_tDCParaIn.wBatOverVolVal = wTemp;
    i++;
    wTemp = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2*i]));
    s_tDCParaIn.wDropVol = wTemp;
    i++;
    wTemp = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2*i]));
    s_tDCParaIn.wChgCurVal = wTemp;
    i++;
    wTemp = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2*i]));
    s_tDCParaIn.wDisChgCurVal = wTemp;
    i++;
    wTemp = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2*i]));
    s_tDCParaIn.wChgVolVal = wTemp;
    i++;
    wTemp = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2*i]));
    s_tDCParaIn.wDischgVolVal = wTemp;
    i++;
    wTemp = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2*i]));
    s_tDCParaIn.wChgBusCurVal = wTemp;
    i++;
#ifdef MAX_CHARGE_AND_DISCHARGE_POWER_ENABLED
    wTemp = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2*i]));
    s_tDCParaIn.wMaxChgPowerVal = wTemp;
    i++;
    wTemp = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2*i]));
    s_tDCParaIn.wMaxDisChgPowerVal = wTemp;        
#endif

    s_tDCParaIn.wCrc = CalcParaCRC(&s_tDCParaIn);

    return;
}

// 获取参数接口
BOOLEAN GetBduPara(T_DCPara *pPara)
{
    if (NULL == pPara)
    {
        return False;
    }
    rt_memcpy(pPara, &s_tDCPara, sizeof(T_DCPara));
//    rt_memcpy(pPara, &s_tDCParaIn, sizeof(T_DCPara));

    return True;
}

/**
 * @brief 执行BduCtrl后对控制命令下发记录
 * @retval SUCCESSFUL 匹配上了命令并做了记录  FAILURE  没有匹配上
*/
static SHORT UpdateBduCtrlRecordInfoPart_1(BYTE ucCmdType, BYTE ucCtrlCode)
{
    // 校验指令是否合法，并记录当前指令内容
    switch (ucCmdType)
    {
        case SCI_CTRL_CHG2CHG:
            s_tDCCtrl.ucChange2Chg = ucCtrlCode;
        break;
        case SCI_CTRL_SLEEP:
            s_tDCCtrl.ucSleep = ucCtrlCode;
        break;
        case SCI_CTRL_CHG_STG:
            s_tDCCtrl.ucChgStraight = ucCtrlCode;
        break;
        case SCI_CTRL_DISCHG_STG:
            s_tDCCtrl.ucDisChgStraight = ucCtrlCode;
        break;
        case SCI_CTRL_CHG_PRT:
            s_tDCCtrl.ucChgPrt = ucCtrlCode;
        break;
        case SCI_CTRL_DISCHG_PRT:
            s_tDCCtrl.ucDischgPrt = ucCtrlCode;
        break;
        case SCI_CTRL_OPEN_LOOP:
            s_tDCCtrl.ucOpenloop = ucCtrlCode;
        break;
        case SCI_CTRL_TEST:
            s_tDCCtrl.ucApptest = ucCtrlCode;
        break;
        case SCI_CTRL_HEATER:
            s_tDCCtrl.ucHeater = ucCtrlCode;
        break;
        case SCI_CTRL_BDULOCK:
            s_tDCCtrl.ucLockCtrl = ucCtrlCode;
        break;

        case SCI_CTRL_CHG_BST:
            s_tDCCtrl.ucBoostChgEn = ucCtrlCode;
        break;

        case SCI_CTRL_CONTACTOR:
            s_tDCCtrl.ucContactor = ucCtrlCode;
        break;

        case SCI_CTRL_CHG_MANCHIN_TEST:
            s_tDCCtrl.ucChargeTestMode = ucCtrlCode;
        break;

        case SCI_CTRL_TEST_RELAY:
            s_tDCCtrl.ucRelayTest = ucCtrlCode;
        break;

        default:
            return FAILURE;
    }
    return SUCCESSFUL;
}

static SHORT UpdateBduCtrlRecordInfoPart_2(BYTE ucCmdType, BYTE ucCtrlCode)
{
    switch (ucCmdType)
    {
        case SCI_CTRL_TEST_WAVE_PRT:
            s_tDCCtrl.ucWavePrtTest = ucCtrlCode;
        break;
        case SCI_CTRL_EMC_TEST:
            s_tDCCtrl.ucEMCTest = ucCtrlCode;
        break;

        case SCI_CTRL_STOP_CAN_COMM:
            s_tDCCtrl.ucStopCan = ucCtrlCode;
        break;
        
        case SCI_CTRL_WATER_INGRNESS:
            s_tDCCtrl.ucWaterIngrnessEn = ucCtrlCode;
        break;
        

        default:
            return FAILURE;
    }
    return SUCCESSFUL;
}

// 控制命令接口: ucCmdType 指令码CID2; ucCtrlCode 指令内容
BOOLEAN BduCtrl(BYTE ucCmdType, BYTE ucCtrlCode)
{
    SHORT retRecord_1 = 0;
    SHORT retRecord_2 = 0;
    T_BduCmdStruct  tBduCmd;
    rt_memset(&tBduCmd, 0, sizeof(T_BduCmdStruct));
    retRecord_1 =  UpdateBduCtrlRecordInfoPart_1(ucCmdType, ucCtrlCode);
    retRecord_2 = (SUCCESSFUL == retRecord_1) ? FAILURE : UpdateBduCtrlRecordInfoPart_2(ucCmdType, ucCtrlCode);
    if ((retRecord_1 != SUCCESSFUL) && (retRecord_2 != SUCCESSFUL))
    {
        return False;
    }

    tBduCmd.ucCmd = SCI_CID_SET_OPS;
    tBduCmd.ucCommandType = ucCmdType;
    tBduCmd.wTemp = (WORD)ucCtrlCode;
    SetCmd(&tBduCmd);

    return True;
}

Static void SciDCCtrlRspPart_1(BYTE ucPort)
{
    switch (s_tCommManage.aucBuf[SCI_SEG_REV1+1])
    {
        case SCI_CTRL_CHG2CHG:
            s_tDCCtrlRet.ucChange2Chg = s_tCommManage.aucBuf[SCI_SEG_RTN];
        break;
        case SCI_CTRL_SLEEP:
            s_tDCCtrlRet.ucSleep = s_tCommManage.aucBuf[SCI_SEG_RTN];
        break;
        case SCI_CTRL_CHG_STG:
            s_tDCCtrlRet.ucChgStraight = s_tCommManage.aucBuf[SCI_SEG_RTN];
        break;
        case SCI_CTRL_DISCHG_STG:
            s_tDCCtrlRet.ucDisChgStraight = s_tCommManage.aucBuf[SCI_SEG_RTN];
        break;
        case SCI_CTRL_CHG_PRT:
            s_tDCCtrlRet.ucChgPrt = s_tCommManage.aucBuf[SCI_SEG_RTN];
        break;
        case SCI_CTRL_DISCHG_PRT:
            s_tDCCtrlRet.ucDischgPrt = s_tCommManage.aucBuf[SCI_SEG_RTN];
        break;
        case SCI_CTRL_OPEN_LOOP:
            s_tDCCtrlRet.ucOpenloop = s_tCommManage.aucBuf[SCI_SEG_RTN];
        break;
        case SCI_CTRL_TEST:
            s_tDCCtrlRet.ucApptest = s_tCommManage.aucBuf[SCI_SEG_RTN];
        break;
        case SCI_CTRL_HEATER:
            s_tDCCtrlRet.ucHeater = s_tCommManage.aucBuf[SCI_SEG_RTN];
        break;
        case SCI_CTRL_BDULOCK:
            s_tDCCtrlRet.ucLockCtrl = s_tCommManage.aucBuf[SCI_SEG_RTN];
        break;
        case SCI_CTRL_CHG_BST:
            s_tDCCtrlRet.ucBoostChgEn = s_tCommManage.aucBuf[SCI_SEG_RTN];
        break;
        case SCI_CTRL_CONTACTOR:
            s_tDCCtrlRet.ucContactor = s_tCommManage.aucBuf[SCI_SEG_RTN];
        break;
		case SCI_CTRL_CHG_MANCHIN_TEST:
            s_tDCCtrlRet.ucChargeTestMode = s_tCommManage.aucBuf[SCI_SEG_RTN];
        break;

        case SCI_CTRL_TEST_RELAY:
            s_tDCCtrlRet.ucRelayTest = s_tCommManage.aucBuf[SCI_SEG_RTN];
        break;
    }
}

Static void SciDCCtrlRspPart_2(BYTE ucPort)
{
    switch (s_tCommManage.aucBuf[SCI_SEG_REV1+1])
    {
        case SCI_CTRL_TEST_WAVE_PRT:
            s_tDCCtrlRet.ucWavePrtTest = s_tCommManage.aucBuf[SCI_SEG_RTN];
        break;
        case SCI_CTRL_EMC_TEST:
            s_tDCCtrlRet.ucEMCTest = s_tCommManage.aucBuf[SCI_SEG_RTN];
        break;
        case SCI_CTRL_STOP_CAN_COMM:
            s_tDCCtrlRet.ucStopCan = s_tCommManage.aucBuf[SCI_SEG_RTN];
        break;
        
        case SCI_CTRL_WATER_INGRNESS:
            s_tDCCtrlRet.ucWaterIngrnessEn = s_tCommManage.aucBuf[SCI_SEG_RTN];
        break;
        
    }
}

// 控制命令响应函数，记录执行返回情况
Static void SciDCCtrlRsp(BYTE ucPort)
{
    SciDCCtrlRspPart_1(ucPort);
    SciDCCtrlRspPart_2(ucPort);
}

// 获取控制指令接口
BOOLEAN GetBduCtrl(T_DCCtrl *pCtrl)
{
    if (NULL == pCtrl)
    {
        return False;
    }
    rt_memcpy(pCtrl, &s_tDCCtrl, sizeof(T_DCCtrl));
    
    return True;
}


// 查询BDU出厂信息: PollBdu中，开机获取一次
// 查询BDU出厂信息响应函数
static void SciDCGetFactRsp(BYTE ucPort)
{
    int index = 0;
    //BYTE ucLen = 0;
    U_32Int ulTemp;

    BYTE ucRet;
    ucRet = s_tCommManage.aucBuf[SCI_SEG_RTN];

    if (SCI_RTN_SUCCESS != ucRet)
    {
        return;  // 异常处理
    }

    index = SCI_SEG_DATA;
    //ucLen = s_tCommManage.aucBuf[index]; // 段长度
    index++;

    rt_memcpy(&s_tDCFactory.acVerDate[0],     &s_tCommManage.aucBuf[index], 4);
    rt_memcpy(&s_tDCFactory.acSysName[0],     &s_tCommManage.aucBuf[index+4], 30);
    rt_memcpy(&s_tDCFactory.acPlatformVer[0], &s_tCommManage.aucBuf[index+34], 8);
    rt_memcpy(&s_tDCFactory.acSoftVer[0],     &s_tCommManage.aucBuf[index+42], 6);
    rt_memcpy(&s_tDCFactory.acAsset[0],       &s_tCommManage.aucBuf[index+48], 30);

    ulTemp.ucData[0] = s_tCommManage.aucBuf[index+81];
    ulTemp.ucData[1] = s_tCommManage.aucBuf[index+80];
    ulTemp.ucData[2] = s_tCommManage.aucBuf[index+79];
    ulTemp.ucData[3] = s_tCommManage.aucBuf[index+78];
    s_tDCFactory.ulSN = ulTemp.LData;

    s_tDCFactory.bParalleUpdate   = (s_tCommManage.aucBuf[index+82]&0x04)>>2;
    s_tDCFactory.bActivatePort = (s_tCommManage.aucBuf[index+82]&0x02)>>1;
    s_tDCFactory.bHeatFilm     =  s_tCommManage.aucBuf[index+82]&0x01;

    s_tDCFactory.acHardwareVer = s_tCommManage.aucBuf[index+83];

    //开机读取一次
    s_tCommManage.ucGetFacInfoCnt++;

    return;
}

// 获取出厂信息接口，带是否支持加热器信息
BOOLEAN GetBduFact(T_DCFactory *pFact)
{
    if (NULL == pFact)
    {
        return False;
    }
    rt_memcpy(pFact, &s_tDCFactory, sizeof(T_DCFactory));
    
    return True;
}

// 设置资产信息接口
BOOLEAN SetBduAsset(T_DCAsset *pAsset)
{
    T_BduCmdStruct  tCmd;
    rt_memset(&tCmd, 0, sizeof(T_BduCmdStruct));

    if (NULL == pAsset)
    {
        return False;
    }

    rt_memcpy(&s_tDCAsset, pAsset, sizeof(T_DCAsset));
    tCmd.ucCmd = SCI_CID_SET_ASSET;
    SetCmd(&tCmd);   

    return True;
}

// 设置BDU资产信息响应函数
static void SciDCSetAssetRsp(BYTE ucPort)
{
    BYTE ucRet;
    ucRet = s_tCommManage.aucBuf[SCI_SEG_RTN];
    
    if (SCI_RTN_SUCCESS != ucRet)
    {
        return;  // 异常处理
    }
}


// 设置校正参数
void BduAdj( BYTE ucCode, WORD wVal )
{
    T_BduCmdStruct  tBduCmd;
    rt_memset(&tBduCmd, 0, sizeof(T_BduCmdStruct));
    tBduCmd.ucCmd = SCI_CID_SET_CALI;

    tBduCmd.wTemp = wVal;
    tBduCmd.ucCommandType = ucCode;
    SetCmd(&tBduCmd);

    s_ucBduAdjCnt = 0;
    SciDCGetAdj();

    return;
}

// 设置校正参数响应函数
static void SciDCSetAdjRsp(BYTE ucPort)
{
    BYTE ucRet;
    ucRet = s_tCommManage.aucBuf[SCI_SEG_RTN];

    if (SCI_RTN_SUCCESS != ucRet)
    {
        return;  // 异常处理
    }
}

// 查询校正参数
static void SciDCGetAdj(void)
{
    T_BduCmdStruct  tBduCmd;
    rt_memset(&tBduCmd, 0, sizeof(T_BduCmdStruct));
    tBduCmd.ucCmd = SCI_CID_GET_CALI;
    SetCmd(&tBduCmd);

    return;
}

// 查询校正参数响应函数
#ifdef DEVICE_USING_D121
static void SciDCGetAdjRsp(BYTE ucPort)
{
    int i, index;
    //BYTE  ucLen ;
    WORD wTemp = 0;

    BYTE ucRet;
    ucRet = s_tCommManage.aucBuf[SCI_SEG_RTN];
    
    if (SCI_RTN_SUCCESS != ucRet)
    {
        return;  // 异常处理
    }

    index = SCI_SEG_DATA;
    //ucLen = s_tCommManage.aucBuf[index]; // 段长度
    index++;

    i = 0;
    wTemp = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2*i]));
    s_tDCCaliGet.wBusVoltScal = wTemp;
    i++;
    wTemp = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2*i]));
    s_tDCCaliGet.wBusPinVoltScal = wTemp;
    i++;
    wTemp = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2*i]));
    s_tDCCaliGet.wBatVoltScal = wTemp;
    i++;
    wTemp = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2*i]));
    s_tDCCaliGet.wBusChaCurrScal = wTemp;
    i++;
    wTemp = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2*i]));
    s_tDCCaliGet.wBusDischaCurrScal = wTemp;
    i++;
    wTemp = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2*i]));
    s_tDCCaliGet.wBatChaCurrScal = wTemp;
    i++;
    wTemp = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2*i]));
    s_tDCCaliGet.wBatDischaCurrLScal = wTemp;
    i++;
    wTemp = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2*i]));
    s_tDCCaliGet.wBatDischaCurrHScal = wTemp;

    i++;
    wTemp = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2*i]));
    s_tDCCaliGet.wBusVoltZero = wTemp;
    i++;
    wTemp = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2*i]));
    s_tDCCaliGet.wBusPinVoltZero = wTemp;
    i++;
    wTemp = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2*i]));
    s_tDCCaliGet.wBatVoltZero = wTemp;
    i++;
    wTemp = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2*i]));
    s_tDCCaliGet.wBusChaCurrZero = wTemp;
    i++;
    wTemp = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2*i]));
    s_tDCCaliGet.wBusDischaCurrZero = wTemp;
    i++;
    wTemp = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2*i]));
    s_tDCCaliGet.wBatChaCurrZero = wTemp;
    i++;
    wTemp = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2*i]));
    s_tDCCaliGet.wBatDischaCurrLZero = wTemp;
    i++;
    wTemp = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2*i]));
    s_tDCCaliGet.wBatDischaCurrHZero = wTemp;

    return;
}
#elif defined DEVICE_USING_R321
static void SciDCGetAdjRsp(BYTE ucPort)
{
    int i, index;
    BYTE  ucLen ;

    BYTE ucRet;
    ucRet = s_tCommManage.aucBuf[SCI_SEG_RTN];

    if (SCI_RTN_SUCCESS != ucRet)
    {
        return;  // 异常处理
    }

    index = SCI_SEG_DATA;
    ucLen = s_tCommManage.aucBuf[index++]; // 段长度
    s_tDCCaliGet.wBduAdjNum = ucLen / 2;
    if(s_tDCCaliGet.wBduAdjNum >= SCI_BDUADJ_MAX_LEN)
    {
        return;
    }

    for(i = 0; i < s_tDCCaliGet.wBduAdjNum; i++)
    {
        s_tDCCaliGet.awBduAdjValue[i] = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2*i]));
    }

    return;
}
#endif

// 获取校正参数接口
BOOLEAN GetBduAdj(T_DCCaliGet *pAdj)
{
    if (NULL == pAdj)
    {
        return False;
    }
    rt_memcpy(pAdj, &s_tDCCaliGet, sizeof(T_DCCaliGet));

    return True;
}

Static void ReGetBduAdjVal(void)
{
    if(s_ucBduAdjCnt >= GET_BDU_ADJ_VAl_DELAY)
    {
        return;
    }
    else if(s_ucBduAdjCnt % 60 == 0)
    {
        SciDCGetAdj();
    }
    s_ucBduAdjCnt++;
}


// 设置调测信息
BOOLEAN SetBduMem(T_DCMemQuery *pMem)
{
    T_BduCmdStruct  tCmd;
    rt_memset(&tCmd, 0, sizeof(T_BduCmdStruct));

    if (NULL == pMem)
    {
        return False;
    }

    rt_memcpy(&s_tDCMemoSet, pMem, sizeof(T_DCMemQuery));
    tCmd.ucCmd = SCI_CID_SET_MEM;
    SetCmd(&tCmd);

    return True;
}

// 设置调测信息响应函数
static void SciDCSetMemRsp(BYTE ucPort)
{
    BYTE ucRet;
    ucRet = s_tCommManage.aucBuf[SCI_SEG_RTN];

    if (SCI_RTN_SUCCESS != ucRet)
    {
        return;  // 异常处理
    }
}

// 通知调测信息查询
BOOLEAN NotifyBduMemQuery(T_DCMemQuery *pMem)
{
    T_BduCmdStruct  tCmd;

    if (NULL == pMem)
    {
        return False;
    }

    rt_memcpy_s(&s_tDCMemoGet, sizeof(T_DCMemQuery), pMem, sizeof(T_DCMemQuery));
    s_tDCMemoGet.ulMemValue = 0;

    rt_memset_s(&tCmd, sizeof(T_BduCmdStruct), 0, sizeof(T_BduCmdStruct));
    tCmd.ucCmd = SCI_CID_GET_MEM;
    SetCmd(&tCmd);

    return True;
}

// 查询调测信息响应函数
static void SciDCGetMemRsp(BYTE ucPort)
{
    int index = 0;
    //BYTE ucLen = 0;
    U_32Int ulTemp;

    BYTE ucRet;
    ucRet = s_tCommManage.aucBuf[SCI_SEG_RTN];

    if (SCI_RTN_SUCCESS != ucRet)
    {
        return;  // 异常处理
    }

    index = SCI_SEG_DATA;
    //ucLen = s_tCommManage.aucBuf[index]; // 段长度
    index++;

    ulTemp.ucData[0] = s_tCommManage.aucBuf[index+3];
    ulTemp.ucData[1] = s_tCommManage.aucBuf[index+2];
    ulTemp.ucData[2] = s_tCommManage.aucBuf[index+1];
    ulTemp.ucData[3] = s_tCommManage.aucBuf[index];

    s_tDCMemoGet.ulMemValue = ulTemp.LData;

    return;
}

// 获取调测信息接口
BOOLEAN GetBduMem(T_DCMemQuery *pMem)
{
    if (NULL == pMem)
    {
        return False;
    }
    rt_memcpy(pMem, &s_tDCMemoGet, sizeof(T_DCMemQuery));

    return True;
}


/////////////////// 故障录波功能 begin ///////////////////
Static WORD CalcRecordParaCRC(void)
{
    int i = 0, j = 0;
    BYTE auTemp[SCI_DATA_LEN_RECORD] = {0, };
    BYTE *p = auTemp;
    WORD wTemp;

    wTemp = s_tBduRecordPara.tRecordCfg.wRecordIntrvalFront;
    p[i++] = wTemp/256;
    p[i++] = wTemp%256;

    wTemp = s_tBduRecordPara.tRecordCfg.wRecordIntrvalAfter;
    p[i++] = wTemp/256;
    p[i++] = wTemp%256;

    p[i++] = s_tBduRecordPara.tRecordCfg.ucRecordNumFront;
    p[i++] = s_tBduRecordPara.tRecordCfg.ucRecordNumAfter;
    p[i++] = s_tBduRecordPara.tRecordCfg.ucRecordPointNum;

    for (j=0; j<s_tBduRecordPara.tRecordCfg.ucRecordPointNum; j++)
    {
        p[i++] = s_tBduRecordPara.tRecordCfg.aucRecordPointID[j];
    }

    wTemp = CRC_Cal(p, i);
    wTemp = (wTemp%256)*256 + wTemp/256;

    return wTemp;
}

static void SynRecordPara(void)
{
    int i = 0;
    int TotalRecord = 0;
    T_SysPara tSysPara;

    rt_memset(&tSysPara, 0, sizeof(T_SysPara));
    GetSysPara(&tSysPara);

    TotalRecord = (int)(tSysPara.ucPreRecordNum + tSysPara.ucPostRecordNum);
    if (TotalRecord > RECORD_TOTAL_NUM)
    {
        return;
    }

    if (tSysPara.ucMeasurePointsNum > RECORD_POINT_MAX)
    {
        return;
    }

    s_tBduRecordPara.tRecordCfg.wRecordIntrvalFront = tSysPara.wPreRecordInterval;
    s_tBduRecordPara.tRecordCfg.wRecordIntrvalAfter = tSysPara.wPostRecordInterval;

    s_tBduRecordPara.tRecordCfg.ucRecordNumFront    = tSysPara.ucPreRecordNum;
    s_tBduRecordPara.tRecordCfg.ucRecordNumAfter    = tSysPara.ucPostRecordNum;
    s_tBduRecordPara.tRecordCfg.ucRecordPointNum    = tSysPara.ucMeasurePointsNum;

    for (i=0; i<s_tBduRecordPara.tRecordCfg.ucRecordPointNum; i++)
    {
        s_tBduRecordPara.tRecordCfg.aucRecordPointID[i] = tSysPara.ucMeasurePointsID[i];
    }

    s_tBduRecordPara.wCrc = CalcRecordParaCRC();

    return;
}

static void CheckRecordParaChange( WORD wCrc )
{
    T_BduCmdStruct  tCmd;
    rt_memset(&tCmd, 0, sizeof(T_BduCmdStruct));
    if ( wCrc != s_tBduRecordPara.wCrc )
    {
        tCmd.ucCmd = SCI_CID_RECORD_CONFIG;
        SetCmd(&tCmd);
    }

    return;
}

static void SciDCSetRecordParaRsp(BYTE ucPort)
{
    BYTE ucRet;
    ucRet = s_tCommManage.aucBuf[SCI_SEG_RTN];

    if (SCI_RTN_SUCCESS != ucRet)
    {
        return;  // 异常处理
    }
}

Static void SciDCRecordActiveRsp( BYTE ucCommond )
{
    T_BduCmdStruct  tCmd;
    rt_memset(&tCmd, 0, sizeof(T_BduCmdStruct));

    tCmd.ucCmd = ucCommond;
    SetCmd(&tCmd);

    return;
}

Static void SciDCRecordOrigin( BYTE ucPort)
{
    int i, index;
    int TotalRecord = 0;

    if(RECORD_CNT_SAVE_MAX <= s_wRecordSaveCnt)
    {
        return;
    }

    if(True == s_ucRecordWait)
    {
        return;
    }

    index = SCI_SEG_DATA;
    index++;

    rt_memset( &s_tBduRecordData, 0, sizeof(T_BduRecordData));

    s_tBduRecordData.tTimeNow = GetTimeStamp();
    s_tBduRecordData.ucTrigger = s_tCommManage.aucBuf[index++];

    s_tBduRecordData.tRecordCfg.wRecordIntrvalFront = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index]));
    s_tBduRecordData.tRecordCfg.wRecordIntrvalAfter = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2]));

    TotalRecord = (int)(s_tCommManage.aucBuf[index+4]+s_tCommManage.aucBuf[index+5]);
    if (TotalRecord > RECORD_TOTAL_NUM)
    {
        return;
    }

    if (s_tCommManage.aucBuf[index+6] > RECORD_POINT_MAX)
    {
        return;
    }

    s_tBduRecordData.tRecordCfg.ucRecordNumFront = s_tCommManage.aucBuf[index+4];
    s_tBduRecordData.tRecordCfg.ucRecordNumAfter = s_tCommManage.aucBuf[index+5];
    s_tBduRecordData.tRecordCfg.ucRecordPointNum = s_tCommManage.aucBuf[index+6];

    for (i=0; i<s_tBduRecordData.tRecordCfg.ucRecordPointNum; i++)
    {
        s_tBduRecordData.tRecordCfg.aucRecordPointID[i] = s_tCommManage.aucBuf[index+7+i];
    }

    SciDCRecordActiveRsp(SCI_CID_RECORD_ORIGIN);

    return;
}

Static void SciDCRecordData( BYTE ucPort)
{
    int i, index;
    BYTE ucTotalFrm = 0, ucCurrentFrm = 0;
    BYTE ucId = 0;
    static WORD s_wCounter = 0;

    if(RECORD_CNT_SAVE_MAX <= s_wRecordSaveCnt)
    {
        return;
    }

    if(True == s_ucRecordWait)
    {
        return;
    }

    index = SCI_SEG_DATA;
    index++;

    ucTotalFrm   = s_tCommManage.aucBuf[SCI_SEG_REV1];
    ucCurrentFrm = s_tCommManage.aucBuf[SCI_SEG_REV1+1];

    if (ucTotalFrm != (s_tBduRecordData.tRecordCfg.ucRecordNumFront + s_tBduRecordData.tRecordCfg.ucRecordNumAfter) ||
        ucTotalFrm > RECORD_TOTAL_NUM)
    {
        return;
    }

    if ((ucCurrentFrm > ucTotalFrm) || (ucCurrentFrm < 1))
    {
        return;
    }

    ucId = (ucCurrentFrm - 1) * RECORD_POINT_MAX;

    for (i=0; i<s_tBduRecordData.tRecordCfg.ucRecordPointNum; i++)
    {
        s_tBduRecordData.awData[ucId++] = (WORD)(GetBduIntData(&s_tCommManage.aucBuf[index+2*i]));
    }

    SciDCRecordActiveRsp(SCI_CID_RECORD_DATA);

    if (ucCurrentFrm == ucTotalFrm)
    {
        if (SaveOneBduRecord(&s_tBduRecordData) < 0)
        {
            return;
        }

        s_ucRecordWait = True;

        TimerPlus(s_wCounter, RECORD_CNT_SAVE_CYCLE);

        if (RECORD_CNT_SAVE_CYCLE <= s_wCounter)
        {
            TimerPlus(s_wRecordSaveCnt, RECORD_CNT_SAVE_MAX);
            writeBduRecordCounter(&s_wRecordSaveCnt);
            s_wCounter = 0;
        }
    }

    return;
}

rt_int32_t checkRecordCnt(void)
{
    rt_kprintf("record save times: %d\n", s_wRecordSaveCnt);

    return 0;
}
FINSH_FUNCTION_EXPORT(checkRecordCnt, show record save times);
MSH_CMD_EXPORT(checkRecordCnt, show record save times);


Static void FaultRecordSave(void)
{
    if (s_ucRecordWait == True)
    {
        TimerPlus(s_wRecordTimer, RECORD_SAVE_CYCLE);
    }

    if (s_wRecordTimer == RECORD_SAVE_CYCLE)
    {
        s_ucRecordWait = False;
        s_wRecordTimer = 0;
    }

    return;
}
/////////////////// 故障录波功能 end ///////////////////

/****************************************************************************
* 函数名称：GetBduIntData
* 输入参数：无
* 返 回 值：整数
* 功能描述：获取整型数（根据协议传送顺序决定是否改变字节序）
// 作    者 ：王威
// 版本信息：V1.0
// 设计日期：2010-06-13
// 修改记录：
// 日    期     版  本      修改人      修改摘要
// 其他说明：
***************************************************************************/
Static WORD GetBduIntData( BYTE *p )
{
    U_Int tData = {0};
    //PCLINT检查:经确认，以下3条对程序正确性无影响。
    tData.ucByte[1] = *p;
    tData.ucByte[0] = *(p+1);
    return tData.iData;
}

BOOLEAN IsBduCommFail(void)
{
    return s_tCommManage.bCommFail;
}

//点对点升级（并发升级不退主机）
BOOLEAN IsBduP2PUpdate(void)
{
    if (!s_ucParalleUpdateFlag) {
        return (s_tCommManage.ucCommStage == BDU_UPGRADE);
    }
    return False;
}

void SetBduParalleUpdate(BOOLEAN bUpdate)
{
    SetBduUpdate(bUpdate);
    s_ucParalleUpdateFlag = bUpdate;
    return;
}

//包括点对点升级和并发升级
BOOLEAN IsBduUpdate(void)
{
    return (s_tCommManage.ucCommStage == BDU_UPGRADE);
}

void SetBduUpdate(BOOLEAN bUpdate)
{
    if (!bUpdate)
    {
        SetBduParallUpdateFlag(False);
        ResetMCU(NO_RESET_UPGRADE_TIMEOUT);
    } else {
        s_tCommManage.ucCommStage = BDU_UPGRADE;
        s_ucParalleUpdateFlag = False;
    }
    return;
}


uint8_t ConvertBduStatus(uint8_t ucBduStatus)
{
    if (BDU_STATUS_NUM > ucBduStatus)
    {
        return (acBduStsConvert[ucBduStatus]);
    }
    else
    {
        return BDU_STATUS_ERROR;
    }
}

static void RefreshCurrBalanceInfo(void)
{
    T_SysPara tSysPara = {0, };
    T_BattResult tBattout;

    rt_memset(&tBattout, 0, sizeof(T_BattResult));

    GetSysPara(&tSysPara);
    GetBattResult(&tBattout);

    s_tCurrBalanceInfo.ucCurrBalanceAmplitude = tSysPara.ucCurrBalanceAmplitude; //均流SOC补偿幅值
    s_tCurrBalanceInfo.ucCurrBalanceSlope = tSysPara.ucCurrBalanceSlope; //均流SOC补偿斜率
    s_tCurrBalanceInfo.wBattSOC = tBattout.wBattHighPrecSOC; // 电池实时容量
    s_tCurrBalanceInfo.bBroadcastEn = IsMaster(); //功率均流通信使能，功率判断如果是主机且只有R321，则下发功率均流帧；如果是主机且存在B3，则主机不处理均流电流

#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
    if (tSysPara.ucCurrBalanceMethod >= 2)
    {
        tSysPara.ucCurrBalanceMethod = 0; // 均流方式
    }
    s_tCurrBalanceInfo.bCurrBalanceEn = tSysPara.ucCurrBalanceMethod; // 均流方式
    s_tCurrBalanceInfo.wBattCap = tSysPara.wBatteryCap; // 电池额定容量
    s_tCurrBalanceInfo.bB3CurrBalance = tBattout.bB3BattExist;  //是否B3均流标志
    s_tCurrBalanceInfo.ucBattSOH = (BYTE)tBattout.wBatSOH;
    s_tCurrBalanceInfo.wCurrBalanceMagnif = Float2Word(fabs(tBattout.fBalanceCurr) * 1000); // BDU均流电流倍率,R321修改精度为3
#else
    s_tCurrBalanceInfo.bCurrBalanceEn = tSysPara.bCurrBalanceSOCEn; // 均流SOC补偿使能
    s_tCurrBalanceInfo.wBattCap = (WORD)((float)tSysPara.wBatteryCap * tBattout.wBatSOH / 100); // 电池实际容量 = 电池额定容量 * SOH
    s_tCurrBalanceInfo.wCurrBalanceMagnif = Float2Word(fabs(tBattout.fBalanceCurr) * 100); // BDU均流电流倍率
#endif
}


/**
 * @brief 获取硬件版本属性
 *
 * 根据硬件版本号获取对应的硬件版本属性。
 *
 * @param  ucVersion 硬件版本号
 * @return 如果硬件版本有效，返回对应的硬件版本属性；否则，返回NULL
 */
Static const T_HardwareVerStruct* GetHardwareVerAttr(BYTE ucVersion)
{
    if ((ucVersion < 1) || (ucVersion > sizeof(s_atHardwareVerAttr) / sizeof(s_atHardwareVerAttr[0])))
    {
        return NULL;
    }

    return &s_atHardwareVerAttr[ucVersion-1];
}

/**
 * @brief 判断是否支持陀螺仪
 *
 * 判断硬件是否支持陀螺仪功能
 *
 * @return 如果硬件版本为1、2、3、8、9，返回True；否则返回False
 */
BOOLEAN IsHardwareSupportGyro(void)
{
    static T_DCFactory tBduFact;
    rt_memset(&tBduFact, 0, sizeof(T_DCFactory));
    GetBduFact(&tBduFact);

    const T_HardwareVerStruct* attr = GetHardwareVerAttr(tBduFact.acHardwareVer);
    if (attr == NULL)
    {
        return False;
    }

    return attr->bGyro == 1 ? True : False;
}

/**
 * @brief 判断是否支持加热膜
 *
 * 判断硬件是否支持加热膜功能
 *
 * @return 如果硬件版本为3、6、8，返回True；否则返回False
 */
BOOLEAN IsHardwareSupportHeater(void)
{
    static T_DCFactory tBduFact;
    rt_memset(&tBduFact, 0, sizeof(T_DCFactory));
    GetBduFact(&tBduFact);

    const T_HardwareVerStruct* attr = GetHardwareVerAttr(tBduFact.acHardwareVer);
    if (attr == NULL)
    {
        return False;
    }

    return attr->bHeatingFilm == 1 ? True : False;
}

/**
 * @brief 判断是否支持防盗线
 *
 * 判断硬件是否支持防盗线功能
 *
 * @return 如果硬件版本为1、2、3、8、9，返回True；否则返回False
 */

BOOLEAN IsSupportsAntiTheftLine(void)
{
    static T_DCFactory tBduFact;
    rt_memset(&tBduFact, 0, sizeof(T_DCFactory));
    GetBduFact(&tBduFact);

    const T_HardwareVerStruct* attr = GetHardwareVerAttr(tBduFact.acHardwareVer);
    if (attr == NULL)
    {
        return False;
    }
    if (attr->bPE == 1) //硬件版本支持防盗线
    {
        #ifdef DEVICE_USING_R321
        //IO信号判断硬件是否支持防盗线
        if (rt_pin_read(PE_THEFT_PIN) == PIN_LOW)
        {
            return True;
        }
        #else
        return True; 
        #endif
    }
    return False;
}


/**
 * @brief 判断是否支持消防功能
 *
 * 判断硬件是否支持消防功能
 *
 * @return 如果硬件版本为7，8，返回True；否则返回False
 */
BOOLEAN IsSupportsFireControl(void)
{
    static T_DCFactory tBduFact;
    rt_memset(&tBduFact, 0, sizeof(T_DCFactory));
    GetBduFact(&tBduFact);

    const T_HardwareVerStruct* attr = GetHardwareVerAttr(tBduFact.acHardwareVer);
    if (attr == NULL)
    {
        return False;
    }

    return attr->bFire == 1 ? True : False;
}

/**
 * @brief 判断是否支持GPS功能
 *
 * 判断是否支持GPS功能
 *
 * @return 如果硬件版本为2，5或者全配版本，返回True；否则返回False
 */

BOOLEAN IsSupportsGpsControl(void)
{
    static T_DCFactory tBduFact;
    rt_memset_s(&tBduFact, sizeof(T_DCFactory), 0, sizeof(T_DCFactory));
    GetBduFact(&tBduFact);

    const T_HardwareVerStruct* attr = GetHardwareVerAttr(tBduFact.acHardwareVer);
    if (attr == NULL)
    {
        return False;
    }

    return attr->b4GGPS == 1 ? True : False;
}

