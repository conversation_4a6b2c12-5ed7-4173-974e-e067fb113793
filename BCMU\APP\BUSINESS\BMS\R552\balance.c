
#include "balance.h"
#include <utils_rtthread_security_func.h>
#include <stdio.h>
#include <string.h>

SingleCellBalanceComponent component;

// 辅助函数：获取最低电压的索引
static int GetMinVoltageIndex(const RealTimeDataStruct* data) {
    if (data->cellCount <= 0) return -1;
    int minIndex = 0;
    for (int i = 1; i < data->cellCount; ++i) {
        if (data->cellVoltages[i] < data->cellVoltages[minIndex]) {
            minIndex = i;
        }
    }
    return minIndex;
}

// 均衡算法单元
int RunBalanceControlAlgorithm(SingleCellBalanceComponent* component, const RealTimeDataStruct* data) {
    if (!component || !data) return -1;

    unsigned short balanceCode = 0;
    float minVoltage = data->cellVoltages[GetMinVoltageIndex(data)];
    BalanceConfigStruct config = component->config;

    for (int i = 0; i < data->cellCount; ++i) {
        float voltage = data->cellVoltages[i];
        if (voltage > 3.36f ) {
			if((voltage - minVoltage) > config.startBalanceVoltageDifference && config.startBalanceVoltageDifference > 0){
				balanceCode |= (1 << i);
			}
        } else if (voltage >= 3.2f ) {
			if((voltage - minVoltage) > 0.1f){
				balanceCode |= (1 << i);
			}
        } else if (voltage >= 3.1f ) {
			if((voltage - minVoltage) > 0.3f){
				balanceCode |= (1 << i);
			}
        }
    }

    // 更新均衡状态
    if (component->state.isBalancing) {
        component->balanceCounter++;
        if (component->balanceCounter >= 120) {
            component->algorithmResult.balanceStatus = 0;
            component->algorithmResult.balanceCode = 0;
            component->balanceCounter = 0;
            component->state.isBalancing = false;
            ExecuteBalanceControl(component);
        } else {
            component->algorithmResult.balanceStatus = 2;
        }
    } else {
        if (balanceCode != 0) {
            component->algorithmResult.balanceStatus = 1;
            component->algorithmResult.balanceCode = balanceCode;
            component->state.isBalancing = true;
            component->balanceCounter = 0;
            ExecuteBalanceControl(component);
        } else {
            component->algorithmResult.balanceStatus = 0;
            component->algorithmResult.balanceCode = 0;
        }
    }

    return 0;
}

// 均衡控制单元
int IdentifyCellSamplingChip(SingleCellBalanceComponent* component) {
    // 这里假设芯片识别总是成功的
    return 0;
}

int ExecuteBalanceControl(SingleCellBalanceComponent* component) {
    if (!component) return -1;

    // 执行均衡控制逻辑
    // unsigned short balanceCode = component->algorithmResult.balanceCode;
    // unsigned char balanceStatus = component->algorithmResult.balanceStatus;

    // 根据balanceCode和balanceStatus执行具体控制逻辑
    // 这里只是一个示例，实际实现可能需要硬件接口

    return 0;
}

// 故障检测单元
int DetectBalanceCircuitFault(SingleCellBalanceComponent* component, const RealTimeDataStruct* data) {
    if (!component || !data) return -1;

    unsigned short balanceCode = component->algorithmResult.balanceCode;
    bool faultSignal = GetBalanceFaultSignal();

    if (balanceCode == 0 && faultSignal) {
        component->openCircuitCounter++;
        component->shortCircuitCounter = 0;
        if (component->openCircuitCounter >= 60) {
            component->state.hasFault = 1; // 断路故障
            UpdateBalanceCircuitFaultState(component, 1);
        }
    } else if (balanceCode != 0 && !faultSignal) {
        component->shortCircuitCounter++;
        component->openCircuitCounter = 0;
        if (component->shortCircuitCounter >= 60) {
            component->state.hasFault = 2; // 短路故障
            UpdateBalanceCircuitFaultState(component, 2);
        }
    } else {
        component->shortCircuitCounter = 0;
        component->openCircuitCounter = 0;
        component->state.hasFault = 0; // 正常
        UpdateBalanceCircuitFaultState(component, 0);
    }

    return 0;
}

int UpdateBalanceCircuitFaultState(SingleCellBalanceComponent* component, uint8_t newFaultState) {
    if (!component) return -1;

    component->state.hasFault = newFaultState;
    return 0;
}

// 配置管理单元
int ManageConfigParams(SingleCellBalanceComponent* component, const BalanceConfigStruct* config) {
    if (!component || !config) return -1;

    component->config = *config;
    return 0;
}

// 参数管理组件
int SetConfigParam(BalanceConfigStruct* config, float startBalanceVoltageDifference) {
    if (!config) return -1;

    config->startBalanceVoltageDifference = startBalanceVoltageDifference;
    return 0;
}

BalanceConfigStruct GetConfigParam(const SingleCellBalanceComponent* component) {
    if (!component) {
        BalanceConfigStruct defaultConfig = {0};
        return defaultConfig;
    }

    return component->config;
}

// 数据管理组件
int SetRealTimeData(SingleCellBalanceComponent* component, const RealTimeDataStruct* data) {
    if (!component || !data) return -1;

    component->realTimeData = *data;
    return 0;
}

RealTimeDataStruct GetRealTimeData(const SingleCellBalanceComponent* component) {
    if (!component) {
        RealTimeDataStruct defaultData = {0};
        return defaultData;
    }

    return component->realTimeData;
}

// 电芯均衡初始化接口
void InitializeSingleCellBalance(int cellCount, SingleCellBalanceComponent* component) {
    if (!component) return;

    component->realTimeData.cellCount = cellCount;
    rt_memset_s(component->realTimeData.cellVoltages, sizeof(component->realTimeData.cellVoltages), 0, sizeof(component->realTimeData.cellVoltages));
    component->state.isBalancing = false;
    component->state.hasFault = 0;
    component->algorithmResult.balanceStatus = 0;
    component->algorithmResult.balanceCode = 0;
    component->balanceCounter = 0;
    component->shortCircuitCounter = 0;
    component->openCircuitCounter = 0;
}

// 获取均衡故障信号接口
bool GetBalanceFaultSignal() {
    // 这里假设一个简单的故障信号生成逻辑
    static bool faultSignal = false;
    faultSignal = !faultSignal; // 简单切换故障信号
    return faultSignal;
}

// 主函数
void bal_main(void * param) {
    // SingleCellBalanceComponent component;
    RealTimeDataStruct data = {
        .cellCount = 16,
        .cellVoltages = {3.4, 3.35, 3.3, 3.25, 3.2, 3.15, 3.1, 3.05, 3.0, 2.95, 2.9, 2.85, 2.8, 2.75, 2.7, 2.65}
    };

    InitializeSingleCellBalance(16, &component);
    SetRealTimeData(&component, &data);

    while (true) {
        RunBalanceControlAlgorithm(&component, &data);
        DetectBalanceCircuitFault(&component, &data);

        // 模拟1秒延迟
        // for (volatile int i = 0; i < 1000000; ++i) {}

        // // 更新实时数据（模拟）
        // for (int i = 0; i < data.cellCount; ++i) {
        //     data.cellVoltages[i] += (float)(rand() % 100) / 1000.0f;
        // }
        //更新单体电压：需要外部接口
        //更新均衡压差启动阈值：需要外部接口
    }

    return;
}

