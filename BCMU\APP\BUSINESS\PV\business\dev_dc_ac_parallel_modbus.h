#ifndef _DEV_DC_AC_PARALLEL_MODBUS_H
#define _DEV_DC_AC_PARALLEL_MODBUS_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */


#include "device_type.h"
#include "cmd.h"
#include "linklayer.h"
#include "protocol_layer.h"
#include "dev_dc_ac_modbus.h"
#include "storage.h"
#include "north_data_utils.h"

/* 缓冲区长度 */
#define R_BUFF_LEN_2048                    2048       ///<  接收缓冲区长度
#define S_BUFF_LEN_2048                    2048      ///<  发送缓冲区长度
//iv数据
#define IV_SINGLE_PART_DATA1_LEN           200      ///<iv单块数据的第一部分数据长度
#define IV_SINGLE_PART_DATA2_LEN           56       ///<iv单块数据的第二部分数据长度
#define IV_SINGLE_DATA_LEN                 256      ///<iv单块数据长度
//实时数据是否需要立刻上送给网管
typedef enum {
    DATA_NO_CHG_NO_SUBMIT = 0x00,
    DATA_CHG_NO_SUBMIT = 0x01,
    DATA_NO_CHG_SUBMIT = 0x10,
    DATA_CHG_SUBMIT = 0x11,
}data_submit_type;


// inverter_modbus命令一标识ID 定义
#define  CMD_INVERTER_PARALLEL_MODBUS_GET_ALARM      0x01           ///<  获取告警量
#define  CMD_INVERTER_PARALLEL_MODBUS_SET_PARA_1     0x03           ///<  设置参数
#define  CMD_INVERTER_PARALLEL_MODBUS_SET_PARA_2     0x04           ///<  设置参数
#define  CMD_INVERTER_PARALLEL_MODBUS_SET_PARA_3     0x05           ///<  设置参数
#define  CMD_INVERTER_PARALLEL_MODBUS_CTRL_CMD       0x06           ///<  控制命令
#define  CMD_INVERTER_PARALLEL_MODBUS_GET_FACTORY    0x07           ///<  获取实时量厂家数据
#define  CMD_INVERTER_PARALLEL_MODBUS_GET_ANALOG_1   0x08           ///<  获取实时模拟量数据1
#define  CMD_INVERTER_PARALLEL_MODBUS_GET_ANALOG_2   0x09           ///<  获取实时模拟量数据2
#define  CMD_INVERTER_PARALLEL_MODBUS_GET_DIGIT      0x0A           ///<  获取实时数字量
#define  CMD_INVERTER_PARALLEL_MODBUS_CTRL_POWER_ON  0x0B           ///<  遥控功率开
#define  CMD_INVERTER_PARALLEL_MODBUS_CTRL_POWER_OFF 0x0C           ///<  遥控功率关
#define  CMD_INVERTER_PARALLEL_MODBUS_CTRL_IV_SCAN_START  0x0D      ///<  IV扫描启动
#define  CMD_INVERTER_PARALLEL_MODBUS_CTRL_IV_SCAN_STOP 0x0E        ///<  IV扫描停止
#define  CMD_INVERTER_PARALLEL_MODBUS_SET_ONE_PARA 0x0F             ///<  设置单个参数
#define  CMD_INVERTER_PARALLEL_MODBUS_GET_PARA_1 0x10               ///<  获取参数
#define  CMD_INVERTER_PARALLEL_MODBUS_GET_PARA_2 0x11               ///<  获取参数
#define  CMD_INVERTER_PARALLEL_MODBUS_GET_PARA_3 0x12               ///<  获取参数
#define  CMD_INVERTER_PARALLEL_MODBUS_CTRL_RESTART_SYSTEM  0x13     ///<  系统重启
#define  CMD_INVERTER_PARALLEL_MODBUS_CTRL_START_CHECK_ISOLATE  0x14     ///<  启动绝缘阻抗诊断
#define  CMD_INVERTER_PARALLEL_MODBUS_CTRL_START_CHECK_AFCI     0x15     ///<  启动AFCI自检

// 功能码ID
#define GET_ANA_DATA        0x04            //< 获取模拟量、状态量、告警量、版本信息
#define GET_PARA_DATA       0x03            //< 获取参数量
#define SET_PARA_DATA       0x10            //< 设置参数量

#define EE_VALID_DATA       0x55            //< EEPROM数据有效

#define PV_INVERTER_MODBUS_SERVER_ID          NORTH_SERVER + 4

#define NO_MATCH_REGISTER_ADDR -1

#define DATA_LEN_2          2//数据长度为2
#define DATA_LEN_4          4 //数据长度为4
#define DATA_LEN_6          6 //数据长度为6
#define EEPROM_ALARM_UPDATE   1

#define SPECIAL_DATA_NUM      7   // 并机写sdram特殊sid数量

typedef struct{
    unsigned short      register_addr;                          //寄存器地址
    unsigned char       reserve_flag;                           //该寄存器值是否保留，1是保留
    char         old_type;                               //原始数据类型
    char         type;                                   //modbus传输数据类型
    unsigned char       precision;                              //modbus精度
    unsigned char       src_data_len;                           //南向原始数据长度
    unsigned char       data_len;                               //modbus上送数据长度
    sid_type_e          sid_type;                               //sid的类型
    unsigned int        data_addr;                              //数据取值地址
}modbus_parallel_addr_map_data_t;

typedef struct{
    unsigned short cmd_id;
    unsigned char func_code;
    unsigned short register_addr;
    unsigned short register_num;
}cmd_id_to_register_info_t;

typedef struct{
    unsigned short cmd_id;
    unsigned short ctrl_status;
}ctrl_set_cmd_modbus_t;

#pragma pack(1)

typedef struct {
    char flag ;         // 高四位(1:立刻上送，0:非立刻上送)，低四位表示(1:值变化，0:值未变化)
    char value[32];     // 根据sid判断类型，字符串上限为32字节
    unsigned int sid;
} realdata_to_sdram_t;
#pragma pack()

dev_type_t *init_dev_inverter_modbus_parallel_master(void);
int find_cmd_id_match_register_info(unsigned short cmd_id, cmd_id_to_register_info_t* input_table,
                                    unsigned short input_table_len, cmd_id_to_register_info_t** ouputdata);
int set_register_info(unsigned char cmd_id, unsigned short register_addr, unsigned short register_num);
int find_register_addr_index_by_id_universal(unsigned int interact_id, int table_size,modbus_addr_map_data_t* data_map);
int set_cmd_deal(unsigned char cmd_id, unsigned int interact_id);
int find_ctrl_value_in_table(unsigned char cmd_id, ctrl_set_cmd_modbus_t* input_table, unsigned short input_table_len);
unsigned char judge_alarm_change(unsigned char alarm_value, unsigned int alaram_id, alarm_to_eeprom_t* input_alarm,unsigned short len_alarm);
int parse_alarm_data_master(void* dev_inst, void* cmd_buff);
int pack_alarm_data_master(void* dev_inst, void* cmd_buff);
int pack_master_req_real_analog_frm(dev_inst_t* dev_inst, cmd_buf_t* cmd_buff);
int pack_crtl_cmd_master(void* dev_inst, void* cmd_buff);
int pack_set_data_master(void* dev_inst, void* cmd_buff);
void modbus_register_master_cmd_table();
int parse_crtl_cmd_master(void* dev_inst, void* cmd_buff);
int parse_set_data_master(void* dev_inst, void* cmd_buff);
int universal_pack_crtl_cmd(void* cmd_buff);
int universal_pack_set_para(void* cmd_buff, int table_size, modbus_addr_map_data_t* data_map, int offset);
int universal_pack_register_info(void* cmd_buff,cmd_id_to_register_info_t* input_table);
int write_realdata_to_sdram(char addr);
int write_data_to_sdram(char addr, int data_type, data_info_id_verison_t *pack_data_info, int data_num);
int write_special_data_to_sdram(char addr);
int get_one_slave_data(char addr, unsigned int sid, void* data);
int judge_realdata_immediate_submit(char addr);
int init_alarm_eeprom(parallel_alarm_msg_t* eeprom_msg);
int process_parallel_alarm(int reg_nums, int index, parallel_alarm_msg_t* eeprom_msg, unsigned char *data_buff);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  // _DEV_DC_AC_PARALLEL_MODBUS_H
