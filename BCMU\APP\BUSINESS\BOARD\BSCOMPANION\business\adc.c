#include <board.h>
#include <rtthread.h>
#include <rtdevice.h>
#include <drivers/adc.h>
#include "pin.h"
#include "pin_define.h"
#include "hal_adc.h"

#define ADC_DEV_NAME        "adc0"
#define REFER_VOLTAGE       250         /* VOLTAGE  is 3.3V */
#define CONVERT_BITS        (1 << 12)   /*  12bit */

static int set_selection_sample_gpio(ADC_SAMPLE_CHANNEL channel);

#ifdef USING_DEVICE_BSC_UIB03
static ADC_CHANNEL_INFO_STRUCT s_sample_info[SAMPLE_CHANNL_END] =
{
	 {VCC2V5_T,      0, 0},

	 {UIB03_TB_IN_1, 1, 0},
	 {UIB03_TB_IN_2, 2, 0},
	 {UIB03_RLY_IN3, 3, 0},

	 {PIN_DOOR_IN,   0, 8},
	 {PIN_WATER_IN,  0, 9},
	 {UIB03_HUM_IN,  0, 0},

	 {UIB03_FAN_IN1, 0, 1},
	 {UIB03_FAN_IN2, 0, 2},
	 {UIB03_PWM_OUT, 0, 7},

	 {UIB03_RLY_IN1, 0,11},
	 {UIB03_RLY_IN2, 0,12},
};
#else
static ADC_CHANNEL_INFO_STRUCT s_sample_info[SAMPLE_CHANNL_END] =
{
	 {IL1,  0, 4},
	 {IL2,  1, 4},
	 {IL3,  2, 4},
	 {IL4,  3, 4},
	 {IL5,  4, 4},
	 {IL6,  5, 4},
	 {IL7,  6, 4},
	 {IL8,  7, 4},
	 {IL9,  0, 3},
	 {IL10, 1, 3},
	 {F_01,  0, 0},
	 {F_02,  1, 0},
	 {F_03,  2, 0},
	 {F_04,  3, 0},
	 {F_05,  4, 0},
	 {F_06,  5, 0},
	 {F_07,  6, 0},
	 {F_08,  7, 0},
	 {TB_IN1,  1, 1},
	 {TB_IN2,  3, 1},
	 {F_09,  4, 1},
	 {F_10,  5, 1},
	 {JK01,   6, 1},
	 {JK02,   7, 1},
	 {JK03,   0, 2},
	 {JK04,   1, 2},
	 {JK05,   2, 2},
	 {JK06,   3, 2},
	 {JK07,   4, 2},
	 {JK08,   5, 2},
	 {JK09,   6, 2},
	 {JK10,   7, 2},
};
#endif

int adc_get_data_by_channel(ADC_SAMPLE_CHANNEL channel)
{
    rt_uint32_t value;
    rt_adc_device_t adc_dev;

    adc_dev = (rt_adc_device_t)rt_device_find(ADC_DEV_NAME);
    if (adc_dev == RT_NULL)
    {
        rt_kprintf("adc sample run failed! can't find %s device!\n", ADC_DEV_NAME);
        return RT_ERROR;
    }
    set_selection_sample_gpio(channel);
    rt_thread_mdelay(20);
	rt_adc_enable(adc_dev, s_sample_info[channel].adc_channel);
    value = rt_adc_read(adc_dev, s_sample_info[channel].adc_channel);
    rt_adc_disable(adc_dev, s_sample_info[channel].adc_channel);
    return value;
}

int hal_board_gpio_init(void)
{
	rt_pin_mode(PIN_A0_S1, PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_A1_S1, PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_A2_S1, PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_ADDR_S1, PIN_MODE_INPUT_PULLDOWN);
    rt_pin_mode(PIN_ADDR_S2, PIN_MODE_INPUT_PULLDOWN);
    rt_pin_mode(PIN_ADDR_S3, PIN_MODE_INPUT_PULLDOWN);
	rt_pin_mode(PIN_LED_CONTROL, PIN_MODE_OUTPUT);

	#ifdef USING_DEVICE_BSC_UIB03
	rt_pin_mode(PIN_RLY_OUT1,  PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_RLY_OUT2,  PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_RLY_OUT3,  PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_RLY_OUT4,  PIN_MODE_OUTPUT);
	#else
	rt_pin_mode(PIN_RLY_D_1,  PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_RLY_D_2,  PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_RLY_D_3,  PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_RLY_D_4,  PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_RLY_D_5,  PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_RLY_D_6,  PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_RLY_D_7,  PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_RLY_D_8,  PIN_MODE_OUTPUT);
	rt_pin_mode(PIN_RLY_D_9,  PIN_MODE_OUTPUT);
    rt_pin_mode(PIN_RLY_D_10, PIN_MODE_OUTPUT);
	rt_pin_mode(PIN_RLY_P_1, PIN_MODE_OUTPUT);
	rt_pin_mode(PIN_RLY_P_2, PIN_MODE_OUTPUT);
	rt_pin_mode(PIN_RLY_P_3, PIN_MODE_OUTPUT);
	#endif
	return 0;

}

int set_selection_sample_gpio(ADC_SAMPLE_CHANNEL channel)
{
    unsigned char status = 0;
    status = (s_sample_info[channel].select_status & 0x01) == 1 ? 1 : 0;
    rt_pin_write(PIN_A0_S1, status);
    status = (s_sample_info[channel].select_status >> 1 & 0x01) == 1 ? 1 : 0;
    rt_pin_write(PIN_A1_S1, status);
    status = (s_sample_info[channel].select_status >> 2 & 0x01) == 1 ? 1 : 0;
    rt_pin_write(PIN_A2_S1, status);
	return 0;
}

int get_board_addr(void)
{
    return rt_pin_read(PIN_ADDR_S1)*4 + rt_pin_read(PIN_ADDR_S2)*2 + rt_pin_read(PIN_ADDR_S3);
}

