/**
 * @brief dev_inverter main板北向通信头文件
 */

#ifndef _TOV_NORTH_MAIN_CAN_H_
#define _TOV_NORTH_MAIN_CAN_H_

#ifdef __cplusplus
extern "C" {
#endif

#include <stdio.h>
#include <rtthread.h>
#include <rtdevice.h>
#include "linklayer.h"
#include "protocol_layer.h"
#include "device_type.h"
#include "sps.h"
#include "parse_layer.h"
#include "north_main.h"

void* init_north_can(void* param);
void north_main_can(void* param);
// char init_dev_init_tab(void);
#ifdef __cplusplus
}
#endif      //< __cplusplus

#endif      //< _TOV_NORTH_MAIN_H_
