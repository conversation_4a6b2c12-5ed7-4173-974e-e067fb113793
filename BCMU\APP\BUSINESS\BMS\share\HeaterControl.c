#include "HeaterControl.h"
#include "commBdu.h"
#include "common_in.h"
#include "para.h"
#include "common.h"
#include "sample.h"
#include "battery.h"
#include "DataLoad_product.h"
#include "realAlarm.h"
#include "battery.h"
#include "qtp.h"
#include "utils_rtthread_security_func.h"

#ifdef ACTIVATE_PORT_ENABLED
#include "ActivatePort.h"
#endif

Static T_SysPara s_tSystemPara;
Static BYTE s_bHeatTurnOnPurpose = HEATER_TURNON_NONE;
Static BOOLEAN s_bBattModeCondition = False;
Static T_HeaterSysPara s_tHeaterSyspara;  //加热器控制参数
Static T_HeaterRealData s_tHeaterRealData;  //加热器相关实时数据
UNUSED Static BOOLEAN s_bHeaterCtrl = False;  //加热器使能状态标志
UNUSED Static BYTE s_bHeaterTestCtrl = HEATER_TEST_NONE;  //协议控制加热器启停标志
UNUSED Static BYTE s_ucTurnOntime = 0, s_ucTurnOfftime = 0;
UNUSED Static BYTE s_ucHeaterRealStat = 2; //加热器下发值
static void TurnOnHeater(void);
Static void TurnOffHeater(void);
Static BOOLEAN HeaterPowerOnFlag(BOOLEAN bExternalPowerOn, BYTE ucBattPackSta, BOOLEAN bChargeRotate);
Static BOOLEAN ShouldTurnOnHeater(void);
Static BOOLEAN ShouldTurnOffHeater(void);
Static BOOLEAN HeaterExceptionHanding(void);
//加热器打开
static void TurnOnHeater(void)
{
    s_ucHeaterRealStat = True;
    rt_pin_write(WARM_CTL_Pin, 1);
    BduCtrl(SCI_CTRL_HEATER, 1);
}

//加热器关闭
Static void TurnOffHeater(void)
{
    s_ucHeaterRealStat = False;
    s_bHeaterTestCtrl = HEATER_TEST_NONE;
    rt_pin_write(WARM_CTL_Pin, 0);
    BduCtrl(SCI_CTRL_HEATER, 0);
}

Static BOOLEAN HeaterPowerOnFlag(BOOLEAN bExternalPowerOn, BYTE ucBattPackSta, BOOLEAN bChargeRotate) //充放电状态是否满足开启加热器判断
{
    if((bChargeRotate == TRUE && GetBattRotateDisable() == FALSE) || bChargeRotate == FALSE)
    {
        if (bExternalPowerOn > 0) //外部有电状态
        {
            return ((ucBattPackSta == BATT_MODE_CHARGE) || (ucBattPackSta == BATT_MODE_STANDBY)) ? True : False;
        }
    }

    return False;
}

void InitHeater(void)
{
    rt_memset_s((BYTE *)&s_tHeaterSyspara, sizeof(s_tHeaterSyspara), 0, sizeof(s_tHeaterSyspara));
    rt_memset_s((BYTE *)&s_tHeaterRealData, sizeof(s_tHeaterRealData), 0, sizeof(s_tHeaterRealData));
    s_tHeaterRealData.tStatus = HEATER_OFF;
    s_bHeaterTestCtrl = HEATER_TEST_NONE;
    s_bHeaterCtrl = False;
    SetHeaterWorkingMode(HEATER_NORMAL_MODE);
    return;
}

//获取加热器相关实时数据
void RefreshHeaterInfo(T_BCMDataStruct * tBCMAnaData)
{
    if(GetApptestFlag())
    {
        SetHeaterWorkingMode(HEATER_TEST_MODE);
    }

    T_DCRealData tDcRealData;

    rt_memset_s(&s_tSystemPara, sizeof(T_SysPara), 0, sizeof(T_SysPara));
    rt_memset_s(&tDcRealData, sizeof(T_DCRealData), 0, sizeof(T_DCRealData));
    GetSysPara(&s_tSystemPara);
    GetBduReal(&tDcRealData);

    s_bHeaterCtrl = s_tSystemPara.bHeatingPadEnable;  //加热器使能状态

    s_tHeaterRealData.tStatus = tDcRealData.tDCStatus.bHeaterStatus;  //加热器开关状态
    s_tHeaterRealData.fMinCellTemp = tBCMAnaData->fCellTempMin;  //电芯最低温度
    s_tHeaterRealData.bPowerOnFlag = HeaterPowerOnFlag(tDcRealData.tDCStatus.bExternalPowerOn, tBCMAnaData->ucBattPackSta, s_tSystemPara.bChargeRotate);
    s_tHeaterRealData.fMaxCellTemp = tBCMAnaData->fCellTempMax;  //电芯最高温度
    s_bBattModeCondition = (tBCMAnaData->ucBattPackSta == BATT_MODE_CHARGE); //充电状态才满足开启条件
    return;
}

// 加热器控制处理
void DealHeaterControl(void)
{
    if(HeaterExceptionHanding())  //当存在异常情况时停止加热
    {
        if(s_tHeaterRealData.tStatus == HEATER_ON)
        {
            TurnOffHeater();
        }
        return;
    }

    if (s_tHeaterRealData.tStatus == HEATER_OFF && ShouldTurnOnHeater())
    {
        TurnOnHeater();
    }
    if (!GetHeatTimeFlag()) //QTP协议读取加热膜电流加热30S后再根据温度判断启停
    {
        if (ShouldTurnOffHeater() && s_tHeaterRealData.tStatus == HEATER_ON)
        {
            TurnOffHeater();
        }
    }

    return;
}

//设置加热器工作状态
BOOLEAN SetHeaterWorkingMode(T_HeaterWorkingMode tMode)
{
    if (tMode >= HEATER_INVALID_MODE)
    {
        return False;
    }

    if (s_tHeaterRealData.tWorkingMode == tMode)
    {
        return True;
    }

    s_tHeaterRealData.tWorkingMode = tMode;

    return True;
}


/**
 * @brief 判断是否开启加热器的条件。
 *
 * 1. 加热膜使能状态为允许。
 * 2. 没有单体温度无效告警。
 * 3. 电芯最低温度低于加热膜启动温度。
 * 4. 最高单体温度小于等于45°C。
 * 5. 处于有电状态，持续30秒。
 *
 * @return BOOLEAN 如果满足条件返回True，否则返回False。
 */
static BOOLEAN TurnOnHeaterCondition(void)
{
    // 如果外部没电，或者最高单体温度大于45°C，不开加热膜
    if (s_tHeaterRealData.bPowerOnFlag == False || s_tHeaterRealData.fMaxCellTemp > 45.0f)
    {
        return False;
    }

    // 充电状态，并且最低单体温度小于加热膜开启阈值
    if (s_bBattModeCondition && s_tHeaterRealData.fMinCellTemp < s_tSystemPara.fChgHeaterStartupTemp)
    {
        // 没有充电保护，或者只有充电低温保护，开加热膜
        if (!GetChgPrtAlmExist() || GetChgPrtAlmExistOnlyChgTempLow())
        {
            s_bHeatTurnOnPurpose = HEATER_TURNON_CHARGE;
            return True;
        }
    }

    // 放电状态，最低单体温度小于放电加热膜开启阈值
    if (s_tHeaterRealData.fMinCellTemp < s_tSystemPara.fDischgHeaterStartupTemp)
    {
        s_bHeatTurnOnPurpose = HEATER_TURNON_DISCHARGE;
        return True;
    }

    return False;
}

/**
 * @brief 判断是否关闭加热器的条件。
 *
 * 1. 最高单体温度大于45°C。
 * 2. 最低单体温度高于充电或放电加热膜关闭阈值。
 *
 * @return BOOLEAN 如果满足条件返回True，否则返回False。
 */
static BOOLEAN TurnOffHeaterCondition(void)
{
    // 最高单体温度大于45°C，关闭加热膜
    if (s_tHeaterRealData.fMaxCellTemp > 45.0f)
    {
        return True;
    }

    // 如果不在测试模式
    if (!GetQtptestFlag())
    {
        // 最低单体温度高于充电或放电加热膜关闭阈值，关闭加热膜
        if ((s_bHeatTurnOnPurpose == HEATER_TURNON_CHARGE && s_tHeaterRealData.fMinCellTemp > s_tSystemPara.fChgHeaterShutdownTemp) ||
            (s_bHeatTurnOnPurpose == HEATER_TURNON_DISCHARGE && s_tHeaterRealData.fMinCellTemp > s_tSystemPara.fDischgHeaterShutdownTemp))
        {
            return True;
        }
    }

    return False;
}


Static BOOLEAN ShouldTurnOnHeater(void)  //开启加热器判断
{
    if(GetApptestFlag())  //apptest不判断加热膜使能和温度条件，直接控制开启
    {
        return (s_bHeaterTestCtrl == HEATER_TEST_ON);
    }

    if(GetQtptestFlag()) {
        //qtp控制开启需系统有电且单体最高温度不大于45度,不判断加热膜使能
        if ((s_tHeaterRealData.bPowerOnFlag) && (s_tHeaterRealData.fMaxCellTemp <= 45.0f)) {
            return (s_bHeaterTestCtrl == HEATER_TEST_ON);
        }
        s_bHeaterTestCtrl = HEATER_TEST_NONE;
        return False;
    } else {
        if(!s_bHeaterCtrl)  //加热器使能关闭，不允许打开加热器
        {
            s_bHeaterTestCtrl = HEATER_TEST_NONE;
            return False;
        }
        if(s_bHeaterTestCtrl == HEATER_TEST_ON)  //pad协议主动控制加热器启动
        {
            return True;
        }
    }
    if (TurnOnHeaterCondition() == True)
    {
        s_ucTurnOntime++;
    }
    else
    {
        s_ucTurnOntime = 0;
    }
    if (s_ucTurnOntime >= 100)
    {
        s_ucTurnOntime = 0;
        return True;
    }
    else
    {
        return False;
    }
}

Static BOOLEAN ShouldTurnOffHeater(void)  //关闭加热器判断
{
    if(GetApptestFlag())  //apptest不判断加热膜使能和温度条件，直接控制关闭
    {
        return (s_bHeaterTestCtrl != HEATER_TEST_ON);
    }

    if(!s_bHeaterCtrl && !GetQtptestFlag())  //加热器使能关闭，关闭加热器   QTP模式下不判断加热使能
    {
        return True;
    }

    if(!s_tHeaterRealData.bPowerOnFlag) //系统停电关闭加热器
    {
        return True;
    }

    if (TurnOffHeaterCondition() == True)
    {
        s_ucTurnOfftime++;
    }
    else
    {
        s_ucTurnOfftime = 0;
    }

    if (s_ucTurnOfftime >= 100)
    {
        s_ucTurnOfftime = 0;
        return True;
    }

    if(s_bHeaterTestCtrl == HEATER_TEST_OFF)
    {
        s_bHeaterTestCtrl = HEATER_TEST_NONE;
        return True;
    }

    return False;
}

void SetHeaterTestCtrl(BYTE status)
{
    s_bHeaterTestCtrl = status;
    return;
}

BOOLEAN IsHeaterFilmConsist(void)  //判断加热器在位状态
{
    T_DCFactory tBduFact;

    rt_memset_s(&tBduFact, sizeof(T_DCFactory), 0, sizeof(T_DCFactory));
    GetBduFact(&tBduFact);

    return tBduFact.bHeatFilm;
}

//低温加热异常处理
Static BOOLEAN HeaterExceptionHanding()
{
    T_BCMAlarmStruct  tBCMAlm;
    BYTE i = 0;

    rt_memset_s(&tBCMAlm, sizeof(T_BCMAlarmStruct), 0, sizeof(T_BCMAlarmStruct));
    GetRealAlarm(BCM_ALARM_REAL, (BYTE *)&tBCMAlm);

    //如果电池出现了休眠，则先停止加热
    if(IsSleep())
    {
        return True;  
    }

    //当某个电芯温度无效时，停止加热
    for(i = 0; i < CELL_TEMP_NUM_MAX; i++)
    {
        if(tBCMAlm.ucCellTempSensorInvalidAlm[i])
        {
            return True;
        }
    }

#ifdef USING_HEAT_CONNECT
    if(tBCMAlm.ucHeatConnTempHighPrt)
    {
        return True;
    }
#endif

#ifdef ACTIVATE_PORT_ENABLED
    if(GetActivateErrorFlag())
    {
        return True;
    }
#endif

    return False;
}

T_HeaterStatus GetHeaterLogicStatus(void)
{
    return s_tHeaterRealData.tStatus;
}


BOOLEAN CtrlHeaterOnOff(T_HeaterStatus tStatus)
{
    BOOLEAN bHeaterCtrl = False;
    // 根据tStatus的值来决定是否打开加热器。
    if(tStatus == HEATER_ON)
    {
        TurnOnHeater();
        bHeaterCtrl = True;
    }
    else
    {
        TurnOffHeater();
        bHeaterCtrl = True;
    }

    return bHeaterCtrl;
}


BYTE checkHeaterRealState(void)
{
    BYTE bFlag = 0;
    bFlag |= s_ucHeaterRealStat;
    bFlag |= ShouldTurnOffHeater() << 1;
    bFlag |= ShouldTurnOnHeater() << 2;
    bFlag |= HeaterExceptionHanding() << 3;
    return bFlag;
}
