/*
 * @file    : control_manage.c
 * @brief   : 控制管理
 * @details : 
 * <AUTHOR> 邹绍云10326737
 * @Date    : 2023-02-08
 * @LastEditTime: 2023-02-08
 * @version : V0.0.1
 * @para    : Copyright (c) 
 *            ZTE Corporation
 * @par     : History
 *     version: author, date, descn
 */
#include <string.h>
#include <rtthread.h>
#include <stdlib.h>
#include "utils_thread.h"
#include "control_manage.h"
#include "msg.h"
#include "cmd.h"
#include "device_type.h"
#include "sps.h"
#include "msg_id.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "utils_heart_beat.h"
#include "thread_id.h"
#include "pin.h"
#include "pin_define.h"
#include "pin_ctrl.h"
#include "para_id_in.h"
#include "para_manage.h"
#include "para_common.h"


Static _rt_server_t g_ctrl_server = NULL;
static msg_map ctrl_msg_map[] =
{
    {CHECK_ALL_ALARM_TRIG_RELAY,      msg_handle_nothing},
};


/* DO控制处理 */
void ctrl_msg_process(void);

void* init_ctrl_manage(void * param)
{
    server_info_t *server_info = (server_info_t *)param;
    RETURN_VAL_IF_FAIL(server_info != NULL, NULL);
    server_info->server.server.map_size = sizeof(ctrl_msg_map) / sizeof(msg_map);
    register_server_msg_map(ctrl_msg_map, server_info);
    return server_info;
}


void ctrl_manage_main(void *param) {
    PRINT_MSG_AND_RETURN_IF_FAIL(param != NULL);
    g_ctrl_server = _curr_server_get();
    pre_thread_beat_f(THREAD_CTRL);

    while (is_running(TRUE)) {
        thread_beat_go_on(THREAD_CTRL);
        ctrl_msg_process();
        in_relay_deal();
        rt_thread_mdelay(20);
    }
}

int set_out_relay_status(do_msg_t* do_msg)
{
    if (do_msg == NULL) 
    {
        return FAILURE;
    }

    // 第一个是输出干接点
    if(do_msg->do_no == OUT_RELAY_NO) 
    {
        return set_one_data(DAC_DATA_ID_OUT_RELAY_STATUS, &do_msg->active);
    }

    return SUCCESSFUL;
}


int alarm_trig_relay(do_msg_t* do_msg)
{
    if (do_msg == NULL) 
    {
        return FAILURE;
    }
    for(int loop = 0; loop < DO_MAX_NUM; loop ++)
    {
        if(do_msg[loop].do_no == 0)
        {
            break;
        }
        set_out_relay_status(do_msg + loop);
    }
    return SUCCESSFUL;
}

void ctrl_msg_process(void) {
    
    if ((g_ctrl_server == NULL) || (rt_sem_take(&g_ctrl_server->msg_sem, 0) != RT_EOK) || (g_ctrl_server->msg_node == RT_NULL))
    {
        return;
    }
    _rt_msg_t curr_msg = g_ctrl_server->msg_node;
    switch (curr_msg->msg.msg_id)
    {   
        case CHECK_ALL_ALARM_TRIG_RELAY:
            alarm_trig_relay((do_msg_t*)curr_msg->msg.data);
            break;
        
        default:
            break;      
    }

    rt_mutex_take(&g_ctrl_server->mutex, RT_WAITING_FOREVER);
    g_ctrl_server->msg_node = curr_msg->next;
    g_ctrl_server->msg_count--;
    rt_mutex_release(&g_ctrl_server->mutex);

    softbus_free(curr_msg);

}


int in_relay_deal(void)
{
    int count = 0;
    unsigned char relay_status = FALSE;
    static unsigned char last_relay_status = FALSE;
    unsigned short shutdown = 0;
    power_off_reason_t* power_off_reason = get_power_off_reason();


    if(get_pin(IN5_RELAY_PIN) == PIN_LOW)
    {
        relay_status = TRUE;
    }

    if((relay_status == TRUE) && (last_relay_status == FALSE))
    {   
        // 从不停机到紧急停机
        set_one_para(DAC_PARA_ID_POWER_ON_OFF_OFFSET, &shutdown, TRUE, TRUE);
        set_one_data(DAC_DATA_ID_RELAY_STATUS + 4, &relay_status);

        for(count = 0; count < 3; count++)
        {
            send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, DC_AC_SET_REMOTE_CTRL_PARA_DATA);
            send_msg_to_thread(GRID_CODE_STATUS_CHECK, MOD_SYS_MANAGE, NULL, 0);
        }
        power_off_reason->emergy_off = TRUE;
        LOG_E("in_relay_deal | emergency stop");
    }

    if(relay_status != last_relay_status)
    {
        last_relay_status = relay_status;
        set_one_data(DAC_DATA_ID_RELAY_STATUS + 4, &relay_status);
    }
    
    return relay_status;
}


