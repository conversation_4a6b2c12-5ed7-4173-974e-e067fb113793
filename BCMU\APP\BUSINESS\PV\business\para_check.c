#include <rtthread.h>
#include "para_check.h"
#include "utils_thread.h"
#include "device_type.h"
#include "para_id_in.h"
#include "cmd.h"
#include "sps.h"
#include "msg.h"
#include "msg_id.h"
#include "utils_time.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "para_manage.h"
#include "type_define_in.h"
#include "his_data.h"
#include "utils_data_transmission.h"
#include "utils_rtthread_security_func.h"

static rt_timer_t g_para_check_timer = NULL; 
static unsigned int g_real_sids[SID_MAX_NUM] = {0}; 
static unsigned short g_para_sids[SID_MAX_NUM] = {0};
static unsigned int g_count_time = 0, g_count_print = 0;


static real_sid_t ctrl_switch_real[] = 
{
    {DAC_DATA_ID_POWER_ON_OFF, 1, <PERSON>IM_TYPE},
    {DAC_DATA_ID_PHASE_SEQ_ADAPTIVE_ENABLE, 1, DIM_TYPE},
    {DAC_DATA_ID_MPPT_CONNECT_TYPE, 1, DIM_TYPE},
    {DAC_DATA_ID_EN_LEAK_CURR_SELF_CHECK, 1, DIM_TYPE},
    {DAC_DATA_ID_RCD_BOOST_ENABLE, 1, DIM_TYPE},
    {DAC_DATA_ID_WEAK_GRID_ENABLE, 1, DIM_TYPE},
    {DAC_DATA_ID_WATER_INGRESS_ENABLE, 1, DIM_TYPE},
};

static para_sid_tab_t ctrl_switch_para[] = 
{
    {DAC_PARA_ID_POWER_ON_OFF_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_PHASE_SEQ_ADAPTIVE_ENABLE_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_MPPT_CONNECT_TYPE_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_EN_LEAK_CURR_SELF_CHECK_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_RCD_BOOST_ENABLE_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_WEAK_GRID_ENABLE_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_WATER_INGRESS_ENABLE_OFFSET, 1, DIM_TYPE},
};

static real_sid_t power_grid_real[] = 
{
    {DAC_DATA_ID_GRID_STANDARD_CODE, 1, DIM_TYPE},
    {DAC_DATA_ID_GRID_ISOLATION_SETTING, 1, DIM_TYPE},
    {DAC_DATA_ID_OUTPUT_MODE, 1, DIM_TYPE},
    {DAC_DATA_ID_ATUO_START_AFTER_GRID_FAULT_RECOVER, 1, DIM_TYPE},
    {DAC_DATA_ID_CONNECT_TIME_AFTER_GRID_FAULT_RECOVER, 1, DIM_TYPE},
    {DAC_DATA_ID_DELAY_START_TIME_AFTER_GRID_FAULT, 1, DIM_TYPE},
    {DAC_DATA_ID_UPPER_LIMIT_GRID_RECONN_VOLT, 1, DIM_TYPE},
    {DAC_DATA_ID_LOWER_LIMIT_GRID_RECONN_VOLT, 1, DIM_TYPE},
    {DAC_DATA_ID_UPPER_LIMIT_GRID_RECONN_FREQ, 1, DIM_TYPE},
    {DAC_DATA_ID_LOWER_LIMIT_GRID_RECONN_FREQ, 1, DIM_TYPE},
    {DAC_DATA_ID_PQ_MODE, 1, DIM_TYPE},
    {DAC_DATA_ID_LOWER_LIMIT_GRID_CONN_START_VOLT, 1, DIM_TYPE},
    {DAC_DATA_ID_UPPER_LIMIT_GRID_CONN_START_FREQ, 1, DIM_TYPE},
    {DAC_DATA_ID_LOWER_LIMIT_GRID_CONN_START_FREQ, 1, DIM_TYPE},
    {DAC_DATA_ID_RATED_GRID_VOLT, 1, DIM_TYPE},
};

static para_sid_tab_t power_grid_para[] = 
{
    {DAC_PARA_ID_GRID_STANDARD_CODE_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_GRID_ISOLATION_SETTING_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_OUTPUT_MODE_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_ATUO_START_AFTER_GRID_FAULT_RECOVER_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_CONNECT_TIME_AFTER_GRID_FAULT_RECOVER_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_DELAY_START_TIME_AFTER_GRID_FAULT_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_UPPER_LIMIT_GRID_RECONN_VOLT_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_LOWER_LIMIT_GRID_RECONN_VOLT_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_UPPER_LIMIT_GRID_RECONN_FREQ_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_LOWER_LIMIT_GRID_RECONN_FREQ_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_PQ_MODE_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_LOWER_LIMIT_GRID_CONN_START_VOLT_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_UPPER_LIMIT_GRID_CONN_START_FREQ_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_LOWER_LIMIT_GRID_CONN_START_FREQ_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_RATED_GRID_VOLT_OFFSET, 1, DIM_TYPE},
};

static real_sid_t chara_real[] = 
{
    {DAC_DATA_ID_STANDBY_TIME, 1, DIM_TYPE},
    {DAC_DATA_ID_BOOT_SOFT_START_TIME, 1, DIM_TYPE},
    {DAC_DATA_ID_ISLAND_DETECT_ENABLE, 1, DIM_TYPE},
    {DAC_DATA_ID_ENABLE_GRID_VOLT_PROT_SHEILD_DURING_VRT, 1, DIM_TYPE},
    {DAC_DATA_ID_VRT_EXIT_HYSTERESIS_THRESHLOD, 1, DIM_TYPE},
    {DAC_DATA_ID_GRID_FAULT_ZERO_CURR_MODE, 1, DIM_TYPE},
};

static para_sid_tab_t chara_para[] = 
{
    {DAC_PARA_ID_STANDBY_TIME_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_BOOT_SOFT_START_TIME_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_ISLAND_DETECT_ENABLE_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_ENABLE_GRID_VOLT_PROT_SHEILD_DURING_VRT_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_VRT_EXIT_HYSTERESIS_THRESHLOD_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_GRID_FAULT_ZERO_CURR_MODE_OFFSET, 1, DIM_TYPE},

};

static real_sid_t mppt_real[] = 
{
    {DAC_DATA_ID_MPPT_ENABLE, 1, DIM_TYPE},
    {DAC_DATA_ID_MPPT_SCAN_INTERVAL, 1, DIM_TYPE},
};

static para_sid_tab_t mppt_para[] = 
{
    {DAC_PARA_ID_MPPT_ENABLE_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_MPPT_SCAN_INTERVAL_OFFSET, 1, DIM_TYPE},
};

static real_sid_t lvrt_real[] = 
{
    {DAC_DATA_ID_LVRT_ENABLE, 1, DIM_TYPE},
    {DAC_DATA_ID_LVRT_TRIGG_THRESHOLD, 1, DIM_TYPE},
    {DAC_DATA_ID_LVRT_POS_SEQ_REACTIVE_POWER_COMPEN_FACTOR, 1, DIM_TYPE},
    {DAC_DATA_ID_LVRT_NEG_SEQ_REACTIVE_POWER_COMPEN_FACTOR, 1, DIM_TYPE},
    {DAC_DATA_ID_LVRT_REACTIVE_CURR_LIMIT_PERCENT, 1, DIM_TYPE},
    {DAC_DATA_ID_LVRT_ZERO_CURR_MODE_THRESHOLD, 1, DIM_TYPE},
    {DAC_DATA_ID_LVRT_MODE, 1, DIM_TYPE},
    {DAC_DATA_ID_LVRT_ACTIVE_CURR_MAINTEN_COEFFICIENT, 1, DIM_TYPE},
};

static para_sid_tab_t lvrt_para[] = 
{
   {DAC_PARA_ID_LVRT_ENABLE_OFFSET, 1, DIM_TYPE},
   {DAC_PARA_ID_LVRT_TRIGG_THRESHOLD_OFFSET, 1, DIM_TYPE},
   {DAC_PARA_ID_LVRT_POS_SEQ_REACTIVE_POWER_COMPEN_FACTOR_OFFSET, 1, DIM_TYPE},
   {DAC_PARA_ID_LVRT_NEG_SEQ_REACTIVE_POWER_COMPEN_FACTOR_OFFSET, 1, DIM_TYPE},
   {DAC_PARA_ID_LVRT_REACTIVE_CURR_LIMIT_PERCENT_OFFSET, 1, DIM_TYPE},
   {DAC_PARA_ID_LVRT_ZERO_CURR_MODE_THRESHOLD_OFFSET, 1, DIM_TYPE},
   {DAC_PARA_ID_LVRT_MODE_OFFSET, 1, DIM_TYPE},
   {DAC_PARA_ID_LVRT_ACTIVE_CURR_MAINTEN_COEFFICIENT_OFFSET, 1, DIM_TYPE},
};

static real_sid_t lvrt_cc_real[] = 
{
    {DAC_DATA_ID_CHARACTER_CURVE_POINT, 1, DIM_TYPE},
    {DAC_DATA_ID_CHARACTER_CURVE_TIME, DAC_DATA_ID_CHARACTER_CURVE_POINT, DIM_SID_TYPE},
    {DAC_DATA_ID_VOLT_PERCENT,DAC_DATA_ID_CHARACTER_CURVE_POINT, DIM_SID_TYPE},
};

static para_sid_tab_t lvrt_cc_para[] = 
{
    {DAC_PARA_ID_CHARACTER_CURVE_POINT_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_CHARACTER_CURVE_TIME_OFFSET, DAC_PARA_ID_CHARACTER_CURVE_POINT_OFFSET, DIM_SID_TYPE},
    {DAC_PARA_ID_VOLT_PERCENT_OFFSET, DAC_PARA_ID_CHARACTER_CURVE_POINT_OFFSET, DIM_SID_TYPE},
};

static real_sid_t check_hvrt_real[] = 
{
    {DAC_DATA_ID_HVRT_ENABLE, 1, DIM_TYPE},
    {DAC_DATA_ID_HVRT_TRIGG_THRESHOLD, 1, DIM_TYPE},
    {DAC_DATA_ID_HVRT_POS_SEQ_REACTIVE_POWER_COMPEN_FACTOR, 1, DIM_TYPE},
    {DAC_DATA_ID_HVRT_NEG_SEQ_REACTIVE_POWER_COMPEN_FACTOR, 1, DIM_TYPE},

};

static para_sid_tab_t check_hvrt_para[] = 
{
    {DAC_PARA_ID_HVRT_ENABLE_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_HVRT_TRIGG_THRESHOLD_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_HVRT_POS_SEQ_REACTIVE_POWER_COMPEN_FACTOR_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_HVRT_NEG_SEQ_REACTIVE_POWER_COMPEN_FACTOR_OFFSET, 1, DIM_TYPE},
};

static real_sid_t hvrt_cc_real[] = 
{
    {DAC_DATA_ID_HVRT_CHARACTER_CURVE_POINT, 1, DIM_TYPE},
    {DAC_DATA_ID_HVRT_CHARACTER_CURVE_TIME, DAC_DATA_ID_HVRT_CHARACTER_CURVE_POINT, DIM_SID_TYPE},
    {DAC_DATA_ID_HVRT_CHARACTER_CURVE_VOLT_PER,DAC_DATA_ID_HVRT_CHARACTER_CURVE_POINT, DIM_SID_TYPE},
};

static para_sid_tab_t hvrt_cc_para[] = 
{
    {DAC_PARA_ID_HVRT_CHARACTER_CURVE_POINT_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_HVRT_CHARACTER_CURVE_TIME_OFFSET, DAC_PARA_ID_HVRT_CHARACTER_CURVE_POINT_OFFSET, DIM_SID_TYPE},
    {DAC_PARA_ID_HVRT_CHARACTER_CURVE_VOLT_PER_OFFSET, DAC_PARA_ID_HVRT_CHARACTER_CURVE_POINT_OFFSET, DIM_SID_TYPE},
};

static real_sid_t pid_real[] = 
{
    {DAC_DATA_ID_PID_REPAIR_ENABLE, 1, DIM_TYPE},
    {DAC_DATA_ID_NIGHT_PID_PROT, 1, DIM_TYPE},
    {DAC_DATA_ID_NIGHT_SLEEP_ENABLE, 1, DIM_TYPE},
    {DAC_DATA_ID_NIGHT_SLEEP_WAIT_TIME, 1, DIM_TYPE},
};

static para_sid_tab_t pid_para[] = 
{
    {DAC_PARA_ID_PID_REPAIR_ENABLE_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_NIGHT_PID_PROT_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_NIGHT_SLEEP_ENABLE_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_NIGHT_SLEEP_WAIT_TIME_OFFSET, 1, DIM_TYPE},
};

static real_sid_t afci_real[] = 
{
    {DAC_DATA_ID_AFCI_DETECT_ON_OFF, 1, DIM_TYPE},
    {DAC_DATA_ID_AFCI_CHECK_DETECTION_SENSITIVITY, 1, DIM_TYPE},
    {DAC_DATA_ID_AFCI_SAVE_ENABLE, 1, DIM_TYPE},
};

static para_sid_tab_t afci_para[] = 
{
    {DAC_PARA_ID_AFCI_DETECT_ON_OFF_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_AFCI_CHECK_DETECTION_SENSITIVITY_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_AFCI_SAVE_ENABLE_OFFSET, 1, DIM_TYPE},
};


static real_sid_t power_regulation_real[] = 
{
    {DAC_DATA_ID_REMOTE_POWER_DISPATCH_ENABLE_DISABLE, 1, DIM_TYPE},
    {DAC_DATA_ID_SHCED_INSTRUCT_MAINTEN_TIME, 1, DIM_TYPE},
    {DAC_DATA_ID_ACTIVE_POWER_REFERENCE, 1, DIM_TYPE},
    {DAC_DATA_ID_APPARNET_POWER_REFERENCE, 1, DIM_TYPE},
    {DAC_DATA_ID_MAX_APPARENT_POWER_PARAM, 1, DIM_TYPE},
    {DAC_DATA_ID_MAX_ACTIVE_POWER_PARAM, 1, DIM_TYPE},
};

static para_sid_tab_t power_regulation_para[] = 
{
    {DAC_PARA_ID_REMOTE_POWER_DISPATCH_ENABLE_DISABLE_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_SHCED_INSTRUCT_MAINTEN_TIME_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_ACTIVE_POWER_REFERENCE_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_APPARNET_POWER_REFERENCE_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_MAX_APPARENT_POWER_PARAM_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_MAX_ACTIVE_POWER_PARAM_OFFSET, 1, DIM_TYPE},
};

static real_sid_t act_regulation_real[] = 
{
    {DAC_DATA_ID_GRID_ACTIVE_POWER_CHANGE, 1, DIM_TYPE},
    {DAC_DATA_ID_ACTIVE_POWER_CONTROL_MODE, 1, DIM_TYPE},
    {DAC_DATA_ID_ACTIVE_POWER_DERATING_SETTING, 1, DIM_TYPE},
    {DAC_DATA_ID_ACTIVE_POWER_DERATING_PERCENT, 1, DIM_TYPE},
};

static para_sid_tab_t act_regulation_para[] = 
{
    {DAC_PARA_ID_GRID_ACTIVE_POWER_CHANGE_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_ACTIVE_POWER_CONTROL_MODE_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_ACTIVE_POWER_DERATING_SETTING_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_ACTIVE_POWER_DERATING_PERCENT_OFFSET, 1, DIM_TYPE},
};


static real_sid_t react_regulation_real[] = 
{
    {DAC_DATA_ID_GRID_REACTIVE_POWER_CHANGE, 1, DIM_TYPE},
    {DAC_DATA_ID_REACTIVE_POWER_COMPEN_MODE, 1, DIM_TYPE},
    {DAC_DATA_ID_REACTIVE_POWER_COMPEN_SETTING_FACTOR, 1, DIM_TYPE},
    {DAC_DATA_ID_REACTIVE_POWER_COMPEN_SETTING, 1, DIM_TYPE},
    {DAC_DATA_ID_REACTIVE_POWER_REGULATIONG_TIME, 1, DIM_TYPE},

    {DAC_DATA_ID_REACTIVE_POWER_VALUE, 1, DIM_TYPE},
    {DAC_DATA_ID_REACTIVE_POWER_PCT, 1, DIM_TYPE},

};

static para_sid_tab_t react_regulation_para[] = 
{
    {DAC_PARA_ID_GRID_REACTIVE_POWER_CHANGE_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_REACTIVE_POWER_COMPEN_MODE_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_REACTIVE_POWER_COMPEN_SETTING_FACTOR_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_REACTIVE_POWER_COMPEN_SETTING_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_REACTIVE_POWER_REGULATIONG_TIME_OFFSET, 1, DIM_TYPE},

    {DAC_PARA_ID_REACTIVE_POWER_VALUE_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_REACTIVE_POWER_PCT_OFFSET, 1, DIM_TYPE},

};


static real_sid_t p_pn_cc_real[] = 
{
    {DAC_DATA_ID_QP_MODE_TRIGG_VOLT, 1, DIM_TYPE},
    {DAC_DATA_ID_QP_MODE_EXIT_VOLT, 1, DIM_TYPE},
    {DAC_DATA_ID_SETTING_POINT_OF_COS_CURVE, 1, DIM_TYPE},
    {DAC_DATA_ID_P_PN_AXIS, DAC_DATA_ID_SETTING_POINT_OF_COS_CURVE, DIM_SID_TYPE},
    {DAC_DATA_ID_COS_AXIS, DAC_DATA_ID_SETTING_POINT_OF_COS_CURVE, DIM_SID_TYPE},
};

static para_sid_tab_t p_pn_cc_para[] = 
{
    {DAC_PARA_ID_QP_MODE_TRIGG_VOLT_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_QP_MODE_EXIT_VOLT_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_SETTING_POINT_OF_COS_CURVE_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_P_PN_AXIS_OFFSET, DAC_PARA_ID_SETTING_POINT_OF_COS_CURVE_OFFSET, DIM_SID_TYPE},
    {DAC_PARA_ID_COS_AXIS_OFFSET, DAC_PARA_ID_SETTING_POINT_OF_COS_CURVE_OFFSET, DIM_SID_TYPE},
};


static real_sid_t q_u_cc_real[] = 
{
    {DAC_DATA_ID_QU_SCHEDU_TRIGG_POWER_PERCENT, 1, DIM_TYPE},
    {DAC_DATA_ID_QU_SCHEDU_EXIT_POWER_PERCENT, 1, DIM_TYPE},
    {DAC_DATA_ID_QU_CHARACTER_CURVE_MIN_PF_LIMIT, 1, DIM_TYPE},
    {DAC_DATA_ID_HYSTERESIS_RATIO, 1, DIM_TYPE},
    {DAC_DATA_ID_NUMBER_OF_POINT_FOR_QU_CURVE, 1, DIM_TYPE},
    {DAC_DATA_ID_U_UN_AXIS, DAC_DATA_ID_NUMBER_OF_POINT_FOR_QU_CURVE, DIM_SID_TYPE},
    {DAC_DATA_ID_QS_AXIS, DAC_DATA_ID_NUMBER_OF_POINT_FOR_QU_CURVE, DIM_SID_TYPE},
};

static para_sid_tab_t q_u_cc_para[] = 
{
    {DAC_PARA_ID_QU_SCHEDU_TRIGG_POWER_PERCENT_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_QU_SCHEDU_EXIT_POWER_PERCENT_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_QU_CHARACTER_CURVE_MIN_PF_LIMIT_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_HYSTERESIS_RATIO_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_NUMBER_OF_POINT_FOR_QU_CURVE_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_U_UN_AXIS_OFFSET, DAC_PARA_ID_NUMBER_OF_POINT_FOR_QU_CURVE_OFFSET, DIM_SID_TYPE},
    {DAC_PARA_ID_QS_AXIS_OFFSET, DAC_PARA_ID_NUMBER_OF_POINT_FOR_QU_CURVE_OFFSET, DIM_SID_TYPE},
};

static real_sid_t pf_u_cc_real[] = 
{
    {DAC_DATA_ID_PF_VOLT_DETECT_FILTER_TIME, 1, DIM_TYPE},
    {DAC_DATA_ID_NUMBER_OF_POINT_FOR_PF_CURVE, 1, DIM_TYPE},
    {DAC_DATA_ID_PF_U_AXIS, DAC_DATA_ID_NUMBER_OF_POINT_FOR_PF_CURVE, DIM_SID_TYPE},
    {DAC_DATA_ID_PF_AXIS, DAC_DATA_ID_NUMBER_OF_POINT_FOR_PF_CURVE, DIM_SID_TYPE},
};

static para_sid_tab_t pf_u_cc_para[] = 
{
    {DAC_PARA_ID_PF_VOLT_DETECT_FILTER_TIME_OFFSET,  1, DIM_TYPE},
    {DAC_PARA_ID_NUMBER_OF_POINT_FOR_PF_CURVE_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_PF_U_AXIS_OFFSET, DAC_PARA_ID_NUMBER_OF_POINT_FOR_PF_CURVE_OFFSET, DIM_SID_TYPE},
    {DAC_PARA_ID_PF_AXIS_OFFSET, DAC_PARA_ID_NUMBER_OF_POINT_FOR_PF_CURVE_OFFSET, DIM_SID_TYPE},
};

static real_sid_t protect_real[] = 
{
    {DAC_DATA_ID_GRID_VOLT_IMBALANCE_PROT_POINT, 1, DIM_TYPE},
    {DAC_DATA_ID_INSULATION_IMPEDANCE_PROT, 1, DIM_TYPE},
};

static para_sid_tab_t protect_para[] = 
{
    {DAC_PARA_ID_GRID_VOLT_IMBALANCE_PROT_POINT_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_INSULATION_IMPEDANCE_PROT_OFFSET, 1, DIM_TYPE},
};

static real_sid_t protect_lx_real[] = 
{
    {DAC_DATA_ID_OVER_VOLT_PROT_POINT_GRID, 1, DIM_TYPE},
    {DAC_DATA_ID_OVER_VOLT_PROT_TIME_GRID, 1, DIM_TYPE},
    {DAC_DATA_ID_UNDER_VOLT_PROT_POINT_GRID, 1, DIM_TYPE},
    {DAC_DATA_ID_UNDER_VOLT_PROT_TIME_GRID, 1, DIM_TYPE},
    {DAC_DATA_ID_OVER_FREQ_PROT_POINT, 1, DIM_TYPE},
    {DAC_DATA_ID_OVER_FREQ_PROT_TIME, 1, DIM_TYPE},
    {DAC_DATA_ID_UNDER_FREQ_PROT_POINT, 1, DIM_TYPE},
    {DAC_DATA_ID_UNDER_FREQ_PROT_TIME, 1, DIM_TYPE},
};

static para_sid_tab_t protect_lx_para[] = 
{
    {DAC_PARA_ID_OVER_VOLT_PROT_POINT_GRID_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_OVER_VOLT_PROT_TIME_GRID_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_UNDER_VOLT_PROT_POINT_GRID_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_UNDER_VOLT_PROT_TIME_GRID_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_OVER_FREQ_PROT_POINT_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_OVER_FREQ_PROT_TIME_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_UNDER_FREQ_PROT_POINT_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_UNDER_FREQ_PROT_TIME_OFFSET, 1, DIM_TYPE},
};

static real_sid_t other_real[] = 
{
    {DAC_DATA_ID_ALTITUDE, 1, DIM_TYPE},
};

static para_sid_tab_t other_para[] = 
{
    {DAC_PARA_ID_ALTITUDE_OFFSET, 1, DIM_TYPE},
};

static real_sid_t string_in_detection_real[] = 
{
    {DAC_DATA_ID_GROUP_STRING_LOSS_DETECTION, 1, DIM_TYPE},
    {DAC_DATA_ID_STARTING_CURRENT, 1, DIM_TYPE},
    {DAC_DATA_ID_TWO_IN_ONE_STARTING_CURRENT, 1, DIM_TYPE},
    {DAC_DATA_ID_GROUP_STRING_ACCESS_TYPE, 1, DIM_TYPE},
    {DAC_DATA_ID_GROUP_STRING_ACCESS_TYPE + 1, 1, DIM_TYPE},
    {DAC_DATA_ID_GROUP_STRING_ACCESS_TYPE + 2, 1, DIM_TYPE},
    {DAC_DATA_ID_GROUP_STRING_ACCESS_TYPE + 3, 1, DIM_TYPE},
    {DAC_DATA_ID_GROUP_STRING_ACCESS_TYPE + 4, 1, DIM_TYPE},
    {DAC_DATA_ID_GROUP_STRING_ACCESS_TYPE + 5, 1, DIM_TYPE},
    {DAC_DATA_ID_GROUP_STRING_ACCESS_TYPE + 6, 1, DIM_TYPE},
    {DAC_DATA_ID_GROUP_STRING_ACCESS_TYPE + 7, 1, DIM_TYPE},
};

static para_sid_tab_t string_in_detection_para[] = 
{
    {DAC_PARA_ID_GROUP_STRING_LOSS_DETECTION_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_STARTING_CURRENT_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_TWO_IN_ONE_STARTING_CURRENT_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 1, 1, DIM_TYPE},
    {DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 2, 1, DIM_TYPE},
    {DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 3, 1, DIM_TYPE},
    {DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 4, 1, DIM_TYPE},
    {DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 5, 1, DIM_TYPE},
    {DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 6, 1, DIM_TYPE},
    {DAC_PARA_ID_GROUP_STRING_ACCESS_TYPE_OFFSET + 7, 1, DIM_TYPE},
};

static real_sid_t over_under_freq_derate_real[] = 
{
    {DAC_DATA_ID_OVERFREQUENCY_DERATING_ENABLE, 1, DIM_TYPE},
    {DAC_DATA_ID_OVERFREQUENCY_DERATING_TRIG_FREQUENCY, 1, DIM_TYPE},
    {DAC_DATA_ID_OVERFREQUENCY_DERATING_EXIT_FREQUENCY, 1, DIM_TYPE},
    {DAC_DATA_ID_OVERFREQUENCY_DERATING_CUTOFF_FREQUENCY, 1, DIM_TYPE},
    {DAC_DATA_ID_OVERFREQUENCY_DERATING_RATED_POWER, 1, DIM_TYPE},
    {DAC_DATA_ID_OVERFREQUENCY_DERATING_RECOVERY_GRADIENT, 1, DIM_TYPE},
    {DAC_DATA_ID_FREQUENCY_CONTROL_FILTERING_TIME, 1, DIM_TYPE},
    {DAC_DATA_ID_OVER_FREQUENCY_ACTIVE_DERATING_RECOVERY_DELAY_TIME, 1, DIM_TYPE},
    {DAC_DATA_ID_OVER_FREQUENCY_ACTIVE_DERATING_EFFECTIVE_TIME, 1, DIM_TYPE},
    {DAC_DATA_ID_UNDER_FREQUENCY_BOOST_POWER_ENABLE, 1, DIM_TYPE},
    {DAC_DATA_ID_UNDER_FREQUENCY_BOOST_POWER_TRIG_FREQUENCY, 1, DIM_TYPE},
    {DAC_DATA_ID_UNDER_FREQUENCY_BOOST_POWER_EXIT_FREQUENCY, 1, DIM_TYPE},
    {DAC_DATA_ID_UNDER_FREQUENCY_BOOST_POWER_CUTOFF_FREQUENCY, 1, DIM_TYPE},
    {DAC_DATA_ID_UNDER_FREQUENCY_BOOST_POWER_CUTOFF_POWER, 1, DIM_TYPE},
    {DAC_DATA_ID_UNDER_FREQUENCY_BOOST_POWER_RECOVERY_GRADIENT, 1, DIM_TYPE},
    {DAC_DATA_ID_UNDER_FREQUENCY_ACTIVE_DERATING_RECOVERY_DELAY_TIME, 1, DIM_TYPE},
    {DAC_DATA_ID_UNDER_FREQUENCY_ACTIVE_DERATING_EFFECTIVE_TIME, 1, DIM_TYPE},
};

static para_sid_tab_t over_under_freq_derate_para[] = 
{
    {DAC_PARA_ID_OVERFREQUENCY_DERATING_ENABLE_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_OVERFREQUENCY_DERATING_TRIG_FREQUENCY_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_OVERFREQUENCY_DERATING_EXIT_FREQUENCY_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_OVERFREQUENCY_DERATING_CUTOFF_FREQUENCY_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_OVERFREQUENCY_DERATING_RATED_POWER_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_OVERFREQUENCY_DERATING_RECOVERY_GRADIENT_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_FREQUENCY_CONTROL_FILTERING_TIME_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_OVER_FREQUENCY_ACTIVE_DERATING_RECOVERY_DELAY_TIME_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_OVER_FREQUENCY_ACTIVE_DERATING_EFFECTIVE_TIME_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_ENABLE_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_TRIG_FREQUENCY_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_EXIT_FREQUENCY_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_CUTOFF_FREQUENCY_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_CUTOFF_POWER_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_UNDER_FREQUENCY_BOOST_POWER_RECOVERY_GRADIENT_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_UNDER_FREQUENCY_ACTIVE_DERATING_RECOVERY_DELAY_TIME_OFFSET, 1, DIM_TYPE},
    {DAC_PARA_ID_UNDER_FREQUENCY_ACTIVE_DERATING_EFFECTIVE_TIME_OFFSET, 1, DIM_TYPE},
};


static para_cmp_t para_cmp_tab[] = 
{
    {ctrl_switch_para,             ctrl_switch_real,             sizeof(ctrl_switch_para)/sizeof(para_sid_tab_t),           sizeof(ctrl_switch_real)/sizeof(real_sid_t),            1, DIM_TYPE, DC_AC_SET_REMOTE_CTRL_PARA_DATA},
    {power_grid_para,              power_grid_real,              sizeof(power_grid_para)/sizeof(para_sid_tab_t),            sizeof(power_grid_real)/sizeof(real_sid_t),             1, DIM_TYPE, DC_AC_SET_POWER_GRID_PARA_DATA},
    {chara_para,                   chara_real,                   sizeof(chara_para)/sizeof(para_sid_tab_t),                 sizeof(chara_real)/sizeof(real_sid_t),                  1, DIM_TYPE, DC_AC_SET_CHARA_PARA_DATA},
    {mppt_para,                    mppt_real,                    sizeof(mppt_para)/sizeof(para_sid_tab_t),                  sizeof(mppt_real)/sizeof(real_sid_t),                   1, DIM_TYPE, DC_AC_SET_MPPT_PARA_DATA},
    {lvrt_para,                    lvrt_real,                    sizeof(lvrt_para)/sizeof(para_sid_tab_t),                  sizeof(lvrt_real)/sizeof(real_sid_t),                   1, DIM_TYPE, DC_AC_SET_LVRT_PARA_DATA},
    {lvrt_cc_para,                 lvrt_cc_real,                 sizeof(lvrt_cc_para)/sizeof(para_sid_tab_t),               sizeof(lvrt_cc_real)/sizeof(real_sid_t),                1, DIM_TYPE, DC_AC_SET_LVRT_CC_PARA_DATA},
    {check_hvrt_para,              check_hvrt_real,              sizeof(check_hvrt_para)/sizeof(para_sid_tab_t),            sizeof(check_hvrt_real)/sizeof(real_sid_t),             1, DIM_TYPE, DC_AC_SET_HVRT_PARA_DATA},
    {hvrt_cc_para,                 hvrt_cc_real,                 sizeof(hvrt_cc_para)/sizeof(para_sid_tab_t),               sizeof(hvrt_cc_real)/sizeof(real_sid_t),                1, DIM_TYPE, DC_AC_SET_HVRT_CC_PARA_DATA},
    {pid_para,                     pid_real,                     sizeof(pid_para)/sizeof(para_sid_tab_t),                   sizeof(pid_real)/sizeof(real_sid_t),                    1, DIM_TYPE, DC_AC_SET_PID_PARA_DATA},
    {afci_para,                    afci_real,                    sizeof(afci_para)/sizeof(para_sid_tab_t),                  sizeof(afci_real)/sizeof(real_sid_t),                   1, DIM_TYPE, DC_AC_SET_AFCI_PARA_DATA},
    {power_regulation_para,        power_regulation_real,        sizeof(power_regulation_para)/sizeof(para_sid_tab_t),      sizeof(power_regulation_real)/sizeof(real_sid_t),       1, DIM_TYPE, DC_AC_SET_POWER_REGULATION_PARA_DATA},
    {act_regulation_para,          act_regulation_real,          sizeof(act_regulation_para)/sizeof(para_sid_tab_t),        sizeof(act_regulation_real)/sizeof(real_sid_t),         1, DIM_TYPE, DC_AC_SET_ACT_REGULATION_PARA_DATA},
    {react_regulation_para,        react_regulation_real,        sizeof(react_regulation_para)/sizeof(para_sid_tab_t),      sizeof(react_regulation_real)/sizeof(real_sid_t),       1, DIM_TYPE, DC_AC_SET_REACT_REGULATION_PARA_DATA},
    {p_pn_cc_para,                 p_pn_cc_real,                 sizeof(p_pn_cc_para)/sizeof(para_sid_tab_t),               sizeof(p_pn_cc_real)/sizeof(real_sid_t),                1, DIM_TYPE, DC_AC_SET_P_PN_CC_PARA_DATA},
    {q_u_cc_para,                  q_u_cc_real,                  sizeof(q_u_cc_para)/sizeof(para_sid_tab_t),                sizeof(q_u_cc_real)/sizeof(real_sid_t),                 1, DIM_TYPE, DC_AC_SET_Q_U_CC_PARA_DATA},
    {pf_u_cc_para,                 pf_u_cc_real,                 sizeof(pf_u_cc_para)/sizeof(para_sid_tab_t),               sizeof(pf_u_cc_real)/sizeof(real_sid_t),                1, DIM_TYPE, DC_AC_SET_PF_U_CC_PARA_DATA},
    {protect_para,                 protect_real,                 sizeof(protect_para)/sizeof(para_sid_tab_t),               sizeof(protect_real)/sizeof(real_sid_t),                1, DIM_TYPE, DC_AC_SET_PROTECT_PARA_DATA},
    {protect_lx_para,              protect_lx_real,              sizeof(protect_lx_para)/sizeof(para_sid_tab_t),            sizeof(protect_lx_real)/sizeof(real_sid_t),             2, DIM_TYPE, DC_AC_SET_PROTECT_L1_PARA_DATA},
    {other_para,                   other_real,                   sizeof(other_para)/sizeof(para_sid_tab_t),                 sizeof(other_real)/sizeof(real_sid_t),                  1, DIM_TYPE, DC_AC_SET_OTHER_PPARA_DATA},
    {string_in_detection_para,     string_in_detection_real,     sizeof(string_in_detection_para)/sizeof(para_sid_tab_t),   sizeof(string_in_detection_real)/sizeof(real_sid_t),    1, DIM_TYPE, DC_AC_SET_STRING_IN_DETECTION_DATA},
    {over_under_freq_derate_para,  over_under_freq_derate_real,  sizeof(over_under_freq_derate_para)/sizeof(para_sid_tab_t),sizeof(over_under_freq_derate_real)/sizeof(real_sid_t), 1, DIM_TYPE, DC_AC_SET_OVER_UNDER_FREQ_DERATE_DATA},

};

static void send_para_check_msg(void *parameter);

para_check_t para_check_manage[] =
{
    {TYPE_INT8U, check_int8u_data},
    {TYPE_INT8S, check_int8s_data},
    {TYPE_INT16U, check_int16u_data},
    {TYPE_INT16S, check_int16s_data},
    {TYPE_INT32U, check_int32u_data},
    {TYPE_INT32S, check_int32s_data},
    {TYPE_FLOAT, check_float_data},
    {TYPE_STRING, check_string_data},
    {TYPE_DATE_T, check_date_data},
    {TYPE_TIME_T, check_time_data},

};

int init_para_check(void)
{   
    char *timer_name = "para_check";
    g_para_check_timer = rt_timer_create(timer_name, send_para_check_msg, NULL, PARA_CHECK_PERIOD, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
    if (g_para_check_timer == NULL)
    {
        return FAILURE;
    }
    rt_timer_start(g_para_check_timer);
    return SUCCESSFUL;
}

void stop_para_check()
{
    rt_timer_stop(g_para_check_timer);
    return;
}

void start_para_check()
{
    rt_timer_start(g_para_check_timer);
    return;
}

//发送消息给设备模块
static void send_para_check_msg(void *parameter)
{   
    send_msg_to_thread(CHECK_DC_AC_PARA, MOD_SYS_MANAGE, NULL, 0);
    return;
}

void para_check_period(void)
{   
    send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, DC_AC_GET_PARA_DATA);
    rt_thread_delay(3000); // 等待3s,保证参数读取成功
    check_all_para();
    g_count_time++;
    if(g_count_time > COUNT_TIME)
    {
        g_count_time = 0;
        g_count_print = 0;
    }
}


int get_para_ids(int para_sid_num, int block_dim, para_sid_tab_t* para_sid_tab)
{   
    int sid_index = 0;
    int para_index = 0;
    int dim_num = 0, dim_index = 0;
    unsigned short para_sid = 0;
    unsigned short dim_sid = 0;
    char flag = 0;
    int dim_sid_num = 0;  // 从DIM_START_TYPE标志位开始，后面再添加dim_sid_num个参数就直接返回
    int accu_sid_num = 0; // 累加的sid数量

    for(sid_index = 0; sid_index < para_sid_num; sid_index++)
    {   
        dim_num = para_sid_tab[sid_index].dim;
        para_sid = para_sid_tab[sid_index].para_sid;
        flag = para_sid_tab[sid_index].flag;

        if (flag == DIM_SID_TYPE)    // 从sid里读取维度
        {   
            dim_sid = dim_num;
            dim_num = 0;
            get_one_para(dim_sid, &dim_num);
        }
        else if (flag == DIM_START_TYPE)    // 从sid读取数据，并且后面sid数量累加到该值，就会返回
        {
            get_one_para(para_sid, &dim_sid_num);
            accu_sid_num = 0;
        }

        for(dim_index = 0; dim_index < dim_num; dim_index++)
        {
            g_para_sids[para_index] = para_sid + dim_index + block_dim;
            para_index++;

            if (dim_sid_num == 0)
            {
                continue;
            }
            
            accu_sid_num++;
            if (accu_sid_num == dim_sid_num + 1)
            {
                // 累加的数量达到了
                return para_index;
            }
        }
    }
    return para_index;
}

int get_real_ids(int real_sid_num, int block_dim, real_sid_t* real_sid_tab)
{   
    int sid_index = 0;
    int real_index = 0;
    int dim_num = 0, dim_index = 0;
    unsigned int real_sid = 0;
    unsigned int dim_sid = 0;
    char flag = 0;
    int dim_sid_num = 0;  // 从DIM_START_TYPE标志位开始，后面再添加dim_sid_num个参数就直接返回
    int accu_sid_num = 0; // 累加的sid数量

    for(sid_index = 0; sid_index < real_sid_num; sid_index++)
    {
        dim_num = real_sid_tab[sid_index].dim;
        real_sid = real_sid_tab[sid_index].real_sid;
        flag = real_sid_tab[sid_index].flag;

        if (flag == DIM_SID_TYPE)
        {   
            dim_sid = dim_num;
            dim_num = 0;
            get_one_data(dim_sid, &dim_num);
        }
        else if (flag == DIM_START_TYPE)
        {
            get_one_data(real_sid, &dim_sid_num);
            accu_sid_num = 0;
        }

        for(dim_index = 0; dim_index < dim_num; dim_index++)
        {
            g_real_sids[real_index] = real_sid + dim_index + block_dim;
            real_index++;

            if (dim_sid_num == 0)
            {
                continue;
            }
            
            accu_sid_num++;
            if (accu_sid_num == dim_sid_num + 1)
            {
                // 累加的数量达到了
                return real_index;
            }
        }

    }
    return real_index;
}

void send_event_info(unsigned short event_id, char* info, int info_len, unsigned char type)
{
    event_record_t save_event_info = {};
    rt_memset(&save_event_info, 0, sizeof(save_event_info));
    save_event_info.type = type;    // 参数
    rt_memcpy(save_event_info.info, info, info_len);  // info 内容
    put_int16_to_buff((unsigned char*)&save_event_info.event_id ,event_id);       // event id 
    send_msg_to_thread(SAVE_EVENT_MSG, MOD_SYS_MANAGE, &save_event_info, sizeof(save_event_info));
    if(g_count_print < COUNT_PRINT)
    {
        LOG_E("para_check|id:0x%x, info:%s", event_id, info);
        g_count_print++;
    }
}

int check_int32s_data(unsigned short para_offset, void* real_data, void* para_data)
{
    char info[MAX_EVENT_INFO_LEN] = {};
    numeric_para_attr_t* numeric_para_attr_tab = get_numeric_para_attr_tab();
    if(*(int*)real_data == *(int*)para_data)
    {
        return TRUE;
    }

    rt_memset(info, 0, MAX_EVENT_INFO_LEN);
    rt_snprintf(info, MAX_EVENT_INFO_LEN, "s %d to %d", *(int*)real_data, *(int*)para_data);
    send_event_info(numeric_para_attr_tab[para_offset].id, info, sizeof(info), PARA_TYPE);
    return FALSE;
}

int check_int32u_data(unsigned short para_offset, void* real_data, void* para_data)
{
    char info[MAX_EVENT_INFO_LEN] = {};
    numeric_para_attr_t* numeric_para_attr_tab = get_numeric_para_attr_tab();
    if(*(unsigned int*)real_data == *(unsigned int*)para_data)
    {
        return TRUE;
    }

    rt_memset(info, 0, MAX_EVENT_INFO_LEN);
    rt_snprintf(info, MAX_EVENT_INFO_LEN, "s %d to %d", *(unsigned int*)real_data, *(unsigned int*)para_data);
    send_event_info(numeric_para_attr_tab[para_offset].id, info, sizeof(info), PARA_TYPE);
    return FALSE;
}

int check_int16s_data(unsigned short para_offset, void* real_data, void* para_data)
{
    char info[MAX_EVENT_INFO_LEN] = {};
    numeric_para_attr_t* numeric_para_attr_tab = get_numeric_para_attr_tab();
    if(*(short*)real_data == *(short*)para_data)
    {
        return TRUE;
    }

    rt_memset(info, 0, MAX_EVENT_INFO_LEN);
    rt_snprintf(info, MAX_EVENT_INFO_LEN, "s %d to %d", *(short*)real_data, *(short*)para_data);
    send_event_info(numeric_para_attr_tab[para_offset].id, info, sizeof(info), PARA_TYPE);
    return FALSE;
}

int check_int16u_data(unsigned short para_offset, void* real_data, void* para_data)
{
    char info[MAX_EVENT_INFO_LEN] = {};
    numeric_para_attr_t* numeric_para_attr_tab = get_numeric_para_attr_tab();
    if(*(unsigned short*)real_data == *(unsigned short*)para_data)
    {
        return TRUE;
    }

    rt_memset(info, 0, MAX_EVENT_INFO_LEN);
    rt_snprintf(info, MAX_EVENT_INFO_LEN, "s %d to %d", *(unsigned short*)real_data, *(unsigned short*)para_data);
    send_event_info(numeric_para_attr_tab[para_offset].id, info, sizeof(info), PARA_TYPE);
    return FALSE;
}

int check_int8s_data(unsigned short para_offset, void* real_data, void* para_data)
{
    char info[MAX_EVENT_INFO_LEN] = {};
    numeric_para_attr_t* numeric_para_attr_tab = get_numeric_para_attr_tab();
    if(*(char*)real_data == *(char*)para_data)
    {
        return TRUE;
    }

    rt_memset(info, 0, MAX_EVENT_INFO_LEN);
    rt_snprintf(info, MAX_EVENT_INFO_LEN, "s %d to %d", *(char*)real_data, *(char*)para_data);
    send_event_info(numeric_para_attr_tab[para_offset].id, info, sizeof(info), PARA_TYPE);
    return FALSE;
}

int check_int8u_data(unsigned short para_offset, void* real_data, void* para_data)
{
    char info[MAX_EVENT_INFO_LEN] = {};
    numeric_para_attr_t* numeric_para_attr_tab = get_numeric_para_attr_tab();
    if(*(unsigned char*)real_data == *(unsigned char*)para_data)
    {
        return TRUE;
    }
    
    rt_memset_s(info, MAX_EVENT_INFO_LEN, 0, MAX_EVENT_INFO_LEN);
    rt_snprintf(info, MAX_EVENT_INFO_LEN, "s %d to %d", *(unsigned char*)real_data, *(unsigned char*)para_data);
    send_event_info(numeric_para_attr_tab[para_offset].id, info, sizeof(info), PARA_TYPE);
    return FALSE;
}

int check_float_data(unsigned short para_offset, void* real_data, void* para_data)
{
    char info[MAX_EVENT_INFO_LEN] = {};
    numeric_para_attr_t* numeric_para_attr_tab = get_numeric_para_attr_tab();
    if(FLOAT_EQ(*(float*)real_data, *(float*)para_data) == TRUE)
    {
        return TRUE;
    }

    rt_memset_s(info, MAX_EVENT_INFO_LEN, 0, MAX_EVENT_INFO_LEN);
    rt_snprintf(info, MAX_EVENT_INFO_LEN, "s %.*f to %.*f", numeric_para_attr_tab[para_offset].precision, *(float*)real_data, numeric_para_attr_tab[para_offset].precision, *(float*)para_data);
    send_event_info(numeric_para_attr_tab[para_offset].id, info, sizeof(info), PARA_TYPE);
    return FALSE;
}

int check_string_data(unsigned short para_offset, void* real_data, void* para_data)
{   
    char info[MAX_EVENT_INFO_LEN] = {};
    string_para_attr_t* string_para_attr_tab = get_string_para_attr_tab();
    if(rt_memcmp(real_data, para_data, sizeof(str64)) == 0)
    {
        return TRUE;
    }

    rt_memset_s(info, MAX_EVENT_INFO_LEN, 0, MAX_EVENT_INFO_LEN);
    rt_snprintf(info, MAX_EVENT_INFO_LEN, "s %s to %s", (const char*)real_data, (const char*)para_data);
    send_event_info(string_para_attr_tab[para_offset].id, info, sizeof(info), PARA_TYPE);
    
    return FALSE;
}

int check_date_data(unsigned short para_offset, void* real_data, void* para_data)
{   
    char info[MAX_EVENT_INFO_LEN] = {};
    string_para_attr_t* string_para_attr_tab = get_string_para_attr_tab();
    date_base_t* date_para = (date_base_t*)para_data;
    if(rt_memcmp(real_data, para_data, sizeof(date_base_t)) == 0)
    {
        return TRUE;
    }

    rt_memset_s(info, MAX_EVENT_INFO_LEN, 0, MAX_EVENT_INFO_LEN);
    rt_snprintf(info, MAX_EVENT_INFO_LEN, "s %d-%d-%d", date_para->year, date_para->month, date_para->day);
    send_event_info(string_para_attr_tab[para_offset].id, info, sizeof(info), PARA_TYPE);
    return FALSE;
}

int check_time_data(unsigned short para_offset, void* real_data, void* para_data)
{   
    char info[MAX_EVENT_INFO_LEN] = {};
    string_para_attr_t* string_para_attr_tab = get_string_para_attr_tab();
    time_base_t* time_para = (time_base_t*)para_data;
    if(rt_memcmp(real_data, para_data, sizeof(time_base_t)) == 0)
    {
        return TRUE;
    }

    rt_memset_s(info, MAX_EVENT_INFO_LEN, 0, MAX_EVENT_INFO_LEN);
    rt_snprintf(info, MAX_EVENT_INFO_LEN, "s %d-%d-%d %d:%d:%d", time_para->year, time_para->month, time_para->day, time_para->hour, time_para->minute, time_para->second);
    send_event_info(string_para_attr_tab[para_offset].id, info, sizeof(info), PARA_TYPE);

    return FALSE;
}

int check_one_para(unsigned int real_sid, unsigned short para_sid)
{   
    int ret = FALSE;
    str64 real_data = {0};
    str64 para_data = {0};
    unsigned char data_type = GET_SYS_PARA_DATA_TYPE(para_sid);
    unsigned short para_offset = GET_SYS_PARA_OFFSET(para_sid);

    get_one_para(para_sid, &para_data);
    get_one_data(real_sid, &real_data);
    
    ret = para_check_manage[data_type].check_para(para_offset, (void*)real_data, (void*)para_data);
    return ret;
}

int check_multi_para(int para_num, int real_num)
{
    int i = 0;
    int ret = 0;
    if(real_num != para_num)
    {   
        // LOG_E("para_check | para_num real_num not same");
        return FALSE;
    }

    for(i = 0; i < para_num; i++)
    {
        ret = check_one_para(g_real_sids[i], g_para_sids[i]);
        if(ret == FALSE)
        {
            return FALSE;
        }
    }
    return TRUE;
}

void check_all_para(void)
{   
    int block_index = 0;
    int para_block_num = sizeof(para_cmp_tab)/sizeof(para_cmp_tab[0]);
    int para_sid_num = 0, real_sid_num = 0;
    int para_num = 0, real_num = 0;
    int block_dim = 0;
    int para_cmp_dim = 0;
    para_sid_tab_t* para_sid_tab = NULL;
    real_sid_t* real_sid_tab = NULL;
    
    for(block_index = 0; block_index < para_block_num; block_index++)
    {   
        para_sid_num = para_cmp_tab[block_index].para_tab_size;
        real_sid_num = para_cmp_tab[block_index].real_tab_size;
        para_sid_tab = (para_sid_tab_t*)para_cmp_tab[block_index].para_tab;
        real_sid_tab = (real_sid_t*)para_cmp_tab[block_index].real_tab;
        para_cmp_dim = para_cmp_tab[block_index].dim;

        for(block_dim = 0; block_dim < para_cmp_dim; block_dim++)
        {
            para_num = get_para_ids(para_sid_num, block_dim, para_sid_tab);
            real_num = get_real_ids(real_sid_num, block_dim, real_sid_tab);

            if(check_multi_para(para_num, real_num) == FALSE)
            {   
                send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, para_cmp_tab[block_index].cmd_id + block_dim);
                rt_thread_mdelay(300);  // 等待300ms
                // LOG_E("check_all_para cmd_id: %d",para_cmp_tab[block_index].cmd_id + block_dim);
            }

            para_num = 0;
            real_num = 0;
            rt_memset_s(g_para_sids, sizeof(g_para_sids), 0x00, sizeof(g_para_sids));
            rt_memset_s(g_real_sids, sizeof(g_real_sids), 0x00, sizeof(g_real_sids));
        }
    }


}
