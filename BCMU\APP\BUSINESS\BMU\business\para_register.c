#include "app_config.h"
#include "type_define_in.h"
#include "para_manage.h"
#include "sys_para_in.h"
#include "alarm_para_in.h"


static para_manage_info_t s_bmu_para_manage = {
    SYS_NUMERIC_PARA_MAX_OFFSET,
    SYS_STRING_PARA_MAX_OFFSET,
    ALARM_ID_OFFSET_MAX,
    numeric_para_attr_tab,
    scope_tab,
    constraint_tab,
    numeric_val_tab,
    string_para_attr_tab,
    string_val_tab,
    alarm_attr_tab,
    alarm_attr_val_tab,
};



void register_product_para(void) {
    register_para_manage_info(&s_bmu_para_manage);
}

