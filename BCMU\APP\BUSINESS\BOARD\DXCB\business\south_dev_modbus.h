#ifndef SOUTH_DEV_MODBUS_H_
#define SOUTH_DEV_MODBUS_H_

#ifdef __cplusplus
extern "C" {
#endif

int update_fan_avc_dev_type(dev_inst_t* dev_inst, char dev_num, char is_inter_fan);
int update_fan_ebm_dev_type(dev_inst_t* dev_inst, char dev_num, char is_inter_fan);
int update_fan_fsd_dev_type(dev_inst_t* dev_inst, char dev_num, char is_inter_fan);
int update_fan_slb_dev_type(dev_inst_t* dev_inst, char dev_num, char is_inter_fan);
int update_vfd_ams_dev_type(dev_inst_t* dev_inst, char dev_num, char is_inter_fan);
int update_vfd_dfs_dev_type(dev_inst_t* dev_inst, char dev_num, char is_inter_fan);
int update_vfd_mz_dev_type(dev_inst_t* dev_inst, char dev_num, char is_inter_fan);
int update_vfd_hc_dev_type(dev_inst_t* dev_inst, char dev_num, char is_inter_fan);
int update_south_mock_dev_type(dev_inst_t* dev_inst);


#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif
