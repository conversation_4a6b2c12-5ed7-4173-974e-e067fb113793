#include "power_ctrl_utils.h"
#include "para_manage.h"
#include "para_id_in.h"
#include "msg_id.h"
#include "cmd.h"
#include "sps.h"
#include "data_type.h"
#include "realdata_save.h"
#include "realdata_id_in.h"
#include "softbus.h"

#define FLT_EPSILON 1e-5
#define FLOAT_EQUAL(a, b) (fabs((a) - (b)) < FLT_EPSILON)

char res_ori_power_ctrl_data()
{
    short data_s = 0;
    float data_f = 0.0;
    short temp_data_s = 0;
    float temp_data_f = 0.0;
    char diff_flag = FALSE;
    int float_para_list[] = {DAC_PARA_ID_GRID_ACTIVE_POWER_CHANGE_OFFSET,
                             DAC_PARA_ID_ACTIVE_POWER_DERATING_SETTING_OFFSET,
                             DAC_PARA_ID_ACTIVE_POWER_DERATING_PERCENT_OFFSET,
                             DAC_PARA_ID_GRID_REACTIVE_POWER_CHANGE_OFFSET,
                             DAC_PARA_ID_REACTIVE_POWER_COMPEN_SETTING_FACTOR_OFFSET,
                             DAC_PARA_ID_REACTIVE_POWER_COMPEN_SETTING_OFFSET,
                             DAC_PARA_ID_REACTIVE_POWER_REGULATIONG_TIME_OFFSET,
                            };
    int short_para_list[] = {DAC_PARA_ID_ACTIVE_POWER_CONTROL_MODE_OFFSET,
                             DAC_PARA_ID_REACTIVE_POWER_COMPEN_MODE_OFFSET,
                            };
    int float_para_len = sizeof(float_para_list)/sizeof(float_para_list[0]);
    int short_para_len = sizeof(short_para_list)/sizeof(short_para_list[0]);
    for(int i = 0; i < float_para_len; i++)
    {
        get_one_para(float_para_list[i]+1, &data_f);
        get_one_para(float_para_list[i], &temp_data_f);
        if(!FLOAT_EQUAL(data_f, temp_data_f))
        {
            set_one_para(float_para_list[i], &data_f, FALSE, TRUE);
            diff_flag |= TRUE;
        }
    }
    for(int i = 0; i < short_para_len; i++)
    {
        get_one_para(short_para_list[i]+1, &data_s);
        get_one_para(short_para_list[i], &temp_data_s);
        if(data_s != temp_data_s)
        {
            set_one_para(short_para_list[i], &data_s, FALSE, TRUE);
            diff_flag |= TRUE;
        }
    }
    if(diff_flag)
    {
        save_numeric_para();
        send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, DC_AC_SET_ACT_REGULATION_PARA_DATA);
        send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, DC_AC_SET_REACT_REGULATION_PARA_DATA);
        send_msg_to_thread(GRID_CODE_STATUS_CHECK, MOD_SYS_MANAGE, NULL, 0);
    }

    return diff_flag;
}

char set_act_power_adj()
{
    short data_s = 0;
    float data_f = 0.0;
    short temp_data_s = 0;
    float temp_data_f = 0.0;
    char act_flag = FALSE;

    // 失效保护有功功率模式
    get_one_para(DAC_PARA_ID_FAIL_PROT_ACTIVE_POWER_MODE_OFFSET, &data_s);
    if(data_s == FAIL_ACT_POWER_PERCENT)
    {
        data_s = ACTIVE_POWER_PERCENT;
    }
    get_one_para(DAC_PARA_ID_ACTIVE_POWER_CONTROL_MODE_OFFSET, &temp_data_s);
    if(data_s != temp_data_s)
    {
        set_one_para(DAC_PARA_ID_ACTIVE_POWER_CONTROL_MODE_OFFSET, &data_s, FALSE, TRUE);
        act_flag |= TRUE;
    }
    // 失效保护有功功率限值(%)
    get_one_para(DAC_PARA_ID_ACTIVE_POWER_LIMIT_FAIL_POWER_PERCENT_OFFSET, &data_f);
    get_one_para(DAC_PARA_ID_ACTIVE_POWER_DERATING_PERCENT_OFFSET, &temp_data_f);
    act_flag |= is_para_change(DAC_PARA_ID_ACTIVE_POWER_DERATING_PERCENT_OFFSET, data_f, temp_data_f);

    // 失效保护有功功率限值(kw)
    get_one_para(DAC_PARA_ID_ACTIVE_POWER_LIMIT_FAIL_PROT_VALUE_OFFSET, &data_f);
    get_one_para(DAC_PARA_ID_ACTIVE_POWER_DERATING_SETTING_OFFSET, &temp_data_f);
    act_flag |= is_para_change(DAC_PARA_ID_ACTIVE_POWER_DERATING_SETTING_OFFSET, data_f, temp_data_f);

    if(act_flag)
    {
        save_numeric_para();
        send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, DC_AC_SET_ACT_REGULATION_PARA_DATA);
        send_msg_to_thread(GRID_CODE_STATUS_CHECK, MOD_SYS_MANAGE, NULL, 0);
    }

    return act_flag;
}

char set_react_power_adj()
{
    short data_s = 0;
    float data_f = 0.0;
    short temp_data_s = 0;
    float temp_data_f = 0.0;
    char react_flag = FALSE;

    // 失效保护无功功率模式
    get_one_para(DAC_PARA_ID_FAIL_PROT_REACTIVE_POWER_MODE_OFFSET, &data_s);
    if(data_s == FAIL_REACT_POWER_FACTOR)
    {
        data_s = REACTIVE_POWER_FACTOR;
    }
    get_one_para(DAC_PARA_ID_REACTIVE_POWER_COMPEN_MODE_OFFSET, &temp_data_s);
    if(data_s != temp_data_s)
    {
        set_one_para(DAC_PARA_ID_REACTIVE_POWER_COMPEN_MODE_OFFSET, &data_s, FALSE, TRUE);
        react_flag |= TRUE;
    }
    // 失效保护无功功率限值(PF)
    get_one_para(DAC_PARA_ID_FAIL_PROT_REACTIVE_LIMIT_VALUE_OFFSET, &data_f);
    get_one_para(DAC_PARA_ID_REACTIVE_POWER_COMPEN_SETTING_FACTOR_OFFSET, &temp_data_f);
    react_flag |= is_para_change(DAC_PARA_ID_REACTIVE_POWER_COMPEN_SETTING_FACTOR_OFFSET, data_f, temp_data_f);

    // 失效保护无功功率限值(Q/S)
    get_one_para(DAC_PARA_ID_FAIL_PROT_REACTIVE_POWER_LIMIT_PROT_OFFSET, &data_f);
    get_one_para(DAC_PARA_ID_REACTIVE_POWER_COMPEN_SETTING_OFFSET, &temp_data_f);
    react_flag |= is_para_change(DAC_PARA_ID_REACTIVE_POWER_COMPEN_SETTING_OFFSET, data_f, temp_data_f);

    if(react_flag)
    {
        save_numeric_para();
        send_msg_to_sps_cmd(EXE_DEST_CMD_MSG, MOD_DC_AC, 1, DC_AC_SET_REACT_REGULATION_PARA_DATA);
        send_msg_to_thread(GRID_CODE_STATUS_CHECK, MOD_SYS_MANAGE, NULL, 0);
    }

    return react_flag;
}

char is_para_change(int sid, float data_f, float temp_data_f)
{
    char change_flag = FALSE;
    if(!FLOAT_EQUAL(data_f, temp_data_f))
    {
        set_one_para(sid, &data_f, FALSE, TRUE);
        change_flag = TRUE;
    }
    return change_flag;
}

int is_power_ctrl_para_chg()
{
    unsigned short power_ctrl_para1[] = { DAC_PARA_ID_ACTIVE_POWER_DERATING_SETTING_OFFSET, DAC_PARA_ID_ACTIVE_POWER_DERATING_PERCENT_OFFSET,
    DAC_PARA_ID_REACTIVE_POWER_COMPEN_SETTING_FACTOR_OFFSET, DAC_PARA_ID_REACTIVE_POWER_COMPEN_SETTING_OFFSET};

    unsigned int power_ctrl_data1[] = {DAC_DATA_ID_ACTIVE_POWER_DERATING_SETTING_CURR, DAC_DATA_ID_ACTIVE_POWER_DERATING_PERCENT_CURR,
    DAC_DATA_ID_REACTIVE_POWER_COMPEN_SETTING_FACTOR_CURR, DAC_DATA_ID_REACTIVE_POWER_COMPEN_SETTING_CURR};

    unsigned short power_ctrl_para2[] = {DAC_PARA_ID_ACTIVE_POWER_CONTROL_MODE_OFFSET, DAC_PARA_ID_REACTIVE_POWER_COMPEN_MODE_OFFSET};

    unsigned int power_ctrl_data2[] = {DAC_DATA_ID_ACTIVE_POWER_CONTROL_MODE_CURR, DAC_DATA_ID_REACTIVE_POWER_COMPEN_MODE_CURR};

    unsigned char version[32] = {0};
    unsigned char open_off_status = 0;
    int loop = 0;
    float f_value = 0.0;
    unsigned short us_value = 0;
    for(loop = 0; loop < sizeof(power_ctrl_para1) / sizeof(unsigned short); loop ++)
    {
        get_one_para(power_ctrl_para1[loop], &f_value);
        set_one_data(power_ctrl_data1[loop], &f_value);
    }

    for(loop = 0; loop < sizeof(power_ctrl_para2) / sizeof(unsigned short); loop ++)
    {
        get_one_para(power_ctrl_para2[loop], &us_value);
        set_one_data(power_ctrl_data2[loop], &us_value);
    }

    get_one_para(DAC_PARA_ID_POWER_ON_OFF_OFFSET, &open_off_status);
    set_one_data(DAC_DATA_ID_POWER_ON_OFF_STA, &open_off_status);
    memset(version, 0, 32); 
    get_one_para(DAC_PARA_ID_PSC_VERSION_OFFSET, version);
    set_one_data(DAC_DATA_ID_PSC_VERSION_CURR, version);
    memset(version, 0, 32);
    get_one_para(DAC_PARA_ID_CSC_VERSION_OFFSET, version);
    set_one_data(DAC_DATA_ID_CSC_VERSION_CURR, version);
    return SUCCESSFUL;
}