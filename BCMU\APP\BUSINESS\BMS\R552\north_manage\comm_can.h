﻿#ifndef _COMM_CAN_H_
#define _COMM_CAN_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "linklayer.h"
#include "protocol_layer.h"


#define NORTH_CAN1_DELAY    10  // unit:ms
#define CAN1_R_BUFF_LEN               1024     ///<  接收缓冲区长度
#define CAN1_S_BUFF_LEN               1024     ///<  发送缓冲区长度

typedef enum {
    BMS_NORTH_DEV_INDEX_DOWNLOAD = 0,  
    BMS_NORTH_DEV_INDEX_1363,           
    BMS_NORTH_DEV_INST_NUM,         //北向设备实例数           
}dev_dev_north_index_e;

typedef struct {
    void*        data;
    cmd_buf_t*   cmd_buff;
    link_inst_t* link_inst;
    rt_mq_t      north_mq;
}north_mgr_t;

void* init_can_comm(void *param);
void process_can_comm(void* parameter);


#ifdef __cplusplus
}
#endif      //< __cplusplus

#endif      //< _COMM_CAN_H_
