/**
 * @file     sample_handle.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2023-05-22
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */

 
#ifndef _PARA_CHECK_H
#define _PARA_CHECK_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include <stdio.h>
#include <rtthread.h>
#include <rtdevice.h>
#include <string.h>
#include "data_type.h"
#include "dev_dc_ac.h"
#include "utils_server.h"

#define PARA_CHECK_PERIOD        (10 * 60 * 1000)  // 10分钟
#define PARA_BUFF_LEN            350               // 一组数据最大长度
#define SID_MAX_NUM              170               // SID的最大数量， 故障录波最大有170个参数

#define DIM_TYPE        0       // 按维度展开
#define DIM_SID_TYPE    1       // 从sid里面读取维度
#define DIM_START_TYPE  2       // 从这关键字开始，累加到读到的值

#define COUNT_TIME      144     // 一天的时间
#define COUNT_PRINT     50      // 一天内能够打印50次

// 实时量数据
typedef struct 
{
    unsigned int real_sid;     // 实时量SID
    unsigned int dim;          // 展开维度
    char         flag;         // 怎么展开
}real_sid_t; 

// 参数量数据
typedef struct 
{
    unsigned short para_sid;   // 参数量SID
    unsigned short dim;        // 展开维度
    char           flag;       // 怎么展开
}para_sid_tab_t;


typedef struct 
{
    void*    para_tab;         // 参数sid表格
    void*    real_tab;         // 实时量sid表格
    int      para_tab_size;    // 参数sid数量
    int      real_tab_size;    // 实时量sid数量
    char     dim;              // 展开维度
    char     flag;             // 怎么展开
    unsigned char cmd_id;      // 南向命令id
}para_cmp_t;


typedef struct {
    unsigned char data_type;
    int (*check_para)(unsigned short para_sid, void* real_data, void* para_data);
} para_check_t;


int init_para_check(void);
void para_check_period(void);
void start_para_check();
void stop_para_check();
int check_int32s_data(unsigned short para_sid, void* real_data, void* para_data);
int check_int32u_data(unsigned short para_sid, void* real_data, void* para_data);
int check_int16s_data(unsigned short para_sid, void* real_data, void* para_data);
int check_int16u_data(unsigned short para_sid, void* real_data, void* para_data);
int check_int8s_data(unsigned short para_sid, void* real_data, void* para_data);
int check_int8u_data(unsigned short para_sid, void* real_data, void* para_data);
int check_float_data(unsigned short para_sid, void* real_data, void* para_data);
int check_string_data(unsigned short para_sid, void* real_data, void* para_data);
int check_date_data(unsigned short para_sid, void* real_data, void* para_data);
int check_time_data(unsigned short para_sid, void* real_data, void* para_data);
int check_one_para(unsigned int real_sid, unsigned short para_sid);
void send_event_info(unsigned short event_id, char* info, int info_len, unsigned char type);
void check_all_para(void);
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _SAMPLE_H