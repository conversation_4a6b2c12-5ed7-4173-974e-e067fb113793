/**************************************************************************
* Copyright (C) 2010-2011, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：ZXDU18 CTL板软件
* 文件名称：battery.c
* 文件说明：电池管理模块
* 作    者  ： 
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/
#include "common.h"
#include "sample.h"
#include "utils_heart_beat.h"
#include "thread_id.h"
#include "realAlarm.h"
#include "battery.h"
#include "para.h"
#include "comm.h"
#include "protocol.h"
#include "CommCan.h"
#include "Candrive.h"
#include "commBdu.h"
#include "hisdata.h"
#include "fileSys.h"
#include "sample.h"
#include "led.h"
#include "prtclDL.h"
#include <stdio.h>
#include <stddef.h>
#include "MultBattData.h"
#include "MasterOper.h"
#include "SlaveOper.h"
#include "ChargeRotate.h"
#include "BattTimer.h"
#include "BattSleep.h"
#include "BattCharge.h"
#include "BattDischarge.h"
#include "BattSOXCalc.h"
#include "BattBalance.h"
#include "BattDataLoad.h"
#include "BattSleepCtrl.h"
#include "apptest.h"
#include "qtp.h"
#include "usart.h"
#include <float.h>
#include "const_define.h"
#include "utils_rtthread_security_func.h"
#include "wireless.h"
#ifdef USING_SNMP
#include "snmp_comm_status.h"
#endif

#ifdef ACTIVATE_PORT_ENABLED
#include "ActivatePort.h"
#endif
#ifdef USE_CAN2
#include "CommCan2.h"
#endif

#ifdef JUDGECOMM_4G_NET_CAN2
#include "download_tcp.h"
#include "NetProtocol1363.h"
#endif

#define CURR_DROP_MIN (3.00)

extern struct netif *netif_default;

/***********************  变量定义  ************************/
Static T_BattInfo           s_tBattIn;
Static T_BattDealInfoStruct s_tBattDeal;
Static T_BattResult         s_tBattOut;
#ifdef USE_CAN2
    Static T_BattResult         s_tBattOutCAN2;
#endif
Static T_SysPara            s_tSysPara;
Static T_HardwareParaStruct s_tHardwarePara;
Static T_BCMAlarmStruct     s_tBCMRealAlm;//未屏蔽的告警
Static T_BCMAlarmStruct     s_tBCMAlm;    //屏蔽后的告警
Static T_RotateDebugStruct  s_tRotateDebug;
Static T_SOCCalDebugStruct  s_tSOCCalDebug;
Static T_DcrCntStruct s_tDcrCnt;
Static BOOLEAN s_bIfDealDcr = FALSE;//是否执行DCR检测

static T_AnalyseInfoStruct s_tAnalyseInfo = {0, };  //统计记录相关告警信息
#ifdef INTELLIGENT_PEAK_SHIFTING
Static T_PeakShiftPara      s_tPeakShift;
#endif

Static BYTE s_battModeChangeCn = 0;
Static BYTE s_ucFlagBattery = 0;
Static WORD s_wCellCycleTimes = 0;
Static WORD s_wNoOutputCurrTimer = 0;  //电池无电流的时间计数器
Static BYTE s_ucJudgeIfLoopFault = NO_LOOP_FAULT;   //是否需要进行回路异常的操作
Static BOOLEAN s_bExternalPowerOnFlag = False;    //是否存在外部有电状态为是的电池,True存在，False不存在
Static BOOLEAN s_bNativePowerOnStatus = False; //本机的外部有电状态
Static FLOAT  s_fCurrentBysVoltageBak = 0;    //进行回路异常判断时记录当时的母排电压
Static T_VoltTwoClassPrtStruct s_tVoltTwoClassPrt;
static WORD s_wVoltTwoClassStatsTimeMinu = 24*60; //供内部测试使用，更改电压二级保护时间
Static BOOLEAN s_bCellDamageLock = False;         // 单体过压闭锁及单体欠压闭锁标志位
static BOOLEAN s_bDcrJudgeFlag = FALSE; //DCR条件开始判断的标志
Static BOOLEAN s_bSaveMasterSlaveChangeOps = False; // 保存主从机切换操作记录延时标志位
Static rt_mutex_t s_ptMutexBattRunFlag = RT_NULL;    // 电池管理线程互斥信号量，进入Take，退出Release
static BOOLEAN s_bEnterDelayJudge = False;
Static BYTE s_ucLoopOffStatus = LOOP_OFF_NO;
static FLOAT s_fSelfAdaptionInitVolt = MIX_INIT_DISCHARGE_VOLT;  //自适应模式初始电压，默认43.8V，只在初始化时赋值
/********************* 静态函数原型定义 ************************/
Static void SaveBattRealData(void);
Static void DealBattResult(void);
static BOOLEAN SmartLi_IsPowerDown( void );
//static void AccumulateBattFullTimer(void);
//static void NewSaveBattInfo(void);
#ifdef SMALL_CURR_COMPENSATION_ENABLED
Static BOOLEAN IfNeedDischargeSOCCal(void);
#endif
Static void CalBattCap( void );
Static void InitBattSave(void);
static void CalcBattSOCAndSOH(void);
static void SaveBattFullTimer(void);
static WORD ReadBattFullTimer(void);
static void startDischg(void);
Static void CheckCtrlStatus(void);
static void CheckAndSetIOPara(void);
static void CheckAndSetCtrlPara(void);

Static BOOLEAN CalcBattDsichargNPlus1Times(void);

// static void CheckLABatteryFull(void);
static void CheckSaveEEPROM(void);
static BOOLEAN GetOverPrtAlm(T_BCMAlarmStruct const* ptAlm);
static BOOLEAN GetRealOverPrtAlm(T_BCMAlarmStruct const* ptAlm);
static BOOLEAN GetBattOverPrtAlm(T_BCMAlarmStruct const* ptAlm);
static BOOLEAN GetLowPrtAlm(T_BCMAlarmStruct const* ptAlm);
static BOOLEAN GetBattOverCurrPrtAlm(T_BCMAlarmStruct const* ptAlm);
static FLOAT CalcOutVolFromBattVol(void);
Static void CalcAccumulateData(void);
Static void CalcBattLeftMinutes(void);
Static void CalcSelfDischargeDecreaseCap(void);
Static void CellUnderVoltCondition(void);
// static void AdjustDischargeVolt(void);
Static BOOLEAN IsCellVoltLow(T_StateListenStruct* ptState, BYTE i);
Static BOOLEAN JudgeCellChargeFullVolt(FLOAT fCellChargeFullVolt);

#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
Static void CalMaxSocVoltDiff(WORD wMaxSOC);
#endif
BOOLEAN (*SmartLi_IsPowerDown_oper)(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattIn);
void (*startDischgOper)(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo);
Static void DefenseBattChargeMinutesCount(void);
Static BOOLEAN CheckBattLockState(void);

static void UpdateAnalyseInfo(void);

Static BOOLEAN IsCellVoltHighLock(T_StateListenStruct *ptState, BYTE i);
Static BOOLEAN IsCellVoltLowLock(T_StateListenStruct *ptState, BYTE i);
Static BOOLEAN JudgeBduLock(void);
static BYTE JudgeBatteryStatus_France(void);
static BOOLEAN DealDcr(void);
static BOOLEAN IfStartDcrTest(void);
static BOOLEAN JudgeBattInnerResFault(void);
static BOOLEAN SaveCtrlLockFlag(void);
#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
Static BOOLEAN CalCAN2ClusterVolt(void);
#endif
#ifdef INTELLIGENT_PEAK_SHIFTING
Static BOOLEAN PowerOffPeakShiftJudge(void);
#endif

#ifdef PAKISTAN_CMPAK_PROTOCOL
Static BOOLEAN CalBattRunTimeAndDisChgTimes(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal);
#endif


BOOLEAN IfBduDischarge(void)
{
    BDU_STATUS aucStatus[] = {BDU_STATUS_COMMON_DISCHARGE, BDU_STATUS_BOOST_DISCHARGE, BDU_STATUS_BUCK_DISCHARGE, BDU_STATUS_STRAIGHT_DISCHARGE};   //1126 mwl
    BYTE i=0;

    for(i=0; i<ARR_SIZE(aucStatus); i++)
    {
        if(s_tBattIn.tData.ucBDUStatus == aucStatus[i])
        {
            return True;
        }
    }

    return False;
}

BOOLEAN IfBduCharge(void)
{
    BDU_STATUS aucStatus[] = {BDU_STATUS_COMMON_CHARGE, BDU_STATUS_BOOST_CHARGE, BDU_STATUS_BUCK_CHARGE, BDU_STATUS_STRAIGHT_CHARGE};  //1126 mwl
    BYTE i=0;

    for(i=0; i<ARR_SIZE(aucStatus); i++)
    {
        if(s_tBattIn.tData.ucBDUStatus == aucStatus[i])
        {
            return True;
        }
    }

    return False;
}
#ifdef RUN_MODE_CONTROLLED_ENABLED

static INT32S SetDataBasedInfoType(BYTE ucProtocolType,SHORT wValue,T_ProtocolSetInfoStruct* ptSetDataInfo)
{

    if(RUN_MODE_FREE == s_tSysPara.ucRunMode && wValue == POWER_STATUS_ON)
    {
        if((s_tBattIn.tPara.ucUsageScen == DISCHG_MODE_SELF_ADAPTION || s_tBattIn.tPara.ucUsageScen == DISCHG_MODE_SOLAR) && s_tBattIn.tPara.bBoostChg == True
            && s_tBattIn.tData.fBusVol > 48.0f) 
        {
            if(s_tBattIn.tData.fBusVol < s_tBattDeal.fPowerDownVolt)
            {
                s_tBattDeal.fPowerDownVolt = s_tBattIn.tData.fBusVol - 1.0f;
                s_tBattDeal.fPowerDownVolt = GetValidData(s_tBattDeal.fPowerDownVolt, 
                    POWER_DOWN_VOL_MAX, POWER_DOWN_VOL_MIN);
            }
        }
    }

    ptSetDataInfo->sValue = wValue;
    ptSetDataInfo->bValid = (wValue != (WORD)POWER_STATUS_INVALID);
    ptSetDataInfo->ucUpdateTimer = (wValue != (WORD)POWER_STATUS_INVALID) ? 0 : PROTO_SET_INFO_TIMEOUT_MINUTES;
    ptSetDataInfo->ucProtoType = ucProtocolType;
    ptSetDataInfo->ucRunMode = s_tBattIn.tPara.ucRunMode;
    
    if((s_tBattIn.tData.bChargeProtect && POWER_STATUS_ON == wValue)
        ||(s_tBattIn.tData.bDischargeProtect && POWER_STATUS_OFF == wValue)
    )
    {
        return FAILED;
    }
    return SUCCESSFUL;

}
static INT32S IsBattModeCheck(SHORT wValue)
{
    if((BATT_MODE_CHARGE == s_tBattDeal.ucChargeMode) && ((s_tBattIn.tData.fBusVol - s_tBattDeal.fPowerDownVolt) < 0.8f) && (wValue < 0) && (s_tBattIn.tData.fBattCurr < 5.0f))
    {
        return 1;
    }
    else
    {
        return 0;
    }
}
static INT32S SetDataBasedSetMixBattCurr(SHORT wValue)
{
    if (IfBduCharge() && (BATT_MODE_CHARGE != s_tBattDeal.ucChargeMode))
    {
        return FAILED;
    }
    else if (IfBduDischarge() && BATT_MODE_DISCHARGE != s_tBattDeal.ucChargeMode)
    {
        return FAILED;
    }
    else if (DISCHG_MODE_SELF_ADAPTION != s_tBattIn.tPara.ucUsageScen && DISCHG_MODE_SOLAR != s_tBattIn.tPara.ucUsageScen)
    {
        return FAILED;
    }
    else if (!IsMaster() && BATT_MODE_DISCHARGE == s_tBattDeal.ucChargeMode)
    {
        return FAILED;
    }
    else if(IsBattModeCheck(wValue))
    {
        return FAILED;
    }
    else if (BATT_MODE_CHARGE == s_tBattDeal.ucChargeMode && (s_tBattIn.tData.fBusVol - s_tBattDeal.fPowerDownVolt) > 1.7f
        && wValue > 0)
    {
        return FAILED;
    }
    else if (BATT_MODE_CHARGE != s_tBattDeal.ucChargeMode && BATT_MODE_DISCHARGE != s_tBattDeal.ucChargeMode)
    {
        return FAILED;
    }
    return SUCCESSFUL;
}

static INT32S SetDataBasedVoltType(BYTE ucProtocolType,SHORT wValue,T_ProtocolSetInfoStruct* ptSetDataInfo)
{

   if (wValue <= 5800 && wValue >= 4200)
    {
        ptSetDataInfo->sValue = wValue;
        ptSetDataInfo->bValid = True;
        ptSetDataInfo->ucUpdateTimer = 0;
        ptSetDataInfo->ucProtoType = ucProtocolType;
        ptSetDataInfo->ucRunMode = s_tBattIn.tPara.ucRunMode;
    }
    else if(wValue == (WORD)0)
    {
        ptSetDataInfo->bValid = False;
        ptSetDataInfo->ucUpdateTimer = PROTO_SET_INFO_TIMEOUT_MINUTES;
        ptSetDataInfo->ucProtoType = ucProtocolType;
        ptSetDataInfo->ucRunMode = s_tBattIn.tPara.ucRunMode;
    }
    else
    {
        return FAILED; //scRet = FAILED;
    }
    return SUCCESSFUL;

}

static INT32S SetDataBasedCurrType(BYTE ucProtocolType, SHORT sValue, T_ProtocolSetInfoStruct* ptSetDataInfo)
{
    if (!GetQtptestFlag() && RUN_MODE_FREE != s_tBattIn.tPara.ucRunMode)
    {
        return FAILED;
    }
    if (sValue <= 2000)
    {
        ptSetDataInfo->sValue = sValue;
        ptSetDataInfo->bValid = True;
        ptSetDataInfo->ucUpdateTimer = 0;
        ptSetDataInfo->ucProtoType = ucProtocolType;
        ptSetDataInfo->ucRunMode = s_tBattIn.tPara.ucRunMode;
    }
    else
    {
        return FAILED;
    }

    return SUCCESSFUL;
}

INT32S SetValueFromProtocol(BYTE ucProtType, BYTE ucInfoType, SHORT sValue)
{
    T_ProtocolSetInfoStruct* ptProtoSetData = NULL;
    INT32S scRet = SUCCESSFUL;

    if (ucProtType >= INFO_MAX_NUM)
    {
        return FAILED;
    }

    ptProtoSetData = &s_tBattIn.tData.atProtoSetValue[ucInfoType];

    switch(ucInfoType)
    {
        case INFO_TYPE_ALL:
            rt_memset(&s_tBattIn.tData.atProtoSetValue[0], 0x00, sizeof(s_tBattIn.tData.atProtoSetValue));
            break;
        case INFO_TYPE_STATUS:
            scRet = SetDataBasedInfoType(ucProtType, sValue, ptProtoSetData);
            break;
        case INFO_TYPE_OUT_VOLT:
            scRet = SetDataBasedVoltType(ucProtType, sValue, ptProtoSetData);
            break;
        case INFO_TYPE_CHG_COEF:
            if (!GetQtptestFlag() && RUN_MODE_CONTROLLED != s_tBattIn.tPara.ucRunMode)
            {
                return FAILED;
            }
            if (sValue <= 200)
            {
                ptProtoSetData->sValue = sValue;
                ptProtoSetData->bValid = True;
                ptProtoSetData->ucUpdateTimer = 0;
                ptProtoSetData->ucProtoType = ucProtType;
                ptProtoSetData->ucRunMode = s_tBattIn.tPara.ucRunMode;
            }
            else
            {
                scRet = FAILED;
            }
            break;
        case INFO_TYPE_CHG_PRIOR:
            ptProtoSetData->sValue = sValue;
            ptProtoSetData->bValid = True;
            ptProtoSetData->ucUpdateTimer = 0;
            ptProtoSetData->ucProtoType = ucProtType;
            ptProtoSetData->ucRunMode = s_tBattIn.tPara.ucRunMode;
            break;
       case INFO_TYPE_SET_MIX_BATT_CURR:
            ptProtoSetData->sValue = 0;
            ptProtoSetData->bValid = True;
            ptProtoSetData->ucUpdateTimer = 0;
            ptProtoSetData->ucProtoType = ucProtType;
            ptProtoSetData->ucRunMode = s_tBattIn.tPara.ucRunMode;
            scRet = SetDataBasedSetMixBattCurr(sValue);
            ptProtoSetData->sValue = sValue;
            break;
        case INFO_TYPE_DISCH_VOLT_FREE:
            scRet = SetDataBasedVoltType(ucProtType, sValue, ptProtoSetData);
            break;
        case INFO_TYPE_SET_MAX_SOC:
            ptProtoSetData->sValue = sValue;
            ptProtoSetData->bValid = True;
            ptProtoSetData->ucUpdateTimer = SEND_MAX_SOC_TIME_OUT;
            ptProtoSetData->ucProtoType = ucProtType;
            ptProtoSetData->ucRunMode = s_tBattIn.tPara.ucRunMode;
            break;
        case INFO_TYPE_CHG_COEF_FREE:
            scRet = SetDataBasedCurrType(ucProtType, sValue, ptProtoSetData);
            break;
        default:
            return -1;
    }

    return scRet;
}
#endif /* RUN_MODE_CONTROLLED_ENABLED */
//shelltest
//void battON_test(int argc, char **argv)//shelltest
//{//shelltest
//  s_tBattDeal.bPowerOff = atoi(argv[1]);//shelltest
//}//shelltest
//MSH_CMD_EXPORT(battON_test, setmode);//shelltest
/***************************************************************/
void SaveBattInfo(void)
{
    T_BattSaveStruct tBattSaveStruct;
    T_BattSaveNewStruct tBattSaveNewStruct;
    CLEAR_STRUCT(tBattSaveStruct);
    CLEAR_STRUCT(tBattSaveNewStruct);

    tBattSaveStruct.bValid                  = True;
    tBattSaveStruct.fBattCap                = s_tBattDeal.fBattCap;
    tBattSaveStruct.fAddedLifeDecline       = s_tBattDeal.fAddedLifeDecline;
    tBattSaveStruct.wBattDischgTimes        = s_tBattDeal.wBattCycleTimes;
    tBattSaveStruct.tLastCalcCalenderTime   = s_tBattDeal.tLastCalcCalenderTime;
    tBattSaveStruct.fDischargeCapForNTimes  = s_tBattDeal.fDischargeCapForNTimes;
    tBattSaveStruct.fTotalDischargeCap      = s_tBattDeal.fTotalDischargeCap;
    tBattSaveStruct.fCalenderLifeDecline    = s_tBattDeal.fCalenderLifeDecline;
    tBattSaveStruct.fCycleLifeDecline       = s_tBattDeal.fCycleLifeDecline;
    tBattSaveStruct.ucBduType               = s_tBattDeal.ucBduType;
    //add by hp
    tBattSaveStruct.fMultiDischargeCap      = s_tBattDeal.fMultiDischargeCap;
    tBattSaveStruct.fMultiDischargePower    = s_tBattDeal.fMultiDischargePower;
    tBattSaveNewStruct.fMultiChargeCap      = s_tBattDeal.fMultiChargeCap;
    tBattSaveNewStruct.fMultiChargePower    = s_tBattDeal.fMultiChargePower;
    tBattSaveNewStruct.bCtrlLock            = s_tBattDeal.bCtlLock;
    WrEEPCap(&tBattSaveStruct);
    WrEEPCapNew(&tBattSaveNewStruct);
    return;
}


Static BOOLEAN DealCalenderDeclineWhenStart(void)
{
    INT32S slDiffDays = GetDiffDays(&s_tBattDeal.tLastCalcCalenderTime);

    if (slDiffDays > ONE_YEAR_DAYS || slDiffDays < 1)
    {
        return False;
    }

    for (INT32S i = 0; i < slDiffDays; i++)
    {
        // 起始时不知道电芯温度，以35度计算
        PlusCalenderLifeDecline(&s_tBattDeal, 35.0);
    }
    
    return True;
}


Static void InitBattSave(void)
{
    T_BCMDataStruct  tBcmData;
    T_BattSaveStruct tBattSaveStruct;
    T_BattSaveNewStruct tBattSaveNewStruct;
    T_DcrRecordStruct tDcrRecord;
    T_HisRecord tHisRecord;
    BYTE i;
    WORD wDcrRecordNum = 0;//已存储的直流内阻记录条数

#ifdef PAKISTAN_CMPAK_PROTOCOL
    T_CmpakSaveInfo     tCmpakSaveInfo;
#endif

    rt_memset(&tBattSaveNewStruct, 0, sizeof(tBattSaveNewStruct));
    rt_memset(&tBattSaveStruct, 0, sizeof(tBattSaveStruct));
    rt_memset(&tBcmData, 0, sizeof(T_BCMDataStruct));
    rt_memset_s(&tDcrRecord, sizeof(T_DcrRecordStruct), 0, sizeof(T_DcrRecordStruct));
    rt_memset_s(&tHisRecord,  sizeof(T_HisRecord), 0, sizeof(T_HisRecord));
#ifdef PAKISTAN_CMPAK_PROTOCOL
    rt_memset(&tCmpakSaveInfo, 0, sizeof(T_CmpakSaveInfo));
#endif

    GetRealData(&tBcmData ); // 先获得实时数据
#ifdef PAKISTAN_CMPAK_PROTOCOL
    readCmpakInfo(&tCmpakSaveInfo);
#endif
#ifndef KW_CHECK
    //读取累计放电电量异常的情况需要将累计充电信息也清零
    if (!RdEEPCap(&tBattSaveStruct))
    {
        rt_memset(&tBattSaveStruct, 0, sizeof(tBattSaveStruct));
        rt_memset(&tBattSaveNewStruct, 0, sizeof(tBattSaveNewStruct));
        tBattSaveStruct.fBattCap = 0.5 * s_tSysPara.wBatteryCap* g_ProConfig.fCapTransRate;
        GetTime(&tBattSaveStruct.tLastCalcCalenderTime);
        s_tBattDeal.bSave2eeprom    = True;
        tBattSaveStruct.ucBduType   = BDU_TYPE_SMART_LI;
    }
    //升级时由于没有累计充电数据，为了避免充放电数据相差过大，利用放电数据给充电数据一个基准值。
    else if (!RdEEPCapNew(&tBattSaveNewStruct))
    {
        tBattSaveNewStruct.fMultiChargeCap = tBattSaveStruct.fMultiDischargeCap;
        tBattSaveNewStruct.fMultiChargePower = tBattSaveStruct.fMultiDischargePower;
        s_tBattDeal.bSave2eeprom = True;
    }
#endif
#ifdef PAKISTAN_CMPAK_PROTOCOL
    s_tBattDeal.ulAccuBattRunTime       = tCmpakSaveInfo.ulAccuBattRunTime;
    s_tBattDeal.ulAccuDischTimes        = tCmpakSaveInfo.ulAccuDischTimes;
#endif
    //软件重启以后起来不置满，不做SOH修正
    s_tBattDeal.fAddedLifeDecline       = tBattSaveStruct.fAddedLifeDecline;
    s_tBattDeal.wBattCycleTimes         = tBattSaveStruct.wBattDischgTimes;
    s_tBattDeal.tLastCalcCalenderTime   = tBattSaveStruct.tLastCalcCalenderTime;
    s_tBattDeal.fDischargeCapForNTimes  = tBattSaveStruct.fDischargeCapForNTimes;
    s_tBattDeal.fTotalDischargeCap      = tBattSaveStruct.fTotalDischargeCap;
    s_tBattDeal.fCalenderLifeDecline    = tBattSaveStruct.fCalenderLifeDecline;
    s_tBattDeal.fCycleLifeDecline       = tBattSaveStruct.fCycleLifeDecline;
    s_tBattDeal.ucBduType               = tBattSaveStruct.ucBduType;

    //add by hp
    s_tBattDeal.fMultiDischargeCap      = tBattSaveStruct.fMultiDischargeCap;
    s_tBattDeal.fMultiDischargePower    = tBattSaveStruct.fMultiDischargePower;
    s_tBattDeal.bCtlLock                = tBattSaveNewStruct.bCtrlLock;
    if(tBattSaveNewStruct.fMultiChargeCap < FLT_MAX && tBattSaveNewStruct.fMultiChargePower < FLT_MAX) // 解决KW问题
    {
        s_tBattDeal.fMultiChargeCap         = tBattSaveNewStruct.fMultiChargeCap;
        s_tBattDeal.fMultiChargePower       = tBattSaveNewStruct.fMultiChargePower;
    }

    DealCalenderDeclineWhenStart();
    s_tBattDeal.fBattSOH                = CalcSOHFromDecline(&s_tBattDeal);
    s_tBattDeal.fBattCap                = MIN(tBattSaveStruct.fBattCap, g_ProConfig.fCapTransRate*(s_tBattDeal.fBattSOH / 100 * s_tSysPara.wBatteryCap)) ;

    // 初始化SOH和SOC及循环次数的实时数据
    tBcmData.wBattSOH           = (WORD)(s_tBattDeal.fBattSOH + 0.5);
    tBcmData.wBattSOC           = CalcSOCFromCap(&s_tBattDeal, s_tSysPara.wBatteryCap, 0);
    tBcmData.wBattCycleTimes    = s_tBattDeal.wBattCycleTimes;
    tBcmData.wBattHighPrecSOC   = CalcSOCFromCap(&s_tBattDeal, s_tSysPara.wBatteryCap, 2);//高精度SOC
    s_tBattOut.wBatSOC          = tBcmData.wBattSOC;  //added by xzx

    // 软件重启时读取DCR记录中的最后一条记录作为当前的DCR实时信息
    // 首先要确定是否有数据，然后读取的最后一条;
    wDcrRecordNum = GetDcrRecordTotNum();
    if(wDcrRecordNum > 0)
    {
        tHisRecord.pcFilename = "/dcrRecord";
        tHisRecord.wRecordSize = sizeof(T_DcrRecordStruct);
        tHisRecord.wReadPoint[MAIN_NORTH_PROTOCOL] = wDcrRecordNum - 1;
        tHisRecord.pucData = (rt_uint8_t *)&tDcrRecord;
        readOneRecord(&tHisRecord, MAIN_NORTH_PROTOCOL);
    }
    // 直流内阻记录总条数为0单体DCR均为初始化的0值
    for (i = 0; i < CELL_VOL_NUM; i++)
    {
        s_tBattOut.fCellDcr[i] = tDcrRecord.wCellDcr[i] / 100.0f;
    }
    // 读取保存的直流内阻异常告警/保护判断计数
    ReadDcrCnt(&s_tDcrCnt);
    s_tBattDeal.ulDcrTestPeriodCnt = s_tDcrCnt.ulDcrTestPeriodCnt;

    SetRealData(&tBcmData);

    CheckSaveEEPROM();
}


Static void DealOneMinuteTask(void)
{
    BattTimer(&s_tBattIn, &s_tBattDeal);
#ifdef PAKISTAN_CMPAK_PROTOCOL
    CalBattRunTimeAndDisChgTimes(&s_tBattIn, &s_tBattDeal);
#endif
    SyncTimeAndMode();
    CalcBattSOCAndSOH();
    CalcBattDsichargNPlus1Times();
    CheckBattEnableDate(&s_tBattIn);
    DealSlaveRotateTimeout(&s_tBattDeal);
    return;
}
static void AccumulateBattFullTimer(void)
{
    WORD wBattFullTimer = 0;
    if (s_tBattDeal.wFullChargeLastDura == 0)
    {//充满时间计时同步
        s_tBattDeal.wFullChargeLastDura = ReadBattFullTimer();
    }
    
    wBattFullTimer = s_tBattDeal.wFullChargeLastDura;

    TimerPlus(s_tBattDeal.wFullChargeLastDura, BATT_CHG_NOFULL_TIME );

    if(CalcSOCFromCap(&s_tBattDeal, s_tBattIn.tPara.wBattCap, 0) >= 99)
    {
        SetChargeNotFullOneWeek(False);
        s_tBattDeal.wFullChargeLastDura = 0;
    }
    if(TimeOut(s_tBattDeal.wFullChargeLastDura ,BATT_CHG_NOFULL_TIME ))
    {
        SetChargeNotFullOneWeek(True);
        s_tBattDeal.wFullChargeLastDura = 0;
    }
    
    if(wBattFullTimer != 0 && s_tBattDeal.wFullChargeLastDura % 60 == 0)
    {//充满时间计时触发非零值清零或是60的倍数时，掉电保存
        SaveBattFullTimer();
    }
    
    return;
}              //充满时间计时，B3新添加 R311没有

static WORD ReadBattFullTimer(void)
{
    LONG lReadRtn = 0;
    WORD wTmp = 0;
    T_BattFullStruct tBattFullInfo;
    rt_memset(&tBattFullInfo, 0, offsetof(T_BattFullStruct,wCheckSum)+2);

    lReadRtn = readFile("/BattFullInfo", (BYTE*)&tBattFullInfo, offsetof(T_BattFullStruct,wCheckSum)+2);
    if ( tBattFullInfo.wCheckSum != CRC_Cal((BYTE*)&tBattFullInfo, offsetof(T_BattFullStruct,wCheckSum)) || (lReadRtn != offsetof(T_BattFullStruct,wCheckSum)+2) )
    {
        rt_memset(&tBattFullInfo, 0, offsetof(T_BattFullStruct,wCheckSum)+2);//数据异常时直接清空
        writeFile("/BattFullInfo", (BYTE*)&tBattFullInfo, offsetof(T_BattFullStruct,wCheckSum)+2);       
    }
    wTmp = tBattFullInfo.wBattFullCounter;
    return wTmp;
}

static void SaveBattFullTimer(void)
{
    T_BattFullStruct tBattFullInfo;
    rt_memset(&tBattFullInfo, 0, offsetof(T_BattFullStruct,wCheckSum)+2);
    tBattFullInfo.wBattFullCounter = s_tBattDeal.wFullChargeLastDura;
    tBattFullInfo.wCheckSum = CRC_Cal((BYTE*)&tBattFullInfo, (offsetof(T_BattFullStruct,wCheckSum)));
    writeFile("/BattFullInfo", (BYTE*)&tBattFullInfo, offsetof(T_BattFullStruct,wCheckSum)+2);
    return;
}

static void CalcBattSOCAndSOH(void)
{
    CalcBattSOH(&s_tBattIn, &s_tBattDeal);
    AccumulateBattFullTimer();
    CalcSelfDischargeDecreaseCap();
    CalcAccumulateData();
    CalcBattLeftMinutes();
    CheckSaveEEPROM();//此处保存应在最后
    return;
}


static WORD GetSavedCellCycleTimes(void)
{
    T_BmsPACKManufactStruct tPackInfo;

    rt_memset_s(&tPackInfo, sizeof(T_BmsPACKManufactStruct), 0, sizeof(T_BmsPACKManufactStruct));
    readPackManufact(&tPackInfo);
    return tPackInfo.wCellCycleTimes;
}


/****************************************************************************
* 函数名称：InitBatt()
* 调    用：
* 被 调 用：
* 输入参数：无
* 返 回 值：无
* 功能描述：初始化电池数据
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
void InitBatt( void )
{
    T_BCMDataStruct     tBcmData;

    rt_memset_s( &s_tBattIn, sizeof(s_tBattIn), 0, sizeof(s_tBattIn));
    rt_memset_s( &s_tBattDeal, sizeof(T_BattDealInfoStruct), 0, sizeof(T_BattDealInfoStruct));
    rt_memset_s( &s_tBattOut, sizeof(s_tBattOut), 0, sizeof(s_tBattOut));
    rt_memset_s( &s_tHardwarePara, sizeof(T_HardwareParaStruct), 0, sizeof(T_HardwareParaStruct));
    rt_memset_s(&s_tDcrCnt, sizeof(T_DcrCntStruct), 0x00, sizeof(T_DcrCntStruct));
    rt_memset_s(&s_tBCMRealAlm, sizeof(T_BCMAlarmStruct), 0, sizeof(T_BCMAlarmStruct));
    rt_memset_s(&s_tBCMAlm, sizeof(T_BCMAlarmStruct), 0, sizeof(T_BCMAlarmStruct));
    readBmsHWPara(&s_tHardwarePara);
    rt_memcpy_s(&s_tBattIn.tData.tHardWareStruct, sizeof(T_HardwareParaStruct), &s_tHardwarePara, sizeof(T_HardwareParaStruct));
    GetSysPara(&s_tSysPara);
    GetRealData(&tBcmData);

    InitBattSave();

    s_bEnterDelayJudge              = True;  //初始化为混用场景时，需要延时一段时间，才能进行错峰 
    s_wCellCycleTimes               = GetSavedCellCycleTimes();
    s_tBattDeal.ucCellVoltNum       = s_tHardwarePara.ucCellVoltNum;
    s_tBattDeal.ucChargeMode        = BATT_MODE_DISCHARGE;
    s_tBattDeal.ucBatStatus         = 0xFF;
    s_tBattDeal.bPowerOff           = True;
    s_tBattDeal.tMinuteCalcTime     = GetTimeStamp();
    s_tBattDeal.bBattChargeFull     = False;
    s_tBattDeal.ucBduCtrlMode       = BDU_CTRL_NULL;
    s_tBattDeal.ucTransCounter      = TRANS_DELAY_MAX;
    s_tBattDeal.fDischargeLimitCurr = g_ProConfig.fMaxDischgCurr;
    s_tBattDeal.ucCtrlChgAndDischgDelay = 0;
    s_tBattDeal.bSourceInsufficientTimerStart = FALSE;
    s_tBattDeal.bActivateTry = TRUE;
#ifdef INTELLIGENT_PEAK_SHIFTING
    s_tBattDeal.ucElectricityPrice  = ELECTRICITY_PRICE_NULL;  //初始化时，电价模板为空
#endif

#ifdef CHARGE_THROUGH_ENABLED
    s_tBattDeal.bChargeThroughEnable   = True;
#else
    s_tBattDeal.bChargeThroughEnable   = False;
#endif

    s_tBattDeal.bVoltSwitchFlag     = False;
    s_tBattDeal.ucRotateMaxCurr     = SMART_LI_ROTATE_CURR_MAX;

#ifdef DEVICE_USING_R321
    if(s_tSysPara.fSelfAdaptionInitVolt >= 42 && s_tSysPara.fSelfAdaptionInitVolt <= 51) //初始电压可设置范围
    {
        s_fSelfAdaptionInitVolt = s_tSysPara.fSelfAdaptionInitVolt;
    }
#endif

    if(s_tSysPara.ucUsageScen == DISCHG_MODE_CONSTANT_VOLTAGE)
    {
        s_tBattOut.fSetDischVolt = s_tSysPara.fRemoteSupplyOutVolt;
        s_tBattDeal.fPowerDownVolt = s_tSysPara.fRemoteSupplyOutVolt;
    }
    else if(s_tSysPara.ucUsageScen == DISCHG_MODE_BATT_CHARACTERISTIC)
    {
        s_tBattOut.fSetDischVolt = tBcmData.fBattVolt*g_ProConfig.fVoltTrasRate;
        s_tBattOut.fSetDischVolt = GetValidData(s_tBattOut.fSetDischVolt, 56, 45);
        s_tBattDeal.fDischargeHopeVol = s_tBattOut.fSetDischVolt;
        s_tBattDeal.fPowerDownVolt = s_tBattOut.fSetDischVolt;
    }
    else if(s_tSysPara.ucUsageScen == DISCHG_MODE_SELF_ADAPTION || s_tSysPara.ucUsageScen == DISCHG_MODE_SOLAR)
    {
        s_tBattIn.tPara.fSysPowerOnThreshold        = MIX_INIT_POWER_ON_VOLT;
        s_tBattDeal.fPowerDownVolt                  = MIX_INIT_POWER_DOWN_VOLT;
        s_tBattDeal.bInitMixDischg                  = True;
        s_tBattOut.fSetDischVolt                    = s_fSelfAdaptionInitVolt;
    }
    ////以下参数后续都按照母排动态计算
    s_tBattDeal.wBusVoltNum = 0;
    s_tBattDeal.fBusVoltSum = 0.0;
    s_tBattDeal.fSysChgMaxCurr                  = (FLOAT)g_ProConfig.fRateCurrChgBus;
    InitRotate(False);
    s_ucFlagBattery = 0;
    return;
}

BYTE GetFlagBattery(void)
{
    return s_ucFlagBattery;
}


Static WORD CellTypeToCellCycleTimes(BYTE ucCellType, WORD wCellCycleTimes)
{
    WORD wConvertCycleTimes = 0;

    wConvertCycleTimes = wCellCycleTimes;
    if (ucCellType == CELL_TYPE_AUTO && wCellCycleTimes == 0) // 当电芯类型选择为自动时，如果没有配置电芯循环次数则取值3500次
    {
        wConvertCycleTimes = 3500;
    }
    else if (ucCellType == CELL_TYPE_1) // 如果电芯类型选择为电芯1，则电芯循环次数为3500次
    {
        wConvertCycleTimes = 3500;
    }
    else if (ucCellType == CELL_TYPE_2) // 如果选择电芯类型选择电芯2，则电芯循环次数为2000
    {
        wConvertCycleTimes = 2000;
    }
    else if (ucCellType == CELL_TYPE_3) // 如果选择电芯类型选择电芯3，则电芯循环次数为3500
    {
        wConvertCycleTimes = 3500;
    }
    else if (ucCellType == CELL_TYPE_4) // 如果选择电芯类型选择电芯4，则电芯循环次数为6000
    {
        wConvertCycleTimes = 6000;
    }

    if (CheckCellCycleTimesValid(wConvertCycleTimes) == False)
    {
        wConvertCycleTimes = CYCLE_TIMES_MAX;
    }

    return wConvertCycleTimes;
}

static WORD CalCellCycleTimes(void)
{
    BYTE ucCellType = CELL_TYPE_AUTO;
    static BYTE s_ucLastCellType = CELL_TYPE_AUTO;
#ifdef GET_NUMOFCELL_AND_TYPEOFBMS_ENABLED
    ucCellType = s_tSysPara.ucCellType;
#else
    ucCellType = CELL_TYPE_AUTO;
#endif
    if(ucCellType == CELL_TYPE_AUTO && ucCellType != s_ucLastCellType)
    {
        s_wCellCycleTimes = GetSavedCellCycleTimes();
    }
    s_ucLastCellType = ucCellType;
    return CellTypeToCellCycleTimes(ucCellType, s_wCellCycleTimes);
}


Static BOOLEAN IsCellVoltLow(T_StateListenStruct* ptState, BYTE i)
{
    FLOAT fDelta = 0.0;

    if(i>=CELL_VOL_NUM_MAX)
    {
        return False;
    }

    if(ptState->bState)
    {
        fDelta = 0.1f;
    }

    return s_tBattIn.tData.afCellVolt[i]<=(CELL_VOL_LOW_CTRL + fDelta);
}

Static BOOLEAN IsCellVoltTooLow(T_StateListenStruct* ptState, BYTE i)
{
#define CELL_VOL_TOO_LOW_CTRL (2.5f)

    FLOAT fDelta = 0.0;

    if(i>=CELL_VOL_NUM_MAX)
    {
        return False;
    }

    if(ptState->bState)
    {
        fDelta = 0.1f;
    }

    return s_tBattIn.tData.afCellVolt[i]<=(CELL_VOL_TOO_LOW_CTRL + fDelta);
}

Static BOOLEAN IsCellUnderVolt(T_StateListenStruct* ptState, BYTE i)
{
    if(i>=CELL_VOL_NUM)
    {
        return False;
    }

    //满足激活口条件 或者 激活回路电流异常不进行强补电
#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
    if(s_tBattDeal.bActivateStart == TRUE || s_tBattDeal.bActivatePortCurrError == TRUE || (GetCan2ConnectedFlag() && IsMaster()))
#else
    if(s_tBattDeal.bActivateStart == TRUE || s_tBattDeal.bActivatePortCurrError == TRUE )
#endif    
    {
        return FALSE;
    }

    return s_tBattIn.tData.fBusVol > SMART_LI_SUPPLE_VOL \
          && fabs(s_tBattIn.tData.fBattCurr) < s_tBattIn.tData.fCurrMinDet \
          && s_tBattIn.tData.afCellVolt[i] <= 2.8f \
          &&s_tBattIn.tData.bBattLowVoltPrt;// s_tBattIn.tData.bCellUnderVoltPrt;条件单体欠压改为任一欠压
}

Static BOOLEAN IsCellVoltHigh(T_StateListenStruct* ptState, BYTE i)
{
#define CELL_VOLT_HIGH_THRESHOLD (3.65f)

    FLOAT fDelta = 0.0;
    
    if(i>=CELL_VOL_NUM_MAX)
    {
        return False;
    }

    if(ptState->bState)
    {
        fDelta = -0.15f;
    }

    return s_tBattIn.tData.afCellVolt[i]>=(CELL_VOLT_HIGH_THRESHOLD + fDelta);
}

Static BOOLEAN IsCellFull(T_StateListenStruct* ptState, BYTE i)
{
    if(s_tBattDeal.ucChargeMode != BATT_MODE_CHARGE)
    {
        return False;
    }

    if(i>=CELL_VOL_NUM_MAX)
    {
        return False;
    }
    
    return s_tBattIn.tData.afCellVolt[i]>=s_tBattIn.tPara.fCellChargeFullVolt;
}

Static void CheckCellVoltStat(void)
{
    BYTE  i=0;
    s_tBattDeal.bCellVoltTooLow       = False;
    s_tBattDeal.bCellVoltLow          = False;
    s_tBattDeal.bCellVoltHigh         = False;
    s_tBattDeal.bCellFull             = False;

    for(i=0; i<s_tHardwarePara.ucCellVoltNum; i++)
    {
        InitListener(&s_tBattDeal.atCellVoltTooLow[i], IsCellVoltTooLow, CELL_CTRL_CHECK_TIMES);
        ////每个单节的状态都要运行，不用break
        if (RunListener(&s_tBattDeal.atCellVoltTooLow[i], i))
        {
            s_tBattDeal.bCellVoltTooLow = True;
        }

        InitListener(&s_tBattDeal.atCellVoltLow[i], IsCellVoltLow, CELL_CTRL_CHECK_TIMES);
        ////每个单节的状态都要运行，不用break
        if (RunListener(&s_tBattDeal.atCellVoltLow[i], i))
        {
            s_tBattDeal.bCellVoltLow = True;
        }

        InitListener(&s_tBattDeal.atCellVoltHigh[i], IsCellVoltHigh, CELL_VOL_HIGH_CHECK_TIMES);
        ////每个单节的状态都要运行，不用break
        if (RunListener(&s_tBattDeal.atCellVoltHigh[i], i))
        {
            s_tBattDeal.bCellVoltHigh = True;
        }

        InitListener(&s_tBattDeal.atCellFull[i], IsCellFull, CELL_VOL_HIGH_CHECK_TIMES);
        ////每个单节的状态都要运行，不用break
        if (RunListener(&s_tBattDeal.atCellFull[i], i))
        {
            s_tBattDeal.bCellFull = True;
        }

        InitListener(&s_tBattDeal.atCellVoltHighLock[i], IsCellVoltHighLock, CELL_VOLT_HIGH_LOCK_TIMES);
        ////每个单节的状态都要运行，不用break
        if (RunListener(&s_tBattDeal.atCellVoltHighLock[i], i))
        {
            s_tBattDeal.bCellVoltHighLock = True;
        }

        InitListener(&s_tBattDeal.atCellVoltLowLock[i], IsCellVoltLowLock, CELL_VOLT_LOW_LOCK_TIMES);
        ////每个单节的状态都要运行，不用break
        if (RunListener(&s_tBattDeal.atCellVoltLowLock[i], i))
        {
            s_tBattDeal.bCellVoltLowLock = True;
        }

        if(!GetQtptestFlag())
        {
            if(!s_tBattDeal.bCellUnderVolt)
            {
                InitListener(&s_tBattDeal.atCellUnderVolt[i], IsCellUnderVolt, 15);
                ////每个单节的状态都要运行，不用break
                if (RunListener(&s_tBattDeal.atCellUnderVolt[i], i))
                {
                    //强补电条件
                    CellUnderVoltCondition();
                }
            }
            else if(s_tBattDeal.bActivateStart == TRUE || s_tBattDeal.bActivatePortCurrError == TRUE    //满足激活口条件 或者 激活回路电流异常不进行强补电
                    ||s_tBattDeal.ucCellUnderVoltTimer >= CELL_UNDER_VOLT_MIN)    //强补电计时
            {
                s_tBattDeal.bCellUnderVolt = False;
            }
        }
        else
        {
            s_tBattDeal.ucCellUnderVoltTimer = 0;
            s_tBattDeal.bCellUnderVolt = False;
        }
    }

    return;
}

Static void CellUnderVoltCondition(void)
{
    //如果没有充电保护，可以进行强补电；
    if(s_tBattIn.tData.bChargeProtect == False)
    {
        s_tBattDeal.bCellUnderVolt = True;
    }
#ifdef BDU_CONNECTOR_TEMP_HIGH_RECOVER_ENABLE
    //如果是由可以强补电的告警的充电保护，也可以进行强补电。
    else if(s_tBattIn.tData.bChgPrtByCanSupplyAlm == True)
    {
        s_tBattDeal.bCellUnderVolt = True;
    }
#endif
}

BOOLEAN GetCellVoltStat(void)
{
    return s_tBattDeal.bCellUnderVolt;
}

//回路异常恢复 数据恢复初始状体
BOOLEAN LoopOffRestore(void)
{
    s_tBattDeal.wLoopOffCounter = 0;
    s_tBattDeal.bLoopOff = False;
    s_ucJudgeIfLoopFault = NO_LOOP_FAULT;
    s_ucLoopOffStatus = LOOP_OFF_NO;
    s_wNoOutputCurrTimer = 0;

    return TRUE;
}

Static BOOLEAN IsLoopOff(void)
{
    // if (s_tBattIn.tData.fBusVolMax <= (s_tBattIn.tData.fBusVol + 1.0f))
    // {
    //     return False;
    // }

    // if (s_tBattDeal.bPowerOff)
    // {
    //     return False;
    // }

    // if (fabs(s_tBattIn.tData.fMaxCurrInAll) < MIN_CURR_DET_BDCU)
    // {
    //    return False;
    // }

    // if (s_tBattIn.tData.bChargeProtect || s_tBattIn.tData.bBduChgDisable)
    // {
    //     return False;
    // }

    if (s_tBattIn.tData.bBduChargeBreak || IfBduDischarge())
    {
        if (fabs(s_tBattIn.tData.fMaxCurrInAll) < MIN_CURR_DET_BDCU)
        {
            if(s_bExternalPowerOnFlag && !GetNativePowerOnStatus())
            {
                if(s_ucJudgeIfLoopFault == NO_LOOP_FAULT)
                {
                    s_ucJudgeIfLoopFault = DECREASE_VOLT_LOOP_FALUT_MOMENT;
                    s_tBattDeal.wLoopOffCounter = 0;
                }
                s_ucLoopOffStatus = LOOP_OFF_CHARGE;
                return True;
            }

            s_ucLoopOffStatus = LOOP_OFF_NO;
            s_ucJudgeIfLoopFault = NO_LOOP_FAULT;
            s_tBattDeal.wLoopOffCounter = 0;
            return False;
        }

        /*在判断的过程中，如果本机外部有电为 否，其他电池外部有电为 是，突然交流断电，所有电池开始正常放电有电流，
          要重新判断，之前的计数全部清零*/
        if(s_ucJudgeIfLoopFault == DECREASE_VOLT_LOOP_FALUT_MOMENT)
        {
            s_ucJudgeIfLoopFault = NO_LOOP_FAULT;
            s_tBattDeal.wLoopOffCounter = 0;
        }
        s_ucLoopOffStatus = LOOP_OFF_DISCHARGE;
        return True;
    }
    else
    {
        /*充电和在线非浮充模式下，充电输入断为否，外部有电状态为是，功率线必定是连接正常的
          ,因此不会出现回路异常，所有标志位和计数器清零,解决回路异常产生后，不消失问题*/
        s_tBattDeal.wLoopOffCounter = 0;
        s_tBattDeal.bLoopOff = False;
        s_ucJudgeIfLoopFault = NO_LOOP_FAULT;
        s_wNoOutputCurrTimer = 0;
        s_ucLoopOffStatus = LOOP_OFF_NO;
    }

    return False;
}

/*检查回路异常*/
Static void CheckLoopOff(void)
{
    BOOLEAN bLoopoff = FALSE;
    // 检查电池电流、母线电压最大值和单电池状态
    if (fabs(s_tBattIn.tData.fBattCurr) > MIN_CURR_DET_BDCU
        || s_tBattIn.tData.fBusVolMax < OUT_VOLT_MIN  // 版本兼容，FB50做主机不判断
        || isSingleBatt())
    {
        s_tBattDeal.wLoopOffCounter = 0;
        s_tBattDeal.bLoopOff = False;
        s_ucJudgeIfLoopFault = NO_LOOP_FAULT;
        s_wNoOutputCurrTimer = 0;
        s_ucLoopOffStatus = LOOP_OFF_NO;
    }
    else if (IsLoopOff())
    {
        switch (s_ucLoopOffStatus)
        {
            case LOOP_OFF_DISCHARGE:
                TimerPlus(s_tBattDeal.wLoopOffCounter, LOOPOFF_COUNTER_DISCHG_MAX);
                bLoopoff = (s_tBattDeal.wLoopOffCounter >= LOOPOFF_COUNTER_DISCHG_MAX);
                break;
            case LOOP_OFF_CHARGE:
                TimerPlus(s_tBattDeal.wLoopOffCounter, LOOPOFF_COUNTER_CHG_MAX);
                bLoopoff = (s_tBattDeal.wLoopOffCounter >= LOOPOFF_COUNTER_CHG_MAX);
                break;
            default:
                break;
        }

        if (bLoopoff)
        {
            // 调整当前电池的放电输出电压为当前母排电压加1V，并设置标志位
            if (s_ucJudgeIfLoopFault == NO_LOOP_FAULT)
            {
                s_ucJudgeIfLoopFault = INCREASE_VOLT_LOOP_FAULT;
            }
            else if (s_ucJudgeIfLoopFault == DECREASE_VOLT_LOOP_FALUT_MOMENT)
            {
                s_ucJudgeIfLoopFault = DECREASE_VOLT_LOOP_FAULT;
            }

            if (TimeOut(s_wNoOutputCurrTimer, 120))
            {
                s_tBattDeal.bLoopOff = True;
            }
        }
    }

    if (s_tBCMRealAlm.ucLoopFault)
    {
        TimerPlus(s_tBattDeal.wLoopOffShutDownCounter, LOOPOFF_SHUTDOWN_COUNTER_MAX);
    }
    else
    {
        s_tBattDeal.wLoopOffShutDownCounter = 0;
    }
    return;
}

Static BOOLEAN IsCellTempHigh(T_StateListenStruct* ptState, BYTE i)
{
    FLOAT fDelta = 0.0;
    FLOAT fThreshold = s_tSysPara.fChgTempHighAlmThre;//CELL_TEMP_HIHT_CTLR;

    if(i>=CELL_TEMP_NUM_MAX)
    {
        return False;
    }

    if(BATT_MODE_CHARGE != s_tBattDeal.ucChargeMode)
    {
        return False;
    }
    
    if(ptState->bState)
    {
        fDelta = -3.0;
    }

    return s_tBattIn.tData.afCellTemp[i]>=(fThreshold + fDelta);

}

Static BOOLEAN IsCellTempTooHigh(T_StateListenStruct* ptState, BYTE i)
{
    FLOAT fDelta = 0.0;
    FLOAT fThreshold = CELL_TEMP_TOO_HIHT_CTLR;

    if(i>=CELL_TEMP_NUM_MAX)
    {
        return False;
    }

    if(BATT_MODE_CHARGE != s_tBattDeal.ucChargeMode)
    {
        return False;
    }
    
    if(ptState->bState)
    {
        fDelta = -2.0;
    }

    return s_tBattIn.tData.afCellTemp[i]>=(fThreshold + fDelta);

}

Static BOOLEAN IsCellTempLow(struct T_StateListen* ptState, BYTE i)
{
    FLOAT fDelta = 0.0;
    FLOAT fThreshold = CELL_TEMP_LOW_CTLR;

    if(i>=CELL_TEMP_NUM_MAX)
    {
        return False;
    }

    if(BATT_MODE_CHARGE != s_tBattDeal.ucChargeMode)
    {
        return False;
    }

    if(ptState->bState)
    {
        fDelta = 2.0;
    }

    return s_tBattIn.tData.afCellTemp[i]<=(fThreshold + fDelta);
}

Static void CheckCellTempStat(void)
{
    BYTE i=0;
    s_tBattDeal.bCellTempLow        = False;
    s_tBattDeal.bCellTempHigh       = False;
    s_tBattDeal.bCellTempTooHigh    = False;

    for(i=(BYTE)0; i<s_tHardwarePara.ucCellTempNum; i++)
    {
        InitListener(&s_tBattDeal.atCellTempLow[i], IsCellTempLow, CELL_CTRL_CHECK_TIMES);
        InitListener(&s_tBattDeal.atCellTempHigh[i], IsCellTempHigh, CELL_CTRL_CHECK_TIMES);
        InitListener(&s_tBattDeal.atCellTempTooHigh[i], IsCellTempTooHigh, CELL_CTRL_CHECK_TIMES);

        if (RunListener(&s_tBattDeal.atCellTempLow[i], i))
        {
            s_tBattDeal.bCellTempLow = True;
        }

        if (RunListener(&s_tBattDeal.atCellTempHigh[i], i))
        {
            s_tBattDeal.bCellTempHigh = True;
        }

        if (RunListener(&s_tBattDeal.atCellTempTooHigh[i], i))
        {
            s_tBattDeal.bCellTempTooHigh = True;
        }
    }
    return;
}

Static BOOLEAN IsBattVoltHigh(T_StateListenStruct* ptState, BYTE i)
{
    #define Batt_VOLT_HIGH_THRESHOLD (53.0f)
    float fBattVoltHighThre = s_tHardwarePara.ucCellVoltNum * (Batt_VOLT_HIGH_THRESHOLD / 15);
    if(s_tBattDeal.ucChargeMode != BATT_MODE_CHARGE)
    {
        return False;
    }
    
    if(ptState->bState)
    {
        return True;
    }

    return s_tBattIn.tData.fBatVol >= fBattVoltHighThre;
}

Static void CheckBattVoltStat(void)
{
    BYTE  i=0;

    s_tBattDeal.bBattVoltHigh = False;

    InitListener(&s_tBattDeal.atBattVoltHigh, IsBattVoltHigh, BATT_CTRL_CHECK_TIMES);

    if (RunListener(&s_tBattDeal.atBattVoltHigh, i))
    {
        s_tBattDeal.bBattVoltHigh = True;
    }

    return;
}

void InitSetCurrAdjust(void)
{
    s_tBattDeal.bChargeCurrMin      = FALSE;
    s_tBattDeal.bChargeCurrMinBak   = FALSE;
    s_tBattDeal.bChargeCurrEnough   = FALSE;
    s_tBattDeal.bSourceInsufficientTimerStart = FALSE;
    s_tBattDeal.wChargeModeChangeTimeDiff = 0;
    s_tBattDeal.ucChargeModeChangeCount = 0;
    s_tBattDeal.wSourceInsufficientCount = 0;
//    ClearLimitCurrent();
}

#ifdef INTELLIGENT_PEAK_SHIFTING
BOOLEAN IsNorthCommFail(void)
{
   return (RUN_MODE_CONTROLLED == s_tBattIn.tPara.ucRunMode && JudgeCommDisconnect());
}
#endif

// static void Judge90sShutdown(void)
// {
//     ////剔除单板避免产线单板无法升级
//     if ( !GetQtptestFlag() && (!IsUpdate()) &&
//          s_tBattIn.tData.fCellVoltMax >= 1.5f &&
//          s_tBattIn.tData.fBatVol > 20.0f &&
//          s_tBattIn.tData.fSysVol < (PMOS_RESTORE_VOL - 1) &&
//          (s_tBattIn.tData.bBattLowVoltPrt || s_tBattIn.tData.bCellDemage) && !s_tBattDeal.bCellUnderVolt
//        )
//     {
//         if((CELL_PROTECT_DELAY - 1) == s_tBattDeal.ucCellProtectCounter)
//         {
//             U_Int ucActId ;
//             SaveHisOpID(&ucActId, CONTOL_SHUTDOWN);
//             SaveAction(ucActId.iData, "90S Protect Shutdown");
//             SaveBattInfo();
//             SaveHisPoint();
//             saveResetData(RESET_REASON, NO_RESET_SHUTDOWN);
//         }
//         TimerPlus(s_tBattDeal.ucCellProtectCounter, CELL_PROTECT_DELAY);
//     }
//     else if(!TimeOut(s_tBattDeal.ucCellProtectCounter, CELL_PROTECT_DELAY))
//     {
//         s_tBattDeal.ucCellProtectCounter = 0;
//     }
// }

/****************************************************************
函数名：LoadBattDeal
入口参数：
出口参数：
功能：载入电池处理信息
****************************************************************/
Static void LoadBattDeal( void )
{
    /*
    todo:
    更新部分能直接更新的BattDeal结构中的内容
    保证这部分的调用只在BatteryManagement中
     */
    time_t tTimeNow = GetTimeStamp();
    GetSysPara(&s_tSysPara);
    GetRealAlarm(BCM_ALARM_REAL, (BYTE *)&s_tBCMRealAlm);
    GetRealAlarm(BCM_ALARM, (BYTE *)&s_tBCMAlm);

    CheckCellTempStat();
    CheckCellVoltStat();
    CheckBattVoltStat();
    CheckBattLockState();
    CalBattChargeMinutes(&s_tBattIn, &s_tBattDeal);

    GetValueFromProtocol(INFO_TYPE_OUT_VOLT, &s_tBattDeal.tOutputVoltByCtrl);
    GetValueFromProtocol(INFO_TYPE_CHG_COEF, &s_tBattDeal.tChgCurrCeofByCtrl);
    GetValueFromProtocol(INFO_TYPE_DISCH_VOLT_FREE, &s_tBattDeal.tOutputVoltNonCtrl);
    GetValueFromProtocol(INFO_TYPE_CHG_COEF_FREE, &s_tBattDeal.tChgCurrCeofNonCtrl);
    GetValueFromProtocol(INFO_TYPE_SET_MAX_SOC, &s_tBattDeal.tMaxSocCtrl);

    TimerPlus(s_tBattDeal.ucCtrlChgAndDischgDelay, CTRL_CHG_DISCHG_DELAY);
    TimerPlus(s_tBattDeal.ucTransCounter, TRANS_DELAY_MAX);
    if (IsMaster())
    {
        SmartLi_IsPowerDown_oper = SmartLi_IsPowerDown_Master;
        startDischgOper = startDischg_Master;
        RotateMaster();
    }
    else
    {
        SmartLi_IsPowerDown_oper = SmartLi_IsPowerDown_Slave;
        startDischgOper = startDischg_Slave;
        RotateSlave();
    }

    s_tBattDeal.bPowerOff           = SmartLi_IsPowerDown();

    // if (s_tBattDeal.bPowerOff != s_bJudgePowerOnOrOffFlag)
    // {
    //     s_bJudgePowerOnOrOffFlag = s_tBattDeal.bPowerOff;
    //     if(s_bJudgePowerOnOrOffFlag)
    //     {
    //         s_tBattDeal.ucPeakDichgNum = 0;
    //         GetTime(&s_tPowerOffTime);
    //     }
    // }
    #ifdef INTELLIGENT_PEAK_SHIFTING 
    PowerOffPeakShiftJudge();
    #endif

    s_tBattDeal.bBattDischarge  = (s_tBattIn.tData.fBattTotalCurr < -1.0*s_tBattIn.tData.fCurrMinDet);
    s_tBattDeal.bCellOverVolPrt     = GetOverPrtAlm(&s_tBCMRealAlm);
    s_tBattDeal.bRealCellOverVolPrt     = GetRealOverPrtAlm(&s_tBCMAlm);
    s_tBattDeal.fChargeCurrHopeBak = s_tBattDeal.fChargeCurrHope;
    s_tBattDeal.fBatteryAverageVol = CalcOutVolFromBattVol();
    s_tBattDeal.bCellLowVolPrt     = GetLowPrtAlm(&s_tBCMAlm);
    s_tBattDeal.bBattOverVolPrt     = GetBattOverPrtAlm(&s_tBCMRealAlm);
    s_tBattDeal.bBattOverCurrPrt    = GetBattOverCurrPrtAlm(&s_tBCMAlm);
    s_tBattDeal.ucBduCtrlMode       = BDU_CTRL_NULL;
    s_tBattDeal.fBattCapBak         = s_tBattDeal.fBattCap;

    if (s_tBattDeal.bBattDischarge != s_tBattDeal.bBattDischargeBak)
    {
        s_tBattDeal.bBattDischargeBak = s_tBattDeal.bBattDischarge;
        if (s_tBattDeal.bBattDischarge)
        {
            s_tBattDeal.bDischargeFromFull      = s_tBattDeal.bBattChargeFull;
            s_tBattDeal.fDischargeCapForCelebrate   = 0.0;
            s_tBattDeal.bBattChargeFull = False;
        }
    }

    s_tBattDeal.ulSecondTimeDiffSec = tTimeNow - s_tBattDeal.tSecondCalcTime;
    s_tBattDeal.tSecondCalcTime = tTimeNow;
#ifdef UNITEST
    s_tBattDeal.tSecondCalcTime = 1;
#endif
    if ((s_tBattDeal.ulSecondTimeDiffSec > 3) || (s_tBattDeal.ulSecondTimeDiffSec == 0))
    {
        s_tBattDeal.ulSecondTimeDiffSec = 1;
    }

    IfConditionOkNTimes(BATT_MODE_DISCHARGE == s_tBattDeal.ucChargeMode, &s_tBattDeal.wNoChargeCounter, CHARGE_COUNTER_TIMEOUT);

    s_tBattDeal.bChargeModeChange = False;

    if (s_tBattDeal.wNoChargeCounter >= CHARGE_COUNTER_TIMEOUT)
    {
        s_tBattDeal.ucChargeCounter = 0;
    }
    else if (s_tBattDeal.ucBatStatus == BATT_MODE_DISCHARGE &&
        (BATT_MODE_CHARGE == s_tBattDeal.ucChargeMode||BATT_MODE_STANDBY == s_tBattDeal.ucChargeMode)
        )
    {
        s_tBattDeal.bChargeModeChange = True;
    }

    s_tBattDeal.bMasterChgFull = IfMasterChgFull(s_tBattDeal.ucChargeMode);

    if(s_tBattDeal.bSourceInsufficientTimerStart)
    {
        TimerPlus(s_tBattDeal.wSourceInsufficientCount, TEN_MINUTES_PER_SECONDS);
    }

#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
    SetExternalPowerOnExistFlag(s_bExternalPowerOnFlag, s_tBattIn.tData.bB3BattExist);
#else
    SetExternalPowerOnExistFlag(s_bExternalPowerOnFlag);
#endif
    s_wCellCycleTimes = CalCellCycleTimes();

    return;
}

/* 获取停电状态 */
BOOLEAN GetPowerOffStatus(void)
{

     s_tBattDeal.bPowerOff = SmartLi_IsPowerDown();
    return s_tBattDeal.bPowerOff;
}

FLOAT clacDischCurr(void)
{
    FLOAT fCurr,fSetCurr;
    fCurr = g_ProConfig.fDischgCurrCoef;
    if (s_tBattIn.tData.fCellAverageTemp < (FLOAT)0 && !GetApptestFlag())
    {
        s_tBattIn.tData.fCellAverageTemp = MAX(s_tBattIn.tData.fCellAverageTemp, -20.0f);
        if(s_tBattIn.tData.fCellAverageTemp > -10.0f)
        {
            fCurr += s_tBattIn.tData.fCellAverageTemp * TEMP_LOW_RATEDEC_PERDEGREE;
        }
        else
        {
            fCurr = GetLowTempDischgCurr(s_tBattIn.tData.fCellAverageTemp);
        }
    }
    fCurr = MIN(s_tSysPara.fDischgMaxCurr, fCurr);
    fSetCurr = fCurr * s_tBattIn.tPara.wBattCap;                //D121 控制母排测
    fSetCurr = fSetCurr <= (float)g_ProConfig.fMaxDischgCurr ? fSetCurr : g_ProConfig.fMaxDischgCurr;
    return fSetCurr;
}


Static BOOLEAN JudgeCellChargeFullVolt(FLOAT fCellChargeFullVolt)
{
    /* 单体充电截止电压大于3.5V时，充电map中的3.65V和单体充电截止电压应保持一致。
       如果单体充电截止电压参数修改，则map表中的3.5~3.65，实际应按照3.5~单体充电截止电压 区间限流0.1C */
    if(fCellChargeFullVolt > 3.5)
    {
        ChangeVoltTempCurrTable(fCellChargeFullVolt);
        return SUCCESSFUL;
    }
    return FAILURE;
}



#ifdef SMOOTH_CHARGE_LIMIT_CURRENT
static BYTE s_aucFlag[CELL_VOL_NUM_MAX] = {0};    // 充电时单体电压大于3.5V时回差标志
Static BYTE GetMapIndex(void)
{
#ifdef GET_NUMOFCELL_AND_TYPEOFBMS_ENABLED
    if (s_tSysPara.ucCellType == CELL_TYPE_AUTO)
#endif
    {
        if (s_tSysPara.wBatteryCap == (150 / (WORD)g_ProConfig.fCapTransRate)) // 电芯类型选择为自动，并且电池容量为150AH
        {
            return CHARGE_MAP_HIGH;
        }
        else if ((s_tSysPara.wBatteryCap == (100 / (WORD)g_ProConfig.fCapTransRate)) && (s_wCellCycleTimes < 3000)) // 电芯类型选择为自动，并且容量为100AH，并且电芯循环次数小于3000
        {
            return CHARGE_MAP_LOW;
        }
    }
#ifdef GET_NUMOFCELL_AND_TYPEOFBMS_ENABLED
    else if ((s_tSysPara.ucCellType == CELL_TYPE_1) || (s_tSysPara.ucCellType == CELL_TYPE_4)) // 电芯类型选择为电芯1或电芯4
    {
        return CHARGE_MAP_HIGH;
    }
    else if (s_tSysPara.ucCellType == CELL_TYPE_2) // 电芯类型选择为电芯2
    {
        return CHARGE_MAP_LOW;
    }
#endif

    return CHARGE_MAP_DEFAULT; // 其它采用默认map
}

FLOAT GetChargeLimitCurrent(void)
{
    BYTE i = 0, ucIndex = 0;
    BYTE ucMap = GetMapIndex();
    FLOAT fMinCurr = 1.0f;
    FLOAT afCellCurr[CELL_TEMP_NUM_MAX] = {0.0f};
    FLOAT afCellVoltTem[CELL_VOL_NUM_MAX] = {0.0f};

    for (i = 0; i < s_tHardwarePara.ucCellVoltNum; i++)
    {
        if (s_aucFlag[i] && s_tBattIn.tData.afCellVolt[i] < 3.5f)
        {
            afCellVoltTem[i] = s_tBattIn.tData.afCellVolt[i] + 0.2f;
            afCellVoltTem[i] = (afCellVoltTem[i] >= 3.5f) ? 3.51f : s_tBattIn.tData.afCellVolt[i];
        }
        else
        {
            afCellVoltTem[i] = s_tBattIn.tData.afCellVolt[i];
        }

        s_aucFlag[i] = (afCellVoltTem[i] >= 3.5f) ? 1 : 0;
    }

    JudgeCellChargeFullVolt(s_tBattIn.tPara.fCellChargeFullVolt);

    for (i = 0; i < s_tHardwarePara.ucCellTempNum; i++)
    {
#ifdef USE_D121_CHARGE_MAP
        if (s_tHardwarePara.ucCellTempNum == 4) // 每个电芯计算一次，第1个温度对应1，2节，依次往后（D121是8个电芯电压，4个电芯温度）
        {
            ucIndex = 2 * i;
            if ((ucIndex + 1) < (sizeof(afCellCurr) / sizeof(FLOAT)))  // Coverity
            {
                afCellCurr[ucIndex]   = VoltTempLinearInsert(ucMap, afCellVoltTem[ucIndex], s_tBattIn.tData.afCellTemp[i]);
                afCellCurr[ucIndex+1] = VoltTempLinearInsert(ucMap, afCellVoltTem[ucIndex+1], s_tBattIn.tData.afCellTemp[i]);
            }
        }
#else
        if (s_tHardwarePara.ucCellTempNum == 4)  // 每个电芯计算一次，第1个温度对应1，2，3，4节，依次往后
        {
            ucIndex = 4 * i;
            if ((ucIndex + 3) < (sizeof(afCellCurr) / sizeof(FLOAT)))  // Coverity
            {
                afCellCurr[ucIndex]   = VoltTempLinearInsert(ucMap, afCellVoltTem[ucIndex], s_tBattIn.tData.afCellTemp[i]);
                afCellCurr[ucIndex+1] = VoltTempLinearInsert(ucMap, afCellVoltTem[ucIndex+1], s_tBattIn.tData.afCellTemp[i]);
                afCellCurr[ucIndex+2] = VoltTempLinearInsert(ucMap, afCellVoltTem[ucIndex+2], s_tBattIn.tData.afCellTemp[i]);
                afCellCurr[ucIndex+3] = VoltTempLinearInsert(ucMap, afCellVoltTem[ucIndex+3], s_tBattIn.tData.afCellTemp[i]);
            }
        }
#endif
        else  // 每个电芯温度对应一个电芯电压
        {
            afCellCurr[i] = VoltTempLinearInsert(ucMap, afCellVoltTem[i], s_tBattIn.tData.afCellTemp[i]);
        }
    }

    for (i = 0; i < s_tHardwarePara.ucCellVoltNum; i++)
    {
        if (fMinCurr > afCellCurr[i])
        {
            fMinCurr = afCellCurr[i];
        }
    }

    if (fMinCurr >= 1.0f)  // 1C充电时，直接给1.5C，避免影响功率，达不到1C充电
    {
        fMinCurr = 1.5f;
    }

    return GetValidData((fMinCurr * s_tBattIn.tPara.wBattCap * g_ProConfig.fCapTransRate), g_ProConfig.fRateCurrChg, 0);
}
#endif


Static void DealChargeCurrSlowUp(FLOAT fTargetCurr)
{
    if(fTargetCurr < SMART_LI_RATE_CURRENT_CHG_SLOW || (fTargetCurr - s_tBattDeal.fChargeCurrHope) < SMART_LI_RATE_CURRENT_CHG_SLOW)
    {
         s_tBattDeal.fChargeCurrHope =  fTargetCurr;
         return;
    }

    if(s_tBattDeal.fChargeCurrHope < fTargetCurr && !GetApptestFlag() && s_tBattDeal.ucBatStatus == BATT_MODE_CHARGE)
    {
         if(s_tBattDeal.wChargeCurrCounter >= SMART_LI_RATE_TIME_CHG_SLOW )
         {
            s_tBattDeal.wChargeCurrCounter = 0;
            s_tBattDeal.fChargeCurrHope += SMART_LI_RATE_CURRENT_CHG_SLOW;
         }
         else
         {
            s_tBattDeal.wChargeCurrCounter ++;
         }
    }
    else
    {
        s_tBattDeal.wChargeCurrCounter = 0;
    }
    return;
}

void SmartLi_CalcChargeHopeCurr(void)
{
    FLOAT fChargeTargetCurr = 0.0; // 缓启动目标电流
    FLOAT fFmChargeTargetCurr = 0.0; //调频充电时的目标电流
#ifdef SMOOTH_CHARGE_LIMIT_CURRENT
    if(!GetQtptestFlag())
    {
        if(s_tBattDeal.ucBatStatus == BATT_MODE_CHARGE) 
        {
            if(s_tBattIn.tPara.ucChargeMap)
            {
                fChargeTargetCurr = GetChargeLimitCurrent();
            }
            else
            {
                fChargeTargetCurr = g_ProConfig.fRateCurrChg;
            }
            s_tBattDeal.fReachMaxCurret = fChargeTargetCurr;
        }
        else //非充电状态，充电电流置2A，清除单体电压大于3.5V回差标志位
        {
            fChargeTargetCurr = s_tBattIn.tData.fCurrMinDet + 1.0;
            for(BYTE i=0; i<s_tHardwarePara.ucCellVoltNum; i++)
            {
                s_aucFlag[i] = 0;
            }
        }

        if(!s_tBattDeal.bPeakShiftOff && s_tBattIn.tData.tFmData.ucFmStatus && s_tBattDeal.fFmMaxCurr > 0.0f)
        {
            fFmChargeTargetCurr = GetValidData(s_tBattDeal.fFmMaxCurr, g_ProConfig.fRateCurrChg, SMART_LI_CURRENT_MIN);
            fChargeTargetCurr = MIN(fFmChargeTargetCurr,fChargeTargetCurr);
        }

        // 进行强补电时，选择充电map和强补电限流较小值
        if (s_tBattDeal.bCellUnderVolt)
        {
            fChargeTargetCurr = MIN(fChargeTargetCurr, s_tBattIn.tData.fCurrMinDet + 1.0);
        }

#else
    if(!GetQtptestFlag())
    {   
        //充电缓启动
        if (s_tBattDeal.bCellVoltTooLow || s_tBattDeal.bCellTempTooHigh || s_tBattDeal.bCellUnderVolt)
        {
            fChargeTargetCurr = s_tBattIn.tData.fCurrMinDet + 1.0;
        }
        else if (s_tBattDeal.bCellTempLow || s_tBattDeal.bCellTempHigh || s_tBattDeal.bCellVoltHigh || s_tBattDeal.bCellVoltLow)
        { //控制电池侧0.1C限流
            fChargeTargetCurr = 0.1 * s_tBattIn.tPara.wBattCap * g_ProConfig.fCapTransRate;
        }
        else
        {
            fChargeTargetCurr = g_ProConfig.fRateCurrChg;
        }

#endif
        // if (s_tBattDeal.bNeedLimitCurrCharge)
        // {
        //     fChargeTargetCurr = MIN(s_tBattDeal.fChargeCurrHope, 10);
        // }

        DealChargeCurrSlowUp(fChargeTargetCurr);
    }
    else
    {
        s_tBattDeal.fChargeCurrHope = g_ProConfig.fRateCurrChg;          //电池侧最大电流充电
    }
    s_tBattDeal.fChargeCurrHope    = GetValidData(s_tBattDeal.fChargeCurrHope, g_ProConfig.fRateCurrChg, MIN_CURR_DET_BDCU + 1.0);
    s_tBattDeal.fChargeSetCurr     = GetValidData(s_tBattDeal.fChargeCurrHope, g_ProConfig.fRateCurrChg, MIN_CURR_DET_BDCU + 1.0);


//母排侧电流限流
    //当为调频充电时，不受最大充电电流限制，因此母排侧电流限制为最大值
    if(!s_tBattDeal.bPeakShiftOff && s_tBattIn.tData.tFmData.ucFmStatus && s_tBattDeal.fFmMaxCurr > 0.0f)
    {
        s_tBattDeal.fChargeBusCurrHope = g_ProConfig.fRateCurrChgBus;
        s_tBattDeal.fReachMaxCurret = MIN(s_tBattDeal.fReachMaxCurret,s_tBattDeal.fChargeBusCurrHope);
    }
    else
    {
        s_tBattDeal.fChargeBusCurrHope = s_tBattIn.tPara.fChargeMaxCurr * s_tBattIn.tPara.wBattCap; //母排侧限流值
    }
    if(s_tBattDeal.tChgCurrCeofByCtrl.bValid)
    {
        s_tBattDeal.fChargeBusCurrHope = MIN(s_tBattDeal.tChgCurrCeofByCtrl.sValue / 100.0 * s_tBattIn.tPara.wBattCap, s_tBattDeal.fChargeBusCurrHope);
    }
    else if(s_tBattDeal.tChgCurrCeofNonCtrl.bValid)
    {
        s_tBattDeal.fChargeBusCurrHope = MIN(s_tBattDeal.tChgCurrCeofNonCtrl.sValue / 1000.0 * s_tBattIn.tPara.wBattCap, s_tBattDeal.fChargeBusCurrHope);
    }
    s_tBattDeal.fChargeBusSetCurr  = GetValidData(s_tBattDeal.fChargeBusCurrHope, g_ProConfig.fRateCurrChgBus, (MIN_CURR_DET_BDCU + 1.0)/g_ProConfig.fCapTransRate);
    return;
}

static void SwitchToLABattery(void)
{
    s_tBattDeal.fDischargeHopeVol = MAX(s_tBattIn.tPara.fLASwitchVolt, OUT_VOLT_MIN);
    return;
}

/** 混用场景，锂电优先 **/
Static void CheckAndOutputVoltAsBatteryVolt(void)
{
    FLOAT fVol=0.0;

    // if (s_tBattDeal.bNeedDeclineDischgVolt)
    // {
    //     s_tBattDeal.fDischargeHopeVol = GetValidData(s_tBattDeal.fDischargeHopeVolTem-1, OUT_VOLT_MAX, OUT_VOLT_MIN);
    //     return;
    // }

    if (s_tBattIn.tData.wBattSOC < s_tBattIn.tPara.wSwitchSOC)
    {
        if(s_tBattIn.tData.fBusVol < 48.0f )
        {
            TimerPlus(s_tBattDeal.ucMixSwitchCount, THREE_MINUTES_SECOND);
            if(s_tBattDeal.ucMixSwitchCount >= THREE_MINUTES_SECOND)
            {
                s_tBattIn.tPara.fSysPowerOnThreshold = MIN(s_tBattIn.tPara.fSysPowerOnThreshold, 51.0f);
            }
        }
        else
        {
            s_tBattDeal.ucMixSwitchCount = 0;
        }
    }

    if ((!GetMasterDischgProtect() && s_tBattIn.tData.wBattSOC < s_tBattIn.tPara.wSwitchSOC)
        ||(GetMasterDischgProtect() && s_tBattIn.tData.wSlaveAvgSOC < s_tBattIn.tPara.wSwitchSOC))    
    {
        if (s_tBattIn.tPara.wSwitchSOC == s_tBattIn.tPara.wSwitchSOC2)
        {
            if(TimeOut(s_tBattDeal.wDischargeEndTimer, THREE_MINUTES_SECOND))
            {
                fVol = s_tBattIn.tPara.fDischargeEndVolt2;
            }
            else
            {
                TimerPlus(s_tBattDeal.wDischargeEndTimer, THREE_MINUTES_SECOND);
                fVol = s_tBattIn.tPara.fDischargeEndVolt1;
            }
        }
        else
        {
            if ((!GetMasterDischgProtect() && s_tBattIn.tData.wBattSOC < s_tBattIn.tPara.wSwitchSOC2)
                ||(GetMasterDischgProtect() && s_tBattIn.tData.wSlaveAvgSOC < s_tBattIn.tPara.wSwitchSOC2))  
            {
                fVol = s_tBattIn.tPara.fDischargeEndVolt2;
            }
            else
            {
                fVol = s_tBattIn.tPara.fDischargeEndVolt1;
            }
        }

        s_tBattDeal.fDischargeHopeVol = GetValidData(fVol, s_tBattIn.tPara.fSysPowerOnThreshold - 0.5f, OUT_VOLT_MIN);
    }
    else
    {
        s_tBattDeal.ucMixSwitchCount = 0;
        s_tBattDeal.wDischargeEndTimer = 0;
        s_tBattDeal.fDischargeHopeVol = GetMixOutputVolt(&s_tBattIn, &s_tBattDeal);
    }
    s_tBattDeal.fDischargeHopeVolTem = s_tBattDeal.fDischargeHopeVol;

    if(GetQtptestFlag())
    {
        s_tBattDeal.fDischargeHopeVol = GetValidData(s_tBattDeal.fDischargeHopeVol, s_tBattIn.tPara.fSysPowerOnThreshold, OUT_ASSIST_VOLT_MIN);
    }
    else
    {
        s_tBattDeal.fDischargeHopeVol = GetValidData(s_tBattDeal.fDischargeHopeVol, s_tBattIn.tPara.fSysPowerOnThreshold, MIN(SMART_LI_OUTPUT_VOL_MIN, PURE_LI_OUT_MIN));
    }

    if(s_tBattDeal.bFmDischg)
    {
        s_tBattDeal.fDischargeHopeVol = s_tBattDeal.fFmDischgVolt;
    }
    else if(s_tBattDeal.bPeakDischg)
    {
        s_tBattDeal.fDischargeHopeVol = s_tBattDeal.fPeakDischgVolt;
    }

    return;
}

/*从电池电压计算输出电压，考虑电池电压会有波动，输出电压希望保持稳定*/
static FLOAT CalcOutVolFromBattVol(void)
{
#define BATT_VOL_CALC_PERIOD 10

    FLOAT fVol=0.0;

    if (s_tBattIn.tData.fBattAverageVolBak <= OUT_VOLT_MIN)
    {
        s_tBattIn.tData.fBattAverageVolBak = s_tBattIn.tData.fBattAverageVol;
    }

    /*
    For the problem in out-factory product, when testing discharge-capacity, battery-voltage low protect occur 
    firstly but the bus voltage is above 43V
    */
    if (isSingleBatt())//only itself in system. Added by fengfj, 2021-03-05 23:24:25
    {
        fVol = s_tBattIn.tData.fBattAverageVol*0.2 + s_tBattIn.tData.fBattAverageVolBak*0.8;
    }
    else
    {
        fVol = s_tBattIn.tData.fBattAverageVol*0.05 + s_tBattIn.tData.fBattAverageVolBak*0.95;

        if ((fVol - s_tBattIn.tData.fBattAverageVolBak) > 0.01f)
        {
            fVol = s_tBattIn.tData.fBattAverageVolBak + 0.01;
        }
        else if ((fVol - s_tBattIn.tData.fBattAverageVolBak) < -0.01f)
        {
            fVol = s_tBattIn.tData.fBattAverageVolBak - 0.01;
        }
    }
    s_tBattIn.tData.fBattAverageVolBak = fVol;

    return fVol;
}

///纯锂电模式采用放电跟随方式进行输出
void KeepOutputEqualBatteryVoltage(void)
{
    static FLOAT s_fBatVoltSum = 0;
    static BYTE s_fBatVoltCounter = 0;
    static BYTE s_bBatteryModeBak;
    static FLOAT s_fBatAvgVolt = 0;
    FLOAT fOutputADischargeVolt = s_tBattDeal.fDischargeHopeVol;

    if(s_bBatteryModeBak == s_tBattDeal.ucChargeMode)
    {
        if(!TimeOut(s_fBatVoltCounter,10))
        {
            s_fBatVoltCounter++;
            s_fBatVoltSum += s_tBattIn.tData.fBatVol; 
            if(s_fBatVoltCounter == 10)
            {
                s_fBatAvgVolt = s_fBatVoltSum/s_fBatVoltCounter;
                fOutputADischargeVolt = g_ProConfig.fVoltTrasRate*s_fBatAvgVolt;
                s_fBatVoltCounter = 0;
                s_fBatVoltSum = 0;
            }   
        }
    }
    else
    {
        s_bBatteryModeBak = s_tBattDeal.ucChargeMode;
        s_fBatVoltCounter = 0;
        s_fBatVoltSum = 0;
        s_fBatVoltCounter++;
        s_fBatVoltSum += s_tBattIn.tData.fBatVol; 
    }

#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
    if(IsMaster() && GetCan2ConnectedFlag())
    {
        if(IsSleep() || s_tBattIn.tData.ucBduDischargePrtStatus || s_tBattIn.tData.bBduDischgDisable)
        {
            fOutputADischargeVolt = s_tBattIn.tData.fAveBattVolt;  //主机休眠保护后放电电压按照簇内平均电池电压
        }
    }
#endif

#ifdef INTELLIGENT_PEAK_SHIFTING
    if(s_tBattDeal.bFmDischg)
    {
        fOutputADischargeVolt = s_tBattDeal.fFmDischgVolt;
    }
    else if(s_tBattDeal.bPeakDischg)
    {
        fOutputADischargeVolt = s_tBattDeal.fPeakDischgVolt;
    }
#endif

    if(GetQtptestFlag())
    {
        s_tBattDeal.fDischargeHopeVol = MAX(fOutputADischargeVolt, OUT_ASSIST_VOLT_MIN);
    }
    else
    {
        s_tBattDeal.fDischargeHopeVol = MAX(fOutputADischargeVolt, PURE_LI_OUT_MIN);
    }

    s_tBattDeal.fDischargeSetVol = s_tBattDeal.fDischargeHopeVol;
}

void CheckDishargeModeAndSwitch(void)
{/*
    0:Li battery switch to LA battery,keep output equal battery voltage;
    1:Li battery only,keep output equal battery voltage;
    2:Li battery switch to LA battery, decrease the output voltage;
*/
    T_fSwitchHandle afSwitchHandle[] = {
        CheckAndOutputVoltAsBatteryVolt,////混用，LI优先放电
        KeepOutputEqualBatteryVoltage,////纯锂电
        SwitchToLABattery////混用，铅酸优先
        };

    s_tBattDeal.ucNextDischargeMode %= ARR_SIZE(afSwitchHandle);
    
    if (NULL != afSwitchHandle[s_tBattDeal.ucNextDischargeMode])
    {
        afSwitchHandle[s_tBattDeal.ucNextDischargeMode]();
    }

    return;
}



#ifdef SMALL_CURR_COMPENSATION_ENABLED
Static BOOLEAN IfNeedDischargeSOCCal(void)
{
    //1、电池处于放电模式；2、放电电流小于2A；3、最高单节温度大于0度。
    if((IfBduDischarge()) && (fabs(s_tBattIn.tData.fBattCurr) < SOC_CAL_DISCHG_CURR) && (s_tBattIn.tData.fCellTempMax > 0))
    {
        if ((s_tBattDeal.ulDischargeSOCCalTimer) < (120*HALF_AN_HOUR_PER_SECONDS))//计时持续60小时,方便查看过程数据
        {
            s_tBattDeal.ulDischargeSOCCalTimer += s_tBattDeal.ulSecondTimeDiffSec;
        }

        if(s_tBattDeal.ulDischargeBatVolDiffTimer <= 3)
        {
            s_tBattDeal.fMinBatVol = s_tBattIn.tData.fBatVol * g_ProConfig.fVoltTrasRate;
            s_tBattDeal.fMaxBatVol = s_tBattIn.tData.fBatVol * g_ProConfig.fVoltTrasRate;
        }
        s_tBattDeal.fMinBatVol = MIN(s_tBattIn.tData.fBatVol * g_ProConfig.fVoltTrasRate, s_tBattDeal.fMinBatVol);
        s_tBattDeal.fMaxBatVol = MAX(s_tBattIn.tData.fBatVol * g_ProConfig.fVoltTrasRate, s_tBattDeal.fMaxBatVol);

        if((s_tBattDeal.fMaxBatVol - s_tBattDeal.fMinBatVol) < 0.05f)
        {
            if((s_tBattDeal.ulDischargeBatVolDiffTimer) < (120*HALF_AN_HOUR_PER_SECONDS))//计时持续60小时,方便查看过程数据
            {
                s_tBattDeal.ulDischargeBatVolDiffTimer += s_tBattDeal.ulSecondTimeDiffSec;
            }
        }
        else
        {
            s_tBattDeal.ulDischargeBatVolDiffTimer = 0;
        }
    }
    else
    {
        s_tBattDeal.ulDischargeSOCCalTimer = 0;
        s_tBattDeal.ulDischargeBatVolDiffTimer = 0;
    }
    //状态持续0.5小时以上；过程中判断整组变化小于0.05V，持续10分钟以上
    if((s_tBattDeal.ulDischargeSOCCalTimer > HALF_AN_HOUR_PER_SECONDS) && (s_tBattDeal.ulDischargeBatVolDiffTimer > TEN_MINUTES_PER_SECONDS))
    {
        return True;
    }

    return False;
}
#endif /* SMALL_CURR_COMPENSATION_ENABLED */

BOOLEAN IfSmartLiDischgStopDelay(void)
{
    return s_tBattDeal.ucDischgDelayCounter < CHARGE_STOP_DELAY;
}

#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
FLOAT CalBoostVolB3(void)
{
    static FLOAT fCompVol = 0.0f;
    BYTE ucSlaveNum = 0;
    WORD wMaxBatSOC=0;
    FLOAT fMaxBatVol = 0.0f, fVol = 0.0, fBoostVol=30.0f, fMaxOutVol=0.0f;
    BOOLEAN bCurrFull = False;
    getMaxVolAndMaxSoc(&fMaxBatVol, &wMaxBatSOC, &ucSlaveNum);

    fVol = (FLOAT)wMaxBatSOC/1000.0 - s_tBattDeal.fBattCap / (s_tBattDeal.fBattSOH * s_tSysPara.wBatteryCap / 100 ) *10.0;
    fVol = GetValidData(2.0*fVol, 0.8, -0.8);
    if((WORD)0 == ucSlaveNum)
    {
        fVol = 0.0;
    }
    CalMaxSocVoltDiff(wMaxBatSOC);
    CalCAN2ClusterVolt();
    /////如下为均流电压补偿
    if ((getSysLimit() && fabs(s_tBattIn.tData.fMaxCurrInAll) > 80.0f )
        && s_tBattDeal.fDischargeHopeVol > (s_tBattIn.tData.fSysVol + 0.2)
        )
    {
        bCurrFull = True;
        fCompVol = MIN(fCompVol+0.05, 1.0);//移植时漏掉fCompVol的赋值
    }
    else if (s_tBattDeal.fDischargeHopeVol < s_tBattIn.tData.fSysVol-0.1
        ||(s_tBattDeal.fDischargeHopeVol + fCompVol) > (s_tBattIn.tPara.fSysPowerOnThreshold-0.5))
    {
        fCompVol = 0;
        s_tBattOut.fSetDischVolt = MIN(s_tBattIn.tPara.fSysPowerOnThreshold-0.5, s_tBattOut.fSetDischVolt);
    }
    else if ( (s_tBattDeal.fDischargeHopeVol > s_tBattIn.tData.fSysVol+0.2f)
           && (fCompVol < 1.0f)
            && (s_tBattDeal.fDischargeHopeVol + fCompVol) < (s_tBattIn.tPara.fSysPowerOnThreshold-0.5))
    {
        fCompVol = MIN(fCompVol+0.01, 1.0);
    }

//    fBoostVol = s_tBattDeal.fDischargeHopeVol + s_tBattDeal.fCompVol - fMaxBatVol + CalcVolDiffFromSOC(wBattSOCWithMaxVol, wMaxBatSOC);
//    fBoostVol = MIN(fBoostVol, s_tBattDeal.fDischargeHopeVol + s_tBattDeal.fCompVol - fBattVolWithMaxSOC);

    getBoostVol(&fBoostVol, &wMaxBatSOC, s_tBattDeal.fDischargeHopeVol, fCompVol);

    if ((s_tBattIn.tData.bRunModeControled && FAILURE != GetValueFromProtocol(INFO_TYPE_STATUS,NULL))
        || (bCurrFull)
        )
    {
        fMaxOutVol = OUT_VOLT_MAX;
    }
    else
    {
        fMaxOutVol = s_tBattIn.tPara.fSysPowerOnThreshold-MIX_POWER_DIFF;
    }

    s_tBattDeal.fDischargeSetVol = GetValidData(s_tBattIn.tData.fBatVol * g_ProConfig.fVoltTrasRate + fBoostVol - fVol, fMaxOutVol, OUT_VOLT_MIN);
    if(s_tBattDeal.bThroughDischg == True)
    {
        s_tBattDeal.fDischargeSetVol = s_tBattDeal.fDischargeHopeVol;  //直通下主从机不考虑电压补充 1209 mwl
        setBMSMode( BATT_MODE_DISCHARGE, (SHORT)0, wMaxBatSOC);
    }
    else
    {
        setBMSMode( BATT_MODE_DISCHARGE, (SHORT)(fBoostVol*100.0), wMaxBatSOC);
    }
    s_tBattDeal.wGroupMaxSoc = wMaxBatSOC;
    s_tBattOut.fBoostVol = fBoostVol;
    return s_tBattDeal.fDischargeHopeVol + fCompVol;
}
#endif

FLOAT CalBoostVol(void)
{
    static FLOAT fCompVol = 0.0f;
    FLOAT fMaxOutVol=0.0f;
    BOOLEAN bCurrFull = False;
#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
    FLOAT fMaxBatVol = 0.0f;
    BYTE ucSlaveNum = 0;
    WORD wMaxBatSOC=0;

    getMaxVolAndMaxSoc(&fMaxBatVol, &wMaxBatSOC, &ucSlaveNum);
    s_tBattDeal.wGroupMaxSoc = wMaxBatSOC;
    if(s_tBattDeal.tMaxSocCtrl.bValid) //scu控制均流兜底
    {
        CalMaxSocVoltDiff(s_tBattDeal.wGroupMaxSoc);
    }
    else //簇间can2均流
    {
        CalCAN2ClusterVolt();
    }
#endif
    if ((getSysLimit() && fabs(s_tBattIn.tData.fMaxCurrInAll) > 80.0f )
        && s_tBattDeal.fDischargeHopeVol > (s_tBattIn.tData.fSysVol + 0.2)
        )
    {
        bCurrFull = True;
        fCompVol = MIN(fCompVol+0.05, 1.0);
    }
    else if (s_tBattDeal.fDischargeHopeVol < s_tBattIn.tData.fSysVol-0.1
        ||(s_tBattDeal.fDischargeHopeVol + fCompVol) > (s_tBattIn.tPara.fSysPowerOnThreshold-0.5))
    {
        fCompVol = 0;
        //s_tBattOut.fSetDischVolt = MIN(s_tBattIn.tPara.fSysPowerOnThreshold-0.5, s_tBattOut.fSetDischVolt);
    }
    else if ( (s_tBattDeal.fDischargeHopeVol > s_tBattIn.tData.fSysVol+0.2f)
            && (fCompVol < 1.0f)
            && (s_tBattDeal.fDischargeHopeVol + fCompVol) < (s_tBattIn.tPara.fSysPowerOnThreshold-0.5))
    {
        fCompVol = MIN(fCompVol+0.01, 1.0);
    }

    if ((0==s_tBattIn.tData.ucBusCurrValidNum || (fabs(s_tBattIn.tData.fBattCurr) > s_tBattIn.tData.fCurrMinDet)))
    {
        s_tBattDeal.fDischargeSetVol = s_tBattDeal.fDischargeHopeVol + s_tBattDeal.fCompVol;
    }

    if ((s_tBattIn.tData.bRunModeControled && FAILURE != GetValueFromProtocol(INFO_TYPE_STATUS,NULL))
        || (bCurrFull)
        )
    {
        fMaxOutVol = OUT_VOLT_MAX;
    }
    else
    {
        fMaxOutVol = s_tBattDeal.fDischargeSetVol;
    }

    s_tBattDeal.fDischargeSetVol = GetValidData(s_tBattDeal.fDischargeSetVol, fMaxOutVol, OUT_VOLT_MIN);

    if(s_tBattDeal.bThroughDischg == True)
    {
        s_tBattDeal.fDischargeSetVol = s_tBattDeal.fDischargeHopeVol;  //直通下主从机不考虑电压补充 1209 mwl
        setVol( BATT_MODE_DISCHARGE, (SHORT)(s_tBattDeal.fDischargeSetVol*100));
    }
    else
    {
        setVol( BATT_MODE_DISCHARGE, (SHORT)(s_tBattDeal.fDischargeSetVol*100.0));
    }
    return s_tBattDeal.fDischargeHopeVol + fCompVol;
}

/***************************************************************************
 * @brief   根据簇主机下发的目标值调整簇从机的放电电压
 **************************************************************************/
#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
Static BOOLEAN CalCAN2ClusterVolt(void) 
{
    if(IsMasterCan2())
    {
        return False;
    }

    if(!IsMaster())
    {
        return False;
    }

    if(!s_tBattDeal.tEqualObjData.bValid)
    {
        return False;
    }


    if(s_tSysPara.ucUsageScen == DISCHG_MODE_BATT_CHARACTERISTIC)
    {
        s_tBattDeal.fDischargeHopeVol = GetValidData(s_tBattDeal.fAdjVolt, s_tBattIn.tData.fBatVol*BATT_SOC_MAX + 0.5f, OUT_VOLT_MIN);
    }
    else if(s_tSysPara.ucUsageScen == DISCHG_MODE_SELF_ADAPTION)
    {
        s_tBattDeal.fDischargeHopeVol = GetValidData(s_tBattDeal.fAdjVolt, s_tBattIn.tPara.fSysPowerOnThreshold - 0.1f, OUT_VOLT_MIN);
    }
    else if(s_tSysPara.ucUsageScen == DISCHG_MODE_CONSTANT_VOLTAGE)
    {
        s_tBattDeal.fDischargeHopeVol = GetValidData(s_tBattDeal.fAdjVolt, s_tSysPara.fRemoteSupplyOutVolt + 0.5f, OUT_VOLT_MIN);
    }


    return True;
}
#endif

#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
Static void CalMaxSocVoltDiff(WORD wMaxSOC)
{
    FLOAT fSocVoltDiff = 0.0f;
    if(s_tBattDeal.tMaxSocCtrl.bValid)
    {
        fSocVoltDiff = 0.2 * (FLOAT)(wMaxSOC - s_tBattDeal.tMaxSocCtrl.sValue) / 100.0;
        fSocVoltDiff = GetValidData(fSocVoltDiff, 0.8, -0.8);
        s_tBattDeal.fDischargeHopeVol += fSocVoltDiff;
    }
}
#endif /* MONITORING_BASED_EQUALIZE_CURRENT_ENABLED */


/***************************************************************************
 * @brief    计算布防条件中的充电时间
 *           必须先防盗告警非屏蔽且接上防盗线，否则清除计时
 **************************************************************************/
Static void DefenseBattChargeMinutesCount(void)
{
    if (s_tSysPara.aucAlarmLevel[BATT_LOSE_ALM_INDEX] == 0
        || (IsSupportsAntiTheftLine() && PIN_HIGH == getPortInput(Digit_THEFT_DET))
        || True == GetDefenceStatus()
        || GetQtptestFlag()) // 处于布防状态时也要清除计时
    {
        s_tBattDeal.slDefenseBattChargeMinutes = 0;
    }
    return;
}


static void startDischg(void)
{
    if (startDischgOper != NULL)
    {
        startDischgOper(&s_tBattDeal, &s_tBattIn);
    }
    return;
}

static void CheckChargeModeChange(void)
{
    if (s_tBattDeal.bModeChange && (s_tBattDeal.ucBatStatus == s_tBattDeal.ucChargeMode || s_tBattDeal.ucChargeMode == BATT_MODE_OFFLINE))
    {
        s_battModeChangeCn++;
    }

    /* 电池充放电状态切换，延时3s保存历史数据*/
    if(s_battModeChangeCn >= 3)
    {
        s_tBattDeal.bModeChange = FALSE;
        s_battModeChangeCn = 0;
        SaveHisData();
    } 
    return;
}

Static INT32S SmartLiBattChargeMode(void)
{
    if ( BATT_MODE_STANDBY == s_tBattDeal.ucChargeMode || BATT_MODE_CHARGE == s_tBattDeal.ucChargeMode)
    {
        s_tBattDeal.ucSleepBattMode = BATT_MODE_CHARGE;
        s_tBattDeal.ucSleepBattStatus = BATT_MODE_CHARGE;
        BattCharge(&s_tHardwarePara, &s_tBattIn, &s_tBattDeal);
    }
    else
    {
        s_tBattDeal.ucSleepBattMode = BATT_MODE_DISCHARGE;
        s_tBattDeal.ucSleepBattStatus = BATT_MODE_DISCHARGE;
        BattDischarge(&s_tBattIn, &s_tBattDeal);
    }

    if(s_tBattDeal.ucBatStatus != s_tBattDeal.ucChargeMode)
    {
        s_tBattDeal.bModeChange = TRUE;
    }

    CheckLoopOff();

    return SUCCESSFUL;
}

INT32S SetChargeMode(BYTE ucChargeMode)
{
    if ( ucChargeMode >= BATT_MODE_INVALID )
    {
        return FAILURE;
    }

    if ( BATT_MODE_OFFLINE <= ucChargeMode )
    {
        s_tBattDeal.ucBduCtrlMode = BDU_SLEEP_START;
        //ShutdownSampleIc();                //B3在此处保存休眠记录，R311调整位置
        s_tBattDeal.tOfflineTimeStamp = GetTimeStamp();
    }
    else if ( BATT_MODE_OFFLINE <= s_tBattDeal.ucChargeMode)
    {
        s_tBattDeal.ucBduCtrlMode = BDU_SLEEP_STOP;
        //WakeupSampleIc();            //B3在此处保存唤醒记录，R311调整位置
    }

    if ( BATT_MODE_CHARGE == ucChargeMode )
    {
        if(!s_tBattDeal.bCellUnderVolt)//避免可能存在主机强补电下还未转从机就下发从机转充电命令
        {
          setBMSMode( BATT_MODE_CHARGE, Float2Short(s_tBattIn.tPara.fChargeFullVol*100), 0);
        }

#ifdef UNITEST
        s_tBattDeal.wChargeModeTimes++;
#endif
        //send to contrl command to clear inputbreak and enter trans-stage. Added by fengfj, 2021-01-23 22:32:31
        BduCtrl(SCI_CTRL_CHG2CHG, ENABLE);
        BduCtrl(SCI_CTRL_CHG2CHG, ENABLE);        //B3为什么要两遍，待删除
        s_tBattDeal.ucTransCounter = 0;
        // End Added
    }
    else if ( BATT_MODE_DISCHARGE == ucChargeMode )
    {
        startDischg();
#ifdef UNITEST
        s_tBattDeal.wDischargeModeTimes++;
#endif
        /*
        When Switch to Discharge, DO Not Send START_CHARGE_CMD at begining till
        the charge stop delay timeout.
        */
        //BduCtrl1( BDU_DISCHG_START );
        BduCtrl(SCI_CTRL_CHG2CHG, DISABLE);
        BduCtrl(SCI_CTRL_CHG2CHG, DISABLE);        //B3为什么要两遍，待删除
    }
    s_tBattDeal.ucChargeMode = ucChargeMode;

    return SUCCESSFUL;
}

void SetSlaveEqualizeCurr(WORD wEqualizCurr, WORD wCap, BYTE bMasterFull)
{
    FLOAT fCurr = 0.0f;

    if ((WORD)0 != wCap)
    {
        fCurr = (FLOAT)wEqualizCurr/100.0f * (FLOAT)s_tBattIn.tPara.wBattCap/(FLOAT)wCap;
        if (s_tBattDeal.bPowerOff)
        {
            s_tBattDeal.fDischgEqualCurr = fCurr;
        }
        else
        {
            s_tBattDeal.fChargeEqualCurr = fCurr;
            s_tBattDeal.bChargeEqualCurrValid = True;
        }
    }

    ////主机充满后需要清除掉补偿值
    if(bMasterFull)
    {
        ClearPowerdownVoltOffset();
    }
    return;
}

BOOLEAN IsSleep( void )
{
    if ( BATT_MODE_OFFLINE == s_tBattDeal.ucChargeMode )
    {
        return True;
    }
    return False;
}

INT32S BattChargeMode( void )
{
    if (BATT_MODE_OFFLINE == s_tBattDeal.ucChargeMode)
    {
#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
        if(GetCan2ConnectedFlag())
        {
            if (BATT_MODE_CHARGE == s_tBattDeal.ucSleepBattMode)
            {
                BattSleepCharge(&s_tHardwarePara, &s_tBattIn, &s_tBattDeal);
            }
            else
            {
                BattSleepDischarge(&s_tBattIn, &s_tBattDeal);
            }
        }
#endif
        return SUCCESSFUL;
    }

    return SmartLiBattChargeMode();
}

/***************************************************************************
* 函数名称：BatteryManagement()
* 调    用：
* 被 调 用：
* 输入参数：无
* 返 回 值：无
* 功能描述：电池管理程序
* 作    者：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
void BatteryManagement( void* parameter )
{
    unsigned int time_count = 0;
#ifndef UNITEST
    while(GetFlagSample())         //保证采样线程优先于电池管理
    {
        rt_thread_delay(100);
    }

    s_ptMutexBattRunFlag = rt_mutex_create("BattThread_lock", RT_IPC_FLAG_PRIO);
    if(s_ptMutexBattRunFlag == NULL)
    {
        return;
    }

    InitBatt();
    pre_thread_beat_f(THREAD_BATT_MANAGE);
    while(1)
    {
        thread_beat_go_on(THREAD_BATT_MANAGE);
        rt_thread_delay(1000);
        if(GetApptestFlag())
        {
            BduCtrl(SCI_CTRL_CHG_BST, ENABLE);
            checkApptestRst(); // 检查apptest模式下按键长按10S，长按后重启
            continue;
        }
        BattThreadMutexTake(100); // 进入电池管理线程Take互斥锁，最多100ms等待
#endif /* UNITEST */
        BattDataLoad(&s_tHardwarePara, &s_tBattIn, &s_tBattDeal);

        LoadBattDeal();

        CalBattCap();

#ifdef ACTIVATE_PORT_ENABLED
        ActivatePort(&s_tBattIn, &s_tBattDeal);
#endif

        SleepMgr(&s_tBattIn, &s_tBattDeal);

        s_bIfDealDcr = DealDcr();

        BattChargeMode();

        PassiveBalanceManagement(&s_tHardwarePara , &s_tBattIn, &s_tBattDeal, &s_tBattOut);

        DealBattResult();

        SaveBattRealData();
        CheckChargeModeChange();
        BattThreadMutexRelease(); // 退出电池管理线程，Release线程锁

        time_count++;
        if(time_count%ONE_MINUTES_SECOND == 0)
        {
            DealOneMinuteTask();
        }
#ifdef USING_SNMP
        snmp_comm_timer_count();
#endif /* USING_SNMP */

#ifdef JUDGECOMM_4G_NET_CAN2
        Net1363NorthTimeCount();
        NetRemoteNorthTimeCount();
#endif
        s_ucFlagBattery = 1;
#ifndef UNITEST
    }
#endif /* UNITEST */
}


Static BOOLEAN CalcBattDsichargNPlus1Times(void)
{
    FLOAT fCapPerDischarge = s_tBattIn.tPara.ucDODOfDischarge * s_tBattIn.tPara.wBattCap * g_ProConfig.fCapTransRate / 100.0;

    if ((DISCHG_MODE_SELF_ADAPTION != s_tBattIn.tPara.ucUsageScen && DISCHG_MODE_SOLAR != s_tBattIn.tPara.ucUsageScen)
        || s_tBattIn.tPara.ucCycleMode != CYCLE_MODE_N_ADD_1)
    {
        s_tBattDeal.fDischargeCapForNTimes = 0.0f;
        return FALSE;
    }

    AccumulateDischargeCap(&s_tBattIn, &s_tBattDeal.fDischargeCapForNTimes, s_tBattDeal.ulMinuteTimeDiffSec);

    if (fabs(fCapPerDischarge) > 0.001f)
    {
        s_tBattDeal.ucLiDischargeNTimes = (BYTE)(s_tBattDeal.fDischargeCapForNTimes / fCapPerDischarge);
    }
    else
    {
        s_tBattDeal.ucLiDischargeNTimes = (BYTE)0;
    }

    return TRUE;
}


#ifdef POWER_OFF_PROTECTION
static BOOLEAN IsPowerOffTooLong(void)
{
    if (True == s_tSysPara.aucAlarmLevel[52])
    {
        if (s_tBattDeal.wPowerOffTime >= s_tSysPara.ulPoweroffTimePrtThre)
        {
            return True;
        }
        return False;
    }
    
    return False;
}
#endif

Static void CalcSelfDischargeDecreaseCap(void)
{
#ifdef CAL_SELF_DISCHAGE_DECREASE_CAP_WITH_PERIOD_ENABLED
    INT32S slTimeDiff = 0;
#endif

    if (BATT_MODE_STANDBY != s_tBattDeal.ucBatStatus)
    {
        return;
    }
#ifdef CAL_SELF_DISCHAGE_DECREASE_CAP_WITH_PERIOD_ENABLED
    if (s_tBattDeal.slSelfDischgCalcTime > s_tBattDeal.slStandByTimer)
    {
        s_tBattDeal.slSelfDischgCalcTime = s_tBattDeal.slStandByTimer;
        return;
    }

    slTimeDiff = s_tBattDeal.slStandByTimer - s_tBattDeal.slSelfDischgCalcTime;
    
    if (slTimeDiff >= MINUTES_PER_MONTH)
    {
        s_tBattDeal.slSelfDischgCalcTime = s_tBattDeal.slStandByTimer;
        s_tBattDeal.fBattCap -= SELF_DISCHARGE_RATE_35C*s_tBattIn.tPara.wBattCap*g_ProConfig.fCapTransRate/100.0;
        s_tBattDeal.fMultiDischargeCap += SELF_DISCHARGE_RATE_35C*s_tBattIn.tPara.wBattCap*BATT_SOC_MAX/100.0; //同步累计放电容量校正
        s_tBattDeal.fMultiDischargePower += (SELF_DISCHARGE_RATE_35C*s_tBattIn.tPara.wBattCap*BATT_SOC_MAX/100.0) * s_tBattIn.tData.fBatVol / 1000; //同步累计放电电量校正
        s_tBattIn.tData.ucBattCycleTimesCal = CAL_SELF_DISCHAGE_DECREASE_CAP_WITH_PERIOD;
        AccumulateDischargeCap(&s_tBattIn, &s_tBattDeal.fTotalDischargeCap, s_tBattDeal.ulSecondTimeDiffSec);    
    }
#elif defined(CAL_SELF_DISCHAGE_DECREASE_CAP_ENABLED) /* !CAL_SELF_DISCHAGE_DECREASE_CAP_WITH_PERIOD_ENABLED && CAL_SELF_DISCHAGE_DECREASE_CAP_ENABLED */
    s_tBattDeal.fBattCap -= SMART_LI_SELF_DISCHARGE_CURRENT*1/60.0;
    s_tBattDeal.fMultiDischargeCap += SMART_LI_SELF_DISCHARGE_CURRENT*1/60.0;//同步累计放电容量校正
    s_tBattDeal.fMultiDischargePower += (SMART_LI_SELF_DISCHARGE_CURRENT*1/60.0) * s_tBattIn.tData.fBatVol / 1000;//同步累计放电电量校正
    s_tBattIn.tData.ucBattCycleTimesCal = CAL_SELF_DISCHAGE_DECREASE_CAP;
    AccumulateDischargeCap(&s_tBattIn, &s_tBattDeal.fTotalDischargeCap, s_tBattDeal.ulSecondTimeDiffSec); 
#endif /* CAL_SELF_DISCHAGE_DECREASE_CAP_WITH_PERIOD_ENABLED */

    return;
}

const T_SOCCalInfoAttrStruct s_tSOCCalInfoAttr[] = {{2.5, 0},  {2.809, 1}, {2.945, 2}, {3.031, 3}, {3.094, 4},  {3.139, 5},
                                                    {3.172, 6}, {3.193, 7},  {3.199, 8}, {3.203, 9},{3.203, 10}, {3.218, 15},
                                                    {3.242, 20},{3.259, 25}, {3.276, 30},{3.288, 35},{3.288, 40}, {3.289, 45},
                                                    {3.29, 50},{3.292, 55}, {3.301, 60},{3.326, 65}, {3.329, 70},{3.329, 75},
                                                    {3.329, 80},{3.329, 85},{3.329, 90},{3.33, 95},{3.331, 98},{3.337, 99},
                                                    {3.4, 100}};

/****************************************************************************
* 函数名称：CalBattCap()
* 调    用：
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：计算电池容量
* 作    者：xzx
* 设计日期：2018-11-14
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
Static void CalBattCap( void )
{
    FLOAT   fBattRealCap = (FLOAT)s_tBattIn.tPara.wBattCap *g_ProConfig.fCapTransRate* s_tBattDeal.fBattSOH / 100;
    FLOAT   fCoef = AFFECT_COEF;

#ifdef SMALL_CURR_COMPENSATION_ENABLED
    BYTE    i = 0;
    BYTE    ucDataAttrNum = 0;
    BOOLEAN bDischargeSOCCalFlag = FALSE;
    bDischargeSOCCalFlag = IfNeedDischargeSOCCal();
    s_tSOCCalDebug.ucSOCCalStatus = bDischargeSOCCalFlag;
#endif /* SMALL_CURR_COMPENSATION_ENABLED */

    if (s_tBattDeal.bDischargeFromFull)
    {
        AccumulateDischargeCap(&s_tBattIn, &s_tBattDeal.fDischargeCapForCelebrate, s_tBattDeal.ulSecondTimeDiffSec);
    }

    if(fabs(s_tBattIn.tData.fBattCurr) >= s_tBattIn.tData.fCurrMinDet)
    {
        if (s_tBattIn.tData.fBattCurr < 0.0f)
        {
            fCoef = 1.00;
        }
        s_tBattDeal.fBattCap += s_tBattIn.tData.fBattCurr / 60 * fCoef * (s_tBattDeal.ulSecondTimeDiffSec/60.0);
    }
    else
    {
#ifdef SMALL_CURR_COMPENSATION_ENABLED
        if (bDischargeSOCCalFlag)
        {
            FLOAT fCellVoltMin = s_tBattIn.tData.fCellVoltMin;
            if(fCellVoltMin > 3.276)    //处于平台区
            {
                fCellVoltMin = fCellVoltMin + 0.005;
            }
            ucDataAttrNum = ARR_SIZE(s_tSOCCalInfoAttr);
            for (i = 0; i < ucDataAttrNum; i++)
            {
                if (fCellVoltMin < s_tSOCCalInfoAttr[i].fCellVol)
                {
                    if (s_tBattIn.tData.wBattSOC > s_tSOCCalInfoAttr[i].ucSoc)
                    {
                        s_tBattDeal.fBattCap -= SOC_CAL_DISCHG_CURR * s_tBattDeal.ulSecondTimeDiffSec / 3600.0; //默认1A放电电流
                        s_tBattDeal.fMultiDischargeCap   += (SOC_CAL_DISCHG_CURR * s_tBattDeal.ulSecondTimeDiffSec / 3600.0) / BATT_SOC_MAX; // SOC校正时同步计算累计放电容量
                        s_tBattDeal.fMultiDischargePower += (SOC_CAL_DISCHG_CURR * s_tBattDeal.ulSecondTimeDiffSec / 3600.0) * s_tBattIn.tData.fBatVol / 1000; // SOC校正时同步计算放电电量
                        s_tBattIn.tData.ucBattCycleTimesCal = SMALL_CURR_COMPENSATION;
                        AccumulateDischargeCap(&s_tBattIn, &s_tBattDeal.fTotalDischargeCap, s_tBattDeal.ulSecondTimeDiffSec);
                    }
                    break;
                }
            }
        }
#endif /* SMALL_CURR_COMPENSATION_ENABLED */
    }

    ////不满足充满条件，则一直充电也不允许置满，防止SOH修正错误
    s_tBattDeal.fBattCap = GetValidData(s_tBattDeal.fBattCap, fBattRealCap, fBattRealCap*BATT_SOC_MIN);

    if (s_tBattDeal.bCellOverVolPrt || s_tBattDeal.bBattOverVolPrt)
    {
        s_tBattDeal.fBattCap = MAX(fBattRealCap * 99.0/100.0, s_tBattDeal.fBattCap);
    }

    if (s_tBattDeal.bCellFull)
    {
        s_tBattDeal.fBattCap = MAX(fBattRealCap, s_tBattDeal.fBattCap);
    }

    if (fabs(s_tBattDeal.fBattCap - s_tBattDeal.fBattCapSave) >= s_tBattIn.tPara.wBattCap * g_ProConfig.fCapTransRate * 0.02 )
    {
        s_tBattDeal.bSave2eeprom = True;
    }

    return;
}

static void CheckSaveEEPROM(void)
{
    if (s_tBattDeal.bSave2eeprom)
    {
        s_tBattDeal.fBattCapSave = s_tBattDeal.fBattCap;
        s_tBattDeal.bSave2eeprom = False;
        SaveBattInfo();
    }
    return;
}

Static void SaveBattRealData(void)
{
    /*
    todo:
    电池SOH, SOC, BDU输出电压，电池状态, 电池管理持续时间、BDU限电流
    写到全局实时信息中
    */
    BYTE i = 0;
    WORD wDefenceStatus = 0;
    LONG lTotalDischargeCap = 0, lTotalDischargePower = 0, lTotalChargeCap = 0, lTotalChargePower = 0;
    T_CtrlOutStruct tCtrlOut;
    T_BCMDataStruct tBcmData;
    T_DCRealData tDCRealData;
#ifdef INTELLIGENT_PEAK_SHIFTING
    T_FmSubmissionstruct tFmRealData;
#endif

    GetBduReal(&tDCRealData);       //获取BDU实时数据
    GetRealData(&tBcmData);         // 获得实时数据
    GetCtrlOut(&tCtrlOut);
#ifdef INTELLIGENT_PEAK_SHIFTING
    GetFmRealData(&tFmRealData);
#endif
    wDefenceStatus = GetDefenceStatus();

    tBcmData.wBattHighPrecSOC   = s_tBattOut.wBattHighPrecSOC;//高精度SOC
    tBcmData.wBattSOH           = s_tBattOut.wBatSOH;
    tBcmData.wBattSOC           = s_tBattOut.wBatSOC;
    tBcmData.ucCellEquSta       = s_tBattOut.ucCellEquSta; //电芯均衡状态
    tBcmData.ucBattPackSta      = s_tBattOut.ucBatStatus;
//    tBcmData.bBattFull          = (s_tBattOut.ucBatStatus == BATT_MODE_STANDBY) ? 1 : 0;    //电池充满电标志  1104协议
    tBcmData.fDischObjVolt      = s_tBattDeal.fDischargeHopeVol;

    tBcmData.wBattCycleTimes    = s_tBattDeal.wBattCycleTimes;
    tBcmData.fVoltChange        = 0;//s_tBattDeal.fSlavePowerDownCompVolt;
    tBcmData.fBattChgReqCurr    = s_tBattDeal.fChargeCurrHope;
    tBcmData.fBusChgReqCurr     = s_tBattDeal.fChargeBusSetCurr;
    tBcmData.fBattChgReqVolt    = s_tBattIn.tPara.fChargeFullVol;
    tBcmData.fBattChgReqVolt    = MAX(s_tBattIn.tPara.fSysPowerOnThreshold, tBcmData.fBattChgReqVolt*g_ProConfig.fVoltTrasRate);

    tBcmData.fBattMaxCurrCap     = (FLOAT)g_ProConfig.fRateCurrDischg/s_tBattIn.tPara.wBattCap;
    tBcmData.ucBattBalBits       = s_tBattOut.wCellBalVoltBits;
    //add by xzx
    tBcmData.ucInputBreak          = s_tBattOut.ucInputBreak;
    tBcmData.wChargeLeftMinutes    = s_tBattOut.wChargeLeftMinutes;
    tBcmData.wDisChargeLeftMinutes = s_tBattOut.wDisChargeLeftMinutes;

    lTotalDischargeCap             = FLOAT_TO_LONG(s_tBattOut.fTotalDischargeCap);//累计放电容量
    tBcmData.wTotalDischgCapHigh   = (WORD)(lTotalDischargeCap / 65536);
    tBcmData.wTotalDischgCapLow    = (WORD)(lTotalDischargeCap % 65536);

    lTotalDischargePower           = FLOAT_TO_LONG(s_tBattOut.fTotalDischargeQuanty);//累计放电电量
    tBcmData.wTotalDischgQHigh     = (WORD)(lTotalDischargePower / 65536);
    tBcmData.wTotalDischgQLow      = (WORD)(lTotalDischargePower % 65536);
    tBcmData.fTotalDischargeQuanty = s_tBattOut.fTotalDischargeQuanty;

    lTotalChargeCap             = FLOAT_TO_LONG(s_tBattOut.fTotalChargeCap);//累计充电容量
    tBcmData.wTotalChgCapHigh   = (WORD)(lTotalChargeCap / 65536);
    tBcmData.wTotalChgCapLow    = (WORD)(lTotalChargeCap % 65536);

    lTotalChargePower           = FLOAT_TO_LONG(s_tBattOut.fTotalChargeQuanty);//累计充电电量
    tBcmData.wTotalChgQHigh     = (WORD)(lTotalChargePower / 65536);
    tBcmData.wTotalChgQLow      = (WORD)(lTotalChargePower % 65536);
    tBcmData.fTotalChargeQuanty = s_tBattOut.fTotalChargeQuanty;
    
    //add by xzx for modbus
    tBcmData.ucMasterSta           = (s_tBattOut.ucMasterStatus + 1)%2;//主从机状态
    tBcmData.ucCAN1Address         = GetBMSAddr();//CAN1地址
#ifdef USE_CAN2
    tBcmData.ucCAN2Address         = GetBMSCan2Addr();//CAN2地址
    tBcmData.ucCabMasterIdent      = cabinetMasterIdentifying(); //柜级主机标识  
#endif
    tBcmData.ucDefenceStatus       = wDefenceStatus & 0xff;//布防状态
    tBcmData.fSetChgVolt           = s_tBattOut.fSetChgVolt;   //当前设定充电电压
    tBcmData.fSetDischVolt         = s_tBattOut.fSetDischVolt; //当前设定放电电压
    tBcmData.wSetChgCurr           = s_tBattOut.wSetChgCurr; //设定电池侧充电限电流比例
    tBcmData.wSetBusChgCurr        = s_tBattOut.wSetBusChgCurr; //设定Bus侧充电限电流比例
    tBcmData.wSetDischCurr         = s_tBattOut.wSetDischCurr; //设定放电限电流比例
    tBcmData.ucThroughStatus       = (s_tBattDeal.bThroughChg || s_tBattDeal.bThroughDischg); //下发直通状态
    tBcmData.ucThroughChgStatus    = s_tBattDeal.bThroughChg;     // 下发充电直通状态
    tBcmData.ucThroughDischgStatus = s_tBattDeal.bThroughDischg;  // 下发放电直通状态
    tBcmData.fPowerOnVoltThre      = s_tBattOut.fPowerOnVolt; //来电电压阈值
    tBcmData.fPowerDownVoltThre    = s_tBattOut.fPowerdownVolt; //掉电电压阈值
    tBcmData.wBattPowerOffTime     = s_tBattOut.wBattPowerOffTimeRes; //停电时间
    tBcmData.ucTransChagEn         = tDCRealData.tDCStatus.bChange2ChgEn; //转充电允许
    tBcmData.wBatteryCap = s_tSysPara.wBatteryCap;   //电池额定容量
#ifdef INTELLIGENT_PEAK_SHIFTING
    tBcmData.ucPriceStatus = s_tBattDeal.ucElectricityPrice;   //当前错峰电价状态
#endif
    tBcmData.wRemainCap = FLOAT_TO_WORD(tBcmData.wBatteryCap*(s_tBattOut.wBatSOC/100.0));   //剩余容量 固网协议

#ifdef PAKISTAN_CMPAK_PROTOCOL
    tBcmData.wAccuBattRunTimeHigh  = (WORD)((s_tBattDeal.ulAccuBattRunTime / 60) / 65535); // 累计运行时间高字节
    tBcmData.wAccuBattRunTimeLow   = (WORD)((s_tBattDeal.ulAccuBattRunTime / 60) % 65535); // 累计运行时间低字节
    tBcmData.wDischgTimesHigh      = (WORD)(s_tBattDeal.ulAccuDischTimes / 65535);         // 放电次数高字节
    tBcmData.wDischgTimesLow       = (WORD)(s_tBattDeal.ulAccuDischTimes % 65535);         // 放电次数低字节
#endif

    for(i=0; i<OUTPUT_RLY_NUM ;i++)
    {
        tBcmData.aucOutputRly[i] = (tCtrlOut.wRelayOut&(1<<i)) ? 1 : 0;
        #ifdef OUTPUT_RLY_ACTION_RESTORE
        if (((BYTE)0==s_tSysPara.ucRelayDefaultStatus) && (!GetApptestFlag()))
        {
            tBcmData.aucOutputRly[i] ^= 1;
        }
        #endif
    }
    // R321 兼容法电 Modbus 相关实时数据
    #ifdef MODE_4G_REAL_DATA
    tBcmData.ucGPSSNR = GetGpsSnr();  //获取GPS载噪比
    #endif
    tBcmData.wBattUsableCap = FLOAT_TO_WORD(s_tSysPara.wBatteryCap * (s_tBattOut.wBatSOH/100.0));   // 电池可用容量
    tBcmData.fBattBusPower = (tBcmData.fBattBusPower+fabs(s_tBattIn.tData.fBusVol * s_tBattIn.tData.fBusCurr))/2;    // 母排功率 与上一次之和取平均作平滑处理
    tBcmData.fBattChgCurr = s_tBattOut.wSetChgCurr/1000.0 * s_tSysPara.wBatteryCap;         // 当前充电限流值
    tBcmData.fBattMaxchgCurr = g_ProConfig.fRateCurrChgBus;                                   // 最大充电限流值
    tBcmData.ucBattStatus = JudgeBatteryStatus_France();          // 电池状态 :0--充电; 1--放电; 2--idle(既不充电也不放电但是在线状态); 3--保护    
    tBcmData.wBattRemaingCap = FLOAT_TO_WORD(s_tSysPara.wBatteryCap*(s_tBattOut.wBatSOC/100.0));    // 电池剩余容量
    tBcmData.wGroupMaxSoc = s_tBattOut.wGroupMaxSoc;
#ifdef INTELLIGENT_PEAK_SHIFTING
    tFmRealData.fFMcurrentSingleMaxChgPower = GetSingleBatteryChargePower(s_tBattDeal.fReachMaxCurret,
                                                                       MIN(tBcmData.fBattVolt,tBcmData.fExterVolt)); //计算单台电池的最大充电功率
    tFmRealData.fCurrentMaxChgPower = s_tBattIn.tData.fFMCurrentMaxChgPower;    //站点最大可充电功率:需要添加主从机通信指令，下次提交加入
    tFmRealData.wFMBattSOCLeftCapacity = s_tBattIn.tData.ucFMBatteryleftcapacity;//站点剩余容量
    tFmRealData.ucCurrentFMStatus = MakeFMCurrentMode(s_tBattIn.tData.tFmData.ucFmStatus,
                                                     JudgePeakShiftCondition(&s_tBattIn,&s_tBattDeal,GetShiftPeakPara())); //当前模式：0备电、1调频、2错峰
    tFmRealData.ucChgAndDischgDisable = s_tBattIn.tData.ucFMChgAndDischgDisable;  //充放电禁止状态：0可充可放、1可充电、2可放电、3禁止充放电:需要添加主从机通信指令，下次提交加入
#endif
    tBcmData.wBattLeftEnergy = FLOAT_TO_WORD(GetValidData(((FLOAT)(s_wCellCycleTimes - tBcmData.wBattCycleTimes)/(FLOAT)s_wCellCycleTimes*100), 100, 0));

    if(BATT_MODE_CHARGE == s_tBattOut.ucBatStatus || BATT_MODE_STANDBY == s_tBattOut.ucBatStatus)
    {
        tBcmData.fBattChgVolt = s_tBattIn.tData.fBusVol;          // 电池充电电压 :充电以及充满时为母排电压，其他情况为0
    }
    else
    {
        tBcmData.fBattChgVolt = 0;
    }

    SetRealData(&tBcmData);
#ifdef INTELLIGENT_PEAK_SHIFTING
    SetFmRealData(&tFmRealData);
#endif
    return;
}

/* 判断电池状态 0--充电; 1--放电; 2--idle(既不充电也不放电但是在线状态); 3--保护 */
static BYTE JudgeBatteryStatus_France(void)
{
    if(s_tBattIn.tData.bChargeProtect || s_tBattIn.tData.bDischargeProtect){
        return 3; // 保护
    }
    if(s_tBattOut.ucBatStatus <= BATT_MODE_DISCHARGE && fabs(s_tBattIn.tData.fBattCurr) >= s_tBattIn.tData.fCurrMinDet){
        return s_tBattOut.ucBatStatus;  // 0：充电 1：放电 (且有充放电流)
    }
    else{
        return 2; // idle
    }
}

static void PrintDebugInfo(void)
{
    setRotateDebugInfo(&s_tRotateDebug, &s_tBattDeal);

    s_tRotateDebug.ucTransCounter = s_tBattDeal.ucTransCounter;
//    s_tRotateDebug.ucInitStat = s_tBattDeal.tRotate.ucMinRotateCurr;

    s_tRotateDebug.fBusMaxVol = s_tBattIn.tData.fBusVolMax;
    s_tRotateDebug.fMaxCurrInAll = s_tBattIn.tData.fMaxCurrInAll;

    // s_tRotateDebug.ucInitStat = s_tBattDeal.tRotate.ucRotateOpenCounter;
    // s_tRotateDebug.wMaxSOC = s_tBattDeal.tRotate.wRotateCloseCounter|(s_tBattDeal.tRotate.ucRotateCurrLimitCounter<<8);

    s_tSOCCalDebug.ucBatStatus = s_tBattOut.ucBatStatus;
    s_tSOCCalDebug.ucIfBduDischarge = IfBduDischarge();
    s_tSOCCalDebug.fBattCurr = s_tBattIn.tData.fBattCurr;
    s_tSOCCalDebug.fCellTempMax = s_tBattIn.tData.fCellTempMax;
    s_tSOCCalDebug.fBattVol = s_tBattIn.tData.fBatVol;
    s_tSOCCalDebug.fMinBatVol = s_tBattDeal.fMinBatVol;
    s_tSOCCalDebug.fMaxBatVol = s_tBattDeal.fMaxBatVol;
    s_tSOCCalDebug.fBattCap = s_tBattDeal.fBattCap;
    s_tSOCCalDebug.wBattSOC = s_tBattIn.tData.wBattSOC;
    s_tSOCCalDebug.ulDischargeSOCCalTimer = s_tBattDeal.ulDischargeSOCCalTimer;
    s_tSOCCalDebug.ulDischargeBatVolDiffTimer = s_tBattDeal.ulDischargeBatVolDiffTimer;

    /*printf("IfBduDischarge():%d, ucSOCCalStatus:%d, ucBatStatus:%d, Curr:%f, TempMax:%f, Vol:%f, fMinBatVol:%f, fMaxBatVol:%f, fBattCap:%f, wBattSOC:%d, CalTimer:%d, DiffTimer:%d\n", IfBduDischarge(), s_tSOCCalDebug.ucSOCCalStatus, 
    s_tSOCCalDebug.ucBatStatus, s_tSOCCalDebug.fBattCurr, s_tSOCCalDebug.fCellTempMax, s_tSOCCalDebug.fBattVol, 
    s_tSOCCalDebug.fMinBatVol, s_tSOCCalDebug.fMaxBatVol, s_tSOCCalDebug.fBattCap, s_tSOCCalDebug.wBattSOC,
    s_tSOCCalDebug.ulDischargeSOCCalTimer, s_tSOCCalDebug.ulDischargeBatVolDiffTimer);*/
}

Static void DealBattResult(void)
{
    FLOAT fChargeCurr = s_tBattDeal.bChargeCurrMin ? SMART_LI_CURRENT_MIN : s_tBattDeal.fChargeSetCurr;
    FLOAT fChgMaxCurr = s_tBattDeal.bChargeCurrMin ? SMART_LI_CURRENT_MIN : s_tBattDeal.fSysChgMaxCurr;
    /*
    todo:
    从Deal结构中计算Result结构数据
    */
    s_tBattOut.bCtlChargeDisable        = s_tBattIn.tData.bChargeProtect;
    s_tBattOut.bChargePrtAlm            = s_tBattIn.tData.bChargePrtAlarm;
    s_tBattOut.bCtrlDischargeDisable    = s_tBattIn.tData.bDischargeProtect;
    s_tBattOut.bDischargePrtAlm         = s_tBattIn.tData.bDischargePrtAlarm;
    s_tBattOut.ucBatStatus              = s_tBattDeal.ucChargeMode;

    TimerPlus(s_bSaveMasterSlaveChangeOps, 3);
    if(TimeOut(s_bSaveMasterSlaveChangeOps, 3)) // 延时3s判断
    {
        if(s_tBattOut.ucMasterStatus != IsMaster())
        {//当主从机状态发生切换
            if(s_tBattOut.ucMasterStatus == 0)
            {//原状态为从机
                //slave to master
                SaveAction(GetActionId(CONTOL_MASTER_SLAVE_CHANGE), "Slave to Master");
            }
            else
            {//原状态为主机
                //master to slave
                SaveAction(GetActionId(CONTOL_MASTER_SLAVE_CHANGE), "Master to Slave");
            }
        }
    }
    
    s_tBattOut.ucMasterStatus           = IsMaster();
#ifdef USE_CAN2
    if(TimeOut(s_bSaveMasterSlaveChangeOps, 3)) // 延时3s判断
    {
        if(s_tBattOutCAN2.ucMasterStatus != IsMasterCan2())
        {//当主从机状态发生切换
            if(s_tBattOutCAN2.ucMasterStatus == 0)
            {//原状态为从机
                //slave to master
                SaveAction(GetActionId(CONTOL_MASTER_SLAVE_CHANGE), "CAN2Slave to Master");
            }
            else
            {//原状态为主机
                //master to slave
                SaveAction(GetActionId(CONTOL_MASTER_SLAVE_CHANGE), "CAN2Master to Slave");
            }
        }
    }
    s_tBattOutCAN2.ucMasterStatus       = IsMasterCan2();
#endif
    s_tBattOut.wDischargeCap            = (WORD)(s_tBattDeal.fTotalDischargeCap*10 + 0.5);
    s_tBattOut.bLoopOff                 = s_tBattDeal.bLoopOff;

    //将计算的处理数据与结果对应起来
    s_tBattOut.wBatSOC                  = CalcSOCFromCap(&s_tBattDeal, s_tBattIn.tPara.wBattCap, 0);
    s_tBattOut.wBatSOH                  = (WORD)(s_tBattDeal.fBattSOH + 0.5);  // 四舍五入
    s_tBattOut.wBattHighPrecSOC         = CalcSOCFromCap(&s_tBattDeal, s_tBattIn.tPara.wBattCap, 2);

    // Added by fengfj, 2019-07-27 16:48:33
    s_tBattOut.ucAddedLifeDecline       = (BYTE)(10 * (fabs(s_tBattDeal.fAddedLifeDecline) + 0.5));
    s_tBattOut.wCalenderLifeDecline     = Float2Word(s_tBattDeal.fCalenderLifeDecline * 10);
    s_tBattOut.wCycleLifeDecline        = Float2Word(s_tBattDeal.fCycleLifeDecline * 10);
    s_tBattOut.ucInputBreak             = s_tBattIn.tData.bBduChargeBreak;
    s_tBattOut.ucDelayCounter           = s_tBattDeal.ucDischgDelayCounter;
    s_tBattOut.ucSourceEnough           = s_tBattDeal.bChargeCurrEnough;
//    s_tBattOut.wSysVoltPowerdown        = (WORD)(s_tBattDeal.fSysVoltOnPowerOff*100+0.5);

    //add by xzx
    s_tBattOut.wChargeLeftMinutes       = Float2Word(s_tBattDeal.fBattChargeLeftMinutes);
    s_tBattOut.wDisChargeLeftMinutes    = Float2Word(s_tBattDeal.fBattDisChargeLeftMinutes);
    s_tBattOut.fTotalDischargeCap       = s_tBattDeal.fMultiDischargeCap;
    s_tBattOut.fTotalDischargeQuanty    = s_tBattDeal.fMultiDischargePower;
    s_tBattOut.fTotalChargeCap          = s_tBattDeal.fMultiChargeCap;
    s_tBattOut.fTotalChargeQuanty       = s_tBattDeal.fMultiChargePower;
    s_tBattOut.fPowerdownVolt           = s_tBattDeal.fPowerDownVolt + s_tBattDeal.fSlavePowerDownCompVolt; //debug,需要小于来电电压阈值，否则容易充放电来回切换
    s_tBattOut.fPowerOnVolt             = s_tBattIn.tPara.fSysPowerOnThreshold;
    s_tBattOut.fPowerOffVolt            = s_tBattIn.tPara.fSysPowerOffThreshold;
    // End Added

    // Added by fengfj, 2022-03-21 20:02:47
    s_tBattOut.ucCellPrtRestoreCounter  = s_tBattDeal.ucPMOSRestoreCounter;
    s_tBattOut.ucCellProtectCounter     = s_tBattDeal.ucCellProtectCounter;
    s_tBattOut.ucCellProtectStatus      = (BYTE)s_tBattIn.tData.bCellProtectStatus;
    s_tBattOut.wBattPowerOffTimeRes     = s_tBattDeal.wPowerOffTime;
    // End Added
    s_tBattOut.ucSleepBattStatus        = s_tBattDeal.ucSleepBattStatus;

    if(CalcSOCFromCap(&s_tBattDeal, s_tBattIn.tPara.wBattCap, 0) >= 99)
    {
        s_tBattOut.wFullChargeLastDura      = 0;
    }
    else
    {
        s_tBattOut.wFullChargeLastDura      = s_tBattDeal.wFullChargeLastDura;
    }                      //充满计时B3 新添加

    DefenseBattChargeMinutesCount();//电池丢失告警级别为屏蔽并且防盗线连接正确时才会计算充电时长是否满三分钟
    s_tBattOut.slDefenseBattChargeMinutes = s_tBattDeal.slDefenseBattChargeMinutes;
    s_tBattDeal.bSmooth             =  TRUE;
    if(GetQtptestFlag())
    {
        s_tBattOut.fSetDischVolt        = GetValidData(s_tBattDeal.fDischargeSetVol*0.35+s_tBattOut.fSetDischVolt*0.65, OUT_VOLT_MAX, OUT_ASSIST_VOLT_MIN);
    }
    else if(s_tBattDeal.bActivateStart == TRUE)  //激活口激活
    {
        s_tBattOut.fSetDischVolt       = MIX_INIT_DISCHARGE_VOLT;
    }
    else if(NeedPlusOneOutVol())//达到静置关机条件时，母排电压不小于44V，提升母排电压1V持续30s，判断是否休眠
    {
       s_tBattOut.fSetDischVolt        = GetValidData(s_tBattDeal.fDischargeSetVol+1, OUT_VOLT_MAX, SMART_LI_OUTPUT_VOL_MIN);
    }
    else if(s_tBattDeal.wDischargeEndTimer != 0)//放电末期已切换
    {
        s_tBattOut.fSetDischVolt       = GetValidData(s_tBattDeal.fDischargeSetVol, OUT_VOLT_MAX, SMART_LI_OUTPUT_VOL_MIN);
    }
    else
    {
        s_tBattOut.fSetDischVolt        = GetValidData(s_tBattDeal.fDischargeSetVol*0.03+s_tBattOut.fSetDischVolt*0.97, OUT_VOLT_MAX, SMART_LI_OUTPUT_VOL_MIN);
    }

    ///s_tBattDeal.fDischargeLimitCurr这里初始化值必须修改，否则导致s_tBattOut.wSetDischCurr会被错误计算为20
    s_tBattOut.wSetDischCurr            = s_tBattDeal.fDischargeLimitCurr/g_ProConfig.fRateCurrDischg * BDU_LIMIT_MAX;
    s_tBattOut.wSetDischCurr            = MAX(s_tBattOut.wSetDischCurr, (WORD)BDCU_DISCHAGRE_LIMIT_MIN);
    s_tBattOut.wSetChgCurr              = fChargeCurr/g_ProConfig.fRateCurrChg * BDU_LIMIT_MAX;
    s_tBattOut.wSetChgCurr              = GetValidData(s_tBattOut.wSetChgCurr, BDU_LIMIT_MAX, BDCU_CHARGE_BAT_LIMIT_MIN);
    s_tBattOut.wSetBusChgCurr              = s_tBattDeal.fChargeBusSetCurr/g_ProConfig.fRateCurrChgBus * BDU_LIMIT_MAX;
    s_tBattOut.wSetBusChgCurr              = GetValidData(s_tBattOut.wSetBusChgCurr, BDU_LIMIT_MAX, BDCU_CHARGE_BUS_LIMIT_MIN);
    s_tBattOut.ucRotateMaxCurr  = s_tBattDeal.ucRotateMaxCurr;
    s_tBattOut.bB3BattExist = s_tBattIn.tData.bB3BattExist;
    s_tBattOut.fBalanceCurr = s_tBattIn.tData.fBalanceCurr;
    s_tBattOut.fAveCurrInCluster = s_tBattIn.tData.fAveCurrInCluster;
    s_tBattOut.wAveSocInCluster = s_tBattIn.tData.wAveSocInCluster;
    s_tBattOut.wAveSocInAllCluster = GetAveSocInAllCluster();
    s_tBattOut.fAveBattVoltInCluster = s_tBattIn.tData.fAveBattVolt;
    s_tBattOut.wGroupMaxSoc = s_tBattDeal.wGroupMaxSoc;
    s_tBattDeal.ucLastFmSta = s_tBattIn.tData.tFmData.ucFmStatus;
    if(s_tBattIn.tPara.ucUsageScen == DISCHG_MODE_BATT_CHARACTERISTIC)
    {
        s_tBattOut.fVoltAdjVal = 0.0f;
    }
    else
    {
        s_tBattOut.fVoltAdjVal = s_tBattDeal.fDischargeHopeVol - s_tBattIn.tData.fAveBattVolt;
    }

    // if (s_tBattDeal.bNeedLimitCurrCharge)
    // {
    //     fChgMaxCurr = 10;
    // }
    setChargeCurr((WORD)(fChgMaxCurr*100), (WORD)(s_tBattIn.tData.fBusVolMax*100 + 0.5),FLOAT_TO_SHORT_POW(s_tBattIn.tData.fMaxCurrInAll, 2));

    s_tBattOut.fSetChgVolt = GetValidData(s_tBattDeal.fChargeSetVol, s_tBattIn.tPara.fChargeFullVol, VOLT_VALID_MIN/15.0*s_tHardwarePara.ucCellVoltNum);

    s_tBattOut.fLastBusVolt = s_tBattDeal.fBusAvgVoltReal;
    #ifdef ZXESM_R321_APTOWER_VERSION
    s_tBattOut.ucBattIdleStatus = s_tBattDeal.ucBattIdleStatus;
    #endif

    CheckAndSetIOPara();

    CheckCtrlStatus();

    CheckAndSetCtrlPara();

    CtrlChargeRotate();

    PrintDebugInfo();

    return;
}

static void CheckAndSetIOPara(void)
{
    T_DCPara tDCPara;

    GetBduPara(&tDCPara);

    
#ifdef MONITORING_BASED_CHARGE_AND_DISCHARGE_OVERCURRENT_PROTECTION_ENABLED
    tDCPara.wChgOverCurVal = Float2Word(BDCU_CHARGE_OVER_CURRENT);          // 充电过流保护阈值，精度2，以D121功率兜底保护点固定数值下发
    tDCPara.wDischOverCurVal = Float2Word(BDCU_DISCHARGE_OVER_CURRENT);     // 放电过流保护阈值，精度2，以D121功率兜底保护点固定数值下发
#endif
    tDCPara.wBatOverVolVal = Float2Word(s_tSysPara.fBattOverVoltPrtThre / g_ProConfig.fVoltTrasRate *100);                // 电池组过压保护阈值，精度2
    tDCPara.wDropVol = Float2Word(s_tBattDeal.fPowerDownVolt*100);                                           // 母排掉电阈值，精度2
    //tDCPara.wBusChgVoltValLow = wBusChgVoltValLow;                                                         // BUS充电最低电压阀值，精度2
    tDCPara.wChgCurVal = s_tBattOut.wSetChgCurr;                                                             // 当前电池侧充电限电流，千分比
    tDCPara.wChgBusCurVal = s_tBattOut.wSetBusChgCurr;                                                       // 当前Bus侧充电限电流，千分比
    tDCPara.wDisChgCurVal = s_tBattOut.wSetDischCurr;                                                        // 当前放电限电流，千分比
    tDCPara.wChgVolVal = Float2Word(s_tBattOut.fSetChgVolt*100);                                             // 当前充电电压，精度2
    tDCPara.wDischgVolVal = GetValidData(Float2Word(s_tBattOut.fSetDischVolt*100), DISCHARGE_VOLT_H, DISCHARGE_VOLT_L);         // 当前放电电压，精度2

#ifdef MAX_CHARGE_AND_DISCHARGE_POWER_ENABLED
    tDCPara.wMaxChgPowerVal = s_tSysPara.wChargeMaxPower;                                                    // 最大充电功率
    tDCPara.wMaxDisChgPowerVal = s_tSysPara.wDischargeMaxPower;                                              // 最大放电功率
#endif

    SetBduPara(&tDCPara);
}

static void CheckAndSetCtrlPara(void)
{
#ifdef MONITORING_BASED_EQUALIZE_CURRENT_ENABLED
    SetEqualizeCurr(fabs(s_tBattDeal.fDischgEqualCurr), s_tBattIn.tPara.wBattCap, s_tBattDeal.bMasterChgFull);
#endif
    return;
}

Static void CheckBduWhenStop(void)
{
     /*
        1.优先根据充放电状态发生充放电命令；
        2.如果充放电状态不是充电或者放电，则根据保护状态发生命令；
        实际上，BDU只会响应两种命令中的一种，应该控制只发一种命令
        */
    if (IsSleep())
    {
        return;
    }        

    if (BATT_MODE_CHARGE == s_tBattDeal.ucChargeMode && !s_tBattIn.tData.bChargeProtect && !IsTransPeriod())
    {
        s_tBattDeal.ucBduCtrlMode = BDU_CHG_START;
        //PrintNowStatus(CHARGE);
    }
    else if (BATT_MODE_DISCHARGE == s_tBattDeal.ucChargeMode && 
        !s_tBattIn.tData.bDischargeProtect && !IfSmartLiDischgStopDelay())
    {
        s_tBattDeal.ucBduCtrlMode = BDU_DISCHG_START;
        //PrintNowStatus(DISCHARGE);
    }

    return;
}

static void CheckChargeStatus(void)
{
    //如果直通码变化,下发SCI控制指令1 20210115
    if (True == s_tBattDeal.bThroughChg) {
        BduCtrl(SCI_CTRL_CHG_STG, ENABLE);
    } else {
        BduCtrl(SCI_CTRL_CHG_STG, DISABLE);
    }

    if (True == s_tBattDeal.bThroughDischg) {
        BduCtrl(SCI_CTRL_DISCHG_STG, ENABLE);
    } else {
        BduCtrl(SCI_CTRL_DISCHG_STG, DISABLE);
    }

    if(IfBattBuckCharge())
    {
        BduCtrl(SCI_CTRL_CHG_BST, DISABLE);
    }
    else
    {
        BduCtrl(SCI_CTRL_CHG_BST, ENABLE);
    }

    if((s_tBattDeal.bThroughChg != s_tBattIn.tData.ucReceivedStaitChg) ||
       (s_tBattDeal.bThroughDischg != s_tBattIn.tData.ucReceivedStaitDischg))
    {
        if(s_tBattDeal.ucBatStatus == BATT_MODE_CHARGE && !IsTransPeriod())
        {
            s_tBattDeal.ucBduCtrlMode = BDU_CHG_START;
            //PrintNowStatus(CHARGE);        
        }
        else if(s_tBattDeal.ucBatStatus == BATT_MODE_DISCHARGE)
        {
            s_tBattDeal.ucBduCtrlMode = BDU_DISCHG_START;
            //PrintNowStatus(DISCHARGE);    
        }
    }

    ///--sleep and wakeup are more prior than start charge/discharge command.

    if (!s_tBattIn.tData.bBduSleep && IsSleep())
    {
        s_tBattDeal.ucBduCtrlMode = BDU_SLEEP_START;
    }
    else if (s_tBattIn.tData.bBduSleep && !IsSleep())
    {
        s_tBattDeal.ucBduCtrlMode = BDU_SLEEP_STOP;
    }
    else if (BDU_STATUS_STOP == s_tBattIn.tData.ucBDUStatus || BDU_STATUS_FAILURE == s_tBattIn.tData.ucBDUStatus)
    {
       CheckBduWhenStop();
    }

    if ((BYTE)BDU_CTRL_NULL != s_tBattDeal.ucBduCtrlMode)
    {
        BduCtrl(s_tBattDeal.ucBduCtrlMode&0x7F, s_tBattDeal.ucBduCtrlMode>>7);
    }

    return;
}

BOOLEAN GetBattRotateDisable(void)
{
    return s_tBattDeal.bBattRotateDisable;
}

static void CheckProtectStatus(void)
{
 //   BYTE ucCtrl = 0;

    //控制充放电保护，暂时放在这里
//    if ( s_tBattIn.tData.bBduChgDisable == (s_tBattIn.tData.bChargeProtect | s_tBattDeal.bBattRotateDisable)
//        && s_tBattIn.tData.bBduDischgDisable == s_tBattIn.tData.bDischargeProtect )
//    {
//        return;
//    }
//    if (s_tBattIn.tData.bChargeProtect || s_tBattDeal.bBattRotateDisable || (BATT_MODE_STANDBY == s_tBattDeal.ucChargeMode))

    if ((s_tBattIn.tData.bChargeProtect || (BATT_MODE_STANDBY == s_tBattDeal.ucChargeMode) || GetChageProtectFlag() || s_tBattDeal.ucDcrStatus == DCR_STOP_CHARGE)//DCR控制停止充电时需要控制功率充电保护
        || !TimeOut(s_tBattDeal.ucCtrlChgAndDischgDelay, CTRL_CHG_DISCHG_DELAY) || s_tBattDeal.bBattRotateDisable
        || s_tBattDeal.bPeakShiftChgDisable || s_tBattDeal.bFmChgDisable)
    {
        BduCtrl(SCI_CTRL_CHG_PRT, ENABLE); //ucCtrl |= 0x01;
    }
    else
    {
        BduCtrl(SCI_CTRL_CHG_PRT, DISABLE);
    }
    #ifdef POWER_OFF_PROTECTION
    if ( (s_tBattIn.tData.bDischargeProtect || IsPowerOffTooLong()) || !TimeOut(s_tBattDeal.ucCtrlChgAndDischgDelay, CTRL_CHG_DISCHG_DELAY))
    {
        BduCtrl(SCI_CTRL_DISCHG_PRT, ENABLE); //ucCtrl |= 0x02;
    }
    #else
    if ( (s_tBattIn.tData.bDischargeProtect && s_tBattDeal.bActivateStart == FALSE) || (s_tBattIn.tData.bActivatePortProtect == TRUE && s_tBattDeal.bActivateStart == TRUE) || !TimeOut(s_tBattDeal.ucCtrlChgAndDischgDelay, CTRL_CHG_DISCHG_DELAY))
    {
        BduCtrl(SCI_CTRL_DISCHG_PRT, ENABLE); //ucCtrl |= 0x02;
    }
    #endif
    else
    {
        BduCtrl(SCI_CTRL_DISCHG_PRT, DISABLE);
    }
    return;
}

Static void CheckCtrlStatus(void)
{
    T_DCRealData tDCReal;
    GetBduReal(&tDCReal);       //获取BDU实时数据
    CheckChargeStatus();
    CheckProtectStatus();
    #ifdef DEVICE_USING_R321
    //控制防水检测状态
    if (s_tSysPara.ucWaterIngrnessEn != tDCReal.tDCStatus.bWaterIngrnessEn)
    {
        BduCtrl(SCI_CTRL_WATER_INGRNESS,s_tSysPara.ucWaterIngrnessEn);
    }
    #endif
    return;
}

void    GetBattResult( T_BattResult *pBattOut )
{
    if (pBattOut == NULL)
    {
        return;
    }
    rt_memcpy( pBattOut, &s_tBattOut, sizeof(T_BattResult) );
    return;
}

void SetBattResult( T_BattResult *pBattOut )
{
    if (pBattOut == NULL)
    {
        return;
    }
    rt_memcpy( &s_tBattOut, pBattOut, sizeof(T_BattResult) );
    return;
}

/*
check if the condition ok N times.
*/
BOOLEAN IfConditionOkNTimes(BOOLEAN condition, WORD *pucCounter, WORD ucMaxTimes)
{
    if (condition)
    {
        if (*pucCounter >= ucMaxTimes)
        {
            return True;
        }
        else
        {
           (*pucCounter) += 1;
        }
    }
    else
    {   
        *pucCounter = 0;
    }

    return False;
}

BOOLEAN IsTransPeriod(void)
{
    return s_tBattDeal.ucTransCounter < TRANS_DELAY_MAX;
}

static BOOLEAN SmartLi_IsPowerDown( void )
{
    BOOLEAN bPowerOff = False;
    if(SmartLi_IsPowerDown_oper != NULL)
    {
        bPowerOff = SmartLi_IsPowerDown_oper(&s_tBattDeal, &s_tBattIn);
    }
    return bPowerOff;
}

// static void ClearSysVoltChange(void)
// {
//    s_tBattDeal.fSysVoltChange = 0.0;
//    return;
// }

/*
ucProtType
#define NPROTOCOL_TYPE_RS485_1363     0
#define NPROTOCOL_TYPE_RS485_MODBUS   1
#define NPROTOCOL_TYPE_CAN_1363     2
#define NPROTOCOL_TYPE_CAN_MODBUS   3

InfoType
#define INFO_TYPE_ALL           0
#define INFO_TYPE_STATUS        1
#define INFO_TYPE_OUT_VOLT      2
#define INFO_TYPE_CHG_COEF      3
#define INFO_TYPE_CHG_PRIOR     4


ucStatus: 
POWER_STATUS_ON 0--有电；
POWER_STATUS_OFF 1--停电；
POWER_STATUS_INVALID 0XFF--通信断以后置位
*/


// void ClearProtoSetValue(BYTE ucInfoType)
// {
//     if(ucInfoType >= INFO_MAX_NUM )
//     {
//         return;
//     }

//     s_tBattIn.tData.atProtoSetValue[ucInfoType].sValue = 0;
//     return;
// }


void SetProtoSetInfoInvalid(BYTE ucInfoType)
{
    BYTE i=0;
    T_ProtocolSetInfoStruct* ptInfo = &s_tBattIn.tData.atProtoSetValue[0];

    if(ucInfoType >= INFO_MAX_NUM )
    {
        return;
    }

    if (ucInfoType != INFO_TYPE_ALL)
    {
        s_tBattIn.tData.atProtoSetValue[ucInfoType].bValid = False;
        return;
    }
    
    for (i=0; i<INFO_MAX_NUM; i++)
    {
        ptInfo->bValid = False;
        ptInfo++;
    }

    return;
}

Bool IsProtoSetInfoInvalid(BYTE ucRunMode)
{
    BYTE i=0;
    T_ProtocolSetInfoStruct* ptInfo = &s_tBattIn.tData.atProtoSetValue[0];

    for (i=0; i<INFO_MAX_NUM; i++)
    {
        if (ptInfo->bValid && ((ptInfo->ucRunMode == ucRunMode)|| (RUN_MODE_ALL == ucRunMode)))
        {
            return FALSE;
        }
        ptInfo++;
    }

    return TRUE;

}

INT32 GetValueFromProtocol(BYTE ucInfoType, T_ProtocolSetInfoStruct* ptSetVarOut)
{
    T_ProtocolSetInfoStruct* ptSetVal = NULL;

    if (ucInfoType >= INFO_MAX_NUM)
    {
        return FAILURE;
    }

    ptSetVal = &s_tBattIn.tData.atProtoSetValue[ucInfoType];

    ////clear the validflag.
    if (NULL != ptSetVarOut)
    {
        rt_memset(ptSetVarOut, 0x00, sizeof(T_ProtocolSetInfoStruct));
    }

    ////the sequence can not be change.valid flag must be cleared before.
    if (RUN_MODE_CONTROLLED != s_tBattIn.tPara.ucRunMode)
    {
        if (GetQtptestFlag() && ucInfoType == INFO_TYPE_CHG_COEF) //支持外协模式下,非受控设置充电电流 20210329 mwl
        {
        }
        else if (INFO_TYPE_SET_MIX_BATT_CURR == ucInfoType || INFO_TYPE_DISCH_VOLT_FREE == ucInfoType || INFO_TYPE_CHG_COEF_FREE == ucInfoType || INFO_TYPE_SET_MAX_SOC == ucInfoType)
        {
        }
        else if(ucInfoType != INFO_TYPE_STATUS)   //支持非受控下交流状态下发 20210218 mwl
        {
            return FAILURE;
        }
    }

    if(IsProtoSetInfoInvalid(RUN_MODE_ALL))
    {
        return FAILURE;
    }

    if (ptSetVal->bValid)
    {
        if (NULL != ptSetVarOut)
        {
            rt_memcpy(ptSetVarOut, ptSetVal, sizeof(T_ProtocolSetInfoStruct));
        }
        return SUCCESSFUL;
    }

    return FAILURE;
}

/***************************************************************************
 * @brief   簇间主机给簇间从机同步目标数据
 **************************************************************************/
BOOLEAN SynCAN2ObjData(T_Can2ObjData* ptObjData)
{
    if(NULL == ptObjData)
    {
        return False;
    }

    if(!IsMaster())
    {
        return False;
    }
    
    rt_memcpy((BYTE*)&s_tBattDeal.tEqualObjData, (BYTE*)ptObjData, sizeof(T_Can2ObjData));
    if(!s_tBattDeal.tEqualObjData.bValid)
    {
        return False;
    }

    if(fabs(s_tBattIn.tData.fAveCurrInCluster) < s_tBattDeal.tEqualObjData.fObjCurr - CURR_ADJUST_DEVIATION)
    {
        s_tBattDeal.fCurrAdj += CURR_ADJUST_STEP;
    }
    else if(fabs(s_tBattIn.tData.fAveCurrInCluster) > s_tBattDeal.tEqualObjData.fObjCurr + CURR_ADJUST_DEVIATION)
    {
        s_tBattDeal.fCurrAdj -= CURR_ADJUST_STEP;
    }
    s_tBattDeal.fCurrAdj = GetValidData(s_tBattDeal.fCurrAdj, CURR_ADJUST_VOLT_UPPER, CURR_ADJUST_VOLT_LOWER);

    s_tBattDeal.fSocAdj = (s_tBattIn.tData.wAveSocInCluster - s_tBattDeal.tEqualObjData.wObjSoc) * SOC_ADJUST_VOLT_SLOPE;
    s_tBattDeal.fSocAdj = GetValidData(s_tBattDeal.fSocAdj, SOC_ADJUST_VOLT_UPPER, SOC_ADJUST_VOLT_LOWER);
    
    s_tBattDeal.fAdjVolt = s_tBattIn.tData.fAveBattVolt + s_tBattDeal.fCurrAdj + s_tBattDeal.fSocAdj + s_tBattDeal.tEqualObjData.fVolAdj;
    return True;
}

void SetACStatus( BOOLEAN bACStatus )
{
    if(s_tBattDeal.bActivateStart == TRUE || s_tBattDeal.bActivatePortCurrError == TRUE)
    {
        return;
    }

    if(!s_tBattDeal.bLoopOff)
    {
        s_tBattDeal.bPowerOff = bACStatus;
    }

    return;
}

void SetDischgVol( SHORT iVol, WORD wRefSOC )
{
    FLOAT fVol;
    if ((WORD)0 != wRefSOC)
    {
        fVol = wRefSOC/1000.0 - s_tBattDeal.fBattCap / (s_tBattDeal.fBattSOH * s_tSysPara.wBatteryCap*g_ProConfig.fCapTransRate / 100 ) *10.0;
    }
    else
    {
        fVol = 0;       //兼容B1不均SOC
    }

    fVol = s_tBattIn.tData.fBatVol * g_ProConfig.fVoltTrasRate + iVol/100.0 - GetValidData(2.0*fVol, 0.8, -0.8);

    if(s_tBattIn.tData.ucBduDischargePrtStatus || s_tBattIn.tData.bBduDischgDisable)
    {/////放电保护时不要接受主机的升压值按照电池电压进行输出
        s_tBattDeal.fDischargeHopeVol = s_tBattIn.tData.fBatVol * g_ProConfig.fVoltTrasRate;
    }
    else
    {
        s_tBattDeal.fDischargeHopeVol = GetValidData( fVol, OUT_VOLT_MAX, OUT_VOLT_MIN );
    }

    s_tBattDeal.fDischargeSetVol  = s_tBattDeal.fDischargeHopeVol;

    if((BYTE)DISCHG_MODE_BATT_CHARACTERISTIC == s_tBattIn.tPara.ucUsageScen && s_tBattIn.tPara.bThroughDischgEnable && BATT_DISCHARGE_FOLLOW == s_tBattIn.tPara.ucDischargeMode)
    {
        s_tBattDeal.fDischargeHopeVol = MAX(s_tBattIn.tData.fBatVol * g_ProConfig.fVoltTrasRate, OUT_VOLT_MIN);
    }
    return;
}

void SetChgHopeVol(FLOAT fVol)
{
    s_tBattDeal.fChargeHopeVol = fVol;
    return;
}

void SetBattChargeCurrAndMaxBusVol(FLOAT fChargeCurr, FLOAT fMaxVol, FLOAT fMaxCurr)
{
    s_tBattDeal.bChargeCurrMin      = False;
    s_tBattDeal.fSysChgMaxCurr      = fChargeCurr;
    s_tBattDeal.fChargeBusSetCurr      = GetValidData(fChargeCurr, 
                                                   s_tBattDeal.fChargeBusCurrHope, 
                                                   SMART_LI_CURRENT_MIN);
    
    ///if master reset, slave data will abnormal.
    s_tBattIn.tData.fBusVolMax = MAX(fMaxVol, s_tBattIn.tData.fBusVol);
    s_tBattIn.tData.fMaxCurrInAll = fMaxCurr;
    if (fabs(s_tBattIn.tData.fMaxCurrInAll) < fabs(s_tBattIn.tData.fBattCurr))
    {
        s_tBattIn.tData.fMaxCurrInAll = s_tBattIn.tData.fBattCurr;
    }

    return;
}

static BOOLEAN GetOverPrtAlm(T_BCMAlarmStruct const* ptAlm)
{
    BYTE i=0;

    if (NULL == ptAlm)
    {
        return FALSE;
    }

    for (i=0; i<s_tHardwarePara.ucCellVoltNum; i++)
    {
        if (ptAlm->aucCellOverVoltPrt[i] != (BYTE)NORMAL 
         && s_tSysPara.fCellOverVoltPrtThre >= 3.7f)
        {
            return TRUE;
        }
    }

    return FALSE;
}

static BOOLEAN GetRealOverPrtAlm(T_BCMAlarmStruct const* ptAlm)
{
    BYTE i=0;

    if (NULL == ptAlm)
    {
        return FALSE;
    }

    for (i=0; i<s_tHardwarePara.ucCellVoltNum; i++)
    {
        if (ptAlm->aucCellOverVoltPrt[i] != (BYTE)NORMAL)
        {
            return TRUE;
        }
    }

    return FALSE;
}

static BOOLEAN GetLowPrtAlm(T_BCMAlarmStruct const* ptAlm)
{
    BYTE i=0;

    if (NULL == ptAlm)
    {
        return FALSE;
    }
    for (i=0; i<s_tHardwarePara.ucCellVoltNum; i++)
    {
        if ((ptAlm->ucBattUnderVoltPrt != (BYTE)NORMAL) || (ptAlm->aucCellUnderVoltPrt[i] != (BYTE)NORMAL ) || (ptAlm->aucCellDynamicUnderVoltPrt[i] != (BYTE)NORMAL))
        {
            return TRUE;
        }
    }
    return FALSE;
}
static BOOLEAN GetBattOverPrtAlm(T_BCMAlarmStruct const* ptAlm)
{
    if (NULL == ptAlm)
    {
        return FALSE;
    }

    if (ptAlm->ucBattOverVoltPrt != (BYTE)NORMAL)
    {
        return TRUE;
    }

    return FALSE;
}

static BOOLEAN GetBattOverCurrPrtAlm(T_BCMAlarmStruct const* ptAlm)
{
    if (NULL == ptAlm)
    {
        return FALSE;
    }

    if (ptAlm->ucChgCurrHighPrt != (BYTE)NORMAL || ptAlm->ucDischgCurrHighPrt != (BYTE)NORMAL)
    {
        return TRUE;
    }

    return FALSE;
}

BOOLEAN SetBattSOH(BYTE ucSOH)
{
    if(ucSOH > 100 || ucSOH <80)
    {
        return False;
    }

    if (s_tBattDeal.fAddedLifeDecline > (FLOAT)0 || s_tBattDeal.fAddedLifeDecline < ADDED_SOH_MIN 
        || (100.0 + s_tBattDeal.fAddedLifeDecline) < ucSOH )
    {
        s_tBattDeal.fAddedLifeDecline = 0.0f;
    }    
    
    s_tBattDeal.fCycleLifeDecline       = (100.0 + s_tBattDeal.fAddedLifeDecline - ucSOH)/2.0;
    s_tBattDeal.fCalenderLifeDecline    = s_tBattDeal.fCycleLifeDecline;
    
    return True;
}

BOOLEAN SetBattCycleTimes(WORD wTimes)
{
    if (wTimes <= 8000)
    {
        FLOAT fMultiDischargeCap = s_tBattIn.tPara.wBattCap*g_ProConfig.fCapTransRate*0.4*wTimes*(2 - 0.3/CYCLE_TIMES_MAX*wTimes);

        s_tBattDeal.wBattCycleTimes     = wTimes;
        s_tBattDeal.fMultiDischargeCap  = fMultiDischargeCap;
        s_tBattDeal.fMultiDischargePower = fMultiDischargeCap*0.05;
        s_tBattDeal.fMultiChargeCap = s_tBattDeal.fMultiDischargeCap;
        s_tBattDeal.fMultiChargePower = s_tBattDeal.fMultiDischargePower;
        s_tBattDeal.bSave2eeprom = True;
        return True;
    }

    return False;
}

BOOLEAN IfBattBuckCharge(void)
{
    if(s_tBattDeal.bCellUnderVolt)
    {
        return False;
    }
    if(!s_tBattIn.tPara.bBoostChg)
    {
        return True;
    }
    if(RUN_MODE_CONTROLLED == s_tBattIn.tPara.ucRunMode && IsProtoSetInfoInvalid(RUN_MODE_CONTROLLED))
    {
        return True;
    }
    if(RUN_MODE_FREE == s_tBattIn.tPara.ucRunMode && IsProtoSetInfoInvalid(RUN_MODE_FREE))
    {
        return True;
    }
    return False;
}

void checkApptestRst(void)
{
    if ( CheckSW_new(10, 15) )                //10s sleep btn reset  mwl 20201224
    {
        ResetMCU(NO_RESET_APPTEST) ;
    }

    return;
}

Static void CalcAccumulateData(void)
{
    FLOAT fBattDisChgCap = 0.0;
    FLOAT fBattChgCap = 0.0;

    if (s_tBattIn.tData.fBattCurr <= -1.0f*s_tBattIn.tData.fCurrMinDet)
    {////取正数
        fBattDisChgCap = fabs(s_tBattIn.tData.fBattCurr) / 60.0;
        s_tBattDeal.fMultiDischargeCap   += fBattDisChgCap * (s_tBattDeal.ulMinuteTimeDiffSec / 60.0) / g_ProConfig.fCapTransRate;      //D121 实际100AH  对外50AH
        s_tBattDeal.fMultiDischargePower += fBattDisChgCap * (s_tBattDeal.ulMinuteTimeDiffSec / 60.0) * s_tBattIn.tData.fBatVol / 1000;
    }
    else if(s_tBattIn.tData.fBattCurr >= s_tBattIn.tData.fCurrMinDet)
    {
        fBattChgCap = s_tBattIn.tData.fBattCurr / 60.0;
        s_tBattDeal.fMultiChargeCap    += fBattChgCap * (s_tBattDeal.ulMinuteTimeDiffSec / 60.0) / g_ProConfig.fCapTransRate; //累计充电容量
        s_tBattDeal.fMultiChargePower  += fBattChgCap * (s_tBattDeal.ulMinuteTimeDiffSec / 60.0) * s_tBattIn.tData.fBatVol / 1000; //累计充电电量
    }

    /*
    it will not save by itself.
    when current is not zero,soc will save if change 2%, and save the value together.
    if (fabs(s_tBattDeal.fBattCap - s_tBattDeal.fBattCapSave) >= s_tBattIn.tPara.wBattCap * 0.05 )
    {
        s_tBattDeal.bSave2eeprom = True;
    } */

    return;
}

Static void CalcBattLeftMinutes(void)
{
    FLOAT fBattRealCap = (FLOAT)s_tBattIn.tPara.wBattCap * g_ProConfig.fCapTransRate * s_tBattDeal.fBattSOH / 100;

    if(BATT_MODE_STANDBY == s_tBattDeal.ucChargeMode)
    {
        s_tBattDeal.fBattChargeLeftMinutes = 0.0;
    }
    else if(s_tBattDeal.bChargeEndHold == True) //末期电流会比较小
    {
        if(s_tBattDeal.fBattChargeLeftMinutes > s_tBattIn.tPara.wChagEndHoldMinute)
        {
            s_tBattDeal.fBattChargeLeftMinutes = s_tBattIn.tPara.wChagEndHoldMinute;
        }
        else
        {
            s_tBattDeal.fBattChargeLeftMinutes = s_tBattDeal.fBattChargeLeftMinutes - 1.0;
        }
        s_tBattDeal.fBattChargeLeftMinutes = MAX(s_tBattDeal.fBattChargeLeftMinutes, 1.0f);
    }
    else if (s_tBattIn.tData.fBattCurr >= s_tBattIn.tData.fCurrMinDet)
    {
        s_tBattDeal.fBattChargeLeftMinutes = (fBattRealCap - s_tBattDeal.fBattCap) / s_tBattIn.tData.fBattCurr * 60;
        s_tBattDeal.fBattChargeLeftMinutes = MAX(s_tBattDeal.fBattChargeLeftMinutes, 2.0f);
    }
    /*else if(s_tBattIn.tData.bChargeProtect)
    {
        s_tBattDeal.fBattChargeLeftMinutes = 9999.0;
    }*/
    else
    {
        s_tBattDeal.fBattChargeLeftMinutes = 9999.0;
    }

    if(s_tBattIn.tData.bDischargeProtect)
    {
        s_tBattDeal.fBattDisChargeLeftMinutes = 0.0;
    }
    else if(s_tBattIn.tData.fBattCurr <= -s_tBattIn.tData.fCurrMinDet)
    {
        s_tBattDeal.fBattDisChargeLeftMinutes = s_tBattDeal.fBattCap / fabs(s_tBattIn.tData.fBattCurr) * 60;
    }
    else
    {
        s_tBattDeal.fBattDisChargeLeftMinutes = 9999.0;
    }

    return;
}

BOOLEAN GetLoopOffStat(void)
{
    return s_tBattDeal.bLoopOff;
}

// static void AdjustDischargeVolt(void)
// {      
// #ifdef CSU_SEND_CURR_ADJUST       //如果要支持CSU下发电流调整锂电输出电压,请定义该宏;20210219 mwl
//     FLOAT fDischargeCurrAdd = 0.0f;
//     FLOAT fCurrDiff = 0.0f;
//     T_ProtocolSetInfoStruct tProtoData;

//     rt_memset(&tProtoData, 0x00, sizeof(tProtoData));
//     if(FAILURE == GetValueFromProtocol(INFO_TYPE_SET_MIX_BATT_CURR, &tProtoData))
//     {
//         return;
//     }

//     fDischargeCurrAdd = tProtoData.sValue/100.0f;

//     if (fDischargeCurrAdd <= 2.0f && fDischargeCurrAdd >= -2.0f)
//     {
//         return;
//     }
//     else if (fDischargeCurrAdd < -2.0f)
//     {
//         fCurrDiff = fabs(fDischargeCurrAdd + 2.0f);
//         if(fCurrDiff > 10.0f)
//         {
//             s_tBattDeal.fDischargeHopeVol += 0.3f;
//         }
//         else if(fCurrDiff > 5.0f)
//         {
//             s_tBattDeal.fDischargeHopeVol += 0.2f;
//         }
//         else
//         {
//             s_tBattDeal.fDischargeHopeVol += 0.1f;
//         }
//     }
//     else
//     {
//         fCurrDiff = fabs(fDischargeCurrAdd - 2.0f);
//         if(fCurrDiff > 10.0f)
//         {
//             s_tBattDeal.fDischargeHopeVol -= 0.3f;
//         }
//         else if(fCurrDiff > 5.0f)
//         {
//             s_tBattDeal.fDischargeHopeVol -= 0.2f;
//         }
//         else
//         {
//             s_tBattDeal.fDischargeHopeVol -= 0.1f;
//         }
//     }

//     s_tBattDeal.fDischargeHopeVol = GetValidData(s_tBattDeal.fDischargeHopeVol, 
//         s_tBattIn.tPara.fSysPowerOnThreshold-0.3f, 
//         s_tBattIn.tPara.fSysPowerOffThreshold
//         );

//     ClearProtoSetValue(INFO_TYPE_SET_MIX_BATT_CURR);
//     return;
// #endif
// }

void ClearPowerdownVoltOffset(void)
{
    s_tBattDeal.fSlavePowerDownCompVolt = 0.00f;
    s_tBattDeal.bChargeEqualCurrValid = False;
    return;
}

void SetRotate(BYTE ucCurr, BOOLEAN bChargeDisable)
{
    s_tBattDeal.ucSlaveDisableCounter = 0;
    s_tBattDeal.bBattRotateDisable = bChargeDisable;
    if(ucCurr > 0)
    {
        s_tBattDeal.ucRotateMaxCurr = ucCurr;
    }
}

BOOLEAN GetSlaveRotateDisable(void)
{
    if(s_tBattDeal.bBattRotateDisable && s_tBattIn.tData.ucBDUStatus == BDU_STATUS_STOP)
    {
        return True;
    }
    return False;
}

void SetBattModeDisable(BYTE ucTemp)
{
    s_tBattDeal.bBattRotateDisable = ucTemp;
}

void SetRotateMaxCurr(BYTE ucCurr)
{
    s_tBattDeal.ucRotateMaxCurr = ucCurr;
}

void GetRotateDebugInfo(T_RotateDebugStruct* ptData)
{
    rt_memcpy(ptData, &s_tRotateDebug, sizeof(T_RotateDebugStruct));
    return;
}

void GetSOCCalDebugInfo(T_SOCCalDebugStruct* ptData)
{
    rt_memcpy(ptData, &s_tSOCCalDebug, sizeof(T_SOCCalDebugStruct));
    return;
}

WORD GetRealBattCap(void)
{
    return (WORD)(s_tBattDeal.fBattCap*100);
}

BYTE CtrlBattery(BYTE ucCtrlCode)
{
    CHAR buff[20] = {0};
    rt_uint16_t wActionId = 0;
    switch(ucCtrlCode)
    {
        case CTRL_BATTERY_CHARGE_START:
            s_tBattDeal.ucBattCtrlStat |= 0x02;
            break;
        case CTRL_BATTERY_CHARGE_STOP:
            s_tBattDeal.ucBattCtrlStat &= 0xFD;
            break;
        case CTRL_BATTERY_DISCHARGE_START:
            s_tBattDeal.ucBattCtrlStat |= 0x01;
            break;
        case CTRL_BATTERY_DISCHARGE_STOP:
            s_tBattDeal.ucBattCtrlStat &= 0xFE;
            break;
        default:
            return RTN_FAIL_COMMAND;
    }

    wActionId = GetCtrlActionId(ucCtrlCode);

    if (s_tBattOut.ucBattCtrlStat != s_tBattDeal.ucBattCtrlStat)
    {
        rt_snprintf_s(buff, sizeof(buff), "%x,%x", s_tBattOut.ucBattCtrlStat, s_tBattDeal.ucBattCtrlStat);
        SaveAction(GetActionId(wActionId), buff);
    }
    return 0;
}

BYTE getBattChargeMode(void)
{
    return s_tBattDeal.ucChargeMode;
}

BOOLEAN getSwUpgrade(void)
{
    return s_tBattOut.bSwUpgrade;
}

BOOLEAN GetStatusConsistFault(void)
{
    if (IfBduDischarge()&& BATT_MODE_CHARGE == s_tBattDeal.ucChargeMode)
    {
        return True;
    }
    else if (IfBduCharge() && BATT_MODE_DISCHARGE == s_tBattDeal.ucChargeMode)
    {
        return True;
    }

    return False;
}

BOOLEAN GetChgCurrStatus(void)
{
    FLOAT fBattThresh1 = 0.0f, fBattThresh2 = 0.0f;
    FLOAT fBusThresh1 = 0.0f, fBusThresh2 = 0.0f;

    if(!IfBduCharge())
    {
        return False;
    }

    fBattThresh1 = (FLOAT)s_tBattOut.wSetChgCurr / BDU_LIMIT_MAX * g_ProConfig.fRateCurrChg - 1.0f;
    fBattThresh2 = (FLOAT)s_tBattOut.wSetChgCurr / BDU_LIMIT_MAX * g_ProConfig.fRateCurrChg * 0.9f;
    fBusThresh1  = (FLOAT)s_tBattOut.wSetBusChgCurr / BDU_LIMIT_MAX * g_ProConfig.fRateCurrChgBus - 1.0f;
    fBusThresh2  = (FLOAT)s_tBattOut.wSetBusChgCurr / BDU_LIMIT_MAX * g_ProConfig.fRateCurrChgBus * 0.9f;

    if((s_tBattIn.tData.fBattCurr < fBattThresh1 && s_tBattIn.tData.fBattCurr < fBattThresh2)
        &&(s_tBattIn.tData.fBusCurr < fBusThresh1 && s_tBattIn.tData.fBusCurr < fBusThresh2))
    {
        return False;
    }
    return True;
}

void SetDisChargeVol(WORD wDisChargeVol)
{
    if (s_tBattDeal.bCellUnderVolt)
    {
        return;
    }
    wDisChargeVol = GetValidData(wDisChargeVol, DISCHARGE_VOLT_H, DISCHARGE_VOLT_L);
    s_tBattDeal.fDischargeHopeVol = wDisChargeVol/100.0;
    s_tBattDeal.fDischargeSetVol  = s_tBattDeal.fDischargeHopeVol;
    return;
}

void SetPowerdownVol(WORD wDisChargeVol)
{
    if (s_tBattDeal.bCellUnderVolt)
    {
        s_tBattDeal.fPowerDownVolt = s_tBattOut.fSetDischVolt;
        return;
    }
    s_tBattDeal.fPowerDownVolt = wDisChargeVol/100.0;
    return;
}

//获取执行回路异常判断标志
BYTE GetJudgeIfLoopFaultFlag(void)
{
    return s_ucJudgeIfLoopFault;
}

//当回路异常持续一小时后，调节电压2分钟，2分钟之内还是没有电流则认为回路异常
VOID CalculateAdjustVoltTime(void)
{
   TimerPlus(s_wNoOutputCurrTimer,120);
}

//设置是否存在外部有电状态为是的电池
VOID SetExternalPowerOnFlag(BOOLEAN Value)
{
    s_bExternalPowerOnFlag = Value;
}

//主机同步是否使用B3的均流方式
BOOLEAN SetCurrBalanceMethod(BOOLEAN bB3Exist)
{
    s_tBattIn.tData.bB3BattExist = bB3Exist;
    return True;
}

//获取本机的外部有电状态
BOOLEAN GetNativePowerOnStatus(void)
{
    return s_bNativePowerOnStatus;
}

//设置的外部有电状态
VOID SetNativePowerOnStatus(BOOLEAN Value)
{
    s_bNativePowerOnStatus = Value;
}

//回路异常判断时的电压调整入口函数
VOID LoopOffVoltAdjust(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo)
{
   
    BYTE ucJudgeIfLoopFaultFlag = GetJudgeIfLoopFaultFlag();

    if(ucJudgeIfLoopFaultFlag == INCREASE_VOLT_LOOP_FAULT)
    {
       CalculateAdjustVoltTime();
       ptBattDeal->fDischargeSetVol  = s_fCurrentBysVoltageBak + 1;
    }
    else if(ucJudgeIfLoopFaultFlag == DECREASE_VOLT_LOOP_FAULT)
    {
       CalculateAdjustVoltTime();
       ptBattDeal->fDischargeSetVol  = s_fCurrentBysVoltageBak - 1;
    }
    else
    {
        s_fCurrentBysVoltageBak = ptBattInfo->tData.fBusVol;
    }

    return;
}

/**
 * @brief 闭锁条件判断
 * @note  condition1: 电池电压大于12V/20V，且最高单节大于4.2V，整组电压与单体电压之和的差值不大于2V，持续2s
 *        condition2: 电池电压大于12V/20V，单体最高电压大于1.5V，整组电压与单体电压之和的差值不大于2V，并且任意单节低于1.5V,持续7s
 *        condition3: BMS测试记录存在且通过
 *       （condition1 || condition2）&& condition3 则闭锁
 */
Static BOOLEAN IsCellVoltHighLock(T_StateListenStruct *ptState, BYTE i)
{
    if (i >= CELL_VOL_NUM_MAX)
    {
        return False;
    }
    return (s_tBattIn.tData.fBatVol > GetBattVoltThresh() && s_tBattIn.tData.fCellVoltMax > 4.2f && fabs(s_tBattIn.tData.fBatVol - SumArray(s_tBattIn.tData.afCellVolt, s_tHardwarePara.ucCellVoltNum)) <= 2.0f);
}

Static BOOLEAN IsCellVoltLowLock(T_StateListenStruct *ptState, BYTE i)
{
    if (i >= CELL_VOL_NUM_MAX)
    {
        return False;
    }
    return (s_tBattIn.tData.fBatVol > GetBattVoltThresh() && s_tBattIn.tData.fCellVoltMax > 1.5f && s_tBattIn.tData.fCellVoltMin < 1.5f && fabs(s_tBattIn.tData.fBatVol - SumArray(s_tBattIn.tData.afCellVolt, s_tHardwarePara.ucCellVoltNum)) <= 2.0f);
}

Static BOOLEAN JudgeBduLock(void)
{
    if (s_tBattDeal.bCellVoltHighLock || s_tBattDeal.bCellVoltLowLock)
    {
        s_bCellDamageLock = True;
    }

    return s_bCellDamageLock;
}

Static BOOLEAN CheckBattLockState(void)
{
    BOOLEAN lockCond_1_2 = False;
    BOOLEAN lockCond_3 = False;
    T_DCCtrl ctrState = {0};
    LONG lFatRecordReadRtn = 0;
    T_FatRecordStruct t_FatRecord = {0};
    BYTE *p = (BYTE *)&t_FatRecord;
    T_DCRealData tDCRealData;
    // 是否已发闭锁控制指令
    GetBduCtrl(&ctrState);
    GetBduReal(&tDCRealData);
    if (1 == ctrState.ucLockCtrl || tDCRealData.tDCAlarm.bLockErr) // 控制闭锁前判断是否功率已判断闭锁，区分监控控制的闭锁
    {
        return False;
    }

#ifdef FIRE_CONTROL_ENABLED
    if(s_tBCMRealAlm.ucFireControlAlm)//存在消防告警时控制闭锁
    {
        // 闭锁控制
        BduCtrl(SCI_CTRL_BDULOCK, 1);
        SaveCtrlLockFlag();
        // 操作记录
        SaveAction(GetActionId(CONTOL_SET_LOCK), "FireAlm Ctrl Lock");
        broadSendFireAlm(True);
        return True;
    }
#endif 

    // condition1 || condition2
    lockCond_1_2 = JudgeBduLock();
    if (False == lockCond_1_2)
    {
        return False;
    }
    // condition3
    lFatRecordReadRtn = readFile("/fatRecord00", p, sizeof(T_FatRecordStruct));
    if (t_FatRecord.wCheckSum != CRC_Cal((BYTE *)&t_FatRecord, sizeof(T_FatRecordStruct) - 2) || lFatRecordReadRtn != sizeof(T_FatRecordStruct))
    {
        rt_memset(&t_FatRecord, 0, sizeof(T_FatRecordStruct));
    }
    lockCond_3 = (t_FatRecord.fatRecordResult == RESULT_PASS);
    if (False == lockCond_3)
    {
        return False;
    }
    // 闭锁控制
    BduCtrl(SCI_CTRL_BDULOCK, 1);
    SaveCtrlLockFlag();
    // 操作记录
    SaveAction(GetActionId(CONTOL_SET_LOCK), "Record Ctrl Lock");
    return True;
}




//接触器控制函数
VOID ContactorCtrl(BYTE ContactorCtr)
{
    BduCtrl(SCI_CTRL_CONTACTOR,ContactorCtr);

    return;
}

//判断是否产生单体过压保护
static BOOLEAN JudgeIfCellVoltProtect(T_BCMAlarmStruct *pBCMAlm)
{
    BYTE i = 0;

    for(i = 0;i < CELL_VOL_NUM;i++)
    {
        if(pBCMAlm->aucCellOverVoltPrt[i])
        {
            return True;
        }
    }
   
    return False;
}

//根据接触器断开次数和时间判断是否需要产生闭锁告警
static void JudgeIfGenerateBattLockAlarm(T_BCMAlarmStruct *pBCMAlm)
{
    T_DCRealData tDCRealData;
    T_DCCtrl ctrState;

    GetBduReal(&tDCRealData);
    GetBduCtrl(&ctrState);

   if(s_tVoltTwoClassPrt.wTimerConuter < s_wVoltTwoClassStatsTimeMinu)
   {
       if(s_tVoltTwoClassPrt.BreakRelayCounter >= 5)
       {
            if(1 == ctrState.ucLockCtrl || tDCRealData.tDCAlarm.bLockErr)
            {
                return;
            }
            //rt_kprintf("In 24 hours ,break contactor 5 times!\n");
            //下发闭锁控制指令，产生闭锁告警
            //s_tVoltTwoClassPrt.bGenerateBattLock = True;
            BduCtrl(SCI_CTRL_BDULOCK, ENABLE);
            SaveCtrlLockFlag();

            //保存操作记录
            if(s_tVoltTwoClassPrt.bGenerateBattLockFlag == False)
            {
                s_tVoltTwoClassPrt.bGenerateBattLockFlag = True;
                SaveAction(GetActionId(CONTOL_TWO_CLASS_LOCK), "V2Prt Ctrl Lock");
            }
       }
   }
   else
   {
        //当超过24小时，但接触器断开的次数少于5次，则重新开始计数
        if(!pBCMAlm->ucBDUBattLockAlm)
        {
            s_tVoltTwoClassPrt.BreakRelayCounter = 0;
            s_tVoltTwoClassPrt.wTimerConuter = 0;
            s_tVoltTwoClassPrt.TimerStatus = False;
        }
   }

   return;

}

//断开接触器判断
static void JudgeDisconnectContactor(T_SysPara *pSysPara)
{
    if(pSysPara->fCellOverVoltPrtThre - pSysPara->fCellChargeFullVolt > 0.1 
       && s_tVoltTwoClassPrt.bCellProtectFlag != s_tVoltTwoClassPrt.bCellProtectFlagBak)
    {
        s_tVoltTwoClassPrt.bJudgePrtVoltFlag = True;
    }

    if( s_tVoltTwoClassPrt.bJudgePrtVoltFlag && (s_tVoltTwoClassPrt.ContactorStatusFlag == False))
    {
        //rt_kprintf("current Cell Voltage is 3.81V,break Contactor! break times:%d\n",s_tVoltTwoClassPrt.BreakRelayCounter);
        //断开继电器
        ContactorCtrl(DISCONNECT_CONTACTOR);

        //保存操作记录
        //SaveAction(GetActionId(CONTOL_CONTACTOR_BREAK), "V2Prt ContactorOFF");

        //接触器第一次断开时，才会开始计时
        if(s_tVoltTwoClassPrt.BreakRelayCounter == 0)
        {
            s_tVoltTwoClassPrt.TimerStatus = True;
        }

        if(s_tVoltTwoClassPrt.bCellProtectFlag != s_tVoltTwoClassPrt.bCellProtectFlagBak)
        {
            s_tVoltTwoClassPrt.BreakRelayCounter++;
            SaveAction(GetActionId(CONTOL_CONTACTOR_BREAK), "V2Prt ContactorOFF");
        }
    }

    return;
}

//闭合接触器判断
static void JudgeConnectContactor(VOID)
{
    if(s_tVoltTwoClassPrt.bCellProtectFlag != s_tVoltTwoClassPrt.bCellProtectFlagBak)
    {
        s_tVoltTwoClassPrt.bJudgePrtVoltFlag = False;
    }

    if(s_tVoltTwoClassPrt.ContactorStatusFlag)
    {
        //rt_kprintf("Current voltage is 3.3V,close Contactor!\n");
        //闭合继电器
        ContactorCtrl(CONNECT_CONTACTOR);
        
        //保存操作记录
         if(s_tVoltTwoClassPrt.bCellProtectFlag != s_tVoltTwoClassPrt.bCellProtectFlagBak)
        {
            SaveAction(GetActionId(CONTOL_CONTACTOR_CLOSE), "V2Prt ContactorON");
        }
    }

    return;
}

//电压二级保护功能函数
static void VoltTwoClassPrtFunc(T_SysPara *pSysPara)
{
    if( s_tVoltTwoClassPrt.bCellProtectFlag)
    {
        JudgeDisconnectContactor(pSysPara);
    }
    else
    {
        JudgeConnectContactor();
    }

    return;
}

//电压二级保护功能入口函数
VOID VOltTwoLevelProtectEnter(T_BCMAlarmStruct *pBCMAlm ,T_SysPara *pSysPara)
{
    //apptest模式下，不进行二级保护继电器逻辑的判断，不自动控制继电器断开或闭合
    if (GetApptestFlag()) {
        return;
    }

    s_tVoltTwoClassPrt.bCellProtectFlag = JudgeIfCellVoltProtect(pBCMAlm);
   
   //只有在闭锁告警未发生时才会在过压保护下进行接触器操作，闭锁发生时，接触器必定断开，判断没有意义
   if(!pBCMAlm->ucBDUBattLockAlm)
   {
        //rt_kprintf("BattLockAlm not hanppened!\n");
        VoltTwoClassPrtFunc(pSysPara);
   } 
   else
   {
      s_tVoltTwoClassPrt.BreakRelayCounter = 0;
      s_tVoltTwoClassPrt.wTimerConuter = 0;
      s_tVoltTwoClassPrt.TimerStatus = False;
      s_tVoltTwoClassPrt.bJudgePrtVoltFlag = False;
      s_tVoltTwoClassPrt.bGenerateBattLockFlag = False;
   }

   //时间判断，接触器在24小时内，5次断开，则产生闭锁告警，否则重新统计
   JudgeIfGenerateBattLockAlarm(pBCMAlm);

   s_tVoltTwoClassPrt.bCellProtectFlagBak =  s_tVoltTwoClassPrt.bCellProtectFlag;

   return;
}

//获取接触器的状态
VOID SetContactorStatus(BYTE bStatus)
{
   s_tVoltTwoClassPrt.ContactorStatusFlag = bStatus & 0x01;
}

//是否需要开始24小时计时的标志，当第一次接触器断开后，标志位置位
BOOLEAN GetVoltTwoClassTimeStatus(VOID)
{
    return s_tVoltTwoClassPrt.TimerStatus;
}

//24小时计时器加1控制
VOID VoltTwoClassTimeCounter(VOID)
{
   TimerPlus(s_tVoltTwoClassPrt.wTimerConuter, 60*24);
}

void ReadAnalyseInfo(void)
{
    LONG lAnalyseInfoReadRtn;
    rt_memset(&s_tAnalyseInfo, 0, offsetof(T_AnalyseInfoStruct,wCheckSum)+2);

    lAnalyseInfoReadRtn = readFile("/AnalyseInfo", (BYTE*)&s_tAnalyseInfo, offsetof(T_AnalyseInfoStruct,wCheckSum)+2);
    if ( s_tAnalyseInfo.wCheckSum != CRC_Cal((BYTE*)&s_tAnalyseInfo, offsetof(T_AnalyseInfoStruct,wCheckSum)) || (lAnalyseInfoReadRtn != offsetof(T_AnalyseInfoStruct,wCheckSum)+2) )
    {
        ClearAnalyticalAlm();       
    }
}

static void UpdateAnalyseInfo(void)
{
	s_tAnalyseInfo.wCheckSum = CRC_Cal((BYTE*)&s_tAnalyseInfo, (offsetof(T_AnalyseInfoStruct,wCheckSum)));
	writeFile("/AnalyseInfo", (BYTE*)&s_tAnalyseInfo, offsetof(T_AnalyseInfoStruct,wCheckSum)+2);
    setBackupAnalyseInfo((BYTE*)&s_tAnalyseInfo, (offsetof(T_AnalyseInfoStruct,wCheckSum)+2));
}

void TransAnalyticalAlm( T_AnalyseInfoStruct* tAnalyseInfo, BOOLEAN bWriteFlag)
{
    rt_memcpy((BYTE *)&s_tAnalyseInfo, (BYTE *)tAnalyseInfo, offsetof(T_AnalyseInfoStruct,wCheckSum)+2); 
    if( bWriteFlag == 1 )
    {
        UpdateAnalyseInfo();
    }
}

void ClearAnalyticalAlm( void )
{
    rt_memset(&s_tAnalyseInfo, 0, offsetof(T_AnalyseInfoStruct,wCheckSum)+2);//数据异常时直接清空
    UpdateAnalyseInfo(); 
}

void GetAnalyticalAlm(T_AnalyseInfoStruct *pDest)
{
    rt_memcpy((BYTE *)pDest, (BYTE *)&s_tAnalyseInfo, offsetof(T_AnalyseInfoStruct,wCheckSum)+2); 
}

#ifdef INTELLIGENT_PEAK_SHIFTING
BOOLEAN JudgePeakShiftCondition(T_BattInfo *pBattIn,T_BattDealInfoStruct *pBattDeal,T_PeakShiftPara *pPeakShift)
{
    if(GetQtptestFlag())
    {
        return False;
    }
    
    if((RUN_MODE_CONTROLLED != pBattIn->tPara.ucRunMode || IsNorthCommFail()) && True == pPeakShift->bPeakShift
    && IsMaster() && !pBattDeal->bPeakShiftOff && !pBattIn->tData.tFmData.ucFmStatus)
    {
        return True;
    }
    return False;

}

void SetPeakShiftDisable(BYTE ucTemp)
{
    s_tBattDeal.bPeakShiftChgDisable = ucTemp;
}

T_PeakShiftPara *GetShiftPeakPara(void)
{
    return &s_tPeakShift;
}

WORD PowerOffPeakTimeCounter(void)
{
    if(s_bEnterDelayJudge)
    {
        TimerPlus(s_tBattDeal.wShiftPeakCounter,s_tPeakShift.ucPoweroffPeakDelay*ONE_HOUR_MINUTES);
    }

    return s_tBattDeal.wShiftPeakCounter;
}

Static BOOLEAN PowerOffPeakShiftJudge(void)
{
    static BOOLEAN s_bJudgePowerOnOrOffFlag = False;

    if(!s_bJudgePowerOnOrOffFlag && s_tBattDeal.bPowerOff)
    {
        s_tBattDeal.ucPeakDichgNum = 0;
    }
    if(DISCHG_MODE_SELF_ADAPTION == s_tBattIn.tPara.ucUsageScen || DISCHG_MODE_SOLAR == s_tBattIn.tPara.ucUsageScen)
    {

        if (s_bJudgePowerOnOrOffFlag != s_tBattDeal.bPowerOff)
        {
            //上一时刻为停电，当前时刻为来电，并且上一时刻转换已经转换为了放电，则需要延时一段时间
            if(s_bJudgePowerOnOrOffFlag && !s_tBattDeal.bPowerOff && s_tBattDeal.ucChargeMode == BATT_MODE_DISCHARGE)
            {
                s_bEnterDelayJudge = True;
                s_tBattDeal.wShiftPeakCounter = 0;           
            }
            else
            {
                s_bEnterDelayJudge = False;
            }
            s_bJudgePowerOnOrOffFlag = s_tBattDeal.bPowerOff;
        } 

       if(TimeOut(s_tBattDeal.wShiftPeakCounter,s_tPeakShift.ucPoweroffPeakDelay*ONE_HOUR_MINUTES))
       {
           s_tBattDeal.bPeakShiftOff = FALSE;
       }
       else
       {
           s_tBattDeal.bPeakShiftOff = TRUE;
       }
    }
    else
    {
       s_bJudgePowerOnOrOffFlag = s_tBattDeal.bPowerOff;
       s_tBattDeal.bPeakShiftOff = FALSE;
       //在本身为来电的情况下，如果由纯锂电或远供模式切换为混用模式，不需要延时
       s_bEnterDelayJudge = False;
       s_tBattDeal.wShiftPeakCounter = s_tPeakShift.ucPoweroffPeakDelay*ONE_HOUR_MINUTES;
    }

    return True;//错峰功能后续修改
}
#endif

/// @brief 是否开启DCR检测
/// @return True:开启DCR检测 False:不开启DCR检测
static BOOLEAN IfStartDcrTest(void)
{
    FLOAT fMedianCellVolt = 0.0; // 单体电压中位数
    FLOAT fMedianCellTemp = 0.0; // 单体温度中位数

    fMedianCellVolt = GetCellMediumValue(s_tBattIn.tData.afCellVolt, s_tHardwarePara.ucCellVoltNum);
    fMedianCellTemp = GetCellMediumValue(s_tBattIn.tData.afCellTemp, s_tHardwarePara.ucCellTempNum);

    if(s_bDcrJudgeFlag == TRUE)
    {
        //1、单体电压中位数在3.15V和3.4V之间
        if(fMedianCellVolt <= 3.15f || fMedianCellVolt >= 3.4f)
        {
            s_tBattDeal.slDcrTestTimer = 0;
            return False;
        }

        //2、充电电流不小7A；
        if(s_tBattIn.tData.fBattCurr < DCR_TEST_MIN_CURRENT)
        {
            s_tBattDeal.slDcrTestTimer = 0;
            return False;
        }

        //3、单体温度中位数在10°以上；
        if(fMedianCellTemp <= 10.0f)
        {
            s_tBattDeal.slDcrTestTimer = 0;
            return False;
        }
        TimerPlus(s_tBattDeal.slDcrTestTimer, DCR_TEST_TIME_MAX);
        //4、条件1、2、3连续判断两分钟
        if(TimeOut(s_tBattDeal.slDcrTestTimer, DCR_TEST_TIME_MAX))
        {
            //DCR测试条件满足后，进行DCR检测周期的计数
            s_tBattDeal.slDcrTestTimer = 0;
            s_bDcrJudgeFlag = FALSE;
            return True;
        }
    }
    return False;
}

/// @brief 直流内阻异常告警/保护判断
/// @param
/// @return 返回值无意义
static BOOLEAN JudgeBattInnerResFault(void)
{
    BYTE i;
    BYTE ucDcrFaultAlmCnt = 0;//直流内阻异常告警判断计数
    BYTE ucDcrFaultPrtCnt = 0;//直流内阻异常保护判断计数
    BYTE ucDcrFaultAlmRecovCnt = 0;//直流内阻异常告警恢复计数
    FLOAT fCellDcr[CELL_VOL_NUM] = {0.0,};
    FLOAT fMedianCellDcr = GetCellMediumValue(s_tBattOut.fCellDcr, s_tHardwarePara.ucCellVoltNum);

    ucDcrFaultAlmCnt = s_tDcrCnt.ucDcrFaultAlmCnt;
    ucDcrFaultPrtCnt = s_tDcrCnt.ucDcrFaultPrtCnt;
    ucDcrFaultAlmRecovCnt = s_tDcrCnt.ucDcrFaultAlmRecovCnt;
    // 排序得到最大单体DCR
    for(i = 0; i < s_tHardwarePara.ucCellVoltNum; i++)
    {
        fCellDcr[i] = s_tBattOut.fCellDcr[i];
    }
    BubbleSort(fCellDcr, s_tHardwarePara.ucCellVoltNum);

    // 不可能出现直流内阻异常保护和直流内阻异常告警同时产生的情形，在DealDcr中已经拦截；

    // 保护非屏蔽时
    if(s_tSysPara.aucAlarmLevel[DCR_FAULT_PRT_INDEX] != SHIELD_LVL)
    {
        if (fCellDcr[0] > fMedianCellDcr * s_tSysPara.ucDcrFaultPrtThre)
        {
            ucDcrFaultPrtCnt++;
        }
        else
        {
            ucDcrFaultPrtCnt = 0;
        }
        // 保护和告警均为未屏蔽
        if(s_tSysPara.aucAlarmLevel[DCR_FAULT_ALM_INDEX] != SHIELD_LVL)
        {
            if(fCellDcr[0] > fMedianCellDcr * s_tSysPara.ucDcrFaultAlmThre)
            {
                ucDcrFaultAlmCnt++;
                // 满足阈值倍数条件时告警恢复计数清零，避免非连续恢复计数累加造成直流内阻异常告警自动恢复。
                ucDcrFaultAlmRecovCnt = 0;
            }
            else
            {
                ucDcrFaultAlmCnt = 0;
                ucDcrFaultAlmRecovCnt++;
            }
        }
        // 保护未屏蔽，告警屏蔽
        else
        {
            ucDcrFaultAlmCnt = 0;
        }
    }
    // 保护屏蔽，告警未屏蔽，保护判断计数清零。
    else
    {
        if(fCellDcr[0] > fMedianCellDcr * s_tSysPara.ucDcrFaultAlmThre)
        {
            ucDcrFaultAlmCnt++;
            // 满足阈值倍数条件时告警恢复计数清零，避免非连续恢复计数累加造成直流内阻异常告警自动恢复。
            ucDcrFaultAlmRecovCnt = 0;
        }
        else
        {
            ucDcrFaultAlmCnt = 0;
            ucDcrFaultAlmRecovCnt++;
        }
        ucDcrFaultPrtCnt = 0;
    }

    // 保护未屏蔽并且保护计数次数大于等于3
    if (ucDcrFaultPrtCnt >= 3 && (s_tSysPara.aucAlarmLevel[DCR_FAULT_PRT_INDEX] != SHIELD_LVL))
    {
        // 触发直流内阻异常保护，直流内阻异常保护屏蔽直流内阻异常告警
        s_tBattOut.bBattDcrFaultPrtSta = FAULT;
        // 直流内阻异常保护掉电保存
        saveResetData(DCR_FAULT_ALM_PRT, DCR_PROTECT);
        // 产生保护之后对应的计数器直接清零，避免手动解除保护后马上又满足条件保护了
        ucDcrFaultPrtCnt = 0;
    }

    // 保护已经屏蔽，那么告警一定会未屏蔽;
    // 如果保护没有屏蔽，并且保护计数没有超过三次，告警未屏蔽的情况下要判断告警的次数
    if (s_tSysPara.aucAlarmLevel[DCR_FAULT_PRT_INDEX] == SHIELD_LVL || (ucDcrFaultPrtCnt < 3))
    {
        if((s_tSysPara.aucAlarmLevel[DCR_FAULT_ALM_INDEX] != SHIELD_LVL) && (ucDcrFaultAlmCnt >= 3))
        {
            // 触发直流内阻异常告警
            s_tBattOut.bBattDcrFaultAlmSta = FAULT;
            // 直流内阻异常告警在重启后执行一次DCR检测即可再次产生直流内阻异常告警
            ucDcrFaultAlmCnt = 3;
        }
    }

    // 直流内阻异常告警的恢复逻辑————只有连续三次小于k才能恢复
    if(ucDcrFaultAlmRecovCnt >= 3 && s_tBCMAlm.ucDcrFaultAlm == FAULT)
    {
        ClearDcrFaultAlm();
        ucDcrFaultAlmRecovCnt = 0;
        // 直流内阻异常告警恢复之后下次判断还是需要连续3次
        ucDcrFaultAlmCnt = 0;
    }
    // 每一次计算出次数后进行保存，保存时间和DCR检测周期保持一致；
    s_tDcrCnt.ulDcrTestPeriodCnt = s_tBattDeal.ulDcrTestPeriodCnt;
    s_tDcrCnt.ucDcrFaultAlmCnt = ucDcrFaultAlmCnt;
    s_tDcrCnt.ucDcrFaultPrtCnt = ucDcrFaultPrtCnt;
    s_tDcrCnt.ucDcrFaultAlmRecovCnt = ucDcrFaultAlmRecovCnt;
    SaveDcrCnt(&s_tDcrCnt);
    return TRUE;
}

/// @brief 判断DCR检测周期是否应该为10分钟
/// @param  无
/// @return TRUE:DCR检测周期为10分钟 FALSE:DCR检测周期为7*24小时
static BOOLEAN IfDcrTestPeroid10(void)
{
    BYTE i, k;
    FLOAT fCellDcr[CELL_VOL_NUM] = {0.0,};
    FLOAT fMedianCellDcr = GetCellMediumValue(s_tBattOut.fCellDcr, s_tHardwarePara.ucCellVoltNum);
    // 排序得到最大单体DCR
    for(i = 0; i < s_tHardwarePara.ucCellVoltNum; i++)
    {
        fCellDcr[i] = s_tBattOut.fCellDcr[i];
    }
    BubbleSort(fCellDcr, s_tHardwarePara.ucCellVoltNum);
    // 在DealDcr中已经通过直流内阻异常告警/保护都屏蔽退出了DCR检测
    if(s_tSysPara.aucAlarmLevel[DCR_FAULT_ALM_INDEX] != SHIELD_LVL)
    {
        k = s_tSysPara.ucDcrFaultAlmThre;
    }
    else
    {
        k = s_tSysPara.ucDcrFaultPrtThre;
    }
    // 上一次DCRmax > DCRmid * k  && 不存在直流内阻异常告警或保护时，DCR检测周期为10分钟。
    // 首次上电单体DCR数据为0，DCR检测周期为10分钟;
    if((fCellDcr[0] + 0.000001 > fMedianCellDcr * k) && (s_tBCMAlm.ucDcrFaultAlm == NORMAL && s_tBCMAlm.ucDcrFaultPrt== NORMAL))
    {
        return TRUE;
    }
    return FALSE;
}

/// @brief 直流内阻检测
/// @param
/// @return 是否执行了DCR检测
static BOOLEAN DealDcr(void)
{
    BYTE i = 0;
    T_DcrRecordStruct tDcrRecord;
    rt_memset_s(&tDcrRecord,  sizeof(T_DcrRecordStruct), 0x00, sizeof(T_DcrRecordStruct));
    tDcrRecord.ucCellNum = s_tBattDeal.ucCellVoltNum;
    
    // 非充电状态不执行DCR检测
    if (BATT_MODE_CHARGE != s_tBattDeal.ucChargeMode)
    {
        s_tBattDeal.ucDcrStatus = DCR_OFF;
        s_tBattDeal.slDcrCtrlChargeTimer = 0;
        s_tBattDeal.slDcrStopChargeTimer = 0;
        s_tBattDeal.slDcrTestTimer = 0;
        s_tBattDeal.ulDcrTestPeriodCnt = 0;
        return FALSE;
    }
    // 直流内阻异常告警和直流内阻异常保护告警级别均为屏蔽时不进行DCR测试;
    if((s_tSysPara.aucAlarmLevel[DCR_FAULT_ALM_INDEX] == SHIELD_LVL) && (s_tSysPara.aucAlarmLevel[DCR_FAULT_PRT_INDEX] == SHIELD_LVL))
    {
        return FALSE;
    }

    // 处于apptest或者qtp模式则不进行DCR测试;
    if(GetApptestFlag() || GetQtptestFlag())
    {
        return FALSE;
    }

    // 已经产生直流内阻异常保护的情况下，不再进行DCR检测，除非手动清除直流内阻异常保护;
    if (s_tBCMAlm.ucDcrFaultPrt == FAULT)
    {
        return FALSE;
    }

    if(s_bDcrJudgeFlag == FALSE)
    {
        // 如果DCR检测周期为10分钟，那么按10分钟的上限进行计数累加;
        if(IfDcrTestPeroid10() == TRUE)
        {
            TimerPlus(s_tBattDeal.ulDcrTestPeriodCnt, DCR_TEST_SHORT_PERIOD);
            if(TimeOut(s_tBattDeal.ulDcrTestPeriodCnt,DCR_TEST_SHORT_PERIOD))
            {
                // DCR检测周期到了后，认为要重新开始判断DCR检测条件了;
                s_tBattDeal.ulDcrTestPeriodCnt = 0;
                s_bDcrJudgeFlag = TRUE;
            }
        }
        else
        {
            TimerPlus(s_tBattDeal.ulDcrTestPeriodCnt, DCR_TEST_LONG_PERIOD);
            if(TimeOut(s_tBattDeal.ulDcrTestPeriodCnt,DCR_TEST_LONG_PERIOD))
            {
                //上一次DCR测试结束后7天进行slDcrTestPeriodCnt归零，重新进行DCR测试条件判断;
                s_tBattDeal.ulDcrTestPeriodCnt = 0;
                s_bDcrJudgeFlag = TRUE;
            }
        }
    }
    switch(s_tBattDeal.ucDcrStatus)
    {
        // 达到DCR检测周期时如果满足DCR检测条件，那么开启DCR检测;
        case DCR_OFF:
            if (IfStartDcrTest())
            {
                s_tBattDeal.ucDcrStatus = DCR_CTRL_CHARGE;
                s_tBattDeal.slDcrCtrlChargeTimer = 0;
                s_tBattDeal.slDcrStopChargeTimer = 0;
            }
            return FALSE;

        // 从DCR检测条件满足开始10秒后当前的电池电流和电池电压以及单体电压，然后将DCR测试状态切换成DCR停止充电;
        case DCR_CTRL_CHARGE:
            TimerPlus(s_tBattDeal.slDcrCtrlChargeTimer, DCR_CTRL_CHARGE_TIME_MAX);
            if (TimeOut(s_tBattDeal.slDcrCtrlChargeTimer, DCR_CTRL_CHARGE_TIME_MAX))
            {
                s_tBattDeal.fDcrCurr[0] = fabs(s_tBattIn.tData.fBattCurr);
                s_tBattDeal.fDcrBattVolt[0] = s_tBattIn.tData.fBatVol;
                for (i = 0; i < tDcrRecord.ucCellNum; i++)
                {
                    s_tBattDeal.fDcrCellVolt[0][i] = s_tBattIn.tData.afCellVolt[i];
                }
                s_tBattDeal.ucDcrStatus = DCR_STOP_CHARGE;
            }
            return FALSE;

        // DCR检测控制停止充电10s后会再次记录当前的电池电流和电池电压以及单体电压，同时认为DCR测试状态已经结束；
        case DCR_STOP_CHARGE:
            TimerPlus(s_tBattDeal.slDcrStopChargeTimer, DCR_STOP_CHARGE_TIME_MAX);
            if (TimeOut(s_tBattDeal.slDcrStopChargeTimer, DCR_STOP_CHARGE_TIME_MAX))
            {
                s_tBattDeal.fDcrCurr[1] = fabs(s_tBattIn.tData.fBattCurr);
                s_tBattDeal.fDcrBattVolt[1] = s_tBattIn.tData.fBatVol;
                for (i = 0; i < tDcrRecord.ucCellNum; i++)
                {
                    s_tBattDeal.fDcrCellVolt[1][i] = s_tBattIn.tData.afCellVolt[i];
                }
                s_tBattDeal.ucDcrStatus = DCR_FINISHED;
            }
            return FALSE;

        // DCR结束状态下进行电池组DCR和单体DCR的计算，计算完毕之后会进行直流内阻记录的保存；
        case DCR_FINISHED:
            s_tBattOut.fDcrDeltaCurr = fabs(s_tBattDeal.fDcrCurr[0] - s_tBattDeal.fDcrCurr[1]);
            s_tBattOut.fDcrDeltaBattVolt = fabs(s_tBattDeal.fDcrBattVolt[0] - s_tBattDeal.fDcrBattVolt[1]);
            for (i = 0; i < tDcrRecord.ucCellNum; i++)
            {
                s_tBattOut.fDcrDeltaCellVolt[i] = fabs(s_tBattDeal.fDcrCellVolt[0][i] - s_tBattDeal.fDcrCellVolt[1][i]);
            }
            s_tBattOut.fBattDcr = s_tBattOut.fDcrDeltaBattVolt * 1000 / s_tBattOut.fDcrDeltaCurr;
            for (i = 0; i < tDcrRecord.ucCellNum; i++)
            {
                s_tBattOut.fCellDcr[i] = s_tBattOut.fDcrDeltaCellVolt[i] * 1000 / s_tBattOut.fDcrDeltaCurr;
                tDcrRecord.wCellDcr[i] = (WORD)(s_tBattOut.fCellDcr[i] * 100);
            }
            // 判断直流内阻异常告警和直流内阻异常保护，该判断还是应该在DCR结束状态下判断比较合适；
            JudgeBattInnerResFault();
            s_tBattDeal.slDcrCtrlChargeTimer = 0;
            s_tBattDeal.slDcrStopChargeTimer = 0;
            // DCR计算出来之后直接保存直流内阻记录
            SaveDcrRecord(&tDcrRecord);
            s_tBattDeal.ucDcrStatus = DCR_OFF;
            return TRUE;

        default:
            return FALSE;
    }
}


BOOLEAN SetCellCycleTimes(WORD wTimes)
{
    if(!CheckCellCycleTimesValid(wTimes)) return FALSE;
    s_wCellCycleTimes = wTimes;
    return TRUE;
}



/**
 * @brief 获取电芯循环次数
 *
 * 该函数返回全局变量 s_wCellCycleTimes 的值
 *
 * @return WORD 电芯的循环次数
 */
WORD GetCellCycleTimes(void)
{
    return s_wCellCycleTimes;
}


/// @brief 清除直流内阻异常保护时需要将BattOut里的异常保护状态恢复正常，防止恢复后立刻又产生了。
/// @param
BOOLEAN SetDcrFaultPrtNormal(void)
{
    s_tBattOut.bBattDcrFaultPrtSta = NORMAL;
    return TRUE;
}

/// @brief 清除直流内阻异常告警时需要将BattOut里的异常告警状态恢复正常，防止恢复后立刻又产生了。
/// @param
BOOLEAN SetDcrFaultAlmNormal(void)
{
    s_tBattOut.bBattDcrFaultAlmSta = NORMAL;
    return TRUE;
}

/***********供内部测试使用start*************/
BYTE GetVoltTwoClassBreakRelayCounter(VOID)
{
    return s_tVoltTwoClassPrt.BreakRelayCounter;
}

VOID SetVoltTwoClassBreakRelayCounter(BYTE bBreakRelayCounter) {
    s_tVoltTwoClassPrt.BreakRelayCounter = bBreakRelayCounter;
}

WORD GetVoltTwoClassStatsTime(void)
{
   return s_wVoltTwoClassStatsTimeMinu;
}


VOID SetVoltTwoClassStatsTime(WORD wTimeMinu)
{
   s_wVoltTwoClassStatsTimeMinu = wTimeMinu;
}

//测试函数，用于测试DCR记录的保存和导出功能
// static BOOLEAN testDcrRecordSaveAndExport(void)
// {
//     BYTE i = 0;
//     T_DcrRecordStruct tDcrRecord;
//     rt_memset(&tDcrRecord, 0x00, sizeof(T_DcrRecordStruct));
//     tDcrRecord.ucCellNum = s_tHardwarePara.ucCellVoltNum;
//     for (i = 0; i < tDcrRecord.ucCellNum; i++)
//     {
//         tDcrRecord.wCellDcr[i] = (WORD)(i * 100);
//     }
//     SaveDcrRecord(&tDcrRecord);
//     return True;
// }


FLOAT GetSelfAdaptionInitVolt(void)
{
    return s_fSelfAdaptionInitVolt;
}


/***********供内部测试使用end*************/

BOOLEAN BattThreadMutexTake(rt_int32_t lTime)
{
    if( s_ptMutexBattRunFlag == NULL )
    {
        return False;
    }
    if(lTime < 0)
    {
        lTime = RT_WAITING_FOREVER;
    }

    return (rt_mutex_take(s_ptMutexBattRunFlag,lTime) ? False : True);
}

BOOLEAN BattThreadMutexRelease(void)
{
    if( s_ptMutexBattRunFlag == NULL )
    {
        return False;
    }
    
    return (rt_mutex_release(s_ptMutexBattRunFlag) ? False : True);
}

//设置调频充电禁止状态

BOOLEAN SetFmChgDisable(BOOLEAN bDisable)
{
    s_tBattDeal.bFmChgDisable = bDisable;
    return True;
}


//计算调频充放电电流

BOOLEAN SetFmMaxCurr(FLOAT fPower)
{
    FLOAT fMinVolt = MIN(s_tBattIn.tData.fBatVol, s_tBattIn.tData.fSysVol);
    if (fMinVolt > 0.0f) 
    {
        s_tBattDeal.fFmMaxCurr = fPower / fMinVolt;
        return True;
    }
    return False;
}


//计算调频目标放电电压

BOOLEAN SetFmDischgVolt(FLOAT fVolt)
{
    s_tBattDeal.fDischargeHopeVol = GetValidData(fVolt, OUT_VOLT_MAX, OUT_VOLT_MIN );
    return True;
}



#ifdef PAKISTAN_CMPAK_PROTOCOL
// 计算电池运行时间和放电次数
Static BOOLEAN CalBattRunTimeAndDisChgTimes(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    if(NULL == pBattIn || NULL == pBattDeal){return False;}
    
    T_CmpakSaveInfo tCmpakSaveInfo;
    rt_memset(&tCmpakSaveInfo, 0x00, sizeof(T_CmpakSaveInfo));
    tCmpakSaveInfo.ulAccuBattRunTime = pBattDeal->ulAccuBattRunTime;
    tCmpakSaveInfo.ulAccuDischTimes  = pBattDeal->ulAccuDischTimes;

    // 放电次数统计，处于放电状态持续五分钟，累加一次，支持掉电保存
    if(BATT_MODE_DISCHARGE == pBattDeal->ucChargeMode && (pBattIn->tData.fBattCurr <= -1.0 * pBattIn->tData.fCurrMinDet))
    {
        pBattDeal->wDischgTime++;
        if(DISCHG_FIVE_MINUTES == pBattDeal->wDischgTime)
        {
            pBattDeal->ulAccuDischTimes++;
            tCmpakSaveInfo.ulAccuDischTimes = pBattDeal->ulAccuDischTimes;
            writeCmpakInfo(&tCmpakSaveInfo);
        }
    }
    else
    {
        pBattDeal->wDischgTime = 0;
    }
    // 累计运行时间，每一小时统计一次，支持掉电保存
    TimerPlus(pBattDeal->wRunOneHour, ONE_HOUR_MINUTES);
    if(ONE_HOUR_MINUTES == pBattDeal->wRunOneHour)
    {
        pBattDeal->ulAccuBattRunTime += ONE_HOUR_MINUTES;
        tCmpakSaveInfo.ulAccuBattRunTime = pBattDeal->ulAccuBattRunTime;
        pBattDeal->wRunOneHour = 0;
        readCmpakInfo(&tCmpakSaveInfo);
    }

    return True;
}
#endif



static BOOLEAN SaveCtrlLockFlag(void)
{
    s_tBattDeal.bCtlLock = True;
    SaveBattInfo();
    return True;
}

 

BOOLEAN CtrlReleaseLock(void)
{
    if (s_tBattDeal.bCtlLock) {
        BduCtrl(SCI_CTRL_BDULOCK, DISABLE);
        s_tBattDeal.bCtlLock = False;
        SaveBattInfo();
        SaveAction(GetActionId(CONTOL_RELEASE_LOCK), "EnterQtp Unlock");
        return True;
    }
    return False;
}

