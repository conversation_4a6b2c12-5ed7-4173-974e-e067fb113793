#include "common.h"
#include "battery.h"
#include "MultBattData.h"
#include "usart.h"

//#define CHARGE_VOL_DIFF (0.45f)   //功率侧要求外部电压和掉电电压阈值压差达到0.45才会有充电电流

BOOLEAN SmartLi_IsPowerDown_Slave(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattIn)
{
    if(ptBattDeal->bActivateStart == TRUE || ptBattDeal->bActivatePortCurrError == TRUE)
    {
        ptBattIn->tData.wJudgePowerOnCounter = 0;       //转充电计数清0
        return TRUE;
    }

    if (ptBattDeal->bLoopOff)
    {
        ptBattDeal->wSlavePowerCounter = (ptBattDeal->wSlavePowerCounter + 1)%300;
        ptBattDeal->bPowerOff = (ptBattDeal->wSlavePowerCounter >= TRANS_DELAY_MAX);
    }
    if(ptBattDeal->bCellUnderVolt)
    {
        return False; 
    }
    return ptBattDeal->bPowerOff;
}

void MultBattMixPowerOnAndOffVolt_Slave(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo, FLOAT *pfPowerDownVol, FLOAT *pfPowerOffVol)
{
    #define CHARGE_VOLT_DIFF_WHEN_MAX_CURR (1.5f)
    if(ptBattDeal->wBusVoltNum > 0 && (DISCHG_MODE_SELF_ADAPTION == ptBattInfo->tPara.ucUsageScen || DISCHG_MODE_SOLAR == ptBattInfo->tPara.ucUsageScen))
    {
        if (ptBattInfo->tData.fBatVol < (51.7f)/15.0*ptBattInfo->tData.ucCellVoltNum || True == ptBattDeal->bChargeEqualCurrValid)
        {
            if ( (ptBattInfo->tData.fBusVol - ptBattDeal->fPowerDownVolt) <= CHARGE_VOLT_DIFF_WHEN_MAX_CURR)
            {
                ///As a slave, if my current is less than master, decrease the powerdown voltage.
                if ((ptBattDeal->fChargeEqualCurr - ptBattInfo->tData.fBattCurr) > CHARGE_EQUAL_CURR_DIFF)
                {
                    ptBattDeal->fSlavePowerDownCompVolt -= 0.01f;
                }
                ////As a slave, if mycurrent is greater than master, increase the powerdown voltage.
                else if ((ptBattInfo->tData.fBattCurr - ptBattDeal->fChargeEqualCurr) > CHARGE_EQUAL_CURR_DIFF)
                {
                    ptBattDeal->fSlavePowerDownCompVolt += 0.01f;
                }

                ptBattDeal->fSlavePowerDownCompVolt = GetValidData(ptBattDeal->fSlavePowerDownCompVolt, 0.4f, -0.4f);
            }
            ptBattDeal->bChargeEqualCurrValid = False;
        }
    }

}

void BMSChgButBDUDischg_Slave(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo)
{
    if (False == ptBattInfo->tData.bBduChargeBreak || ptBattInfo->tData.fBattCurr >= -MIN_CURR_DET_BDCU)
    {
        TimerPlus(ptBattInfo->tData.ucSlaveDischgCounter, (BYTE)SLAVE_BREAK_COUNTER_MAX);
        if (ptBattInfo->tData.ucSlaveDischgCounter >= (BYTE)SLAVE_BREAK_COUNTER_MAX)
        {
            ptBattInfo->tData.ucSlaveDischgCounter = 0;                
            ptBattDeal->ucBduCtrlMode = BDU_CHG_START;            
        }
    }

    if(ptBattDeal->bCellUnderVolt)
    {
        ptBattDeal->ucBduCtrlMode = BDU_CHG_START;            
    }
}

void SmartLi_CalcDischargeOutputVolt_Slave(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo)
{
    //slave-machine volt from master-machine. Added by fengfj, 2020-05-09 16:04:35
    ptBattDeal->fDischargeSetVol  = ptBattDeal->fDischargeHopeVol;

    LoopOffVoltAdjust(ptBattDeal, ptBattInfo);
    return;
}


void SmartLi_CalcSysMaxChargeCurr_Slave(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo)
{
    if (ptBattInfo->tData.bRunModeControled)
    {
        ptBattDeal->fSysChgMaxCurr = g_ProConfig.fRateCurrChgBus;
        InitSetCurrAdjust();
    }
    
    ptBattDeal->fSysChgMaxCurr = GetValidData(ptBattDeal->fSysChgMaxCurr, g_ProConfig.fRateCurrChgBus, SMART_LI_CURRENT_MIN);
    ptBattDeal->fChargeBusSetCurr = GetValidData(ptBattDeal->fChargeBusCurrHope, ptBattDeal->fSysChgMaxCurr, SMART_LI_CURRENT_MIN);
    ptBattDeal->fChargeBusSetCurr = GetValidData(ptBattDeal->fChargeBusSetCurr, (FLOAT)ptBattDeal->ucRotateMaxCurr, SMART_LI_CURRENT_MIN);
    
    return;
}


void startDischg_Slave(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo)
{
    return;
}

void SetChgPara_Slave(T_BattDealInfoStruct *ptBattDeal, FLOAT fChargeFullVol)
{
    ptBattDeal->fChargeSetVol   = ptBattDeal->fChargeHopeVol;
    return;
}

void GetPowerdownVolt_Slave(T_BattInfo *pBattIn, T_BattDealInfoStruct *pBattDeal)
{
    #ifdef SYNC_POWER_DOWN_VOLTAGE
    if (pBattIn->tPara.ucRunMode == RUN_MODE_CONTROLLED && !JudgeCommDisconnect())
    {
//        pBattDeal->fPowerDownVolt = 48.0f - CHARGE_VOL_DIFF;
        return;
    }
    else if(pBattIn->tPara.ucUsageScen == DISCHG_MODE_BATT_CHARACTERISTIC)
    {
        pBattDeal->fPowerDownVolt = pBattIn->tData.fBatVol * g_ProConfig.fVoltTrasRate - 0.5;
    }

    if (pBattDeal->bActivatePowerOffFlag == TRUE)
    {
        pBattDeal->fPowerDownVolt = 48;
    }
    #endif
    return;
}

