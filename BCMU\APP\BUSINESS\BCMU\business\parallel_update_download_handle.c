#include "data_type.h"
#include "cmd.h"
#include "update_download_manage.h"
#include "protocol_remote_download.h"
#include "partition_def.h"
#include "utils_data_transmission.h"
#include "device_type.h"
#include "download.h"
#include "sps.h"
#include "update_manage.h"
#include "utils_time.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "north_parallel_modbus.h"
#include "msg_id.h"
#include "utils_rtthread_security_func.h"

static download_trig_ctr_inf_t s_parallel_update_trig_info;
static update_file_attr_t s_file_info;                       // 首发帧文件信息
rt_timer_t g_parallel_update_timer = NULL;
unsigned int g_fn1 = 0;
unsigned int g_fn2 = 0;

int get_parallel_update_trig_flag()
{
    return s_parallel_update_trig_info.trig_success;
}

int get_parallel_update_slave_trig_flag()
{
    return s_parallel_update_trig_info.slave_trig_success;
}

int get_parallel_update_slave_trig_times()
{
    return s_parallel_update_trig_info.trig_times;
}

void clear_parallel_update_slave_trig_flag()
{
    s_parallel_update_trig_info.slave_trig_success = FALSE;
    s_parallel_update_trig_info.slave_trig_times = 0;
}

int set_update_file_info(char* file_name, unsigned int file_size)
{
    rt_memset_s(&s_file_info, sizeof(update_file_attr_t), 0, sizeof(update_file_attr_t));
    rt_memcpy(s_file_info.file_name, file_name, FILE_NAME_LEN);
    time_base_t time = {};
    get_time(&time);
    char file_time[64] = {};
    rt_snprintf_s(file_time, 64, "%d-%d-%d %d:%d:%d", time.year, time.month, time.day, time.hour, time.minute, time.second);
    rt_memcpy(s_file_info.file_time, file_time, FILE_TIME_LEN);
    s_file_info.total_leng = file_size;
    s_file_info.total_frames = (s_file_info.total_leng + PARAEELE_UPDATE_DATA_LEN - 1) / PARAEELE_UPDATE_DATA_LEN + 1;
    // s_file_info.filecrc = 0x0102;
    s_file_info.param_type = 0;
    return SUCCESSFUL;
}
      
int parse_master_trig_data(void* dev_inst, void* cmd_buf)
{   
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    // 入参校验
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL)
    {
        return FAILURE;
    }
    // {0x7e,[ADDR],0x30,    0x41,0x41,0x32,0x37,       0x30,0x31,0x34,0x30,0x45,0x45,0x35,0x36,0x35,0x42,0x41,0x36,    0x41,0x42,0x0d }	
    unsigned char extend_trig_rsp_info[]              = {0x30,0x31,0x34,0x30,0x45,0x45,0x35,0x36,0x35,0x42,0x41,0x36};
    // {0x7e,[ADDR],0x30,    0x41,0x41,0x32,0x32,       0x30,0x31,0x34,0x30,0x45,0x45,0x35,0x35,0x35,0x41,0x41,0x35,    0x41,0x41,0x0d }
    unsigned char extend_trig_info[]                  = {0x30,0x31,0x34,0x30,0x45,0x45,0x35,0x35,0x35,0x41,0x41,0x35};

    // print_com_buff(tran_cmd_buf->buf , tran_cmd_buf->data_len);   //30 31 34 30 45 45 35 35 35 41 41 35

    unsigned char master_salve_status = 0;  
    get_one_data(DAC_DATA_ID_MASTER_SLAVE_STATUS, &master_salve_status);
    if(master_salve_status == 0)            // 单机场景下，扩展触发帧(从机)
    {
        if(rt_memcmp(extend_trig_info, tran_cmd_buf->buf, sizeof(extend_trig_info)) == 0)
        {
            s_parallel_update_trig_info.trig_times ++;
        }
        rt_kprintf("parse_master_trig_data|single req, trig_time:%d\n", s_parallel_update_trig_info.trig_times);
    }
    else                                    // 并机场景下，扩展触发帧的响应帧(主机)
    {
        if(rt_memcmp(extend_trig_rsp_info, tran_cmd_buf->buf, sizeof(extend_trig_rsp_info)) == 0)
        {
            s_parallel_update_trig_info.trig_times ++;
        }
        rt_kprintf("parse_master_trig_data|parallel rsp, trig_time:%d\n", s_parallel_update_trig_info.trig_times);
    }

    if(s_parallel_update_trig_info.trig_times >= DOWNLOAD_TRIG_TIMES)
    {
        s_parallel_update_trig_info.trig_success = TRUE;
        s_parallel_update_trig_info.trig_times = 0;
    }


    return SUCCESSFUL;
}

int pack_master_trig_data(void* dev_inst, void* cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    // download_cmd_head_t* proto_head = NULL;
    // 入参校验
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL || dev_inst == NULL)
    {
        return FAILURE;
    }
    unsigned char addr = ((dev_inst_t*)dev_inst)->dev_addr;
    //{0x7e,                        [ADDR],0x30,0x41,0x41,0x32,0x32,0x30,0x31,0x34,0x30,0x45,0x45,0x35,0x35,0x35,0x41,0x41,0x35,0x41,0x41,0x0d};
    unsigned char trig_info_req[] = {addr, 0x30, 0x41,0x41,0x32,0x32,0x30,0x31,0x34,0x30,0x45,0x45,0x35,0x35,0x35,0x41,0x41,0x35,0x41,0x41};
    unsigned char trig_info_rsp[] = {addr, 0x30, 0x41,0x41,0x32,0x34,0x30,0x31,0x34,0x30,0x45,0x45,0x35,0x36,0x35,0x42,0x41,0x36,0x41,0x42};
    unsigned char master_salve_status = 0;  
    get_one_data(DAC_DATA_ID_MASTER_SLAVE_STATUS, &master_salve_status);
    if(master_salve_status == 1)     // 主机发送请求帧
    {   
        rt_memcpy(tran_cmd_buf->buf, trig_info_req, sizeof(trig_info_req));
        tran_cmd_buf->data_len = sizeof(trig_info_req);
        rt_kprintf ("pack_master_trig_data|parallel send req \n");
    }
    else                             // 从机发送回复响应帧
    {
        rt_memcpy(tran_cmd_buf->buf, trig_info_rsp, sizeof(trig_info_rsp));
        tran_cmd_buf->data_len = sizeof(trig_info_rsp);
        s_parallel_update_trig_info.slave_trig_times ++;
        if(s_parallel_update_trig_info.slave_trig_times >= DOWNLOAD_TRIG_TIMES)
        {
            s_parallel_update_trig_info.slave_trig_success = TRUE;
            s_parallel_update_trig_info.slave_trig_times = 0;
        }
        rt_kprintf ("pack_master_trig_data|single send rsp \n");
    }
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;

    g_fn2 = 0;

    return SUCCESSFUL;
}

int parse_master_update_data(void* dev_inst, void* cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    // 入参校验
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL || dev_inst == NULL)
    {
        return FAILURE;
    }
    g_fn2 = ((download_cmd_head_t*)tran_cmd_buf->cmd->req_head)->fn2;    // 期望帧号  
    // rt_kprintf("parse_master_update_data|g_fn2:%d\n", g_fn2);
    return SUCCESSFUL;
}

int pack_master_first_data_download(cmd_buf_t* tran_cmd_buf)
{
    
    if(!s_parallel_update_trig_info.trig_success)
    {
        rt_kprintf("pack_master_first_data_download|fail\n");
        return FAILURE;
    }
 
    //首帧 req 段
    download_cmd_head_t* proto_head = (download_cmd_head_t*)tran_cmd_buf->cmd->req_head;
    proto_head->fn1 = 0;
    proto_head->fn2 = 0;
    proto_head->cid = 0x10;
    //首帧 data 段
    put_int16_to_buff(tran_cmd_buf->buf, s_file_info.total_frames);                                            // 总帧数
    put_uint32_to_buff(&tran_cmd_buf->buf[2], s_file_info.total_leng);                                         // 文件长度
    rt_memcpy(&tran_cmd_buf->buf[6], s_file_info.file_name, UPDATE_FILE_NAME_LEN);                          // 文件名称
    rt_memcpy(&tran_cmd_buf->buf[6 + UPDATE_FILE_NAME_LEN], s_file_info.file_time, UPDATE_FILE_TIME_LEN);   // 文件时间
    tran_cmd_buf->buf[6 + UPDATE_FILE_NAME_LEN + UPDATE_FILE_TIME_LEN] = 0;                               // 参数类型
    tran_cmd_buf->data_len = DOWNLOAD_UPDATE_FIRST_FRAME_LEN;
    // rt_kprintf("pack_master_first_data_download|len:%ld, frames:%ld, file_name:%s, file_time:%s\n", 
    // s_file_info.total_leng , s_file_info.total_frames, s_file_info.file_name, s_file_info.file_time);
    return SUCCESSFUL;
}

int pack_master_data_download(cmd_buf_t* tran_cmd_buf)
{
    if(g_fn2 == s_file_info.total_frames)
    {
        rt_kprintf("master send all frame and start next device update\n");
        set_parallel_update_shield_com_flag(FALSE);   // 并机升级 --传输完成
        start_next_device_update();
        rt_timer_stop(g_parallel_update_timer);
        return FAILURE;
    }
    //数据帧 req 段
    download_cmd_head_t* proto_head = (download_cmd_head_t*)tran_cmd_buf->cmd->ack_head;      // 兼容 单机和并机场景
    proto_head->fn1 = 0;
    proto_head->fn2 = g_fn2;
    proto_head->cid = 0x10;

    //数据帧 data 段
    unsigned int file_offset = 0;
    unsigned int actual_len = 0;
    part_data_t part_data = {0};
    file_offset = (g_fn2 - 1) * PARAEELE_UPDATE_DATA_LEN;
    actual_len = (s_file_info.total_leng - file_offset) > PARAEELE_UPDATE_DATA_LEN ? PARAEELE_UPDATE_DATA_LEN : (s_file_info.total_leng - file_offset);

    part_data.buff = (unsigned char *)tran_cmd_buf->buf;
    part_data.len = actual_len;                                                  // 默认单帧为 1024 字节
    part_data.offset = file_offset;
    tran_cmd_buf->data_len = actual_len;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), UPDATE_DOWNLOAD_PART);
    rt_kprintf("pack_master_data_download|flash_name:%s, file_offset:%ld, fn2:%ld, actual_len:%d\n", 
    part_data.name, file_offset, g_fn2, actual_len);
    if (SUCCESSFUL != storage_process(&part_data, read_opr)) {
        return FAILURE;
    }
    
    return SUCCESSFUL;
}

int pack_master_update_data(void* dev_inst, void* cmd_buf)
{
    int ret = FAILURE;
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    // 入参校验
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL || dev_inst == NULL)
    {
        return FAILURE;
    }
    rt_timer_start(g_parallel_update_timer);
    if(g_fn2 == 0)   // 首帧
    {
        ret = pack_master_first_data_download(tran_cmd_buf);
    }
    else
    {
        ret = pack_master_data_download(tran_cmd_buf);
    }
    return ret;
}

int start_parallel_update()
{
    dev_inst_t* download_inst = init_dev_inst(DEV_DAC_NORTH_DOWNLOAD);
    RETURN_VAL_IF_FAIL(download_inst != NULL, FAILURE);
    rt_kprintf("master trig %d device update\n", download_inst->dev_addr);
    return send_dest_cmd(download_inst, DOWNLOAD_MASTER_DATA_TRIG);
}

int send_parallel_update_data_download()
{
    dev_inst_t* download_inst = init_dev_inst(DEV_DAC_NORTH_DOWNLOAD);
    RETURN_VAL_IF_FAIL(download_inst != NULL, FAILURE);
    return send_dest_cmd(download_inst, DOWNLOAD_MASTER_DATA_DOWNLOAD);
}

int parallel_update_init()
{
    g_parallel_update_timer = rt_timer_create("trigTimer", parallel_timeout, RT_NULL, 10000, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
    if(g_parallel_update_timer == NULL)
    {
        return FAILURE;
    }
    return SUCCESSFUL;
}

void parallel_timeout()
{
    static unsigned char timeout_count = 0;
    static unsigned int last_fn2 = 0;
    if(g_fn2 != last_fn2)
    {
        timeout_count = 0;
    }
    last_fn2 = g_fn2;
    timeout_count++;
    send_parallel_update_data_download();
    rt_kprintf("parallel_timeout | count:%d,fn2:%d\n",timeout_count,g_fn2);
    if(timeout_count >= 3)
    {
        timeout_count = 0;
        rt_timer_stop(g_parallel_update_timer);
        start_next_device_update();
    }
}