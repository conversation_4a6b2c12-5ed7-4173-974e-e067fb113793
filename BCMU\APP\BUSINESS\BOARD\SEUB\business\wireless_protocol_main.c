#include <string.h>
#include <stdlib.h>
#include <rtthread.h>
#include "utils_thread.h"
#include "msg.h"
#include "cmd.h"
#include "data_type.h"
#include "sps.h"
#include "utils_server.h"
#include "utils_string.h"
#include "msg_id.h"
#include "wireless_protocol_main.h"
#include "para_id_in.h"
#include "para_manage.h"
#include "net_ip.h"
#include "remote_download_update_handle.h"

dev_inst_t* s_ip_dev_inst[] = {NULL, NULL, NULL};
unsigned int g_ip_timeout_max_count[] = {S1363_COMMUNICATION_TIMEOUT_CNT, UPDATE_COMMUNICATION_TIMEOUT_CNT, S1363_COMMUNICATION_TIMEOUT_CNT};
static unsigned char release_link_data(dev_inst_t* dev_inverter_ip, link_inst_t* link_inst_ip);
dev_inst_t** get_ip_dev_inst()
{
    return s_ip_dev_inst;
}

ip_thread_info_t wireless_ip_thread_info[] = {
    {{0, 503, 2, 0, {480, 0}}, {"SEUB_apptest", TYPE_S1363},},
    {{0, 1234, 2, 0, {480, 0}}, {"SEUB_update", TYPE_UPDATE},},
    {{0, 502, 4, 0, {480, 0}}, {"SEUB_modbusTCP", TYPE_MODBUS_TCP},},
};
#define IP_THREAD_INFO_NUM sizeof(wireless_ip_thread_info) / sizeof(ip_thread_info_t)


dev_inst_t* init_ip_dev_inst(int protol_type)
{
    static dev_inst_t* s_dev_inverter_ip = NULL;
    unsigned char dev_type_ip[] = {DEV_NORTH_SEUB_APPTEST, DEV_REMOTE_DOWNLOAD, DEV_MODBUS_TCP_SEUB};

    s_dev_inverter_ip = init_dev_inst(dev_type_ip[protol_type]);
    RETURN_VAL_IF_FAIL(s_dev_inverter_ip != NULL, NULL);

    dev_inst_t* dev_inverter_ip = (dev_inst_t*)rt_malloc(sizeof(dev_inst_t));
    RETURN_VAL_IF_FAIL(dev_inverter_ip != NULL, NULL);
    rt_memset(dev_inverter_ip, 0, sizeof(dev_inst_t));

    dev_inverter_ip->dev_addr = s_dev_inverter_ip->dev_addr;
    dev_inverter_ip->state = s_dev_inverter_ip->state;
    dev_inverter_ip->timeout_counts = s_dev_inverter_ip->timeout_counts;

    dev_inverter_ip->dev_type = (dev_type_t*)rt_malloc(sizeof(dev_type_t));
    if(dev_inverter_ip->dev_type == NULL)
    {
        rt_free(dev_inverter_ip);
        return NULL;
    }

    rt_memcpy(dev_inverter_ip->dev_type, s_dev_inverter_ip->dev_type, sizeof(dev_type_t));
    return dev_inverter_ip;
}



int init_ip_link_info(net_pro_para_t* parameter, north_mgr_t** north_mgr, dev_inst_t** dev_inverter_ip)
{
    if (parameter == NULL || north_mgr == NULL || dev_inverter_ip == NULL) {
        return FAILURE;
    }

    net_pro_para_t tmp;
    rt_memcpy(&tmp, parameter, sizeof(net_pro_para_t));
    pollfds_t* poll_data = get_pollfds_data(tmp.proto_type);
    RETURN_VAL_IF_FAIL(poll_data != NULL, FAILURE);
    *dev_inverter_ip = init_ip_dev_inst(tmp.proto_type);
    if (*dev_inverter_ip == NULL) {
        return FAILURE;
    }

    link_inst_t* link_inst_ip = init_ip_link_inst();
    if (link_inst_ip == NULL) {
        rt_free((*dev_inverter_ip)->dev_type);
        rt_free(*dev_inverter_ip);
        return FAILURE;
    }

    link_inst_ip->device->device_id = poll_data->fds[tmp.fd_index].fd;
    (*dev_inverter_ip)->dev_type->link_inst = link_inst_ip;

    *north_mgr = init_thread_data(link_inst_ip, MOD_INVERTER);
    if (*north_mgr == NULL) {
        release_link_data(dev_inverter_ip, link_inst_ip);
        return FAILURE;
    }

    LOG_E("wired_protocol create. fd_index = %d, device_id = %d\n", tmp.fd_index, link_inst_ip->device->device_id);
    s_ip_dev_inst[tmp.proto_type] = *dev_inverter_ip;
    return SUCCESSFUL;
}


void wireless_protocol(void *parameter)
{
    north_mgr_t* north_mgr = NULL;
    dev_inst_t* dev_inverter_ip = NULL;
    if(FAILURE == init_ip_link_info((net_pro_para_t*)parameter, &north_mgr, &dev_inverter_ip))
    {
        return;
    }
    wireless_protocol_main(dev_inverter_ip,  dev_inverter_ip->dev_type->link_inst, north_mgr, (net_pro_para_t*)parameter);
}


void wireless_protocol_main(dev_inst_t* dev_inverter_ip, link_inst_t* link_inst_ip, north_mgr_t* north_mgr, net_pro_para_t* net_pro_para)
{
    static unsigned int s_timeout_count[TYPE_MAX_PROTO][CONN_MAX] = {0};
    int recv_rtn = 0;
    pollfds_t* poll_data = get_pollfds_data(net_pro_para->proto_type);
    RETURN_IF_FAIL(poll_data != NULL);
    rt_kprintf("main|fd_index:%d, proto_type:%d\n", net_pro_para->fd_index, net_pro_para->proto_type);

    while(is_running(TRUE))
    {
        s_timeout_count[net_pro_para->proto_type][net_pro_para->fd_index]++;
        if (poll_data->fds[net_pro_para->fd_index].revents & POLLIN)
        {
            s_timeout_count[net_pro_para->proto_type][net_pro_para->fd_index] = 0;
            recv_rtn = linker_recv(dev_inverter_ip, north_mgr->cmd_buff);
            if(SUCCESSFUL == recv_rtn)
            {
                protocol_parse_and_send_ip(dev_inverter_ip, north_mgr->cmd_buff);
            }
        }
        if (LINK_CLOSE == recv_rtn || s_timeout_count[net_pro_para->proto_type][net_pro_para->fd_index] > g_ip_timeout_max_count[net_pro_para->proto_type])
        {
            unlink_client(net_pro_para->fd_index, poll_data);
            s_timeout_count[net_pro_para->proto_type][net_pro_para->fd_index] = 0;
            delete_wireless_pro_thread(dev_inverter_ip, link_inst_ip, north_mgr);
            s_ip_dev_inst[net_pro_para->proto_type] = NULL;
            return;
        }
        rt_thread_mdelay(IP_THREAD_RUN_PERIOD);
    }
}



unsigned char delete_wireless_pro_thread(dev_inst_t* dev_inverter_ip, 
                                    link_inst_t* link_inst_ip, north_mgr_t* north_mgr)
{
    // Free resources in reverse order of allocation
    release_link_data(dev_inverter_ip, link_inst_ip);
    rt_free(north_mgr->cmd_buff);
    rt_free(north_mgr);


    // Log the deletion of the thread
    struct rt_thread* rt_thread_tmp = rt_thread_self();
    LOG_E("delete thread: %s", rt_thread_tmp->name);
    return SUCCESSFUL;
}


static unsigned char release_link_data(dev_inst_t* dev_inverter_ip, link_inst_t* link_inst_ip)
{
    rt_free(link_inst_ip->r_cache.buff);
    rt_free(link_inst_ip->s_cache.buff);
    rt_free(link_inst_ip->device);
    rt_free(link_inst_ip);
    rt_free(dev_inverter_ip->dev_type);
    rt_free(dev_inverter_ip);
    return SUCCESSFUL;
}


int* init_wired_server_listen(void * param)
{
    //远程下载协议初始化
    update_download_init();
    // 遍历所有线程信息，初始化每个线程的监听信息和轮询文件描述符数据
    for(int loop = 0; loop < IP_THREAD_INFO_NUM; loop++ )
    {
        // 调用 `init_server_listen` 函数初始化每个线程的监听信息和轮询文件描述符数据
        init_server_listen(&wireless_ip_thread_info[loop].listen_info, &wireless_ip_thread_info[loop].poll_fds_data);
    }
    // 返回传入的参数指针
    return param;
}


void wired_network_listen_thread(void* thread_data)
{
    while (is_running(TRUE))
    {
        start_server_do_poll(wireless_ip_thread_info, IP_THREAD_INFO_NUM);
        rt_thread_mdelay(100);
    }
}