/**************************************************************************
* Copyright (C) 2010-2011, ZTE Corporation.
* 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：ZXDU18 CTL板软件
* 文件名称：battery.h
* 文件说明：电池管理模块头文件
* 作    者  ：冯福建
* 版本信息：V1.0
* 设计日期：2018-10-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
* 其他说明：
***************************************************************************/
/***********************  常量定义  ************************/
#ifndef SOFTWARE_SRC_APP_BATTERY_H_
#define SOFTWARE_SRC_APP_BATTERY_H_

#include "commBdu.h"
#include "alarm_in.h"
#include "fm.h"

#ifdef INTELLIGENT_PEAK_SHIFTING
#include "peakshift.h"
#endif

#ifdef __cplusplus
extern "C" {
#endif
extern T_ProjConfig g_ProConfig;

#define BATT_MIN_CAP    10//电池组最小容量（额定容量小于此判断该组电池没有配置）

#define SLAVE_NUM   32
#define LOOPOFF_COUNTER_DISCHG_MAX (480)
#define LOOPOFF_COUNTER_CHG_MAX (180)
#define LOOPOFF_SHUTDOWN_COUNTER_MAX (3600)

#define LOOP_OFF_NO             ((BYTE)0)       ////正常
#define LOOP_OFF_CHARGE         ((BYTE)1)       ////充电回路异常
#define LOOP_OFF_DISCHARGE      ((BYTE)2)       ////放电回路异常

#define DEFAULT_FLOAT_VOLT 53.5
#define DISCHG_16FOLLOW_RATIO    (16.0f/15.0f)

#define BATT_MODE_CHARGE        ((BYTE)0)       ////充电
#define BATT_MODE_DISCHARGE     ((BYTE)1)       ////放电
#define BATT_MODE_STANDBY       ((BYTE)2)       ////在线非浮充
#define BATT_MODE_OFFLINE       ((BYTE)3)       ////离线（假休眠）
#define BATT_MODE_CONSRV        ((BYTE)4)       ////节能
#define BATT_MODE_INVALID       ((BYTE)5)       ////无效
#define BATT_MODE_DISABLE       ((BYTE)6)       ////错峰充放电停止

#define BDU_CTRL_NULL            (0)
#define BDU_CHG_START           (1<<7 | SCI_CTRL_CHG2CHG)
#define BDU_DISCHG_START        (SCI_CTRL_CHG2CHG)
#define BDU_SLEEP_START         (1<<7 | SCI_CTRL_SLEEP)
#define BDU_SLEEP_STOP          (SCI_CTRL_SLEEP)

//#define BATT_DISCHARGE_THROUGH 	((BYTE)0)       ////直通放电
#define BATT_DISCHARGE_FOLLOW 	((BYTE)0)       ////跟随
#define BATT_DISCHARGE_16FOLLOW ((BYTE)1)       ////16串跟随
#define DISCHG_FIVE_MINUTES (5)
#define MIX_INIT_POWER_ON_VOLT (51.0f)
#define MIX_INIT_POWER_DOWN_VOLT (51.0f)
#define MIX_INIT_DISCHARGE_VOLT (43.8f)

#define CELL_VOLT_HIGH_LOCK_TIMES 2
#define CELL_VOLT_LOW_LOCK_TIMES 7

// 电池温度分段统计:以10度为粒度全范围统计,21~30之间以5度统计，统计范围【-40，100】，-40~-30为0，依次类推 单位:Min
#define BATT_TEMP_STEP_NUM  15
#define CELL_VOLT_LOW_THRESHOLD (2.8)
#define CELL_VOLT_LOW_DELTA (0.3)
#define DISCHARGE_VOLT_DELTA    (0.9)
#define ADDED_SOH_MIN   (-5.0f)
#define PURELLI_POWER_ON_VOLTDIFF (0.9f)
#define PURELLI_POWER_OFF_VOLTDIFF (0.7f)

#define SMARTLI_OUTPUT_VOL_MAX   (58.0f)
#define COMMONLI_OUTPUT_VOL_MAX  (58.0f)

#define OUT_VOLT_MAX           (58.0f) //输出电压有效上限
#define OUT_VOLT_MIN           (42.0f) //输出电压有效下限
#define PURE_LI_OUT_MIN        (42.0f) //输出电压，纯锂电场景
#define OUT_ASSIST_VOLT_MIN    (40.0f) //外协模式，最小输出电压
#define CHARGE_EQUAL_CURR_DIFF (5.0f)


// 均衡相关条件
#define PLATFORM_AREA_START_BALANCE_VOLT    (0.1f)  //平台区启动均衡压差值，单位V
#define PLAT_LOWER_AREA_START_BALANCE_VOLT  (0.3f)  //平台区以下启动均衡压差值，单位V
#define PLATFORM_CELL_VOLT_UPPER_LIMIT      (3.36f) //平台区电压阈值上限，单位V
#define PLATFORM_CELL_VOLT_LOWER_LIMIT      (3.2f)  //平台区电压阈值下限，单位V
#define START_BALANCE_CELL_VOLT_LOWER_LIMIT (3.1f)  //启动均衡电压阈值下限，单位V
#define BANLANCE_CELL_VOLT_START            PLATFORM_CELL_VOLT_UPPER_LIMIT
#define BATT_CELLFULL_VOLT_MAX              (3.45f) //电池进入在线非浮充后，电芯充满电压
#define BATT_FULLHOLD_MUNITE_MAX            (2*60) //电池进入在线非浮充后SOC置满最大维持时间


#define BATT_FULL_JUDGE_CNT 10
#define BATT_IDLE_MUNITE_MAX (24*60)
#define BATT_IC_OPEN_SECOND_MAX (24*60*60)
#define BATT_REMOTE_END_ONE_MUNITE_MAX (3*60)
#define BATT_UV_SECOND_MIN (5*60)//代码走查优化：为满足欠压进休眠时间超过5分钟优化
#define BATT_UV_MUNITE_MAX 61
#define BATT_SOH_FULL_VALUE 100
#define BATT_CYCLE_TIMES_ZERO 0
#define BDCU_CHARGE_OVER_CURRENT (30.0f)       //D121功率兜底充电过流保护值
#define BDCU_DISCHARGE_OVER_CURRENT (70.0f)    //D121功率兜底放电过流保护值

#define TRANS_TIMES         (50)      //过渡阶段进入次数
// #define BDU_LIMIT_MAX_10A   (1000)
#define BDU_LIMIT_MAX       (1000)
//#define BDCU_LIMIT_MIN       (20)  	//(30)
#define BDCU_CHARGE_BAT_LIMIT_MIN   (((MIN_CURR_DET_BDCU+1)/g_ProConfig.fRateCurrChg)*BDU_LIMIT_MAX)     // 充电电池侧 R321功率限流计算公式 :下发充电限流点/1000*150
#define BDCU_CHARGE_BUS_LIMIT_MIN   (((MIN_CURR_DET_BDCU+1)/g_ProConfig.fRateCurrChgBus)*BDU_LIMIT_MAX) // 充电BUS侧
#define BDCU_DISCHAGRE_LIMIT_MIN   (((MIN_CURR_DET_BDCU+1)/g_ProConfig.fRateCurrDischg)*BDU_LIMIT_MAX)   // 放电电池、BUS侧
#define ODCU_LIMIT_MIN       (200)
#define PMOS_RESTORE_DELAY  (15)
#define CELL_PROTECT_DELAY  (90)
#define PMOS_RESTORE_VOL    (50.0f)

#define CELL_SUPPL_MAX_VOLT 3.5
#define EQUAL_DISCHG_INIT_VOLT (54.0f)  // 均充转放电初始放电电压
//停电标志，yang_an,整理协议修改
#define POWER_ON    0
#define POWER_OFF   1

#define SMARTLI_SYSPOWER_COUNTER_SOLAR (8)
#define SMARTLI_SYSPOWER_COUNTER (10)
#define COMMONLI_SYSPOWER_COUNTER_SOLAR (8)
#define COMMONLI_SYSPOWER_COUNTER (30)
#define JUDGE_BATT_VOLT_DROP_COUNTER (20)
//can2均流
#define CURR_ADJUST_DEVIATION (2.0f)
#define CURR_ADJUST_STEP (0.01f)
#define CURR_ADJUST_VOLT_UPPER (0.2f)
#define CURR_ADJUST_VOLT_LOWER (-0.2f)
#define SOC_ADJUST_VOLT_SLOPE (0.002f)
#define SOC_ADJUST_VOLT_UPPER (0.4f)
#define SOC_ADJUST_VOLT_LOWER (-0.4f)


// #define SMARTLI_BDU_STATUS_COUNTER          (2*SMARTLI_SYSPOWER_COUNTER)

// #define LVD_SOURCE_POWEROFF 1
// #define LVD_SOURCE_VOLT     0
#define CTRL_BATTERY_CHARGE_START       1
#define CTRL_BATTERY_CHARGE_STOP        2
#define CTRL_BATTERY_DISCHARGE_START    3
#define CTRL_BATTERY_DISCHARGE_STOP     4

#define DC_VOLT_BIAS (0.3f)

#define SLEEP_SW_TIME        3     //3秒
#define WAKEUP_SW_TIME       1     //1秒

#define     DELTA_POWEROFF_NUM                   3   // 次数 
#define     DELTA_POWEROFF_TIME                  60   
#define     AUTO_CHG_POWEROFF_NUM           8
#define     AUTO_CHG_POWEROFF_TIME          600     //  单位:Min
#define     DISCHG_REC_NUM                              100

#define     RATE_CURR   (63.5)
#define     SELF_RECHARGE_TIME_MAX     (60*24)
#define     PERSIST_MINUTES         (5*60)
#define     ONE_HOUR_MINUTES           (60)
#define     ONE_MINUTES_SECOND         (60)     //1分钟=60秒
#define     ONE_HOUR_SECONDS           (60*60)  //1小时=3600秒
#define     THREE_MINUTES   (3)
#define     THREE_MINUTES_SECOND   (180)
#define     TWO_THOUSAND_PER_MINUTES   (2000)   //停电时间保护 pad协议可调0-2000分钟
#define     CELL_UNDER_VOLT_MIN        (20)
#define 	SMART_LI_CURRENT_MAX_COEF (1.0f)  ////50AH1C充电
#define 	SMART_LI_CURRENT_MIN_COEF (0.02f)
#define 	SMART_LI_CURRENT_MIN (2.0f)
#define     SOC_CAL_DISCHG_CURR (1)           //SOC校正放电电流1A
#define     TEN_MINUTES_PER_SECONDS    600    //10分钟
#define     HALF_AN_HOUR_PER_SECONDS   1800   //30分钟
#define 	COMMON_LI_CURRENT_MAX_COEF (1.0f)  ////50AH1C充电
#define 	COMMON_LI_CURRENT_MIN_COEF (0.02f)
#define 	COMMON_LI_CURRENT_MIN (2)
#define 	BUS_VOLT_SAMPLE_NUM           (12)  //10s一次，采样2分钟
#define 	BUS_VOLT_SAMPLE_INTERVAL      (10)  //母排电压采样时间间隔
#define 	DISCHG_VOLT_DECLINE_THRESHOLD (1)   //放电电压下降1V
#define     START_CHG_JUDGE_INTERVAL      (4*ONE_HOUR_MINUTES)
#define     BATT_CHG_NOFULL_TIME      (24*7*60)
#define     BATT_MAX_LIFETIME_INTERVAL (15*365*24*ONE_HOUR_MINUTES) //电池最大15年寿命
#define     ALARM_RECORDS_MAX_COUNT (60000) //告警累计次数统计最大值
#define     IRREVSB_ALARM_COUNT (30)    //不可逆告警持续时间Min

#define 	SMART_LI_MAX_CHARGE_CURRENT (50)
#define 	COMMON_LI_MAX_CHARGE_CURRENT (50)

#define 	NO_FLAG         (0)
#define 	SHUT_DOWN_FLAG  (1)
#define 	RESTART_FLAG    (2)

#define     INFO_NOT_WRITTEN    (0)
#define     INFO_WRITTEN        (1)

#define     SMART_LI_OUTPUT_VOL_MIN (42.0f)
#define 	COMMON_LI_MAX_DISCHARGE_CURRENT (105.0f)
#define     COMMON_LI_OUTPUT_VOL_MIN (42.0f)
#define     SYS_VOLT_MAX_EXPECTED   (53.5f)
#define     SMART_LI_SUPPLE_VOL (44.0f)//强补电电压
#define     SUPPLE_DISCHG_VOL (SMART_LI_SUPPLE_VOL - 0.6f)

#define     SYS_VOLT_BIAS   (1)
#define     SMART_LI_OUTPUT_VOL_DEFAULT (46.5f)
#define     SMART_LI_DETECT_OUTPUT_VOL (46.5f-0.9f)
#define     COMMON_LI_OUTPUT_VOL_DEFAULT (46.5f)
#define     COMMON_LI_DETECT_OUTPUT_VOL (46.5f-0.9f)

#define     SYS_SHUTDOWN_VOL (52.0f)
#define     SYS_QUITE_SHUTDOWN (44.0f)
#define     TEMP_LOW_RATEDEC_PERDEGREE (0.032f)      //低温放电降额系数
#define     BATT_CHARGE_EXTRA_VOLT (0.6f)

#define CELL_TEMP_LOW_CTLR  10	//以PB为准，改为10, Added by fengfj, 2019-07-13 17:47:40
#define CELL_TEMP_HIHT_CTLR  50
#define CELL_TEMP_TOO_HIHT_CTLR  55
#define CELL_VOL_LOW_CTRL (2.8f)
#define CELL_VOL_LOW_CTRL_DELTA (0.3f)
#define CELL_CTRL_CHECK_TIMES (7)
#define BATT_CTRL_CHECK_TIMES (3)
#define VOLT_VALID_MIN (40)
#define CHARGE_VOLT_MAX (52.5f)
#define SMART_LI_DEFAULT_OUTPUT_VOL (51.5f)
#define CELL_VOL_HIGH_CHECK_TIMES (2)
#define BATT_FULL_CNT_MAX (30)
#define PROTO_SET_INFO_TIMEOUT_MINUTES (10)
#define MIX_SEND_BATT_CURR_TIMEOUT (10)
#define SEND_MAX_SOC_TIME_OUT (5)

#define LA_FULL_CHECK_TIMES         60
#define IN_EQU_CHARGING_VOLT        56
#define OUT_EQU_CHARGING_VOLT       55
#define DISCHARGE_POWER_STAT_DELAY  (30)
#define CHARGE_STOP_DELAY  (10)
#define BATT_SOC_MIN (0.0f)


#define AFFECT_COEF (0.98f)
#define BATT_CAP_MIN (10)

#define AMEND_OCV_MAX (3.00f)
#define AMEND_OCV_MIN (2.0f)
#define OCV_AMEND_DELAY_MAX (30)

// #define VOLT_CHANGE_DIFF (1.0)
#define VOLT_CHANGE_RISE_DIFF (5.0f)
#define VOLT_CHANGE_DROP_DIFF (-5.0f)

#define SYS_POWER_ON_VOLT_DIFF (0.2f)
#define FOLLOW_LI_VOLT_MAX (48.0f)

#define NPROTOCOL_TYPE_RS485_1363     0
#define NPROTOCOL_TYPE_RS485_MODBUS   1
#define NPROTOCOL_TYPE_CAN_1363     2
#define NPROTOCOL_TYPE_CAN_MODBUS   3
#define NPROTOCOL_MAX_NUM           4

#define POWER_STATUS_ON         1
#define POWER_STATUS_OFF        2
#define POWER_STATUS_INVALID    0

#define REMOTE_SUPPLY_DISCHG_SWITCH_MODE_SOC  0
#define REMOTE_SUPPLY_DISCHG_SWITCH_MODE_VOLT 1

#define INFO_TYPE_ALL                    0
#define INFO_TYPE_STATUS                 1
#define INFO_TYPE_OUT_VOLT               2
#define INFO_TYPE_CHG_COEF               3
#define INFO_TYPE_CHG_PRIOR              4
#define INFO_TYPE_SET_MIX_BATT_CURR      5
#define INFO_TYPE_AVERAGE_CURR           6
#define INFO_TYPE_DISCH_VOLT_FREE        7
#define INFO_TYPE_SET_MAX_SOC            8
#define INFO_TYPE_CHG_COEF_FREE          9
#define INFO_MAX_NUM                     10

#define VOLT_CHANGE_MAX         (FLOAT)(VOLT_CHANGE_RISE_DIFF+0.01f)
#define VOLT_CHANGE_MIN         (FLOAT)(VOLT_CHANGE_DROP_DIFF-0.01f)

// #define BATT_CURR_CHANGE_THRESHOLD (8.0)
#define CHARGE_TIME_FULL_MIN (600)

#define BDU_LIMIT_MAX_10A   (1000)
#define BDU_NO_LIMIT_NUMBER 1200
#define SET_COMPVOLT_MAX (0.5) 	// Added by fengfj, 2019-05-28 18:40:24

#define MINUTES_PER_DAY (24*60)
#define MINUTES_PER_MONTH (30*MINUTES_PER_DAY)
// #define MINUTES_PER_MONTH 10

#define BALANCE_START_PEROID        (120)
#define BALANCE_STOP_PEROID         (10)
#define BALANCE_PEROID              (BALANCE_STOP_PEROID + BALANCE_START_PEROID)
#define BALANCE_DO_STOP             (BALANCE_STOP_PEROID-7)
#define BALANCE_COUNTER             (7)
#define NONE    (0)
#define DEFAULT_LINE_R  (10.0)
#define CHARG_MINIMUM_VOL   (51.8) ////必须不大于来电电压
#define CHARGE_CURR_BACKLASH    (0.001)

#define CHARGE_CURR_INC (2.0)
//#define CHARGE_CURR_COUNTER_MAX  ((WORD)((FLOAT)SMART_LI_RATE_CURRENT/CHARGE_CURR_INC + 1))
#define SMART_LI_CHARGE_COUNT_PERIOD (60)
#define COMMON_LI_CHARGE_COUNT_PERIOD (60)
#define BATT_IR (0.001*10)
#define DECLINE_CURR_EACH_TIME 5.0
#define DEFAULT_FLOAT_VOLT 53.5
#define MIX_BACKLASH_CHG 0.1
#define SLAVE_BREAK_COUNTER_MAX 60

#define CHARGE_SOURCE_DELAY (50)        ///源不足判断延时
//#define CHARGE_SOURCE_DELAY (10)        ///源不足判断延时

#define TRANS_DELAY_MAX (30)    ///过渡阶段尝试充电的时间
#define CTRL_CHG_DISCHG_DELAY (10)    ///延时启动充放电

#define NO_DISCHG_CURR_TIMER_MAX           (120)
#define BUS_VOLT_NUM_MAX                   (120)
#define MIX_POWER_ON_VOL_DIFF              ((FLOAT)0.4)        ///来电电压差值
#define MIX_POWER_OFF_VOL_DIFF             ((FLOAT)1.9)        ///停电电压差值
#define MIX_POWRE_DOWN_VOL_DIFF_CHG        ((FLOAT)1.0)        ///掉电电压差值
#define MIX_POWRE_DIFF_DISCHG              ((FLOAT)1.0)        ///放电输出电压
#define MIX_POWRE_ON_DOWN_DIFF             (0.4f)
#define MIX_POWER_DIFF                     (MIX_POWRE_DIFF_DISCHG - MIX_POWER_ON_VOL_DIFF)
#define MIX_VOLT_MIN                       (51.0f)
#define MIX_POWER_ON_MAX                   (54.6f)
#define MIX_POWER_DOWN_MAX                 (54.0f)
#define MIX_DISCHG_VOLT_MAX                (54.0f)

#define SOLAR_POWER_DOWN_DIFF              ((FLOAT)1.5)       ///光伏混用掉电电压差值
#define SOLAR_POWRE_ON_DOWN_DIFF           (0.5f)

#define CHARGE_COUNTER_TIMEOUT  (120)

#define MIX_DISCHG_CURR_MAX      (90.0f)
#define MIX_EQUAL_BUS_VOL        (55.2f)
#define MIX_CHG_VOL_MIN          (52.0f)

#define POWER_DOWN_VOL_MAX  (52.9f)
#define POWER_DOWN_VOL_MIN  (47.0f)
#define SMART_LI_ROTATE_CURR_MAX ((BYTE)g_ProConfig.fRateCurrChgBus)
#define ROTATE_OPEN_COUNTER_MAX (20)
#define ROTATE_OPEN_FAULT_COUNTER_MAX (250)
// #define ROTATE_NO_CURRENT_COUNTER_MAX (60)
#define ROTATE_CLOSE_COUNTER_MAX (300)
#define ROTATE_HEATER_OPEN_COUNTER_MAX (4 * 60 * 60)
#define ROTATE_CURR_LIMIT_COUNTER_MAX (50)
#define ROTATE_STEP_CURRENT (5)
#define Sn2Addr(sn) (sn+1)
#define CTRL_SLAVE_FAIL_COUNTER_MAX (200)
#define SLAVE_DISABLE_ROTATE_TIME_MAX (30)
#define UNDERVOLL_CHARGE_MIN_CURRENT (2)
#define SOURCE_INSUFFICIENT_COUNT_MIN (3)  //源不足切换最小次数

////////////////// 参数保护阀值上下限及默认值定义 ///////////////////
// #define CHARGE_OVER_CURR_L       (uint16_t)2000 // 充电过流保护阀值
// #define CHARGE_OVER_CURR_H       (uint16_t)13000
// //#define CHARGE_OVER_CURR_DEF     (uint16_t)11000
// #define DISCHARGE_OVER_CURR_L    (uint16_t)2000 // 放电过流保护阀值
// #define DISCHARGE_OVER_CURR_H    (uint16_t)13000
// //#define DISCHARGE_OVER_CURR_DEF  (uint16_t)11000
// #define BATTERY_OVER_VOLT_L      (uint16_t)4700 // 电池组过压保护阀值
// #define BATTERY_OVER_VOLT_H      (uint16_t)6000
// //#define BATTERY_OVER_VOLT_DEF    (uint16_t)5480
// #define BUS_DROP_VOLT_L          (uint16_t)0    // 母排跌落电压阀值
// #define BUS_DROP_VOLT_H          (uint16_t)0xFFFF
// //#define BUS_DROP_VOLT_DEF
// #define CHARGE_LIMIT_CURR_L      (uint16_t)20   // 设定充电限电流，千分比
// #define CHARGE_LIMIT_CURR_H      (uint16_t)1200
// //#define CHARGE_LIMIT_CURR_DEF
// #define CHARGE_VOLT_L            (uint16_t)3600 // 设定充电电压，精度2
// #define CHARGE_VOLT_H            (uint16_t)5840
// //#define CHARGE_VOLT_DEF          (uint16_t)5250
#define DISCHARGE_VOLT_L         (uint16_t)4200 // 设定放电电压，精度2
#define DISCHARGE_VOLT_H         (uint16_t)5800
// //#define DISCHARGE_VOLT_DEF       (uint16_t)5300
// #define CURR_SHARE_L             (uint16_t)0    // 均流电流，精度2
// #define CURR_SHARE_H             (uint16_t)0xFFFF
// //#define CURR_SHARE_DEF
// #define CHARGE_BUS_VOLT_L        (uint16_t)3500 // BUS 充电最低电压阀值，精度2
// #define CHARGE_BUS_VOLT_H        (uint16_t)5800

//DCR测试相关宏定义
#define DCR_OFF 0                   //DCR测试未开启
#define DCR_CTRL_CHARGE 1           //DCR测试控制充电
#define DCR_STOP_CHARGE 2           //DCR测试停止充电
#define DCR_FINISHED 3              //DCR测试结束
#define DCR_CTRL_CHARGE_TIME_MAX 10
#define DCR_STOP_CHARGE_TIME_MAX 10
#define DCR_TEST_TIME_MAX 120       //DCR测试条件判断时间：2分钟
#define DCR_TEST_SHORT_PERIOD 600   //DCR测试短周期：10分钟
#define DCR_TEST_LONG_PERIOD 7*24*3600   //DCR测试长周期：7天

// 测试使用
//#define DCR_TEST_SHORT_PERIOD 120   //DCR测试短周期：2分钟
//#define DCR_TEST_LONG_PERIOD 240   //DCR测试长周期：4分钟

typedef void (*T_fSwitchHandle)(void);

//以下为用户接口（数据接口和程序接口）！
typedef struct
{
    //电池管理类参数
    FLOAT   fChargeFullVol;             //充满电压    
    FLOAT   fChagEndCurCoeff;          //充电末期电流比率
    WORD    wBattCap;                 //电池标称容量
    WORD    wChagMaxMinute;            //充电最长分钟数
    WORD    wChagEndHoldMinute;          //充电末期维持分钟数
    FLOAT   fBattRefreshVoltThreshold;  ///补充电电压
    WORD    wBattRefreshPeriod;         ///补充电周期
    FLOAT   fEqulizationStartThreshold; ////均衡启动阈值
    FLOAT   fChargeMaxCurr;             ////充电最大电流
    FLOAT   fSysPowerOffThreshold;      ////系统停电阈值电压
    FLOAT   fSysPowerOnThreshold;       ////系统来电阈值电压
    FLOAT   fCommonLiLimitCurr;      ////常规锂电充电限流值
    BYTE    ucUsageScen;                ///0：纯锂电；1：混用；2：远供
    BYTE    ucUsageScenBak;                ///0：纯锂电；1：混用；2：远供    
    BYTE    ucCycleMode;                //0:锂电优先；1：N+1循环
    BYTE    ucCycleModeBak;
	BYTE  	ucDischargeMode;            //放电方式
    BOOLEAN bThroughDischgEnable;       //放电直通使能
//    BOOLEAN bOnlyBuckCharge;            //仅降压充电
    WORD    wSwitchSOC;                 //切换SOC1
    WORD    wSwitchSOC2;                 //切换SOC2
    FLOAT   fLASwitchVolt;                 //铅酸切换电压
    BYTE    ucDischargeRate;                 //放电比率
    BYTE    ucDODOfDischarge;             ///单次放电DOD定义
    FLOAT   fRemoteSuplyVolt;           ///远供输出电压
    FLOAT   fDischargeEndVolt1;         ///末期输出电压1
    FLOAT   fDischargeEndVolt2;         ///末期输出电压2
    WORD    wLABattDurPerDischg;        ////铅酸单次放电时间
    BYTE    ucRunMode;                  ////运行模式,0:非受控，1：受控
    FLOAT   fOutVoltOffset;             ///输出电压偏差阈值
//    FLOAT   fPowerDownVolt;             ///掉电电压阈值
    FLOAT   fChgHighTempPrt;
    FLOAT   fChgLowTempPrt;
    FLOAT   fDischgHighTempPrt;
    FLOAT   fDischgLowTempPrt;
    BYTE    bRemoSupplyDischgSwitchSOC; //远供末期放电切换SOC
    BYTE    bRemoSupplyDischgSwitchSOC2; //远供末期放电切换SOC2
    BOOLEAN bBoostChg;
    BOOLEAN bChargeRotate;
    FLOAT   fCellChargeFullVolt;		//充电单体截至电压
    WORD    wSleepShutdownTime;         //休眠关机时间(min)
    WORD    wRechargeSOC;         ///补充电SOC
    FLOAT   fCellSupplVolt;       ///电芯补充电电压
    WORD    wPowerOnCheckTime;    ///来电判断时间
    BYTE    ucChargeMap;          ///充电map使能
    BOOLEAN bSagEqualCurr;
    FLOAT   fSelfDischgACR;     //自放电容量比率
    FLOAT   fCapDCDR;         //容量衰减一致性差比率
} T_BattParaStruct;
#define SYS_VOLT_NUM_MAX    (10)
#define VOLT_SAVE_SECONDS   (30)

typedef struct
{
    FLOAT   afSysVolt[SYS_VOLT_NUM_MAX];
    BYTE    bInit;
    BYTE    ucCounter;
}T_SysVoltInfoStruct;

typedef struct
{
    SHORT   sValue;
    BOOLEAN bValid;
    BYTE    ucProtoType;
    BYTE    ucUpdateTimer;
    BYTE    ucRunMode;
}T_ProtocolSetInfoStruct;

typedef struct
{
    FLOAT fCellVol;
    BYTE  ucSoc;
}T_SOCCalInfoAttrStruct;

typedef struct
{
	BYTE       ucBDUType;      //BDU类型
    // 直流输出信息
    BDU_STATUS        ucBDUStatus; //0:常规充电；1：常规放电；2：升压充电；3：升压放电；4：降压充电；5：降压放电；
    Bool        bBduChargeBreak;    //充电输入断表示停电
    Bool        bCtrChargeFail;   //最近一次充电命令执行成功
    Bool        bSlaveChargeBreak;
    BYTE        ucSlaveDischgCounter;////从机输入断延时
    BYTE        ucReceivedStaitChg;      ///< 下发通电直通标志
    BYTE        ucReceivedStaitDischg;   ///< 下发放电直通标志

    // 电池组信息
    WORD        wBattHighPrecSOC;   //高精度SOC(扩大100倍)
    WORD        wBattSOC;                      // 电池SOC
    WORD        wBattSOH;                      // 电池SOH
    BYTE        ucBduChargeProtectStatus;		//Bdu充电保护状态
    BYTE        ucBduDischargePrtStatus;		//Bdu放电保护状态
    Bool        bBduChgDisable;                  //Bdu充电使能状态
    Bool        bBduDischgDisable;               //Bdu放电使能状态
    Bool        bChargeProtect;                 //充电保护
    Bool        bDischargeProtect;              //放电保护
    Bool        bBattLowVoltPrt;               //电池组欠压保护
    Bool        bCellDemage;                    //单节损坏
    Bool        bCellUnderVoltPrt;
//    Bool        bCellConsistPoolPrt;            //单体一致性差
    Bool        bBduSleep;                         ///BDU处于sleep状态
    Bool        bChargePrtAlarm;                ////是否存在充电保护告警
    Bool        bChargePrtNotVolLow;            ////存在充电欠压以外的保护
    Bool        bDischargePrtAlarm;                ////是否存在放电保护告警
    Bool        bChgPrtByCanSupplyAlm;             //充电保护是否由可进行强补电的告警产生
    Bool        bIrreversibleAlarm;
    BOOLEAN     bChgTempLowPrtOnly;     //是否只存在充电低温保护
//    BOOLEAN     bEqCtlStart[CELL_VOL_NUM_MAX];      //电芯均衡启动状态
    BYTE        ucCellVoltNum;                     //电芯数量
    FLOAT       fSysVol;                       //母排电压
    FLOAT       fBusVol;
    FLOAT       fSysOpenVol;                       //母排开路电压
    FLOAT       fBatVol;                  //电池电压
    FLOAT       afCellVolt[CELL_VOL_NUM_MAX];          //单体电压
    FLOAT       fCellVoltMin;                   //单体最小电压
    FLOAT       fCellVoltMax;                   //单体最大电压    
    FLOAT       fBattCurr;                     //电池电流
    FLOAT       fBusCurr;                      //母排电流
    FLOAT       afCellTemp[CELL_TEMP_NUM_MAX];          //单节温度，其实就是PACK温度，每个PACK有一个温度，一共4个PACK
    FLOAT       fCellAverageTemp;          //单节平均温度
    FLOAT       fCellOCV;                   //电芯OCV
//    FLOAT       fCellOCVBak;                   //电芯OCV记录值
    FLOAT       fCurrMinDet;                //检测误差，ODCU和BDCU不一样
    FLOAT       fBattAverageVol;            ///电池平均电压
    FLOAT       fBattAverageVolBak;
//#ifdef CHARGEMAP_CONFIG
    FLOAT       fCellTempMin;               ////单节最低温度
    FLOAT       fCellTempMax;               ////单节最高温度
    FLOAT       fCellTempMedium;            ////单节温度中位数
//#endif
    FLOAT       fBattRealCap;                   //电池真实AH容量
    WORD        wOCVAmendDelay;            //OCV修正需要反复判断
    FLOAT       fBusVolMax;
//    FLOAT       fBusVolMaxBak;
    FLOAT       fMaxCurrInAll;                   ///各组电流中最大值
    FLOAT       fSysMaxBattVol;                 ///并联电池最大电压
    FLOAT       fMinBattVolt;
    BOOLEAN     bB3BattExist;
    FLOAT       fBalanceCurr;

    WORD        wJudgePowerOnCounter;          //停电来电判断次数
    WORD        wJudgePowerOffCounter;          //停电来电判断次数
    WORD        wJudgeBattVoltRiseCounter;     //系统电压上升计数次数
    WORD        wJudgeBattVoltDropCounter;     //系统电压下降计数次数
	WORD        wJudgePowerOnCounterPureLi;          //停电来电判断次数(纯锂电)
    WORD        wJudgePowerOffCounterPureLi;          //停电来电判断次数(纯锂电)
    WORD        wJudgeCannotSwitchChgCounter;
    // 系统信息获取标志
//    BYTE        ucSysLimit;       // 系统输出状态 是否限流
//    BYTE        ucSelfCurrLimit;    ////主机限流状态
    BOOLEAN     bCellProtectStatus;	// Added by fengfj, 2022-03-17 11:25:47

    // 系统时间
    T_TimeStruct    tTime;  //当前时间

    ////从机信息
    T_SlavesRealData atSlavesRealData[SLAVE_NUM];
    FLOAT       fBattTotalCurr;         ////电池总电流
    Bool        bRunModeControled;          ///运行模式    
    BYTE        ucNormalSlaveNum;           //正常从机数量
    BYTE        ucBusCurrValidNum;
    WORD        wMinSoc;
    WORD        wMaxSoc;
    BYTE        ucMinSocAddr;
    BYTE        ucMaxSocAddr;
    BYTE        ucMinLimitCurr;
    WORD        wLimitCurrSoc;
    BYTE        ucMinAddr;
    BOOLEAN     bSysLimit;
    BOOLEAN     bMyLimit;
    BYTE        ucChgStaNum;
    T_ProtocolSetInfoStruct atProtoSetValue[INFO_MAX_NUM];
    FLOAT       fActivatePortVol;          // 激活口电压
    BOOLEAN     bActivatePortProtect;      // 激活口相关保护
    FLOAT       fAveCurrInCluster;      //簇内平均电流
    WORD        wAveSocInCluster;       //簇内平均高精度soc
    FLOAT       fAveBattVolt;           //簇内平均电池电压
    BOOLEAN     bCAN2DischExist;        //簇间是否存在放电的电池簇
    BYTE        ucBattCycleTimesCal;    // 循环次数校正标志位
    BYTE        ucFMBatteryleftcapacity;          //整个站点的电池剩余容量，SOC百分比
    BYTE        ucFMChgAndDischgDisable;    //整个站点的充放电禁止状态
    BYTE        ucFmNormalSlaveNum;      //调频时正常从机数量
    FLOAT       fFMCurrentMaxChgPower;          //整个站点的充电最大功率
    T_FmDistributestruct tFmData;       //记录调频状态和调频功率
    WORD        wSlaveAvgSOC;    //从机平均SOC
    T_HardwareParaStruct tHardWareStruct;
} T_DataInfoStruct;

typedef struct
{
    UINT32 ulDisableFlag;//轮换标志
    UINT32 ulDisableStat;//轮换状态
    UINT32 ulCurrStat;//电流状态
    UINT32 ulCurrPrt;//保护状态
    BYTE   ucTransCounter;
    BYTE   ucInitStat;
    FLOAT  fBusMaxVol;
    FLOAT  fMaxCurrInAll;
    BYTE   ucMaxSocAddr;
    WORD   wMaxSOC;    
    BYTE   ucMinSocAddr;
    WORD   wMinSOC;
    BYTE   aucCtrlRotateLimitCurr[SLAVE_NUM];    
    BYTE   aucSlaveRotateCurr[SLAVE_NUM];
}T_RotateDebugStruct;

typedef struct
{
    BYTE   ucSOCCalStatus;
    BYTE   ucBatStatus;
    BYTE   ucIfBduDischarge;
    FLOAT  fBattCurr;
    FLOAT  fCellTempMax;
    FLOAT  fBattVol;
    FLOAT  fMinBatVol;
    FLOAT  fMaxBatVol;
    FLOAT  fBattCap;
    WORD   wBattSOC;
    UINT32 ulDischargeSOCCalTimer;
    UINT32 ulDischargeBatVolDiffTimer;
}T_SOCCalDebugStruct;

typedef struct
{
    BOOLEAN     abCtrlSlaveRotateChargeClose[SLAVE_NUM];////how to control the slave charge, charge or not.    
    BYTE        aucCtrlSlaveCurrLimit[SLAVE_NUM];
//    BYTE        aucNoCurrCounter[SLAVE_NUM]; ////没保护，充电启动以后长时间没电流.
    BYTE        aucFaultCounter[SLAVE_NUM]; ////if not support rotate ctrl command, do not control it.    
    BYTE        ucRotateOpenCounter;////counter for open bms
    BYTE        ucMinRotateCurr;////
    WORD        wRotateCloseCounter;////founter for close bms    
    BYTE        ucRotateCurrLimitCounter; ////
    BYTE        ucMaxSocSnInOpen;////Whose Soc is maxinum in all Charging BMS.
    BYTE        ucMinSocSnInClose;////Whose Soc is mininum in all non-Charging-but-charge-ready BMS.
    BOOLEAN     bStartChargeRotate;////start charge rotate.
    WORD        wRotateHeaterCounter;////counter for open bms
}T_RotateInfoStruct;


typedef struct
{
    T_BattParaStruct    tPara;  //输入电池管理相关设定参数部分
    T_DataInfoStruct    tData;  //输入电池管理相关实时数据部分
} T_BattInfo;


typedef struct
{
    // 1、输入信息
    BOOLEAN     bPowerOff;  //停电标志 （=1停电;=0有电）
    FLOAT       fBattSOH;                       //电池SOH
    FLOAT       fBattCap;             ///电池当前实际AH容量 用于AH积分、校正，最终计算SOC
    FLOAT       fBattCapBak;          ///电池容量备份
    FLOAT       fBattCapSave;         // ///保存时的AH数，用于判断是否要保存
    BOOLEAN     bBattDischarge;       ///依据电流判断电池在放电
    BOOLEAN     bBattDischargeBak;      ////上次是否放电
    BOOLEAN     bBattChargeFull;       ////满足SOC为100%条件，可以进行SOH修正
    BOOLEAN     bBatt99PercentFull;       ////SOC 99%满
    BOOLEAN     bCellOverVolPrt;               ///过压保护告警
    BOOLEAN     bRealCellOverVolPrt;           ///实际过压保护告警
    BOOLEAN		bCellLowVolPrt;		    //欠压保护告警
	BOOLEAN		bBattOverVolPrt;		//电池组过压保护告警
    BOOLEAN		bBattOverCurrPrt;		//电池组过流保护告警
//    WORD        wCalibrateCounter;
	
    FLOAT       fCalenderLifeDecline;    //日历循环寿命衰减值
    FLOAT       fCycleLifeDecline;   //循环次数寿命衰减值
    FLOAT       fAddedLifeDecline;   //附加寿命衰减值
    WORD        wBattCycleTimes;        //循环次数，用于实时信息显示
    T_TimeStruct tLastCalcCalenderTime;   //长期掉电时计算日历寿命衰减
    BOOLEAN     bSave2eeprom;   ////是否保存EEPROM
    BOOLEAN     bNewSave2eeprom;   ////新增结构体是否保存EEPROM
    BYTE        ucBduType;      ////BDU类型,用来比较不同BDU类型

    /*充电设定值*/
    FLOAT        fChargeCurrHope; // 电芯侧电流
    FLOAT        fChargeCurrHopeBak; ////解决能源充足时增加最大电流参数，快速上升问题
    FLOAT        fChargeBusCurrHope; // 母排侧电流
    FLOAT        fChargeSetVol;     // 当前设定电压
//    BOOLEAN     bChargeLimit;       //是否要充电限流
    WORD         wThrouhgChgCounter;////充电直通退出计数

    WORD        wChargeCurrCounter;////缓慢调整充电电流的计数器
    FLOAT       fChargeSetCurr;     	// Added by fengfj, 2019-11-07 12:23:16
    FLOAT       fChargeBusSetCurr;
    BOOLEAN     bChargeCurrMin;	//限流到最小 Added by fengfj, 2019-11-07 15:24:10
//    WORD        wSetChgCurrBak;
    BOOLEAN     bChargeCurrMinBak;	//限流到最小 Added by fengfj, 2019-11-07 15:24:10
    BOOLEAN     bChargeCurrEnough;	//充电电流足够
    FLOAT       fBattChargeCurrMaxWhenLimit;///充电限流时最大
    FLOAT       fBattChargeCurrMaxWhenLimitBak;///充电限流时最大
    FLOAT       fSysChgMaxCurr;     	//System charge maximum current.
    BOOLEAN     bChargeModeChange;      ////充电状态改变
    FLOAT       fMultiChargeCap;      //累计充电容量
    FLOAT       fMultiChargePower;    //累计充电电量

    /*放电设定值*/
    FLOAT       fDischargeLimitCurr;  // 当前设定放电限流点
    FLOAT       fDischargeSetVol;     // 当前放电电压
    BYTE        ucDischgDelayCounter; //放电延时计数
    FLOAT       fDischargeCapForCelebrate; ///单次放出容量 用于SOH修正
    FLOAT       fTotalDischargeCap;        ///全部放出容量，用于计算放电次数
    FLOAT       fBatteryAverageVol;        ////电池平均电压
    FLOAT       fDischargeCurrAdd;         ////电池放电电流要增加多少(CSU下发电流调整值) add mwl 1106

    FLOAT       fMultiDischargeCap;        //累计放电容量
    FLOAT       fMultiDischargePower;      //累计放电电量
    FLOAT       fBattDisChargeLeftMinutes; //放电剩余时间
    FLOAT       fBattChargeLeftMinutes;    //充电剩余时间

    ////受控模式下ByCtrl类数据参数起作用
    T_ProtocolSetInfoStruct       tOutputVoltByCtrl;      ///CSU通过控制命令设置的输出电压
    T_ProtocolSetInfoStruct       tChgCurrCeofByCtrl;     ///CSU通过控制命令设置的充电限流系数
    T_ProtocolSetInfoStruct       tOutputVoltNonCtrl;     ////非受控下发放电电压
    T_ProtocolSetInfoStruct       tChgCurrCeofNonCtrl;    ////非受控下发充电限流系数
    T_ProtocolSetInfoStruct       tMaxSocCtrl;

    FLOAT        fChargeHopeVol;       // 最终的设定充电电压
    FLOAT        fDischargeHopeVol;    // 最终的设定放电电压
    FLOAT        fDischargeHopeVolTem;
    FLOAT        fCompVol;
    FLOAT        fMinBatVol;           //放电SOC校正启动过程最小电压
    FLOAT        fMaxBatVol;           //放电SOC校正启动过程最大电压
    FLOAT        fSysVoltOnPowerOff; //停电时的系统电压

    WORD         wNoDisChargeCurrTimer;
    FLOAT        fPowerDownVolt;     ///掉电电压阈值
    FLOAT        fSlavePowerDownCompVolt; ////从机的补偿，消除检测误差影响:主机不补偿，从机补偿来对齐主机
    FLOAT        fBusVoltAvgForMix;  ///BUS平均电压，用于计算混用电压
    FLOAT        fBusVoltSum;        ///BUS电压和
    WORD         wBusVoltNum;
    FLOAT        fBusAvgVoltReal;   ////母排有电阶段实际电压
    WORD         wRecalculatePowerdownVoltDelay;///重新计算掉电电压

    /*-----N+1功能------*/
    BYTE        ucNextDischargeMode;    ////0:未知，1：锂电；2：铅酸
    BYTE        ucLiDischargeNTimes; ////锂电放电次数
//    BOOLEAN     bLABatteryFull;     ////铅酸满电
    FLOAT       fDischargeCapForNTimes;      ///放出容量 用于N计算
//    BYTE        ucSysVoltGT56Cnt;   ////系统电压大于56计数
//    BYTE        ucSysVoltLt55Cnt;   ////系统电压小于55计数
    BOOLEAN     bDischargeFromFull; ///从满电开始放电的
    BYTE        ucControlledDelayCnt;////延时计数

    // 电池管理模式
    BYTE        ucChargeMode;   // 管理模式
    BYTE        ucBatStatus;    // 当前状态
    BYTE        ucSleepBattMode; //接can2电池休眠后仍强制主机，电池的管理模式
    BYTE        ucSleepBattStatus; //接can2电池休眠后仍强制主机，电池的当前状态
    
//    BYTE        ucBatJugeStatus;    // 监控侧判断的状态
//    BOOLEAN     bJugeStatus;		//监控侧判断状态
    BYTE        ucTransCounter;
    BOOLEAN     abBalanceStart[CELL_VOL_NUM_MAX];
    BOOLEAN     abBalanceCount[CELL_VOL_NUM];
    BOOLEAN     bChargeTimerEnable;             ///计数使能
    BOOLEAN     bChargeEndHold;                 ///处于均充末期
    INT32S      slBattChargeMinutes;            ////电池充电时长：分钟
	INT32S      slDefenseBattChargeMinutes;     // 用于布防充电判断的电池充电时长(min)
    WORD        wBattChargeEndHoldSeconds;     //电池充电末期维持时间
    BYTE        ucBduCtrlMode;
	
    WORD        wBattFullHoldMinutes;          //电池在线非浮充维持时间，用于SOC置满判断
	INT32S      slDeclineTimer;                 //日历寿命时间每天
	WORD        wBalanceTimer;                  //均衡计数器
	BYTE        ucStopBalanceCounter;           //
    WORD        wBattCompleteFullCounter;      ///电池全满，3.45V
    BYTE        ucChargeCounter;                //充电模式计数，用于延时判断
    WORD        wNoChargeCounter;               //计时不处于充电状态的时间，超过超时时间则判断退出充电
    UINT32      ulDischargeSOCCalTimer;         //计时处于放电SOC校正状态的时间
    UINT32      ulDischargeBatVolDiffTimer;     //计时处于整组电压变化范围内的时间

    BOOLEAN     bSelfRechargeTimerEnable;       //
    INT32S      slSelfRechargeTimer;
    INT32S      slStandByTimer;
    INT32S      slSelfDischgCalcTime;           // ////计算静置条件自放电时间
    WORD        wLABattDischargeTimer;         ///铅酸放电时间
    WORD        wUvPrtTimer;                    //欠压保护计时
    INT32S      sICOpenTimer;                   //IC打开计时
    FLOAT       fUvPrtSleepBattVolt;            //休眠前的电池组电压
    FLOAT       fQuietSleepBattVolt;            //静置休眠前的电池组电压
    WORD        wDischargeEndTimer;             //放电末期计时
    BOOLEAN     wUvPrtSleepFlag;                //欠压条件进入休眠标志
    WORD        wBattIdleTimer;                 //静置计时
    time_t       tOfflineTimeStamp;             //启动假休眠时间
    BYTE        ucCellUnderVoltTimer;
//    BOOLEAN     bShouldLimitCharge;             ////是否限流充电
    BYTE        ucCellType;                     ////电芯厂家
    Bool        bModeChange;

    ////对于休眠的管理，在单独的模块中进行判断，解除耦合
    FLOAT       fSysVoltChange;                 ///母排电压变化
    FLOAT       fSysOpenVoltBak;
    FLOAT       fBattCurrBak;
    BYTE        ucCurrLimitFlagBak;            //上次限流标志
    
    BOOLEAN     bCellTempLow;                  //某个单节低于10度         
    BOOLEAN     bCellTempHigh;                  //某个单节高于50度
    BOOLEAN     bCellTempTooHigh;                  //某个单节高于55度
    BOOLEAN     bCellVoltHigh;                   //单节高告警 3.65
    BOOLEAN     bCellVoltTooLow;                   //lower than 2.5V
    BOOLEAN     bBattVoltHigh;				//电池组电压高于52.5V
    BOOLEAN     bCellVoltLow;               ////lower than 2.8V

    BOOLEAN     bCellUnderVolt;             //代码走查：强补电标志添加说明
    BOOLEAN     bCellFull;

    BOOLEAN     bMasterChgFull;
    BOOLEAN     bLoopOff;
    WORD        wLoopOffCounter;
    WORD        wLoopOffShutDownCounter;
    WORD        wSlavePowerCounter;

    time_t       tSecondCalcTime;                       ////秒计时时刻点
    ULONG       ulSecondTimeDiffSec;                       ////秒计时两次时间差，S

    time_t       tMinuteCalcTime;                      // ////计算时间，用于精确及时
    ULONG       ulMinuteTimeDiffSec;                      // ////分钟计时两次时间差，S
    BOOLEAN     bThroughChg;                       ///< 充电直通标志
    BOOLEAN     bThroughDischg;                 ///< 放电直通标志
    BOOLEAN     bChargeThroughBak;    
    BOOLEAN     bChargeThroughEnable;               ///直通充电末期是否再次进入直通
    BOOLEAN     bVoltSwitchFlag;                 //远供末期放电电压电压切换模式是否达到切换条件标志
	BOOLEAN     bSmooth;            ////输出电压是否进行了平滑处理
	
	FLOAT       fCsuSentCurr;  
    FLOAT       fDischgEqualCurr;   ////放电均流电流
    FLOAT       fChargeEqualCurr;   ////充电均流电流
    BOOLEAN     bChargeEqualCurrValid;  ///
    BYTE        ucPMOSRestoreCounter;
    BYTE        ucCellProtectCounter;    
    BOOLEAN     bBattRotateDisable;

    BOOLEAN     bNextCharge;
    BYTE        ucNextChgCounter;
    BYTE        ucRotateMaxCurr;
    BOOLEAN     bInitMixDischg;
    BOOLEAN     bFirstEnterDischg;
    FLOAT       fMixTargetVolt;
    
    BOOLEAN bCellVoltHighLock;
    BOOLEAN bCellVoltLowLock;

    // T_RotateInfoStruct tRotate;

    WORD      wFullChargeLastDura;                 //满充计时小时

    T_StateListenStruct atCellVoltTooLow[CELL_VOL_NUM];
    T_StateListenStruct atCellVoltLow[CELL_VOL_NUM];
    T_StateListenStruct atCellVoltHigh[CELL_VOL_NUM];
    T_StateListenStruct atCellVoltTooHigh[CELL_VOL_NUM];
    T_StateListenStruct atCellUnderVolt[CELL_VOL_NUM];
    T_StateListenStruct atCellFull[CELL_VOL_NUM];
    T_StateListenStruct atCellVoltHighLock[CELL_VOL_NUM];
    T_StateListenStruct atCellVoltLowLock[CELL_VOL_NUM];

    T_StateListenStruct atCellTempHigh[CELL_TEMP_NUM_MAX];
    T_StateListenStruct atCellTempLow[CELL_TEMP_NUM_MAX];
    T_StateListenStruct atCellTempTooHigh[CELL_TEMP_NUM_MAX];

	T_StateListenStruct atBattVoltHigh;

    T_SlavesRealData atSlavesRealDataBak[SLAVE_NUM];
    WORD        wMixPowerOnCounter;
    BYTE        ucSlaveDisableCounter; ////从机充电被禁止轮换时间

    BYTE       ucBattCtrlStat;
    BYTE       ucUpBuzzTime;
    WORD       wNeedChgTimer;
    WORD       wNeedCancelChgLimitTimer;
    BOOLEAN    bDirectStartChgJudge;
    BOOLEAN    bNeedLimitCurrCharge;
    BOOLEAN    bNeedDeclineDischgVolt;
    WORD       wPowerOffTime;    //停电时间
    BYTE       ucMixSwitchCount;
    BYTE       ucCtrlChgAndDischgDelay;    //开机充放电延时启动
    WORD       wEqualChgPowerOffTime;  ////均充停电后不放电5小时计时
    BYTE       ucChangePowerOnDelay;   ////调整来电电压阈值延时2分钟
    BOOLEAN    bSourceInsufficientTimerStart; // 源不足计时开始
    WORD       wSourceInsufficientCount;      // 源不足窗口计数
    WORD       wChargeModeChangeTimeDiff;     // 最近两次转放电时差
    BYTE       ucChargeModeChangeCount;       // 窗口内转放电计数
    BYTE       ucIrreversibleAlarmCount;      //不可逆告警持续时间
    WORD       wChgNoCurrCount;
    BYTE       ucUpdateThreCount;
    BYTE       ucChargeClearActivatePortVoltErrCnt;  //清除转充电激活口电压异常计数  15秒
    BYTE       ucChargeActivatePortVoltErrCnt;       //转充电激活口电压异常计数（充放电反复切换）
    BYTE       ucPowerOnCounter;                     //正常开机计时
    BYTE       ucActivateCounter;                    //持续5秒激活
    BOOLEAN    bActivateStart;                       //是否开始激活
    BYTE       ucActivateCurrCounter;                //电流持续5秒
    BYTE       ucActivateVoltCounter;                //电压持续20秒
    WORD       wActivateNoCurrAndVoltCounter;        //没有电压和电流持续10分钟
    UINT32     ulActivateTryCounter;                 //24小时重新尝试计时
    BOOLEAN    bActivateTry;                         //上电尝试
    BOOLEAN    bActivatePortCurrError;               //激活口回路电流异常标志（回路有充电电流）
    BOOLEAN    bActivatePowerOffFlag;                //激活充电调低掉电电压
    BYTE       ucActivatePowerOffFlagCounter;        //激活充电调低掉电电压计时
    BYTE       ucCellVoltNum;                        //单体电压数量
    BYTE       ucDcrStatus;                          // DCR测试状态    
    INT32S     slDcrCtrlChargeTimer;                 // 
    INT32S     slDcrStopChargeTimer;                 // DCR停止充电计数
    INT32S     slDcrTestTimer;                       // DCR测试条件判断计数
    UINT32     ulDcrTestPeriodCnt;                   // DCR测试周期计数
    FLOAT      fDcrCurr[2];                          // 记录DCR测试过程中的两次电池组电流
    FLOAT      fDcrBattVolt[2];                      // 记录DCR测试过程中的两次电池组电压
    FLOAT      fDcrCellVolt[2][CELL_VOL_NUM];        // 记录DCR测试过程中的两次单体电压
    FLOAT      fCurrAdj;
    FLOAT      fSocAdj;
    T_Can2ObjData tEqualObjData;
    BYTE       ucCAN2SlavePowerOnCount;
    WORD       wGroupMaxSoc;
    FLOAT      fAdjVolt;

    BOOLEAN     bPeakDischg;             //错峰放电标志位
    FLOAT       fPeakDischgVolt;         //错峰放电需要抬升电压，最终的计算值放在此变量中
    WORD        wMixBattFullTimer;       
    BYTE        ucElectricityPrice;      // 当前电价
    BOOLEAN     bPeakShiftChgDisable;    //错峰充电禁止
    BYTE        ucPeakDichgNum;          //停电变为来电，错峰放电之前需要等待210S，此变量为时间计数
    BOOLEAN     bValleySetCurr;          //谷充电限流值
    BOOLEAN     bPeakShiftOff;           //混用场景下，停电后来电，错峰放电需要等待一段时间，等待一段时间此标志位复位，才能正常错峰放电
    WORD        wShiftPeakCounter;     //停电错峰延时计数

    BOOLEAN     bFmChgDisable;           //调频充电禁止
    FLOAT       fFmMaxCurr;              //调频最大电流
    BOOLEAN     bFmDischg;               //是否处于调频放电状态
    BYTE        ucLastFmSta;             // 上一时刻调频状态
    FLOAT       fFmDischgVolt;           // 调频放电电压
    FLOAT       fReachMaxCurret; //电池最大能够达到的电流
    BOOLEAN     bPkSftSlaveDisableDischgFlag; //错峰时，主机放电保护，从机放电禁止标志位
    BOOLEAN     bCtlLock;

#ifdef UNITEST
    WORD wDischargeModeTimes;
    WORD wChargeModeTimes;
#endif
#ifdef ZXESM_R321_APTOWER_VERSION
    BYTE        ucBattIdleStatus;       // 电池静置状态
#endif
#ifdef PAKISTAN_CMPAK_PROTOCOL
    ULONG       ulAccuBattRunTime;       // 电池累计运行时间
    WORD        wRunOneHour;	         // 运行时间计数器
    ULONG       ulAccuDischTimes;        // 电池累计放电次数
    WORD        wDischgTime;             // 放电次数计数器
#endif
} T_BattDealInfoStruct;

/************************************************************
** 结构名: T_BattResult
** 描  述: 电池输出数据结构定义
** 作  者: 严宏明
** 日  期: 2004-06-21
** 版  本: V4.0 
** 修改记录         
** 日  期       版  本      修改人      修改摘要
** 
**************************************************************/
typedef struct
{    
    BOOLEAN     bPowerOff;  //停电标志 （=1停电;=0有电）

    // 输出信息
    FLOAT   fSetChgVolt;          //设定充电电压
    FLOAT   fSetDischVolt;        //设定放电电压
    WORD    wSetChgCurr;          //设定电池侧充电电流 千分比
    WORD    wSetBusChgCurr;        //设定Bus侧充电电流 千分比
    WORD    wSetDischCurr;        //设定放电电流 千分比

    // 电池组信息    
    WORD        wBatSOC;    //电池SOC,单位 %
    WORD        wBatSOH;    //电池SOC,单位 %
    BYTE        ucCellEquSta; //电芯均衡状态
    WORD        wCellBalVoltBits;    // 电芯均衡按位信号
    WORD        wCellBalVoltBitsBak;    // 电芯均衡按位信号    

    // 电池管理记录
    BYTE       ucBatStatus;    // 当前状态
   
    BOOLEAN     bCtlChargeDisable;                  //充电禁止
    BOOLEAN     bCtrlDischargeDisable;               //放电禁止
    Bool        bChargePrtAlm;                      //充电保护告警
    BOOLEAN     bDischargePrtAlm;                   //放电保护告警
    BYTE        ucMasterStatus;
    WORD        wDischargeCap;      //用于计算循环次数的放电AH数,精度1

    WORD        wCalenderLifeDecline;    //日历循环寿命衰减值
    WORD        wCycleLifeDecline;   //循环次数寿命衰减值
    WORD        wBalanceCounter;
    BYTE        ucAddedLifeDecline;   //附加寿命衰减值
    BYTE        ucInputBreak;   //输入断标志
    WORD        wChargeLeftMinutes;//充电剩余时间
    WORD        wDisChargeLeftMinutes;//放电剩余时间
    FLOAT       fTotalDischargeCap;//累计放电容量
    FLOAT       fTotalDischargeQuanty;//累计放电电量
    FLOAT       fTotalChargeCap;//累计充电容量
    FLOAT       fTotalChargeQuanty;//累计充电电量
    // Added by fengfj, 2020-08-16 19:22:48
    BYTE        ucDelayCounter;
    BYTE        ucSourceEnough;
    FLOAT       fLastBusVolt;
    BYTE        ucCellPrtCounter;
    BYTE        ucCellPrtRestoreCounter;
    BYTE        ucCellProtectCounter;
    BYTE        ucCellProtectStatus;
    // End Added

    WORD        wFullChargeLastDura;//最近一次充满时间

    WORD        wBattHighPrecSOC;//高精度SOC(扩大100倍)
    FLOAT       fPowerOnVolt;    //来电电压阈值
    FLOAT       fPowerOffVolt;    //停电电压阈值
    FLOAT       fPowerdownVolt;  //掉电电压阈值
    BOOLEAN     bLoopOff;
    BOOLEAN     bSwUpgrade;
    WORD        wGroupMaxSoc;
    FLOAT        fBoostVol;    
    BYTE        ucRotateMaxCurr;
    BYTE        ucBattCtrlStat;
    WORD        wBattPowerOffTimeRes;   //停电时间
	INT32S      slDefenseBattChargeMinutes;    // 用于布防充电判断的电池充电时长(min)
    BOOLEAN     bB3BattExist;
    FLOAT       fBalanceCurr;
    BOOLEAN     bBattDcrFaultAlmSta;// DCR异常告警状态
    BOOLEAN     bBattDcrFaultPrtSta;// DCR异常保护状态
    FLOAT       fDcrDeltaCurr;// DCR电池组电流差值
    FLOAT       fDcrDeltaBattVolt;// DCR电池组电压差值
    FLOAT       fDcrDeltaCellVolt[CELL_VOL_NUM];// DCR单体电压差值
    FLOAT       fBattDcr;// 电池组DCR
    FLOAT       fCellDcr[CELL_VOL_NUM];// 单体DCR
    FLOAT       fAveCurrInCluster;
    WORD        wAveSocInCluster;
    WORD        wAveSocInAllCluster;
    FLOAT       fAveBattVoltInCluster;
    FLOAT       fVoltAdjVal;
    BYTE        ucSleepBattStatus;
    #ifdef ZXESM_R321_APTOWER_VERSION
    BYTE        ucBattIdleStatus;       // 电池静置状态
    #endif
}T_BattResult;

typedef struct
{
    size_t offset;
    BYTE ucNum;
}T_AlarmConfigStruct;

enum
{
    CHARGE_MAP_DEFAULT = 0,
    CHARGE_MAP_LOW,
    CHARGE_MAP_HIGH,
};

enum
{
    CELL_TYPE_AUTO = 0,
    CELL_TYPE_1,
    CELL_TYPE_2,
    CELL_TYPE_3,
    CELL_TYPE_4,
};

enum
{
    BATT_STATE_CHARGE = 0,
    BATT_STATE_DISCHG,
    BATT_STATE_IDLE,
    BATT_STATE_PROTECT,
};

enum
{
    NO_LOOP_FAULT = 0,
    INCREASE_VOLT_LOOP_FAULT,
    DECREASE_VOLT_LOOP_FALUT_MOMENT,
    DECREASE_VOLT_LOOP_FAULT,
};

enum
{
    CONNECT_CONTACTOR = 0,
    DISCONNECT_CONTACTOR,
};

enum
{
    DEFUALT_COMPENSATION = 0,
    SMALL_CURR_COMPENSATION,
    CAL_SELF_DISCHAGE_DECREASE_CAP_WITH_PERIOD,
    CAL_SELF_DISCHAGE_DECREASE_CAP,
};

typedef struct 
{
    BYTE BreakRelayCounter;       //接触器在24小时内断开的次数
    BOOLEAN ContactorStatusFlag;      //接触器的状态，是断开还是闭合，断开为0，闭合为1
    WORD wTimerConuter;           //电压二级保护时间计数器，从接触器第一次断开开始计数
    BOOLEAN TimerStatus;          //时间计数器的状态，为0表示没有开始计时，为1表示开始计时
    BOOLEAN bJudgePrtVoltFlag;    //是否需要进行接触器断开的操作，主要用于区分单体充电截至电压大于单体过压保护阈值的情况
    BOOLEAN bCellProtectFlag;     //用来判断是否产生了单体过压保护，单体过压保护的当前状态
    BOOLEAN bCellProtectFlagBak;  //单体过压保护，单体过压保护的上一次判断状态
    BOOLEAN bGenerateBattLockFlag;    //电压二级保护产生闭锁告警的标志
}T_VoltTwoClassPrtStruct;

typedef struct
{
    BOOLEAN		bBattLowVolPrtTriggered;		
	BOOLEAN		bBattOverVolPrtTriggered;		
    BOOLEAN		bBattOverCurrPrtTriggered;		
    BYTE		ucBattUVPShutDownFlag;
    time_t tTime;    		//异常关机时间
    time_t tTime2;          //关机后重启时间

    ULONG     aulBattCellTempandSOCRangeTimer[CELL_TEMP_RANGE_NUM][SOC_RANGE_NUM]; ////统计各温度和SOC区间运行状态累计时间
    ULONG     ulBattUVPTimer;//欠压保护状态累计时间
    ULONG     ulBattOVPTimer;//过压保护状态累计时间
    ULONG     ulBattOCPTimer;//过流保护累计时间
    ULONG     ulBattUVPShutDownTimer;//欠压保护关机累计时间
    WORD      wBattUVPCounter;//欠压保护状态累计次数
    WORD      wBattOVPCounter;//过压保护状态累计次数
    WORD      wBattOCPCounter;//过流保护累计次数
    WORD      wBattUVPShutDownCounter;//欠压保护关机累计次数

    WORD           wCheckSum;  
}T_AnalyseInfoStruct;

typedef struct
{
    WORD        wBattFullCounter;
    WORD           wCheckSum;

}T_BattFullStruct;


#ifdef PAKISTAN_CMPAK_PROTOCOL
typedef struct 
{
    ULONG           ulAccuBattRunTime;  //累计运行时间
    ULONG           ulAccuDischTimes;   //累计放电次数
    CHAR            acReserve[8];
    WORD            wCRC;
} T_CmpakSaveInfo;
#endif

/*********************  函数原型定义  **********************/
void InitBatt( void );
void BatteryManagement( void* parameter );
void GetBattResult( T_BattResult *pBattOut );
void SetBattResult( T_BattResult *pBattOut );
void SetACStatus( BOOLEAN bACStatus );
void SetChgHopeVol(FLOAT fVol);
void SetDischgVol( short iVol, WORD wRefSOC );
//void SetConsrv( BOOLEAN bConsrv );
BOOLEAN IsSleep( void );
BOOLEAN IfBduCharge(void);
BOOLEAN IfBduDischarge(void);
INT32 SetOutputVolt(FLOAT fOutputVolt);
BOOLEAN SetBattSOH(BYTE ucSOH);
BOOLEAN SetBattCycleTimes(WORD wTimes);
INT32S SetValueFromProtocol(BYTE ucProtType, BYTE ucInfoType, SHORT sValue);
BOOLEAN IsNorthCommFail(void);
void SetBattChargeCurrAndMaxBusVol(FLOAT fChargeCurr, FLOAT fMaxVol, FLOAT fMaxCurr);
void checkApptestRst(void);
BOOLEAN IfBattBuckCharge(void);
INT32 GetValueFromProtocol(BYTE ucInfoType, T_ProtocolSetInfoStruct* );
BOOLEAN GetLoopOffStat(void);
// INT32S DischargeCurrAdd(FLOAT fCurr);
// INT32S DealCsuSentCurrForSOC(SHORT sCurr);
void initConsrvCtrl(void);
//void consrvManager(void);
void SaveBattInfo(void);
BOOLEAN GetCellVoltStat(void);
void SetRotate(BYTE ucCurr, BOOLEAN bChargeDisable);
BOOLEAN GetSlaveRotateDisable(void);
void SetBattModeDisable(BYTE ucTemp);
void SetRotateMaxCurr(BYTE ucCurr);
void GetRotateDebugInfo(T_RotateDebugStruct* ptData);
void GetSOCCalDebugInfo(T_SOCCalDebugStruct* ptData);

void SetSlaveEqualizeCurr(WORD wEqualizCurr, WORD wCap, BYTE bMasterFull);
INT32S SetChargeMode(BYTE ucChargeMode);
WORD GetRealBattCap(void);
BYTE CtrlBattery(BYTE ucCtrlCode);
BOOLEAN batteryUnitest(BOOLEAN bFlag);

BOOLEAN IsTransPeriod(void);
BOOLEAN IfConditionOkNTimes(BOOLEAN condition, WORD *pucCounter, WORD ucMaxTimes);
FLOAT CalBoostVol(void);
void SmartLi_CalcChargeHopeCurr(void);
void CheckDishargeModeAndSwitch(void);
// void ClearProtoSetValue(BYTE ucInfoType);
void InitSetCurrAdjust(void);
void SetProtoSetInfoInvalid(BYTE ucInfoType);
void ClearPowerdownVoltOffset(void);
BOOLEAN IfSmartLiDischgStopDelay(void);
Bool IsProtoSetInfoInvalid(BYTE ucRunMode);
FLOAT clacDischCurr(void);
BOOLEAN GetPowerOffStatus(void);
BOOLEAN getSwUpgrade(void);
BYTE getBattChargeMode(void);
void SetDisChargeVol(WORD wDisChargeVol);
BOOLEAN GetStatusConsistFault(void);
void SetPowerdownVol(WORD wDisChargeVol);

void TransAnalyticalAlm( T_AnalyseInfoStruct* tAnalyseInfo, BOOLEAN bWriteFlag);
void ClearAnalyticalAlm( void );
void GetAnalyticalAlm( T_AnalyseInfoStruct* tAnalyseInfo );
void ReadAnalyseInfo(void);

BYTE GetFlagBattery(void);
BOOLEAN GetChgCurrStatus(void);
BOOLEAN BatteryExceptionJudge( void );
VOID SetDischargeOutputVoltage(FLOAT VoltValue);
BYTE GetJudgeIfLoopFaultFlag(void);
VOID CalculateAdjustVoltTime(void);
VOID SetExternalPowerOnFlag(BOOLEAN Value);
BOOLEAN GetNativePowerOnStatus(void);
VOID SetNativePowerOnStatus(BOOLEAN Value);
VOID LoopOffVoltAdjust(T_BattDealInfoStruct *ptBattDeal, T_BattInfo *ptBattInfo); //回路异常判断时的电压调整
VOID SetContactorStatus(BYTE bStatus);
BOOLEAN GetVoltTwoClassTimeStatus(VOID);
VOID VoltTwoClassTimeCounter(VOID);
VOID VOltTwoLevelProtectEnter(T_BCMAlarmStruct *pBCMAlm ,T_SysPara *pSysPara);  //电压二级保护入口函数
BYTE GetVoltTwoClassBreakRelayCounter(VOID);
VOID SetVoltTwoClassBreakRelayCounter(BYTE bBreakRelayCounter);
WORD GetVoltTwoClassStatsTime(VOID);
VOID SetVoltTwoClassStatsTime(WORD wTimeMinu);
FLOAT CalBoostVolB3(void);
WORD GetCellCycleTimes(void);
BOOLEAN SetCellCycleTimes(WORD wTimes);
BOOLEAN SetDcrFaultPrtNormal(void);
BOOLEAN SetDcrFaultAlmNormal(void);
BOOLEAN SetCurrBalanceMethod(BOOLEAN bB3Exist);
BOOLEAN LoopOffRestore(void);
BOOLEAN SynCAN2ObjData(T_Can2ObjData* ptObjData);
BOOLEAN BattThreadMutexTake(rt_int32_t lTime);
BOOLEAN BattThreadMutexRelease(void);
void KeepOutputEqualBatteryVoltage(void);
BOOLEAN SetFmChgDisable(BOOLEAN bDisable);
BOOLEAN SetFmMaxCurr(FLOAT fPower);
BOOLEAN SetFmDischgVolt(FLOAT fVolt);
BOOLEAN GetBattRotateDisable(void);
FLOAT GetSelfAdaptionInitVolt(void); //获取自适应模式初始电压，只在电池管理初始化阶段赋值
BOOLEAN CtrlReleaseLock(void);

#ifdef INTELLIGENT_PEAK_SHIFTING
BOOLEAN JudgePeakShiftCondition(T_BattInfo *pBattIn,T_BattDealInfoStruct *pBattDeal,T_PeakShiftPara *pPeakShift); //是否进行错峰放电的条件判断
void SetPeakShiftDisable(BYTE ucTemp);   //设置错峰放电禁止充电标志位
T_PeakShiftPara *GetShiftPeakPara(void);  //获取错峰参数
WORD PowerOffPeakTimeCounter(void);   //停电错峰延时计数
#endif

#ifdef __cplusplus
}  /* end of the 'extern "C"' block */
#endif

#endif  // SOFTWARE_SRC_APP_BATTERY_H_;
