/**************************************************************************
* Copyright (C) 2001, ZTE Corporation.
* 版权信息：（C）2010-2022，深圳市中兴通讯股份有限公司电源开发部版权所有
* 系统名称：
* 文件名称：qtp.c
* 文件说明：协议模块
* 作    者：
* 版本信息：
* 设计日期：
* 修改记录：
* 日    期      版  本      修改人      修改摘要
* 其他说明：
***************************************************************************/

#include "common.h"
#include "sample.h"
#include "hisdata.h"
#include "realAlarm.h"
#include "battery.h"
#include "para.h"
#include "comm.h"
#include "protocol.h"
#include "CommCan.h"
#include "Candrive.h"
#include "HeaterControl.h"
#include "commBdu.h"
#include "led.h"
#include "apptest.h"
#include "qtp.h"
#include "flash.h"
#include "fileSys.h"
#include "pdt_version.h"
#include "utils_rtthread_security_func.h"
#include "wireless.h"
#include <netif/ethernetif.h>
#include <sys/time.h>
#ifdef NTP_TIME_ZONE_ENABLE
#include "Timing.h"
#endif

#define TRIGGER_SUCCESS     1
#define TRIGGER_FAILED      0

static T_SysPara   s_tSysPara;
static BYTE   s_ucQTPTestType = 0;  ///测试子阶段
static BOOLEAN s_bQtpSleepStatus = 0;
static BOOLEAN s_bQtpBalaCircCheckFlag = False;
Static BYTE s_bChargeProtectFlag = 0;
Static BOOLEAN s_bHeaterTimeFlag = False;
Static FLOAT s_fHeaterCurrent = 0.0; // 保存加热膜电流
static rt_timer_t s_ShutDownTimer = RT_NULL;
static rt_timer_t s_Heattimer = RT_NULL;
static rt_timer_t s_GetCurrentTimer = RT_NULL;

/*********************  静态函数原型定义  **********************/
Static void DealQTPCommand(BYTE ucPort);
Static void QtptestShutDown(void *para);

/*********************  通用命令原型定义  **********************/
static void SendBmsAnalogAndSwitchData(BYTE ucPort);
static void SendBmsAlarms(BYTE ucPort);
static void SendBMSCtrlForQTP(BYTE ucPort);
static void DelHisDatas(BYTE ucPort);
static void SetQuitQTPTest(BYTE ucPort);
static void GetIOStatus(BYTE ucPort);
static void SetQTPTestPeriod(BYTE ucPort);
static void GetQTPTestPeriod(BYTE ucPort);
static void GetIOStatus(BYTE ucPort);
static void GetBmsFactInfos(BYTE ucPort);
static void GetBduFactInfos(BYTE ucPort);
static void SetCellFactInfo(BYTE ucPort);
static void GetCellFactInfo(BYTE ucPort);
static void SetBattInfos(BYTE ucPort);
#ifndef SHIELD_BALANCE_CIRCUIT_OPENALM
static void StartQtpBalanceCircCheck(BYTE ucPort);
static void GetQtpBalanceCircFaultInfos(BYTE ucPort);
#endif

Static void SetSoftwareInfo(BYTE ucPort);
Static void GetSoftwareInfo(BYTE ucPort);

static void StartAndStopHeaterQtp(BYTE ucPort); //启动和停止加热
static void SetFactoryAcceptanceTestQtp( BYTE ucPort );//设置测试记录
static void GetFactoryAcceptanceTestQtp( BYTE ucPort );//获取测试记录

static void GetFaultInfo(BYTE ucPort);      //获取故障诊断状态
static void GetHeaterCurrent(BYTE ucPort);  // 获取加热膜电流
static void SendHeaterCurrent(BYTE ucPort); // 发送加热膜电流
Static void GetCurrent(UNUSED void *parameter);// 保存加热膜电流
static void SendAlarmBit(BYTE arr[],BYTE StartIndex, BYTE EndIndex, BYTE *Alarm);
#ifndef DEVICE_USING_R321
static void SetBMSPara( BYTE ucPort );  // 设置电池参数数据 // TODO: 宏分割
static void GetBMSPara(BYTE ucPort);  // 获取电池参数数据
#endif
static void RecvSysParaById(BYTE ucPort);
static void SendSysParaById(BYTE ucPort);
static void RefreshDefaultPar(BYTE ucPort);
static void GetProductVerInfo(BYTE ucPort);
Static void StopHeaterCurrent(BYTE ucPort);
static void GetTotalDischargeInfo(BYTE ucPort);     //获取累计放电信息
static void ClearTotalDischargeInfo(BYTE ucPort);   //清除累计放电信息
static BOOLEAN IsVariablelengthCommandQtp(void);
#ifdef INTELLIGENT_PEAK_SHIFTING
static void GetPeakShiftParaQtp(BYTE ucPort);   //获取智能错峰参数
static void SetPeakShiftParaQtp(BYTE ucPort);   //设置智能错峰参数
#endif
Static void SetTimeQTP(BYTE ucPort);
Static void GetTimeQTP(BYTE ucPort);

Static void SetSpecialParaQtp(BYTE ucPort);
Static void GetSpecialParaQtp(BYTE ucPort);

/********************************命令码处理START**************************************/
//CID1处理
const T_CmdFuncStruct s_atCID1AllFuncTableForQtp[] =
{
    {CID1_QTP, DealQTPCommand, 0},
    {0x00, 0x0000, 0x00},
};
//zte锂离子电池QTP自动化测试协议
const T_CmdFuncStruct s_atCID2QTPFuncTable[] =
{
        {GET_ANALOG_AND_SWITCH_INFO, SendBmsAnalogAndSwitchData, 0},
        {GET_BMS_ALM_INFO, SendBmsAlarms, 0},
        {GET_TIME_INFO, GetTimeQTP, 0},
        {SET_TIME_INFO, SetTimeQTP, 14},
        {SET_SERIAL_NUMBER_INFO, SetCsuId, 30},
        {GET_SERIAL_NUMBER_INFO, GetCsuId, 0},
        {GET_CELL_FACTORY_INFO, GetCellFactInfo, 0},
        {SET_CELL_FACTORY_INFO, SetCellFactInfo, 0},
        {GET_REMOTE_COMMAND_INFO, SendBMSCtrlForQTP, 6},
        {GET_IO_STATE_INFO, GetIOStatus, 0},
        {ClEAR_HISTORY_INFO, DelHisDatas, 0},
        {SET_TEST_SUBPHASE_CTR_INFO, SetQTPTestPeriod, 2},
        {GET_TEST_SUBPHASE_CTR_INFO, GetQTPTestPeriod, 0},
        {SET_QUIT_TEST_MODE_INFO, SetQuitQTPTest, 0},
        {GET_BMS_FACTORY_INFO, GetBmsFactInfos ,0},
        {GET_BDU_FACTORY_INFO, GetBduFactInfos, 0},
        {SET_BATT_INFO, SetBattInfos, 4},
#ifndef SHIELD_BALANCE_CIRCUIT_OPENALM
        {START_BALA_CIRC_CHECK_QTP, StartQtpBalanceCircCheck, 0},  //启动均衡电路故障检测
        {GET_BALA_CIRC_FAULT_INFO, GetQtpBalanceCircFaultInfos, 0},  //获取均衡电路故障检测状态
#endif
        {START_AND_STOP_HEATER_HAET_QTP, StartAndStopHeaterQtp, 2},  //启动和停止加热器加热
        {GET_FAULT_INFO, GetFaultInfo, 0}, //获取故障诊断状态
        {GET_HEATER_CURRENT, GetHeaterCurrent, 0}, // 启动加热膜电流检测
        {SEND_HEATER_CURRENT, SendHeaterCurrent, 0}, // 获取加热膜电流
        {SET_FAT_ID_AT_QTP, SetFactoryAcceptanceTestQtp, 18},
        {GET_FAT_ID_AT_QTP, GetFactoryAcceptanceTestQtp, 0},
#ifndef DEVICE_USING_R321
        {SET_BMS_PARA, SetBMSPara, 6},
        {GET_BMS_PARA, GetBMSPara, 0},
#endif
        {SET_SYS_PARA_BY_ID, RecvSysParaById, 0},
        {GET_SYS_PARA_BY_ID, SendSysParaById, 4},
        {RESTORE_DEFAULT_PARA_QTP, RefreshDefaultPar, 0},
        {GET_PRODUCTION_VER_INFO, GetProductVerInfo, 0},
        {SET_BMS_INFO_QTP, SetBmsInfo, 0},
        {GET_BMS_INFO_QTP, GetBmsInfo, 0},
        {STOP_HEATER_CURRENT, StopHeaterCurrent, 0}, // 停止加热膜电流检测
        {GET_TOTAL_DISCHARG_INFO, GetTotalDischargeInfo, 0}, //获取累计放电信息
        {CLEAR_TOTAL_DISCHARG_INFO, ClearTotalDischargeInfo, 0}, //清除累计放电信息
        {SET_SOFTWARE_CUSTOMED_INFO, SetSoftwareInfo, 0},
        {GET_SOFTWARE_CUSTOMED_INFO, GetSoftwareInfo, 0},
        {GET_HARDWARE_INFORMATION, GetHardwareInfo, 0},
        {SET_HARDWARE_INFORMATION, SetHardwareInfo, 40},
#ifdef INTELLIGENT_PEAK_SHIFTING
        {GET_PEAK_SHIFT_PARA_QTP, GetPeakShiftParaQtp, 0}, //获取智能错峰参数
        {SET_PEAK_SHIFT_PARA_QTP, SetPeakShiftParaQtp, 4}, //设置智能错峰参数
#endif

        {GET_SPECIAL_PARA_QTP, GetSpecialParaQtp, 0}, //获取特殊参数
        {SET_SPECIAL_PARA_QTP, SetSpecialParaQtp, 4}, //设置特殊参数

        {0x00, 0x0000, 0x00},
};

const T_CmdFuncStruct* s_auQtpProtocl[] =

{s_atCID2QTPFuncTable};



/****************************************************************************
* 函数名称：
* 输入参数：
* 返 回 值：
* 功能描述：如果版本号为偶数，版本号加1，否则不变
* 作    者  ：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要
yang_an,整理协议修改，20110511
***************************************************************************/
static const T_CmdFuncStruct* GetCmdFnucStruct(BYTE segment)
{
    if (segment < sizeof(s_auQtpProtocl)/sizeof(s_auQtpProtocl[0]))
    {
        return s_auQtpProtocl[segment];
    }
    return NULL;
}

void DealQtptestCommand(BYTE ucComType)
{
    WORD i = 0;

    const T_CmdFuncStruct *s_atCID2AllFuncTable = GetCmdFnucStruct(CID2QTP);

    /* 清空响应缓冲区 */
    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0x00, sizeof(s_tProtocol.aucSendBuf));
    /* 将响应包的LENID预置为0 */
    s_tProtocol.wSendLenid  = 0;
    // if (GET_PROTOCOL_VER == s_tProtocol.ucCID2)
    // {
    //     return;
    // }

    i = 0;
    while (s_atCID2AllFuncTable != NULL && s_atCID2AllFuncTable[i].ucCmdCode)
    {
        if ( s_atCID2AllFuncTable[i].ucCmdCode == s_tProtocol.ucCID2 )
        {
            if (s_atCID2AllFuncTable[i].ucDataLen != s_tProtocol.wRecLenid )
            {
                if (IsVariablelengthCommandQtp())
                {

                }
                else
                {
                    s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
                    return;
                }
            }
            (*s_atCID2AllFuncTable[i].func)(ucComType);
            DealDeviceUnlock(AUTO_UNLOCK);
            return;
        }
        i++;
    }

    i = 0;
    while (s_atCID1AllFuncTableForQtp[i].ucCmdCode)
    {
        if ( s_atCID1AllFuncTableForQtp[i].ucCmdCode == s_tProtocol.ucCID1 )
        {
            (*s_atCID1AllFuncTableForQtp[i].func)(ucComType);
            return;
        }
        i++;
    }

    // CID2错，返回RTN_WRONG_CID2
    s_tProtocol.ucRTN   = RTN_WRONG_CID2;

    return;
}


static BOOLEAN IsVariablelengthCommandQtp(void)
{
    if((s_tProtocol.ucCID2 == SET_SERIAL_NUMBER_INFO)  //新增QTP
            ||(s_tProtocol.ucCID2 == SET_CELL_FACTORY_INFO) //新增QTP
            ||(s_tProtocol.ucCID2 == SET_TEST_SUBPHASE_CTR_INFO) //新增QTP
            ||(s_tProtocol.ucCID2 == SET_QUIT_TEST_MODE_INFO) //新增QTP
            ||(s_tProtocol.ucCID2 == SET_SYS_PARA_BY_ID)
            ||(s_tProtocol.ucCID2 == SET_BMS_INFO_QTP)
            ||(s_tProtocol.ucCID2 == SET_SOFTWARE_CUSTOMED_INFO)
            ||(s_tProtocol.ucCID2 == GET_SOFTWARE_CUSTOMED_INFO))
    {
        return True;
    }
    return False;
}


Static void DealQTPCommand(BYTE ucPort )
{
    BYTE i = 0;
    const T_CmdFuncStruct* s_atCID2QTPFuncTable = GetCmdFnucStruct(CID2QTP);
    while (s_atCID2QTPFuncTable != NULL && s_atCID2QTPFuncTable[i].ucCmdCode)
    {
        if ( s_atCID2QTPFuncTable[i].ucCmdCode == s_tProtocol.ucCID2 )
        {
            if (s_atCID2QTPFuncTable[i].ucDataLen != s_tProtocol.wRecLenid)
            {
                               if(s_tProtocol.ucCID2 == SET_CELL_FACTORY_INFO
                                  ||(s_tProtocol.ucCID2 == SET_TEST_SUBPHASE_CTR_INFO)
                                  ||(s_tProtocol.ucCID2 == SET_QUIT_TEST_MODE_INFO)
                                  ||(s_tProtocol.ucCID2 == SET_SYS_PARA_BY_ID))
                                 {
                 }
                                 else
                                 {
                    s_tProtocol.ucRTN   = RTN_WRONG_COMMAND;
                    return;
                                 }
            }
            (*s_atCID2QTPFuncTable[i].func)(ucPort);
            return;
        }
        i++;
    }
    s_tProtocol.ucRTN   = RTN_WRONG_CID2;       /* CID2无效 */

    return;
}

static BYTE ByteValueRightOffset(BYTE Temp, BYTE offsetValue, BYTE pos)
{
    return (Temp & offsetValue) << pos;
}

static BYTE  Bit2Byte(BYTE* value)
{
    BYTE temp = 0;
    for(INT32 i = 0; i < 8; i++)
    {
        temp |= *(value + i);
    }
    return temp;
}

void DealQtptestRtnAbnormal(void)
{
    if (RTN_CORRECT != s_tProtocol.ucRTN)
    {
        s_tProtocol.ucRTN   = NO_RETURN;
    }

    return;
}

/****************************************************************************
* 函数名称：QtptestShutDown()
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：qtptest模式下关机
* 作    者：
* 设计日期：2023-02-6
* 修改记录：
* 日    期              版      本              修改人          修改摘要
***************************************************************************/
Static void QtptestShutDown(void *para)
{
    BOOLEAN bOnOff = GPIO_OFF;
#ifndef KW_CHECK
    rt_pin_write( SW_B1_Pin, bOnOff);
#endif
}

void SetQtpSleepStatus(BOOLEAN status)
{
    s_bQtpSleepStatus = status;
    return ;
}

BOOLEAN GetQtpSleepStatus()
{
    return (s_bQtpSleepStatus == 1 ? 1 : 0); //防止内存异常时导致s_bQtpSleepStatus值异常
}

static void SendBmsAnalogAndSwitchData(BYTE ucPort)
{
//    WORD wSendLen = 172;//(2+2+1+15*2+2*3+1+2*16+2*5+2)*2 = 86*2 = 172
    BYTE *p = NULL;
    BYTE i = 0;
    BYTE arr[8] = {0};
    BYTE value = 0;
    T_BCMDataStruct tBCMData;
    T_BattResult tBattout;
    T_HardwareParaStruct tHardwarePara;
    T_AlarmCheckStruct tAlarmCheck;
    #ifdef MODE_4G_REAL_DATA
    T_Mg21Status tMg21Data;
    #endif
    rt_memset(&tBCMData, 0, sizeof(T_BCMDataStruct));
    rt_memset(&tBattout, 0, sizeof(T_BattResult));
    rt_memset(&tHardwarePara, 0, sizeof(T_HardwareParaStruct));
    rt_memset(&tAlarmCheck, 0, sizeof(T_AlarmCheckStruct));
    #ifdef MODE_4G_REAL_DATA
    rt_memset_s(&tMg21Data,sizeof(T_Mg21Status) ,0, sizeof(T_Mg21Status));
    #endif
    GetRealData( &tBCMData );
    GetBattResult(&tBattout);
    GetHardwarePara(&tHardwarePara);
    readAlarmCheckFile(&tAlarmCheck);
    #ifdef MODE_4G_REAL_DATA
    getMg21Status(&tMg21Data);
    #endif

    p  = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    /* DATA_FLAG */
//    *p++   = GetDataFlag( BCM_ALARM, ucPort );
//    *p++ = BATT_NUM_BCM;//铁锂电池组数M

    *(SHORT*)p  = Host2Modbus((SHORT*)&tBCMData.wBattSOC);    //电池SOC
    p += 2;
    *(SHORT*)p  = FloatChangeToModbus(tBCMData.fBattVolt*100);    //电池电压
    p += 2;
    *p++  = tHardwarePara.ucCellVoltNum;//单体数量
    for (i = 0; i < tHardwarePara.ucCellVoltNum; i++)
    {
        *(SHORT*)p  = FloatChangeToModbus(tBCMData.afCellVolt[i]*1000);    //单体电压
        p += 2;
    }
    *(SHORT*)p  = FloatChangeToModbus(tBCMData.fEnvTemp*10);    //环境温度
    p += 2;
    *(SHORT*)p  = FloatChangeToModbus(tBCMData.maxVoltDiff*100);    //单体最大压差
    p += 2;
    *(SHORT*)p = FloatChangeToModbus(tBCMData.maxTempDiff*100);     //单体最大温差
    p += 2;
    *p++  = tHardwarePara.ucCellTempNum;//单体温度数量
    for (i = 0; i < tHardwarePara.ucCellTempNum; i++)
    {
        *(SHORT*)p  = FloatChangeToModbus(tBCMData.afCellTemp[i]*10);    //单体温度
        p += 2;
    }
    *(SHORT*)p  = FloatChangeToModbus(tBCMData.fBattCurr*100);    //电池电流
    p += 2;
    *(SHORT*)p  = Host2Modbus((SHORT*)&tBCMData.wBattSOH);    //电池SOH
    p += 2;
    *(SHORT*)p  = FloatChangeToModbus(tBCMData.fExterVolt*100);    //外部电压
    p += 2;
    *(SHORT*)p  = FloatChangeToModbus(tBCMData.fBusCurr*100);    //BUS电流
    p += 2;

    arr[0] = ByteValueRightOffset(tBCMData.ucBattChargeEn, 1, 7);
    arr[1] = ByteValueRightOffset(tBCMData.ucBattDischEn, 1, 6);
    arr[2] = ByteValueRightOffset(tBCMData.ucChgProtSta, 1, 5);
    arr[3] = ByteValueRightOffset(tBCMData.ucDischgProtSta, 1, 4);
    arr[4] = ByteValueRightOffset(tBCMData.ucCellEquSta, 1, 3);
    arr[5] = ByteValueRightOffset(tBCMData.ucBattPackSta, 3, 1);
    for(i = 0; i < 6; i++)
    {
        value |= arr[i];
    }
    *p++ = value;
    arr[0] = ByteValueRightOffset(tBCMData.ucBduStatus, 15, 4);
    arr[1] = ByteValueRightOffset(tBattout.ucMasterStatus, 1, 3);
    arr[2] = ByteValueRightOffset(tBCMData.aucOutputRly[0], 1, 2);
    arr[3] = ByteValueRightOffset(tBCMData.aucOutputRly[1], 1, 1);
    value = 0;
    for(i = 0; i < 4; i++)
    {
        value |= arr[i];
    }
    *p++ = value;
    #ifdef DEVICE_USING_R321
    *p++ = 23;  ///用户自定义的23个字节
    #else
    *p++ = 16;  ///用户自定义的16个字节
    #endif
    *(SHORT*)p  = FloatChangeToModbus(tBCMData.fCellVoltMax*1000);    //单体电压最大值
    p += 2;
    *(SHORT*)p  = FloatChangeToModbus(tBCMData.fCellVoltMin*1000);    //单体电压最小值
    p += 2;
    *(SHORT*)p  = FloatChangeToModbus(tBCMData.fCellTempMax*10);    //单体温度最大值
    p += 2;
    *(SHORT*)p  = FloatChangeToModbus(tBCMData.fCellTempMin*10);    //单体温度最小值
    p += 2;
    *(SHORT*)p  = FloatChangeToModbus(tBCMData.fBoardTemp*10);    //单板温度
    p += 2;
    *p++ = tHardwarePara.ucCellVoltNum;    // 单节电压检测数量
    *p++ = tHardwarePara.ucCellTempNum;    // 单体温度检测数量
    *(SHORT*)p  = FloatChangeToModbus(tBCMData.fConnTemp*10);    // 连接器温度
    p += 2;
    #ifdef DEVICE_USING_R321
    *(SHORT*)p  = FloatChangeToModbus(tBCMData.fBalanceResisTemp*10);    // 均衡电阻温度
    p += 2;
    #endif 
	*p++ = tBCMData.bHeatpadEnable; //加热器状态
    *p++ = tAlarmCheck.bIfShortCutExist;   // 历史告警中是否存在短路告警
    #ifdef FIRE_CONTROL_ENABLED
    *p++ = GetFireControlStatus();	//消防信号状态
    *(SHORT*)p = FloatChangeToModbus(GetFireControlAdcVol() * 10);	//消防采样电压
    p += 2;
    *p++ = GetFireControlFaultStatus();	//消防故障状态
    #endif 
    #ifdef MODE_4G_REAL_DATA
    *p++ = tMg21Data.ucSNR; // GPS载噪比
    #endif
    /* LENID */
    s_tProtocol.wSendLenid  = (WORD) ((p-s_tProtocol.aucSendBuf)*2);
//    s_tProtocol.wSendLenid = wSendLen;//为解决KW问题
    return;
}

static void SendBmsAlarms(BYTE ucPort)
{
    BYTE *p = NULL;
    BYTE i;
    BYTE arr[8] = {0};
    T_BCMAlarmStruct    tBCMAlm;
    rt_memset(&tBCMAlm, 0, sizeof(T_BCMAlarmStruct));
    GetRealAlarm(BCM_ALARM, (BYTE *)&tBCMAlm);

    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    /* DATA_FLAG */
//    ClearDataFlag( BCM_ALARM, 0xEF, ucPort );    /* 开关量状态变化标志清0 */
//    *p++ = GetDataFlag( BCM_ALARM, ucPort );
//    *p++ = BATT_NUM_BCM;//铁锂电池组数M
    arr[0] = ByteValueRightOffset(tBCMAlm.ucBattOverVoltAlm, 1, 7);
    arr[1] = ByteValueRightOffset(tBCMAlm.ucChgCurrHighAlm, 1, 6);
    arr[2] = 0;//ByteValueRightOffset(tBCMAlm.ucBattUnderVoltAlm, 1, 5);
    arr[3] = ByteValueRightOffset(tBCMAlm.ucDischgCurrHighAlm, 1, 4);
    arr[4] = ByteValueRightOffset(tBCMAlm.ucBattReverse, 1, 3);
    arr[5] = ByteValueRightOffset(tBCMAlm.ucEnvTempHighAlm, 1, 2);
    arr[6] = ByteValueRightOffset(tBCMAlm.ucEnvTempLowAlm, 1, 1);
    *p++ = Bit2Byte(arr);

    arr[0] = 0;
    arr[1] = 0;//ByteValueRightOffset(tBCMAlm.ucBattSOCLowAlm, 1, 6);
    arr[2] = 0;
    arr[3] = 0;
    arr[4] = 0;
    arr[5] = 0;
#ifdef DEVICE_USING_D121
    arr[6] = ByteValueRightOffset(tBCMAlm.ucEnvTempSensorInvalidAlm, 1, 1);//环境温度传感器无效告警
#else
    arr[6] = ByteValueRightOffset(0, 1, 1);   //环境温度传感器无效告警，R321无此告警
#endif
    arr[7] = ByteValueRightOffset(tBCMAlm.aucCellOverVoltAlm[0], 1, 0); //单体1过压告警
    *p++ = Bit2Byte(arr);

    SendAlarmBit(arr, 0, 8, &(tBCMAlm.aucCellOverVoltAlm[1]));//单体2-9过压告警
    *p++ = Bit2Byte(arr);

    SendAlarmBit(arr, 0, CELL_VOL_NUM - 9, &(tBCMAlm.aucCellOverVoltAlm[9]));//单体10-15过压告警

    arr[6] = 0;//ByteValueRightOffset(tBCMAlm.aucCellUnderVoltAlm[0], 1, 1);   //单体1欠压告警
    arr[7] = 0;//ByteValueRightOffset(tBCMAlm.aucCellUnderVoltAlm[1], 1, 0);
    *p++ = Bit2Byte(arr);
for(i = 0; i < 8; i++)
    {
        arr[i] = 0;    //单体3-10欠压告警
    }
    // SendAlarmBit(arr, 0, 8, &(tBCMAlm.aucCellUnderVoltAlm[2]));//单体3-10过压告警

    *p++ = Bit2Byte(arr);

    // SendAlarmBit(arr, 0, CELL_VOL_NUM - 10, &(tBCMAlm.aucCellUnderVoltAlm[10]));//单体10-15过压告警
    for(i = 0; i < 8; i++)
    {
        arr[i] = 0;  //单体10-15欠压告警，单体1-3失效告警
    }
    *p++ = Bit2Byte(arr);

    for(i = 0; i < 8; i++)
    {
        arr[i] = 0;    //单体4-11失效告警
    }
    *p++ = Bit2Byte(arr);

    for(i = 0; i < 4; i++)
    {
        arr[i] = 0;    //单体12-15失效告警
    }
#ifdef DEVICE_USING_D121
    arr[4] = ByteValueRightOffset(0, 1, 3);        //电压采样故障
#else
	arr[4] = ByteValueRightOffset(tBCMAlm.ucBattVoltSampleAlm, 1, 3);   //电压采样故障
#endif
    arr[5] = ByteValueRightOffset(tBCMAlm.ucLoopFault, 1, 2);   //回路异常
    arr[6] = ByteValueRightOffset(tBCMAlm.ucBDUBattChgVoltLowPrt, 1, 1);   //BDU电池充电欠压保护
    arr[7] = ByteValueRightOffset(tBCMAlm.ucBDUBattLockAlm, 1, 0);   //BDU电池闭锁告警
    *p++ = Bit2Byte(arr);

    arr[0] = ByteValueRightOffset(tBCMAlm.ucEnvTempHighPrt, 1, 7); //环境温度高保护
    arr[1] = ByteValueRightOffset(tBCMAlm.ucEnvTempLowPrt, 1, 6); //环境温度低保护
    arr[2] = ByteValueRightOffset(tBCMAlm.ucBoardTempHighAlm, 1, 5); //单板过温告警
#ifdef SHIELD_BALANCE_CIRCUIT_OPENALM
    arr[3] = ByteValueRightOffset(0, 1, 4); //均衡电路故障告警(删除该参数，默认上送0)
#else
    arr[3] = ByteValueRightOffset(tBCMAlm.ucEqualCircuitFaultAlm, 1, 4); //均衡电路故障告警
#endif
#ifdef DEVICE_USING_D121
    arr[4] = ByteValueRightOffset(tBCMAlm.ucPowerOffTimePrtAlm, 1, 3);//停电时间保护告警
#else
    arr[4] = ByteValueRightOffset(0, 1, 3);   //停电时间保护告警，R321无此告警
#endif
    arr[5] = ByteValueRightOffset(tBCMAlm.ucHeaterFilmFailure, 1, 2);//加热膜失效
    arr[6] = ByteValueRightOffset(tBCMAlm.ucBattOverVoltPrt, 1, 1);  //电池组过压保护
    arr[7] = ByteValueRightOffset(tBCMAlm.ucChgCurrHighPrt, 1, 0); //充电过流保护
    *p++ = Bit2Byte(arr);

    arr[0] = ByteValueRightOffset(tBCMAlm.ucBattUnderVoltPrt, 1, 7); //电池组欠压保护
    arr[1] = ByteValueRightOffset(tBCMAlm.ucDischgCurrHighPrt, 1, 6); //放电过流保护
    arr[2] = ByteValueRightOffset(tBCMAlm.ucCellPoorConsisAlm, 1, 5); //单体一致性差告警
    arr[3] = ByteValueRightOffset(tBCMAlm.ucBoardTempHighPrt, 1, 4); //单板过温保护
    arr[4] = ByteValueRightOffset(tBCMAlm.ucBattSOCLowPrt, 1, 3); //电池SOC低保护
    arr[5] = ByteValueRightOffset(tBCMAlm.ucBattSOHAlm, 1, 2); //电池SOH告警
    arr[6] = ByteValueRightOffset(tBCMAlm.ucBattLoseAlm, 1, 1);  //电池丢失告警
    arr[7] = ByteValueRightOffset(tBCMAlm.ucBattShortCut, 1, 0); //电池短路
    *p++ = Bit2Byte(arr);

    arr[0] = ByteValueRightOffset(tBCMAlm.ucCellVoltSampleFault, 1, 7); //单体电压采样异常
    arr[1] = ByteValueRightOffset(tBCMAlm.ucChgLoopInvalid, 1, 6); //充电回路失效
    arr[2] = ByteValueRightOffset(tBCMAlm.ucDischgLoopInvalid, 1, 5); //放电回路失效
    arr[3] = ByteValueRightOffset(tBCMAlm.ucCurrLimLoopInvalid, 1, 4); //限流回路失效
#ifdef DEVICE_USING_D121
    arr[4] = ByteValueRightOffset(tBCMAlm.ucFuseError, 1, 3); //熔丝损坏
#else
    arr[4] = ByteValueRightOffset(0, 1, 3);   //熔丝损坏，R321无此告警
#endif

    SendAlarmBit(arr, 5, 8, &(tBCMAlm.aucCellUnderVoltPrt[0]));//单体1-3欠压保护
    *p++ = Bit2Byte(arr);

    SendAlarmBit(arr, 0, 8, &(tBCMAlm.aucCellUnderVoltPrt[3]));//单体4-11欠压保护

    *p++ = Bit2Byte(arr);

    SendAlarmBit(arr, 0, 4, &(tBCMAlm.aucCellUnderVoltPrt[11]));//单体12-15欠压保护

    SendAlarmBit(arr, 4, 8, &(tBCMAlm.aucCellOverVoltPrt[0]));//单体1-4过压保护
    *p++ = Bit2Byte(arr);

    SendAlarmBit(arr, 0, 8, &(tBCMAlm.aucCellOverVoltPrt[4]));//单体5-12过压保护
    *p++ = Bit2Byte(arr);

    SendAlarmBit(arr, 0, 3, &(tBCMAlm.aucCellOverVoltPrt[12]));//单体13-15过压保护

    SendAlarmBit(arr, 3, 8, &(tBCMAlm.aucCellDamagePrt[0]));//单体1-5损坏保护
    *p++ = Bit2Byte(arr);

    SendAlarmBit(arr, 0, 8, &(tBCMAlm.aucCellDamagePrt[5]));//单体6-13损坏保护
    *p++ = Bit2Byte(arr);

    SendAlarmBit(arr, 0, 2, &(tBCMAlm.aucCellDamagePrt[13]));//单体14-15损坏保护
    arr[2] = ByteValueRightOffset(tBCMAlm.ucBDUConnTempHighPrt, 1, 5);//连接器温度高保护

    SendAlarmBit(arr, 3, 8, &(tBCMAlm.aucChgTempLowAlm[0]));//单体1-5充电低温告警
    *p++ = Bit2Byte(arr);

    SendAlarmBit(arr, 0, 8, &(tBCMAlm.aucChgTempLowAlm[5]));//单体6-13充电低温告警
    *p++ = Bit2Byte(arr);

    SendAlarmBit(arr, 0, 3, &(tBCMAlm.aucChgTempLowAlm[13]));//单体14-16充电低温告警

    SendAlarmBit(arr, 3, 8, &(tBCMAlm.aucChgTempLowPrt[0]));//单体1-5充电低温保护
    *p++ = Bit2Byte(arr);

    SendAlarmBit(arr, 0, 8, &(tBCMAlm.aucChgTempLowPrt[5]));//单体6-13充电低温保护
    *p++ = Bit2Byte(arr);

    SendAlarmBit(arr, 0, 3, &(tBCMAlm.aucChgTempLowPrt[13]));//单体14-16充电低温保护

    SendAlarmBit(arr, 3, 8, &(tBCMAlm.aucChgTempHighPrt[0]));//单体1-5充电高温保护
    *p++ = Bit2Byte(arr);

    SendAlarmBit(arr, 0, 8, &(tBCMAlm.aucChgTempHighPrt[5]));//单体6-13充电高温保护
    *p++ = Bit2Byte(arr);

    SendAlarmBit(arr, 0, 3, &(tBCMAlm.aucChgTempHighPrt[13]));//单体14-16充电高温保护

    SendAlarmBit(arr, 3, 8, &(tBCMAlm.aucDischgTempLowAlm[0]));//单体1-5放电低温告警
    *p++ = Bit2Byte(arr);

    SendAlarmBit(arr, 0, 8, &(tBCMAlm.aucDischgTempLowAlm[5]));//单体6-13放电低温告警
    *p++ = Bit2Byte(arr);

    SendAlarmBit(arr, 0, 3, &(tBCMAlm.aucDischgTempLowAlm[13]));//单体14-16放电低温告警

    SendAlarmBit(arr, 3, 8, &(tBCMAlm.aucDischgTempHighAlm[0]));//单体1-5放电高温告警
    *p++ = Bit2Byte(arr);

    SendAlarmBit(arr, 0, 8, &(tBCMAlm.aucDischgTempHighAlm[5]));//单体6-13放电高温告警
    *p++ = Bit2Byte(arr);

    SendAlarmBit(arr, 0, 3, &(tBCMAlm.aucDischgTempHighAlm[13]));//单体14-16放电高温告警

    SendAlarmBit(arr, 3, 8, &(tBCMAlm.aucDischgTempLowPrt[0]));//单体1-5放电低温保护
    *p++ = Bit2Byte(arr);

    SendAlarmBit(arr, 0, 8, &(tBCMAlm.aucDischgTempLowPrt[5]));//单体6-13放电低温保护
    *p++ = Bit2Byte(arr);

    SendAlarmBit(arr, 0, 3, &(tBCMAlm.aucDischgTempLowPrt[13]));//单体14-16放电低温保护

    SendAlarmBit(arr, 3, 8, &(tBCMAlm.aucDischgTempHighPrt[0]));//单体1-5放电高温保护
    *p++ = Bit2Byte(arr);

    SendAlarmBit(arr, 0, 8, &(tBCMAlm.aucDischgTempHighPrt[5]));//单体6-13放电高温保护
    *p++ = Bit2Byte(arr);

    SendAlarmBit(arr, 0, 3, &(tBCMAlm.aucDischgTempHighPrt[13]));//单体14-16放电高温保护
//    arr[3] = ByteValueRightOffset(tBCMAlm.ucInsideTempHighPrt, 1, 4);  //机内过温保护
    arr[3] = 0;
    arr[4] = ByteValueRightOffset(tBCMAlm.ucBDUBattVoltLowPrt, 1, 3);  //BDU电池欠压保护
//    arr[5] = ByteValueRightOffset(tBCMAlm.ucCellTempAbnormal, 1, 2);  //单体温度异常
    arr[5] = 0;
//    arr[6] = ByteValueRightOffset(tBCMAlm.ucAddressClash, 1, 1);  //地址冲突
    arr[6] = 0;

#ifdef DEVICE_USING_D121
    arr[7] = ByteValueRightOffset(0, 1, 0);   //振动告警
#else
    arr[7] = ByteValueRightOffset(tBCMAlm.ucShakeAlarm, 1, 0);      //振动告警
#endif
    *p++ = Bit2Byte(arr);

    arr[0] = ByteValueRightOffset(tBCMAlm.ucBduEepromAlm, 1, 7);  //BDU EEPROM故障
    arr[1] = ByteValueRightOffset(tBCMAlm.ucBDUBusVoltLowPrt, 1, 6); //BDU母排欠压保护
    arr[2] = ByteValueRightOffset(tBCMAlm.ucBDUCommFail, 1, 5); //BDU通讯断
    arr[3] = ByteValueRightOffset(tBCMAlm.ucCellPoorConsisPrt, 1, 4); //单体一致性保护
    arr[4] = ByteValueRightOffset(tBCMAlm.ucBattSOHPrt, 1, 3);//电池SOH保护
    arr[5] = ByteValueRightOffset(tBCMAlm.ucBDUBusVoltHighPrt, 1, 2);//BDU母排过压保护

    SendAlarmBit(arr, 6, 8, &(tBCMAlm.aucChgTempHighAlm[0]));//单体1-2充电高温告警
    *p++ = Bit2Byte(arr);

    SendAlarmBit(arr, 0, 8, &(tBCMAlm.aucChgTempHighAlm[2]));//单体3-10充电高温告警
    *p++ = Bit2Byte(arr);

    SendAlarmBit(arr, 0, 6, &(tBCMAlm.aucChgTempHighAlm[10]));//单体11-16充电高温告警

    SendAlarmBit(arr, 6, 8, &(tBCMAlm.ucCellTempSensorInvalidAlm[0]));//单体1-2温度传感器失效
    *p++ = Bit2Byte(arr);

    SendAlarmBit(arr, 0, 8, &(tBCMAlm.ucCellTempSensorInvalidAlm[2]));//单体3-10温度传感器失效
    *p++ = Bit2Byte(arr);

    SendAlarmBit(arr, 0, 6, &(tBCMAlm.ucCellTempSensorInvalidAlm[10]));//单体11-16温度传感器失效
    arr[6] = ByteValueRightOffset(tBCMAlm.ucMainRelayFail, 1, 1);//主继电器失效
	arr[7] = ByteValueRightOffset(tBCMAlm.ucDCDCErr, 1, 0);//DCDC故障
    *p++ = Bit2Byte(arr);

    arr[0] = ByteValueRightOffset(tBCMAlm.ucSampleErr, 1, 7);//采集异常
    arr[1] = ByteValueRightOffset(tBCMAlm.ucAuxiSourceErr, 1, 6);//辅助源故障
#ifdef ACTIVATE_PORT_ENABLED
    arr[2] = ByteValueRightOffset(tBCMAlm.ucActivePortCurrError, 1, 5);//激活回路电流异常保护
#endif
    *p++ = Bit2Byte(arr);
    /* LENID */
    s_tProtocol.wSendLenid  = GetLength((p-s_tProtocol.aucSendBuf)*2);
    return;
}

static void SendAlarmBit(BYTE arr[],BYTE StartIndex, BYTE EndIndex, BYTE *Alarm)
{
    BYTE i = 0;
    for(i = StartIndex; i < EndIndex; i++)
    {
        arr[i] = ByteValueRightOffset(*(Alarm + i - StartIndex), 1, 7-i);//单体11-16温度传感器失效
    }
}

static void SendBMSCtrlForQTP(BYTE ucPort)
{
    BYTE ledStatus = 0;         // LED灯控制
    BYTE bStatus = 0;
    BYTE bBuzz = 0;
    BYTE i = 0;

    T_CtrlOutStruct tCtrlOut;
    rt_memset(&tCtrlOut, 0, sizeof(T_CtrlOutStruct));

    tCtrlOut.bCtrlFlag = s_tProtocol.aucRecBuf[7];
    bBuzz = s_tProtocol.aucRecBuf[8];
    ledStatus = s_tProtocol.aucRecBuf[9];

    if( tCtrlOut.bCtrlFlag == 0x01)
    {
        tCtrlOut.bBuzz = bBuzz;
        for(i = 0; i < 6; i++)
        {
            bStatus = ledStatus % 2;
            tCtrlOut.bLed[i] = bStatus;
            ledStatus = ledStatus / 2;
            SetLedStatus(i, bStatus);
        }
    }
    SetCtrlOut(&tCtrlOut);
    ClearLedByAddr();
    return;
}

static void DelHisDatas(BYTE ucPort)
{
    BYTE *p = NULL;
    BYTE *ptr = NULL;
    p = s_tProtocol.aucSendBuf;
    ptr = s_tProtocol.aucSendBuf;
    LONG hisAlarmSize = 0;
    LONG hisActionSize = 0;
    LONG hisDataSize = 0;
    LONG slDcrRecordSize = 0;
    LONG slDcrCntSize = 0;

#ifdef GPS_BEIDOU_SET
    T_GPSPositionStruct tGPSPos = {0};
    T_GPSDirectionStruct tGpsDirect = {0};
    // 保存经纬度坐标(清除)
    writeGPSLocation(&tGPSPos);
    // 保存方向信息(清除)
    writeGPSDirection(&tGpsDirect);
#endif

    DeleteHisAlarm();
    hisAlarmSize = readFile("/hisalarm", ptr, sizeof(T_HisAlarmSaveStruct));
    DelAllHisAction();
    hisActionSize = readFile("/hisaction", ptr, sizeof(T_ActionRecord));
    DelAllHisData();
    hisDataSize = readFile("/hisdata", ptr, sizeof(T_HisDataStruct));
    //删除直流内阻记录
    DelAllDcrRecord();
    slDcrRecordSize = readFile("/dcrRecord", ptr, sizeof(T_DcrRecordStruct));
    //删除直流内阻相关计数
    DelDcrCnt();
    slDcrCntSize = readFile("/DcrCnt", ptr, sizeof(T_DcrCntStruct));
    //清除SOH衰减信息和累计量
    SetBattSOH(BATT_SOH_FULL_VALUE);
    SetBattCycleTimes(BATT_CYCLE_TIMES_ZERO);
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

    if(hisAlarmSize == 0 && hisActionSize == 0 && hisDataSize == 0 && slDcrRecordSize == 0 && slDcrCntSize == 0)
    {
        *p++ = TRIGGER_SUCCESS; //触发成功
    }
    else
    {
        *p++ = TRIGGER_FAILED; //触发失败
    }

    s_tProtocol.wSendLenid = 2;
}

static void SetQuitQTPTest(BYTE ucPort)
{
    GetQtpTrigger();
    Set_Mode_Work(BUT_DEFENCE, True);           //退出QTP模式下恢复按键防盗查询
    SetQtptestFlag(False);
    SaveAction(GetActionId(CONTOL_QUIT_QTP),"Quit Qtp");
    GetQtpTestTriggerRepeat();
    ClearLedByAddr();
    ClearBlanceCircuitStatus();
    ClearBtnCount();     //清除按键次数，重新计数 0908 mwl
//    InitCtrlOut();
    DeleteTrigTimer();
#ifdef POWER_COMM_FAIL_ALARM
    SetPowerCommShieldFlag(True);
#endif
    s_tProtocol.wSendLenid  = 2;
    return;
}

static void GetIOStatus(BYTE ucPort)
{
    BYTE  *p = NULL;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    p  = s_tProtocol.aucSendBuf;
    *p++ = GetSPowerBtnCount();
    *p++ = GetSWSleepBtnCount();
    *p++ = getPortInput(Digit_THEFT_DET);
    s_tProtocol.wSendLenid = 6;
    return;
}

static void SetQTPTestPeriod(BYTE ucPort)
{
    BYTE ucCtrlCode  = 0;
    ucCtrlCode = s_tProtocol.aucRecBuf[7];

    if(ucCtrlCode <= 3)  ///ucCtrlCode >= 0
    {
        s_ucQTPTestType = ucCtrlCode;
    }
    else
    {
         s_tProtocol.ucRTN  = RTN_WRONG_COMMAND;    // 命令格式错
         return;
    }

    return;
}

static void GetQTPTestPeriod(BYTE ucPort)
{
    BYTE *p = NULL;

    p = s_tProtocol.aucSendBuf;
    *p++ = s_ucQTPTestType;

    s_tProtocol.wSendLenid  = GetLength((p-s_tProtocol.aucSendBuf)*2);
    return;
}

static void GetBmsFactInfos(BYTE ucPort)
{
    BYTE *p = NULL;
    CHAR chBmsId[15];

    rt_memset(&s_tSysPara, 0, sizeof(T_SysPara));
    GetSysPara(&s_tSysPara);

    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

    MemsetBuff(p, s_tSysPara.acBMSSysName, 20, 20, 0x00); //BMS系统名称
    p += 20;

    GetBmsSn((CHAR *)chBmsId);
    rt_memcpy(p, (BYTE *)chBmsId, sizeof(chBmsId));
    p += sizeof(chBmsId);

#ifdef SOFTBANK_VERSION_BUILD
    MemsetBuff(p, BMS_VER_SOFTBANK, (BYTE)rt_strnlen_s(BMS_VER_SOFTBANK, 15), 15, 0x00); // BMS软件版本
#else
    MemsetBuff(p, BMS_VER, (BYTE)rt_strnlen_s(BMS_VER, 15), 15, 0x00); // BMS软件版本
#endif
    p += 15;

    PutInt16ToBuff(p, SOFTWARE_RELEASE_YEAR);
    p += 2;
    *p++ = SOFTWARE_RELEASE_MONTH;
    *p++ = SOFTWARE_RELEASE_DATE; // BMS软件发布日期

    *p++ = 0; // 自定义字节数

    /* LENID */
    s_tProtocol.wSendLenid = (WORD)((p - s_tProtocol.aucSendBuf) * 2);
    return;
}

static void GetBduFactInfos(BYTE ucPort)
{
    T_DCFactory tBduFact;
    BYTE *p = NULL;
    WORD wYearOfVerDate = 0;

    if ( ucPort >= SCI_PORT_NUM )
    {
        return;
    }
    GetBduFact(&tBduFact);
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    p  = s_tProtocol.aucSendBuf;
    MemsetBuff(p, tBduFact.acSysName, 30, 32, 0x20);//SM软件名称
    p += 32;

    MemsetBuff(p, tBduFact.acSoftVer, 6, 32, 0x20);//SM软件版本
    p += 32;

    wYearOfVerDate = (tBduFact.acVerDate[0] << 8) | tBduFact.acVerDate[1];
    PutInt16ToBuff(p, wYearOfVerDate); // BDU版本日期-年
    p += 2;
    *p++ = tBduFact.acVerDate[2]; // BDU版本日期-月
    *p++ = tBduFact.acVerDate[3]; // BDU版本日期-日

    PutInt32ToBuff(p, tBduFact.ulSN);
    p += 4;

    *p++ = tBduFact.acHardwareVer; // 硬件版本号

    s_tProtocol.wSendLenid  = (WORD) ((p-s_tProtocol.aucSendBuf)*2);

    return;
}

static void SetCellFactInfo(BYTE ucPort) //设置电芯和PACK厂家信息
{
    T_BmsPACKManufactStruct tPackInfo;
    T_BmsPACKFactoryStruct tPackFactory= {0};
    SHORT wTemp;

    rt_memset((BYTE*)&tPackInfo,0x00,sizeof(T_BmsPACKManufactStruct));
    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];

    //190128先读取再设置
    readPackManufact(&tPackInfo);
    // 读取 PACK BAR CODE
    readBmsPackFacInfo(&tPackFactory);

    if (s_tProtocol.ucCommandType == 0x80) //设置电芯厂家名称
    {
        rt_memcpy((BYTE *)&(tPackInfo.acCellManufactruer[0]), (BYTE *)(&s_tProtocol.aucRecBuf[8]), sizeof(tPackInfo.acCellManufactruer));
        if (False == CheckPackOrCellInfo(tPackInfo.acCellManufactruer, sizeof(tPackInfo.acCellManufactruer)))
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
            return;
        }
    }
    else if (s_tProtocol.ucCommandType == 0x81)
    {
        wTemp = Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf+8));
        tPackInfo.tBattDate.wYear = (WORD)wTemp;
        tPackInfo.tBattDate.ucMonth = s_tProtocol.aucRecBuf[10];
        tPackInfo.tBattDate.ucDay = s_tProtocol.aucRecBuf[11];
        if(!CheckDateValid(&tPackInfo.tBattDate))
        {
            s_tProtocol.ucRTN   = RTN_INVALID_DATA;
            return;
        }
    }
    else if(s_tProtocol.ucCommandType == 0x82)
    {
        tPackInfo.wCellCycleTimes = (WORD)(Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf + 8)));
        if(!CheckCellCycleTimesValid(tPackInfo.wCellCycleTimes))
        {
            s_tProtocol.ucRTN   = RTN_INVALID_DATA;
            return;
        }
        SetCellCycleTimes(tPackInfo.wCellCycleTimes);
    }
    else if(s_tProtocol.ucCommandType == 0x83) //设置PACK厂家名称
    {
        rt_memcpy((BYTE *)&(tPackInfo.acPackInfo[0]), (BYTE *)(&s_tProtocol.aucRecBuf[8]), sizeof(tPackInfo.acPackInfo));
        if (False == CheckPackOrCellInfo(tPackInfo.acPackInfo, sizeof(tPackInfo.acPackInfo)))
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
            return;
        }
    }
    else if(0x84 == s_tProtocol.ucCommandType)
    {
        if (CheckDigit((BYTE*)(&s_tProtocol.aucRecBuf[8]), sizeof(tPackFactory.acPackBarCode[0])))
        {
            rt_memcpy((BYTE*)&(tPackFactory.acPackBarCode[0][0]), (BYTE*)(&s_tProtocol.aucRecBuf[8]), sizeof(tPackFactory.acPackBarCode[0]));
        }
        else
        {
            s_tProtocol.ucRTN   = RTN_INVALID_DATA;
            return;
        }
    }
#ifdef GET_NUMOFCELL_AND_TYPEOFBMS_ENABLED
    else if(0x85 == s_tProtocol.ucCommandType)
    {
        rt_memset_s(&s_tSysPara, sizeof(T_SysPara), 0, sizeof(T_SysPara));
        GetSysPara(&s_tSysPara);
        s_tSysPara.ucCellType = s_tProtocol.aucRecBuf[8];
        if(!(SetSysPara(&s_tSysPara, True, CHANGE_BY_QTPTEST)))
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
        }
        else
        {
            GetSysPara(&s_tSysPara);
            WriteBMSDefaultPara(&s_tSysPara);
        }
        return;
    }
#endif
    else
    {
        s_tProtocol.ucRTN  = RTN_WRONG_COMMAND;
        return;
    }

    tPackInfo.wCRC = CRC_Cal((BYTE*)&tPackInfo, (sizeof(tPackInfo)-2));
    writePackManufact(&tPackInfo);
    tPackFactory.wCRC = CRC_Cal((BYTE*)&tPackFactory, (sizeof(tPackFactory)-2));
    writeBmsPackFacInfo(&tPackFactory);
    return;
}

static void GetCellFactInfo(BYTE ucPort) //获取电芯和PACK厂家信息
{
    BYTE *p = NULL;
    T_BmsPACKManufactStruct tPackInfo;
    T_BmsPACKFactoryStruct tPackFactory= {0};

    rt_memset_s(&s_tSysPara, sizeof(T_SysPara), 0, sizeof(T_SysPara));
    GetSysPara(&s_tSysPara);
    rt_memset_s(&tPackInfo, sizeof(T_BmsPACKManufactStruct), 0, sizeof(T_BmsPACKManufactStruct));
    readPackManufact(&tPackInfo);
    readBmsPackFacInfo(&tPackFactory);  // 读取PACK BAR CODE

    p = s_tProtocol.aucSendBuf;
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

    MemsetBuff(p, (BYTE *)(&tPackInfo.acCellManufactruer[0]), 20, 20, 0x20);//电芯厂家名称
    p += 20;
    PutInt16ToBuff(p, (SHORT)tPackInfo.tBattDate.wYear);//电芯出厂日期
    p +=2;
    *p++ = tPackInfo.tBattDate.ucMonth;
    *p++ = tPackInfo.tBattDate.ucDay;

    PutInt16ToBuff(p, (SHORT)tPackInfo.wCellCycleTimes); // 电芯循环次数
    p += 2;
    MemsetBuff(p, (BYTE *)(&tPackInfo.acPackInfo[0]), 20, 20, 0x20);//PACK厂家名称
    p += 20;
#ifdef GET_NUMOFCELL_AND_TYPEOFBMS_ENABLED
    *p++ = 21;    //自定义字节数
    MemsetBuff(p, (BYTE *)(&tPackFactory.acPackBarCode[0][0]), 20, 20, 0x00);  //PACK BAR CODE,第一条电池包序列号
    p += 20;
    *p++ = s_tSysPara.ucCellType; //电芯类型
#else
    *p++ = 20;    //自定义字节数
    MemsetBuff(p, (BYTE *)(&tPackFactory.acPackBarCode[0][0]), 20, 20, 0x00);  //PACK BAR CODE,第一条电池包序列号
    p += 20;
#endif
    s_tProtocol.wSendLenid = (WORD)((p - s_tProtocol.aucSendBuf) * 2);
}

static void SetBattInfos(BYTE ucPort)
{
    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[8];

    switch (s_tProtocol.ucCommandType)
    {
    case BATT_SHUT_DOWN:
        rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
        s_tProtocol.wSendLenid = 0;
#ifndef KW_CHECK
        if (RT_NULL == s_ShutDownTimer)
        {
            s_ShutDownTimer = rt_timer_create("shutdown", QtptestShutDown, RT_NULL, ONE_SEC, RT_TIMER_FLAG_ONE_SHOT);
        }

        if (s_ShutDownTimer != RT_NULL)
        {
            SaveAction(GetActionId(CONTOL_SHUTDOWN),"QtptestShutdown");
            rt_timer_start(s_ShutDownTimer);
        }
#endif
        break;

    case BATT_SLEEP:
        if(!IsSleep())
        {
            SetQtpSleepStatus(True);
        }
        break;
    default:
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
        break;
    }
}

#ifndef SHIELD_BALANCE_CIRCUIT_OPENALM
static void StartQtpBalanceCircCheck(BYTE ucPort)
{
    ClearBlanceCircuitStatus();
    CheckEqualCircuitFault();
    SetQtpBalanceCircCheckFlag(TRUE);

    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    s_tProtocol.wSendLenid = 0;

    return;
}

static void GetQtpBalanceCircFaultInfos(BYTE ucPort)
{
    BYTE  *p = NULL;

    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    p  = s_tProtocol.aucSendBuf;
    *p++ = GetEqualCircuitFault();
    *p = 0;
    s_tProtocol.wSendLenid = 2;

    return;
}
#endif

BOOLEAN GetQtpBalanceCircCheckFlag()
{
    return s_bQtpBalaCircCheckFlag;
}

BOOLEAN SetQtpBalanceCircCheckFlag(BOOLEAN Param)
{
    return s_bQtpBalaCircCheckFlag = Param;
}

//启动加热器加热
static void StartAndStopHeaterQtp(BYTE ucPort)
{
    BYTE ucCtrlCode = 0;

    ucCtrlCode = s_tProtocol.aucRecBuf[7];

    if(ucCtrlCode == 0)
    {
        SetHeaterTestCtrl(HEATER_TEST_OFF);
        rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
        s_tProtocol.wSendLenid = 0;
    }
    else if(ucCtrlCode == 1)
    {
        SetHeaterTestCtrl(HEATER_TEST_ON);
        rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
        s_tProtocol.wSendLenid = 0;
    }
    else
    {
       s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
    }

    return;
} 


/****************************************************************************
* 函数名称：CheckFatRecordValid()
* 调    用：无
* 被 调 用：
* 输入参数：fatRecord -出厂测试记录结果
* 返 回 值：
* 功能描述：对出厂测试记录信息进行容错校验
* 作    者：周雪刚
* 设计日期：2013-03-28
* 修改记录：
* 日    期      版  本      修改人      修改摘要
***************************************************************************/
static BOOLEAN CheckFatRecordValid( T_FatRecordStruct * fatRecord )
{
    BOOLEAN ucFlag = True;

    if ((fatRecord->fatRecordType != MODILE_TEST))
    {
        ucFlag = False;
    }
    else if ((CheckTimeValid(&fatRecord->tTime) == False) || (fatRecord->tTime.wYear > 2037))
    {
        ucFlag = False;
    }
    else if ((fatRecord->fatRecordResult > RESULT_PASS))
    {
        ucFlag = False;
    }

    return ucFlag;
}

/****************************************************************************
* 函数名称：WriteFatRecordToEEPROM
* 输入参数：无
* 返 回 值：无
* 功能描述：保存出厂测试记录到EEPROM
***************************************************************************/
static void WriteFatRecordToEEPROM( T_FatRecordStruct * fatRecord )
{
    BYTE    *p;

    p = (BYTE *)fatRecord;
    fatRecord->wCheckSum = CRC_Cal( p, sizeof(T_FatRecordStruct)-2 );

	switch(fatRecord->fatRecordType)
	{
		case 0:
		    writeFile("/fatRecord00", p, sizeof(T_FatRecordStruct));
			break;
		case 1:
		    writeFile("/fatRecord01", p, sizeof(T_FatRecordStruct));
			break;
		default:
		    break;
	}

    return;
}


/****************************************************************************
* 函数名称：ReadFatRecordFromEEPROM
* 输入参数：无
* 返 回 值：无
* 功能描述：从EEPROM中读取出厂测试记录
***************************************************************************/
static void ReadFatRecordFromEEPROM( T_FatRecordStruct * fatRecord )
{
	BYTE    *p;
    WORD    wCrc;
    BOOLEAN bFlag = True;
	UNUSED LONG lFatBmsRecordReadRtn = 0;  // NOTE: 在QTP中校验fatRecord00文件里数据
	LONG lFatModRecordReadRtn = 0;

    p = (BYTE *)fatRecord;
	lFatBmsRecordReadRtn = readFile("/fatRecord00", p, sizeof(T_FatRecordStruct));
	p+=sizeof(T_FatRecordStruct);
	lFatModRecordReadRtn = readFile("/fatRecord01", p, sizeof(T_FatRecordStruct));

	bFlag = True;
	wCrc = CRC_Cal( (BYTE *)(fatRecord + 1), sizeof(T_FatRecordStruct)-2 );

	if (wCrc == fatRecord[1].wCheckSum && lFatModRecordReadRtn > 0)
	{
		if (CheckFatRecordValid((T_FatRecordStruct *)(fatRecord + 1)) == False)
		{
			bFlag = False;  
		}
	}
	else
	{
		bFlag = False;
	}

	if (bFlag == False )
	{
		rt_memset((T_FatRecordStruct *)(fatRecord + 1), 0x00, sizeof(T_FatRecordStruct));
	}

    return;
}

/****************************************************************************
* 函数名称：SetFactoryAcceptanceTestQtp()
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：获取出厂测试记录
* 作    者  ：
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
static void SetFactoryAcceptanceTestQtp( BYTE ucPort )
{
    T_FatRecordStruct t_FatRecord;

    t_FatRecord.fatRecordType = s_tProtocol.aucRecBuf[7];
    ExchangeIntHighLowByte( (BYTE *)(s_tProtocol.aucRecBuf+8) );
    rt_memcpy((BYTE*)(&t_FatRecord), (BYTE*)(&s_tProtocol.aucRecBuf[7]),
            SIGAL_FAT_RECORD_LENGTH);//PCLINT检查:经确认，对程序正确性无影响。
    //校验记录是否符合条件
    if (CheckFatRecordValid((T_FatRecordStruct*) & t_FatRecord) == True)
    {
        t_FatRecord.bRecValid = True;
        WriteFatRecordToEEPROM((T_FatRecordStruct*)&t_FatRecord);
    }
    else
    {
        s_tProtocol.ucRTN   = RTN_INVALID_DATA;
    }

    return;
}

/****************************************************************************
* 函数名称：GetFactoryAcceptanceTestQtp()
* 调    用：无
* 被 调 用：
* 输入参数：
* 返 回 值：
* 功能描述：发送出厂测试记录
* 作    者  ：王威
* 版本信息：V1.0
* 设计日期：2010-06-13
* 修改记录：
* 日    期      版  本      修改人      修改摘要      
***************************************************************************/
static void GetFactoryAcceptanceTestQtp( BYTE ucPort )
{
    WORD wSendLen = 36;//(9+9) * 2 = 36
    BYTE *p = RT_NULL;
    T_FatRecordStruct t_FatRecord[3];
    rt_memset(t_FatRecord, 0x00, sizeof(t_FatRecord));

    ReadFatRecordFromEEPROM((T_FatRecordStruct *)(&t_FatRecord[0]));
    ExchangeIntHighLowByte((BYTE*)&t_FatRecord[0].tTime);
    ExchangeIntHighLowByte((BYTE*)&t_FatRecord[1].tTime);

    p = s_tProtocol.aucSendBuf;

    rt_memcpy( p, (BYTE *)&t_FatRecord[0], SIGAL_FAT_RECORD_LENGTH);//后面3个字节不发送，共9字节
    p+=SIGAL_FAT_RECORD_LENGTH;
    rt_memcpy( p, (BYTE *)&t_FatRecord[1], SIGAL_FAT_RECORD_LENGTH);

    /* LENID */
    s_tProtocol.wSendLenid = wSendLen;//为解决KW问题

    return;
}

static void GetFaultInfo(BYTE ucPort)
{
    BYTE *p = NULL;

    T_DCRealData tDcRealData;
	rt_memset(&tDcRealData, 0, sizeof(T_DCRealData));
	GetBduReal(&tDcRealData);

	p  = s_tProtocol.aucSendBuf;
	rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));

    DealFaultDiagnAlarm(p, &tDcRealData);
    p += 4;

	/* LENID */	
    s_tProtocol.wSendLenid  = (WORD) ((p-s_tProtocol.aucSendBuf)*2);
}


BYTE GetChageProtectFlag(void)
{
    return s_bChargeProtectFlag;
}

BOOLEAN GetHeatTimeFlag(void)
{
    return s_bHeaterTimeFlag;
}

void SetHeatTimeFlagFalse(UNUSED void *parameter)
{
    s_bHeaterTimeFlag = False;
    SetHeaterTestCtrl(HEATER_TEST_OFF);
    s_bChargeProtectFlag = 0;
}

Static void GetCurrent(UNUSED void *parameter)
{
    T_BCMDataStruct tBCMData;
    rt_memset(&tBCMData, 0, sizeof(T_BCMDataStruct));

    GetRealData( &tBCMData );
    s_fHeaterCurrent = tBCMData.fBusCurr;
}

static void GetHeaterCurrent(BYTE ucPort)
{
    s_bChargeProtectFlag = 1;
    SetHeaterTestCtrl(True);
    s_bHeaterTimeFlag = True;

    if (RT_NULL == s_Heattimer)
    {
        s_Heattimer = rt_timer_create("HeatTime", SetHeatTimeFlagFalse, RT_NULL, 30000, RT_TIMER_FLAG_ONE_SHOT);
    }

    if (RT_NULL == s_GetCurrentTimer)
    {
        s_GetCurrentTimer = rt_timer_create("GetCurrentTime", GetCurrent, RT_NULL, 2000, RT_TIMER_FLAG_ONE_SHOT);
    }

    if (s_Heattimer != RT_NULL && s_GetCurrentTimer != RT_NULL)
    {
        rt_timer_start(s_Heattimer);
        rt_timer_start(s_GetCurrentTimer);
    }
    else
    {
        s_tProtocol.ucRTN   = RTN_FAIL_COMMAND;
        return;
    }

    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    s_tProtocol.wSendLenid = 0;

    return;
}

static void SendHeaterCurrent(BYTE ucPort)
{
    BYTE *p = NULL;

    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    p  = s_tProtocol.aucSendBuf;

    *(SHORT *)p = FloatChangeToModbus(s_fHeaterCurrent * 100); // 加热膜电流( A )
    p += 2;

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
}

// 停止加热膜电流检测
Static void StopHeaterCurrent(BYTE ucPort)
{
    SetHeatTimeFlagFalse(NULL);

    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    s_tProtocol.wSendLenid = 0;
}

#ifndef DEVICE_USING_R321
// 设置电池参数数据
static void SetBMSPara( BYTE ucPort )
{
    U_Int tData;
	T_DCPara tDCPara;

	rt_memset(&tDCPara,0x00,sizeof(T_DCPara));
    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];
	tData.ucByte[0] = s_tProtocol.aucRecBuf[9];
	tData.ucByte[1] = s_tProtocol.aucRecBuf[8];

    switch (s_tProtocol.ucCommandType)
    {
        case 0x80:
            if(!RangeValidationFloat(tData.iData / 100.0, 3.0f, 100.0f)) {//设置充电电流 1103 mwl 
                s_tProtocol.ucRTN   = RTN_INVALID_DATA;
                return;
            } 
            GetBduPara(&tDCPara);

            tDCPara.wChgCurVal = (WORD)(g_ProConfig.fRateCurrChg / g_ProConfig.fRateCurrChg * BDU_LIMIT_MAX);//采用千分比类型
            tDCPara.wChgBusCurVal = (WORD)(tData.iData / 100.0 / g_ProConfig.fRateCurrChgBus * BDU_LIMIT_MAX);//采用千分比类型

            SetBduPara(&tDCPara);
            BduCtrl(SCI_CTRL_CHG_STG, DISABLE);
            BduCtrl(SCI_CTRL_CHG2CHG, ENABLE);
            BduCtrl(SCI_CTRL_CHG_PRT, DISABLE);
            break;
        default:
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
            break;
    }
}

static void GetBMSPara(BYTE ucPort)
{
    BYTE *p = NULL;
    T_DCPara tDCPara;

	rt_memset(&tDCPara,0x00,sizeof(T_DCPara));
    GetBduPara(&tDCPara);

    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    p  = s_tProtocol.aucSendBuf;

    *(SHORT *)p = FloatChangeToModbus(((FLOAT)tDCPara.wChgBusCurVal) * g_ProConfig.fRateCurrChgBus / BDU_LIMIT_MAX * 100.0f); // 充电限流值( A )
    p += 2;

    *p ++ = 0; //自定义字节数

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
}
#endif

static void RecvSysParaById(BYTE ucPort)
{
    WORD wParaId = MakeParaId(s_tProtocol.aucRecBuf[7], s_tProtocol.aucRecBuf[8]);
    WORD wRecvParaLen = s_tProtocol.wRecLenid / 2 - 2;
    BYTE *p = &s_tProtocol.aucRecBuf[9];

    int sz = SetParaById(wParaId, p, wRecvParaLen, EndianConvert);
    if (sz < 0)
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
        return;
    }
    GetSysPara(&s_tSysPara);
    WriteBMSDefaultPara(&s_tSysPara);
}

static void SendSysParaById(BYTE ucPort)
{ 
    BYTE *p = s_tProtocol.aucSendBuf;
    WORD wParaId = 0;
    int sz = -1;
    rt_memset(p, 0x00, sizeof(s_tProtocol.aucSendBuf));

    if (s_tProtocol.aucRecBuf[8] != 0xFF)
    {
        wParaId = MakeParaId(s_tProtocol.aucRecBuf[7], s_tProtocol.aucRecBuf[8]);
        sz = GetParaById(wParaId, p, sizeof(s_tProtocol.aucSendBuf), EndianConvert);
    }
    else
    {
        sz = GetMultiParaBySection(s_tProtocol.aucRecBuf[7], p, sizeof(s_tProtocol.aucSendBuf), EndianConvert);
    }

    if (sz < 0)
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
        return;
    }
    p += sz;

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
}

static void RefreshDefaultPar(BYTE ucPort)
{
    GetSysDefaultPara(&s_tSysPara);
    PackInfoSync();
    if (False == SetSysPara(&s_tSysPara, True, CHANGE_BY_QTPTEST))
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
        return;
    }
    deleteFile("/defaultpara");
#ifndef UNITEST
    SaveAction(GetActionId(CONTOL_RST_PARA), "Qtp Default Para");
#endif
    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    s_tProtocol.wSendLenid = 0;
}

static void GetProductVerInfo(BYTE ucPort)
{
    BYTE *p = NULL;
    BYTE *pucSelfDefinedByteCount = NULL;
    T_HardwareParaStruct tHardwarePara = {0, };
    T_BmsPACKFactoryStruct tBmsPackFacInfo = {0, };
    T_DCFactory tBduAsset = {0, };
    BYTE aucMacAddr[6] = {0, };

    rt_memset(s_tProtocol.aucSendBuf, 0, sizeof(s_tProtocol.aucSendBuf));
    p = s_tProtocol.aucSendBuf;

    GetBduFact(&tBduAsset);
    rt_memcpy(p, tBduAsset.acAsset, 15);  // BDU序列号（15位）
    p += 15;

    readBmsHWPara(&tHardwarePara);
    *p++ = tHardwarePara.ucCellVoltNum;    // 单节电压检测数量
    *p++ = tHardwarePara.ucCellTempNum;    // 单体温度检测数量

    readBmsPackFacInfo(&tBmsPackFacInfo);
    p += PutTimeStruct2Buff(p, tBmsPackFacInfo.tFactoryTime);      //出厂时间
    p += PutTimeStruct2Buff(p, tBmsPackFacInfo.tFirstBootTime);    //第一次开机时间

    pucSelfDefinedByteCount = p; // 自定义字节数
    p++;

    rt_device_t ethDev = rt_device_find("e0");  // dirty
    if (ethDev != RT_NULL)
    {
        rt_device_control(ethDev, NIOCTL_GADDR, aucMacAddr);
        for (uint8_t i = 0; i < 6; i++)
        {
            *p++ = aucMacAddr[i];
        }
    }

    *pucSelfDefinedByteCount = p - pucSelfDefinedByteCount - 1;

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
}

static void GetTotalDischargeInfo(BYTE ucPort)
{
    BYTE *p = NULL;
    T_BattResult tBattResult;

    rt_memset_s(&tBattResult, sizeof(T_BattResult), 0, sizeof(T_BattResult));
    GetBattResult(&tBattResult);

    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));
    p = s_tProtocol.aucSendBuf;

    *(INT32 *)p = FloatChangeToInt32Modbus(tBattResult.fTotalDischargeCap * 100); //累计放电容量
    p += 4;
    *(INT32 *)p = FloatChangeToInt32Modbus(tBattResult.fTotalDischargeQuanty * 100); // 累计放电电量
    p += 4;

    *p = 0; // 自定义字节数
    p += 1;

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf)*2);

    return;
}

//清除累计放电信息
static void ClearTotalDischargeInfo(BYTE ucPort)
{
    SetBattCycleTimes(0);

    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));
    s_tProtocol.wSendLenid = 0;

    return;
}

#ifdef INTELLIGENT_PEAK_SHIFTING

static void GetPeakShiftParaQtp(BYTE ucPort)
{
    BYTE *p = NULL;
    T_PeakShiftPara tPeakShiftPara;
    rt_memset_s(&tPeakShiftPara, sizeof(T_PeakShiftPara), 0x00, sizeof(T_PeakShiftPara));
    GetPeakShiftPara(&tPeakShiftPara);

    p = s_tProtocol.aucSendBuf;
    rt_memset_s(s_tProtocol.aucSendBuf, sizeof(s_tProtocol.aucSendBuf), 0, sizeof(s_tProtocol.aucSendBuf));

    *p++ = tPeakShiftPara.bPeakShift;   // 智能错峰使能
    *p++ = tPeakShiftPara.ucPeakShiftDOD;  // 错峰放电深度
    *p++ = 0; // 自定义字节数

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
}



static void SetPeakShiftParaQtp(BYTE ucPort)
{
    BYTE aucLenBuf[2] = {1, 1};
    T_PeakShiftPara tPeakShiftPara;
    rt_memset_s(&tPeakShiftPara, sizeof(T_PeakShiftPara), 0x00, sizeof(T_PeakShiftPara));
    GetPeakShiftPara(&tPeakShiftPara);

    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];

    if (s_tProtocol.ucCommandType < 0x80 + sizeof(aucLenBuf) && s_tProtocol.ucCommandType >= 0x80)
    {
        if (aucLenBuf[s_tProtocol.ucCommandType - 0x80] != (s_tProtocol.wRecLenid / 2 - 1))
        {
            s_tProtocol.ucRTN = RTN_INVALID_DATA;
            return;
        }
    }
    else
    {
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
        return;
    }

    switch (s_tProtocol.ucCommandType)
    {
        case 0x80:
            tPeakShiftPara.bPeakShift = s_tProtocol.aucRecBuf[8];
            break;
        case 0x81:
            tPeakShiftPara.ucPeakShiftDOD = s_tProtocol.aucRecBuf[8];
            break;
        default:
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND; // 命令格式错
            return;
    }

    if (!SetPeakShiftPara(&tPeakShiftPara, s_tProtocol.ucCommandType))
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
    }
    return;
}

#endif

// 设置客户软件定制化信息

Static void SetSoftwareInfo(BYTE ucPort)
{
    SHORT wTemp = 0;
    T_DCFactory tDcFactory;
    T_DateStruct tDate;
    T_SoftwareCustomizedInfo tSoftwareCustomizedInfo;

    rt_memset_s(&tSoftwareCustomizedInfo, sizeof(T_SoftwareCustomizedInfo), 0x00, sizeof(T_SoftwareCustomizedInfo));
    rt_memset_s(&tDate, sizeof(T_DateStruct), 0x00, sizeof(T_DateStruct));

    GetBduFact(&tDcFactory);
    
    readSoftwareCustomizedInfo(&tSoftwareCustomizedInfo);

    // 保存客户定制化配置信息
    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];
    wTemp = Host2Modbus((SHORT*)(s_tProtocol.aucRecBuf + 8));
    tDate.wYear = (WORD)wTemp;
    tDate.ucMonth = s_tProtocol.aucRecBuf[10];
    tDate.ucDay = s_tProtocol.aucRecBuf[11];

    switch(s_tProtocol.ucCommandType)
    {
        case 0x80:// 定制BMS软件版本
            rt_memcpy_s(tSoftwareCustomizedInfo.CustomizedBmsVersion, sizeof(tSoftwareCustomizedInfo.CustomizedBmsVersion), s_tProtocol.aucRecBuf + 8, sizeof(tSoftwareCustomizedInfo.CustomizedBmsVersion));
            break;
        case 0x81:// 定制BMS软件发布日期
            if(!CheckDateAllZero(&tDate))
            {
                if(!CheckDateValid(&tDate))
                {
                    s_tProtocol.ucRTN = RTN_INVALID_DATA;
                    return;
                }
            }
            rt_memcpy_s(tSoftwareCustomizedInfo.CustomizedBmsReleaseDate, sizeof(tSoftwareCustomizedInfo.CustomizedBmsReleaseDate), s_tProtocol.aucRecBuf + 8, sizeof(tSoftwareCustomizedInfo.CustomizedBmsReleaseDate));
            break;
        case 0x82:// 定制BDU软件版本
            rt_memcpy_s(tSoftwareCustomizedInfo.CustomizedBduVersion, sizeof(tSoftwareCustomizedInfo.CustomizedBduVersion), s_tProtocol.aucRecBuf + 8, sizeof(tSoftwareCustomizedInfo.CustomizedBduVersion));
            break;
        case 0x83:// 定制BDU软件发布日期
            if(!CheckDateAllZero(&tDate))
            {
                if(!CheckDateValid(&tDate))
                {
                    s_tProtocol.ucRTN = RTN_INVALID_DATA;
                    return;
                }
            }
            rt_memcpy_s(tSoftwareCustomizedInfo.CustomizedBduRealseDate, sizeof(tSoftwareCustomizedInfo.CustomizedBduRealseDate), s_tProtocol.aucRecBuf + 8, sizeof(tSoftwareCustomizedInfo.CustomizedBduRealseDate));
            break;
        default:
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND; // 命令格式错
            return;
    }

    if(!writeSoftwareCustomizedInfo(&tSoftwareCustomizedInfo))
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA; 
    }

    return;
}


// 获取客户软件定制化信息，按照commandtype进行获取

Static void GetSoftwareInfo(BYTE ucPort)
{
    BYTE *p = NULL;
    T_SoftwareCustomizedInfo tSoftwareCustomizedInfo;
    rt_memset_s(&tSoftwareCustomizedInfo, sizeof(T_SoftwareCustomizedInfo), 0x00, sizeof(T_SoftwareCustomizedInfo));

    readSoftwareCustomizedInfo(&tSoftwareCustomizedInfo);

    p = s_tProtocol.aucSendBuf;

    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];

    switch(s_tProtocol.ucCommandType)
    {
        case 0x80: // 定制BMS软件版本
            MemsetBuff(p, tSoftwareCustomizedInfo.CustomizedBmsVersion, 20, 20, 0x20);
            p += 20;
            break;
        case 0x81: // 定制BMS软件发布日期
            MemsetBuff(p, tSoftwareCustomizedInfo.CustomizedBmsReleaseDate, 4, 4, 0x20);
            p += 4;
            break;
        case 0x82: // 定制BDU软件版本
            MemsetBuff(p, tSoftwareCustomizedInfo.CustomizedBduVersion, 6, 6, 0x20);
            p += 6;
            break;
        case 0x83: // 定制BDU软件发布日期
            MemsetBuff(p, tSoftwareCustomizedInfo.CustomizedBduRealseDate, 4, 4, 0x20);
            p += 4;
            break;
        default:
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND; // 命令格式错
            return;
    }

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);

    return;
}



Static void SetTimeQTP(BYTE ucPort)
{
#ifdef NTP_TIME_ZONE_ENABLE
    struct tm tSetTime = {0};
    time_t t_set;

    GetSysPara(&s_tSysPara);
    // Extract and set the time components from the received buffer
    tSetTime.tm_year = (s_tProtocol.aucRecBuf[7] * 256 + s_tProtocol.aucRecBuf[8]) - 1900;
    tSetTime.tm_mon  = s_tProtocol.aucRecBuf[9] - 1;
    tSetTime.tm_mday = s_tProtocol.aucRecBuf[10];
    tSetTime.tm_hour = s_tProtocol.aucRecBuf[11];
    tSetTime.tm_min  = s_tProtocol.aucRecBuf[12];
    tSetTime.tm_sec  = s_tProtocol.aucRecBuf[13];

    // Adjust for timezone difference
    t_set = mktime(&tSetTime) - GetTimeZone(8) * ONE_HOUR_SECONDS + GetTimeZone(s_tSysPara.ucTimeZone) * ONE_HOUR_SECONDS;

    // Convert back to local time
    localtime_r(&t_set, &tSetTime);

    // Update the received buffer with the adjusted time
    s_tProtocol.aucRecBuf[7] = (tSetTime.tm_year + 1900) / 256;
    s_tProtocol.aucRecBuf[8] = (tSetTime.tm_year + 1900) % 256;
    s_tProtocol.aucRecBuf[9] = tSetTime.tm_mon + 1;
    s_tProtocol.aucRecBuf[10] = tSetTime.tm_mday;
    s_tProtocol.aucRecBuf[11] = tSetTime.tm_hour;
    s_tProtocol.aucRecBuf[12] = tSetTime.tm_min;
    s_tProtocol.aucRecBuf[13] = tSetTime.tm_sec;
#endif

    RecSysTime(ucPort);
}



Static void GetTimeQTP(BYTE ucPort)
{
    time_t t = time(RT_NULL);

#ifdef NTP_TIME_ZONE_ENABLE
    GetSysPara(&s_tSysPara);
    t = t - GetTimeZone(s_tSysPara.ucTimeZone) * ONE_HOUR_SECONDS + GetTimeZone(8) * ONE_HOUR_SECONDS;
#endif

    GetSysTimeQTP(t);
}



Static void GetSpecialParaQtp(BYTE ucPort)
{
    BYTE *p = NULL;
    T_HardwareParaStruct tHardwarePara;

    if (ucPort >= SCI_PORT_NUM )
    {
        return;
    }    
    rt_memset_s(&tHardwarePara, sizeof(T_HardwareParaStruct), 0, sizeof(T_HardwareParaStruct)); 
    readBmsHWPara(&tHardwarePara);

    p = s_tProtocol.aucSendBuf;

    *p++ = tHardwarePara.ucCellVoltNum;   // 单节电压检测数量
    *p++ = tHardwarePara.ucCellTempNum;  // 单体温度检测数量
    *p++ = 0; // 自定义字节数

    s_tProtocol.wSendLenid = GetLength((p - s_tProtocol.aucSendBuf) * 2);
    return;
}



Static void SetSpecialParaQtp(BYTE ucPort)
{
    // 定义和初始化变量
    BYTE aucLenBuf[2] = {1, 1};
    T_HardwareParaStruct tHardwarePara;
    rt_memset_s(&tHardwarePara, sizeof(T_HardwareParaStruct), 0x00, sizeof(T_HardwareParaStruct));
    readBmsHWPara(&tHardwarePara);

    // 获取命令类型
    s_tProtocol.ucCommandType = s_tProtocol.aucRecBuf[7];

    // 检查命令类型是否有效
    if (s_tProtocol.ucCommandType < 0x80 || s_tProtocol.ucCommandType >= 0x80 + sizeof(aucLenBuf))
    {
        s_tProtocol.ucRTN = RTN_WRONG_COMMAND;
        return;
    }

    // 检查数据长度是否匹配
    if (aucLenBuf[s_tProtocol.ucCommandType - 0x80] != (s_tProtocol.wRecLenid / 2 - 1))
    {
        s_tProtocol.ucRTN = RTN_INVALID_DATA;
        return;
    }

    // 处理不同的命令类型
    switch (s_tProtocol.ucCommandType)
    {
        case 0x80:
            tHardwarePara.ucCellVoltNum = s_tProtocol.aucRecBuf[8];
            break;
        case 0x81:
            tHardwarePara.ucCellTempNum = s_tProtocol.aucRecBuf[8];
            break;
        default:
            s_tProtocol.ucRTN = RTN_WRONG_COMMAND; // 命令格式错
            return;
    }

    // 写入硬件参数
    if (s_tProtocol.ucRTN != RTN_INVALID_DATA && s_tProtocol.ucRTN != RTN_WRONG_COMMAND)
    {
        writeBmsHWPara(&tHardwarePara);
    }
}

