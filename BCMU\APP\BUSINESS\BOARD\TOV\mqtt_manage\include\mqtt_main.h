#ifndef _MQTTT_MAIN_H_
#define _MQTTT_MAIN_H_

#ifdef __cplusplus
extern "C" {
#endif    //  __cplusplus

#include "softbus.h"

#define BUFFER_SIZE 2048
#define MQTT_PERIOD (15*60*1000)
#define MQTT_THRESH 4
#define WAIT_COUNT   (5*60*1000/20) // 5分钟
#define COUNT_4G     (30*1000/20)  // sim卡插好后等待30s

typedef struct
{
    char*         file_name[15];
    unsigned char num;
}file_name_map_t;

typedef struct {
    unsigned int msg_id;
    void (*handle)(_rt_msg_t curr_msg);
}mqtt_msg_handle_process_t;

void* init_mqtt(void* param);
void mqtt_thread_entry(void* thread_data);
void handle_upfile(_rt_msg_t curr_msg);
int process_recv_mqtt_msg(void);
void reset_4g_timeout();
int init_file_upload();
int get_msgid(char** msgid);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif // _MQTTT_MAIN_H_
