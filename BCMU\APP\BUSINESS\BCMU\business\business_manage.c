#include "msg.h"
#include "msg_id.h"
#include "data_type.h"
#include "softbus.h"
#include "utils_server.h"

static rt_uint32_t last_time = 0;
static module_msg_t msg_rcv = {.dest = MOD_BUSINESS_MANAGE};

static void process_recv_msg(void);

static void process_data_change(const rt_msg_t pMsg){

}

static msg_map business_manage_msg_map[] =
{
    DATA_CHANGE_MSG_ID,   process_data_change,          
};


void* init_business_manage(void * param)
{
    server_info_t *server_info = (server_info_t *)param;
    register_server_msg_map(business_manage_msg_map, server_info);
    RETURN_VAL_IF_FAIL(init_msg_queue(MOD_SYS_MANAGE) != NULL, NULL);
    return NULL;
    //business_manage_main(NULL);
}

void business_manage_main(void *param) {
    cell_balance_process_start();
    while (is_running(TRUE)) {
        process_recv_msg();
        rt_thread_delay(1000);
        exe_heartbeat(last_time, MOD_BUSINESS_MANAGE);
    }
}

static void process_recv_msg(void) {
    
    if (SUCCESSFUL != recv_msg(&msg_rcv))
        return;
    
    // switch (msg_rcv.msg_id)
    // {
    //     case /* constant-expression */:
    //         /* code */
    //         break;
        
    //     default:
    //         break;
    // }
    
    if (msg_rcv.data != NULL) {
        free(msg_rcv.data);
        msg_rcv.data = NULL;
    }
}
