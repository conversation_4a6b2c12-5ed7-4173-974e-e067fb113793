/**
 * @file     acmu_protocol_north.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2025-05-12
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */

#ifndef _ACMU_PROTOCOL_NORTH_H
#define _ACMU_PROTOCOL_NORTH_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "device_type.h"

// 厂家信息长度宏定义
#define LEN_SMNAME      30
#define LEN_RESERVED    13

// 功能码定义
#define	ORDER_NULL				0x00	// 
#define	ORDER_CTRL				0x06	// 遥控命令
#define	ORDER_GET				0x20	// 呼叫命令
#define	ORDER_GETREALALM	    0x21	// 查询实时告警
#define	ORDER_GETHISALM		    0x22	// 查询历史告警
#define	ORDER_GETFACTINFO	    0x23	// 查询厂家信息
#define	ORDER_GETPARA			0x24	// 查询全部参数
#define	ORDER_SETTIME			0x25	// 校准时间
#define	ORDER_SETPARA			0x26	// 设置全部参数
#define	ORDER_CTRLRST			0xF3	// 复位命令

#define GROUP_TYPE_RCV_START    0x00     //接收第一包
#define GROUP_TYPE_RCV_NEXT     0x01     //接收下一包
#define GROUP_TYPE_RCV_ERROR    0x02     //接收错误，重发
#define GROUP_TYPE_RCV_END      0x03     //接收结束

#define RTNRS485_OK	0x00	// 正确
#define RTNRS485_INVLORDER	0x01	// 不合法功能代码
#define RTNRS485_INVLDATAADD	0x02	// 不合法数据地址
#define RTNRS485_INVLDATA	0x03	// 不合法数据
#define RTNRS485_NODATA		0x04	// 无数据

#define GROUP_TYPE_SEND_START    0x00     //发送第一包
#define GROUP_TYPE_SEND_NEXT     0x01     //发送下一包

#define GROUP_TYPE_SEND_NORMAL    0x00    //正常发送
#define GROUP_TYPE_SEND_LAST      0x01     //最后一包
#define GROUP_TYPE_SEND_END       0x02     //发送结束

#define RS485_DATALEN_MAX             255
#define ACMU_REAL_ALARM_DATALEN_MAX   (RS485_DATALEN_MAX - 15)	// 一条实时告警的长度是11,预留4个字节,所以要减去15
#define ACMU_HIS_ALARM_PACK_MAX       (RS485_DATALEN_MAX - 22)	// 一条历史告警的长度是18,预留4个字节,所以要减去22


/*north protocol的命令唯一标识定义 */
#define ACMU_NORTH_GET_DATA                 1           //<呼叫命令
#define ACMU_NORTH_CTRL_CMD                 2           //<遥控命令
#define ACMU_NORTH_GET_ALARM                3           //<查询实时告警
#define ACMU_NORTH_GET_PARA                 4           //<获取交流公共参数
#define ACMU_NORTH_SET_PARA                 5           //<设置交流公共参数
#define ACMU_NORTH_GET_HIS_ALM              6           //<查询历史告警
#define ACMU_NORTH_GET_FAC                  7           //<查询厂家信息
#define ACMU_NORTH_SET_TIME                 8           //<校准时间

dev_type_t* init_dev_acmu_north(void);
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _ACMU_PROTOCOL_NORTH_H

