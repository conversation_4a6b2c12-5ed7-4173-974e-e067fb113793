#include "protocol_layer.h"
#include "bms_1363.h"
#include "sps.h"
#include "realdata_id_in.h"
#include "const_define_in.h"
#include "realdata_save.h"
#include "para_id_in.h"
#include "comm_can.h"

Static unsigned char g_rtn = 0;


Static int parse_control_cmd(void* dev_inst, void* cmd_buff);
Static int pack_control_cmd(void* dev_inst, void* cmd_buf);

// 以下代码定义了请求和应答命令头数组，用于处理不同类型的命令

static s1363_cmd_head_t cmd_req[] RAM_SECTION = 
{
    {VER_22 , BMS_1363_CID1 , CMD_GET_ANA_INT_DATA},
    {VER_22 , BMS_1363_CID1 , CMD_GET_DIG_DATA},
    {VER_22 , BMS_1363_CID1 , CMD_GET_ALM_DATA},
    {VER_22 , BMS_1363_CID1 , <PERSON><PERSON>_CONTROL},
    {VER_22 , BMS_1363_CID1 , C<PERSON>_GET_SYSPARA_DATA},
    {VER_22 , BMS_1363_CID1 , CMD_SET_SYSPARA_DATA},
    {VER_22 , BMS_1363_CID1 , CMD_GET_HIS_DATA},
    {VER_22 , BMS_1363_CID1 , CMD_GET_HIS_ALM_DATA},
    {VER_22 , BMS_1363_CID1 , CMD_GET_TIME_DATA},
    {VER_22 , BMS_1363_CID1 , CMD_SET_TIME_DATA},
    {VER_22 , BMS_1363_CID1 , CMD_GET_FACT_DATA},
    {VER_22 , BMS_1363_CID1 , CMD_GET_SPE_PARA},
    {VER_22 , BMS_1363_CID1 , CMD_SET_SPE_PARA},
    {VER_22 , BMS_1363_CID1 , CMD_GET_ALM_LEV},
    {VER_22 , BMS_1363_CID1 , CMD_SET_ALM_LEV},
    {VER_22 , BMS_1363_CID1 , CMD_GET_HIS_OPE_DATA},
    {VER_22 , BMS_1363_CID1 , CMD_GET_EXTREME_REC},
    {VER_22 , BMS_1363_CID1 , CMD_GET_CELL_STA_REC},
    {VER_22 , BMS_1363_CID1 , CMD_SET_SPE_DATA},
};

static s1363_cmd_head_t cmd_ack[] RAM_SECTION = 
{
    {VER_22 , BMS_1363_CID1 , CMD_GET_ANA_INT_DATA},
    {VER_22 , BMS_1363_CID1 , CMD_GET_DIG_DATA},
    {VER_22 , BMS_1363_CID1 , CMD_GET_ALM_DATA},
    {VER_22 , BMS_1363_CID1 , CMD_CONTROL},
    {VER_22 , BMS_1363_CID1 , CMD_GET_SYSPARA_DATA},
    {VER_22 , BMS_1363_CID1 , CMD_SET_SYSPARA_DATA},
    {VER_22 , BMS_1363_CID1 , CMD_GET_HIS_DATA},
    {VER_22 , BMS_1363_CID1 , CMD_GET_HIS_ALM_DATA},
    {VER_22 , BMS_1363_CID1 , CMD_GET_TIME_DATA},
    {VER_22 , BMS_1363_CID1 , CMD_SET_TIME_DATA},
    {VER_22 , BMS_1363_CID1 , CMD_GET_FACT_DATA},
    {VER_22 , BMS_1363_CID1 , CMD_GET_SPE_PARA},
    {VER_22 , BMS_1363_CID1 , CMD_SET_SPE_PARA},
    {VER_22 , BMS_1363_CID1 , CMD_GET_ALM_LEV},
    {VER_22 , BMS_1363_CID1 , CMD_SET_ALM_LEV},
    {VER_22 , BMS_1363_CID1 , CMD_GET_HIS_OPE_DATA},
    {VER_22 , BMS_1363_CID1 , CMD_GET_EXTREME_REC},
    {VER_22 , BMS_1363_CID1 , CMD_GET_CELL_STA_REC},
    {VER_22 , BMS_1363_CID1 , CMD_SET_SPE_DATA},
};



// 以下代码定义了控制命令数组，用于处理不同的控制命令

Static ctrl_cmd_1363_t s_ctrl_cmd[] RAM_SECTION =
{    /*cmd_type                     ctrl_id                               ctrl_status          ctrl_cmd_id          is_south_ctrl_cmd           info[20]             func */
    {RES_DEFAULT_PARA_CMD,     CTRL_ID_RESTORE_DEFAULT_PARA,                0,                    0,                   FALSE,               "restore para",       NULL},
    {START_ADDR_COMP_CMD,      CTRL_ID_RESET_ADD_COMPETE,                   0,                    0,                   FALSE,                "start addr comp",   NULL},
    {REL_LOCK_CMD,             CTRL_ID_SET_CLEAR_LOCKSTAT,                  0,                    0,                   FALSE,                "release lock",      NULL},
    {UNLOCK_LOSS_ALM_CMD,      CTRL_ID_SET_BATT_MANUAL_UNLOCK,              0,                    0,                   FALSE,                "unlock loss alm",   NULL},
    {ENTER_DEFENCE_CMD,        CTRL_ID_MANUAL_CTRL_ENTER_DEFENCE_STATE,     0,                    0,                   FALSE,                "enter defence",     NULL},
    {EXIT_DEFENCE_CMD,         CTRL_ID_MANUAL_CANCEL_DEVICE_DEFENCE,        0,                    0,                   FALSE,                "exit defence",      NULL},
    {START_HEATER_CMD,         CTRL_ID_SET_HEATING_START,                   0,                    0,                   FALSE,                "start heat",        NULL},
    {STOP_HEATER_CMD,          CTRL_ID_SET_HEATING_STOP,                    0,                    0,                   FALSE,                "stop heat" ,        NULL},
};

Static cmd_handle_register_t s_cmd_handle[] = {
    {DEV_BMS_CAN, REC_CONTROL, CMD_TYPE_NO_POLL, parse_control_cmd, pack_control_cmd},  //遥控命令
};

/*获取模拟量 有效值*/
static data_info_id_verison_t cmd_pack_ana_valid_data_info[] RAM_SECTION = 
{
    {SEG_DATA, type_unsigned_short,  ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMS_DATA_ID_BATT_VOLT}, 
    {SEG_DATA, type_unsigned_short,  ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMS_DATA_ID_EXTER_VOLT}, 
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMS_DATA_ID_ENV_TEMP}, 
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMS_DATA_ID_BATT_CURR}, 
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, CELL_VOL_NUM, NOT_NEED_INTERACT},  //单体电压数量
    {SEG_DATA, type_unsigned_short,  ARRAY_SIZE_16, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMS_DATA_ID_CELL_VOLT}, 
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, CELL_TEMP_NUM, NOT_NEED_INTERACT},  //单体温度数量
    {SEG_DATA, type_short,  ARRAY_SIZE_4, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMS_DATA_ID_CELL_TEMP}, 
    {SEG_DATA, type_short,  ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMS_DATA_ID_BOARD_TEMP}, 
    {SEG_DATA, type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMS_DATA_ID_BATT_SOC}, 
    {SEG_DATA, type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMS_DATA_ID_BATT_SOH}, 
    {SEG_DATA, type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMS_DATA_ID_BATT_CYCLE_TIMES}, 
    {SEG_DATA, type_unsigned_short,  ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMS_DATA_ID_BATT_CHG_REQ_VOLT}, 
    {SEG_DATA, type_unsigned_short,  ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMS_DATA_ID_BATT_CHG_REQ_CURR}, 
    {SEG_DATA, type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMS_DATA_ID_CHARGR_LEFT_MINUTES}, 
    {SEG_DATA, type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMS_DATA_ID_DISCHARGR_LEFT_MINUTES}, 
    {SEG_DATA, type_unsigned_int,  ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMS_DATA_ID_TOTAL_DISCHARGE_CAP}, 
    {SEG_DATA, type_unsigned_int,  ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMS_DATA_ID_TOTAL_DISCHARGE_QUANTY}, 
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMS_DATA_ID_SIGNAL_QUALITY}, 
    {SEG_DATA, type_unsigned_short,  ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMS_DATA_ID_BATT_USABLE_CAP}, 
    {SEG_DATA, type_unsigned_int,  ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMS_DATA_ID_TOTAL_CHARGE_CAP}, 
    {SEG_DATA, type_unsigned_int,  ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMS_DATA_ID_TOTAL_CHARGE_QUANTY}, 
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},  //GPS搜星数量
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},

};

// 获取状态量
static data_info_id_verison_t cmd_pack_dig_data_info[] RAM_SECTION =
{
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMS_DATA_ID_CHG_PROT_STA},  
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMS_DATA_ID_DISCHG_PROT_STA},
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMS_DATA_ID_ALARM_STATUS},
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMS_DATA_ID_SIM_STAT},
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMS_DATA_ID_HEATPAD_ENABLE},
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMS_DATA_ID_BATT_STATUS},
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT}, //请求补充电标志
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMS_DATA_ID_MASTER_STA},
    {SEG_DATA, type_unsigned_short,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMS_DATA_ID_CELL_EQU_STA},
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMS_DATA_ID_DEFENCE_STATUS},
    {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NOT_NEED_INTERACT},

};


// 获取告警量
// static data_info_id_verison_t cmd_pack_alm_data_info[] RAM_SECTION =
// {
//     {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMS_DATA_ID_CHG_PROT_STA},  
//     {SEG_DATA, type_unsigned_char,  ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, BMS_DATA_ID_DISCHG_PROT_STA},
// }


// 以下代码定义了系统参数信息数组，用于存储和处理各种系统参数

static data_info_id_verison_t cmd_pack_sys_para_info[] RAM_SECTION ={
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_CELL_OVER_VOLT_ALM},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_CELL_UNDER_VOLT_ALM},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_BATT_OVER_VOLT_ALM},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_BATT_UNDER_VOLT_ALM},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_CELL_OVER_VOLT_PRT},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_CELL_UNDER_VOLT_PRT},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_BATT_OVER_VOLT_PRT},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_BATT_UNDER_VOLT_PRT},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_CHG_TEMP_HIGH_ALM},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_CHG_TEMP_LOW_ALM},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_CHG_TEMP_HIGH_PRT},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_CHG_TEMP_LOW_PRT},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_DISCHG_TEMP_HIGH_ALM},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_DISCHG_TEMP_LOW_ALM},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_DISCHG_TEMP_HIGH_PRT},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_DISCHG_TEMP_LOW_PRT},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_CHG_CURR_HIGH_ALM},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_CHG_CURR_HIGH_PRT},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_DISCHG_CURR_HIGH_ALM},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_DISCHG_CURR_HIGH_PRT},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_BOARD_TEMP_HIGH_PRT},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_BOARD_TEMP_HIGH_ALM},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_CELL_POOR_CONSIS_ALM},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_CELL_POOR_CONSIS_PRT},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_BATT_SOC_LOW_ALM},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_BATT_SOC_LOW_PRT},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_BATT_SOH_ALM},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_BATT_SOH_PRT},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_CELL_DAMAGE_PRT},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_ENV_TEMP_HIGH_ALM},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_ENV_TEMP_LOW_ALM},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_BATT_SUPPL_VOLT},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_CELL_EQU_VOLT_DIFF},
    // {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_CELL_EQU_VOLT_DIFF},
    {SEG_SYSTEM_PARA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_BUZZER_ENABLE},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_BATT_CAP},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_SOFT_ANTI_THEFT_DELAY},
    {SEG_SYSTEM_PARA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_GYROSCOPE_SENSITIVITY},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_HEARTBEAT_CYCLE},
    {SEG_SYSTEM_PARA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_UVP_TEMP_COMPENSATION_EN},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_ENV_TEMP_HIGH_PRT},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_ENV_TEMP_LOW_PRT},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_GSP_ANTI_THEFT_DISTANCE},
    {SEG_SYSTEM_PARA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_GYROSCOPE_ANTI_THEFT_MODE},
    {SEG_SYSTEM_PARA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_BATT_UNLOCK_MODE},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_CELL_CHG_CUTOFF_VOL},
    {SEG_SYSTEM_PARA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_HEATING_PAD_ENABLE},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_HEATER_STARTUP_TEMP},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_HEATER_SHUTDOWN_TEMP},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_2, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_CELL_SUPPL_VOLT},
    {SEG_SYSTEM_PARA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_DCR_FAULT_ALM_THRE},
    {SEG_SYSTEM_PARA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_DCR_FAULT_PRT_THRE},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_CELL_TEMP_RISE_ABNORMAL},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_SELFDISCHG_ACR},
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_CAP_DCDR},
    // {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_1, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_CAP_DCDR}, //异常高温
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_SITE_ANTITHEFT_DELAY_TIME},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_PREFAULT_RECORDING_TIME_INTERVAL},
    {SEG_SYSTEM_PARA, type_unsigned_short, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_POSTFAULT_RECORDING_TIME_INTERVAL},
    {SEG_SYSTEM_PARA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_PREFAULT_RECORDING_NUMBER},
    {SEG_SYSTEM_PARA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_POSTFAULT_RECORDING_NUMBER},
    {SEG_SYSTEM_PARA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_MEASUREMENT_POINTS_NUMBER},
    // {SEG_SYSTEM_PARA, type_unsigned_char, ARRAY_SIZE_1, DOP_0, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_MEASUREMENT_POINTS_NUMBER}, //测点数量
    {SEG_SYSTEM_PARA, type_short, ARRAY_SIZE_1, DOP_3, DATA_DROP, NO_DEF_VALUE, NEED_INTERACT, PARA_ID_CHRG_MAX_CURR},
    //自定义字节数
};



// 以下代码定义了命令组包信息数组，用于处理不同类型的数据包

static cmd_parse_info_id_verison_t cmd_pack_info[] RAM_SECTION = 
{
    {&cmd_pack_ana_valid_data_info[0],       sizeof(cmd_pack_ana_valid_data_info)/sizeof(data_info_id_verison_t)},
    {&cmd_pack_dig_data_info[0],             sizeof(cmd_pack_dig_data_info)/sizeof(data_info_id_verison_t)},
    // {&cmd_pack_alm_data_info[0],             sizeof(cmd_pack_alm_data_info)/sizeof(data_info_id_verison_t)},
    {&cmd_pack_sys_para_info[0],             sizeof(cmd_pack_sys_para_info)/sizeof(data_info_id_verison_t)},
};

// 无轮询命令表，定义了不同命令的处理方式和相关信息

static cmd_t no_poll_cmd_tab[] = 
{
    {GET_ANA_INT_DATA, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),   NULL, NULL, NULL, SELF_PARSE, &cmd_pack_info[0]},
    {GET_DIG_DATA, CMD_PASSIVE, &cmd_req[1], &cmd_ack[1], sizeof(s1363_cmd_head_t),   NULL, NULL, NULL, SELF_PARSE, &cmd_pack_info[1]},
    // {GET_ALM_DATA, CMD_PASSIVE, &cmd_req[2], &cmd_ack[2], sizeof(s1363_cmd_head_t),   NULL, NULL, NULL, SELF_PARSE, &cmd_pack_info[2]},
    {REC_CONTROL, CMD_PASSIVE, &cmd_req[3], &cmd_ack[3], sizeof(s1363_cmd_head_t),   NULL, NULL, SELF_PACK, SELF_PARSE},
    {GET_SYSPARA_DATA,  CMD_PASSIVE, &cmd_req[4], &cmd_ack[4], sizeof(s1363_cmd_head_t),      NULL, NULL, SELF_PACK, SELF_PARSE,&cmd_pack_info[3]},

};

// 设备类型定义，包含设备的基本信息和命令表

Static dev_type_t dev_bms = 
{
    DEV_BMS_CAN, 1, PROTOCOL_YD_1363, LINK_BMS_CAN, CAN1_R_BUFF_LEN, CAN1_S_BUFF_LEN, BOTTOM_R552_TYPE, &no_poll_cmd_tab[0],
};



dev_type_t* init_dev_bms_can(void){
    return &dev_bms;
}

void bms_register_cmd_table()
{
    for(size_t i = 0; i < sizeof(s_cmd_handle)/sizeof(s_cmd_handle[0]); i++) 
    {
        register_cmd_handle(&s_cmd_handle[i]);
    }
}



Static int parse_control_cmd(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);
    cmd_buf_t *cmd_buf_tmp = (cmd_buf_t *)cmd_buff;
    unsigned char cmd_type = cmd_buf_tmp->buf[0];
    int loop = 0;
    g_rtn = 0;
    for(loop = 0; loop < sizeof(s_ctrl_cmd) / sizeof(ctrl_cmd_1363_t); loop++)
    {
        if(cmd_type != s_ctrl_cmd[loop].cmd_type)
        {
            continue;
        }

        set_one_data(s_ctrl_cmd[loop].ctrl_id, &s_ctrl_cmd[loop].ctrl_status);

        //save_real_ctrl_cmd_event(s_ctrl_cmd[loop].ctrl_id, CTRL_ID_TYPE, s_ctrl_cmd[loop].info); // 保存操作记录

        if(s_ctrl_cmd[loop].func != NULL)
        {
            s_ctrl_cmd[loop].func();
        }
        break;
    }
    return SUCCESSFUL;
}



Static int pack_control_cmd(void* dev_inst, void* cmd_buf)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf != NULL, FAILURE);
    cmd_buf_t* cmd_buf_tmp = (cmd_buf_t *)cmd_buf;
    if(cmd_buf_tmp->rtn == 0)
    {
        cmd_buf_tmp->rtn = g_rtn; 
    }
    return SUCCESSFUL;
}


