/**************************************************************************
 * Copyright (C) 2001, ZTE Corporation.
 * 版权信息：（C）2010-2011，深圳市中兴通讯股份有限公司电源开发部版权所有
 * 系统名称：E260 DXCB板软件
 * 文件名称：sample.h
 * 文件说明：数据采集模块头文件
 * 作    者：hlb
 * 版本信息：V1.0
 * 设计日期：2023-09-13
 * 修改记录：
 * 日    期      版  本      修改人      修改摘要
 * 其他说明：
 ***************************************************************************/
#ifndef DEMUB_MAIN_SAMPLE_H_
#define DEMUB_MAIN_SAMPLE_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include "softbus.h"
#include "pin_define.h"
/***********************  常量定义  ************************/
#define ADCCONVERTEDVALUES_SAMPLING_NUM 16 // ADC采样次数
#define DIGITAL_COUNT 24
#define MRASURE_ANALOG_NUMBER_AC 19
#define CHECK_ANALOG_NUMBER_AC 12
#define NUM_HARMONIC 30
#define PHASE_NUM 3
#define CALC_BY_A 0
#define CALC_BY_B 1
#define SAMPLE_TEN_TIMES 10
#define COUNT_TIMES 18
#define AVER_NUM 5
#define INPUT_MCCB_NUM_NONE 0
#define INPUT_MCCB_NUM_ONE 1
#define INPUT_MCCB_NUM_TWO 2
#define MCCB_ON 0
#define MCCB_OFF 1
#define ATS_ON 0
#define ATS_OFF 1
#define MTS_DET_NONE 0
#define MTS_DET_HAS 1
#define FILTER_SAMPLE_TIMES 20
#define REMOVE_HALF_SAMPLE_TIMES 7
#define DO_COUNT 2
#define SAVE_DATA_COUNT (5*60)   // 大概5min存储一次

// 计量参数寄存器地址
#define r_UaRms 0x0D  // index=0,A相电压有效值
#define r_UbRms 0x0E  // index=1,B相电压有效值
#define r_UcRms 0x0F  // index=2,C相电压有效值
#define r_IaRms 0x10  // index=3,A相电流有效值
#define r_IbRms 0x11  // index=4,B相电流有效值
#define r_IcRms 0x12  // index=5,C相电流有效值
#define r_Pfa 0x14    // index=6,A相功率因数
#define r_Pfb 0x15    // index=7,B相功率因数
#define r_Pfc 0x16    // index=8,C相功率因数
#define r_Pft 0x17    // index=9,合相功率因数
#define r_Pga 0x18    // index=10,A相电流与电压相角
#define r_Pgb 0x19    // index=11,B相电流与电压相角
#define r_Pgc 0x1A    // index=12,C相电流与电压相角
#define r_Freq 0x1C   // index=13,线频率
#define r_YUaUb 0x26  // index=14,Ua与Ub的电压夹角
#define r_YUbUc 0x28  // index=15,Ub与Uc的电压夹角
#define r_YUaUc 0x27  // index=16,Ua与Uc的电压夹角
#define r_PFlag 0x3D  // index=17,功率方向（0正1负）
// #define r_UabRms 0x7A // index=18,AB线电压有效值
// #define r_UbcRms 0x7B // index=19,BC线电压有效值
// #define r_UacRms 0x7C // index=20,AC线电压有效值
#define r_UnRms 0x29  // index=18,零地电压有效值

#define r_URms_start_index 0      // 相电压index起始
#define r_IRms_start_index 3      // 相电流index起始
#define r_Pf_start_index 6        // 功率因数index起始
#define r_Pft_index 9             // 合相功率因数index
#define r_Pg_start_index 10       // 电流与电压相角index起始
#define r_Freq_index 13           // 线频率index
#define r_YUU_start_index 14      // 电压夹角index起始
#define r_PFlag_index 17          // 功率方向（0正1负）index
// #define r_ULineRms_start_index 18 // 线电压index起始
#define r_UnRms_index 18          // 零地电压index

// 校准参数寄存器地址
#define w_UgainA 0x17      // index=0,A相电压增益
#define w_UgainB 0x18      // index=1,B相电压增益
#define w_UgainC 0x19      // index=2,C相电压增益
#define w_IgainA 0x1A      // index=3,A相电流增益
#define w_IgainB 0x1B      // index=4,B相电流增益
#define w_IgainC 0x1C      // index=5,C相电流增益
#define w_UaRmsoffset 0x24 // index=6,A相电压有效值 offset 校正
#define w_UbRmsoffset 0x25 // index=7,B相电压有效值 offset 校正
#define w_UcRmsoffset 0x26 // index=8,C相电压有效值 offset 校正
#define w_IaRmsoffset 0x27 // index=9,A相电流有效值 offset 校正
#define w_IbRmsoffset 0x28 // index=10,B相电流有效值 offset 校正
#define w_IcRmsoffset 0x29 // index=11,C相电流有效值 offset 校正

#define w_Ugain_start_index 0      // 电压增益index起始
#define w_Igain_start_index 3      // 电流增益index起始
#define w_URmsoffset_start_index 6 // 电压offset index起始
#define w_IRmsoffset_start_index 9 // 电流offset index起始

#define ATS_A_status_index  12
#define ATS_B_status_index  15
#define Mccb_A_status_index 18
#define Mccb_B_status_index 1

/*********************  数据结构定义  **********************/

typedef enum
{
    DI1_IN_0 = 0,
    DI2_IN_0,
    DI3_IN_0,
    DI1_IN_1,
    DI2_IN_1,
    DI3_IN_1,
    DI1_IN_2,
    DI2_IN_2,
    DI3_IN_2,
    DI1_IN_3,
    DI2_IN_3,
    DI3_IN_3,
    DI1_IN_4,
    DI2_IN_4,
    DI3_IN_4,
    DI1_IN_5,
    DI2_IN_5,
    DI3_IN_5,
    DI1_IN_6,
    DI2_IN_6,
    DI3_IN_6,
    DI1_IN_7,
    DI2_IN_7,
    DI3_IN_7,
    DI_END,
} eDI_SAMPLE; // 采样顺序不按照实际端口顺序，而是将（PIN_A0_S1、PIN_A1_S1，PIN_A2_S1）一致的连着采，避免采样通道频繁切换数据不稳定

typedef enum
{
    DO1_OUT = 0,
    DO2_OUT,
    DO_END,
} eDO_OUT;

typedef struct
{
    eDO_OUT       do_channel;
    int           do_pin;
} DO_CHANNEL_INFO_STRUCT;

typedef struct
{
    int id_index;
    eDI_SAMPLE sample_channel;
    int select_status;
    int di_pin;
} DI_CHANNEL_INFO_STRUCT;

typedef struct
{
    float sAC1PhaseVolt[PHASE_NUM];           // 相电压
    float uiAC1PhaseCurr[PHASE_NUM];   // 相电流
    float sAC1LineVolt[PHASE_NUM];            // 线电压
    float usAC1V_V_Angle[PHASE_NUM]; // 电压角度差
    float usAC1I_I_Angle[PHASE_NUM]; // 电流角度差
    float usAC1V_I_Angle[PHASE_NUM]; // 电压电流角度差
    float usAC1Frequen;              // 频率
    float usAC1VZero;                // 零地电压
    float usAC1Yv;                   // 三相电压不平衡度

    float sAC2PhaseVolt[PHASE_NUM];           // 相电压
    float uiAC2PhaseCurr[PHASE_NUM];   // 相电流
    float sAC2LineVolt[PHASE_NUM];            // 线电压
    float usAC2V_V_Angle[PHASE_NUM]; // 电压角度差
    float usAC2I_I_Angle[PHASE_NUM]; // 电流角度差
    float usAC2V_I_Angle[PHASE_NUM]; // 电压电流角度差
    float usAC2Frequen;              // 频率
    float usAC2VZero;                // 零地电压
    float usAC2Yv;                   // 三相电压不平衡度

    float uiIn;                      // 零序电流
    float usYi;                    // 电流不平衡度
    float iActivePower[PHASE_NUM];            // 有功功率
    float usSysActivePower;                 // 系统有功功率
    float iReactivePower[PHASE_NUM];          // 无功功率
    float iSysReactivePower;                  // 系统无功功率
    float sApparentPower[PHASE_NUM];        // 视在功率
    float sSysApparentPower;                // 系统视在功率
    float sPowerFactor[PHASE_NUM];          // 功率因数
    float sSysPowerFactor;                  // 系统功率因数
    int uiActiveEnergy[PHASE_NUM]; // 有功电能
    int uiSysActiveEnergy;                // 系统有功电能
    int uiPosReactiveEnergy[PHASE_NUM];   // 正向无功电能
    int uiSysPosReactiveEnergy;           // 系统正向无功电能
    int uiNegReactiveEnergy[PHASE_NUM];   // 负向无功电能
    int uiSysNegReactiveEnergy;           // 系统负向无功电能
    int uiTotalReactiveEnergy[PHASE_NUM]; // 总无功电能
    int uiSysTotalReactiveEnergy;         // 系统总无功电能
    float uiPhaseCurrDemand[PHASE_NUM];     // 相电流需量
    float uiPhaseCurrMaxDemand[PHASE_NUM];  // 相电流最大需量
    float iActivePowerDemand[PHASE_NUM];             // 有功功率需量
    float iSysActivePowerDemand;                     // 系统有功功率需量
    float iActivePowerMaxDemand[PHASE_NUM];          // 有功功率最大需量
    float iSysActivePowerMaxDemand;                  // 系统有功功率最大需量

    float usPhaseCurrTotalTHD[PHASE_NUM]; // 相电流总谐波畸变率
    float usPhaseCurrAverageTHD;          // 相电流平均总谐波畸变率
    float usPhaseVoltTotalTHD[PHASE_NUM]; // 相电压总谐波畸变率
    float usPhaseVoltAverageTHD;          // 相电压平均总谐波畸变率
    float usVoltHarmonics1[NUM_HARMONIC]; // 相电压谐波含有率
    float usCurrHarmonics1[NUM_HARMONIC]; // 相电流谐波含有率
    float usVoltHarmonics2[NUM_HARMONIC];
    float usCurrHarmonics2[NUM_HARMONIC];
    float usVoltHarmonics3[NUM_HARMONIC];
    float usCurrHarmonics3[NUM_HARMONIC];
} T_SheetAnalogDataStruct;

typedef struct
{
    unsigned short ac_cur_tran_ratio;       // 交流输入电流互感器变比
    unsigned short ac_mts_status_detect;    // 交流MTS状态检测
    unsigned short ac_input_mccb_config;    // 交流输入塑壳断路器配置
    float first_cur_zero[PHASE_NUM];        // 输入A电流零点
    float first_cur_slope[PHASE_NUM];       // 输入A电流斜率
    float first_vol_zero[PHASE_NUM];        // 输入A电压零点
    float first_vol_slope[PHASE_NUM];       // 输入A电压斜率
    float second_cur_zero[PHASE_NUM];       // 输入B电流零点
    float second_cur_slope[PHASE_NUM];      // 输入B电流斜率
    float second_vol_zero[PHASE_NUM];       // 输入B电压零点
    float second_vol_slope[PHASE_NUM];      // 输入B电压斜率
} para_info_t;

typedef struct
{
    float afSampleArray[FILTER_SAMPLE_TIMES];
    unsigned char ucCounter;
}T_FilterAna;

typedef struct
{
    float real; // real data
    float imag; // zero
} compx;


typedef struct {
    rt_uint32_t msg_id;
    void (*handle)(_rt_msg_t curr_msg);
}msg_handle_process_t;

/*********************  函数原型定义  **********************/
void *sample_init_sys(void *param);
void sample_main(void *parameter);
unsigned short get_digital_data(int *data, unsigned short data_size);
unsigned short get_analog_data(T_SheetAnalogDataStruct *data, unsigned short data_size);
// signed int set_para_data(T_SheetParaDataStruct *data);
float FilterSingleAna(T_FilterAna *ptFilter, float finVal);
void setDoValue(eDO_OUT do_index, int value);
int getDoValue(eDO_OUT do_index);
int init_energy_data();
int save_energy_data();

void handle_para_set_msg(_rt_msg_t curr_msg);
void handle_period_data_cal_msg(_rt_msg_t curr_msg);
void send_period_data_calc_msg();
void handle_para_set_msg(_rt_msg_t curr_msg);
int di_get_data_by_channel(eDI_SAMPLE channel);
int get_sample_digital_data(unsigned int* digital_data);
void handle_clean_energy(_rt_msg_t curr_msg);

#ifdef __cplusplus
} /* end of the 'extern "C"' block */
#endif

#endif
