/*
 * @file    : 
 * @brief   : 
 * @details : 
 * <AUTHOR> 周彬
 * @Date    : 2021-12-13 14:25:06
 * @LastEditTime: 2022-02-15 16:07:15
 * @version : V0.0.1
 * @para    : Copyright (c) 
 *            ZTE Corporation
 * @par     : History
 *     version: author, date, descn
 */

#include <stdio.h>
#include <rtthread.h>
#include <rtdbg.h>
#include <board.h>

//#include "dataTypedef.h"
//#include "utils.h"

#include "led.h"
#include "sample.h"
#include "realAlarm.h"
#include "utils_heart_beat.h"
#include "utils_thread.h"
#include "thread_id.h"
#include "CommCan.h"
#include "CommCan2.h"
#include "prtclDL.h"
#include "hisdata.h"
#include "flash.h"
#include "wireless.h"
#include "fileSys.h"
#include "para.h"
#include "usart.h"
#include "ism330dhcx.h"
#include "MdbsRtu.h"
#include "server_id.h"
#include "thread_id.h"
#include "algorithmAES.h"
#include "apptest.h"
#include "bduRecord.h"
#include "utils_server.h"
#include "syswatch.h"
#include "SNMPData.h"
#include "Timing.h"
#include "peakshift.h"

#include "lwip/netifapi.h"
#include "netdev.h"
#include "snmp_example.h"
#include "commBdu.h"
#include "HeaterControl.h"
#include "NetProtocol1363.h"
#include "netdev.h"
#include "circle_buff.h"
#include "utils_rtthread_security_func.h"

#define THREAD_PRIORITY 15
#define THREAD_TIMESLICE 5

#define THREAD_STACK_SIZE_SAMPLE   6144
#define THREAD_STACK_SIZE_LED      2048
#define THREAD_STACK_SIZE_CAN1     6144
#define THREAD_STACK_SIZE_CAN2     3072
#define THREAD_STACK_SIZE_COMM     6144
#define THREAD_STACK_SIZE_HISDATA  2048
#define THREAD_STACK_SIZE_WIRELESS 4096
#define THREAD_STACK_SIZE_NET1104  4096
#define THREAD_STACK_SIZE_GSENSOR  2048
#define THREAD_STACK_SIZE_ALARM    3072
#define THREAD_STACK_SIZE_BATTERY  3072
#define THREAD_STACK_SIZE_COMMBDU  6144
#define THREAD_STACK_SIZE_TRAP     3072
#define THREAD_STACK_SIZE_BACKUP   4096
#define THREAD_STACK_SIZE_RECORD   3072
#define THREAD_STACK_SIZE_SNMP     3072
#define THREAD_STACK_SIZE_TIMING   2048


static char s_thread_stack_sample[THREAD_STACK_SIZE_SAMPLE] RT_SECTION(".bss");
static char s_thread_stack_led[THREAD_STACK_SIZE_LED] RT_SECTION(".tcm_data_bss");
static char s_thread_stack_can1[THREAD_STACK_SIZE_CAN1] RT_SECTION(".tcm_data_bss");
static char s_thread_stack_can2[THREAD_STACK_SIZE_CAN2] RT_SECTION(".tcm_data_bss");
static char s_thread_stack_comm[THREAD_STACK_SIZE_COMM] RT_SECTION(".bss");
static char s_thread_stack_hisdata[THREAD_STACK_SIZE_HISDATA] RT_SECTION(".bss");
static char s_thread_stack_wireless[THREAD_STACK_SIZE_WIRELESS] RT_SECTION(".bss");
static char s_thread_stack_net1104[THREAD_STACK_SIZE_NET1104] RT_SECTION(".tcm_data_bss");
static char s_thread_stack_gsensor[THREAD_STACK_SIZE_GSENSOR] RT_SECTION(".bss");
static char s_thread_stack_alarm[THREAD_STACK_SIZE_ALARM] RT_SECTION(".bss");
static char s_thread_stack_battery[THREAD_STACK_SIZE_BATTERY] RT_SECTION(".bss");
static char s_thread_stack_commbdu[THREAD_STACK_SIZE_COMMBDU] RT_SECTION(".bss");
static char s_thread_stack_trap[THREAD_STACK_SIZE_TRAP] RT_SECTION(".bss");
static char s_thread_stack_backup[THREAD_STACK_SIZE_BACKUP] RT_SECTION(".bss");
static char s_thread_stack_bdurecord[THREAD_STACK_SIZE_RECORD] RT_SECTION(".bss");
static char s_thread_stack_snmp[THREAD_STACK_SIZE_SNMP] RT_SECTION(".bss");
static char s_thread_stack_timing[THREAD_STACK_SIZE_TIMING] RT_SECTION(".bss");



rt_uint32_t Iwd_timeout = 10;

static void alarm_trap(void* parameter);

static void deamon_thread(){
    while(is_running(TRUE)){
        check_heart_beat();
        rt_thread_delay(10);
    }
}


static server_info_t g_server_group[] = {
    {{{SAMPLE_SERVER_ID,         "Sample",      sizeof(s_thread_stack_sample),    s_thread_stack_sample,    THREAD_PRIORITY  }}, NULL,  SampleInSys},
    {{{LED_SERVER_ID,            "LED",         sizeof(s_thread_stack_led),       s_thread_stack_led,       THREAD_PRIORITY  }}, NULL,  CtrlOut},
    {{{CANCOMM_SERVER_ID ,       "CanComm",     sizeof(s_thread_stack_can1),      s_thread_stack_can1,      THREAD_PRIORITY  }}, NULL,  Process_CAN_Comm},
    {{{CANCOMM2_SERVER_ID ,      "CanComm2",    sizeof(s_thread_stack_can2),      s_thread_stack_can2,      THREAD_PRIORITY  }}, NULL,  Process_CAN2_Comm},
    {{{UARTCOMM_SERVER_ID ,      "Comm",        sizeof(s_thread_stack_comm),      s_thread_stack_comm,      THREAD_PRIORITY  }}, NULL,  DealComm},
    {{{HISDATA_SERVER_ID,        "HisData",     sizeof(s_thread_stack_hisdata),   s_thread_stack_hisdata,   THREAD_PRIORITY  }}, NULL,  processHisData},
    {{{WIRELESS_SERVER_ID,       "Wireless",    sizeof(s_thread_stack_wireless),  s_thread_stack_wireless,  THREAD_PRIORITY  }}, NULL,  mg21_CommTask},
    {{{NET1104_SERVER_ID,        "Net1104",     sizeof(s_thread_stack_net1104),   s_thread_stack_net1104,   THREAD_PRIORITY  }}, NULL,  Process1363_NET_Comm},
    {{{GSENSOR_SERVER_ID,        "GSensor",     sizeof(s_thread_stack_gsensor),   s_thread_stack_gsensor,   THREAD_PRIORITY  }}, NULL,  Proc_G_Sensor},
    {{{ALARM_MANAGE_SERVER_ID,   "Alarm",       sizeof(s_thread_stack_alarm),     s_thread_stack_alarm,     THREAD_PRIORITY  }}, NULL,  JudgeAlarm},
    {{{BATTERY_MANAGE_SERVER_ID, "Battery",     sizeof(s_thread_stack_battery),   s_thread_stack_battery,   THREAD_PRIORITY  }}, NULL,  BatteryManagement},
    {{{BDUCOMM_SERVER_ID,        "CommBdu",     sizeof(s_thread_stack_commbdu),   s_thread_stack_commbdu,   THREAD_PRIORITY  }}, NULL,  DealBduComm},
    {{{ALARM_TRAP_SERVER_ID,     "alarm_trap",  sizeof(s_thread_stack_trap),      s_thread_stack_trap,      THREAD_PRIORITY  }}, NULL,  alarm_trap},
    {{{BACKUP_SERVER_ID  ,       "BackUp",      sizeof(s_thread_stack_backup),    s_thread_stack_backup,    THREAD_PRIORITY+1}}, NULL,  processBackupData},
    {{{BDU_RECORD_SERVER_ID,     "BduRecord",   sizeof(s_thread_stack_bdurecord), s_thread_stack_bdurecord, THREAD_PRIORITY  }}, NULL,  BduRecordSaver},
    {{{SNMP_SERVER_ID,           "SNMP",        sizeof(s_thread_stack_snmp),      s_thread_stack_snmp,      THREAD_PRIORITY  }}, NULL,  Process_SNMP},
    {{{TIMING_SERVER_ID,         "Timing",      sizeof(s_thread_stack_timing),    s_thread_stack_timing,    THREAD_PRIORITY  }}, NULL,  Process_Timing},
};


void init_main(void)
{
    int mcuResetReason = 0;
    mcuResetReason = GetMcuResetReason();
    rt_thread_delay(1000);
    initFileSys();//文件系统
    InitProConfig();
    InitCRC();
    initHisData();
    initExtremeData();
    InitAesKey();
    InitSysPara();
    InitFrequencyModulation();  //初始化调频数据
    InitPeakShiftPara();
    JudgeBuzzEnable();
    InitCan1();
    InitCan2();
    init_circle_buff();
    InitBduRecordSaver();
    InitExistBattInfo();

    snmp_initial();
    SaveMcuResetReason(mcuResetReason);
    syswatch_init();
}

int main(void)
{
    init_main();
    create_server(&g_server_group[0], sizeof(g_server_group)/sizeof(g_server_group[0]));
    rt_thread_delay(10000);
    SaveResetToHisOper();
    BeginDownload(FLAG_BACKUP); 
    deamon_thread();
    return RT_EOK;
}

static void alarm_trap(void* parameter)
{
    BYTE ucJudgeFlag = 0;
    struct netdev *pDev = NULL;
    pre_thread_beat_f(THREAD_ALARM_TRAP);
    while (1)
    {
        rt_thread_delay(100);
        thread_beat_go_on(THREAD_ALARM_TRAP);
        CheckUpdateTimeOut();
        
        if (5 == ucJudgeFlag++)
        {
            ucJudgeFlag = 0;

            pDev = netdev_get_by_name("e0");
            if (pDev != RT_NULL && (pDev->flags & NETDEV_FLAG_LINK_UP))
            {
                send_alarm_trap();
            }
        } 
    }
}

void jugde_heartbeat_flag(void)
{
    int abnormal_thread_t;
    char reset_reason[20] = {0};
    if (judge_thread_beat_normal(&abnormal_thread_t) == TRUE) {
        reset_thread_beat_flag();
    } else {
        rt_snprintf_s(reset_reason, sizeof(reset_reason), "thread%d wd rst", abnormal_thread_t);
        SaveAction(GetActionId(CONTOL_RST_SYS), reset_reason);
        rt_hw_cpu_reset();
    }
}