
#ifndef _UPDATE_COMMON_H_
#define _UPDATE_COMMON_H_

#ifdef __cplusplus
extern "C" {
#endif

// 业务特有的函数都通过这个结构体注册，然后调用
typedef struct
{
    void (*ptr_ctrl_led_when_update)(unsigned char);
    void (*ptr_set_pv_update_sta)(unsigned char);
    void (*ptr_get_pv_update_sta)(unsigned char*);
    int  (*north_thread_beat_go_on)();
    unsigned short (*ptr_is_wifi_should_recover)();
}common_fun_t;


common_fun_t* get_register_common_fun();

#ifdef __cplusplus
}
#endif

#endif 